<!-- 水源巡检-巡检任务 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable
      ref="refTable"
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm ref="refDialogForm" :config="addOrUpdateConfig"></DialogForm>
    <DialogForm ref="refImg" :config="imgConfig">
      <el-image
        v-if="state.imageUrl"
        style="width: 440px; height: 400px"
        :src="state.imageUrl"
        :preview-src-list="srcList"
        :initial-index="1"
        fit="cover"
      />
      <div class="" style="text-align: center">暂无信息</div>
    </DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Edit, Refresh } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { ElMessageBox } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import useGlobal from '@/hooks/global/useGlobal';
import { SLConfirm } from '@/utils/Message';
import {
  addCircuitTask,
  circuitConfigList,
  circuitTaskItemList,
  circuitTaskList,
  circuitTemplateList,
  delCircuitTask,
  receiveCircuitTask,
  sendVerifyCircuitTask,
  verifyCircuitTask
} from '@/api/headwatersManage/waterInspection';
import { removeSlash } from '@/utils/removeIdSlash';
import useDepartment from '@/hooks/department/useDepartment';
import useStation from '@/hooks/station/useStation';
import { getUserList } from '@/api/user';
import { useUserStore } from '@/store';
import { formatDate } from '@/utils/DateFormatter';

const userStore = useUserStore();
const { $messageSuccess, $messageError } = useGlobal();
const { getAllStationOption } = useStation();
const { getDepartmentTree } = useDepartment();
const { $btnPerms } = useGlobal();
const refSearch = ref<ICardSearchIns>();
const srcList = ref<string[]>([]);
const refDialogForm = ref<IDialogFormIns>();
const refTable = ref<ICardTableIns>();
const refImg = ref<IDialogFormIns>();
const templateItem = [
  { label: '项目分类', prop: 'itemType' },
  { label: '项目名称', prop: 'name' },
  { label: '巡检方法', prop: 'method' },
  { label: '巡检要求', prop: 'require' }
];

const state = reactive<{
  departmentTree: any[];
  templateList: any[];
  stationOptionList: any[];
  searchExecutionUsers: any[];
  searchAuditUsers: any[];
  searchAuditUserDepartment: any;
  searchExecutionUserDepartment: any;
  resultEnum: any;
  code: string;
  imageUrl: string;
  counter: any;
}>({
  departmentTree: [],
  templateList: [],
  stationOptionList: [],
  searchAuditUserDepartment: {},
  searchExecutionUserDepartment: {},
  searchAuditUsers: [],
  searchExecutionUsers: [],
  resultEnum: {
    PENDING: '未审核',
    RECEIVED: '未审核',
    VERIFY: '未审核',
    APPROVED: '合格',
    REJECTED: '不合格'
  },
  code: '',
  imageUrl: '',
  counter: '000100'
});

// 搜索条件配置
const cardSearchConfig = reactive<ISearch>({
  filters: [
    { label: '任务编号', field: 'code', type: 'input' },
    { label: '任务名称', field: 'name', type: 'input' },
    {
      label: '任务类型',
      field: 'taskType',
      type: 'select',
      options: [
        { label: '临时任务', value: '临时任务' },
        { label: '常规任务', value: '常规任务' }
      ]
    },
    {
      label: '巡检部门',
      field: 'executionUserDepartmentId',
      type: 'select-tree',
      checkStrictly: true,
      options: computed(() => state.departmentTree) as any,
      onChange: async (val: any) => {
        const executionUser = cardSearchConfig.filters?.find(
          (field) => field.field === 'executionUserId'
        ) as IFormSelect;
        if (val) {
          const userOptions = await userList(val);
          executionUser.options = userOptions;
        } else {
          executionUser.options = [];
        }
        cardSearchConfig.defaultParams = {
          ...cardSearchConfig.defaultParams,
          ...refSearch.value?.queryParams,
          executionUserDepartmentId: val,
          executionUserId: ''
        };
        refSearch.value?.resetForm();
      }
    },
    {
      label: '巡检人员',
      field: 'executionUserId',
      type: 'select',
      placeholder: '请选择巡检人员'
    },
    {
      label: '巡检水源',
      field: 'stationId',
      type: 'select',
      multiple: true,
      options: computed(() => state.stationOptionList) as any
    },
    { label: '预计开始', field: 'startTime', type: 'date' },
    { label: '预计完成', field: 'endTime', type: 'date' },
    { label: '实际开始', field: 'realStartTime', type: 'date' },
    { label: '实际完成', field: 'realEndTime', type: 'date' },
    // { label: '工单数量', field: 'key10', type: 'input' },
    {
      label: '任务状态',
      field: 'status',
      type: 'select',
      options: [
        { label: '待接收', value: 'PENDING' },
        { label: '处理中', value: 'RECEIVED' },
        { label: '已审核', value: 'VERIFY' }
      ]
    },
    {
      xs: 11,
      type: 'select-tree',
      label: '审核部门',
      clearable: false,
      checkStrictly: true,
      options: computed(() => state.departmentTree) as any,
      field: 'auditUserDepartmentId',
      rules: [{ required: true, message: '请选择巡检部门' }],
      onChange: async (val: any) => {
        if (val) {
          state.searchAuditUsers = await userList(val);
        } else {
          state.searchAuditUsers = [];
        }
        cardSearchConfig.defaultParams = {
          ...cardSearchConfig.defaultParams,
          ...refSearch.value?.queryParams,
          auditUserDepartmentId: val,
          auditUserId: ''
        };
        refSearch.value?.resetForm();
      }
    },
    {
      xs: 11,
      type: 'select',
      label: '审核人员',
      field: 'auditUserId',
      options: computed(() => state.searchAuditUsers) as any,
      rules: [{ required: true, message: '请选择审核人员' }]
    },
    {
      label: '审核结果',
      field: 'status',
      type: 'select',
      options: [
        { label: '通过', value: 'APPROVED' },
        { label: '拒绝', value: 'REJECTED' }
      ]
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshDataDetail()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            cardSearchConfig.defaultParams = {};
            refSearch.value?.resetForm();
          }
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '新增',
          type: 'success',
          icon: ICONS.ADD,
          click: () => {
            addOrUpdateConfig.title = '新增';
            handleAddEditShow();
          }
        }
        // {
        //   perm: $btnPerms('RoleManageAdd'),
        //   text: '批量删除',
        //   type: 'danger',
        //   icon: ICONS.DELETE,
        //   click: () => clickCreatedRole()
        // }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  loading: true,
  defaultExpandAll: true,
  indexVisible: true,
  selectList: [],
  columns: [
    { label: '任务编号', prop: 'code', minWidth: 140 },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD'),
      minWidth: 140
    },
    { label: '任务名称', prop: 'name', minWidth: 140 },
    { label: '任务类型', prop: 'taskType', minWidth: 140 },
    { label: '巡检人员', prop: 'executionUserName', minWidth: 140 },
    { label: '巡检水源', prop: 'stationName', minWidth: 140 },
    {
      label: '预计开始',
      prop: 'startTime',
      formatter: (row: any, value: any) => {
        return formatDate(value, 'YYYY-MM-DD');
      },
      minWidth: 140
    },
    {
      label: '预计完成',
      prop: 'endTime',
      formatter: (row: any, value: any) => {
        return formatDate(value, 'YYYY-MM-DD');
      },
      minWidth: 140
    },
    {
      label: '实际开始',
      prop: 'realStartTime',
      formatter: (row: any, value: any) => {
        return formatDate(value, 'YYYY-MM-DD');
      },
      minWidth: 140
    },
    {
      label: '实际完成',
      prop: 'realEndTime',
      formatter: (row: any, value: any) => {
        return formatDate(value, 'YYYY-MM-DD');
      },
      minWidth: 140
    },
    // { label: '工单数量', prop: 'key10' },
    {
      label: '任务状态',
      prop: 'statusName',
      minWidth: 140
    },
    { label: '审核部门', prop: 'auditUserDepartmentName', minWidth: 140 },
    { label: '审核人员', prop: 'auditUserName', minWidth: 140 },
    {
      label: '审核结果',
      prop: 'status',
      formatter: (row: any, value: any) => {
        return state.resultEnum[value];
      },
      minWidth: 140
    }
  ],
  operationWidth: '280px',
  operationFixed: 'right',
  operations: [
    {
      type: 'primary',
      isTextBtn: true,
      perm: true,
      text: '查看',
      click: (row) => {
        addOrUpdateConfig.title = '查看';
        handleAddEditShow(row, true);
      }
    },
    // {
    //   type: 'primary',
    //   isTextBtn: true,
    //   text: '编辑',
    //   perm: $btnPerms('RoleManageEdit'),
    //   icon: ICONS.EDIT,
    //   click: row => {
    //     addOrUpdateConfig.title = '编辑'
    //     handleAddEditShow(row, true)
    //   }
    // },
    {
      type: 'primary',
      isTextBtn: true,
      perm: true,
      text: '接收',
      disabled: (row?: any) => {
        const userId = removeSlash(userStore?.user?.id?.id || '');
        return !(row.executionUserId === userId && row.status === 'PENDING');
      },
      click: (row) => {
        // 接收任务
        SLConfirm('确定接收任务？', '提示信息')
          .then(() => {
            receiveCircuitTask(row.id).then(() => {
              refreshDataDetail();
              $messageSuccess('接收成功');
            });
          })
          .catch(() => {
            //
          });
      }
    },
    {
      type: 'primary',
      isTextBtn: true,
      perm: true,
      text: '提交审核',
      disabled: (row?: any) => {
        const userId = removeSlash(userStore?.user?.id?.id || '');
        return !(row.executionUserId === userId && row.status === 'RECEIVED');
      },
      click: (row) => {
        // 提交任务审核
        SLConfirm('确定提交任务审核？', '提示信息')
          .then(() => {
            sendVerifyCircuitTask(row.id, row.auditUserId)
              .then(() => {
                refreshDataDetail();
                $messageSuccess('提交成功');
              })
              .catch(() => {
                $messageError('提交失败');
              });
          })
          .catch(() => {
            //
          });
      }
    },
    {
      type: 'primary',
      isTextBtn: true,
      perm: true,
      text: '审核',
      disabled: (row?: any) => {
        const userId = removeSlash(userStore?.user?.id?.id || '');
        return !(row.auditUserId === userId && row.status === 'VERIFY');
      },
      click: (row) => {
        // 提交审核结果
        ElMessageBox.confirm('审核是否通过', '提示', {
          confirmButtonText: '通过',
          cancelButtonText: '驳回',
          type: 'warning'
        })
          .then(() => {
            verifyCircuitTask(row.id, true)
              .then(() => {
                refreshDataDetail();
                $messageSuccess('提交成功');
              })
              .catch(() => {
                $messageError('提交失败');
              });
          })
          .catch(() => {
            // 驳回
            verifyCircuitTask(row.id, false)
              .then(() => {
                refreshDataDetail();
                $messageSuccess('提交成功');
              })
              .catch(() => {
                $messageError('提交失败');
              });
          });
      }
    },
    {
      isTextBtn: true,
      type: 'danger',
      perm: true,
      text: '删除',
      disabled: (row?: any) => {
        const userId = removeSlash(userStore?.user?.id?.id || '');
        return !(row.creator === userId && row.status === 'PENDING');
      },
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshDataDetail();
    }
  }
});

const imgConfig = reactive<IDialogFormConfig>({
  title: '附件信息',
  labelWidth: '130px',
  dialogWidth: 500,
  group: []
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: '130px',
  defaultValue: {
    taskType: '临时任务',
    code: state.code
  },
  group: [
    {
      fields: [
        {
          xs: 16,
          type: 'input',
          label: '任务编号',
          readonly: true,
          clearable: false,
          field: 'code',
          rules: [{ required: true, message: '请输入任务名称' }]
        },
        {
          xs: 8,
          type: 'btn-group',
          field: 'codeButton',
          btns: [
            {
              text: '获取编号',
              perm: true,
              click: () => {
                const code = getCode();
                addOrUpdateConfig.defaultValue = {
                  ...addOrUpdateConfig.defaultValue,
                  ...refDialogForm.value?.refForm?.dataForm,
                  code
                };
                refDialogForm.value?.refForm?.resetForm();
              }
            }
          ]
          // handleHidden: (params, query, config) => {
          //   config.hidden = params.code
          // }
        },
        {
          xs: 8,
          type: 'input',
          label: '任务名称',
          field: 'name',
          rules: [{ required: true, message: '请输入任务名称' }]
        },
        {
          xs: 8,
          type: 'select',
          label: '任务类型',
          field: 'taskType',
          readonly: true,
          options: [
            { label: '临时任务', value: '临时任务' },
            { label: '常规任务', value: '常规任务' }
          ],
          rules: [{ required: true, message: '请选择任务类型' }]
        },
        {
          xs: 8,
          type: 'select-tree',
          checkStrictly: true,
          label: '巡检部门',
          options: computed(() => state.departmentTree) as any,
          field: 'executionUserDepartmentId',
          onChange: async (val: any) => {
            const executionUser = addOrUpdateConfig.group[0].fields.find(
              (field) => field.field === 'executionUserId'
            ) as IFormSelect;
            if (val) {
              const userOptions = await userList(val);
              executionUser.options = userOptions;
            } else {
              executionUser.options = [];
            }
            addOrUpdateConfig.defaultValue = {
              ...addOrUpdateConfig.defaultValue,
              ...refDialogForm.value?.refForm?.dataForm,
              executionUserId: ''
            };
            refDialogForm.value?.refForm?.resetForm();
          },
          rules: [{ required: true, message: '请选择巡检部门' }]
        },
        {
          xs: 8,
          type: 'select',
          label: '巡检人员',
          field: 'executionUserId',
          rules: [{ required: true, message: '请选择人员名称' }]
        },
        {
          xs: 8,
          type: 'date',
          label: '预计时间',
          field: 'startTime',
          min: dayjs().format('YYYY-MM-DD'),
          rules: [{ required: true, message: '请输入预计时间' }],
          onChange: (val) => {
            const endTime = addOrUpdateConfig.group[0].fields.find(
              (field) => field.field === 'endTime'
            ) as IFormDate;
            console.log(endTime);
            endTime.min = val;
            addOrUpdateConfig.defaultValue = {
              ...addOrUpdateConfig.defaultValue,
              ...refDialogForm.value?.refForm?.dataForm,
              endTime: ''
            };
            refDialogForm.value?.refForm?.resetForm();
          }
        },
        {
          xs: 8,
          type: 'date',
          label: '预计完成',
          field: 'endTime',
          rules: [{ required: true, message: '请输入预计完成时间' }]
        },
        {
          xs: 8,
          type: 'select-tree',
          checkStrictly: true,
          label: '审核部门',
          field: 'auditUserDepartmentId',
          options: computed(() => state.departmentTree) as any,
          onChange: async (val: any) => {
            const executionUser = addOrUpdateConfig.group[0].fields.find(
              (field) => field.field === 'auditUserId'
            ) as IFormSelect;
            if (val) {
              executionUser.options = await userList(val);
            } else {
              executionUser.options = [];
            }
            addOrUpdateConfig.defaultValue = {
              ...addOrUpdateConfig.defaultValue,
              ...refDialogForm.value?.refForm?.dataForm,
              auditUserId: '',
              auditUserDepartmentId: val
            };
            refDialogForm.value?.refForm?.resetForm();
          },
          rules: [{ required: true, message: '请选择审核部门' }]
        },
        {
          xs: 8,
          type: 'select',
          label: '审核人员',
          field: 'auditUserId',
          rules: [{ required: true, message: '请选择审核人员' }]
        },
        {
          xs: 8,
          type: 'select',
          label: '巡检水源',
          field: 'stationId',
          options: computed(() => state.stationOptionList) as any,
          rules: [{ required: true, message: '请选择巡检水源' }]
        },
        {
          xs: 8,
          type: 'select',
          label: '巡检模板',
          field: 'templateId',
          clearable: false,
          options: computed(() => state.templateList) as any,
          onChange: (val) => getCircuitConfigList(val),
          rules: [{ required: true, message: '请选择巡检模板' }]
        },
        {
          type: 'table',
          field: 'configList',
          config: {
            indexVisible: true,
            height: '200px',
            dataList: [],
            columns: templateItem,
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

// 修改弹框
// const handleEdit = row => {
//   addOrUpdateConfig.group.map(res => {
//     res.fields.map(field => {
//       if (field.field === 'key') {
//         field.readonly = true
//       } else {
//         field.readonly = false
//       }
//     })
//   })
//   addOrUpdateConfig.defaultValue = {
//     ...row
//   }
//   addOrUpdateConfig.submit = (params: any) => {
//     SLConfirm('确定提交？', '提示信息').then(() => {
//       console.log(params)
//       params.typeName = '泵房'
//       TableConfig.dataList.push(params)
//       refForm.value?.closeDialog()
//     }).catch(() => {
//       //
//     })
//   }
//   refForm.value?.openDialog()
// }
const getCode = () => {
  const now = new Date();
  const datePart =
    now.getFullYear().toString() +
    (now.getMonth() + 1).toString().padStart(2, '0') +
    now.getDate().toString().padStart(2, '0');

  // 存储和维护生成的编号计数器
  if (!state.counter) {
    state.counter = 100; // 从000100开始
  }

  // 获取当前计数器并加1
  const numberPart = state.counter.toString().padStart(6, '0');
  state.counter++;

  // 拼接成最终的生产编号
  return datePart + numberPart;
};
// 弹框
const handleAddEditShow = async (row?: any, readOnly?: boolean) => {
  addOrUpdateConfig.group.map((res) => {
    res.fields.map((field: any) => {
      field.readonly = !!readOnly;
      if (field.field === 'taskType') {
        field.readonly = true;
      } else if (field.field === 'code') {
        field.readonly = true;
      } else if (field.field === 'codeButton') {
        field.btns[0].disabled = !!row;
      }
    });
  });

  // 获取模板列表
  const res = await circuitTemplateList({
    page: 1,
    size: 999,
    type: '水源'
  });
  const templateList = res.data?.data?.data;
  state.templateList = templateList?.map((template) => {
    return {
      id: template.id,
      label: template.name,
      value: template.id
    };
  });
  // const templateField = addOrUpdateConfig.group[0].fields?.find(field => field.field === 'templateId') as IFormSelect
  // templateField.options = state.templateList
  //
  // const executionDepartment = addOrUpdateConfig.group[0].fields.find(field => field.field === 'executionUserDepartmentId') as ISelectTree
  // executionDepartment.options = state.departmentTree
  //
  // const auditDepartment = addOrUpdateConfig.group[0].fields.find(field => field.field === 'auditUserDepartmentId') as ISelectTree
  // auditDepartment.options = state.departmentTree
  //
  // const stationField = addOrUpdateConfig.group[0].fields.find(field => field.field === 'stationId') as ISelectTree
  // stationField.options = state.stationOptionList

  const configList = addOrUpdateConfig.group[0].fields?.find(
    (field) => field.field === 'configList'
  ) as any;
  configList.config.dataList = [];
  const columns = JSON.parse(JSON.stringify(templateItem));
  configList.config.columns = columns;
  if (row) {
    configList.config.columns = columns.concat([
      { label: '巡检结果', prop: 'result' },
      { label: '结果备注', prop: 'resultRemark' },
      {
        label: '附件',
        prop: 'file',
        formItemConfig: {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查看',
              svgIcon: shallowRef(Edit),
              plain: true,
              click: (row: any) => {
                refImg.value?.openDialog();
                srcList.value = row.file?.split(',');
                state.imageUrl = srcList.value ? srcList.value[0] : '';
              }
            }
          ]
        }
      }
    ]);

    addOrUpdateConfig.defaultValue = {
      ...row,
      startTime: dayjs(row?.startTime).format(),
      endTime: dayjs(row?.endTime).format(),
      auditUserId: row.auditUserName,
      executionUserId: row.executionUserName
    };
    await getCircuitTaskItemList(row.id);
  } else {
    addOrUpdateConfig.defaultValue = {
      taskType: '临时任务',
      code: getCode()
    };
  }
  if (readOnly) {
    addOrUpdateConfig.submit = undefined;
  } else {
    addOrUpdateConfig.submit = (params: any) => {
      SLConfirm('确定提交？', '提示信息')
        .then(() => {
          params.type = '水源';
          params.id = row ? row.id : null;
          delete params.configList;
          // if (params.endDate < params.endDate) {
          //   SLMessage.error('结束日期大于开始日期')
          //   return
          // }
          console.log(params);
          addCircuitTask(params)
            .then(() => {
              refDialogForm.value?.refForm?.resetForm();
              refDialogForm.value?.closeDialog();
              $messageSuccess('提交成功');
              refreshDataDetail();
            })
            .catch(() => {
              $messageError('提交失败');
            });
        })
        .catch(() => {
          //
        });
    };
  }
  refDialogForm.value?.openDialog();
};
// 删除巡检任务
const handleDelete = (row: { id: any }) => {
  SLConfirm('确定删除巡检任务, 是否继续?', '删除提示').then(() => {
    // 删除计划
    delCircuitTask(row.id)
      .then(() => {
        refreshDataDetail();
        $messageSuccess('删除成功');
      })
      .catch((err) => {
        $messageError(err.data.message);
      });
  });
};
// 获取巡检配置列表
const getCircuitConfigList = async (val: any) => {
  const params = {
    page: 1,
    size: 9999,
    type: '水源',
    templateId: val
  };
  const result = await circuitConfigList(params);
  const configData = result.data?.data?.data;
  const configList = addOrUpdateConfig.group[0].fields?.find(
    (field) => field.field === 'configList'
  ) as any;
  configList.config.dataList = configData;
};

// 但条目数据获取
const getCircuitTaskItemList = async (mainId: string) => {
  const params = {
    page: 1,
    size: 999,
    mainId,
    type: '水源'
  };
  const result = await circuitTaskItemList(params);
  const configDataData = result.data?.data?.data;
  const configData = configDataData?.map((data) => {
    return {
      ...data,
      method: data.itemMethod,
      name: data.itemName,
      require: data.itemRequire
    };
  });
  const configList = addOrUpdateConfig.group[0].fields?.find(
    (field) => field.field === 'configList'
  ) as any;
  configList.config.dataList = configData;
};
// 获取任务列表
const refreshDataDetail = async () => {
  TableConfig.loading = true;
  const query = refSearch.value?.queryParams || {
    createTime: []
  };
  const params: any = {
    ...query,
    type: '水源',
    startTime: query.startTime
      ? dayjs(query.startTime).startOf('day').valueOf()
      : null,
    endTime: query.endTime ? dayjs(query.endTime).endOf('day').valueOf() : null,
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    stationId: query.stationId ? query.stationId.join(',') : ''
  };
  const result = await circuitTaskList(params);
  TableConfig.pagination.total = result.data?.data.total;
  TableConfig.dataList = result.data?.data.data;
  TableConfig.loading = false;
};

// 获取部门用户
const userList = async (val: string) => {
  const res = await getUserList({
    pid: val,
    status: 1,
    page: 1,
    size: 999
  });
  const users = res.data?.data?.data;
  const userOptions: NormalOption[] = users?.map((user) => {
    return {
      id: removeSlash(user.id.id),
      label: user.firstName,
      value: removeSlash(user.id.id)
    };
  });
  return userOptions;
};

onBeforeMount(async () => {
  // 获取部门树
  state.departmentTree = await getDepartmentTree(2);
  state.stationOptionList = await getAllStationOption('水源地');
  // const stationField = cardSearchConfig.moreFilters?.find(field => field.field === 'stationId') as IFormSelect
  // stationField.options = state.stationOptionList
  await refreshDataDetail();
});
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
