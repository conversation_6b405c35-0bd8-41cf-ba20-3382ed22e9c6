import{e as x,a as V,S as v}from"./Point-WxyopZva.js";import{T as l,$ as L}from"./index-r0dFAfgr.js";import{ea as b,aS as u,aQ as g,af as p,aN as m}from"./MapView-DaoQedLH.js";import{v as S,b as j,j as $}from"./lineSegment-DQ0q5UHF.js";import{R as P,k as _,N as C}from"./sphere-NgXH-gLx.js";import{D as E}from"./QueryEngineResult-D2Huf9Bb.js";import{V as y}from"./Octree-s2cwrV3a.js";import{m as T}from"./edgeProcessing-OWtVBtJ5.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./plane-BhzlJB-C.js";import"./Util-sSNWzwlq.js";import"./deduplicate-Clsym5GM.js";import"./Indices-iFKW8TWb.js";import"./InterleavedLayout-EYSqXknm.js";import"./BufferView-BcX1hwIm.js";import"./types-Cezv0Yl1.js";import"./VertexAttribute-BAIQI41G.js";import"./glUtil-D4FNL8tc.js";import"./enums-BDQrMlcz.js";import"./VertexElementDescriptor-BOD-G50G.js";const I=1e3;function N(t,o,i){const e=P(),n=_(e);return b(n,n,t,.5),b(n,n,o,.5),e[3]=u(n,t),g(n,n,i),e}let f=class{constructor(){this._idToComponent=new Map,this._components=new y(t=>t.bounds),this._edges=new y(t=>t.bounds),this._tmpLineSegment=S(),this._tmpP1=p(),this._tmpP2=p(),this._tmpP3=p(),this.remoteClient=null}async fetchCandidates(t,o){await Promise.resolve(),v(o),await this._ensureEdgeLocations(t,o);const i=[];return this._edges.forEachNeighbor(e=>(this._addCandidates(t,e,i),i.length<I),t.bounds),{result:{candidates:i}}}async _ensureEdgeLocations(t,o){const i=[];if(this._components.forEachNeighbor(s=>{if(l(s.info)){const{id:a,uid:r}=s;i.push({id:a,uid:r})}return!0},t.bounds),!i.length)return;const e={components:i},n=await this.remoteClient.invoke("fetchAllEdgeLocations",e,L(o,{}));for(const s of n.components)this._setFetchEdgeLocations(s)}async add(t){const o=new h(t.id,t.bounds);return this._idToComponent.set(o.id,o),this._components.add([o]),{result:{}}}async remove(t){const o=this._idToComponent.get(t.id);if(o){const i=[];this._edges.forEachNeighbor(e=>(e.component===o&&i.push(e),!0),o.bounds),this._edges.remove(i),this._components.remove([o]),this._idToComponent.delete(o.id)}return{result:{}}}_setFetchEdgeLocations(t){const o=this._idToComponent.get(t.id);if(l(o)||t.uid!==o.uid)return;const i=T.createView(t.locations),e=new Array(i.count),n=p(),s=p();for(let d=0;d<i.count;d++){i.position0.getVec(d,n),i.position1.getVec(d,s);const c=N(n,s,t.origin),w=new k(o,d,c);e[d]=w}this._edges.add(e);const{objectIds:a,origin:r}=t;o.info={locations:i,objectIds:a,origin:r}}_addCandidates(t,o,i){const{info:e}=o.component,{origin:n,objectIds:s}=e,a=e.locations,r=a.position0.getVec(o.index,this._tmpP1),d=a.position1.getVec(o.index,this._tmpP2);g(r,r,n),g(d,d,n);const c=s[a.componentIndex.get(o.index)];this._addEdgeCandidate(t,c,r,d,i),this._addVertexCandidate(t,c,r,i),this._addVertexCandidate(t,c,d,i)}_addEdgeCandidate(t,o,i,e,n){if(!(t.types&E.EDGE))return;const s=_(t.bounds),a=j(i,e,this._tmpLineSegment),r=$(a,s,this._tmpP3);C(t.bounds,r)&&n.push({type:"edge",objectId:o,target:m(r),distance:u(s,r),start:m(i),end:m(e)})}_addVertexCandidate(t,o,i,e){if(!(t.types&E.VERTEX))return;const n=_(t.bounds);C(t.bounds,i)&&e.push({type:"vertex",objectId:o,target:m(i),distance:u(n,i)})}};f=x([V("esri.views.interactive.snapping.featureSources.sceneLayerSource.SceneLayerSnappingSourceWorker")],f);const ut=f;class h{constructor(o,i){this.id=o,this.bounds=i,this.info=null,this.uid=++h.uid}}h.uid=0;class k{constructor(o,i,e){this.component=o,this.index=i,this.bounds=e}}export{ut as default};
