/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.telemetryAttribute;

import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.telemetryAttribute.TelemetryAttribute;
import org.thingsboard.server.dao.model.sql.TelemetryAttributeEntity;
import org.thingsboard.server.dao.nosql.CassandraAbstractSearchTextDao;
import org.thingsboard.server.dao.util.NoSqlDao;

import java.util.List;
import java.util.UUID;

@Component
@Slf4j
@NoSqlDao
public class CassandraTelemetryAttributeDao extends CassandraAbstractSearchTextDao<TelemetryAttributeEntity, TelemetryAttribute> implements TelemetryAttributeDao {


    @Override
    protected Class<TelemetryAttributeEntity> getColumnFamilyClass() {
        return null;
    }

    @Override
    protected String getColumnFamilyName() {
        return null;
    }

    @Override
    public ListenableFuture<List<TelemetryAttribute>> getAttribute(UUID deviceId, String showName) {
        return null;
    }

    @Override
    public ListenableFuture<List<TelemetryAttribute>> getTelemetryAttributeByDevice(EntityId deviceId) {
        return null;
    }

    @Override
    public ListenableFuture<List<TelemetryAttribute>> getTelemetryAttributeByDeviceNameAndName(String deviceName, String showName) {
        return null;
    }

    @Override
    public TelemetryAttribute save(TelemetryAttribute telemetryAttribute) {
        return null;
    }
}
