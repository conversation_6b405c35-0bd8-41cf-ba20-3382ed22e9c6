/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.rule.engine.api;

import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.VO.AlarmLinkedUser;
import org.thingsboard.server.common.data.VO.TenantSmsKey;
import org.thingsboard.server.common.data.alarm.AlarmReport;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.exception.ThingsboardException;

import com.fasterxml.jackson.databind.JsonNode;
import org.thingsboard.server.common.data.kv.TsKvEntry;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

public interface MailService {

    void updateMailConfiguration();

    void sendEmail(AlarmLinkedUser user, String subject, String message,String logType);
    
    void sendTestMail(JsonNode config, String email) throws ThingsboardException;
    
    void sendActivationEmail(String activationLink, String email) throws ThingsboardException;
    
    void sendAccountActivatedEmail(String loginLink, String email) throws ThingsboardException;
    
    void sendResetPasswordEmail(String passwordResetLink, String email) throws ThingsboardException;
    
    void sendPasswordWasResetEmail(String loginLink, String email) throws ThingsboardException;

    void send(String from, String to, String cc, String bcc, String subject, String body) throws MessagingException;

    void sendSMS(AlarmLinkedUser user, AlarmReport alarmReport);

    void sendOfflineSMS(User user, AlarmReport alarmReport);

    void sendRangeMsg(Device device, TsKvEntry kvEntry, PropAttribute propAttribute);


    boolean sendToUser(AlarmLinkedUser user, String body, TenantSmsKey tenantSmsKey);

    boolean sendToUser(AlarmLinkedUser user, String body);


    boolean sendCaptcha(AlarmLinkedUser user, String body);

    boolean sendLogicalFlowAlarmToUser(AlarmLinkedUser user, String body);
}
