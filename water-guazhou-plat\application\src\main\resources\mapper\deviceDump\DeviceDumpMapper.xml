<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.deviceDump.DeviceDumpMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           code,
                           name,
                           upload_time,
                           storehouse_id,
                           (select name from store where id = storehouse_id) storehouse_name,
                           upload_user_id,
                           handle_user_id,
                           device_dump_is_dump(id, tenant_id)                is_dump,
                           remark,
                           creator,
                           create_time,
                           tenant_id
        <!--@sql from device_dump-->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.deviceDump.DeviceDump">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="storehouse_id" property="storehouseId"/>
        <result column="storehouse_name" property="storehouseName"/>
        <result column="upload_user_id" property="uploadUserId"/>
        <result column="handle_user_id" property="handleUserId"/>
        <result column="is_dump" property="isDump"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_dump
        <where>
            <if test="code != null">
                and code like '%' || #{code} || '%'
            </if>
            <if test="name != null">
                and "name" like '%' || #{name} || '%'
            </if>
            <if test="uploadUserId != null and uploadUserId != ''">
                and upload_user_id = #{uploadUserId}
            </if>
            <if test="uploadDepartmentId != null and uploadDepartmentId != ''">
                and is_user_at_department(device_dump.upload_user_id, #{uploadDepartmentId})
            </if>
            <if test="handleUserId != null and handleUserId != ''">
                and handle_user_id = #{handleUserId}
            </if>
            <if test="handleDepartmentId != null and handleDepartmentId != ''">
                and is_user_at_department(device_dump.handle_user_id, #{handleDepartmentId})
            </if>
            <if test="createTime != null">
                and onday(create_time, #{createTime})
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update device_dump
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="uploadTime != null">
                upload_time = #{uploadTime},
            </if>
            <if test="storehouseId != null">
                storehouse_id = #{storehouseId},
            </if>
            <if test="uploadUserId != null">
                upload_user_id = #{uploadUserId},
            </if>
            <if test="handleUserId != null">
                handle_user_id = #{handleUserId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="dump">
        update device_storage_journal storage
        set scrapped_time = now()
        from (select device_label_code
              from device_dump_detail detail
              where main_id = #{id}) as valueTable(device_label_code)
        where storage.device_label_code = valueTable.device_label_code
    </update>

    <select id="getChildrenId" resultType="java.lang.String">
        select id
        from device_dump_detail
        where main_id = #{id}
    </select>

    <select id="isUserHandleUser" resultType="boolean">
        select handle_user_id = #{userId}
        from device_dump
        where id = #{id}
    </select>
</mapper>