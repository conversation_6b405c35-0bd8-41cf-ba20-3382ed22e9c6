import{b as e,z as s}from"./index-r0dFAfgr.js";import{s as o}from"./printUtils-C-AxhDcd.js";const r=()=>s({url:"/api/spp/partitionCust/template",method:"get",responseType:"blob"}),i=async()=>{try{e.info("正在下载模板...");const t=await r();o(t.data,"分区用户挂接模板")}catch{e.error("下载模板失败")}},n=t=>s({url:"/api/spp/partitionCust/importSave",method:"post",data:t}),u=t=>s({url:"/api/spp/partitionCust/list",method:"get",params:t}),m=t=>s({url:"/api/spp/partitionCust/export",method:"get",params:t,responseType:"blob"}),d=t=>s({url:"/api/spp/partitionCust",method:"post",data:t}),l=t=>s({url:"/api/spp/partitionCust",method:"delete",data:t}),c=t=>s({url:"/api/spp/partitionCust/batchRemove",method:"post",data:t}),h=t=>s({url:"/api/spp/partitionCust/batchSave",method:"post",data:t}),D=()=>s({url:"/api/jinzhou/dmaBook/getBookList",method:"get"}),M=t=>s({url:`/api/jinzhou/dmaBook/${t}`,method:"get"}),A=t=>s({url:"/api/jinzhou/dmaBook/save",method:"post",data:t});export{i as D,m as E,D as G,n as I,h as M,d as P,M as a,u as b,c,l as d,A as p};
