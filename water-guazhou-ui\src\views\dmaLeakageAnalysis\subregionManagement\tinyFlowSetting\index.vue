<!-- 小流指标设置 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>

    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <editDialogForm
      ref="refEditDialog"
      :default-value="TableConfig.currentRow"
      @success="refreshData"
    ></editDialogForm>
  </TreeBox>
</template>
<script lang="ts" setup>
import editDialogForm from './components/editDialogForm.vue'
import { usePartition } from '@/hooks/arcgis'
import { CopyMeterTypes, GetDMAMinFlowConfig } from '@/api/mapservice/dma'

type EditDialogIns = InstanceType<typeof editDialogForm>

const refEditDialog = ref<EditDialogIns>()
const partition = usePartition()
const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: false,
  isFilterTree: true,
  currentProject: null,
  title: '选择分区',
  expandOnClickNode: false,
  treeNodeHandleClick: async (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      TreeData.currentProject = data
      await refreshData()
    }
  }
})
const refreshTree = () => {
  partition.getTree().then(() => {
    TreeData.data = partition.Tree.value
    TreeData.currentProject = partition.Tree.value?.[0]
    refreshData()
  })
}
// 列表
const TableConfig = reactive<ITable>({
  loading: false,
  indexVisible: true,
  dataList: [],
  columns: [
    {
      prop: 'partitionName',
      label: '分区名称',
      align: 'center',
      fixed: 'left',
      minWidth: 160
    },
    {
      prop: 'copyMeterType',
      label: '抄表类型',
      align: 'center',
      minWidth: 120,
      formatter: (row, val) => {
        return CopyMeterTypes[val]
      }
    },
    {
      prop: 'nightFlow',
      label: '夜间最小流',
      align: 'center',
      subColumns: [
        {
          prop: 'nightFlowMin',
          label: '最小值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        },
        {
          prop: 'nightFlowMax',
          label: '最大值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        }
      ]
    },
    {
      prop: 'nightValue',
      label: '夜间最小值',
      align: 'center',
      subColumns: [
        {
          prop: 'nightValueMin',
          label: '最小值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        },
        {
          prop: 'nightValueMax',
          label: '最大值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        }
      ]
    },
    {
      prop: 'unitPipeNightFlow',
      label: '单位管长夜间流量',
      align: 'center',
      subColumns: [
        {
          prop: 'unitPipeNightFlowMin',
          label: '最小值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        },
        {
          prop: 'unitPipeNightFlowMax',
          label: '最大值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        }
      ]
    },
    {
      prop: 'mnfDivDayAvgHourFlow',
      label: 'MNF/日均小时流量',
      align: 'center',
      subColumns: [
        {
          prop: 'mnfDivDayAvgHourFlowMin',
          label: '最小值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        },
        {
          prop: 'mnfDivDayAvgHourFlowMax',
          label: '最大值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        }
      ]
    },
    {
      prop: 'incrBase',
      label: '基准值',
      unit: '(m³/h)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'incrWarn',
      label: '黄色预警值',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'incrError',
      label: '红色预警值',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'isDefault',
      label: '是否默认',
      align: 'center',
      subColumns: [
        {
          prop: 'stockType',
          label: '存量',
          align: 'center',
          minWidth: 120,
          formatter: (row, val) => {
            return val === '1' ? '手动' : val === '2' ? '自动' : val
          }
        },
        {
          prop: 'incrType',
          label: '增量',
          align: 'center',
          minWidth: 120,
          formatter: (row, val) => {
            return val === '1' ? '手动' : val === '2' ? '自动' : val
          }
        }
      ]
    }
  ],
  operationWidth: 120,
  operations: [
    {
      perm: true,
      text: '修改',
      isTextBtn: false,
      iconifyIcon: 'ep:edit',
      click: row => handleAddEdit(row)
    }
  ],
  pagination: {
    hide: true
  }
})

// 修改
const handleAddEdit = async (row: any) => {
  TableConfig.currentRow = row
  await nextTick()
  refEditDialog.value?.openDialog()
}
// 数据获取
const refreshData = async () => {
  if (!TreeData.currentProject) return
  TableConfig.loading = true
  try {
    const res = await GetDMAMinFlowConfig(TreeData.currentProject.id)
    TableConfig.dataList = res.data.data || []
  } catch (error) {
    TableConfig.dataList = []
  }
  TableConfig.loading = false
}
onMounted(() => {
  refreshTree()
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}

.card-table {
  height: 100%;
  width: 100%;
}
</style>
