<template>
  <div class="img-box">
    <div class="img-top">
      <img
        :src="state.currentImage"
        alt=""
      />
    </div>
    <div>
      <el-carousel
        :interval="4000"
        type="card"
        height="120px"
        class="mg_top_10"
        @change="toggle"
      >
        <el-carousel-item
          v-for="(item, index) in state.imgs"
          :key="index"
        >
          <img
            :src="item.img"
            style="width: 100%; height: 160px"
          />
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>
<script lang="ts" setup>
const getScrollPic = (path: string) => {
  return new URL(`../imgs/scroll/${path}`, import.meta.url).href
}
const state = reactive<{
  currentImage: string
  imgs: { label: string; img: string }[]
}>({
  currentImage: '',

  // 'http://***************:10000/bigData/1c8049e92d5f49109e1a6b909ca13521111.png',
  imgs: [
    ...Array.from({ length: 8 }).map((item, i) => {
      const path = `img_${i + 1}.jpg`
      return {
        label: i.toString(),
        img: getScrollPic(path)
      }
    })
    // {
    //   label: '沉淀池',
    //   img: 'http://***************:10000/bigData/1c8049e92d5f49109e1a6b909ca13521111.png'
    // },
    // {
    //   label: '反应池',
    //   img: 'http://***************:10000/bigData/aa8854b70b56464d8bfbce144892bee4222.png'
    // },
    // {
    //   label: '加药加氯间',
    //   img: 'http://***************:10000/bigData/6b9d7545d59d42829fa251813446c9b2333.png'
    // },
    // {
    //   label: '空压机鼓风机',
    //   img: 'http://***************:10000/bigData/b0b2fceb957649729e606a46e174b3bcIMG_20210318_100529.jpg'
    // },
    // {
    //   label: '浓缩池',
    //   img: 'http://***************:10000/bigData/1e18fa90a3ea4e37a49cb637914911f7555.png'
    // },
    // {
    //   label: '排污泵房',
    //   img: 'http://***************:10000/bigData/dd0cadf1f7064c939cea1866060ccadc666.png'
    // }
  ]
})
const toggle = (id: number) => {
  state.currentImage = state.imgs[id].img
}
onMounted(() => {
  toggle(0)
})
</script>
<style lang="scss" scoped>
.img-box {
  width: 100%;
  height: 100%;
  .img-top {
    height: 270px;
    margin-bottom: 12px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
