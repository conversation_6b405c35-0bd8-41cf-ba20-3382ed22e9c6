<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.input.InputCarInfoMapper">

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.input.InputCarInfo">
        SELECT
        A.*
        FROM
        tb_input_car_info A
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.carNo != null and param.carNo != ''">
                AND a.car_no = #{param.carNo}
            </if>
            <if test="param.inTimeStart != null and param.inTimeEnd != null">
                AND a.in_time BETWEEN #{param.inTimeStart} AND #{param.inTimeEnd}
            </if>
            <if test="param.outTimeStart != null and param.outTimeEnd != null">
                AND a.out_time BETWEEN #{param.outTimeStart} AND #{param.outTimeEnd}
            </if>
            <if test="param.createUser != null and param.createUser != ''">
                AND a.create_user = #{param.createUser}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>


</mapper>