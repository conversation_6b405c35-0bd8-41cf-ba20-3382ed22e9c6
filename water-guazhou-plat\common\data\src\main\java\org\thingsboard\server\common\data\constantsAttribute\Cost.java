/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.constantsAttribute;

import lombok.Data;
import org.thingsboard.server.common.data.utils.DateUtils;

@Data
public class Cost implements Comparable<Cost> {
    private String name;
    private String type;
    private String startTime;
    private String price;
    private String interval;

    public Cost(String type, String startTime, String price, String interval) {
        this.type = type;
        this.startTime = startTime;
        this.price = price;
        this.interval = interval;
    }

    @Override
    public int compareTo(Cost o) {
        return Long.compare(DateUtils.str2Date(o.startTime, DateUtils.DATE_FORMATE_DAY).getTime(), DateUtils.str2Date(this.startTime, DateUtils.DATE_FORMATE_DAY).getTime());
    }

}
