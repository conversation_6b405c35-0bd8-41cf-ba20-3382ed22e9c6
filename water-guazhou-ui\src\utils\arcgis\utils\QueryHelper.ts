import * as query from '@arcgis/core/rest/query'
import FindParameters from '@arcgis/core/rest/support/FindParameters'
import Query from '@arcgis/core/rest/support/Query'
import * as geometryService from '@arcgis/core/rest/geometryService.js'
import * as find from '@arcgis/core/rest/find.js'
import BufferParameters from '@arcgis/core/rest/support/BufferParameters'
import { queryLayerClassName } from '@/api/mapservice'

/**
 * 查询详情指定图层的所有oid
 * @param layerIds 要查询的所有图层id
 * @param index 开始查询的图层id的索引,默认0
 *
 * @param queryParams 查询的参数
 * @returns
 */
export const queryMultiLayerForIds = async (
  layerInfos?: ILayerInfo[],
  queryParams?: { geometry?: __esri.Geometry; where?: string }
) => {
  const tabs: any[] = []
  if (!layerInfos?.length) {
    return tabs
  }
  const oidUrl = window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService
  const p = await Promise.allSettled(
    layerInfos?.map(item => {
      return excuteQueryForIds(
        `${oidUrl}/${item.layerid}`,
        initQueryParams({
          orderByFields: ['OBJECTID asc'],
          ...(queryParams || {})
        })
      )
    })
  )
  p.map((item, i) => {
    console.log(item)
    const tab = layerInfos[i].layername
    const alloids = item.status === 'fulfilled' ? item.value : []
    tabs[i] = {
      label: tab + '(' + (alloids?.length || 0) + ')',
      name: tab,
      data: alloids || []
    }
  })
  return tabs
}

/**
 * 执行服务查询
 * @param url 查询路径
 * @param params 查询参数
 * @returns
 */
export const excuteQuery = (url: string, params?: __esri.Query) => {
  params = params || initQueryParams()
  return (query as __esri.query).executeQueryJSON(url, params)
}
/**
 * 执行服务查询(查询id数组)
 * @param url 查询路径
 * @param params 查询参数
 * @returns
 */
export const excuteQueryForIds = (url: string, params?: __esri.Query) => {
  params = params || initQueryParams()
  return query.executeForIds(url, params)
}

/**
 * 初始化Query的参数
 * @param options
 * @returns
 */
export const initQueryParams = (options?: {
  returnGeometry?: boolean
  outFields?: string[]
  objectIds?: number[]
  orderByFields?: string[]
  where?: string
  geometry?: __esri.Geometry
}) => {
  return new Query({
    returnGeometry: true,
    outFields: ['*'],
    ...(options || {})
  })
}

/**
 * 执行查询服务(查询数量)
 * @param url
 * @param params
 * @returns
 */
export const excuteQueryForCount = (url: string, params?: __esri.Query) => {
  params = params || initQueryParams()
  return query.executeForCount(url, params)
}
export const queryBufferPolygon = async (params: __esri.BufferParameters) => {
  const result = await (geometryService as __esri.geometryService).buffer(
    window.SITE_CONFIG.GIS_CONFIG.gisUtilitiesService + window.SITE_CONFIG.GIS_CONFIG.gisGeometryService,
    params
  )
  return result
}
export const initFindParams = (options?: {
  layerIds?: number[]
  searchFields?: string[]
  returnGeometry?: boolean
  searchText?: string
  contains: boolean
}) => {
  return new FindParameters({
    ...options
  }) as __esri.FindParameters
}
export const excuteFind = (url, params?: __esri.FindParameters) => {
  params = params || initFindParams()
  return find.find(url, params)
}
export const initBufferParams = (options: {
  bufferSpatialReference?: any
  distances?: number[]
  geometries?: __esri.Geometry[]
  outSpatialReference?: any
  geodesic?: boolean
  unit?: 'feet' | 'kilometers' | 'meters' | 'miles' | 'nautical-miles' | 'yards'
  unionResults?: boolean
}) => {
  return new BufferParameters({
    ...options
  }) as __esri.BufferParameters
}

/**
 * (方法说明)获取当前页的objectid集合
 * @method (方法名)
 * @for (所属类名)
 * @param {(参数类型)} (参数名) (参数说明)
 * @return {(返回值类型)} (返回值说明)
 */
export const getCurrentPageOIDs = (alloids: number[], pageindex: number, pagesize: number): number[] => {
  const subobjectids: any[] = []
  const total = alloids.length
  if (total === 0) {
    return []
  }
  const totalpage = Math.ceil(total / pagesize)
  if (pageindex <= totalpage) {
    const startindex = (pageindex - 1) * pagesize
    const endindex = pageindex * pagesize
    for (let i = startindex; i < total && i < endindex; i++) {
      subobjectids.push(alloids[i])
    }
  }
  return subobjectids
}

/**
 * 查询详情指定图层的所有oid
 * @param layerIds 要查询的所有图层id
 * @param index 开始查询的图层id的索引,默认0
 *
 * @param queryParams 查询的参数
 * @returns
 */
export const getLayerOids = async (
  layerIds?: any[],
  layerInfos?: any[],
  queryParams?: { geometry?: __esri.Geometry; where?: string }
) => {
  if (!layerIds?.length) return []
  if (!layerInfos?.length) {
    const res = await queryLayerClassName(layerIds)
    layerInfos = res.data?.result?.rows || []
  }
  const tabs: any[] = []
  const getTab = async (index: number) => {
    if (index >= layerIds.length) return
    try {
      const id = layerIds[index]
      // 图层id小于0则表示未找到此图层，直接进行下一个循环
      if (id < 0) {
        await getTab(++index)
        return
      }
      const tab = layerInfos?.find(item => item.layerid === id).layername
      const oidUrl = window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService
      const alloids = await excuteQueryForIds(
        `${oidUrl}/${id}`,
        initQueryParams({
          orderByFields: ['OBJECTID asc'],
          ...(queryParams || {})
        })
      )
      tabs.push({
        label: tab + '(' + (alloids?.length || 0) + ')',
        name: tab,
        data: alloids || []
      })

      await getTab(++index)
    } catch (error) {
      console.log('发生错误，停止递归')
    }
  }
  await getTab(0)
  return tabs
}
