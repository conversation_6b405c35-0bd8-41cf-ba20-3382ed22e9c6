package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.construction.SoConstructionApplyFlowService;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyFlow;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyFlowPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyFlowSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/constructionApplyFlow")
public class SoConstructionApplyFlowController extends BaseController {
    @Autowired
    private SoConstructionApplyFlowService service;

    @GetMapping
    public IPage<SoConstructionApplyFlow> findAllConditional(SoConstructionApplyFlowPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public SoConstructionApplyFlow save(@RequestBody SoConstructionApplyFlowSaveRequest req) {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionApplyFlowSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

}