package org.thingsboard.server.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.SupplierEntity;
import org.thingsboard.server.dao.supplier.SupplierService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 供应商
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@RestController
@RequestMapping("api/supplier")
public class SupplierController extends BaseController {
    @Autowired
    private SupplierService supplierService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam(required = false, defaultValue = "") String address,
                                 @RequestParam(required = false, defaultValue = "") String status,
                                 @RequestParam(required = false, defaultValue = "") String importance,
                                 int page, int size) throws ThingsboardException {

        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(supplierService.getList(name, address, status, importance, page, size, tenantId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody SupplierEntity supplierEntity) throws ThingsboardException {
        supplierEntity.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        supplierEntity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(supplierService.save(supplierEntity));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return supplierService.delete(ids);
    }

}
