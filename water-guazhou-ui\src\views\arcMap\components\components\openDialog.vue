<template>
  <!-- 底部弹窗 -->
  <Panel
    ref="refPanel"
    custom-class="gis-bottom-drawer"
    :telport="telport"
    :draggable="false"
    :max-min="false"
    :before-close="() => $emit('close')"
  >
    <template #header>
      <slot name="header">
        <Search
          ref="refTab"
          :config="props.config?.tabs"
        />
      </slot>
    </template>
    <div
      v-if="show"
      class="table-box"
    >
      <div class="table_left">
        <slot name="left">
          <FormTable
            v-if="props.config?.left"
            :config="props.config?.left.TableConfig"
          ></FormTable>
        </slot>
      </div>
      <div class="table_right">
        <slot name="right">
          <FormTable
            v-if="props.config?.right"
            :config="props.config?.right.TableConfig"
          ></FormTable>
        </slot>
      </div>
    </div>
  </Panel>
</template>

<script lang="ts" setup>
import { IPanelIns } from '@/components/type'

defineEmits(['close'])

const refPanel = ref<IPanelIns>()

const show = ref(false)

const props = defineProps<{
  telport?: string
  config?: {
    tabs?: any
    left?: any
    right?: any
  }
}>()

const openDialog = async () => {
  show.value = true
  refPanel.value?.Open()
}

const closeDialog = () => {
  show.value = false
  refPanel.value?.Close()
}

defineExpose({
  openDialog,
  closeDialog
})
</script>

<style lang="scss" scoped>
.table-box {
  height: 100%;
  display: flex;
  align-items: center;
}

.flex {
  display: flex;
}

.table_left {
  height: 100%;
  align-items: center;
  padding-right: 8px;
}

.table_right {
  flex: 1;
  height: 100%;
  padding-left: 8px;
  border-left: 2px solid var(--el-border-color-lighter);
}
</style>
