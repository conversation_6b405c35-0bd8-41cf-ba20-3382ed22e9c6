package org.thingsboard.server.dao.totalreport;


import org.apache.poi.ss.usermodel.*;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.sql.smartProduction.totalreport.PrimeCostMapper;
import org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.PrimeCost;
import org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.PrimeCostStatisticRequest;
import org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.PrimeCostStatisticResult;
import org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.PrimeCostStatisticResultOfMedical;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PrimeCostService {
    @Autowired
    private PrimeCostMapper primeCostMapper;

    @Transactional
    public boolean saveAll(List<PrimeCost> costs) {
        for (PrimeCost cost : costs) {
            cost.setRecordTime(new DateTime(cost.getYm()).millisOfDay().withMinimumValue().toDate());
        }
        int count = primeCostMapper.saveAll(costs);
        for (PrimeCost cost : costs) {
            cost.setRecordTime(new DateTime(cost.getYm()).dayOfMonth().withMinimumValue().millisOfDay().withMinimumValue().toDate());
        }
        costs.stream().filter(x -> x.getPrice() != null).forEach(primeCost -> primeCostMapper.adjustYMPrice(primeCost));
        return count > 0;
    }

    public PrimeCostStatisticResult statistic(PrimeCostStatisticRequest request) {
        PrimeCostStatisticResult result = new PrimeCostStatisticResult();
        result.setWaterPriceInfoMap(primeCostMapper.getWaterPriceInfoList(request));
        result.setMedicalPriceInfoMap(primeCostMapper.getMedicalPriceInfoList(request).stream()
                .collect(Collectors.groupingBy(PrimeCostStatisticResultOfMedical::getFactory)));
        result.setPowerPriceInfoMap(primeCostMapper.getPowerPriceInfoList(request));
        result.calculate();
        return result;
    }


    public List<PrimeCost> excelAnalysis(InputStream inputStream) throws IOException {
        List<PrimeCost> primeCostList = new ArrayList<PrimeCost>();
        Workbook workbook = null;
        workbook = WorkbookFactory.create(inputStream);
        inputStream.close();
        Sheet sheet = workbook.getSheetAt(0);
        int rowLength = sheet.getLastRowNum() + 1;
        Row row = sheet.getRow(0);
        int colLength = row.getLastCellNum();
        for(int i=1;i<rowLength;i++){
            row = sheet.getRow(i);
            row.getCell(3).setCellType(CellType.STRING);
            row.getCell(4).setCellType(CellType.STRING);
            row.getCell(5).setCellType(CellType.STRING);
            row.getCell(6).setCellType(CellType.STRING);
        }
        return primeCostList;

    }

}
