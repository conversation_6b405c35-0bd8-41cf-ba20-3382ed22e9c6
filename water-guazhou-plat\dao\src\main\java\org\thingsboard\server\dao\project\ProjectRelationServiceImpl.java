package org.thingsboard.server.dao.project;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.project.ProjectTreeVO;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.ProjectRelationEntity;
import org.thingsboard.server.dao.model.sql.RelationEntity;
import org.thingsboard.server.dao.sql.project.ProjectRelationRepository;
import org.thingsboard.server.dao.sql.project.ProjectRepository;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class ProjectRelationServiceImpl implements ProjectRelationService {

    @Autowired
    private ProjectRelationRepository projectRelationRepository;

    @Autowired
    private ProjectRepository projectRepository;


    @Override
    public List<ProjectRelationEntity> findProjectRelationByEntityTypeAndProjectId(String entityType, String projectId) {
        return projectRelationRepository.findByEntityTypeAndProjectId(entityType, projectId);
    }

    @Override
    public boolean mountEntityToProject(String entityType, String projectId, List<String> entityIdStringList) {
        // 删除相关实体关联
        projectRelationRepository.deleteByEntityTypeAndEntityIdIn(entityType, entityIdStringList);
        ProjectEntity one = projectRepository.findOne(projectId);
        // 挂载实体到项目
        if (one != null) {
            // 保存
            List<ProjectRelationEntity> saveList = entityIdStringList
                    .stream()
                    .map(entityId -> new ProjectRelationEntity(projectId, entityId, entityType, System.currentTimeMillis()))
                    .collect(Collectors.toList());

            projectRelationRepository.save(saveList);
            return true;
        }
        return false;
    }

    @Override
    public boolean mountEntityToProject(String entityType, List<String> projectId, List<String> entityIdStringList) {
        // 删除相关实体关联
        projectRelationRepository.deleteByEntityTypeAndEntityIdIn(entityType, entityIdStringList);
        projectId.forEach(id -> {
            ProjectEntity one = projectRepository.findOne(id);
            // 挂载实体到项目
            if (one != null) {
                // 保存
                List<ProjectRelationEntity> saveList = entityIdStringList
                        .stream()
                        .map(entityId -> new ProjectRelationEntity(id, entityId, entityType, System.currentTimeMillis()))
                        .collect(Collectors.toList());

                projectRelationRepository.save(saveList);
            }
        });

        return true;
    }

    @Override
    public List<ProjectEntity> findProjectRelationByEntityTypeAndEntityId(String entityType, String entityId) {
        return projectRelationRepository.findProjectRelationByEntityTypeAndEntityId(entityType, entityId);
    }

    @Override
    public List<ProjectTreeVO> findProjectTreeVOByEntityTypeAndEntityId(String entityType, String entityId) {
        List<ProjectEntity> list = findProjectRelationByEntityTypeAndEntityId(entityType, entityId);
        return projectEntityListToProjectTreeVOList(list);
    }

    @Override
    public void removeProjectRelation(String projectId) {
        projectRelationRepository.deleteByProjectId(projectId);
    }

    @Override
    public List<ProjectRelationEntity> findByProjectIdInAndEntityType(List<String> projectList, String entityType) {
        return projectRelationRepository.findByProjectIdInAndEntityType(projectList, entityType);
    }

    @Override
    public List<ProjectRelationEntity> findByEntityTypeAndEntityIdIn(String entityType, List<String> entityIdList) {
        return projectRelationRepository.findByEntityTypeAndEntityIdIn(entityType, entityIdList);
    }

    private List<ProjectTreeVO> projectEntityListToProjectTreeVOList(List<ProjectEntity> projectList) {
        return projectList.stream().map(this::projectEntityToProjectTreeVO).collect(Collectors.toList());
    }

    /**
     * projectEntity转换projectTreeVO
     *
     * @param project
     * @return
     */
    private ProjectTreeVO projectEntityToProjectTreeVO(ProjectEntity project) {
        ProjectTreeVO treeVO = new ProjectTreeVO();
        treeVO.setId(project.getId());
        treeVO.setName(project.getName());
        treeVO.setParentId(project.getParentId());
        treeVO.setAdditionalInfo(project.getAdditionalInfo());
        treeVO.setCreateTime(project.getCreateTime());
        treeVO.setTenantId(project.getTenantId());
        treeVO.setType(project.getType());
        int count = projectRepository.countByParentId(project.getId());
        treeVO.setLeaf(count <= 0);

        return treeVO;
    }


    private String mountEntityToParentProject(String parentProjectId, String entityType, List<String> entityIdStringList) {
        if (parentProjectId.equals(DataConstants.ROOT_PROJECT_PARENT_ID)) {
            return parentProjectId;
        }
        ProjectEntity project = projectRepository.findOne(parentProjectId);
        // 删除旧的的信息
        projectRelationRepository.deleteByEntityTypeAndProjectIdAndEntityIdIn(entityType, parentProjectId, entityIdStringList);

        // 保存
        entityIdStringList.forEach(entityId -> {
            ProjectRelationEntity relationEntity = new ProjectRelationEntity(project.getId(), entityId, entityType, System.currentTimeMillis());
            projectRelationRepository.save(relationEntity);
        });

        return mountEntityToParentProject(project.getParentId(), entityType, entityIdStringList);
    }
}
