/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.maintain;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import org.thingsboard.server.common.data.BaseData;
import org.thingsboard.server.common.data.id.*;

@Data
public class MaintainRecord extends BaseData<MaintainRecordId> {

    private DeviceId deviceId;
    private Long createTime;
    private String name;
    private JsonNode additionalInfo;
    private TenantId tenantId;
    private Long maintainTime;
    private Long startTime;
    private Long endTime;
    private MaintainId maintainId;
    private UserId maintainUser;
    private String status;
    private String projectId;

    public MaintainRecord() {
        super();
    }

    public MaintainRecord(MaintainRecordId id) {
        super(id);
    }

    public MaintainRecord(MaintainRecord maintainRecord) {
        super();
        this.deviceId = maintainRecord.getDeviceId();
        this.createTime = maintainRecord.getCreateTime();
        this.additionalInfo= maintainRecord.getAdditionalInfo();
        this.tenantId = maintainRecord.getTenantId();
        this.maintainTime=maintainRecord.getMaintainTime();
        this.name=maintainRecord.getName();
        this.startTime=maintainRecord.getStartTime();
        this.endTime=maintainRecord.getEndTime();
        this.maintainId=maintainRecord.getMaintainId();
        this.maintainUser=maintainRecord.getMaintainUser();
        this.status=maintainRecord.getStatus();
        this.projectId=maintainRecord.getProjectId();
    }

}
