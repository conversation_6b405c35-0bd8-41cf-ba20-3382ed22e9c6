import{_ as C}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as v}from"./CardTable-rdWOL4_6.js";import{_ as x}from"./CardSearch-CB_HNR-Q.js";import{z as c,C as T,c as _,r as d,bF as F,b as n,S as L,o as E,g as w,n as I,q as g}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function k(l){return c({url:"/api/base/database/upgrade/list",method:"get",params:l})}function B(l){return c({url:"/api/base/database/upgrade/getDetail",method:"get",params:{id:l}})}function U(l){return c({url:"/api/base/database/upgrade/add",method:"post",data:l})}function q(l){return c({url:"/api/base/database/upgrade/edit",method:"post",data:l})}function P(l){return c({url:"/api/base/database/upgrade/deleteIds",method:"delete",data:l})}const A={class:"wrapper"},M={__name:"databaseUpgrade",setup(l){const m=_(),p=_(),y=d({labelWidth:"100px",filters:[{type:"input",label:"升级前版本",field:"versionFrom",placeholder:"请输入起始版本号",onChange:()=>i()},{type:"input",label:"升级后版本",field:"versionTo",placeholder:"请输入目标版本号",onChange:()=>i()},{type:"select",label:"执行状态",field:"status",options:[{label:"全部",value:""},{label:"成功",value:"SUCCESS"},{label:"失败",value:"FAILED"},{label:"执行中",value:"PROCESSING"}],onChange:()=>i()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>i()},{perm:!0,type:"primary",text:"新增",click:()=>b()},{perm:!0,type:"danger",text:"批量删除",click:()=>h()}]}],defaultParams:{}}),o=d({columns:[{label:"主键ID",prop:"id"},{label:"升级前版本",prop:"versionFrom"},{label:"升级后版本",prop:"versionTo"},{label:"执行状态",prop:"status",tagType:e=>({SUCCESS:"success",FAILED:"danger",PROCESSING:"warning"})[e.status]},{label:"执行时间",prop:"executionTime",formatter:e=>F(e.executionTime).format("YYYY-MM-DD HH:mm:ss")},{label:"操作者ID",prop:"operatorId"},{label:"SQL文件路径",prop:"sqlFilePath"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>S(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>b(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>h(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{o.pagination.page=e,i()},handleSize:e=>{o.pagination.limit=e,i()}},handleSelectChange:e=>{o.selectList=e||[]}}),t=d({title:"数据库版本升级",group:[{fields:[{type:"input",label:"升级前版本",field:"versionFrom",rules:[{required:!0,message:"请输入起始版本号"}]},{type:"input",label:"升级后版本",field:"versionTo",rules:[{required:!0,message:"请输入目标版本号"}]},{type:"select",label:"执行状态",field:"status",options:[{label:"成功",value:"SUCCESS"},{label:"失败",value:"FAILED"},{label:"执行中",value:"PROCESSING"}],rules:[{required:!0,message:"请选择执行状态"}]},{type:"input",label:"SQL文件路径",field:"sqlFilePath",rules:[{required:!0,message:"请输入SQL文件路径"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"提交",submit:async e=>{var a;try{e.id?(await q(e),n.success("修改成功")):(await U(e),n.success("新增成功")),(a=p.value)==null||a.closeDialog(),i()}catch{n.error("操作失败")}}}),f=()=>{t.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),t.showSubmit=!0,t.showCancel=!0,t.cancelText="取消",t.submitText="提交"},b=e=>{var a;f(),t.title=e?"编辑升级记录":"新增升级记录",t.defaultValue={...e||{}},(a=p.value)==null||a.openDialog()},S=async e=>{var a,r;try{const s=await B(e.id),u=((a=s.data)==null?void 0:a.data)||s;f(),t.title="升级记录详情",t.defaultValue={...u},t.group[0].fields.forEach(D=>{D.disabled=!0}),t.showSubmit=!1,t.cancelText="关闭",(r=p.value)==null||r.openDialog()}catch{n.error("获取详情失败")}},h=e=>{L("确定删除？","删除提示").then(async()=>{var r;const a=e?[e.id]:((r=o.selectList)==null?void 0:r.map(s=>s.id))||[];if(!a.length){n.warning("请选择要删除的数据");return}await P(a),n.success("删除成功"),i()}).catch(()=>{})},i=async()=>{var e,a;try{const r=await k({page:o.pagination.page,size:o.pagination.limit,...((e=m.value)==null?void 0:e.queryParams)||{}}),s=((a=r.data)==null?void 0:a.data)||r;o.dataList=s.records||s,o.pagination.total=s.total||s.length||0}catch{n.error("数据加载失败")}};return E(()=>{i()}),(e,a)=>{const r=x,s=v,u=C;return w(),I("div",A,[g(r,{ref_key:"refSearch",ref:m,config:y},null,8,["config"]),g(s,{class:"card-table",config:o},null,8,["config"]),g(u,{ref_key:"refDialogForm",ref:p,config:t},null,8,["config"])])}}},Q=T(M,[["__scopeId","data-v-5f40fd3c"]]);export{Q as default};
