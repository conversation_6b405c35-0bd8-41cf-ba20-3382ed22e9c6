package org.thingsboard.server.dao.area;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.AreaEntity;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 区域
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface AreaService {
    List getTree();

    PageData getTree(String name, String shortName, int page, int size, String tenantId);

    AreaEntity save(AreaEntity areaEntity);

    String delete(List<String> ids);

    IstarResponse bindImg(AreaEntity areaEntity);
}
