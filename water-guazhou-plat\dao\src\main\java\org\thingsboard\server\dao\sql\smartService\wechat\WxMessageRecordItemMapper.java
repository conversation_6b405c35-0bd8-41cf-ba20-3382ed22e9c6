package org.thingsboard.server.dao.sql.smartService.wechat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecordItem;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecordItemCompleteStatistic;
import org.thingsboard.server.dao.util.imodel.query.smartService.wechat.WxMessageRecordItemPageRequest;

import java.util.List;

@Mapper
public interface WxMessageRecordItemMapper extends BaseMapper<WxMessageRecordItem> {
    IPage<WxMessageRecordItem> findByPage(WxMessageRecordItemPageRequest request);

    boolean update(WxMessageRecordItem entity);

    int saveAll(List<WxMessageRecordItem> items);

    boolean markMessageItemComplete(@Param("messageId") Long messageId, @Param("recordId") String recordId);

    WxMessageRecordItemCompleteStatistic completeStatistic(@Param("messageRecordId") String messageRecordId, @Param("tenantId") String tenantId);
}
