<template>
  <div class="file-uploader-wrapper">
    <el-upload
      ref="refUploader"
      class="file-uploader"
      :class="disabled ? 'is-disabled' : ''"
      :action="url ?? useAppStore().actionUrl + 'file/api/upload/image'"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      multiple
      :limit="limit || 3"
      :on-exceed="handleExceed"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload1"
      :file-list="fileList"
      :disabled="disabled"
      :accept="props.accept"
      :headers="{ 'X-Authorization': 'Bearer ' + useUserStore().token }"
    >
      <el-button v-if="!disabled" type="primary" :loading="loading">
        {{ loading ? '上传中...' : '点击上传' }}
      </el-button>
      <!-- <template #trigger>
    </template> -->
      <template #tip>
        <div v-if="tips" class="el-upload__tip">
          {{ tips }}
        </div>
      </template>
    </el-upload>
    <el-dialog
      v-if="state.previewVisible"
      v-model="state.previewVisible"
      width="40%"
      :title="state.previewTitle"
    >
      <div class="previewBox">
        <embed
          style="width: 100%; height: 560px"
          width="100%"
          height="100%"
          :src="state.iframeUrl"
          type="application/pdf"
        />
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup name="SLFileUploader">
import { UploadFile } from 'element-plus/lib/components/upload/src/upload';
import { ElUpload } from 'element-plus';
import { SLMessage } from '@/utils/Message';
import { useAppStore, useUserStore } from '@/store';
import { formateFiles } from '../SLUploader/utils';
import { downloadFile } from '@/utils/fileHelper';
import useUser from '@/hooks/user/useUser';

const refUploader = ref<InstanceType<typeof ElUpload>>();
const emit = defineEmits(['update:modelValue', 'handleSuccess']);
const props = defineProps<{
  modelValue?: string | any[];
  disabled?: boolean;
  url?: string;
  limit?: number;
  multiple?: boolean;
  returnType?: 'arrStr' | 'comma';
  tips?: string;
  accept?: any;
  beforeUpload?: (file) => any;
}>();
const loading = ref<boolean>(false);
interface RawFile {
  name: string;
  url: string;
}
const state = reactive<{
  iframeUrl: string;
  previewVisible: boolean;
  previewTitle: string;
}>({
  iframeUrl: '',
  previewVisible: false,
  previewTitle: ' 文件预览'
});
const fileList = ref<RawFile[]>(formateFiles(props.modelValue || ''));
const beforeUpload1 = (file) => {
  loading.value = true;
  props.beforeUpload?.(file);
};
const handleRemove = (file: UploadFile, fileList: UploadFile[]) => {
  const list = fileList
    ? fileList.map((item) => {
        const obj = {
          url: item.response || item.url,
          name: item.name
        };
        return obj;
      })
    : [];
  emitValue(list);
  loading.value = false;
};
const getUrlFromFile = (url: any) => {
  if (typeof url === 'object') {
    return url.url;
  }
  if (typeof url === 'string') {
    return url.split(',')[0];
  }
  return url;
};
const handlePreview = (file: UploadFile) => {
  console.log(file);
  const url: any = getUrlFromFile(file.url || file.response);
  const perviewableArr = ['png', 'jpg', 'gif', 'pdf', 'jpeg'];
  // (file.url as any).url || file.url || (file.response as any).url || file.response
  const urlItems: string[] = (url && url.split('.')) || [];
  const fileType = urlItems[urlItems.length - 1];
  const type = fileType?.split('&')[0] || '';
  if (!perviewableArr.includes(type.toLowerCase())) {
    //  return SLMessage.warning('只能预览图片、pdf文件格式')
    url && downloadFile(url, file.name || url);
  } else {
    state.iframeUrl = url;
    state.previewTitle = file.name;
    state.previewVisible = true;
  }
};
const handleExceed = () => {
  loading.value = false;
  SLMessage.warning(`最多可添加${props.limit}份文件`);
};
// const beforeRemove = async(file: UploadFile, fileList: UploadFile[]) => {
//   console.log(file, fileList)
//   fileList = fileList.filter(item => item.url !== file.url)
//   return true
//   // return ElMessageBox.confirm(`Cancel the transfert of ${file.name} ?`)
// }
const handleSuccess = (response, file, fileList) => {
  loading.value = false;
  const list = fileList
    ? fileList.map((item) => {
        const obj = {
          url: item.response || item.url,
          name: item.name
        };
        return obj;
      })
    : [];
  emitValue(list);
  emit('handleSuccess', list);
};
const handleError = () => {
  loading.value = false;
};
const emitValue = (list: any) => {
  if (!list) emit('update:modelValue', list);
  else {
    emit(
      'update:modelValue',
      props.returnType === 'arrStr'
        ? JSON.stringify(list)
        : list.map((item) => item.url).join(',')
    );
  }
};
watch(
  () => props.modelValue,
  () => {
    fileList.value = formateFiles(props.modelValue || '');
  }
);
onMounted(() => {
  if (fileList.value) {
    emitValue(fileList.value);
  }
});
</script>
<style scoped lang="scss">
.file-uploader-wrapper {
  width: 100%;
}

.file-uploader {
  width: 100%;
  margin: 0;

  &.is-disabled {
    :deep(.el-upload.el-upload--text) {
      display: none;
    }
  }
}

.previewBox {
  position: relative;
  width: 100%;
}
</style>
