package org.thingsboard.server.dao.util.imodel.query.department;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.department.Department;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class DepartmentSaveRequest extends SaveRequest<Department> {
    // "父级ID"
    @NotNullOrEmpty
    private String parentId;

    // "部门名称"
    private String name;

    // "部门类型"
    private String type;

    // "排序，升序"
    @NotNullOrEmpty
    private Integer orderNum;

    @Override
    protected Department build() {
        Department department = new Department();
        department.setParentId(parentId);
        department.setName(name);
        department.setType(type);
        department.setOrderNum(orderNum);
        department.setCreateTime(new Date());
        department.setTenantId(tenantId());
        return department;
    }
    @Override
    protected Department update(String id) {
        Department department = new Department();
        department.setId(id);
        department.setParentId(parentId);
        department.setName(name);
        department.setType(type);
        department.setOrderNum(orderNum);
        return department;
    }

}
