import{d as O,c as M,r as R,s as q,bC as B,b as y,o as D,Q as E,g as V,n as z,q as A,i as I,_ as N,bD as U}from"./index-r0dFAfgr.js";import{C as j}from"./AnimatedLinesLayer-B2VbV4jv.js";import{g as G,a as Q}from"./config-DncLSA-r.js";import{n as W,s as $,i as H}from"./FeatureHelper-Da16o0mu.js";import{e as J}from"./GPHelper-fLrvVD-A.js";import"./IdentifyResult-4DxLVhTm.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import{g as K}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{e as X}from"./ViewHelper-BGCZjxXH.js";import{v as Y}from"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./index-DeAQQ1ej.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";import"./fieldconfig-Bk3o1wi7.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const ve=O({__name:"TemplatePrint",props:{view:{}},setup(L){const a=L,i={},n=M(),h=R({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"标题"},fields:[{type:"input",field:"title",rules:[{required:!0,message:"请输入标题"}]}]},{fieldset:{desc:"格式"},fields:[{type:"select",field:"type",clearable:!1,options:G()}]},{id:"print-template",fieldset:{desc:"模板"},fields:[{type:"select",clearable:!1,field:"template",options:Q()}]},{fieldset:{desc:"比例尺"},fields:[{type:"input-number",field:"scale",placeholder:"比如：500",prepend:"1:",rules:[{required:!0,message:"请输入比例尺"}]}]},{fieldset:{desc:"选取范围"},fields:[{type:"btn-group",btns:[{perm:!0,text:"点击生成范围",styles:{width:"100%"},click:async()=>{var t;try{await((t=n.value)==null?void 0:t.Submit())!==!1&&(w(),i.graphic&&X(a.view,i.graphic.geometry.extent,!0))}catch(e){console.log(e)}},type:"success"}]}]},{fieldset:{desc:"高级设置"},fields:[{type:"input",label:"出图人员",field:"username"},{type:"input",label:"出图单位",field:"depart"},{type:"btn-group",btns:[{perm:!0,svgIcon:q(U),text:"打印",click:()=>P()}]}]},{id:"print-result",fieldset:{desc:"打印结果"},fields:[{type:"list",data:[],style:{height:"100px"},itemStyle:t=>({textDecoration:t.url?"underline":"none",color:"#00ff00"}),formatter:(t,e)=>t&&t+(e.status==="success"?"（打印完成,点击下载！）":e.status==="printing"?"（正在打印...）":e.status==="failed"?"打印失败!":""),displayField:"title",nodeClick:t=>{t.url&&B(t.url,t.title)}}]}],defaultValue:{type:"pdf",template:"a3-landscape",scale:500}}),w=(t="add")=>{var f,g,b,x,_,F,S;if(!a.view)return;const e=(f=n.value)==null?void 0:f.dataForm.scale;if(!e){t==="add"&&y.warning("请输入比例尺");return}const s=(g=n.value)==null?void 0:g.dataForm.template;if(!s){t==="add"&&y.warning("请选择模板");return}(b=i.graphicsLayer)==null||b.removeAll(),i.graphic=void 0;const u=1,d=(x=h.group.find(v=>v.id==="print-template"))==null?void 0:x.fields[0],l=(F=(_=d==null?void 0:d.options)==null?void 0:_.find(v=>v.value===s))==null?void 0:F.data;if(!l)return;i.curTemplate=l;const m=l.pageSize[0]*e/100*u,c=l.pageSize[1]*e/100*u,p=[],o=a.view.center;p.push([o.x-m/2,o.y+c/2]),p.push([o.x+m/2,o.y+c/2]),p.push([o.x+m/2,o.y-c/2]),p.push([o.x-m/2,o.y-c/2]);const r=W(p,a.view.spatialReference,$("polygon",{outlineColor:[255,0,0],color:[0,0,0,.3],outlineWidth:1}),{id:"template-rect"});i.graphic=r,r&&((S=i.graphicsLayer)==null||S.add(r))},T=()=>{w("update")},k=()=>{var t;(t=a.view)==null||t.on("drag",T)},C=()=>{var t;i.graphicsLayer&&((t=a.view)==null||t.map.remove(i.graphicsLayer))},P=async()=>{var d,l,m,c,p,o;if(!a.view||await((d=n.value)==null?void 0:d.Submit())===!1)return;if(!i.graphic){y.warning("请先生成打印范围");return}const e={id:Y(),url:"",status:"printing",title:(l=n.value)==null?void 0:l.dataForm.title},s=(m=h.group.find(r=>r.id==="print-result"))==null?void 0:m.fields[0];s.data.unshift(e);try{const r=((c=n.value)==null?void 0:c.dataForm)||{},f=H((o=(p=i.graphic)==null?void 0:p.geometry)==null?void 0:o.extent);console.log("scale:"+f);const g=await J(a.view,new j({format:r.type,exportOptions:{dpi:96},outScale:r.scale||500,layout:r.template,layoutOptions:{titleText:r.title,authorText:r.username,copyrightText:r.depart,scalebarUnit:"Meters",elementOverrides:{"North Arrow":{visible:!0}}},showLabels:!0}));e.status="success",e.url=g.url}catch{e.status="failed"}const u=s.data.findIndex(r=>r.id===e.id);u!==-1&&s.data.splice(u,1),s.data.unshift(e)};return D(()=>{if(!a.view){y.error("地图未加载，请稍候再试");return}i.graphicsLayer=K(a.view,{id:"template-print",title:"模板打印绘制图层"}),k()}),E(()=>{C()}),(t,e)=>{const s=N;return V(),z("div",null,[A(s,{ref_key:"refForm",ref:n,config:I(h)},null,8,["config"])])}}});export{ve as default};
