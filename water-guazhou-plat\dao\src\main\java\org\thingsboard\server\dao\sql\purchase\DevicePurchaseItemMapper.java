package org.thingsboard.server.dao.sql.purchase;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseItem;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchaseItemPageRequest;

import java.util.List;

@Mapper
public interface DevicePurchaseItemMapper extends BaseMapper<DevicePurchaseItem> {
    IPage<DevicePurchaseItem> findByPage(DevicePurchaseItemPageRequest request);

    boolean update(DevicePurchaseItem attr);

    boolean saveAll(List<DevicePurchaseItem> list);

    String getNameByItemId(String id);

    int deleteByMainIdOnIdNotIn(@Param("id") String id, @Param("idList") List<String> idList);

    boolean submitInquiry(String id);

    int getStatus(String id);
}
