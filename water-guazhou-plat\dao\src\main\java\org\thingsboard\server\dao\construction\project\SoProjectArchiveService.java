package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectArchive;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectFiles;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectArchivePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectArchiveSaveRequest;

public interface SoProjectArchiveService {
    /**
     * 分页条件查询项目归档信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoProjectArchive> findAllConditional(SoProjectArchivePageRequest request);

    /**
     * 保存项目归档信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoProjectArchive save(SoProjectArchiveSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoProjectArchive entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 是否允许归档
     *
     * @param projectCode 项目编码
     * @param tenantId    客户id
     * @return 是否允许
     */
    boolean canArchive(String projectCode, String tenantId);

    /**
     * 通过项目编码获取所有其下的文件（工程、工程合同....）
     *
     * @param projectCode 项目编码
     * @param tenantId    客户id
     * @return 所有附件信息
     */
    SoProjectFiles findFilesByProjectCode(String projectCode, String tenantId);

}
