package org.thingsboard.server.dao.sql.accessControl;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.AccessControl;

import java.util.List;

public interface AccessControlRepository extends JpaRepository<AccessControl, String> {

    @Query("SELECT ac FROM AccessControl ac " +
            "WHERE ac.projectId IN ?1 " +
            "ORDER BY ac.createTime DESC")
    Page<AccessControl> findList(List<String> projectList, Pageable pageable);
}
