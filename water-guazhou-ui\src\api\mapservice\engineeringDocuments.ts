import { request } from '@/plugins/axios'
import { EPipeFileStatus } from '@/views/arcMap/smartPipe/EngineeringArchives/config'

/**
 * 查询Gis多媒体文件列表
 * @param params
 * @returns
 */
export const GetGisFileList = (
  params: IQueryPagerParams & Partial<{ fileType: string; deviceType: string; deviceCode: number; status: string }>
) => {
  return request({
    url: '/api/gis/file/list',
    method: 'get',
    params
  })
}
/**
 * 保存Gis多媒体文件
 * @param params
 * @returns
 */
export const PostGisFile = (params: any) => {
  return request({
    url: '/api/gis/file/save',
    method: 'post',
    data: params
  })
}
/**
 * 批量保存Gis多媒体文件
 * @param params
 * @returns
 */
export const PostGisFileBatch = (params: any[]) => {
  return request({
    url: '/api/gis/file/batch/save',
    method: 'post',
    data: params
  })
}
/**
 *
 * @param ids 状态修改ID
 * @param status 状态： 0： 正常, 1: 已删除
 */
export const PostGisFileStatus = (ids: string[], status: EPipeFileStatus) => {
  return request({
    url: `/api/gis/file/changeStatus/${status}`,
    method: 'post',
    data: ids
  })
}
/**
 * 删除Gis多媒体文件
 * @param ids
 * @returns
 */
export const DeleteGisFiles = (ids: string[]) => {
  return request({
    url: '/api/gis/file/remove',
    method: 'delete',
    data: ids
  })
}
/**
 * 查询Gis管网工程列表
 * @param params
 * @returns
 */
export const GetGisProjectList = (params: any) => {
  return request({
    url: '/api/gis/project/list',
    method: 'get',
    params
  })
}
/**
 * 保存Gis管网工程
 * @param params
 * @returns
 */
export const PostGisProject = (
  params: IQueryPagerParams &
    Partial<{
      name: string
      /**
       * 竣工开始时间，格式：yyyy-MM-dd
       */
      beginTime: string
      /**
       * 竣工结束时间，格式：yyyy-MM-dd
       */
      endTime: string
    }>
) => {
  return request({
    url: '/api/gis/project/save',
    method: 'post',
    data: params
  })
}
/**
 * 删除管网工程
 * @param ids
 * @returns
 */
export const DeleteGisProject = (ids: string[]) => {
  return request({
    url: '/api/gis/project/remove',
    method: 'delete',
    data: ids
  })
}
