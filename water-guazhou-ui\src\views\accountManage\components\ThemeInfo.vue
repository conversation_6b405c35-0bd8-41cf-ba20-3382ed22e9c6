<template>
  <Form ref="refForm" :config="FormConfig"></Form>
</template>
<script lang="ts" setup>
import { saveUser } from '@/api/user';
import { useAppStore, useTagsStore, useUserStore } from '@/store';
import { SLConfirm, SLMessage } from '@/utils/Message';
const appStore = useAppStore();
const tagsStore = useTagsStore();
const userStore = useUserStore();
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        type: 'simple',
        desc: '通知相关'
      },
      fields: [
        {
          type: 'switch',
          label: '消息通知:',
          field: 'MessageNotification',
          activeText: '接收',
          inActiveText: '关闭',
          inlinePrompt: false,
          inActiveColor: '#66b1ff',
          aInfo: true,
          onChange: (value) => {
            appStore.SET_MessageNotification(value);
          }
        },
        {
          labelWidth: '200px',
          type: 'switch',
          label: '语音播报(需开启消息通知):',
          field: 'VoiceBroadcast',
          activeText: '开启',
          inActiveText: '关闭',
          inlinePrompt: false,
          inActiveColor: '#66b1ff',
          aInfo: true,
          onChange: (value) => {
            appStore.SET_VoiceBroadcast(value);
          }
        }
      ]
    },
    {
      fieldset: {
        type: 'simple',
        desc: '主题相关'
      },
      fields: [
        {
          type: 'radio',
          label: '菜单风格:',
          field: 'menuType',
          aInfo: true,
          options: [
            { label: '下拉面板', value: 'top' },
            { label: '侧边面板', value: 'left' }
          ],
          onChange: (value) => {
            appStore.TOGGLE_menuType(value);
            // saveTheme()
          }
        },
        {
          type: 'switch',
          label: '标签导航:',
          field: 'showTags',
          activeText: '显示',
          inActiveText: '隐藏',
          aInfo: true,
          width: 60,
          onChange: (value) => {
            tagsStore.TOGGLE_showTags(value);
            // saveTheme()
          }
        }
      ]
    },
    {
      fieldset: {
        type: 'simple',
        desc: '地图相关'
      },
      fields: [
        {
          type: 'radio',
          label: '地图底图:',
          field: 'gisDefaultBaseMap',
          aInfo: true,
          options: [
            { label: '矢量图', value: 'vec_w' },
            { label: '卫星图', value: 'img_w' }
          ],
          onChange: (value) => {
            window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap = value;
            window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi &&
              (window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi =
                value === 'img_w' ? 'cia_w' : 'cva_w');
            // saveTheme()
            refForm.value?.dataForm &&
              (refForm.value.dataForm.gisDefaultPoi =
                window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi);
          }
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = params.gisDefaultBaseMap === 'img_w';
          },
          type: 'radio',
          label: '地图主题:',
          field: 'gisDefaultBaseMapFilterColor',
          aInfo: true,
          options: [
            { label: '象牙白', value: '' },
            { label: '典雅黑', value: 'rgba(255, 255, 255, 0.0)' }
          ],
          onChange: (value) => {
            window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMapFilterColor = value;
            // saveTheme()
          }
        },
        {
          type: 'switch',
          label: '地名标注:',
          field: 'showPoi',
          activeText: '显示',
          inActiveText: '隐藏',
          aInfo: true,
          width: 60,
          onChange: (value) => {
            if (value === false) {
              window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi = '';
            } else {
              window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi =
                value === 'img_w' ? 'cia_w' : 'cva_w';
            }
            refForm.value?.dataForm &&
              (refForm.value.dataForm.gisDefaultPoi =
                window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi);
            // saveTheme()
          }
        },
        {
          type: 'input',
          field: 'gisDefaultPoi',
          aInfo: true,
          hidden: true
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '保存',
              loading: (): boolean => !!FormConfig.submitting,
              click: () => refForm.value?.Submit()
            },
            {
              perm: true,
              text: '同步账号配置',
              type: 'default',
              click: () => resetForm()
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {},
  submit: (params) => {
    SLConfirm('确定保存?', '提示信息')
      .then(async () => {
        try {
          FormConfig.submitting = true;

          const additionalInfo =
            typeof params.additionalInfo === 'string'
              ? JSON.parse(params.additionalInfo)
              : params.additionalInfo;
          const submitParams = {
            ...params,
            additionalInfo: {
              ...(additionalInfo || {})
            }
          };
          await saveUser(submitParams);
          resetForm();
          SLMessage.success('保存成功');
        } catch (error) {
          SLMessage.error('系统错误');
        }
        FormConfig.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});

const resetForm = async () => {
  if (!refForm.value) return;
  try {
    await userStore.GetInfo();
    FormConfig.defaultValue = {
      ...(userStore.user || {}),
      isDark: !!appStore.isDark,
      menuType: appStore.menuType,
      showTags: tagsStore.showTags,
      gisDefaultBaseMap: window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap,
      showPoi: !!window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi,
      gisDefaultBaseMapFilterColor:
        window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMapFilterColor,
      gisDefaultPoi: window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi,
      MessageNotification: false,
      VoiceBroadcast: false
    };
    refForm.value.resetForm();
  } catch (error) {
    SLMessage.error('同步失败');
  }
};
onMounted(() => {
  resetForm();
});
</script>
<style lang="scss" scoped></style>
