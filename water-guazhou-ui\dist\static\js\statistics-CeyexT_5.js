import{z as r}from"./index-r0dFAfgr.js";const a=t=>r({url:"/api/spp/dma/partition/nrwCount",method:"get",params:t}),o=t=>r({url:"/api/spp/dma/partition/totalSupplyCount",method:"get",params:t}),i=()=>r({url:"/api/spp/dma/partition/waterOverview",method:"get"}),p=t=>r({url:"api/spp/dma/partition/referenceLeakSort",method:"get",params:t}),s=t=>r({url:"/api/spp/dma/partition/supplyCount",method:"get",params:t}),n=t=>r({url:"/api/spp/dma/partition/saleCount",method:"get",params:t}),u=t=>r({url:"/api/spp/dma/partition/bigUserRate",method:"get",params:t}),l=t=>r({url:"/api/spp/dma/partition/monitor",method:"get",params:t}),m=t=>r({url:"/api/spp/dma/partition/monthDetailFlow",method:"get",params:t}),d=t=>r({url:"/api/spp/partitionTotalFlow/flowAnalysis",method:"post",data:t}),c=t=>r({url:"/api/spp/partitionTotalFlow/flowAnalysisExport",method:"post",data:t,responseType:"blob"}),P=t=>r({url:"/istar/api/customer/report/waterPlantSupplyReport",method:"get",params:t}),h=t=>r({url:"/istar/api/customer/report/waterPlantSupplyPressureReport",method:"get",params:t});export{c as E,a as G,u as a,s as b,p as c,n as d,o as e,i as f,d as g,m as h,l as i,P as j,h as k};
