<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.deviceType.DevicePurchaseInquiryMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           purchase_detail_id,
                           supplier_id,
                           contact,
                           contact_phone,
                           price,
                           inquiry_time,
                           create_time,
                           intention_supplier,
                           "file",
                           tenant_id<!--@sql
        from device_purchase_inquiry inquiry -->
    </sql>
    <sql id="Full_Info_Query">
        <!--@sql select -->inquiry.id,
                           purchase_detail_id,
                           supplier_id,
                           contact,
                           contact_phone,
                           price,
                           inquiry_time,
                           create_time,
                           intention_supplier,
                           "file",
                           inquiry.tenant_id,
                           item.serial_id,
                           item.main_id
    from device_purchase_inquiry inquiry,
         device_purchase_item item
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.util.imodel.response.device.DevicePurchaseInquiryItemResponse">
        <result column="id" property="id"/>
        <result column="purchase_detail_id" property="purchaseDetailId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="contact" property="contact"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="price" property="price"/>
        <result column="inquiry_time" property="inquiryTime"/>
        <result column="intention_supplier" property="intentionSupplier"/>
        <result column="file" property="file"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Full_Info_Query"/>
        <where>
            inquiry.purchase_detail_id = item.id
            <if test="purchaseDetailId != null and purchaseDetailId != ''">
                and purchase_detail_id = #{purchaseDetailId}
            </if>
            <if test="serialId != null and serialId != ''">
                and item.serial_id = #{serialId}
            </if>
            <if test="deviceName != null and deviceName != ''">
                <!--@formatter:off-->
and (select name like '%' || #{deviceName} || '%' from m_device where serial_id = item.serial_id and tenant_id = inquiry.tenant_id)
                <!--@formatter:on-->
            </if>
            <if test="deviceModel != null and deviceModel != ''">
                and device_is_model_like(item.serial_id, #{deviceModel})
            </if>
            <if test="purchaseCode != null and purchaseCode != ''">
                and device_purchase_is_purchase_code_like(item.main_id, #{purchaseCode})
            </if>
            <if test="purchaseTitle != null and purchaseTitle != ''">
                and device_purchase_is_purchase_title_like(item.main_id, #{purchaseTitle})
            </if>
            <if test="(userId == null or userId == '') and (userDepartmentId != null and userDepartmentId != '')">
                and device_purchase_is_user_at_department(item.main_id, #{userDepartmentId})
            </if>
            <if test="userId != null and userId != ''">
                and device_purchase_is_user(item.main_id, #{userId})
            </if>
            and inquiry.tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update device_purchase_inquiry
        <set>
            <if test="purchaseDetailId != null">
                purchase_detail_id = #{purchaseDetailId},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="contact != null">
                contact = #{contact},
            </if>
            <if test="contactPhone != null">
                contact_phone = #{contactPhone},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="intentionSupplier != null">
                intention_supplier = #{intentionSupplier},
            </if>
            <if test="file != null">
                file = #{file},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO device_purchase_inquiry(id,
                                            purchase_detail_id,
                                            supplier_id,
                                            contact,
                                            contact_phone,
                                            price,
                                            intention_supplier,
                                            inquiry_time,
                                            file,
                                            create_time,
                                            tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.purchaseDetailId},
             #{element.supplierId},
             #{element.contact},
             #{element.contactPhone},
             #{element.price},
             #{element.intentionSupplier},
             #{element.inquiryTime},
             #{element.file},
             #{element.createTime},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="updateAll">
        update device_purchase_inquiry
        <set>
            supplier_id        = valueTable.supplierId,
            contact            = valueTable.contact,
            contact_phone      = valueTable.contactPhone,
            price              = valueTable.price,
            intention_supplier = valueTable.intentionSupplier,
            file               = valueTable.file,
            inquiry_time       = valueTable.inquiryTime
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.purchaseDetailId},
             #{element.supplierId},
             #{element.contact},
             #{element.contactPhone},
             #{element.price},
             #{element.intentionSupplier},
             #{element.file},
             #{element.inquiryTime}::timestamp)
        </foreach>
        ) as valueTable(id, purchaseDetailId, supplierId, contact, contactPhone, price, intentionSupplier, file,
                        inquiryTime)
        where device_purchase_inquiry.id = valueTable.id
    </update>

    <delete id="deleteByPurchaseItemMainId">
        delete
        from device_purchase_inquiry inq
            using device_purchase_item item
        where inq.purchase_detail_id = item.id
          and item.main_id = #{mainId}
        <if test="idList != null and idList.size() != 0">
            and inq.purchase_detail_id not in
            <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteBatchByPurchaseItemId">
        delete
        from device_purchase_inquiry
        where purchase_detail_id in (
        <foreach collection="list" item="element" separator=",">
            #{element}
        </foreach>
        )
    </delete>

    <delete id="deleteByPurchaseItemId">
        delete
        from device_purchase_inquiry
        where purchase_detail_id = #{id}
    </delete>

    <update id="inquiry">
        update device_purchase_inquiry
        <set>
            <if test="purchaseDetailId != null">
                purchase_detail_id = #{purchaseDetailId},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="contact != null">
                contact = #{contact},
            </if>
            <if test="contactPhone != null">
                contact_phone = #{contactPhone},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="intentionSupplier != null">
                intention_supplier = #{intentionSupplier},
            </if>
            <if test="file != null">
                file = #{file},
            </if>
            inquiry_time = now(),
        </set>
        where id = #{id}
    </update>

    <delete id="removeAllByMainId">
        delete
        from device_purchase_inquiry
        where purchase_detail_id = #{mainId}
    </delete>

    <delete id="deleteByPurchaseItemMainIdOnIdNotIn">
        delete
        from device_purchase_inquiry inquiry
            using device_purchase_item item
        where inquiry.purchase_detail_id = item.id
          and inquiry.purchase_detail_id = #{mainId}
        <if test="idList != null and idList.size() != 0">
            and inquiry.id not in
            <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>
</mapper>