<template>
  <div class="total-statistic">
    <div v-for="(item, i) in state.items" :key="i" class="statistic-item">
      <div class="block">
        {{ item.title }}
      </div>
      <div class="count">
        <span class="value">{{ item.value }}</span>
        <span class="unit">{{ item.unit }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getWaterSupplyTree } from '@/api/company_org';
import { getCustInfoList } from '@/api/operatingCharges/fileManagement';
import { GetviewCount, nrwByName } from '@/api/shuiwureports/shuichangzonglan';
import { toCommaNumber, transNumberUnit } from '@/utils/GlobalHelper';

const props = defineProps<{
  custom?: boolean;
  waterSupplyNum?: any;
  waterSupply?: any;
  data?: { title: string; value: string; unit: string }[];
}>();
const factory = ref<any>({
  梓莲达: '46b4960678843b025563c6fa86c5f21b',
  金峰水厂: 'b7507185378b2e2bde66053d0abb4c27',
  北部水厂: 'e7c762b34e63ed75ed708d52ae849120',
  西部水厂: '96c0dd4cb78d6bd654423b4f427f4ec4'
});
const curItem = ref<any>();
const state = reactive<{
  items: { title: string; value: string; unit: string }[];
}>({
  items: props.data || [
    { title: '今日供水量', value: toCommaNumber(0), unit: 'm³' },
    // { title: '今日耗电量', value: '0.0', unit: 'kWh' },
    { title: '产销率', value: '0.00', unit: '%' },
    { title: '服务用户数', value: toCommaNumber(0), unit: '人' }
  ]
});
const refreshData = () => {
  console.log(props.waterSupply);
  getWaterSupplyTree(1).then((res) => {
    getCustInfoList({ page: 1, size: 1, orgId: factory.value[props.waterSupply.name] }).then((res) => {
      state.items[2].value = toCommaNumber(res.data?.data.total ?? 0);
    });
  });
  // GetviewCount({ projectId: '' }).then(res => {
  //   const todayTotalFlow = transNumberUnit(res.data.todayTotalFlow ?? 0)
  //   const todayEnergyIn = transNumberUnit(res.data.todayEnergyIn ?? 0)
  //   state.items[0].value = toCommaNumber(todayTotalFlow.value)
  //   state.items[0].unit = todayTotalFlow.unit + 'm³'
  //   state.items[1].value = toCommaNumber(todayEnergyIn.value)
  //   state.items[1].unit = todayEnergyIn.unit + 'kWh'
  // })
  nrwByName({ name: props.waterSupply.name }).then((res) => {
    state.items[1].value = res.data.data;
    state.items[1].unit = '%';
  });
};

watch(
  () => props.waterSupplyNum,
  (val) => {
    state.items[0].value = val.todayWaterSupply;
  }
);
watch(
  () => props.waterSupply,
  (val) => {
    refreshData();
  }
);
</script>
<style lang="scss" scoped>
.total-statistic {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: nowrap;
  padding: 0 40px;
}
.statistic-item {
  .block {
    width: 127px;
    height: 33px;
    display: flex;
    justify-content: center;
    line-height: 28px;
    margin-bottom: 12px;
    background: url('../../imgs/polygon.png') 0 0 /100% 100% no-repeat;
  }
  .count {
    text-align: center;
    .value {
      color: rgba(255, 210, 50, 1);
      font-size: 32px;
      font-family: font-lcd;
      margin-right: 8px;
      @supports (-webkit-background-clip: text) or (background-clip: text) {
        background: linear-gradient(to right, rgba(255, 210, 50, 1), rgba(255, 247, 50, 1));
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }
    }
    .unit {
      font-size: 12px;
    }
  }
}
</style>
