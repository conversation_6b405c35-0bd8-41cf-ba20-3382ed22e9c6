package org.thingsboard.server.controller.smartPipe;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.DTO.PartitionTreeDTO;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition;
import org.thingsboard.server.dao.smartPipe.PartitionService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 智慧管网-DMA分区
 *
 * dma分区
 */
@RestController
@RequestMapping("api/spp/dma/partition")
public class PartitionController extends BaseController {
    @Autowired
    private PartitionService dmaPartitionService;

    @GetMapping("list")
    public List<PartitionTreeDTO> getList(@RequestParam Map<String, Object> params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        params.put("tenantId", tenantId);
        return dmaPartitionService.getList(params);
    }

    @GetMapping("partitionDeviceTree")
    public IstarResponse getPartitionDeviceTree(@RequestParam String type) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getPartitionDeviceTree(type, tenantId));
    }

    @GetMapping("all")
    public List<Partition> getAll(@RequestParam Map<String, Object> params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        params.put("tenantId", tenantId);
        return dmaPartitionService.getAll(params);
    }

    @GetMapping("{id}")
    public Partition getById(@PathVariable String id) {
        return dmaPartitionService.getById(id);
    }

    @PostMapping
    public Partition save(@RequestBody Partition partition) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partition.setTenantId(tenantId);
        return dmaPartitionService.save(partition);
    }

    @DeleteMapping
    public void delete(@RequestBody List<String> ids) throws ThingsboardException {
        dmaPartitionService.delete(ids);
    }

    @GetMapping("child/{pid}")
    public List getAllChildByPid(@PathVariable String pid) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return dmaPartitionService.getChildPositionByPid(pid, tenantId);
    }

    @GetMapping("parent/{id}")
    public Object getParentById(@PathVariable String id) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return dmaPartitionService.getParentById(id, tenantId);
    }

    @GetMapping("parentAndSameRange/{partitionId}")
    public List getParentAndLayerRange(@PathVariable String partitionId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return dmaPartitionService.getParentAndLayerRange(partitionId, tenantId);
    }

    @GetMapping("totalWater")
    public IstarResponse getTotalWater(String partitionId, String direction, String month) {
        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }
        if (StringUtils.isBlank(direction)) {
            direction = "in";
        }
        return IstarResponse.ok(dmaPartitionService.getTotalWater(partitionId, direction, month));
    }

    /**
     * 指标总览
     */
    @GetMapping("waterOverview")
    public IstarResponse getWaterOverview() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getWaterOverview(tenantId));
    }

    /**
     * 漏损概览(黄平：昨天，本月，本年)
     */
    @GetMapping("waterOverviewHP")
    public IstarResponse getWaterOverviewHP() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getWaterOverviewHP(tenantId));
    }

    /**
     * 漏损概览(黄平：昨天，本月，本年)
     */
    @GetMapping("nightMinFLow")
    public IstarResponse getNightMinFLow() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getNightMinFLow(tenantId));
    }

    @GetMapping("numCount")
    public IstarResponse getNumCount() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getNumCount(tenantId));
    }

    /**
     * 指标总览-产销差统计
     */
    @GetMapping("referenceLeakSortHP")
    public IstarResponse getReferenceLeakSortHP(String month) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getReferenceLeakSortHP(month, tenantId));
    }

    /**
     * 指标总览-产销差统计
     */
    @GetMapping("overviewHP")
    public IstarResponse getOverviewHP(String month) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getOverviewHP(month, tenantId));
    }


    /**
     * 分区总览
     */
    @GetMapping("overview")
    public IstarResponse getOverview(String partitionId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getOverview(partitionId, tenantId));
    }

    /**
     * 指标总览-总供水统计
     */
    @GetMapping("totalSupplyCount")
    public IstarResponse getTotalSupplyCount(String type) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getTotalSupplyCount(type, tenantId));
    }

    /**
     * 指标总览-产销差统计
     */
    @GetMapping("nrwCount")
    public IstarResponse getNrwCount(String type) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getNrwCount(type, tenantId));
    }

    /**
     * 指标总览-产销差统计
     */
    @GetMapping("referenceLeakSort")
    public IstarResponse getReferenceLeakSort(String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getReferenceLeakSort(name, tenantId));
    }

    /**
     * 指标总览-供水量分析
     */
    @GetMapping("supplyCount")
    public IstarResponse getSupplyCount(String type, Integer grade) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getSupplyCount(type, grade, "1", tenantId));
    }

    /**
     * 指标总览-售水量分析
     */
    @GetMapping("saleCount")
    public IstarResponse getSaleCount(String type, Integer grade) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String date = format.format(new Date());
        return IstarResponse.ok(dmaPartitionService.getSaleCount(type, grade, tenantId, date));
    }

    /**
     * 指标总览-大用户占比分析
     */
    @GetMapping("bigUserRate")
    public IstarResponse getBigUserRate(String type, Integer grade) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getSupplyCount(type, grade, "3", tenantId));
    }

    /**
     * 分区监控
     */
    @GetMapping("monitor")
    public IstarResponse monitor(String partitionId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.monitor(partitionId, tenantId));
    }

    /**
     * 计量分区上月详细流量
     */
    @GetMapping("monthDetailFlow")
    public IstarResponse getMonthDetailFlow(String partitionId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getMonthDetailFlow(partitionId, tenantId));
    }

    /**
     * 计量分区上月详细流量
     */
    @GetMapping("nightMinFlow")
    public IstarResponse getNightMinFlow(String partitionId, Long start, Long end, Double minFlow, Double maxFlow) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getNightMinFlow(partitionId, start, end, minFlow, maxFlow, tenantId));
    }

    /**
     * 改状态
     */
    @PostMapping("changeStatus")
    public IstarResponse changeStatus(@RequestBody Partition partition) {
        if (StringUtils.isBlank(partition.getId())) {
            return IstarResponse.error("请选中分区");
        }
        if (StringUtils.isBlank(partition.getStatus())) {
            return IstarResponse.error("请设置分区状态");
        }
        dmaPartitionService.changeStatus(partition);
        return IstarResponse.ok("操作成功");
    }

    /**
     * 盐亭定制产销差
     */
    @GetMapping("nrwByName")
    public IstarResponse getNRWByName(String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getNRWByName(name, tenantId));
    }

    /**
     * 盐亭定制年度产销差
     */
    @GetMapping("nrwYearByName")
    public IstarResponse getNrwYearByName(String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.getNrwYearByName(name, tenantId));
    }



    /**
     * 盐亭定制-取水量
     */
    @GetMapping("inWater")
    public IstarResponse inWater(String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(dmaPartitionService.inWater(name, tenantId));
    }
}
