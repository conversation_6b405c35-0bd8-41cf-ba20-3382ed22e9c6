package org.thingsboard.server.dao.logicalFlow;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.logicalFlow.BO.LogicalFlowNodeTreeVO;
import org.thingsboard.server.dao.model.sql.LogicalFlow;
import org.thingsboard.server.dao.model.sql.LogicalFlowNode;

import java.util.List;

public interface LogicalFlowService {
    LogicalFlow save(LogicalFlow logicalFlow);

    LogicalFlow edit(LogicalFlow logicalFlow);

    LogicalFlow findById(String id);

    List<LogicalFlow> findList(String type);

    void deleteById(String id);

    List<LogicalFlow> findList(String type, String projectId);

    LogicalFlow save(String type, String id, LogicalFlow logicalFlow);

    LogicalFlowNode findLogicalFlowNodeById(String id);

    LogicalFlowNode deleteLogicalFlowNodeById(String id);

    List<LogicalFlowNode> findLogicalFlowNodeByLogicalFlowId(String logicalFlowId);

    LogicalFlowNode saveLogicalFlowNode(LogicalFlowNode logicalFlowNode) throws ThingsboardException;

    List<LogicalFlow> findListByParentId(String parentId);

    LogicalFlowNodeTreeVO findTreeByLogicalFlowId(String logicalFlowId);

    void forceStop(String id);

    void importLogicalFlow(JSONObject logicalFlowJson, User currentUser, String type, String id) throws ThingsboardException;

    List<LogicalFlow> findAll(String local, String projectId);
}
