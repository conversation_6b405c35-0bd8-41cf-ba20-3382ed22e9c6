<!-- 工程管理-总验期-总归档 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
    <SLDrawer ref="refDetail" :config="detailConfig">
      <detail :config="data.selected" :show="16"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import {
  getProjectArchiveList,
  postProjectArchive,
  getProjectArchiveExport
} from '@/api/engineeringManagement/inspectionPeriod';
import { formatDate } from '@/utils/DateFormatter';
import detail from '../../components/detail.vue';

const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();
const refDetail = ref<ISLDrawerIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '项目编号', field: 'projectCode', type: 'input' },
    { label: '项目名称', field: 'projectName', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'default',
          perm: true,
          text: '导出',
          icon: ICONS.DOWNLOAD,
          click: () => {
            getProjectArchiveExport().then((res) => {
              const url = window.URL.createObjectURL(res.data);
              const link = document.createElement('a');
              link.style.display = 'none';
              link.href = url;
              link.setAttribute('download', `总归档.xlsx`);
              document.body.appendChild(link);
              link.click();
            });
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '项目编号', prop: 'projectCode' },
    { label: '项目名称', prop: 'projectName' },
    {
      label: '启动时间',
      prop: 'projectStartTime',
      formatter: (row) => formatDate(row.projectStartTime, 'YYYY-MM-DD')
    },
    {
      label: '总归档时间',
      prop: 'archiveTime',
      formatter: (row) => formatDate(row.archiveTime, 'YYYY-MM-DD')
    },
    {
      label: '工作状态',
      prop: 'status',
      tag: true,
      tagColor: (row): string =>
        row.createTime === null ? '#409EFF' : '#67C23A',
      formatter: (row) => (row.createTime === null ? '处理中' : '已完成')
    }
  ],
  operationWidth: '300px',
  operations: [
    {
      disabled: (val) => !val.createTime,
      isTextBtn: false,
      text: '详情',
      perm: true,
      click: (row) => {
        data.selected = row;
        refDetail.value?.openDrawer();
      }
    },
    {
      disabled: (val) => !(val.createTime === null),
      isTextBtn: false,
      type: 'primary',
      text: '添加归档',
      perm: true,
      click: (row) => {
        clickAdd(row);
      }
    },
    {
      disabled: (val) => val.createTime === null,
      isTextBtn: false,
      type: 'success',
      text: '编辑归档',
      perm: true,
      click: (row) => clickEdit(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加归档',
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    params.pipLengthDesign = JSON.stringify(params.pipLengthDesign);
    postProjectArchive(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
          refreshData();
        } else {
          ElMessage.warning(text + '失败');
        }
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '项目编号',
          field: 'projectCode'
        },
        {
          xs: 12,
          type: 'input',
          label: '项目名称',
          field: 'projectName'
        },
        {
          xs: 12,
          type: 'date',
          label: '总归档时间',
          field: 'archiveTime',
          format: 'x'
        },
        {
          type: 'textarea',
          label: '总归档说明',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  group: [],
  width: '80%',
  modalClass: 'lightColor',
  cancel: false
});

const clickAdd = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '添加归档';
  addOrUpdateConfig.defaultValue = { ...(row || {}) };
  refForm.value?.openDialog();
};

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑归档';
  addOrUpdateConfig.defaultValue = { ...(row || {}) };
  refForm.value?.openDialog();
};

const data = reactive({
  selected: {}
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  };
  getProjectArchiveList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
});
</script>
