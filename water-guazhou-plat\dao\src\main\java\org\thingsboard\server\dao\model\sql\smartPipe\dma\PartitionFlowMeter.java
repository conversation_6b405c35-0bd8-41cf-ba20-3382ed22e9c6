package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 *
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_PARTITION_FLOW_METER_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PartitionFlowMeter {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_PARTITION_ID)
    private String partitionId;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_TYPE)
    private String type;

    @TableField(exist = false)
    private String typeName;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_CODE)
    private String code;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_POSITION)
    private String position;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_METER_TYPE)
    private String meterType;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_BRAND)
    private String brand;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_CALIBER)
    private String caliber;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_PIPE)
    private String pipe;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_IS_REMOTE)
    private String isRemote;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_YEAR)
    private String year;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_REMARK)
    private String remark;

    @TableField(ModelConstants.PIPE_PARTITION_FLOW_METER_IMG)
    private String img;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
