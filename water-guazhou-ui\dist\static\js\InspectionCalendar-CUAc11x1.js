import{d as re,c as C,r as le,o as ce,l as o,g as Y,n as M,p as l,q as s,F as r,i as f,dj as q,G as h,dk as O,bh as p,an as E,aw as H,bw as j,h as x,aB as ie,aJ as de,b$ as ue,bo as me,b as g,bB as z,J as pe,cE as fe,cu as _e,dl as Ye,cJ as he,ct as ye,bV as ge,dm as ve,dn as De,N as Me,c6 as ke,O as be,P as we,br as Te,C as Ce}from"./index-r0dFAfgr.js";/* empty css                    */import{a as A}from"./plan-BLf3nu6_.js";const Se={class:"inspection-calendar"},Ee={class:"calendar-header"},ze={class:"header-left"},$e={class:"header-title"},xe={class:"main-content"},Ne={class:"calendar-container"},Pe={key:0,class:"loading-overlay"},Le={class:"el-calendar-header-custom"},Ue={class:"el-calendar__title"},Ve=["onClick","data-date"],Be={class:"day-header"},Ie={class:"day-content"},qe={class:"task-title"},Oe={class:"task-assignee"},He={key:0,class:"more-tasks"},je={key:1,class:"no-tasks"},Ae={key:0,class:"day-tasks-panel"},Fe={class:"day-tasks-header"},Ge={class:"day-tasks-list"},Je={class:"task-name-cell"},Re={key:0,class:"pagination-container"},Ke=re({__name:"InspectionCalendar",setup(Qe){const y=C(new Date),k=C(),i=C(null),N=C([]),P=C([]),v=C(!1),d=le({currentPage:1,pageSize:10,total:0}),F=async t=>{v.value=!0;try{const a=(t?o(t):o()).format("YYYY-MM-DD"),u=await A({beginTime:a,endTime:a,page:1,size:100});if(u.data.code===200){const _=u.data.data.data||[];P.value=_,(!t||o(t).isSame(o(),"day"))&&(i.value=a,d.currentPage=1,await S())}else g.error(u.data.message||"获取任务列表失败")}catch(e){console.error("获取任务列表失败:",e),g.error("系统错误")}finally{v.value=!1}},b=t=>{const e=o(t).format("YYYY-MM-DD");return P.value.filter(c=>{const u=o(c.beginTime).format("YYYY-MM-DD"),_=o(c.endTime).format("YYYY-MM-DD");return u===e||_===e})},G=t=>`task-status-${$(t)}`,$=t=>{const e=o(),a=o(t.beginTime),c=o(t.endTime);return e.isBefore(a)?"pending":e.isAfter(c)?t.status==="COMPLETED"?"completed":"overdue":"in-progress"},J=t=>{switch($(t)){case"pending":return"未开始";case"in-progress":return"进行中";case"completed":return"已完成";case"overdue":return"已逾期";default:return"未知状态"}},R=t=>{switch($(t)){case"pending":return"info";case"in-progress":return"success";case"completed":return"primary";case"overdue":return"danger";default:return"info"}},w=async t=>{try{let e="";if(typeof t=="string")if(t.match(/^\d{1,2}$/)){const a=o(y.value).format("YYYY-MM");e=o(`${a}-${t.padStart(2,"0")}`).format("YYYY-MM-DD")}else e=o(t).format("YYYY-MM-DD");else e=o(new Date).format("YYYY-MM-DD");i.value=e,d.currentPage=1,await S(),await z(),setTimeout(()=>{document.querySelectorAll(".calendar-day").forEach(u=>{u.classList.remove("is-selected")});const c=document.querySelector(`.calendar-day[data-date="${e}"]`);c&&(c.classList.add("is-selected"),c.scrollIntoView({behavior:"smooth",block:"center"}))},100)}catch(e){console.error("显示日期任务时出错:",e),g.error("显示任务列表时出错"),v.value=!1}},L=t=>{try{return o(t).format("YYYY年MM月")}catch{return o(new Date).format("YYYY年MM月")}},K=()=>{if(!i.value)return"";try{return o(i.value).format("YYYY年MM月DD日")}catch{return o(new Date).format("YYYY年MM月DD日")}},U=async()=>{try{const t=o(y.value).subtract(1,"month"),e=t.startOf("month").format("YYYY-MM-DD");y.value=t.toDate(),await w(e),k.value&&k.value.$forceUpdate(),await z(),setTimeout(()=>{const a=document.querySelector(`.calendar-day[data-date="${e}"]`);a&&a.scrollIntoView({behavior:"smooth",block:"center"})},100)}catch(t){console.error("切换到上个月时出错:",t),g.error("切换月份失败")}},V=async()=>{try{const t=o(y.value).add(1,"month"),e=t.startOf("month").format("YYYY-MM-DD");y.value=t.toDate(),await w(e),k.value&&k.value.$forceUpdate(),await z(),setTimeout(()=>{const a=document.querySelector(`.calendar-day[data-date="${e}"]`);a&&a.scrollIntoView({behavior:"smooth",block:"center"})},100)}catch(t){console.error("切换到下个月时出错:",t),g.error("切换月份失败")}},B=async()=>{try{const t=new Date,e=o(t).format("YYYY-MM-DD");y.value=t,await w(e),k.value&&k.value.$forceUpdate(),await z(),setTimeout(()=>{const a=document.querySelector(`.calendar-day[data-date="${e}"]`);a&&a.scrollIntoView({behavior:"smooth",block:"center"})},100)}catch(t){console.error("切换到今天时出错:",t),g.error("切换日期失败")}},Q=t=>{d.currentPage=t,S()},W=t=>{d.pageSize=t,d.currentPage=1,S()},S=async()=>{if(i.value){v.value=!0;try{const t={beginTime:i.value,endTime:i.value,page:d.currentPage,size:d.pageSize},e=await A(t);if(e.data.code===200){let a=e.data.data.data||[];a=a.filter(c=>{const u=o(c.beginTime).format("YYYY-MM-DD"),_=o(c.endTime).format("YYYY-MM-DD");return u===i.value||_===i.value}),N.value=a,d.total=a.length}else g.error(e.data.message||"获取任务列表失败")}catch(t){console.error("获取任务列表失败:",t),g.error("系统错误")}finally{v.value=!1}}},X=async()=>{if(!i.value){g.warning("请先选择日期");return}d.currentPage=1,await S()};return ce(()=>{const t=o().format("YYYY-MM-DD");F(t),document.addEventListener("click",e=>{var c,u;const a=e.target;if(a.classList.contains("el-calendar-day")||a.parentElement&&a.parentElement.classList.contains("el-calendar-day")){let _="";a.classList.contains("el-calendar-day")?_=((c=a.textContent)==null?void 0:c.trim())||"":a.parentElement&&a.parentElement.classList.contains("el-calendar-day")&&(_=((u=a.parentElement.textContent)==null?void 0:u.trim())||"");const T=`${o(y.value).format("YYYY-MM")}-${_.padStart(2,"0")}`;w(T)}})}),(t,e)=>{const a=pe,c=fe,u=_e,_=Ye,I=he,T=ye,Z=ge,ee=ve,te=De,m=Me,ae=ke,ne=be,se=we,oe=Te;return Y(),M("div",Se,[l("div",Ee,[l("div",ze,[s(u,null,{default:r(()=>[s(a,{onClick:U,icon:f(q),type:"primary",plain:""},{default:r(()=>e[3]||(e[3]=[h("上个月")])),_:1},8,["icon"]),s(a,{onClick:B,type:"primary"},{default:r(()=>e[4]||(e[4]=[h("今天")])),_:1}),s(a,{onClick:V,type:"primary",plain:""},{default:r(()=>[e[5]||(e[5]=h("下个月")),s(c,{class:"el-icon--right"},{default:r(()=>[s(f(O))]),_:1})]),_:1})]),_:1})]),l("div",$e,[l("h2",null,p(L(y.value))+" 巡检任务日历",1)])]),l("div",xe,[l("div",Ne,[v.value?(Y(),M("div",Pe,[s(_,{background:"rgba(255, 255, 255, 0.8)",text:"加载中..."})])):E("",!0),s(te,{ref_key:"calendarRef",ref:k,modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=n=>y.value=n)},{header:r(({date:n})=>[l("div",Le,[s(u,null,{default:r(()=>[s(a,{size:"small",onClick:U,icon:f(q)},null,8,["icon"]),s(a,{size:"small",onClick:B},{default:r(()=>e[6]||(e[6]=[h("今天")])),_:1}),s(a,{size:"small",onClick:V,icon:f(O)},null,8,["icon"])]),_:1}),l("span",Ue,p(L(n)),1)])]),dateCell:r(({data:n})=>[l("div",{class:H(["calendar-day",{"has-tasks":b(n.day).length>0,"is-loading":v.value&&i.value&&f(o)(i.value).format("YYYY-MM-DD")===f(o)(n.day).format("YYYY-MM-DD"),"is-selected":i.value&&f(o)(i.value).format("YYYY-MM-DD")===f(o)(n.day).format("YYYY-MM-DD")}]),onClick:j(D=>w(n.day),["stop"]),"data-date":f(o)(n.day).format("YYYY-MM-DD")},[l("div",Be,[l("span",null,p(n.day.split("-").slice(2).join("")),1),b(n.day).length>0?(Y(),x(I,{key:0,value:b(n.day).length,type:"primary"},null,8,["value"])):E("",!0)]),l("div",Ie,[b(n.day).length>0?(Y(),x(Z,{key:0,"max-height":"150px"},{default:r(()=>[(Y(!0),M(ie,null,de(b(n.day).slice(0,2),D=>(Y(),M("div",{key:D.id,class:H(["task-item",G(D)])},[s(T,{content:`${D.name} (${D.receiveUserName})`,placement:"top","show-after":300},{default:r(()=>[l("div",qe,p(D.name),1)]),_:2},1032,["content"]),l("div",Oe,p(D.receiveUserName),1)],2))),128)),b(n.day).length>2?(Y(),M("div",He,[s(a,{type:"primary",size:"small",onClick:j(D=>w(n.day),["stop"])},{default:r(()=>[h(" 查看更多 ("+p(b(n.day).length-2)+") ",1)]),_:2},1032,["onClick"])])):E("",!0)]),_:2},1024)):(Y(),M("div",je,[s(ee,{"image-size":40,description:"无任务"})]))])],10,Ve)]),_:1},8,["modelValue"])]),i.value?(Y(),M("div",Ae,[l("div",Fe,[l("h3",null,p(K())+" 任务列表",1),l("div",null,[s(a,{type:"success",size:"small",onClick:X,style:{"margin-right":"10px"}},{default:r(()=>[s(c,null,{default:r(()=>[s(f(ue))]),_:1}),e[7]||(e[7]=h(" 刷新 "))]),_:1})])]),l("div",Ge,[me((Y(),x(ne,{data:N.value,style:{width:"100%"},border:"",stripe:""},{default:r(()=>[s(m,{prop:"name",label:"任务名称","min-width":"120"},{default:r(({row:n})=>[s(T,{content:n.name,placement:"top","show-after":300},{default:r(()=>[l("span",Je,p(n.name),1)]),_:2},1032,["content"])]),_:1}),s(m,{prop:"code",label:"任务编号",width:"120"}),s(m,{prop:"planCircleName",label:"计划周期",width:"100"},{default:r(({row:n})=>[s(T,{content:n.planCircleName||"未指定",placement:"top","show-after":300},{default:r(()=>[l("span",null,p(n.planCircleName||"未指定"),1)]),_:2},1032,["content"])]),_:1}),s(m,{prop:"districtAreaName",label:"区域",width:"120"}),s(m,{prop:"moveType",label:"巡检方式",width:"100"}),s(m,{prop:"receiveUserName",label:"接收人员",width:"100"}),s(m,{prop:"receiveUserDepartmentName",label:"所属部门",width:"120"},{default:r(({row:n})=>[h(p(n.receiveUserDepartmentName||"未指定"),1)]),_:1}),s(m,{prop:"collaborateUserName",label:"协作人员",width:"100"},{default:r(({row:n})=>[h(p(n.collaborateUserName||"无"),1)]),_:1}),s(m,{label:"开始时间",width:"160"},{default:r(({row:n})=>[h(p(f(o)(n.beginTime).format("YYYY-MM-DD HH:mm")),1)]),_:1}),s(m,{label:"结束时间",width:"160"},{default:r(({row:n})=>[h(p(f(o)(n.endTime).format("YYYY-MM-DD HH:mm")),1)]),_:1}),s(m,{prop:"keyPointCount",label:"关键点数",width:"90"}),s(m,{prop:"deviceCount",label:"设备数",width:"90"}),s(m,{prop:"presentState",label:"到位状态",width:"100"}),s(m,{label:"状态",width:"100"},{default:r(({row:n})=>[s(ae,{type:R(n),size:"small"},{default:r(()=>[h(p(J(n)),1)]),_:2},1032,["type"])]),_:1}),s(m,{prop:"remark",label:"备注","min-width":"120"},{default:r(({row:n})=>[s(T,{content:n.remark||"无",placement:"top","show-after":300},{default:r(()=>[l("span",null,p(n.remark||"无"),1)]),_:2},1032,["content"])]),_:1})]),_:1},8,["data"])),[[oe,v.value]]),d.total>0?(Y(),M("div",Re,[s(se,{"current-page":d.currentPage,"onUpdate:currentPage":e[1]||(e[1]=n=>d.currentPage=n),"page-size":d.pageSize,"onUpdate:pageSize":e[2]||(e[2]=n=>d.pageSize=n),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:d.total,onSizeChange:W,onCurrentChange:Q,background:""},null,8,["current-page","page-size","total"])])):E("",!0)])])):E("",!0)])])}}}),et=Ce(Ke,[["__scopeId","data-v-c84d61b7"]]);export{et as default};
