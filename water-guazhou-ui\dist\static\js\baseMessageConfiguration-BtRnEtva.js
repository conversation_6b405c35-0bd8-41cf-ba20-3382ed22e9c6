import{_ as T}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as q}from"./CardTable-rdWOL4_6.js";import{_ as S}from"./CardSearch-CB_HNR-Q.js";import{z as c,C as k,c as h,r as g,b as s,S as v,o as L,g as M,n as B,q as d}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function P(i){return c({url:"/api/base/message/configuration/list",method:"get",params:i})}function V(i){return c({url:"/api/base/message/configuration/getDetail",method:"get",params:{id:i}})}function _(i){return c({url:"/api/base/message/configuration/add",method:"post",data:i})}function C(i){return c({url:"/api/base/message/configuration/edit",method:"post",data:i})}function F(i){return c({url:"/api/base/message/configuration/deleteIds",method:"delete",data:i})}const I={class:"wrapper"},N={__name:"baseMessageConfiguration",setup(i){const f=h(),u=h(),x=g({labelWidth:"100px",filters:[{type:"input",label:"配置名称",field:"configName",placeholder:"请输入配置名称",onChange:()=>n()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>n()},{perm:!0,type:"primary",text:"新增",click:()=>b()},{perm:!0,type:"danger",text:"批量删除",click:()=>y()}]}],defaultParams:{}}),o=g({columns:[{label:"配置名称",prop:"configName"},{label:"消息类型",prop:"messageType"},{label:"目标平台",prop:"platform"},{label:"启用状态",prop:"status"},{label:"消息标题模板",prop:"titleTemplate"},{label:"消息内容模板",prop:"contentTemplate"},{label:"内容编码格式",prop:"encoding"},{label:"推送失败重试次数",prop:"retryCount"},{label:"重试间隔",prop:"retryInterval"},{label:"每秒推送速率限制",prop:"rateLimit"},{label:"是否异步推送",prop:"isAsync"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>w(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>b(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>y(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{o.pagination.page=e,n()},handleSize:e=>{o.pagination.limit=e,n()}},handleSelectChange:e=>{o.selectList=e||[]}}),a=g({title:"新增消息配置",group:[{fields:[{type:"input",label:"配置名称",field:"configName",rules:[{required:!0,message:"请输入配置名称"}]},{type:"input",label:"消息类型",field:"messageType",rules:[{required:!0,message:"请输入消息类型"}]},{type:"input",label:"目标平台",field:"platform",rules:[{required:!0,message:"请输入目标平台"}]},{type:"input",label:"启用状态",field:"status",rules:[{required:!0,message:"请输入启用状态"}]},{type:"input",label:"消息标题模板",field:"titleTemplate",rules:[{required:!0,message:"请输入消息标题模板"}]},{type:"input",label:"消息内容模板",field:"contentTemplate",rules:[{required:!0,message:"请输入消息内容模板"}]},{type:"input",label:"内容编码格式",field:"encoding",rules:[{required:!0,message:"请输入内容编码格式"}]},{type:"input",label:"推送失败重试次数",field:"retryCount",rules:[{required:!0,message:"请输入推送失败重试次数"}]},{type:"input",label:"重试间隔",field:"retryInterval",rules:[{required:!0,message:"请输入重试间隔"}]},{type:"input",label:"每秒推送速率限制",field:"rateLimit",rules:[{required:!0,message:"请输入每秒推送速率限制"}]},{type:"input",label:"是否异步推送",field:"isAsync",rules:[{required:!0,message:"请输入是否异步推送"}]},{type:"password",label:"密码",field:"password",rules:[{required:!1,message:"请输入密码"}],showPassword:!0}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var t;try{e.id?(await C(e),s.success("修改成功")):(await _(e),s.success("新增成功")),(t=u.value)==null||t.closeDialog(),n()}catch{s.error("操作失败")}}}),m=()=>{a.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),a.showSubmit=!0,a.showCancel=!0,a.cancelText="取消",a.submitText="确定",a.submit=async e=>{var t;try{e.id?(await C(e),s.success("修改成功")):(await _(e),s.success("新增成功")),(t=u.value)==null||t.closeDialog(),n()}catch{s.error("操作失败")}}},w=async e=>{var t,r;try{const l=await V(e.id),p=((t=l.data)==null?void 0:t.data)||l;m(),a.title="消息配置详情",a.defaultValue={...p},a.group[0].fields.forEach(D=>{D.disabled=!0}),a.showSubmit=!1,a.cancelText="关闭",(r=u.value)==null||r.openDialog()}catch{s.error("获取详情失败")}},b=e=>{var t;m(),e?(a.title="编辑消息配置",a.defaultValue={...e}):(a.title="新增消息配置",a.defaultValue={}),(t=u.value)==null||t.openDialog()},y=async e=>{try{const t=e?[e.id]:o.selectList.map(r=>r.id);if(!t.length){s.warning("请选择要删除的数据");return}await v("确定要删除选中的数据吗？"),await F(t),s.success("删除成功"),n()}catch(t){t!=="cancel"&&s.error("删除失败")}},n=async()=>{var e,t;try{const r=await P({page:o.pagination.page,size:o.pagination.limit,...((e=f.value)==null?void 0:e.queryParams)||{}}),l=((t=r.data)==null?void 0:t.data)||r;o.dataList=l.records||l,o.pagination.total=l.total||l.length||0}catch{s.error("数据加载失败")}};return L(()=>{n()}),(e,t)=>{const r=S,l=q,p=T;return M(),B("div",I,[d(r,{ref_key:"refSearch",ref:f,config:x},null,8,["config"]),d(l,{class:"card-table",config:o},null,8,["config"]),d(p,{ref_key:"refDialogForm",ref:u,config:a},null,8,["config"])])}}},G=k(N,[["__scopeId","data-v-08b205c6"]]);export{G as default};
