import{_ as D}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{z as g,d as k,c as b,r as u,b as o,S,o as v,g as H,n as I,q as f,i as m,aB as q,aq as F,C}from"./index-r0dFAfgr.js";import{_ as X}from"./Search-NSrhrIa_.js";const P=i=>g({url:"/api/spp/dma/partition/fireHydrant/list",method:"get",params:i}),L=i=>g({url:"/api/spp/dma/partition/fireHydrant",method:"post",data:i}),M=i=>g({url:"/api/spp/dma/partition/fireHydrant",method:"delete",data:i}),B=k({__name:"MoreDetail_XHSXX",props:{partition:{}},setup(i){const y=i,p=b(),d=b(),h=u({filters:[{type:"input",label:"编号",field:"code",clearable:!1}],operations:[{type:"btn-group",btns:[{perm:!0,iconifyIcon:"ep:search",text:"查询",type:"primary",click:()=>s()},{perm:!0,iconifyIcon:"ep:refresh",text:"重置",type:"default",click:()=>{var e;(e=p.value)==null||e.resetForm()}},{perm:!0,iconifyIcon:"ep:circle-plus",text:"新增",type:"success",click:()=>_()}]}]}),r=u({dataList:[],columns:[{label:"编号",prop:"code"},{label:"地址",prop:"address"},{label:"类型",prop:"type"},{label:"运行状态",prop:"runStatus"},{label:"备注",prop:"remark"},{label:"现场图片",prop:"img",image:!0}],pagination:{refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,s()}},operations:[{perm:!0,text:"编辑",iconifyIcon:"ep:edit",click:e=>_(e)},{perm:!0,text:"删除",iconifyIcon:"ep:delete",type:"danger",click:e=>x(e)}]}),s=async()=>{var e,t;r.loading=!0;try{const a=((e=p.value)==null?void 0:e.queryParams)||{},c=(await P({...a,partitionId:(t=y.partition)==null?void 0:t.value,page:r.pagination.page||1,size:r.pagination.limit||20})).data.data||{};r.dataList=c.data||[],r.pagination.total=c.total||0}catch{}r.loading=!1},_=e=>{var t;l.defaultValue={...e||{}},l.title=e?"编辑消火栓":"添加消火栓",(t=d.value)==null||t.openDialog()},x=e=>{const t=e?[e.id]:[];if(!t.length){o.error("请选择要删除的数据");return}S("确定删除?","提示信息").then(async()=>{try{const a=await M(t);a.data.code===200?(o.success("删除成功"),s()):o.error(a.data.message)}catch{o.error("删除失败")}}).catch(()=>{})},l=u({title:"添加流量表",dialogWidth:600,labelPosition:"right",group:[{fields:[{lg:24,xl:12,type:"input",label:"编号",field:"code",rules:[{required:!0,message:"请输入编号"}]},{lg:24,xl:12,type:"textarea",label:"地址",field:"address"},{lg:24,xl:12,type:"input",label:"类型",field:"type",rules:[{required:!0,message:"请输入类型"}]},{lg:24,xl:12,type:"input",label:"运行状态",field:"runStatus"},{type:"textarea",label:"备注",field:"remark"},{type:"image",label:"现场图片",field:"img"}]}],submit:async e=>{var t,a;l.submitting=!0;try{const n=await L({...e,partitionId:(t=y.partition)==null?void 0:t.value});n.data.code===200?(o.success("提交成功"),s(),(a=d.value)==null||a.closeDialog()):o.error(n.data.message)}catch{o.error("提交失败")}l.submitting=!1}});return v(()=>{s()}),(e,t)=>{const a=X,n=F,c=D;return H(),I(q,null,[f(a,{ref_key:"refSearch",ref:p,config:m(h),class:"search"},null,8,["config"]),f(n,{config:m(r),class:"table-box"},null,8,["config"]),f(c,{ref_key:"refDialog",ref:d,config:m(l)},null,8,["config"])],64)}}}),V=C(B,[["__scopeId","data-v-69d2301b"]]);export{V as default};
