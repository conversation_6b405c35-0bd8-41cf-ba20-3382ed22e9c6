import{d as Z,a6 as tt,c as D,r as R,bB as O,ay as at,g as et,h as ot,F as V,p as M,q as k,i as it,j as rt,_ as st,bt as nt,aq as pt,c5 as mt,a0 as j,C as lt}from"./index-r0dFAfgr.js";import{w as ct}from"./Point-WxyopZva.js";import{g as B,n as ut,m as dt}from"./MapView-DaoQedLH.js";import{u as ft}from"./useStation-DJgnSZIA.js";import{d as gt}from"./zhandian-YaGuQZe6.js";import yt from"./RightDrawerMap-D5PhmGFO.js";import{g as z}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as bt}from"./ViewHelper-BGCZjxXH.js";import{s as vt}from"./flowMonitoring-DtJlPj0G.js";import{a as ht}from"./headwaterMonitoring-BgK7jThW.js";import{p as $,l as wt}from"./echart-CJcuvGqs.js";import{r as G}from"./chart-wy3NEK2T.js";import{d as _t}from"./onemap-CEunQziB.js";import{g as H}from"./URLHelper-B9aplt5w.js";import"./widget-BcWKanF2.js";import"./ArcView-DpMnCY82.js";import"./geometryEngineBase-BhsKaODW.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./DateFormatter-Bm9a68Ax.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Dt={class:"content"},kt={class:"right-box"},Rt=Z({__name:"index",setup(Ct){const q=tt(),U=D();D();const{getAllStationOption:W}=ft(),I=D(),x=D(),T=D(!0),N=D();R([]);const Y=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],e=R({lineOption:null,pieOption:null,stationStatus:[],curRow:{},activeName:null,data:null,stationLocation:[],tableData:[],windows:[]}),y={},C=async a=>{var s,u,d,v,n,g,_;const o=b.dataList.find(c=>c.stationId===a),r=e.tableData.find(c=>c.stationId===a);f.tabs=(s=r==null?void 0:r.dataList)==null?void 0:s.map((c,h)=>({label:c.propertyName,value:c.property+h,data:c})),e.activeName=f.tabs?f.tabs[0].value:"",e.curRow=o,L(o,f.tabs?f.tabs[0].data:{});let t;if(a?(t=(v=y.view)==null?void 0:v.graphics.find(c=>{var h,w;return((w=(h=c.attributes)==null?void 0:h.row)==null?void 0:w.id)===a}),t&&await z(y.view,t,{zoom:15,avoidHighlight:!0})):t=(d=(u=y.view)==null?void 0:u.graphics)==null?void 0:d.getItemAt(0),!t)return;const p=((n=t.attributes)==null?void 0:n.row)||{},l=((g=(await gt(p.id)).data)==null?void 0:g.map(c=>(c.label=c.propertyName,c.value,c)))||[],i=t==null?void 0:t.geometry;e.windows.length=0,e.windows.push({visible:!1,x:i.x,y:i.y,offsetY:-30,title:p.name,attributes:{values:l,id:p.id}}),await O(),(_=I.value)==null||_.openPop(p.id)},f=R({type:"tabs",tabType:"simple",tabs:[],handleTabClick:(a,o)=>{const r=f.tabs.find(t=>t.value===a.props.name)||{};L(e.curRow,r.data)}}),A=R({group:[{id:"chart",fieldset:{desc:"水质监测设备",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:G(),style:{height:"150px"}}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),b=R({loading:!1,dataList:[],columns:[],highlightCurrentRow:!0,currentRowKey:"stationId",handleRowClick:async a=>{var t,p;const o=(t=y.view)==null?void 0:t.graphics.find(m=>{var l,i;return((i=(l=m.attributes)==null?void 0:l.row)==null?void 0:i.id)===a.stationId});o&&await z(y.view,o,{zoom:15,avoidHighlight:!0});const r=e.tableData.find(m=>m.stationId===a.stationId);f.tabs=(p=r==null?void 0:r.dataList)==null?void 0:p.map((m,l)=>({label:m.propertyName,value:m.property+l,data:m})),e.activeName=f.tabs[0].value,L(f.tabs[0].data),b.currentRow=a,e.curRow=a,C(a.stationId)},pagination:{hide:!0}}),K=async()=>{var a;vt({stationType:"水质监测站",projectId:(a=j().selectedProject)==null?void 0:a.value}).then(o=>{var r,t,p,m,l,i,s;if(e.tableData=o.data,e.tableData){const u=(t=(r=e.tableData[0])==null?void 0:r.dataList)==null?void 0:t.map(n=>({prop:n.property,label:n.propertyName,unit:n.unit}));b.loading=!1;const d=(p=e.tableData)==null?void 0:p.map(n=>{var c,h;const g=(c=n.dataList)==null?void 0:c.find(w=>w.property==="pressure"),_={name:n.name,stationId:n.stationId,time:g?g.time:"-",unit:g?g.unit:"-"};return(h=n==null?void 0:n.dataList)==null||h.map(w=>{_[w.property+""]=w.value}),_});J(d),b.columns=[{prop:"name",label:"名称",minWidth:200}].concat(u),b.dataList=d,e.activeName=(m=e.tableData[0])==null?void 0:m.dataList[0],b.currentRow=d[0];const v=e.tableData.find(n=>{var g;return n.stationId===((g=d[0])==null?void 0:g.stationId)});f.tabs=(l=v==null?void 0:v.dataList)==null?void 0:l.map((n,g)=>({label:n.propertyName,value:n.property+g,data:n})),e.activeName=f.tabs?f.tabs[0].value:"",L(f.tabs?f.tabs[0].value:""),console.log("resetPanel",(i=e.tableData[0])==null?void 0:i.stationId),C((s=e.tableData[0])==null?void 0:s.stationId)}else b.loading=!1,T.value=!1}),await Q()},Q=async()=>{var m,l,i,s;const a=await _t({projectId:(m=j().selectedProject)==null?void 0:m.value,status:""}),o=A.group[0].fields[0],r=((i=(l=a.data)==null?void 0:l.data)==null?void 0:i.length)||0,t=[],p=(s=a.data)==null?void 0:s.data;p==null||p.map(u=>{let d=t.find(n=>n.status===u.status);const{label:v}=Y.find(n=>n.name===u.status)||{};d?d.value++:(d={name:u.status,status:u.status,nameAlias:v,value:1,scale:"0%"},t.push(d))}),t.map(u=>(u.scale=r===0?"0%":Number(u.value)/r*100+"%",u)),o&&(o.option=G(t,"个"))},L=async(a,o)=>{b.currentRow=a,(o||a)&&(await E(o||a),O(async()=>{e.pieOption=$()})),T.value=!1},E=async a=>{var l,i;const r=(l=(await ht({deviceId:a.deviceId,attr:a.property})).data)==null?void 0:l.data,t=wt(200,r.todayDataList.map(s=>s.ts),40,40),p=[{name:"前天",key:"beforeYesterdayDataList"},{name:"昨天",key:"yesterdayDataList"},{name:"今天",key:"todayDataList"}];t.yAxis[0].name=a.propertyName.concat(a.unit?"("+a.unit+")":"");const m=p.map(s=>{const u=r[s.key].map(d=>d.value);return{name:s.name,smooth:!0,data:u,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}});t.series=m,(i=x.value)==null||i.clear(),await O(()=>{N.value&&q.listenTo(N.value,()=>{var s;e.lineOption=t,e.pieOption=$(),(s=x.value)==null||s.resize()})})},J=async a=>{var r,t,p,m;const o=await W("水质监测站");e.stationStatus=o,console.log("水质监测站",o),(t=(r=y.view)==null?void 0:r.graphics)==null||t.removeAll(),o.map(l=>{var c,h,w,P,F;const i=l.data,s=(c=i==null?void 0:i.location)==null?void 0:c.split(","),u=new ct({longitude:s==null?void 0:s[0],latitude:s==null?void 0:s[1],spatialReference:(h=y.view)==null?void 0:h.spatialReference}),d=(w=e.stationStatus)==null?void 0:w.find(S=>S.id===l.id);console.log("status",d.status);const v=d.status==="online"?H("水质监测站.png"):H("水质监测站.png"),n=new B({geometry:u,symbol:new ut({width:25,height:30,yoffset:15,url:v}),attributes:{row:i}}),g=a==null?void 0:a.find(S=>S.stationId===i.id),_=new B({geometry:u,symbol:new dt({yoffset:-15,color:"#00ff33",text:g?g.name:"-"})});(F=(P=y.view)==null?void 0:P.graphics)==null||F.addMany([n,_])}),e.stationLocation=o,e.curRow=(p=o[0])==null?void 0:p.data,b.currentRow=e.curRow,C((m=e.curRow)==null?void 0:m.id)},X=async a=>{var o;y.view=a,(o=I.value)==null||o.toggleCustomDetail(!1),await K(),bt(y.view,r=>{var p,m,l;const t=(p=r.results)==null?void 0:p[0];if(t&&t.type==="graphic"){const i=(l=(m=t.graphic)==null?void 0:m.attributes)==null?void 0:l.row;C(i==null?void 0:i.id)}})};return(a,o)=>{const r=st,t=nt,p=pt,m=mt,l=at("VChart");return et(),ot(yt,{ref_key:"refMap",ref:I,title:"水质监测总览",windows:e.windows,"hide-detail-close":!0,"hide-layer-list":!0,"right-drawer-width":540,onMapLoaded:X},{"detail-header":V(()=>o[1]||(o[1]=[])),"detail-default":V(()=>o[2]||(o[2]=[])),default:V(()=>{var i;return[M("div",Dt,[k(r,{ref:"refForm",config:A},null,8,["config"]),k(t,{type:"underline",title:"水质数据监测"}),M("div",kt,[k(p,{ref_key:"refCard",ref:U,class:"table-box",config:b},null,8,["config"])]),k(t,{type:"underline",title:(((i=e.curRow)==null?void 0:i.name)||"")+"近三天运行曲线"},null,8,["title"]),M("div",{ref_key:"echartsDiv",ref:N,class:"right-box bottom"},[k(m,{modelValue:e.activeName,"onUpdate:modelValue":o[0]||(o[0]=s=>e.activeName=s),config:f},null,8,["modelValue","config"]),k(l,{ref_key:"refChart",ref:x,theme:it(rt)().isDark?"dark":"light",option:e.lineOption},null,8,["theme","option"])],512)])]}),_:1},8,["windows"])}}}),Me=lt(Rt,[["__scopeId","data-v-bad30f3e"]]);export{Me as default};
