package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_TENANT_LOGIN_TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
public class TenantLogin {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_TENANT_LOGIN_NAME)
    private String name;

    @Column(name = ModelConstants.TB_TENANT_LOGIN_ACCOUNT)
    private String account;

    @Column(name = ModelConstants.TB_TENANT_LOGIN_PASSWORD)
    private String password;

    @Column(name = ModelConstants.TB_TENANT_LOGIN_URL)
    private String url;


}
