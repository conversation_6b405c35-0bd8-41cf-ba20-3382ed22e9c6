package org.thingsboard.server.dao.gis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.gis.GisPeopleSettingEntity;
import org.thingsboard.server.dao.sql.gis.GisPeopleSettingRepository;

import java.util.List;

@Slf4j
@Service
public class GisPeopleSettingServiceImpl implements GisPeopleSettingService {

    @Autowired
    private GisPeopleSettingRepository gisPeopleSettingRepository;

    @Override
    public GisPeopleSettingEntity get(String id) {
        return gisPeopleSettingRepository.findOne(id);
    }

    @Override
    public PageData<GisPeopleSettingEntity> findList(Integer page, Integer size, String name, TenantId tenantId) {
        // 处理分页数据
        PageRequest pageRequest = new PageRequest(page - 1, size);

        Page<GisPeopleSettingEntity> pageResult = gisPeopleSettingRepository.findList(name, UUIDConverter.fromTimeUUID(tenantId.getId()), pageRequest);
        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public void save(GisPeopleSettingEntity entity) {
        gisPeopleSettingRepository.save(entity);
    }

    @Override
    public List<GisPeopleSettingEntity> findByType(String type, TenantId tenantId) {
        return gisPeopleSettingRepository.findByTypeAndTenantId(type, UUIDConverter.fromTimeUUID(tenantId.getId()));
    }
}
