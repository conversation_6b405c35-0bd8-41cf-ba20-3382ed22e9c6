package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.dao.model.request.PipeCollectDataRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollectData;

import java.util.List;

public interface PipeCollectDataService {

    List<PipeCollectData> save(List<PipeCollectData> pipeCollectData, String tenantId, String userId);

    List<PipeCollectData> getList(PipeCollectDataRequest pipeCollectDataRequest);

    void delete(List<String> ids);
}
