import{d as O,r as f,bF as o,c as y,a8 as Y,bX as I,s as w,o as M,ah as N,g as v,n as x,bo as F,i as h,p as B,q as P,br as H,bD as V,C as W}from"./index-r0dFAfgr.js";import{_ as E}from"./CardTable-rdWOL4_6.js";import{_ as R}from"./CardSearch-CB_HNR-Q.js";import{a as z}from"./statisticalAnalysis-BoRmiv4A.js";import{r as C}from"./data-D3PIONJl.js";import{u as A}from"./useStation-DJgnSZIA.js";import{p as J}from"./printUtils-C-AxhDcd.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const Q={class:"wrapper"},X={class:"main"},G={class:"right"},K=O({__name:"index",setup(U){const{getStationTree:S}=A(),d=f({queryType:"day",treeDataType:"Station",stationId:"",title:""}),_=o().date(),g=y(),l=y(),n=f({data:[],currentProject:{}}),k=y(!1),c=f({defaultParams:{type:"day",year:[o().format(),o().format()],month:[o().format(),o().format()],day:[o().date(_-6).format("YYYY-MM-DD"),o().date(_).format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:Y(()=>n.data),label:"站点选择",onChange:t=>{const e=I(n.data,"children","id",t);n.currentProject=e,d.treeDataType=e.data.type,d.treeDataType==="Station"&&(d.stationId=e.id,u())}},{type:"radio-button",field:"type",options:C,label:"报告类型",onChange:t=>q(t)},{type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(t,e,a)=>{a.hidden=t.type==="month"||t.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(t,e,a)=>{a.hidden=t.type==="day"||t.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(t,e,a)=>{a.hidden=t.type==="month"||t.type==="day"}},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>u(),icon:"iconfont icon-chaxun"},{text:"导出",perm:!0,type:"warning",icon:"iconfont icon-xiazai",click:()=>j()},{perm:!0,text:"打印",type:"success",svgIcon:w(V),click:()=>L()}]}]}),q=t=>{var a,s;const e=(a=c.filters)==null?void 0:a.find(i=>i.field==="date");e.type=t==="month"?"monthrange":t==="year"?"yearrange":"daterange",d.queryType=t,console.log(o().add(-1,"month").startOf("month")),t==="month"&&(c.defaultParams={...c.defaultParams,date:[o().add(-1,"month").startOf("month"),o().endOf("month")]},(s=l.value)==null||s.resetForm())},r=f({loading:!1,dataList:[],columns:[],operations:[],operationWidth:"150px",pagination:{hide:!0}}),u=()=>{var b;r.loading=!0;const t=n.currentProject.id,e=((b=l.value)==null?void 0:b.queryParams)||{},a=e[e.type],s=C.find(m=>m.value===e.type);d.title=n.currentProject.label+"单耗报表("+s.label+o(a[0]).format(s.data)+"至"+o(a[1]).format(s.data)+")",r.title=d.title,console.log(e.date);const i={stationId:t,start:o(a[0]).startOf(e.type).valueOf(),end:o(a[1]).endOf(e.type).valueOf(),queryType:e.type};z(i).then(m=>{const D=m.data.data,T=D.tableInfo.map(p=>({prop:p.columnValue,label:p.columnName,unit:p.unit?"("+p.unit+")":""}));console.log(T),r.columns=T,r.dataList=D.tableDataList,r.loading=!1})},j=()=>{var t;(t=g.value)==null||t.exportTable()},L=()=>{J({title:d.title,data:r.dataList,titleList:r.columns})};return M(async()=>{var e;const t=await S("泵站");n.data=t,n.currentProject=N(n.data),c.defaultParams={...c.defaultParams,treeData:n.currentProject},(e=l.value)==null||e.resetForm(),u()}),(t,e)=>{const a=R,s=E,i=H;return v(),x("div",Q,[F((v(),x("div",X,[B("div",G,[P(a,{ref_key:"cardSearch",ref:l,config:h(c)},null,8,["config"]),P(s,{id:"print",ref_key:"refTable",ref:g,class:"card-table",config:h(r)},null,8,["config"])])])),[[i,h(k)]])])}}}),ct=W(K,[["__scopeId","data-v-1925e162"]]);export{ct as default};
