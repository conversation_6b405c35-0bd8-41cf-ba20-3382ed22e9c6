<template>
  <slot :config="config">
    <label
      v-if="config.type === 'text'"
      class="readonly-input"
      :class="config.className"
      :style="
        typeof config.style === 'function'
          ? config.style(state.value, row, config)
          : config.style
      "
    >
      {{
        (config.formatter ? config.formatter(modelValue, row) : modelValue) ||
        '-'
      }}
      {{ config.unit ? config.unit : '' }}
    </label>
    <Input
      v-else-if="
        config.type === 'input' ||
        config.type === 'password' ||
        config.type === 'textarea'
      "
      v-model="state.value"
      :config="config"
      :size="computedSize"
      :row="row"
      @blur="(e) => $emit('blur', state.value, e)"
      @change="handleChange"
      @focus="(e) => $emit('focus', state.value, e)"
      @clear="$emit('clear', state.value)"
    >
      <template v-if="config.suffix" #suffix>
        <span v-if="config.unit">{{ config.unit }}</span>
        <span v-else>{{ config.suffix }}</span>
      </template>
      <template v-if="config.prefix" #prefix>
        <span>{{ config.prefix }}</span>
      </template>
      <template v-if="config.prepend" #prepend>
        <span v-if="typeof config.prepend === 'string'">{{
          config.prepend
        }}</span>
        <template v-else>
          <el-select
            v-model="state.prependVal"
            :popper-class="popperClass || config.popperClass"
            :placeholder="
              (typeof config.placeholder === 'function'
                ? config.placeholder(state.prependVal, row, config)
                : config.placeholder) || '请输入'
            "
            :style="{
              width: '100%',
              ...(typeof config.prepend?.style === 'function'
                ? config.prepend.style(state.prependVal, row, config)
                : config.prepend?.style)
            }"
            @change="(val) => onChange(config.prepend, val, config)"
          >
            <el-option
              v-for="(obj, i) in config.prepend?.options"
              :key="i"
              :label="obj.label"
              :value="obj.value"
            />
          </el-select>
        </template>
      </template>
      <template
        v-if="config.append || config.btn || config.appendBtns?.length"
        #append
      >
        <span v-if="typeof config.append === 'string'">{{
          config.append
        }}</span>
        <Button
          v-else-if="config.btn"
          :config="config.btn"
          :row="row"
          :size="computedSize"
        ></Button>
        <template v-else-if="config.appendBtns?.length">
          <Button
            v-for="(btn, i) in config.appendBtns"
            :key="i"
            :config="btn"
            :row="row"
            :size="computedSize"
          ></Button>
        </template>

        <template v-else>
          <el-select
            v-model="state.appendVal"
            :popper-class="popperClass || config.popperClass"
            :placeholder="
              (typeof config.placeholder === 'function'
                ? config.placeholder(state.appendVal, row, config)
                : config.placeholder) || '请输入'
            "
            :style="
              typeof config.append?.style === 'function'
                ? config.append.style(state.appendVal, row, config)
                : config.append?.style
            "
            @change="(val) => onChange(config.append, val, config)"
          >
            <el-option
              v-for="(obj, i) in config.append?.options"
              :key="i"
              :label="obj.label"
              :value="obj.value"
            />
          </el-select>
        </template>
      </template>
    </Input>

    <el-input
      v-if="config.type === 'input-number'"
      v-model="state.value"
      :size="computedSize"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      style="width: 100%"
      :class="{ 'no-border': config.noBorder }"
      :placeholder="
        (typeof config.placeholder === 'function'
          ? config.placeholder(state.value, row, config)
          : config.placeholder) || '请输入'
      "
      onkeyup="value=value.replace(/[^\d.]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')"
      @change="handleChange"
    >
      <template v-if="config.suffix || config.unit" #suffix>
        <span v-if="config.unit">{{ config.unit }}</span>
        <span v-else>{{ config.suffix }}</span>
      </template>
      <template v-if="config.prefix" #prefix>
        <span>{{ config.prefix }}</span>
      </template>
      <template v-if="config.prepend" #prepend>
        <span v-if="typeof config.prepend === 'string'">{{
          config.prepend
        }}</span>
        <template v-else>
          <el-select
            v-model="state.prependVal"
            :placeholder="
              (typeof config.placeholder === 'function'
                ? config.placeholder(state.prependVal, row, config)
                : config.placeholder) || '请输入'
            "
            :style="
              typeof config.prepend?.style === 'function'
                ? config.prepend.style(state.prependVal, row, config)
                : config.prepend?.style
            "
            @change="(val) => onChange(config.prepend, val, config)"
          >
            <el-option
              v-for="(obj, i) in config.prepend?.options"
              :key="i"
              :label="obj.label"
              :value="obj.value"
            />
          </el-select>
        </template>
      </template>
      <template v-if="config.append || config.appendBtns?.length" #append>
        <template v-if="config.append">
          <span v-if="typeof config.append === 'string'">{{
            config.append
          }}</span>
          <template v-else>
            <el-select
              v-model="state.appendVal"
              :popper-class="popperClass || config.popperClass"
              :placeholder="
                (typeof config.placeholder === 'function'
                  ? config.placeholder(state.appendVal, row, config)
                  : config.placeholder) || '请输入'
              "
              :style="
                typeof config.append?.style === 'function'
                  ? config.append.style(state.appendVal, row, config)
                  : config.append?.style
              "
              @change="(val) => onChange(config.append, val, config)"
            >
              <el-option
                v-for="(obj, i) in config.append?.options"
                :key="i"
                :label="obj.label"
                :value="obj.value"
              />
            </el-select>
          </template>
        </template>

        <Button
          v-for="(btn, i) in config.appendBtns"
          :key="i"
          :config="btn"
          :row="row"
          :size="computedSize"
        ></Button>
      </template>
    </el-input>

    <el-input-number
      v-else-if="config.type === 'number'"
      v-show="!config.hidden"
      v-model="state.value"
      :size="computedSize"
      style="width: 100%"
      :class="{ 'no-border': config.noBorder }"
      :min="config.min"
      :precision="config.precision"
      :max="config.max"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :controls-position="config.controlPosition"
      :step="config.step || 1"
      @change="handleChange"
    />
    <el-select
      v-else-if="config.type === 'select'"
      v-show="!config.hidden"
      v-model="state.value"
      :size="computedSize"
      :style="{
        width: config.width || '100%',
        minWidth: '140px'
      }"
      :class="{ 'no-border': config.noBorder }"
      filterable
      :popper-class="popperClass || config.popperClass"
      :collapse-tags="config.collapseTags !== false"
      :clearable="config.clearable !== false"
      :allow-create="config.allowCreate"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :multiple="config.multiple"
      :placeholder="
        (typeof config.placeholder === 'function'
          ? config.placeholder(state.value, row, config)
          : config.placeholder) || '请输入'
      "
      @change="handleChange"
      @focus="(e) => $emit('focus', state.value, e)"
    >
      <!-- <template v-if="config.setOptionBy">
      <el-option v-for="(obj, i) in state.options" :key="i" :label="obj.label" :value="obj.value" />
    </template>
    <template v-else> -->
      <el-option
        v-for="(obj, i) in config.options"
        :key="i"
        :label="obj.label"
        :value="obj.value"
        :disabled="
          config.disableOption
            ? config.disableOption(obj, state.value, row)
            : typeof obj.disabled === 'function'
              ? obj.disabled(state.value, row, obj)
              : obj.disabled
        "
      >
        <span style="float: left">{{ obj.label }}</span>
        <span
          v-if="obj.append"
          style="
            float: right;
            color: var(--el-text-color-secondary);
            font-size: 13px;
          "
          >{{ obj.value }}</span
        >
      </el-option>
      <!-- </template> -->
    </el-select>
    <el-cascader
      v-else-if="config.type === 'cascader'"
      v-model="state.value"
      :style="{
        width: config.width || '100%'
      }"
      :popper-class="popperClass || config.popperClass"
      :clearable="config.clearable !== false"
      :size="computedSize"
      :class="{ 'no-border': config.noBorder }"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :options="config.options as any"
      :props="
        config.props || {
          checkStrictly: true
        }
      "
      @change="handleChange"
      @focus="(e) => $emit('focus', state.value, e)"
    />
    <el-tree-select
      v-else-if="config.type === 'select-tree'"
      v-model="state.value"
      :style="{
        width: config.width || '100%',
        minWidth: '140px'
      }"
      :size="computedSize"
      :data="config.options"
      :popper-class="popperClass || config.popperClass"
      :default-expand-all="config.defaultExpandAll ?? true"
      :class="{ 'no-border': config.noBorder }"
      :filterable="config.filterable"
      :check-strictly="config.checkStrictly"
      :multiple="config.multiple"
      :lazy="config.lazy"
      :collapse-tags="true"
      :placeholder="
        typeof config.placeholder === 'function'
          ? config.placeholder(state.value, row, config)
          : config.placeholder
      "
      :clearable="config.clearable !== false"
      :load="config.lazyLoad"
      :props="config.props || state.defaultProp"
      :disabled="
        typeof config.readonly === 'function'
          ? !!config.readonly(state.value, row, config)
          : config.readonly
      "
      :show-checkbox="config.multiple && config.showCheckbox"
      :filter-node-method="filterNodeMethod"
      @change="() => handleChange(state.value)"
      @node-click="(...args: any[]) => nodeClick(config, ...args)"
      @focus="(e) => $emit('focus', state.value, e)"
    />
    <el-switch
      v-else-if="config.type === 'switch'"
      v-show="!config.hidden"
      v-model="state.value"
      :inline-prompt="config.inlinePrompt !== false"
      :width="config.width"
      :size="computedSize"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :active-color="config.activeColor || '#1DCF8E'"
      :inactive-color="config.inActiveColor || '#909399'"
      :active-text="config.activeText || '是'"
      :inactive-text="config.inActiveText || '否'"
      :active-value="config.activeValue || true"
      :inactive-value="config.inActiveValue || false"
      @change="handleChange"
    />
    <el-radio-group
      v-else-if="config.type === 'radio' || config.type === 'radio-button'"
      v-show="!config.hidden"
      v-model="state.value"
      :size="computedSize"
      :style="{
        ...(config.style || {}),
        width: config.width || '100%'
      }"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      @change="handleChange"
    >
      <template v-if="config.type === 'radio'">
        <el-radio
          v-for="(kv, i) in config.options"
          :key="i"
          :border="config.noBorder === undefined ? false : !config.noBorder"
          :label="kv.value"
        >
          {{ kv.label }}
        </el-radio>
      </template>
      <template v-else-if="config.type === 'radio-button'">
        <el-radio-button
          v-for="(kv, i) in config.options"
          :key="i"
          :label="kv.value"
          :style="
            typeof config.itemStyle === 'function'
              ? config.itemStyle(state.value, row, config)
              : config.itemStyle
          "
        >
          <span
            v-if="config.prefix"
            :style="
              typeof config.prefixStyle === 'function'
                ? config.prefixStyle(kv, state.value, row, config)
                : config.prefixStyle
            "
            >{{
              typeof config.prefix === 'function'
                ? config.prefix(kv, state.value, row, config)
                : config.prefix
            }}</span
          >
          <span>
            <el-icon v-if="kv.icon || kv.svgIcon">
              <i v-if="kv.icon" :class="kv.icon"></i>
              <component :is="kv.svgIcon" v-else></component>
            </el-icon>

            {{ kv.label }}
          </span>
          <Icon v-if="kv.iconifyIcon" :icon="kv.iconifyIcon as any"></Icon>

          <span
            v-if="config.suffix"
            :style="
              typeof config.suffixStyle === 'function'
                ? config.suffixStyle(kv, state.value, row, config)
                : config.suffixStyle
            "
            >{{
              typeof config.suffix === 'function'
                ? config.suffix(kv, state.value, row, config)
                : config.suffix
            }}</span
          >
        </el-radio-button>
      </template>
    </el-radio-group>
    <el-checkbox-group
      v-else-if="config.type === 'checkbox'"
      v-show="!config.hidden"
      v-model="state.value"
      :size="computedSize"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      @change="handleChange"
    >
      <el-checkbox
        v-for="(kv, i) in config.options"
        :key="i"
        :border="config.noBorder === undefined ? false : !config.noBorder"
        :value="kv.value"
        :label="kv.label"
      >
      </el-checkbox>
    </el-checkbox-group>
    <el-date-picker
      v-else-if="config.type === 'month'"
      v-model="state.value"
      :clearable="config.clearable !== false"
      :popper-class="popperClass || config.popperClass"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :style="{
        width: config.width || '100%'
      }"
      :size="computedSize"
      :type="config.type"
      :placeholder="
        (typeof config.placeholder === 'function'
          ? config.placeholder(state.value, row, config)
          : config.placeholder) || '请选择'
      "
      :disabled-date="(val) => getDateOptions(val)"
      :format="config.textFormat || 'YYYY-MM'"
      :value-format="config.format || 'YYYY-MM'"
      @change="handleChange"
      @focus="(e) => $emit('focus', state.value, e)"
    />
    <el-date-picker
      v-else-if="config.type === 'year'"
      v-model="state.value"
      :clearable="config.clearable !== false"
      :popper-class="popperClass || config.popperClass"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :style="{
        width: config.width || '100%'
      }"
      :size="computedSize"
      :type="config.type"
      :placeholder="
        (typeof config.placeholder === 'function'
          ? config.placeholder(state.value, row, config)
          : config.placeholder) || '请选择'
      "
      :disabled-date="(val) => getDateOptions(val)"
      :format="config.textFormat || 'YYYY'"
      :value-format="config.format || 'YYYY'"
      @change="handleChange"
      @focus="(e) => $emit('focus', state.value, e)"
    />
    <el-date-picker
      v-else-if="config.type === 'date' || config.type === 'datetime'"
      v-model="state.value"
      :clearable="config.clearable !== false"
      :popper-class="popperClass || config.popperClass"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :style="{
        width: config.width || '100%'
      }"
      :size="computedSize"
      :type="config.type"
      :placeholder="
        (typeof config.placeholder === 'function'
          ? config.placeholder(state.value, row, config)
          : config.placeholder) || '请选择'
      "
      :disabled-date="(val) => getDateOptions(val)"
      :format="
        config.textFormat ||
        (config.type === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss')
      "
      :value-format="
        config.format
          ? config.format
          : config.type === 'date'
            ? 'YYYY-MM-DD'
            : 'YYYY-MM-DD HH:mm:ss'
      "
      @change="handleChange"
      @focus="(e) => $emit('focus', state.value, e)"
    />
    <el-date-picker
      v-else-if="
        config.type === 'daterange' ||
        config.type === 'datetimerange' ||
        config.type === 'monthrange'
      "
      v-model="state.value"
      style="padding: 0 10px"
      :style="
        typeof config.style === 'function'
          ? config.style(state.value, row, config)
          : config.style
      "
      :shortcuts="config.shortCusts"
      :popper-class="popperClass || config.popperClass"
      :size="computedSize"
      :clearable="config.clearable !== false"
      :type="config.type"
      :range-separator="config.rangeSeparator || '到'"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :placeholder="
        (typeof config.placeholder === 'function'
          ? config.placeholder(state.value, row, config)
          : config.placeholder) || '请选择'
      "
      :disabled-date="(val) => getDateOptions(val)"
      :value-format="
        config.format
          ? config.format
          : config.type === 'daterange'
            ? 'YYYY-MM-DD'
            : 'YYYY-MM-DD HH:mm:ss'
      "
      :format="config.textFormat"
      @change="handleChange"
      @focus="(e) => $emit('focus', state.value, e)"
    />

    <div
      v-else-if="config.type === 'yearrange'"
      style="padding: 0 10px; display: flex"
    >
      <el-date-picker
        v-model="state.value[0]"
        type="year"
        format="YYYY"
        :size="computedSize"
        :popper-class="popperClass || config.popperClass"
        :clearable="config.clearable !== false"
        :placeholder="
          (typeof config.placeholder === 'function'
            ? config.placeholder(state.value[0], row, config)
            : config.placeholder) || '请选择'
        "
        :disabled-date="(val) => pickerStartAuditYear(val)"
        value-format="YYYY"
        style="width: 100px"
      >
      </el-date-picker>
      ～
      <el-date-picker
        v-model="state.value[1]"
        type="year"
        format="YYYY"
        :size="computedSize"
        :popper-class="popperClass || config.popperClass"
        :clearable="config.clearable !== false"
        :placeholder="
          (typeof config.placeholder === 'function'
            ? config.placeholder(state.value[1], row, config)
            : config.placeholder) || '请选择'
        "
        :disabled-date="(val) => pickerEndAuditYear(val)"
        value-format="YYYY"
        style="width: 100px"
      >
      </el-date-picker>
    </div>

    <el-time-picker
      v-else-if="config.type === 'time'"
      v-model="state.value"
      placeholder="请选择时间"
      :style="{
        width: config.width || '100%'
      }"
      range-separator="至"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :popper-class="popperClass || config.popperClass"
      :format="config.format"
      :is-range="config.isRange"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :size="computedSize"
      :clearable="config.clearable !== false"
      @change="handleChange"
      @focus="(e) => $emit('focus', state.value, e)"
    />
    <ChooseUserByRole
      v-else-if="config.type === 'user'"
      width="100%"
      :size="computedSize"
      :multiple="config.multiple"
      :height="config.height || '40px'"
      @check-users="(users) => handleChange(users)"
    />
    <SLUploader
      v-else-if="config.type === 'image'"
      v-model="state.value"
      :limit="config.limit"
      :size="computedSize"
      :accept="config.accept"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :url="config.url || state.imgActionUrl"
      :multiple="config.multiple"
      :return-type="config.returnType"
      @handle-success="config.handleSuccess"
    >
    </SLUploader>
    <SLFileUploader
      v-else-if="config.type === 'file'"
      v-model="state.value"
      :limit="config.limit"
      :size="computedSize"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :url="config.url || state.fileActionUrl"
      :multiple="config.multiple"
      :tips="config.tips"
      :return-type="config.returnType"
      :before-upload="config.beforeUpdate"
      :accept="config.accept"
      @handle-success="config.handleSuccess"
    ></SLFileUploader>
    <!-- <div v-else-if="config.type === 'amap'" class="amap-wrapper">
      <SLAmap
        v-show="!config.hidden"
        v-model="state.value"
        :size="computedSize"
        :init-center-mark="true"
        :disabled="
          typeof config.readonly === 'function'
            ? config.readonly(state.value, row, config)
            : config.readonly
        "
        :hide-input="config.hideInput"
        :required="config.required"
        :result-type="config.resultType"
        @getplacename="handleChange"
      ></SLAmap>
    </div>-->
    <TagGroup
      v-else-if="config.type === 'tags'"
      v-model="state.value"
      :config="config"
      :size="computedSize"
      @change="handleChange"
    ></TagGroup>
    <span
      v-else-if="config.type === 'hint'"
      style="color: #0099ff"
      :style="
        typeof config.style === 'function'
          ? config.style(state.value, row, config)
          : config.style
      "
      >{{ config?.text }}</span
    >
    <AvatarUploader
      v-else-if="config.type === 'avatar'"
      v-model="state.value"
      :url="config.url"
      :size="computedSize"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      :tips="config.tips"
      :image-style="config.imageStyle"
      @change="handleChange"
    ></AvatarUploader>
    <div
      v-else-if="config.type === 'btn-group'"
      class="btn-wrapper"
      :class="config.className"
      :style="
        typeof config.style === 'function'
          ? config.style(state.value, row, config)
          : config.style
      "
    >
      <template v-for="(item, i) in config.btns" :key="i">
        <component
          :is="item.component"
          v-if="item.component"
          :config="item"
          :row="row"
        ></component>
        <el-tooltip v-else-if="item.title" :content="item.title">
          <Button :config="item" :row="row" :size="computedSize"></Button>
        </el-tooltip>
        <el-popover
          v-else-if="item.items"
          placement="bottom"
          :width="50"
          trigger="hover"
        >
          <template #reference>
            <Button :config="item" :row="row" :size="computedSize"></Button>
          </template>
          <div class="btns">
            <template v-for="(item1, index) in item.items" :key="index">
              <component
                :is="item1.component"
                v-if="item1.component"
                :config="item1"
                :row="row"
              ></component>
              <Button
                v-else
                :config="item1"
                :row="row"
                :size="computedSize"
              ></Button>
              <div class="btn"></div>
            </template>
          </div>
        </el-popover>
        <Button v-else :config="item" :row="row" :size="computedSize"></Button>
      </template>
    </div>
    <Tabs
      v-else-if="config.type === 'tabs'"
      v-model="state.value"
      :row="row"
      :config="config"
      :size="computedSize"
      :style="{ width: config.width || '100%' }"
      @change="handleChange"
    ></Tabs>
    <IconSelector
      v-else-if="config.type === 'icon-selector'"
      v-model="state.value"
      style="width: 100%"
      :size="computedSize"
      :title="config.title"
      :popper-class="popperClass || config.popperClass"
      :clearable="config.clearable !== false"
      :default-type="config.defaultType"
      :placeholder="
        (typeof config.placeholder === 'function'
          ? config.placeholder(state.value, row, config)
          : config.placeholder) || '请选择'
      "
    ></IconSelector>
    <TiniImageUploader
      v-else-if="config.type === 'image-tiny'"
      v-model="state.value"
      :config="config"
      :size="computedSize"
      :placeholder="config.placeholder"
      @change="handleChange"
    ></TiniImageUploader>
    <FormTree
      v-else-if="config.type === 'tree'"
      v-model="state.value"
      :config="config"
      :row="row"
      :size="computedSize"
      @change="handleChange"
    >
      <template v-if="config.customNode" #default="{ data, node, customprops }">
        <component
          :is="config.customNode"
          :data="data"
          :node="node"
          :custom-props="customprops"
        ></component>
      </template>
    </FormTree>
    <FormWangeditor
      v-else-if="config.type === 'wangeditor'"
      v-model="state.value"
      :url="config.url"
      :mode="config.mode"
    />
    <List
      v-else-if="config.type === 'list'"
      v-model="state.value"
      :row="row"
      :config="config"
      @change="handleChange"
    />
    <div
      v-else-if="config.type === 'vchart'"
      ref="refChartDiv"
      style="width: 100%"
      :style="
        typeof config.style === 'function'
          ? config.style(state.value, row, config)
          : config.style
      "
      :class="config.className"
    >
      <VChart
        ref="refChart"
        :option="config.option"
        @highlight="(...args) => handleHighlight(config, ...args)"
        @click="(...args) => handleClick(config, ...args)"
        @zr:click="(...args) => handleZRClick(config, ...args)"
      >
      </VChart>
    </div>
    <div v-else-if="config.type === 'range'" style="width: 100%">
      <RangeSelecter
        v-model="state.value"
        :config="config"
        :row="row"
        @change="config.onChange"
      ></RangeSelecter>
    </div>
    <!-- <ColorPicker
      v-else-if="config.type === 'color-picker'"
      v-model="state.value"
      :size="computedSize"
      :input="config.input"
      :disabled="typeof config.readonly === 'function' ? config.readonly(state.value, row, config) : config.readonly"
      @change="config.onChange"
    /> -->
    <el-color-picker
      v-else-if="config.type === 'color-picker'"
      v-model="state.value"
      :size="computedSize"
      :disabled="
        typeof config.readonly === 'function'
          ? config.readonly(state.value, row, config)
          : config.readonly
      "
      style="width: 100%"
      :class="{ 'no-border': config.noBorder }"
      :placeholder="
        (typeof config.placeholder === 'function'
          ? config.placeholder(state.value, row, config)
          : config.placeholder) || '请输入'
      "
      @change="config.onChange"
    />
    <Tag
      v-else-if="config.type === 'tag'"
      :color="config.color"
      :round="config.round"
      :value="state.value"
      :closeable="config.closeable"
      :disable-transitions="config.disableTransitions"
      :hit="config.hit"
      :size="config.size"
      :effect="config.effect"
      :tag-type="config.tagType"
      :row="row"
    >
      {{
        (config.formatter && config.formatter(state.value, row, config)) ||
        state.value
      }}
    </Tag>
    <UserSelector
      v-else-if="config.type === 'user-select'"
      v-model="state.value"
      :row="row"
      :size="computedSize"
      :config="config"
      :popper-class="popperClass || config.popperClass"
      @change="handleChange"
    ></UserSelector>
    <div
      v-else-if="config.type === 'component'"
      :style="
        typeof config.style === 'function'
          ? config.style(state.value, row, config)
          : config.style
      "
    >
      <component
        :is="config.component"
        v-model="state.value"
        :row="row"
        :config="config"
        @change="handleChange"
      >
      </component>
    </div>
    <DepartmentUser
      v-else-if="config.type === 'department-user'"
      v-model="state.value"
      :row="row"
      :config="config"
      @change="handleChange"
    >
    </DepartmentUser>
    <SLCodeMirror
      v-else-if="config.type === 'code-mirror'"
      v-model="state.value"
      style="height: 500px"
      :language="config.language || 'sql'"
      :type="config.valueType || 'string'"
    ></SLCodeMirror>
  </slot>
  <!-- 出于初始化加载性能考虑，请通过slot的方式进行使用，v-model绑定值需要自行定义
    在表单中使用时，需要手动根据change事件修改表单字段的值
  -->
  <!-- <div
    v-else-if="config.type === 'form-map'"
    class="amap-wrapper"
  >
    <FormMap
      v-model="state.value"
      :row="row"
      :show-input="config.showInput"
      :disabled="config.disabled"
      :readonly="config.readonly"
      @change="handleChange"
    ></FormMap>
  </div> -->
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import detector from 'element-resize-detector';
import { useAppStore } from '@/store';
import { deresolveValue, resolveValue } from './isValid';
import {
  handleHighlight,
  handleClick,
  handleZRClick,
  onChange,
  nodeClick
} from '@/utils/FormHelper';
import DepartmentUser from './DepartmentUser.vue';
import { IECharts } from '@/plugins/echart';

const appStore = useAppStore();
const props = defineProps<{
  modelValue?: any;
  row?: Record<string, any>;
  size?: ISize;
  config: IFormItem;
  width?: string;
  popperClass?: string;
}>();
const emit = defineEmits([
  'update:model-value',
  'change',
  'blur',
  'clear',
  'focus'
]);
const computedSize = computed(() => {
  return props.config.size || props.size || 'default';
});
const state = reactive<{
  value: any;
  prependVal?: any;
  appendVal?: any;
  imgActionUrl: string;
  fileActionUrl: string;
  defaultProp: Record<string, string>;
}>({
  value: resolveValue(props.config, props.modelValue),
  imgActionUrl: appStore.actionUrl + 'file/api/upload/image',
  fileActionUrl: appStore.actionUrl + 'file/api/upload/file',
  prependVal: props.config.prependDefault,
  appendVal: props.config.appendDefault,
  defaultProp: {
    label: 'label',
    children: 'children',
    disabled: 'disabled',
    isLeaf: 'isLeaf',
    class: 'class',
    value: 'value'
  }
});
const refChartDiv = ref<HTMLDivElement>();
const refChart = ref<IECharts>();
const handleChange = (val: any, ...args: any[]) => {
  emit('update:model-value', deresolveValue(props.config, val));
  emit('change', val, props.row, props.config, ...args);
  // props.config.onChange && props.config.onChange(val, props.row, props.config, state.options)
};
const setoptionval = computed(() => {
  if (props.config.type === 'select') {
    if (!props.row || !props.config.setOptionBy) return '';
    return props.row[props.config.setOptionBy];
  }
  return '';
});
watch(
  () => setoptionval.value,
  async () => {
    if (
      (props.config.type === 'select' ||
        props.config.type === 'select-tree' ||
        props.config.type === 'cascader') &&
      props.config.setOptionBy
    ) {
      // state.value = undefined
      if (props.config.setOptionMethod) {
        props.config.setOptionMethod(props.config, props.row);
        // state.options = res
      } else {
        // state.options = []
      }
    }
  },
  { immediate: true }
);
const getDateOptions = (date: any) => {
  const item = props.config as IFormDate | IFormDateRange;
  if (item.disabledDate) {
    return item.disabledDate(date);
  }
  // 2021.07.17设置时间可选范围
  if ((!item.min && !item.max) || !date) {
    return false;
  }
  if (item.min && item.min.indexOf(' ') === -1) {
    // 不设置时分秒，后面会自动加上 08:00
    item.min += ' 00:00:000';
  }
  return compareDate(date, item.min) || !compareDate(date, item.max);
};
const filterNodeMethod = (value, data) => data.label?.includes(value);
const compareDate = (date1: any, date2: any) => {
  if (!date2) {
    return true;
  }
  return (
    date1.valueOf() <
    (typeof date2 === 'number' ? date2 : new Date(date2).valueOf())
  );
};
watch(
  () => state.value,
  (val: string) => {
    emit('update:model-value', val);
    // emit('change', val, props.row, props.config,state.options)
    // props.config.onChange && props.config.onChange(val, props.row, props.config,state.options)
  },
  {
    deep: true
  }
);
watch(
  () => props.modelValue,
  (val) => {
    const newVal = resolveValue(props.config, val);
    state.value = newVal;
    // if (val !== newVal) {
    // }
  },
  {
    immediate: true
  }
);

// 年份区间相关

const pickerStartAuditYear = (value) => {
  let flag = (props.config as IFormDateRange)?.disabledDate?.(value);
  if (flag === undefined || flag === false) {
    flag = value.getFullYear() > parseInt(state.value[1]);
  }
  return flag;
  // state.value[1] = null  开启开始年份清空结束年份
};

const pickerEndAuditYear = (value) => {
  let flag = (props.config as IFormDateRange)?.disabledDate?.(value);
  if (flag === undefined || flag === false) {
    flag = value.getFullYear() < parseInt(state.value[0]);
  }
  return flag;
};
const resizer = detector();
onMounted(() => {
  if (props.config.type === 'vchart') {
    refChartDiv.value && resizer.listenTo(refChartDiv.value, resize);
  }
  props.config.autoFillOptions?.(props.config as any);
});
const resize = () => {
  console.log('resized');
  refChart.value?.resize();
};
defineExpose({
  resize
});
</script>

<style lang="scss" scoped>
.amap-wrapper {
  width: 100%;
  height: 250px;
}

.readonly-input {
  color: var(--el-text-color-primary);
}

.unit {
  font-style: normal;
  font-size: 14px;
  margin-right: 10px;
}

.btn-wrapper {
  width: 100%;
  display: flex;
}

:deep(.el-input--small) {
  .el-input__inner {
    height: 24px !important;
  }
}

.el-radio-button {
  :deep(.el-radio-button__inner) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
}

.no-border {
  :deep(.el-input__wrapper) {
    box-shadow: none;
  }
}

:deep(.el-date-editor) {
  .el-input__wrapper {
    widows: 100%;
  }
}
.el-input__wrapper {
  width: 100%;
}
.btns {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .btn {
    height: 5px;
  }
}
</style>

<style lang="scss">
.el-input__wrapper {
  width: 100%;
}
/* 垃圾代码，用于解决elemen-plus升级后el-select宽度问题 */
.el-select__input {
  width: 100% !important;
}
</style>
