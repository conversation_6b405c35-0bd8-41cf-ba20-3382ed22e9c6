package org.thingsboard.server.dao.construction;

import org.springframework.beans.factory.annotation.Autowired;
import org.thingsboard.server.dao.construction.project.SoProjectOperateRecordService;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionRelatedEntity;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionTaskInfoSaveRequest;

public abstract class BasicSoConstructionTaskDriveService<T extends SoConstructionRelatedEntity> implements SoConstructionTaskDriveService<T>  {
    @Autowired
    protected SoProjectOperateRecordService recordService;

    @Autowired
    protected SoConstructionTaskInfoService taskInfoService;

    // 子项为多个时的检查策略
    protected final int commonSave(SaveRequest<T> entity, T e) {
        int count = getDirectMapper().insert(e);
        if (count > 0 &&
            taskInfoService.save(SoConstructionTaskInfoSaveRequest.of(entity, getCurrentScope(), e.getConstructionCode())) != null) {
            recordService.recordCreate(entity, e.getConstructionCode(), getCurrentJournalType());
        }
        return count;
    }

}
