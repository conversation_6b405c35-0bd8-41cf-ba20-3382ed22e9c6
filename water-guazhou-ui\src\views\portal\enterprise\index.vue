<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="treeData" />
    </template>
    <EnterpriseDetails :config="enterprisevalue"></EnterpriseDetails>
    <AddOrUpdateDialog
      v-if="projectDialog.visible"
      dialog-width="560px"
      :config="projectDialog"
      @refresh-data="refreshTree"
    />
  </TreeBox>
</template>

<script lang="ts" setup>
import EnterpriseDetails from '../components/card.vue'
import { getTenants, deleteTenants } from '@/api/tenant'
import { getAppList } from '@/api/application'
import AddOrUpdateDialog from './addOrUpdateDialog/index.vue'

import TreeBox from '@/views/layout/treeOrDetailFrame/TreeBox.vue'
import { SLConfirm, SLMessage } from '@/utils/Message'

const state = reactive<{
  totalLoading: boolean
}>({
  totalLoading: false
})
const treeData = reactive<SLTreeConfig>({
  title: '区域划分',
  data: [],
  loading: false,
  isFilterTree: true,
  currentProject: {},
  addRoot: {
    text: '新建项目',
    perm: true,
    click: (data: any) => handleAdd(data)
  },
  edit: {
    text: '编辑',
    perm: true,
    click: data => {
      handleAdd(data, 'edit')
    }
  },
  delete: {
    text: '删除',
    perm: true,
    click: data => {
      SLConfirm('确定删除？', '删除提示')
        .then(() => {
          deleteTenants(data?.id.id)
            .then(() => {
              SLMessage.success('操作成功')
              refreshTree()
            })
            .catch(() => SLMessage.error('删除失败'))
        })
        .catch(() => {
          //
        })
    }
  },
  // btnPerms: {
  //   addBtn: true,
  //   editBtn: true,
  //   delBtn: true
  // },
  // allowCreate: true,
  // allowNew: true,
  // clickAddOrEdit: (node, current) => {
  //   projectDialog.externalParams = {}
  //   if (current === 'edit') {
  //     if (node.additionalInfo) {
  //       // 2022-01-26 修复additional解析出错的bug
  //       let additionalInfo: any = null
  //       try {
  //         additionalInfo = JSON.parse(node.additionalInfo)
  //       } catch (error) {
  //         additionalInfo = node.additionalInfo
  //       }
  //       projectDialog.defaultValue = {
  //         ...node,
  //         latd: node.latd * 1,
  //         lgtd: node.lgtd * 1,
  //         ...additionalInfo
  //       }
  //     }
  //     projectDialog.title = '编辑应用'
  //   } else {
  //     projectDialog.title = '新建应用'
  //     projectDialog.defaultValue = { location: [116.4, 39.91] }
  //     if (node) {
  //       projectDialog.defaultValue = { location: [116.4, 39.91] }
  //       projectDialog.externalParams = { parentId: node.id }
  //     }
  //   }
  //   projectDialog.visible = true
  // },
  expandNodeId: [],
  defaultProps: {
    children: 'children',
    label: 'name'
  },
  //   allowAdd: true,
  // allowEdit: true,
  // allowDelete: true,
  // projectDelete(id: any) {
  //   deleteTenants(id.id).then(() => {
  //     SLMessage.success('操作成功')
  //     refreshTree()
  //   })
  // },
  treeNodeHandleClick: (data: any) => {
    // 设置当前选中企业的应用
    enterprisevalue.value = data
    treeData.currentProject = data
  }
})
const handleAdd = (data?: any, current?: string) => {
  const node = data?.data
  projectDialog.externalParams = {}
  if (current === 'edit') {
    if (node?.additionalInfo) {
      // 2022-01-26 修复additional解析出错的bug
      let additionalInfo: any = null
      try {
        additionalInfo = JSON.parse(node.additionalInfo)
      } catch (error) {
        additionalInfo = node.additionalInfo
      }
      projectDialog.defaultValue = {
        ...node,
        latd: node.latd * 1,
        lgtd: node.lgtd * 1,
        ...additionalInfo
      }
    }
    projectDialog.title = '编辑应用'
  } else {
    projectDialog.title = '新建应用'
    projectDialog.defaultValue = { location: [116.4, 39.91] }
    if (node) {
      projectDialog.defaultValue = { location: [116.4, 39.91] }
      projectDialog.externalParams = { parentId: node.id }
    }
  }
  projectDialog.visible = true
}
const verification = reactive({
  validatePhone: (rule, value, callback) => {
    // const valid = /(13\d|14[579]|15[^4\D]|17[^49\D]|18\d)\d{8}/g
    const valid = /^1\d{10}$/
    if (value === '' || value === null) {
      callback()
    } else if (valid.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确手机号'))
    }
  },
  postcode: (rule, value, callback) => {
    const valid = /^[1-9][0-9]{5}$/
    if (value === '' || value === null) {
      callback()
    } else if (valid.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确邮政编码'))
    }
  }
})

const projectDialog = reactive({
  visible: false,
  title: '新建应用',
  close: () => {
    projectDialog.visible = false
  },
  addUrl: 'api/tenant',
  editUrl: 'api/tenant',
  defaultValue: {},
  externalParams: {},
  columns: [
    {
      type: 'input',
      label: '企业名称',
      key: 'title',
      rules: [{ required: true, message: '请填写企业名称' }]
    },
    {
      type: 'input',
      label: '邮箱',
      key: 'email',
      rules: [{ type: 'email', message: '请输入正确邮箱地址', trigger: 'blur' }]
    },
    {
      type: 'select',
      label: '应用',
      key: 'appTypeId',
      options: [],
      rules: [{ required: true, message: '请选择应用', trigger: 'change' }]
    },
    {
      type: 'input',
      label: '联系手机',
      key: 'phone',
      rules: [{ validator: verification.validatePhone, trigger: 'blur' }]
    },
    {
      type: 'input',
      label: '国家',
      key: 'country',
      rules: [{ max: 20, message: '输入不可超过20位', trigger: 'blur' }]
    },
    {
      type: 'input',
      label: '省',
      key: 'state',
      rules: [{ max: 20, message: '输入不可超过20位', trigger: 'blur' }]
    },
    {
      type: 'input',
      label: '城市',
      key: 'city',
      rules: [{ max: 20, message: '输入不可超过20位', trigger: 'blur' }]
    },
    {
      type: 'input',
      label: '地址',
      key: 'address',
      rules: [
        { required: true, message: '请输入企业地址', trigger: 'blur' },
        { max: 40, message: '输入不可超过40位', trigger: 'blur' }
      ]
    },
    {
      type: 'input-number',
      label: '经度',
      key: 'lgtd',
      rules: [{ required: true, message: '请输入企业经度', trigger: 'blur' }]
    },
    {
      type: 'input-number',
      label: '纬度',
      key: 'latd',
      rules: [{ required: true, message: '请输入企业纬度', trigger: 'blur' }]
    },
    {
      type: 'input',
      label: '邮编',
      key: 'zip',
      rules: [
        { validator: verification.postcode, trigger: 'blur' },
        { max: 20, message: '名称不超过20位', trigger: 'blur' }
      ]
    },
    {
      type: 'input',
      label: '地区',
      key: 'region',
      rules: [{ max: 40, message: '输入不可超过40位', trigger: 'blur' }]
    },
    {
      type: 'input',
      label: '平台名称',
      key: 'platformName',
      aInfo: true,
      rules: [{ max: 40, message: '输入不可超过40位', trigger: 'blur' }]
    },
    {
      type: 'textarea',
      label: '企业简介',
      key: 'companyProfiles',
      aInfo: true,
      rules: [{ max: 200, message: '输入不可超过200位', trigger: 'blur' }]
    },
    {
      type: 'input',
      label: 'App标题',
      key: 'apptitle',
      aInfo: true,
      rules: [{ max: 40, message: '输入不可超过200位', trigger: 'blur' }]
    }
  ]
})

const refreshTree = () => {
  getTenants()
    .then(res => {
      console.log(res)

      if (res.data) {
        treeData.data = res.data.data
        const fTData = treeData.data

        enterprisevalue.value = fTData[0]
        state.totalLoading = false
      } else {
        SLMessage.info('暂无企业 不可操作，请创建企业')
        state.totalLoading = false
      }
    })
    .catch(err => {
      console.log(err)
      SLMessage.info('暂无企业 不可操作，请创建企业')
      state.totalLoading = false
    })
}

onBeforeUnmount(() => {
  // refreshInterval.value && clearInterval(refreshInterval.value)
  console.log('dataMonitoring is Unmounted')
})

const enterprisevalue = reactive({
  value: {}
})

onMounted(() => {
  refreshTree()
  getAppList().then(res => {
    console.log(res.data)
    projectDialog.columns[2].options = res.data.map(item => ({
      label: item.appName,
      value: item.id
    }))
  })
})
</script>

<style lang="scss" scoped></style>
