import{d as c,g as r,h as p,F as i,G as _,i as f,al as l,J as d,C as u}from"./index-r0dFAfgr.js";/* empty css                                                                      */const m=c({__name:"UserLocatePop",props:{visible:{type:Boolean},config:{}},setup(n){const t=n,s=()=>{var e,o;(o=(e=t.config).extentTo)==null||o.call(e,t.config.userId)};return(e,o)=>{const a=d;return r(),p(a,{icon:f(l),onClick:s},{default:i(()=>o[0]||(o[0]=[_(" 缩放至 ")])),_:1},8,["icon"])}}}),B=u(m,[["__scopeId","data-v-74daa5f2"]]);export{B as default};
