package org.thingsboard.server.dao.sql.deviceDump;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.deviceDump.DeviceDump;
import org.thingsboard.server.dao.util.imodel.query.deviceDump.DeviceDumpPageRequest;

import java.util.List;

@Mapper
public interface DeviceDumpMapper extends BaseMapper<DeviceDump> {
    IPage<DeviceDump> findByPage(DeviceDumpPageRequest request);

    boolean update(DeviceDump entity);

    int dump(String id);

    List<String> getChildrenId(String id);

    boolean isUserHandleUser(@Param("id") String id, @Param("userId") String userId);

}
