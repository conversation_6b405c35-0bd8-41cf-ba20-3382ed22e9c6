// 智慧生产=二供管理-报表统计 api
import request from '@/plugins/axios';

// 查询单个二供泵房的水量报表
export function getWaterSupplyReport(params?: {
  stationId: any;
  queryType: any;
  start: number;
  end: number;
}) {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyReport',
    method: 'get',
    params
  });
}

// 查询单个二供泵房的单耗报表
export function getWaterSupplyConsumptionReport(params?: {
  stationId: any;
  queryType: any;
  start: number;
  end: number;
}) {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyConsumptionReport',
    method: 'get',
    params
  });
}

// 查询单个二供泵房的能耗报表
export function getEnergyMethodReport(params?: {
  stationId: any;
  queryType: any;
  start: number;
  end: number;
}) {
  return request({
    url: '/istar/api/boosterPumpStation/getEnergyMethodReport',
    method: 'get',
    params
  });
}

// 泵站分析

// 查询所有二供泵房的电耗数据（本期及上期供水量、耗电量、单耗分析）
export function getWaterSupplyAndEnergyData(params?: {
  stationId: any;
  queryType: any;
  start: number;
  end: number;
}) {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyAndEnergyData',
    method: 'get',
    params
  });
}

// 查询单个二供泵房的耗电量数据（本期上期供水量耗电量曲线、本期上期吨水电耗曲线）
export function getWaterSupplyAndEnergyDataDetail(params?: {
  stationId: any;
  queryType: any;
  start: number;
  end: number;
}) {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyAndEnergyDataDetail',
    method: 'get',
    params
  });
}
