package org.thingsboard.server.utils.imodel.annotations;

import org.springframework.context.annotation.Import;
import org.thingsboard.server.utils.imodel.aop.ControllerIntercept;
import org.thingsboard.server.utils.imodel.aop.EntityValidator;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Documented
@Import({EntityValidator.class, ControllerIntercept.class})
public @interface EnableIModel {
}
