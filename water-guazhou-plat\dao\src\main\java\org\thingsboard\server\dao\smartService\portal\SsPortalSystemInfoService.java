package org.thingsboard.server.dao.smartService.portal;

import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSystemInfo;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalSystemInfoSaveRequest;

public interface SsPortalSystemInfoService {
    SsPortalSystemInfo get(String tenantId);

    SsPortalSystemInfo save(SsPortalSystemInfoSaveRequest entity);

    boolean update(SsPortalSystemInfo entity);

    boolean delete(String id);

}
