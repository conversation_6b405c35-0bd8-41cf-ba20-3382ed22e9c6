package org.thingsboard.server.dao.model.sql.smartService.duty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 班次管理
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-14
 */
@TableName("tb_service_duty_shift")
@Data
public class DutyShift {

    @TableId
    private String id;

    private String code;

    private String name;

    private Date startTime;

    private Date endTime;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

}
