import { useSketch } from '@/hooks/arcgis'

import {
  createGraphic,
  excuteIdentify,
  getGraphicLayer,
  getLayerOids,
  getNeerestPoint,
  getSubLayerIds,
  gotoAndHighLight,
  initIdentifyParams,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper'
import { SLMessage } from '@/utils/Message'
import {
  GetFieldConfig,
  GetFieldUniqueValue
} from '@/api/mapservice/fieldconfig'
import { queryLayerClassName } from '@/api/mapservice'

export const usePick = (formIns: any) => {
  const refForm = formIns
  let timer: any
  let moveEvent: any
  let clickEvent: any
  let identifyResult: any
  const identityParams = initIdentifyParams()
  let textLayer: __esri.GraphicsLayer | undefined
  let referLayer: __esri.GraphicsLayer | undefined
  let view: __esri.MapView | undefined
  const start = (mapView?: __esri.MapView) => {
    if (!mapView) return
    // clear()
    view = mapView
    referLayer = getGraphicLayer(view, {
      id: 'pipe-brush-referdev',
      title: '参考设备'
    })
    textLayer = getGraphicLayer(view, {
      id: 'pipe-brush-text',
      title: '捕捉标注'
    })
    setMapCursor('crosshair')
    moveEvent = view.on('pointer-move', e => {
      timer && clearTimeout(timer)
      textLayer?.removeAll()
      identifyResult = undefined
      timer = setTimeout(() => {
        const mapPoint = view?.toMap(e)
        _doIdentify(mapPoint, timer)
      }, 200)
    })
    clickEvent = view?.on('click', () => {
      if (!identifyResult) {
        SLMessage.warning('没有相关设备')
        return
      }
      identifyResult.feature.symbol = setSymbol(
        identifyResult.feature.geometry.type,
        {
          color: [255, 0, 255, 1],
          outlineColor: [255, 0, 0, 1],
          outlineWidth: 2
        }
      )
      referLayer?.removeAll()
      textLayer?.removeAll()
      setMapCursor('')
      referLayer?.add(identifyResult.feature)
      moveEvent?.remove()
      clickEvent?.remove()
      gotoAndHighLight(view, identifyResult.feature, {
        avoidHighlight: true
      })
    })
  }

  const _doIdentify = async (mapPoint?: __esri.Point, newtimer?: any) => {
    if (!view || !mapPoint) return
    try {
      identityParams.geometry = mapPoint
      identityParams.mapExtent = view.extent
      const layerId = refForm.value?.dataForm.layerid?.[0]
      if (layerId === undefined) return
      identityParams.layerIds = [layerId]
      const res = await excuteIdentify(
        window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
        identityParams
      )
      if (newtimer !== timer) return
      textLayer?.removeAll()

      identifyResult = res.results?.filter(item => item.layerId === layerId)[0]

      if (!identifyResult) return
      const point = getNeerestPoint(identifyResult.feature?.geometry, mapPoint)
      if (!point) return
      const textGraphic = createGraphic({
        geometry: point,
        symbol: setSymbol('text', {
          yOffset: 15,
          text:
            identifyResult.layerName
            + ':'
            + identifyResult.feature.attributes['新编号']
        })
      })
      textLayer?.add(textGraphic)
    } catch (error) {
      textLayer?.removeAll()
    }
  }
  const clear = () => {
    referLayer?.removeAll()
    textLayer?.removeAll()
    moveEvent?.remove()
    clickEvent?.remove()
    timer && clearTimeout(timer)
    setMapCursor('')
    identifyResult = undefined
  }
  const getIdentifyResult = () => {
    return identifyResult
  }
  const destroy = () => {
    clear()
    textLayer && view?.map.remove(textLayer)
    referLayer && view?.map.remove(referLayer)
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    start,
    clear,
    destroy,
    getIdentifyResult
  }
}

export const useDraw = () => {
  let sketch: __esri.SketchViewModel | undefined
  let graphic: __esri.Graphic | undefined
  let graphicsLayer: __esri.GraphicsLayer | undefined
  const { initSketch, destroySketch } = useSketch()
  let view: __esri.MapView | undefined
  const init = (mapView?: __esri.MapView) => {
    if (!mapView) return
    view = mapView
    graphicsLayer = getGraphicLayer(view, {
      id: 'pipe-brush',
      title: '范围'
    })
    sketch = initSketch(view, graphicsLayer, {
      createCallBack: resolveDrawEnd,
      updateCallBack: resolveDrawEnd
    })
  }
  const create = (type: 'polygon' | 'point' | 'rectangle' | 'circle') => {
    graphicsLayer?.removeAll()
    graphic = undefined
    sketch?.create(type)
  }
  const resolveDrawEnd = (result: ISketchHandlerParameter) => {
    graphic = result.graphics[0]
  }
  const destroy = () => {
    destroySketch()
    graphicsLayer && view?.map.remove(graphicsLayer)
  }
  const clear = () => {
    graphicsLayer?.removeAll()
    graphic = undefined
    sketch?.cancel()
  }
  const getGraphic = () => {
    return graphic
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init,
    destroy,
    getGraphic,
    create,
    clear
  }
}

export const useForm = (
  formIns: any,
  queryCallBack: () => void,
  layerChange: () => void
) => {
  const refForm = formIns
  const state: {
    curLayerFields: any[]
    curFieldNode?: any
    layerInfos: any[]
    layerIds: any[]
    tabs: any[]
  } = {
    curLayerFields: [],
    layerInfos: [],
    layerIds: [],
    tabs: []
  }
  const loading = ref<boolean>(false)
  const uniqueing = ref<boolean>(false)
  const curLayerName = ref<string>('')
  const pick = usePick(formIns)
  const draw = useDraw()
  let view: __esri.MapView | undefined
  const init = (mapViwe?: __esri.MapView) => {
    if (!mapViwe) return
    view = mapViwe
    draw.init(view)
    getLayerInfo(view)
  }

  const getLayerInfo = async (view?: __esri.MapView) => {
    state.layerIds = getSubLayerIds(view)
    const layerInfo = await queryLayerClassName(state.layerIds)
    state.layerInfos = layerInfo.data?.result?.rows || []
    const field = FormConfig.value.group.find(item => item.id === 'layer')
      ?.fields[0] as IFormTree
    const points = state.layerInfos
      .filter(item => item.geometrytype === 'esriGeometryPoint')
      .map(item => {
        return {
          label: item.layername,
          value: item.layerid,
          data: item
        }
      })
    const lines = state.layerInfos
      .filter(item => item.geometrytype === 'esriGeometryPolyline')
      .map(item => {
        return {
          label: item.layername,
          value: item.layerid,
          data: item
        }
      })
    field
      && (field.options = [
        { label: '管点类', value: -1, disabled: true, children: points },
        { label: '管线类', value: -2, disabled: true, children: lines }
      ])
    refForm.value && (refForm.value.dataForm.layerid = [lines[0].value])
  }

  const FormConfig = ref<IFormConfig>({
    group: [
      {
        id: 'layer',
        fieldset: {
          desc: '选择刷新图层'
        },
        fields: [
          {
            type: 'tree',
            options: [],
            checkStrictly: true,
            showCheckbox: true,
            field: 'layerid',
            nodeKey: 'value',
            handleCheckChange: (data, isChecked) => {
              if (isChecked) {
                refForm.value && (refForm.value.dataForm.layerid = [data.value])
                clear()
                draw.clear()
                pick.clear()
                state.tabs = []
                layerChange()
              }
            }
          }
        ]
      },
      {
        fieldset: {
          desc: '选择参考设备'
        },
        fields: [
          {
            type: 'btn-group',
            btns: [
              {
                perm: true,
                text: '点击地图选择',
                iconifyIcon: 'gis:arrow-o',
                click: () => {
                  pick.start(view)
                  state.tabs = []
                  layerChange()
                }
              }
            ]
          }
        ]
      },
      {
        fieldset: {
          desc: '绘制工具'
        },
        fields: [
          {
            type: 'btn-group',
            btns: [
              {
                perm: true,
                text: '',
                type: 'default',
                size: 'large',
                title: '绘制多边形',
                disabled: () => loading.value,
                iconifyIcon: 'mdi:shape-polygon-plus',
                click: () => draw.create('polygon')
              },
              {
                perm: true,
                text: '',
                type: 'default',
                size: 'large',
                title: '绘制矩形',
                disabled: () => loading.value,
                iconifyIcon: 'ep:crop',
                click: () => draw.create('rectangle')
              },
              {
                perm: true,
                text: '',
                type: 'default',
                size: 'large',
                title: '绘制椭圆',
                disabled: () => loading.value,
                iconifyIcon: 'mdi:ellipse-outline',
                click: () => draw.create('circle')
              },
              {
                perm: true,
                text: '',
                type: 'default',
                size: 'large',
                title: '清除图形',
                disabled: () => loading.value,
                iconifyIcon: 'ep:delete',
                click: () => draw.clear()
              }
            ]
          }
        ]
      },
      {
        id: 'layer',
        fieldset: {
          desc: '图层字段'
        },
        fields: [
          {
            type: 'list',
            data: [],
            className: 'sql-list-wrapper',
            setData: async (config: IFormList, row) => {
              if (!row.layerid?.length) {
                state.curLayerFields.length = 0
                return
              }
              const layerid = row.layerid[0]
              const layerName = state.layerInfos.find(
                item => item.layerid === layerid
              )?.layername
              if (!layerName) return
              const fields = await GetFieldConfig(layerName)
              config.data = fields.data?.result?.rows
              state.curLayerFields = fields.data?.result?.rows || []
              curLayerName.value = state.layerInfos.find(
                item => item.layerid === row.layerid[0]
              )?.layername
            },
            setDataBy: 'layerid',
            displayField: 'alias',
            valueField: 'name',
            highlightCurrentRow: true,
            nodeClick: node => {
              state.curFieldNode = node
              appendSQL(node.name)
            }
          }
        ]
      },
      {
        id: 'field-construct',
        fieldset: {
          desc: '构建查询语句'
        },
        fields: [
          {
            type: 'btn-group',
            size: 'small',
            style: {
              width: '40%',
              display: 'flex',
              flexWrap: 'wrap'
            },
            className: 'sql-btns-wrapper',
            btns: [
              {
                perm: true,
                text: '=',
                styles: {
                  margin: '6px',
                  width: '50px'
                },
                click: () => {
                  appendSQL('=')
                }
              },
              {
                perm: true,
                text: '模糊',
                styles: {
                  margin: '6px',
                  width: '50px'
                },
                click: () => {
                  appendSQL("like '%替换此处%'")
                }
              },
              {
                perm: true,
                text: '>',
                styles: {
                  margin: '6px',
                  width: '50px'
                },
                click: () => {
                  appendSQL('>')
                }
              },
              {
                perm: true,
                text: '<',
                styles: {
                  margin: '6px',
                  width: '50px'
                },
                click: () => {
                  appendSQL('<')
                }
              },
              {
                perm: true,
                text: '非',
                styles: {
                  margin: '6px',
                  width: '50px'
                },
                click: () => {
                  appendSQL('<>')
                }
              },
              {
                perm: true,
                text: '并且',
                styles: {
                  margin: '6px',
                  width: '50px'
                },
                click: () => {
                  appendSQL('and')
                }
              },
              {
                perm: true,
                text: '或者',
                styles: {
                  margin: '6px',
                  width: '50px'
                },
                click: () => {
                  appendSQL('or')
                }
              },
              {
                perm: true,
                text: '%',
                styles: {
                  margin: '6px',
                  width: '50px'
                },
                click: () => {
                  appendSQL('%')
                }
              }
            ],
            extraFormItem: [
              {
                type: 'list',
                wrapperStyle: {
                  width: '60%',
                  height: '144px'
                },
                className: 'sql-list-wrapper',
                field: 'uniqueValue',
                data: [],
                nodeClick: node => {
                  appendSQL("'" + node + "'")
                },
                filters: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        perm: true,
                        text: () => (uniqueing.value ? '正在获取唯一值' : '获取唯一值'),
                        loading: () => uniqueing.value,
                        disabled: () => loading.value,
                        styles: {
                          width: '100%',
                          borderRadius: '0'
                        },
                        click: () => getUniqueValue()
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        fieldset: {
          desc: '组合查询条件'
        },
        fields: [
          {
            type: 'textarea',
            field: 'sql',
            placeholder: 'OBJECTID > 0'
          },
          {
            type: 'btn-group',
            itemContainerStyle: {
              marginBottom: '8px'
            },
            btns: [
              {
                perm: true,
                text: '清除组合条件',
                type: 'danger',
                disabled: () => loading.value,
                click: () => clear(),
                styles: {
                  width: '100%'
                }
              }
            ]
          }
        ]
      },
      {
        fields: [
          {
            type: 'btn-group',
            btns: [
              {
                perm: true,
                text: '查询',
                styles: {
                  width: '100%'
                },
                click: () => startQuery()
              },
              {
                perm: true,
                text: '重置',
                type: 'default',
                styles: {
                  width: '100%'
                },
                click: () => resetForm()
              }
            ]
          }
        ]
      }
    ],
    labelPosition: 'top',
    gutter: 12,
    defaultValue: {
      length: 1
    }
  })
  const resetForm = () => {
    refForm.value?.resetForm()
  }
  const clear = () => {
    refForm.value?.dataForm && (refForm.value.dataForm.sql = '')
  }
  const appendSQL = val => {
    if (!refForm.value) return
    if (!refForm.value?.dataForm) refForm.value.dataForm = {}
    const sql = refForm.value.dataForm.sql || ' '
    refForm.value.dataForm.sql = sql + val + ' '
  }

  const getUniqueValue = async () => {
    if (!state.curFieldNode) return
    const layerid = refForm.value?.dataForm.layerid
    if (!layerid?.length) {
      SLMessage.warning('请先选择一个图层')
      return
    }
    uniqueing.value = true
    try {
      const res = await GetFieldUniqueValue({
        layerid: layerid[0],
        field_name: state.curFieldNode.name
      })
      const extraFormItem = FormConfig.value.group.find(
        item => item.id === 'field-construct'
      )?.fields[0].extraFormItem
      const field = extraFormItem && (extraFormItem[0] as IFormList)
      field && (field.data = res.data.result.rows)
    } catch (error) {
      SLMessage.error('获取唯一值失败')
    }
    uniqueing.value = false
  }

  const startQuery = async () => {
    const layerIds = refForm.value?.dataForm.layerid || []
    const queryParams = {
      where: refForm.value?.dataForm.sql || '1=1',
      geometry: draw.getGraphic()?.geometry
    }
    state.tabs = await getLayerOids(layerIds, state.layerInfos, queryParams)
    queryCallBack()
  }
  return {
    init,
    loading,
    curLayerName,
    state,
    FormConfig,
    startQuery,
    draw,
    pick
  }
}
