package org.thingsboard.server.dao.smartProduction.sludge;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.SludgeRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.sludge.Sludge;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-26
 */
public interface SludgeService {

    Sludge save(Sludge sludge);

    PageData<Sludge> getList(SludgeRequest request);

    JSONObject analysis(String year, String tenantId);

    void delete(List<String> ids);

}
