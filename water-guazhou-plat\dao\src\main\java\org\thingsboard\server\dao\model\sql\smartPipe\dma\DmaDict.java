package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * DMA分区设置
 */
@Data
@TableName("tb_pipe_dma_dict")
public class DmaDict {
    private String id;

    private String code;

    private String type;

    private String label;

    private String value;

    private Integer orderNum;

    private Date createTime;

    private String tenantId;


}
