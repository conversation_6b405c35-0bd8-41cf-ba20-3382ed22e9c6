import{S as t,d as m}from"./scrim-Eo5BG2Ie.js";import"./widget-BcWKanF2.js";import"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./t9n-B2bWcUZc.js";import"./key-7hamXU9f.js";import"./observers-D10wq1Ib.js";import"./loader-DYvscnHN.js";import"./guid-DO7TRjsS.js";/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const f=t,l=m;export{f as CalciteScrim,l as defineCustomElement};
