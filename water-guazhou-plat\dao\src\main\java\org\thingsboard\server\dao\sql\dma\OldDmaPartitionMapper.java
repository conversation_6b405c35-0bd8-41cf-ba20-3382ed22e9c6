package org.thingsboard.server.dao.sql.dma;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.dma.OldDmaPartitionEntity;

import java.util.List;
import java.util.Map;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface OldDmaPartitionMapper extends BaseMapper<OldDmaPartitionEntity> {

    List<Map> getRootIdNameMapList(@Param("tenantId") String tenantId);

    List<OldDmaPartitionEntity> getRootIdNameList(@Param("tenantId") String tenantId);

    List<OldDmaPartitionEntity> getAllIdNameByPid(@Param("pid") String pid);

    List<Map> getIdNameById(String partitionId, String tenantId);

}
