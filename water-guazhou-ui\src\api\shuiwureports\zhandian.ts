import { request } from '@/plugins/axios';

/**
 * 查询站点列表
 * @param params
 * @returns
 */
export const GetStationList = (params: {
  page: number;
  size: number;
  type?: string;
  projectId?: string;
}) =>
  request({
    url: '/api/station/list',
    method: 'get',
    params
  });
/**
 * 查询指定类型的站点的近期数据列表
 * @param params
 * @returns
 */
export const GetStationRecentDataByType = (params: {
  stationType: string;
  projectId: string;
}) =>
  request({
    url: '/istar/api/station/data/detailList',
    method: 'get',
    params
  });

/**
 * 查询指定站点所有的动态属性列表
 * @param params
 * @returns
 */
export const GetAllStationAttrs = (params: { stationId: string }) =>
  request({
    url: '/api/station/stationAttrList',
    method: 'get',
    params
  });
/**
 * 查询站点分组动态属性列表
 * @param params
 * @returns
 */
export const GetStationAttrs = (params: { stationId: string }) =>
  request({
    url: '/api/station/allAttrList',
    method: 'get',
    params
  });
/**
 * 查询站点明细
 * @param id
 * @returns
 */
export const GetStationDetail = (id: string) =>
  request({
    url: `/api/station/${id}`,
    method: 'get'
  });
/**
 * 查询站点的实时数据
 * @param stationId
 * @param type 属性分组名
 * @returns
 */
export const GetStationRealTimeDetail = (stationId: string, type?: string) =>
  request({
    url: `/istar/api/station/data/detail/${stationId}`,
    method: 'get',
    params: {
      type: type || ''
    }
  });
/**
 * 查询站点的属性分组列表
 * @param params
 * @returns
 */
export const GetStationAttrGroupNames = (params: { stationId: string }) => {
  return request({
    url: '/api/station/attrGroupNames',
    method: 'get',
    params
  });
};
/**
 * 查询站点的告警列表
 * @param params
 * @returns
 */
export const GetStationAlarmList = (
  stationId: string,
  start?: any,
  end?: any
) =>
  request({
    url: `/api/alarm/station/${stationId}`,
    method: 'get',
    params: {
      start: start || '',
      end: end || ''
    }
  });
export const GetStationAlarmListV2 = (params: {
  stationId: string;
  page?: any;
  size?: any;
  start?: any;
  end?: any;
}) =>
  request({
    url: `/api/alarmV2/alarmCenter/list`,
    method: 'get',
    params
  });
// 站点树
export const GetStationTree = (stationType?: string) =>
  request({
    url: '/api/station/simpleTree',
    method: 'get',
    params: {
      type: stationType || null
    }
  });
/**
 * 新增/编辑站点
 * @param params
 * @returns
 */
export const PostStation = (params: {
  id?: string;
  name?: string;
  type?: string;
  address?: string;
  location?: string;
  additionalInfo?: string;
  remark?: string;
  imgs?: string;
  orderNum?: string;
  createTime?: string;
  projectId?: string;
  tenantId?: string;
  info?: string;
  stationAttrInfo?: {
    type?: string;
    attrList: {
      id?: string;
      stationId?: string;
      deviceId?: string;
      attr?: string;
      type?: string;
      name?: string;
      orderNum?: string;
      tenantId?: string;
    }[];
  }[];
}) =>
  request({
    url: '/api/station/save',
    method: 'post',
    data: params
  });
/**
 * 删除站点
 * @param ids
 * @returns
 */
export const DeleteStation = (ids: string[]) =>
  request({
    url: '/api/station/remove',
    method: 'delete',
    data: ids
  });

/**
 * 查询站点各类型数量统计列表
 * @param params
 * @returns
 */
export const GetStationTypeCount = (params?: {
  /**
   * 不传则查询全部，多个类型用逗号分割
   */
  types?: string;
  projectId?: string;
}) => {
  return request({
    url: '/api/station/typeCount',
    method: 'get',
    params
  });
};
/**
 * 查询报警规则列表
 * @param params
 * @returns
 */
export const getAlarmRules = (params: {
  page?: string | number;
  size?: string | number;
  name?: number;
}) => {
  return request({
    url: '/api/alarmV2/alarmRule/list',
    method: 'get',
    params
  });
};

// 查询指定报警规则
export const getAlarmRulesById = (id: string) => {
  return request({
    url: `/api/alarmV2/alarmRule/${id}`,
    method: 'get'
  });
};

/**
 * 保存报警规则
 * @param params
 * @returns
 */
export const postAlarmRules = (params: {
  id: string;
  stationAttrId: string;
  ruleType: string;
  status: string;
  alarmLevel: string;
  alarmType: string;
  processMethod: string;
  remoteStationAttrId: string;
  remoteVideoId: string;
}) => {
  return request({
    url: '/api/alarmV2/alarmRule/save',
    method: 'post',
    data: params
  });
};

/**
 * 删除报警规则
 * @param ids
 * @returns
 */
export const deleteAlarmRules = (data: string[]) =>
  request({
    url: '/api/alarmV2/alarmRule/remove',
    method: 'delete',
    data
  });

/**
 * 查询报警中心列表
 * @param params
 * @returns
 */
export const getAlarmCenter = (params: {
  page: number | string;
  size: number | string;
  alarmType?: string;
  alarmLevel?: string;
  processStatus?: string;
  alarmStatus?: string;
  stationId?: string;
  stationType?: string;
  attr?: string;
  startTime?: string | number;
  endTime?: string | number;
}) => {
  return request({
    url: '/api/alarmV2/alarmCenter/list',
    method: 'get',
    params
  });
};

/**
 * 报警解除告警
 * @param params
 * @returns
 */
export const postClearAlarm = (data: string[]) => {
  return request({
    url: `/api/alarmV2/alarmCenter/clearAlarm`,
    method: 'post',
    data
  });
};

/**
 * 报警分析
 * @param params
 * @returns
 */
export const getAlarmAnalysis = (params: { startTime: any; endTime: any }) => {
  return request({
    url: '/api/alarmV2/alarmCenter/report',
    method: 'get',
    params
  });
};

/**
 * 报警关联工单
 * @param params
 * @returns
 */
export const postAlarmWorkOrder = (alarmId: string, data: string[]) => {
  return request({
    url: `/api/alarmV2/alarmCenter/createWorkOrder/${alarmId}`,
    method: 'post',
    data
  });
};
/**
 * 查询列表
 * @param params
 * @returns
 */
export const GetAlarmRank = (params: {
  startTime: number;
  endTime: number;
}) => {
  return request({
    url: '/api/alarmV2/alarmCenter/rankByStation',
    method: 'get',
    params
  });
};

/**
 * 查询列表
 * @param params
 * @returns
 */
export const GetAlarmCountOfLevel = (params: {
  startTime: number;
  endTime: number;
}) => {
  return request({
    url: '/api/alarmV2/alarmCenter/countByLevel',
    method: 'get',
    params
  });
};
/**
 * 查询当前年份的月报警数据统计
 * @param params
 * @returns
 */
export const GetAlarmYearReport = () => {
  return request({
    url: '/api/alarmV2/alarmCenter/yearReport',
    method: 'get'
  });
};
/**
 * 查询指定站点所有属性的今日数据
 * @param params
 * @returns
 */
export const GetStationTodayDataList = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/station/data/todayDataList',
    method: 'get',
    params
  });
};

/**
 * 查询指定站点的指定属性的数据并按日进行分组
 * @param params
 * @returns
 */
export const getStationAttrDataQueryGroupByDay = (params: {
  stationId: string;
  start: string;
  end: string;
  filterStart: string;
  filterEnd: string;
  queryType: string;
  attributeId: string;
}) => {
  return request({
    url: '/istar/api/station/data/stationAttrDataQueryGroupByDay',
    method: 'get',
    params
  });
};

/**
 * 按站点查询报警排名
 * @param params
 * @returns
 */
export const GetAlarmRankByStation = (params: {
  startTime: string | number;
  endTime: string | number;
  stationType: string;
}) => {
  return request({
    url: '/api/alarmV2/alarmCenter/rankByStation',
    method: 'get',
    params
  });
};

// 锦州-报警导出
export const ExportAlarmReport = (params: {
  page: number | string;
  size: number | string;
  alarmType?: string;
  alarmLevel?: string;
  processStatus?: string;
  alarmStatus?: string;
  stationId?: string;
  stationType?: string;
}) => {
  return request({
    url: `/api/alarmV2/alarmCenter/list/export`,
    params,
    responseType: 'blob'
  });
};
