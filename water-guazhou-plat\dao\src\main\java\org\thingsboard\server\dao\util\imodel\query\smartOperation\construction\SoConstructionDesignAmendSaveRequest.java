package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesignAmend;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoConstructionDesignAmendSaveRequest extends SaveRequest<SoConstructionDesignAmend> {
    // 编号
    @NotNullOrEmpty
    private String code;

    // 所属设计编号
    @NotNullOrEmpty
    private String designCode;

    // 所属工程编号
    @NotNullOrEmpty
    private String constructionCode;

    // 变更类型
    private String type;

    // 说明
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    protected SoConstructionDesignAmend build() {
        SoConstructionDesignAmend entity = new SoConstructionDesignAmend();
        entity.setCode(code);
        entity.setDesignCode(designCode);
        entity.setConstructionCode(constructionCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionDesignAmend update(String id) {
        SoConstructionDesignAmend entity = new SoConstructionDesignAmend();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionDesignAmend entity) {
        entity.setType(type);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
    }
}