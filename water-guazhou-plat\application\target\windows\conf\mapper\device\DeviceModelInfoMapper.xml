<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.device.DeviceModelInfoMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.DeviceModelInfoEntity">
        select * from device_model_info
        <where>
            <if test="param.name != null and param.name != ''">
                and name like '%' || #{param.name} || '%'
            </if>
            <if test="param.brand != null and param.brand != ''">
                and brand like '%' || #{param.brand} || '%'
            </if>
            <if test="param.caliber != null and param.caliber != ''">
                and caliber = #{param.caliber}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="getByDeviceId" resultType="org.thingsboard.server.dao.model.sql.DeviceModelInfoEntity">
        select a.*
        from device_model_info a
        left join device b on a.id = b.hardware_id
                 where b.id = #{deviceId}
    </select>
</mapper>