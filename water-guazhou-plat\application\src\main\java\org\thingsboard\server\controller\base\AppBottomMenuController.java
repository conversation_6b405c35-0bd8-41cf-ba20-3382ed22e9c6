package org.thingsboard.server.controller.base;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.app.AppBottomMenuService;
import org.thingsboard.server.dao.model.sql.AppBottomMenu;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

@RestController
@RequestMapping("api/appBottomMenu")
public class AppBottomMenuController extends BaseController {

    @Autowired
    private AppBottomMenuService appBottomMenuService;

    @GetMapping("list")
    public IstarResponse findList(@RequestParam(required = false) String tenantId) throws ThingsboardException {
        TenantId tid = getTenantId();
        if (StringUtils.isNotBlank(tenantId)) {
            tid = new TenantId(UUIDConverter.fromString(tenantId));
        }
        return IstarResponse.ok(appBottomMenuService.findList(tid));
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody List<AppBottomMenu> appBottomMenuList) throws ThingsboardException {
        appBottomMenuService.save(appBottomMenuList, getTenantId());
        return IstarResponse.ok();
    }

}
