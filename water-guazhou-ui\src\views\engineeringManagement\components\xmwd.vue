<!-- 工程管理-详情-项目文档 -->
<template>
  <CardTable :config="TableConfig" class="card-table"></CardTable>
</template>

<script lang="ts" setup>
import { getProjectList } from '@/api/engineeringManagement/projectManagement';

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '文件名称', prop: 'code' },
    { label: '文件类型', prop: 'code' },
    { label: '文件大小', prop: 'code' },
    { label: '上传时间', prop: 'code' },
    { label: '上传人', prop: 'code' }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1
  };
  if (params?.time) {
    params.startTimeFrom = params.time[0];
    params.startTimeTo = params.time[1];
    delete params.time;
  }
  getProjectList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
});
</script>
