package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 异常分析-流量突变数据
 */
@Data
public class ExceptionByChangeRatioDTO {

    private String stationId;

    private String stationName;

    private String unit;

    // 分析值
    private BigDecimal value;

    // 对比值
    private BigDecimal compareValue;

    // 变化率
    private BigDecimal ratio;

    // 分析值日期时间
    private String valueDate;

    // 对比值日期时间
    private String compareValueDate;

}
