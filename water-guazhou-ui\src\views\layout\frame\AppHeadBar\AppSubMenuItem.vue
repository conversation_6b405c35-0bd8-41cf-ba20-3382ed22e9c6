<template>
  <div
    class="submenu-group-item"
    :class="[router.currentRoute.value.path === menu.path ? 'is-active' : '']"
    @click="goto"
  >
    <div class="submenu-group-item__text">
      <span
        class="title"
        :title="menu.meta?.title"
      >{{
        menu.meta?.title
      }}</span>
      <Icon
        class="submenu-follow"
        icon="ep:star"
      ></Icon>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { useAppStore } from '@/store'

const props = defineProps<{
  menu: any
}>()
const router = useRouter()
const goto = () => {
  useAppStore().TOGGLE_menuShow(false)
  router.push({ ...props.menu })
}
</script>
<style lang="scss" scoped>
.submenu-group-item {
  width: 150px;
  height: 42px;
  background-color: var(--el-color-info-light-8);
  cursor: pointer;
  display: inline-block;
  margin-right: 10px;
  padding: 12px 10px;
  margin-bottom: 10px;
  &.is-active,
  &:hover {
    .submenu-group-item__text {
      .title {
        color: var(--el-color-primary);
      }
    }
  }
  .submenu-group-item__text {
    padding: 4px;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: var(--el-text-color-regular);
    .title {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-right: auto;
    }
  }
}
</style>
