package org.thingsboard.server.controller.production;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.pipeNetwork.PipeNetworkService;
import org.thingsboard.server.dao.production.ProductionService;
import org.thingsboard.server.dao.stationData.StationDataService;
import org.thingsboard.server.dao.util.StationDataUtil;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.math.BigDecimal;
import java.util.*;

@RestController
@RequestMapping("api/pressureMonitoringStation")
public class PressureMonitoringStationController extends BaseController {

    @Autowired
    private ProductionService productionService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private PipeNetworkService pipeNetworkService;

    @GetMapping("getList")
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String projectId,
                                 @RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam String status) throws ThingsboardException {
        String type = DataConstants.StationType.PRESSURE_MONITORING.getValue() + "," + DataConstants.StationType.FLOW_FLOW_MONITORING.getValue();
        return IstarResponse.ok(productionService.getList(type, projectId, name, status, getTenantId()));
    }

    /**
     * 查询指定站点的流量监测详情数据
     */
    @GetMapping("gis/getDataDetail")
    public IstarResponse getDataDetail(@RequestParam String stationId) throws ThingsboardException {
        JSONObject waterSupplyDetail = new JSONObject();

        // 压力曲线
        List<String> attrList = new ArrayList<>();
        attrList.add(DataConstants.DeviceAttrType.PRESSURE.getValue());
        // 今日时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        Map<String, List<JSONObject>> stationDataMap = productionService.getStationData(
                stationId, "", attrList, DateUtils.HOUR, todayStart, new Date(), getTenantId());

        for (Map.Entry<String, List<JSONObject>> entry : stationDataMap.entrySet()) {
            waterSupplyDetail.put(entry.getKey(), entry.getValue());
        }

        // 最后压力
        List<DeviceFullData> dataList = stationDataService.getStationDataDetail(stationId, "", true, getTenantId());
        BigDecimal currentPressure = null;
        if (dataList != null && dataList.size() > 0) {
            for (DeviceFullData deviceFullData : dataList) {
                if (deviceFullData.getProperty().equals(DataConstants.DeviceAttrType.PRESSURE.getValue())) {
                    currentPressure = new BigDecimal(deviceFullData.getValue());
                }
            }
        }
        waterSupplyDetail.put("currentPressure", currentPressure);

        return IstarResponse.ok(waterSupplyDetail);
    }

    @GetMapping("pressureData")
    public IstarResponse pressureData(@RequestParam String stationId, @RequestParam String queryType, @RequestParam String date) {
        try {
            // 查询站点
            StationEntity station = stationFeignClient.get(stationId);
            if (station == null) {
                throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            // 压力曲线、瞬时流量曲线
            List<String> attrList = new ArrayList<>();
            attrList.add(DataConstants.DeviceAttrType.PRESSURE.getValue());

            Map<String, Date> timeRange = StationDataUtil.getTimeRange(date, queryType);
            // 本期开始时间
            Date startTime = timeRange.get("start");
            // 本期结束时间
            Date endTime = timeRange.get("end");

            switch (queryType) {
                case "day":
                    queryType = DateUtils.HOUR;
                    break;
                case "month":
                    queryType = DateUtils.DAY;
                    break;
                case "year":
                    queryType = DateUtils.MONTH;
                    break;
                default:
                    throw new ThingsboardException("非法的查询类型!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }


            Map<String, List<JSONObject>> stationDataMap = productionService.getStationData(
                    stationId, "", attrList, queryType, startTime, endTime, getTenantId());

            return IstarResponse.ok(stationDataMap);
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 压力分析
     */
    @GetMapping("getPeriod")
    public IstarResponse getPeriod(@RequestParam Long start, @RequestParam Long end,
                                   @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(pipeNetworkService.getPressurePeriod(DataConstants.StationType.PRESSURE_MONITORING.getValue(), stationId, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }
}
