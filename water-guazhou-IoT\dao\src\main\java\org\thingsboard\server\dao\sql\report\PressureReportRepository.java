package org.thingsboard.server.dao.sql.report;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.PressureReport;

import java.util.Date;
import java.util.List;

public interface PressureReportRepository extends JpaRepository<PressureReport, String> {

    List<PressureReport> findByStationIdAndTimeBetween(String stationId, Date startTime, Date endTime);

}
