import{d as at,j,a6 as rt,c as k,r as W,ay as et,g as I,h as st,F as z,p as r,bo as q,i as l,n as E,bh as M,q as g,bB as G,bt as nt,aq as pt,bs as lt,br as mt,C as ct}from"./index-r0dFAfgr.js";import{g as K,n as dt,m as ut}from"./MapView-DaoQedLH.js";import{w as ft}from"./Point-WxyopZva.js";import{b as A,l as ht}from"./echart-D5stWtDc.js";import{u as bt}from"./useStation-DJgnSZIA.js";import{d as gt}from"./zhandian-YaGuQZe6.js";import{i as wt,g as yt}from"./headwaterMonitoring-BgK7jThW.js";import vt from"./RightDrawerMap-D5PhmGFO.js";import{g as U}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as _t}from"./ViewHelper-BGCZjxXH.js";import{g as kt}from"./URLHelper-B9aplt5w.js";import"./widget-BcWKanF2.js";import"./ArcView-DpMnCY82.js";import"./geometryEngineBase-BhsKaODW.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./DateFormatter-Bm9a68Ax.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Ot={class:"content"},Ct={class:"right-box"},Dt={class:"right-box bottom"},xt={class:"bottom-content"},Bt={class:"bottom-box"},St={class:"water-supply-card"},Rt={class:"card-value"},Lt={class:"number"},Ft={class:"bottom-box"},Tt={class:"water-supply-card"},Wt={class:"card-value"},It={class:"number"},zt={class:"water-supply-card"},Mt={class:"card-value"},At={class:"number"},Vt=at({__name:"index",setup(Ht){const x=j(),Y=rt(),B=k(),{getAllStationOption:J}=bt(),V=k(),S=k(),R=k(),L=k(),F=k(),T=k(!1);let h=W([]);const a=W({colors:["#43B53099","#318DFF99","#FFB800D4"],pieOption1:null,barOption1:null,pieOption2:null,barOption2:null,pieOption3:null,barOption3:null,lineOption:null,curRow:{},data:null,stationLocation:[],windows:[]}),u={},D=async i=>{var d,w,O,y,v,b;const t=f.dataList.find(m=>m.stationId===i);a.curRow=t,X(t);let o;if(i?(o=(O=u.view)==null?void 0:O.graphics.find(m=>{var _,C;return((C=(_=m.attributes)==null?void 0:_.row)==null?void 0:C.id)===i}),o&&await U(u.view,o,{zoom:15,avoidHighlight:!0})):o=(w=(d=u.view)==null?void 0:d.graphics)==null?void 0:w.getItemAt(0),!o)return;const e=((y=o.attributes)==null?void 0:y.row)||{},s=((v=(await gt(e.id)).data)==null?void 0:v.map(m=>(m.label=m.propertyName,m.value,m)))||[],c=o==null?void 0:o.geometry;a.windows.length=0,a.windows.push({visible:!1,x:c.x,y:c.y,offsetY:-30,title:e.name,attributes:{values:s,id:e.id}}),await G(),(b=B.value)==null||b.openPop(e.id)},f=W({loading:!0,dataList:[],columns:[{prop:"name",label:"名称",minWidth:80},{prop:"todayWaterSupply",label:"今日供水量",minWidth:95},{prop:"yesterdayWaterSupply",label:"昨日供水量",minWidth:95},{prop:"monthWaterSupply",label:"本月供水量",minWidth:95}],highlightCurrentRow:!0,currentRowKey:"stationId",handleRowClick:async i=>{var o;a.curRow=i;const t=(o=u.view)==null?void 0:o.graphics.find(e=>{var p,s;return((s=(p=e.attributes)==null?void 0:p.row)==null?void 0:s.id)===i.stationId});t&&await U(u.view,t,{zoom:15,avoidHighlight:!0}),D(i.stationId)},pagination:{layout:"total,  prev, pager, next, jumper",refreshData:({page:i,size:t})=>{f.pagination.page=i,f.pagination.limit=t,f.dataList=h.slice((i-1)*t,i*t)}}}),Q=()=>{yt().then(i=>{var t,o;h=(t=i.data)==null?void 0:t.data,f.dataList=h==null?void 0:h.slice(0,20),f.pagination.total=h.length,f.currentRow=h[0],f.loading=!1,D((o=h[0])==null?void 0:o.stationId)}),ot()},X=async i=>{if(i){try{Z(),Y.listenTo(V.value,async()=>{f.currentRow=i;const o=(await wt(i.stationId||i.id)).data.data;G(()=>{var b,m,_,C,H,N,P,$;const e=(b=o.todayTotalFlowDataList)==null?void 0:b.map(n=>n.ts.substring(8,20)),p=(m=o.todayTotalFlowDataList)==null?void 0:m.map(n=>n.value);a.barOption1=A("",a.colors[0],e,p,50,10),a.barOption1&&(a.barOption1.backgroundColor="transparent"),a.pieOption1=i.todayWaterSupply;const s=(_=o.yesterdayTotalFlowDataList)==null?void 0:_.map(n=>n.ts.substring(8,20)),c=(C=o.yesterdayTotalFlowDataList)==null?void 0:C.map(n=>n.value);a.pieOption2=i.yesterdayWaterSupply,a.barOption2=A("",a.colors[1],s,c,50,10),a.barOption2&&(a.barOption2.backgroundColor="transparent");const d=(H=o.monthTotalFlowDataList)==null?void 0:H.map(n=>n.ts.substring(8,20)),w=(N=o.monthTotalFlowDataList)==null?void 0:N.map(n=>n.value);a.pieOption3=i.monthWaterSupply,a.barOption3=A("",a.colors[2],d,w,50,10),a.barOption3&&(a.barOption3.backgroundColor="transparent");const O=o.todayTotalFlowDataList.map(n=>n.ts),y=(P=o.pressure)==null?void 0:P.map(n=>n.value),v=($=o.Instantaneous_flow)==null?void 0:$.map(n=>n.value);a.lineOption=ht(O,y,v,50,50),a.lineOption&&(a.lineOption.backgroundColor="transparent"),a.lineOption.yAxis[0].name="压力(MPa)",a.lineOption.yAxis[1].name="瞬时流量(m³/h)",tt()})})}catch{}T.value=!1}},Z=()=>{var i,t,o,e;(i=R.value)==null||i.clear(),(t=S.value)==null||t.clear(),(o=L.value)==null||o.clear(),(e=F.value)==null||e.clear()},tt=()=>{var i,t,o,e;(i=R.value)==null||i.resize(),(t=S.value)==null||t.resize(),(o=L.value)==null||o.resize(),(e=F.value)==null||e.resize()},ot=async()=>{var t,o;const i=await J("水源地");(o=(t=u.view)==null?void 0:t.graphics)==null||o.removeAll(),i.map(e=>{var y,v,b,m;const p=e.data,s=(y=p==null?void 0:p.location)==null?void 0:y.split(","),c=new ft({longitude:s==null?void 0:s[0],latitude:s==null?void 0:s[1],spatialReference:(v=u.view)==null?void 0:v.spatialReference}),d=new K({geometry:c,symbol:new dt({width:25,height:30,yoffset:15,url:kt("水源地.png")}),attributes:{row:p}}),w=(i==null?void 0:i.find(_=>_.id===p.id))||{},O=new K({geometry:c,symbol:new ut({yoffset:-15,color:"#00ff33",text:w?w.label:"-"})});(m=(b=u.view)==null?void 0:b.graphics)==null||m.addMany([d,O])}),D()},it=async i=>{var t;u.view=i,(t=B.value)==null||t.toggleCustomDetail(!0),Q(),_t(u.view,o=>{var p,s,c;const e=(p=o.results)==null?void 0:p[0];if(e&&e.type==="graphic"){const d=(c=(s=e.graphic)==null?void 0:s.attributes)==null?void 0:c.row;D(d==null?void 0:d.id)}})};return(i,t)=>{const o=nt,e=pt,p=lt,s=et("VChart"),c=mt;return I(),st(vt,{ref_key:"refMap",ref:B,title:"水源监测总览",windows:l(a).windows,"hide-detail-close":!0,"hide-layer-list":!0,"right-drawer-width":600,onMapLoaded:it},{"detail-header":z(()=>t[0]||(t[0]=[r("span",{class:"title"},"水源总览",-1)])),"detail-default":z(()=>[q((I(),E("div",xt,[r("div",Bt,[r("div",St,[t[2]||(t[2]=r("div",{class:"card-header"},[r("i",{class:"water-icon el-icon-water-cup"}),r("span",{class:"title"},"今日供水量")],-1)),r("div",Rt,[r("span",Lt,M(l(a).pieOption1),1),t[1]||(t[1]=r("span",{class:"unit"},"m³",-1))]),g(s,{ref_key:"refChart2",ref:S,class:"bottom-chart-box",theme:l(x).isDark?"blackBackground":"whiteBackground",option:l(a).barOption1,autoresize:"",style:{backgroundColor:"transparent"}},null,8,["theme","option"])])]),r("div",Ft,[r("div",Tt,[t[4]||(t[4]=r("div",{class:"card-header"},[r("i",{class:"water-icon el-icon-data-analysis"}),r("span",{class:"title"},"昨日供水量")],-1)),r("div",Wt,[r("span",It,M(l(a).pieOption2),1),t[3]||(t[3]=r("span",{class:"unit"},"m³",-1))]),g(s,{ref_key:"refChart3",ref:L,class:"bottom-chart-box",theme:l(x).isDark?"blackBackground":"whiteBackground",option:l(a).barOption2,autoresize:"",style:{backgroundColor:"transparent"}},null,8,["theme","option"])])]),r("div",{ref_key:"refBottom",ref:V,class:"bottom-box"},[r("div",zt,[t[6]||(t[6]=r("div",{class:"card-header"},[r("i",{class:"water-icon el-icon-data-line"}),r("span",{class:"title"},"本月供水量")],-1)),r("div",Mt,[r("span",At,M(l(a).pieOption3),1),t[5]||(t[5]=r("span",{class:"unit"},"m³",-1))]),g(s,{ref_key:"refChart4",ref:F,class:"bottom-chart-box",theme:l(x).isDark?"blackBackground":"whiteBackground",option:l(a).barOption3,autoresize:"",style:{backgroundColor:"transparent"}},null,8,["theme","option"])])],512)])),[[c,l(T)]])]),default:z(()=>{var d;return[r("div",Ot,[g(o,{type:"simple",title:"水源监测"}),r("div",Ct,[g(e,{ref:"refCard",class:"table-box",config:l(f)},null,8,["config"])]),g(p),g(o,{type:"simple",title:(((d=l(a).curRow)==null?void 0:d.name)||"")+"24小时运行曲线"},null,8,["title"]),q((I(),E("div",Dt,[g(s,{ref_key:"refChart1",ref:R,option:l(a).lineOption,theme:l(j)().isDark?"blackBackground":"whiteBackground"},null,8,["option","theme"])])),[[c,l(T)]])])]}),_:1},8,["windows"])}}}),Ei=ct(Vt,[["__scopeId","data-v-ed652c38"]]);export{Ei as default};
