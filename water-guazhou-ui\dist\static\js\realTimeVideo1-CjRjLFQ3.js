import{_ as n}from"./index-C9hz-UZb.js";import c from"./historyVideo-DAxbHV6W.js";import _ from"./realTimeVideo-BzA6orc2.js";import{d,c as o,r as u,g as t,n as f,q as y,F as s,i,h as l,an as p}from"./index-r0dFAfgr.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./index-D9ERhRP6.js";import"./data-CfQCw447.js";const h={class:"wrapper"},N=d({__name:"realTimeVideo1",setup(b){const e=o("real");return o(),u({filters:[{type:"tabs",label:"",field:"type",tabType:"simple",tabs:[{label:"视频预览",value:"real"},{label:"视频回放",value:"history"}],onChange:a=>{e.value=a}}],defaultParams:{type:"real"}}),(a,r)=>{const m=n;return t(),f("div",h,[y(m,{title:"",style:{height:"100%"}},{title:s(()=>r[0]||(r[0]=[])),default:s(()=>[i(e)==="history"?(t(),l(c,{key:0})):p("",!0),i(e)==="real"?(t(),l(_,{key:1})):p("",!0)]),_:1})])}}});export{N as default};
