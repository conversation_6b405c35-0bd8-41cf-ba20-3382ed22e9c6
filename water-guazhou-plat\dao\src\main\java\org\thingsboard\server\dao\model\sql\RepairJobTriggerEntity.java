package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.DTO.TriggerDTO;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.REPAIR_PLAN_TRIGGER_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class RepairJobTriggerEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.REPAIR_PLAN_TRIGGER_MAIN_ID)
    private String mainId;

    @Column(name = ModelConstants.REPAIR_PLAN_TRIGGER_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.REPAIR_PLAN_TRIGGER_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.REPAIR_PLAN_TRIGGER_DETAIL)
    private String detail;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private String projectName;

    @Transient
    private String deviceName;

    @Transient
    private List<TriggerDTO> triggerList;

}
