{"widgetLabel": "Weather", "widgetLabelAccessible": "Weather widget", "weatherType": "Weather type", "error": {"unsupported": "Weather is only supported in SceneView.", "localScene": "Weather is only available in global scenes.", "notVisible": "Weather is only visible at lower altitudes.", "noAtmosphere": "Weather is only supported if the atmosphere is enabled."}, "sunny": {"label": "<PERSON>", "cloudCover": "Cloud cover"}, "cloudy": {"label": "Cloudy", "cloudCover": "Cloud cover"}, "rainy": {"label": "Rainy", "cloudCover": "Cloud cover", "precipitation": "Precipitation"}, "snowy": {"label": "Snowy", "cloudCover": "Cloud cover", "precipitation": "Precipitation", "snowCover": "Snow cover", "snowCoverTooltip": "Cover surfaces with snow (except basemap)."}, "foggy": {"label": "Foggy", "fogStrength": "Fog density"}}