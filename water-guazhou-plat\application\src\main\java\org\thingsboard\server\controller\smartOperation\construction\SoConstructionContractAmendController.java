package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractAmend;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractAmendPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractAmendSaveRequest;
import org.thingsboard.server.dao.construction.SoConstructionContractAmendService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/constructionContractAmend")
public class SoConstructionContractAmendController extends BaseController {
    @Autowired
    private SoConstructionContractAmendService service;

    @GetMapping
    public IPage<SoConstructionContractAmend> findAllConditional(SoConstructionContractAmendPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public SoConstructionContractAmend save(@RequestBody SoConstructionContractAmendSaveRequest req) {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionContractAmendSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}