package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SoGeneralTypePageRequest extends AdvancedPageableQueryEntity<SoGeneralType, SoGeneralTypePageRequest> {
    // 类型名称
    private String name;

    // 类型作用域
    private SoGeneralSystemScope scope;

}
