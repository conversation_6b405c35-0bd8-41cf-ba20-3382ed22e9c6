package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus;
import org.thingsboard.server.dao.util.imodel.query.Requestible;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus.*;

@Getter
@Setter
public class OrderRecordRejectRequest implements Requestible {
    @NotNullOrEmpty
    private List<String> idList;

    // 拒绝备注/原因
    private String rejectRemark;

    public OrderRecordStatus getStatus() {
        return DECLINED;
    }


}
