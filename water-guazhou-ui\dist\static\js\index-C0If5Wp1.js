import{d as E,M as C,c as p,s as R,r as f,x as u,a8 as h,bQ as P,l as S,S as w,o as F,g as H,n as N,q as b,i as g,b6 as W,b7 as B}from"./index-r0dFAfgr.js";import{_ as j}from"./CardTable-rdWOL4_6.js";import{_ as z}from"./CardSearch-CB_HNR-Q.js";import{I as x}from"./common-CvK_P_ao.js";import{j as A,k as G,a as O,b as Q}from"./equipmentPurchase-KOqzaoYr.js";import{l as U}from"./equipmentManage-DuoY00aj.js";import{f as v}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const $={class:"wrapper"},ne=E({__name:"index",setup(J){const{$btnPerms:c}=C(),d=p(),y=p(),m=p(),Y=p(),D=p([]),M=p({filters:[{label:"采购单编码",field:"code",type:"input",labelWidth:"90px"},{label:"采购单标题",field:"title",type:"input",labelWidth:"90px"},{type:"department-user",label:"请购人",field:"userId"},{label:"请购时间",field:"time",type:"daterange",format:"YYYY-MM-DD"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:x.QUERY,click:()=>o()},{type:"default",perm:!0,text:"重置",svgIcon:R(B),click:()=>{var e;(e=d.value)==null||e.resetForm(),o()}},{type:"success",perm:c("RoleManageAdd"),text:"新建采购单",icon:x.ADD,click:()=>k()}]}]}),n=f({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"采购单标题",prop:"title"},{label:"采购单编码",prop:"code"},{label:"采购部门名称",prop:"userDepartmentName"},{label:"请购人",prop:"userName"},{label:"用途",prop:"useWay"},{label:"申请时间",prop:"preTime",formatter:e=>v(e.preTime,"YYYY-MM-DD")},{label:"采购单创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>v(e.createTime,"YYYY-MM-DD")}],operations:[{type:"primary",isTextBtn:!0,color:"#4195f0",text:"详情",perm:c("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>I(e)},{type:"primary",isTextBtn:!0,color:"#4195f0",text:"编辑",perm:c("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>q(e)},{isTextBtn:!0,type:"danger",text:"删除",icon:"iconfont icon-shanchu",perm:c("RoleManageDelete"),click:e=>V(e)}],dataList:[],pagination:{page:1,limit:20,refreshData:({page:e,size:t})=>{n.pagination.page=e,n.pagination.limit=t,o()}}}),s=f({title:"新建采购单",labelWidth:"130px",submit:(e,t)=>{var r;if(t){(r=m.value)==null||r.openDrawer(),a.getDevice();return}if(!e.id&&n.dataList.some(i=>i.code===e.code)){u.warning("采购单编码重复");return}if(!e.items.some(i=>i.num&&i.inquiryEndTime)){u.warning("询价时间未填写");return}let l="新增成功";e.id&&(l="修改成功"),s.submitting=!0,A(e).then(()=>{var i;o(),u.success(l),(i=y.value)==null||i.closeDrawer(),s.submitting=!1})},defaultValue:{addRecord:!1},group:[{fields:[{xl:8,type:"input",label:"采购单标题",field:"title",rules:[{required:!0,message:"请输入采购单标题"}]},{xl:8,type:"department-user",label:"请购人",field:"userId",rules:[{required:!0,message:"请输入请购人"}]},{readonly:!0,xl:8,type:"input",label:"采购单编码",field:"code",rules:[{required:!0,message:"请输入采购单编码"}]},{xl:8,type:"input",label:"用途",field:"useWay"},{xl:8,type:"input",label:"预算",field:"budget",rules:[{required:!0,message:"请输入预算"}]},{xl:8,type:"date",label:"申请时间",field:"uploadTime",format:"YYYY-MM-DD HH:mm:ss",rules:[{required:!0,message:"请输入申请时间"}]},{xl:8,type:"date",label:"计划采购时间",field:"preTime",format:"YYYY-MM-DD HH:mm:ss",rules:[{required:!0,message:"请输入计划采购时间"}]},{xl:8,type:"switch",label:"是否补录",field:"addRecord"},{type:"table",field:"items",config:{indexVisible:!0,height:"350px",titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加设备",perm:!0,click:()=>{var e;(e=m.value)==null||e.openDrawer(),a.getDevice({})}}]}]}],dataList:h(()=>a.selectList),columns:[{label:"设备编号",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"规格型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"linkedType"},{label:"购买数量",prop:"num",formItemConfig:{type:"number",min:0}},{label:"询价时间",prop:"inquiryEndTime",tableDataName:"items",formItemConfig:{type:"date",field:"inquiryEndTime",rules:[{required:!0,message:"请选择询价时间"}],format:"YYYY-MM-DD HH:mm:ss"}}],operations:[{text:"移除",icon:x.DELETE,perm:c("RoleManageDelete"),click:e=>{a.selectList=a.selectList.filter(t=>t.id!==e.id)}}],pagination:{hide:!0}}}]}]}),T=f({title:"设备选择",submit:(e,t)=>{var l;t?a.getDevice(e):(a.selectList=[...a.selectList,...D.value],a.selectList=P(a.selectList,"id"),a.selectList=a.selectList.map(r=>((!r.num||r.num===null)&&(r.num="0"),r)),(l=m.value)==null||l.closeDrawer())},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"设备编码",field:"serialId"},{xl:8,type:"input",label:"设备名称",field:"name"},{xl:8,type:"input",label:"设备型号",field:"model"},{type:"table",field:"device",config:{indexVisible:!0,height:"350px",dataList:h(()=>a.deviceValue),selectList:[],handleSelectChange:e=>{D.value=e},titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"搜索",perm:!0,click:()=>{var e;(e=m.value)==null||e.Submit(!0)}}]}]}],columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"可用年限",prop:"useYear"}],pagination:{hide:!0}}}]}]}),_=f({title:"详情",labelWidth:"130px",print:!0,defaultValue:{},group:[{fields:[{xl:8,disabled:!0,type:"input",label:"采购单编码",field:"code"},{xl:8,disabled:!0,type:"input",label:"采购单标题",field:"title"},{xl:8,readonly:!0,type:"date",label:"计划采购日期",field:"uploadTime"},{xl:8,disabled:!0,type:"input",label:"用途",field:"useWay"},{xl:8,disabled:!0,type:"input",label:"预算",field:"budget"},{xl:8,disabled:!0,type:"input",label:"请购部门",field:"userDepartmentName"},{xl:8,disabled:!0,type:"input",label:"请购人",field:"userName"},{type:"table",field:"table",config:{indexVisible:!0,height:"350px",dataList:h(()=>a.DevicePurchaseItem||[]),columns:[{label:"设备编号",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"规格型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"购买数量",prop:"num"},{label:"要求完成询价时间",prop:"inquiryEndTime",formatter:e=>v(e.inquiryEndTime,"YYYY-MM-DD")}],pagination:{hide:!0}}}]}]}),k=()=>{var e;s.title="添加",a.selectList=[],D.value=[],s.defaultValue={code:"CGD"+S(new Date).format("YYYYMMDDHHmmss"),addRecord:!1},(e=y.value)==null||e.openDrawer()},q=e=>{var t;s.title="添加",a.selectList=[],D.value=[],s.defaultValue={...e},a.getDevicePurchaseItemValue(e.id),(t=y.value)==null||t.openDrawer()},I=e=>{var t;for(const l in e)e[l]===null&&(e[l]=" ");s.title="详情",_.defaultValue={...e||{}},a.getDevicePurchaseItemValue(e.id),(t=Y.value)==null||t.openDrawer()},V=e=>{w("确定删除该采购单, 是否继续?","删除提示").then(()=>{G(e.id).then(()=>{u.success("删除成功"),o()}).catch(t=>{u.warning(t)})})},a=f({DevicePurchaseItem:[],deviceValue:[],selectList:[],getDevice:e=>{const t={size:9999,page:1,...e};delete t.device,U(t).then(l=>{a.deviceValue=l.data.data.data||[]})},getDevicePurchaseItemValue:e=>{O({mainId:e,page:1,size:20}).then(l=>{a.selectList=l.data.data.data||[]})}}),o=async()=>{var t,l,r,i;const e={size:n.pagination.limit,page:n.pagination.page,...((t=d.value)==null?void 0:t.queryParams)||{}};e.time&&((l=e.time)==null?void 0:l.length)>1&&(e.fromTime=((r=d.value)==null?void 0:r.queryParams).time[0]||"",e.toTime=((i=d.value)==null?void 0:i.queryParams).time[1]||""),delete e.time,Q(e).then(L=>{n.dataList=L.data.data.data||[],n.pagination.total=L.data.data.total||0}).catch(()=>{u.error("获取数据失败")})};return F(()=>{o()}),(e,t)=>{const l=z,r=j,i=W;return H(),N("div",$,[b(l,{ref_key:"refSearch",ref:d,config:g(M)},null,8,["config"]),b(r,{config:g(n),class:"card-table"},null,8,["config"]),b(i,{ref_key:"refFormDetail",ref:Y,config:g(_)},null,8,["config"]),b(i,{ref_key:"refForm",ref:y,config:g(s)},null,8,["config"]),b(i,{ref_key:"refForm1",ref:m,config:g(T)},null,8,["config"])])}}});export{ne as default};
