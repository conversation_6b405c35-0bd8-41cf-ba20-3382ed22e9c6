<!-- 火灾分析 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'火灾分析'"
    :full-content="true"
    @map-loaded="onMapLoaded"
    @detail-refreshed="state.loading = false"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import {
  createGraphic,
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds,
  initBufferParams,
  queryBufferPolygon,
  setSymbol
} from '@/utils/MapHelper'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { useSketch } from '@/hooks/arcgis'

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()

const state = reactive<{
  tabs: any[]
  loading: boolean
  mounted: boolean
  layerInfos: any[]
  layerIds: any[]
}>({
  tabs: [],
  loading: false,
  mounted: false,
  layerInfos: [],
  layerIds: []
})
const staticState: {
  view?: __esri.MapView
  sketch?: __esri.SketchViewModel
  bufferLayer?: __esri.GraphicsLayer
  drawLayer?: __esri.GraphicsLayer
  firePoint?: __esri.Graphic
  queryParams: {
    geometry?: __esri.Geometry
    where?: string
  }
} = {
  queryParams: {
    geometry: undefined,
    where: undefined
  }
}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制着火点',
              iconifyIcon: 'mdi:circle-slice-8',
              click: () => startDraw()
            },
            {
              perm: true,
              text: '',
              title: '清除绘制',
              type: 'default',
              size: 'large',
              iconifyIcon: 'ep:delete',
              click: () => {
                clearGraphicsLayer()
                refMap.value?.clearDetailData()
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '分析范围'
      },
      fields: [
        {
          type: 'input-number',
          append: '米',
          field: 'distance'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              styles: {
                width: '100%'
              },
              click: () => startQuery()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    distance: 200
  }
})
const { initSketch, destroySketch } = useSketch()
const startDraw = () => {
  clearGraphicsLayer()
  refMap.value?.clearDetailData()
  staticState.sketch?.create('point')
  // if (!staticState.view) return
  // setMapCursor('crosshair')

  // staticState.drawer?.destroy()
  // staticState.drawer = initDrawer(staticState.view)
  // staticState.drawAction?.destroy()
  // staticState.drawAction = staticState.drawer?.create('point')

  // staticState.bufferLayer?.removeAll()
  // staticState.vertices = []
  // // staticState.drawAction?.on(['vertex-add', 'cursor-update'], updateVertices)
  // staticState.drawAction?.on('draw-complete', async e => {
  //   updateVertices(e)
  //   staticState.vertices = e.vertices
  //   setMapCursor('')
  // })
}
// const updateVertices = e => {
//   const graphic = createPoint(e.vertices, staticState.view?.spatialReference)
//   staticState.bufferLayer?.removeAll()
//   graphic && staticState.bufferLayer?.add(graphic)
//   staticState.queryParams.geometry = graphic?.geometry
// }

const startQuery = async () => {
  if (!staticState.firePoint) {
    SLMessage.warning('请先绘制着火点')
    return
  }
  SLMessage.info('正在查询中，请稍候...')
  const distance = refForm.value?.dataForm?.distance || '0'
  state.tabs.length = 0
  try {
    const geometry: __esri.Geometry | undefined = staticState.firePoint.geometry
    if (distance === '0') {
      staticState.queryParams.geometry = geometry
    } else {
      await doBuffer(geometry)
    }
    state.tabs = await getLayerOids(
      state.layerIds,
      state.layerInfos,
      staticState.queryParams
    )
    refMap.value?.refreshDetail(state.tabs)
  } catch (error) {
    SLMessage.error('系统错误')
    state.loading = false
  }
}
/**
 * 进行缓冲区分析
 * @param geometry 缓冲要素
 */
const doBuffer = async (geometry?: __esri.Geometry) => {
  staticState.bufferLayer?.removeAll()
  if (!geometry) return
  const distance = refForm.value?.dataForm?.distance || 0
  const polygons = await queryBufferPolygon(
    initBufferParams({
      bufferSpatialReference: staticState.view?.spatialReference,
      distances: [distance],
      geometries: [geometry],
      outSpatialReference: staticState.view?.spatialReference,
      geodesic: true,
      unit: 'meters',
      unionResults: false
    })
  )
  staticState.queryParams.geometry = polygons[0]
  const graphic = createGraphic({
    geometry: polygons[0],
    symbol: setSymbol('polygon', {
      color: [0, 182, 153, 0.2],
      outlineColor: '#00B699',
      outlineWidth: 1
    })
  })
  staticState.bufferLayer?.add(graphic)
}
const clearGraphicsLayer = () => {
  staticState.bufferLayer?.removeAll()
  staticState.drawLayer?.removeAll()
  staticState.firePoint?.destroy()
  staticState.firePoint = undefined
  staticState.queryParams.geometry = undefined
}
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(
    staticState.view,
    undefined,
    undefined,
    '消防栓'
  )
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
}
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    staticState.firePoint = result.graphics[0]
    // resolveMeasure()
  }
}
const onMapLoaded = view => {
  staticState.view = view
  staticState.bufferLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-analys-buffer',
    title: '火灾分析结果'
  })
  staticState.drawLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-analys-firepoint',
    title: '火灾点'
  })
  staticState.sketch = initSketch(staticState.view, staticState.drawLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  })
  staticState.sketch.pointSymbol = setSymbol('point', { color: '#ff0000' }) as __esri.SimpleMarkerSymbol
  getLayerInfo()
}
onBeforeUnmount(() => {
  staticState.bufferLayer?.removeAll()
  staticState.drawLayer?.removeAll()
  destroySketch()
})
</script>
<style lang="scss" scoped></style>
