import{_ as B}from"./index-C9hz-UZb.js";import{d as E,c as i,r as m,j as I,o as P,ay as W,g as _,h as b,F as T,p as c,q as w,i as s,aq as j,C as z}from"./index-r0dFAfgr.js";import{_ as M}from"./Search-NSrhrIa_.js";import"./index-0NlGN6gS.js";import{u as N}from"./useDetector-BRcb7GRN.js";import{r as X}from"./echarts-DP4wVWSW.js";import{b as H}from"./statistics-CeyexT_5.js";const J={class:"search-box"},K={class:"chart-box"},Q={class:"chart-box"},R=E({__name:"GSLFX",setup(U){const y=i(),r=m({showTable:!1,pieChartOption:null,lineChartOption:null}),D=m({defaultParams:{type:"day",grade:"1",tc:"c"},filters:[{label:"",field:"grade",type:"select",options:[{label:"一级分区",value:"1"},{label:"二级分区",value:"2"}],onChange:()=>{u()}},{label:"",field:"type",type:"radio-button",options:[{label:"日",value:"day"},{label:"月",value:"month"},{label:"季度",value:"quarter"},{label:"年",value:"year"}],onChange:()=>{u()}},{label:"",field:"tc",type:"radio-button",options:[{label:"",iconifyIcon:"ep:pie-chart",value:"c"},{label:"",iconifyIcon:"ep:grid",value:"t"}],onChange:a=>{r.showTable=a==="t"}}]}),p=m({indexVisible:!0,dataList:[],columns:[{label:"区域名称",prop:"name"}],pagination:{hide:!0}}),g=I(),q=(a,o)=>{const l=o.map(n=>({type:"line",data:n.y||[],name:n.name,smooth:!0,symbol:"none"}));return{backgroundColor:g.isDark?"transparent":"#F4F7FA",tooltip:{trigger:"axis"},legend:{top:"top",left:"right",textStyle:{color:g.isDark?"#fff":"#333"}},grid:{left:60,right:40,top:40,bottom:30,containLabel:!0},xAxis:{axisLabel:{color:"#409EFF"},boundaryGap:!1,type:"category",data:a,axisTick:{show:!1}},yAxis:{type:"value",axisLabel:{color:"#409EFF"},name:"各区域供水量(m³)",axisTick:{show:!1},axisLine:{show:!1},splitLine:{lineStyle:{color:"#409EFF",type:"dashed"}}},series:l}},u=()=>{var o,l,n;(o=h.value)==null||o.clear(),(l=f.value)==null||l.clear();const a=((n=y.value)==null?void 0:n.queryParams)||{};H({...a}).then(d=>{var C,x,k;const t=d.data.data||[],V=t==null?void 0:t.map(e=>({name:e.name,value:e.total,scale:e.rate}));r.pieChartOption=X(V,"m³"),r.lineChartOption=q(((C=t[0])==null?void 0:C.x)||[],t||[]),p.columns=[{fixed:"left",minWidth:160,label:"区域名称",prop:"name"},...((k=(x=t[0])==null?void 0:x.x)==null?void 0:k.map(e=>({minWidth:120,label:e,prop:e})))||[]],p.dataList=t.map(e=>{var S;const F={name:e.name};return(S=e.x)==null||S.map((A,G)=>{var L;F[A]=(L=e.y)==null?void 0:L[G]}),F})})},O=N(),v=i(),f=i(),h=i();return P(()=>{u(),O.listenToMush(v.value,()=>{var a,o;(a=f.value)==null||a.resize(),(o=h.value)==null||o.resize()})}),(a,o)=>{const l=M,n=W("VChart"),d=j,t=B;return _(),b(t,{title:"供水量分析"},{query:T(()=>[c("div",J,[w(l,{ref_key:"refSearch",ref:y,config:s(D)},null,8,["config"])])]),default:T(()=>[c("div",{ref_key:"refDiv",ref:v,class:"charts-box"},[c("div",K,[w(n,{ref_key:"refChart1",ref:f,option:s(r).pieChartOption},null,8,["option"])]),c("div",Q,[s(r).showTable?(_(),b(d,{key:0,config:s(p)},null,8,["config"])):(_(),b(n,{key:1,ref_key:"refChart2",ref:h,option:s(r).lineChartOption},null,8,["option"]))])],512)]),_:1})}}}),ne=z(R,[["__scopeId","data-v-0cdf756c"]]);export{ne as default};
