<template>
  <div class="scgk-info">
    <TargetItem
      v-for="(item, i) in state.info"
      :key="i"
      :config="item"
      :class="item.className"
    />
    <el-image
      :fit="'contain'"
      class="ellipse-image"
      :src="ellipseBg"
      alt=""
    />
    <div class="annual-water-supply">
      <div class="count">
        1.95
      </div>
      <div class="text">
        年度累计供水量 (万m3)
      </div>
    </div>
  </div>
  <ScrollList :data="state.data"></ScrollList>
</template>
<script lang="ts" setup>
import ScrollList from './ScrollList.vue'
import TargetItem from './TargetItem.vue'
import ellipseBg from '../imgs/decisionMaking_center_left.png'

const state = reactive<{
  info: ITargetItem[]
  data: any[]
}>({
  data: [
    { name: '供水总量(万m³)', value: '1.95', scale: '-' },
    { name: '总电耗(KW•H)', value: '0', scale: '-' }
  ],
  info: [
    {
      label: '年度目标供水量',
      value: '3,000.00',
      unit: '万m3',
      text: '完成率',
      scale: '19.2%',
      className: 'annual-water-target-supply',
      rows: [1, 2, 3]
    },
    {
      label: '全年日均供水量',
      value: '0.04',
      unit: '万m3',
      text: '同比',
      status: 'up',
      scale: '3.4%',
      className: 'average-water-supply',
      rows: [1, 2, 3]
    },
    {
      label: '最高日水量',
      value: '1.25',
      unit: '万m3',
      text: '同比',
      status: 'down',
      scale: '3.4%',
      className: 'dayly-max-water-supply',
      rows: [1, 2, 3]
    }
  ]
})
</script>
<style lang="scss" scoped>
.scgk-info {
  position: relative;
  width: 100%;
  height: 290px;
  .annual-water-target-supply {
    position: absolute;
    top: 60px;
    left: 0;
  }
  .average-water-supply {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
  }
  .dayly-max-water-supply {
    position: absolute;
    top: 60px;
    right: 0;
  }
  .ellipse-image {
    position: absolute;
    top: 60%;
    left: 50%;
    width: 90%;
    transform: translateX(-52%) translateY(-50%);
  }
  .annual-water-supply {
    position: absolute;
    top: 60%;
    left: 50%;
    transform: translateX(-50%);
    .count {
      font-size: 24px;
      text-align: center;
    }
    .text {
      font-size: 14px;
      text-align: center;
    }
  }
}
</style>
