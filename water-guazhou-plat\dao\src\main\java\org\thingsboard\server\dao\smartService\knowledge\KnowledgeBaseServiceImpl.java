package org.thingsboard.server.dao.smartService.knowledge;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeBase;
import org.thingsboard.server.dao.sql.smartService.knowledge.KnowledgeBaseMapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;

    @Autowired
    private KnowledgeBaseTypeService knowledgeBaseTypeService;

    @Override
    public PageData getList(String typeId, String title, String content, Long startTime, Long endTime, int page, int size, String tenantId) {
        List<String> typeIds = new ArrayList<>();
        knowledgeBaseTypeService.getAllIdByPid(typeId, typeIds);
        List<KnowledgeBase> baseList = knowledgeBaseMapper.getList(typeIds, title, content, startTime, endTime, page, size, tenantId);
        int total = knowledgeBaseMapper.getListCount(typeIds, title, content, startTime, endTime, tenantId);

        return new PageData(total, baseList);

    }

    @Override
    public KnowledgeBase save(KnowledgeBase knowledgeBase) {
        knowledgeBase.setUpdateTime(new Date());
        if (StringUtils.isBlank(knowledgeBase.getId())) {
            knowledgeBase.setCreateTime(new Date());
            knowledgeBaseMapper.insert(knowledgeBase);
        } else {
            knowledgeBaseMapper.updateById(knowledgeBase);
        }

        return knowledgeBase;
    }

    @Override
    public int delete(List<String> ids) {
        return knowledgeBaseMapper.deleteBatchIds(ids);
    }
}
