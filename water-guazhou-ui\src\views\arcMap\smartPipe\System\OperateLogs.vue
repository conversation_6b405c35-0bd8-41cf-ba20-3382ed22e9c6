<!-- <template>
  <div class="wrapper">
    <SLCard class="card-table">
      <iframe
        width="100%"
        height="100%"
        src=""
      ></iframe>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import { GetGisLogs } from '@/api/mapservice'

const state = reactive<{
  result: string
  logUrl: string
}>({
  result: '',
  logUrl: window.SITE_CONFIG.GIS_CONFIG.gisApi + '/api/webapp/log'
})
const refreshData = () => {
  GetGisLogs().then(res => {
    state.result = res.data
  })
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.card-table {
  padding: 8px 20px;
  height: 100%;
}
iframe {
  border: none;
}
</style> -->

<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    ></CardTable>
  </div>
</template>
<script lang="ts" setup>
import { GetGisOperateLogs } from '@/api/system/gisSetting'
import { ICardSearchIns, ICardTableIns } from '@/components/type'
import { formatDate } from '@/utils/DateFormatter'
import { GisLogAppsOption, GisLogOperateTypeOptions } from '../../config'

const refTable = ref<ICardTableIns>()
const refSearch = ref<ICardSearchIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'datetimerange',
      field: 'date',
      label: '时间范围',
      shortCusts: [
        {
          text: '今天',
          value: () => [
            dayjs().startOf('D').toDate(),
            dayjs().endOf('D').toDate()
          ]
        },
        {
          text: '昨天',
          value: () => [
            dayjs().startOf('D').subtract(1, 'day').toDate(),
            dayjs().endOf('D').subtract(1, 'day').toDate()
          ]
        },
        {
          text: '最近三天',
          value: () => [
            dayjs().startOf('D').subtract(2, 'd').toDate(),
            dayjs().endOf('D').toDate()
          ]
        },
        {
          text: '本周',
          value: () => [
            dayjs().startOf('week').toDate(),
            dayjs().endOf('week').toDate()
          ]
        },
        {
          text: '上周',
          value: () => [
            dayjs().startOf('w').subtract(1, 'w').toDate(),
            dayjs().endOf('w').subtract(1, 'w').toDate()
          ]
        },
        {
          text: '本月',
          value: () => [
            dayjs().startOf('month').toDate(),
            dayjs().endOf('month').toDate()
          ]
        },
        {
          text: '上月',
          value: () => [
            dayjs().startOf('month').subtract(1, 'month').toDate(),
            dayjs().endOf('month').subtract(1, 'month').toDate()
          ]
        },
        {
          text: '最近三月',
          value: () => [
            dayjs().startOf('day').add(1, 'day').subtract(3, 'month')
              .toDate(),
            dayjs().endOf('day').toDate()
          ]
        },
        {
          text: '最近半年',
          value: () => [
            dayjs().startOf('day').add(1, 'day').subtract(6, 'month')
              .toDate(),
            dayjs().endOf('day').toDate()
          ]
        },
        {
          text: '最近一年',
          value: () => [
            dayjs().startOf('day').add(1, 'd').subtract(1, 'year')
              .toDate(),
            dayjs().endOf('day').toDate()
          ]
        }
      ]
    },
    {
      type: 'select',
      options: GisLogAppsOption,
      field: 'type',
      label: '系统模块'
    },
    { type: 'input', label: '关键字', field: 'keyword' },
    {
      type: 'select',
      label: '操作类型',
      field: 'optionType',
      options: GisLogOperateTypeOptions
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => refSearch.value?.resetForm()
        },
        {
          perm: true,
          text: '导出',
          type: 'success',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ]
})
const TableConfig = reactive<ICardTable>({
  dataList: [],
  columns: [
    { minWidth: 120, label: '功能模块', prop: 'optionName' },
    { minWidth: 220, label: '日志消息', prop: 'content' },
    { minWidth: 120, label: '操作用户', prop: 'optionUserName' },
    { minWidth: 120, label: '操作类型', prop: 'optionType' },
    {
      minWidth: 180,
      label: '操作时间',
      prop: 'optionTime',
      formatter(row, value) {
        return formatDate(value)
      }
    }
  ],
  pagination: {
    refreshData({ page, size }) {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const params: any = {
      ...query,
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      beginOptionTime: query.date?.[0],
      endOptionTime: query.date?.[1]
    }
    delete params.date
    const res = await GetGisOperateLogs(params)
    const data = res.data?.data || {}
    TableConfig.dataList = data.data || []
    TableConfig.pagination.total = data.total || 0
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped></style>
