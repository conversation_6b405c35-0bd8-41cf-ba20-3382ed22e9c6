"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6239,3172,9880],{22303:(e,t,r)=>{r.d(t,{Z:()=>h});var o,n,s=r(35270),i=r(22021),a=r(70586),l=r(75215);function u(e){return(0,i.uZ)((0,l.vU)(e),0,255)}function c(e,t,r){return e=Number(e),isNaN(e)?r:e<t?t:e>r?r:e}class d{static blendColors(e,t,r,o=new d){return o.r=Math.round(e.r+(t.r-e.r)*r),o.g=Math.round(e.g+(t.g-e.g)*r),o.b=Math.round(e.b+(t.b-e.b)*r),o.a=e.a+(t.a-e.a)*r,o._sanitize()}static fromRgb(e,t){const r=e.toLowerCase().match(/^(rgba?|hsla?)\(([\s\.\-,%0-9]+)\)/);if(r){const e=r[2].split(/\s*,\s*/),o=r[1];if("rgb"===o&&3===e.length||"rgba"===o&&4===e.length){const r=e[0];if("%"===r.charAt(r.length-1)){const r=e.map((e=>2.56*parseFloat(e)));return 4===e.length&&(r[3]=parseFloat(e[3])),d.fromArray(r,t)}return d.fromArray(e.map((e=>parseFloat(e))),t)}if("hsl"===o&&3===e.length||"hsla"===o&&4===e.length)return d.fromArray((0,s.B7)(parseFloat(e[0]),parseFloat(e[1])/100,parseFloat(e[2])/100,parseFloat(e[3])),t)}return null}static fromHex(e,t=new d){if(4!==e.length&&7!==e.length||"#"!==e[0])return null;const r=4===e.length?4:8,o=(1<<r)-1;let n=Number("0x"+e.substr(1));return isNaN(n)?null:(["b","g","r"].forEach((e=>{const s=n&o;n>>=r,t[e]=4===r?17*s:s})),t.a=1,t)}static fromArray(e,t=new d){return t._set(Number(e[0]),Number(e[1]),Number(e[2]),Number(e[3])),isNaN(t.a)&&(t.a=1),t._sanitize()}static fromString(e,t){const r=(0,s.St)(e)?(0,s.h$)(e):null;return r&&d.fromArray(r,t)||d.fromRgb(e,t)||d.fromHex(e,t)}static fromJSON(e){return e&&new d([e[0],e[1],e[2],e[3]/255])}static toUnitRGB(e){return(0,a.pC)(e)?[e.r/255,e.g/255,e.b/255]:null}static toUnitRGBA(e){return(0,a.pC)(e)?[e.r/255,e.g/255,e.b/255,null!=e.a?e.a:1]:null}constructor(e){this.r=255,this.g=255,this.b=255,this.a=1,e&&this.setColor(e)}get isBright(){return.299*this.r+.587*this.g+.114*this.b>=127}setColor(e){return"string"==typeof e?d.fromString(e,this):Array.isArray(e)?d.fromArray(e,this):(this._set(e.r??0,e.g??0,e.b??0,e.a??1),e instanceof d||this._sanitize()),this}toRgb(){return[this.r,this.g,this.b]}toRgba(){return[this.r,this.g,this.b,this.a]}toHex(){const e=this.r.toString(16),t=this.g.toString(16),r=this.b.toString(16);return`#${e.length<2?"0"+e:e}${t.length<2?"0"+t:t}${r.length<2?"0"+r:r}`}toCss(e=!1){const t=this.r+", "+this.g+", "+this.b;return e?`rgba(${t}, ${this.a})`:`rgb(${t})`}toString(){return this.toCss(!0)}toJSON(){return this.toArray()}toArray(e=d.AlphaMode.ALWAYS){const t=u(this.r),r=u(this.g),o=u(this.b);return e===d.AlphaMode.ALWAYS||1!==this.a?[t,r,o,u(255*this.a)]:[t,r,o]}clone(){return new d(this.toRgba())}hash(){return this.r<<24|this.g<<16|this.b<<8|255*this.a}equals(e){return(0,a.pC)(e)&&e.r===this.r&&e.g===this.g&&e.b===this.b&&e.a===this.a}_sanitize(){return this.r=Math.round(c(this.r,0,255)),this.g=Math.round(c(this.g,0,255)),this.b=Math.round(c(this.b,0,255)),this.a=c(this.a,0,1),this}_set(e,t,r,o){this.r=e,this.g=t,this.b=r,this.a=o}}d.prototype.declaredClass="esri.Color",o=d||(d={}),(n=o.AlphaMode||(o.AlphaMode={}))[n.ALWAYS=0]="ALWAYS",n[n.UNLESS_OPAQUE=1]="UNLESS_OPAQUE";const h=d},99880:(e,t,r)=>{r.d(t,{V:()=>l});var o=r(68773),n=(r(3172),r(20102)),s=r(92604),i=r(17452);const a=s.Z.getLogger("esri.assets");function l(e){if(!o.Z.assetsPath)throw a.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new n.Z("assets:path-not-set","config.assetsPath is not set");return(0,i.v_)(o.Z.assetsPath,e)}},35270:(e,t,r)=>{r.d(t,{B7:()=>l,St:()=>n,VL:()=>i,h$:()=>s,rW:()=>u});const o={transparent:[0,0,0,0],black:[0,0,0,1],silver:[192,192,192,1],gray:[128,128,128,1],white:[255,255,255,1],maroon:[128,0,0,1],red:[255,0,0,1],purple:[128,0,128,1],fuchsia:[255,0,255,1],green:[0,128,0,1],lime:[0,255,0,1],olive:[128,128,0,1],yellow:[255,255,0,1],navy:[0,0,128,1],blue:[0,0,255,1],teal:[0,128,128,1],aqua:[0,255,255,1],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],blanchedalmond:[255,235,205,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],oldlace:[253,245,230,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],rebeccapurple:[102,51,153,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],whitesmoke:[245,245,245,1],yellowgreen:[154,205,50,1]};function n(e){return o[e]||o[e.toLowerCase()]}function s(e){return o[e]??o[e.toLowerCase()]}function i(e){return[...s(e)]}function a(e,t,r){r<0&&++r,r>1&&--r;const o=6*r;return o<1?e+(t-e)*o:2*r<1?t:3*r<2?e+(t-e)*(2/3-r)*6:e}function l(e,t,r,o=1){const n=(e%360+360)%360/360,s=r<=.5?r*(t+1):r+t-r*t,i=2*r-s;return[Math.round(255*a(i,s,n+1/3)),Math.round(255*a(i,s,n)),Math.round(255*a(i,s,n-1/3)),o]}function u(e){const t=e.length>5,r=t?8:4,o=(1<<r)-1,n=t?1:17,s=t?9===e.length:5===e.length;let i=Number("0x"+e.substr(1));if(isNaN(i))return null;const a=[0,0,0,1];let l;return s&&(l=i&o,i>>=r,a[3]=n*l/255),l=i&o,i>>=r,a[2]=n*l,l=i&o,i>>=r,a[1]=n*l,l=i&o,i>>=r,a[0]=n*l,a}},36030:(e,t,r)=>{r.d(t,{J:()=>s});var o=r(35454),n=r(5600);function s(e,t={}){const r=e instanceof o.X?e:new o.X(e,t),s={type:t?.ignoreUnknown??1?r.apiValues:String,json:{type:r.jsonValues,read:!t?.readOnly&&{reader:r.read},write:{writer:r.write}}};return void 0!==t?.readOnly&&(s.readOnly=!!t.readOnly),void 0!==t?.default&&(s.json.default=t.default),void 0!==t?.name&&(s.json.name=t.name),void 0!==t?.nonNullable&&(s.nonNullable=t.nonNullable),(0,n.Cb)(s)}},10661:(e,t,r)=>{r.d(t,{s:()=>n});var o=r(42100);class n extends o.s{notify(){const e=this._observers;if(e&&e.length>0){const t=e.slice();for(const e of t)e.onInvalidated(),e.onCommitted()}}}},66577:(e,t,r)=>{r.d(t,{qM:()=>c});var o=r(75215),n=r(6570),s=r(9361),i=r(65091),a=r(94139),l=r(38913),u=r(58901);r(82971),r(86973),r(33955);const c={base:s.Z,key:"type",typeMap:{extent:n.Z,multipoint:i.Z,point:a.Z,polyline:u.Z,polygon:l.Z}};(0,o.N7)(c)},24470:(e,t,r)=>{r.d(t,{Gv:()=>b,HH:()=>u,SO:()=>d,Ue:()=>s,al:()=>a,cS:()=>f,fS:()=>y,jE:()=>h,jn:()=>c,kK:()=>m,oJ:()=>l,r3:()=>p}),r(80442),r(22021);var o=r(70586),n=r(6570);function s(e=w){return[e[0],e[1],e[2],e[3]]}function i(e,t){return e!==t&&(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3]),e}function a(e,t,r,o,n=s()){return n[0]=e,n[1]=t,n[2]=r,n[3]=o,n}function l(e,t=s()){return t[0]=e.xmin,t[1]=e.ymin,t[2]=e.xmax,t[3]=e.ymax,t}function u(e,t){return new n.Z({xmin:e[0],ymin:e[1],xmax:e[2],ymax:e[3],spatialReference:t})}function c(e,t,r){if((0,o.Wi)(t))i(r,e);else if("length"in t)g(t)?(r[0]=Math.min(e[0],t[0]),r[1]=Math.min(e[1],t[1]),r[2]=Math.max(e[2],t[2]),r[3]=Math.max(e[3],t[3])):2!==t.length&&3!==t.length||(r[0]=Math.min(e[0],t[0]),r[1]=Math.min(e[1],t[1]),r[2]=Math.max(e[2],t[0]),r[3]=Math.max(e[3],t[1]));else switch(t.type){case"extent":r[0]=Math.min(e[0],t.xmin),r[1]=Math.min(e[1],t.ymin),r[2]=Math.max(e[2],t.xmax),r[3]=Math.max(e[3],t.ymax);break;case"point":r[0]=Math.min(e[0],t.x),r[1]=Math.min(e[1],t.y),r[2]=Math.max(e[2],t.x),r[3]=Math.max(e[3],t.y)}}function d(e){return function(e){return(0,o.Wi)(e)||e[0]>=e[2]?0:e[2]-e[0]}(e)*function(e){return e[1]>=e[3]?0:e[3]-e[1]}(e)}function h(e,t,r){return t>=e[0]&&r>=e[1]&&t<=e[2]&&r<=e[3]}function m(e,t){return Math.max(t[0],e[0])<=Math.min(t[2],e[2])&&Math.max(t[1],e[1])<=Math.min(t[3],e[3])}function p(e,t){return t[0]>=e[0]&&t[2]<=e[2]&&t[1]>=e[1]&&t[3]<=e[3]}function f(e){return e?i(e,b):s(b)}function g(e){return null!=e&&4===e.length}function y(e,t){return g(e)&&g(t)?e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]:e===t}const b=[1/0,1/0,-1/0,-1/0],w=[0,0,0,0]},33955:(e,t,r)=>{r.d(t,{Ji:()=>g,YX:()=>c,aW:()=>d,im:()=>f,l9:()=>m,oU:()=>p,q9:()=>b,wp:()=>h});var o=r(70586),n=r(6570),s=r(9361),i=r(65091),a=r(94139),l=r(38913),u=r(58901);function c(e){return void 0!==e.xmin&&void 0!==e.ymin&&void 0!==e.xmax&&void 0!==e.ymax}function d(e){return void 0!==e.points}function h(e){return void 0!==e.x&&void 0!==e.y}function m(e){return void 0!==e.paths}function p(e){return void 0!==e.rings}function f(e){return(0,o.Wi)(e)?null:e instanceof s.Z?e:h(e)?a.Z.fromJSON(e):m(e)?u.Z.fromJSON(e):p(e)?l.Z.fromJSON(e):d(e)?i.Z.fromJSON(e):c(e)?n.Z.fromJSON(e):null}function g(e){return e?h(e)?"esriGeometryPoint":m(e)?"esriGeometryPolyline":p(e)?"esriGeometryPolygon":c(e)?"esriGeometryEnvelope":d(e)?"esriGeometryMultipoint":null:null}const y={esriGeometryPoint:a.Z,esriGeometryPolyline:u.Z,esriGeometryPolygon:l.Z,esriGeometryEnvelope:n.Z,esriGeometryMultipoint:i.Z};function b(e){return e&&y[e]||null}},86973:(e,t,r)=>{r.d(t,{M:()=>n,P:()=>s});var o=r(35454);const n=(0,o.w)()({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon"}),s=(0,o.w)()({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh"})},61576:(e,t,r)=>{r.r(t),r.d(t,{default:()=>g}),r(66577);var o=r(70586),n=r(18571),s=r(5847),i=r(75993),a=r(55914),l=r(98722),u=r(73506),c=r(15612),d=r(80676),h=r(87521),m=r(72758),p=r(23808),f=r(6570);class g{convertVectorFieldData(e){const t=s.Z.fromJSON(e.pixelBlock),r=(0,d.KC)(t,e.type);return Promise.resolve((0,o.pC)(r)?r.toJSON():null)}async decode(e){const t=await(0,i.J)(e.data,e.options);return t&&t.toJSON()}symbolize(e){e.pixelBlock=s.Z.fromJSON(e.pixelBlock),e.extent=e.extent?f.Z.fromJSON(e.extent):null;const t=this.symbolizer.symbolize(e);return Promise.resolve((0,o.pC)(t)?t.toJSON():null)}async updateSymbolizer(e){this.symbolizer=m.Z.fromJSON(e.symbolizerJSON),e.histograms&&"rasterStretch"===this.symbolizer?.rendererJSON.type&&(this.symbolizer.rendererJSON.histograms=e.histograms)}async updateRasterFunction(e){this.rasterFunction=(0,l.Ue)(e.rasterFunctionJSON)}async process(e){const t=this.rasterFunction.process({extent:f.Z.fromJSON(e.extent),primaryPixelBlocks:e.primaryPixelBlocks.map((e=>(0,o.pC)(e)?s.Z.fromJSON(e):null)),primaryRasterIds:e.primaryRasterIds});return(0,o.pC)(t)?t.toJSON():null}stretch(e){const t=this.symbolizer.simpleStretch(s.Z.fromJSON(e.srcPixelBlock),e.stretchParams);return Promise.resolve((0,o.pC)(t)&&t.toJSON())}estimateStatisticsHistograms(e){const t=(0,c.Hv)(s.Z.fromJSON(e.srcPixelBlock));return Promise.resolve(t)}split(e){const t=(0,a.Vl)(s.Z.fromJSON(e.srcPixelBlock),e.tileSize,e.maximumPyramidLevel);return t&&t.forEach(((e,r)=>{t.set(r,e?.toJSON())})),Promise.resolve(t)}async mosaicAndTransform(e){const t=e.srcPixelBlocks.map((e=>e?new s.Z(e):null)),r=(0,a.us)(t,e.srcMosaicSize,{blockWidths:e.blockWidths,alignmentInfo:e.alignmentInfo,clipOffset:e.clipOffset,clipSize:e.clipSize});let n,i=r;return e.coefs&&(i=(0,a.Uk)(r,e.destDimension,e.coefs,e.sampleSpacing,e.interpolation)),e.projectDirections&&e.gcsGrid&&(n=(0,a.Qh)(e.destDimension,e.gcsGrid),i=(0,o.Wg)((0,d.xQ)(i,e.isUV?"vector-uv":"vector-magdir",n))),{pixelBlock:i?.toJSON(),localNorthDirections:n}}async createFlowMesh(e,t){const r={data:new Float32Array(e.flowData.buffer),mask:new Uint8Array(e.flowData.maskBuffer),width:e.flowData.width,height:e.flowData.height},{vertexData:o,indexData:n}=await(0,p.GE)(e.meshType,e.simulationSettings,r,t.signal);return{result:{vertexBuffer:o.buffer,indexBuffer:n.buffer},transferList:[o.buffer,n.buffer]}}async getProjectionOffsetGrid(e){const t=f.Z.fromJSON(e.projectedExtent),r=f.Z.fromJSON(e.srcBufferExtent);let o=null;e.datumTransformationSteps&&(o=new n.Z({steps:e.datumTransformationSteps})),(e.includeGCSGrid||(0,u.Mk)(t.spatialReference,r.spatialReference,o))&&await(0,u.zD)();const s=e.rasterTransform?(0,h.c)(e.rasterTransform):null;return(0,u.Qp)({...e,projectedExtent:t,srcBufferExtent:r,datumTransformation:o,rasterTransform:s})}}},3172:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var o=r(68773),n=r(40330),s=r(20102),i=r(80442),a=r(22974),l=r(70586),u=r(95330),c=r(17452),d=r(19745),h=r(71058),m=r(85958);async function p(e,t){const a=(0,c.HK)(e),d=(0,c.jc)(e);d||a||(e=(0,c.Fv)(e));const b={url:e,requestOptions:{...(0,l.Wg)(t)}};let w=(0,c.oh)(e);if(w){const e=await async function(e,t){if(null!=e.responseData)return e.responseData;if(e.headers&&(t.requestOptions.headers={...t.requestOptions.headers,...e.headers}),e.query&&(t.requestOptions.query={...t.requestOptions.query,...e.query}),e.before){let r,o;try{o=await e.before(t)}catch(e){r=O("request:interceptor",e,t)}if((o instanceof Error||o instanceof s.Z)&&(r=O("request:interceptor",o,t)),r)throw e.error&&e.error(r),r;return o}}(w,b);if(null!=e)return{data:e,getHeader:k,httpStatus:200,requestOptions:b.requestOptions,url:b.url};w.after||w.error||(w=null)}if(e=b.url,"image"===(t=b.requestOptions).responseType){if((0,i.Z)("host-webworker")||(0,i.Z)("host-node"))throw O("request:invalid-parameters",new Error("responseType 'image' is not supported in Web Workers or Node environment"),b)}else if(a)throw O("request:invalid-parameters",new Error("Data URLs are not supported for responseType = "+t.responseType),b);if("head"===t.method){if(t.body)throw O("request:invalid-parameters",new Error("body parameter cannot be set when method is 'head'"),b);if(a||d)throw O("request:invalid-parameters",new Error("data and blob URLs are not supported for method 'head'"),b)}if(await async function(){(0,i.Z)("host-webworker")?f||(f=await r.e(9884).then(r.bind(r,29884))):p._abortableFetch||(p._abortableFetch=globalThis.fetch.bind(globalThis))}(),f)return f.execute(e,t);const v=new AbortController;(0,u.fu)(t,(()=>v.abort()));const S={controller:v,credential:void 0,credentialToken:void 0,fetchOptions:void 0,hasToken:!1,interceptor:w,params:b,redoRequest:!1,useIdentity:g.useIdentity,useProxy:!1,useSSL:!1,withCredentials:!1},x=await async function(e){let t,r;await async function(e){const t=e.params.url,r=e.params.requestOptions,s=e.controller.signal,i=r.body;let a=null,l=null;if(y&&"HTMLFormElement"in globalThis&&(i instanceof FormData?a=i:i instanceof HTMLFormElement&&(a=new FormData(i))),"string"==typeof i&&(l=i),e.fetchOptions={cache:r.cacheBust&&!p._abortableFetch.polyfill?"no-cache":"default",credentials:"same-origin",headers:r.headers||{},method:"head"===r.method?"HEAD":"GET",mode:"cors",priority:g.priority,redirect:"follow",signal:s},(a||l)&&(e.fetchOptions.body=a||l),"anonymous"===r.authMode&&(e.useIdentity=!1),e.hasToken=!!(/token=/i.test(t)||r.query?.token||a?.get("token")),!e.hasToken&&o.Z.apiKey&&(0,h.r)(t)&&(r.query||(r.query={}),r.query.token=o.Z.apiKey,e.hasToken=!0),e.useIdentity&&!e.hasToken&&!e.credentialToken&&!q(t)&&!(0,u.Hc)(s)){let o;"immediate"===r.authMode?(await C(),o=await n.id.getCredential(t,{signal:s}),e.credential=o):"no-prompt"===r.authMode?(await C(),o=await n.id.getCredential(t,{prompt:!1,signal:s}).catch((()=>{})),e.credential=o):n.id&&(o=n.id.findCredential(t)),o&&(e.credentialToken=o.token,e.useSSL=!!o.ssl)}}(e);try{do{[t,r]=await N(e)}while(!await P(e,t,r))}catch(r){const o=O("request:server",r,e.params,t);throw o.details.ssl=e.useSSL,e.interceptor&&e.interceptor.error&&e.interceptor.error(o),o}const s=e.params.url;if(r&&/\/sharing\/rest\/(accounts|portals)\/self/i.test(s)){if(!e.hasToken&&!e.credentialToken&&r.user?.username&&!(0,c.kl)(s)){const e=(0,c.P$)(s,!0);e&&g.trustedServers.push(e)}Array.isArray(r.authorizedCrossOriginNoCorsDomains)&&(0,m.Hu)(r.authorizedCrossOriginNoCorsDomains)}const i=e.credential;if(i&&n.id){const e=n.id.findServerInfo(i.server);let t=e&&e.owningSystemUrl;if(t){t=t.replace(/\/?$/,"/sharing");const e=n.id.findCredential(t,i.userId);e&&-1===n.id._getIdenticalSvcIdx(t,e)&&e.resources.unshift(t)}}return{data:r,getHeader:t?e=>t?.headers.get(e):k,httpStatus:t?.status??200,requestOptions:e.params.requestOptions,ssl:e.useSSL,url:e.params.url}}(S);return w?.after?.(x),x}let f;const g=o.Z.request,y="FormData"in globalThis,b=[499,498,403,401],w=["COM_0056","COM_0057","SB_0008"],v=[/\/arcgis\/tokens/i,/\/sharing(\/rest)?\/generatetoken/i,/\/rest\/info/i],k=()=>null,S=Symbol();function x(e){const t=(0,c.P$)(e);return!t||t.endsWith(".arcgis.com")||p._corsServers.includes(t)||(0,c.kl)(t)}function O(e,t,r,o){let n="Error";const i={url:r.url,requestOptions:r.requestOptions,getHeader:k,ssl:!1};if(t instanceof s.Z)return t.details?(t.details=(0,a.d9)(t.details),t.details.url=r.url,t.details.requestOptions=r.requestOptions):t.details=i,t;if(t){const e=o&&(e=>o.headers.get(e)),r=o&&o.status,s=t.message;s&&(n=s),e&&(i.getHeader=e),i.httpStatus=(null!=t.httpCode?t.httpCode:t.code)||r||0,i.subCode=t.subcode,i.messageCode=t.messageCode,"string"==typeof t.details?i.messages=[t.details]:i.messages=t.details,i.raw=S in t?t[S]:t}return(0,u.D_)(t)?(0,u.zE)():new s.Z(e,n,i)}async function C(){n.id||await Promise.all([r.e(6261),r.e(1400),r.e(450)]).then(r.bind(r,73660))}function q(e){return v.some((t=>t.test(e)))}async function N(e){let t=e.params.url;const r=e.params.requestOptions,o=e.fetchOptions??{},s=(0,c.jc)(t)||(0,c.HK)(t),a=r.responseType||"json",l=s?0:null!=r.timeout?r.timeout:g.timeout;let h=!1;if(!s){e.useSSL&&(t=(0,c.hO)(t)),r.cacheBust&&"default"===o.cache&&(t=(0,c.ZN)(t,"request.preventCache",Date.now()));let s={...r.query};e.credentialToken&&(s.token=e.credentialToken);let a=(0,c.B7)(s);(0,i.Z)("esri-url-encodes-apostrophe")&&(a=a.replace(/'/g,"%27"));const l=t.length+1+a.length;let u;h="delete"===r.method||"post"===r.method||"put"===r.method||!!r.body||l>g.maxUrlLength;const p=r.useProxy||!!(0,c.ed)(t);if(p){const e=(0,c.b7)(t);u=e.path,!h&&u.length+1+l>g.maxUrlLength&&(h=!0),e.query&&(s={...e.query,...s})}if("HEAD"===o.method&&(h||p)){if(h){if(l>g.maxUrlLength)throw O("request:invalid-parameters",new Error("URL exceeds maximum length"),e.params);throw O("request:invalid-parameters",new Error("cannot use POST request when method is 'head'"),e.params)}if(p)throw O("request:invalid-parameters",new Error("cannot use proxy when method is 'head'"),e.params)}if(h?(o.method="delete"===r.method?"DELETE":"put"===r.method?"PUT":"POST",r.body?t=(0,c.fl)(t,s):(o.body=(0,c.B7)(s),o.headers||(o.headers={}),o.headers["Content-Type"]="application/x-www-form-urlencoded")):t=(0,c.fl)(t,s),p&&(e.useProxy=!0,t=`${u}?${t}`),s.token&&y&&o.body instanceof FormData&&!(0,d.P)(t)&&o.body.set("token",s.token),r.hasOwnProperty("withCredentials"))e.withCredentials=r.withCredentials;else if(!(0,c.D6)(t,(0,c.TI)()))if((0,c.kl)(t))e.withCredentials=!0;else if(n.id){const r=n.id.findServerInfo(t);r&&r.webTierAuth&&(e.withCredentials=!0)}e.withCredentials&&(o.credentials="include",(0,m.jH)(t)&&await(0,m.jz)(h?(0,c.fl)(t,s):t))}let f,b,w=0,v=!1;l>0&&(w=setTimeout((()=>{v=!0,e.controller.abort()}),l));try{if("native-request-init"===r.responseType)b=o,b.url=t;else if("image"!==r.responseType||"default"!==o.cache||"GET"!==o.method||h||function(e){if(e)for(const t of Object.getOwnPropertyNames(e))if(e[t])return!0;return!1}(r.headers)||!s&&!e.useProxy&&g.proxyUrl&&!x(t)){if(f=await p._abortableFetch(t,o),e.useProxy||function(e){const t=(0,c.P$)(e);t&&!p._corsServers.includes(t)&&p._corsServers.push(t)}(t),"native"===r.responseType)b=f;else if("HEAD"!==o.method)if(f.ok){switch(a){case"array-buffer":b=await f.arrayBuffer();break;case"blob":case"image":b=await f.blob();break;default:b=await f.text()}if(w&&(clearTimeout(w),w=0),"json"===a||"xml"===a||"document"===a)if(b)switch(a){case"json":b=JSON.parse(b);break;case"xml":b=Z(b,"application/xml");break;case"document":b=Z(b,"text/html")}else b=null;if(b){if("array-buffer"===a||"blob"===a){const e=f.headers.get("Content-Type");if(e&&/application\/json|text\/plain/i.test(e)&&b["blob"===a?"size":"byteLength"]<=750)try{const e=await new Response(b).json();e.error&&(b=e)}catch{}}"image"===a&&b instanceof Blob&&(b=await T(URL.createObjectURL(b),e,!0))}}else b=await f.text()}else b=await T(t,e)}catch(o){if("AbortError"===o.name){if(v)throw new Error("Timeout exceeded");throw(0,u.zE)("Request canceled")}if(!(!f&&o instanceof TypeError&&g.proxyUrl)||r.body||"delete"===r.method||"head"===r.method||"post"===r.method||"put"===r.method||e.useProxy||x(t))throw o;e.redoRequest=!0,(0,c.tD)({proxyUrl:g.proxyUrl,urlPrefix:(0,c.P$)(t)??""})}finally{w&&clearTimeout(w)}return[f,b]}function Z(e,t){let r;try{r=(new DOMParser).parseFromString(e,t)}catch{}if(!r||r.getElementsByTagName("parsererror").length)throw new SyntaxError("XML Parse error");return r}async function P(e,t,r){if(e.redoRequest)return e.redoRequest=!1,!1;const o=e.params.requestOptions;if(!t||"native"===o.responseType||"native-request-init"===o.responseType)return!0;let s,i;if(!t.ok)throw s=new Error(`Unable to load ${t.url} status: ${t.status}`),s[S]=r,s;r&&(r.error?s=r.error:"error"===r.status&&Array.isArray(r.messages)&&(s={...r},s[S]=r,s.details=r.messages));let a,l=null;s&&(i=Number(s.code),l=s.hasOwnProperty("subcode")?Number(s.subcode):null,a=s.messageCode,a=a&&a.toUpperCase());const u=o.authMode;if(403===i&&(4===l||s.message&&s.message.toLowerCase().includes("ssl")&&!s.message.toLowerCase().includes("permission"))){if(!e.useSSL)return e.useSSL=!0,!1}else if(!e.hasToken&&e.useIdentity&&("no-prompt"!==u||498===i)&&void 0!==i&&b.includes(i)&&!q(e.params.url)&&(403!==i||a&&!w.includes(a)&&(null==l||2===l&&e.credentialToken))){await C();try{const t=await n.id.getCredential(e.params.url,{error:O("request:server",s,e.params),prompt:"no-prompt"!==u,signal:e.controller.signal,token:e.credentialToken});return e.credential=t,e.credentialToken=t.token,e.useSSL=e.useSSL||t.ssl,!1}catch(t){if("no-prompt"===u)return e.credential=void 0,e.credentialToken=void 0,!1;s=t}}if(s)throw s;return!0}function T(e,t,r=!1){const o=t.controller.signal,n=new Image;return t.withCredentials?n.crossOrigin="use-credentials":n.crossOrigin="anonymous",n.alt="",n.fetchPriority=g.priority,n.src=e,(0,m.fY)(n,e,r,o)}p._abortableFetch=null,p._corsServers=["https://server.arcgisonline.com","https://services.arcgisonline.com"]},98046:(e,t,r)=>{r.d(t,{Z:()=>m});var o,n=r(43697),s=r(22303),i=r(22974),a=r(5600),l=r(75215),u=r(36030),c=r(52011),d=r(899);let h=o=class extends d.Z{constructor(e){super(e),this.algorithm=null,this.fromColor=null,this.toColor=null,this.type="algorithmic"}clone(){return new o({fromColor:(0,i.d9)(this.fromColor),toColor:(0,i.d9)(this.toColor),algorithm:this.algorithm})}};(0,n._)([(0,u.J)({esriCIELabAlgorithm:"cie-lab",esriHSVAlgorithm:"hsv",esriLabLChAlgorithm:"lab-lch"})],h.prototype,"algorithm",void 0),(0,n._)([(0,a.Cb)({type:s.Z,json:{type:[l.z8],write:!0}})],h.prototype,"fromColor",void 0),(0,n._)([(0,a.Cb)({type:s.Z,json:{type:[l.z8],write:!0}})],h.prototype,"toColor",void 0),(0,n._)([(0,a.Cb)({type:["algorithmic"]})],h.prototype,"type",void 0),h=o=(0,n._)([(0,c.j)("esri.rest.support.AlgorithmicColorRamp")],h);const m=h},899:(e,t,r)=>{r.d(t,{Z:()=>l});var o=r(43697),n=r(96674),s=r(5600),i=(r(75215),r(67676),r(52011));let a=class extends n.wq{constructor(e){super(e),this.type=null}};(0,o._)([(0,s.Cb)({readOnly:!0,json:{read:!1,write:!0}})],a.prototype,"type",void 0),a=(0,o._)([(0,i.j)("esri.rest.support.ColorRamp")],a);const l=a},1515:(e,t,r)=>{r.d(t,{Z:()=>d});var o,n=r(43697),s=r(22974),i=r(5600),a=(r(75215),r(52011)),l=r(98046),u=r(899);let c=o=class extends u.Z{constructor(e){super(e),this.colorRamps=null,this.type="multipart"}clone(){return new o({colorRamps:(0,s.d9)(this.colorRamps)})}};(0,n._)([(0,i.Cb)({type:[l.Z],json:{write:!0}})],c.prototype,"colorRamps",void 0),(0,n._)([(0,i.Cb)({type:["multipart"]})],c.prototype,"type",void 0),c=o=(0,n._)([(0,a.j)("esri.rest.support.MultipartColorRamp")],c);const d=c},94593:(e,t,r)=>{r.d(t,{V:()=>i,i:()=>a});var o=r(98046),n=r(899),s=r(1515);const i={key:"type",base:n.Z,typeMap:{algorithmic:o.Z,multipart:s.Z}};function a(e){return e&&e.type?"algorithmic"===e.type?o.Z.fromJSON(e):"multipart"===e.type?s.Z.fromJSON(e):null:null}},71058:(e,t,r)=>{r.d(t,{r:()=>s});var o=r(17452);const n=["elevation3d.arcgis.com","js.arcgis.com","jsdev.arcgis.com","jsqa.arcgis.com","static.arcgis.com"];function s(e){const t=(0,o.P$)(e,!0);return!!t&&t.endsWith(".arcgis.com")&&!n.includes(t)&&!e.endsWith("/sharing/rest/generateToken")}},85958:(e,t,r)=>{r.d(t,{Hu:()=>c,fY:()=>l,jH:()=>d,jz:()=>h});var o=r(68773),n=r(80442),s=r(70586),i=r(95330),a=r(17452);function l(e,t,r=!1,o){return new Promise(((a,l)=>{if((0,i.Hc)(o))return void l(u());let c=()=>{m(),l(new Error(`Unable to load ${t}`))},d=()=>{const t=e;m(),a(t)},h=()=>{if(!e)return;const t=e;m(),t.src="",l(u())};const m=()=>{(0,n.Z)("esri-image-decode")||(e.removeEventListener("error",c),e.removeEventListener("load",d)),c=null,d=null,e=null,(0,s.pC)(o)&&o.removeEventListener("abort",h),h=null,r&&URL.revokeObjectURL(t)};(0,s.pC)(o)&&o.addEventListener("abort",h),(0,n.Z)("esri-image-decode")?e.decode().then(d,c):(e.addEventListener("error",c),e.addEventListener("load",d))}))}function u(){try{return new DOMException("Aborted","AbortError")}catch{const e=new Error;return e.name="AbortError",e}}function c(e){o.Z.request.crossOriginNoCorsDomains||(o.Z.request.crossOriginNoCorsDomains={});const t=o.Z.request.crossOriginNoCorsDomains;for(let r of e)r=r.toLowerCase(),/^https?:\/\//.test(r)?t[(0,a.P$)(r)??""]=0:(t[(0,a.P$)("http://"+r)??""]=0,t[(0,a.P$)("https://"+r)??""]=0)}function d(e){const t=o.Z.request.crossOriginNoCorsDomains;if(t){let r=(0,a.P$)(e);if(r)return r=r.toLowerCase(),!(0,a.D6)(r,(0,a.TI)())&&t[r]<Date.now()-36e5}return!1}async function h(e){const t=o.Z.request.crossOriginNoCorsDomains,r=(0,a.P$)(e);t&&r&&(t[r.toLowerCase()]=Date.now());const n=(0,a.mN)(e);e=n.path,"json"===n.query?.f&&(e+="?f=json");try{await fetch(e,{mode:"no-cors",credentials:"include"})}catch{}}}}]);