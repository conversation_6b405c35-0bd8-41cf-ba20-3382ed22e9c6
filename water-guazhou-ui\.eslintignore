/src/assets
/public
auto-imports.d.ts
components.d.ts
tsconfig.json
yarn.lock
package.json
stats.html
.DS_Store
node_modules
/dist
/dist_demo
*.md
*.png
*.jpg
# local env files
.env.local
.env.*.local
*.scss
# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# /src/views/test
dist_demo/
.eslintcache
.history/*
/lib/*
