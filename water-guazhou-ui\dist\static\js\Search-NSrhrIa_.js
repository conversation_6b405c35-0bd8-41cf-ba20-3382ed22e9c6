import{d as j,r as A,am as W,bB as K,c as L,o as X,Q as Z,bv as $,g as l,n as i,p as m,aw as N,aB as t,aJ as z,av as C,bh as q,an as g,h as u,q as B,i as U,cs as D,F as w,bo as x,bR as ee,by as oe,J as se,bU as le,bW as ae,bz as ne,fe as ie,C as re}from"./index-r0dFAfgr.js";const de={class:"search-root"},pe={key:0,class:"form-item app-header-divider"},ce={key:0,class:"form-item app-header-divider"},ue={key:1,class:"form-item"},te={key:0,class:"form-item-label"},me={key:0,class:"scroll-bar left"},ge={class:"bar-wrapper"},fe={key:1,class:"scroll-bar right"},he={class:"bar-wrapper"},ye={class:"operation-item"},ve={key:0,class:"form-item app-header-divider"},ke={key:0,class:"form-item app-header-divider"},ze=j({__name:"Search",props:{config:{}},setup(R,{expose:T}){const f=R,a=A({showScrollBar:!1,scrollLeft:0,isBottom:!1,operationStyle:{"--operationWidth":"0","--gradientColor":f.config.scrollBarGradientColor},moreFilterVisiable:!1,moreFilterConditions:[],queryParams:{...f.config.defaultParams||{}},showDetail:!1}),y=(s,n)=>{n.onChange&&n.onChange(s,n)},v=(s,n)=>{n.onBlur&&n.onBlur(s,n)},k=(s,n)=>{n.onFocus&&n.onFocus(s,n)},J=()=>{a.showDetail=!a.showDetail},M=()=>{a.showDetail=!1},O=()=>{a.moreFilterVisiable=!a.moreFilterVisiable},G=()=>{a.queryParams={...f.config.defaultParams||{}}},b=()=>{var n;let s=0;if(_.value){const r=(n=_.value)==null?void 0:n.children;if(r!=null&&r.length)for(let c=0;c<r.length;c++)s+=r[c].scrollWidth;a.operationStyle["--operationWidth"]=s+"px"}if(p.value&&H.value){const r=p.value.children;let c=0;if(r!=null&&r.length)for(let V=0;V<r.length;V++)c+=r[V].scrollWidth;const P=p.value.clientWidth;a.showScrollBar=P<c,E()}};W(()=>a.showScrollBar,()=>{K().then(()=>{b()})});const E=()=>{p.value&&(a.showScrollBar?(a.scrollLeft=p.value.scrollLeft<=0?0:p.value.scrollLeft,a.isBottom=p.value.scrollLeft+p.value.clientWidth>p.value.scrollWidth-1):(a.scrollLeft=0,a.isBottom=!0,p.value.scrollLeft=0))},I=s=>{p.value&&(s==="left"&&(p.value.scrollLeft-=120),s==="right"&&(p.value.scrollLeft+=120),E())};W(()=>a.queryParams,()=>{var s,n,r;f.config.static||((s=f.config.filters)==null||s.map(c=>{c.handleHidden&&c.handleHidden(a.queryParams,void 0,c)}),(n=f.config.moreFilters)==null||n.map(c=>{c.handleHidden&&c.handleHidden(a.queryParams,void 0,c)}),(r=f.config.operations)==null||r.map(c=>{c.handleHidden&&c.handleHidden(a.queryParams,void 0,c)}))},{deep:!0,immediate:!0});const p=L(),_=L(),H=L();return W(()=>[f.config.filters,f.config.moreFilters],()=>b()),X(()=>{var s;b(),setTimeout(()=>{b()},500),(s=p.value)==null||s.addEventListener("wheel",n=>{n.preventDefault(),p.value&&(p.value.scrollLeft+=n.deltaY,a.scrollLeft=p.value.scrollLeft,a.isBottom=p.value.scrollLeft+p.value.clientWidth>p.value.scrollWidth-1)},{passive:!1}),window.addEventListener("resize",b)}),Z(()=>{window.removeEventListener("resize",b)}),T({...$(a),toggleMore:O,resetForm:G}),(s,n)=>{var F;const r=oe,c=se,P=le,V=ae,Q=ne,Y=ie;return l(),i("div",de,[m("div",{class:"search-box",style:C(a.operationStyle)},[m("div",{ref_key:"refWrapper",ref:H,class:N({"filter-wrapper":!0})},[m("div",{ref_key:"refScrollable",ref:p,class:N(["scrollable-items",[a.showScrollBar?"show-scroll-bar":""]])},[(l(!0),i(t,null,z(s.config.filters,(e,h)=>(l(),i(t,{key:h},[e.hidden?g("",!0):(l(),i(t,{key:0},[e.type==="divider"?(l(),i("div",pe,n[2]||(n[2]=[m("div",{class:"app-header-divider left"},null,-1),m("div",{class:"app-header-divider right"},null,-1)]))):(l(),i("div",{key:1,class:"form-item",style:C(e.itemContainerStyle)},[e.label?(l(),i("label",{key:0,style:C({width:e.labelWidth,...e.colStyles||{}}),class:"form-item-label"},q(e.label),5)):g("",!0),e.field?(l(),u(r,{key:1,modelValue:a.queryParams[e.field],"onUpdate:modelValue":o=>a.queryParams[e.field]=o,config:e,"popper-class":s.config.popperClass,size:s.config.size,onChange:o=>y(o,e),onBlur:o=>v(o,e),onFocus:o=>k(o,e)},null,8,["modelValue","onUpdate:modelValue","config","popper-class","size","onChange","onBlur","onFocus"])):(l(),u(r,{key:2,config:e,"popper-class":s.config.popperClass,size:s.config.size},null,8,["config","popper-class","size"])),(l(!0),i(t,null,z(e.extraFormItem,(o,S)=>(l(),i(t,{key:S},[o!=null&&o.field?(l(),u(r,{key:0,modelValue:a.queryParams[o.field],"onUpdate:modelValue":d=>a.queryParams[o.field]=d,config:o,"popper-class":s.config.popperClass,size:s.config.size,onChange:d=>y(d,o),onBlur:d=>v(d,o),onFocus:d=>k(d,o)},null,8,["modelValue","onUpdate:modelValue","config","popper-class","size","onChange","onBlur","onFocus"])):(l(),u(r,{key:1,config:o,"popper-class":s.config.popperClass,size:s.config.size},null,8,["config","popper-class","size"]))],64))),128))],4))],64))],64))),128)),(l(!0),i(t,null,z(s.config.moreFilters,(e,h)=>(l(),i(t,{key:h},[e.hidden?g("",!0):(l(),i(t,{key:0},[e.type==="divider"?(l(),i("div",ce,n[3]||(n[3]=[m("div",{class:"app-header-divider left"},null,-1),m("div",{class:"app-header-divider right"},null,-1)]))):(l(),i("div",ue,[e.label?(l(),i("label",te,q(e.label),1)):g("",!0),e.field?(l(),u(r,{key:1,modelValue:a.queryParams[e.field],"onUpdate:modelValue":o=>a.queryParams[e.field]=o,config:e,"popper-class":s.config.popperClass,size:s.config.size,onChange:o=>y(o,e),onBlur:o=>v(o,e),onFocus:o=>k(o,e)},null,8,["modelValue","onUpdate:modelValue","config","popper-class","size","onChange","onBlur","onFocus"])):(l(),u(r,{key:2,config:e,"popper-class":s.config.popperClass,size:s.config.size},null,8,["config","popper-class","size"]))]))],64))],64))),128))],2),a.showScrollBar&&a.scrollLeft>0?(l(),i("div",me,[m("div",ge,[B(U(D),{class:"scroll-bar-icon",icon:"ep:d-arrow-left",onClick:n[0]||(n[0]=e=>I("left"))})])])):g("",!0),a.showScrollBar&&!a.isBottom?(l(),i("div",fe,[m("div",he,[B(U(D),{class:"scroll-bar-icon",icon:"ep:d-arrow-right",onClick:n[1]||(n[1]=e=>I("right"))})])])):g("",!0)],512),(F=s.config.operations)!=null&&F.length||a.showScrollBar?(l(),i("div",{key:0,ref_key:"refOperations",ref:_,class:"operation-items"},[(l(!0),i(t,null,z(s.config.operations,(e,h)=>(l(),i(t,{key:h},[e.hidden?g("",!0):(l(),i("div",{key:0,class:"operation-item",onClick:M},[e.field?(l(),u(r,{key:0,modelValue:a.queryParams[e.field],"onUpdate:modelValue":o=>a.queryParams[e.field]=o,config:e,"popper-class":s.config.popperClass,size:s.config.size,onChange:o=>y(o,e),onBlur:o=>v(o,e),onFocus:o=>k(o,e)},null,8,["modelValue","onUpdate:modelValue","config","popper-class","size","onChange","onBlur","onFocus"])):(l(),u(r,{key:1,config:e,"popper-class":s.config.popperClass,size:s.config.size},null,8,["config","popper-class","size"]))]))],64))),128)),m("div",ye,[a.showScrollBar?(l(),u(c,{key:0,class:"stateSwitch",type:"text",title:a.showDetail?"点击收起":"点击展开",link:"",onClick:J},{default:w(()=>[B(U(D),{icon:a.showDetail?"tabler:arrows-minimize":"tabler:arrows-maximize"},null,8,["icon"])]),_:1},8,["title"])):g("",!0)])],512)):g("",!0)],4),B(Y,null,{default:w(()=>[x(B(Q,{class:"detail"},{default:w(()=>[B(V,{gutter:10,class:"show_detail"},{default:w(()=>[(l(!0),i(t,null,z(s.config.filters,(e,h)=>(l(),u(P,{key:h,xs:e.xs??24,sm:e.sm??12,md:e.md??6,lg:e.lg??5,xl:e.xl??4},{default:w(()=>[e.type==="divider"?(l(),i("div",ve,n[4]||(n[4]=[m("div",{class:"app-header-divider left"},null,-1),m("div",{class:"app-header-divider right"},null,-1)]))):(l(),i("div",{key:1,class:"form-item",style:C(e.itemContainerStyle)},[e.label?(l(),i("label",{key:0,style:C({width:e.labelWidth,...e.colStyles||{}}),class:"form-item-label"},q(e.label),5)):g("",!0),e.field?(l(),u(r,{key:1,modelValue:a.queryParams[e.field],"onUpdate:modelValue":o=>a.queryParams[e.field]=o,config:e,"popper-class":s.config.popperClass,size:s.config.size,onChange:o=>y(o,e),onBlur:o=>v(o,e),onFocus:o=>k(o,e)},null,8,["modelValue","onUpdate:modelValue","config","popper-class","size","onChange","onBlur","onFocus"])):(l(),u(r,{key:2,config:e,"popper-class":s.config.popperClass,size:s.config.size},null,8,["config","popper-class","size"])),(l(!0),i(t,null,z(e.extraFormItem,(o,S)=>(l(),i(t,{key:S},[o!=null&&o.field?(l(),u(r,{key:0,modelValue:a.queryParams[o.field],"onUpdate:modelValue":d=>a.queryParams[o.field]=d,config:o,"popper-class":s.config.popperClass,size:s.config.size,onChange:d=>y(d,o),onBlur:d=>v(d,o),onFocus:d=>k(d,o)},null,8,["modelValue","onUpdate:modelValue","config","popper-class","size","onChange","onBlur","onFocus"])):(l(),u(r,{key:1,config:o,"popper-class":s.config.popperClass,size:s.config.size},null,8,["config","popper-class","size"]))],64))),128))],4))]),_:2},1032,["xs","sm","md","lg","xl"]))),128)),(l(!0),i(t,null,z(s.config.moreFilters,(e,h)=>(l(),u(P,{key:h,xs:e.xs??24,sm:e.sm??12,md:e.md??6,lg:e.lg??5,xl:e.xl??4},{default:w(()=>[e.type==="divider"?(l(),i("div",ke,n[5]||(n[5]=[m("div",{class:"app-header-divider left"},null,-1),m("div",{class:"app-header-divider right"},null,-1)]))):(l(),i("div",{key:1,class:"form-item",style:C(e.itemContainerStyle)},[e.label?(l(),i("label",{key:0,style:C({width:e.labelWidth,...e.colStyles||{}}),class:"form-item-label"},q(e.label),5)):g("",!0),e.field?(l(),u(r,{key:1,modelValue:a.queryParams[e.field],"onUpdate:modelValue":o=>a.queryParams[e.field]=o,config:e,"popper-class":s.config.popperClass,size:s.config.size,onChange:o=>y(o,e),onBlur:o=>v(o,e),onFocus:o=>k(o,e)},null,8,["modelValue","onUpdate:modelValue","config","popper-class","size","onChange","onBlur","onFocus"])):(l(),u(r,{key:2,config:e,"popper-class":s.config.popperClass,size:s.config.size},null,8,["config","popper-class","size"])),(l(!0),i(t,null,z(e.extraFormItem,(o,S)=>(l(),i(t,{key:S},[o!=null&&o.field?(l(),u(r,{key:0,modelValue:a.queryParams[o.field],"onUpdate:modelValue":d=>a.queryParams[o.field]=d,config:o,"popper-class":s.config.popperClass,size:s.config.size,onChange:d=>y(d,o),onBlur:d=>v(d,o),onFocus:d=>k(d,o)},null,8,["modelValue","onUpdate:modelValue","config","popper-class","size","onChange","onBlur","onFocus"])):(l(),u(r,{key:1,config:o,"popper-class":s.config.popperClass,size:s.config.size},null,8,["config","popper-class","size"]))],64))),128))],4))]),_:2},1032,["xs","sm","md","lg","xl"]))),128))]),_:1})]),_:1},512),[[ee,a.showDetail]])]),_:1})])}}}),be=re(ze,[["__scopeId","data-v-e241c752"]]);export{be as _};
