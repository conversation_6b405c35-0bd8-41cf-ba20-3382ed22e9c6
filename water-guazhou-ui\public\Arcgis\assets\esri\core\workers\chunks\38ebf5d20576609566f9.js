"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5115],{28924:(e,t,r)=>{r.d(t,{Z:()=>s});class s{constructor(){this.declaredRootClass="esri.arcade.featureSetCollection",this._layerById={},this._layerByName={}}add(e,t,r){this._layerById[t]=r,this._layerByName[e]=r}async featureSetByName(e,t=!0,r=["*"]){return void 0===this._layerByName[e]?null:this._layerByName[e]}async featureSetById(e,t=!0,r=["*"]){return void 0===this._layerById[e]?null:this._layerById[e]}castToText(e=!1){return"object, FeatureSetCollection"}}},50954:(e,t,r)=>{r.r(t),r.d(t,{constructAssociationMetaDataFeatureSetFromUrl:()=>X,constructFeatureSet:()=>K,constructFeatureSetFromPortalItem:()=>ne,constructFeatureSetFromRelationship:()=>Y,constructFeatureSetFromUrl:()=>J,convertToFeatureSet:()=>ie,createFeatureSetCollectionFromMap:()=>re,createFeatureSetCollectionFromService:()=>se,initialiseMetaDataCache:()=>V});var s=r(3172),i=r(28924),n=r(84328),a=r(38171),l=r(61363),o=r(70409),u=r(85065),d=r(91136),c=r(90961),h=r(3823),p=r(90658),f=r(36515),y=r(95002),_=r(59987),g=r(41534);class m{constructor(){this.field="",this.tofieldname="",this.typeofstat="MIN",this.workingexpr=null}clone(){const e=new m;return e.field=this.field,e.tofieldname=this.tofieldname,e.typeofstat=this.typeofstat,e.workingexpr=this.workingexpr,e}static parseStatField(e,t,r){const s=new m;s.field=e;const i=g.WhereClause.create(t,r),n=function(e){if("function"===e.parseTree.type){if(0===e.parseTree.args.value.length)return{name:e.parseTree.name,expr:null};if(e.parseTree.args.value.length>1)throw new _.eS(_.f.MissingStatisticParameters);const t=g.WhereClause.create((0,f.XF)(e.parseTree.args.value[0],p.Bj.Standardised,e.parameters),e.fieldsIndex);return{name:e.parseTree.name,expr:t}}return null}(i);if(null===n)throw new _.eS(_.f.UnsupportedSqlFunction,{function:""});const a=n.name.toUpperCase().trim();if("MIN"===a){if(s.typeofstat="MIN",s.workingexpr=n.expr,null===i)throw new _.eS(_.f.InvalidFunctionParameters,{function:"min"})}else if("MAX"===a){if(s.typeofstat="MAX",s.workingexpr=n.expr,null===i)throw new _.eS(_.f.InvalidFunctionParameters,{function:"max"})}else if("COUNT"===a)s.typeofstat="COUNT",s.workingexpr=n.expr;else if("STDEV"===a){if(s.typeofstat="STDDEV",s.workingexpr=n.expr,null===i)throw new _.eS(_.f.InvalidFunctionParameters,{function:"stdev"})}else if("SUM"===a){if(s.typeofstat="SUM",s.workingexpr=n.expr,null===i)throw new _.eS(_.f.InvalidFunctionParameters,{function:"sum"})}else if("MEAN"===a){if(s.typeofstat="AVG",s.workingexpr=n.expr,null===i)throw new _.eS(_.f.InvalidFunctionParameters,{function:a})}else if("AVG"===a){if(s.typeofstat="AVG",s.workingexpr=n.expr,null===i)throw new _.eS(_.f.InvalidFunctionParameters,{function:"avg"})}else{if("VAR"!==a)throw new _.eS(_.f.UnsupportedSqlFunction,{function:a});if(s.typeofstat="VAR",s.workingexpr=n.expr,null===i)throw new _.eS(_.f.InvalidFunctionParameters,{function:"var"})}return s}toStatisticsName(){switch(this.typeofstat.toUpperCase()){case"MIN":return"min";case"MAX":return"max";case"SUM":return"sum";case"COUNT":default:return"count";case"VAR":return"var";case"STDDEV":return"stddev";case"AVG":return"avg"}}}var w=r(44543),S=r(82971),b=r(1231),F=r(99514);function I(e){if(!e)return"COUNT";switch(e.toLowerCase()){case"max":return"MAX";case"var":case"variance":return"VAR";case"avg":case"average":case"mean":return"AVG";case"min":return"MIN";case"sum":return"SUM";case"stdev":case"stddev":return"STDDEV";case"count":return"COUNT"}return"COUNT"}class v extends d.Z{constructor(e){super(e),this._decodedStatsfield=[],this._decodedGroupbyfield=[],this._candosimplegroupby=!0,this.phsyicalgroupbyfields=[],this.objectIdField="ROW__ID",this._internalObjectIdField="ROW__ID",this._adaptedFields=[],this.declaredClass="esri.arcade.featureset.actions.Aggregate",this._uniqueIds=1,this._maxQuery=10,this._maxProcessing=10,this._parent=e.parentfeatureset,this._config=e}isTable(){return!0}async _getSet(e){if(null===this._wset){const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,this._wset}return this._wset}_isInFeatureSet(){return p.dj.InFeatureSet}_nextUniqueName(e){for(;1===e["T"+this._uniqueIds.toString()];)this._uniqueIds++;const t="T"+this._uniqueIds.toString();return e[t]=1,t}_convertToEsriFieldType(e){return e}_initialiseFeatureSet(){const e={};let t=!1,r=1;const s=this._parent?this._parent.getFieldsIndex():new F.Z([]);for(this.objectIdField="ROW__ID",this.globalIdField="";!1===t;){let e=!1;for(let t=0;t<this._config.groupbyfields.length;t++)if(this._config.groupbyfields[t].name.toLowerCase()===this.objectIdField.toLowerCase()){e=!0;break}if(!1===e)for(let t=0;t<this._config.statsfields.length;t++)if(this._config.statsfields[t].name.toLowerCase()===this.objectIdField.toLowerCase()){e=!0;break}!1===e?t=!0:(this.objectIdField="ROW__ID"+r.toString(),r++)}for(const e of this._config.statsfields){const t=new m;t.field=e.name,t.tofieldname=e.name,t.workingexpr=e.expression instanceof g.WhereClause?e.expression:g.WhereClause.create(e.expression,s),t.typeofstat=I(e.statistic),this._decodedStatsfield.push(t)}this._decodedGroupbyfield=[];for(const e of this._config.groupbyfields){const t={name:e.name,singlefield:null,tofieldname:e.name,expression:e.expression instanceof g.WhereClause?e.expression:g.WhereClause.create(e.expression,s)};this._decodedGroupbyfield.push(t)}if(null!==this._parent){this.geometryType=this._parent.geometryType,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField="";for(const t of this._parent.fields)e[t.name.toUpperCase()]=1;this.types=null}else this.geometryType=p.Qk.point,this.typeIdField="",this.types=null,this.spatialReference=new S.Z({wkid:4326});this.fields=[];const i=new m;i.field=this._nextUniqueName(e),i.tofieldname=this.objectIdField,i.workingexpr=g.WhereClause.create(this._parent.objectIdField,this._parent.getFieldsIndex()),i.typeofstat="MIN",this._decodedStatsfield.push(i);for(const t of this._decodedGroupbyfield){const r=new b.Z;if(t.name=this._nextUniqueName(e),r.name=t.tofieldname,r.alias=r.name,(0,f.y5)(t.expression)){const e=this._parent.getField((0,f.zR)(t.expression,p.Bj.Standardised));if(!e)throw new _.EN(_.H9.AggregationFieldNotFound);t.name=e.name,t.singlefield=e.name,this.phsyicalgroupbyfields.push(e.name),r.type=e.type}else{r.type=this._convertToEsriFieldType((0,f.DT)(t.expression,this._parent.fields));const e=new b.Z;e.name=t.name,e.alias=e.name,this.phsyicalgroupbyfields.push(t.name),this._adaptedFields.push(new o.yN(e,t.expression)),this._candosimplegroupby=!1}this.fields.push(r)}if(this._adaptedFields.length>0)for(const e of this._parent.fields)this._adaptedFields.push(new o.$X(e));for(let t=0;t<this._decodedStatsfield.length;t++){const r=new b.Z;let s=null;const i=this._decodedStatsfield[t];i.field=this._nextUniqueName(e),i.tofieldname===this.objectIdField&&(this._internalObjectIdField=i.field),r.name=i.tofieldname,r.alias=r.name;const n=null!==i.workingexpr&&(0,f.y5)(i.workingexpr)?(0,f.zR)(i.workingexpr,p.Bj.Standardised):"";switch(this._decodedStatsfield[t].typeofstat){case"SUM":if(""!==n){if(s=this._parent.getField(n),!s)throw new _.EN(_.H9.AggregationFieldNotFound);r.type=s.type}else r.type="double";break;case"MIN":case"MAX":if(""!==n){if(s=this._parent.getField(n),!s)throw new _.EN(_.H9.AggregationFieldNotFound);r.type=s.type}else r.type="double";break;case"COUNT":r.type="integer";break;case"STDDEV":case"VAR":case"AVG":if(""!==n&&(s=this._parent.getField(n),!s))throw new _.EN(_.H9.AggregationFieldNotFound);r.type="double"}this.fields.push(r)}}async _canDoAggregates(){return!1}async _getFeatures(e,t,r,s){-1!==t&&this._featureCache[t];const i=this._maxQuery;return!0===this._checkIfNeedToExpandKnownPage(e,i)?(await this._expandPagedSet(e,i,0,0,s),this._getFeatures(e,t,r,s)):"success"}async _getFilteredSet(e,t,r,s,i){if(""!==e)return new c.Z([],[],!0,null);let a=null;const l={ordered:!1,nowhereclause:!1};if(await this._ensureLoaded(),null!==r)for(let e=0;e<this._decodedStatsfield.length;e++)if(!0===(0,f.hq)(r,this._decodedStatsfield[e].tofieldname)){l.nowhereclause=!0,r=null;break}if(null!==s){l.ordered=!0;for(let e=0;e<this._decodedStatsfield.length;e++)if(!0===s.scanForField(this._decodedStatsfield[e].tofieldname)){s=null,l.ordered=!1;break}if(null!==s)for(const e of this._decodedGroupbyfield)if(null===e.singlefield&&!0===s.scanForField(e.tofieldname)){s=null,l.ordered=!1;break}}if(!1!==this._candosimplegroupby&&await this._parent._canDoAggregates(this.phsyicalgroupbyfields,this._decodedStatsfield,"",null,null)){let e=null;r&&(e=this._reformulateWhereClauseWithoutGroupByFields(r));let t=null;s&&(t=this._reformulateOrderClauseWithoutGroupByFields(s));const n=await this._parent._getAggregatePagesDataSourceDefinition(this.phsyicalgroupbyfields,this._decodedStatsfield,"",null,e,t,this._internalObjectIdField);return this._checkCancelled(i),a=!0===l.nowhereclause?new c.Z(n._candidates.slice(0).concat(n._known.slice(0)),[],!0===l.ordered&&n._ordered,this._clonePageDefinition(n.pagesDefinition)):new c.Z(n._candidates.slice(0),n._known.slice(0),!0===l.ordered&&n._ordered,this._clonePageDefinition(n.pagesDefinition)),a}let d=this._parent;if(this._adaptedFields.length>0&&(d=new o.Xx({parentfeatureset:this._parent,adaptedFields:this._adaptedFields,extraFilter:null})),!0===l.nowhereclause)a=new c.Z(["GETPAGES"],[],!1,{aggregatefeaturesetpagedefinition:!0,resultOffset:0,resultRecordCount:this._maxQuery,internal:{fullyResolved:!1,workingItem:null,type:"manual",iterator:null,set:[],subfeatureset:new u.Z({parentfeatureset:d,orderbyclause:new h.Z(this.phsyicalgroupbyfields.join(",")+","+this._parent.objectIdField+" ASC")})}});else{let e=d;if(null!==r){let t=null;r&&(t=this._reformulateWhereClauseWithoutGroupByFields(r)),e=new n.Z({parentfeatureset:e,whereclause:t})}a=new c.Z(["GETPAGES"],[],!1,{aggregatefeaturesetpagedefinition:!0,resultOffset:0,resultRecordCount:this._maxQuery,internal:{fullyResolved:!1,workingItem:null,type:"manual",iterator:null,set:[],subfeatureset:new u.Z({parentfeatureset:e,orderbyclause:new h.Z(this.phsyicalgroupbyfields.join(",")+","+this._parent.objectIdField+" ASC")})}})}return a}_reformulateWhereClauseWithoutStatsFields(e){for(const t of this._decodedStatsfield)e=(0,f.bB)(e,t.tofieldname,(0,f.zR)(t.workingexpr,p.Bj.Standardised),this._parent.getFieldsIndex());return e}_reformulateWhereClauseWithoutGroupByFields(e){for(const t of this._decodedGroupbyfield)t.tofieldname!==t.name&&(e=(0,f.bB)(e,t.tofieldname,(0,f.zR)(t.expression,p.Bj.Standardised),this._parent.getFieldsIndex()));return e}_reformulateOrderClauseWithoutGroupByFields(e){const t=[];for(const e of this._decodedGroupbyfield)e.tofieldname!==e.name&&t.push({field:e.tofieldname,newfield:e.name});return t.length>0?e.replaceFields(t):e}_clonePageDefinition(e){return null===e?null:!0===e.aggregatefeaturesetpagedefinition?{aggregatefeaturesetpagedefinition:!0,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,internal:e.internal}:this._parent._clonePageDefinition(e)}async _refineSetBlock(e,t,r){return!0===this._checkIfNeedToExpandCandidatePage(e,this._maxQuery)?(await this._expandPagedSet(e,this._maxQuery,0,0,r),this._refineSetBlock(e,t,r)):(this._checkCancelled(r),e._candidates.length,this._refineKnowns(e,t),e._candidates.length,e._candidates.length,e)}_expandPagedSet(e,t,r,s,i){return this._expandPagedSetFeatureSet(e,t,r,s,i)}async _getPhysicalPage(e,t,r){if(!0===e.pagesDefinition.aggregatefeaturesetpagedefinition)return this._sequentialGetPhysicalItem(e,e.pagesDefinition.resultRecordCount,r,[]);const s=await this._getAgregagtePhysicalPage(e,t,r);for(const e of s){const t={geometry:e.geometry,attributes:{}};for(const r of this._decodedGroupbyfield)t.attributes[r.tofieldname]=e.attributes[r.name];for(const r of this._decodedStatsfield)t.attributes[r.tofieldname]=e.attributes[r.field];this._featureCache[t.attributes[this.objectIdField]]=new a.Z(t)}return s.length}_sequentialGetPhysicalItem(e,t,r,s){return new Promise(((i,n)=>{null===e.pagesDefinition.internal.iterator&&(e.pagesDefinition.internal.iterator=e.pagesDefinition.internal.subfeatureset.iterator(r)),!0===e.pagesDefinition.internal.fullyResolved||0===t?i(s.length):this._nextAggregateItem(e,t,r,s,(n=>{null===n?i(s.length):(t-=1,i(this._sequentialGetPhysicalItem(e,t,r,s)))}),n)}))}_nextAggregateItem(e,t,r,s,i,n){try{(0,l.W)(e.pagesDefinition.internal.iterator.next()).then((a=>{if(null===a)if(null!==e.pagesDefinition.internal.workingItem){const t=this._calculateAndAppendAggregateItem(e.pagesDefinition.internal.workingItem);s.push(t),e.pagesDefinition.internal.workingItem=null,e.pagesDefinition.internal.set.push(t.attributes[this.objectIdField]),e.pagesDefinition.internal.fullyResolved=!0,i(null)}else e.pagesDefinition.internal.fullyResolved=!0,i(null);else{const l=this._generateAggregateHash(a);if(null===e.pagesDefinition.internal.workingItem)e.pagesDefinition.internal.workingItem={features:[a],id:l};else{if(l!==e.pagesDefinition.internal.workingItem.id){const r=this._calculateAndAppendAggregateItem(e.pagesDefinition.internal.workingItem);return s.push(r),e.pagesDefinition.internal.workingItem=null,e.pagesDefinition.internal.set.push(r.attributes[this.objectIdField]),t-=1,e.pagesDefinition.internal.workingItem={features:[a],id:l},void i(r)}e.pagesDefinition.internal.workingItem.features.push(a)}this._nextAggregateItem(e,t,r,s,i,n)}}),n)}catch(e){n(e)}}_calculateFieldStat(e,t,r){const s=[];for(let r=0;r<e.features.length;r++)if(null!==t.workingexpr){const i=t.workingexpr.calculateValue(e.features[r]);null!==i&&s.push(i)}else s.push(null);switch(t.typeofstat){case"MIN":r.attributes[t.tofieldname]=(0,y.tj)("min",s,-1);break;case"MAX":r.attributes[t.tofieldname]=(0,y.tj)("max",s,-1);break;case"SUM":r.attributes[t.tofieldname]=(0,y.tj)("sum",s,-1);break;case"COUNT":r.attributes[t.tofieldname]=s.length;break;case"VAR":r.attributes[t.tofieldname]=(0,y.tj)("var",s,-1);break;case"STDDEV":r.attributes[t.tofieldname]=(0,y.tj)("stddev",s,-1);break;case"AVG":r.attributes[t.tofieldname]=(0,y.tj)("avg",s,-1)}return!0}_calculateAndAppendAggregateItem(e){const t={attributes:{},geometry:null};for(const r of this._decodedGroupbyfield){const s=r.singlefield?e.features[0].attributes[r.singlefield]:r.expression.calculateValue(e.features[0]);t.attributes[r.tofieldname]=s}for(const r of this._decodedStatsfield)this._calculateFieldStat(e,r,t);const r=[];for(let s=0;s<this._decodedStatsfield.length;s++)r.push(this._calculateFieldStat(e,this._decodedStatsfield[s],t));return this._featureCache[t.attributes[this.objectIdField]]=new a.Z({attributes:t.attributes,geometry:t.geometry}),t}_generateAggregateHash(e){let t="";for(const r of this._decodedGroupbyfield){const s=r.singlefield?e.attributes[r.singlefield]:r.expression.calculateValue(e);t+=null==s?":":":"+s.toString()}return(0,w.F)(t,w.M.String)}async _stat(){return{calculated:!1}}async getFeatureByObjectId(){return null}static registerAction(){d.Z._featuresetFunctions.groupby=function(e,t){return new v({parentfeatureset:this,groupbyfields:e,statsfields:t})}}}var C=r(53073),R=r(63991),T=r(80692),x=r(40330),D=r(33955),k=r(19238),N=r(98732),E=(r(20102),r(80442),r(92604),r(10158),r(4967)),A=(r(68773),r(67676),r(17452),r(67900),r(8744),r(34599)),L=r(74889),j=r(14165),O=(r(98326),r(56545),r(41818)),P=(r(66577),r(5396)),Z=r(75935),G=(r(16306),r(28141),r(78760)),U=r(58539),q=r(33586);class B extends d.Z{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerDynamic",this._removeGeometry=!1,this._overrideFields=null,this.formulaCredential=null,this._pageJustIds=!1,this._requestStandardised=!1,this._useDefinitionExpression=!0,this._cachedDateMetaData={},e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,void 0!==e.outFields&&(this._overrideFields=e.outFields),void 0!==e.includeGeometry&&(this._removeGeometry=!1===e.includeGeometry)}_maxQueryRate(){return p.tI}end(){return this._layer}optimisePagingFeatureQueries(e){this._pageJustIds=e}get urlQueryPath(){return this._layer.parsedUrl.path||""}convertQueryToLruCacheKey(e){const t=this.urlQueryPath+","+(0,p.hd)(e.toJSON());return(0,w.F)(t,w.M.String)}async loadImpl(){return!0===this._layer.loaded?(this._initialiseFeatureSet(),this):(await this._layer.load(),this._initialiseFeatureSet(),this)}_initialiseFeatureSet(){if(null==this.spatialReference&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._layer.geometryType,this.fields=this._layer.fields.slice(0),this._layer.outFields)if(1===this._layer.outFields.length&&"*"===this._layer.outFields[0]);else{const e=[];for(const t of this.fields)if("oid"===t.type)e.push(t);else for(const r of this._layer.outFields)if(r.toLowerCase()===t.name.toLowerCase()){e.push(t);break}this.fields=e}if(null!==this._overrideFields)if(1===this._overrideFields.length&&"*"===this._overrideFields[0])this._overrideFields=null;else{const e=[],t=[];for(const r of this.fields)if("oid"===r.type)e.push(r),t.push(r.name);else for(const s of this._overrideFields)if(s.toLowerCase()===r.name.toLowerCase()){e.push(r),t.push(r.name);break}this.fields=e,this._overrideFields=t}if(this._layer.source&&this._layer.source.sourceJSON){const e=this._layer.source.sourceJSON.currentVersion;!0===this._layer.source.sourceJSON.useStandardizedQueries?(this._databaseType=p.Bj.StandardisedNoInterval,null!=e&&e>=10.61&&(this._databaseType=p.Bj.Standardised)):null!=e&&(e>=10.5&&(this._databaseType=p.Bj.StandardisedNoInterval,this._requestStandardised=!0),e>=10.61&&(this._databaseType=p.Bj.Standardised))}this.objectIdField=this._layer.objectIdField;for(const e of this.fields)"global-id"===e.type&&(this.globalIdField=e.name);this.hasM=this._layer.supportsM,this.hasZ=this._layer.supportsZ,this.typeIdField=this._layer.typeIdField??"",this.types=this._layer.types}_isInFeatureSet(){return p.dj.InFeatureSet}async _refineSetBlock(e){return e}_candidateIdTransform(e){return e}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,t}return this._wset}async _runDatabaseProbe(e){await this._ensureLoaded();const t=new j.Z;this.datesInUnknownTimezone&&(t.timeReferenceUnknownClient=!0),t.where=e.replace("OBJECTID",this._layer.objectIdField);try{return await this._layer.queryObjectIds(t),!0}catch(e){return!1}}_canUsePagination(){return!(!this._layer.capabilities||!this._layer.capabilities.query||!0!==this._layer.capabilities.query.supportsPagination)}_cacheableFeatureSetSourceKey(){return this._layer.url}pbfSupportedForQuery(e){const t=this._layer?.capabilities?.query;return!e.outStatistics&&!0===t?.supportsFormatPBF&&!0===t?.supportsQuantizationEditMode}async queryPBF(e){e.quantizationParameters={mode:"edit"};const t=await(0,A.qp)(this._layer.parsedUrl,e,new G.J({}));return L.Z.fromJSON((0,N.cn)(t.data)).unquantize()}get gdbVersion(){return this._layer&&this._layer.capabilities&&this._layer.capabilities.data&&this._layer.capabilities.data.isVersioned?this._layer.gdbVersion?this._layer.gdbVersion:"SDE.DEFAULT":""}nativeCapabilities(){return{title:this._layer.title??"",source:this,canQueryRelated:!0,capabilities:this._layer.capabilities,databaseType:this._databaseType,requestStandardised:this._requestStandardised}}executeQuery(e,t){const r="execute"===t?E.e:"executeForCount"===t?O.P:P.G,s="execute"===t&&this.pbfSupportedForQuery(e);let i=null;if(this.recentlyUsedQueries){const t=this.convertQueryToLruCacheKey(e);i=this.recentlyUsedQueries.getFromCache(t),null===i&&(i=!0!==s?r(this._layer.parsedUrl.path,e):this.queryPBF(e),this.recentlyUsedQueries.addToCache(t,i),i=i.catch((e=>{throw this.recentlyUsedQueries?.removeFromCache(t),e})))}return this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:e,method:t}),null===i&&(i=!0!==s?r(this._layer.parsedUrl.path,e):this.queryPBF(e)),i}async _getFilteredSet(e,t,r,s,i){const n=await this.databaseType();if(this.isTable()&&t&&null!==e&&""!==e)return new c.Z([],[],!0,null);if(this._canUsePagination())return this._getFilteredSetUsingPaging(e,t,r,s,i);let a="",l=!1;null!==s&&this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsOrderBy&&(a=s.constructClause(),l=!0);const o=new j.Z;this.datesInUnknownTimezone&&(o.timeReferenceUnknownClient=!0),o.where=null===r?null===t?"1=1":"":(0,f.zR)(r,n),this._requestStandardised&&(o.sqlFormat="standard"),o.spatialRelationship=this._makeRelationshipEnum(e),o.outSpatialReference=this.spatialReference,o.orderByFields=""!==a?a.split(","):null,o.geometry=null===t?null:t,o.relationParameter=this._makeRelationshipParam(e);let u=await this.executeQuery(o,"executeForIds");return null===u&&(u=[]),this._checkCancelled(i),new c.Z([],u,l,null)}_expandPagedSet(e,t,r,s,i){return this._expandPagedSetFeatureSet(e,t,r,s,i)}async _getFilteredSetUsingPaging(e,t,r,s,i){let n="",a=!1;null!==s&&this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsOrderBy&&(n=s.constructClause(),a=!0);const l=await this.databaseType();let o=null===r?null===t?"1=1":"":(0,f.zR)(r,l);this._layer.definitionExpression&&this._useDefinitionExpression&&(o=""!==o?"(("+this._layer.definitionExpression+") AND ("+o+"))":this._layer.definitionExpression);let u=this._maxQueryRate();const d=this._layer.capabilities?.query.maxRecordCount;null!=d&&d<u&&(u=d);let h=null;if(!0===this._pageJustIds)h=new c.Z([],["GETPAGES"],a,{spatialRel:this._makeRelationshipEnum(e),relationParam:this._makeRelationshipParam(e),outFields:this._layer.objectIdField,resultRecordCount:u,resultOffset:0,geometry:null===t?null:t,where:o,orderByFields:n,returnGeometry:!1,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}});else{let r=!0;!0===this._removeGeometry&&(r=!1);const s=null!==this._overrideFields?this._overrideFields:this._fieldsIncludingObjectId(this._layer.outFields?this._layer.outFields:["*"]);h=new c.Z([],["GETPAGES"],a,{spatialRel:this._makeRelationshipEnum(e),relationParam:this._makeRelationshipParam(e),outFields:s.join(","),resultRecordCount:u,resultOffset:0,geometry:null===t?null:t,where:o,orderByFields:n,returnGeometry:r,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}})}return await this._expandPagedSet(h,u,0,1,i),h}_clonePageDefinition(e){return null===e?null:!0!==e.groupbypage?{groupbypage:!1,spatialRel:e.spatialRel,relationParam:e.relationParam,outFields:e.outFields,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,geometry:e.geometry,where:e.where,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}:{groupbypage:!0,spatialRel:e.spatialRel,relationParam:e.relationParam,outFields:e.outFields,resultRecordCount:e.resultRecordCount,useOIDpagination:e.useOIDpagination,generatedOid:e.generatedOid,groupByFieldsForStatistics:e.groupByFieldsForStatistics,resultOffset:e.resultOffset,outStatistics:e.outStatistics,geometry:e.geometry,where:e.where,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}}async _getPhysicalPage(e,t,r){const s=e.pagesDefinition.internal.lastRetrieved,i=s,n=e.pagesDefinition.internal.lastPage,a=new j.Z;this._requestStandardised&&(a.sqlFormat="standard"),this.datesInUnknownTimezone&&(a.timeReferenceUnknownClient=!0),a.spatialRelationship=e.pagesDefinition.spatialRel,a.relationParameter=e.pagesDefinition.relationParam,a.outFields=e.pagesDefinition.outFields.split(","),a.num=e.pagesDefinition.resultRecordCount,a.start=e.pagesDefinition.internal.lastPage,a.geometry=e.pagesDefinition.geometry,a.where=e.pagesDefinition.where,a.orderByFields=""!==e.pagesDefinition.orderByFields?e.pagesDefinition.orderByFields.split(","):null,a.returnGeometry=e.pagesDefinition.returnGeometry,a.outSpatialReference=this.spatialReference;const l=await this.executeQuery(a,"execute");if(this._checkCancelled(r),e.pagesDefinition.internal.lastPage!==n)return"done";const o=this._layer.objectIdField;for(let t=0;t<l.features.length;t++)e.pagesDefinition.internal.set[i+t]=l.features[t].attributes[o];if(!1===this._pageJustIds)for(let e=0;e<l.features.length;e++)this._featureCache[l.features[e].attributes[o]]=l.features[e];return(void 0===l.exceededTransferLimit&&l.features.length!==e.pagesDefinition.resultRecordCount||!1===l.exceededTransferLimit)&&(e.pagesDefinition.internal.fullyResolved=!0),e.pagesDefinition.internal.lastRetrieved=s+l.features.length,e.pagesDefinition.internal.lastPage+=e.pagesDefinition.resultRecordCount,"done"}_fieldsIncludingObjectId(e){if(null===e)return[this.objectIdField];const t=e.slice(0);if(t.includes("*"))return t;let r=!1;for(const e of t)if(e.toUpperCase()===this.objectIdField.toUpperCase()){r=!0;break}return!1===r&&t.push(this.objectIdField),t}async _getFeatures(e,t,r,s){const i=[];if(-1!==t&&void 0===this._featureCache[t]&&i.push(t),!0===this._checkIfNeedToExpandKnownPage(e,this._maxProcessingRate()))return await this._expandPagedSet(e,this._maxProcessingRate(),0,0,s),this._getFeatures(e,t,r,s);let n=0;for(let s=e._lastFetchedIndex;s<e._known.length;s++){if(e._lastFetchedIndex+=1,n++,void 0===this._featureCache[e._known[s]]){let r=!1;if(null!==this._layer._mode&&void 0!==this._layer._mode){const t=this._layer._mode;if(void 0!==t._featureMap[e._known[s]]){const i=t._featureMap[e._known[s]];null!==i&&(r=!0,this._featureCache[e._known[s]]=i)}}if(!1===r&&(e._known[s]!==t&&i.push(e._known[s]),i.length>=this._maxProcessingRate()-1))break}if(n>=r&&0===i.length)break}if(0===i.length)return"success";const a=new j.Z;this._requestStandardised&&(a.sqlFormat="standard"),this.datesInUnknownTimezone&&(a.timeReferenceUnknownClient=!0),a.objectIds=i,a.outFields=null!==this._overrideFields?this._overrideFields:this._fieldsIncludingObjectId(this._layer.outFields?this._layer.outFields:["*"]),a.returnGeometry=!0,!0===this._removeGeometry&&(a.returnGeometry=!1),a.outSpatialReference=this.spatialReference;const l=await this.executeQuery(a,"execute");if(this._checkCancelled(s),void 0!==l.error)throw new _.EN(_.H9.RequestFailed,{reason:l.error});const o=this._layer.objectIdField;for(let e=0;e<l.features.length;e++)this._featureCache[l.features[e].attributes[o]]=l.features[e];return"success"}async _getDistinctPages(e,t,r,s,i,n,a,l,o){await this._ensureLoaded();const u=await this.databaseType();let d=r.parseTree.column;const c=this._layer.fields??[];for(let e=0;e<c.length;e++)if(c[e].name.toLowerCase()===d.toLowerCase()){d=c[e].name;break}const h=new j.Z;this._requestStandardised&&(h.sqlFormat="standard"),this.datesInUnknownTimezone&&(h.timeReferenceUnknownClient=!0);let p=null===n?null===i?"1=1":"":(0,f.zR)(n,u);this._layer.definitionExpression&&this._useDefinitionExpression&&(p=""!==p?"(("+this._layer.definitionExpression+") AND ("+p+"))":this._layer.definitionExpression),h.where=p,h.spatialRelationship=this._makeRelationshipEnum(s),h.relationParameter=this._makeRelationshipParam(s),h.geometry=null===i?null:i,h.returnDistinctValues=!0,h.returnGeometry=!1,h.outFields=[d];const y=await this.executeQuery(h,"execute");if(this._checkCancelled(o),!y.hasOwnProperty("features"))throw new _.EN(_.H9.InvalidStatResponse);let g=!1;for(let e=0;e<c.length;e++)if(c[e].name===d){"date"===c[e].type&&(g=!0);break}for(let e=0;e<y.features.length;e++){if(g){const t=y.features[e].attributes[d];null!==t?l.push(new Date(t)):l.push(t)}else l.push(y.features[e].attributes[d]);if(l.length>=a)break}return 0===y.features.length?l:y.features.length===this._layer.capabilities?.query.maxRecordCount&&l.length<a?{calculated:!0,result:await this._getDistinctPages(e+y.features.length,t,r,s,i,n,a,l,o)}:l}async _distinctStat(e,t,r,s,i,n,a){return{calculated:!0,result:await this._getDistinctPages(0,e,t,r,s,i,n,[],a)}}isTable(){return this._layer.isTable||null===this._layer.geometryType||"table"===this._layer.type||""===this._layer.geometryType||"esriGeometryNull"===this._layer.geometryType}async _countstat(e,t,r,s){const i=await this.databaseType(),n=new j.Z;if(this._requestStandardised&&(n.sqlFormat="standard"),this.isTable()&&r&&null!==t&&""!==t)return{calculated:!0,result:0};let a=null===s?null===r?"1=1":"":(0,f.zR)(s,i);return this._layer.definitionExpression&&this._useDefinitionExpression&&(a=""!==a?"(("+this._layer.definitionExpression+") AND ("+a+"))":this._layer.definitionExpression),n.where=a,this.datesInUnknownTimezone&&(n.timeReferenceUnknownClient=!0),n.where=a,n.spatialRelationship=this._makeRelationshipEnum(t),n.relationParameter=this._makeRelationshipParam(t),n.geometry=null===r?null:r,n.returnGeometry=!1,{calculated:!0,result:await this.executeQuery(n,"executeForCount")}}async _stats(e,t,r,s,i,n,a){await this._ensureLoaded();const l=this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsSqlExpression,o=this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsStatistics,u=this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsDistinct;if("count"===e)return u?this._countstat(e,r,s,i):{calculated:!1};if(!1===o||!1===(0,f.y5)(t)&&!1===l||!1===t.isStandardized)return""!==r||null!==i?{calculated:!1}:this._manualStat(e,t,n,a);if("distinct"===e)return!1===u?""!==r||null!==i?{calculated:!1}:this._manualStat(e,t,n,a):this._distinctStat(e,t,r,s,i,n,a);const d=await this.databaseType();if(this.isTable()&&s&&null!==r&&""!==r)return{calculated:!0,result:null};const c=new j.Z;this._requestStandardised&&(c.sqlFormat="standard");let h=null===i?null===s?"1=1":"":(0,f.zR)(i,d);this._layer.definitionExpression&&this._useDefinitionExpression&&(h=""!==h?"(("+this._layer.definitionExpression+") AND ("+h+"))":this._layer.definitionExpression),c.where=h,c.spatialRelationship=this._makeRelationshipEnum(r),c.relationParameter=this._makeRelationshipParam(r),c.geometry=null===s?null:s,this.datesInUnknownTimezone&&(c.timeReferenceUnknownClient=!0);const p=new U.Z;p.statisticType=(0,y.g3)(e),p.onStatisticField=(0,f.zR)(t,d),p.outStatisticFieldName="ARCADE_STAT_RESULT",c.returnGeometry=!1;let g="ARCADE_STAT_RESULT";c.outStatistics=[p];const m=await this.executeQuery(c,"execute");if(!m.hasOwnProperty("features")||0===m.features.length)throw new _.EN(_.H9.InvalidStatResponse);let w=!1;const S=m.fields??[];for(let e=0;e<S.length;e++)if("ARCADE_STAT_RESULT"===S[e].name.toUpperCase()){g=S[e].name,"date"===S[e].type&&(w=!0);break}if(w){let e=m.features[0].attributes[g];return null!==e&&(e=new Date(m.features[0].attributes[g])),{calculated:!0,result:e}}return{calculated:!0,result:m.features[0].attributes[g]}}_stat(e,t,r,s,i,n,a){return this._stats(e,t,r,s,i,n,a)}async _canDoAggregates(e,t){await this._ensureLoaded();let r=!1;const s=this._layer.capabilities?.query,i=!0===s?.supportsSqlExpression;if(null!=s&&!0===s.supportsStatistics&&!0===s.supportsOrderBy&&(r=!0),r)for(let e=0;e<t.length-1;e++)(!1===t[e].workingexpr?.isStandardized||!1===(0,f.y5)(t[e].workingexpr)&&!1===i)&&(r=!1);return!1!==r}_makeRelationshipEnum(e){if(e.includes("esriSpatialRelRelation"))return"relation";switch(e){case"esriSpatialRelRelation":return"relation";case"esriSpatialRelIntersects":return"intersects";case"esriSpatialRelContains":return"contains";case"esriSpatialRelOverlaps":return"overlaps";case"esriSpatialRelWithin":return"within";case"esriSpatialRelTouches":return"touches";case"esriSpatialRelCrosses":return"crosses";case"esriSpatialRelEnvelopeIntersects":return"envelope-intersects"}return e}_makeRelationshipParam(e){return e.includes("esriSpatialRelRelation")?e.split(":")[1]:""}async _getAggregatePagesDataSourceDefinition(e,t,r,s,i,n,a){await this._ensureLoaded();const l=await this.databaseType();let o="",u=!1,d=!1;null!==n&&this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsOrderBy&&(o=n.constructClause(),d=!0),this._layer.capabilities&&this._layer.capabilities.query&&!1===this._layer.capabilities.query.supportsPagination&&(d=!1,u=!0,o=this._layer.objectIdField);const h=[];for(let e=0;e<t.length;e++){const r=new U.Z;r.onStatisticField=null!==t[e].workingexpr?(0,f.zR)(t[e].workingexpr,l):"",r.outStatisticFieldName=t[e].field,r.statisticType=t[e].toStatisticsName(),h.push(r)}""===o&&(o=e.join(","));let p=this._maxQueryRate();const y=this._layer.capabilities?.query.maxRecordCount;null!=y&&y<p&&(p=y);let _=null===i?null===s?"1=1":"":(0,f.zR)(i,l);return this._layer.definitionExpression&&this._useDefinitionExpression&&(_=""!==_?"(("+this._layer.definitionExpression+") AND ("+_+"))":this._layer.definitionExpression),new c.Z([],["GETPAGES"],d,{groupbypage:!0,spatialRel:this._makeRelationshipEnum(r),relationParam:this._makeRelationshipParam(r),outFields:["*"],useOIDpagination:u,generatedOid:a,resultRecordCount:p,resultOffset:0,groupByFieldsForStatistics:e,outStatistics:h,geometry:null===s?null:s,where:_,orderByFields:o,returnGeometry:!1,returnIdsOnly:!1,internal:{lastMaxId:-1,set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}})}async _getAgregagtePhysicalPage(e,t,r){let s=e.pagesDefinition.where;!0===e.pagesDefinition.useOIDpagination&&(s=""!==s?"("+s+") AND ("+e.pagesDefinition.generatedOid+">"+e.pagesDefinition.internal.lastMaxId.toString()+")":e.pagesDefinition.generatedOid+">"+e.pagesDefinition.internal.lastMaxId.toString());const i=e.pagesDefinition.internal.lastRetrieved,n=i,l=e.pagesDefinition.internal.lastPage,o=new j.Z;if(this._requestStandardised&&(o.sqlFormat="standard"),o.where=s,o.spatialRelationship=e.pagesDefinition.spatialRel,o.relationParameter=e.pagesDefinition.relationParam,o.outFields=e.pagesDefinition.outFields,o.outStatistics=e.pagesDefinition.outStatistics,o.geometry=e.pagesDefinition.geometry,o.groupByFieldsForStatistics=e.pagesDefinition.groupByFieldsForStatistics,o.num=e.pagesDefinition.resultRecordCount,o.start=e.pagesDefinition.internal.lastPage,o.returnGeometry=e.pagesDefinition.returnGeometry,this.datesInUnknownTimezone&&(o.timeReferenceUnknownClient=!0),o.orderByFields=""!==e.pagesDefinition.orderByFields?e.pagesDefinition.orderByFields.split(","):null,this.isTable()&&o.geometry&&o.spatialRelationship)return[];const u=await this.executeQuery(o,"execute");if(this._checkCancelled(r),!u.hasOwnProperty("features"))throw new _.EN(_.H9.InvalidStatResponse);const d=[];if(e.pagesDefinition.internal.lastPage!==l)return[];for(let t=0;t<u.features.length;t++)e.pagesDefinition.internal.set[n+t]=u.features[t].attributes[e.pagesDefinition.generatedOid];for(let e=0;e<u.features.length;e++)d.push(new a.Z({attributes:u.features[e].attributes,geometry:null}));return!0===e.pagesDefinition.useOIDpagination?0===u.features.length?e.pagesDefinition.internal.fullyResolved=!0:e.pagesDefinition.internal.lastMaxId=u.features[u.features.length-1].attributes[e.pagesDefinition.generatedOid]:(void 0===u.exceededTransferLimit&&u.features.length!==e.pagesDefinition.resultRecordCount||!1===u.exceededTransferLimit)&&(e.pagesDefinition.internal.fullyResolved=!0),e.pagesDefinition.internal.lastRetrieved=i+u.features.length,e.pagesDefinition.internal.lastPage+=e.pagesDefinition.resultRecordCount,d}static create(e,t,r,s,i){const n=new k.default({url:e,outFields:null===t?["*"]:t});return new B({layer:n,spatialReference:r,lrucache:s,interceptor:i})}relationshipMetaData(){return this._layer&&this._layer.source&&this._layer.source.sourceJSON&&this._layer.source.sourceJSON.relationships?this._layer.source.sourceJSON.relationships:[]}serviceUrl(){return(0,p.US)(this._layer.parsedUrl.path)}async queryAttachments(e,t,r,s,i){const n=this._layer.capabilities;if(n?.data.supportsAttachment&&n?.operations.supportsQueryAttachments){const n={objectIds:[e],returnMetadata:i};(t&&t>0||r&&r>0)&&(n.size=[t&&t>0?t:0,r&&r>0?r:t+1]),s&&s.length>0&&(n.attachmentTypes=s),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:n,method:"attachments"});const a=await this._layer.queryAttachments(n),l=[];return a&&a[e]&&a[e].forEach((t=>{const r=this._layer.parsedUrl.path+"/"+e.toString()+"/attachments/"+t.id.toString();let s=null;i&&t.exifInfo&&(s=q.Z.convertJsonToArcade(t.exifInfo,"system",!0)),l.push(new T.Z(t.id,t.name,t.contentType,t.size,r,s))})),l}return[]}async queryRelatedFeatures(e){const t={f:"json",relationshipId:e.relationshipId.toString(),definitionExpression:e.where,outFields:e.outFields?.join(","),returnGeometry:e.returnGeometry.toString()};void 0!==e.resultOffset&&null!==e.resultOffset&&(t.resultOffset=e.resultOffset.toString()),void 0!==e.resultRecordCount&&null!==e.resultRecordCount&&(t.resultRecordCount=e.resultRecordCount.toString()),e.orderByFields&&(t.orderByFields=e.orderByFields.join(",")),e.objectIds&&e.objectIds.length>0&&(t.objectIds=e.objectIds.join(",")),e.outSpatialReference&&(t.outSR=JSON.stringify(e.outSpatialReference.toJSON())),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preRequestCallback({layer:this._layer,queryPayload:t,method:"relatedrecords",url:this._layer.parsedUrl.path+"/queryRelatedRecords"});const r=await(0,s.default)(this._layer.parsedUrl.path+"/queryRelatedRecords",{responseType:"json",query:t});if(r.data){const e={},t=r.data;if(t&&t.relatedRecordGroups){const r=t.spatialReference;for(const s of t.relatedRecordGroups){const i=s.objectId,n=[];for(const e of s.relatedRecords){e.geometry&&(e.geometry.spatialReference=r);const t=new a.Z({geometry:e.geometry?(0,D.im)(e.geometry):null,attributes:e.attributes});n.push(t)}e[i]={features:n,exceededTransferLimit:!0===t.exceededTransferLimit}}}return e}throw new _.EN(_.H9.InvalidRequest)}async getFeatureByObjectId(e,t){const r=new j.Z;r.outFields=t,r.returnGeometry=!1,r.outSpatialReference=this.spatialReference,r.where=this.objectIdField+"="+e.toString(),this.datesInUnknownTimezone&&(r.timeReferenceUnknownClient=!0),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:r,method:"execute"});const s=await(0,E.e)(this._layer.parsedUrl.path,r);return 1===s.features.length?s.features[0]:null}async getIdentityUser(){await this.load();const e=x.id?.findCredential(this._layer.url);return e?e.userId:null}async getOwningSystemUrl(){await this.load();const e=x.id?.findServerInfo(this._layer.url);if(e)return e.owningSystemUrl;let t=this._layer.url;const r=t.toLowerCase().indexOf("/rest/services");if(t=r>-1?t.substring(0,r):t,t){t+="/rest/info";try{const e=await(0,s.default)(t,{query:{f:"json"}});let r="";return e.data&&e.data.owningSystemUrl&&(r=e.data.owningSystemUrl),r}catch(e){return""}}return""}getDataSourceFeatureSet(){const e=new B({layer:this._layer,spatialReference:this.spatialReference??void 0,outFields:this._overrideFields??void 0,includeGeometry:!this._removeGeometry,lrucache:this.recentlyUsedQueries??void 0,interceptor:this.featureSetQueryInterceptor??void 0});return e._useDefinitionExpression=!1,e}get preferredTimeReference(){return void 0===this._cachedDateMetaData.preferredTimeReference&&(this._cachedDateMetaData.preferredTimeReference=this._layer?.preferredTimeReference?.toJSON()??null),this._cachedDateMetaData.preferredTimeReference}get dateFieldsTimeReference(){return void 0===this._cachedDateMetaData.dateFieldsTimeReference&&(this._cachedDateMetaData.dateFieldsTimeReference=this._layer?.dateFieldsTimeReference?.toJSON()??null),this._cachedDateMetaData.dateFieldsTimeReference}get datesInUnknownTimezone(){return this._layer.datesInUnknownTimezone}get editFieldsInfo(){return void 0===this._cachedDateMetaData.editFieldsInfo&&(this._cachedDateMetaData.editFieldsInfo=this._layer?.editFieldsInfo?.toJSON()??null),this._cachedDateMetaData.editFieldsInfo}get timeInfo(){return void 0===this._cachedDateMetaData.timeInfo&&(this._cachedDateMetaData.timeInfo=this._layer?.timeInfo?.toJSON()??null),this._cachedDateMetaData.timeInfo}}var M=r(17805);class W extends d.Z{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerRelated",this._findObjectId=-1,this._requestStandardised=!1,this._removeGeometry=!1,this._overrideFields=null,this.featureObjectId=null,e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,this._findObjectId=e.objectId,this.featureObjectId=e.objectId,this.relationship=e.relationship,this._relatedLayer=e.relatedLayer,void 0!==e.outFields&&(this._overrideFields=e.outFields),void 0!==e.includeGeometry&&(this._removeGeometry=!1===e.includeGeometry)}_maxQueryRate(){return p.tI}end(){return this._layer}optimisePagingFeatureQueries(){}async loadImpl(){return await Promise.all([this._layer.load(),this._relatedLayer?.load()]),this._initialiseFeatureSet(),this}nativeCapabilities(){return this._relatedLayer.nativeCapabilities()}_initialiseFeatureSet(){if(null==this.spatialReference&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._relatedLayer.geometryType,this.fields=this._relatedLayer.fields.slice(0),null!==this._overrideFields)if(1===this._overrideFields.length&&"*"===this._overrideFields[0])this._overrideFields=null;else{const e=[],t=[];for(const r of this.fields)if("oid"===r.type)e.push(r),t.push(r.name);else for(const s of this._overrideFields)if(s.toLowerCase()===r.name.toLowerCase()){e.push(r),t.push(r.name);break}this.fields=e,this._overrideFields=t}const e=this._layer.nativeCapabilities();e&&(this._databaseType=e.databaseType,this._requestStandardised=e.requestStandardised),this.objectIdField=this._relatedLayer.objectIdField,this.globalIdField=this._relatedLayer.globalIdField,this.hasM=this._relatedLayer.supportsM,this.hasZ=this._relatedLayer.supportsZ,this.typeIdField=this._relatedLayer.typeIdField,this.types=this._relatedLayer.types}async databaseType(){return await this._relatedLayer.databaseType(),this._databaseType=this._relatedLayer._databaseType,this._databaseType}isTable(){return this._relatedLayer.isTable()}_isInFeatureSet(){return p.dj.InFeatureSet}_candidateIdTransform(e){return e}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,t}return this._wset}_changeFeature(e){const t={};for(const r of this.fields)t[r.name]=e.attributes[r.name];return new a.Z({geometry:!0===this._removeGeometry?null:e.geometry,attributes:t})}async _getFilteredSet(e,t,r,s,i){if(await this.databaseType(),this.isTable()&&t&&null!==e&&""!==e)return new c.Z([],[],!0,null);const n=this._layer.nativeCapabilities();if(!1===n.canQueryRelated)return new c.Z([],[],!0,null);if(n.capabilities?.queryRelated&&n.capabilities.queryRelated.supportsPagination)return this._getFilteredSetUsingPaging(e,t,r,s,i);let a="",l=!1;null!==s&&n.capabilities&&n.capabilities.queryRelated&&!0===n.capabilities.queryRelated.supportsOrderBy&&(a=s.constructClause(),l=!0);const o=new Z.default;o.objectIds=[this._findObjectId];const u=null!==this._overrideFields?this._overrideFields:this._fieldsIncludingObjectId(this._relatedLayer.fields?this._relatedLayer.fields.map((e=>e.name)):["*"]);o.outFields=u,o.relationshipId=this.relationship.id,o.where="1=1";let d=!0;!0===this._removeGeometry&&(d=!1),o.returnGeometry=d,this._requestStandardised&&(o.sqlFormat="standard"),o.outSpatialReference=this.spatialReference,o.orderByFields=""!==a?a.split(","):null;const h=await n.source.queryRelatedFeatures(o);this._checkCancelled(i);const p=h[this._findObjectId]?h[this._findObjectId].features:[],f=[];for(let e=0;e<p.length;e++)this._featureCache[p[e].attributes[this._relatedLayer.objectIdField]]=p[e],f.push(p[e].attributes[this._relatedLayer.objectIdField]);const y=t&&null!==e&&""!==e,_=null!=r;return new c.Z(y||_?f:[],y||_?[]:f,l,null)}_fieldsIncludingObjectId(e){if(null===e)return[this.objectIdField];const t=e.slice(0);if(t.includes("*"))return t;let r=!1;for(const e of t)if(e.toUpperCase()===this.objectIdField.toUpperCase()){r=!0;break}return!1===r&&t.push(this.objectIdField),t}async _getFilteredSetUsingPaging(e,t,r,s,i){let n="",a=!1;const l=this._layer.nativeCapabilities();null!==s&&l&&l.capabilities?.queryRelated&&!0===l.capabilities.queryRelated.supportsOrderBy&&(n=s.constructClause(),a=!0),await this.databaseType();let o=this._maxQueryRate();const u=l.capabilities?.query.maxRecordCount;null!=u&&u<o&&(o=u);const d=t&&null!==e&&""!==e,h=null!=r;let p=null,f=!0;!0===this._removeGeometry&&(f=!1);const y=null!==this._overrideFields?this._overrideFields:this._fieldsIncludingObjectId(this._relatedLayer.fields?this._relatedLayer.fields.map((e=>e.name)):["*"]);return p=new c.Z(d||h?["GETPAGES"]:[],d||h?[]:["GETPAGES"],a,{outFields:y.join(","),resultRecordCount:o,resultOffset:0,objectIds:[this._findObjectId],where:"1=1",orderByFields:n,returnGeometry:f,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}}),await this._expandPagedSet(p,o,0,0,i),p}_expandPagedSet(e,t,r,s,i){return this._expandPagedSetFeatureSet(e,t,r,s,i)}_clonePageDefinition(e){return null===e?null:!0!==e.groupbypage?{groupbypage:!1,outFields:e.outFields,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,where:e.where,objectIds:e.objectIds,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}:{groupbypage:!0,outFields:e.outFields,resultRecordCount:e.resultRecordCount,useOIDpagination:e.useOIDpagination,generatedOid:e.generatedOid,groupByFieldsForStatistics:e.groupByFieldsForStatistics,resultOffset:e.resultOffset,outStatistics:e.outStatistics,geometry:e.geometry,where:e.where,objectIds:e.objectIds,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}}async _getPhysicalPage(e,t,r){const s=e.pagesDefinition.internal.lastRetrieved,i=s,n=e.pagesDefinition.internal.lastPage,a=this._layer.nativeCapabilities(),l=new Z.default;!0===this._requestStandardised&&(l.sqlFormat="standard"),l.relationshipId=this.relationship.id,l.objectIds=e.pagesDefinition.objectIds,l.resultOffset=e.pagesDefinition.internal.lastPage,l.resultRecordCount=e.pagesDefinition.resultRecordCount,l.outFields=e.pagesDefinition.outFields.split(","),l.where=e.pagesDefinition.where,l.orderByFields=""!==e.pagesDefinition.orderByFields?e.pagesDefinition.orderByFields.split(","):null,l.returnGeometry=e.pagesDefinition.returnGeometry,l.outSpatialReference=this.spatialReference;const o=await a.source.queryRelatedFeatures(l);if(this._checkCancelled(r),e.pagesDefinition.internal.lastPage!==n)return 0;const u=o[this._findObjectId]?o[this._findObjectId].features:[];for(let t=0;t<u.length;t++)e.pagesDefinition.internal.set[i+t]=u[t].attributes[this._relatedLayer.objectIdField];for(let e=0;e<u.length;e++)this._featureCache[u[e].attributes[this._relatedLayer.objectIdField]]=u[e];const d=!o[this._findObjectId]||!1===o[this._findObjectId].exceededTransferLimit;return u.length!==e.pagesDefinition.resultRecordCount&&d&&(e.pagesDefinition.internal.fullyResolved=!0),e.pagesDefinition.internal.lastRetrieved=s+u.length,e.pagesDefinition.internal.lastPage+=e.pagesDefinition.resultRecordCount,u.length}async _getFeatures(e,t,r,s){const i=[];-1!==t&&void 0===this._featureCache[t]&&i.push(t);const n=this._maxQueryRate();if(!0===this._checkIfNeedToExpandKnownPage(e,n))return await this._expandPagedSet(e,n,0,0,s),this._getFeatures(e,t,r,s);let a=0;for(let s=e._lastFetchedIndex;s<e._known.length&&(a++,a<=r&&(e._lastFetchedIndex+=1),!("GETPAGES"!==e._known[s]&&void 0===this._featureCache[e._known[s]]&&(e._known[s]!==t&&i.push(e._known[s]),i.length>r)))&&!(a>=r&&0===i.length);s++);if(0===i.length)return"success";throw new _.EN(_.H9.MissingFeatures)}async _refineSetBlock(e,t,r){return e}async _stat(e,t,r,s,i,n,a){return{calculated:!1}}get gdbVersion(){return this._relatedLayer.gdbVersion}async _canDoAggregates(e,t,r,s,i){return!1}relationshipMetaData(){return this._relatedLayer.relationshipMetaData()}serviceUrl(){return this._relatedLayer.serviceUrl()}queryAttachments(e,t,r,s,i){return this._relatedLayer.queryAttachments(e,t,r,s,i)}getFeatureByObjectId(e,t){return this._relatedLayer.getFeatureByObjectId(e,t)}getOwningSystemUrl(){return this._relatedLayer.getOwningSystemUrl()}getIdentityUser(){return this._relatedLayer.getIdentityUser()}getDataSourceFeatureSet(){return this._relatedLayer}get preferredTimeReference(){return this._relatedLayer?.preferredTimeReference??null}get dateFieldsTimeReference(){return this._relatedLayer?.dateFieldsTimeReference??null}get datesInUnknownTimezone(){return this._relatedLayer?.datesInUnknownTimezone}get editFieldsInfo(){return this._relatedLayer?.editFieldsInfo??null}get timeInfo(){return this._relatedLayer?.timeInfo??null}}var Q=r(48039),z=r(15235);function V(){null===Q.Z.applicationCache&&(Q.Z.applicationCache=new Q.Z)}async function H(e,t){if(Q.Z.applicationCache){const r=Q.Z.applicationCache.getLayerInfo(e);if(r){const s=await r;return new k.default({url:e,outFields:t,sourceJSON:s})}const s=new k.default({url:e,outFields:t}),i=(async()=>(await s.load(),s.sourceJSON))();if(Q.Z.applicationCache){Q.Z.applicationCache.setLayerInfo(e,i);try{return await i,s}catch(t){throw Q.Z.applicationCache.clearLayerInfo(e),t}}return await i,s}return new k.default({url:e,outFields:t})}async function J(e,t,r,s,i,n=null){return K(await H(e,["*"]),t,r,s,i,n)}function K(e,t=null,r=null,s=!0,i=null,n=null){const a={layer:e,spatialReference:t,outFields:r,includeGeometry:s,lrucache:i,interceptor:n};return!0===e._hasMemorySource()?new M.Z(a):new B(a)}async function $(e){if(null!==Q.Z.applicationCache){const t=Q.Z.applicationCache.getLayerInfo(e);if(null!==t)return t}const t=(async()=>{const t=await(0,s.default)(e,{responseType:"json",query:{f:"json"}});if(t.data){const e=t.data;return e.layers||(e.layers=[]),e.tables||(e.tables=[]),e}return{layers:[],tables:[]}})();if(null!==Q.Z.applicationCache){Q.Z.applicationCache.setLayerInfo(e,t);try{return await t}catch(t){throw Q.Z.applicationCache.clearLayerInfo(e),t}}return t}async function X(e,t){const r={metadata:null,networkId:-1,unVersion:3,terminals:[],queryelem:null,layerNameLkp:{},lkp:null},i=await $(e);if(r.metadata=i,i.controllerDatasetLayers&&void 0!==i.controllerDatasetLayers.utilityNetworkLayerId&&null!==i.controllerDatasetLayers.utilityNetworkLayerId){if(i.layers)for(const e of i.layers)r.layerNameLkp[e.id]=e.name;if(i.tables)for(const e of i.tables)r.layerNameLkp[e.id]=e.name;const n=i.controllerDatasetLayers.utilityNetworkLayerId;r.networkId=n;const a=await async function(e,t){const r="QUERYDATAELEMTS:"+t.toString()+":"+e;if(null!==Q.Z.applicationCache){const e=Q.Z.applicationCache.getLayerInfo(r);if(null!==e)return e}const i=(async()=>{const r=await(0,s.default)(e+"/queryDataElements",{method:"post",responseType:"json",query:{layers:JSON.stringify([t.toString()]),f:"json"}});if(r.data){const e=r.data;if(e.layerDataElements&&e.layerDataElements[0])return e.layerDataElements[0]}throw new _.EN(_.H9.DataElementsNotFound)})();if(null!==Q.Z.applicationCache){Q.Z.applicationCache.setLayerInfo(r,i);try{return await i}catch(e){throw Q.Z.applicationCache.clearLayerInfo(r),e}}return i}(e,n);if(a){r.queryelem=a,r.queryelem&&r.queryelem.dataElement&&void 0!==r.queryelem.dataElement.schemaGeneration&&(r.unVersion=r.queryelem.dataElement.schemaGeneration),r.lkp={},r.queryelem.dataElement.domainNetworks||(r.queryelem.dataElement.domainNetworks=[]);for(const e of r.queryelem.dataElement.domainNetworks){for(const t of e.edgeSources?e.edgeSources:[]){const e={layerId:t.layerId,sourceId:t.sourceId,className:r.layerNameLkp[t.layerId]?r.layerNameLkp[t.layerId]:null};e.className&&(r.lkp[e.className]=e)}for(const t of e.junctionSources?e.junctionSources:[]){const e={layerId:t.layerId,sourceId:t.sourceId,className:r.layerNameLkp[t.layerId]?r.layerNameLkp[t.layerId]:null};e.className&&(r.lkp[e.className]=e)}}if(r.queryelem.dataElement.terminalConfigurations)for(const e of r.queryelem.dataElement.terminalConfigurations)for(const t of e.terminals)r.terminals.push({terminalId:t.terminalId,terminalName:t.terminalName});const i=await async function(e){if(null!==Q.Z.applicationCache){const t=Q.Z.applicationCache.getLayerInfo(e);if(null!==t)return t}const t=(async()=>{const t=await(0,s.default)(e,{responseType:"json",query:{f:"json"}});return t.data?t.data:null})();if(null!==Q.Z.applicationCache){Q.Z.applicationCache.setLayerInfo(e,t);try{return await t}catch(t){throw Q.Z.applicationCache.clearLayerInfo(e),t}}return t}(e+"/"+n);if(i.systemLayers&&void 0!==i.systemLayers.associationsTableId&&null!==i.systemLayers.associationsTableId){const s=[];r.unVersion>=4&&(s.push("STATUS"),s.push("PERCENTALONG"));let n=await J(e+"/"+i.systemLayers.associationsTableId.toString(),t,["OBJECTID","FROMNETWORKSOURCEID","TONETWORKSOURCEID","FROMGLOBALID","TOGLOBALID","TOTERMINALID","FROMTERMINALID","ASSOCIATIONTYPE","ISCONTENTVISIBLE","GLOBALID",...s],!1,null,null);return await n.load(),r.unVersion>=4&&(n=n.filter(g.WhereClause.create("STATUS NOT IN (1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 57, 58, 59, 60, 61, 62,63)",n.getFieldsIndex())),await n.load()),{lkp:r.lkp,associations:n,unVersion:r.unVersion,terminals:r.terminals}}return{associations:null,unVersion:r.unVersion,lkp:null,terminals:[]}}return{associations:null,unVersion:r.unVersion,lkp:null,terminals:[]}}return{associations:null,unVersion:r.unVersion,lkp:null,terminals:[]}}async function Y(e,t,r,s=null,i=null,n=!0,a=null,l=null){let o=e.serviceUrl();if(!o)return null;o="/"===o.charAt(o.length-1)?o+t.relatedTableId.toString():o+"/"+t.relatedTableId.toString();const u=await J(o,s,i,n,a,l);return new W({layer:e,relatedLayer:u,relationship:t,objectId:r,spatialReference:s,outFields:i,includeGeometry:n,lrucache:a,interceptor:l})}n.Z.registerAction(),v.registerAction(),u.Z.registerAction(),C.Z.registerAction(),R.Z.registerAction();class ee extends i.Z{constructor(e,t=null,r=null,s=null){super(),this._map=e,this._overridespref=t,this._lrucache=r,this._interceptor=s,this._instantLayers=[]}_makeAndAddFeatureSet(e,t=!0,r=null){const s=K(e,this._overridespref,null===r?["*"]:r,t,this._lrucache,this._interceptor);return this._instantLayers.push({featureset:s,opitem:e,includeGeometry:t,outFields:JSON.stringify(r)}),s}async featureSetByName(e,t=!0,r=null){if(void 0!==this._map.loaded&&void 0!==this._map.load&&!1===this._map.loaded)return await this._map.load(),this.featureSetByName(e,t,r);null===r&&(r=["*"]),r=(r=r.slice(0)).sort();const s=JSON.stringify(r);for(let r=0;r<this._instantLayers.length;r++){const i=this._instantLayers[r];if(i.opitem.title===e&&i.includeGeometry===t&&i.outFields===s)return this._instantLayers[r].featureset}const i=this._map.allLayers.find((t=>t instanceof k.default&&t.title===e));if(i)return this._makeAndAddFeatureSet(i,t,r);if(this._map.tables){const s=this._map.tables.find((t=>!!(t.title&&t.title===e||t.title&&t.title===e)));if(s){if(s instanceof k.default)return this._makeAndAddFeatureSet(s,t,r);if(s._materializedTable);else{const e=s.outFields?s:{...s,outFields:["*"]};s._materializedTable=new k.default(e)}return await s._materializedTable.load(),this._makeAndAddFeatureSet(s._materializedTable,t,r)}}return null}async featureSetById(e,t=!0,r=["*"]){if(void 0!==this._map.loaded&&void 0!==this._map.load&&!1===this._map.loaded)return await this._map.load(),this.featureSetById(e,t,r);null===r&&(r=["*"]),r=(r=r.slice(0)).sort();const s=JSON.stringify(r);for(let r=0;r<this._instantLayers.length;r++){const i=this._instantLayers[r];if(i.opitem.id===e&&i.includeGeometry===t&&i.outFields===s)return this._instantLayers[r].featureset}const i=this._map.allLayers.find((t=>t instanceof k.default&&t.id===e));if(i)return this._makeAndAddFeatureSet(i,t,r);if(this._map.tables){const s=this._map.tables.find((t=>t.id===e));if(s){if(s instanceof k.default)return this._makeAndAddFeatureSet(s,t,r);if(s._materializedTable);else{const e={...s,outFields:["*"]};s._materializedTable=new k.default(e)}return await s._materializedTable.load(),this._makeAndAddFeatureSet(s._materializedTable,t,r)}}return null}}class te extends i.Z{constructor(e,t=null,r=null,s=null){super(),this._url=e,this._overridespref=t,this._lrucache=r,this._interceptor=s,this.metadata=null,this._instantLayers=[]}get url(){return this._url}_makeAndAddFeatureSet(e,t=!0,r=null){const s=K(e,this._overridespref,null===r?["*"]:r,t,this._lrucache);return this._instantLayers.push({featureset:s,opitem:e,includeGeometry:t,outFields:JSON.stringify(r)}),s}async _loadMetaData(){const e=await $(this._url);return this.metadata=e,e}load(){return this._loadMetaData()}clone(){return new te(this._url,this._overridespref,this._lrucache,this._interceptor)}async featureSetByName(e,t=!0,r=null){null===r&&(r=["*"]),r=(r=r.slice(0)).sort();const s=JSON.stringify(r);for(let r=0;r<this._instantLayers.length;r++){const i=this._instantLayers[r];if(i.opitem.title===e&&i.includeGeometry===t&&i.outFields===s)return this._instantLayers[r].featureset}const i=await this._loadMetaData();let n=null;for(const t of i.layers?i.layers:[])t.name===e&&(n=t);if(!n)for(const t of i.tables?i.tables:[])t.name===e&&(n=t);if(n){const e=await H(this._url+"/"+n.id,["*"]);return this._makeAndAddFeatureSet(e,t,r)}return null}async featureSetById(e,t=!0,r=["*"]){null===r&&(r=["*"]),r=(r=r.slice(0)).sort();const s=JSON.stringify(r);e=null!=e?e.toString():"";for(let r=0;r<this._instantLayers.length;r++){const i=this._instantLayers[r];if(i.opitem.id===e&&i.includeGeometry===t&&i.outFields===s)return this._instantLayers[r].featureset}const i=await this._loadMetaData();let n=null;for(const t of i.layers?i.layers:[])null!==t.id&&void 0!==t.id&&t.id.toString()===e&&(n=t);if(!n)for(const t of i.tables?i.tables:[])null!==t.id&&void 0!==t.id&&t.id.toString()===e&&(n=t);if(n){const e=await H(this._url+"/"+n.id,["*"]);return this._makeAndAddFeatureSet(e,t,r)}return null}}function re(e,t,r=null,s=null){return new ee(e,t,r,s)}function se(e,t,r=null,s=null){return new te(e,t,r,s)}function ie(e,t,r,s,i){if(null===e)return null;if(e instanceof k.default){switch(t){case"datasource":return K(e,i,e.outFields,!0,r,s).getDataSourceFeatureSet();case"parent":case"root":return K(e,i,e.outFields,!0,r,s)}return null}if(e instanceof d.Z)switch(t){case"datasource":return e.getDataSourceFeatureSet();case"parent":return e;case"root":return e.getRootFeatureSet()}return null}async function ne(e,t,r,s,i,n,a,l=null){if(Q.Z.applicationCache){const o=Q.Z.applicationCache.getLayerInfo(e+":"+n.url);if(o){const e=await o;return K(new k.default({url:(0,p.US)(e.url)+"/"+t,outFields:["*"]}),r,s,i,a,l)}}const o=new z.default({id:e,portal:n}).load();Q.Z.applicationCache&&Q.Z.applicationCache.setLayerInfo(e+":"+n.url,o);try{const e=await o;return K(new k.default({url:(0,p.US)(e.url??"")+"/"+t,outFields:["*"]}),r,s,i,a,l)}catch(t){throw Q.Z.applicationCache&&Q.Z.applicationCache.clearLayerInfo(e+":"+n.url),t}}},70409:(e,t,r)=>{r.d(t,{$X:()=>y,QP:()=>_,TO:()=>g,Xx:()=>w,yN:()=>m});var s=r(38171),i=r(48853),n=r(77286),a=r(59987),l=r(91136),o=r(90961),u=r(90658),d=r(36515),c=r(70586),h=r(41534),p=r(82971);class f{constructor(e){this.field=e,this.sqlRewritable=!1}postInitialization(e,t){}}class y extends f{constructor(e){super(e),this.sqlRewritable=!0}extractValue(e){return e.attributes[this.field.name]}rewriteSql(e){return{rewritten:this.sqlRewritable,where:e}}}class _ extends f{constructor(e,t,r){super((0,u.JW)(e)),this.originalField=e,this.sqlRewritable=!0,this.field.name=t,this.field.alias=r}rewriteSql(e,t){return{rewritten:this.sqlRewritable,where:(0,d.bB)(e,this.field.name,this.originalField.name,t.getFieldsIndex())}}extractValue(e){return e.attributes[this.originalField.name]}}class g extends f{constructor(e,t,r){super(e),this.codefield=t,this.lkp=r,this.reverseLkp={};for(const e in r)this.reverseLkp[r[e]]=e;this.sqlRewritable=!0}rewriteSql(e,t){const r=this.evaluateNodeToWhereClause(e.parseTree,u.Bj.Standardised,this.field.name,this.codefield instanceof h.WhereClause?(0,d.zR)(this.codefield,u.Bj.Standardised):this.codefield,e.parameters);return r.includes(g.BADNESS)?{rewritten:!1,where:e}:{rewritten:this.sqlRewritable,where:h.WhereClause.create(r,(0,c.s3)(t._parent).getFieldsIndex())}}evaluateNodeToWhereClause(e,t,r=null,s=null,n){let l,o,u,c;switch(e.type){case"interval":return(0,d.TE)(this.evaluateNodeToWhereClause(e.value,t,r,s,n),e.qualifier,e.op);case"case-expression":{let s=" CASE ";"simple"===e.format&&(s+=this.evaluateNodeToWhereClause(e.operand,t,r,g.BADNESS,n));for(let i=0;i<e.clauses.length;i++)s+=" WHEN "+this.evaluateNodeToWhereClause(e.clauses[i].operand,t,r,g.BADNESS,n)+" THEN "+this.evaluateNodeToWhereClause(e.clauses[i].value,t,r,g.BADNESS,n);return null!==e.else&&(s+=" ELSE "+this.evaluateNodeToWhereClause(e.else,t,r,g.BADNESS,n)),s+=" END ",s}case"parameter":{const r=n[e.value.toLowerCase()];if("string"==typeof r)return"'"+r.toString().replace(/'/g,"''")+"'";if(r instanceof Date)return(0,d.oX)(r,t,null);if(r instanceof i.iG)return(0,d.mA)(r,t,null);if(r instanceof Array){const e=[];for(let s=0;s<r.length;s++)"string"==typeof r[s]?e.push("'"+r[s].toString().replace(/'/g,"''")+"'"):r[s]instanceof Date?e.push((0,d.oX)(r[s],t,null)):r[s]instanceof i.iG?e.push((0,d.mA)(r[s],t,null)):e.push(r[s].toString());return e}return r.toString()}case"expression-list":o=[];for(const i of e.value)o.push(this.evaluateNodeToWhereClause(i,t,r,s,n));return o;case"unary-expression":return" ( NOT "+this.evaluateNodeToWhereClause(e.expr,t,r,g.BADNESS,n)+" ) ";case"binary-expression":switch(e.operator){case"AND":return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" AND "+this.evaluateNodeToWhereClause(e.right,t,r,s,n)+") ";case"OR":return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" OR "+this.evaluateNodeToWhereClause(e.right,t,r,s,n)+") ";case"IS":if("null"!==e.right.type)throw new a.eS(a.f.UnsupportedIsRhs);return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" IS NULL )";case"ISNOT":if("null"!==e.right.type)throw new a.eS(a.f.UnsupportedIsRhs);return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" IS NOT NULL )";case"IN":if(l=[],"expression-list"===e.right.type){if("column-reference"===e.left.type&&e.left.column.toUpperCase()===this.field.name.toUpperCase()){const i=[];let a=!0;for(const t of e.right.value){if("string"!==t.type){a=!1;break}if(void 0===this.lkp[t.value]){a=!1;break}i.push(this.lkp[t.value].toString())}if(a)return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" IN ("+i.join(",")+")) "}return l=this.evaluateNodeToWhereClause(e.right,t,r,s,n)," ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" IN ("+l.join(",")+")) "}return c=this.evaluateNodeToWhereClause(e.right,t,r,s,n),c instanceof Array?" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" IN ("+c.join(",")+")) ":" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" IN ("+c+")) ";case"NOT IN":if(l=[],"expression-list"===e.right.type){if("column-reference"===e.left.type&&e.left.column.toUpperCase()===this.field.name.toUpperCase()){const i=[];let a=!0;for(const t of e.right.value){if("string"!==t.type){a=!1;break}if(void 0===this.lkp[t.value]){a=!1;break}i.push(this.lkp[t.value].toString())}if(a)return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" NOT IN ("+i.join(",")+")) "}return l=this.evaluateNodeToWhereClause(e.right,t,r,s,n)," ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" NOT IN ("+l.join(",")+")) "}return c=this.evaluateNodeToWhereClause(e.right,t,r,s,n),c instanceof Array?" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" NOT IN ("+c.join(",")+")) ":" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,n)+" NOT IN ("+c+")) ";case"BETWEEN":return u=this.evaluateNodeToWhereClause(e.right,t,r,g.BADNESS,n)," ("+this.evaluateNodeToWhereClause(e.left,t,r,g.BADNESS,n)+" BETWEEN "+u[0]+" AND "+u[1]+" ) ";case"NOTBETWEEN":return u=this.evaluateNodeToWhereClause(e.right,t,r,g.BADNESS,n)," ("+this.evaluateNodeToWhereClause(e.left,t,r,g.BADNESS,n)+" NOT BETWEEN "+u[0]+" AND "+u[1]+" ) ";case"LIKE":return""!==e.escape?" ("+this.evaluateNodeToWhereClause(e.left,t,r,g.BADNESS,n)+" LIKE "+this.evaluateNodeToWhereClause(e.right,t,r,g.BADNESS,n)+" ESCAPE '"+e.escape+"') ":" ("+this.evaluateNodeToWhereClause(e.left,t,r,g.BADNESS,n)+" LIKE "+this.evaluateNodeToWhereClause(e.right,t,r,g.BADNESS,n)+") ";case"NOT LIKE":return""!==e.escape?" ("+this.evaluateNodeToWhereClause(e.left,t,r,g.BADNESS,n)+" NOT LIKE "+this.evaluateNodeToWhereClause(e.right,t,r,g.BADNESS,n)+" ESCAPE '"+e.escape+"') ":" ("+this.evaluateNodeToWhereClause(e.left,t,r,g.BADNESS,n)+" NOT LIKE "+this.evaluateNodeToWhereClause(e.right,t,r,g.BADNESS,n)+") ";case"<>":case"=":if("column-reference"===e.left.type&&"string"===e.right.type){if(e.left.column.toUpperCase()===this.field.name.toUpperCase()&&void 0!==this.lkp[e.right.value.toString()])return" ("+s+" "+e.operator+" "+this.lkp[e.right.value.toString()].toString()+") "}else if("column-reference"===e.right.type&&"string"===e.left.type&&e.right.column.toUpperCase()===this.field.name.toUpperCase())return" ("+this.lkp[e.right.value.toString()].toString()+" "+e.operator+" "+s+") ";return" ("+this.evaluateNodeToWhereClause(e.left,t,r,g.BADNESS,n)+" "+e.operator+" "+this.evaluateNodeToWhereClause(e.right,t,r,g.BADNESS,n)+") ";case"<":case">":case">=":case"<=":case"*":case"-":case"+":case"/":case"||":return" ("+this.evaluateNodeToWhereClause(e.left,t,r,g.BADNESS,n)+" "+e.operator+" "+this.evaluateNodeToWhereClause(e.right,t,r,g.BADNESS,n)+") "}case"null":return"null";case"boolean":return!0===e.value?"1":"0";case"string":return"'"+e.value.toString().replace(/'/g,"''")+"'";case"timestamp":case"date":return(0,d.oX)(e.value,t,null);case"number":return e.value.toString();case"current-time":return(0,d.vR)("date"===e.mode,t);case"column-reference":return r&&r.toLowerCase()===e.column.toLowerCase()?"("+s+")":e.column;case"data-type":return e.value;case"function":{const s=this.evaluateNodeToWhereClause(e.args,t,r,g.BADNESS,n);return(0,d.fz)(e.name,s,t)}}throw new a.eS(a.f.UnsupportedSyntax,{node:e.type})}extractValue(e){return this.codefield instanceof h.WhereClause?this.reverseLkp[this.codefield.calculateValueCompiled(e)]:this.reverseLkp[e.attributes[this.codefield]]}}g.BADNESS="_!!!_BAD_LKP_!!!!";class m extends f{constructor(e,t){super(e),this._sql=t}rewriteSql(e,t){return{rewritten:!0,where:(0,d.bB)(e,this.field.name,(0,d.zR)(this._sql,u.Bj.Standardised),t.getFieldsIndex())}}extractValue(e){return this._sql.calculateValueCompiled(e)}}class w extends l.Z{static findField(e,t){for(const r of e)if(r.name.toLowerCase()===t.toString().toLowerCase())return r;return null}constructor(e){super(e),this._calcFunc=null,this.declaredClass="esri.arcade.featureset.actions.Adapted",this.adaptedFields=[],this._extraFilter=null,this._extraFilter=e.extraFilter,this._parent=e.parentfeatureset,this._maxProcessing=30,this.adaptedFields=e.adaptedFields}_initialiseFeatureSet(){null!==this._parent?(this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types):(this.spatialReference=new p.Z({wkid:4326}),this.objectIdField="",this.globalIdField="",this.geometryType=u.Qk.point,this.typeIdField="",this.types=null),this.fields=[];for(const e of this.adaptedFields)e.postInitialization(this,this._parent),this.fields.push(e.field)}async _getSet(e){if(null===this._wset){await this._ensureLoaded();let t=null;return t=this._extraFilter?await this._getFilteredSet("",null,null,null,e):await(this._parent?._getSet(e)),this._checkCancelled(e),(0,c.O3)(t),this._wset=new o.Z(t._candidates.slice(0),t._known.slice(0),t._ordered,this._clonePageDefinition(t.pagesDefinition)),this._wset}return this._wset}_isInFeatureSet(e){return(0,c.s3)(this._parent)._isInFeatureSet(e)}async _getFeatures(e,t,r,i){const a=[];-1!==t&&void 0===this._featureCache[t]&&a.push(t);const l=this._maxQueryRate();if(!0===this._checkIfNeedToExpandKnownPage(e,l))return await this._expandPagedSet(e,l,0,0,i),this._getFeatures(e,t,r,i);let u=0;for(let s=e._lastFetchedIndex;s<e._known.length&&(u++,u<=r&&(e._lastFetchedIndex+=1),!(void 0===this._featureCache[e._known[s]]&&(e._known[s]!==t&&a.push(e._known[s]),a.length>=l)));s++);if(0===a.length)return"success";e=new o.Z([],a,e._ordered,null);const d=Math.min(a.length,r);await(this._parent?._getFeatures(e,-1,d,i)),this._checkCancelled(i);const c=[];for(let e=0;e<d;e++){const t=this._parent?._featureFromCache(a[e]);void 0!==t&&c.push({geometry:t.geometry,attributes:t.attributes,id:a[e]})}for(const e of c){const t=[];for(const r of this.adaptedFields)t[r.field.name]=r.extractValue(e);this._featureCache[e.id]=new s.Z({attributes:t,geometry:(0,n.r1)(e.geometry)})}return"success"}async _fetchAndRefineFeatures(){throw new a.EN(a.H9.NeverReach)}async _getFilteredSet(e,t,r,s,i){let n=!1;const a=this._reformulateWithoutAdaptions(r);n=a.cannot,r=a.where;let l=!1;if(null!==s){l=!0;const e=[];for(const t of this.adaptedFields)if(!(t instanceof y)&&!0===s.scanForField(t.field.name)){if(!(t instanceof _)){s=null,l=!1;break}e.push({field:t.field.name,newfield:t.originalField.name})}s&&e.length>0&&(s=s.replaceFields(e))}null!==r?null!==this._extraFilter&&(r=(0,d.$e)(this._extraFilter,r)):r=this._extraFilter,await this._ensureLoaded();const u=await(0,c.s3)(this._parent)._getFilteredSet(e,t,r,s,i);let h;return this._checkCancelled(i),h=!0===n?new o.Z(u._candidates.slice(0).concat(u._known.slice(0)),[],!0===l&&u._ordered,this._clonePageDefinition(u.pagesDefinition)):new o.Z(u._candidates.slice(0),u._known.slice(0),!0===l&&u._ordered,this._clonePageDefinition(u.pagesDefinition)),h}_reformulateWithoutAdaptions(e){const t={cannot:!1,where:e};if(null!==e)for(const r of this.adaptedFields)if(!0===(0,d.hq)(e,r.field.name)){const s=r.rewriteSql(e,this);if(!0!==s.rewritten){t.cannot=!0,t.where=null;break}t.where=s.where}return t}async _stat(e,t,r,s,i,n,a){let l=!1,o=this._reformulateWithoutAdaptions(t);if(l=o.cannot,t=o.where,o=this._reformulateWithoutAdaptions(i),l=l||o.cannot,null!==(i=o.where)?null!==this._extraFilter&&(i=(0,d.$e)(this._extraFilter,i)):i=this._extraFilter,!0===l)return null===i&&""===r&&null===s?this._manualStat(e,t,n,a):{calculated:!1};const u=await(0,c.s3)(this._parent)._stat(e,t,r,s,i,n,a);return!1===u.calculated?null===i&&""===r&&null===s?this._manualStat(e,t,n,a):{calculated:!1}:u}async _canDoAggregates(e,t,r,s,i){if(null===this._parent)return!1;for(let t=0;t<e.length;t++)for(const r of this.adaptedFields)if(e[t].toLowerCase()===r.field.name.toLowerCase()&&!(r instanceof y))return!1;const n=[];for(let e=0;e<t.length;e++){const r=t[e];if(null!==r.workingexpr){const e=this._reformulateWithoutAdaptions(r.workingexpr);if(e.cannot)return!1;const t=r.clone();t.workingexpr=e.where,n.push(t)}else n.push(r)}const a=this._reformulateWithoutAdaptions(i);return!a.cannot&&(null!==(i=a.where)?null!==this._extraFilter&&(i=(0,d.$e)(this._extraFilter,i)):i=this._extraFilter,this._parent._canDoAggregates(e,n,r,s,i))}async _getAggregatePagesDataSourceDefinition(e,t,r,s,i,n,l){if(null===this._parent)throw new a.EN(a.H9.NeverReach);const o=[];for(let e=0;e<t.length;e++){const r=t[e];if(null!==r.workingexpr){const e=this._reformulateWithoutAdaptions(r.workingexpr);if(e.cannot)throw new a.EN(a.H9.NeverReach);const t=r.clone();t.workingexpr=e.where,o.push(t)}else o.push(r)}const u=this._reformulateWithoutAdaptions(i);if(u.cannot)throw new a.EN(a.H9.NeverReach);return null!==(i=u.where)?null!==this._extraFilter&&(i=(0,d.$e)(this._extraFilter,i)):i=this._extraFilter,this._parent._getAggregatePagesDataSourceDefinition(e,o,r,s,i,n,l)}}},84328:(e,t,r)=>{r.d(t,{Z:()=>c});var s=r(59987),i=r(91136),n=r(90961),a=r(90658),l=r(36515),o=r(95330),u=r(41534),d=r(82971);class c extends i.Z{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.actions.AttributeFilter",this._maxProcessing=1e3,this._parent=e.parentfeatureset,e.whereclause instanceof u.WhereClause?(this._whereclause=e.whereclause,this._whereClauseFunction=null):(this._whereClauseFunction=e.whereclause,this._whereclause=null)}_initialiseFeatureSet(){null!==this._parent?(this.fields=this._parent.fields.slice(0),this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types):(this.fields=[],this.typeIdField="",this.objectIdField="",this.globalIdField="",this.spatialReference=new d.Z({wkid:4326}),this.geometryType=a.Qk.point)}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._parent._getFilteredSet("",null,this._whereclause,null,e);return this._checkCancelled(e),null!==this._whereClauseFunction?this._wset=new n.Z(t._candidates.slice(0).concat(t._known.slice(0)),[],t._ordered,this._clonePageDefinition(t.pagesDefinition)):this._wset=new n.Z(t._candidates.slice(0),t._known.slice(0),t._ordered,this._clonePageDefinition(t.pagesDefinition)),this._wset}return this._wset}_isInFeatureSet(e){let t=this._parent?._isInFeatureSet(e);return t===a.dj.NotInFeatureSet?t:(t=this._idstates[e],void 0===t?a.dj.Unknown:t)}_getFeature(e,t,r){return this._parent._getFeature(e,t,r)}_getFeatures(e,t,r,s){return this._parent._getFeatures(e,t,r,s)}_featureFromCache(e){return this._parent._featureFromCache(e)}executeWhereClause(e){return this._whereclause?.testFeature(e)??!1}async executeWhereClauseDeferred(e){if(null!==this._whereClauseFunction){const t=this._whereClauseFunction(e);return(0,o.y8)(t),t}return this.executeWhereClause(e)}async _fetchAndRefineFeatures(e,t,r){const s=new n.Z([],e,!1,null),i=Math.min(t,e.length);if(await(this._parent?._getFeatures(s,-1,i,r)),this._checkCancelled(r),null==this._whereClauseFunction){for(let t=0;t<i;t++){const r=this._parent?._featureFromCache(e[t]);!0===this.executeWhereClause(r)?this._idstates[e[t]]=a.dj.InFeatureSet:this._idstates[e[t]]=a.dj.NotInFeatureSet}return"success"}const l=[];for(let t=0;t<i;t++){const r=this._parent?._featureFromCache(e[t]);l.push(await this.executeWhereClauseDeferred(r))}for(let r=0;r<t;r++)!0===l[r]?this._idstates[e[r]]=a.dj.InFeatureSet:this._idstates[e[r]]=a.dj.NotInFeatureSet;return"success"}async _getFilteredSet(e,t,r,s,i){null!==this._whereClauseFunction||(null!==r?null!==this._whereclause&&(r=(0,l.$e)(this._whereclause,r)):r=this._whereclause),await this._ensureLoaded();const a=await this._parent._getFilteredSet(e,t,r,s,i);let o;return this._checkCancelled(i),o=null!==this._whereClauseFunction?new n.Z(a._candidates.slice(0).concat(a._known.slice(0)),[],a._ordered,this._clonePageDefinition(a.pagesDefinition)):new n.Z(a._candidates.slice(0),a._known.slice(0),a._ordered,this._clonePageDefinition(a.pagesDefinition)),o}async _stat(e,t,r,s,i,n,a){if(null!==this._whereClauseFunction)return null===i&&""===r&&null===s?this._manualStat(e,t,n,a):{calculated:!1};let o=this._whereclause;null!==i&&null!==this._whereclause&&(o=(0,l.$e)(this._whereclause,i));const u=await this._parent._stat(e,t,r,s,o,n,a);return!1===u.calculated?null===i&&""===r&&null===s?this._manualStat(e,t,n,a):{calculated:!1}:u}async _canDoAggregates(e,t,r,s,i){return null===this._whereClauseFunction&&(null!==i?null!==this._whereclause&&(i=(0,l.$e)(this._whereclause,i)):i=this._whereclause,null!==this._parent&&this._parent._canDoAggregates(e,t,r,s,i))}async _getAggregatePagesDataSourceDefinition(e,t,r,i,n,a,o){if(null===this._parent)throw new s.EN(s.H9.NeverReach);return null!==n?null!==this._whereclause&&(n=(0,l.$e)(this._whereclause,n)):n=this._whereclause,this._parent._getAggregatePagesDataSourceDefinition(e,t,r,i,n,a,o)}static registerAction(){i.Z._featuresetFunctions.filter=function(e){if("function"==typeof e)return new c({parentfeatureset:this,whereclause:e});let t=null;return e instanceof u.WhereClause&&(t=e),new c({parentfeatureset:this,whereclause:t})}}}},85065:(e,t,r)=>{r.d(t,{Z:()=>o});var s=r(61363),i=r(59987),n=r(91136),a=r(90961),l=r(3823);class o extends n.Z{constructor(e){super(e),this._orderbyclause=null,this.declaredClass="esri.arcade.featureset.actions.OrderBy",this._maxProcessing=100,this._orderbyclause=e.orderbyclause,this._parent=e.parentfeatureset}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,this._orderbyclause,e);return this._checkCancelled(e),this._wset=t,this._wset}return this._wset}async manualOrderSet(e,t){const r=await this.getIdColumnDictionary(e,[],-1,t);this._orderbyclause?.order(r);const s=new a.Z([],[],!0,null);for(let e=0;e<r.length;e++)s._known.push(r[e].id);return s}async getIdColumnDictionary(e,t,r,i){if(r<e._known.length-1){const n=this._maxQueryRate();if("GETPAGES"===e._known[r+1])return await(0,s.W)(this._parent._expandPagedSet(e,n,0,0,i)),this.getIdColumnDictionary(e,t,r,i);let a=r+1;const l=[];for(;a<e._known.length&&"GETPAGES"!==e._known[a];)l.push(e._known[a]),a++;r+=l.length;const o=await(0,s.W)(this._parent._getFeatureBatch(l,i));this._checkCancelled(i);for(const e of o)t.push({id:e.attributes[this.objectIdField],feature:e});return this.getIdColumnDictionary(e,t,r,i)}return e._candidates.length>0?(await(0,s.W)(this._refineSetBlock(e,this._maxProcessingRate(),i)),this._checkCancelled(i),this.getIdColumnDictionary(e,t,r,i)):t}_isInFeatureSet(e){return this._parent._isInFeatureSet(e)}_getFeatures(e,t,r,s){return this._parent._getFeatures(e,t,r,s)}_featureFromCache(e){if(void 0===this._featureCache[e]){const t=this._parent._featureFromCache(e);if(void 0===t)return;return null===t?null:(this._featureCache[e]=t,t)}return this._featureCache[e]}async _fetchAndRefineFeatures(){throw new i.EN(i.H9.NeverReach)}async _getFilteredSet(e,t,r,s,i){await this._ensureLoaded();const n=await this._parent._getFilteredSet(e,t,r,null===s?this._orderbyclause:s,i);this._checkCancelled(i);const l=new a.Z(n._candidates.slice(0),n._known.slice(0),n._ordered,this._clonePageDefinition(n.pagesDefinition));let o=!0;if(n._candidates.length>0&&(o=!1),!1===l._ordered){let e=await this.manualOrderSet(l,i);return!1===o&&(null===t&&null===r||(e=new a.Z(e._candidates.slice(0).concat(e._known.slice(0)),[],e._ordered,this._clonePageDefinition(e.pagesDefinition)))),e}return l}static registerAction(){n.Z._featuresetFunctions.orderBy=function(e){return""===e?this:new o({parentfeatureset:this,orderbyclause:new l.Z(e)})}}}},63991:(e,t,r)=>{r.d(t,{Z:()=>l});var s=r(59987),i=r(91136),n=r(90961),a=r(90658);class l extends i.Z{constructor(e){super(e),this._topnum=0,this.declaredClass="esri.arcade.featureset.actions.Top",this._countedin=0,this._maxProcessing=100,this._topnum=e.topnum,this._parent=e.parentfeatureset}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._parent._getSet(e);return this._wset=new n.Z(t._candidates.slice(0),t._known.slice(0),!1,this._clonePageDefinition(t.pagesDefinition)),this._setKnownLength(this._wset)>this._topnum&&(this._wset._known=this._wset._known.slice(0,this._topnum)),this._setKnownLength(this._wset)>=this._topnum&&(this._wset._candidates=[]),this._wset}return this._wset}_setKnownLength(e){return e._known.length>0&&"GETPAGES"===e._known[e._known.length-1]?e._known.length-1:e._known.length}_isInFeatureSet(e){const t=this._parent._isInFeatureSet(e);if(t===a.dj.NotInFeatureSet)return t;const r=this._idstates[e];return r===a.dj.InFeatureSet||r===a.dj.NotInFeatureSet?r:t===a.dj.InFeatureSet&&void 0===r?this._countedin<this._topnum?(this._idstates[e]=a.dj.InFeatureSet,this._countedin++,a.dj.InFeatureSet):(this._idstates[e]=a.dj.NotInFeatureSet,a.dj.NotInFeatureSet):a.dj.Unknown}async _expandPagedSet(e,t,r,i,n){if(null===this._parent)throw new s.EN(s.H9.NotImplemented);if(t>this._topnum&&(t=this._topnum),this._countedin>=this._topnum&&e.pagesDefinition.internal.set.length<=e.pagesDefinition.resultOffset){let t=e._known.length;return t>0&&"GETPAGES"===e._known[t-1]&&(e._known.length=t-1),t=e._candidates.length,t>0&&"GETPAGES"===e._candidates[t-1]&&(e._candidates.length=t-1),"success"}const a=await this._parent._expandPagedSet(e,t,r,i,n);return this._setKnownLength(e)>this._topnum&&(e._known.length=this._topnum),this._setKnownLength(e)>=this._topnum&&(e._candidates.length=0),a}async _getFeatures(e,t,r,s){const i=[],a=this._maxQueryRate();if(!0===this._checkIfNeedToExpandKnownPage(e,a))return await this._expandPagedSet(e,a,0,0,s),this._getFeatures(e,t,r,s);-1!==t&&void 0===this._featureCache[t]&&i.push(t);let l=0;for(let s=e._lastFetchedIndex;s<e._known.length&&(l++,l<=r&&(e._lastFetchedIndex+=1),!(void 0===this._featureCache[e._known[s]]&&(e._known[s]!==t&&i.push(e._known[s]),i.length>a)));s++);if(0===i.length)return"success";const o=new n.Z([],i,!1,null),u=Math.min(i.length,r);await this._parent._getFeatures(o,-1,u,s);for(let e=0;e<u;e++){const t=this._parent._featureFromCache(i[e]);void 0!==t&&(this._featureCache[i[e]]=t)}return"success"}async _getFilteredSet(e,t,r,s,i){await this._ensureLoaded();const a=await this._getSet(i);return new n.Z(a._candidates.slice(0).concat(a._known.slice(0)),[],!1,this._clonePageDefinition(a.pagesDefinition))}_refineKnowns(e,t){let r=0,s=null;const i=[];for(let n=0;n<e._candidates.length;n++){const l=this._isInFeatureSet(e._candidates[n]);if(l===a.dj.InFeatureSet){if(e._known.push(e._candidates[n]),r+=1,null===s?s={start:n,end:n}:s.end===n-1?s.end=n:(i.push(s),s={start:n,end:n}),e._known.length>=this._topnum)break}else if(l===a.dj.NotInFeatureSet)null===s?s={start:n,end:n}:s.end===n-1?s.end=n:(i.push(s),s={start:n,end:n}),r+=1;else if(l===a.dj.Unknown)break;if(r>=t)break}null!==s&&i.push(s);for(let t=i.length-1;t>=0;t--)e._candidates.splice(i[t].start,i[t].end-i[t].start+1);this._setKnownLength(e)>this._topnum&&(e._known=e._known.slice(0,this._topnum)),this._setKnownLength(e)>=this._topnum&&(e._candidates=[])}async _stat(){return{calculated:!1}}async _canDoAggregates(){return!1}static registerAction(){i.Z._featuresetFunctions.top=function(e){return new l({parentfeatureset:this,topnum:e})}}}},17805:(e,t,r)=>{r.d(t,{Z:()=>f});var s=r(38171),i=r(59987),n=r(91136),a=r(90961),l=r(90658),o=r(36515),u=r(9361),d=r(19238),c=r(16451),h=r(1231),p=r(14165);class f extends n.Z{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerMemory",this._removeGeometry=!1,this._overrideFields=null,this._forceIsTable=!1,e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,!0===e.isTable&&(this._forceIsTable=!0),void 0!==e.outFields&&(this._overrideFields=e.outFields),void 0!==e.includeGeometry&&(this._removeGeometry=!1===e.includeGeometry)}_maxQueryRate(){return l.tI}end(){return this._layer}optimisePagingFeatureQueries(){}async loadImpl(){return!0===this._layer.loaded?(this._initialiseFeatureSet(),this):(await this._layer.load(),this._initialiseFeatureSet(),this)}get gdbVersion(){return""}_initialiseFeatureSet(){if(null==this.spatialReference&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._layer.geometryType,this.fields=this._layer.fields.slice(0),this._layer.outFields)if(1===this._layer.outFields.length&&"*"===this._layer.outFields[0]);else{const e=[];for(const t of this.fields)if("oid"===t.type)e.push(t);else for(const r of this._layer.outFields)if(r.toLowerCase()===t.name.toLowerCase()){e.push(t);break}this.fields=e}if(null!==this._overrideFields)if(1===this._overrideFields.length&&"*"===this._overrideFields[0])this._overrideFields=null;else{const e=[],t=[];for(const r of this.fields)if("oid"===r.type)e.push(r),t.push(r.name);else for(const s of this._overrideFields)if(s.toLowerCase()===r.name.toLowerCase()){e.push(r),t.push(r.name);break}this.fields=e,this._overrideFields=t}this.objectIdField=this._layer.objectIdField;for(const e of this.fields)"global-id"===e.type&&(this.globalIdField=e.name);this.hasM=this._layer.supportsM,this.hasZ=this._layer.supportsZ,this._databaseType=l.Bj.Standardised,this.typeIdField=this._layer.typeIdField,this.types=this._layer.types}isTable(){return this._forceIsTable||this._layer.isTable||"table"===this._layer.type||!this._layer.geometryType}_isInFeatureSet(){return l.dj.InFeatureSet}_candidateIdTransform(e){return e}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,t}return this._wset}_changeFeature(e){const t={};for(const r of this.fields)t[r.name]=e.attributes[r.name];return new s.Z({geometry:!0===this._removeGeometry?null:e.geometry,attributes:t})}async _getFilteredSet(e,t,r,s,i){let n="",u=!1;if(null!==s&&(n=s.constructClause(),u=!0),this.isTable()&&t&&null!==e&&""!==e)return new a.Z([],[],!0,null);const d=new p.Z;d.where=null===r?null===t?"1=1":"":(0,o.zR)(r,l.Bj.Standardised),d.spatialRelationship=this._makeRelationshipEnum(e),d.outSpatialReference=this.spatialReference,d.orderByFields=""!==n?n.split(","):null,d.geometry=null===t?null:t,d.returnGeometry=!0,d.relationParameter=this._makeRelationshipParam(e);const c=await this._layer.queryFeatures(d);if(null===c)return new a.Z([],[],u,null);this._checkCancelled(i);const h=[];return c.features.forEach((e=>{const t=e.attributes[this._layer.objectIdField];h.push(t),this._featureCache[t]=this._changeFeature(e)})),new a.Z([],h,u,null)}_makeRelationshipEnum(e){if(e.includes("esriSpatialRelRelation"))return"relation";switch(e){case"esriSpatialRelRelation":return"relation";case"esriSpatialRelIntersects":return"intersects";case"esriSpatialRelContains":return"contains";case"esriSpatialRelOverlaps":return"overlaps";case"esriSpatialRelWithin":return"within";case"esriSpatialRelTouches":return"touches";case"esriSpatialRelCrosses":return"crosses";case"esriSpatialRelEnvelopeIntersects":return"envelope-intersects"}return e}_makeRelationshipParam(e){return e.includes("esriSpatialRelRelation")?e.split(":")[1]:""}async _queryAllFeatures(){if(this._wset)return this._wset;const e=new p.Z;if(e.where="1=1",await this._ensureLoaded(),this._layer.source&&this._layer.source.items){const e=[];return this._layer.source.items.forEach((t=>{const r=t.attributes[this._layer.objectIdField];e.push(r),this._featureCache[r]=this._changeFeature(t)})),this._wset=new a.Z([],e,!1,null),this._wset}const t=await this._layer.queryFeatures(e),r=[];return t.features.forEach((e=>{const t=e.attributes[this._layer.objectIdField];r.push(t),this._featureCache[t]=this._changeFeature(e)})),this._wset=new a.Z([],r,!1,null),this._wset}async _getFeatures(e,t,r){const s=[];-1!==t&&void 0===this._featureCache[t]&&s.push(t);for(let i=e._lastFetchedIndex;i<e._known.length&&(e._lastFetchedIndex+=1,!(void 0===this._featureCache[e._known[i]]&&(e._known[i]!==t&&s.push(e._known[i]),s.length>r)));i++);if(0===s.length)return"success";throw new i.EN(i.H9.MissingFeatures)}async _refineSetBlock(e){return e}async _stat(){return{calculated:!1}}async _canDoAggregates(){return!1}relationshipMetaData(){return[]}static _cloneAttr(e){const t={};for(const r in e)t[r]=e[r];return t}nativeCapabilities(){return{title:this._layer.title??"",canQueryRelated:!1,source:this,capabilities:this._layer.capabilities,databaseType:this._databaseType,requestStandardised:!0}}static create(e,t){let r=e.layerDefinition.objectIdField;const s=e.layerDefinition.typeIdField?e.layerDefinition.typeIdField:"",i=[];if(e.layerDefinition.types)for(const t of e.layerDefinition.types)i.push(c.Z.fromJSON(t));let n=e.layerDefinition.geometryType;void 0===n&&(n=e.featureSet.geometryType||"");let a=e.featureSet.features;const l=t.toJSON();if(""===r||void 0===r){let t=!1;for(const s of e.layerDefinition.fields)if("oid"===s.type||"esriFieldTypeOID"===s.type){r=s.name,t=!0;break}if(!1===t){let t="FID",s=!0,i=0;for(;s;){let r=!0;for(const s of e.layerDefinition.fields)if(s.name===t){r=!1;break}!0===r?s=!1:(i++,t="FID"+i.toString())}e.layerDefinition.fields.push({type:"esriFieldTypeOID",name:t,alias:t});const n=[];for(let r=0;r<a.length;r++)n.push({geometry:e.featureSet.features[r].geometry,attributes:e.featureSet.features[r].attributes?this._cloneAttr(e.featureSet.features[r].attributes):{}}),n[r].attributes[t]=r;a=n,r=t}}const o=[];for(const t of e.layerDefinition.fields)t instanceof h.Z?o.push(t):o.push(h.Z.fromJSON(t));let p=n;switch(p||(p=""),p){case"esriGeometryPoint":p="point";break;case"esriGeometryPolyline":p="polyline";break;case"esriGeometryPolygon":p="polygon";break;case"esriGeometryExtent":p="extent";break;case"esriGeometryMultipoint":p="multipoint";break;case"":case"esriGeometryNull":p="esriGeometryNull"}if("esriGeometryNull"!==p)for(const e of a)e.geometry&&e.geometry instanceof u.Z==0&&(e.geometry.type=p,void 0===e.geometry.spatialReference&&(e.geometry.spatialReference=l));else for(const e of a)e.geometry&&(e.geometry=null);const y={outFields:["*"],source:a,fields:o,types:i,typeIdField:s,objectIdField:r,spatialReference:t};""!==p&&"esriGeometryNull"!==p&&null!==p&&(y.geometryType=p);const _=new d.default(y);return new f({layer:_,spatialReference:t,isTable:null===p||""===p||"esriGeometryNull"===p})}async queryAttachments(){return[]}async getFeatureByObjectId(e){const t=new p.Z;t.where=this.objectIdField+"="+e.toString();const r=await this._layer.queryFeatures(t);return 1===r.features.length?r.features[0]:null}async getOwningSystemUrl(){return""}async getIdentityUser(){return""}get preferredTimeReference(){return this._layer?.preferredTimeReference?.toJSON()??null}get dateFieldsTimeReference(){return this._layer?.dateFieldsTimeReference?.toJSON()??null}get datesInUnknownTimezone(){return this._layer?.datesInUnknownTimezone}get editFieldsInfo(){return this._layer?.editFieldsInfo?.toJSON()??null}get timeInfo(){return this._layer?.timeInfo?.toJSON()??null}}},3823:(e,t,r)=>{function s(e,t){return e===t?0:null===e?-1:null===t?1:e<t?-1:1}r.d(t,{Z:()=>i});class i{constructor(e){const t=e.split(",");this._fields=[],this._directions=[];for(let e=0;e<t.length;e++){const r=t[e].match(/\S+/g);this._fields.push(r[0]),2===r.length?"asc"===r[1].toLowerCase()?this._directions.push(1):this._directions.push(0):this._directions.push(1)}}constructClause(){let e="";for(let t=0;t<this._fields.length;t++)0!==t&&(e+=","),e+=this._fields[t],1===this._directions[t]?e+=" ASC":e+=" DESC";return e}order(e){e.sort(((e,t)=>{for(let r=0;r<this._fields.length;r++){const i=this.featureValue(e.feature,this._fields[r],r),n=this.featureValue(t.feature,this._fields[r],r);let a=0;if(a=1===this._directions[r]?s(i,n):-1*s(i,n),0!==a)return a}return 0}))}scanForField(e){for(let t=0;t<this._fields.length;t++)if(this._fields[t].toLowerCase().trim()===e.toLowerCase().trim())return!0;return!1}replaceFields(e){let t="";for(let r=0;r<this._fields.length;r++){0!==r&&(t+=",");let s=this._fields[r];for(const t of e)if(s.toLowerCase()===t.field.toLowerCase()){s=t.newfield;break}t+=s,1===this._directions[r]?t+=" ASC":t+=" DESC"}return new i(t)}featureValue(e,t,r){const s=e.attributes[t];if(void 0!==s)return s;for(const s in e.attributes)if(t.toLowerCase()===s.toLowerCase())return this._fields[r]=s,e.attributes[s];return null}}},25929:(e,t,r)=>{r.d(t,{M:()=>y,a:()=>g,e:()=>p,f:()=>n,i:()=>c,p:()=>h,r:()=>l,t:()=>a,w:()=>o});var s=r(70586),i=r(17452);function n(e,t){const r=t&&t.url&&t.url.path;if(e&&r&&(e=(0,i.hF)(e,r,{preserveProtocolRelative:!0}),t.portalItem&&t.readResourcePaths)){const r=(0,i.PF)(e,t.portalItem.itemUrl);null!=r&&d.test(r)&&t.readResourcePaths.push(t.portalItem.resourceFromPath(r).path)}return f(e,t&&t.portal)}function a(e,t,r=y.YES){if(null==e)return e;!(0,i.YP)(e)&&t&&t.blockedRelativeUrls&&t.blockedRelativeUrls.push(e);let s=(0,i.hF)(e);if(t){const r=t.verifyItemRelativeUrls&&t.verifyItemRelativeUrls.rootPath||t.url&&t.url.path;if(r){const n=f(r,t.portal),a=f(s,t.portal);s=(0,i.PF)(a,n,n),null!=s&&s!==a&&s!==e&&t.verifyItemRelativeUrls&&t.verifyItemRelativeUrls.writtenUrls.push(s)}}return s=p(s,t?.portal),(0,i.YP)(s)&&(s=(0,i.Fv)(s)),t?.resources&&t?.portalItem&&!(0,i.YP)(s)&&!(0,i.HK)(s)&&r===y.YES&&t.resources.toKeep.push({resource:t.portalItem.resourceFromPath(s),compress:!1}),s}function l(e,t,r){return n(e,r)}function o(e,t,r,s){const i=a(e,s);void 0!==i&&(t[r]=i)}const u=/\/items\/([^\/]+)\/resources\/(.*)/,d=/^\.\/resources\//;function c(e){return(e?.match(u)??null)?.[1]??null}function h(e){const t=e?.match(u)??null;if(null==t)return null;const r=t[2],n=r.lastIndexOf("/");if(-1===n){const{path:e,extension:t}=(0,i.fZ)(r);return{prefix:null,filename:e,extension:(0,s.Wg)(t)}}const{path:a,extension:l}=(0,i.fZ)(r.slice(n+1));return{prefix:r.slice(0,n),filename:a,extension:(0,s.Wg)(l)}}function p(e,t){return t&&!t.isPortal&&t.urlKey&&t.customBaseUrl?(0,i.Ie)(e,`${t.urlKey}.${t.customBaseUrl}`,t.portalHostname):e}function f(e,t){if(!t||t.isPortal||!t.urlKey||!t.customBaseUrl)return e;const r=`${t.urlKey}.${t.customBaseUrl}`,s=(0,i.TI)();return(0,i.D6)(s,`${s.scheme}://${r}`)?(0,i.Ie)(e,t.portalHostname,r):(0,i.Ie)(e,r,t.portalHostname)}var y,_;(_=y||(y={}))[_.YES=0]="YES",_[_.NO=1]="NO";const g=Object.freeze(Object.defineProperty({__proto__:null,get MarkKeep(){return y},ensureMainOnlineDomain:p,fromJSON:n,itemIdFromResourceUrl:c,prefixAndFilenameFromResourceUrl:h,read:l,toJSON:a,write:o},Symbol.toStringTag,{value:"Module"}))},32448:(e,t,r)=>{r.d(t,{Z:()=>o});var s=r(43697),i=r(15923),n=r(50758),a=r(52011);class l{constructor(){this._emitter=new l.EventEmitter(this)}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}}!function(e){class t{constructor(e=null){this._target=e,this._listenersMap=null}clear(){this._listenersMap&&this._listenersMap.clear(),this._listenersMap=null}emit(e,t){const r=this._listenersMap&&this._listenersMap.get(e);if(!r)return!1;const s=this._target||this;return[...r].forEach((e=>{e.call(s,t)})),r.length>0}on(e,t){if(Array.isArray(e)){const r=e.map((e=>this.on(e,t)));return(0,n.AL)(r)}if(e.includes(","))throw new TypeError("Evented.on() with a comma delimited string of event types is not supported");this._listenersMap||(this._listenersMap=new Map);const r=this._listenersMap.get(e)||[];return r.push(t),this._listenersMap.set(e,r),{remove:()=>{const r=this._listenersMap&&this._listenersMap.get(e)||[],s=r.indexOf(t);s>=0&&r.splice(s,1)}}}once(e,t){const r=this.on(e,(e=>{r.remove(),t.call(null,e)}));return r}hasEventListener(e){const t=this._listenersMap&&this._listenersMap.get(e);return null!=t&&t.length>0}}e.EventEmitter=t,e.EventedMixin=e=>{let r=class extends e{constructor(){super(...arguments),this._emitter=new t}destroy(){this._emitter.clear()}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}};return r=(0,s._)([(0,a.j)("esri.core.Evented")],r),r};let r=class extends i.Z{constructor(){super(...arguments),this._emitter=new l.EventEmitter(this)}destroy(){this._emitter.clear()}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}};r=(0,s._)([(0,a.j)("esri.core.Evented")],r),e.EventedAccessor=r}(l||(l={}));const o=l},3920:(e,t,r)=>{r.d(t,{p:()=>u,r:()=>d});var s=r(43697),i=r(15923),n=r(61247),a=r(5600),l=r(52011),o=r(72762);const u=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new n.Z}get updatingHandles(){return this._get("updatingHandles")||new o.t}};return(0,s._)([(0,a.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,s._)([(0,a.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,s._)([(0,l.j)("esri.core.HandleOwner")],t),t};let d=class extends(u(i.Z)){};d=(0,s._)([(0,l.j)("esri.core.HandleOwner")],d)},10699:(e,t,r)=>{r.d(t,{IG:()=>a,iv:()=>l});var s=r(43697),i=r(52011);let n=0;const a=e=>{let t=class extends e{constructor(...e){super(...e),Object.defineProperty(this,"uid",{writable:!1,configurable:!1,value:Date.now().toString(16)+"-object-"+n++})}};return t=(0,s._)([(0,i.j)("esri.core.Identifiable")],t),t},l=e=>{let t=class extends e{constructor(...e){super(...e),Object.defineProperty(this,"uid",{writable:!1,configurable:!1,value:n++})}};return t=(0,s._)([(0,i.j)("esri.core.NumericIdentifiable")],t),t};let o=class extends(a(class{})){};o=(0,s._)([(0,i.j)("esri.core.Identifiable")],o)},10661:(e,t,r)=>{r.d(t,{s:()=>i});var s=r(42100);class i extends s.s{notify(){const e=this._observers;if(e&&e.length>0){const t=e.slice();for(const e of t)e.onInvalidated(),e.onCommitted()}}}},66643:(e,t,r)=>{r.d(t,{Ed:()=>u,UI:()=>d,mt:()=>f,q6:()=>p,vr:()=>y});var s=r(43697),i=r(15923),n=r(70586),a=r(95330),l=r(5600),o=(r(75215),r(67676),r(52011));function u(e,t,r){return(0,a.as)(e.map(((e,s)=>t.apply(r,[e,s]))))}async function d(e,t,r){return(await(0,a.as)(e.map(((e,s)=>t.apply(r,[e,s]))))).map((e=>e.value))}function c(e){return{ok:!0,value:e}}function h(e){return{ok:!1,error:e}}async function p(e){if((0,n.Wi)(e))return{ok:!1,error:new Error("no promise provided")};try{return c(await e)}catch(e){return h(e)}}async function f(e){try{return c(await e)}catch(e){return(0,a.r9)(e),h(e)}}function y(e,t){return new _(e,t)}let _=class extends i.Z{get value(){return e=this._result,(0,n.pC)(e)&&!0===e.ok?e.value:null;var e}get error(){return e=this._result,(0,n.pC)(e)&&!1===e.ok?e.error:null;var e}get finished(){return(0,n.pC)(this._result)}constructor(e,t){super({}),this._result=null,this._abortHandle=null,this.abort=()=>{this._abortController=(0,n.IM)(this._abortController)},this.remove=this.abort,this._abortController=new AbortController;const{signal:r}=this._abortController;this.promise=e(r),this.promise.then((e=>{this._result=c(e),this._cleanup()}),(e=>{this._result=h(e),this._cleanup()})),this._abortHandle=(0,a.fu)(t,this.abort)}normalizeCtorArgs(){return{}}destroy(){this.abort()}_cleanup(){this._abortHandle=(0,n.hw)(this._abortHandle),this._abortController=null}};(0,s._)([(0,l.Cb)()],_.prototype,"value",null),(0,s._)([(0,l.Cb)()],_.prototype,"error",null),(0,s._)([(0,l.Cb)()],_.prototype,"finished",null),(0,s._)([(0,l.Cb)()],_.prototype,"promise",void 0),(0,s._)([(0,l.Cb)()],_.prototype,"_result",void 0),_=(0,s._)([(0,o.j)("esri.core.asyncUtils.ReactiveTask")],_)},42033:(e,t,r)=>{r.d(t,{E:()=>i,_:()=>n});var s=r(70586);async function i(e,t){const{WhereClause:s}=await r.e(1534).then(r.bind(r,41534));return s.create(e,t)}function n(e,t){return(0,s.pC)(e)?(0,s.pC)(t)?`(${e}) AND (${t})`:e:t}},72762:(e,t,r)=>{r.d(t,{t:()=>c});var s=r(43697),i=r(15923),n=r(61247),a=r(70586),l=r(17445),o=r(1654),u=r(5600),d=r(52011);let c=class extends i.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new n.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,r={}){return this._installWatch(e,t,r,l.YP)}addWhen(e,t,r={}){return this._installWatch(e,t,r,l.gx)}addOnCollectionChange(e,t,{initial:r=!1,final:s=!1}={}){const i=++this._handleId;return this._handles.add([(0,l.on)(e,"after-changes",this._createSyncUpdatingCallback(),l.Z_),(0,l.on)(e,"change",t,{onListenerAdd:r?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:s?e=>t({added:[],removed:e.toArray()}):void 0})],i),{remove:()=>this._handles.remove(i)}}addPromise(e){if((0,a.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(h)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const r=()=>this._handles.remove(t);return e.then(r,r),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,r={},s){const i=++this._handleId;r.sync||this._installSyncUpdatingWatch(e,i);const n=s(e,t,r);return this._handles.add(n,i),{remove:()=>this._handles.remove(i)}}_installSyncUpdatingWatch(e,t){const r=this._createSyncUpdatingCallback(),s=(0,l.YP)(e,r,{sync:!0,equals:()=>!1});return this._handles.add(s,t),s}_createSyncUpdatingCallback(){return()=>{this._handles.remove(h),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,o.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(h))})),h)}}};(0,s._)([(0,u.Cb)({readOnly:!0})],c.prototype,"updating",void 0),c=(0,s._)([(0,d.j)("esri.core.support.WatchUpdatingTracking")],c);const h=-42},87085:(e,t,r)=>{r.d(t,{Z:()=>w});var s=r(43697),i=(r(66577),r(3172)),n=r(20102),a=r(32448),l=r(10699),o=r(83379),u=r(92604),d=r(95330),c=r(17452),h=r(5600),p=(r(75215),r(67676),r(52011)),f=r(68773),y=r(6570),_=r(82971);let g=0,m=class extends(a.Z.EventedMixin((0,l.IG)(o.Z))){constructor(){super(...arguments),this.attributionDataUrl=null,this.fullExtent=new y.Z(-180,-90,180,90,_.Z.WGS84),this.id=Date.now().toString(16)+"-layer-"+g++,this.legendEnabled=!0,this.listMode="show",this.opacity=1,this.parent=null,this.popupEnabled=!0,this.attributionVisible=!0,this.spatialReference=_.Z.WGS84,this.title=null,this.type=null,this.url=null,this.visible=!0}static async fromArcGISServerUrl(e){const t="string"==typeof e?{url:e}:e;return(await r.e(3529).then(r.bind(r,63529))).fromUrl(t)}static fromPortalItem(e){return async function(e){const t="portalItem"in e?e:{portalItem:e},s=await r.e(8008).then(r.bind(r,28008));try{return await s.fromItem(t)}catch(e){const r=t&&t.portalItem,s=r&&r.id||"unset",i=r&&r.portal&&r.portal.url||f.Z.portalUrl;throw u.Z.getLogger("esri.layers.support.fromPortalItem").error("#fromPortalItem()","Failed to create layer from portal item (portal: '"+i+"', id: '"+s+"')",e),e}}(e)}initialize(){this.when().catch((e=>{(0,d.D_)(e)||u.Z.getLogger(this.declaredClass).error("#load()",`Failed to load layer (title: '${this.title??"no title"}', id: '${this.id??"no id"}')`,{error:e})}))}destroy(){if(this.parent){const e=this,t=this.parent;"layers"in t&&t.layers.includes(e)?t.layers.remove(e):"tables"in t&&t.tables.includes(e)?t.tables.remove(e):"baseLayers"in t&&t.baseLayers.includes(e)?t.baseLayers.remove(e):"baseLayers"in t&&t.referenceLayers.includes(e)&&t.referenceLayers.remove(e)}}get hasAttributionData(){return null!=this.attributionDataUrl}get parsedUrl(){return(0,c.mN)(this.url)}async fetchAttributionData(){const e=this.attributionDataUrl;if(this.hasAttributionData&&e)return(await(0,i.default)(e,{query:{f:"json"},responseType:"json"})).data;throw new n.Z("layer:no-attribution-data","Layer does not have attribution data")}};(0,s._)([(0,h.Cb)({type:String})],m.prototype,"attributionDataUrl",void 0),(0,s._)([(0,h.Cb)({type:y.Z})],m.prototype,"fullExtent",void 0),(0,s._)([(0,h.Cb)({readOnly:!0})],m.prototype,"hasAttributionData",null),(0,s._)([(0,h.Cb)({type:String,clonable:!1})],m.prototype,"id",void 0),(0,s._)([(0,h.Cb)({type:Boolean,nonNullable:!0})],m.prototype,"legendEnabled",void 0),(0,s._)([(0,h.Cb)({type:["show","hide","hide-children"]})],m.prototype,"listMode",void 0),(0,s._)([(0,h.Cb)({type:Number,range:{min:0,max:1},nonNullable:!0})],m.prototype,"opacity",void 0),(0,s._)([(0,h.Cb)({clonable:!1})],m.prototype,"parent",void 0),(0,s._)([(0,h.Cb)({readOnly:!0})],m.prototype,"parsedUrl",null),(0,s._)([(0,h.Cb)({type:Boolean})],m.prototype,"popupEnabled",void 0),(0,s._)([(0,h.Cb)({type:Boolean})],m.prototype,"attributionVisible",void 0),(0,s._)([(0,h.Cb)({type:_.Z})],m.prototype,"spatialReference",void 0),(0,s._)([(0,h.Cb)({type:String})],m.prototype,"title",void 0),(0,s._)([(0,h.Cb)({readOnly:!0,json:{read:!1}})],m.prototype,"type",void 0),(0,s._)([(0,h.Cb)()],m.prototype,"url",void 0),(0,s._)([(0,h.Cb)({type:Boolean,nonNullable:!0})],m.prototype,"visible",void 0),m=(0,s._)([(0,p.j)("esri.layers.Layer")],m);const w=m},54295:(e,t,r)=>{r.d(t,{V:()=>a});var s=r(43697),i=r(5600),n=(r(75215),r(67676),r(52011));const a=e=>{let t=class extends e{get apiKey(){return this._isOverridden("apiKey")?this._get("apiKey"):"portalItem"in this?this.portalItem?.apiKey:null}set apiKey(e){null!=e?this._override("apiKey",e):(this._clearOverride("apiKey"),this.clear("apiKey","user"))}};return(0,s._)([(0,i.Cb)({type:String})],t.prototype,"apiKey",null),t=(0,s._)([(0,n.j)("esri.layers.mixins.APIKeyMixin")],t),t}},17287:(e,t,r)=>{r.d(t,{Y:()=>u});var s=r(43697),i=r(92604),n=r(70586),a=r(5600),l=(r(75215),r(67676),r(52011)),o=r(66677);const u=e=>{let t=class extends e{get title(){if(this._get("title")&&"defaults"!==this.originOf("title"))return this._get("title");if(this.url){const e=(0,o.Qc)(this.url);if((0,n.pC)(e)&&e.title)return e.title}return this._get("title")||""}set title(e){this._set("title",e)}set url(e){this._set("url",(0,o.Nm)(e,i.Z.getLogger(this.declaredClass)))}};return(0,s._)([(0,a.Cb)()],t.prototype,"title",null),(0,s._)([(0,a.Cb)({type:String})],t.prototype,"url",null),t=(0,s._)([(0,l.j)("esri.layers.mixins.ArcGISService")],t),t}},70082:(e,t,r)=>{r.d(t,{Z:()=>c});var s=r(43697),i=r(2368),n=r(35454),a=r(96674),l=r(5600),o=(r(75215),r(67676),r(52011));const u=new n.X({esriFeatureEditToolAutoCompletePolygon:"auto-complete-polygon",esriFeatureEditToolCircle:"circle",esriFeatureEditToolEllipse:"ellipse",esriFeatureEditToolFreehand:"freehand",esriFeatureEditToolLine:"line",esriFeatureEditToolNone:"none",esriFeatureEditToolPoint:"point",esriFeatureEditToolPolygon:"polygon",esriFeatureEditToolRectangle:"rectangle",esriFeatureEditToolArrow:"arrow",esriFeatureEditToolTriangle:"triangle",esriFeatureEditToolLeftArrow:"left-arrow",esriFeatureEditToolRightArrow:"right-arrow",esriFeatureEditToolUpArrow:"up-arrow",esriFeatureEditToolDownArrow:"down-arrow"});let d=class extends((0,i.J)(a.wq)){constructor(e){super(e),this.name=null,this.description=null,this.drawingTool=null,this.prototype=null,this.thumbnail=null}};(0,s._)([(0,l.Cb)({json:{write:!0}})],d.prototype,"name",void 0),(0,s._)([(0,l.Cb)({json:{write:!0}})],d.prototype,"description",void 0),(0,s._)([(0,l.Cb)({json:{read:u.read,write:u.write}})],d.prototype,"drawingTool",void 0),(0,s._)([(0,l.Cb)({json:{write:!0}})],d.prototype,"prototype",void 0),(0,s._)([(0,l.Cb)({json:{write:!0}})],d.prototype,"thumbnail",void 0),d=(0,s._)([(0,o.j)("esri.layers.support.FeatureTemplate")],d);const c=d},16451:(e,t,r)=>{r.d(t,{Z:()=>p});var s=r(43697),i=r(2368),n=r(96674),a=r(5600),l=(r(75215),r(67676),r(71715)),o=r(52011),u=r(30556),d=r(72729),c=r(70082);let h=class extends((0,i.J)(n.wq)){constructor(e){super(e),this.id=null,this.name=null,this.domains=null,this.templates=null}readDomains(e){const t={};for(const r of Object.keys(e))t[r]=(0,d.im)(e[r]);return t}writeDomains(e,t){const r={};for(const t of Object.keys(e))e[t]&&(r[t]=e[t]?.toJSON());t.domains=r}};(0,s._)([(0,a.Cb)({json:{write:!0}})],h.prototype,"id",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],h.prototype,"name",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],h.prototype,"domains",void 0),(0,s._)([(0,l.r)("domains")],h.prototype,"readDomains",null),(0,s._)([(0,u.c)("domains")],h.prototype,"writeDomains",null),(0,s._)([(0,a.Cb)({type:[c.Z],json:{write:!0}})],h.prototype,"templates",void 0),h=(0,s._)([(0,o.j)("esri.layers.support.FeatureType")],h);const p=h},56765:(e,t,r)=>{r.d(t,{Z:()=>d});var s,i=r(43697),n=r(46791),a=r(96674),l=r(5600),o=(r(75215),r(67676),r(52011));let u=s=class extends a.wq{constructor(e){super(e),this.floorField=null,this.viewAllMode=!1,this.viewAllLevelIds=new n.Z}clone(){return new s({floorField:this.floorField,viewAllMode:this.viewAllMode,viewAllLevelIds:this.viewAllLevelIds})}};(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],u.prototype,"floorField",void 0),(0,i._)([(0,l.Cb)({json:{read:!1,write:!1}})],u.prototype,"viewAllMode",void 0),(0,i._)([(0,l.Cb)({json:{read:!1,write:!1}})],u.prototype,"viewAllLevelIds",void 0),u=s=(0,i._)([(0,o.j)("esri.layers.support.LayerFloorInfo")],u);const d=u},66677:(e,t,r)=>{r.d(t,{B5:()=>c,DR:()=>p,G:()=>S,M8:()=>_,Nm:()=>g,Qc:()=>h,XG:()=>m,a7:()=>y,ld:()=>f,wH:()=>w});var s=r(70586),i=r(17452),n=r(25929);const a={mapserver:"MapServer",imageserver:"ImageServer",featureserver:"FeatureServer",sceneserver:"SceneServer",streamserver:"StreamServer",vectortileserver:"VectorTileServer"},l=Object.values(a),o=new RegExp(`^((?:https?:)?\\/\\/\\S+?\\/rest\\/services\\/(.+?)\\/(${l.join("|")}))(?:\\/(?:layers\\/)?(\\d+))?`,"i"),u=new RegExp(`^((?:https?:)?\\/\\/\\S+?\\/([^\\/\\n]+)\\/(${l.join("|")}))(?:\\/(?:layers\\/)?(\\d+))?`,"i"),d=/(.*?)\/(?:layers\/)?(\d+)\/?$/i;function c(e){return!!o.test(e)}function h(e){if((0,s.Wi)(e))return null;const t=(0,i.mN)(e),r=t.path.match(o)||t.path.match(u);if(!r)return null;const[,n,l,d,c]=r,h=l.indexOf("/");return{title:f(-1!==h?l.slice(h+1):l),serverType:a[d.toLowerCase()],sublayer:null!=c&&""!==c?parseInt(c,10):null,url:{path:n}}}function p(e){const t=(0,i.mN)(e).path.match(d);return t?{serviceUrl:t[1],sublayerId:Number(t[2])}:null}function f(e){return(e=e.replace(/\s*[/_]+\s*/g," "))[0].toUpperCase()+e.slice(1)}function y(e,t){const r=[];if(e){const t=h(e);(0,s.pC)(t)&&t.title&&r.push(t.title)}if(t){const e=f(t);r.push(e)}if(2===r.length){if(r[0].toLowerCase().includes(r[1].toLowerCase()))return r[0];if(r[1].toLowerCase().includes(r[0].toLowerCase()))return r[1]}return r.join(" - ")}function _(e){if(!e)return!1;const t=(e=e.toLowerCase()).includes(".arcgis.com/"),r=e.includes("//services")||e.includes("//tiles")||e.includes("//features");return t&&r}function g(e,t){return e?(0,i.Qj)((0,i.Hu)(e,t)):e}function m(e){let{url:t}=e;if(!t)return{url:t};t=(0,i.Hu)(t,e.logger);const r=(0,i.mN)(t),n=h(r.path);let a;if((0,s.pC)(n))null!=n.sublayer&&null==e.layer.layerId&&(a=n.sublayer),t=n.url.path;else if(e.nonStandardUrlAllowed){const e=p(r.path);(0,s.pC)(e)&&(t=e.serviceUrl,a=e.sublayerId)}return{url:(0,i.Qj)(t),layerId:a}}function w(e,t,r,s,a){(0,n.w)(t,s,"url",a),s.url&&null!=e.layerId&&(s.url=(0,i.v_)(s.url,r,e.layerId.toString()))}function S(e){if(!e)return!1;const t=e.toLowerCase(),r=t.includes("/services/"),s=t.includes("/mapserver/wmsserver"),i=t.includes("/imageserver/wmsserver"),n=t.includes("/wmsserver");return r&&(s||i||n)}},84230:(e,t,r)=>{r.d(t,{A2:()=>l,S1:()=>c,fb:()=>a,ln:()=>h,oP:()=>d,rQ:()=>o,y2:()=>u});var s=r(40330),i=r(3172),n=r(70586);const a={Point:"SceneLayer","3DObject":"SceneLayer",IntegratedMesh:"IntegratedMeshLayer",PointCloud:"PointCloudLayer",Building:"BuildingSceneLayer"};function l(e){const t=e?.type;return"building-scene"===t||"integrated-mesh"===t||"point-cloud"===t||"scene"===t}function o(e){return"feature"===e?.type&&!e.url&&"memory"===e.source?.type}function u(e){return"feature"===e?.type&&"feature-layer"===e.source?.type}async function d(e,t){const r=s.id?.findServerInfo(e);if(null!=r?.currentVersion)return r.owningSystemUrl||null;const a=e.toLowerCase().indexOf("/rest/services");if(-1===a)return null;const l=`${e.substring(0,a)}/rest/info`,o=(0,n.pC)(t)?t.signal:null,{data:u}=await(0,i.default)(l,{query:{f:"json"},responseType:"json",signal:o});return u?.owningSystemUrl||null}function c(e){return function(e){if(!("capabilities"in e))return!1;switch(e.type){case"csv":case"feature":case"geojson":case"imagery":case"knowledge-graph-sublayer":case"ogc-feature":case"oriented-imagery":case"scene":case"subtype-group":case"subtype-sublayer":case"wfs":return!0;default:return!1}}(e)?"effectiveCapabilities"in e?e.effectiveCapabilities:e.capabilities:null}function h(e){return!!function(e){if(!("editingEnabled"in e))return!1;switch(e.type){case"csv":case"feature":case"geojson":case"oriented-imagery":case"scene":case"subtype-group":case"subtype-sublayer":return!0;default:return!1}}(e)&&("effectiveEditingEnabled"in e?e.effectiveEditingEnabled:e.editingEnabled)}},72064:(e,t,r)=>{r.d(t,{h:()=>c});var s=r(80442),i=r(70586),n=r(66677);const a={name:"supportsName",size:"supportsSize",contentType:"supportsContentType",keywords:"supportsKeywords",exifInfo:"supportsExifInfo"};function l(e,t,r){return!!(e&&e.hasOwnProperty(t)?e[t]:r)}function o(e,t,r){return e&&e.hasOwnProperty(t)?e[t]:r}function u(e){const t=e?.supportedSpatialAggregationStatistics?.map((e=>e.toLowerCase()));return{envelope:!!t?.includes("envelopeaggregate"),centroid:!!t?.includes("centroidaggregate"),convexHull:!!t?.includes("convexhullaggregate")}}function d(e,t){const r=e?.supportedOperationsWithCacheHint?.map((e=>e.toLowerCase()));return!!r?.includes(t.toLowerCase())}function c(e,t){return{analytics:h(e),attachment:p(e),data:f(e),metadata:y(e),operations:_(e.capabilities,e,t),query:g(e,t),queryRelated:m(e),queryTopFeatures:w(e),editing:S(e)}}function h(e){return{supportsCacheHint:d(e.advancedQueryCapabilities,"queryAnalytics")}}function p(e){const t=e.attachmentProperties,r={supportsName:!1,supportsSize:!1,supportsContentType:!1,supportsKeywords:!1,supportsExifInfo:!1,supportsCacheHint:d(e.advancedQueryCapabilities,"queryAttachments"),supportsResize:l(e,"supportsAttachmentsResizing",!1)};return t&&Array.isArray(t)&&t.forEach((e=>{const t=a[e.name];t&&(r[t]=!!e.isEnabled)})),r}function f(e){return{isVersioned:l(e,"isDataVersioned",!1),supportsAttachment:l(e,"hasAttachments",!1),supportsM:l(e,"hasM",!1),supportsZ:l(e,"hasZ",!1)}}function y(e){return{supportsAdvancedFieldProperties:l(e,"supportsFieldDescriptionProperty",!1)}}function _(e,t,r){const s=e?e.toLowerCase().split(",").map((e=>e.trim())):[],a=r?(0,n.Qc)(r):null,o=s.includes((0,i.pC)(a)&&"MapServer"===a.serverType?"data":"query"),u=s.includes("editing")&&!t.datesInUnknownTimezone;let d=u&&s.includes("create"),c=u&&s.includes("delete"),h=u&&s.includes("update");const p=s.includes("changetracking"),f=t.advancedQueryCapabilities;return u&&!(d||c||h)&&(d=c=h=!0),{supportsCalculate:l(t,"supportsCalculate",!1),supportsTruncate:l(t,"supportsTruncate",!1),supportsValidateSql:l(t,"supportsValidateSql",!1),supportsAdd:d,supportsDelete:c,supportsEditing:u,supportsChangeTracking:p,supportsQuery:o,supportsQueryAnalytics:l(f,"supportsQueryAnalytic",!1),supportsQueryAttachments:l(f,"supportsQueryAttachments",!1),supportsQueryTopFeatures:l(f,"supportsTopFeaturesQuery",!1),supportsResizeAttachments:l(t,"supportsAttachmentsResizing",!1),supportsSync:s.includes("sync"),supportsUpdate:h,supportsExceedsLimitStatistics:l(t,"supportsExceedsLimitStatistics",!1)}}function g(e,t){const r=e.advancedQueryCapabilities,i=e.ownershipBasedAccessControlForFeatures,a=e.archivingInfo,c=e.currentVersion,h=t?.includes("MapServer"),p=!h||c>=(0,s.Z)("mapserver-pbf-version-support"),f=(0,n.M8)(t),y=new Set((e.supportedQueryFormats??"").split(",").map((e=>e.toLowerCase().trim())));return{supportsStatistics:l(r,"supportsStatistics",e.supportsStatistics),supportsPercentileStatistics:l(r,"supportsPercentileStatistics",!1),supportsSpatialAggregationStatistics:l(r,"supportsSpatialAggregationStatistics",!1),supportedSpatialAggregationStatistics:u(r),supportsCentroid:l(r,"supportsReturningGeometryCentroid",!1),supportsDistance:l(r,"supportsQueryWithDistance",!1),supportsDistinct:l(r,"supportsDistinct",e.supportsAdvancedQueries),supportsExtent:l(r,"supportsReturningQueryExtent",!1),supportsGeometryProperties:l(r,"supportsReturningGeometryProperties",!1),supportsHavingClause:l(r,"supportsHavingClause",!1),supportsOrderBy:l(r,"supportsOrderBy",e.supportsAdvancedQueries),supportsPagination:l(r,"supportsPagination",!1),supportsQuantization:l(e,"supportsCoordinatesQuantization",!1),supportsQuantizationEditMode:l(e,"supportsQuantizationEditMode",!1),supportsQueryGeometry:l(e,"supportsReturningQueryGeometry",!1),supportsResultType:l(r,"supportsQueryWithResultType",!1),supportsMaxRecordCountFactor:l(r,"supportsMaxRecordCountFactor",!1),supportsSqlExpression:l(r,"supportsSqlExpression",!1),supportsStandardizedQueriesOnly:l(e,"useStandardizedQueries",!1),supportsTopFeaturesQuery:l(r,"supportsTopFeaturesQuery",!1),supportsQueryByOthers:l(i,"allowOthersToQuery",!0),supportsHistoricMoment:l(a,"supportsQueryWithHistoricMoment",!1),supportsFormatPBF:p&&y.has("pbf"),supportsDisjointSpatialRelationship:l(r,"supportsDisjointSpatialRel",!1),supportsCacheHint:l(r,"supportsQueryWithCacheHint",!1)||d(r,"query"),supportsDefaultSpatialReference:l(r,"supportsDefaultSR",!1),supportsCompactGeometry:f,supportsFullTextSearch:l(r,"supportsFullTextSearch",!1),maxRecordCountFactor:o(e,"maxRecordCountFactor",void 0),maxRecordCount:o(e,"maxRecordCount",void 0),standardMaxRecordCount:o(e,"standardMaxRecordCount",void 0),tileMaxRecordCount:o(e,"tileMaxRecordCount",void 0)}}function m(e){const t=e.advancedQueryCapabilities,r=l(t,"supportsAdvancedQueryRelated",!1);return{supportsPagination:l(t,"supportsQueryRelatedPagination",!1),supportsCount:r,supportsOrderBy:r,supportsCacheHint:d(t,"queryRelated")}}function w(e){return{supportsCacheHint:d(e.advancedQueryCapabilities,"queryTopFilter")}}function S(e){const t=e.ownershipBasedAccessControlForFeatures;return{supportsGeometryUpdate:l(e,"allowGeometryUpdates",!0),supportsGlobalId:l(e,"supportsApplyEditsWithGlobalIds",!1),supportsReturnServiceEditsInSourceSpatialReference:l(e,"supportsReturnServiceEditsInSourceSR",!1),supportsRollbackOnFailure:l(e,"supportsRollbackOnFailureParameter",!1),supportsUpdateWithoutM:l(e,"allowUpdateWithoutMValues",!1),supportsUploadWithItemId:l(e,"supportsAttachmentsByUploadId",!1),supportsDeleteByAnonymous:l(t,"allowAnonymousToDelete",!0),supportsDeleteByOthers:l(t,"allowOthersToDelete",!0),supportsUpdateByAnonymous:l(t,"allowAnonymousToUpdate",!0),supportsUpdateByOthers:l(t,"allowOthersToUpdate",!0)}}},14661:(e,t,r)=>{r.d(t,{$o:()=>u,Kz:()=>d,Ss:()=>c,_$:()=>l,ck:()=>o,qj:()=>a});var s=r(44547),i=r(82971),n=r(40488);function a(e,t){if(!l(e,t)){const r=e.typeKeywords;r?r.push(t):e.typeKeywords=[t]}}function l(e,t){return!!e.typeKeywords?.includes(t)}function o(e,t){const r=e.typeKeywords;if(r){const e=r.indexOf(t);e>-1&&r.splice(e,1)}}async function u(e){const t=e.clone().normalize();let r;if(t.length>1)for(const e of t)r?e.width>r.width&&(r=e):r=e;else r=t[0];return async function(e){const t=e.spatialReference;if(t.isWGS84)return e.clone();if(t.isWebMercator)return(0,n.Sx)(e);const r=i.Z.WGS84;return await(0,s.iQ)(t,r),(0,s.iV)(e,r)}(r)}const d={DEVELOPER_BASEMAP:"DeveloperBasemap",JSAPI:"ArcGIS API for JavaScript",METADATA:"Metadata",MULTI_LAYER:"Multilayer",SINGLE_LAYER:"Singlelayer",TABLE:"Table"};function c(e){const{portal:t,isOrgItem:r,itemControl:s}=e,i=t.user?.privileges;let n=!i||i.includes("features:user:edit"),a=!!r&&!!i?.includes("features:user:fullEdit");const l="update"===s||"admin"===s;return l?a=n=!0:a&&(n=!0),{features:{edit:n,fullEdit:a},content:{updateItem:l}}}},41818:(e,t,r)=>{r.d(t,{P:()=>a});var s=r(11282),i=r(34599),n=r(14165);async function a(e,t,r){const a=(0,s.en)(e);return(0,i.hH)(a,n.Z.from(t),{...r}).then((e=>e.data.count))}},5396:(e,t,r)=>{r.d(t,{G:()=>a});var s=r(11282),i=r(34599),n=r(14165);async function a(e,t,r){const a=(0,s.en)(e);return(0,i.Ev)(a,n.Z.from(t),{...r}).then((e=>e.data.objectIds))}},4967:(e,t,r)=>{r.d(t,{F:()=>o,e:()=>l});var s=r(11282),i=r(34599),n=r(74889),a=r(14165);async function l(e,t,r){const s=await o(e,t,r);return n.Z.fromJSON(s)}async function o(e,t,r){const n=(0,s.en)(e),l={...r},o=a.Z.from(t),{data:u}=await(0,i.JT)(n,o,o.sourceSpatialReference,l);return u}},28694:(e,t,r)=>{r.d(t,{p:()=>n});var s=r(70586),i=r(69285);function n(e,t,r){if(!r||!r.features||!r.hasZ)return;const n=(0,i.k)(r.geometryType,t,e.outSpatialReference);if(!(0,s.Wi)(n))for(const e of r.features)n(e.geometry)}},98326:(e,t,r)=>{r.d(t,{Z:()=>c});var s,i=r(43697),n=r(96674),a=r(5600),l=r(75215),o=(r(67676),r(52011));const u={1:{id:1,rotation:0,mirrored:!1},2:{id:2,rotation:0,mirrored:!0},3:{id:3,rotation:180,mirrored:!1},4:{id:4,rotation:180,mirrored:!0},5:{id:5,rotation:-90,mirrored:!0},6:{id:6,rotation:90,mirrored:!1},7:{id:7,rotation:90,mirrored:!0},8:{id:8,rotation:-90,mirrored:!1}};let d=s=class extends n.wq{constructor(e){super(e),this.contentType=null,this.exifInfo=null,this.id=null,this.globalId=null,this.keywords=null,this.name=null,this.parentGlobalId=null,this.parentObjectId=null,this.size=null,this.url=null}get orientationInfo(){const{exifInfo:e}=this,t=function(e){const{exifInfo:t,exifName:r,tagName:s}=e;if(!t||!r||!s)return null;const i=t.find((e=>e.name===r));return i?function(e){const{tagName:t,tags:r}=e;if(!r||!t)return null;const s=r.find((e=>e.name===t));return s&&s.value||null}({tagName:s,tags:i.tags}):null}({exifName:"Exif IFD0",tagName:"Orientation",exifInfo:e});return u[t]||null}clone(){return new s({contentType:this.contentType,exifInfo:this.exifInfo,id:this.id,globalId:this.globalId,keywords:this.keywords,name:this.name,parentGlobalId:this.parentGlobalId,parentObjectId:this.parentObjectId,size:this.size,url:this.url})}};(0,i._)([(0,a.Cb)({type:String})],d.prototype,"contentType",void 0),(0,i._)([(0,a.Cb)()],d.prototype,"exifInfo",void 0),(0,i._)([(0,a.Cb)({readOnly:!0})],d.prototype,"orientationInfo",null),(0,i._)([(0,a.Cb)({type:l.z8})],d.prototype,"id",void 0),(0,i._)([(0,a.Cb)({type:String})],d.prototype,"globalId",void 0),(0,i._)([(0,a.Cb)({type:String})],d.prototype,"keywords",void 0),(0,i._)([(0,a.Cb)({type:String})],d.prototype,"name",void 0),(0,i._)([(0,a.Cb)({json:{read:!1}})],d.prototype,"parentGlobalId",void 0),(0,i._)([(0,a.Cb)({json:{read:!1}})],d.prototype,"parentObjectId",void 0),(0,i._)([(0,a.Cb)({type:l.z8})],d.prototype,"size",void 0),(0,i._)([(0,a.Cb)({json:{read:!1}})],d.prototype,"url",void 0),d=s=(0,i._)([(0,o.j)("esri.layers.support.AttachmentInfo")],d);const c=d},56545:(e,t,r)=>{r.d(t,{Z:()=>h});var s,i=r(43697),n=r(96674),a=r(22974),l=r(5600),o=r(75215),u=r(52011),d=r(30556);let c=s=class extends n.wq{constructor(e){super(e),this.attachmentTypes=null,this.attachmentsWhere=null,this.cacheHint=void 0,this.keywords=null,this.globalIds=null,this.name=null,this.num=null,this.objectIds=null,this.returnMetadata=!1,this.size=null,this.start=null,this.where=null}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10}clone(){return new s((0,a.d9)({attachmentTypes:this.attachmentTypes,attachmentsWhere:this.attachmentsWhere,cacheHint:this.cacheHint,keywords:this.keywords,where:this.where,globalIds:this.globalIds,name:this.name,num:this.num,objectIds:this.objectIds,returnMetadata:this.returnMetadata,size:this.size,start:this.start}))}};(0,i._)([(0,l.Cb)({type:[String],json:{write:!0}})],c.prototype,"attachmentTypes",void 0),(0,i._)([(0,l.Cb)({type:String,json:{read:{source:"attachmentsDefinitionExpression"},write:{target:"attachmentsDefinitionExpression"}}})],c.prototype,"attachmentsWhere",void 0),(0,i._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],c.prototype,"cacheHint",void 0),(0,i._)([(0,l.Cb)({type:[String],json:{write:!0}})],c.prototype,"keywords",void 0),(0,i._)([(0,l.Cb)({type:[Number],json:{write:!0}})],c.prototype,"globalIds",void 0),(0,i._)([(0,l.Cb)({json:{write:!0}})],c.prototype,"name",void 0),(0,i._)([(0,l.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],c.prototype,"num",void 0),(0,i._)([(0,l.Cb)({type:[Number],json:{write:!0}})],c.prototype,"objectIds",void 0),(0,i._)([(0,l.Cb)({type:Boolean,json:{default:!1,write:!0}})],c.prototype,"returnMetadata",void 0),(0,i._)([(0,l.Cb)({type:[Number],json:{write:!0}})],c.prototype,"size",void 0),(0,i._)([(0,l.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],c.prototype,"start",void 0),(0,i._)([(0,d.c)("start"),(0,d.c)("num")],c.prototype,"writeStart",null),(0,i._)([(0,l.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],c.prototype,"where",void 0),c=s=(0,i._)([(0,u.j)("esri.rest.support.AttachmentQuery")],c),c.from=(0,o.se)(c);const h=c},74889:(e,t,r)=>{r.d(t,{Z:()=>S});var s,i=r(43697),n=r(66577),a=r(38171),l=r(35454),o=r(96674),u=r(22974),d=r(70586),c=r(5600),h=(r(75215),r(71715)),p=r(52011),f=r(30556),y=r(82971),_=r(33955),g=r(1231);const m=new l.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let w=s=class extends o.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const r=y.Z.fromJSON(t.spatialReference),s=[];for(let t=0;t<e.length;t++){const i=e[t],n=a.Z.fromJSON(i),l=i.geometry&&i.geometry.spatialReference;(0,d.pC)(n.geometry)&&!l&&(n.geometry.spatialReference=r);const o=i.aggregateGeometries,u=n.aggregateGeometries;if(o&&(0,d.pC)(u))for(const e in u){const t=u[e],s=o[e]?.spatialReference;(0,d.pC)(t)&&!s&&(t.spatialReference=r)}s.push(n)}return s}writeGeometryType(e,t,r,s){if(e)return void m.write(e,t,r,s);const{features:i}=this;if(i)for(const e of i)if(e&&(0,d.pC)(e.geometry))return void m.write(e.geometry.type,t,r,s)}readQueryGeometry(e,t){if(!e)return null;const r=!!e.spatialReference,s=(0,_.im)(e);return s&&!r&&t.spatialReference&&(s.spatialReference=y.Z.fromJSON(t.spatialReference)),s}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:r}=this;if(r)for(const e of r)if(e&&(0,d.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new s(this.cloneProperties())}cloneProperties(){return(0,u.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let r=0;r<t.features.length;r++){const s=t.features[r];if(s.geometry){const t=e&&e[r];s.geometry=t&&t.toJSON()||s.geometry}}return t}quantize(e){const{scale:[t,r],translate:[s,i]}=e,n=this.features,a=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-s)/t)),(e=>Math.round((i-e)/r)));for(let e=0,t=n.length;e<t;e++)a?.((0,d.Wg)(n[e].geometry))||(n.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:r}=this;if(!r)return this;const{translate:[s,i],scale:[n,a]}=r,l=this._getHydrationFunction(e,(e=>e*n+s),(e=>i-e*a));for(const{geometry:e}of t)(0,d.pC)(e)&&l&&l(e);return this.transform=null,this}_quantizePoints(e,t,r){let s,i;const n=[];for(let a=0,l=e.length;a<l;a++){const l=e[a];if(a>0){const e=t(l[0]),a=r(l[1]);e===s&&a===i||(n.push([e-s,a-i]),s=e,i=a)}else s=t(l[0]),i=r(l[1]),n.push([s,i])}return n.length>0?n:null}_getQuantizationFunction(e,t,r){return"point"===e?e=>(e.x=t(e.x),e.y=r(e.y),e):"polyline"===e||"polygon"===e?e=>{const s=(0,_.oU)(e)?e.rings:e.paths,i=[];for(let e=0,n=s.length;e<n;e++){const n=s[e],a=this._quantizePoints(n,t,r);a&&i.push(a)}return i.length>0?((0,_.oU)(e)?e.rings=i:e.paths=i,e):null}:"multipoint"===e?e=>{const s=this._quantizePoints(e.points,t,r);return s&&s.length>0?(e.points=s,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,r){return"point"===e?e=>{e.x=t(e.x),e.y=r(e.y)}:"polyline"===e||"polygon"===e?e=>{const s=(0,_.oU)(e)?e.rings:e.paths;let i,n;for(let e=0,a=s.length;e<a;e++){const a=s[e];for(let e=0,s=a.length;e<s;e++){const s=a[e];e>0?(i+=s[0],n+=s[1]):(i=s[0],n=s[1]),s[0]=t(i),s[1]=r(n)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=r(e.ymin),e.xmax=t(e.xmax),e.ymax=r(e.ymax)}:"multipoint"===e?e=>{const s=e.points;let i,n;for(let e=0,a=s.length;e<a;e++){const a=s[e];e>0?(i+=a[0],n+=a[1]):(i=a[0],n=a[1]),a[0]=t(i),a[1]=r(n)}}:null}};(0,i._)([(0,c.Cb)({type:String,json:{write:!0}})],w.prototype,"displayFieldName",void 0),(0,i._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],w.prototype,"exceededTransferLimit",void 0),(0,i._)([(0,c.Cb)({type:[a.Z],json:{write:!0}})],w.prototype,"features",void 0),(0,i._)([(0,h.r)("features")],w.prototype,"readFeatures",null),(0,i._)([(0,c.Cb)({type:[g.Z],json:{write:!0}})],w.prototype,"fields",void 0),(0,i._)([(0,c.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:m.read}}})],w.prototype,"geometryType",void 0),(0,i._)([(0,f.c)("geometryType")],w.prototype,"writeGeometryType",null),(0,i._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],w.prototype,"hasM",void 0),(0,i._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],w.prototype,"hasZ",void 0),(0,i._)([(0,c.Cb)({types:n.qM,json:{write:!0}})],w.prototype,"queryGeometry",void 0),(0,i._)([(0,h.r)("queryGeometry")],w.prototype,"readQueryGeometry",null),(0,i._)([(0,c.Cb)({type:y.Z,json:{write:!0}})],w.prototype,"spatialReference",void 0),(0,i._)([(0,f.c)("spatialReference")],w.prototype,"writeSpatialReference",null),(0,i._)([(0,c.Cb)({json:{write:!0}})],w.prototype,"transform",void 0),w=s=(0,i._)([(0,p.j)("esri.rest.support.FeatureSet")],w),w.prototype.toJSON.isDefaultToJSON=!0;const S=w},58333:(e,t,r)=>{r.d(t,{ET:()=>n,I4:()=>i,eG:()=>o,lF:()=>a,lj:()=>d,qP:()=>l,wW:()=>u});const s=[252,146,31,255],i={type:"esriSMS",style:"esriSMSCircle",size:6,color:s,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},n={type:"esriSLS",style:"esriSLSSolid",width:.75,color:s},a={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},l={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},o={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},u={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},d={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);