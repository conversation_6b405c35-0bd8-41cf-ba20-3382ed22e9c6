package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionArchive;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoConstructionArchiveSaveRequest extends SaveRequest<SoConstructionArchive> {
    // 所属工程编号
    @NotNullOrEmpty
    private String constructionCode;

    // 备注
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    protected SoConstructionArchive build() {
        SoConstructionArchive entity = new SoConstructionArchive();
        entity.setConstructionCode(constructionCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionArchive update(String id) {
        SoConstructionArchive entity = new SoConstructionArchive();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionArchive entity) {
        entity.setRemark(remark);
        entity.setAttachments(attachments);
        entity.setUpdateUser(currentUserUUID());
        entity.setUpdateTime(createTime());
    }
}