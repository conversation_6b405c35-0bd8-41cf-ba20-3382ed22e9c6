<template>
  <div class="hgl-wrapper">
    <img
      src="../imgs/smartproduce_item_left_bottom.png"
      alt=""
    />
    <div class="left-value">
      {{ state.left }} %
    </div>
    <div class="right-value">
      {{ state.right }} %
    </div>
  </div>
</template>
<script lang="ts" setup>
const state = reactive<{
  left: number
  right: number
}>({
  left: 100,
  right: 100
})
</script>
<style lang="scss" scoped>
.hgl-wrapper {
  border-radius: 8px;
  width: 100%;
  height: 100%;
  img {
    width: 100%;
    height: 100%;
  }
  .left-value {
    position: absolute;
    left: 35%;
    transform: translateX(-50%) translateY(-50%);
    top: 55%;
  }
  .right-value {
    position: absolute;
    right: 14%;
    transform: translateX(50%) translateY(-50%);
    top: 55%;
  }
}
</style>
