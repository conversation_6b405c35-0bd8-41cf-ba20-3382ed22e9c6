/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import com.datastax.driver.core.utils.UUIDs;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.alarm.Alarm;
import org.thingsboard.server.common.data.alarm.AlarmId;
import org.thingsboard.server.common.data.dataMonitor.DataMonitor;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EntityIdFactory;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.BaseEntity;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.SearchTextEntity;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

import static org.thingsboard.server.dao.model.ModelConstants.*;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = DATA_MONITOR)
public final class DataMonitorEntity extends BaseSqlEntity<DataMonitor> implements SearchTextEntity<DataMonitor> {

    @Column(name = MENU_TENANT_TENANT_ID)
    private String tenantId;
    @Column(name = DEVICE_ID_PROPERTY)
    private String deviceId;
    @Column(name = ALARM_CREATE_TIME)
    private Long time;
    @Column(name = ENERGY_ATTRIBUTE)
    private String attribute;
    @Column(name = VALUE_COLUMN)
    private String value;
    @Column(name = EVENT_BODY_PROPERTY)
    private String body;


    public DataMonitorEntity() {
        super();
    }

    public DataMonitorEntity(DataMonitor dataMonitor) {
        this.tenantId = UUIDConverter.fromTimeUUID(dataMonitor.getTenantId().getId());
        this.deviceId = UUIDConverter.fromTimeUUID(dataMonitor.getDeviceId().getId());
        this.time = dataMonitor.getTime();
        this.attribute = dataMonitor.getAttribute();
        this.value = dataMonitor.getValue();
        this.body = dataMonitor.getBody();
    }

    @Override
    public DataMonitor toData() {
        DataMonitor dataMonitor = new DataMonitor();
        dataMonitor.setTenantId(new TenantId(UUIDConverter.fromString(this.tenantId)));
        dataMonitor.setDeviceId(new DeviceId(UUIDConverter.fromString(this.deviceId)));
        dataMonitor.setBody(this.body);
        dataMonitor.setTime(this.time);
        dataMonitor.setAttribute(this.attribute);
        dataMonitor.setValue(this.value);
        return dataMonitor;
    }

    @Override
    public String getSearchTextSource() {
        return attribute;
    }

    @Override
    public void setSearchText(String searchText) {
           attribute=searchText;
    }
}