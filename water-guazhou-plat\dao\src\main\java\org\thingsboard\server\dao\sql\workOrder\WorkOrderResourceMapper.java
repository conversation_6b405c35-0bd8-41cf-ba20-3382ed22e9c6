package org.thingsboard.server.dao.sql.workOrder;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderResource;

@Mapper
public interface WorkOrderResourceMapper extends BaseMapper<WorkOrderResource> {
    IPage<WorkOrderResource> findList(IPage<WorkOrderResource> pageRequest, @Param("status") String status, @Param("tenantId") String tenantId);
}
