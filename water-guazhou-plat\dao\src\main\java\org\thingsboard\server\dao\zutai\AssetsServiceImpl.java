package org.thingsboard.server.dao.zutai;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.zutai.AssetsEntity;
import org.thingsboard.server.dao.sql.zutai.AssetsRepository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
@Service
public class AssetsServiceImpl implements AssetsService {
    @Autowired
    private AssetsRepository assetsRepository;

    @Override
    public List getListById(Map params) {
        String id = (String) params.get("id");
        String projectId = (String) params.get("projectId");
        String tenantId = (String) params.get("tenantId");

        if (StringUtils.isBlank(id)) {
            id = "";
        }
        id = "%" + id + "%";
        if (StringUtils.isBlank(projectId)) {
            projectId = "";
        }
        projectId = "%" + projectId + "%";

        return assetsRepository.findAllByIdLikeAndProjectIdLikeAndTenantIdOrderByCreateTimeDesc(id, projectId, tenantId);
    }

    @Override
    public AssetsEntity save(AssetsEntity assetsEntity) {
        // 是否新增
        if (StringUtils.isBlank(assetsEntity.getId())) {
            assetsEntity.setCreateTime(new Date());
        }
        // 项目id判断
        if (StringUtils.isBlank(assetsEntity.getProjectId())) {
            assetsEntity.setProjectId("");
        }
        return assetsRepository.save(assetsEntity);
    }

    @Override
    public void delete(String id) {
        assetsRepository.delete(id);
    }
}
