import BaseTileLayer from '@arcgis/core/layers/BaseTileLayer.js';
import esriRequest from '@arcgis/core/request.js';

class TintLayer extends BaseTileLayer {
  constructor(properties: {
    urlTemplate: string;
    subDomains?: string[];
    color?: any;
    filter?: string;
  }) {
    super();
    this.urlTemplate = properties.urlTemplate;
    this.color = properties.color;
    this.filter = properties.filter;
    properties.subDomains && (this.subDomains = properties.subDomains);
  }

  subDomains: string[] = ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'];

  urlTemplate: string;

  color: any;

  filter: any;

  getTileUrl(level: number, row: number, col: number): string {
    const length = this.subDomains.length;
    const idx = col % length;
    return (
      this.urlTemplate
        .replace('{level}', level.toString())
        .replace('{col}', col.toString())
        .replace('{row}', row.toString())
        .replace('{subDomain}', this.subDomains[idx]) +
      '&color=' +
      (this.color || '')
    );
  }

  static generateTemplate(
    type = 'vec_w',
    urlType: 'DataServer' | 'wmts' = 'wmts'
  ) {
    const mapType = type.split('_');
    return urlType === 'DataServer'
      ? `http://{subDomain}.tianditu.com/DataServer?T=${type}&x={col}&y={row}&l={level}&tk=${window.SITE_CONFIG.GIS_CONFIG.gisTdtToken}`
      : `http://{subDomain}.tianditu.gov.cn/${type}/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=${mapType[0]}&STYLE=default&TILEMATRIXSET=${mapType[1]}&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=${window.SITE_CONFIG.GIS_CONFIG.gisTdtToken}`;
  }

  fetchTile(
    level: number,
    row: number,
    col: number,
    options?: __esri.BaseTileLayerFetchTileOptions | undefined
  ): Promise<HTMLCanvasElement | HTMLImageElement> {
    // call getTileUrl() method to construct the URL to tiles
    // for a given level, row and col provided by the LayerView
    const url = this.getTileUrl(level, row, col);
    // 此行用于处理天地图级别大于18时无内容的情况，reject以保持之前渲染的图形不被刷掉
    if (level >= 19) return Promise.reject();

    // request for tiles based on the generated url
    // the signal option ensures that obsolete requests are aborted
    return esriRequest(url, {
      responseType: 'image',
      signal: options && options.signal
    }).then((response) => {
      // when esri request resolves successfully
      // get the image from the response
      const image = response.data;
      const width = this.tileInfo.size[0];
      const height = this.tileInfo.size[0];

      // create a canvas with 2D rendering context
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d') as CanvasRenderingContext2D;
      canvas.width = width;
      canvas.height = height;

      // Apply the tint color provided by
      // by the application to the canvas
      // if (this.color) {
      //   context.fillStyle = this.color;
      // }
      // if (this.filter) {
      //   // Get a CSS color string in rgba form
      //   // representing the tint Color instance.
      //   // debugger
      //   context.filter = this.filter;
      //   //  || 'grayscale(0%) invert(100%) opacity(100%)';
      // }
      context.fillStyle = 'rgba(255,255,255,0.0)';
      context.filter = this.filter || '';
      // context.filter = 'grayscale(0%) invert(100%) opacity(100%)';
      // context.filter =
      //   "url('#sea') contrast(1.6) grayscale(0.18) saturate(0.8)";
      context.fillRect(0, 0, width, height);

      // Applies "difference" blending operation between canvas
      // and steman tiles. Difference blending operation subtracts
      // the bottom layer (canvas) from the top layer (tiles) or the
      // other way round to always get a positive value.
      context.globalCompositeOperation = 'difference';

      // Draw the blended image onto the canvas.
      context.drawImage(image, 0, 0, width, height);

      return canvas;
    });
  }
}
export default TintLayer;
