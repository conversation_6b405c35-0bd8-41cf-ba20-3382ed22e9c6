/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.alarm.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.utils.Base64Util;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/noAuthAlarm")
public class NoAuthAlarmController extends BaseController {


    @RequestMapping(value = "/getAlarm", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarm(
            @RequestBody JsonNode jsonNode
    ) throws Exception {
        User user = convertUser(jsonNode.get(DataConstants.REQUEST_PARAM_SECRET).asText());
        List<DeviceId> deviceIds = new ArrayList<>();
        Iterator<JsonNode> jsonNodeIterator = jsonNode.get("deviceId").elements();
        jsonNodeIterator.forEachRemaining(jsonNode1 -> {
            deviceIds.add(new DeviceId(UUIDConverter.fromString(jsonNode1.asText())));
        });
        String type = jsonNode.get("type") == null ? null : jsonNode.get("type").asText();
        String level = jsonNode.get("level") == null ? null : jsonNode.get("level").asText();
        String status = jsonNode.get("status") == null ? null : jsonNode.get("status").asText();
        long start = jsonNode.get("start") == null ? 0 : jsonNode.get("start").asLong();
        long end = jsonNode.get("end") == null ? 0 : jsonNode.get("end").asLong();
        return alarmService.findClearAlarmByTenantAndLevel(user.getTenantId(), deviceIds, type, level, status, start, end)
                .stream().filter(f -> f.getStatus() == AlarmStatus.CLEAR_FORCED || f.getStatus() == AlarmStatus.CLEARED_ACK).collect(Collectors.toList());
    }

    /**
     * 强行解除报警
     *
     * @param jsonNode
     * @return
     */
    @RequestMapping(value = "/clear", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ALARM_CLEAR)
    @ResponseBody
    public List<Alarm> clearAlarm(@RequestBody JsonNode jsonNode) throws Exception {
        checkNotNull(jsonNode);
        User user = convertUser(jsonNode.get(DataConstants.REQUEST_PARAM_SECRET).asText());
        Iterator<JsonNode> iterator = jsonNode.get("alarmId").elements();
        List<Alarm> alarms = new ArrayList<>();
        //先找出待处理的报警信息
        iterator.forEachRemaining(alarm -> {
            try {
                alarms.add(alarmService.findAlarmByIdAsync(new AlarmId(UUIDConverter.fromString(alarm.asText()))).get());
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        });
        List<Alarm> result = new ArrayList<>();
        //处理报警信息
        alarms.forEach(alarm -> {
            alarm.setClearTs(new Date().getTime());
            alarm.setStatus(AlarmStatus.CLEAR_FORCED);
            JsonNode json = alarm.getDetails();
            if (json == null) {
                AlarmType ala = new AlarmType("clear");
                json = JacksonUtil.toJsonNode(JacksonUtil.toString(ala));
            }
            ObjectNode objectNode = (ObjectNode) json;
            objectNode.put("dismissal", user.getName());
            objectNode.put("clearRemarks", jsonNode.get("remark") != null ? jsonNode.get("remark").asText() : null);
            alarm.setDetails(objectNode);
            result.add(alarmService.createOrUpdateAlarm(alarm));
        });
        return result;
    }


    /**
     * 确认报警
     *
     * @param jsonNode
     * @return
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ALARM_ACK)
    @ResponseBody
    public List<Alarm> confirAlarm(@RequestBody JsonNode jsonNode) throws Exception {
        checkNotNull(jsonNode);
        User user = convertUser(jsonNode.get(DataConstants.REQUEST_PARAM_SECRET).asText());
        Iterator<JsonNode> iterator = jsonNode.get("alarmId").elements();
        List<Alarm> alarms = new ArrayList<>();
        //先找出待处理的报警信息
        iterator.forEachRemaining(alarm -> {
            try {
                alarms.add(alarmService.findAlarmByIdAsync(new AlarmId(UUIDConverter.fromString(alarm.asText()))).get());
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        });
        List<Alarm> result = new ArrayList<>();
        //处理报警信息
        alarms.forEach(alarm -> {
            if (alarm.getStatus() == AlarmStatus.RESTORE_ACK) {
                alarm.setStatus(AlarmStatus.CLEARED_ACK);
                alarm.setClearTs(new Date().getTime());
            } else
                alarm.setStatus(AlarmStatus.CONFIRM_ACK);
            JsonNode json = alarm.getDetails();
            if (json == null) {
                AlarmType ala = new AlarmType("clear");
                json = JacksonUtil.toJsonNode(JacksonUtil.toString(ala));
            }
            ObjectNode objectNode = (ObjectNode) json;
            objectNode.put("confirm", user.getName());
            objectNode.put("confirmRemarks", jsonNode.get("remark") != null ? jsonNode.get("remark").asText() : null);
            alarm.setDetails(objectNode);
            result.add(alarmService.createOrUpdateAlarm(alarm));
        });
        return result;
    }


    /**
     * 根据tenantID获取历史数据
     *
     * @param start
     * @param end
     * @return
     * @throws ThingsboardException
     */
    @RequestMapping(value = "/getAlarmHistory", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmHistory(
            @RequestParam(required = false) long start,
            @RequestParam(required = false) long end,
            @RequestParam(required = false) String secret
    ) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        User user = convertUser(secret);
        return alarmService.findHistoryAlarm(user.getTenantId(), start, end).get();
    }


    /**
     * 根据TENANTID获取实时报警
     */
    @RequestMapping(value = "/getAlarmRealTime", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmRealTime(
            @RequestParam(required = false) long start,
            @RequestParam(required = false) long end,
            @RequestParam(required = false) String secret
    ) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        User user = convertUser(secret);
        return alarmService.findRealTimeAlarm(user.getTenantId(), start, end).get();
    }


    /**
     * 根据报警类别查看该设备历史报警
     */
    @RequestMapping(value = "/getAlarmHistoryByDevice", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmFromDevice(
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false) String alarmType,
            @RequestParam(required = false) String secret
    ) throws Exception {
        checkNotNull(deviceId);
        checkNotNull(alarmType);
        checkNotNull(secret);
        return alarmService.findHistoryAlarmByDevice(new DeviceId(UUIDConverter.fromString(deviceId)), alarmType).get();
    }

    /**
     * 根据秘钥返回用户
     *
     * @param userSecret
     * @return
     * @throws Exception
     */
    private User convertUser(String userSecret) throws Exception {
        String userId = Base64Util.decodeData(userSecret).split("\\.")[0];
        return userService.findUserById(new UserId(UUIDConverter.fromString(userId)));

    }


    @RequestMapping(value = "/alarmJson", method = RequestMethod.GET)
    @ResponseBody
    public List<AttrAlarmJson> getAlarmByTenantId(@RequestParam(required = false) String secret) throws Exception {
        User user = convertUser(secret);
        List<AttrAlarmJson> alarmJsons = new ArrayList<>();
        try {
            alarmJsons = alarmJsonService.findByTenant(user.getTenantId()).get();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return alarmJsons;
    }

    /**
     * 根据秘钥查询设备列表
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/getDevice", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Device> getAlarmByDevice(@RequestParam(required = false) String secret) throws Exception {
        checkNotNull(secret);
        User user = convertUser(secret);
        return deviceService.findAllByTenantId(user.getTenantId()).stream()
                .filter(d -> d.getIsDelete().equals(DataConstants.IS_DELETE_NO) && !d.getType().equals(DataConstants.GATEWAY_NAME))
                .collect(Collectors.toList());
    }

}
