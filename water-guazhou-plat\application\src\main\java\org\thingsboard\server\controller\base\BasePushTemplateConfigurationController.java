package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBasePushTemplateConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BasePushTemplateConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BasePushTemplateConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 平台管理-推送模板配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Api(tags = "平台管理-推送模板配置")
@RestController
@RequestMapping("api/base/push/template/configuration")
public class BasePushTemplateConfigurationController extends BaseController {

    @Autowired
    private IBasePushTemplateConfigurationService basePushTemplateConfigurationService;

    /**
     * 查询平台管理-推送模板配置列表
     */
    @MonitorPerformance(description = "平台管理-推送模板配置列表查询接口")
    @ApiOperation(value = "查询推送模板配置表")
    @GetMapping("/list")
    public IstarResponse list(BasePushTemplateConfigurationPageRequest basePushTemplateConfiguration) {
        return IstarResponse.ok(basePushTemplateConfigurationService.selectBasePushTemplateConfigurationList(basePushTemplateConfiguration));
    }

    /**
     * 获取平台管理-推送模板配置详细信息
     */
    @MonitorPerformance(description = "平台管理-推送模板配置详细信息接口")
    @ApiOperation(value = "获取推送模板配置表详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(basePushTemplateConfigurationService.selectBasePushTemplateConfigurationById(id));
    }

    /**
     * 新增平台管理-推送模板配置
     */
    @MonitorPerformance(description = "平台管理-新增推送模板配置接口")
    @ApiOperation(value = "新增推送模板配置表")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BasePushTemplateConfiguration basePushTemplateConfiguration) {
        return IstarResponse.ok(basePushTemplateConfigurationService.insertBasePushTemplateConfiguration(basePushTemplateConfiguration));
    }

    /**
     * 修改平台管理-推送模板配置
     */
    @MonitorPerformance(description = "平台管理-修改推送模板配置接口")
    @ApiOperation(value = "修改推送模板配置表")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BasePushTemplateConfiguration basePushTemplateConfiguration) {
        return IstarResponse.ok(basePushTemplateConfigurationService.updateBasePushTemplateConfiguration(basePushTemplateConfiguration));
    }

    /**
     * 删除平台管理-推送模板配置
     */
    @MonitorPerformance(description = "平台管理-删除推送模板配置接口")
    @ApiOperation(value = "删除推送模板配置表")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody  List<String> ids) {
        return IstarResponse.ok(basePushTemplateConfigurationService.deleteBasePushTemplateConfigurationByIds(ids));
    }
}
