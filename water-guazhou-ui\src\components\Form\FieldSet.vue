<template>
  <div
    class="fieldset"
    :class="[type, size]"
  >
    <div class="fieldset-title">
      <slot>
        <span class="title">
          <Icon
            v-if="icon"
            :icon="icon"
          ></Icon>
          <span> {{ title }}</span>
        </span>
        <div
          v-if="titleRight?.length"
          class="right-wrapper"
        >
          <template
            v-for="(rightitem, k) in titleRight"
            :key="k"
          >
            <div :class="rightitem.className">
              <template
                v-for="(obj, z) in rightitem.items"
                :key="z"
              >
                <FormItem
                  v-if="obj.field"
                  v-model="state.titleQueryParams[obj.field]"
                  :config="obj"
                  @change="(val) => obj.onChange && obj.onChange(val,obj)"
                />
                <FormItem
                  v-else
                  :config="obj"
                />
              </template>
            </div>
          </template>
        </div>
      </slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { reactive } from 'vue'

const props = withDefaults(
  defineProps<{
    title?: string
    icon?: string
    titleRight?: {
      className?: string
      items: IFormItem[]
    }[]
    titleQueryParams?: Record<string, any>
    type?: 'simple' | 'default' | 'underline'
    size?: ISize
  }>(),
  {
    title: '',
    titleIcon: '',
    type: 'default',
    titleRight: undefined,
    titleQueryParams: () => {
      return {}
    },
    size: 'default'
  }
)
const state = reactive<{
  titleQueryParams: Record<string, any>
}>({
  titleQueryParams: { ...(props.titleQueryParams || {}) }
})
</script>
<style lang="scss" scoped>
.fieldset {
  margin: 8px 0;
  height: var(--el-component-size);
  border-bottom: 1px solid var(--el-border-color);
  &.large {
    height: var(--el-component-size-large);
    .fieldset-title {
      height: var(--el-component-size);
      font-size: var(--el-font-size-large);
    }
  }
  &.default {
    height: var(--el-component-size);
    .fieldset-title {
      height: var(--el-component-size-small);
      font-size: var(--el-font-size-base);
    }
  }
  &.small {
    height: var(--el-component-size-small);
    .fieldset-title {
      height: 20px;
      font-size: var(--el-font-size-small);
    }
  }
  .fieldset-title {
    height: 25px;
    line-height: 25px;
    position: relative;
    padding-left: 15px;
    // color: #3d95ec;
    color: #000000;
    font-size: 14px;
    display: flex;
    align-items: center;
    &::before {
      content: ' ';
      position: absolute;
      height: 100%;
      width: 4px;
      background: linear-gradient(to bottom, #3d95ec, #6bb6ff);
      top: 0;
      left: 0;
      border-radius: 0 3px 3px 0;
    }
  }
  .title {
    margin-right: auto;
    word-break: keep-all;
    display: flex;
    align-items: center;
    margin-right: 8px;
  }
  &.simple {
    display: flex;
    align-items: center;
    border-bottom: none;

    .fieldset-title {
      height: 1em;
      line-height: 1em;
      padding-left: 10px;
      &::before {
        width: 3px;
        background: linear-gradient(to bottom, #3d95ec, #6bb6ff);
        border-radius: 0 2px 2px 0;
      }
    }
  }
  &.underline {
    .fieldset-title {
      padding-left: 0;
      &::before {
        display: none;
      }
    }
  }
}
</style>
