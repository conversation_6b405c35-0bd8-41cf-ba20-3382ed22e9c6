package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContract;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractContainer;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoSimpleConstructionContract;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypeSaveRequest;

import java.util.List;

public interface SoConstructionContractService {
    /**
     * 分页条件查询工程合同
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoConstructionContractContainer> findAllConditional(SoConstructionContractPageRequest request);

    /**
     * 保存工程合同
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoConstructionContract save(SoConstructionContractSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoConstructionContract entity);

    /**
     * 编号是否已存在
     *
     * @param code     编号
     * @param tenantId 客户id
     * @param id       自身id（更新时不为null）
     * @return 是否已存在
     */
    boolean isCodeExists(String code, String tenantId, String id);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 完成
     *
     * @param constructionCode 工程编号
     * @param userId           请求发起人id
     * @param tenantId         客户id
     * @return 是否成功
     */
    boolean complete(String constructionCode, String userId, String tenantId);

    /**
     * 分页条件查询设备项
     *
     * @param request 分页请求
     * @return 设备项
     */
    IPage<SoDeviceItem> getDevices(SoDeviceItemPageRequest request);

    /**
     * 保存设备项
     *
     * @param request 明细
     * @return 保存好的设备项
     */
    List<SoDeviceItem> saveDevice(List<SoDeviceItemSaveRequest> request);

    /**
     * 是否已完成
     *
     * @param id 唯一标识
     * @return 是否已完成
     */
    boolean isComplete(String id);

    /**
     * 是否已完成
     *
     * @param constructionCode 编号
     * @param tenantId         客户id
     * @return 是否已完成
     */
    boolean isComplete(String constructionCode, String tenantId);

    /**
     * 分页条件查询类型
     *
     * @param request 分页请求
     * @return 类型分页
     */
    IPage<SoGeneralType> getTypes(SoGeneralTypePageRequest request);

    /**
     * 保存类型
     *
     * @param request 明细
     * @return 保存好的数据
     */
    SoGeneralType saveType(SoGeneralTypeSaveRequest request);

    /**
     * 获取简单合同信息
     * @param request 请求
     * @return 合同信息
     */
    IPage<SoSimpleConstructionContract> findSimple(SoConstructionContractPageRequest request);

    /**
     * 通过合同编号判断是否完成
     * @param code 合同编号
     * @param tenantId 客户id
     * @return 是否完成
     */
    boolean isCompleteByContractCode(String code, String tenantId);

}
