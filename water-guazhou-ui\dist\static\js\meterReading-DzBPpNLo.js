import{d as h,c as i,r as l,o as b,g as k,n as v,q as o,i as t,t as y,p as C,_ as x,aq as F,C as R}from"./index-r0dFAfgr.js";import{C as V}from"./index-CcDafpIP.js";import{r as B}from"./chart-wy3NEK2T.js";const M={class:"onemap-panel-wrapper"},W={class:"table-box"},q=h({__name:"meterReading",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(r,{emit:p}){const d=p,c=r,m=i(),n=i([{label:"0 个",value:"抄表员"},{label:"0 个",value:"用户数"}]),a=l({dataList:[],pagination:{hide:!0},columns:[{minWidth:120,label:"抄表员",prop:"key1",sortable:!0},{minWidth:120,label:"用户数",prop:"key3"},{minWidth:120,label:"已抄表",prop:"key4"}],handleRowClick:e=>{a.currentRow=e,d("highlightMark",c.menu,e==null?void 0:e.id)}}),u=l({group:[{fieldset:{type:"underline",desc:"用水性质占比"},fields:[{type:"vchart",option:B(),style:{width:"100%",height:"150px"},itemContainerStyle:{marginBottom:0}}]},{fields:[{type:"input",field:"layer",append:"刷新"}]}],labelPosition:"top",gutter:12});return b(()=>{}),(e,s)=>{const f=x,_=F;return k(),v("div",M,[o(t(V),{modelValue:t(n),"onUpdate:modelValue":s[0]||(s[0]=g=>y(n)?n.value=g:null),span:12},null,8,["modelValue"]),o(f,{ref_key:"refForm",ref:m,config:t(u)},null,8,["config"]),C("div",W,[o(_,{config:t(a)},null,8,["config"])])])}}}),I=R(q,[["__scopeId","data-v-4596b3d1"]]);export{I as default};
