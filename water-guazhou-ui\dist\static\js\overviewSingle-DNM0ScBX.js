import i from"./index-DNNRpU7M.js";import m from"./overview_zilianda-CwDy4Qk4.js";import p from"./overview_yanting-Py7FkKG_.js";import{d as e,c as s,u as a,g as r,h as o,F as n,i as c,C as _}from"./index-r0dFAfgr.js";import"./index-pS52AAh1.js";import"./index-DAiX9D4h.js";import"./Timer-DD6yFqCB.js";import"./padStart-BKfyZZDO.js";import"./ExitFullScreen-BAhtug-v.js";import"./Weather-C85tAM-i.js";import"./TitleCard-BgReUNwX.js";import"./TitleHeader-CBWfLOPA.js";import"./GWJK-D5ckGRs7.js";import"./index-BlG8PIOK.js";import"./bengzhan-Dc7fbek7.js";import"./SCDD_zilianda-DAeJlhcp.js";import"./monitoringOverview-DvKhtmcR.js";import"./wing_light-VdA2SB0B.js";import"./headwaterMonitoring-BgK7jThW.js";import"./YSGY_zilianda-Bpgb0VBC.js";import"./ZGS-Qo4tRa49.js";import"./ThreeMap.vue_vue_type_script_setup_true_lang-DUXb8phz.js";import"./Map3D-CdvMh5hC.js";import"./yljcd-ChNaQtoa.js";import"./useStation-DJgnSZIA.js";import"./zhandian-YaGuQZe6.js";import"./Legend-B_ToteFS.js";import"./StationStatistic-DVvE1CRo.js";import"./pipe-nogVzCHG.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./StatisticsHelper-D-s_6AyQ.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./data-CLo2TII-.js";import"./index-BggOjNGp.js";import"./TotalStatistic_zilianda-DP8k11Cn.js";import"./shuichangzonglan-HwbtusbI.js";const f=e({__name:"overviewSingle",setup(d){const t=s(a().roles.includes("TENANT_ADMIN"));return(u,l)=>(r(),o(i,{"show-bars":!1,"show-headers":!0},{default:n(()=>[c(t)?(r(),o(p,{key:0})):(r(),o(m,{key:1}))]),_:1}))}}),io=_(f,[["__scopeId","data-v-b880d6c9"]]);export{io as default};
