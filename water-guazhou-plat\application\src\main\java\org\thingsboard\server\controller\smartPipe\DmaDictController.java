package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.DmaDict;
import org.thingsboard.server.dao.smartPipe.DmaDictService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;
import java.util.Map;

/**
 * 智慧管网-DMA分区
 */
@RestController
@RequestMapping("api/spp/dma/dict")
public class DmaDictController extends BaseController {
    @Autowired
    private DmaDictService dmaDictService;

    @GetMapping("list")
    public IstarResponse getList(@RequestParam Map<String, Object> params) {
        return IstarResponse.ok(dmaDictService.getList(params));
    }

    @PostMapping
    public IstarResponse save(@RequestBody DmaDict dmaDict) throws ThingsboardException {
        dmaDict.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(dmaDictService.save(dmaDict));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> idList) throws ThingsboardException {
        dmaDictService.delete(idList);
        return IstarResponse.ok();
    }
}
