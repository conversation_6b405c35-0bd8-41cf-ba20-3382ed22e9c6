import{aH as Ve,au as ke,am as Ye,j as He}from"./Point-WxyopZva.js";import{E as _e,I as oe}from"./enums-BRzLM11V.js";import{eZ as Ze,R as Je}from"./index-r0dFAfgr.js";import{a as A,e as Be,b as le,n as se,f as ve,i as Ke,r as Ce,t as Qe,N as Y,h as ge,c as Xe,_ as et}from"./GeometryUtils-B7ExOJII.js";import{t as Se}from"./Rect-CUzevAry.js";import{o as Ee}from"./config-MDUrh2eL.js";import{l as ie,m as k,o as Re,n as be,p as tt,u as Pe,a as ee}from"./StyleDefinition-Bnnz5uyC.js";import{hp as st}from"./MapView-DaoQedLH.js";import{r as it,i as rt}from"./libtess-lH4Jrtkh.js";import{t as E,T as nt,m as at,l as ot}from"./StyleRepository-CdCHyVhB.js";import{r as Oe}from"./earcut-BJup91r2.js";import{c as lt}from"./TurboLine-CDscS66C.js";import{C as ht}from"./BidiEngine-CsUYIMdL.js";import"./enums-B5k73o5q.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./enums-BDQrMlcz.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./definitions-826PWLuy.js";function ct(u){return u===746||u===747||!(u<4352)&&(u>=12704&&u<=12735||u>=12544&&u<=12591||u>=65072&&u<=65103&&!(u>=65097&&u<=65103)||u>=63744&&u<=64255||u>=13056&&u<=13311||u>=11904&&u<=12031||u>=12736&&u<=12783||u>=12288&&u<=12351&&!(u>=12296&&u<=12305||u>=12308&&u<=12319||u===12336)||u>=13312&&u<=19903||u>=19968&&u<=40959||u>=12800&&u<=13055||u>=12592&&u<=12687||u>=43360&&u<=43391||u>=55216&&u<=55295||u>=4352&&u<=4607||u>=44032&&u<=55215||u>=12352&&u<=12447||u>=12272&&u<=12287||u>=12688&&u<=12703||u>=12032&&u<=12255||u>=12784&&u<=12799||u>=12448&&u<=12543&&u!==12540||u>=65280&&u<=65519&&!(u===65288||u===65289||u===65293||u>=65306&&u<=65310||u===65339||u===65341||u===65343||u>=65371&&u<=65503||u===65507||u>=65512&&u<=65519)||u>=65104&&u<=65135&&!(u>=65112&&u<=65118||u>=65123&&u<=65126)||u>=5120&&u<=5759||u>=6320&&u<=6399||u>=65040&&u<=65055||u>=19904&&u<=19967||u>=40960&&u<=42127||u>=42128&&u<=42191)}function ut(u){return!(u<11904)&&(u>=12704&&u<=12735||u>=12544&&u<=12591||u>=65072&&u<=65103||u>=63744&&u<=64255||u>=13056&&u<=13311||u>=11904&&u<=12031||u>=12736&&u<=12783||u>=12288&&u<=12351||u>=13312&&u<=19903||u>=19968&&u<=40959||u>=12800&&u<=13055||u>=65280&&u<=65519||u>=12352&&u<=12447||u>=12272&&u<=12287||u>=12032&&u<=12255||u>=12784&&u<=12799||u>=12448&&u<=12543||u>=65040&&u<=65055||u>=42128&&u<=42191||u>=40960&&u<=42127)}function ft(u){switch(u){case 10:case 32:case 38:case 40:case 41:case 43:case 45:case 47:case 173:case 183:case 8203:case 8208:case 8211:case 8231:return!0}return!1}function Fe(u){switch(u){case 9:case 10:case 11:case 12:case 13:case 32:return!0}return!1}const K=24,We=17;let je=class{constructor(e,t,i,s,r,n,a){this._glyphItems=e,this._maxWidth=t,this._lineHeight=i,this._letterSpacing=s,this._hAnchor=r,this._vAnchor=n,this._justify=a}getShaping(e,t,i){const s=this._letterSpacing,r=this._lineHeight,n=this._justify,a=this._maxWidth,o=[];let c=0,h=0;const f=e.length;for(let g=0;g<f;g++){const I=e.charCodeAt(g),b=i&&ct(I);let _;for(const M of this._glyphItems)if(_=M[I],_)break;o.push({codePoint:I,x:c,y:h,vertical:b,glyphMosaicItem:_}),_&&(c+=_.metrics.advance+s)}let l=c;a>0&&(l=c/Math.max(1,Math.ceil(c/a)));const d=e.includes("​"),x=[];for(let g=0;g<f-1;g++){const I=o[g].codePoint,b=ut(I);if(ft(I)||b){let _=0;if(I===10)_-=1e4;else if(b&&d)_+=150;else{I!==40&&I!==65288||(_+=50);const M=o[g+1].codePoint;M!==41&&M!==65289||(_+=50)}x.push(this._buildBreak(g+1,o[g].x,l,x,_,!1))}}const p=this._optimalBreaks(this._buildBreak(f,c,l,x,0,!0));let w=0;const y=t?-r:r;let m=0;for(let g=0;g<p.length;g++){const I=p[g];let b=m;for(;b<I&&Fe(o[b].codePoint);)o[b].glyphMosaicItem=null,++b;let _=I-1;for(;_>b&&Fe(o[_].codePoint);)o[_].glyphMosaicItem=null,--_;if(b<=_){const M=o[b].x;for(let P=b;P<=_;P++)o[P].x-=M,o[P].y=h;let B=o[_].x;o[_].glyphMosaicItem&&(B+=o[_].glyphMosaicItem.metrics.advance),w=Math.max(B,w),n&&this._applyJustification(o,b,_)}m=I,h+=y}if(o.length>0){const g=p.length-1,I=(n-this._hAnchor)*w;let b=(-this._vAnchor*(g+1)+.5)*r;t&&g&&(b+=g*r);for(const _ of o)_.x+=I,_.y+=b}return o.filter(g=>g.glyphMosaicItem)}static getTextBox(e,t){if(!e.length)return null;let i=1/0,s=1/0,r=0,n=0;for(const a of e){const o=a.glyphMosaicItem.metrics.advance,c=a.x,h=a.y-We,f=c+o,l=h+t;i=Math.min(i,c),r=Math.max(r,f),s=Math.min(s,h),n=Math.max(n,l)}return{x:i,y:s,width:r-i,height:n-s}}static getBox(e){if(!e.length)return null;let t=1/0,i=1/0,s=0,r=0;for(const n of e){const{height:a,left:o,top:c,width:h}=n.glyphMosaicItem.metrics,f=n.x,l=n.y-(a-Math.abs(c)),d=f+h+o,x=l+a;t=Math.min(t,f),s=Math.max(s,d),i=Math.min(i,l),r=Math.max(r,x)}return{x:t,y:i,width:s-t,height:r-i}}static addDecoration(e,t){const i=e.length;if(i===0)return;const s=3;let r=e[0].x+e[0].glyphMosaicItem.metrics.left,n=e[0].y;for(let o=1;o<i;o++){const c=e[o];if(c.y!==n){const h=e[o-1].x+e[o-1].glyphMosaicItem.metrics.left+e[o-1].glyphMosaicItem.metrics.width;e.push({codePoint:0,x:r,y:n+t-s,vertical:!1,glyphMosaicItem:{sdf:!0,rect:new Se(4,0,4,8),metrics:{width:h-r,height:2+2*s,left:0,top:0,advance:0},page:0,code:0}}),n=c.y,r=c.x+c.glyphMosaicItem.metrics.left}}const a=e[i-1].x+e[i-1].glyphMosaicItem.metrics.left+e[i-1].glyphMosaicItem.metrics.width;e.push({codePoint:0,x:r,y:n+t-s,vertical:!1,glyphMosaicItem:{sdf:!0,rect:new Se(4,0,4,8),metrics:{width:a-r,height:2+2*s,left:0,top:0,advance:0},page:0,code:0}})}_breakScore(e,t,i,s){const r=(e-t)*(e-t);return s?e<t?r/2:2*r:r+Math.abs(i)*i}_buildBreak(e,t,i,s,r,n){let a=null,o=this._breakScore(t,i,r,n);for(const c of s){const h=t-c.x,f=this._breakScore(h,i,r,n)+c.score;f<=o&&(a=c,o=f)}return{index:e,x:t,score:o,previousBreak:a}}_optimalBreaks(e){return e?this._optimalBreaks(e.previousBreak).concat(e.index):[]}_applyJustification(e,t,i){const s=e[i],r=s.vertical?K:s.glyphMosaicItem?s.glyphMosaicItem.metrics.advance:0,n=(s.x+r)*this._justify;for(let a=t;a<=i;a++)e[a].x-=n}};const Ne=4096,he=8,X=.5,Te=2;class pe{constructor(e,t,i=0,s=-1,r=X){this.x=e,this.y=t,this.angle=i,this.segment=s,this.minzoom=r}}class me{constructor(e,t,i,s,r,n=X,a=se){this.anchor=e,this.labelAngle=t,this.glyphAngle=i,this.page=s,this.alternateVerticalGlyph=r,this.minzoom=n,this.maxzoom=a}}let ze=class{constructor(e,t,i,s,r,n,a,o,c,h,f,l){this.tl=e,this.tr=t,this.bl=i,this.br=s,this.mosaicRect=r,this.labelAngle=n,this.minAngle=a,this.maxAngle=o,this.anchor=c,this.minzoom=h,this.maxzoom=f,this.page=l}},Ue=class{constructor(e){this.shapes=e}},dt=class{getIconPlacement(e,t,i){const s=new A(e.x,e.y),r=i.rotationAlignment===ie.MAP,n=i.keepUpright;let a=i.rotate*Be;r&&(a+=e.angle);const o=new Ue([]);return i.allowOverlap&&i.ignorePlacement||!Ee||(o.iconColliders=[]),this._addIconPlacement(o,s,t,i,a),r&&n&&this._addIconPlacement(o,s,t,i,a+le),o}_addIconPlacement(e,t,i,s,r){const n=i.pixelRatio,a=i.width/n,o=i.height/n,c=s.offset;let h=c[0],f=c[1];switch(s.anchor){case k.CENTER:h-=a/2,f-=o/2;break;case k.LEFT:f-=o/2;break;case k.RIGHT:h-=a,f-=o/2;break;case k.TOP:h-=a/2;break;case k.BOTTOM:h-=a/2,f-=o;break;case k.TOP_LEFT:break;case k.BOTTOM_LEFT:f-=o;break;case k.TOP_RIGHT:h-=a;break;case k.BOTTOM_RIGHT:h-=a,f-=o}const l=i.rect,d=2/n,x=h-d,p=f-d,w=x+l.width/n,y=p+l.height/n,m=new A(x,p),g=new A(w,y),I=new A(x,y),b=new A(w,p);if(r!==0){const M=Math.cos(r),B=Math.sin(r);m.rotate(M,B),g.rotate(M,B),I.rotate(M,B),b.rotate(M,B)}const _=new ze(m,b,I,g,l,r,0,256,t,X,se,0);if(e.shapes.push(_),(!s.allowOverlap||!s.ignorePlacement)&&Ee){const M=s.size,B=s.padding,P={xTile:t.x,yTile:t.y,dxPixels:h*M-B,dyPixels:f*M-B,hard:!s.optional,partIndex:0,width:a*M+2*B,height:o*M+2*B,angle:r,minLod:X,maxLod:se};e.iconColliders.push(P)}}getTextPlacement(e,t,i,s){const r=new A(e.x,e.y),n=s.rotate*Be,a=s.rotationAlignment===ie.MAP,o=s.keepUpright,c=s.padding;let h=X;const f=a?e.angle:0,l=e.segment>=0&&a,d=s.allowOverlap&&s.ignorePlacement?null:[],x=[],p=4,w=!l;let y=Number.POSITIVE_INFINITY,m=Number.NEGATIVE_INFINITY,g=y,I=m;const b=(l||a)&&o,_=s.size/K;let M=!1;for(const T of t)if(T.vertical){M=!0;break}let B,P=0,D=0;if(!l&&M){const T=je.getTextBox(t,s.lineHeight*K);switch(s.anchor){case k.LEFT:P=T.height/2,D=-T.width/2;break;case k.RIGHT:P=-T.height/2,D=T.width/2;break;case k.TOP:P=T.height/2,D=T.width/2;break;case k.BOTTOM:P=-T.height/2,D=-T.width/2;break;case k.TOP_LEFT:P=T.height;break;case k.BOTTOM_LEFT:D=-T.width;break;case k.TOP_RIGHT:D=T.width;break;case k.BOTTOM_RIGHT:P=-T.height}}P+=s.offset[0]*K,D+=s.offset[1]*K;for(const T of t){const C=T.glyphMosaicItem;if(!C||C.rect.isEmpty)continue;const V=C.rect,v=C.metrics,R=C.page;if(d&&w){if(B!==void 0&&B!==T.y){let S,F,N,z;M?(S=-I+P,F=y+D,N=I-g,z=m-y):(S=y+P,F=g+D,N=m-y,z=I-g);const G={xTile:e.x,yTile:e.y,dxPixels:S*_-c,dyPixels:F*_-c,hard:!s.optional,partIndex:1,width:N*_+2*c,height:z*_+2*c,angle:n,minLod:X,maxLod:se};d.push(G),y=Number.POSITIVE_INFINITY,m=Number.NEGATIVE_INFINITY,g=y,I=m}B=T.y}const Z=[];if(l){const S=.5*C.metrics.width,F=(T.x+v.left-p+S)*_*he;if(h=this._placeGlyph(e,h,F,i,e.segment,1,T.vertical,R,Z),o&&(h=this._placeGlyph(e,h,F,i,e.segment,-1,T.vertical,R,Z)),h>=Te)break}else Z.push(new me(r,f,f,R,!1)),a&&o&&Z.push(new me(r,f+le,f+le,R,!1));const $=T.x+v.left,j=T.y-We-v.top,J=$+v.width,we=j+v.height;let O,H,ce,ue,Q,fe,Ae,Le;if(!l&&M)if(T.vertical){const S=($+J)/2-v.height/2,F=(j+we)/2+v.width/2;O=new A(-F-p+P,S-p+D),H=new A(O.x+V.width,O.y+V.height),ce=new A(O.x,H.y),ue=new A(H.x,O.y)}else O=new A(-j+p+P,$-p+D),H=new A(O.x-V.height,O.y+V.width),ce=new A(H.x,O.y),ue=new A(O.x,H.y);else O=new A($-p+P,j-p+D),H=new A(O.x+V.width,O.y+V.height),ce=new A(O.x,H.y),ue=new A(H.x,O.y);for(const S of Z){let F,N,z,G;if(S.alternateVerticalGlyph){if(!Q){const q=(j+we)/2+D;Q=new A(($+J)/2+P-v.height/2-p,q+v.width/2+p),fe=new A(Q.x+V.height,Q.y-V.width),Ae=new A(fe.x,Q.y),Le=new A(Q.x,fe.y)}F=Q,N=Ae,z=Le,G=fe}else F=O,N=ce,z=ue,G=H;const de=j,Me=we,ye=S.glyphAngle+n;if(ye!==0){const q=Math.cos(ye),xe=Math.sin(ye);F=F.clone(),N=N==null?void 0:N.clone(),z=z==null?void 0:z.clone(),G=G==null?void 0:G.clone(),F.rotate(q,xe),G==null||G.rotate(q,xe),N==null||N.rotate(q,xe),z==null||z.rotate(q,xe)}let ne=0,ae=256;if(l&&M?T.vertical?S.alternateVerticalGlyph?(ne=32,ae=96):(ne=224,ae=32):(ne=224,ae=96):(ne=192,ae=64),x.push(new ze(F,z,N,G,V,S.labelAngle,ne,ae,S.anchor,S.minzoom,S.maxzoom,S.page)),d&&(!b||this._legible(S.labelAngle))){if(w)$<y&&(y=$),de<g&&(g=de),J>m&&(m=J),Me>I&&(I=Me);else if(S.minzoom<Te){const q={xTile:e.x,yTile:e.y,dxPixels:($+P)*_-c,dyPixels:(de+P)*_-c,hard:!s.optional,partIndex:1,width:(J-$)*_+2*c,height:(Me-de)*_+2*c,angle:ye,minLod:S.minzoom,maxLod:S.maxzoom};d.push(q)}}}}if(h>=Te)return null;if(d&&w){let T,C,V,v;M?(T=-I+P,C=y+D,V=I-g,v=m-y):(T=y+P,C=g+D,V=m-y,v=I-g);const R={xTile:e.x,yTile:e.y,dxPixels:T*_-c,dyPixels:C*_-c,hard:!s.optional,partIndex:1,width:V*_+2*c,height:v*_+2*c,angle:n,minLod:X,maxLod:se};d.push(R)}const L=new Ue(x);return d&&d.length>0&&(L.textColliders=d),L}_legible(e){const t=Ke(e);return t<65||t>=193}_placeGlyph(e,t,i,s,r,n,a,o,c){let h=n;const f=h<0?ve(e.angle+le,Ce):e.angle;let l=0;i<0&&(h*=-1,i*=-1,l=le),h>0&&++r;let d=new A(e.x,e.y),x=s[r],p=se;if(s.length<=r)return p;for(;;){const w=x.x-d.x,y=x.y-d.y,m=Math.sqrt(w*w+y*y),g=Math.max(i/m,t),I=w/m,b=y/m,_=ve(Math.atan2(b,I)+l,Ce);if(c.push(new me(d,f,_,o,!1,g,p)),a&&c.push(new me(d,f,_,o,!0,g,p)),g<=t)return g;d=x.clone();do{if(r+=h,s.length<=r||r<0)return g;x=s[r]}while(d.isEqual(x));let M=x.x-d.x,B=x.y-d.y;const P=Math.sqrt(M*M+B*B);M*=m/P,B*=m/P,d.x-=M,d.y-=B,p=g}}};var re;(function(u){u[u.moveTo=1]="moveTo",u[u.lineTo=2]="lineTo",u[u.close=7]="close"})(re||(re={}));let yt=class{constructor(e,t){this.values={},this._geometry=void 0,this._pbfGeometry=null;const i=t.keys,s=t.values,r=e.asUnsafe();for(;r.next();)switch(r.tag()){case 1:this.id=r.getUInt64();break;case 2:{const n=r.getMessage().asUnsafe(),a=this.values;for(;!n.empty();){const o=n.getUInt32(),c=n.getUInt32();a[i[o]]=s[c]}n.release();break}case 3:this.type=r.getUInt32();break;case 4:this._pbfGeometry=r.getMessage();break;default:r.skip()}}getGeometry(e){if(this._geometry!==void 0)return this._geometry;if(!this._pbfGeometry)return null;const t=this._pbfGeometry.asUnsafe();let i,s;this._pbfGeometry=null,e?e.reset(this.type):i=[];let r,n=re.moveTo,a=0,o=0,c=0;for(;!t.empty();){if(a===0){const h=t.getUInt32();n=7&h,a=h>>3}switch(a--,n){case re.moveTo:o+=t.getSInt32(),c+=t.getSInt32(),e?e.moveTo(o,c):i&&(s&&i.push(s),s=[],s.push(new A(o,c)));break;case re.lineTo:o+=t.getSInt32(),c+=t.getSInt32(),e?e.lineTo(o,c):s&&s.push(new A(o,c));break;case re.close:e?e.close():s&&!s[0].equals(o,c)&&s.push(s[0].clone());break;default:throw t.release(),new Error("Invalid path operation")}}return e?r=e.result():i&&(s&&i.push(s),r=i),t.release(),this._geometry=r,r}},te=class extends E{constructor(){super(12)}add(e,t,i){const s=this.array;s.push(e),s.push(t),s.push(i)}};class De{constructor(e){this.extent=4096,this.keys=[],this.values=[],this._pbfLayer=e.clone();const t=e.asUnsafe();for(;t.next();)switch(t.tag()){case 1:this.name=t.getString();break;case 3:this.keys.push(t.getString());break;case 4:this.values.push(t.processMessage(De._parseValue));break;case 5:this.extent=t.getUInt32();break;default:t.skip()}}getData(){return this._pbfLayer}static _parseValue(e){for(;e.next();)switch(e.tag()){case 1:return e.getString();case 2:return e.getFloat();case 3:return e.getDouble();case 4:return e.getInt64();case 5:return e.getUInt64();case 6:return e.getSInt64();case 7:return e.getBool();default:e.skip()}return null}}let xt=class extends E{constructor(e){super(e)}add(e,t,i,s,r,n,a,o,c,h,f,l){const d=this.array;let x=E.i1616to32(e,t);d.push(x);const p=31;x=E.i8888to32(Math.round(p*i),Math.round(p*s),Math.round(p*r),Math.round(p*n)),d.push(x),x=E.i8888to32(Math.round(p*a),Math.round(p*o),Math.round(p*c),Math.round(p*h)),d.push(x),x=E.i1616to32(f,0),d.push(x),l&&d.push(...l)}},gt=class extends E{constructor(e){super(e)}add(e,t,i){const s=this.array;s.push(E.i1616to32(e,t)),i&&s.push(...i)}};class pt extends E{constructor(e){super(e)}add(e,t,i,s,r,n,a){const o=this.array,c=this.index;let h=E.i1616to32(e,t);o.push(h);const f=15;return h=E.i8888to32(Math.round(f*i),Math.round(f*s),r,n),o.push(h),a&&o.push(...a),c}}class Ge extends E{constructor(e){super(e)}add(e,t,i,s,r,n,a,o,c,h,f,l){const d=this.array;let x=E.i1616to32(e,t);d.push(x),x=E.i1616to32(Math.round(8*i),Math.round(8*s)),d.push(x),x=E.i8888to32(r/4,n/4,o,c),d.push(x),x=E.i8888to32(0,Ke(a),10*h,Math.min(10*f,255)),d.push(x),l&&d.push(...l)}}let mt=class extends E{constructor(e){super(e)}add(e,t,i,s,r){const n=this.array,a=E.i1616to32(2*e+i,2*t+s);n.push(a),r&&n.push(...r)}};class Ie{constructor(e,t,i){this.layerExtent=4096,this._features=[],this.layer=e,this.zoom=t,this._spriteInfo=i,this._filter=e.getFeatureFilter()}pushFeature(e){this._filter&&!this._filter.filter(e,this.zoom)||this._features.push(e)}hasFeatures(){return this._features.length>0}getResources(e,t,i){}}let _t=class extends Ie{constructor(e,t,i,s,r){super(e,t,i),this.type=_e.CIRCLE,this._circleVertexBuffer=s,this._circleIndexBuffer=r}get circleIndexStart(){return this._circleIndexStart}get circleIndexCount(){return this._circleIndexCount}processFeatures(e){const t=this._circleVertexBuffer,i=this._circleIndexBuffer;this._circleIndexStart=3*i.index,this._circleIndexCount=0;const s=this.layer,r=this.zoom;e&&e.setExtent(this.layerExtent);for(const n of this._features){const a=n.getGeometry(e);if(!a)continue;const o=s.circleMaterial.encodeAttributes(n,r,s);for(const c of a)if(c)for(const h of c){const f=t.index;t.add(h.x,h.y,0,0,o),t.add(h.x,h.y,0,1,o),t.add(h.x,h.y,1,0,o),t.add(h.x,h.y,1,1,o),i.add(f+0,f+1,f+2),i.add(f+1,f+2,f+3),this._circleIndexCount+=6}}}serialize(){let e=6;e+=this.layerUIDs.length,e+=this._circleVertexBuffer.array.length,e+=this._circleIndexBuffer.array.length;const t=new Uint32Array(e),i=new Int32Array(t.buffer);let s=0;t[s++]=this.type,t[s++]=this.layerUIDs.length;for(let r=0;r<this.layerUIDs.length;r++)t[s++]=this.layerUIDs[r];t[s++]=this._circleIndexStart,t[s++]=this._circleIndexCount,t[s++]=this._circleVertexBuffer.array.length;for(let r=0;r<this._circleVertexBuffer.array.length;r++)i[s++]=this._circleVertexBuffer.array[r];t[s++]=this._circleIndexBuffer.array.length;for(let r=0;r<this._circleIndexBuffer.array.length;r++)t[s++]=this._circleIndexBuffer.array[r];return t.buffer}},It=class qe extends Ie{constructor(e,t,i,s,r,n,a){super(e,t,i),this.type=_e.FILL,this._patternMap=new Map,this._fillVertexBuffer=s,this._fillIndexBuffer=r,this._outlineVertexBuffer=n,this._outlineIndexBuffer=a}get fillIndexStart(){return this._fillIndexStart}get fillIndexCount(){return this._fillIndexCount}get outlineIndexStart(){return this._outlineIndexStart}get outlineIndexCount(){return this._outlineIndexCount}getResources(e,t,i){const s=this.layer,r=this.zoom,n=s.getPaintProperty("fill-pattern");if(n)if(n.isDataDriven)for(const a of this._features)t(n.getValue(r,a),!0);else t(n.getValue(r),!0)}processFeatures(e){this._fillIndexStart=3*this._fillIndexBuffer.index,this._fillIndexCount=0,this._outlineIndexStart=3*this._outlineIndexBuffer.index,this._outlineIndexCount=0;const t=this.layer,i=this.zoom,{fillMaterial:s,outlineMaterial:r,hasDataDrivenFill:n,hasDataDrivenOutline:a}=t;e&&e.setExtent(this.layerExtent);const o=t.getPaintProperty("fill-pattern"),c=o==null?void 0:o.isDataDriven;let h=!o&&t.getPaintValue("fill-antialias",i);if(t.outlineUsesFillColor){if(h&&!t.hasDataDrivenOpacity){const d=t.getPaintValue("fill-opacity",i),x=t.getPaintValue("fill-opacity",i+1);d<1&&x<1&&(h=!1)}if(h&&!t.hasDataDrivenColor){const d=t.getPaintValue("fill-color",i),x=t.getPaintValue("fill-color",i+1);d[3]<1&&x[3]<1&&(h=!1)}}const f=this._features,l=e==null?void 0:e.validateTessellation;if(c){const d=[];for(const x of f){const p=o.getValue(i,x),w=this._spriteInfo[p];if(!w||!w.rect)continue;const y=s.encodeAttributes(x,i,t,w),m=h&&a?r.encodeAttributes(x,i,t):[],g=x.getGeometry(e);d.push({ddFillAttributes:y,ddOutlineAttributes:m,page:w.page,geometry:g}),d.sort((I,b)=>I.page-b.page);for(const{ddFillAttributes:I,ddOutlineAttributes:b,page:_,geometry:M}of d)this._processFeature(M,h,t.outlineUsesFillColor,I,b,l,_)}}else for(const d of f){const x=n?s.encodeAttributes(d,i,t):null,p=h&&a?r.encodeAttributes(d,i,t):null,w=d.getGeometry(e);this._processFeature(w,h,t.outlineUsesFillColor,x,p,l)}}serialize(){let e=10;e+=this.layerUIDs.length,e+=this._fillVertexBuffer.array.length,e+=this._fillIndexBuffer.array.length,e+=this._outlineVertexBuffer.array.length,e+=this._outlineIndexBuffer.array.length,e+=3*this._patternMap.size+1;const t=new Uint32Array(e),i=new Int32Array(t.buffer);let s=0;t[s++]=this.type,t[s++]=this.layerUIDs.length;for(let a=0;a<this.layerUIDs.length;a++)t[s++]=this.layerUIDs[a];t[s++]=this._fillIndexStart,t[s++]=this._fillIndexCount,t[s++]=this._outlineIndexStart,t[s++]=this._outlineIndexCount;const r=this._patternMap,n=r.size;if(t[s++]=n,n>0)for(const[a,[o,c]]of r)t[s++]=a,t[s++]=o,t[s++]=c;t[s++]=this._fillVertexBuffer.array.length;for(let a=0;a<this._fillVertexBuffer.array.length;a++)i[s++]=this._fillVertexBuffer.array[a];t[s++]=this._fillIndexBuffer.array.length;for(let a=0;a<this._fillIndexBuffer.array.length;a++)t[s++]=this._fillIndexBuffer.array[a];t[s++]=this._outlineVertexBuffer.array.length;for(let a=0;a<this._outlineVertexBuffer.array.length;a++)i[s++]=this._outlineVertexBuffer.array[a];t[s++]=this._outlineIndexBuffer.array.length;for(let a=0;a<this._outlineIndexBuffer.array.length;a++)t[s++]=this._outlineIndexBuffer.array[a];return t.buffer}_processFeature(e,t,i,s,r,n,a){if(!e)return;const o=e.length,c=!r||r.length===0;if(t&&(!i||c))for(let l=0;l<o;l++)this._processOutline(e[l],r);const h=32;let f;for(let l=0;l<o;l++){const d=qe._area(e[l]);d>h?(f!==void 0&&this._processFill(e,f,s,n,a),f=[l]):d<-h&&f!==void 0&&f.push(l)}f!==void 0&&this._processFill(e,f,s,n,a)}_processOutline(e,t){const i=this._outlineVertexBuffer,s=this._outlineIndexBuffer,r=s.index;let n,a,o;const c=new A(0,0),h=new A(0,0),f=new A(0,0);let l=-1,d=-1,x=-1,p=-1,w=-1,y=!1;const m=0;let g=e.length;if(g<2)return;const I=e[m];let b=e[g-1];for(;g&&b.isEqual(I);)--g,b=e[g-1];if(!(g-m<2)){for(let _=m;_<g;++_){_===m?(n=e[g-1],a=e[m],o=e[m+1],c.assignSub(a,n),c.normalize(),c.rightPerpendicular()):(n=a,a=o,o=_!==g-1?e[_+1]:e[m],c.assign(h));const M=this._isClipEdge(n,a);p===-1&&(y=M),h.assignSub(o,a),h.normalize(),h.rightPerpendicular();const B=c.x*h.y-c.y*h.x;f.assignAdd(c,h),f.normalize();const P=-f.x*-c.x+-f.y*-c.y;let D=Math.abs(P!==0?1/P:1);D>8&&(D=8),B>=0?(x=i.add(a.x,a.y,c.x,c.y,0,1,t),p===-1&&(p=x),l>=0&&d>=0&&x>=0&&!M&&s.add(l,d,x),d=i.add(a.x,a.y,D*-f.x,D*-f.y,0,-1,t),w===-1&&(w=d),l>=0&&d>=0&&x>=0&&!M&&s.add(l,d,x),l=d,d=x,x=i.add(a.x,a.y,f.x,f.y,0,1,t),l>=0&&d>=0&&x>=0&&!M&&s.add(l,d,x),d=i.add(a.x,a.y,h.x,h.y,0,1,t),l>=0&&d>=0&&x>=0&&!M&&s.add(l,d,x)):(x=i.add(a.x,a.y,D*f.x,D*f.y,0,1,t),p===-1&&(p=x),l>=0&&d>=0&&x>=0&&!M&&s.add(l,d,x),d=i.add(a.x,a.y,-c.x,-c.y,0,-1,t),w===-1&&(w=d),l>=0&&d>=0&&x>=0&&!M&&s.add(l,d,x),l=d,d=x,x=i.add(a.x,a.y,-f.x,-f.y,0,-1,t),l>=0&&d>=0&&x>=0&&!M&&s.add(l,d,x),l=i.add(a.x,a.y,-h.x,-h.y,0,-1,t),l>=0&&d>=0&&x>=0&&!M&&s.add(l,d,x))}l>=0&&d>=0&&p>=0&&!y&&s.add(l,d,p),l>=0&&p>=0&&w>=0&&!y&&s.add(l,w,p),this._outlineIndexCount+=3*(s.index-r)}}_processFill(e,t,i,s,r){s=!0;let n;t.length>1&&(n=[]);let a=0;for(const f of t)a!==0&&n.push(a),a+=e[f].length;const o=2*a,c=Ve.acquire();for(const f of t){const l=e[f],d=l.length;for(let x=0;x<d;++x)c.push(l[x].x,l[x].y)}const h=Oe(c,n,2);if(s&&Oe.deviation(c,n,2,h)>0){const f=t.map(x=>e[x].length),{buffer:l,vertexCount:d}=it(c,f);if(d>0){const x=this._fillVertexBuffer.index;for(let p=0;p<d;p++)this._fillVertexBuffer.add(l[2*p],l[2*p+1],i);for(let p=0;p<d;p+=3){const w=x+p;this._fillIndexBuffer.add(w,w+1,w+2)}if(r!==void 0){const p=this._patternMap,w=p.get(r);w?w[1]+=d:p.set(r,[this._fillIndexStart+this._fillIndexCount,d])}this._fillIndexCount+=d}}else{const f=h.length;if(f>0){const l=this._fillVertexBuffer.index;let d=0;for(;d<o;)this._fillVertexBuffer.add(c[d++],c[d++],i);let x=0;for(;x<f;)this._fillIndexBuffer.add(l+h[x++],l+h[x++],l+h[x++]);if(r!==void 0){const p=this._patternMap,w=p.get(r);w?w[1]+=f:p.set(r,[this._fillIndexStart+this._fillIndexCount,f])}this._fillIndexCount+=f}}Ve.release(c)}_isClipEdge(e,t){return e.x===t.x?e.x<=-64||e.x>=4160:e.y===t.y&&(e.y<=-64||e.y>=4160)}static _area(e){let t=0;const i=e.length-1;for(let s=0;s<i;s++)t+=(e[s].x-e[s+1].x)*(e[s].y+e[s+1].y);return t+=(e[i].x-e[0].x)*(e[i].y+e[0].y),.5*t}};const wt=65535;class Mt extends Ie{constructor(e,t,i,s,r){super(e,t,i),this.type=_e.LINE,this._tessellationOptions={pixelCoordRatio:8,halfWidth:0,offset:0},this._patternMap=new Map,this.tessellationProperties={_lineVertexBuffer:null,_lineIndexBuffer:null,_ddValues:null},this.tessellationProperties._lineVertexBuffer=s,this.tessellationProperties._lineIndexBuffer=r,this._lineTessellator=new lt(bt(this.tessellationProperties),Pt(this.tessellationProperties),e.canUseThinTessellation)}get lineIndexStart(){return this._lineIndexStart}get lineIndexCount(){return this._lineIndexCount}getResources(e,t,i){const s=this.layer,r=this.zoom,n=s.getPaintProperty("line-pattern"),a=s.getPaintProperty("line-dasharray"),o=s.getLayoutProperty("line-cap");if(!n&&!a)return;const c=(o==null?void 0:o.getValue(r))||0,h=o==null?void 0:o.isDataDriven,f=n==null?void 0:n.isDataDriven,l=a==null?void 0:a.isDataDriven;if(f||l)for(const d of this._features)t(f?n.getValue(r,d):this._getDashArrayKey(d,r,s,a,h,o,c));else if(n)t(n.getValue(r));else if(a){const d=a.getValue(r);t(s.getDashKey(d,c))}}processFeatures(e){this._lineIndexStart=3*this.tessellationProperties._lineIndexBuffer.index,this._lineIndexCount=0;const t=this.layer,i=this.zoom,s=this._features,r=this._tessellationOptions,{hasDataDrivenLine:n,lineMaterial:a}=t;e&&e.setExtent(this.layerExtent);const o=t.getPaintProperty("line-pattern"),c=t.getPaintProperty("line-dasharray"),h=o==null?void 0:o.isDataDriven,f=c==null?void 0:c.isDataDriven;let l;l=t.getLayoutProperty("line-cap");const d=l!=null&&l.isDataDriven?l:null,x=d?null:t.getLayoutValue("line-cap",i),p=x||0,w=!!d;l=t.getLayoutProperty("line-join");const y=l!=null&&l.isDataDriven?l:null,m=y?null:t.getLayoutValue("line-join",i);l=t.getLayoutProperty("line-miter-limit");const g=l!=null&&l.isDataDriven?l:null,I=g?null:t.getLayoutValue("line-miter-limit",i);l=t.getLayoutProperty("line-round-limit");const b=l!=null&&l.isDataDriven?l:null,_=b?null:t.getLayoutValue("line-round-limit",i);l=t.getPaintProperty("line-width");const M=l!=null&&l.isDataDriven?l:null,B=M?null:t.getPaintValue("line-width",i);l=t.getPaintProperty("line-offset");const P=l!=null&&l.isDataDriven?l:null,D=P?null:t.getPaintValue("line-offset",i);if(h||f){const L=[];for(const T of s){const C=h?o.getValue(i,T):this._getDashArrayKey(T,i,t,c,w,d,p),V=this._spriteInfo[C];if(!V||!V.rect)continue;const v=a.encodeAttributes(T,i,t,V),R=T.getGeometry(e);L.push({ddAttributes:v,page:V.page,cap:d?d.getValue(i,T):x,join:y?y.getValue(i,T):m,miterLimit:g?g.getValue(i,T):I,roundLimit:b?b.getValue(i,T):_,halfWidth:.5*(M?M.getValue(i,T):B),offset:P?P.getValue(i,T):D,geometry:R})}L.sort((T,C)=>T.page-C.page),r.textured=!0;for(const{ddAttributes:T,page:C,cap:V,join:v,miterLimit:R,roundLimit:Z,halfWidth:$,offset:j,geometry:J}of L)r.capType=V,r.joinType=v,r.miterLimit=R,r.roundLimit=Z,r.halfWidth=$,r.offset=j,this._processFeature(J,T,C)}else{if(o){const L=o.getValue(i),T=this._spriteInfo[L];if(!T||!T.rect)return}r.textured=!(!o&&!c),r.capType=x,r.joinType=m,r.miterLimit=I,r.roundLimit=_,r.halfWidth=.5*B,r.offset=D;for(const L of s){const T=n?a.encodeAttributes(L,i,t):null;d&&(r.capType=d.getValue(i,L)),y&&(r.joinType=y.getValue(i,L)),g&&(r.miterLimit=g.getValue(i,L)),b&&(r.roundLimit=b.getValue(i,L)),M&&(r.halfWidth=.5*M.getValue(i,L)),P&&(r.offset=P.getValue(i,L));const C=L.getGeometry(e);this._processFeature(C,T)}}}serialize(){let e=6;e+=this.layerUIDs.length,e+=this.tessellationProperties._lineVertexBuffer.array.length,e+=this.tessellationProperties._lineIndexBuffer.array.length,e+=3*this._patternMap.size+1;const t=new Uint32Array(e),i=new Int32Array(t.buffer);let s=0;t[s++]=this.type,t[s++]=this.layerUIDs.length;for(let a=0;a<this.layerUIDs.length;a++)t[s++]=this.layerUIDs[a];t[s++]=this._lineIndexStart,t[s++]=this._lineIndexCount;const r=this._patternMap,n=r.size;if(t[s++]=n,n>0)for(const[a,[o,c]]of r)t[s++]=a,t[s++]=o,t[s++]=c;t[s++]=this.tessellationProperties._lineVertexBuffer.array.length;for(let a=0;a<this.tessellationProperties._lineVertexBuffer.array.length;a++)i[s++]=this.tessellationProperties._lineVertexBuffer.array[a];t[s++]=this.tessellationProperties._lineIndexBuffer.array.length;for(let a=0;a<this.tessellationProperties._lineIndexBuffer.array.length;a++)t[s++]=this.tessellationProperties._lineIndexBuffer.array[a];return t.buffer}_processFeature(e,t,i){if(!e)return;const s=e.length;for(let r=0;r<s;r++)this._processGeometry(e[r],t,i)}_processGeometry(e,t,i){if(e.length<2)return;const s=.001;let r,n,a=e[0],o=1;for(;o<e.length;)r=e[o].x-a.x,n=e[o].y-a.y,r*r+n*n<s*s?e.splice(o,1):(a=e[o],++o);if(e.length<2)return;const c=this.tessellationProperties._lineIndexBuffer,h=3*c.index;this._tessellationOptions.initialDistance=0,this._tessellationOptions.wrapDistance=wt,this.tessellationProperties._ddValues=t,this._lineTessellator.tessellate(e,this._tessellationOptions);const f=3*c.index-h;if(i!==void 0){const l=this._patternMap,d=l.get(i);d?d[1]+=f:l.set(i,[h+this._lineIndexCount,f])}this._lineIndexCount+=f}_getDashArrayKey(e,t,i,s,r,n,a){const o=r?n.getValue(t,e):a,c=s.getValue(t,e);return i.getDashKey(c,o)}}const bt=u=>(e,t,i,s,r,n,a,o,c,h,f)=>(u._lineVertexBuffer.add(e,t,a,o,i,s,r,n,c,h,f,u._ddValues),u._lineVertexBuffer.index-1),Pt=u=>(e,t,i)=>{u._lineIndexBuffer.add(e,t,i)},$e=10;function Tt(u,e){return u.iconMosaicItem&&e.iconMosaicItem?u.iconMosaicItem.page===e.iconMosaicItem.page?0:u.iconMosaicItem.page-e.iconMosaicItem.page:u.iconMosaicItem&&!e.iconMosaicItem?1:!u.iconMosaicItem&&e.iconMosaicItem?-1:0}class U extends Ie{constructor(e,t,i,s,r,n,a,o){super(e,t,o.getSpriteItems()),this.type=_e.SYMBOL,this._markerMap=new Map,this._glyphMap=new Map,this._glyphBufferDataStorage=new Map,this._isIconSDF=!1,this._iconVertexBuffer=i,this._iconIndexBuffer=s,this._textVertexBuffer=r,this._textIndexBuffer=n,this._placementEngine=a,this._workerTileHandler=o}get markerPageMap(){return this._markerMap}get glyphsPageMap(){return this._glyphMap}get symbolInstances(){return this._symbolInstances}getResources(e,t,i){const s=this.layer,r=this.zoom;e&&e.setExtent(this.layerExtent);const n=s.getLayoutProperty("icon-image"),a=s.getLayoutProperty("text-field");let o=s.getLayoutProperty("text-transform"),c=s.getLayoutProperty("text-font");const h=[];let f,l,d,x;n&&!n.isDataDriven&&(f=n.getValue(r)),a&&!a.isDataDriven&&(l=a.getValue(r)),o&&o.isDataDriven||(d=s.getLayoutValue("text-transform",r),o=null),c&&c.isDataDriven||(x=s.getLayoutValue("text-font",r),c=null);for(const p of this._features){const w=p.getGeometry(e);if(!w||w.length===0)continue;let y,m;n&&(y=n.isDataDriven?n.getValue(r,p):this._replaceKeys(f,p.values),y&&t(y));let g=!1;if(a&&(m=a.isDataDriven?a.getValue(r,p):this._replaceKeys(l,p.values),m)){switch(m=m.replace(/\\n/g,`
`),o&&(d=o.getValue(r,p)),d){case Re.LOWERCASE:m=m.toLowerCase();break;case Re.UPPERCASE:m=m.toUpperCase()}if(U._bidiEngine.hasBidiChar(m)){let M;M=U._bidiEngine.checkContextual(m)==="rtl"?"IDNNN":"ICNNN",m=U._bidiEngine.bidiTransform(m,M,"VLYSN"),g=!0}const _=m.length;if(_>0){c&&(x=c.getValue(r,p));for(const M of x){let B=i[M];B||(B=i[M]=new Set);for(let P=0;P<_;P++){const D=m.charCodeAt(P);B.add(D)}}}}if(!y&&!m)continue;const I=s.getLayoutValue("symbol-sort-key",r,p),b={feature:p,sprite:y,label:m,rtl:g,geometry:w,hash:(m?ke(m):0)^(y?ke(y):0),priority:I,textFont:x};h.push(b)}this._symbolFeatures=h}processFeatures(e){e&&e.setExtent(this.layerExtent);const t=this.layer,i=this.zoom,s=t.getLayoutValue("symbol-placement",i),r=s!==be.POINT,n=t.getLayoutValue("symbol-spacing",i)*he,a=t.getLayoutProperty("icon-image"),o=t.getLayoutProperty("text-field"),c=a?new nt(t,i,r):null,h=o?new at(t,i,r):null,f=this._workerTileHandler;let l;a&&(l=f.getSpriteItems()),this._iconIndexStart=3*this._iconIndexBuffer.index,this._textIndexStart=3*this._textIndexBuffer.index,this._iconIndexCount=0,this._textIndexCount=0,this._markerMap.clear(),this._glyphMap.clear();const d=[];let x=1;h&&h.size&&(x=h.size/K);const p=h?h.maxAngle*Be:0,w=h?h.size*he:0;for(const y of this._symbolFeatures){let m;c&&l&&y.sprite&&(m=l[y.sprite],m&&m.sdf&&(this._isIconSDF=!0));let g;m&&c.update(i,y.feature);let I=0;const b=y.label;if(b){Ze(h),h.update(i,y.feature);const _=r&&h.rotationAlignment===ie.MAP?h.keepUpright:h.writingMode&&h.writingMode.includes(tt.VERTICAL);let M=.5;switch(h.anchor){case k.TOP_LEFT:case k.LEFT:case k.BOTTOM_LEFT:M=0;break;case k.TOP_RIGHT:case k.RIGHT:case k.BOTTOM_RIGHT:M=1}let B=.5;switch(h.anchor){case k.TOP_LEFT:case k.TOP:case k.TOP_RIGHT:B=0;break;case k.BOTTOM_LEFT:case k.BOTTOM:case k.BOTTOM_RIGHT:B=1}let P=.5;switch(h.justify){case Pe.AUTO:P=M;break;case Pe.LEFT:P=0;break;case Pe.RIGHT:P=1}const D=h.letterSpacing*K,L=r?0:h.maxWidth*K,T=h.lineHeight*K,C=y.textFont.map(V=>f.getGlyphItems(V));if(g=new je(C,L,T,D,M,B,P).getShaping(b,y.rtl,_),g&&g.length>0){let V=1e30,v=-1e30;for(const R of g)V=Math.min(V,R.x),v=Math.max(v,R.x);I=(v-V+2*K)*x*he}}for(let _ of y.geometry){const M=[];if(s===be.LINE){if(g!=null&&g.length&&(h!=null&&h.size)){const B=h.size*he*(2+Math.min(2,4*Math.abs(h.offset[1])));_=U._smoothVertices(_,B)}U._pushAnchors(M,_,n,I)}else s===be.LINE_CENTER?U._pushCenterAnchor(M,_):y.feature.type===Qe.Polygon?U._pushCentroid(M,_):M.push(new pe(_[0].x,_[0].y));for(const B of M){if(B.x<0||B.x>Ne||B.y<0||B.y>Ne||r&&I>0&&(h==null?void 0:h.rotationAlignment)===ie.MAP&&!U._honorsTextMaxAngle(_,B,I,p,w))continue;const P={shaping:g,line:_,iconMosaicItem:m,anchor:B,symbolFeature:y,textColliders:[],iconColliders:[],textVertexRanges:[],iconVertexRanges:[]};d.push(P),this._processFeature(P,c,h)}}}d.sort(Tt),this._addPlacedGlyphs(),this._symbolInstances=d}serialize(){let e=11;e+=this.layerUIDs.length,e+=3*this.markerPageMap.size,e+=3*this.glyphsPageMap.size,e+=U._symbolsSerializationLength(this._symbolInstances),e+=this._iconVertexBuffer.array.length,e+=this._iconIndexBuffer.array.length,e+=this._textVertexBuffer.array.length,e+=this._textIndexBuffer.array.length;const t=new Uint32Array(e),i=new Int32Array(t.buffer),s=new Float32Array(t.buffer);let r=0;t[r++]=this.type,t[r++]=this.layerUIDs.length;for(let n=0;n<this.layerUIDs.length;n++)t[r++]=this.layerUIDs[n];t[r++]=this._isIconSDF?1:0,t[r++]=this.markerPageMap.size;for(const[n,[a,o]]of this.markerPageMap)t[r++]=n,t[r++]=a,t[r++]=o;t[r++]=this.glyphsPageMap.size;for(const[n,[a,o]]of this.glyphsPageMap)t[r++]=n,t[r++]=a,t[r++]=o;t[r++]=this._iconVertexBuffer.index/4,t[r++]=this._textVertexBuffer.index/4,r=U.serializeSymbols(t,i,s,r,this._symbolInstances),t[r++]=this._iconVertexBuffer.array.length;for(let n=0;n<this._iconVertexBuffer.array.length;n++)i[r++]=this._iconVertexBuffer.array[n];t[r++]=this._iconIndexBuffer.array.length;for(let n=0;n<this._iconIndexBuffer.array.length;n++)t[r++]=this._iconIndexBuffer.array[n];t[r++]=this._textVertexBuffer.array.length;for(let n=0;n<this._textVertexBuffer.array.length;n++)i[r++]=this._textVertexBuffer.array[n];t[r++]=this._textIndexBuffer.array.length;for(let n=0;n<this._textIndexBuffer.array.length;n++)t[r++]=this._textIndexBuffer.array[n];return t.buffer}static _symbolsSerializationLength(e){let t=0;t+=1;for(const i of e||[]){t+=4,t+=1;for(const s of i.textColliders)t+=$e;for(const s of i.iconColliders)t+=$e;t+=1,t+=2*i.textVertexRanges.length,t+=1,t+=2*i.iconVertexRanges.length}return t}static serializeSymbols(e,t,i,s,r){r=r||[],t[s++]=r.length;for(const n of r){t[s++]=n.anchor.x,t[s++]=n.anchor.y,t[s++]=n.symbolFeature.hash,t[s++]=n.symbolFeature.priority,t[s++]=n.textColliders.length+n.iconColliders.length;for(const a of n.textColliders)t[s++]=a.xTile,t[s++]=a.yTile,t[s++]=a.dxPixels,t[s++]=a.dyPixels,t[s++]=a.hard?1:0,t[s++]=a.partIndex,i[s++]=a.minLod,i[s++]=a.maxLod,t[s++]=a.width,t[s++]=a.height;for(const a of n.iconColliders)t[s++]=a.xTile,t[s++]=a.yTile,t[s++]=a.dxPixels,t[s++]=a.dyPixels,t[s++]=a.hard?1:0,t[s++]=a.partIndex,i[s++]=a.minLod,i[s++]=a.maxLod,t[s++]=a.width,t[s++]=a.height;t[s++]=n.textVertexRanges.length;for(const[a,o]of n.textVertexRanges)t[s++]=a,t[s++]=o;t[s++]=n.iconVertexRanges.length;for(const[a,o]of n.iconVertexRanges)t[s++]=a,t[s++]=o}return s}_replaceKeys(e,t){return e.replace(/{([^{}]+)}/g,(i,s)=>s in t?t[s]:"")}_processFeature(e,t,i){const{line:s,iconMosaicItem:r,shaping:n,anchor:a}=e,o=this.zoom,c=this.layer,h=!!r;let f=!0;h&&(f=(t==null?void 0:t.optional)||!r);const l=n&&n.length>0,d=!l||(i==null?void 0:i.optional);let x,p;if(h&&(x=this._placementEngine.getIconPlacement(a,r,t)),(x||f)&&(l&&(p=this._placementEngine.getTextPlacement(a,n,s,i)),p||d)){if(x&&p||(d||f?d||p?f||x||(p=null):x=null:(x=null,p=null)),p){const w=c.hasDataDrivenText?c.textMaterial.encodeAttributes(e.symbolFeature.feature,o,c):null;if(this._storePlacedGlyphs(e,p.shapes,o,i.rotationAlignment,w),p.textColliders){e.textColliders=p.textColliders;for(const y of p.textColliders){y.minLod=Math.max(o+Y(y.minLod),0),y.maxLod=Math.min(o+Y(y.maxLod),25);const m=y.angle;if(m){const g=Math.cos(m),I=Math.sin(m),b=y.dxPixels*g-y.dyPixels*I,_=y.dxPixels*I+y.dyPixels*g,M=(y.dxPixels+y.width)*g-y.dyPixels*I,B=(y.dxPixels+y.width)*I+y.dyPixels*g,P=y.dxPixels*g-(y.dyPixels+y.height)*I,D=y.dxPixels*I+(y.dyPixels+y.height)*g,L=(y.dxPixels+y.width)*g-(y.dyPixels+y.height)*I,T=(y.dxPixels+y.width)*I+(y.dyPixels+y.height)*g,C=Math.min(b,M,P,L),V=Math.max(b,M,P,L),v=Math.min(_,B,D,T),R=Math.max(_,B,D,T);y.dxPixels=C,y.dyPixels=v,y.width=V-C,y.height=R-v}}}}if(x){const w=c.hasDataDrivenIcon?c.iconMaterial.encodeAttributes(e.symbolFeature.feature,o,c):null;if(this._addPlacedIcons(e,x.shapes,o,r.page,t.rotationAlignment===ie.VIEWPORT,w),x.iconColliders){e.iconColliders=x.iconColliders;for(const y of x.iconColliders){y.minLod=Math.max(o+Y(y.minLod),0),y.maxLod=Math.min(o+Y(y.maxLod),25);const m=y.angle;if(m){const g=Math.cos(m),I=Math.sin(m),b=y.dxPixels*g-y.dyPixels*I,_=y.dxPixels*I+y.dyPixels*g,M=(y.dxPixels+y.width)*g-y.dyPixels*I,B=(y.dxPixels+y.width)*I+y.dyPixels*g,P=y.dxPixels*g-(y.dyPixels+y.height)*I,D=y.dxPixels*I+(y.dyPixels+y.height)*g,L=(y.dxPixels+y.width)*g-(y.dyPixels+y.height)*I,T=(y.dxPixels+y.width)*I+(y.dyPixels+y.height)*g,C=Math.min(b,M,P,L),V=Math.max(b,M,P,L),v=Math.min(_,B,D,T),R=Math.max(_,B,D,T);y.dxPixels=C,y.dyPixels=v,y.width=V-C,y.height=R-v}}}}}}_addPlacedIcons(e,t,i,s,r,n){const a=Math.max(i-1,0),o=this._iconVertexBuffer,c=this._iconIndexBuffer,h=this._markerMap;for(const f of t){const l=r?0:Math.max(i+Y(f.minzoom),a),d=r?25:Math.min(i+Y(f.maxzoom),25);if(d<=l)continue;const x=f.tl,p=f.tr,w=f.bl,y=f.br,m=f.mosaicRect,g=f.labelAngle,I=f.minAngle,b=f.maxAngle,_=f.anchor,M=o.index,B=m.x,P=m.y,D=B+m.width,L=P+m.height,T=o.index;o.add(_.x,_.y,x.x,x.y,B,P,g,I,b,l,d,n),o.add(_.x,_.y,p.x,p.y,D,P,g,I,b,l,d,n),o.add(_.x,_.y,w.x,w.y,B,L,g,I,b,l,d,n),o.add(_.x,_.y,y.x,y.y,D,L,g,I,b,l,d,n),e.iconVertexRanges.length>0&&e.iconVertexRanges[0][0]+e.iconVertexRanges[0][1]===T?e.iconVertexRanges[0][1]+=4:e.iconVertexRanges.push([T,4]),c.add(M+0,M+1,M+2),c.add(M+1,M+2,M+3),h.has(s)?h.get(s)[1]+=6:h.set(s,[this._iconIndexStart+this._iconIndexCount,6]),this._iconIndexCount+=6}}_addPlacedGlyphs(){const e=this._textVertexBuffer,t=this._textIndexBuffer,i=this._glyphMap;for(const[s,r]of this._glyphBufferDataStorage)for(const n of r){const a=e.index,o=n.symbolInstance,c=n.ddAttributes,h=e.index;e.add(n.glyphAnchor[0],n.glyphAnchor[1],n.tl[0],n.tl[1],n.xmin,n.ymin,n.labelAngle,n.minAngle,n.maxAngle,n.minLod,n.maxLod,c),e.add(n.glyphAnchor[0],n.glyphAnchor[1],n.tr[0],n.tr[1],n.xmax,n.ymin,n.labelAngle,n.minAngle,n.maxAngle,n.minLod,n.maxLod,c),e.add(n.glyphAnchor[0],n.glyphAnchor[1],n.bl[0],n.bl[1],n.xmin,n.ymax,n.labelAngle,n.minAngle,n.maxAngle,n.minLod,n.maxLod,c),e.add(n.glyphAnchor[0],n.glyphAnchor[1],n.br[0],n.br[1],n.xmax,n.ymax,n.labelAngle,n.minAngle,n.maxAngle,n.minLod,n.maxLod,c),o.textVertexRanges.length>0&&o.textVertexRanges[0][0]+o.textVertexRanges[0][1]===h?o.textVertexRanges[0][1]+=4:o.textVertexRanges.push([h,4]),t.add(a+0,a+1,a+2),t.add(a+1,a+2,a+3),i.has(s)?i.get(s)[1]+=6:i.set(s,[this._textIndexStart+this._textIndexCount,6]),this._textIndexCount+=6}this._glyphBufferDataStorage.clear()}_storePlacedGlyphs(e,t,i,s,r){const n=Math.max(i-1,0),a=s===ie.VIEWPORT;let o,c,h,f,l,d,x,p,w,y,m;for(const g of t)o=a?0:Math.max(i+Y(g.minzoom),n),c=a?25:Math.min(i+Y(g.maxzoom),25),!(c<=o)&&(h=g.tl,f=g.tr,l=g.bl,d=g.br,x=g.labelAngle,p=g.minAngle,w=g.maxAngle,y=g.anchor,m=g.mosaicRect,this._glyphBufferDataStorage.has(g.page)||this._glyphBufferDataStorage.set(g.page,[]),this._glyphBufferDataStorage.get(g.page).push({glyphAnchor:[y.x,y.y],tl:[h.x,h.y],tr:[f.x,f.y],bl:[l.x,l.y],br:[d.x,d.y],xmin:m.x,ymin:m.y,xmax:m.x+m.width,ymax:m.y+m.height,labelAngle:x,minAngle:p,maxAngle:w,minLod:o,maxLod:c,placementLod:n,symbolInstance:e,ddAttributes:r}))}static _pushAnchors(e,t,i,s){i+=s;let r=0;const n=t.length-1;for(let l=0;l<n;l++)r+=A.distance(t[l],t[l+1]);let a=s||i;if(a*=.5,r<=a)return;const o=a/r;let c=0,h=-(i=r/Math.max(Math.round(r/i),1))/2;const f=t.length-1;for(let l=0;l<f;l++){const d=t[l],x=t[l+1],p=x.x-d.x,w=x.y-d.y,y=Math.sqrt(p*p+w*w);let m;for(;h+i<c+y;){h+=i;const g=(h-c)/y,I=ge(d.x,x.x,g),b=ge(d.y,x.y,g);m===void 0&&(m=Math.atan2(w,p)),e.push(new pe(I,b,m,l,o))}c+=y}}static _pushCenterAnchor(e,t){let i=0;const s=t.length-1;for(let o=0;o<s;o++)i+=A.distance(t[o],t[o+1]);const r=i/2;let n=0;const a=t.length-1;for(let o=0;o<a;o++){const c=t[o],h=t[o+1],f=h.x-c.x,l=h.y-c.y,d=Math.sqrt(f*f+l*l);if(r<n+d){const x=(r-n)/d,p=ge(c.x,h.x,x),w=ge(c.y,h.y,x),y=Math.atan2(l,f);return void e.push(new pe(p,w,y,o,0))}n+=d}}static _deviation(e,t,i){const s=(t.x-e.x)*(i.x-t.x)+(t.y-e.y)*(i.y-t.y),r=(t.x-e.x)*(i.y-t.y)-(t.y-e.y)*(i.x-t.x);return Math.atan2(r,s)}static _honorsTextMaxAngle(e,t,i,s,r){let n=0;const a=i/2;let o=new A(t.x,t.y),c=t.segment+1;for(;n>-a;){if(--c,c<0)return!1;n-=A.distance(e[c],o),o=e[c]}n+=A.distance(e[c],e[c+1]);const h=[];let f=0;const l=e.length;for(;n<a;){const d=e[c];let x,p=c;do{if(++p,p===l)return!1;x=e[p]}while(x.isEqual(d));let w,y=p;do{if(++y,y===l)return!1;w=e[y]}while(w.isEqual(x));const m=this._deviation(d,x,w);for(h.push({deviation:m,distToAnchor:n}),f+=m;n-h[0].distToAnchor>r;)f-=h.shift().deviation;if(Math.abs(f)>s)return!1;n+=A.distance(x,w),c=p}return!0}static _smoothVertices(e,t){if(t<=0)return e;let i=e.length;if(i<3)return e;const s=[];let r=0,n=0;s.push(0);for(let p=1;p<i;p++){const w=A.distance(e[p],e[p-1]);w>0&&(r+=w,s.push(r),n++,n!==p&&(e[n]=e[p]))}if(i=n+1,i<3)return e;t=Math.min(t,.2*r);const a=e[0].x,o=e[0].y,c=e[i-1].x,h=e[i-1].y,f=A.sub(e[0],e[1]);f.normalize(),e[0].x+=t*f.x,e[0].y+=t*f.y,f.assignSub(e[i-1],e[i-2]),f.normalize(),e[i-1].x+=t*f.x,e[i-1].y+=t*f.y,s[0]-=t,s[i-1]+=t;const l=[];l.push(new A(a,o));const d=1e-6,x=.5*t;for(let p=1;p<i-1;p++){let w=0,y=0,m=0;for(let g=p-1;g>=0;g--){const I=x+s[g+1]-s[p];if(I<0)break;const b=s[g+1]-s[g],_=s[p]-s[g]<x?1:I/b;if(_<d)break;const M=_*_,B=_*I-.5*M*b,P=_*b/t,D=e[g+1],L=e[g].x-D.x,T=e[g].y-D.y;w+=P/B*(D.x*_*I+.5*M*(I*L-b*D.x)-M*_*b*L/3),y+=P/B*(D.y*_*I+.5*M*(I*T-b*D.y)-M*_*b*T/3),m+=P}for(let g=p+1;g<i;g++){const I=x-s[g-1]+s[p];if(I<0)break;const b=s[g]-s[g-1],_=s[g]-s[p]<x?1:I/b;if(_<d)break;const M=_*_,B=_*I-.5*M*b,P=_*b/t,D=e[g-1],L=e[g].x-D.x,T=e[g].y-D.y;w+=P/B*(D.x*_*I+.5*M*(I*L-b*D.x)-M*_*b*L/3),y+=P/B*(D.y*_*I+.5*M*(I*T-b*D.y)-M*_*b*T/3),m+=P}l.push(new A(w/m,y/m))}return l.push(new A(c,h)),e[0].x=a,e[0].y=o,e[i-1].x=c,e[i-1].y=h,l}static _pushCentroid(e,t){const a=t.length-1;let o=0,c=0,h=0,f=t[0].x,l=t[0].y;f>4096&&(f=4096),f<0&&(f=0),l>4096&&(l=4096),l<0&&(l=0);for(let d=1;d<a;d++){let x=t[d].x,p=t[d].y,w=t[d+1].x,y=t[d+1].y;x>4096&&(x=4096),x<0&&(x=0),p>4096&&(p=4096),p<0&&(p=0),w>4096&&(w=4096),w<0&&(w=0),y>4096&&(y=4096),y<0&&(y=0);const m=(x-f)*(y-l)-(w-f)*(p-l);o+=m*(f+x+w),c+=m*(l+p+y),h+=m}o/=3*h,c/=3*h,isNaN(o)||isNaN(c)||e.push(new pe(o,c))}}U._bidiEngine=new ht;var W;(function(u){u[u.INITIALIZED=0]="INITIALIZED",u[u.NO_DATA=1]="NO_DATA",u[u.READY=2]="READY",u[u.MODIFIED=3]="MODIFIED",u[u.INVALID=4]="INVALID"})(W||(W={}));const Bt=8,Dt=14,At=5;class Lt{constructor(e,t,i,s,r){var h;if(this._pbfTiles={},this._tileClippers={},this._client=i,this._tile=t,r){this._styleLayerUIDs=new Set;for(const f of r)this._styleLayerUIDs.add(f)}this._styleRepository=s,this._layers=((h=this._styleRepository)==null?void 0:h.layers)??[];const[n,a,o]=t.tileKey.split("/").map(parseFloat);this._level=n;const c=Bt+Math.max((this._level-Dt)*At,0);for(const f of Object.keys(e)){const l=e[f];if(this._pbfTiles[f]=new st(new Uint8Array(l.protobuff),new DataView(l.protobuff)),l.refKey){const[d]=l.refKey.split("/").map(parseFloat),x=n-d;if(x>0){const p=(1<<x)-1,w=a&p,y=o&p;this._tileClippers[f]=new Xe(x,w,y,8,c)}}this._tileClippers[f]||(this._tileClippers[f]=new et)}}_canParseStyleLayer(e){return!this._styleLayerUIDs||this._styleLayerUIDs.has(e)}async parse(e){const t=rt(),i=this._initialize(e),{returnedBuckets:s}=i;this._processLayers(i),this._linkReferences(i),this._filterFeatures(i);const r=[],n=new Set,a=(h,f)=>{n.has(h)||(r.push({name:h,repeat:f}),n.add(h))},o={};for(const h of s)h.getResources(h.tileClipper,a,o);if(this._tile.status===W.INVALID)return[];const c=this._fetchResources(r,o,e);return Promise.all([...c,t]).then(()=>this._processFeatures(i.returnedBuckets))}_initialize(e){return{signal:e&&e.signal,sourceNameToTileData:this._parseTileData(this._pbfTiles),layers:this._layers,zoom:this._level,sourceNameToTileClipper:this._tileClippers,sourceNameToUniqueSourceLayerBuckets:{},sourceNameToUniqueSourceLayers:{},returnedBuckets:[],layerIdToBucket:{},referencerUIDToReferencedId:new Map}}_processLayers(e){const{sourceNameToTileData:t,layers:i,zoom:s,sourceNameToTileClipper:r,sourceNameToUniqueSourceLayerBuckets:n,sourceNameToUniqueSourceLayers:a,returnedBuckets:o,layerIdToBucket:c,referencerUIDToReferencedId:h}=e;for(let f=i.length-1;f>=0;f--){const l=i[f];if(!this._canParseStyleLayer(l.uid)||l.minzoom&&s<Math.floor(l.minzoom)||l.maxzoom&&s>=l.maxzoom||l.type===ee.BACKGROUND||!t[l.source]||!r[l.source])continue;const d=t[l.source],x=r[l.source],p=l.sourceLayer,w=d[p];if(w){let y=a[l.source];if(y||(y=a[l.source]=new Set),y.add(l.sourceLayer),l.refLayerId)h.set(l.uid,l.refLayerId);else{const m=this._createBucket(l);if(m){m.layerUIDs=[l.uid],m.layerExtent=w.extent,m.tileClipper=x;let g=n[l.source];g||(g=n[l.source]={});let I=g[p];I||(I=g[p]=[]),I.push(m),o.push(m),c[l.id]=m}}}}}_linkReferences(e){const{layerIdToBucket:t,referencerUIDToReferencedId:i}=e;i.forEach((s,r)=>{t[s]&&t[s].layerUIDs.push(r)})}_filterFeatures(e){const{signal:t,sourceNameToTileData:i,sourceNameToUniqueSourceLayerBuckets:s,sourceNameToUniqueSourceLayers:r}=e,n=10*this._level,a=10*(this._level+1),o=[],c=[];for(const h of Object.keys(r))r[h].forEach(f=>{o.push(f),c.push(h)});for(let h=0;h<o.length;h++){const f=c[h],l=o[h];if(!i[f]||!s[f])continue;const d=i[f][l],x=s[f][l];if(!x||x.length===0)continue;if(Ye(t))return;const p=d.getData();for(;p.nextTag(2);){const w=p.getMessage(),y=new yt(w,d);w.release();const m=y.values;if(m){const g=m._minzoom;if(g&&g>=a)continue;const I=m._maxzoom;if(I&&I<=n)continue}for(const g of x)g.pushFeature(y)}}}_fetchResources(e,t,i){const s=[],r=this._tile.getWorkerTileHandler();let n,a;e.length>0&&(n=r.fetchSprites(e,this._client,i),s.push(n));for(const o in t){const c=t[o];c.size>0&&(a=r.fetchGlyphs(this._tile.tileKey,o,c,this._client,i),s.push(a))}return s}_processFeatures(e){const t=e.filter(i=>i.hasFeatures()||this._canParseStyleLayer(i.layer.uid));for(const i of t)i.processFeatures(i.tileClipper);return t}_parseTileData(e){const t={};for(const i of Object.keys(e)){const s=e[i],r={};for(;s.next();)switch(s.tag()){case 3:{const n=s.getMessage(),a=new De(n);n.release(),r[a.name]=a;break}default:s.skip()}t[i]=r}return t}_createBucket(e){switch(e.type){case ee.BACKGROUND:return null;case ee.FILL:return this._createFillBucket(e);case ee.LINE:return this._createLineBucket(e);case ee.CIRCLE:return this._createCircleBucket(e);case ee.SYMBOL:return this._createSymbolBucket(e)}}_createFillBucket(e){return new It(e,this._level,this._tile.getWorkerTileHandler().getSpriteItems(),new gt(e.fillMaterial.getStride()),new te,new pt(e.outlineMaterial.getStride()),new te)}_createLineBucket(e){return new Mt(e,this._level,this._tile.getWorkerTileHandler().getSpriteItems(),new xt(e.lineMaterial.getStride()),new te)}_createCircleBucket(e){return new _t(e,this._level,this._tile.getWorkerTileHandler().getSpriteItems(),new mt(e.circleMaterial.getStride()),new te)}_createSymbolBucket(e){const t=this._tile;return new U(e,this._level,new Ge(e.iconMaterial.getStride()),new te,new Ge(e.textMaterial.getStride()),new te,t.placementEngine,t.getWorkerTileHandler())}}class Vt{constructor(e,t,i,s){this.status=W.INITIALIZED,this.placementEngine=new dt,this.tileKey=e,this.refKeys=t,this._workerTileHandler=i,this._styleRepository=s}release(){this.tileKey="",this.refKeys=null,this.status=W.INITIALIZED,this._workerTileHandler=null}async parse(e,t){const i=t&&t.signal;if(Je(i)){const h=()=>{i.removeEventListener("abort",h),this.status=W.INVALID};i.addEventListener("abort",h)}let s;const r={bucketsWithData:[],emptyBuckets:null};try{s=await this._parse(e,t)}catch(h){if(He(h))throw h;return{result:r,transferList:[]}}this.status=W.READY;const n=r.bucketsWithData,a=[];for(const h of s)if(h.hasFeatures()){const f=h.serialize();n.push(f)}else a.push(h.layer.uid);const o=[...n];let c=null;return a.length>0&&(c=Uint32Array.from(a),o.push(c.buffer)),r.emptyBuckets=c,{result:r,transferList:o}}setObsolete(){this.status=W.INVALID}getLayers(){return this._workerTileHandler.getLayers()}getWorkerTileHandler(){return this._workerTileHandler}async _parse(e,t){const i=e.sourceName2DataAndRefKey;return Object.keys(i).length===0?[]:(this.status=W.MODIFIED,new Lt(i,this,t.client,this._styleRepository,e.styleLayerUIDs).parse(t))}}class as{constructor(){this._spriteInfo={},this._glyphInfo={}}reset(){return this._spriteInfo={},this._glyphInfo={},Promise.resolve()}getLayers(){var e;return((e=this._styleRepository)==null?void 0:e.layers)??[]}async createTileAndParse(e,t){const{key:i}=e,s={};for(const n of Object.keys(e.sourceName2DataAndRefKey)){const a=e.sourceName2DataAndRefKey[n];s[n]=a.refKey}const r=new Vt(i,s,this,this._styleRepository);try{return await r.parse(e,t)}catch(n){if(r.setObsolete(),r.release(),!He(n))throw n;return null}}updateStyle(e){if(!e||e.length===0||!this._styleRepository)return;const t=this._styleRepository;for(const i of e){const s=i.type,r=i.data;switch(s){case oe.PAINTER_CHANGED:t.setPaintProperties(r.layer,r.paint);break;case oe.LAYOUT_CHANGED:t.setLayoutProperties(r.layer,r.layout);break;case oe.LAYER_REMOVED:t.deleteStyleLayer(r.layer);break;case oe.LAYER_CHANGED:t.setStyleLayer(r.layer,r.index);break;case oe.SPRITES_CHANGED:this._spriteInfo={}}}}setStyle(e){this._styleRepository=new ot(e),this._spriteInfo={},this._glyphInfo={}}fetchSprites(e,t,i){const s=[],r=this._spriteInfo;for(const n of e)r[n.name]===void 0&&s.push(n);return s.length===0?Promise.resolve():t.invoke("getSprites",s,{signal:i&&i.signal}).then(n=>{for(const a in n){const o=n[a];r[a]=o}})}getSpriteItems(){return this._spriteInfo}fetchGlyphs(e,t,i,s,r){const n=[];let a=this._glyphInfo[t];return a?i.forEach(o=>{a[o]||n.push(o)}):(a=this._glyphInfo[t]=[],i.forEach(o=>n.push(o))),n.length===0?Promise.resolve():s.invoke("getGlyphs",{tileID:e,font:t,codePoints:n},r).then(o=>{for(let c=0;c<o.length;c++)o[c]&&(a[c]=o[c])})}getGlyphItems(e){return this._glyphInfo[e]}}export{as as default};
