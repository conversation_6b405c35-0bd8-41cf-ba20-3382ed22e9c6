import{_ as se}from"./index-C9hz-UZb.js";import{d as re,r as N,bF as n,c as q,am as R,bB as T,a8 as le,bX as ie,s as W,b as H,o as ce,ah as de,bA as ue,ay as me,g as P,n as $,q as x,i as f,F as L,cs as J,bo as G,bR as U,p as v,aB as X,aJ as K,h as Q,G as Z,bh as ee,j as pe,dF as fe,dA as ye,J as he,al as ve,aj as be,bD as _e,C as ge}from"./index-r0dFAfgr.js";import{_ as De}from"./CardTable-rdWOL4_6.js";import{_ as Se}from"./CardSearch-CB_HNR-Q.js";import{b as te}from"./statisticalAnalysis-D5JxC4wJ.js";import{u as xe}from"./useStation-DJgnSZIA.js";import{p as Ce}from"./printUtils-C-AxhDcd.js";import{r as Ye}from"./data-D3PIONJl.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const Le={class:"wrapper"},we={class:"content-container"},ke={class:"list-layout"},Te={class:"table-container"},Pe={class:"date-selector"},Oe={class:"date-buttons"},Me={class:"content-container"},Ne={class:"chart-layout"},ze={class:"chart-container"},je={class:"date-selector"},qe={class:"date-buttons"},We=re({__name:"index",setup($e){const{getStationTree:ae}=xe(),o=N({type:"date",treeDataType:"Station",stationId:"",sumsRow:{},title:"",activeName:"list",chartOption:null,availableDates:[],selectedDate:"",chartData:null}),B=n().date(),I=q(),w=q(),O=q();R(()=>o.activeName,()=>{o.activeName==="echarts"&&T(()=>{setTimeout(()=>{j()},100)})}),R(()=>{var e,t;return(t=(e=w.value)==null?void 0:e.queryParams)==null?void 0:t.type},e=>{var t;e&&((t=y.currentProject)!=null&&t.id)&&T(()=>{M()})});const y=N({data:[],currentProject:{}}),z=N({defaultParams:{type:"day",year:[n().format(),n().format()],month:[n().format(),n().format()],day:[n().date(B-6).format("YYYY-MM-DD"),n().date(B).format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:le(()=>y.data),label:"站点选择",onChange:e=>{var a;const t=ie(y.data,"children","id",e);t&&t.id&&(y.currentProject=t,o.treeDataType=((a=t.data)==null?void 0:a.type)||"Station",o.stationId=t.id,T(()=>{M()}))}},{type:"radio-button",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型"},{hidden:!0,type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="day"}},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>M(),svgIcon:W(ve)},{text:"导出",perm:!0,type:"warning",svgIcon:W(be),click:()=>oe()},{perm:!0,text:"打印",type:"success",svgIcon:W(_e),click:()=>ne()}]}]}),b=N({loading:!1,dataList:[],columns:[],operations:[],operationWidth:"150px",pagination:{hide:!0}}),M=()=>{var C,_,D;b.loading=!0;const e=(C=y.currentProject)==null?void 0:C.id,t=((_=w.value)==null?void 0:_.queryParams)||{},a=t[t.type],l=Ye.find(u=>u.value===t.type);if(!e){b.loading=!1,console.log("等待站点数据加载...");return}if(!a||!a[0]||!a[1]){b.loading=!1,console.log("等待时间范围设置...");return}o.title=(((D=y.currentProject)==null?void 0:D.label)||"未选择站点")+"吨水电耗分析("+((l==null?void 0:l.label)||"日报")+n(a[0]).format((l==null?void 0:l.data)||"YYYY-MM-DD")+"至"+n(a[1]).format((l==null?void 0:l.data)||"YYYY-MM-DD")+")";const d={stationId:e,queryType:t.type,start:n(a[0]).startOf(t.type).valueOf(),end:n(a[1]).endOf(t.type).valueOf()};te(d).then(u=>{const s=u.data.data,g=s.flowList||[],i=s.energyList||[],k=s.unitConsumption||[],m=[];for(let c=0;c<g.length;c++)t.type==="day"?m.push(c+"时"):t.type==="month"?m.push(c+1+"日"):m.push(c+1+"月");const Y=[{prop:"time",label:"时间",minWidth:120,align:"center"},{prop:"unitConsumption",label:"吨水电耗",unit:"(kWh/m³)",minWidth:180,align:"center"},{prop:"totalFlow",label:"取水量",unit:"(m³)",minWidth:180,align:"center"},{prop:"energy",label:"用电量",unit:"(kW)",minWidth:180,align:"center"}];b.columns=Y;const S=m.map((c,p)=>{var h,r,V;return{time:c,unitConsumption:((h=k[p])==null?void 0:h.value)||0,totalFlow:((r=g[p])==null?void 0:r.value)||0,energy:((V=i[p])==null?void 0:V.value)||0}});if(b.dataList=S,b.loading=!1,o.chartData=s,t.type==="day"){const c=n(a[0]),p=n(a[1]),h=[];let r=c;for(;r.isBefore(p)||r.isSame(p);)h.push(r.format("YYYY-MM-DD")),r=r.add(1,"day");o.availableDates=h}else if(t.type==="month"){const c=n(a[0]),p=n(a[1]),h=[];let r=c;for(;r.isBefore(p)||r.isSame(p);)h.push(r.format("YYYY-MM")),r=r.add(1,"month");o.availableDates=h}else{const c=n(a[0]),p=n(a[1]),h=[];let r=c;for(;r.isBefore(p)||r.isSame(p);)h.push(r.format("YYYY")),r=r.add(1,"year");o.availableDates=h}o.selectedDate=o.availableDates[0]||"",o.activeName==="echarts"&&T(()=>{setTimeout(()=>{o.availableDates.length>1?E(o.selectedDate):j()},200)})}).catch(u=>{console.error("获取数据失败:",u),b.loading=!1,H.error("获取数据失败，请稍后重试")})},oe=()=>{var e;(e=I.value)==null||e.exportTable()},ne=()=>{Ce({title:o.title,data:b.dataList,titleList:b.columns})},j=()=>{var g;if(!o.chartData)return;const e=o.chartData,a=(((g=w.value)==null?void 0:g.queryParams)||{}).type||"day",l=e.flowList||[],d=[];for(let i=0;i<l.length;i++)a==="day"?d.push(i+"时"):a==="month"?d.push(i+1+"日"):d.push(i+1+"月");const C=(e.unitConsumption||[]).map(i=>i.value||0),_=(e.flowList||[]).map(i=>i.value||0),D=(e.energyList||[]).map(i=>i.value||0);let u="";a==="day"?u=`${n(o.selectedDate).format("M月D日")} ${y.currentProject.label||""}生产图`:a==="month"?u=`${n(o.selectedDate).format("YYYY年M月")} ${y.currentProject.label||""}生产图`:u=`${n(o.selectedDate).format("YYYY年")} ${y.currentProject.label||""}生产图`;const s={title:{text:u,left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},formatter:function(i){let k=`${i[0].axisValue}<br/>`;return i.forEach(m=>{let Y="";m.seriesName.includes("吨水电耗")?Y="kWh/m³":m.seriesName.includes("取水量")?Y="m³":m.seriesName.includes("用电量")&&(Y="kW"),k+=`${m.marker} ${m.seriesName}: ${m.value} ${Y}<br/>`}),k}},legend:{top:30,data:["吨水电耗","取水量","用电量"],textStyle:{fontSize:12}},grid:{left:"8%",right:"8%",bottom:"10%",top:"20%",containLabel:!0},xAxis:{type:"category",data:d,name:a==="day"?"时间":a==="month"?"日期":"月份",nameTextStyle:{fontSize:12},axisLabel:{fontSize:11,rotate:0}},yAxis:[{type:"value",name:"取水量(m³)/用电量(kW)",position:"left",nameTextStyle:{fontSize:12},axisLabel:{fontSize:11,formatter:"{value}"}},{type:"value",name:"吨水电耗(kWh/m³)",position:"right",nameTextStyle:{fontSize:12},axisLabel:{fontSize:11,formatter:"{value}"}}],series:[{name:"取水量",type:"bar",yAxisIndex:0,data:_,itemStyle:{color:"#91CC75"},barWidth:"20%"},{name:"用电量",type:"bar",yAxisIndex:0,data:D,itemStyle:{color:"#C0C0C0"},barWidth:"20%"},{name:"吨水电耗",type:"line",yAxisIndex:1,data:C,smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:3,color:"#5CB85C"},itemStyle:{color:"#5CB85C"}}]};o.chartOption=s,T(()=>{setTimeout(()=>{var i;(i=O.value)==null||i.resize()},200)})},A=e=>{o.selectedDate=e,E(e)},E=e=>{var _,D;const t=(_=y.currentProject)==null?void 0:_.id,a=((D=w.value)==null?void 0:D.queryParams)||{};if(!t||!e)return;let l,d;a.type==="day"?(l=n(e).startOf("day").valueOf(),d=n(e).endOf("day").valueOf()):a.type==="month"?(l=n(e).startOf("month").valueOf(),d=n(e).endOf("month").valueOf()):(l=n(e).startOf("year").valueOf(),d=n(e).endOf("year").valueOf());const C={stationId:t,queryType:a.type,start:l,end:d};te(C).then(u=>{const s=u.data.data;o.chartData=s;const g=s.flowList||[],i=s.energyList||[],k=s.unitConsumption||[],m=[];for(let S=0;S<g.length;S++)a.type==="day"?m.push(S+"时"):a.type==="month"?m.push(S+1+"日"):m.push(S+1+"月");const Y=m.map((S,c)=>{var p,h,r;return{time:S,unitConsumption:((p=k[c])==null?void 0:p.value)||0,totalFlow:((h=g[c])==null?void 0:h.value)||0,energy:((r=i[c])==null?void 0:r.value)||0}});b.dataList=Y,o.activeName==="echarts"&&j()}).catch(u=>{console.error("获取选中日期数据失败:",u),H.error("获取数据失败，请稍后重试")})},F=e=>{const t=e.split("-");return t.length===3?`${t[1]}-${t[2]}`:e};return ce(async()=>{var a,l;const e=await ae("水源地");y.data=e;const t=de(y.data);t&&t.id&&(y.currentProject=t,o.treeDataType=((a=t.data)==null?void 0:a.type)||"Station",o.stationId=t.id),z.defaultParams={...z.defaultParams,treeData:y.currentProject},(l=w.value)==null||l.resetForm(),T(()=>{setTimeout(()=>{M()},100)}),window.addEventListener("resize",()=>{setTimeout(()=>{var d;(d=O.value)==null||d.resize()},100)})}),ue(()=>{window.removeEventListener("resize",()=>{var e;(e=O.value)==null||e.resize()})}),(e,t)=>{const a=Se,l=fe,d=ye,C=De,_=he,D=me("VChart"),u=se;return P(),$("div",Le,[x(a,{ref_key:"cardSearch",ref:w,config:f(z)},null,8,["config"]),x(u,{class:"card",title:f(o).activeName==="list"?"吨水电耗分析":"吨水电耗图表"},{query:L(()=>[x(d,{modelValue:f(o).activeName,"onUpdate:modelValue":t[0]||(t[0]=s=>f(o).activeName=s)},{default:L(()=>[x(l,{label:"echarts"},{default:L(()=>[x(f(J),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),x(l,{label:"list"},{default:L(()=>[x(f(J),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:L(()=>[G(v("div",we,[v("div",ke,[v("div",Te,[x(C,{id:"print",ref_key:"refTable",ref:I,class:"card-table",config:f(b)},null,8,["config"])]),v("div",Pe,[t[1]||(t[1]=v("div",{class:"selector-title"},"选择日期",-1)),v("div",Oe,[(P(!0),$(X,null,K(f(o).availableDates,s=>(P(),Q(_,{key:s,type:f(o).selectedDate===s?"primary":"default",size:"small",onClick:g=>A(s),class:"date-btn"},{default:L(()=>[Z(ee(F(s)),1)]),_:2},1032,["type","onClick"]))),128))])])])],512),[[U,f(o).activeName==="list"]]),G(v("div",Me,[v("div",Ne,[v("div",ze,[x(D,{ref_key:"refChart",ref:O,theme:f(pe)().isDark?"dark":"light",option:f(o).chartOption,class:"line-chart"},null,8,["theme","option"])]),v("div",je,[t[2]||(t[2]=v("div",{class:"selector-title"},"选择日期",-1)),v("div",qe,[(P(!0),$(X,null,K(f(o).availableDates,s=>(P(),Q(_,{key:s,type:f(o).selectedDate===s?"primary":"default",size:"small",onClick:g=>A(s),class:"date-btn"},{default:L(()=>[Z(ee(F(s)),1)]),_:2},1032,["type","onClick"]))),128))])])])],512),[[U,f(o).activeName==="echarts"]])]),_:1},8,["title"])])}}}),Ue=ge(We,[["__scopeId","data-v-dbd53f3b"]]);export{Ue as default};
