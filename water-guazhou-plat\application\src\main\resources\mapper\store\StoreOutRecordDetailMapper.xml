<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.StoreOutRecordDetailMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           num,
                           store_out_count_by_store_out_item_id(store_out_record_detail.id)             as amount,
                           device_rest_storage_count_by_serial_id_and_shelves_id(serial_id, shelves_id, tenant_id) as count,
                           shelves_id,
                           store_house_get_id_by_shelves_id(shelves_id)                                    store_id,
                           main_id,
                           serial_id,
                           tenant_id<!--@sql from store_out_record_detail -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetailResponse">
        <result column="id" property="id"/>
        <result column="num" property="num"/>
        <result column="amount" property="amount"/>
        <result column="count" property="count"/>
        <result column="shelves_id" property="shelvesId"/>
        <result column="store_id" property="storeId"/>
        <result column="main_id" property="mainId"/>
        <result column="tenant_id" property="tenantId"/>
        <association property="deviceInfoResponse"
                     column="{serialId=serial_id,tenantId=tenant_id}"
                     select="org.thingsboard.server.dao.sql.deviceType.DeviceMapper.getInfoBySerialId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from store_out_record_detail
        <where>
            <if test="mainId != null and mainId != ''">
                and main_id = #{mainId}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update store_out_record_detail
        <set>
            <if test="num != null">
                num = greatest(#{num}, store_out_item_exact_out_count(#{id})),
            </if>
            <if test="mainId != null">
                main_id = #{mainId},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO store_out_record_detail(id,
                                            num,
                                            main_id,
                                            serial_id,
                                            shelves_id,
                                            out_time,
                                            tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             greatest(#{element.num}, store_out_item_exact_out_count(#{element.id})),
             #{element.mainId},
             #{element.serialId},
             #{element.shelvesId},
             #{element.outTime},
             #{element.tenantId})
        </foreach>
        on conflict(id) do update set
        num     = greatest(excluded.num, store_out_item_exact_out_count(store_out_record_detail.id))
    </insert>

    <update id="updateAll">
        update store_out_record_detail ord
        <set>
            num = greatest(valueTable.num, store_out_item_exact_out_count(ord.id))
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.num})
        </foreach>
        ) as valueTable(id, num)
        where ord.id = valueTable.id
    </update>

    <delete id="removeAllByMainOnIdNotIn">
        delete
        from store_out_record_detail
        where main_id = #{id}
        <if test="idList != null and idList.size() != 0">
            and id not in
            <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="differentIdListTo" resultType="java.lang.String">
        select id
        from store_out_record_detail where main_id = #{mainId}
                                       and id not in (
        <foreach collection="excludeList" item="element" separator=",">
            #{element}
        </foreach>
        )
    </select>
</mapper>