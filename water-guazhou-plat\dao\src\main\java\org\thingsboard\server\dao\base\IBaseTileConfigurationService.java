package org.thingsboard.server.dao.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTileConfigurationPageRequest;

import java.util.List;

/**
 * 公共管理平台-瓦片数据配置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IBaseTileConfigurationService {
    /**
     * 查询公共管理平台-瓦片数据配置
     *
     * @param id 公共管理平台-瓦片数据配置主键
     * @return 公共管理平台-瓦片数据配置
     */
    public BaseTileConfiguration selectBaseTileConfigurationById(String id);

    /**
     * 查询公共管理平台-瓦片数据配置列表
     *
     * @param baseTileConfiguration 公共管理平台-瓦片数据配置
     * @return 公共管理平台-瓦片数据配置集合
     */
    public IPage<BaseTileConfiguration> selectBaseTileConfigurationList(BaseTileConfigurationPageRequest baseTileConfiguration);

    /**
     * 新增公共管理平台-瓦片数据配置
     *
     * @param baseTileConfiguration 公共管理平台-瓦片数据配置
     * @return 结果
     */
    public int insertBaseTileConfiguration(BaseTileConfiguration baseTileConfiguration);

    /**
     * 修改公共管理平台-瓦片数据配置
     *
     * @param baseTileConfiguration 公共管理平台-瓦片数据配置
     * @return 结果
     */
    public int updateBaseTileConfiguration(BaseTileConfiguration baseTileConfiguration);

    /**
     * 批量删除公共管理平台-瓦片数据配置
     *
     * @param ids 需要删除的公共管理平台-瓦片数据配置主键集合
     * @return 结果
     */
    public int deleteBaseTileConfigurationByIds(List<String> ids);

    /**
     * 删除公共管理平台-瓦片数据配置信息
     *
     * @param id 公共管理平台-瓦片数据配置主键
     * @return 结果
     */
    public int deleteBaseTileConfigurationById(String id);

    /**
     * 获取所有的瓦片数据配置数据
     * @return
     */
    public List<BaseTileConfiguration> selectAllBaseTileConfiguration();
}
