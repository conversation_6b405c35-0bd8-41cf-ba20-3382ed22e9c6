package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.DispatchMethod;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.DispatchMethodPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.DispatchMethodSaveRequest;

public interface DispatchMethodService {
    /**
     * 分页条件查询调度方案
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DispatchMethod> findAllConditional(DispatchMethodPageRequest request);

    /**
     * 保存调度方案
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    DispatchMethod save(DispatchMethodSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(DispatchMethod entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 切换启用状态
     *
     * @param id      调度方案id
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean switchEnabled(String id, boolean enabled);

}
