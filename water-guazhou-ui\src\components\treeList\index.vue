<template>
  <div
    v-loading.body="treeData.data && treeData.loading"
    class="tree-list-container"
  >
    <div :class="{ headerBoxBorder: !treeData.noTitle }">
      <p v-if="!treeData.noTitle" class="tree-list-title">
        <i class="iconfont"></i> {{ treeData.title }}
      </p>
      <div v-if="treeData.selectFilter">
        <el-select
          v-if="treeData.selectFilter"
          v-model="queryParams[treeData.selectFilter.key]"
          :multiple="treeData.selectFilter.multiple"
          collapse-tags
          :placeholder="'请选择' + treeData.selectFilter.label"
          class="tree-filter-box"
          :style="treeData.selectFilter.style"
          style="width: 100%; margin: 0px 0 16px"
          :filterable="treeData.selectFilter.search"
          @change="treeData.selectFilter.handleChange"
        >
          <el-option
            v-for="option of treeData.selectFilter.options"
            :key="option.value"
            :value="option.value"
            :label="option.label"
            style="box-sizing: border-box"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div v-if="treeData.switch" class="switchBox">
      <span
        v-for="item in treeData.switch.options"
        :key="item.name"
        :class="{ active: treeData.switch.curVal === item.val }"
        @click="treeData.switch.handle(item)"
        >{{ item.name }}</span
      >
    </div>
    <el-input
      v-if="treeData.isFilterTree"
      v-model="filterText"
      placeholder="搜索关键字"
      class="tree-filter-box"
    >
    </el-input>
    <el-button
      v-if="treeData.allowNew && treeData.btnPerms.addBtn"
      class="tree-o-btn"
      type="primary"
      size="large"
      :disabled="customer_disable"
      @click="handleCreate(treeData)"
    >
      {{
        treeData.operationText
          ? treeData.operationText[0] || '新建项目'
          : '新建项目'
      }}
    </el-button>
    <div
      v-if="operationVsible"
      class="operation-btns"
      :class="{ 'active-operation': treeData.currentProject }"
    >
      <el-button
        v-if="treeData.btnPerms.addBtn && !treeData.btnPerms.noChild"
        :disabled="customer_disable"
        :style="{ width: treeData.nodeBtnWidth || '' }"
        type="warning"
        size="large"
        @click="add(treeData.currentProject)"
      >
        {{
          treeData.operationText ? treeData.operationText[1] || '子项' : '子项'
        }}
      </el-button>
      <el-button
        v-if="treeData.btnPerms.editBtn"
        :disabled="customer_disable"
        :style="{ width: treeData.nodeBtnWidth || '' }"
        class="add-child-blue"
        size="large"
        @click="edit(treeData.currentProject)"
      >
        编辑
      </el-button>
      <el-button
        v-if="treeData.btnPerms.delBtn"
        :disabled="customer_disable"
        :style="{ width: treeData.nodeBtnWidth || '' }"
        size="large"
        type="danger"
        @click="deleteHandle(treeData.currentProject)"
      >
        删除
      </el-button>
    </div>
    <p
      v-if="treeData.showAll"
      class="all-project"
      :class="{ 'active-all-project': treeData.activeAll }"
      @click="treeData.showAllProject"
    >
      <i class="iconfont icon-xiangmu1"></i> 所有项目
    </p>
    <div
      class="tree-list-box"
      :class="{
        'tree-btn-list-box': operationVsible,
        'tree-f-list-box':
          !operationVsible && !treeData.noTitle && treeData.isFilterTree,
        'tree-all-list': treeData.showAll
      }"
    >
      <el-tree
        ref="tree"
        class="filter-tree"
        accordion
        highlight-current
        node-key="id"
        default-expand-all
        :data="treeData.data"
        :props="treeData.defaultProps"
        :filter-node-method="filterNode"
        :default-checked-keys="[1]"
        :default-expanded-keys="treeData.expandNodeId"
        @node-click="treeNodeHandleClick"
      >
        <template #default="{ node, data }">
          <div
            class="custom-tree-node"
            :class="{
              'active-tree-node':
                treeData.currentProject &&
                treeData.currentProject.id === data.id,
              'disabled-node': data.disabled
            }"
          >
            <p class="c-t-label">
              <i
                class="iconfont project-icon"
                :class="{
                  'icon-xiangmu1': !node.isHost,
                  'icon-wangguan': node.isHost
                }"
              ></i>
              <span class="c-t-name">{{ node.label }}</span>
            </p>
            <span
              v-if="operationVsible && !treeData.btnPerms.noChild"
              class="hover-button"
            >
              <Plus
                v-if="treeData.btnPerms.addBtn"
                style="color: #32d1db"
                :size="14"
                @click.stop="add(data, $parent, node)"
              ></Plus>
              <Delete
                v-if="treeData.btnPerms.delBtn"
                style="color: #f56c6c"
                :size="14"
                @click.stop="deleteHandle(data, $parent, node)"
              ></Delete>
              <Edit
                v-if="treeData.btnPerms.editBtn"
                style="color: #1f9fff"
                :size="14"
                @click.stop="edit(data, $parent, node)"
              ></Edit>
            </span>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script>
import { Plus, Delete, Edit } from '@element-plus/icons-vue';
import useGlobal from '@/hooks/global/useGlobal';
import { useAppStore, useUserStore } from '@/store';

const { $messageInfo, $confirm } = useGlobal();
export default {
  name: 'TreeList',
  components: { Plus, Delete, Edit },
  props: {
    treeData: {
      type: Object,
      default() {
        return {
          title: '区域划分',
          data: Array,
          isFilterTree: Boolean,
          // filter: Function,
          showAll: Boolean, // 全部项目
          allowCreate: Boolean,
          btnPerms: Object,
          allowNew: Boolean,
          treeNodeHandleClick: Function,
          expandNodeId: Array,
          defaultProps: Object,
          clickAddOrEdit: Function, // 点击增加或者编辑
          allowAdd: Boolean,
          allowEdit: Boolean,
          allowDelete: Boolean,
          projectDelete: Function,
          currentProject: Object,
          selectFilter: Object
        };
      }
    }
  },

  data() {
    return {
      filterText: '',
      customer_disable: false,
      queryParams: {}
    };
  },
  computed: {
    textColor() {
      return useAppStore().theme === '252C47' ? '#BCC3DF' : '#606266';
    },
    operationVsible() {
      if (!this.treeData.btnPerms) return false;
      for (const key in this.treeData.btnPerms) {
        if (this.treeData.btnPerms[key]) {
          return true && this.treeData.allowCreate;
        }
      }
      return false;
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    if (useUserStore().roles[0] === 'CUSTOMER_USER') {
      if (this.treeData.allowNew && this.treeData.allowCreate) {
        this.customer_disable = true;
      }
    } else {
      this.customer_disable = false;
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$nextTick(() => {
        // eslint-disable-next-line vue/no-mutating-props
        this.treeData.treeRef = this.$refs.tree;
      });
    });
    if (this.treeData.selectFilter) {
      this.queryParams = this.treeData.selectFilter.defaultVal || {};
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data[this.treeData.defaultProps.label].indexOf(value) !== -1;
    },
    handleCreate() {
      this.treeData.clickAddOrEdit(null, 'created');
    },
    add(data) {
      if (this.customer_disable) {
        $messageInfo('暂无权限');
      } else if (data) this.treeData.clickAddOrEdit(data, 'add');
    },
    edit(data) {
      if (this.customer_disable) {
        $messageInfo('暂无权限');
      } else if (data) this.treeData.clickAddOrEdit(data, 'edit');
    },
    deleteHandle(data) {
      if (this.customer_disable) {
        $messageInfo('暂无权限');
      } else {
        const msg = data.name
          ? '确定要删除' + data.name + '吗?'
          : '确定要删除吗?';
        $confirm(msg, '删除提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            if (data) this.treeData.projectDelete(data.id, this);
          })
          .catch((e) => {
            console.log(e);
            //
          });
      }
    },
    treeNodeHandleClick(data, b, vue) {
      this.treeData.treeNodeHandleClick(data, b, vue, this.treeData);
    }
  }
};
</script>

<style lang="scss" scoped>
.tree-list-container {
  height: 100%;
  width: 100%;
  background-color: transparent;
  .tree-list-title {
    height: 56px;
    line-height: 56px;
    margin: -1px -1px 0 0;
    padding: 0 16px;
    //  ;
    font-size: 18px;
    color: #9097c0;
    font-weight: 400;
    // border-bottom: 1px solid #4e5166;
  }
  .switchBox {
    // padding: 0 10px;
    margin: 20px 10px 10px;
    display: flex;
    height: 30px;
    border-radius: 5px;
    border: 1px solid #404354;
    span {
      // border-right: 1px solid #404354;
      display: block;
      flex: 1;
      color: #fff;
      text-align: center;
      line-height: 30px;
      &.active {
        background-color: #3e8ef7;
      }
      &:hover {
        cursor: pointer;
      }
      &:last-of-type {
        border-right: none;
      }
    }
  }
  .tree-filter-box {
    padding: 0 16px;
    margin-top: 20px;
    height: 48px;
    .el-input-group__append {
      background: #10bc1e;
      color: #fff;
    }
  }

  .add-child-blue {
    border-color: #40b5e6;
    background-color: #40b5e6;

    &:hover,
    &:active,
    &:focus {
      background-color: #32d1db;
    }
  }

  .tree-o-btn {
    width: calc(100% - 32px);
    margin: 16px 16px 0 16px;
  }
  .operation-btns {
    margin: 16px 16px 0 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .el-button {
      width: 100%;
    }
  }
  .all-project {
    height: 40px;
    line-height: 40px;
    padding-left: 15px;
    width: calc(100% - 20px);
    margin: 10px 10px 0 10px;
    border-bottom: 1px solid #23729f;
    cursor: pointer;
    .icon-xiangmu1 {
      color: #25a1cf;
      margin-right: 10px;
    }
    &:hover {
      background: rgba(9, 17, 40, 0.5);
      box-shadow:
        0 2px 10px rgba(0, 0, 0, 0.2),
        inset 0 0 20px 2px rgba(0, 149, 255, 0.6);
    }
  }
  .active-all-project {
    color: #02feff;
    font-weight: 600;
    background: rgba(9, 17, 40, 0.5);
    border: 1px solid #23729f;
    box-shadow:
      0 2px 10px rgba(0, 0, 0, 0.2),
      inset 0 0 20px 2px rgba(0, 149, 255, 0.6);
    .icon-xiangmu1 {
      color: #526dff;
    }
  }
  .tree-list-box {
    height: calc(100% - 65px);
    margin-top: 10px;
  }
  .tree-btn-list-box {
    height: calc(100% - 256px);
  }
  .tree-f-list-box {
    height: calc(100% - 150px);
  }
  .tree-all-list {
    height: calc(100% - 115px);
  }
}
.headerBoxBorder {
  border-bottom: 1px solid #666;
}
:deep(.el-tree) {
  height: 100%;
  padding: 0 16px;
  overflow-y: auto;
  transition: height ease 1s;
  .el-tree-node {
    height: auto;

    &.is-current > .el-tree-node__content,
    .el-tree-node__content:hover {
      background-color: transparent;
      background-image: linear-gradient(270deg, #2980b9 0%, #6dd5fa 100%);
    }

    .el-tree-node__content {
      height: auto;
      border-radius: 5px;
    }
  }
}
.custom-tree-node {
  flex: 1;
  height: 40px;
  display: flex;
  font-size: 14px;
  padding-right: 8px;
  align-items: center;
  position: relative;
  justify-content: space-between;
  &:hover {
    .hover-button {
      width: 78px;
      height: 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
    }
  }
  .hover-button {
    right: 0;
    z-index: 10;
    font-size: 14px;
    padding: 4px 5px;
  }
}
</style>
