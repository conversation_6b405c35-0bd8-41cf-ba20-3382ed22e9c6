package org.thingsboard.server.dao.util.imodel.query.store;

import org.thingsboard.server.dao.model.sql.store.Store;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class StorePageRequest extends AdvancedPageableQueryEntity<Store, StorePageRequest> {
    private String id;

    // 仓库编码
    private String code;

    // 仓库名称
    private String name;

    // 管理员ID
    private String managerId;

    // 管理员部门Id
    private String managerDepartmentId;
}
