import{d as pe,a6 as me,bF as f,r as h,c as k,a8 as fe,s as Y,bB as N,D as z,am as be,ay as ge,g as O,h as K,F as p,q as c,n as M,p as w,an as D,i as q,cs as X,c5 as ve,dt as he,dF as _e,dA as ye,aq as Te,al as Z,aj as ee,C as ke}from"./index-r0dFAfgr.js";import{_ as xe}from"./CardSearch-CB_HNR-Q.js";import{_ as Ce}from"./index-C9hz-UZb.js";import{l as ae,g as Oe}from"./echart-Bd1EZNhy.js";import{u as Se}from"./useStation-DJgnSZIA.js";import{g as Le,d as Ie,s as Ne}from"./zhandian-YaGuQZe6.js";import{s as we,a as De}from"./headwaterMonitoring-BgK7jThW.js";import"./Search-NSrhrIa_.js";const Ae=[{label:"0时",value:0},{label:"1时",value:1},{label:"2时",value:2},{label:"3时",value:3},{label:"4时",value:4},{label:"5时",value:5},{label:"6时",value:6},{label:"7时",value:7},{label:"8时",value:8},{label:"9时",value:9},{label:"10时",value:10},{label:"11时",value:11},{label:"12时",value:12},{label:"13时",value:13},{label:"14时",value:14},{label:"15时",value:15},{label:"16时",value:16},{label:"17时",value:17},{label:"18时",value:18},{label:"19时",value:19},{label:"20时",value:20},{label:"21时",value:21},{label:"22时",value:22},{label:"23时",value:23}],Ge=[[{label:"监测点名称",prop:"name",value:"-"},{label:"类型",prop:"type",value:"-"}],[{label:"安装地址",prop:"address",value:"-"},{label:"经纬度",prop:"location",value:"-"}],[{label:"压力",prop:"pressure",value:"-"}]],Re={key:0,class:"content"},Ye={class:"top"},Me={class:"table-box"},Ve={class:"bottom"},Pe={key:1},Ee={key:0},Fe=pe({__name:"stationDetailMonitoring",props:{stationId:{},stationDetail:{}},emits:["hiddenLoading","update:model-value"],setup(te,{emit:le}){const{getStationAttrGroups:re}=Se(),oe=le,B=me(),x=te,A=f().date(),t=h({activeName:"status",chartOption:null,chartOption1:null,gaugeOption:null,searchActiveName:"echarts",groupTab:"",groupStation:[],currentGroupTabs:[],attributeOptions:[],rows:[],data:null,detailData:{},columns:Ge,stationId:x.stationId,tableDataList:[]}),H=k(),j=k(),J=k(),S=k(),V=k(),P=k(),G=k(),L=k();let C=h([]),n=h([]);const T=h({loading:!0,currentRow:[],currentRowKey:"property",highlightCurrentRow:!0,dataList:[],columns:[{prop:"propertyName",label:"检测项名称"},{prop:"value",label:"检测项数据"},{prop:"collectionTime",label:"采集时间",formatter:e=>e.collectionTime>0?f(e.collectionTime).format("YYYY-MM-DD HH:mm:ss"):"-"}],operations:[],pagination:{hide:!0},handleRowClick:e=>{T.currentRow=e,F()}}),_=h({loading:!0,dataList:[],indexVisible:!0,columns:[{prop:"name1",label:"报警描述"},{prop:"name2",label:"报警时间"}],operations:[],pagination:{page:1,limit:20,total:0,layout:"total, prev, pager, next",handleSize:e=>{_.pagination.limit=e},refreshData:({page:e,size:a})=>{_.pagination.page=e,_.pagination.limit=a,_.dataList=C.slice((e-1)*a,e*a)}}}),g=h({loading:!0,dataList:[],indexVisible:!0,columns:[{prop:"name1",label:"报警描述"},{prop:"name2",label:"报警时间"},{prop:"name2",label:"处理状态"}],operations:[],pagination:{page:1,limit:20,total:0,layout:"total, prev, pager, next, sizes, jumper",handleSize:e=>{g.pagination.limit=e},refreshData:({page:e,size:a})=>{var s;g.pagination.page=e,g.pagination.limit=a,g.dataList=(s=n==null?void 0:n.tableDataList)==null?void 0:s.slice((e-1)*a,e*a)}}}),m=h({defaultParams:{date:[f().date(A-2),f().date(A)],filterStart:[0,23],group:"",attributeId:""},filters:[{type:"daterange",label:"选中日期",field:"date",clearable:!1},{label:"时间",type:"range",rangeType:"select",field:"filterStart",options:JSON.parse(JSON.stringify(Ae)),startPlaceHolder:"0时",endPlaceHolder:"23时",startOptionDisabled:(e,a)=>a&&Number(a)<e.value,endOptionDisabled:(e,a)=>a&&e.value<=Number(a)},{label:"监测组",labelWidth:60,field:"group",type:"select",clearable:!1,options:[],onChange:e=>Q(e)},{label:"曲线类型",labelWidth:70,field:"attributeId",type:"select",clearable:!1,options:[],hidden:fe(()=>t.searchActiveName==="list")}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{g.pagination.page=1,U()},svgIcon:Y(Z)},{perm:!0,type:"warning",text:"导出",svgIcon:Y(ee),hide:()=>t.searchActiveName!=="list",click:()=>{var e;(e=H.value)==null||e.exportTable()}}]}]}),ne=h({defaultParams:{date:[f().date(A-2).format("YYYY-MM-DD"),f().date(A).format("YYYY-MM-DD")]},filters:[{type:"daterange",label:"选择时间",field:"date",clearable:!1},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>E(),svgIcon:Y(Z)},{perm:!0,type:"warning",text:"导出",svgIcon:Y(ee),click:()=>{var e;(e=J.value)==null||e.exportTable()}}]}]}),se=h({type:"tabs",tabType:"simple",width:"100%",tabs:[{label:"当前状态",value:"status"},{label:"数据查询",value:"search"},{label:"报警信息",value:"alarm"}],handleTabClick:e=>{console.log(e.props.name),t.activeName=e.props.name,t.activeName==="search"?N(()=>{var s,r;console.log(t.currentGroupTabs);const a=(s=m.filters)==null?void 0:s.find(l=>l.field==="group");a.options=t.currentGroupTabs,console.log("cardSearch.value?.queryParams",m.defaultParams),m.defaultParams={...m.defaultParams,group:t.currentGroupTabs[0].value},(r=L.value)==null||r.resetForm(),Q(t.currentGroupTabs[0].value)}):t.activeName==="alarm"&&N(()=>{E("range")})}}),Q=e=>{var r,l;const a=t.currentGroupTabs.find(o=>o.value===e),s=(r=m.filters)==null?void 0:r.find(o=>(o==null?void 0:o.field)==="attributeId");s.options=a.children.map(o=>({label:o.name,value:o.id,data:z(o.deviceId)+"."+o.attr,unit:o.unit?"("+o.unit+")":""})),m.defaultParams={...m.defaultParams,attributeId:a.children[0].id},(l=L.value)==null||l.resetForm(),console.log(e),U()},U=async()=>{var u,b,y,I;const e=((u=L.value)==null?void 0:u.queryParams)||{},[a,s]=e.date||[],[r,l]=e.filterStart||[],o={filterStart:r||0,filterEnd:l||23,queryType:"10m",stationId:x.stationId,group:e==null?void 0:e.group,start:a?f(a).startOf("day").valueOf():"",end:s?f(s).endOf("day").valueOf():""};n=(b=(await we(o)).data)==null?void 0:b.data;const v=n==null?void 0:n.tableInfo.map(d=>({prop:d.columnValue,label:d.columnName,unit:d.unit?"("+d.unit+")":""}));console.log(v),g.columns=v,g.dataList=(y=n==null?void 0:n.tableDataList)==null?void 0:y.slice(0*20,20),g.pagination.total=(I=n==null?void 0:n.tableDataList)==null?void 0:I.length,g.loading=!1,ie(e==null?void 0:e.attributeId)},ie=e=>{var o,i,v;const a=ae(),r=(i=((o=m.filters)==null?void 0:o.find(u=>u.field==="attributeId")).options)==null?void 0:i.find(u=>u.value===e);a.yAxis[0].name="压力(Mpa)",a.xAxis.data=n==null?void 0:n.tableDataList.map(u=>u.ts),console.log(e+"."+r.data,n==null?void 0:n.tableDataList);const l={name:r.label,smooth:!0,data:n==null?void 0:n.tableDataList.map(u=>u[r.data]),type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]}};(v=P.value)==null||v.clear(),a.series=[l],N(()=>{B.listenTo(S.value,()=>{var u,b;t.chartOption1=a,(u=P.value)==null||u.resize(),(b=G.value)==null||b.resize()})})},W=h({type:"tabs",tabType:"simple",width:"100%",stretch:!1,tabs:[],handleTabClick:e=>{$(e.props.name)}});be(()=>[x.stationId,t.searchActiveName],async(e,a)=>{e[0]&&a[0]!==e[0]&&ce(),a[1]!==e[1]&&t.searchActiveName==="echarts"&&F()});const ce=()=>{t.activeName="status",N(async()=>{console.log("refreshData",t.stationId),await ue(x.stationId)})},ue=async e=>{var r,l,o;t.groupStation=await re(e),t.currentGroupTabs=t.groupStation;const a=t.groupStation[0].children,s=a==null?void 0:a.map(i=>({label:i.name,value:i.id,data:z(i.deviceId)+"."+i.attr}));t.attributeOptions=s,console.log("getStationGroup",m),m.defaultParams={...m.defaultParams,attributeId:(r=t.attributeOptions)==null?void 0:r.find(i=>i.data.indexOf("pressure")!==-1).value,group:t.groupStation[0].value},(l=L.value)==null||l.resetForm(),W.tabs=a.map(i=>({label:i.label,value:i.id,data:i})),t.groupTab=(o=W.tabs[0])==null?void 0:o.label,await $(t.groupTab),await E()},E=async e=>{var r;_.loading=!0;let a=f().startOf("month").valueOf(),s=f().endOf("month").valueOf();if(e==="range"){const l=(r=j.value)==null?void 0:r.queryParams;a=f(l==null?void 0:l.date[0]).startOf("day").valueOf(),s=f(l==null?void 0:l.date[1]).endOf("day").valueOf()}Le(x.stationId,a,s).then(l=>{console.log("res",l),C=l.data,_.dataList=C==null?void 0:C.slice(0*20,20),_.pagination.total=C.length,_.loading=!1})},$=async e=>{T.loading=!0;const a=await Ie(x.stationId,e);T.dataList=a.data;const s=a==null?void 0:a.data[0];T.currentRow=s,F(),console.log(T.currentRow),T.loading=!1},F=async()=>{var b,y,I;const e=T.dataList.find(d=>d.property==="pressure"),a=await Ne(x.stationId);t.detailData=a.data;const r=(b=(await De({deviceId:z(e.deviceId),attr:"pressure"})).data)==null?void 0:b.data,l=ae(),o=[{name:"前天",key:"beforeYesterdayDataList"},{name:"昨天",key:"yesterdayDataList"},{name:"今天",key:"todayDataList"}],i=T.dataList.find(d=>d.property==="pressure");t.detailData={...t.detailData,pressure:i.value+"("+i.unit+")"};const v=Oe([{value:i.value,name:"压力"}]);l.xAxis.data=r.todayDataList.map(d=>d.ts),l.yAxis[0].name="压力(Mpa)";const u=o.map(d=>{const R=r[d.key].map(de=>de.value);return{name:d.name,smooth:!0,data:R,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}});l.series=u,(y=V.value)==null||y.clear(),(I=G.value)==null||I.clear(),N(()=>{B.listenTo(S.value,()=>{var d,R;t.chartOption=l,t.gaugeOption=v,(d=V.value)==null||d.resize(),(R=G.value)==null||R.resize()})}),oe("hiddenLoading")};return(e,a)=>{const s=ve,r=ge("VChart"),l=Ce,o=he,i=xe,v=_e,u=ye,b=Te;return O(),K(l,{class:"wrapper-content",title:" ",overlay:""},{title:p(()=>[c(s,{modelValue:t.activeName,"onUpdate:modelValue":a[0]||(a[0]=y=>t.activeName=y),config:se},null,8,["modelValue","config"])]),default:p(()=>[t.activeName==="status"?(O(),M("div",Re,[w("div",Ye,[c(l,{title:"",overlay:"",class:"card-table"},{default:p(()=>[w("div",{ref_key:"echartsDiv",ref:S,class:"chart-box"},[c(r,{ref_key:"refGaugeChart",ref:G,option:t.gaugeOption},null,8,["option"])],512)]),_:1}),c(l,{title:"数据详情",overlay:""},{default:p(()=>[w("div",Me,[c(o,{data:t.detailData,columns:t.columns},null,8,["data","columns"])])]),_:1})]),w("div",Ve,[c(l,{title:"",overlay:""},{default:p(()=>[w("div",{ref_key:"echartsDiv",ref:S,class:"chart-box"},[c(r,{ref_key:"refChart",ref:V,option:t.chartOption},null,8,["option"])],512)]),_:1})])])):D("",!0),t.activeName==="search"?(O(),M("div",Pe,[c(l,{class:"wrapper-content content1",title:" ",overlay:""},{title:p(()=>[c(i,{ref_key:"cardSearch",ref:L,style:{"margin-top":"5px"},config:m},null,8,["config"])]),default:p(()=>[c(l,{title:" ",class:"card-table"},{right:p(()=>[c(u,{modelValue:t.searchActiveName,"onUpdate:modelValue":a[1]||(a[1]=y=>t.searchActiveName=y)},{default:p(()=>[c(v,{label:"echarts"},{default:p(()=>[c(q(X),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),c(v,{label:"list"},{default:p(()=>[c(q(X),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:p(()=>[t.searchActiveName==="list"?(O(),M("div",Ee,[c(b,{ref_key:"refTable",ref:H,config:g,class:"chart-box"},null,8,["config"])])):D("",!0),t.searchActiveName==="echarts"?(O(),M("div",{key:1,ref_key:"echartsDiv",ref:S,class:"chart-box"},[c(r,{ref_key:"refChart1",ref:P,option:t.chartOption1},null,8,["option"])],512)):D("",!0)]),_:1})]),_:1})])):D("",!0),t.activeName==="alarm"?(O(),K(l,{key:2,class:"wrapper-content content1",title:" ",overlay:""},{title:p(()=>[c(i,{ref_key:"alarmCardSearch",ref:j,config:ne},null,8,["config"])]),default:p(()=>[c(b,{ref_key:"alarmTable",ref:J,config:_,class:"card-table"},null,8,["config"])]),_:1})):D("",!0)]),_:1})}}}),$e=ke(Fe,[["__scopeId","data-v-d61cf03b"]]);export{$e as default};
