<template>
  <div class="key-value-table">
    <Search
      ref="refSearch"
      class="key-value-search"
      :config="SearchConfig"
    ></Search>
    <FormTable :config="TableConfig"></FormTable>
  </div>
</template>
<script lang="ts" setup>
import { SLConfirm } from '@/utils/Message'

const emit = defineEmits(['onSave'])
const props = defineProps<{
  field: string
  keyValues?: any[]
}>()
const SearchConfig = reactive<ISearch>({
  filters: [
    // { type: 'input', readonly: true, label: '字段名', field: 'field' },
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '添加行', styles: { marginLeft: 'auto' }, click: () => handleAdd() },
        { perm: true, text: '删除', type: 'danger', click: () => handleRemove() }
      ]
    }
  ],
  defaultParams: {
    field: props.field
  }
})
const handleAdd = () => {
  TableConfig.dataList.unshift({
    label: '',
    value: ''
  })
}
const handleRemove = (i?: number) => {
  SLConfirm('确定移除?', '提示信息')
    .then(() => {
      if (i === undefined) {
        const selectedValues = TableConfig.selectList?.map(item => item.value)
        TableConfig.dataList = TableConfig.dataList.filter(item => selectedValues?.indexOf(item.value) === -1)
      } else {
        TableConfig.dataList.splice(i, 1)
      }
    })
    .catch(() => {
      //
    })
}
const TableConfig = reactive<ITable>({
  height: 300,
  columns: [
    { label: '值', prop: 'value', formItemConfig: { type: 'input' } },
    { label: '显示名称', prop: 'label', formItemConfig: { type: 'input' } }
  ],
  dataList: [],
  operations: [{ perm: true, text: '删除', type: 'danger', click: (row, i) => handleRemove(i) }],
  pagination: {
    hide: true
  },
  handleSelectChange(val) {
    TableConfig.selectList = val ?? []
  }
})
const refreshKeyValues = () => {
  if (!props.keyValues) TableConfig.dataList = []
  else {
    TableConfig.dataList = typeof props.keyValues === 'string' ? JSON.parse(props.keyValues) : props.keyValues
  }
}
watch(
  () => props.keyValues,
  () => refreshKeyValues()
)
const save = () => {
  const list = TableConfig.dataList.map(item => {
    item.value = item.value?.trim()
    return item
  }) || []
  const values = list.map(item => item.value)
  const noMultipleVals = Array.from(new Set(values))
  const hasMultiple = values.length !== noMultipleVals.length

  emit('onSave', list, hasMultiple)
  return hasMultiple
}
defineExpose({
  save
})
onMounted(() => {
  refreshKeyValues()
})
</script>
<style lang="scss" scoped>
.key-value-table {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
  .key-value-search {
    :deep(.search-box) {
      padding: 0;
    }
  }
}
</style>
