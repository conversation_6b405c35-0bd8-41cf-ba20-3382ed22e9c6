package org.thingsboard.server.dao.orderWork;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderResource;

import java.util.List;

public interface WorkOrderResourceService {
    PageData<WorkOrderResource> findList(int page, int size, String status, TenantId tenantId);

    void save(WorkOrderResource entity);

    void changeStatus(String status, String id);

    List<WorkOrderResource> findAll(String status, TenantId tenantId);
}
