package org.thingsboard.server.dao.gis;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisOptionLogListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisOptionLog;

import java.util.List;

public interface GisOptionLogService {
    /**
     * 分页查询gis操作日志列表
     *
     * @param request  请求参数
     * @param tenantId 租户ID
     * @return 数据
     */
    PageData<GisOptionLog> findList(GisOptionLogListRequest request, TenantId tenantId);

    void save(GisOptionLog entity, User currentUser);

    void remove(List<String> ids);
}
