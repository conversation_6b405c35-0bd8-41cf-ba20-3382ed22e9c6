<!-- 报废单 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <SLDrawer ref="refForm" :config="addOrUpdateConfig"></SLDrawer>
    <SLDrawer ref="detailForm" :config="detailConfig"></SLDrawer>
    <!-- 设备选中 -->
    <SLDrawer ref="refFormEquipment" :config="addEquipment"></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import useGlobal from '@/hooks/global/useGlobal'
import { getdeviceDumpSerch, getPlanTaskDetail, postdeviceDump } from '@/api/equipment_assets/equipmentScrapped'

import { getstoreSerch } from '@/api/equipment_assets/equipmentOutStock'
import { getWaterSupplyTree } from '@/api/company_org'
import { getUserList } from '@/api/user/index'
import { removeSlash } from '@/utils/removeIdSlash'
import { getDevicePurchaseSearch } from '@/api/equipment_assets/equipmentPurchase'
import { getDeviceStorageJournalSerch } from '@/api/equipment_assets/ledgerManagement'
import { traverse, uniqueFunc } from '@/utils/GlobalHelper'
import { formatDate } from '@/utils/DateFormatter'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const refForm = ref<ISLDrawerIns>()

const detailForm = ref<ISLDrawerIns>()

const chosen = ref([])

const refFormEquipment = ref<ISLDrawerIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '报废单编码', field: 'code', type: 'input', labelWidth: '90px' },
    { label: '报废单标题', field: 'name', type: 'input', labelWidth: '90px' },
    { label: '申请人员', field: 'uploadUserId', type: 'department-user' },
    { label: '经办人', field: 'handleUserId', type: 'department-user' },
    {
      type: 'date',
      label: '报废单创建时间',
      field: 'createTime',
      labelWidth: '120px',
      format: 'YYYY-MM-DD HH:mm:ss',
      onChange: () => refreshData()
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          type: 'success',
          perm: true,
          text: '新增',
          icon: ICONS.ADD,
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '报废单标题', prop: 'name' },
    { label: '报废单编码', prop: 'code' },
    { label: '报废申请时间', prop: 'uploadTime', formatter: row => (row.uploadTime ? dayjs(row.uploadTime).format('YYYY-MM-DD') : '') },
    { label: '申请部门', prop: 'uploadUserDepartmentName' },
    { label: '申请人', prop: 'uploadUserName' },
    { label: '经办人', prop: 'handleUserName' },
    { label: '创建人', prop: 'creatorName' },
    { label: '创建时间', prop: 'createTime' },
    { label: '报废状态', prop: 'isDump', formatter: row => (row.isDump ? '已报废' : '待报废') }
  ],
  operationWidth: '160px',
  operations: [
    {
      type: 'primary',
      color: '#4195f0',
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: row => openDetails(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

// 添加
const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增报废单',
  labelWidth: '100px',
  submitting: false,
  submit: (params: any, status?: boolean) => {
    if (status) {
      addEquipment.defaultValue = { storehouseId: params.storehouseId }
      data.getDevice({ storehouseId: params.storehouseId })
      refFormEquipment.value?.openDrawer()
      return
    }
    addOrUpdateConfig.submitting = true
    if (!data.selectList.length) {
      ElMessage.warning('请选择设备')
      return
    }
    postdeviceDump(params).then(() => {
      ElMessage.success('添加成功')
      refreshData()
      addOrUpdateConfig.submitting = false
      refForm.value?.closeDrawer()
    }).catch(error => {
      ElMessage.warning(error)
      addOrUpdateConfig.submitting = false
    })
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '报废单标题',
          field: 'name',
          rules: [{ required: true, message: '请输入报废单标题' }]
        }, {
          xl: 8,
          readonly: true,
          type: 'input-number',
          label: '报废单编码',
          field: 'code',
          rules: [{ required: true, message: '请输入报废单编码' }]
        }, {
          xl: 8,
          type: 'select',
          label: '仓库名称',
          field: 'storehouseId',
          rules: [{ required: true, message: '请输入仓库名称' }],
          options: computed(() => data.storeList) as any
        }, {
          xl: 8,
          type: 'department-user',
          label: '申请人员',
          field: 'uploadUserId',
          rules: [{ required: true, message: '请输入申请人员' }]
        }, {
          xl: 8,
          type: 'department-user',
          label: '经办人',
          field: 'handleUserId',
          rules: [{ required: true, message: '请输入经办人' }]
        }, {
          xl: 8,
          type: 'date',
          label: '报废时间',
          field: 'uploadTime',
          rules: [{ required: true, message: '请输入报废时间' }],
          format: 'YYYY-MM-DD HH:mm:ss'
        }, {
          xl: 16,
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },

        {
          type: 'table',
          field: 'items',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end',
                  marginBottom: '10px'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [{
                      text: '增加设备',
                      perm: true,
                      click: () => {
                        refForm.value?.Submit(true)
                      }
                    }]
                  }
                ]
              }
            ],
            columns: [
              { label: '标签编码', prop: 'deviceLabelCode' },
              { label: '设备名称', prop: 'name' },
              { label: '型号/规格', prop: 'model' },
              { label: '所属大类', prop: 'topType' },
              { label: '货架', prop: 'shelvesName' },
              { label: '所属类别', prop: 'type' }
            ],
            operations: [
              {
                text: '移除',
                perm: true,
                type: 'danger',
                icon: ICONS.DELETE,
                click: row => { data.selectList = data.selectList.filter(item => item.id !== row.id) }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  labelWidth: '100px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '报废单标题',
          field: 'name',
          rules: [{ required: true, message: '请输入报废单标题' }]
        }, {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '报废单编码',
          field: 'code',
          rules: [{ required: true, message: '请输入报废单编码' }]
        }, {
          xl: 8,
          readonly: true,
          type: 'select',
          label: '仓库名称',
          field: 'storehouseId',
          rules: [{ required: true, message: '请输入仓库名称' }],
          options: computed(() => data.storeList) as any
        }, {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '申请部门',
          field: 'uploadUserDepartmentName',
          rules: [{ required: true, message: '请输入申请部门' }]
        }, {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '申请人员',
          field: 'uploadUserName',
          rules: [{ required: true, message: '请输入申请人员' }]
        }, {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '经办部门',
          field: 'handleUserDepartmentName',
          rules: [{ required: true, message: '请输入经办部门' }]
        }, {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '经办人',
          field: 'handleUserName',
          rules: [{ required: true, message: '请输入经办人' }]
        }, {
          xl: 8,
          readonly: true,
          type: 'date',
          label: '报废时间',
          field: 'uploadTime',
          rules: [{ required: true, message: '请输入报废时间' }],
          format: 'YYYY-MM-DD HH:mm:ss'
        }, {
          xl: 16,
          disabled: true,
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },

        {
          type: 'table',
          field: 'items',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              { label: '标签编码', prop: 'deviceLabelCode' },
              { label: '设备名称', prop: 'name' },
              { label: '型号/规格', prop: 'model' },
              { label: '所属大类', prop: 'topType' },
              { label: '货架', prop: 'shelvesName' },
              { label: '所属类别', prop: 'type' }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 设备选择
const addEquipment = reactive<IDrawerConfig>({
  title: '设备选择',
  submit: (params: any, status?: boolean) => {
    delete params.device
    // 搜索处理
    if (status) {
      data.getDevice(params)
    } else {
      data.selectList = [...data.selectList, ...chosen.value]
      data.selectList = uniqueFunc(data.selectList, ['deviceLabelCode'])
      refFormEquipment.value?.closeDrawer()
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '标签编码',
          field: 'deviceLabelCode'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备名称',
          field: 'name'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备型号',
          field: 'model'
        },
        {
          type: 'table',
          field: 'device',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceValue) as any,
            selectList: [],
            handleSelectChange: val => {
              chosen.value = val
            },
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '搜索',
                        perm: true,
                        click: () => {
                          refFormEquipment.value?.Submit(true)
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '标签编码',
                prop: 'deviceLabelCode'
              }, {
                label: '设备名称',
                prop: 'name'
              }, {
                label: '规格/型号',
                prop: 'model'
              }, {
                label: '所属大类',
                prop: 'topType'
              }, {
                label: '所属类别',
                prop: 'type'
              }, {
                label: '货架',
                prop: 'shelvesName'
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增报废单'
  data.selectList = []
  addOrUpdateConfig.defaultValue = { code: formatDate(new Date(), 'YYYYMMDDHHmmss') }
  refForm.value?.openDrawer()
}

const openDetails = (row: { [x: string]: any }) => {
  detailConfig.title = '报废单详情'
  detailConfig.defaultValue = { ...(row) || {} }
  data.getSelectValue(row)
  detailForm.value?.openDrawer()
}

const data = reactive({
  // 部门
  WaterSupplyTree: [] as any,
  // 用户列表
  UserList: [],
  // 采购单
  DevicePurchase: [],
  // 仓库
  storeList: [],
  // 设备列表
  deviceValue: [] as any,
  // 设备数量
  total: 0,
  // 选中的设备
  selectList: [] as any[],

  getDevice: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      ...param
    }
    getDeviceStorageJournalSerch(params).then(res => {
      data.deviceValue = res.data.data.data || []
    })
  },
  getWaterSupplyTreeValue: () => {
    const depth = 2
    getWaterSupplyTree(depth).then(res => {
      data.WaterSupplyTree = traverse(res.data.data || [])
    })
  },
  getUserListValue: (pid: string) => {
    getUserList({ pid }).then(res => {
      const value = res.data.data.data || []
      data.UserList = value.map(item => {
        return { label: item.firstName, value: removeSlash(item.id.id) }
      })
    })
  },
  getDevicePurchaseValue: () => {
    const params = { page: 1, size: 99999 }
    getDevicePurchaseSearch(params).then(res => {
      const value = res.data.data.data || []
      data.DevicePurchase = value.map(item => {
        return { label: item.title, value: item.id }
      })
    })
  },
  getstoreSerchValue: () => {
    const params = { page: 1, size: 99999 }
    getstoreSerch(params).then(res => {
      const value = res.data.data.data || []
      data.storeList = value.map(item => {
        return { label: item.name, value: item.id }
      })
    })
  },
  getSelectValue: (row: any) => {
    const params = { page: 1, size: 99999, mainId: row.id }
    getPlanTaskDetail(params).then(res => {
      data.selectList = res.data.data.data || []
    })
  },

  init: () => {
    data.getWaterSupplyTreeValue()
    data.getDevicePurchaseValue()
    data.getstoreSerchValue()
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  }
  getdeviceDumpSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(() => {
  refreshData()
  data.init()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
