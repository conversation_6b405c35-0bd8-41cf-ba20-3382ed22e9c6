// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cluster.proto

package org.thingsboard.server.gen.cluster;

public final class ClusterAPIProtos {
  private ClusterAPIProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code cluster.MessageType}
   */
  public enum MessageType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *Cluster control messages
     * </pre>
     *
     * <code>RPC_SESSION_CREATE_REQUEST_MSG = 0;</code>
     */
    RPC_SESSION_CREATE_REQUEST_MSG(0),
    /**
     * <code>TO_ALL_NODES_MSG = 1;</code>
     */
    TO_ALL_NODES_MSG(1),
    /**
     * <code>RPC_SESSION_TELL_MSG = 2;</code>
     */
    RPC_SESSION_TELL_MSG(2),
    /**
     * <code>RPC_BROADCAST_MSG = 3;</code>
     */
    RPC_BROADCAST_MSG(3),
    /**
     * <code>CONNECT_RPC_MESSAGE = 4;</code>
     */
    CONNECT_RPC_MESSAGE(4),
    /**
     * <code>CLUSTER_ACTOR_MESSAGE = 5;</code>
     */
    CLUSTER_ACTOR_MESSAGE(5),
    /**
     * <pre>
     * Messages related to TelemetrySubscriptionService
     * </pre>
     *
     * <code>CLUSTER_TELEMETRY_SUBSCRIPTION_CREATE_MESSAGE = 6;</code>
     */
    CLUSTER_TELEMETRY_SUBSCRIPTION_CREATE_MESSAGE(6),
    /**
     * <code>CLUSTER_TELEMETRY_SUBSCRIPTION_UPDATE_MESSAGE = 7;</code>
     */
    CLUSTER_TELEMETRY_SUBSCRIPTION_UPDATE_MESSAGE(7),
    /**
     * <code>CLUSTER_TELEMETRY_SUBSCRIPTION_CLOSE_MESSAGE = 8;</code>
     */
    CLUSTER_TELEMETRY_SUBSCRIPTION_CLOSE_MESSAGE(8),
    /**
     * <code>CLUSTER_TELEMETRY_SESSION_CLOSE_MESSAGE = 9;</code>
     */
    CLUSTER_TELEMETRY_SESSION_CLOSE_MESSAGE(9),
    /**
     * <code>CLUSTER_TELEMETRY_ATTR_UPDATE_MESSAGE = 10;</code>
     */
    CLUSTER_TELEMETRY_ATTR_UPDATE_MESSAGE(10),
    /**
     * <code>CLUSTER_TELEMETRY_TS_UPDATE_MESSAGE = 11;</code>
     */
    CLUSTER_TELEMETRY_TS_UPDATE_MESSAGE(11),
    /**
     * <code>CLUSTER_RPC_FROM_DEVICE_RESPONSE_MESSAGE = 12;</code>
     */
    CLUSTER_RPC_FROM_DEVICE_RESPONSE_MESSAGE(12),
    /**
     * <code>CLUSTER_DEVICE_STATE_SERVICE_MESSAGE = 13;</code>
     */
    CLUSTER_DEVICE_STATE_SERVICE_MESSAGE(13),
    /**
     * <code>CLUSTER_TRANSACTION_SERVICE_MESSAGE = 14;</code>
     */
    CLUSTER_TRANSACTION_SERVICE_MESSAGE(14),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *Cluster control messages
     * </pre>
     *
     * <code>RPC_SESSION_CREATE_REQUEST_MSG = 0;</code>
     */
    public static final int RPC_SESSION_CREATE_REQUEST_MSG_VALUE = 0;
    /**
     * <code>TO_ALL_NODES_MSG = 1;</code>
     */
    public static final int TO_ALL_NODES_MSG_VALUE = 1;
    /**
     * <code>RPC_SESSION_TELL_MSG = 2;</code>
     */
    public static final int RPC_SESSION_TELL_MSG_VALUE = 2;
    /**
     * <code>RPC_BROADCAST_MSG = 3;</code>
     */
    public static final int RPC_BROADCAST_MSG_VALUE = 3;
    /**
     * <code>CONNECT_RPC_MESSAGE = 4;</code>
     */
    public static final int CONNECT_RPC_MESSAGE_VALUE = 4;
    /**
     * <code>CLUSTER_ACTOR_MESSAGE = 5;</code>
     */
    public static final int CLUSTER_ACTOR_MESSAGE_VALUE = 5;
    /**
     * <pre>
     * Messages related to TelemetrySubscriptionService
     * </pre>
     *
     * <code>CLUSTER_TELEMETRY_SUBSCRIPTION_CREATE_MESSAGE = 6;</code>
     */
    public static final int CLUSTER_TELEMETRY_SUBSCRIPTION_CREATE_MESSAGE_VALUE = 6;
    /**
     * <code>CLUSTER_TELEMETRY_SUBSCRIPTION_UPDATE_MESSAGE = 7;</code>
     */
    public static final int CLUSTER_TELEMETRY_SUBSCRIPTION_UPDATE_MESSAGE_VALUE = 7;
    /**
     * <code>CLUSTER_TELEMETRY_SUBSCRIPTION_CLOSE_MESSAGE = 8;</code>
     */
    public static final int CLUSTER_TELEMETRY_SUBSCRIPTION_CLOSE_MESSAGE_VALUE = 8;
    /**
     * <code>CLUSTER_TELEMETRY_SESSION_CLOSE_MESSAGE = 9;</code>
     */
    public static final int CLUSTER_TELEMETRY_SESSION_CLOSE_MESSAGE_VALUE = 9;
    /**
     * <code>CLUSTER_TELEMETRY_ATTR_UPDATE_MESSAGE = 10;</code>
     */
    public static final int CLUSTER_TELEMETRY_ATTR_UPDATE_MESSAGE_VALUE = 10;
    /**
     * <code>CLUSTER_TELEMETRY_TS_UPDATE_MESSAGE = 11;</code>
     */
    public static final int CLUSTER_TELEMETRY_TS_UPDATE_MESSAGE_VALUE = 11;
    /**
     * <code>CLUSTER_RPC_FROM_DEVICE_RESPONSE_MESSAGE = 12;</code>
     */
    public static final int CLUSTER_RPC_FROM_DEVICE_RESPONSE_MESSAGE_VALUE = 12;
    /**
     * <code>CLUSTER_DEVICE_STATE_SERVICE_MESSAGE = 13;</code>
     */
    public static final int CLUSTER_DEVICE_STATE_SERVICE_MESSAGE_VALUE = 13;
    /**
     * <code>CLUSTER_TRANSACTION_SERVICE_MESSAGE = 14;</code>
     */
    public static final int CLUSTER_TRANSACTION_SERVICE_MESSAGE_VALUE = 14;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static MessageType valueOf(int value) {
      return forNumber(value);
    }

    public static MessageType forNumber(int value) {
      switch (value) {
        case 0: return RPC_SESSION_CREATE_REQUEST_MSG;
        case 1: return TO_ALL_NODES_MSG;
        case 2: return RPC_SESSION_TELL_MSG;
        case 3: return RPC_BROADCAST_MSG;
        case 4: return CONNECT_RPC_MESSAGE;
        case 5: return CLUSTER_ACTOR_MESSAGE;
        case 6: return CLUSTER_TELEMETRY_SUBSCRIPTION_CREATE_MESSAGE;
        case 7: return CLUSTER_TELEMETRY_SUBSCRIPTION_UPDATE_MESSAGE;
        case 8: return CLUSTER_TELEMETRY_SUBSCRIPTION_CLOSE_MESSAGE;
        case 9: return CLUSTER_TELEMETRY_SESSION_CLOSE_MESSAGE;
        case 10: return CLUSTER_TELEMETRY_ATTR_UPDATE_MESSAGE;
        case 11: return CLUSTER_TELEMETRY_TS_UPDATE_MESSAGE;
        case 12: return CLUSTER_RPC_FROM_DEVICE_RESPONSE_MESSAGE;
        case 13: return CLUSTER_DEVICE_STATE_SERVICE_MESSAGE;
        case 14: return CLUSTER_TRANSACTION_SERVICE_MESSAGE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MessageType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        MessageType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MessageType>() {
            public MessageType findValueByNumber(int number) {
              return MessageType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.getDescriptor().getEnumTypes().get(0);
    }

    private static final MessageType[] VALUES = values();

    public static MessageType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private MessageType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cluster.MessageType)
  }

  public interface ClusterMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.ClusterMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .cluster.MessageType messageType = 1;</code>
     */
    int getMessageTypeValue();
    /**
     * <code>optional .cluster.MessageType messageType = 1;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType getMessageType();

    /**
     * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
     */
    boolean hasMessageMetaInfo();
    /**
     * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo getMessageMetaInfo();
    /**
     * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfoOrBuilder getMessageMetaInfoOrBuilder();

    /**
     * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
     */
    boolean hasServerAddress();
    /**
     * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress getServerAddress();
    /**
     * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddressOrBuilder getServerAddressOrBuilder();

    /**
     * <code>optional bytes payload = 4;</code>
     */
    com.google.protobuf.ByteString getPayload();
  }
  /**
   * Protobuf type {@code cluster.ClusterMessage}
   */
  public  static final class ClusterMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.ClusterMessage)
      ClusterMessageOrBuilder {
    // Use ClusterMessage.newBuilder() to construct.
    private ClusterMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClusterMessage() {
      messageType_ = 0;
      payload_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private ClusterMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              int rawValue = input.readEnum();

              messageType_ = rawValue;
              break;
            }
            case 18: {
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.Builder subBuilder = null;
              if (messageMetaInfo_ != null) {
                subBuilder = messageMetaInfo_.toBuilder();
              }
              messageMetaInfo_ = input.readMessage(org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(messageMetaInfo_);
                messageMetaInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.Builder subBuilder = null;
              if (serverAddress_ != null) {
                subBuilder = serverAddress_.toBuilder();
              }
              serverAddress_ = input.readMessage(org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(serverAddress_);
                serverAddress_ = subBuilder.buildPartial();
              }

              break;
            }
            case 34: {

              payload_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_ClusterMessage_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_ClusterMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage.Builder.class);
    }

    public static final int MESSAGETYPE_FIELD_NUMBER = 1;
    private int messageType_;
    /**
     * <code>optional .cluster.MessageType messageType = 1;</code>
     */
    public int getMessageTypeValue() {
      return messageType_;
    }
    /**
     * <code>optional .cluster.MessageType messageType = 1;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType getMessageType() {
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType result = org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType.valueOf(messageType_);
      return result == null ? org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType.UNRECOGNIZED : result;
    }

    public static final int MESSAGEMETAINFO_FIELD_NUMBER = 2;
    private org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo messageMetaInfo_;
    /**
     * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
     */
    public boolean hasMessageMetaInfo() {
      return messageMetaInfo_ != null;
    }
    /**
     * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo getMessageMetaInfo() {
      return messageMetaInfo_ == null ? org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.getDefaultInstance() : messageMetaInfo_;
    }
    /**
     * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfoOrBuilder getMessageMetaInfoOrBuilder() {
      return getMessageMetaInfo();
    }

    public static final int SERVERADDRESS_FIELD_NUMBER = 3;
    private org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress serverAddress_;
    /**
     * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
     */
    public boolean hasServerAddress() {
      return serverAddress_ != null;
    }
    /**
     * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress getServerAddress() {
      return serverAddress_ == null ? org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.getDefaultInstance() : serverAddress_;
    }
    /**
     * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddressOrBuilder getServerAddressOrBuilder() {
      return getServerAddress();
    }

    public static final int PAYLOAD_FIELD_NUMBER = 4;
    private com.google.protobuf.ByteString payload_;
    /**
     * <code>optional bytes payload = 4;</code>
     */
    public com.google.protobuf.ByteString getPayload() {
      return payload_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (messageType_ != org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType.RPC_SESSION_CREATE_REQUEST_MSG.getNumber()) {
        output.writeEnum(1, messageType_);
      }
      if (messageMetaInfo_ != null) {
        output.writeMessage(2, getMessageMetaInfo());
      }
      if (serverAddress_ != null) {
        output.writeMessage(3, getServerAddress());
      }
      if (!payload_.isEmpty()) {
        output.writeBytes(4, payload_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (messageType_ != org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType.RPC_SESSION_CREATE_REQUEST_MSG.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, messageType_);
      }
      if (messageMetaInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getMessageMetaInfo());
      }
      if (serverAddress_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getServerAddress());
      }
      if (!payload_.isEmpty()) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, payload_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage) obj;

      boolean result = true;
      result = result && messageType_ == other.messageType_;
      result = result && (hasMessageMetaInfo() == other.hasMessageMetaInfo());
      if (hasMessageMetaInfo()) {
        result = result && getMessageMetaInfo()
            .equals(other.getMessageMetaInfo());
      }
      result = result && (hasServerAddress() == other.hasServerAddress());
      if (hasServerAddress()) {
        result = result && getServerAddress()
            .equals(other.getServerAddress());
      }
      result = result && getPayload()
          .equals(other.getPayload());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + MESSAGETYPE_FIELD_NUMBER;
      hash = (53 * hash) + messageType_;
      if (hasMessageMetaInfo()) {
        hash = (37 * hash) + MESSAGEMETAINFO_FIELD_NUMBER;
        hash = (53 * hash) + getMessageMetaInfo().hashCode();
      }
      if (hasServerAddress()) {
        hash = (37 * hash) + SERVERADDRESS_FIELD_NUMBER;
        hash = (53 * hash) + getServerAddress().hashCode();
      }
      hash = (37 * hash) + PAYLOAD_FIELD_NUMBER;
      hash = (53 * hash) + getPayload().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.ClusterMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.ClusterMessage)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_ClusterMessage_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_ClusterMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        messageType_ = 0;

        if (messageMetaInfoBuilder_ == null) {
          messageMetaInfo_ = null;
        } else {
          messageMetaInfo_ = null;
          messageMetaInfoBuilder_ = null;
        }
        if (serverAddressBuilder_ == null) {
          serverAddress_ = null;
        } else {
          serverAddress_ = null;
          serverAddressBuilder_ = null;
        }
        payload_ = com.google.protobuf.ByteString.EMPTY;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_ClusterMessage_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage(this);
        result.messageType_ = messageType_;
        if (messageMetaInfoBuilder_ == null) {
          result.messageMetaInfo_ = messageMetaInfo_;
        } else {
          result.messageMetaInfo_ = messageMetaInfoBuilder_.build();
        }
        if (serverAddressBuilder_ == null) {
          result.serverAddress_ = serverAddress_;
        } else {
          result.serverAddress_ = serverAddressBuilder_.build();
        }
        result.payload_ = payload_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage.getDefaultInstance()) return this;
        if (other.messageType_ != 0) {
          setMessageTypeValue(other.getMessageTypeValue());
        }
        if (other.hasMessageMetaInfo()) {
          mergeMessageMetaInfo(other.getMessageMetaInfo());
        }
        if (other.hasServerAddress()) {
          mergeServerAddress(other.getServerAddress());
        }
        if (other.getPayload() != com.google.protobuf.ByteString.EMPTY) {
          setPayload(other.getPayload());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int messageType_ = 0;
      /**
       * <code>optional .cluster.MessageType messageType = 1;</code>
       */
      public int getMessageTypeValue() {
        return messageType_;
      }
      /**
       * <code>optional .cluster.MessageType messageType = 1;</code>
       */
      public Builder setMessageTypeValue(int value) {
        messageType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional .cluster.MessageType messageType = 1;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType getMessageType() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType result = org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType.valueOf(messageType_);
        return result == null ? org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType.UNRECOGNIZED : result;
      }
      /**
       * <code>optional .cluster.MessageType messageType = 1;</code>
       */
      public Builder setMessageType(org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        messageType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .cluster.MessageType messageType = 1;</code>
       */
      public Builder clearMessageType() {
        
        messageType_ = 0;
        onChanged();
        return this;
      }

      private org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo messageMetaInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo, org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfoOrBuilder> messageMetaInfoBuilder_;
      /**
       * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
       */
      public boolean hasMessageMetaInfo() {
        return messageMetaInfoBuilder_ != null || messageMetaInfo_ != null;
      }
      /**
       * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo getMessageMetaInfo() {
        if (messageMetaInfoBuilder_ == null) {
          return messageMetaInfo_ == null ? org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.getDefaultInstance() : messageMetaInfo_;
        } else {
          return messageMetaInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
       */
      public Builder setMessageMetaInfo(org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo value) {
        if (messageMetaInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          messageMetaInfo_ = value;
          onChanged();
        } else {
          messageMetaInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
       */
      public Builder setMessageMetaInfo(
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.Builder builderForValue) {
        if (messageMetaInfoBuilder_ == null) {
          messageMetaInfo_ = builderForValue.build();
          onChanged();
        } else {
          messageMetaInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
       */
      public Builder mergeMessageMetaInfo(org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo value) {
        if (messageMetaInfoBuilder_ == null) {
          if (messageMetaInfo_ != null) {
            messageMetaInfo_ =
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.newBuilder(messageMetaInfo_).mergeFrom(value).buildPartial();
          } else {
            messageMetaInfo_ = value;
          }
          onChanged();
        } else {
          messageMetaInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
       */
      public Builder clearMessageMetaInfo() {
        if (messageMetaInfoBuilder_ == null) {
          messageMetaInfo_ = null;
          onChanged();
        } else {
          messageMetaInfo_ = null;
          messageMetaInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.Builder getMessageMetaInfoBuilder() {
        
        onChanged();
        return getMessageMetaInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfoOrBuilder getMessageMetaInfoOrBuilder() {
        if (messageMetaInfoBuilder_ != null) {
          return messageMetaInfoBuilder_.getMessageOrBuilder();
        } else {
          return messageMetaInfo_ == null ?
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.getDefaultInstance() : messageMetaInfo_;
        }
      }
      /**
       * <code>optional .cluster.MessageMataInfo messageMetaInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo, org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfoOrBuilder> 
          getMessageMetaInfoFieldBuilder() {
        if (messageMetaInfoBuilder_ == null) {
          messageMetaInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo, org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfoOrBuilder>(
                  getMessageMetaInfo(),
                  getParentForChildren(),
                  isClean());
          messageMetaInfo_ = null;
        }
        return messageMetaInfoBuilder_;
      }

      private org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress serverAddress_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress, org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddressOrBuilder> serverAddressBuilder_;
      /**
       * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
       */
      public boolean hasServerAddress() {
        return serverAddressBuilder_ != null || serverAddress_ != null;
      }
      /**
       * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress getServerAddress() {
        if (serverAddressBuilder_ == null) {
          return serverAddress_ == null ? org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.getDefaultInstance() : serverAddress_;
        } else {
          return serverAddressBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
       */
      public Builder setServerAddress(org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress value) {
        if (serverAddressBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          serverAddress_ = value;
          onChanged();
        } else {
          serverAddressBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
       */
      public Builder setServerAddress(
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.Builder builderForValue) {
        if (serverAddressBuilder_ == null) {
          serverAddress_ = builderForValue.build();
          onChanged();
        } else {
          serverAddressBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
       */
      public Builder mergeServerAddress(org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress value) {
        if (serverAddressBuilder_ == null) {
          if (serverAddress_ != null) {
            serverAddress_ =
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.newBuilder(serverAddress_).mergeFrom(value).buildPartial();
          } else {
            serverAddress_ = value;
          }
          onChanged();
        } else {
          serverAddressBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
       */
      public Builder clearServerAddress() {
        if (serverAddressBuilder_ == null) {
          serverAddress_ = null;
          onChanged();
        } else {
          serverAddress_ = null;
          serverAddressBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.Builder getServerAddressBuilder() {
        
        onChanged();
        return getServerAddressFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddressOrBuilder getServerAddressOrBuilder() {
        if (serverAddressBuilder_ != null) {
          return serverAddressBuilder_.getMessageOrBuilder();
        } else {
          return serverAddress_ == null ?
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.getDefaultInstance() : serverAddress_;
        }
      }
      /**
       * <code>optional .cluster.ServerAddress serverAddress = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress, org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddressOrBuilder> 
          getServerAddressFieldBuilder() {
        if (serverAddressBuilder_ == null) {
          serverAddressBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress, org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddressOrBuilder>(
                  getServerAddress(),
                  getParentForChildren(),
                  isClean());
          serverAddress_ = null;
        }
        return serverAddressBuilder_;
      }

      private com.google.protobuf.ByteString payload_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes payload = 4;</code>
       */
      public com.google.protobuf.ByteString getPayload() {
        return payload_;
      }
      /**
       * <code>optional bytes payload = 4;</code>
       */
      public Builder setPayload(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        payload_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes payload = 4;</code>
       */
      public Builder clearPayload() {
        
        payload_ = getDefaultInstance().getPayload();
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.ClusterMessage)
    }

    // @@protoc_insertion_point(class_scope:cluster.ClusterMessage)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ClusterMessage>
        PARSER = new com.google.protobuf.AbstractParser<ClusterMessage>() {
      public ClusterMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new ClusterMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClusterMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClusterMessage> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ClusterMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ServerAddressOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.ServerAddress)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string host = 1;</code>
     */
    java.lang.String getHost();
    /**
     * <code>optional string host = 1;</code>
     */
    com.google.protobuf.ByteString
        getHostBytes();

    /**
     * <code>optional int32 port = 2;</code>
     */
    int getPort();
  }
  /**
   * Protobuf type {@code cluster.ServerAddress}
   */
  public  static final class ServerAddress extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.ServerAddress)
      ServerAddressOrBuilder {
    // Use ServerAddress.newBuilder() to construct.
    private ServerAddress(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ServerAddress() {
      host_ = "";
      port_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private ServerAddress(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              host_ = s;
              break;
            }
            case 16: {

              port_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_ServerAddress_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_ServerAddress_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.Builder.class);
    }

    public static final int HOST_FIELD_NUMBER = 1;
    private volatile java.lang.Object host_;
    /**
     * <code>optional string host = 1;</code>
     */
    public java.lang.String getHost() {
      java.lang.Object ref = host_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        host_ = s;
        return s;
      }
    }
    /**
     * <code>optional string host = 1;</code>
     */
    public com.google.protobuf.ByteString
        getHostBytes() {
      java.lang.Object ref = host_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        host_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PORT_FIELD_NUMBER = 2;
    private int port_;
    /**
     * <code>optional int32 port = 2;</code>
     */
    public int getPort() {
      return port_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getHostBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, host_);
      }
      if (port_ != 0) {
        output.writeInt32(2, port_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getHostBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, host_);
      }
      if (port_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, port_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress) obj;

      boolean result = true;
      result = result && getHost()
          .equals(other.getHost());
      result = result && (getPort()
          == other.getPort());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + HOST_FIELD_NUMBER;
      hash = (53 * hash) + getHost().hashCode();
      hash = (37 * hash) + PORT_FIELD_NUMBER;
      hash = (53 * hash) + getPort();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.ServerAddress}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.ServerAddress)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddressOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_ServerAddress_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_ServerAddress_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        host_ = "";

        port_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_ServerAddress_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress(this);
        result.host_ = host_;
        result.port_ = port_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress.getDefaultInstance()) return this;
        if (!other.getHost().isEmpty()) {
          host_ = other.host_;
          onChanged();
        }
        if (other.getPort() != 0) {
          setPort(other.getPort());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object host_ = "";
      /**
       * <code>optional string host = 1;</code>
       */
      public java.lang.String getHost() {
        java.lang.Object ref = host_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          host_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string host = 1;</code>
       */
      public com.google.protobuf.ByteString
          getHostBytes() {
        java.lang.Object ref = host_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          host_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string host = 1;</code>
       */
      public Builder setHost(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        host_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string host = 1;</code>
       */
      public Builder clearHost() {
        
        host_ = getDefaultInstance().getHost();
        onChanged();
        return this;
      }
      /**
       * <code>optional string host = 1;</code>
       */
      public Builder setHostBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        host_ = value;
        onChanged();
        return this;
      }

      private int port_ ;
      /**
       * <code>optional int32 port = 2;</code>
       */
      public int getPort() {
        return port_;
      }
      /**
       * <code>optional int32 port = 2;</code>
       */
      public Builder setPort(int value) {
        
        port_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 port = 2;</code>
       */
      public Builder clearPort() {
        
        port_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.ServerAddress)
    }

    // @@protoc_insertion_point(class_scope:cluster.ServerAddress)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ServerAddress>
        PARSER = new com.google.protobuf.AbstractParser<ServerAddress>() {
      public ServerAddress parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new ServerAddress(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ServerAddress> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ServerAddress> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.ServerAddress getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MessageMataInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.MessageMataInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string payloadMetaInfo = 1;</code>
     */
    java.lang.String getPayloadMetaInfo();
    /**
     * <code>optional string payloadMetaInfo = 1;</code>
     */
    com.google.protobuf.ByteString
        getPayloadMetaInfoBytes();

    /**
     * <code>repeated string tags = 2;</code>
     */
    java.util.List<java.lang.String>
        getTagsList();
    /**
     * <code>repeated string tags = 2;</code>
     */
    int getTagsCount();
    /**
     * <code>repeated string tags = 2;</code>
     */
    java.lang.String getTags(int index);
    /**
     * <code>repeated string tags = 2;</code>
     */
    com.google.protobuf.ByteString
        getTagsBytes(int index);
  }
  /**
   * Protobuf type {@code cluster.MessageMataInfo}
   */
  public  static final class MessageMataInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.MessageMataInfo)
      MessageMataInfoOrBuilder {
    // Use MessageMataInfo.newBuilder() to construct.
    private MessageMataInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MessageMataInfo() {
      payloadMetaInfo_ = "";
      tags_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private MessageMataInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              payloadMetaInfo_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                tags_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000002;
              }
              tags_.add(s);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          tags_ = tags_.getUnmodifiableView();
        }
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_MessageMataInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_MessageMataInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.Builder.class);
    }

    private int bitField0_;
    public static final int PAYLOADMETAINFO_FIELD_NUMBER = 1;
    private volatile java.lang.Object payloadMetaInfo_;
    /**
     * <code>optional string payloadMetaInfo = 1;</code>
     */
    public java.lang.String getPayloadMetaInfo() {
      java.lang.Object ref = payloadMetaInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payloadMetaInfo_ = s;
        return s;
      }
    }
    /**
     * <code>optional string payloadMetaInfo = 1;</code>
     */
    public com.google.protobuf.ByteString
        getPayloadMetaInfoBytes() {
      java.lang.Object ref = payloadMetaInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payloadMetaInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TAGS_FIELD_NUMBER = 2;
    private com.google.protobuf.LazyStringList tags_;
    /**
     * <code>repeated string tags = 2;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getTagsList() {
      return tags_;
    }
    /**
     * <code>repeated string tags = 2;</code>
     */
    public int getTagsCount() {
      return tags_.size();
    }
    /**
     * <code>repeated string tags = 2;</code>
     */
    public java.lang.String getTags(int index) {
      return tags_.get(index);
    }
    /**
     * <code>repeated string tags = 2;</code>
     */
    public com.google.protobuf.ByteString
        getTagsBytes(int index) {
      return tags_.getByteString(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getPayloadMetaInfoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, payloadMetaInfo_);
      }
      for (int i = 0; i < tags_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, tags_.getRaw(i));
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getPayloadMetaInfoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, payloadMetaInfo_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < tags_.size(); i++) {
          dataSize += computeStringSizeNoTag(tags_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getTagsList().size();
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo) obj;

      boolean result = true;
      result = result && getPayloadMetaInfo()
          .equals(other.getPayloadMetaInfo());
      result = result && getTagsList()
          .equals(other.getTagsList());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + PAYLOADMETAINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPayloadMetaInfo().hashCode();
      if (getTagsCount() > 0) {
        hash = (37 * hash) + TAGS_FIELD_NUMBER;
        hash = (53 * hash) + getTagsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.MessageMataInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.MessageMataInfo)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_MessageMataInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_MessageMataInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        payloadMetaInfo_ = "";

        tags_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_MessageMataInfo_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.payloadMetaInfo_ = payloadMetaInfo_;
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          tags_ = tags_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.tags_ = tags_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo.getDefaultInstance()) return this;
        if (!other.getPayloadMetaInfo().isEmpty()) {
          payloadMetaInfo_ = other.payloadMetaInfo_;
          onChanged();
        }
        if (!other.tags_.isEmpty()) {
          if (tags_.isEmpty()) {
            tags_ = other.tags_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureTagsIsMutable();
            tags_.addAll(other.tags_);
          }
          onChanged();
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object payloadMetaInfo_ = "";
      /**
       * <code>optional string payloadMetaInfo = 1;</code>
       */
      public java.lang.String getPayloadMetaInfo() {
        java.lang.Object ref = payloadMetaInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          payloadMetaInfo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string payloadMetaInfo = 1;</code>
       */
      public com.google.protobuf.ByteString
          getPayloadMetaInfoBytes() {
        java.lang.Object ref = payloadMetaInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          payloadMetaInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string payloadMetaInfo = 1;</code>
       */
      public Builder setPayloadMetaInfo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        payloadMetaInfo_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string payloadMetaInfo = 1;</code>
       */
      public Builder clearPayloadMetaInfo() {
        
        payloadMetaInfo_ = getDefaultInstance().getPayloadMetaInfo();
        onChanged();
        return this;
      }
      /**
       * <code>optional string payloadMetaInfo = 1;</code>
       */
      public Builder setPayloadMetaInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        payloadMetaInfo_ = value;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList tags_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureTagsIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          tags_ = new com.google.protobuf.LazyStringArrayList(tags_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated string tags = 2;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getTagsList() {
        return tags_.getUnmodifiableView();
      }
      /**
       * <code>repeated string tags = 2;</code>
       */
      public int getTagsCount() {
        return tags_.size();
      }
      /**
       * <code>repeated string tags = 2;</code>
       */
      public java.lang.String getTags(int index) {
        return tags_.get(index);
      }
      /**
       * <code>repeated string tags = 2;</code>
       */
      public com.google.protobuf.ByteString
          getTagsBytes(int index) {
        return tags_.getByteString(index);
      }
      /**
       * <code>repeated string tags = 2;</code>
       */
      public Builder setTags(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureTagsIsMutable();
        tags_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string tags = 2;</code>
       */
      public Builder addTags(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureTagsIsMutable();
        tags_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string tags = 2;</code>
       */
      public Builder addAllTags(
          java.lang.Iterable<java.lang.String> values) {
        ensureTagsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, tags_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string tags = 2;</code>
       */
      public Builder clearTags() {
        tags_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string tags = 2;</code>
       */
      public Builder addTagsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        ensureTagsIsMutable();
        tags_.add(value);
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.MessageMataInfo)
    }

    // @@protoc_insertion_point(class_scope:cluster.MessageMataInfo)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MessageMataInfo>
        PARSER = new com.google.protobuf.AbstractParser<MessageMataInfo>() {
      public MessageMataInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new MessageMataInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MessageMataInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MessageMataInfo> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.MessageMataInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SubscriptionProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.SubscriptionProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string sessionId = 1;</code>
     */
    java.lang.String getSessionId();
    /**
     * <code>optional string sessionId = 1;</code>
     */
    com.google.protobuf.ByteString
        getSessionIdBytes();

    /**
     * <code>optional int32 subscriptionId = 2;</code>
     */
    int getSubscriptionId();

    /**
     * <code>optional string entityType = 3;</code>
     */
    java.lang.String getEntityType();
    /**
     * <code>optional string entityType = 3;</code>
     */
    com.google.protobuf.ByteString
        getEntityTypeBytes();

    /**
     * <code>optional string tenantId = 4;</code>
     */
    java.lang.String getTenantId();
    /**
     * <code>optional string tenantId = 4;</code>
     */
    com.google.protobuf.ByteString
        getTenantIdBytes();

    /**
     * <code>optional string entityId = 5;</code>
     */
    java.lang.String getEntityId();
    /**
     * <code>optional string entityId = 5;</code>
     */
    com.google.protobuf.ByteString
        getEntityIdBytes();

    /**
     * <code>optional string type = 6;</code>
     */
    java.lang.String getType();
    /**
     * <code>optional string type = 6;</code>
     */
    com.google.protobuf.ByteString
        getTypeBytes();

    /**
     * <code>optional bool allKeys = 7;</code>
     */
    boolean getAllKeys();

    /**
     * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
     */
    java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto> 
        getKeyStatesList();
    /**
     * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto getKeyStates(int index);
    /**
     * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
     */
    int getKeyStatesCount();
    /**
     * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
     */
    java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProtoOrBuilder> 
        getKeyStatesOrBuilderList();
    /**
     * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProtoOrBuilder getKeyStatesOrBuilder(
        int index);

    /**
     * <code>optional string scope = 9;</code>
     */
    java.lang.String getScope();
    /**
     * <code>optional string scope = 9;</code>
     */
    com.google.protobuf.ByteString
        getScopeBytes();
  }
  /**
   * <pre>
   * Messages related to CLUSTER_TELEMETRY_MESSAGE
   * </pre>
   *
   * Protobuf type {@code cluster.SubscriptionProto}
   */
  public  static final class SubscriptionProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.SubscriptionProto)
      SubscriptionProtoOrBuilder {
    // Use SubscriptionProto.newBuilder() to construct.
    private SubscriptionProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SubscriptionProto() {
      sessionId_ = "";
      subscriptionId_ = 0;
      entityType_ = "";
      tenantId_ = "";
      entityId_ = "";
      type_ = "";
      allKeys_ = false;
      keyStates_ = java.util.Collections.emptyList();
      scope_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private SubscriptionProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              sessionId_ = s;
              break;
            }
            case 16: {

              subscriptionId_ = input.readInt32();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              entityType_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              tenantId_ = s;
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              entityId_ = s;
              break;
            }
            case 50: {
              java.lang.String s = input.readStringRequireUtf8();

              type_ = s;
              break;
            }
            case 56: {

              allKeys_ = input.readBool();
              break;
            }
            case 66: {
              if (!((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
                keyStates_ = new java.util.ArrayList<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto>();
                mutable_bitField0_ |= 0x00000080;
              }
              keyStates_.add(
                  input.readMessage(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.parser(), extensionRegistry));
              break;
            }
            case 74: {
              java.lang.String s = input.readStringRequireUtf8();

              scope_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
          keyStates_ = java.util.Collections.unmodifiableList(keyStates_);
        }
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto.Builder.class);
    }

    private int bitField0_;
    public static final int SESSIONID_FIELD_NUMBER = 1;
    private volatile java.lang.Object sessionId_;
    /**
     * <code>optional string sessionId = 1;</code>
     */
    public java.lang.String getSessionId() {
      java.lang.Object ref = sessionId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sessionId_ = s;
        return s;
      }
    }
    /**
     * <code>optional string sessionId = 1;</code>
     */
    public com.google.protobuf.ByteString
        getSessionIdBytes() {
      java.lang.Object ref = sessionId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sessionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SUBSCRIPTIONID_FIELD_NUMBER = 2;
    private int subscriptionId_;
    /**
     * <code>optional int32 subscriptionId = 2;</code>
     */
    public int getSubscriptionId() {
      return subscriptionId_;
    }

    public static final int ENTITYTYPE_FIELD_NUMBER = 3;
    private volatile java.lang.Object entityType_;
    /**
     * <code>optional string entityType = 3;</code>
     */
    public java.lang.String getEntityType() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        entityType_ = s;
        return s;
      }
    }
    /**
     * <code>optional string entityType = 3;</code>
     */
    public com.google.protobuf.ByteString
        getEntityTypeBytes() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        entityType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TENANTID_FIELD_NUMBER = 4;
    private volatile java.lang.Object tenantId_;
    /**
     * <code>optional string tenantId = 4;</code>
     */
    public java.lang.String getTenantId() {
      java.lang.Object ref = tenantId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tenantId_ = s;
        return s;
      }
    }
    /**
     * <code>optional string tenantId = 4;</code>
     */
    public com.google.protobuf.ByteString
        getTenantIdBytes() {
      java.lang.Object ref = tenantId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tenantId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENTITYID_FIELD_NUMBER = 5;
    private volatile java.lang.Object entityId_;
    /**
     * <code>optional string entityId = 5;</code>
     */
    public java.lang.String getEntityId() {
      java.lang.Object ref = entityId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        entityId_ = s;
        return s;
      }
    }
    /**
     * <code>optional string entityId = 5;</code>
     */
    public com.google.protobuf.ByteString
        getEntityIdBytes() {
      java.lang.Object ref = entityId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        entityId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TYPE_FIELD_NUMBER = 6;
    private volatile java.lang.Object type_;
    /**
     * <code>optional string type = 6;</code>
     */
    public java.lang.String getType() {
      java.lang.Object ref = type_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        type_ = s;
        return s;
      }
    }
    /**
     * <code>optional string type = 6;</code>
     */
    public com.google.protobuf.ByteString
        getTypeBytes() {
      java.lang.Object ref = type_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        type_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ALLKEYS_FIELD_NUMBER = 7;
    private boolean allKeys_;
    /**
     * <code>optional bool allKeys = 7;</code>
     */
    public boolean getAllKeys() {
      return allKeys_;
    }

    public static final int KEYSTATES_FIELD_NUMBER = 8;
    private java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto> keyStates_;
    /**
     * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
     */
    public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto> getKeyStatesList() {
      return keyStates_;
    }
    /**
     * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
     */
    public java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProtoOrBuilder> 
        getKeyStatesOrBuilderList() {
      return keyStates_;
    }
    /**
     * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
     */
    public int getKeyStatesCount() {
      return keyStates_.size();
    }
    /**
     * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto getKeyStates(int index) {
      return keyStates_.get(index);
    }
    /**
     * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProtoOrBuilder getKeyStatesOrBuilder(
        int index) {
      return keyStates_.get(index);
    }

    public static final int SCOPE_FIELD_NUMBER = 9;
    private volatile java.lang.Object scope_;
    /**
     * <code>optional string scope = 9;</code>
     */
    public java.lang.String getScope() {
      java.lang.Object ref = scope_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        scope_ = s;
        return s;
      }
    }
    /**
     * <code>optional string scope = 9;</code>
     */
    public com.google.protobuf.ByteString
        getScopeBytes() {
      java.lang.Object ref = scope_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        scope_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getSessionIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, sessionId_);
      }
      if (subscriptionId_ != 0) {
        output.writeInt32(2, subscriptionId_);
      }
      if (!getEntityTypeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, entityType_);
      }
      if (!getTenantIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, tenantId_);
      }
      if (!getEntityIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, entityId_);
      }
      if (!getTypeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, type_);
      }
      if (allKeys_ != false) {
        output.writeBool(7, allKeys_);
      }
      for (int i = 0; i < keyStates_.size(); i++) {
        output.writeMessage(8, keyStates_.get(i));
      }
      if (!getScopeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, scope_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getSessionIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, sessionId_);
      }
      if (subscriptionId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, subscriptionId_);
      }
      if (!getEntityTypeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, entityType_);
      }
      if (!getTenantIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, tenantId_);
      }
      if (!getEntityIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, entityId_);
      }
      if (!getTypeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, type_);
      }
      if (allKeys_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(7, allKeys_);
      }
      for (int i = 0; i < keyStates_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, keyStates_.get(i));
      }
      if (!getScopeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, scope_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto) obj;

      boolean result = true;
      result = result && getSessionId()
          .equals(other.getSessionId());
      result = result && (getSubscriptionId()
          == other.getSubscriptionId());
      result = result && getEntityType()
          .equals(other.getEntityType());
      result = result && getTenantId()
          .equals(other.getTenantId());
      result = result && getEntityId()
          .equals(other.getEntityId());
      result = result && getType()
          .equals(other.getType());
      result = result && (getAllKeys()
          == other.getAllKeys());
      result = result && getKeyStatesList()
          .equals(other.getKeyStatesList());
      result = result && getScope()
          .equals(other.getScope());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + SESSIONID_FIELD_NUMBER;
      hash = (53 * hash) + getSessionId().hashCode();
      hash = (37 * hash) + SUBSCRIPTIONID_FIELD_NUMBER;
      hash = (53 * hash) + getSubscriptionId();
      hash = (37 * hash) + ENTITYTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getEntityType().hashCode();
      hash = (37 * hash) + TENANTID_FIELD_NUMBER;
      hash = (53 * hash) + getTenantId().hashCode();
      hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
      hash = (53 * hash) + getEntityId().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType().hashCode();
      hash = (37 * hash) + ALLKEYS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getAllKeys());
      if (getKeyStatesCount() > 0) {
        hash = (37 * hash) + KEYSTATES_FIELD_NUMBER;
        hash = (53 * hash) + getKeyStatesList().hashCode();
      }
      hash = (37 * hash) + SCOPE_FIELD_NUMBER;
      hash = (53 * hash) + getScope().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Messages related to CLUSTER_TELEMETRY_MESSAGE
     * </pre>
     *
     * Protobuf type {@code cluster.SubscriptionProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.SubscriptionProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getKeyStatesFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        sessionId_ = "";

        subscriptionId_ = 0;

        entityType_ = "";

        tenantId_ = "";

        entityId_ = "";

        type_ = "";

        allKeys_ = false;

        if (keyStatesBuilder_ == null) {
          keyStates_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000080);
        } else {
          keyStatesBuilder_.clear();
        }
        scope_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.sessionId_ = sessionId_;
        result.subscriptionId_ = subscriptionId_;
        result.entityType_ = entityType_;
        result.tenantId_ = tenantId_;
        result.entityId_ = entityId_;
        result.type_ = type_;
        result.allKeys_ = allKeys_;
        if (keyStatesBuilder_ == null) {
          if (((bitField0_ & 0x00000080) == 0x00000080)) {
            keyStates_ = java.util.Collections.unmodifiableList(keyStates_);
            bitField0_ = (bitField0_ & ~0x00000080);
          }
          result.keyStates_ = keyStates_;
        } else {
          result.keyStates_ = keyStatesBuilder_.build();
        }
        result.scope_ = scope_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto.getDefaultInstance()) return this;
        if (!other.getSessionId().isEmpty()) {
          sessionId_ = other.sessionId_;
          onChanged();
        }
        if (other.getSubscriptionId() != 0) {
          setSubscriptionId(other.getSubscriptionId());
        }
        if (!other.getEntityType().isEmpty()) {
          entityType_ = other.entityType_;
          onChanged();
        }
        if (!other.getTenantId().isEmpty()) {
          tenantId_ = other.tenantId_;
          onChanged();
        }
        if (!other.getEntityId().isEmpty()) {
          entityId_ = other.entityId_;
          onChanged();
        }
        if (!other.getType().isEmpty()) {
          type_ = other.type_;
          onChanged();
        }
        if (other.getAllKeys() != false) {
          setAllKeys(other.getAllKeys());
        }
        if (keyStatesBuilder_ == null) {
          if (!other.keyStates_.isEmpty()) {
            if (keyStates_.isEmpty()) {
              keyStates_ = other.keyStates_;
              bitField0_ = (bitField0_ & ~0x00000080);
            } else {
              ensureKeyStatesIsMutable();
              keyStates_.addAll(other.keyStates_);
            }
            onChanged();
          }
        } else {
          if (!other.keyStates_.isEmpty()) {
            if (keyStatesBuilder_.isEmpty()) {
              keyStatesBuilder_.dispose();
              keyStatesBuilder_ = null;
              keyStates_ = other.keyStates_;
              bitField0_ = (bitField0_ & ~0x00000080);
              keyStatesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getKeyStatesFieldBuilder() : null;
            } else {
              keyStatesBuilder_.addAllMessages(other.keyStates_);
            }
          }
        }
        if (!other.getScope().isEmpty()) {
          scope_ = other.scope_;
          onChanged();
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object sessionId_ = "";
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public java.lang.String getSessionId() {
        java.lang.Object ref = sessionId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          sessionId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public com.google.protobuf.ByteString
          getSessionIdBytes() {
        java.lang.Object ref = sessionId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sessionId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder setSessionId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        sessionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder clearSessionId() {
        
        sessionId_ = getDefaultInstance().getSessionId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder setSessionIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        sessionId_ = value;
        onChanged();
        return this;
      }

      private int subscriptionId_ ;
      /**
       * <code>optional int32 subscriptionId = 2;</code>
       */
      public int getSubscriptionId() {
        return subscriptionId_;
      }
      /**
       * <code>optional int32 subscriptionId = 2;</code>
       */
      public Builder setSubscriptionId(int value) {
        
        subscriptionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 subscriptionId = 2;</code>
       */
      public Builder clearSubscriptionId() {
        
        subscriptionId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object entityType_ = "";
      /**
       * <code>optional string entityType = 3;</code>
       */
      public java.lang.String getEntityType() {
        java.lang.Object ref = entityType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          entityType_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string entityType = 3;</code>
       */
      public com.google.protobuf.ByteString
          getEntityTypeBytes() {
        java.lang.Object ref = entityType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          entityType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string entityType = 3;</code>
       */
      public Builder setEntityType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        entityType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 3;</code>
       */
      public Builder clearEntityType() {
        
        entityType_ = getDefaultInstance().getEntityType();
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 3;</code>
       */
      public Builder setEntityTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        entityType_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object tenantId_ = "";
      /**
       * <code>optional string tenantId = 4;</code>
       */
      public java.lang.String getTenantId() {
        java.lang.Object ref = tenantId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          tenantId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string tenantId = 4;</code>
       */
      public com.google.protobuf.ByteString
          getTenantIdBytes() {
        java.lang.Object ref = tenantId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tenantId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string tenantId = 4;</code>
       */
      public Builder setTenantId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        tenantId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string tenantId = 4;</code>
       */
      public Builder clearTenantId() {
        
        tenantId_ = getDefaultInstance().getTenantId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string tenantId = 4;</code>
       */
      public Builder setTenantIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        tenantId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object entityId_ = "";
      /**
       * <code>optional string entityId = 5;</code>
       */
      public java.lang.String getEntityId() {
        java.lang.Object ref = entityId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          entityId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string entityId = 5;</code>
       */
      public com.google.protobuf.ByteString
          getEntityIdBytes() {
        java.lang.Object ref = entityId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          entityId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string entityId = 5;</code>
       */
      public Builder setEntityId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityId = 5;</code>
       */
      public Builder clearEntityId() {
        
        entityId_ = getDefaultInstance().getEntityId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityId = 5;</code>
       */
      public Builder setEntityIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        entityId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object type_ = "";
      /**
       * <code>optional string type = 6;</code>
       */
      public java.lang.String getType() {
        java.lang.Object ref = type_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          type_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string type = 6;</code>
       */
      public com.google.protobuf.ByteString
          getTypeBytes() {
        java.lang.Object ref = type_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          type_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string type = 6;</code>
       */
      public Builder setType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string type = 6;</code>
       */
      public Builder clearType() {
        
        type_ = getDefaultInstance().getType();
        onChanged();
        return this;
      }
      /**
       * <code>optional string type = 6;</code>
       */
      public Builder setTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        type_ = value;
        onChanged();
        return this;
      }

      private boolean allKeys_ ;
      /**
       * <code>optional bool allKeys = 7;</code>
       */
      public boolean getAllKeys() {
        return allKeys_;
      }
      /**
       * <code>optional bool allKeys = 7;</code>
       */
      public Builder setAllKeys(boolean value) {
        
        allKeys_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool allKeys = 7;</code>
       */
      public Builder clearAllKeys() {
        
        allKeys_ = false;
        onChanged();
        return this;
      }

      private java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto> keyStates_ =
        java.util.Collections.emptyList();
      private void ensureKeyStatesIsMutable() {
        if (!((bitField0_ & 0x00000080) == 0x00000080)) {
          keyStates_ = new java.util.ArrayList<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto>(keyStates_);
          bitField0_ |= 0x00000080;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProtoOrBuilder> keyStatesBuilder_;

      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto> getKeyStatesList() {
        if (keyStatesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(keyStates_);
        } else {
          return keyStatesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public int getKeyStatesCount() {
        if (keyStatesBuilder_ == null) {
          return keyStates_.size();
        } else {
          return keyStatesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto getKeyStates(int index) {
        if (keyStatesBuilder_ == null) {
          return keyStates_.get(index);
        } else {
          return keyStatesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public Builder setKeyStates(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto value) {
        if (keyStatesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureKeyStatesIsMutable();
          keyStates_.set(index, value);
          onChanged();
        } else {
          keyStatesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public Builder setKeyStates(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder builderForValue) {
        if (keyStatesBuilder_ == null) {
          ensureKeyStatesIsMutable();
          keyStates_.set(index, builderForValue.build());
          onChanged();
        } else {
          keyStatesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public Builder addKeyStates(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto value) {
        if (keyStatesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureKeyStatesIsMutable();
          keyStates_.add(value);
          onChanged();
        } else {
          keyStatesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public Builder addKeyStates(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto value) {
        if (keyStatesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureKeyStatesIsMutable();
          keyStates_.add(index, value);
          onChanged();
        } else {
          keyStatesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public Builder addKeyStates(
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder builderForValue) {
        if (keyStatesBuilder_ == null) {
          ensureKeyStatesIsMutable();
          keyStates_.add(builderForValue.build());
          onChanged();
        } else {
          keyStatesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public Builder addKeyStates(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder builderForValue) {
        if (keyStatesBuilder_ == null) {
          ensureKeyStatesIsMutable();
          keyStates_.add(index, builderForValue.build());
          onChanged();
        } else {
          keyStatesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public Builder addAllKeyStates(
          java.lang.Iterable<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto> values) {
        if (keyStatesBuilder_ == null) {
          ensureKeyStatesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, keyStates_);
          onChanged();
        } else {
          keyStatesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public Builder clearKeyStates() {
        if (keyStatesBuilder_ == null) {
          keyStates_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000080);
          onChanged();
        } else {
          keyStatesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public Builder removeKeyStates(int index) {
        if (keyStatesBuilder_ == null) {
          ensureKeyStatesIsMutable();
          keyStates_.remove(index);
          onChanged();
        } else {
          keyStatesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder getKeyStatesBuilder(
          int index) {
        return getKeyStatesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProtoOrBuilder getKeyStatesOrBuilder(
          int index) {
        if (keyStatesBuilder_ == null) {
          return keyStates_.get(index);  } else {
          return keyStatesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProtoOrBuilder> 
           getKeyStatesOrBuilderList() {
        if (keyStatesBuilder_ != null) {
          return keyStatesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(keyStates_);
        }
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder addKeyStatesBuilder() {
        return getKeyStatesFieldBuilder().addBuilder(
            org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.getDefaultInstance());
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder addKeyStatesBuilder(
          int index) {
        return getKeyStatesFieldBuilder().addBuilder(
            index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.getDefaultInstance());
      }
      /**
       * <code>repeated .cluster.SubscriptionKetStateProto keyStates = 8;</code>
       */
      public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder> 
           getKeyStatesBuilderList() {
        return getKeyStatesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProtoOrBuilder> 
          getKeyStatesFieldBuilder() {
        if (keyStatesBuilder_ == null) {
          keyStatesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProtoOrBuilder>(
                  keyStates_,
                  ((bitField0_ & 0x00000080) == 0x00000080),
                  getParentForChildren(),
                  isClean());
          keyStates_ = null;
        }
        return keyStatesBuilder_;
      }

      private java.lang.Object scope_ = "";
      /**
       * <code>optional string scope = 9;</code>
       */
      public java.lang.String getScope() {
        java.lang.Object ref = scope_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          scope_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string scope = 9;</code>
       */
      public com.google.protobuf.ByteString
          getScopeBytes() {
        java.lang.Object ref = scope_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          scope_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string scope = 9;</code>
       */
      public Builder setScope(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        scope_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string scope = 9;</code>
       */
      public Builder clearScope() {
        
        scope_ = getDefaultInstance().getScope();
        onChanged();
        return this;
      }
      /**
       * <code>optional string scope = 9;</code>
       */
      public Builder setScopeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        scope_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.SubscriptionProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.SubscriptionProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SubscriptionProto>
        PARSER = new com.google.protobuf.AbstractParser<SubscriptionProto>() {
      public SubscriptionProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new SubscriptionProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SubscriptionProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SubscriptionProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SubscriptionUpdateProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.SubscriptionUpdateProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string sessionId = 1;</code>
     */
    java.lang.String getSessionId();
    /**
     * <code>optional string sessionId = 1;</code>
     */
    com.google.protobuf.ByteString
        getSessionIdBytes();

    /**
     * <code>optional int32 subscriptionId = 2;</code>
     */
    int getSubscriptionId();

    /**
     * <code>optional int32 errorCode = 3;</code>
     */
    int getErrorCode();

    /**
     * <code>optional string errorMsg = 4;</code>
     */
    java.lang.String getErrorMsg();
    /**
     * <code>optional string errorMsg = 4;</code>
     */
    com.google.protobuf.ByteString
        getErrorMsgBytes();

    /**
     * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
     */
    java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto> 
        getDataList();
    /**
     * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto getData(int index);
    /**
     * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
     */
    int getDataCount();
    /**
     * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
     */
    java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProtoOrBuilder> 
        getDataOrBuilderList();
    /**
     * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProtoOrBuilder getDataOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code cluster.SubscriptionUpdateProto}
   */
  public  static final class SubscriptionUpdateProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.SubscriptionUpdateProto)
      SubscriptionUpdateProtoOrBuilder {
    // Use SubscriptionUpdateProto.newBuilder() to construct.
    private SubscriptionUpdateProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SubscriptionUpdateProto() {
      sessionId_ = "";
      subscriptionId_ = 0;
      errorCode_ = 0;
      errorMsg_ = "";
      data_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private SubscriptionUpdateProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              sessionId_ = s;
              break;
            }
            case 16: {

              subscriptionId_ = input.readInt32();
              break;
            }
            case 24: {

              errorCode_ = input.readInt32();
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              errorMsg_ = s;
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                data_ = new java.util.ArrayList<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto>();
                mutable_bitField0_ |= 0x00000010;
              }
              data_.add(
                  input.readMessage(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
          data_ = java.util.Collections.unmodifiableList(data_);
        }
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionUpdateProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionUpdateProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto.Builder.class);
    }

    private int bitField0_;
    public static final int SESSIONID_FIELD_NUMBER = 1;
    private volatile java.lang.Object sessionId_;
    /**
     * <code>optional string sessionId = 1;</code>
     */
    public java.lang.String getSessionId() {
      java.lang.Object ref = sessionId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sessionId_ = s;
        return s;
      }
    }
    /**
     * <code>optional string sessionId = 1;</code>
     */
    public com.google.protobuf.ByteString
        getSessionIdBytes() {
      java.lang.Object ref = sessionId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sessionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SUBSCRIPTIONID_FIELD_NUMBER = 2;
    private int subscriptionId_;
    /**
     * <code>optional int32 subscriptionId = 2;</code>
     */
    public int getSubscriptionId() {
      return subscriptionId_;
    }

    public static final int ERRORCODE_FIELD_NUMBER = 3;
    private int errorCode_;
    /**
     * <code>optional int32 errorCode = 3;</code>
     */
    public int getErrorCode() {
      return errorCode_;
    }

    public static final int ERRORMSG_FIELD_NUMBER = 4;
    private volatile java.lang.Object errorMsg_;
    /**
     * <code>optional string errorMsg = 4;</code>
     */
    public java.lang.String getErrorMsg() {
      java.lang.Object ref = errorMsg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorMsg_ = s;
        return s;
      }
    }
    /**
     * <code>optional string errorMsg = 4;</code>
     */
    public com.google.protobuf.ByteString
        getErrorMsgBytes() {
      java.lang.Object ref = errorMsg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorMsg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DATA_FIELD_NUMBER = 5;
    private java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto> data_;
    /**
     * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
     */
    public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto> getDataList() {
      return data_;
    }
    /**
     * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
     */
    public java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProtoOrBuilder> 
        getDataOrBuilderList() {
      return data_;
    }
    /**
     * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
     */
    public int getDataCount() {
      return data_.size();
    }
    /**
     * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto getData(int index) {
      return data_.get(index);
    }
    /**
     * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProtoOrBuilder getDataOrBuilder(
        int index) {
      return data_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getSessionIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, sessionId_);
      }
      if (subscriptionId_ != 0) {
        output.writeInt32(2, subscriptionId_);
      }
      if (errorCode_ != 0) {
        output.writeInt32(3, errorCode_);
      }
      if (!getErrorMsgBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, errorMsg_);
      }
      for (int i = 0; i < data_.size(); i++) {
        output.writeMessage(5, data_.get(i));
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getSessionIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, sessionId_);
      }
      if (subscriptionId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, subscriptionId_);
      }
      if (errorCode_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, errorCode_);
      }
      if (!getErrorMsgBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, errorMsg_);
      }
      for (int i = 0; i < data_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, data_.get(i));
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto) obj;

      boolean result = true;
      result = result && getSessionId()
          .equals(other.getSessionId());
      result = result && (getSubscriptionId()
          == other.getSubscriptionId());
      result = result && (getErrorCode()
          == other.getErrorCode());
      result = result && getErrorMsg()
          .equals(other.getErrorMsg());
      result = result && getDataList()
          .equals(other.getDataList());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + SESSIONID_FIELD_NUMBER;
      hash = (53 * hash) + getSessionId().hashCode();
      hash = (37 * hash) + SUBSCRIPTIONID_FIELD_NUMBER;
      hash = (53 * hash) + getSubscriptionId();
      hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
      hash = (53 * hash) + getErrorCode();
      hash = (37 * hash) + ERRORMSG_FIELD_NUMBER;
      hash = (53 * hash) + getErrorMsg().hashCode();
      if (getDataCount() > 0) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + getDataList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.SubscriptionUpdateProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.SubscriptionUpdateProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionUpdateProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionUpdateProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDataFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        sessionId_ = "";

        subscriptionId_ = 0;

        errorCode_ = 0;

        errorMsg_ = "";

        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          dataBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionUpdateProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.sessionId_ = sessionId_;
        result.subscriptionId_ = subscriptionId_;
        result.errorCode_ = errorCode_;
        result.errorMsg_ = errorMsg_;
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010)) {
            data_ = java.util.Collections.unmodifiableList(data_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.data_ = data_;
        } else {
          result.data_ = dataBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto.getDefaultInstance()) return this;
        if (!other.getSessionId().isEmpty()) {
          sessionId_ = other.sessionId_;
          onChanged();
        }
        if (other.getSubscriptionId() != 0) {
          setSubscriptionId(other.getSubscriptionId());
        }
        if (other.getErrorCode() != 0) {
          setErrorCode(other.getErrorCode());
        }
        if (!other.getErrorMsg().isEmpty()) {
          errorMsg_ = other.errorMsg_;
          onChanged();
        }
        if (dataBuilder_ == null) {
          if (!other.data_.isEmpty()) {
            if (data_.isEmpty()) {
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureDataIsMutable();
              data_.addAll(other.data_);
            }
            onChanged();
          }
        } else {
          if (!other.data_.isEmpty()) {
            if (dataBuilder_.isEmpty()) {
              dataBuilder_.dispose();
              dataBuilder_ = null;
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000010);
              dataBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDataFieldBuilder() : null;
            } else {
              dataBuilder_.addAllMessages(other.data_);
            }
          }
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object sessionId_ = "";
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public java.lang.String getSessionId() {
        java.lang.Object ref = sessionId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          sessionId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public com.google.protobuf.ByteString
          getSessionIdBytes() {
        java.lang.Object ref = sessionId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sessionId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder setSessionId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        sessionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder clearSessionId() {
        
        sessionId_ = getDefaultInstance().getSessionId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder setSessionIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        sessionId_ = value;
        onChanged();
        return this;
      }

      private int subscriptionId_ ;
      /**
       * <code>optional int32 subscriptionId = 2;</code>
       */
      public int getSubscriptionId() {
        return subscriptionId_;
      }
      /**
       * <code>optional int32 subscriptionId = 2;</code>
       */
      public Builder setSubscriptionId(int value) {
        
        subscriptionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 subscriptionId = 2;</code>
       */
      public Builder clearSubscriptionId() {
        
        subscriptionId_ = 0;
        onChanged();
        return this;
      }

      private int errorCode_ ;
      /**
       * <code>optional int32 errorCode = 3;</code>
       */
      public int getErrorCode() {
        return errorCode_;
      }
      /**
       * <code>optional int32 errorCode = 3;</code>
       */
      public Builder setErrorCode(int value) {
        
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 errorCode = 3;</code>
       */
      public Builder clearErrorCode() {
        
        errorCode_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object errorMsg_ = "";
      /**
       * <code>optional string errorMsg = 4;</code>
       */
      public java.lang.String getErrorMsg() {
        java.lang.Object ref = errorMsg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          errorMsg_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string errorMsg = 4;</code>
       */
      public com.google.protobuf.ByteString
          getErrorMsgBytes() {
        java.lang.Object ref = errorMsg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          errorMsg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string errorMsg = 4;</code>
       */
      public Builder setErrorMsg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        errorMsg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string errorMsg = 4;</code>
       */
      public Builder clearErrorMsg() {
        
        errorMsg_ = getDefaultInstance().getErrorMsg();
        onChanged();
        return this;
      }
      /**
       * <code>optional string errorMsg = 4;</code>
       */
      public Builder setErrorMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        errorMsg_ = value;
        onChanged();
        return this;
      }

      private java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto> data_ =
        java.util.Collections.emptyList();
      private void ensureDataIsMutable() {
        if (!((bitField0_ & 0x00000010) == 0x00000010)) {
          data_ = new java.util.ArrayList<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto>(data_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProtoOrBuilder> dataBuilder_;

      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto> getDataList() {
        if (dataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(data_);
        } else {
          return dataBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public int getDataCount() {
        if (dataBuilder_ == null) {
          return data_.size();
        } else {
          return dataBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto getData(int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);
        } else {
          return dataBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public Builder setData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.set(index, value);
          onChanged();
        } else {
          dataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public Builder setData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.set(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public Builder addData(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(value);
          onChanged();
        } else {
          dataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public Builder addData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(index, value);
          onChanged();
        } else {
          dataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public Builder addData(
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public Builder addData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public Builder addAllData(
          java.lang.Iterable<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto> values) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, data_);
          onChanged();
        } else {
          dataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          dataBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public Builder removeData(int index) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.remove(index);
          onChanged();
        } else {
          dataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder getDataBuilder(
          int index) {
        return getDataFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProtoOrBuilder getDataOrBuilder(
          int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);  } else {
          return dataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProtoOrBuilder> 
           getDataOrBuilderList() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(data_);
        }
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder addDataBuilder() {
        return getDataFieldBuilder().addBuilder(
            org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.getDefaultInstance());
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder addDataBuilder(
          int index) {
        return getDataFieldBuilder().addBuilder(
            index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.getDefaultInstance());
      }
      /**
       * <code>repeated .cluster.SubscriptionUpdateValueListProto data = 5;</code>
       */
      public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder> 
           getDataBuilderList() {
        return getDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProtoOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProtoOrBuilder>(
                  data_,
                  ((bitField0_ & 0x00000010) == 0x00000010),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.SubscriptionUpdateProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.SubscriptionUpdateProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SubscriptionUpdateProto>
        PARSER = new com.google.protobuf.AbstractParser<SubscriptionUpdateProto>() {
      public SubscriptionUpdateProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new SubscriptionUpdateProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SubscriptionUpdateProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SubscriptionUpdateProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AttributeUpdateProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.AttributeUpdateProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string entityType = 1;</code>
     */
    java.lang.String getEntityType();
    /**
     * <code>optional string entityType = 1;</code>
     */
    com.google.protobuf.ByteString
        getEntityTypeBytes();

    /**
     * <code>optional string entityId = 2;</code>
     */
    java.lang.String getEntityId();
    /**
     * <code>optional string entityId = 2;</code>
     */
    com.google.protobuf.ByteString
        getEntityIdBytes();

    /**
     * <code>optional string scope = 3;</code>
     */
    java.lang.String getScope();
    /**
     * <code>optional string scope = 3;</code>
     */
    com.google.protobuf.ByteString
        getScopeBytes();

    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> 
        getDataList();
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto getData(int index);
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    int getDataCount();
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder> 
        getDataOrBuilderList();
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder getDataOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code cluster.AttributeUpdateProto}
   */
  public  static final class AttributeUpdateProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.AttributeUpdateProto)
      AttributeUpdateProtoOrBuilder {
    // Use AttributeUpdateProto.newBuilder() to construct.
    private AttributeUpdateProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AttributeUpdateProto() {
      entityType_ = "";
      entityId_ = "";
      scope_ = "";
      data_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private AttributeUpdateProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              entityType_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              entityId_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              scope_ = s;
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                data_ = new java.util.ArrayList<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto>();
                mutable_bitField0_ |= 0x00000008;
              }
              data_.add(
                  input.readMessage(org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          data_ = java.util.Collections.unmodifiableList(data_);
        }
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_AttributeUpdateProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_AttributeUpdateProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto.Builder.class);
    }

    private int bitField0_;
    public static final int ENTITYTYPE_FIELD_NUMBER = 1;
    private volatile java.lang.Object entityType_;
    /**
     * <code>optional string entityType = 1;</code>
     */
    public java.lang.String getEntityType() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        entityType_ = s;
        return s;
      }
    }
    /**
     * <code>optional string entityType = 1;</code>
     */
    public com.google.protobuf.ByteString
        getEntityTypeBytes() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        entityType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENTITYID_FIELD_NUMBER = 2;
    private volatile java.lang.Object entityId_;
    /**
     * <code>optional string entityId = 2;</code>
     */
    public java.lang.String getEntityId() {
      java.lang.Object ref = entityId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        entityId_ = s;
        return s;
      }
    }
    /**
     * <code>optional string entityId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getEntityIdBytes() {
      java.lang.Object ref = entityId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        entityId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SCOPE_FIELD_NUMBER = 3;
    private volatile java.lang.Object scope_;
    /**
     * <code>optional string scope = 3;</code>
     */
    public java.lang.String getScope() {
      java.lang.Object ref = scope_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        scope_ = s;
        return s;
      }
    }
    /**
     * <code>optional string scope = 3;</code>
     */
    public com.google.protobuf.ByteString
        getScopeBytes() {
      java.lang.Object ref = scope_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        scope_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DATA_FIELD_NUMBER = 4;
    private java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> data_;
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> getDataList() {
      return data_;
    }
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    public java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder> 
        getDataOrBuilderList() {
      return data_;
    }
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    public int getDataCount() {
      return data_.size();
    }
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto getData(int index) {
      return data_.get(index);
    }
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder getDataOrBuilder(
        int index) {
      return data_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getEntityTypeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, entityType_);
      }
      if (!getEntityIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, entityId_);
      }
      if (!getScopeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, scope_);
      }
      for (int i = 0; i < data_.size(); i++) {
        output.writeMessage(4, data_.get(i));
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getEntityTypeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, entityType_);
      }
      if (!getEntityIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, entityId_);
      }
      if (!getScopeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, scope_);
      }
      for (int i = 0; i < data_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, data_.get(i));
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto) obj;

      boolean result = true;
      result = result && getEntityType()
          .equals(other.getEntityType());
      result = result && getEntityId()
          .equals(other.getEntityId());
      result = result && getScope()
          .equals(other.getScope());
      result = result && getDataList()
          .equals(other.getDataList());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + ENTITYTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getEntityType().hashCode();
      hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
      hash = (53 * hash) + getEntityId().hashCode();
      hash = (37 * hash) + SCOPE_FIELD_NUMBER;
      hash = (53 * hash) + getScope().hashCode();
      if (getDataCount() > 0) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + getDataList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.AttributeUpdateProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.AttributeUpdateProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_AttributeUpdateProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_AttributeUpdateProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDataFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        entityType_ = "";

        entityId_ = "";

        scope_ = "";

        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          dataBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_AttributeUpdateProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.entityType_ = entityType_;
        result.entityId_ = entityId_;
        result.scope_ = scope_;
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008)) {
            data_ = java.util.Collections.unmodifiableList(data_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.data_ = data_;
        } else {
          result.data_ = dataBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto.getDefaultInstance()) return this;
        if (!other.getEntityType().isEmpty()) {
          entityType_ = other.entityType_;
          onChanged();
        }
        if (!other.getEntityId().isEmpty()) {
          entityId_ = other.entityId_;
          onChanged();
        }
        if (!other.getScope().isEmpty()) {
          scope_ = other.scope_;
          onChanged();
        }
        if (dataBuilder_ == null) {
          if (!other.data_.isEmpty()) {
            if (data_.isEmpty()) {
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureDataIsMutable();
              data_.addAll(other.data_);
            }
            onChanged();
          }
        } else {
          if (!other.data_.isEmpty()) {
            if (dataBuilder_.isEmpty()) {
              dataBuilder_.dispose();
              dataBuilder_ = null;
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000008);
              dataBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDataFieldBuilder() : null;
            } else {
              dataBuilder_.addAllMessages(other.data_);
            }
          }
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object entityType_ = "";
      /**
       * <code>optional string entityType = 1;</code>
       */
      public java.lang.String getEntityType() {
        java.lang.Object ref = entityType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          entityType_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public com.google.protobuf.ByteString
          getEntityTypeBytes() {
        java.lang.Object ref = entityType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          entityType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public Builder setEntityType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        entityType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public Builder clearEntityType() {
        
        entityType_ = getDefaultInstance().getEntityType();
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public Builder setEntityTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        entityType_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object entityId_ = "";
      /**
       * <code>optional string entityId = 2;</code>
       */
      public java.lang.String getEntityId() {
        java.lang.Object ref = entityId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          entityId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string entityId = 2;</code>
       */
      public com.google.protobuf.ByteString
          getEntityIdBytes() {
        java.lang.Object ref = entityId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          entityId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string entityId = 2;</code>
       */
      public Builder setEntityId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityId = 2;</code>
       */
      public Builder clearEntityId() {
        
        entityId_ = getDefaultInstance().getEntityId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityId = 2;</code>
       */
      public Builder setEntityIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        entityId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object scope_ = "";
      /**
       * <code>optional string scope = 3;</code>
       */
      public java.lang.String getScope() {
        java.lang.Object ref = scope_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          scope_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string scope = 3;</code>
       */
      public com.google.protobuf.ByteString
          getScopeBytes() {
        java.lang.Object ref = scope_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          scope_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string scope = 3;</code>
       */
      public Builder setScope(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        scope_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string scope = 3;</code>
       */
      public Builder clearScope() {
        
        scope_ = getDefaultInstance().getScope();
        onChanged();
        return this;
      }
      /**
       * <code>optional string scope = 3;</code>
       */
      public Builder setScopeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        scope_ = value;
        onChanged();
        return this;
      }

      private java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> data_ =
        java.util.Collections.emptyList();
      private void ensureDataIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          data_ = new java.util.ArrayList<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto>(data_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder> dataBuilder_;

      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> getDataList() {
        if (dataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(data_);
        } else {
          return dataBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public int getDataCount() {
        if (dataBuilder_ == null) {
          return data_.size();
        } else {
          return dataBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto getData(int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);
        } else {
          return dataBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder setData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.set(index, value);
          onChanged();
        } else {
          dataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder setData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.set(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder addData(org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(value);
          onChanged();
        } else {
          dataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder addData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(index, value);
          onChanged();
        } else {
          dataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder addData(
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder addData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder addAllData(
          java.lang.Iterable<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> values) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, data_);
          onChanged();
        } else {
          dataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          dataBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder removeData(int index) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.remove(index);
          onChanged();
        } else {
          dataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder getDataBuilder(
          int index) {
        return getDataFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder getDataOrBuilder(
          int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);  } else {
          return dataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder> 
           getDataOrBuilderList() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(data_);
        }
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder addDataBuilder() {
        return getDataFieldBuilder().addBuilder(
            org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.getDefaultInstance());
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder addDataBuilder(
          int index) {
        return getDataFieldBuilder().addBuilder(
            index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.getDefaultInstance());
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder> 
           getDataBuilderList() {
        return getDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder>(
                  data_,
                  ((bitField0_ & 0x00000008) == 0x00000008),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.AttributeUpdateProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.AttributeUpdateProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<AttributeUpdateProto>
        PARSER = new com.google.protobuf.AbstractParser<AttributeUpdateProto>() {
      public AttributeUpdateProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new AttributeUpdateProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AttributeUpdateProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AttributeUpdateProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.AttributeUpdateProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TimeseriesUpdateProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.TimeseriesUpdateProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string entityType = 1;</code>
     */
    java.lang.String getEntityType();
    /**
     * <code>optional string entityType = 1;</code>
     */
    com.google.protobuf.ByteString
        getEntityTypeBytes();

    /**
     * <code>optional string entityId = 2;</code>
     */
    java.lang.String getEntityId();
    /**
     * <code>optional string entityId = 2;</code>
     */
    com.google.protobuf.ByteString
        getEntityIdBytes();

    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> 
        getDataList();
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto getData(int index);
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    int getDataCount();
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder> 
        getDataOrBuilderList();
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder getDataOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code cluster.TimeseriesUpdateProto}
   */
  public  static final class TimeseriesUpdateProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.TimeseriesUpdateProto)
      TimeseriesUpdateProtoOrBuilder {
    // Use TimeseriesUpdateProto.newBuilder() to construct.
    private TimeseriesUpdateProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TimeseriesUpdateProto() {
      entityType_ = "";
      entityId_ = "";
      data_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private TimeseriesUpdateProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              entityType_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              entityId_ = s;
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                data_ = new java.util.ArrayList<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto>();
                mutable_bitField0_ |= 0x00000004;
              }
              data_.add(
                  input.readMessage(org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          data_ = java.util.Collections.unmodifiableList(data_);
        }
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_TimeseriesUpdateProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_TimeseriesUpdateProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto.Builder.class);
    }

    private int bitField0_;
    public static final int ENTITYTYPE_FIELD_NUMBER = 1;
    private volatile java.lang.Object entityType_;
    /**
     * <code>optional string entityType = 1;</code>
     */
    public java.lang.String getEntityType() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        entityType_ = s;
        return s;
      }
    }
    /**
     * <code>optional string entityType = 1;</code>
     */
    public com.google.protobuf.ByteString
        getEntityTypeBytes() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        entityType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENTITYID_FIELD_NUMBER = 2;
    private volatile java.lang.Object entityId_;
    /**
     * <code>optional string entityId = 2;</code>
     */
    public java.lang.String getEntityId() {
      java.lang.Object ref = entityId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        entityId_ = s;
        return s;
      }
    }
    /**
     * <code>optional string entityId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getEntityIdBytes() {
      java.lang.Object ref = entityId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        entityId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DATA_FIELD_NUMBER = 4;
    private java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> data_;
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> getDataList() {
      return data_;
    }
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    public java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder> 
        getDataOrBuilderList() {
      return data_;
    }
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    public int getDataCount() {
      return data_.size();
    }
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto getData(int index) {
      return data_.get(index);
    }
    /**
     * <code>repeated .cluster.KeyValueProto data = 4;</code>
     */
    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder getDataOrBuilder(
        int index) {
      return data_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getEntityTypeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, entityType_);
      }
      if (!getEntityIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, entityId_);
      }
      for (int i = 0; i < data_.size(); i++) {
        output.writeMessage(4, data_.get(i));
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getEntityTypeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, entityType_);
      }
      if (!getEntityIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, entityId_);
      }
      for (int i = 0; i < data_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, data_.get(i));
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto) obj;

      boolean result = true;
      result = result && getEntityType()
          .equals(other.getEntityType());
      result = result && getEntityId()
          .equals(other.getEntityId());
      result = result && getDataList()
          .equals(other.getDataList());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + ENTITYTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getEntityType().hashCode();
      hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
      hash = (53 * hash) + getEntityId().hashCode();
      if (getDataCount() > 0) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + getDataList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.TimeseriesUpdateProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.TimeseriesUpdateProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_TimeseriesUpdateProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_TimeseriesUpdateProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDataFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        entityType_ = "";

        entityId_ = "";

        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          dataBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_TimeseriesUpdateProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.entityType_ = entityType_;
        result.entityId_ = entityId_;
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            data_ = java.util.Collections.unmodifiableList(data_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.data_ = data_;
        } else {
          result.data_ = dataBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto.getDefaultInstance()) return this;
        if (!other.getEntityType().isEmpty()) {
          entityType_ = other.entityType_;
          onChanged();
        }
        if (!other.getEntityId().isEmpty()) {
          entityId_ = other.entityId_;
          onChanged();
        }
        if (dataBuilder_ == null) {
          if (!other.data_.isEmpty()) {
            if (data_.isEmpty()) {
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureDataIsMutable();
              data_.addAll(other.data_);
            }
            onChanged();
          }
        } else {
          if (!other.data_.isEmpty()) {
            if (dataBuilder_.isEmpty()) {
              dataBuilder_.dispose();
              dataBuilder_ = null;
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000004);
              dataBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDataFieldBuilder() : null;
            } else {
              dataBuilder_.addAllMessages(other.data_);
            }
          }
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object entityType_ = "";
      /**
       * <code>optional string entityType = 1;</code>
       */
      public java.lang.String getEntityType() {
        java.lang.Object ref = entityType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          entityType_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public com.google.protobuf.ByteString
          getEntityTypeBytes() {
        java.lang.Object ref = entityType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          entityType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public Builder setEntityType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        entityType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public Builder clearEntityType() {
        
        entityType_ = getDefaultInstance().getEntityType();
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public Builder setEntityTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        entityType_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object entityId_ = "";
      /**
       * <code>optional string entityId = 2;</code>
       */
      public java.lang.String getEntityId() {
        java.lang.Object ref = entityId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          entityId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string entityId = 2;</code>
       */
      public com.google.protobuf.ByteString
          getEntityIdBytes() {
        java.lang.Object ref = entityId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          entityId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string entityId = 2;</code>
       */
      public Builder setEntityId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityId = 2;</code>
       */
      public Builder clearEntityId() {
        
        entityId_ = getDefaultInstance().getEntityId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityId = 2;</code>
       */
      public Builder setEntityIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        entityId_ = value;
        onChanged();
        return this;
      }

      private java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> data_ =
        java.util.Collections.emptyList();
      private void ensureDataIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          data_ = new java.util.ArrayList<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto>(data_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder> dataBuilder_;

      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> getDataList() {
        if (dataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(data_);
        } else {
          return dataBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public int getDataCount() {
        if (dataBuilder_ == null) {
          return data_.size();
        } else {
          return dataBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto getData(int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);
        } else {
          return dataBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder setData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.set(index, value);
          onChanged();
        } else {
          dataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder setData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.set(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder addData(org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(value);
          onChanged();
        } else {
          dataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder addData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(index, value);
          onChanged();
        } else {
          dataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder addData(
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder addData(
          int index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder addAllData(
          java.lang.Iterable<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto> values) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, data_);
          onChanged();
        } else {
          dataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          dataBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public Builder removeData(int index) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.remove(index);
          onChanged();
        } else {
          dataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder getDataBuilder(
          int index) {
        return getDataFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder getDataOrBuilder(
          int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);  } else {
          return dataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public java.util.List<? extends org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder> 
           getDataOrBuilderList() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(data_);
        }
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder addDataBuilder() {
        return getDataFieldBuilder().addBuilder(
            org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.getDefaultInstance());
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder addDataBuilder(
          int index) {
        return getDataFieldBuilder().addBuilder(
            index, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.getDefaultInstance());
      }
      /**
       * <code>repeated .cluster.KeyValueProto data = 4;</code>
       */
      public java.util.List<org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder> 
           getDataBuilderList() {
        return getDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder>(
                  data_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.TimeseriesUpdateProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.TimeseriesUpdateProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TimeseriesUpdateProto>
        PARSER = new com.google.protobuf.AbstractParser<TimeseriesUpdateProto>() {
      public TimeseriesUpdateProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new TimeseriesUpdateProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TimeseriesUpdateProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TimeseriesUpdateProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.TimeseriesUpdateProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SessionCloseProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.SessionCloseProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string sessionId = 1;</code>
     */
    java.lang.String getSessionId();
    /**
     * <code>optional string sessionId = 1;</code>
     */
    com.google.protobuf.ByteString
        getSessionIdBytes();
  }
  /**
   * Protobuf type {@code cluster.SessionCloseProto}
   */
  public  static final class SessionCloseProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.SessionCloseProto)
      SessionCloseProtoOrBuilder {
    // Use SessionCloseProto.newBuilder() to construct.
    private SessionCloseProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SessionCloseProto() {
      sessionId_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private SessionCloseProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              sessionId_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SessionCloseProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SessionCloseProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto.Builder.class);
    }

    public static final int SESSIONID_FIELD_NUMBER = 1;
    private volatile java.lang.Object sessionId_;
    /**
     * <code>optional string sessionId = 1;</code>
     */
    public java.lang.String getSessionId() {
      java.lang.Object ref = sessionId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sessionId_ = s;
        return s;
      }
    }
    /**
     * <code>optional string sessionId = 1;</code>
     */
    public com.google.protobuf.ByteString
        getSessionIdBytes() {
      java.lang.Object ref = sessionId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sessionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getSessionIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, sessionId_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getSessionIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, sessionId_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto) obj;

      boolean result = true;
      result = result && getSessionId()
          .equals(other.getSessionId());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + SESSIONID_FIELD_NUMBER;
      hash = (53 * hash) + getSessionId().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.SessionCloseProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.SessionCloseProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SessionCloseProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SessionCloseProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        sessionId_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SessionCloseProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto(this);
        result.sessionId_ = sessionId_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto.getDefaultInstance()) return this;
        if (!other.getSessionId().isEmpty()) {
          sessionId_ = other.sessionId_;
          onChanged();
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object sessionId_ = "";
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public java.lang.String getSessionId() {
        java.lang.Object ref = sessionId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          sessionId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public com.google.protobuf.ByteString
          getSessionIdBytes() {
        java.lang.Object ref = sessionId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sessionId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder setSessionId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        sessionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder clearSessionId() {
        
        sessionId_ = getDefaultInstance().getSessionId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder setSessionIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        sessionId_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.SessionCloseProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.SessionCloseProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SessionCloseProto>
        PARSER = new com.google.protobuf.AbstractParser<SessionCloseProto>() {
      public SessionCloseProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new SessionCloseProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SessionCloseProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SessionCloseProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SessionCloseProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SubscriptionCloseProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.SubscriptionCloseProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string sessionId = 1;</code>
     */
    java.lang.String getSessionId();
    /**
     * <code>optional string sessionId = 1;</code>
     */
    com.google.protobuf.ByteString
        getSessionIdBytes();

    /**
     * <code>optional int32 subscriptionId = 2;</code>
     */
    int getSubscriptionId();
  }
  /**
   * Protobuf type {@code cluster.SubscriptionCloseProto}
   */
  public  static final class SubscriptionCloseProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.SubscriptionCloseProto)
      SubscriptionCloseProtoOrBuilder {
    // Use SubscriptionCloseProto.newBuilder() to construct.
    private SubscriptionCloseProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SubscriptionCloseProto() {
      sessionId_ = "";
      subscriptionId_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private SubscriptionCloseProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              sessionId_ = s;
              break;
            }
            case 16: {

              subscriptionId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionCloseProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionCloseProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto.Builder.class);
    }

    public static final int SESSIONID_FIELD_NUMBER = 1;
    private volatile java.lang.Object sessionId_;
    /**
     * <code>optional string sessionId = 1;</code>
     */
    public java.lang.String getSessionId() {
      java.lang.Object ref = sessionId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sessionId_ = s;
        return s;
      }
    }
    /**
     * <code>optional string sessionId = 1;</code>
     */
    public com.google.protobuf.ByteString
        getSessionIdBytes() {
      java.lang.Object ref = sessionId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sessionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SUBSCRIPTIONID_FIELD_NUMBER = 2;
    private int subscriptionId_;
    /**
     * <code>optional int32 subscriptionId = 2;</code>
     */
    public int getSubscriptionId() {
      return subscriptionId_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getSessionIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, sessionId_);
      }
      if (subscriptionId_ != 0) {
        output.writeInt32(2, subscriptionId_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getSessionIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, sessionId_);
      }
      if (subscriptionId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, subscriptionId_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto) obj;

      boolean result = true;
      result = result && getSessionId()
          .equals(other.getSessionId());
      result = result && (getSubscriptionId()
          == other.getSubscriptionId());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + SESSIONID_FIELD_NUMBER;
      hash = (53 * hash) + getSessionId().hashCode();
      hash = (37 * hash) + SUBSCRIPTIONID_FIELD_NUMBER;
      hash = (53 * hash) + getSubscriptionId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.SubscriptionCloseProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.SubscriptionCloseProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionCloseProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionCloseProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        sessionId_ = "";

        subscriptionId_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionCloseProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto(this);
        result.sessionId_ = sessionId_;
        result.subscriptionId_ = subscriptionId_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto.getDefaultInstance()) return this;
        if (!other.getSessionId().isEmpty()) {
          sessionId_ = other.sessionId_;
          onChanged();
        }
        if (other.getSubscriptionId() != 0) {
          setSubscriptionId(other.getSubscriptionId());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object sessionId_ = "";
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public java.lang.String getSessionId() {
        java.lang.Object ref = sessionId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          sessionId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public com.google.protobuf.ByteString
          getSessionIdBytes() {
        java.lang.Object ref = sessionId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sessionId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder setSessionId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        sessionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder clearSessionId() {
        
        sessionId_ = getDefaultInstance().getSessionId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string sessionId = 1;</code>
       */
      public Builder setSessionIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        sessionId_ = value;
        onChanged();
        return this;
      }

      private int subscriptionId_ ;
      /**
       * <code>optional int32 subscriptionId = 2;</code>
       */
      public int getSubscriptionId() {
        return subscriptionId_;
      }
      /**
       * <code>optional int32 subscriptionId = 2;</code>
       */
      public Builder setSubscriptionId(int value) {
        
        subscriptionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 subscriptionId = 2;</code>
       */
      public Builder clearSubscriptionId() {
        
        subscriptionId_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.SubscriptionCloseProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.SubscriptionCloseProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SubscriptionCloseProto>
        PARSER = new com.google.protobuf.AbstractParser<SubscriptionCloseProto>() {
      public SubscriptionCloseProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new SubscriptionCloseProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SubscriptionCloseProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SubscriptionCloseProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionCloseProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SubscriptionKetStateProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.SubscriptionKetStateProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string key = 1;</code>
     */
    java.lang.String getKey();
    /**
     * <code>optional string key = 1;</code>
     */
    com.google.protobuf.ByteString
        getKeyBytes();

    /**
     * <code>optional int64 ts = 2;</code>
     */
    long getTs();
  }
  /**
   * Protobuf type {@code cluster.SubscriptionKetStateProto}
   */
  public  static final class SubscriptionKetStateProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.SubscriptionKetStateProto)
      SubscriptionKetStateProtoOrBuilder {
    // Use SubscriptionKetStateProto.newBuilder() to construct.
    private SubscriptionKetStateProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SubscriptionKetStateProto() {
      key_ = "";
      ts_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private SubscriptionKetStateProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              key_ = s;
              break;
            }
            case 16: {

              ts_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionKetStateProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionKetStateProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder.class);
    }

    public static final int KEY_FIELD_NUMBER = 1;
    private volatile java.lang.Object key_;
    /**
     * <code>optional string key = 1;</code>
     */
    public java.lang.String getKey() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        key_ = s;
        return s;
      }
    }
    /**
     * <code>optional string key = 1;</code>
     */
    public com.google.protobuf.ByteString
        getKeyBytes() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        key_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TS_FIELD_NUMBER = 2;
    private long ts_;
    /**
     * <code>optional int64 ts = 2;</code>
     */
    public long getTs() {
      return ts_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getKeyBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, key_);
      }
      if (ts_ != 0L) {
        output.writeInt64(2, ts_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getKeyBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, key_);
      }
      if (ts_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, ts_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto) obj;

      boolean result = true;
      result = result && getKey()
          .equals(other.getKey());
      result = result && (getTs()
          == other.getTs());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + KEY_FIELD_NUMBER;
      hash = (53 * hash) + getKey().hashCode();
      hash = (37 * hash) + TS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTs());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.SubscriptionKetStateProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.SubscriptionKetStateProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionKetStateProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionKetStateProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        key_ = "";

        ts_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionKetStateProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto(this);
        result.key_ = key_;
        result.ts_ = ts_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto.getDefaultInstance()) return this;
        if (!other.getKey().isEmpty()) {
          key_ = other.key_;
          onChanged();
        }
        if (other.getTs() != 0L) {
          setTs(other.getTs());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object key_ = "";
      /**
       * <code>optional string key = 1;</code>
       */
      public java.lang.String getKey() {
        java.lang.Object ref = key_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          key_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public com.google.protobuf.ByteString
          getKeyBytes() {
        java.lang.Object ref = key_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          key_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public Builder setKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        key_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public Builder clearKey() {
        
        key_ = getDefaultInstance().getKey();
        onChanged();
        return this;
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public Builder setKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        key_ = value;
        onChanged();
        return this;
      }

      private long ts_ ;
      /**
       * <code>optional int64 ts = 2;</code>
       */
      public long getTs() {
        return ts_;
      }
      /**
       * <code>optional int64 ts = 2;</code>
       */
      public Builder setTs(long value) {
        
        ts_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ts = 2;</code>
       */
      public Builder clearTs() {
        
        ts_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.SubscriptionKetStateProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.SubscriptionKetStateProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SubscriptionKetStateProto>
        PARSER = new com.google.protobuf.AbstractParser<SubscriptionKetStateProto>() {
      public SubscriptionKetStateProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new SubscriptionKetStateProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SubscriptionKetStateProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SubscriptionKetStateProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionKetStateProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SubscriptionUpdateValueListProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.SubscriptionUpdateValueListProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string key = 1;</code>
     */
    java.lang.String getKey();
    /**
     * <code>optional string key = 1;</code>
     */
    com.google.protobuf.ByteString
        getKeyBytes();

    /**
     * <code>repeated int64 ts = 2;</code>
     */
    java.util.List<java.lang.Long> getTsList();
    /**
     * <code>repeated int64 ts = 2;</code>
     */
    int getTsCount();
    /**
     * <code>repeated int64 ts = 2;</code>
     */
    long getTs(int index);

    /**
     * <code>repeated string value = 3;</code>
     */
    java.util.List<java.lang.String>
        getValueList();
    /**
     * <code>repeated string value = 3;</code>
     */
    int getValueCount();
    /**
     * <code>repeated string value = 3;</code>
     */
    java.lang.String getValue(int index);
    /**
     * <code>repeated string value = 3;</code>
     */
    com.google.protobuf.ByteString
        getValueBytes(int index);
  }
  /**
   * Protobuf type {@code cluster.SubscriptionUpdateValueListProto}
   */
  public  static final class SubscriptionUpdateValueListProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.SubscriptionUpdateValueListProto)
      SubscriptionUpdateValueListProtoOrBuilder {
    // Use SubscriptionUpdateValueListProto.newBuilder() to construct.
    private SubscriptionUpdateValueListProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SubscriptionUpdateValueListProto() {
      key_ = "";
      ts_ = java.util.Collections.emptyList();
      value_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private SubscriptionUpdateValueListProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              key_ = s;
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                ts_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000002;
              }
              ts_.add(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002) && input.getBytesUntilLimit() > 0) {
                ts_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                ts_.add(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                value_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000004;
              }
              value_.add(s);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          ts_ = java.util.Collections.unmodifiableList(ts_);
        }
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          value_ = value_.getUnmodifiableView();
        }
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionUpdateValueListProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionUpdateValueListProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder.class);
    }

    private int bitField0_;
    public static final int KEY_FIELD_NUMBER = 1;
    private volatile java.lang.Object key_;
    /**
     * <code>optional string key = 1;</code>
     */
    public java.lang.String getKey() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        key_ = s;
        return s;
      }
    }
    /**
     * <code>optional string key = 1;</code>
     */
    public com.google.protobuf.ByteString
        getKeyBytes() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        key_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TS_FIELD_NUMBER = 2;
    private java.util.List<java.lang.Long> ts_;
    /**
     * <code>repeated int64 ts = 2;</code>
     */
    public java.util.List<java.lang.Long>
        getTsList() {
      return ts_;
    }
    /**
     * <code>repeated int64 ts = 2;</code>
     */
    public int getTsCount() {
      return ts_.size();
    }
    /**
     * <code>repeated int64 ts = 2;</code>
     */
    public long getTs(int index) {
      return ts_.get(index);
    }
    private int tsMemoizedSerializedSize = -1;

    public static final int VALUE_FIELD_NUMBER = 3;
    private com.google.protobuf.LazyStringList value_;
    /**
     * <code>repeated string value = 3;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getValueList() {
      return value_;
    }
    /**
     * <code>repeated string value = 3;</code>
     */
    public int getValueCount() {
      return value_.size();
    }
    /**
     * <code>repeated string value = 3;</code>
     */
    public java.lang.String getValue(int index) {
      return value_.get(index);
    }
    /**
     * <code>repeated string value = 3;</code>
     */
    public com.google.protobuf.ByteString
        getValueBytes(int index) {
      return value_.getByteString(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (!getKeyBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, key_);
      }
      if (getTsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(tsMemoizedSerializedSize);
      }
      for (int i = 0; i < ts_.size(); i++) {
        output.writeInt64NoTag(ts_.get(i));
      }
      for (int i = 0; i < value_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, value_.getRaw(i));
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getKeyBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, key_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < ts_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(ts_.get(i));
        }
        size += dataSize;
        if (!getTsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        tsMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < value_.size(); i++) {
          dataSize += computeStringSizeNoTag(value_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getValueList().size();
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto) obj;

      boolean result = true;
      result = result && getKey()
          .equals(other.getKey());
      result = result && getTsList()
          .equals(other.getTsList());
      result = result && getValueList()
          .equals(other.getValueList());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + KEY_FIELD_NUMBER;
      hash = (53 * hash) + getKey().hashCode();
      if (getTsCount() > 0) {
        hash = (37 * hash) + TS_FIELD_NUMBER;
        hash = (53 * hash) + getTsList().hashCode();
      }
      if (getValueCount() > 0) {
        hash = (37 * hash) + VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getValueList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.SubscriptionUpdateValueListProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.SubscriptionUpdateValueListProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionUpdateValueListProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionUpdateValueListProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        key_ = "";

        ts_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        value_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_SubscriptionUpdateValueListProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.key_ = key_;
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          ts_ = java.util.Collections.unmodifiableList(ts_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.ts_ = ts_;
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          value_ = value_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.value_ = value_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto.getDefaultInstance()) return this;
        if (!other.getKey().isEmpty()) {
          key_ = other.key_;
          onChanged();
        }
        if (!other.ts_.isEmpty()) {
          if (ts_.isEmpty()) {
            ts_ = other.ts_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureTsIsMutable();
            ts_.addAll(other.ts_);
          }
          onChanged();
        }
        if (!other.value_.isEmpty()) {
          if (value_.isEmpty()) {
            value_ = other.value_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureValueIsMutable();
            value_.addAll(other.value_);
          }
          onChanged();
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object key_ = "";
      /**
       * <code>optional string key = 1;</code>
       */
      public java.lang.String getKey() {
        java.lang.Object ref = key_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          key_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public com.google.protobuf.ByteString
          getKeyBytes() {
        java.lang.Object ref = key_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          key_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public Builder setKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        key_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public Builder clearKey() {
        
        key_ = getDefaultInstance().getKey();
        onChanged();
        return this;
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public Builder setKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        key_ = value;
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Long> ts_ = java.util.Collections.emptyList();
      private void ensureTsIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          ts_ = new java.util.ArrayList<java.lang.Long>(ts_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int64 ts = 2;</code>
       */
      public java.util.List<java.lang.Long>
          getTsList() {
        return java.util.Collections.unmodifiableList(ts_);
      }
      /**
       * <code>repeated int64 ts = 2;</code>
       */
      public int getTsCount() {
        return ts_.size();
      }
      /**
       * <code>repeated int64 ts = 2;</code>
       */
      public long getTs(int index) {
        return ts_.get(index);
      }
      /**
       * <code>repeated int64 ts = 2;</code>
       */
      public Builder setTs(
          int index, long value) {
        ensureTsIsMutable();
        ts_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 ts = 2;</code>
       */
      public Builder addTs(long value) {
        ensureTsIsMutable();
        ts_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 ts = 2;</code>
       */
      public Builder addAllTs(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureTsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ts_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 ts = 2;</code>
       */
      public Builder clearTs() {
        ts_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList value_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureValueIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          value_ = new com.google.protobuf.LazyStringArrayList(value_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated string value = 3;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getValueList() {
        return value_.getUnmodifiableView();
      }
      /**
       * <code>repeated string value = 3;</code>
       */
      public int getValueCount() {
        return value_.size();
      }
      /**
       * <code>repeated string value = 3;</code>
       */
      public java.lang.String getValue(int index) {
        return value_.get(index);
      }
      /**
       * <code>repeated string value = 3;</code>
       */
      public com.google.protobuf.ByteString
          getValueBytes(int index) {
        return value_.getByteString(index);
      }
      /**
       * <code>repeated string value = 3;</code>
       */
      public Builder setValue(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureValueIsMutable();
        value_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string value = 3;</code>
       */
      public Builder addValue(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureValueIsMutable();
        value_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string value = 3;</code>
       */
      public Builder addAllValue(
          java.lang.Iterable<java.lang.String> values) {
        ensureValueIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, value_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string value = 3;</code>
       */
      public Builder clearValue() {
        value_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string value = 3;</code>
       */
      public Builder addValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        ensureValueIsMutable();
        value_.add(value);
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.SubscriptionUpdateValueListProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.SubscriptionUpdateValueListProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SubscriptionUpdateValueListProto>
        PARSER = new com.google.protobuf.AbstractParser<SubscriptionUpdateValueListProto>() {
      public SubscriptionUpdateValueListProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new SubscriptionUpdateValueListProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SubscriptionUpdateValueListProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SubscriptionUpdateValueListProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.SubscriptionUpdateValueListProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KeyValueProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.KeyValueProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string key = 1;</code>
     */
    java.lang.String getKey();
    /**
     * <code>optional string key = 1;</code>
     */
    com.google.protobuf.ByteString
        getKeyBytes();

    /**
     * <code>optional int64 ts = 2;</code>
     */
    long getTs();

    /**
     * <code>optional int32 valueType = 3;</code>
     */
    int getValueType();

    /**
     * <code>optional string strValue = 4;</code>
     */
    java.lang.String getStrValue();
    /**
     * <code>optional string strValue = 4;</code>
     */
    com.google.protobuf.ByteString
        getStrValueBytes();

    /**
     * <code>optional int64 longValue = 5;</code>
     */
    long getLongValue();

    /**
     * <code>optional double doubleValue = 6;</code>
     */
    double getDoubleValue();

    /**
     * <code>optional bool boolValue = 7;</code>
     */
    boolean getBoolValue();
  }
  /**
   * Protobuf type {@code cluster.KeyValueProto}
   */
  public  static final class KeyValueProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.KeyValueProto)
      KeyValueProtoOrBuilder {
    // Use KeyValueProto.newBuilder() to construct.
    private KeyValueProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KeyValueProto() {
      key_ = "";
      ts_ = 0L;
      valueType_ = 0;
      strValue_ = "";
      longValue_ = 0L;
      doubleValue_ = 0D;
      boolValue_ = false;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private KeyValueProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              key_ = s;
              break;
            }
            case 16: {

              ts_ = input.readInt64();
              break;
            }
            case 24: {

              valueType_ = input.readInt32();
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              strValue_ = s;
              break;
            }
            case 40: {

              longValue_ = input.readInt64();
              break;
            }
            case 49: {

              doubleValue_ = input.readDouble();
              break;
            }
            case 56: {

              boolValue_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_KeyValueProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_KeyValueProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder.class);
    }

    public static final int KEY_FIELD_NUMBER = 1;
    private volatile java.lang.Object key_;
    /**
     * <code>optional string key = 1;</code>
     */
    public java.lang.String getKey() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        key_ = s;
        return s;
      }
    }
    /**
     * <code>optional string key = 1;</code>
     */
    public com.google.protobuf.ByteString
        getKeyBytes() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        key_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TS_FIELD_NUMBER = 2;
    private long ts_;
    /**
     * <code>optional int64 ts = 2;</code>
     */
    public long getTs() {
      return ts_;
    }

    public static final int VALUETYPE_FIELD_NUMBER = 3;
    private int valueType_;
    /**
     * <code>optional int32 valueType = 3;</code>
     */
    public int getValueType() {
      return valueType_;
    }

    public static final int STRVALUE_FIELD_NUMBER = 4;
    private volatile java.lang.Object strValue_;
    /**
     * <code>optional string strValue = 4;</code>
     */
    public java.lang.String getStrValue() {
      java.lang.Object ref = strValue_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        strValue_ = s;
        return s;
      }
    }
    /**
     * <code>optional string strValue = 4;</code>
     */
    public com.google.protobuf.ByteString
        getStrValueBytes() {
      java.lang.Object ref = strValue_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        strValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LONGVALUE_FIELD_NUMBER = 5;
    private long longValue_;
    /**
     * <code>optional int64 longValue = 5;</code>
     */
    public long getLongValue() {
      return longValue_;
    }

    public static final int DOUBLEVALUE_FIELD_NUMBER = 6;
    private double doubleValue_;
    /**
     * <code>optional double doubleValue = 6;</code>
     */
    public double getDoubleValue() {
      return doubleValue_;
    }

    public static final int BOOLVALUE_FIELD_NUMBER = 7;
    private boolean boolValue_;
    /**
     * <code>optional bool boolValue = 7;</code>
     */
    public boolean getBoolValue() {
      return boolValue_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getKeyBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, key_);
      }
      if (ts_ != 0L) {
        output.writeInt64(2, ts_);
      }
      if (valueType_ != 0) {
        output.writeInt32(3, valueType_);
      }
      if (!getStrValueBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, strValue_);
      }
      if (longValue_ != 0L) {
        output.writeInt64(5, longValue_);
      }
      if (doubleValue_ != 0D) {
        output.writeDouble(6, doubleValue_);
      }
      if (boolValue_ != false) {
        output.writeBool(7, boolValue_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getKeyBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, key_);
      }
      if (ts_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, ts_);
      }
      if (valueType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, valueType_);
      }
      if (!getStrValueBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, strValue_);
      }
      if (longValue_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, longValue_);
      }
      if (doubleValue_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(6, doubleValue_);
      }
      if (boolValue_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(7, boolValue_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto) obj;

      boolean result = true;
      result = result && getKey()
          .equals(other.getKey());
      result = result && (getTs()
          == other.getTs());
      result = result && (getValueType()
          == other.getValueType());
      result = result && getStrValue()
          .equals(other.getStrValue());
      result = result && (getLongValue()
          == other.getLongValue());
      result = result && (
          java.lang.Double.doubleToLongBits(getDoubleValue())
          == java.lang.Double.doubleToLongBits(
              other.getDoubleValue()));
      result = result && (getBoolValue()
          == other.getBoolValue());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + KEY_FIELD_NUMBER;
      hash = (53 * hash) + getKey().hashCode();
      hash = (37 * hash) + TS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTs());
      hash = (37 * hash) + VALUETYPE_FIELD_NUMBER;
      hash = (53 * hash) + getValueType();
      hash = (37 * hash) + STRVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getStrValue().hashCode();
      hash = (37 * hash) + LONGVALUE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLongValue());
      hash = (37 * hash) + DOUBLEVALUE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getDoubleValue()));
      hash = (37 * hash) + BOOLVALUE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getBoolValue());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.KeyValueProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.KeyValueProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_KeyValueProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_KeyValueProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        key_ = "";

        ts_ = 0L;

        valueType_ = 0;

        strValue_ = "";

        longValue_ = 0L;

        doubleValue_ = 0D;

        boolValue_ = false;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_KeyValueProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto(this);
        result.key_ = key_;
        result.ts_ = ts_;
        result.valueType_ = valueType_;
        result.strValue_ = strValue_;
        result.longValue_ = longValue_;
        result.doubleValue_ = doubleValue_;
        result.boolValue_ = boolValue_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto.getDefaultInstance()) return this;
        if (!other.getKey().isEmpty()) {
          key_ = other.key_;
          onChanged();
        }
        if (other.getTs() != 0L) {
          setTs(other.getTs());
        }
        if (other.getValueType() != 0) {
          setValueType(other.getValueType());
        }
        if (!other.getStrValue().isEmpty()) {
          strValue_ = other.strValue_;
          onChanged();
        }
        if (other.getLongValue() != 0L) {
          setLongValue(other.getLongValue());
        }
        if (other.getDoubleValue() != 0D) {
          setDoubleValue(other.getDoubleValue());
        }
        if (other.getBoolValue() != false) {
          setBoolValue(other.getBoolValue());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object key_ = "";
      /**
       * <code>optional string key = 1;</code>
       */
      public java.lang.String getKey() {
        java.lang.Object ref = key_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          key_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public com.google.protobuf.ByteString
          getKeyBytes() {
        java.lang.Object ref = key_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          key_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public Builder setKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        key_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public Builder clearKey() {
        
        key_ = getDefaultInstance().getKey();
        onChanged();
        return this;
      }
      /**
       * <code>optional string key = 1;</code>
       */
      public Builder setKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        key_ = value;
        onChanged();
        return this;
      }

      private long ts_ ;
      /**
       * <code>optional int64 ts = 2;</code>
       */
      public long getTs() {
        return ts_;
      }
      /**
       * <code>optional int64 ts = 2;</code>
       */
      public Builder setTs(long value) {
        
        ts_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ts = 2;</code>
       */
      public Builder clearTs() {
        
        ts_ = 0L;
        onChanged();
        return this;
      }

      private int valueType_ ;
      /**
       * <code>optional int32 valueType = 3;</code>
       */
      public int getValueType() {
        return valueType_;
      }
      /**
       * <code>optional int32 valueType = 3;</code>
       */
      public Builder setValueType(int value) {
        
        valueType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 valueType = 3;</code>
       */
      public Builder clearValueType() {
        
        valueType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object strValue_ = "";
      /**
       * <code>optional string strValue = 4;</code>
       */
      public java.lang.String getStrValue() {
        java.lang.Object ref = strValue_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          strValue_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string strValue = 4;</code>
       */
      public com.google.protobuf.ByteString
          getStrValueBytes() {
        java.lang.Object ref = strValue_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          strValue_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string strValue = 4;</code>
       */
      public Builder setStrValue(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        strValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string strValue = 4;</code>
       */
      public Builder clearStrValue() {
        
        strValue_ = getDefaultInstance().getStrValue();
        onChanged();
        return this;
      }
      /**
       * <code>optional string strValue = 4;</code>
       */
      public Builder setStrValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        strValue_ = value;
        onChanged();
        return this;
      }

      private long longValue_ ;
      /**
       * <code>optional int64 longValue = 5;</code>
       */
      public long getLongValue() {
        return longValue_;
      }
      /**
       * <code>optional int64 longValue = 5;</code>
       */
      public Builder setLongValue(long value) {
        
        longValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 longValue = 5;</code>
       */
      public Builder clearLongValue() {
        
        longValue_ = 0L;
        onChanged();
        return this;
      }

      private double doubleValue_ ;
      /**
       * <code>optional double doubleValue = 6;</code>
       */
      public double getDoubleValue() {
        return doubleValue_;
      }
      /**
       * <code>optional double doubleValue = 6;</code>
       */
      public Builder setDoubleValue(double value) {
        
        doubleValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional double doubleValue = 6;</code>
       */
      public Builder clearDoubleValue() {
        
        doubleValue_ = 0D;
        onChanged();
        return this;
      }

      private boolean boolValue_ ;
      /**
       * <code>optional bool boolValue = 7;</code>
       */
      public boolean getBoolValue() {
        return boolValue_;
      }
      /**
       * <code>optional bool boolValue = 7;</code>
       */
      public Builder setBoolValue(boolean value) {
        
        boolValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool boolValue = 7;</code>
       */
      public Builder clearBoolValue() {
        
        boolValue_ = false;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.KeyValueProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.KeyValueProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<KeyValueProto>
        PARSER = new com.google.protobuf.AbstractParser<KeyValueProto>() {
      public KeyValueProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new KeyValueProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KeyValueProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KeyValueProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.KeyValueProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FromDeviceRPCResponseProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.FromDeviceRPCResponseProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 requestIdMSB = 1;</code>
     */
    long getRequestIdMSB();

    /**
     * <code>optional int64 requestIdLSB = 2;</code>
     */
    long getRequestIdLSB();

    /**
     * <code>optional string response = 3;</code>
     */
    java.lang.String getResponse();
    /**
     * <code>optional string response = 3;</code>
     */
    com.google.protobuf.ByteString
        getResponseBytes();

    /**
     * <code>optional int32 error = 4;</code>
     */
    int getError();
  }
  /**
   * Protobuf type {@code cluster.FromDeviceRPCResponseProto}
   */
  public  static final class FromDeviceRPCResponseProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.FromDeviceRPCResponseProto)
      FromDeviceRPCResponseProtoOrBuilder {
    // Use FromDeviceRPCResponseProto.newBuilder() to construct.
    private FromDeviceRPCResponseProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FromDeviceRPCResponseProto() {
      requestIdMSB_ = 0L;
      requestIdLSB_ = 0L;
      response_ = "";
      error_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private FromDeviceRPCResponseProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              requestIdMSB_ = input.readInt64();
              break;
            }
            case 16: {

              requestIdLSB_ = input.readInt64();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              response_ = s;
              break;
            }
            case 32: {

              error_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_FromDeviceRPCResponseProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_FromDeviceRPCResponseProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto.Builder.class);
    }

    public static final int REQUESTIDMSB_FIELD_NUMBER = 1;
    private long requestIdMSB_;
    /**
     * <code>optional int64 requestIdMSB = 1;</code>
     */
    public long getRequestIdMSB() {
      return requestIdMSB_;
    }

    public static final int REQUESTIDLSB_FIELD_NUMBER = 2;
    private long requestIdLSB_;
    /**
     * <code>optional int64 requestIdLSB = 2;</code>
     */
    public long getRequestIdLSB() {
      return requestIdLSB_;
    }

    public static final int RESPONSE_FIELD_NUMBER = 3;
    private volatile java.lang.Object response_;
    /**
     * <code>optional string response = 3;</code>
     */
    public java.lang.String getResponse() {
      java.lang.Object ref = response_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        response_ = s;
        return s;
      }
    }
    /**
     * <code>optional string response = 3;</code>
     */
    public com.google.protobuf.ByteString
        getResponseBytes() {
      java.lang.Object ref = response_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        response_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ERROR_FIELD_NUMBER = 4;
    private int error_;
    /**
     * <code>optional int32 error = 4;</code>
     */
    public int getError() {
      return error_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (requestIdMSB_ != 0L) {
        output.writeInt64(1, requestIdMSB_);
      }
      if (requestIdLSB_ != 0L) {
        output.writeInt64(2, requestIdLSB_);
      }
      if (!getResponseBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, response_);
      }
      if (error_ != 0) {
        output.writeInt32(4, error_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (requestIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, requestIdMSB_);
      }
      if (requestIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, requestIdLSB_);
      }
      if (!getResponseBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, response_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, error_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto) obj;

      boolean result = true;
      result = result && (getRequestIdMSB()
          == other.getRequestIdMSB());
      result = result && (getRequestIdLSB()
          == other.getRequestIdLSB());
      result = result && getResponse()
          .equals(other.getResponse());
      result = result && (getError()
          == other.getError());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + REQUESTIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRequestIdMSB());
      hash = (37 * hash) + REQUESTIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRequestIdLSB());
      hash = (37 * hash) + RESPONSE_FIELD_NUMBER;
      hash = (53 * hash) + getResponse().hashCode();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.FromDeviceRPCResponseProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.FromDeviceRPCResponseProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_FromDeviceRPCResponseProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_FromDeviceRPCResponseProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        requestIdMSB_ = 0L;

        requestIdLSB_ = 0L;

        response_ = "";

        error_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_FromDeviceRPCResponseProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto(this);
        result.requestIdMSB_ = requestIdMSB_;
        result.requestIdLSB_ = requestIdLSB_;
        result.response_ = response_;
        result.error_ = error_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto.getDefaultInstance()) return this;
        if (other.getRequestIdMSB() != 0L) {
          setRequestIdMSB(other.getRequestIdMSB());
        }
        if (other.getRequestIdLSB() != 0L) {
          setRequestIdLSB(other.getRequestIdLSB());
        }
        if (!other.getResponse().isEmpty()) {
          response_ = other.response_;
          onChanged();
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long requestIdMSB_ ;
      /**
       * <code>optional int64 requestIdMSB = 1;</code>
       */
      public long getRequestIdMSB() {
        return requestIdMSB_;
      }
      /**
       * <code>optional int64 requestIdMSB = 1;</code>
       */
      public Builder setRequestIdMSB(long value) {
        
        requestIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 requestIdMSB = 1;</code>
       */
      public Builder clearRequestIdMSB() {
        
        requestIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long requestIdLSB_ ;
      /**
       * <code>optional int64 requestIdLSB = 2;</code>
       */
      public long getRequestIdLSB() {
        return requestIdLSB_;
      }
      /**
       * <code>optional int64 requestIdLSB = 2;</code>
       */
      public Builder setRequestIdLSB(long value) {
        
        requestIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 requestIdLSB = 2;</code>
       */
      public Builder clearRequestIdLSB() {
        
        requestIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object response_ = "";
      /**
       * <code>optional string response = 3;</code>
       */
      public java.lang.String getResponse() {
        java.lang.Object ref = response_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          response_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string response = 3;</code>
       */
      public com.google.protobuf.ByteString
          getResponseBytes() {
        java.lang.Object ref = response_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          response_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string response = 3;</code>
       */
      public Builder setResponse(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        response_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string response = 3;</code>
       */
      public Builder clearResponse() {
        
        response_ = getDefaultInstance().getResponse();
        onChanged();
        return this;
      }
      /**
       * <code>optional string response = 3;</code>
       */
      public Builder setResponseBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        response_ = value;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <code>optional int32 error = 4;</code>
       */
      public int getError() {
        return error_;
      }
      /**
       * <code>optional int32 error = 4;</code>
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 error = 4;</code>
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.FromDeviceRPCResponseProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.FromDeviceRPCResponseProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FromDeviceRPCResponseProto>
        PARSER = new com.google.protobuf.AbstractParser<FromDeviceRPCResponseProto>() {
      public FromDeviceRPCResponseProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new FromDeviceRPCResponseProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FromDeviceRPCResponseProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FromDeviceRPCResponseProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.FromDeviceRPCResponseProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DeviceStateServiceMsgProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.DeviceStateServiceMsgProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 tenantIdMSB = 1;</code>
     */
    long getTenantIdMSB();

    /**
     * <code>optional int64 tenantIdLSB = 2;</code>
     */
    long getTenantIdLSB();

    /**
     * <code>optional int64 deviceIdMSB = 3;</code>
     */
    long getDeviceIdMSB();

    /**
     * <code>optional int64 deviceIdLSB = 4;</code>
     */
    long getDeviceIdLSB();

    /**
     * <code>optional bool added = 5;</code>
     */
    boolean getAdded();

    /**
     * <code>optional bool updated = 6;</code>
     */
    boolean getUpdated();

    /**
     * <code>optional bool deleted = 7;</code>
     */
    boolean getDeleted();
  }
  /**
   * Protobuf type {@code cluster.DeviceStateServiceMsgProto}
   */
  public  static final class DeviceStateServiceMsgProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.DeviceStateServiceMsgProto)
      DeviceStateServiceMsgProtoOrBuilder {
    // Use DeviceStateServiceMsgProto.newBuilder() to construct.
    private DeviceStateServiceMsgProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeviceStateServiceMsgProto() {
      tenantIdMSB_ = 0L;
      tenantIdLSB_ = 0L;
      deviceIdMSB_ = 0L;
      deviceIdLSB_ = 0L;
      added_ = false;
      updated_ = false;
      deleted_ = false;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private DeviceStateServiceMsgProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              tenantIdMSB_ = input.readInt64();
              break;
            }
            case 16: {

              tenantIdLSB_ = input.readInt64();
              break;
            }
            case 24: {

              deviceIdMSB_ = input.readInt64();
              break;
            }
            case 32: {

              deviceIdLSB_ = input.readInt64();
              break;
            }
            case 40: {

              added_ = input.readBool();
              break;
            }
            case 48: {

              updated_ = input.readBool();
              break;
            }
            case 56: {

              deleted_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_DeviceStateServiceMsgProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_DeviceStateServiceMsgProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto.Builder.class);
    }

    public static final int TENANTIDMSB_FIELD_NUMBER = 1;
    private long tenantIdMSB_;
    /**
     * <code>optional int64 tenantIdMSB = 1;</code>
     */
    public long getTenantIdMSB() {
      return tenantIdMSB_;
    }

    public static final int TENANTIDLSB_FIELD_NUMBER = 2;
    private long tenantIdLSB_;
    /**
     * <code>optional int64 tenantIdLSB = 2;</code>
     */
    public long getTenantIdLSB() {
      return tenantIdLSB_;
    }

    public static final int DEVICEIDMSB_FIELD_NUMBER = 3;
    private long deviceIdMSB_;
    /**
     * <code>optional int64 deviceIdMSB = 3;</code>
     */
    public long getDeviceIdMSB() {
      return deviceIdMSB_;
    }

    public static final int DEVICEIDLSB_FIELD_NUMBER = 4;
    private long deviceIdLSB_;
    /**
     * <code>optional int64 deviceIdLSB = 4;</code>
     */
    public long getDeviceIdLSB() {
      return deviceIdLSB_;
    }

    public static final int ADDED_FIELD_NUMBER = 5;
    private boolean added_;
    /**
     * <code>optional bool added = 5;</code>
     */
    public boolean getAdded() {
      return added_;
    }

    public static final int UPDATED_FIELD_NUMBER = 6;
    private boolean updated_;
    /**
     * <code>optional bool updated = 6;</code>
     */
    public boolean getUpdated() {
      return updated_;
    }

    public static final int DELETED_FIELD_NUMBER = 7;
    private boolean deleted_;
    /**
     * <code>optional bool deleted = 7;</code>
     */
    public boolean getDeleted() {
      return deleted_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tenantIdMSB_ != 0L) {
        output.writeInt64(1, tenantIdMSB_);
      }
      if (tenantIdLSB_ != 0L) {
        output.writeInt64(2, tenantIdLSB_);
      }
      if (deviceIdMSB_ != 0L) {
        output.writeInt64(3, deviceIdMSB_);
      }
      if (deviceIdLSB_ != 0L) {
        output.writeInt64(4, deviceIdLSB_);
      }
      if (added_ != false) {
        output.writeBool(5, added_);
      }
      if (updated_ != false) {
        output.writeBool(6, updated_);
      }
      if (deleted_ != false) {
        output.writeBool(7, deleted_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tenantIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, tenantIdMSB_);
      }
      if (tenantIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, tenantIdLSB_);
      }
      if (deviceIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, deviceIdMSB_);
      }
      if (deviceIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, deviceIdLSB_);
      }
      if (added_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, added_);
      }
      if (updated_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(6, updated_);
      }
      if (deleted_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(7, deleted_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto) obj;

      boolean result = true;
      result = result && (getTenantIdMSB()
          == other.getTenantIdMSB());
      result = result && (getTenantIdLSB()
          == other.getTenantIdLSB());
      result = result && (getDeviceIdMSB()
          == other.getDeviceIdMSB());
      result = result && (getDeviceIdLSB()
          == other.getDeviceIdLSB());
      result = result && (getAdded()
          == other.getAdded());
      result = result && (getUpdated()
          == other.getUpdated());
      result = result && (getDeleted()
          == other.getDeleted());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + TENANTIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTenantIdMSB());
      hash = (37 * hash) + TENANTIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTenantIdLSB());
      hash = (37 * hash) + DEVICEIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDeviceIdMSB());
      hash = (37 * hash) + DEVICEIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDeviceIdLSB());
      hash = (37 * hash) + ADDED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getAdded());
      hash = (37 * hash) + UPDATED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getUpdated());
      hash = (37 * hash) + DELETED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getDeleted());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.DeviceStateServiceMsgProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.DeviceStateServiceMsgProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_DeviceStateServiceMsgProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_DeviceStateServiceMsgProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        tenantIdMSB_ = 0L;

        tenantIdLSB_ = 0L;

        deviceIdMSB_ = 0L;

        deviceIdLSB_ = 0L;

        added_ = false;

        updated_ = false;

        deleted_ = false;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_DeviceStateServiceMsgProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto(this);
        result.tenantIdMSB_ = tenantIdMSB_;
        result.tenantIdLSB_ = tenantIdLSB_;
        result.deviceIdMSB_ = deviceIdMSB_;
        result.deviceIdLSB_ = deviceIdLSB_;
        result.added_ = added_;
        result.updated_ = updated_;
        result.deleted_ = deleted_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto.getDefaultInstance()) return this;
        if (other.getTenantIdMSB() != 0L) {
          setTenantIdMSB(other.getTenantIdMSB());
        }
        if (other.getTenantIdLSB() != 0L) {
          setTenantIdLSB(other.getTenantIdLSB());
        }
        if (other.getDeviceIdMSB() != 0L) {
          setDeviceIdMSB(other.getDeviceIdMSB());
        }
        if (other.getDeviceIdLSB() != 0L) {
          setDeviceIdLSB(other.getDeviceIdLSB());
        }
        if (other.getAdded() != false) {
          setAdded(other.getAdded());
        }
        if (other.getUpdated() != false) {
          setUpdated(other.getUpdated());
        }
        if (other.getDeleted() != false) {
          setDeleted(other.getDeleted());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long tenantIdMSB_ ;
      /**
       * <code>optional int64 tenantIdMSB = 1;</code>
       */
      public long getTenantIdMSB() {
        return tenantIdMSB_;
      }
      /**
       * <code>optional int64 tenantIdMSB = 1;</code>
       */
      public Builder setTenantIdMSB(long value) {
        
        tenantIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 tenantIdMSB = 1;</code>
       */
      public Builder clearTenantIdMSB() {
        
        tenantIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long tenantIdLSB_ ;
      /**
       * <code>optional int64 tenantIdLSB = 2;</code>
       */
      public long getTenantIdLSB() {
        return tenantIdLSB_;
      }
      /**
       * <code>optional int64 tenantIdLSB = 2;</code>
       */
      public Builder setTenantIdLSB(long value) {
        
        tenantIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 tenantIdLSB = 2;</code>
       */
      public Builder clearTenantIdLSB() {
        
        tenantIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private long deviceIdMSB_ ;
      /**
       * <code>optional int64 deviceIdMSB = 3;</code>
       */
      public long getDeviceIdMSB() {
        return deviceIdMSB_;
      }
      /**
       * <code>optional int64 deviceIdMSB = 3;</code>
       */
      public Builder setDeviceIdMSB(long value) {
        
        deviceIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 deviceIdMSB = 3;</code>
       */
      public Builder clearDeviceIdMSB() {
        
        deviceIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long deviceIdLSB_ ;
      /**
       * <code>optional int64 deviceIdLSB = 4;</code>
       */
      public long getDeviceIdLSB() {
        return deviceIdLSB_;
      }
      /**
       * <code>optional int64 deviceIdLSB = 4;</code>
       */
      public Builder setDeviceIdLSB(long value) {
        
        deviceIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 deviceIdLSB = 4;</code>
       */
      public Builder clearDeviceIdLSB() {
        
        deviceIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private boolean added_ ;
      /**
       * <code>optional bool added = 5;</code>
       */
      public boolean getAdded() {
        return added_;
      }
      /**
       * <code>optional bool added = 5;</code>
       */
      public Builder setAdded(boolean value) {
        
        added_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool added = 5;</code>
       */
      public Builder clearAdded() {
        
        added_ = false;
        onChanged();
        return this;
      }

      private boolean updated_ ;
      /**
       * <code>optional bool updated = 6;</code>
       */
      public boolean getUpdated() {
        return updated_;
      }
      /**
       * <code>optional bool updated = 6;</code>
       */
      public Builder setUpdated(boolean value) {
        
        updated_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool updated = 6;</code>
       */
      public Builder clearUpdated() {
        
        updated_ = false;
        onChanged();
        return this;
      }

      private boolean deleted_ ;
      /**
       * <code>optional bool deleted = 7;</code>
       */
      public boolean getDeleted() {
        return deleted_;
      }
      /**
       * <code>optional bool deleted = 7;</code>
       */
      public Builder setDeleted(boolean value) {
        
        deleted_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool deleted = 7;</code>
       */
      public Builder clearDeleted() {
        
        deleted_ = false;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.DeviceStateServiceMsgProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.DeviceStateServiceMsgProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DeviceStateServiceMsgProto>
        PARSER = new com.google.protobuf.AbstractParser<DeviceStateServiceMsgProto>() {
      public DeviceStateServiceMsgProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new DeviceStateServiceMsgProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DeviceStateServiceMsgProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DeviceStateServiceMsgProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.DeviceStateServiceMsgProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TransactionEndServiceMsgProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cluster.TransactionEndServiceMsgProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string entityType = 1;</code>
     */
    java.lang.String getEntityType();
    /**
     * <code>optional string entityType = 1;</code>
     */
    com.google.protobuf.ByteString
        getEntityTypeBytes();

    /**
     * <code>optional int64 originatorIdMSB = 2;</code>
     */
    long getOriginatorIdMSB();

    /**
     * <code>optional int64 originatorIdLSB = 3;</code>
     */
    long getOriginatorIdLSB();

    /**
     * <code>optional int64 transactionIdMSB = 4;</code>
     */
    long getTransactionIdMSB();

    /**
     * <code>optional int64 transactionIdLSB = 5;</code>
     */
    long getTransactionIdLSB();
  }
  /**
   * Protobuf type {@code cluster.TransactionEndServiceMsgProto}
   */
  public  static final class TransactionEndServiceMsgProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cluster.TransactionEndServiceMsgProto)
      TransactionEndServiceMsgProtoOrBuilder {
    // Use TransactionEndServiceMsgProto.newBuilder() to construct.
    private TransactionEndServiceMsgProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TransactionEndServiceMsgProto() {
      entityType_ = "";
      originatorIdMSB_ = 0L;
      originatorIdLSB_ = 0L;
      transactionIdMSB_ = 0L;
      transactionIdLSB_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private TransactionEndServiceMsgProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              entityType_ = s;
              break;
            }
            case 16: {

              originatorIdMSB_ = input.readInt64();
              break;
            }
            case 24: {

              originatorIdLSB_ = input.readInt64();
              break;
            }
            case 32: {

              transactionIdMSB_ = input.readInt64();
              break;
            }
            case 40: {

              transactionIdLSB_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_TransactionEndServiceMsgProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_TransactionEndServiceMsgProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto.Builder.class);
    }

    public static final int ENTITYTYPE_FIELD_NUMBER = 1;
    private volatile java.lang.Object entityType_;
    /**
     * <code>optional string entityType = 1;</code>
     */
    public java.lang.String getEntityType() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        entityType_ = s;
        return s;
      }
    }
    /**
     * <code>optional string entityType = 1;</code>
     */
    public com.google.protobuf.ByteString
        getEntityTypeBytes() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        entityType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ORIGINATORIDMSB_FIELD_NUMBER = 2;
    private long originatorIdMSB_;
    /**
     * <code>optional int64 originatorIdMSB = 2;</code>
     */
    public long getOriginatorIdMSB() {
      return originatorIdMSB_;
    }

    public static final int ORIGINATORIDLSB_FIELD_NUMBER = 3;
    private long originatorIdLSB_;
    /**
     * <code>optional int64 originatorIdLSB = 3;</code>
     */
    public long getOriginatorIdLSB() {
      return originatorIdLSB_;
    }

    public static final int TRANSACTIONIDMSB_FIELD_NUMBER = 4;
    private long transactionIdMSB_;
    /**
     * <code>optional int64 transactionIdMSB = 4;</code>
     */
    public long getTransactionIdMSB() {
      return transactionIdMSB_;
    }

    public static final int TRANSACTIONIDLSB_FIELD_NUMBER = 5;
    private long transactionIdLSB_;
    /**
     * <code>optional int64 transactionIdLSB = 5;</code>
     */
    public long getTransactionIdLSB() {
      return transactionIdLSB_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getEntityTypeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, entityType_);
      }
      if (originatorIdMSB_ != 0L) {
        output.writeInt64(2, originatorIdMSB_);
      }
      if (originatorIdLSB_ != 0L) {
        output.writeInt64(3, originatorIdLSB_);
      }
      if (transactionIdMSB_ != 0L) {
        output.writeInt64(4, transactionIdMSB_);
      }
      if (transactionIdLSB_ != 0L) {
        output.writeInt64(5, transactionIdLSB_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getEntityTypeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, entityType_);
      }
      if (originatorIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, originatorIdMSB_);
      }
      if (originatorIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, originatorIdLSB_);
      }
      if (transactionIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, transactionIdMSB_);
      }
      if (transactionIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, transactionIdLSB_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto other = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto) obj;

      boolean result = true;
      result = result && getEntityType()
          .equals(other.getEntityType());
      result = result && (getOriginatorIdMSB()
          == other.getOriginatorIdMSB());
      result = result && (getOriginatorIdLSB()
          == other.getOriginatorIdLSB());
      result = result && (getTransactionIdMSB()
          == other.getTransactionIdMSB());
      result = result && (getTransactionIdLSB()
          == other.getTransactionIdLSB());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + ENTITYTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getEntityType().hashCode();
      hash = (37 * hash) + ORIGINATORIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getOriginatorIdMSB());
      hash = (37 * hash) + ORIGINATORIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getOriginatorIdLSB());
      hash = (37 * hash) + TRANSACTIONIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTransactionIdMSB());
      hash = (37 * hash) + TRANSACTIONIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTransactionIdLSB());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cluster.TransactionEndServiceMsgProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cluster.TransactionEndServiceMsgProto)
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_TransactionEndServiceMsgProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_TransactionEndServiceMsgProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto.class, org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        entityType_ = "";

        originatorIdMSB_ = 0L;

        originatorIdLSB_ = 0L;

        transactionIdMSB_ = 0L;

        transactionIdLSB_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.internal_static_cluster_TransactionEndServiceMsgProto_descriptor;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto getDefaultInstanceForType() {
        return org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto.getDefaultInstance();
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto build() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto buildPartial() {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto result = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto(this);
        result.entityType_ = entityType_;
        result.originatorIdMSB_ = originatorIdMSB_;
        result.originatorIdLSB_ = originatorIdLSB_;
        result.transactionIdMSB_ = transactionIdMSB_;
        result.transactionIdLSB_ = transactionIdLSB_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto) {
          return mergeFrom((org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto other) {
        if (other == org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto.getDefaultInstance()) return this;
        if (!other.getEntityType().isEmpty()) {
          entityType_ = other.entityType_;
          onChanged();
        }
        if (other.getOriginatorIdMSB() != 0L) {
          setOriginatorIdMSB(other.getOriginatorIdMSB());
        }
        if (other.getOriginatorIdLSB() != 0L) {
          setOriginatorIdLSB(other.getOriginatorIdLSB());
        }
        if (other.getTransactionIdMSB() != 0L) {
          setTransactionIdMSB(other.getTransactionIdMSB());
        }
        if (other.getTransactionIdLSB() != 0L) {
          setTransactionIdLSB(other.getTransactionIdLSB());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object entityType_ = "";
      /**
       * <code>optional string entityType = 1;</code>
       */
      public java.lang.String getEntityType() {
        java.lang.Object ref = entityType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          entityType_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public com.google.protobuf.ByteString
          getEntityTypeBytes() {
        java.lang.Object ref = entityType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          entityType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public Builder setEntityType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        entityType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public Builder clearEntityType() {
        
        entityType_ = getDefaultInstance().getEntityType();
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 1;</code>
       */
      public Builder setEntityTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        entityType_ = value;
        onChanged();
        return this;
      }

      private long originatorIdMSB_ ;
      /**
       * <code>optional int64 originatorIdMSB = 2;</code>
       */
      public long getOriginatorIdMSB() {
        return originatorIdMSB_;
      }
      /**
       * <code>optional int64 originatorIdMSB = 2;</code>
       */
      public Builder setOriginatorIdMSB(long value) {
        
        originatorIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 originatorIdMSB = 2;</code>
       */
      public Builder clearOriginatorIdMSB() {
        
        originatorIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long originatorIdLSB_ ;
      /**
       * <code>optional int64 originatorIdLSB = 3;</code>
       */
      public long getOriginatorIdLSB() {
        return originatorIdLSB_;
      }
      /**
       * <code>optional int64 originatorIdLSB = 3;</code>
       */
      public Builder setOriginatorIdLSB(long value) {
        
        originatorIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 originatorIdLSB = 3;</code>
       */
      public Builder clearOriginatorIdLSB() {
        
        originatorIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private long transactionIdMSB_ ;
      /**
       * <code>optional int64 transactionIdMSB = 4;</code>
       */
      public long getTransactionIdMSB() {
        return transactionIdMSB_;
      }
      /**
       * <code>optional int64 transactionIdMSB = 4;</code>
       */
      public Builder setTransactionIdMSB(long value) {
        
        transactionIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 transactionIdMSB = 4;</code>
       */
      public Builder clearTransactionIdMSB() {
        
        transactionIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long transactionIdLSB_ ;
      /**
       * <code>optional int64 transactionIdLSB = 5;</code>
       */
      public long getTransactionIdLSB() {
        return transactionIdLSB_;
      }
      /**
       * <code>optional int64 transactionIdLSB = 5;</code>
       */
      public Builder setTransactionIdLSB(long value) {
        
        transactionIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 transactionIdLSB = 5;</code>
       */
      public Builder clearTransactionIdLSB() {
        
        transactionIdLSB_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:cluster.TransactionEndServiceMsgProto)
    }

    // @@protoc_insertion_point(class_scope:cluster.TransactionEndServiceMsgProto)
    private static final org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto();
    }

    public static org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TransactionEndServiceMsgProto>
        PARSER = new com.google.protobuf.AbstractParser<TransactionEndServiceMsgProto>() {
      public TransactionEndServiceMsgProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new TransactionEndServiceMsgProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TransactionEndServiceMsgProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TransactionEndServiceMsgProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.cluster.ClusterAPIProtos.TransactionEndServiceMsgProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_ClusterMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_ClusterMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_ServerAddress_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_ServerAddress_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_MessageMataInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_MessageMataInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_SubscriptionProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_SubscriptionProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_SubscriptionUpdateProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_SubscriptionUpdateProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_AttributeUpdateProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_AttributeUpdateProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_TimeseriesUpdateProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_TimeseriesUpdateProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_SessionCloseProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_SessionCloseProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_SubscriptionCloseProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_SubscriptionCloseProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_SubscriptionKetStateProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_SubscriptionKetStateProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_SubscriptionUpdateValueListProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_SubscriptionUpdateValueListProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_KeyValueProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_KeyValueProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_FromDeviceRPCResponseProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_FromDeviceRPCResponseProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_DeviceStateServiceMsgProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_DeviceStateServiceMsgProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cluster_TransactionEndServiceMsgProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cluster_TransactionEndServiceMsgProto_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rcluster.proto\022\007cluster\"\256\001\n\016ClusterMess" +
      "age\022)\n\013messageType\030\001 \001(\0162\024.cluster.Messa" +
      "geType\0221\n\017messageMetaInfo\030\002 \001(\0132\030.cluste" +
      "r.MessageMataInfo\022-\n\rserverAddress\030\003 \001(\013" +
      "2\026.cluster.ServerAddress\022\017\n\007payload\030\004 \001(" +
      "\014\"+\n\rServerAddress\022\014\n\004host\030\001 \001(\t\022\014\n\004port" +
      "\030\002 \001(\005\"8\n\017MessageMataInfo\022\027\n\017payloadMeta" +
      "Info\030\001 \001(\t\022\014\n\004tags\030\002 \003(\t\"\333\001\n\021Subscriptio" +
      "nProto\022\021\n\tsessionId\030\001 \001(\t\022\026\n\016subscriptio" +
      "nId\030\002 \001(\005\022\022\n\nentityType\030\003 \001(\t\022\020\n\010tenantI",
      "d\030\004 \001(\t\022\020\n\010entityId\030\005 \001(\t\022\014\n\004type\030\006 \001(\t\022" +
      "\017\n\007allKeys\030\007 \001(\010\0225\n\tkeyStates\030\010 \003(\0132\".cl" +
      "uster.SubscriptionKetStateProto\022\r\n\005scope" +
      "\030\t \001(\t\"\242\001\n\027SubscriptionUpdateProto\022\021\n\tse" +
      "ssionId\030\001 \001(\t\022\026\n\016subscriptionId\030\002 \001(\005\022\021\n" +
      "\terrorCode\030\003 \001(\005\022\020\n\010errorMsg\030\004 \001(\t\0227\n\004da" +
      "ta\030\005 \003(\0132).cluster.SubscriptionUpdateVal" +
      "ueListProto\"q\n\024AttributeUpdateProto\022\022\n\ne" +
      "ntityType\030\001 \001(\t\022\020\n\010entityId\030\002 \001(\t\022\r\n\005sco" +
      "pe\030\003 \001(\t\022$\n\004data\030\004 \003(\0132\026.cluster.KeyValu",
      "eProto\"c\n\025TimeseriesUpdateProto\022\022\n\nentit" +
      "yType\030\001 \001(\t\022\020\n\010entityId\030\002 \001(\t\022$\n\004data\030\004 " +
      "\003(\0132\026.cluster.KeyValueProto\"&\n\021SessionCl" +
      "oseProto\022\021\n\tsessionId\030\001 \001(\t\"C\n\026Subscript" +
      "ionCloseProto\022\021\n\tsessionId\030\001 \001(\t\022\026\n\016subs" +
      "criptionId\030\002 \001(\005\"4\n\031SubscriptionKetState" +
      "Proto\022\013\n\003key\030\001 \001(\t\022\n\n\002ts\030\002 \001(\003\"J\n Subscr" +
      "iptionUpdateValueListProto\022\013\n\003key\030\001 \001(\t\022" +
      "\n\n\002ts\030\002 \003(\003\022\r\n\005value\030\003 \003(\t\"\210\001\n\rKeyValueP" +
      "roto\022\013\n\003key\030\001 \001(\t\022\n\n\002ts\030\002 \001(\003\022\021\n\tvalueTy",
      "pe\030\003 \001(\005\022\020\n\010strValue\030\004 \001(\t\022\021\n\tlongValue\030" +
      "\005 \001(\003\022\023\n\013doubleValue\030\006 \001(\001\022\021\n\tboolValue\030" +
      "\007 \001(\010\"i\n\032FromDeviceRPCResponseProto\022\024\n\014r" +
      "equestIdMSB\030\001 \001(\003\022\024\n\014requestIdLSB\030\002 \001(\003\022" +
      "\020\n\010response\030\003 \001(\t\022\r\n\005error\030\004 \001(\005\"\241\001\n\032Dev" +
      "iceStateServiceMsgProto\022\023\n\013tenantIdMSB\030\001" +
      " \001(\003\022\023\n\013tenantIdLSB\030\002 \001(\003\022\023\n\013deviceIdMSB" +
      "\030\003 \001(\003\022\023\n\013deviceIdLSB\030\004 \001(\003\022\r\n\005added\030\005 \001" +
      "(\010\022\017\n\007updated\030\006 \001(\010\022\017\n\007deleted\030\007 \001(\010\"\231\001\n" +
      "\035TransactionEndServiceMsgProto\022\022\n\nentity",
      "Type\030\001 \001(\t\022\027\n\017originatorIdMSB\030\002 \001(\003\022\027\n\017o" +
      "riginatorIdLSB\030\003 \001(\003\022\030\n\020transactionIdMSB" +
      "\030\004 \001(\003\022\030\n\020transactionIdLSB\030\005 \001(\003*\306\004\n\013Mes" +
      "sageType\022\"\n\036RPC_SESSION_CREATE_REQUEST_M" +
      "SG\020\000\022\024\n\020TO_ALL_NODES_MSG\020\001\022\030\n\024RPC_SESSIO" +
      "N_TELL_MSG\020\002\022\025\n\021RPC_BROADCAST_MSG\020\003\022\027\n\023C" +
      "ONNECT_RPC_MESSAGE\020\004\022\031\n\025CLUSTER_ACTOR_ME" +
      "SSAGE\020\005\0221\n-CLUSTER_TELEMETRY_SUBSCRIPTIO" +
      "N_CREATE_MESSAGE\020\006\0221\n-CLUSTER_TELEMETRY_" +
      "SUBSCRIPTION_UPDATE_MESSAGE\020\007\0220\n,CLUSTER",
      "_TELEMETRY_SUBSCRIPTION_CLOSE_MESSAGE\020\010\022" +
      "+\n\'CLUSTER_TELEMETRY_SESSION_CLOSE_MESSA" +
      "GE\020\t\022)\n%CLUSTER_TELEMETRY_ATTR_UPDATE_ME" +
      "SSAGE\020\n\022\'\n#CLUSTER_TELEMETRY_TS_UPDATE_M" +
      "ESSAGE\020\013\022,\n(CLUSTER_RPC_FROM_DEVICE_RESP" +
      "ONSE_MESSAGE\020\014\022(\n$CLUSTER_DEVICE_STATE_S" +
      "ERVICE_MESSAGE\020\r\022\'\n#CLUSTER_TRANSACTION_" +
      "SERVICE_MESSAGE\020\0162Y\n\021ClusterRpcService\022D" +
      "\n\nhandleMsgs\022\027.cluster.ClusterMessage\032\027." +
      "cluster.ClusterMessage\"\000(\0010\001B6\n\"org.thin",
      "gsboard.server.gen.clusterB\020ClusterAPIPr" +
      "otosb\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_cluster_ClusterMessage_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_cluster_ClusterMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_ClusterMessage_descriptor,
        new java.lang.String[] { "MessageType", "MessageMetaInfo", "ServerAddress", "Payload", });
    internal_static_cluster_ServerAddress_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_cluster_ServerAddress_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_ServerAddress_descriptor,
        new java.lang.String[] { "Host", "Port", });
    internal_static_cluster_MessageMataInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_cluster_MessageMataInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_MessageMataInfo_descriptor,
        new java.lang.String[] { "PayloadMetaInfo", "Tags", });
    internal_static_cluster_SubscriptionProto_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_cluster_SubscriptionProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_SubscriptionProto_descriptor,
        new java.lang.String[] { "SessionId", "SubscriptionId", "EntityType", "TenantId", "EntityId", "Type", "AllKeys", "KeyStates", "Scope", });
    internal_static_cluster_SubscriptionUpdateProto_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_cluster_SubscriptionUpdateProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_SubscriptionUpdateProto_descriptor,
        new java.lang.String[] { "SessionId", "SubscriptionId", "ErrorCode", "ErrorMsg", "Data", });
    internal_static_cluster_AttributeUpdateProto_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_cluster_AttributeUpdateProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_AttributeUpdateProto_descriptor,
        new java.lang.String[] { "EntityType", "EntityId", "Scope", "Data", });
    internal_static_cluster_TimeseriesUpdateProto_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_cluster_TimeseriesUpdateProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_TimeseriesUpdateProto_descriptor,
        new java.lang.String[] { "EntityType", "EntityId", "Data", });
    internal_static_cluster_SessionCloseProto_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_cluster_SessionCloseProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_SessionCloseProto_descriptor,
        new java.lang.String[] { "SessionId", });
    internal_static_cluster_SubscriptionCloseProto_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_cluster_SubscriptionCloseProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_SubscriptionCloseProto_descriptor,
        new java.lang.String[] { "SessionId", "SubscriptionId", });
    internal_static_cluster_SubscriptionKetStateProto_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_cluster_SubscriptionKetStateProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_SubscriptionKetStateProto_descriptor,
        new java.lang.String[] { "Key", "Ts", });
    internal_static_cluster_SubscriptionUpdateValueListProto_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_cluster_SubscriptionUpdateValueListProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_SubscriptionUpdateValueListProto_descriptor,
        new java.lang.String[] { "Key", "Ts", "Value", });
    internal_static_cluster_KeyValueProto_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_cluster_KeyValueProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_KeyValueProto_descriptor,
        new java.lang.String[] { "Key", "Ts", "ValueType", "StrValue", "LongValue", "DoubleValue", "BoolValue", });
    internal_static_cluster_FromDeviceRPCResponseProto_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_cluster_FromDeviceRPCResponseProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_FromDeviceRPCResponseProto_descriptor,
        new java.lang.String[] { "RequestIdMSB", "RequestIdLSB", "Response", "Error", });
    internal_static_cluster_DeviceStateServiceMsgProto_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_cluster_DeviceStateServiceMsgProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_DeviceStateServiceMsgProto_descriptor,
        new java.lang.String[] { "TenantIdMSB", "TenantIdLSB", "DeviceIdMSB", "DeviceIdLSB", "Added", "Updated", "Deleted", });
    internal_static_cluster_TransactionEndServiceMsgProto_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_cluster_TransactionEndServiceMsgProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cluster_TransactionEndServiceMsgProto_descriptor,
        new java.lang.String[] { "EntityType", "OriginatorIdMSB", "OriginatorIdLSB", "TransactionIdMSB", "TransactionIdLSB", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
