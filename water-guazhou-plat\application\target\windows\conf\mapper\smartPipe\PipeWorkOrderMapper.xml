<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipeWorkOrderMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.DTO.PipeWorkOrderDTO">
        select work.*, organizer.first_name as organizerName, receiver.first_name as processUserName, parti.id as partitionId, parti.name as partitionName, department.name as receiveDepartmentName
        from work_order work
        left join tb_user organizer on work.organizer_id = organizer.id
        left join tb_user receiver on work.process_user_id = receiver.id
        left join tb_pipe_work_order p_work on work.id = p_work.work_order_id
        left join tb_pipe_partition parti on p_work.partition_id = parti.id
        left join tb_department department on receiver.department_id = department.id
        <where>
            <if test="req.startTime != null ">
                and work.create_time &gt;= #{req.startTime}
            </if>
            <if test="req.endTime != null">
                and work.create_time &lt; #{req.endTime}
            </if>
            <if test="req.organizerName != null and req.organizerName != ''">
                and organizer.first_name like '%' || #{req.organizerName} || '%'
            </if>
            <if test="req.partitionName != null and req.partitionName != ''">
                and parti.name like '%' || #{req.partitionName} || '%'
            </if>
            <if test="req.processUserName != null and req.processUserName != ''">
                and receiver.first_name like '%' || #{req.processUserName} || '%'
            </if>
            <if test="req.receiveDepartmentName != null and req.receiveDepartmentName != ''">
                and department.name like '%' || #{req.receiveDepartmentName} || '%'
            </if>

            <if test="req.status != null and req.status != ''">
                <choose>
                    <when test="req.status == 'CHARGEBACK'">
                        <!--@formatter:off-->
                        and (select count(1) > 0
                        from work_order_details
                        where process_time > (select max(process_time) from work_order_details where type = 'CHARGEBACK' and main_id = work.id))
                        <!--@formatter:on-->
                    </when>

                    <!-- 漏损工单状态 -->
                    <when test="req.status == 'PIPE_PROCESSING'">
                        and (work.status != 'PENDING' and work.status != 'ASSIGN' and work.status != 'APPROVED' and work.status != 'TERMINATED'  and work.status != 'COMPLETE' )
                    </when>
                    <when test="req.status == 'PIPE_COMPLETE'">
                        and (work.status = 'APPROVED' or work.status = 'TERMINATED' or work.status = 'COMPLETE' )
                    </when>
                    <otherwise>
                        and work.status = #{req.status}
                    </otherwise>
                </choose>
            </if>
            <if test="req.level != null and req.level != ''">
                and work.level = #{req.level}
            </if>
            and work.tenant_id = #{req.tenantId}
            <choose>
                <when test="req.source != null and req.source != ''">
                    and source = #{req.source}
                </when>
                <otherwise>
                  and work.source = '漏损控制'
                </otherwise>
            </choose>
        </where>
        order by create_time desc
    </select>

</mapper>