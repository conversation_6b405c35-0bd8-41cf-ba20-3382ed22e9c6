package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.construction.project.SoDeviceItemService;
import org.thingsboard.server.dao.construction.project.SoGeneralTypeService;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContract;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractContainer;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoSimpleConstructionContract;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionContractMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypeSaveRequest;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal.SO_CONSTRUCTION_CONTRACT_JOURNAL;
import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope.SO_CONSTRUCTION_CONTRACT;

@Service
public class SoConstructionContractServiceImpl extends BasicSoConstructionTaskDriveService<SoConstructionContract> implements SoConstructionContractService {
    @Autowired
    private SoConstructionContractMapper mapper;

    @Autowired
    private SoDeviceItemService deviceItemService;

    @Autowired
    private SoGeneralTypeService typeService;


    @Override
    public IPage<SoConstructionContractContainer> findAllConditional(SoConstructionContractPageRequest request) {
        IPage<SoConstructionContractContainer> result = QueryUtil.pagify(request, (i, j) -> mapper.findByPage(request), () -> mapper.countByPage(request));
        for (SoConstructionContractContainer record : result.getRecords()) {
            List<SoConstructionContract> items = record.getItems();
            if (items.size() == 1 && items.get(0).getId() == null) {
                items.clear();
            }
        }
        return result;
    }

    @Override
    @Transactional
    public SoConstructionContract save(SoConstructionContractSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> commonSave(entity, e), mapper::updateFully);
    }

    @Override
    public boolean isCodeExists(String code, String tenantId, String id) {
        return mapper.isCodeExists(code, tenantId, id);
    }

    @Override
    public boolean update(SoConstructionContract entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public boolean complete(String constructionCode, String userId, String tenantId) {
        boolean success = taskInfoService.markAsComplete(constructionCode, tenantId, getCurrentScope());
        if (success) {
            recordService.recordComplete(tenantId, userId, constructionCode, getCurrentJournalType());
        }
        return success;
    }

    // region 设备项管理
    @Override
    public IPage<SoDeviceItem> getDevices(SoDeviceItemPageRequest request) {
        return deviceItemService.findAllConditional(request);
    }

    @Override
    public List<SoDeviceItem> saveDevice(List<SoDeviceItemSaveRequest> request) {
        for (SoDeviceItemSaveRequest req : request) {
            req.setScope(getCurrentScope());
        }
        return deviceItemService.saveAll(request, getCurrentScope());
    }

    // endregion

    @Override
    public boolean isComplete(String id) {
        return taskInfoService.isComplete(id, getCurrentScope());
    }

    @Override
    public boolean isComplete(String constructionCode, String tenantId) {
        return taskInfoService.isComplete(constructionCode, tenantId, getCurrentScope());
    }

    @Override
    public SoGeneralSystemScope getCurrentScope() {
        return SO_CONSTRUCTION_CONTRACT;
    }

    @Override
    public SoGeneralSystemJournal getCurrentJournalType() {
        return SO_CONSTRUCTION_CONTRACT_JOURNAL;
    }

    @Override
    public BaseMapper<SoConstructionContract> getDirectMapper() {
        return mapper;
    }


    // region 类型配置
    @Override
    public IPage<SoGeneralType> getTypes(SoGeneralTypePageRequest request) {
        request.setScope(SO_CONSTRUCTION_CONTRACT);
        return typeService.findAllConditional(request);
    }

    @Override
    public SoGeneralType saveType(SoGeneralTypeSaveRequest request) {
        request.setScope(SO_CONSTRUCTION_CONTRACT);
        return typeService.save(request);
    }

    // endregion
    @Override
    public IPage<SoSimpleConstructionContract> findSimple(SoConstructionContractPageRequest request) {
        return mapper.findSimple(request);
    }

    @Override
    public boolean isCompleteByContractCode(String code, String tenantId) {
        return mapper.isCompleteByContractCode(code, tenantId);
    }

}
