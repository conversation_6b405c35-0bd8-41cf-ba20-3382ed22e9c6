package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import org.thingsboard.server.dao.util.imodel.response.NameDisplayableEnum;

public enum OrderRecordCompleteStatus implements NameDisplayableEnum {
    PROCESSING("未完成"),
    COMPLETED("完成");

    private final String displayName;

    OrderRecordCompleteStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
