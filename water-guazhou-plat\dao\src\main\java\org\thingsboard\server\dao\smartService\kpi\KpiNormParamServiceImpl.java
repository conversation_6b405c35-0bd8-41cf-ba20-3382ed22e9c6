package org.thingsboard.server.dao.smartService.kpi;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormParam;
import org.thingsboard.server.dao.sql.smartService.kpi.KpiNormParamMapper;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class KpiNormParamServiceImpl implements KpiNormParamService {
    @Autowired
    private KpiNormParamMapper kpiNormParamMapper;

    @Override
    public PageData getList(String source, String type, String code, String name, int page, int size, Boolean enabled, String tenantId) {

        List<KpiNormParam> kpiNormParamMapperList = kpiNormParamMapper.getList(source, type, code, name, page, size, enabled, tenantId);

        int total = kpiNormParamMapper.getListCount(source, type, code, name, enabled, tenantId);

        return new PageData(total, kpiNormParamMapperList);

    }

    @Override
    public KpiNormParam save(KpiNormParam kpiNormParam) {
        kpiNormParam.setUpdateTime(new Date());
        if (StringUtils.isBlank(kpiNormParam.getId())) {
            kpiNormParam.setCreateTime(new Date());
            kpiNormParamMapper.insert(kpiNormParam);
        } else {
            kpiNormParamMapper.updateById(kpiNormParam);
        }

        return kpiNormParam;
    }

    @Override
    public int delete(List<String> ids) {
        // 系统指标不许删除
        List<KpiNormParam> kpiNormParams = kpiNormParamMapper.selectBatchIds(ids);
        ids = kpiNormParams.stream().filter(a -> a.getSource().equals("1")).map(a -> a.getId()).collect(Collectors.toList());
        if (ids.size() == 0) {
            return 0;
        }
        return kpiNormParamMapper.deleteBatchIds(ids);
    }

    @Override
    public boolean checkCode(KpiNormParam kpiNormParam) {
        QueryWrapper<KpiNormParam> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", kpiNormParam.getCode());
        queryWrapper.eq("tenant_id", kpiNormParam.getTenantId());
        List<KpiNormParam> list = kpiNormParamMapper.selectList(queryWrapper);
        for (KpiNormParam normParam : list) {
            if (!normParam.getId().equals(kpiNormParam.getId())) {
                return false;
            }
        }
        return true;
    }
}
