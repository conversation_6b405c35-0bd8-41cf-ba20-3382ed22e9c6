package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.statistic.StatisticTimeUnit;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderCountOfStatusRequest;

import java.util.Set;

@Getter
@Setter
public class SMCircuitTaskWorkOrderTrendRequest extends WorkOrderCountOfStatusRequest {
    // 时间单位
    private StatisticTimeUnit timeUnit;

    @Override
    public Set<WorkOrderStatus> getActiveStageSet() {
        return getOnProcessStage();
    }
}
