package org.thingsboard.server.controller.gis;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.util.OracleUtil;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * GIS查询
 *
 * @since v1.0.0 2022-04-15
 */

@RestController
@RequestMapping("api/gis")
@Slf4j
public class GISController {

    @Autowired
    private OracleUtil oracleUtil;

    @PostMapping("monitorList")
    public List monitorList(@RequestBody JSONObject params) throws SQLException {
        String keywords = params.getString("keywords") == null ? "" : params.getString("keywords");

        List<String> typeList = new ArrayList<>();
        try {
            typeList = params.getObject("typeList", List.class);
        } catch (Exception e) {
            log.error("类型转换错误：{}", params.get("typeList"));
            e.printStackTrace();
        }

        Connection connection = oracleUtil.getConnection();
        Statement statement = connection.createStatement();
        List<Map> result = new ArrayList<>();
        if (typeList == null || typeList.size() == 0) {
            return new ArrayList();
        }

        // 查询所有站点
        String sql = "select ptid, ptname, x, y, managedept_code, groupname, type from SCADAPNTS_STATIONS where concat(groupname, ptname) like '%" + keywords + "%' ";
        String types = typeList.stream().collect(Collectors.joining("','"));
        sql = sql + " and type in ('" + types + "')";
        sql = sql + " order by TO_NUMBER(ptid)";

        ResultSet resultSet = statement.executeQuery(sql);
        Map stationMap;
        List<Map> valus;
        Map valueMap;
        ResultSet valueResult;
        Map nameMap = new HashMap();
        while (resultSet.next()) {
            stationMap = new HashMap();
            stationMap.put("ptid", resultSet.getObject(1));
            stationMap.put("ptname", resultSet.getObject(2));
            stationMap.put("x", resultSet.getObject(3));
            stationMap.put("y", resultSet.getObject(4));
            stationMap.put("managedeptCode", resultSet.getObject(5));
            stationMap.put("groupName", resultSet.getObject(6));
            stationMap.put("type", resultSet.getObject(7));

            // 查找实时值
            valus = new ArrayList<>();
            sql = "select a.description, a.maxval, a.minval, a.name, a.paratype, b.unit,b.value, to_char(b.updatetime, 'YYYY-MM-DD HH24:MI:SS') as time from scadapnts_des a left join scadapnts b on a.name = b.name left join scadapnts_con c on a.name = c.valname   where a.PTNUM = " + resultSet.getObject(1) + " order by c.item_index";
            statement = connection.createStatement();
            valueResult = statement.executeQuery(sql);
            while (valueResult.next()) {
                valueMap = new HashMap();
                if (nameMap.containsKey(valueResult.getObject(4))) {
                    continue;
                }
                nameMap.put(valueResult.getObject(4), "");
                valueMap.put("description", valueResult.getObject(1));
                valueMap.put("maxval", valueResult.getObject(2));
                valueMap.put("minval", valueResult.getObject(3));
                valueMap.put("name", valueResult.getObject(4));
                valueMap.put("paratype", valueResult.getObject(5));
                valueMap.put("unit", valueResult.getObject(6));
                valueMap.put("value", valueResult.getObject(7));
                valueMap.put("time", valueResult.getObject(8));
                valus.add(valueMap);
            }
            stationMap.put("values", valus);
            result.add(stationMap);
        }

        return result;
    }
}
