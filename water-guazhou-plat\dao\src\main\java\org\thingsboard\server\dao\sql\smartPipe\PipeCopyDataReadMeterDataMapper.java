package org.thingsboard.server.dao.sql.smartPipe;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataReadMeterData;

import java.util.Date;
import java.util.List;

/**
 * 用水量修正
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-10
 */
@Mapper
public interface PipeCopyDataReadMeterDataMapper extends BaseMapper<PipeCopyDataReadMeterData> {

    IPage<PipeCopyDataReadMeterData> getList(IPage<PipeCopyDataReadMeterData> custPage, @Param("param") PartitionCustRequest partitionCustRequest);

    List<JSONObject> sumCorrectByPartitionId(@Param("partitionIdList") List<String> partitionIdList, @Param("start") Long start, @Param("end") Long end);

    List<PipeCopyDataReadMeterData> getListByPartitionId(@Param("partitionIdList") List<String> partitionIdList, @Param("start") Long start, @Param("end") Long end);

    void batchInsert(List<PipeCopyDataReadMeterData> pipeCopyDataReadMeterDataList);

    void deleteByTime(@Param("start") Date start, @Param("end") Date end);
}
