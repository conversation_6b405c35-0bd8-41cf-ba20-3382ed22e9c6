import{_ as v}from"./TreeBox-DDD2iwoR.js";import{_ as C}from"./CardTable-rdWOL4_6.js";import{_ as S}from"./CardSearch-CB_HNR-Q.js";import{_ as I}from"./index-BJ-QPYom.js";import T from"./stationDetailMonitoring-amDn42AY.js";import{d as F,r as s,c as g,l as P,bH as N,b as n,S as M,o as B,g as h,h as D,F as L,q as c,n as j,aB as E,C as A}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as V}from"./usePartition-DkcY9fQ2.js";import{E as q}from"./index-0NlGN6gS.js";import{_ as H}from"./NewOrder.vue_vue_type_script_setup_true_lang-DAnrmOVe.js";import{E as R,d as $}from"./lossControl-DNefZk8I.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./useDetector-BRcb7GRN.js";import"./padStart-BKfyZZDO.js";import"./minBy-DBQvPu-j.js";import"./_baseExtremum-UssVWohW.js";import"./_baseSum-Cz9yialR.js";import"./_baseLt-svgXHEqw.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./FormMap-BGaXSqQF.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./utils-D5nxoMq3.js";import"./useUser-Blb5V02j.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const Y=F({__name:"index",setup(G){const u=s({isDetail:!1}),_=g(),d=g(),r=s({data:[],loading:!0,title:"选择分区",expandOnClickNode:!1,treeNodeHandleClick:async t=>{r.currentProject!==t&&(r.currentProject=t,await a())}}),w=()=>{r.loading=!1},x=s({defaultParams:{date:P().format(N)},filters:[{type:"date",label:"日期",field:"date",clearable:!1},{type:"input",label:"分区名称",field:"name"},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>a()},{type:"default",perm:!0,text:"重置",iconifyIcon:"ep:refresh",click:()=>{var t;(t=d.value)==null||t.resetForm()}},{perm:!0,text:"工单",type:"success",iconifyIcon:"ep:plus",click:()=>{var t,e;(t=i.selectList)!=null&&t.length?i.selectList.length>1?n.warning("只能选择一条数据"):(e=b.value)==null||e.openDialog():n.warning("请选择一条数据")}},{perm:!0,type:"warning",text:"导出",iconifyIcon:"ep:download",click:()=>{var t;(t=_.value)==null||t.exportTable()}}]}]}),i=s({loading:!1,dataList:[],indexVisible:!0,handleRowDbClick(t){i.currentRow=t,u.isDetail=!0},handleSelectChange(t){i.selectList=t},columns:[{prop:"partitionName",label:"分区名称",minWidth:120},{prop:"statusName",label:"分区状态",minWidth:120},{prop:"userNum",label:"用户数(户)",minWidth:120},{prop:"inlet",label:"进水口(个)",minWidth:120},{prop:"supplyTotal",label:"供水量",unit:"(m³)",minWidth:120},{prop:"minValue",label:"MNF夜间最小流",unit:"(m³/h)",minWidth:220,sortable:!0},{prop:"minFlow",label:"夜间最小水量值",unit:"(m³)",minWidth:210,sortable:!0},{prop:"legalUseWater",label:"用户合法用水量",unit:"(m³/h)",minWidth:210,sortable:!0},{prop:"netNightFlow",label:"净夜间流量",unit:"(m³/h)",minWidth:180,sortable:!0},{prop:"lossWater",label:"漏失水量",unit:"(m³)",minWidth:240,sortable:!0},{prop:"mainLineLength",label:"管线长度",unit:"(km)",minWidth:240,sortable:!0},{prop:"unitPipeNightFlowMin",label:"单位管线夜间小流",unit:"(m³/h/km)",minWidth:240,sortable:!0},{prop:"avgDayFlow",label:"日均流量",unit:"(m³/h)",minWidth:150,sortable:!0},{prop:"mnfDivideAvgDayFlow",label:"MNF/日均流量",unit:"(%)",minWidth:180,sortable:!0},{prop:"lossValuation",label:"漏损评估",tag:!0,tagColor(t,e){return e==="一般"?"#e6a23c":e==="较好"?"#318DFF":e==="较差"?"#f56c6c":"#909399"},formatter(t,e){return e||"--"}}],operations:[{text:"切营运",isTextBtn:!0,perm:!0,iconifyIcon:"ep:edit",click:t=>W(t)}],singleSelect:!0,select(t){var e;(e=i.selectList)!=null&&e.length&&i.selectList.findIndex(o=>o.partitionId===t.partitionId)!==-1?i.selectList=[]:i.selectList=[t]},operationWidth:"100px",pagination:{hide:!0,refreshData:({page:t,size:e})=>{i.pagination.page=t,i.pagination.limit=e,a()}}}),W=t=>{M("是否将状态修改为营运中?","提示信息").then(async()=>{try{(await R({id:t.partitionId,status:q.YingYunZhong})).data.code===200?(n.success("操作成功"),a()):n.success("操作失败")}catch{n.error("操作失败")}}).catch(()=>{})},b=g(),a=async()=>{var o,p,m,l;if(!r.currentProject)return;const t=((o=d.value)==null?void 0:o.queryParams)||{},e=await $({...t,partitionId:(p=r.currentProject)==null?void 0:p.value});i.dataList=((m=e.data)==null?void 0:m.data)||[],i.pagination.total=((l=e.data)==null?void 0:l.total)||0},f=V();return B(async()=>{const t=f.getTree(),e=f.getList();await Promise.all([t,e]),r.data=f.Tree.value||[],r.currentProject=r.data[0],a()}),(t,e)=>{const o=I,p=S,m=C,l=v;return h(),D(l,null,{tree:L(()=>[c(o,{ref:"refTree","tree-data":r},null,8,["tree-data"])]),default:L(()=>{var y,k;return[u.isDetail?(h(),D(T,{key:0,device:i.currentRow,onHiddenLoading:w,onBack:e[0]||(e[0]=O=>u.isDetail=!1)},null,8,["device"])):(h(),j(E,{key:1},[c(p,{ref_key:"refSearch",ref:d,config:x},null,8,["config"]),c(m,{ref_key:"refTable",ref:_,class:"card-table",config:i},null,8,["config"])],64)),c(H,{ref_key:"refDialog",ref:b,"default-values":{partitionId:(k=(y=i.selectList)==null?void 0:y[0])==null?void 0:k.partitionId}},null,8,["default-values"])]}),_:1})}}}),Ee=A(Y,[["__scopeId","data-v-d345ed35"]]);export{Ee as default};
