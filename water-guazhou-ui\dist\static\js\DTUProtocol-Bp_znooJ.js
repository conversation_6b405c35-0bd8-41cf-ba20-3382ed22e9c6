import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as U}from"./CardSearch-CB_HNR-Q.js";import{C as F,M as B,b7 as M,bM as V,bq as A,ca as N,cb as E,s as g,j as G,u as j,b as m,cc as J,ay as b,g as f,n as u,p as a,G as n,q as s,F as p,aB as O,aJ as q,aw as z,bh as R,bw as T,h as D,an as v,al as H,ak as K,J as X,H as Q,bK as W}from"./index-r0dFAfgr.js";import{f as Y,i as Z,j as $,k as tt,l as et}from"./index-BggOjNGp.js";import ot from"./dialog-DYfJ1HfJ.js";import{v as at,a as lt}from"./variableDialog-BU6B0mQZ.js";import{n as y}from"./importControl-BvALbT5Z.js";import{I as it}from"./ImportButton-BXPDxx92.js";import{l as rt}from"./lodash.default-B3JdLn1L.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./formValidate-U0WTqY4Y.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./KeyValueTable-yppKjcF3.js";/* empty css                                                                     */import"./reduce-BbPixnH6.js";import"./padStart-BKfyZZDO.js";import"./_baseExtremum-UssVWohW.js";import"./_baseLt-svgXHEqw.js";import"./sortBy-DDhdj0i5.js";import"./max-CCqK09y5.js";import"./minBy-DBQvPu-j.js";import"./_baseSum-Cz9yialR.js";import"./min-ks0CS-3r.js";import"./sumBy-Dpy7mNiE.js";const{$btnPerms:C,$confirm:st}=B(),nt={name:"DTUProtocol",components:{tpDialog:ot,variableDialog:at},setup(){return{Refresh:M,Edit:V,Delete:A,Upload:N,DocumentAdd:E}},data(){return{cardSearchConfig:{filters:[{label:"搜索",field:"name",type:"input",width:"240px"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:g(H),click:()=>this.clickFilterData()},{text:"当前页变量增改",perm:C("DTUVariableAdd"),svgIcon:g(K),click:()=>this.editProtocolVariable()},{text:"导入",perm:C("DTUVariableImport"),click:t=>y(this,t,"DTU",this.currentTemplate),component:g(it)}]}]},cardTableConfig:{loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"name",label:"变量名称",width:80},{prop:"propertyCategory",label:"实际变量名称(非中文)",width:180},{prop:"statType",label:"统计类型",formatter:t=>this.valueTextMap.statType[t.statType]},{prop:"dataType",label:"数据类型",width:140,formatter:t=>this.valueTextMap.dataType[t.dataType]},{prop:"propertyType",label:"变量类型",width:140,formatter:t=>this.valueTextMap.propertyType[t.propertyType]},{prop:"unit",label:"单位"},{prop:"registerType",label:"寄存器类型",width:140,formatter:t=>this.valueTextMap.registerType[t.registerType]},{prop:"functionCode",label:"功能码"},{prop:"registerAddress",label:"寄存器地址",width:140},{prop:"byteCount",label:"读取寄存器个数",width:180},{prop:"bitPosition",label:"位寄存器读取地址",width:200},{prop:"registerSignFlag",label:"数据有无符号",width:140,formatter:t=>this.valueTextMap.registerSignFlag[t.registerSignFlag]},{prop:"sampleDeviation",label:"每小时数据偏差值",width:180},{prop:"order",label:"大小端",width:140,formatter:t=>this.valueTextMap.order[t.order]},{prop:"byteOrder",label:"解析顺序",width:120},{prop:"dataOffset",label:"数据偏移量",width:120},{prop:"samplingMax",label:"采样最大值",width:120,textColor:"#FF5722"},{prop:"samplingMin",label:"采样最小值",width:120,textColor:"#33AB9F"},{prop:"sampleCoef",label:"采样系数",width:100,textColor:"#FFB800"},{prop:"unitCoef",label:"数据小数位",width:100},{prop:"range",label:"量程"},{prop:"formulaProperty",label:"公式"}],pagination:{page:1,limit:15,total:0,layout:"total, prev, pager, next, jumper",handleSize:()=>{},handlePage:t=>{this.cardTableConfig.pagination.page=t,this.cardTableConfig.dataList=this.filterProtocolData.slice(t*15-15,[t*15])}}},actionUrl:"",filterTemplate:"",templateList:[],dialogInfo:{currentTitle:"添加协议模板",visible:!1,template:{},type:"DTU",data:[],close:()=>this.dialogInfo.visible=!1},uploadDis:!1,headers:{},currentTemplate:{},varDialogInfo:{currentTitle:"添加变量",visible:!1,template:{},protocolList:[],prototypeData1:[],prototypeData2:[],filtered:[],close:()=>this.varDialogInfo.visible=!1},protocolData:[],copyInfo:null,currentItem:{},valueTextMap:lt}},computed:{fTemplateList(){return this.templateList.filter(t=>t.name.toLowerCase().includes(this.filterTemplate.toLowerCase()))}},created(){this.actionUrl=G().actionUrl+"/api/deviceTemplate/import/DTU",this.getTemlate(),this.headers["X-Authorization"]="Bearer "+j().token},methods:{getTemlate(){Y("DTU").then(t=>{if(this.templateList=t.data,this.dialogInfo.data=t.data,this.templateList.length>0){const e=this.currentItem.id?this.currentItem:this.templateList[0];this.clickTemplate(e)}else this.filterProtocolData=[],this.cardTableConfig.pagination.total=this.filterProtocolData.length,this.protocolData=[],this.cardTableConfig.dataList=[],m.warning("暂无数据"),this.$forceUpdate()})},clickTemplate(t){this.currentItem=t,Z(t.id).then(e=>{if(this.currentTemplate=e.data,this.protocolData=[],this.cardTableConfig.pagination.page=1,this.currentTemplate.protocolList)for(const[c,r]of this.currentTemplate.protocolList.entries())r.indexNumber=c+1,this.protocolData.push(r);this.filterProtocolData=this.protocolData,this.cardTableConfig.pagination.total=this.filterProtocolData.length,this.cardTableConfig.dataList=this.filterProtocolData.slice(0,15)})},exportTemplate(){$(this.currentTemplate.id).then(t=>{const e=JSON.stringify(t.data);J(e,"export.json")})},uploadSuccess(t){m.info(t.message),this.getTemlate()},handleChange(){},addTemplate(){this.dialogInfo.visible=!0,this.dialogInfo.currentTitle="添加协议模板";const t={name:"",type:"",remark:"",additionalInfo:""};this.dialogInfo.template=t},editTemplate(t){this.dialogInfo.visible=!0,this.dialogInfo.currentTitle="编辑协议模板";for(const e in t)this.dialogInfo.template[e]=t[e]},copyTemplate(t){t&&(this.currentTemplate=t),tt(this.currentTemplate.id).then(e=>{m.info(e.data.result),this.getTemlate()})},async refreshData(){this.getTemlate()},deleteTemplate(t){const e="确定要删除"+t.name+"吗?";st(e,"删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{et(t.id).then(()=>{m.success("删除成功"),this.currentItem={},this.getTemlate()}).catch(c=>{const r=c.data.message;m.error(r)})}).catch(c=>{console.log(c)})},editProtocolVariable(){if(this.varDialogInfo.template=this.currentTemplate,this.varDialogInfo.protocolList=JSON.parse(JSON.stringify(this.cardTableConfig.dataList)),this.protocolData.length!==this.filterProtocolData.length){const t=rt.pullAll(this.protocolData,this.filterProtocolData);this.varDialogInfo.filtered=t}if(this.filterProtocolData.length>15)if(this.cardTableConfig.pagination.page===1)this.varDialogInfo.prototypeData1=[],this.varDialogInfo.prototypeData2=this.filterProtocolData.slice(15,this.filterProtocolData.length);else{const t=this.cardTableConfig.pagination.page*15-15;this.varDialogInfo.prototypeData1=this.filterProtocolData.slice(0,t),this.varDialogInfo.prototypeData2=this.filterProtocolData.slice(this.cardTableConfig.pagination.page*15,this.filterProtocolData.length)}else this.varDialogInfo.prototypeData1=[],this.varDialogInfo.prototypeData2=[];this.varDialogInfo.visible=!0,this.varDialogInfo.currentTitle="协议变量操作"},clickFilterData(){const t=this.$refs.cardSearch.queryParams||{name:""};this.filterProtocolData=this.protocolData.filter(e=>!!(!e.name||!t.name||e.name.includes(t.name))),this.cardTableConfig.pagination.total=this.filterProtocolData.length,this.cardTableConfig.dataList=this.filterProtocolData.slice(0,15)},handleVarChange(){},async uploadFile(t){this.uploadDis=!0,y(this,t,"DTU",this.currentTemplate)}}},pt={class:"template-protocol-container"},ct={class:"left-template-box custom-bg-box-shadow1"},dt={class:"operation-btns"},mt={class:"template-list-box"},ft=["onClick"],ht={class:"t-item-name"},gt={class:"hover-button"},ut=["onClick"],Tt=["onClick"],bt=["onClick"],Dt={class:"right-protocol-box custom-main-bg-box-shadow1"};function vt(t,e,c,r,o,l){const d=X,_=Q,I=W,x=U,w=S,k=b("tpDialog"),P=b("variableDialog");return f(),u("div",pt,[a("div",ct,[e[10]||(e[10]=a("div",{class:"title-box custom-bottom-box-shadow"},[a("p",{class:"title-text top-title"},[a("i",{class:"iconfont icon-shuangjiantouyou"}),n("模板列表 ")])],-1)),s(_,{modelValue:o.filterTemplate,"onUpdate:modelValue":e[1]||(e[1]=i=>o.filterTemplate=i),placeholder:"输入模板名称搜索",class:"tree-filter-create-box"},{append:p(()=>[s(d,{icon:r.Refresh,onClick:e[0]||(e[0]=i=>o.filterTemplate="")},{default:p(()=>e[4]||(e[4]=[n(" 重置 ")])),_:1},8,["icon"])]),_:1},8,["modelValue"]),s(d,{class:"tree-o-btn add-blue-green",icon:r.DocumentAdd,onClick:l.addTemplate},{default:p(()=>e[5]||(e[5]=[n(" 新建模板 ")])),_:1},8,["icon","onClick"]),a("div",dt,[s(d,{icon:r.Edit,class:"node-o-btn edit-primary-blue",onClick:e[2]||(e[2]=i=>l.editTemplate(o.currentItem))},{default:p(()=>e[6]||(e[6]=[n(" 编辑 ")])),_:1},8,["icon"]),s(I,{class:"upload-demo",action:o.actionUrl,"on-change":l.handleChange,headers:o.headers,"show-file-list":!1,"on-success":l.uploadSuccess},{default:p(()=>[s(d,{class:"node-o-btn add-child-blue",icon:r.Upload},{default:p(()=>e[7]||(e[7]=[n(" 导入 ")])),_:1},8,["icon"])]),_:1},8,["action","on-change","headers","on-success"]),s(d,{icon:r.Delete,class:"node-o-btn delete-orange",onClick:e[3]||(e[3]=i=>l.deleteTemplate(o.currentItem))},{default:p(()=>e[8]||(e[8]=[n(" 删除 ")])),_:1},8,["icon"])]),e[11]||(e[11]=a("span",{class:"alltemp"},"所有模板",-1)),a("div",mt,[(f(!0),u(O,null,q(l.fTemplateList,(i,L)=>(f(),u("div",{key:L,class:z(["t-item",{"active-item":i.id===o.currentTemplate.id}]),onClick:h=>l.clickTemplate(i)},[a("p",ht,[e[9]||(e[9]=a("i",{class:"iconfont icon-moban"},null,-1)),n(" "+R(i.name),1)]),a("span",gt,[a("i",{class:"el-icon-edit",style:{color:"#0a81ff"},onClick:T(h=>l.editTemplate(i),["stop"])},null,8,ut),a("i",{class:"iconfont icon-daochu1",style:{color:"#32d1db"},onClick:T(h=>l.exportTemplate(i),["stop"])},null,8,Tt),a("i",{class:"el-icon-delete",style:{color:"#f56c6c"},onClick:T(h=>l.deleteTemplate(i),["stop"])},null,8,bt)])],10,ft))),128))])]),a("div",Dt,[s(x,{ref:"cardSearch",class:"card-search",config:o.cardSearchConfig},null,8,["config"]),s(w,{config:o.cardTableConfig,class:"card-table"},null,8,["config"])]),o.dialogInfo.visible?(f(),D(k,{key:0,"dialog-info":o.dialogInfo,onGetTemlate:l.getTemlate},null,8,["dialog-info","onGetTemlate"])):v("",!0),o.varDialogInfo.visible?(f(),D(P,{key:1,"var-dialog-info":o.varDialogInfo,onGetTemlate:l.getTemlate},null,8,["var-dialog-info","onGetTemlate"])):v("",!0)])}const Ht=F(nt,[["render",vt],["__scopeId","data-v-e00084b1"]]);export{Ht as default};
