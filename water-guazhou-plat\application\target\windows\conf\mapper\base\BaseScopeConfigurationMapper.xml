<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseScopeConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseScopeConfiguration" id="BaseScopeConfigurationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="regionType"    column="region_type"    />
        <result property="minX"    column="min_x"    />
        <result property="minY"    column="min_y"    />
        <result property="maxX"    column="max_x"    />
        <result property="maxY"    column="max_y"    />
    </resultMap>

    <sql id="selectBaseScopeConfigurationVo">
        select id, name, description, region_type, min_x, min_y, max_x, max_y from base_scope_configuration
    </sql>

    <select id="selectBaseScopeConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseScopeConfiguration" resultMap="BaseScopeConfigurationResult">
        <include refid="selectBaseScopeConfigurationVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="regionType != null  and regionType != ''"> and region_type = #{regionType}</if>
        </where>
    </select>
    
    <select id="selectBaseScopeConfigurationById" parameterType="String" resultMap="BaseScopeConfigurationResult">
        <include refid="selectBaseScopeConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseScopeConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseScopeConfiguration">
        insert into base_scope_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="regionType != null">region_type,</if>
            <if test="minX != null">min_x,</if>
            <if test="minY != null">min_y,</if>
            <if test="maxX != null">max_x,</if>
            <if test="maxY != null">max_y,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="regionType != null">#{regionType},</if>
            <if test="minX != null">#{minX},</if>
            <if test="minY != null">#{minY},</if>
            <if test="maxX != null">#{maxX},</if>
            <if test="maxY != null">#{maxY},</if>
         </trim>
    </insert>

    <update id="updateBaseScopeConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseScopeConfiguration">
        update base_scope_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="regionType != null">region_type = #{regionType},</if>
            <if test="minX != null">min_x = #{minX},</if>
            <if test="minY != null">min_y = #{minY},</if>
            <if test="maxX != null">max_x = #{maxX},</if>
            <if test="maxY != null">max_y = #{maxY},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseScopeConfigurationById" parameterType="String">
        delete from base_scope_configuration where id = #{id}
    </delete>

    <delete id="deleteBaseScopeConfigurationByIds" parameterType="String">
        delete from base_scope_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>