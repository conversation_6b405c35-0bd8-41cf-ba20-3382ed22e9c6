<!-- 设备赋码 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      id="cs111"
      :config="TableConfig"
      class="card-table"
    />
  </TreeBox>
</template>

<script lang="ts" setup>
import { shallowRef } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns } from '@/components/type'
import QRCodePopover from './components/QRCodePopover.vue'
import useGlobal from '@/hooks/global/useGlobal'
import { getDeviceTypeTree,
  getSupplierSerch
} from '@/api/equipment_assets/equipmentManage'
import { getDeviceStorageJournalSerch } from '@/api/equipment_assets/ledgerManagement'
import {
  getstoreSerch
} from '@/api/equipment_assets/equipmentOutStock'
import { traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '标签编码', field: 'deviceLabelCode', type: 'input' },
    { label: '设备名称', field: 'name', type: 'input' },
    { label: '设备型号', field: 'model', type: 'input' },
    { label: '供应商',
      field: 'supplierId',
      type: 'select-tree',
      checkStrictly: true,
      defaultExpandAll: true,
      autoFillOptions: config => {
        getSupplierSerch({ page: 1, size: 99999 }).then(res => {
          config.options = traverse(res.data.data.data || [])
        })
      } },
    { label: '所属仓库',
      field: 'storehouseId',
      type: 'select-tree',
      checkStrictly: true,
      defaultExpandAll: true,
      autoFillOptions: config => {
        getstoreSerch({ page: 1, size: -1 }).then(res => {
          config.options = traverse(res.data.data.data || [])
        })
      } }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '批量下载',
          icon: ICONS.EXPORT,
          click: () => {
            const length:any[] = []
            TableConfig.selectList?.forEach((i:any) => {
              length.push(getindex(i))
            })
            if (length.length) {
              download(length)
            } else {
              ElMessage.warning('请选择设备')
            }
          }
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  selectList: [],
  handleSelectChange: val => {
    TableConfig.selectList = val
  },
  columns: [
    { label: '标签编码', prop: 'deviceLabelCode' },
    { label: '设备名称', prop: 'name' },
    { label: '设备型号', prop: 'model' },
    { label: '所属大类', prop: 'topType' },
    { label: '所属类别', prop: 'type' },
    { label: '供应商', prop: 'supplierName' },
    { label: '所在仓库', prop: 'storehouseName' },
    { label: '存放位置', prop: 'shelvesName' }
  ],
  operationWidth: '200px',
  operations: [
    {
      type: 'primary',
      text: '二维码',
      icon: ICONS.DETAIL,
      component: shallowRef(QRCodePopover),
      perm: $btnPerms('RoleManageEdit')
    },
    {
      type: 'primary',
      text: '下载',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.EXPORT,
      click: row => {
        download([getindex(row)])
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const TreeData = reactive<SLTreeConfig>({
  title: '',
  data: [],
  currentProject: {},
  expandOnClickNode: false,
  isFilterTree: true,
  treeNodeHandleClick: data => {
    TreeData.currentProject = data
    refreshData()
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    deviceTypeId: TreeData.currentProject.id,
    ...(refSearch.value?.queryParams || {})
  }
  getDeviceStorageJournalSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

function init() {
  getDeviceTypeTree().then(res => {
    TreeData.data = traverse(res.data.data || [])
    TreeData.currentProject = res.data.data[0]
    refreshData()
  })
}

function download(length) {
  // 获取canvas标签
  const canvas = document?.getElementsByTagName('canvas') as any
  // 创建a标签
  const a = document.createElement('a')
  length.forEach(i => {
    // 获取二维码的url并赋值为a.href
    a.href = canvas[i].toDataURL('img/png') || ''
    // 设置下载文件的名字
    a.download = TableConfig.dataList[i].deviceLabelCode || ''
    // 点击事件，相当于下载
    a.click()
  })
  // 提示信息
  ElMessage.success('下载中')
}

function getindex(row:any) {
  return TableConfig.dataList.indexOf(row)
}

onMounted(async () => {
  init()
})
</script>
