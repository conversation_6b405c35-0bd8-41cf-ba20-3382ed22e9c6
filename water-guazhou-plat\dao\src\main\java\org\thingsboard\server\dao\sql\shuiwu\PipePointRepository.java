package org.thingsboard.server.dao.sql.shuiwu;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.shuiwu.PipePointEntity;

public interface PipePointRepository extends JpaRepository<PipePointEntity, String> {

    @Query("SELECT pp FROM PipePointEntity pp " +
            "WHERE pp.tenantId = ?2 AND (pp.code LIKE %?1% OR pp.name LIKE %?1%)")
    Page<PipePointEntity> finList(String keyword, String tenantId, Pageable pageable);
}
