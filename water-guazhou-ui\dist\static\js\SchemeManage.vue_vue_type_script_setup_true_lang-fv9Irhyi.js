import{_ as F}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{z as m,d as L,r as l,c as f,l as P,bE as G,S as q,b as s,g as T,h as R,F as V,q as u,i as p,aq as z}from"./index-r0dFAfgr.js";import{_ as B}from"./Search-NSrhrIa_.js";const E=o=>m({url:"/api/gis/plan/list",method:"get",params:o}),O=o=>m({url:"/api/gis/plan",method:"post",data:o}),I=o=>m({url:"/api/gis/plan/remove",method:"delete",data:o}),$=L({__name:"SchemeManage",props:{type:{}},emits:["row-click"],setup(o,{expose:d,emit:h}){const g=o,_=h,y=l({filters:[{type:"input",field:"name",onChange:()=>n()}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>n()},{perm:!0,text:"重置",iconifyIcon:"ep:refresh",click:()=>b()}]}],defaultParams:{}}),n=async()=>{var e;if(!g.type){a.dataList=[],a.pagination.total=0,a.pagination.page=1;return}try{const t={...((e=i.value)==null?void 0:e.queryParams)||{},type:g.type,page:a.pagination.page||1,size:a.pagination.limit||20},r=await E(t);a.dataList=r.data.data.data||[],a.pagination.total=r.data.data.total||0}catch(t){console.log(t)}},i=f(),b=()=>{var e;(e=i.value)==null||e.resetForm()},k=l({title:"方案管理",dialogWidth:700,group:[],labelPosition:"right",labelWidth:"100px",defaultValue:{},cancel:!1,model:!1,draggable:!0,closeOnPressEscape:!0}),c=f(),D=()=>{var e;(e=c.value)==null||e.openDialog(),n()},x=()=>{var e;(e=c.value)==null||e.closeDialog()},a=l({indexVisible:!0,height:400,columns:[{label:"方案名称",prop:"name"},{label:"创建时间",prop:"createTime",formatter(e,t){return P(t).format(G)}},{label:"备注",prop:"remark"}],dataList:[],operations:[{perm:!0,text:"使用",click:e=>S(e)},{perm:!0,text:"删除",type:"danger",click:e=>C(e)}],pagination:{refreshData:({page:e,size:t})=>{a.pagination.page=e||1,a.pagination.limit=t||20,n()}}}),S=e=>{a.currentRow=e,_("row-click",e)},C=e=>{q("确定删除?","提示信息").then(async()=>{try{const t=e.id?[e.id]:[];if(!t.length){s.error("请先选择要删除的数据");return}const r=await I(t);r.data.code===200?(s.success("删除成功"),n()):s.error(r.data.message)}catch(t){s.error("删除失败"),console.log(t)}}).catch(()=>{})};return d({openDialog:D,closeDialog:x}),(e,t)=>{const r=B,v=z,w=F;return T(),R(w,{ref_key:"refDialog",ref:c,config:p(k)},{default:V(()=>[u(r,{ref_key:"refSearch",ref:i,config:p(y),style:{"margin-bottom":"20px"}},null,8,["config"]),u(v,{config:p(a)},null,8,["config"])]),_:1},8,["config"])}}});export{E as G,O as P,$ as _};
