package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBanner;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class SsPortalBannerSaveRequest extends SaveRequest<SsPortalBanner> {
    // 轮播图片
    private String image;

    // 排列顺序
    private Integer orderNum;

    @Override
    protected SsPortalBanner build() {
        SsPortalBanner entity = new SsPortalBanner();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalBanner update(String id) {
        SsPortalBanner entity = new SsPortalBanner();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalBanner entity) {
        entity.setImage(image);
        entity.setActive(false);
        entity.setOrderNum(orderNum);
    }

}