<!-- 环比曲线 -->
<template>
  <div
    v-loading="loading"
    class=""
  >
    <Search
      ref="cardSearch"
      :config="cardSearchConfig"
    >
    </Search>
    <!-- 图表模式 -->
    <div
      ref="agriEcoDev"
      class="card-ehcarts"
    >
      <VChart
        ref="refChart"
        :theme="useAppStore().isDark?'dark':''"
        class="card-ehcarts"
        :option="state.chartOption"
      ></VChart>
    </div>
  </div>
</template>
<script lang="ts" setup>
import elementResizeDetectorMaker from 'element-resize-detector'
import { Search as SearchIcon } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { useAppStore } from '@/store'
import { lineOption } from '../../data/echart'
import { IECharts } from '@/plugins/echart'
import { getRatio } from '@/api/pipeNetworkMonitoring/flowMonitoring'
import useGlobal from '@/hooks/global/useGlobal'

const { $messageWarning } = useGlobal()
const erd = elementResizeDetectorMaker()
const loading = ref<boolean>(false)
const refChart = ref<IECharts>()
const props = defineProps<{
  stationName?: string,
  stationId?: string
}>()
const cardSearch = ref<ISearchIns>()
const agriEcoDev = ref<any>()

const state = reactive<{
  chartOption: any,
}>({
  chartOption: null
})

watch(
  () => props.stationId,
  () => {
    console.log(props.stationId)
    refreshData()
  }
)

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    date: dayjs().format()
  },
  filters: [
    // {
    //   type: 'select',
    //   label: '统计类型',
    //   field: 'va1',
    //   width: '140px',
    //   options: [
    //     { label: '净累计', value: '净累计' }
    //   ]
    // },
    { type: 'month', label: '日期', field: 'date', width: '300px' },
    {
      type: 'btn-group',
      btns: [
        { perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => {
            if (props.stationId) {
              refreshData()
            } else {
              $messageWarning('请选择监测点')
            }
          }
        }
      ]
    }
  ]
})

// 配置加载图表数据
const refuseChart = (data:any) => {
  const chartOption = lineOption()
  chartOption.series = []
  let dataX = []
  for (const key in data) {
    const newData = data[key]?.map(res => { return res.value })
    dataX = dataX.length === 0 ? data[key]?.map(res => { return res.ts }) : dataX
    const serie = {
      name: key,
      smooth: true,
      data: newData,
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }
    chartOption.series.push(serie)
    chartOption.yAxis[0].name = '流量(m³)'
    chartOption.xAxis.data = dataX
  }
  refChart.value?.clear()
  nextTick(() => {
    erd.listenTo(agriEcoDev.value, () => {
      state.chartOption = chartOption
      refChart.value?.resize()
    })
  })
}
const refreshData = async () => {
  loading.value = true
  const query = cardSearch.value?.queryParams || {}
  const params = {
    start: dayjs(query.date).startOf('month').valueOf(),
    end: dayjs(query.date).endOf('month').valueOf(),
    stationId: props.stationId as string,
    type: '2'
  }
  const res = await getRatio(params)
  const data = res.data?.data
  refuseChart(data)
  loading.value = false
}

onMounted(() => {
  if (props.stationId) {
    refreshData()
  }
})
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100vh - 300px);
}

.card-ehcarts {
  height: calc(100vh - 300px);
  width: 100%;
}
</style>
