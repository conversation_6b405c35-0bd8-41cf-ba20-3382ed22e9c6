<template>
  <Layout :show-bars="false" :show-headers="true">
    <produce_beibei v-if="state.siteName === 'beibei'" />
    <produce v-else />
  </Layout>
</template>
<script lang="ts" setup>
import Layout from '../layout/index.vue';
import produce from './produce.vue';
import produce_beibei from './produce_beibei.vue';

const state = reactive<{ siteName: ISiteName }>({
  siteName: window.SITE_CONFIG.SITENAME
});
</script>
<style lang="scss" scoped></style>
