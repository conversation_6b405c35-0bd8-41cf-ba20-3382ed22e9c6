package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseDatabaseUpgradeService;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseUpgrade;
import org.thingsboard.server.dao.sql.base.BaseDatabaseUpgradeMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseUpgradePageRequest;

/**
 * 平台管理-数据库升级记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class BaseDatabaseUpgradeServiceImpl implements IBaseDatabaseUpgradeService {

    @Autowired
    private BaseDatabaseUpgradeMapper baseDatabaseUpgradeMapper;

    /**
     * 查询平台管理-数据库升级记录
     *
     * @param id 平台管理-数据库升级记录主键
     * @return 平台管理-数据库升级记录
     */
    @Override
    public BaseDatabaseUpgrade selectBaseDatabaseUpgradeById(String id) {
        return baseDatabaseUpgradeMapper.selectBaseDatabaseUpgradeById(id);
    }

    /**
     * 查询平台管理-数据库升级记录列表
     *
     * @param baseDatabaseUpgrade 平台管理-数据库升级记录
     * @return 平台管理-数据库升级记录
     */
    @Override
    public IPage<BaseDatabaseUpgrade> selectBaseDatabaseUpgradeList(BaseDatabaseUpgradePageRequest baseDatabaseUpgrade) {
        return baseDatabaseUpgradeMapper.selectBaseDatabaseUpgradeList(baseDatabaseUpgrade);
    }

    /**
     * 新增平台管理-数据库升级记录
     *
     * @param baseDatabaseUpgrade 平台管理-数据库升级记录
     * @return 结果
     */
    @Override
    public int insertBaseDatabaseUpgrade(BaseDatabaseUpgrade baseDatabaseUpgrade) {
        baseDatabaseUpgrade.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseDatabaseUpgradeMapper.insertBaseDatabaseUpgrade(baseDatabaseUpgrade);
    }

    /**
     * 修改平台管理-数据库升级记录
     *
     * @param baseDatabaseUpgrade 平台管理-数据库升级记录
     * @return 结果
     */
    @Override
    public int updateBaseDatabaseUpgrade(BaseDatabaseUpgrade baseDatabaseUpgrade) {
        return baseDatabaseUpgradeMapper.updateBaseDatabaseUpgrade(baseDatabaseUpgrade);
    }

    /**
     * 批量删除平台管理-数据库升级记录
     *
     * @param ids 需要删除的平台管理-数据库升级记录主键
     * @return 结果
     */
    @Override
    public int deleteBaseDatabaseUpgradeByIds(List<String> ids) {
        return baseDatabaseUpgradeMapper.deleteBaseDatabaseUpgradeByIds(ids);
    }

    /**
     * 删除平台管理-数据库升级记录信息
     *
     * @param id 平台管理-数据库升级记录主键
     * @return 结果
     */
    @Override
    public int deleteBaseDatabaseUpgradeById(String id) {
        return baseDatabaseUpgradeMapper.deleteBaseDatabaseUpgradeById(id);
    }
}
