<template>
  <div
    class="title-card__header"
    :class="[props.type, props.size]"
    :style="styleVars"
  >
    <slot name="title">
      <span class="title-text">
        <i>
          {{ title }}
        </i>
      </span>
    </slot>
    <slot name="right"></slot>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{
  title: string;
  /**
   * 标题占位宽度，默认200,大概能包含6个汉字，最小124,即不能包含汉字，请自行根据具体情况调整宽度
   */
  titleWidth?: number;
  type?: 'simple';
  size?: 'small';
}>();
const styleVars = computed(() => {
  return {
    '--titleWidth': (props.titleWidth || 200) + 'px',
    '--diamondLeft': (props.titleWidth || 200) - 76 + 'px'
  };
});
</script>
<style lang="scss" scoped>
.title-card__header {
  display: flex;
  align-items: center;

  height: 36px;
  width: 100%;
  border-radius: 8px 8px 0 0;
  background:
    url(../imgs/title_card/diamonds.png) var(--diamondLeft, 124px) 50% / 40px
      20px no-repeat,
    url(../imgs/title_card/diamonds_1.png) 8px 50% / 20px 20px no-repeat,
    url(../imgs/title_card/bg_3.png) 0 0 / var(--titleWidth, 200px) 100%
      no-repeat,
    url(../imgs/title_card/bg_2.png) 20px 0 / var(--titleWidth, 200px) 100%
      no-repeat,
    url(../imgs/title_card/bg_1.png) 40px 0 / var(--titleWidth, 200px) 100%
      no-repeat,
    linear-gradient(333.29deg, rgba(31, 67, 255, 0) 7.95%, #1fbcff 100%) 0 0 /100%
      100% no-repeat;
  backdrop-filter: blur(4px);
  &.small {
    height: 24px;
    font-size: 12px;
    line-height: 24px;
    .title-text {
    }
  }
  &.simple {
    background:
      url(../imgs/title_card/diamonds.png) var(--diamondLeft, 124px) 50% / 40px
        20px no-repeat,
      url(../imgs/title_card/diamonds_1.png) 8px 50% / 20px 20px no-repeat,
      no-repeat,
      linear-gradient(
        90deg,
        rgba(43, 166, 249, 0.78) 0%,
        rgba(43, 166, 249, 0) 80.42%
      ),
      rgba(46, 63, 95, 0.11);
    backdrop-filter: blur(7px);
  }
  .title-text {
    font-weight: 600;
    color: #fff;
    line-height: 36px;
    padding-left: 25px;
  }
}
</style>
