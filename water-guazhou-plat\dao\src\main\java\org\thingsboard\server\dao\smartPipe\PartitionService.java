package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.DTO.DMAOverviewDTO;
import org.thingsboard.server.dao.model.DTO.PartitionTreeDTO;
import org.thingsboard.server.dao.model.DTO.WaterOverviewDTO;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-25
 *
 * dma分区服务
 */
public interface PartitionService {


    Partition getById(String id);

    Partition save(Partition partition);

    void delete(List<String> ids) throws ThingsboardException;

    List<PartitionTreeDTO> getList(Map<String, Object> params);

    List<PartitionTreeDTO> getPartitionDeviceTree(String type, String tenantId);

    List<PartitionTreeDTO> getListById(String partitionId, String tenantId);

    List getChildPositionByPid(String pid, String tenantId);

    void getAllChildId(String pid, List<String> partitionIds);

    void getDMAChildId(Partition partition, List<String> partitionIds);

    void getDMAChild(Partition partition, List<Partition> partitionIds);

    Object getParentById(String id, String tenantId);

    List getParentAndLayerRange(String partitionId, String tenantId);

    List<Partition> getAll(Map<String, Object> params);

    List<String> getAllId(String tenantId);

    BigDecimal getTotalWater(String partitionId, String direction, String month);

    WaterOverviewDTO getWaterOverview(String tenantId);

    JSONObject getTotalSupplyCount(String type, String tenantId);

    JSONObject getNrwCount(String type, String tenantId);

    JSONObject getReferenceLeakSort(String name, String tenantId);

    JSONArray getSupplyCount(String type, Integer grade, String mountType, String tenantId);

    JSONArray getSaleCount(String type, Integer grade, String tenantId, String date);

    JSONObject monitor(String partitionId, String tenantId);

    JSONObject getMonthDetailFlow(String partitionId, String tenantId);

    JSONObject getOverview(String partitionId, String tenantId);

    /**
     * 获取分区用户数
     * @param partitionIdList
     * @return
     */
    Map<String, Integer> getUserNum(List<String> partitionIdList);

    void changeStatus(Partition partition);

    /**
     * 获取分区该月抄表数
     * @param partitionIdList
     * @param ymList
     * @return
     */
    Map<String, Integer> getCopiedNum(List<String> partitionIdList, List<String> ymList);

    List<Partition> getListByIdIn(List<String> partitionIdList);

    List<Partition> getchildByPid(String partitionId, String tenantId);

    List<Partition> getAll(String type, String status, String partitionName, String tenantId);

    List<DMAOverviewDTO> getDMAOverview(String status, String partitionName, String tenantId);

    Map<String, Integer> getMountNum(List<String> partitionIdList, String mountType, String direction);

    List<Partition> getRootIdNameList(String tenantId);

    List<Partition> getAllIdNameByPidIn(List<String> partitionIdList, String type);

    String getNRWByName(String name, String tenantId);

    BigDecimal inWater(String name, String tenantId);

    JSONObject getNrwYearByName(String name, String tenantId);

    Object getWaterOverviewHP(String tenantId);

    List<JSONObject> getNightMinFLow(String tenantId);

    JSONObject getNumCount(String tenantId);

    List<JSONObject> getReferenceLeakSortHP(String month, String tenantId);

    Object getOverviewHP(String month, String tenantId);

    List<JSONObject> getNightMinFlow(String partitionId, Long start, Long end, Double minFlow, Double maxFlow, String tenantId);

}
