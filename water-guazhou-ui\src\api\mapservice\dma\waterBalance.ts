import { request } from '@/plugins/axios'

/**
 * 查询水平衡自产水供水量
 * @param params
 * @returns
 */
export const GetWaterBalanceTotalWater = (params: any) => {
  return request({
    url: '/api/spp/dma/partition/totalWater',
    method: 'get',
    params
  })
}
/**
 * 水量填报
 * @param params
 */
export const DMAReportWater = (params: any) => {
  return request({
    url: '/api/spp/useWaterReport',
    method: 'post',
    data: params
  })
}
/**
 * 查询水平衡明细
 * @param params
 * @returns
 */
export const GetWaterBalanceDetail = (params: {
  partitionId: string
  ym: string
}) => {
  return request({
    url: `/api/spp/useWaterReport/waterBalance`,
    method: 'get',
    params
  })
}
