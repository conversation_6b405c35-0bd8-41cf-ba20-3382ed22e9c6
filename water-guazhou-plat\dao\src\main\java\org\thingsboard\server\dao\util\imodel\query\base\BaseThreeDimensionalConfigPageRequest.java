package org.thingsboard.server.dao.util.imodel.query.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseThreeDimensionalConfig;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 公共管理平台-三维配置对象 base_three_dimensional_config
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@ApiModel(value = "三维配置", description = "公共管理平台-三维配置实体类")
@Data
public class BaseThreeDimensionalConfigPageRequest extends PageableQueryEntity<BaseThreeDimensionalConfig> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 瓦片数据配置id
     */
    @ApiModelProperty(value = "瓦片数据配置id")
    private String tileId;

    /**
     * 矢量数据配置ids
     */
    @ApiModelProperty(value = "矢量数据配置ids")
    private String vectorIds;

    /**
     * 方案管理id
     */
    @ApiModelProperty(value = "方案管理id")
    private String schemeId;
}
