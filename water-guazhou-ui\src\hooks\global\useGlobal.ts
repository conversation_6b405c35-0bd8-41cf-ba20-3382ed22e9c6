import { ElMessage, ElMessageBox } from 'element-plus'

import Video from 'video.js'
import 'video.js/dist/video-js.min.css'
// import moment from 'moment'
import { usePermissionStore } from '@/store'

export default () => {
  const $video = Video
  /**
   * 格式化时间
   * @param val 时间
   * @param format 格式化
   * @returns 格式化后的值
   */
  const $format = (val: string | Date, format?: string) => {
    if (!val) return '-'
    if (!format) return moment(val).format('YYYY-MM-DD HH:mm:ss')
    format = format.replace('Y', 'YYYY')
    format = format.replace('M', 'MM')
    format = format.replace('D', 'DD')
    format = format.replace('H', 'HH')
    format = format.replace('m', 'mm')
    format = format.replace('s', 'ss')
    return moment(val).format(format)
  }
  /**
   * 对对象中的指定属性进行时间格式转换
   * @param row 对象
   * @param key 属性
   * @param format 转换格式
   * @returns 转换后的值
   */
  const format = (row?: any, key?: string, format?: string) => {
    if (!row || !key) return '-'
    return $format(row[key], format)
  }
  /**
   * 把时间转换成数字
   * @param val 时间
   * @returns Returns the primitive value of the specified object.
   */
  const formatToNumber = (val: string | Date) => moment(val).valueOf()
  /**
   * 把时间数组转换成数字数组
   * @param val 时间数组
   * @returns 【number,number】
   */
  const formatToArrayNumber = (val: any[]) => val.map(item => moment(item).valueOf())
  /**
   * 按钮权限
   * @param perms
   * @returns 如果开启按钮权限，则过滤，否则则全部返回true
   */
  const $btnPerms = function (perms) {
    const permissionStore = usePermissionStore()
    if (permissionStore.usebtnperms) {
      let hasPermission = false
      if (!perms) return hasPermission
      const permission = permissionStore.btnPerms
      hasPermission = permission.some(item => item.permissions === perms)
      return hasPermission
    }
    return true
  }
  const btnPerms = $btnPerms
  /**
   * treeData格式化
   * @param tree
   * @param keys
   * @returns
   */
  const $formatTree = (tree, keys = {
    label: 'name', value: 'id', id: 'id', children: 'children'
  }) => {
    tree = tree.map(node => {
      if (node.children && node.children.length) {
        node.children = $formatTree(node.children, keys)
      } else {
        delete node.children
      }
      const obj: any = {}
      for (const key in keys) {
        obj[key] = node[keys[key]]
      }
      obj.data = node
      return obj
    })

    return tree
  }

  const $confirm = ElMessageBox.confirm

  const $message = ElMessage
  const $messageSuccess = (message: string) => ElMessage({
    type: 'success',
    duration: 2000,
    showClose: true,
    message
  })
  const $messageError = (message: string) => ElMessage({
    type: 'error',
    duration: 2000,
    showClose: true,
    message
  })
  const $messageWarning = (message: string) => ElMessage({
    type: 'warning',
    duration: 2000,
    showClose: true,
    message
  })
  const $messageInfo = (message: string) => ElMessage({
    type: 'info',
    duration: 2000,
    showClose: true,
    message
  })
  return {
    $message,
    $messageInfo,
    $messageSuccess,
    $messageWarning,
    $messageError,
    $confirm,
    $formatTree,
    $format,
    format,
    formatToNumber,
    formatToArrayNumber,
    $video,
    $btnPerms,
    btnPerms
  }
}
