import{e as o,y as p,a as m,j as l,i as g}from"./Point-WxyopZva.js";import{l as d}from"./widget-BcWKanF2.js";import"./index-r0dFAfgr.js";import{j as u,i as c}from"./MapView-DaoQedLH.js";import{a as y}from"./BitmapContainer-ziwQ7v9F.js";import{f,u as x}from"./LayerView-BSt9B8Gh.js";import{a as w}from"./GraphicsView2D-DDTEO9AX.js";import{n as v}from"./HighlightGraphicContainer-B4wkFrY6.js";import{v as _}from"./ExportStrategy-BadISnDs.js";import{c as H}from"./ExportImageParameters-BiedgHNY.js";import{i as I}from"./RefreshableLayerView-DUeNHzrW.js";import{S as V}from"./MapServiceLayerViewHelper-Cc5aApGi.js";import{a as P}from"./drapedUtils-DJwxIB1g.js";import"./pe-B8dP0-Ut.js";import"./WGLContainer-Dyx9110G.js";import"./definitions-826PWLuy.js";import"./FramebufferObject-8j9PRuxE.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./vec4f32-CjrfB-0a.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./enums-L38xj_2E.js";import"./number-CoJp78Rz.js";import"./ProgramTemplate-tdUBoAol.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./utils-DPUVnAXL.js";import"./StyleDefinition-Bnnz5uyC.js";import"./config-MDUrh2eL.js";import"./GeometryUtils-BRRfazic.js";import"./Container-BwXq1a-x.js";import"./earcut-BJup91r2.js";import"./cimAnalyzer-CMgqZsaO.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./normalizeUtilsSync-NMksarRY.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./AttributeStoreView-B0-phoCE.js";import"./TiledDisplayObject-C5kAiJtw.js";import"./visualVariablesUtils-0WgcmuMn.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./Matcher-v9ErZwmD.js";import"./tileUtils-B7X19rIS.js";import"./libtess-lH4Jrtkh.js";import"./TurboLine-CDscS66C.js";import"./ExpandedCIM-C1laM-_7.js";import"./schemaUtils-DLXXqxNF.js";import"./util-DPgA-H2V.js";import"./ComputedAttributeStorage-CF7WDnl8.js";import"./arcadeTimeUtils-CyWQANWo.js";import"./executionError-BOo4jP8A.js";import"./centroid-UTistape.js";import"./BaseGraphicContainer-Cqw9Xlck.js";import"./FeatureContainer-B5oUlI2-.js";import"./TileContainer-CC8_A7ZF.js";import"./vec3f32-nZdmKIgz.js";import"./Bitmap-CraE42_6.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./scaleUtils-DgkF6NQH.js";import"./identify-4SBo5EZk.js";import"./IdentifyResult-4DxLVhTm.js";import"./popupUtils-BjdidZV3.js";const U=t=>{let e=class extends t{initialize(){this.exportImageParameters=new H({layer:this.layer})}destroy(){this.exportImageParameters.destroy(),this.exportImageParameters=null}get floors(){var i;return((i=this.view)==null?void 0:i.floors)??null}get exportImageVersion(){var i;return(i=this.exportImageParameters)==null||i.commitProperty("version"),this.commitProperty("timeExtent"),this.commitProperty("floors"),(this._get("exportImageVersion")||0)+1}canResume(){var i;return!!super.canResume()&&!((i=this.timeExtent)!=null&&i.isEmpty)}};return o([p()],e.prototype,"exportImageParameters",void 0),o([p({readOnly:!0})],e.prototype,"floors",null),o([p({readOnly:!0})],e.prototype,"exportImageVersion",null),o([p()],e.prototype,"layer",void 0),o([p()],e.prototype,"suspended",void 0),o([p(u)],e.prototype,"timeExtent",void 0),e=o([m("esri.views.layers.MapImageLayerView")],e),e};let a=class extends U(I(f(x))){constructor(){super(...arguments),this._highlightGraphics=new c,this._updateHash=""}fetchPopupFeatures(t,e){return this._popupHighlightHelper.fetchPopupFeatures(t,e)}update(t){const e=`${this.exportImageVersion}/${t.state.id}/${t.pixelRatio}/${t.stationary}`;this._updateHash!==e&&(this._updateHash=e,this.strategy.update(t).catch(r=>{l(r)||g.getLogger(this.declaredClass).error(r)}),t.stationary&&this._popupHighlightHelper.updateHighlightedFeatures(t.state.resolution)),this._highlightView.processUpdate(t)}attach(){const{imageMaxWidth:t,imageMaxHeight:e,version:r}=this.layer,i=r>=10.3,n=r>=10;this._bitmapContainer=new y,this.container.addChild(this._bitmapContainer),this._highlightView=new w({view:this.view,graphics:this._highlightGraphics,requestUpdateCallback:()=>this.requestUpdate(),container:new v(this.view.featuresTilingScheme),defaultPointSymbolEnabled:!1}),this.container.addChild(this._highlightView.container),this._popupHighlightHelper=new V({createFetchPopupFeaturesQueryGeometry:(s,h)=>P(s,h,this.view),highlightGraphics:this._highlightGraphics,highlightGraphicUpdated:(s,h)=>{this._highlightView.graphicUpdateHandler({graphic:s,property:h})},layerView:this,updatingHandles:this.updatingHandles}),this.strategy=new _({container:this._bitmapContainer,fetchSource:this.fetchImageBitmap.bind(this),requestUpdate:this.requestUpdate.bind(this),imageMaxWidth:t,imageMaxHeight:e,imageRotationSupported:i,imageNormalizationSupported:n,hidpi:!0}),this.addAttachHandles(d(()=>this.exportImageVersion,()=>this.requestUpdate())),this.requestUpdate()}detach(){this.strategy.destroy(),this.container.removeAllChildren(),this._bitmapContainer.removeAllChildren(),this._highlightView.destroy(),this._popupHighlightHelper.destroy()}moveStart(){}viewChange(){}moveEnd(){this.requestUpdate()}supportsSpatialReference(t){return this.layer.serviceSupportsSpatialReference(t)}async doRefresh(){this._updateHash="",this.requestUpdate()}isUpdating(){return this.strategy.updating||this.updateRequested}fetchImage(t,e,r,i){return this.layer.fetchImage(t,e,r,{timeExtent:this.timeExtent,floors:this.floors,...i})}fetchImageBitmap(t,e,r,i){return this.layer.fetchImageBitmap(t,e,r,{timeExtent:this.timeExtent,floors:this.floors,...i})}highlight(t){return this._popupHighlightHelper.highlight(t)}};o([p()],a.prototype,"strategy",void 0),o([p()],a.prototype,"updating",void 0),a=o([m("esri.views.2d.layers.MapImageLayerView2D")],a);const Dt=a;export{Dt as default};
