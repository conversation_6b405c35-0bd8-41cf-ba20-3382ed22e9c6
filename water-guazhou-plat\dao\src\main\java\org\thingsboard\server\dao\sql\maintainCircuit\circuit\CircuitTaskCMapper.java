package org.thingsboard.server.dao.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskC;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface CircuitTaskCMapper extends BaseMapper<CircuitTaskC> {

    List<CircuitTaskC> getList(String mainId);

    CircuitTaskC selectFirstByDeviceLabelCode(@Param("deviceLabelCode") String deviceLabelCode, @Param("thisTime") Date time);

    int countByLabelCode(@Param("deviceLabelCode") String deviceLabelCode);

    List<Map> getNowYearCircuitByDeviceLabelCode(@Param("deviceLabelCode") String deviceLabelCode, @Param("nowYear") Date nowYear);

    CircuitTaskC selectLatestByDeviceLabelCode(@Param("deviceLabelCode") String deviceLabelCode);
}
