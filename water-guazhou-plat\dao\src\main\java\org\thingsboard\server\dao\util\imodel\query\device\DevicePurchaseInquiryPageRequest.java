package org.thingsboard.server.dao.util.imodel.query.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseInquiry;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class DevicePurchaseInquiryPageRequest extends AdvancedPageableQueryEntity<DevicePurchaseInquiry, DevicePurchaseInquiryPageRequest> {
    // 采购单子表ID
    private String purchaseDetailId;

    // 设备编码
    private String serialId;

    // 设备名称
    private String deviceName;

    // 设备型号
    private String deviceModel;

    // 采购单编号
    private String purchaseCode;

    // 采购单标题
    private String purchaseTitle;

    // 采购人
    private String userDepartmentId;

    // 采购人
    private String userId;


}
