"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[4165],{10158:(e,t,r)=>{r.d(t,{n:()=>N});var o,i=r(43697),s=r(35454),n=r(96674),a=r(5600),l=r(90578),p=r(36030),u=r(71715),c=r(52011),d=r(75215),y=r(63213),h=r(1231),m=r(42843),b=(r(66577),r(67676),r(82971)),w=r(86973);let v=o=class extends n.wq{constructor(e){super(e),this.type="query-table"}clone(){const{workspaceId:e,query:t,oidFields:r,spatialReference:i,geometryType:s}=this,n={workspaceId:e,query:t,oidFields:r,spatialReference:i?.clone()??void 0,geometryType:s};return new o(n)}};var S;(0,i._)([(0,p.J)({queryTable:"query-table"})],v.prototype,"type",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:!0}})],v.prototype,"workspaceId",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:!0}})],v.prototype,"query",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:!0}})],v.prototype,"oidFields",void 0),(0,i._)([(0,a.Cb)({type:b.Z,json:{write:!0}})],v.prototype,"spatialReference",void 0),(0,i._)([(0,p.J)(w.M)],v.prototype,"geometryType",void 0),v=o=(0,i._)([(0,c.j)("esri.layers.support.source.QueryTableDataSource")],v);let _=S=class extends n.wq{constructor(e){super(e),this.type="raster"}clone(){const{workspaceId:e,dataSourceName:t}=this;return new S({workspaceId:e,dataSourceName:t})}};var C;(0,i._)([(0,p.J)({raster:"raster"})],_.prototype,"type",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:!0}})],_.prototype,"dataSourceName",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:!0}})],_.prototype,"workspaceId",void 0),_=S=(0,i._)([(0,c.j)("esri.layers.support.source.RasterDataSource")],_);let g=C=class extends n.wq{constructor(e){super(e),this.type="table"}clone(){const{workspaceId:e,gdbVersion:t,dataSourceName:r}=this;return new C({workspaceId:e,gdbVersion:t,dataSourceName:r})}};var j,f;(0,i._)([(0,p.J)({table:"table"})],g.prototype,"type",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:!0}})],g.prototype,"workspaceId",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:!0}})],g.prototype,"gdbVersion",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:!0}})],g.prototype,"dataSourceName",void 0),g=C=(0,i._)([(0,c.j)("esri.layers.support.source.TableDataSource")],g);const T=(0,s.w)()({esriLeftInnerJoin:"left-inner-join",esriLeftOuterJoin:"left-outer-join"});let R=j=class extends n.wq{constructor(e){super(e),this.type="join-table"}readLeftTableSource(e,t,r){return F()(e,t,r)}castLeftTableSource(e){return(0,d.N7)(V(),e)}readRightTableSource(e,t,r){return F()(e,t,r)}castRightTableSource(e){return(0,d.N7)(V(),e)}clone(){const{leftTableKey:e,rightTableKey:t,leftTableSource:r,rightTableSource:o,joinType:i}=this,s={leftTableKey:e,rightTableKey:t,leftTableSource:r?.clone()??void 0,rightTableSource:o?.clone()??void 0,joinType:i};return new j(s)}};(0,i._)([(0,p.J)({joinTable:"join-table"})],R.prototype,"type",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:!0}})],R.prototype,"leftTableKey",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:!0}})],R.prototype,"rightTableKey",void 0),(0,i._)([(0,a.Cb)({json:{write:!0}})],R.prototype,"leftTableSource",void 0),(0,i._)([(0,u.r)("leftTableSource")],R.prototype,"readLeftTableSource",null),(0,i._)([(0,l.p)("leftTableSource")],R.prototype,"castLeftTableSource",null),(0,i._)([(0,a.Cb)({json:{write:!0}})],R.prototype,"rightTableSource",void 0),(0,i._)([(0,u.r)("rightTableSource")],R.prototype,"readRightTableSource",null),(0,i._)([(0,l.p)("rightTableSource")],R.prototype,"castRightTableSource",null),(0,i._)([(0,p.J)(T)],R.prototype,"joinType",void 0),R=j=(0,i._)([(0,c.j)("esri.layers.support.source.JoinTableDataSource")],R);let x=null;function F(){return x||(x=(0,y.d)({types:V()})),x}let P=null;function V(){return P||(P={key:"type",base:null,typeMap:{"data-layer":N,"map-layer":m.R}}),P}const M={key:"type",base:null,typeMap:{"join-table":R,"query-table":v,raster:_,table:g}};let N=f=class extends n.wq{constructor(e){super(e),this.type="data-layer"}clone(){const{fields:e,dataSource:t}=this;return new f({fields:e,dataSource:t})}};(0,i._)([(0,p.J)({dataLayer:"data-layer"})],N.prototype,"type",void 0),(0,i._)([(0,a.Cb)({type:[h.Z],json:{write:!0}})],N.prototype,"fields",void 0),(0,i._)([(0,a.Cb)({types:M,json:{write:!0}})],N.prototype,"dataSource",void 0),N=f=(0,i._)([(0,c.j)("esri.layers.support.source.DataLayerSource")],N),N.from=(0,d.se)(N)},42843:(e,t,r)=>{r.d(t,{R:()=>u});var o,i=r(43697),s=r(96674),n=r(5600),a=r(75215),l=(r(67676),r(36030)),p=r(52011);let u=o=class extends s.wq{constructor(e){super(e),this.type="map-layer"}clone(){const{mapLayerId:e,gdbVersion:t}=this;return new o({mapLayerId:e,gdbVersion:t})}};(0,i._)([(0,l.J)({mapLayer:"map-layer"})],u.prototype,"type",void 0),(0,i._)([(0,n.Cb)({type:a.z8,json:{write:!0}})],u.prototype,"mapLayerId",void 0),(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],u.prototype,"gdbVersion",void 0),u=o=(0,i._)([(0,p.j)("esri.layers.support.source.MapLayerSource")],u)},6388:(e,t,r)=>{r.d(t,{Z:()=>y});var o,i=r(43697),s=(r(66577),r(35454)),n=r(96674),a=r(22974),l=r(5600),p=(r(75215),r(52011)),u=r(6570);const c=new s.X({upperLeft:"upper-left",lowerLeft:"lower-left"});let d=o=class extends n.wq{constructor(e){super(e),this.extent=null,this.mode="view",this.originPosition="upper-left",this.tolerance=1}clone(){return new o((0,a.d9)({extent:this.extent,mode:this.mode,originPosition:this.originPosition,tolerance:this.tolerance}))}};(0,i._)([(0,l.Cb)({type:u.Z,json:{write:{overridePolicy(){return{enabled:"view"===this.mode}}}}})],d.prototype,"extent",void 0),(0,i._)([(0,l.Cb)({type:["view","edit"],json:{write:!0}})],d.prototype,"mode",void 0),(0,i._)([(0,l.Cb)({type:String,json:{read:c.read,write:c.write}})],d.prototype,"originPosition",void 0),(0,i._)([(0,l.Cb)({type:Number,json:{write:{overridePolicy(){return{enabled:"view"===this.mode}}}}})],d.prototype,"tolerance",void 0),d=o=(0,i._)([(0,p.j)("esri.rest.support.QuantizationParameters")],d);const y=d},14165:(e,t,r)=>{r.d(t,{Z:()=>P});var o=r(43697),i=r(66577),s=r(92835),n=r(35454),a=r(96674),l=r(22974),p=r(70586),u=r(5600),c=r(90578),d=r(36030),y=r(52011),h=r(30556),m=r(75215),b=r(33955),w=r(10158),v=r(2368);r(67676);let S=class extends((0,v.J)(a.wq)){constructor(e){super(e),this.onFields=null,this.operator=null,this.searchTerm=null,this.searchType=null}};(0,o._)([(0,u.Cb)({type:[String],json:{write:{enabled:!0,overridePolicy(){return{enabled:(0,p.pC)(this.onFields)&&this.onFields.length>0}}}}})],S.prototype,"onFields",void 0),(0,o._)([(0,u.Cb)({type:String,json:{write:!0}})],S.prototype,"operator",void 0),(0,o._)([(0,u.Cb)({type:String,json:{write:!0}})],S.prototype,"searchTerm",void 0),(0,o._)([(0,u.Cb)({type:String,json:{write:!0}})],S.prototype,"searchType",void 0),S=(0,o._)([(0,y.j)("esri.rest.support.FullTextSearch")],S);const _=S;var C,g=r(6388),j=r(58539),f=r(82971),T=r(94139);const R=new n.X({esriSpatialRelIntersects:"intersects",esriSpatialRelContains:"contains",esriSpatialRelCrosses:"crosses",esriSpatialRelDisjoint:"disjoint",esriSpatialRelEnvelopeIntersects:"envelope-intersects",esriSpatialRelIndexIntersects:"index-intersects",esriSpatialRelOverlaps:"overlaps",esriSpatialRelTouches:"touches",esriSpatialRelWithin:"within",esriSpatialRelRelation:"relation"}),x=new n.X({esriSRUnit_Meter:"meters",esriSRUnit_Kilometer:"kilometers",esriSRUnit_Foot:"feet",esriSRUnit_StatuteMile:"miles",esriSRUnit_NauticalMile:"nautical-miles",esriSRUnit_USNauticalMile:"us-nautical-miles"});let F=C=class extends a.wq{static from(e){return(0,m.TJ)(C,e)}constructor(e){super(e),this.aggregateIds=null,this.cacheHint=void 0,this.compactGeometryEnabled=!1,this.datumTransformation=null,this.defaultSpatialReferenceEnabled=!1,this.distance=void 0,this.dynamicDataSource=void 0,this.formatOf3DObjects=null,this.fullText=null,this.gdbVersion=null,this.geometry=null,this.geometryPrecision=void 0,this.groupByFieldsForStatistics=null,this.having=null,this.historicMoment=null,this.maxAllowableOffset=void 0,this.maxRecordCountFactor=1,this.multipatchOption=null,this.num=void 0,this.objectIds=null,this.orderByFields=null,this.outFields=null,this.outSpatialReference=null,this.outStatistics=null,this.parameterValues=null,this.pixelSize=null,this.quantizationParameters=null,this.rangeValues=null,this.relationParameter=null,this.resultType=null,this.returnCentroid=!1,this.returnDistinctValues=!1,this.returnExceededLimitFeatures=!0,this.returnGeometry=!1,this.returnQueryGeometry=!1,this.returnM=void 0,this.returnZ=void 0,this.sourceSpatialReference=null,this.spatialRelationship="intersects",this.start=void 0,this.sqlFormat=null,this.text=null,this.timeExtent=null,this.timeReferenceUnknownClient=!1,this.units=null,this.where=null}castDatumTransformation(e){return"number"==typeof e||"object"==typeof e?e:null}writeHistoricMoment(e,t){t.historicMoment=e&&e.getTime()}writeParameterValues(e,t){if(e){const r={};for(const t in e){const o=e[t];Array.isArray(o)?r[t]=o.map((e=>e instanceof Date?e.getTime():e)):o instanceof Date?r[t]=o.getTime():r[t]=o}t.parameterValues=r}}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10,t.where="1=1"}writeWhere(e,t){t.where=e||"1=1"}clone(){return new C((0,l.d9)({aggregateIds:this.aggregateIds,cacheHint:this.cacheHint,compactGeometryEnabled:this.compactGeometryEnabled,datumTransformation:this.datumTransformation,defaultSpatialReferenceEnabled:this.defaultSpatialReferenceEnabled,distance:this.distance,fullText:this.fullText,gdbVersion:this.gdbVersion,geometry:this.geometry,geometryPrecision:this.geometryPrecision,groupByFieldsForStatistics:this.groupByFieldsForStatistics,having:this.having,historicMoment:(0,p.pC)(this.historicMoment)?new Date(this.historicMoment.getTime()):null,maxAllowableOffset:this.maxAllowableOffset,maxRecordCountFactor:this.maxRecordCountFactor,multipatchOption:this.multipatchOption,num:this.num,objectIds:this.objectIds,orderByFields:this.orderByFields,outFields:this.outFields,outSpatialReference:this.outSpatialReference,outStatistics:this.outStatistics,parameterValues:this.parameterValues,pixelSize:this.pixelSize,quantizationParameters:this.quantizationParameters,rangeValues:this.rangeValues,relationParameter:this.relationParameter,resultType:this.resultType,returnDistinctValues:this.returnDistinctValues,returnGeometry:this.returnGeometry,returnCentroid:this.returnCentroid,returnExceededLimitFeatures:this.returnExceededLimitFeatures,returnQueryGeometry:this.returnQueryGeometry,returnM:this.returnM,returnZ:this.returnZ,dynamicDataSource:this.dynamicDataSource,sourceSpatialReference:this.sourceSpatialReference,spatialRelationship:this.spatialRelationship,start:this.start,sqlFormat:this.sqlFormat,text:this.text,timeExtent:this.timeExtent,timeReferenceUnknownClient:this.timeReferenceUnknownClient,units:this.units,where:this.where}))}};F.MAX_MAX_RECORD_COUNT_FACTOR=5,(0,o._)([(0,u.Cb)({json:{write:!0}})],F.prototype,"aggregateIds",void 0),(0,o._)([(0,u.Cb)({type:Boolean,json:{write:!0}})],F.prototype,"cacheHint",void 0),(0,o._)([(0,u.Cb)({type:Boolean,json:{default:!1,write:!0}})],F.prototype,"compactGeometryEnabled",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],F.prototype,"datumTransformation",void 0),(0,o._)([(0,c.p)("datumTransformation")],F.prototype,"castDatumTransformation",null),(0,o._)([(0,u.Cb)({type:Boolean,json:{default:!1,write:!0}})],F.prototype,"defaultSpatialReferenceEnabled",void 0),(0,o._)([(0,u.Cb)({type:Number,json:{write:{overridePolicy:e=>({enabled:e>0})}}})],F.prototype,"distance",void 0),(0,o._)([(0,u.Cb)({type:w.n,json:{write:!0}})],F.prototype,"dynamicDataSource",void 0),(0,o._)([(0,u.Cb)({type:String,json:{write:!0}})],F.prototype,"formatOf3DObjects",void 0),(0,o._)([(0,u.Cb)({type:[_],json:{write:{enabled:!0,overridePolicy(){return{enabled:(0,p.pC)(this.fullText)&&this.fullText.length>0}}}}})],F.prototype,"fullText",void 0),(0,o._)([(0,u.Cb)({type:String,json:{write:!0}})],F.prototype,"gdbVersion",void 0),(0,o._)([(0,u.Cb)({types:i.qM,json:{read:b.im,write:!0}})],F.prototype,"geometry",void 0),(0,o._)([(0,u.Cb)({type:Number,json:{write:!0}})],F.prototype,"geometryPrecision",void 0),(0,o._)([(0,u.Cb)({type:[String],json:{write:!0}})],F.prototype,"groupByFieldsForStatistics",void 0),(0,o._)([(0,u.Cb)({type:String,json:{write:!0}})],F.prototype,"having",void 0),(0,o._)([(0,u.Cb)({type:Date})],F.prototype,"historicMoment",void 0),(0,o._)([(0,h.c)("historicMoment")],F.prototype,"writeHistoricMoment",null),(0,o._)([(0,u.Cb)({type:Number,json:{write:!0}})],F.prototype,"maxAllowableOffset",void 0),(0,o._)([(0,u.Cb)({type:Number,cast:e=>e<1?1:e>C.MAX_MAX_RECORD_COUNT_FACTOR?C.MAX_MAX_RECORD_COUNT_FACTOR:e,json:{write:{overridePolicy:e=>({enabled:e>1})}}})],F.prototype,"maxRecordCountFactor",void 0),(0,o._)([(0,u.Cb)({type:["xyFootprint"],json:{write:!0}})],F.prototype,"multipatchOption",void 0),(0,o._)([(0,u.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],F.prototype,"num",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],F.prototype,"objectIds",void 0),(0,o._)([(0,u.Cb)({type:[String],json:{write:!0}})],F.prototype,"orderByFields",void 0),(0,o._)([(0,u.Cb)({type:[String],json:{write:!0}})],F.prototype,"outFields",void 0),(0,o._)([(0,u.Cb)({type:f.Z,json:{name:"outSR",write:!0}})],F.prototype,"outSpatialReference",void 0),(0,o._)([(0,u.Cb)({type:[j.Z],json:{write:{enabled:!0,overridePolicy(){return{enabled:(0,p.pC)(this.outStatistics)&&this.outStatistics.length>0}}}}})],F.prototype,"outStatistics",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],F.prototype,"parameterValues",void 0),(0,o._)([(0,h.c)("parameterValues")],F.prototype,"writeParameterValues",null),(0,o._)([(0,u.Cb)({type:T.Z,json:{write:!0}})],F.prototype,"pixelSize",void 0),(0,o._)([(0,u.Cb)({type:g.Z,json:{write:!0}})],F.prototype,"quantizationParameters",void 0),(0,o._)([(0,u.Cb)({type:[Object],json:{write:!0}})],F.prototype,"rangeValues",void 0),(0,o._)([(0,u.Cb)({type:String,json:{read:{source:"relationParam"},write:{target:"relationParam",overridePolicy(){return{enabled:"relation"===this.spatialRelationship}}}}})],F.prototype,"relationParameter",void 0),(0,o._)([(0,u.Cb)({type:String,json:{write:!0}})],F.prototype,"resultType",void 0),(0,o._)([(0,u.Cb)({type:Boolean,json:{default:!1,write:!0}})],F.prototype,"returnCentroid",void 0),(0,o._)([(0,u.Cb)({type:Boolean,json:{default:!1,write:!0}})],F.prototype,"returnDistinctValues",void 0),(0,o._)([(0,u.Cb)({type:Boolean,json:{default:!0,write:!0}})],F.prototype,"returnExceededLimitFeatures",void 0),(0,o._)([(0,u.Cb)({type:Boolean,json:{write:!0}})],F.prototype,"returnGeometry",void 0),(0,o._)([(0,u.Cb)({type:Boolean,json:{default:!1,write:!0}})],F.prototype,"returnQueryGeometry",void 0),(0,o._)([(0,u.Cb)({type:Boolean,json:{default:!1,write:!0}})],F.prototype,"returnM",void 0),(0,o._)([(0,u.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],F.prototype,"returnZ",void 0),(0,o._)([(0,u.Cb)({type:f.Z,json:{write:!0}})],F.prototype,"sourceSpatialReference",void 0),(0,o._)([(0,d.J)(R,{ignoreUnknown:!1,name:"spatialRel"})],F.prototype,"spatialRelationship",void 0),(0,o._)([(0,u.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],F.prototype,"start",void 0),(0,o._)([(0,h.c)("start"),(0,h.c)("num")],F.prototype,"writeStart",null),(0,o._)([(0,u.Cb)({type:String,json:{write:!0}})],F.prototype,"sqlFormat",void 0),(0,o._)([(0,u.Cb)({type:String,json:{write:!0}})],F.prototype,"text",void 0),(0,o._)([(0,u.Cb)({type:s.Z,json:{write:!0}})],F.prototype,"timeExtent",void 0),(0,o._)([(0,u.Cb)({type:Boolean,json:{default:!1,write:!0}})],F.prototype,"timeReferenceUnknownClient",void 0),(0,o._)([(0,d.J)(x,{ignoreUnknown:!1}),(0,u.Cb)({json:{write:{overridePolicy(e){return{enabled:!!e&&null!=this.distance&&this.distance>0}}}}})],F.prototype,"units",void 0),(0,o._)([(0,u.Cb)({type:String,json:{write:{overridePolicy(e){return{enabled:null!=e||null!=this.start&&this.start>0}}}}})],F.prototype,"where",void 0),(0,o._)([(0,h.c)("where")],F.prototype,"writeWhere",null),F=C=(0,o._)([(0,y.j)("esri.rest.support.Query")],F);const P=F},58539:(e,t,r)=>{r.d(t,{Z:()=>y});var o,i=r(43697),s=r(35454),n=r(96674),a=r(22974),l=r(5600),p=(r(75215),r(52011)),u=r(30556);const c=new s.X({count:"count",sum:"sum",min:"min",max:"max",avg:"avg",stddev:"stddev",var:"var",exceedslimit:"exceedslimit",percentile_cont:"percentile-continuous",percentile_disc:"percentile-discrete",EnvelopeAggregate:"envelope-aggregate",CentroidAggregate:"centroid-aggregate",ConvexHullAggregate:"convex-hull-aggregate"});let d=o=class extends n.wq{constructor(e){super(e),this.maxPointCount=void 0,this.maxRecordCount=void 0,this.maxVertexCount=void 0,this.onStatisticField=null,this.outStatisticFieldName=null,this.statisticType=null,this.statisticParameters=null}writeStatisticParameters(e,t){"percentile-continuous"!==this.statisticType&&"percentile-discrete"!==this.statisticType||(t.statisticParameters=(0,a.d9)(e))}clone(){return new o({maxPointCount:this.maxPointCount,maxRecordCount:this.maxRecordCount,maxVertexCount:this.maxVertexCount,onStatisticField:this.onStatisticField,outStatisticFieldName:this.outStatisticFieldName,statisticType:this.statisticType,statisticParameters:(0,a.d9)(this.statisticParameters)})}};(0,i._)([(0,l.Cb)({type:Number,json:{write:!0}})],d.prototype,"maxPointCount",void 0),(0,i._)([(0,l.Cb)({type:Number,json:{write:!0}})],d.prototype,"maxRecordCount",void 0),(0,i._)([(0,l.Cb)({type:Number,json:{write:!0}})],d.prototype,"maxVertexCount",void 0),(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],d.prototype,"onStatisticField",void 0),(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],d.prototype,"outStatisticFieldName",void 0),(0,i._)([(0,l.Cb)({type:String,json:{read:{source:"statisticType",reader:c.read},write:{target:"statisticType",writer:c.write}}})],d.prototype,"statisticType",void 0),(0,i._)([(0,l.Cb)({type:Object})],d.prototype,"statisticParameters",void 0),(0,i._)([(0,u.c)("statisticParameters")],d.prototype,"writeStatisticParameters",null),d=o=(0,i._)([(0,p.j)("esri.rest.support.StatisticDefinition")],d);const y=d}}]);