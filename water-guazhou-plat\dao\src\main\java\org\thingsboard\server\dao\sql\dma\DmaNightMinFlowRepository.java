package org.thingsboard.server.dao.sql.dma;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.dma.DmaNightMinFlowEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.Date;
import java.util.List;

@SqlDao
public interface DmaNightMinFlowRepository extends CrudRepository<DmaNightMinFlowEntity, String> {

    Page<DmaNightMinFlowEntity> findALlByPartitionIdAndCreateTimeIsBetween(String partitionId, Date startTime, Date endTime, Pageable pageRequest);

    List<DmaNightMinFlowEntity> findALlByPartitionIdAndCreateTimeIsBetween(String partitionId, Date startTime, Date endTime);

    // @Query("select new DmaNightMinFlowEntity(a, b) from DmaNightMinFlowEntity a, DmaPartitionEntity b where a.partitionId = b.id and a.partitionId in ?1 and a.createTime between ?2 and ?3 order by a.createTime desc ")
    Page<DmaNightMinFlowEntity> findAllByPartitionIdIn(List<String> partitionIdList, Date start, Date end, Pageable pageable);

    List<DmaNightMinFlowEntity> findAllByPartitionIdAndCreateTimeBetweenOrderByCreateTimeDesc(String id, Date start, Date end);
}
