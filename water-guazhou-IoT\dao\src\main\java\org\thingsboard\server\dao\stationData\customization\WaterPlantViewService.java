package org.thingsboard.server.dao.stationData.customization;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.alarm.Alarm;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.alarm.AlarmService;
import org.thingsboard.server.dao.client.*;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.DTO.StationAttrDTO;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.stationData.StationDataService;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 水厂总览API
 */
@Slf4j
@Service
public class WaterPlantViewService {

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private BaseObtainDataService obtainDataService;

    @Autowired
    private MedicineManageFeignClient medicineManageFeignClient;

    @Autowired
    private AssayRecordFeignClient assayRecordFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private AlarmFeignClient alarmFeignClient;


    /**
     * 日供水
     */
    public Object timeAreaUseWater(String projectId, Long start, Long end, TenantId tenantId) throws ThingsboardException {
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, "水厂", projectId);
        List<StationEntity> stationList = stationPageData.getData();
        if (stationList == null || stationList.size() < 1) {
            log.error("该项目下没有水厂! projectId = {}", projectId);
            return null;
        }

        Date now = new Date();
        Date todayStart = null;
        if (end != null) {
            now = new Date(end);
        }
        if (start != null) {
            todayStart = new Date(start);
        } else {
            Calendar instance = Calendar.getInstance();
            instance.set(Calendar.HOUR_OF_DAY, 0);
            instance.set(Calendar.MINUTE, 0);
            instance.set(Calendar.SECOND, 0);
            todayStart = instance.getTime();
        }


        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = dateFormat.format(now);
        Map<String, BigDecimal> resultMap = new LinkedHashMap<>();
        for (int i = 1; i < 24; i++) {
            if (i < 10) {
                resultMap.put(format + " 0" + i, new BigDecimal(0));
            } else {
                resultMap.put(format + " " + i, new BigDecimal(0));
            }
        }

        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains("取水")) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                continue;
            }

            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    String attr = deviceId + "." + deviceFullData.getProperty();
                    List<String> attributes = Collections.singletonList(attr);
                    totalFlowData = obtainDataService.getDeviceData(attributes, todayStart.getTime(), now.getTime(), DateUtils.HOUR, null, tenantId);
                    break;
                }
            }

            if (totalFlowData != null) {
                Set<String> keySet = resultMap.keySet();
                for (String key : keySet) {
                    LinkedHashMap<String, BigDecimal> todayTotalFlowMap = totalFlowData.get(key);
                    if (todayTotalFlowMap != null && !todayTotalFlowMap.isEmpty()) {
                        BigDecimal hourData = new ArrayList<>(todayTotalFlowMap.values()).get(0);
                        if (hourData != null) {
                            resultMap.put(key, resultMap.get(key).add(hourData));
                        }
                    }
                }
            }
        }

        return resultMap;
    }

    /**
     * 周供水分析（非自然周，统计7天）
     */
    public Object weekSupplyWaterAnalyze(String projectId, TenantId tenantId) throws ThingsboardException {
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, "水厂", projectId);
        List<StationEntity> stationList = stationPageData.getData();

        Calendar instance = Calendar.getInstance();
        Date now = instance.getTime();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);

        Date date = new Date(instance.getTimeInMillis() - (6 * 24 * 60 * 60 * 1000));

        List<JSONObject> result = new ArrayList<>();
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                continue;
            }

            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            // 统计数据
            // 查询累计流量
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    String attr = deviceId + "." + deviceFullData.getProperty();
                    List<String> attributes = Collections.singletonList(attr);
                    totalFlowData = obtainDataService.getDeviceData(attributes, date.getTime(), now.getTime(), DateUtils.DAY, null, tenantId);
                    break;
                }
            }
            if (totalFlowData != null && !totalFlowData.isEmpty()) {
                JSONObject data = new JSONObject();
                data.put("station", station.getName());
                data.put("data", totalFlowData);
                result.add(data);
            }
        }

        return result;
    }

    /**
     * 总览统计
     */
    public Object viewCount(String projectId, TenantId tenantId) throws ThingsboardException {
        JSONObject result = new JSONObject();

        List<Device> deviceList = deviceFeignClient.getDeviceByProject(projectId);

        // 统计设备数据
        int deviceTotal = 0;
        int deviceExceptionTotal = 0;
        if (deviceList != null) {
            deviceTotal = deviceList.size();
            for (Device device : deviceList) {
                if (!device.isStatus()) {
                    deviceExceptionTotal++;
                }
            }
        }
        result.put("deviceTotal", deviceTotal);
        result.put("deviceExceptionTotal", deviceExceptionTotal);

        // 统计供水量
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, "水厂", projectId);
        List<StationEntity> stationList = stationPageData.getData();

        Calendar instance = Calendar.getInstance();
        Date now = instance.getTime();

        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        Date yesterdayStart = new Date(todayStart.getTime() - (24 * 60 * 60 * 1000));
        Date yesterdayEnd = new Date(todayStart.getTime() - (1000));

        // 查询供水量数据
        BigDecimal yesterdayTotalFlow = new BigDecimal("0");
        BigDecimal todayTotalFlow = new BigDecimal("0");
        // 查询能耗数据
        BigDecimal yesterdayEnergyIn = new BigDecimal("0");
        BigDecimal todayEnergyIn = new BigDecimal("0");
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                continue;
            }

            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            // 统计数据
            // 查询今日、昨日累计流量
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> energyInData = null;
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                    totalFlowData = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), now.getTime(), DateUtils.DAY, null, tenantId);
                    break;
                }
                if ("ENERGY_IN".equals(deviceFullData.getProperty())) {// 有功电能
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                    energyInData = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), now.getTime(), DateUtils.DAY, null, tenantId);
                    break;
                }
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            if (totalFlowData != null) {
                LinkedHashMap<String, BigDecimal> yesterdayTotalFlowMap = totalFlowData.get(dateFormat.format(yesterdayStart));
                BigDecimal yesterdayData = BigDecimal.ZERO;
                if (yesterdayTotalFlowMap != null && !yesterdayTotalFlowMap.isEmpty() && new ArrayList<>(yesterdayTotalFlowMap.values()).get(0) != null) {
                    yesterdayData = new ArrayList<>(yesterdayTotalFlowMap.values()).get(0);
                }
                BigDecimal todayData = BigDecimal.ZERO;
                LinkedHashMap<String, BigDecimal> todayTotalFlowMap = totalFlowData.get(dateFormat.format(todayStart));
                if (todayTotalFlowMap != null && !todayTotalFlowMap.isEmpty() && new ArrayList<>(todayTotalFlowMap.values()).get(0) != null) {
                    todayData = new ArrayList<>(todayTotalFlowMap.values()).get(0);
                }
                yesterdayTotalFlow = yesterdayTotalFlow.add(yesterdayData);
                todayTotalFlow = todayTotalFlow.add(todayData);
            }
            if (energyInData != null) {
                LinkedHashMap<String, BigDecimal> yesterdayEnergyInMap = energyInData.get(dateFormat.format(yesterdayStart));
                BigDecimal yesterdayData = BigDecimal.ZERO;
                if (yesterdayEnergyInMap != null && !yesterdayEnergyInMap.isEmpty() && new ArrayList<>(yesterdayEnergyInMap.values()).get(0) != null) {
                    yesterdayData = new ArrayList<>(yesterdayEnergyInMap.values()).get(0);
                }
                BigDecimal todayData = BigDecimal.ZERO;
                LinkedHashMap<String, BigDecimal> todayEnergyInMap = totalFlowData.get(dateFormat.format(todayStart));
                if (todayEnergyInMap != null && !todayEnergyInMap.isEmpty() && new ArrayList<>(todayEnergyInMap.values()).get(0) != null) {
                    todayData = new ArrayList<>(todayEnergyInMap.values()).get(0);
                }
                yesterdayEnergyIn = yesterdayEnergyIn.add(yesterdayData);
                todayEnergyIn = todayEnergyIn.add(todayData);
            }
        }
        result.put("yesterdayTotalFlow", yesterdayTotalFlow);
        result.put("todayTotalFlow", todayTotalFlow);
        result.put("yesterdayEnergyIn", yesterdayEnergyIn);
        result.put("todayEnergyIn", todayEnergyIn);

        return result;
    }

    /**
     * 生产看板头部统计
     * 1. 上月供水量
     * 2. 本月供水量
     * 3. 年供水量
     * 4. 报警数
     */
    public Object produceDashboardCount(String projectId, String stationType, TenantId tenantId) {
        // 查询指定类型的站点列表
        PageData<StationEntity> stationEntityPageData = stationFeignClient.list(1, 99999, stationType, projectId);
        List<StationEntity> stationList = stationEntityPageData.getData();

        if (stationList == null) {
            return new ArrayList<>();
        }

        BigDecimal total = new BigDecimal("0");
        BigDecimal monthTotal = new BigDecimal("0");
        BigDecimal lastMonthTotal = new BigDecimal("0");
        int alarm = 0;

        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.MONTH, 0);
        instance.set(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);

        Date startTime = instance.getTime();
        Date endTime = new Date();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        // 查询各个站点的数据
        for (StationEntity station : stationList) {
            try {
                // 查询站点数据以及站点的动态属性列表
                List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
                StationAttrDTO stationAttr = null;
                for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                    if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                        stationAttr = stationAttrDTO;
                        break;
                    }
                }

                if (stationAttr == null) {
                    log.error("水厂未设置供水相关的动态属性分组");
                    continue;
                }

                List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

                // 统计数据
                // 按月查询今年的累计流量
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
                for (DeviceFullData deviceFullData : stationDataDetail) {
                    if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                        List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                        totalFlowData = obtainDataService.getDeviceData(attributes, startTime.getTime(), endTime.getTime(), DateUtils.MONTH, null, tenantId);
                        break;
                    }
                }

                // 本月
                String monthKey = dateFormat.format(endTime);
                if (totalFlowData != null) {
                    LinkedHashMap<String, BigDecimal> dataMap = totalFlowData.get(monthKey);
                    if (dataMap != null && !dataMap.isEmpty()) {
                        for (BigDecimal value : dataMap.values()) {
                            if (value != null) {
                                monthTotal = monthTotal.add(value);
                            }
                        }
                    }
                }

                // 上月
                instance.setTime(endTime);
                if (instance.get(Calendar.MONTH) > 0) {
                    instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) - 1);
                    String lastMonthKey = dateFormat.format(instance.getTime());
                    if (totalFlowData != null) {
                        LinkedHashMap<String, BigDecimal> dataMap = totalFlowData.get(lastMonthKey);
                        if (dataMap != null && !dataMap.isEmpty()) {
                            for (BigDecimal value : dataMap.values()) {
                                if (value != null) {
                                    lastMonthTotal = lastMonthTotal.add(value);
                                }
                            }
                        }
                    }
                }

                // 本年
                if (totalFlowData != null) {
                    for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : totalFlowData.entrySet()) {
                        for (Map.Entry<String, BigDecimal> dataEntry : entry.getValue().entrySet()) {
                            BigDecimal value = dataEntry.getValue();
                            if (value != null) {
                                total = total.add(value);
                            }
                        }
                    }
                }

                // 查询报警数
                List<Alarm> alarmList = alarmFeignClient.findAlarmByStation(station.getId(), dateFormat.parse(monthKey).getTime(), endTime.getTime());
                alarm = alarm + (alarmList == null ? 0 : alarmList.size());

            } catch (Exception e) {
                e.printStackTrace();
            }

        }

        JSONObject result = new JSONObject();
        result.put("total", total);
        result.put("monthTotal", monthTotal);
        result.put("lastMonthTotal", lastMonthTotal);
        result.put("alarm", alarm);

        return result;
    }
}
