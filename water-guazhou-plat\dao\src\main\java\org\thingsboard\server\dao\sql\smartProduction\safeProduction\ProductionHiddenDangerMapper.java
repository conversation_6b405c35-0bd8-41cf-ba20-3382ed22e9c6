package org.thingsboard.server.dao.sql.smartProduction.safeProduction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.SafeProductionRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionHiddenDander;

@Mapper
public interface ProductionHiddenDangerMapper extends BaseMapper<ProductionHiddenDander> {

    IPage<ProductionHiddenDander> findList(IPage<ProductionHiddenDander> ipage, @Param("param") SafeProductionRequest request);
}
