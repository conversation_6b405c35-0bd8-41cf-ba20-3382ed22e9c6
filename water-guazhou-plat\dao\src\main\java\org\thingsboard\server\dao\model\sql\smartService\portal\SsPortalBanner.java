package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_banner")
public class SsPortalBanner {
    // id
    private String id;

    // 轮播图片
    private String image;

    // 是否设为活动轮播图
    private Boolean active;

    // 排列顺序
    private Integer orderNum;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
