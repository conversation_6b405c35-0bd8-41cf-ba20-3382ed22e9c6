import{d as Nt,c as Ue,r as ht,am as rr,o as Et,g as ue,n as we,h as Qe,F as Le,q as me,i as H,al as dn,an as _t,p as _e,cs as fn,bh as hr,aB as Pr,aJ as Mr,G as cn,bw as kr,bb as hn,aw as nr,J as Fr,H as _n,C as Vt,bd as pn,bA as lr,a8 as Ge,ax as Ze,av as rt,dy as _r,fS as mn,cN as ur,bB as Br,t as Ht,x as vn,bu as gn,d6 as xn,dg as En,dh as yn,cE as bn,bU as Sn,bW as Ln,bz as An}from"./index-r0dFAfgr.js";/* empty css                         */import{a as Rn}from"./GeneralProcessing-CQ8i9ijT.js";import{d as On,o as Tn}from"./index-D9ERhRP6.js";import{H as wn,D as Cn}from"./DPlayer.min-_HMH7IVX.js";import{W as Dn,X as In}from"./WSPlayer-B40t_qqh.js";import Pn from"./PTZ-hYuvIhtf.js";import Mn from"./btns-B3YiSmAd.js";import kn from"./Intercom-z_58ITv_.js";/* empty css                  */const Fn={class:nr({"custom-tree-node":!0})},Bn={class:"label"},Un={key:0,class:"btns"},jn=Nt({__name:"SimpleTree",props:{config:{}},setup(e){const d=Ue(),i=ht({search:"",data:[],defaultProps:{children:"children",label:"name",disabled:"disabled"},currentProject:""}),x=e,A=(y,m,L)=>{x.config.click&&x.config.click(y,m,L)},C=()=>{d.value.filter(i.search)},E=(y,m)=>y?m[i.defaultProps.label].includes(y):!0;return rr(()=>x.config.data,()=>{i.data=x.config.data}),rr(()=>x.config.currentProject,()=>{var y;i.currentProject=x.config.currentProject.id,(y=d.value)==null||y.setCurrentKey(x.config.currentProject.id)}),Et(()=>{i.data=x.config.data,x.config.currentProject&&setTimeout(()=>{var y;i.currentProject=x.config.currentProject.id,(y=d.value)==null||y.setCurrentKey(x.config.currentProject.id)},0)}),(y,m)=>{var a;const L=Fr,_=_n;return ue(),we("div",null,[x.config.search?(ue(),Qe(_,{key:0,modelValue:H(i).search,"onUpdate:modelValue":m[0]||(m[0]=s=>H(i).search=s),style:{width:"100%"},placeholder:"请输入",class:"input_search",size:((a=y.config.search)==null?void 0:a.size)??"small"},{append:Le(()=>{var s;return[me(L,{icon:H(dn),size:((s=y.config.search)==null?void 0:s.size)??"small",onClick:C},null,8,["icon","size"])]}),_:1},8,["modelValue","size"])):_t("",!0),me(H(hn),{ref_key:"treeRef",ref:d,style:{width:"100%",height:"calc(100% - 50px)","overflow-y":"auto"},data:H(i).data,"node-key":"id","default-expand-all":"","expand-on-click-node":!1,props:H(i).defaultProps,"highlight-current":y.config.highlight??!0,"filter-node-method":E,lazy:y.config.lazy??!1,load:y.config.load,onNodeClick:A},{default:Le(({node:s,data:c})=>[_e("div",Fn,[_e("div",Bn,[s.data.icon??y.config.icon??""?(ue(),Qe(H(fn),{key:0,icon:s.data.icon??y.config.icon??"",style:{"font-size":"20px"}},null,8,["icon"])):_t("",!0),_e("span",null,hr(s.label),1)]),x.config.btns?(ue(),we("div",Un,[(ue(!0),we(Pr,null,Mr(x.config.btns,(n,f)=>(ue(),Qe(L,{key:f,type:n.type,link:"",size:n.size??"small",onClick:kr(h=>n.click&&n.click(c),["stop"])},{default:Le(()=>[cn(hr(n.text),1)]),_:2},1032,["type","size","onClick"]))),128))])):_t("",!0)])]),_:1},8,["data","props","highlight-current","lazy","load"])])}}}),Nn=Vt(jn,[["__scopeId","data-v-d7d06ff0"]]);var Ur={exports:{}};(function(e,d){(function(x,A){e.exports=A()})(self,function(){return function(){var i={"./node_modules/es6-promise/dist/es6-promise.js":function(E,y,m){/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   v4.2.8+1e68dce6
 */(function(L,_){E.exports=_()})(this,function(){function L(I){var k=typeof I;return I!==null&&(k==="object"||k==="function")}function _(I){return typeof I=="function"}var a=void 0;Array.isArray?a=Array.isArray:a=function(I){return Object.prototype.toString.call(I)==="[object Array]"};var s=a,c=0,n=void 0,f=void 0,h=function(k,B){R[c]=k,R[c+1]=B,c+=2,c===2&&(f?f(w):D())};function o(I){f=I}function r(I){h=I}var u=typeof window<"u"?window:void 0,l=u||{},t=l.MutationObserver||l.WebKitMutationObserver,p=typeof self>"u"&&typeof process<"u"&&{}.toString.call(process)==="[object process]",v=typeof Uint8ClampedArray<"u"&&typeof importScripts<"u"&&typeof MessageChannel<"u";function g(){return function(){return process.nextTick(w)}}function b(){return typeof n<"u"?function(){n(w)}:S()}function T(){var I=0,k=new t(w),B=document.createTextNode("");return k.observe(B,{characterData:!0}),function(){B.data=I=++I%2}}function O(){var I=new MessageChannel;return I.port1.onmessage=w,function(){return I.port2.postMessage(0)}}function S(){var I=setTimeout;return function(){return I(w,1)}}var R=new Array(1e3);function w(){for(var I=0;I<c;I+=2){var k=R[I],B=R[I+1];k(B),R[I]=void 0,R[I+1]=void 0}c=0}function P(){try{var I=Function("return this")().require("vertx");return n=I.runOnLoop||I.runOnContext,b()}catch{return S()}}var D=void 0;p?D=g():t?D=T():v?D=O():u===void 0?D=P():D=S();function M(I,k){var B=this,U=new this.constructor(j);U[z]===void 0&&et(U);var Y=B._state;if(Y){var $=arguments[Y-1];h(function(){return Xe(Y,U,$,B._result)})}else ye(B,U,I,k);return U}function G(I){var k=this;if(I&&typeof I=="object"&&I.constructor===k)return I;var B=new k(j);return Ee(B,I),B}var z=Math.random().toString(36).substring(2);function j(){}var Z=void 0,ee=1,W=2;function J(){return new TypeError("You cannot resolve a promise with itself")}function xe(){return new TypeError("A promises callback cannot return that same promise.")}function te(I,k,B,U){try{I.call(k,B,U)}catch(Y){return Y}}function re(I,k,B){h(function(U){var Y=!1,$=te(B,k,function(de){Y||(Y=!0,k!==de?Ee(U,de):ne(U,de))},function(de){Y||(Y=!0,K(U,de))},"Settle: "+(U._label||" unknown promise"));!Y&&$&&(Y=!0,K(U,$))},I)}function Re(I,k){k._state===ee?ne(I,k._result):k._state===W?K(I,k._result):ye(k,void 0,function(B){return Ee(I,B)},function(B){return K(I,B)})}function fe(I,k,B){k.constructor===I.constructor&&B===M&&k.constructor.resolve===G?Re(I,k):B===void 0?ne(I,k):_(B)?re(I,k,B):ne(I,k)}function Ee(I,k){if(I===k)K(I,J());else if(L(k)){var B=void 0;try{B=k.then}catch(U){K(I,U);return}fe(I,k,B)}else ne(I,k)}function Fe(I){I._onerror&&I._onerror(I._result),Ie(I)}function ne(I,k){I._state===Z&&(I._result=k,I._state=ee,I._subscribers.length!==0&&h(Ie,I))}function K(I,k){I._state===Z&&(I._state=W,I._result=k,h(Fe,I))}function ye(I,k,B,U){var Y=I._subscribers,$=Y.length;I._onerror=null,Y[$]=k,Y[$+ee]=B,Y[$+W]=U,$===0&&I._state&&h(Ie,I)}function Ie(I){var k=I._subscribers,B=I._state;if(k.length!==0){for(var U=void 0,Y=void 0,$=I._result,de=0;de<k.length;de+=3)U=k[de],Y=k[de+B],U?Xe(B,U,Y,$):Y($);I._subscribers.length=0}}function Xe(I,k,B,U){var Y=_(B),$=void 0,de=void 0,tt=!0;if(Y){try{$=B(U)}catch(Lt){tt=!1,de=Lt}if(k===$){K(k,xe());return}}else $=U;k._state!==Z||(Y&&tt?Ee(k,$):tt===!1?K(k,de):I===ee?ne(k,$):I===W&&K(k,$))}function Be(I,k){try{k(function(U){Ee(I,U)},function(U){K(I,U)})}catch(B){K(I,B)}}var We=0;function qe(){return We++}function et(I){I[z]=We++,I._state=void 0,I._result=void 0,I._subscribers=[]}function Pe(){return new Error("Array Methods must be provided an Array")}var Oe=function(){function I(k,B){this._instanceConstructor=k,this.promise=new k(j),this.promise[z]||et(this.promise),s(B)?(this.length=B.length,this._remaining=B.length,this._result=new Array(this.length),this.length===0?ne(this.promise,this._result):(this.length=this.length||0,this._enumerate(B),this._remaining===0&&ne(this.promise,this._result))):K(this.promise,Pe())}return I.prototype._enumerate=function(B){for(var U=0;this._state===Z&&U<B.length;U++)this._eachEntry(B[U],U)},I.prototype._eachEntry=function(B,U){var Y=this._instanceConstructor,$=Y.resolve;if($===G){var de=void 0,tt=void 0,Lt=!1;try{de=B.then}catch(Kt){Lt=!0,tt=Kt}if(de===M&&B._state!==Z)this._settledAt(B._state,U,B._result);else if(typeof de!="function")this._remaining--,this._result[U]=B;else if(Y===ve){var Gt=new Y(j);Lt?K(Gt,tt):fe(Gt,B,de),this._willSettleAt(Gt,U)}else this._willSettleAt(new Y(function(Kt){return Kt(B)}),U)}else this._willSettleAt($(B),U)},I.prototype._settledAt=function(B,U,Y){var $=this.promise;$._state===Z&&(this._remaining--,B===W?K($,Y):this._result[U]=Y),this._remaining===0&&ne($,this._result)},I.prototype._willSettleAt=function(B,U){var Y=this;ye(B,void 0,function($){return Y._settledAt(ee,U,$)},function($){return Y._settledAt(W,U,$)})},I}();function ut(I){return new Oe(this,I).promise}function St(I){var k=this;return s(I)?new k(function(B,U){for(var Y=I.length,$=0;$<Y;$++)k.resolve(I[$]).then(B,U)}):new k(function(B,U){return U(new TypeError("You must pass an array to race."))})}function sn(I){var k=this,B=new k(j);return K(B,I),B}function an(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function ln(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}var ve=function(){function I(k){this[z]=qe(),this._result=this._state=void 0,this._subscribers=[],j!==k&&(typeof k!="function"&&an(),this instanceof I?Be(this,k):ln())}return I.prototype.catch=function(B){return this.then(null,B)},I.prototype.finally=function(B){var U=this,Y=U.constructor;return _(B)?U.then(function($){return Y.resolve(B()).then(function(){return $})},function($){return Y.resolve(B()).then(function(){throw $})}):U.then(B,B)},I}();ve.prototype.then=M,ve.all=ut,ve.race=St,ve.resolve=G,ve.reject=sn,ve._setScheduler=o,ve._setAsap=r,ve._asap=h;function un(){var I=void 0;if(typeof m.g<"u")I=m.g;else if(typeof self<"u")I=self;else try{I=Function("return this")()}catch{throw new Error("polyfill failed because global object is unavailable in this environment")}var k=I.Promise;if(k){var B=null;try{B=Object.prototype.toString.call(k.resolve())}catch{}if(B==="[object Promise]"&&!k.cast)return}I.Promise=ve}return ve.polyfill=un,ve.Promise=ve,ve})},"./node_modules/events/events.js":function(E){var y=typeof Reflect=="object"?Reflect:null,m=y&&typeof y.apply=="function"?y.apply:function(S,R,w){return Function.prototype.apply.call(S,R,w)},L;y&&typeof y.ownKeys=="function"?L=y.ownKeys:Object.getOwnPropertySymbols?L=function(S){return Object.getOwnPropertyNames(S).concat(Object.getOwnPropertySymbols(S))}:L=function(S){return Object.getOwnPropertyNames(S)};function _(O){console&&console.warn&&console.warn(O)}var a=Number.isNaN||function(S){return S!==S};function s(){s.init.call(this)}E.exports=s,E.exports.once=g,s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var c=10;function n(O){if(typeof O!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof O)}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return c},set:function(O){if(typeof O!="number"||O<0||a(O))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+O+".");c=O}}),s.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(S){if(typeof S!="number"||S<0||a(S))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+S+".");return this._maxListeners=S,this};function f(O){return O._maxListeners===void 0?s.defaultMaxListeners:O._maxListeners}s.prototype.getMaxListeners=function(){return f(this)},s.prototype.emit=function(S){for(var R=[],w=1;w<arguments.length;w++)R.push(arguments[w]);var P=S==="error",D=this._events;if(D!==void 0)P=P&&D.error===void 0;else if(!P)return!1;if(P){var M;if(R.length>0&&(M=R[0]),M instanceof Error)throw M;var G=new Error("Unhandled error."+(M?" ("+M.message+")":""));throw G.context=M,G}var z=D[S];if(z===void 0)return!1;if(typeof z=="function")m(z,this,R);else for(var j=z.length,Z=t(z,j),w=0;w<j;++w)m(Z[w],this,R);return!0};function h(O,S,R,w){var P,D,M;if(n(R),D=O._events,D===void 0?(D=O._events=Object.create(null),O._eventsCount=0):(D.newListener!==void 0&&(O.emit("newListener",S,R.listener?R.listener:R),D=O._events),M=D[S]),M===void 0)M=D[S]=R,++O._eventsCount;else if(typeof M=="function"?M=D[S]=w?[R,M]:[M,R]:w?M.unshift(R):M.push(R),P=f(O),P>0&&M.length>P&&!M.warned){M.warned=!0;var G=new Error("Possible EventEmitter memory leak detected. "+M.length+" "+String(S)+" listeners added. Use emitter.setMaxListeners() to increase limit");G.name="MaxListenersExceededWarning",G.emitter=O,G.type=S,G.count=M.length,_(G)}return O}s.prototype.addListener=function(S,R){return h(this,S,R,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(S,R){return h(this,S,R,!0)};function o(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function r(O,S,R){var w={fired:!1,wrapFn:void 0,target:O,type:S,listener:R},P=o.bind(w);return P.listener=R,w.wrapFn=P,P}s.prototype.once=function(S,R){return n(R),this.on(S,r(this,S,R)),this},s.prototype.prependOnceListener=function(S,R){return n(R),this.prependListener(S,r(this,S,R)),this},s.prototype.removeListener=function(S,R){var w,P,D,M,G;if(n(R),P=this._events,P===void 0)return this;if(w=P[S],w===void 0)return this;if(w===R||w.listener===R)--this._eventsCount===0?this._events=Object.create(null):(delete P[S],P.removeListener&&this.emit("removeListener",S,w.listener||R));else if(typeof w!="function"){for(D=-1,M=w.length-1;M>=0;M--)if(w[M]===R||w[M].listener===R){G=w[M].listener,D=M;break}if(D<0)return this;D===0?w.shift():p(w,D),w.length===1&&(P[S]=w[0]),P.removeListener!==void 0&&this.emit("removeListener",S,G||R)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(S){var R,w,P;if(w=this._events,w===void 0)return this;if(w.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):w[S]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete w[S]),this;if(arguments.length===0){var D=Object.keys(w),M;for(P=0;P<D.length;++P)M=D[P],M!=="removeListener"&&this.removeAllListeners(M);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(R=w[S],typeof R=="function")this.removeListener(S,R);else if(R!==void 0)for(P=R.length-1;P>=0;P--)this.removeListener(S,R[P]);return this};function u(O,S,R){var w=O._events;if(w===void 0)return[];var P=w[S];return P===void 0?[]:typeof P=="function"?R?[P.listener||P]:[P]:R?v(P):t(P,P.length)}s.prototype.listeners=function(S){return u(this,S,!0)},s.prototype.rawListeners=function(S){return u(this,S,!1)},s.listenerCount=function(O,S){return typeof O.listenerCount=="function"?O.listenerCount(S):l.call(O,S)},s.prototype.listenerCount=l;function l(O){var S=this._events;if(S!==void 0){var R=S[O];if(typeof R=="function")return 1;if(R!==void 0)return R.length}return 0}s.prototype.eventNames=function(){return this._eventsCount>0?L(this._events):[]};function t(O,S){for(var R=new Array(S),w=0;w<S;++w)R[w]=O[w];return R}function p(O,S){for(;S+1<O.length;S++)O[S]=O[S+1];O.pop()}function v(O){for(var S=new Array(O.length),R=0;R<S.length;++R)S[R]=O[R].listener||O[R];return S}function g(O,S){return new Promise(function(R,w){function P(M){O.removeListener(S,D),w(M)}function D(){typeof O.removeListener=="function"&&O.removeListener("error",P),R([].slice.call(arguments))}T(O,S,D,{once:!0}),S!=="error"&&b(O,P,{once:!0})})}function b(O,S,R){typeof O.on=="function"&&T(O,"error",S,R)}function T(O,S,R,w){if(typeof O.on=="function")w.once?O.once(S,R):O.on(S,R);else if(typeof O.addEventListener=="function")O.addEventListener(S,function P(D){w.once&&O.removeEventListener(S,P),R(D)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof O)}},"./node_modules/webworkify-webpack/index.js":function(E,y,m){function L(o){var r={};function u(t){if(r[t])return r[t].exports;var p=r[t]={i:t,l:!1,exports:{}};return o[t].call(p.exports,p,p.exports,u),p.l=!0,p.exports}u.m=o,u.c=r,u.i=function(t){return t},u.d=function(t,p,v){u.o(t,p)||Object.defineProperty(t,p,{configurable:!1,enumerable:!0,get:v})},u.r=function(t){Object.defineProperty(t,"__esModule",{value:!0})},u.n=function(t){var p=t&&t.__esModule?function(){return t.default}:function(){return t};return u.d(p,"a",p),p},u.o=function(t,p){return Object.prototype.hasOwnProperty.call(t,p)},u.p="/",u.oe=function(t){throw console.error(t),t};var l=u(u.s=ENTRY_MODULE);return l.default||l}var _="[\\.|\\-|\\+|\\w|/|@]+",a="\\(\\s*(/\\*.*?\\*/)?\\s*.*?("+_+").*?\\)";function s(o){return(o+"").replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}function c(o){return!isNaN(1*o)}function n(o,r,u){var l={};l[u]=[];var t=r.toString(),p=t.match(/^function\s?\w*\(\w+,\s*\w+,\s*(\w+)\)/);if(!p)return l;for(var v=p[1],g=new RegExp("(\\\\n|\\W)"+s(v)+a,"g"),b;b=g.exec(t);)b[3]!=="dll-reference"&&l[u].push(b[3]);for(g=new RegExp("\\("+s(v)+'\\("(dll-reference\\s('+_+'))"\\)\\)'+a,"g");b=g.exec(t);)o[b[2]]||(l[u].push(b[1]),o[b[2]]=m(b[1]).m),l[b[2]]=l[b[2]]||[],l[b[2]].push(b[4]);for(var T=Object.keys(l),O=0;O<T.length;O++)for(var S=0;S<l[T[O]].length;S++)c(l[T[O]][S])&&(l[T[O]][S]=1*l[T[O]][S]);return l}function f(o){var r=Object.keys(o);return r.reduce(function(u,l){return u||o[l].length>0},!1)}function h(o,r){for(var u={main:[r]},l={main:[]},t={main:{}};f(u);)for(var p=Object.keys(u),v=0;v<p.length;v++){var g=p[v],b=u[g],T=b.pop();if(t[g]=t[g]||{},!(t[g][T]||!o[g][T])){t[g][T]=!0,l[g]=l[g]||[],l[g].push(T);for(var O=n(o,o[g][T],g),S=Object.keys(O),R=0;R<S.length;R++)u[S[R]]=u[S[R]]||[],u[S[R]]=u[S[R]].concat(O[S[R]])}}return l}E.exports=function(o,r){r=r||{};var u={main:m.m},l=r.all?{main:Object.keys(u.main)}:h(u,o),t="";Object.keys(l).filter(function(T){return T!=="main"}).forEach(function(T){for(var O=0;l[T][O];)O++;l[T].push(O),u[T][O]="(function(module, exports, __webpack_require__) { module.exports = __webpack_require__; })",t=t+"var "+T+" = ("+L.toString().replace("ENTRY_MODULE",JSON.stringify(O))+")({"+l[T].map(function(S){return""+JSON.stringify(S)+": "+u[T][S].toString()}).join(",")+`});
`}),t=t+"new (("+L.toString().replace("ENTRY_MODULE",JSON.stringify(o))+")({"+l.main.map(function(T){return""+JSON.stringify(T)+": "+u.main[T].toString()}).join(",")+"}))(self);";var p=new window.Blob([t],{type:"text/javascript"});if(r.bare)return p;var v=window.URL||window.webkitURL||window.mozURL||window.msURL,g=v.createObjectURL(p),b=new window.Worker(g);return b.objectURL=g,b}},"./src/config.js":function(E,y,m){m.r(y),m.d(y,{defaultConfig:function(){return L},createDefaultConfig:function(){return _}});var L={enableWorker:!1,enableStashBuffer:!0,stashInitialSize:void 0,isLive:!1,lazyLoad:!0,lazyLoadMaxDuration:3*60,lazyLoadRecoverDuration:30,deferLoadAfterSourceOpen:!0,autoCleanupMaxBackwardDuration:3*60,autoCleanupMinBackwardDuration:2*60,statisticsInfoReportInterval:600,fixAudioTimestampGap:!0,accurateSeek:!1,seekType:"range",seekParamStart:"bstart",seekParamEnd:"bend",rangeLoadZeroStart:!1,customSeekHandler:void 0,reuseRedirectedURL:!1,headers:void 0,customLoader:void 0};function _(){return Object.assign({},L)}},"./src/core/features.js":function(E,y,m){m.r(y);var L=m("./src/io/io-controller.js"),_=m("./src/config.js"),a=function(){function s(){}return s.supportMSEH264Playback=function(){return window.MediaSource&&window.MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"')},s.supportNetworkStreamIO=function(){var c=new L.default({},(0,_.createDefaultConfig)()),n=c.loaderType;return c.destroy(),n=="fetch-stream-loader"||n=="xhr-moz-chunked-loader"},s.getNetworkLoaderTypeName=function(){var c=new L.default({},(0,_.createDefaultConfig)()),n=c.loaderType;return c.destroy(),n},s.supportNativeMediaPlayback=function(c){s.videoElement==null&&(s.videoElement=window.document.createElement("video"));var n=s.videoElement.canPlayType(c);return n==="probably"||n=="maybe"},s.getFeatureList=function(){var c={mseFlvPlayback:!1,mseLiveFlvPlayback:!1,networkStreamIO:!1,networkLoaderName:"",nativeMP4H264Playback:!1,nativeWebmVP8Playback:!1,nativeWebmVP9Playback:!1};return c.mseFlvPlayback=s.supportMSEH264Playback(),c.networkStreamIO=s.supportNetworkStreamIO(),c.networkLoaderName=s.getNetworkLoaderTypeName(),c.mseLiveFlvPlayback=c.mseFlvPlayback&&c.networkStreamIO,c.nativeMP4H264Playback=s.supportNativeMediaPlayback('video/mp4; codecs="avc1.42001E, mp4a.40.2"'),c.nativeWebmVP8Playback=s.supportNativeMediaPlayback('video/webm; codecs="vp8.0, vorbis"'),c.nativeWebmVP9Playback=s.supportNativeMediaPlayback('video/webm; codecs="vp9"'),c},s}();y.default=a},"./src/core/media-info.js":function(E,y,m){m.r(y);var L=function(){function _(){this.mimeType=null,this.duration=null,this.hasAudio=null,this.hasVideo=null,this.audioCodec=null,this.videoCodec=null,this.audioDataRate=null,this.videoDataRate=null,this.audioSampleRate=null,this.audioChannelCount=null,this.width=null,this.height=null,this.fps=null,this.profile=null,this.level=null,this.refFrames=null,this.chromaFormat=null,this.sarNum=null,this.sarDen=null,this.metadata=null,this.segments=null,this.segmentCount=null,this.hasKeyframesIndex=null,this.keyframesIndex=null}return _.prototype.isComplete=function(){var a=this.hasAudio===!1||this.hasAudio===!0&&this.audioCodec!=null&&this.audioSampleRate!=null&&this.audioChannelCount!=null,s=this.hasVideo===!1||this.hasVideo===!0&&this.videoCodec!=null&&this.width!=null&&this.height!=null&&this.fps!=null&&this.profile!=null&&this.level!=null&&this.refFrames!=null&&this.chromaFormat!=null&&this.sarNum!=null&&this.sarDen!=null;return this.mimeType!=null&&this.duration!=null&&this.metadata!=null&&this.hasKeyframesIndex!=null&&a&&s},_.prototype.isSeekable=function(){return this.hasKeyframesIndex===!0},_.prototype.getNearestKeyframe=function(a){if(this.keyframesIndex==null)return null;var s=this.keyframesIndex,c=this._search(s.times,a);return{index:c,milliseconds:s.times[c],fileposition:s.filepositions[c]}},_.prototype._search=function(a,s){var c=0,n=a.length-1,f=0,h=0,o=n;for(s<a[0]&&(c=0,h=o+1);h<=o;)if(f=h+Math.floor((o-h)/2),f===n||s>=a[f]&&s<a[f+1]){c=f;break}else a[f]<s?h=f+1:o=f-1;return c},_}();y.default=L},"./src/core/media-segment-info.js":function(E,y,m){m.r(y),m.d(y,{SampleInfo:function(){return L},MediaSegmentInfo:function(){return _},IDRSampleList:function(){return a},MediaSegmentInfoList:function(){return s}});var L=function(){function c(n,f,h,o,r){this.dts=n,this.pts=f,this.duration=h,this.originalDts=o,this.isSyncPoint=r,this.fileposition=null}return c}(),_=function(){function c(){this.beginDts=0,this.endDts=0,this.beginPts=0,this.endPts=0,this.originalBeginDts=0,this.originalEndDts=0,this.syncPoints=[],this.firstSample=null,this.lastSample=null}return c.prototype.appendSyncPoint=function(n){n.isSyncPoint=!0,this.syncPoints.push(n)},c}(),a=function(){function c(){this._list=[]}return c.prototype.clear=function(){this._list=[]},c.prototype.appendArray=function(n){var f=this._list;n.length!==0&&(f.length>0&&n[0].originalDts<f[f.length-1].originalDts&&this.clear(),Array.prototype.push.apply(f,n))},c.prototype.getLastSyncPointBeforeDts=function(n){if(this._list.length==0)return null;var f=this._list,h=0,o=f.length-1,r=0,u=0,l=o;for(n<f[0].dts&&(h=0,u=l+1);u<=l;)if(r=u+Math.floor((l-u)/2),r===o||n>=f[r].dts&&n<f[r+1].dts){h=r;break}else f[r].dts<n?u=r+1:l=r-1;return this._list[h]},c}(),s=function(){function c(n){this._type=n,this._list=[],this._lastAppendLocation=-1}return Object.defineProperty(c.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"length",{get:function(){return this._list.length},enumerable:!1,configurable:!0}),c.prototype.isEmpty=function(){return this._list.length===0},c.prototype.clear=function(){this._list=[],this._lastAppendLocation=-1},c.prototype._searchNearestSegmentBefore=function(n){var f=this._list;if(f.length===0)return-2;var h=f.length-1,o=0,r=0,u=h,l=0;if(n<f[0].originalBeginDts)return l=-1,l;for(;r<=u;)if(o=r+Math.floor((u-r)/2),o===h||n>f[o].lastSample.originalDts&&n<f[o+1].originalBeginDts){l=o;break}else f[o].originalBeginDts<n?r=o+1:u=o-1;return l},c.prototype._searchNearestSegmentAfter=function(n){return this._searchNearestSegmentBefore(n)+1},c.prototype.append=function(n){var f=this._list,h=n,o=this._lastAppendLocation,r=0;o!==-1&&o<f.length&&h.originalBeginDts>=f[o].lastSample.originalDts&&(o===f.length-1||o<f.length-1&&h.originalBeginDts<f[o+1].originalBeginDts)?r=o+1:f.length>0&&(r=this._searchNearestSegmentBefore(h.originalBeginDts)+1),this._lastAppendLocation=r,this._list.splice(r,0,h)},c.prototype.getLastSegmentBefore=function(n){var f=this._searchNearestSegmentBefore(n);return f>=0?this._list[f]:null},c.prototype.getLastSampleBefore=function(n){var f=this.getLastSegmentBefore(n);return f!=null?f.lastSample:null},c.prototype.getLastSyncPointBefore=function(n){for(var f=this._searchNearestSegmentBefore(n),h=this._list[f].syncPoints;h.length===0&&f>0;)f--,h=this._list[f].syncPoints;return h.length>0?h[h.length-1]:null},c}()},"./src/core/mse-controller.js":function(E,y,m){m.r(y);var L=m("./node_modules/events/events.js"),_=m.n(L),a=m("./src/utils/logger.js"),s=m("./src/utils/browser.js"),c=m("./src/core/mse-events.js"),n=m("./src/core/media-segment-info.js"),f=m("./src/utils/exception.js"),h=function(){function o(r){this.TAG="MSEController",this._config=r,this._emitter=new(_()),this._config.isLive&&this._config.autoCleanupSourceBuffer==null&&(this._config.autoCleanupSourceBuffer=!0),this.e={onSourceOpen:this._onSourceOpen.bind(this),onSourceEnded:this._onSourceEnded.bind(this),onSourceClose:this._onSourceClose.bind(this),onSourceBufferError:this._onSourceBufferError.bind(this),onSourceBufferUpdateEnd:this._onSourceBufferUpdateEnd.bind(this)},this._mediaSource=null,this._mediaSourceObjectURL=null,this._mediaElement=null,this._isBufferFull=!1,this._hasPendingEos=!1,this._requireSetMediaDuration=!1,this._pendingMediaDuration=0,this._pendingSourceBufferInit=[],this._mimeTypes={video:null,audio:null},this._sourceBuffers={video:null,audio:null},this._lastInitSegments={video:null,audio:null},this._pendingSegments={video:[],audio:[]},this._pendingRemoveRanges={video:[],audio:[]},this._idrList=new n.IDRSampleList}return o.prototype.destroy=function(){(this._mediaElement||this._mediaSource)&&this.detachMediaElement(),this.e=null,this._emitter.removeAllListeners(),this._emitter=null},o.prototype.on=function(r,u){this._emitter.addListener(r,u)},o.prototype.off=function(r,u){this._emitter.removeListener(r,u)},o.prototype.attachMediaElement=function(r){if(this._mediaSource)throw new f.IllegalStateException("MediaSource has been attached to an HTMLMediaElement!");var u=this._mediaSource=new window.MediaSource;u.addEventListener("sourceopen",this.e.onSourceOpen),u.addEventListener("sourceended",this.e.onSourceEnded),u.addEventListener("sourceclose",this.e.onSourceClose),this._mediaElement=r,this._mediaSourceObjectURL=window.URL.createObjectURL(this._mediaSource),r.src=this._mediaSourceObjectURL},o.prototype.detachMediaElement=function(){if(this._mediaSource){var r=this._mediaSource;for(var u in this._sourceBuffers){var l=this._pendingSegments[u];l.splice(0,l.length),this._pendingSegments[u]=null,this._pendingRemoveRanges[u]=null,this._lastInitSegments[u]=null;var t=this._sourceBuffers[u];if(t){if(r.readyState!=="closed"){try{r.removeSourceBuffer(t)}catch(p){a.default.e(this.TAG,p.message)}t.removeEventListener("error",this.e.onSourceBufferError),t.removeEventListener("updateend",this.e.onSourceBufferUpdateEnd)}this._mimeTypes[u]=null,this._sourceBuffers[u]=null}}if(r.readyState==="open")try{r.endOfStream()}catch(p){a.default.e(this.TAG,p.message)}r.removeEventListener("sourceopen",this.e.onSourceOpen),r.removeEventListener("sourceended",this.e.onSourceEnded),r.removeEventListener("sourceclose",this.e.onSourceClose),this._pendingSourceBufferInit=[],this._isBufferFull=!1,this._idrList.clear(),this._mediaSource=null}this._mediaElement&&(this._mediaElement.src="",this._mediaElement.removeAttribute("src"),this._mediaElement=null),this._mediaSourceObjectURL&&(window.URL.revokeObjectURL(this._mediaSourceObjectURL),this._mediaSourceObjectURL=null)},o.prototype.appendInitSegment=function(r,u){if(!this._mediaSource||this._mediaSource.readyState!=="open"){this._pendingSourceBufferInit.push(r),this._pendingSegments[r.type].push(r);return}var l=r,t=""+l.container;l.codec&&l.codec.length>0&&(t+=";codecs="+l.codec);var p=!1;if(a.default.v(this.TAG,"Received Initialization Segment, mimeType: "+t),this._lastInitSegments[l.type]=l,t!==this._mimeTypes[l.type]){if(this._mimeTypes[l.type])a.default.v(this.TAG,"Notice: "+l.type+" mimeType changed, origin: "+this._mimeTypes[l.type]+", target: "+t);else{p=!0;try{var v=this._sourceBuffers[l.type]=this._mediaSource.addSourceBuffer(t);v.addEventListener("error",this.e.onSourceBufferError),v.addEventListener("updateend",this.e.onSourceBufferUpdateEnd)}catch(g){a.default.e(this.TAG,g.message),this._emitter.emit(c.default.ERROR,{code:g.code,msg:g.message});return}}this._mimeTypes[l.type]=t}u||this._pendingSegments[l.type].push(l),p||this._sourceBuffers[l.type]&&!this._sourceBuffers[l.type].updating&&this._doAppendSegments(),s.default.safari&&l.container==="audio/mpeg"&&l.mediaDuration>0&&(this._requireSetMediaDuration=!0,this._pendingMediaDuration=l.mediaDuration/1e3,this._updateMediaSourceDuration())},o.prototype.appendMediaSegment=function(r){var u=r;this._pendingSegments[u.type].push(u),this._config.autoCleanupSourceBuffer&&this._needCleanupSourceBuffer()&&this._doCleanupSourceBuffer();var l=this._sourceBuffers[u.type];l&&!l.updating&&!this._hasPendingRemoveRanges()&&this._doAppendSegments()},o.prototype.seek=function(r){for(var u in this._sourceBuffers)if(this._sourceBuffers[u]){var l=this._sourceBuffers[u];if(this._mediaSource.readyState==="open")try{l.abort()}catch(T){a.default.e(this.TAG,T.message)}this._idrList.clear();var t=this._pendingSegments[u];if(t.splice(0,t.length),this._mediaSource.readyState!=="closed"){for(var p=0;p<l.buffered.length;p++){var v=l.buffered.start(p),g=l.buffered.end(p);this._pendingRemoveRanges[u].push({start:v,end:g})}if(l.updating||this._doRemoveRanges(),s.default.safari){var b=this._lastInitSegments[u];b&&(this._pendingSegments[u].push(b),l.updating||this._doAppendSegments())}}}},o.prototype.endOfStream=function(){var r=this._mediaSource,u=this._sourceBuffers;if(!r||r.readyState!=="open"){r&&r.readyState==="closed"&&this._hasPendingSegments()&&(this._hasPendingEos=!0);return}u.video&&u.video.updating||u.audio&&u.audio.updating?this._hasPendingEos=!0:(this._hasPendingEos=!1,r.endOfStream())},o.prototype.getNearestKeyframe=function(r){return this._idrList.getLastSyncPointBeforeDts(r)},o.prototype._needCleanupSourceBuffer=function(){if(!this._config.autoCleanupSourceBuffer)return!1;var r=this._mediaElement.currentTime;for(var u in this._sourceBuffers){var l=this._sourceBuffers[u];if(l){var t=l.buffered;if(t.length>=1&&r-t.start(0)>=this._config.autoCleanupMaxBackwardDuration)return!0}}return!1},o.prototype._doCleanupSourceBuffer=function(){var r=this._mediaElement.currentTime;for(var u in this._sourceBuffers){var l=this._sourceBuffers[u];if(l){for(var t=l.buffered,p=!1,v=0;v<t.length;v++){var g=t.start(v),b=t.end(v);if(g<=r&&r<b+3){if(r-g>=this._config.autoCleanupMaxBackwardDuration){p=!0;var T=r-this._config.autoCleanupMinBackwardDuration;this._pendingRemoveRanges[u].push({start:g,end:T})}}else b<r&&(p=!0,this._pendingRemoveRanges[u].push({start:g,end:b}))}p&&!l.updating&&this._doRemoveRanges()}}},o.prototype._updateMediaSourceDuration=function(){var r=this._sourceBuffers;if(!(this._mediaElement.readyState===0||this._mediaSource.readyState!=="open")&&!(r.video&&r.video.updating||r.audio&&r.audio.updating)){var u=this._mediaSource.duration,l=this._pendingMediaDuration;l>0&&(isNaN(u)||l>u)&&(a.default.v(this.TAG,"Update MediaSource duration from "+u+" to "+l),this._mediaSource.duration=l),this._requireSetMediaDuration=!1,this._pendingMediaDuration=0}},o.prototype._doRemoveRanges=function(){for(var r in this._pendingRemoveRanges)if(!(!this._sourceBuffers[r]||this._sourceBuffers[r].updating))for(var u=this._sourceBuffers[r],l=this._pendingRemoveRanges[r];l.length&&!u.updating;){var t=l.shift();u.remove(t.start,t.end)}},o.prototype._doAppendSegments=function(){var r=this._pendingSegments;for(var u in r)if(!(!this._sourceBuffers[u]||this._sourceBuffers[u].updating)&&r[u].length>0){var l=r[u].shift();if(l.timestampOffset){var t=this._sourceBuffers[u].timestampOffset,p=l.timestampOffset/1e3,v=Math.abs(t-p);v>.1&&(a.default.v(this.TAG,"Update MPEG audio timestampOffset from "+t+" to "+p),this._sourceBuffers[u].timestampOffset=p),delete l.timestampOffset}if(!l.data||l.data.byteLength===0)continue;try{this._sourceBuffers[u].appendBuffer(l.data),this._isBufferFull=!1,u==="video"&&l.hasOwnProperty("info")&&this._idrList.appendArray(l.info.syncPoints)}catch(g){this._pendingSegments[u].unshift(l),g.code===22?(this._isBufferFull||this._emitter.emit(c.default.BUFFER_FULL),this._isBufferFull=!0):(a.default.e(this.TAG,g.message),this._emitter.emit(c.default.ERROR,{code:g.code,msg:g.message}))}}},o.prototype._onSourceOpen=function(){if(a.default.v(this.TAG,"MediaSource onSourceOpen"),this._mediaSource.removeEventListener("sourceopen",this.e.onSourceOpen),this._pendingSourceBufferInit.length>0)for(var r=this._pendingSourceBufferInit;r.length;){var u=r.shift();this.appendInitSegment(u,!0)}this._hasPendingSegments()&&this._doAppendSegments(),this._emitter.emit(c.default.SOURCE_OPEN)},o.prototype._onSourceEnded=function(){a.default.v(this.TAG,"MediaSource onSourceEnded")},o.prototype._onSourceClose=function(){a.default.v(this.TAG,"MediaSource onSourceClose"),this._mediaSource&&this.e!=null&&(this._mediaSource.removeEventListener("sourceopen",this.e.onSourceOpen),this._mediaSource.removeEventListener("sourceended",this.e.onSourceEnded),this._mediaSource.removeEventListener("sourceclose",this.e.onSourceClose))},o.prototype._hasPendingSegments=function(){var r=this._pendingSegments;return r.video.length>0||r.audio.length>0},o.prototype._hasPendingRemoveRanges=function(){var r=this._pendingRemoveRanges;return r.video.length>0||r.audio.length>0},o.prototype._onSourceBufferUpdateEnd=function(){this._requireSetMediaDuration?this._updateMediaSourceDuration():this._hasPendingRemoveRanges()?this._doRemoveRanges():this._hasPendingSegments()?this._doAppendSegments():this._hasPendingEos&&this.endOfStream(),this._emitter.emit(c.default.UPDATE_END)},o.prototype._onSourceBufferError=function(r){a.default.e(this.TAG,"SourceBuffer Error: "+r)},o}();y.default=h},"./src/core/mse-events.js":function(E,y,m){m.r(y);var L={ERROR:"error",SOURCE_OPEN:"source_open",UPDATE_END:"update_end",BUFFER_FULL:"buffer_full"};y.default=L},"./src/core/transmuxer.js":function(E,y,m){m.r(y);var L=m("./node_modules/events/events.js"),_=m.n(L),a=m("./node_modules/webworkify-webpack/index.js"),s=m.n(a),c=m("./src/utils/logger.js"),n=m("./src/utils/logging-control.js"),f=m("./src/core/transmuxing-controller.js"),h=m("./src/core/transmuxing-events.js"),o=m("./src/core/media-info.js"),r=function(){function u(l,t){if(this.TAG="Transmuxer",this._emitter=new(_()),t.enableWorker&&typeof Worker<"u")try{this._worker=s()("./src/core/transmuxing-worker.js"),this._workerDestroying=!1,this._worker.addEventListener("message",this._onWorkerMessage.bind(this)),this._worker.postMessage({cmd:"init",param:[l,t]}),this.e={onLoggingConfigChanged:this._onLoggingConfigChanged.bind(this)},n.default.registerListener(this.e.onLoggingConfigChanged),this._worker.postMessage({cmd:"logging_config",param:n.default.getConfig()})}catch{c.default.e(this.TAG,"Error while initialize transmuxing worker, fallback to inline transmuxing"),this._worker=null,this._controller=new f.default(l,t)}else this._controller=new f.default(l,t);if(this._controller){var p=this._controller;p.on(h.default.IO_ERROR,this._onIOError.bind(this)),p.on(h.default.DEMUX_ERROR,this._onDemuxError.bind(this)),p.on(h.default.INIT_SEGMENT,this._onInitSegment.bind(this)),p.on(h.default.MEDIA_SEGMENT,this._onMediaSegment.bind(this)),p.on(h.default.LOADING_COMPLETE,this._onLoadingComplete.bind(this)),p.on(h.default.RECOVERED_EARLY_EOF,this._onRecoveredEarlyEof.bind(this)),p.on(h.default.MEDIA_INFO,this._onMediaInfo.bind(this)),p.on(h.default.METADATA_ARRIVED,this._onMetaDataArrived.bind(this)),p.on(h.default.SCRIPTDATA_ARRIVED,this._onScriptDataArrived.bind(this)),p.on(h.default.STATISTICS_INFO,this._onStatisticsInfo.bind(this)),p.on(h.default.RECOMMEND_SEEKPOINT,this._onRecommendSeekpoint.bind(this))}}return u.prototype.destroy=function(){this._worker?this._workerDestroying||(this._workerDestroying=!0,this._worker.postMessage({cmd:"destroy"}),n.default.removeListener(this.e.onLoggingConfigChanged),this.e=null):(this._controller.destroy(),this._controller=null),this._emitter.removeAllListeners(),this._emitter=null},u.prototype.on=function(l,t){this._emitter.addListener(l,t)},u.prototype.off=function(l,t){this._emitter.removeListener(l,t)},u.prototype.hasWorker=function(){return this._worker!=null},u.prototype.open=function(){this._worker?this._worker.postMessage({cmd:"start"}):this._controller.start()},u.prototype.close=function(){this._worker?this._worker.postMessage({cmd:"stop"}):this._controller.stop()},u.prototype.seek=function(l){this._worker?this._worker.postMessage({cmd:"seek",param:l}):this._controller.seek(l)},u.prototype.pause=function(){this._worker?this._worker.postMessage({cmd:"pause"}):this._controller.pause()},u.prototype.resume=function(){this._worker?this._worker.postMessage({cmd:"resume"}):this._controller.resume()},u.prototype._onInitSegment=function(l,t){var p=this;Promise.resolve().then(function(){p._emitter.emit(h.default.INIT_SEGMENT,l,t)})},u.prototype._onMediaSegment=function(l,t){var p=this;Promise.resolve().then(function(){p._emitter.emit(h.default.MEDIA_SEGMENT,l,t)})},u.prototype._onLoadingComplete=function(){var l=this;Promise.resolve().then(function(){l._emitter.emit(h.default.LOADING_COMPLETE)})},u.prototype._onRecoveredEarlyEof=function(){var l=this;Promise.resolve().then(function(){l._emitter.emit(h.default.RECOVERED_EARLY_EOF)})},u.prototype._onMediaInfo=function(l){var t=this;Promise.resolve().then(function(){t._emitter.emit(h.default.MEDIA_INFO,l)})},u.prototype._onMetaDataArrived=function(l){var t=this;Promise.resolve().then(function(){t._emitter.emit(h.default.METADATA_ARRIVED,l)})},u.prototype._onScriptDataArrived=function(l){var t=this;Promise.resolve().then(function(){t._emitter.emit(h.default.SCRIPTDATA_ARRIVED,l)})},u.prototype._onStatisticsInfo=function(l){var t=this;Promise.resolve().then(function(){t._emitter.emit(h.default.STATISTICS_INFO,l)})},u.prototype._onIOError=function(l,t){var p=this;Promise.resolve().then(function(){p._emitter.emit(h.default.IO_ERROR,l,t)})},u.prototype._onDemuxError=function(l,t){var p=this;Promise.resolve().then(function(){p._emitter.emit(h.default.DEMUX_ERROR,l,t)})},u.prototype._onRecommendSeekpoint=function(l){var t=this;Promise.resolve().then(function(){t._emitter.emit(h.default.RECOMMEND_SEEKPOINT,l)})},u.prototype._onLoggingConfigChanged=function(l){this._worker&&this._worker.postMessage({cmd:"logging_config",param:l})},u.prototype._onWorkerMessage=function(l){var t=l.data,p=t.data;if(t.msg==="destroyed"||this._workerDestroying){this._workerDestroying=!1,this._worker.terminate(),this._worker=null;return}switch(t.msg){case h.default.INIT_SEGMENT:case h.default.MEDIA_SEGMENT:this._emitter.emit(t.msg,p.type,p.data);break;case h.default.LOADING_COMPLETE:case h.default.RECOVERED_EARLY_EOF:this._emitter.emit(t.msg);break;case h.default.MEDIA_INFO:Object.setPrototypeOf(p,o.default.prototype),this._emitter.emit(t.msg,p);break;case h.default.METADATA_ARRIVED:case h.default.SCRIPTDATA_ARRIVED:case h.default.STATISTICS_INFO:this._emitter.emit(t.msg,p);break;case h.default.IO_ERROR:case h.default.DEMUX_ERROR:this._emitter.emit(t.msg,p.type,p.info);break;case h.default.RECOMMEND_SEEKPOINT:this._emitter.emit(t.msg,p);break;case"logcat_callback":c.default.emitter.emit("log",p.type,p.logcat);break}},u}();y.default=r},"./src/core/transmuxing-controller.js":function(E,y,m){m.r(y);var L=m("./node_modules/events/events.js"),_=m.n(L),a=m("./src/utils/logger.js"),s=m("./src/utils/browser.js"),c=m("./src/core/media-info.js"),n=m("./src/demux/flv-demuxer.js"),f=m("./src/remux/mp4-remuxer.js"),h=m("./src/demux/demux-errors.js"),o=m("./src/io/io-controller.js"),r=m("./src/core/transmuxing-events.js"),u=function(){function l(t,p){this.TAG="TransmuxingController",this._emitter=new(_()),this._config=p,t.segments||(t.segments=[{duration:t.duration,filesize:t.filesize,url:t.url}]),typeof t.cors!="boolean"&&(t.cors=!0),typeof t.withCredentials!="boolean"&&(t.withCredentials=!1),this._mediaDataSource=t,this._currentSegmentIndex=0;var v=0;this._mediaDataSource.segments.forEach(function(g){g.timestampBase=v,v+=g.duration,g.cors=t.cors,g.withCredentials=t.withCredentials,p.referrerPolicy&&(g.referrerPolicy=p.referrerPolicy)}),!isNaN(v)&&this._mediaDataSource.duration!==v&&(this._mediaDataSource.duration=v),this._mediaInfo=null,this._demuxer=null,this._remuxer=null,this._ioctl=null,this._pendingSeekTime=null,this._pendingResolveSeekPoint=null,this._statisticsReporter=null}return l.prototype.destroy=function(){this._mediaInfo=null,this._mediaDataSource=null,this._statisticsReporter&&this._disableStatisticsReporter(),this._ioctl&&(this._ioctl.destroy(),this._ioctl=null),this._demuxer&&(this._demuxer.destroy(),this._demuxer=null),this._remuxer&&(this._remuxer.destroy(),this._remuxer=null),this._emitter.removeAllListeners(),this._emitter=null},l.prototype.on=function(t,p){this._emitter.addListener(t,p)},l.prototype.off=function(t,p){this._emitter.removeListener(t,p)},l.prototype.start=function(){this._loadSegment(0),this._enableStatisticsReporter()},l.prototype._loadSegment=function(t,p){this._currentSegmentIndex=t;var v=this._mediaDataSource.segments[t],g=this._ioctl=new o.default(v,this._config,t);g.onError=this._onIOException.bind(this),g.onSeeked=this._onIOSeeked.bind(this),g.onComplete=this._onIOComplete.bind(this),g.onRedirect=this._onIORedirect.bind(this),g.onRecoveredEarlyEof=this._onIORecoveredEarlyEof.bind(this),p?this._demuxer.bindDataSource(this._ioctl):g.onDataArrival=this._onInitChunkArrival.bind(this),g.open(p)},l.prototype.stop=function(){this._internalAbort(),this._disableStatisticsReporter()},l.prototype._internalAbort=function(){this._ioctl&&(this._ioctl.destroy(),this._ioctl=null)},l.prototype.pause=function(){this._ioctl&&this._ioctl.isWorking()&&(this._ioctl.pause(),this._disableStatisticsReporter())},l.prototype.resume=function(){this._ioctl&&this._ioctl.isPaused()&&(this._ioctl.resume(),this._enableStatisticsReporter())},l.prototype.seek=function(t){if(!(this._mediaInfo==null||!this._mediaInfo.isSeekable())){var p=this._searchSegmentIndexContains(t);if(p===this._currentSegmentIndex){var v=this._mediaInfo.segments[p];if(v==null)this._pendingSeekTime=t;else{var g=v.getNearestKeyframe(t);this._remuxer.seek(g.milliseconds),this._ioctl.seek(g.fileposition),this._pendingResolveSeekPoint=g.milliseconds}}else{var b=this._mediaInfo.segments[p];if(b==null)this._pendingSeekTime=t,this._internalAbort(),this._remuxer.seek(),this._remuxer.insertDiscontinuity(),this._loadSegment(p);else{var g=b.getNearestKeyframe(t);this._internalAbort(),this._remuxer.seek(t),this._remuxer.insertDiscontinuity(),this._demuxer.resetMediaInfo(),this._demuxer.timestampBase=this._mediaDataSource.segments[p].timestampBase,this._loadSegment(p,g.fileposition),this._pendingResolveSeekPoint=g.milliseconds,this._reportSegmentMediaInfo(p)}}this._enableStatisticsReporter()}},l.prototype._searchSegmentIndexContains=function(t){for(var p=this._mediaDataSource.segments,v=p.length-1,g=0;g<p.length;g++)if(t<p[g].timestampBase){v=g-1;break}return v},l.prototype._onInitChunkArrival=function(t,p){var v=this,g=null,b=0;if(p>0)this._demuxer.bindDataSource(this._ioctl),this._demuxer.timestampBase=this._mediaDataSource.segments[this._currentSegmentIndex].timestampBase,b=this._demuxer.parseChunks(t,p);else if((g=n.default.probe(t)).match){this._demuxer=new n.default(g,this._config),this._remuxer||(this._remuxer=new f.default(this._config));var T=this._mediaDataSource;T.duration!=null&&!isNaN(T.duration)&&(this._demuxer.overridedDuration=T.duration),typeof T.hasAudio=="boolean"&&(this._demuxer.overridedHasAudio=T.hasAudio),typeof T.hasVideo=="boolean"&&(this._demuxer.overridedHasVideo=T.hasVideo),this._demuxer.timestampBase=T.segments[this._currentSegmentIndex].timestampBase,this._demuxer.onError=this._onDemuxException.bind(this),this._demuxer.onMediaInfo=this._onMediaInfo.bind(this),this._demuxer.onMetaDataArrived=this._onMetaDataArrived.bind(this),this._demuxer.onScriptDataArrived=this._onScriptDataArrived.bind(this),this._remuxer.bindDataSource(this._demuxer.bindDataSource(this._ioctl)),this._remuxer.onInitSegment=this._onRemuxerInitSegmentArrival.bind(this),this._remuxer.onMediaSegment=this._onRemuxerMediaSegmentArrival.bind(this),b=this._demuxer.parseChunks(t,p)}else g=null,a.default.e(this.TAG,"Non-FLV, Unsupported media type!"),Promise.resolve().then(function(){v._internalAbort()}),this._emitter.emit(r.default.DEMUX_ERROR,h.default.FORMAT_UNSUPPORTED,"Non-FLV, Unsupported media type"),b=0;return b},l.prototype._onMediaInfo=function(t){var p=this;this._mediaInfo==null&&(this._mediaInfo=Object.assign({},t),this._mediaInfo.keyframesIndex=null,this._mediaInfo.segments=[],this._mediaInfo.segmentCount=this._mediaDataSource.segments.length,Object.setPrototypeOf(this._mediaInfo,c.default.prototype));var v=Object.assign({},t);Object.setPrototypeOf(v,c.default.prototype),this._mediaInfo.segments[this._currentSegmentIndex]=v,this._reportSegmentMediaInfo(this._currentSegmentIndex),this._pendingSeekTime!=null&&Promise.resolve().then(function(){var g=p._pendingSeekTime;p._pendingSeekTime=null,p.seek(g)})},l.prototype._onMetaDataArrived=function(t){this._emitter.emit(r.default.METADATA_ARRIVED,t)},l.prototype._onScriptDataArrived=function(t){this._emitter.emit(r.default.SCRIPTDATA_ARRIVED,t)},l.prototype._onIOSeeked=function(){this._remuxer.insertDiscontinuity()},l.prototype._onIOComplete=function(t){var p=t,v=p+1;v<this._mediaDataSource.segments.length?(this._internalAbort(),this._remuxer.flushStashedSamples(),this._loadSegment(v)):(this._remuxer.flushStashedSamples(),this._emitter.emit(r.default.LOADING_COMPLETE),this._disableStatisticsReporter())},l.prototype._onIORedirect=function(t){var p=this._ioctl.extraData;this._mediaDataSource.segments[p].redirectedURL=t},l.prototype._onIORecoveredEarlyEof=function(){this._emitter.emit(r.default.RECOVERED_EARLY_EOF)},l.prototype._onIOException=function(t,p){a.default.e(this.TAG,"IOException: type = "+t+", code = "+p.code+", msg = "+p.msg),this._emitter.emit(r.default.IO_ERROR,t,p),this._disableStatisticsReporter()},l.prototype._onDemuxException=function(t,p){a.default.e(this.TAG,"DemuxException: type = "+t+", info = "+p),this._emitter.emit(r.default.DEMUX_ERROR,t,p)},l.prototype._onRemuxerInitSegmentArrival=function(t,p){this._emitter.emit(r.default.INIT_SEGMENT,t,p)},l.prototype._onRemuxerMediaSegmentArrival=function(t,p){if(this._pendingSeekTime==null&&(this._emitter.emit(r.default.MEDIA_SEGMENT,t,p),this._pendingResolveSeekPoint!=null&&t==="video")){var v=p.info.syncPoints,g=this._pendingResolveSeekPoint;this._pendingResolveSeekPoint=null,s.default.safari&&v.length>0&&v[0].originalDts===g&&(g=v[0].pts),this._emitter.emit(r.default.RECOMMEND_SEEKPOINT,g)}},l.prototype._enableStatisticsReporter=function(){this._statisticsReporter==null&&(this._statisticsReporter=self.setInterval(this._reportStatisticsInfo.bind(this),this._config.statisticsInfoReportInterval))},l.prototype._disableStatisticsReporter=function(){this._statisticsReporter&&(self.clearInterval(this._statisticsReporter),this._statisticsReporter=null)},l.prototype._reportSegmentMediaInfo=function(t){var p=this._mediaInfo.segments[t],v=Object.assign({},p);v.duration=this._mediaInfo.duration,v.segmentCount=this._mediaInfo.segmentCount,delete v.segments,delete v.keyframesIndex,this._emitter.emit(r.default.MEDIA_INFO,v)},l.prototype._reportStatisticsInfo=function(){var t={};t.url=this._ioctl.currentURL,t.hasRedirect=this._ioctl.hasRedirect,t.hasRedirect&&(t.redirectedURL=this._ioctl.currentRedirectedURL),t.speed=this._ioctl.currentSpeed,t.loaderType=this._ioctl.loaderType,t.currentSegmentIndex=this._currentSegmentIndex,t.totalSegmentCount=this._mediaDataSource.segments.length,this._emitter.emit(r.default.STATISTICS_INFO,t)},l}();y.default=u},"./src/core/transmuxing-events.js":function(E,y,m){m.r(y);var L={IO_ERROR:"io_error",DEMUX_ERROR:"demux_error",INIT_SEGMENT:"init_segment",MEDIA_SEGMENT:"media_segment",LOADING_COMPLETE:"loading_complete",RECOVERED_EARLY_EOF:"recovered_early_eof",MEDIA_INFO:"media_info",METADATA_ARRIVED:"metadata_arrived",SCRIPTDATA_ARRIVED:"scriptdata_arrived",STATISTICS_INFO:"statistics_info",RECOMMEND_SEEKPOINT:"recommend_seekpoint"};y.default=L},"./src/core/transmuxing-worker.js":function(E,y,m){m.r(y);var L=m("./src/utils/logging-control.js"),_=m("./src/utils/polyfill.js"),a=m("./src/core/transmuxing-controller.js"),s=m("./src/core/transmuxing-events.js"),c=function(n){var f=null,h=S.bind(this);_.default.install(),n.addEventListener("message",function(R){switch(R.data.cmd){case"init":f=new a.default(R.data.param[0],R.data.param[1]),f.on(s.default.IO_ERROR,b.bind(this)),f.on(s.default.DEMUX_ERROR,T.bind(this)),f.on(s.default.INIT_SEGMENT,o.bind(this)),f.on(s.default.MEDIA_SEGMENT,r.bind(this)),f.on(s.default.LOADING_COMPLETE,u.bind(this)),f.on(s.default.RECOVERED_EARLY_EOF,l.bind(this)),f.on(s.default.MEDIA_INFO,t.bind(this)),f.on(s.default.METADATA_ARRIVED,p.bind(this)),f.on(s.default.SCRIPTDATA_ARRIVED,v.bind(this)),f.on(s.default.STATISTICS_INFO,g.bind(this)),f.on(s.default.RECOMMEND_SEEKPOINT,O.bind(this));break;case"destroy":f&&(f.destroy(),f=null),n.postMessage({msg:"destroyed"});break;case"start":f.start();break;case"stop":f.stop();break;case"seek":f.seek(R.data.param);break;case"pause":f.pause();break;case"resume":f.resume();break;case"logging_config":{var w=R.data.param;L.default.applyConfig(w),w.enableCallback===!0?L.default.addLogListener(h):L.default.removeLogListener(h);break}}});function o(R,w){var P={msg:s.default.INIT_SEGMENT,data:{type:R,data:w}};n.postMessage(P,[w.data])}function r(R,w){var P={msg:s.default.MEDIA_SEGMENT,data:{type:R,data:w}};n.postMessage(P,[w.data])}function u(){var R={msg:s.default.LOADING_COMPLETE};n.postMessage(R)}function l(){var R={msg:s.default.RECOVERED_EARLY_EOF};n.postMessage(R)}function t(R){var w={msg:s.default.MEDIA_INFO,data:R};n.postMessage(w)}function p(R){var w={msg:s.default.METADATA_ARRIVED,data:R};n.postMessage(w)}function v(R){var w={msg:s.default.SCRIPTDATA_ARRIVED,data:R};n.postMessage(w)}function g(R){var w={msg:s.default.STATISTICS_INFO,data:R};n.postMessage(w)}function b(R,w){n.postMessage({msg:s.default.IO_ERROR,data:{type:R,info:w}})}function T(R,w){n.postMessage({msg:s.default.DEMUX_ERROR,data:{type:R,info:w}})}function O(R){n.postMessage({msg:s.default.RECOMMEND_SEEKPOINT,data:R})}function S(R,w){n.postMessage({msg:"logcat_callback",data:{type:R,logcat:w}})}};y.default=c},"./src/demux/amf-parser.js":function(E,y,m){m.r(y);var L=m("./src/utils/logger.js"),_=m("./src/utils/utf8-conv.js"),a=m("./src/utils/exception.js"),s=function(){var n=new ArrayBuffer(2);return new DataView(n).setInt16(0,256,!0),new Int16Array(n)[0]===256}(),c=function(){function n(){}return n.parseScriptData=function(f,h,o){var r={};try{var u=n.parseValue(f,h,o),l=n.parseValue(f,h+u.size,o-u.size);r[u.data]=l.data}catch(t){L.default.e("AMF",t.toString())}return r},n.parseObject=function(f,h,o){if(o<3)throw new a.IllegalStateException("Data not enough when parse ScriptDataObject");var r=n.parseString(f,h,o),u=n.parseValue(f,h+r.size,o-r.size),l=u.objectEnd;return{data:{name:r.data,value:u.data},size:r.size+u.size,objectEnd:l}},n.parseVariable=function(f,h,o){return n.parseObject(f,h,o)},n.parseString=function(f,h,o){if(o<2)throw new a.IllegalStateException("Data not enough when parse String");var r=new DataView(f,h,o),u=r.getUint16(0,!s),l;return u>0?l=(0,_.default)(new Uint8Array(f,h+2,u)):l="",{data:l,size:2+u}},n.parseLongString=function(f,h,o){if(o<4)throw new a.IllegalStateException("Data not enough when parse LongString");var r=new DataView(f,h,o),u=r.getUint32(0,!s),l;return u>0?l=(0,_.default)(new Uint8Array(f,h+4,u)):l="",{data:l,size:4+u}},n.parseDate=function(f,h,o){if(o<10)throw new a.IllegalStateException("Data size invalid when parse Date");var r=new DataView(f,h,o),u=r.getFloat64(0,!s),l=r.getInt16(8,!s);return u+=l*60*1e3,{data:new Date(u),size:10}},n.parseValue=function(f,h,o){if(o<1)throw new a.IllegalStateException("Data not enough when parse Value");var r=new DataView(f,h,o),u=1,l=r.getUint8(0),t,p=!1;try{switch(l){case 0:t=r.getFloat64(1,!s),u+=8;break;case 1:{var v=r.getUint8(1);t=!!v,u+=1;break}case 2:{var g=n.parseString(f,h+1,o-1);t=g.data,u+=g.size;break}case 3:{t={};var b=0;for((r.getUint32(o-4,!s)&16777215)===9&&(b=3);u<o-4;){var T=n.parseObject(f,h+u,o-u-b);if(T.objectEnd)break;t[T.data.name]=T.data.value,u+=T.size}if(u<=o-3){var O=r.getUint32(u-1,!s)&16777215;O===9&&(u+=3)}break}case 8:{t={},u+=4;var b=0;for((r.getUint32(o-4,!s)&16777215)===9&&(b=3);u<o-8;){var S=n.parseVariable(f,h+u,o-u-b);if(S.objectEnd)break;t[S.data.name]=S.data.value,u+=S.size}if(u<=o-3){var O=r.getUint32(u-1,!s)&16777215;O===9&&(u+=3)}break}case 9:t=void 0,u=1,p=!0;break;case 10:{t=[];var R=r.getUint32(1,!s);u+=4;for(var w=0;w<R;w++){var P=n.parseValue(f,h+u,o-u);t.push(P.data),u+=P.size}break}case 11:{var D=n.parseDate(f,h+1,o-1);t=D.data,u+=D.size;break}case 12:{var M=n.parseString(f,h+1,o-1);t=M.data,u+=M.size;break}default:u=o,L.default.w("AMF","Unsupported AMF value type "+l)}}catch(G){L.default.e("AMF",G.toString())}return{data:t,size:u,objectEnd:p}},n}();y.default=c},"./src/demux/demux-errors.js":function(E,y,m){m.r(y);var L={OK:"OK",FORMAT_ERROR:"FormatError",FORMAT_UNSUPPORTED:"FormatUnsupported",CODEC_UNSUPPORTED:"CodecUnsupported"};y.default=L},"./src/demux/exp-golomb.js":function(E,y,m){m.r(y);var L=m("./src/utils/exception.js"),_=function(){function a(s){this.TAG="ExpGolomb",this._buffer=s,this._buffer_index=0,this._total_bytes=s.byteLength,this._total_bits=s.byteLength*8,this._current_word=0,this._current_word_bits_left=0}return a.prototype.destroy=function(){this._buffer=null},a.prototype._fillCurrentWord=function(){var s=this._total_bytes-this._buffer_index;if(s<=0)throw new L.IllegalStateException("ExpGolomb: _fillCurrentWord() but no bytes available");var c=Math.min(4,s),n=new Uint8Array(4);n.set(this._buffer.subarray(this._buffer_index,this._buffer_index+c)),this._current_word=new DataView(n.buffer).getUint32(0,!1),this._buffer_index+=c,this._current_word_bits_left=c*8},a.prototype.readBits=function(s){if(s>32)throw new L.InvalidArgumentException("ExpGolomb: readBits() bits exceeded max 32bits!");if(s<=this._current_word_bits_left){var c=this._current_word>>>32-s;return this._current_word<<=s,this._current_word_bits_left-=s,c}var n=this._current_word_bits_left?this._current_word:0;n=n>>>32-this._current_word_bits_left;var f=s-this._current_word_bits_left;this._fillCurrentWord();var h=Math.min(f,this._current_word_bits_left),o=this._current_word>>>32-h;return this._current_word<<=h,this._current_word_bits_left-=h,n=n<<h|o,n},a.prototype.readBool=function(){return this.readBits(1)===1},a.prototype.readByte=function(){return this.readBits(8)},a.prototype._skipLeadingZero=function(){var s;for(s=0;s<this._current_word_bits_left;s++)if(this._current_word&2147483648>>>s)return this._current_word<<=s,this._current_word_bits_left-=s,s;return this._fillCurrentWord(),s+this._skipLeadingZero()},a.prototype.readUEG=function(){var s=this._skipLeadingZero();return this.readBits(s+1)-1},a.prototype.readSEG=function(){var s=this.readUEG();return s&1?s+1>>>1:-1*(s>>>1)},a}();y.default=_},"./src/demux/flv-demuxer.js":function(E,y,m){m.r(y);var L=m("./src/utils/logger.js"),_=m("./src/demux/amf-parser.js"),a=m("./src/demux/sps-parser.js"),s=m("./src/demux/demux-errors.js"),c=m("./src/core/media-info.js"),n=m("./src/utils/exception.js");function f(o,r){return o[r]<<24|o[r+1]<<16|o[r+2]<<8|o[r+3]}var h=function(){function o(r,u){this.TAG="FLVDemuxer",this._config=u,this._onError=null,this._onMediaInfo=null,this._onMetaDataArrived=null,this._onScriptDataArrived=null,this._onTrackMetadata=null,this._onDataAvailable=null,this._dataOffset=r.dataOffset,this._firstParse=!0,this._dispatch=!1,this._hasAudio=r.hasAudioTrack,this._hasVideo=r.hasVideoTrack,this._hasAudioFlagOverrided=!1,this._hasVideoFlagOverrided=!1,this._audioInitialMetadataDispatched=!1,this._videoInitialMetadataDispatched=!1,this._mediaInfo=new c.default,this._mediaInfo.hasAudio=this._hasAudio,this._mediaInfo.hasVideo=this._hasVideo,this._metadata=null,this._audioMetadata=null,this._videoMetadata=null,this._naluLengthSize=4,this._timestampBase=0,this._timescale=1e3,this._duration=0,this._durationOverrided=!1,this._referenceFrameRate={fixed:!0,fps:23.976,fps_num:23976,fps_den:1e3},this._flvSoundRateTable=[5500,11025,22050,44100,48e3],this._mpegSamplingRates=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],this._mpegAudioV10SampleRateTable=[44100,48e3,32e3,0],this._mpegAudioV20SampleRateTable=[22050,24e3,16e3,0],this._mpegAudioV25SampleRateTable=[11025,12e3,8e3,0],this._mpegAudioL1BitRateTable=[0,32,64,96,128,160,192,224,256,288,320,352,384,416,448,-1],this._mpegAudioL2BitRateTable=[0,32,48,56,64,80,96,112,128,160,192,224,256,320,384,-1],this._mpegAudioL3BitRateTable=[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],this._videoTrack={type:"video",id:1,sequenceNumber:0,samples:[],length:0},this._audioTrack={type:"audio",id:2,sequenceNumber:0,samples:[],length:0},this._littleEndian=function(){var l=new ArrayBuffer(2);return new DataView(l).setInt16(0,256,!0),new Int16Array(l)[0]===256}()}return o.prototype.destroy=function(){this._mediaInfo=null,this._metadata=null,this._audioMetadata=null,this._videoMetadata=null,this._videoTrack=null,this._audioTrack=null,this._onError=null,this._onMediaInfo=null,this._onMetaDataArrived=null,this._onScriptDataArrived=null,this._onTrackMetadata=null,this._onDataAvailable=null},o.probe=function(r){var u=new Uint8Array(r),l={match:!1};if(u[0]!==70||u[1]!==76||u[2]!==86||u[3]!==1)return l;var t=(u[4]&4)>>>2!==0,p=(u[4]&1)!==0,v=f(u,5);return v<9?l:{match:!0,consumed:v,dataOffset:v,hasAudioTrack:t,hasVideoTrack:p}},o.prototype.bindDataSource=function(r){return r.onDataArrival=this.parseChunks.bind(this),this},Object.defineProperty(o.prototype,"onTrackMetadata",{get:function(){return this._onTrackMetadata},set:function(r){this._onTrackMetadata=r},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"onMediaInfo",{get:function(){return this._onMediaInfo},set:function(r){this._onMediaInfo=r},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"onMetaDataArrived",{get:function(){return this._onMetaDataArrived},set:function(r){this._onMetaDataArrived=r},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"onScriptDataArrived",{get:function(){return this._onScriptDataArrived},set:function(r){this._onScriptDataArrived=r},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"onError",{get:function(){return this._onError},set:function(r){this._onError=r},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"onDataAvailable",{get:function(){return this._onDataAvailable},set:function(r){this._onDataAvailable=r},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"timestampBase",{get:function(){return this._timestampBase},set:function(r){this._timestampBase=r},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"overridedDuration",{get:function(){return this._duration},set:function(r){this._durationOverrided=!0,this._duration=r,this._mediaInfo.duration=r},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"overridedHasAudio",{set:function(r){this._hasAudioFlagOverrided=!0,this._hasAudio=r,this._mediaInfo.hasAudio=r},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"overridedHasVideo",{set:function(r){this._hasVideoFlagOverrided=!0,this._hasVideo=r,this._mediaInfo.hasVideo=r},enumerable:!1,configurable:!0}),o.prototype.resetMediaInfo=function(){this._mediaInfo=new c.default},o.prototype._isInitialMetadataDispatched=function(){return this._hasAudio&&this._hasVideo?this._audioInitialMetadataDispatched&&this._videoInitialMetadataDispatched:this._hasAudio&&!this._hasVideo?this._audioInitialMetadataDispatched:!this._hasAudio&&this._hasVideo?this._videoInitialMetadataDispatched:!1},o.prototype.parseChunks=function(r,u){if(!this._onError||!this._onMediaInfo||!this._onTrackMetadata||!this._onDataAvailable)throw new n.IllegalStateException("Flv: onError & onMediaInfo & onTrackMetadata & onDataAvailable callback must be specified");var l=0,t=this._littleEndian;if(u===0)if(r.byteLength>13){var p=o.probe(r);l=p.dataOffset}else return 0;if(this._firstParse){this._firstParse=!1,u+l!==this._dataOffset&&L.default.w(this.TAG,"First time parsing but chunk byteStart invalid!");var v=new DataView(r,l),g=v.getUint32(0,!t);g!==0&&L.default.w(this.TAG,"PrevTagSize0 !== 0 !!!"),l+=4}for(;l<r.byteLength;){this._dispatch=!0;var v=new DataView(r,l);if(l+11+4>r.byteLength)break;var b=v.getUint8(0),T=v.getUint32(0,!t)&16777215;if(l+11+T+4>r.byteLength)break;if(b!==8&&b!==9&&b!==18){L.default.w(this.TAG,"Unsupported tag type "+b+", skipped"),l+=11+T+4;continue}var O=v.getUint8(4),S=v.getUint8(5),R=v.getUint8(6),w=v.getUint8(7),P=R|S<<8|O<<16|w<<24,D=v.getUint32(7,!t)&16777215;D!==0&&L.default.w(this.TAG,"Meet tag which has StreamID != 0!");var M=l+11;switch(b){case 8:this._parseAudioData(r,M,T,P);break;case 9:this._parseVideoData(r,M,T,P,u+l);break;case 18:this._parseScriptData(r,M,T);break}var G=v.getUint32(11+T,!t);G!==11+T&&L.default.w(this.TAG,"Invalid PrevTagSize "+G),l+=11+T+4}return this._isInitialMetadataDispatched()&&this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack),l},o.prototype._parseScriptData=function(r,u,l){var t=_.default.parseScriptData(r,u,l);if(t.hasOwnProperty("onMetaData")){if(t.onMetaData==null||typeof t.onMetaData!="object"){L.default.w(this.TAG,"Invalid onMetaData structure!");return}this._metadata&&L.default.w(this.TAG,"Found another onMetaData tag!"),this._metadata=t;var p=this._metadata.onMetaData;if(this._onMetaDataArrived&&this._onMetaDataArrived(Object.assign({},p)),typeof p.hasAudio=="boolean"&&this._hasAudioFlagOverrided===!1&&(this._hasAudio=p.hasAudio,this._mediaInfo.hasAudio=this._hasAudio),typeof p.hasVideo=="boolean"&&this._hasVideoFlagOverrided===!1&&(this._hasVideo=p.hasVideo,this._mediaInfo.hasVideo=this._hasVideo),typeof p.audiodatarate=="number"&&(this._mediaInfo.audioDataRate=p.audiodatarate),typeof p.videodatarate=="number"&&(this._mediaInfo.videoDataRate=p.videodatarate),typeof p.width=="number"&&(this._mediaInfo.width=p.width),typeof p.height=="number"&&(this._mediaInfo.height=p.height),typeof p.duration=="number"){if(!this._durationOverrided){var v=Math.floor(p.duration*this._timescale);this._duration=v,this._mediaInfo.duration=v}}else this._mediaInfo.duration=0;if(typeof p.framerate=="number"){var g=Math.floor(p.framerate*1e3);if(g>0){var b=g/1e3;this._referenceFrameRate.fixed=!0,this._referenceFrameRate.fps=b,this._referenceFrameRate.fps_num=g,this._referenceFrameRate.fps_den=1e3,this._mediaInfo.fps=b}}if(typeof p.keyframes=="object"){this._mediaInfo.hasKeyframesIndex=!0;var T=p.keyframes;this._mediaInfo.keyframesIndex=this._parseKeyframesIndex(T),p.keyframes=null}else this._mediaInfo.hasKeyframesIndex=!1;this._dispatch=!1,this._mediaInfo.metadata=p,L.default.v(this.TAG,"Parsed onMetaData"),this._mediaInfo.isComplete()&&this._onMediaInfo(this._mediaInfo)}Object.keys(t).length>0&&this._onScriptDataArrived&&this._onScriptDataArrived(Object.assign({},t))},o.prototype._parseKeyframesIndex=function(r){for(var u=[],l=[],t=1;t<r.times.length;t++){var p=this._timestampBase+Math.floor(r.times[t]*1e3);u.push(p),l.push(r.filepositions[t])}return{times:u,filepositions:l}},o.prototype._parseAudioData=function(r,u,l,t){if(l<=1){L.default.w(this.TAG,"Flv: Invalid audio packet, missing SoundData payload!");return}if(!(this._hasAudioFlagOverrided===!0&&this._hasAudio===!1)){this._littleEndian;var p=new DataView(r,u,l),v=p.getUint8(0),g=v>>>4;if(g!==2&&g!==10){this._onError(s.default.CODEC_UNSUPPORTED,"Flv: Unsupported audio codec idx: "+g);return}var b=0,T=(v&12)>>>2;if(T>=0&&T<=4)b=this._flvSoundRateTable[T];else{this._onError(s.default.FORMAT_ERROR,"Flv: Invalid audio sample rate idx: "+T);return}var O=v&1,S=this._audioMetadata,R=this._audioTrack;if(S||(this._hasAudio===!1&&this._hasAudioFlagOverrided===!1&&(this._hasAudio=!0,this._mediaInfo.hasAudio=!0),S=this._audioMetadata={},S.type="audio",S.id=R.id,S.timescale=this._timescale,S.duration=this._duration,S.audioSampleRate=b,S.channelCount=O===0?1:2),g===10){var w=this._parseAACAudioData(r,u+1,l-1);if(w==null)return;if(w.packetType===0){S.config&&L.default.w(this.TAG,"Found another AudioSpecificConfig!");var P=w.data;S.audioSampleRate=P.samplingRate,S.channelCount=P.channelCount,S.codec=P.codec,S.originalCodec=P.originalCodec,S.config=P.config,S.refSampleDuration=1024/S.audioSampleRate*S.timescale,L.default.v(this.TAG,"Parsed AudioSpecificConfig"),this._isInitialMetadataDispatched()?this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack):this._audioInitialMetadataDispatched=!0,this._dispatch=!1,this._onTrackMetadata("audio",S);var D=this._mediaInfo;D.audioCodec=S.originalCodec,D.audioSampleRate=S.audioSampleRate,D.audioChannelCount=S.channelCount,D.hasVideo?D.videoCodec!=null&&(D.mimeType='video/x-flv; codecs="'+D.videoCodec+","+D.audioCodec+'"'):D.mimeType='video/x-flv; codecs="'+D.audioCodec+'"',D.isComplete()&&this._onMediaInfo(D)}else if(w.packetType===1){var M=this._timestampBase+t,G={unit:w.data,length:w.data.byteLength,dts:M,pts:M};R.samples.push(G),R.length+=w.data.length}else L.default.e(this.TAG,"Flv: Unsupported AAC data type "+w.packetType)}else if(g===2){if(!S.codec){var P=this._parseMP3AudioData(r,u+1,l-1,!0);if(P==null)return;S.audioSampleRate=P.samplingRate,S.channelCount=P.channelCount,S.codec=P.codec,S.originalCodec=P.originalCodec,S.refSampleDuration=1152/S.audioSampleRate*S.timescale,L.default.v(this.TAG,"Parsed MPEG Audio Frame Header"),this._audioInitialMetadataDispatched=!0,this._onTrackMetadata("audio",S);var D=this._mediaInfo;D.audioCodec=S.codec,D.audioSampleRate=S.audioSampleRate,D.audioChannelCount=S.channelCount,D.audioDataRate=P.bitRate,D.hasVideo?D.videoCodec!=null&&(D.mimeType='video/x-flv; codecs="'+D.videoCodec+","+D.audioCodec+'"'):D.mimeType='video/x-flv; codecs="'+D.audioCodec+'"',D.isComplete()&&this._onMediaInfo(D)}var z=this._parseMP3AudioData(r,u+1,l-1,!1);if(z==null)return;var M=this._timestampBase+t,j={unit:z,length:z.byteLength,dts:M,pts:M};R.samples.push(j),R.length+=z.length}}},o.prototype._parseAACAudioData=function(r,u,l){if(l<=1){L.default.w(this.TAG,"Flv: Invalid AAC packet, missing AACPacketType or/and Data!");return}var t={},p=new Uint8Array(r,u,l);return t.packetType=p[0],p[0]===0?t.data=this._parseAACAudioSpecificConfig(r,u+1,l-1):t.data=p.subarray(1),t},o.prototype._parseAACAudioSpecificConfig=function(r,u,l){var t=new Uint8Array(r,u,l),p=null,v=0,g=0,b=0,T=null;if(v=g=t[0]>>>3,b=(t[0]&7)<<1|t[1]>>>7,b<0||b>=this._mpegSamplingRates.length){this._onError(s.default.FORMAT_ERROR,"Flv: AAC invalid sampling frequency index!");return}var O=this._mpegSamplingRates[b],S=(t[1]&120)>>>3;if(S<0||S>=8){this._onError(s.default.FORMAT_ERROR,"Flv: AAC invalid channel configuration");return}v===5&&(T=(t[1]&7)<<1|t[2]>>>7,(t[2]&124)>>>2);var R=self.navigator.userAgent.toLowerCase();return R.indexOf("firefox")!==-1?b>=6?(v=5,p=new Array(4),T=b-3):(v=2,p=new Array(2),T=b):R.indexOf("android")!==-1?(v=2,p=new Array(2),T=b):(v=5,T=b,p=new Array(4),b>=6?T=b-3:S===1&&(v=2,p=new Array(2),T=b)),p[0]=v<<3,p[0]|=(b&15)>>>1,p[1]=(b&15)<<7,p[1]|=(S&15)<<3,v===5&&(p[1]|=(T&15)>>>1,p[2]=(T&1)<<7,p[2]|=8,p[3]=0),{config:p,samplingRate:O,channelCount:S,codec:"mp4a.40."+v,originalCodec:"mp4a.40."+g}},o.prototype._parseMP3AudioData=function(r,u,l,t){if(l<4){L.default.w(this.TAG,"Flv: Invalid MP3 packet, header missing!");return}this._littleEndian;var p=new Uint8Array(r,u,l),v=null;if(t){if(p[0]!==255)return;var g=p[1]>>>3&3,b=(p[1]&6)>>1,T=(p[2]&240)>>>4,O=(p[2]&12)>>>2,S=p[3]>>>6&3,R=S!==3?2:1,w=0,P=0,D="mp3";switch(g){case 0:w=this._mpegAudioV25SampleRateTable[O];break;case 2:w=this._mpegAudioV20SampleRateTable[O];break;case 3:w=this._mpegAudioV10SampleRateTable[O];break}switch(b){case 1:T<this._mpegAudioL3BitRateTable.length&&(P=this._mpegAudioL3BitRateTable[T]);break;case 2:T<this._mpegAudioL2BitRateTable.length&&(P=this._mpegAudioL2BitRateTable[T]);break;case 3:T<this._mpegAudioL1BitRateTable.length&&(P=this._mpegAudioL1BitRateTable[T]);break}v={bitRate:P,samplingRate:w,channelCount:R,codec:D,originalCodec:D}}else v=p;return v},o.prototype._parseVideoData=function(r,u,l,t,p){if(l<=1){L.default.w(this.TAG,"Flv: Invalid video packet, missing VideoData payload!");return}if(!(this._hasVideoFlagOverrided===!0&&this._hasVideo===!1)){var v=new Uint8Array(r,u,l)[0],g=(v&240)>>>4,b=v&15;if(b!==7){this._onError(s.default.CODEC_UNSUPPORTED,"Flv: Unsupported codec in video frame: "+b);return}this._parseAVCVideoPacket(r,u+1,l-1,t,p,g)}},o.prototype._parseAVCVideoPacket=function(r,u,l,t,p,v){if(l<4){L.default.w(this.TAG,"Flv: Invalid AVC packet, missing AVCPacketType or/and CompositionTime");return}var g=this._littleEndian,b=new DataView(r,u,l),T=b.getUint8(0),O=b.getUint32(0,!g)&16777215,S=O<<8>>8;if(T===0)this._parseAVCDecoderConfigurationRecord(r,u+4,l-4);else if(T===1)this._parseAVCVideoData(r,u+4,l-4,t,p,v,S);else if(T!==2){this._onError(s.default.FORMAT_ERROR,"Flv: Invalid video packet type "+T);return}},o.prototype._parseAVCDecoderConfigurationRecord=function(r,u,l){if(l<7){L.default.w(this.TAG,"Flv: Invalid AVCDecoderConfigurationRecord, lack of data!");return}var t=this._videoMetadata,p=this._videoTrack,v=this._littleEndian,g=new DataView(r,u,l);t?typeof t.avcc<"u"&&L.default.w(this.TAG,"Found another AVCDecoderConfigurationRecord!"):(this._hasVideo===!1&&this._hasVideoFlagOverrided===!1&&(this._hasVideo=!0,this._mediaInfo.hasVideo=!0),t=this._videoMetadata={},t.type="video",t.id=p.id,t.timescale=this._timescale,t.duration=this._duration);var b=g.getUint8(0),T=g.getUint8(1);if(g.getUint8(2),g.getUint8(3),b!==1||T===0){this._onError(s.default.FORMAT_ERROR,"Flv: Invalid AVCDecoderConfigurationRecord");return}if(this._naluLengthSize=(g.getUint8(4)&3)+1,this._naluLengthSize!==3&&this._naluLengthSize!==4){this._onError(s.default.FORMAT_ERROR,"Flv: Strange NaluLengthSizeMinusOne: "+(this._naluLengthSize-1));return}var O=g.getUint8(5)&31;if(O===0){this._onError(s.default.FORMAT_ERROR,"Flv: Invalid AVCDecoderConfigurationRecord: No SPS");return}else O>1&&L.default.w(this.TAG,"Flv: Strange AVCDecoderConfigurationRecord: SPS Count = "+O);for(var S=6,R=0;R<O;R++){var w=g.getUint16(S,!v);if(S+=2,w!==0){var P=new Uint8Array(r,u+S,w);S+=w;var D=a.default.parseSPS(P);if(R===0){t.codecWidth=D.codec_size.width,t.codecHeight=D.codec_size.height,t.presentWidth=D.present_size.width,t.presentHeight=D.present_size.height,t.profile=D.profile_string,t.level=D.level_string,t.bitDepth=D.bit_depth,t.chromaFormat=D.chroma_format,t.sarRatio=D.sar_ratio,t.frameRate=D.frame_rate,(D.frame_rate.fixed===!1||D.frame_rate.fps_num===0||D.frame_rate.fps_den===0)&&(t.frameRate=this._referenceFrameRate);var M=t.frameRate.fps_den,G=t.frameRate.fps_num;t.refSampleDuration=t.timescale*(M/G);for(var z=P.subarray(1,4),j="avc1.",Z=0;Z<3;Z++){var ee=z[Z].toString(16);ee.length<2&&(ee="0"+ee),j+=ee}t.codec=j;var W=this._mediaInfo;W.width=t.codecWidth,W.height=t.codecHeight,W.fps=t.frameRate.fps,W.profile=t.profile,W.level=t.level,W.refFrames=D.ref_frames,W.chromaFormat=D.chroma_format_string,W.sarNum=t.sarRatio.width,W.sarDen=t.sarRatio.height,W.videoCodec=j,W.hasAudio?W.audioCodec!=null&&(W.mimeType='video/x-flv; codecs="'+W.videoCodec+","+W.audioCodec+'"'):W.mimeType='video/x-flv; codecs="'+W.videoCodec+'"',W.isComplete()&&this._onMediaInfo(W)}}}var J=g.getUint8(S);if(J===0){this._onError(s.default.FORMAT_ERROR,"Flv: Invalid AVCDecoderConfigurationRecord: No PPS");return}else J>1&&L.default.w(this.TAG,"Flv: Strange AVCDecoderConfigurationRecord: PPS Count = "+J);S++;for(var R=0;R<J;R++){var w=g.getUint16(S,!v);S+=2,w!==0&&(S+=w)}t.avcc=new Uint8Array(l),t.avcc.set(new Uint8Array(r,u,l),0),L.default.v(this.TAG,"Parsed AVCDecoderConfigurationRecord"),this._isInitialMetadataDispatched()?this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack):this._videoInitialMetadataDispatched=!0,this._dispatch=!1,this._onTrackMetadata("video",t)},o.prototype._parseAVCVideoData=function(r,u,l,t,p,v,g){for(var b=this._littleEndian,T=new DataView(r,u,l),O=[],S=0,R=0,w=this._naluLengthSize,P=this._timestampBase+t,D=v===1;R<l;){if(R+4>=l){L.default.w(this.TAG,"Malformed Nalu near timestamp "+P+", offset = "+R+", dataSize = "+l);break}var M=T.getUint32(R,!b);if(w===3&&(M>>>=8),M>l-w){L.default.w(this.TAG,"Malformed Nalus near timestamp "+P+", NaluSize > DataSize!");return}var G=T.getUint8(R+w)&31;G===5&&(D=!0);var z=new Uint8Array(r,u+R,w+M),j={type:G,data:z};O.push(j),S+=z.byteLength,R+=w+M}if(O.length){var Z=this._videoTrack,ee={units:O,length:S,isKeyframe:D,dts:P,cts:g,pts:P+g};D&&(ee.fileposition=p),Z.samples.push(ee),Z.length+=S}},o}();y.default=h},"./src/demux/sps-parser.js":function(E,y,m){m.r(y);var L=m("./src/demux/exp-golomb.js"),_=function(){function a(){}return a._ebsp2rbsp=function(s){for(var c=s,n=c.byteLength,f=new Uint8Array(n),h=0,o=0;o<n;o++)o>=2&&c[o]===3&&c[o-1]===0&&c[o-2]===0||(f[h]=c[o],h++);return new Uint8Array(f.buffer,0,h)},a.parseSPS=function(s){var c=a._ebsp2rbsp(s),n=new L.default(c);n.readByte();var f=n.readByte();n.readByte();var h=n.readByte();n.readUEG();var o=a.getProfileString(f),r=a.getLevelString(h),u=1,l=420,t=[0,420,422,444],p=8;if((f===100||f===110||f===122||f===244||f===44||f===83||f===86||f===118||f===128||f===138||f===144)&&(u=n.readUEG(),u===3&&n.readBits(1),u<=3&&(l=t[u]),p=n.readUEG()+8,n.readUEG(),n.readBits(1),n.readBool()))for(var v=u!==3?8:12,g=0;g<v;g++)n.readBool()&&(g<6?a._skipScalingList(n,16):a._skipScalingList(n,64));n.readUEG();var b=n.readUEG();if(b===0)n.readUEG();else if(b===1){n.readBits(1),n.readSEG(),n.readSEG();for(var T=n.readUEG(),g=0;g<T;g++)n.readSEG()}var O=n.readUEG();n.readBits(1);var S=n.readUEG(),R=n.readUEG(),w=n.readBits(1);w===0&&n.readBits(1),n.readBits(1);var P=0,D=0,M=0,G=0,z=n.readBool();z&&(P=n.readUEG(),D=n.readUEG(),M=n.readUEG(),G=n.readUEG());var j=1,Z=1,ee=0,W=!0,J=0,xe=0,te=n.readBool();if(te){if(n.readBool()){var re=n.readByte(),Re=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2],fe=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1];re>0&&re<16?(j=Re[re-1],Z=fe[re-1]):re===255&&(j=n.readByte()<<8|n.readByte(),Z=n.readByte()<<8|n.readByte())}if(n.readBool()&&n.readBool(),n.readBool()&&(n.readBits(4),n.readBool()&&n.readBits(24)),n.readBool()&&(n.readUEG(),n.readUEG()),n.readBool()){var Ee=n.readBits(32),Fe=n.readBits(32);W=n.readBool(),J=Fe,xe=Ee*2,ee=J/xe}}var ne=1;(j!==1||Z!==1)&&(ne=j/Z);var K=0,ye=0;if(u===0)K=1,ye=2-w;else{var Ie=u===3?1:2,Xe=u===1?2:1;K=Ie,ye=Xe*(2-w)}var Be=(S+1)*16,We=(2-w)*((R+1)*16);Be-=(P+D)*K,We-=(M+G)*ye;var qe=Math.ceil(Be*ne);return n.destroy(),n=null,{profile_string:o,level_string:r,bit_depth:p,ref_frames:O,chroma_format:l,chroma_format_string:a.getChromaFormatString(l),frame_rate:{fixed:W,fps:ee,fps_den:xe,fps_num:J},sar_ratio:{width:j,height:Z},codec_size:{width:Be,height:We},present_size:{width:qe,height:We}}},a._skipScalingList=function(s,c){for(var n=8,f=8,h=0,o=0;o<c;o++)f!==0&&(h=s.readSEG(),f=(n+h+256)%256),n=f===0?n:f},a.getProfileString=function(s){switch(s){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}},a.getLevelString=function(s){return(s/10).toFixed(1)},a.getChromaFormatString=function(s){switch(s){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}},a}();y.default=_},"./src/flv.js":function(E,y,m){m.r(y);var L=m("./src/utils/polyfill.js"),_=m("./src/core/features.js"),a=m("./src/io/loader.js"),s=m("./src/player/flv-player.js"),c=m("./src/player/native-player.js"),n=m("./src/player/player-events.js"),f=m("./src/player/player-errors.js"),h=m("./src/utils/logging-control.js"),o=m("./src/utils/exception.js");L.default.install();function r(p,v){var g=p;if(g==null||typeof g!="object")throw new o.InvalidArgumentException("MediaDataSource must be an javascript object!");if(!g.hasOwnProperty("type"))throw new o.InvalidArgumentException("MediaDataSource must has type field to indicate video file type!");switch(g.type){case"flv":return new s.default(g,v);default:return new c.default(g,v)}}function u(){return _.default.supportMSEH264Playback()}function l(){return _.default.getFeatureList()}var t={};t.createPlayer=r,t.isSupported=u,t.getFeatureList=l,t.BaseLoader=a.BaseLoader,t.LoaderStatus=a.LoaderStatus,t.LoaderErrors=a.LoaderErrors,t.Events=n.default,t.ErrorTypes=f.ErrorTypes,t.ErrorDetails=f.ErrorDetails,t.FlvPlayer=s.default,t.NativePlayer=c.default,t.LoggingControl=h.default,Object.defineProperty(t,"version",{enumerable:!0,get:function(){return"1.6.2"}}),y.default=t},"./src/index.js":function(E,y,m){E.exports=m("./src/flv.js").default},"./src/io/fetch-stream-loader.js":function(E,y,m){m.r(y);var L=m("./src/utils/browser.js"),_=m("./src/io/loader.js"),a=m("./src/utils/exception.js"),s=function(){var n=function(f,h){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,r){o.__proto__=r}||function(o,r){for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&(o[u]=r[u])},n(f,h)};return function(f,h){if(typeof h!="function"&&h!==null)throw new TypeError("Class extends value "+String(h)+" is not a constructor or null");n(f,h);function o(){this.constructor=f}f.prototype=h===null?Object.create(h):(o.prototype=h.prototype,new o)}}(),c=function(n){s(f,n);function f(h,o){var r=n.call(this,"fetch-stream-loader")||this;return r.TAG="FetchStreamLoader",r._seekHandler=h,r._config=o,r._needStash=!0,r._requestAbort=!1,r._contentLength=null,r._receivedLength=0,r}return f.isSupported=function(){try{var h=L.default.msedge&&L.default.version.minor>=15048,o=L.default.msedge?h:!0;return self.fetch&&self.ReadableStream&&o}catch{return!1}},f.prototype.destroy=function(){this.isWorking()&&this.abort(),n.prototype.destroy.call(this)},f.prototype.open=function(h,o){var r=this;this._dataSource=h,this._range=o;var u=h.url;this._config.reuseRedirectedURL&&h.redirectedURL!=null&&(u=h.redirectedURL);var l=this._seekHandler.getConfig(u,o),t=new self.Headers;if(typeof l.headers=="object"){var p=l.headers;for(var v in p)p.hasOwnProperty(v)&&t.append(v,p[v])}var g={method:"GET",headers:t,mode:"cors",cache:"default",referrerPolicy:"no-referrer-when-downgrade"};if(typeof this._config.headers=="object")for(var v in this._config.headers)t.append(v,this._config.headers[v]);h.cors===!1&&(g.mode="same-origin"),h.withCredentials&&(g.credentials="include"),h.referrerPolicy&&(g.referrerPolicy=h.referrerPolicy),self.AbortController&&(this._abortController=new self.AbortController,g.signal=this._abortController.signal),this._status=_.LoaderStatus.kConnecting,self.fetch(l.url,g).then(function(b){if(r._requestAbort){r._status=_.LoaderStatus.kIdle,b.body.cancel();return}if(b.ok&&b.status>=200&&b.status<=299){if(b.url!==l.url&&r._onURLRedirect){var T=r._seekHandler.removeURLParameters(b.url);r._onURLRedirect(T)}var O=b.headers.get("Content-Length");return O!=null&&(r._contentLength=parseInt(O),r._contentLength!==0&&r._onContentLengthKnown&&r._onContentLengthKnown(r._contentLength)),r._pump.call(r,b.body.getReader())}else if(r._status=_.LoaderStatus.kError,r._onError)r._onError(_.LoaderErrors.HTTP_STATUS_CODE_INVALID,{code:b.status,msg:b.statusText});else throw new a.RuntimeException("FetchStreamLoader: Http code invalid, "+b.status+" "+b.statusText)}).catch(function(b){if(!(r._abortController&&r._abortController.signal.aborted))if(r._status=_.LoaderStatus.kError,r._onError)r._onError(_.LoaderErrors.EXCEPTION,{code:-1,msg:b.message});else throw b})},f.prototype.abort=function(){if(this._requestAbort=!0,(this._status!==_.LoaderStatus.kBuffering||!L.default.chrome)&&this._abortController)try{this._abortController.abort()}catch{}},f.prototype._pump=function(h){var o=this;return h.read().then(function(r){if(r.done)if(o._contentLength!==null&&o._receivedLength<o._contentLength){o._status=_.LoaderStatus.kError;var u=_.LoaderErrors.EARLY_EOF,l={code:-1,msg:"Fetch stream meet Early-EOF"};if(o._onError)o._onError(u,l);else throw new a.RuntimeException(l.msg)}else o._status=_.LoaderStatus.kComplete,o._onComplete&&o._onComplete(o._range.from,o._range.from+o._receivedLength-1);else{if(o._abortController&&o._abortController.signal.aborted){o._status=_.LoaderStatus.kComplete;return}else if(o._requestAbort===!0)return o._status=_.LoaderStatus.kComplete,h.cancel();o._status=_.LoaderStatus.kBuffering;var t=r.value.buffer,p=o._range.from+o._receivedLength;o._receivedLength+=t.byteLength,o._onDataArrival&&o._onDataArrival(t,p,o._receivedLength),o._pump(h)}}).catch(function(r){if(o._abortController&&o._abortController.signal.aborted){o._status=_.LoaderStatus.kComplete;return}if(!(r.code===11&&L.default.msedge)){o._status=_.LoaderStatus.kError;var u=0,l=null;if((r.code===19||r.message==="network error")&&(o._contentLength===null||o._contentLength!==null&&o._receivedLength<o._contentLength)?(u=_.LoaderErrors.EARLY_EOF,l={code:r.code,msg:"Fetch stream meet Early-EOF"}):(u=_.LoaderErrors.EXCEPTION,l={code:r.code,msg:r.message}),o._onError)o._onError(u,l);else throw new a.RuntimeException(l.msg)}})},f}(_.BaseLoader);y.default=c},"./src/io/io-controller.js":function(E,y,m){m.r(y);var L=m("./src/utils/logger.js"),_=m("./src/io/speed-sampler.js"),a=m("./src/io/loader.js"),s=m("./src/io/fetch-stream-loader.js"),c=m("./src/io/xhr-moz-chunked-loader.js"),n=m("./src/io/xhr-range-loader.js"),f=m("./src/io/websocket-loader.js"),h=m("./src/io/range-seek-handler.js"),o=m("./src/io/param-seek-handler.js"),r=m("./src/utils/exception.js"),u=function(){function l(t,p,v){this.TAG="IOController",this._config=p,this._extraData=v,this._stashInitialSize=1024*384,p.stashInitialSize!=null&&p.stashInitialSize>0&&(this._stashInitialSize=p.stashInitialSize),this._stashUsed=0,this._stashSize=this._stashInitialSize,this._bufferSize=1024*1024*3,this._stashBuffer=new ArrayBuffer(this._bufferSize),this._stashByteStart=0,this._enableStash=!0,p.enableStashBuffer===!1&&(this._enableStash=!1),this._loader=null,this._loaderClass=null,this._seekHandler=null,this._dataSource=t,this._isWebSocketURL=/wss?:\/\/(.+?)/.test(t.url),this._refTotalLength=t.filesize?t.filesize:null,this._totalLength=this._refTotalLength,this._fullRequestFlag=!1,this._currentRange=null,this._redirectedURL=null,this._speedNormalized=0,this._speedSampler=new _.default,this._speedNormalizeList=[64,128,256,384,512,768,1024,1536,2048,3072,4096],this._isEarlyEofReconnecting=!1,this._paused=!1,this._resumeFrom=0,this._onDataArrival=null,this._onSeeked=null,this._onError=null,this._onComplete=null,this._onRedirect=null,this._onRecoveredEarlyEof=null,this._selectSeekHandler(),this._selectLoader(),this._createLoader()}return l.prototype.destroy=function(){this._loader.isWorking()&&this._loader.abort(),this._loader.destroy(),this._loader=null,this._loaderClass=null,this._dataSource=null,this._stashBuffer=null,this._stashUsed=this._stashSize=this._bufferSize=this._stashByteStart=0,this._currentRange=null,this._speedSampler=null,this._isEarlyEofReconnecting=!1,this._onDataArrival=null,this._onSeeked=null,this._onError=null,this._onComplete=null,this._onRedirect=null,this._onRecoveredEarlyEof=null,this._extraData=null},l.prototype.isWorking=function(){return this._loader&&this._loader.isWorking()&&!this._paused},l.prototype.isPaused=function(){return this._paused},Object.defineProperty(l.prototype,"status",{get:function(){return this._loader.status},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"extraData",{get:function(){return this._extraData},set:function(t){this._extraData=t},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onDataArrival",{get:function(){return this._onDataArrival},set:function(t){this._onDataArrival=t},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onSeeked",{get:function(){return this._onSeeked},set:function(t){this._onSeeked=t},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onError",{get:function(){return this._onError},set:function(t){this._onError=t},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onComplete",{get:function(){return this._onComplete},set:function(t){this._onComplete=t},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onRedirect",{get:function(){return this._onRedirect},set:function(t){this._onRedirect=t},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"onRecoveredEarlyEof",{get:function(){return this._onRecoveredEarlyEof},set:function(t){this._onRecoveredEarlyEof=t},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"currentURL",{get:function(){return this._dataSource.url},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"hasRedirect",{get:function(){return this._redirectedURL!=null||this._dataSource.redirectedURL!=null},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"currentRedirectedURL",{get:function(){return this._redirectedURL||this._dataSource.redirectedURL},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"currentSpeed",{get:function(){return this._loaderClass===n.default?this._loader.currentSpeed:this._speedSampler.lastSecondKBps},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"loaderType",{get:function(){return this._loader.type},enumerable:!1,configurable:!0}),l.prototype._selectSeekHandler=function(){var t=this._config;if(t.seekType==="range")this._seekHandler=new h.default(this._config.rangeLoadZeroStart);else if(t.seekType==="param"){var p=t.seekParamStart||"bstart",v=t.seekParamEnd||"bend";this._seekHandler=new o.default(p,v)}else if(t.seekType==="custom"){if(typeof t.customSeekHandler!="function")throw new r.InvalidArgumentException("Custom seekType specified in config but invalid customSeekHandler!");this._seekHandler=new t.customSeekHandler}else throw new r.InvalidArgumentException("Invalid seekType in config: "+t.seekType)},l.prototype._selectLoader=function(){if(this._config.customLoader!=null)this._loaderClass=this._config.customLoader;else if(this._isWebSocketURL)this._loaderClass=f.default;else if(s.default.isSupported())this._loaderClass=s.default;else if(c.default.isSupported())this._loaderClass=c.default;else if(n.default.isSupported())this._loaderClass=n.default;else throw new r.RuntimeException("Your browser doesn't support xhr with arraybuffer responseType!")},l.prototype._createLoader=function(){this._loader=new this._loaderClass(this._seekHandler,this._config),this._loader.needStashBuffer===!1&&(this._enableStash=!1),this._loader.onContentLengthKnown=this._onContentLengthKnown.bind(this),this._loader.onURLRedirect=this._onURLRedirect.bind(this),this._loader.onDataArrival=this._onLoaderChunkArrival.bind(this),this._loader.onComplete=this._onLoaderComplete.bind(this),this._loader.onError=this._onLoaderError.bind(this)},l.prototype.open=function(t){this._currentRange={from:0,to:-1},t&&(this._currentRange.from=t),this._speedSampler.reset(),t||(this._fullRequestFlag=!0),this._loader.open(this._dataSource,Object.assign({},this._currentRange))},l.prototype.abort=function(){this._loader.abort(),this._paused&&(this._paused=!1,this._resumeFrom=0)},l.prototype.pause=function(){this.isWorking()&&(this._loader.abort(),this._stashUsed!==0?(this._resumeFrom=this._stashByteStart,this._currentRange.to=this._stashByteStart-1):this._resumeFrom=this._currentRange.to+1,this._stashUsed=0,this._stashByteStart=0,this._paused=!0)},l.prototype.resume=function(){if(this._paused){this._paused=!1;var t=this._resumeFrom;this._resumeFrom=0,this._internalSeek(t,!0)}},l.prototype.seek=function(t){this._paused=!1,this._stashUsed=0,this._stashByteStart=0,this._internalSeek(t,!0)},l.prototype._internalSeek=function(t,p){this._loader.isWorking()&&this._loader.abort(),this._flushStashBuffer(p),this._loader.destroy(),this._loader=null;var v={from:t,to:-1};this._currentRange={from:v.from,to:-1},this._speedSampler.reset(),this._stashSize=this._stashInitialSize,this._createLoader(),this._loader.open(this._dataSource,v),this._onSeeked&&this._onSeeked()},l.prototype.updateUrl=function(t){if(!t||typeof t!="string"||t.length===0)throw new r.InvalidArgumentException("Url must be a non-empty string!");this._dataSource.url=t},l.prototype._expandBuffer=function(t){for(var p=this._stashSize;p+1024*1024*1<t;)p*=2;if(p+=1024*1024*1,p!==this._bufferSize){var v=new ArrayBuffer(p);if(this._stashUsed>0){var g=new Uint8Array(this._stashBuffer,0,this._stashUsed),b=new Uint8Array(v,0,p);b.set(g,0)}this._stashBuffer=v,this._bufferSize=p}},l.prototype._normalizeSpeed=function(t){var p=this._speedNormalizeList,v=p.length-1,g=0,b=0,T=v;if(t<p[0])return p[0];for(;b<=T;){if(g=b+Math.floor((T-b)/2),g===v||t>=p[g]&&t<p[g+1])return p[g];p[g]<t?b=g+1:T=g-1}},l.prototype._adjustStashSize=function(t){var p=0;this._config.isLive||t<512?p=t:t>=512&&t<=1024?p=Math.floor(t*1.5):p=t*2,p>8192&&(p=8192);var v=p*1024+1024*1024*1;this._bufferSize<v&&this._expandBuffer(v),this._stashSize=p*1024},l.prototype._dispatchChunks=function(t,p){return this._currentRange.to=p+t.byteLength-1,this._onDataArrival(t,p)},l.prototype._onURLRedirect=function(t){this._redirectedURL=t,this._onRedirect&&this._onRedirect(t)},l.prototype._onContentLengthKnown=function(t){t&&this._fullRequestFlag&&(this._totalLength=t,this._fullRequestFlag=!1)},l.prototype._onLoaderChunkArrival=function(t,p,v){if(!this._onDataArrival)throw new r.IllegalStateException("IOController: No existing consumer (onDataArrival) callback!");if(!this._paused){this._isEarlyEofReconnecting&&(this._isEarlyEofReconnecting=!1,this._onRecoveredEarlyEof&&this._onRecoveredEarlyEof()),this._speedSampler.addBytes(t.byteLength);var g=this._speedSampler.lastSecondKBps;if(g!==0){var b=this._normalizeSpeed(g);this._speedNormalized!==b&&(this._speedNormalized=b,this._adjustStashSize(b))}if(this._enableStash)if(this._stashUsed===0&&this._stashByteStart===0&&(this._stashByteStart=p),this._stashUsed+t.byteLength<=this._stashSize){var S=new Uint8Array(this._stashBuffer,0,this._stashSize);S.set(new Uint8Array(t),this._stashUsed),this._stashUsed+=t.byteLength}else{var S=new Uint8Array(this._stashBuffer,0,this._bufferSize);if(this._stashUsed>0){var w=this._stashBuffer.slice(0,this._stashUsed),T=this._dispatchChunks(w,this._stashByteStart);if(T<w.byteLength){if(T>0){var R=new Uint8Array(w,T);S.set(R,0),this._stashUsed=R.byteLength,this._stashByteStart+=T}}else this._stashUsed=0,this._stashByteStart+=T;this._stashUsed+t.byteLength>this._bufferSize&&(this._expandBuffer(this._stashUsed+t.byteLength),S=new Uint8Array(this._stashBuffer,0,this._bufferSize)),S.set(new Uint8Array(t),this._stashUsed),this._stashUsed+=t.byteLength}else{var T=this._dispatchChunks(t,p);if(T<t.byteLength){var O=t.byteLength-T;O>this._bufferSize&&(this._expandBuffer(O),S=new Uint8Array(this._stashBuffer,0,this._bufferSize)),S.set(new Uint8Array(t,T),0),this._stashUsed+=O,this._stashByteStart=p+T}}}else if(this._stashUsed===0){var T=this._dispatchChunks(t,p);if(T<t.byteLength){var O=t.byteLength-T;O>this._bufferSize&&this._expandBuffer(O);var S=new Uint8Array(this._stashBuffer,0,this._bufferSize);S.set(new Uint8Array(t,T),0),this._stashUsed+=O,this._stashByteStart=p+T}}else{this._stashUsed+t.byteLength>this._bufferSize&&this._expandBuffer(this._stashUsed+t.byteLength);var S=new Uint8Array(this._stashBuffer,0,this._bufferSize);S.set(new Uint8Array(t),this._stashUsed),this._stashUsed+=t.byteLength;var T=this._dispatchChunks(this._stashBuffer.slice(0,this._stashUsed),this._stashByteStart);if(T<this._stashUsed&&T>0){var R=new Uint8Array(this._stashBuffer,T);S.set(R,0)}this._stashUsed-=T,this._stashByteStart+=T}}},l.prototype._flushStashBuffer=function(t){if(this._stashUsed>0){var p=this._stashBuffer.slice(0,this._stashUsed),v=this._dispatchChunks(p,this._stashByteStart),g=p.byteLength-v;if(v<p.byteLength)if(t)L.default.w(this.TAG,g+" bytes unconsumed data remain when flush buffer, dropped");else{if(v>0){var b=new Uint8Array(this._stashBuffer,0,this._bufferSize),T=new Uint8Array(p,v);b.set(T,0),this._stashUsed=T.byteLength,this._stashByteStart+=v}return 0}return this._stashUsed=0,this._stashByteStart=0,g}return 0},l.prototype._onLoaderComplete=function(t,p){this._flushStashBuffer(!0),this._onComplete&&this._onComplete(this._extraData)},l.prototype._onLoaderError=function(t,p){switch(L.default.e(this.TAG,"Loader error, code = "+p.code+", msg = "+p.msg),this._flushStashBuffer(!1),this._isEarlyEofReconnecting&&(this._isEarlyEofReconnecting=!1,t=a.LoaderErrors.UNRECOVERABLE_EARLY_EOF),t){case a.LoaderErrors.EARLY_EOF:{if(!this._config.isLive&&this._totalLength){var v=this._currentRange.to+1;v<this._totalLength&&(L.default.w(this.TAG,"Connection lost, trying reconnect..."),this._isEarlyEofReconnecting=!0,this._internalSeek(v,!1));return}t=a.LoaderErrors.UNRECOVERABLE_EARLY_EOF;break}case a.LoaderErrors.UNRECOVERABLE_EARLY_EOF:case a.LoaderErrors.CONNECTING_TIMEOUT:case a.LoaderErrors.HTTP_STATUS_CODE_INVALID:case a.LoaderErrors.EXCEPTION:break}if(this._onError)this._onError(t,p);else throw new r.RuntimeException("IOException: "+p.msg)},l}();y.default=u},"./src/io/loader.js":function(E,y,m){m.r(y),m.d(y,{LoaderStatus:function(){return _},LoaderErrors:function(){return a},BaseLoader:function(){return s}});var L=m("./src/utils/exception.js"),_={kIdle:0,kConnecting:1,kBuffering:2,kError:3,kComplete:4},a={OK:"OK",EXCEPTION:"Exception",HTTP_STATUS_CODE_INVALID:"HttpStatusCodeInvalid",CONNECTING_TIMEOUT:"ConnectingTimeout",EARLY_EOF:"EarlyEof",UNRECOVERABLE_EARLY_EOF:"UnrecoverableEarlyEof"},s=function(){function c(n){this._type=n||"undefined",this._status=_.kIdle,this._needStash=!1,this._onContentLengthKnown=null,this._onURLRedirect=null,this._onDataArrival=null,this._onError=null,this._onComplete=null}return c.prototype.destroy=function(){this._status=_.kIdle,this._onContentLengthKnown=null,this._onURLRedirect=null,this._onDataArrival=null,this._onError=null,this._onComplete=null},c.prototype.isWorking=function(){return this._status===_.kConnecting||this._status===_.kBuffering},Object.defineProperty(c.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"status",{get:function(){return this._status},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"needStashBuffer",{get:function(){return this._needStash},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"onContentLengthKnown",{get:function(){return this._onContentLengthKnown},set:function(n){this._onContentLengthKnown=n},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"onURLRedirect",{get:function(){return this._onURLRedirect},set:function(n){this._onURLRedirect=n},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"onDataArrival",{get:function(){return this._onDataArrival},set:function(n){this._onDataArrival=n},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"onError",{get:function(){return this._onError},set:function(n){this._onError=n},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"onComplete",{get:function(){return this._onComplete},set:function(n){this._onComplete=n},enumerable:!1,configurable:!0}),c.prototype.open=function(n,f){throw new L.NotImplementedException("Unimplemented abstract function!")},c.prototype.abort=function(){throw new L.NotImplementedException("Unimplemented abstract function!")},c}()},"./src/io/param-seek-handler.js":function(E,y,m){m.r(y);var L=function(){function _(a,s){this._startName=a,this._endName=s}return _.prototype.getConfig=function(a,s){var c=a;if(s.from!==0||s.to!==-1){var n=!0;c.indexOf("?")===-1&&(c+="?",n=!1),n&&(c+="&"),c+=this._startName+"="+s.from.toString(),s.to!==-1&&(c+="&"+this._endName+"="+s.to.toString())}return{url:c,headers:{}}},_.prototype.removeURLParameters=function(a){var s=a.split("?")[0],c=void 0,n=a.indexOf("?");n!==-1&&(c=a.substring(n+1));var f="";if(c!=null&&c.length>0)for(var h=c.split("&"),o=0;o<h.length;o++){var r=h[o].split("="),u=o>0;r[0]!==this._startName&&r[0]!==this._endName&&(u&&(f+="&"),f+=h[o])}return f.length===0?s:s+"?"+f},_}();y.default=L},"./src/io/range-seek-handler.js":function(E,y,m){m.r(y);var L=function(){function _(a){this._zeroStart=a||!1}return _.prototype.getConfig=function(a,s){var c={};if(s.from!==0||s.to!==-1){var n=void 0;s.to!==-1?n="bytes="+s.from.toString()+"-"+s.to.toString():n="bytes="+s.from.toString()+"-",c.Range=n}else this._zeroStart&&(c.Range="bytes=0-");return{url:a,headers:c}},_.prototype.removeURLParameters=function(a){return a},_}();y.default=L},"./src/io/speed-sampler.js":function(E,y,m){m.r(y);var L=function(){function _(){this._firstCheckpoint=0,this._lastCheckpoint=0,this._intervalBytes=0,this._totalBytes=0,this._lastSecondBytes=0,self.performance&&self.performance.now?this._now=self.performance.now.bind(self.performance):this._now=Date.now}return _.prototype.reset=function(){this._firstCheckpoint=this._lastCheckpoint=0,this._totalBytes=this._intervalBytes=0,this._lastSecondBytes=0},_.prototype.addBytes=function(a){this._firstCheckpoint===0?(this._firstCheckpoint=this._now(),this._lastCheckpoint=this._firstCheckpoint,this._intervalBytes+=a,this._totalBytes+=a):this._now()-this._lastCheckpoint<1e3?(this._intervalBytes+=a,this._totalBytes+=a):(this._lastSecondBytes=this._intervalBytes,this._intervalBytes=a,this._totalBytes+=a,this._lastCheckpoint=this._now())},Object.defineProperty(_.prototype,"currentKBps",{get:function(){this.addBytes(0);var a=(this._now()-this._lastCheckpoint)/1e3;return a==0&&(a=1),this._intervalBytes/a/1024},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"lastSecondKBps",{get:function(){return this.addBytes(0),this._lastSecondBytes!==0?this._lastSecondBytes/1024:this._now()-this._lastCheckpoint>=500?this.currentKBps:0},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"averageKBps",{get:function(){var a=(this._now()-this._firstCheckpoint)/1e3;return this._totalBytes/a/1024},enumerable:!1,configurable:!0}),_}();y.default=L},"./src/io/websocket-loader.js":function(E,y,m){m.r(y);var L=m("./src/io/loader.js"),_=m("./src/utils/exception.js"),a=function(){var c=function(n,f){return c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(h,o){h.__proto__=o}||function(h,o){for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(h[r]=o[r])},c(n,f)};return function(n,f){if(typeof f!="function"&&f!==null)throw new TypeError("Class extends value "+String(f)+" is not a constructor or null");c(n,f);function h(){this.constructor=n}n.prototype=f===null?Object.create(f):(h.prototype=f.prototype,new h)}}(),s=function(c){a(n,c);function n(){var f=c.call(this,"websocket-loader")||this;return f.TAG="WebSocketLoader",f._needStash=!0,f._ws=null,f._requestAbort=!1,f._receivedLength=0,f}return n.isSupported=function(){try{return typeof self.WebSocket<"u"}catch{return!1}},n.prototype.destroy=function(){this._ws&&this.abort(),c.prototype.destroy.call(this)},n.prototype.open=function(f){try{var h=this._ws=new self.WebSocket(f.url);h.binaryType="arraybuffer",h.onopen=this._onWebSocketOpen.bind(this),h.onclose=this._onWebSocketClose.bind(this),h.onmessage=this._onWebSocketMessage.bind(this),h.onerror=this._onWebSocketError.bind(this),this._status=L.LoaderStatus.kConnecting}catch(r){this._status=L.LoaderStatus.kError;var o={code:r.code,msg:r.message};if(this._onError)this._onError(L.LoaderErrors.EXCEPTION,o);else throw new _.RuntimeException(o.msg)}},n.prototype.abort=function(){var f=this._ws;f&&(f.readyState===0||f.readyState===1)&&(this._requestAbort=!0,f.close()),this._ws=null,this._status=L.LoaderStatus.kComplete},n.prototype._onWebSocketOpen=function(f){this._status=L.LoaderStatus.kBuffering},n.prototype._onWebSocketClose=function(f){if(this._requestAbort===!0){this._requestAbort=!1;return}this._status=L.LoaderStatus.kComplete,this._onComplete&&this._onComplete(0,this._receivedLength-1)},n.prototype._onWebSocketMessage=function(f){var h=this;if(f.data instanceof ArrayBuffer)this._dispatchArrayBuffer(f.data);else if(f.data instanceof Blob){var o=new FileReader;o.onload=function(){h._dispatchArrayBuffer(o.result)},o.readAsArrayBuffer(f.data)}else{this._status=L.LoaderStatus.kError;var r={code:-1,msg:"Unsupported WebSocket message type: "+f.data.constructor.name};if(this._onError)this._onError(L.LoaderErrors.EXCEPTION,r);else throw new _.RuntimeException(r.msg)}},n.prototype._dispatchArrayBuffer=function(f){var h=f,o=this._receivedLength;this._receivedLength+=h.byteLength,this._onDataArrival&&this._onDataArrival(h,o,this._receivedLength)},n.prototype._onWebSocketError=function(f){this._status=L.LoaderStatus.kError;var h={code:f.code,msg:f.message};if(this._onError)this._onError(L.LoaderErrors.EXCEPTION,h);else throw new _.RuntimeException(h.msg)},n}(L.BaseLoader);y.default=s},"./src/io/xhr-moz-chunked-loader.js":function(E,y,m){m.r(y);var L=m("./src/utils/logger.js"),_=m("./src/io/loader.js"),a=m("./src/utils/exception.js"),s=function(){var n=function(f,h){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,r){o.__proto__=r}||function(o,r){for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&(o[u]=r[u])},n(f,h)};return function(f,h){if(typeof h!="function"&&h!==null)throw new TypeError("Class extends value "+String(h)+" is not a constructor or null");n(f,h);function o(){this.constructor=f}f.prototype=h===null?Object.create(h):(o.prototype=h.prototype,new o)}}(),c=function(n){s(f,n);function f(h,o){var r=n.call(this,"xhr-moz-chunked-loader")||this;return r.TAG="MozChunkedLoader",r._seekHandler=h,r._config=o,r._needStash=!0,r._xhr=null,r._requestAbort=!1,r._contentLength=null,r._receivedLength=0,r}return f.isSupported=function(){try{var h=new XMLHttpRequest;return h.open("GET","https://example.com",!0),h.responseType="moz-chunked-arraybuffer",h.responseType==="moz-chunked-arraybuffer"}catch(o){return L.default.w("MozChunkedLoader",o.message),!1}},f.prototype.destroy=function(){this.isWorking()&&this.abort(),this._xhr&&(this._xhr.onreadystatechange=null,this._xhr.onprogress=null,this._xhr.onloadend=null,this._xhr.onerror=null,this._xhr=null),n.prototype.destroy.call(this)},f.prototype.open=function(h,o){this._dataSource=h,this._range=o;var r=h.url;this._config.reuseRedirectedURL&&h.redirectedURL!=null&&(r=h.redirectedURL);var u=this._seekHandler.getConfig(r,o);this._requestURL=u.url;var l=this._xhr=new XMLHttpRequest;if(l.open("GET",u.url,!0),l.responseType="moz-chunked-arraybuffer",l.onreadystatechange=this._onReadyStateChange.bind(this),l.onprogress=this._onProgress.bind(this),l.onloadend=this._onLoadEnd.bind(this),l.onerror=this._onXhrError.bind(this),h.withCredentials&&(l.withCredentials=!0),typeof u.headers=="object"){var t=u.headers;for(var p in t)t.hasOwnProperty(p)&&l.setRequestHeader(p,t[p])}if(typeof this._config.headers=="object"){var t=this._config.headers;for(var p in t)t.hasOwnProperty(p)&&l.setRequestHeader(p,t[p])}this._status=_.LoaderStatus.kConnecting,l.send()},f.prototype.abort=function(){this._requestAbort=!0,this._xhr&&this._xhr.abort(),this._status=_.LoaderStatus.kComplete},f.prototype._onReadyStateChange=function(h){var o=h.target;if(o.readyState===2){if(o.responseURL!=null&&o.responseURL!==this._requestURL&&this._onURLRedirect){var r=this._seekHandler.removeURLParameters(o.responseURL);this._onURLRedirect(r)}if(o.status!==0&&(o.status<200||o.status>299))if(this._status=_.LoaderStatus.kError,this._onError)this._onError(_.LoaderErrors.HTTP_STATUS_CODE_INVALID,{code:o.status,msg:o.statusText});else throw new a.RuntimeException("MozChunkedLoader: Http code invalid, "+o.status+" "+o.statusText);else this._status=_.LoaderStatus.kBuffering}},f.prototype._onProgress=function(h){if(this._status!==_.LoaderStatus.kError){this._contentLength===null&&h.total!==null&&h.total!==0&&(this._contentLength=h.total,this._onContentLengthKnown&&this._onContentLengthKnown(this._contentLength));var o=h.target.response,r=this._range.from+this._receivedLength;this._receivedLength+=o.byteLength,this._onDataArrival&&this._onDataArrival(o,r,this._receivedLength)}},f.prototype._onLoadEnd=function(h){if(this._requestAbort===!0){this._requestAbort=!1;return}else if(this._status===_.LoaderStatus.kError)return;this._status=_.LoaderStatus.kComplete,this._onComplete&&this._onComplete(this._range.from,this._range.from+this._receivedLength-1)},f.prototype._onXhrError=function(h){this._status=_.LoaderStatus.kError;var o=0,r=null;if(this._contentLength&&h.loaded<this._contentLength?(o=_.LoaderErrors.EARLY_EOF,r={code:-1,msg:"Moz-Chunked stream meet Early-Eof"}):(o=_.LoaderErrors.EXCEPTION,r={code:-1,msg:h.constructor.name+" "+h.type}),this._onError)this._onError(o,r);else throw new a.RuntimeException(r.msg)},f}(_.BaseLoader);y.default=c},"./src/io/xhr-range-loader.js":function(E,y,m){m.r(y);var L=m("./src/utils/logger.js"),_=m("./src/io/speed-sampler.js"),a=m("./src/io/loader.js"),s=m("./src/utils/exception.js"),c=function(){var f=function(h,o){return f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,u){r.__proto__=u}||function(r,u){for(var l in u)Object.prototype.hasOwnProperty.call(u,l)&&(r[l]=u[l])},f(h,o)};return function(h,o){if(typeof o!="function"&&o!==null)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");f(h,o);function r(){this.constructor=h}h.prototype=o===null?Object.create(o):(r.prototype=o.prototype,new r)}}(),n=function(f){c(h,f);function h(o,r){var u=f.call(this,"xhr-range-loader")||this;return u.TAG="RangeLoader",u._seekHandler=o,u._config=r,u._needStash=!1,u._chunkSizeKBList=[128,256,384,512,768,1024,1536,2048,3072,4096,5120,6144,7168,8192],u._currentChunkSizeKB=384,u._currentSpeedNormalized=0,u._zeroSpeedChunkCount=0,u._xhr=null,u._speedSampler=new _.default,u._requestAbort=!1,u._waitForTotalLength=!1,u._totalLengthReceived=!1,u._currentRequestURL=null,u._currentRedirectedURL=null,u._currentRequestRange=null,u._totalLength=null,u._contentLength=null,u._receivedLength=0,u._lastTimeLoaded=0,u}return h.isSupported=function(){try{var o=new XMLHttpRequest;return o.open("GET","https://example.com",!0),o.responseType="arraybuffer",o.responseType==="arraybuffer"}catch(r){return L.default.w("RangeLoader",r.message),!1}},h.prototype.destroy=function(){this.isWorking()&&this.abort(),this._xhr&&(this._xhr.onreadystatechange=null,this._xhr.onprogress=null,this._xhr.onload=null,this._xhr.onerror=null,this._xhr=null),f.prototype.destroy.call(this)},Object.defineProperty(h.prototype,"currentSpeed",{get:function(){return this._speedSampler.lastSecondKBps},enumerable:!1,configurable:!0}),h.prototype.open=function(o,r){this._dataSource=o,this._range=r,this._status=a.LoaderStatus.kConnecting;var u=!1;this._dataSource.filesize!=null&&this._dataSource.filesize!==0&&(u=!0,this._totalLength=this._dataSource.filesize),!this._totalLengthReceived&&!u?(this._waitForTotalLength=!0,this._internalOpen(this._dataSource,{from:0,to:-1})):this._openSubRange()},h.prototype._openSubRange=function(){var o=this._currentChunkSizeKB*1024,r=this._range.from+this._receivedLength,u=r+o;this._contentLength!=null&&u-this._range.from>=this._contentLength&&(u=this._range.from+this._contentLength-1),this._currentRequestRange={from:r,to:u},this._internalOpen(this._dataSource,this._currentRequestRange)},h.prototype._internalOpen=function(o,r){this._lastTimeLoaded=0;var u=o.url;this._config.reuseRedirectedURL&&(this._currentRedirectedURL!=null?u=this._currentRedirectedURL:o.redirectedURL!=null&&(u=o.redirectedURL));var l=this._seekHandler.getConfig(u,r);this._currentRequestURL=l.url;var t=this._xhr=new XMLHttpRequest;if(t.open("GET",l.url,!0),t.responseType="arraybuffer",t.onreadystatechange=this._onReadyStateChange.bind(this),t.onprogress=this._onProgress.bind(this),t.onload=this._onLoad.bind(this),t.onerror=this._onXhrError.bind(this),o.withCredentials&&(t.withCredentials=!0),typeof l.headers=="object"){var p=l.headers;for(var v in p)p.hasOwnProperty(v)&&t.setRequestHeader(v,p[v])}if(typeof this._config.headers=="object"){var p=this._config.headers;for(var v in p)p.hasOwnProperty(v)&&t.setRequestHeader(v,p[v])}t.send()},h.prototype.abort=function(){this._requestAbort=!0,this._internalAbort(),this._status=a.LoaderStatus.kComplete},h.prototype._internalAbort=function(){this._xhr&&(this._xhr.onreadystatechange=null,this._xhr.onprogress=null,this._xhr.onload=null,this._xhr.onerror=null,this._xhr.abort(),this._xhr=null)},h.prototype._onReadyStateChange=function(o){var r=o.target;if(r.readyState===2){if(r.responseURL!=null){var u=this._seekHandler.removeURLParameters(r.responseURL);r.responseURL!==this._currentRequestURL&&u!==this._currentRedirectedURL&&(this._currentRedirectedURL=u,this._onURLRedirect&&this._onURLRedirect(u))}if(r.status>=200&&r.status<=299){if(this._waitForTotalLength)return;this._status=a.LoaderStatus.kBuffering}else if(this._status=a.LoaderStatus.kError,this._onError)this._onError(a.LoaderErrors.HTTP_STATUS_CODE_INVALID,{code:r.status,msg:r.statusText});else throw new s.RuntimeException("RangeLoader: Http code invalid, "+r.status+" "+r.statusText)}},h.prototype._onProgress=function(o){if(this._status!==a.LoaderStatus.kError){if(this._contentLength===null){var r=!1;if(this._waitForTotalLength){this._waitForTotalLength=!1,this._totalLengthReceived=!0,r=!0;var u=o.total;this._internalAbort(),u!=null&u!==0&&(this._totalLength=u)}if(this._range.to===-1?this._contentLength=this._totalLength-this._range.from:this._contentLength=this._range.to-this._range.from+1,r){this._openSubRange();return}this._onContentLengthKnown&&this._onContentLengthKnown(this._contentLength)}var l=o.loaded-this._lastTimeLoaded;this._lastTimeLoaded=o.loaded,this._speedSampler.addBytes(l)}},h.prototype._normalizeSpeed=function(o){var r=this._chunkSizeKBList,u=r.length-1,l=0,t=0,p=u;if(o<r[0])return r[0];for(;t<=p;){if(l=t+Math.floor((p-t)/2),l===u||o>=r[l]&&o<r[l+1])return r[l];r[l]<o?t=l+1:p=l-1}},h.prototype._onLoad=function(o){if(this._status!==a.LoaderStatus.kError){if(this._waitForTotalLength){this._waitForTotalLength=!1;return}this._lastTimeLoaded=0;var r=this._speedSampler.lastSecondKBps;if(r===0&&(this._zeroSpeedChunkCount++,this._zeroSpeedChunkCount>=3&&(r=this._speedSampler.currentKBps)),r!==0){var u=this._normalizeSpeed(r);this._currentSpeedNormalized!==u&&(this._currentSpeedNormalized=u,this._currentChunkSizeKB=u)}var l=o.target.response,t=this._range.from+this._receivedLength;this._receivedLength+=l.byteLength;var p=!1;this._contentLength!=null&&this._receivedLength<this._contentLength?this._openSubRange():p=!0,this._onDataArrival&&this._onDataArrival(l,t,this._receivedLength),p&&(this._status=a.LoaderStatus.kComplete,this._onComplete&&this._onComplete(this._range.from,this._range.from+this._receivedLength-1))}},h.prototype._onXhrError=function(o){this._status=a.LoaderStatus.kError;var r=0,u=null;if(this._contentLength&&this._receivedLength>0&&this._receivedLength<this._contentLength?(r=a.LoaderErrors.EARLY_EOF,u={code:-1,msg:"RangeLoader meet Early-Eof"}):(r=a.LoaderErrors.EXCEPTION,u={code:-1,msg:o.constructor.name+" "+o.type}),this._onError)this._onError(r,u);else throw new s.RuntimeException(u.msg)},h}(a.BaseLoader);y.default=n},"./src/player/flv-player.js":function(E,y,m){m.r(y);var L=m("./node_modules/events/events.js"),_=m.n(L),a=m("./src/utils/logger.js"),s=m("./src/utils/browser.js"),c=m("./src/player/player-events.js"),n=m("./src/core/transmuxer.js"),f=m("./src/core/transmuxing-events.js"),h=m("./src/core/mse-controller.js"),o=m("./src/core/mse-events.js"),r=m("./src/player/player-errors.js"),u=m("./src/config.js"),l=m("./src/utils/exception.js"),t=function(){function p(v,g){if(this.TAG="FlvPlayer",this._type="FlvPlayer",this._emitter=new(_()),this._config=(0,u.createDefaultConfig)(),typeof g=="object"&&Object.assign(this._config,g),v.type.toLowerCase()!=="flv")throw new l.InvalidArgumentException("FlvPlayer requires an flv MediaDataSource input!");v.isLive===!0&&(this._config.isLive=!0),this.e={onvLoadedMetadata:this._onvLoadedMetadata.bind(this),onvSeeking:this._onvSeeking.bind(this),onvCanPlay:this._onvCanPlay.bind(this),onvStalled:this._onvStalled.bind(this),onvProgress:this._onvProgress.bind(this)},self.performance&&self.performance.now?this._now=self.performance.now.bind(self.performance):this._now=Date.now,this._pendingSeekTime=null,this._requestSetTime=!1,this._seekpointRecord=null,this._progressChecker=null,this._mediaDataSource=v,this._mediaElement=null,this._msectl=null,this._transmuxer=null,this._mseSourceOpened=!1,this._hasPendingLoad=!1,this._receivedCanPlay=!1,this._mediaInfo=null,this._statisticsInfo=null;var b=s.default.chrome&&(s.default.version.major<50||s.default.version.major===50&&s.default.version.build<2661);this._alwaysSeekKeyframe=!!(b||s.default.msedge||s.default.msie),this._alwaysSeekKeyframe&&(this._config.accurateSeek=!1)}return p.prototype.destroy=function(){this._progressChecker!=null&&(window.clearInterval(this._progressChecker),this._progressChecker=null),this._transmuxer&&this.unload(),this._mediaElement&&this.detachMediaElement(),this.e=null,this._mediaDataSource=null,this._emitter.removeAllListeners(),this._emitter=null},p.prototype.on=function(v,g){var b=this;v===c.default.MEDIA_INFO?this._mediaInfo!=null&&Promise.resolve().then(function(){b._emitter.emit(c.default.MEDIA_INFO,b.mediaInfo)}):v===c.default.STATISTICS_INFO&&this._statisticsInfo!=null&&Promise.resolve().then(function(){b._emitter.emit(c.default.STATISTICS_INFO,b.statisticsInfo)}),this._emitter.addListener(v,g)},p.prototype.off=function(v,g){this._emitter.removeListener(v,g)},p.prototype.attachMediaElement=function(v){var g=this;if(this._mediaElement=v,v.addEventListener("loadedmetadata",this.e.onvLoadedMetadata),v.addEventListener("seeking",this.e.onvSeeking),v.addEventListener("canplay",this.e.onvCanPlay),v.addEventListener("stalled",this.e.onvStalled),v.addEventListener("progress",this.e.onvProgress),this._msectl=new h.default(this._config),this._msectl.on(o.default.UPDATE_END,this._onmseUpdateEnd.bind(this)),this._msectl.on(o.default.BUFFER_FULL,this._onmseBufferFull.bind(this)),this._msectl.on(o.default.SOURCE_OPEN,function(){g._mseSourceOpened=!0,g._hasPendingLoad&&(g._hasPendingLoad=!1,g.load())}),this._msectl.on(o.default.ERROR,function(b){g._emitter.emit(c.default.ERROR,r.ErrorTypes.MEDIA_ERROR,r.ErrorDetails.MEDIA_MSE_ERROR,b)}),this._msectl.attachMediaElement(v),this._pendingSeekTime!=null)try{v.currentTime=this._pendingSeekTime,this._pendingSeekTime=null}catch{}},p.prototype.detachMediaElement=function(){this._mediaElement&&(this._msectl.detachMediaElement(),this._mediaElement.removeEventListener("loadedmetadata",this.e.onvLoadedMetadata),this._mediaElement.removeEventListener("seeking",this.e.onvSeeking),this._mediaElement.removeEventListener("canplay",this.e.onvCanPlay),this._mediaElement.removeEventListener("stalled",this.e.onvStalled),this._mediaElement.removeEventListener("progress",this.e.onvProgress),this._mediaElement=null),this._msectl&&(this._msectl.destroy(),this._msectl=null)},p.prototype.load=function(){var v=this;if(!this._mediaElement)throw new l.IllegalStateException("HTMLMediaElement must be attached before load()!");if(this._transmuxer)throw new l.IllegalStateException("FlvPlayer.load() has been called, please call unload() first!");if(!this._hasPendingLoad){if(this._config.deferLoadAfterSourceOpen&&this._mseSourceOpened===!1){this._hasPendingLoad=!0;return}this._mediaElement.readyState>0&&(this._requestSetTime=!0,this._mediaElement.currentTime=0),this._transmuxer=new n.default(this._mediaDataSource,this._config),this._transmuxer.on(f.default.INIT_SEGMENT,function(g,b){v._msectl.appendInitSegment(b)}),this._transmuxer.on(f.default.MEDIA_SEGMENT,function(g,b){if(v._msectl.appendMediaSegment(b),v._config.lazyLoad&&!v._config.isLive){var T=v._mediaElement.currentTime;b.info.endDts>=(T+v._config.lazyLoadMaxDuration)*1e3&&v._progressChecker==null&&(a.default.v(v.TAG,"Maximum buffering duration exceeded, suspend transmuxing task"),v._suspendTransmuxer())}}),this._transmuxer.on(f.default.LOADING_COMPLETE,function(){v._msectl.endOfStream(),v._emitter.emit(c.default.LOADING_COMPLETE)}),this._transmuxer.on(f.default.RECOVERED_EARLY_EOF,function(){v._emitter.emit(c.default.RECOVERED_EARLY_EOF)}),this._transmuxer.on(f.default.IO_ERROR,function(g,b){v._emitter.emit(c.default.ERROR,r.ErrorTypes.NETWORK_ERROR,g,b)}),this._transmuxer.on(f.default.DEMUX_ERROR,function(g,b){v._emitter.emit(c.default.ERROR,r.ErrorTypes.MEDIA_ERROR,g,{code:-1,msg:b})}),this._transmuxer.on(f.default.MEDIA_INFO,function(g){v._mediaInfo=g,v._emitter.emit(c.default.MEDIA_INFO,Object.assign({},g))}),this._transmuxer.on(f.default.METADATA_ARRIVED,function(g){v._emitter.emit(c.default.METADATA_ARRIVED,g)}),this._transmuxer.on(f.default.SCRIPTDATA_ARRIVED,function(g){v._emitter.emit(c.default.SCRIPTDATA_ARRIVED,g)}),this._transmuxer.on(f.default.STATISTICS_INFO,function(g){v._statisticsInfo=v._fillStatisticsInfo(g),v._emitter.emit(c.default.STATISTICS_INFO,Object.assign({},v._statisticsInfo))}),this._transmuxer.on(f.default.RECOMMEND_SEEKPOINT,function(g){v._mediaElement&&!v._config.accurateSeek&&(v._requestSetTime=!0,v._mediaElement.currentTime=g/1e3)}),this._transmuxer.open()}},p.prototype.unload=function(){this._mediaElement&&this._mediaElement.pause(),this._msectl&&this._msectl.seek(0),this._transmuxer&&(this._transmuxer.close(),this._transmuxer.destroy(),this._transmuxer=null)},p.prototype.play=function(){return this._mediaElement.play()},p.prototype.pause=function(){this._mediaElement.pause()},Object.defineProperty(p.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"buffered",{get:function(){return this._mediaElement.buffered},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"duration",{get:function(){return this._mediaElement.duration},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"volume",{get:function(){return this._mediaElement.volume},set:function(v){this._mediaElement.volume=v},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"muted",{get:function(){return this._mediaElement.muted},set:function(v){this._mediaElement.muted=v},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"currentTime",{get:function(){return this._mediaElement?this._mediaElement.currentTime:0},set:function(v){this._mediaElement?this._internalSeek(v):this._pendingSeekTime=v},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"mediaInfo",{get:function(){return Object.assign({},this._mediaInfo)},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"statisticsInfo",{get:function(){return this._statisticsInfo==null&&(this._statisticsInfo={}),this._statisticsInfo=this._fillStatisticsInfo(this._statisticsInfo),Object.assign({},this._statisticsInfo)},enumerable:!1,configurable:!0}),p.prototype._fillStatisticsInfo=function(v){if(v.playerType=this._type,!(this._mediaElement instanceof HTMLVideoElement))return v;var g=!0,b=0,T=0;if(this._mediaElement.getVideoPlaybackQuality){var O=this._mediaElement.getVideoPlaybackQuality();b=O.totalVideoFrames,T=O.droppedVideoFrames}else this._mediaElement.webkitDecodedFrameCount!=null?(b=this._mediaElement.webkitDecodedFrameCount,T=this._mediaElement.webkitDroppedFrameCount):g=!1;return g&&(v.decodedFrames=b,v.droppedFrames=T),v},p.prototype._onmseUpdateEnd=function(){if(!(!this._config.lazyLoad||this._config.isLive)){for(var v=this._mediaElement.buffered,g=this._mediaElement.currentTime,b=0,T=0;T<v.length;T++){var O=v.start(T),S=v.end(T);if(O<=g&&g<S){b=S;break}}b>=g+this._config.lazyLoadMaxDuration&&this._progressChecker==null&&(a.default.v(this.TAG,"Maximum buffering duration exceeded, suspend transmuxing task"),this._suspendTransmuxer())}},p.prototype._onmseBufferFull=function(){a.default.v(this.TAG,"MSE SourceBuffer is full, suspend transmuxing task"),this._progressChecker==null&&this._suspendTransmuxer()},p.prototype._suspendTransmuxer=function(){this._transmuxer&&(this._transmuxer.pause(),this._progressChecker==null&&(this._progressChecker=window.setInterval(this._checkProgressAndResume.bind(this),1e3)))},p.prototype._checkProgressAndResume=function(){for(var v=this._mediaElement.currentTime,g=this._mediaElement.buffered,b=!1,T=0;T<g.length;T++){var O=g.start(T),S=g.end(T);if(v>=O&&v<S){v>=S-this._config.lazyLoadRecoverDuration&&(b=!0);break}}b&&(window.clearInterval(this._progressChecker),this._progressChecker=null,b&&(a.default.v(this.TAG,"Continue loading from paused position"),this._transmuxer.resume()))},p.prototype._isTimepointBuffered=function(v){for(var g=this._mediaElement.buffered,b=0;b<g.length;b++){var T=g.start(b),O=g.end(b);if(v>=T&&v<O)return!0}return!1},p.prototype._internalSeek=function(v){var g=this._isTimepointBuffered(v),b=!1,T=0;if(v<1&&this._mediaElement.buffered.length>0){var O=this._mediaElement.buffered.start(0);(O<1&&v<O||s.default.safari)&&(b=!0,T=s.default.safari?.1:O)}if(b)this._requestSetTime=!0,this._mediaElement.currentTime=T;else if(g){if(!this._alwaysSeekKeyframe)this._requestSetTime=!0,this._mediaElement.currentTime=v;else{var S=this._msectl.getNearestKeyframe(Math.floor(v*1e3));this._requestSetTime=!0,S!=null?this._mediaElement.currentTime=S.dts/1e3:this._mediaElement.currentTime=v}this._progressChecker!=null&&this._checkProgressAndResume()}else this._progressChecker!=null&&(window.clearInterval(this._progressChecker),this._progressChecker=null),this._msectl.seek(v),this._transmuxer.seek(Math.floor(v*1e3)),this._config.accurateSeek&&(this._requestSetTime=!0,this._mediaElement.currentTime=v)},p.prototype._checkAndApplyUnbufferedSeekpoint=function(){if(this._seekpointRecord)if(this._seekpointRecord.recordTime<=this._now()-100){var v=this._mediaElement.currentTime;this._seekpointRecord=null,this._isTimepointBuffered(v)||(this._progressChecker!=null&&(window.clearTimeout(this._progressChecker),this._progressChecker=null),this._msectl.seek(v),this._transmuxer.seek(Math.floor(v*1e3)),this._config.accurateSeek&&(this._requestSetTime=!0,this._mediaElement.currentTime=v))}else window.setTimeout(this._checkAndApplyUnbufferedSeekpoint.bind(this),50)},p.prototype._checkAndResumeStuckPlayback=function(v){var g=this._mediaElement;if(v||!this._receivedCanPlay||g.readyState<2){var b=g.buffered;b.length>0&&g.currentTime<b.start(0)&&(a.default.w(this.TAG,"Playback seems stuck at "+g.currentTime+", seek to "+b.start(0)),this._requestSetTime=!0,this._mediaElement.currentTime=b.start(0),this._mediaElement.removeEventListener("progress",this.e.onvProgress))}else this._mediaElement.removeEventListener("progress",this.e.onvProgress)},p.prototype._onvLoadedMetadata=function(v){this._pendingSeekTime!=null&&(this._mediaElement.currentTime=this._pendingSeekTime,this._pendingSeekTime=null)},p.prototype._onvSeeking=function(v){var g=this._mediaElement.currentTime,b=this._mediaElement.buffered;if(this._requestSetTime){this._requestSetTime=!1;return}if(g<1&&b.length>0){var T=b.start(0);if(T<1&&g<T||s.default.safari){this._requestSetTime=!0,this._mediaElement.currentTime=s.default.safari?.1:T;return}}if(this._isTimepointBuffered(g)){if(this._alwaysSeekKeyframe){var O=this._msectl.getNearestKeyframe(Math.floor(g*1e3));O!=null&&(this._requestSetTime=!0,this._mediaElement.currentTime=O.dts/1e3)}this._progressChecker!=null&&this._checkProgressAndResume();return}this._seekpointRecord={seekPoint:g,recordTime:this._now()},window.setTimeout(this._checkAndApplyUnbufferedSeekpoint.bind(this),50)},p.prototype._onvCanPlay=function(v){this._receivedCanPlay=!0,this._mediaElement.removeEventListener("canplay",this.e.onvCanPlay)},p.prototype._onvStalled=function(v){this._checkAndResumeStuckPlayback(!0)},p.prototype._onvProgress=function(v){this._checkAndResumeStuckPlayback()},p}();y.default=t},"./src/player/native-player.js":function(E,y,m){m.r(y);var L=m("./node_modules/events/events.js"),_=m.n(L),a=m("./src/player/player-events.js"),s=m("./src/config.js"),c=m("./src/utils/exception.js"),n=function(){function f(h,o){if(this.TAG="NativePlayer",this._type="NativePlayer",this._emitter=new(_()),this._config=(0,s.createDefaultConfig)(),typeof o=="object"&&Object.assign(this._config,o),h.type.toLowerCase()==="flv")throw new c.InvalidArgumentException("NativePlayer does't support flv MediaDataSource input!");if(h.hasOwnProperty("segments"))throw new c.InvalidArgumentException("NativePlayer("+h.type+") doesn't support multipart playback!");this.e={onvLoadedMetadata:this._onvLoadedMetadata.bind(this)},this._pendingSeekTime=null,this._statisticsReporter=null,this._mediaDataSource=h,this._mediaElement=null}return f.prototype.destroy=function(){this._mediaElement&&(this.unload(),this.detachMediaElement()),this.e=null,this._mediaDataSource=null,this._emitter.removeAllListeners(),this._emitter=null},f.prototype.on=function(h,o){var r=this;h===a.default.MEDIA_INFO?this._mediaElement!=null&&this._mediaElement.readyState!==0&&Promise.resolve().then(function(){r._emitter.emit(a.default.MEDIA_INFO,r.mediaInfo)}):h===a.default.STATISTICS_INFO&&this._mediaElement!=null&&this._mediaElement.readyState!==0&&Promise.resolve().then(function(){r._emitter.emit(a.default.STATISTICS_INFO,r.statisticsInfo)}),this._emitter.addListener(h,o)},f.prototype.off=function(h,o){this._emitter.removeListener(h,o)},f.prototype.attachMediaElement=function(h){if(this._mediaElement=h,h.addEventListener("loadedmetadata",this.e.onvLoadedMetadata),this._pendingSeekTime!=null)try{h.currentTime=this._pendingSeekTime,this._pendingSeekTime=null}catch{}},f.prototype.detachMediaElement=function(){this._mediaElement&&(this._mediaElement.src="",this._mediaElement.removeAttribute("src"),this._mediaElement.removeEventListener("loadedmetadata",this.e.onvLoadedMetadata),this._mediaElement=null),this._statisticsReporter!=null&&(window.clearInterval(this._statisticsReporter),this._statisticsReporter=null)},f.prototype.load=function(){if(!this._mediaElement)throw new c.IllegalStateException("HTMLMediaElement must be attached before load()!");this._mediaElement.src=this._mediaDataSource.url,this._mediaElement.readyState>0&&(this._mediaElement.currentTime=0),this._mediaElement.preload="auto",this._mediaElement.load(),this._statisticsReporter=window.setInterval(this._reportStatisticsInfo.bind(this),this._config.statisticsInfoReportInterval)},f.prototype.unload=function(){this._mediaElement&&(this._mediaElement.src="",this._mediaElement.removeAttribute("src")),this._statisticsReporter!=null&&(window.clearInterval(this._statisticsReporter),this._statisticsReporter=null)},f.prototype.play=function(){return this._mediaElement.play()},f.prototype.pause=function(){this._mediaElement.pause()},Object.defineProperty(f.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"buffered",{get:function(){return this._mediaElement.buffered},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"duration",{get:function(){return this._mediaElement.duration},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"volume",{get:function(){return this._mediaElement.volume},set:function(h){this._mediaElement.volume=h},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"muted",{get:function(){return this._mediaElement.muted},set:function(h){this._mediaElement.muted=h},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"currentTime",{get:function(){return this._mediaElement?this._mediaElement.currentTime:0},set:function(h){this._mediaElement?this._mediaElement.currentTime=h:this._pendingSeekTime=h},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"mediaInfo",{get:function(){var h=this._mediaElement instanceof HTMLAudioElement?"audio/":"video/",o={mimeType:h+this._mediaDataSource.type};return this._mediaElement&&(o.duration=Math.floor(this._mediaElement.duration*1e3),this._mediaElement instanceof HTMLVideoElement&&(o.width=this._mediaElement.videoWidth,o.height=this._mediaElement.videoHeight)),o},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"statisticsInfo",{get:function(){var h={playerType:this._type,url:this._mediaDataSource.url};if(!(this._mediaElement instanceof HTMLVideoElement))return h;var o=!0,r=0,u=0;if(this._mediaElement.getVideoPlaybackQuality){var l=this._mediaElement.getVideoPlaybackQuality();r=l.totalVideoFrames,u=l.droppedVideoFrames}else this._mediaElement.webkitDecodedFrameCount!=null?(r=this._mediaElement.webkitDecodedFrameCount,u=this._mediaElement.webkitDroppedFrameCount):o=!1;return o&&(h.decodedFrames=r,h.droppedFrames=u),h},enumerable:!1,configurable:!0}),f.prototype._onvLoadedMetadata=function(h){this._pendingSeekTime!=null&&(this._mediaElement.currentTime=this._pendingSeekTime,this._pendingSeekTime=null),this._emitter.emit(a.default.MEDIA_INFO,this.mediaInfo)},f.prototype._reportStatisticsInfo=function(){this._emitter.emit(a.default.STATISTICS_INFO,this.statisticsInfo)},f}();y.default=n},"./src/player/player-errors.js":function(E,y,m){m.r(y),m.d(y,{ErrorTypes:function(){return a},ErrorDetails:function(){return s}});var L=m("./src/io/loader.js"),_=m("./src/demux/demux-errors.js"),a={NETWORK_ERROR:"NetworkError",MEDIA_ERROR:"MediaError",OTHER_ERROR:"OtherError"},s={NETWORK_EXCEPTION:L.LoaderErrors.EXCEPTION,NETWORK_STATUS_CODE_INVALID:L.LoaderErrors.HTTP_STATUS_CODE_INVALID,NETWORK_TIMEOUT:L.LoaderErrors.CONNECTING_TIMEOUT,NETWORK_UNRECOVERABLE_EARLY_EOF:L.LoaderErrors.UNRECOVERABLE_EARLY_EOF,MEDIA_MSE_ERROR:"MediaMSEError",MEDIA_FORMAT_ERROR:_.default.FORMAT_ERROR,MEDIA_FORMAT_UNSUPPORTED:_.default.FORMAT_UNSUPPORTED,MEDIA_CODEC_UNSUPPORTED:_.default.CODEC_UNSUPPORTED}},"./src/player/player-events.js":function(E,y,m){m.r(y);var L={ERROR:"error",LOADING_COMPLETE:"loading_complete",RECOVERED_EARLY_EOF:"recovered_early_eof",MEDIA_INFO:"media_info",METADATA_ARRIVED:"metadata_arrived",SCRIPTDATA_ARRIVED:"scriptdata_arrived",STATISTICS_INFO:"statistics_info"};y.default=L},"./src/remux/aac-silent.js":function(E,y,m){m.r(y);var L=function(){function _(){}return _.getSilentFrame=function(a,s){if(a==="mp4a.40.2"){if(s===1)return new Uint8Array([0,200,0,128,35,128]);if(s===2)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(s===3)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(s===4)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(s===5)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(s===6)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(s===1)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(s===2)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(s===3)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}return null},_}();y.default=L},"./src/remux/mp4-generator.js":function(E,y,m){m.r(y);var L=function(){function _(){}return _.init=function(){_.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],".mp3":[]};for(var a in _.types)_.types.hasOwnProperty(a)&&(_.types[a]=[a.charCodeAt(0),a.charCodeAt(1),a.charCodeAt(2),a.charCodeAt(3)]);var s=_.constants={};s.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),s.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),s.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),s.STSC=s.STCO=s.STTS,s.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),s.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),s.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),s.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),s.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),s.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])},_.box=function(a){for(var s=8,c=null,n=Array.prototype.slice.call(arguments,1),f=n.length,h=0;h<f;h++)s+=n[h].byteLength;c=new Uint8Array(s),c[0]=s>>>24&255,c[1]=s>>>16&255,c[2]=s>>>8&255,c[3]=s&255,c.set(a,4);for(var o=8,h=0;h<f;h++)c.set(n[h],o),o+=n[h].byteLength;return c},_.generateInitSegment=function(a){var s=_.box(_.types.ftyp,_.constants.FTYP),c=_.moov(a),n=new Uint8Array(s.byteLength+c.byteLength);return n.set(s,0),n.set(c,s.byteLength),n},_.moov=function(a){var s=_.mvhd(a.timescale,a.duration),c=_.trak(a),n=_.mvex(a);return _.box(_.types.moov,s,c,n)},_.mvhd=function(a,s){return _.box(_.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,a>>>24&255,a>>>16&255,a>>>8&255,a&255,s>>>24&255,s>>>16&255,s>>>8&255,s&255,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))},_.trak=function(a){return _.box(_.types.trak,_.tkhd(a),_.mdia(a))},_.tkhd=function(a){var s=a.id,c=a.duration,n=a.presentWidth,f=a.presentHeight;return _.box(_.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,s>>>24&255,s>>>16&255,s>>>8&255,s&255,0,0,0,0,c>>>24&255,c>>>16&255,c>>>8&255,c&255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,n>>>8&255,n&255,0,0,f>>>8&255,f&255,0,0]))},_.mdia=function(a){return _.box(_.types.mdia,_.mdhd(a),_.hdlr(a),_.minf(a))},_.mdhd=function(a){var s=a.timescale,c=a.duration;return _.box(_.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,s>>>24&255,s>>>16&255,s>>>8&255,s&255,c>>>24&255,c>>>16&255,c>>>8&255,c&255,85,196,0,0]))},_.hdlr=function(a){var s=null;return a.type==="audio"?s=_.constants.HDLR_AUDIO:s=_.constants.HDLR_VIDEO,_.box(_.types.hdlr,s)},_.minf=function(a){var s=null;return a.type==="audio"?s=_.box(_.types.smhd,_.constants.SMHD):s=_.box(_.types.vmhd,_.constants.VMHD),_.box(_.types.minf,s,_.dinf(),_.stbl(a))},_.dinf=function(){var a=_.box(_.types.dinf,_.box(_.types.dref,_.constants.DREF));return a},_.stbl=function(a){var s=_.box(_.types.stbl,_.stsd(a),_.box(_.types.stts,_.constants.STTS),_.box(_.types.stsc,_.constants.STSC),_.box(_.types.stsz,_.constants.STSZ),_.box(_.types.stco,_.constants.STCO));return s},_.stsd=function(a){return a.type==="audio"?a.codec==="mp3"?_.box(_.types.stsd,_.constants.STSD_PREFIX,_.mp3(a)):_.box(_.types.stsd,_.constants.STSD_PREFIX,_.mp4a(a)):_.box(_.types.stsd,_.constants.STSD_PREFIX,_.avc1(a))},_.mp3=function(a){var s=a.channelCount,c=a.audioSampleRate,n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,s,0,16,0,0,0,0,c>>>8&255,c&255,0,0]);return _.box(_.types[".mp3"],n)},_.mp4a=function(a){var s=a.channelCount,c=a.audioSampleRate,n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,s,0,16,0,0,0,0,c>>>8&255,c&255,0,0]);return _.box(_.types.mp4a,n,_.esds(a))},_.esds=function(a){var s=a.config||[],c=s.length,n=new Uint8Array([0,0,0,0,3,23+c,0,1,0,4,15+c,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([c]).concat(s).concat([6,1,2]));return _.box(_.types.esds,n)},_.avc1=function(a){var s=a.avcc,c=a.codecWidth,n=a.codecHeight,f=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,c>>>8&255,c&255,n>>>8&255,n&255,0,72,0,0,0,72,0,0,0,0,0,0,0,1,10,120,113,113,47,102,108,118,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return _.box(_.types.avc1,f,_.box(_.types.avcC,s))},_.mvex=function(a){return _.box(_.types.mvex,_.trex(a))},_.trex=function(a){var s=a.id,c=new Uint8Array([0,0,0,0,s>>>24&255,s>>>16&255,s>>>8&255,s&255,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return _.box(_.types.trex,c)},_.moof=function(a,s){return _.box(_.types.moof,_.mfhd(a.sequenceNumber),_.traf(a,s))},_.mfhd=function(a){var s=new Uint8Array([0,0,0,0,a>>>24&255,a>>>16&255,a>>>8&255,a&255]);return _.box(_.types.mfhd,s)},_.traf=function(a,s){var c=a.id,n=_.box(_.types.tfhd,new Uint8Array([0,0,0,0,c>>>24&255,c>>>16&255,c>>>8&255,c&255])),f=_.box(_.types.tfdt,new Uint8Array([0,0,0,0,s>>>24&255,s>>>16&255,s>>>8&255,s&255])),h=_.sdtp(a),o=_.trun(a,h.byteLength+16+16+8+16+8+8);return _.box(_.types.traf,n,f,o,h)},_.sdtp=function(a){for(var s=a.samples||[],c=s.length,n=new Uint8Array(4+c),f=0;f<c;f++){var h=s[f].flags;n[f+4]=h.isLeading<<6|h.dependsOn<<4|h.isDependedOn<<2|h.hasRedundancy}return _.box(_.types.sdtp,n)},_.trun=function(a,s){var c=a.samples||[],n=c.length,f=12+16*n,h=new Uint8Array(f);s+=8+f,h.set([0,0,15,1,n>>>24&255,n>>>16&255,n>>>8&255,n&255,s>>>24&255,s>>>16&255,s>>>8&255,s&255],0);for(var o=0;o<n;o++){var r=c[o].duration,u=c[o].size,l=c[o].flags,t=c[o].cts;h.set([r>>>24&255,r>>>16&255,r>>>8&255,r&255,u>>>24&255,u>>>16&255,u>>>8&255,u&255,l.isLeading<<2|l.dependsOn,l.isDependedOn<<6|l.hasRedundancy<<4|l.isNonSync,0,0,t>>>24&255,t>>>16&255,t>>>8&255,t&255],12+16*o)}return _.box(_.types.trun,h)},_.mdat=function(a){return _.box(_.types.mdat,a)},_}();L.init(),y.default=L},"./src/remux/mp4-remuxer.js":function(E,y,m){m.r(y);var L=m("./src/utils/logger.js"),_=m("./src/remux/mp4-generator.js"),a=m("./src/remux/aac-silent.js"),s=m("./src/utils/browser.js"),c=m("./src/core/media-segment-info.js"),n=m("./src/utils/exception.js"),f=function(){function h(o){this.TAG="MP4Remuxer",this._config=o,this._isLive=o.isLive===!0,this._dtsBase=-1,this._dtsBaseInited=!1,this._audioDtsBase=1/0,this._videoDtsBase=1/0,this._audioNextDts=void 0,this._videoNextDts=void 0,this._audioStashedLastSample=null,this._videoStashedLastSample=null,this._audioMeta=null,this._videoMeta=null,this._audioSegmentInfoList=new c.MediaSegmentInfoList("audio"),this._videoSegmentInfoList=new c.MediaSegmentInfoList("video"),this._onInitSegment=null,this._onMediaSegment=null,this._forceFirstIDR=!!(s.default.chrome&&(s.default.version.major<50||s.default.version.major===50&&s.default.version.build<2661)),this._fillSilentAfterSeek=s.default.msedge||s.default.msie,this._mp3UseMpegAudio=!s.default.firefox,this._fillAudioTimestampGap=this._config.fixAudioTimestampGap}return h.prototype.destroy=function(){this._dtsBase=-1,this._dtsBaseInited=!1,this._audioMeta=null,this._videoMeta=null,this._audioSegmentInfoList.clear(),this._audioSegmentInfoList=null,this._videoSegmentInfoList.clear(),this._videoSegmentInfoList=null,this._onInitSegment=null,this._onMediaSegment=null},h.prototype.bindDataSource=function(o){return o.onDataAvailable=this.remux.bind(this),o.onTrackMetadata=this._onTrackMetadataReceived.bind(this),this},Object.defineProperty(h.prototype,"onInitSegment",{get:function(){return this._onInitSegment},set:function(o){this._onInitSegment=o},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"onMediaSegment",{get:function(){return this._onMediaSegment},set:function(o){this._onMediaSegment=o},enumerable:!1,configurable:!0}),h.prototype.insertDiscontinuity=function(){this._audioNextDts=this._videoNextDts=void 0},h.prototype.seek=function(o){this._audioStashedLastSample=null,this._videoStashedLastSample=null,this._videoSegmentInfoList.clear(),this._audioSegmentInfoList.clear()},h.prototype.remux=function(o,r){if(!this._onMediaSegment)throw new n.IllegalStateException("MP4Remuxer: onMediaSegment callback must be specificed!");this._dtsBaseInited||this._calculateDtsBase(o,r),this._remuxVideo(r),this._remuxAudio(o)},h.prototype._onTrackMetadataReceived=function(o,r){var u=null,l="mp4",t=r.codec;if(o==="audio")this._audioMeta=r,r.codec==="mp3"&&this._mp3UseMpegAudio?(l="mpeg",t="",u=new Uint8Array):u=_.default.generateInitSegment(r);else if(o==="video")this._videoMeta=r,u=_.default.generateInitSegment(r);else return;if(!this._onInitSegment)throw new n.IllegalStateException("MP4Remuxer: onInitSegment callback must be specified!");this._onInitSegment(o,{type:o,data:u.buffer,codec:t,container:o+"/"+l,mediaDuration:r.duration})},h.prototype._calculateDtsBase=function(o,r){this._dtsBaseInited||(o.samples&&o.samples.length&&(this._audioDtsBase=o.samples[0].dts),r.samples&&r.samples.length&&(this._videoDtsBase=r.samples[0].dts),this._dtsBase=Math.min(this._audioDtsBase,this._videoDtsBase),this._dtsBaseInited=!0)},h.prototype.flushStashedSamples=function(){var o=this._videoStashedLastSample,r=this._audioStashedLastSample,u={type:"video",id:1,sequenceNumber:0,samples:[],length:0};o!=null&&(u.samples.push(o),u.length=o.length);var l={type:"audio",id:2,sequenceNumber:0,samples:[],length:0};r!=null&&(l.samples.push(r),l.length=r.length),this._videoStashedLastSample=null,this._audioStashedLastSample=null,this._remuxVideo(u,!0),this._remuxAudio(l,!0)},h.prototype._remuxAudio=function(o,r){if(this._audioMeta!=null){var u=o,l=u.samples,t=void 0,p=-1,v=-1,g=this._audioMeta.refSampleDuration,b=this._audioMeta.codec==="mp3"&&this._mp3UseMpegAudio,T=this._dtsBaseInited&&this._audioNextDts===void 0,O=!1;if(!(!l||l.length===0)&&!(l.length===1&&!r)){var S=0,R=null,w=0;b?(S=0,w=u.length):(S=8,w=8+u.length);var P=null;if(l.length>1&&(P=l.pop(),w-=P.length),this._audioStashedLastSample!=null){var D=this._audioStashedLastSample;this._audioStashedLastSample=null,l.unshift(D),w+=D.length}P!=null&&(this._audioStashedLastSample=P);var M=l[0].dts-this._dtsBase;if(this._audioNextDts)t=M-this._audioNextDts;else if(this._audioSegmentInfoList.isEmpty())t=0,this._fillSilentAfterSeek&&!this._videoSegmentInfoList.isEmpty()&&this._audioMeta.originalCodec!=="mp3"&&(O=!0);else{var G=this._audioSegmentInfoList.getLastSampleBefore(M);if(G!=null){var z=M-(G.originalDts+G.duration);z<=3&&(z=0);var j=G.dts+G.duration+z;t=M-j}else t=0}if(O){var Z=M-t,ee=this._videoSegmentInfoList.getLastSegmentBefore(M);if(ee!=null&&ee.beginDts<Z){var W=a.default.getSilentFrame(this._audioMeta.originalCodec,this._audioMeta.channelCount);if(W){var J=ee.beginDts,xe=Z-ee.beginDts;L.default.v(this.TAG,"InsertPrefixSilentAudio: dts: "+J+", duration: "+xe),l.unshift({unit:W,dts:J,pts:J}),w+=W.byteLength}}else O=!1}for(var te=[],re=0;re<l.length;re++){var D=l[re],Re=D.unit,fe=D.dts-this._dtsBase,J=fe,Ee=!1,Fe=null,ne=0;if(!(fe<-.001)){if(this._audioMeta.codec!=="mp3"){var K=fe,ye=3;if(this._audioNextDts&&(K=this._audioNextDts),t=fe-K,t<=-ye*g){L.default.w(this.TAG,"Dropping 1 audio frame (originalDts: "+fe+" ms ,curRefDts: "+K+" ms)  due to dtsCorrection: "+t+" ms overlap.");continue}else if(t>=ye*g&&this._fillAudioTimestampGap&&!s.default.safari){Ee=!0;var Ie=Math.floor(t/g);L.default.w(this.TAG,`Large audio timestamp gap detected, may cause AV sync to drift. Silent frames will be generated to avoid unsync.
`+("originalDts: "+fe+" ms, curRefDts: "+K+" ms, ")+("dtsCorrection: "+Math.round(t)+" ms, generate: "+Ie+" frames")),J=Math.floor(K),ne=Math.floor(K+g)-J;var W=a.default.getSilentFrame(this._audioMeta.originalCodec,this._audioMeta.channelCount);W==null&&(L.default.w(this.TAG,"Unable to generate silent frame for "+(this._audioMeta.originalCodec+" with "+this._audioMeta.channelCount+" channels, repeat last frame")),W=Re),Fe=[];for(var Xe=0;Xe<Ie;Xe++){K=K+g;var Be=Math.floor(K),We=Math.floor(K+g)-Be,qe={dts:Be,pts:Be,cts:0,unit:W,size:W.byteLength,duration:We,originalDts:fe,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}};Fe.push(qe),w+=qe.size}this._audioNextDts=K+g}else J=Math.floor(K),ne=Math.floor(K+g)-J,this._audioNextDts=K+g}else{if(J=fe-t,re!==l.length-1){var et=l[re+1].dts-this._dtsBase-t;ne=et-J}else if(P!=null){var et=P.dts-this._dtsBase-t;ne=et-J}else te.length>=1?ne=te[te.length-1].duration:ne=Math.floor(g);this._audioNextDts=J+ne}p===-1&&(p=J),te.push({dts:J,pts:J,cts:0,unit:D.unit,size:D.unit.byteLength,duration:ne,originalDts:fe,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}}),Ee&&te.push.apply(te,Fe)}}if(te.length===0){u.samples=[],u.length=0;return}b?R=new Uint8Array(w):(R=new Uint8Array(w),R[0]=w>>>24&255,R[1]=w>>>16&255,R[2]=w>>>8&255,R[3]=w&255,R.set(_.default.types.mdat,4));for(var re=0;re<te.length;re++){var Re=te[re].unit;R.set(Re,S),S+=Re.byteLength}var Pe=te[te.length-1];v=Pe.dts+Pe.duration;var Oe=new c.MediaSegmentInfo;Oe.beginDts=p,Oe.endDts=v,Oe.beginPts=p,Oe.endPts=v,Oe.originalBeginDts=te[0].originalDts,Oe.originalEndDts=Pe.originalDts+Pe.duration,Oe.firstSample=new c.SampleInfo(te[0].dts,te[0].pts,te[0].duration,te[0].originalDts,!1),Oe.lastSample=new c.SampleInfo(Pe.dts,Pe.pts,Pe.duration,Pe.originalDts,!1),this._isLive||this._audioSegmentInfoList.append(Oe),u.samples=te,u.sequenceNumber++;var ut=null;b?ut=new Uint8Array:ut=_.default.moof(u,p),u.samples=[],u.length=0;var St={type:"audio",data:this._mergeBoxes(ut,R).buffer,sampleCount:te.length,info:Oe};b&&T&&(St.timestampOffset=p),this._onMediaSegment("audio",St)}}},h.prototype._remuxVideo=function(o,r){if(this._videoMeta!=null){var u=o,l=u.samples,t=void 0,p=-1,v=-1,g=-1,b=-1;if(!(!l||l.length===0)&&!(l.length===1&&!r)){var T=8,O=null,S=8+o.length,R=null;if(l.length>1&&(R=l.pop(),S-=R.length),this._videoStashedLastSample!=null){var w=this._videoStashedLastSample;this._videoStashedLastSample=null,l.unshift(w),S+=w.length}R!=null&&(this._videoStashedLastSample=R);var P=l[0].dts-this._dtsBase;if(this._videoNextDts)t=P-this._videoNextDts;else if(this._videoSegmentInfoList.isEmpty())t=0;else{var D=this._videoSegmentInfoList.getLastSampleBefore(P);if(D!=null){var M=P-(D.originalDts+D.duration);M<=3&&(M=0);var G=D.dts+D.duration+M;t=P-G}else t=0}for(var z=new c.MediaSegmentInfo,j=[],Z=0;Z<l.length;Z++){var w=l[Z],ee=w.dts-this._dtsBase,W=w.isKeyframe,J=ee-t,xe=w.cts,te=J+xe;p===-1&&(p=J,g=te);var re=0;if(Z!==l.length-1){var Re=l[Z+1].dts-this._dtsBase-t;re=Re-J}else if(R!=null){var Re=R.dts-this._dtsBase-t;re=Re-J}else j.length>=1?re=j[j.length-1].duration:re=Math.floor(this._videoMeta.refSampleDuration);if(W){var fe=new c.SampleInfo(J,te,re,w.dts,!0);fe.fileposition=w.fileposition,z.appendSyncPoint(fe)}j.push({dts:J,pts:te,cts:xe,units:w.units,size:w.length,isKeyframe:W,duration:re,originalDts:ee,flags:{isLeading:0,dependsOn:W?2:1,isDependedOn:W?1:0,hasRedundancy:0,isNonSync:W?0:1}})}O=new Uint8Array(S),O[0]=S>>>24&255,O[1]=S>>>16&255,O[2]=S>>>8&255,O[3]=S&255,O.set(_.default.types.mdat,4);for(var Z=0;Z<j.length;Z++)for(var Ee=j[Z].units;Ee.length;){var Fe=Ee.shift(),ne=Fe.data;O.set(ne,T),T+=ne.byteLength}var K=j[j.length-1];if(v=K.dts+K.duration,b=K.pts+K.duration,this._videoNextDts=v,z.beginDts=p,z.endDts=v,z.beginPts=g,z.endPts=b,z.originalBeginDts=j[0].originalDts,z.originalEndDts=K.originalDts+K.duration,z.firstSample=new c.SampleInfo(j[0].dts,j[0].pts,j[0].duration,j[0].originalDts,j[0].isKeyframe),z.lastSample=new c.SampleInfo(K.dts,K.pts,K.duration,K.originalDts,K.isKeyframe),this._isLive||this._videoSegmentInfoList.append(z),u.samples=j,u.sequenceNumber++,this._forceFirstIDR){var ye=j[0].flags;ye.dependsOn=2,ye.isNonSync=0}var Ie=_.default.moof(u,p);u.samples=[],u.length=0,this._onMediaSegment("video",{type:"video",data:this._mergeBoxes(Ie,O).buffer,sampleCount:j.length,info:z})}}},h.prototype._mergeBoxes=function(o,r){var u=new Uint8Array(o.byteLength+r.byteLength);return u.set(o,0),u.set(r,o.byteLength),u},h}();y.default=f},"./src/utils/browser.js":function(E,y,m){m.r(y);var L={};function _(){var a=self.navigator.userAgent.toLowerCase(),s=/(edge)\/([\w.]+)/.exec(a)||/(opr)[\/]([\w.]+)/.exec(a)||/(chrome)[ \/]([\w.]+)/.exec(a)||/(iemobile)[\/]([\w.]+)/.exec(a)||/(version)(applewebkit)[ \/]([\w.]+).*(safari)[ \/]([\w.]+)/.exec(a)||/(webkit)[ \/]([\w.]+).*(version)[ \/]([\w.]+).*(safari)[ \/]([\w.]+)/.exec(a)||/(webkit)[ \/]([\w.]+)/.exec(a)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(a)||/(msie) ([\w.]+)/.exec(a)||a.indexOf("trident")>=0&&/(rv)(?::| )([\w.]+)/.exec(a)||a.indexOf("compatible")<0&&/(firefox)[ \/]([\w.]+)/.exec(a)||[],c=/(ipad)/.exec(a)||/(ipod)/.exec(a)||/(windows phone)/.exec(a)||/(iphone)/.exec(a)||/(kindle)/.exec(a)||/(android)/.exec(a)||/(windows)/.exec(a)||/(mac)/.exec(a)||/(linux)/.exec(a)||/(cros)/.exec(a)||[],n={browser:s[5]||s[3]||s[1]||"",version:s[2]||s[4]||"0",majorVersion:s[4]||s[2]||"0",platform:c[0]||""},f={};if(n.browser){f[n.browser]=!0;var h=n.majorVersion.split(".");f.version={major:parseInt(n.majorVersion,10),string:n.version},h.length>1&&(f.version.minor=parseInt(h[1],10)),h.length>2&&(f.version.build=parseInt(h[2],10))}if(n.platform&&(f[n.platform]=!0),(f.chrome||f.opr||f.safari)&&(f.webkit=!0),f.rv||f.iemobile){f.rv&&delete f.rv;var o="msie";n.browser=o,f[o]=!0}if(f.edge){delete f.edge;var r="msedge";n.browser=r,f[r]=!0}if(f.opr){var u="opera";n.browser=u,f[u]=!0}if(f.safari&&f.android){var l="android";n.browser=l,f[l]=!0}f.name=n.browser,f.platform=n.platform;for(var t in L)L.hasOwnProperty(t)&&delete L[t];Object.assign(L,f)}_(),y.default=L},"./src/utils/exception.js":function(E,y,m){m.r(y),m.d(y,{RuntimeException:function(){return _},IllegalStateException:function(){return a},InvalidArgumentException:function(){return s},NotImplementedException:function(){return c}});var L=function(){var n=function(f,h){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,r){o.__proto__=r}||function(o,r){for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&(o[u]=r[u])},n(f,h)};return function(f,h){if(typeof h!="function"&&h!==null)throw new TypeError("Class extends value "+String(h)+" is not a constructor or null");n(f,h);function o(){this.constructor=f}f.prototype=h===null?Object.create(h):(o.prototype=h.prototype,new o)}}(),_=function(){function n(f){this._message=f}return Object.defineProperty(n.prototype,"name",{get:function(){return"RuntimeException"},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"message",{get:function(){return this._message},enumerable:!1,configurable:!0}),n.prototype.toString=function(){return this.name+": "+this.message},n}(),a=function(n){L(f,n);function f(h){return n.call(this,h)||this}return Object.defineProperty(f.prototype,"name",{get:function(){return"IllegalStateException"},enumerable:!1,configurable:!0}),f}(_),s=function(n){L(f,n);function f(h){return n.call(this,h)||this}return Object.defineProperty(f.prototype,"name",{get:function(){return"InvalidArgumentException"},enumerable:!1,configurable:!0}),f}(_),c=function(n){L(f,n);function f(h){return n.call(this,h)||this}return Object.defineProperty(f.prototype,"name",{get:function(){return"NotImplementedException"},enumerable:!1,configurable:!0}),f}(_)},"./src/utils/logger.js":function(E,y,m){m.r(y);var L=m("./node_modules/events/events.js"),_=m.n(L),a=function(){function s(){}return s.e=function(c,n){(!c||s.FORCE_GLOBAL_TAG)&&(c=s.GLOBAL_TAG);var f="["+c+"] > "+n;s.ENABLE_CALLBACK&&s.emitter.emit("log","error",f),s.ENABLE_ERROR&&(console.error?console.error(f):console.warn?console.warn(f):console.log(f))},s.i=function(c,n){(!c||s.FORCE_GLOBAL_TAG)&&(c=s.GLOBAL_TAG);var f="["+c+"] > "+n;s.ENABLE_CALLBACK&&s.emitter.emit("log","info",f),s.ENABLE_INFO&&(console.info?console.info(f):console.log(f))},s.w=function(c,n){(!c||s.FORCE_GLOBAL_TAG)&&(c=s.GLOBAL_TAG);var f="["+c+"] > "+n;s.ENABLE_CALLBACK&&s.emitter.emit("log","warn",f),s.ENABLE_WARN&&(console.warn?console.warn(f):console.log(f))},s.d=function(c,n){(!c||s.FORCE_GLOBAL_TAG)&&(c=s.GLOBAL_TAG);var f="["+c+"] > "+n;s.ENABLE_CALLBACK&&s.emitter.emit("log","debug",f),s.ENABLE_DEBUG&&(console.debug?console.debug(f):console.log(f))},s.v=function(c,n){(!c||s.FORCE_GLOBAL_TAG)&&(c=s.GLOBAL_TAG);var f="["+c+"] > "+n;s.ENABLE_CALLBACK&&s.emitter.emit("log","verbose",f),s.ENABLE_VERBOSE&&console.log(f)},s}();a.GLOBAL_TAG="flv.js",a.FORCE_GLOBAL_TAG=!1,a.ENABLE_ERROR=!0,a.ENABLE_INFO=!0,a.ENABLE_WARN=!0,a.ENABLE_DEBUG=!0,a.ENABLE_VERBOSE=!0,a.ENABLE_CALLBACK=!1,a.emitter=new(_()),y.default=a},"./src/utils/logging-control.js":function(E,y,m){m.r(y);var L=m("./node_modules/events/events.js"),_=m.n(L),a=m("./src/utils/logger.js"),s=function(){function c(){}return Object.defineProperty(c,"forceGlobalTag",{get:function(){return a.default.FORCE_GLOBAL_TAG},set:function(n){a.default.FORCE_GLOBAL_TAG=n,c._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(c,"globalTag",{get:function(){return a.default.GLOBAL_TAG},set:function(n){a.default.GLOBAL_TAG=n,c._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(c,"enableAll",{get:function(){return a.default.ENABLE_VERBOSE&&a.default.ENABLE_DEBUG&&a.default.ENABLE_INFO&&a.default.ENABLE_WARN&&a.default.ENABLE_ERROR},set:function(n){a.default.ENABLE_VERBOSE=n,a.default.ENABLE_DEBUG=n,a.default.ENABLE_INFO=n,a.default.ENABLE_WARN=n,a.default.ENABLE_ERROR=n,c._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(c,"enableDebug",{get:function(){return a.default.ENABLE_DEBUG},set:function(n){a.default.ENABLE_DEBUG=n,c._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(c,"enableVerbose",{get:function(){return a.default.ENABLE_VERBOSE},set:function(n){a.default.ENABLE_VERBOSE=n,c._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(c,"enableInfo",{get:function(){return a.default.ENABLE_INFO},set:function(n){a.default.ENABLE_INFO=n,c._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(c,"enableWarn",{get:function(){return a.default.ENABLE_WARN},set:function(n){a.default.ENABLE_WARN=n,c._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(c,"enableError",{get:function(){return a.default.ENABLE_ERROR},set:function(n){a.default.ENABLE_ERROR=n,c._notifyChange()},enumerable:!1,configurable:!0}),c.getConfig=function(){return{globalTag:a.default.GLOBAL_TAG,forceGlobalTag:a.default.FORCE_GLOBAL_TAG,enableVerbose:a.default.ENABLE_VERBOSE,enableDebug:a.default.ENABLE_DEBUG,enableInfo:a.default.ENABLE_INFO,enableWarn:a.default.ENABLE_WARN,enableError:a.default.ENABLE_ERROR,enableCallback:a.default.ENABLE_CALLBACK}},c.applyConfig=function(n){a.default.GLOBAL_TAG=n.globalTag,a.default.FORCE_GLOBAL_TAG=n.forceGlobalTag,a.default.ENABLE_VERBOSE=n.enableVerbose,a.default.ENABLE_DEBUG=n.enableDebug,a.default.ENABLE_INFO=n.enableInfo,a.default.ENABLE_WARN=n.enableWarn,a.default.ENABLE_ERROR=n.enableError,a.default.ENABLE_CALLBACK=n.enableCallback},c._notifyChange=function(){var n=c.emitter;if(n.listenerCount("change")>0){var f=c.getConfig();n.emit("change",f)}},c.registerListener=function(n){c.emitter.addListener("change",n)},c.removeListener=function(n){c.emitter.removeListener("change",n)},c.addLogListener=function(n){a.default.emitter.addListener("log",n),a.default.emitter.listenerCount("log")>0&&(a.default.ENABLE_CALLBACK=!0,c._notifyChange())},c.removeLogListener=function(n){a.default.emitter.removeListener("log",n),a.default.emitter.listenerCount("log")===0&&(a.default.ENABLE_CALLBACK=!1,c._notifyChange())},c}();s.emitter=new(_()),y.default=s},"./src/utils/polyfill.js":function(E,y,m){m.r(y);var L=function(){function _(){}return _.install=function(){Object.setPrototypeOf=Object.setPrototypeOf||function(a,s){return a.__proto__=s,a},Object.assign=Object.assign||function(a){if(a==null)throw new TypeError("Cannot convert undefined or null to object");for(var s=Object(a),c=1;c<arguments.length;c++){var n=arguments[c];if(n!=null)for(var f in n)n.hasOwnProperty(f)&&(s[f]=n[f])}return s},typeof self.Promise!="function"&&m("./node_modules/es6-promise/dist/es6-promise.js").polyfill()},_}();L.install(),y.default=L},"./src/utils/utf8-conv.js":function(E,y,m){m.r(y);function L(a,s,c){var n=a;if(s+c<n.length){for(;c--;)if((n[++s]&192)!==128)return!1;return!0}else return!1}function _(a){for(var s=[],c=a,n=0,f=a.length;n<f;){if(c[n]<128){s.push(String.fromCharCode(c[n])),++n;continue}else if(!(c[n]<192)){if(c[n]<224){if(L(c,n,1)){var h=(c[n]&31)<<6|c[n+1]&63;if(h>=128){s.push(String.fromCharCode(h&65535)),n+=2;continue}}}else if(c[n]<240){if(L(c,n,2)){var h=(c[n]&15)<<12|(c[n+1]&63)<<6|c[n+2]&63;if(h>=2048&&(h&63488)!==55296){s.push(String.fromCharCode(h&65535)),n+=3;continue}}}else if(c[n]<248&&L(c,n,3)){var h=(c[n]&7)<<18|(c[n+1]&63)<<12|(c[n+2]&63)<<6|c[n+3]&63;if(h>65536&&h<1114112){h-=65536,s.push(String.fromCharCode(h>>>10|55296)),s.push(String.fromCharCode(h&1023|56320)),n+=4;continue}}}s.push("�"),++n}return s.join("")}y.default=_}},x={};function A(E){var y=x[E];if(y!==void 0)return y.exports;var m=x[E]={exports:{}};return i[E].call(m.exports,m,m.exports,A),m.exports}A.m=i,function(){A.n=function(E){var y=E&&E.__esModule?function(){return E.default}:function(){return E};return A.d(y,{a:y}),y}}(),function(){A.d=function(E,y){for(var m in y)A.o(y,m)&&!A.o(E,m)&&Object.defineProperty(E,m,{enumerable:!0,get:y[m]})}}(),function(){A.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch{if(typeof window=="object")return window}}()}(),function(){A.o=function(E,y){return Object.prototype.hasOwnProperty.call(E,y)}}(),function(){A.r=function(E){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(E,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(E,"__esModule",{value:!0})}}();var C=A("./src/index.js");return C}()})})(Ur);var Vn=Ur.exports;const Wn=pn(Vn),Gn=Nt({__name:"DPlayer_v2",props:{videoInfo:{}},setup(e){const d=e,i=new wn,x=Ue(),A=Ue();function C(){var y,m,L,_;A.value=new Cn({element:x.value,loop:((y=d.videoInfo)==null?void 0:y.loop)??!1,autoplay:((m=d.videoInfo)==null?void 0:m.autoplay)??!1,hotkey:((L=d.videoInfo)==null?void 0:L.hotkey)??!1,preload:((_=d.videoInfo)==null?void 0:_.preload)??"auto",preventClickToggle:!0,live:d.videoInfo.live??!0,mutex:!1,video:{url:d.videoInfo.video.url,type:d.videoInfo.video.type||"auto",customType:{customHls(a,s){i.loadSource(a.src),i.attachMedia(a)},customFlv:function(a,s){const c=Wn.createPlayer({type:"flv",url:a.src});c.attachMediaElement(a),c.load(),s.events.on("destroy",()=>{c.unload(),c.detachMediaElement(),c.destroy()})}}}})}const E=()=>{A.value.fullScreen.request("web")};return Et(()=>{C()}),lr(()=>{i.stopLoad(),i.detachMedia(),A.value=null}),(y,m)=>(ue(),we("div",{class:"video_box",onDblclick:E},[_e("div",{ref_key:"DPlayerIns",ref:x,style:{width:"100%",height:"100%"}},null,512)],32))}}),Kn=Vt(Gn,[["__scopeId","data-v-016d0aea"]]),Hn={key:0,class:"horizontal"},zn={key:1,class:"trigger-content-default-wrap"},Xn={key:1,class:"vertical"},Yn={key:1,class:"trigger-content-default-wrap"},$n=Nt({__name:"Split",props:{value:{default:.2},mode:{default:"horizontal"},min:{default:.1},max:{default:.8}},emits:["on-move-start","on-moving","on-move-end"],setup(e,{emit:d}){const i=Ue(),x=Ue(),A=Ue(),C=e,E=d,y=ht({min:C.min,max:C.max,left:C.value*1,top:C.value*1,mode:C.mode,gSplitWidth:0,gSplitHeight:0,horizontalTriggerPanelWidht:0,verticalTriggerPanelHeight:0,showHorizontal:Ge(()=>C.mode==="horizontal"),showVertical:Ge(()=>C.mode==="vertical"),horizontalLeftPanel:Ge(()=>({left:0,right:(1-y.left)*100+"%"})),horizontalRightPanel:Ge(()=>({left:(y.left+y.horizontalTriggerPanelWidht/y.gSplitWidth)*100+"%",width:"calc(100% - "+(y.left+y.horizontalTriggerPanelWidht/y.gSplitWidth)*100+"%)"})),horizontaltriggerPanel:Ge(()=>({left:y.left*100+"%"})),verticalTopPanel:Ge(()=>({top:0,bottom:(1-y.top)*100+"%"})),verticalBottomPanel:Ge(()=>({top:(y.top+y.verticalTriggerPanelHeight/y.gSplitHeight)*100+"%",height:"calc(100% - "+(y.top+y.verticalTriggerPanelHeight/y.gSplitHeight)*100+"%)"})),verticaltriggerPanel:Ge(()=>({top:y.top*100+"%"}))}),m=()=>{y.gSplitWidth=i.value.clientWidth,y.gSplitHeight=i.value.clientHeight,y.mode=="horizontal"&&(y.horizontalTriggerPanelWidht=x.value.clientWidth),y.mode=="vertical"&&(y.verticalTriggerPanelHeight=A.value.clientHeight)},L=()=>{y.mode=="horizontal"&&a(),y.mode=="vertical"&&s()},_=n=>{n.preventDefault()},a=()=>{c("horizontal",x.value)},s=()=>{c("vertical",A.value)},c=(n="horizontal",f)=>{const h=o=>{document.addEventListener("selectstart",_),E("on-move-start",o);const r=n=="horizontal"?"left":"top",u=n=="horizontal"?o.clientX-f.offsetLeft:o.clientY-f.offsetTop,l=p=>{E("on-moving",p);const v=n=="horizontal"?i.value.clientWidth:i.value.clientHeight;return y[r]=(n=="horizontal"?p.clientX-u:p.clientY-u)/v,y[r]<y.min&&(y[r]=y.min),y[r]>1-y.min&&(y[r]=1-y.min),!1},t=()=>(E("on-move-end",o),document.removeEventListener("mousemove",l),document.removeEventListener("mouseup",t),document.removeEventListener("selectstart",_),!1);return document.addEventListener("mousemove",l),document.addEventListener("mouseup",t),!1};f.addEventListener("mousedown",h)};return Et(()=>{L(),setTimeout(()=>{m()},0)}),(n,f)=>(ue(),we("div",{class:"g-split",ref_key:"gSplit",ref:i},[H(y).showHorizontal?(ue(),we("div",Hn,[_e("div",{class:"left-panel position",style:rt(H(y).horizontalLeftPanel)},[Ze(n.$slots,"left",{},void 0,!0)],4),_e("div",{class:"horizontal-trigger-panel position",style:rt(H(y).horizontaltriggerPanel),ref_key:"horizontalTriggerPanel",ref:x},[n.$slots.trigger?Ze(n.$slots,"trigger",{key:0},void 0,!0):(ue(),we("div",zn,f[0]||(f[0]=[_r('<div class="trigger-content" data-v-222efde5><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i></div>',1)])))],4),_e("div",{class:"right-panel position",style:rt(H(y).horizontalRightPanel)},[Ze(n.$slots,"right",{},void 0,!0)],4)])):_t("",!0),H(y).showVertical?(ue(),we("div",Xn,[_e("div",{class:"top-panel position",style:rt(H(y).verticalTopPanel)},[Ze(n.$slots,"top",{},void 0,!0)],4),_e("div",{class:"vertical-trigger-panel position",style:rt(H(y).verticaltriggerPanel),ref_key:"verticalTriggerPanel",ref:A},[n.$slots.trigger?Ze(n.$slots,"trigger",{key:0},void 0,!0):(ue(),we("div",Yn,f[1]||(f[1]=[_r('<div class="trigger-content" data-v-222efde5><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i><i class="trigger-bar" data-v-222efde5></i></div>',1)])))],4),_e("div",{class:"bottom-panel position",style:rt(H(y).verticalBottomPanel)},[Ze(n.$slots,"bottom",{},void 0,!0)],4)])):_t("",!0)],512))}}),Zn=Vt($n,[["__scopeId","data-v-222efde5"]]),pr=mn("video",{state:()=>({videoList:[{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null}],group:8,total:9}),actions:{putValue(){return{videoList:this.videoList,group:this.group,total:this.total}},setValue(e){this.videoList=e.videoList,this.total=e.total,this.group=e.group}}});var Jn=Object.defineProperty,Mt=Object.getOwnPropertySymbols,jr=Object.prototype.hasOwnProperty,Nr=Object.prototype.propertyIsEnumerable,mr=(e,d,i)=>d in e?Jn(e,d,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[d]=i,Vr=(e,d)=>{for(var i in d||(d={}))jr.call(d,i)&&mr(e,i,d[i]);if(Mt)for(var i of Mt(d))Nr.call(d,i)&&mr(e,i,d[i]);return e},Qn=(e,d)=>{var i={};for(var x in e)jr.call(e,x)&&d.indexOf(x)<0&&(i[x]=e[x]);if(e!=null&&Mt)for(var x of Mt(e))d.indexOf(x)<0&&Nr.call(e,x)&&(i[x]=e[x]);return i};const Wr="[vue-draggable-plus]: ";function qn(e){console.warn(Wr+e)}function ei(e){console.error(Wr+e)}function vr(e,d,i){return i>=0&&i<e.length&&e.splice(i,0,e.splice(d,1)[0]),e}function gr(e,d){return Array.isArray(e)&&e.splice(d,1),e}function xr(e,d,i){return Array.isArray(e)&&e.splice(d,0,i),e}function ti(e){return typeof e>"u"}function ri(e){return typeof e=="string"}function Er(e,d,i){const x=e.children[i];e.insertBefore(d,x)}function zt(e){e.parentNode&&e.parentNode.removeChild(e)}function ni(e,d=document){var i;let x=null;return typeof(d==null?void 0:d.querySelector)=="function"?x=(i=d==null?void 0:d.querySelector)==null?void 0:i.call(d,e):x=document.querySelector(e),x||qn(`Element not found: ${e}`),x}function ii(e,d,i=null){return function(...x){return e.apply(i,x),d.apply(i,x)}}function oi(e,d){const i=Vr({},e);return Object.keys(d).forEach(x=>{i[x]?i[x]=ii(e[x],d[x]):i[x]=d[x]}),i}function si(e){return e instanceof HTMLElement}function yr(e,d){Object.keys(e).forEach(i=>{d(i,e[i])})}function ai(e){return e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97)}const li=Object.assign;/**!
 * Sortable 1.15.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function br(e,d){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var x=Object.getOwnPropertySymbols(e);d&&(x=x.filter(function(A){return Object.getOwnPropertyDescriptor(e,A).enumerable})),i.push.apply(i,x)}return i}function ke(e){for(var d=1;d<arguments.length;d++){var i=arguments[d]!=null?arguments[d]:{};d%2?br(Object(i),!0).forEach(function(x){ui(e,x,i[x])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):br(Object(i)).forEach(function(x){Object.defineProperty(e,x,Object.getOwnPropertyDescriptor(i,x))})}return e}function wt(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?wt=function(d){return typeof d}:wt=function(d){return d&&typeof Symbol=="function"&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},wt(e)}function ui(e,d,i){return d in e?Object.defineProperty(e,d,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[d]=i,e}function Ne(){return Ne=Object.assign||function(e){for(var d=1;d<arguments.length;d++){var i=arguments[d];for(var x in i)Object.prototype.hasOwnProperty.call(i,x)&&(e[x]=i[x])}return e},Ne.apply(this,arguments)}function di(e,d){if(e==null)return{};var i={},x=Object.keys(e),A,C;for(C=0;C<x.length;C++)A=x[C],!(d.indexOf(A)>=0)&&(i[A]=e[A]);return i}function fi(e,d){if(e==null)return{};var i=di(e,d),x,A;if(Object.getOwnPropertySymbols){var C=Object.getOwnPropertySymbols(e);for(A=0;A<C.length;A++)x=C[A],!(d.indexOf(x)>=0)&&Object.prototype.propertyIsEnumerable.call(e,x)&&(i[x]=e[x])}return i}var ci="1.15.2";function je(e){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(e)}var Ve=je(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),yt=je(/Edge/i),Sr=je(/firefox/i),pt=je(/safari/i)&&!je(/chrome/i)&&!je(/android/i),Gr=je(/iP(ad|od|hone)/i),Kr=je(/chrome/i)&&je(/android/i),Hr={capture:!1,passive:!1};function q(e,d,i){e.addEventListener(d,i,!Ve&&Hr)}function Q(e,d,i){e.removeEventListener(d,i,!Ve&&Hr)}function kt(e,d){if(d){if(d[0]===">"&&(d=d.substring(1)),e)try{if(e.matches)return e.matches(d);if(e.msMatchesSelector)return e.msMatchesSelector(d);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(d)}catch{return!1}return!1}}function hi(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function De(e,d,i,x){if(e){i=i||document;do{if(d!=null&&(d[0]===">"?e.parentNode===i&&kt(e,d):kt(e,d))||x&&e===i)return e;if(e===i)break}while(e=hi(e))}return null}var Lr=/\s+/g;function be(e,d,i){if(e&&d)if(e.classList)e.classList[i?"add":"remove"](d);else{var x=(" "+e.className+" ").replace(Lr," ").replace(" "+d+" "," ");e.className=(x+(i?" "+d:"")).replace(Lr," ")}}function N(e,d,i){var x=e&&e.style;if(x){if(i===void 0)return document.defaultView&&document.defaultView.getComputedStyle?i=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(i=e.currentStyle),d===void 0?i:i[d];!(d in x)&&d.indexOf("webkit")===-1&&(d="-webkit-"+d),x[d]=i+(typeof i=="string"?"":"px")}}function at(e,d){var i="";if(typeof e=="string")i=e;else do{var x=N(e,"transform");x&&x!=="none"&&(i=x+" "+i)}while(!d&&(e=e.parentNode));var A=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return A&&new A(i)}function zr(e,d,i){if(e){var x=e.getElementsByTagName(d),A=0,C=x.length;if(i)for(;A<C;A++)i(x[A],A);return x}return[]}function Me(){var e=document.scrollingElement;return e||document.documentElement}function le(e,d,i,x,A){if(!(!e.getBoundingClientRect&&e!==window)){var C,E,y,m,L,_,a;if(e!==window&&e.parentNode&&e!==Me()?(C=e.getBoundingClientRect(),E=C.top,y=C.left,m=C.bottom,L=C.right,_=C.height,a=C.width):(E=0,y=0,m=window.innerHeight,L=window.innerWidth,_=window.innerHeight,a=window.innerWidth),(d||i)&&e!==window&&(A=A||e.parentNode,!Ve))do if(A&&A.getBoundingClientRect&&(N(A,"transform")!=="none"||i&&N(A,"position")!=="static")){var s=A.getBoundingClientRect();E-=s.top+parseInt(N(A,"border-top-width")),y-=s.left+parseInt(N(A,"border-left-width")),m=E+C.height,L=y+C.width;break}while(A=A.parentNode);if(x&&e!==window){var c=at(A||e),n=c&&c.a,f=c&&c.d;c&&(E/=f,y/=n,a/=n,_/=f,m=E+_,L=y+a)}return{top:E,left:y,bottom:m,right:L,width:a,height:_}}}function Ar(e,d,i){for(var x=ze(e,!0),A=le(e)[d];x;){var C=le(x)[i],E=void 0;if(E=A>=C,!E)return x;if(x===Me())break;x=ze(x,!1)}return!1}function lt(e,d,i,x){for(var A=0,C=0,E=e.children;C<E.length;){if(E[C].style.display!=="none"&&E[C]!==V.ghost&&(x||E[C]!==V.dragged)&&De(E[C],i.draggable,e,!1)){if(A===d)return E[C];A++}C++}return null}function dr(e,d){for(var i=e.lastElementChild;i&&(i===V.ghost||N(i,"display")==="none"||d&&!kt(i,d));)i=i.previousElementSibling;return i||null}function Te(e,d){var i=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)e.nodeName.toUpperCase()!=="TEMPLATE"&&e!==V.clone&&(!d||kt(e,d))&&i++;return i}function Rr(e){var d=0,i=0,x=Me();if(e)do{var A=at(e),C=A.a,E=A.d;d+=e.scrollLeft*C,i+=e.scrollTop*E}while(e!==x&&(e=e.parentNode));return[d,i]}function _i(e,d){for(var i in e)if(e.hasOwnProperty(i)){for(var x in d)if(d.hasOwnProperty(x)&&d[x]===e[i][x])return Number(i)}return-1}function ze(e,d){if(!e||!e.getBoundingClientRect)return Me();var i=e,x=!1;do if(i.clientWidth<i.scrollWidth||i.clientHeight<i.scrollHeight){var A=N(i);if(i.clientWidth<i.scrollWidth&&(A.overflowX=="auto"||A.overflowX=="scroll")||i.clientHeight<i.scrollHeight&&(A.overflowY=="auto"||A.overflowY=="scroll")){if(!i.getBoundingClientRect||i===document.body)return Me();if(x||d)return i;x=!0}}while(i=i.parentNode);return Me()}function pi(e,d){if(e&&d)for(var i in d)d.hasOwnProperty(i)&&(e[i]=d[i]);return e}function Xt(e,d){return Math.round(e.top)===Math.round(d.top)&&Math.round(e.left)===Math.round(d.left)&&Math.round(e.height)===Math.round(d.height)&&Math.round(e.width)===Math.round(d.width)}var mt;function Xr(e,d){return function(){if(!mt){var i=arguments,x=this;i.length===1?e.call(x,i[0]):e.apply(x,i),mt=setTimeout(function(){mt=void 0},d)}}}function mi(){clearTimeout(mt),mt=void 0}function Yr(e,d,i){e.scrollLeft+=d,e.scrollTop+=i}function $r(e){var d=window.Polymer,i=window.jQuery||window.Zepto;return d&&d.dom?d.dom(e).cloneNode(!0):i?i(e).clone(!0)[0]:e.cloneNode(!0)}function Zr(e,d,i){var x={};return Array.from(e.children).forEach(function(A){var C,E,y,m;if(!(!De(A,d.draggable,e,!1)||A.animated||A===i)){var L=le(A);x.left=Math.min((C=x.left)!==null&&C!==void 0?C:1/0,L.left),x.top=Math.min((E=x.top)!==null&&E!==void 0?E:1/0,L.top),x.right=Math.max((y=x.right)!==null&&y!==void 0?y:-1/0,L.right),x.bottom=Math.max((m=x.bottom)!==null&&m!==void 0?m:-1/0,L.bottom)}}),x.width=x.right-x.left,x.height=x.bottom-x.top,x.x=x.left,x.y=x.top,x}var Ae="Sortable"+new Date().getTime();function vi(){var e=[],d;return{captureAnimationState:function(){if(e=[],!!this.options.animation){var i=[].slice.call(this.el.children);i.forEach(function(x){if(!(N(x,"display")==="none"||x===V.ghost)){e.push({target:x,rect:le(x)});var A=ke({},e[e.length-1].rect);if(x.thisAnimationDuration){var C=at(x,!0);C&&(A.top-=C.f,A.left-=C.e)}x.fromRect=A}})}},addAnimationState:function(i){e.push(i)},removeAnimationState:function(i){e.splice(_i(e,{target:i}),1)},animateAll:function(i){var x=this;if(!this.options.animation){clearTimeout(d),typeof i=="function"&&i();return}var A=!1,C=0;e.forEach(function(E){var y=0,m=E.target,L=m.fromRect,_=le(m),a=m.prevFromRect,s=m.prevToRect,c=E.rect,n=at(m,!0);n&&(_.top-=n.f,_.left-=n.e),m.toRect=_,m.thisAnimationDuration&&Xt(a,_)&&!Xt(L,_)&&(c.top-_.top)/(c.left-_.left)===(L.top-_.top)/(L.left-_.left)&&(y=xi(c,a,s,x.options)),Xt(_,L)||(m.prevFromRect=L,m.prevToRect=_,y||(y=x.options.animation),x.animate(m,c,_,y)),y&&(A=!0,C=Math.max(C,y),clearTimeout(m.animationResetTimer),m.animationResetTimer=setTimeout(function(){m.animationTime=0,m.prevFromRect=null,m.fromRect=null,m.prevToRect=null,m.thisAnimationDuration=null},y),m.thisAnimationDuration=y)}),clearTimeout(d),A?d=setTimeout(function(){typeof i=="function"&&i()},C):typeof i=="function"&&i(),e=[]},animate:function(i,x,A,C){if(C){N(i,"transition",""),N(i,"transform","");var E=at(this.el),y=E&&E.a,m=E&&E.d,L=(x.left-A.left)/(y||1),_=(x.top-A.top)/(m||1);i.animatingX=!!L,i.animatingY=!!_,N(i,"transform","translate3d("+L+"px,"+_+"px,0)"),this.forRepaintDummy=gi(i),N(i,"transition","transform "+C+"ms"+(this.options.easing?" "+this.options.easing:"")),N(i,"transform","translate3d(0,0,0)"),typeof i.animated=="number"&&clearTimeout(i.animated),i.animated=setTimeout(function(){N(i,"transition",""),N(i,"transform",""),i.animated=!1,i.animatingX=!1,i.animatingY=!1},C)}}}}function gi(e){return e.offsetWidth}function xi(e,d,i,x){return Math.sqrt(Math.pow(d.top-e.top,2)+Math.pow(d.left-e.left,2))/Math.sqrt(Math.pow(d.top-i.top,2)+Math.pow(d.left-i.left,2))*x.animation}var nt=[],Yt={initializeByDefault:!0},bt={mount:function(e){for(var d in Yt)Yt.hasOwnProperty(d)&&!(d in e)&&(e[d]=Yt[d]);nt.forEach(function(i){if(i.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),nt.push(e)},pluginEvent:function(e,d,i){var x=this;this.eventCanceled=!1,i.cancel=function(){x.eventCanceled=!0};var A=e+"Global";nt.forEach(function(C){d[C.pluginName]&&(d[C.pluginName][A]&&d[C.pluginName][A](ke({sortable:d},i)),d.options[C.pluginName]&&d[C.pluginName][e]&&d[C.pluginName][e](ke({sortable:d},i)))})},initializePlugins:function(e,d,i,x){nt.forEach(function(E){var y=E.pluginName;if(!(!e.options[y]&&!E.initializeByDefault)){var m=new E(e,d,e.options);m.sortable=e,m.options=e.options,e[y]=m,Ne(i,m.defaults)}});for(var A in e.options)if(e.options.hasOwnProperty(A)){var C=this.modifyOption(e,A,e.options[A]);typeof C<"u"&&(e.options[A]=C)}},getEventProperties:function(e,d){var i={};return nt.forEach(function(x){typeof x.eventProperties=="function"&&Ne(i,x.eventProperties.call(d[x.pluginName],e))}),i},modifyOption:function(e,d,i){var x;return nt.forEach(function(A){e[A.pluginName]&&A.optionListeners&&typeof A.optionListeners[d]=="function"&&(x=A.optionListeners[d].call(e[A.pluginName],i))}),x}};function Ei(e){var d=e.sortable,i=e.rootEl,x=e.name,A=e.targetEl,C=e.cloneEl,E=e.toEl,y=e.fromEl,m=e.oldIndex,L=e.newIndex,_=e.oldDraggableIndex,a=e.newDraggableIndex,s=e.originalEvent,c=e.putSortable,n=e.extraEventProperties;if(d=d||i&&i[Ae],!!d){var f,h=d.options,o="on"+x.charAt(0).toUpperCase()+x.substr(1);window.CustomEvent&&!Ve&&!yt?f=new CustomEvent(x,{bubbles:!0,cancelable:!0}):(f=document.createEvent("Event"),f.initEvent(x,!0,!0)),f.to=E||i,f.from=y||i,f.item=A||i,f.clone=C,f.oldIndex=m,f.newIndex=L,f.oldDraggableIndex=_,f.newDraggableIndex=a,f.originalEvent=s,f.pullMode=c?c.lastPutMode:void 0;var r=ke(ke({},n),bt.getEventProperties(x,d));for(var u in r)f[u]=r[u];i&&i.dispatchEvent(f),h[o]&&h[o].call(d,f)}}var yi=["evt"],ge=function(e,d){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},x=i.evt,A=fi(i,yi);bt.pluginEvent.bind(V)(e,d,ke({dragEl:F,parentEl:se,ghostEl:X,rootEl:ie,nextEl:Je,lastDownEl:Ct,cloneEl:oe,cloneHidden:He,dragStarted:dt,putSortable:ce,activeSortable:V.active,originalEvent:x,oldIndex:st,oldDraggableIndex:vt,newIndex:Se,newDraggableIndex:Ke,hideGhostForTarget:en,unhideGhostForTarget:tn,cloneNowHidden:function(){He=!0},cloneNowShown:function(){He=!1},dispatchSortableEvent:function(C){pe({sortable:d,name:C,originalEvent:x})}},A))};function pe(e){Ei(ke({putSortable:ce,cloneEl:oe,targetEl:F,rootEl:ie,oldIndex:st,oldDraggableIndex:vt,newIndex:Se,newDraggableIndex:Ke},e))}var F,se,X,ie,Je,Ct,oe,He,st,Se,vt,Ke,At,ce,ot=!1,Ft=!1,Bt=[],Ye,Ce,$t,Zt,Or,Tr,dt,it,gt,xt=!1,Rt=!1,Dt,he,Jt=[],ir=!1,Ut=[],Wt=typeof document<"u",Ot=Gr,wr=yt||Ve?"cssFloat":"float",bi=Wt&&!Kr&&!Gr&&"draggable"in document.createElement("div"),Jr=function(){if(Wt){if(Ve)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto",e.style.pointerEvents==="auto"}}(),Qr=function(e,d){var i=N(e),x=parseInt(i.width)-parseInt(i.paddingLeft)-parseInt(i.paddingRight)-parseInt(i.borderLeftWidth)-parseInt(i.borderRightWidth),A=lt(e,0,d),C=lt(e,1,d),E=A&&N(A),y=C&&N(C),m=E&&parseInt(E.marginLeft)+parseInt(E.marginRight)+le(A).width,L=y&&parseInt(y.marginLeft)+parseInt(y.marginRight)+le(C).width;if(i.display==="flex")return i.flexDirection==="column"||i.flexDirection==="column-reverse"?"vertical":"horizontal";if(i.display==="grid")return i.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(A&&E.float&&E.float!=="none"){var _=E.float==="left"?"left":"right";return C&&(y.clear==="both"||y.clear===_)?"vertical":"horizontal"}return A&&(E.display==="block"||E.display==="flex"||E.display==="table"||E.display==="grid"||m>=x&&i[wr]==="none"||C&&i[wr]==="none"&&m+L>x)?"vertical":"horizontal"},Si=function(e,d,i){var x=i?e.left:e.top,A=i?e.right:e.bottom,C=i?e.width:e.height,E=i?d.left:d.top,y=i?d.right:d.bottom,m=i?d.width:d.height;return x===E||A===y||x+C/2===E+m/2},Li=function(e,d){var i;return Bt.some(function(x){var A=x[Ae].options.emptyInsertThreshold;if(!(!A||dr(x))){var C=le(x),E=e>=C.left-A&&e<=C.right+A,y=d>=C.top-A&&d<=C.bottom+A;if(E&&y)return i=x}}),i},qr=function(e){function d(A,C){return function(E,y,m,L){var _=E.options.group.name&&y.options.group.name&&E.options.group.name===y.options.group.name;if(A==null&&(C||_))return!0;if(A==null||A===!1)return!1;if(C&&A==="clone")return A;if(typeof A=="function")return d(A(E,y,m,L),C)(E,y,m,L);var a=(C?E:y).options.group.name;return A===!0||typeof A=="string"&&A===a||A.join&&A.indexOf(a)>-1}}var i={},x=e.group;(!x||wt(x)!="object")&&(x={name:x}),i.name=x.name,i.checkPull=d(x.pull,!0),i.checkPut=d(x.put),i.revertClone=x.revertClone,e.group=i},en=function(){!Jr&&X&&N(X,"display","none")},tn=function(){!Jr&&X&&N(X,"display","")};Wt&&!Kr&&document.addEventListener("click",function(e){if(Ft)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),Ft=!1,!1},!0);var $e=function(e){if(F){e=e.touches?e.touches[0]:e;var d=Li(e.clientX,e.clientY);if(d){var i={};for(var x in e)e.hasOwnProperty(x)&&(i[x]=e[x]);i.target=i.rootEl=d,i.preventDefault=void 0,i.stopPropagation=void 0,d[Ae]._onDragOver(i)}}},Ai=function(e){F&&F.parentNode[Ae]._isOutsideThisEl(e.target)};function V(e,d){if(!(e&&e.nodeType&&e.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=d=Ne({},d),e[Ae]=this;var i={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Qr(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(C,E){C.setData("Text",E.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:V.supportPointer!==!1&&"PointerEvent"in window&&!pt,emptyInsertThreshold:5};bt.initializePlugins(this,e,i);for(var x in i)!(x in d)&&(d[x]=i[x]);qr(d);for(var A in this)A.charAt(0)==="_"&&typeof this[A]=="function"&&(this[A]=this[A].bind(this));this.nativeDraggable=d.forceFallback?!1:bi,this.nativeDraggable&&(this.options.touchStartThreshold=1),d.supportPointer?q(e,"pointerdown",this._onTapStart):(q(e,"mousedown",this._onTapStart),q(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(q(e,"dragover",this),q(e,"dragenter",this)),Bt.push(this.el),d.store&&d.store.get&&this.sort(d.store.get(this)||[]),Ne(this,vi())}V.prototype={constructor:V,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(it=null)},_getDirection:function(e,d){return typeof this.options.direction=="function"?this.options.direction.call(this,e,d,F):this.options.direction},_onTapStart:function(e){if(e.cancelable){var d=this,i=this.el,x=this.options,A=x.preventOnFilter,C=e.type,E=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,y=(E||e).target,m=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||y,L=x.filter;if(Pi(i),!F&&!(/mousedown|pointerdown/.test(C)&&e.button!==0||x.disabled)&&!m.isContentEditable&&!(!this.nativeDraggable&&pt&&y&&y.tagName.toUpperCase()==="SELECT")&&(y=De(y,x.draggable,i,!1),!(y&&y.animated)&&Ct!==y)){if(st=Te(y),vt=Te(y,x.draggable),typeof L=="function"){if(L.call(this,e,y,this)){pe({sortable:d,rootEl:m,name:"filter",targetEl:y,toEl:i,fromEl:i}),ge("filter",d,{evt:e}),A&&e.cancelable&&e.preventDefault();return}}else if(L&&(L=L.split(",").some(function(_){if(_=De(m,_.trim(),i,!1),_)return pe({sortable:d,rootEl:_,name:"filter",targetEl:y,fromEl:i,toEl:i}),ge("filter",d,{evt:e}),!0}),L)){A&&e.cancelable&&e.preventDefault();return}x.handle&&!De(m,x.handle,i,!1)||this._prepareDragStart(e,E,y)}}},_prepareDragStart:function(e,d,i){var x=this,A=x.el,C=x.options,E=A.ownerDocument,y;if(i&&!F&&i.parentNode===A){var m=le(i);if(ie=A,F=i,se=F.parentNode,Je=F.nextSibling,Ct=i,At=C.group,V.dragged=F,Ye={target:F,clientX:(d||e).clientX,clientY:(d||e).clientY},Or=Ye.clientX-m.left,Tr=Ye.clientY-m.top,this._lastX=(d||e).clientX,this._lastY=(d||e).clientY,F.style["will-change"]="all",y=function(){if(ge("delayEnded",x,{evt:e}),V.eventCanceled){x._onDrop();return}x._disableDelayedDragEvents(),!Sr&&x.nativeDraggable&&(F.draggable=!0),x._triggerDragStart(e,d),pe({sortable:x,name:"choose",originalEvent:e}),be(F,C.chosenClass,!0)},C.ignore.split(",").forEach(function(L){zr(F,L.trim(),Qt)}),q(E,"dragover",$e),q(E,"mousemove",$e),q(E,"touchmove",$e),q(E,"mouseup",x._onDrop),q(E,"touchend",x._onDrop),q(E,"touchcancel",x._onDrop),Sr&&this.nativeDraggable&&(this.options.touchStartThreshold=4,F.draggable=!0),ge("delayStart",this,{evt:e}),C.delay&&(!C.delayOnTouchOnly||d)&&(!this.nativeDraggable||!(yt||Ve))){if(V.eventCanceled){this._onDrop();return}q(E,"mouseup",x._disableDelayedDrag),q(E,"touchend",x._disableDelayedDrag),q(E,"touchcancel",x._disableDelayedDrag),q(E,"mousemove",x._delayedDragTouchMoveHandler),q(E,"touchmove",x._delayedDragTouchMoveHandler),C.supportPointer&&q(E,"pointermove",x._delayedDragTouchMoveHandler),x._dragStartTimer=setTimeout(y,C.delay)}else y()}},_delayedDragTouchMoveHandler:function(e){var d=e.touches?e.touches[0]:e;Math.max(Math.abs(d.clientX-this._lastX),Math.abs(d.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){F&&Qt(F),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;Q(e,"mouseup",this._disableDelayedDrag),Q(e,"touchend",this._disableDelayedDrag),Q(e,"touchcancel",this._disableDelayedDrag),Q(e,"mousemove",this._delayedDragTouchMoveHandler),Q(e,"touchmove",this._delayedDragTouchMoveHandler),Q(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,d){d=d||e.pointerType=="touch"&&e,!this.nativeDraggable||d?this.options.supportPointer?q(document,"pointermove",this._onTouchMove):d?q(document,"touchmove",this._onTouchMove):q(document,"mousemove",this._onTouchMove):(q(F,"dragend",this),q(ie,"dragstart",this._onDragStart));try{document.selection?It(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,d){if(ot=!1,ie&&F){ge("dragStarted",this,{evt:d}),this.nativeDraggable&&q(document,"dragover",Ai);var i=this.options;!e&&be(F,i.dragClass,!1),be(F,i.ghostClass,!0),V.active=this,e&&this._appendGhost(),pe({sortable:this,name:"start",originalEvent:d})}else this._nulling()},_emulateDragOver:function(){if(Ce){this._lastX=Ce.clientX,this._lastY=Ce.clientY,en();for(var e=document.elementFromPoint(Ce.clientX,Ce.clientY),d=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(Ce.clientX,Ce.clientY),e!==d);)d=e;if(F.parentNode[Ae]._isOutsideThisEl(e),d)do{if(d[Ae]){var i=void 0;if(i=d[Ae]._onDragOver({clientX:Ce.clientX,clientY:Ce.clientY,target:e,rootEl:d}),i&&!this.options.dragoverBubble)break}e=d}while(d=d.parentNode);tn()}},_onTouchMove:function(e){if(Ye){var d=this.options,i=d.fallbackTolerance,x=d.fallbackOffset,A=e.touches?e.touches[0]:e,C=X&&at(X,!0),E=X&&C&&C.a,y=X&&C&&C.d,m=Ot&&he&&Rr(he),L=(A.clientX-Ye.clientX+x.x)/(E||1)+(m?m[0]-Jt[0]:0)/(E||1),_=(A.clientY-Ye.clientY+x.y)/(y||1)+(m?m[1]-Jt[1]:0)/(y||1);if(!V.active&&!ot){if(i&&Math.max(Math.abs(A.clientX-this._lastX),Math.abs(A.clientY-this._lastY))<i)return;this._onDragStart(e,!0)}if(X){C?(C.e+=L-($t||0),C.f+=_-(Zt||0)):C={a:1,b:0,c:0,d:1,e:L,f:_};var a="matrix(".concat(C.a,",").concat(C.b,",").concat(C.c,",").concat(C.d,",").concat(C.e,",").concat(C.f,")");N(X,"webkitTransform",a),N(X,"mozTransform",a),N(X,"msTransform",a),N(X,"transform",a),$t=L,Zt=_,Ce=A}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!X){var e=this.options.fallbackOnBody?document.body:ie,d=le(F,!0,Ot,!0,e),i=this.options;if(Ot){for(he=e;N(he,"position")==="static"&&N(he,"transform")==="none"&&he!==document;)he=he.parentNode;he!==document.body&&he!==document.documentElement?(he===document&&(he=Me()),d.top+=he.scrollTop,d.left+=he.scrollLeft):he=Me(),Jt=Rr(he)}X=F.cloneNode(!0),be(X,i.ghostClass,!1),be(X,i.fallbackClass,!0),be(X,i.dragClass,!0),N(X,"transition",""),N(X,"transform",""),N(X,"box-sizing","border-box"),N(X,"margin",0),N(X,"top",d.top),N(X,"left",d.left),N(X,"width",d.width),N(X,"height",d.height),N(X,"opacity","0.8"),N(X,"position",Ot?"absolute":"fixed"),N(X,"zIndex","100000"),N(X,"pointerEvents","none"),V.ghost=X,e.appendChild(X),N(X,"transform-origin",Or/parseInt(X.style.width)*100+"% "+Tr/parseInt(X.style.height)*100+"%")}},_onDragStart:function(e,d){var i=this,x=e.dataTransfer,A=i.options;if(ge("dragStart",this,{evt:e}),V.eventCanceled){this._onDrop();return}ge("setupClone",this),V.eventCanceled||(oe=$r(F),oe.removeAttribute("id"),oe.draggable=!1,oe.style["will-change"]="",this._hideClone(),be(oe,this.options.chosenClass,!1),V.clone=oe),i.cloneId=It(function(){ge("clone",i),!V.eventCanceled&&(i.options.removeCloneOnHide||ie.insertBefore(oe,F),i._hideClone(),pe({sortable:i,name:"clone"}))}),!d&&be(F,A.dragClass,!0),d?(Ft=!0,i._loopId=setInterval(i._emulateDragOver,50)):(Q(document,"mouseup",i._onDrop),Q(document,"touchend",i._onDrop),Q(document,"touchcancel",i._onDrop),x&&(x.effectAllowed="move",A.setData&&A.setData.call(i,x,F)),q(document,"drop",i),N(F,"transform","translateZ(0)")),ot=!0,i._dragStartId=It(i._dragStarted.bind(i,d,e)),q(document,"selectstart",i),dt=!0,pt&&N(document.body,"user-select","none")},_onDragOver:function(e){var d=this.el,i=e.target,x,A,C,E=this.options,y=E.group,m=V.active,L=At===y,_=E.sort,a=ce||m,s,c=this,n=!1;if(ir)return;function f(M,G){ge(M,c,ke({evt:e,isOwner:L,axis:s?"vertical":"horizontal",revert:C,dragRect:x,targetRect:A,canSort:_,fromSortable:a,target:i,completed:o,onMove:function(z,j){return Tt(ie,d,F,x,z,le(z),e,j)},changed:r},G))}function h(){f("dragOverAnimationCapture"),c.captureAnimationState(),c!==a&&a.captureAnimationState()}function o(M){return f("dragOverCompleted",{insertion:M}),M&&(L?m._hideClone():m._showClone(c),c!==a&&(be(F,ce?ce.options.ghostClass:m.options.ghostClass,!1),be(F,E.ghostClass,!0)),ce!==c&&c!==V.active?ce=c:c===V.active&&ce&&(ce=null),a===c&&(c._ignoreWhileAnimating=i),c.animateAll(function(){f("dragOverAnimationComplete"),c._ignoreWhileAnimating=null}),c!==a&&(a.animateAll(),a._ignoreWhileAnimating=null)),(i===F&&!F.animated||i===d&&!i.animated)&&(it=null),!E.dragoverBubble&&!e.rootEl&&i!==document&&(F.parentNode[Ae]._isOutsideThisEl(e.target),!M&&$e(e)),!E.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),n=!0}function r(){Se=Te(F),Ke=Te(F,E.draggable),pe({sortable:c,name:"change",toEl:d,newIndex:Se,newDraggableIndex:Ke,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),i=De(i,E.draggable,d,!0),f("dragOver"),V.eventCanceled)return n;if(F.contains(e.target)||i.animated&&i.animatingX&&i.animatingY||c._ignoreWhileAnimating===i)return o(!1);if(Ft=!1,m&&!E.disabled&&(L?_||(C=se!==ie):ce===this||(this.lastPutMode=At.checkPull(this,m,F,e))&&y.checkPut(this,m,F,e))){if(s=this._getDirection(e,i)==="vertical",x=le(F),f("dragOverValid"),V.eventCanceled)return n;if(C)return se=ie,h(),this._hideClone(),f("revert"),V.eventCanceled||(Je?ie.insertBefore(F,Je):ie.appendChild(F)),o(!0);var u=dr(d,E.draggable);if(!u||wi(e,s,this)&&!u.animated){if(u===F)return o(!1);if(u&&d===e.target&&(i=u),i&&(A=le(i)),Tt(ie,d,F,x,i,A,e,!!i)!==!1)return h(),u&&u.nextSibling?d.insertBefore(F,u.nextSibling):d.appendChild(F),se=d,r(),o(!0)}else if(u&&Ti(e,s,this)){var l=lt(d,0,E,!0);if(l===F)return o(!1);if(i=l,A=le(i),Tt(ie,d,F,x,i,A,e,!1)!==!1)return h(),d.insertBefore(F,l),se=d,r(),o(!0)}else if(i.parentNode===d){A=le(i);var t=0,p,v=F.parentNode!==d,g=!Si(F.animated&&F.toRect||x,i.animated&&i.toRect||A,s),b=s?"top":"left",T=Ar(i,"top","top")||Ar(F,"top","top"),O=T?T.scrollTop:void 0;it!==i&&(p=A[b],xt=!1,Rt=!g&&E.invertSwap||v),t=Ci(e,i,A,s,g?1:E.swapThreshold,E.invertedSwapThreshold==null?E.swapThreshold:E.invertedSwapThreshold,Rt,it===i);var S;if(t!==0){var R=Te(F);do R-=t,S=se.children[R];while(S&&(N(S,"display")==="none"||S===X))}if(t===0||S===i)return o(!1);it=i,gt=t;var w=i.nextElementSibling,P=!1;P=t===1;var D=Tt(ie,d,F,x,i,A,e,P);if(D!==!1)return(D===1||D===-1)&&(P=D===1),ir=!0,setTimeout(Oi,30),h(),P&&!w?d.appendChild(F):i.parentNode.insertBefore(F,P?w:i),T&&Yr(T,0,O-T.scrollTop),se=F.parentNode,p!==void 0&&!Rt&&(Dt=Math.abs(p-le(i)[b])),r(),o(!0)}if(d.contains(F))return o(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){Q(document,"mousemove",this._onTouchMove),Q(document,"touchmove",this._onTouchMove),Q(document,"pointermove",this._onTouchMove),Q(document,"dragover",$e),Q(document,"mousemove",$e),Q(document,"touchmove",$e)},_offUpEvents:function(){var e=this.el.ownerDocument;Q(e,"mouseup",this._onDrop),Q(e,"touchend",this._onDrop),Q(e,"pointerup",this._onDrop),Q(e,"touchcancel",this._onDrop),Q(document,"selectstart",this)},_onDrop:function(e){var d=this.el,i=this.options;if(Se=Te(F),Ke=Te(F,i.draggable),ge("drop",this,{evt:e}),se=F&&F.parentNode,Se=Te(F),Ke=Te(F,i.draggable),V.eventCanceled){this._nulling();return}ot=!1,Rt=!1,xt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),or(this.cloneId),or(this._dragStartId),this.nativeDraggable&&(Q(document,"drop",this),Q(d,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),pt&&N(document.body,"user-select",""),N(F,"transform",""),e&&(dt&&(e.cancelable&&e.preventDefault(),!i.dropBubble&&e.stopPropagation()),X&&X.parentNode&&X.parentNode.removeChild(X),(ie===se||ce&&ce.lastPutMode!=="clone")&&oe&&oe.parentNode&&oe.parentNode.removeChild(oe),F&&(this.nativeDraggable&&Q(F,"dragend",this),Qt(F),F.style["will-change"]="",dt&&!ot&&be(F,ce?ce.options.ghostClass:this.options.ghostClass,!1),be(F,this.options.chosenClass,!1),pe({sortable:this,name:"unchoose",toEl:se,newIndex:null,newDraggableIndex:null,originalEvent:e}),ie!==se?(Se>=0&&(pe({rootEl:se,name:"add",toEl:se,fromEl:ie,originalEvent:e}),pe({sortable:this,name:"remove",toEl:se,originalEvent:e}),pe({rootEl:se,name:"sort",toEl:se,fromEl:ie,originalEvent:e}),pe({sortable:this,name:"sort",toEl:se,originalEvent:e})),ce&&ce.save()):Se!==st&&Se>=0&&(pe({sortable:this,name:"update",toEl:se,originalEvent:e}),pe({sortable:this,name:"sort",toEl:se,originalEvent:e})),V.active&&((Se==null||Se===-1)&&(Se=st,Ke=vt),pe({sortable:this,name:"end",toEl:se,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){ge("nulling",this),ie=F=se=X=Je=oe=Ct=He=Ye=Ce=dt=Se=Ke=st=vt=it=gt=ce=At=V.dragged=V.ghost=V.clone=V.active=null,Ut.forEach(function(e){e.checked=!0}),Ut.length=$t=Zt=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":F&&(this._onDragOver(e),Ri(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],d,i=this.el.children,x=0,A=i.length,C=this.options;x<A;x++)d=i[x],De(d,C.draggable,this.el,!1)&&e.push(d.getAttribute(C.dataIdAttr)||Ii(d));return e},sort:function(e,d){var i={},x=this.el;this.toArray().forEach(function(A,C){var E=x.children[C];De(E,this.options.draggable,x,!1)&&(i[A]=E)},this),d&&this.captureAnimationState(),e.forEach(function(A){i[A]&&(x.removeChild(i[A]),x.appendChild(i[A]))}),d&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,d){return De(e,d||this.options.draggable,this.el,!1)},option:function(e,d){var i=this.options;if(d===void 0)return i[e];var x=bt.modifyOption(this,e,d);typeof x<"u"?i[e]=x:i[e]=d,e==="group"&&qr(i)},destroy:function(){ge("destroy",this);var e=this.el;e[Ae]=null,Q(e,"mousedown",this._onTapStart),Q(e,"touchstart",this._onTapStart),Q(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(Q(e,"dragover",this),Q(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(d){d.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Bt.splice(Bt.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!He){if(ge("hideClone",this),V.eventCanceled)return;N(oe,"display","none"),this.options.removeCloneOnHide&&oe.parentNode&&oe.parentNode.removeChild(oe),He=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(He){if(ge("showClone",this),V.eventCanceled)return;F.parentNode==ie&&!this.options.group.revertClone?ie.insertBefore(oe,F):Je?ie.insertBefore(oe,Je):ie.appendChild(oe),this.options.group.revertClone&&this.animate(F,oe),N(oe,"display",""),He=!1}}};function Ri(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move"),e.cancelable&&e.preventDefault()}function Tt(e,d,i,x,A,C,E,y){var m,L=e[Ae],_=L.options.onMove,a;return window.CustomEvent&&!Ve&&!yt?m=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(m=document.createEvent("Event"),m.initEvent("move",!0,!0)),m.to=d,m.from=e,m.dragged=i,m.draggedRect=x,m.related=A||d,m.relatedRect=C||le(d),m.willInsertAfter=y,m.originalEvent=E,e.dispatchEvent(m),_&&(a=_.call(L,m,E)),a}function Qt(e){e.draggable=!1}function Oi(){ir=!1}function Ti(e,d,i){var x=le(lt(i.el,0,i.options,!0)),A=Zr(i.el,i.options,X),C=10;return d?e.clientX<A.left-C||e.clientY<x.top&&e.clientX<x.right:e.clientY<A.top-C||e.clientY<x.bottom&&e.clientX<x.left}function wi(e,d,i){var x=le(dr(i.el,i.options.draggable)),A=Zr(i.el,i.options,X),C=10;return d?e.clientX>A.right+C||e.clientY>x.bottom&&e.clientX>x.left:e.clientY>A.bottom+C||e.clientX>x.right&&e.clientY>x.top}function Ci(e,d,i,x,A,C,E,y){var m=x?e.clientY:e.clientX,L=x?i.height:i.width,_=x?i.top:i.left,a=x?i.bottom:i.right,s=!1;if(!E){if(y&&Dt<L*A){if(!xt&&(gt===1?m>_+L*C/2:m<a-L*C/2)&&(xt=!0),xt)s=!0;else if(gt===1?m<_+Dt:m>a-Dt)return-gt}else if(m>_+L*(1-A)/2&&m<a-L*(1-A)/2)return Di(d)}return s=s||E,s&&(m<_+L*C/2||m>a-L*C/2)?m>_+L/2?1:-1:0}function Di(e){return Te(F)<Te(e)?1:-1}function Ii(e){for(var d=e.tagName+e.className+e.src+e.href+e.textContent,i=d.length,x=0;i--;)x+=d.charCodeAt(i);return x.toString(36)}function Pi(e){Ut.length=0;for(var d=e.getElementsByTagName("input"),i=d.length;i--;){var x=d[i];x.checked&&Ut.push(x)}}function It(e){return setTimeout(e,0)}function or(e){return clearTimeout(e)}Wt&&q(document,"touchmove",function(e){(V.active||ot)&&e.cancelable&&e.preventDefault()});V.utils={on:q,off:Q,css:N,find:zr,is:function(e,d){return!!De(e,d,e,!1)},extend:pi,throttle:Xr,closest:De,toggleClass:be,clone:$r,index:Te,nextTick:It,cancelNextTick:or,detectDirection:Qr,getChild:lt};V.get=function(e){return e[Ae]};V.mount=function(){for(var e=arguments.length,d=new Array(e),i=0;i<e;i++)d[i]=arguments[i];d[0].constructor===Array&&(d=d[0]),d.forEach(function(x){if(!x.prototype||!x.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(x));x.utils&&(V.utils=ke(ke({},V.utils),x.utils)),bt.mount(x)})};V.create=function(e,d){return new V(e,d)};V.version=ci;var ae=[],ft,sr,ar=!1,qt,er,jt,ct;function Mi(){function e(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var d in this)d.charAt(0)==="_"&&typeof this[d]=="function"&&(this[d]=this[d].bind(this))}return e.prototype={dragStarted:function(d){var i=d.originalEvent;this.sortable.nativeDraggable?q(document,"dragover",this._handleAutoScroll):this.options.supportPointer?q(document,"pointermove",this._handleFallbackAutoScroll):i.touches?q(document,"touchmove",this._handleFallbackAutoScroll):q(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(d){var i=d.originalEvent;!this.options.dragOverBubble&&!i.rootEl&&this._handleAutoScroll(i)},drop:function(){this.sortable.nativeDraggable?Q(document,"dragover",this._handleAutoScroll):(Q(document,"pointermove",this._handleFallbackAutoScroll),Q(document,"touchmove",this._handleFallbackAutoScroll),Q(document,"mousemove",this._handleFallbackAutoScroll)),Cr(),Pt(),mi()},nulling:function(){jt=sr=ft=ar=ct=qt=er=null,ae.length=0},_handleFallbackAutoScroll:function(d){this._handleAutoScroll(d,!0)},_handleAutoScroll:function(d,i){var x=this,A=(d.touches?d.touches[0]:d).clientX,C=(d.touches?d.touches[0]:d).clientY,E=document.elementFromPoint(A,C);if(jt=d,i||this.options.forceAutoScrollFallback||yt||Ve||pt){tr(d,this.options,E,i);var y=ze(E,!0);ar&&(!ct||A!==qt||C!==er)&&(ct&&Cr(),ct=setInterval(function(){var m=ze(document.elementFromPoint(A,C),!0);m!==y&&(y=m,Pt()),tr(d,x.options,m,i)},10),qt=A,er=C)}else{if(!this.options.bubbleScroll||ze(E,!0)===Me()){Pt();return}tr(d,this.options,ze(E,!1),!1)}}},Ne(e,{pluginName:"scroll",initializeByDefault:!0})}function Pt(){ae.forEach(function(e){clearInterval(e.pid)}),ae=[]}function Cr(){clearInterval(ct)}var tr=Xr(function(e,d,i,x){if(d.scroll){var A=(e.touches?e.touches[0]:e).clientX,C=(e.touches?e.touches[0]:e).clientY,E=d.scrollSensitivity,y=d.scrollSpeed,m=Me(),L=!1,_;sr!==i&&(sr=i,Pt(),ft=d.scroll,_=d.scrollFn,ft===!0&&(ft=ze(i,!0)));var a=0,s=ft;do{var c=s,n=le(c),f=n.top,h=n.bottom,o=n.left,r=n.right,u=n.width,l=n.height,t=void 0,p=void 0,v=c.scrollWidth,g=c.scrollHeight,b=N(c),T=c.scrollLeft,O=c.scrollTop;c===m?(t=u<v&&(b.overflowX==="auto"||b.overflowX==="scroll"||b.overflowX==="visible"),p=l<g&&(b.overflowY==="auto"||b.overflowY==="scroll"||b.overflowY==="visible")):(t=u<v&&(b.overflowX==="auto"||b.overflowX==="scroll"),p=l<g&&(b.overflowY==="auto"||b.overflowY==="scroll"));var S=t&&(Math.abs(r-A)<=E&&T+u<v)-(Math.abs(o-A)<=E&&!!T),R=p&&(Math.abs(h-C)<=E&&O+l<g)-(Math.abs(f-C)<=E&&!!O);if(!ae[a])for(var w=0;w<=a;w++)ae[w]||(ae[w]={});(ae[a].vx!=S||ae[a].vy!=R||ae[a].el!==c)&&(ae[a].el=c,ae[a].vx=S,ae[a].vy=R,clearInterval(ae[a].pid),(S!=0||R!=0)&&(L=!0,ae[a].pid=setInterval((function(){x&&this.layer===0&&V.active._onTouchMove(jt);var P=ae[this.layer].vy?ae[this.layer].vy*y:0,D=ae[this.layer].vx?ae[this.layer].vx*y:0;typeof _=="function"&&_.call(V.dragged.parentNode[Ae],D,P,e,jt,ae[this.layer].el)!=="continue"||Yr(ae[this.layer].el,D,P)}).bind({layer:a}),24))),a++}while(d.bubbleScroll&&s!==m&&(s=ze(s,!1)));ar=L}},30),rn=function(e){var d=e.originalEvent,i=e.putSortable,x=e.dragEl,A=e.activeSortable,C=e.dispatchSortableEvent,E=e.hideGhostForTarget,y=e.unhideGhostForTarget;if(d){var m=i||A;E();var L=d.changedTouches&&d.changedTouches.length?d.changedTouches[0]:d,_=document.elementFromPoint(L.clientX,L.clientY);y(),m&&!m.el.contains(_)&&(C("spill"),this.onSpill({dragEl:x,putSortable:i}))}};function fr(){}fr.prototype={startIndex:null,dragStart:function(e){var d=e.oldDraggableIndex;this.startIndex=d},onSpill:function(e){var d=e.dragEl,i=e.putSortable;this.sortable.captureAnimationState(),i&&i.captureAnimationState();var x=lt(this.sortable.el,this.startIndex,this.options);x?this.sortable.el.insertBefore(d,x):this.sortable.el.appendChild(d),this.sortable.animateAll(),i&&i.animateAll()},drop:rn};Ne(fr,{pluginName:"revertOnSpill"});function cr(){}cr.prototype={onSpill:function(e){var d=e.dragEl,i=e.putSortable,x=i||this.sortable;x.captureAnimationState(),d.parentNode&&d.parentNode.removeChild(d),x.animateAll()},drop:rn};Ne(cr,{pluginName:"removeOnSpill"});V.mount(new Mi);V.mount(cr,fr);function ki(e){return e==null?e:JSON.parse(JSON.stringify(e))}function Fi(e){ur()&&lr(e)}function Bi(e){ur()?Et(e):Br(e)}let nn=null,on=null;function Dr(e=null,d=null){nn=e,on=d}function Ui(){return{data:nn,clonedData:on}}const Ir=Symbol("cloneElement");function ji(...e){var d,i;const x=(d=ur())==null?void 0:d.proxy;let A=null;const C=e[0];let[,E,y]=e;Array.isArray(H(E))||(y=E,E=null);let m=null;const{immediate:L=!0,clone:_=ki,customUpdate:a}=(i=H(y))!=null?i:{};function s(g){var b;const{from:T,oldIndex:O,item:S}=g;A=Array.from(T.childNodes);const R=H((b=H(E))==null?void 0:b[O]),w=_(R);Dr(R,w),S[Ir]=w}function c(g){const b=g.item[Ir];if(!ti(b)){if(zt(g.item),Ht(E)){const T=[...H(E)];E.value=xr(T,g.newDraggableIndex,b);return}xr(H(E),g.newDraggableIndex,b)}}function n(g){const{from:b,item:T,oldIndex:O,oldDraggableIndex:S,pullMode:R,clone:w}=g;if(Er(b,T,O),R==="clone"){zt(w);return}if(Ht(E)){const P=[...H(E)];E.value=gr(P,S);return}gr(H(E),S)}function f(g){if(a){a(g);return}const{from:b,item:T,oldIndex:O,newIndex:S}=g;if(zt(T),Er(b,T,O),Ht(E)){const R=[...H(E)];E.value=vr(R,O,S);return}vr(H(E),O,S)}function h(g){const{newIndex:b,oldIndex:T,from:O,to:S}=g;let R=null;const w=b===T&&O===S;try{if(w){let P=null;A==null||A.some((D,M)=>{if(P&&(A==null?void 0:A.length)!==S.childNodes.length)return O.insertBefore(P,D.nextSibling),!0;const G=S.childNodes[M];P=S==null?void 0:S.replaceChild(D,G)})}}catch(P){R=P}finally{A=null}Br(()=>{if(Dr(),R)throw R})}const o={onUpdate:f,onStart:s,onAdd:c,onRemove:n,onEnd:h};function r(g){const b=H(C);return g||(g=ri(b)?ni(b,x==null?void 0:x.$el):b),g&&!si(g)&&(g=g.$el),g||ei("Root element not found"),g}function u(){var g;const b=(g=H(y))!=null?g:{},T=Qn(b,["immediate","clone"]);return yr(T,(O,S)=>{ai(O)&&(T[O]=(R,...w)=>{const P=Ui();return li(R,P),S(R,...w)})}),oi(E===null?{}:o,T)}const l=g=>{g=r(g),m&&t.destroy(),m=new V(g,u())};rr(()=>y,()=>{m&&yr(u(),(g,b)=>{m==null||m.option(g,b)})},{deep:!0});const t={option:(g,b)=>m==null?void 0:m.option(g,b),destroy:()=>{m==null||m.destroy(),m=null},save:()=>m==null?void 0:m.save(),toArray:()=>m==null?void 0:m.toArray(),closest:(...g)=>m==null?void 0:m.closest(...g)},p=()=>t==null?void 0:t.option("disabled",!0),v=()=>t==null?void 0:t.option("disabled",!1);return Bi(()=>{L&&l()}),Fi(t.destroy),Vr({start:l,pause:p,resume:v},t)}const Ni=["update","start","add","remove","choose","unchoose","end","sort","filter","clone","move","change"];[...Ni.map(e=>`on${e.replace(/^\S/,d=>d.toUpperCase())}`)];const Vi={class:"wrapper"},Wi={class:"header"},Gi={class:"content"},Ki={class:"content_body",id:"videos"},Hi={key:0,class:"item_bg"},zi={class:"content_title"},Xi={class:"btns"},Yi={class:"btns"},$i=Nt({__name:"LivePreview",props:{config:{}},setup(e,{expose:d}){const i=e,x=Ue([]),A=Ue(),C=Ue(),E=ht({fullScreen:!1,activeNames:["1"],value1:"",index:1,group:8,total:9,status:!1,videoList:[{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null},{status:!1,url:"",talkurl:"",type:"customHls",key:null}],currentVideo:{}});ji(C,E.videoList,{animation:150,ghostClass:"ghost",onStart(){console.log("start"),console.table(E.videoList)},onUpdate(v){console.log("update",v),console.table(E.videoList),y((v.oldIndex||0)+1,(v.newIndex||0)+1)}});const y=(v,g)=>{[E.videoList[v],E.videoList[g]]=[E.videoList[g],E.videoList[v]],E.index=g,console.table(E.videoList)},m=ht({search:!0,defaultExpandAll:!1,data:[],currentProject:{},tag:{noShow:v=>!("status"in v.nodeDetail)||v.nodeDetail.status===null,label:v=>v.nodeDetail.status==="1"?"在线":"离线",type:v=>v.nodeDetail.status==="1"?"success":"danger"},click:v=>{v.type==="video"&&a(v)}});ht({search:!0,data:[],click:v=>{m.currentProject=v,a(v)}});const L=(v,g)=>{E.status=g,E.videoList.forEach((b,T)=>{var O;b.status===!0&&((O=x.value[T])==null||O.talkStop()),b.status=!1,v===T&&(E.videoList[T].status=!0,E.currentVideo=E.videoList[T],E.index=v)})},_=v=>v.indexOf("ws")!==-1?"ws":"customHls",a=v=>{if(E.videoList.some(g=>g.key===v.id)){vn.warning("当前摄像头已打开，请关闭后再试");return}On(v.id).then(g=>{s(v,{data:{data:g.data}},v.id)})},s=(v,g,b)=>{let T=!1;if(E.videoList.forEach((S,R)=>{S.url===(g.data.data||"")&&(h(R),E.status=!1,E.videoList[E.index]={...E.videoList[E.index],...v,url:g.data.data||"",type:_(g.data.data||""),key:b},T=!0)}),T)return;if(E.status){E.videoList[E.index]={...E.videoList[E.index],...v,url:g.data.data||"",type:_(g.data.data||""),key:b},E.status=!1;return}if(E.videoList[E.index].url===""&&E.index<E.total){E.videoList[E.index]={...E.videoList[E.index],...v,url:g.data.data||"",type:_(g.data.data||""),key:b};return}if(E.index<E.total){E.index++,E.videoList[E.index]={...E.videoList[E.index],...v,url:g.data.data||"",type:_(g.data.data||""),key:b},L(E.index,!1);return}else{E.videoList[1]={...E.videoList[1],...v,url:g.data.data||"",type:_(g.data.data||""),key:b},L(1,!1);return}},c=()=>{Rn({},Tn).then(v=>{m.data=n(v.data,"children")})};function n(v,g="children"){return v.map(b=>{if(b){if(b.type==="video")return b;b.name=b.name+` (${b.num})`,b[g]&&b[g].length&&n(b[g],g)}return b}),v}const f=()=>{m.currentProject.id},h=v=>{v?E.videoList[v]={status:E.videoList[v].status,url:"",talkurl:"",type:"customHls",key:null}:E.videoList=E.videoList.map(g=>{var b;return(b=A.value)==null||b.refresh(g,100),g={status:g.status,url:"",talkurl:"",type:"customHls",key:null},g})},o=()=>{const v=document.getElementById("videos");document.fullScreen||document.mozFullScreen||document.webkitIsFullScreen?(document.exitFullscreen&&document.exitFullscreen(),E.fullScreen=!1):(v.requestFullscreen&&v.requestFullscreen()||v.mozRequestFullScreen&&v.mozRequestFullScreen()||v.webkitRequestFullscreen&&v.webkitRequestFullscreen()||v.msRequestFullscreen&&v.msRequestFullscreen(),E.fullScreen=!0)},r=(v,g)=>{E.group=v,E.total=g;for(let b=g+1;b<E.videoList.length;b++)E.videoList[b].url=""},u=(v,g)=>{var b;(b=A.value)==null||b.setVolume(v,g)},l=v=>{var g;(g=x.value[v])==null||g.setVolume(v)},t=v=>{var g;(g=x.value[v])==null||g.talkStart()},p=v=>{var g;(g=x.value[v])==null||g.talkStop()};return Et(()=>{var v;if(c(),(v=i==null?void 0:i.config)!=null&&v.total)switch(E.total=i.config.total,i.config.total){case 1:E.group=24;break;case 4:E.group=12;break;case 9:E.group=8;break}}),d({getPlaySourceData:a,getVideoData:f}),lr(()=>{pr().setValue({videoList:E.videoList,group:E.group,total:E.total})}),gn(()=>{if(!i.config){const v=pr().putValue();for(let g in v)E[g]=v[g]}}),(v,g)=>{const b=En,T=yn,O=bn,S=Fr,R=Sn,w=Ln,P=An;return ue(),we("div",Vi,[me(P,{class:"box-card",style:{width:"100%",height:"100%"},shadow:"never"},{default:Le(()=>[me(Zn,{style:{width:"100%",height:"100%"},value:.15},{left:Le(()=>[_e("div",Wi,[me(T,{modelValue:H(E).activeNames,"onUpdate:modelValue":g[0]||(g[0]=D=>H(E).activeNames=D),style:{height:"calc(100% - 0px)",width:"100%",position:"relative"}},{default:Le(()=>[me(b,{class:"FZXX",title:"视频列表",name:"1"},{default:Le(()=>[Ze(v.$slots,"video",{},()=>[me(Nn,{config:H(m)},null,8,["config"])],!0)]),_:3}),me(b,{class:"ptz",title:"云台",name:"3"},{default:Le(()=>[me(Pn,{config:H(E).currentVideo},null,8,["config"])]),_:1})]),_:3},8,["modelValue"])])]),right:Le(()=>[_e("div",Gi,[_e("div",Ki,[me(w,{style:{height:"100%"},ref_key:"el",ref:C},{default:Le(()=>[(ue(!0),we(Pr,null,Mr(H(E).total,D=>{var M;return ue(),Qe(R,{onClick:G=>L(D,!0),class:nr({item_1:H(E).group===24,item_4:H(E).group===12,item_9:H(E).group===8,item:!0,item_selected:((M=H(E).videoList[D])==null?void 0:M.status)??!1}),style:{position:"relative"},span:H(E).group},{default:Le(()=>{var G,z,j,Z,ee,W,J;return[(G=H(E).videoList[D])!=null&&G.url?((z=H(E).videoList[D])==null?void 0:z.type)==="customHls"?(ue(),Qe(Kn,{key:1,style:{width:"100%",height:"100%"},"video-info":{live:!0,hotkey:!1,preload:"auto",autoplay:!0,video:{url:((j=H(E).videoList[D])==null?void 0:j.url)??"",type:"customHls"}}},null,8,["video-info"])):((Z=H(E).videoList[D])==null?void 0:Z.type)==="ws"?(ue(),Qe(Dn,{key:2,ref_for:!0,ref:xe=>{H(x)[D]=xe},config:{url:((ee=H(E).videoList[D])==null?void 0:ee.url)??"",index:D,talkurl:(W=H(E).videoList[D])==null?void 0:W.talkurl},onSetVolume:u},null,8,["config"])):(ue(),Qe(In,{key:3,"video-info":{live:!0,hotkey:!1,preload:"auto",autoplay:!0,video:{url:((J=H(E).videoList[D])==null?void 0:J.url)??"",type:"flv"}}},null,8,["video-info"])):(ue(),we("div",Hi," 待选中视频源 ")),me(S,{class:nr([{no_close:!1},"close"]),text:"",circle:"",color:"#232323",onClick:kr(xe=>h(D),["stop"])},{default:Le(()=>[me(O,{slot:"10"},{default:Le(()=>[me(H(xn))]),_:1})]),_:2},1032,["onClick"])]}),_:2},1032,["onClick","class","span"])}),256))]),_:1},512)]),_e("div",zi,[_e("div",Xi,[me(kn,{ref_key:"refIntercom",ref:A,onTalkStart:t,onTalkStop:p,onPutVolume:l},null,512)]),_e("div",Yi,[me(Mn,{onChangeFullScreen:o,onSetView:r,onCloseAll:h})])])])]),_:3})]),_:3})])}}}),so=Vt($i,[["__scopeId","data-v-156a9802"]]);export{so as default};
