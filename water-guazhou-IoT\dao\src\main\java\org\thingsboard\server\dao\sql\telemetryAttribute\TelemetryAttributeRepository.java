/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.telemetryAttribute;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.TelemetryAttributeEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/21/2017.
 */
@SqlDao
public interface TelemetryAttributeRepository extends CrudRepository<TelemetryAttributeEntity, String> {


    @Query("SELECT a FROM TelemetryAttributeEntity a WHERE a.deviceId = :deviceId " +
            "AND a.showName = :showName")
    List<TelemetryAttributeEntity> findByDeviceIdAndShowName(@Param("deviceId") String deviceId,
                                                             @Param("showName") String showName);

    @Query("SELECT DISTINCT a.type FROM AssetEntity a WHERE a.tenantId = :tenantId")
    List<String> findTenantAssetTypes(@Param("tenantId") String tenantId);

    @Query("SELECT a FROM TelemetryAttributeEntity a WHERE a.deviceId =:deviceId")
    List<TelemetryAttributeEntity> findAllByDeviceId(@Param("deviceId") String deviceId);



}
