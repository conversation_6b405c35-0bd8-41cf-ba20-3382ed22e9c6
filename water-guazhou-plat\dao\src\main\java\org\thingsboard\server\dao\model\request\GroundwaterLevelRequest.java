package org.thingsboard.server.dao.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 地下水水位查询参数
 */
@Data
@ApiModel(value = "地下水水位查询参数", description = "地下水水位查询参数")
public class GroundwaterLevelRequest {

    @ApiModelProperty(value = "页码", example = "1")
    private int page = 1;

    @ApiModelProperty(value = "每页条数", example = "10")
    private int size = 10;
    
    @ApiModelProperty(value = "测点ID")
    private String stationId;

    @ApiModelProperty(value = "测点名称")
    private String stationName;
    
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    
    @ApiModelProperty(value = "状态 (1-正常, 2-偏低, 3-偏高)")
    private Integer status;
    
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 