package org.thingsboard.server.dao.assay;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.AssayBaseSettingListRequest;
import org.thingsboard.server.dao.model.sql.assay.AssayBaseSetting;
import org.thingsboard.server.dao.sql.assay.AssayBaseSettingMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class AssayBaseSettingServiceImpl implements AssayBaseSettingService {

    @Autowired
    private AssayBaseSettingMapper assayBaseSettingMapper;


    @Override
    public PageData<AssayBaseSetting> findList(AssayBaseSettingListRequest request, TenantId tenantId) {
        Page<AssayBaseSetting> pageRequest = new Page<>(request.getPage(), request.getSize());
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        IPage<AssayBaseSetting> pageResult = assayBaseSettingMapper.findList(pageRequest, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void save(AssayBaseSetting entity, TenantId tenantId) {
        Date now = new Date();
        if (StringUtils.isNotBlank(entity.getId())) {
            entity.setUpdateTime(now);
            assayBaseSettingMapper.updateById(entity);
        } else {
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
            assayBaseSettingMapper.insert(entity);
        }
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            assayBaseSettingMapper.deleteById(id);
            // 关联删除其他数据
        }
    }
}
