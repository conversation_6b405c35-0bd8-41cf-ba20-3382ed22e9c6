// 系统配置
import request from '@/plugins/axios';

// 获取工单类型树
export const workOrderTypeList = (params?: { isDel?: number | null }) => {
  return request({
    url: '/api/ss/workOrder/type/tree',
    method: 'get',
    params
  });
};

// 获取字典列表
export const dictList = (params?: { pid?: string }) => {
  return request({
    url: '/api/ss/dict',
    method: 'get',
    params
  });
};

// 删除字典
export const delDict = (ids?: string[]) => {
  return request({
    url: '/api/ss/dict',
    method: 'delete',
    data: ids
  });
};

// 保存字典
export const saveDict = (params: {
  pid: string;
  code: string;
  name: string;
  orderNum: number;
}) => {
  return request({
    url: '/api/ss/dict',
    method: 'post',
    data: params
  });
};
