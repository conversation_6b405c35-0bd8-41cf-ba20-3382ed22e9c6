<template>
  <div class="tools-temp-wrapper">
    <div
      id="tool-search-poi"
      class="esri-widget"
    >
      <el-select
        v-model="state.queryType"
        placeholder="Select"
        class="poi-selector"
      >
        <el-option
          label="poi"
          value="1"
        />
        <el-option
          label="地名"
          value="7"
        />
      </el-select>
      <el-select
        v-model="state.value"
        filterable
        remote
        reserve-keyword
        placeholder="请输入地名"
        remote-show-suffix
        :remote-method="remoteMethod"
        :loading="state.loading"
        :class="'poi-search'"
        @change="handleChange"
      >
        <el-option
          v-for="item in state.options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button
        :icon="SearchIcon"
        type="primary"
        class="poi-button"
        @click="handleChange"
      >
      </el-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point.js'
import Graphic from '@arcgis/core/Graphic.js'
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol.js'
import { Search as SearchIcon } from '@element-plus/icons-vue'
import useWidgets from '@/hooks/arcgis/useWidgets'
import { gotoAndHighLight } from '@/utils/MapHelper'
import { queryTdt } from '@/api/mapservice/utils'
import { getMapLocationImageUrl } from '@/utils/URLHelper'

const props = defineProps<{
  view?: __esri.MapView
}>()
const state = reactive<{
  value: string
  loading: boolean
  options: NormalOption[]
  queryType: string
}>({
  value: '',
  loading: false,
  options: [],
  queryType: '1'
})
const remoteMethod = (query: string) => {
  if (!props.view) return
  if (query) {
    state.loading = true
    queryTdt({ keyWord: query, queryType: state.queryType })
      .then(res => {
        state.options = res.data.pois?.map(item => {
          return {
            label: item.name,
            value: item.hotPointID,
            data: item
          }
        }) || []
      })
      .finally(() => {
        state.loading = false
      })
  } else {
    state.options = []
  }
}
const handleChange = () => {
  const data = state.options.find(item => item.value === state.value)
  if (!data) return
  const location = data.data.lonlat?.split(',')
  const point = new Point({
    longitude: location?.[0],
    latitude: location?.[1],
    spatialReference: props.view?.spatialReference
  })
  const mark = new Graphic({
    geometry: point,
    symbol: new PictureMarkerSymbol({
      url: getMapLocationImageUrl()
    })
  })
  props.view?.graphics.removeAll()
  props.view?.graphics.add(mark)
  gotoAndHighLight(props.view, mark, {
    avoidHighlight: true,
    zoom: 16
  })
}
const { addCustomWidget } = useWidgets()
const init = async () => {
  if (!props.view) return
  addCustomWidget(props.view, 'tool-search-poi', 'top-right')
}
defineExpose({
  init
})
</script>
<style lang="scss" scoped>
#tool-search-poi {
  display: flex;
  align-items: center;
  border-radius: var(--el-border-radius-base);
}
.tools-temp-wrapper {
  position: absolute;
}
.custom-toolbar {
  line-height: 32px;
  text-align: center;
  display: flex;

  .tool-icon {
    margin: auto;
  }
}
.poi-selector {
  width: 80px;
  :deep(.el-input__wrapper) {
    border-radius: 4px 0 0 4px;
  }
}
.poi-search {
  :deep(.el-input__wrapper) {
    border-radius: 0;
  }
}
.poi-button {
  margin: 0;
  border-radius: 0 4px 4px 0;
}
</style>
