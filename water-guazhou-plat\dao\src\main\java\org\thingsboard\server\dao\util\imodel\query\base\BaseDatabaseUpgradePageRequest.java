package org.thingsboard.server.dao.util.imodel.query.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseUpgrade;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

import java.util.Date;

/**
 * 平台管理-数据库升级记录对象 base_database_upgrade
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@ApiModel(value = "数据库升级记录", description = "平台管理-数据库升级记录实体类")
@Data
public class BaseDatabaseUpgradePageRequest extends PageableQueryEntity<BaseDatabaseUpgrade> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 升级前版本号
     */
    @ApiModelProperty(value = "升级前版本号")
    private String versionFrom;

    /**
     * 升级后版本号
     */
    @ApiModelProperty(value = "升级后版本号")
    private String versionTo;

    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executionTime;

    /**
     * 操作者ID
     */
    @ApiModelProperty(value = "操作者ID")
    private String operatorId;

    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态")
    private String status;

    /**
     * 升级SQL文件路径
     */
    @ApiModelProperty(value = "升级SQL文件路径")
    private String sqlFilePath;

}
