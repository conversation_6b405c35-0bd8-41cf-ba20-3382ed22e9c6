package org.thingsboard.server.dao.origin;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.OriginDataEntity;
import org.thingsboard.server.dao.sql.orginData.JpaOriginDataDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/3 14:05
 */
@Service
public class OriginDataServiceImpl implements OriginDataService {

    @Autowired
    private JpaOriginDataDao originDataDao;

    @Override
    public void saveOriginData(List<OriginDataEntity> originDataEntities) {
        originDataDao.saveOriginData(originDataEntities);
    }

    @Override
    public OriginDataEntity saveOriginData(OriginDataEntity originDataEntity) {
       return originDataDao.saveOriginData(originDataEntity);
    }

    @Override
    public List<OriginDataEntity> getOriginDataFormIdAndTime(String dataSourceId, long startTime, long endTime) {
        return originDataDao.getOriginDataFormIdAndTime(dataSourceId, startTime, endTime);
    }

    @Override
    public OriginDataEntity getLastOriginDataFormId(String dataSourceId) {
        return originDataDao.getLastOriginDataFormId(dataSourceId);
    }

    @Override
    public List<OriginDataEntity> getOriginDataFormId(String dataSourceId) {
        return originDataDao.getOriginDataFormId(dataSourceId);
    }
}
