import{_ as B}from"./index-C9hz-UZb.js";import{d as E,a0 as P,c as F,cN as A,r as O,o as z,g as f,n as g,p as u,q as m,F as h,aB as _,aJ as b,bo as G,bR as Y,h as j,bh as v,G as I,an as D,aw as J,N as U,J as H,O as K,bB as N,C as X}from"./index-r0dFAfgr.js";import{_ as Z}from"./ArcView-DpMnCY82.js";import tt from"./ListWindow-WS05QqV0.js";import{W as et}from"./index-EuPVwsd1.js";import{w as at}from"./Point-WxyopZva.js";import{g as ot,n as it}from"./MapView-DaoQedLH.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as st}from"./ViewHelper-BGCZjxXH.js";import{g as nt}from"./URLHelper-B9aplt5w.js";import{c as lt,n as rt}from"./onemap-CEunQziB.js";import"./PopLayout-BP55MvL7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const pt={class:"water-quality-distribution"},ct={class:"map-container"},dt={key:0,class:"infowindow-container"},mt={class:"popup-content"},ut={class:"popup-label"},ht={class:"popup-value"},ft={key:0,class:"popup-unit"},gt={class:"data-panel"},yt={class:"water-quality-list"},vt={class:"sample-time"},wt=E({__name:"index",setup(_t){const S=P(),M=F();A();const c={},n=O({statistics:[{className:"text-blue",title:"监测站点",count:0,unit:"个"},{className:"text-green",title:"达标数据",count:0,unit:"条"},{className:"text-orange",title:"超标数据",count:0,unit:"条"},{className:"text-red",title:"异常数据",count:0,unit:"条"}],stationList:[],waterQualityData:[],loading:!1,windows:[],startDate:"2025-06",endDate:"2025-06",selectedStation:"",selectedQualityLevel:"",showReportDialog:!1,currentReportData:null}),x=t=>{switch(t){case"I类":return"quality-level-1";case"II类":return"quality-level-2";case"III类":return"quality-level-3";case"IV类":return"quality-level-4";case"V类":return"quality-level-5";default:return"quality-level-unknown"}},L=t=>{if(!t)return"yyyy-MM-dd";const s=new Date(t),i=s.getFullYear(),r=String(s.getMonth()+1).padStart(2,"0"),l=String(s.getDate()).padStart(2,"0");return`${i}-${r}-${l}`},k=t=>{const s=[],i=["II类","III类","IV类"];t.forEach(o=>{const p=new Date(Date.now()),d=i[Math.floor(Math.random()*i.length)],y={dissolvedOxygen:4.5+Math.random()*3,permanganate:2+Math.random()*8,cod:15+Math.random()*20,ammoniaNitrogen:.2+Math.random()*1.5,totalPhosphorus:.05+Math.random()*.3,totalNitrogen:.3+Math.random()*1.5,fecalColiform:500+Math.random()*15e3,ph:6.5+Math.random()*2,inflow:80+Math.random()*40,outflow:75+Math.random()*35,bod5:5+Math.random()*15};s.push({id:`${o.stationId||o.id}`,stationId:o.stationId||o.id,stationName:`${o.name||o.stationName}`,sampleTime:p.toISOString(),waterQualityLevel:d,reportId:`report-${o.stationId||o.id}`,indicators:y})}),n.waterQualityData=s;const r=t.length,l=s.filter(o=>o.waterQualityLevel==="II类").length,e=s.filter(o=>o.waterQualityLevel==="III类").length,a=s.filter(o=>o.waterQualityLevel==="IV类").length;n.statistics[0].count=r,n.statistics[1].count=l,n.statistics[2].count=e,n.statistics[3].count=a},W=t=>{console.log("查看检测报告:",t),n.currentReportData=t,n.showReportDialog=!0},Q=()=>{n.showReportDialog=!1,n.currentReportData=null},C=async t=>{console.log("地图加载完成，开始初始化"),c.view=t,await new Promise(s=>setTimeout(s,1e3)),st(c.view,s=>{var r,l,e;const i=(r=s.results)==null?void 0:r[0];if(i&&i.type==="graphic"){const a=(e=(l=i.graphic)==null?void 0:l.attributes)==null?void 0:e.row;a!=null&&a.fromWaterQuality&&V(a)}}),console.log("开始调用initMapMarkers"),await $()},$=async()=>{var t,s;try{if(!c.view){console.log("地图视图未准备好");return}c.view.graphics.removeAll();try{const i=await lt({projectId:(t=S.selectedProject)==null?void 0:t.value}),r=((s=i.data)==null?void 0:s.data)||i.data||[];if(console.log("获取到的原始数据:",r),Array.isArray(r)&&r.length>0){const l=r.filter(e=>{const a=e.name||e.stationName||"";return a.includes("污水")||a.includes("污水厂")||a.includes("污水处理厂")});console.log("过滤后的污水厂数据:",l),n.stationList=l,l.forEach((e,a)=>{var p;const o=(p=e.location)==null?void 0:p.split(",");if((o==null?void 0:o.length)===2&&o[0]&&o[1]){const d=parseFloat(o[0]),y=parseFloat(o[1]);if(console.log(`站点坐标: ${d}, ${y}`),!isNaN(d)&&!isNaN(y)){const T=new at({longitude:d,latitude:y,spatialReference:c.view.spatialReference}),q=new ot({geometry:T,symbol:new it({width:40,height:40,yoffset:20,url:nt("污水处理厂.png")}),attributes:{row:{...e,id:e.stationId||e.id||`sewage-plant-${a}`,stationId:e.stationId||e.id,name:e.name||e.stationName||`污水厂${a+1}`,fromWaterQuality:!0}}});c.view.graphics.add(q),console.log(`成功添加标记点: ${e.name||e.stationName}`)}else console.log("坐标解析失败:",o)}else console.log("位置信息无效:",e.location)}),k(l)}}catch(i){console.error("API调用失败:",i)}console.log("地图标记初始化完成，总共添加了",c.view.graphics.length,"个标记点")}catch(i){console.error("初始化地图标记失败:",i)}},V=async t=>{var r;if(!t||!c.view)return;n.windows.length=0;const s=c.view.graphics.find(l=>{var e,a,o,p;return((a=(e=l.attributes)==null?void 0:e.row)==null?void 0:a.id)===t.id||((p=(o=l.attributes)==null?void 0:o.row)==null?void 0:p.stationId)===t.stationId});if(!s)return;const i=s.geometry;try{const e=((r=(await rt({stationId:t.stationId||t.id})).data)==null?void 0:r.data)||{},a=[{label:"污水厂名称",value:t.name||t.stationName||"污水厂"}];e.todayWaterSupply!==void 0&&e.todayWaterSupply!==null&&a.push({label:"今日处理量",value:`${Number(e.todayWaterSupply).toFixed(1)} m³`}),e.yesterdayWaterSupply!==void 0&&e.yesterdayWaterSupply!==null&&a.push({label:"昨日处理量",value:`${Number(e.yesterdayWaterSupply).toFixed(1)} m³`}),e.monthWaterSupply!==void 0&&e.monthWaterSupply!==null&&a.push({label:"本月处理量",value:`${Number(e.monthWaterSupply).toFixed(1)} m³`}),e.status!==void 0&&a.push({label:"运行状态",value:e.status===1?"正常":"异常"}),[{label:"COD",value:"15.2 mg/L"},{label:"BOD5",value:"8.5 mg/L"},{label:"氨氮",value:"2.1 mg/L"},{label:"pH值",value:"7.2"}].forEach(d=>{a.push(d)});const p={id:t.id||t.stationId,title:t.name||t.stationName||"污水厂",visible:!1,x:i.x,y:i.y,offsetY:-30,attributes:{values:a,id:t.id||t.stationId}};n.windows.push(p),await N(),w(t.id||t.stationId)}catch{const e={id:t.id||t.stationId,title:t.name||t.stationName||"污水厂",visible:!1,x:i.x,y:i.y,offsetY:-30,attributes:{values:[{label:"污水厂名称",value:t.name||t.stationName||"污水厂"},{label:"数据获取",value:"暂时无法获取详细数据"}],id:t.id||t.stationId}};n.windows.push(e),await N(),w(t.id||t.stationId)}},w=t=>{const s=n.windows.find(i=>i.id===t);s&&(n.windows.forEach(i=>{i.visible=i.id===t}),s.visible=!0)},R=(t,s)=>{};return z(()=>{}),(t,s)=>{const i=U,r=H,l=K,e=B;return f(),g("div",pt,[u("div",ct,[m(Z,{ref_key:"refArcView",ref:M,onLoaded:C},{default:h(()=>{var a;return[(a=n.windows)!=null&&a.length?(f(),g("div",dt,[(f(!0),g(_,null,b(n.windows,o=>G((f(),j(tt,{key:o.id,ref_for:!0,ref:"refPop"+o.id,view:c.view,config:o,onToggled:p=>R(o,p)},{default:h(()=>[u("div",mt,[(f(!0),g(_,null,b(o.attributes.values,(p,d)=>(f(),g("div",{key:d,class:"popup-item"},[u("span",ut,v(p.label)+":",1),u("span",ht,[I(v(p.value)+" ",1),p.unit?(f(),g("span",ft,v(p.unit),1)):D("",!0)])]))),128))])]),_:2},1032,["view","config","onToggled"])),[[Y,o.visible]])),128))])):D("",!0)]}),_:1},512)]),u("div",gt,[m(e,{class:"panel-content",title:"水质分布",overlay:""},{default:h(()=>[u("div",yt,[m(l,{data:n.waterQualityData,loading:n.loading,style:{width:"100%"},"max-height":"calc(100vh - 400px)","show-header":!0,size:"small"},{default:h(()=>[m(i,{prop:"sampleTime",label:"采样时间","min-width":"100"},{default:h(({row:a})=>[u("div",vt,v(L(a.sampleTime)),1)]),_:1}),m(i,{prop:"stationName",label:"采样点位","min-width":"100"}),m(i,{prop:"waterQualityLevel",label:"水质等级","min-width":"80"},{default:h(({row:a})=>[u("span",{class:J(x(a.waterQualityLevel))},v(a.waterQualityLevel),3)]),_:1}),m(i,{label:"检测报告","min-width":"80"},{default:h(({row:a})=>[m(r,{type:"primary",link:"",size:"small",onClick:o=>W(a)},{default:h(()=>s[1]||(s[1]=[I(" 查看 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","loading"])])]),_:1})]),m(et,{modelValue:n.showReportDialog,"onUpdate:modelValue":s[0]||(s[0]=a=>n.showReportDialog=a),data:n.currentReportData,onClose:Q},null,8,["modelValue","data"])])}}}),Oe=X(wt,[["__scopeId","data-v-f4d78e49"]]);export{Oe as default};
