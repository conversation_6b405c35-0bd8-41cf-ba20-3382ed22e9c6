"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6772],{92835:(e,t,i)=>{i.d(t,{Z:()=>g});var s,r=i(43697),n=i(96674),o=i(70586),l=i(35463),a=i(5600),h=(i(75215),i(67676),i(71715)),d=i(52011),u=i(30556);let c=s=class extends n.wq{static get allTime(){return p}static get empty(){return f}constructor(e){super(e),this.end=null,this.start=null}readEnd(e,t){return null!=t.end?new Date(t.end):null}writeEnd(e,t){t.end=e?e.getTime():null}get isAllTime(){return this.equals(s.allTime)}get isEmpty(){return this.equals(s.empty)}readStart(e,t){return null!=t.start?new Date(t.start):null}writeStart(e,t){t.start=e?e.getTime():null}clone(){return new s({end:this.end,start:this.start})}equals(e){if(!e)return!1;const t=(0,o.pC)(this.start)?this.start.getTime():this.start,i=(0,o.pC)(this.end)?this.end.getTime():this.end,s=(0,o.pC)(e.start)?e.start.getTime():e.start,r=(0,o.pC)(e.end)?e.end.getTime():e.end;return t===s&&i===r}expandTo(e){if(this.isEmpty||this.isAllTime)return this.clone();const t=(0,o.yw)(this.start,(t=>(0,l.JE)(t,e))),i=(0,o.yw)(this.end,(t=>{const i=(0,l.JE)(t,e);return t.getTime()===i.getTime()?i:(0,l.Nm)(i,1,e)}));return new s({start:t,end:i})}intersection(e){if(!e)return this.clone();if(this.isEmpty||e.isEmpty)return s.empty;if(this.isAllTime)return e.clone();if(e.isAllTime)return this.clone();const t=(0,o.R2)(this.start,-1/0,(e=>e.getTime())),i=(0,o.R2)(this.end,1/0,(e=>e.getTime())),r=(0,o.R2)(e.start,-1/0,(e=>e.getTime())),n=(0,o.R2)(e.end,1/0,(e=>e.getTime()));let l,a;if(r>=t&&r<=i?l=r:t>=r&&t<=n&&(l=t),i>=r&&i<=n?a=i:n>=t&&n<=i&&(a=n),null!=l&&null!=a&&!isNaN(l)&&!isNaN(a)){const e=new s;return e.start=l===-1/0?null:new Date(l),e.end=a===1/0?null:new Date(a),e}return s.empty}offset(e,t){if(this.isEmpty||this.isAllTime)return this.clone();const i=new s,{start:r,end:n}=this;return(0,o.pC)(r)&&(i.start=(0,l.Nm)(r,e,t)),(0,o.pC)(n)&&(i.end=(0,l.Nm)(n,e,t)),i}union(e){if(!e||e.isEmpty)return this.clone();if(this.isEmpty)return e.clone();if(this.isAllTime||e.isAllTime)return p.clone();const t=(0,o.pC)(this.start)&&(0,o.pC)(e.start)?new Date(Math.min(this.start.getTime(),e.start.getTime())):null,i=(0,o.pC)(this.end)&&(0,o.pC)(e.end)?new Date(Math.max(this.end.getTime(),e.end.getTime())):null;return new s({start:t,end:i})}};(0,r._)([(0,a.Cb)({type:Date,json:{write:{allowNull:!0}}})],c.prototype,"end",void 0),(0,r._)([(0,h.r)("end")],c.prototype,"readEnd",null),(0,r._)([(0,u.c)("end")],c.prototype,"writeEnd",null),(0,r._)([(0,a.Cb)({readOnly:!0,json:{read:!1}})],c.prototype,"isAllTime",null),(0,r._)([(0,a.Cb)({readOnly:!0,json:{read:!1}})],c.prototype,"isEmpty",null),(0,r._)([(0,a.Cb)({type:Date,json:{write:{allowNull:!0}}})],c.prototype,"start",void 0),(0,r._)([(0,h.r)("start")],c.prototype,"readStart",null),(0,r._)([(0,u.c)("start")],c.prototype,"writeStart",null),c=s=(0,r._)([(0,d.j)("esri.TimeExtent")],c);const p=new c,f=new c({start:void 0,end:void 0}),g=c},46791:(e,t,i)=>{i.d(t,{Z:()=>R});var s,r=i(43697),n=i(3894),o=i(32448),l=i(22974),a=i(70586),h=i(71143);!function(e){e[e.ADD=1]="ADD",e[e.REMOVE=2]="REMOVE",e[e.MOVE=4]="MOVE"}(s||(s={}));var d,u=i(1654),c=i(5600),p=i(75215),f=i(52421),g=i(52011),m=i(58971),y=i(10661);const _=new h.Z(class{constructor(){this.target=null,this.cancellable=!1,this.defaultPrevented=!1,this.item=void 0,this.type=void 0}preventDefault(){this.cancellable&&(this.defaultPrevented=!0)}reset(e){this.defaultPrevented=!1,this.item=e}},void 0,(e=>{e.item=null,e.target=null,e.defaultPrevented=!1,e.cancellable=!1})),v=()=>{};function b(e){return e?e instanceof x?e.toArray():e.length?Array.prototype.slice.apply(e):[]:[]}function w(e){if(e&&e.length)return e[0]}function C(e,t,i,s){const r=Math.min(e.length-i,t.length-s);let n=0;for(;n<r&&e[i+n]===t[s+n];)n++;return n}function E(e,t,i,s){t&&t.forEach(((t,r,n)=>{e.push(t),E(e,i.call(s,t,r,n),i,s)}))}const F=new Set,I=new Set,S=new Set,D=new Map;let M=0,x=d=class extends o.Z.EventedAccessor{static isCollection(e){return null!=e&&e instanceof d}constructor(e){super(e),this._chgListeners=[],this._notifications=null,this._timer=null,this._observable=new y.s,this.length=0,this._items=[],Object.defineProperty(this,"uid",{value:M++})}normalizeCtorArgs(e){return e?Array.isArray(e)||e instanceof d?{items:e}:e:{}}destroy(){this.removeAll()}*[Symbol.iterator](){yield*this.items}get items(){return(0,m.it)(this._observable),this._items}set items(e){this._emitBeforeChanges(s.ADD)||(this._splice(0,this.length,b(e)),this._emitAfterChanges(s.ADD))}hasEventListener(e){return"change"===e?this._chgListeners.length>0:this._emitter.hasEventListener(e)}on(e,t){if("change"===e){const e=this._chgListeners,i={removed:!1,callback:t};return e.push(i),this._notifications&&this._notifications.push({listeners:e.slice(),items:this._items.slice(),changes:[]}),{remove(){this.remove=v,i.removed=!0,e.splice(e.indexOf(i),1)}}}return this._emitter.on(e,t)}once(e,t){const i=this.on(e,t);return{remove(){i.remove()}}}add(e,t){if((0,m.it)(this._observable),this._emitBeforeChanges(s.ADD))return this;const i=this.getNextIndex(t??null);return this._splice(i,0,[e]),this._emitAfterChanges(s.ADD),this}addMany(e,t=this._items.length){if((0,m.it)(this._observable),!e||!e.length)return this;if(this._emitBeforeChanges(s.ADD))return this;const i=this.getNextIndex(t);return this._splice(i,0,b(e)),this._emitAfterChanges(s.ADD),this}at(e){if((0,m.it)(this._observable),(e=Math.trunc(e)||0)<0&&(e+=this.length),!(e<0||e>=this.length))return this._items[e]}removeAll(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return[];const e=this._splice(0,this.length)||[];return this._emitAfterChanges(s.REMOVE),e}clone(){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.map(l.d9)})}concat(...e){(0,m.it)(this._observable);const t=e.map(b);return this._createNewInstance({items:this._items.concat(...t)})}drain(e,t){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const i=(0,a.j0)(this._splice(0,this.length)),r=i.length;for(let s=0;s<r;s++)e.call(t,i[s],s,i);this._emitAfterChanges(s.REMOVE)}every(e,t){return(0,m.it)(this._observable),this._items.every(e,t)}filter(e,t){let i;return(0,m.it)(this._observable),i=2===arguments.length?this._items.filter(e,t):this._items.filter(e),this._createNewInstance({items:i})}find(e,t){return(0,m.it)(this._observable),this._items.find(e,t)}findIndex(e,t){return(0,m.it)(this._observable),this._items.findIndex(e,t)}flatten(e,t){(0,m.it)(this._observable);const i=[];return E(i,this,e,t),new d(i)}forEach(e,t){return(0,m.it)(this._observable),this._items.forEach(e,t)}getItemAt(e){return(0,m.it)(this._observable),this._items[e]}getNextIndex(e){(0,m.it)(this._observable);const t=this.length;return(e=e??t)<0?e=0:e>t&&(e=t),e}includes(e,t=0){return(0,m.it)(this._observable),this._items.includes(e,t)}indexOf(e,t=0){return(0,m.it)(this._observable),this._items.indexOf(e,t)}join(e=","){return(0,m.it)(this._observable),this._items.join(e)}lastIndexOf(e,t=this.length-1){return(0,m.it)(this._observable),this._items.lastIndexOf(e,t)}map(e,t){(0,m.it)(this._observable);const i=this._items.map(e,t);return new d({items:i})}reorder(e,t=this.length-1){(0,m.it)(this._observable);const i=this.indexOf(e);if(-1!==i){if(t<0?t=0:t>=this.length&&(t=this.length-1),i!==t){if(this._emitBeforeChanges(s.MOVE))return e;this._splice(i,1),this._splice(t,0,[e]),this._emitAfterChanges(s.MOVE)}return e}}pop(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const e=w(this._splice(this.length-1,1));return this._emitAfterChanges(s.REMOVE),e}push(...e){return(0,m.it)(this._observable),this._emitBeforeChanges(s.ADD)||(this._splice(this.length,0,e),this._emitAfterChanges(s.ADD)),this.length}reduce(e,t){(0,m.it)(this._observable);const i=this._items;return 2===arguments.length?i.reduce(e,t):i.reduce(e)}reduceRight(e,t){(0,m.it)(this._observable);const i=this._items;return 2===arguments.length?i.reduceRight(e,t):i.reduceRight(e)}remove(e){return(0,m.it)(this._observable),this.removeAt(this.indexOf(e))}removeAt(e){if((0,m.it)(this._observable),e<0||e>=this.length||this._emitBeforeChanges(s.REMOVE))return;const t=w(this._splice(e,1));return this._emitAfterChanges(s.REMOVE),t}removeMany(e){if((0,m.it)(this._observable),!e||!e.length||this._emitBeforeChanges(s.REMOVE))return[];const t=e instanceof d?e.toArray():e,i=this._items,r=[],n=t.length;for(let e=0;e<n;e++){const s=t[e],n=i.indexOf(s);if(n>-1){const s=1+C(t,i,e+1,n+1),o=this._splice(n,s);o&&o.length>0&&r.push.apply(r,o),e+=s-1}}return this._emitAfterChanges(s.REMOVE),r}reverse(){if((0,m.it)(this._observable),this._emitBeforeChanges(s.MOVE))return this;const e=this._splice(0,this.length);return e&&(e.reverse(),this._splice(0,0,e)),this._emitAfterChanges(s.MOVE),this}shift(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const e=w(this._splice(0,1));return this._emitAfterChanges(s.REMOVE),e}slice(e=0,t=this.length){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.slice(e,t)})}some(e,t){return(0,m.it)(this._observable),this._items.some(e,t)}sort(e){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.MOVE))return this;const t=(0,a.j0)(this._splice(0,this.length));return arguments.length?t.sort(e):t.sort(),this._splice(0,0,t),this._emitAfterChanges(s.MOVE),this}splice(e,t,...i){(0,m.it)(this._observable);const r=(t?s.REMOVE:0)|(i.length?s.ADD:0);if(this._emitBeforeChanges(r))return[];const n=this._splice(e,t,i)||[];return this._emitAfterChanges(r),n}toArray(){return(0,m.it)(this._observable),this._items.slice()}toJSON(){return(0,m.it)(this._observable),this.toArray()}toLocaleString(){return(0,m.it)(this._observable),this._items.toLocaleString()}toString(){return(0,m.it)(this._observable),this._items.toString()}unshift(...e){return(0,m.it)(this._observable),!e.length||this._emitBeforeChanges(s.ADD)||(this._splice(0,0,e),this._emitAfterChanges(s.ADD)),this.length}_createNewInstance(e){return new this.constructor(e)}_splice(e,t,i){const s=this._items,r=this.itemType;let n,o;if(!this._notifications&&this.hasEventListener("change")&&(this._notifications=[{listeners:this._chgListeners.slice(),items:this._items.slice(),changes:[]}],this._timer&&this._timer.remove(),this._timer=(0,u.Os)((()=>this._dispatchChange()))),t){if(o=s.splice(e,t),this.hasEventListener("before-remove")){const t=_.acquire();t.target=this,t.cancellable=!0;for(let i=0,r=o.length;i<r;i++)n=o[i],t.reset(n),this.emit("before-remove",t),t.defaultPrevented&&(o.splice(i,1),s.splice(e,0,n),e+=1,i-=1,r-=1);_.release(t)}if(this.length=this._items.length,this.hasEventListener("after-remove")){const e=_.acquire();e.target=this,e.cancellable=!1;const t=o.length;for(let i=0;i<t;i++)e.reset(o[i]),this.emit("after-remove",e);_.release(e)}}if(i&&i.length){if(r){const e=[];for(const t of i){const i=r.ensureType(t);null==i&&null!=t||e.push(i)}i=e}const t=this.hasEventListener("before-add"),n=this.hasEventListener("after-add"),o=e===this.length;if(t||n){const r=_.acquire();r.target=this,r.cancellable=!0;const l=_.acquire();l.target=this,l.cancellable=!1;for(const a of i)t?(r.reset(a),this.emit("before-add",r),r.defaultPrevented||(o?s.push(a):s.splice(e++,0,a),this._set("length",s.length),n&&(l.reset(a),this.emit("after-add",l)))):(o?s.push(a):s.splice(e++,0,a),this._set("length",s.length),l.reset(a),this.emit("after-add",l));_.release(l),_.release(r)}else{if(o)for(const e of i)s.push(e);else s.splice(e,0,...i);this._set("length",s.length)}}return(i&&i.length||o&&o.length)&&this._notifyChangeEvent(i,o),o}_emitBeforeChanges(e){let t=!1;if(this.hasEventListener("before-changes")){const i=_.acquire();i.target=this,i.cancellable=!0,i.type=e,this.emit("before-changes",i),t=i.defaultPrevented,_.release(i)}return t}_emitAfterChanges(e){if(this.hasEventListener("after-changes")){const t=_.acquire();t.target=this,t.cancellable=!1,t.type=e,this.emit("after-changes",t),_.release(t)}this._observable.notify()}_notifyChangeEvent(e,t){this.hasEventListener("change")&&this._notifications&&this._notifications[this._notifications.length-1].changes.push({added:e,removed:t})}_dispatchChange(){if(this._timer&&(this._timer.remove(),this._timer=null),!this._notifications)return;const e=this._notifications;this._notifications=null;for(const t of e){const e=t.changes;F.clear(),I.clear(),S.clear();for(const{added:t,removed:i}of e){if(t)if(0===S.size&&0===I.size)for(const e of t)F.add(e);else for(const e of t)I.has(e)?(S.add(e),I.delete(e)):S.has(e)||F.add(e);if(i)if(0===S.size&&0===F.size)for(const e of i)I.add(e);else for(const e of i)F.has(e)?F.delete(e):(S.delete(e),I.add(e))}const i=n.Z.acquire();F.forEach((e=>{i.push(e)}));const s=n.Z.acquire();I.forEach((e=>{s.push(e)}));const r=this._items,o=t.items,l=n.Z.acquire();if(S.forEach((e=>{o.indexOf(e)!==r.indexOf(e)&&l.push(e)})),t.listeners&&(i.length||s.length||l.length)){const e={target:this,added:i,removed:s,moved:l},r=t.listeners.length;for(let i=0;i<r;i++){const s=t.listeners[i];s.removed||s.callback.call(this,e)}}n.Z.release(i),n.Z.release(s),n.Z.release(l)}F.clear(),I.clear(),S.clear()}};x.ofType=e=>{if(!e)return d;if(D.has(e))return D.get(e);let t=null;if("function"==typeof e)t=e.prototype.declaredClass;else if(e.base)t=e.base.prototype.declaredClass;else for(const i in e.typeMap){const s=e.typeMap[i].prototype.declaredClass;t?t+=` | ${s}`:t=s}let i=class extends d{};return(0,r._)([(0,f.c)({Type:e,ensureType:"function"==typeof e?(0,p.se)(e):(0,p.N7)(e)})],i.prototype,"itemType",void 0),i=(0,r._)([(0,g.j)(`esri.core.Collection<${t}>`)],i),D.set(e,i),i},(0,r._)([(0,c.Cb)()],x.prototype,"length",void 0),(0,r._)([(0,c.Cb)()],x.prototype,"items",null),x=d=(0,r._)([(0,g.j)("esri.core.Collection")],x);const R=x},52421:(e,t,i)=>{function s(e){return(t,i)=>{t[i]=e}}i.d(t,{c:()=>s})},35463:(e,t,i)=>{i.d(t,{JE:()=>o,Nm:()=>n,rJ:()=>l}),i(80442);const s={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},r={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function n(e,t,i){const s=new Date(e.getTime());if(t&&i){const e=r[i],{getter:n,setter:o,multiplier:l}=e;if("months"===i){const e=function(e,t){const i=new Date(e,t+1,1);return i.setDate(0),i.getDate()}(s.getFullYear(),s.getMonth()+t);s.getDate()>e&&s.setDate(e)}s[o](s[n]()+t*l)}return s}function o(e,t){switch(t){case"milliseconds":return new Date(e.getTime());case"seconds":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case"minutes":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case"hours":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case"days":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case"weeks":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case"months":return new Date(e.getFullYear(),e.getMonth(),1);case"years":return new Date(e.getFullYear(),0,1);case"decades":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case"centuries":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function l(e,t,i){return 0===e?0:e*s[t]/s[i]}},79235:(e,t,i)=>{i.d(t,{Z:()=>v});var s,r=i(43697),n=i(67676),o=i(35454),l=i(96674),a=i(67900),h=i(20941),d=i(5600),u=(i(75215),i(71715)),c=i(52011),p=i(30556);const f=(0,o.w)()({orthometric:"gravity-related-height",gravity_related_height:"gravity-related-height",ellipsoidal:"ellipsoidal"}),g=f.jsonValues.slice();(0,n.e$)(g,"orthometric");const m=(0,o.w)()({meter:"meters",foot:"feet","us-foot":"us-feet","clarke-foot":"clarke-feet","clarke-yard":"clarke-yards","clarke-link":"clarke-links","sears-yard":"sears-yards","sears-foot":"sears-feet","sears-chain":"sears-chains","benoit-1895-b-chain":"benoit-1895-b-chains","indian-yard":"indian-yards","indian-1937-yard":"indian-1937-yards","gold-coast-foot":"gold-coast-feet","sears-1922-truncated-chain":"sears-1922-truncated-chains","50-kilometers":"50-kilometers","150-kilometers":"150-kilometers"});let y=s=class extends l.wq{constructor(e){super(e),this.heightModel="gravity-related-height",this.heightUnit="meters",this.vertCRS=null}writeHeightModel(e,t,i){return f.write(e,t,i)}readHeightModel(e,t,i){return f.read(e)||(i&&i.messages&&i.messages.push(function(e,t){return new h.Z("height-model:unsupported",`Height model of value '${e}' is not supported`,t)}(e,{context:i})),null)}readHeightUnit(e,t,i){return m.read(e)||(i&&i.messages&&i.messages.push(_(e,{context:i})),null)}readHeightUnitService(e,t,i){return(0,a.$C)(e)||m.read(e)||(i&&i.messages&&i.messages.push(_(e,{context:i})),null)}readVertCRS(e,t){return t.vertCRS||t.ellipsoid||t.geoid}clone(){return new s({heightModel:this.heightModel,heightUnit:this.heightUnit,vertCRS:this.vertCRS})}equals(e){return!!e&&(this===e||this.heightModel===e.heightModel&&this.heightUnit===e.heightUnit&&this.vertCRS===e.vertCRS)}static deriveUnitFromSR(e,t){const i=(0,a.cM)(t);return new s({heightModel:e.heightModel,heightUnit:i,vertCRS:e.vertCRS})}write(e,t){return t={origin:"web-scene",...t},super.write(e,t)}static fromJSON(e){if(!e)return null;const t=new s;return t.read(e,{origin:"web-scene"}),t}};function _(e,t){return new h.Z("height-unit:unsupported",`Height unit of value '${e}' is not supported`,t)}(0,r._)([(0,d.Cb)({type:f.apiValues,constructOnly:!0,json:{origins:{"web-scene":{type:g,default:"ellipsoidal"}}}})],y.prototype,"heightModel",void 0),(0,r._)([(0,p.c)("web-scene","heightModel")],y.prototype,"writeHeightModel",null),(0,r._)([(0,u.r)(["web-scene","service"],"heightModel")],y.prototype,"readHeightModel",null),(0,r._)([(0,d.Cb)({type:m.apiValues,constructOnly:!0,json:{origins:{"web-scene":{type:m.jsonValues,write:m.write}}}})],y.prototype,"heightUnit",void 0),(0,r._)([(0,u.r)("web-scene","heightUnit")],y.prototype,"readHeightUnit",null),(0,r._)([(0,u.r)("service","heightUnit")],y.prototype,"readHeightUnitService",null),(0,r._)([(0,d.Cb)({type:String,constructOnly:!0,json:{origins:{"web-scene":{write:!0}}}})],y.prototype,"vertCRS",void 0),(0,r._)([(0,u.r)("service","vertCRS",["vertCRS","ellipsoid","geoid"])],y.prototype,"readVertCRS",null),y=s=(0,r._)([(0,c.j)("esri.geometry.HeightModelInfo")],y);const v=y},10608:(e,t,i)=>{i.r(t),i.d(t,{default:()=>ee});var s=i(43697),r=i(51773),n=i(3172),o=i(20102),l=i(22974),a=i(92604),h=i(70586),d=i(16453),u=i(78286),c=i(95330),p=i(17452),f=i(5600),g=i(75215),m=i(71715),y=i(52011),_=i(30556),v=i(87085),b=i(54295),w=i(17287),C=i(38009),E=i(16859),F=i(72965),I=i(20559),S=i(36030),D=i(96674);i(67676);let M=class extends D.wq{constructor(e){super(e),this.field=null,this.type=null}clone(){return console.warn(".clone() is not implemented for "+this.declaredClass),null}};(0,s._)([(0,f.Cb)({type:String,json:{write:{enabled:!0,isRequired:!0}}})],M.prototype,"field",void 0),(0,s._)([(0,f.Cb)({readOnly:!0,nonNullable:!0,json:{read:!1}})],M.prototype,"type",void 0),M=(0,s._)([(0,y.j)("esri.layers.pointCloudFilters.PointCloudFilter")],M);const x=M;var R;let T=R=class extends x{constructor(e){super(e),this.requiredClearBits=null,this.requiredSetBits=null,this.type="bitfield"}clone(){return new R({field:this.field,requiredClearBits:(0,l.d9)(this.requiredClearBits),requiredSetBits:(0,l.d9)(this.requiredSetBits)})}};(0,s._)([(0,f.Cb)({type:[g.z8],json:{write:{enabled:!0,overridePolicy(){return{enabled:!0,isRequired:!this.requiredSetBits}}}}})],T.prototype,"requiredClearBits",void 0),(0,s._)([(0,f.Cb)({type:[g.z8],json:{write:{enabled:!0,overridePolicy(){return{enabled:!0,isRequired:!this.requiredClearBits}}}}})],T.prototype,"requiredSetBits",void 0),(0,s._)([(0,S.J)({pointCloudBitfieldFilter:"bitfield"})],T.prototype,"type",void 0),T=R=(0,s._)([(0,y.j)("esri.layers.pointCloudFilters.PointCloudBitfieldFilter")],T);const A=T;var O;let j=O=class extends x{constructor(e){super(e),this.includedReturns=[],this.type="return"}clone(){return new O({field:this.field,includedReturns:(0,l.d9)(this.includedReturns)})}};(0,s._)([(0,f.Cb)({type:[["firstOfMany","last","lastOfMany","single"]],json:{write:{enabled:!0,isRequired:!0}}})],j.prototype,"includedReturns",void 0),(0,s._)([(0,S.J)({pointCloudReturnFilter:"return"})],j.prototype,"type",void 0),j=O=(0,s._)([(0,y.j)("esri.layers.pointCloudFilters.PointCloudReturnFilter")],j);const Z=j;var V;let L=V=class extends x{constructor(e){super(e),this.mode="exclude",this.type="value",this.values=[]}clone(){return new V({field:this.field,mode:this.mode,values:(0,l.d9)(this.values)})}};(0,s._)([(0,f.Cb)({type:["exclude","include"],json:{write:{enabled:!0,isRequired:!0}}})],L.prototype,"mode",void 0),(0,s._)([(0,S.J)({pointCloudValueFilter:"value"})],L.prototype,"type",void 0),(0,s._)([(0,f.Cb)({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],L.prototype,"values",void 0),L=V=(0,s._)([(0,y.j)("esri.layers.pointCloudFilters.PointCloudValueFilter")],L);const N={key:"type",base:x,typeMap:{value:L,bitfield:A,return:Z}};var P,q=i(21506),B=i(1231),k=i(53518),$=i(11223),Y=i(23030),H=i(25848);let U=P=class extends H.Z{constructor(e){super(e),this.type="point-cloud-rgb",this.field=null}clone(){return new P({...this.cloneProperties(),field:(0,l.d9)(this.field)})}};(0,s._)([(0,S.J)({pointCloudRGBRenderer:"point-cloud-rgb"})],U.prototype,"type",void 0),(0,s._)([(0,f.Cb)({type:String,json:{write:!0}})],U.prototype,"field",void 0),U=P=(0,s._)([(0,y.j)("esri.renderers.PointCloudRGBRenderer")],U);const J=U;var z=i(14008),K=i(46329);const G={key:"type",base:H.Z,typeMap:{"point-cloud-class-breaks":Y.Z,"point-cloud-rgb":J,"point-cloud-stretch":z.Z,"point-cloud-unique-value":K.Z},errorContext:"renderer"};var Q=i(32163);const W=(0,k.v)();let X=class extends((0,I.Vt)((0,w.Y)((0,C.q)((0,E.I)((0,F.M)((0,d.R)((0,b.V)(v.Z)))))))){constructor(...e){super(...e),this.operationalLayerType="PointCloudLayer",this.popupEnabled=!0,this.popupTemplate=null,this.opacity=1,this.filters=[],this.fields=null,this.fieldsIndex=null,this.outFields=null,this.path=null,this.legendEnabled=!0,this.renderer=null,this.type="point-cloud"}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}get defaultPopupTemplate(){return this.attributeStorageInfo?this.createPopupTemplate():null}getFieldDomain(e){const t=this.fieldsIndex.get(e);return t&&t.domain?t.domain:null}readServiceFields(e,t,i){return Array.isArray(e)?e.map((e=>{const t=new B.Z;return"FieldTypeInteger"===e.type&&((e=(0,l.d9)(e)).type="esriFieldTypeInteger"),t.read(e,i),t})):Array.isArray(t.attributeStorageInfo)?t.attributeStorageInfo.map((e=>new B.Z({name:e.name,type:"ELEVATION"===e.name?"double":"integer"}))):null}set elevationInfo(e){this._set("elevationInfo",e),this._validateElevationInfo()}writeRenderer(e,t,i,s){(0,u.RB)("layerDefinition.drawingInfo.renderer",e.write({},s),t)}load(e){const t=(0,h.pC)(e)?e.signal:null,i=this.loadFromPortal({supportedTypes:["Scene Service"]},e).catch(c.r9).then((()=>this._fetchService(t)));return this.addResolvingPromise(i),Promise.resolve(this)}createPopupTemplate(e){const t=(0,Q.eZ)(this,e);return t&&(this._formatPopupTemplateReturnsField(t),this._formatPopupTemplateRGBField(t)),t}_formatPopupTemplateReturnsField(e){const t=this.fieldsIndex.get("RETURNS");if(!t)return;const i=e.fieldInfos?.find((e=>e.fieldName===t.name));if(!i)return;const s=new $.Z({name:"pcl-returns-decoded",title:t.alias||t.name,expression:`\n        var returnValue = $feature.${t.name};\n        return (returnValue % 16) + " / " + Floor(returnValue / 16);\n      `});e.expressionInfos=[...e.expressionInfos||[],s],i.fieldName="expression/pcl-returns-decoded"}_formatPopupTemplateRGBField(e){const t=this.fieldsIndex.get("RGB");if(!t)return;const i=e.fieldInfos?.find((e=>e.fieldName===t.name));if(!i)return;const s=new $.Z({name:"pcl-rgb-decoded",title:t.alias||t.name,expression:`\n        var rgb = $feature.${t.name};\n        var red = Floor(rgb / 65536, 0);\n        var green = Floor((rgb - (red * 65536)) / 256,0);\n        var blue = rgb - (red * 65536) - (green * 256);\n\n        return "rgb(" + red + "," + green + "," + blue + ")";\n      `});e.expressionInfos=[...e.expressionInfos||[],s],i.fieldName="expression/pcl-rgb-decoded"}async queryCachedStatistics(e,t){if(await this.load(t),!this.attributeStorageInfo)throw new o.Z("scenelayer:no-cached-statistics","Cached statistics are not available for this layer");const i=this.fieldsIndex.get(e);if(!i)throw new o.Z("pointcloudlayer:field-unexisting",`Field '${e}' does not exist on the layer`);for(const e of this.attributeStorageInfo)if(e.name===i.name){const i=(0,p.v_)(this.parsedUrl.path,`./statistics/${e.key}`);return(0,n.default)(i,{query:{f:"json",token:this.apiKey},responseType:"json",signal:t?t.signal:null}).then((e=>e.data))}throw new o.Z("pointcloudlayer:no-cached-statistics","Cached statistics for this attribute are not available")}async saveAs(e,t){return this._debouncedSaveOperations(I.xp.SAVE_AS,{...t,getTypeKeywords:()=>this._getTypeKeywords(),portalItemLayerType:"point-cloud"},e)}async save(){const e={getTypeKeywords:()=>this._getTypeKeywords(),portalItemLayerType:"point-cloud"};return this._debouncedSaveOperations(I.xp.SAVE,e)}validateLayer(e){if(e.layerType&&"PointCloud"!==e.layerType)throw new o.Z("pointcloudlayer:layer-type-not-supported","PointCloudLayer does not support this layer type",{layerType:e.layerType});if(isNaN(this.version.major)||isNaN(this.version.minor))throw new o.Z("layer:service-version-not-supported","Service version is not supported.",{serviceVersion:this.version.versionString,supportedVersions:"1.x-2.x"});if(this.version.major>2)throw new o.Z("layer:service-version-too-new","Service version is too new.",{serviceVersion:this.version.versionString,supportedVersions:"1.x-2.x"})}hasCachedStatistics(e){return null!=this.attributeStorageInfo&&this.attributeStorageInfo.some((t=>t.name===e))}_getTypeKeywords(){return["PointCloud"]}_validateElevationInfo(){const e=this.elevationInfo;e&&("absolute-height"!==e.mode&&a.Z.getLogger(this.declaredClass).warn(".elevationInfo=","Point cloud layers only support absolute-height elevation mode"),e.featureExpressionInfo&&"0"!==e.featureExpressionInfo.expression&&a.Z.getLogger(this.declaredClass).warn(".elevationInfo=","Point cloud layers do not support featureExpressionInfo"))}};(0,s._)([(0,f.Cb)({type:["PointCloudLayer"]})],X.prototype,"operationalLayerType",void 0),(0,s._)([(0,f.Cb)(q.C_)],X.prototype,"popupEnabled",void 0),(0,s._)([(0,f.Cb)({type:r.Z,json:{name:"popupInfo",write:!0}})],X.prototype,"popupTemplate",void 0),(0,s._)([(0,f.Cb)({readOnly:!0,json:{read:!1}})],X.prototype,"defaultPopupTemplate",null),(0,s._)([(0,f.Cb)({readOnly:!0,json:{write:!1,read:!1,origins:{"web-document":{write:!1,read:!1}}}})],X.prototype,"opacity",void 0),(0,s._)([(0,f.Cb)({type:["show","hide"]})],X.prototype,"listMode",void 0),(0,s._)([(0,f.Cb)({types:[N],json:{origins:{service:{read:{source:"filters"}}},name:"layerDefinition.filters",write:!0}})],X.prototype,"filters",void 0),(0,s._)([(0,f.Cb)({type:[B.Z]})],X.prototype,"fields",void 0),(0,s._)([(0,f.Cb)(W.fieldsIndex)],X.prototype,"fieldsIndex",void 0),(0,s._)([(0,m.r)("service","fields",["fields","attributeStorageInfo"])],X.prototype,"readServiceFields",null),(0,s._)([(0,f.Cb)(W.outFields)],X.prototype,"outFields",void 0),(0,s._)([(0,f.Cb)({readOnly:!0})],X.prototype,"attributeStorageInfo",void 0),(0,s._)([(0,f.Cb)(q.PV)],X.prototype,"elevationInfo",null),(0,s._)([(0,f.Cb)({type:String,json:{origins:{"web-scene":{read:!0,write:!0},"portal-item":{read:!0,write:!0}},read:!1}})],X.prototype,"path",void 0),(0,s._)([(0,f.Cb)(q.rn)],X.prototype,"legendEnabled",void 0),(0,s._)([(0,f.Cb)({types:G,json:{origins:{service:{read:{source:"drawingInfo.renderer"}}},name:"layerDefinition.drawingInfo.renderer",write:{target:{"layerDefinition.drawingInfo.renderer":{types:G},"layerDefinition.drawingInfo.transparency":{type:Number}}}}})],X.prototype,"renderer",void 0),(0,s._)([(0,_.c)("renderer")],X.prototype,"writeRenderer",null),(0,s._)([(0,f.Cb)({json:{read:!1},readOnly:!0})],X.prototype,"type",void 0),X=(0,s._)([(0,y.j)("esri.layers.PointCloudLayer")],X);const ee=X},53518:(e,t,i)=>{i.d(t,{v:()=>l});var s=i(92604),r=i(1231),n=i(99514),o=i(35671);function l(){return{fields:{type:[r.Z],value:null},fieldsIndex:{readOnly:!0,get(){return new n.Z(this.fields||[])}},outFields:{type:[String],json:{read:!1},set:function(e){this._userOutFields=e,this.notifyChange("outFields")},get:function(){const e=this._userOutFields;if(!e||!e.length)return null;if(e.includes("*"))return["*"];if(!this.fields)return e;for(const t of e){const i=this.fieldsIndex?.has(t);i||s.Z.getLogger("esri.layers.support.fieldProperties").error("field-attributes-layer:invalid-field",`Invalid field ${t} found in outFields`,{layer:this,outFields:e})}return(0,o.Q0)(this.fieldsIndex,e)}}}}},32163:(e,t,i)=>{i.d(t,{eZ:()=>u});var s=i(51773),r=i(35671),n=i(84649),o=(i(63801),i(48074),i(38745),i(9190)),l=(i(10214),i(71423),i(44951),i(422)),a=i(63061);const h=["oid","global-id"],d=["oid","global-id","guid"];function u({displayField:e,editFieldsInfo:t,fields:i,objectIdField:a,title:h},d){if(!i)return null;const u=function({editFieldsInfo:e,fields:t,objectIdField:i},s){return function(e,t){const i=e;return t&&(e=e.filter((e=>!t.includes(e.type)))),e===i&&(e=e.slice()),e.sort(f),e}(t??[],s?.ignoreFieldTypes||v).map((t=>new l.Z({fieldName:t.name,isEditable:m(t,e),label:t.alias,format:y(t),visible:p(t,{editFieldsInfo:e,objectIdField:i,visibleFieldNames:s?.visibleFieldNames})})))}({editFieldsInfo:t,fields:i,objectIdField:a},d);if(!u.length)return null;const c=function(e){const t=(0,r.O5)(e),{titleBase:i}=e;return t?`${i}: {${t.trim()}}`:i??""}({titleBase:h,fields:i,displayField:e}),g=[new o.Z,new n.Z];return new s.Z({title:c,content:g,fieldInfos:u})}const c=[/^fnode_$/i,/^tnode_$/i,/^lpoly_$/i,/^rpoly_$/i,/^poly_$/i,/^subclass$/i,/^subclass_$/i,/^rings_ok$/i,/^rings_nok$/i,/shape/i,/perimeter/i,/objectid/i,/_i$/i],p=(e,{editFieldsInfo:t,objectIdField:i,visibleFieldNames:s})=>s?s.has(e.name):!(g(e.name,t)||i&&e.name===i||h.includes(e.type)||c.some((t=>t.test(e.name))));function f(e,t){return"oid"===e.type?-1:"oid"===t.type?1:_(e)?-1:_(t)?1:(e.alias||e.name).toLocaleLowerCase().localeCompare((t.alias||t.name).toLocaleLowerCase())}function g(e,t){if(!e||!t)return!1;const{creationDateField:i,creatorField:s,editDateField:r,editorField:n}=t;return[i&&i.toLowerCase(),s&&s.toLowerCase(),r&&r.toLowerCase(),n&&n.toLowerCase()].includes(e.toLowerCase())}function m(e,t){return e.editable&&!d.includes(e.type)&&!g(e.name,t)}function y(e){switch(e.type){case"small-integer":case"integer":case"single":return new a.Z({digitSeparator:!0,places:0});case"double":return new a.Z({digitSeparator:!0,places:2});case"date":return new a.Z({dateFormat:"long-month-day-year"});default:return"string"===e.type&&(0,r.Ec)(e.name)?new a.Z({digitSeparator:!0,places:0}):null}}function _(e){return"name"===(e.name&&e.name.toLowerCase())||"name"===(e.alias&&e.alias.toLowerCase())}const v=["geometry","blob","raster","guid","xml"]}}]);