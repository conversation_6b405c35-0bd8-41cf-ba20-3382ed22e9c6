<template>
  <div
    v-loading="state.loading"
    class="cxcfx"
  >
    <VChart
      ref="refChart"
      :option="state.cxcOption"
    ></VChart>
  </div>
</template>
<script lang="ts" setup>
import { useDetector } from '@/hooks/echarts'
import { initCXXOption } from '../echart'
import { GetDMANRWByXiaoQu } from '@/api/smartDesision'

const state = reactive<{
  cxcOption: any
  loading: boolean
}>({
  loading: false,
  cxcOption: initCXXOption()
})

const refreshData = (type = '1') => {
  state.loading = true
  GetDMANRWByXiaoQu({
    type
  })
    .then(res => {
      const data = res.data.data || {}
      const x: any[] = []
      const supply: any[] = []
      const use: any[] = []
      const nrw: any[] = []
      data.map(item => {
        x.push(item.time)
        supply.push(item.supply)
        use.push(item.use)
        nrw.push(item.nrw)
      })
      state.cxcOption = initCXXOption(x, supply, use, nrw)
    })
    .finally(() => {
      state.loading = false
    })
}
const refChart = ref()
const detector = useDetector()
onMounted(() => {
  detector.listenToMush(document.documentElement, () => {
    refChart.value?.resize()
  })
  refreshData()
})
defineExpose({
  refreshData
})
</script>
<style lang="scss" scoped>
.cxcfx {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.cxcfx{
  .el-loading-mask{
    background-color: rgba(0,0,0,0.5);
  }
}
</style>
