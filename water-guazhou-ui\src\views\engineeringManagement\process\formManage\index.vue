<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="searchConfig" />
    <CardTable ref="refTable" class="card-table" :config="tableConfig" />
    <DialogForm ref="refForm" :config="formConfig" />
    <el-dialog
      v-model="state.formVisible"
      title="表单设计"
      :fullscreen="true"
      :close-on-press-escape="false"
      @close="closeFormDialog"
    >
      <v-form-designer
        ref="vfDesigner"
        :designer-config="designerConfig"
        :form-json="state.formJson"
        :form-data="state.formData"
        :option-data="state.optionData"
      ></v-form-designer>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="state.formVisible = false">关闭</el-button>
          <el-button
            :loading="state.submitting"
            type="primary"
            @click="saveFormDesigner"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <DialogForm ref="refFormRender" :config="formRenderConfig">
      <v-form-render
        ref="vFormRef"
        :form-json="state.formJson"
        :form-data="state.formData"
        :option-data="state.optionData"
      >
      </v-form-render>
    </DialogForm>
  </div>
</template>

<script lang="ts" setup>
// 新增按钮
import {
  Delete,
  Edit,
  Plus,
  Search as SearchIcon,
  TrendCharts,
  View
} from '@element-plus/icons-vue';
import { SLConfirm } from '@/utils/Message';
import {
  delForm,
  ediForm,
  formList,
  getFormById
} from '@/api/engineeringManagement/process';
import useGlobal from '@/hooks/global/useGlobal';
import router from '@/router';

const { $messageError, $messageSuccess, $messageWarning } = useGlobal();
const refForm = ref<IDialogFormIns>();
const refFormRender = ref<IDialogFormIns>();
const refSearch = ref<ICardSearchIns>();
const refTable = ref<ICardTableIns>();
const vfDesigner = ref<any>(null);
const vFormRef = ref<any>(null);
const state = reactive<{
  editRow: any;
  formJson: any;
  optionData: any;
  formData: any;
  formVisible: boolean;
  submitting: boolean;
}>({
  editRow: null,
  formJson: {},
  optionData: {},
  formData: {},
  formVisible: false,
  submitting: false
});
const designerConfig = reactive<any>({
  languageMenu: false, // 是否显示语言切换菜单
  externalLink: false, // 是否显示GitHub、文档等外部链接
  formTemplates: false, // 是否显示表单模板
  eventCollapse: false, // 是否显示组件事件属性折叠面板
  widgetNameReadonly: false, // 禁止修改组件名称

  clearDesignerButton: true, // 是否显示清空设计器按钮
  previewFormButton: true, // 是否显示预览表单按钮
  importJsonButton: true, // 是否显示导入JSON按钮
  exportJsonButton: true, // 是否显示导出JSON器按钮
  exportCodeButton: true, // 是否显示导出代码按钮
  generateSFCButton: false, // 是否显示生成SFC按钮
  toolbarMaxWidth: 420, // 设计器工具按钮栏最大宽度（单位像素）
  toolbarMinWidth: 300, // 设计器工具按钮栏最小宽度（单位像素）

  presetCssCode: '', // 设计器预设CSS样式代码

  resetFormJson: false // 是否在设计器初始化时将表单内容重置为空
});
const searchConfig = reactive<ISearch>({
  defaultParams: {},
  filters: [{ type: 'input', label: '表单名称', field: 'name' }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '新增',
          svgIcon: shallowRef(Plus),
          click: () => handleAddEdit()
        },
        {
          perm: true,
          text: '删除',
          type: 'danger',
          svgIcon: shallowRef(Delete),
          click: () => {
            console.log(tableConfig.selectList);
            if ((tableConfig.selectList || []).length > 0) {
              const ids = tableConfig.selectList?.map((select) => {
                return select.id;
              });
              handleDelForm(ids as string[]);
            } else {
              $messageWarning('请选择删除数据');
            }
          }
        },
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        }
      ]
    }
  ]
});

// 数据列表配置
const tableConfig = reactive<ICardTable>({
  loading: true,
  indexVisible: true,
  selectList: [],
  rowKey: 'id',
  columns: [
    { label: '表单名称', prop: 'name', minWidth: 120, align: 'center' },
    {
      prop: 'status',
      label: '是否启用',
      minWidth: 120,
      align: 'center',
      iconStyle: {
        color: '#19a39e'
      },
      cellStyle: (row) => ({
        color: row.status ? '#36a624' : '#f56c6c'
      }),
      formatter: (row) => {
        return row.status ? '是' : '否';
      }
    },
    { label: '备注', prop: 'remark', minWidth: 120, align: 'center' }
  ],
  dataList: [{}],
  operationFixed: 'right',
  operationWidth: 320,
  operations: [
    {
      perm: true,
      text: '预览',
      isTextBtn: false,
      type: 'success',
      svgIcon: shallowRef(View),
      click: (row) => handleFormRender(row)
    },
    {
      perm: true,
      text: '编辑',
      isTextBtn: false,
      svgIcon: shallowRef(Edit),
      click: (row) => handleAddEdit(row)
    },
    {
      perm: true,
      text: '删除',
      isTextBtn: false,
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: (row) => handleDelForm([row.id])
    },
    {
      perm: true,
      text: '设计',
      isTextBtn: false,
      type: 'warning',
      svgIcon: shallowRef(TrendCharts),
      click: (row) => handleFormDesign(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      tableConfig.pagination.page = page;
      tableConfig.pagination.limit = size;
      refreshData();
    }
  },
  handleSelectChange: (val) => {
    tableConfig.selectList = val;
  }
});
// 弹框表单配置
const formConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: 120,
  dialogWidth: 500,
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '表单名',
          field: 'name',
          rules: [{ required: true, message: '请填写表单名' }],
          placeholder: '请填写表单名'
        },
        {
          type: 'switch',
          label: '是否启用',
          field: 'status',
          rules: [{ required: true, message: '请选择是否启用' }]
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark',
          placeholder: '请填写备注'
        }
      ]
    }
  ]
});
// 表单预览
const formRenderConfig = reactive<IDialogFormConfig>({
  title: '表单预览',
  desTroyOnClose: true,
  group: []
});

// 删除数据
const handleDelForm = (ids: string[]) => {
  SLConfirm('确定删除？', '提示信息').then(() => {
    delForm(ids)
      .then(() => {
        $messageSuccess('删除成功');
        refreshData();
      })
      .catch((error) => {
        $messageWarning(error);
      });
  });
};

// 附件弹框配置
const handleAddEdit = async (row?: any) => {
  formConfig.defaultValue = {
    ...(row || {
      status: true
    })
  };
  formConfig.submit = (params: any) => {
    SLConfirm('确定提交？', '提示信息').then(() => {
      formConfig.submitting = true;
      params = {
        ...params,
        id: row ? row.id : null
      };
      ediForm(params)
        .then(() => {
          refForm.value?.closeDialog();
          formConfig.submitting = false;
          $messageSuccess('保存成功');
          refreshData();
        })
        .catch((error) => {
          $messageError(error);
          formConfig.submitting = false;
        });
    });
  };
  refForm.value?.openDialog();
};
// 打开表单设计框
const handleFormDesign = (row: any) => {
  state.editRow = row;
  state.formVisible = true;
  getFormById(row.id)
    .then((res) => {
      const form = res.data?.data;
      vfDesigner.value?.setFormJson();
      if (form.content) {
        vfDesigner.value?.setFormJson(JSON.parse(form.content));
      } else {
        vfDesigner.value?.clearDesigner();
      }
    })
    .catch((error) => {
      $messageError(error);
    });
  // nextTick(() => {
  //   vfDesigner.value?.setFormData(newFormData)
  // })
};
const handleFormRender = (row: any) => {
  state.editRow = row;
  refFormRender.value?.openDialog();
  getFormById(row.id)
    .then((res) => {
      const form = res.data?.data;
      if (form.content) {
        vFormRef.value?.setFormJson(JSON.parse(form.content));
      } else {
        vFormRef.value?.clearDesigner();
      }
    })
    .catch((error) => {
      $messageError(error);
    });
};
// 刷新数据
const refreshData = async () => {
  tableConfig.loading = true;
  const query = refSearch.value?.queryParams || {};
  const params = {
    ...query,
    page: tableConfig.pagination.page || 1,
    size: tableConfig.pagination.limit || 20
  };
  const result = await formList(params);
  const data = result.data?.data?.data;
  tableConfig.dataList = data;
  tableConfig.loading = false;
};
const refreshDataById = async (formId: string) => {
  getFormById(formId)
    .then((res) => {
      const form = res.data?.data;
      tableConfig.dataList = [form];
    })
    .catch((error) => {
      $messageError(error);
    });
  tableConfig.loading = false;
};
// 关闭表单设计弹框
const closeFormDialog = () => {
  // vfDesigner.value?.clearDesigner()
  vfDesigner.value?.refreshDesigner();
  state.formVisible = false;
};
// 提交表单设计数据
const saveFormDesigner = () => {
  SLConfirm('确定提交？', '提示信息').then(() => {
    state.submitting = true;
    state.editRow.content = JSON.stringify(vfDesigner.value?.getFormJson());
    ediForm(state.editRow)
      .then(() => {
        // vfDesigner.value?.refreshDesigner()
        vfDesigner.value?.clearDesigner();
        state.submitting = false;
        state.formVisible = false;
        state.editRow = null;
        $messageSuccess('保存成功');
        refreshData();
        state.formJson = {};
      })
      .catch((error) => {
        $messageError(error);
        state.submitting = false;
      });
  });
};
onMounted(async () => {
  // 获取部门树
  console.log();
  const formId = router.currentRoute.value.query.formId as string;
  if (formId) {
    await refreshDataById(formId);
  } else {
    await refreshData();
  }
});
</script>

<style scoped lang="scss">
:deep(.el-container.main-container) {
  background: transparent !important;
}
:deep(.el-container.panel-container) {
  padding-left: 10px;
  width: 34vh;
}
:deep(.el-tabs__content) {
  li {
    background: transparent !important;
  }
}
:deep(.form-widget-container) {
  background: transparent !important;
  .el-form.full-height-width {
    background: transparent !important;
  }
}
:deep(.el-header.main-header) {
  display: none;
}
:deep(.el-container.center-layout-container) {
  min-width: 655px;
  border-left: 1px solid #424554;
  border-right: 1px solid #424554;
}
:deep(.el-dialog__body) {
  //height: 90%;
  overflow: auto !important;
}
:deep(.setting-scrollbar) {
  width: 34vh;
}
</style>
