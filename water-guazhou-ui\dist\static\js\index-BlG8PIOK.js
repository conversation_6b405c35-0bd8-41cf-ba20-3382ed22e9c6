import{C as S,au as m,g as a,n,av as r,aw as u,ax as c,an as d,p as f}from"./index-r0dFAfgr.js";const g={name:"VueSeamlessScroll",props:{data:{type:Array,default:()=>[]},classOption:{type:Object,default:()=>({})}},emits:["ScrollEnd"],data(){return{xPos:0,yPos:0,delay:0,copyHtml:"",height:0,width:0,realBoxWidth:0}},computed:{leftSwitchState(){return this.xPos<0},rightSwitchState(){return Math.abs(this.xPos)<this.realBoxWidth-this.width},leftSwitchClass(){return this.leftSwitchState?"":this.options.switchDisabledClass},rightSwitchClass(){return this.rightSwitchState?"":this.options.switchDisabledClass},leftSwitch(){return{position:"absolute",margin:`${this.height/2}px 0 0 -${this.options.switchOffset}px`,transform:"translate(-100%,-50%)"}},rightSwitch(){return{position:"absolute",margin:`${this.height/2}px 0 0 ${this.width+this.options.switchOffset}px`,transform:"translateY(-50%)"}},float(){return this.isHorizontal?{float:"left",overflow:"hidden"}:{overflow:"hidden"}},pos(){return{transform:`translate(${this.xPos}px,${this.yPos}px)`,transition:`all ${this.ease} ${this.delay}ms`,overflow:"hidden"}},defaultOption(){return{step:1,limitMoveNum:5,hoverStop:!0,direction:1,openTouch:!0,singleHeight:0,singleWidth:0,waitTime:1e3,switchOffset:30,autoPlay:!0,navigation:!1,switchSingleStep:134,switchDelay:400,switchDisabledClass:"disabled",isSingleRemUnit:!1}},options(){return{...this.defaultOption,...this.classOption}},navigation(){return this.options.navigation},autoPlay(){return this.navigation?!1:this.options.autoPlay},scrollSwitch(){return this.data.length>=this.options.limitMoveNum},hoverStopSwitch(){return this.options.hoverStop&&this.autoPlay&&this.scrollSwitch},canTouchScroll(){return this.options.openTouch},isHorizontal(){return this.options.direction>1},baseFontSize(){return this.options.isSingleRemUnit?parseInt(window.getComputedStyle(document.documentElement,null).fontSize):1},realSingleStopWidth(){return this.options.singleWidth*this.baseFontSize},realSingleStopHeight(){return this.options.singleHeight*this.baseFontSize},step(){let i;const t=this.options.step;return this.isHorizontal?i=this.realSingleStopWidth:i=this.realSingleStopHeight,i>0&&i%t>0&&console.error("如果设置了单步滚动,step需是单步大小的约数,否则无法保证单步滚动结束的位置是否准确。~~~~~"),t}},watch:{data(i,t){this._dataWarm(i),m(i,t)||this.reset()},autoPlay(i){i?this.reset():this._stopMove()}},mounted(){this._initMove()},beforeCreate(){this.reqFrame=null,this.singleWaitTime=null,this.isHover=!1,this.ease="ease-in"},beforeUnmount(){this._cancle(),clearTimeout(this.singleWaitTime)},methods:{reset(){this._cancle(),this._initMove()},leftSwitchClick(){if(this.leftSwitchState){if(Math.abs(this.xPos)<this.options.switchSingleStep){this.xPos=0;return}this.xPos+=this.options.switchSingleStep}},rightSwitchClick(){if(this.rightSwitchState){if(this.realBoxWidth-this.width+this.xPos<this.options.switchSingleStep){this.xPos=this.width-this.realBoxWidth;return}this.xPos-=this.options.switchSingleStep}},_cancle(){cancelAnimationFrame(this.reqFrame||"")},touchStart(i){if(!this.canTouchScroll)return;let t;const e=i.targetTouches[0],{waitTime:h,singleHeight:l,singleWidth:s}=this.options;this.startPos={x:e.pageX,y:e.pageY},this.startPosY=this.yPos,this.startPosX=this.xPos,l&&s?(t&&clearTimeout(t),t=setTimeout(()=>{this._cancle()},h+20)):this._cancle()},touchMove(i){if(!this.canTouchScroll||i.targetTouches.length>1||i.scale&&i.scale!==1)return;const t=i.targetTouches[0],{direction:e}=this.options;this.endPos={x:t.pageX-this.startPos.x,y:t.pageY-this.startPos.y},event.preventDefault();const h=Math.abs(this.endPos.x)<Math.abs(this.endPos.y)?1:0;h===1&&e<2?this.yPos=this.startPosY+this.endPos.y:h===0&&e>1&&(this.xPos=this.startPosX+this.endPos.x)},touchEnd(){if(!this.canTouchScroll)return;let i;const t=this.options.direction;if(this.delay=50,t===1)this.yPos>0&&(this.yPos=0);else if(t===0){const e=this.realBoxHeight/2*-1;this.yPos<e&&(this.yPos=e)}else if(t===2)this.xPos>0&&(this.xPos=0);else if(t===3){const e=this.realBoxWidth*-1;this.xPos<e&&(this.xPos=e)}i&&clearTimeout(i),i=setTimeout(()=>{this.delay=0,this._move()},this.delay)},enter(){this.hoverStopSwitch&&this._stopMove()},leave(){this.hoverStopSwitch&&this._startMove()},_move(){this.isHover||(this._cancle(),this.reqFrame=requestAnimationFrame(()=>{const i=this.realBoxHeight/2,t=this.realBoxWidth/2,{direction:e,waitTime:h}=this.options,{step:l}=this;e===1?(Math.abs(this.yPos)>=i&&(this.$emit("ScrollEnd"),this.yPos=0),this.yPos-=l):e===0?(this.yPos>=0&&(this.$emit("ScrollEnd"),this.yPos=i*-1),this.yPos+=l):e===2?(Math.abs(this.xPos)>=t&&(this.$emit("ScrollEnd"),this.xPos=0),this.xPos-=l):e===3&&(this.xPos>=0&&(this.$emit("ScrollEnd"),this.xPos=t*-1),this.xPos+=l),this.singleWaitTime&&clearTimeout(this.singleWaitTime),this.realSingleStopHeight?Math.abs(this.yPos)%this.realSingleStopHeight<l?this.singleWaitTime=setTimeout(()=>{this._move()},h):this._move():this.realSingleStopWidth?Math.abs(this.xPos)%this.realSingleStopWidth<l?this.singleWaitTime=setTimeout(()=>{this._move()},h):this._move():this._move()}))},_initMove(){this.$nextTick(()=>{const{switchDelay:i}=this.options,{autoPlay:t,isHorizontal:e}=this;if(this._dataWarm(this.data),this.copyHtml="",e){this.height=this.$refs.wrap.offsetHeight,this.width=this.$refs.wrap.offsetWidth;let h=this.$refs.slotList.offsetWidth;t&&(h=h*2+1),this.$refs.realBox.style.width=h+"px",this.realBoxWidth=h}if(t)this.ease="ease-in",this.delay=0;else{this.ease="linear",this.delay=i;return}this.scrollSwitch?(this.copyHtml=this.$refs.slotList.innerHTML,setTimeout(()=>{this.realBoxHeight=this.$refs.realBox.offsetHeight,this._move()},0)):(this._cancle(),this.yPos=this.xPos=0)})},_dataWarm(i){i.length>100&&console.warn(`数据达到了${i.length}条有点多哦~,可能会造成部分老旧浏览器卡顿。`)},_startMove(){this.isHover=!1,this._move()},_stopMove(){this.isHover=!0,this.singleWaitTime&&clearTimeout(this.singleWaitTime),this._cancle()}}},p={ref:"wrap"},w=["innerHTML"];function P(i,t,e,h,l,s){return a(),n("div",p,[s.navigation?(a(),n("div",{key:0,style:r(s.leftSwitch),class:u(s.leftSwitchClass),onClick:t[0]||(t[0]=(...o)=>s.leftSwitchClick&&s.leftSwitchClick(...o))},[c(i.$slots,"left-switch")],6)):d("",!0),s.navigation?(a(),n("div",{key:1,style:r(s.rightSwitch),class:u(s.rightSwitchClass),onClick:t[1]||(t[1]=(...o)=>s.rightSwitchClick&&s.rightSwitchClick(...o))},[c(i.$slots,"right-switch")],6)):d("",!0),f("div",{ref:"realBox",style:r(s.pos),onMouseenter:t[2]||(t[2]=(...o)=>s.enter&&s.enter(...o)),onMouseleave:t[3]||(t[3]=(...o)=>s.leave&&s.leave(...o)),onTouchstart:t[4]||(t[4]=(...o)=>s.touchStart&&s.touchStart(...o)),onTouchmove:t[5]||(t[5]=(...o)=>s.touchMove&&s.touchMove(...o)),onTouchend:t[6]||(t[6]=(...o)=>s.touchEnd&&s.touchEnd(...o))},[f("div",{ref:"slotList",style:r(s.float)},[c(i.$slots,"default")],4),f("div",{style:r(s.float),innerHTML:l.copyHtml},null,12,w)],36)],512)}const y=S(g,[["render",P]]);export{y as _};
