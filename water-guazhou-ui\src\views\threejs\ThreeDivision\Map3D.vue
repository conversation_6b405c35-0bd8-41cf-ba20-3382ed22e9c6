<template>
  <div ref="refDiv" class="viewDiv"></div>
</template>
<script lang="ts" setup>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import {
  CSS2DRenderer,
  CSS2DObject
} from 'three/examples/jsm/renderers/CSS2DRenderer';
import * as d3 from 'd3';
import MeshLine from '@/utils/three/mesh/MeshLine';
import { toCommaNumber } from '@/utils/GlobalHelper';
import {
  useStations,
  useStationRealTimeData
} from '@/hooks/station/useStation';
const props = defineProps<{
  projectId?: string;
}>();
/**
 * 加载图片的方法
 * @param name 图片名称
 */
const getSmartDesisionLegendImage = (name: string | URL) => {
  const href = new URL(
    `../../smartDecision/imgs/legends/${name}`,
    import.meta.url
  )?.href;
  return href;
};
const config: ISMART_DECISION_CONFIG_D3MAPCONFIG = {
  boundaryGeoJsonUrl: '/THREEJS/geojson/gansu_boundary.geojson',
  linesGeoJsonUrl: '/THREEJS/geojson/gansu_lines.geojson',
  pointsGeoJsonUrl: '/THREEJS/geojson/gansu_points.geojson',
  textureUrl: '/THREEJS/textures/marrine.png',
  textureOffset: [0.3, 0.2],
  textureRepeat: [0.01, 0.01],
  // 镜头位置
  cameraPosition: [0, -15, 30],
  // 地图中心坐标
  mapCenter: [95.787329, 40.516879],
  height: 1.0,
  ...(window.SITE_CONFIG?.SMART_DECISION_CONFIG.d3MapConfig || {})
};
const refDiv = ref<HTMLDivElement>();
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, 1920 / 1080, 0.1, 1000);
const renderer = new THREE.WebGLRenderer({
  antialias: true,
  alpha: true
});
// renderer.setClearAlpha(0)
const controls = new OrbitControls(camera, renderer.domElement);
controls.mouseButtons = {
  LEFT: THREE.MOUSE.RIGHT,
  MIDDLE: THREE.MOUSE.MIDDLE,
  RIGHT: THREE.MOUSE.LEFT
  // ORBIT: THREE.MOUSE.RIGHT,
  // ZOOM: THREE.MOUSE.MIDDLE,
  // PAN: THREE.MOUSE.LEFT
};
controls.enableDamping = true;
// 是否开启右键拖拽,好像与浏览器鼠标手势冲突，需要关闭浏览器的鼠标手势
controls.enablePan = true;
controls.minPolarAngle = 0;
controls.maxPolarAngle = 90;
controls.rotateSpeed = (4 / 180) * Math.PI;
// const axisHelper = new THREE.AxesHelper(5)
// scene.add(axisHelper)
// 通过设置position来进行移动
camera.position.set(...config.cameraPosition);

/** 纹理加载器 */
const textureLoader = new THREE.TextureLoader();

/** **************************统一处理json数据 */
const loader = new THREE.FileLoader();

// 边界数据
if (config.boundaryGeoJsonUrl) {
  loader.load(config.boundaryGeoJsonUrl, (data: any) => {
    const jsonData = JSON.parse(data);
    dealBoundaryData(jsonData);
  });
}
// 场景坐标转换
const projection = d3.geoMercator().center(config.mapCenter).translate([0, 0]);

// 处理范围边界
const dealBoundaryData = (jsonData: any) => {
  // 总的数据对象
  const boundaryObj = new THREE.Object3D();
  const features = jsonData.features;
  features.map((feature) => {
    // 创建市区的物体
    const city = new THREE.Object3D();
    city.name = feature.properties.name;
    // 获取特征的坐标
    const coordinates = feature.geometry.coordinates;

    if (feature.geometry.type === 'Polygon') {
      coordinates.forEach((coords) => {
        const mesh = createMesh(coords);
        mesh.name = feature.properties.name;
        city.add(mesh);
        // const line = createLine(coords)
        // city.add(line)
        const line = new MeshLine(mesh.geometry);
        city.add(line.mesh);
      });
    } else if (feature.geometry.type === 'MultiPolygon') {
      coordinates.forEach((coords) => {
        coords.forEach((cCoords) => {
          const mesh = createMesh(cCoords);
          mesh.name = feature.properties.name;
          city.add(mesh);
          // const line = createLine(cCoords)
          // city.add(line)

          const line = new MeshLine(mesh.geometry);
          city.add(line.mesh);
        });
      });
    } else {
      return;
    }
    boundaryObj.add(city);
  });
  scene.add(boundaryObj);
};

// 加载范围纹理

const marrineTexture = textureLoader.load(config.textureUrl);
marrineTexture.wrapS = marrineTexture.wrapT = THREE.RepeatWrapping;
marrineTexture.offset.set(...config.textureOffset);
marrineTexture.repeat.set(...config.textureRepeat);
// 范围体的两面材质
const material1 = new THREE.MeshBasicMaterial({
  color: 0x00619b,
  transparent: false,
  opacity: 1,
  map: marrineTexture
  // depthTest: false,
  // depthWrite: true
  // wireframe: true
});
// 范围体侧边材质
const material2 = new THREE.MeshBasicMaterial({
  color: 0x00ffff,
  transparent: true,
  opacity: 0.9
});
/**
 * 创建各范围实体
 * @param polygons
 */
const createMesh = (polygons: any) => {
  const shape = new THREE.Shape();
  polygons.forEach((row, i) => {
    const [longitute, latitude] = projection(row) || [0, 0];
    if (i === 0) {
      shape.moveTo(longitute, -latitude);
    }
    shape.lineTo(longitute, -latitude);
  });
  const geometry = new THREE.ExtrudeGeometry(shape, {
    depth: config.height,
    bevelEnabled: false
  });

  return new THREE.Mesh(geometry, [material1, material2]);
};

/** *************处理管线数据 */
// 加载管线数据
if (config.linesGeoJsonUrl) {
  loader.load(config.linesGeoJsonUrl, (data: any) => {
    const jsonData = JSON.parse(data);
    dealPipeData(jsonData);
  });
}
// 处理管线数据
const dealPipeData = (jsonData: any) => {
  const features = jsonData.features;
  // 总的数据对象
  const map = new THREE.Object3D();
  features.map(
    (feature: {
      properties: { name: string };
      geometry: { coordinates: any; type: string };
    }) => {
      // 创建市区的物体
      const city = new THREE.Object3D();
      city.name = feature.properties.name;
      // 获取特征的坐标
      const coordinates = feature.geometry.coordinates;

      if (feature.geometry.type === 'LineString') {
        const line = createLine(coordinates);
        city.add(line);
      }
      map.add(city);
    }
  );
  scene.add(map);
};
// 加载管线流光纹理
const lineTexture = textureLoader.load('/webgl/textures/spriteline1.png');
lineTexture.wrapS = lineTexture.wrapT = THREE.RepeatWrapping;
lineTexture.repeat.set(1, 1);
lineTexture.needsUpdate = true;
const lineMaterial = new THREE.MeshBasicMaterial({
  map: lineTexture,
  side: THREE.BackSide,
  transparent: true
});
// 创建管线
const createLine = (path: any) => {
  const pointsArray: any[] = [];
  path.forEach((row) => {
    const [longitude, latitude] = projection(row) || [0, 0];
    pointsArray.push(
      new THREE.Vector3(longitude, -latitude, config.height * 1.1)
    );
  });
  const curve = new THREE.CatmullRomCurve3(pointsArray, false, 'catmullrom', 0);
  const lineGeometry = new THREE.TubeGeometry(curve, 80, config.height * 0.1);
  return new THREE.Mesh(lineGeometry, lineMaterial);
};

/** **************处理标点数据 */
// 加载数据
// if (config.pointsGeoJsonUrl) {
//   loader.load(config.pointsGeoJsonUrl, (data: any) => {
//     const jsonData = JSON.parse(data)
//     console.log(jsonData)
//     dealPointData(jsonData)
//   })
// }
// 处理数据
// 总的数据对象
const map = new THREE.Object3D();
scene.add(map);
// 用来装弹窗的对象
const popMap = new THREE.Object3D();
scene.add(popMap);
// 用来装文字标签的对象
const textMap = new THREE.Object3D();
scene.add(textMap);
// const typeObj = {
//   clyz: '测流压站',
//   lljcd: '流量监测点',
//   bf: '泵房',
//   sc: '水厂',
//   syd: '水源地',
//   szjcd: '水质监测点',
//   yljcd: '压力监测点'
// }
const typeObjR = {
  测流压站: 'clyz',
  测流压点: 'clyz',
  流量监测站: 'lljcd',
  流量监测点: 'lljcd',
  泵房: 'bf',
  泵站: 'bf',
  水厂: 'sc',
  水源地: 'syd',
  水质监测点: 'szjcd',
  水质监测站: 'szjcd',
  压力监测点: 'yljcd',
  压力监测站: 'yljcd',
  水池监测点: 'scjcd',
  水池监测站: 'scjcd'
};
const typeArr = [
  { type: 'clyz', name: '测流压站' },
  { type: 'clyz', name: '测流压点' },
  { type: 'lljcd', name: '流量监测点' },
  { type: 'lljcd', name: '流量监测站' },
  { type: 'bf', name: '泵房' },
  { type: 'bf', name: '泵站' },
  { type: 'sc', name: '水厂' },
  { type: 'syd', name: '水源地' },
  { type: 'szjcd', name: '水质监测点' },
  { type: 'szjcd', name: '水质监测站' },
  { type: 'yljcd', name: '压力监测点' },
  { type: 'yljcd', name: '压力监测站' },
  { type: 'scjcd', name: '水池监测点' },
  { type: 'scjcd', name: '水池监测站' }
];
type IFeature = {
  id: any;
  properties: { name: string; type: string; stationType: string };
  geometry: { coordinates: any; type: string };
};
const pointFeatures: IFeature[] = [];
const stations = useStations();
const dealPointData = async () => {
  // pointFeatures = jsonData.features
  pointFeatures.length = 0;
  map.clear();
  textMap.clear();
  popMap.clear();

  const res = await stations.getStations(undefined, props.projectId);
  res.map((item) => {
    let location = item.location?.split(',');
    if (location?.length === 2) {
      location = [Number(location[0]), Number(location[1])];
      const type = typeObjR[item.type];
      const obj: IFeature = {
        id: item.id,
        properties: {
          type,
          name: item.name,
          stationType: item.type
        },
        geometry: {
          type: 'Point',
          coordinates: location
        }
      };
      pointFeatures.push(obj);
    }
  });
  pointFeatures.map((feature: IFeature, i: number) => {
    const id = feature.id;
    // 创建点
    const point = new THREE.Object3D();
    point.name = feature.properties.name;

    if (feature.geometry.type === 'Point') {
      const sprite = createMarkSprite(
        id,
        { name: feature.properties.stationType, type: feature.properties.type },
        feature.geometry.coordinates
      );
      // 添加图标
      map.add(sprite);
      // 添加图标文本

      const [longitude, latitude] = projection(
        feature.geometry.coordinates
      ) || [0, 0];
      const label = createLableObj(feature.properties.name, {
        longitude,
        latitude,
        className: 'laber_name ' + feature.properties.type
      });
      textMap.add(label);
    }
  });
};
// 加载标点纹理
const loadTexture = (name: string) => {
  return textureLoader.load(getSmartDesisionLegendImage(name + '.png'));
};
const createMarkSprite = (
  id: string,
  station: { name: string; type: string },
  coordinates: [number, number]
) => {
  const [longitude, latitude] = projection(coordinates) || [0, 0];
  const texture = loadTexture(station.type);
  const material = new THREE.SpriteMaterial({
    map: texture,
    sizeAttenuation: false
  });
  const sprite = new THREE.Sprite(material);
  sprite.scale.set(0.1, 0.1, 0.1);
  sprite.position.set(longitude, -latitude, config.height * 1.5);
  const material1 = new THREE.SpriteMaterial({
    map: texture,
    sizeAttenuation: false,
    depthTest: false,
    opacity: 1
  });
  const sprite1 = new THREE.Sprite(material1);
  sprite.add(sprite1);
  sprite.name = id;
  return sprite;
};
const labelRenderer = new CSS2DRenderer();
labelRenderer.domElement.style.position = 'absolute';
// labelRenderer.domElement.style.overflow = 'visible'
// labelRenderer.domElement.style.opacity = '0.2'
labelRenderer.domElement.style.top = '0px';
labelRenderer.domElement.style.pointerEvents = 'none';

const createLableObj = (
  text,
  options: {
    longitude: number;
    latitude: number;
    className?: string;
  }
) => {
  const laberDiv = document.createElement('div'); // 创建div容器
  laberDiv.className = options.className || '';
  laberDiv.innerHTML = text;
  const pointLabel = new CSS2DObject(laberDiv);
  pointLabel.position.set(
    options.longitude,
    -options.latitude,
    config.height * 1.5
  );
  return pointLabel;
};
const realtime = useStationRealTimeData();
const openPop = async (
  intersectResult: THREE.Intersection<THREE.Object3D<THREE.Event>>
) => {
  const id = intersectResult.object.name;
  console.log(id);
  if (!id) return;
  const res = await realtime.getRealTimeData(id);

  const data = res.map((item) => {
    return {
      label: item.propertyName,
      value: item.value,
      unit: item.unit,
      color: undefined
    };
  });
  // [
  //   { label: '液位1', value: 5.23, unit: 'm' },
  //   { label: '液位2', value: 0.42, unit: 'm' },
  //   { label: '流量', value: 45.25, unit: 'm³/h' },
  //   { label: '压力', value: 0.48, unit: 'MPa' },
  //   { label: '水质', value: '合格', unit: '', color: '#1CDB96' }
  // ]
  let listStr = '';
  data.map((item) => {
    listStr += `
    <div class="list-item">
      <span class="label">${item.label}:</span>
      <span class="value" style="color:${item.color};">${toCommaNumber(item.value) || item.value}</span>
      <span class="unit">${item.unit}</span>
    </div>
    `;
  });
  const stationObj = pointFeatures.find(
    (item) => item.id === intersectResult.object.name
  );
  const typeName = typeArr.find(
    (item) => item.type === stationObj?.properties.type
  )?.name;
  const label = createLableObj(
    `
    <div class="three-popup">
      <p class="three-popup__header">${typeName}</p>
      <div class="three-popup__main">
        ${listStr}
        </div>
    </div>
    `,
    {
      longitude: intersectResult.point.x,
      latitude: -intersectResult.point.y
    }
  );
  popMap.add(label);
};
const run = () => {
  requestAnimationFrame(run);
  // 设置enableDamping需要调用update方法

  // 管线贴图动起来
  if (lineTexture) lineTexture.offset.x -= 0.005;
  controls.update();
  renderer.render(scene, camera);
  labelRenderer.render(scene, camera);
};
const resizeDiv = () => {
  if (!refDiv.value) return;
  // 更新摄像头
  // camera.aspect = window.innerWidth / window.innerHeight
  camera.aspect = refDiv.value.clientWidth / refDiv.value.clientHeight;
  // 更新摄像头的投影矩阵
  camera.updateProjectionMatrix();
  renderer.setSize(refDiv.value.clientWidth, refDiv.value.clientHeight);
  renderer.setPixelRatio(window.devicePixelRatio);
};
const init = () => {
  resizeDiv();
  refDiv.value?.appendChild(renderer.domElement);
  if (refDiv.value) {
    labelRenderer.setSize(refDiv.value.clientWidth, refDiv.value.clientHeight);
    refDiv.value.appendChild(labelRenderer.domElement);
  }
  nextTick().then(() => {
    dealPointData();
  });
  run();
};
watch(
  () => props.projectId,
  () => {
    dealPointData();
  }
);
onMounted(() => {
  init();
  window.addEventListener('resize', () => {
    // 更新渲染器
    resizeDiv();
  });
  // controls.addEventListener('change', e => console.log(e))

  // window.addEventListener('dblclick', () => {
  //   if (document.fullscreenElement) {
  //     document.exitFullscreen()
  //   } else {
  //     renderer.domElement.requestFullscreen()
  //   }
  // })
  window.addEventListener('click', (e) => {
    if (!refDiv.value) return;
    // 获取鼠标的位置
    const mouse = new THREE.Vector2();
    mouse.x = (e.offsetX / refDiv.value.clientWidth) * 2 - 1;
    mouse.y = -(e.offsetY / refDiv.value.clientHeight) * 2 + 1;

    // 获取鼠标点击的位置
    const rayCaster = new THREE.Raycaster();
    rayCaster.setFromCamera(mouse, camera);
    // 获取点击的点
    const intersects = rayCaster.intersectObjects(map.children);
    if (intersects.length) {
      popMap.clear();
      openPop(intersects[0]);
    } else {
      popMap.clear();
    }
    return false;
  });
});
</script>
<style lang="scss" scoped>
.viewDiv {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: transparent;
}
</style>
<style lang="scss">
.laber_name {
  position: relative;
  top: -40px;
  padding: 0 8px;
  display: flex;
  font-size: 12px;
  line-height: 20px;
  justify-content: flex-start;
  align-items: center;
  border: 2px solid transparent;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  color: rgb(96, 223, 71);
  background-image: linear-gradient(
      to right,
      rgba(7, 81, 84, 0.8),
      rgba(7, 81, 84, 0.8)
    ),
    linear-gradient(90deg, rgba(0, 216, 225, 1), rgba(0, 216, 225, 0));
  &.lljcd {
    background-image: linear-gradient(
        to right,
        rgba(7, 81, 84, 0.8),
        rgba(7, 81, 84, 0.8)
      ),
      linear-gradient(90deg, rgba(0, 216, 225, 1), rgba(0, 216, 225, 0));
  }
  &.yljcd {
    background-image: linear-gradient(to right, #664109, #664109),
      linear-gradient(90deg, rgba(255, 171, 45, 1), rgba(255, 171, 45, 0));
  }
  &.szjcd {
    background-image: linear-gradient(to right, #575808, #575808),
      linear-gradient(90deg, rgba(224, 228, 21, 1), rgba(224, 228, 21, 0));
  }
  &.bf {
    background-image: linear-gradient(to right, #400854, #400854),
      linear-gradient(90deg, rgba(196, 30, 255, 1), rgba(196, 30, 255, 0));
  }
  &.syd {
    background-image: linear-gradient(to right, #064830, #064830),
      linear-gradient(90deg, rgba(28, 219, 150, 1), rgba(28, 219, 150, 0));
  }
  &.sc {
    background-image: linear-gradient(to right, #0a3060, #0a3060),
      linear-gradient(90deg, rgba(77, 148, 255, 1), rgba(77, 148, 255, 0));
  }
}
.three-popup {
  width: 200px;
  height: 200px;
  position: relative;
  left: -75px;
  bottom: 130px;
  z-index: 100;
  animation-name: scaleable;
  animation-duration: 0.2s;
  animation-delay: 0.2;
  background: url('./imgs/pop_bg.png') 0 0 /100% 100% no-repeat;
  &::after {
    content: '';
    width: 2px;
    height: 20px;
    background-color: #20bcff;
    position: absolute;
    bottom: -37px;
    left: 155px;
    transform: rotate(-90deg);
    animation-name: scaleable;
    animation-duration: 0.1s;
  }
  &::before {
    content: '';
    width: 2px;
    height: 40px;
    background-color: #20bcff;
    position: absolute;
    bottom: -33px;
    left: 132px;
    transform: rotate(-45deg);
    animation-name: scaleable;
    animation-duration: 0.1s;
  }
  .three-popup__header {
    height: 40px;
    font-size: 14px;
    color: #fff;
    line-height: 40px;
    padding: 0 8px;
  }
  .three-popup__main {
    padding: 6px 8px;
    height: 160px;
    overflow-y: hidden;
    .list-item {
      display: flex;
      align-items: center;
      height: 30px;
      font-size: 12px;
      .label {
        color: rgba(253, 59, 0, 0.8);
        font-size: 12px;
        width: 100px;
        margin-right: auto;
      }
      .value {
        color: rgb(29, 248, 0);
        font-size: 20px;
        font-family: font-lcd;
        text-align: right;
      }
      .unit {
        width: 50px;
        text-align: right;
        margin-left: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
      }
      .label,
      .value,
      .unit {
        word-break: keep-all;
      }
    }
  }
}
@keyframes scaleable {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
</style>
