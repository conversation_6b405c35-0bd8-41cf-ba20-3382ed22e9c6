import{j as o,E as f,i as w,e as n,y as m,a as g}from"./Point-WxyopZva.js";import{l as I}from"./widget-BcWKanF2.js";import"./index-r0dFAfgr.js";import{t as S,n as d}from"./imageUtils-BPtoKHto.js";import{f as T,u as V}from"./LayerView-BSt9B8Gh.js";import{bB as b,bC as v,bD as q,bs as y}from"./MapView-DaoQedLH.js";import{i as x}from"./RefreshableLayerView-DUeNHzrW.js";import"./pe-B8dP0-Ut.js";import"./BitmapTileContainer-CnUUv4uK.js";import"./Bitmap-CraE42_6.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./TiledDisplayObject-C5kAiJtw.js";import"./WGLContainer-Dyx9110G.js";import"./FramebufferObject-8j9PRuxE.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./vec4f32-CjrfB-0a.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./enums-L38xj_2E.js";import"./number-CoJp78Rz.js";import"./ProgramTemplate-tdUBoAol.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./utils-DPUVnAXL.js";import"./StyleDefinition-Bnnz5uyC.js";import"./config-MDUrh2eL.js";import"./GeometryUtils-BRRfazic.js";import"./earcut-BJup91r2.js";import"./TileContainer-CC8_A7ZF.js";const M=[102113,102100,3857,3785,900913],R=[0,0];let a=class extends x(S(T(V))){constructor(){super(...arguments),this._tileStrategy=null,this._fetchQueue=null,this._tileRequests=new Map,this.layer=null}get tileMatrixSet(){const e=this._getTileMatrixSetBySpatialReference(this.layer.activeLayer);return e?(e.id!==this.layer.activeLayer.tileMatrixSetId&&(this.layer.activeLayer.tileMatrixSetId=e.id),e):null}update(e){this._fetchQueue.pause(),this._fetchQueue.state=e.state,this._tileStrategy.update(e),this._fetchQueue.resume()}attach(){var t;const e=(t=this.tileMatrixSet)==null?void 0:t.tileInfo;e&&(this._tileInfoView=new b(e),this._fetchQueue=new v({tileInfoView:this._tileInfoView,concurrency:16,process:(i,s)=>this.fetchTile(i,s)}),this._tileStrategy=new q({cachePolicy:"keep",resampling:!0,acquireTile:i=>this.acquireTile(i),releaseTile:i=>this.releaseTile(i),tileInfoView:this._tileInfoView}),this.addAttachHandles(I(()=>{var i,s;return[(s=(i=this.layer)==null?void 0:i.activeLayer)==null?void 0:s.styleId,this.tileMatrixSet]},()=>this._refresh())),super.attach())}detach(){var e,t;super.detach(),(e=this._tileStrategy)==null||e.destroy(),(t=this._fetchQueue)==null||t.destroy(),this._fetchQueue=this._tileStrategy=this._tileInfoView=null}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this.requestUpdate()}releaseTile(e){this._fetchQueue.abort(e.key.id),this._bitmapView.removeChild(e),e.once("detach",()=>e.destroy()),this.requestUpdate()}acquireTile(e){const t=this._bitmapView.createTile(e),i=t.bitmap;return[i.x,i.y]=this._tileInfoView.getTileCoords(R,t.key),i.resolution=this._tileInfoView.getTileResolution(t.key),[i.width,i.height]=this._tileInfoView.tileInfo.size,this._enqueueTileFetch(t),this._bitmapView.addChild(t),this.requestUpdate(),t}async doRefresh(){!this.attached||this.updateRequested||this.suspended||this._refresh()}isUpdating(){var e;return((e=this._fetchQueue)==null?void 0:e.updating)??!1}async fetchTile(e,t={}){const i="tilemapCache"in this.layer?this.layer.tilemapCache:null,{signal:s,resamplingLevel:r=0}=t;if(!i)return this._fetchImage(e,s);const l=new y(0,0,0,0);let u;try{await i.fetchAvailabilityUpsample(e.level,e.row,e.col,l,{signal:s}),u=await this._fetchImage(l,s)}catch(h){if(o(h))throw h;if(r<3){const c=this._tileInfoView.getTileParentId(e.id);if(c){const p=new y(c),_=await this.fetchTile(p,{...t,resamplingLevel:r+1});return d(this._tileInfoView,_,p,e)}}throw h}return d(this._tileInfoView,u,l,e)}canResume(){const e=super.canResume();return e&&this.tileMatrixSet!==null}supportsSpatialReference(e){var t;return((t=this.layer.activeLayer.tileMatrixSets)==null?void 0:t.some(i=>{var s;return f((s=i.tileInfo)==null?void 0:s.spatialReference,e)}))??!1}async _enqueueTileFetch(e){if(!this._fetchQueue.has(e.key.id)){try{const t=await this._fetchQueue.push(e.key);e.bitmap.source=t,e.bitmap.width=this._tileInfoView.tileInfo.size[0],e.bitmap.height=this._tileInfoView.tileInfo.size[1],e.once("attach",()=>this.requestUpdate())}catch(t){o(t)||w.getLogger(this.declaredClass).error(t)}this.requestUpdate()}}async _fetchImage(e,t){return this.layer.fetchImageBitmapTile(e.level,e.row,e.col,{signal:t})}_refresh(){this._fetchQueue.reset(),this._tileStrategy.tiles.forEach(e=>{if(!e.bitmap.source)return;const t={id:e.key.id,fulfilled:!1,promise:this._fetchQueue.push(e.key).then(i=>{e.bitmap.source=i}).catch(i=>{o(i)||(e.bitmap.source=null)}).finally(()=>{e.requestRender(),t.fulfilled=!0})};this._tileRequests.set(e,t)})}_getTileMatrixSetBySpatialReference(e){const t=this.view.spatialReference;if(!e.tileMatrixSets)return null;let i=e.tileMatrixSets.find(s=>{var r;return f((r=s.tileInfo)==null?void 0:r.spatialReference,t)});return!i&&t.isWebMercator&&(i=e.tileMatrixSets.find(s=>{var r;return M.includes(((r=s.tileInfo)==null?void 0:r.spatialReference.wkid)??-1)})),i}};n([m()],a.prototype,"_fetchQueue",void 0),n([m({readOnly:!0})],a.prototype,"tileMatrixSet",null),a=n([g("esri.views.2d.layers.WMTSLayerView2D")],a);const oe=a;export{oe as default};
