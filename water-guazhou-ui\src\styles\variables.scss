$color-white:#fff;

// 和bootstrap相反，数字越小，宽度越大
$span: (1: 100%,
  2: 50%,
  3: 33.33%,
  4:25%,
  5:20%,
  6:16.66%,
  7:14.285%,
  8:12.5%,
  9:11.111%,
  10:10%,
  11:9.09%,
  12:8.333%);

.ls-flex-box,
.ls-disflex-box {


  .el-form-item {
    padding: 0 8px;
    margin: 0 0 16px 0;
  }
}

.ls-flex-box {
  .el-form-item {

    flex-basis: 25%;
  }
}

/* 遍历map */
@each $key,
$value in $span {
  .ls-flex-span--#{$key} {
    flex-basis: $value;
  }
}

/* 遍历map */
@each $key,
$value in $span {
  .ls-flex-box .ls-flex-item--#{$key} {
    flex-basis: $value;
    max-width: $value;
  }

  .ls-disflex-box .ls-flex-item--#{$key} {
    padding: 0 8px;
    margin: 0 0 16px 0;
  }
}