package org.thingsboard.server.dao.smartProduction.guard.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardEventRecord;
import org.thingsboard.server.dao.smartProduction.guard.GuardEventRecordService;
import org.thingsboard.server.dao.sql.smartProduction.guard.GuardEventRecordMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardEventRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardEventRecordSaveRequest;

@Service
public class GuardEventRecordServiceImpl implements GuardEventRecordService {
    @Autowired
    private GuardEventRecordMapper mapper;

    @Override
    public IPage<GuardEventRecord> findAllConditional(GuardEventRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public GuardEventRecord save(GuardEventRecordSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, null);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteByRecord(String recordId) {
        return mapper.deleteByRecordId(recordId) > 0;
    }

}
