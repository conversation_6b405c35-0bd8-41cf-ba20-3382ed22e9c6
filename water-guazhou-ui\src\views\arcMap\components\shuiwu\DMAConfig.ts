export const initLossRateLineChartOption = (data?:any[]) => {
  const DataGS: number[] = []
  const DataLShui: number[] = []
  const DataLSL: number[] = []
  const xData = data?.map(item => {
    DataGS.push(item.supply)
    DataLShui.push(item.loss)
    DataLSL.push(item.lossRate)
    return item.date
  }) || []
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      show: true,
      textStyle: {
        color: '#fff'
      },
      icon: 'rect',
      top: 0,
      left: 'center',
      itemWidth: 15,
      itemHeight: 3
    },
    xAxis: {
      type: 'category', // 坐标类型
      data: xData || [],
      show: true, // 是否显示
      color: '#fff',
      axisLabel: {
        textStyle: {
          color: '#ffffff'
        }
      },
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      }
    },
    yAxis: [
      {
        name: 'm³',
        type: 'value',
        color: '#ffffff',
        axisLabel: {
          textStyle: {
            color: '#ffffff'
          }
        },
        splitArea: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#ffffff',
            opacity: 0.2,
            type: 'dashed'
          }
        }
      },
      {
        name: '%',
        type: 'value',
        color: '#ffffff',
        axisLabel: {
          textStyle: {
            color: '#ffffff'
          }
        },
        splitArea: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#ffffff',
            opacity: 0.2,
            type: 'dashed'
          }
        }
      }
    ],
    grid: {
      left: 70,
      right: 30,
      top: 50,
      bottom: 30
    },
    series: [
      {
        type: 'bar',
        barMaxWidth: 5,
        stack: 'bar',
        name: '供水量',
        itemStyle: {
          color: '#54a8ff'
        },
        yAxisIndex: 0,
        data: DataGS || []
      },
      {
        type: 'bar',
        barMaxWidth: 5,
        stack: 'bar',
        name: '漏水量',
        itemStyle: {
          color: '#80e539'
        },
        yAxisIndex: 0,
        data: DataLShui || []
      },
      {
        type: 'line',
        symbol: 'none',
        name: '漏失率',
        yAxisIndex: 1,
        itemStyle: {
          color: '#be9b41'
        },
        data: DataLSL || []
      }
    ]
  }
  return option
}

export const initLossRateAndTrendLineChartOption = (res?:any[]) => {
  const Data: number[] = []
  const Data1: number[] = []
  const xData = res?.map(item => {
    Data.push(item.lossRate)
    Data1.push(item.differenceRate)
    return item.date
  }) || []
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      show: true,
      textStyle: {
        color: '#fff'
      },
      icon: 'rect',
      top: 0,
      left: 'center',
      itemWidth: 15,
      itemHeight: 3
    },
    yAxis: {
      name: '%',
      type: 'value', // 坐标类型
      show: true, // 是否显示
      color: '#fff',
      axisLabel: {
        textStyle: {
          color: '#ffffff'
        }
      },

      splitLine: {
        lineStyle: {
          color: '#ffffff',
          opacity: 0.2,
          type: 'dashed'
        }
      },
      splitArea: {
        show: false
      }
    },
    xAxis: {
      data: xData || [],
      type: 'category',
      boundaryGap: false,
      scale: true,
      color: '#ffffff',
      axisLabel: {
        textStyle: {
          color: '#ffffff'
        }
      },

      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      }
    },
    grid: {
      left: 70,
      right: 30,
      top: 50,
      bottom: 30
    },
    series: [
      {
        type: 'line',
        barWidth: 10,
        symbol: 'none',
        smooth: false,
        name: '漏损率',
        data: Data || [],
        itemStyle: {
          color: '#be9b41'
        }
      },
      {
        type: 'line',
        barWidth: 10,
        symbol: 'none',
        smooth: false,
        name: '差销差',
        data: Data1 || [],
        itemStyle: {
          color: '#cc3f40'
        }
      }
    ]
  }
  return option
}
export const initWholeDayLiuliangPercentLineChartOption = (res?:any[]) => {
  const Data:any[] = []
  const xData = res?.map(item => {
    Data.push([
      item.start,
      item.end,
      item.min,
      item.max
    ])
    return item.date
  }) || []
  const option = {
    backgroundColor: 'transparent',
    color: ['#36E5E4', '#E33737', '#80E539', '#E2E033', '#EA54FF'],
    xAxis: {
      type: 'category', // 坐标类型
      data: xData,
      show: true, // 是否显示
      color: '#fff',
      axisLabel: {
        textStyle: {
          color: '#ffffff'
        }
      },
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      }
    },
    yAxis: {
      name: 'MPa',
      type: 'value',
      color: '#ffffff',
      axisLabel: {
        textStyle: {
          color: '#ffffff'
        }
      },
      splitArea: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#ffffff',
          opacity: 0.2,
          type: 'dashed'
        }
      }
    },
    grid: {
      left: 50,
      right: 30,
      top: 30,
      bottom: 30
    },
    series: {
      type: 'candlestick',
      data: Data || [],
      itemStyle: {
        color: '#54a8ff',
        borderColor: '#54a8ff',
        borderColor0: '#54a8ff',
        color0: '##54a8ff'
      }
    }
  }
  return option
}
