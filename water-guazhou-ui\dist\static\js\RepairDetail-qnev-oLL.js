import{d as M,cN as w,r as E,o as A,g as r,n as u,p as F,q as b,F as c,aB as h,aJ as k,h as f,i as t,l as o,bB as D,bE as p,bl as L,bm as S,C as $}from"./index-r0dFAfgr.js";import{r as x,c as N}from"./chart-wy3NEK2T.js";import{a as B}from"./index-CcDafpIP.js";import{S as z}from"./workorder-jXNat1mh.js";import{s as q}from"./sortBy-DDhdj0i5.js";const H={class:"one-map-detail"},J={class:"chart-wrapper"},P={key:0,class:"empty"},W={key:0,class:"empty"},j={key:0,class:"empty"},G=M({__name:"RepairDetail",emits:["refresh","mounted"],setup(K,{expose:I,emit:U}){const{proxy:g}=w(),O=U,e=E({activeName_Bar1:"today",activeName_Bar2:"today",activeName_Ring:"today",tabs:[{label:"本日",value:"today"},{label:"本月",value:"month"},{label:"本年",value:"year"}],detailLoading:!1,option2:x(),option3:N("人员上报事件数量排行"),option4:N("巡检人员里程数排行")}),R=async()=>{var a,n,_,v,y;if(!e.curRow)return;const s=e.activeName_Ring==="year"?o().startOf("y").valueOf():e.activeName_Ring==="month"?o().startOf("M").valueOf():o().startOf("D").valueOf(),l=e.activeName_Ring==="year"?o().endOf("y").valueOf():e.activeName_Ring==="month"?o().endOf("M").valueOf():o().endOf("D").valueOf(),i=(_=(n=(a=(await z({fromTime:s,toTime:l,processUserId:e.curRow.userId})).data)==null?void 0:a.data)==null?void 0:n.data)==null?void 0:_.map(m=>({name:m.key,nameAlias:m.key,value:m.value,valueAlias:m.value,scale:(m.percentage||0)*100+"%"}));e.option2=x(i,"个"),await D(),(y=(v=g.$refs["refChart2"+e.activeName_Ring])==null?void 0:v[0])==null||y.resize()},C=async()=>{var _,v,y,m;if(!e.curRow)return;const s=e.activeName_Bar1==="year"?o().startOf("y").format(p):e.activeName_Bar1==="month"?o().startOf("M").format(p):o().startOf("D").format(p),l=e.activeName_Bar1==="year"?o().endOf("y").format(p):e.activeName_Bar1==="month"?o().endOf("M").format(p):o().endOf("D").format(p),d=await z({fromTime:s,toTime:l,processUserId:e.curRow.userId}),i=[],a=[],n=q(((v=(_=d.data)==null?void 0:_.data)==null?void 0:v.data)||[],"value");if(n.map(V=>{i.push(V.key),a.push(V.value)}),!n.length){e.option3=void 0;return}e.option3=N("人员维修工单数量排行",i,a),await D(),(m=(y=g.$refs["refChart3"+e.activeName_Bar1])==null?void 0:y[0])==null||m.resize()},T=async()=>{e.option4=void 0};return I({refreshDetail:async s=>{s&&(e.curRow=s,R(),C(),T(),O("refresh",{...s||{},title:s==null?void 0:s.userName}))}}),A(()=>{O("mounted")}),(s,l)=>{const d=L,i=S;return r(),u("div",H,[F("div",J,[b(i,{modelValue:t(e).activeName_Ring,"onUpdate:modelValue":l[0]||(l[0]=a=>t(e).activeName_Ring=a),type:"border-card",lazy:!0,class:"darkblue tabs",onTabChange:R},{default:c(()=>[(r(!0),u(h,null,k(t(e).tabs,(a,n)=>(r(),f(d,{key:n,label:a.label,name:a.value},{default:c(()=>[t(e).option2?(r(),f(t(B),{key:1,ref_for:!0,ref:"refChart2"+a.value,autoresize:"",theme:"dark",option:t(e).option2},null,8,["option"])):(r(),u("div",P," 暂无数据 "))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"]),b(i,{modelValue:t(e).activeName_Bar1,"onUpdate:modelValue":l[1]||(l[1]=a=>t(e).activeName_Bar1=a),type:"border-card",class:"darkblue tabs",onTabChange:C},{default:c(()=>[(r(!0),u(h,null,k(t(e).tabs,(a,n)=>(r(),f(d,{key:n,label:a.label,name:a.value},{default:c(()=>[t(e).option3?(r(),f(t(B),{key:1,ref_for:!0,ref:"refChart3"+a.value,autoresize:"",theme:"dark",option:t(e).option3},null,8,["option"])):(r(),u("div",W," 暂无数据 "))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"]),b(i,{modelValue:t(e).activeName_Bar2,"onUpdate:modelValue":l[2]||(l[2]=a=>t(e).activeName_Bar2=a),type:"border-card",class:"darkblue tabs",onTabChange:T},{default:c(()=>[(r(!0),u(h,null,k(t(e).tabs,(a,n)=>(r(),f(d,{key:n,label:a.label,name:a.value},{default:c(()=>[t(e).option4?(r(),f(t(B),{key:1,ref_for:!0,ref:"refChart4"+a.value,autoresize:"",theme:"dark",option:t(e).option4},null,8,["option"])):(r(),u("div",j," 暂无数据 "))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])])}}}),te=$(G,[["__scopeId","data-v-dab55583"]]);export{te as default};
