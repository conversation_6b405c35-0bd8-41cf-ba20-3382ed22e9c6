import{d as Y,M as T,c as p,s as C,r as n,bF as k,x as y,a8 as u,bQ as U,ar as I,a9 as M,bT as N,D as E,o as W,g as w,n as F,q as d,i as c,b6 as H,b7 as P}from"./index-r0dFAfgr.js";import{_ as R}from"./CardTable-rdWOL4_6.js";import{_ as z}from"./CardSearch-CB_HNR-Q.js";import{I as x}from"./common-CvK_P_ao.js";import{p as j,g as B,a as A}from"./equipmentScrapped-Gar_DpYq.js";import{g as O}from"./equipmentOutStock-BiNkB8x8.js";import{b as Q}from"./equipmentPurchase-KOqzaoYr.js";import{a as $}from"./ledgerManagement-CkhtRd8m.js";import{f as G}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const J={class:"wrapper"},ne=Y({__name:"index",setup(K){const{$btnPerms:_}=T(),b=p(),m=p(),v=p(),D=p([]),f=p(),S=p({filters:[{label:"报废单编码",field:"code",type:"input",labelWidth:"90px"},{label:"报废单标题",field:"name",type:"input",labelWidth:"90px"},{label:"申请人员",field:"uploadUserId",type:"department-user"},{label:"经办人",field:"handleUserId",type:"department-user"},{type:"date",label:"报废单创建时间",field:"createTime",labelWidth:"120px",format:"YYYY-MM-DD HH:mm:ss",onChange:()=>o()}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:x.QUERY,click:()=>o()},{type:"default",perm:!0,text:"重置",svgIcon:C(P),click:()=>{var e;(e=b.value)==null||e.resetForm(),o()}},{type:"success",perm:!0,text:"新增",icon:x.ADD,click:()=>V()}]}]}),s=n({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"报废单标题",prop:"name"},{label:"报废单编码",prop:"code"},{label:"报废申请时间",prop:"uploadTime",formatter:e=>e.uploadTime?k(e.uploadTime).format("YYYY-MM-DD"):""},{label:"申请部门",prop:"uploadUserDepartmentName"},{label:"申请人",prop:"uploadUserName"},{label:"经办人",prop:"handleUserName"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime"},{label:"报废状态",prop:"isDump",formatter:e=>e.isDump?"已报废":"待报废"}],operationWidth:"160px",operations:[{type:"primary",color:"#4195f0",text:"详情",perm:_("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>q(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:a})=>{s.pagination.page=e,s.pagination.limit=a,o()}}}),i=n({title:"新增报废单",labelWidth:"100px",submitting:!1,submit:(e,a)=>{var l;if(a){L.defaultValue={storehouseId:e.storehouseId},t.getDevice({storehouseId:e.storehouseId}),(l=f.value)==null||l.openDrawer();return}if(i.submitting=!0,!t.selectList.length){y.warning("请选择设备");return}j(e).then(()=>{var r;y.success("添加成功"),o(),i.submitting=!1,(r=m.value)==null||r.closeDrawer()}).catch(r=>{y.warning(r),i.submitting=!1})},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"报废单标题",field:"name",rules:[{required:!0,message:"请输入报废单标题"}]},{xl:8,readonly:!0,type:"input-number",label:"报废单编码",field:"code",rules:[{required:!0,message:"请输入报废单编码"}]},{xl:8,type:"select",label:"仓库名称",field:"storehouseId",rules:[{required:!0,message:"请输入仓库名称"}],options:u(()=>t.storeList)},{xl:8,type:"department-user",label:"申请人员",field:"uploadUserId",rules:[{required:!0,message:"请输入申请人员"}]},{xl:8,type:"department-user",label:"经办人",field:"handleUserId",rules:[{required:!0,message:"请输入经办人"}]},{xl:8,type:"date",label:"报废时间",field:"uploadTime",rules:[{required:!0,message:"请输入报废时间"}],format:"YYYY-MM-DD HH:mm:ss"},{xl:16,type:"textarea",label:"备注",field:"remark"},{type:"table",field:"items",config:{indexVisible:!0,height:"350px",dataList:u(()=>t.selectList),titleRight:[{style:{justifyContent:"flex-end",marginBottom:"10px"},items:[{type:"btn-group",btns:[{text:"增加设备",perm:!0,click:()=>{var e;(e=m.value)==null||e.Submit(!0)}}]}]}],columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"型号/规格",prop:"model"},{label:"所属大类",prop:"topType"},{label:"货架",prop:"shelvesName"},{label:"所属类别",prop:"type"}],operations:[{text:"移除",perm:!0,type:"danger",icon:x.DELETE,click:e=>{t.selectList=t.selectList.filter(a=>a.id!==e.id)}}],pagination:{hide:!0}}}]}]}),g=n({title:"详情",labelWidth:"100px",defaultValue:{},group:[{fields:[{xl:8,disabled:!0,type:"input",label:"报废单标题",field:"name",rules:[{required:!0,message:"请输入报废单标题"}]},{xl:8,disabled:!0,type:"input",label:"报废单编码",field:"code",rules:[{required:!0,message:"请输入报废单编码"}]},{xl:8,readonly:!0,type:"select",label:"仓库名称",field:"storehouseId",rules:[{required:!0,message:"请输入仓库名称"}],options:u(()=>t.storeList)},{xl:8,disabled:!0,type:"input",label:"申请部门",field:"uploadUserDepartmentName",rules:[{required:!0,message:"请输入申请部门"}]},{xl:8,disabled:!0,type:"input",label:"申请人员",field:"uploadUserName",rules:[{required:!0,message:"请输入申请人员"}]},{xl:8,disabled:!0,type:"input",label:"经办部门",field:"handleUserDepartmentName",rules:[{required:!0,message:"请输入经办部门"}]},{xl:8,disabled:!0,type:"input",label:"经办人",field:"handleUserName",rules:[{required:!0,message:"请输入经办人"}]},{xl:8,readonly:!0,type:"date",label:"报废时间",field:"uploadTime",rules:[{required:!0,message:"请输入报废时间"}],format:"YYYY-MM-DD HH:mm:ss"},{xl:16,disabled:!0,type:"textarea",label:"备注",field:"remark"},{type:"table",field:"items",config:{indexVisible:!0,height:"350px",dataList:u(()=>t.selectList),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"型号/规格",prop:"model"},{label:"所属大类",prop:"topType"},{label:"货架",prop:"shelvesName"},{label:"所属类别",prop:"type"}],pagination:{hide:!0}}}]}]}),L=n({title:"设备选择",submit:(e,a)=>{var l;delete e.device,a?t.getDevice(e):(t.selectList=[...t.selectList,...D.value],t.selectList=U(t.selectList,["deviceLabelCode"]),(l=f.value)==null||l.closeDrawer())},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"标签编码",field:"deviceLabelCode"},{xl:8,type:"input",label:"设备名称",field:"name"},{xl:8,type:"input",label:"设备型号",field:"model"},{type:"table",field:"device",config:{indexVisible:!0,height:"350px",dataList:u(()=>t.deviceValue),selectList:[],handleSelectChange:e=>{D.value=e},titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"搜索",perm:!0,click:()=>{var e;(e=f.value)==null||e.Submit(!0)}}]}]}],columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"货架",prop:"shelvesName"}],pagination:{hide:!0}}}]}]}),V=()=>{var e;i.title="新增报废单",t.selectList=[],i.defaultValue={code:G(new Date,"YYYYMMDDHHmmss")},(e=m.value)==null||e.openDrawer()},q=e=>{var a;g.title="报废单详情",g.defaultValue={...e||{}},t.getSelectValue(e),(a=v.value)==null||a.openDrawer()},t=n({WaterSupplyTree:[],UserList:[],DevicePurchase:[],storeList:[],deviceValue:[],total:0,selectList:[],getDevice:e=>{const a={size:99999,page:1,...e};$(a).then(l=>{t.deviceValue=l.data.data.data||[]})},getWaterSupplyTreeValue:()=>{I(2).then(a=>{t.WaterSupplyTree=M(a.data.data||[])})},getUserListValue:e=>{N({pid:e}).then(a=>{const l=a.data.data.data||[];t.UserList=l.map(r=>({label:r.firstName,value:E(r.id.id)}))})},getDevicePurchaseValue:()=>{Q({page:1,size:99999}).then(a=>{const l=a.data.data.data||[];t.DevicePurchase=l.map(r=>({label:r.title,value:r.id}))})},getstoreSerchValue:()=>{O({page:1,size:99999}).then(a=>{const l=a.data.data.data||[];t.storeList=l.map(r=>({label:r.name,value:r.id}))})},getSelectValue:e=>{const a={page:1,size:99999,mainId:e.id};B(a).then(l=>{t.selectList=l.data.data.data||[]})},init:()=>{t.getWaterSupplyTreeValue(),t.getDevicePurchaseValue(),t.getstoreSerchValue()}}),o=async()=>{var a;const e={size:s.pagination.limit,page:s.pagination.page,...((a=b.value)==null?void 0:a.queryParams)||{}};A(e).then(l=>{s.dataList=l.data.data.data||[],s.pagination.total=l.data.data.total||0})};return W(()=>{o(),t.init()}),(e,a)=>{const l=z,r=R,h=H;return w(),F("div",J,[d(l,{ref_key:"refSearch",ref:b,config:c(S)},null,8,["config"]),d(r,{config:c(s),class:"card-table"},null,8,["config"]),d(h,{ref_key:"refForm",ref:m,config:c(i)},null,8,["config"]),d(h,{ref_key:"detailForm",ref:v,config:c(g)},null,8,["config"]),d(h,{ref_key:"refFormEquipment",ref:f,config:c(L)},null,8,["config"])])}}});export{ne as default};
