package org.thingsboard.server.dao.model.sql.shuiwu;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SHUIWU_PIPE_POINT_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PipePointEntity {


    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SHUIWU_PIPE_POINT_CODE)
    private String code;

    @Column(name = ModelConstants.SHUIWU_PIPE_POINT_NAME)
    private String name;

    @Column(name = ModelConstants.SHUIWU_PIPE_POINT_LOCATION)
    private String location;

    @Column(name = ModelConstants.SHUIWU_PIPE_POINT_POINT_ELEVATION)
    private BigDecimal pointElevation;

    @Column(name = ModelConstants.SHUIWU_PIPE_POINT_POINT_DEPTH)
    private BigDecimal depth;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.PROJECT_RELATION_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;
}
