package org.thingsboard.server.dao.sql.revenue;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.revenue.RemoteMeterData;

import java.util.Date;
import java.util.List;

@Mapper
public interface RemoteMeterDataMapper extends BaseMapper<RemoteMeterData> {
    List<RemoteMeterData> selectAllByTime(@Param("start") Date start, @Param("end") Date end);
}
