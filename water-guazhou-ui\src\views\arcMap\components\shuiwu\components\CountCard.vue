<template>
  <div class="card" :class="useAppStore().isDark ? 'darkblue' : ''">
    <div class="header">
      <span class="title">DMA总数</span>
      <span class="info">
        <span class="count">{{ row?.overview?.total || 0 }}</span>
        <span class="unit">个</span>
      </span>
    </div>
    <div class="content">
      <div class="content-item">
        <span class="title">已运营</span>
        <span class="count">{{ row?.overview?.start || 0 }}</span>
      </div>
      <div class="content-item">
        <span class="title">规划中</span>
        <span class="count">{{ row?.overview?.plan || 0 }}</span>
      </div>
      <div class="content-item">
        <span class="title">已停运</span>
        <span class="count">{{ row?.overview?.stop || 0 }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useAppStore } from '@/store';

defineProps<{
  row: any;
  modelValue?: any;
}>();
</script>
<style lang="scss" scoped>
.card {
  color: #333;
  padding: 12px;
  background-color: #c7c7c7;
  &.darkblue {
    background-color: #283e55;
    color: #fff;
    .content {
      background-color: #213549;
    }
  }
  .header {
    display: flex;
    justify-content: space-between;
    line-height: 40px;
    .title {
      font-size: 14px;
    }

    .info {
      color: #3bbdb5;

      .count {
        font-size: 16px;
      }
      .unit {
        font-size: 12px;
      }
    }
  }
  .content {
    background-color: #fff;
    width: 100%;
    display: flex;
    align-items: center;
    height: 35px;
    &-item {
      flex: 1;
      height: 16px;
      font-size: 12px;
      display: flex;
      padding: 0 8px;
      justify-content: space-between;
      align-items: center;
      &:not(:last-child) {
        border-right: 1px solid #316d6e;
      }
      &:nth-child(1) {
        .count {
          color: #3ea0ce;
        }
      }
      &:nth-child(2) {
        .count {
          color: #b89541;
        }
      }
      &:nth-child(3) {
        .count {
          color: #cc3f40;
        }
      }
    }
  }
}
</style>
