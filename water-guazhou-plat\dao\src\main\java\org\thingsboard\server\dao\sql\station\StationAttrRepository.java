package org.thingsboard.server.dao.sql.station;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;

import java.util.List;

public interface StationAttrRepository extends JpaRepository<StationAttrEntity, String> {
    List<StationAttrEntity> findByStationIdOrderByOrderNum(String stationId);

    @Modifying
    @Transactional
    void deleteByStationId(String stationId);

    List<StationAttrEntity> findByStationIdAndTypeOrderByOrderNum(String stationId, String type);

    @Modifying
    @Transactional
    void deleteByStationIdAndType(String stationId, String type);

    List<StationAttrEntity> findByStationIdInAndTypeOrderByOrderNum(List<String> stationIdList, String type);

    @Query("SELECT sa.type FROM StationAttrEntity sa " +
            "WHERE sa.stationId = ?1 " +
            "GROUP BY sa.type " +
            "ORDER BY sa.type")
    List<String> groupByType(String stationId);

    List<StationAttrEntity> findByIdIn(List<String> ids);

    List<StationAttrEntity> findByDeviceIdAndAttrIn(String deviceId, List<String> attrList);

    List<StationAttrEntity> findByDeviceId(String deviceId);

    List<StationAttrEntity> findByStationIdInAndAttr(List<String> id, String attr);
}
