<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipeUseWaterReportMapper">

    <select id="getWaterBalance" resultType="org.thingsboard.server.dao.model.DTO.PipeUseWaterReportDTO">
        select sum(own_supply_water) as ownSupplyWater,
                   sum(buy_supply_water) as buySupplyWater,
            sum(batch_sale_water) as batchSaleWater,
            sum(supply_total_water) as supplyTotalWater,
            sum(fee_metering_use_water) as feeMeteringUseWater,
            sum(huanwei_use_water) as huanweiUseWater,
            sum(lvhua_use_water) as lvhuaUseWater,
            sum(pipe_use_water) as pipeUseWater,
            sum(sunhuai_use_water) as sunhuaiUseWater,
            sum(dingliang_use_water) as dingliangUseWater,
            sum(fee_no_metering_use_water) as feeNoMeteringUseWater,
            sum(bangong_use_water) as bangongUseWater,
            sum(xiaofang_use_water) as xiaofangUseWater,
            sum(jianmian_use_water) as jianmianUseWater,
            sum(free_metering_use_water) as freeMeteringUseWater,
            sum(free_xiaofang_use_water) as freeXiaofangUseWater,
            sum(free_weihu_use_water) as freeWeihuUseWater,
            sum(free_bangong_use_water) as freeBangongUseWater,
            sum(free_chongxi_use_water) as freeChongxiUseWater,
            sum(free_no_metering_use_water) as freeNoMeteringUseWater,
            sum(use_total_water) as useTotalWater,
            sum(front_point_leak_water) as frontPointLeakWater,
            sum(front_pipe_leak_water) as frontPipeLeakWater,
            sum(front_leak_total_water) as frontLeakTotalWater,
            sum(backend_checked_leak_water) as backendCheckedLeakWater,
            sum(backend_no_checked_water) as backendNoCheckedWater,
            sum(backend_leak_total_water) as backendLeakTotalWater,
            sum(background_leak_water) as backgroundLeakWater,
            sum(shuixiang_leak_water) as shuixiangLeakWater,
            sum(leak_total_water) as leakTotalWater,
            sum(cust_mistake_loss_water) as custMistakeLossWater,
            sum(non_cust_mistake_loss_water) as nonCustMistakeLossWater,
            sum(mistake_loss_total_water) as mistakeLossTotalWater,
            sum(toudao_loss_water) as toudaoLossWater,
            sum(copy_meter_loss_water) as copyMeterLossWater,
            sum(other_loss_water) as otherLossWater,
            sum(no_register_loss_water) as noRegisterLossWater,
            sum(pipe_loss_water) as pipeLossWater,
            sum(other_loss_total_water) as otherLossTotalWater,
            sum(loss_total_water) as lossTotalWater

            from tb_pipe_use_water_report

            where partition_id = #{partitionId} and ym like '%' || #{ym} || '%'

            group by partition_id

    </select>

    <select id="getSystemData" resultType="org.thingsboard.server.dao.model.DTO.PipeUseWaterReportDTO">
        select ifnull(dn75_pipe_length, 0) as dn75_pipe_length,
                   ifnull(unit_supply_pipe_length, 0) as unit_supply_pipe_length,
            ifnull(year_avg_pressure, 0) as year_avg_pressure,
            ifnull(cust_copied_water, 0) as cust_copied_water,
            ifnull(max_frozen_soil_depth, 0) as max_frozen_soil_depth

            from tb_pipe_use_water_report
            where partition_id = #{partitionId} and ym like '%' || #{ym} || '%'
            offset 0 limit 1;

    </select>

    <select id="getWaterBalanceMonth" resultType="org.thingsboard.server.dao.model.DTO.PipeUseWaterReportDTO">
        select * from tb_pipe_use_water_report
        where partition_id = #{partitionId} and ym = #{ym}
    </select>
</mapper>