import{ab as b,j as u,a7 as d,ac as h}from"./index-r0dFAfgr.js";const f=(e=[],r,t,l=2)=>{const s="总数",i=function(a){const m=/(?=(\B)(\d{3})+$)/g;return a.toString().replace(m,",")},n=e.reduce((a,m)=>a+(parseFloat(m.value)||0)*1,0),p=b(n);return{tooltip:{trigger:"item",formatter:a=>(t||"")+a.name+": "+Number(a.value).toFixed(l)+" "+r},legend:{type:"scroll",icon:"circle",orient:"vertical",left:"right",top:"center",align:"left",itemGap:10,itemWidth:10,itemHeight:10,symbolKeepAspect:!0,textStyle:{color:"#fff",rich:{name:{align:"left",width:190,fontSize:12,color:u().isDark?"#fff":"#2A2A2A"},value:{align:"left",width:80,fontSize:12,color:"#00ff00"},count:{align:"left",width:70,fontSize:12},upRate:{align:"left",fontSize:12},downRate:{align:"left",fontSize:12,color:"#409EFF"}}},data:e.map(a=>a.name),formatter(a){var m;if(e&&e.length)for(let o=0;o<e.length;o++){const y=(m=e[o].scale)==null?void 0:m.substring(0,e[o].scale.length-1);if(a===e[o].name)return"{name| "+(e[o].nameAlias||a)+"}{value| "+(e[o].valueAlias||e[o].value)+" "+(r||"")+"}{downRate| "+(i(Number(y||"0").toFixed(l))+"%"||"")+"}"}}},title:[{text:"{name|"+s+(r&&"("+p.unit+r+")"||"("+p.unit+")")+`}
{val|`+i(p.value.toFixed(l))+"}",top:"center",left:"19%",textAlign:"center",textStyle:{rich:{name:{fontSize:10,fontWeight:"normal",padding:[8,0],align:"center",color:u().isDark?"#fff":"#2A2A2A"},val:{fontSize:16,fontWeight:"bold",color:u().isDark?"#fff":"#2A2A2A"}}}}],series:[{type:"pie",radius:["45%","60%"],center:["20%","50%"],data:e,hoverAnimation:!0,label:{show:!1,formatter:a=>"{icon|●}{name|"+a.name+"}{value|"+i(Number(a.value||"0").toFixed(l))+"}",padding:[0,-100,25,-100],rich:{icon:{fontSize:16},name:{fontSize:14,padding:[0,10,0,4]},value:{fontSize:18,fontWeight:"bold"}}}}]}},A=e=>{var t,l,s,i,n,p,x,a,m,o;return{title:{text:"24小时运行曲线",left:"30",textStyle:{color:"#fff"}},tooltip:{trigger:"axis",axisPointer:{type:"cross"}},grid:{left:70,right:90,bottom:30,top:70},legend:{textStyle:{color:u().isDark?"#fff":"#7C8295"}},xAxis:{type:"category",boundaryGap:!1,data:["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"]},yAxis:[{name:((e==null?void 0:e.line1.name)||"")+(e!=null&&e.line1.unit?"("+((e==null?void 0:e.line1.unit)||"")+")":""),type:"value",splitLine:{lineStyle:{color:"#ffffff",opacity:.2,type:"dashed"}},axisLine:{show:!0,lineStyle:{color:"#666"}},position:"left"},...[e!=null&&e.line2?{name:(((t=e==null?void 0:e.line2)==null?void 0:t.name)||"")+((l=e==null?void 0:e.line2)!=null&&l.unit?"("+((e==null?void 0:e.line2.unit)||"")+")":""),type:"value",splitLine:{show:!1,lineStyle:{color:"#ffffff",opacity:.2,type:"dashed"}},axisLine:{show:!0,lineStyle:{color:"#666"}},alignTicks:!0}:void 0].filter(y=>!!y),...[e!=null&&e.line3?{name:(((s=e==null?void 0:e.line3)==null?void 0:s.name)||"")+((i=e==null?void 0:e.line3)!=null&&i.unit?"("+((e==null?void 0:e.line3.unit)||"")+")":""),type:"value",splitLine:{show:!1,lineStyle:{color:"#ffffff",opacity:.2,type:"dashed"}},position:"right",alignTicks:!0,offset:60,axisLine:{show:!0,lineStyle:{color:"#666"}}}:void 0].filter(y=>!!y)],series:[{name:(e==null?void 0:e.line1.name)||"",type:"line",data:(e==null?void 0:e.line1.data)||[],markPoint:{data:[{type:"max",name:"Max"},{type:"min",name:"Min"}]}},e!=null&&e.line2?{yAxisIndex:((n=e==null?void 0:e.line2)==null?void 0:n.yAxisIndex)??1,name:((p=e==null?void 0:e.line2)==null?void 0:p.name)||"",type:"line",data:((x=e==null?void 0:e.line2)==null?void 0:x.data)||[],markPoint:{data:[{type:"max",name:"Max"},{type:"min",name:"Min"}]}}:{},e!=null&&e.line3?{yAxisIndex:((a=e==null?void 0:e.line3)==null?void 0:a.yAxisIndex)??2,name:((m=e==null?void 0:e.line3)==null?void 0:m.name)||"",type:"line",data:((o=e==null?void 0:e.line3)==null?void 0:o.data)||[],markPoint:{data:[{type:"max",name:"Max"},{type:"min",name:"Min"}]}}:{}]}};function v(){const e={tooltip:{formatter:"{a} <br/>{b} : {c}%"},toolbox:{feature:{restore:{},saveAsImage:{}}},series:[{name:"业务指标",type:"gauge",detail:{formatter:"{value}%"},data:[{value:50,name:"完成率"}],axisLine:{show:!0,lineStyle:{color:[[1,new d(0,0,1,0,[{offset:.1,color:"#FFC600"},{offset:.6,color:"#30D27C"},{offset:1,color:"#0B95FF"}])]]}}}]};return setInterval(()=>{e.series[0].data[0].value=parseFloat((Math.random()*100).toFixed(2))},2e3),e}function w(e,r,t){return{xAxis:{type:"category",boundaryGap:!1,data:e},tooltip:{show:!0,trigger:"axis"},yAxis:{type:"value",name:(t.name||"")+(t.unit?"("+t.unit+")":"")},series:[{data:r,type:"line",smooth:!0,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:h(t.color1||"#63a4b9",.3)},{offset:1,color:h(t.color2||t.color1||"#63a4b9",.1)}],global:!1}}}]}}function z(e="",r=[],t=[]){return{title:{text:e,textStyle:{color:u().isDark?"#aaa":"#333"}},grid:{left:10,right:40,bottom:10,top:30,containLabel:!0},xAxis:[{splitLine:{show:!1},type:"value",show:!1}],yAxis:[{splitLine:{show:!1},axisLine:{show:!1},type:"category",axisTick:{show:!1},data:r,axisLabel:{}}],series:[{name:"标准化",type:"bar",barWidth:10,label:{show:!0,position:"right",color:"#1CD8A8",fontSize:14,fontWeight:"bold",distance:5},itemStyle:{barBorderRadius:[20,20,20,20],color:new d(0,0,1,0,["#2FAEF2","#1CD8A8"].map((s,i)=>({color:s,offset:i})))},data:t}]}}function F(e=[],r,t=[{offset:1,color:"#83bff6"},{offset:0,color:"#188df0"}],l="管长"){const s=e.map(n=>n.nameAlias||n.name);return{tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:50,top:30,right:30,bottom:20},xAxis:{type:"category",data:s},yAxis:{name:r,type:"value",splitLine:{lineStyle:{color:"#ffffff",opacity:.2,type:"dashed"}}},series:[{name:l,type:"bar",barWidth:10,itemStyle:{color:new d(0,1,1,0,t)},data:e,label:{show:!0,position:"top"}}]}}function L(e=[{name:"常规",value:175.17},{name:"紧急",value:148.35},{name:"疑难",value:95.36}]){const r=[],t=[],l="#02c3f1",s="rgba(2,195,241,0.1)";return e.forEach(n=>{r.push(n.name),t.push(n.value)}),{title:{text:"多色立体圆柱",top:20,left:"center",textStyle:{color:"#fff",fontSize:20}},grid:{top:"25%",bottom:"15%"},xAxis:{data:r},yAxis:{},series:[{name:"柱顶部",type:"pictorialBar",symbolSize:[26,10],symbolOffset:[0,-5],z:12,itemStyle:{normal:{color(){return l}}},label:{show:!0,position:"top",fontSize:16},symbolPosition:"end",data:t},{name:"柱底部",type:"pictorialBar",symbolSize:[26,10],symbolOffset:[0,5],z:12,itemStyle:{normal:{color(){return l}}},data:t},{name:"第一圈",type:"pictorialBar",symbolSize:[47,16],symbolOffset:[0,11],z:11,itemStyle:{normal:{color:"transparent",borderColor:"#3ACDC5",borderWidth:2}},data:t},{name:"第二圈",type:"pictorialBar",symbolSize:[62,22],symbolOffset:[0,17],z:10,itemStyle:{normal:{color:"transparent",borderColor:l,borderWidth:2}},data:t},{type:"bar",itemStyle:{normal:{color(){return new d(0,0,0,1,[{offset:1,color:l},{offset:0,color:s}])},opacity:.8}},z:16,silent:!0,barWidth:26,barGap:"-100%",data:t}]}}export{v as a,w as b,z as c,L as d,A as h,F as o,f as r};
