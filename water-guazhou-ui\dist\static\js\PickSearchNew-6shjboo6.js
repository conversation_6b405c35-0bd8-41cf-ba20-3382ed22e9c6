import{D as M}from"./DrawerBox-CLde5xC8.js";import{d as S,ad as E,c as I,r as C,a8 as U,am as q,Q as G,bo as H,bR as W,i as B,g as w,n as L,ax as T,C as j,cN as K,o as O,aB as Q,aJ as z,h as A,F as b,bB as X,q as P,p as Y,bh as Z,_ as V,aq as tt,W as et,b as rt}from"./index-r0dFAfgr.js";import{_ as ot}from"./ArcLayout-CHnHL9Pv.js";import{g as it,n as st}from"./MapView-DaoQedLH.js";import{w as at}from"./Point-WxyopZva.js";import{g as R,s as nt,p as pt}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import{i as lt,e as mt}from"./IdentifyHelper-RJWmLn49.js";import{g as ct}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as ft}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import"./SideDrawer-CBntChyn.js";import"./ArcView-DpMnCY82.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./pipe-nogVzCHG.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./identify-4SBo5EZk.js";const ut=S({__name:"ArcPop",props:{markUrl:{},xoffset:{},yoffset:{},visible:{type:Boolean},x:{},y:{},longitude:{},latitude:{},markWidth:{},markHeight:{}},emits:["toggled"],setup(k,{expose:_,emit:h}){const a=E("view");let f;const t=I(),n=k,g=C({visible:!!n.visible}),v=h,l=()=>{g.visible=!0,o(),v("toggled",!0)},p=r=>{g.visible=r??!g.visible,g.visible&&o(),v("toggled",g.visible)},i=()=>{g.visible=!1,v("toggled",!1)},o=()=>{var r,m;(m=(r=t.value)==null?void 0:r.parentElement)==null||m.appendChild(t.value)},e=(r=a,m)=>{if(!r||(m=m||s.value,!m))return;const y=new at({...m,spatialReference:r==null?void 0:r.spatialReference});if(n.markUrl&&(f?f.geometry=y:(f=new it({geometry:y,symbol:new st({url:n.markUrl,xoffset:n.xoffset,yoffset:n.yoffset,width:n.markWidth??20,height:n.markHeight??20})}),a==null||a.graphics.add(f))),!t.value)return;const u=r==null?void 0:r.toScreen(y);t.value.style.left=((u==null?void 0:u.x)||-1e4)+(n.xoffset||0)+"px",t.value.style.top=((u==null?void 0:u.y)||-1e4)+(n.yoffset||-10)+"px"},s=U(()=>({x:n.x,y:n.y,longitude:n.longitude,latitude:n.latitude}));return q(()=>s.value,r=>{r&&e()},{immediate:!0}),G(()=>{f&&(a==null||a.graphics.remove(f))}),_({open:l,close:i,toggle:p,setPosition:e}),(r,m)=>H((w(),L("div",{ref_key:"refContainer",ref:t,class:"arc-infowindow"},[T(r.$slots,"default",{},void 0,!0)],512)),[[W,B(g).visible]])}}),dt=j(ut,[["__scopeId","data-v-fa8a2e1c"]]),gt={class:"pop-container"},yt=S({__name:"ArcPops",props:{pops:{}},setup(k,{expose:_}){const h=k,a=E("view"),{proxy:f}=K(),t=l=>{var i;const p=f.$refs["refPop"+l.id];p!=null&&p[0]&&((i=p[0])==null||i.setPosition(a))},n=(l,p)=>{var o;const i=f.$refs["refPop"+l];i!=null&&i[0]&&((o=i[0])==null||o.toggle(p),X().then(()=>{var e;(e=i[0])==null||e.setPosition(a)}))};return _({togglePop:(l,p)=>{const i=l.id;i!==void 0&&n(i,p)},togglePopById:n,closeAllPop:()=>{var l;(l=h.pops)==null||l.map(p=>{var e;const i=p==null?void 0:p.id;if(!i)return;const o=f.$refs["refPop"+i];o!=null&&o.length&&((e=o[0])==null||e.toggle(!1))})},setPopPosition:t}),O(()=>{a==null||a.watch("extent",()=>{var l;(l=h.pops)==null||l.map(p=>{var o;(o=f.$refs["refPop"+p.id][0])==null||o.setPosition(a)})})}),(l,p)=>{const i=dt;return w(),L("div",gt,[(w(!0),L(Q,null,z(l.pops,o=>{var e,s,r;return w(),A(i,{key:o.id,ref_for:!0,ref:"refPop"+o.id,latitude:o.latitude,longitude:o.longitude,x:o.x,y:o.y,"mark-url":(e=o.symbolConfig)==null?void 0:e.url,"mark-width":(s=o.symbolConfig)==null?void 0:s.width,"mark-height":(r=o.symbolConfig)==null?void 0:r.height},{default:b(()=>[T(l.$slots,"default",{config:o})]),_:2},1032,["latitude","longitude","x","y","mark-url","mark-width","mark-height"])}),128))])}}}),ht={style:{width:"200px",height:"200px","background-color":"red"}},or=S({__name:"PickSearchNew",setup(k){const _=I(),h=I(),a=I(),f=C({pops:[]}),t={view:void 0,identifyResults:[]},n=C({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!1,showCheckbox:!0,field:"layerid",nodeKey:"value"}]},{fieldset:{desc:"点击地图进行查询"},fields:[]}]}),g=C({height:"none",columns:[{label:"类型",prop:"layerName"},{label:"编号",prop:"新编号"}],pagination:{hide:!0},handleRowClick:async e=>{var r;const s=t.identifyResults.find(m=>m.feature.attributes.OBJECTID===e.OBJECTID);await R(t.view,s==null?void 0:s.feature),(r=h.value)==null||r.togglePopById(e.OBJECTID,!0)},dataList:[]}),v=()=>{var y,u;const e=et(),s=n.group[0].fields[0],r=(y=e.gLayerInfos)==null?void 0:y.filter(c=>c.geometrytype==="esriGeometryPoint").map(c=>({label:c.layername,value:c.layerid,data:c})),m=(u=e.gLayerInfos)==null?void 0:u.filter(c=>c.geometrytype==="esriGeometryPolyline").map(c=>({label:c.layername,value:c.layerid,data:c}));s&&(s.options=[{label:"管点类",value:-1,children:r},{label:"管线类",value:-2,children:m}]),a.value&&(a.value.dataForm.layerid=e.gLayerIds)},l=()=>{var e;t.view&&(ft("crosshair"),t.graphicsLayer=ct(t.view,{id:"search-pick",title:"查询结果"}),t.mapClick=(e=t.view)==null?void 0:e.on("click",async s=>{await p(s)}))},p=async e=>{var s,r,m,y,u,c;if(t.view)try{const x=lt({layerIds:((s=a.value)==null?void 0:s.dataForm.layerid)||[],geometry:e.mapPoint,mapExtent:t.view.extent}),D=await mt(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,x);if(!((r=D.results)!=null&&r.length)){rt.warning("没有查询到设备");return}(m=t.graphicsLayer)==null||m.removeAll(),t.identifyResults=D.results||[],t.identifyResults.length>50&&(t.identifyResults.length=50);const F=[],$=[];t.identifyResults.map(d=>{F.push({layerName:d.layerName,layerId:d.layerId,...d.feature.attributes}),d.feature.symbol=nt(d.feature.geometry.type),$.push(d.feature);const N=pt(d.feature.geometry)||[];f.pops.push({id:d.feature.attributes.OBJECTID,visible:!1,x:N[0],y:N[1],title:d.layerName+"("+d.feature.attributes.新编号+")",attributes:{row:d.feature.attributes,id:d.feature.attributes.OBJECTID}})}),(y=t.graphicsLayer)==null||y.addMany($),g.dataList=F,await R(t.view,t.identifyResults[0].feature);const J=(u=t.identifyResults[0])==null?void 0:u.feature.attributes.OBJECTID;(c=h.value)==null||c.togglePopById(J,!0)}catch{}},i=async e=>{t.view=e},o=()=>{v(),l()};return O(()=>{var e;(e=_.value)==null||e.toggleDrawer("rtl",!0)}),G(()=>{var e,s;(e=t.mapClick)==null||e.remove(),t.graphicsLayer&&((s=t.view)==null||s.map.remove(t.graphicsLayer))}),(e,s)=>{const r=yt,m=ot,y=V,u=tt,c=M;return w(),A(c,{ref_key:"refDrawerBox",ref:_,"right-drawer":!0,"right-drawer-title":"点选查询","right-drawer-bar-position":"top"},{right:b(()=>[P(y,{ref_key:"refForm",ref:a,config:B(n)},null,8,["config"]),P(u,{config:B(g)},null,8,["config"])]),default:b(()=>[P(m,{onMapLoaded:i,onPipeLoaded:o},{"map-bars":b(()=>[P(r,{ref_key:"refPops",ref:h,pops:B(f).pops,"close-on-click-modal":!0},{default:b(({config:x})=>[Y("div",ht,Z(x.id),1)]),_:1},8,["pops"])]),_:1})]),_:1},512)}}});export{or as default};
