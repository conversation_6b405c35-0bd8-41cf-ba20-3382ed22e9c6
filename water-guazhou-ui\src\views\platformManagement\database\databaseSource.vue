<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getBaseDatabaseSourceList,
  addBaseDatabaseSource,
  editBaseDatabaseSource,
  deleteBaseDatabaseSource,
  getBaseDatabaseSourceDetail
} from '@/api/platformManagement/baseDatabaseSource'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()
const refDialogForm = ref()

// 数字验证规则
const numberValidator = (rule, value, callback) => {
  if (value === '' || value === null) {
    callback(new Error('不能为空'))
    return
  }
  if (!/^\d+$/.test(value)) {
    callback(new Error('必须为正整数'))
  } else if (Number(value) <= 0) {
    callback(new Error('必须大于0'))
  } else {
    callback()
  }
}

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '数据源名称', 
      field: 'scName', 
      placeholder: '请输入数据源名称',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '数据源名称', prop: 'scName' },
    { label: '数据库类型', prop: 'dbType' },
    { label: '服务器地址', prop: 'dbHost' },
    { label: '端口号', prop: 'dbPort' },
    { label: '最大连接数', prop: 'dbMaxPoolSize' },
    { label: '最小空闲连接数', prop: 'minIdle' }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增数据源',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '数据源名称',
          field: 'scName',
          rules: [{ required: true, message: '请输入数据源名称' }]
        },
        {
          type: 'select',
          label: '数据库类型',
          field: 'dbType',
          options: [
            { label: 'MySQL', value: 'MySQL' },
            { label: 'Oracle', value: 'Oracle' },
            { label: 'PostgreSQL', value: 'PostgreSQL' },
            { label: 'SQL Server', value: 'SQL Server' }
          ],
          rules: [{ required: true, message: '请选择数据库类型' }]
        },
        {
          type: 'input',
          label: '服务器地址',
          field: 'dbHost',
          rules: [{ required: true, message: '请输入服务器地址' }]
        },
        {
          type: 'input',
          label: '端口号',
          field: 'dbPort',
          rules: [
            { required: true, message: '请输入端口号' },
            { validator: numberValidator, trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          label: '数据库名称',
          field: 'dbName',
          rules: [{ required: true, message: '请输入数据库名称' }]
        },
        {
          type: 'input',
          label: '数据库账号',
          field: 'dbUsername',
          rules: [{ required: true, message: '请输入数据库账号' }]
        },
        {
          type: 'password',
          label: '数据库密码',
          field: 'password',
          rules: [{ required: true, message: '请输入密码' }],
          showPassword: true
        },
        {
          type: 'input-number',
          label: '最大连接数',
          field: 'dbMaxPoolSize',
          min: 1,
          step: 1,
          rules: [
            { required: true, message: '请输入最大连接数' },
            { validator: numberValidator, trigger: 'blur' }
          ]
        },
        {
          type: 'input-number',
          label: '最小空闲连接数',
          field: 'minIdle',
          min: 1,
          step: 1,
          rules: [
            { required: true, message: '请输入最小空闲连接数' },
            { validator: numberValidator, trigger: 'blur' }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      const encryptedParams = {
        ...params,
        password: btoa(params.password)
      }
      if (params.id) {
        await editBaseDatabaseSource(encryptedParams)
        SLMessage.success('修改成功')
      } else {
        await addBaseDatabaseSource(encryptedParams)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'
}

const handleAdd = (row) => {
  resetDialogConfig()
  DialogFormConfig.title = row ? '编辑数据源' : '新增数据源'
  DialogFormConfig.defaultValue = { ...(row || {}) }
  refDialogForm.value?.openDialog()
}

const handleDetail = async (row) => {
  try {
    const res = await getBaseDatabaseSourceDetail(row.id)
    let detailData = res.data?.data || res
    if (detailData.password) {
      detailData.password = atob(detailData.password)
    }
    resetDialogConfig()
    DialogFormConfig.title = '数据源详情'
    DialogFormConfig.defaultValue = { ...detailData }
    DialogFormConfig.group[0].fields.forEach(field => {
      field.disabled = true
      field.readonly = true
      if (field.type === 'password') field.showPassword = false
    })
    DialogFormConfig.showSubmit = false
    DialogFormConfig.cancelText = '关闭'
    refDialogForm.value?.openDialog()
  } catch (error) {
    SLMessage.error('获取详情失败')
  }
}

const handleDelete = (row) => {
  SLConfirm('确定删除？', '删除提示')
    .then(async () => {
      const ids = row ? [row.id] : TableConfig.selectList?.map(item => item.id) || []
      if (!ids.length) {
        SLMessage.warning('请选择要删除的数据')
        return
      }
      await deleteBaseDatabaseSource(ids)
      SLMessage.success('删除成功')
      refreshData()
    })
    .catch(() => {})
}

const refreshData = async () => {
  try {
    const res = await getBaseDatabaseSourceList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  flex: 1;
  margin-top: 16px;
}
</style>