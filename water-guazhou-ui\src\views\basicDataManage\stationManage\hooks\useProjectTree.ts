import { useBusinessStore } from '@/store'

export const useProjectTree = () => {
  const businessStore = useBusinessStore()
  const TreeData = ref<SLTreeConfig>({
    data: businessStore.projectList,
    isFilterTree: true,
    title: '区域划分',
    currentProject: businessStore.selectedProject
  })
  const init = (options?: { nodeClick?: (data) => void }) => {
    TreeData.value.treeNodeHandleClick = options?.nodeClick
  }
  return {
    init,
    TreeData
  }
}
