package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.store.StoreInRecord;
import org.thingsboard.server.dao.sql.department.StoreInRecordMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordSaveRequest;

@Service
public class StoreInRecordServiceImpl implements StoreInRecordService {
    @Autowired
    private StoreInRecordMapper mapper;

    @Autowired
    private StoreInRecordDetailService service;

    @Override
    public IPage<StoreInRecord> findAllConditional(StoreInRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public StoreInRecord save(StoreInRecordSaveRequest entity) {
        if (entity.getId() != null)
            return null;
        StoreInRecord result = QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
        result.setCode(mapper.getCodeById(result.getId()));
        service.save(entity.getItems(result.getId()), result);
        // service.deleteAll(entity.getRemove());
        return result;
    }

    @Override
    public boolean update(StoreInRecord entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
