package org.thingsboard.server.dao.sql.waterSource;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.assay.Assay;

import java.util.Map;

@Mapper
public interface AssayMapper extends BaseMapper<Assay> {
    
    IPage<Assay> getList(Page<Assay> page, @Param("params") Map<String, Object> params);
} 