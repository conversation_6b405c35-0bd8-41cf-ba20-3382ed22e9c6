import{z as a}from"./workerHelper-wv3qZZD7.js";import{f as p,u as c,p as l,A as d,a as f,m as g}from"./edgeProcessing-OWtVBtJ5.js";import"./BufferView-BcX1hwIm.js";import"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./InterleavedLayout-EYSqXknm.js";import"./types-Cezv0Yl1.js";import"./deduplicate-Clsym5GM.js";import"./Indices-iFKW8TWb.js";import"./VertexAttribute-BAIQI41G.js";import"./glUtil-D4FNL8tc.js";import"./enums-BDQrMlcz.js";import"./VertexElementDescriptor-BOD-G50G.js";class j{async extract(t){const e=o(t),r=p(e),n=[e.data.buffer];return{result:m(r,n),transferList:n}}async extractComponentsEdgeLocations(t){const e=o(t),r=c(e.data,e.skipDeduplicate,e.indices,e.indicesLength),n=l(r,D,u),s=[];return{result:a(n.regular.instancesData,s),transferList:s}}async extractEdgeLocations(t){const e=o(t),r=c(e.data,e.skipDeduplicate,e.indices,e.indicesLength),n=l(r,w,u),s=[];return{result:a(n.regular.instancesData,s),transferList:s}}}function o(i){return{data:d.createView(i.dataBuffer),indices:i.indicesType==="Uint32Array"?new Uint32Array(i.indices):i.indicesType==="Uint16Array"?new Uint16Array(i.indices):i.indices,indicesLength:i.indicesLength,writerSettings:i.writerSettings,skipDeduplicate:i.skipDeduplicate}}function m(i,t){return t.push(i.regular.lodInfo.lengths.buffer),t.push(i.silhouette.lodInfo.lengths.buffer),{regular:{instancesData:a(i.regular.instancesData,t),lodInfo:{lengths:i.regular.lodInfo.lengths.buffer}},silhouette:{instancesData:a(i.silhouette.instancesData,t),lodInfo:{lengths:i.silhouette.lodInfo.lengths.buffer}},averageEdgeLength:i.averageEdgeLength}}class h{allocate(t){return f.createBuffer(t)}trim(t,e){return t.slice(0,e)}write(t,e,r){t.position0.setVec(e,r.position0),t.position1.setVec(e,r.position1)}}class L{allocate(t){return g.createBuffer(t)}trim(t,e){return t.slice(0,e)}write(t,e,r){t.position0.setVec(e,r.position0),t.position1.setVec(e,r.position1),t.componentIndex.set(e,r.componentIndex)}}const w=new h,D=new L,u={allocate:()=>null,write:()=>{},trim:()=>null};export{j as default};
