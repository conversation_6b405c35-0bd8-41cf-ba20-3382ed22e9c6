<template>
  <div
    class="layout-tree-detail-container"
    :class="{ dark: useAppStore().isDark }"
  >
    <div
      class="left-project-tree-list"
      :class="{ 'tree-hidden': state.treeState }"
    >
      <slot name="tree"></slot>
      <p
        class="control-fold-btn"
        @click="handleCollapse"
      >
        <CaretRight v-if="state.treeState"></CaretRight>
        <CaretLeft v-else></CaretLeft>
      </p>
    </div>
    <div
      class="right-detail-box"
      :class="{ 'fill-width': state.treeState }"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { CaretRight, CaretLeft } from '@element-plus/icons-vue'
import { useAppStore } from '@/store'

const emit = defineEmits(['collapse'])
const state = reactive<{
  treeState: boolean
}>({
  treeState: false
})
const handleCollapse = () => {
  state.treeState = !state.treeState
  emit('collapse', state.treeState)
}
</script>

<style lang="scss" scoped>
.layout-tree-detail-container {
  height: 100%;
  width: 100%;
  display: flex;
  &.dark {
    .left-project-tree-list {
      background-color: var(--el-bg-color);
    }
    .control-fold-btn {
      background: var(--el-bg-color);
    }
  }
  .left-project-tree-list {
    width: 300px;
    // width: 260px;
    height: 100%;
    // overflow-y: auto;
    border-radius: 5px;
    position: relative;
    // margin-right: 20px;
    box-shadow: 2px 0 4px -2px rgba(0, 0, 0, 0.25);
    transition: margin-left ease 0.5s;
    &:hover {
      box-shadow: 2px 0 4px -2px rgba(0, 0, 0, 0.25);
    }
  }
  .tree-hidden {
    margin-left: -300px;
  }
  .right-detail-box {
    width: calc(100% - 300px);
    height: 100%;
    transition: width ease 0.5s;
    padding: 15px 15px 15px 20px;
    overflow: hidden;
    &:hover {
      overflow-y: auto;
      overflow-y: overlay;
    }
  }
  .fill-width {
    width: 100%;
  }
  .control-fold-btn {
    position: absolute;
    right: -15px;
    top: 42%;
    z-index: 50;
    margin: 0 0;
    width: 14px;
    height: 100px;
    cursor: pointer;
    padding: 40px 0;
    vertical-align: middle;
    border-left: none;
    border-radius: 0 50px 50px 0;
    background-color: rgb(255, 255, 255);
    box-shadow: 2px 0 4px -2px rgba(0, 0, 0, 0.25);
    .fold-icon {
      font-size: 16px;
      margin-left: -1px;
    }
  }
}
</style>
