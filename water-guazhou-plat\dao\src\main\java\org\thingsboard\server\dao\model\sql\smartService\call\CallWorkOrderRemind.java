package org.thingsboard.server.dao.model.sql.smartService.call;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 号码归属地
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-01
 */
@TableName("tb_service_work_order_remind")
@Data
public class CallWorkOrderRemind {

    @TableId
    private String id;

    private String workOrderId;

    private String source;

    private transient String sourceName;

    private String phone;

    private String remark;

    private String creator;

    private transient String creatorName;

    private Date createTime;

    private String tenantId;

}
