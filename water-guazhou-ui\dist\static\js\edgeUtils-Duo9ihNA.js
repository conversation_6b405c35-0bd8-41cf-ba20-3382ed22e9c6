import{d0 as r,eA as a,l,cx as u}from"./MapView-DaoQedLH.js";import{R as i,T as f}from"./index-r0dFAfgr.js";var t;(function(e){e[e.INVISIBLE=0]="INVISIBLE",e[e.TRANSPARENT=1]="TRANSPARENT",e[e.OPAQUE=2]="OPAQUE"})(t||(t={}));function p(e){return e.type==="fill"}function h(e){return e.type==="extrude"}function A(e){return e&&e.enabled&&(h(e)||p(e))&&i(e.edges)}function d(e){return e&&e.enabled&&e.edges||null}function I(e,n){return y(d(e),n)}function y(e,n){if(f(e))return null;const o=i(e.color)?a(l.toUnitRGBA(e.color)):r(0,0,0,0),s=u(e.size),c=u(e.extensionLength);switch(e.type){case"solid":return g({color:o,size:s,extensionLength:c,...n});case"sketch":return x({color:o,size:s,extensionLength:c,...n});default:return}}function g(e){return{...E,...e,type:"solid"}}function x(e){return{...P,...e,type:"sketch"}}const E={color:r(0,0,0,.2),size:1,extensionLength:0,opacity:1,objectTransparency:t.OPAQUE,hasSlicePlane:!1},P={color:r(0,0,0,.2),size:1,extensionLength:0,opacity:1,objectTransparency:t.OPAQUE,hasSlicePlane:!1};export{t as A,A as a,I as f,g as m};
