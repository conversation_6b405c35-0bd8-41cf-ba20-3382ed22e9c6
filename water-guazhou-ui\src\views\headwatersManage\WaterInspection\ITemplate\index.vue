<!-- 巡检模板 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refForm"
      :config="FormConfig"
    >
    </DialogForm>
  </div>
</template>
<script lang="ts" setup>
import { Plus, Refresh, Search } from '@element-plus/icons-vue'
import { SLConfirm } from '@/utils/Message.js'
import { projectType } from '../data/data'
import {
  addCircuitTemplate,
  circuitConfigList,
  circuitTemplateList,
  delCircuitTemplate
} from '@/api/headwatersManage/waterInspection'
import useGlobal from '@/hooks/global/useGlobal'
import { getUserList } from '@/api/user'
import { removeSlash } from '@/utils/removeIdSlash'

const { $messageError, $messageWarning, $messageSuccess } = useGlobal()
const state = reactive<{
  settings: string
}>({
  settings: ''
})
const projectSelectList = ref<any>([])
const refForm = ref<IDialogFormIns>()
const refSearch = ref<ICardSearchIns>()
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: null
  },
  filters: [
    {
      type: 'department-user',
      label: '创建人员',
      field: 'creator'
    },
    { type: 'daterange', label: '创建时间', field: 'fromTime', format: 'YYYY-MM-DD' },
    { type: 'input', label: '模板名称', field: 'name' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', svgIcon: shallowRef(Search), click: () => refreshData() },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '添加',
          svgIcon: shallowRef(Plus),
          click: () => {
            FormConfig.title = '新增'
            state.settings = ''
            const table = FormConfig.group[0]?.fields?.find(field => field.field === 'project') as any
            table.config.handleSelectChange = (val: any) => handleSelectChange(val)
            handleAddEdit()
          }
        }
      ]
    }
  ]
})
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: [
    { label: '模板名称', prop: 'name', align: 'center' },
    { label: '创建人', prop: 'creatorName', align: 'center' },
    { label: '备注', prop: 'remark', align: 'center' },
    { label: '创建时间', prop: 'createTime', align: 'center' }
  ],
  operationWidth: '180px',
  operations: [
    {
      perm: true,
      text: '查看',
      click: row => {
        FormConfig.title = '查看'
        handleAddEdit(row, true)
      }
    },
    {
      perm: true,
      text: '修改',
      click: row => {
        FormConfig.title = '修改'
        handleAddEdit(row)
      }
    },
    { perm: true, text: '删除', type: 'danger', click: row => handleDelete(row) }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
const FormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 1000,
  title: '新增',
  group: [
    {
      fields: [
        {
          type: 'input', label: '模板名称', field: 'name', rules: [{ required: true, message: '请输入模板名称' }], placeholder: '请输入模板名称'
        },
        {
          type: 'select',
          label: '项目分类',
          field: 'itemTypes',
          returnType: 'str',
          multiple: true,
          clearable: false,
          options: projectType,
          onChange: (val: any) => selectProjectList(val, state.settings)
        },
        {
          type: 'table',
          field: 'project',
          config: {
            height: '300px',
            dataList: [],
            selectList: [],
            border: true,
            handleSelectChange: val => handleSelectChange(val),
            columns: [
              { label: '项目分类', prop: 'itemType' },
              { label: '项目名称', prop: 'name' },
              { label: '巡检方法', prop: 'method' },
              { label: '巡检要求', prop: 'require' }
            ],
            pagination: {
              hide: true
            }
          }
        },
        {
          type: 'textarea',
          label: '备注信息',
          field: 'remark',
          placeholder: '请输入备注信息',
          colStyles: {
            marginTop: '20px'
          }
        }
      ]
    }
  ]
  // submit: (params: any) => {
  //   SLConfirm('确定提交？', '提示信息').then(() => {
  //     console.log(projectSelectList.value)
  //     const newParams = {
  //       type: '水源',
  //       settings: projectSelectList.value.join(','),
  //       remark: params.remark,
  //       name: params.name
  //     }
  //     addCircuitTemplate(newParams).then(() => {
  //       refForm.value?.resetForm()
  //       refForm.value?.closeDialog()
  //       refreshData()
  //     })
  //   }).catch(() => {
  //     //
  //   })
  // }
})

const handleSelectChange = val => {
  projectSelectList.value = val.map(data => data.id)
  console.log(projectSelectList.value)
}
// 搜索列表
const selectProjectList = async (val: any, settings?: any, readOnly?: boolean) => {
  const itemType = val ? val.join(',') : null
  const table = FormConfig.group[0].fields.find(field => field.field === 'project') as IFormTable
  if (itemType) {
    const params = {
      page: 1,
      size: 999,
      itemType,
      type: '水源'
    }
    circuitConfigList(params).then(res => {
      const data = res.data.data?.data
      console.log('res', val)
      const selectData = data.filter(item => settings.indexOf(item.id) !== -1)
      // if (val) {
      //   table.config.dataList = data
      //   nextTick(() => {
      //     table.config.selectList = selectData
      //   })
      // } else {
      //   nextTick(() => {
      //     table.config.dataList = selectData
      //     table.config.selectList = selectData
      //   })
      // }
      if (readOnly) {
        nextTick(() => {
          table.config.dataList = selectData
          table.config.selectList = selectData
        })
      } else {
        table.config.dataList = data
        nextTick(() => {
          table.config.selectList = selectData
        })
      }
    })
  } else {
    table.config.dataList = []
  }
}
// 修改弹框
const handleAddEdit = (row?: any, readOnly?: boolean) => {
  const table = FormConfig.group[0]?.fields?.find(field => field.field === 'project') as any
  table.config.dataList = []
  table.config.selectList = []
  console.log(table.config.dataList)
  if (readOnly) {
    table.config.handleSelectChange = undefined
  } else {
    table.config.handleSelectChange = (val: any) => handleSelectChange(val)
  }
  FormConfig.group.map(res => {
    res.fields.map(field => {
      field.readonly = !!readOnly
    })
  })
  FormConfig.defaultValue = {
    ...(row) || {}
  }
  if (row) {
    state.settings = row.settings
    selectProjectList(row.itemTypes.split(','), row.settings, readOnly)
  }
  if (readOnly) {
    // const itemType = FormConfig.group[0]?.fields?.find(field => field.field === 'itemType') as IFormSelect
    // itemType.hide = true
    console.log(row.itemTypes)
    FormConfig.submit = undefined
  } else {
    FormConfig.submit = (params: any) => {
      const selectData = table.config?.dataList?.filter(data => projectSelectList.value.includes(data.id))
      const settingIds = selectData.map(data => {
        return data.id
      })
      const settings = settingIds?.join(',')
      if (settings && settings.length > 0) {
        const newParams = {
          type: '水源',
          settings,
          remark: params.remark,
          name: params.name,
          id: row ? row.id : null
        }
        SLConfirm('确定提交？', '提示信息')
          .then(() => {
            addCircuitTemplate(newParams)
              .then(() => {
                refForm.value?.resetForm()
                refForm.value?.closeDialog()
                refreshData()
                $messageSuccess('提交成功')
              })
              .catch(error => {
                $messageError(error)
              })
          })
          .catch(() => {
            //
          })
      } else {
        $messageWarning('请选择至少一个项目')
      }
    }
  }
  refForm.value?.openDialog()
}

// 获取部门用户
const userList = async (val: string) => {
  const res = await getUserList({
    pid: val,
    status: 1,
    page: 1,
    size: 999
  })
  const users = res.data?.data?.data
  const userOptions: NormalOption[] = users?.map(user => {
    return {
      id: removeSlash(user.id.id),
      label: user.firstName,
      value: removeSlash(user.id.id),
      isLeaf: true
    }
  })
  return userOptions
}
const handleDelete = (row?: any) => {
  SLConfirm('确定删除?', '提示信息').then(() => {
    delCircuitTemplate(row.id).then(() => {
      refreshData()
    })
  }).catch(() => {
    //
  })
}
const refreshData = async () => {
  const query = refSearch.value?.queryParams || {}
  const [start, end] = query.fromTime || []
  const params = {
    ...query,
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    fromTime: start || null,
    toTime: end || null,
    type: '水源'
  }
  const result = await circuitTemplateList(params)
  console.log(result.data?.data.total)
  TableConfig.pagination.total = result.data?.data.total
  TableConfig.dataList = result.data?.data.data
}

onMounted(async () => {
  refreshData()
})
</script>
<style lang="scss" scoped></style>
