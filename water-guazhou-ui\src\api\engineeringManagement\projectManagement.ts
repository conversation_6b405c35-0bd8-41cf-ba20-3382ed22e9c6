// 工程管理-项目管理
import request from '@/plugins/axios';

/**
 * 获取项目列表
 * @param params
 * @returns
 */
export function getProjectList(params: {
  size: number;
  page: number;
  fromTime?: any;
  toTime?: string;
  code?: string;
  name?: string;
  typeId?: string;
  startTimeFrom?: string;
  startTimeTo?: string;
}) {
  return request({
    url: `/api/so/project`,
    method: 'get',
    params
  });
}

/**
 * 导出项目立项
 * @returns
 */
export function getprojectExport() {
  return request({
    url: `/api/so/project/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 导出招投标流程
 * @returns
 */
export function getprojectBiddingExport() {
  return request({
    url: `/api/so/bidding/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 删除项目立项
 * @returns
 */
export function deleteprojectExport(id: string) {
  return request({
    url: `/api/so/project/${id}`,
    method: 'delete'
  });
}

/**
 * 删除项目下的设备
 * @returns
 */
export function deleteDeviceItemExport(id: string) {
  return request({
    url: `/api/so/deviceItem/${id}`,
    method: 'delete'
  });
}

/**
 * 添加项目
 * @param params
 * @returns
 */
export function postProject(params: {
  id?: string;
  code: string;
  name: string;
  typeId?: string;
  scale?: string;
  organization: string;
  principal: string;
  phone?: string;
  address?: string;
  remark?: string;
  estimate?: number;
  startTime?: string;
  expectEndTime?: string;
  attachments?: string;
}) {
  return request({
    url: `/api/so/project`,
    method: 'post',
    data: params
  });
}

/**
 * 获取项目招标信息
 * @param params
 * @returns
 */
export function getBidding(params: {
  size: number;
  page: number;
  projectCode?: any;
  projectName?: string;
  projectTypeId?: string;
  startTimeFrom?: string;
  startTimeTo?: string;
}) {
  return request({
    url: `/api/so/bidding`,
    method: 'get',
    params
  });
}

/**
 * 获取项目招标信息
 * @param params
 * @returns
 */
export function getBiddingCompany(params: {
  size: number;
  page: number;
  projectCode?: string;
}) {
  return request({
    url: `/api/so/biddingCompany`,
    method: 'get',
    params
  });
}

/**
 * 更新项目招标信息
 * @param params
 * @returns
 */
export function postBidding(params: {
  id?: string;
  projectCode: string;
  proxyBiddingCompany?: string;
  preferCompanyId?: string;
  attachments?: string;
  items?: {
    name: string;
    biddingId: string;
    contactUser: string;
    phone: string;
  }[];
}) {
  return request({
    url: `/api/so/bidding`,
    method: 'post',
    data: params
  });
}

/**
 * 获取大事记
 * @param params
 * @returns
 */
export function getProjectOperateRecord(params: {
  size: number;
  page: number;
  fromTime?: string;
  toTime?: string;
  code?: string;
  name?: string;
}) {
  return request({
    url: `/api/so/projectOperateRecord`,
    method: 'get',
    params
  });
}

/**
 * 项目下所有工程完成信息获取
 * @param params
 * @returns
 */
export function getConstructioncompletionInfo(projectCode: string) {
  return request({
    url: `/api/so/construction/completionInfo/${projectCode}`,
    method: 'get'
  });
}

/**
 * 项目完成信息获取
 * @param params
 * @returns
 */
export function getProjectcompletionInfo(projectCode: string) {
  return request({
    url: `/api/so/project/completionInfo/${projectCode}`,
    method: 'get'
  });
}

/**
 * 通过编号查询工程和其关联项目
 * @param params
 * @returns
 */
export function getConstruction(constructionCode: string) {
  return request({
    url: `/api/so/construction/${constructionCode}`,
    method: 'get'
  });
}
