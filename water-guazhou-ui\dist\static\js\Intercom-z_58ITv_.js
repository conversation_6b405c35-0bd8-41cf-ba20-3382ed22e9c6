import{d as E,c as a,a8 as p,g as n,n as u,aB as f,aJ as I,h as w,F as m,q as v,i as r,cs as F,t as J,J as q,ct as z,c3 as D,C as L}from"./index-r0dFAfgr.js";/* empty css                  */const N={key:0,class:"interval"},R=E({__name:"Intercom",emits:["talkStart","talkStop","putVolume"],setup(T,{expose:k,emit:b}){const i=b,t=a(!1),s=a(!0),d=a(0),c=a(100),_=a(!1),x=a([{title:p(()=>t.value?"结束对讲":"点击对讲"),onclick:()=>{t.value?i("talkStop",d.value):i("talkStart",d.value),t.value=!t.value},icon:"ant-design:sound-filled",disabled:p(()=>s.value),color:p(()=>s.value?"#eeeeee":t.value?"#4caf50":"#409eff")},{type:"interval",icon:""}]),V=(l,o)=>{l.talkurl?s.value=!1:s.value=!0,t.value=!1,d.value=o},y=(l,o)=>{_.value=l,c.value=o},C=l=>{i("putVolume",l)};return k({refresh:V,setVolume:y}),(l,o)=>{const g=q,h=z,B=D;return n(),u(f,null,[(n(!0),u(f,null,I(r(x),(e,S)=>(n(),u(f,{key:S},[e.type==="interval"?(n(),u("div",N)):(n(),w(h,{key:1,class:"box-item",effect:"dark",content:e.title,placement:"top"},{default:m(()=>[v(g,{text:"",disabled:e.disabled,onClick:U=>e.onclick&&e.onclick(e)},{default:m(()=>[v(r(F),{icon:e.icon,color:e.color,style:{"font-size":"20px"}},null,8,["icon","color"])]),_:2},1032,["disabled","onClick"])]),_:2},1032,["content"]))],64))),128)),v(B,{disabled:!r(_),style:{width:"100px","margin-left":"15px"},modelValue:r(c),"onUpdate:modelValue":o[0]||(o[0]=e=>J(c)?c.value=e:null),step:1,onChange:C},null,8,["disabled","modelValue"])],64)}}}),A=L(R,[["__scopeId","data-v-188fc6aa"]]);export{A as default};
