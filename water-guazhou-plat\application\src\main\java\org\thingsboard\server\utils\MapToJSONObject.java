package org.thingsboard.server.utils;

import com.alibaba.fastjson.JSONObject;

import java.util.Iterator;
import java.util.Map;

/**
 * map转jsonobject
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-03
 */
public class MapToJSONObject {
    public static JSONObject toJsonObj(Map<String, Object> map) {
        JSONObject resultJson = new JSONObject();
        Iterator it = map.keySet().iterator();
        while (it.hasNext()) {
            String key = (String) it.next();
            resultJson.put(key, map.get(key));
        }
        return resultJson;
    }
}
