package org.thingsboard.server.dao.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardClass;
import org.thingsboard.server.dao.sql.smartProduction.guard.GuardClassMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardClassPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardClassSaveRequest;

@Service
public class GuardClassServiceImpl implements GuardClassService {
    @Autowired
    private GuardClassMapper mapper;

    @Override
    public GuardClass findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<GuardClass> findAllConditional(GuardClassPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public GuardClass save(GuardClassSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
