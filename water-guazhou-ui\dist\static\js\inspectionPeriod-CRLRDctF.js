import{m as e}from"./index-r0dFAfgr.js";function r(t){return e({url:"/api/so/projectAccept",method:"get",params:t})}function c(t){return e({url:"/api/so/projectAccept",method:"post",data:t})}function p(){return e({url:"/api/so/projectAccept/export/excel",method:"get",responseType:"blob"})}function n(t){return e({url:"/api/so/projectSettlement",method:"get",params:t})}function s(t){return e({url:"/api/so/projectSettlement",method:"post",data:t})}function i(){return e({url:"/api/so/projectSettlement/export/excel",method:"get",responseType:"blob"})}function a(t){return e({url:"/api/so/projectArchive",method:"get",params:t})}function u(t){return e({url:"/api/so/projectArchive",method:"post",data:t})}function l(){return e({url:"/api/so/projectArchive/export/excel",method:"get",responseType:"blob"})}export{r as a,l as b,u as c,a as d,i as e,s as f,p as g,n as h,c as p};
