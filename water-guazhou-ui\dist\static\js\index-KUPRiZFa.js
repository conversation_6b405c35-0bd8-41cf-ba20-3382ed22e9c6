import{_ as L}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as q}from"./CardTable-rdWOL4_6.js";import{_ as O}from"./CardSearch-CB_HNR-Q.js";import{d as V,M as E,c as w,r as g,a8 as i,s as C,bF as b,bT as H,D as P,S as B,o as A,g as $,n as j,q as D,i as y,al as W,ak as N}from"./index-r0dFAfgr.js";import{u as R}from"./useStation-DJgnSZIA.js";import{f as Y}from"./DateFormatter-Bm9a68Ax.js";import{s as z,g as G,d as J}from"./stationCircuit-CGrX5qR4.js";import{u as K}from"./useDepartment-BkP08hh6.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const Q={class:"wrapper"},ce=V({__name:"index",setup(X){const{getAllStationOption:F}=R(),{getDepartmentTree:k}=K(),{$messageSuccess:T,$messageError:m,$messageWarning:Z}=E(),l=w(),_=w(),o=g({optionUsers:[],auditUsers:[],searchOptionUsers:[],searchAuditUsers:[],stationOptionList:[],departmentTree:[]}),h=g({filters:[{label:"关键字",field:"keyword",type:"input",placeholder:"请输入关键字"},{label:"维保部门",field:"optionDep",type:"select-tree",options:i(()=>o.departmentTree),placeholder:"请选择维保部门",onChange:async e=>{o.searchOptionUsers=await c(e)}},{label:"维保人员",field:"optionUserId",type:"select",placeholder:"请选择维保人员",options:i(()=>o.searchOptionUsers)},{label:"审核部门",field:"auditDep",type:"select-tree",options:i(()=>o.departmentTree),placeholder:"请选审核部门",onChange:async e=>{o.searchAuditUsers=await c(e)}},{label:"审核人员",field:"auditUserId",type:"select",placeholder:"请选择审核人",options:i(()=>o.searchAuditUsers)},{label:"开始时间",field:"beginStartTime",type:"daterange",format:"YYYY-MM-DD",placeholder:"请选择开始时间",onChange:e=>{var a;const t=(a=h.filters)==null?void 0:a.find(s=>s.field==="endStartTime");t.disabledDate=function(s){const n=e[1];return s<new Date(n)}}},{label:"结束时间",field:"endStartTime",type:"daterange",format:"YYYY-MM-DD",placeholder:"请选择结束时间",onChange:e=>{var a;const t=(a=h.filters)==null?void 0:a.find(s=>s.field==="beginStartTime");t.disabledDate=function(s){const n=e[0];return s>new Date(n)}}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:C(W),click:()=>f()},{text:"新增",perm:!0,svgIcon:C(N),type:"success",click:()=>{var e;d.defaultValue={},d.title="新增",(e=l.value)==null||e.openDialog()}}]}]}),r=g({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"name",label:"计划名称"},{prop:"type",label:"业务类型"},{prop:"days",label:"消耗天数"},{prop:"startTime",label:"计划开始时间",formatter:(e,t)=>Y(t,"YYYY-MM-DD HH:mm:ss")},{prop:"endTime",label:"计划结束时间",formatter:(e,t)=>Y(t,"YYYY-MM-DD HH:mm:ss")},{prop:"createTime",label:"添加时间",formatter:(e,t)=>Y(t,"YYYY-MM-DD HH:mm:ss")}],operations:[{text:"编辑",isTextBtn:!0,perm:!0,icon:"iconfont icon-bianji",click:async e=>{var t,a;d.title="编辑",d.defaultValue={...e,startTime:b(e.startTime).format("YYYY-MM-DD"),endTime:b(e.endTime).format("YYYY-MM-DD"),stationIds:((t=e.stationIds)==null?void 0:t.split(","))||[]},o.optionUsers=await c(e.optionDep),o.auditUsers=await c(e.auditDep),(a=l.value)==null||a.openDialog()}},{perm:!0,text:"删除",isTextBtn:!0,type:"danger",icon:"iconfont icon-shanchu",click:e=>I(e)}],operationWidth:"200px",pagination:{refreshData:({page:e,size:t})=>{r.pagination.limit=t,r.pagination.page=e,f()}}}),d=g({title:"新增",defaultValue:{},dialogWidth:900,labelPosition:"right",group:[{fields:[{xs:12,type:"input",label:"计划名称",field:"name",rules:[{required:!0,message:"请填写计划名称"}]},{xs:12,label:"业务类型",field:"type",type:"select",placeholder:"请选择业务类型",options:[{label:"维修业务",value:"维修业务"},{label:"保养业务",value:"保养业务"},{label:"清洗业务",value:"清洗业务"}]},{xs:12,type:"number",label:"消耗天数",field:"days",rules:[{required:!0,message:"请填写各泵房消耗天数"}],onChange:e=>{var a,s,n,p;const t=(s=(a=l.value)==null?void 0:a.refForm)==null?void 0:s.dataForm;t.startTime&&(d.defaultValue={...t,endTime:b(t.startTime).add(e,"day").format("YYYY-MM-DD")},(p=(n=l.value)==null?void 0:n.refForm)==null||p.resetForm())}},{xs:12,type:"date",label:"开始时间",field:"startTime",format:"YYYY-MM-DD",rules:[{required:!0,message:"请选择开始时间"}],disabledDate:e=>new Date>e,onChange:e=>{var a,s,n,p;const t=(s=(a=l.value)==null?void 0:a.refForm)==null?void 0:s.dataForm;t.days>0&&(d.defaultValue={...t,endTime:b(e).add(t.days,"day").format("YYYY-MM-DD")},(p=(n=l.value)==null?void 0:n.refForm)==null||p.resetForm())}},{xs:12,type:"date",label:"结束时间",field:"endTime",format:"YYYY-MM-DD",readonly:!0},{xs:12,type:"select-tree",label:"维保部门",field:"optionDep",options:i(()=>o.departmentTree),onChange:async e=>{o.optionUsers=await c(e)}},{xs:12,type:"select",label:"维保人员",field:"optionUserId",options:i(()=>o.optionUsers)},{xs:12,type:"select-tree",label:"审核部门",field:"auditDep",options:i(()=>o.departmentTree),onChange:async e=>{o.auditUsers=await c(e)}},{xs:12,type:"select",label:"审核人员",field:"auditUserId",options:i(()=>o.auditUsers)},{type:"checkbox",field:"stationIds",label:"泵站列表",rules:[{required:!0,message:"请选择泵站列表"}],noBorder:!1,options:i(()=>o.stationOptionList)},{xs:24,type:"textarea",minRow:5,label:"备注",field:"remark",colStyles:{marginTop:"20px"}}]}],submit:e=>{z({...e,stationType:"泵站",stationIds:e.stationIds.join(",")}).then(t=>{var a,s;((a=t.data)==null?void 0:a.code)===200?T("保存成功"):m("保存失败"),f(),(s=l.value)==null||s.closeDialog()}).catch(t=>{m(t)})}}),c=async e=>((await H({pid:e})).data.data.data||[]).map(n=>({label:n.firstName,value:P(n.id.id)})),f=async()=>{var x;r.loading=!0;const e=((x=_.value)==null?void 0:x.queryParams)||{},[t,a]=e.beginStartTime||[],[s,n]=e.endStartTime||[],p={...e,beginStartTime:t,beginEndTime:a,endStartTime:s,endEndTime:n,size:r.pagination.limit||20,page:r.pagination.page||1};G(p).then(u=>{var M,S,U,v;r.dataList=((S=(M=u.data)==null?void 0:M.data)==null?void 0:S.data)||[],r.pagination.total=((v=(U=u.data)==null?void 0:U.data)==null?void 0:v.total)||0,r.loading=!1}).catch(u=>{m(u),r.loading=!1})},I=e=>{B("确定删除指定养护计划?","删除提示").then(()=>{J([e.id]).then(t=>{var a;((a=t.data)==null?void 0:a.code)===200?(T("删除成功"),f()):m("删除失败")}).catch(t=>{m(t)})})};return A(async()=>{o.stationOptionList=await F("泵站"),o.departmentTree=await k(2),f()}),(e,t)=>{const a=O,s=q,n=L;return $(),j("div",Q,[D(a,{ref_key:"refSearch",ref:_,config:y(h)},null,8,["config"]),D(s,{config:y(r),class:"card-table"},null,8,["config"]),D(n,{ref_key:"refForm",ref:l,config:y(d)},null,8,["config"])])}}});export{ce as default};
