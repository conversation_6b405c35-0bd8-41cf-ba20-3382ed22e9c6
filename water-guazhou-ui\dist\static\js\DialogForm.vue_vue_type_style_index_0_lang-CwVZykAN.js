import{d as T,c as $,r as E,am as N,g as l,n as b,q as y,bg as O,F as i,p as v,aB as P,aJ as J,h as f,an as d,G as u,bh as L,ax as k,_ as q,bi as G,aH as H,J as M,L as U}from"./index-r0dFAfgr.js";const W={class:"system-add-menu-container"},j={class:"dialog-form-container"},A={class:"dialog-footer"},K=T({__name:"DialogForm",props:{config:{}},emits:["close"],setup(C,{expose:w,emit:B}){const c=C,F=B,n=$(),a=E({visible:!1}),S=()=>{a.visible=!0},m=()=>{a.visible=!1},V=()=>{var e;(e=n.value)==null||e.resetForm()},p=e=>{var o,r;n.value===void 0&&c.config.submit?(o=c.config)==null||o.submit(!1):(r=n.value)==null||r.Submit(e)},z=()=>{G("print","",!1,!0,e=>{console.log(e)})};return N(()=>a.visible,e=>{var o;e&&c.config.desTroyOnClose&&((o=n.value)==null||o.resetForm())}),w({refForm:n,Submit:p,closeDialog:m,openDialog:S,resetForm:V}),(e,o)=>{const r=H,g=M,h=U;return l(),b("div",W,[y(h,{id:"print",modelValue:a.visible,"onUpdate:modelValue":o[1]||(o[1]=s=>a.visible=s),title:e.config.title,width:e.config.dialogWidth||"60%",fullscreen:e.config.fullscreen,draggable:e.config.draggable,top:e.config.top,modal:e.config.model,"close-on-click-modal":e.config.closeOnClickModal===!0,"close-on-press-escape":e.config.closeOnPressEscape===!0,"show-close":e.config.showClose,"destroy-on-close":e.config.desTroyOnClose!==!1,"append-to-body":e.config.appendToBody||!1,onClose:o[2]||(o[2]=s=>F("close"))},O({default:i(()=>[k(e.$slots,"default",{},()=>[v("div",j,[y(q,{ref_key:"refForm",ref:n,config:e.config},{fieldSlot:i(s=>[k(e.$slots,"fieldSlot",{config:s.config,row:s.row})]),_:3},8,["config"])])])]),_:2},[e.config.cancel!==!1||e.config.submit?{name:"footer",fn:i(()=>{var s;return[v("span",A,[e.config.btns?(l(!0),b(P,{key:0},J(e.config.btns,(t,D)=>(l(),f(r,{key:D,config:t,size:(t==null?void 0:t.size)||"default",loading:e.config.submitting},null,8,["config","size","loading"]))),128)):d("",!0),(s=e.config)!=null&&s.print?(l(),f(g,{key:1,type:"primary",size:"default",onClick:z},{default:i(()=>o[3]||(o[3]=[u(" 打 印")])),_:1})):d("",!0),e.config.submit?(l(),f(g,{key:2,type:"primary",size:"default",loading:e.config.submitting,onClick:o[0]||(o[0]=t=>p(!1))},{default:i(()=>[u(L(e.config.submitText||"确 定"),1)]),_:1},8,["loading"])):d("",!0),e.config.cancel!==!1?(l(),f(g,{key:3,size:"default",onClick:m},{default:i(()=>o[4]||(o[4]=[u("关 闭")])),_:1})):d("",!0)])]}),key:"0"}:void 0]),1032,["modelValue","title","width","fullscreen","draggable","top","modal","close-on-click-modal","close-on-press-escape","show-close","destroy-on-close","append-to-body"])])}}});export{K as _};
