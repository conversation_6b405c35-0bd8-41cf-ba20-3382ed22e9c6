import * as echarts from 'echarts'

// 供水管网接口
export function guanwang() {
  const data: number[] = []
  for (let i = 0; i < 12; i++) {
    data.push(parseInt(`${Math.random() * 4000}`))
  }
  return {
    color: ['#2a70da'],
    tooltip: {},
    grid: {
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: [
          'DN10',
          'DN20',
          'DN30',
          'DN40',
          'DN50',
          'DN60',
          'DN300',
          'DN400',
          'DN500',
          'DN600',
          'DN800',
          'DN120'
        ],
        axisTick: {
          alignWithLabel: true
        },
        nameTextStyle: {
          color: '#82b0ec'
        },
        axisLine: {
          lineStyle: {
            color: '#82b0ec'
          }
        },
        axisLabel: {
          textStyle: {
            fontSize: 30,
            color: '#82b0ec'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          textStyle: {
            fontSize: 30,
            color: '#82b0ec'
          },
          formatter: '{value}%'
        },
        splitLine: {
          lineStyle: {
            color: '#0c2c5a'
          }
        },
        axisLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [40, 10],
        symbolOffset: [0, -5],
        symbolPosition: 'end',
        z: 12,
        label: {
          normal: {
            show: true,
            position: 'top',
            formatter: '{c}%'
          }
        },
        data
      },
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [40, 10],
        symbolOffset: [0, 5],
        z: 12,
        data
      },
      {
        type: 'bar',
        itemStyle: {
          normal: {
            opacity: 0.7
          }
        },
        barWidth: '40',
        data
      }
    ]
  }
}

// 柱状图
export function columnar(
  xValue: string[],
  legend: string[],
  values: any[],
  setting?: any
) {
  return {
    grid: {
      top: '20%',
      bottom: '10%' // 也可设置left和right设置距离来控制图表的大小
    },
    tooltip: {
      fontSize: 30,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        label: {
          show: true
        }
      }
    },
    legend: {
      data: legend,
      textStyle: {
        color: '#fff',
        fontSize: 30
      }
    },
    xAxis: {
      data: xValue,
      axisLine: {
        show: true, // 隐藏X轴轴线

        lineStyle: {
          color: '#01FCE3'
        }
      },
      axisTick: {
        show: true // 隐藏X轴刻度
      },
      axisLabel: {
        show: true,
        fontSize: 30,
        textStyle: {
          color: '#ffffff' // X轴文字颜色
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: setting.yname,
        nameTextStyle: {
          color: '#fff'
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: true
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#FFFFFF'
          }
        },
        axisLabel: {
          show: true,
          fontSize: 30,
          textStyle: {
            color: '#ffffff'
          }
        }
      },
      {
        type: 'value',
        name: setting.yname1,
        nameTextStyle: {
          color: '#ffffff'
        },
        position: 'right',
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true,
          formatter: '{value} %', // 右侧Y轴文字显示
          textStyle: {
            color: '#ffffff'
          }
        }
      },
      {
        type: 'value',
        gridIndex: 0,
        min: 50,
        max: 100,
        splitNumber: 8,
        splitLine: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(250,250,250,0.0)', 'rgba(250,250,250,0.05)']
          }
        }
      }
    ],
    series: [
      {
        name: legend[0],
        type: 'line',
        yAxisIndex: 1, // 使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
        smooth: true, // 平滑曲线显示
        showAllSymbol: true, // 显示所有图形。
        symbol: 'circle', // 标记的图形为实心圆
        symbolSize: 10, // 标记的大小
        itemStyle: {
          // 折线拐点标志的样式
          color: '#058cff'
        },
        lineStyle: {
          color: '#058cff'
        },
        areaStyle: {
          color: 'rgba(5,140,255, 0.2)'
        },
        data: values[0]
      },

      {
        name: legend[1],
        type: 'bar',
        barWidth: 15,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#00FFE3'
              },
              {
                offset: 1,
                color: '#4693EC'
              }
            ])
          }
        },
        data: values[1]
      },
      {
        name: legend[2],
        type: 'bar',
        barWidth: 15,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#00FFE3'
              },
              {
                offset: 1,
                color: '#4693EC'
              }
            ])
          }
        },
        data: values[2]
      }
    ]
  }
}

// 漏损排行
export function lousunpaihang(params: any) {
  console.log(params)

  const data = [
    '公园世家',
    '御台公馆',
    '翰墨苑',
    '东城国际',
    '奥星凤凰城',
    '翰林公馆',
    '城市丽景',
    '和合国际'
  ]
  const myColor = [
    '#eb2100',
    '#eb3600',
    '#d0570e',
    '#d0a00e',
    '#34da62',
    '#00e9db',
    '#00c0e9',
    '#0096f3'
  ]
  return {
    xAxis: [
      {
        show: false
      }
    ],
    grid: {
      left: '5%',
      right: '0',
      bottom: '1%',
      containLabel: true
    },
    yAxis: [
      {
        axisTick: 'none',
        axisLine: 'none',
        offset: '27',
        axisLabel: {
          textStyle: {
            color: '#ffffff',
            fontSize: '16'
          }
        },
        data
      },
      {
        axisTick: 'none',
        axisLine: 'none',
        axisLabel: {
          show: false,
          textStyle: {
            color: '#ffffff',
            fontSize: '16'
          }
        },
        data: [
          '12567',
          '12566',
          '12566',
          '12566',
          '12566',
          '12566',
          '12566',
          '12566'
        ]
      },
      {
        nameGap: '50',
        nameTextStyle: {
          color: '#ffffff',
          fontSize: '16'
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(0,0,0,0)'
          }
        },
        data: []
      }
    ],
    series: [
      {
        name: '条',
        type: 'bar',
        yAxisIndex: 0,
        data: [52, 60, 22, 33, 44, 78, 59, 15],
        label: {
          normal: {
            show: true,
            position: 'right',
            formatter(param) {
              return param.value
            },
            textStyle: {
              color: '#ffffff',
              fontSize: '16'
            }
          }
        },
        barWidth: 20,
        itemStyle: {
          show: false,
          normal: {
            color(params) {
              const num = myColor.length
              return myColor[params.dataIndex % num]
            }
          }
        },
        z: 2
      },
      {
        name: '白框',
        type: 'bar',
        yAxisIndex: 1,
        barGap: '-100%',
        data: [99, 99.5, 99.5, 99.5, 99.5, 99.5, 99.5, 99.5],
        barWidth: 20,
        itemStyle: {
          normal: {
            color: '#0e2147',
            barBorderRadius: 5
          }
        },
        z: 1
      },
      {
        name: '外框',
        type: 'bar',
        yAxisIndex: 2,
        barGap: '-100%',
        data: [100, 100, 100, 100, 100, 100, 100, 100],
        barWidth: 24,
        itemStyle: {
          normal: {
            color(params) {
              const num = myColor.length
              return myColor[params.dataIndex % num]
            },
            barBorderRadius: 5
          }
        },
        z: 0
      },
      {
        name: '外圆',
        type: 'scatter',
        hoverAnimation: false,
        data: [0, 0, 0, 0, 0, 0, 0, 0],
        yAxisIndex: 2,
        symbolSize: 35,
        itemStyle: {
          normal: {
            color(params) {
              const num = myColor.length
              return myColor[params.dataIndex % num]
            },
            opacity: 1
          }
        },
        z: 2
      }
    ]
  }
}

// 仪表盘
export function yibiaopan(name: string) {
  const demoData = [
    {
      name,
      unit: '%',
      value: 100
    }
  ]
  return {
    series: (function () {
      const result: any[] = []

      demoData.forEach(item => {
        result.push(
          {
            name: item.name,
            type: 'gauge',
            radius: '73.10%',
            startAngle: 225,
            endAngle: -45,
            min: 0,
            max: 100,
            axisLine: {
              show: true,
              lineStyle: {
                width: 30,
                shadowBlur: 0,
                color: [
                  [0, 'transparent'],
                  [0.1, '#194383'],
                  [0.108, 'transparent'],
                  [0.2, '#194383'],
                  [0.208, 'transparent'],
                  [0.3, '#194383'],
                  [0.308, 'transparent'],
                  [0.4, '#194383'],
                  [0.408, 'transparent'],
                  [0.5, '#194383'],
                  [0.508, 'transparent'],
                  [0.6, '#194383'],
                  [0.608, 'transparent'],
                  [0.7, '#194383'],
                  [0.708, 'transparent'],
                  [0.8, '#194383'],
                  [0.808, 'transparent'],
                  [0.9, '#194383'],
                  [0.908, 'transparent'],
                  [1, '#194383']
                ]
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            pointer: {
              show: false
            },
            detail: {
              show: false
            },
            data: [
              {
                show: false
              }
            ]
          },
          {
            name: item.name,
            type: 'gauge',
            radius: '73.10%',
            startAngle: 225,
            endAngle: -45,
            min: 0,
            max: 100,
            axisLine: {
              show: true,
              lineStyle: {
                width: 150,
                color: [
                  [
                    item.value / 100,
                    new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                      {
                        offset: 0,
                        color: 'rgba(69, 161, 255,0)'
                      },
                      {
                        offset: 0.3,
                        color: 'rgba(69, 161, 255,0)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(69, 161, 255,0.7)'
                      }
                    ])
                  ],
                  [1, 'rgba(28,128,245,.0)']
                ]
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: 0
            },
            axisLabel: {
              show: 0
            },
            pointer: {
              show: false,
              length: '100%'
            },
            detail: {
              show: true,
              offsetCenter: [1, '60%'],
              textStyle: {
                fontSize: 20,
                color: '#fff'
              },
              formatter: [item.name || ''].join('\n')
            },
            itemStyle: {
              color: 'rgba(28, 128, 245,.3)',
              borderColor: 'rgba(28, 128, 245,1)'
            },
            data: [
              {
                value: item.value
              }
            ]
          },
          {
            type: 'gauge',
            radius: '90%',
            splitNumber: 10,
            splitLine: {
              show: false
            },
            min: 0,
            max: 100,
            startAngle: 225,
            endAngle: -45,
            axisLabel: {
              show: false
            },
            axisLine: {
              show: false
            },

            pointer: {
              show: 0
            },
            axisTick: {
              show: false
            },
            detail: {
              show: true,
              offsetCenter: [0, '50%'],
              formatter: '未来15日\n场地预约率',
              textStyle: {
                fontSize: 10,
                color: '#ffffff',
                lineHeight: 50,
                fontWeight: '100'
              }
            }
          },
          {
            type: 'gauge',
            radius: '73%',
            splitNumber: 10,
            splitLine: {
              show: false
            },
            min: 0,
            max: 100,
            startAngle: 225,
            endAngle: -45,
            axisLabel: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: 2,
                shadowBlur: 0,
                color: [
                  [0.05, '#3F6BFC'],
                  [0.1, '#4072FB'],
                  [0.15, '#4077F9'],
                  [0.2, '#417BF9'],
                  [0.25, '#4282F8'],
                  [0.3, '#4385F7'],
                  [0.35, '#4389F6'],
                  [0.4, '#448FF5'],
                  [0.45, '#4594F4'],
                  [0.5, '#4599F3'],
                  [0.55, '#469EF2'],
                  [0.6, '#46A3F1'],
                  [0.65, '#46A6F0'],
                  [0.7, '#24befe'],
                  [0.75, '#12d1ff'],
                  [0.8, '#06defe'],
                  [0.85, '#05e0ff'],
                  [0.9, '#06deff'],
                  [0.95, '#06deff'],
                  [1, '#06deff']
                ]
              }
            },
            pointer: {
              show: 0
            },
            axisTick: {
              show: false
            },
            detail: {
              show: false
            }
          },
          {
            type: 'gauge',
            radius: '82%',
            splitNumber: 10,
            splitLine: {
              show: false
            },
            min: 0,
            max: 100,
            startAngle: 225,
            endAngle: -45,
            axisLabel: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: 2,
                shadowBlur: 0,
                color: [
                  [0.05, '#0A498E'],
                  [0.1, '#0C58A7'],
                  [0.15, '#254DD3'],
                  [0.2, '#355FEC'],
                  [0.25, '#4072FB'],
                  [0.3, '#355FEC'],
                  [0.35, '#254DD3'],
                  [0.4, '#06478B'],
                  [0.45, '#0A246C'],
                  [0.5, '#071848'],
                  [0.55, '#071848'],
                  [0.6, '#0A246C'],
                  [0.65, '#06478B'],
                  [0.7, '#254DD3'],
                  [0.75, '#355FEC'],
                  [0.8, '#355FEC'],
                  [0.85, '#254DD3'],
                  [0.9, '#0C58A7'],
                  [0.95, '#0A498E'],
                  [1, '#0A498E']
                ]
              }
            },
            pointer: {
              show: 0
            },
            axisTick: {
              show: false
            },
            detail: {
              show: false
            }
          }
        )
      })

      return result
    }())
  }
}

// 树图
export function shutu(params: string) {
  console.log(params)

  const data = [
    {
      name: '北碚区自来水公司',
      children: [
        {
          name: '西区2',
          children: [
            {
              name: '西区-1 9'
            },
            {
              name: '西区-2 7'
            }
          ]
        },
        {
          name: '东区 7',
          children: []
        },
        {
          name: '西南区 25',
          children: []
        },
        {
          name: '南西区 2',
          children: [{ name: '南西区-1 26' }, { name: '南西区-2 5' }]
        }
      ]
    }
  ]

  return {
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove'
    },
    series: [
      {
        type: 'tree',
        data,
        top: '1%',
        left: '14%',
        bottom: '1%',
        right: '20%',
        symbolSize: 14,
        label: {
          position: 'left',
          verticalAlign: 'middle',
          align: 'right',
          fontSize: 24,
          color: '#fff'
        },
        leaves: {
          label: {
            position: 'right',
            verticalAlign: 'middle',
            align: 'left'
          }
        },
        emphasis: {
          focus: 'descendant'
        },
        expandAndCollapse: true,
        animationDuration: 550,
        animationDurationUpdate: 750
      }
    ]
  }

  //   myChart.on('mousedown', e => {
  //     const name = e.data.name
  //     const curNode = myChart._chartsViews[0]._data.tree._nodes.find(item => {
  //       return item.name === name
  //     })
  //     const depth = curNode.depth
  //     const curIsExpand = curNode.isExpand
  //     myChart._chartsViews[0]._data.tree._nodes.forEach((item, index) => {
  //       if (item.depth === depth && item.name !== name && !curIsExpand) {
  //         item.isExpand = false
  //       }
  //     })
  //   })
}
/**
 * 产销差分析
 * @param xData
 * @param yData1 供水量
 * @param yData2 售水量
 * @param yData3 产销差
 */
export const initCXXOption = (
  xData: string[] = [],
  yData1: number[] = [],
  yData2: number[] = [],
  yData3: number[] = []
) => {
  return {
    legend: {
      right: 20,
      top: 'top',
      type: 'scroll',
      textStyle: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: 10,
      right: 30,
      top: 50,
      bottom: 20,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xData,

      axisLabel: {
        show: true,
        textStyle: {
          color: 'rgba(255, 255, 255, 0.6)'
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    yAxis: [
      {
        position: 'left',
        type: 'value',
        name: 'm³',
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.6)'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(255,255,255,0.6)'
          }
        }
      },
      {
        type: 'value',
        name: '产销差（%）',

        axisLabel: {
          show: true,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.6)'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '供水量',
        type: 'bar',
        barWidth: 10,
        data: yData1 || []
      },
      {
        name: '用水量',
        type: 'bar',
        barWidth: 10,
        data: yData2 || []
      },
      {
        name: '产销差',
        type: 'line',
        yAxisIndex: 1,
        data: yData3 || []
      }
    ]
  }
}
