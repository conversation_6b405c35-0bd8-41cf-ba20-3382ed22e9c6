package org.thingsboard.server.dao.maintainCircuit.maintain;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface MaintainTaskMService {
    PageData getList(String code, String name, String planName, String teamName, String userName, String userId, String status, String auditStatus, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId);

    MaintainTaskM save(MaintainTaskM maintainTaskM);

    IstarResponse delete(List<String> ids);

    void reviewer(MaintainTaskM maintainTaskM);

    void changeStatus(MaintainTaskM maintainTaskM);

    List<MaintainTaskM> findAll();

    List statistics(Long startTime, Long endTime, String tenantId);

    MaintainTaskM getDetail(String mainId);

    boolean checkAuditor(String id, String userId);

    IstarResponse receive(String id, String userId);

    Integer getNotCompleteNum(String userId, String tenantId);
}
