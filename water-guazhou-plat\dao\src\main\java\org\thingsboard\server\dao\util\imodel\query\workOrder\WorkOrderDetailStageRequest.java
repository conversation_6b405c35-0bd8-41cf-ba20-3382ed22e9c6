package org.thingsboard.server.dao.util.imodel.query.workOrder;

import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;

public class WorkOrderDetailStageRequest extends WorkOrderStageRequest {
    @Override
    public String valid(IStarHttpRequest request) {

        if (getStage().equals(WorkOrderStatus.SUBMIT.name())) {
            if (StringUtils.isNullOrBlank(getNextProcessUserId()))
                return "未指定下一步处理人";
        }

        return super.valid(request);
    }
}
