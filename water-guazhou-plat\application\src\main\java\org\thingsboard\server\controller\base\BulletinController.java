package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datastax.driver.mapping.annotations.Param;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.BaseResult;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.dashChart.DashChartVO;
import org.thingsboard.server.dao.model.sql.BulletinDataEntity;
import org.thingsboard.server.dao.model.sql.DashChartEntity;
import org.thingsboard.server.dao.util.RestUtil;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.rpc.DeviceRpcService;
import org.thingsboard.server.service.utils.Base64Util;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/4/26 10:54
 */
@RestController
@RequestMapping("/api/bulletin")
public class BulletinController extends BaseController {

    @SneakyThrows
    @PostMapping(value = "save")
   public BulletinDataEntity save(@RequestBody BulletinDataEntity bulletinDataEntity){
        bulletinDataEntity.setUpdateTime(System.currentTimeMillis());
        bulletinDataEntity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return bulletinService.save(bulletinDataEntity);
    }


    @SneakyThrows
    @GetMapping(value = "last")
    public BulletinDataEntity getLastByTenantId(){
        return bulletinService.findLastByTenantId(getTenantId());
    }



    @SneakyThrows
    @GetMapping(value = "list")
    public List<BulletinDataEntity> findByTenantId(){
        return bulletinService.findByTenant(getTenantId());
    }


}
