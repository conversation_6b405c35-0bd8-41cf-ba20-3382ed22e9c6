import{_ as R}from"./TreeBox-DDD2iwoR.js";import{d as j,M as w,c as h,r as d,a8 as B,s as M,a9 as C,o as O,g as p,h as m,F as u,q as o,i as a,p as U,t as $,an as y,bl as F,bm as q,bV as z,b6 as W,b7 as G}from"./index-r0dFAfgr.js";import{_ as H}from"./CardTable-rdWOL4_6.js";import{_ as J}from"./CardSearch-CB_HNR-Q.js";import{_ as Q}from"./index-BJ-QPYom.js";import{I}from"./common-CvK_P_ao.js";import{_ as X}from"./deviceInfo.vue_vue_type_script_setup_true_lang-D5q8lzUm.js";import Y from"./repairinfo-DgFr9NYT.js";import K from"./maintenanceinfo-tc4mDUfU.js";import Z from"./patrolinfo-SPbFoRLm.js";import{s as ee,c as ae}from"./equipmentManage-DuoY00aj.js";import{f as te,a as ne}from"./ledgerManagement-CkhtRd8m.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./charts-CPgX3ymz.js";import"./DateFormatter-Bm9a68Ax.js";const oe={class:"item"},Ne=j({__name:"index",setup(le){const{$btnPerms:T}=w(),k=h(),l=h("deviceinfo"),x=d({title:"asd",labelWidth:"120px",width:"1100px",group:[]}),v=h(),N=h({filters:[{label:"设备名称",field:"name",type:"input"},{label:"设备标签编码",field:"deviceLabelCode",type:"input",labelWidth:"100px"},{label:"安装区域",field:"areaId",type:"select-tree",checkStrictly:!0,options:B(()=>_.installationArea)},{label:"安装位置",field:"address",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:I.QUERY,click:()=>c()},{type:"default",perm:!0,text:"重置",svgIcon:M(G),click:()=>{var e;(e=v.value)==null||e.resetForm(),c()}},{perm:!0,text:"导出",icon:I.EXPORT,click:()=>c(!0)}]}]}),r=d({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"设备编码",prop:"serialId"},{label:"设备标签码",prop:"deviceLabelCode"},{label:"设备型号",prop:"model"},{label:"设备名称",prop:"name"},{label:"使用部门",prop:"departmentName"},{label:"设备类型",prop:"type"},{label:"设备供应商",prop:"supplierName"},{label:"安装区域",prop:"installAddressName"},{label:"安装位置",prop:"detailInstallAddressName"}],operations:[{type:"primary",text:"详情",icon:I.DETAIL,perm:T("RoleManageEdit"),click:e=>{var t;x.title=e.name,i.serialId=e.serialId,i.title=e.name,i.currentId=e.deviceLabelCode,i.id=e.id,l.value="",(t=k.value)==null||t.openDrawer(),setTimeout(()=>{l.value="deviceinfo"},200)}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,c()}}}),f=d({title:" ",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{f.currentProject=e,c()}}),i=d({visible:!1,title:"cs",currentId:"",deviceNo:"",id:"",serialId:"",readonly:!1}),_=d({installationArea:[],getAreaTreeValue:()=>{ee({page:1,size:99999,shortName:""}).then(t=>{_.installationArea=C(t.data.data.data||[])})},init:()=>{_.getAreaTreeValue()}}),A=e=>{console.log("tab ",e)},c=async e=>{var b;const t={size:r.pagination.limit,page:r.pagination.page,deviceTypeId:f.currentProject.id||"",...((b=v.value)==null?void 0:b.queryParams)||{}};if(e){te(t).then(s=>{console.log(s);const g=window.URL.createObjectURL(s.data);console.log(g);const n=document.createElement("a");n.style.display="none",n.href=g,n.setAttribute("download","设备台账.xlsx"),document.body.appendChild(n),n.click()});return}ne(t).then(s=>{r.dataList=s.data.data.data||[],r.pagination.total=s.data.data.total||0})};function S(){ae().then(e=>{f.data=C(e.data.data||[]),c()})}return O(async()=>{S(),_.init()}),(e,t)=>{const b=Q,s=J,g=H,n=F,E=q,L=z,V=W,D=R;return p(),m(D,null,{tree:u(()=>[o(b,{"tree-data":a(f)},null,8,["tree-data"])]),default:u(()=>[o(s,{ref_key:"refSearch",ref:v,config:a(N)},null,8,["config"]),o(g,{config:a(r),class:"card-table"},null,8,["config"]),o(V,{ref_key:"refdetail",ref:k,config:a(x)},{default:u(()=>[U("div",oe,[o(E,{modelValue:a(l),"onUpdate:modelValue":t[0]||(t[0]=P=>$(l)?l.value=P:null),onTabClick:A},{default:u(()=>[o(n,{label:"设备信息",name:"deviceinfo"}),o(n,{label:"维修信息",name:"repairinfo"}),o(n,{label:"保养信息",name:"maintenanceinfo"}),o(n,{label:"巡检信息",name:"patrolinfo"})]),_:1},8,["modelValue"]),o(L,{height:"100%"},{default:u(()=>[a(l)==="deviceinfo"?(p(),m(X,{key:0,id:a(i).id,"serial-id":a(i).serialId},null,8,["id","serial-id"])):y("",!0),a(l)==="repairinfo"?(p(),m(Y,{key:1,id:a(i).currentId},null,8,["id"])):y("",!0),a(l)==="maintenanceinfo"?(p(),m(K,{key:2,id:a(i).currentId},null,8,["id"])):y("",!0),a(l)==="patrolinfo"?(p(),m(Z,{key:3,id:a(i).currentId},null,8,["id"])):y("",!0)]),_:1})])]),_:1},8,["config"])]),_:1})}}});export{Ne as default};
