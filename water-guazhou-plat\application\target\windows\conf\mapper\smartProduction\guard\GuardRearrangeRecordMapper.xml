<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.guard.GuardRearrangeRecordMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        place_id,
        class_id,
        class_name,
        day_time,
        begin_time,
        end_time,
        before_group_user,
        case
            when length(before_group_user) > 0 then
                (select string_agg(coalesce(first_name, '用户不存在'), '、')
                 from regexp_split_to_table(before_group_user, ',') temp(uid)
                          left join tb_user
                                    on tb_user.id = temp.uid) end before_group_user_name,
        after_group_user,
        case
            when length(after_group_user) > 0 then
                (select string_agg(coalesce(first_name, '用户不存在'), '、')
                 from regexp_split_to_table(after_group_user, ',') temp(uid)
                          left join tb_user
                                    on tb_user.id = temp.uid) end after_group_user_name,
        remark,
        creator,
        create_time,
        tenant_id
        <!--@sql from guard_rearrange_record -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRearrangeRecord">
        <result column="id" property="id"/>
        <result column="place_id" property="placeId"/>
        <result column="class_id" property="classId"/>
        <result column="class_name" property="className"/>
        <result column="day_time" property="dayTime"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="before_group_user" property="beforeGroupUser"/>
        <result column="before_group_user_name" property="beforeGroupUserName"/>
        <result column="after_group_user" property="afterGroupUser"/>
        <result column="after_group_user_name" property="afterGroupUserName"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from guard_rearrange_record
        <where>
            <if test="placeId != null and placeId != ''">
                and place_id = #{placeId}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <insert id="record">
        insert into guard_rearrange_record(id, before_group_user, after_group_user, place_id, begin_time, end_time,
                                           class_id,
                                           class_name, create_time, creator, day_time, remark, tenant_id)
        select #{randomId},
        <!-- before_user_group -->
        (select string_agg(user_id, ',') from guard_arrange_partner where arrange_id = #{arrangeId}),
        <!-- after_user_group -->
        (select string_agg(user_id, ',') from (select user_id
                                               from guard_arrange_partner
                                               where arrange_id = #{arrangeId}
                                                 and id != #{id}
                                               union all
                                               select user_id from (values
        <foreach item="item" index="index" collection="guardArrangePartners" separator=",">
            (#{item.userId})
        </foreach>) user_id_list(user_id)
    ) user_id_list(user_id)),
                      place_id,
                      begin_time,
                      end_time,
                      class_id,
                      class_name,
                      now(),
                      #{currentUserId},
                      day_time,
                      #{remark},
                      #{tenantId}
    from guard_arrange
    where id = #{arrangeId}
    </insert>
</mapper>