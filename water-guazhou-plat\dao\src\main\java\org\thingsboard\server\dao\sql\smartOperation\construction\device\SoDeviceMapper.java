package org.thingsboard.server.dao.sql.smartOperation.construction.device;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDevice;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDevicePageRequest;

@Mapper
public interface SoDeviceMapper extends BaseMapper<SoDevice> {
    IPage<SoDevice> findByPage(SoDevicePageRequest request);

    boolean update(SoDevice entity);

    boolean updateFully(SoDevice entity);
    default boolean isSerialIdExists(String serialId, String id, String tenantId) {
        boolean serialIdExists = selectCountBySerialIdAndTenantId(serialId, tenantId) > 0;
        if (id == null) {
            return serialIdExists;
        }
        return serialIdExists && selectCountByIdAndSerialId(id, serialId) == 0;
    }

    int selectCountByIdAndSerialId(@Param("id") String id, @Param("serialId") String serialId);

    int selectCountBySerialIdAndTenantId(@Param("serialId") String serialId, @Param("tenantId") String tenantId);
}
