package org.thingsboard.server.dao.feignService;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2020/2/21 13:30
 */
@FeignClient(value = "service-feign")
@Service
public interface FeignTest {

    @RequestMapping(value = "/api-b/get/Hello",method = RequestMethod.GET)
    String getHello(@RequestParam("end") String end, @RequestParam("start") String start);
}
