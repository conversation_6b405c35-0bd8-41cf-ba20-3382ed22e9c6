<!--

    Copyright © 2016-2019 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.thingsboard.msa</groupId>
        <version>2.3.0</version>
        <artifactId>transport</artifactId>
    </parent>
    <groupId>org.thingsboard.msa.transport</groupId>
    <artifactId>mqtt</artifactId>
    <packaging>pom</packaging>

    <name>ThingsBoard MQTT Transport Microservice</name>
    <url>https://thingsboard.io</url>
    <description>ThingsBoard MQTT Transport Microservice</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <main.dir>${basedir}/../../..</main.dir>
        <pkg.name>tb-mqtt-transport</pkg.name>
        <docker.name>tb-mqtt-transport</docker.name>
        <pkg.user>thingsboard</pkg.user>
        <pkg.logFolder>/var/log/${pkg.name}</pkg.logFolder>
        <pkg.installFolder>/usr/share/${pkg.name}</pkg.installFolder>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.thingsboard.transport</groupId>
            <artifactId>mqtt</artifactId>
            <version>${project.version}</version>
            <classifier>deb</classifier>
            <type>deb</type>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-tb-mqtt-transport-deb</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>org.thingsboard.transport</groupId>
                                    <artifactId>mqtt</artifactId>
                                    <classifier>deb</classifier>
                                    <type>deb</type>
                                    <destFileName>${pkg.name}.deb</destFileName>
                                    <outputDirectory>${project.build.directory}</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-docker-config</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>docker</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>build-docker-image</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <configuration>
                            <skip>${dockerfile.skip}</skip>
                            <repository>${docker.repo}/${docker.name}</repository>
                            <verbose>true</verbose>
                            <googleContainerRegistryEnabled>false</googleContainerRegistryEnabled>
                            <contextDirectory>${project.build.directory}</contextDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>tag-docker-image</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>tag</goal>
                        </goals>
                        <configuration>
                            <skip>${dockerfile.skip}</skip>
                            <repository>${docker.repo}/${docker.name}</repository>
                            <tag>${project.version}</tag>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>push-docker-image</id>
            <activation>
                <property>
                    <name>push-docker-image</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>push-latest-docker-image</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                                <configuration>
                                    <tag>latest</tag>
                                    <repository>${docker.repo}/${docker.name}</repository>
                                </configuration>
                            </execution>
                            <execution>
                                <id>push-version-docker-image</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                                <configuration>
                                    <tag>${project.version}</tag>
                                    <repository>${docker.repo}/${docker.name}</repository>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <repositories>
        <repository>
            <id>jenkins</id>
            <name>Jenkins Repository</name>
            <url>http://repo.jenkins-ci.org/releases</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
