package org.thingsboard.server.dao.menu2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.menu.MenuPoolService;
import org.thingsboard.server.dao.model.sql.MenuResource;
import org.thingsboard.server.dao.sql.menu2.MenuResourceRepository;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class MenuResourceServiceImpl implements MenuResourceService {

    @Autowired
    private MenuResourceRepository menuResourceRepository;

    @Autowired
    private MenuPoolService menuPoolService;

    @Override
    public PageData<MenuResource> findList(int page, int size, String name, String url) {
        PageRequest pageRequest = new PageRequest(page - 1, size);
        Page<MenuResource> pageResult = menuResourceRepository.findList(name, url, pageRequest);
        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public MenuResource save(MenuResource menuResource) {
        return menuResourceRepository.save(menuResource);
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            menuResourceRepository.delete(id);
        }
    }

    @Override
    public List<MenuResource> findAll() {
        return menuResourceRepository.findAll();
    }

    @Override
    @Transactional
    public void menuPoolToMenuResource() {
        List<MenuPool> all = menuPoolService.findAll();

        List<MenuResource> list = new ArrayList<>();
        for (MenuPool menuPool : all) {
            String additionalInfo = menuPool.getAdditionalInfo();
            JSONObject object = JSON.parseObject(additionalInfo);
            if (object.containsKey("component") && StringUtils.isNotBlank(object.getString("component"))
                    && ("LayoutParentView".equals(object.getString("component")) || "Layout".equals(object.getString("component")))) {
                continue;
            }

            MenuResource menuResource = new MenuResource();
            menuResource.setName(menuPool.getDefaultName());
            menuResource.setUrl(object.getString("component"));

            list.add(menuResource);
        }

        menuResourceRepository.save(list);
    }
}
