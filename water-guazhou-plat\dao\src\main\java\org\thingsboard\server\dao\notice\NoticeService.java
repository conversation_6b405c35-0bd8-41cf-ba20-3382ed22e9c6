package org.thingsboard.server.dao.notice;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.fault.FaultReport;
import org.thingsboard.server.dao.model.sql.fault.FaultReportC;
import org.thingsboard.server.dao.model.sql.notice.NoticeDomain;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface NoticeService {

    boolean saveNotice(NoticeDomain notice);

    boolean delete(String id);

    NoticeDomain getNoticeById(String id);

    PageData getList(String type, Date beginTime, Date endTime, int page, int size);

    boolean upNotice(NoticeDomain notice);

    // IstarResponse delete(List<String> ids);
    //
    //
    // FaultReport getByWorkOrderId(String id);
    //
    //
    // PageData<Map> getRepairList(String deviceLabelCode, int page, int size);
}
