package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardClass;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

@Getter
@Setter
public class GuardClassPageRequest extends PageableQueryEntity<GuardClass> {
    // 地点id
    private String placeId;

    // 班次名称
    private String name;

}
