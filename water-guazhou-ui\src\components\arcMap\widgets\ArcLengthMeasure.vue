<template>
  <div>
    <ArcWidgetButton
      id="tool-pipelength"
      ref="refBtn"
      :icon="'mdi:ruler'"
      :title="'管线长度'"
      @click="
        (isCollapsed) =>
          handleToolClick('pipelength', '管线长度测量', isCollapsed)
      "
    ></ArcWidgetButton>
    <Panel
      v-if="mounted"
      ref="refToolPanel"
      :custom-class="'tool-pipelength-measure-panel'"
      :telport="'#arcmap-wrapper'"
      :title="state.toolPanelTitle"
      :destroy-by-close="true"
      :before-close="() => handleToolPanelClose()"
      :before-open="() => initPipeLayerOption()"
    >
      <Form ref="refForm" :config="FormConfig"></Form>
    </Panel>
  </div>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice';
import { PipeStatistics } from '@/api/mapservice/pipe';
import ArcWidgetButton from '@/components/arcMap/arcWidgetButton.vue';
import { useSketch, useWidgets } from '@/hooks/arcgis';
import { useGisStore } from '@/store';
import {
  EStatisticField,
  getGraphicLayer,
  getSubLayerIds
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';

const view: __esri.MapView | undefined = inject('view');

const refToolPanel = ref<IPanelIns>();
const refBtn = ref<InstanceType<typeof ArcWidgetButton>>();
const state = reactive<{
  showOverViewMap: boolean;
  toolPanelTitle: string;
  toolPanelOperate: string;
  measuring: boolean;
}>({
  showOverViewMap: false,
  toolPanelTitle: '',
  toolPanelOperate: '',
  measuring: false
});
const staticState: {
  sketch?: __esri.SketchViewModel;
  queryGeometry?: __esri.Geometry;
  graphicsLayer?: __esri.GraphicsLayer;
} = {};
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'select',
          clearable: false,
          options: [],
          label: '管线图层',
          field: 'pipeLayer'
        },
        { type: 'text', field: 'pipeLength', label: '管线长度', unit: '米' },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '新测量',
              type: 'default',
              loading: () => state.measuring,
              click: () => startMeasure()
            }
          ]
        }
      ]
    }
  ],
  gutter: 12
});
const handleToolClick = async (
  path: string,
  title: string,
  isCollapsed: boolean,
  fromPanel?: boolean
) => {
  if (!isCollapsed) {
    state.toolPanelOperate = path;
    state.toolPanelTitle = title;
    refToolPanel.value?.Open();
  } else {
    !fromPanel && refToolPanel.value?.Close();
  }
};
const handleToolPanelClose = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.sketch?.cancel();
  refBtn.value?.toggle(true);
};

const { initSketch, destroySketch } = useSketch();

const resolveMeasure = async () => {
  if (!staticState.queryGeometry) return;
  state.measuring = true;

  const pipeLayerId = refForm.value?.dataForm.pipeLayer;
  try {
    const res = await PipeStatistics({
      usertoken: useGisStore().gToken,
      layerids: JSON.stringify(pipeLayerId !== undefined ? [pipeLayerId] : []),
      group_fields: JSON.stringify([]),
      statistic_field: EStatisticField.ShapeLen,
      statistic_type: '2',
      where: '1=1',
      geometry: staticState.queryGeometry,
      f: 'pjson'
    });
    if (res.data.code === 10000) {
      const data = res.data?.result?.rows;
      const params = data.length && data[0].rows?.length && data[0].rows[0];
      refForm.value &&
        (refForm.value.dataForm.pipeLength =
          params && params[EStatisticField.ShapeLen]);
    } else {
      SLMessage.error('统计失败');
      refForm.value && (refForm.value.dataForm.pipeLength = undefined);
    }
  } catch (error) {
    console.dir(error);
    SLMessage.error('统计失败');
  }
  state.measuring = false;
};
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    staticState.queryGeometry = result.graphics[0]?.geometry;
    resolveMeasure();
  }
};
const startMeasure = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.sketch?.create('polygon');
};
const initPipeLayerOption = async () => {
  if (!view) return;
  const sublayers = getSubLayerIds(view);
  const layersres = await queryLayerClassName(sublayers);
  const layers = layersres.data?.result?.rows || [];
  const pipeLayerOption: any[] = [];
  layers.map((item) => {
    if (
      item.geometrytype === 'esriGeometryPolyline' ||
      item.layername.indexOf('立管') > -1
    ) {
      pipeLayerOption?.push({
        label: item.layername,
        value: item.layerid,
        id: item.layerid,
        data: item
      });
    }
  });

  const field = FormConfig.group[0].fields[0] as IFormSelect;
  field && (field.options = pipeLayerOption);
  refForm.value &&
    (refForm.value.dataForm.pipeLayer = pipeLayerOption[0]?.value);
};
const { addCustomWidget, removeCustomWidget } = useWidgets();
const mounted = ref<boolean>();
onMounted(() => {
  mounted.value = true;
  addCustomWidget(view, 'tool-pipelength', 'bottom-right');
  staticState.graphicsLayer = getGraphicLayer(view, {
    id: 'pipe-length',
    title: '管线长度测量'
  });
  staticState.sketch = initSketch(view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  });
});
const destroy = () => {
  removeCustomWidget(view, 'tool-pipelength');
  staticState.graphicsLayer?.removeAll();
  destroySketch();
  refToolPanel.value?.Close();
  staticState.graphicsLayer && view?.map.remove(staticState.graphicsLayer);
};
onBeforeUnmount(() => {
  destroy();
});
onUnmounted(() => {
  mounted.value = false;
});
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.tool-pipelength-measure-panel {
  width: 260px;
  height: 250px;
  position: absolute;
  bottom: 15px;
  right: 70px;
}
</style>
