<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.stationSetting.StationCategoryMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.stationSetting.StationCategory">
        SELECT
            a.*
        FROM
            tb_station_category a
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                AND (a.name LIKE '%' || #{param.keyword} || '%' OR a.code LIKE '%' || #{param.keyword} || '%')
            </if>
            <if test="param.type != null and param.type != ''">
                AND a.type = #{param.type}
            </if>
            <if test="param.isSystem != null and param.isSystem != ''">
                AND a.is_system = #{param.isSystem}
            </if>
            <if test="param.status != null and param.status != ''">
                AND a.status = #{param.status}
            </if>

        </where>
        ORDER BY a.order_number,a.create_time
    </select>

</mapper>