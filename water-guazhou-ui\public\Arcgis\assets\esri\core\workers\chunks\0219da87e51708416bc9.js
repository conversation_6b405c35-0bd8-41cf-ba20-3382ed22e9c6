"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[719],{92835:(e,t,r)=>{r.d(t,{Z:()=>m});var s,i=r(43697),l=r(96674),o=r(70586),n=r(35463),a=r(5600),u=(r(75215),r(67676),r(71715)),h=r(52011),c=r(30556);let p=s=class extends l.wq{static get allTime(){return d}static get empty(){return y}constructor(e){super(e),this.end=null,this.start=null}readEnd(e,t){return null!=t.end?new Date(t.end):null}writeEnd(e,t){t.end=e?e.getTime():null}get isAllTime(){return this.equals(s.allTime)}get isEmpty(){return this.equals(s.empty)}readStart(e,t){return null!=t.start?new Date(t.start):null}writeStart(e,t){t.start=e?e.getTime():null}clone(){return new s({end:this.end,start:this.start})}equals(e){if(!e)return!1;const t=(0,o.pC)(this.start)?this.start.getTime():this.start,r=(0,o.pC)(this.end)?this.end.getTime():this.end,s=(0,o.pC)(e.start)?e.start.getTime():e.start,i=(0,o.pC)(e.end)?e.end.getTime():e.end;return t===s&&r===i}expandTo(e){if(this.isEmpty||this.isAllTime)return this.clone();const t=(0,o.yw)(this.start,(t=>(0,n.JE)(t,e))),r=(0,o.yw)(this.end,(t=>{const r=(0,n.JE)(t,e);return t.getTime()===r.getTime()?r:(0,n.Nm)(r,1,e)}));return new s({start:t,end:r})}intersection(e){if(!e)return this.clone();if(this.isEmpty||e.isEmpty)return s.empty;if(this.isAllTime)return e.clone();if(e.isAllTime)return this.clone();const t=(0,o.R2)(this.start,-1/0,(e=>e.getTime())),r=(0,o.R2)(this.end,1/0,(e=>e.getTime())),i=(0,o.R2)(e.start,-1/0,(e=>e.getTime())),l=(0,o.R2)(e.end,1/0,(e=>e.getTime()));let n,a;if(i>=t&&i<=r?n=i:t>=i&&t<=l&&(n=t),r>=i&&r<=l?a=r:l>=t&&l<=r&&(a=l),null!=n&&null!=a&&!isNaN(n)&&!isNaN(a)){const e=new s;return e.start=n===-1/0?null:new Date(n),e.end=a===1/0?null:new Date(a),e}return s.empty}offset(e,t){if(this.isEmpty||this.isAllTime)return this.clone();const r=new s,{start:i,end:l}=this;return(0,o.pC)(i)&&(r.start=(0,n.Nm)(i,e,t)),(0,o.pC)(l)&&(r.end=(0,n.Nm)(l,e,t)),r}union(e){if(!e||e.isEmpty)return this.clone();if(this.isEmpty)return e.clone();if(this.isAllTime||e.isAllTime)return d.clone();const t=(0,o.pC)(this.start)&&(0,o.pC)(e.start)?new Date(Math.min(this.start.getTime(),e.start.getTime())):null,r=(0,o.pC)(this.end)&&(0,o.pC)(e.end)?new Date(Math.max(this.end.getTime(),e.end.getTime())):null;return new s({start:t,end:r})}};(0,i._)([(0,a.Cb)({type:Date,json:{write:{allowNull:!0}}})],p.prototype,"end",void 0),(0,i._)([(0,u.r)("end")],p.prototype,"readEnd",null),(0,i._)([(0,c.c)("end")],p.prototype,"writeEnd",null),(0,i._)([(0,a.Cb)({readOnly:!0,json:{read:!1}})],p.prototype,"isAllTime",null),(0,i._)([(0,a.Cb)({readOnly:!0,json:{read:!1}})],p.prototype,"isEmpty",null),(0,i._)([(0,a.Cb)({type:Date,json:{write:{allowNull:!0}}})],p.prototype,"start",void 0),(0,i._)([(0,u.r)("start")],p.prototype,"readStart",null),(0,i._)([(0,c.c)("start")],p.prototype,"writeStart",null),p=s=(0,i._)([(0,h.j)("esri.TimeExtent")],p);const d=new p,y=new p({start:void 0,end:void 0}),m=p},5732:(e,t,r)=>{r.d(t,{c:()=>s});var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},13867:(e,t,r)=>{r.d(t,{Z:()=>i});var s=r(69801);class i{constructor(e,t){this._storage=new s.WJ,this._storage.maxSize=e,t&&this._storage.registerRemoveFunc("",t)}put(e,t,r){this._storage.put(e,t,r,1)}pop(e){return this._storage.pop(e)}get(e){return this._storage.get(e)}clear(){this._storage.clearAll()}destroy(){this._storage.destroy()}get maxSize(){return this._storage.maxSize}set maxSize(e){this._storage.maxSize=e}}},69801:(e,t,r)=>{r.d(t,{WJ:()=>a,Xq:()=>n});var s,i,l=r(70586),o=r(44553);(i=s||(s={}))[i.ALL=0]="ALL",i[i.SOME=1]="SOME";class n{constructor(e,t,r){this._namespace=e,this._storage=t,this._removeFunc=!1,this._hit=0,this._miss=0,this._storage.register(this),this._namespace+=":",r&&(this._storage.registerRemoveFunc(this._namespace,r),this._removeFunc=!0)}destroy(){this._storage.clear(this._namespace),this._removeFunc&&this._storage.deregisterRemoveFunc(this._namespace),this._storage.deregister(this),this._storage=null}get namespace(){return this._namespace.slice(0,-1)}get hitRate(){return this._hit/(this._hit+this._miss)}get size(){return this._storage.size}get maxSize(){return this._storage.maxSize}resetHitRate(){this._hit=this._miss=0}put(e,t,r,s=0){this._storage.put(this._namespace+e,t,r,s)}get(e){const t=this._storage.get(this._namespace+e);return void 0===t?++this._miss:++this._hit,t}pop(e){const t=this._storage.pop(this._namespace+e);return void 0===t?++this._miss:++this._hit,t}updateSize(e,t,r){this._storage.updateSize(this._namespace+e,t,r)}clear(){this._storage.clear(this._namespace)}clearAll(){this._storage.clearAll()}getStats(){return this._storage.getStats()}resetStats(){this._storage.resetStats()}}class a{constructor(e=10485760){this._maxSize=e,this._db=new Map,this._size=0,this._hit=0,this._miss=0,this._removeFuncs=new o.Z,this._users=new o.Z}destroy(){this.clearAll(),this._removeFuncs.clear(),this._users.clear(),this._db=null}register(e){this._users.push(e)}deregister(e){this._users.removeUnordered(e)}registerRemoveFunc(e,t){this._removeFuncs.push([e,t])}deregisterRemoveFunc(e){this._removeFuncs.filterInPlace((t=>t[0]!==e))}get size(){return this._size}get maxSize(){return this._maxSize}set maxSize(e){this._maxSize=Math.max(e,0),this._checkSizeLimit()}put(e,t,r,i){const l=this._db.get(e);if(l&&(this._size-=l.size,this._db.delete(e),l.entry!==t&&this._notifyRemove(e,l.entry,s.ALL)),r>this._maxSize)return void this._notifyRemove(e,t,s.ALL);if(void 0===t)return void console.warn("Refusing to cache undefined entry ");if(!r||r<0)return void console.warn("Refusing to cache entry with invalid size "+r);const o=1+Math.max(i,-3)- -3;this._db.set(e,{entry:t,size:r,lifetime:o,lives:o}),this._size+=r,this._checkSizeLimit()}updateSize(e,t,r){const i=this._db.get(e);if(i&&i.entry===t){for(this._size-=i.size;r>this._maxSize;){const i=this._notifyRemove(e,t,s.SOME);if(!((0,l.pC)(i)&&i>0))return void this._db.delete(e);r=i}i.size=r,this._size+=r,this._checkSizeLimit()}}pop(e){const t=this._db.get(e);if(t)return this._size-=t.size,this._db.delete(e),++this._hit,t.entry;++this._miss}get(e){const t=this._db.get(e);if(void 0!==t)return this._db.delete(e),t.lives=t.lifetime,this._db.set(e,t),++this._hit,t.entry;++this._miss}getStats(){const e={Size:Math.round(this._size/1048576)+"/"+Math.round(this._maxSize/1048576)+"MB","Hit rate":Math.round(100*this._getHitRate())+"%",Entries:this._db.size.toString()},t={},r=new Array;this._db.forEach(((e,s)=>{const i=e.lifetime;r[i]=(r[i]||0)+e.size,this._users.forAll((r=>{const i=r.namespace;if(s.startsWith(i)){const r=t[i]||0;t[i]=r+e.size}}))}));const s={};this._users.forAll((e=>{const r=e.namespace;if(!isNaN(e.hitRate)&&e.hitRate>0){const i=t[r]||0;t[r]=i,s[r]=Math.round(100*e.hitRate)+"%"}else s[r]="0%"}));const i=Object.keys(t);i.sort(((e,r)=>t[r]-t[e])),i.forEach((r=>e[r]=Math.round(t[r]/2**20)+"MB / "+s[r]));for(let t=r.length-1;t>=0;--t){const s=r[t];s&&(e["Priority "+(t+-3-1)]=Math.round(s/this.size*100)+"%")}return e}resetStats(){this._hit=this._miss=0,this._users.forAll((e=>e.resetHitRate()))}clear(e){this._db.forEach(((t,r)=>{r.startsWith(e)&&(this._size-=t.size,this._db.delete(r),this._notifyRemove(r,t.entry,s.ALL))}))}clearAll(){this._db.forEach(((e,t)=>this._notifyRemove(t,e.entry,s.ALL))),this._size=0,this._db.clear()}_getHitRate(){return this._hit/(this._hit+this._miss)}_notifyRemove(e,t,r){let s;return this._removeFuncs.some((i=>{if(e.startsWith(i[0])){const e=i[1](t,r);return"number"==typeof e&&(s=e),!0}return!1})),s}_checkSizeLimit(){if(!(this._size<=this._maxSize))for(const[e,t]of this._db){if(this._db.delete(e),t.lives<=1){this._size-=t.size;const r=this._notifyRemove(e,t.entry,s.SOME);(0,l.pC)(r)&&r>0&&(this._size+=r,t.lives=t.lifetime,t.size=r,this._db.set(e,t))}else--t.lives,this._db.set(e,t);if(this._size<=.9*this.maxSize)return}}}},17445:(e,t,r)=>{r.d(t,{N1:()=>p,YP:()=>a,Z_:()=>m,gx:()=>u,nn:()=>f,on:()=>c,tX:()=>g});var s=r(91460),i=r(50758),l=r(70586),o=r(95330),n=r(26258);function a(e,t,r={}){return h(e,t,r,d)}function u(e,t,r={}){return h(e,t,r,y)}function h(e,t,r={},s){let i=null;const o=r.once?(e,r)=>{s(e)&&((0,l.hw)(i),t(e,r))}:(e,r)=>{s(e)&&t(e,r)};if(i=(0,n.aQ)(e,o,r.sync,r.equals),r.initial){const t=e();o(t,t)}return i}function c(e,t,r,o={}){let n=null,u=null,h=null;function c(){n&&u&&(u.remove(),o.onListenerRemove?.(n),n=null,u=null)}function p(e){o.once&&o.once&&(0,l.hw)(h),r(e)}const d=a(e,((e,r)=>{c(),(0,s.vT)(e)&&(n=e,u=(0,s.on)(e,t,p),o.onListenerAdd?.(e))}),{sync:o.sync,initial:!0});return h=(0,i.kB)((()=>{d.remove(),c()})),h}function p(e,t){return function(e,t,r){if((0,o.Hc)(r))return Promise.reject((0,o.zE)());const s=e();if(t?.(s))return Promise.resolve(s);let n=null;function a(){n=(0,l.hw)(n)}return new Promise(((s,l)=>{n=(0,i.AL)([(0,o.fu)(r,(()=>{a(),l((0,o.zE)())})),h(e,(e=>{a(),s(e)}),{sync:!1,once:!0},t??d)])}))}(e,y,t)}function d(e){return!0}function y(e){return!!e}r(87538);const m={sync:!0},f={initial:!0},g={sync:!0,initial:!0}},35463:(e,t,r)=>{r.d(t,{JE:()=>o,Nm:()=>l,rJ:()=>n}),r(80442);const s={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},i={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function l(e,t,r){const s=new Date(e.getTime());if(t&&r){const e=i[r],{getter:l,setter:o,multiplier:n}=e;if("months"===r){const e=function(e,t){const r=new Date(e,t+1,1);return r.setDate(0),r.getDate()}(s.getFullYear(),s.getMonth()+t);s.getDate()>e&&s.setDate(e)}s[o](s[l]()+t*n)}return s}function o(e,t){switch(t){case"milliseconds":return new Date(e.getTime());case"seconds":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case"minutes":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case"hours":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case"days":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case"weeks":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case"months":return new Date(e.getFullYear(),e.getMonth(),1);case"years":return new Date(e.getFullYear(),0,1);case"decades":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case"centuries":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function n(e,t,r){return 0===e?0:e*s[t]/s[r]}},94756:(e,t,r)=>{r.r(t),r.d(t,{default:()=>me});var s=r(43697),i=r(3172),l=r(66643),o=r(20102),n=r(22974),a=r(70586),u=r(16453),h=r(95330),c=r(17452),p=r(5600),d=(r(75215),r(71715)),y=r(52011),m=r(30556),f=r(6570),g=r(82971),_=r(87085),w=r(54295),v=r(46486),S=r(17287),A=r(71612),b=r(17017),x=r(38009),E=r(16859),I=r(72965);let T=null;var C=r(92604),R=r(1533);class U{constructor(e,t){this._spriteSource=e,this._maxTextureSize=t,this.devicePixelRatio=1,this._spriteImageFormat="png",this._isRetina=!1,this._spritesData={},this.image=null,this.width=null,this.height=null,this.loadStatus="not-loaded","url"===e.type&&e.spriteFormat&&(this._spriteImageFormat=e.spriteFormat),e.pixelRatio&&(this.devicePixelRatio=e.pixelRatio),this.baseURL=e.spriteUrl}get spriteNames(){const e=[];for(const t in this._spritesData)e.push(t);return e.sort(),e}getSpriteInfo(e){return this._spritesData?this._spritesData[e]:null}async load(e){if(this.baseURL){this.loadStatus="loading";try{await this._loadSprites(e),this.loadStatus="loaded"}catch{this.loadStatus="failed"}}else this.loadStatus="failed"}async _loadSprites(e){this._isRetina=this.devicePixelRatio>1.15;const{width:t,height:r,data:s,json:i}=await this._getSpriteData(this._spriteSource,e),l=Object.keys(i);if(!l||0===l.length||!s)return this._spritesData=this.image=null,void(this.width=this.height=0);this._spritesData=i,this.width=t,this.height=r;const n=Math.max(this._maxTextureSize,4096);if(t>n||r>n){const e=`Sprite resource for style ${this.baseURL} is bigger than the maximum allowed of ${n} pixels}`;throw C.Z.getLogger("esri.layers.support.SpriteSource").error(e),new o.Z("SpriteSource",e)}let a;for(let e=0;e<s.length;e+=4)a=s[e+3]/255,s[e]=s[e]*a,s[e+1]=s[e+1]*a,s[e+2]=s[e+2]*a;this.image=s}async _getSpriteData(e,t){if("image"===e.type){let t,r;if(this.devicePixelRatio<1.15){if(!e.spriteSource1x)throw new o.Z("SpriteSource","no image data provided for low resolution sprites!");t=e.spriteSource1x.image,r=e.spriteSource1x.json}else{if(!e.spriteSource2x)throw new o.Z("SpriteSource","no image data provided for high resolution sprites!");t=e.spriteSource2x.image,r=e.spriteSource2x.json}return"width"in t&&"height"in t&&"data"in t&&((0,R.eP)(t.data)||(0,R.KZ)(t.data))?{width:t.width,height:t.height,data:new Uint8Array(t.data),json:r}:{...P(t),json:r}}const r=(0,c.mN)(this.baseURL),s=r.query?"?"+(0,c.B7)(r.query):"",l=this._isRetina?"@2x":"",n=`${r.path}${l}.${this._spriteImageFormat}${s}`,a=`${r.path}${l}.json${s}`,[u,h]=await Promise.all([(0,i.default)(a,t),(0,i.default)(n,{responseType:"image",...t})]);return{...P(h.data),json:u.data}}}function P(e){const t=document.createElement("canvas"),r=t.getContext("2d");t.width=e.width,t.height=e.height,r.drawImage(e,0,0,e.width,e.height);const s=r.getImageData(0,0,e.width,e.height);return{width:e.width,height:e.height,data:new Uint8Array(s.data)}}var L=r(11145),D=r(68773),F=r(45322),M=r(56608),z=r(55415);class B{constructor(e){this.url=e}async fetchTileIndex(){return this._tileIndexPromise||(this._tileIndexPromise=(0,i.default)(this.url).then((e=>e.data.index))),this._tileIndexPromise}async dataKey(e,t){const r=await this.fetchTileIndex();return(0,h.k_)(t),this._getIndexedDataKey(r,e)}_getIndexedDataKey(e,t){const r=[t];if(t.level<0||t.row<0||t.col<0||t.row>>t.level>0||t.col>>t.level>0)return null;let s=t;for(;0!==s.level;)s=new z.Z(s.level-1,s.row>>1,s.col>>1,s.world),r.push(s);let i,l,o=e,n=r.pop();if(1===o)return n;for(;r.length;)if(i=r.pop(),l=(1&i.col)+((1&i.row)<<1),o){if(0===o[l]){n=null;break}if(1===o[l]){n=i;break}n=i,o=o[l]}return n}}class O{constructor(e,t){this._tilemap=e,this._tileIndexUrl=t}async fetchTileIndex(e){return this._tileIndexPromise||(this._tileIndexPromise=(0,i.default)(this._tileIndexUrl,{query:{...e?.query}}).then((e=>e.data.index))),this._tileIndexPromise}dataKey(e,t){const{level:r,row:s,col:i}=e,l=new z.Z(e);return this._tilemap.fetchAvailabilityUpsample(r,s,i,l,t).then((()=>(l.world=e.world,l))).catch((e=>{if((0,h.D_)(e))throw e;return null}))}}var k=r(30175);class j{constructor(e){this._tileUrl=e,this._promise=null,this._abortController=null,this._abortOptions=[]}getData(e){null===this._promise&&(this._abortController=new AbortController,this._promise=this._makeRequest(this._tileUrl,this._abortController.signal));const t=this._abortOptions;return t.push(e),(0,h.fu)(e,(()=>{t.every((e=>(0,h.Hc)(e)))&&this._abortController.abort()})),this._promise.then((e=>(0,n.d9)(e)))}async _makeRequest(e,t){const{data:r}=await(0,i.default)(e,{responseType:"array-buffer",signal:t});return r}}const N=new Map;function W(e,t,r,s,i){return function(e,t){return(0,k.s1)(N,e,(()=>new j(e))).getData(t).then((t=>(N.delete(e),t))).catch((t=>{throw N.delete(e),t}))}(e.replace(/\{z\}/gi,t.toString()).replace(/\{y\}/gi,r.toString()).replace(/\{x\}/gi,s.toString()),i)}class G{constructor(e,t,r){this.tilemap=null,this.tileInfo=null,this.capabilities=null,this.fullExtent=null,this.name=e,this.sourceUrl=t;const s=(0,c.mN)(this.sourceUrl),i=(0,n.d9)(r),l=i.tiles;if(s)for(let e=0;e<l.length;e++){const t=(0,c.mN)(l[e]);t&&((0,c.YP)(t.path)||(t.path=(0,c.v_)(s.path,t.path)),l[e]=(0,c.fl)(t.path,{...s.query,...t.query}))}this.tileServers=l;const o=r.capabilities&&r.capabilities.split(",").map((e=>e.toLowerCase().trim())),a=!0===r?.exportTilesAllowed,u=!0===o?.includes("tilemap"),h=a&&r.hasOwnProperty("maxExportTilesCount")?r.maxExportTilesCount:0;this.capabilities={operations:{supportsExportTiles:a,supportsTileMap:u},exportTiles:a?{maxExportTilesCount:+h}:null},this.tileInfo=(0,F.d)(i.tileInfo,i,null,{ignoreMinMaxLOD:!0});const p=r.tileMap?(0,c.fl)((0,c.v_)(s.path,r.tileMap),s.query??{}):null;u?(this.type="vector-tile",this.tilemap=new O(new M.y({layer:{parsedUrl:s,tileInfo:this.tileInfo,type:"vector-tile",tileServers:this.tileServers}}),p)):p&&(this.tilemap=new B(p)),this.fullExtent=f.Z.fromJSON(r.fullExtent)}destroy(){}async getRefKey(e,t){return await(this.tilemap?.dataKey(e,t))??e}requestTile(e,t,r,s){return W(this.tileServers[t%this.tileServers.length],e,t,r,s)}isCompatibleWith(e){const t=this.tileInfo,r=e.tileInfo;if(!t.spatialReference.equals(r.spatialReference))return!1;if(!t.origin.equals(r.origin))return!1;if(Math.round(t.dpi)!==Math.round(r.dpi))return!1;const s=t.lods,i=r.lods,l=Math.min(s.length,i.length);for(let e=0;e<l;e++){const t=s[e],r=i[e];if(t.level!==r.level||Math.round(t.scale)!==Math.round(r.scale))return!1}return!0}}const Z=D.Z.defaults&&D.Z.defaults.io.corsEnabledServers;function H(e){if(!e)return;const t=(0,c.P$)(e);Z&&!Z.includes(t)&&Z.push(t)}function $(...e){let t;for(const r of e)if(null!=r)if((0,c.oC)(r)){if(t){const e=t.split("://")[0];t=e+":"+r.trim()}}else t=(0,c.YP)(r)?r:(0,c.v_)(t,r);return t?(0,c.Qj)(t):void 0}async function q(e,t,r,s,l){let o,n,a;if((0,h.k_)(l),"string"==typeof r){const e=(0,c.Fv)(r);H(e),a=await(0,i.default)(e,{...l,responseType:"json",query:{f:"json",...l?.query}}),a.ssl&&(o&&(o=o.replace(/^http:/i,"https:")),n&&(n=n.replace(/^http:/i,"https:"))),o=e,n=e}else null!=r&&(a={data:r},o=r.jsonUrl||null,n=s);const u=a?.data;if(Y(u))return e.styleUrl=o||null,async function(e,t,r,s){const i=r?(0,c.Yd)(r):(0,c.L)();e.styleBase=i,e.style=t,e.styleUrl&&H(e.styleUrl),t["sprite-format"]&&"webp"===t["sprite-format"].toLowerCase()&&(e.spriteFormat="webp");const l=[];if(t.sources&&t.sources.esri){const r=t.sources.esri;r.url?await q(e,"esri",$(i,r.url),void 0,s):l.push(q(e,"esri",r,i,s))}for(const r of Object.keys(t.sources))"esri"!==r&&"vector"===t.sources[r].type&&(t.sources[r].url?l.push(q(e,r,$(i,t.sources[r].url),void 0,s)):t.sources[r].tiles&&l.push(q(e,r,t.sources[r],i,s)));await Promise.all(l)}(e,u,n,l);if(function(e){return!Y(e)}(u))return e.sourceUrl?K(e,u,n,!1,t,l):(e.sourceUrl=o||null,K(e,u,n,!0,t,l));throw new Error("You must specify the URL or the JSON for a service or for a style.")}function Y(e){return!!e?.sources}async function K(e,t,r,s,i,l){const o=r?(0,c.Qj)(r)+"/":(0,c.L)(),n=function(e,t){if(e.hasOwnProperty("tileInfo"))return e;const r={xmin:-20037507.067161843,ymin:-20037507.067161843,xmax:20037507.067161843,ymax:20037507.067161843,spatialReference:{wkid:102100}};let s=78271.51696400007,i=295828763.7957775;const l=[],o=e.hasOwnProperty("minzoom")?+e.minzoom:0,n=e.hasOwnProperty("maxzoom")?+e.maxzoom:22;for(let e=0;e<=n;e++)e>=o&&l.push({level:e,scale:i,resolution:s}),s/=2,i/=2;for(const r of e.tiles??[])H($(t,r));return{capabilities:"TilesOnly",initialExtent:r,fullExtent:r,minScale:0,maxScale:0,tiles:e.tiles,tileInfo:{rows:512,cols:512,dpi:96,format:"pbf",origin:{x:-20037508.342787,y:20037508.342787},lods:l,spatialReference:{wkid:102100}}}}(t,o),a=new G(i,(0,c.fl)(o,l?.query??{}),n);if(!s&&e.primarySourceName in e.sourceNameToSource){const t=e.sourceNameToSource[e.primarySourceName];if(!t.isCompatibleWith(a))return;null!=a.fullExtent&&(null!=t.fullExtent?t.fullExtent.union(a.fullExtent):t.fullExtent=a.fullExtent.clone()),t.tileInfo&&a.tileInfo&&t.tileInfo.lods.length<a.tileInfo.lods.length&&(t.tileInfo=a.tileInfo)}if(s?(e.sourceBase=o,e.source=t,e.validatedSource=n,e.primarySourceName=i,e.sourceUrl&&H(e.sourceUrl)):H(o),e.sourceNameToSource[i]=a,!e.style){if(null==t.defaultStyles)throw new Error;return"string"==typeof t.defaultStyles?q(e,"",$(o,t.defaultStyles,"root.json"),void 0,l):q(e,"",t.defaultStyles,$(o,"root.json"),l)}}var V=r(33516),Q=r(19745),J=r(25929),X=r(94139),ee=r(39450);const te=1e-6;function re(e,t){if(e===t)return!0;if(null==e&&null!=t)return!1;if(null!=e&&null==t)return!1;if(null==e||null==t)return!1;if(!e.spatialReference.equals(t.spatialReference)||e.dpi!==t.dpi)return!1;const r=e.origin,s=t.origin;if(Math.abs(r.x-s.x)>=te||Math.abs(r.y-s.y)>=te)return!1;let i,l;e.lods[0].scale>t.lods[0].scale?(i=e,l=t):(l=e,i=t);for(let e=i.lods[0].scale;e>=l.lods[l.lods.length-1].scale-te;e/=2)if(Math.abs(e-l.lods[0].scale)<te)return!0;return!1}function se(e,t){if(e===t)return e;if(null==e&&null!=t)return t;if(null!=e&&null==t)return e;if(null==e||null==t)return null;const r=e.size[0],s=e.format,i=e.dpi,l=new X.Z({x:e.origin.x,y:e.origin.y}),o=e.spatialReference,n=e.lods[0].scale>t.lods[0].scale?e.lods[0]:t.lods[0],a=e.lods[e.lods.length-1].scale<=t.lods[t.lods.length-1].scale?e.lods[e.lods.length-1]:t.lods[t.lods.length-1],u=n.scale,h=n.resolution,c=a.scale,p=[];let d=u,y=h,m=0;for(;d>c;)p.push(new ee.Z({level:m,resolution:y,scale:d})),m++,d/=2,y/=2;return new L.Z({size:[r,r],dpi:i,format:s||"pbf",origin:l,lods:p,spatialReference:o})}var ie=r(51785),le=r(54738);let oe,ne;function ae(e){const t=(0,le.Sh)(e);for(;t.length>1;){const e=ue(t.shift());if(e.available)return e}return ue(t.shift())}function ue(e){switch(e){case le.zO.WEBGL1:return oe||(oe=function(){const e=new ce,t=de(le.zO.WEBGL1,e);return(0,a.Wi)(t)||(e.supportsElementIndexUint=null!==t.getExtension("OES_element_index_uint"),e.supportsStandardDerivatives=null!==t.getExtension("OES_standard_derivatives"),e.supportsInstancedArrays=null!==t.getExtension("ANGLE_instanced_arrays"),e.supportsTextureFloat=null!==t.getExtension("OES_texture_float"),e.supportsTextureHalfFloat=null!==t.getExtension("OES_texture_half_float"),e.supportsColorBufferFloat=null!==t.getExtension("WEBGL_color_buffer_float"),e.supportsColorBufferFloatBlend=null!==t.getExtension("EXT_float_blend"),e.supportsColorBufferHalfFloat=null!==t.getExtension("EXT_color_buffer_half_float")),e}()),oe;case le.zO.WEBGL2:return ne||(ne=function(){const e=new pe,t=de(le.zO.WEBGL2,e);return(0,a.Wi)(t)||(e.supportsColorBufferFloat=null!==t.getExtension("EXT_color_buffer_float"),e.supportsColorBufferFloatBlend=null!==t.getExtension("EXT_float_blend"),e.supportsColorBufferHalfFloat=e.supportsColorBufferFloat||null!==t.getExtension("EXT_color_buffer_half_float")),e}()),ne}}class he{constructor(){this.available=!1,this.majorPerformanceCaveat=!1,this.maxTextureSize=0,this.supportsVertexShaderSamplers=!1,this.supportsHighPrecisionFragment=!1,this.supportsElementIndexUint=!1,this.supportsStandardDerivatives=!1,this.supportsInstancedArrays=!1,this.supportsTextureFloat=!1,this.supportsTextureHalfFloat=!1,this.supportsColorBufferFloat=!1,this.supportsColorBufferFloatBlend=!1,this.supportsColorBufferHalfFloat=!1}}class ce extends he{constructor(){super(...arguments),this.type=le.zO.WEBGL1}}class pe extends he{constructor(){super(...arguments),this.type=le.zO.WEBGL2,this.supportsElementIndexUint=!0,this.supportsStandardDerivatives=!0,this.supportsInstancedArrays=!0,this.supportsTextureFloat=!0,this.supportsTextureHalfFloat=!0}}function de(e,t){if(e===le.zO.WEBGL1&&"undefined"==typeof WebGLRenderingContext||e===le.zO.WEBGL2&&"undefined"==typeof WebGL2RenderingContext)return null;const r=document.createElement("canvas");if(!r)return null;let s=(0,le.kr)(r,e,{failIfMajorPerformanceCaveat:!0});if((0,a.Wi)(s)&&(s=(0,le.kr)(r,e),(0,a.pC)(s)&&(t.majorPerformanceCaveat=!0)),(0,a.Wi)(s))return s;if(e===le.zO.WEBGL1){const e=s.getParameter(s.VERSION)?.match(/^WebGL\s+([\d.]*)/);if(e){const r=parseFloat(e[1]);t.available=r>=.94}}else t.available=!0;t.maxTextureSize=s.getParameter(s.MAX_TEXTURE_SIZE),t.supportsVertexShaderSamplers=s.getParameter(s.MAX_VERTEX_TEXTURE_IMAGE_UNITS)>0;const i=s.getShaderPrecisionFormat(s.FRAGMENT_SHADER,s.HIGH_FLOAT);return i&&(t.supportsHighPrecisionFragment=i.precision>0),s}let ye=class extends((0,A.h)((0,I.M)((0,v.Z)((0,S.Y)((0,x.q)((0,E.I)((0,b.N)((0,w.V)((0,u.R)(_.Z)))))))))){constructor(...e){super(...e),this._spriteSourceMap=new Map,this.currentStyleInfo=null,this.style=null,this.isReference=null,this.operationalLayerType="VectorTileLayer",this.type="vector-tile",this.url=null,this.showCollisionBoxes="none",this.path=null}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}destroy(){if(this.sourceNameToSource)for(const e of Object.values(this.sourceNameToSource))e?.destroy();this._spriteSourceMap.clear()}async prefetchResources(e){await this.loadSpriteSource(globalThis.devicePixelRatio||1,e)}load(e){const t=this.loadFromPortal({supportedTypes:["Vector Tile Service"],supportsData:!1},e).catch(h.r9).then((async()=>{if(!this.portalItem||!this.portalItem.id)return;const t=`${this.portalItem.itemUrl}/resources/styles/root.json`;(await(0,i.default)(t,{...e,query:{f:"json",...this.customParameters,token:this.apiKey}})).data&&this.read({url:t},(0,V.h)(this.portalItem))})).catch(h.r9).then((()=>this._loadStyle(e)));return this.addResolvingPromise(t),Promise.resolve(this)}get attributionDataUrl(){const e=this.currentStyleInfo,t=e&&e.serviceUrl&&(0,c.mN)(e.serviceUrl);if(!t)return null;const r=this._getDefaultAttribution(t.path);return r?(0,c.fl)(r,{...this.customParameters,token:this.apiKey}):null}get capabilities(){const e=this.primarySource;return e?e.capabilities:{operations:{supportsExportTiles:!1,supportsTileMap:!1},exportTiles:null}}get fullExtent(){return this.primarySource?.fullExtent||null}get parsedUrl(){return this.serviceUrl?(0,c.mN)(this.serviceUrl):null}get serviceUrl(){return this.currentStyleInfo&&this.currentStyleInfo.serviceUrl||null}get spatialReference(){return this.tileInfo?.spatialReference??null}get styleUrl(){return this.currentStyleInfo&&this.currentStyleInfo.styleUrl||null}writeStyleUrl(e,t){e&&(0,c.oC)(e)&&(e=`https:${e}`);const r=(0,a.Wg)((0,Q.a)(e));t.styleUrl=(0,J.e)(e,r)}get tileInfo(){const e=[];for(const t in this.sourceNameToSource)e.push(this.sourceNameToSource[t]);let t=this.primarySource?.tileInfo||new L.Z;if(e.length>1)for(let r=0;r<e.length;r++)re(t,e[r].tileInfo)&&(t=se(t,e[r].tileInfo));return t}readVersion(e,t){return t.version?parseFloat(t.version):parseFloat(t.currentVersion)}async loadSpriteSource(e=1,t){if(!this._spriteSourceMap.has(e)){const r=ae("2d").maxTextureSize,s=this.currentStyleInfo?.spriteUrl?(0,c.fl)(this.currentStyleInfo.spriteUrl,{...this.customParameters,token:this.apiKey}):null,i=new U({type:"url",spriteUrl:s,pixelRatio:e,spriteFormat:this.currentStyleInfo?.spriteFormat},r);await i.load(t),this._spriteSourceMap.set(e,i)}return this._spriteSourceMap.get(e)}async setSpriteSource(e,t){if(!e)return null;const r=ae("2d").maxTextureSize,s=e.spriteUrl,i=s?(0,c.fl)(s,{...this.customParameters,token:this.apiKey}):null;if(!i&&"url"===e.type)return null;const l=new U(e,r);try{await l.load(t);const r=e.pixelRatio||1;return this._spriteSourceMap.clear(),this._spriteSourceMap.set(r,l),i&&this.currentStyleInfo&&(this.currentStyleInfo.spriteUrl=i),this.emit("spriteSource-change",{spriteSource:l}),l}catch(e){(0,h.r9)(e)}return null}async loadStyle(e,t){const r=e||this.style||this.url;return this._loadingTask&&"string"==typeof r&&this.url===r||(this._loadingTask?.abort(),this._loadingTask=(0,l.vr)((e=>(this._spriteSourceMap.clear(),this._getSourceAndStyle(r,{signal:e}))),t)),this._loadingTask.promise}getStyleLayerId(e){return this.styleRepository.getStyleLayerId(e)}getStyleLayerIndex(e){return this.styleRepository.getStyleLayerIndex(e)}getPaintProperties(e){return(0,n.d9)(this.styleRepository.getPaintProperties(e))}setPaintProperties(e,t){const r=this.styleRepository.isPainterDataDriven(e);this.styleRepository.setPaintProperties(e,t);const s=this.styleRepository.isPainterDataDriven(e);this.emit("paint-change",{layer:e,paint:t,isDataDriven:r||s})}getStyleLayer(e){return(0,n.d9)(this.styleRepository.getStyleLayer(e))}setStyleLayer(e,t){this.styleRepository.setStyleLayer(e,t),this.emit("style-layer-change",{layer:e,index:t})}deleteStyleLayer(e){this.styleRepository.deleteStyleLayer(e),this.emit("delete-style-layer",{layer:e})}getLayoutProperties(e){return(0,n.d9)(this.styleRepository.getLayoutProperties(e))}setLayoutProperties(e,t){this.styleRepository.setLayoutProperties(e,t),this.emit("layout-change",{layer:e,layout:t})}setStyleLayerVisibility(e,t){this.styleRepository.setStyleLayerVisibility(e,t),this.emit("style-layer-visibility-change",{layer:e,visibility:t})}getStyleLayerVisibility(e){return this.styleRepository.getStyleLayerVisibility(e)}write(e,t){return t?.origin&&!this.styleUrl?(t.messages&&t.messages.push(new o.Z("vectortilelayer:unsupported",`VectorTileLayer (${this.title}, ${this.id}) with style defined by JSON only are not supported`,{layer:this})),null):super.write(e,t)}getTileUrl(e,t,r){return null}async _getSourceAndStyle(e,t){if(!e)throw new Error("invalid style!");const r=await async function(e,t){const r={source:null,sourceBase:null,sourceUrl:null,validatedSource:null,style:null,styleBase:null,styleUrl:null,sourceNameToSource:{},primarySourceName:"",spriteFormat:"png"},[s,i]="string"==typeof e?[e,null]:[null,e.jsonUrl];await q(r,"esri",e,i,t);const l={layerDefinition:r.validatedSource,url:s,serviceUrl:r.sourceUrl,style:r.style,styleUrl:r.styleUrl,spriteUrl:r.style.sprite&&$(r.styleBase,r.style.sprite),spriteFormat:r.spriteFormat,glyphsUrl:r.style.glyphs&&$(r.styleBase,r.style.glyphs),sourceNameToSource:r.sourceNameToSource,primarySourceName:r.primarySourceName};return H(l.spriteUrl),H(l.glyphsUrl),l}(e,{...t,query:{...this.customParameters,token:this.apiKey}});"webp"===r.spriteFormat&&(await function(e){if(T)return T;const t={lossy:"UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",lossless:"UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==",alpha:"UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==",animation:"UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA"};return T=new Promise((e=>{const r=new Image;r.onload=()=>{r.onload=r.onerror=null,e(r.width>0&&r.height>0)},r.onerror=()=>{r.onload=r.onerror=null,e(!1)},r.src="data:image/webp;base64,"+t.lossy})),T}()||(r.spriteFormat="png")),this._set("currentStyleInfo",{...r}),"string"==typeof e?(this.url=e,this.style=null):(this.url=null,this.style=e),this._set("sourceNameToSource",r.sourceNameToSource),this._set("primarySource",r.sourceNameToSource[r.primarySourceName]),this._set("styleRepository",new ie.Z(r.style)),this.read(r.layerDefinition,{origin:"service"}),this.emit("load-style")}_getDefaultAttribution(e){const t=e.match(/^https?:\/\/(?:basemaps|basemapsbeta|basemapsdev)(?:-api)?\.arcgis\.com(\/[^\/]+)?\/arcgis\/rest\/services\/([^\/]+(\/[^\/]+)*)\/vectortileserver/i),r=["OpenStreetMap_v2","OpenStreetMap_Daylight_v2","OpenStreetMap_Export_v2","OpenStreetMap_FTS_v2","OpenStreetMap_GCS_v2","World_Basemap","World_Basemap_v2","World_Basemap_Export_v2","World_Basemap_GCS_v2","World_Basemap_WGS84","World_Contours_v2"];if(!t)return;const s=t[2]&&t[2].toLowerCase();if(!s)return;const i=t[1]||"";for(const e of r)if(e.toLowerCase().includes(s))return(0,c.Fv)(`//static.arcgis.com/attribution/Vector${i}/${e}`)}async _loadStyle(e){return this._loadingTask?.promise??this.loadStyle(null,e)}};(0,s._)([(0,p.Cb)({readOnly:!0})],ye.prototype,"attributionDataUrl",null),(0,s._)([(0,p.Cb)({type:["show","hide"]})],ye.prototype,"listMode",void 0),(0,s._)([(0,p.Cb)({json:{read:!0,write:!0}})],ye.prototype,"blendMode",void 0),(0,s._)([(0,p.Cb)({readOnly:!0,json:{read:!1}})],ye.prototype,"capabilities",null),(0,s._)([(0,p.Cb)({readOnly:!0})],ye.prototype,"currentStyleInfo",void 0),(0,s._)([(0,p.Cb)({json:{read:!1},readOnly:!0,type:f.Z})],ye.prototype,"fullExtent",null),(0,s._)([(0,p.Cb)()],ye.prototype,"style",void 0),(0,s._)([(0,p.Cb)({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],ye.prototype,"isReference",void 0),(0,s._)([(0,p.Cb)({type:["VectorTileLayer"]})],ye.prototype,"operationalLayerType",void 0),(0,s._)([(0,p.Cb)({readOnly:!0})],ye.prototype,"parsedUrl",null),(0,s._)([(0,p.Cb)({readOnly:!0})],ye.prototype,"serviceUrl",null),(0,s._)([(0,p.Cb)({type:g.Z,readOnly:!0})],ye.prototype,"spatialReference",null),(0,s._)([(0,p.Cb)({readOnly:!0})],ye.prototype,"styleRepository",void 0),(0,s._)([(0,p.Cb)({readOnly:!0})],ye.prototype,"sourceNameToSource",void 0),(0,s._)([(0,p.Cb)({readOnly:!0})],ye.prototype,"primarySource",void 0),(0,s._)([(0,p.Cb)({type:String,readOnly:!0,json:{write:{ignoreOrigin:!0},origins:{"web-document":{write:{ignoreOrigin:!0,isRequired:!0}}}}})],ye.prototype,"styleUrl",null),(0,s._)([(0,m.c)(["portal-item","web-document"],"styleUrl")],ye.prototype,"writeStyleUrl",null),(0,s._)([(0,p.Cb)({json:{read:!1,origins:{service:{read:!1}}},readOnly:!0,type:L.Z})],ye.prototype,"tileInfo",null),(0,s._)([(0,p.Cb)({json:{read:!1},readOnly:!0,value:"vector-tile"})],ye.prototype,"type",void 0),(0,s._)([(0,p.Cb)({json:{origins:{"web-document":{read:{source:"styleUrl"}},"portal-item":{read:{source:"url"}}},write:!1,read:!1}})],ye.prototype,"url",void 0),(0,s._)([(0,p.Cb)({readOnly:!0})],ye.prototype,"version",void 0),(0,s._)([(0,d.r)("version",["version","currentVersion"])],ye.prototype,"readVersion",null),(0,s._)([(0,p.Cb)({type:String})],ye.prototype,"showCollisionBoxes",void 0),(0,s._)([(0,p.Cb)({type:String,json:{origins:{"web-scene":{read:!0,write:!0}},read:!1}})],ye.prototype,"path",void 0),ye=(0,s._)([(0,y.j)("esri.layers.VectorTileLayer")],ye);const me=ye},54295:(e,t,r)=>{r.d(t,{V:()=>o});var s=r(43697),i=r(5600),l=(r(75215),r(67676),r(52011));const o=e=>{let t=class extends e{get apiKey(){return this._isOverridden("apiKey")?this._get("apiKey"):"portalItem"in this?this.portalItem?.apiKey:null}set apiKey(e){null!=e?this._override("apiKey",e):(this._clearOverride("apiKey"),this.clear("apiKey","user"))}};return(0,s._)([(0,i.Cb)({type:String})],t.prototype,"apiKey",null),t=(0,s._)([(0,l.j)("esri.layers.mixins.APIKeyMixin")],t),t}},46486:(e,t,r)=>{r.d(t,{Z:()=>h});var s=r(43697),i=(r(66577),r(5600)),l=(r(75215),r(67676),r(71715)),o=r(52011),n=r(45322),a=r(56608),u=r(82971);const h=e=>{let t=class extends e{constructor(){super(...arguments),this.copyright=null,this.minScale=0,this.maxScale=0,this.spatialReference=null,this.tileInfo=null,this.tilemapCache=null}readMinScale(e,t){return null!=t.minLOD&&null!=t.maxLOD?e:0}readMaxScale(e,t){return null!=t.minLOD&&null!=t.maxLOD?e:0}get supportsBlankTile(){return this.version>=10.2}readTilemapCache(e,t){return t.capabilities&&t.capabilities.includes("Tilemap")?new a.y({layer:this}):null}};return(0,s._)([(0,i.Cb)({json:{read:{source:"copyrightText"}}})],t.prototype,"copyright",void 0),(0,s._)([(0,i.Cb)()],t.prototype,"minScale",void 0),(0,s._)([(0,l.r)("service","minScale")],t.prototype,"readMinScale",null),(0,s._)([(0,i.Cb)()],t.prototype,"maxScale",void 0),(0,s._)([(0,l.r)("service","maxScale")],t.prototype,"readMaxScale",null),(0,s._)([(0,i.Cb)({type:u.Z})],t.prototype,"spatialReference",void 0),(0,s._)([(0,i.Cb)({readOnly:!0})],t.prototype,"supportsBlankTile",null),(0,s._)([(0,i.Cb)(n.h)],t.prototype,"tileInfo",void 0),(0,s._)([(0,i.Cb)()],t.prototype,"tilemapCache",void 0),(0,s._)([(0,l.r)("service","tilemapCache",["capabilities"])],t.prototype,"readTilemapCache",null),(0,s._)([(0,i.Cb)()],t.prototype,"version",void 0),t=(0,s._)([(0,o.j)("esri.layers.mixins.ArcGISCachedService")],t),t}},17017:(e,t,r)=>{r.d(t,{N:()=>o});var s=r(43697),i=r(5600),l=(r(75215),r(67676),r(52011));const o=e=>{let t=class extends e{constructor(){super(...arguments),this.customParameters=null}};return(0,s._)([(0,i.Cb)({type:Object,json:{write:{overridePolicy:e=>({enabled:!!(e&&Object.keys(e).length>0)})}}})],t.prototype,"customParameters",void 0),t=(0,s._)([(0,l.j)("esri.layers.mixins.CustomParametersMixin")],t),t}},16859:(e,t,r)=>{r.d(t,{I:()=>A});var s=r(43697),i=r(68773),l=r(40330),o=r(3172),n=r(66643),a=r(20102),u=r(92604),h=r(70586),c=r(95330),p=r(17452),d=r(5600),y=(r(75215),r(67676),r(71715)),m=r(52011),f=r(30556),g=r(84230),_=r(65587),w=r(15235),v=r(86082),S=r(14661);const A=e=>{let t=class extends e{constructor(){super(...arguments),this.resourceReferences={portalItem:null,paths:[]},this.userHasEditingPrivileges=!0,this.userHasFullEditingPrivileges=!1,this.userHasUpdateItemPrivileges=!1}destroy(){this.portalItem=(0,h.SC)(this.portalItem)}set portalItem(e){e!==this._get("portalItem")&&(this.removeOrigin("portal-item"),this._set("portalItem",e))}readPortalItem(e,t,r){if(t.itemId)return new w.default({id:t.itemId,portal:r&&r.portal})}writePortalItem(e,t){e&&e.id&&(t.itemId=e.id)}async loadFromPortal(e,t){if(this.portalItem&&this.portalItem.id)try{const s=await r.e(8062).then(r.bind(r,18062));return(0,c.k_)(t),await s.load({instance:this,supportedTypes:e.supportedTypes,validateItem:e.validateItem,supportsData:e.supportsData,layerModuleTypeMap:e.layerModuleTypeMap},t)}catch(e){throw(0,c.D_)(e)||u.Z.getLogger(this.declaredClass).warn(`Failed to load layer (${this.title}, ${this.id}) portal item (${this.portalItem.id})\n  ${e}`),e}}async finishLoadEditablePortalLayer(e){this._set("userHasEditingPrivileges",await this._fetchUserHasEditingPrivileges(e).catch((e=>((0,c.r9)(e),!0))))}async _setUserPrivileges(e,t){if(!i.Z.userPrivilegesApplied)return this.finishLoadEditablePortalLayer(t);if(this.url)try{const{features:{edit:r,fullEdit:s},content:{updateItem:i}}=await this._fetchUserPrivileges(e,t);this._set("userHasEditingPrivileges",r),this._set("userHasFullEditingPrivileges",s),this._set("userHasUpdateItemPrivileges",i)}catch(e){(0,c.r9)(e)}}async _fetchUserPrivileges(e,t){let r=this.portalItem;if(!e||!r||!r.loaded||r.sourceUrl)return this._fetchFallbackUserPrivileges(t);const s=e===r.id;if(s&&r.portal.user)return(0,S.Ss)(r);let i,o;if(s)i=r.portal.url;else try{i=await(0,g.oP)(this.url,t)}catch(e){(0,c.r9)(e)}if(!i||!(0,p.Zo)(i,r.portal.url))return this._fetchFallbackUserPrivileges(t);try{const e=(0,h.pC)(t)?t.signal:null;o=await(l.id?.getCredential(`${i}/sharing`,{prompt:!1,signal:e}))}catch(e){(0,c.r9)(e)}if(!o)return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}};try{if(s?await r.reload():(r=new w.default({id:e,portal:{url:i}}),await r.load(t)),r.portal.user)return(0,S.Ss)(r)}catch(e){(0,c.r9)(e)}return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}}}async _fetchFallbackUserPrivileges(e){let t=!0;try{t=await this._fetchUserHasEditingPrivileges(e)}catch(e){(0,c.r9)(e)}return{features:{edit:t,fullEdit:!1},content:{updateItem:!1}}}async _fetchUserHasEditingPrivileges(e){const t=this.url?l.id?.findCredential(this.url):null;if(!t)return!0;const r=b.credential===t?b.user:await this._fetchEditingUser(e);return b.credential=t,b.user=r,(0,h.Wi)(r)||null==r.privileges||r.privileges.includes("features:user:edit")}async _fetchEditingUser(e){const t=this.portalItem?.portal?.user;if(t)return t;const r=l.id.findServerInfo(this.url??"");if(!r?.owningSystemUrl)return null;const s=`${r.owningSystemUrl}/sharing/rest`,i=_.Z.getDefault();if(i&&i.loaded&&(0,p.Fv)(i.restUrl)===(0,p.Fv)(s))return i.user;const a=`${s}/community/self`,u=(0,h.pC)(e)?e.signal:null,c=await(0,n.q6)((0,o.default)(a,{authMode:"no-prompt",query:{f:"json"},signal:u}));return c.ok?v.default.fromJSON(c.value.data):null}read(e,t){t&&(t.layer=this),super.read(e,t)}write(e,t){const r=t&&t.portal,s=this.portalItem&&this.portalItem.id&&(this.portalItem.portal||_.Z.getDefault());return r&&s&&!(0,p.tm)(s.restUrl,r.restUrl)?(t.messages&&t.messages.push(new a.Z("layer:cross-portal",`The layer '${this.title} (${this.id})' cannot be persisted because it refers to an item on a different portal than the one being saved to. To save, set layer.portalItem to null or save to the same portal as the item associated with the layer`,{layer:this})),null):super.write(e,{...t,layer:this})}};return(0,s._)([(0,d.Cb)({type:w.default})],t.prototype,"portalItem",null),(0,s._)([(0,y.r)("web-document","portalItem",["itemId"])],t.prototype,"readPortalItem",null),(0,s._)([(0,f.c)("web-document","portalItem",{itemId:{type:String}})],t.prototype,"writePortalItem",null),(0,s._)([(0,d.Cb)({clonable:!1})],t.prototype,"resourceReferences",void 0),(0,s._)([(0,d.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasEditingPrivileges",void 0),(0,s._)([(0,d.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasFullEditingPrivileges",void 0),(0,s._)([(0,d.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasUpdateItemPrivileges",void 0),t=(0,s._)([(0,m.j)("esri.layers.mixins.PortalLayer")],t),t},b={credential:null,user:null}},33516:(e,t,r)=>{r.d(t,{Y:()=>o,h:()=>l});var s=r(17452),i=r(65587);function l(e){return{origin:"portal-item",url:(0,s.mN)(e.itemUrl),portal:e.portal||i.Z.getDefault(),portalItem:e,readResourcePaths:[]}}function o(e){return{origin:"portal-item",messages:[],writtenProperties:[],url:e.itemUrl?(0,s.mN)(e.itemUrl):null,portal:e.portal||i.Z.getDefault(),portalItem:e}}},55415:(e,t,r)=>{r.d(t,{Z:()=>i});var s=r(71143);class i{static getId(e,t,r,s){return"object"==typeof e?`${e.level}/${e.row}/${e.col}/${e.world}`:`${e}/${t}/${r}/${s}`}constructor(e,t,r,s){this.set(e,t,r,s)}get key(){return this}get id(){return this.toString()}set id(e){this.set(e)}get hash(){const e=4095&this.row,t=4095&this.col,r=63&this.level;return(3&this.world)<<30|t<<22|e<<8|r}acquire(e,t,r,s){this.set(e,t,r,s)}contains(e){const t=e.level-this.level;return t>=0&&this.row===e.row>>t&&this.col===e.col>>t&&this.world===e.world}equals(e){return this.level===e.level&&this.row===e.row&&this.col===e.col&&this.world===e.world}clone(){return new i(this)}release(){this.level=0,this.row=0,this.col=0,this.world=0}set(e,t,r,s){if(null==e)this.level=0,this.row=0,this.col=0,this.world=0;else if("object"==typeof e)this.level=e.level||0,this.row=e.row||0,this.col=e.col||0,this.world=e.world||0;else if("string"==typeof e){const[t,r,s,i]=e.split("/");this.level=parseFloat(t),this.row=parseFloat(r),this.col=parseFloat(s),this.world=parseFloat(i)}else this.level=+e,this.row=+t,this.col=+r,this.world=+s||0;return this}toString(){return`${this.level}/${this.row}/${this.col}/${this.world}`}getParentKey(){return this.level<=0?null:new i(this.level-1,this.row>>1,this.col>>1,this.world)}getChildKeys(){const e=this.level+1,t=this.row<<1,r=this.col<<1,s=this.world;return[new i(e,t,r,s),new i(e,t,r+1,s),new i(e,t+1,r,s),new i(e,t+1,r+1,s)]}compareRowMajor(e){return this.row<e.row?-1:this.row>e.row?1:this.col<e.col?-1:this.col>e.col?1:0}}i.pool=new s.Z(i,null,null,25,50)},54738:(e,t,r)=>{r.d(t,{Sh:()=>o,kr:()=>n,zO:()=>s});var s,i,l=r(80442);function o(e){const t=(0,l.Z)("esri-force-webgl");if(t===s.WEBGL1||t===s.WEBGL2)return[t];switch(e){case"2d":return(0,l.Z)("mac")&&(0,l.Z)("chrome")?[s.WEBGL1,s.WEBGL2]:[s.WEBGL2,s.WEBGL1];case"3d":return[s.WEBGL2,s.WEBGL1]}}function n(e,t,r={}){const i=t===s.WEBGL1?["webgl","experimental-webgl","webkit-3d","moz-webgl"]:["webgl2"];let l=null;for(const t of i){try{l=e.getContext(t,r)}catch(e){}if(l)break}return l}(i=s||(s={}))[i.WEBGL1=1]="WEBGL1",i[i.WEBGL2=2]="WEBGL2"}}]);