package org.thingsboard.server.dao.util.imodel.query.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.plan.PlanTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class PlanTaskSaveRequest extends ComplexSaveRequest<PlanTask, PlanTaskDetailSaveRequest> {
    // 任务名称
    @NotNullOrEmpty
    private String name;

    // 盘点类型
    private String executionType;

    // 盘点类型
    private String executionUserId;

    // 盘点目标仓库ID
    private String storehouseId;

    // 开始日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNullOrEmpty
    private Date startTime;

    // 结束日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNullOrEmpty
    private Date endTime;

    // 任务类型。临时任务/计划任务
    @NotNullOrEmpty
    private String type;

    // 备注
    private String remark;

    @Override
    public String valid(IStarHttpRequest request) {
        return checkItemExistence("至少应该存在一条计划项目");
    }

    @Override
    protected PlanTask build() {
        PlanTask entity = new PlanTask();
        entity.setCreateTime(new Date());
        entity.setCreator(currentUserUUID());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected PlanTask update(String id) {
        disallowUpdate();
        PlanTask entity = new PlanTask();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(PlanTask entity) {
        entity.setName(name);
        entity.setStatus(GeneralTaskStatus.PENDING);
        entity.setExecutionType(executionType);
        entity.setExecutionUserId(executionUserId);
        entity.setStorehouseId(storehouseId);
        entity.setStartTime(startTime);
        entity.setEndTime(endTime);
        entity.setType(type);
        entity.setRemark(remark);
    }

    @Override
    protected StringSetter<PlanTaskDetailSaveRequest> parentSetter() {
        return PlanTaskDetailSaveRequest::setMainId;
    }
}