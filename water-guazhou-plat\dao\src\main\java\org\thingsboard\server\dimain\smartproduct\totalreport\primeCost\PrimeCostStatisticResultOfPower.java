package org.thingsboard.server.dimain.smartproduct.totalreport.primeCost;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class PrimeCostStatisticResultOfPower {
    // 工厂名称
    private String factory;

    // 总电费
    private BigDecimal totalPrice;

    // 去年总电费
    private BigDecimal totalPriceLastYear;

    // 吨水成本
    private BigDecimal pricePerWater;

}
