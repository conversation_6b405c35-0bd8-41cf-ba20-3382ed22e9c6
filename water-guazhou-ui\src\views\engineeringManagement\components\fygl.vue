<!-- 工程管理-详情-合同基本信息 -->
<template>
  <el-card class="card">
    <descriptions :config="basicConfig"></descriptions>
  </el-card>
  <el-card class="card">
    <descriptions :config="htConfig"></descriptions>
  </el-card>
</template>

<script lang="ts" setup>
const props = defineProps<{ config: any }>();

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => props.config) as any,
  border: true,
  direction: 'horizontal',
  column: 2,
  title: '费用明细基本信息',
  fields: [
    { type: 'text', label: '资金编号:', field: 'code' },
    { type: 'text', label: '支付方式:', field: 'paymentType' },
    { type: 'text', label: '支付金额:', field: 'cost' },
    { type: 'text', label: '费用类型:', field: 'type' },
    { type: 'text', label: '一审审核金额(万元):', field: 'firstVerifyCost' },
    { type: 'text', label: '一审结算单位:', field: 'firstVerifyOrganization' },
    { type: 'text', label: '二审审核金额(万元):', field: 'secondVerifyCost' },
    { type: 'text', label: '二审结算单位:', field: 'secondVerifyOrganization' },
    { type: 'text', label: '代收款信息:', field: 'payeeInfo' },
    {
      type: 'text',
      label: '报批时间:',
      field: 'approvalTime',
      formatter: (row) => dayjs(row).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      type: 'text',
      label: '提交财务处理时间:',
      field: 'submitFinanceTime',
      formatter: (row) => dayjs(row).format('YYYY-MM-DD HH:mm:ss')
    },
    { type: 'text', label: '申请人:', field: 'creatorName' },
    { type: 'text', label: '收款单位:', field: 'payeeOrganization' },
    { type: 'text', label: '说明:', field: 'remark' },
    { type: 'text', label: '创建人:', field: 'creatorName' },
    { type: 'text', label: '创建时间:', field: 'createTimeName' },
    { type: 'text', label: '最后更新人:', field: 'updateUserName' },
    { type: 'text', label: '最后更新时间:', field: 'updateTimeName' }
  ]
});

const htConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => props.config) as any,
  border: true,
  direction: 'horizontal',
  column: 2,
  title: '所属合同信息',
  fields: [
    { type: 'text', label: '合同编号:', field: 'contractCode' },
    { type: 'text', label: '合同名称:', field: 'contractName' },
    { type: 'text', label: '合同类型:', field: 'contractTypeName' },
    { type: 'text', label: '合同金额(万元):', field: 'contractCost' },
    { type: 'text', label: '合同工期(开始时间):', field: 'workTimeBeginName' },
    { type: 'text', label: '合同工期(完成时间):', field: 'workTimeEndName' }
  ]
});
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}
</style>
