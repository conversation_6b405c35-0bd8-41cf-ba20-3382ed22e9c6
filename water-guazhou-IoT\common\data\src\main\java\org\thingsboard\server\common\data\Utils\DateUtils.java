/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.Utils;

import org.thingsboard.server.common.data.DataConstants;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtils {
    public static final String DATE_FORMATE_DAY = "yyyy-MM-dd";
    public static final String DATE_FORMATE_MONTH = "yyyy-MM";
    public static final String DATE_FORMATE_YEAR = "yyyy";
    public static final String DATE_FORMATE_DEFAULT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMATE_DEFAULT_2 = "yyyy/MM/dd HH:mm:ss";
    public static final String DATE_FORMATE_MINUTE = "yyyy-MM-dd HH:mm";
    public static final String DATE_FORMATE_HOUR = "yyyy-MM-dd HH";
    public static final String DATE_FORMATE_DATE = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    public static final String DAY = "day";
    public static final String MONTH = "month";
    public static final String YEAR = "year";
    public static final String HOUR = "hour";
    public static final String WEEK = "week";
    public static final String DATE = "date";
    public static final String THIRTY_MINUTE = "30m";
    public static final String FIFTEEN_MINUTE = "15m";
    public static final String TEN_MINUTE = "10m";
    public static final String FIVE_MINUTE = "5m";
    public static final String MINUTE = "1m";
    public static final String SECOND = "30s";

    public static final long DAY_TIME = 1000 * 60 * 60 * 24;
    public static final long MONTH_TIME = 1000 * 60 * 60;
    public static final long YEAR_TIME = 1000 * 60 * 60;
    public static final long FIFTEEN_MINUTE_TIMESTAMP = 1000 * 60 * 15;
    public static final long THIRTY_MINUTE_TIMESTAMP = 1000 * 60 * 30;

    public static final long DAY_TIME_S = 60 * 60 * 24;
    public static final long MONTH_TIME_S = 60 * 60;
    public static final long YEAR_TIME_S = 60 * 60;
    public static final long FIFTEEN_MINUTE_TIMESTAMP_S = 60 * 15;

    public static String date2Str(Date d, String format) {// yyyy-MM-dd HH:mm:ss
        if (d == null) {
            return null;
        }
        if (format == null || format.length() == 0) {
            format = DATE_FORMATE_DEFAULT;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String s = sdf.format(d);
        return s;
    }

    public static String date2StrByType(Long l, String type) {
        if (l.toString().length() < 13)
            l = l * 1000;
        Date date = new Date(l);
        switch (type) {
            case DateUtils.SECOND: {
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_DEFAULT);
            }
            case DateUtils.MINUTE:
            case DateUtils.FIVE_MINUTE:
            case DateUtils.TEN_MINUTE:
            case DateUtils.FIFTEEN_MINUTE:
            case DateUtils.THIRTY_MINUTE:
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_MINUTE);
            case "1h":
            case DateUtils.HOUR: {
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_HOUR);
            }
            case "1nc":
            case DateUtils.MONTH: {
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_MONTH);
            }
            case "1yc":
            case DateUtils.YEAR: {
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_YEAR);
            }
            default: {
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_DAY);
            }
        }
    }


    public static String date2Str(Long l, String format) {
        if (l.toString().length() < 13)
            l = l * 1000;
        Date date = new Date(l);
        return date2Str(date, format);
    }

    /**
     * 根据时间和抄表时间返回周几
     *
     * @param date
     * @param meterReadTime
     * @return
     */
    public static String getWeekday(String date, String meterReadTime) {// 必须yyyy-MM-dd
        int hour = 0;
        int minute = 0;
        if (meterReadTime != null) {
            hour = Integer.parseInt(meterReadTime.split(":")[0]);
            minute = Integer.parseInt(meterReadTime.split(":")[1]);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(str2Date(date, DATE_FORMATE_MINUTE));
        if (calendar.get(Calendar.HOUR_OF_DAY) < hour || (calendar.get(Calendar.HOUR_OF_DAY) == hour && calendar.get(Calendar.MINUTE) <= minute))
            calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 1);
        switch (calendar.get(Calendar.DAY_OF_WEEK)) {
            case 1:
                return "星期日";
            case 2:
                return "星期一";
            case 3:
                return "星期二";
            case 4:
                return "星期三";
            case 5:
                return "星期四";
            case 6:
                return "星期五";
            case 7:
                return "星期六";
        }
        return "星期一";
    }

    /**
     * 获取当前时间向前推进一个月的时间
     *
     * @param l
     * @return
     */
    public static long timeBeforeMonth(long l) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(l);
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        return calendar.getTime().getTime();
    }

    public static Date str2DateByLength(String str) {
        switch (str.length()) {
            case 4:
                return str2Date(str, DATE_FORMATE_YEAR);
            case 7:
                return str2Date(str, DATE_FORMATE_MONTH);
            case 10:
                return str2Date(str, DATE_FORMATE_DAY);
            case 13:
                return str2Date(str, DATE_FORMATE_HOUR);
            case 16:
                return str2Date(str, DATE_FORMATE_MINUTE);
            default:
                return str2Date(str, DATE_FORMATE_DEFAULT);
        }
    }

    public static Date str2Date(String str, String format) {
        if (str == null || str.length() == 0) {
            return null;
        }
        if (format == null || format.length() == 0) {
            format = DATE_FORMATE_DEFAULT;
        }
        Date date = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            date = sdf.parse(str);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return date;

    }


    public static Long nextDay(Long timeStamp) {
        return timeStamp + DAY_TIME;
    }

    public static Long lastDay(Long timestamp) {
        return timestamp - DAY_TIME;
    }


    //获取当前时间向前推进一年的时间
    public static long timeBeforeYear(long l) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(l);
        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) - 1);
        return calendar.getTime().getTime();
    }


    /**
     * 获取当前时间向前推进一天的时间
     *
     * @return
     */
    public static Long oneDayAgo(Date date) {
        return date.getTime() - DAY_TIME;
    }

    /**
     * 将时间转换为当天的0点0分0秒
     * 为了使每天的统计数据timestamp保持一致性，方便查询操作
     *
     * @param date
     * @return
     */
    public static Date format2Day(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 根据租户设定的抄表时间转换初始起始时间
     *
     * @param date
     * @param hour
     * @return
     */
    public static Date getMeterReadingTime(Date date, String hour, String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (hour != null) {
            String[] time = hour.split(":");
            switch (type) {
                case DAY: {
                    calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time[0]));
                    calendar.set(Calendar.MINUTE, Integer.parseInt(time[1]));
                    break;
                }
                case MONTH: {
                    calendar.set(Calendar.DAY_OF_MONTH, 1);
                    calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time[0]));
                    calendar.set(Calendar.MINUTE, Integer.parseInt(time[1]));
                    break;
                }
                case YEAR: {
                    calendar.set(Calendar.DAY_OF_YEAR, 1);
                    calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time[0]));
                    calendar.set(Calendar.MINUTE, Integer.parseInt(time[1]));
                    break;
                }
            }

        }
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }


    //获取自然月的天数
    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }


    //获取自然年的天数
    public static int getDaysOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_YEAR);
    }

    //获取自然年的天数
    public static int getDaysOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_WEEK);
    }

    public static List<Long> getTimesByWeek(Date date, int days) {
        List<Long> times = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        int count = 1;
        while (count <= days) {
            calendar.set(Calendar.DAY_OF_WEEK, count++);
            times.add(calendar.getTimeInMillis());
        }
        return times;
    }

    public static List<Long> getTimesByMouth(Date date, int days) {
        List<Long> times = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        int count = 1;
        while (count <= days) {
            calendar.set(Calendar.DAY_OF_MONTH, count++);
            times.add(calendar.getTimeInMillis());
        }
        return times;
    }

    public static List<Long> getTimesByYear(Date date, int days) {
        List<Long> times = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        int count = 1;
        while (count <= days) {
            calendar.set(Calendar.DAY_OF_YEAR, count++);
            times.add(calendar.getTimeInMillis());
        }
        return times;
    }


    /**
     * 获取某段时这里写代码片间内的所有日期
     *
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static ArrayList<String> findDates(long dBegin, long dEnd, String type) {
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(new Date(dBegin));
        calBegin.set(Calendar.MILLISECOND, 0);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(new Date(dEnd));
        // 测试此日期是否在指定日期之后
        ArrayList<String> array = new ArrayList<>();
        Boolean isFirst = true;
        while (calBegin.getTimeInMillis() < getNextTime(calEnd.getTimeInMillis(), type)) {
            if (isFirst)
                array.add(date2StrByType(dBegin, type));
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            switch (type) {
                case SECOND: {
                    calBegin.set(Calendar.SECOND, calBegin.get(Calendar.SECOND) + 30);
                    array.add(date2Str(calBegin.getTime(), DATE_FORMATE_DEFAULT));
                    break;
                }
                case MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 1);
                    array.add(date2Str(calBegin.getTime(), DATE_FORMATE_MINUTE));
                    break;
                }
                case FIVE_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 5);
                    array.add(date2Str(calBegin.getTime(), DATE_FORMATE_MINUTE));
                    break;
                }
                case TEN_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 10);
                    array.add(date2Str(calBegin.getTime(), DATE_FORMATE_MINUTE));
                    break;
                }
                case FIFTEEN_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 15);
                    array.add(date2Str(calBegin.getTime(), DATE_FORMATE_MINUTE));
                    break;
                }
                case THIRTY_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 30);
                    array.add(date2Str(calBegin.getTime(), DATE_FORMATE_MINUTE));
                    break;
                }
                case "1h":
                case HOUR: {
                    calBegin.set(Calendar.HOUR_OF_DAY, calBegin.get(Calendar.HOUR_OF_DAY) + 1);
                    break;
                }
                case "1nc":
                case MONTH: {
                    calBegin.set(Calendar.DAY_OF_MONTH, 1);
                    calBegin.set(Calendar.MONTH, calBegin.get(Calendar.MONTH) + 1);
                    break;
                }
                case "1yc":
                case YEAR: {
                    calBegin.set(Calendar.DAY_OF_YEAR, 1);
                    calBegin.set(Calendar.YEAR, calBegin.get(Calendar.YEAR) + 1);
                    break;
                }
                default: {
                    calBegin.set(Calendar.DAY_OF_YEAR, calBegin.get(Calendar.DAY_OF_YEAR) + 1);
                    break;
                }

            }
            if (!type.equalsIgnoreCase(FIFTEEN_MINUTE) && !type.equalsIgnoreCase(SECOND) && !type.equalsIgnoreCase(MINUTE) && !type.equalsIgnoreCase(FIVE_MINUTE) && !type.equalsIgnoreCase(TEN_MINUTE)
                    && calBegin.getTimeInMillis() < getNextTime(calEnd.getTimeInMillis(), type))
                array.add(date2StrByType(calBegin.getTimeInMillis(), type));
            isFirst = false;
        }
        return array;
    }


    /**
     * 获取某段时这里写代码片间内的所有日期
     *
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static ArrayList<Long> findTimeStamp(long dBegin, long dEnd, String type) {
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(new Date(dBegin));
        calBegin.set(Calendar.MILLISECOND, 0);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(new Date(dEnd));
        // 测试此日期是否在指定日期之后
        ArrayList<Long> array = new ArrayList<>();
        Boolean isFirst = true;
        while (calBegin.getTimeInMillis() < getNextTime(calEnd.getTimeInMillis(), type)) {
            if (isFirst)
                array.add(dBegin);
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            switch (type) {
                case SECOND: {
                    calBegin.set(Calendar.SECOND, calBegin.get(Calendar.SECOND) + 30);
                    array.add(calBegin.getTimeInMillis());
                    break;
                }
                case MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 1);
                    array.add(calBegin.getTimeInMillis());
                    break;
                }
                case FIVE_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 5);
                    array.add(calBegin.getTimeInMillis());
                    break;
                }
                case TEN_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 10);
                    array.add(calBegin.getTimeInMillis());
                    break;
                }
                case FIFTEEN_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 15);
                    array.add(calBegin.getTimeInMillis());
                    break;
                }
                case HOUR: {
                    calBegin.set(Calendar.HOUR_OF_DAY, calBegin.get(Calendar.HOUR_OF_DAY) + 1);
                    break;
                }
                case "1nc":
                case MONTH: {
                    calBegin.set(Calendar.DAY_OF_MONTH, 1);
                    calBegin.set(Calendar.MONTH, calBegin.get(Calendar.MONTH) + 1);
                    break;
                }
                case "1yc":
                case YEAR: {
                    calBegin.set(Calendar.DAY_OF_YEAR, 1);
                    calBegin.set(Calendar.YEAR, calBegin.get(Calendar.YEAR) + 1);
                    break;
                }
                default: {
                    calBegin.set(Calendar.DAY_OF_YEAR, calBegin.get(Calendar.DAY_OF_YEAR) + 1);
                    break;
                }

            }
            if (!type.equalsIgnoreCase(FIFTEEN_MINUTE) && !type.equalsIgnoreCase(SECOND) && !type.equalsIgnoreCase(MINUTE) && !type.equalsIgnoreCase(FIVE_MINUTE) && !type.equalsIgnoreCase(TEN_MINUTE)
                    && calBegin.getTimeInMillis() < getNextTime(calEnd.getTimeInMillis(), type))
                array.add(calBegin.getTimeInMillis());
            isFirst = false;
        }
        return array;
    }

    /**
     * 根据类型为结束时间做限制
     *
     * @param time
     * @param type
     * @return
     */
    public static Long getNextTime(long time, String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        calendar.set(Calendar.MILLISECOND, 0);
        switch (type) {
            case SECOND: {
                return str2Date(TimeDiff.getTrulyMinute(time, type), DATE_FORMATE_DEFAULT).getTime();
            }
            case MINUTE:
            case FIVE_MINUTE:
            case TEN_MINUTE:
            case FIFTEEN_MINUTE:
                return str2Date(TimeDiff.getTrulyMinute(time, type), DATE_FORMATE_MINUTE).getTime();
            case HOUR: {
                if (calendar.get(Calendar.MINUTE) != 0 && calendar.get(Calendar.SECOND) != 0 && calendar.get(Calendar.MILLISECOND) != 0) {
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) + 1);
                }
                return calendar.getTimeInMillis();
            }
            case "1d":
            case DAY: {
                if (calendar.get(Calendar.MINUTE) != 0 && calendar.get(Calendar.SECOND) != 0 && calendar.get(Calendar.MILLISECOND) != 0 && calendar.get(Calendar.HOUR_OF_DAY) != 0) {
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + 1);
                }
                return calendar.getTimeInMillis() - 1;
            }
            case "1nc":
            case MONTH: {
                if (calendar.get(Calendar.MINUTE) != 0 && calendar.get(Calendar.SECOND) != 0 && calendar.get(Calendar.MILLISECOND) != 0 && calendar.get(Calendar.HOUR_OF_DAY) != 0
                        && calendar.get(Calendar.DAY_OF_MONTH) != 0) {
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.DAY_OF_MONTH, 0);
                    calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
                }
                return calendar.getTimeInMillis() - 1;
            }
            case "1yc":
            case YEAR: {
                if (calendar.get(Calendar.MINUTE) != 0 && calendar.get(Calendar.SECOND) != 0 && calendar.get(Calendar.MILLISECOND) != 0 && calendar.get(Calendar.HOUR_OF_DAY) != 0
                        && calendar.get(Calendar.DAY_OF_YEAR) != 0) {
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.DAY_OF_YEAR, 0);
                    calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + 1);
                }
                return calendar.getTimeInMillis() - 1;
            }
            default: {
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) + 1);
                return calendar.getTimeInMillis() - 1;
            }

        }
    }

    /**
     * 获取当年的第一天
     *
     * @return
     */
    public static Date getCurrYearFirst() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearFirst(currentYear);
    }

    /**
     * 获取当年的最后一天
     *
     * @return
     */
    public static Date getCurrYearLast() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearLast(currentYear);
    }

    /**
     * 获取某年第一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static Date getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }

    /**
     * 获取某年最后一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static Date getYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        Date currYearLast = calendar.getTime();

        return currYearLast;
    }


    public static void main(String[] args) {
        findDates(Long.parseLong("1538323200000"), Long.parseLong("1541001600000"), "15m").forEach(str -> {
            //System.out.println(str);
        });
    }

}
