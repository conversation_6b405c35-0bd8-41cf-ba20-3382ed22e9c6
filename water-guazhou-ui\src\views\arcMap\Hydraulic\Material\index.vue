<!-- 材质 -->
<template>
  <RightDrawerMap :title="'材质'">
    <HydraulicPanel
      :header="['材质', '图层控制', '定位']"
      :legends="[
        { label: '铸铁', value: 14, checked: true },
        { label: 'PE', value: 3232, checked: true },
        { label: 'PVC', value: 1431, checked: true },
        { label: '砼', value: 0, checked: true }
      ]"
      :unit="'个'"
    ></HydraulicPanel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import HydraulicPanel from '../components/HydraulicPanel.vue'
</script>
<style lang="scss" scoped></style>
