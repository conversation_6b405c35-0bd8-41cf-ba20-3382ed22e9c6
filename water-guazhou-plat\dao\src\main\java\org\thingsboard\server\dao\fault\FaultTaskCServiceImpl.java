package org.thingsboard.server.dao.fault;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.fault.FaultTaskC;
import org.thingsboard.server.dao.model.sql.fault.FaultTaskM;
import org.thingsboard.server.dao.sql.fault.FaultTaskCMapper;
import org.thingsboard.server.dao.sql.fault.FaultTaskMMapper;
import org.thingsboard.server.dao.util.imodel.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class FaultTaskCServiceImpl implements FaultTaskCService {

    @Autowired
    private FaultTaskCMapper faultTaskCMapper;

    @Autowired
    private FaultTaskMMapper faultTaskMMapper;

    @Override
    public void save(FaultTaskC faultTaskC) {
        faultTaskC.setTime(new Date());
        faultTaskCMapper.updateById(faultTaskC);
        // 是否全部完成
        Map queryMap = new HashMap<>();
        queryMap.put("main_id", faultTaskC.getMainId());
        List<FaultTaskC> list = faultTaskCMapper.selectByMap(queryMap);
        boolean isComplete = true;
        for (FaultTaskC faultTaskC1 : list) {
            if ("0".equals(faultTaskC1.getStatus())) {
                isComplete = false;
            }
        }
        // 是否第一次，第一次则设置实际开始时间
        FaultTaskM faultTaskM = faultTaskMMapper.selectById(faultTaskC.getMainId());
        if (faultTaskM != null) {
            faultTaskM.setRealStartTime(new Date());
            // 接收任务
            faultTaskM.setStatus("1");

            // 全部完成  任务状态 0 待接收 1已接收 2按时完成 3超时完成 4未完成
            if (true == isComplete) {
                // 是否按时完成
                faultTaskM.setRealEndTime(new Date());
                if (faultTaskM.getEndTime() != null && faultTaskM.getEndTime().getTime() < faultTaskM.getRealEndTime().getTime()) {
                    faultTaskM.setStatus("3");
                } else {
                    faultTaskM.setStatus("2");
                }
            }
            faultTaskMMapper.updateById(faultTaskM);
        }
    }

    @Override
    public boolean checkUser(String mainId, String userId) {
        FaultTaskM faultTaskM = faultTaskMMapper.selectById(mainId);
        if (faultTaskM == null || StringUtils.isNullOrEmpty(faultTaskM.getUserId()) || !faultTaskM.getUserId().contains(userId)) {
            return false;
        }
        return true;
    }
}
