package org.thingsboard.server.controller.leakDetectionManagement;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMount;
import org.thingsboard.server.dao.smartPipe.PartitionMountService;
import org.thingsboard.server.dao.smartPipe.PartitionService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 用水分析控制器
 * 实现自动统计各分区的用户用水量数据，分析用水异常的用户数据，判断是否存在流量异常及表计异常
 */
@RestController
@RequestMapping("/api/leakDetection/waterAnalysis")
public class waterAnalysisController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(waterAnalysisController.class);

    @Autowired
    private PartitionService partitionService;
    
    @Autowired
    private DeviceService deviceService;

    @Autowired
    private PartitionMountService partitionMountService;

    /**
     * 获取用水分析列表
     * @param partitionId 分区ID
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页数据
     */
    @GetMapping("/list")
    public IstarResponse getWaterAnalysisList(
            @RequestParam(required = false) String partitionId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize
    ) {
        try {
            // 获取分区列表
            List<Partition> partitions = getPartitions(partitionId);
            
            // 生成分析数据
            List<WaterAnalysisDto> allData = generateAnalysisData(partitions);
            
            // 分页处理
            PageData<WaterAnalysisDto> pageData = paginateData(allData, page, pageSize);
            
            return IstarResponse.ok(pageData);
        } catch (Exception e) {
            return IstarResponse.error("获取用水分析数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取异常详情数据
     * @param partitionId 分区ID
     * @param type 异常类型：flow(流量异常) 或 meter(表计异常)
     * @param page 页码
     * @param pageSize 每页大小
     * @return 异常详情分页数据
     */
    @GetMapping("/abnormalDetail")
    public IstarResponse getAbnormalDetail(
            @RequestParam String partitionId,
            @RequestParam String type,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize
    ) {
        try {
            // 获取分区信息
            Partition partition = null;
            try {
                partition = partitionService.getById(partitionId);
                if (partition == null) {
                    logger.warn("No partition found with id: {}", partitionId);
                    return IstarResponse.ok(new PageData<>(0, new ArrayList<>()));
                }
            } catch (Exception e) {
                logger.error("Failed to get partition by id: {}", partitionId, e);
                return IstarResponse.ok(new PageData<>(0, new ArrayList<>()));
            }
            
            // 查询分区下的设备数量
            PartitionMountRequest request = new PartitionMountRequest();
            request.setPartitionId(partition.getId());
            request.setType("1"); // 流量计类型
            request.setPage(1);
            request.setSize(1000);
            request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
            
            PageData<PartitionMount> mountDevices = partitionMountService.getList(request);
            int totalDevices = (int) mountDevices.getTotal();
            
            // 如果设备数量大于2个，则展示一条模拟数据
            if (totalDevices > 2) {
                if ("flow".equals(type)) {
                    // 流量异常模拟数据
                    List<FlowAbnormalDetailDto> detailList = new ArrayList<>();
                    FlowAbnormalDetailDto dto = new FlowAbnormalDetailDto();
                    dto.setMeterId("WM" + partition.getId().substring(0, Math.min(3, partition.getId().length())) + "10001");
                    dto.setUserName("用户1");
                    dto.setAddress(partition.getName() + "区域第1小区1号");
                    dto.setAbnormalType("流量突增");
                    dto.setCurrentFlow("5.20");
                    dto.setAvgFlow("2.10");
                    dto.setAbnormalLevel(2);
                    detailList.add(dto);
                    
                    return IstarResponse.ok(new PageData<>(1, detailList));
                } else if ("meter".equals(type)) {
                    // 表计异常模拟数据
                    List<MeterAbnormalDetailDto> detailList = new ArrayList<>();
                    MeterAbnormalDetailDto dto = new MeterAbnormalDetailDto();
                    dto.setMeterId("WM" + partition.getId().substring(0, Math.min(3, partition.getId().length())) + "20001");
                    dto.setUserName("用户2");
                    dto.setAddress(partition.getName() + "区域第1小区2号");
                    dto.setAbnormalType("电池电量低");
                    dto.setBatteryLevel("10%");
                    dto.setSignalStrength("75%");
                    dto.setAbnormalLevel(1);
                    detailList.add(dto);
                    
                    return IstarResponse.ok(new PageData<>(1, detailList));
                }
            }
            
            // 分区下无设备或设备数量不足，返回空列表
            return IstarResponse.ok(new PageData<>(0, new ArrayList<>()));
        } catch (Exception e) {
            logger.error("Error getting abnormal detail", e);
            return IstarResponse.error("获取异常详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取分区列表
     */
    private List<Partition> getPartitions(String partitionId) {
        List<Partition> partitions = new ArrayList<>();
        
        if (StringUtils.isNotEmpty(partitionId)) {
            // 如果指定了分区ID，只返回该分区
            try {
                logger.info("Querying partition by id: {}", partitionId);
                Partition partition = partitionService.getById(partitionId);
                if (partition != null) {
                    partitions.add(partition);
                    logger.info("Found partition: {}", partition.getName());
                } else {
                    logger.warn("No partition found with id: {}", partitionId);
                }
            } catch (Exception e) {
                // 查询失败，记录日志
                logger.error("Failed to get partition by id: {}", partitionId, e);
            }
        } else {
            // 否则返回所有分区
            try {
                // 从数据库获取分区列表
                Map<String, Object> params = new HashMap<>();
                String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
                params.put("tenantId", tenantId);
                logger.info("Querying all partitions with tenantId: {}", tenantId);
                partitions = partitionService.getAll(params);
                logger.info("Found {} partitions", partitions.size());
                
                // 如果没有获取到分区，则尝试获取根分区
                if (partitions.isEmpty()) {
                    logger.info("No partitions found, trying to get root partitions");
                    List<Partition> rootPartitions = partitionService.getRootIdNameList(tenantId);
                    if (rootPartitions != null && !rootPartitions.isEmpty()) {
                        partitions = rootPartitions;
                        logger.info("Found {} root partitions", partitions.size());
                    }
                }
            } catch (Exception e) {
                // 查询失败，记录日志
                logger.error("Failed to get all partitions", e);
            }
        }
        
        return partitions;
    }
    
    /**
     * 生成分析数据，查询分区下的流量设备列表，后续可改为水表类型设备
     */
    private List<WaterAnalysisDto> generateAnalysisData(List<Partition> partitions) {
        List<WaterAnalysisDto> result = new ArrayList<>();
        
        // 为每个分区生成分析数据
        for (Partition partition : partitions) {
            WaterAnalysisDto dto = new WaterAnalysisDto();
            dto.setPartitionId(partition.getId());
            dto.setPartitionName(partition.getName());
            
            // 获取分区下的设备数量作为用户总数
            int totalUsers = 0;
            try {
                // 使用 PartitionMountService 查询分区下的设备列表
                PartitionMountRequest request = new PartitionMountRequest();
                request.setPartitionId(partition.getId());
                request.setType("1"); // 流量计类型
                request.setPage(1);
                request.setSize(1000);
                request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
                
                logger.info("Querying devices for partition: {}, id: {}", partition.getName(), partition.getId());
                PageData<PartitionMount> mountDevices = partitionMountService.getList(request);
                
                if (mountDevices != null) {
                    totalUsers = (int) mountDevices.getTotal();
                    logger.info("Found {} devices for partition: {}", totalUsers, partition.getName());
                }
            } catch (Exception e) {
                logger.error("Failed to get devices for partition: {}", partition.getId(), e);
                // 查询失败，使用0
                totalUsers = 0;
            }
            
            dto.setTotalUsers(totalUsers);
            
            // 如果没有用户，则所有异常数据都为0
            if (totalUsers == 0) {
                dto.setAbnormalWaterUsers(0);
                dto.setNormalUsers(0);
                dto.setAbnormalFlow(0);
                dto.setAbnormalMeter(0);
            } else {
                //假数据
                if(totalUsers > 2){
                    //用水异常用户暂时设置等于流量异常用户+表计异常用户
                    dto.setAbnormalWaterUsers(2);
                    //流量异常用户
                    dto.setAbnormalFlow(1);
                    //表计异常用户
                    dto.setAbnormalMeter(1);
                    //正常用户
                    dto.setNormalUsers(totalUsers - 2);
                }
            }
            
            result.add(dto);
        }
        
        return result;
    }
    
    /**
     * 分页处理
     */
    private PageData<WaterAnalysisDto> paginateData(List<WaterAnalysisDto> allData, int page, int pageSize) {
        int total = allData.size();
        int start = (page - 1) * pageSize;
        int end = Math.min(start + pageSize, total);
        
        if (start >= total) {
            return new PageData<>(0, new ArrayList<>());
        }
        
        List<WaterAnalysisDto> pageData = allData.subList(start, end);
        return new PageData<>(total, pageData);
    }
}
