package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.dataSource.BaseDataSource;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2020/2/26 10:51
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DATASOURCE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class DataSourceEntity {

    @Id
    private String id;
    /**
     * 数据源类型
     * 设备数据源
     * 统计数据源
     * 中间数据源
     * 系统数据源
     * API数据源
     */
    @Column(name = ModelConstants.ALARM_TYPE_PROPERTY)
    private String type;
    /**
     * 数据源名称
     */
    @Column(name = ModelConstants.DATASOURCE_NAME)
    private String sourceName;
    /**
     * 是否启用 默认为1启用
     */
    @Column(name = ModelConstants.DATASOURCE_ENABLE)
    private int enable;

    /**
     * 数据源排序（默认-1）
     */
    @Column(name = ModelConstants.DATASOURCE_ORDER)
    private long order = -1;
    /**
     * 备注
     */
    @Column(name = ModelConstants.DATASOURCE_FORMAT)
    private String format;

    /**
     * 数据类型-数值/布尔
     */
    @Column(name = ModelConstants.DATASOURCE_DATATYPE)
    private String dataType;

    /**
     * 最后一次更新时间
     */
    @Column(name = ModelConstants.DATASOURCE_UPDATE_TIME)
    private long updateTime;

    @Column(name = ModelConstants.ADDITIONAL_INFO_PROPERTY)
    private String additionalInfo;

    @Column(name = ModelConstants.VALUE_COLUMN)
    private String value;


    /* ------------------------  设备数据源 ----------------------------*/

    /**
     * 设备数据源-设备ID
     */
    @Column(name = ModelConstants.DATASOURCE_DEVICE_ID)
    private String deviceId;

    /**
     * 设备数据源-属性
     */
    @Column(name = ModelConstants.DATASOURCE_PROPERTY)
    private String property;

    /**
     * 设备数据源-数据类型(累计值-瞬时值)
     */
    @Column(name = ModelConstants.DATASOURCE_DEVICE_STATISTICS)
    private String statisticsType;

    /* ------------------------  API数据源 ----------------------------*/

    /**
     * API数据源-URL
     */
    @Column(name = ModelConstants.DATASOURCE_API_URL)
    private String URL;
    /**
     * API数据源-方法
     */
    @Column(name = ModelConstants.DATASOURCE_API_METHOD)
    private String method;
    /**
     * API数据源-传递参数列表
     */
    @Column(name = ModelConstants.DATASOURCE_API_PARAMS)
    private String params;
    /**
     * API数据源-解析数据名
     */
    @Column(name = ModelConstants.DATASOURCE_PARSING_ATTRIBUTE)
    private String parsingAttribute;
    /**
     * API数据源-解析数据路径
     */
    @Column(name = ModelConstants.DATASOURCE_PARSING_PATH)
    private String parsingPath;


    /* ------------------------  统计数据源 ----------------------------*/

    /**
     * 统计数据源-公式
     */
    @Column(name = ModelConstants.DATASOURCE_STATISTICS_FORMULA)
    private String formula;
    /**
     * 统计数据源-统计模板
     */
    @Column(name = ModelConstants.DATASOURCE_STATISTICS_TEMPLATE)
    private String template;
    /**
     * 统计数据源-统计频率
     */
    @Column(name = ModelConstants.DATASOURCE_STATISTICS_FREQUENCY)
    private String frequency;

    /**
     * 统计数据源-预设统计值
     */
    @Column(name = ModelConstants.DATASOURCE_STATISTICS_PREPARATION)
    private String preparation;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.ENERGY_UNIT)
    private String unit;

    @Transient
    private String projectId;

    @Transient
    private String nodeId;

    @Transient
    private String nodeType = "DataSource";

    @Transient
    private String name = sourceName;

    @Transient
    private String deviceName;

    public String getName() {
        return sourceName;
    }

}
