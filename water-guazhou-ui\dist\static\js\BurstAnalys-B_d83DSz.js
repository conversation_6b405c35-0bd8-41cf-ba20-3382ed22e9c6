import{_ as J}from"./Panel-DyoxrWMd.js";import{c as S,Q as O,X as q,W,b as F,d as z,r as N,g as Q,h as $,F as B,q as V,i as G,p as H,_ as X,aq as K,C as Y}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import{s as P}from"./ToolHelper-BiiInOzB.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import Z from"./RightDrawerMap-D5PhmGFO.js";import{s as E,d as ee,h as te}from"./FeatureHelper-Da16o0mu.js";import{s as ae,a as re}from"./GPHelper-fLrvVD-A.js";import{e as ie,g as U,a as R}from"./LayerHelper-Cn-iiqxI.js";import{a as A,i as M}from"./QueryHelper-ILO3qZqg.js";import{GetFieldConfig as oe}from"./fieldconfig-Bk3o1wi7.js";import{i as se,e as le}from"./IdentifyHelper-RJWmLn49.js";import{u as ne}from"./useWaterPoint-Bv0z6ym6.js";import{b as ue}from"./URLHelper-B9aplt5w.js";import{g as me}from"./gisUser-Ba96nctf.js";import"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./identify-4SBo5EZk.js";const pe=()=>{const f=S(!1),y=S(!1),a=S([]),L=S([]),e={mustShutVolveFeatures:[]},o=S({data:[],columns:[]}),u=S({handleSelectChange:r=>{var l,p;if(!e.view)return;u.value.selectList=r||[];const t=r.map(s=>s.SID),i=e.mustShutVolveFeatures.filter(s=>t.indexOf(s.attributes.SID)!==-1).map(s=>(s.symbol=E("point",{color:[0,255,255],outlineColor:[255,0,255],outlineWidth:2}),s));(l=e.mustShutValveLayer)==null||l.removeAll(),(p=e.mustShutValveLayer)==null||p.addMany(i)},handleRowClick:r=>{var i;const t=e.mustShutVolveFeatures.find(l=>l.attributes.SID===r.SID);t&&((i=e.view)==null||i.goTo(t))},dataList:[],columns:[{label:"编号",prop:"SID"},{label:"阀门级别",prop:"VALVECLASS"}],pagination:{hide:!0}}),m=async(r,t,i,l)=>{var p,s,g,I,w,b,_;if(r){e.view=r;try{e.resultLayer&&((p=e.view)==null||p.map.remove(e.resultLayer));let c;if(t){y.value=!0;const d=typeof l=="string"?l:l==null?void 0:l.join(",");if(!(d!=null&&d.length)){y.value=!1;return}c=await re({bysource:!0,usertoken:W().gToken,valves:"valve"+":"+(d||"")})}else{if(i===void 0){f.value=!1;return}f.value=!0;const T=(g=(s=(await q(i)).data)==null?void 0:s.result)==null?void 0:g.rows,k=(T==null?void 0:T.length)&&T[0].layerdbname;c=await ae(k,l)}if(await c.waitForJobCompletion(),c.jobStatus==="job-succeeded"){e.resultLayer&&e.view.map.remove(e.resultLayer),e.resultLayer=await c.fetchResultMapImageLayer(c.jobId),e.resultLayer.title=t?"扩展分析结果":"爆管分析结果";const d=ie(e.view);(I=e.view)==null||I.map.add(e.resultLayer,d);const k=(await c.fetchResultData("summary")).value;(k==null?void 0:k.code)!==1e4?F.error(k.error):(e.resultSummary=((w=k==null?void 0:k.result)==null?void 0:w.summary)||[],n(),a.value=((b=e.resultSummary)==null?void 0:b.layersummary.map(j=>({label:j.layername,name:j.layername,data:[]})))||[],L.value.length=0,await v(a.value,0),await D(),e.mustShutValveLayer=U(e.view,{id:"mustShutValveLayer",title:"必关阀"}),(_=e.mustShutValveLayer)==null||_.addMany(e.mustShutVolveFeatures))}else c.jobStatus==="job-cancelled"?F.info("已取消分析"):c.jobStatus==="job-cancelling"?F.info("任务正在取消"):c.jobStatus==="job-failed"&&F.info("分析失败，请联系管理员")}catch{F.info("分析失败，请联系管理员")}f.value=!1,y.value=!1}},n=()=>{var t;const r=e.resultSummary;if((t=r==null?void 0:r.layersummary)!=null&&t.length){const i={},l=[];r.layersummary.forEach(p=>{i[p.layerdbname]=p.geometrytype==="esriGeometryPoint"?p.count+"个":p.length+"米",l.push([{label:p.layername,prop:p.layerdbname}])}),o.value.data=i,o.value.columns=l}},v=async(r,t)=>{if(t<r.length){const i=r[t];i.data=await h(i.name),t<r.length-1&&await v(r,++t)}},h=async r=>{var t,i,l,p,s,g;try{const I=(l=(i=(t=e.resultSummary)==null?void 0:t.layersummary)==null?void 0:i.find(d=>d.layername===r))==null?void 0:l.geometrytype,w=I==="esriGeometryPolyline"?1:I==="esriGeometryPoint"?0:-1,b=await A((((p=e.resultLayer)==null?void 0:p.url)||"")+"/"+w,M({where:"layername='"+r+"'",outFields:I==="esriGeometryPoint"?["sourceoid","mustshut"]:["sourceoid"],returnGeometry:!1})),_=(s=b.features)==null?void 0:s.filter(d=>d.attributes.mustshut===1).map(d=>d.attributes.sourceoid);L.value=L.value.concat(..._);let c=(g=b.features)==null?void 0:g.map(d=>d.attributes.sourceoid);return c===null&&(c=await h(r)),c}catch{return[]}},D=async()=>{var r,t,i,l,p;if(e.view){f.value&&((r=e.mustShutValveLayer)==null||r.removeAll());try{u.value.loading=!0;const s=[];if(L.value.length){const g=R(e.view,void 0,void 0,"阀门"),I=await oe("阀门"),w=await A(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+g[0],M({objectIds:L.value,outFields:((l=(i=(t=I.data)==null?void 0:t.result)==null?void 0:i.rows)==null?void 0:l.filter(b=>b.visible).map(b=>b.name))||[],returnGeometry:!0}));(p=w.features)==null||p.map(b=>{s.push({...b.attributes||{},layerId:g[0]}),b.symbol=E("point",{color:[0,255,255],outlineColor:[255,0,255],outlineWidth:2})}),e.mustShutVolveFeatures=w.features}else e.mustShutVolveFeatures=[];u.value.dataList=s,u.value.selectList=s}catch(s){F.error("必关阀查询失败"),console.dir(s)}u.value.loading=!1}},x=()=>{var r,t;e.resultLayer&&((r=e.view)==null||r.map.remove(e.resultLayer)),e.mustShutValveLayer&&((t=e.view)==null||t.map.remove(e.mustShutValveLayer)),e.mustShutVolveFeatures.length=0,e.resultSummary=void 0,e.view=void 0,o.value.data=[],u.value.dataList=[],u.value.selectList=[]},C=r=>{e.resultLayer&&(e.resultLayer.visible=!!r)};return O(()=>{x()}),{init:m,destroy:x,tabs:a,isAnalys:f,isExtendAnalys:y,TableConfig_EffectRange:o,TableConfig_MustShut:u,toggleResultLayer:C,staticState:e}},ce=()=>{const f=S(!1),y=ne("viewDiv"),a={},L=m=>{var n;u(),a.view=m,y.watchExtent(a.view),P("crosshair"),f.value=!0,a.markLayer=U(a.view,{id:"burst-analys",title:"爆管标注"}),a.mapClick=(n=a.view)==null?void 0:n.on("click",async v=>{var h;(h=a.markLayer)==null||h.removeAll(),await e(v)})},e=async m=>{var n,v,h,D,x,C,r,t,i;if(a.view)try{const l=se();l.layerIds=R(a.view,!0),l.geometry=m.mapPoint,l.mapExtent=a.view.extent;const p=await le(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,l);(n=a.markLayer)==null||n.removeAll();const s=(v=p.results)==null?void 0:v.filter(_=>{var c,d;return((d=(c=_.feature)==null?void 0:c.geometry)==null?void 0:d.type)==="polyline"});if(!(s!=null&&s.length)){F.warning("没有查询到管线"),f.value=!1;return}a.identifyResult=s[0];const g=s&&ee((D=(h=s[0])==null?void 0:h.feature)==null?void 0:D.geometry,m.mapPoint),I=g&&te(g.x,g.y,{picUrl:ue("poi_burst.png"),spatialReference:(x=a.view)==null?void 0:x.spatialReference,yOffset:8}),w=s[0].feature;w&&(w.symbol=E(w.geometry.type)),s.length&&((C=a.markLayer)==null||C.add(w)),y.add(a.view,{id:w.attributes.OBJECTID,point:g},{color:"#ff0000"}),(r=a.markLayer)==null||r.add(I),P(""),f.value=!1,(t=a.mapClick)==null||t.remove(),a.mapClick=void 0;const b=s.map(_=>{var c,d,T;return{layerName:_.layerName,layerId:_.layerId,value:(c=_.feature.attributes)==null?void 0:c.OBJECTID,SID:(d=_.feature.attributes)==null?void 0:d.新编号,attributes:(T=_.feature)==null?void 0:T.attributes}})||[];o.value.dataList=b.length&&[b[0]]||[]}catch{o.value.dataList=[],(i=a.markLayer)==null||i.removeAll(),f.value=!1}},o=S({columns:[{label:"管线类型",prop:"layerName"},{label:"管线编号",prop:"SID"}],dataList:[],pagination:{hide:!0}}),u=()=>{var m,n;P(""),o.value.dataList=[],(m=a.mapClick)==null||m.remove(),a.markLayer&&((n=a.view)==null||n.map.remove(a.markLayer)),a.mapClick=void 0,a.identifyResult=void 0,a.view=void 0,y.destroy()};return O(()=>{u()}),{init:L,destroy:u,isPicking:f,TableConfig:o,staticState:a}},de=()=>{const f=S(!1),y={devicelayers:["阀门","计量装置"],deviceids:[]},a=S({columns:[{prop:"yhbh",label:"用户编号"},{prop:"yhxm",label:"用户姓名"},{prop:"yhdz",label:"用户地址"},{prop:"lxdh",label:"联系电话"},{prop:"dxdh",label:"短信电话"},{prop:"ysxz",label:"用水性质"},{prop:"vnum",label:"阀门编号"},{prop:"sbbh",label:"水表编号"}],dataList:[],pagination:{hide:!0}}),L=async(u,m,n)=>{if(u){f.value=!0;try{y.deviceids=[],await e(0,m);const v=await me(y.deviceids);a.value.dataList=v.data.Data.rows,n==null||n()}catch{F.error("暂无相关用户信息")}f.value=!1}},e=async(u,m)=>{var v;const n=y.devicelayers[u];if(!(!y.view||!n))try{const h=await R(y.view,void 0,void 0,n),D=(v=m.find(C=>C.name===n))==null?void 0:v.data;(await A(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+h,M({outFields:["SID"],objectIds:D,returnGeometry:!1}))).features.map(C=>{y.deviceids.push(C.attributes.SID)}),u<y.devicelayers.length-1&&await e(++u,m)}catch(h){console.dir(h)}},o=()=>{y.view=void 0,f.value=!1};return O(()=>{o()}),{init:L,destroy:o,loading:f,TableConfig_User:a}},ye={class:"table-box"},fe=z({__name:"BurstAnalys",setup(f){const y=S(),a=S(),L=S(),e=ce(),o=pe(),u=de(),m=N({viewingDetail:!1,detailUrl:"",curOperate:""}),n={deviceids:[],devicelayers:["阀门","计量装置"]},v=N({dataList:[],columns:[],pagination:{hide:!0,refreshData:({page:t,size:i})=>{v.pagination.page=t,v.pagination.limit=i,D()}}}),h=N({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"选取管线"},fields:[{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},type:"warning",text:()=>e.isPicking.value===!0?"正在选取管线":"点击选取管线",loading:()=>e.isPicking.value===!0,click:()=>e.init(n.view)}]},{type:"table",style:{height:"80px"},config:e.TableConfig.value}]},{fieldset:{desc:"执行分析"},fields:[{type:"btn-group",itemContainerStyle:{marginBottom:0},btns:[{perm:!0,styles:{width:"100%"},loading:()=>o.isAnalys.value===!0,text:()=>o.isAnalys.value===!0?"正在分析":"开始分析",disabled:()=>!e.TableConfig.value.dataList.length,click:()=>{var t;return o.init(n.view,!1,e.TableConfig.value.dataList[0].layerId,(t=e.staticState.identifyResult)==null?void 0:t.feature.attributes.OBJECTID)}}]}]},{fieldset:{desc:"分析结果"},fields:[{label:"影响范围概览",type:"checkbox",field:"showInMap",options:[{label:"地图显示",value:"show"}],onChange:t=>{o.staticState.resultLayer&&(o.staticState.resultLayer.visible=!!t.length)}},{type:"attr-table",style:{minHeight:"50px"},config:o.TableConfig_EffectRange.value},{type:"table",label:"必关阀",style:{height:"250px"},config:o.TableConfig_MustShut.value},{type:"btn-group",itemContainerStyle:{marginBottom:"5px",marginTop:"15px"},btns:[{perm:!0,styles:{width:"100%"},disabled:()=>{var t;return!((t=o.TableConfig_EffectRange.value.columns)!=null&&t.length)},loading:()=>m.viewingDetail===!0,text:()=>m.viewingDetail===!0?"正在查询...":"查看详细结果",click:()=>D()}]},{type:"btn-group",itemContainerStyle:{marginBottom:"5px"},btns:[{perm:!1,styles:{width:"100%"},text:()=>m.curOperate==="userDetailing"?"正在查询":"查看受影响用户",loading:()=>m.curOperate==="userDetailing",disabled:()=>{var t;return!((t=o.TableConfig_EffectRange.value.columns)!=null&&t.length)},click:()=>u.init(n.view,o.tabs.value,()=>{var t;(t=a.value)==null||t.Open()})}]},{type:"btn-group",itemContainerStyle:{marginBottom:"5px"},btns:[{perm:!0,styles:{width:"100%"},loading:()=>o.isExtendAnalys.value,disabled:()=>{var t;return!((t=o.TableConfig_MustShut.value.selectList)!=null&&t.length)},text:()=>o.isExtendAnalys.value===!0?"正在分析":"二次关阀分析",click:()=>{var t;return o.init(n.view,!0,void 0,((t=o.TableConfig_MustShut.value.selectList)==null?void 0:t.map(i=>i.OBJECTID))||[])}}]},{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},type:"danger",disabled:()=>["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(m.curOperate)!==-1,text:"清除所有",click:()=>x()}]}]}],defaultValue:{showInMap:["show"]}}),D=async()=>{var t;(t=L.value)==null||t.refreshDetail(o.tabs.value)},x=()=>{P(""),o.destroy(),e.destroy(),u.destroy()},C=()=>{var t;x(),(t=n.view)==null||t.destroy(),n.view=void 0},r=t=>{n.view=t};return O(()=>{C()}),(t,i)=>{const l=X,p=K,s=J;return Q(),$(Z,{ref_key:"refMap",ref:L,title:"爆管分析","full-content":!0,onMapLoaded:r,onDetailRefreshed:i[0]||(i[0]=g=>G(m).viewingDetail=!1),onDetailRefreshing:i[1]||(i[1]=g=>G(m).viewingDetail=!0)},{default:B(()=>[V(l,{ref_key:"refForm",ref:y,config:G(h)},null,8,["config"]),V(s,{ref_key:"refPanel_User",ref:a,"custom-class":"gis-detail-panel",draggable:!1,"max-min":!0,title:"受影响用户详情"},{default:B(()=>[H("div",ye,[V(p,{config:G(u).TableConfig_User.value},null,8,["config"])])]),_:1},512)]),_:1},512)}}}),_a=Y(fe,[["__scopeId","data-v-ca628cce"]]);export{_a as default};
