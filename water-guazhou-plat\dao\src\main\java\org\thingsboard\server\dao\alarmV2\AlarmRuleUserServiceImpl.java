package org.thingsboard.server.dao.alarmV2;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleUser;
import org.thingsboard.server.dao.sql.alarmV2.AlarmRuleUserMapper;

import java.util.*;

@Slf4j
@Service
public class AlarmRuleUserServiceImpl implements AlarmRuleUserService {

    @Autowired
    private AlarmRuleUserMapper alarmRuleUserMapper;

    @Override
    public void save(String id, List<AlarmRuleUser> msgList, List<AlarmRuleUser> appList) {
        QueryWrapper<AlarmRuleUser> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("main_id", id);
        alarmRuleUserMapper.delete(deleteWrapper);

        if ((msgList == null || msgList.size() == 0) && (appList == null || appList.size() == 0)) {
            return;
        }
        if (msgList == null) {
            msgList = new ArrayList<>();
        }
        if (appList == null) {
            appList = new ArrayList<>();
        }
        for (AlarmRuleUser alarmRuleUser : msgList) {
            alarmRuleUser.setId(UUID.randomUUID().toString().replace("-", ""));
            alarmRuleUser.setType("1");
            alarmRuleUser.setMainId(id);
            alarmRuleUser.setCreateTime(new Date());
        }
        for (AlarmRuleUser alarmRuleUser : appList) {
            alarmRuleUser.setId(UUID.randomUUID().toString().replace("-", ""));
            alarmRuleUser.setType("2");
            alarmRuleUser.setMainId(id);
            alarmRuleUser.setCreateTime(new Date());
        }
        msgList.addAll(appList);

        if (msgList.size() > 0) {
            alarmRuleUserMapper.batchInsert(msgList);
        }
    }

    @Override
    public List<AlarmRuleUser> findByRuleIdList(List<String> ruleIds) {
        if (ruleIds == null || ruleIds.size() == 0) {
            return new ArrayList<>();
        }
        QueryWrapper<AlarmRuleUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("main_id", ruleIds);

        return alarmRuleUserMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteByRuleIdList(List<String> idList) {
        QueryWrapper<AlarmRuleUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("main_id", idList);
        alarmRuleUserMapper.delete(queryWrapper);
    }
}
