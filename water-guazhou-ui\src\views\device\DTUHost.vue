<template>
  <TreeBox v-loading="state.totalLoading">
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch
      ref="refCardSearch"
      class="card-search"
      :config="SearchConfig"
    />
    <CardTable :config="TableConfig_Host" class="card-table" />
    <SLCard class="card">
      <InlineForm
        ref="refFormConfig_SensorHeader"
        class="search-sensor"
        :config="FormConfig_SensorHeader"
      ></InlineForm>

      <CardTable :config="TableConfig_Sensor" class="card-table-bottom" />
    </SLCard>
    <DialogForm
      ref="refDialogForm_Project"
      :config="DialogFormConfig_Project"
    ></DialogForm>
    <DialogForm
      ref="refDialogForm_Host"
      :config="DialogFormConfig_Host"
    ></DialogForm>
    <DialogForm
      ref="refDialogForm_Sensor"
      :config="DialogFormConfig_Sendor"
    ></DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Search, Plus } from '@element-plus/icons-vue';
import { onMounted, reactive, ref, shallowRef } from 'vue';
import { removeSlash } from '@/utils/removeIdSlash';
import useGlobal from '@/hooks/global/useGlobal';
import { addProject, delProject, editProject } from '@/api/project';
import {
  copyGateway,
  deleteGatewayAndDevice,
  getDevicesListByGatewayId,
  getHostOrDeviceByType,
  deleteDevice,
  getTemplateListByType,
  createdHostDevice,
  addSensor
} from '@/api/device';
// 接口
import { getDeviceType } from '@/api/ledger/ledger2';
import TreeBox from '../layout/treeOrDetailFrame/TreeBox.vue';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { getFormatTreeNodeDeepestChild } from '@/utils/GlobalHelper';
import useArea from '@/hooks/project/useProject';

const { btnPerms } = useGlobal();
const refDialogForm_Project = ref<IDialogFormIns>();
const refDialogForm_Host = ref<IDialogFormIns>();
const refDialogForm_Sensor = ref<IDialogFormIns>();
const refCardSearch = ref<ICardSearchIns>();
const state = reactive<{
  totalLoading: boolean;
  submitProjectType: 'add' | 'edit' | 'addRoot';
}>({
  totalLoading: false,
  submitProjectType: 'add'
});
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      label: '搜索',
      field: 'name',
      type: 'input'
    },
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', click: () => refreshHost() },
        {
          perm: btnPerms('DTUHostAdd'),
          text: '添加网关',
          click: () => addOrEditHost()
        }
      ]
    }
  ],
  defaultParams: {}
});
const { getProjectTreeData } = useArea();
const useProject = () => {
  const handleAddRoot = () => {
    state.submitProjectType = 'addRoot';
    DialogFormConfig_Project.title = '新建项目';
    DialogFormConfig_Project.defaultValue = {
      location: [116.4, 39.91]
    };
    refDialogForm_Project.value?.openDialog();
  };
  const handleEditChild = (node?: any) => {
    if (!node) {
      node = TreeData.currentProject;
    }
    state.submitProjectType = 'edit';
    DialogFormConfig_Project.defaultValue = {
      ...(node?.data || {}),
      ...((node?.data?.additionalInfo &&
        JSON.parse(node.data.additionalInfo)) ||
        {})
    };
    refDialogForm_Project.value?.openDialog();
  };
  const handleAddChild = (node?: any) => {
    state.submitProjectType = 'add';
    DialogFormConfig_Project.defaultValue = {
      parentId: node?.id || TreeData.currentProject?.id
    };
    refDialogForm_Project.value?.openDialog();
  };
  const handleDelete = (row?: any) => {
    SLConfirm('确定删除?', '删除提示')
      .then(async () => {
        try {
          const id = row?.id || TreeData.currentProject?.id;
          await delProject(id);
          SLMessage.success('删除成功');
          refreshTree();
        } catch (error) {
          SLMessage.error('删除失败');
        }
      })
      .catch(() => {
        //
      });
  };

  const refreshTree = async () => {
    state.totalLoading = true;
    try {
      const res = await getProjectTreeData();

      TreeData.data = res;
      TreeData.currentProject = getFormatTreeNodeDeepestChild(
        TreeData.data || []
      );
      refreshHost();

      state.totalLoading = false;
    } catch (error) {
      state.totalLoading = false;
    }
  };
  const TreeData = reactive<SLTreeConfig>({
    title: '区域划分',
    data: [],
    loading: false,
    isFilterTree: true,
    addRoot: {
      perm: true,
      text: '新建项目',
      click: () => handleAddRoot()
    },
    add: {
      // perm: btnPerms('projectTreeAdd'),
      perm: true,
      text: '子项',
      click: (data) => handleAddChild(data)
    },
    edit: {
      // perm: btnPerms('projectTreeEdit'),
      perm: true,
      text: '编辑',
      click: (data) => handleEditChild(data)
    },
    delete: {
      // perm: btnPerms('projectTreeDel'),
      perm: true,
      text: '删除',
      click: (data) => handleDelete(data)
    },
    treeNodeHandleClick: (data) => {
      TreeData.currentProject = data;
      TableConfig_Sensor.dataList = [];
      refreshHost();
    }
  });

  const DialogFormConfig_Project = reactive<IDialogFormConfig>({
    dialogWidth: 500,
    title: '新建项目',
    group: [
      {
        fields: [
          {
            type: 'input',
            label: '项目名称:',
            field: 'name',
            rules: [{ required: true, message: '请填写项目名称' }]
          },
          {
            type: 'select',
            label: '项目类型',
            field: 'type',
            options: [
              { label: '水司', value: '1' },
              { label: '水厂', value: '2' }
            ]
          },
          // {
          //   type: 'input',
          //   label: 'WIFI名称:',
          //   aInfo: true,
          //   key: 'WIFIName',
          //   rules: [{ required: true, message: '请填写WIFI名称' }]
          // },
          {
            type: 'textarea',
            label: '项目简介:',
            field: 'introduction',
            aInfo: true
          },
          {
            type: 'avatar',
            aInfo: true,
            label: '图片:',
            field: 'imageUrl'
          },
          {
            type: 'input',
            label: '项目地址:',
            aInfo: true,
            field: 'address',
            rules: [{ required: true, message: '请填写项目地址' }]
          },
          {
            type: 'amap',
            label: '项目定位:',
            aInfo: true,
            field: 'location',
            rules: [{ required: true, message: '请输入项目定位' }]
          }
        ]
      }
    ],
    defaultValue: {},
    submit: (params: any) => {
      SLConfirm('确定提交？', '确认信息')
        .then(async () => {
          try {
            if (params.id) {
              await editProject(params);
            } else {
              await addProject(params);
            }
            SLMessage.success('提交成功');
            refreshTree();
            refDialogForm_Project.value?.closeDialog();
          } catch (error) {
            SLMessage.error('提交失败');
          }
        })
        .catch(() => {
          //
        });
    },
    submitting: false
  });
  return {
    refreshTree,
    DialogFormConfig_Project,
    TreeData
  };
};
const useHost = () => {
  // 获取项目下的网关采集器
  const refreshHost = () => {
    TableConfig_Host.dataList = [];
    const queryParams = refCardSearch.value?.queryParams;
    const params = {
      page: TableConfig_Host.pagination.page,
      size: TableConfig_Host.pagination.limit,
      name: queryParams?.name
    };
    // 调拿设备的方法，拿选中项目的设备
    getHostOrDeviceByType(TreeData.currentProject.id, 'DTU', params).then(
      (res) => {
        TableConfig_Host.dataList = res.data.data.map((item) => ({
          ...item,
          ...(JSON.parse(item.additionalInfo || '') || {})
        }));
        TableConfig_Host.pagination.total = res.data.total;
        if (!res.data || !res.data.data || !res.data.data[0]) {
          TableConfig_Host.dataList = [];
          // $messageInfo('暂无网关')
        } else {
          refreshSensor(res.data.data[0]);
        }
      }
    );
  };
  // 点击行 复制网关
  const clickCopyHost = (row) => {
    copyGateway(removeSlash(row.id.id), TreeData.currentProject.id).then(
      (res) => {
        refreshHost();
        SLMessage.success(res.data.result);
      }
    );
  };
  // 点击行 删除网关
  const clickDeleteHost = (row) => {
    SLConfirm('确定删除该网关?', '删除提示')
      .then(() => {
        deleteGatewayAndDevice(removeSlash(row.id.id)).then((res) => {
          refreshHost();
          SLMessage.success(res.data.result);
        });
      })
      .catch(() => {
        //
      });
  };

  const addOrEditHost = (row?: any) => {
    if (!row) {
      DialogFormConfig_Host.defaultValue = {
        location: [116.4, 39.91],
        gateway: true,
        type: 'DTU',
        projectId: TreeData.currentProject.id
      };

      DialogFormConfig_Host.title = '添加网关';
    } else {
      DialogFormConfig_Host.title = '编辑网关';
      if (row.info) {
        for (const key in row.info) {
          row[`info__${key}`] = row.info[key];
        }
      }
      DialogFormConfig_Host.defaultValue = {
        ...row,
        ...JSON.parse(row.additionalInfo),
        gateway: true
      };
    }
    refDialogForm_Host.value?.openDialog();
  };

  const TableConfig_Host = reactive<ICardTable>({
    columns: [
      { label: '网关名称', prop: 'name', width: 200 },
      { label: '网关地址', prop: 'address' },
      { label: '网关密钥', prop: 'credentialsId' },
      { label: '掉线判断', prop: 'dropJudgement', width: 100 },
      { label: '网关定位', prop: 'showLocation', width: 200 }
    ],
    pagination: {
      page: 1,
      limit: 20,
      total: 0,
      handleSize: (val) => {
        TableConfig_Host.pagination.limit = val;
        refreshHost();
      },
      handlePage: (val) => {
        TableConfig_Host.pagination.page = val;
        refreshHost();
      }
    },
    dataList: [],
    operationFixed: 'right',
    handleRowClick: (row) => {
      TableConfig_Host.currentRow = row;
      refreshSensor(row);
    },
    operations: [
      {
        isTextBtn: true,
        text: '编辑',
        perm: btnPerms('DTUHostEdit'),
        // perm: true,
        icon: 'iconfont icon-bianji',
        click: (row) => addOrEditHost(row)
      },
      {
        isTextBtn: true,
        text: '复制',
        perm: btnPerms('DTUHostCopy'),
        // perm: true,
        icon: 'iconfont icon-icon_fuzhi',
        click: (row) => clickCopyHost(row)
      },
      {
        isTextBtn: true,
        text: '删除',
        perm: btnPerms('DTUHostDelete'),
        // perm: true,
        type: 'danger',
        icon: 'iconfont icon-shanchu',
        click: (row) => clickDeleteHost(row)
      }
    ],
    operationWidth: '230px',
    indexVisible: true
  });
  const DialogFormConfig_Host = reactive<IDialogFormConfig>({
    dialogWidth: 500,
    labelPosition: 'top',
    title: '添加网关',
    group: [
      {
        fields: [
          {
            type: 'input',
            label: '网关名称:',
            field: 'name',
            rules: [{ required: true, message: '请输入网关名称' }]
          },
          {
            type: 'input',
            label: '传输协议:',
            readonly: true,
            field: 'type',
            rules: [{ required: true, message: '请填写传输协议' }]
          },
          {
            type: 'textarea',
            label: '网关简介:',
            aInfo: true,
            field: 'introduction'
          },
          {
            type: 'input',
            aInfo: true,
            label: '硬件ID:',
            field: 'hardwareId',
            rules: [{ required: true, message: '请输入硬件ID' }]
          },
          {
            type: 'input',
            label: '网关地址:',
            aInfo: true,
            field: 'address',
            rules: [{ required: true, message: '请填写网关地址' }]
          },
          {
            type: 'amap',
            label: '网关定位:',
            aInfo: true,
            field: 'location',
            rules: [{ required: true, message: '请输入网关定位' }]
          },
          {
            type: 'image',
            aInfo: true,
            label: '网关图片:',
            field: 'imageUrl'
          },
          {
            type: 'select',
            aInfo: true,
            label: '掉线判断:',
            field: 'dropJudgement',
            options: [
              { label: '5m', value: '5m' },
              { label: '10m', value: '10m' },
              { label: '15m', value: '15m' },
              { label: '30m', value: '30m' },
              { label: '60m', value: '60m' }
            ],
            rules: [{ required: true, message: '请选择掉线判断' }]
          }
          // {
          //   type: 'none',
          //   aInfo: true,
          //   label: '',
          //   key: 'gateway'
          // }
        ]
      }
    ],
    defaultValue: {
      type: 'DTU'
    },
    submit: (params: any) => {
      SLConfirm('确定提交？', '提示信息').then(async () => {
        try {
          const res = await createdHostDevice(params);
          console.log(res);

          SLMessage.success('提交成功');
          refreshHost();
          refDialogForm_Host.value?.closeDialog();
        } catch (error) {
          SLMessage.error('提交失败');
        }
      });
    }
  });
  return {
    TableConfig_Host,
    DialogFormConfig_Host,
    refreshHost,
    addOrEditHost
  };
};

const refFormConfig_SensorHeader = ref<IInlineFormIns>();

const useSensor = () => {
  const TableConfig_Sensor = reactive<ICardTable>({
    columns: [
      { label: '采集器名称', prop: 'name', minWidth: 140 },
      { label: '接入类型', prop: 'accessType' },
      { label: '设备协议', prop: 'type' },
      { label: '从站地址', prop: 'unitId' },
      { label: 'Modbus类型', prop: 'modbusType', minWidth: 120 },
      { label: 'RTU串口', prop: 'portName' },
      { label: 'TCP地址', prop: 'host' },
      { label: 'TCP端口', prop: 'port' },
      { label: 'rtuOverTcp', prop: 'useRtuTcp', minWidth: 140 },
      { label: '波特率', prop: 'baudRate' },
      { label: 'Modbus 数据位', prop: 'dataBits', minWidth: 140 },
      { label: 'Modbus 校验位', prop: 'parity', minWidth: 140 },
      { label: 'Modbus 停止位', prop: 'stopBits', minWidth: 140 }
    ],
    operations: [
      {
        isTextBtn: true,
        text: '复制ID',
        // perm: btnPerms('pc_gateway_hostTable_copy'),
        perm: true,
        icon: 'iconfont icon-icon_fuzhi',
        click: (row) => copySensorId(row)
      },
      {
        isTextBtn: true,
        text: '编辑',
        perm: btnPerms('DTUSensorEdit'),
        // perm: true,
        icon: 'iconfont icon-bianji',
        click: (row) => {
          DialogFormConfig_Sendor.title = '编辑采集器';
          const location = row.location?.split(',');
          const locationArr =
            location?.length === 2
              ? location
              : window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter;
          const defaultValue = {
            ...row,
            ...JSON.parse(row.additionalInfo || {}),
            location: locationArr,
            gateway: true
          };
          for (const key in row.info) {
            defaultValue[`info__${key}`] = row.info[key];
          }
          DialogFormConfig_Sendor.defaultValue = defaultValue;
          refDialogForm_Sensor.value?.openDialog();
        }
      },
      {
        isTextBtn: true,
        text: '协议',
        // perm: btnPerms('pc_gateway_hostTable_copy'),
        perm: true,
        icon: 'iconfont icon-icon_fuzhi',
        click: (row) => {
          //
          console.log(row);
        }
      },
      {
        isTextBtn: true,
        text: '删除',
        type: 'danger',
        perm: btnPerms('DTUSensorDelete'),
        // perm: true,
        icon: 'iconfont icon-bianji',
        click: (row) => clickDeleteSensor(row)
      }
    ],
    operationWidth: '320px',
    operationFixed: 'right',
    pagination: {
      page: 1,
      limit: 20,
      total: 0,
      layout: 'total, sizes, prev, pager, next, jumper',
      handleSize: (val) => {
        TableConfig_Sensor.pagination.limit = val;
        refreshSensor();
      },
      handlePage: (val) => {
        TableConfig_Sensor.pagination.page = val;
        refreshSensor();
      }
    },
    dataList: [],
    indexVisible: true
  });
  const DialogFormConfig_Sendor = reactive<IDialogFormConfig>({
    dialogWidth: 500,
    labelPosition: 'top',
    title: '添加采集器',
    group: [
      {
        fields: [
          {
            type: 'input',
            label: '采集器名称:',
            field: 'name',
            rules: [{ required: true, message: '请输入采集器名称' }]
          },
          {
            type: 'select',
            label: '协议模板:',
            field: 'templateId',
            options: [],
            rules: [{ required: true, message: '请选择' }]
          },

          {
            type: 'select',
            allowCreate: true,
            field: 'deviceTypeName',
            label: '采集器类型',
            rules: [{ required: true, message: '请输入采集器类型' }]
          },
          {
            type: 'input',
            label: '从站地址:',
            field: 'info__unitId',
            rules: [{ required: true, message: '请输入从站地址' }]
          },
          {
            type: 'radio',
            label: '接入类型:',
            field: 'info__accessType',
            options: [
              { label: '232', value: '232' },
              { label: '485', value: '485' }
            ],
            rules: [{ required: true, message: '请输入硬件ID' }]
          },
          {
            type: 'input',
            label: '读取超时时间 单位：(ms):',
            // message: '如无特殊，无需修改',
            field: 'info__timeout',
            rules: [{ required: true, message: '请填写网关地址' }]
          },
          {
            type: 'amap',
            label: '采集器定位:',
            field: 'location',
            resultType: 'str',
            rules: [{ required: true, message: '请输入采集器定位' }]
          },
          {
            type: 'select',
            label: '波特率',
            field: 'info__baudRate',
            options: [
              { label: '1200', value: 1200 },
              { label: '2400', value: 2400 },
              { label: '4800', value: 4800 },
              { label: '9600', value: 9600 },
              { label: '14400', value: 1400 },
              { label: '19200', value: 19200 },
              { label: '38400', value: 38400 },
              { label: '57600', value: 57600 },
              { label: '115200', value: 115200 },
              { label: '128000', value: 128000 }
            ],
            rules: [{ required: true, message: '请选择波特率' }]
          },
          {
            type: 'select',
            label: 'Modbus数据位',
            field: 'info__dataBits',
            options: [
              { label: '7', value: 7 },
              { label: '8', value: 8 }
            ],
            rules: [{ required: true, message: '请选择Modbus数据位' }]
          },
          {
            type: 'select',
            label: 'Modbus校验',
            field: 'info__parity',
            options: [
              { value: 'none', label: 'none' },
              { value: 'even', label: 'even' },
              { value: 'odd', label: 'odd' }
            ],
            rules: [{ required: true, message: '请选择Modbus校验' }]
          },
          {
            type: 'select',
            label: 'Modbus停止位',
            field: 'info__stopBits',
            options: [
              { value: '1', label: '1' },
              { value: '2', label: '2' }
            ],
            rules: [{ required: true, message: '请输入Modbus停止位' }]
          },
          {
            type: 'select',
            aInfo: true,
            label: '掉线判断:',
            field: 'dropJudgement',
            options: [
              { label: '5m', value: '5m' },
              { label: '10m', value: '10m' },
              { label: '15m', value: '15m' },
              { label: '30m', value: '30m' },
              { label: '60m', value: '60m' }
            ],
            rules: [{ required: true, message: '请选择掉线判断' }]
          }
        ]
      }
    ],
    defaultValue: {
      type: 'DTU'
    },
    submit: (params: any) => {
      SLConfirm('确定提交？', '提示信息')
        .then(async () => {
          try {
            params.info = {};
            for (const key in params) {
              if (key.indexOf('__') !== -1) {
                const subKey = key.split('__')[1];
                params.info[subKey] = params[key];
                delete params[key];
              }
            }
            params.info = JSON.stringify(params.info);
            params.projectId = TreeData.currentProject.id;
            const res = await addSensor(params);
            if (res.data) {
              SLMessage.success('提交成功');
              refreshSensor();
              refDialogForm_Sensor.value?.closeDialog();
            } else {
              SLMessage.error(res.data?.message || '提交失败');
            }
          } catch (error: any) {
            SLMessage.error(error.message || '系统错误');
          }
        })
        .catch(() => {
          //
        });
    }
  });
  const FormConfig_SensorHeader = reactive<IFormConfig>({
    size: 'small',
    labelPosition: 'top',
    defaultValue: {
      sensorType: 'sonsor'
    },
    group: [
      {
        fields: [
          {
            itemContainerStyle: {
              width: '100%'
            },
            type: 'tabs',
            label: '',
            field: 'sensorType',
            tabs: [{ label: '采集器', value: 'sonsor' }]
          }
        ]
      },
      {
        fields: [
          { type: 'input', label: '', field: 'sensorKeyWords' },
          {
            type: 'btn-group',
            btns: [
              {
                perm: true,
                svgIcon: shallowRef(Search),
                text: '查询',
                click: () => refreshSensor()
              }
            ]
          },
          {
            type: 'btn-group',
            btns: [
              {
                perm: true,
                svgIcon: shallowRef(Plus),
                text: '添加采集器',
                click: () => addOrEditSensor()
              }
            ]
          }
        ]
      }
    ]
  });

  // 点击行 选中网关
  const refreshSensor = (row?: any) => {
    row = row || TableConfig_Host.currentRow || TableConfig_Host.dataList[0];
    TableConfig_Sensor.dataList = [];
    // todo: 获取网关的 采集器，逻辑流程，组态，变量等信息
    const params = {
      key: 'info,prop',
      page: TableConfig_Sensor.pagination.page,
      size: TableConfig_Sensor.pagination.limit,
      name: refFormConfig_SensorHeader.value?.dataForm?.sensorKeyWords
    };
    console.log(refFormConfig_SensorHeader.value?.dataForm);

    // 采集器 'info,prop',
    getDevicesListByGatewayId(removeSlash(row.id.id), params).then((res) => {
      const data = res.data?.data || [];
      TableConfig_Sensor.dataList = data.map((item) => {
        // item.info = JSON.parse(item.info||{})
        // for (const key in item.info) {
        //   item[key] = item.info[key]
        // }
        // const aInfo = JSON.parse(item.additionalInfo)
        // for (const key in aInfo) {
        //   item[key] = aInfo[key]
        // }
        const info = JSON.parse(item.info);
        const aInfo = JSON.parse(item.additionalInfo);
        const obj = {
          ...item,
          info,
          ...info,
          ...aInfo,
          name: item.name
        };
        return obj;
      });

      TableConfig_Sensor.pagination.total = res.data.total || 0;
    });

    // getFindScadaList(removeSlash(row.id.id)).then(res => {
    //   console.log(res.data, '网关-组态')
    // })
  };
  const initDeviceType = () => {
    //    调用获取设备类型接口
    getDeviceType().then((res) => {
      if (res.data) {
        const field = DialogFormConfig_Sendor.group[0].fields.find(
          (item) => item.field === 'deviceTypeName'
        ) as IFormSelect;
        field &&
          (field.options = res.data.map((item) => ({
            label: item,
            value: item
          })));
      }
    });
  };
  const copySensorId = (row) => {
    console.log('----------', row);
    const el = document.createElement('textarea');
    el.value = row.id.id;
    document.body.appendChild(el);
    el.select();
    document.execCommand('copy');
    document.body.removeChild(el);

    SLMessage.info('已复制设备ID');
  };
  const clickDeleteSensor = (row) => {
    SLConfirm('确定删除该采集器?', '删除提示')
      .then(() => {
        deleteDevice(row.id.id).then(() => {
          SLMessage.success('操作成功');
          refreshSensor();
        });
      })
      .catch(() => {
        //
      });
  };

  const addOrEditSensor = (row?: any) => {
    if (!row) {
      if (!TableConfig_Host.currentRow) return SLMessage.info('请选择网关');
      DialogFormConfig_Sendor.defaultValue = {
        type: 'DTU',
        location: [116.4, 39.91],
        gateWayId: TableConfig_Host.currentRow.id
      };
      DialogFormConfig_Sendor.title = '添加采集器';
    } else {
      DialogFormConfig_Sendor.title = '编辑采集器';
      if (row.info) {
        for (const key in row.info) {
          row[`info__${key}`] = row.info[key];
        }
      }
      const location = row.location?.split(',');
      const locationArr =
        location?.length === 2
          ? location
          : window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter;

      DialogFormConfig_Sendor.defaultValue = {
        ...row,
        ...JSON.parse(row.additionalInfo || {}),
        location: locationArr
      };
    }
    refDialogForm_Sensor.value?.openDialog();
  };
  return {
    FormConfig_SensorHeader,
    DialogFormConfig_Sendor,
    TableConfig_Sensor,
    refreshSensor,
    initDeviceType,
    copySensorId
  };
};
const { TreeData, refreshTree, DialogFormConfig_Project } = useProject();
const { TableConfig_Host, DialogFormConfig_Host, refreshHost, addOrEditHost } =
  useHost();
const {
  FormConfig_SensorHeader,
  DialogFormConfig_Sendor,
  TableConfig_Sensor,
  initDeviceType,
  refreshSensor
} = useSensor();

onMounted(() => {
  state.totalLoading = true;
  try {
    refreshTree();
  } catch (error) {
    state.totalLoading = false;
  }
  initDeviceType();
  getTemplateListByType('DTU').then((res) => {
    const field = DialogFormConfig_Sendor.group[0].fields.find(
      (item) => item.field === 'templateId'
    ) as IFormSelect;
    field &&
      (field.options = res.data.map((item) => ({
        label: item.name,
        value: item.id
      })));
  });
});
</script>

<style lang="scss" scoped>
// @import './css/host-page.scss';

.card-table {
  height: 40%;
  min-height: 350px;
  margin-bottom: 20px;
}

.card-table-bottom {
  height: 400px;
}
</style>
