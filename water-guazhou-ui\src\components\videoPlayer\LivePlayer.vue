<template>
  <div class="video_box">
    {{ videoUrl }}
    <!-- <div :id="videoInfo.id"></div> -->
    <!-- <LivePlayer
      ref="videoPlayer"
      style="width:100%;height:100%"
      :video-url="''"
      fluent
      live
      stretch
    ></LivePlayer> -->
  </div>
</template>

<script>
// import LivePlayer from '@liveqing/liveplayer'

export default {
  name: 'VideoPlayer',
  // components: { LivePlayer },
  props: {
    videoUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      player: null
    }
  },
  // watch: {
  //   videoInfo() {
  //     this.player ? this.player.pause() : ''
  //     this.playVideo()
  //   }
  // },

  mounted() {
    // this.playVideo()
  },
  // beforeDestroy() {
  //   clearInterval(this.intervalState)
  //   if (this.player) {
  //     this.player.pause()
  //     this.player = null
  //   }
  // },
  methods: {
    playVideo() {
      console.log(this.videoInfo, 'video info')
      this.player = document.getElementById(videoInfo.id)
      // this.player.getVueInstance().play()
      this.player.play()
    }
  }
}
</script>

<style lang='scss' scoped>
.video_box {
  width: 100%;
  height: 100%;
}
.video-js {
  width: 100%;
  height: 100%;
}
</style>

<style lang="scss">
.video-wrapper {
  width: 100%;
  height: 100%;
  .video-inner {
    width: 100%;
    height: 100%;
  }
}
</style>
