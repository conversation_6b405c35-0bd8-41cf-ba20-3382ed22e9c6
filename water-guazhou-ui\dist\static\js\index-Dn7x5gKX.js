import{_ as q}from"./CardTable-rdWOL4_6.js";import{m as O,d as $,c as _,r as T,bF as f,s as y,o as L,g as I,n as k,q as m,i as d,p as h,F as N,db as U,bh as B,an as j,cE as z,al as A,aj as J,bD as K,C as M}from"./index-r0dFAfgr.js";import{_ as G}from"./CardSearch-CB_HNR-Q.js";import{c as H}from"./statisticalAnalysis-BoRmiv4A.js";import{p as Q}from"./printUtils-C-AxhDcd.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function X(F){return O({url:"/api/station/list",method:"get",params:{page:1,size:1e3,type:F.type||"泵房"}})}const Z={class:"wrapper"},ee={key:0,class:"suggestion-card"},te={class:"suggestion-header"},ae={class:"suggestion-content"},ne=$({__name:"index",setup(F){const b=_(),x=_(),c=_(""),W=T({defaultParams:{year:[f().format("YYYY"),f().format("YYYY")]},filters:[{type:"yearrange",label:"选择年份",field:"year",clearable:!1},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>w(),svgIcon:y(A)},{text:"导出",perm:!0,type:"warning",svgIcon:y(J),click:()=>P()},{perm:!0,text:"打印",type:"success",svgIcon:y(K),click:()=>Y()}]}]}),n=T({loading:!1,dataList:[],columns:[{prop:"name",label:"泵站名称",minWidth:120,align:"center"},{prop:"time",label:"数据年份",minWidth:100,align:"center"},{prop:"unitConsumption",label:"吨水电耗（kWh/吨）",minWidth:150,align:"center"},{prop:"totalFlow",label:"供水量（千吨）",minWidth:130,align:"center"},{prop:"energy",label:"用电量（千kWh）",minWidth:140,align:"center"}],operations:[],showSummary:!1,operationWidth:"150px",indexVisible:!0,pagination:{hide:!0}}),E=t=>{if(!t||t.length===0)return"";let a=t[0],o=parseFloat(t[0].rawUnitConsumption)||1/0;return t.forEach(l=>{const r=parseFloat(l.rawUnitConsumption)||1/0;r<o&&r>0&&(o=r,a=l)}),a&&o<1/0?`通过对比分析，${a.name}泵站的运行效率最高（吨水电耗：${o.toFixed(2)} kWh/吨），建议作为水厂泵站运行优化的参考标准。`:""},P=()=>{var t;(t=x.value)==null||t.exportTable()},Y=()=>{Q({title:"水厂泵站比泵分析报告",data:n.dataList,titleList:n.columns})},w=async()=>{var o,l;n.loading=!0;const a=(((o=b.value)==null?void 0:o.queryParams)||{}).year||[];try{const v=((l=(await X({type:"泵站"})).data)==null?void 0:l.data)||[];if(v.length===0){n.dataList=[],n.loading=!1,c.value="暂无水厂泵站数据";return}const g=[],C=new Set;for(const i of v){const e={stationId:i.id,start:f(a[0]).startOf("year").valueOf(),end:f(a[1]).endOf("year").valueOf(),queryType:"year"};try{((await H(e)).data.data||[]).forEach(s=>{const u=`${s.stationId||i.id}_${s.time||a[0]}`;C.has(u)||(C.add(u),s.name=s.name||i.name||"未知泵站",s.stationId=s.stationId||i.id,g.push(s))})}catch(p){console.warn(`获取泵站 ${i.name} 数据失败:`,p)}}if(g.length>0){const i=g.map(e=>{const p=parseFloat(e.unitConsumption)||0,S=parseFloat(e.totalFlow)||0,s=parseFloat(e.energy)||0,u=parseFloat(e.lastTimeEnergy)||0,R=parseFloat(e.lastTimeTotalFlow)||0,D=parseFloat(e.differenceValue)||0,V=parseFloat(e.changeRate)||0;return{...e,name:e.name||"未知泵站",time:e.time||a[0]||new Date().getFullYear().toString(),unitConsumption:p.toFixed(2),totalFlow:(S/1e3).toFixed(2),energy:(s/1e3).toFixed(2),lastTimeEnergy:(u/1e3).toFixed(2),lastTimeTotalFlow:(R/1e3).toFixed(2),differenceValue:D.toFixed(2),changeRate:V.toFixed(2)+"%",rawUnitConsumption:p}});c.value=E(i),n.dataList=i}else n.dataList=[],c.value="暂无有效的泵站数据"}catch(r){console.error("获取数据失败:",r),n.dataList=[],c.value="数据获取失败，请稍后重试"}finally{n.loading=!1}};return L(()=>{w()}),(t,a)=>{const o=G,l=z,r=q;return I(),k("div",Z,[m(o,{ref_key:"cardSearch",ref:b,config:d(W)},null,8,["config"]),d(c)?(I(),k("div",ee,[h("div",te,[m(l,{class:"suggestion-icon"},{default:N(()=>[m(d(U))]),_:1}),a[0]||(a[0]=h("span",{class:"suggestion-title"},"分析建议",-1))]),h("div",ae,B(d(c)),1)])):j("",!0),m(r,{ref_key:"refTable",ref:x,class:"card-table",config:d(n)},null,8,["config"])])}}}),de=M(ne,[["__scopeId","data-v-341a274f"]]);export{de as default};
