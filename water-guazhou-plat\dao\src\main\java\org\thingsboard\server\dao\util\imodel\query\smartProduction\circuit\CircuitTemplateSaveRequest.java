package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTemplate;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

@Getter
@Setter
public class CircuitTemplateSaveRequest extends SaveRequest<CircuitTemplate> {

    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    private String type;

    // 巡检模板名称
    private String name;

    // 巡检项目，巡检配置的ID。多个用逗号分隔
    private String settings;

    // 备注
    private String remark;

    protected CircuitTemplate build() {
        CircuitTemplate entity = new CircuitTemplate();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    protected CircuitTemplate update(String id) {
        CircuitTemplate entity = new CircuitTemplate();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(CircuitTemplate entity) {
        entity.setType(type);
        entity.setName(name);
        entity.setSettings(settings);
        entity.setRemark(remark);
    }
}