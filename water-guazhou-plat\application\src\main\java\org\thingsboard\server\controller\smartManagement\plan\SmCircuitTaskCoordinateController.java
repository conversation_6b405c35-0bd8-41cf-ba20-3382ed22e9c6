package org.thingsboard.server.controller.smartManagement.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;;
import org.thingsboard.server.dao.gis.SmCircuitTaskCoordinateService;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SmCircuitTaskCoordinate;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SmCircuitTaskCoordinatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SmCircuitTaskCoordinateSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/sm/circuitTaskCoordinate")
public class SmCircuitTaskCoordinateController extends BaseController {
    @Autowired
    private SmCircuitTaskCoordinateService service;


    @GetMapping
    public IPage<SmCircuitTaskCoordinate> findAllConditional(SmCircuitTaskCoordinatePageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public SmCircuitTaskCoordinate save(@RequestBody SmCircuitTaskCoordinateSaveRequest req) {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SmCircuitTaskCoordinateSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

}