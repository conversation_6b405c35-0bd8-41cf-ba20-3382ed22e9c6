package org.thingsboard.server.dao.util.imodel.query.deviceDump;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.deviceDump.DeviceDumpDetail;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class DeviceDumpDetailSaveRequest extends SaveRequest<DeviceDumpDetail> {

    // 主表ID
    @NotNullOrEmpty(parentIgnore = true)
    private String mainId;

    // 设备标签码
    @NotNullOrEmpty
    private String deviceLabelCode;

    public DeviceDumpDetail build() {
        DeviceDumpDetail entity = new DeviceDumpDetail();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    public DeviceDumpDetail update(String id) {
        DeviceDumpDetail entity = new DeviceDumpDetail();
        entity.setId(id);
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    private void commonSet(DeviceDumpDetail entity) {
        entity.setMainId(mainId);
        entity.setDeviceLabelCode(deviceLabelCode);
    }
}