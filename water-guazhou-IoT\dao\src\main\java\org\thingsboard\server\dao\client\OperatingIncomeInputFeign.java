package org.thingsboard.server.dao.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

@Controller
@FeignClient(name = "base-service", configuration = {FeignConfig.class})
public interface OperatingIncomeInputFeign {

    @GetMapping("api/operatingIncomeInput/list")
    IstarResponse list(@RequestParam("year") String year, @RequestParam("stationId") String stationId) throws ThingsboardException;

}
