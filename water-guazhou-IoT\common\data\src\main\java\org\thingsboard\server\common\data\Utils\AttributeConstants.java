/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.Utils;

public class AttributeConstants {
    public static final String ATTRIBUTE_FORMULA = "formula";
    public static final String ATTRIBUTE_METER_READING_TIME = "readMeterTime";
    public static final String ATTRIBUTE_STATISTIC = "statistic";
    //tsdb相关常量
    public static final String TSDB_API_QUERY = "/api/query";
    public static final String TSDB_API_QUERY_LAST = "/api/query/last";
    public static final String TSDB_API_PUSH = "/api/put";
    public static final String TSDB_AGGREGATOR = "aggregator";
    public static final String TSDB_METRIC = "metric";
    public static final String TSDB_PROPERTY = "prop";
    public static final String TSDB_RATE = "rate";

    public static final String TELEMETRY_DATA_TYPE_ACCUMULATE="1";
    public static final String TELEMETRY_DATA_TYPE_RANDOM="0";
    public static final String TSDB_DOWNSAMPLE="downsample";
    //默认降采样为30分钟，取最后一条
    public static final String TSDB_DOWNSAMPLE_DEFAULT="15m-last";


    public enum TSDB_AGGREGATOR_TYPE {
        none, avg, count, first, last, minmin, mimmax, min, max, sum, zimsum
    }
}
