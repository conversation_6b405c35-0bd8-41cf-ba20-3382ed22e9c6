import { GetPipeCollectDataList } from '@/api/mapservice/pipeCollection'
// import { formatterDateTime } from '@/utils/GlobalHelper'

export const usePipeCollectData = () => {
  const list = ref<Record<string, any>[]>([])
  const getList = async (mainId: string) => {
    try {
      const res = await GetPipeCollectDataList({ mainId })
      list.value = res.data.data || []
      // total.value = res.data.total || 0
    } catch (error) {
      list.value = []
      // total.value = 0
    }
  }
  return {
    list,
    getList
  }
}
export const usePipeCollectStatus = (initialVal = '') => {
  const status = ref<string>(initialVal)
  const setStatus = (val: string) => {
    status.value = val
  }
  return {
    setStatus,
    status
  }
}
