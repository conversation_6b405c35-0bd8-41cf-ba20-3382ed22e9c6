import{h,z as ae,D as ye,E as we,G as xe,I as A,J as L,p as oe,H as re,m as C,t as H,d as ke,u as Pe,q as De,e as Ce,v as N,F as Te}from"./widget-BcWKanF2.js";import{g as se}from"./guid-DO7TRjsS.js";import{i as ce}from"./key-7hamXU9f.js";import{s as le,a as ue,c as de}from"./loadable-DZS8sRBo.js";import{d as fe}from"./action-CK67UTEO.js";import{d as he}from"./icon-vUORPQEt.js";import{d as pe}from"./loader-DYvscnHN.js";import{c as j,f as Fe,a as Ie,d as Ae,u as Be,b as Oe,e as Me,r as Le,F as q}from"./openCloseComponent-aiDFLC5b.js";import{u as Re,c as He,a as Ne,s as Se,d as ze,b as Ke}from"./t9n-B2bWcUZc.js";/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const _e=(t,e)=>{const n=t.level?`h${t.level}`:"div";return delete t.level,h(n,{...t},e)};/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */function G(t,e){return(t+e)%e}/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 *//*!
* focus-trap 7.2.0
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/function Y(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),n.push.apply(n,i)}return n}function V(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Y(Object(n),!0).forEach(function(i){Ue(t,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Y(Object(n)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(n,i))})}return t}function Ue(t,e,n){return e=je(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function $e(t,e){if(typeof t!="object"||t===null)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var i=n.call(t,e||"default");if(typeof i!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function je(t){var e=$e(t,"string");return typeof e=="symbol"?e:String(e)}var X={activateTrap:function(e,n){if(e.length>0){var i=e[e.length-1];i!==n&&i.pause()}var s=e.indexOf(n);s===-1||e.splice(s,1),e.push(n)},deactivateTrap:function(e,n){var i=e.indexOf(n);i!==-1&&e.splice(i,1),e.length>0&&e[e.length-1].unpause()}},qe=function(e){return e.tagName&&e.tagName.toLowerCase()==="input"&&typeof e.select=="function"},Ge=function(e){return e.key==="Escape"||e.key==="Esc"||e.keyCode===27},T=function(e){return e.key==="Tab"||e.keyCode===9},Ye=function(e){return T(e)&&!e.shiftKey},Ve=function(e){return T(e)&&e.shiftKey},W=function(e){return setTimeout(e,0)},J=function(e,n){var i=-1;return e.every(function(s,l){return n(s)?(i=l,!1):!0}),i},D=function(e){for(var n=arguments.length,i=new Array(n>1?n-1:0),s=1;s<n;s++)i[s-1]=arguments[s];return typeof e=="function"?e.apply(void 0,i):e},B=function(e){return e.target.shadowRoot&&typeof e.composedPath=="function"?e.composedPath()[0]:e.target},Xe=[],We=function(e,n){var i=(n==null?void 0:n.document)||document,s=(n==null?void 0:n.trapStack)||Xe,l=V({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:Ye,isKeyBackward:Ve},n),r={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0},m,b=function(a,o,c){return a&&a[o]!==void 0?a[o]:l[c||o]},g=function(a){return r.containerGroups.findIndex(function(o){var c=o.container,u=o.tabbableNodes;return c.contains(a)||u.find(function(d){return d===a})})},w=function(a){var o=l[a];if(typeof o=="function"){for(var c=arguments.length,u=new Array(c>1?c-1:0),d=1;d<c;d++)u[d-1]=arguments[d];o=o.apply(void 0,u)}if(o===!0&&(o=void 0),!o){if(o===void 0||o===!1)return o;throw new Error("`".concat(a,"` was specified but was not a node, or did not return a node"))}var p=o;if(typeof o=="string"&&(p=i.querySelector(o),!p))throw new Error("`".concat(a,"` as selector refers to no known node"));return p},F=function(){var a=w("initialFocus");if(a===!1)return!1;if(a===void 0)if(g(i.activeElement)>=0)a=i.activeElement;else{var o=r.tabbableGroups[0],c=o&&o.firstTabbableNode;a=c||w("fallbackFocus")}if(!a)throw new Error("Your focus-trap needs to have at least one focusable element");return a},x=function(){if(r.containerGroups=r.containers.map(function(a){var o=we(a,l.tabbableOptions),c=xe(a,l.tabbableOptions);return{container:a,tabbableNodes:o,focusableNodes:c,firstTabbableNode:o.length>0?o[0]:null,lastTabbableNode:o.length>0?o[o.length-1]:null,nextTabbableNode:function(d){var p=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,v=c.findIndex(function(y){return y===d});if(!(v<0))return p?c.slice(v+1).find(function(y){return A(y,l.tabbableOptions)}):c.slice(0,v).reverse().find(function(y){return A(y,l.tabbableOptions)})}}}),r.tabbableGroups=r.containerGroups.filter(function(a){return a.tabbableNodes.length>0}),r.tabbableGroups.length<=0&&!w("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times")},k=function f(a){if(a!==!1&&a!==i.activeElement){if(!a||!a.focus){f(F());return}a.focus({preventScroll:!!l.preventScroll}),r.mostRecentlyFocusedNode=a,qe(a)&&a.select()}},S=function(a){var o=w("setReturnFocus",a);return o||(o===!1?!1:a)},I=function(a){var o=B(a);if(!(g(o)>=0)){if(D(l.clickOutsideDeactivates,a)){m.deactivate({returnFocus:l.returnFocusOnDeactivate&&!L(o,l.tabbableOptions)});return}D(l.allowOutsideClick,a)||a.preventDefault()}},z=function(a){var o=B(a),c=g(o)>=0;c||o instanceof Document?c&&(r.mostRecentlyFocusedNode=o):(a.stopImmediatePropagation(),k(r.mostRecentlyFocusedNode||F()))},ve=function(a){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,c=B(a);x();var u=null;if(r.tabbableGroups.length>0){var d=g(c),p=d>=0?r.containerGroups[d]:void 0;if(d<0)o?u=r.tabbableGroups[r.tabbableGroups.length-1].lastTabbableNode:u=r.tabbableGroups[0].firstTabbableNode;else if(o){var v=J(r.tabbableGroups,function(O){var M=O.firstTabbableNode;return c===M});if(v<0&&(p.container===c||L(c,l.tabbableOptions)&&!A(c,l.tabbableOptions)&&!p.nextTabbableNode(c,!1))&&(v=d),v>=0){var y=v===0?r.tabbableGroups.length-1:v-1,be=r.tabbableGroups[y];u=be.lastTabbableNode}else T(a)||(u=p.nextTabbableNode(c,!1))}else{var P=J(r.tabbableGroups,function(O){var M=O.lastTabbableNode;return c===M});if(P<0&&(p.container===c||L(c,l.tabbableOptions)&&!A(c,l.tabbableOptions)&&!p.nextTabbableNode(c))&&(P=d),P>=0){var ge=P===r.tabbableGroups.length-1?0:P+1,Ee=r.tabbableGroups[ge];u=Ee.firstTabbableNode}else T(a)||(u=p.nextTabbableNode(c))}}else u=w("fallbackFocus");u&&(T(a)&&a.preventDefault(),k(u))},K=function(a){if(Ge(a)&&D(l.escapeDeactivates,a)!==!1){a.preventDefault(),m.deactivate();return}(l.isKeyForward(a)||l.isKeyBackward(a))&&ve(a,l.isKeyBackward(a))},_=function(a){var o=B(a);g(o)>=0||D(l.clickOutsideDeactivates,a)||D(l.allowOutsideClick,a)||(a.preventDefault(),a.stopImmediatePropagation())},U=function(){if(r.active)return X.activateTrap(s,m),r.delayInitialFocusTimer=l.delayInitialFocus?W(function(){k(F())}):k(F()),i.addEventListener("focusin",z,!0),i.addEventListener("mousedown",I,{capture:!0,passive:!1}),i.addEventListener("touchstart",I,{capture:!0,passive:!1}),i.addEventListener("click",_,{capture:!0,passive:!1}),i.addEventListener("keydown",K,{capture:!0,passive:!1}),m},$=function(){if(r.active)return i.removeEventListener("focusin",z,!0),i.removeEventListener("mousedown",I,!0),i.removeEventListener("touchstart",I,!0),i.removeEventListener("click",_,!0),i.removeEventListener("keydown",K,!0),m};return m={get active(){return r.active},get paused(){return r.paused},activate:function(a){if(r.active)return this;var o=b(a,"onActivate"),c=b(a,"onPostActivate"),u=b(a,"checkCanFocusTrap");u||x(),r.active=!0,r.paused=!1,r.nodeFocusedBeforeActivation=i.activeElement,o&&o();var d=function(){u&&x(),U(),c&&c()};return u?(u(r.containers.concat()).then(d,d),this):(d(),this)},deactivate:function(a){if(!r.active)return this;var o=V({onDeactivate:l.onDeactivate,onPostDeactivate:l.onPostDeactivate,checkCanReturnFocus:l.checkCanReturnFocus},a);clearTimeout(r.delayInitialFocusTimer),r.delayInitialFocusTimer=void 0,$(),r.active=!1,r.paused=!1,X.deactivateTrap(s,m);var c=b(o,"onDeactivate"),u=b(o,"onPostDeactivate"),d=b(o,"checkCanReturnFocus"),p=b(o,"returnFocus","returnFocusOnDeactivate");c&&c();var v=function(){W(function(){p&&k(S(r.nodeFocusedBeforeActivation)),u&&u()})};return p&&d?(d(S(r.nodeFocusedBeforeActivation)).then(v,v),this):(v(),this)},pause:function(){return r.paused||!r.active?this:(r.paused=!0,$(),this)},unpause:function(){return!r.paused||!r.active?this:(r.paused=!1,x(),U(),this)},updateContainerElements:function(a){var o=[].concat(a).filter(Boolean);return r.containers=o.map(function(c){return typeof c=="string"?i.querySelector(c):c}),r.active&&x(),this}},m.updateContainerElements(e),m};const Je=[];function Qe(t){const{focusTrapEl:e}=t;if(!e)return;e.tabIndex==null&&(e.tabIndex=-1);const n={clickOutsideDeactivates:!0,document:e.ownerDocument,escapeDeactivates:!1,fallbackFocus:e,setReturnFocus:i=>(ae(i),!1),tabbableOptions:ye,trapStack:Je};t.focusTrap=We(e,n)}function Q(t){var e;t.focusTrapDisabled||(e=t.focusTrap)==null||e.activate()}function R(t){var e;(e=t.focusTrap)==null||e.deactivate()}function Ze(t){var e;(e=t.focusTrap)==null||e.updateContainerElements(t.focusTrapEl)}/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const E={container:"container",arrow:"arrow",imageContainer:"image-container",closeButtonContainer:"close-button-container",closeButton:"close-button",content:"content",hasHeader:"has-header",header:"header",headerContent:"header-content",heading:"heading"},et="auto",Z="aria-controls",ee="aria-expanded";class tt{constructor(){this.registeredElements=new Map,this.registeredElementCount=0,this.queryPopover=e=>{const{registeredElements:n}=this,i=e.find(s=>n.has(s));return n.get(i)},this.togglePopovers=e=>{const n=e.composedPath(),i=this.queryPopover(n);i&&!i.triggerDisabled&&(i.open=!i.open),Array.from(this.registeredElements.values()).filter(s=>s!==i&&s.autoClose&&s.open&&!n.includes(s)).forEach(s=>s.open=!1)},this.keyHandler=e=>{e.defaultPrevented||(e.key==="Escape"?this.closeAllPopovers():ce(e.key)&&this.togglePopovers(e))},this.clickHandler=e=>{N(e)&&this.togglePopovers(e)}}registerElement(e,n){this.registeredElementCount++,this.registeredElements.set(e,n),this.registeredElementCount===1&&this.addListeners()}unregisterElement(e){this.registeredElements.delete(e)&&this.registeredElementCount--,this.registeredElementCount===0&&this.removeListeners()}closeAllPopovers(){Array.from(this.registeredElements.values()).forEach(e=>e.open=!1)}addListeners(){document.addEventListener("pointerdown",this.clickHandler,{capture:!0}),document.addEventListener("keydown",this.keyHandler,{capture:!0})}removeListeners(){document.removeEventListener("pointerdown",this.clickHandler,{capture:!0}),document.removeEventListener("keydown",this.keyHandler,{capture:!0})}}const nt='@keyframes in{0%{opacity:0}100%{opacity:1}}@keyframes in-down{0%{opacity:0;transform:translate3D(0, -5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-up{0%{opacity:0;transform:translate3D(0, 5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-scale{0%{opacity:0;transform:scale3D(0.95, 0.95, 1)}100%{opacity:1;transform:scale3D(1, 1, 1)}}:root{--calcite-animation-timing:calc(150ms * var(--calcite-internal-duration-factor));--calcite-internal-duration-factor:var(--calcite-duration-factor, 1);--calcite-internal-animation-timing-fast:calc(100ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-medium:calc(200ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-slow:calc(300ms * var(--calcite-internal-duration-factor))}.calcite-animate{opacity:0;animation-fill-mode:both;animation-duration:var(--calcite-animation-timing)}.calcite-animate__in{animation-name:in}.calcite-animate__in-down{animation-name:in-down}.calcite-animate__in-up{animation-name:in-up}.calcite-animate__in-scale{animation-name:in-scale}@media (prefers-reduced-motion: reduce){:root{--calcite-internal-duration-factor:0}}:root{--calcite-floating-ui-transition:var(--calcite-animation-timing);--calcite-floating-ui-z-index:600}:host([hidden]){display:none}:host{--calcite-floating-ui-z-index:var(--calcite-popover-z-index, 900);display:block;position:absolute;z-index:var(--calcite-floating-ui-z-index)}.calcite-floating-ui-anim{position:relative;transition:var(--calcite-floating-ui-transition);transition-property:transform, visibility, opacity;opacity:0;box-shadow:0 0 16px 0 rgba(0, 0, 0, 0.16);z-index:1;border-radius:0.25rem}:host([data-placement^=bottom]) .calcite-floating-ui-anim{transform:translateY(-5px)}:host([data-placement^=top]) .calcite-floating-ui-anim{transform:translateY(5px)}:host([data-placement^=left]) .calcite-floating-ui-anim{transform:translateX(5px)}:host([data-placement^=right]) .calcite-floating-ui-anim{transform:translateX(-5px)}:host([data-placement]) .calcite-floating-ui-anim--active{opacity:1;transform:translate(0)}:host([calcite-hydrated-hidden]){visibility:hidden !important;pointer-events:none}.arrow,.arrow::before{position:absolute;inline-size:8px;block-size:8px;z-index:-1}.arrow::before{content:"";--tw-shadow:0 4px 8px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04);--tw-shadow-colored:0 4px 8px -1px var(--tw-shadow-color), 0 2px 4px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);transform:rotate(45deg);background:var(--calcite-ui-foreground-1)}:host([data-placement^=top]) .arrow{inset-block-end:-4px}:host([data-placement^=bottom]) .arrow{inset-block-start:-4px}:host([data-placement^=right]) .arrow,:host([data-placement^=left]) .arrow{direction:ltr;text-align:start}:host([data-placement^=left]) .arrow{inset-inline-end:-4px}:host([data-placement^=right]) .arrow{inset-inline-start:-4px}:host([scale=s]) .heading{padding-inline:0.75rem;padding-block:0.5rem;font-size:var(--calcite-font-size--1);line-height:1.375}:host([scale=m]) .heading{padding-inline:1rem;padding-block:0.75rem;font-size:var(--calcite-font-size-0);line-height:1.375}:host([scale=l]) .heading{padding-inline:1.25rem;padding-block:1rem;font-size:var(--calcite-font-size-1);line-height:1.375}:host{pointer-events:none}:host([open]){pointer-events:initial}.calcite-floating-ui-anim{border-radius:0.25rem;border-width:1px;border-style:solid;border-color:var(--calcite-ui-border-3);background-color:var(--calcite-ui-foreground-1)}.arrow::before{outline:1px solid var(--calcite-ui-border-3)}.header{display:flex;flex:1 1 auto;align-items:stretch;justify-content:flex-start;border-width:0px;border-block-end-width:1px;border-style:solid;background-color:var(--calcite-ui-foreground-1);border-block-end-color:var(--calcite-ui-border-3)}.heading{margin:0px;display:block;flex:1 1 auto;align-self:center;white-space:normal;font-weight:var(--calcite-font-weight-medium);color:var(--calcite-ui-text-1);word-wrap:break-word;word-break:break-word}.container{position:relative;display:flex;block-size:100%;flex-direction:row;flex-wrap:nowrap;border-radius:0.25rem;background-color:var(--calcite-ui-foreground-1);color:var(--calcite-ui-text-1)}.container.has-header{flex-direction:column}.content{display:flex;block-size:100%;inline-size:100%;flex-direction:column;flex-wrap:nowrap;align-self:center;word-wrap:break-word;word-break:break-word}.close-button-container{display:flex;overflow:hidden;flex:0 0 auto;border-start-end-radius:0.25rem;border-end-end-radius:0.25rem}::slotted(calcite-panel),::slotted(calcite-flow){block-size:100%}',te=new tt,it=oe(class extends re{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.calcitePopoverBeforeClose=C(this,"calcitePopoverBeforeClose",6),this.calcitePopoverClose=C(this,"calcitePopoverClose",6),this.calcitePopoverBeforeOpen=C(this,"calcitePopoverBeforeOpen",6),this.calcitePopoverOpen=C(this,"calcitePopoverOpen",6),this.guid=`calcite-popover-${se()}`,this.openTransitionProp="opacity",this.hasLoaded=!1,this.setTransitionEl=t=>{this.transitionEl=t,j(this),this.focusTrapEl=t,Qe(this)},this.setFilteredPlacements=()=>{const{el:t,flipPlacements:e}=this;this.filteredFlipPlacements=e?Fe(e,t):null},this.setUpReferenceElement=(t=!0)=>{this.removeReferences(),this.effectiveReferenceElement=this.getReferenceElement(),Ie(this,this.effectiveReferenceElement,this.el);const{el:e,referenceElement:n,effectiveReferenceElement:i}=this;t&&n&&!i&&console.warn(`${e.tagName}: reference-element id "${n}" was not found.`,{el:e}),this.addReferences()},this.getId=()=>this.el.id||this.guid,this.setExpandedAttr=()=>{const{effectiveReferenceElement:t,open:e}=this;t&&"setAttribute"in t&&t.setAttribute(ee,H(e))},this.addReferences=()=>{const{effectiveReferenceElement:t}=this;if(!t)return;const e=this.getId();"setAttribute"in t&&t.setAttribute(Z,e),te.registerElement(t,this.el),this.setExpandedAttr()},this.removeReferences=()=>{const{effectiveReferenceElement:t}=this;t&&("removeAttribute"in t&&(t.removeAttribute(Z),t.removeAttribute(ee)),te.unregisterElement(t))},this.hide=()=>{this.open=!1},this.storeArrowEl=t=>{this.arrowEl=t,this.reposition(!0)},this.autoClose=!1,this.closable=!1,this.flipDisabled=!1,this.focusTrapDisabled=!1,this.pointerDisabled=!1,this.flipPlacements=void 0,this.heading=void 0,this.headingLevel=void 0,this.label=void 0,this.messageOverrides=void 0,this.messages=void 0,this.offsetDistance=Ae,this.offsetSkidding=0,this.open=!1,this.overlayPositioning="absolute",this.placement=et,this.referenceElement=void 0,this.scale="m",this.triggerDisabled=!1,this.effectiveLocale="",this.effectiveReferenceElement=void 0,this.defaultMessages=void 0}handlefocusTrapDisabled(t){this.open&&(t?R(this):Q(this))}flipPlacementsHandler(){this.setFilteredPlacements(),this.reposition(!0)}onMessagesChange(){}offsetDistanceOffsetHandler(){this.reposition(!0)}offsetSkiddingHandler(){this.reposition(!0)}openHandler(t){t?this.reposition(!0):Be(this.el),this.setExpandedAttr()}overlayPositioningHandler(){this.reposition(!0)}placementHandler(){this.reposition(!0)}referenceElementHandler(){this.setUpReferenceElement(),this.reposition(!0)}effectiveLocaleChange(){Re(this,this.effectiveLocale)}connectedCallback(){this.setFilteredPlacements(),He(this),Ne(this),j(this),this.setUpReferenceElement(this.hasLoaded)}async componentWillLoad(){await Se(this),le(this)}componentDidLoad(){ue(this),this.referenceElement&&!this.effectiveReferenceElement&&this.setUpReferenceElement(),this.reposition(),this.hasLoaded=!0}disconnectedCallback(){this.removeReferences(),ze(this),Ke(this),Oe(this,this.effectiveReferenceElement,this.el),Me(this),R(this)}async reposition(t=!1){const{el:e,effectiveReferenceElement:n,placement:i,overlayPositioning:s,flipDisabled:l,filteredFlipPlacements:r,offsetDistance:m,offsetSkidding:b,arrowEl:g}=this;return Le(this,{floatingEl:e,referenceEl:n,overlayPositioning:s,placement:i,flipDisabled:l,flipPlacements:r,offsetDistance:m,offsetSkidding:b,includeArrow:!this.pointerDisabled,arrowEl:g,type:"popover"},t)}async setFocus(){await de(this),ke(this.el),Pe(this.focusTrapEl)}async updateFocusTrapElements(){Ze(this)}getReferenceElement(){const{referenceElement:t,el:e}=this;return(typeof t=="string"?De(e,{id:t}):t)||null}onBeforeOpen(){this.calcitePopoverBeforeOpen.emit()}onOpen(){this.calcitePopoverOpen.emit(),Q(this)}onBeforeClose(){this.calcitePopoverBeforeClose.emit()}onClose(){this.calcitePopoverClose.emit(),R(this)}renderCloseButton(){const{messages:t,closable:e}=this;return e?h("div",{class:E.closeButtonContainer,key:E.closeButtonContainer},h("calcite-action",{class:E.closeButton,onClick:this.hide,ref:n=>this.closeButtonEl=n,scale:this.scale,text:t.close},h("calcite-icon",{icon:"x",scale:this.scale==="l"?"m":this.scale}))):null}renderHeader(){const{heading:t,headingLevel:e}=this,n=t?h(_e,{class:E.heading,level:e},t):null;return n?h("div",{class:E.header,key:E.header},n,this.renderCloseButton()):null}render(){const{effectiveReferenceElement:t,heading:e,label:n,open:i,pointerDisabled:s}=this,l=t&&i,r=!l,m=s?null:h("div",{class:E.arrow,ref:this.storeArrowEl});return h(Ce,{"aria-hidden":H(r),"aria-label":n,"aria-live":"polite","calcite-hydrated-hidden":r,id:this.getId(),role:"dialog"},h("div",{class:{[q.animation]:!0,[q.animationActive]:l},ref:this.setTransitionEl},m,h("div",{class:{[E.hasHeader]:!!e,[E.container]:!0}},this.renderHeader(),h("div",{class:E.content},h("slot",null)),e?null:this.renderCloseButton())))}static get assetsDirs(){return["assets"]}get el(){return this}static get watchers(){return{focusTrapDisabled:["handlefocusTrapDisabled"],flipPlacements:["flipPlacementsHandler"],messageOverrides:["onMessagesChange"],offsetDistance:["offsetDistanceOffsetHandler"],offsetSkidding:["offsetSkiddingHandler"],open:["openHandler"],overlayPositioning:["overlayPositioningHandler"],placement:["placementHandler"],referenceElement:["referenceElementHandler"],effectiveLocale:["effectiveLocaleChange"]}}static get style(){return nt}},[1,"calcite-popover",{autoClose:[516,"auto-close"],closable:[1540],flipDisabled:[516,"flip-disabled"],focusTrapDisabled:[516,"focus-trap-disabled"],pointerDisabled:[516,"pointer-disabled"],flipPlacements:[16],heading:[1],headingLevel:[514,"heading-level"],label:[1],messageOverrides:[1040],messages:[1040],offsetDistance:[514,"offset-distance"],offsetSkidding:[514,"offset-skidding"],open:[1540],overlayPositioning:[513,"overlay-positioning"],placement:[513],referenceElement:[1,"reference-element"],scale:[513],triggerDisabled:[516,"trigger-disabled"],effectiveLocale:[32],effectiveReferenceElement:[32],defaultMessages:[32],reposition:[64],setFocus:[64],updateFocusTrapElements:[64]}]);function me(){if(typeof customElements>"u")return;["calcite-popover","calcite-action","calcite-icon","calcite-loader"].forEach(e=>{switch(e){case"calcite-popover":customElements.get(e)||customElements.define(e,it);break;case"calcite-action":customElements.get(e)||fe();break;case"calcite-icon":customElements.get(e)||he();break;case"calcite-loader":customElements.get(e)||pe();break}})}me();/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const ne={menu:"menu",defaultTrigger:"default-trigger"},ie={tooltip:"tooltip",trigger:"trigger"},at={menu:"ellipsis"},ot="@keyframes in{0%{opacity:0}100%{opacity:1}}@keyframes in-down{0%{opacity:0;transform:translate3D(0, -5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-up{0%{opacity:0;transform:translate3D(0, 5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-scale{0%{opacity:0;transform:scale3D(0.95, 0.95, 1)}100%{opacity:1;transform:scale3D(1, 1, 1)}}:root{--calcite-animation-timing:calc(150ms * var(--calcite-internal-duration-factor));--calcite-internal-duration-factor:var(--calcite-duration-factor, 1);--calcite-internal-animation-timing-fast:calc(100ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-medium:calc(200ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-slow:calc(300ms * var(--calcite-internal-duration-factor))}.calcite-animate{opacity:0;animation-fill-mode:both;animation-duration:var(--calcite-animation-timing)}.calcite-animate__in{animation-name:in}.calcite-animate__in-down{animation-name:in-down}.calcite-animate__in-up{animation-name:in-up}.calcite-animate__in-scale{animation-name:in-scale}@media (prefers-reduced-motion: reduce){:root{--calcite-internal-duration-factor:0}}:root{--calcite-floating-ui-transition:var(--calcite-animation-timing);--calcite-floating-ui-z-index:600}:host([hidden]){display:none}:host{box-sizing:border-box;display:flex;flex-direction:column;background-color:var(--calcite-ui-foreground-1);font-size:var(--calcite-font-size-1);color:var(--calcite-ui-text-2)}.menu ::slotted(calcite-action){margin:0.125rem;display:flex;outline-color:transparent}.menu ::slotted(calcite-action[active]){outline:2px solid var(--calcite-ui-brand);outline-offset:0px}.default-trigger{position:relative;block-size:100%;flex:0 1 auto;align-self:stretch}slot[name=trigger]::slotted(calcite-action),calcite-action::slotted([slot=trigger]){position:relative;block-size:100%;flex:0 1 auto;align-self:stretch}.menu{flex-direction:column;flex-wrap:nowrap;outline:2px solid transparent;outline-offset:2px}",rt=["ArrowUp","ArrowDown","End","Home"],st=oe(class extends re{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.calciteActionMenuOpen=C(this,"calciteActionMenuOpen",6),this.actionElements=[],this.guid=`calcite-action-menu-${se()}`,this.menuId=`${this.guid}-menu`,this.menuButtonId=`${this.guid}-menu-button`,this.connectMenuButtonEl=()=>{const{menuButtonId:t,menuId:e,open:n,label:i}=this,s=this.slottedMenuButtonEl||this.defaultMenuButtonEl;this.menuButtonEl!==s&&(this.disconnectMenuButtonEl(),this.menuButtonEl=s,this.setTooltipReferenceElement(),s&&(s.active=n,s.setAttribute("aria-controls",e),s.setAttribute("aria-expanded",H(n)),s.setAttribute("aria-haspopup","true"),s.id||(s.id=t),s.label||(s.label=i),s.text||(s.text=i),s.addEventListener("pointerdown",this.menuButtonClick),s.addEventListener("keydown",this.menuButtonKeyDown)))},this.disconnectMenuButtonEl=()=>{const{menuButtonEl:t}=this;t&&(t.removeEventListener("pointerdown",this.menuButtonClick),t.removeEventListener("keydown",this.menuButtonKeyDown))},this.setMenuButtonEl=t=>{const e=t.target.assignedElements({flatten:!0}).filter(n=>n==null?void 0:n.matches("calcite-action"));this.slottedMenuButtonEl=e[0],this.connectMenuButtonEl()},this.setDefaultMenuButtonEl=t=>{this.defaultMenuButtonEl=t,this.connectMenuButtonEl()},this.handleCalciteActionClick=()=>{this.open=!1,this.setFocus()},this.menuButtonClick=t=>{N(t)&&this.toggleOpen()},this.updateTooltip=t=>{const e=t.target.assignedElements({flatten:!0}).filter(n=>n==null?void 0:n.matches("calcite-tooltip"));this.tooltipEl=e[0],this.setTooltipReferenceElement()},this.setTooltipReferenceElement=()=>{const{tooltipEl:t,expanded:e,menuButtonEl:n,open:i}=this;t&&(t.referenceElement=!e&&!i?n:null)},this.updateAction=(t,e)=>{const{guid:n,activeMenuItemIndex:i}=this,s=`${n}-action-${e}`;t.tabIndex=-1,t.setAttribute("role","menuitem"),t.id||(t.id=s),t.active=e===i},this.updateActions=t=>{t==null||t.forEach(this.updateAction)},this.handleDefaultSlotChange=t=>{const e=t.target.assignedElements({flatten:!0}).filter(n=>n==null?void 0:n.matches("calcite-action"));this.actionElements=e},this.menuButtonKeyDown=t=>{const{key:e}=t,{actionElements:n,activeMenuItemIndex:i,open:s}=this;if(n.length){if(ce(e)){if(t.preventDefault(),!s){this.toggleOpen();return}const l=n[i];l?l.click():this.toggleOpen(!1)}if(e==="Tab"){this.open=!1;return}if(e==="Escape"){this.toggleOpen(!1),t.preventDefault();return}this.handleActionNavigation(t,e,n)}},this.handleActionNavigation=(t,e,n)=>{if(!this.isValidKey(e,rt))return;if(t.preventDefault(),!this.open){this.toggleOpen(),(e==="Home"||e==="ArrowDown")&&(this.activeMenuItemIndex=0),(e==="End"||e==="ArrowUp")&&(this.activeMenuItemIndex=n.length-1);return}e==="Home"&&(this.activeMenuItemIndex=0),e==="End"&&(this.activeMenuItemIndex=n.length-1);const i=this.activeMenuItemIndex;e==="ArrowUp"&&(this.activeMenuItemIndex=G(Math.max(i-1,-1),n.length)),e==="ArrowDown"&&(this.activeMenuItemIndex=G(i+1,n.length))},this.toggleOpenEnd=()=>{this.setFocus(),this.el.removeEventListener("calcitePopoverOpen",this.toggleOpenEnd)},this.toggleOpen=(t=!this.open)=>{this.el.addEventListener("calcitePopoverOpen",this.toggleOpenEnd),this.open=t},this.expanded=!1,this.flipPlacements=void 0,this.label=void 0,this.open=!1,this.overlayPositioning="absolute",this.placement="auto",this.scale=void 0,this.menuButtonEl=void 0,this.activeMenuItemIndex=-1}componentWillLoad(){le(this)}componentDidLoad(){ue(this)}disconnectedCallback(){this.disconnectMenuButtonEl()}expandedHandler(){this.open=!1,this.setTooltipReferenceElement()}openHandler(t){this.activeMenuItemIndex=this.open?0:-1,this.menuButtonEl&&(this.menuButtonEl.active=t),this.calciteActionMenuOpen.emit(),this.setTooltipReferenceElement()}closeCalciteActionMenuOnClick(t){!N(t)||t.composedPath().includes(this.el)||(this.open=!1)}activeMenuItemIndexHandler(){this.updateActions(this.actionElements)}async setFocus(){await de(this),ae(this.menuButtonEl)}renderMenuButton(){const{label:t,scale:e,expanded:n}=this;return h("slot",{name:ie.trigger,onSlotchange:this.setMenuButtonEl},h("calcite-action",{class:ne.defaultTrigger,icon:at.menu,ref:this.setDefaultMenuButtonEl,scale:e,text:t,textEnabled:n}))}renderMenuItems(){const{actionElements:t,activeMenuItemIndex:e,open:n,menuId:i,menuButtonEl:s,label:l,placement:r,overlayPositioning:m,flipPlacements:b}=this,g=t[e],w=(g==null?void 0:g.id)||null;return h("calcite-popover",{flipPlacements:b,focusTrapDisabled:!0,label:l,offsetDistance:0,open:n,overlayPositioning:m,placement:r,pointerDisabled:!0,referenceElement:s},h("div",{"aria-activedescendant":w,"aria-labelledby":s==null?void 0:s.id,class:ne.menu,id:i,onClick:this.handleCalciteActionClick,role:"menu",tabIndex:-1},h("slot",{onSlotchange:this.handleDefaultSlotChange})))}render(){return h(Te,null,this.renderMenuButton(),this.renderMenuItems(),h("slot",{name:ie.tooltip,onSlotchange:this.updateTooltip}))}isValidKey(t,e){return!!e.find(n=>n===t)}get el(){return this}static get watchers(){return{expanded:["expandedHandler"],open:["openHandler"],activeMenuItemIndex:["activeMenuItemIndexHandler"]}}static get style(){return ot}},[1,"calcite-action-menu",{expanded:[516],flipPlacements:[16],label:[1],open:[1540],overlayPositioning:[513,"overlay-positioning"],placement:[513],scale:[513],menuButtonEl:[32],activeMenuItemIndex:[32],setFocus:[64]},[[9,"pointerdown","closeCalciteActionMenuOnClick"]]]);function ct(){if(typeof customElements>"u")return;["calcite-action-menu","calcite-action","calcite-icon","calcite-loader","calcite-popover"].forEach(e=>{switch(e){case"calcite-action-menu":customElements.get(e)||customElements.define(e,st);break;case"calcite-action":customElements.get(e)||fe();break;case"calcite-icon":customElements.get(e)||he();break;case"calcite-loader":customElements.get(e)||pe();break;case"calcite-popover":customElements.get(e)||me();break}})}ct();export{_e as H,ie as S,ct as a,me as d};
