package org.thingsboard.server.dao.model.sql.smartService.seats;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * kpi指标配置
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-15
 */
@TableName("tb_service_seats_user")
@Data
public class SeatsUser {

    @TableId
    private String id;

    private String userId;

    private transient String userName;

    private transient String roleName;

    private transient String organizationName;

    private transient String departmentId;

    private transient String departmentName;

    private transient String phone;

    private Integer orderNum;

    private Integer userNo;

    private Date createTime;

    private String tenantId;

}
