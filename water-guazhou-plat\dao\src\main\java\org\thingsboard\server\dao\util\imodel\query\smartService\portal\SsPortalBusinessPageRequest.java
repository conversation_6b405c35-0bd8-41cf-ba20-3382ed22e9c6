package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBusiness;

@Getter
@Setter
public class SsPortalBusinessPageRequest extends AdvancedPageableQueryEntity<SsPortalBusiness, SsPortalBusinessPageRequest> {
    // 标题
    private String title;

    // 是否显示
    private Boolean active;

}
