package org.thingsboard.server.dao.model.sql.deviceManage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("m_device")
public class Device  {
    @TableId
    private String id;

    private String serialId;

    private String name;

    private String model;

    private String label;

    private String unit;

    private Integer maintenanceCycle;

    private Double minStock;

    private String images;

    private String files;

    private String creator;

    private transient String creatorName;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

    private String remark;

    private String typeId;

    private transient String typeSerialId;

    private Integer useYear;

    private String autoField;

    private transient String linkedType;

    private transient String topType;

}
