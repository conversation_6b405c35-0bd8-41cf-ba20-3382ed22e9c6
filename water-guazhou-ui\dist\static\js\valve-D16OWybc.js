import{d as $,c as N,r as x,s as P,o as z,am as E,j as G,g as O,n as W,q as F,i as y,t as Y,p as H,_ as J,aq as K,C as Q}from"./index-r0dFAfgr.js";import{b as X}from"./onemap-CEunQziB.js";import{g as Z}from"./URLHelper-B9aplt5w.js";import{P as S,C as aa}from"./index-CcDafpIP.js";import{w as ta}from"./Point-WxyopZva.js";import{r as U}from"./chart-wy3NEK2T.js";const ea={class:"onemap-panel-wrapper"},sa={class:"table-box"},na=$({__name:"valve",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(j,{emit:q}){const I=q,C=j,m=N([{label:"0 个",value:"阀门总数"}]),w=N(),f=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],k=x({group:[{id:"chart",fieldset:{desc:"监测状态统计",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:U(),style:{height:"150px"}}]},{id:"tab",fields:[{type:"input",field:"name",appendBtns:[{perm:!0,text:"刷新",click:()=>r(!0)}],onChange:()=>r()},{type:"tabs",field:"type",tabs:[{label:"全部",value:"all"},...f.map(t=>({...t,value:t.name}))],tabType:"simple",onChange:()=>r()}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),i=x({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:t,size:n})=>{i.pagination.page=t,i.pagination.limit=n},layout:"total,sizes, jumper"},handleRowClick:t=>A(t),columns:[{width:200,label:"名称",prop:"name",sortable:!0},{width:80,label:"状态",prop:"status",formatter:t=>{var n;return((n=f.find(p=>p.name===t.status))==null?void 0:n.label)||t.status}},{width:160,label:"更新时间",prop:"lastTime",sortable:!0}]}),V=x({dataList:[],columns:[],pagination:{refreshData:({page:t,size:n})=>{V.pagination.page=t,V.pagination.limit=n,r()}}}),r=async t=>{var n,p,g,b,D,T,L,M;i.loading=!0;try{const h=(n=w.value)==null?void 0:n.dataForm.type,v=await X({name:(p=w.value)==null?void 0:p.dataForm.name,status:h==="all"?"":h});i.dataList=((g=v.data)==null?void 0:g.data)||[];const R=k.group[0].fields[0],_=((D=(b=v.data)==null?void 0:b.data)==null?void 0:D.length)||0,B=[],d=[];if((L=(T=v.data)==null?void 0:T.data)!=null&&L.map(a=>{var u,c;const s=(u=a.location)==null?void 0:u.split(",");if((s==null?void 0:s.length)===2){const l=new ta({longitude:s[0],latitude:s[1],spatialReference:(c=C.view)==null?void 0:c.spatialReference});B.push({visible:!1,x:l.x,y:l.y,offsetY:-40,id:a.stationId,title:a.name,customComponent:P(S),customConfig:{info:{type:"attrs",imageUrl:a.imgs,stationId:a.stationId}},attributes:{path:C.menu.path,id:a.stationId,row:a},symbolConfig:{url:Z("测压点.png")}})}let e=d.find(l=>l.name===a.status);const{label:o}=f.find(l=>l.name===a.status)||{};e?e.value++:(e={name:a.status,nameAlias:o,value:1,scale:"0%"},d.push(e))}),d.map(a=>{var s,e,o;return a.scale=_===0?"0%":(Number(a.value)/_*100).toFixed(2)+"%",a.value=((o=(e=(s=v.data)==null?void 0:s.data)==null?void 0:e.filter(u=>u.status===a.name))==null?void 0:o.length)||0,a}),t){const a=(M=k.group.find(s=>s.id==="tab"))==null?void 0:M.fields[1];if(a){a.tabs=a.tabs.map(e=>{var c;const o=d.find(l=>l.name===e.value),u=((c=f.find(l=>l.name===e.value))==null?void 0:c.label)||"";return e.label=u+"("+((o==null?void 0:o.value)||0)+")",e});const s=a.tabs.find(e=>e.value==="all");s&&(s.label="全部("+_+")"),R&&(R.option=U(d,"个","",0))}m.value[0].label=_+"个"}I("addMarks",{windows:B,customWinComp:P(S)})}catch(h){console.dir(h)}i.loading=!1},A=async t=>{I("highlightMark",C.menu,t==null?void 0:t.stationId)};return z(()=>{r(!0)}),E(()=>G().isDark,()=>r(!0)),(t,n)=>{const p=J,g=K;return O(),W("div",ea,[F(y(aa),{modelValue:y(m),"onUpdate:modelValue":n[0]||(n[0]=b=>Y(m)?m.value=b:null),span:24},null,8,["modelValue"]),F(p,{ref_key:"refForm",ref:w,config:y(k)},null,8,["config"]),H("div",sa,[F(g,{config:y(i)},null,8,["config"])])])}}}),da=Q(na,[["__scopeId","data-v-cf0794d0"]]);export{da as default};
