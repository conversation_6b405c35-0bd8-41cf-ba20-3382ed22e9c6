import{饼 as i,渐 as l}from"./charts-m9UAsyIq.js";import{f as m,a as p}from"./smartDecisionData-BKd4shVG.js";import{u as d}from"./useDetector-BRcb7GRN.js";import{d as u,c as n,o as h,ay as C,g as v,n as x,p as c,q as _,i as e,C as k}from"./index-r0dFAfgr.js";const S={class:"bottom-box",title:"售水量"},V={class:"chart"},y={class:"chart"},B=u({__name:"SSL",setup(D){const s=n(),a=n(),f=d();return h(()=>{f.listenToMush(document.documentElement,()=>{var t,o;(t=s.value)==null||t.resize(),(o=a.value)==null||o.resize()})}),(t,o)=>{const r=C("VChart");return v(),x("div",S,[c("div",V,[_(r,{ref_key:"refChart1",ref:s,option:e(i)(e(m))},null,8,["option"])]),c("div",y,[_(r,{ref_key:"refChart2",ref:a,option:e(l)(e(p))},null,8,["option"])])])}}}),L=k(B,[["__scopeId","data-v-66f46a31"]]);export{L as default};
