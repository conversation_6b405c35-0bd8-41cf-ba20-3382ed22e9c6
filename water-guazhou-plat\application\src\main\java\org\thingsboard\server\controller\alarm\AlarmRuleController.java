package org.thingsboard.server.controller.alarm;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.alarmV2.AlarmRuleService;
import org.thingsboard.server.dao.model.DTO.AlarmRuleDTO;
import org.thingsboard.server.dao.model.request.AlarmRuleListRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 报警规则
 */
@RestController
@RequestMapping("api/alarmV2/alarmRule")
public class AlarmRuleController extends BaseController {

    @Autowired
    private AlarmRuleService alarmRuleService;

    @GetMapping("list")
    public IstarResponse findList(AlarmRuleListRequest request) throws ThingsboardException {
        return IstarResponse.ok(alarmRuleService.findList(request, getTenantId()));
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody AlarmRuleDTO entity) throws ThingsboardException {
        alarmRuleService.save(entity, getTenantId());
        return IstarResponse.ok();
    }

    @DeleteMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        alarmRuleService.remove(ids);

        return IstarResponse.ok();
    }

}
