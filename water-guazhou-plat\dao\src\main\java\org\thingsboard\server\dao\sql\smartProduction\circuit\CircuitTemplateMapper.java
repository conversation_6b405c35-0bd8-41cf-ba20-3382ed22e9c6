package org.thingsboard.server.dao.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTemplate;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTemplatePageRequest;

@Mapper
public interface CircuitTemplateMapper extends BaseMapper<CircuitTemplate> {
    IPage<CircuitTemplate> findByPage(CircuitTemplatePageRequest request);

    boolean update(CircuitTemplate entity);

    String getSettings(String templateId);

    String getNameById(String id);
}
