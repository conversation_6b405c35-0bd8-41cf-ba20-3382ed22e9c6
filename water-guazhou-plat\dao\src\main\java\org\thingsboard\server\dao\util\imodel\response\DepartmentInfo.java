package org.thingsboard.server.dao.util.imodel.response;

import lombok.Getter;

@Getter
public class DepartmentInfo {
    private final String id;

    private final String name;

    private final String directOrgId;

    private final int flag;

    private boolean invalid = false;

    public DepartmentInfo(String id, String name, String directOrgId, int flag) {
        this.id = id;
        this.name = name;
        this.directOrgId = id == null || id.equals(directOrgId) ? null : directOrgId;
        this.flag = flag;
    }

    public boolean isDepartment() {
        return flag == 0 || flag == 1;
    }

    public boolean isOrganization() {
        return flag == 0 || flag == 2;
    }

    public void invalid() {
        invalid = true;
    }
}
