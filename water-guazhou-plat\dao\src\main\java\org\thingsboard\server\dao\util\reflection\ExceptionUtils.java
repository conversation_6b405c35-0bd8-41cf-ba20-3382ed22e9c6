package org.thingsboard.server.dao.util.reflection;

import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.regex.Pattern;

public class ExceptionUtils {
    public static RuntimeException createSilentThrow(String pattern, Object... params) {
        return new UndeclaredThrowableException(new ThingsboardException(
                String.format(pattern, params), ThingsboardErrorCode.GENERAL
        ));
    }

    public static void silentThrow(String pattern, Object... params) {
        throw createSilentThrow(pattern, params);
    }

    public static void silentThrow(String pattern, boolean condition, Object... params) {
        if (condition)
            throw createSilentThrow(pattern, params);
    }

    public static void testNumber(String testNumberStr, String message) {
        if (!Pattern.matches("\\d+", testNumberStr))
            silentThrow(message);
    }
}
