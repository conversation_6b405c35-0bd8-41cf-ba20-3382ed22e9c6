import{d as C,a8 as m,g as t,n as a,q as v,F as N,h as l,i as d,di as P,an as f,p as h,bh as b,aB as g,aJ as y,cE as E,aH as V,C as z}from"./index-r0dFAfgr.js";const A={class:"custom-tree-node"},F={key:1,class:"iconfont icon-xunjian district"},I={class:"custom-tree-node__label"},T={class:"btn-wrapper"},j={key:0,class:"divider"},q=C({__name:"AreaTreeNode",props:{node:{},data:{},customProps:{}},setup(B){const n=B,k=m(()=>{var e,o;return(o=(e=n.customProps)==null?void 0:e.iconBtns)==null?void 0:o.filter(s=>typeof s.perm=="function"?s.perm(n.data)===!0:s.perm===!0).length}),w=m(()=>{var e;return(e=n.customProps)==null?void 0:e.textBtns.filter(o=>typeof o.perm=="function"?o.perm(n.data)===!0:o.perm===!0).length});return(e,o)=>{var i,p,_;const s=E,u=V;return t(),a("div",A,[v(s,null,{default:N(()=>[e.data.data.layer===1?(t(),l(d(P),{key:0,class:"area"})):e.data.data.layer===2?(t(),a("i",F)):f("",!0)]),_:1}),h("span",I,b((i=e.data)==null?void 0:i.label),1),h("div",T,[(t(!0),a(g,null,y((p=e.customProps)==null?void 0:p.iconBtns,(r,c)=>(t(),l(u,{key:c,size:"small",row:e.data,config:r},null,8,["row","config"]))),128)),d(k)&&d(w)?(t(),a("div",j)):f("",!0),(t(!0),a(g,null,y((_=e.customProps)==null?void 0:_.textBtns,(r,c)=>(t(),l(u,{key:c,size:"small",row:e.data,config:r},null,8,["row","config"]))),128))])])}}}),D=z(q,[["__scopeId","data-v-34c1ee17"]]);export{D as default};
