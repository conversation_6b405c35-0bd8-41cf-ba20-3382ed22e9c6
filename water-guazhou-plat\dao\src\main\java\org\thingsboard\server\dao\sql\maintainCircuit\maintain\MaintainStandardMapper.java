package org.thingsboard.server.dao.sql.maintainCircuit.maintain;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainStandard;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface MaintainStandardMapper extends BaseMapper<MaintainStandard> {

    List<MaintainStandard> getList(@Param("deviceTypeSerialIds") List<String> deviceTypeSerialIds, @Param("keywords") String keywords, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("deviceTypeSerialIds") List<String> deviceTypeSerialIds, @Param("keywords") String keywords, @Param("tenantId") String tenantId);
}
