package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyFlow;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyFlowPageRequest;

@Mapper
public interface SoConstructionAcceptFlowMapper extends BaseMapper<SoConstructionApplyFlow> {
    IPage<SoConstructionApplyFlow> findByPage(SoConstructionApplyFlowPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SoConstructionApplyFlow entity);

    boolean updateFully(SoConstructionApplyFlow entity);

}
