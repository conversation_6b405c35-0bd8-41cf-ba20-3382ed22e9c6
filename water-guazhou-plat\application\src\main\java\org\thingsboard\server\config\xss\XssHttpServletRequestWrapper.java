package org.thingsboard.server.config.xss;

import org.apache.commons.lang3.StringUtils;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.util.*;

public class XssHttpServletRequestWrapper /*extends HttpServletRequestWrapper */{
/*
    // 敏感词库
    private static String words = "腐败,贪污,gcd,共贪党,gongchandang,阿共,共一产一党,产党共,公产党,工产党,共c党,共x党,共铲,供产,共惨,供铲党,供铲谠,供铲裆,共残党,共残主义,共产主义的幽灵,拱铲,老共,中珙,中gong,gc党,贡挡,gong党,g产,狗产蛋,共残裆,恶党,邪党,共产专制,共产王朝,裆中央,土共,土g,共狗,g匪,共匪,仇共,共产党腐败,共产党专制,共产党的报应,共产党的末日,共产党专制,communistparty,症腐,政腐,政付,正府,政俯,政f,zhengfu,政zhi,挡中央,档中央,中国zf,中央zf,国wu院,中华帝国,gong和,大陆官方,北京政权,刘志军,张曙,刘志军,买别墅,玩女人,贪20亿,许宗衡,贪财物,李启红,贪腐财富,落马,高官名单,陈希同,贪污,玩忽职守,有期徒刑,陈良宇,受贿罪,滥用职权,有期徒刑,没收个人财产,成克杰,死刑,程维高,严重违纪,开除党籍,撤销职务,刘方仁,无期徒刑,倪献策,徇私舞弊,梁湘,以权谋私,撤职。,李嘉廷,死刑缓期,张国光,韩桂芝,宋平顺,自杀,黄瑶,双规,陈绍基,判处死刑,剥夺政治权利终身,没收个人全部财产,石兆彬,侯伍杰,王昭耀,剥夺政治权利,杜世成,沈图,叛逃美国,罗云光,起诉,张辛泰,李效时,边少斌,徐鹏航,违纪,收受股票,王乐毅,李纪周,郑光迪,田凤山。,邱晓华,郑筱萸,孙鹤龄,蓝田造假案,于幼军,留党察看,何洪达,朱志刚,杨汇泉,官僚主义,徐炳松,托乎提沙比尔,王宝森,经济犯罪,畏罪自杀。,陈水文,孟庆平,胡长清,朱川,许运鸿,丘广钟,刘知炳,丛福奎,王怀忠,巨额财产,来源不明罪,李达昌,刘长贵,王钟麓,阿曼哈吉,付晓光,自动辞,刘克田,吕德彬,刘维明,双开,刘志华,孙瑜,李堂堂,韩福才 青海,欧阳德 广东,韦泽芳 海南,铁英 北京,辛业江 海南,于飞 广东,姜殿武 河北,秦昌典 重庆,范广举 黑龙江,张凯广东,王厚宏海南,陈维席安徽,王有杰河南,王武龙江苏,米凤君吉林,宋勇辽宁,张家盟浙江,马烈孙宁夏,黄纪诚北京,常征贵州,王式惠重庆,周文吉,王庆录广西,潘广田山东,朱作勇甘肃,孙善武河南,宋晨光江西,梁春禄广西政协,鲁家善 中国交通,金德琴 中信,李大强 神华,吴文英 纺织,查克明 华能,朱小华光大,高严 国家电力,王雪冰,林孔兴,刘金宝,张恩照,陈同海,康日新,王益,张春江,洪清源,平义杰,李恩潮,孙小虹,陈忠,慕绥新,田凤岐,麦崇楷,柴王群,吴振汉,张秋阳,徐衍东,徐发 黑龙江,张宗海,丁鑫发,徐国健,李宝金,单平,段义和,荆福生,陈少勇,黄松有,皮黔生,王华元,王守业,刘连昆,孙晋美,邵松高,肖怀枢,刘广智 空军,姬胜德 总参,廖伯年 北京,爱女人,爱液,按摩棒,拔出来,爆草,包二奶,暴干,暴奸,暴乳,爆乳,暴淫,屄,被操,被插,被干,逼奸,仓井空,插暴,操逼,操黑,操烂,肏你,肏死,操死,操我,厕奴,插比,插b,插逼,插进,插你,插我,插阴,潮吹,潮喷,成人dv,成人电影,成人论坛,成人小说,成人电,成人电影,成人卡通,成人聊,成人片,成人视,成人图,成人文,成人小,成人电影,成人论坛,成人色情,成人网站,成人文学,成人小说,艳情小说,成人游戏,吃精,赤裸,抽插,扌由插,抽一插,春药,大波,大力抽送,大乳,荡妇,荡女,盗撮,多人轮,发浪,放尿,肥逼,粉穴,封面女郎,风月大陆,干死你,干穴,肛交,肛门,龟头,裹本,国产av,好嫩,豪乳,黑逼,后庭,后穴,虎骑,花花公子,换妻俱乐部,黄片,几吧,鸡吧,鸡巴,鸡奸,寂寞男,寂寞女,妓女,激情,集体淫,奸情,叫床,脚交,金鳞岂是池中物,金麟岂是池中物,精液,就去日,巨屌,菊花洞,菊门,巨奶,巨乳,菊穴,开苞,口爆,口活,口交,口射,口淫,裤袜,狂操,狂插,浪逼,浪妇,浪叫,浪女,狼友,聊性,流淫,铃木麻,凌辱,漏乳,露b,乱交,乱伦,轮暴,轮操,轮奸,裸陪,买春,美逼,美少妇,美乳,美腿,美穴,美幼,秘唇,迷奸,密穴,蜜穴,蜜液,摸奶,摸胸,母奸,奈美,奶子,男奴,内射,嫩逼,嫩女,嫩穴,捏弄,女优,炮友,砲友,喷精,屁眼,品香堂,前凸后翘,强jian,强暴,强奸处女,情趣用品,情色,拳交,全裸,群交,惹火身材,人妻,人兽,日逼,日烂,肉棒,肉逼,肉唇,肉洞,肉缝,肉棍,肉茎,肉具,揉乳,肉穴,肉欲,乳爆,乳房,乳沟,乳交,乳头,三级片,骚逼,骚比,骚女,骚水,骚穴,色逼,色界,色猫,色盟,色情网站,色区,色色,色诱,色欲,色b,少年阿宾,少修正,射爽,射颜,食精,释欲,兽奸,兽交,手淫,兽欲,熟妇,熟母,熟女,爽片,爽死我了,双臀,死逼,丝袜,丝诱,松岛枫,酥痒,汤加丽,套弄,体奸,体位,舔脚,舔阴,调教,偷欢,偷拍,推油,脱内裤,文做,我就色,无码,舞女,无修正,吸精,夏川纯,相奸,小逼,校鸡,小穴,小xue,写真,性感妖娆,性感诱惑,性虎,性饥渴,性技巧,性交,性奴,性虐,性息,性欲,胸推,穴口,学生妹,穴图,亚情,颜射,阳具,杨思敏,要射了,夜勤病栋,一本道,一夜欢,一夜情,一ye情,阴部,淫虫,阴唇,淫荡,阴道,淫电影,阴阜,淫妇,淫河,阴核,阴户,淫贱,淫叫,淫教师,阴茎,阴精,淫浪,淫媚,淫糜,淫魔,淫母,淫女,淫虐,淫妻,淫情,淫色,淫声浪语,淫兽学园,淫书,淫术炼金士,淫水,淫娃,淫威,淫亵,淫样,淫液,淫照,阴b,应召,幼交,幼男,幼女,欲火,欲女,玉女心经,玉蒲团,玉乳,欲仙欲死,玉穴,援交,原味内衣,援助交际,张筱雨,招鸡,招妓,中年美妇,抓胸,自拍,自慰,作爱,18禁,99bb,a4u,a4y,adult,amateur,anal,a片,fuck,gay片,g点,g片,hardcore,h动画,h动漫,incest,porn,secom,sexinsex,sm女王,xiao77,xing伴侣,tokyohot,yin荡,贱人,装b,大sb,傻逼,傻b,煞逼,煞笔,刹笔,傻比,沙比,欠干,婊子养的,我日你,我操,我草,卧艹,卧槽,爆你菊,艹你,cao你,你他妈,真他妈,别他吗,草你吗,草你丫,操你妈,擦你妈,操你娘,操他妈,日你妈,干你妈,干你娘,娘西皮,狗操,狗草,狗杂种,狗日的,操你祖宗,操你全家,操你大爷,妈逼,你麻痹,麻痹的,妈了个逼,马勒,狗娘养,贱比,贱b,下贱,死全家,全家死光,全家不得好死,全家死绝,白痴,无耻,sb,杀b,你吗b,你妈的,婊子,贱货,人渣,混蛋,媚外,和弦,兼职,限量,铃声,性伴侣,男公关,火辣,精子,射精,诱奸,强奸,做爱,性爱,发生关系,按摩,快感,处男,猛男,少妇,屌,屁股,下体,a片,内裤,浑圆,咪咪,发情,刺激,白嫩,粉嫩,兽性,风骚,呻吟,sm,阉割,高潮,裸露,不穿,一丝不挂,脱光,干你,干死,我干,裙中性运动,乱奸,乱伦,乱伦类,乱伦小,伦理大,伦理电影,伦理毛,伦理片,裸聊,裸聊网,裸体写真,裸舞视,裸照,美女裸体,美女写真,美女上门,美艳少妇,妹按摩,妹上门,迷幻药,迷幻藥,迷昏口,迷昏药,迷昏藥,迷魂香,迷魂药,迷魂藥,迷奸粉,迷奸药,迷情粉,迷情水,迷情药,迷药,迷藥,谜奸药,骚妇,骚货,骚浪,骚女,骚嘴,色电影,色妹妹,色情表演,色情电影,色情服务,色情图片,色情小说,色情影片,色情表演,色情电影,色情服务,色情片,色视频,色小说,性伴侣,性服务,性福情,性感少,性伙伴,性交,性交视频,性交图片,性奴,性奴集中营,性虐,阴唇,阴道,阴蒂,阴户,阴间来电,阴茎,阴茎增大,阴茎助勃,阴毛,陰唇,陰道,陰戶,淫荡,淫荡美女,淫荡视频,淫荡照片,淫乱,淫靡,淫魔,淫魔舞,淫女,淫情女,淫肉,淫騷妹,淫兽,淫兽学,淫水,淫穴,morphine,摇头丸,迷药,乖乖粉,narcotic,麻醉药,精神药品,爱女人,爱液,按摩棒,拔出来,爆草,包二奶,暴干,暴奸,暴乳,爆乳,暴淫,屄,被操,被插,被干,逼奸,仓井空,插暴,操逼,操黑,操烂,肏你,肏死,操死,操我,厕奴,插比,插b,插逼,插进,插你,插我,插阴,潮吹,潮喷,成人电影,成人论坛,成人色情,成人网站,成人文学,成人小说,艳情小说,成人游戏,吃精,赤裸,抽插,扌由插,抽一插,春药,大波,大力抽送,大乳,荡妇,荡女,盗撮,多人轮,发浪,放尿,肥逼,粉穴,封面女郎,风月大陆,干死你,干穴,肛交,肛门,龟头,裹本,国产av,好嫩,豪乳,黑逼,后庭,后穴,虎骑,花花公子,换妻俱乐部,黄片,几吧,鸡吧,鸡巴,鸡奸,寂寞男,寂寞女,妓女,激情,集体淫,奸情,叫床,脚交,金鳞岂是池中物,金麟岂是池中物,精液,就去日,巨屌,菊花洞,菊门,巨奶,巨乳,菊穴,开苞,口爆,口活,口交,口射,口淫,裤袜,狂操,狂插,浪逼,浪妇,浪叫,浪女,狼友,聊性,流淫,铃木麻,凌辱,漏乳,露b,乱交,乱伦,轮暴,轮操,轮奸,裸陪,买春,美逼,美少妇,美乳,美腿,美穴,美幼,秘唇,迷奸,密穴,蜜穴,蜜液,摸奶,摸胸,母奸,奈美,奶子,男奴,内射,嫩逼,嫩女,嫩穴,捏弄,女优,炮友,砲友,喷精,屁眼,品香堂,前凸后翘,强jian,强暴,强奸处女,情趣用品,情色,拳交,全裸,群交,惹火身材,人妻,人兽,日逼,日烂,肉棒,肉逼,肉唇,肉洞,肉缝,肉棍,肉茎,肉具,揉乳,肉穴,肉欲,乳爆,乳房,乳沟,乳交,乳头,三级片,骚逼,骚比,骚女,骚水,骚穴,色逼,色界,色猫,色盟,色情网站,色区,色色,色诱,色欲,色b,少年阿宾,少修正,射爽,射颜,食精,释欲,兽奸,兽交,手淫,兽欲,熟妇,熟母,熟女,爽片,爽死我了,双臀,死逼,丝袜,丝诱,松岛枫,酥痒,汤加丽,套弄,体奸,体位,舔脚,舔阴,调教,偷欢,偷拍,推油,脱内裤,文做,我就色,无码,舞女,无修正,吸精,夏川纯,相奸,小逼,校鸡,小穴,小xue,写真,性感妖娆,性感诱惑,性虎,性饥渴,性技巧,性交,性奴,性虐,性息,性欲,胸推,穴口,学生妹,穴图,亚情,颜射,阳具,杨思敏,要射了,夜勤病栋,一本道,一夜欢,一夜情,一ye情,阴部,淫虫,阴唇,淫荡,阴道,淫电影,阴阜,淫妇,淫河,阴核,阴户,淫贱,淫叫,淫教师,阴茎,阴精,淫浪,淫媚,淫糜,淫魔,淫母,淫女,淫虐,淫妻,淫情,淫色,淫声浪语,淫兽学园,淫书,淫术炼金士,淫水,淫娃,淫威,淫亵,淫样,淫液,淫照,阴b,应召,幼交,幼男,幼女,欲火,欲女,玉女心经,玉蒲团,玉乳,欲仙欲死,玉穴,援交,原味内衣,援助交际,张筱雨,招鸡,招妓,中年美妇,抓胸,自拍,自慰,作爱,18禁,99bb,a4u,a4y,adult,amateur,anal,a片,fuck,gay片,g点,g片,hardcore,h动画,h动漫,incest,porn,secom,sexinsex,sm女王,xiao77,xing伴侣,tokyohot,yin荡,福音会,中国教徒,统一教,观音法门,清海无上师,盘古,李洪志,志洪李,李宏志,轮功,法轮,轮法功,三去车仑,氵去车仑,发论工,法x功,法o功,法0功,法一轮一功,轮子功,车仑工力,法lun,fa轮,法lg,flg,fl功,falungong,大法弟子,大纪元,dajiyuan,明慧网,明慧周报,正见网,新唐人,伪火,退党,tuidang,退dang,超越红墙,自fen,真善忍,九评,9评,9ping,九ping,jiuping,藏字石,集体自杀,自sha,zi杀,suicide,titor,逢8必灾,逢八必灾,逢9必乱,逢九必乱,朱瑟里诺,根达亚文明,诺查丹玛斯,人类灭亡进程表,按照马雅历法,推背图,推bei图,济世灵文,诸世纪,电狗,电话定位器,电话拦截器,电话窃听,电话监,电话交友,电话追杀系统,电击枪,电鸡,电警棒,枪出售,枪的制,枪货到,枪决女犯,枪模,枪手,枪销售,枪械制,枪子弹,售步枪,售纯度,售单管,售弹簧刀,售防身,售狗子,售虎头,售火药,售假币,售健卫,售军用,售猎枪,售氯胺,售麻醉,售枪支,售热武,售三棱,售手枪,售五四,售一元硬,售子弹,售左轮,亚砷（酸）酐,亚砷酸钾,亚砷酸钠,亚硒酸,亚硒酸二钠,亚硒酸镁,亚硒酸钠,亚硒酸氢钠,亚硝酸乙酯,亚硝酰乙氧,氧化二丁基锡,氧化汞,氧化铊,氧化亚铊,氧氯化磷,原装弹,原子弹方法,原子弹清单,安眠酮,代血浆,普萘洛尔,呋塞米,西布曲明,testosterone,胰岛素样生长因子,促红细胞生成素,地西泮,尼可刹米,甲睾酮,adrenaline,erythropoietin,地奈德,莫达非尼,氯噻嗪,苯巴比妥,促性腺激素,泼尼松,麻黄草,雄烯二醇,地塞米松,tamoxifen,strychnine,androst,新型毒品,杜冷丁,兴奋剂,mdma,海洛因,海luo因,heroin,diamorphine,diacetylmorphine,鸦片,阿芙蓉,咖啡因,cocain,三唑仑,美沙酮,麻古,k粉,凯他敏,ketamine,冰毒,苯丙胺,cannabis,大麻,爱他死,氯胺酮,benzodiazepines,甲基安非他明,安非他命,吗啡,打人,打人,拆迁,拆迁,纠纷,纠纷,盗窃,盗窃,安眠酮,代药物毒品类：血浆,普萘洛尔,呋塞米,西布曲明,testosterone,胰岛素样生长因子,促红细胞生成素,地西泮,尼可刹米,甲睾酮,adrenaline,erythropoietin,地奈德,莫达非尼,氯噻嗪,苯巴比妥,促性腺激素,泼尼松,麻黄草,雄烯二醇,地塞米松,tamoxifen,strychnine,androst,新型毒品,杜冷丁,兴奋剂,mdma,海洛因,海luo因,heroin,diamorphine,diacetylmorphine,鸦片,阿芙蓉,咖啡因,cocain,三唑仑,美沙酮,麻古,k粉,凯他敏,ketamine,冰毒,苯丙胺,cannabis,大麻,爱他死,氯胺酮,benzodiazepines,甲基安非他明,安非他命,吗啡,morphine,摇头丸,迷药,乖乖粉,narcotic,麻醉药,精神药品,专业代理,帮忙点一下,帮忙点下,请点击进入,详情请进入,私人侦探,私家侦探,针孔摄象,调查婚外情,信用卡提现,无抵押贷款,广告代理,原音铃声,借腹生子,找个妈妈,找个爸爸,代孕妈妈,代生孩子,代开发票,腾讯客服电话,销售热线,免费订购热线,低价出售,款到发货,回复可见,连锁加盟,加盟连锁,免费二级域名,免费使用,免费索取,蚁力神,婴儿汤,售肾,刻章办,买小车,套牌车,玛雅网,电脑传讯,视频来源,下载速度,高清在线,全集在线,在线播放,txt下载,六位qq,6位qq,位的qq,个qb,送qb,用刀横向切腹,完全自杀手册,四海帮,足球投注,地下钱庄,中国复兴党,阿波罗网,曾道人,六合彩,改卷内幕,替考试,隐形耳机,出售答案,考中答案,答an,da案,资金周转,救市,股市圈钱,崩盘,资金短缺,证监会,质押贷款,小额贷款,周小川,刘明康,尚福林,孔丹,汉芯造假,杨树宽,中印边界谈判结果,喂奶门,摸nai门,酒瓶门,脱裤门,75事件,乌鲁木齐,新疆骚乱,针刺,打针,食堂涨价,饭菜涨价,h1n1,瘟疫爆发,yangjia,y佳,yang佳,杨佳,杨j,袭警,杀警,武侯祠,川b26931,贺立旗,周正毅,px项目,骂四川,家l福,家le福,加了服,麦当劳被砸,豆腐渣,这不是天灾,龙小霞,震其国土,yuce,提前预测,地震预测,隐瞒地震,李四光预测,蟾蜍迁徙,地震来得更猛烈,八级地震毫无预报,踩踏事故,聂树斌,万里大造林,陈相贵,张丹红,尹方明,李树菲,王奉友,零八奥运艰,惨奥,奥晕,凹晕,懊运,懊孕,奥孕,奥你妈的运,反奥,628事件,weng安,wengan,翁安,瓮安事件,化工厂爆炸,讨回工资,代办发票,代办各,代办文,代办学,代办制,代辦,代表烦,代开发票,代開,代考,代理发票,代理票据,代您考,代讨债,代写毕,代写论文,代孕,代追债,考后付款,考机构,考考邓,考联盟,考前答案,考前付,考前密卷,考前预测,考试,答案,考试,作弊器,考试包过,考试保,考试答案,考试机构,考试联盟,考试枪,考试作弊,考试作弊器,考研考中,考中答案,透视功能,透视镜,透视扑,透视器,透视眼睛,透视眼镜,透视药,透视仪,打死经过,打死人,打砸办公,打砸抢,安眠酮,代血浆,普萘洛尔,呋塞米,西布曲明,testosterone,胰岛素样生长因子,促红细胞生成素,地西泮,尼可刹米,甲睾酮,adrenaline,erythropoietin,地奈德,莫达非尼,氯噻嗪,苯巴比妥,促性腺激素,泼尼松,麻黄草,雄烯二醇,地塞米松,tamoxifen,strychnine,androst,新型毒品,杜冷丁,兴奋剂,mdma,海洛因,海luo因,heroin,diamorphine,diacetylmorphine,鸦片,阿芙蓉,咖啡因,cocain,三唑仑,美沙酮,麻古,k粉,凯他敏,ketamine,冰毒,苯丙胺,cannabis,大麻,爱他死,氯胺酮,benzodiazepines,甲基安非他明,安非他命,吗啡,KC短信,KC嘉年华,短信广告,短信群发,短信群发器,小6灵通,短信商务广告,段录定,无界浏览,无界浏览器,无界,无网界,无网界浏览,无帮国,KC提示,KC网站,UP8新势力,白皮书,UP新势力,移民,易达网络卡,安魂网,罢工,罢课,纽崔莱七折,手机复制,手机铃声,网关,神通加持法,全1球通,如6意通,清仓,灵动卡,答案卫星接收机,高薪养廉,考后付款,佳静安定片,航空母舰,航空售票,号码百事通,考前发放,成本价,诚信通手机商城,高利贷,联4通,黑庄,黑手党,黑车,联通贵宾卡,联总,联总这声传单,联总之声传单,高息贷款,高干子弟,恭喜你的号码,恭喜您的号码,高干子女,各个银行全称,各种发票,高官,高官互调,高官子女,喝一送一,卡号,复制,监听王,传单,旦科,钓鱼岛,钓鱼台,当官靠后台,党校安插亲信,传九促三,客户端非法字符,刻章,大麻树脂,大麻油,大法,大法弟子,dpp大法,fa lun,falu,发抡,发抡功,洗脑,下法轮,发轮,发伦,发伦功,发仑,发沦,发纶,发论,发论功,发论公,发正念,发囵,发愣,发瞟,罚抡,罚伦,罚仑,罚沦,罚纶,罚囵,筏抡,筏轮,筏伦,筏仑,筏沦,筏纶,筏论,筏囵,伐抡,伐轮,伐伦,伐仑,伐沦,伐论,伐囵,乏抡,乏轮,乏伦,乏仑,乏沦,乏纶,乏论,乏囵,阀抡,阀伦,阀仑,阀沦,阀纶,阀论,阀囵,法 轮 功,法*功,法.轮.功,法L功,法lun功,法功,法会,法抡,法抡功,法轮,法轮大法,法轮佛法,法轮功,法伦,法仑,法沦,法纶,法论,法十轮十功,法西斯,法院,法正,法谪,法谪功,法輪,法囵,法愣,珐.輪功,珐抡,珐轮,珐伦,珐仑,珐沦,五不,五不争鸣论坛,五出三进,五套功法,邝锦文,垡抡,垡轮,垡伦,垡仑,垡沦,垡纶,垡论,垡囵,茳澤民,荭志,闳志,闵维方,氵去,氵去车仑工力,转法轮,砝抡,砝轮,砝伦,砝仑,砝沦,砝纶,真、善、忍,真理教,真善美,真善忍,砝论,砝囵,泓志,屙民,珐纶,珐论,珐囵,falun,Falundafa,fa轮,Flg,弟子,地下教会,炼功,梦网洪志,轮大,抡功,轮功,伦功,摩门教,三水法轮,三唑仑,天皇,天怒,天葬,车臣,达赖,功法,讲法,基督,基督教,护法,回教,教徒,功友,大师,达赖喇嘛,虹志,鸿志,洪传,贯通两极法,光祖,洪吟,洪哲胜,洪志,宏法,观世音,宏志,弘志,古兰经,红志,车库,车仑,经文";
    private static List<String> wordsList = new ArrayList<>(Arrays.asList(words.split(",")));

    HttpServletRequest orgRequest = null;

    private String body;

    public XssHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
        orgRequest = request;
        body = HttpGetBody.getBodyString(request);
    }

    *//**
     * 覆盖getParameter方法，将参数名和参数值都做xss过滤。<br/>
     * 如果需要获得原始的值，则通过super.getParameterValues(name)来获取<br/>
     * getParameterNames,getParameterValues和getParameterMap也可能需要覆盖
     *//*
    @Override
    public String getParameter(String name) {
        String value = super.getParameter(xssEncode(name, 0));
        if (null != value) {
            value = xssEncode(value, 0);
        }
        return value;
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] values = super.getParameterValues(xssEncode(name, 0));
        if (values == null) {
            return null;
        }
        int count = values.length;
        String[] encodedValues = new String[count];
        for (int i = 0; i < count; i++) {
            encodedValues[i] = xssEncode(values[i], 0);
        }
        return encodedValues;
    }

    @Override
    public Map getParameterMap() {

        HashMap paramMap = (HashMap) super.getParameterMap();
        paramMap = (HashMap) paramMap.clone();

        for (Iterator iterator = paramMap.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry entry = (Map.Entry) iterator.next();
            String[] values = (String[]) entry.getValue();
            for (int i = 0; i < values.length; i++) {
                if (values[i] instanceof String) {
                    values[i] = xssEncode(values[i], 0);
                }
            }
            entry.setValue(values);
        }
        return paramMap;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        ServletInputStream inputStream = null;
        if (StringUtils.isNotEmpty(body)) {
            body = xssEncode(body, 1);
            inputStream = new TranslateServletInputStream(body);
        }
        return inputStream;
    }

    *//**
     * 覆盖getHeader方法，将参数名和参数值都做xss过滤。<br/>
     * 如果需要获得原始的值，则通过super.getHeaders(name)来获取<br/>
     * getHeaderNames 也可能需要覆盖
     *//*
    @Override
    public String getHeader(String name) {
        String value = super.getHeader(xssEncode(name, 0));
        if (value != null) {
            value = xssEncode(value, 0);
        }
        return value;
    }

    *//**
     * 将容易引起xss漏洞的半角字符直接替换成全角字符
     *
     * @param s
     * @return
     *//*
    private static String xssEncode(String s, int type) {
        *//*if (s == null || s.isEmpty()) {
            return s;
        }
        for (String string : wordsList) {
            if (s.contains(string)) {
                throw new RuntimeException("录入信息含有敏感词汇!");
            }
        }
        StringBuilder sb = new StringBuilder(s.length() + 16);
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (type == 0) {
                switch (c) {
                    case '\'':
                        // 全角单引号
                        sb.append('‘');
                        break;
                    *//**//*case '\"':
                        // 全角双引号
                        sb.append('“');
                        break;*//**//*
//                    case '>':
//                        // 全角大于号
//                        sb.append('＞');
//                        break;
//                    case '<':
//                        // 全角小于号
//                        sb.append('＜');
//                        break;
                    case '&':
                        // 全角&符号
                        sb.append('＆');
                        break;
                    case '\\':
                        // 全角斜线
                        sb.append('＼');
                        break;
                    case '#':
                        // 全角井号
                        sb.append('＃');
                        break;
                    // < 字符的 URL 编码形式表示的 ASCII 字符（十六进制格式） 是: %3c
                    case '%':
                        processUrlEncoder(sb, s, i);
                        break;
                    default:
                        sb.append(c);
                        break;
                }
            } else {
                switch (c) {
                    case '>':
                        // 全角大于号
                        sb.append('＞');
                        break;
                    case '<':
                        // 全角小于号
                        sb.append('＜');
                        break;
                    case '&':
                        // 全角&符号
                        sb.append('＆');
                        break;
                    *//**//*case '\\':
                        // 全角斜线
                        sb.append('＼');
                        break;*//**//*
                    case '#':
                        // 全角井号
                        sb.append('＃');
                        break;
                    // < 字符的 URL 编码形式表示的 ASCII 字符（十六进制格式） 是: %3c
                    case '%':
                        processUrlEncoder(sb, s, i);
                        break;
                    default:
                        sb.append(c);
                        break;
                }
            }

        }
        return sb.toString();*//*
        return s;
    }

    public static void processUrlEncoder(StringBuilder sb, String s, int index) {
        if (s.length() >= index + 2) {
            // %3c, %3C
            if (s.charAt(index + 1) == '3' && (s.charAt(index + 2) == 'c' || s.charAt(index + 2) == 'C')) {
                sb.append('＜');
                return;
            }
            // %3c (0x3c=60)
            if (s.charAt(index + 1) == '6' && s.charAt(index + 2) == '0') {
                sb.append('＜');
                return;
            }
            // %3e, %3E
            if (s.charAt(index + 1) == '3' && (s.charAt(index + 2) == 'e' || s.charAt(index + 2) == 'E')) {
                sb.append('＞');
                return;
            }
            // %3e (0x3e=62)
            if (s.charAt(index + 1) == '6' && s.charAt(index + 2) == '2') {
                sb.append('＞');
                return;
            }
        }
        sb.append(s.charAt(index));
    }

    *//**
     * 获取最原始的request
     *
     * @return
     *//*
    public HttpServletRequest getOrgRequest() {
        return orgRequest;
    }

    *//**
     * 获取最原始的request的静态方法
     *
     * @return
     *//*
    public static HttpServletRequest getOrgRequest(HttpServletRequest req) {
        if (req instanceof XssHttpServletRequestWrapper) {
            return ((XssHttpServletRequestWrapper) req).getOrgRequest();
        }
        return req;
    }*/
}