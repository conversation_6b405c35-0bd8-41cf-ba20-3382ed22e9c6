package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.store.DeviceSettleJournal;
import org.thingsboard.server.dao.sql.department.DeviceSettleJournalMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceSettleJournalPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceSettleJournalSaveRequest;

import java.util.List;

@Service
public class DeviceSettleJournalServiceImpl implements DeviceSettleJournalService {
    @Autowired
    private DeviceSettleJournalMapper mapper;

    @Override
    public IPage<DeviceSettleJournal> findAllConditional(DeviceSettleJournalPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public DeviceSettleJournal save(DeviceSettleJournalSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    public boolean update(DeviceSettleJournal entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<String> findSettleNames(String tenantId) {
        return mapper.findSettleNames(tenantId);
    }

}
