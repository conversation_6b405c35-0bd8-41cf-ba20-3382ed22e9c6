package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.common.data.menu.MenuPoolVO;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_TENANT_MENU_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class TenantMenus {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.MENU_CUSTOMER_PARENT_ID)
    private String parentId;

    @Column(name = ModelConstants.MENU_CUSTOMER_NAME_PROPERTY)
    private String name;

    @Column(name = ModelConstants.MENU_CUSTOMER_ICON)
    private String icon;

    @Column(name = ModelConstants.MENU_CUSTOMER_ORDER_NUM)
    private Integer orderNum;

    @Column(name = ModelConstants.MENU_CUSTOMER_URL)
    private String url;

    @Column(name = ModelConstants.MENU_CUSTOMER_ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private List<TenantMenus> children;

    public static MenuPoolVO toMenuPoolVO(TenantMenus tenantMenus) {
        MenuPoolVO vo = new MenuPoolVO();
        vo.setId(tenantMenus.getId());
        vo.setLabel(tenantMenus.getName());

        return vo;
    }

    public static List<MenuPoolVO> toMenuPoolVOList(List<TenantMenus> childrenTree) {
        List<MenuPoolVO> result = new ArrayList<>();
        for (TenantMenus tenantMenus : childrenTree) {
            MenuPoolVO menuPoolVO = toMenuPoolVO(tenantMenus);
            result.add(menuPoolVO);
        }

        return result;
    }
}
