package org.thingsboard.server.dao.maintainCircuit.circuit;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitPlanM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface CircuitPlanMService {
    PageData getList(String planName, String teamName, String userName, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId);

    CircuitPlanM getDetail(String mainId);

    CircuitPlanM save(CircuitPlanM circuitPlanM);

    IstarResponse delete(List<String> ids);

    void reviewer(CircuitPlanM circuitPlanM);

    List<CircuitPlanM> findAll();

    void execute(CircuitPlanM circuitPlanM) throws ParseException;

    PageData<Map> getMaintainList(String deviceLabelCode, int page, int size);
}
