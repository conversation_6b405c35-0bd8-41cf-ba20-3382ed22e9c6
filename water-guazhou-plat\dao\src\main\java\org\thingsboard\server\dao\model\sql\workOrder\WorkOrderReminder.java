package org.thingsboard.server.dao.model.sql.workOrder;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("work_order_reminder")
public class WorkOrderReminder {

    private String id;

    private String content;

    private String isDel;

    private Date createTime;

    private String tenantId;


}
