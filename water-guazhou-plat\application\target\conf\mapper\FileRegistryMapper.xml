<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.FileRegistryMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           file_name,
                           file_address,
                           label,
                           host,
                           upload_time,
                           tenant_id<!--@sql from file_registry -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.fileRegistry.FileRegistry">
        <result column="id" property="id"/>
        <result column="file_name" property="fileName"/>
        <result column="file_address" property="fileAddress"/>
        <result column="label" property="label"/>
        <result column="host" property="host"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from file_registry
        <where>
            <if test="fileName != null">
                and file_name like '%' || #{fileName} || '%'
            </if>
            <if test="label != null">
                and "label" = #{label}
            </if>
            <if test="host != null">
                and "host" = #{host}
            </if>
            <if test="fromTime != null">
                and upload_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and upload_time &lt;= #{toTime}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <update id="updateFully">
        update file_registry
        set file_name    = #{fileName},
            file_address = #{fileAddress},
            label        = #{label},
            host         = #{host}
        where id = #{id}
    </update>

    <update id="updateSelective">
        update file_registry
        <set>
            <if test="fileName != null">
                file_name = #{fileName},
            </if>
            <if test="fileAddress != null">
                file_address = #{fileAddress},
            </if>
            <if test="label != null">
                label = #{label},
            </if>
            <if test="host != null">
                host = #{host},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO file_registry(id,
                                  file_name,
                                  file_address,
                                  label,
                                  host,
                                  upload_time,
                                  tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.fileName},
             #{element.fileAddress},
             #{element.label},
             #{element.host},
             now(),
             #{element.tenantId})
        </foreach>
    </insert>

    <delete id="removeAll">
        delete
        from file_registry
        where label = #{label}
          and host = #{host}
    </delete>

</mapper>