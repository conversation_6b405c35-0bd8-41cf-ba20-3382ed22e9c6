<template>
  <div class="block-item">
    <slot>
      <img class="diamond" src="./diamond_light.png" alt="diamand" />
      <div class="info">
        <div class="text nowrap">{{ props.title }}</div>
        <div class="count nowrap">{{ props.content }}</div>
      </div>
    </slot>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{
  showDiamond?: boolean;
  title?: any;
  content?: any;
}>();
</script>
<style lang="scss" scoped>
.block-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border: 1px solid transparent;
  border-radius: 6px;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(
      to right,
      rgba(1, 27, 76, 0.8),
      rgba(1, 27, 76, 0.4)
    ),
    linear-gradient(90deg, rgba(77, 148, 255, 1), rgba(77, 148, 255, 0));
  display: flex;
  flex-wrap: nowrap;
  padding: 0 12px;
  padding: 0;
  .diamond {
    width: 24px;
    margin-left: 8px;
  }
  .info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: calc(100% - 60px);
  }
  .text {
    margin-right: 12px;
    font-size: 12px;
    text-wrap: nowrap;
    word-break: keep-all;
  }
  .count {
    font-size: 20px;
    font-weight: 400;
    font-family: 'font-lcd';
    word-break: keep-all;
    text-wrap: nowrap;
  }
  &:nth-child(odd) {
    .count {
      color: #65ffbe;
    }
  }
  &:nth-child(even) {
    .count {
      color: #eda95a;
    }
  }
}

.linear-title {
  position: relative;
  height: 48px;
  color: rgba(233, 251, 255, 1);
  box-shadow: 0 0 20px 0 rgba(0, 133, 255, 0.5) inset;
  font-size: 20px;
  line-height: 28px;
  padding: 10px;
  padding-left: 28px;
  background: linear-gradient(
    90deg,
    rgba(0, 133, 255, 0.6) 0%,
    rgba(0, 133, 255, 0) 100%
  );
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 2px;
    background: linear-gradient(
      180deg,
      rgba(0, 240, 255, 0.8) 0%,
      rgba(255, 255, 255, 0.9) 100%
    );
  }
  &::after {
    content: '';
    position: absolute;
    left: 14px;
    top: 10px;
    height: 28px;
    width: 4px;
    background: linear-gradient(
      360deg,
      rgba(0, 240, 255, 0.8) 0%,
      rgba(255, 255, 255, 0.9) 100%
    );
  }

  .dots {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    flex-wrap: nowrap;
    .dot {
      width: 8px;
      height: 8px;
      margin-left: 8px;
      &:nth-child(1) {
        background: rgba(0, 133, 255, 0.2);
      }
      &:nth-child(2) {
        background: rgba(0, 133, 255, 0.6);
      }
      &:nth-child(3) {
        background: rgba(0, 133, 255, 1);
      }
    }
  }
  .title-text {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
