<!-- 故障知识库 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    />
    <DialogForm
      ref="refForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'

import { formatDate } from '@/utils/DateFormatter'
import { getDeviceTypeTree } from '@/api/equipment_assets/equipmentManage'
import { getFaultKnowledgeSerch, postFaultKnowledge, deleteFaultKnowledge } from '@/api/equipment_assets/malfunctionRepair'
import { traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const router = useRouter()

const refForm = ref<IDialogFormIns>()

const refSearch = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '故障类别名称', field: 'name', type: 'input', labelWidth: '100px' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole('新建')
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '类别名称', prop: 'name' },
    { label: '故障类别说明', prop: 'remark' },
    { label: '添加人', prop: 'creatorName' },
    { label: '添加时间', prop: 'createTime', formatter: row => formatDate(row.createTime, 'YYYY-MM-DD HH:mm') }
  ],
  operationWidth: '300px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: row => clickEdit(row)
    },
    {
      type: 'primary',
      text: '故障信息',
      icon: ICONS.DETAIL,
      perm: $btnPerms('RoleManageEdit'),
      click: row => openDetail(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: row => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: '120px',
  dialogWidth: '500px',
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true
    params.serialId = TreeData.currentProject.serialId
    let val = '新增成功'
    if (params.id) { val = '修改成功' }
    postFaultKnowledge(params).then(() => {
      addOrUpdateConfig.submitting = false
      ElMessage.success(val)
      refreshData()
      refForm.value?.closeDialog()
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'textarea',
          label: '故障类别名称',
          field: 'name',
          rules: [{ required: true, message: '请输入故障类别名称' }]
        },
        {
          type: 'textarea',
          label: '故障类别说明',
          field: 'remark',
          rules: [{ required: true, message: '请输入故障类别说明' }]
        }
      ]
    }
  ]
})

const TreeData = reactive<SLTreeConfig>({
  title: ' ',
  data: [],
  currentProject: {},
  expandOnClickNode: false,
  isFilterTree: true,
  treeNodeHandleClick: data => {
    TreeData.currentProject = data
    refreshData()
  }
})

const clickCreatedRole = (title?: any) => {
  addOrUpdateConfig.title = title || '编辑'
  addOrUpdateConfig.defaultValue = {}
  refForm.value?.openDialog()
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  addOrUpdateConfig.defaultValue = { ...(row) || {} }
  refForm.value?.openDialog()
}

const openDetail = (row: any) => {
  router.push({
    name: 'knowledgeBaseDetail',
    query: {
      id: row.id,
      title: row.name
    }
  })
}

const handleDelete = (row: { id: string }) => {
  SLConfirm('确定删除指定故障类别?', '删除提示').then(() => {
    deleteFaultKnowledge([row.id]).then(() => {
      ElMessage.success('删除成功')
      refreshData()
    })
  })
}

function init() {
  getDeviceTypeTree().then(res => {
    TreeData.data = traverse(res.data.data || [])
    TreeData.currentProject = res.data.data[0]
    refreshData()
  })
}

function refreshData() {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    deviceTypeId: TreeData.currentProject.id,
    ...(refSearch.value?.queryParams || {})
  }
  getFaultKnowledgeSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(async () => {
  init()
})

</script>
