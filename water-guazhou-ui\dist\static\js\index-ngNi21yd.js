import{_ as D}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as v}from"./CardSearch-CB_HNR-Q.js";import{d as C,M as q,c as p,s as E,r as m,x as n,S as M,o as N,g as R,n as w,q as u,i as d,b7 as I}from"./index-r0dFAfgr.js";import{I as f}from"./common-CvK_P_ao.js";import{j as T,k as V,l as B,g as F}from"./equipmentOutStock-BiNkB8x8.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const A={class:"wrapper"},Q=C({__name:"index",setup(L){const{$btnPerms:s}=q(),c=p(),i=p(),g=p({filters:[{label:"仓库编码",field:"code",type:"input"},{label:"仓库名称",field:"name",type:"input"},{label:"管理员",field:"managerId",type:"department-user"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:f.QUERY,click:()=>r()},{type:"default",perm:!0,text:"重置",svgIcon:E(I),click:()=>{var e;(e=c.value)==null||e.resetForm(),r()}},{perm:s("RoleManageAdd"),text:"新增",type:"success",icon:f.ADD,click:()=>b()}]}]}),l=m({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"仓库编码",prop:"code"},{label:"仓库名称",prop:"name"},{label:"仓库地址",prop:"address"},{label:"排序编号",prop:"orderNum"},{label:"管理员",prop:"managerName"},{label:"联系电话",prop:"tel"},{label:"备注/说明",prop:"remark"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime"}],operationWidth:"200px",operations:[{type:"primary",isTextBtn:!0,color:"#4195f0",text:"编辑",perm:s("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>_(e)},{isTextBtn:!0,type:"danger",text:"删除",icon:"iconfont icon-shanchu",perm:s("RoleManageDelete"),click:e=>h(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{l.pagination.page=e,l.pagination.limit=t,r()}}}),a=m({title:"新增",labelWidth:"100px",dialogWidth:"500px",submitting:!1,submit:e=>{delete e.department,a.submitting=!0,e.id?T(e.id,e).then(()=>{var t;r(),n.success("修改成功"),(t=i.value)==null||t.closeDialog(),a.submitting=!1}).catch(t=>{n.warning(t),a.submitting=!1}):V(e).then(()=>{var t;r(),n.success("添加成功"),(t=i.value)==null||t.closeDialog(),a.submitting=!1}).catch(t=>{n.warning(t),a.submitting=!1})},defaultValue:{},group:[{fields:[{type:"input",label:"仓库编码",field:"code",rules:[{required:!0,message:"请输入仓库编码"}]},{type:"input",label:"仓库名称",field:"name",rules:[{required:!0,message:"请输入仓库名称"}]},{type:"textarea",label:"仓库地址",field:"address",rules:[{required:!0,message:"请输入仓库地址"}]},{type:"input-number",label:"排序编号",field:"orderNum"},{type:"department-user",label:"管理员",field:"managerId",rules:[{required:!0,message:"请输入管理员"}]},{type:"input-number",label:"联系电话",field:"tel",rules:[{required:!0,message:"请输入联系电话"}]},{type:"textarea",label:"备注",field:"remark"}]}]}),b=()=>{var e;a.title="新增",a.defaultValue={},(e=i.value)==null||e.openDialog()},_=e=>{var t;a.title="编辑",a.defaultValue={...e||{}},(t=i.value)==null||t.openDialog()},h=e=>{M("确定删除该仓库, 是否继续?","删除提示").then(()=>{B(e.id).then(()=>{r(),n.success("删除成功")}).catch(t=>{n.warning(t)})})},r=async()=>{var t;const e={size:l.pagination.limit,page:l.pagination.page,...((t=c.value)==null?void 0:t.queryParams)||{}};delete e.department,F(e).then(o=>{l.dataList=o.data.data.data||[],l.pagination.total=o.data.data.total||0})};function y(){r()}return N(()=>{y()}),(e,t)=>{const o=v,x=S,k=D;return R(),w("div",A,[u(o,{ref_key:"refSearch",ref:c,config:d(g)},null,8,["config"]),u(x,{config:d(l),class:"card-table"},null,8,["config"]),u(k,{ref_key:"refForm",ref:i,config:d(a)},null,8,["config"])])}}});export{Q as default};
