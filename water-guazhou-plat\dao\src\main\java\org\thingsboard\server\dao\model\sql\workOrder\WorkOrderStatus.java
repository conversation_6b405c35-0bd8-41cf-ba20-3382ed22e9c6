package org.thingsboard.server.dao.model.sql.workOrder;

public enum WorkOrderStatus {
    // 创建
    PENDING("待派单", "上报"),
    ASSIGN("待接单", "指派"),
    // 处理流程
    RESOLVING("处理中", "开始处理"),
    ARRIVING("处理中", "到场"),
    PROCESSING("处理中", "处理"),
    REJECTED("审核退回", "审核退回"),
    // 审核流程
    SUBMIT("待审核", "完成"),
    REVIEW("复审中", "复审"),
    CHARGEBACK_REVIEW("审核中", "退单申请"),
    HANDOVER_REVIEW("审核中", "转单申请"),
    // 最终流程
    APPROVED("审核通过", "审核通过"),
    TERMINATED("已终止", "终止"),
    COMPLETE("完工", "完工"),

    // 话务工单
    CONCLUDE("已办结", "办结"),
    REVISIT("已回访", "回访"),
    FORWARD("已转发", "转发"),
    CHARGEBACK_REJECTED("退单驳回", "退单驳回"),


    // === 中间状态 ===
    REASSIGN(null, "变更处理人", RESOLVING),
    CHARGEBACK("已退单", "退单", PENDING),
    COLLABORATION(null, "申请协作"),
    ;

    private final String stageName;
    private final String detailName;
    private final WorkOrderStatus toStage;

    WorkOrderStatus(String stageName, String detailName) {
        this(stageName, detailName, null);
    }

    WorkOrderStatus(String stageName, String detailName, WorkOrderStatus toStage) {
        this.stageName = stageName;
        this.detailName = detailName;
        this.toStage = toStage;
    }

    public String getStageName() {
        return stageName;
    }

    public String getDetailName() {
        return detailName;
    }

    public boolean between(WorkOrderStatus from, WorkOrderStatus to) {
        return ordinal() >= from.ordinal() && ordinal() <= to.ordinal();
    }

    public boolean betweenOrEqualsAny(WorkOrderStatus from, WorkOrderStatus to, WorkOrderStatus... stages) {
        return between(from, to) || equalsAny(stages);
    }

    public boolean lessThanOrEquals(WorkOrderStatus other) {
        return ordinal() <= other.ordinal();
    }

    public boolean equalsAny(WorkOrderStatus... stages) {
        for (WorkOrderStatus stage : stages) {
            if (this.equals(stage))
                return true;
        }
        return false;
    }

    @Override
    public String toString() {
        return stageName + ":" + detailName;
    }

    public static WorkOrderStatus indexOf(int ordinal) {
        return values()[ordinal];
    }


    public WorkOrderStatus getToStage() {
        if (toStage == null) {
            return this;
        }
        return toStage;
    }

    public boolean in(WorkOrderStage stage) {
        return stage.contains(this);
    }
}
