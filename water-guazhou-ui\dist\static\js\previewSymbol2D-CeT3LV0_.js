import{cy as z,cx as v,cz as T,cA as Z,cB as B,cC as C,cD as P,l as F,cE as R,cF as U}from"./MapView-DaoQedLH.js";import{s as V}from"./Point-WxyopZva.js";import{n as W}from"./fontUtils-BuXIMW9g.js";import{u as E}from"./colorUtils-BY7h749P.js";import"./index-r0dFAfgr.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";const q="picture-fill",H="picture-marker",G="simple-fill",I="simple-line",J="simple-marker",K="text",N="Aa",Q=z.size,k=z.maxSize,X=z.maxOutlineSize,Y=z.lineWidth,j=225,_=document.createElement("canvas");function O(a,e){const n=_.getContext("2d"),s=[];return e&&(e.weight&&s.push(e.weight),e.size&&s.push(e.size+"px"),e.family&&s.push(e.family)),n.font=s.join(" "),n.measureText(a).width}const $=7.2/2.54,ee=72/2.54;function ae(a){if(a.length===0)return 0;if(a.length>2){const e=U(1),n=parseFloat(a);switch(a.slice(-2)){case"px":return n;case"pt":return n*e;case"in":return 72*n*e;case"pc":return 12*n*e;case"mm":return n*$*e;case"cm":return n*ee*e}}return parseFloat(a)}function x(a){const e=a==null?void 0:a.size;return{width:e!=null&&typeof e=="object"&&"width"in e?v(e.width):null,height:e!=null&&typeof e=="object"&&"height"in e?v(e.height):null}}async function ne(a,e){const n=e.fill,s=a.color;if((n==null?void 0:n.type)==="pattern"&&s&&a.type!==q){const h=await R(n.src,s.toCss(!0));n.src=h,e.fill=n}}async function se(a,e,n,s){var p,c,o;if(!("font"in a)||!a.font||e.shape.type!=="text")return;try{await W(a.font)}catch{}const{width:h}=x(s),u=/[\uE600-\uE6FF]/.test(e.shape.text);h!=null||u||(n[0]=O(e.shape.text,{weight:(p=e.font)==null?void 0:p.weight,size:(c=e.font)==null?void 0:c.size,family:(o=e.font)==null?void 0:o.family}))}function D(a,e){return a>e?"dark":"light"}function ie(a,e){var M;const n=typeof(e==null?void 0:e.size)=="number"?e==null?void 0:e.size:null,s=n!=null?v(n):null,h=(e==null?void 0:e.maxSize)!=null?v(e.maxSize):null,u=(e==null?void 0:e.rotation)!=null?e.rotation:"angle"in a?a.angle:null,p=T(a);let c=Z(a);le(a,245)!=="dark"||e!=null&&e.ignoreWhiteSymbols||(c={width:.75,...c,color:"#bdc3c7"});const o={shape:null,fill:p,stroke:c,offset:[0,0]};c!=null&&c.width&&(c.width=Math.min(c.width,X));const d=(c==null?void 0:c.width)||0;let g=(e==null?void 0:e.size)!=null&&((e==null?void 0:e.scale)==null||(e==null?void 0:e.scale)),i=0,l=0,b=!1;switch(a.type){case J:{const r=a.style,{width:m,height:t}=x(e),w=m===t&&m!=null?m:s??Math.min(v(a.size),h||k);switch(i=w,l=w,r){case"circle":o.shape={type:"circle",cx:0,cy:0,r:.5*w},g||(i+=d,l+=d);break;case"cross":o.shape={type:"path",path:[{command:"M",values:[0,.5*l]},{command:"L",values:[i,.5*l]},{command:"M",values:[.5*i,0]},{command:"L",values:[.5*i,l]}]};break;case"diamond":o.shape={type:"path",path:[{command:"M",values:[0,.5*l]},{command:"L",values:[.5*i,0]},{command:"L",values:[i,.5*l]},{command:"L",values:[.5*i,l]},{command:"Z",values:[]}]},g||(i+=d,l+=d);break;case"square":o.shape={type:"path",path:[{command:"M",values:[0,0]},{command:"L",values:[i,0]},{command:"L",values:[i,l]},{command:"L",values:[0,l]},{command:"Z",values:[]}]},g||(i+=d,l+=d),u&&(b=!0);break;case"triangle":o.shape={type:"path",path:[{command:"M",values:[.5*i,0]},{command:"L",values:[i,l]},{command:"L",values:[0,l]},{command:"Z",values:[]}]},g||(i+=d,l+=d),u&&(b=!0);break;case"x":o.shape={type:"path",path:[{command:"M",values:[0,0]},{command:"L",values:[i,l]},{command:"M",values:[i,0]},{command:"L",values:[0,l]}]},u&&(b=!0);break;case"path":o.shape={type:"path",path:a.path||""},g||(i+=d,l+=d),u&&(b=!0),g=!0}break}case I:{const{width:r,height:m}=x(e),t=m??s??d,w=r??Y;c&&(c.width=t),i=w,l=t;const y=((M=o==null?void 0:o.stroke)==null?void 0:M.cap)||"butt",f=y==="round";g=!0,o.stroke&&(o.stroke.cap=y==="butt"?"square":y),o.shape={type:"path",path:[{command:"M",values:[f?t/2:0,l/2]},{command:"L",values:[f?i-t/2:i,l/2]}]};break}case q:case G:{const r=typeof(e==null?void 0:e.symbolConfig)=="object"&&(e==null?void 0:e.symbolConfig.isSquareFill),{width:m,height:t}=x(e);i=!r&&m!==t||m==null?s??Q:m,l=!r&&m!==t||t==null?i:t,g||(i+=d,l+=d),g=!0,o.shape=r?{type:"path",path:[{command:"M",values:[0,0]},{command:"L",values:[i,0]},{command:"L",values:[i,l]},{command:"L",values:[0,l]},{command:"L",values:[0,0]},{command:"Z",values:[]}]}:B.fill[0];break}case H:{const r=Math.min(v(a.width),h||k),m=Math.min(v(a.height),h||k),{width:t,height:w}=x(e),y=t===w&&t!=null?t:s??Math.max(r,m),f=r/m;i=f<=1?Math.ceil(y*f):y,l=f<=1?y:Math.ceil(y/f),o.shape={type:"image",x:-Math.round(i/2),y:-Math.round(l/2),width:i,height:l,src:a.url||""},u&&(b=!0);break}case K:{const r=a,m=(e==null?void 0:e.overrideText)||r.text||N,t=r.font,{width:w,height:y}=x(e),f=y??s??Math.min(v(t.size),h||k),A=O(m,{weight:t.weight,size:f,family:t.family}),L=/[\uE600-\uE6FF]/.test(m);i=w??(L?f:A),l=f;let S=.25*ae((t?f:0).toString());L&&(S+=5),o.shape={type:"text",text:m,x:r.xoffset||0,y:r.yoffset||S,align:"middle",alignBaseline:r.verticalAlignment,decoration:t&&t.decoration,rotated:r.rotated,kerning:r.kerning},o.font=t&&{size:f,style:t.style,decoration:t.decoration,weight:t.weight,family:t.family};break}}return{shapeDescriptor:o,size:[i,l],renderOptions:{node:e==null?void 0:e.node,scale:g,opacity:e==null?void 0:e.opacity,rotation:u,useRotationSize:b,effectView:e==null?void 0:e.effectView}}}async function pe(a,e){const{shapeDescriptor:n,size:s,renderOptions:h}=ie(a,e);if(!n.shape)throw new V("symbolPreview: renderPreviewHTML2D","symbol not supported.");await ne(a,n),await se(a,n,s,e);const u=[[n]];if(typeof(e==null?void 0:e.symbolConfig)=="object"&&(e!=null&&e.symbolConfig.applyColorModulation)){const p=.6*s[0];u.unshift([{...n,offset:[-p,0],fill:C(n.fill,-.3)}]),u.push([{...n,offset:[p,0],fill:C(n.fill,.3)}]),s[0]+=2*p,h.scale=!1}return P(u,s,h)}function le(a,e=j){const n=T(a),s=Z(a),h=!n||"type"in n?null:new F(n),u=s!=null&&s.color?new F(s==null?void 0:s.color):null,p=h?D(E(h),e):null,c=u?D(E(u),e):null;return c?p?p===c?p:e>=j?"light":"dark":c:p}export{le as getContrastingBackgroundTheme,ie as getRenderSymbolParameters,pe as previewSymbol2D};
