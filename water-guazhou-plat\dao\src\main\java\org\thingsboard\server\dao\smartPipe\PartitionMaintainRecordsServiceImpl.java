package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMaintainRecords;
import org.thingsboard.server.dao.sql.smartPipe.PartitionMaintainRecordsMapper;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PartitionMaintainRecordsServiceImpl implements PartitionMaintainRecordsService {

    @Autowired
    private PartitionMaintainRecordsMapper partitionMaintainRecordsMapper;

    @Override
    public PartitionMaintainRecords save(PartitionMaintainRecords partitionMaintainRecords) {
        if (StringUtils.isBlank(partitionMaintainRecords.getId())) {
            partitionMaintainRecords.setCreateTime(new Date());
            partitionMaintainRecordsMapper.insert(partitionMaintainRecords);
        } else {
            partitionMaintainRecordsMapper.updateById(partitionMaintainRecords);
        }
        return partitionMaintainRecords;
    }


    @Override
    public PageData<PartitionMaintainRecords> getList(PartitionMountRequest request) {
        IPage<PartitionMaintainRecords> page = new Page<>(request.getPage(), request.getSize());
        IPage<PartitionMaintainRecords> result = partitionMaintainRecordsMapper.getList(page, request);
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        partitionMaintainRecordsMapper.deleteBatchIds(ids);
    }

}
