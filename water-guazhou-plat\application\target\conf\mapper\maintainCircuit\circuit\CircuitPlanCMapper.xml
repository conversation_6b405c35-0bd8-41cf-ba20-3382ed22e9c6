<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitPlanCMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitPlanC">

        select a.*
        from tb_device_circuit_plan_c a
            where a.main_id = #{mainId}
            order by a.create_time desc

    </select>
    <select id="getCircuitList" resultType="java.util.Map">
        select e.name, e.execution_days as "limitDays", e.interval_days as "cycleDays", e.user_id as "userId", e.start_time as "startTime", e.execution_num as "executionNum"
        from tb_device_circuit_point_c a
                 left join device_storage_journal b on a.device_label_code = b.device_label_code
                 left join tb_device_circuit_point_m c on a.main_id = c.id
                 left join tb_device_circuit_plan_c d on d.point_id = c.id
                 left join tb_device_circuit_plan_m e on d.main_id = e.id

        where a.device_label_code = #{deviceLabelCode} and e.name is not null
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getCircuitListCount" resultType="int">
        select count(*)
            from tb_device_circuit_point_c a
            left join device_storage_journal b on a.device_label_code = b.device_label_code
            left join tb_device_circuit_point_m c on a.main_id = c.id
            left join tb_device_circuit_plan_c d on d.point_id = c.id
            left join tb_device_circuit_plan_m e on d.main_id = e.id

        where a.device_label_code = #{deviceLabelCode} and e.name is not null
    </select>

</mapper>