package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class CircuitTaskPageRequest extends AdvancedPageableQueryEntity<CircuitTask, CircuitTaskPageRequest> {
    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    private String type;

    // 任务编号
    private String code;

    // 任务名称
    private String name;

    // 任务类型。常规任务、临时任务。常规任务为巡检计划生成的任务；临时任务为任务页面新增的任务
    private String taskType;

    // 执行巡检人员
    private String executionUserId;

    // 执行巡检人员部门id
    private String executionUserDepartmentId;

    // 巡检成果审核人员
    private String auditUserId;

    // 预计开始时间
    private String startTime;

    // 预计结束时间
    private String endTime;

    // 实际开始时间
    private String realStartTime;

    // 实际结束时间
    private String realEndTime;

    // 创建人
    private String creator;

    // 要巡检的站点
    private String stationId;

    // 巡检模板
    private String templateId;

    // 任务状态
    private String status;

    public Date getStartTime() {
        return toDate(startTime);
    }

    public Date getEndTime() {
        return toDate(endTime);
    }

    public Date getRealStartTime() {
        return toDate(realStartTime);
    }

    public Date getRealEndTime() {
        return toDate(realEndTime);
    }
}
