package org.thingsboard.server.dao.sql.dma;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.dma.DmaWarnListEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@SqlDao
public interface DmaWarnListRepository extends CrudRepository<DmaWarnListEntity, String> {

    @Query(value = "select a from DmaWarnListEntity a, DmaPartitionEntity b where a.partitionId = b.id and a.partitionId in ?1 and b.name like ?2 limit ?3 offset ?4" , nativeQuery = true)
    List<DmaWarnListEntity> findAllByPartitionIdAndName(List<String> partitionId, String name, int page, int size);
}
