<template>
  <div class="sidebar-container" :class="[{ dark: useAppStore().isDark }, { collapsed: isCollapsed }]">
    <el-scrollbar class="side-menu-scrollbar" wrap-class="scrollbar-wrapper">
      <el-menu
        v-if="state.showMenu"
        :key="expandDefault[0]"
        ref="refMenu"
        mode="vertical"
        router
        :show-timeout="200"
        :default-active="state.activePath"
        :unique-opened="true"
        :default-openeds="expandDefault"
        :collapse="isCollapsed"
      >
        <template v-for="(route, index) in usePermissionStore().routers">
          <!-- 只处理二级及以上的，因为第一级为布局 -->
          <sidebar-item
            v-if="route.children?.length"
            :key="index"
            :item="route"
            :is-nest="false"
            :icon="route.meta?.icon"
            :collapsed="isCollapsed"
          ></sidebar-item>
        </template>
      </el-menu>
    </el-scrollbar>
    <div class="sidebar-toggle-bottom" @click="toggleCollapse">
      <el-icon>
        <component :is="isCollapsed ? 'ArrowRight' : 'ArrowLeft'" />
      </el-icon>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAppStore, usePermissionStore } from '@/store';
import SidebarItem from './SidebarItem.vue';
import { IELMenu } from '@/common/types/element-plus';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

const router = useRouter();
const refMenu = ref<IELMenu>();
const isCollapsed = ref(false);
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
const state = reactive<{
  activePath: string;
  showMenu: boolean;
}>({
  activePath: router.currentRoute.value.fullPath,
  showMenu: false
});
watch(
  () => router.currentRoute.value.fullPath,
  () => {
    if (state.activePath !== router.currentRoute.value.fullPath) {
      state.activePath = router.currentRoute.value.fullPath;
    }
  }
);
const expandDefault = computed(
  () =>
    [
      usePermissionStore().routers.filter((item) => item.hidden !== true)[0]?.path
    ] || []
);

onMounted(() => {
  state.showMenu = true;
});
</script>

<style lang="scss" scoped>
.sidebar-container {
  user-select: none;
  border-right: 1px solid var(--el-border-color);
  height: 100%;
  width: 200px;
  position: relative;
  display: flex;
  flex-direction: column;
  transition: width 0.2s;
  &.collapsed {
    width: 60px;
    .el-menu {
      width: 60px !important;
      min-width: 60px !important;
    }
  }
  &.dark {
    background-color: #1d1f2e;
    border: none;
  }
}
.sidebar-toggle-bottom {
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-top: 1px solid #e4e7ed;
  background: #fff;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 10;
  transition: width 0.2s;
}
.el-menu {
  border: none;
  height: 100%;
  width: 200px;
  min-width: 200px;
  transition: width 0.2s, min-width 0.2s;
}
// 鼠标移入菜单项字体变色，背景不变
:deep(.el-menu-item), :deep(.el-sub-menu__title) {
  transition: color 0.2s;
}
:deep(.el-menu-item:hover), :deep(.el-sub-menu__title:hover) {
  color: #0080FF !important;
  background: none !important;
}
:deep(.el-menu-item.is-active), :deep(.el-sub-menu__title.is-active) {
  color: #0080FF !important;
  background: none !important;
}
</style>
