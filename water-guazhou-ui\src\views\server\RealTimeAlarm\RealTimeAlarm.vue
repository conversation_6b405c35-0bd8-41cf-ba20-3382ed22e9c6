<!-- 实时报警 -->
<template>
  <TreeBox v-loading="!!TreeData.loading">
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch ref="refSearch" :config="SearchConfig" />
    <CardTable :config="TableConfig" class="card-table" />
    <!-- 弹出窗 -->
    <ConfirmDialog
      v-if="state.confirmInfo.visible"
      :dialog-info="state.confirmInfo"
      @refresh="refreshData()"
    ></ConfirmDialog>
    <!-- <HandleDialog
            v-if="handleInfo.visible"
            :dialog-info="handleInfo"
            :device-name="deviceName"
            @refresh="refreshData(currentProject)"
          ></HandleDialog> -->
    <SLDrawer ref="HistoryTableref" :config="historyInfo">
      <HistoryTable
        v-if="state.historyInfo.visible"
        :dialog-info="state.historyInfo"
        :device-name="state.deviceName"
      ></HistoryTable>
    </SLDrawer>
    <SLDrawer ref="InfoTableref" :config="detailsInfo">
      <InfoTable
        v-if="state.detailsInfo.visible"
        :dialog-info="state.detailsInfo"
        :device-name="state.deviceName"
      ></InfoTable>
    </SLDrawer>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Check, Delete } from '@element-plus/icons-vue';
import {
  getAlarmRealTimeList,
  alarmConfirmByAuth,
  clearAlarmByAuth,
  getAlarmList
} from '@/api/alarm';
import { getDevicesAll } from '@/api/device';
import { removeSlash } from '@/utils/removeIdSlash'; // 处理id
import ConfirmDialog from './components/confirmDialog.vue';
import HistoryTable from './components/historyTable.vue';
import InfoTable from './components/infoTable.vue';
import useGlobal from '@/hooks/global/useGlobal';
import { useBusinessStore } from '@/store';
import { formatDate } from '@/utils/DateFormatter';
import { SLConfirm, SLMessage } from '@/utils/Message';

const businessStore = useBusinessStore();
const { $btnPerms } = useGlobal();
const refSearch = ref<ISearchIns>();
const HistoryTableref = ref<ISLDrawerIns>();
const InfoTableref = ref<ISLDrawerIns>();

const severityType = [
  { label: '紧急', color: '#F56C6C' },
  { label: '重要', color: '#E6A23C' }
];

const state = reactive<{
  alarmState: string;
  deviceName: Map<string, any>;
  alarmInfo: Map<string, any>;
  listFilter: boolean;
  filterItem: string;
  unconfirmed: number;
  unsolved: number;
  unconfirmedList: Map<string, any>;
  // 弹出窗配置
  confirmInfo: {
    visible: boolean;
    row: any;
    close: () => any;
  };
  handleInfo: {
    visible: boolean;
    row: any;
    close: () => any;
  };
  historyInfo: {
    visible: boolean;
    project: any;
    row: any;
    close: () => any;
  };
  detailsInfo: {
    visible: boolean;
    row: any;
    close: () => any;
  };
  // 批量 小窗 配置
  severityColor: Record<string, any>;

  alarmTypes: { offline: string; scope: string; change: string };
}>({
  alarmState: '',
  deviceName: new Map(),
  alarmInfo: new Map(),
  listFilter: false,
  filterItem: '',
  unconfirmed: 0,
  unsolved: 0,
  unconfirmedList: new Map(),
  // 弹出窗配置
  confirmInfo: {
    visible: false,
    row: {},
    close: () => {
      state.confirmInfo.visible = false;
    }
  },
  handleInfo: {
    visible: false,
    row: {},
    close: () => {
      state.handleInfo.visible = false;
    }
  },
  historyInfo: {
    visible: false,
    project: {},
    row: {},
    close: () => {
      state.historyInfo.visible = false;
    }
  },
  detailsInfo: {
    visible: false,
    row: {},
    close: () => {
      state.detailsInfo.visible = false;
    }
  },
  // 批量 小窗 配置
  severityColor: {
    提示: 'rgb(85,204,244)',
    次要: 'rgb(255,216,0)',
    重要: '#f58717',
    紧急: 'rgb(245,75,23)',
    严重: '#FF0000'
  },

  alarmTypes: { offline: '掉线报警', scope: '范围报警', change: '变动报警' }
});
const TreeData = reactive<SLTreeConfig>({
  title: '区域划分',
  data: businessStore.projectList,
  currentProject: businessStore.selectedProject,
  isFilterTree: true,
  treeNodeHandleClick: (data) => {
    // 设置当前选中项目信息
    TreeData.currentProject = data;
    businessStore.SET_selectedProject(data);
    refreshData();
    // 更新设备列表
  }
});
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      field: 'type',
      type: 'select',
      label: '报警类型',
      options: [
        { value: 'scope', label: '范围报警' },
        { value: 'change', label: '变动报警' },
        { value: 'offline', label: '掉线报警' }
      ]
    },
    {
      type: 'btn-group',
      btns: [
        {
          text: '查询',
          perm: true,
          icon: 'iconfont icon-chaxun',
          click: () => refreshData()
        },
        {
          text: '批量确认',
          svgIcon: shallowRef(Check),
          perm: $btnPerms('RealTimeAlarmMultiConfirm'),
          disabled: () => !TableConfig.selectList?.length,
          click: () => alarmConfirm()
        },
        {
          text: '批量解除',
          type: 'danger',
          svgIcon: shallowRef(Delete),
          perm: $btnPerms('RealTimeAlarmMultiRemove'),
          disabled: () => !TableConfig.selectList?.length,
          click: () => handleDelete()
        }
      ]
    }
  ]
});
const TableConfig = reactive<ICardTable>({
  loading: false,
  dataList: [],
  selectList: [],
  handleSelectChange: (val) => {
    TableConfig.selectList = val;
  },
  columns: [
    { prop: 'name', label: '报警名称', minWidth: 160 },
    { prop: 'showDeviceN', label: '报警设备', minWidth: 160 },
    { prop: 'showDeviceN', label: '报警类型', minWidth: 120 },
    { prop: 'cycleName', label: '周期', minWidth: 55 },
    { prop: 'alarmValue', label: '报警触发值', minWidth: 120 },
    { prop: 'recoverSet', label: '恢复触发值', minWidth: 120 },
    {
      prop: 'createdTime',
      label: '报警时间',
      minWidth: 180,
      icon: 'iconfont icon-shijian',
      formatter: (row) => formatDate(row.createdTime, 'YYYY-MM-DD HH:mm'),
      iconStyle: {
        color: '#69e850'
      }
    },
    {
      prop: 'severity',
      label: '报警级别',
      tag: true,
      tagColor: (row): string =>
        severityType.find((item) => item.label === row.severity)?.color || '',
      formatter: (row) =>
        severityType.find((item) => item.label === row.severity)?.label
    },
    {
      prop: 'confirm',
      label: '报警状态',
      minWidth: 240,
      cellStyle: (row) => {
        return row.confirm !== '未恢复 | 未确认' ? '#36a624' : '';
      }
    }
  ],
  operationFixed: 'right',
  operations: [
    {
      text: '强制解除',
      isTextBtn: true,
      perm: $btnPerms('RealTimeAlarmRemove'),
      icon: 'iconfont icon-xiangqing',
      click: (row) => handleDelete(row)
    },
    {
      text: '历史',
      perm: true,
      isTextBtn: true,
      icon: 'iconfont icon-xiangqing',
      click: (row) => rowHistory(row)
    },
    {
      text: '详情',
      perm: true,
      isTextBtn: true,
      icon: 'iconfont icon-xiangqing',
      click: (row) => rowInfo(row)
    }
  ],
  operationWidth: '260px',
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const historyInfo = reactive<IDrawerConfig>({
  title: '历史记录',
  group: []
});

const detailsInfo = reactive<IDrawerConfig>({
  title: '告警详情',
  group: []
});

// 点击项目获取项目信息
const refreshData = async () => {
  if (!TreeData.currentProject.disabled) {
    const query = refSearch.value?.queryParams || {};
    TableConfig.loading = true;
    // todo: 获取设备 存,然后或者对应报警
    const alarmSetRes = await getAlarmList();
    alarmSetRes.data.forEach((item) =>
      state.alarmInfo.set(removeSlash(item.id.id), item.details)
    );

    const paramsObj = {
      keyword: '水质',
      start: moment().subtract(1, 'year').startOf('day').valueOf(),
      end: new Date().getTime(),
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query
    };
    const res = await getAlarmRealTimeList(
      paramsObj,
      TreeData.currentProject.id
    );
    TableConfig.loading = false;
    handleAlarm(res.data);
  }
};

const handleAlarm = (val) => {
  const originalData = val.data;
  const data: any[] = [];
  state.unconfirmed = 0;
  state.unsolved = 0;
  const date = {
    day: '日',
    month: '月',
    year: '年'
  };

  // originalData.forEach(item => {
  for (const item of originalData) {
    // 未确认 CONFIRM_UNACK   已确认 CONFIRM_ACK   已恢复 RESTORE_ACK
    // 解除 CLEARED_ACK  强制解除 CLEAR_FORCED
    item.severityColor = state.severityColor[item.severity];
    if (item.status === 'CONFIRM_UNACK' || item.status === 'ACTIVE_ACK') {
      item.confirm = '未恢复 | 未确认';
      state.unconfirmed++;
      state.unsolved++;
      state.unconfirmedList.set(item.id.id, item.confirm);
    }
    if (item.status === 'CONFIRM_ACK') {
      item.confirm = '未恢复 | 已确认';
      state.unsolved++;
    }
    if (item.status === 'RESTORE_ACK') {
      item.confirm = '已恢复 | 未确认';
      state.unconfirmed++;
      state.unconfirmedList.set(item.id.id, item.confirm);
    }

    item.alarmType = state.alarmTypes[item.type];

    if (item.details !== null) {
      item.alarmRemarks = item.details.alarmRemarks;
      const info = state.alarmInfo.get(item.alarmJsonId);

      if (info) {
        item.alarmValue = info.attributeName + ': ' + info.alarmSetValue;
        item.recoverSet = info.recoverSetValue;
        item.alarmRemarks = info.alarmRemarks;
      } else {
        item.alarmValue = '此条设置已删除';
        item.recoverSet = '此条设置已删除';
        if (item.type === 'offline') {
          item.alarmValue = '-';
          item.recoverSet = '-';
        }
      }
    }

    if (item.alarmCycle) {
      item.cycleName = date[item.alarmCycle];
      item.recoverSet = '-';
    } else {
      item.cycleName = '';
    }

    item.info = [];

    if (item.details) {
      if (item.details.record) {
        for (const i of item.details.record) {
          const infoItem = {
            time: formatDate(parseInt(i.ts), 'YYYY-MM-DD HH:mm'),
            infoValue: i.info,
            status: i.status.toUpperCase() === 'ALARM' ? '触发报警' : '恢复'
          };
          item.info.push(infoItem);
        }
      }
      item.activeRemarks = item.details.activeRemarks;
    }

    const dName = state.deviceName.get(item.originator.id) || '设备已删除';

    item.name = (item.alarmJsonName || '掉线 - ') + dName;

    item.showDeviceN = dName;

    if (state.listFilter) {
      if (
        state.filterItem === 'unconfirmed' &&
        (item.status === 'CONFIRM_UNACK' || item.status === 'RESTORE_ACK')
      ) {
        data.push(item);
      }
      if (
        state.filterItem === 'unsolved' &&
        (item.status === 'CONFIRM_UNACK' || item.status === 'CONFIRM_ACK')
      ) {
        data.push(item);
      }
    } else {
      data.push(item);
    }
  }

  TableConfig.dataList = data;
  TableConfig.pagination.total = val.total;
};

const handleDelete = (row?: any) => {
  const successMessage = row ? '强制解除成功' : '批量解除成功';
  SLConfirm('确定解除告警, 是否继续?', '解除提示').then(() => {
    let ids: any[] = [];
    if (row) {
      ids = [removeSlash(row.id.id)];
    } else {
      ids =
        TableConfig.selectList?.map((item) => removeSlash(item.id.id)) || [];
    }

    // 批量解除
    clearAlarmByAuth({
      alarmId: ids
      // remark: this.form2.removeRemark.trim()
    }).then(() => {
      SLMessage.success(successMessage);
      refreshData();
    });
  });
};

const alarmConfirm = (row?: any) => {
  SLConfirm('确定解除目标告警, 是否继续?', '解除提示').then(() => {
    let ids: any[] = [];
    if (row) {
      ids = [removeSlash(row.id.id)];
      if (!state.unconfirmedList.get(row.id.id)) {
        SLMessage.error('只能选择未确认数据，请重选');
        return;
      }
    } else {
      ids =
        TableConfig.selectList?.map((item) => removeSlash(item.id.id)) || [];
      for (const item of TableConfig.selectList || []) {
        if (!state.unconfirmedList.get(item.id.id)) {
          SLMessage.error('只能选择未确认数据，请重选');
          return;
        }
      }
    }
    // 批量解除
    alarmConfirmByAuth({
      alarmId: ids
      // remark: this.form2.removeRemark.trim()
    }).then(() => {
      SLMessage.success('确认成功');
      refreshData();
    });
  });
};

const rowHistory = (row) => {
  state.historyInfo.row = row;
  state.historyInfo.project = TreeData.currentProject;
  state.historyInfo.visible = true;
  HistoryTableref.value?.openDrawer();
};
const rowInfo = (row) => {
  state.detailsInfo.row = row;
  state.detailsInfo.visible = true;
  InfoTableref.value?.openDrawer();
};
const initDeviceTree = async () => {
  const deviceRes = await getDevicesAll('info');
  if (deviceRes.data && deviceRes.data.length) {
    deviceRes.data.forEach((item) =>
      state.deviceName.set(item.id.id, item.name)
    );
  }
};
onMounted(() => {
  initDeviceTree();
});
</script>
