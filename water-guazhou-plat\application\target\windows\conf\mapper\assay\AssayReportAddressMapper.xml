<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.assay.AssayReportAddressMapper">

    <insert id="batchInsert">
        INSERT INTO tb_assay_report_address(id, report_id, address_id, tenant_id, create_time) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.reportId},
            #{element.addressId},
            #{element.tenantId},
            #{element.createTime}
            )
        </foreach>
    </insert>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.assay.AssayReportAddress">
        select a.*, b.name as addressName
        from tb_assay_report_address a
        left join tb_assay_base_setting b on a.address_id = b.id
        where a.report_id = #{reportId}
    </select>
</mapper>