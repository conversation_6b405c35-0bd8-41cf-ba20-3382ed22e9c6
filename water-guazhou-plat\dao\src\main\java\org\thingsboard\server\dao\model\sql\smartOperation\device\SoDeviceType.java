package org.thingsboard.server.dao.model.sql.smartOperation.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.smartOperation.construction.device.SoDeviceTypeMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.InfoViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;

import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoDeviceType implements Identifiable {
    // id
    @InfoViaMapper(name = "treePath", mapper = SoDeviceTypeMapper.class)
    private String id;

    // 类别编码
    private String serialId;

    // 名称
    private String name;

    // 父级id
    private String parentId;

    // 节点级别
    private Integer level;

    // 排序编号
    private Integer orderNum;

    // 备注
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

    public SoDeviceType() {
        
    }
    
    public SoDeviceType(String serialId, String name, Integer orderNum, String level, Date createTime) {
        this.serialId = serialId;
        this.name = name;
        this.orderNum = orderNum;
        this.level = Integer.parseInt(level);
        this.createTime = createTime;
    }
}
