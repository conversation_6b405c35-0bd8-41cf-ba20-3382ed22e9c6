package org.thingsboard.server.service.task.repair;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.utils.JavaScriptUtil;
import org.thingsboard.server.dao.model.DTO.TriggerDTO;
import org.thingsboard.server.dao.model.sql.*;
import org.thingsboard.server.dao.repair.MaintenanceJobService;
import org.thingsboard.server.dao.repair.MaintenancePlanService;
import org.thingsboard.server.dao.repair.RepairJobService;
import org.thingsboard.server.dao.repair.RepairPlanService;
import org.thingsboard.server.dao.timeseries.TimeseriesService;

import java.util.List;

/**
 * 维修保养定时任务
 */
@Slf4j
@Component
public class RepairAndMaintenanceTask {

    @Autowired
    private RepairJobService repairJobService;
    @Autowired
    private RepairPlanService repairPlanService;
    @Autowired
    private MaintenanceJobService maintenanceJobService;
    @Autowired
    private MaintenancePlanService maintenancePlanService;
    @Autowired
    private TimeseriesService timeseriesService;

    /**
     * 每15分钟检测一次数据, 过一遍维修和保养任务
     */
    @Scheduled(fixedRate = 900000)
    public void checkTrigger() {
        // 检测触发性维修计划
        checkRepairTrigger();

        // 检测触发性保养计划
        checkMaintenanceTrigger();

    }

    /**
     * 检测触发性维修计划
     */
    private void checkRepairTrigger() {
        // 查询所有预防性维修计划
        List<RepairPlanEntity> planList = repairPlanService.findPlanByType("2");
        // 遍历
        if (planList.size() > 0) {
            for (RepairPlanEntity plan : planList) {
                try {
                    // 查询trigger
                    RepairPlanTriggerEntity trigger = repairPlanService.findTrigger(plan.getId());
                    String detail = trigger.getDetail();
                    List<TriggerDTO> triggerList = JSON.parseArray(detail, TriggerDTO.class);
                    // 拼写判断语句
                    StringBuilder builder = new StringBuilder();
                    if (triggerList != null) {
                        for (int i = 0; i < triggerList.size(); i++) {
                            TriggerDTO triggerDTO = triggerList.get(i);
                            String attr = triggerDTO.getAttr();
                            String condition = triggerDTO.getCondition();
                            String value = triggerDTO.getValue();
                            String relation = triggerDTO.getRelation();

                            builder.append("${").append(attr).append("}")
                                    .append(condition).append(value);
                            if (triggerList.size() - 1 > i) {
                                builder.append(relation);
                            }
                        }
                    }
                    String script = builder.toString();
                    // 查询实际数据
                    List<TsKvEntry> tsDataList = timeseriesService.findAllLatest(
                            new TenantId(UUIDConverter.fromString(trigger.getTenantId())),
                            new DeviceId(UUIDConverter.fromString(trigger.getDeviceId()))).get();
                    if (tsDataList == null) {
                        continue;
                    }
                    for (TsKvEntry kv : tsDataList) {
                        long ts = kv.getTs();
                        if (System.currentTimeMillis() - ts < (30 * 60 * 1000)) {// 数据半小时内有效
                            String key = kv.getKey();
                            String data = kv.getValueAsString();
                            script = script.replace("${" + key + "}", data);
                        }
                    }
                    log.info("准备执行触发性维修任务判断语句: [{}]", script);
                    boolean execute = (boolean) JavaScriptUtil.execute(script);
                    if (execute) {// 触发任务
                        repairPlanService.buildTriggerJob(plan);
                    }
                } catch (Exception e) {
                    log.error("执行语句错误, 错误原因: ", e);
                }
            }
        }
    }

    /**
     * 检测触发性保养计划
     */
    private void checkMaintenanceTrigger() {
        // 查询所有预防性保养计划
        List<MaintenancePlanEntity> planList = maintenancePlanService.findPlanByType("2");
        // 遍历
        if (planList.size() > 0) {
            for (MaintenancePlanEntity plan : planList) {
                try {
                    // 查询trigger
                    RepairPlanTriggerEntity trigger = repairPlanService.findTrigger(plan.getId());
                    String detail = trigger.getDetail();
                    List<TriggerDTO> triggerList = JSON.parseArray(detail, TriggerDTO.class);
                    // 拼写判断语句
                    StringBuilder builder = new StringBuilder();
                    if (triggerList != null) {
                        for (int i = 0; i < triggerList.size(); i++) {
                            TriggerDTO triggerDTO = triggerList.get(i);
                            String attr = triggerDTO.getAttr();
                            String condition = triggerDTO.getCondition();
                            String value = triggerDTO.getValue();
                            String relation = triggerDTO.getRelation();

                            builder.append("${").append(attr).append("}")
                                    .append(condition).append(value);
                            if (triggerList.size() - 1 > i) {
                                builder.append(relation);
                            }
                        }
                    }
                    String script = builder.toString();
                    // 查询实际数据
                    List<TsKvEntry> tsDataList = timeseriesService.findAllLatest(
                            new TenantId(UUIDConverter.fromString(trigger.getTenantId())),
                            new DeviceId(UUIDConverter.fromString(trigger.getDeviceId()))).get();
                    if (tsDataList == null) {
                        continue;
                    }
                    for (TsKvEntry kv : tsDataList) {
                        long ts = kv.getTs();
                        if (System.currentTimeMillis() - ts < (30 * 60 * 1000)) {// 数据半小时内有效
                            String key = kv.getKey();
                            String data = kv.getValueAsString();
                            script = script.replace("${" + key + "}", data);
                        }
                    }
                    log.info("准备执行触发性保养任务判断语句: [{}]", script);
                    boolean execute = (boolean) JavaScriptUtil.execute(script);
                    if (execute) {// 触发任务
                        maintenancePlanService.buildTriggerJob(plan);
                    }
                } catch (Exception e) {
                    log.error("执行语句错误, 错误原因: ", e);
                }
            }
        }
    }


    /**
     * 生成维修和保养任务
     */
    @Scheduled(cron = "1 0 0 * * ?")
    public void generateTask() {
        // 生成维修任务
        repairTask();

        // 生成保养任务
        maintenanceTask();

    }

    /**
     * 生成维修任务
     */
    private void repairTask() {
        // 查询所有预防性维修计划
        List<RepairPlanEntity> planList = repairPlanService.findPlanByType("3");
        // 执行生成
        if (planList != null && planList.size() > 0) {
            for (RepairPlanEntity plan : planList) {
                repairPlanService.executePlan(plan, false);
            }
        }
    }

    /**
     * 生成保养任务
     */
    private void maintenanceTask() {
        // 查询所有预防性保养计划
        List<MaintenancePlanEntity> planList = maintenancePlanService.findPlanByType("3");
        if (planList != null && planList.size() > 0) {
            for (MaintenancePlanEntity plan : planList) {
                maintenancePlanService.executePlan(plan, false);
            }
        }
    }


}
