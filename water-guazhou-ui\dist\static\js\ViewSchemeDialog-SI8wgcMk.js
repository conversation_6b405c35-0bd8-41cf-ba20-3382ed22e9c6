import{d as T,a8 as U,r as F,c as S,am as w,g as B,h as I,F as l,p as s,q as e,G as h,bh as b,H as O,I as P,bU as j,bW as G,c6 as J,K as L,N as q,O as z,J as H,L as K,C as W}from"./index-r0dFAfgr.js";const A={class:"scheme-dialog"},M={class:"pump-config-section"},Q={class:"scheme-description"},X={class:"description-item"},Y={class:"description-item"},Z={class:"scheme-info"},$={class:"info-item"},ee={class:"info-item"},te={class:"dialog-footer"},ae=T({__name:"ViewSchemeDialog",props:{visible:{type:Boolean,default:!1},schemeData:{type:Object,default:()=>({})}},emits:["update:visible","edit"],setup(g,{emit:N}){const o=g,D=N,c=U({get:()=>o.visible,set:m=>D("update:visible",m)}),a=F({schemeName:"",schemeCode:"",pumpRoomName:"",schemeDescription:"",schemeRemark:"",status:1,createTime:"",creator:""}),_=S([]),y=m=>m?new Date(m).toLocaleString("zh-CN"):"-",v=()=>{if(o.schemeData&&Object.keys(o.schemeData).length>0){a.schemeName=o.schemeData.schemeName||"",a.schemeCode=o.schemeData.schemeCode||"",a.pumpRoomName=o.schemeData.pumpRoomName||"",a.schemeDescription=o.schemeData.schemeDescription||"",a.schemeRemark=o.schemeData.schemeRemark||"",a.status=o.schemeData.status||1,a.createTime=o.schemeData.createTime||"",a.creator=o.schemeData.creator||"";try{const m=o.schemeData.pumpGroupConfig||"[]",t=JSON.parse(m);_.value=t.map(d=>({pumpName:d.pumpDisplayName||d.pumpName||"未知泵机",fixedPower:d.fixedPower||"-",fixedFlow:d.fixedFlow||"-"}))}catch(m){console.error("解析泵组配置失败:",m),_.value=[]}}},f=()=>{c.value=!1},x=()=>{D("edit",o.schemeData),f()};return w(()=>o.visible,m=>{m&&v()}),w(()=>o.schemeData,()=>{o.visible&&v()},{deep:!0}),(m,t)=>{const d=O,p=P,i=j,u=G,C=J,E=L,r=q,R=z,V=H,k=K;return B(),I(k,{modelValue:c.value,"onUpdate:modelValue":t[5]||(t[5]=n=>c.value=n),title:"查看泵组方案",width:"800px","before-close":f},{footer:l(()=>[s("div",te,[e(V,{onClick:f},{default:l(()=>t[11]||(t[11]=[h("关闭")])),_:1}),e(V,{type:"primary",onClick:x},{default:l(()=>t[12]||(t[12]=[h("编辑")])),_:1})])]),default:l(()=>[s("div",A,[e(E,{model:a,"label-width":"100px"},{default:l(()=>[e(u,{gutter:20},{default:l(()=>[e(i,{span:12},{default:l(()=>[e(p,{label:"方案名称:"},{default:l(()=>[e(d,{modelValue:a.schemeName,"onUpdate:modelValue":t[0]||(t[0]=n=>a.schemeName=n),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(p,{label:"方案编码:"},{default:l(()=>[e(d,{modelValue:a.schemeCode,"onUpdate:modelValue":t[1]||(t[1]=n=>a.schemeCode=n),readonly:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(u,{gutter:20},{default:l(()=>[e(i,{span:12},{default:l(()=>[e(p,{label:"所属泵房:"},{default:l(()=>[e(d,{modelValue:a.pumpRoomName,"onUpdate:modelValue":t[2]||(t[2]=n=>a.pumpRoomName=n),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(p,{label:"方案状态:"},{default:l(()=>[e(C,{type:a.status===1?"success":"info"},{default:l(()=>[h(b(a.status===1?"启用":"禁用"),1)]),_:1},8,["type"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),s("div",M,[t[6]||(t[6]=s("div",{class:"section-title"},"泵组配置",-1)),e(R,{data:_.value,border:"",style:{width:"100%"}},{default:l(()=>[e(r,{type:"index",label:"序号",width:"60"}),e(r,{prop:"pumpName",label:"泵机名称","min-width":"150"}),e(r,{prop:"fixedPower",label:"额定功率",width:"120"}),e(r,{prop:"fixedFlow",label:"额定流量",width:"120"})]),_:1},8,["data"])]),s("div",Q,[e(u,{gutter:20},{default:l(()=>[e(i,{span:12},{default:l(()=>[s("div",X,[t[7]||(t[7]=s("label",null,"方案描述:",-1)),e(d,{modelValue:a.schemeDescription,"onUpdate:modelValue":t[3]||(t[3]=n=>a.schemeDescription=n),type:"textarea",rows:3,readonly:""},null,8,["modelValue"])])]),_:1}),e(i,{span:12},{default:l(()=>[s("div",Y,[t[8]||(t[8]=s("label",null,"方案备注:",-1)),e(d,{modelValue:a.schemeRemark,"onUpdate:modelValue":t[4]||(t[4]=n=>a.schemeRemark=n),type:"textarea",rows:3,readonly:""},null,8,["modelValue"])])]),_:1})]),_:1})]),s("div",Z,[e(u,{gutter:20},{default:l(()=>[e(i,{span:12},{default:l(()=>[s("div",$,[t[9]||(t[9]=s("label",null,"创建时间:",-1)),s("span",null,b(y(a.createTime)),1)])]),_:1}),e(i,{span:12},{default:l(()=>[s("div",ee,[t[10]||(t[10]=s("label",null,"创建人:",-1)),s("span",null,b(a.creator||"系统"),1)])]),_:1})]),_:1})])])]),_:1},8,["modelValue"])}}}),oe=W(ae,[["__scopeId","data-v-8667993e"]]);export{oe as default};
