import L from"./PopLayout-BP55MvL7.js";import{d as M,c as N,a8 as w,am as z,o as F,g as l,h as V,F as W,n as a,ax as p,p as i,aB as v,aJ as h,bh as n,aw as y,av as f,an as m,C as j}from"./index-r0dFAfgr.js";import"./Point-WxyopZva.js";const A={class:"label"},D={class:"count"},E={class:"unit"},H={class:"label"},I={class:"count"},J={class:"unit"},O={class:"label"},T={class:"count"},X={class:"label"},Y={class:"count"},q=M({__name:"ListWindow",props:{view:{},config:{},disableScroll:{type:Boolean}},emits:["highlight","more","back","toggled"],setup(k,{expose:$}){const r=N(),s=k,B=()=>{var t;(t=r.value)==null||t.open()},P=()=>{var t;(t=r.value)==null||t.close()},C=t=>{var e;(e=r.value)==null||e.toggle(t)},S=(t,e)=>{var o;(o=r.value)==null||o.setPosition(t,e)},g=w(()=>{const t=s.config.attributes.row;return t?Object.keys(t).map(e=>({label:e,value:t[e]==="Null"?void 0:t[e]})):[]}),d=w(()=>{var t;return((t=s.config.attributes.values)==null?void 0:t.length)||g.value.length});return z(()=>s.config.visible,t=>{var e;s.view&&t&&((e=r.value)==null||e.setPosition(s.view,{x:s.config.x,y:s.config.y,longitude:s.config.longitude,latitude:s.config.latitude}))}),F(()=>{var t;(t=s.view)==null||t.watch("extent",()=>{var e;s.view&&((e=r.value)==null||e.setPosition(s.view,{x:s.config.x,y:s.config.y,longitude:s.config.longitude,latitude:s.config.latitude}))})}),$({open:B,close:P,toggle:C,setPosition:S}),(t,e)=>(l(),V(L,{ref_key:"refParent",ref:r,title:t.config.title,visible:t.config.visible,"show-more":t.config.showMore,"show-back":t.config.showBack,status:t.config.status,"high-light":t.config.highLight,"background-color":t.config.bgColor,view:t.view,x:t.config.x,y:t.config.y,offsetx:t.config.offsetX,offsety:t.config.offsetY,onToggled:e[0]||(e[0]=o=>t.$emit("toggled",o)),onHighlight:e[1]||(e[1]=o=>t.$emit("highlight",t.config)),onMore:e[2]||(e[2]=o=>t.$emit("more",t.config.attributes)),onBack:e[3]||(e[3]=o=>t.$emit("back",t.config.attributes))},{default:W(()=>[t.config.attributes.values?(l(),a("div",{key:0,class:"list-wrapper",style:f(t.config.style)},[p(t.$slots,"default",{},()=>{var o,c;return[i("ul",{class:y(["attr-list",{"first-list":s.disableScroll!==!0&&((o=t.config.attributes.values)==null?void 0:o.length)&&t.config.attributes.values.length>6}]),style:f("animation-duration:"+d.value+"s;")},[(l(!0),a(v,null,h(t.config.attributes.values,(u,b)=>(l(),a("li",{key:b,class:"attr-list-item"},[i("span",A,n(u.label),1),i("span",D,n(u.value??"--"),1),i("span",E,n(u.unit),1)]))),128))],6),s.disableScroll!==!0&&((c=t.config.attributes.values)!=null&&c.length)&&t.config.attributes.values.length>6?(l(),a("ul",{key:0,class:"attr-list second-list",style:f("animation-duration:"+d.value+"s;")},[(l(!0),a(v,null,h(t.config.attributes.values,(u,b)=>(l(),a("li",{key:b,class:"attr-list-item"},[i("span",H,n(u.label),1),i("span",I,n(u.value??"--"),1),i("span",J,n(u.unit),1)]))),128))],4)):m("",!0)]},!0)],4)):t.config.attributes.row?(l(),a("div",{key:1,class:"list-wrapper",style:f(t.config.style)},[p(t.$slots,"default",{},()=>[i("ul",{class:y(["attr-list",{"first-list":g.value.length>6}]),style:f("animation-duration:"+d.value+"s;")},[(l(!0),a(v,null,h(g.value,(o,c)=>(l(),a("li",{key:c,class:"attr-list-item"},[i("span",O,n(o.label),1),i("span",T,n(o.value||"--"),1)]))),128))],6),g.value.length>6?(l(),a("ul",{key:0,class:y(["attr-list",{"second-list":g.value.length>6}]),style:f("animation-duration:"+d.value+"s;")},[(l(!0),a(v,null,h(g.value,(o,c)=>(l(),a("li",{key:c,class:"attr-list-item"},[i("span",X,n(o.label),1),i("span",Y,n(o.value||"--"),1)]))),128))],6)):m("",!0)],!0)],4)):p(t.$slots,"default",{key:2},void 0,!0)]),_:3},8,["title","visible","show-more","show-back","status","high-light","background-color","view","x","y","offsetx","offsety"]))}}),R=j(q,[["__scopeId","data-v-22530fab"]]);export{R as default};
