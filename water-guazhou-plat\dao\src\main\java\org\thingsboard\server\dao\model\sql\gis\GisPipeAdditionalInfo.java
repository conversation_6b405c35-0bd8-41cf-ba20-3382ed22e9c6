package org.thingsboard.server.dao.model.sql.gis;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

/**
 * GIS管网附加属性
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_GIS_PIPE_ADDITIONAL_INFO_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class GisPipeAdditionalInfo {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_GIS_PIPE_ADDITIONAL_INFO_OBJECTID)
    private String objectid;

    @Column(name = ModelConstants.TB_GIS_PIPE_ADDITIONAL_INFO_LAYERID)
    private String layerid;

    @Column(name = ModelConstants.TB_GIS_PIPE_ADDITIONAL_INFO_IMG)
    private String img;

    @Column(name = ModelConstants.TB_GIS_PIPE_ADDITIONAL_INFO_AUDIO)
    private String audio;

    @Column(name = ModelConstants.TB_GIS_PIPE_ADDITIONAL_INFO_VIDIO)
    private String vidio;

    @Column(name = ModelConstants.TB_GIS_PIPE_ADDITIONAL_INFO_FILES)
    private String files;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
