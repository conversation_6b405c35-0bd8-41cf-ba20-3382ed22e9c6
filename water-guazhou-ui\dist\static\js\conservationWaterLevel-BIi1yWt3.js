import{m as t}from"./index-r0dFAfgr.js";function s(e){return t({url:"/api/conservation-water-level/water-level",method:"post",data:e})}function n(e){return t({url:"/api/conservation-water-level/water-level",method:"put",data:e})}function i(e){return t({url:`/api/conservation-water-level/water-level/${e}`,method:"delete"})}function o(e){return t({url:"/api/conservation-water-level/water-level/list",method:"get",params:e})}function u(e,a,r){return t({url:"/api/conservation-water-level/water-level/statistics",method:"get",params:{stationId:e,startTime:a,endTime:r}})}function v(e){return t({url:"/api/conservation-water-level/water-level/batch-import",method:"post",data:e})}function c(e,a,r){return t({url:"/api/conservation-water-level/analysis/perform",method:"post",params:{stationId:e,startTime:a,endTime:r}})}function p(e){return t({url:"/api/conservation-water-level/analysis/list",method:"get",params:e})}function m(){return t({url:"/api/conservation-water-level/analysis/risk-statistics",method:"get"})}function d(e){return t({url:`/api/conservation-water-level/analysis/re-analysis/${e}`,method:"post"})}function w(e){return t({url:`/api/conservation-water-level/analysis/${e}`,method:"delete"})}export{p as a,u as b,v as c,w as d,o as e,i as f,m as g,c as p,d as r,s,n as u};
