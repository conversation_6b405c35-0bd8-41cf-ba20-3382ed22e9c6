import{s as y,u as L,S as V,i as k,e as c,y as u,b as R,o as f,m as q,a as C,x as M}from"./Point-WxyopZva.js";import{U as v,L as N}from"./pe-B8dP0-Ut.js";import{R as I}from"./index-r0dFAfgr.js";import{i as T}from"./originUtils-DOOsZebp.js";import{fs as U,cQ as F,w as E,eK as z,cS as J,f1 as D,aJ as B,fC as H,fD as Z,fE as G,aH as Q,aI as $}from"./MapView-DaoQedLH.js";import{n as W}from"./widget-BcWKanF2.js";import{getSiblingOfSameTypeI as X}from"./resourceUtils-DVO9IyPB.js";async function Y(a,s,e,t,o,r){let i=null;if(I(e)){const n=`${a}/nodepages/`,h=n+Math.floor(e.rootIndex/e.nodesPerPage);try{return{type:"page",rootPage:(await v(h,{query:{f:"json",token:t},responseType:"json",signal:r})).data,rootIndex:e.rootIndex,pageSize:e.nodesPerPage,lodMetric:e.lodSelectionMetricType,urlPrefix:n}}catch(d){I(o)&&o.warn("#fetchIndexInfo()","Failed to load root node page. Falling back to node documents.",h,d),i=d}}if(!s)return null;const l=`${a}/nodes/`,p=l+(s&&s.split("/").pop());try{return{type:"node",rootNode:(await v(p,{query:{f:"json",token:t},responseType:"json",signal:r})).data,urlPrefix:l}}catch(n){throw new y("sceneservice:root-node-missing","Root node missing.",{pageError:i,nodeError:n,url:p})}}let ee=null;function te(){return ee}async function O(a,s,e){if(!s||!s.resources)return;const t=s.portalItem===a.portalItem?new Set(a.paths):new Set;a.paths.length=0,a.portalItem=s.portalItem;const o=new Set(s.resources.toKeep.map(n=>n.resource.path)),r=new Set,i=[];o.forEach(n=>{t.delete(n),a.paths.push(n)});for(const n of s.resources.toUpdate)if(t.delete(n.resource.path),o.has(n.resource.path)||r.has(n.resource.path)){const{resource:h,content:d,finish:b,error:_}=n,w=X(h,W());a.paths.push(w.path),i.push(j({resource:w,content:d,compress:n.compress,finish:b,error:_},e))}else a.paths.push(n.resource.path),i.push(re(n,e)),r.add(n.resource.path);for(const n of s.resources.toAdd)i.push(j(n,e)),a.paths.push(n.resource.path);if(t.forEach(n=>{if(s.portalItem){const h=s.portalItem.resourceFromPath(n);i.push(h.portalItem.removeResource(h).catch(()=>{}))}}),i.length===0)return;const l=await L(i);V(e);const p=l.filter(n=>"error"in n).map(n=>n.error);if(p.length>0)throw new y("save:resources","Failed to save one or more resources",{errors:p})}async function j(a,s){var o,r;const e={...I(s)?s:{},compress:a.compress},t=await U(a.resource.portalItem.addResource(a.resource,a.content,e));if(t.ok!==!0)throw(o=a.error)==null||o.call(a,t.error),t.error;(r=a.finish)==null||r.call(a,a.resource)}async function re(a,s){var t,o;const e=await U(a.resource.update(a.content,s));if(e.ok!==!0)throw(t=a.error)==null||t.call(a,e.error),e.error;(o=a.finish)==null||o.call(a,a.resource)}const P="esri.layers.mixins.SceneService",m=k.getLogger(P),ce=a=>{let s=class extends a{constructor(){super(...arguments),this.spatialReference=null,this.fullExtent=null,this.heightModelInfo=null,this.minScale=0,this.maxScale=0,this.version={major:Number.NaN,minor:Number.NaN,versionString:""},this.copyright=null,this.sublayerTitleMode="item-title",this.title=null,this.layerId=null,this.indexInfo=null,this._debouncedSaveOperations=M(async(e,t,o)=>{switch(e){case x.SAVE:return this._save(t);case x.SAVE_AS:return this._saveAs(o,t)}})}readSpatialReference(e,t){return this._readSpatialReference(t)}_readSpatialReference(e){if(e.spatialReference!=null)return R.fromJSON(e.spatialReference);{const t=e.store,o=t.indexCRS||t.geographicCRS,r=o&&parseInt(o.substring(o.lastIndexOf("/")+1,o.length),10);return r!=null?new R(r):null}}readFullExtent(e,t,o){if(e!=null&&typeof e=="object"){const l=e.spatialReference==null?{...e,spatialReference:this._readSpatialReference(t)}:e;return E.fromJSON(l,o)}const r=t.store,i=this._readSpatialReference(t);return i==null||r==null||r.extent==null||!Array.isArray(r.extent)||r.extent.some(l=>l<A)?null:new E({xmin:r.extent[0],ymin:r.extent[1],xmax:r.extent[2],ymax:r.extent[3],spatialReference:i})}parseVersionString(e){const t={major:Number.NaN,minor:Number.NaN,versionString:e},o=e.split(".");return o.length>=2&&(t.major=parseInt(o[0],10),t.minor=parseInt(o[1],10)),t}readVersion(e,t){const o=t.store,r=o.version!=null?o.version.toString():"";return this.parseVersionString(r)}readTitlePortalItem(e){return this.sublayerTitleMode!=="item-title"?void 0:e}readTitleService(e,t){const o=this.portalItem&&this.portalItem.title;if(this.sublayerTitleMode==="item-title")return D(this.url,t.name);let r=t.name;if(!r&&this.url){const i=B(this.url);I(i)&&(r=i.title)}return this.sublayerTitleMode==="item-title-and-service-name"&&o&&(r=o+" - "+r),H(r)}set url(e){const t=Z({layer:this,url:e,nonStandardUrlAllowed:!1,logger:m});this._set("url",t.url),t.layerId!=null&&this._set("layerId",t.layerId)}writeUrl(e,t,o,r){G(this,e,"layers",t,r)}get parsedUrl(){const e=this._get("url"),t=N(e);return this.layerId!=null&&(t.path=`${t.path}/layers/${this.layerId}`),t}async _fetchIndexAndUpdateExtent(e,t){this.indexInfo=Y(this.parsedUrl.path,this.rootNode,e,this.apiKey,m,t),this.fullExtent==null||this.fullExtent.hasZ||this._updateExtent(await this.indexInfo)}_updateExtent(e){var t,o,r;if((e==null?void 0:e.type)==="page"){const i=e.rootIndex%e.pageSize,l=(o=(t=e.rootPage)==null?void 0:t.nodes)==null?void 0:o[i];if(l==null||l.obb==null||l.obb.center==null||l.obb.halfSize==null)throw new y("sceneservice:invalid-node-page","Invalid node page.");if(l.obb.center[0]<A||this.fullExtent==null||this.fullExtent.hasZ)return;const p=l.obb.halfSize,n=l.obb.center[2],h=Math.sqrt(p[0]*p[0]+p[1]*p[1]+p[2]*p[2]);this.fullExtent.zmin=n-h,this.fullExtent.zmax=n+h}else if((e==null?void 0:e.type)==="node"){const i=(r=e.rootNode)==null?void 0:r.mbs;if(!Array.isArray(i)||i.length!==4||i[0]<A)return;const l=i[2],p=i[3],{fullExtent:n}=this;n&&(n.zmin=l-p,n.zmax=l+p)}}async _fetchService(e){if(this.url==null)throw new y("sceneservice:url-not-set","Scene service can not be loaded without valid portal item or url");if(this.layerId==null&&/SceneServer\/*$/i.test(this.url)){const t=await this._fetchFirstLayerId(e);t!=null&&(this.layerId=t)}return this._fetchServiceLayer(e)}async _fetchFirstLayerId(e){const t=await v(this.url,{query:{f:"json",token:this.apiKey},responseType:"json",signal:e});if(t.data&&Array.isArray(t.data.layers)&&t.data.layers.length>0)return t.data.layers[0].id}async _fetchServiceLayer(e){var i;const t=await v(((i=this.parsedUrl)==null?void 0:i.path)??"",{query:{f:"json",token:this.apiKey},responseType:"json",signal:e});t.ssl&&(this.url=this.url.replace(/^http:/i,"https:"));let o=!1;if(t.data.layerType&&t.data.layerType==="Voxel"&&(o=!0),o)return this._fetchVoxelServiceLayer();const r=t.data;this.read(r,this._getServiceContext()),this.validateLayer(r)}async _fetchVoxelServiceLayer(e){var o;const t=(await v(((o=this.parsedUrl)==null?void 0:o.path)+"/layer",{query:{f:"json",token:this.apiKey},responseType:"json",signal:e})).data;this.read(t,this._getServiceContext()),this.validateLayer(t)}_getServiceContext(){var e;return{origin:"service",portalItem:this.portalItem,portal:(e=this.portalItem)==null?void 0:e.portal,url:this.parsedUrl}}async _ensureLoadBeforeSave(){await this.load(),"beforeSave"in this&&typeof this.beforeSave=="function"&&await this.beforeSave()}validateLayer(e){}_updateTypeKeywords(e,t,o){e.typeKeywords||(e.typeKeywords=[]);const r=t.getTypeKeywords();for(const i of r)e.typeKeywords.push(i);e.typeKeywords&&(e.typeKeywords=e.typeKeywords.filter((i,l,p)=>p.indexOf(i)===l),o===g.newItem&&(e.typeKeywords=e.typeKeywords.filter(i=>i!=="Hosted Service")))}async _saveAs(e,t){var n;const o={...K,...t};let r=Q.from(e);r||(m.error("_saveAs(): requires a portal item parameter"),await Promise.reject(new y("sceneservice:portal-item-required","_saveAs() requires a portal item to save to"))),r.id&&(r=r.clone(),r.id=null);const i=r.portal||$.getDefault();await this._ensureLoadBeforeSave(),r.type=S,r.portal=i;const l={origin:"portal-item",url:null,messages:[],portal:i,portalItem:r,writtenProperties:[],blockedRelativeUrls:[],resources:{toAdd:[],toUpdate:[],toKeep:[],pendingOperations:[]}},p={layers:[this.write({},l)]};return await Promise.all(l.resources.pendingOperations??[]),await this._validateAgainstJSONSchema(p,l,o),r.url=this.url,r.title||(r.title=this.title),this._updateTypeKeywords(r,o,g.newItem),await i.signIn(),await((n=i.user)==null?void 0:n.addItem({item:r,folder:o&&o.folder,data:p})),await O(this.resourceReferences,l,null),this.portalItem=r,T(l),l.portalItem=r,r}async _save(e){const t={...K,...e};if(!this.portalItem)throw m.error("_save(): requires the .portalItem property to be set"),new y("sceneservice:portal-item-not-set","Portal item to save to has not been set on this SceneService");if(this.portalItem.type!==S)throw m.error("_save(): Non-matching portal item type. Got "+this.portalItem.type+", expected "+S),new y("sceneservice:portal-item-wrong-type",`Portal item needs to have type "${S}"`);await this._ensureLoadBeforeSave();const o={origin:"portal-item",url:this.portalItem.itemUrl&&N(this.portalItem.itemUrl),messages:[],portal:this.portalItem.portal||$.getDefault(),portalItem:this.portalItem,writtenProperties:[],blockedRelativeUrls:[],resources:{toAdd:[],toUpdate:[],toKeep:[],pendingOperations:[]}},r={layers:[this.write({},o)]};return await Promise.all(o.resources.pendingOperations??[]),await this._validateAgainstJSONSchema(r,o,t),this.portalItem.url=this.url,this.portalItem.title||(this.portalItem.title=this.title),this._updateTypeKeywords(this.portalItem,t,g.existingItem),await this.portalItem.update({data:r}),await O(this.resourceReferences,o,null),T(o),this.portalItem}async _validateAgainstJSONSchema(e,t,o){var n,h;let r=((n=t.messages)==null?void 0:n.filter(d=>d.type==="error").map(d=>new y(d.name,d.message,d.details)))??[];(h=o==null?void 0:o.validationOptions)!=null&&h.ignoreUnsupported&&(r=r.filter(d=>d.name!=="layer:unsupported"&&d.name!=="symbol:unsupported"&&d.name!=="symbol-layer:unsupported"&&d.name!=="property:unsupported"&&d.name!=="url:unsupported"&&d.name!=="scenemodification:unsupported"));const i=o==null?void 0:o.validationOptions,l=i==null?void 0:i.enabled,p=te();if(l&&p){const d=(await p()).validate(e,o.portalItemLayerType);if(d.length>0){const b=`Layer item did not validate:
${d.join(`
`)}`;if(m.error(`_validateAgainstJSONSchema(): ${b}`),i.failPolicy==="throw"){const _=d.map(w=>new y("sceneservice:schema-validation",w)).concat(r);throw new y("sceneservice-validate:error","Failed to save layer item due to schema validation, see `details.errors`.",{combined:_})}}}if(r.length>0)throw new y("sceneservice:save","Failed to save SceneService due to unsupported or invalid content. See 'details.errors' for more detailed information",{errors:r})}};return c([u(F)],s.prototype,"id",void 0),c([u({type:R})],s.prototype,"spatialReference",void 0),c([f("spatialReference",["spatialReference","store.indexCRS","store.geographicCRS"])],s.prototype,"readSpatialReference",null),c([u({type:E})],s.prototype,"fullExtent",void 0),c([f("fullExtent",["fullExtent","store.extent","spatialReference","store.indexCRS","store.geographicCRS"])],s.prototype,"readFullExtent",null),c([u({readOnly:!0,type:z})],s.prototype,"heightModelInfo",void 0),c([u({type:Number,json:{name:"layerDefinition.minScale",write:!0,origins:{service:{read:{source:"minScale"},write:!1}}}})],s.prototype,"minScale",void 0),c([u({type:Number,json:{name:"layerDefinition.maxScale",write:!0,origins:{service:{read:{source:"maxScale"},write:!1}}}})],s.prototype,"maxScale",void 0),c([u({readOnly:!0})],s.prototype,"version",void 0),c([f("version",["store.version"])],s.prototype,"readVersion",null),c([u({type:String,json:{read:{source:"copyrightText"}}})],s.prototype,"copyright",void 0),c([u({type:String,json:{read:!1}})],s.prototype,"sublayerTitleMode",void 0),c([u({type:String})],s.prototype,"title",void 0),c([f("portal-item","title")],s.prototype,"readTitlePortalItem",null),c([f("service","title",["name"])],s.prototype,"readTitleService",null),c([u({type:Number,json:{origins:{service:{read:{source:"id"}},"portal-item":{write:{target:"id",isRequired:!0,ignoreOrigin:!0},read:!1}}}})],s.prototype,"layerId",void 0),c([u(J)],s.prototype,"url",null),c([q("url")],s.prototype,"writeUrl",null),c([u()],s.prototype,"parsedUrl",null),c([u({readOnly:!0})],s.prototype,"store",void 0),c([u({type:String,readOnly:!0,json:{read:{source:"store.rootNode"}}})],s.prototype,"rootNode",void 0),s=c([C(P)],s),s},A=-1e38;var g;(function(a){a[a.existingItem=0]="existingItem",a[a.newItem=1]="newItem"})(g||(g={}));const S="Scene Service",K={getTypeKeywords:()=>[],portalItemLayerType:"unknown",validationOptions:{enabled:!0,ignoreUnsupported:!1,failPolicy:"throw"}};var x;(function(a){a[a.SAVE=0]="SAVE",a[a.SAVE_AS=1]="SAVE_AS"})(x||(x={}));export{ce as E,x as L,Y as n};
