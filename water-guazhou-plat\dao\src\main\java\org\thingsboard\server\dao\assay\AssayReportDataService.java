package org.thingsboard.server.dao.assay;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.AssayReportDataDTO;
import org.thingsboard.server.dao.model.request.AssayReportRequest;
import org.thingsboard.server.dao.model.sql.assay.AssayReportData;

import java.util.List;

public interface AssayReportDataService {

    void remove(List<String> ids);

    JSONObject getDataListByTypeId(String typeId);

    void save(AssayReportDataDTO entity, String tenantId);

    PageData<AssayReportData> findList(AssayReportRequest request);

    JSONObject getDataListByPid(String pid);
}
