var b=Object.defineProperty;var M=(l,r,h)=>r in l?b(l,r,{enumerable:!0,configurable:!0,writable:!0,value:h}):l[r]=h;var u=(l,r,h)=>M(l,typeof r!="symbol"?r+"":r,h);import{Q as W}from"./index-r0dFAfgr.js";class p{constructor(r,h,n,d){u(this,"x");u(this,"y");u(this,"curRadius",0);u(this,"radius");u(this,"color");this.x=h,this.y=n,this.radius=r,this.color=d||"#00ffff"}setRadius(r){this.radius=r}expandRadius(r){this.curRadius+=r}setColor(r){this.color=r}draw(r){if(r===null){console.warn("context is null");return}r.beginPath(),this.curRadius+2>this.radius&&(this.curRadius=0),r.arc(this.x,this.y,this.curRadius,0,Math.PI*2),r.closePath(),r.lineWidth=2,r.strokeStyle=this.color,r.stroke()}}const S=(l,r)=>{let h,n;const d=()=>(h=document.getElementById(l),h),o=[],a=t=>{const e=document.createElement("canvas");return e.id=t.id,e.width=t.width||25,e.height=t.height||25,e.style.position="absolute",e.style.cursor="pointer",e},C=(t,e)=>{const s=Math.min(t.width,t.height),i=new p(s/2,t.width/2,t.height/2,e||"#67c23a"),c=s/33/2;w(t,i,c)},w=(t,e,s=.2)=>{const i=t.getContext("2d");if(i===null){console.warn("_drawCircles：","context is null, progress exited");return}const c=document.createElement("canvas"),m=c.getContext("2d");m!==null&&(c.width=t.width,c.height=t.height,i.globalAlpha=.95,m.globalCompositeOperation="copy",e.draw(i),e.expandRadius(s),setTimeout(()=>{m.drawImage(t,0,0,t.width,t.height),i.clearRect(0,0,t.width,t.height),w(t,e,s),i.drawImage(c,0,0,t.width,t.height)},33))},f=(t,e,s)=>{if(!s||!e||!t)return;const i=t.toScreen(e);i!==null&&(s.style.left=i.x-s.width/2+"px",s.style.top=i.y-s.height/2+"px")},R=(t,e,s)=>{d(),!(!t||!h||!(e!=null&&e.length))&&(e=e.map(i=>{const c=a({id:((r==null?void 0:r.popIdPrefix)??"point_canvas_")+i.id,...s||{}});return h==null||h.appendChild(c),i.canvas=c,C(c,s==null?void 0:s.color),f(t,i.point,c),i}),o.push(...e),g(t))},I=t=>{t!==null&&(h=t)},E=(t,e,s)=>{if(!t||!e)return;d(),n||g(t);const i=a({id:((r==null?void 0:r.popIdPrefix)??"point_canvas_")+e.id,...s});e.canvas=i,o.push(e),h==null||h.appendChild(i),C(i,s==null?void 0:s.color),f(t,e.point,i)},y=()=>{h&&(o.map(t=>{t.canvas&&(h==null||h.removeChild(t.canvas))}),o.length=0)},k=t=>{const e=o.find(s=>s.id===t);e!=null&&e.canvas&&(h==null||h.removeChild(e.canvas))},g=t=>{t&&(n&&(n==null||n.remove()),n=t.watch("extent",()=>{o.map(e=>{const s=e.canvas;f(t,e.point,s)})}))},x=()=>{n==null||n.remove(),n=void 0},_=()=>{y(),x()};return W(()=>{_()}),{watchExtent:g,removeWatchExtent:x,setContainer:I,removeAll:y,remove:k,add:E,addMany:R,destroy:_}};export{S as u};
