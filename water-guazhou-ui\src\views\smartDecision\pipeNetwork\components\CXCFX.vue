<template>
  <div class="cxcfx">
    <VChart
      ref="refChart"
      :option="state.cxcOption"
    ></VChart>
  </div>
</template>
<script lang="ts" setup>
import { GetPartionProSaleDeltaStatistic } from '@/api/mapservice/dma'
import { useDetector } from '@/hooks/echarts'
import { initCXXOption } from '../echart'

const state = reactive<{
  cxcOption: any
}>({
  cxcOption: initCXXOption()
})

const refreshData = () => {
  GetPartionProSaleDeltaStatistic({
    type: 'year'
  }).then(res => {
    const data = res.data.data || {}
    const x = data.x.map(item => item + '月')
    state.cxcOption = initCXXOption(x, data.supply, data.sale, data.nrw)
  })
}
const refChart = ref()
const detector = useDetector()
onMounted(() => {
  detector.listenToMush(document.documentElement, () => {
    refChart.value?.resize()
  })
  refreshData()
})
</script>
<style lang="scss" scoped>
.cxcfx {
  width: 100%;
  height: 100%;
}
</style>
