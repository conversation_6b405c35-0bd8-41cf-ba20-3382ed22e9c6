"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5638,3172],{17057:(e,t,n)=>{n.d(t,{P:()=>r});class r{constructor(e){this.source=e}}},92089:(e,t,n)=>{n.d(t,{s:()=>r});class r{constructor(e,t){this._moduleSingletons=e,this._syntaxModules=t}loadLibrary(e){if(null==this._syntaxModules)return null;const t=this._syntaxModules[e.toLowerCase()];return t?{syntax:t.script,uri:t.uri}:null}}},28228:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(33586);class i extends r.Z{constructor(e){super(),this.declaredClass="esri.arcade.Portal",this.immutable=!1,this.setField("url",e),this.immutable=!0}}},80692:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(33586);class i extends r.Z{constructor(e,t,n,r,i,a){super(),this.attachmentUrl=i,this.declaredClass="esri.arcade.Attachment",this.immutable=!1,this.setField("id",e),this.setField("name",t),this.setField("contenttype",n),this.setField("size",r),this.setField("exifinfo",a),this.immutable=!0}}},33586:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(48853),i=n(27535),a=n(12384),s=n(61363),o=n(9361);function u(e,t,n=!1){if(null==e)return null;if((0,s.b)(e))return(0,s.g)(e);if((0,s.a)(e))return(0,s.h)(e);if((0,s.c)(e))return(0,s.j)(e);if((0,s.k)(e))return(0,s.l)(e,t);if((0,s.m)(e)){const r=[];for(const i of e)r.push(u(i,t,n));return r}const r=new l;r.immutable=!1;for(const i of Object.keys(e)){const a=e[i];void 0!==a&&r.setField(i,u(a,t,n))}return r.immutable=n,r}class l{constructor(e){this.declaredClass="esri.arcade.Dictionary",this.attributes=null,this.plain=!1,this.immutable=!0,this.attributes=e instanceof l?e.attributes:e??{}}field(e){const t=e.toLowerCase(),n=this.attributes[e];if(void 0!==n)return n;for(const e in this.attributes)if(e.toLowerCase()===t)return this.attributes[e];throw new i.aV(null,i.rH.FieldNotFound,null,{key:e})}setField(e,t){if(this.immutable)throw new i.aV(null,i.rH.Immutable,null);if((0,s.i)(t))throw new i.aV(null,i.rH.NoFunctionInDictionary,null);const n=e.toLowerCase();if(t instanceof Date&&(t=r.iG.dateJSToArcadeDate(t)),void 0===this.attributes[e]){for(const e in this.attributes)if(e.toLowerCase()===n)return void(this.attributes[e]=t);this.attributes[e]=t}else this.attributes[e]=t}hasField(e){const t=e.toLowerCase();if(void 0!==this.attributes[e])return!0;for(const e in this.attributes)if(e.toLowerCase()===t)return!0;return!1}keys(){let e=[];for(const t in this.attributes)e.push(t);return e=e.sort(),e}castToText(e=!1){let t="";for(const n in this.attributes){""!==t&&(t+=",");const i=this.attributes[n];null==i?t+=JSON.stringify(n)+":null":(0,s.a)(i)||(0,s.b)(i)||(0,s.c)(i)?t+=JSON.stringify(n)+":"+JSON.stringify(i):i instanceof o.Z?t+=JSON.stringify(n)+":"+(0,s.t)(i):i instanceof a.Z||i instanceof Array?t+=JSON.stringify(n)+":"+(0,s.t)(i,null,e):i instanceof r.iG?t+=e?JSON.stringify(n)+":"+JSON.stringify(i.getTime()):JSON.stringify(n)+":"+i.stringify():null!==i&&"object"==typeof i&&void 0!==i.castToText&&(t+=JSON.stringify(n)+":"+i.castToText(e))}return"{"+t+"}"}static convertObjectToArcadeDictionary(e,t,n=!0){const r=new l;r.immutable=!1;for(const n in e){const i=e[n];void 0!==i&&r.setField(n.toString(),u(i,t))}return r.immutable=n,r}static convertJsonToArcade(e,t,n=!1){return u(e,t,n)}castAsJson(e=null){const t={};for(let n in this.attributes){const r=this.attributes[n];void 0!==r&&(e?.keyTranslate&&(n=e.keyTranslate(n)),t[n]=(0,s.d)(r,e))}return t}async castDictionaryValueAsJsonAsync(e,t,n,r=null,i){const a=await(0,s.e)(n,r,i);return e[t]=a,a}async castAsJsonAsync(e=null,t=null){const n={},i=[];for(let a in this.attributes){const u=this.attributes[a];t?.keyTranslate&&(a=t.keyTranslate(a)),void 0!==u&&((0,s.f)(u)||u instanceof o.Z||u instanceof r.iG?n[a]=(0,s.d)(u,t):i.push(this.castDictionaryValueAsJsonAsync(n,a,u,e,t)))}return i.length>0&&await Promise.all(i),n}}},15274:(e,t,n)=>{n.d(t,{Z:()=>m});var r=n(33586),i=n(12384),a=n(61363),s=n(9361),o=n(94139),u=n(33955),l=n(98732),c=n(70586),d=n(48853),h=n(27535),f=n(85839);class m{constructor(){this.arcadeDeclaredClass="esri.arcade.Feature",this._optimizedGeomDefinition=null,this._geometry=null,this.attributes=null,this._layer=null,this._datesfixed=!0,this.dateTimeReferenceFieldIndex=null,this.contextTimeReference=null,this.immutable=!0,this._datefields=null,this.immutable=!0}static createFromGraphic(e,t){const n=new m;return n.contextTimeReference=t??null,n._geometry=(0,c.pC)(e.geometry)?e.geometry:null,void 0===e.attributes||null===e.attributes?n.attributes={}:n.attributes=e.attributes,e._sourceLayer?(n._layer=e._sourceLayer,n._datesfixed=!1):e._layer?(n._layer=e._layer,n._datesfixed=!1):e.layer&&"fields"in e.layer?(n._layer=e.layer,n._datesfixed=!1):e.sourceLayer&&"fields"in e.sourceLayer&&(n._layer=e.sourceLayer,n._datesfixed=!1),n._layer&&!1===n._datesfixed&&(void 0!==n._layer.dateTimeReferenceFieldIndex?n.dateTimeReferenceFieldIndex=n._layer.dateTimeReferenceFieldIndex:n.dateTimeReferenceFieldIndex=f.nu.createFromLayer(n._layer)),n}static createFromArcadeFeature(e){const t=new m;return t._datesfixed=e._datesfixed,t.attributes=e.attributes,t._geometry=e._geometry,t._optimizedGeomDefinition=e._optimizedGeomDefinition,e._layer&&(t._layer=e._layer),t.dateTimeReferenceFieldIndex=e.dateTimeReferenceFieldIndex,t.contextTimeReference=e.contextTimeReference,t}static createFromOptimisedFeature(e,t,n){const r=new m;return r._geometry=e.geometry?{geometry:e.geometry}:null,r._optimizedGeomDefinition=n,r.attributes=e.attributes||{},r._layer=t,r._datesfixed=!1,r}static createFromArcadeDictionary(e){const t=new m;return t.attributes=e.field("attributes"),null!==t.attributes&&t.attributes instanceof r.Z?(t.attributes=t.attributes.attributes,null===t.attributes&&(t.attributes={})):t.attributes={},t._geometry=e.field("geometry"),null!==t._geometry&&(t._geometry instanceof r.Z?t._geometry=m.parseGeometryFromDictionary(t._geometry):t._geometry instanceof s.Z||(t._geometry=null)),t}static createFromGraphicLikeObject(e,t,n=null,r){const i=new m;return i.contextTimeReference=r??null,null===t&&(t={}),i.attributes=t,i._geometry=(0,c.pC)(e)?e:null,i._layer=n,i._layer&&(i._datesfixed=!1,void 0!==i._layer.dateTimeReferenceFieldIndex?i.dateTimeReferenceFieldIndex=i._layer.dateTimeReferenceFieldIndex:i.dateTimeReferenceFieldIndex=f.nu.createFromLayer(i._layer)),i}repurposeFromGraphicLikeObject(e,t,n=null){null===t&&(t={}),this.attributes=t,this._geometry=e||null,this._layer=n,this._layer?this._datesfixed=!1:this._datesfixed=!0}get layerPreferredTimeZone(){return this.dateTimeReferenceFieldIndex?.layerPreferredTimeZone??""}fieldSourceTimeZone(e){return this.dateTimeReferenceFieldIndex?.fieldTimeZone(e)??""}castToText(e=!1){let t="";!1===this._datesfixed&&this._fixDates();for(const n in this.attributes){""!==t&&(t+=",");const r=this.attributes[n];null==r?t+=JSON.stringify(n)+":null":(0,a.a)(r)||(0,a.b)(r)||(0,a.c)(r)?t+=JSON.stringify(n)+":"+JSON.stringify(r):r instanceof s.Z?t+=JSON.stringify(n)+":"+(0,a.t)(r):r instanceof i.Z||r instanceof Array?t+=JSON.stringify(n)+":"+(0,a.t)(r,null,e):r instanceof d.iG?t+=e?JSON.stringify(n)+":"+JSON.stringify(r.getTime()):JSON.stringify(n)+":"+r.stringify():null!==r&&"object"==typeof r&&void 0!==r.castToText&&(t+=JSON.stringify(n)+":"+r.castToText(e))}return'{"geometry":'+(null===this.geometry()?"null":(0,a.t)(this.geometry()))+',"attributes":{'+t+"}}"}_fixDates(){if(null!==this._datefields)return this._datefields.length>0&&this._fixDateFields(this._datefields),void(this._datesfixed=!0);const e=[],t=this._layer.fields;for(let n=0;n<t.length;n++){const r=t[n],i=r.type;"date"!==i&&"esriFieldTypeDate"!==i||e.push(r.name)}this._datefields=e,e.length>0&&this._fixDateFields(e),this._datesfixed=!0}isUnknownDateTimeField(e){return"unknown"===this.dateTimeReferenceFieldIndex?.fieldTimeZone(e)}_fixDateFields(e){this.attributes={...this.attributes};const t=this.contextTimeReference?.timeZone??"system";for(let n=0;n<e.length;n++){let r=this.attributes[e[n]];if(null===r);else if(void 0===r){for(const i in this.attributes)if(i.toLowerCase()===e[n].toLowerCase()){if(r=this.attributes[i],null!==r){const e=this.isUnknownDateTimeField(i);(0,a.k)(r)?this.attributes[i]=r:r instanceof Date?this.attributes[i]=e?d.iG.unknownDateJSToArcadeDate(r):d.iG.dateJSAndZoneToArcadeDate(r,t):this.attributes[i]=e?d.iG.unknownEpochToArcadeDate(r):d.iG.epochToArcadeDate(r,t)}break}}else{const i=this.isUnknownDateTimeField(e[n]);(0,a.k)(r)?this.attributes[e[n]]=r:r instanceof Date?this.attributes[e[n]]=i?d.iG.unknownDateJSToArcadeDate(r):d.iG.dateJSAndZoneToArcadeDate(r,t):this.attributes[e[n]]=i?d.iG.unknownEpochToArcadeDate(r):d.iG.epochToArcadeDate(r,t)}}}geometry(){return null===this._geometry||this._geometry instanceof s.Z||(this._optimizedGeomDefinition?(this._geometry=(0,c.Wg)((0,u.im)((0,l.di)(this._geometry,this._optimizedGeomDefinition.geometryType,this._optimizedGeomDefinition.hasZ,this._optimizedGeomDefinition.hasM))),this._geometry.spatialReference=this._optimizedGeomDefinition.spatialReference):this._geometry=(0,c.Wg)((0,u.im)(this._geometry))),this._geometry}field(e){!1===this._datesfixed&&this._fixDates();const t=this.attributes[e];if(void 0!==t)return t;const n=e.toLowerCase();for(const e in this.attributes)if(e.toLowerCase()===n)return this.attributes[e];if(this._hasFieldDefinition(n))return null;throw new h.aV(null,h.rH.FieldNotFound,null,{key:e})}_hasFieldDefinition(e){if(null===this._layer)return!1;for(let t=0;t<this._layer.fields.length;t++)if(this._layer.fields[t].name.toLowerCase()===e)return!0;return!1}setField(e,t){if(this.immutable)throw new h.aV(null,h.rH.Immutable,null);if(t instanceof Date&&(t=this.isUnknownDateTimeField(e)?d.iG.unknownDateJSToArcadeDate(t):d.iG.dateJSToArcadeDate(t)),!1===(0,a.f)(t))throw new h.aV(null,h.rH.TypeNotAllowedInFeature,null);const n=e.toLowerCase();if(void 0===this.attributes[e]){for(const e in this.attributes)if(e.toLowerCase()===n)return void(this.attributes[e]=t);this.attributes[e]=t}else this.attributes[e]=t}hasField(e){const t=e.toLowerCase();if(void 0!==this.attributes[e])return!0;for(const e in this.attributes)if(e.toLowerCase()===t)return!0;return!!this._hasFieldDefinition(t)}keys(){let e=[];const t={};for(const n in this.attributes)e.push(n),t[n.toLowerCase()]=1;if(null!==this._layer)for(let n=0;n<this._layer.fields.length;n++){const r=this._layer.fields[n];1!==t[r.name.toLowerCase()]&&e.push(r.name)}return e=e.sort(),e}static parseGeometryFromDictionary(e){const t=m._convertDictionaryToJson(e,!0);return void 0!==t.hasm&&(t.hasM=t.hasm,delete t.hasm),void 0!==t.hasz&&(t.hasZ=t.hasz,delete t.hasz),void 0!==t.spatialreference&&(t.spatialReference=t.spatialreference,delete t.spatialreference),void 0!==t.rings&&(t.rings=this._fixPathArrays(t.rings,!0===t.hasZ,!0===t.hasZ)),void 0!==t.paths&&(t.paths=this._fixPathArrays(t.paths,!0===t.hasZ,!0===t.hasM)),void 0!==t.points&&(t.points=this._fixPointArrays(t.points,!0===t.hasZ,!0===t.hasM)),(0,u.im)(t)}static _fixPathArrays(e,t,n){const r=[];if(e instanceof Array)for(let i=0;i<e.length;i++)r.push(this._fixPointArrays(e[i],t,n));else if(e instanceof i.Z)for(let i=0;i<e.length();i++)r.push(this._fixPointArrays(e.get(i),t,n));return r}static _fixPointArrays(e,t,n){const r=[];if(e instanceof Array)for(let a=0;a<e.length;a++){const s=e[a];s instanceof o.Z?t&&n?r.push([s.x,s.y,s.z,s.m]):t?r.push([s.x,s.y,s.z]):n?r.push([s.x,s.y,s.m]):r.push([s.x,s.y]):s instanceof i.Z?r.push(s.toArray()):r.push(s)}else if(e instanceof i.Z)for(let a=0;a<e.length();a++){const s=e.get(a);s instanceof o.Z?t&&n?r.push([s.x,s.y,s.z,s.m]):t?r.push([s.x,s.y,s.z]):n?r.push([s.x,s.y,s.m]):r.push([s.x,s.y]):s instanceof i.Z?r.push(s.toArray()):r.push(s)}return r}static _convertDictionaryToJson(e,t=!1){const n={};for(const i in e.attributes){let a=e.attributes[i];a instanceof r.Z&&(a=m._convertDictionaryToJson(a)),t?n[i.toLowerCase()]=a:n[i]=a}return n}static parseAttributesFromDictionary(e){const t={};for(const n in e.attributes){const r=e.attributes[n];if(!(0,a.f)(r))throw new h.aV(null,h.rH.InvalidParameter,null);t[n]=r}return t}static fromJson(e,t){let n=null;null!==e.geometry&&void 0!==e.geometry&&(n=(0,u.im)(e.geometry));const r={};if(null!==e.attributes&&void 0!==e.attributes)for(const t in e.attributes){const n=e.attributes[t];if(null===n)r[t]=n;else{if(!((0,a.c)(n)||(0,a.b)(n)||(0,a.a)(n)||(0,a.k)(n)))throw new h.aV(null,h.rH.InvalidParameter,null);r[t]=n}}return m.createFromGraphicLikeObject(n,r,null,t??null)}fullSchema(){return this._layer}gdbVersion(){if(null===this._layer)return"";const e=this._layer.gdbVersion;return void 0===e?"":""===e&&this._layer.capabilities&&this._layer.capabilities.isVersioned?"SDE.DEFAULT":e}castAsJson(e){const t={attributes:{},geometry:!0===e?.keepGeometryType?this.geometry():this.geometry()?.toJSON()??null};for(const n in this.attributes){const r=this.attributes[n];void 0!==r&&(t.attributes[n]=(0,a.d)(r,e))}return t}async castAsJsonAsync(e=null,t){return this.castAsJson(t)}}},9609:(e,t,n)=>{n.d(t,{Bx:()=>s,Rm:()=>i,Vg:()=>o,aq:()=>a});var r=n(95330);class i{constructor(){}}function a(e,t,n){if(e instanceof i&&!(e instanceof o)){const r=new o;return r.fn=e,r.parameterEvaluator=n,r.context=t,r}return e}class s extends i{constructor(e){super(),this.fn=e}createFunction(e){return(...t)=>this.fn(e,{preparsed:!0,arguments:t})}call(e,t){return this.fn(e,t)}marshalledCall(e,t,n,s){return s(e,t,((t,u,l)=>{l=l.map((t=>t instanceof i&&!(t instanceof o)?a(t,e,s):t));const c=this.call(n,{args:l});return(0,r.y8)(c)?c.then((e=>a(e,n,s))):c}))}}class o extends i{constructor(){super(...arguments),this.fn=null,this.context=null}createFunction(e){return this.fn.createFunction(this.context)}call(e,t){return this.fn.marshalledCall(e,t,this.context,this.parameterEvaluator)}marshalledCall(e,t,n){return this.fn.marshalledCall(e,t,this.context,this.parameterEvaluator)}}},12384:(e,t,n)=>{n.d(t,{Z:()=>r});class r{constructor(e=[]){this._elements=e}length(){return this._elements.length}get(e){return this._elements[e]}toArray(){const e=[];for(let t=0;t<this.length();t++)e.push(this.get(t));return e}}},50728:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(12384),i=n(25785);class a extends r.Z{constructor(e,t,n,r,i){super(e),this._lazyPath=[],this._hasZ=!1,this._hasM=!1,this._hasZ=n,this._hasM=r,this._spRef=t,this._cacheId=i}get(e){if(void 0===this._lazyPath[e]){const t=this._elements[e];if(void 0===t)return;this._lazyPath[e]=new i.Z(t,this._spRef,this._hasZ,this._hasM,this._cacheId,e)}return this._lazyPath[e]}equalityTest(e){return e===this||null!==e&&e instanceof a!=0&&e.getUniqueHash()===this.getUniqueHash()}getUniqueHash(){return this._cacheId.toString()}}},25785:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(12384),i=n(94139);class a extends r.Z{constructor(e,t,n,r,i,a){super(e),this._lazyPt=[],this._hasZ=!1,this._hasM=!1,this._spRef=t,this._hasZ=n,this._hasM=r,this._cacheId=i,this._partId=a}get(e){if(void 0===this._lazyPt[e]){const t=this._elements[e];if(void 0===t)return;const n=this._hasZ,r=this._hasM;let a=null;a=n&&!r?new i.Z(t[0],t[1],t[2],void 0,this._spRef):r&&!n?new i.Z(t[0],t[1],void 0,t[2],this._spRef):n&&r?new i.Z(t[0],t[1],t[2],t[3],this._spRef):new i.Z(t[0],t[1],this._spRef),a.cache._arcadeCacheId=this._cacheId.toString()+"-"+this._partId.toString()+"-"+e.toString(),this._lazyPt[e]=a}return this._lazyPt[e]}equalityTest(e){return e===this||null!==e&&e instanceof a!=0&&e.getUniqueHash()===this.getUniqueHash()}getUniqueHash(){return this._cacheId.toString()+"-"+this._partId.toString()}}},27535:(e,t,n)=>{var r;n.d(t,{Hy:()=>d,OF:()=>l,TD:()=>f,Tu:()=>p,VO:()=>h,aV:()=>o,kq:()=>c,rH:()=>r}),function(e){e.AsyncNotEnabled="AsyncNotEnabled",e.ModulesNotSupported="ModulesNotSupported",e.CircularModules="CircularModules",e.NeverReach="NeverReach",e.UnsupportedHashType="UnsupportedHashType",e.InvalidParameter="InvalidParameter",e.UnexpectedToken="UnexpectedToken",e.Unrecognised="Unrecognised",e.UnrecognisedType="UnrecognisedType",e.MaximumCallDepth="MaximumCallDepth",e.BooleanConditionRequired="BooleanConditionRequired",e.TypeNotAllowedInFeature="TypeNotAllowedInFeature",e.KeyMustBeString="KeyMustBeString",e.WrongNumberOfParameters="WrongNumberOfParameters",e.CallNonFunction="CallNonFunction",e.NoFunctionInTemplateLiteral="NoFunctionInTemplateLiteral",e.NoFunctionInDictionary="NoFunctionInDictionary",e.NoFunctionInArray="NoFunctionInArray",e.AssignModuleFunction="AssignModuleFunction",e.LogicExpressionOrAnd="LogicExpressionOrAnd",e.LogicalExpressionOnlyBoolean="LogicalExpressionOnlyBoolean",e.FuncionNotFound="FunctionNotFound",e.InvalidMemberAccessKey="InvalidMemberAccessKey",e.UnsupportedUnaryOperator="UnsupportUnaryOperator",e.InvalidIdentifier="InvalidIdentifier",e.MemberOfNull="MemberOfNull",e.UnsupportedOperator="UnsupportedOperator",e.Cancelled="Cancelled",e.ModuleAccessorMustBeString="ModuleAccessorMustBeString",e.ModuleExportNotFound="ModuleExportNotFound",e.Immutable="Immutable",e.OutOfBounds="OutOfBounds",e.IllegalResult="IllegalResult",e.FieldNotFound="FieldNotFound",e.PortalRequired="PortalRequired",e.LogicError="LogicError",e.ArrayAccessorMustBeNumber="ArrayAccessMustBeNumber",e.KeyAccessorMustBeString="KeyAccessorMustBeString",e.WrongSpatialReference="WrongSpatialReference"}(r||(r={}));const i={[r.TypeNotAllowedInFeature]:"Feature attributes only support dates, numbers, strings, guids.",[r.LogicError]:"Logic error - {reason}",[r.NeverReach]:"Encountered unreachable logic",[r.AsyncNotEnabled]:"Async Arcade must be enabled for this script",[r.ModuleAccessorMustBeString]:"Module accessor must be a string",[r.ModuleExportNotFound]:"Module has no export with provided identifier",[r.ModulesNotSupported]:"Current profile does not support modules",[r.ArrayAccessorMustBeNumber]:"Array accessor must be a number",[r.FuncionNotFound]:"Function not found",[r.FieldNotFound]:"Key not found - {key}",[r.CircularModules]:"Circular module dependencies are not allowed",[r.Cancelled]:"Execution cancelled",[r.UnsupportedHashType]:"Type not supported in hash function",[r.IllegalResult]:"Value is not a supported return type",[r.PortalRequired]:"Portal is required",[r.InvalidParameter]:"Invalid parameter",[r.WrongNumberOfParameters]:"Call with wrong number of parameters",[r.Unrecognised]:"Unrecognised code structure",[r.UnrecognisedType]:"Unrecognised type",[r.WrongSpatialReference]:"Cannot work with geometry in this spatial reference. It is different to the execution spatial reference",[r.BooleanConditionRequired]:"Conditions must use booleans",[r.NoFunctionInDictionary]:"Dictionaries cannot contain functions.",[r.NoFunctionInArray]:"Arrays cannot contain functions.",[r.NoFunctionInTemplateLiteral]:"Template Literals do not expect functions by value.",[r.KeyAccessorMustBeString]:"Accessor must be a string",[r.KeyMustBeString]:"Object keys must be a string",[r.Immutable]:"Object is immutable",[r.InvalidParameter]:"Invalid parameter",[r.UnexpectedToken]:"Unexpected token",[r.MemberOfNull]:"Cannot access property of null object",[r.MaximumCallDepth]:"Exceeded maximum function depth",[r.OutOfBounds]:"Out of bounds",[r.InvalidIdentifier]:"Identifier not recognised",[r.FuncionNotFound]:"Function not found",[r.CallNonFunction]:"Expression is not a function",[r.InvalidMemberAccessKey]:"Cannot access value using a key of this type",[r.AssignModuleFunction]:"Cannot assign function to module variable",[r.UnsupportedUnaryOperator]:"Unsupported unary operator",[r.UnsupportedOperator]:"Unsupported operator",[r.LogicalExpressionOnlyBoolean]:"Logical expressions must be boolean",[r.LogicExpressionOrAnd]:"Logical expression can only be combined with || or &&"};class a extends Error{constructor(...e){super(...e)}}class s extends a{constructor(e,t){super(u(t)+e.message,{cause:e}),this.loc=null,Error.captureStackTrace&&Error.captureStackTrace(this,s),t&&t.loc&&(this.loc=t.loc)}}class o extends Error{constructor(e,t,n,r){super("Execution error - "+u(n)+d(i[t],r)),this.loc=null,this.declaredRootClass="esri.arcade.arcadeexecutionerror",Error.captureStackTrace&&Error.captureStackTrace(this,o),n&&n.loc&&(this.loc=n.loc)}}function u(e){return e&&e.loc?`Line : ${e.loc.start?.line}, ${e.loc.start?.column}: `:""}class l extends Error{constructor(e,t,n,r){super("Compilation error - "+u(n)+d(i[t],r)),this.loc=null,this.declaredRootClass="esri.arcade.arcadecompilationerror",Error.captureStackTrace&&Error.captureStackTrace(this,l),n&&n.loc&&(this.loc=n.loc)}}class c extends Error{constructor(){super("Uncompilable code structures"),this.declaredRootClass="esri.arcade.arcadeuncompilableerror",Error.captureStackTrace&&Error.captureStackTrace(this,c)}}function d(e,t){try{if(!t)return e;for(const n in t){let r=t[n];r||(r=""),e=e.replace("{"+n+"}",t[n])}}catch(e){}return e}function h(e,t,n){return"esri.arcade.arcadeexecutionerror"===n.declaredRootClass||"esri.arcade.arcadecompilationerror"===n.declaredRootClass?null===n.loc&&t&&t.loc?new s(n,{cause:n}):n:("esri.arcade.featureset.support.featureseterror"===n.declaredRootClass||"esri.arcade.featureset.support.sqlerror"===n.declaredRootClass||n.declaredRootClass,t&&t.loc?new s(n,{cause:n}):n)}var f;!function(e){e.UnrecognisedUri="UnrecognisedUri",e.UnsupportedUriProtocol="UnsupportedUriProtocol"}(f||(f={}));const m={[f.UnrecognisedUri]:"Unrecognised uri - {uri}",[f.UnsupportedUriProtocol]:"Unrecognised uri protocol"};class p extends Error{constructor(e,t){super(d(m[e],t)),this.declaredRootClass="esri.arcade.arcademoduleerror",Error.captureStackTrace&&Error.captureStackTrace(this,p)}}},90658:(e,t,n)=>{n.d(t,{Bj:()=>r,EI:()=>g,HD:()=>d,JW:()=>l,J_:()=>f,Lz:()=>y,NP:()=>m,Qk:()=>x,SV:()=>D,Sh:()=>c,US:()=>A,dj:()=>i,hd:()=>b,hj:()=>h,q2:()=>F,tI:()=>w,tt:()=>p,yE:()=>C});var r,i,a,s=n(48853),o=n(6570),u=n(1231);function l(e){return u.Z.fromJSON(e.toJSON())}function c(e){return e.toJSON?e.toJSON():e}function d(e){return"string"==typeof e||e instanceof String}function h(e){return"number"==typeof e}function f(e){return e instanceof Date}function m(e){return e instanceof s.iG}function p(e,t){return e===t||!(!f(e)&&!m(e)||!f(t)&&!m(t))&&e.getTime()===t.getTime()}function g(e){if(null==e)return null;if("number"==typeof e)return e;switch(e.toLowerCase()){case"meters":case"meter":return 109404;case"miles":case"mile":return 109439;case"kilometers":case"kilometer":case"km":return 109414}return null}function D(e){if(null==e)return null;switch(e.type){case"polygon":case"multipoint":case"polyline":return e.extent;case"point":return new o.Z({xmin:e.x,ymin:e.y,xmax:e.x,ymax:e.y,spatialReference:e.spatialReference});case"extent":return e}return null}function y(e){if(null==e)return null;if("number"==typeof e)return e;if("number"==typeof e)return e;switch(e.toLowerCase()){case"meters":case"meter":return 9001;case"miles":case"mile":return 9093;case"kilometers":case"kilometer":case"km":return 9036}return null}(a=r||(r={}))[a.Standardised=0]="Standardised",a[a.StandardisedNoInterval=1]="StandardisedNoInterval",a[a.SqlServer=2]="SqlServer",a[a.Oracle=3]="Oracle",a[a.Postgres=4]="Postgres",a[a.PGDB=5]="PGDB",a[a.FILEGDB=6]="FILEGDB",a[a.NotEvaluated=7]="NotEvaluated",function(e){e[e.InFeatureSet=0]="InFeatureSet",e[e.NotInFeatureSet=1]="NotInFeatureSet",e[e.Unknown=2]="Unknown"}(i||(i={}));const w=1e3,x={point:"point",polygon:"polygon",polyline:"polyline",multipoint:"multipoint",extent:"extent",esriGeometryPoint:"point",esriGeometryPolygon:"polygon",esriGeometryPolyline:"polyline",esriGeometryMultipoint:"multipoint",esriGeometryEnvelope:"extent",envelope:"extent"},F={point:"esriGeometryPoint",polygon:"esriGeometryPolygon",polyline:"esriGeometryPolyline",multipoint:"esriGeometryMultipoint",extent:"esriGeometryEnvelope",esriGeometryPoint:"esriGeometryPoint",esriGeometryPolygon:"esriGeometryPolygon",esriGeometryPolyline:"esriGeometryPolyline",esriGeometryMultipoint:"esriGeometryMultipoint",esriGeometryEnvelope:"esriGeometryEnvelope",envelope:"esriGeometryEnvelope"},C={"small-integer":"esriFieldTypeSmallInteger",integer:"esriFieldTypeInteger",long:"esriFieldTypeLong",single:"esriFieldTypeSingle",double:"esriFieldTypeDouble",string:"esriFieldTypeString",date:"esriFieldTypeDate",oid:"esriFieldTypeOID",geometry:"esriFieldTypeGeometry",blob:"esriFieldTypeBlob",raster:"esriFieldTypeRaster",guid:"esriFieldTypeGUID","global-id":"esriFieldTypeGlobalID",xml:"eesriFieldTypeXML",esriFieldTypeSmallInteger:"esriFieldTypeSmallInteger",esriFieldTypeInteger:"esriFieldTypeInteger",esriFieldTypeLong:"esriFieldTypeLong",esriFieldTypeSingle:"esriFieldTypeSingle",esriFieldTypeDouble:"esriFieldTypeDouble",esriFieldTypeString:"esriFieldTypeString",esriFieldTypeDate:"esriFieldTypeDate",esriFieldTypeOID:"esriFieldTypeOID",esriFieldTypeGeometry:"esriFieldTypeGeometry",esriFieldTypeBlob:"esriFieldTypeBlob",esriFieldTypeRaster:"esriFieldTypeRaster",esriFieldTypeGUID:"esriFieldTypeGUID",esriFieldTypeGlobalID:"esriFieldTypeGlobalID",esriFieldTypeXML:"eesriFieldTypeXML"};function A(e){return void 0===e?"":e=(e=(e=e.replace(/\/featureserver\/[0-9]*/i,"/FeatureServer")).replace(/\/mapserver\/[0-9]*/i,"/MapServer")).split("?")[0]}function b(e,t){t||(t={}),"function"==typeof t&&(t={cmp:t});const n="boolean"==typeof t.cycles&&t.cycles,r=t.cmp&&(i=t.cmp,function(e){return function(t,n){const r={key:t,value:e[t]},a={key:n,value:e[n]};return i(r,a)}});var i;const a=[];return function e(t){if(t&&t.toJSON&&"function"==typeof t.toJSON&&(t=t.toJSON()),void 0===t)return;if("number"==typeof t)return isFinite(t)?""+t:"null";if("object"!=typeof t)return JSON.stringify(t);let i,s;if(Array.isArray(t)){for(s="[",i=0;i<t.length;i++)i&&(s+=","),s+=e(t[i])||"null";return s+"]"}if(null===t)return"null";if(a.includes(t)){if(n)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}const o=a.push(t)-1,u=Object.keys(t).sort(r&&r(t));for(s="",i=0;i<u.length;i++){const n=u[i],r=e(t[n]);r&&(s&&(s+=","),s+=JSON.stringify(n)+":"+r)}return a.splice(o,1),"{"+s+"}"}(e)}},71201:(e,t,n)=>{n.d(t,{AW:()=>a,Ay:()=>p,B9:()=>w,Es:()=>C,RI:()=>y,_R:()=>u,nB:()=>b,s9:()=>m,ws:()=>F});var r=n(94139),i=n(87416);function a(e,t,n){return Math.sqrt((e[0]-t[0])**2+(e[1]-t[1])**2+(void 0!==e[2]&&void 0!==t[2]?(e[2]*n-t[2]*n)**2:0))}const s=[];for(const e of[[9002,56146130,6131,6132,8050,8051,8228],[9003,5702,6358,6359,6360,8052,8053],[9095,5754]]){const t=e[0];for(let n=1;n<e.length;n++)s[e[n]]=t}const o=[];function u(e){return e.vcsWkid&&void 0!==s[e.vcsWkid]?o[s[e.vcsWkid]]:e.latestVcsWkid&&void 0!==s[e.latestVcsWkid]?o[s[e.latestVcsWkid]]:1}function l(e,t,n){const r={x:0,y:0};t&&(r.z=0),n&&(r.m=0);let i=0,a=e[0];for(let s=0;s<e.length;s++){const o=e[s];if(!1===f(o,a)){const e=h(a,o,t),s=c(a,o,t,n);s.x*=e,s.y*=e,r.x+=s.x,r.y+=s.y,t&&(s.z*=e,r.z+=s.z),n&&(s.m*=e,r.m+=s.m),i+=e,a=o}}return i>0?(r.x/=i,r.y/=i,t&&(r.z/=i),n&&(r.m/=i)):(r.x=e[0][0],r.y=e[0][1],t&&(r.z=e[0][2]),n&&t?r.m=e[0][3]:n&&(r.m=e[0][2])),r}function c(e,t,n,r){const i={x:(e[0]+t[0])/2,y:(e[1]+t[1])/2};return n&&(i.z=(e[2]+t[2])/2),n&&r?i.m=(e[3]+t[3])/2:r&&(i.m=(e[2]+t[2])/2),i}function d(e,t){if(e.length<=1)return 0;let n=0;for(let r=1;r<e.length;r++)n+=h(e[r-1],e[r],t);return n}function h(e,t,n){const r=t[0]-e[0],i=t[1]-e[1];if(n){const e=t[2]-t[2];return Math.sqrt(r*r+i*i+e*e)}return Math.sqrt(r*r+i*i)}function f(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function m(e){const t={x:0,y:0,spatialReference:e.spatialReference.toJSON()},n={x:0,y:0,spatialReference:e.spatialReference.toJSON()};let i=0,a=0;for(let r=0;r<e.paths.length;r++){if(0===e.paths[r].length)continue;const s=d(e.paths[r],!0===e.hasZ);if(0===s){const n=l(e.paths[r],!0===e.hasZ,!0===e.hasM);t.x+=n.x,t.y+=n.y,!0===e.hasZ&&(t.z+=n.z),!0===e.hasM&&(t.m+=n.m),++i}else{const t=l(e.paths[r],!0===e.hasZ,!0===e.hasM);n.x+=t.x*s,n.y+=t.y*s,!0===e.hasZ&&(n.z+=t.z*s),!0===e.hasM&&(n.m+=t.m*s),a+=s}}return a>0?(n.x/=a,n.y/=a,!0===e.hasZ&&(n.z/=a),!0===e.hasM&&(n.m/=a),new r.Z(n)):i>0?(t.x/=i,t.y/=i,!0===e.hasZ&&(n.z/=i),!0===e.hasM&&(t.m/=i),new r.Z(t)):null}function p(e){if(0===e.points.length)return null;let t=0,n=0,i=0,a=0;for(let r=0;r<e.points.length;r++){const s=e.getPoint(r);!0===s.hasZ&&(i+=s.z),!0===s.hasM&&(a+=s.m),t+=s.x,n+=s.y,a+=s.m}const s={x:t/e.points.length,y:n/e.points.length,spatialReference:null};return s.spatialReference=e.spatialReference.toJSON(),!0===e.hasZ&&(s.z=i/e.points.length),!0===e.hasM&&(s.m=a/e.points.length),new r.Z(s)}function g(e,t,n=0){for(;e<n;)e+=t;const r=n+t;for(;e>=r;)e-=t;return e}function D(e,t){return Math.atan2(t.y-e.y,t.x-e.x)}function y(e,t){return g(D(e,t),2*Math.PI)*(180/Math.PI)}function w(e,t){return g(Math.PI/2-D(e,t),2*Math.PI)*(180/Math.PI)}function x(e,t,n){const r={x:e.x-t.x,y:e.y-t.y},i={x:n.x-t.x,y:n.y-t.y};return Math.atan2(function(e,t){return e.x*t.y-t.x*e.y}(r,i),function(e,t){return e.x*t.x+e.y*t.y}(r,i))}function F(e,t,n){return g(x(e,t,n),2*Math.PI)*(180/Math.PI)}function C(e,t,n){return g(-1*x(e,t,n),2*Math.PI)*(180/Math.PI)}o[9002]=.3048,o[9003]=.3048006096012192,o[9095]=.3048007491;const A=[0,0];function b(e){for(let t=0;t<e.length;t++){const n=e[t];for(let r=0;r<n.length-1;r++){const a=n[r],s=n[r+1];for(let n=t+1;n<e.length;n++)for(let t=0;t<e[n].length-1;t++){const r=e[n][t],o=e[n][t+1];if((0,i.UT)(a,s,r,o,A)&&!(A[0]===a[0]&&A[1]===a[1]||A[0]===r[0]&&A[1]===r[1]||A[0]===s[0]&&A[1]===s[1]||A[0]===o[0]&&A[1]===o[1]))return!0}}const r=n.length;if(!(r<3))for(let e=0;e<=r-2;e++){const t=n[e],a=n[e+1];for(let s=e+2;s<=r-2;s++){const e=n[s],r=n[s+1];if((0,i.UT)(t,a,e,r,A)&&!(A[0]===t[0]&&A[1]===t[1]||A[0]===e[0]&&A[1]===e[1]||A[0]===a[0]&&A[1]===a[1]||A[0]===r[0]&&A[1]===r[1]))return!0}}}return!1}},13976:(e,t,n)=>{n.d(t,{r:()=>f});var r=n(48853),i=n(61363),a=n(27535),s=n(70171),o=n(17126);function u(e,t,n){return e+(function(e){return e%4==0&&(e%100!=0||e%400==0)}(n)?c:l)[t]}const l=[0,31,59,90,120,151,181,212,243,273,304,334],c=[0,31,60,91,121,152,182,213,244,274,305,335];function d(e){return null===e?e:!1===e.isValid?null:e}function h(e,t){return""===e||"default"===e.toLowerCase().trim()?(0,i.C)(t):e}function f(e,t){e.today=function(e,n){return t(e,n,((t,a,s)=>{(0,i.y)(s,0,0,e,n);const o=new Date;return o.setHours(0,0,0,0),r.iG.dateJSAndZoneToArcadeDate(o,(0,i.C)(e))}))},e.changetimezone=function(e,n){return t(e,n,((t,a,s)=>{(0,i.y)(s,2,2,e,n);const o=(0,i.l)(s[0],(0,i.C)(e));if(null===o)return null;const u=r.iG.arcadeDateAndZoneToArcadeDate(o,h((0,i.j)(s[1]),e));return!1===u.isValid?null:u}))},e.timezone=function(e,n){return t(e,n,((t,a,s)=>{(0,i.y)(s,1,2,e,n);const o=(0,i.l)(s[0],(0,i.C)(e));if(null===o)return null;const u=o.timeZone;return"system"===u?r.iG.systemTimeZoneCanonicalName:u}))},e.timezoneoffset=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?null:s.timeZoneOffset}))},e.now=function(e,n){return t(e,n,((t,a,s)=>{(0,i.y)(s,0,0,e,n);const o=r.iG.nowToArcadeDate((0,i.C)(e));return!1===o.isValid?null:o}))},e.timestamp=function(e,n){return t(e,n,((t,a,s)=>{(0,i.y)(s,0,0,e,n);const o=r.iG.nowUTCToArcadeDate();return!1===o.isValid?null:o}))},e.toutc=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?null:s.toUTC()}))},e.tolocal=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?null:s.toLocal()}))},e.day=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.day}))},e.month=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.monthJS}))},e.year=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.year}))},e.hour=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.hour}))},e.second=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.second}))},e.millisecond=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.millisecond}))},e.minute=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.minute}))},e.week=function(e,n){return t(e,n,((t,r,s)=>{(0,i.y)(s,1,2,e,n);const o=(0,i.l)(s[0],(0,i.C)(e));if(null===o)return NaN;const l=(0,i.g)((0,i.A)(s[1],0));if(l<0||l>6)throw new a.aV(e,a.rH.InvalidParameter,n);const c=o.day,d=o.monthJS,h=o.year,f=o.dayOfWeekJS,m=u(c,d,h)-1,p=Math.floor(m/7);return f-l+(f-l<0?7:0)<m-7*p?p+1:p}))},e.weekday=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.dayOfWeekJS}))},e.isoweekday=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.dayOfWeekISO}))},e.isomonth=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.monthISO}))},e.isoweek=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.weekISO}))},e.isoyear=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,1,1,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));return null===s?NaN:s.yearISO}))},e.date=function(e,n){return t(e,n,((t,a,u)=>{if((0,i.y)(u,0,8,e,n),3===u.length)return d(r.iG.fromParts((0,i.g)(u[0]),(0,i.g)(u[1])+1,(0,i.g)(u[2]),0,0,0,0,(0,i.C)(e)));if(4===u.length)return d(r.iG.fromParts((0,i.g)(u[0]),(0,i.g)(u[1])+1,(0,i.g)(u[2]),(0,i.g)(u[3]),0,0,0,(0,i.C)(e)));if(5===u.length)return d(r.iG.fromParts((0,i.g)(u[0]),(0,i.g)(u[1])+1,(0,i.g)(u[2]),(0,i.g)(u[3]),(0,i.g)(u[4]),0,0,(0,i.C)(e)));if(6===u.length)return d(r.iG.fromParts((0,i.g)(u[0]),(0,i.g)(u[1])+1,(0,i.g)(u[2]),(0,i.g)(u[3]),(0,i.g)(u[4]),(0,i.g)(u[5]),0,(0,i.C)(e)));if(7===u.length)return d(r.iG.fromParts((0,i.g)(u[0]),(0,i.g)(u[1])+1,(0,i.g)(u[2]),(0,i.g)(u[3]),(0,i.g)(u[4]),(0,i.g)(u[5]),(0,i.g)(u[6]),(0,i.C)(e)));if(8===u.length)return d(r.iG.fromParts((0,i.g)(u[0]),(0,i.g)(u[1])+1,(0,i.g)(u[2]),(0,i.g)(u[3]),(0,i.g)(u[4]),(0,i.g)(u[5]),(0,i.g)(u[6]),h((0,i.j)(u[7]),e)));if(2===u.length){let e,t=(0,i.j)(u[1]);return""===t?null:(t=(0,i.D)(t),e="X"===t?o.ou.fromSeconds((0,i.g)(u[0])):"x"===t?o.ou.fromMillis((0,i.g)(u[0])):o.ou.fromFormat((0,i.j)(u[0]),t,{locale:(0,s.Kd)(),numberingSystem:"latn"}),e.isValid?r.iG.dateTimeToArcadeDate(e):null)}if(1===u.length){if((0,i.c)(u[0])){if(""===u[0].replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""))return null;if(!0===/^[0-9][0-9][0-9][0-9]$/.test(u[0]))return(0,i.l)(u[0]+"-01-01",(0,i.C)(e))}const t=(0,i.g)(u[0]);if(!1===isNaN(t)){const n=o.ou.fromMillis(t);return n.isValid?r.iG.dateTimeAndZoneToArcadeDate(n,(0,i.C)(e)):null}return(0,i.l)(u[0],(0,i.C)(e))}return 0===u.length?r.iG.nowToArcadeDate((0,i.C)(e)):null}))},e.datediff=function(e,n){return t(e,n,((t,a,s)=>{(0,i.y)(s,2,4,e,n);let o=(0,i.l)(s[0],(0,i.C)(e)),u=(0,i.l)(s[1],(0,i.C)(e));if(null===o||null===u)return NaN;let l=(0,i.A)(s[3],"");switch(""!==l&&null!==l?(l=(0,i.j)(l),o=r.iG.arcadeDateAndZoneToArcadeDate(o,l),u=r.iG.arcadeDateAndZoneToArcadeDate(u,l)):o.timeZone!==u.timeZone&&(o.isUnknownTimeZone?o=r.iG.arcadeDateAndZoneToArcadeDate(o,u.timeZone):(u.isUnknownTimeZone,u=r.iG.arcadeDateAndZoneToArcadeDate(u,o.timeZone))),(0,i.j)(s[2]).toLowerCase()){case"days":case"day":case"d":return o.diff(u,"days");case"months":case"month":return o.diff(u,"months");case"minutes":case"minute":case"m":return"M"===s[2]?o.diff(u,"months"):o.diff(u,"minutes");case"seconds":case"second":case"s":return o.diff(u,"seconds");case"milliseconds":case"millisecond":case"ms":default:return o.diff(u);case"hours":case"hour":case"h":return o.diff(u,"hours");case"years":case"year":case"y":return o.diff(u,"years")}}))},e.dateadd=function(e,n){return t(e,n,((t,r,a)=>{(0,i.y)(a,2,3,e,n);const s=(0,i.l)(a[0],(0,i.C)(e));if(null===s)return null;let o=(0,i.g)(a[1]);if(isNaN(o))return s;let u="milliseconds";switch((0,i.j)(a[2]).toLowerCase()){case"days":case"day":case"d":u="days",o=(0,i.E)(o);break;case"months":case"month":u="months",o=(0,i.E)(o);break;case"minutes":case"minute":case"m":u="M"===a[2]?"months":"minutes";break;case"seconds":case"second":case"s":u="seconds";break;case"milliseconds":case"millisecond":case"ms":u="milliseconds";break;case"hours":case"hour":case"h":u="hours";break;case"years":case"year":case"y":u="years"}return s.plus({[u]:o})}))}}},24240:(e,t,n)=>{n.d(t,{t:()=>s});var r=n(61363);function i(e){let t=0;for(let n=0;n<e.length;n++)t+=e[n];return t/e.length}function a(e){const t=i(e);let n=0;for(let r=0;r<e.length;r++)n+=(t-e[r])**2;return n/e.length}function s(e,t,n=1e3){switch(e.toLowerCase()){case"distinct":return function(e,t){const n=[],i={},a=[];for(let s=0;s<e.length;s++){if(void 0!==e[s]&&null!==e[s]&&e[s]!==r.v){const t=e[s];if((0,r.b)(t)||(0,r.c)(t))void 0===i[t]&&(n.push(t),i[t]=1);else{let e=!1;for(let n=0;n<a.length;n++)!0===(0,r.s)(a[n],t)&&(e=!0);!1===e&&(a.push(t),n.push(t))}}if(n.length>=t&&-1!==t)return n}return n}(t,n);case"avg":case"mean":return i((0,r.V)(t));case"min":return Math.min.apply(Math,(0,r.V)(t));case"sum":return function(e){let t=0;for(let n=0;n<e.length;n++)t+=e[n];return t}((0,r.V)(t));case"max":return Math.max.apply(Math,(0,r.V)(t));case"stdev":case"stddev":return Math.sqrt(a((0,r.V)(t)));case"var":case"variance":return a((0,r.V)(t));case"count":return t.length}return 0}},12834:(e,t,n)=>{n.d(t,{Z:()=>F,r:()=>w});var r=n(33586),i=n(15274),a=n(25785),s=n(61363),o=n(6570),u=n(9361),l=n(65091),c=n(94139),d=n(38913),h=n(58901),f=n(71201),m=n(33955),p=n(27535),g=n(50728),D=n(86662);function y(e){return e&&"esri.arcade.Feature"===e.arcadeDeclaredClass}function w(e,t){e.ringisclockwise=function(e,n){return t(e,n,((t,r,i)=>{(0,s.y)(i,1,1,e,n);let o=[],u=!1,l=!1;if(null===i[0])return!1;if((0,s.m)(i[0])){for(const t of i[0]){if(!(t instanceof c.Z))throw new p.aV(e,p.rH.InvalidParameter,n);o.push(t.hasZ?t.hasM?[t.x,t.y,t.z,t.m]:[t.x,t.y,t.z]:[t.x,t.y])}o.length>0&&(u=i[0][0].hasZ,l=i[0][0].hasM)}else if(i[0]instanceof a.Z)o=i[0]._elements,o.length>0&&(u=i[0]._hasZ,l=i[0]._hasM);else{if(!(0,s.x)(i[0]))throw new p.aV(e,p.rH.InvalidParameter,n);for(const t of i[0].toArray()){if(!(t instanceof c.Z))throw new p.aV(e,p.rH.InvalidParameter,n);o.push(t.hasZ?t.hasM?[t.x,t.y,t.z,t.m]:[t.x,t.y,t.z]:[t.x,t.y])}o.length>0&&(u=i[0].get(0).hasZ,l=i[0].get(0).hasM)}return!(o.length<3)&&(0,D.bu)(o,l,u)}))},e.polygon=function(e,n){return t(e,n,((t,a,o)=>{(0,s.y)(o,1,1,e,n);let u=null;if(o[0]instanceof r.Z){if(u=(0,s.q)(i.Z.parseGeometryFromDictionary(o[0]),e.spatialReference),u instanceof d.Z==0)throw new p.aV(e,p.rH.InvalidParameter,n)}else u=o[0]instanceof d.Z?(0,m.im)(o[0].toJSON()):(0,s.q)(new d.Z(JSON.parse(o[0])),e.spatialReference);if(null!==u&&!1===u.spatialReference.equals(e.spatialReference))throw new p.aV(e,p.rH.WrongSpatialReference,n);return(0,s.F)(u)}))},e.polyline=function(e,n){return t(e,n,((t,a,o)=>{(0,s.y)(o,1,1,e,n);let u=null;if(o[0]instanceof r.Z){if(u=(0,s.q)(i.Z.parseGeometryFromDictionary(o[0]),e.spatialReference),u instanceof h.Z==0)throw new p.aV(e,p.rH.InvalidParameter,n)}else u=o[0]instanceof h.Z?(0,m.im)(o[0].toJSON()):(0,s.q)(new h.Z(JSON.parse(o[0])),e.spatialReference);if(null!==u&&!1===u.spatialReference.equals(e.spatialReference))throw new p.aV(e,p.rH.WrongSpatialReference,n);return(0,s.F)(u)}))},e.point=function(e,n){return t(e,n,((t,a,o)=>{(0,s.y)(o,1,1,e,n);let u=null;if(o[0]instanceof r.Z){if(u=(0,s.q)(i.Z.parseGeometryFromDictionary(o[0]),e.spatialReference),u instanceof c.Z==0)throw new p.aV(e,p.rH.InvalidParameter,n)}else u=o[0]instanceof c.Z?(0,m.im)(o[0].toJSON()):(0,s.q)(new c.Z(JSON.parse(o[0])),e.spatialReference);if(null!==u&&!1===u.spatialReference.equals(e.spatialReference))throw new p.aV(e,p.rH.WrongSpatialReference,n);return(0,s.F)(u)}))},e.multipoint=function(e,n){return t(e,n,((t,a,o)=>{(0,s.y)(o,1,1,e,n);let u=null;if(o[0]instanceof r.Z){if(u=(0,s.q)(i.Z.parseGeometryFromDictionary(o[0]),e.spatialReference),u instanceof l.Z==0)throw new p.aV(e,p.rH.InvalidParameter,n)}else u=o[0]instanceof l.Z?(0,m.im)(o[0].toJSON()):(0,s.q)(new l.Z(JSON.parse(o[0])),e.spatialReference);if(null!==u&&!1===u.spatialReference.equals(e.spatialReference))throw new p.aV(e,p.rH.WrongSpatialReference,n);return(0,s.F)(u)}))},e.extent=function(e,n){return t(e,n,((t,a,u)=>{u=(0,s.G)(u),(0,s.y)(u,1,1,e,n);let f=null;if(u[0]instanceof r.Z)f=(0,s.q)(i.Z.parseGeometryFromDictionary(u[0]),e.spatialReference);else if(u[0]instanceof c.Z){const e={xmin:u[0].x,ymin:u[0].y,xmax:u[0].x,ymax:u[0].y,spatialReference:u[0].spatialReference.toJSON()},t=u[0];t.hasZ?(e.zmin=t.z,e.zmax=t.z):t.hasM&&(e.mmin=t.m,e.mmax=t.m),f=(0,m.im)(e)}else f=u[0]instanceof d.Z||u[0]instanceof h.Z||u[0]instanceof l.Z?(0,m.im)(u[0].extent?.toJSON()):u[0]instanceof o.Z?(0,m.im)(u[0].toJSON()):(0,s.q)(new o.Z(JSON.parse(u[0])),e.spatialReference);if(null!==f&&!1===f.spatialReference.equals(e.spatialReference))throw new p.aV(e,p.rH.WrongSpatialReference,n);return(0,s.F)(f)}))},e.geometry=function(e,n){return t(e,n,((t,a,o)=>{(0,s.y)(o,1,1,e,n);let u=null;if(null===o[0])return null;if(u=y(o[0])?(0,s.q)(o[0].geometry(),e.spatialReference):o[0]instanceof r.Z?(0,s.q)(i.Z.parseGeometryFromDictionary(o[0]),e.spatialReference):(0,s.q)((0,m.im)(JSON.parse(o[0])),e.spatialReference),null!==u&&!1===u.spatialReference.equals(e.spatialReference))throw new p.aV(e,p.rH.WrongSpatialReference,n);return(0,s.F)(u)}))},e.setgeometry=function(e,n){return t(e,n,((t,r,i)=>{if((0,s.y)(i,2,2,e,n),!y(i[0]))throw new p.aV(e,p.rH.InvalidParameter,n);if(!0===i[0].immutable)throw new p.aV(e,p.rH.Immutable,n);if(!(i[1]instanceof u.Z||null===i[1]))throw new p.aV(e,p.rH.InvalidParameter,n);return i[0]._geometry=i[1],s.v}))},e.feature=function(e,n){return t(e,n,((t,a,o)=>{if(0===o.length)throw new p.aV(e,p.rH.WrongNumberOfParameters,n);let l=null;if(1===o.length)if((0,s.c)(o[0]))l=i.Z.fromJson(JSON.parse(o[0]),e.timeReference);else if(y(o[0]))l=i.Z.createFromArcadeFeature(o[0]);else if(o[0]instanceof u.Z)l=i.Z.createFromGraphicLikeObject(o[0],null,null,e.timeReference);else{if(!(o[0]instanceof r.Z))throw new p.aV(e,p.rH.InvalidParameter,n);{let t=o[0].hasField("geometry")?o[0].field("geometry"):null,n=o[0].hasField("attributes")?o[0].field("attributes"):null;null!==t&&t instanceof r.Z&&(t=i.Z.parseGeometryFromDictionary(t)),null!==n&&(n=i.Z.parseAttributesFromDictionary(n)),l=i.Z.createFromGraphicLikeObject(t,n,null,e.timeReference)}}else if(2===o.length){let t=null,a=null;if(null!==o[0])if(o[0]instanceof u.Z)t=o[0];else{if(!(t instanceof r.Z))throw new p.aV(e,p.rH.InvalidParameter,n);t=i.Z.parseGeometryFromDictionary(o[0])}if(null!==o[1]){if(!(o[1]instanceof r.Z))throw new p.aV(e,p.rH.InvalidParameter,n);a=i.Z.parseAttributesFromDictionary(o[1])}l=i.Z.createFromGraphicLikeObject(t,a,null,e.timeReference)}else{let t=null;const a={};if(null!==o[0])if(o[0]instanceof u.Z)t=o[0];else{if(!(t instanceof r.Z))throw new p.aV(e,p.rH.InvalidParameter,n);t=i.Z.parseGeometryFromDictionary(o[0])}for(let t=1;t<o.length;t+=2){const r=(0,s.j)(o[t]),i=o[t+1];if(!(null==i||(0,s.c)(i)||isNaN(i)||(0,s.k)(i)||(0,s.b)(i)||(0,s.a)(i)))throw new p.aV(e,p.rH.InvalidParameter,n);if((0,s.i)(i)||!1===(0,s.f)(i))throw new p.aV(e,p.rH.InvalidParameter,n);a[r]=i===s.v?null:i}l=i.Z.createFromGraphicLikeObject(t,a,null,e.timeReference)}return l._geometry=(0,s.q)(l.geometry(),e.spatialReference),l.immutable=!1,l}))},e.dictionary=function(e,n){return t(e,n,((t,i,a)=>{if(0===a.length){const e=new r.Z;return e.immutable=!1,e}if(1===a.length&&(0,s.c)(a[0]))try{const t=JSON.parse(a[0]),n=r.Z.convertObjectToArcadeDictionary(t,(0,s.C)(e),!1);return n.immutable=!1,n}catch(t){throw new p.aV(e,p.rH.InvalidParameter,n)}if(a.length%2!=0)throw new p.aV(e,p.rH.WrongNumberOfParameters,n);const o={};for(let t=0;t<a.length;t+=2){const r=(0,s.j)(a[t]),i=a[t+1];if(!(null==i||(0,s.c)(i)||isNaN(i)||(0,s.k)(i)||(0,s.b)(i)||(0,s.a)(i)||(0,s.m)(i)||(0,s.x)(i)))throw new p.aV(e,p.rH.InvalidParameter,n);if((0,s.i)(i))throw new p.aV(e,p.rH.InvalidParameter,n);o[r]=i===s.v?null:i}const u=new r.Z(o);return u.immutable=!1,u}))},e.haskey=function(e,n){return t(e,n,((t,i,a)=>{(0,s.y)(a,2,2,e,n);const o=(0,s.j)(a[1]);if(y(a[0]))return a[0].hasField(o);if(a[0]instanceof r.Z)return a[0].hasField(o);if(a[0]instanceof u.Z){const e=F(a[0],o,null,null,2);return!e||"notfound"!==e.keystate}throw new p.aV(e,p.rH.InvalidParameter,n)}))},e.hasvalue=function(e,n){return t(e,n,((t,i,a)=>{if((0,s.y)(a,2,2,e,n),null===a[0]||null===a[1])return!1;const o=(0,s.j)(a[1]);return(0,s.w)(a[0])||a[0]instanceof r.Z?!!a[0].hasField(o)&&null!==a[0].field(o):a[0]instanceof u.Z&&null!==F(a[0],o,null,null,0)}))},e.indexof=function(e,n){return t(e,n,((t,r,i)=>{(0,s.y)(i,2,2,e,n);const a=i[1];if((0,s.m)(i[0])){for(let e=0;e<i[0].length;e++)if((0,s.s)(a,i[0][e]))return e;return-1}if((0,s.x)(i[0])){const e=i[0].length();for(let t=0;t<e;t++)if((0,s.s)(a,i[0].get(t)))return t;return-1}throw new p.aV(e,p.rH.InvalidParameter,n)}))},e.angle=function(e,n){return t(e,n,((t,r,i)=>{if(i=(0,s.G)(i),(0,s.y)(i,2,3,e,n),!(i[0]instanceof c.Z))throw new p.aV(e,p.rH.InvalidParameter,n);if(!(i[1]instanceof c.Z))throw new p.aV(e,p.rH.InvalidParameter,n);if(i.length>2&&!(i[2]instanceof c.Z))throw new p.aV(e,p.rH.InvalidParameter,n);return 2===i.length?(0,f.RI)(i[0],i[1]):(0,f.ws)(i[0],i[1],i[2])}))},e.bearing=function(e,n){return t(e,n,((t,r,i)=>{if(i=(0,s.G)(i),(0,s.y)(i,2,3,e,n),!(i[0]instanceof c.Z))throw new p.aV(e,p.rH.InvalidParameter,n);if(!(i[1]instanceof c.Z))throw new p.aV(e,p.rH.InvalidParameter,n);if(i.length>2&&!(i[2]instanceof c.Z))throw new p.aV(e,p.rH.InvalidParameter,n);return 2===i.length?(0,f.B9)(i[0],i[1]):(0,f.Es)(i[0],i[1],i[2])}))},e.isselfintersecting=function(e,n){return t(e,n,((t,r,i)=>{i=(0,s.G)(i),(0,s.y)(i,1,1,e,n);let a=i[0];if(a instanceof d.Z)return a.isSelfIntersecting;if(a instanceof h.Z)return a=a.paths,(0,f.nB)(a);if(a instanceof l.Z){const e=a.points;for(let t=0;t<e.length;t++)for(let n=0;n<e.length;n++)if(n!==t){let r=!0;for(let i=0;i<e[t].length;i++)if(e[t][i]!==e[n][i]){r=!1;break}if(!0===r)return!0}}return!(!(0,s.m)(a)&&!(0,s.x)(a))&&(a=(0,s.H)(a,e.spatialReference),null!==a&&(a=a.paths),(0,f.nB)(a))}))}}let x=0;function F(e,t,n,i,s=1){let o;switch(t=t.toLowerCase()){case"hasz":{const t=e.hasZ;return void 0!==t&&t}case"hasm":{const t=e.hasM;return void 0!==t&&t}case"spatialreference":{let t=e.spatialReference._arcadeCacheId;if(void 0===t){let n=!0;Object.freeze&&Object.isFrozen(e.spatialReference)&&(n=!1),n&&(x++,e.spatialReference._arcadeCacheId=x,t=x)}const n=new r.Z({wkt:e.spatialReference.wkt,wkid:e.spatialReference.wkid});return void 0!==t&&(n._arcadeCacheId="SPREF"+t.toString()),n}}switch(e.type){case"extent":switch(t){case"xmin":case"xmax":case"ymin":case"ymax":case"zmin":case"zmax":case"mmin":case"mmax":{const n=e[t];return void 0!==n?n:null}case"type":return"Extent"}break;case"polygon":switch(t){case"rings":return o=e.cache._arcadeCacheId,void 0===o&&(x++,o=x,e.cache._arcadeCacheId=o),new g.Z(e.rings,e.spatialReference,!0===e.hasZ,!0===e.hasM,o);case"type":return"Polygon"}break;case"point":switch(t){case"x":case"y":case"z":case"m":return void 0!==e[t]?e[t]:null;case"type":return"Point"}break;case"polyline":switch(t){case"paths":return o=e.cache._arcadeCacheId,void 0===o&&(x++,o=x,e.cache._arcadeCacheId=o),new g.Z(e.paths,e.spatialReference,!0===e.hasZ,!0===e.hasM,o);case"type":return"Polyline"}break;case"multipoint":switch(t){case"points":return o=e.cache._arcadeCacheId,void 0===o&&(x++,o=x,e.cache._arcadeCacheId=o),new a.Z(e.points,e.spatialReference,!0===e.hasZ,!0===e.hasM,o,1);case"type":return"Multipoint"}}if(1===s)throw new p.aV(n,p.rH.InvalidIdentifier,i);return 2===s?{keystate:"notfound"}:null}},18811:(e,t,n)=>{n.r(t),n.d(t,{registerFunctions:()=>x,setGeometryEngine:()=>y});var r=n(40330),i=n(77286),a=n(61363),s=n(71201),o=n(6570),u=n(9361),l=n(65091),c=n(94139),d=n(38913),h=n(58901),f=n(33955),m=n(27535),p=n(67900);let g=null;function D(e){return 0===r.i8.indexOf("4.")?d.Z.fromExtent(e):new d.Z({spatialReference:e.spatialReference,rings:[[[e.xmin,e.ymin],[e.xmin,e.ymax],[e.xmax,e.ymax],[e.xmax,e.ymin],[e.xmin,e.ymin]]]})}function y(e){g=e}function w(e,t){if("polygon"!==e.type&&"polyline"!==e.type&&"extent"!==e.type)return 0;let n=1;(e.spatialReference.vcsWkid||e.spatialReference.latestVcsWkid)&&(n=(0,s._R)(e.spatialReference)/(0,p.c9)(e.spatialReference));let r=0;if("polyline"===e.type)for(const t of e.paths)for(let e=1;e<t.length;e++)r+=(0,s.AW)(t[e],t[e-1],n);else if("polygon"===e.type)for(const t of e.rings){for(let e=1;e<t.length;e++)r+=(0,s.AW)(t[e],t[e-1],n);(t[0][0]!==t[t.length-1][0]||t[0][1]!==t[t.length-1][1]||void 0!==t[0][2]&&t[0][2]!==t[t.length-1][2])&&(r+=(0,s.AW)(t[0],t[t.length-1],n))}else"extent"===e.type&&(r+=2*(0,s.AW)([e.xmin,e.ymin,0],[e.xmax,e.ymin,0],n),r+=2*(0,s.AW)([e.xmin,e.ymin,0],[e.xmin,e.ymax,0],n),r*=2,r+=4*Math.abs((0,a.A)(e.zmax,0)*n-(0,a.A)(e.zmin,0)*n));const i=new h.Z({hasZ:!1,hasM:!1,spatialReference:e.spatialReference,paths:[[0,0],[0,r]]});return g.planarLength(i,t)}function x(e,t){function n(e,t,n){if((0,a.y)(n,2,2,e,t),n[0]instanceof u.Z&&n[1]instanceof u.Z);else if(n[0]instanceof u.Z&&null===n[1]);else if(n[1]instanceof u.Z&&null===n[0]);else if(null!==n[0]||null!==n[1])throw new m.aV(e,m.rH.InvalidParameter,t)}e.disjoint=function(e,r){return t(e,r,((t,i,s)=>(s=(0,a.G)(s),n(e,r,s),null===s[0]||null===s[1]||g.disjoint(s[0],s[1]))))},e.intersects=function(e,r){return t(e,r,((t,i,s)=>(s=(0,a.G)(s),n(e,r,s),null!==s[0]&&null!==s[1]&&g.intersects(s[0],s[1]))))},e.touches=function(e,r){return t(e,r,((t,i,s)=>(s=(0,a.G)(s),n(e,r,s),null!==s[0]&&null!==s[1]&&g.touches(s[0],s[1]))))},e.crosses=function(e,r){return t(e,r,((t,i,s)=>(s=(0,a.G)(s),n(e,r,s),null!==s[0]&&null!==s[1]&&g.crosses(s[0],s[1]))))},e.within=function(e,r){return t(e,r,((t,i,s)=>(s=(0,a.G)(s),n(e,r,s),null!==s[0]&&null!==s[1]&&g.within(s[0],s[1]))))},e.contains=function(e,r){return t(e,r,((t,i,s)=>(s=(0,a.G)(s),n(e,r,s),null!==s[0]&&null!==s[1]&&g.contains(s[0],s[1]))))},e.overlaps=function(e,r){return t(e,r,((t,i,s)=>(s=(0,a.G)(s),n(e,r,s),null!==s[0]&&null!==s[1]&&g.overlaps(s[0],s[1]))))},e.equals=function(e,n){return t(e,n,((t,r,i)=>((0,a.y)(i,2,2,e,n),i[0]===i[1]||(i[0]instanceof u.Z&&i[1]instanceof u.Z?g.equals(i[0],i[1]):!(!(0,a.k)(i[0])||!(0,a.k)(i[1]))&&i[0].equals(i[1])))))},e.relate=function(e,n){return t(e,n,((t,r,i)=>{if(i=(0,a.G)(i),(0,a.y)(i,3,3,e,n),i[0]instanceof u.Z&&i[1]instanceof u.Z)return g.relate(i[0],i[1],(0,a.j)(i[2]));if(i[0]instanceof u.Z&&null===i[1])return!1;if(i[1]instanceof u.Z&&null===i[0])return!1;if(null===i[0]&&null===i[1])return!1;throw new m.aV(e,m.rH.InvalidParameter,n)}))},e.intersection=function(e,r){return t(e,r,((t,i,s)=>(s=(0,a.G)(s),n(e,r,s),null===s[0]||null===s[1]?null:g.intersect(s[0],s[1]))))},e.union=function(e,n){return t(e,n,((t,r,s)=>{const o=[];if(0===(s=(0,a.G)(s)).length)throw new m.aV(e,m.rH.WrongNumberOfParameters,n);if(1===s.length)if((0,a.m)(s[0])){const t=(0,a.G)(s[0]);for(let r=0;r<t.length;r++)if(null!==t[r]){if(!(t[r]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);o.push(t[r])}}else{if(!(0,a.x)(s[0])){if(s[0]instanceof u.Z)return(0,a.q)((0,i.r1)(s[0]),e.spatialReference);if(null===s[0])return null;throw new m.aV(e,m.rH.InvalidParameter,n)}{const t=(0,a.G)(s[0].toArray());for(let r=0;r<t.length;r++)if(null!==t[r]){if(!(t[r]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);o.push(t[r])}}}else for(let t=0;t<s.length;t++)if(null!==s[t]){if(!(s[t]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);o.push(s[t])}return 0===o.length?null:g.union(o)}))},e.difference=function(e,r){return t(e,r,((t,s,o)=>(o=(0,a.G)(o),n(e,r,o),null!==o[0]&&null===o[1]?(0,i.r1)(o[0]):null===o[0]?null:g.difference(o[0],o[1]))))},e.symmetricdifference=function(e,r){return t(e,r,((t,s,o)=>(o=(0,a.G)(o),n(e,r,o),null===o[0]&&null===o[1]?null:null===o[0]?(0,i.r1)(o[1]):null===o[1]?(0,i.r1)(o[0]):g.symmetricDifference(o[0],o[1]))))},e.clip=function(e,n){return t(e,n,((t,r,i)=>{if(i=(0,a.G)(i),(0,a.y)(i,2,2,e,n),!(i[1]instanceof o.Z)&&null!==i[1])throw new m.aV(e,m.rH.InvalidParameter,n);if(null===i[0])return null;if(!(i[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return null===i[1]?null:g.clip(i[0],i[1])}))},e.cut=function(e,n){return t(e,n,((t,r,s)=>{if(s=(0,a.G)(s),(0,a.y)(s,2,2,e,n),!(s[1]instanceof h.Z)&&null!==s[1])throw new m.aV(e,m.rH.InvalidParameter,n);if(null===s[0])return[];if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return null===s[1]?[(0,i.r1)(s[0])]:g.cut(s[0],s[1])}))},e.area=function(e,n){return t(e,n,((t,r,s)=>{if((0,a.y)(s,1,2,e,n),null===(s=(0,a.G)(s))[0])return 0;if((0,a.m)(s[0])||(0,a.x)(s[0])){const t=(0,a.J)(s[0],e.spatialReference);return null===t?0:g.planarArea(t,(0,i.EI)((0,a.A)(s[1],-1)))}if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return g.planarArea(s[0],(0,i.EI)((0,a.A)(s[1],-1)))}))},e.areageodetic=function(e,n){return t(e,n,((t,r,s)=>{if((0,a.y)(s,1,2,e,n),null===(s=(0,a.G)(s))[0])return 0;if((0,a.m)(s[0])||(0,a.x)(s[0])){const t=(0,a.J)(s[0],e.spatialReference);return null===t?0:g.geodesicArea(t,(0,i.EI)((0,a.A)(s[1],-1)))}if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return g.geodesicArea(s[0],(0,i.EI)((0,a.A)(s[1],-1)))}))},e.length=function(e,n){return t(e,n,((t,r,s)=>{if((0,a.y)(s,1,2,e,n),null===(s=(0,a.G)(s))[0])return 0;if((0,a.m)(s[0])||(0,a.x)(s[0])){const t=(0,a.H)(s[0],e.spatialReference);return null===t?0:g.planarLength(t,(0,i.Lz)((0,a.A)(s[1],-1)))}if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return g.planarLength(s[0],(0,i.Lz)((0,a.A)(s[1],-1)))}))},e.length3d=function(e,n){return t(e,n,((t,r,s)=>{if((0,a.y)(s,1,2,e,n),null===(s=(0,a.G)(s))[0])return 0;if((0,a.m)(s[0])||(0,a.x)(s[0])){const t=(0,a.H)(s[0],e.spatialReference);return null===t?0:!0===t.hasZ?w(t,(0,i.Lz)((0,a.A)(s[1],-1))):g.planarLength(t,(0,i.Lz)((0,a.A)(s[1],-1)))}if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return!0===s[0].hasZ?w(s[0],(0,i.Lz)((0,a.A)(s[1],-1))):g.planarLength(s[0],(0,i.Lz)((0,a.A)(s[1],-1)))}))},e.lengthgeodetic=function(e,n){return t(e,n,((t,r,s)=>{if((0,a.y)(s,1,2,e,n),null===(s=(0,a.G)(s))[0])return 0;if((0,a.m)(s[0])||(0,a.x)(s[0])){const t=(0,a.H)(s[0],e.spatialReference);return null===t?0:g.geodesicLength(t,(0,i.Lz)((0,a.A)(s[1],-1)))}if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return g.geodesicLength(s[0],(0,i.Lz)((0,a.A)(s[1],-1)))}))},e.distance=function(e,n){return t(e,n,((t,r,s)=>{s=(0,a.G)(s),(0,a.y)(s,2,3,e,n);let o=s[0];((0,a.m)(s[0])||(0,a.x)(s[0]))&&(o=(0,a.K)(s[0],e.spatialReference));let l=s[1];if(((0,a.m)(s[1])||(0,a.x)(s[1]))&&(l=(0,a.K)(s[1],e.spatialReference)),!(o instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);if(!(l instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return g.distance(o,l,(0,i.Lz)((0,a.A)(s[2],-1)))}))},e.distancegeodetic=function(e,n){return t(e,n,((t,r,s)=>{s=(0,a.G)(s),(0,a.y)(s,2,3,e,n);const o=s[0],u=s[1];if(!(o instanceof c.Z))throw new m.aV(e,m.rH.InvalidParameter,n);if(!(u instanceof c.Z))throw new m.aV(e,m.rH.InvalidParameter,n);const l=new h.Z({paths:[],spatialReference:o.spatialReference});return l.addPath([o,u]),g.geodesicLength(l,(0,i.Lz)((0,a.A)(s[2],-1)))}))},e.densify=function(e,n){return t(e,n,((t,r,s)=>{if(s=(0,a.G)(s),(0,a.y)(s,2,3,e,n),null===s[0])return null;if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);const l=(0,a.g)(s[1]);if(isNaN(l))throw new m.aV(e,m.rH.InvalidParameter,n);if(l<=0)throw new m.aV(e,m.rH.InvalidParameter,n);return s[0]instanceof d.Z||s[0]instanceof h.Z?g.densify(s[0],l,(0,i.Lz)((0,a.A)(s[2],-1))):s[0]instanceof o.Z?g.densify(D(s[0]),l,(0,i.Lz)((0,a.A)(s[2],-1))):s[0]}))},e.densifygeodetic=function(e,n){return t(e,n,((t,r,s)=>{if(s=(0,a.G)(s),(0,a.y)(s,2,3,e,n),null===s[0])return null;if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);const l=(0,a.g)(s[1]);if(isNaN(l))throw new m.aV(e,m.rH.InvalidParameter,n);if(l<=0)throw new m.aV(e,m.rH.InvalidParameter,n);return s[0]instanceof d.Z||s[0]instanceof h.Z?g.geodesicDensify(s[0],l,(0,i.Lz)((0,a.A)(s[2],-1))):s[0]instanceof o.Z?g.geodesicDensify(D(s[0]),l,(0,i.Lz)((0,a.A)(s[2],-1))):s[0]}))},e.generalize=function(e,n){return t(e,n,((t,r,s)=>{if(s=(0,a.G)(s),(0,a.y)(s,2,4,e,n),null===s[0])return null;if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);const o=(0,a.g)(s[1]);if(isNaN(o))throw new m.aV(e,m.rH.InvalidParameter,n);return g.generalize(s[0],o,(0,a.h)((0,a.A)(s[2],!0)),(0,i.Lz)((0,a.A)(s[3],-1)))}))},e.buffer=function(e,n){return t(e,n,((t,r,s)=>{if(s=(0,a.G)(s),(0,a.y)(s,2,3,e,n),null===s[0])return null;if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);const o=(0,a.g)(s[1]);if(isNaN(o))throw new m.aV(e,m.rH.InvalidParameter,n);return 0===o?(0,i.r1)(s[0]):g.buffer(s[0],o,(0,i.Lz)((0,a.A)(s[2],-1)))}))},e.buffergeodetic=function(e,n){return t(e,n,((t,r,s)=>{if(s=(0,a.G)(s),(0,a.y)(s,2,3,e,n),null===s[0])return null;if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);const o=(0,a.g)(s[1]);if(isNaN(o))throw new m.aV(e,m.rH.InvalidParameter,n);return 0===o?(0,i.r1)(s[0]):g.geodesicBuffer(s[0],o,(0,i.Lz)((0,a.A)(s[2],-1)))}))},e.offset=function(e,n){return t(e,n,((t,r,s)=>{if(s=(0,a.G)(s),(0,a.y)(s,2,6,e,n),null===s[0])return null;if(!(s[0]instanceof d.Z||s[0]instanceof h.Z))throw new m.aV(e,m.rH.InvalidParameter,n);const o=(0,a.g)(s[1]);if(isNaN(o))throw new m.aV(e,m.rH.InvalidParameter,n);const u=(0,a.g)((0,a.A)(s[4],10));if(isNaN(u))throw new m.aV(e,m.rH.InvalidParameter,n);const l=(0,a.g)((0,a.A)(s[5],0));if(isNaN(l))throw new m.aV(e,m.rH.InvalidParameter,n);return g.offset(s[0],o,(0,i.Lz)((0,a.A)(s[2],-1)),(0,a.j)((0,a.A)(s[3],"round")).toLowerCase(),u,l)}))},e.rotate=function(e,n){return t(e,n,((t,r,i)=>{i=(0,a.G)(i),(0,a.y)(i,2,3,e,n);let s=i[0];if(null===s)return null;if(!(s instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);s instanceof o.Z&&(s=d.Z.fromExtent(s));const l=(0,a.g)(i[1]);if(isNaN(l))throw new m.aV(e,m.rH.InvalidParameter,n);const h=(0,a.A)(i[2],null);if(null===h)return g.rotate(s,l);if(h instanceof c.Z)return g.rotate(s,l,h);throw new m.aV(e,m.rH.InvalidParameter,n)}))},e.centroid=function(e,n){return t(e,n,((t,r,f)=>{if(f=(0,a.G)(f),(0,a.y)(f,1,1,e,n),null===f[0])return null;let p=f[0];if(((0,a.m)(f[0])||(0,a.x)(f[0]))&&(p=(0,a.K)(f[0],e.spatialReference)),null===p)return null;if(!(p instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return p instanceof c.Z?(0,a.q)((0,i.r1)(f[0]),e.spatialReference):p instanceof d.Z?p.centroid:p instanceof h.Z?(0,s.s9)(p):p instanceof l.Z?(0,s.Ay)(p):p instanceof o.Z?p.center:null}))},e.multiparttosinglepart=function(e,n){return t(e,n,((t,r,s)=>{s=(0,a.G)(s),(0,a.y)(s,1,1,e,n);const p=[];if(null===s[0])return null;if(!(s[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);if(s[0]instanceof c.Z)return[(0,a.q)((0,i.r1)(s[0]),e.spatialReference)];if(s[0]instanceof o.Z)return[(0,a.q)((0,i.r1)(s[0]),e.spatialReference)];const D=g.simplify(s[0]);if(D instanceof d.Z){const e=[],t=[];for(let n=0;n<D.rings.length;n++)if(D.isClockwise(D.rings[n])){const t=(0,f.im)({rings:[D.rings[n]],hasZ:!0===D.hasZ,hasM:!0===D.hasM,spatialReference:D.spatialReference.toJSON()});e.push(t)}else t.push({ring:D.rings[n],pt:D.getPoint(n,0)});for(let n=0;n<t.length;n++)for(let r=0;r<e.length;r++)if(e[r].contains(t[n].pt)){e[r].addRing(t[n].ring);break}return e}if(D instanceof h.Z){const e=[];for(let t=0;t<D.paths.length;t++){const n=(0,f.im)({paths:[D.paths[t]],hasZ:!0===D.hasZ,hasM:!0===D.hasM,spatialReference:D.spatialReference.toJSON()});e.push(n)}return e}if(s[0]instanceof l.Z){const t=(0,a.q)((0,i.r1)(s[0]),e.spatialReference);for(let e=0;e<t.points.length;e++)p.push(t.getPoint(e));return p}return null}))},e.issimple=function(e,n){return t(e,n,((t,r,i)=>{if(i=(0,a.G)(i),(0,a.y)(i,1,1,e,n),null===i[0])return!0;if(!(i[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return g.isSimple(i[0])}))},e.simplify=function(e,n){return t(e,n,((t,r,i)=>{if(i=(0,a.G)(i),(0,a.y)(i,1,1,e,n),null===i[0])return null;if(!(i[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return g.simplify(i[0])}))},e.convexhull=function(e,n){return t(e,n,((t,r,i)=>{if(i=(0,a.G)(i),(0,a.y)(i,1,1,e,n),null===i[0])return null;if(!(i[0]instanceof u.Z))throw new m.aV(e,m.rH.InvalidParameter,n);return g.convexHull(i[0])}))}}},38176:(e,t,n)=>{n.d(t,{r:()=>s});var r=n(61363),i=n(14808);function a(e,t,n){return void 0===n||0==+n?Math[e](t):(t=+t,n=+n,isNaN(t)||"number"!=typeof n||n%1!=0?NaN:(t=t.toString().split("e"),+((t=(t=Math[e](+(t[0]+"e"+(t[1]?+t[1]-n:-n)))).toString().split("e"))[0]+"e"+(t[1]?+t[1]+n:n))))}function s(e,t){function n(e,t,n){const i=(0,r.g)(e);return isNaN(i)?i:isNaN(t)||isNaN(n)||t>n?NaN:i<t?t:i>n?n:i}e.number=function(e,n){return t(e,n,((t,a,s)=>{(0,r.y)(s,1,2,e,n);const o=s[0];if((0,r.b)(o))return o;if(null===o)return 0;if((0,r.k)(o))return o.toNumber();if((0,r.a)(o))return Number(o);if((0,r.m)(o))return NaN;if(""===o)return Number(o);if(void 0===o)return Number(o);if((0,r.c)(o)){if(void 0!==s[1]){let e=(0,r.L)(s[1],"‰","");return e=(0,r.L)(e,"¤",""),(0,i.Qc)(o,{pattern:e})}return Number(o.trim())}return Number(o)}))},e.abs=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),Math.abs((0,r.g)(a[0])))))},e.acos=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),Math.acos((0,r.g)(a[0])))))},e.asin=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),Math.asin((0,r.g)(a[0])))))},e.atan=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),Math.atan((0,r.g)(a[0])))))},e.atan2=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,2,2,e,n),Math.atan2((0,r.g)(a[0]),(0,r.g)(a[1])))))},e.ceil=function(e,n){return t(e,n,((t,i,s)=>{if((0,r.y)(s,1,2,e,n),2===s.length){let e=(0,r.g)(s[1]);return isNaN(e)&&(e=0),a("ceil",(0,r.g)(s[0]),-1*e)}return Math.ceil((0,r.g)(s[0]))}))},e.round=function(e,n){return t(e,n,((t,i,s)=>{if((0,r.y)(s,1,2,e,n),2===s.length){let e=(0,r.g)(s[1]);return isNaN(e)&&(e=0),a("round",(0,r.g)(s[0]),-1*e)}return Math.round((0,r.g)(s[0]))}))},e.floor=function(e,n){return t(e,n,((t,i,s)=>{if((0,r.y)(s,1,2,e,n),2===s.length){let e=(0,r.g)(s[1]);return isNaN(e)&&(e=0),a("floor",(0,r.g)(s[0]),-1*e)}return Math.floor((0,r.g)(s[0]))}))},e.cos=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),Math.cos((0,r.g)(a[0])))))},e.isnan=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),"number"==typeof a[0]&&isNaN(a[0]))))},e.exp=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),Math.exp((0,r.g)(a[0])))))},e.log=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),Math.log((0,r.g)(a[0])))))},e.pow=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,2,2,e,n),(0,r.g)(a[0])**(0,r.g)(a[1]))))},e.random=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,0,0,e,n),Math.random())))},e.sin=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),Math.sin((0,r.g)(a[0])))))},e.sqrt=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),Math.sqrt((0,r.g)(a[0])))))},e.tan=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),Math.tan((0,r.g)(a[0])))))},e.defaultvalue=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,2,2,e,n),null===a[0]||""===a[0]||void 0===a[0]?a[1]:a[0])))},e.isempty=function(e,n){return t(e,n,((t,i,a)=>((0,r.y)(a,1,1,e,n),null===a[0]||""===a[0]||void 0===a[0])))},e.boolean=function(e,n){return t(e,n,((t,i,a)=>{(0,r.y)(a,1,1,e,n);const s=a[0];return(0,r.h)(s)}))},e.constrain=function(e,i){return t(e,i,((t,a,s)=>{(0,r.y)(s,3,3,e,i);const o=(0,r.g)(s[1]),u=(0,r.g)(s[2]);if((0,r.m)(s[0])){const e=[];for(const t of s[0])e.push(n(t,o,u));return e}if((0,r.x)(s[0])){const e=[];for(let t=0;t<s[0].length();t++)e.push(n(s[0].get(t),o,u));return e}return n(s[0],o,u)}))}}},58130:(e,t,n)=>{n.d(t,{r:()=>o});var r=n(27535),i=n(61363),a=n(24240);function s(e,t,n,r){if(1===r.length){if((0,i.m)(r[0]))return(0,a.t)(e,r[0],-1);if((0,i.x)(r[0]))return(0,a.t)(e,r[0].toArray(),-1)}return(0,a.t)(e,r,-1)}function o(e,t){e.stdev=function(e,n){return t(e,n,((e,t,n)=>s("stdev",0,0,n)))},e.variance=function(e,n){return t(e,n,((e,t,n)=>s("variance",0,0,n)))},e.average=function(e,n){return t(e,n,((e,t,n)=>s("mean",0,0,n)))},e.mean=function(e,n){return t(e,n,((e,t,n)=>s("mean",0,0,n)))},e.sum=function(e,n){return t(e,n,((e,t,n)=>s("sum",0,0,n)))},e.min=function(e,n){return t(e,n,((e,t,n)=>s("min",0,0,n)))},e.max=function(e,n){return t(e,n,((e,t,n)=>s("max",0,0,n)))},e.distinct=function(e,n){return t(e,n,((e,t,n)=>s("distinct",0,0,n)))},e.count=function(e,n){return t(e,n,((t,a,s)=>{if((0,i.y)(s,1,1,e,n),(0,i.m)(s[0])||(0,i.c)(s[0]))return s[0].length;if((0,i.x)(s[0]))return s[0].length();throw new r.aV(e,r.rH.InvalidParameter,n)}))}}},27360:(e,t,n)=>{n.d(t,{r:()=>pe});var r=n(28228),i=n(80692),a=n(33586),s=n(27535),o=n(61363),u=n(90658),l=n(70586);const c=e=>(t,n,r)=>(r=r||14,+e(t,n).toFixed(r)),d=(e,t)=>e+t,h=(e,t)=>e*t,f=(e,t)=>e/t,m=(e,t,n)=>c(d)(e,t,n),p=(e,t,n)=>c(h)(e,t,n),g=(e,t,n)=>c(f)(e,t,n),D=360,y=2*Math.PI,w=3600,x=60,F=180*w/Math.PI,C=60*D*x,A=90*w,b=180*w,E=String.fromCharCode(7501);function v(e){if(!1===(0,o.c)(e))throw new s.aV(null,s.rH.InvalidParameter,null);return e}function S(e,t){const n=10**t;return Math.round(e*n)/n}function k(e){const t=parseFloat(e.toString().replace(Math.trunc(e).toString(),"0"))*Math.sign(e);return e<0?{fraction:t,integer:Math.ceil(e)}:{fraction:t,integer:Math.floor(e)}}var I,T,B,_;function M(e,t){switch(e){case I.north:return"SHORT"===t?"N":"North";case I.east:return"SHORT"===t?"E":"East";case I.south:return"SHORT"===t?"S":"South";case I.west:return"SHORT"===t?"W":"West"}}function N(e,t,n){for(;e.length<n;)e=t+e;return e}function L(e,t){return e-Math.floor(e/t)*t}function H(e){switch(e){case T.truncated_degrees:case T.decimal_degrees:return D;case T.radians:return y;case T.gradians:return 400;case T.seconds:return C;case T.fractional_degree_minutes:return 60;case T.fractional_minute_seconds:return x;default:throw new s.aV(null,s.rH.LogicError,null,{reason:"unsupported evaluations"})}}function R(e){switch(e.toUpperCase().trim()){case"NORTH":case"NORTHAZIMUTH":case"NORTH AZIMUTH":return B.north_azimuth;case"POLAR":return B.polar;case"QUADRANT":return B.quadrant;case"SOUTH":case"SOUTHAZIMUTH":case"SOUTH AZIMUTH":return B.south_azimuth}throw new s.aV(null,s.rH.LogicError,null,{reason:"unsupported directionType"})}function O(e){switch(e.toUpperCase().trim()){case"D":case"DD":case"DECIMALDEGREE":case"DECIMAL DEGREE":case"DEGREE":case"DECIMALDEGREES":case"DECIMAL DEGREES":case"DEGREES":return T.decimal_degrees;case"DMS":case"DEGREESMINUTESSECONDS":case"DEGREES MINUTES SECONDS":return T.degrees_minutes_seconds;case"R":case"RAD":case"RADS":case"RADIAN":case"RADIANS":return T.radians;case"G":case"GON":case"GONS":case"GRAD":case"GRADS":case"GRADIAN":case"GRADIANS":return T.gradians}throw new s.aV(null,s.rH.LogicError,null,{reason:"unsupported units"})}!function(e){e[e.north=0]="north",e[e.east=1]="east",e[e.south=2]="south",e[e.west=3]="west"}(I||(I={})),function(e){e[e.decimal_degrees=1]="decimal_degrees",e[e.seconds=2]="seconds",e[e.degrees_minutes_seconds=3]="degrees_minutes_seconds",e[e.radians=4]="radians",e[e.gradians=5]="gradians",e[e.truncated_degrees=6]="truncated_degrees",e[e.fractional_degree_minutes=7]="fractional_degree_minutes",e[e.fractional_minute_seconds=8]="fractional_minute_seconds"}(T||(T={})),function(e){e[e.north_azimuth=1]="north_azimuth",e[e.polar=2]="polar",e[e.quadrant=3]="quadrant",e[e.south_azimuth=4]="south_azimuth"}(B||(B={})),function(e){e[e.meridian=0]="meridian",e[e.direction=1]="direction"}(_||(_={}));class V{constructor(e,t,n){this.m_degrees=e,this.m_minutes=t,this.m_seconds=n}getField(e){switch(e){case T.decimal_degrees:case T.truncated_degrees:return this.m_degrees;case T.fractional_degree_minutes:return this.m_minutes;case T.seconds:case T.fractional_minute_seconds:return this.m_seconds;default:throw new s.aV(null,s.rH.LogicError,null,{reason:"unexpected evaluation"})}}static secondsToDMS(e){const t=k(e).fraction;let n=k(e).integer;const r=Math.floor(n/w);n-=r*w;const i=Math.floor(n/x);return n-=i*x,new V(r,i,n+t)}static numberToDms(e){const t=k(e).fraction,n=k(e).integer,r=p(k(100*t).fraction,100),i=k(100*t).integer;return new V(n,i,r)}format(e,t){let n=S(this.m_seconds,t),r=this.m_minutes,i=this.m_degrees;if(e===T.seconds||e===T.fractional_minute_seconds)x<=n&&(n-=x,++r),60<=r&&(r=0,++i),D<=i&&(i=0);else if(e===T.fractional_degree_minutes)n=0,r=30<=this.m_seconds?this.m_minutes+1:this.m_minutes,i=this.m_degrees,60<=r&&(r=0,++i),D<=i&&(i=0);else if(e===T.decimal_degrees||e===T.truncated_degrees){const e=g(this.m_seconds,w),t=g(this.m_minutes,60);i=Math.round(this.m_degrees+t+e),r=0,n=0}return new V(i,r,n)}static dmsToSeconds(e,t,n){return e*w+t*x+n}}class P{constructor(e,t,n){this.meridian=e,this.angle=t,this.direction=n}fetchAzimuth(e){return e===_.meridian?this.meridian:this.direction}}class Z{constructor(e){this._angle=e}static createFromAngleAndDirection(e,t){return new Z(new G(Z._convertDirectionFormat(e.extractAngularUnits(T.seconds),t,B.north_azimuth)))}getAngle(e){const t=this._angle.extractAngularUnits(T.seconds);switch(e){case B.north_azimuth:case B.south_azimuth:case B.polar:return new G(Z._convertDirectionFormat(t,B.north_azimuth,e));case B.quadrant:{const e=Z.secondsNorthAzimuthToQuadrant(t);return new G(e.angle)}}}getMeridian(e){const t=this._angle.extractAngularUnits(T.seconds);switch(e){case B.north_azimuth:return I.north;case B.south_azimuth:return I.south;case B.polar:return I.east;case B.quadrant:return Z.secondsNorthAzimuthToQuadrant(t).meridian}}getDirection(e){const t=this._angle.extractAngularUnits(T.seconds);switch(e){case B.north_azimuth:return I.east;case B.south_azimuth:return I.west;case B.polar:return I.north;case B.quadrant:return Z.secondsNorthAzimuthToQuadrant(t).direction}}static secondsNorthAzimuthToQuadrant(e){const t=e<=A||e>=972e3?I.north:I.south,n=t===I.north?Math.min(C-e,e):Math.abs(e-b),r=e>b?I.west:I.east;return new P(t,n,r)}static createFromAngleMeridianAndDirection(e,t,n){return new Z(new G(Z.secondsQuadrantToNorthAzimuth(e.extractAngularUnits(T.seconds),t,n)))}static secondsQuadrantToNorthAzimuth(e,t,n){return t===I.north?n===I.east?e:C-e:n===I.east?b-e:b+e}static _convertDirectionFormat(e,t,n){let r=0;switch(t){case B.north_azimuth:r=e;break;case B.polar:r=A-e;break;case B.quadrant:throw new s.aV(null,s.rH.LogicError,null,{reason:"unexpected evaluation"});case B.south_azimuth:r=e+b}let i=0;switch(n){case B.north_azimuth:i=r;break;case B.polar:i=A-r;break;case B.quadrant:throw new s.aV(null,s.rH.LogicError,null,{reason:"unexpected evaluation"});case B.south_azimuth:i=r-b}return i=function(e,t){return e%1296e3}(i),i<0?C+i:i}}function U(e,t,n){let r=null;switch(t){case T.decimal_degrees:r=p(e,w);break;case T.seconds:r=e;break;case T.gradians:r=p(e,3240);break;case T.radians:r=p(e,F);break;default:throw new s.aV(null,s.rH.LogicError,null,{reason:"unexpected evaluation"})}switch(n){case T.decimal_degrees:return g(r,w);case T.seconds:return r;case T.gradians:return g(r,3240);case T.radians:return r/F;default:throw new s.aV(null,s.rH.LogicError,null,{reason:"unexpected evaluation"})}}class G{constructor(e){this._seconds=e}static createFromAngleAndUnits(e,t){return new G(U(e,t,T.seconds))}extractAngularUnits(e){return U(this._seconds,T.seconds,z(e))}static createFromDegreesMinutesSeconds(e,t,n){return new G(m(m(p(e,w),p(t,x)),n))}}function z(e){switch((0,l.O3)(e),e){case T.decimal_degrees:case T.truncated_degrees:case T.degrees_minutes_seconds:return T.decimal_degrees;case T.gradians:return T.gradians;case T.fractional_degree_minutes:return T.fractional_degree_minutes;case T.radians:return T.radians;case T.seconds:case T.fractional_minute_seconds:return T.seconds}}class q{constructor(e,t,n,r){this.view=e,this.angle=t,this.merdian=n,this.direction=r,this._dms=null,this._formattedDms=null}static createFromStringAndBearing(e,t,n){return new q(e,t.getAngle(n),t.getMeridian(n),t.getDirection(n))}fetchAngle(){return this.angle}fetchMeridian(){return this.merdian}fetchDirection(){return this.direction}fetchView(){return this.view}fetchDms(){return null===this._dms&&this._calculateDms(),this._dms}fetchFormattedDms(){return null===this._formattedDms&&this._calculateDms(),this._formattedDms}_calculateDms(){let e=null,t=T.truncated_degrees,n=0;for(let r=0;r<this.view.length;r++){const i=this.view[r];switch(i){case"m":e=$(this.view,r,i),t=t===T.truncated_degrees?T.fractional_degree_minutes:t,r=e.newpos;continue;case"s":e=$(this.view,r,i),t=T.fractional_minute_seconds,n=n<e.rounding?e.rounding:n,r=e.newpos;continue;default:continue}}this._dms=V.secondsToDMS(this.angle.extractAngularUnits(T.seconds)),this._formattedDms=V.secondsToDMS(this.angle.extractAngularUnits(T.seconds)).format(t,n)}}function j(e,t,n,r,i){let a=null;switch(t){case T.decimal_degrees:case T.radians:case T.gradians:return a=L(S(e.extractAngularUnits(t),r),H(t)),N(a.toFixed(r),"0",n+r+(r>0?1:0));case T.truncated_degrees:case T.fractional_degree_minutes:return a=L(i.fetchFormattedDms().getField(t),H(t)),N(a.toFixed(r),"0",n+r+(r>0?1:0));case T.fractional_minute_seconds:return a=L(S(i.fetchDms().getField(t),r),H(t)),N(a.toFixed(r),"0",n+r+(r>0?1:0));default:throw new s.aV(null,s.rH.LogicError,null,{reason:"unexpected evaluation"})}}function J(e){switch(e.toUpperCase().trim()){case"N":case"NORTH":return I.north;case"E":case"EAST":return I.east;case"S":case"SOUTH":return I.south;case"W":case"WEST":return I.west}return null}function W(e){const t=parseFloat(e);if((0,o.b)(t)){if(isNaN(t))throw new s.aV(null,s.rH.LogicError,null,{reason:"invalid conversion"});return t}throw new s.aV(null,s.rH.LogicError,null,{reason:"invalid conversion"})}function K(e,t,n){const r=n===B.quadrant;let i=null,a=null,u=0,l=0,c=0;if(r){if(e.length<2)throw new s.aV(null,s.rH.LogicError,null,{reason:"conversion error"});c=1;const t=function(e){switch((0,o.g)(e)){case 1:return{first:I.north,second:I.east};case 2:return{first:I.south,second:I.east};case 3:return{first:I.south,second:I.west};case 4:return{first:I.north,second:I.west}}return null}((0,o.j)(e[e.length-1]));if(t?(i=t.first,a=t.second):(u=1,i=J((0,o.j)(e[0])),a=J((0,o.j)(e[e.length-1]))),null===i||null===a)throw new s.aV(null,s.rH.LogicError,null,{reason:"invalid conversion"})}switch(t){case T.decimal_degrees:case T.radians:case T.gradians:if(0===e.length)throw new s.aV(null,s.rH.LogicError,null,{reason:"invalid conversion"});return r?Z.createFromAngleMeridianAndDirection(G.createFromAngleAndUnits(W(e[u]),z(t)),i,a):Z.createFromAngleAndDirection(G.createFromAngleAndUnits(W(e[u]),z(t)),n);case T.degrees_minutes_seconds:if(l=e.length-c-u,3===l){const t=G.createFromDegreesMinutesSeconds(W(e[u]),W(e[u+1]),W(e[u+2]));return r?Z.createFromAngleMeridianAndDirection(t,i,a):Z.createFromAngleAndDirection(t,n)}if(1===l){const t=W(e[u]),s=V.numberToDms(t),o=G.createFromDegreesMinutesSeconds(s.m_degrees,s.m_minutes,s.m_seconds);return r?Z.createFromAngleMeridianAndDirection(o,i,a):Z.createFromAngleAndDirection(o,n)}}throw new s.aV(null,s.rH.LogicError,null,{reason:"invalid conversion"})}function $(e,t,n){const r={padding:0,rounding:0,newpos:t};let i=!1;for(;t<e.length;){const a=e[t];if(a===n)i?r.rounding++:r.padding++,t++;else{if("."!==a)break;i=!0,t++}}return r.newpos=t-1,r}function Y(e,t,n){const r={escaped:"",newpos:t};for(t++;t<e.length;){const n=e[t];if(t++,"]"===n)break;r.escaped+=n}return r.newpos=t-1,r}function Q(e,t,n){if(!(t instanceof a.Z))throw new s.aV(null,s.rH.InvalidParameter,null);if(!1===t.hasField("directionType"))throw new s.aV(null,s.rH.LogicError,null,{reason:"missing directionType"});if(!1===t.hasField("angleType"))throw new s.aV(null,s.rH.LogicError,null,{reason:"missing angleType"});const r=R(v(t.field("directiontype"))),i=function(e,t,n){if((0,o.b)(e))return function(e,t,n){if(n===B.quadrant)throw new s.aV(null,s.rH.LogicError,null,{reason:"conversion error"});if(t===T.degrees_minutes_seconds){const t=V.numberToDms(e);return Z.createFromAngleAndDirection(G.createFromDegreesMinutesSeconds(t.m_degrees,t.m_minutes,t.m_seconds),n)}return Z.createFromAngleAndDirection(G.createFromAngleAndUnits(e,z(t)),n)}((0,o.g)(e),t,n);if((0,o.c)(e))return K(function(e){const t=[" ","-","/","'",'"',"\\","^","°",E,"\t","\r","\n","*"];let n="";for(let r=0;r<e.length;r++){const i=e.charAt(r);t.includes(i)?n+="RRSPLITRRSPLITRR":n+=i}return n.split("RRSPLITRRSPLITRR").filter((e=>""!==e))}(e),t,n);if((0,o.m)(e))return K(e,t,n);if((0,o.x)(e))return K(e.toArray(),t,n);throw new s.aV(null,s.rH.LogicError,null,{reason:"conversion error"})}(e,O(v(t.field("angletype"))),r);if(!(n instanceof a.Z))throw new s.aV(null,s.rH.InvalidParameter,null);if(!1===n.hasField("directionType"))throw new s.aV(null,s.rH.LogicError,null,{reason:"missing directionType"});if(!1===n.hasField("outputType"))throw new s.aV(null,s.rH.LogicError,null,{reason:"missing angleType"});const u=R(v(n.field("directiontype"))),l=n.hasField("angleType")?O(v(n.field("angletype"))):null,c=v(n.field("outputType")).toUpperCase().trim();if(!u||!c)throw new s.aV(null,s.rH.LogicError,null,{reason:"conversion error"});if(!(l||"TEXT"===c&&n.hasField("format")))throw new s.aV(null,s.rH.LogicError,null,{reason:"invalid unit"});switch(c){case"VALUE":return u===B.quadrant||l===T.degrees_minutes_seconds?function(e,t,n){const r=e.getAngle(t);if(t===B.quadrant&&n===T.degrees_minutes_seconds){const n=V.secondsToDMS(r.extractAngularUnits(T.seconds));return[M(e.getMeridian(t),"SHORT"),n.m_degrees,n.m_minutes,n.m_seconds,M(e.getDirection(t),"SHORT")]}if(n===T.degrees_minutes_seconds){const e=V.secondsToDMS(r.extractAngularUnits(T.seconds));return[e.m_degrees,e.m_minutes,e.m_seconds]}return t===B.quadrant?[M(e.getMeridian(t),"SHORT"),r.extractAngularUnits(n),M(e.getDirection(t),"SHORT")]:[r.extractAngularUnits(n)]}(i,u,l):function(e,t,n){const r=z(n);if(r&&n!==T.degrees_minutes_seconds)return e.getAngle(t).extractAngularUnits(r);throw new s.aV(null,s.rH.LogicError,null,{reason:"conversion error"})}(i,u,l);case"TEXT":{let e="";return n.hasField("format")&&(e=(0,o.j)(n.field("format"))),null!==e&&""!==e||(e=function(e,t){let n="";switch(e){case T.decimal_degrees:n=t===B.quadrant?"DD.DD°":"DDD.DD°";break;case T.degrees_minutes_seconds:n=t===B.quadrant?"dd° mm' ss\"":"ddd° mm' ss.ss\"";break;case T.radians:n="R.RR";break;case T.gradians:n="GGG.GG"+E;break;default:throw new s.aV(null,s.rH.LogicError,null,{reason:"conversion error"})}return t===B.quadrant&&(n="p "+n+" b"),n}(l,u)),function(e,t,n){let r="",i=null,a=null;const s=q.createFromStringAndBearing(t,e,n),o={D:T.decimal_degrees,d:T.truncated_degrees,m:T.fractional_degree_minutes,s:T.fractional_minute_seconds,R:T.radians,G:T.gradians};for(let u=0;u<t.length;u++){const l=t[u];switch(l){case"[":i=Y(t,u),r+=i.escaped,u=i.newpos;continue;case"D":case"d":case"m":case"s":case"R":case"G":i=$(t,u,l),a=e.getAngle(n),r+=j(a,o[l],i.padding,i.rounding,s),u=i.newpos;continue;case"P":case"p":r+=M(s.fetchMeridian(),"p"===l?"SHORT":"LONG");continue;case"B":case"b":r+=M(s.fetchDirection(),"b"===l?"SHORT":"LONG");continue;default:r+=l}}return r}(i,e,u)}default:throw new s.aV(null,s.rH.InvalidParameter,null)}}const X=2654435761,ee=2246822519,te=3266489917,ne=668265263,re=374761393;function ie(e){const t=[];for(let n=0,r=e.length;n<r;n++){let r=e.charCodeAt(n);r<128?t.push(r):r<2048?t.push(192|r>>6,128|63&r):r<55296||r>=57344?t.push(224|r>>12,128|r>>6&63,128|63&r):(n++,r=65536+((1023&r)<<10|1023&e.charCodeAt(n)),t.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|63&r))}return new Uint8Array(t)}class ae{constructor(e){this._seed=e,this._totallen=0,this._bufs=[],this.init()}init(){return this._bufs=[],this._totallen=0,this}updateFloatArray(e){const t=[];for(const n of e)isNaN(n)?t.push("NaN"):n===1/0?t.push("Infinity"):n===-1/0?t.push("-Infinity"):0===n?t.push("0"):t.push(n.toString(16));this.update(ie(t.join("")))}updateIntArray(e){const t=Int32Array.from(e);this.update(new Uint8Array(t.buffer))}updateUint8Array(e){this.update(Uint8Array.from(e))}updateWithString(e){return this.update(ie(e))}update(e){return this._bufs.push(e),this._totallen+=e.length,this}digest(){const e=new Uint8Array(this._totallen);let t=0;for(const n of this._bufs)e.set(n,t),t+=n.length;return this.init(),this._xxHash32(e,this._seed)}_xxHash32(e,t=0){const n=e;let r=t+re&4294967295,i=0;if(n.length>=16){const n=[t+X+ee&4294967295,t+ee&4294967295,t+0&4294967295,t-X&4294967295],a=e,s=a.length-16;let o=0;for(i=0;(4294967280&i)<=s;i+=4){const e=i,t=a[e+0]+(a[e+1]<<8),r=a[e+2]+(a[e+3]<<8),s=t*ee+(r*ee<<16);let u=n[o]+s&4294967295;u=u<<13|u>>>19;const l=65535&u,c=u>>>16;n[o]=l*X+(c*X<<16)&4294967295,o=o+1&3}r=(n[0]<<1|n[0]>>>31)+(n[1]<<7|n[1]>>>25)+(n[2]<<12|n[2]>>>20)+(n[3]<<18|n[3]>>>14)&4294967295}r=r+e.length&4294967295;const a=e.length-4;for(;i<=a;i+=4){const e=i,t=n[e+0]+(n[e+1]<<8),a=n[e+2]+(n[e+3]<<8);r=r+(t*te+(a*te<<16))&4294967295,r=r<<17|r>>>15,r=(65535&r)*ne+((r>>>16)*ne<<16)&4294967295}for(;i<n.length;++i)r+=n[i]*re,r=r<<11|r>>>21,r=(65535&r)*X+((r>>>16)*X<<16)&4294967295;return r^=r>>>15,r=((65535&r)*ee&4294967295)+((r>>>16)*ee<<16),r^=r>>>13,r=((65535&r)*te&4294967295)+((r>>>16)*te<<16),r^=r>>>16,r<0?r+4294967296:r}}var se=n(6570),oe=n(65091),ue=n(94139),le=n(38913),ce=n(58901),de=n(82971),he=n(17452);function fe(e,t){if(!e||!t)return e===t;if(e.x===t.x&&e.y===t.y){if(e.hasZ){if(e.z!==t.z)return!1}else if(t.hasZ)return!1;if(e.hasM){if(e.m!==t.m)return!1}else if(t.hasM)return!1;return!0}return!1}function me(e,t,n){if(null!==e)if((0,o.m)(e)){if(t.updateUint8Array([61]),n.map.has(e)){const r=n.map.get(e);t.updateIntArray([61237541^r])}else{n.map.set(e,n.currentLength++);for(const r of e)me(r,t,n);n.map.delete(e),n.currentLength--}t.updateUint8Array([199])}else if((0,o.x)(e)){if(t.updateUint8Array([61]),n.map.has(e)){const r=n.map.get(e);t.updateIntArray([61237541^r])}else{n.map.set(e,n.currentLength++);for(const r of e.toArray())me(r,t,n);n.map.delete(e),n.currentLength--}t.updateUint8Array([199])}else{if((0,o.k)(e))return t.updateIntArray([e.toNumber()]),void t.updateUint8Array([241]);if((0,o.c)(e))return t.updateIntArray([e.length]),t.updateWithString(e),void t.updateUint8Array([41]);if((0,o.a)(e))t.updateUint8Array([!0===e?1:0,113]);else{if((0,o.b)(e))return t.updateFloatArray([e]),void t.updateUint8Array([173]);if(e instanceof i.Z)throw new s.aV(n.context,s.rH.UnsupportedHashType,n.node);if(e instanceof r.Z)throw new s.aV(n.context,s.rH.UnsupportedHashType,n.node);if(!(e instanceof a.Z)){if((0,o.w)(e))throw new s.aV(n.context,s.rH.UnsupportedHashType,n.node);if(e instanceof ue.Z)return t.updateIntArray([3833836621]),t.updateIntArray([0]),t.updateFloatArray([e.x]),t.updateIntArray([1]),t.updateFloatArray([e.y]),e.hasZ&&(t.updateIntArray([2]),t.updateFloatArray([e.z])),e.hasM&&(t.updateIntArray([3]),t.updateFloatArray([e.m])),t.updateIntArray([3765347959]),void me(e.spatialReference.wkid,t,n);if(e instanceof le.Z){t.updateIntArray([1266616829]);for(let r=0;r<e.rings.length;r++){const i=e.rings[r],a=[];let s=null,o=null;for(let t=0;t<i.length;t++){const n=e.getPoint(r,t);if(0===t)s=n;else if(fe(o,n))continue;o=n,t===i.length-1&&fe(s,n)||a.push(n)}t.updateIntArray([1397116793,a.length]);for(let e=0;e<a.length;e++){const r=a[e];t.updateIntArray([3962308117,e]),me(r,t,n),t.updateIntArray([2716288009])}t.updateIntArray([2278822459])}return t.updateIntArray([3878477243]),void me(e.spatialReference.wkid,t,n)}if(e instanceof ce.Z){t.updateIntArray([4106883559]);for(let r=0;r<e.paths.length;r++){const i=e.paths[r];t.updateIntArray([1397116793,i.length]);for(let a=0;a<i.length;a++)t.updateIntArray([3962308117,a]),me(e.getPoint(r,a),t,n),t.updateIntArray([2716288009]);t.updateIntArray([2278822459])}return t.updateIntArray([2568784753]),void me(e.spatialReference.wkid,t,n)}if(e instanceof oe.Z){t.updateIntArray([588535921,e.points.length]);for(let r=0;r<e.points.length;r++){const i=e.getPoint(r);t.updateIntArray([r]),me(i,t,n)}return t.updateIntArray([1700171621]),void me(e.spatialReference.wkid,t,n)}if(e instanceof se.Z)return t.updateIntArray([3483648373]),t.updateIntArray([0]),t.updateFloatArray([e.xmax]),t.updateIntArray([1]),t.updateFloatArray([e.xmin]),t.updateIntArray([2]),t.updateFloatArray([e.ymax]),t.updateIntArray([3]),t.updateFloatArray([e.ymin]),e.hasZ&&(t.updateIntArray([4]),t.updateFloatArray([e.zmax]),t.updateIntArray([5]),t.updateFloatArray([e.zmin])),e.hasM&&(t.updateIntArray([6]),t.updateFloatArray([e.mmax]),t.updateIntArray([7]),t.updateFloatArray([e.mmin])),t.updateIntArray([3622027469]),void me(e.spatialReference.wkid,t,n);if(e instanceof de.Z)return t.updateIntArray([14]),void 0!==e.wkid&&null!==e.wkid&&t.updateIntArray([e.wkid]),void(e.wkt&&t.updateWithString(e.wkt));if((0,o.i)(e))throw new s.aV(n.context,s.rH.UnsupportedHashType,n.node);if((0,o.T)(e))throw new s.aV(n.context,s.rH.UnsupportedHashType,n.node);if((0,o.U)(e))throw new s.aV(n.context,s.rH.UnsupportedHashType,n.node);if(e===o.v)throw new s.aV(n.context,s.rH.UnsupportedHashType,n.node);throw new s.aV(n.context,s.rH.UnsupportedHashType,n.node)}if(t.updateUint8Array([223]),n.map.has(e)){const r=n.map.get(e);t.updateIntArray([61237541^r])}else{n.map.set(e,n.currentLength++);for(const r of e.keys())t.updateIntArray([r.length]),t.updateWithString(r),t.updateUint8Array([251]),me(e.field(r),t,n),t.updateUint8Array([239]);n.map.delete(e),n.currentLength--}t.updateUint8Array([73])}}else t.updateUint8Array([0,139])}function pe(e,t){e.portal=function(e,n){return t(e,n,((t,i,a)=>((0,o.y)(a,1,1,e,n),new r.Z((0,o.j)(a[0])))))},e.typeof=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,1,1,e,n);const a=(0,o.B)(i[0]);if("Unrecognised Type"===a)throw new s.aV(e,s.rH.UnrecognisedType,n);return a}))},e.trim=function(e,n){return t(e,n,((t,r,i)=>((0,o.y)(i,1,1,e,n),(0,o.j)(i[0]).trim())))},e.tohex=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,1,1,e,n);const a=(0,o.g)(i[0]);return isNaN(a)?a:a.toString(16)}))},e.upper=function(e,n){return t(e,n,((t,r,i)=>((0,o.y)(i,1,1,e,n),(0,o.j)(i[0]).toUpperCase())))},e.proper=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,1,2,e,n);let a=1;2===i.length&&"firstword"===(0,o.j)(i[1]).toLowerCase()&&(a=2);const s=/\s/,u=(0,o.j)(i[0]);let l="",c=!0;for(let e=0;e<u.length;e++){let t=u[e];s.test(t)?1===a&&(c=!0):t.toUpperCase()!==t.toLowerCase()&&(c?(t=t.toUpperCase(),c=!1):t=t.toLowerCase()),l+=t}return l}))},e.lower=function(e,n){return t(e,n,((t,r,i)=>((0,o.y)(i,1,1,e,n),(0,o.j)(i[0]).toLowerCase())))},e.guid=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,0,1,e,n),i.length>0)switch((0,o.j)(i[0]).toLowerCase()){case"digits":return(0,o.M)().replace("-","").replace("-","").replace("-","").replace("-","");case"digits-hyphen":return(0,o.M)();case"digits-hyphen-braces":return"{"+(0,o.M)()+"}";case"digits-hyphen-parentheses":return"("+(0,o.M)()+")"}return"{"+(0,o.M)()+"}"}))},e.standardizeguid=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,2,2,e,n);let a=(0,o.j)(i[0]);if(""===a||null===a)return"";const s=/^(\{|\()?(?<partA>[0-9a-z]{8})(\-?)(?<partB>[0-9a-z]{4})(\-?)(?<partC>[0-9a-z]{4})(\-?)(?<partD>[0-9a-z]{4})(\-?)(?<partE>[0-9a-z]{12})(\}|\))?$/gim.exec(a);if(!s)return"";const u=s.groups;switch(a=u.partA+"-"+u.partB+"-"+u.partC+"-"+u.partD+"-"+u.partE,(0,o.j)(i[1]).toLowerCase()){case"digits":return a.replace("-","").replace("-","").replace("-","").replace("-","");case"digits-hyphen":return a;case"digits-hyphen-braces":return"{"+a+"}";case"digits-hyphen-parentheses":return"("+a+")"}return"{"+a+"}"}))},e.console=function(e,n){return t(e,n,((t,n,r)=>(0===r.length||(1===r.length?e.console((0,o.j)(r[0])):e.console((0,o.j)(r))),o.v)))},e.mid=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,2,3,e,n);let a=(0,o.g)(i[1]);if(isNaN(a))return"";if(a<0&&(a=0),2===i.length)return(0,o.j)(i[0]).substr(a);let s=(0,o.g)(i[2]);return isNaN(s)?"":(s<0&&(s=0),(0,o.j)(i[0]).substr(a,s))}))},e.find=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,2,3,e,n);let a=0;if(i.length>2){if(a=(0,o.g)((0,o.A)(i[2],0)),isNaN(a))return-1;a<0&&(a=0)}return(0,o.j)(i[1]).indexOf((0,o.j)(i[0]),a)}))},e.left=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,2,2,e,n);let a=(0,o.g)(i[1]);return isNaN(a)?"":(a<0&&(a=0),(0,o.j)(i[0]).substr(0,a))}))},e.right=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,2,2,e,n);let a=(0,o.g)(i[1]);return isNaN(a)?"":(a<0&&(a=0),(0,o.j)(i[0]).substr(-1*a,a))}))},e.split=function(e,n){return t(e,n,((t,r,i)=>{let a;(0,o.y)(i,2,4,e,n);let s=(0,o.g)((0,o.A)(i[2],-1));const u=(0,o.h)((0,o.A)(i[3],!1));if(-1===s||null===s||!0===u?a=(0,o.j)(i[0]).split((0,o.j)(i[1])):(isNaN(s)&&(s=-1),s<-1&&(s=-1),a=(0,o.j)(i[0]).split((0,o.j)(i[1]),s)),!1===u)return a;const l=[];for(let e=0;e<a.length&&!(-1!==s&&l.length>=s);e++)""!==a[e]&&void 0!==a[e]&&l.push(a[e]);return l}))},e.text=function(e,n){return t(e,n,((t,r,i)=>((0,o.y)(i,1,2,e,n),(0,o.t)(i[0],i[1]))))},e.concatenate=function(e,n){return t(e,n,((e,t,n)=>{const r=[];if(n.length<1)return"";if((0,o.m)(n[0])){const e=(0,o.A)(n[2],"");for(let t=0;t<n[0].length;t++)r[t]=(0,o.t)(n[0][t],e);return n.length>1?r.join(n[1]):r.join("")}if((0,o.x)(n[0])){const e=(0,o.A)(n[2],"");for(let t=0;t<n[0].length();t++)r[t]=(0,o.t)(n[0].get(t),e);return n.length>1?r.join(n[1]):r.join("")}for(let e=0;e<n.length;e++)r[e]=(0,o.t)(n[e]);return r.join("")}))},e.reverse=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,1,1,e,n),(0,o.m)(i[0])){const e=i[0].slice(0);return e.reverse(),e}if((0,o.x)(i[0])){const e=i[0].toArray().slice(0);return e.reverse(),e}throw new s.aV(e,s.rH.InvalidParameter,n)}))},e.replace=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,3,4,e,n);const a=(0,o.j)(i[0]),s=(0,o.j)(i[1]),u=(0,o.j)(i[2]);return 4!==i.length||(0,o.h)(i[3])?(0,o.L)(a,s,u):a.replace(s,u)}))},e.schema=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.w)(i[0])){const t=(0,o.N)(i[0]);return t?a.Z.convertObjectToArcadeDictionary(t,(0,o.C)(e)):null}throw new s.aV(e,s.rH.InvalidParameter,n)}))},e.subtypes=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,1,1,e,n),(0,o.w)(i[0])){const t=(0,o.O)(i[0]);return t?a.Z.convertObjectToArcadeDictionary(t,(0,o.C)(e)):null}throw new s.aV(e,s.rH.InvalidParameter,n)}))},e.subtypecode=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,1,1,e,n),(0,o.w)(i[0])){const e=(0,o.O)(i[0]);if(!e)return null;if(e.subtypeField&&i[0].hasField(e.subtypeField)){const t=i[0].field(e.subtypeField);for(const n of e.subtypes)if(n.code===t)return n.code;return null}return null}throw new s.aV(e,s.rH.InvalidParameter,n)}))},e.subtypename=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,1,1,e,n),(0,o.w)(i[0])){const e=(0,o.O)(i[0]);if(!e)return"";if(e.subtypeField&&i[0].hasField(e.subtypeField)){const t=i[0].field(e.subtypeField);for(const n of e.subtypes)if(n.code===t)return n.name;return""}return""}throw new s.aV(e,s.rH.InvalidParameter,n)}))},e.gdbversion=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,1,1,e,n),(0,o.w)(i[0]))return i[0].gdbVersion();throw new s.aV(e,s.rH.InvalidParameter,n)}))},e.domain=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,2,3,e,n),(0,o.w)(i[0])){const t=(0,o.P)(i[0],(0,o.j)(i[1]),void 0===i[2]?void 0:(0,o.g)(i[2]));return t&&t.domain?"coded-value"===t.domain.type||"codedValue"===t.domain.type?a.Z.convertObjectToArcadeDictionary({type:"codedValue",name:t.domain.name,dataType:u.yE[t.field.type],codedValues:t.domain.codedValues.map((e=>({name:e.name,code:e.code})))},(0,o.C)(e)):a.Z.convertObjectToArcadeDictionary({type:"range",name:t.domain.name,dataType:u.yE[t.field.type],min:t.domain.min,max:t.domain.max},(0,o.C)(e)):null}throw new s.aV(e,s.rH.InvalidParameter,n)}))},e.domainname=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,2,4,e,n),(0,o.w)(i[0]))return(0,o.Q)(i[0],(0,o.j)(i[1]),i[2],void 0===i[3]?void 0:(0,o.g)(i[3]));throw new s.aV(e,s.rH.InvalidParameter,n)}))},e.domaincode=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,2,4,e,n),(0,o.w)(i[0]))return(0,o.S)(i[0],(0,o.j)(i[1]),i[2],void 0===i[3]?void 0:(0,o.g)(i[3]));throw new s.aV(e,s.rH.InvalidParameter,n)}))},e.urlencode=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,1,1,e,n),null===i[0])return"";if(i[0]instanceof a.Z){let e="";for(const t of i[0].keys()){const n=i[0].field(t);""!==e&&(e+="&"),e+=null===n?encodeURIComponent(t)+"=":encodeURIComponent(t)+"="+encodeURIComponent(n)}return e}return encodeURIComponent((0,o.j)(i[0]))}))},e.hash=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,1,1,e,n);const a=new ae(0);return me(i[0],a,{context:e,node:n,map:new Map,currentLength:0}),a.digest()}))},e.convertdirection=function(e,n){return t(e,n,((t,r,i)=>((0,o.y)(i,3,3,e,n),Q(i[0],i[1],i[2]))))},e.fromjson=function(e,n){return t(e,n,((t,r,i)=>{if((0,o.y)(i,1,1,e,n),!1===(0,o.c)(i[0]))throw new s.aV(e,s.rH.InvalidParameter,n);return a.Z.convertJsonToArcade(JSON.parse((0,o.j)(i[0])),(0,o.C)(e))}))},e.expects=function(e,n){return t(e,n,((t,r,i)=>{if(i.length<1)throw new s.aV(e,s.rH.WrongNumberOfParameters,n);return o.v}))},e.tocharcode=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,1,2,e,n);const a=(0,o.g)((0,o.A)(i[1],0)),u=(0,o.j)(i[0]);if(0===u.length&&1===i.length)return null;if(u.length<=a||a<0)throw new s.aV(e,s.rH.OutOfBounds,n);return u.charCodeAt(a)}))},e.tocodepoint=function(e,n){return t(e,n,((t,r,i)=>{(0,o.y)(i,1,2,e,n);const a=(0,o.g)((0,o.A)(i[1],0)),u=(0,o.j)(i[0]);if(0===u.length&&1===i.length)return null;if(u.length<=a||a<0)throw new s.aV(e,s.rH.OutOfBounds,n);return u.codePointAt(a)}))},e.fromcharcode=function(e,n){return t(e,n,((t,r,i)=>{if(i.length<1)throw new s.aV(e,s.rH.WrongNumberOfParameters,n);const a=i.map((e=>Math.trunc((0,o.g)(e)))).filter((e=>e>=0&&e<=65535));return 0===a.length?null:String.fromCharCode.apply(null,a)}))},e.fromcodepoint=function(e,n){return t(e,n,((t,r,i)=>{if(i.length<1)throw new s.aV(e,s.rH.WrongNumberOfParameters,n);let a;try{a=i.map((e=>Math.trunc((0,o.g)(e)))).filter((e=>e<=1114111&&e>>>0===e))}catch(e){return null}return 0===a.length?null:String.fromCodePoint.apply(null,a)}))},e.getuser=function(e,n){return t(e,n,((t,i,u)=>{(0,o.y)(u,0,2,e,n);let l=(0,o.A)(u[1],"");if(l=!0===l||!1===l?"":(0,o.j)(l),null!==l&&""!==l)return null;if(0===u.length||u[0]instanceof r.Z){let t=null;if(e.services&&e.services.portal&&(t=e.services.portal),u.length>0&&!function(e,t){return!!e&&(0,he.tm)(e,t?.restUrl||"")}(u[0].field("url"),t))return null;if(!t)return null;if(""===l){const n=function(e){return"loaded"===e.loadStatus&&e.user&&e.user.sourceJSON?e.user.sourceJSON:null}(t);if(n){const t=JSON.parse(JSON.stringify(n));for(const e of["lastLogin","created","modified"])void 0!==t[e]&&null!==t[e]&&(t[e]=new Date(t[e]));return a.Z.convertObjectToArcadeDictionary(t,(0,o.C)(e))}}return null}throw new s.aV(e,s.rH.InvalidParameter,n)}))}}},77286:(e,t,n)=>{n.d(t,{EI:()=>i,Lz:()=>s,SV:()=>a,r1:()=>o});var r=n(6570);function i(e){if(null==e)return null;if("number"==typeof e)return e;let t=e.toLowerCase();switch(t=t.replace(/\s/g,""),t=t.replace(/-/g,""),t){case"meters":case"meter":case"m":case"squaremeters":case"squaremeter":return 109404;case"miles":case"mile":case"squaremile":case"squaremiles":return 109439;case"kilometers":case"kilometer":case"squarekilometers":case"squarekilometer":case"km":return 109414;case"acres":case"acre":case"ac":return 109402;case"hectares":case"hectare":case"ha":return 109401;case"yard":case"yd":case"yards":case"squareyards":case"squareyard":return 109442;case"feet":case"ft":case"foot":case"squarefeet":case"squarefoot":return 109405;case"nmi":case"nauticalmile":case"nauticalmiles":case"squarenauticalmile":case"squarenauticalmiles":return 109409}return null}function a(e){if(null==e)return null;switch(e.type){case"polygon":case"multipoint":case"polyline":return e.extent;case"point":return new r.Z({xmin:e.x,ymin:e.y,xmax:e.x,ymax:e.y,spatialReference:e.spatialReference});case"extent":return e}return null}function s(e){if(null==e)return null;if("number"==typeof e)return e;let t=e.toLowerCase();switch(t=t.replace(/\s/g,""),t=t.replace(/-/g,""),t){case"meters":case"meter":case"m":case"squaremeters":case"squaremeter":return 9001;case"miles":case"mile":case"squaremile":case"squaremiles":return 9093;case"kilometers":case"kilometer":case"squarekilometers":case"squarekilometer":case"km":return 9036;case"yard":case"yd":case"yards":case"squareyards":case"squareyard":return 9096;case"feet":case"ft":case"foot":case"squarefeet":case"squarefoot":return 9002;case"nmi":case"nauticalmile":case"nauticalmiles":case"squarenauticalmile":case"squarenauticalmiles":return 9030}return null}function o(e){if(null==e)return null;const t=e.clone();return void 0!==e.cache._geVersion&&(t.cache._geVersion=e.cache._geVersion),t}},90077:(e,t,n)=>{n.d(t,{Kq:()=>g,Vf:()=>D,bV:()=>h,dN:()=>y,gW:()=>l,mb:()=>p,w8:()=>m,x5:()=>f});const r={all:{min:2,max:2},none:{min:2,max:2},any:{min:2,max:2},reduce:{min:2,max:3},map:{min:2,max:2},filter:{min:2,max:2},fromcodepoint:{min:1,max:-1},fromcharcode:{min:1,max:-1},tocodepoint:{min:1,max:2},tocharcode:{min:1,max:2},concatenate:{min:0,max:-1},expects:{min:1,max:-1},getfeatureset:{min:1,max:2},week:{min:1,max:2},fromjson:{min:1,max:1},length3d:{min:1,max:2},tohex:{min:1,max:1},hash:{min:1,max:1},timezone:{min:1,max:1},timezoneoffset:{min:1,max:1},changetimezone:{min:2,max:2},isoweek:{min:1,max:1},isoweekday:{min:1,max:1},hasvalue:{min:2,max:2},isomonth:{min:1,max:1},isoyear:{min:1,max:1},resize:{min:2,max:3},slice:{min:0,max:-1},splice:{min:0,max:-1},push:{min:2,max:2},pop:{min:1,max:1},includes:{min:2,max:2},array:{min:1,max:2},front:{min:1,max:1},back:{min:1,max:1},insert:{min:3,max:3},erase:{min:2,max:2},split:{min:2,max:4},guid:{min:0,max:1},standardizeguid:{min:2,max:2},today:{min:0,max:0},angle:{min:2,max:3},bearing:{min:2,max:3},urlencode:{min:1,max:1},now:{min:0,max:0},timestamp:{min:0,max:0},day:{min:1,max:1},month:{min:1,max:1},year:{min:1,max:1},hour:{min:1,max:1},second:{min:1,max:1},millisecond:{min:1,max:1},minute:{min:1,max:1},weekday:{min:1,max:1},toutc:{min:1,max:1},tolocal:{min:1,max:1},date:{min:0,max:8},datediff:{min:2,max:4},dateadd:{min:2,max:3},trim:{min:1,max:1},text:{min:1,max:2},left:{min:2,max:2},right:{min:2,max:2},mid:{min:2,max:3},upper:{min:1,max:1},proper:{min:1,max:2},lower:{min:1,max:1},find:{min:2,max:3},iif:{min:3,max:3},decode:{min:2,max:-1},when:{min:2,max:-1},defaultvalue:{min:2,max:2},isempty:{min:1,max:1},domaincode:{min:2,max:4},domainname:{min:2,max:4},polygon:{min:1,max:1},point:{min:1,max:1},polyline:{min:1,max:1},extent:{min:1,max:1},multipoint:{min:1,max:1},ringisclockwise:{min:1,max:1},geometry:{min:1,max:1},count:{min:0,max:-1},number:{min:1,max:2},acos:{min:1,max:1},asin:{min:1,max:1},atan:{min:1,max:1},atan2:{min:2,max:2},ceil:{min:1,max:2},floor:{min:1,max:2},round:{min:1,max:2},cos:{min:1,max:1},exp:{min:1,max:1},log:{min:1,max:1},min:{min:0,max:-1},constrain:{min:3,max:3},console:{min:0,max:-1},max:{min:0,max:-1},pow:{min:2,max:2},random:{min:0,max:0},sqrt:{min:1,max:1},sin:{min:1,max:1},tan:{min:1,max:1},abs:{min:1,max:1},isnan:{min:1,max:1},stdev:{min:0,max:-1},average:{min:0,max:-1},mean:{min:0,max:-1},sum:{min:0,max:-1},variance:{min:0,max:-1},distinct:{min:0,max:-1},first:{min:1,max:1},top:{min:2,max:2},boolean:{min:1,max:1},dictionary:{min:0,max:-1},typeof:{min:1,max:1},reverse:{min:1,max:1},replace:{min:3,max:4},sort:{min:1,max:2},feature:{min:1,max:-1},haskey:{min:2,max:2},indexof:{min:2,max:2},disjoint:{min:2,max:2},intersects:{min:2,max:2},touches:{min:2,max:2},crosses:{min:2,max:2},within:{min:2,max:2},contains:{min:2,max:2},overlaps:{min:2,max:2},equals:{min:2,max:2},relate:{min:3,max:3},intersection:{min:2,max:2},union:{min:1,max:2},difference:{min:2,max:2},symmetricdifference:{min:2,max:2},clip:{min:2,max:2},cut:{min:2,max:2},area:{min:1,max:2},areageodetic:{min:1,max:2},length:{min:1,max:2},lengthgeodetic:{min:1,max:2},distancegeodetic:{min:2,max:3},distance:{min:2,max:3},densify:{min:2,max:3},densifygeodetic:{min:2,max:3},generalize:{min:2,max:4},buffer:{min:2,max:3},buffergeodetic:{min:2,max:3},offset:{min:2,max:6},rotate:{min:2,max:3},issimple:{min:1,max:1},simplify:{min:1,max:1},convexhull:{min:1,max:1},centroid:{min:1,max:1},isselfintersecting:{min:1,max:1},multiparttosinglepart:{min:1,max:1},setgeometry:{min:2,max:2},portal:{min:1,max:1},getuser:{min:0,max:2},subtypes:{min:1,max:1},subtypecode:{min:1,max:1},subtypename:{min:1,max:1},domain:{min:2,max:3},convertdirection:{min:3,max:3},sqltimestamp:{min:1,max:3},schema:{min:1,max:1}},i={functionDefinitions:new Map,constantDefinitions:new Map},a={functionDefinitions:new Map,constantDefinitions:new Map};for(const e of["pi","infinity"])a.constantDefinitions.set(e,{type:"constant"}),i.constantDefinitions.set(e,{type:"constant"});a.constantDefinitions.set("textformatting",{type:"namespace",key:"textformatting",members:[{key:"backwardslash",type:"constant"},{key:"doublequote",type:"constant"},{key:"forwardslash",type:"constant"},{key:"tab",type:"constant"},{key:"singlequote",type:"constant"},{key:"newline",type:"constant"}]}),i.constantDefinitions.set("textformatting",{type:"namespace",key:"textformatting",members:[{key:"backwardslash",type:"constant"},{key:"tab",type:"constant"},{key:"singlequote",type:"constant"},{key:"doublequote",type:"constant"},{key:"forwardslash",type:"constant"},{key:"newline",type:"constant"}]});for(const e in r){const t=r[e];a.functionDefinitions.set(e,{overloads:[{type:"function",parametersInfo:{min:t.min,max:t.max}}]}),i.functionDefinitions.set(e,{overloads:[{type:"function",parametersInfo:{min:t.min,max:t.max}}]})}const s=["featureset","featuresetbyid","featuresetbyname","featuresetbyassociation","featuresetbyrelationshipname","featuresetbyurl","getfeatureset","getuser","attachments","featuresetbyportalitem"],o=["disjoint","intersects","touches","crosses","within","contains","overlaps","equals","relate","intersection","union","difference","symmetricdifference","clip","cut","area","areageodetic","length","length3d","lengthgeodetic","distance","distancegeodetic","densify","densifygeodetic","generalize","buffer","buffergeodetic","offset","rotate","issimple","convexhull","simplify","multiparttosinglepart"];function u(e){return"string"==typeof e||e instanceof String}function l(e,t){const n="sync"===t?i:a;n.functionDefinitions.has(e.name.toLowerCase())?n.functionDefinitions.get(e.name.toLowerCase())?.overloads.push({type:"function",parametersInfo:{min:e.min,max:e.max}}):n.functionDefinitions.set(e.name.toLowerCase(),{overloads:[{type:"function",parametersInfo:{min:e.min,max:e.max}}]})}function c(e,t){if(e)for(const n of e)d(n,t)}function d(e,t){if(e&&!1!==t(e))switch(e.type){case"ImportDeclaration":c(e.specifiers,t),d(e.source,t);break;case"ExportNamedDeclaration":d(e.declaration,t);break;case"ArrayExpression":c(e.elements,t);break;case"AssignmentExpression":case"BinaryExpression":case"LogicalExpression":d(e.left,t),d(e.right,t);break;case"BlockStatement":case"Program":c(e.body,t);break;case"BreakStatement":case"ContinueStatement":case"EmptyStatement":case"Identifier":case"Literal":break;case"CallExpression":d(e.callee,t),c(e.arguments,t);break;case"ExpressionStatement":d(e.expression,t);break;case"ForInStatement":d(e.left,t),d(e.right,t),d(e.body,t);break;case"ForStatement":d(e.init,t),d(e.test,t),d(e.update,t),d(e.body,t);break;case"WhileStatement":d(e.test,t),d(e.body,t);break;case"FunctionDeclaration":d(e.id,t),c(e.params,t),d(e.body,t);break;case"IfStatement":d(e.test,t),d(e.consequent,t),d(e.alternate,t);break;case"MemberExpression":d(e.object,t),d(e.property,t);break;case"ObjectExpression":c(e.properties,t);break;case"Property":d(e.key,t),d(e.value,t);break;case"ReturnStatement":case"UnaryExpression":case"UpdateExpression":d(e.argument,t);break;case"VariableDeclaration":c(e.declarations,t);break;case"VariableDeclarator":d(e.id,t),d(e.init,t);break;case"TemplateLiteral":c(e.expressions,t),c(e.quasis,t)}}function h(e,t){let n=!1;const r=t.toLowerCase();return d(e,(e=>!n&&("Identifier"===e.type&&e.name&&e.name.toLowerCase()===r&&(n=!0),!0))),n}function f(e){const t=[];return d(e,(e=>("ImportDeclaration"===e.type&&e.source&&e.source.value&&t.push({libname:e.specifiers[0].local.name.toLowerCase(),source:e.source.value}),!0))),t}function m(e,t){let n=!1;const r=t.toLowerCase();return d(e,(e=>!(n||"CallExpression"===e.type&&"Identifier"===e.callee.type&&e.callee.name&&e.callee.name.toLowerCase()===r&&(n=!0,1)))),n}function p(e){const t=[];return d(e,(e=>"MemberExpression"!==e.type||"Identifier"!==e.object.type||(!1===e.computed&&e.object&&e.object.name&&e.property&&"Identifier"===e.property.type&&e.property.name?t.push(e.object.name.toLowerCase()+"."+e.property.name.toLowerCase()):e.object&&e.object.name&&e.property&&"Literal"===e.property.type&&"string"==typeof e.property.value&&t.push(e.object.name.toLowerCase()+"."+e.property.value?.toString().toLowerCase()),!1))),t}function g(e){const t=[];return d(e,(e=>{if("CallExpression"===e.type){if("Identifier"===e.callee.type&&"expects"===e.callee.name.toLowerCase()){let n="";for(let r=0;r<(e.arguments||[]).length;r++)0===r?"Identifier"===e.arguments[r].type&&(n=e.arguments[r].name.toLowerCase()):n&&"Literal"===e.arguments[r].type&&u(e.arguments[r].value)&&t.push(n+"."+e.arguments[r].value.toLowerCase());return!1}if("Identifier"===e.callee.type&&["domainname","domaincode","domain","haskey"].includes(e.callee.name.toLowerCase())&&e.arguments.length>=2){let n="";return"Identifier"===e.arguments[0].type&&(n=e.arguments[0].name.toLowerCase()),n&&"Literal"===e.arguments[1].type&&u(e.arguments[1].value)&&t.push(n+"."+e.arguments[1].value.toLowerCase()),!1}}return"MemberExpression"!==e.type||"Identifier"!==e.object.type||(!1===e.computed&&e.object&&e.object.name&&e.property&&"Identifier"===e.property.type&&e.property.name?t.push(e.object.name.toLowerCase()+"."+e.property.name.toLowerCase()):e.object&&e.object.name&&e.property&&"Literal"===e.property.type&&"string"==typeof e.property.value&&t.push(e.object.name.toLowerCase()+"."+e.property.value?.toString().toLowerCase()),!1)})),t}function D(e){const t=[];return d(e,(e=>("CallExpression"===e.type&&"Identifier"===e.callee.type&&t.push(e.callee.name.toLowerCase()),!0))),t}function y(e,t=[]){let n=null;if(void 0===e.usesFeatureSet){null===n&&(n=D(e)),e.usesFeatureSet=!1;for(let t=0;t<n.length;t++)s.includes(n[t])&&(e.usesFeatureSet=!0,e.isAsync=!0);if(!1===e.usesFeatureSet&&t&&t.length>0)for(const n of t)if(h(e,n)){e.usesFeatureSet=!0,e.isAsync=!0;break}}if(void 0===e.usesModules&&(e.usesModules=!1,f(e).length>0&&(e.usesModules=!0)),void 0===e.usesGeometry){e.usesGeometry=!1,null===n&&(n=D(e));for(let t=0;t<n.length;t++)o.includes(n[t])&&(e.usesGeometry=!0)}}},84211:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(27535),i=n(12384),a=n(61363),s=n(95330);const o=Object.freeze(Object.defineProperty({__proto__:null,registerFunctions:function(e){function t(e,t,n){if(e instanceof i.Z)return e.toArray();if((0,a.m)(e))return e;throw new r.aV(t,r.rH.InvalidParameter,n)}function n(e,t){const r=e.length,i=Math.floor(r/2);return 0===r?[]:1===r?[e[0]]:function(e,t,n){const r=[];for(;e.length>0||t.length>0;)if(e.length>0&&t.length>0){let i=n(e[0],t[0]);isNaN(i)&&(i=0),i<=0?(r.push(e[0]),e=e.slice(1)):(r.push(t[0]),t=t.slice(1))}else e.length>0?(r.push(e[0]),e=e.slice(1)):t.length>0&&(r.push(t[0]),t=t.slice(1));return r}(n(e.slice(0,i),t),n(e.slice(i,r),t),t)}async function o(e,t){const n=e.length,r=Math.floor(n/2);if(0===n)return[];if(1===n)return[e[0]];const i=[await o(e.slice(0,r),t),await o(e.slice(r,n),t)];return u(i[0],i[1],t,[])}async function u(e,t,n,r){const i=r;if(!(e.length>0||t.length>0))return r;if(e.length>0&&t.length>0){let a=await n(e[0],t[0]);return isNaN(a)&&(a=1),a<=0?(i.push(e[0]),e=e.slice(1)):(i.push(t[0]),t=t.slice(1)),u(e,t,n,r)}return e.length>0?(i.push(e[0]),u(e=e.slice(1),t,n,r)):t.length>0?(i.push(t[0]),u(e,t=t.slice(1),n,r)):void 0}function l(e,t,i,s){(0,a.y)(i,1,2,e,t);let u=i[0];if((0,a.x)(u)&&(u=u.toArray()),!1===(0,a.m)(u))throw new r.aV(e,r.rH.InvalidParameter,t);if(i.length>1){if(!1===(0,a.i)(i[1]))throw new r.aV(e,r.rH.InvalidParameter,t);let l=u;const c=i[1].createFunction(e);return s?o(l,c):(l=n(l,((e,t)=>c(e,t))),l)}let l=u;if(0===l.length)return[];const c={};for(let e=0;e<l.length;e++){const t=(0,a.B)(l[e]);""!==t&&(c[t]=!0)}if(!0===c.Array||!0===c.Dictionary||!0===c.Feature||!0===c.Point||!0===c.Polygon||!0===c.Polyline||!0===c.Multipoint||!0===c.Extent||!0===c.Function)return l.slice(0);let d=0,h="";for(const e in c)d++,h=e;return l=d>1||"String"===h?n(l,((e,t)=>{if(null==e||e===a.v)return null==t||t===a.v?0:1;if(null==t||t===a.v)return-1;const n=(0,a.j)(e),r=(0,a.j)(t);return n<r?-1:n===r?0:1})):"Number"===h?n(l,((e,t)=>e-t)):"Boolean"===h?n(l,((e,t)=>e===t?0:t?-1:1)):"Date"===h?n(l,((e,t)=>t-e)):l.slice(0),l}e.functions.array=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{(0,a.y)(s,1,2,t,n);const o=(0,a.g)(s[0]);if(isNaN(o)||!1===(0,a.z)(o))throw new r.aV(t,r.rH.InvalidParameter,n);const u=(0,a.A)(s[1],null),l=new Array(o);return l.fill(u),l}))},e.functions.front=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{if((0,a.y)(s,1,1,t,n),(0,a.x)(s[0])){if(s[0].length()<=0)throw new r.aV(t,r.rH.OutOfBounds,n);return s[0].get(0)}if((0,a.m)(s[0])){if(s[0].length<=0)throw new r.aV(t,r.rH.OutOfBounds,n);return s[0][0]}throw new r.aV(t,r.rH.InvalidParameter,n)}))},e.functions.back=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{if((0,a.y)(s,1,1,t,n),(0,a.x)(s[0])){if(s[0].length()<=0)throw new r.aV(t,r.rH.OutOfBounds,n);return s[0].get(s[0].length()-1)}if((0,a.m)(s[0])){if(s[0].length<=0)throw new r.aV(t,r.rH.OutOfBounds,n);return s[0][s[0].length-1]}throw new r.aV(t,r.rH.InvalidParameter,n)}))},e.functions.push=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{if((0,a.y)(s,1,2,t,n),(0,a.m)(s[0]))return s[0][s[0].length]=s[1],s[0].length;throw new r.aV(t,r.rH.InvalidParameter,n)}))},e.functions.pop=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{if((0,a.y)(s,1,1,t,n),(0,a.m)(s[0])){if(s[0].length<=0)throw new r.aV(t,r.rH.OutOfBounds,n);const e=s[0][s[0].length-1];return s[0].length=s[0].length-1,e}throw new r.aV(t,r.rH.InvalidParameter,n)}))},e.functions.erase=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{if((0,a.y)(s,2,2,t,n),(0,a.m)(s[0])){let e=(0,a.g)(s[1]);if(isNaN(e)||!1===(0,a.z)(e))throw new r.aV(t,r.rH.InvalidParameter,n);const i=s[0];if(i.length<=0)throw new r.aV(t,r.rH.OutOfBounds,n);if(e<0&&(e=i.length+e),e<0)throw new r.aV(t,r.rH.OutOfBounds,n);if(e>=i.length)throw new r.aV(t,r.rH.OutOfBounds,n);return i.splice(e,1),a.v}throw new r.aV(t,r.rH.InvalidParameter,n)}))},e.functions.insert=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{if((0,a.y)(s,3,3,t,n),(0,a.m)(s[0])){const e=(0,a.g)(s[1]);if(isNaN(e)||!1===(0,a.z)(e))throw new r.aV(t,r.rH.InvalidParameter,n);const i=s[2],o=s[0];if(e>o.length)throw new r.aV(t,r.rH.OutOfBounds,n);if(e<0&&e<-1*o.length)throw new r.aV(t,r.rH.OutOfBounds,n);return e===o.length?(o[e]=i,a.v):(o.splice(e,0,i),a.v)}throw new r.aV(t,r.rH.InvalidParameter,n)}))},e.functions.resize=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{if((0,a.y)(s,2,3,t,n),(0,a.m)(s[0])){const e=(0,a.g)(s[1]);if(isNaN(e)||!1===(0,a.z)(e))throw new r.aV(t,r.rH.InvalidParameter,n);if(e<0)throw new r.aV(t,r.rH.InvalidParameter,n);const i=(0,a.A)(s[2],null),o=s[0];if(o.length>=e)return o.length=e,a.v;const u=o.length;o.length=e;for(let e=u;e<o.length;e++)o[e]=i;return a.v}throw new r.aV(t,r.rH.InvalidParameter,n)}))},e.functions.includes=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{if((0,a.y)(s,2,2,t,n),(0,a.m)(s[0])){const e=s[1];return s[0].findIndex((t=>(0,a.s)(t,e)))>-1}if((0,a.x)(s[0])){const e=s[1];return s[0].toArray().findIndex((t=>(0,a.s)(t,e)))>-1}throw new r.aV(t,r.rH.InvalidParameter,n)}))},e.functions.slice=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{if((0,a.y)(s,1,3,t,n),(0,a.m)(s[0])){const e=(0,a.g)((0,a.A)(s[1],0)),i=(0,a.g)((0,a.A)(s[2],s[0].length));if(isNaN(e)||!1===(0,a.z)(e))throw new r.aV(t,r.rH.InvalidParameter,n);if(isNaN(i)||!1===(0,a.z)(i))throw new r.aV(t,r.rH.InvalidParameter,n);return s[0].slice(e,i)}if((0,a.x)(s[0])){const e=s[0],i=(0,a.g)((0,a.A)(s[1],0)),o=(0,a.g)((0,a.A)(s[2],e.length()));if(isNaN(i)||!1===(0,a.z)(i))throw new r.aV(t,r.rH.InvalidParameter,n);if(isNaN(o)||!1===(0,a.z)(o))throw new r.aV(t,r.rH.InvalidParameter,n);return e.toArray().slice(i,o)}throw new r.aV(t,r.rH.InvalidParameter,n)}))},e.functions.splice=function(t,n){return e.standardFunction(t,n,((e,t,n)=>{const r=[];for(let e=0;e<n.length;e++)(0,a.m)(n[e])?r.push(...n[e]):(0,a.x)(n[e])?r.push(...n[e].toArray()):r.push(n[e]);return r}))},e.functions.top=function(t,n){return e.standardFunction(t,n,((e,i,s)=>{if((0,a.y)(s,2,2,t,n),(0,a.m)(s[0]))return(0,a.g)(s[1])>=s[0].length?s[0].slice(0):s[0].slice(0,(0,a.g)(s[1]));if((0,a.x)(s[0]))return(0,a.g)(s[1])>=s[0].length()?s[0].slice(0):s[0].slice(0,(0,a.g)(s[1]));throw new r.aV(t,r.rH.InvalidParameter,n)}))},e.functions.first=function(t,n){return e.standardFunction(t,n,((e,r,i)=>((0,a.y)(i,1,1,t,n),(0,a.m)(i[0])?0===i[0].length?null:i[0][0]:(0,a.x)(i[0])?0===i[0].length()?null:i[0].get(0):null)))},"sync"===e.mode&&(e.functions.sort=function(t,n){return e.standardFunction(t,n,((e,r,i)=>l(t,n,i,!1)))},e.functions.any=function(n,r){return e.standardFunction(n,r,((e,i,s)=>{(0,a.y)(s,2,2,n,r);const o=s[1].createFunction(n),u=t(s[0],n,r);for(const e of u){const t=o(e);if((0,a.a)(t)&&!0===t)return!0}return!1}))},e.functions.all=function(n,r){return e.standardFunction(n,r,((e,i,s)=>{(0,a.y)(s,2,2,n,r);const o=s[1].createFunction(n),u=t(s[0],n,r);for(const e of u)if(!0!==o(e))return!1;return!0}))},e.functions.none=function(n,r){return e.standardFunction(n,r,((e,i,s)=>{(0,a.y)(s,2,2,n,r);const o=s[1].createFunction(n),u=t(s[0],n,r);for(const e of u)if(!0===o(e))return!1;return!0}))},e.functions.reduce=function(n,r){return e.standardFunction(n,r,((e,i,s)=>{(0,a.y)(s,2,3,n,r);const o=s[1].createFunction(n),u=t(s[0],n,r);return 2===s.length?0===u.length?null:u.reduce(((e,t)=>{const n=o(e,t);return void 0!==n&&n!==a.v?n:null})):u.reduce(((e,t)=>{const n=o(e,t);return void 0!==n&&n!==a.v?n:null}),s[2])}))},e.functions.map=function(n,r){return e.standardFunction(n,r,((e,i,s)=>{(0,a.y)(s,2,2,n,r);const o=s[1].createFunction(n),u=t(s[0],n,r),l=[];for(const e of u){const t=o(e);void 0!==t&&t!==a.v?l.push(t):l.push(null)}return l}))},e.functions.filter=function(n,r){return e.standardFunction(n,r,((e,i,s)=>{(0,a.y)(s,2,2,n,r);const o=s[1].createFunction(n),u=t(s[0],n,r),l=[];for(const e of u)!0===o(e)&&l.push(e);return l}))}),"async"===e.mode&&(e.functions.sort=function(t,n){return e.standardFunctionAsync(t,n,((e,r,i)=>l(t,n,i,!0)))},e.functions.any=function(n,r){return e.standardFunctionAsync(n,r,(async(e,i,o)=>{(0,a.y)(o,2,2,n,r);const u=o[1].createFunction(n),l=t(o[0],n,r);for(const e of l){const t=await u(e);let n=null;if(n=(0,s.y8)(n)?await t:t,(0,a.a)(n)&&!0===n)return!0}return!1}))},e.functions.all=function(n,r){return e.standardFunctionAsync(n,r,(async(e,i,o)=>{(0,a.y)(o,2,2,n,r);const u=o[1].createFunction(n),l=t(o[0],n,r);for(const e of l){const t=await u(e);let n=null;if(n=(0,s.y8)(n)?await t:t,!0!==n)return!1}return!0}))},e.functions.none=function(n,r){return e.standardFunctionAsync(n,r,(async(e,i,o)=>{(0,a.y)(o,2,2,n,r);const u=o[1].createFunction(n),l=t(o[0],n,r);for(const e of l){const t=await u(e);let n=null;if(n=(0,s.y8)(n)?await t:t,!0===n)return!1}return!0}))},e.functions.filter=function(n,r){return e.standardFunctionAsync(n,r,(async(e,i,o)=>{(0,a.y)(o,2,2,n,r);const u=o[1].createFunction(n),l=t(o[0],n,r),c=[];for(const e of l){const t=await u(e);let n=null;n=(0,s.y8)(n)?await t:t,!0===n&&c.push(e)}return c}))},e.functions.reduce=function(n,r){return e.standardFunctionAsync(n,r,((e,i,s)=>{(0,a.y)(s,2,3,n,r);const o=s[1].createFunction(n),u=t(s[0],n,r);let l=null;if(s.length>2){const e=(0,a.A)(s[2],null);l=u.reduce((async(e,t)=>{let n=await e;return void 0!==n&&n!==a.v||(n=null),o(n,t)}),Promise.resolve(e))}else{if(0===u.length)return null;l=u.reduce((async(e,t,n)=>{if(n<=1)return o(e,t);let r=await e;return void 0!==r&&r!==a.v||(r=null),o(r,t)}))}return l.then((e=>void 0!==e&&e!==a.v?e:null))}))},e.functions.map=function(n,r){return e.standardFunctionAsync(n,r,(async(e,i,o)=>{(0,a.y)(o,2,2,n,r);const u=o[1].createFunction(n),l=t(o[0],n,r),c=[];for(const e of l){const t=await u(e);let n=null;n=(0,s.y8)(n)?await t:t,void 0!==n&&n!==a.v?c.push(n):c.push(null)}return c}))})}},Symbol.toStringTag,{value:"Module"}))},61363:(e,t,n)=>{n.d(t,{A:()=>M,B:()=>N,C:()=>_e,D:()=>Q,E:()=>W,F:()=>ce,G:()=>ge,H:()=>we,I:()=>b,J:()=>ye,K:()=>xe,L:()=>I,M:()=>K,N:()=>Me,O:()=>Ie,P:()=>ke,Q:()=>Te,R:()=>E,S:()=>Be,T:()=>Z,U:()=>U,V:()=>re,W:()=>Ae,X:()=>Fe,Y:()=>fe,Z:()=>de,_:()=>he,a:()=>H,b:()=>R,c:()=>L,d:()=>Ee,e:()=>Se,f:()=>_,g:()=>se,h:()=>ue,i:()=>T,j:()=>ne,k:()=>z,l:()=>oe,m:()=>V,n:()=>Ne,o:()=>S,p:()=>k,q:()=>le,r:()=>ee,s:()=>te,t:()=>ie,u:()=>be,v:()=>v,w:()=>P,x:()=>G,y:()=>J,z:()=>O});var r=n(9609),i=n(12384),a=n(50728),s=n(25785),o=n(48853),u=n(6570),l=n(9361),c=n(65091),d=n(94139),h=n(38913),f=n(58901),m=n(17126),p=n(70586),g=n(14808),D=n(86662),y=n(70171),w=n(90658),x=n(17057),F=n(27535);class C{constructor(e){this.value=e}}class A{constructor(e){this.value=e}}const b=A,E=C,v={type:"VOID"},S={type:"BREAK"},k={type:"CONTINUE"};function I(e,t,n){return""===t||null==t||t===n||t===n?e:e=e.split(t).join(n)}function T(e){return e instanceof r.Rm}function B(e){return e instanceof x.P}function _(e){return!!(L(e)||R(e)||z(e)||H(e)||null===e||e===v||"number"==typeof e)}function M(e,t){return void 0===e?t:e}function N(e){return null==e?"":V(e)||G(e)?"Array":z(e)?"Date":L(e)?"String":H(e)?"Boolean":R(e)?"Number":"esri.arcade.Attachment"===e?.declaredClass?"Attachment":"esri.arcade.Portal"===e?.declaredClass?"Portal":"esri.arcade.Dictionary"===e?.declaredClass?"Dictionary":e instanceof x.P?"Module":P(e)?"Feature":e instanceof d.Z?"Point":e instanceof h.Z?"Polygon":e instanceof f.Z?"Polyline":e instanceof c.Z?"Multipoint":e instanceof u.Z?"Extent":T(e)?"Function":Z(e)?"FeatureSet":U(e)?"FeatureSetCollection":e===v?"":"number"==typeof e&&isNaN(e)?"Number":"Unrecognised Type"}function L(e){return"string"==typeof e||e instanceof String}function H(e){return"boolean"==typeof e}function R(e){return"number"==typeof e}function O(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e}function V(e){return e instanceof Array}function P(e){return"esri.arcade.Feature"===e?.arcadeDeclaredClass}function Z(e){return"esri.arcade.featureset.support.FeatureSet"===e?.declaredRootClass}function U(e){return"esri.arcade.featureSetCollection"===e?.declaredRootClass}function G(e){return e instanceof i.Z}function z(e){return e instanceof o.iG}function q(e){return null!=e&&"object"==typeof e}function j(e){return e instanceof Date}function J(e,t,n,r,i){if(e.length<t||e.length>n)throw new F.aV(r,F.rH.WrongNumberOfParameters,i)}function W(e){return e<0?-Math.round(-e):Math.round(e)}function K(){let e=Date.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const n=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"===t?n:3&n|8).toString(16)}))}function $(e,t){return isNaN(e)||null==t||""===t?e.toString():(t=I(t,"‰",""),t=I(t,"¤",""),(0,g.WU)(e,{pattern:t}))}function Y(e,t){return null==t||""===t?e.toISOString(!0):e.toFormat(Q(t),{locale:(0,y.Kd)(),numberingSystem:"latn"})}function Q(e){e=e.replace(/LTS|LT|LL?L?L?|l{1,4}/g,"[$&]");let t="";const n=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g;for(const r of e.match(n)||[])switch(r){case"D":t+="d";break;case"DD":t+="dd";break;case"DDD":t+="o";break;case"d":t+="c";break;case"ddd":t+="ccc";break;case"dddd":t+="cccc";break;case"M":t+="L";break;case"MM":t+="LL";break;case"MMM":t+="LLL";break;case"MMMM":t+="LLLL";break;case"YY":t+="yy";break;case"Y":case"YYYY":t+="yyyy";break;case"Q":t+="q";break;case"Z":t+="ZZ";break;case"ZZ":t+="ZZZ";break;case"S":t+="'S'";break;case"SS":t+="'SS'";break;case"SSS":t+="u";break;case"A":case"a":t+="a";break;case"m":case"mm":case"h":case"hh":case"H":case"HH":case"s":case"ss":case"X":case"x":t+=r;break;default:r.length>=2&&"["===r.slice(0,1)&&"]"===r.slice(-1)?t+=`'${r.slice(1,-1)}'`:t+=`'${r}'`}return t}function X(e,t,n){switch(n){case">":return e>t;case"<":return e<t;case">=":return e>=t;case"<=":return e<=t}return!1}function ee(e,t,n){if(null===e){if(null===t||t===v)return X(null,null,n);if(R(t))return X(0,t,n);if(L(t))return X(0,se(t),n);if(H(t))return X(0,se(t),n);if(z(t))return X(0,t.toNumber(),n)}if(e===v){if(null===t||t===v)return X(null,null,n);if(R(t))return X(0,t,n);if(L(t))return X(0,se(t),n);if(H(t))return X(0,se(t),n);if(z(t))return X(0,t.toNumber(),n)}else if(R(e)){if(R(t))return X(e,t,n);if(H(t))return X(e,se(t),n);if(null===t||t===v)return X(e,0,n);if(L(t))return X(e,se(t),n);if(z(t))return X(e,t.toNumber(),n)}else if(L(e)){if(L(t))return X(ne(e),ne(t),n);if(z(t))return X(se(e),t.toNumber(),n);if(R(t))return X(se(e),t,n);if(null===t||t===v)return X(se(e),0,n);if(H(t))return X(se(e),se(t),n)}else if(z(e)){if(z(t))return X(e.toNumber(),t.toNumber(),n);if(null===t||t===v)return X(e.toNumber(),0,n);if(R(t))return X(e.toNumber(),t,n);if(H(t))return X(e.toNumber(),se(t),n);if(L(t))return X(e.toNumber(),se(t),n)}else if(H(e)){if(H(t))return X(e,t,n);if(R(t))return X(se(e),se(t),n);if(z(t))return X(se(e),t.toNumber(),n);if(null===t||t===v)return X(se(e),0,n);if(L(t))return X(se(e),se(t),n)}return!!te(e,t)&&("<="===n||">="===n)}function te(e,t){if(e===t)return!0;if(null===e&&t===v||null===t&&e===v)return!0;if(z(e)&&z(t))return e.equals(t);if(e instanceof a.Z)return e.equalityTest(t);if(e instanceof s.Z)return e.equalityTest(t);if(e instanceof d.Z&&t instanceof d.Z){const n=e.cache._arcadeCacheId,r=t.cache._arcadeCacheId;if(null!=n)return n===r}if(q(e)&&q(t)){if(e._arcadeCacheId===t._arcadeCacheId&&void 0!==e._arcadeCacheId&&null!==e._arcadeCacheId)return!0;if(e._underlyingGraphic===t._underlyingGraphic&&void 0!==e._underlyingGraphic&&null!==e._underlyingGraphic)return!0}return!1}function ne(e,t){if(L(e))return e;if(null===e)return"";if(R(e))return $(e,t);if(H(e))return e.toString();if(z(e))return Y(e,t);if(e instanceof l.Z)return JSON.stringify(e.toJSON());if(V(e)){const t=[];for(let n=0;n<e.length;n++)t[n]=ae(e[n]);return"["+t.join(",")+"]"}if(e instanceof i.Z){const t=[];for(let n=0;n<e.length();n++)t[n]=ae(e.get(n));return"["+t.join(",")+"]"}return null!==e&&"object"==typeof e&&void 0!==e.castToText?e.castToText():T(e)?"object, Function":e===v?"":B(e)?"object, Module":""}function re(e){const t=[];if(!V(e))return null;if(e instanceof i.Z){for(let n=0;n<e.length();n++)t[n]=se(e.get(n));return t}for(let n=0;n<e.length;n++)t[n]=se(e[n]);return t}function ie(e,t,n=!1){if(L(e))return e;if(null===e)return"";if(R(e))return $(e,t);if(H(e))return e.toString();if(z(e))return Y(e,t);if(e instanceof l.Z)return e instanceof u.Z?'{"xmin":'+e.xmin.toString()+',"ymin":'+e.ymin.toString()+","+(e.hasZ?'"zmin":'+e.zmin.toString()+",":"")+(e.hasM?'"mmin":'+e.mmin.toString()+",":"")+'"xmax":'+e.xmax.toString()+',"ymax":'+e.ymax.toString()+","+(e.hasZ?'"zmax":'+e.zmax.toString()+",":"")+(e.hasM?'"mmax":'+e.mmax.toString()+",":"")+'"spatialReference":'+pe(e.spatialReference)+"}":pe(e.toJSON(),((e,t)=>e.key===t.key?0:"spatialReference"===e.key?1:"spatialReference"===t.key||e.key<t.key?-1:e.key>t.key?1:0));if(V(e)){const t=[];for(let r=0;r<e.length;r++)t[r]=ae(e[r],n);return"["+t.join(",")+"]"}if(e instanceof i.Z){const t=[];for(let r=0;r<e.length();r++)t[r]=ae(e.get(r),n);return"["+t.join(",")+"]"}return null!==e&&"object"==typeof e&&void 0!==e.castToText?e.castToText(n):T(e)?"object, Function":e===v?"":B(e)?"object, Module":""}function ae(e,t=!1){if(null===e)return"null";if(H(e)||R(e)||L(e))return JSON.stringify(e);if(e instanceof l.Z)return ie(e,null,t);if(e instanceof i.Z)return ie(e,null,t);if(e instanceof Array)return ie(e,null,t);if(z(e))return t?JSON.stringify(e.getTime()):JSON.stringify(Y(e,""));if(null!==e&&"object"==typeof e){if(void 0!==e.castToText)return e.castToText(t)}else if(e===v)return"null";return"null"}function se(e,t){return R(e)?e:null===e||""===e?0:z(e)?NaN:H(e)?e?1:0:V(e)||""===e||void 0===e?NaN:void 0!==t&&L(e)?(t=I(t,"‰",""),t=I(t,"¤",""),(0,g.Qc)(e,{pattern:t})):e===v?0:Number(e)}function oe(e,t){if(z(e))return e;if(L(e)){const n=function(e,t){const n=/ (\d\d)/,r=(0,o.Qn)(t);let i=m.ou.fromISO(e,{zone:r});return i.isValid||n.test(e)&&(e=e.replace(n,"T$1"),i=m.ou.fromISO(e,{zone:t}),i.isValid)?i:null}(e,t);if(n)return o.iG.dateTimeToArcadeDate(n)}return null}function ue(e){return H(e)?e:L(e)?"true"===(e=e.toLowerCase()):!!R(e)&&0!==e&&!isNaN(e)}function le(e,t){return(0,p.Wi)(e)?null:(null!==e.spatialReference&&void 0!==e.spatialReference||(e.spatialReference=t),e)}function ce(e){if(null===e)return null;if(e instanceof d.Z)return"NaN"===e.x||null===e.x||isNaN(e.x)?null:e;if(e instanceof h.Z){if(0===e.rings.length)return null;for(const t of e.rings)if(t.length>0)return e;return null}if(e instanceof f.Z){if(0===e.paths.length)return null;for(const t of e.paths)if(t.length>0)return e;return null}return e instanceof c.Z?0===e.points.length?null:e:e instanceof u.Z?"NaN"===e.xmin||null===e.xmin||isNaN(e.xmin)?null:e:null}function de(e,t){if(!e)return t;if(!e.domain)return t;let n=null;if("string"===e.field.type||"esriFieldTypeString"===e.field.type)t=ne(t);else{if(null==t)return null;if(""===t)return t;t=se(t)}for(let r=0;r<e.domain.codedValues.length;r++){const i=e.domain.codedValues[r];i.code===t&&(n=i)}return null===n?t:n.name}function he(e,t){if(!e)return t;if(!e.domain)return t;let n=null;t=ne(t);for(let r=0;r<e.domain.codedValues.length;r++){const i=e.domain.codedValues[r];i.name===t&&(n=i)}return null===n?t:n.code}function fe(e,t,n=null,r=null){if(!t)return null;if(!t.fields)return null;let i,a,s=null;for(let n=0;n<t.fields.length;n++){const r=t.fields[n];r.name.toLowerCase()===e.toString().toLowerCase()&&(s=r)}if(null===s)throw new F.aV(null,F.rH.FieldNotFound,null,{key:e});return null===r&&n&&t.typeIdField&&(r=n.hasField(t.typeIdField)?n.field(t.typeIdField):null),null!=r&&t.types.some((e=>e.id===r&&(i=e.domains&&e.domains[s.name],i&&"inherited"===i.type&&(i=me(s.name,t),a=!0),!0))),a||i||(i=me(e,t)),{field:s,domain:i}}function me(e,t){let n;return t.fields.some((t=>(t.name.toLowerCase()===e.toLowerCase()&&(n=t.domain),!!n))),n}function pe(e,t){t||(t={}),"function"==typeof t&&(t={cmp:t});const n="boolean"==typeof t.cycles&&t.cycles,r=t.cmp&&(i=t.cmp,function(e){return function(t,n){const r={key:t,value:e[t]},a={key:n,value:e[n]};return i(r,a)}});var i;const a=[];return function e(t){if(t&&t.toJSON&&"function"==typeof t.toJSON&&(t=t.toJSON()),void 0===t)return;if("number"==typeof t)return isFinite(t)?""+t:"null";if("object"!=typeof t)return JSON.stringify(t);let i,s;if(Array.isArray(t)){for(s="[",i=0;i<t.length;i++)i&&(s+=","),s+=e(t[i])||"null";return s+"]"}if(null===t)return"null";if(a.includes(t)){if(n)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}const o=a.push(t)-1,u=Object.keys(t).sort(r&&r(t));for(s="",i=0;i<u.length;i++){const n=u[i],r=e(t[n]);r&&(s&&(s+=","),s+=JSON.stringify(n)+":"+r)}return a.splice(o,1),"{"+s+"}"}(e)}function ge(e){if(null===e)return null;const t=[];for(const n of e)n&&n.arcadeDeclaredClass&&"esri.arcade.Feature"===n.arcadeDeclaredClass?t.push(n.geometry()):t.push(n);return t}function De(e,t){if(!(t instanceof d.Z))throw new F.aV(null,F.rH.InvalidParameter,null);e.push(t.hasZ?t.hasM?[t.x,t.y,t.z,t.m]:[t.x,t.y,t.z]:[t.x,t.y])}function ye(e,t){if(V(e)||G(e)){let n=!1,r=!1,i=[],a=t;if(V(e)){for(const t of e)De(i,t);i.length>0&&(a=e[0].spatialReference,n=e[0].hasZ,r=e[0].hasM)}else if(e instanceof s.Z)i=e._elements,i.length>0&&(n=e._hasZ,r=e._hasM,a=e.get(0).spatialReference);else{if(!G(e))throw new F.aV(null,F.rH.InvalidParameter,null);for(const t of e.toArray())De(i,t);i.length>0&&(a=e.get(0).spatialReference,n=!0===e.get(0).hasZ,r=!0===e.get(0).hasM)}return 0===i.length?null:((0,D.bu)(i,r,n)||(i=i.slice(0).reverse()),new h.Z({rings:[i],spatialReference:a,hasZ:n,hasM:r}))}return e}function we(e,t){if(V(e)||G(e)){let n=!1,r=!1,i=[],a=t;if(V(e)){for(const t of e)De(i,t);i.length>0&&(a=e[0].spatialReference,n=!0===e[0].hasZ,r=!0===e[0].hasM)}else if(e instanceof s.Z)i=e._elements,i.length>0&&(n=e._hasZ,r=e._hasM,a=e.get(0).spatialReference);else if(G(e)){for(const t of e.toArray())De(i,t);i.length>0&&(a=e.get(0).spatialReference,n=!0===e.get(0).hasZ,r=!0===e.get(0).hasM)}return 0===i.length?null:new f.Z({paths:[i],spatialReference:a,hasZ:n,hasM:r})}return e}function xe(e,t){if(V(e)||G(e)){let n=!1,r=!1,i=[],a=t;if(V(e)){for(const t of e)De(i,t);i.length>0&&(a=e[0].spatialReference,n=!0===e[0].hasZ,r=!0===e[0].hasM)}else if(e instanceof s.Z)i=e._elements,i.length>0&&(n=e._hasZ,r=e._hasM,a=e.get(0).spatialReference);else if(G(e)){for(const t of e.toArray())De(i,t);i.length>0&&(a=e.get(0).spatialReference,n=!0===e.get(0).hasZ,r=!0===e.get(0).hasM)}return 0===i.length?null:new c.Z({points:i,spatialReference:a,hasZ:n,hasM:r})}return e}function Fe(e,t=!1){const n=[];if(null===e)return n;if(!0===V(e)){for(let r=0;r<e.length;r++){const i=ne(e[r]);""===i&&!0!==t||n.push(i)}return n}if(e instanceof i.Z){for(let r=0;r<e.length();r++){const i=ne(e.get(r));""===i&&!0!==t||n.push(i)}return n}if(_(e)){const r=ne(e);return""===r&&!0!==t||n.push(r),n}return[]}let Ce=0;function Ae(e){return Ce++,Ce%100==0?(Ce=0,new Promise((t=>{setTimeout((()=>{t(e)}),0)}))):e}function be(e,t,n){switch(n){case"&":return e&t;case"|":return e|t;case"^":return e^t;case"<<":return e<<t;case">>":return e>>t;case">>>":return e>>>t}}function Ee(e,t=null){return null==e?null:H(e)||R(e)||L(e)?e:e instanceof l.Z?!0===t?.keepGeometryType?e:e.toJSON():e instanceof i.Z?e.toArray().map((e=>Ee(e,t))):e instanceof Array?e.map((e=>Ee(e,t))):j(e)?e:z(e)?e.toJSDate():null!==e&&"object"==typeof e&&void 0!==e.castAsJson?e.castAsJson(t):null}async function ve(e,t,n,r,i){const a=await Se(e,t,n);i[r]=a}async function Se(e,t=null,n=null){if(e instanceof i.Z&&(e=e.toArray()),null==e)return null;if(_(e)||e instanceof l.Z||j(e)||z(e))return Ee(e,n);if(e instanceof Array){const r=[],i=[];for(const a of e)null===a||_(a)||a instanceof l.Z||j(a)||z(a)?i.push(Ee(a,n)):(i.push(null),r.push(ve(a,t,n,i.length-1,i)));return r.length>0&&await Promise.all(r),i}return null!==e&&"object"==typeof e&&void 0!==e.castAsJsonAsync?e.castAsJsonAsync(t,n):null}function ke(e,t,n){const r=e.fullSchema();return null===r?null:r.fields?fe(t,r,e,n):null}function Ie(e){const t=e.fullSchema();return null===t?null:t.fields&&t.typeIdField?{subtypeField:t.typeIdField,subtypes:t.types?t.types.map((e=>({name:e.name,code:e.id}))):[]}:null}function Te(e,t,n,r){const i=e.fullSchema();if(null===i)return null;if(!i.fields)return null;const a=fe(t,i,e,r);if(void 0===n)try{n=e.field(t)}catch(e){return null}return de(a,n)}function Be(e,t,n,r){const i=e.fullSchema();if(null===i)return null;if(!i.fields)return null;if(void 0===n){try{n=e.field(t)}catch(e){return null}return n}return he(fe(t,i,e,r),n)}function _e(e){return e?.timeReference?.timeZone?e?.timeReference?.timeZone:"system"}function Me(e){const t=e.fullSchema();if(null===t)return null;if(!t.fields)return null;const n=[];for(const e of t.fields)n.push((0,w.Sh)(e));return{objectIdField:t.objectIdField,globalIdField:t.globalIdField,geometryType:void 0===w.q2[t.geometryType]?"":w.q2[t.geometryType],fields:n,datesInUnknownTimezone:!0===t.datesInUnknownTimezone,preferredTimeReference:t.preferredTimeReference||null,editFieldsInfo:t.editFieldsInfo||null,timeInfo:t.timeInfo||null,dateFieldsTimeReference:t.dateFieldsTimeReference||null}}const Ne=Object.freeze(Object.defineProperty({__proto__:null,ImplicitResult:b,ImplicitResultE:A,ReturnResult:E,ReturnResultE:C,absRound:W,autoCastArrayOfPointsToMultiPoint:xe,autoCastArrayOfPointsToPolygon:ye,autoCastArrayOfPointsToPolyline:we,autoCastFeatureToGeometry:ge,binaryOperator:be,breakResult:S,castAsJson:Ee,castAsJsonAsync:Se,continueResult:k,defaultTimeZone:_e,defaultUndefined:M,equalityTest:te,featureDomainCodeLookup:Be,featureDomainValueLookup:Te,featureFullDomain:ke,featureSchema:Me,featureSubtypes:Ie,fixNullGeometry:ce,fixSpatialReference:le,formatDate:Y,formatNumber:$,generateUUID:K,getDomain:fe,getDomainCode:he,getDomainValue:de,getType:N,greaterThanLessThan:ee,isArray:V,isBoolean:H,isDate:z,isFeature:P,isFeatureSet:Z,isFeatureSetCollection:U,isFunctionParameter:T,isImmutableArray:G,isInteger:O,isJsDate:j,isModule:B,isNumber:R,isObject:q,isSimpleType:_,isString:L,multiReplace:I,pcCheck:J,stableStringify:pe,standardiseDateFormat:Q,tick:Ae,toBoolean:ue,toDate:oe,toNumber:se,toNumberArray:re,toString:ne,toStringArray:Fe,toStringExplicit:ie,voidOperation:v},Symbol.toStringTag,{value:"Module"}))},14808:(e,t,n)=>{n.d(t,{Qc:()=>c,WU:()=>o,lt:()=>l});var r=n(19153),i=n(70171);const a={ar:[".",","],bg:[","," "],bs:[",","."],ca:[",","."],cs:[","," "],da:[",","."],de:[",","."],"de-ch":[".","’"],el:[",","."],en:[".",","],"en-au":[".",","],es:[",","."],"es-mx":[".",","],et:[","," "],fi:[","," "],fr:[","," "],"fr-ch":[","," "],he:[".",","],hi:[".",",","#,##,##0.###"],hr:[",","."],hu:[","," "],id:[",","."],it:[",","."],"it-ch":[".","’"],ja:[".",","],ko:[".",","],lt:[","," "],lv:[","," "],mk:[",","."],nb:[","," "],nl:[",","."],pl:[","," "],pt:[",","."],"pt-pt":[","," "],ro:[",","."],ru:[","," "],sk:[","," "],sl:[",","."],sr:[",","."],sv:[","," "],th:[".",","],tr:[",","."],uk:[","," "],vi:[",","."],zh:[".",","]};function s(e=(0,i.Kd)()){let t=(e=e.toLowerCase())in a;if(!t){const n=e.split("-");n.length>1&&n[0]in a&&(e=n[0],t=!0),t||(e="en")}const[n,r,s="#,##0.###"]=a[e];return{decimal:n,group:r,pattern:s}}function o(e,t){const n=s((t={...t}).locale);t.customs=n;const r=t.pattern||n.pattern;return isNaN(e)||Math.abs(e)===1/0?null:function(e,t,n){const r=(n=n||{}).customs.group,i=n.customs.decimal,a=t.split(";"),s=a[0];if((t=a[e<0?1:0]||"-"+s).includes("%"))e*=100;else if(t.includes("‰"))e*=1e3;else{if(t.includes("¤"))throw new Error("currency notation not supported");if(t.includes("E"))throw new Error("exponential notation not supported")}const o=u,l=s.match(o);if(!l)throw new Error("unable to find a number expression in pattern: "+t);return!1===n.fractional&&(n.places=0),t.replace(o,function(e,t,n){!0===(n=n||{}).places&&(n.places=0),n.places===1/0&&(n.places=6);const r=t.split("."),i="string"==typeof n.places&&n.places.indexOf(",");let a=n.places;i?a=n.places.substring(i+1):a>=0||(a=(r[1]||[]).length),n.round<0||(e=Number(e.toFixed(Number(a))));const s=String(Math.abs(e)).split("."),o=s[1]||"";if(r[1]||n.places){i&&(n.places=n.places.substring(0,i));const e=void 0!==n.places?n.places:r[1]&&r[1].lastIndexOf("0")+1;e>o.length&&(s[1]=o.padEnd(Number(e),"0")),a<o.length&&(s[1]=o.substr(0,Number(a)))}else s[1]&&s.pop();const u=r[0].replace(",","");let l=u.indexOf("0");-1!==l&&(l=u.length-l,l>s[0].length&&(s[0]=s[0].padStart(l,"0")),u.includes("#")||(s[0]=s[0].substr(s[0].length-l)));let c,d,h=r[0].lastIndexOf(",");if(-1!==h){c=r[0].length-h-1;const e=r[0].substr(0,h);h=e.lastIndexOf(","),-1!==h&&(d=e.length-h-1)}const f=[];for(let e=s[0];e;){const t=e.length-c;f.push(t>0?e.substr(t):e),e=t>0?e.slice(0,t):"",d&&(c=d,d=void 0)}return s[0]=f.reverse().join(n.group||","),s.join(n.decimal||".")}(e,l[0],{decimal:i,group:r,places:n.places,round:n.round}))}(e,r,t)}const u=/[#0,]*[#0](?:\.0*#*)?/;function l(e){const t=s((e=e||{}).locale),n=e.pattern||t.pattern,i=t.group,a=t.decimal;let o=1;if(n.includes("%"))o/=100;else if(n.includes("‰"))o/=1e3;else if(n.includes("¤"))throw new Error("currency notation not supported");const l=n.split(";");1===l.length&&l.push("-"+l[0]);const c=h(l,(t=>(t="(?:"+(0,r.Qs)(t,".")+")").replace(u,(t=>{const n={signed:!1,separator:e.strict?i:[i,""],fractional:e.fractional,decimal:a,exponent:!1},r=t.split(".");let s=e.places;1===r.length&&1!==o&&(r[1]="###"),1===r.length||0===s?n.fractional=!1:(void 0===s&&(s=e.pattern?r[1].lastIndexOf("0")+1:1/0),s&&null==e.fractional&&(n.fractional=!0),!e.places&&s<r[1].length&&(s+=","+r[1].length),n.places=s);const u=r[0].split(",");return u.length>1&&(n.groupSize=u.pop().length,u.length>1&&(n.groupSize2=u.pop().length)),"("+function(e){"places"in(e=e||{})||(e.places=1/0),"string"!=typeof e.decimal&&(e.decimal="."),"fractional"in e&&!/^0/.test(String(e.places))||(e.fractional=[!0,!1]),"exponent"in e||(e.exponent=[!0,!1]),"eSigned"in e||(e.eSigned=[!0,!1]);const t=d(e),n=h(e.fractional,(t=>{let n="";return t&&0!==e.places&&(n="\\"+e.decimal,e.places===1/0?n="(?:"+n+"\\d+)?":n+="\\d{"+e.places+"}"),n}),!0);let r=t+n;return n&&(r="(?:(?:"+r+")|(?:"+n+"))"),r+h(e.exponent,(t=>t?"([eE]"+d({signed:e.eSigned})+")":""))}(n)+")"}))),!0);return{regexp:c.replace(/[\xa0 ]/g,"[\\s\\xa0]"),group:i,decimal:a,factor:o}}function c(e,t){const n=l(t),r=new RegExp("^"+n.regexp+"$").exec(e);if(!r)return NaN;let i=r[1];if(!r[1]){if(!r[2])return NaN;i=r[2],n.factor*=-1}return i=i.replace(new RegExp("["+n.group+"\\s\\xa0]","g"),"").replace(n.decimal,"."),Number(i)*n.factor}function d(e){return"signed"in(e=e||{})||(e.signed=[!0,!1]),"separator"in e?"groupSize"in e||(e.groupSize=3):e.separator="",h(e.signed,(e=>e?"[-+]":""),!0)+h(e.separator,(t=>{if(!t)return"(?:\\d+)";" "===(t=(0,r.Qs)(t))?t="\\s":" "===t&&(t="\\s\\xa0");const n=e.groupSize,i=e.groupSize2;if(i){const e="(?:0|[1-9]\\d{0,"+(i-1)+"}(?:["+t+"]\\d{"+i+"})*["+t+"]\\d{"+n+"})";return n-i>0?"(?:"+e+"|(?:0|[1-9]\\d{0,"+(n-1)+"}))":e}return"(?:0|[1-9]\\d{0,"+(n-1)+"}(?:["+t+"]\\d{"+n+"})*)"}),!0)}const h=(e,t,n)=>{if(!(e instanceof Array))return t(e);const r=[];for(let n=0;n<e.length;n++)r.push(t(e[n]));return f(r.join("|"),Boolean(n))},f=(e,t)=>"("+(t?"?:":"")+e+")"},99514:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(35671);function i(e){return"oid"===e.type||"esriFieldTypeOID"===e.type}function a(e){return"global-id"===e.type||"esriFieldTypeGlobalID"===e.type}class s{constructor(e=[]){if(this.fields=[],this._fieldsMap=new Map,this._normalizedFieldsMap=new Map,this._dateFieldsSet=new Set,this._numericFieldsSet=new Set,this.dateFields=[],this.numericFields=[],this._requiredFields=null,!e)return;this.fields=e;const t=[];for(const s of e){const e=s?.name,l=u(s?.name);if(e&&l){const u=o(e);this._fieldsMap.set(e,s),this._fieldsMap.set(u,s),this._normalizedFieldsMap.set(l,s),t.push(u),"date"===(n=s).type||"esriFieldTypeDate"===n.type?(this.dateFields.push(s),this._dateFieldsSet.add(s)):(0,r.H7)(s)&&(this._numericFieldsSet.add(s),this.numericFields.push(s)),i(s)||a(s)||(s.editable=null==s.editable||!!s.editable,s.nullable=null==s.nullable||!!s.nullable)}}var n;t.sort(),this.uid=t.join(",")}destroy(){this._fieldsMap.clear()}get requiredFields(){if(!this._requiredFields){this._requiredFields=[];for(const e of this.fields)i(e)||a(e)||e.nullable||void 0!==(0,r.os)(e)||this._requiredFields.push(e)}return this._requiredFields}has(e){return null!=this.get(e)}get(e){if(!e)return;let t=this._fieldsMap.get(e);return t||(t=this._fieldsMap.get(o(e))??this._normalizedFieldsMap.get(u(e)),t&&this._fieldsMap.set(e,t),t)}isDateField(e){return this._dateFieldsSet.has(this.get(e))}isNumericField(e){return this._numericFieldsSet.has(this.get(e))}normalizeFieldName(e){const t=this.get(e);if(t)return t.name??void 0}}function o(e){return e.trim().toLowerCase()}function u(e){return(0,r.q6)(e)?.toLowerCase()??""}},51706:(e,t,n)=>{var r,i;function a(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function s(e){return null!=e&&!isNaN(e)&&isFinite(e)}function o(e){return e.valueExpression?r.Expression:e.field&&"string"==typeof e.field?r.Field:r.Unknown}function u(e,t){const n=t||o(e),a=e.valueUnit||"unknown";return n===r.Unknown?i.Constant:e.stops?i.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?i.ClampedLinear:"unknown"===a?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?i.Proportional:i.Additive:i.Identity:i.RealWorldSize}n.d(t,{PS:()=>o,QW:()=>u,RY:()=>r,hL:()=>i,iY:()=>a,qh:()=>s}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(r||(r={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(i||(i={}))},3172:(e,t,n)=>{n.r(t),n.d(t,{default:()=>m});var r=n(68773),i=n(40330),a=n(20102),s=n(80442),o=n(22974),u=n(70586),l=n(95330),c=n(17452),d=n(19745),h=n(71058),f=n(85958);async function m(e,t){const o=(0,c.HK)(e),d=(0,c.jc)(e);d||o||(e=(0,c.Fv)(e));const y={url:e,requestOptions:{...(0,u.Wg)(t)}};let w=(0,c.oh)(e);if(w){const e=await async function(e,t){if(null!=e.responseData)return e.responseData;if(e.headers&&(t.requestOptions.headers={...t.requestOptions.headers,...e.headers}),e.query&&(t.requestOptions.query={...t.requestOptions.query,...e.query}),e.before){let n,r;try{r=await e.before(t)}catch(e){n=b("request:interceptor",e,t)}if((r instanceof Error||r instanceof a.Z)&&(n=b("request:interceptor",r,t)),n)throw e.error&&e.error(n),n;return r}}(w,y);if(null!=e)return{data:e,getHeader:F,httpStatus:200,requestOptions:y.requestOptions,url:y.url};w.after||w.error||(w=null)}if(e=y.url,"image"===(t=y.requestOptions).responseType){if((0,s.Z)("host-webworker")||(0,s.Z)("host-node"))throw b("request:invalid-parameters",new Error("responseType 'image' is not supported in Web Workers or Node environment"),y)}else if(o)throw b("request:invalid-parameters",new Error("Data URLs are not supported for responseType = "+t.responseType),y);if("head"===t.method){if(t.body)throw b("request:invalid-parameters",new Error("body parameter cannot be set when method is 'head'"),y);if(o||d)throw b("request:invalid-parameters",new Error("data and blob URLs are not supported for method 'head'"),y)}if(await async function(){(0,s.Z)("host-webworker")?p||(p=await n.e(9884).then(n.bind(n,29884))):m._abortableFetch||(m._abortableFetch=globalThis.fetch.bind(globalThis))}(),p)return p.execute(e,t);const x=new AbortController;(0,l.fu)(t,(()=>x.abort()));const C={controller:x,credential:void 0,credentialToken:void 0,fetchOptions:void 0,hasToken:!1,interceptor:w,params:y,redoRequest:!1,useIdentity:g.useIdentity,useProxy:!1,useSSL:!1,withCredentials:!1},A=await async function(e){let t,n;await async function(e){const t=e.params.url,n=e.params.requestOptions,a=e.controller.signal,s=n.body;let o=null,u=null;if(D&&"HTMLFormElement"in globalThis&&(s instanceof FormData?o=s:s instanceof HTMLFormElement&&(o=new FormData(s))),"string"==typeof s&&(u=s),e.fetchOptions={cache:n.cacheBust&&!m._abortableFetch.polyfill?"no-cache":"default",credentials:"same-origin",headers:n.headers||{},method:"head"===n.method?"HEAD":"GET",mode:"cors",priority:g.priority,redirect:"follow",signal:a},(o||u)&&(e.fetchOptions.body=o||u),"anonymous"===n.authMode&&(e.useIdentity=!1),e.hasToken=!!(/token=/i.test(t)||n.query?.token||o?.get("token")),!e.hasToken&&r.Z.apiKey&&(0,h.r)(t)&&(n.query||(n.query={}),n.query.token=r.Z.apiKey,e.hasToken=!0),e.useIdentity&&!e.hasToken&&!e.credentialToken&&!v(t)&&!(0,l.Hc)(a)){let r;"immediate"===n.authMode?(await E(),r=await i.id.getCredential(t,{signal:a}),e.credential=r):"no-prompt"===n.authMode?(await E(),r=await i.id.getCredential(t,{prompt:!1,signal:a}).catch((()=>{})),e.credential=r):i.id&&(r=i.id.findCredential(t)),r&&(e.credentialToken=r.token,e.useSSL=!!r.ssl)}}(e);try{do{[t,n]=await S(e)}while(!await I(e,t,n))}catch(n){const r=b("request:server",n,e.params,t);throw r.details.ssl=e.useSSL,e.interceptor&&e.interceptor.error&&e.interceptor.error(r),r}const a=e.params.url;if(n&&/\/sharing\/rest\/(accounts|portals)\/self/i.test(a)){if(!e.hasToken&&!e.credentialToken&&n.user?.username&&!(0,c.kl)(a)){const e=(0,c.P$)(a,!0);e&&g.trustedServers.push(e)}Array.isArray(n.authorizedCrossOriginNoCorsDomains)&&(0,f.Hu)(n.authorizedCrossOriginNoCorsDomains)}const s=e.credential;if(s&&i.id){const e=i.id.findServerInfo(s.server);let t=e&&e.owningSystemUrl;if(t){t=t.replace(/\/?$/,"/sharing");const e=i.id.findCredential(t,s.userId);e&&-1===i.id._getIdenticalSvcIdx(t,e)&&e.resources.unshift(t)}}return{data:n,getHeader:t?e=>t?.headers.get(e):F,httpStatus:t?.status??200,requestOptions:e.params.requestOptions,ssl:e.useSSL,url:e.params.url}}(C);return w?.after?.(A),A}let p;const g=r.Z.request,D="FormData"in globalThis,y=[499,498,403,401],w=["COM_0056","COM_0057","SB_0008"],x=[/\/arcgis\/tokens/i,/\/sharing(\/rest)?\/generatetoken/i,/\/rest\/info/i],F=()=>null,C=Symbol();function A(e){const t=(0,c.P$)(e);return!t||t.endsWith(".arcgis.com")||m._corsServers.includes(t)||(0,c.kl)(t)}function b(e,t,n,r){let i="Error";const s={url:n.url,requestOptions:n.requestOptions,getHeader:F,ssl:!1};if(t instanceof a.Z)return t.details?(t.details=(0,o.d9)(t.details),t.details.url=n.url,t.details.requestOptions=n.requestOptions):t.details=s,t;if(t){const e=r&&(e=>r.headers.get(e)),n=r&&r.status,a=t.message;a&&(i=a),e&&(s.getHeader=e),s.httpStatus=(null!=t.httpCode?t.httpCode:t.code)||n||0,s.subCode=t.subcode,s.messageCode=t.messageCode,"string"==typeof t.details?s.messages=[t.details]:s.messages=t.details,s.raw=C in t?t[C]:t}return(0,l.D_)(t)?(0,l.zE)():new a.Z(e,i,s)}async function E(){i.id||await Promise.all([n.e(6261),n.e(1400),n.e(450)]).then(n.bind(n,73660))}function v(e){return x.some((t=>t.test(e)))}async function S(e){let t=e.params.url;const n=e.params.requestOptions,r=e.fetchOptions??{},a=(0,c.jc)(t)||(0,c.HK)(t),o=n.responseType||"json",u=a?0:null!=n.timeout?n.timeout:g.timeout;let h=!1;if(!a){e.useSSL&&(t=(0,c.hO)(t)),n.cacheBust&&"default"===r.cache&&(t=(0,c.ZN)(t,"request.preventCache",Date.now()));let a={...n.query};e.credentialToken&&(a.token=e.credentialToken);let o=(0,c.B7)(a);(0,s.Z)("esri-url-encodes-apostrophe")&&(o=o.replace(/'/g,"%27"));const u=t.length+1+o.length;let l;h="delete"===n.method||"post"===n.method||"put"===n.method||!!n.body||u>g.maxUrlLength;const m=n.useProxy||!!(0,c.ed)(t);if(m){const e=(0,c.b7)(t);l=e.path,!h&&l.length+1+u>g.maxUrlLength&&(h=!0),e.query&&(a={...e.query,...a})}if("HEAD"===r.method&&(h||m)){if(h){if(u>g.maxUrlLength)throw b("request:invalid-parameters",new Error("URL exceeds maximum length"),e.params);throw b("request:invalid-parameters",new Error("cannot use POST request when method is 'head'"),e.params)}if(m)throw b("request:invalid-parameters",new Error("cannot use proxy when method is 'head'"),e.params)}if(h?(r.method="delete"===n.method?"DELETE":"put"===n.method?"PUT":"POST",n.body?t=(0,c.fl)(t,a):(r.body=(0,c.B7)(a),r.headers||(r.headers={}),r.headers["Content-Type"]="application/x-www-form-urlencoded")):t=(0,c.fl)(t,a),m&&(e.useProxy=!0,t=`${l}?${t}`),a.token&&D&&r.body instanceof FormData&&!(0,d.P)(t)&&r.body.set("token",a.token),n.hasOwnProperty("withCredentials"))e.withCredentials=n.withCredentials;else if(!(0,c.D6)(t,(0,c.TI)()))if((0,c.kl)(t))e.withCredentials=!0;else if(i.id){const n=i.id.findServerInfo(t);n&&n.webTierAuth&&(e.withCredentials=!0)}e.withCredentials&&(r.credentials="include",(0,f.jH)(t)&&await(0,f.jz)(h?(0,c.fl)(t,a):t))}let p,y,w=0,x=!1;u>0&&(w=setTimeout((()=>{x=!0,e.controller.abort()}),u));try{if("native-request-init"===n.responseType)y=r,y.url=t;else if("image"!==n.responseType||"default"!==r.cache||"GET"!==r.method||h||function(e){if(e)for(const t of Object.getOwnPropertyNames(e))if(e[t])return!0;return!1}(n.headers)||!a&&!e.useProxy&&g.proxyUrl&&!A(t)){if(p=await m._abortableFetch(t,r),e.useProxy||function(e){const t=(0,c.P$)(e);t&&!m._corsServers.includes(t)&&m._corsServers.push(t)}(t),"native"===n.responseType)y=p;else if("HEAD"!==r.method)if(p.ok){switch(o){case"array-buffer":y=await p.arrayBuffer();break;case"blob":case"image":y=await p.blob();break;default:y=await p.text()}if(w&&(clearTimeout(w),w=0),"json"===o||"xml"===o||"document"===o)if(y)switch(o){case"json":y=JSON.parse(y);break;case"xml":y=k(y,"application/xml");break;case"document":y=k(y,"text/html")}else y=null;if(y){if("array-buffer"===o||"blob"===o){const e=p.headers.get("Content-Type");if(e&&/application\/json|text\/plain/i.test(e)&&y["blob"===o?"size":"byteLength"]<=750)try{const e=await new Response(y).json();e.error&&(y=e)}catch{}}"image"===o&&y instanceof Blob&&(y=await T(URL.createObjectURL(y),e,!0))}}else y=await p.text()}else y=await T(t,e)}catch(r){if("AbortError"===r.name){if(x)throw new Error("Timeout exceeded");throw(0,l.zE)("Request canceled")}if(!(!p&&r instanceof TypeError&&g.proxyUrl)||n.body||"delete"===n.method||"head"===n.method||"post"===n.method||"put"===n.method||e.useProxy||A(t))throw r;e.redoRequest=!0,(0,c.tD)({proxyUrl:g.proxyUrl,urlPrefix:(0,c.P$)(t)??""})}finally{w&&clearTimeout(w)}return[p,y]}function k(e,t){let n;try{n=(new DOMParser).parseFromString(e,t)}catch{}if(!n||n.getElementsByTagName("parsererror").length)throw new SyntaxError("XML Parse error");return n}async function I(e,t,n){if(e.redoRequest)return e.redoRequest=!1,!1;const r=e.params.requestOptions;if(!t||"native"===r.responseType||"native-request-init"===r.responseType)return!0;let a,s;if(!t.ok)throw a=new Error(`Unable to load ${t.url} status: ${t.status}`),a[C]=n,a;n&&(n.error?a=n.error:"error"===n.status&&Array.isArray(n.messages)&&(a={...n},a[C]=n,a.details=n.messages));let o,u=null;a&&(s=Number(a.code),u=a.hasOwnProperty("subcode")?Number(a.subcode):null,o=a.messageCode,o=o&&o.toUpperCase());const l=r.authMode;if(403===s&&(4===u||a.message&&a.message.toLowerCase().includes("ssl")&&!a.message.toLowerCase().includes("permission"))){if(!e.useSSL)return e.useSSL=!0,!1}else if(!e.hasToken&&e.useIdentity&&("no-prompt"!==l||498===s)&&void 0!==s&&y.includes(s)&&!v(e.params.url)&&(403!==s||o&&!w.includes(o)&&(null==u||2===u&&e.credentialToken))){await E();try{const t=await i.id.getCredential(e.params.url,{error:b("request:server",a,e.params),prompt:"no-prompt"!==l,signal:e.controller.signal,token:e.credentialToken});return e.credential=t,e.credentialToken=t.token,e.useSSL=e.useSSL||t.ssl,!1}catch(t){if("no-prompt"===l)return e.credential=void 0,e.credentialToken=void 0,!1;a=t}}if(a)throw a;return!0}function T(e,t,n=!1){const r=t.controller.signal,i=new Image;return t.withCredentials?i.crossOrigin="use-credentials":i.crossOrigin="anonymous",i.alt="",i.fetchPriority=g.priority,i.src=e,(0,f.fY)(i,e,n,r)}m._abortableFetch=null,m._corsServers=["https://server.arcgisonline.com","https://services.arcgisonline.com"]},71058:(e,t,n)=>{n.d(t,{r:()=>a});var r=n(17452);const i=["elevation3d.arcgis.com","js.arcgis.com","jsdev.arcgis.com","jsqa.arcgis.com","static.arcgis.com"];function a(e){const t=(0,r.P$)(e,!0);return!!t&&t.endsWith(".arcgis.com")&&!i.includes(t)&&!e.endsWith("/sharing/rest/generateToken")}},20837:(e,t,n)=>{n.r(t),n.d(t,{Dictionary:()=>r.Z,arcade:()=>vt,arcadeFeature:()=>i.Z,convertFeatureLayerToFeatureSet:()=>Yt,convertJsonToArcade:()=>Xt,convertMapToFeatureSetCollection:()=>Qt,convertServiceUrlToWorkspace:()=>$t,createExecContext:()=>Pt,createFeature:()=>Zt,createFunction:()=>Vt,createSyntaxTree:()=>Ot,dependsOnView:()=>Jt,enableFeatureSetOperations:()=>nn,enableGeometryOperations:()=>tn,evalSyntaxTree:()=>Gt,executeAsyncFunction:()=>qt,executeFunction:()=>zt,extractFieldNames:()=>jt,getArcadeType:()=>Rt,getViewInfo:()=>Kt,hasGeometryFunctions:()=>sn,hasGeometryOperations:()=>un,hasVariable:()=>Wt,loadScriptDependencies:()=>en,updateExecContext:()=>Ut}),n(66577);var r=n(33586),i=n(15274),a=n(61363),s=n(90077),o=n(84211),u=n(13976),l=n(12834),c=n(18811),d=n(38176),h=n(58130),f=n(27360),m=n(95330),p=n(9361),g=n(82971),D=n(92089),y=n(17057),w=n(9609),x=n(27535);class F extends w.Rm{constructor(e,t){super(),this.paramCount=t,this.fn=e}createFunction(e){return(...t)=>{if(t.length!==this.paramCount)throw new x.aV(e,x.rH.WrongNumberOfParameters,null);return this.fn(...t)}}call(e,t){return this.fn(...t.arguments)}marshalledCall(e,t,n,r){return r(e,t,((t,i,s)=>{s=s.map((t=>!(0,a.i)(t)||t instanceof w.Vg?t:(0,w.aq)(t,e,r)));const o=this.call(n,{arguments:s});return(0,m.y8)(o)?o.then((e=>(0,w.aq)(e,n,r))):o}))}}function C(e,t,n){try{return n(e,null,t.arguments)}catch(e){throw e}}function A(e,t){try{switch(t.type){case"EmptyStatement":return"lc.voidOperation";case"VariableDeclarator":return function(e,t){let n=null===t.init?null:A(e,t.init);n===a.v&&(n=null);const r=t.id.name.toLowerCase();if(S(r),null!==e.localScope){if(void 0!==e.localScope[r])return"lscope['"+r+"']="+n+"; ";if(void 0!==e.localScope._SymbolsMap[r])return"lscope['"+e.localScope._SymbolsMap[r]+"']="+n+"; ";{const t=I(e);return e.localScope._SymbolsMap[r]=t,e.mangleMap[r]=t,"lscope['"+t+"']="+n+"; "}}if(void 0!==e.globalScope[r])return"gscope['"+r+"']="+n+"; ";if(void 0!==e.globalScope._SymbolsMap[r])return"gscope['"+e.globalScope._SymbolsMap[r]+"']="+n+"; ";if(e.undeclaredGlobalsInFunctions.has(r)){const t=e.undeclaredGlobalsInFunctions.get(r).manglename;return e.globalScope._SymbolsMap[r]=t,e.mangleMap[r]=t,e.undeclaredGlobalsInFunctions.delete(r),"gscope[lang.setAssig('"+t+"', runtimeCtx)]="+n+"; "}const i=I(e);return e.globalScope._SymbolsMap[r]=i,e.mangleMap[r]=i,"gscope['"+i+"']="+n+"; "}(e,t);case"VariableDeclaration":return function(e,t){const n=[];for(let r=0;r<t.declarations.length;r++)n.push(A(e,t.declarations[r]));return n.join("\n")+" \n lastStatement=  lc.voidOperation; \n"}(e,t);case"BlockStatement":case"Program":return v(e,t);case"FunctionDeclaration":return function(e,t){const n=t.id.name.toLowerCase();S(n);let r="",i=!1;void 0!==e.globalScope[n]?r=n:void 0!==e.globalScope._SymbolsMap[n]?r=e.globalScope._SymbolsMap[n]:e.undeclaredGlobalsInFunctions.has(n)?(r=e.undeclaredGlobalsInFunctions.get(n).manglename,e.globalScope._SymbolsMap[n]=r,e.mangleMap[n]=r,e.undeclaredGlobalsInFunctions.delete(n),i=!0):(r=I(e),e.globalScope._SymbolsMap[n]=r,e.mangleMap[n]=r);const a={isAsync:e.isAsync,console:e.console,exports:e.exports,undeclaredGlobalsInFunctions:e.undeclaredGlobalsInFunctions,customfunctions:e.customfunctions,moduleFactory:e.moduleFactory,moduleFactoryMap:e.moduleFactoryMap,libraryResolver:e.libraryResolver,lrucache:e.lrucache,interceptor:e.interceptor,services:e.services,symbols:e.symbols,mangleMap:e.mangleMap,localScope:{_SymbolsMap:{}},depthCounter:e.depthCounter,globalScope:e.globalScope};let s="new lang.UserDefinedCompiledFunction( lang.functionDepthchecker(function() { var lastStatement = lc.voidOperation; \n   var lscope = runtimeCtx.localStack[runtimeCtx.localStack.length-1];\n";for(let n=0;n<t.params.length;n++){const r=t.params[n].name.toLowerCase();S(r);const i=I(e);a.localScope._SymbolsMap[r]=i,a.mangleMap[r]=i,s+="lscope['"+i+"']=arguments["+n.toString()+"];\n"}return!0===e.isAsync?(s+="return lang.__awaiter(this, void 0, void 0, function* () {\n",s+=v(a,t.body)+"\n return lastStatement; ",s+="});  }",s+=", runtimeCtx),"+t.params.length+")",s+="\n lastStatement = lc.voidOperation; \n"):(s+=v(a,t.body)+"\n return lastStatement; }, runtimeCtx),"+t.params.length+")",s+="\n lastStatement = lc.voidOperation; \n"),i?"gscope[lang.setAssig('"+r+"', runtimeCtx)]="+s:"gscope['"+r+"']="+s}(e,t);case"ImportDeclaration":return function(e,t){const n=t.specifiers[0].local.name.toLowerCase();S(n);const r=e.libraryResolver?.loadLibrary(n),i=I(e);void 0===e.moduleFactory[r.uri]&&(e.moduleFactory[r.uri]=function(e,t,n=!1){const r={isAsync:n,moduleFactory:t.moduleFactory,moduleFactoryMap:{},libraryResolver:new D.s(null,e.loadedModules),globalScope:N(t.vars,n?B:k,t.customfunctions),customfunctions:t.customfunctions,localScope:null,mangleMap:{},undeclaredGlobalsInFunctions:new Map,depthCounter:{depth:1},exports:{},console:P,lrucache:t.lrucache,timeReference:t.timeReference??null,interceptor:t.interceptor,services:t.services,symbols:{symbolCounter:0}};let i=A(r,e);""===i&&(i="lc.voidOperation; ");let s="";s=n?"var runtimeCtx=this.prepare(context, true);\n var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; \nreturn lang.__awaiter(this, void 0, void 0, function* () {\n\n function mainBody() {\n var lastStatement=lc.voidOperation;\n return lang.__awaiter(this, void 0, void 0, function* () {\n"+i+"\n return lastStatement; }); } \n yield mainBody(); \n return this.prepareModule(runtimeCtx); }); ":"var runtimeCtx=this.prepare(context, false);\n var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; \n function mainBody() {\n var lastStatement=lc.voidOperation;\n "+i+"\n return lastStatement; } \n mainBody(); \n return this.prepareModule(runtimeCtx); ";const o=r.moduleFactory,u=r.moduleFactoryMap,l=r.exports,c={};for(const e in l)c[e]=void 0!==r.mangleMap[e]?r.mangleMap[e]:e;const d={lc:a.n,lang:V,mangles:r.mangleMap,prepareModule:e=>new U(e),prepare(e,t){let n=e.spatialReference;null==n&&(n=new g.Z({wkid:102100}));const r=L(e.vars,e.customfunctions,t,e.timeReference);return{localStack:[],isAsync:t,exports:l,exportmangle:c,gdefs:{},moduleFactory:o,moduleFactoryMap:u,moduleSingletons:e.moduleSingletons,mangleMap:this.mangles,spatialReference:n,globalScope:r,abortSignal:void 0===e.abortSignal||null===e.abortSignal?{aborted:!1}:e.abortSignal,localScope:null,services:e.services,console:e.console?e.console:P,lrucache:e.lrucache,timeReference:e.timeReference??null,interceptor:e.interceptor,symbols:{symbolCounter:0},depthCounter:e.depthCounter}}};return new Function("context","spatialReference",s).bind(d)}(r.syntax,{interceptor:e.interceptor,services:e.services,moduleFactory:e.moduleFactory,lrucache:e.lrucache,timeReference:e.timeReference??null,libraryResolver:e.libraryResolver,customfunctions:e.customfunctions,vars:{}},e.isAsync)),e.moduleFactoryMap[i]=r.uri;let s="";if(s=e.isAsync?"(yield lang.loadModule('"+i+"', runtimeCtx) ); ":"lang.loadModule('"+i+"', runtimeCtx); ",void 0!==e.globalScope[n])return"gscope['"+n+"']="+s;if(void 0!==e.globalScope._SymbolsMap[n])return"gscope['"+e.globalScope._SymbolsMap[n]+"']="+s;let o="";return e.undeclaredGlobalsInFunctions.has(n)?(o=e.undeclaredGlobalsInFunctions.get(n).manglename,e.undeclaredGlobalsInFunctions.delete(n)):o=I(e),e.globalScope._SymbolsMap[n]=o,e.mangleMap[n]=o,"gscope[lang.setAssig('"+o+"', runtimeCtx)]="+s}(e,t);case"ExportNamedDeclaration":return function(e,t){const n=A(e,t.declaration);if("FunctionDeclaration"===t.declaration.type)e.exports[t.declaration.id.name.toLowerCase()]="function";else if("VariableDeclaration"===t.declaration.type)for(const n of t.declaration.declarations)e.exports[n.id.name.toLowerCase()]="variable";return n}(e,t);case"ReturnStatement":return function(e,t){return null===t.argument?"return lc.voidOperation":"return "+A(e,t.argument)}(e,t);case"IfStatement":return E(e,t);case"ExpressionStatement":return function(e,t){return"AssignmentExpression"===t.expression.type?"lastStatement = lc.voidOperation; "+A(e,t.expression)+"; \n ":(t.expression.type,"lastStatement = "+A(e,t.expression)+"; ")}(e,t);case"AssignmentExpression":return function(e,t){const n=A(e,t.right);let r=null,i="";if("MemberExpression"===t.left.type)return r=A(e,t.left.object),!0===t.left.computed?i=A(e,t.left.property):(i="'"+t.left.property.name+"'",S(t.left.property.name)),"lang.assignmember("+r+","+i+",'"+t.operator+"',"+n+")";if(r=t.left.name.toLowerCase(),S(r),null!==e.localScope){if(void 0!==e.localScope[r])return"lscope['"+r+"']=lang.assign("+n+",'"+t.operator+"', lscope['"+r+"'])";if(void 0!==e.localScope._SymbolsMap[r])return"lscope['"+e.localScope._SymbolsMap[r]+"']=lang.assign("+n+",'"+t.operator+"', lscope['"+e.localScope._SymbolsMap[r]+"'])"}if(void 0!==e.globalScope[r])return"gscope['"+r+"']=lang.assign("+n+",'"+t.operator+"', gscope['"+r+"'])";if(void 0!==e.globalScope._SymbolsMap[r])return"gscope['"+e.globalScope._SymbolsMap[r]+"']=lang.assign("+n+",'"+t.operator+"', gscope['"+e.globalScope._SymbolsMap[r]+"'])";if(null!==e.localScope){if(e.undeclaredGlobalsInFunctions.has(r))return"gscope[lang.chkAssig('"+e.undeclaredGlobalsInFunctions.get(r).manglename+"',runtimeCtx)]=lang.assign("+n+",'"+t.operator+"', gscope['"+e.undeclaredGlobalsInFunctions.get(r).manglename+"'])";const i={manglename:I(e),node:t.argument};return e.undeclaredGlobalsInFunctions.set(r,i),"gscope[lang.chkAssig('"+i.manglename+"',runtimeCtx)]=lang.assign("+n+",'"+t.operator+"', gscope['"+i.manglename+"'])"}throw new x.aV(e,x.rH.InvalidIdentifier,t)}(e,t);case"UpdateExpression":return function(e,t){let n=null,r="";if("MemberExpression"===t.argument.type)return n=A(e,t.argument.object),!0===t.argument.computed?r=A(e,t.argument.property):(r="'"+t.argument.property.name+"'",S(t.argument.property.name)),"lang.memberupdate("+n+","+r+",'"+t.operator+"',"+t.prefix+")";if(n=t.argument.name.toLowerCase(),S(n),null!==e.localScope){if(void 0!==e.localScope[n])return"lang.update(lscope, '"+n+"','"+t.operator+"',"+t.prefix+")";if(void 0!==e.localScope._SymbolsMap[n])return"lang.update(lscope, '"+e.localScope._SymbolsMap[n]+"','"+t.operator+"',"+t.prefix+")"}if(void 0!==e.globalScope[n])return"lang.update(gscope, '"+n+"','"+t.operator+"',"+t.prefix+")";if(void 0!==e.globalScope._SymbolsMap[n])return"lang.update(gscope, '"+e.globalScope._SymbolsMap[n]+"','"+t.operator+"',"+t.prefix+")";if(null!==e.localScope){if(e.undeclaredGlobalsInFunctions.has(n))return"lang.update(gscope,lang.chkAssig( '"+e.undeclaredGlobalsInFunctions.get(n).manglename+"',runtimeCtx),'"+t.operator+"',"+t.prefix+")";const r={manglename:I(e),node:t.argument};return e.undeclaredGlobalsInFunctions.set(n,r),"lang.update(gscope, lang.chkAssig('"+r.manglename+"',runtimeCtx),'"+t.operator+"',"+t.prefix+")"}throw new x.aV(e,x.rH.InvalidIdentifier,t)}(e,t);case"BreakStatement":return"break";case"ContinueStatement":return"continue";case"TemplateLiteral":return function(e,t){try{const n=[];let r=0;for(const i of t.quasis)n.push(i.value?JSON.stringify(i.value.cooked):JSON.stringify("")),!1===i.tail&&(n.push(t.expressions[r]?"lang.castString(lang.aCheck("+A(e,t.expressions[r])+", 'TemplateLiteral'))":""),r++);return"(["+n.join(",")+"]).join('')"}catch(e){throw e}}(e,t);case"TemplateElement":return JSON.stringify(t.value?t.value.cooked:"");case"ForStatement":return function(e,t){let n="lastStatement = lc.voidOperation; \n";null!==t.init&&(n+=A(e,t.init)+"; ");const r=T(e),i=T(e);return n+="var "+r+" = true; ",n+="\n do { ",null!==t.update&&(n+=" if ("+r+"===false) {\n "+A(e,t.update)+"  \n}\n "+r+"=false; \n"),null!==t.test&&(n+="var "+i+" = "+A(e,t.test)+"; ",n+="if ("+i+"===false) { break; } else if ("+i+"!==true) { lang.error('"+x.rH.BooleanConditionRequired+"');   }\n"),n+=A(e,t.body),null!==t.update&&(n+="\n "+A(e,t.update)),n+="\n"+r+" = true; \n} while(true);  lastStatement = lc.voidOperation; ",n}(e,t);case"ForInStatement":return function(e,t){const n=T(e),r=T(e),i=T(e);let a="var "+n+" = "+A(e,t.right)+";\n";"VariableDeclaration"===t.left.type&&(a+=A(e,t.left));let s="VariableDeclaration"===t.left.type?t.left.declarations[0].id.name:t.left.name;s=s.toLowerCase(),S(s);let o="";null!==e.localScope&&(void 0!==e.localScope[s]?o="lscope['"+s+"']":void 0!==e.localScope._SymbolsMap[s]&&(o="lscope['"+e.localScope._SymbolsMap[s]+"']"));let u="";if(""===o)if(void 0!==e.globalScope[s])o="gscope['"+s+"']";else if(void 0!==e.globalScope._SymbolsMap[s])o="gscope['"+e.globalScope._SymbolsMap[s]+"']";else if(null!==e.localScope)if(e.undeclaredGlobalsInFunctions.has(s))o="gscope['"+e.undeclaredGlobalsInFunctions.get(s).manglename+"']",u=e.undeclaredGlobalsInFunctions.get(s).manglename;else{const n={manglename:I(e),node:t.left};e.undeclaredGlobalsInFunctions.set(s,n),o="gscope['"+n.manglename+"']",u=n.manglename}return u&&(a+="lang.chkAssig('"+u+"',runtimeCtx); \n"),a+="if ("+n+"===null) {  lastStatement = lc.voidOperation; }\n ",a+="else if (lc.isArray("+n+") || lc.isString("+n+")) {",a+="var "+r+"="+n+".length; \n",a+="for(var "+i+"=0; "+i+"<"+r+"; "+i+"++) {\n",a+=o+"="+i+";\n",a+=A(e,t.body),a+="\n}\n",a+=" lastStatement = lc.voidOperation; \n",a+=" \n}\n",a+="else if (lc.isImmutableArray("+n+")) {",a+="var "+r+"="+n+".length(); \n",a+="for(var "+i+"=0; "+i+"<"+r+"; "+i+"++) {\n",a+=o+"="+i+";\n",a+=A(e,t.body),a+="\n}\n",a+=" lastStatement = lc.voidOperation; \n",a+=" \n}\n",a+="else if (( "+n+" instanceof lang.Dictionary) || ( "+n+" instanceof lang.Feature)) {",a+="var "+r+"="+n+".keys(); \n",a+="for(var "+i+"=0; "+i+"<"+r+".length; "+i+"++) {\n",a+=o+"="+r+"["+i+"];\n",a+=A(e,t.body),a+="\n}\n",a+=" lastStatement = lc.voidOperation; \n",a+=" \n}\n",e.isAsync&&(a+="else if (lc.isFeatureSet("+n+")) {",a+="var "+r+"="+n+".iterator(runtimeCtx.abortSignal); \n",a+="for(var "+i+"=lang. graphicToFeature( yield "+r+".next(),"+n+", runtimeCtx); "+i+"!=null; "+i+"=lang. graphicToFeature( yield "+r+".next(),"+n+", runtimeCtx)) {\n",a+=o+"="+i+";\n",a+=A(e,t.body),a+="\n}\n",a+=" lastStatement = lc.voidOperation; \n",a+=" \n}\n"),a+="else { lastStatement = lc.voidOperation; } \n",a}(e,t);case"WhileStatement":return function(e,t){let n="lastStatement = lc.voidOperation; \n";const r=T(e);return n+=`\n  var ${r} = true;\n    do {\n      ${r} = ${A(e,t.test)};\n      if (${r}==false) {\n        break;\n      }\n      if (${r}!==true) {\n        lang.error('${x.rH.BooleanConditionRequired}');\n      }\n      ${A(e,t.body)}\n    }\n    while (${r} !== false);\n    lastStatement = lc.voidOperation;\n  `,n}(e,t);case"Identifier":return function(e,t){try{const n=t.name.toLowerCase();if(S(n),null!==e.localScope){if(void 0!==e.localScope[n])return"lscope['"+n+"']";if(void 0!==e.localScope._SymbolsMap[n])return"lscope['"+e.localScope._SymbolsMap[n]+"']"}if(void 0!==e.globalScope[n])return"gscope['"+n+"']";if(void 0!==e.globalScope._SymbolsMap[n])return"gscope['"+e.globalScope._SymbolsMap[n]+"']";if(null!==e.localScope){if(e.undeclaredGlobalsInFunctions.has(n))return"gscope[lang.chkAssig('"+e.undeclaredGlobalsInFunctions.get(n).manglename+"',runtimeCtx)]";const r={manglename:I(e),node:t.argument};return e.undeclaredGlobalsInFunctions.set(n,r),"gscope[lang.chkAssig('"+r.manglename+"',runtimeCtx)]"}throw new x.OF(e,x.rH.InvalidIdentifier,t)}catch(e){throw e}}(e,t);case"MemberExpression":return function(e,t){try{let n;return!0===t.computed?n=A(e,t.property):(n="'"+t.property.name+"'",S(t.property.name)),"lang.member("+A(e,t.object)+","+n+")"}catch(e){throw e}}(e,t);case"Literal":return null===t.value||void 0===t.value?"null":JSON.stringify(t.value);case"CallExpression":return function(e,t){try{if("MemberExpression"===t.callee.type){let n;!0===t.callee.computed?n=A(e,t.callee.property):(n="'"+t.callee.property.name+"'",S(t.callee.property.name));let r="[";for(let n=0;n<t.arguments.length;n++)n>0&&(r+=", "),r+=A(e,t.arguments[n]);return r+="]",e.isAsync?"(yield lang.callModuleFunction("+A(e,t.callee.object)+","+r+","+n+",runtimeCtx))":"lang.callModuleFunction("+A(e,t.callee.object)+","+r+","+n+",runtimeCtx)"}if("Identifier"!==t.callee.type)throw new x.OF(e,x.rH.FuncionNotFound,t);const n=t.callee.name.toLowerCase();if("iif"===n)return function(e,t){try{if(3!==t.arguments.length)throw new x.OF(e,x.rH.WrongNumberOfParameters,t);const n=T(e);return`${e.isAsync?"(yield (function() { \n return lang.__awaiter(this, void 0, void 0, function* () {":"function() {"}\n        var ${n} = ${A(e,t.arguments[0])};\n       \n        if (${n} === true) {\n          return  ${A(e,t.arguments[1])};\n        }\n        else if (${n} === false) {\n          return ${A(e,t.arguments[2])};\n        }\n        else {\n          lang.error('ExecutionErrorCodes.BooleanConditionRequired');\n        }\n      ${e.isAsync?"})}()))":"}()"}`}catch(e){throw e}}(e,t);if("when"===n)return function(e,t){try{if(t.arguments.length<3)throw new x.OF(e,x.rH.WrongNumberOfParameters,t);if(t.arguments.length%2==0)throw new x.OF(e,x.rH.WrongNumberOfParameters,t);const n=T(e);let r="var ";for(let i=0;i<t.arguments.length-1;i+=2)r+=`${n} = lang.mustBoolean(${A(e,t.arguments[i])}, runtimeCtx);\n      if (${n} === true ) {\n        return ${A(e,t.arguments[i+1])} \n      }\n`;return`${e.isAsync?"(yield (function() { \n return lang.__awaiter(this, void 0, void 0, function* () {":"function() {"}\n        ${r}\n        return ${A(e,t.arguments[t.arguments.length-1])}\n        ${e.isAsync?"})}()))":"}()"}`}catch(e){throw e}}(e,t);if("decode"===n)return function(e,t){try{if(t.arguments.length<2)throw new x.OF(e,x.rH.WrongNumberOfParameters,t);if(2===t.arguments.length)return`(${A(e,t.arguments[1])})`;if((t.arguments.length-1)%2==0)throw new x.OF(e,x.rH.WrongNumberOfParameters,t);const n=T(e),r=T(e);let i="var ";for(let a=1;a<t.arguments.length-1;a+=2)i+=`${r} = ${A(e,t.arguments[a])};\n      if (lang.binary(${r}, ${n}, "==") === true ) {\n        return ${A(e,t.arguments[a+1])} \n      }\n`;return`${e.isAsync?"(yield (function() { \n return lang.__awaiter(this, void 0, void 0, function* () {":"function() {"}\n        var ${n} = ${A(e,t.arguments[0])};\n        ${i}\n        return ${A(e,t.arguments[t.arguments.length-1])}\n        ${e.isAsync?"})}()))":"}()"}`}catch(e){throw e}}(e,t);let r="";if(null!==e.localScope&&(void 0!==e.localScope[n]?r="lscope['"+n+"']":void 0!==e.localScope._SymbolsMap[n]&&(r="lscope['"+e.localScope._SymbolsMap[n]+"']")),""===r)if(void 0!==e.globalScope[n])r="gscope['"+n+"']";else if(void 0!==e.globalScope._SymbolsMap[n])r="gscope['"+e.globalScope._SymbolsMap[n]+"']";else if(null!==e.localScope)if(e.undeclaredGlobalsInFunctions.has(n))r="gscope[lang.chkAssig('"+e.undeclaredGlobalsInFunctions.get(n).manglename+"',runtimeCtx)]";else{const i={manglename:I(e),node:t.argument};e.undeclaredGlobalsInFunctions.set(n,i),r="gscope[lang.chkAssig('"+i.manglename+"',runtimeCtx)]"}if(""!==r){let n="[";for(let r=0;r<t.arguments.length;r++)r>0&&(n+=", "),n+=A(e,t.arguments[r]);return n+="]",e.isAsync?"(yield lang.callfunc("+r+","+n+",runtimeCtx) )":"lang.callfunc("+r+","+n+",runtimeCtx)"}throw new x.OF(e,x.rH.FuncionNotFound,t)}catch(e){throw e}}(e,t);case"UnaryExpression":return function(e,t){try{return"lang.unary("+A(e,t.argument)+",'"+t.operator+"')"}catch(e){throw e}}(e,t);case"BinaryExpression":return function(e,t){try{return"lang.binary("+A(e,t.left)+","+A(e,t.right)+",'"+t.operator+"')"}catch(e){throw e}}(e,t);case"LogicalExpression":return function(e,t){try{if("AssignmentExpression"===t.left.type||"UpdateExpression"===t.left.type)throw new x.OF(e,x.rH.LogicalExpressionOnlyBoolean,t);if("AssignmentExpression"===t.right.type||"UpdateExpression"===t.right.type)throw new x.OF(e,x.rH.LogicalExpressionOnlyBoolean,t);if("&&"===t.operator||"||"===t.operator)return"(lang.logicalCheck("+A(e,t.left)+") "+t.operator+" lang.logicalCheck("+A(e,t.right)+"))";throw new x.OF(null,x.rH.LogicExpressionOrAnd,null)}catch(e){throw e}}(e,t);case"ArrayExpression":return function(e,t){try{const n=[];for(let r=0;r<t.elements.length;r++)"Literal"===t.elements[r].type?n.push(A(e,t.elements[r])):n.push("lang.aCheck("+A(e,t.elements[r])+",'ArrayExpression')");return"["+n.join(",")+"]"}catch(e){throw e}}(e,t);case"ObjectExpression":return function(e,t){let n="lang.dictionary([";for(let r=0;r<t.properties.length;r++){const i=t.properties[r];S(i.key.name),r>0&&(n+=","),n+="lang.strCheck("+("Identifier"===i.key.type?"'"+i.key.name+"'":A(e,i.key))+",'ObjectExpression'),lang.aCheck("+A(e,i.value)+", 'ObjectExpression')"}return n+="])",n}(e,t);case"Property":return function(e,t){throw new x.OF(e,x.rH.NeverReach,t)}(e,t);case"Array":throw new x.OF(e,x.rH.NeverReach,t);default:throw new x.OF(e,x.rH.Unrecognised,t)}}catch(e){throw e}}function b(e,t){return"BlockStatement"===t.type?A(e,t):"ReturnStatement"===t.type||"BreakStatement"===t.type||"ContinueStatement"===t.type?A(e,t)+"; ":"UpdateExpression"===t.type?"lastStatement = "+A(e,t)+"; ":"ExpressionStatement"===t.type?A(e,t):"ObjectExpression"===t.type?"lastStatement = "+A(e,t)+"; ":A(e,t)+"; "}function E(e,t){if("AssignmentExpression"===t.test.type||"UpdateExpression"===t.test.type)throw new x.OF(e,x.rH.BooleanConditionRequired,t);return`if (lang.mustBoolean(${A(e,t.test)}, runtimeCtx) === true) {\n    ${b(e,t.consequent)}\n  } `+(null!==t.alternate?"IfStatement"===t.alternate.type?" else "+E(e,t.alternate):` else {\n      ${b(e,t.alternate)}\n    }\n`:" else {\n      lastStatement = lc.voidOperation;\n    }\n")}function v(e,t){let n="";for(let r=0;r<t.body.length;r++)"EmptyStatement"!==t.body[r].type&&("ReturnStatement"===t.body[r].type||"BreakStatement"===t.body[r].type||"ContinueStatement"===t.body[r].type?n+=A(e,t.body[r])+"; \n":"UpdateExpression"===t.body[r].type||"ObjectExpression"===t.body[r].type?n+="lastStatement = "+A(e,t.body[r])+"; \n":n+=A(e,t.body[r])+" \n");return n}function S(e){if("iif"===e)throw new x.kq;if("decode"===e)throw new x.kq;if("when"===e)throw new x.kq}const k={};function I(e){return e.symbols.symbolCounter++,"_T"+e.symbols.symbolCounter.toString()}function T(e){return e.symbols.symbolCounter++,"_Tvar"+e.symbols.symbolCounter.toString()}(0,u.r)(k,C),(0,f.r)(k,C),(0,d.r)(k,C),(0,l.r)(k,C),(0,h.r)(k,C),k.iif=function(e,t){try{return C(e,t,((n,r,i)=>{throw new x.aV(e,x.rH.Unrecognised,t)}))}catch(e){throw e}},k.decode=function(e,t){try{return C(e,t,((n,r,i)=>{throw new x.aV(e,x.rH.Unrecognised,t)}))}catch(e){throw e}},k.when=function(e,t){try{return C(e,t,((n,r,i)=>{throw new x.aV(e,x.rH.Unrecognised,t)}))}catch(e){throw e}};const B={};for(const e in k)B[e]=new w.Bx(k[e]);(0,c.registerFunctions)(k,C);for(const e in k)k[e]=new w.Bx(k[e]);const _=function(){};_.prototype=k;const M=function(){};function N(e,t,n){const r={};e||(e={}),n||(n={}),r._SymbolsMap={},r.textformatting=1,r.infinity=1,r.pi=1;for(const e in t)r[e]=1;for(const e in n)r[e]=1;for(const t in e)r[t]=1;return r}function L(e,t,n,a){const s=n?new M:new _;e||(e={}),t||(t={});const o=new r.Z({newline:"\n",tab:"\t",singlequote:"'",doublequote:'"',forwardslash:"/",backwardslash:"\\"});o.immutable=!1,s._SymbolsMap={textformatting:1,infinity:1,pi:1},s.textformatting=o,s.infinity=Number.POSITIVE_INFINITY,s.pi=Math.PI;for(const e in t)s[e]=t[e],s._SymbolsMap[e]=1;for(const t in e)s._SymbolsMap[t]=1,e[t]&&"esri.Graphic"===e[t].declaredClass?s[t]=i.Z.createFromGraphic(e[t],a??null):s[t]=e[t];return s}function H(e,t){const n={mode:t,compiled:!0,functions:{},signatures:[],standardFunction:C,standardFunctionAsync:C,evaluateIdentifier:R};for(let t=0;t<e.length;t++)e[t].registerFunctions(n);if("sync"===t){for(const e in n.functions)k[e]=new w.Bx(n.functions[e]),_.prototype[e]=k[e];for(let e=0;e<n.signatures.length;e++)(0,s.gW)(n.signatures[e],"sync")}else{for(const e in n.functions)B[e]=new w.Bx(n.functions[e]),M.prototype[e]=B[e];for(let e=0;e<n.signatures.length;e++)(0,s.gW)(n.signatures[e],"async")}}function R(e,t){const n=t.name;if("_SymbolsMap"===n)throw new x.aV(e,x.rH.InvalidIdentifier,null);if(e.localStack.length>0){if("_t"!==n.substr(0,2).toLowerCase()&&void 0!==e.localStack[e.localStack.length-1][n])return e.localStack[e.localStack.length-1][n];const t=e.mangleMap[n];if(void 0!==t&&void 0!==e.localStack[e.localStack.length-1][t])return e.localStack[e.localStack.length-1][t]}if("_t"!==n.substr(0,2).toLowerCase()&&void 0!==e.globalScope[n])return e.globalScope[n];if(1===e.globalScope._SymbolsMap[n])return e.globalScope[n];const r=e.mangleMap[n];return void 0!==r?e.globalScope[r]:void 0}M.prototype=B,a.q,H([o.A],"sync"),H([o.A],"async");let O=0;const V={error(e){throw new x.aV(null,e,null)},__awaiter:(e,t,n,r)=>new Promise(((n,i)=>{function a(e){try{o(r.next(e))}catch(e){i(e)}}function s(e){try{o(r.throw(e))}catch(e){i(e)}}function o(e){e.done?n(e.value):e.value&&e.value.then?e.value.then(a,s):(O++,O%100==0?setTimeout((()=>{O=0,a(e.value)}),0):a(e.value))}o((r=r.apply(e,t||[])).next())})),functionDepthchecker:(e,t)=>function(){if(t.depthCounter.depth++,t.localStack.push([]),t.depthCounter.depth>64)throw new x.aV(null,x.rH.MaximumCallDepth,null);const n=e.apply(this,arguments);return(0,m.y8)(n)?n.then((e=>(t.depthCounter.depth--,t.localStack.length=t.localStack.length-1,e))):(t.depthCounter.depth--,t.localStack.length=t.localStack.length-1,n)},chkAssig(e,t){if(void 0===t.gdefs[e])throw new x.aV(t,x.rH.InvalidIdentifier,null);return e},mustBoolean(e,t){if(!0===e||!1===e)return e;throw new x.aV(t,x.rH.BooleanConditionRequired,null)},setAssig:(e,t)=>(t.gdefs[e]=1,e),castString:e=>(0,a.j)(e),aCheck(e,t){if((0,a.i)(e)){if("ArrayExpression"===t)throw new x.aV(null,x.rH.NoFunctionInArray,null);if("ObjectExpression"===t)throw new x.aV(null,x.rH.NoFunctionInDictionary,null);throw new x.aV(null,x.rH.NoFunctionInTemplateLiteral,null)}return e===a.v?null:e},Dictionary:r.Z,Feature:i.Z,UserDefinedCompiledFunction:F,dictionary(e){const t={},n=new Map;for(let r=0;r<e.length;r+=2){if((0,a.i)(e[r+1]))throw new x.aV(null,x.rH.NoFunctionInDictionary,null);if(!1===(0,a.c)(e[r]))throw new x.aV(null,x.rH.KeyMustBeString,null);let i=e[r].toString();const s=i.toLowerCase();n.has(s)?i=n.get(s):n.set(s,i),e[r+1]===a.v?t[i]=null:t[i]=e[r+1]}const i=new r.Z(t);return i.immutable=!1,i},strCheck(e){if(!1===(0,a.c)(e))throw new x.aV(null,x.rH.KeyMustBeString,null);return e},unary(e,t){if((0,a.a)(e)){if("!"===t)return!e;if("-"===t)return-1*(0,a.g)(e);if("+"===t)return 1*(0,a.g)(e);if("~"===t)return~(0,a.g)(e);throw new x.aV(null,x.rH.UnsupportedUnaryOperator,null)}if("-"===t)return-1*(0,a.g)(e);if("+"===t)return 1*(0,a.g)(e);if("~"===t)return~(0,a.g)(e);throw new x.aV(null,x.rH.UnsupportedUnaryOperator,null)},logicalCheck(e){if(!1===(0,a.a)(e))throw new x.aV(null,x.rH.LogicExpressionOrAnd,null);return e},logical(e,t,n){if((0,a.a)(e)&&(0,a.a)(t))switch(n){case"||":return e||t;case"&&":return e&&t;default:throw new x.aV(null,x.rH.LogicExpressionOrAnd,null)}throw new x.aV(null,x.rH.LogicExpressionOrAnd,null)},binary(e,t,n){switch(n){case"|":case"<<":case">>":case">>>":case"^":case"&":return(0,a.u)((0,a.g)(e),(0,a.g)(t),n);case"==":case"=":return(0,a.s)(e,t);case"!=":return!(0,a.s)(e,t);case"<":case">":case"<=":case">=":return(0,a.r)(e,t,n);case"+":return(0,a.c)(e)||(0,a.c)(t)?(0,a.j)(e)+(0,a.j)(t):(0,a.g)(e)+(0,a.g)(t);case"-":return(0,a.g)(e)-(0,a.g)(t);case"*":return(0,a.g)(e)*(0,a.g)(t);case"/":return(0,a.g)(e)/(0,a.g)(t);case"%":return(0,a.g)(e)%(0,a.g)(t);default:throw new x.aV(null,x.rH.UnsupportedOperator,null)}},assign(e,t,n){switch(t){case"=":return e===a.v?null:e;case"/=":return(0,a.g)(n)/(0,a.g)(e);case"*=":return(0,a.g)(n)*(0,a.g)(e);case"-=":return(0,a.g)(n)-(0,a.g)(e);case"+=":return(0,a.c)(n)||(0,a.c)(e)?(0,a.j)(n)+(0,a.j)(e):(0,a.g)(n)+(0,a.g)(e);case"%=":return(0,a.g)(n)%(0,a.g)(e);default:throw new x.aV(null,x.rH.UnsupportedOperator,null)}},update(e,t,n,r){const i=(0,a.g)(e[t]);return e[t]="++"===n?i+1:i-1,!1===r?i:"++"===n?i+1:i-1},graphicToFeature:(e,t,n)=>null===e?null:i.Z.createFromGraphicLikeObject(e.geometry,e.attributes,t,n.timeReference),memberupdate(e,t,n,i){let s;if((0,a.m)(e)){if(!(0,a.b)(t))throw new x.aV(null,x.rH.ArrayAccessorMustBeNumber,null);if(t<0&&(t=e.length+t),t<0||t>=e.length)throw new x.aV(null,x.rH.OutOfBounds,null);s=(0,a.g)(e[t]),e[t]="++"===n?s+1:s-1}else if(e instanceof r.Z){if(!1===(0,a.c)(t))throw new x.aV(null,x.rH.KeyAccessorMustBeString,null);if(!0!==e.hasField(t))throw new x.aV(null,x.rH.FieldNotFound,null,{key:t});s=(0,a.g)(e.field(t)),e.setField(t,"++"===n?s+1:s-1)}else if((0,a.w)(e)){if(!1===(0,a.c)(t))throw new x.aV(null,x.rH.KeyAccessorMustBeString,null);if(!0!==e.hasField(t))throw new x.aV(null,x.rH.FieldNotFound,null);s=(0,a.g)(e.field(t)),e.setField(t,"++"===n?s+1:s-1)}else{if((0,a.x)(e))throw new x.aV(null,x.rH.Immutable,null);if(!(e instanceof U))throw new x.aV(null,x.rH.InvalidIdentifier,null);if(!1===(0,a.c)(t))throw new x.aV(null,x.rH.ModuleAccessorMustBeString,null);if(!0!==e.hasGlobal(t))throw new x.aV(null,x.rH.ModuleExportNotFound,null);s=(0,a.g)(e.global(t)),e.setGlobal(t,"++"===n?s+1:s-1)}return!1===i?s:"++"===n?s+1:s-1},assignmember(e,t,n,i){if((0,a.m)(e)){if(!(0,a.b)(t))throw new x.aV(null,x.rH.ArrayAccessorMustBeNumber,null);if(t<0&&(t=e.length+t),t<0||t>e.length)throw new x.aV(null,x.rH.OutOfBounds,null);if(t===e.length){if("="!==n)throw new x.aV(null,x.rH.OutOfBounds,null);e[t]=this.assign(i,n,e[t])}else e[t]=this.assign(i,n,e[t])}else if(e instanceof r.Z){if(!1===(0,a.c)(t))throw new x.aV(null,x.rH.KeyAccessorMustBeString,null);if(!0===e.hasField(t))e.setField(t,this.assign(i,n,e.field(t)));else{if("="!==n)throw new x.aV(null,x.rH.FieldNotFound,null);e.setField(t,this.assign(i,n,null))}}else if((0,a.w)(e)){if(!1===(0,a.c)(t))throw new x.aV(null,x.rH.KeyAccessorMustBeString,null);if(!0===e.hasField(t))e.setField(t,this.assign(i,n,e.field(t)));else{if("="!==n)throw new x.aV(null,x.rH.FieldNotFound,null);e.setField(t,this.assign(i,n,null))}}else{if((0,a.x)(e))throw new x.aV(null,x.rH.Immutable,null);if(!(e instanceof U))throw new x.aV(null,x.rH.InvalidIdentifier,null);if(!1===(0,a.c)(t))throw new x.aV(null,x.rH.ModuleAccessorMustBeString,null);if(!e.hasGlobal(t))throw new x.aV(null,x.rH.ModuleExportNotFound,null);e.setGlobal(t,this.assign(i,n,e.global(t)))}},member(e,t){if(null===e)throw new x.aV(null,x.rH.MemberOfNull,null);if(e instanceof r.Z||(0,a.w)(e)){if((0,a.c)(t))return e.field(t);throw new x.aV(null,x.rH.InvalidMemberAccessKey,null)}if(e instanceof p.Z){if((0,a.c)(t))return(0,l.Z)(e,t,null,null);throw new x.aV(null,x.rH.InvalidMemberAccessKey,null)}if((0,a.m)(e)){if((0,a.b)(t)&&isFinite(t)&&Math.floor(t)===t){if(t<0&&(t=e.length+t),t>=e.length||t<0)throw new x.aV(null,x.rH.OutOfBounds,null);return e[t]}throw new x.aV(null,x.rH.InvalidMemberAccessKey,null)}if((0,a.c)(e)){if((0,a.b)(t)&&isFinite(t)&&Math.floor(t)===t){if(t<0&&(t=e.length+t),t>=e.length||t<0)throw new x.aV(null,x.rH.OutOfBounds,null);return e[t]}throw new x.aV(null,x.rH.InvalidMemberAccessKey,null)}if((0,a.x)(e)){if((0,a.b)(t)&&isFinite(t)&&Math.floor(t)===t){if(t<0&&(t=e.length()+t),t>=e.length()||t<0)throw new x.aV(null,x.rH.OutOfBounds,null);return e.get(t)}throw new x.aV(null,x.rH.InvalidMemberAccessKey,null)}if(e instanceof U){if((0,a.c)(t))return e.global(t);throw new x.aV(null,x.rH.InvalidMemberAccessKey,null)}throw new x.aV(null,x.rH.InvalidMemberAccessKey,null)},callfunc:(e,t,n)=>e.call(n,{arguments:t,preparsed:!0}),loadModule(e,t){const n=t.moduleFactoryMap[e];if(t.moduleSingletons[n])return t.moduleSingletons[n];const r=t.moduleFactory[n]({vars:{},moduleSingletons:t.moduleSingletons,depthCounter:t.depthCounter,console:t.console,abortSignal:t.abortSignal,isAsync:t.isAsync,services:t.services,lrucache:t.lrucache,timeReference:t.timeReference?t.timeReference:null,interceptor:t.interceptor},t.spatialReference);return t.moduleSingletons[n]=r,r},callModuleFunction(e,t,n,r){if(!(e instanceof U))throw new x.aV(null,x.rH.FuncionNotFound,null);const i=e.global(n);if(!1===(0,a.i)(i))throw new x.aV(null,x.rH.CallNonFunction,null);return i.call(r,{preparsed:!0,arguments:t})}};function P(e){console.log(e)}function Z(e,t,n=!1){null===t&&(t={vars:{},customfunctions:{}});let r=null;e.usesModules&&(r=new D.s(null,e.loadedModules));const i={isAsync:n,globalScope:N(t.vars,n?B:k,t.customfunctions),moduleFactory:{},moduleFactoryMap:{},undeclaredGlobalsInFunctions:new Map,customfunctions:t.customfunctions,libraryResolver:r,localScope:null,mangleMap:{},depthCounter:{depth:1},exports:{},console:P,lrucache:t.lrucache,timeReference:t.timeReference??null,interceptor:t.interceptor,services:t.services,symbols:{symbolCounter:0}};let s=A(i,e);""===s&&(s="lc.voidOperation; "),i.undeclaredGlobalsInFunctions.size>0&&i.undeclaredGlobalsInFunctions.forEach((e=>{throw new x.OF(t,x.rH.InvalidIdentifier,e.node)}));let o="";o=n?"var runtimeCtx=this.prepare(context, true);\n var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; \nreturn lang.__awaiter(this, void 0, void 0, function* () {\n\n function mainBody() {\n var lastStatement=lc.voidOperation;\n return lang.__awaiter(this, void 0, void 0, function* () {\n"+s+"\n return lastStatement; }); } \n return this.postProcess(yield mainBody()); }); ":"var runtimeCtx=this.prepare(context, false);\n var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; \n function mainBody() {\n var lastStatement=lc.voidOperation;\n "+s+"\n return lastStatement; } \n return this.postProcess(mainBody()); ";const u=i.moduleFactory,l=i.moduleFactoryMap,c=i.exports,d={};for(const e in c)d[e]=void 0!==i.mangleMap[e]?i.mangleMap[e]:e;const h={lc:a.n,lang:V,mangles:i.mangleMap,postProcess(e){if(e instanceof a.R&&(e=e.value),e instanceof a.I&&(e=e.value),e===a.v&&(e=null),e===a.o)throw new x.aV(null,x.rH.IllegalResult,null);if(e===a.p)throw new x.aV(null,x.rH.IllegalResult,null);if((0,a.i)(e))throw new x.aV(null,x.rH.IllegalResult,null);return e},prepare(e,t){let n=e.spatialReference;null==n&&(n=g.Z.WebMercator);const r=L(e.vars,e.customfunctions,t,e.timeReference);return{localStack:[],isAsync:t,moduleFactory:u,moduleFactoryMap:l,mangleMap:this.mangles,moduleSingletons:{},exports:c,gdefs:{},exportmangle:d,spatialReference:n,globalScope:r,abortSignal:void 0===e.abortSignal||null===e.abortSignal?{aborted:!1}:e.abortSignal,localScope:null,services:e.services,console:e.console?e.console:P,lrucache:e.lrucache,timeReference:e.timeReference??null,interceptor:e.interceptor,symbols:{symbolCounter:0},depthCounter:{depth:1}}}};return new Function("context","spatialReference",o).bind(h)}class U extends y.P{constructor(e){super(null),this.moduleContext=e}hasGlobal(e){return void 0===this.moduleContext.exports[e]&&(e=e.toLowerCase()),void 0!==this.moduleContext.exports[e]}setGlobal(e,t){const n=this.moduleContext.globalScope,r=e.toLowerCase();if((0,a.i)(t))throw new x.aV(null,x.rH.AssignModuleFunction,null);n[this.moduleContext.exportmangle[r]]=t}global(e){const t=this.moduleContext.globalScope;e=e.toLowerCase();const n=t[this.moduleContext.exportmangle[e]];if(void 0===n)throw new x.aV(null,x.rH.InvalidIdentifier,null);if((0,a.i)(n)&&!(n instanceof w.Vg)){const r=new w.Vg;return r.fn=n,r.parameterEvaluator=C,r.context=this.moduleContext,t[this.moduleContext.exportmangle[e]]=r,r}return n}}var G,z,q,j=n(3172),J=n(65587);(q=G||(G={})).Break="break",q.Continue="continue",q.Else="else",q.False="false",q.For="for",q.From="from",q.Function="function",q.If="if",q.Import="import",q.Export="export",q.In="in",q.Null="null",q.Return="return",q.True="true",q.Var="var",q.While="while",function(e){e.AssignmentExpression="AssignmentExpression",e.ArrayExpression="ArrayExpression",e.BlockComment="BlockComment",e.BlockStatement="BlockStatement",e.BinaryExpression="BinaryExpression",e.BreakStatement="BreakStatement",e.CallExpression="CallExpression",e.ContinueStatement="ContinueStatement",e.EmptyStatement="EmptyStatement",e.ExpressionStatement="ExpressionStatement",e.ExportNamedDeclaration="ExportNamedDeclaration",e.ExportSpecifier="ExportSpecifier",e.ForStatement="ForStatement",e.ForInStatement="ForInStatement",e.FunctionDeclaration="FunctionDeclaration",e.Identifier="Identifier",e.IfStatement="IfStatement",e.ImportDeclaration="ImportDeclaration",e.ImportDefaultSpecifier="ImportDefaultSpecifier",e.LineComment="LineComment",e.Literal="Literal",e.LogicalExpression="LogicalExpression",e.MemberExpression="MemberExpression",e.ObjectExpression="ObjectExpression",e.Program="Program",e.Property="Property",e.ReturnStatement="ReturnStatement",e.TemplateElement="TemplateElement",e.TemplateLiteral="TemplateLiteral",e.UnaryExpression="UnaryExpression",e.UpdateExpression="UpdateExpression",e.VariableDeclaration="VariableDeclaration",e.VariableDeclarator="VariableDeclarator",e.WhileStatement="WhileStatement"}(z||(z={}));const W=["++","--"],K=["-","+","!","~"],$=["=","/=","*=","%=","+=","-="],Y=["||","&&"],Q={"||":1,"&&":2,"|":3,"^":4,"&":5,"==":6,"!=":6,"<":7,">":7,"<=":7,">=":7,"<<":8,">>":8,">>>":8,"+":9,"-":9,"*":10,"/":10,"%":10};var X;!function(e){e[e.Unknown=0]="Unknown",e[e.BooleanLiteral=1]="BooleanLiteral",e[e.EOF=2]="EOF",e[e.Identifier=3]="Identifier",e[e.Keyword=4]="Keyword",e[e.NullLiteral=5]="NullLiteral",e[e.NumericLiteral=6]="NumericLiteral",e[e.Punctuator=7]="Punctuator",e[e.StringLiteral=8]="StringLiteral",e[e.Template=10]="Template"}(X||(X={}));const ee=["Unknown","Boolean","<end>","Identifier","Keyword","Null","Numeric","Punctuator","String","RegularExpression","Template"];var te;!function(e){e.InvalidModuleUri="InvalidModuleUri",e.ForInOfLoopInitializer="ForInOfLoopInitializer",e.IdentiferExpected="IdentiferExpected",e.InvalidEscapedReservedWord="InvalidEscapedReservedWord",e.InvalidExpression="InvalidExpression",e.InvalidFunctionIdentifier="InvalidFunctionIdentifier",e.InvalidHexEscapeSequence="InvalidHexEscapeSequence",e.InvalidLeftHandSideInAssignment="InvalidLeftHandSideInAssignment",e.InvalidLeftHandSideInForIn="InvalidLeftHandSideInForIn",e.InvalidTemplateHead="InvalidTemplateHead",e.InvalidVariableAssignment="InvalidVariableAssignment",e.KeyMustBeString="KeyMustBeString",e.NoFunctionInsideBlock="NoFunctionInsideBlock",e.NoFunctionInsideFunction="NoFunctionInsideFunction",e.ModuleExportRootOnly="ModuleExportRootOnly",e.ModuleImportRootOnly="ModuleImportRootOnly",e.PunctuatorExpected="PunctuatorExpected",e.TemplateOctalLiteral="TemplateOctalLiteral",e.UnexpectedBoolean="UnexpectedBoolean",e.UnexpectedEndOfScript="UnexpectedEndOfScript",e.UnexpectedIdentifier="UnexpectedIdentifier",e.UnexpectedKeyword="UnexpectedKeyword",e.UnexpectedNull="UnexpectedNull",e.UnexpectedNumber="UnexpectedNumber",e.UnexpectedPunctuator="UnexpectedPunctuator",e.UnexpectedString="UnexpectedString",e.UnexpectedTemplate="UnexpectedTemplate",e.UnexpectedToken="UnexpectedToken"}(te||(te={}));const ne={[te.InvalidModuleUri]:"Module uri must be a text literal.",[te.ForInOfLoopInitializer]:"for-in loop variable declaration may not have an initializer.",[te.IdentiferExpected]:"'${value}' is an invalid identifier.",[te.InvalidEscapedReservedWord]:"Keyword cannot contain escaped characters.",[te.InvalidExpression]:"Invalid expression.",[te.InvalidFunctionIdentifier]:"'${value}' is an invalid function identifier.",[te.InvalidHexEscapeSequence]:"Invalid hexadecimal escape sequence.",[te.InvalidLeftHandSideInAssignment]:"Invalid left-hand side in assignment.",[te.InvalidLeftHandSideInForIn]:"Invalid left-hand side in for-in.",[te.InvalidTemplateHead]:"Invalid template structure.",[te.InvalidVariableAssignment]:"Invalid variable assignment.",[te.KeyMustBeString]:"Object property keys must be a word starting with a letter.",[te.NoFunctionInsideBlock]:"Functions cannot be declared inside of code blocks.",[te.NoFunctionInsideFunction]:"Functions cannot be declared inside another function.",[te.ModuleExportRootOnly]:"Module exports cannot be declared inside of code blocks.",[te.ModuleImportRootOnly]:"Module import cannot be declared inside of code blocks.",[te.PunctuatorExpected]:"'${value}' expected.",[te.TemplateOctalLiteral]:"Octal literals are not allowed in template literals.",[te.UnexpectedBoolean]:"Unexpected boolean literal.",[te.UnexpectedEndOfScript]:"Unexpected end of Arcade expression.",[te.UnexpectedIdentifier]:"Unexpected identifier.",[te.UnexpectedKeyword]:"Unexpected keyword.",[te.UnexpectedNull]:"Unexpected null literal.",[te.UnexpectedNumber]:"Unexpected number.",[te.UnexpectedPunctuator]:"Unexpected ponctuator.",[te.UnexpectedString]:"Unexpected text literal.",[te.UnexpectedTemplate]:"Unexpected quasi '${value}'.",[te.UnexpectedToken]:"Unexpected token '${value}'."};class re extends Error{constructor({code:e,index:t,line:n,column:r,len:i=0,description:a,data:s}){super(`${a??e}`),this.declaredRootClass="esri.arcade.lib.parsingerror",this.name="ParsingError",this.code=e,this.index=t,this.line=n,this.column=r,this.len=i,this.data=s,this.description=a,this.range={start:{line:n,column:r-1},end:{line:n,column:r+i}},Error.captureStackTrace?.(this,re)}}function ie(e){return e?.type===z.BlockStatement}function ae(e){return e?.type===z.BlockComment}function se(e){return e?.type===z.EmptyStatement}function oe(e){return e?.type===z.VariableDeclarator}function ue(e,t){return!!t&&t.loc.end.line===e.loc.start.line&&t.loc.end.column<=e.loc.start.column}function le(e,t){return e.range[0]>=t.range[0]&&e.range[1]<=t.range[1]}class ce{constructor(){this.comments=[],this._nodeStack=[],this._newComments=[]}insertInnerComments(e){if(!ie(e)||0!==e.body.length)return;const t=[];for(let n=this._newComments.length-1;n>=0;--n){const r=this._newComments[n];e.range[1]>=r.range[0]&&(t.unshift(r),this._newComments.splice(n,1))}t.length&&(e.innerComments=t)}attachTrailingComments(e){if(!e)return;const t=this._nodeStack[this._nodeStack.length-1];if(ie(e)&&le(t,e))for(let n=this._newComments.length-1;n>=0;--n){const r=this._newComments[n];le(r,e)&&(t.trailingComments=[...t.trailingComments??[],r],this._newComments.splice(n,1))}let n=[];if(this._newComments.length>0)for(let r=this._newComments.length-1;r>=0;--r){const i=this._newComments[r];ue(i,t)?(t.trailingComments=[...t.trailingComments??[],i],this._newComments.splice(r,1)):ue(i,e)&&(n.unshift(i),this._newComments.splice(r,1))}t?.trailingComments&&ue(t.trailingComments[0],e)&&(n=[...n,...t.trailingComments],delete t.trailingComments),n.length>0&&(e.trailingComments=n)}attachLeadingComments(e){if(!e)return;let t;for(;this._nodeStack.length>0;){const n=this._nodeStack[this._nodeStack.length-1];if(!(e.range[0]<=n.range[0]))break;t=n,this._nodeStack.pop()}const n=[],r=[];if(t){for(let i=(t.leadingComments?.length??0)-1;i>=0;--i){const a=t.leadingComments[i];e.range[0]>=a.range[1]?(n.unshift(a),t.leadingComments.splice(i,1)):oe(e)&&!ae(a)&&(r.unshift(a),t.leadingComments.splice(i,1))}return 0===t.leadingComments?.length&&delete t.leadingComments,n.length&&(e.leadingComments=n),void(r.length&&(e.trailingComments=[...r,...e.trailingComments??[]]))}for(let t=this._newComments.length-1;t>=0;--t){const r=this._newComments[t];e.range[0]>=r.range[0]&&(n.unshift(r),this._newComments.splice(t,1))}n.length&&(e.leadingComments=n)}attachComments(e){if(function(e){return e?.type===z.Program}(e)&&e.body.length>0){const t=this._nodeStack[this._nodeStack.length-1];return t?(t.trailingComments=[...t.trailingComments??[],...this._newComments],this._newComments.length=0,void this._nodeStack.pop()):(e.trailingComments=[...this._newComments],void(this._newComments.length=0))}this.attachTrailingComments(e),this.attachLeadingComments(e),this.insertInnerComments(e),this._nodeStack.push(e)}collectComment(e){this.comments.push(e),this._newComments.push(e)}}function de(e,t){const n=ne[e];return t?n.replace(/\${(.*?)}/g,((e,n)=>t[n]?.toString()??"")):n}class he{constructor(e=!1){this.tolerant=e,this.errors=[]}recordError(e){this.errors.push(e)}tolerate(e){if(!this.tolerant)throw e;this.recordError(e)}throwError(e){throw e.description=e.description??de(e.code,e.data),new re(e)}tolerateError(e){e.description=e.description??de(e.code,e.data);const t=new re(e);if(!this.tolerant)throw t;this.recordError(t)}}function fe(e,t){if(!e)throw new Error("ASSERT: "+t)}const me={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEF\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7BF\uA7C2-\uA7C6\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB67\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDEC0-\uDEEB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05EF-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u07FD\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D3-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u09FE\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1878\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CD0-\u1CD2\u1CD4-\u1CFA\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEF\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7BF\uA7C2-\uA7C6\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB67\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD27\uDD30-\uDD39\uDF00-\uDF1C\uDF27\uDF30-\uDF50\uDFE0-\uDFF6]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD44-\uDD46\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDC9-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3B-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC5E\uDC5F\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDC00-\uDC3A\uDCA0-\uDCE9\uDCFF\uDDA0-\uDDA7\uDDAA-\uDDD7\uDDDA-\uDDE1\uDDE3\uDDE4\uDE00-\uDE3E\uDE47\uDE50-\uDE99\uDE9D\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD8E\uDD90\uDD91\uDD93-\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF6]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF4F-\uDF87\uDF8F-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDD00-\uDD2C\uDD30-\uDD3D\uDD40-\uDD49\uDD4E\uDEC0-\uDEF9]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4B\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/},pe={fromCodePoint:e=>e<65536?String.fromCharCode(e):String.fromCharCode(55296+(e-65536>>10))+String.fromCharCode(56320+(e-65536&1023)),isWhiteSpace:e=>32===e||9===e||11===e||12===e||160===e||e>=5760&&[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].includes(e),isLineTerminator:e=>10===e||13===e||8232===e||8233===e,isIdentifierStart:e=>36===e||95===e||e>=65&&e<=90||e>=97&&e<=122||92===e||e>=128&&me.NonAsciiIdentifierStart.test(pe.fromCodePoint(e)),isIdentifierPart:e=>36===e||95===e||e>=65&&e<=90||e>=97&&e<=122||e>=48&&e<=57||92===e||e>=128&&me.NonAsciiIdentifierPart.test(pe.fromCodePoint(e)),isDecimalDigit:e=>e>=48&&e<=57,isHexDigit:e=>e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102,isOctalDigit:e=>e>=48&&e<=55};function ge(e){return"0123456789abcdef".indexOf(e.toLowerCase())}function De(e){return"01234567".indexOf(e)}const ye=[[],[],[]];W.forEach((e=>ye[e.length-1].push(e))),K.forEach((e=>ye[e.length-1].push(e))),Y.forEach((e=>ye[e.length-1].push(e))),$.forEach((e=>ye[e.length-1].push(e))),["|","&",">>","<<",">>>","^","==","!=","<","<=",">",">=","+","-","*","/","%"].forEach((e=>ye[e.length-1].push(e)));class we{constructor(e,t){this.source=e,this.errorHandler=t,this._length=e.length,this.index=0,this.lineNumber=1,this.lineStart=0,this.curlyStack=[]}saveState(){return{index:this.index,lineNumber:this.lineNumber,lineStart:this.lineStart,curlyStack:this.curlyStack.slice()}}restoreState(e){this.index=e.index,this.lineNumber=e.lineNumber,this.lineStart=e.lineStart,this.curlyStack=e.curlyStack}eof(){return this.index>=this._length}throwUnexpectedToken(e=te.UnexpectedToken){this.errorHandler.throwError({code:e,index:this.index,line:this.lineNumber,column:this.index-this.lineStart+1})}tolerateUnexpectedToken(e=te.UnexpectedToken){this.errorHandler.tolerateError({code:e,index:this.index,line:this.lineNumber,column:this.index-this.lineStart+1})}skipSingleLineComment(e){const t=[],n=this.index-e,r={start:{line:this.lineNumber,column:this.index-this.lineStart-e},end:{line:0,column:0}};for(;!this.eof();){const i=this.source.charCodeAt(this.index);if(++this.index,pe.isLineTerminator(i)){if(r){r.end={line:this.lineNumber,column:this.index-this.lineStart-1};const i={multiLine:!1,start:n+e,end:this.index-1,range:[n,this.index-1],loc:r};t.push(i)}return 13===i&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,t}}if(r){r.end={line:this.lineNumber,column:this.index-this.lineStart};const i={multiLine:!1,start:n+e,end:this.index,range:[n,this.index],loc:r};t.push(i)}return t}skipMultiLineComment(){const e=[],t=this.index-2,n={start:{line:this.lineNumber,column:this.index-this.lineStart-2},end:{line:0,column:0}};for(;!this.eof();){const r=this.source.charCodeAt(this.index);if(pe.isLineTerminator(r))13===r&&10===this.source.charCodeAt(this.index+1)&&++this.index,++this.lineNumber,++this.index,this.lineStart=this.index;else if(42===r){if(47===this.source.charCodeAt(this.index+1)){if(this.index+=2,n){n.end={line:this.lineNumber,column:this.index-this.lineStart};const r={multiLine:!0,start:t+2,end:this.index-2,range:[t,this.index],loc:n};e.push(r)}return e}++this.index}else++this.index}if(n){n.end={line:this.lineNumber,column:this.index-this.lineStart};const r={multiLine:!0,start:t+2,end:this.index,range:[t,this.index],loc:n};e.push(r)}return this.tolerateUnexpectedToken(),e}scanComments(){let e=[];for(;!this.eof();){let t=this.source.charCodeAt(this.index);if(pe.isWhiteSpace(t))++this.index;else if(pe.isLineTerminator(t))++this.index,13===t&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index;else{if(47!==t)break;if(t=this.source.charCodeAt(this.index+1),47===t){this.index+=2;const t=this.skipSingleLineComment(2);e=[...e,...t]}else{if(42!==t)break;{this.index+=2;const t=this.skipMultiLineComment();e=[...e,...t]}}}}return e}isKeyword(e){switch((e=e.toLowerCase()).length){case 2:return e===G.If||e===G.In;case 3:return e===G.Var||e===G.For;case 4:return e===G.Else;case 5:return e===G.Break||e===G.While;case 6:return e===G.Return||e===G.Import||e===G.Export;case 8:return e===G.Function||e===G.Continue;default:return!1}}codePointAt(e){let t=this.source.charCodeAt(e);if(t>=55296&&t<=56319){const n=this.source.charCodeAt(e+1);n>=56320&&n<=57343&&(t=1024*(t-55296)+n-56320+65536)}return t}scanHexEscape(e){const t="u"===e?4:2;let n=0;for(let e=0;e<t;++e){if(this.eof()||!pe.isHexDigit(this.source.charCodeAt(this.index)))return null;n=16*n+ge(this.source[this.index++])}return String.fromCharCode(n)}scanUnicodeCodePointEscape(){let e=this.source[this.index],t=0;for("}"===e&&this.throwUnexpectedToken();!this.eof()&&(e=this.source[this.index++],pe.isHexDigit(e.charCodeAt(0)));)t=16*t+ge(e);return(t>1114111||"}"!==e)&&this.throwUnexpectedToken(),pe.fromCodePoint(t)}getIdentifier(){const e=this.index++;for(;!this.eof();){const t=this.source.charCodeAt(this.index);if(92===t)return this.index=e,this.getComplexIdentifier();if(t>=55296&&t<57343)return this.index=e,this.getComplexIdentifier();if(!pe.isIdentifierPart(t))break;++this.index}return this.source.slice(e,this.index)}getComplexIdentifier(){let e,t=this.codePointAt(this.index),n=pe.fromCodePoint(t);for(this.index+=n.length,92===t&&(117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,e=this.scanUnicodeCodePointEscape()):(e=this.scanHexEscape("u"),null!==e&&"\\"!==e&&pe.isIdentifierStart(e.charCodeAt(0))||this.throwUnexpectedToken()),n=e);!this.eof()&&(t=this.codePointAt(this.index),pe.isIdentifierPart(t));)e=pe.fromCodePoint(t),n+=e,this.index+=e.length,92===t&&(n=n.substring(0,n.length-1),117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,e=this.scanUnicodeCodePointEscape()):(e=this.scanHexEscape("u"),null!==e&&"\\"!==e&&pe.isIdentifierPart(e.charCodeAt(0))||this.throwUnexpectedToken()),n+=e);return n}octalToDecimal(e){let t="0"!==e,n=De(e);return!this.eof()&&pe.isOctalDigit(this.source.charCodeAt(this.index))&&(t=!0,n=8*n+De(this.source[this.index++]),"0123".includes(e)&&!this.eof()&&pe.isOctalDigit(this.source.charCodeAt(this.index))&&(n=8*n+De(this.source[this.index++]))),{code:n,octal:t}}scanIdentifier(){let e;const t=this.index,n=92===this.source.charCodeAt(t)?this.getComplexIdentifier():this.getIdentifier();if(e=1===n.length?X.Identifier:this.isKeyword(n)?X.Keyword:n.toLowerCase()===G.Null?X.NullLiteral:n.toLowerCase()===G.True||n.toLowerCase()===G.False?X.BooleanLiteral:X.Identifier,e!==X.Identifier&&t+n.length!==this.index){const e=this.index;this.index=t,this.tolerateUnexpectedToken(te.InvalidEscapedReservedWord),this.index=e}return{type:e,value:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}}scanPunctuator(){const e=this.index;let t=this.source[this.index];switch(t){case"(":case"{":"{"===t&&this.curlyStack.push("{"),++this.index;break;case".":case")":case";":case",":case"[":case"]":case":":case"?":case"~":++this.index;break;case"}":++this.index,this.curlyStack.pop();break;default:for(let e=ye.length;e>0;e--)if(t=this.source.substring(this.index,this.index+e),ye[e-1].includes(t)){this.index+=e;break}}return this.index===e&&this.throwUnexpectedToken(),{type:X.Punctuator,value:t,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}}scanHexLiteral(e){let t="";for(;!this.eof()&&pe.isHexDigit(this.source.charCodeAt(this.index));)t+=this.source[this.index++];return 0===t.length&&this.throwUnexpectedToken(),pe.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:X.NumericLiteral,value:parseInt("0x"+t,16),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}}scanBinaryLiteral(e){let t="";for(;!this.eof();){const e=this.source[this.index];if("0"!==e&&"1"!==e)break;t+=this.source[this.index++]}if(0===t.length&&this.throwUnexpectedToken(),!this.eof()){const e=this.source.charCodeAt(this.index);(pe.isIdentifierStart(e)||pe.isDecimalDigit(e))&&this.throwUnexpectedToken()}return{type:X.NumericLiteral,value:parseInt(t,2),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}}scanOctalLiteral(e,t){let n="",r=!1;for(pe.isOctalDigit(e.charCodeAt(0))?(r=!0,n="0"+this.source[this.index++]):++this.index;!this.eof()&&pe.isOctalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];return r||0!==n.length||this.throwUnexpectedToken(),(pe.isIdentifierStart(this.source.charCodeAt(this.index))||pe.isDecimalDigit(this.source.charCodeAt(this.index)))&&this.throwUnexpectedToken(),{type:X.NumericLiteral,value:parseInt(n,8),lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}}scanNumericLiteral(){const e=this.index;let t=this.source[e];fe(pe.isDecimalDigit(t.charCodeAt(0))||"."===t,"Numeric literal must start with a decimal digit or a decimal point");let n="";if("."!==t){if(n=this.source[this.index++],t=this.source[this.index],"0"===n){if("x"===t||"X"===t)return++this.index,this.scanHexLiteral(e);if("b"===t||"B"===t)return++this.index,this.scanBinaryLiteral(e);if("o"===t||"O"===t)return this.scanOctalLiteral(t,e)}for(;pe.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];t=this.source[this.index]}if("."===t){for(n+=this.source[this.index++];pe.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];t=this.source[this.index]}if("e"===t||"E"===t)if(n+=this.source[this.index++],t=this.source[this.index],"+"!==t&&"-"!==t||(n+=this.source[this.index++]),pe.isDecimalDigit(this.source.charCodeAt(this.index)))for(;pe.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];else this.throwUnexpectedToken();return pe.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:X.NumericLiteral,value:parseFloat(n),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}}scanStringLiteral(){const e=this.index;let t=this.source[e];fe("'"===t||'"'===t,"String literal must starts with a quote"),++this.index;let n=!1,r="";for(;!this.eof();){let e=this.source[this.index++];if(e===t){t="";break}if("\\"===e)if(e=this.source[this.index++],e&&pe.isLineTerminator(e.charCodeAt(0)))++this.lineNumber,"\r"===e&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(e){case"u":if("{"===this.source[this.index])++this.index,r+=this.scanUnicodeCodePointEscape();else{const t=this.scanHexEscape(e);null===t&&this.throwUnexpectedToken(),r+=t}break;case"x":{const t=this.scanHexEscape(e);null===t&&this.throwUnexpectedToken(te.InvalidHexEscapeSequence),r+=t;break}case"n":r+="\n";break;case"r":r+="\r";break;case"t":r+="\t";break;case"b":r+="\b";break;case"f":r+="\f";break;case"v":r+="\v";break;case"8":case"9":r+=e,this.tolerateUnexpectedToken();break;default:if(e&&pe.isOctalDigit(e.charCodeAt(0))){const t=this.octalToDecimal(e);n=t.octal||n,r+=String.fromCharCode(t.code)}else r+=e}else{if(pe.isLineTerminator(e.charCodeAt(0)))break;r+=e}}return""!==t&&(this.index=e,this.throwUnexpectedToken()),{type:X.StringLiteral,value:r,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}}scanTemplate(){let e="",t=!1;const n=this.index,r="`"===this.source[n];let i=!1,a=2;for(++this.index;!this.eof();){let n=this.source[this.index++];if("`"===n){a=1,i=!0,t=!0;break}if("$"!==n)if("\\"!==n)pe.isLineTerminator(n.charCodeAt(0))?(++this.lineNumber,"\r"===n&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index,e+="\n"):e+=n;else if(n=this.source[this.index++],pe.isLineTerminator(n.charCodeAt(0)))++this.lineNumber,"\r"===n&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(n){case"n":e+="\n";break;case"r":e+="\r";break;case"t":e+="\t";break;case"u":if("{"===this.source[this.index])++this.index,e+=this.scanUnicodeCodePointEscape();else{const t=this.index,r=this.scanHexEscape(n);null!==r?e+=r:(this.index=t,e+=n)}break;case"x":{const t=this.scanHexEscape(n);null===t&&this.throwUnexpectedToken(te.InvalidHexEscapeSequence),e+=t;break}case"b":e+="\b";break;case"f":e+="\f";break;case"v":e+="\v";break;default:"0"===n?(pe.isDecimalDigit(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(te.TemplateOctalLiteral),e+="\0"):pe.isOctalDigit(n.charCodeAt(0))?this.throwUnexpectedToken(te.TemplateOctalLiteral):e+=n}else{if("{"===this.source[this.index]){this.curlyStack.push("${"),++this.index,t=!0;break}e+=n}}return t||this.throwUnexpectedToken(),r||this.curlyStack.pop(),{type:X.Template,value:this.source.slice(n+1,this.index-a),cooked:e,head:r,tail:i,lineNumber:this.lineNumber,lineStart:this.lineStart,start:n,end:this.index}}lex(){if(this.eof())return{type:X.EOF,value:"",lineNumber:this.lineNumber,lineStart:this.lineStart,start:this.index,end:this.index};const e=this.source.charCodeAt(this.index);return pe.isIdentifierStart(e)?this.scanIdentifier():40===e||41===e||59===e?this.scanPunctuator():39===e||34===e?this.scanStringLiteral():46===e?pe.isDecimalDigit(this.source.charCodeAt(this.index+1))?this.scanNumericLiteral():this.scanPunctuator():pe.isDecimalDigit(e)?this.scanNumericLiteral():96===e||125===e&&"${"===this.curlyStack[this.curlyStack.length-1]?this.scanTemplate():e>=55296&&e<57343&&pe.isIdentifierStart(this.codePointAt(this.index))?this.scanIdentifier():this.scanPunctuator()}}var xe,Fe,Ce;function Ae(e,t=0){let n=e.start-e.lineStart,r=e.lineNumber;return n<0&&(n+=t,r--),{index:e.start,line:r,column:n}}function be(e){return[{index:e.range[0],...e.loc.start},{index:e.range[1],...e.loc.end}]}function Ee(e){return Q[e]??0}(Ce=xe||(xe={}))[Ce.None=0]="None",Ce[Ce.Function=1]="Function",Ce[Ce.IfClause=2]="IfClause",Ce[Ce.ForLoop=4]="ForLoop",Ce[Ce.WhileLoop=8]="WhileLoop",function(e){e[e.AsObject=0]="AsObject",e[e.Automatic=1]="Automatic"}(Fe||(Fe={}));class ve{constructor(e,t={},n){this.delegate=n,this.hasLineTerminator=!1,this.options={tokens:"boolean"==typeof t.tokens&&t.tokens,comments:"boolean"==typeof t.comments&&t.comments,tolerant:"boolean"==typeof t.tolerant&&t.tolerant},this.options.comments&&(this.commentHandler=new ce),this.errorHandler=new he(this.options.tolerant),this.scanner=new we(e,this.errorHandler),this.context={isAssignmentTarget:!1,blockContext:xe.None,curlyParsingType:Fe.AsObject},this.rawToken={type:X.EOF,value:"",lineNumber:this.scanner.lineNumber,lineStart:0,start:0,end:0},this.tokens=[],this.startMarker={index:0,line:this.scanner.lineNumber,column:0},this.endMarker={index:0,line:this.scanner.lineNumber,column:0},this.readNextRawToken(),this.endMarker={index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}throwIfInvalidType(e,t,{validTypes:n,invalidTypes:r}){n?.some((t=>e.type===t))||r?.some((t=>e.type===t))&&this.throwError(te.InvalidExpression,t)}throwError(e,t,n=this.endMarker){const{index:r,line:i,column:a}=t,s=n.index-r-1;this.errorHandler.throwError({code:e,index:r,line:i,column:a+1,len:s})}tolerateError(e,t){throw new Error("######################################### !!!")}unexpectedTokenError(e={}){const{rawToken:t}=e;let n,{code:r,data:i}=e;if(t){if(!r)switch(t.type){case X.EOF:r=te.UnexpectedEndOfScript;break;case X.Identifier:r=te.UnexpectedIdentifier;break;case X.NumericLiteral:r=te.UnexpectedNumber;break;case X.StringLiteral:r=te.UnexpectedString;break;case X.Template:r=te.UnexpectedTemplate}n=t.value.toString()}else n="ILLEGAL";r=r??te.UnexpectedToken,i||(i={value:n});const a=de(r,i);if(t){const e=t.start,n=t.lineNumber,s=t.start-t.lineStart+1;return new re({code:r,index:e,line:n,column:s,len:t.end-t.start-1,data:i,description:a})}const{index:s,line:o}=this.endMarker;return new re({code:r,index:s,line:o,column:this.endMarker.column+1,data:i,description:a})}throwUnexpectedToken(e={}){throw e.rawToken=e.rawToken??this.rawToken,this.unexpectedTokenError(e)}collectComments(e){const{commentHandler:t}=this;t&&e.length&&e.forEach((e=>{const n={type:e.multiLine?z.BlockComment:z.LineComment,value:this.getSourceValue(e),range:e.range,loc:e.loc};t.collectComment(n)}))}peekAhead(e){const t=this.scanner.saveState(),n=e.call(this,(()=>(this.scanner.scanComments(),this.scanner.lex())));return this.scanner.restoreState(t),n}getSourceValue(e){return this.scanner.source.slice(e.start,e.end)}convertToToken(e){return{type:ee[e.type],value:this.getSourceValue(e),range:[e.start,e.end],loc:{start:{line:this.startMarker.line,column:this.startMarker.column},end:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}}}readNextRawToken(){this.endMarker.index=this.scanner.index,this.endMarker.line=this.scanner.lineNumber,this.endMarker.column=this.scanner.index-this.scanner.lineStart;const e=this.rawToken;this.collectComments(this.scanner.scanComments()),this.scanner.index!==this.startMarker.index&&(this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart),this.rawToken=this.scanner.lex(),this.hasLineTerminator=e.lineNumber!==this.rawToken.lineNumber,this.options.tokens&&this.rawToken.type!==X.EOF&&this.tokens.push(this.convertToToken(this.rawToken))}captureStartMarker(){return{index:this.startMarker.index,line:this.startMarker.line,column:this.startMarker.column}}getItemLocation(e){return{range:[e.index,this.endMarker.index],loc:{start:{line:e.line,column:e.column},end:{line:this.endMarker.line,column:this.endMarker.column}}}}finalize(e){return(this.delegate||this.commentHandler)&&(this.commentHandler?.attachComments(e),this.delegate?.(e)),e}expectPunctuator(e){const t=this.rawToken;this.matchPunctuator(e)?this.readNextRawToken():this.throwUnexpectedToken({rawToken:t,code:te.PunctuatorExpected,data:{value:e}})}expectKeyword(e){this.rawToken.type!==X.Keyword||this.rawToken.value.toLowerCase()!==e?this.throwUnexpectedToken({rawToken:this.rawToken}):this.readNextRawToken()}expectContextualKeyword(e){this.rawToken.type!==X.Identifier||this.rawToken.value.toLowerCase()!==e?this.throwUnexpectedToken({rawToken:this.rawToken}):this.readNextRawToken()}matchKeyword(e){return this.rawToken.type===X.Keyword&&this.rawToken.value.toLowerCase()===e}matchContextualKeyword(e){return this.rawToken.type===X.Identifier&&this.rawToken.value===e}matchPunctuator(e){return this.rawToken.type===X.Punctuator&&this.rawToken.value===e}getMatchingPunctuator(e){if("string"==typeof e&&(e=e.split("")),this.rawToken.type===X.Punctuator&&e?.length)return e.find(this.matchPunctuator,this)}isolateCoverGrammar(e){const t=this.context.isAssignmentTarget;this.context.isAssignmentTarget=!0;const n=e.call(this);return this.context.isAssignmentTarget=t,n}inheritCoverGrammar(e){const t=this.context.isAssignmentTarget;this.context.isAssignmentTarget=!0;const n=e.call(this);return this.context.isAssignmentTarget=this.context.isAssignmentTarget&&t,n}withBlockContext(e,t){const n=this.context.blockContext;this.context.blockContext=this.context.blockContext|e;const r=this.context.curlyParsingType;this.context.curlyParsingType=Fe.Automatic;const i=t.call(this);return this.context.blockContext=n,this.context.curlyParsingType=r,i}consumeSemicolon(){if(this.matchPunctuator(";"))this.readNextRawToken();else if(!this.hasLineTerminator)return this.rawToken.type===X.EOF||this.matchPunctuator("}")?(this.endMarker.index=this.startMarker.index,this.endMarker.line=this.startMarker.line,void(this.endMarker.column=this.startMarker.column)):void this.throwUnexpectedToken({rawToken:this.rawToken})}parsePrimaryExpression(){const e=this.captureStartMarker(),t=this.rawToken;switch(t.type){case X.Identifier:return this.readNextRawToken(),this.finalize({type:z.Identifier,name:t.value,...this.getItemLocation(e)});case X.NumericLiteral:case X.StringLiteral:return this.context.isAssignmentTarget=!1,this.readNextRawToken(),this.finalize({type:z.Literal,value:t.value,raw:this.getSourceValue(t),isString:"string"==typeof t.value,...this.getItemLocation(e)});case X.BooleanLiteral:return this.context.isAssignmentTarget=!1,this.readNextRawToken(),this.finalize({type:z.Literal,value:t.value.toLowerCase()===G.True,raw:this.getSourceValue(t),isString:!1,...this.getItemLocation(e)});case X.NullLiteral:return this.context.isAssignmentTarget=!1,this.readNextRawToken(),this.finalize({type:z.Literal,value:null,raw:this.getSourceValue(t),isString:!1,...this.getItemLocation(e)});case X.Template:return this.parseTemplateLiteral();case X.Punctuator:switch(t.value){case"(":return this.inheritCoverGrammar(this.parseGroupExpression);case"[":return this.inheritCoverGrammar(this.parseArrayInitializer);case"{":return this.inheritCoverGrammar(this.parseObjectExpression);default:return this.throwUnexpectedToken({rawToken:this.rawToken})}case X.Keyword:return this.context.isAssignmentTarget=!1,this.throwUnexpectedToken({rawToken:this.rawToken});default:return this.throwUnexpectedToken({rawToken:this.rawToken})}}parseArrayInitializer(){const e=this.captureStartMarker();this.expectPunctuator("[");const t=[];for(;!this.matchPunctuator("]");){const e=this.captureStartMarker();this.matchPunctuator(",")?(this.readNextRawToken(),this.throwError(te.InvalidExpression,e)):(t.push(this.inheritCoverGrammar(this.parseAssignmentExpression)),this.matchPunctuator("]")||this.expectPunctuator(","))}return this.expectPunctuator("]"),this.finalize({type:z.ArrayExpression,elements:t,...this.getItemLocation(e)})}parseObjectPropertyKey(){const e=this.captureStartMarker(),t=this.rawToken;switch(t.type){case X.StringLiteral:return this.readNextRawToken(),this.finalize({type:z.Literal,value:t.value,raw:this.getSourceValue(t),isString:!0,...this.getItemLocation(e)});case X.Identifier:case X.BooleanLiteral:case X.NullLiteral:case X.Keyword:return this.readNextRawToken(),this.finalize({type:z.Identifier,name:t.value,...this.getItemLocation(e)});default:this.throwError(te.KeyMustBeString,e)}}parseObjectProperty(){const e=this.rawToken,t=this.captureStartMarker(),n=this.parseObjectPropertyKey();let r=!1,i=null;return this.matchPunctuator(":")?(this.readNextRawToken(),i=this.inheritCoverGrammar(this.parseAssignmentExpression)):e.type===X.Identifier?(r=!0,i=this.finalize({type:z.Identifier,name:e.value,...this.getItemLocation(t)})):this.throwUnexpectedToken({rawToken:this.rawToken}),this.finalize({type:z.Property,kind:"init",key:n,value:i,shorthand:r,...this.getItemLocation(t)})}parseObjectExpression(){const e=this.captureStartMarker();this.expectPunctuator("{");const t=[];for(;!this.matchPunctuator("}");)t.push(this.parseObjectProperty()),this.matchPunctuator("}")||this.expectPunctuator(",");return this.expectPunctuator("}"),this.finalize({type:z.ObjectExpression,properties:t,...this.getItemLocation(e)})}parseTemplateElement(e=!1){const t=this.rawToken;t.type!==X.Template&&this.throwUnexpectedToken({rawToken:t}),e&&!t.head&&this.throwUnexpectedToken({code:te.InvalidTemplateHead,rawToken:t});const n=this.captureStartMarker();this.readNextRawToken();const{value:r,cooked:i,tail:a}=t,s=this.finalize({type:z.TemplateElement,value:{raw:r,cooked:i},tail:a,...this.getItemLocation(n)});return s.loc.start.column++,s.loc.end.column=s.loc.end.column-(a?1:2),s}parseTemplateLiteral(){const e=this.captureStartMarker(),t=[],n=[];let r=this.parseTemplateElement(!0);for(n.push(r);!r.tail;)t.push(this.parseExpression()),r=this.parseTemplateElement(),n.push(r);return this.finalize({type:z.TemplateLiteral,quasis:n,expressions:t,...this.getItemLocation(e)})}parseGroupExpression(){this.expectPunctuator("(");const e=this.inheritCoverGrammar(this.parseAssignmentExpression);return this.expectPunctuator(")"),e}parseArguments(){this.expectPunctuator("(");const e=[];if(!this.matchPunctuator(")"))for(;;){const t=this.isolateCoverGrammar(this.parseAssignmentExpression);if(e.push(t),this.matchPunctuator(")"))break;if(this.expectPunctuator(","),this.matchPunctuator(")"))break}return this.expectPunctuator(")"),e}parseMemberName(){const e=this.rawToken,t=this.captureStartMarker();return this.readNextRawToken(),e.type!==X.NullLiteral&&e.type!==X.Identifier&&e.type!==X.Keyword&&e.type!==X.BooleanLiteral&&this.throwUnexpectedToken({rawToken:e}),this.finalize({type:z.Identifier,name:e.value,...this.getItemLocation(t)})}parseLeftHandSideExpression(){const e=this.captureStartMarker();let t=this.inheritCoverGrammar(this.parsePrimaryExpression);const n=this.captureStartMarker();let r;for(;r=this.getMatchingPunctuator("([.");)switch(r){case"(":{this.context.isAssignmentTarget=!1,t.type!==z.Identifier&&t.type!==z.MemberExpression&&this.throwError(te.IdentiferExpected,e,n);const r=this.parseArguments();t=this.finalize({type:z.CallExpression,callee:t,arguments:r,...this.getItemLocation(e)});continue}case"[":{this.context.isAssignmentTarget=!0,this.expectPunctuator("[");const n=this.isolateCoverGrammar(this.parseExpression);this.expectPunctuator("]"),t=this.finalize({type:z.MemberExpression,computed:!0,object:t,property:n,...this.getItemLocation(e)});continue}case".":{this.context.isAssignmentTarget=!0,this.expectPunctuator(".");const n=this.parseMemberName();t=this.finalize({type:z.MemberExpression,computed:!1,object:t,property:n,...this.getItemLocation(e)});continue}}return t}parseUpdateExpression(){const e=this.captureStartMarker();let t=this.getMatchingPunctuator(W);if(t){this.readNextRawToken();const n=this.captureStartMarker(),r=this.inheritCoverGrammar(this.parseUnaryExpression);return r.type!==z.Identifier&&r.type!==z.MemberExpression&&r.type!==z.CallExpression&&this.throwError(te.InvalidExpression,n),this.context.isAssignmentTarget||this.tolerateError(te.InvalidLeftHandSideInAssignment,e),this.context.isAssignmentTarget=!1,this.finalize({type:z.UpdateExpression,operator:t,argument:r,prefix:!0,...this.getItemLocation(e)})}const n=this.captureStartMarker(),r=this.inheritCoverGrammar(this.parseLeftHandSideExpression),i=this.captureStartMarker();return this.hasLineTerminator?r:(t=this.getMatchingPunctuator(W),t?(r.type!==z.Identifier&&r.type!==z.MemberExpression&&this.throwError(te.InvalidExpression,n,i),this.context.isAssignmentTarget||this.tolerateError(te.InvalidLeftHandSideInAssignment,e),this.readNextRawToken(),this.context.isAssignmentTarget=!1,this.finalize({type:z.UpdateExpression,operator:t,argument:r,prefix:!1,...this.getItemLocation(e)})):r)}parseUnaryExpression(){const e=this.getMatchingPunctuator(K);if(e){const t=this.captureStartMarker();this.readNextRawToken();const n=this.inheritCoverGrammar(this.parseUnaryExpression);return this.context.isAssignmentTarget=!1,this.finalize({type:z.UnaryExpression,operator:e,argument:n,prefix:!0,...this.getItemLocation(t)})}return this.parseUpdateExpression()}parseBinaryExpression(){const e=this.rawToken;let t=this.inheritCoverGrammar(this.parseUnaryExpression);if(this.rawToken.type!==X.Punctuator)return t;const n=this.rawToken.value;let r=Ee(n);if(0===r)return t;this.readNextRawToken(),this.context.isAssignmentTarget=!1;const i=[e,this.rawToken];let a=t,s=this.inheritCoverGrammar(this.parseUnaryExpression);const o=[a,n,s],u=[r];for(;this.rawToken.type===X.Punctuator&&(r=Ee(this.rawToken.value))>0;){for(;o.length>2&&r<=u[u.length-1];){s=o.pop();const e=o.pop();u.pop(),a=o.pop(),i.pop();const t=i[i.length-1],n=Ae(t,t.lineStart);o.push(this.finalize(this.createBinaryOrLogicalExpression(n,e,a,s)))}o.push(this.rawToken.value),u.push(r),i.push(this.rawToken),this.readNextRawToken(),o.push(this.inheritCoverGrammar(this.parseUnaryExpression))}let l=o.length-1;t=o[l];let c=i.pop();for(;l>1;){const e=i.pop();if(!e)break;const n=c?.lineStart,r=Ae(e,n),a=o[l-1];t=this.finalize(this.createBinaryOrLogicalExpression(r,a,o[l-2],t)),l-=2,c=e}return t}createBinaryOrLogicalExpression(e,t,n,r){const i=Y.includes(t)?z.LogicalExpression:z.BinaryExpression;return i===z.BinaryExpression||(n.type!==z.AssignmentExpression&&n.type!==z.UpdateExpression||this.throwError(te.InvalidExpression,...be(n)),r.type!==z.AssignmentExpression&&r.type!==z.UpdateExpression||this.throwError(te.InvalidExpression,...be(n))),{type:i,operator:t,left:n,right:r,...this.getItemLocation(e)}}parseAssignmentExpression(){const e=this.captureStartMarker(),t=this.inheritCoverGrammar(this.parseBinaryExpression),n=this.captureStartMarker(),r=this.getMatchingPunctuator($);if(!r)return t;t.type!==z.Identifier&&t.type!==z.MemberExpression&&this.throwError(te.InvalidExpression,e,n),this.context.isAssignmentTarget||this.tolerateError(te.InvalidLeftHandSideInAssignment,e),this.matchPunctuator("=")||(this.context.isAssignmentTarget=!1),this.readNextRawToken();const i=this.isolateCoverGrammar(this.parseAssignmentExpression);return this.finalize({type:z.AssignmentExpression,left:t,operator:r,right:i,...this.getItemLocation(e)})}parseExpression(){return this.isolateCoverGrammar(this.parseAssignmentExpression)}parseStatements(e){const t=[];for(;this.rawToken.type!==X.EOF&&!this.matchPunctuator(e);){const e=this.parseStatementListItem();se(e)||t.push(e)}return t}parseStatementListItem(){return this.context.isAssignmentTarget=!0,this.matchKeyword(G.Function)?this.parseFunctionDeclaration():this.matchKeyword(G.Export)?this.parseExportDeclaration():this.matchKeyword(G.Import)?this.parseImportDeclaration():this.parseStatement()}parseBlock(){const e=this.captureStartMarker();this.expectPunctuator("{");const t=this.parseStatements("}");return this.expectPunctuator("}"),this.finalize({type:z.BlockStatement,body:t,...this.getItemLocation(e)})}parseObjectStatement(){const e=this.captureStartMarker(),t=this.parseObjectExpression();return this.finalize({type:z.ExpressionStatement,expression:t,...this.getItemLocation(e)})}parseBlockOrObjectStatement(){return this.context.curlyParsingType===Fe.AsObject||this.peekAhead((e=>{let t=e();return(t.type===X.Identifier||t.type===X.StringLiteral)&&(t=e(),t.type===X.Punctuator&&":"===t.value)}))?this.parseObjectStatement():this.parseBlock()}parseIdentifier(){const e=this.rawToken;if(e.type!==X.Identifier)return null;const t=this.captureStartMarker();return this.readNextRawToken(),this.finalize({type:z.Identifier,name:e.value,...this.getItemLocation(t)})}parseVariableDeclarator(){const e=this.captureStartMarker(),t=this.parseIdentifier();t||this.throwUnexpectedToken({code:te.IdentiferExpected});let n=null;if(this.matchPunctuator("=")){this.readNextRawToken();const e=this.rawToken;try{n=this.isolateCoverGrammar(this.parseAssignmentExpression)}catch(t){this.throwUnexpectedToken({rawToken:e,code:te.InvalidVariableAssignment})}}return this.finalize({type:z.VariableDeclarator,id:t,init:n,...this.getItemLocation(e)})}parseVariableDeclarationList(){const e=[this.parseVariableDeclarator()];for(;this.matchPunctuator(",");)this.readNextRawToken(),e.push(this.parseVariableDeclarator());return e}parseVariableDeclaration(){const e=this.captureStartMarker();this.expectKeyword(G.Var);const t=this.parseVariableDeclarationList();return this.consumeSemicolon(),this.finalize({type:z.VariableDeclaration,declarations:t,kind:"var",...this.getItemLocation(e)})}parseEmptyStatement(){const e=this.captureStartMarker();return this.expectPunctuator(";"),this.finalize({type:z.EmptyStatement,...this.getItemLocation(e)})}parseExpressionStatement(){const e=this.captureStartMarker(),t=this.parseExpression();return this.consumeSemicolon(),this.finalize({type:z.ExpressionStatement,expression:t,...this.getItemLocation(e)})}parseIfClause(){return this.withBlockContext(xe.IfClause,this.parseStatement)}parseIfStatement(){const e=this.captureStartMarker();this.expectKeyword(G.If),this.expectPunctuator("(");const t=this.captureStartMarker(),n=this.parseExpression(),r=this.captureStartMarker();this.expectPunctuator(")"),n.type!==z.AssignmentExpression&&n.type!==z.UpdateExpression||this.throwError(te.InvalidExpression,t,r);const i=this.parseIfClause();let a=null;return this.matchKeyword(G.Else)&&(this.readNextRawToken(),a=this.parseIfClause()),this.finalize({type:z.IfStatement,test:n,consequent:i,alternate:a,...this.getItemLocation(e)})}parseWhileStatement(){const e=this.captureStartMarker();this.expectKeyword(G.While),this.expectPunctuator("(");const t=this.captureStartMarker(),n=this.parseExpression(),r=this.captureStartMarker();this.expectPunctuator(")"),n.type!==z.AssignmentExpression&&n.type!==z.UpdateExpression||this.throwError(te.InvalidExpression,t,r);const i=this.withBlockContext(xe.WhileLoop,this.parseStatement);return this.finalize({type:z.WhileStatement,test:n,body:i,...this.getItemLocation(e)})}parseForStatement(){let e=null,t=null,n=null,r=null,i=null;const a=this.captureStartMarker();if(this.expectKeyword(G.For),this.expectPunctuator("("),this.matchPunctuator(";"))this.readNextRawToken();else if(this.matchKeyword(G.Var)){const t=this.captureStartMarker();this.readNextRawToken();const n=this.parseVariableDeclarationList();1===n.length&&this.matchKeyword(G.In)?(n[0].init&&this.throwError(te.ForInOfLoopInitializer,t),r=this.finalize({type:z.VariableDeclaration,declarations:n,kind:"var",...this.getItemLocation(t)}),this.readNextRawToken(),i=this.parseExpression()):(this.matchKeyword(G.In)&&this.throwError(te.InvalidLeftHandSideInForIn,t),e=this.finalize({type:z.VariableDeclaration,declarations:n,kind:"var",...this.getItemLocation(t)}),this.expectPunctuator(";"))}else{const t=this.context.isAssignmentTarget,n=this.captureStartMarker();e=this.inheritCoverGrammar(this.parseAssignmentExpression),this.matchKeyword(G.In)?(this.context.isAssignmentTarget||this.tolerateError(te.InvalidLeftHandSideInForIn,n),e.type!==z.Identifier&&this.throwError(te.InvalidLeftHandSideInForIn,n),this.readNextRawToken(),r=e,i=this.parseExpression(),e=null):(this.context.isAssignmentTarget=t,this.expectPunctuator(";"))}r||(this.matchPunctuator(";")||(t=this.isolateCoverGrammar(this.parseExpression)),this.expectPunctuator(";"),this.matchPunctuator(")")||(n=this.isolateCoverGrammar(this.parseExpression))),this.expectPunctuator(")");const s=this.withBlockContext(xe.ForLoop,(()=>this.isolateCoverGrammar(this.parseStatement)));return r&&i?this.finalize({type:z.ForInStatement,left:r,right:i,body:s,...this.getItemLocation(a)}):this.finalize({type:z.ForStatement,init:e,test:t,update:n,body:s,...this.getItemLocation(a)})}parseContinueStatement(){const e=this.captureStartMarker();return this.expectKeyword(G.Continue),this.consumeSemicolon(),this.finalize({type:z.ContinueStatement,...this.getItemLocation(e)})}parseBreakStatement(){const e=this.captureStartMarker();return this.expectKeyword(G.Break),this.consumeSemicolon(),this.finalize({type:z.BreakStatement,...this.getItemLocation(e)})}parseReturnStatement(){const e=this.captureStartMarker();this.expectKeyword(G.Return);const t=(this.matchPunctuator(";")||this.matchPunctuator("}")||this.hasLineTerminator||this.rawToken.type===X.EOF)&&this.rawToken.type!==X.StringLiteral&&this.rawToken.type!==X.Template?null:this.parseExpression();return this.consumeSemicolon(),this.finalize({type:z.ReturnStatement,argument:t,...this.getItemLocation(e)})}parseStatement(){switch(this.rawToken.type){case X.BooleanLiteral:case X.NullLiteral:case X.NumericLiteral:case X.StringLiteral:case X.Template:case X.Identifier:return this.parseExpressionStatement();case X.Punctuator:return"{"===this.rawToken.value?this.parseBlockOrObjectStatement():"("===this.rawToken.value?this.parseExpressionStatement():";"===this.rawToken.value?this.parseEmptyStatement():this.parseExpressionStatement();case X.Keyword:switch(this.rawToken.value.toLowerCase()){case G.Break:return this.parseBreakStatement();case G.Continue:return this.parseContinueStatement();case G.For:return this.parseForStatement();case G.Function:return this.parseFunctionDeclaration();case G.If:return this.parseIfStatement();case G.Return:return this.parseReturnStatement();case G.Var:return this.parseVariableDeclaration();case G.While:return this.parseWhileStatement();default:return this.parseExpressionStatement()}default:return this.throwUnexpectedToken({rawToken:this.rawToken})}}parseFormalParameters(){const e=[];if(this.expectPunctuator("("),!this.matchPunctuator(")"))for(;this.rawToken.type!==X.EOF;){const t=this.parseIdentifier();if(t||this.throwUnexpectedToken({rawToken:this.rawToken,code:te.IdentiferExpected}),e.push(t),this.matchPunctuator(")"))break;if(this.expectPunctuator(","),this.matchPunctuator(")"))break}return this.expectPunctuator(")"),e}parseFunctionDeclaration(){(this.context.blockContext&xe.Function)===xe.Function&&this.throwUnexpectedToken({code:te.NoFunctionInsideFunction}),(this.context.blockContext&xe.WhileLoop)!==xe.WhileLoop&&(this.context.blockContext&xe.IfClause)!==xe.IfClause||this.throwUnexpectedToken({code:te.NoFunctionInsideBlock});const e=this.captureStartMarker();this.expectKeyword(G.Function);const t=this.parseIdentifier();t||this.throwUnexpectedToken({code:te.InvalidFunctionIdentifier});const n=this.parseFormalParameters(),r=this.context.blockContext;this.context.blockContext=this.context.blockContext|xe.Function;const i=this.parseBlock();return this.context.blockContext=r,this.finalize({type:z.FunctionDeclaration,id:t,params:n,body:i,...this.getItemLocation(e)})}parseScript(){const e=this.captureStartMarker(),t=this.parseStatements(),n=this.finalize({type:z.Program,body:t,...this.getItemLocation(e)});return this.options.tokens&&(n.tokens=this.tokens),this.options.tolerant&&(n.errors=this.errorHandler.errors),n}parseExportDeclaration(){this.context.blockContext!==xe.None&&this.throwUnexpectedToken({code:te.ModuleExportRootOnly});let e=null;const t=this.captureStartMarker();return this.expectKeyword(G.Export),this.matchKeyword(G.Var)?e=this.parseVariableDeclaration():this.matchKeyword("function")?e=this.parseFunctionDeclaration():this.throwUnexpectedToken({code:te.InvalidExpression}),this.finalize({type:z.ExportNamedDeclaration,declaration:e,specifiers:[],source:null,...this.getItemLocation(t)})}parseModuleSpecifier(){const e=this.captureStartMarker(),t=this.rawToken;if(t.type===X.StringLiteral)return this.readNextRawToken(),this.finalize({type:z.Literal,value:t.value,raw:this.getSourceValue(t),isString:!0,...this.getItemLocation(e)});this.throwError(te.InvalidModuleUri,e)}parseDefaultSpecifier(){const e=this.captureStartMarker(),t=this.parseIdentifier();return t||this.throwUnexpectedToken({code:te.IdentiferExpected}),this.finalize({type:z.ImportDefaultSpecifier,local:t,...this.getItemLocation(e)})}parseImportDeclaration(){this.context.blockContext!==xe.None&&this.throwUnexpectedToken({code:te.ModuleImportRootOnly});const e=this.captureStartMarker();this.expectKeyword(G.Import);const t=this.parseDefaultSpecifier();this.expectContextualKeyword(G.From);const n=this.parseModuleSpecifier();return this.finalize({type:z.ImportDeclaration,specifiers:[t],source:n,...this.getItemLocation(e)})}}function Se(e,t=[]){const n=function(e,t,n){return new ve(e,void 0,void 0).parseScript()}(e);if(null===n.body||void 0===n.body)throw new re({index:0,line:0,column:0,data:null,description:"",code:te.InvalidExpression});if(0===n.body.length)throw new re({index:0,line:0,column:0,data:null,description:"",code:te.InvalidExpression});if(0===n.body.length)throw new re({index:0,line:0,column:0,data:null,description:"",code:te.InvalidExpression});return n.loadedModules={},(0,s.dN)(n,t),n}class ke{constructor(e){const t=this;t._keys=[],t._values=[],t.length=0,e&&e.forEach((e=>{t.set(e[0],e[1])}))}entries(){return[].slice.call(this.keys().map(((e,t)=>[e,this._values[t]])))}keys(){return[].slice.call(this._keys)}values(){return[].slice.call(this._values)}has(e){return this._keys.includes(e)}get(e){const t=this._keys.indexOf(e);return t>-1?this._values[t]:null}deepGet(e){if(!e||!e.length)return null;const t=(e,n)=>null==e?null:n.length?t(e instanceof ke?e.get(n[0]):e[n[0]],n.slice(1)):e;return t(this.get(e[0]),e.slice(1))}set(e,t){const n=this,r=this._keys.indexOf(e);return r>-1?n._values[r]=t:(n._keys.push(e),n._values.push(t),n.length=n._values.length),this}sortedSet(e,t,n,r){const i=this,a=this._keys.length,s=n||0,o=void 0!==r?r:a-1;if(0===a)return i._keys.push(e),i._values.push(t),i;if(e===this._keys[s])return this._values.splice(s,0,t),this;if(e===this._keys[o])return this._values.splice(o,0,t),this;if(e>this._keys[o])return this._keys.splice(o+1,0,e),this._values.splice(o+1,0,t),this;if(e<this._keys[s])return this._values.splice(s,0,t),this._keys.splice(s,0,e),this;if(s>=o)return this;const u=s+Math.floor((o-s)/2);return e<this._keys[u]?this.sortedSet(e,t,s,u-1):e>this._keys[u]?this.sortedSet(e,t,u+1,o):this}size(){return this.length}clear(){const e=this;return e._keys.length=e.length=e._values.length=0,this}delete(e){const t=this,n=t._keys.indexOf(e);return n>-1&&(t._keys.splice(n,1),t._values.splice(n,1),t.length=t._keys.length,!0)}forEach(e){this._keys.forEach(((t,n)=>{e(this._values[n],t,n)}))}map(e){return this.keys().map(((t,n)=>e(this._values[n],t,n)))}filter(e){const t=this;return t._keys.forEach(((n,r)=>{!1===e(t._values[r],n,r)&&t.delete(n)})),this}clone(){return new ke(this.entries())}}class Ie{constructor(e=20){this._maxEntries=e,this._values=new ke}delete(e){this._values.has(e)&&this._values.delete(e)}get(e){let t=null;return this._values.has(e)&&(t=this._values.get(e),this._values.delete(e),this._values.set(e,t)),t}put(e,t){if(this._values.size()>=this._maxEntries){const e=this._values.keys()[0];this._values.delete(e)}this._values.set(e,t)}}class Te{constructor(e){this.portalUri=e}normalizeModuleUri(e){const t=/^[a-z0-9A-Z]+(@[0-9]+\.[0-9]+\.[0-9]+)?([\?|\/].*)?$/gi,n=/(?<portalurl>.+)\/home\/item\.html\?id\=(?<itemid>.+)$/gi,r=/(?<portalurl>.+)\/sharing\/rest\/content\/users\/[a-zA-Z0-9]+\/items\/(?<itemid>.+)$/gi,i=/(?<portalurl>.+)\/sharing\/rest\/content\/items\/(?<itemid>.+)$/gi,a=/(?<itemid>.*)@(?<versionstring>[0-9]+\.[0-9]+\.[0-9]+)([\?|\/].*)?$/gi;if(e.startsWith("portal+")){let s=e.substring(7),o="",u=s,l=!1;for(const e of[n,i,r]){const t=e.exec(s);if(null!==t){const e=t.groups;u=e.itemid,o=e.portalurl,l=!0;break}}if(!1===l){if(!t.test(s))throw new x.Tu(x.TD.UnsupportedUriProtocol,{uri:e});u=s,o=this.portalUri}u.includes("/")&&(u=u.split("/")[0]),u.includes("?")&&(u=u.split("?")[0]);let c="current";const d=a.exec(u);if(null!==d){const e=d.groups;u=e.itemid,c=e.versionstring}return s=new J.Z({url:o}).restUrl+"/content/items/"+u+"/resources/"+c+".arc",{url:s,scheme:"portal",uri:"PO:"+s}}if(e.startsWith("mock")){if("mock"===e)return{url:"",scheme:"mock",data:'\n      export var hello = 1;\n      export function helloWorld() {\n          return "Hello World " + hello;\n      }\n  ',uri:"mock"};const t=e.replace("mock:","");if(void 0!==Te.mocks[t])return{url:"",scheme:"mock",data:Te.mocks[t],uri:e}}throw new x.Tu(x.TD.UnrecognisedUri,{uri:e})}async fetchModule(e){const t=Te.cachedModules.getFromCache(e.uri);if(t)return t;const n=this.fetchSource(e);Te.cachedModules.addToCache(e.uri,n);let r=null;try{r=await n}catch(t){throw Te.cachedModules.removeFromCache(e.uri),t}return r}async fetchSource(e){if("portal"===e.scheme){const t=await(0,j.default)(e.url,{responseType:"text",query:{}});if(t.data)return Se(t.data,[])}if("mock"===e.scheme)return Se(e.data??"",[]);throw new x.Tu(x.TD.UnsupportedUriProtocol)}static create(e){return new Te(e)}static getDefault(){return this._default??(Te._default=Te._moduleResolverFactory())}static set moduleResolverClass(e){this._moduleResolverFactory=e,this._default=null}}Te.mocks={},Te.cachedModules=new class{constructor(e=20){this._maxEntries=e,this._cache=new Ie(this._maxEntries)}clear(){this._cache=new Ie(this._maxEntries)}addToCache(e,t){this._cache.put(e,t)}removeFromCache(e){this._cache.delete(e)}getFromCache(e){return this._cache.get(e)}}(30),Te._default=null,Te._moduleResolverFactory=()=>{const e=J.Z.getDefault();return new Te(e.url)};class Be extends w.Rm{constructor(e,t){super(),this.definition=null,this.context=null,this.definition=e,this.context=t}createFunction(e){return(...t)=>{const n={spatialReference:this.context.spatialReference,console:this.context.console,timeReference:this.context.timeReference?this.context.timeReference:null,lrucache:this.context.lrucache,exports:this.context.exports,libraryResolver:this.context.libraryResolver,interceptor:this.context.interceptor,localScope:{},depthCounter:{depth:e.depthCounter.depth+1},globalScope:this.context.globalScope};if(n.depthCounter.depth>64)throw new x.aV(e,x.rH.MaximumCallDepth,null);return Ue(this.definition,n,t,null)}}call(e,t){return Me(e,t,((n,r,i)=>{const a={spatialReference:e.spatialReference,globalScope:e.globalScope,depthCounter:{depth:e.depthCounter.depth+1},libraryResolver:e.libraryResolver,exports:e.exports,timeReference:e.timeReference??null,console:e.console,lrucache:e.lrucache,interceptor:e.interceptor,localScope:{}};if(a.depthCounter.depth>64)throw new x.aV(e,x.rH.MaximumCallDepth,t);return Ue(this.definition,a,i,t)}))}marshalledCall(e,t,n,r){return r(e,t,((i,s,o)=>{const u={spatialReference:e.spatialReference,globalScope:n.globalScope,depthCounter:{depth:e.depthCounter.depth+1},libraryResolver:e.libraryResolver,exports:e.exports,console:e.console,timeReference:e.timeReference??null,lrucache:e.lrucache,interceptor:e.interceptor,localScope:{}};return o=o.map((t=>!(0,a.i)(t)||t instanceof w.Vg?t:(0,w.aq)(t,e,r))),(0,w.aq)(Ue(this.definition,u,o,t),n,r)}))}}class _e extends y.P{constructor(e){super(e)}global(e){const t=this.executingContext.globalScope[e.toLowerCase()];if(t.valueset||(t.value=Ne(this.executingContext,t.node),t.valueset=!0),(0,a.i)(t.value)&&!(t.value instanceof w.Vg)){const e=new w.Vg;e.fn=t.value,e.parameterEvaluator=Me,e.context=this.executingContext,t.value=e}return t.value}setGlobal(e,t){if((0,a.i)(t))throw new x.aV(null,x.rH.AssignModuleFunction,null);this.executingContext.globalScope[e.toLowerCase()]={value:t,valueset:!0,node:null}}hasGlobal(e){return void 0===this.executingContext.exports[e]&&(e=e.toLowerCase()),void 0!==this.executingContext.exports[e]}loadModule(e){let t=e.spatialReference;null==t&&(t=new g.Z({wkid:102100})),this.moduleScope=ze({},e.customfunctions,e.timeReference),this.executingContext={spatialReference:t,globalScope:this.moduleScope,localScope:null,libraryResolver:new D.s(e.libraryResolver._moduleSingletons,this.source.syntax.loadedModules),exports:{},console:e.console?e.console:qe,timeReference:e.timeReference??null,lrucache:e.lrucache,interceptor:e.interceptor,depthCounter:{depth:1}},Ne(this.executingContext,this.source.syntax)}}function Me(e,t,n){try{return!0===t.preparsed?n(e,null,t.arguments):n(e,t,function(e,t){const n=[];for(let r=0;r<t.arguments.length;r++)n.push(Ne(e,t.arguments[r]));return n}(e,t))}catch(e){throw e}}function Ne(e,t){try{switch(t?.type){case"EmptyStatement":return a.v;case"VariableDeclarator":return function(e,t){let n=null===t.init?null:Ne(e,t.init);if(n===a.v&&(n=null),"Identifier"!==t.id.type)throw new x.aV(e,x.rH.InvalidIdentifier,t);const r=t.id.name.toLowerCase();return null!=e.localScope?e.localScope[r]={value:n,valueset:!0,node:t.init}:e.globalScope[r]={value:n,valueset:!0,node:t.init},a.v}(e,t);case"VariableDeclaration":return function(e,t){for(let n=0;n<t.declarations.length;n++)Ne(e,t.declarations[n]);return a.v}(e,t);case"ImportDeclaration":return function(e,t){const n=t.specifiers[0].local.name.toLowerCase(),r=e.libraryResolver.loadLibrary(n);let i=null;return e.libraryResolver._moduleSingletons?.has(r.uri)?i=e.libraryResolver._moduleSingletons.get(r.uri):(i=new _e(r),i.loadModule(e),e.libraryResolver._moduleSingletons?.set(r.uri,i)),e.globalScope[n]={value:i,valueset:!0,node:t},a.v}(e,t);case"ExportNamedDeclaration":return function(e,t){if(Ne(e,t.declaration),"FunctionDeclaration"===t.declaration.type)e.exports[t.declaration.id.name.toLowerCase()]="function";else if("VariableDeclaration"===t.declaration.type)for(const n of t.declaration.declarations)e.exports[n.id.name.toLowerCase()]="variable";return a.v}(e,t);case"BlockStatement":case"Program":return function(e,t){let n=a.v;for(let r=0;r<t.body.length;r++)if(n=Ne(e,t.body[r]),n instanceof a.R||n===a.o||n===a.p)return n;return n}(e,t);case"FunctionDeclaration":return function(e,t){const n=t.id.name.toLowerCase();return e.globalScope[n]={valueset:!0,node:null,value:new Be(t,e)},a.v}(e,t);case"ReturnStatement":return function(e,t){if(null===t.argument)return new a.R(a.v);const n=Ne(e,t.argument);return new a.R(n)}(e,t);case"IfStatement":return function(e,t){const n=Ne(e,t.test);if(!0===n)return Ne(e,t.consequent);if(!1===n)return null!==t.alternate?Ne(e,t.alternate):a.v;throw new x.aV(e,x.rH.BooleanConditionRequired,t)}(e,t);case"ExpressionStatement":return function(e,t){if("AssignmentExpression"===t.expression.type||"UpdateExpression"===t.expression.type)return Ne(e,t.expression);if("CallExpression"===t.expression.type){const n=Ne(e,t.expression);return n===a.v?a.v:new a.I(n)}{const n=Ne(e,t.expression);return n===a.v?a.v:new a.I(n)}}(e,t);case"AssignmentExpression":return function(e,t){let n=null,i="";if("MemberExpression"===t.left.type){if(n=Ne(e,t.left.object),!0===t.left.computed)i=Ne(e,t.left.property);else{if("Identifier"!==t.left.property.type)throw new x.aV(e,x.rH.InvalidIdentifier,t);i=t.left.property.name}const s=Ne(e,t.right);if((0,a.m)(n)){if(!(0,a.b)(i))throw new x.aV(e,x.rH.ArrayAccessorMustBeNumber,t);if(i<0&&(i=n.length+i),i<0||i>n.length)throw new x.aV(e,x.rH.OutOfBounds,t);if(i===n.length){if("="!==t.operator)throw new x.aV(e,x.rH.OutOfBounds,t);n[i]=He(s,t.operator,n[i],t,e)}else n[i]=He(s,t.operator,n[i],t,e)}else if(n instanceof r.Z){if(!1===(0,a.c)(i))throw new x.aV(e,x.rH.KeyAccessorMustBeString,t);if(!0===n.hasField(i))n.setField(i,He(s,t.operator,n.field(i),t,e));else{if("="!==t.operator)throw new x.aV(e,x.rH.FieldNotFound,t,{key:i});n.setField(i,He(s,t.operator,null,t,e))}}else if((0,a.w)(n)){if(!1===(0,a.c)(i))throw new x.aV(e,x.rH.KeyAccessorMustBeString,t);if(!0===n.hasField(i))n.setField(i,He(s,t.operator,n.field(i),t,e));else{if("="!==t.operator)throw new x.aV(e,x.rH.FieldNotFound,t,{key:i});n.setField(i,He(s,t.operator,null,t,e))}}else{if((0,a.x)(n))throw new x.aV(e,x.rH.Immutable,t);if(!(n instanceof _e))throw new x.aV(e,x.rH.InvalidIdentifier,t);if(!1===(0,a.c)(i))throw new x.aV(e,x.rH.ModuleAccessorMustBeString,t);if(!0!==n.hasGlobal(i))throw new x.aV(e,x.rH.ModuleExportNotFound,t);n.setGlobal(i,He(s,t.operator,n.global(i),t,e))}return a.v}n=t.left.name.toLowerCase();const s=Ne(e,t.right);if(null!=e.localScope&&void 0!==e.localScope[n])return e.localScope[n]={value:He(s,t.operator,e.localScope[n].value,t,e),valueset:!0,node:t.right},a.v;if(void 0!==e.globalScope[n])return e.globalScope[n]={value:He(s,t.operator,e.globalScope[n].value,t,e),valueset:!0,node:t.right},a.v;throw new x.aV(e,x.rH.InvalidIdentifier,t)}(e,t);case"UpdateExpression":return function(e,t){let n,i=null,s="";if("MemberExpression"===t.argument.type){if(i=Ne(e,t.argument.object),!0===t.argument.computed?s=Ne(e,t.argument.property):"Identifier"===t.argument.property.type&&(s=t.argument.property.name),(0,a.m)(i)){if(!(0,a.b)(s))throw new x.aV(e,x.rH.ArrayAccessorMustBeNumber,t);if(s<0&&(s=i.length+s),s<0||s>=i.length)throw new x.aV(e,x.rH.OutOfBounds,t);n=(0,a.g)(i[s]),i[s]="++"===t.operator?n+1:n-1}else if(i instanceof r.Z){if(!1===(0,a.c)(s))throw new x.aV(e,x.rH.KeyAccessorMustBeString,t);if(!0!==i.hasField(s))throw new x.aV(e,x.rH.FieldNotFound,t);n=(0,a.g)(i.field(s)),i.setField(s,"++"===t.operator?n+1:n-1)}else if((0,a.w)(i)){if(!1===(0,a.c)(s))throw new x.aV(e,x.rH.KeyAccessorMustBeString,t);if(!0!==i.hasField(s))throw new x.aV(e,x.rH.FieldNotFound,t);n=(0,a.g)(i.field(s)),i.setField(s,"++"===t.operator?n+1:n-1)}else{if((0,a.x)(i))throw new x.aV(e,x.rH.Immutable,t);if(!(i instanceof _e))throw new x.aV(e,x.rH.InvalidParameter,t);if(!1===(0,a.c)(s))throw new x.aV(e,x.rH.ModuleAccessorMustBeString,t);if(!0!==i.hasGlobal(s))throw new x.aV(e,x.rH.ModuleExportNotFound,t);n=(0,a.g)(i.global(s)),i.setGlobal(s,"++"===t.operator?n+1:n-1)}return!1===t.prefix?n:"++"===t.operator?n+1:n-1}if(i="Identifier"===t.argument.type?t.argument.name.toLowerCase():"",!i)throw new x.aV(e,x.rH.InvalidIdentifier,t);if(null!=e.localScope&&void 0!==e.localScope[i])return n=(0,a.g)(e.localScope[i].value),e.localScope[i]={value:"++"===t.operator?n+1:n-1,valueset:!0,node:t},!1===t.prefix?n:"++"===t.operator?n+1:n-1;if(void 0!==e.globalScope[i])return n=(0,a.g)(e.globalScope[i].value),e.globalScope[i]={value:"++"===t.operator?n+1:n-1,valueset:!0,node:t},!1===t.prefix?n:"++"===t.operator?n+1:n-1;throw new x.aV(e,x.rH.InvalidIdentifier,t)}(e,t);case"BreakStatement":return a.o;case"ContinueStatement":return a.p;case"TemplateElement":return function(e,t){return t.value?t.value.cooked:""}(0,t);case"TemplateLiteral":return function(e,t){let n="",r=0;for(const i of t.quasis)n+=i.value?i.value.cooked:"",!1===i.tail&&(n+=t.expressions[r]?(0,a.j)(Re(Ne(e,t.expressions[r]),e,t)):"",r++);return n}(e,t);case"ForStatement":return function(e,t){null!==t.init&&Ne(e,t.init);const n={testResult:!0,lastAction:a.v};do{Le(e,t,n)}while(!0===n.testResult);return n.lastAction instanceof a.R?n.lastAction:a.v}(e,t);case"ForInStatement":return function(e,t){const n=Ne(e,t.right);"VariableDeclaration"===t.left.type&&Ne(e,t.left);let i=null,s="";if("VariableDeclaration"===t.left.type){const e=t.left.declarations[0].id;"Identifier"===e.type&&(s=e.name)}else"Identifier"===t.left.type&&(s=t.left.name);if(!s)throw new x.aV(e,x.rH.InvalidIdentifier,t);if(s=s.toLowerCase(),null!=e.localScope&&void 0!==e.localScope[s]&&(i=e.localScope[s]),null===i&&void 0!==e.globalScope[s]&&(i=e.globalScope[s]),null===i)throw new x.aV(e,x.rH.InvalidIdentifier,t);if((0,a.m)(n)||(0,a.c)(n)){const r=n.length;for(let n=0;n<r;n++){i.value=n;const r=Ne(e,t.body);if(r===a.o)break;if(r instanceof a.R)return r}return a.v}if((0,a.x)(n)){for(let r=0;r<n.length();r++){i.value=r;const n=Ne(e,t.body);if(n===a.o)break;if(n instanceof a.R)return n}return a.v}if(!(n instanceof r.Z||(0,a.w)(n)))return a.v;{const r=n.keys();for(let n=0;n<r.length;n++){i.value=r[n];const s=Ne(e,t.body);if(s===a.o)break;if(s instanceof a.R)return s}}}(e,t);case"WhileStatement":return function(e,t){const n={testResult:!0,lastAction:a.v};if(n.testResult=Ne(e,t.test),!1===n.testResult)return a.v;if(!0!==n.testResult)throw new x.aV(e,x.rH.BooleanConditionRequired,t);for(;!0===n.testResult&&(n.lastAction=Ne(e,t.body),n.lastAction!==a.o)&&!(n.lastAction instanceof a.R);)if(n.testResult=Ne(e,t.test),!0!==n.testResult&&!1!==n.testResult)throw new x.aV(e,x.rH.BooleanConditionRequired,t);return n.lastAction instanceof a.R?n.lastAction:a.v}(e,t);case"Identifier":return Oe(e,t);case"MemberExpression":return function(e,t){try{const n=Ne(e,t.object);if(null===n)throw new x.aV(e,x.rH.MemberOfNull,t);if(!1===t.computed){if("Identifier"===t.property.type){if(n instanceof r.Z||(0,a.w)(n))return n.field(t.property.name);if(n instanceof p.Z)return(0,l.Z)(n,t.property.name,t,e);if(n instanceof _e){if(!n.hasGlobal(t.property.name))throw new x.aV(e,x.rH.InvalidIdentifier,t);return n.global(t.property.name)}}throw new x.aV(e,x.rH.InvalidMemberAccessKey,t)}{let i=Ne(e,t.property);if(n instanceof r.Z||(0,a.w)(n)){if((0,a.c)(i))return n.field(i);throw new x.aV(e,x.rH.InvalidMemberAccessKey,t)}if(n instanceof _e){if((0,a.c)(i))return n.global(i);throw new x.aV(e,x.rH.InvalidMemberAccessKey,t)}if(n instanceof p.Z){if((0,a.c)(i))return(0,l.Z)(n,i,t,e);throw new x.aV(e,x.rH.InvalidMemberAccessKey,t)}if((0,a.m)(n)){if((0,a.b)(i)&&isFinite(i)&&Math.floor(i)===i){if(i<0&&(i=n.length+i),i>=n.length||i<0)throw new x.aV(e,x.rH.OutOfBounds,t);return n[i]}throw new x.aV(e,x.rH.InvalidMemberAccessKey,t)}if((0,a.c)(n)){if((0,a.b)(i)&&isFinite(i)&&Math.floor(i)===i){if(i<0&&(i=n.length+i),i>=n.length||i<0)throw new x.aV(e,x.rH.OutOfBounds,t);return n[i]}throw new x.aV(e,x.rH.InvalidMemberAccessKey,t)}if((0,a.x)(n)){if((0,a.b)(i)&&isFinite(i)&&Math.floor(i)===i){if(i<0&&(i=n.length()+i),i>=n.length()||i<0)throw new x.aV(e,x.rH.OutOfBounds,t);return n.get(i)}throw new x.aV(e,x.rH.InvalidMemberAccessKey,t)}throw new x.aV(e,x.rH.InvalidMemberAccessKey,t)}}catch(e){throw e}}(e,t);case"Literal":return t.value;case"CallExpression":return function(e,t){try{if("MemberExpression"===t.callee.type){const n=Ne(e,t.callee.object);if(!(n instanceof _e))throw new x.aV(e,x.rH.FuncionNotFound,t);const r=!1===t.callee.computed?t.callee.property.name:Ne(e,t.callee.property);if(!n.hasGlobal(r))throw new x.aV(e,x.rH.FuncionNotFound,t);const i=n.global(r);if(!(0,a.i)(i))throw new x.aV(e,x.rH.CallNonFunction,t);return i.call(e,t)}if("Identifier"!==t.callee.type)throw new x.aV(e,x.rH.FuncionNotFound,t);if(null!=e.localScope&&void 0!==e.localScope[t.callee.name.toLowerCase()]){const n=e.localScope[t.callee.name.toLowerCase()];if((0,a.i)(n.value))return n.value.call(e,t);throw new x.aV(e,x.rH.CallNonFunction,t)}if(void 0!==e.globalScope[t.callee.name.toLowerCase()]){const n=e.globalScope[t.callee.name.toLowerCase()];if((0,a.i)(n.value))return n.value.call(e,t);throw new x.aV(e,x.rH.CallNonFunction,t)}throw new x.aV(e,x.rH.FuncionNotFound,t)}catch(e){throw e}}(e,t);case"UnaryExpression":return function(e,t){try{const n=Ne(e,t.argument);if((0,a.a)(n)){if("!"===t.operator)return!n;if("-"===t.operator)return-1*(0,a.g)(n);if("+"===t.operator)return 1*(0,a.g)(n);if("~"===t.operator)return~(0,a.g)(n);throw new x.aV(e,x.rH.UnsupportedUnaryOperator,t)}if("~"===t.operator)return~(0,a.g)(n);if("-"===t.operator)return-1*(0,a.g)(n);if("+"===t.operator)return 1*(0,a.g)(n);throw new x.aV(e,x.rH.UnsupportedUnaryOperator,t)}catch(e){throw e}}(e,t);case"BinaryExpression":return function(e,t){try{const n=[Ne(e,t.left),Ne(e,t.right)],r=n[0],i=n[1];switch(t.operator){case"|":case"<<":case">>":case">>>":case"^":case"&":return(0,a.u)((0,a.g)(r),(0,a.g)(i),t.operator);case"==":return(0,a.s)(r,i);case"!=":return!(0,a.s)(r,i);case"<":case">":case"<=":case">=":return(0,a.r)(r,i,t.operator);case"+":return(0,a.c)(r)||(0,a.c)(i)?(0,a.j)(r)+(0,a.j)(i):(0,a.g)(r)+(0,a.g)(i);case"-":return(0,a.g)(r)-(0,a.g)(i);case"*":return(0,a.g)(r)*(0,a.g)(i);case"/":return(0,a.g)(r)/(0,a.g)(i);case"%":return(0,a.g)(r)%(0,a.g)(i);default:throw new x.aV(e,x.rH.UnsupportedOperator,t)}}catch(e){throw e}}(e,t);case"LogicalExpression":return function(e,t){try{const n=Ne(e,t.left);if((0,a.a)(n))switch(t.operator){case"||":if(!0===n)return n;{const n=Ne(e,t.right);if((0,a.a)(n))return n;throw new x.aV(e,x.rH.LogicExpressionOrAnd,t)}case"&&":if(!1===n)return n;{const n=Ne(e,t.right);if((0,a.a)(n))return n;throw new x.aV(e,x.rH.LogicExpressionOrAnd,t)}default:throw new x.aV(e,x.rH.LogicExpressionOrAnd,t)}throw new x.aV(e,x.rH.LogicalExpressionOnlyBoolean,t)}catch(e){throw e}}(e,t);case"ArrayExpression":return function(e,t){try{const n=[];for(let r=0;r<t.elements.length;r++){const i=Ne(e,t.elements[r]);if((0,a.i)(i))throw new x.aV(e,x.rH.NoFunctionInArray,t);i===a.v?n.push(null):n.push(i)}return n}catch(e){throw e}}(e,t);case"ObjectExpression":return function(e,t){const n={},i=new Map;for(let r=0;r<t.properties.length;r++){const s=Ne(e,t.properties[r]);if((0,a.i)(s.value))throw new x.aV(e,x.rH.NoFunctionInDictionary,t);if(!1===(0,a.c)(s.key))throw new x.aV(e,x.rH.KeyMustBeString,t);let o=s.key.toString();const u=o.toLowerCase();i.has(u)?o=i.get(u):i.set(u,o),s.value===a.v?n[o]=null:n[o]=s.value}const s=new r.Z(n);return s.immutable=!1,s}(e,t);case"Property":return function(e,t){return{key:"Identifier"===t.key.type?t.key.name:Ne(e,t.key),value:Ne(e,t.value)}}(e,t);default:throw new x.aV(e,x.rH.Unrecognised,t)}}catch(n){throw(0,x.VO)(e,t,n)}}function Le(e,t,n){if(null!==t.test){if(n.testResult=Ne(e,t.test),!1===n.testResult)return;if(!0!==n.testResult)throw new x.aV(e,x.rH.BooleanConditionRequired,t)}n.lastAction=Ne(e,t.body),n.lastAction!==a.o?n.lastAction instanceof a.R?n.testResult=!1:null!==t.update&&Ne(e,t.update):n.testResult=!1}function He(e,t,n,r,i){switch(t){case"=":return e===a.v?null:e;case"/=":return(0,a.g)(n)/(0,a.g)(e);case"*=":return(0,a.g)(n)*(0,a.g)(e);case"-=":return(0,a.g)(n)-(0,a.g)(e);case"+=":return(0,a.c)(n)||(0,a.c)(e)?(0,a.j)(n)+(0,a.j)(e):(0,a.g)(n)+(0,a.g)(e);case"%=":return(0,a.g)(n)%(0,a.g)(e);default:throw new x.aV(i,x.rH.UnsupportedOperator,r)}}function Re(e,t,n){if((0,a.i)(e))throw new x.aV(t,x.rH.NoFunctionInTemplateLiteral,n);return e}function Oe(e,t){let n;try{const r=t.name.toLowerCase();if(null!=e.localScope&&void 0!==e.localScope[r])return n=e.localScope[r],!0===n.valueset||(n.value=Ne(e,n.node),n.valueset=!0),n.value;if(void 0!==e.globalScope[r])return n=e.globalScope[r],!0===n.valueset||(n.value=Ne(e,n.node),n.valueset=!0),n.value;throw new x.aV(e,x.rH.InvalidIdentifier,t)}catch(e){throw e}}const Ve={};function Pe(e,t,n,r){try{const i=Ne(e,t.arguments[n]);if((0,a.s)(i,r))return Ne(e,t.arguments[n+1]);{const i=t.arguments.length-n;return 1===i?Ne(e,t.arguments[n]):2===i?null:3===i?Ne(e,t.arguments[n+2]):Pe(e,t,n+2,r)}}catch(e){throw e}}function Ze(e,t,n,r){try{if(!0===r)return Ne(e,t.arguments[n+1]);if(3==t.arguments.length-n)return Ne(e,t.arguments[n+2]);{const r=Ne(e,t.arguments[n+2]);if(!1===(0,a.a)(r))throw new x.aV(e,x.rH.BooleanConditionRequired,t.arguments[n+2]);return Ze(e,t,n+2,r)}}catch(e){throw e}}function Ue(e,t,n,r){try{const i=e.body;if(n.length!==e.params.length)throw new x.aV(t,x.rH.WrongNumberOfParameters,r);if(null!=t.localScope)for(let r=0;r<n.length;r++)t.localScope[e.params[r].name.toLowerCase()]={value:n[r],valueset:!0,node:null};const s=Ne(t,i);if(s instanceof a.R)return s.value;if(s===a.o)throw new x.aV(t,x.rH.UnexpectedToken,r);if(s===a.p)throw new x.aV(t,x.rH.UnexpectedToken,r);return s instanceof a.I?s.value:s}catch(e){throw e}}(0,u.r)(Ve,Me),(0,f.r)(Ve,Me),(0,d.r)(Ve,Me),(0,l.r)(Ve,Me),(0,h.r)(Ve,Me),(0,c.registerFunctions)(Ve,Me),Ve.iif=function(e,t){try{(0,a.y)(null===t.arguments?[]:t.arguments,3,3,e,t);const n=Ne(e,t.arguments[0]);if(!1===(0,a.a)(n))throw new x.aV(e,x.rH.BooleanConditionRequired,t);return Ne(e,!0===n?t.arguments[1]:t.arguments[2])}catch(e){throw e}},Ve.decode=function(e,t){try{if(t.arguments.length<2)throw new x.aV(e,x.rH.WrongNumberOfParameters,t);if(2===t.arguments.length)return Ne(e,t.arguments[1]);if((t.arguments.length-1)%2==0)throw new x.aV(e,x.rH.WrongNumberOfParameters,t);return Pe(e,t,1,Ne(e,t.arguments[0]))}catch(e){throw e}},Ve.when=function(e,t){try{if(t.arguments.length<3)throw new x.aV(e,x.rH.WrongNumberOfParameters,t);if(t.arguments.length%2==0)throw new x.aV(e,x.rH.WrongNumberOfParameters,t);const n=Ne(e,t.arguments[0]);if(!1===(0,a.a)(n))throw new x.aV(e,x.rH.BooleanConditionRequired,t.arguments[0]);return Ze(e,t,0,n)}catch(e){throw e}};for(const e in Ve)Ve[e]={value:new w.Bx(Ve[e]),valueset:!0,node:null};const Ge=function(){};function ze(e,t,n){const a=new Ge;e||(e={}),t||(t={});const s=new r.Z({newline:"\n",tab:"\t",singlequote:"'",doublequote:'"',forwardslash:"/",backwardslash:"\\"});s.immutable=!1,a.textformatting={value:s,valueset:!0,node:null};for(const e in t)a[e]={value:new w.Bx(t[e]),native:!0,valueset:!0,node:null};for(const t in e)e[t]&&"esri.Graphic"===e[t].declaredClass?a[t]={value:i.Z.createFromGraphic(e[t],n),valueset:!0,node:null}:a[t]={value:e[t],valueset:!0,node:null};return a}function qe(e){console.log(e)}function je(e){const t={mode:"sync",compiled:!1,functions:{},signatures:[],standardFunction:Me,evaluateIdentifier:Oe};for(let n=0;n<e.length;n++)e[n].registerFunctions(t);for(const e in t.functions)Ve[e]={value:new w.Bx(t.functions[e]),valueset:!0,node:null},Ge.prototype[e]=Ve[e];for(let e=0;e<t.signatures.length;e++)(0,s.gW)(t.signatures[e],"sync")}function Je(e,t){let n=t.spatialReference;null==n&&(n=new g.Z({wkid:102100}));let r=null;e.usesModules&&(r=new D.s(new Map,e.loadedModules));const i={spatialReference:n,globalScope:ze(t.vars,t.customfunctions,t.timeReference),localScope:null,exports:{},libraryResolver:r,console:t.console?t.console:qe,timeReference:t.timeReference??null,lrucache:t.lrucache,interceptor:t.interceptor,depthCounter:{depth:1}};let s=Ne(i,e);if(s instanceof a.R&&(s=s.value),s instanceof a.I&&(s=s.value),s===a.v&&(s=null),s===a.o)throw new x.aV(i,x.rH.IllegalResult,null);if(s===a.p)throw new x.aV(i,x.rH.IllegalResult,null);if((0,a.i)(s))throw new x.aV(i,x.rH.IllegalResult,null);return s}(Ge.prototype=Ve).infinity={value:Number.POSITIVE_INFINITY,valueset:!0,node:null},Ge.prototype.pi={value:Math.PI,valueset:!0,node:null},a.q,je([o.A]);var We=n(80442),Ke=n(70586);const $e=["feature","angle","bearing","centroid","envelopeintersects","extent","geometry","isselfintersecting","ringisclockwise"];let Ye=!1,Qe=!1,Xe=null,et=[];function tt(e,t){if(!0===t.useAsync||!0===e.isAsync)return function(e,t){if(null===Xe)throw new x.aV(null,x.rH.AsyncNotEnabled,null);if((0,We.Z)("esri-csp-restrictions"))return function(t){return Xe.executeScript(e,t)};try{return Z(e,t,!0)}catch(t){if("esri.arcade.arcadeuncompilableerror"===t.declaredRootClass)return function(t){return Xe.executeScript(e,t)};throw t}}(e,t);if((0,We.Z)("esri-csp-restrictions"))return function(t){return Je(e,t)};try{return Z(e,t)}catch(t){if("esri.arcade.arcadeuncompilableerror"===t.declaredRootClass)return function(t){return Je(e,t)};throw t}}function nt(e,t=[]){return Se(e,t)}function rt(e,t){if(!0===t.useAsync||!0===e.isAsync){if(null===Xe)throw new x.aV(null,x.rH.AsyncNotEnabled,null);return Xe.executeScript(e,t)}return Je(e,t)}function it(e,t){return(0,s.bV)(e,t)}function at(e){return(0,s.Kq)(e)}function st(e,t=[]){return void 0===e.usesGeometry&&(0,s.dN)(e,t),!0===e.usesGeometry}let ot=null;function ut(){return ot||(ot=lt(),ot)}async function lt(){const[e,t]=await Promise.all([Promise.all([n.e(5837),n.e(1433)]).then(n.bind(n,61433)),Promise.resolve().then(n.bind(n,18811))]);return Qe=!0,t.setGeometryEngine(e),!0}let ct=null;function dt(){return null!==ct||(ct=ht()),ct}async function ht(){await async function(){return H([await n.e(6748).then(n.bind(n,66748))],"async"),!0}(),Xe=await Promise.all([n.e(6748),n.e(8828)]).then(n.bind(n,18828));for(const e of et)Xe.extend(e),H(e,"async");return et=null,!0}function ft(){return Ye}function mt(){return!!Xe}function pt(){return Qe}let gt=null;function Dt(){return gt||(gt=yt(),gt)}async function yt(){await dt();const[e,t,r,i,a]=await Promise.all([Promise.all([n.e(2710),n.e(1612),n.e(4729),n.e(9790),n.e(8244),n.e(1223),n.e(1423),n.e(4165),n.e(911),n.e(4547),n.e(5546),n.e(1534),n.e(9942),n.e(9238),n.e(4599),n.e(5235),n.e(6584),n.e(5115)]).then(n.bind(n,50954)),Promise.all([n.e(2710),n.e(1612),n.e(4729),n.e(9790),n.e(8244),n.e(1223),n.e(1423),n.e(4165),n.e(911),n.e(4547),n.e(5546),n.e(1534),n.e(9942),n.e(9238),n.e(4599),n.e(5235),n.e(6584),n.e(5115),n.e(8758)]).then(n.bind(n,48758)),Promise.all([n.e(1534),n.e(6584),n.e(6565)]).then(n.bind(n,7301)),Promise.all([n.e(1534),n.e(1074)]).then(n.bind(n,59153)),n.e(565).then(n.bind(n,90565))]);return bt=e,Xe.extend([t,r,i,a]),H([t,r,i,a],"async"),Ye=!0,!0}function wt(e,t=[]){return void 0===e.usesFeatureSet&&(0,s.dN)(e,t),!0===e.usesFeatureSet}async function xt(e,t,n=[],r=!1,i=null){return Ft(new Set,e,t,n,r,i)}async function Ft(e,t,n,r=[],i=!1,a=null){const s="string"==typeof t?nt(t):t,o=[];return s&&(!1===pt()&&(st(s)||i)&&o.push(ut()),!1===mt()&&(!0===s.isAsync||n)&&o.push(dt()),!1===ft()&&(wt(s)||function(e,t){if(t){for(const n of t)if(it(e,n))return!0;return!1}return!1}(s,r))&&o.push(Dt())),o.length&&await Promise.all(o),await Ct(e,s,a,n,i),!0}async function Ct(e,t,n=null,r=!1,i=!1){const a=(0,s.x5)(t);null===n&&a.length>0&&(n=Te.getDefault()),t.loadedModules={};for(const s of a){(0,Ke.O3)(n);const a=n.normalizeModuleUri(s.source);if(e.has(a.uri))throw new x.aV(null,x.rH.CircularModules,null);e.add(a.uri);const o=await n.fetchModule(a);await Ft(e,o,r,[],i,n),e.delete(a.uri),o.isAsync&&(t.isAsync=!0),o.usesFeatureSet&&(t.usesFeatureSet=!0),o.usesGeometry&&(t.usesGeometry=!0),t.loadedModules[s.libname]={uri:a.uri,script:o}}}function At(e){if(st(e))return!0;const t=(0,s.Vf)(e);let n=!1;for(let e=0;e<t.length;e++)if($e.includes(t[e])){n=!0;break}return n}let bt=null;function Et(){return bt}const vt=Object.freeze(Object.defineProperty({__proto__:null,_loadScriptDependenciesImpl:Ft,compileScript:tt,enableAsyncSupport:dt,enableAsyncSupportImpl:ht,enableFeatureSetSupport:Dt,enableFeatureSetSupportImpl:yt,enableGeometrySupport:ut,enableGeometrySupportImpl:lt,executeScript:rt,extend:function(e){je(e),H(e,"sync"),null===Xe?et.push(e):(H(e,"async"),Xe.extend(e))},extractExpectedFieldLiterals:at,extractFieldLiterals:function(e,t=!1){return void 0===t&&(t=!1),(0,s.mb)(e)},featureSetUtils:Et,isAsyncEnabled:mt,isFeatureSetSupportEnabled:ft,isGeometryEnabled:pt,loadDependentModules:Ct,loadScriptDependencies:xt,parseAndExecuteScript:function(e,t,n=[]){return rt(Se(e,n),t)},parseScript:nt,referencesFunction:function(e,t){return(0,s.w8)(e,t)},referencesMember:it,scriptIsAsync:function(e,t=[]){return void 0===e.isAsync&&(0,s.dN)(e,t),!0===e.isAsync},scriptTouchesGeometry:At,scriptUsesFeatureSet:wt,scriptUsesGeometryEngine:st,scriptUsesModules:function(e,t=[]){return void 0===e.usesModules&&(0,s.dN)(e,t),!0===e.usesModules}},Symbol.toStringTag,{value:"Module"}));var St=n(22974),kt=n(51706),It=n(94139),Tt=n(38913),Bt=n(58901),_t=n(65091),Mt=n(6570);const Nt=/^\$(feature|aggregatedFeatures)\./i,Lt={vars:{$feature:"any",$view:"any"},spatialReference:null};function Ht(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&")}function Rt(e){return null==e?null:(0,a.m)(e)||(0,a.x)(e)?"array":(0,a.k)(e)?"date":(0,a.c)(e)?"text":(0,a.a)(e)?"boolean":(0,a.b)(e)?"number":e instanceof r.Z?"dictionary":(0,a.w)(e)?"feature":e instanceof It.Z?"point":e instanceof Tt.Z?"polygon":e instanceof Bt.Z?"polyline":e instanceof _t.Z?"multipoint":e instanceof Mt.Z?"extent":(0,a.T)(e)?"featureSet":(0,a.U)(e)?"featureSetCollection":null}function Ot(e){if(!e)return null;try{return nt(e)}catch(e){}return null}function Vt(e,t){const n="string"==typeof e?Ot(e):e;if(!n)return null;try{return tt(n,t=t||(0,St.d9)(Lt))}catch(e){}return null}function Pt(e,t){return{vars:{$feature:null==e?new i.Z:i.Z.createFromGraphic(e,null),$view:t&&t.view},spatialReference:t&&t.sr}}function Zt(e,t,n){return i.Z.createFromGraphicLikeObject(t,e,n,null)}function Ut(e,t){null!=e.vars&&(e.vars.$feature=t)}function Gt(e,t){let n;try{n=rt(e,t)}catch(e){n=null}return n}function zt(e,t){let n;try{n=e?e(t):null}catch(e){n=null}return n}function qt(e,t){try{return e?e(t):Promise.resolve(null)}catch(e){return Promise.resolve(null)}}function jt(e,t){if(!e)return[];const n="string"==typeof e?Ot(e):e;if(!n)return[];const r=at(n);let i=new Array;r.forEach((e=>{Nt.test(e)&&(e=e.replace(Nt,""),i.push(e))}));const a=i.filter((e=>e.includes("*")));return i=i.filter((e=>!a.includes(e))),t&&a.forEach((e=>{const n=new RegExp(`^${e.split(/\*+/).map(Ht).join(".*")}$`,"i");t.forEach((e=>n.test(e)?i.push(e):null))})),[...new Set(i.sort())]}function Jt(e){return it(e,"$view")}function Wt(e,t){return!!e&&it(e,t)}function Kt(e){if(e&&(null!=e.spatialReference||null!=e.scale&&null!=e.viewingMode))return{view:e.viewingMode&&null!=e.scale?new r.Z({viewingMode:e.viewingMode,scale:e.scale}):null,sr:e.spatialReference}}function $t({url:e,spatialReference:t,lrucache:n,interceptor:r}){const i=Et();return i?i.createFeatureSetCollectionFromService(e,t,n,r):null}function Yt({layer:e,spatialReference:t,outFields:n,returnGeometry:r,lrucache:i,interceptor:a}){if(null===e)return null;const s=Et();return s?s.constructFeatureSet(e,t,n,r??!0,i,a):null}function Qt(e){if(null===e?.map)return null;const t=Et();return t?t.createFeatureSetCollectionFromMap(e.map,e.spatialReference,e.lrucache,e.interceptor):null}function Xt(e,t){return r.Z.convertJsonToArcade(e,t)}function en(e,t,n=[]){return xt(e,t,n)}function tn(){return ut()}function nn(){return Dt()}function rn(e,t){if(!e)return!1;if("string"==typeof e)return t(e);const n=e;if(function(e){return"simple"===e.type||"class-breaks"===e.type||"unique-value"===e.type||"dot-density"===e.type||"dictionary"===e.type||"pie-chart"===e.type}(n)){if("dot-density"===n.type){const e=n.attributes?.some((e=>t(e.valueExpression)));if(e)return e}const e=n.visualVariables,r=!!e&&e.some((e=>{let n=t(e.valueExpression);return"size"===e.type&&((0,kt.iY)(e.minSize)&&(n=n||t(e.minSize.valueExpression)),(0,kt.iY)(e.maxSize)&&(n=n||t(e.maxSize.valueExpression))),n}));return!(!("valueExpression"in n)||!t(n.valueExpression))||r}if(function(e){return"esri.layers.support.LabelClass"===e.declaredClass}(n)){const e=n.labelExpressionInfo&&n.labelExpressionInfo.expression;return!(!e||!t(e))||!1}return!!function(e){return"esri.PopupTemplate"===e.declaredClass}(n)&&(!!n.expressionInfos&&n.expressionInfos.some((e=>t(e.expression)))||Array.isArray(n.content)&&n.content.some((e=>"expression"===e.type&&t(e.expressionInfo?.expression))))}function an(e){const t=Ot(e);return!!t&&At(t)}function sn(e){return rn(e,an)}function on(e){const t=Ot(e);return!!t&&st(t)}function un(e){return rn(e,on)}},85958:(e,t,n)=>{n.d(t,{Hu:()=>c,fY:()=>u,jH:()=>d,jz:()=>h});var r=n(68773),i=n(80442),a=n(70586),s=n(95330),o=n(17452);function u(e,t,n=!1,r){return new Promise(((o,u)=>{if((0,s.Hc)(r))return void u(l());let c=()=>{f(),u(new Error(`Unable to load ${t}`))},d=()=>{const t=e;f(),o(t)},h=()=>{if(!e)return;const t=e;f(),t.src="",u(l())};const f=()=>{(0,i.Z)("esri-image-decode")||(e.removeEventListener("error",c),e.removeEventListener("load",d)),c=null,d=null,e=null,(0,a.pC)(r)&&r.removeEventListener("abort",h),h=null,n&&URL.revokeObjectURL(t)};(0,a.pC)(r)&&r.addEventListener("abort",h),(0,i.Z)("esri-image-decode")?e.decode().then(d,c):(e.addEventListener("error",c),e.addEventListener("load",d))}))}function l(){try{return new DOMException("Aborted","AbortError")}catch{const e=new Error;return e.name="AbortError",e}}function c(e){r.Z.request.crossOriginNoCorsDomains||(r.Z.request.crossOriginNoCorsDomains={});const t=r.Z.request.crossOriginNoCorsDomains;for(let n of e)n=n.toLowerCase(),/^https?:\/\//.test(n)?t[(0,o.P$)(n)??""]=0:(t[(0,o.P$)("http://"+n)??""]=0,t[(0,o.P$)("https://"+n)??""]=0)}function d(e){const t=r.Z.request.crossOriginNoCorsDomains;if(t){let n=(0,o.P$)(e);if(n)return n=n.toLowerCase(),!(0,o.D6)(n,(0,o.TI)())&&t[n]<Date.now()-36e5}return!1}async function h(e){const t=r.Z.request.crossOriginNoCorsDomains,n=(0,o.P$)(e);t&&n&&(t[n.toLowerCase()]=Date.now());const i=(0,o.mN)(e);e=i.path,"json"===i.query?.f&&(e+="?f=json");try{await fetch(e,{mode:"no-cors",credentials:"include"})}catch{}}}}]);