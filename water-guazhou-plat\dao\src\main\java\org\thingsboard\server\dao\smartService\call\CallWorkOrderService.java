package org.thingsboard.server.dao.smartService.call;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrder;
import org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrderRemind;
import org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrderRevisit;

import java.util.List;
import java.util.Map;

/**
 * 黑名单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface CallWorkOrderService {
    PageData getList(String isDispatch, String seatsId, String type, String topic, String departmentId, String status, String chaoShi, String cuiBan, String zhongZhi, String manYi, String zenRenFang, String jinChang, String chongFa, String heGe, String wuPan, String serialNo, String keywords, String phone, Long startTime, Long endTime, int page, int size, String tenantId);

    CallWorkOrder save(CallWorkOrder callWorkOrder);

    int delete(List<String> ids);

    Map getDetails(String workOrderId);

    CallWorkOrderRevisit revisit(CallWorkOrderRevisit callWorkOrderRevisit);

    CallWorkOrderRemind remind(CallWorkOrderRemind callWorkOrderRemind);

    Object getWorkOrderStatus();

    List getMonthCallLogStatistics(String seatsId, String year, String tenantId);

    List getYearCallLogStatistics(String start, String end, String tenantId);

    PageData getCallDaily(String seatsId, String start, String end, int page, int size, String tenantId);

    List getWorkMonth(String start, String end, String tenantId);

    Map getWorkArea(String start, String end, String tenantId);

    List getSeatsMonth(String type, String year, String tenantId);

    List getSeatsCall(String start, String end, String tenantId);

    PageData getIvr(String start, String end, Integer page, Integer size, String tenantId);

    List getCallSource(String start, String end, String tenantId);

    PageData getServiceDetail(String type, String year, String start, String end, Integer page, Integer size, String tenantId);

    Map getHotLine(String type, String time, String tenantId);
}
