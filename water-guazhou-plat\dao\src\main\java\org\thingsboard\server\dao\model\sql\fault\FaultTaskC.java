package org.thingsboard.server.dao.model.sql.fault;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 保养任务子表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@TableName("tb_device_fault_task_c")
@Data
public class FaultTaskC {
    @TableId
    private String id;

    private String mainId;

    private String deviceLabelCode;

    private transient String typeId;

    private transient String type;

    private transient String topType;

    private transient String linkedType;

    private transient String name;

    private transient String serialId;

    private transient String model;

    private transient String installAddressName;

    private transient String detailInstallAddressName;

    private transient Date lastModifyTime;

    private String img;

    private String remark;

    private String status;

    private Date time;

    private Date createTime;

    private String tenantId;


}
