import{b as M,n as le}from"./WGLContainer-Dyx9110G.js";import{a4 as zt,T as K,aS as ce,R as F,cl as Ct,a_ as ue,aW as it}from"./index-r0dFAfgr.js";import{U as Kt}from"./pe-B8dP0-Ut.js";import{e as de,f as me,b as _e}from"./cimAnalyzer-CMgqZsaO.js";import{ab as pe,ak as ge,an as fe,s as C,aq as S,i as te,S as we,j as Et,e as p,y as f,a as q,v as Z,w as _t}from"./Point-WxyopZva.js";import{o as ye}from"./fontUtils-BuXIMW9g.js";import{hp as ve,gr as be,e0 as tt,bv as Ft,cx as G,h$ as Me,b1 as $,i0 as R,dP as xe,f as $e,i1 as Se,br as At,bq as Te,af as ee,aR as ze,aT as De,aP as ie,i2 as pt,i3 as ut,dD as Ie,ag as Re,i4 as Pe,fZ as Ce,i5 as Ee,aA as et,i6 as Fe,i7 as $t,i8 as Ae,i9 as Ve,ia as Vt}from"./MapView-DaoQedLH.js";import{c as Be}from"./Rasterizer-CuAuGNQK.js";import{e as v,z as Bt,y as kt,j as ke,U as Le,V as Ne}from"./definitions-826PWLuy.js";import{_ as st,T as Ue}from"./enums-L38xj_2E.js";import{t as b}from"./Rect-CUzevAry.js";import{P as dt,G as Dt,L as St,R as k,M as Oe,D as Ge}from"./enums-BDQrMlcz.js";import{E as It}from"./Texture-BYqObwfn.js";import{o as qe}from"./floatRGBA-PQQNbO39.js";import{e as Lt}from"./GeometryUtils-BRRfazic.js";import{u as Ze,p as He,l as We,n as Nt,r as Ye,I as je,f as at,s as Ut,d as Ot,i as Qe,o as Xe,m as Je}from"./color-DAS1c3my.js";import{c as Ke}from"./imageutils-KgbVacIV.js";import{d as I}from"./enums-B5k73o5q.js";import{b as ti}from"./Matcher-v9ErZwmD.js";import{s as ei}from"./CircularArray-CFz2ft5h.js";import{c as ii,f as se}from"./widget-BcWKanF2.js";import{T as si}from"./webgl-debug-BJuvLAW9.js";import{t as ai}from"./ComputedAttributeStorage-CF7WDnl8.js";import{a as ni}from"./ProgramTemplate-tdUBoAol.js";const $s={shaders:{vertexShader:M("bitBlit/bitBlit.vert"),fragmentShader:M("bitBlit/bitBlit.frag")},attributes:new Map([["a_pos",0],["a_tex",1]])};let mt=class{constructor(t,e){this._width=0,this._height=0,this._free=[],this._width=t,this._height=e,this._free.push(new b(0,0,t,e))}get width(){return this._width}get height(){return this._height}allocate(t,e){if(t>this._width||e>this._height)return new b;let i=null,a=-1;for(let s=0;s<this._free.length;++s){const n=this._free[s];t<=n.width&&e<=n.height&&(i===null||n.y<=i.y&&n.x<=i.x)&&(i=n,a=s)}return i===null?new b:(this._free.splice(a,1),i.width<i.height?(i.width>t&&this._free.push(new b(i.x+t,i.y,i.width-t,e)),i.height>e&&this._free.push(new b(i.x,i.y+e,i.width,i.height-e))):(i.width>t&&this._free.push(new b(i.x+t,i.y,i.width-t,i.height)),i.height>e&&this._free.push(new b(i.x,i.y+e,t,i.height-e))),new b(i.x,i.y,t,e))}release(t){for(let e=0;e<this._free.length;++e){const i=this._free[e];if(i.y===t.y&&i.height===t.height&&i.x+i.width===t.x)i.width+=t.width;else if(i.x===t.x&&i.width===t.width&&i.y+i.height===t.y)i.height+=t.height;else if(t.y===i.y&&t.height===i.height&&t.x+t.width===i.x)i.x=t.x,i.width+=t.width;else{if(t.x!==i.x||t.width!==i.width||t.y+t.height!==i.y)continue;i.y=t.y,i.height+=t.height}this._free.splice(e,1),this.release(t)}this._free.push(t)}};const ri=256,oi=r=>Math.floor(r/256);function hi(r){const t=new Set;for(const e of r)t.add(oi(e));return t}function li(r,t,e){return r.has(t)||r.set(t,e().then(()=>{r.delete(t)}).catch(i=>{r.delete(t),pe(i)})),r.get(t)}const ci=r=>({rect:new b(0,0,0,0),page:0,metrics:{left:0,width:0,height:0,advance:0,top:0},code:r,sdf:!0});let ui=class{constructor(t,e,i){this.width=0,this.height=0,this._dirties=[],this._glyphData=[],this._currentPage=0,this._glyphCache={},this._textures=[],this._rangePromises=new Map,this.width=t,this.height=e,this._glyphSource=i,this._binPack=new mt(t-4,e-4),this._glyphData.push(new Uint8Array(t*e)),this._dirties.push(!0),this._textures.push(null),this._initDecorationGlyph()}dispose(){this._binPack=null;for(const t of this._textures)t&&t.dispose();this._textures.length=0,this._glyphData.length=0}_initDecorationGlyph(){const t=[117,149,181,207,207,181,149,117],e=[];for(let a=0;a<t.length;a++){const s=t[a];for(let n=0;n<11;n++)e.push(s)}const i={metrics:{width:5,height:2,left:0,top:0,advance:0},bitmap:new Uint8Array(e)};this._recordGlyph(i)}async getGlyphItems(t,e,i){const a=this._getGlyphCache(t);return await this._fetchRanges(t,e,i),e.map(s=>this._getMosaicItem(a,t,s))}bind(t,e,i,a){const s=this._getTexture(t,i);s.setSamplingMode(e),this._dirties[i]&&(s.setData(this._glyphData[i]),this._dirties[i]=!1),t.bindTexture(s,a)}_getGlyphCache(t){return this._glyphCache[t]||(this._glyphCache[t]={}),this._glyphCache[t]}_getTexture(t,e){return this._textures[e]||(this._textures[e]=new It(t,{pixelFormat:dt.ALPHA,dataType:Dt.UNSIGNED_BYTE,width:this.width,height:this.height},new Uint8Array(this.width*this.height))),this._textures[e]}_invalidate(){this._dirties[this._currentPage]=!0}async _fetchRanges(t,e,i){const a=hi(e),s=[];a.forEach(n=>{s.push(this._fetchRange(t,n,i))}),await Promise.all(s)}async _fetchRange(t,e,i){if(e>ri)return;const a=t+e;return li(this._rangePromises,a,()=>this._glyphSource.getRange(t,e,i))}_getMosaicItem(t,e,i){if(!t[i]){const a=this._glyphSource.getGlyph(e,i);if(!a||!a.metrics)return ci(i);const s=this._recordGlyph(a),n=this._currentPage,o=a.metrics;t[i]={rect:s,page:n,metrics:o,code:i,sdf:!0},this._invalidate()}return t[i]}_recordGlyph(t){const e=t.metrics;let i;if(e.width===0)i=new b(0,0,0,0);else{const s=e.width+6,n=e.height+2*3;i=this._binPack.allocate(s,n),i.isEmpty&&(this._dirties[this._currentPage]||(this._glyphData[this._currentPage]=null),this._currentPage=this._glyphData.length,this._glyphData.push(new Uint8Array(this.width*this.height)),this._dirties.push(!0),this._textures.push(null),this._initDecorationGlyph(),this._binPack=new mt(this.width-4,this.height-4),i=this._binPack.allocate(s,n));const o=this._glyphData[this._currentPage],h=t.bitmap;let l,c;if(h)for(let u=0;u<n;u++){l=s*u,c=this.width*(i.y+u)+i.x;for(let d=0;d<s;d++)o[c+d]=h[l+d]}zt("esri-glyph-debug")&&this._showDebugPage(o)}return i}_showDebugPage(t){const e=document.createElement("canvas"),i=e.getContext("2d"),a=new ImageData(this.width,this.height),s=a.data;e.width=this.width,e.height=this.height,e.style.border="1px solid black";for(let n=0;n<t.length;++n)s[4*n+0]=t[n],s[4*n+1]=0,s[4*n+2]=0,s[4*n+3]=255;i.putImageData(a,0,0),document.body.appendChild(e)}},di=class{constructor(t){for(this._metrics=[],this._bitmaps=[];t.next();)switch(t.tag()){case 1:{const e=t.getMessage();for(;e.next();)switch(e.tag()){case 3:{const i=e.getMessage();let a,s,n,o,h,l,c;for(;i.next();)switch(i.tag()){case 1:a=i.getUInt32();break;case 2:s=i.getBytes();break;case 3:n=i.getUInt32();break;case 4:o=i.getUInt32();break;case 5:h=i.getSInt32();break;case 6:l=i.getSInt32();break;case 7:c=i.getUInt32();break;default:i.skip()}i.release(),a&&(this._metrics[a]={width:n,height:o,left:h,top:l,advance:c},this._bitmaps[a]=s);break}default:e.skip()}e.release();break}default:t.skip()}}getMetrics(t){return this._metrics[t]}getBitmap(t){return this._bitmaps[t]}},mi=class{constructor(){this._ranges=[]}getRange(t){return this._ranges[t]}addRange(t,e){this._ranges[t]=e}},_i=class{constructor(t){this._glyphInfo={},this._baseURL=t}getRange(t,e,i){const a=this._getFontStack(t);if(a.getRange(e))return Promise.resolve();const s=256*e,n=s+255,o=this._baseURL.replace("{fontstack}",t).replace("{range}",s+"-"+n);return Kt(o,{responseType:"array-buffer",...i}).then(h=>{a.addRange(e,new di(new ve(new Uint8Array(h.data),new DataView(h.data))))})}getGlyph(t,e){const i=this._getFontStack(t);if(!i)return;const a=Math.floor(e/256);if(a>256)return;const s=i.getRange(a);return s?{metrics:s.getMetrics(e),bitmap:s.getBitmap(e)}:void 0}_getFontStack(t){let e=this._glyphInfo[t];return e||(e=this._glyphInfo[t]=new mi),e}};const H=1e20;let pi=class{constructor(t){this._svg=null,this.size=t;const e=document.createElement("canvas");e.width=e.height=t,this._context=e.getContext("2d"),this._gridOuter=new Float64Array(t*t),this._gridInner=new Float64Array(t*t),this._f=new Float64Array(t),this._d=new Float64Array(t),this._z=new Float64Array(t+1),this._v=new Int16Array(t)}dispose(){this._context=this._gridOuter=this._gridInner=this._f=this._d=this._z=this._v=null,this._svg&&(document.body.removeChild(this._svg),this._svg=null)}draw(t,e,i=31){this._initSVG();const a=this.createSVGString(t);return new Promise((s,n)=>{const o=new Image;o.src="data:image/svg+xml; charset=utf8, "+encodeURIComponent(a),o.onload=()=>{o.onload=null,this._context.clearRect(0,0,this.size,this.size),this._context.drawImage(o,0,0,this.size,this.size);const l=this._context.getImageData(0,0,this.size,this.size),c=new Uint8Array(this.size*this.size*4);for(let u=0;u<this.size*this.size;u++){const d=l.data[4*u+3]/255;this._gridOuter[u]=d===1?0:d===0?H:Math.max(0,.5-d)**2,this._gridInner[u]=d===1?H:d===0?0:Math.max(0,d-.5)**2}this._edt(this._gridOuter,this.size,this.size),this._edt(this._gridInner,this.size,this.size);for(let u=0;u<this.size*this.size;u++){const d=this._gridOuter[u]-this._gridInner[u];qe(.5-d/(2*i),c,4*u)}s(c)};const h=e&&e.signal;h&&ge(h,()=>n(fe()))})}_initSVG(){if(!this._svg){const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("style","position: absolute;"),t.setAttribute("width","0"),t.setAttribute("height","0"),t.setAttribute("aria-hidden","true"),t.setAttribute("role","presentation"),document.body.appendChild(t),this._svg=t}return this._svg}createSVGString(t){const e=this._initSVG(),i=document.createElementNS("http://www.w3.org/2000/svg","path");i.setAttribute("d",t),e.appendChild(i);const a=i.getBBox(),s=a.width/a.height,n=this.size/2;let o,h,l,c;if(s>1){h=o=n/a.width;const _=n*(1/s);l=this.size/4,c=n-_/2}else o=h=n/a.height,l=n-n*s/2,c=this.size/4;const u=-a.x*o+l,d=-a.y*h+c;i.setAttribute("style",`transform: matrix(${o}, 0, 0, ${h}, ${u}, ${d})`);const m=`<svg style="fill:red;" height="${this.size}" width="${this.size}" xmlns="http://www.w3.org/2000/svg">${e.innerHTML}</svg>`;return e.removeChild(i),m}_edt(t,e,i){const a=this._f,s=this._d,n=this._v,o=this._z;for(let h=0;h<e;h++){for(let l=0;l<i;l++)a[l]=t[l*e+h];this._edt1d(a,s,n,o,i);for(let l=0;l<i;l++)t[l*e+h]=s[l]}for(let h=0;h<i;h++){for(let l=0;l<e;l++)a[l]=t[h*e+l];this._edt1d(a,s,n,o,e);for(let l=0;l<e;l++)t[h*e+l]=Math.sqrt(s[l])}}_edt1d(t,e,i,a,s){i[0]=0,a[0]=-H,a[1]=+H;for(let n=1,o=0;n<s;n++){let h=(t[n]+n*n-(t[i[o]]+i[o]*i[o]))/(2*n-2*i[o]);for(;h<=a[o];)o--,h=(t[n]+n*n-(t[i[o]]+i[o]*i[o]))/(2*n-2*i[o]);o++,i[o]=n,a[o]=h,a[o+1]=+H}for(let n=0,o=0;n<s;n++){for(;a[o+1]<n;)o++;e[n]=(n-i[o])*(n-i[o])+t[i[o]]}}};function L(r){return r&&r.type==="static"}let gi=class ae{constructor(t,e,i=0){this._mosaicPages=[],this._maxItemSize=0,this._currentPage=0,this._pageWidth=0,this._pageHeight=0,this._mosaicRects=new Map,this._spriteCopyQueue=[],this.pixelRatio=1,(t<=0||e<=0)&&console.error("Sprites mosaic defaultWidth and defaultHeight must be greater than zero!"),this._pageWidth=t,this._pageHeight=e,i>0&&(this._maxItemSize=i),this.pixelRatio=window.devicePixelRatio||1,this._binPack=new mt(this._pageWidth,this._pageHeight);const a=Math.floor(this._pageWidth),s=Math.floor(this._pageHeight);this._mosaicPages.push({mosaicsData:{type:"static",data:new Uint32Array(a*s)},size:[this._pageWidth,this._pageHeight],dirty:!0,texture:void 0})}getWidth(t){return t>=this._mosaicPages.length?-1:this._mosaicPages[t].size[0]}getHeight(t){return t>=this._mosaicPages.length?-1:this._mosaicPages[t].size[1]}getPageTexture(t){return t<this._mosaicPages.length?this._mosaicPages[t].texture:null}has(t){return this._mosaicRects.has(t)}get itemCount(){return this._mosaicRects.size}getSpriteItem(t){return this._mosaicRects.get(t)}addSpriteItem(t,e,i,a,s,n,o=1){if(this._mosaicRects.has(t))return this._mosaicRects.get(t);let h,l,c;if(L(i)?[h,l,c]=this._allocateImage(e[0],e[1]):(h=new b(0,0,e[0],e[1]),l=this._mosaicPages.length,this._mosaicPages.push({mosaicsData:i,size:[e[0]+2*v,e[1]+2*v],dirty:!0,texture:void 0})),h.width<=0||h.height<=0)return null;const u={rect:h,width:e[0],height:e[1],sdf:s,simplePattern:n,pixelRatio:o,page:l};return this._mosaicRects.set(t,u),L(i)&&this._copy({rect:h,spriteSize:e,spriteData:i.data,page:l,pageSize:c,repeat:a,sdf:s}),u}hasItemsToProcess(){return this._spriteCopyQueue.length!==0}processNextItem(){const t=this._spriteCopyQueue.pop();t&&this._copy(t)}getSpriteItems(t){const e={};for(const i of t)e[i]=this.getSpriteItem(i);return e}getMosaicItemPosition(t){const e=this.getSpriteItem(t),i=e&&e.rect;if(!i)return null;i.width=e.width,i.height=e.height;const a=e.width,s=e.height,n=v,o=this._mosaicPages[e.page];return{size:[e.width,e.height],tl:[(i.x+n)/o[0],(i.y+n)/o[1]],br:[(i.x+n+a)/o[0],(i.y+n+s)/o[1]],page:e.page}}bind(t,e,i=0,a=0){const s=this._mosaicPages[i],n=s.mosaicsData;let o=s.texture;o||(o=fi(t,s.size),s.texture=o),o.setSamplingMode(e),L(n)?(t.bindTexture(o,a),s.dirty&&(o.setData(new Uint8Array(n.data.buffer)),o.generateMipmap())):(n.data.bindFrame(t,o,a),o.generateMipmap()),s.dirty=!1}static _copyBits(t,e,i,a,s,n,o,h,l,c,u){let d=a*e+i,m=h*n+o;if(u){m-=n;for(let _=-1;_<=c;_++,d=((_+c)%c+a)*e+i,m+=n)for(let g=-1;g<=l;g++)s[m+g]=t[d+(g+l)%l]}else for(let _=0;_<c;_++){for(let g=0;g<l;g++)s[m+g]=t[d+g];d+=e,m+=n}}_copy(t){if(t.page>=this._mosaicPages.length)return;const e=this._mosaicPages[t.page],i=e.mosaicsData;if(!L(e.mosaicsData))throw new C("mapview-invalid-resource","unsuitable data type!");const a=t.spriteData,s=i.data;s&&a||console.error("Source or target images are uninitialized!"),ae._copyBits(a,t.spriteSize[0],0,0,s,t.pageSize[0],t.rect.x+v,t.rect.y+v,t.spriteSize[0],t.spriteSize[1],t.repeat),e.dirty=!0}_allocateImage(t,e){t+=2*v,e+=2*v;const i=Math.max(t,e);if(this._maxItemSize&&this._maxItemSize<i){const s=2**Math.ceil(Lt(t)),n=2**Math.ceil(Lt(e)),o=new b(0,0,t,e);return this._mosaicPages.push({mosaicsData:{type:"static",data:new Uint32Array(s*n)},size:[s,n],dirty:!0,texture:void 0}),[o,this._mosaicPages.length-1,[s,n]]}const a=this._binPack.allocate(t,e);if(a.width<=0){const s=this._mosaicPages[this._currentPage];return!s.dirty&&L(s.mosaicsData)&&(s.mosaicsData.data=null),this._currentPage=this._mosaicPages.length,this._mosaicPages.push({mosaicsData:{type:"static",data:new Uint32Array(this._pageWidth*this._pageHeight)},size:[this._pageWidth,this._pageHeight],dirty:!0,texture:void 0}),this._binPack=new mt(this._pageWidth,this._pageHeight),this._allocateImage(t,e)}return[a,this._currentPage,[this._pageWidth,this._pageHeight]]}dispose(){this._binPack=null;for(const t of this._mosaicPages){const e=t.texture;e&&e.dispose();const i=t.mosaicsData;L(i)||i.data.destroy()}this._mosaicPages=null,this._mosaicRects.clear()}};function fi(r,t){return new It(r,{pixelFormat:dt.RGBA,dataType:Dt.UNSIGNED_BYTE,width:t[0],height:t[1]},null)}function ne(r){return S(r.frameDurations.reduce((t,e)=>t+e,0))}function wi(r){const{width:t,height:e}=r;return{frameDurations:r.frameDurations.reverse(),getFrame:i=>{const a=r.frameDurations.length-1-i;return r.getFrame(a)},width:t,height:e}}function yi(r,t){const{width:e,height:i,getFrame:a}=r,s=t/ne(r);return{frameDurations:r.frameDurations.map(n=>S(n*s)),getFrame:a,width:e,height:i}}function vi(r,t){const{width:e,height:i,getFrame:a}=r,s=r.frameDurations.slice(),n=s.shift();return s.unshift(S(n+t)),{frameDurations:s,getFrame:a,width:e,height:i}}function Gt(r,t){const{width:e,height:i,getFrame:a}=r,s=r.frameDurations.slice(),n=s.pop();return s.push(S(n+t)),{frameDurations:s,getFrame:a,width:e,height:i}}let bi=class{constructor(t,e,i,a){this._animation=t,this._repeatType=i,this._onFrameData=a,this._direction=1,this._currentFrame=0,this.timeToFrame=this._animation.frameDurations[this._currentFrame];let s=0;for(;e>s;)s+=this.timeToFrame,this.nextFrame();const n=this._animation.getFrame(this._currentFrame);this._onFrameData(n)}nextFrame(){if(this._currentFrame+=this._direction,this._direction>0){if(this._currentFrame===this._animation.frameDurations.length)switch(this._repeatType){case I.None:this._currentFrame-=this._direction;break;case I.Loop:this._currentFrame=0;break;case I.Oscillate:this._currentFrame-=this._direction,this._direction=-1}}else if(this._currentFrame===-1)switch(this._repeatType){case I.None:this._currentFrame-=this._direction;break;case I.Loop:this._currentFrame=this._animation.frameDurations.length-1;break;case I.Oscillate:this._currentFrame-=this._direction,this._direction=1}this.timeToFrame=this._animation.frameDurations[this._currentFrame];const t=this._animation.getFrame(this._currentFrame);this._onFrameData(t)}};function Mi(r,t,e,i){let a,{repeatType:s}=t;if(s==null&&(s=I.Loop),t.reverseAnimation===!0&&(r=wi(r)),t.duration!=null&&(r=yi(r,S(1e3*t.duration))),t.repeatDelay!=null){const n=1e3*t.repeatDelay;s===I.Loop?r=Gt(r,S(n)):s===I.Oscillate&&(r=vi(Gt(r,S(n/2)),S(n/2)))}if(t.startTimeOffset!=null)a=S(1e3*t.startTimeOffset);else if(t.randomizeStartTime!=null){const n=de(e),o=82749913,h=t.randomizeStartSeed!=null?t.randomizeStartSeed:o,l=me(n,h);a=S(l*ne(r))}else a=S(0);return new bi(r,a,s,i)}function xi(r,t,e,i){const a=t.playAnimation==null||t.playAnimation,s=Mi(r,t,e,i);let n,o=s.timeToFrame;function h(){n=a?setTimeout(()=>{s.nextFrame(),o=s.timeToFrame,h()},o):void 0}return h(),{remove:()=>{a&&clearTimeout(n)}}}const Tt=document.createElement("canvas"),nt=Tt.getContext("2d");function $i(r,t,e){Tt.width=t,Tt.height=e;const i=[],a=r.frameDurations.length;for(let s=0;s<a;s++){const n=r.getFrame(s);nt.clearRect(0,0,t,e),n instanceof ImageData?nt.drawImage(Ke(n),0,0,t,e):nt.drawImage(n,0,0,t,e),i.push(nt.getImageData(0,0,t,e))}return{width:t,height:e,frameDurations:r.frameDurations,getFrame:s=>i[s]}}let Si=class{constructor(t,e,i,a){this._animation=t,this._frameData=null;const s=n=>{this._frameData=n,e.requestRender()};this.frameCount=this._animation.frameDurations.length,this.width=this._animation.width,this.height=this._animation.height,this._playHandle=xi(this._animation,i,a,s)}destroy(){this._playHandle.remove()}bindFrame(t,e,i){t.bindTexture(e,i),K(this._frameData)||(e.updateData(0,v,v,this._frameData.width,this._frameData.height,this._frameData),this._frameData=null)}};function Ti(r){switch(r.type){case"esriSMS":return`${r.style}.${r.path}`;case"esriSLS":return`${r.style}.${r.cap}`;case"esriSFS":return`${r.style}`;case"esriPFS":case"esriPMS":return r.imageData?`${r.imageData}${r.width}${r.height}`:`${r.url}${r.width}${r.height}`;default:return"mosaicHash"in r?r.mosaicHash:JSON.stringify(r)}}const qt=Me(),Zt="arial-unicode-ms-regular",ft=126,re=te.getLogger("esri.views.2d.engine.webgl.TextureManager");function Ht(r,t){const e=Math.round(G(t)*window.devicePixelRatio),i=e>=128?2:4;return Math.min(r,e*i)}const wt=(r,t,e)=>re.error(new C(r,t,e));class Rt{static fromMosaic(t,e){return new Rt(t,e.page,e.sdf)}constructor(t,e,i){this.mosaicType=t,this.page=e,this.sdf=i}}class Es{constructor(t,e,i){this._requestRender=t,this.resourceManager=e,this._allowNonPowerOfTwo=i,this._invalidFontsMap=new Map,this._sdfConverter=new pi(ft),this._bindingInfos=new Array,this._hashToBindingIndex=new Map,this._ongoingRasterizations=new Map,this._imageRequestQueue=new be({concurrency:10,process:async(a,s)=>{we(s);try{return await Kt(a,{responseType:"image",signal:s})}catch(n){throw Et(n)?n:new C("mapview-invalid-resource",`Could not fetch requested resource at ${a}`,n)}}}),this._spriteMosaic=new gi(2048,2048,500),this._glyphSource=new _i(`${ce.fontsUrl}/{fontstack}/{range}.pbf`),this._glyphMosaic=new ui(1024,1024,this._glyphSource),this._rasterizer=new Be(e)}dispose(){this._spriteMosaic.dispose(),this._glyphMosaic.dispose(),this._rasterizer.dispose(),this._sdfConverter.dispose(),this._spriteMosaic=null,this._glyphMosaic=null,this._sdfConverter=null,this._hashToBindingIndex.clear(),this._hashToBindingIndex=null,this._bindingInfos=null,this._ongoingRasterizations.clear(),this._ongoingRasterizations=null,this._imageRequestQueue.clear(),this._imageRequestQueue=null}get sprites(){return this._spriteMosaic}get glyphs(){return this._glyphMosaic}async rasterizeItem(t,e,i,a){if(K(t))return wt("mapview-null-resource","Unable to rasterize null resource"),null;switch(t.type){case"text":case"esriTS":{const s=await this._rasterizeText(t,i,a);return s.forEach(n=>this._setTextureBinding(st.GLYPH,n)),{glyphMosaicItems:s}}default:{if(Ze(t))return wt("mapview-invalid-type",`MapView does not support symbol type: ${t.type}`,t),null;const s=await this._rasterizeSpriteSymbol(t,e,a);return ti(s)&&s&&this._setTextureBinding(st.SPRITE,s),{spriteMosaicItem:s}}}}bindTextures(t,e,i,a=!1){if(i.textureBinding===0)return;const s=this._bindingInfos[i.textureBinding-1],n=s.page,o=a?St.LINEAR_MIPMAP_LINEAR:St.LINEAR;switch(s.mosaicType){case st.SPRITE:{const h=this.sprites.getWidth(n),l=this.sprites.getHeight(n),c=tt(qt,h,l);return this._spriteMosaic.bind(t,o,n,kt),e.setUniform1i("u_texture",kt),void e.setUniform2fv("u_mosaicSize",c)}case st.GLYPH:{const h=this.glyphs.width,l=this.glyphs.height,c=tt(qt,h,l);return this._glyphMosaic.bind(t,o,n,Bt),e.setUniform1i("u_texture",Bt),void e.setUniform2fv("u_mosaicSize",c)}default:re.error("mapview-texture-manager",`Cannot handle unknown type ${s.mosaicType}`)}}_hashMosaic(t,e){return 1|t<<1|(e.sdf?1:0)<<2|e.page<<3}_setTextureBinding(t,e){const i=this._hashMosaic(t,e);if(!this._hashToBindingIndex.has(i)){const a=Rt.fromMosaic(t,e),s=this._bindingInfos.length+1;this._hashToBindingIndex.set(i,s),this._bindingInfos.push(a)}e.textureBinding=this._hashToBindingIndex.get(i)}async _rasterizeText(t,e,i){let a,s;if("cim"in t){const h=t;a=h.fontName,s=h.text}else{const h=t;a=ye(h.font),s=h.text}const n=this._invalidFontsMap.has(a),o=e||He(_e(s)[0]);try{return await this._glyphMosaic.getGlyphItems(n?Zt:a,o,i)}catch{return wt("mapview-invalid-resource",`Couldn't find font ${a}. Falling back to Arial Unicode MS Regular`),this._invalidFontsMap.set(a,!0),this._glyphMosaic.getGlyphItems(Zt,o,i)}}async _rasterizeSpriteSymbol(t,e,i){if(We(t))return;const a=Ti(t);if(this._spriteMosaic.has(a))return this._spriteMosaic.getSpriteItem(a);if(Nt(t)||Ye(t)&&!je(t))return this._handleAsyncResource(a,t,i);const s=ke,n=this._rasterizer.rasterizeJSONResource(t,s);if(n){const{size:o,image:h,sdf:l,simplePattern:c,rasterizationScale:u}=n;return this._addItemToMosaic(a,o,{type:"static",data:h},at(t),l,c,u)}return new C("TextureManager","unrecognized or null rasterized image")}async _handleAsyncResource(t,e,i){if(this._ongoingRasterizations.has(t))return this._ongoingRasterizations.get(t);let a;a=Nt(e)?this._handleSVG(e,t,i):this._handleImage(e,t,i),this._ongoingRasterizations.set(t,a);try{await a,this._ongoingRasterizations.delete(t)}catch{this._ongoingRasterizations.delete(t)}return a}async _handleSVG(t,e,i){const a=[ft,ft],s=await this._sdfConverter.draw(t.path,i);return this._addItemToMosaic(e,a,{type:"static",data:new Uint32Array(s.buffer)},!1,!0,!0)}async _handleGIFOrPNG(t,e,i){const a=Ut(t);await this.resourceManager.fetchResource(a,i);let s=this.resourceManager.getResource(a);if(K(s))return new C("mapview-invalid-resource",`Could not fetch requested resource at ${a}.`);let n=s.width,o=s.height;if(s instanceof HTMLImageElement){t.type==="esriPMS"&&(n=Math.round(Ht(s.width,Ot(t))),o=Math.round(s.height*(n/s.width)));const u="cim"in t?t.cim.colorSubstitutions:void 0,{size:d,sdf:m,image:_}=this._rasterizer.rasterizeImageResource(n,o,s,u);return this._addItemToMosaic(e,d,{type:"static",data:_},at(t),m,!1)}this._allowNonPowerOfTwo||(n=Ft(s.width+2*v)-2*v,o=Ft(s.height+2*v)-2*v),n===s.width&&o===s.height||(s=$i(s,n,o));const h=t.animatedSymbolProperties||{},l=t.objectId,c=new Si(s,this._requestRender,h,l);return this._addItemToMosaic(e,[c.width,c.height],{type:"animated",data:c},at(t),!1,!1)}async _handleImage(t,e,i){if(Qe(t)||Xe(t))return this._handleGIFOrPNG(t,e,i);const a=Ut(t);try{let s;const n=this.resourceManager.getResource(a);if(F(n)&&n instanceof HTMLImageElement)s=n;else{const{data:m}=await this._imageRequestQueue.push(a,{...i});s=m}if(Je(a)){if("width"in t&&"height"in t)s.width=G(t.width),s.height=G(t.height);else if("cim"in t){const m=t.cim;s.width=G(m.width??m.scaleX*m.size),s.height=G(m.size)}}if(!s.width||!s.height)return null;let o=s.width,h=s.height;t.type==="esriPMS"&&(o=Math.round(Ht(s.width,Ot(t))),h=Math.round(s.height*(o/s.width)));const l="cim"in t?t.cim.colorSubstitutions:void 0,{size:c,sdf:u,image:d}=this._rasterizer.rasterizeImageResource(o,h,s,l);return this._addItemToMosaic(e,c,{type:"static",data:d},at(t),u,!1)}catch(s){if(!Et(s))return new C("mapview-invalid-resource",`Could not fetch requested resource at ${a}. ${s.message}`)}}_addItemToMosaic(t,e,i,a,s,n,o){return this._spriteMosaic.addSpriteItem(t,e,i,a,s,n,o)}}const Fs={shaders:{vertexShader:M("stencil/stencil.vert"),fragmentShader:M("stencil/stencil.frag")},attributes:new Map([["a_pos",0]])},zi=r=>r.replace("-","_").toUpperCase(),Wt=r=>`#define ${zi(r)}
`;function Yt(r){return{attributes:new Map([["a_pos",0],["a_tex",1]]),shaders:{vertexShader:Wt(r)+M("blend/blend.vert"),fragmentShader:Wt(r)+M("blend/blend.frag")}}}const jt=te.getLogger("esri.views.2d.engine.webgl.effects.blendEffects.BlendEffect");let As=class{constructor(){this._size=[0,0]}dispose(t){this._backBufferTexture=Ct(this._backBufferTexture),this._quad=Ct(this._quad)}draw(t,e,i,a,s){const{context:n,drawPhase:o}=t;if(this._setupShader(n),a&&a!=="normal"&&o!==Ue.LABEL)return void this._drawBlended(t,e,i,a,s);const h=Yt("normal"),l=n.programCache.acquire(h.shaders.vertexShader,h.shaders.fragmentShader,h.attributes);if(!l)return void jt.error(new C("mapview-BlendEffect",'Error creating shader program for blend mode "normal"'));n.useProgram(l),e.setSamplingMode(i),n.bindTexture(e,0),l.setUniform1i("u_layerTexture",0),l.setUniform1f("u_opacity",s),n.setBlendingEnabled(!0),n.setBlendFunction(k.ONE,k.ONE_MINUS_SRC_ALPHA);const c=this._quad;c.draw(),c.unbind(),l.dispose()}_drawBlended(t,e,i,a,s){const{context:n,state:o,pixelRatio:h,inFadeTransition:l}=t,{size:c}=o,u=n.getBoundFramebufferObject();let d,m;if(F(u)){const A=u.descriptor;d=A.width,m=A.height}else d=Math.round(h*c[0]),m=Math.round(h*c[1]);this._createOrResizeTexture(t,d,m);const _=this._backBufferTexture;u.copyToTexture(0,0,d,m,0,0,_),n.setStencilTestEnabled(!1),n.setStencilWriteMask(0),n.setBlendingEnabled(!0),n.setDepthTestEnabled(!1),n.setDepthWriteEnabled(!1);const g=Yt(a),y=n.programCache.acquire(g.shaders.vertexShader,g.shaders.fragmentShader,g.attributes);if(!y)return void jt.error(new C("mapview-BlendEffect",`Error creating shader program for blend mode ${a}`));n.useProgram(y),_.setSamplingMode(i),n.bindTexture(_,0),y.setUniform1i("u_backbufferTexture",0),e.setSamplingMode(i),n.bindTexture(e,1),y.setUniform1i("u_layerTexture",1),y.setUniform1f("u_opacity",s),y.setUniform1f("u_inFadeOpacity",l?1:0),n.setBlendFunction(k.ONE,k.ZERO);const w=this._quad;w.draw(),w.unbind(),y.dispose(),n.setBlendFunction(k.ONE,k.ONE_MINUS_SRC_ALPHA)}_setupShader(t){this._quad||(this._quad=new le(t,[-1,-1,1,-1,-1,1,1,1]))}_createOrResizeTexture(t,e,i){const{context:a}=t;this._backBufferTexture!==null&&e===this._size[0]&&i===this._size[1]||(this._backBufferTexture?this._backBufferTexture.resize(e,i):this._backBufferTexture=new It(a,{target:Oe.TEXTURE_2D,pixelFormat:dt.RGBA,internalFormat:dt.RGBA,dataType:Dt.UNSIGNED_BYTE,wrapMode:Ge.CLAMP_TO_EDGE,samplingMode:St.LINEAR,flipped:!1,width:e,height:i}),this._size[0]=e,this._size[1]=i)}};const Bs={shaders:{vertexShader:M("highlight/textured.vert"),fragmentShader:M("highlight/highlight.frag")},attributes:new Map([["a_position",0],["a_texcoord",1]])},ks={shaders:{vertexShader:M("highlight/textured.vert"),fragmentShader:M("highlight/blur.frag")},attributes:new Map([["a_position",0],["a_texcoord",1]])},x=zt("esri-2d-profiler");let Ls=class{constructor(t,e){if(this._events=new ii,this._entries=new Map,this._timings=new ei(10),this._currentContainer=null,this._currentPass=null,this._currentBrush=null,this._currentSummary=null,!x)return;this._ext=si(t.gl,{}),this._debugOutput=e;const i=t.gl;if(this.enableCommandLogging){for(const a in i)if(typeof i[a]=="function"){const s=i[a],n=a.includes("draw");i[a]=(...o)=>(this._events.emit("command",{container:this._currentContainer,pass:this._currentPass,brush:this._currentBrush,method:a,args:o,isDrawCommand:n}),this._currentSummary&&(this._currentSummary.commands++,n&&this._currentSummary.drawCommands++),s.apply(i,o))}}}get enableCommandLogging(){return!(typeof x=="object"&&x.disableCommands)}recordContainerStart(t){x&&(this._currentContainer=t)}recordContainerEnd(){x&&(this._currentContainer=null)}recordPassStart(t){x&&(this._currentPass=t,this._initSummary())}recordPassEnd(){x&&(this._currentPass=null,this._emitSummary())}recordBrushStart(t){x&&(this._currentBrush=t)}recordBrushEnd(){x&&(this._currentBrush=null)}recordStart(t){if(x&&F(this._ext)){if(this._entries.has(t)){const i=this._entries.get(t),a=this._ext.resultAvailable(i.query),s=this._ext.disjoint();if(a&&!s){const n=this._ext.getResult(i.query)/1e6;let o=0;if(F(this._timings.enqueue(n))){const c=this._timings.entries,u=c.length;let d=0;for(const m of c)d+=m;o=d/u}const h=n.toFixed(2),l=o?o.toFixed(2):"--";this.enableCommandLogging?(console.groupCollapsed(`Frame report for ${t}, ${h} ms (${l} last 10 avg)
${i.commandsLen} Commands (${i.drawCommands} draw)`),console.log("RenderPass breakdown: "),console.table(i.summaries),console.log("Commands: ",i.commands),console.groupEnd()):console.log(`Frame report for ${t}, ${h} ms (${l} last 10 avg)`),this._debugOutput.innerHTML=`${h} (${l})`}for(const n of i.handles)n.remove();this._ext.deleteQuery(i.query),this._entries.delete(t)}const e={name:t,query:this._ext.createQuery(),commands:[],commandsLen:0,drawCommands:0,summaries:[],handles:[]};this.enableCommandLogging&&(e.handles.push(this._events.on("command",i=>{e.commandsLen++,e.commands.push(i),i.isDrawCommand&&e.drawCommands++})),e.handles.push(this._events.on("summary",i=>{e.summaries.push(i)}))),this._ext.beginTimeElapsed(e.query),this._entries.set(t,e)}}recordEnd(t){x&&F(this._ext)&&this._entries.has(t)&&this._ext.endTimeElapsed()}_initSummary(){this.enableCommandLogging&&(this._currentSummary={container:this._currentContainer,pass:this._currentPass,drawCommands:0,commands:0})}_emitSummary(){this.enableCommandLogging&&this._currentSummary&&this._events.emit("summary",this._currentSummary)}};const rt=2,z=1,Q=0,X=1,J=2;let Di=class{constructor(t,e,i){this._debugMap=new Map,this._width=t*i,this._height=e*i,this._pixelRatio=i;const a=Math.ceil(this._width/z),s=Math.ceil(this._height/z);this._cols=a,this._rows=s,this._cells=ai.create(a*s)}insertMetrics(t){const e=this._hasCollision(t);return e===Q&&this._markMetrics(t),e}getCellId(t,e){return t+e*this._cols}has(t){return this._cells.has(t)}hasRange(t,e){return this._cells.hasRange(t,e)}set(t){this._cells.set(t)}setRange(t,e){this._cells.setRange(t,e)}_collide(t,e,i,a){const s=t-i/2,n=e-a/2,o=s+i,h=n+a;if(o<0||h<0||s>this._width||n>this._height)return X;const l=$(Math.floor(s/z),0,this._cols),c=$(Math.floor(n/z),0,this._rows),u=$(Math.ceil(o/z),0,this._cols),d=$(Math.ceil(h/z),0,this._rows);for(let m=c;m<=d;m++)for(let _=l;_<=u;_++){const g=this.getCellId(_,m);if(this.has(g))return J}return Q}_mark(t,e,i,a,s){const n=t-i/2,o=e-a/2,h=n+i,l=o+a,c=$(Math.floor(n/z),0,this._cols),u=$(Math.floor(o/z),0,this._rows),d=$(Math.ceil(h/z),0,this._cols),m=$(Math.ceil(l/z),0,this._rows);for(let _=u;_<=m;_++)for(let g=c;g<=d;g++){const y=this.getCellId(g,_);this._debugMap.set(y,s),this.set(y)}return!1}_hasCollision(t){const e=t.id;let i=0,a=0;t.save();do{const s=t.boundsCount;i+=s;for(let n=0;n<s;n++){const o=t.boundsComputedAnchorX(n),h=t.boundsComputedAnchorY(n),l=(t.boundsWidth(n)+rt)*this._pixelRatio,c=(t.boundsHeight(n)+rt)*this._pixelRatio;switch(this._collide(o,h,l,c)){case J:return J;case X:a++}}}while(t.peekId()===e&&t.next());return t.restore(),i===a?X:Q}_markMetrics(t){const e=t.id;t.save();do{const i=t.boundsCount;for(let a=0;a<i;a++){const s=t.boundsComputedAnchorX(a),n=t.boundsComputedAnchorY(a),o=(t.boundsWidth(a)+rt)*this._pixelRatio,h=(t.boundsHeight(a)+rt)*this._pixelRatio;this._mark(s,n,o,h,t.id)}}while(t.peekId()===e&&t.next());t.restore()}};const Ii=Math.PI;function oe(r,t){switch(t.transformationType){case R.Additive:return Ri(r,t);case R.Constant:return Pi(t,r);case R.ClampedLinear:return Ci(r,t);case R.Proportional:return Ei(r,t);case R.Stops:return Fi(r,t);case R.RealWorldSize:return Ai(r,t);case R.Identity:return r;case R.Unknown:return null}}function T(r,t){return typeof r=="number"?r:oe(t,r)}function Ri(r,t){return r+(T(t.minSize,r)||t.minDataValue)}function Pi(r,t){const e=r.stops;let i=e&&e.length&&e[0].size;return i==null&&(i=r.minSize),T(i,t)}function Ci(r,t){const e=t.minDataValue,i=t.maxDataValue,a=(r-e)/(i-e),s=T(t.minSize,r),n=T(t.maxSize,r);return r<=e?s:r>=i?n:s+a*(n-s)}function Ei(r,t){const e=r/t.minDataValue,i=T(t.minSize,r),a=T(t.maxSize,r);let s=null;return s=e*i,$(s,i,a)}function Fi(r,t){const[e,i,a]=Vi(r,t.cache.ipData);if(e===i)return T(t.stops[e].size,r);{const s=T(t.stops[e].size,r);return s+(T(t.stops[i].size,r)-s)*a}}function Ai(r,t){const e=xe[t.valueUnit],i=T(t.minSize,r),a=T(t.maxSize,r),{valueRepresentation:s}=t;let n=null;return n=s==="area"?2*Math.sqrt(r/Ii)/e:s==="radius"||s==="distance"?2*r/e:r/e,$(n,i,a)}function Vi(r,t){if(!t)return;let e=0,i=t.length-1;return t.some((a,s)=>r<a?(i=s,!0):(e=s,!1)),[e,i,(r-t[e])/(t[i]-t[e])]}const yt=254,ot=255,W=0;function N(r,t){const e=[];r.forEachTile(i=>e.push(i)),e.sort((i,a)=>i.instanceId-a.instanceId),e.forEach(i=>{F(i.labelMetrics)&&i.isReady&&t(i,i.labelMetrics.getCursor())})}function Bi(r){return r.layer&&(r.layer.type==="feature"||r.layer.type==="csv"||r.layer.type==="geojson"||r.layer.type==="ogc-feature"||r.layer.type==="stream"||r.layer.type==="subtype-group"||r.layer.type==="wfs")}function ki(r){return t=>G(oe(t,r))}function Li(r){const t=r!=null&&"visualVariables"in r&&r.visualVariables;if(!t)return null;for(const e of t)if(e.type==="size")return ki(e);return null}function Ni(r){for(const t of r){const e="featureReduction"in t&&t.featureReduction&&"labelingInfo"in t.featureReduction?t.featureReduction:void 0,i=[...t.labelingInfo||[],...(e==null?void 0:e.labelingInfo)||[]];if(!(!t.labelsVisible||!i.length)&&i.some(a=>a.deconflictionStrategy==="none"))return!0}return!1}function Ui(r,t){var h;if(!Bi(t))return;const e=t.layer.type==="subtype-group"?t.layer.sublayers.items:[t.layer],i=t.layer.geometryType,a=!Ni(e),s={};if(t.layer.type!=="subtype-group"){if(((h=t.tileRenderer)==null?void 0:h.type)==="heatmap")return;const l=Li(t.layer.renderer);s[0]=l}const n=t.tileRenderer;if(K(n))return;const o=t.layer.visible&&!t.suspended;r.push({tileRenderer:n,vvEvaluators:s,deconflictionEnabled:a,geometryType:i,visible:o})}let Oi=class{run(t,e,i){const a=[];for(let s=t.length-1;s>=0;s--)Ui(a,t[s]);this._transformMetrics(a),this._runCollision(a,e,i)}_runCollision(t,e,i){const[a,s]=e.state.size,n=new Di(a,s,e.pixelRatio);for(const{tileRenderer:o,deconflictionEnabled:h,visible:l}of t){const c=o.featuresView.attributeView;h?l?(this._prepare(o),this._collideVisible(n,o,i),this._collideInvisible(n,o)):N(o,(u,d)=>{for(;d.nextId();)c.setLabelMinZoom(d.id,ot)}):N(o,(u,d)=>{for(;d.nextId();)c.setLabelMinZoom(d.id,W),l&&n.insertMetrics(d)})}}_isFiltered(t,e,i){const a=e.getFilterFlags(t),s=!i.hasFilter||!!(a&Le),n=K(i.featureEffect)||i.featureEffect.excludedLabelsVisible||!!(a&Ne);return!(s&&n)}_prepare(t){const e=t.featuresView.attributeView,i=new Set;N(t,(a,s)=>{for(;s.nextId();)if(!i.has(s.id)){if(i.add(s.id),this._isFiltered(s.id,e,t.layerView)){e.setLabelMinZoom(s.id,yt);continue}e.getLabelMinZoom(s.id)!==W?e.setLabelMinZoom(s.id,ot):e.setLabelMinZoom(s.id,W)}})}_collideVisible(t,e,i){const a=e.featuresView.attributeView,s=new Set;N(e,(n,o)=>{for(;o.nextId();)if(!s.has(o.id))if(n.key.level===i){if(a.getLabelMinZoom(o.id)===0)switch(t.insertMetrics(o)){case X:break;case J:a.setLabelMinZoom(o.id,yt),s.add(o.id);break;case Q:a.setLabelMinZoom(o.id,W),s.add(o.id)}}else a.setLabelMinZoom(o.id,yt)})}_collideInvisible(t,e){const i=e.featuresView.attributeView,a=new Set;N(e,(s,n)=>{for(;n.nextId();)if(!a.has(n.id)&&i.getLabelMinZoom(n.id)===ot)switch(t.insertMetrics(n)){case X:break;case J:i.setLabelMinZoom(n.id,ot),a.add(n.id);break;case Q:i.setLabelMinZoom(n.id,W),a.add(n.id)}})}_transformMetrics(t){for(const{tileRenderer:e,geometryType:i,vvEvaluators:a}of t)N(e,(s,n)=>{const o=e.featuresView.attributeView,h=s.transforms.labelMat2d;h[4]=Math.round(h[4]),h[5]=Math.round(h[5]);const l=i==="polyline";for(;n.next();){const c=n.boundsCount,u=n.anchorX,d=n.anchorY;let m=n.size;const _=a[0];if(F(_)){const w=_(o.getVVSize(n.id));m=isNaN(w)||w==null||w===1/0?m:w}const g=n.directionX*(m/2),y=n.directionY*(m/2);for(let w=0;w<c;w++){let A=u,gt=n.anchorY;if(l){let V=A+n.boundsX(w)+g,B=gt+n.boundsY(w)+y;V=h[0]*V+h[2]*B+h[4],B=h[1]*V+h[3]*B+h[5],n.setBoundsComputedAnchorX(w,Math.floor(V)),n.setBoundsComputedAnchorY(w,Math.floor(B))}else{A=h[0]*u+h[2]*d+h[4],gt=h[1]*u+h[3]*d+h[5];const V=A+n.boundsX(w)+g,B=gt+n.boundsY(w)+y;n.setBoundsComputedAnchorX(w,V),n.setBoundsComputedAnchorY(w,B)}}}})}};const Gi=32;let E=class extends $e(Z){constructor(t){super(t),this._applyVisibilityPassThrottled=Se(this._applyVisibilityPass,Gi,this),this.lastUpdateId=-1,this.updateRequested=!1,this.view=null}initialize(){this.collisionEngine=new Oi}destroy(){this.collisionEngine=null,this._applyVisibilityPassThrottled=ue(this._applyVisibilityPassThrottled)}get updating(){return zt("esri-2d-log-updating")&&console.log(`Updating LabelManager ${this.updateRequested}:
-> updateRequested: ${this.updateRequested}`),this.updateRequested}update(t){this._applyVisibilityPassThrottled(t)}viewChange(){this.requestUpdate()}requestUpdate(){var t;this.updateRequested||(this.updateRequested=!0,(t=this.view)==null||t.requestUpdate())}processUpdate(t){this._set("updateParameters",t),this.updateRequested&&(this.updateRequested=!1,this.update(t))}_applyVisibilityPass(t){const e=this.view;if(e)try{const i=e.featuresTilingScheme.getClosestInfoForScale(t.state.scale).level;this.collisionEngine.run(e.allLayerViews.items,t,i)}catch{}}};p([f()],E.prototype,"updateRequested",void 0),p([f({readOnly:!0})],E.prototype,"updateParameters",void 0),p([f()],E.prototype,"updating",null),p([f()],E.prototype,"view",void 0),E=p([q("esri.views.2d.layers.labels.LabelManager")],E);const qs=E,ht="esri-zoom-box",lt={container:`${ht}__container`,overlay:`${ht}__overlay`,background:`${ht}__overlay-background`,box:`${ht}__outline`},vt={zoom:"Shift",counter:"Ctrl"};let Y=class extends Z{constructor(t){super(t),this._container=null,this._overlay=null,this._backgroundShape=null,this._boxShape=null,this._box={x:0,y:0,width:0,height:0},this._rafId=null,this._handles=null,this._redraw=this._redraw.bind(this)}destroy(){this.view=null}set view(t){this._handles&&this._handles.forEach(e=>{e.remove()}),this._handles=null,this._destroyOverlay(),this._set("view",t),t&&(t.on("drag",[vt.zoom],e=>this._handleDrag(e,1),At.INTERNAL),t.on("drag",[vt.zoom,vt.counter],e=>this._handleDrag(e,-1),At.INTERNAL))}_start(){this._createContainer(),this._createOverlay(),this.navigation.begin()}_update(t,e,i,a){this._box.x=t,this._box.y=e,this._box.width=i,this._box.height=a,this._rafId||(this._rafId=requestAnimationFrame(this._redraw))}_end(t,e,i,a,s){const n=this.view,o=n.toMap(Te(t+.5*i,e+.5*a));let h=Math.max(i/n.width,a/n.height);s===-1&&(h=1/h),this._destroyOverlay(),this.navigation.end(),n.goTo({center:o,scale:n.scale*h})}_updateBox(t,e,i,a){const s=this._boxShape;s.setAttributeNS(null,"x",""+t),s.setAttributeNS(null,"y",""+e),s.setAttributeNS(null,"width",""+i),s.setAttributeNS(null,"height",""+a),s.setAttributeNS(null,"class",lt.box)}_updateBackground(t,e,i,a){this._backgroundShape.setAttributeNS(null,"d",this._toSVGPath(t,e,i,a,this.view.width,this.view.height))}_createContainer(){const t=document.createElement("div");t.className=lt.container,this.view.root.appendChild(t),this._container=t}_createOverlay(){const t=this.view.width,e=this.view.height,i=document.createElementNS("http://www.w3.org/2000/svg","path");i.setAttributeNS(null,"d","M 0 0 L "+t+" 0 L "+t+" "+e+" L 0 "+e+" Z"),i.setAttributeNS(null,"class",lt.background);const a=document.createElementNS("http://www.w3.org/2000/svg","rect"),s=document.createElementNS("http://www.w3.org/2000/svg","svg");s.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink","http://www.w3.org/1999/xlink"),s.setAttributeNS(null,"class",lt.overlay),s.appendChild(i),s.appendChild(a),this._container.appendChild(s),this._backgroundShape=i,this._boxShape=a,this._overlay=s}_destroyOverlay(){this._container&&this._container.parentNode&&this._container.parentNode.removeChild(this._container),this._container=this._backgroundShape=this._boxShape=this._overlay=null}_toSVGPath(t,e,i,a,s,n){const o=t+i,h=e+a;return"M 0 0 L "+s+" 0 L "+s+" "+n+" L 0 "+n+" ZM "+t+" "+e+" L "+t+" "+h+" L "+o+" "+h+" L "+o+" "+e+" Z"}_handleDrag(t,e){const i=t.x,a=t.y,s=t.origin.x,n=t.origin.y;let o,h,l,c;switch(i>s?(o=s,l=i-s):(o=i,l=s-i),a>n?(h=n,c=a-n):(h=a,c=n-a),t.action){case"start":this._start();break;case"update":this._update(o,h,l,c);break;case"end":this._end(o,h,l,c,e)}t.stopPropagation()}_redraw(){if(!this._rafId||(this._rafId=null,!this._overlay))return;const{x:t,y:e,width:i,height:a}=this._box;this._updateBox(t,e,i,a),this._updateBackground(t,e,i,a),this._rafId=requestAnimationFrame(this._redraw)}};p([f()],Y.prototype,"navigation",void 0),p([f()],Y.prototype,"view",null),Y=p([q("esri.views.2d.navigation.ZoomBox")],Y);const qi=Y;let P=class{constructor(t){this._gain=t,this.lastValue=void 0,this.filteredDelta=void 0}update(t){if(this.hasLastValue()){const e=this.computeDelta(t);this._updateDelta(e)}this.lastValue=t}reset(){this.lastValue=void 0,this.filteredDelta=void 0}hasLastValue(){return this.lastValue!==void 0}hasFilteredDelta(){return this.filteredDelta!==void 0}computeDelta(t){return this.lastValue===void 0?NaN:t-this.lastValue}_updateDelta(t){this.filteredDelta!==void 0?this.filteredDelta=(1-this._gain)*this.filteredDelta+this._gain*t:this.filteredDelta=t}},Pt=class{constructor(t,e,i){this._initialVelocity=t,this._stopVelocity=e,this._friction=i,this._duration=Math.abs(Math.log(Math.abs(this._initialVelocity)/this._stopVelocity)/Math.log(1-this._friction))}get duration(){return this._duration}isFinished(t){return t>this.duration}get friction(){return this._friction}value(t){return this.valueFromInitialVelocity(this._initialVelocity,t)}valueDelta(t,e){const i=this.value(t);return this.value(t+e)-i}valueFromInitialVelocity(t,e){e=Math.min(e,this.duration);const i=1-this.friction;return t*(i**e-1)/Math.log(i)}};class Zi extends Pt{constructor(t,e,i,a,s){super(t,e,i),this._sceneVelocity=a,this.direction=s}value(t){return super.valueFromInitialVelocity(this._sceneVelocity,t)}}class Hi{constructor(t=300,e=12,i=.84){this._minimumInitialVelocity=t,this._stopVelocity=e,this._friction=i,this.enabled=!0,this._time=new P(.6),this._screen=[new P(.4),new P(.4)],this._scene=[new P(.6),new P(.6),new P(.6)],this._tmpDirection=ee()}add(t,e,i){if(this.enabled){if(this._time.hasLastValue()&&this._time.computeDelta(i)<.015)return;this._screen[0].update(t[0]),this._screen[1].update(t[1]),this._scene[0].update(e[0]),this._scene[1].update(e[1]),this._scene[2].update(e[2]),this._time.update(i)}}reset(){this._screen[0].reset(),this._screen[1].reset(),this._scene[0].reset(),this._scene[1].reset(),this._scene[2].reset(),this._time.reset()}evaluateMomentum(){if(!this.enabled||!this._screen[0].hasFilteredDelta()||!this._time.hasFilteredDelta())return null;const t=this._screen[0].filteredDelta,e=this._screen[1].filteredDelta,i=t==null||e==null?0:Math.sqrt(t*t+e*e),a=this._time.filteredDelta,s=a==null||i==null?0:i/a;return Math.abs(s)<this._minimumInitialVelocity?null:this.createMomentum(s,this._stopVelocity,this._friction)}createMomentum(t,e,i){ze(this._tmpDirection,this._scene[0].filteredDelta??0,this._scene[1].filteredDelta??0,this._scene[2].filteredDelta??0);const a=De(this._tmpDirection);a>0&&ie(this._tmpDirection,this._tmpDirection,1/a);const s=this._time.filteredDelta;return new Zi(t,e,i,s==null?0:a/s,this._tmpDirection)}}let U=class extends Z{constructor(t){super(t),this.animationTime=0,this.momentumEstimator=new Hi(500,6,.92),this.momentum=null,this.tmpMomentum=ee(),this.momentumFinished=!1,this.viewpoint=new pt({targetGeometry:new _t,scale:0,rotation:0}),this._previousDrag=null,se(()=>this.momentumFinished,()=>this.navigation.stop())}begin(t,e){this.navigation.begin(),this.momentumEstimator.reset(),this.addToEstimator(e),this._previousDrag=e}update(t,e){this.addToEstimator(e);let i=e.center.x,a=e.center.y;const s=this._previousDrag;i=s?s.center.x-i:-i,a=s?a-s.center.y:a,t.viewpoint=ut(this.viewpoint,t.viewpoint,[i||0,a||0]),this._previousDrag=e}end(t,e){this.addToEstimator(e);const i=t.navigation.momentumEnabled;this.momentum=i?this.momentumEstimator.evaluateMomentum():null,this.animationTime=0,this.momentum&&this.onAnimationUpdate(t),this._previousDrag=null,this.navigation.end()}addToEstimator(t){const e=t.center.x,i=t.center.y,a=Ie(-e,i),s=Re(-e,i,0);this.momentumEstimator.add(a,s,.001*t.timestamp)}onAnimationUpdate(t){var e;(e=this.navigation.animationManager)==null||e.animateContinous(t.viewpoint,(i,a)=>{const{momentum:s,animationTime:n,tmpMomentum:o}=this,h=.001*a;if(!(this.momentumFinished=!s||s.isFinished(n))){const l=s.valueDelta(n,h);ie(o,s.direction,l),ut(i,i,o),t.constraints.constrainByGeometry(i)}this.animationTime+=h})}stopMomentumNavigation(){this.momentum&&(this.momentumEstimator.reset(),this.momentum=null,this.navigation.stop())}};p([f()],U.prototype,"momentumFinished",void 0),p([f()],U.prototype,"viewpoint",void 0),p([f()],U.prototype,"navigation",void 0),U=p([q("esri.views.2d.navigation.actions.Pan")],U);const Wi=U;let he=class{constructor(t=2.5,e=.01,i=.95,a=12){this._minimumInitialVelocity=t,this._stopVelocity=e,this._friction=i,this._maxVelocity=a,this.enabled=!0,this.value=new P(.8),this.time=new P(.3)}add(t,e){if(this.enabled&&e!=null){if(this.time.hasLastValue()){if(this.time.computeDelta(e)<.01)return;if(this.value.hasFilteredDelta()){const i=this.value.computeDelta(t);this.value.filteredDelta*i<0&&this.value.reset()}}this.time.update(e),this.value.update(t)}}reset(){this.value.reset(),this.time.reset()}evaluateMomentum(){if(!this.enabled||!this.value.hasFilteredDelta()||!this.time.hasFilteredDelta())return null;let t=this.value.filteredDelta/this.time.filteredDelta;return t=$(t,-this._maxVelocity,this._maxVelocity),Math.abs(t)<this._minimumInitialVelocity?null:this.createMomentum(t,this._stopVelocity,this._friction)}createMomentum(t,e,i){return new Pt(t,e,i)}},Yi=class extends he{constructor(t=3,e=.01,i=.95,a=12){super(t,e,i,a)}add(t,e){const i=this.value.lastValue;if(i!=null){let a=t-i;for(;a>Math.PI;)a-=2*Math.PI;for(;a<-Math.PI;)a+=2*Math.PI;t=i+a}super.add(t,e)}};class ji extends Pt{constructor(t,e,i){super(t,e,i)}value(t){const e=super.value(t);return Math.exp(e)}valueDelta(t,e){const i=super.value(t),a=super.value(t+e)-i;return Math.exp(a)}}class Qi extends he{constructor(t=2.5,e=.01,i=.95,a=12){super(t,e,i,a)}add(t,e){super.add(Math.log(t),e)}createMomentum(t,e,i){return new ji(t,e,i)}}let O=class extends Z{constructor(t){super(t),this._animationTime=0,this._momentumFinished=!1,this._previousAngle=0,this._previousRadius=0,this._previousCenter=null,this._rotationMomentumEstimator=new Yi(.6,.15,.95),this._rotationDirection=1,this._startAngle=0,this._startRadius=0,this._updateTimestamp=null,this._zoomDirection=1,this._zoomMomentumEstimator=new Qi,this._zoomOnly=null,this.zoomMomentum=null,this.rotateMomentum=null,this.viewpoint=new pt({targetGeometry:new _t,scale:0,rotation:0}),this.addHandles(se(()=>this._momentumFinished,()=>this.navigation.stop()))}begin(t,e){this.navigation.begin(),this._rotationMomentumEstimator.reset(),this._zoomMomentumEstimator.reset(),this._zoomOnly=null,this._previousAngle=this._startAngle=e.angle,this._previousRadius=this._startRadius=e.radius,this._previousCenter=e.center,this._updateTimestamp=null,t.constraints.rotationEnabled&&this.addToRotateEstimator(0,e.timestamp),this.addToZoomEstimator(e,1)}update(t,e){this._updateTimestamp===null&&(this._updateTimestamp=e.timestamp);const i=e.angle,a=e.radius,s=e.center,n=Math.abs(180*(i-this._startAngle)/Math.PI),o=Math.abs(a-this._startRadius),h=this._startRadius/a;if(this._previousRadius&&this._previousCenter){const l=a/this._previousRadius;let c=180*(i-this._previousAngle)/Math.PI;this._rotationDirection=c>=0?1:-1,this._zoomDirection=l>=1?1:-1,t.constraints.rotationEnabled?(this._zoomOnly===null&&e.timestamp-this._updateTimestamp>200&&(this._zoomOnly=o-n>0),this._zoomOnly===null||this._zoomOnly?c=0:this.addToRotateEstimator(i-this._startAngle,e.timestamp)):c=0,this.addToZoomEstimator(e,h),this.navigation.setViewpoint([s.x,s.y],1/l,c,[this._previousCenter.x-s.x,s.y-this._previousCenter.y])}this._previousAngle=i,this._previousRadius=a,this._previousCenter=s}end(t){this.rotateMomentum=this._rotationMomentumEstimator.evaluateMomentum(),this.zoomMomentum=this._zoomMomentumEstimator.evaluateMomentum(),this._animationTime=0,(this.rotateMomentum||this.zoomMomentum)&&this.onAnimationUpdate(t),this.navigation.end()}addToRotateEstimator(t,e){this._rotationMomentumEstimator.add(t,.001*e)}addToZoomEstimator(t,e){this._zoomMomentumEstimator.add(e,.001*t.timestamp)}canZoomIn(t){const e=t.scale,i=t.constraints.effectiveMaxScale;return i===0||e>i}canZoomOut(t){const e=t.scale,i=t.constraints.effectiveMinScale;return i===0||e<i}onAnimationUpdate(t){var e;(e=this.navigation.animationManager)==null||e.animateContinous(t.viewpoint,(i,a)=>{const s=!this.canZoomIn(t)&&this._zoomDirection>1||!this.canZoomOut(t)&&this._zoomDirection<1,n=!this.rotateMomentum||this.rotateMomentum.isFinished(this._animationTime),o=s||!this.zoomMomentum||this.zoomMomentum.isFinished(this._animationTime),h=.001*a;if(this._momentumFinished=n&&o,!this._momentumFinished){const l=this.rotateMomentum?Math.abs(this.rotateMomentum.valueDelta(this._animationTime,h))*this._rotationDirection*180/Math.PI:0;let c=this.zoomMomentum?Math.abs(this.zoomMomentum.valueDelta(this._animationTime,h)):1;const u=et(),d=et();if(this._previousCenter){tt(u,this._previousCenter.x,this._previousCenter.y),Pe(d,t.size,t.padding),Ce(u,u,d);const{constraints:m,scale:_}=t,g=_*c;c<1&&!m.canZoomInTo(g)?(c=_/m.effectiveMaxScale,this.zoomMomentum=null,this.rotateMomentum=null):c>1&&!m.canZoomOutTo(g)&&(c=_/m.effectiveMinScale,this.zoomMomentum=null,this.rotateMomentum=null),Ee(i,t.viewpoint,c,l,u,t.size),t.constraints.constrainByGeometry(i)}}this._animationTime+=h})}stopMomentumNavigation(){(this.rotateMomentum||this.zoomMomentum)&&(this.rotateMomentum&&(this._rotationMomentumEstimator.reset(),this.rotateMomentum=null),this.zoomMomentum&&(this._zoomMomentumEstimator.reset(),this.zoomMomentum=null),this.navigation.stop())}};p([f()],O.prototype,"_momentumFinished",void 0),p([f()],O.prototype,"viewpoint",void 0),p([f()],O.prototype,"navigation",void 0),O=p([q("esri.views.2d.navigation.actions.Pinch")],O);const Xi=O,bt=et(),Qt=et();let j=class extends Z{constructor(r){super(r),this._previousCenter=et(),this.viewpoint=new pt({targetGeometry:new _t,scale:0,rotation:0})}begin(r,t){this.navigation.begin(),tt(this._previousCenter,t.center.x,t.center.y)}update(r,t){const{state:{size:e,padding:i}}=r;tt(bt,t.center.x,t.center.y),Fe(Qt,e,i),r.viewpoint=$t(this.viewpoint,r.state.paddedViewState.viewpoint,Ae(Qt,this._previousCenter,bt)),Ve(this._previousCenter,bt)}end(){this.navigation.end()}};p([f()],j.prototype,"viewpoint",void 0),p([f()],j.prototype,"navigation",void 0),j=p([q("esri.views.2d.actions.Rotate")],j);const Ji=j,ct=10,Xt=1,Mt=new pt({targetGeometry:new _t}),xt=[0,0],Jt=250;let D=class extends Z{constructor(r){super(r),this._endTimer=null,this._lastEventTimestamp=null,this.animationManager=null,this.interacting=!1}initialize(){this.pan=new Wi({navigation:this}),this.rotate=new Ji({navigation:this}),this.pinch=new Xi({navigation:this}),this.zoomBox=new qi({view:this.view,navigation:this})}destroy(){this.pan=it(this.pan),this.rotate=it(this.rotate),this.pinch=it(this.pinch),this.zoomBox=it(this.zoomBox),this.animationManager=null}begin(){this._set("interacting",!0)}end(){this._lastEventTimestamp=performance.now(),this._startTimer(Jt)}async zoom(r,t=this._getDefaultAnchor()){if(this.stop(),this.begin(),this.view.constraints.snapToZoom&&this.view.constraints.effectiveLODs)return r<1?this.zoomIn(t):this.zoomOut(t);this.setViewpoint(t,r,0,[0,0])}async zoomIn(r){const t=this.view,e=t.constraints.snapToNextScale(t.scale);return this._zoomToScale(e,r)}async zoomOut(r){const t=this.view,e=t.constraints.snapToPreviousScale(t.scale);return this._zoomToScale(e,r)}setViewpoint(r,t,e,i){this.begin(),this.view.state.viewpoint=this._scaleRotateTranslateViewpoint(this.view.viewpoint,r,t,e,i),this.end()}setViewpointImmediate(r,t=0,e=[0,0],i=this._getDefaultAnchor()){this.view.state.viewpoint=this._scaleRotateTranslateViewpoint(this.view.viewpoint,i,r,t,e)}continousRotateClockwise(){var t;const r=this.get("view.viewpoint");(t=this.animationManager)==null||t.animateContinous(r,e=>{$t(e,e,-Xt)})}continousRotateCounterclockwise(){var t;const r=this.get("view.viewpoint");(t=this.animationManager)==null||t.animateContinous(r,e=>{$t(e,e,Xt)})}resetRotation(){this.view.rotation=0}continousPanLeft(){this._continuousPan([-ct,0])}continousPanRight(){this._continuousPan([ct,0])}continousPanUp(){this._continuousPan([0,ct])}continousPanDown(){this._continuousPan([0,-ct])}stop(){var r;this.pan.stopMomentumNavigation(),(r=this.animationManager)==null||r.stop(),this.end(),this._endTimer!==null&&(clearTimeout(this._endTimer),this._endTimer=null,this._set("interacting",!1))}_continuousPan(r){var e;const t=this.view.viewpoint;(e=this.animationManager)==null||e.animateContinous(t,i=>{ut(i,i,r),this.view.constraints.constrainByGeometry(i)})}_startTimer(r){return this._endTimer!==null||(this._endTimer=setTimeout(()=>{this._endTimer=null;const t=performance.now()-(this._lastEventTimestamp??0);t<Jt?this._endTimer=this._startTimer(t):this._set("interacting",!1)},r)),this._endTimer}_getDefaultAnchor(){const{size:r,padding:{left:t,right:e,top:i,bottom:a}}=this.view;return xt[0]=.5*(r[0]-e+t),xt[1]=.5*(r[1]-a+i),xt}async _zoomToScale(r,t=this._getDefaultAnchor()){const{view:e}=this,{constraints:i,scale:a,viewpoint:s,size:n,padding:o}=e,h=i.canZoomInTo(r),l=i.canZoomOutTo(r);if(!(r<a&&!h||r>a&&!l))return Vt(Mt,s,r/a,0,t,n,o),i.constrainByGeometry(Mt),e.goTo(Mt,{animate:!0})}_scaleRotateTranslateViewpoint(r,t,e,i,a){const{view:s}=this,{size:n,padding:o,constraints:h,scale:l,viewpoint:c}=s,u=l*e,d=h.canZoomInTo(u),m=h.canZoomOutTo(u);return(e<1&&!d||e>1&&!m)&&(e=1),ut(c,c,a),Vt(r,c,e,i,t,n,o),h.constrainByGeometry(r)}};p([f()],D.prototype,"animationManager",void 0),p([f({type:Boolean,readOnly:!0})],D.prototype,"interacting",void 0),p([f()],D.prototype,"pan",void 0),p([f()],D.prototype,"pinch",void 0),p([f()],D.prototype,"rotate",void 0),p([f()],D.prototype,"view",void 0),p([f()],D.prototype,"zoomBox",void 0),D=p([q("esri.views.2d.navigation.MapViewNavigation")],D);const Js=D,Ki={shaders:{vertexShader:M("magnifier/magnifier.vert"),fragmentShader:M("magnifier/magnifier.frag")},attributes:new Map([["a_pos",0]])};function Ks(r){return ni(r,Ki)}export{Es as J,As as _,ks as a,Ks as b,Ki as c,$s as e,Ls as i,Fs as r,Bs as t,qs as u,Js as y};
