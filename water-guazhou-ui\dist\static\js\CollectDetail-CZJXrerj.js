import{c as J,a8 as H,d as Q,ad as K,r as T,fn as X,S as N,b as g,l as Z,W as O,o as ee,Q as te,g as x,n as ae,q as _,F as D,i as d,h as V,an as $,aw as oe,bl as re,c5 as ne,aq as le,bm as se,C as ie}from"./index-r0dFAfgr.js";import{_ as de}from"./Search-NSrhrIa_.js";import{w as I}from"./Point-WxyopZva.js";import{g as k,b as ce,a as pe,n as me}from"./MapView-DaoQedLH.js";import ue from"./detail-CU6-qhMl.js";import{f as z}from"./DateFormatter-Bm9a68Ax.js";import{A as be,b as fe,c as ge,d as he}from"./usePipeCollect-DNAtT5mx.js";import{d as _e}from"./pipe-nogVzCHG.js";import{g as ye}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{f as we,g as We}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";var u=(o=>(o.未派发="0",o.待接收="1",o.处理中="2",o.待审核="3",o.完成="4",o.审核退回="5",o))(u||{}),y=(o=>(o.PASS="4",o.REJECT="5",o))(y||{}),R=(o=>(o.线="0",o.点="1",o))(R||{});const Ue=Object.entries(u).map(o=>({label:o[0],value:o[1]})),qe=o=>{var b;return(b=Object.entries(u).find(h=>h[1]===o))==null?void 0:b[0]},Te=()=>{const o=J([]),b=H(()=>{const r={};return o.value.map(s=>{r[s.layerId]=s}),r});return{getLegends:async()=>{try{const r=await _e();return console.log(r),o.value=r.data.layers||[],r}catch(r){console.log(r),o.value=[]}},legends:o,legendRecords:b}},Ce={class:"tab-box"},Pe=Q({__name:"CollectDetail",props:{row:{}},emits:["rowClick","audit"],setup(o,{emit:b}){const h=b,r=K("view"),s=o,p=T({layerInfoes:[],curTab:void 0,curPage:void 0}),M=T({filters:[{type:"btn-group",label:"审核：",field:"layerid",btns:[{perm:!0,text:"同意",disabled(){return i.loading},click:()=>F(y.PASS)},{perm:!0,text:"拒绝",type:"danger",disabled(){return i.loading},click:()=>F(y.REJECT)}]}],defaultParams:{}}),F=async e=>{var t;try{const a=await X("审核意见","审核");if(a.action!=="confirm"||e===y.PASS&&await N("确定上传到空间数据库？","提示信息")!=="confirm")return;i.loading=!0;const l=await be({id:(t=s.row)==null?void 0:t.id,remark:a.value,status:e});if(l.data.code!==200){g.error(l.data.message||"操作失败"),i.loading=!1;return}e===y.PASS?(g.success("正在上传..."),await j(),n!=null&&n.graphics&&(r==null||r.goTo(n==null?void 0:n.graphics))):g.success("已拒绝"),h("audit",e)}catch(a){console.log(a)}i.loading=!1},j=async()=>{var e;if(((e=s.row)==null?void 0:e.status)===u.待审核)try{(await Promise.allSettled(S.legends.value.map(a=>we(a.layerId,{addFeatures:n==null?void 0:n.graphics.filter(l=>Number(l.attributes.row.layerId)===a.layerId).map(l=>new k({geometry:l.geometry,attributes:{...l.attributes.row||{},createdate:Z(l.attributes.row.createTime).format("YYYYMMDD")}}))})))).map(a=>{a.status==="fulfilled"&&(a.value.addFeatureResults.some(l=>l.error)?console.log(a.value.addFeatureResults):console.log("添加成功"))})}catch(t){console.log(t)}},f=T({tabs:[],type:"tabs"}),L=[{minWidth:220,label:"编号",prop:"sid"},{minWidth:100,label:"名称",prop:"name"},{minWidth:100,label:"材质",prop:"material"},{minWidth:100,label:"所在道路",prop:"laneway"},{minWidth:100,label:"地址",prop:"address"},{minWidth:100,label:"埋设类型",prop:"burytype"},{minWidth:100,label:"口径",prop:"diameter"},{minWidth:100,label:"埋深",prop:"depth"},{minWidth:100,label:"x",prop:"x"},{minWidth:100,label:"y",prop:"y"},{minWidth:100,label:"z",prop:"z"},{minWidth:160,label:"创建时间",prop:"createTime",formatter:(e,t)=>z(t)},{minWidth:100,label:"创建人",prop:"creatorName"}],A=[{minWidth:220,label:"编号",prop:"sid"},{minWidth:100,label:"名称",prop:"name"},{minWidth:220,label:"起点编号",prop:"start_sid"},{minWidth:220,label:"终点编号",prop:"end_sid"},{minWidth:100,label:"起点埋深",prop:"start_depth"},{minWidth:100,label:"终点埋深",prop:"end_depth"},{minWidth:100,label:"起点高程",prop:"start_z"},{minWidth:100,label:"终点高程",prop:"end_z"},{minWidth:100,label:"材质",prop:"material"},{minWidth:100,label:"所在道路",prop:"laneway"},{minWidth:100,label:"地址",prop:"address"},{minWidth:100,label:"埋设类型",prop:"burytype"},{minWidth:100,label:"口径",prop:"diameter"},{minWidth:160,label:"创建时间",prop:"createTime",formatter:(e,t)=>z(t)},{minWidth:100,label:"创建人",prop:"creatorName"}],i=T({columns:A,dataList:[],operations:[{perm:!0,type:"success",text:"定位",click:e=>E(e)},{perm:()=>{var e;return((e=s.row)==null?void 0:e.status)===u.处理中},type:"danger",text:"删除",click:e=>B(e)}],pagination:{hide:!0,refreshData:({page:e,size:t})=>{i.pagination.page=e||1,i.pagination.limit=t||20,P()}}}),B=async e=>{N(e.type===R.点?"确定删除点及相关联管线吗？":"确定删除？","提示信息").then(async()=>{try{(await fe(w.list.value.filter(a=>a.id===e.id||a.sid===e.sid||a.start_sid===e.sid||a.end_sid===e.sid).map(a=>a.id))).data.code===200?(g.success("删除成功"),P()):g.error("删除失败")}catch(t){g.error("删除失败"),console.log(t)}})},E=e=>{h("rowClick",e);const t=n==null?void 0:n.graphics.find(a=>a.attributes.row.id===e.id);t&&ye(r,t,{duration:500,zoom:13})},w=ge(),Y=()=>{const e=[];w.list.value.map(t=>{if(t.type===R.线){const a=new I({longitude:t.x,latitude:t.y,spatialReference:r==null?void 0:r.spatialReference}),l=new I({longitude:t.end_x,latitude:t.end_y,spatialReference:r==null?void 0:r.spatialReference});e.push(new k({geometry:new ce({paths:[[[a.x,a.y],[l.x,l.y]]],spatialReference:r==null?void 0:r.spatialReference}),symbol:new pe({color:"#318DFF",style:"solid",width:2}),attributes:{row:t}}))}else{const a=S.legendRecords.value[t.layerId],l=a==null?void 0:a.legend[0];l&&e.push(new k({geometry:new I({longitude:t.x,latitude:t.y,spatialReference:r==null?void 0:r.spatialReference}),symbol:new me({width:l.width||20,height:l.height||20,url:`${window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService}/${a.layerId}/images/${l.url}`}),attributes:{row:t}}))}}),n==null||n.removeAll(),n==null||n.addMany(e)},C=()=>{var t;const e=(t=O().gLayerInfos)==null?void 0:t.find(a=>a.layerid===p.curTab);i.columns=(e==null?void 0:e.geometrytype)==="esriGeometryPolyline"?A:L,i.dataList=w.list.value.filter(a=>Number(a.layerId)===p.curTab)},P=async()=>{var e;try{if(!((e=s.row)!=null&&e.id))return;i.loading=!0,await w.getList(s.row.id),C(),Y(),U()}catch(t){console.log(t)}i.loading=!1},U=()=>{var e;p.curPage!==0&&he((e=s.row)==null?void 0:e.id).then(t=>{var a;f.tabs=(a=t.data.data)==null?void 0:a.map(l=>{var W;const c=(W=O().gLayerInfos)==null?void 0:W.find(v=>v.layerid===Number(l));return console.log(c),{label:c==null?void 0:c.layername,value:c==null?void 0:c.layerid}}),f.tabs.length&&(p.curTab=f.tabs[0].value,C())})},S=Te();let n;return ee(async()=>{await S.getLegends(),P(),n=We(r,{id:"pipe-collect",title:"采集数据"})}),te(()=>{n==null||n.removeAll()}),(e,t)=>{const a=re,l=de,c=ne,W=le,v=se;return x(),ae("div",Ce,[_(v,{modelValue:d(p).curPage,"onUpdate:modelValue":t[1]||(t[1]=m=>d(p).curPage=m),type:"border-card",style:{height:"100%"}},{default:D(()=>[_(a,{label:"工单信息"},{default:D(()=>{var m;return[_(ue,{id:(m=s.row)==null?void 0:m.workOrderId},null,8,["id"])]}),_:1}),_(a,{label:"采集信息"},{default:D(()=>{var m,G;return[((m=s.row)==null?void 0:m.status)===d(u).待审核?(x(),V(l,{key:0,config:d(M)},null,8,["config"])):$("",!0),d(f).tabs.length?(x(),V(c,{key:1,modelValue:d(p).curTab,"onUpdate:modelValue":t[0]||(t[0]=q=>d(p).curTab=q),config:d(f),style:{"margin-bottom":"12px"},onChange:C},null,8,["modelValue","config"])):$("",!0),_(W,{class:oe(["table-box",[d(f).tabs.length?"hasTab":"noTab",((G=s.row)==null?void 0:G.status)===d(u).待审核?"hasAudit":"noAudit"]]),config:d(i)},null,8,["class","config"])]}),_:1})]),_:1},8,["modelValue"])])}}}),Se=ie(Pe,[["__scopeId","data-v-65d3494e"]]),Je=Object.freeze(Object.defineProperty({__proto__:null,default:Se},Symbol.toStringTag,{value:"Module"}));export{Se as C,u as E,qe as F,Ue as a,Je as b};
