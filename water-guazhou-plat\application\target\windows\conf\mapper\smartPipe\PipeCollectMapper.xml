<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipeCollectMapper">
    <update id="storage">
        update tb_pipe_collect_data set is_storage = '1' where main_id = #{mainId}
    </update>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollect">
        select a.*, creator.first_name as creatorName, process.first_name as processUserName, review.first_name as reviewUserName
        from tb_pipe_collect a
        left join tb_user creator on a.creator = creator.id
        left join tb_user process on a.process_user = process.id
        left join tb_user review on a.review_user = review.id
        <where>
            a.tenant_id = #{param.tenantId}
            <if test="param.status != null and param.status != ''">
                <choose>
                    <when test="param.status == '11'.toString()">
                        and a.status != '6'
                    </when>
                    <otherwise>
                        and a.status = #{param.status}
                    </otherwise>
                </choose>
            </if>
            <if test="param.name != null and param.name != ''">
                and a.name like '%' || #{param.name} || '%'
            </if>
            <if test="param.code != null and param.code != ''">
                and a.code like '%' || #{param.code} || '%'
            </if>
            <if test="param.start != null">
                and a.create_time &gt;= to_timestamp(#{param.start} / 1000)
            </if>
            <if test="param.end != null">
                and a.create_time &lt;= to_timestamp(#{param.end} / 1000)
            </if>
            <if test="param.creator != null and param.creator != ''">
                and a.creator like '%' || #{param.creator} || '%'
            </if>
            <if test="param.processUser != null and param.processUser != ''">
                and a.process_user like '%' || #{param.processUser} || '%'
            </if>
            <if test="param.reviewUser != null and param.reviewUser != ''">
                and a.review_user like '%' || #{param.reviewUser} || '%'
            </if>
        </where>
        order by a.create_time desc
    </select>

</mapper>