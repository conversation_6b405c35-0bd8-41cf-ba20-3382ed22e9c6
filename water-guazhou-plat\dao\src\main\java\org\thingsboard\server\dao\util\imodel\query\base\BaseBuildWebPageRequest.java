package org.thingsboard.server.dao.util.imodel.query.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseBuildWeb;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 平台管理-Web搭建对象 base_build_web
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@ApiModel(value = "Web搭建", description = "平台管理-Web搭建实体类")
@Data
public class BaseBuildWebPageRequest extends PageableQueryEntity<BaseBuildWeb> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 系统图标地址
     */
    @ApiModelProperty(value = "系统图标地址")
    private String iconUrl;

    /**
     * 系统登录页面地址
     */
    @ApiModelProperty(value = "系统登录页面地址")
    private String loginUrl;

    /**
     * 登录模板
     */
    @ApiModelProperty(value = "登录模板")
    private String loginTemplate;

    /**
     * 系统风格
     */
    @ApiModelProperty(value = "系统风格")
    private String style;

    /**
     * 消息通知方式
     */
    @ApiModelProperty(value = "消息通知方式")
    private String messageType;
}
