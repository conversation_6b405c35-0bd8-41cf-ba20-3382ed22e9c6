import{_}from"./CardTable-rdWOL4_6.js";import{m as y,M as m,dJ as F,r as T,c as s,o as x,bv as I,bO as u,C as w,g as B,n as S,p as $,q as C}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";const N=e=>y({url:"/api/assets/transfer/page",method:"get",params:e}),{$message:g}=m(),k={name:"Registration",props:{currentId:{type:String,default:""}},setup(e){const{$format:o}=m(),{getAreaTreeData:c}=F(),l=T({showTable:!0,areaTree:[],projectId:"",dialogVisible:!1}),d=s(),t=s({loading:!1,dataList:[],operationFixed:"right",columns:[{prop:"transferNo",label:"转移编号",width:200,cellStyle:{textDecoration:"underline",color:"#6BD2F8FF"},handleClick:a=>f(a)},{prop:"applicantName",label:"申请人"},{prop:"",label:"申请时间",width:190,formatter:a=>o(a.createTime),icon:"el-icon-time",iconStyle:{color:"#69e850",display:"inline-block","font-size":"16px"}},{prop:"projectName",label:"调往部门"},{prop:"position",label:"调往地址"},{prop:"director",label:"新资产负责人"},{prop:"remark",label:"转移说明"}],pagination:{page:1,limit:20,total:0,layout:"total, sizes, prev, pager, next, jumper",handleSize:a=>{t.value.pagination.limit=a,i(e.currentId)},handlePage:a=>{t.value.pagination.page=a,i(e.currentId)}}}),r=s({list:[]});x(async()=>{p(),l.areaTree=await c()});const p=()=>{e.currentId&&i(e.currentId)},i=async a=>{t.value.loading=!0;const v={page:t.value.pagination.page||1,size:0,limit:t.value.pagination.limit,deviceId:a};try{const n=await N(v);console.log(n),t.value.loading=!1,n.status===200?(t.value.dataList=n.data.data,t.value.pagination.total=n.data.total):g.error("获取失败"),t.value.loading=!1}catch{t.value.loading=!1}},f=async a=>{sessionStorage.setItem("row",JSON.stringify(a)),u.push({name:"addTransApply",query:{delFlag:0,title:h(a)}})},h=a=>a.status===2?"调拨审核":"调拨详情",b=s([]);return{FilesList:r,FileList:b,cardTableConfig:t,...I(l),initForm:p,baseForm:d}},methods:{goOff(){u.push({name:"deviceLedgerIndex"})},handlePreview(e){console.log("预览图片",e)},handleExceed(e,o){g.warning(`The limit is 5, you selected ${e.length} files this time, add up to ${e.length+o.length} totally`)},beforeRemove(e,o){console.log(e,o)}}},D={class:"info-container"};function L(e,o,c,l,d,t){const r=_;return B(),S("div",D,[o[0]||(o[0]=$("h3",null,"转移记录",-1)),C(r,{config:l.cardTableConfig,style:{height:"600px"}},null,8,["config"])])}const A=w(k,[["render",L],["__scopeId","data-v-54803dd3"]]);export{A as default};
