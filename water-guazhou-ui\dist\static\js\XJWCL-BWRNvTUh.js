import{o as b}from"./onemap-CEunQziB.js";import{l as g}from"./index-CpGhZCTT.js";import{d as v,r as x,o as y,ay as C,g as s,n as r,aB as m,aJ as _,p as l,q as k,bh as h,i as w,C as S}from"./index-r0dFAfgr.js";const L={class:"inspection-comp-ratio"},N={class:"chart-wrapper"},B={class:"blocks"},F={class:"text"},V={class:"count"},W=v({__name:"XJWCL",setup(Y){const c=(a,t)=>({series:[{type:"gauge",min:0,max:100,startAngle:200,endAngle:-20,splitNumber:12,itemStyle:{color:"rgb(60, 148, 221)"},radius:"100%",center:["50%","65%"],progress:{show:!0,width:12},pointer:{show:!1},axisLine:{lineStyle:{width:12}},axisTick:{show:!1},axisLabel:{show:!1},splitLine:{show:!1},detail:{offsetCenter:[0,0],valueAnimation:!0,formatter(e){return"{value|"+e.toFixed(0)+`}{unit|%}
{name|`+a+"}"},rich:{name:{fontSize:12,color:"rgb(146, 209, 253)",fontWight:"bolder"},value:{fontSize:20,fontWeight:"bolder",color:"rgb(60, 148, 221)"},unit:{fontSize:12,color:"#999"}}},data:[{value:t}]}]}),n=x({data:[{ratio:35.18,option:c("工单及时率",35.18),items:[{label:"完成工单总数",count:4096},{label:"及时完成数",count:1441}]}]}),f=()=>{b().then(a=>{var u;const t=((u=a.data)==null?void 0:u.data)??{},e=t.complete??0,i=t.pending??0,d=t.received??0,o=e+i+d,p=o===0?0:Number((e/o).toFixed(2));n.data[0].items=[{label:"巡检次数",count:o},{label:"完成次数",count:e}],n.data[0].option=c("巡检完成率",p)}),g().then(a=>{var e;const t=((e=a.data)==null?void 0:e.data)||{};n.data[1].items=[{label:"完成工单总数",count:t.totalYearly},{label:"及时完成数",count:(t.totalYearly??0)*(t.percentYearly??0)}],n.data[1].option=c("工单及时率",Number(((t.percentYearly??0)*100).toFixed(2)))})};return y(()=>{f()}),(a,t)=>{const e=C("VChart");return s(),r("div",L,[(s(!0),r(m,null,_(w(n).data,(i,d)=>(s(),r("div",{key:d,class:"item"},[l("div",N,[k(e,{option:i.option},null,8,["option"])]),l("div",B,[(s(!0),r(m,null,_(i.items,(o,p)=>(s(),r("div",{key:p,class:"block-item"},[l("div",F,h(o.label),1),l("div",V,h(o.count),1)]))),128))])]))),128))])}}}),D=S(W,[["__scopeId","data-v-3d06b78f"]]);export{D as default};
