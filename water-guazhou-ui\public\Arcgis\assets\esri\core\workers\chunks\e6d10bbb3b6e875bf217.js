"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5590],{21787:(t,n,e)=>{e.d(n,{a:()=>u,b:()=>f,e:()=>s,f:()=>o,i:()=>h,m:()=>c,s:()=>i,t:()=>a});var r=e(46851);function o(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[4],t[4]=n[5],t[5]=n[6],t[6]=n[8],t[7]=n[9],t[8]=n[10],t}function i(t,n,e,r,o,i,a,s,u,c){return t[0]=n,t[1]=e,t[2]=r,t[3]=o,t[4]=i,t[5]=a,t[6]=s,t[7]=u,t[8]=c,t}function a(t,n){if(t===n){const e=n[1],r=n[2],o=n[5];t[1]=n[3],t[2]=n[6],t[3]=e,t[5]=n[7],t[6]=r,t[7]=o}else t[0]=n[0],t[1]=n[3],t[2]=n[6],t[3]=n[1],t[4]=n[4],t[5]=n[7],t[6]=n[2],t[7]=n[5],t[8]=n[8];return t}function s(t,n){const e=n[0],r=n[1],o=n[2],i=n[3],a=n[4],s=n[5],u=n[6],c=n[7],f=n[8],l=f*a-s*c,h=-f*i+s*u,M=c*i-a*u;let m=e*l+r*h+o*M;return m?(m=1/m,t[0]=l*m,t[1]=(-f*r+o*c)*m,t[2]=(s*r-o*a)*m,t[3]=h*m,t[4]=(f*e-o*u)*m,t[5]=(-s*e+o*i)*m,t[6]=M*m,t[7]=(-c*e+r*u)*m,t[8]=(a*e-r*i)*m,t):null}function u(t,n){const e=n[0],r=n[1],o=n[2],i=n[3],a=n[4],s=n[5],u=n[6],c=n[7],f=n[8];return t[0]=a*f-s*c,t[1]=o*c-r*f,t[2]=r*s-o*a,t[3]=s*u-i*f,t[4]=e*f-o*u,t[5]=o*i-e*s,t[6]=i*c-a*u,t[7]=r*u-e*c,t[8]=e*a-r*i,t}function c(t,n,e){const r=n[0],o=n[1],i=n[2],a=n[3],s=n[4],u=n[5],c=n[6],f=n[7],l=n[8],h=e[0],M=e[1],m=e[2],b=e[3],d=e[4],g=e[5],O=e[6],_=e[7],A=e[8];return t[0]=h*r+M*a+m*c,t[1]=h*o+M*s+m*f,t[2]=h*i+M*u+m*l,t[3]=b*r+d*a+g*c,t[4]=b*o+d*s+g*f,t[5]=b*i+d*u+g*l,t[6]=O*r+_*a+A*c,t[7]=O*o+_*s+A*f,t[8]=O*i+_*u+A*l,t}function f(t,n){const e=n[0],r=n[1],o=n[2],i=n[3],a=n[4],s=n[5],u=n[6],c=n[7],f=n[8],l=n[9],h=n[10],M=n[11],m=n[12],b=n[13],d=n[14],g=n[15],O=e*s-r*a,_=e*u-o*a,A=e*c-i*a,T=r*u-o*s,E=r*c-i*s,S=o*c-i*u,p=f*b-l*m,I=f*d-h*m,R=f*g-M*m,y=l*d-h*b,P=l*g-M*b,N=h*g-M*d;let L=O*N-_*P+A*y+T*R-E*I+S*p;return L?(L=1/L,t[0]=(s*N-u*P+c*y)*L,t[1]=(u*R-a*N-c*I)*L,t[2]=(a*P-s*R+c*p)*L,t[3]=(o*P-r*N-i*y)*L,t[4]=(e*N-o*R+i*I)*L,t[5]=(r*R-e*P-i*p)*L,t[6]=(b*S-d*E+g*T)*L,t[7]=(d*A-m*S-g*_)*L,t[8]=(m*E-b*A+g*O)*L,t):null}function l(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t[4]=n[4]-e[4],t[5]=n[5]-e[5],t[6]=n[6]-e[6],t[7]=n[7]-e[7],t[8]=n[8]-e[8],t}function h(t){const n=(0,r.g)(),e=t[0],o=t[1],i=t[2],a=t[3],s=t[4],u=t[5],c=t[6],f=t[7],l=t[8];return Math.abs(1-(e*e+a*a+c*c))<=n&&Math.abs(1-(o*o+s*s+f*f))<=n&&Math.abs(1-(i*i+u*u+l*l))<=n}const M=c,m=l;Object.freeze(Object.defineProperty({__proto__:null,add:function(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t[2]=n[2]+e[2],t[3]=n[3]+e[3],t[4]=n[4]+e[4],t[5]=n[5]+e[5],t[6]=n[6]+e[6],t[7]=n[7]+e[7],t[8]=n[8]+e[8],t},adjoint:u,copy:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t},determinant:function(t){const n=t[0],e=t[1],r=t[2],o=t[3],i=t[4],a=t[5],s=t[6],u=t[7],c=t[8];return n*(c*i-a*u)+e*(-c*o+a*s)+r*(u*o-i*s)},equals:function(t,n){const e=t[0],o=t[1],i=t[2],a=t[3],s=t[4],u=t[5],c=t[6],f=t[7],l=t[8],h=n[0],M=n[1],m=n[2],b=n[3],d=n[4],g=n[5],O=n[6],_=n[7],A=n[8],T=(0,r.g)();return Math.abs(e-h)<=T*Math.max(1,Math.abs(e),Math.abs(h))&&Math.abs(o-M)<=T*Math.max(1,Math.abs(o),Math.abs(M))&&Math.abs(i-m)<=T*Math.max(1,Math.abs(i),Math.abs(m))&&Math.abs(a-b)<=T*Math.max(1,Math.abs(a),Math.abs(b))&&Math.abs(s-d)<=T*Math.max(1,Math.abs(s),Math.abs(d))&&Math.abs(u-g)<=T*Math.max(1,Math.abs(u),Math.abs(g))&&Math.abs(c-O)<=T*Math.max(1,Math.abs(c),Math.abs(O))&&Math.abs(f-_)<=T*Math.max(1,Math.abs(f),Math.abs(_))&&Math.abs(l-A)<=T*Math.max(1,Math.abs(l),Math.abs(A))},exactEquals:function(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]&&t[8]===n[8]},frob:function(t){return Math.sqrt(t[0]**2+t[1]**2+t[2]**2+t[3]**2+t[4]**2+t[5]**2+t[6]**2+t[7]**2+t[8]**2)},fromMat2d:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=0,t[3]=n[2],t[4]=n[3],t[5]=0,t[6]=n[4],t[7]=n[5],t[8]=1,t},fromMat4:o,fromQuat:function(t,n){const e=n[0],r=n[1],o=n[2],i=n[3],a=e+e,s=r+r,u=o+o,c=e*a,f=r*a,l=r*s,h=o*a,M=o*s,m=o*u,b=i*a,d=i*s,g=i*u;return t[0]=1-l-m,t[3]=f-g,t[6]=h+d,t[1]=f+g,t[4]=1-c-m,t[7]=M-b,t[2]=h-d,t[5]=M+b,t[8]=1-c-l,t},fromRotation:function(t,n){const e=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=e,t[2]=0,t[3]=-e,t[4]=r,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},fromScaling:function(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=0,t[4]=n[1],t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},fromTranslation:function(t,n){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=n[0],t[7]=n[1],t[8]=1,t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},invert:s,isOrthoNormal:h,mul:M,multiply:c,multiplyScalar:function(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t[2]=n[2]*e,t[3]=n[3]*e,t[4]=n[4]*e,t[5]=n[5]*e,t[6]=n[6]*e,t[7]=n[7]*e,t[8]=n[8]*e,t},multiplyScalarAndAdd:function(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t[2]=n[2]+e[2]*r,t[3]=n[3]+e[3]*r,t[4]=n[4]+e[4]*r,t[5]=n[5]+e[5]*r,t[6]=n[6]+e[6]*r,t[7]=n[7]+e[7]*r,t[8]=n[8]+e[8]*r,t},normalFromMat4:f,normalFromMat4Legacy:function(t,n){const e=n[0],r=n[1],o=n[2],i=n[4],a=n[5],s=n[6],u=n[8],c=n[9],f=n[10],l=f*a-s*c,h=-f*i+s*u,M=c*i-a*u,m=e*l+r*h+o*M;if(!m)return null;const b=1/m;return t[0]=l*b,t[1]=(-f*r+o*c)*b,t[2]=(s*r-o*a)*b,t[3]=h*b,t[4]=(f*e-o*u)*b,t[5]=(-s*e+o*i)*b,t[6]=M*b,t[7]=(-c*e+r*u)*b,t[8]=(a*e-r*i)*b,t},projection:function(t,n,e){return t[0]=2/n,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/e,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t},rotate:function(t,n,e){const r=n[0],o=n[1],i=n[2],a=n[3],s=n[4],u=n[5],c=n[6],f=n[7],l=n[8],h=Math.sin(e),M=Math.cos(e);return t[0]=M*r+h*a,t[1]=M*o+h*s,t[2]=M*i+h*u,t[3]=M*a-h*r,t[4]=M*s-h*o,t[5]=M*u-h*i,t[6]=c,t[7]=f,t[8]=l,t},scale:function(t,n,e){const r=e[0],o=e[1],i=e[2];return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=o*n[3],t[4]=o*n[4],t[5]=o*n[5],t[6]=i*n[6],t[7]=i*n[7],t[8]=i*n[8],t},scaleByVec2:function(t,n,e){const r=e[0],o=e[1];return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=o*n[3],t[4]=o*n[4],t[5]=o*n[5],t},set:i,str:function(t){return"mat3("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+")"},sub:m,subtract:l,translate:function(t,n,e){const r=n[0],o=n[1],i=n[2],a=n[3],s=n[4],u=n[5],c=n[6],f=n[7],l=n[8],h=e[0],M=e[1];return t[0]=r,t[1]=o,t[2]=i,t[3]=a,t[4]=s,t[5]=u,t[6]=h*r+M*a+c,t[7]=h*o+M*s+f,t[8]=h*i+M*u+l,t},transpose:a},Symbol.toStringTag,{value:"Module"}))},46521:(t,n,e)=>{function r(){return[1,0,0,0,1,0,0,0,1]}function o(t,n,e,r,o,i,a,s,u){return[t,n,e,r,o,i,a,s,u]}function i(t,n){return new Float64Array(t,n,9)}e.d(n,{a:()=>i,c:()=>r,f:()=>o}),Object.freeze(Object.defineProperty({__proto__:null,clone:function(t){return[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8]]},create:r,createView:i,fromValues:o},Symbol.toStringTag,{value:"Module"}))},13598:(t,n,e)=>{function r(){return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]}function o(t){return[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15]]}function i(t,n){return new Float64Array(t,n,16)}e.d(n,{I:()=>a,a:()=>i,b:()=>o,c:()=>r});const a=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1];Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:a,clone:o,create:r,createView:i,fromValues:function(t,n,e,r,o,i,a,s,u,c,f,l,h,M,m,b){return[t,n,e,r,o,i,a,s,u,c,f,l,h,M,m,b]}},Symbol.toStringTag,{value:"Module"}))},51305:(t,n,e)=>{e.d(n,{c:()=>M,g:()=>f,j:()=>P,k:()=>b,m:()=>l,s:()=>c});var r=e(46521),o=e(94961),i=e(65617),a=e(46851),s=e(17896),u=e(98766);function c(t,n,e){e*=.5;const r=Math.sin(e);return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=Math.cos(e),t}function f(t,n){const e=2*Math.acos(n[3]),r=Math.sin(e/2);return r>(0,a.g)()?(t[0]=n[0]/r,t[1]=n[1]/r,t[2]=n[2]/r):(t[0]=1,t[1]=0,t[2]=0),e}function l(t,n,e){const r=n[0],o=n[1],i=n[2],a=n[3],s=e[0],u=e[1],c=e[2],f=e[3];return t[0]=r*f+a*s+o*c-i*u,t[1]=o*f+a*u+i*s-r*c,t[2]=i*f+a*c+r*u-o*s,t[3]=a*f-r*s-o*u-i*c,t}function h(t,n,e,r){const o=n[0],i=n[1],s=n[2],u=n[3];let c,f,l,h,M,m=e[0],b=e[1],d=e[2],g=e[3];return f=o*m+i*b+s*d+u*g,f<0&&(f=-f,m=-m,b=-b,d=-d,g=-g),1-f>(0,a.g)()?(c=Math.acos(f),l=Math.sin(c),h=Math.sin((1-r)*c)/l,M=Math.sin(r*c)/l):(h=1-r,M=r),t[0]=h*o+M*m,t[1]=h*i+M*b,t[2]=h*s+M*d,t[3]=h*u+M*g,t}function M(t,n){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t[3]=n[3],t}function m(t,n){const e=n[0]+n[4]+n[8];let r;if(e>0)r=Math.sqrt(e+1),t[3]=.5*r,r=.5/r,t[0]=(n[5]-n[7])*r,t[1]=(n[6]-n[2])*r,t[2]=(n[1]-n[3])*r;else{let e=0;n[4]>n[0]&&(e=1),n[8]>n[3*e+e]&&(e=2);const o=(e+1)%3,i=(e+2)%3;r=Math.sqrt(n[3*e+e]-n[3*o+o]-n[3*i+i]+1),t[e]=.5*r,r=.5/r,t[3]=(n[3*o+i]-n[3*i+o])*r,t[o]=(n[3*o+e]+n[3*e+o])*r,t[i]=(n[3*i+e]+n[3*e+i])*r}return t}function b(t,n,e,r){const o=.5*Math.PI/180;n*=o,e*=o,r*=o;const i=Math.sin(n),a=Math.cos(n),s=Math.sin(e),u=Math.cos(e),c=Math.sin(r),f=Math.cos(r);return t[0]=i*u*f-a*s*c,t[1]=a*s*f+i*u*c,t[2]=a*u*c-i*s*f,t[3]=a*u*f+i*s*c,t}const d=u.c,g=u.s,O=u.a,_=l,A=u.b,T=u.d,E=u.l,S=u.e,p=S,I=u.f,R=I,y=u.n,P=u.g,N=u.h,L=(0,i.c)(),B=(0,i.f)(1,0,0),C=(0,i.f)(0,1,0),F=(0,o.a)(),U=(0,o.a)(),w=(0,r.c)();Object.freeze(Object.defineProperty({__proto__:null,add:O,calculateW:function(t,n){const e=n[0],r=n[1],o=n[2];return t[0]=e,t[1]=r,t[2]=o,t[3]=Math.sqrt(Math.abs(1-e*e-r*r-o*o)),t},conjugate:M,copy:d,dot:T,equals:N,exactEquals:P,fromEuler:b,fromMat3:m,getAxisAngle:f,identity:function(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t},invert:function(t,n){const e=n[0],r=n[1],o=n[2],i=n[3],a=e*e+r*r+o*o+i*i,s=a?1/a:0;return t[0]=-e*s,t[1]=-r*s,t[2]=-o*s,t[3]=i*s,t},len:p,length:S,lerp:E,mul:_,multiply:l,normalize:y,random:function(t){const n=a.R,e=n(),r=n(),o=n(),i=Math.sqrt(1-e),s=Math.sqrt(e);return t[0]=i*Math.sin(2*Math.PI*r),t[1]=i*Math.cos(2*Math.PI*r),t[2]=s*Math.sin(2*Math.PI*o),t[3]=s*Math.cos(2*Math.PI*o),t},rotateX:function(t,n,e){e*=.5;const r=n[0],o=n[1],i=n[2],a=n[3],s=Math.sin(e),u=Math.cos(e);return t[0]=r*u+a*s,t[1]=o*u+i*s,t[2]=i*u-o*s,t[3]=a*u-r*s,t},rotateY:function(t,n,e){e*=.5;const r=n[0],o=n[1],i=n[2],a=n[3],s=Math.sin(e),u=Math.cos(e);return t[0]=r*u-i*s,t[1]=o*u+a*s,t[2]=i*u+r*s,t[3]=a*u-o*s,t},rotateZ:function(t,n,e){e*=.5;const r=n[0],o=n[1],i=n[2],a=n[3],s=Math.sin(e),u=Math.cos(e);return t[0]=r*u+o*s,t[1]=o*u-r*s,t[2]=i*u+a*s,t[3]=a*u-i*s,t},rotationTo:function(t,n,e){const r=(0,s.e)(n,e);return r<-.999999?((0,s.f)(L,B,n),(0,s.u)(L)<1e-6&&(0,s.f)(L,C,n),(0,s.n)(L,L),c(t,L,Math.PI),t):r>.999999?(t[0]=0,t[1]=0,t[2]=0,t[3]=1,t):((0,s.f)(L,n,e),t[0]=L[0],t[1]=L[1],t[2]=L[2],t[3]=1+r,y(t,t))},scale:A,set:g,setAxes:function(t,n,e,r){const o=w;return o[0]=e[0],o[3]=e[1],o[6]=e[2],o[1]=r[0],o[4]=r[1],o[7]=r[2],o[2]=-n[0],o[5]=-n[1],o[8]=-n[2],y(t,m(t,o))},setAxisAngle:c,slerp:h,sqlerp:function(t,n,e,r,o,i){return h(F,n,o,i),h(U,e,r,i),h(t,F,U,2*i*(1-i)),t},sqrLen:R,squaredLength:I,str:function(t){return"quat("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+")"}},Symbol.toStringTag,{value:"Module"}))},94961:(t,n,e)=>{function r(){return[0,0,0,1]}function o(t){return[t[0],t[1],t[2],t[3]]}function i(t,n){return new Float64Array(t,n,4)}e.d(n,{I:()=>a,a:()=>r,b:()=>o,c:()=>i});const a=[0,0,0,1];Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:a,clone:o,create:r,createView:i,fromValues:function(t,n,e,r){return[t,n,e,r]}},Symbol.toStringTag,{value:"Module"}))},29268:(t,n,e)=>{e.d(n,{a:()=>O,c:()=>d,g:()=>_,h:()=>g,j:()=>E,m:()=>N}),e(80442);var r,o,i=e(92604),a=e(22021),s=e(70586),u=e(52138),c=e(17896),f=e(65617),l=e(98766),h=e(88669);(o=r||(r={}))[o.X=0]="X",o[o.Y=1]="Y",o[o.Z=2]="Z";var M=e(78341),m=e(61277),b=e(12981);function d(){return(0,h.c)()}function g(t,n=d()){return(0,l.c)(n,t)}function O(t){return t[3]}function _(t){return t}function A(t,n,e){if((0,s.Wi)(n))return!1;const{origin:r,direction:o}=n,i=T;i[0]=r[0]-t[0],i[1]=r[1]-t[1],i[2]=r[2]-t[2];const a=o[0]*o[0]+o[1]*o[1]+o[2]*o[2];if(0===a)return!1;const u=2*(o[0]*i[0]+o[1]*i[1]+o[2]*i[2]),c=u*u-4*a*(i[0]*i[0]+i[1]*i[1]+i[2]*i[2]-t[3]*t[3]);if(c<0)return!1;const f=Math.sqrt(c);let l=(-u-f)/(2*a);const h=(-u+f)/(2*a);return(l<0||h<l&&h>0)&&(l=h),!(l<0||(e&&(e[0]=r[0]+o[0]*l,e[1]=r[1]+o[1]*l,e[2]=r[2]+o[2]*l),0))}const T=(0,f.c)();function E(t,n){return A(t,n,null)}function S(t,n,e){const r=b.WM.get(),o=b.MP.get();(0,c.f)(r,n.origin,n.direction);const i=O(t);(0,c.f)(e,r,n.origin),(0,c.g)(e,e,1/(0,c.l)(e)*i);const a=I(t,n.origin),s=(0,m.EU)(n.origin,e);return(0,u.d)(o,s+a,r),(0,c.m)(e,e,o),e}function p(t,n,e){const r=(0,c.b)(b.WM.get(),n,t),o=(0,c.g)(b.WM.get(),r,t[3]/(0,c.l)(r));return(0,c.a)(e,o,t)}function I(t,n){const e=(0,c.b)(b.WM.get(),n,t),r=(0,c.l)(e),o=O(t),i=o+Math.abs(o-r);return(0,a.ZF)(o/i)}const R=(0,f.c)();function y(t,n,e,o){const i=(0,c.b)(R,n,t);switch(e){case r.X:{const t=(0,a.jE)(i,R)[2];return(0,c.s)(o,-Math.sin(t),Math.cos(t),0)}case r.Y:{const t=(0,a.jE)(i,R),n=t[1],e=t[2],r=Math.sin(n);return(0,c.s)(o,-r*Math.cos(e),-r*Math.sin(e),Math.cos(n))}case r.Z:return(0,c.n)(o,i);default:return}}function P(t,n){const e=(0,c.b)(L,n,t);return(0,c.l)(e)-t[3]}function N(t,n){const e=(0,c.d)(t,n),r=O(t);return e<=r*r}const L=(0,f.c)(),B=d();Object.freeze(Object.defineProperty({__proto__:null,altitudeAt:P,angleToSilhouette:I,axisAt:y,clear:function(t){t[0]=t[1]=t[2]=t[3]=0},closestPoint:function(t,n,e){return A(t,n,e)?e:((0,M.JI)(n,t,e),p(t,e,e))},closestPointOnSilhouette:S,containsPoint:N,copy:g,create:d,distanceToSilhouette:function(t,n){const e=(0,c.b)(b.WM.get(),n,t),r=(0,c.p)(e),o=t[3]*t[3];return Math.sqrt(Math.abs(r-o))},elevate:function(t,n,e){return t!==e&&(0,c.c)(e,t),e[3]=t[3]+n,e},fromCenterAndRadius:function(t,n){return(0,h.f)(t[0],t[1],t[2],n)},fromRadius:function(t,n){return t[0]=t[1]=t[2]=0,t[3]=n,t},fromValues:function(t,n,e,r){return(0,h.f)(t,n,e,r)},getCenter:_,getRadius:O,intersectRay:A,intersectRayClosestSilhouette:function(t,n,e){if(A(t,n,e))return e;const r=S(t,n,b.WM.get());return(0,c.a)(e,n.origin,(0,c.g)(b.WM.get(),n.direction,(0,c.i)(n.origin,r)/(0,c.l)(n.direction))),e},intersectsRay:E,projectPoint:p,setAltitudeAt:function(t,n,e,o){const i=P(t,n),a=y(t,n,r.Z,L),s=(0,c.g)(L,a,e-i);return(0,c.a)(o,n,s)},setExtent:function(t,n,e){return i.Z.getLogger("esri.geometry.support.sphere").error("sphere.setExtent is not yet supported"),t===e?e:g(t,e)},tmpSphere:B,wrap:function(t){return t}},Symbol.toStringTag,{value:"Module"}))},97323:(t,n,e)=>{function r(){return[0,0]}function o(t,n){return[t,n]}function i(t,n){return new Float64Array(t,n,2)}function a(){return o(1,1)}function s(){return o(1,0)}function u(){return o(0,1)}e.d(n,{Z:()=>c,a:()=>r,c:()=>i,f:()=>o});const c=[0,0],f=a(),l=s(),h=u();Object.freeze(Object.defineProperty({__proto__:null,ONES:f,UNIT_X:l,UNIT_Y:h,ZEROS:c,clone:function(t){return[t[0],t[1]]},create:r,createView:i,fromArray:function(t){const n=[0,0],e=Math.min(2,t.length);for(let r=0;r<e;++r)n[r]=t[r];return n},fromValues:o,ones:a,unitX:s,unitY:u,zeros:function(){return[0,0]}},Symbol.toStringTag,{value:"Module"}))},72119:(t,n,e)=>{function r(){return new Float32Array(3)}function o(t){const n=new Float32Array(3);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n}function i(t,n,e){const r=new Float32Array(3);return r[0]=t,r[1]=n,r[2]=e,r}function a(){return r()}function s(){return i(1,1,1)}function u(){return i(1,0,0)}function c(){return i(0,1,0)}function f(){return i(0,0,1)}e.d(n,{b:()=>o,c:()=>r,f:()=>i});const l=a(),h=s(),M=u(),m=c(),b=f();Object.freeze(Object.defineProperty({__proto__:null,ONES:h,UNIT_X:M,UNIT_Y:m,UNIT_Z:b,ZEROS:l,clone:o,create:r,createView:function(t,n){return new Float32Array(t,n,3)},fromValues:i,ones:s,unitX:u,unitY:c,unitZ:f,zeros:a},Symbol.toStringTag,{value:"Module"}))},22807:(t,n,e)=>{e.d(n,{x:()=>o});var r=e(41213);class o{constructor(t){this._allocator=t,this._items=[],this._itemsPtr=0,this._grow()}get(){return 0===this._itemsPtr&&(0,r.Y)((()=>this._reset())),this._itemsPtr===this._items.length&&this._grow(),this._items[this._itemsPtr++]}_reset(){const t=Math.min(3*Math.max(8,this._itemsPtr),this._itemsPtr+3*i);this._items.length=Math.min(t,this._items.length),this._itemsPtr=0}_grow(){for(let t=0;t<Math.max(8,Math.min(this._items.length,i));t++)this._items.push(this._allocator())}}const i=1024},43090:(t,n,e)=>{function r(t){return 32+t.length}function o(t){if(!t)return 0;let n=u;for(const e in t)if(t.hasOwnProperty(e)){const o=t[e];switch(typeof o){case"string":n+=r(o);break;case"number":n+=16;break;case"boolean":n+=4}}return n}function i(t){if(!t)return 0;if(Array.isArray(t))return function(t){const n=t.length;if(0===n||"number"==typeof t[0])return 32+8*n;let e=c;for(let r=0;r<n;r++)e+=a(t[r]);return e}(t);let n=u;for(const e in t)t.hasOwnProperty(e)&&(n+=a(t[e]));return n}function a(t){switch(typeof t){case"object":return i(t);case"string":return r(t);case"number":return 16;case"boolean":return 4;default:return 8}}function s(t,n){return c+t.length*n}e.d(n,{Ul:()=>i,Y8:()=>f,do:()=>s,f2:()=>o});const u=32,c=32;var f;!function(t){t[t.KILOBYTES=1024]="KILOBYTES",t[t.MEGABYTES=1048576]="MEGABYTES",t[t.GIGABYTES=1073741824]="GIGABYTES"}(f||(f={}))},78341:(t,n,e)=>{e.d(n,{JI:()=>c,Ue:()=>a,re:()=>u}),e(67676);var r=e(22807),o=e(17896),i=e(65617);function a(t){return t?s((0,i.a)(t.origin),(0,i.a)(t.direction)):s((0,i.c)(),(0,i.c)())}function s(t,n){return{origin:t,direction:n}}function u(t,n){const e=f.get();return e.origin=t,e.direction=n,e}function c(t,n,e){const r=(0,o.e)(t.direction,(0,o.b)(e,n,t.origin));return(0,o.a)(e,t.origin,(0,o.g)(e,t.direction,r)),e}e(12981);const f=new r.x((()=>a()))},61277:(t,n,e)=>{e.d(n,{EU:()=>a});var r=e(22021),o=e(17896),i=e(65617);function a(t,n){const e=(0,o.e)(t,n)/((0,o.l)(t)*(0,o.l)(n));return-(0,r.ZF)(e)}(0,i.c)(),(0,i.c)()},12981:(t,n,e)=>{e.d(n,{MP:()=>m,WM:()=>M});var r=e(43090),o=e(41213),i=e(46521),a=e(13598),s=e(94961),u=e(97323),c=e(65617),f=e(88669);class l{constructor(t,n,e){this._itemByteSize=t,this._itemCreate=n,this._buffers=new Array,this._items=new Array,this._itemsPtr=0,this._itemsPerBuffer=Math.ceil(e/this._itemByteSize)}get(){0===this._itemsPtr&&(0,o.Y)((()=>this._reset()));const t=Math.floor(this._itemsPtr/this._itemsPerBuffer);for(;this._buffers.length<=t;){const t=new ArrayBuffer(this._itemsPerBuffer*this._itemByteSize);for(let n=0;n<this._itemsPerBuffer;++n)this._items.push(this._itemCreate(t,n*this._itemByteSize));this._buffers.push(t)}return this._items[this._itemsPtr++]}_reset(){const t=2*(Math.floor(this._itemsPtr/this._itemsPerBuffer)+1);for(;this._buffers.length>t;)this._buffers.pop(),this._items.length=this._buffers.length*this._itemsPerBuffer;this._itemsPtr=0}static createVec2f64(t=h){return new l(16,u.c,t)}static createVec3f64(t=h){return new l(24,c.b,t)}static createVec4f64(t=h){return new l(32,f.a,t)}static createMat3f64(t=h){return new l(72,i.a,t)}static createMat4f64(t=h){return new l(128,a.a,t)}static createQuatf64(t=h){return new l(32,s.c,t)}get test(){return{size:this._buffers.length*this._itemsPerBuffer*this._itemByteSize}}}const h=4*r.Y8.KILOBYTES,M=(l.createVec2f64(),l.createVec3f64()),m=(l.createVec4f64(),l.createMat3f64(),l.createMat4f64());l.createQuatf64()},6206:(t,n,e)=>{var r,o;e.d(n,{a9:()=>r}),e(22021),(o=r||(r={}))[o.Multiply=1]="Multiply",o[o.Ignore=2]="Ignore",o[o.Replace=3]="Replace",o[o.Tint=4]="Tint"},51417:(t,n,e)=>{e.d(n,{a:()=>r});class r{constructor(t,n,e=!1,r=n){this.data=t,this.size=n,this.exclusive=e,this.stride=r}}},35065:(t,n,e)=>{var r;e.d(n,{T:()=>r}),function(t){t.POSITION="position",t.NORMAL="normal",t.UV0="uv0",t.AUXPOS1="auxpos1",t.AUXPOS2="auxpos2",t.COLOR="color",t.SYMBOLCOLOR="symbolColor",t.SIZE="size",t.TANGENT="tangent",t.OFFSET="offset",t.SUBDIVISIONFACTOR="subdivisionFactor",t.COLORFEATUREATTRIBUTE="colorFeatureAttribute",t.SIZEFEATUREATTRIBUTE="sizeFeatureAttribute",t.OPACITYFEATUREATTRIBUTE="opacityFeatureAttribute",t.DISTANCETOSTART="distanceToStart",t.UVMAPSPACE="uvMapSpace",t.BOUNDINGRECT="boundingRect",t.UVREGION="uvRegion",t.NORMALCOMPRESSED="normalCompressed",t.PROFILERIGHT="profileRight",t.PROFILEUP="profileUp",t.PROFILEVERTEXANDNORMAL="profileVertexAndNormal",t.FEATUREVALUE="featureValue",t.MODELORIGINHI="modelOriginHi",t.MODELORIGINLO="modelOriginLo",t.MODEL="model",t.MODELNORMAL="modelNormal",t.INSTANCECOLOR="instanceColor",t.INSTANCEFEATUREATTRIBUTE="instanceFeatureAttribute",t.LOCALTRANSFORM="localTransform",t.GLOBALTRANSFORM="globalTransform",t.BOUNDINGSPHERE="boundingSphere",t.MODELORIGIN="modelOrigin",t.MODELSCALEFACTORS="modelScaleFactors",t.FEATUREATTRIBUTE="featureAttribute",t.STATE="state",t.LODLEVEL="lodLevel",t.POSITION0="position0",t.POSITION1="position1",t.NORMALA="normalA",t.NORMALB="normalB",t.COMPONENTINDEX="componentIndex",t.VARIANTOFFSET="variantOffset",t.VARIANTSTROKE="variantStroke",t.VARIANTEXTENSION="variantExtension",t.U8PADDING="u8padding",t.U16PADDING="u16padding",t.SIDENESS="sideness",t.START="start",t.END="end",t.UP="up",t.EXTRUDE="extrude",t.OBJECTANDLAYERIDCOLOR="objectAndLayerIdColor",t.OBJECTANDLAYERIDCOLOR_INSTANCED="objectAndLayerIdColor_instanced"}(r||(r={}))},64822:(t,n,e)=>{var r;e.d(n,{JY:()=>r}),function(t){t[t.Global=1]="Global",t[t.Local=2]="Local"}(r||(r={}))}}]);