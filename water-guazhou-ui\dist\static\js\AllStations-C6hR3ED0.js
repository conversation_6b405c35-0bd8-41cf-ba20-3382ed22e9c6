import{d as v,r as _,c as S,s as F,o as I,g as M,n as B,q as h,i as b,_ as R,aq as A,C as L}from"./index-r0dFAfgr.js";import{w as P}from"./Point-WxyopZva.js";import{g as V}from"./URLHelper-B9aplt5w.js";import{a as $}from"./useStation-DJgnSZIA.js";import{P as q}from"./index-CcDafpIP.js";import"./zhandian-YaGuQZe6.js";const D={class:"onemap-panel-wrapper"},T=v({__name:"AllStations",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(y,{emit:x}){const p=x,r=y,n=_({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:e,size:s})=>{n.pagination.page=e,n.pagination.limit=s},layout:"total,sizes, jumper"},handleRowClick:e=>w(e),columns:[{minWidth:120,label:"名称",prop:"name",sortable:!0},{minWidth:120,label:"类型",prop:"type",sortable:!0}]}),C=_({labelPosition:"top",group:[{fields:[{type:"input",field:"name",itemContainerStyle:{marginBottom:"10px"},appendBtns:[{perm:!0,text:"刷新",click:()=>l()}],onChange:()=>l()}]}],defaultValue:{}}),c=S(),k=$(),l=async()=>{var e,s;n.loading=!0;try{const o=await k.getStations(),i=(s=(e=c.value)==null?void 0:e.dataForm)==null?void 0:s.name;n.dataList=o.filter(t=>{var a;return i?(a=t.name)==null?void 0:a.includes(i):!0});const m=[];o.map(t=>{var d,f,u;const a=(d=t.location)==null?void 0:d.split(",");if((a==null?void 0:a.length)===2){const g=new P({longitude:a[0],latitude:a[1],spatialReference:(f=r.view)==null?void 0:f.spatialReference});m.push({visible:!1,id:t.id,x:g.x,y:g.y,offsetY:-40,title:t.name,customComponent:F(q),customConfig:{info:{type:"attrs",imageUrl:t.imgs,stationId:t.id}},attributes:{path:r.menu.path,id:t.id,row:{...t,stationId:t.id,fromAllStation:!0}},symbolConfig:{url:V(((u=t.name)==null?void 0:u.indexOf("热"))!==-1?"测流压站.png":`${t.type}.png`)}})}}),p("addMarks",{windows:m})}catch(o){console.dir(o)}n.loading=!1},w=async e=>{p("highlightMark",r.menu,e==null?void 0:e.id)};return I(()=>{l()}),(e,s)=>{const o=R,i=A;return M(),B("div",D,[h(o,{ref_key:"refForm",ref:c,config:b(C)},null,8,["config"]),h(i,{class:"table-box",config:b(n)},null,8,["config"])])}}}),O=L(T,[["__scopeId","data-v-a8eb6fe5"]]);export{O as default};
