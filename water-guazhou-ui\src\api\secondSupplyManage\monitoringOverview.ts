// 智慧生产=二供管理-监测总览 api
import request from '@/plugins/axios';

/**
 * 查询二供泵房列表以及各个泵房的今日、昨日、本月供水量
 * @param params
 * @returns
 */
export const getWaterSupplyInfo = (params?: {
  projectId?: string;
  name?: string;
}) => {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyInfo',
    method: 'get',
    params
  });
};

// 查询消防栓列表
export const getFireAlarmList = (params: { status?: string }) => {
  return request({
    url: '/istar/api/xiaofangshuanStation/getList',
    method: 'get',
    params
  });
};

// 查询消防栓数据趋势
export const getFireAlarmFlowData = (params: {
  // 站点id
  stationId: string;
  // 开始时间戳
  start: string | number;
  // 结束时间戳
  end: string | number;
}) => {
  return request({
    url: '/istar/api/xiaofangshuanStation/flowData',
    method: 'get',
    params
  });
};

// 查询单个二供泵房的详细供水数据（今日、昨日、本月）、出水压力、瞬时流量曲线数据
export function getWaterSupplyDetail(stationId: string) {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyDetail',
    method: 'get',
    params: {
      stationId: stationId || ''
    }
  });
}

// 查询二供泵房供水量总览数据。包含：今日、昨日、本月、上月、总供水、报警数
export function getWaterSupplyInfoView(params: { projectId: string }) {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyInfoView',
    method: 'get',
    params
  });
}

// 查询二供泵房的实时数据列表
export function getWaterSupplyInfoDetail(params: { projectId: string }) {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyInfoDetail',
    method: 'get',
    params
  });
}

// 查询指定属性的近三天数据曲线
export function getThreeDaysData(params: { deviceId: string; attr: string }) {
  return request({
    url: '/istar/api/station/data/getThreeDaysData',
    method: 'get',
    params
  });
}

// 查询指定站点的指定类型的历史数据
export function stationDayDataQuery(params: {
  stationId: string;
  start: any;
  end: any;
  filterStart: string;
  filterEnd: string;
  queryType: string;
  group?: string;
  attributeId?: string;
}) {
  return request({
    url: '/istar/api/station/data/stationDayDataQuery',
    method: 'get',
    params
  });
}
