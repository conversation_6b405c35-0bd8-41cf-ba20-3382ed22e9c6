/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.obtain;

import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;

import java.util.List;

public interface BaseObtainDataService {

    /**
     * 周期报警获取数据，获取一段时间内，指定步长的第一条数据
     * @param start
     * @param end
     * @param formula
     * @param tenantId
     * @param timeLimit
     * @return
     * @throws ThingsboardException
     */
    List<ResponseTs> getFirstDataFromOpenTSDB(long start, long end, String formula, TenantId
            tenantId, String timeLimit) throws ThingsboardException;
}
