package org.thingsboard.server.dao.shuiwu;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.SwXjrwCEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.SwXjrwMEntity;

import java.util.Date;
import java.util.List;

public interface SwXjrwService {
    SwXjrwMEntity getXjrwM(String id);

    PageData<SwXjrwMEntity> findList(int page, int size, String content, String status, String deviceId, TenantId tenantId);

    PageData<SwXjrwMEntity> findHistoryList(int page, int size, String content, Date createTime1, Date createTime2, String deviceId, TenantId tenantId);

    void saveXJRWM(SwXjrwMEntity xjrwmEntity);

    void save(List<SwXjrwCEntity> xjrwcEntityList);

    JSONObject complete(JSONObject params);

    Object end(JSONObject params);

    JSONObject checkExecutor(String xjrwId, User currentUser) throws ThingsboardException;

    List<SwXjrwMEntity> findAllByTenantId(TenantId tenantId);

    Object groupByStatus(String deviceId);

    List<SwXjrwMEntity> findAllByTenantId(TenantId tenantId, Date startTime, Date endTime);

    Object countStatus(Long start, Long end, User currentUser);
}
