import Graphic from '@arcgis/core/Graphic';
import Point from '@arcgis/core/geometry/Point';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol';
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol';
import TextSymbol from '@arcgis/core/symbols/TextSymbol';
import * as geometryEngine from '@arcgis/core/geometry/geometryEngine.js';
import SimpleLineSymbol from '@arcgis/core/symbols/SimpleLineSymbol';
import SimpleFillSymbol from '@arcgis/core/symbols/SimpleFillSymbol';
import Color from '@arcgis/core/Color';
import Polygon from '@arcgis/core/geometry/Polygon';
import Extent from '@arcgis/core/geometry/Extent';
import Polyline from '@arcgis/core/geometry/Polyline';
import { calcStringWidth } from './ToolHelper';

/**
 * 创建一个简单的标注
 * @param x
 * @param y
 * @param options
 * @returns
 */
export const createSimpleMarker = (
  x: number,
  y: number,
  options: {
    color?: string | number[];
    outLineColor?: string | number[];
    xOffset?: number;
    yOffset?: number;
    spatialReference?: any;
    attributes?: any;
  }
) => {
  //
  const point = new Point({
    x,
    y,
    spatialReference: options.spatialReference
  });
  const markSymbol = new SimpleMarkerSymbol({
    // url: options.picUrl,
    // height: options.picSize || 15,
    // width: options.picSize || 15,
    color: options.color || '#00ffff',
    xoffset: options.xOffset || 0,
    yoffset: options.yOffset || 0
  });

  const markGrapic: __esri.Graphic = new Graphic({
    geometry: point,
    symbol: markSymbol,
    attributes: options.attributes
  });

  return markGrapic;
};

/**
 * 创建一个图标标注
 * @param x
 * @param y
 * @param options
 * @returns
 */
export const createPictureMarker = (
  x: number,
  y: number,
  options: {
    picUrl: string;
    picSize?: number | number[];
    // text?:string
    xOffset?: number;
    yOffset?: number;
    spatialReference?: any;
    attributes?: any;
  }
) => {
  //
  const point = new Point({
    x,
    y,
    spatialReference: options.spatialReference
  });
  const markSymbol = new PictureMarkerSymbol({
    url: options.picUrl,
    height:
      (options.picSize instanceof Array
        ? options.picSize[1]
        : options.picSize) || 15,
    width:
      (options.picSize instanceof Array
        ? options.picSize[0]
        : options.picSize) || 15,
    xoffset: options.xOffset || 0,
    yoffset: options.yOffset || 0
  });

  const markGrapic: __esri.Graphic = new Graphic({
    geometry: point,
    symbol: markSymbol,
    attributes: options.attributes
  });

  return markGrapic;
};
/**
 * 创建一个文本标注
 * @param x
 * @param y
 * @param options
 * @returns
 */
export const createTextMarker = (
  x: number,
  y: number,
  options: {
    text?: string;
    font?: Record<string, any>;
    xOffset?: number;
    yOffset?: number;
    spatialReference?: any;
    attributes?: any;
  }
) => {
  //
  const point = new Point({
    x,
    y,
    spatialReference: options.spatialReference
  });
  const textSymol = new TextSymbol({
    text: options.text,
    backgroundColor: [0, 0, 0, 0.1],
    xoffset: options.xOffset || 0,
    yoffset: options.yOffset || -15,
    color: '#fff',
    font: {
      size: 12,
      weight: 'bold',
      ...(options.font || {})
    }
  });

  const textGraphic = new Graphic({
    geometry: point,
    symbol: textSymol,
    attributes: options.attributes
  }) as __esri.Graphic;
  return textGraphic;
};

/**
 * 生成symbol
 * @param type 类型
 * @param options 可选项
 * @returns
 */
export const setSymbol = (
  type:
    | 'extent'
    | 'point'
    | 'polyline' | 'LineString'
    | 'polygon' | 'Polygon'
    | 'multipoint' | 'MultiPoint' | 'MultiLineString'
    | 'mesh'
    | 'picture'
    | 'text',
  options?: {
    size?: number | number[];
    style?: any;
    color?: string | number[] | __esri.Color;
    width?: number;
    outlineColor?: string | number[];
    outlineWidth?: number;
    outlineStyle?: any;
    text?: string;
    textColor?: string | number[] | __esri.Color;
    xOffset?: number;
    yOffset?: number;
    url?: string;
  }
) => {
  let lowerCaseType = type.toLowerCase();
  switch (lowerCaseType) {
    case 'polyline':
      return new SimpleLineSymbol({
        color:
          options?.color instanceof Color
            ? options.color
            : new Color(options?.color || '#00ffff'),
        style: options?.style || 'solid', // 线的样式 dash|dash-dot|solid等
        width: options?.width || 4
      });
    case 'multilinestring':
      return new SimpleLineSymbol({
        color:
          options?.color instanceof Color
            ? options.color
            : new Color(options?.color || '#00ffff'),
        style: options?.style || 'solid', // 线的样式 dash|dash-dot|solid等
        width: options?.width || 4
      });
    case 'polygon':
      return new SimpleFillSymbol({
        color:
          options?.color instanceof Color
            ? options.color
            : new Color(options?.color || [0, 0, 0, 0.1]),
        outline: {
          style: options?.outlineStyle || 'solid',
          color: new Color(options?.outlineColor || '#00ffff'),
          width: options?.outlineWidth || 2
        },
        style: options?.style || 'solid'
      });
    case 'point':
      return new SimpleMarkerSymbol({
        size:
          (options?.size instanceof Array ? options.size[0] : options?.size) ||
          12,
        color:
          options?.color instanceof Color
            ? options.color
            : new Color(options?.color || '#00ffff'),
        style: options?.style || 'circle', // 点样式solid\cross\square|diamond|circle|x
        outline: {
          color: new Color(options?.outlineColor || '#00ffff'),
          width: options?.outlineWidth || 0.2
        }
      });
    case 'text':
      return new TextSymbol({
        text: options?.text,
        backgroundColor:
          options?.color instanceof Color
            ? options.color
            : options?.color || [0, 0, 0, 0.3],
        xoffset: options?.xOffset || 0,
        yoffset: options?.yOffset === undefined ? -15 : options.yOffset,
        color: options?.textColor || '#fff',
        font: {
          size: 12,
          weight: 'bold'
        }
      });
    case 'picture':
      return new PictureMarkerSymbol({
        url: options?.url,
        height:
          (options?.size instanceof Array ? options.size[1] : options?.size) ||
          15,
        width:
          (options?.size instanceof Array ? options.size[0] : options?.size) ||
          15,
        xoffset: options?.xOffset || 0,
        yoffset: options?.yOffset || 0
      });
    default:
      return new SimpleMarkerSymbol({
        color: new Color(options?.color || '#00ffff'),
        style: 'diamond', // 点样式solid\cross\square|diamond|circle|x
        outline: {
          color: new Color(options?.outlineColor || '#00ffff'),
          width: options?.outlineWidth || 0.2
        }
      });
  }
};
/**
 * 生成矩形
 * @param vertices 包含两个对角坐标的数组
 * @param spatialReference 地图的坐标系
 * @returns
 */
export const createRect = (vertices?: number[][], spatialReference?: any) => {
  if (vertices?.length !== 2) return;
  const rect = new Polygon({
    rings: [
      [
        [vertices[0][0], vertices[0][1]],
        [vertices[1][0], vertices[0][1]],
        [vertices[1][0], vertices[1][1]],
        [vertices[0][0], vertices[1][1]]
      ]
    ],
    spatialReference
  }) as __esri.Polygon;
  return rect;
};
/**
 * 生成Graphic
 * @param options 配置项
 * @returns
 */
export const createGraphic = (options: {
  geometry?: __esri.Geometry;
  symbol?: __esri.Symbol;
  attributes?: any;
}) => {
  const graphic = new Graphic(options);
  return graphic;
};

/**
 * 高亮要素
 * @param feature
 */
export const highlightFeature = (feature: __esri.Graphic) => {
  const oldSymbol = feature.symbol;
  const oldcolor = feature.symbol.color;
  let index = 0;
  const timer = setInterval(() => {
    feature.symbol = setSymbol(feature.geometry.type, {
      color: index % 2 === 0 ? '#ff0000' : oldcolor
    });
    index++;
    if (index === 6) {
      clearInterval(timer);
      feature.symbol = oldSymbol;
    }
  }, 300);
};
/**
 * 定位并缩放到到要素并闪烁高亮
 * @param view
 * @param target 要素
 * @param options 更多选项
 * @returns
 */
export const gotoAndHighLight = async (
  view?: __esri.MapView,
  target?: __esri.Graphic,
  options?: {
    /** 仅当要素没有extent属性时生效，即点要素，默认24，值 1-24，值大，地图放大 */
    zoom?: number;
    duration?: number;
    avoidHighlight?: boolean;
    /** 缩放倍数， 默认2，表示在屏幕区域内，目标的四周的区域宽/高是目标的两倍， 值 为2 时，宽/高为屏幕的1/5 */
    ratio?: number;
  }
) => {
  if (!target || !view) return;
  try {
    const extent = target?.geometry.extent;
    const screenRatio = view.extent
      ? view.extent.width / view.extent.height
      : 16 / 9;
    if (extent) {
      let xmin: number = extent?.xmin || 0;
      let xmax: number = extent?.xmax || 0;
      let ymin: number = extent?.ymin || 0;
      let ymax: number = extent?.ymax || 0;
      let width = xmax - xmin;
      let height = ymax - ymin;
      if (width > height * screenRatio) {
        height = width / screenRatio;
      } else {
        width = height * screenRatio;
      }
      xmin -= width * (options?.ratio || 2);
      xmax += width * (options?.ratio || 2);
      ymin -= height * (options?.ratio || 2);
      ymax += height * (options?.ratio || 2);
      await view?.goTo(
        new Extent({
          spatialReference: extent.spatialReference,
          xmin,
          xmax,
          ymin,
          ymax
        }),
        { duration: 500 }
      );
    } else {
      await view?.goTo(
        { zoom: options?.zoom || 16, target: target?.geometry }
        // { duration: options?.duration || 500 }
      );
    }
  } catch (error) {
    console.log(error);
  }

  if (options?.avoidHighlight) return;
  highlightFeature(target);
};
/**
 * 获取要素的中心点
 */
export const getGeometryCenter = (
  geometry?: any
) => {
  if (!geometry) return;
  if (geometry.type === 'point') {
    return [geometry.x, geometry.y];
  }
  if (geometry.extent) {
    const result = [
      (geometry.extent.xmax + geometry.extent.xmin) / 2,
      (geometry.extent.ymax + geometry.extent.ymin) / 2
    ];
    // 当为多边形时，点要在线上
    if (geometry.type === 'polyline') {
      const point = new Point({
        x: result[0],
        y: result[1],
        spatialReference: geometry.spatialReference
      });
      const nearstPoint = getNeerestPoint(geometry, point);
      if (!nearstPoint) return;
      return [nearstPoint.x, nearstPoint.y];
    }
    return result;
  }
};
/**
 * 获取要素的中心点
 */
export const getGeometryCenterPoint = (
  geometry?: any
): __esri.Point | undefined => {
  if (!geometry) return;
  if (geometry.type === 'point') {
    return geometry;
  }
  if (geometry.extent) {
    const result = [
      (geometry.extent.xmax + geometry.extent.xmin) / 2,
      (geometry.extent.ymax + geometry.extent.ymin) / 2
    ];
    // 当为多边形时，点要在线上
    if (geometry.type === 'polyline') {
      const point = new Point({
        x: result[0],
        y: result[1],
        spatialReference: geometry.spatialReference
      });
      const nearstPoint = getNeerestPoint(geometry, point);
      return nearstPoint;
    }
  }
};
/**
 * 获取指定几何体上距离指定点最近的坐标
 * @param geometry 指定的几何体
 * @param point 指定点
 * @returns
 */
export const getNeerestPoint = (geometry?: __esri.Geometry, point?: any) => {
  if (!geometry || !point) return;
  return geometryEngine.nearestCoordinate(geometry, point).coordinate;
};
/**
 * 判断两个要素是否相交
 * @param geometry1
 * @param geometry2
 * @returns
 */
export const intersects = (
  geometry1: __esri.Geometry,
  geometry2: __esri.Geometry
) => {
  if (!geometry1 || !geometry2) return false;
  return geometryEngine.intersects(geometry1, geometry2);
};

export const createPolyline = (
  vertices?: any[],
  spatialReference?: any,
  symbol?: __esri.Symbol,
  attributes?: any
) => {
  if (vertices?.length && vertices.length < 2) return;
  const line = createGraphic({
    geometry: new Polyline({
      paths: vertices,
      spatialReference
    }),
    symbol: symbol || setSymbol('polyline'),
    attributes
  });
  return line;
};
export const createPoint = (
  point?: any[],
  spatialReference?: any,
  symbol?: __esri.Symbol
) => {
  if (!point?.length) return;
  return createGraphic({
    geometry: new Point({ x: point[0][0], y: point[0][1], spatialReference }),
    symbol: symbol || setSymbol('point')
  });
};
export const createPolygon = (
  vertices?: any[],
  spatialReference?: any,
  symbol?: __esri.Symbol,
  attributes?: any
) => {
  if (!vertices?.length || vertices.length < 3) return;
  return createGraphic({
    geometry: new Polygon({
      rings: vertices,
      spatialReference
    }),
    symbol: symbol || setSymbol('polygon'),
    attributes
  });
};
/**
 * 绘制椭圆
 * @param vertices
 * @param spatialReference
 * @param symbol
 * @param attributes
 * @returns
 */
export const createEllipse = (
  vertices?: any[],
  spatialReference?: any,
  symbol?: __esri.Symbol,
  attributes?: any
) => {
  if (vertices?.length !== 2) return;
  const ring: number[][] = [];
  const maxX = Math.max(vertices[0][0], vertices[1][0]);
  const maxY = Math.max(vertices[0][1], vertices[1][1]);
  const minX = Math.min(vertices[0][0], vertices[1][0]);
  const minY = Math.min(vertices[0][1], vertices[1][1]);
  const width = maxX - minX;
  const height = maxY - minY;
  const cX = maxX - width / 2;
  const cY = maxY - height / 2;
  for (let i = 0; i < 360; i++) {
    const t = (i * Math.PI) / 180;
    const x = cX - (width / 2) * Math.cos(t);
    const y = cY + (height / 2) * Math.sin(t);
    ring.push([x, y]);
  }
  ring.push(ring[0]);
  const graphic = createPolygon(
    ring,
    spatialReference,
    symbol ||
      setSymbol('polygon', {
        outlineWidth: 1
      }),
    attributes
  );
  return graphic;
};
/**
 * 绘制矩形
 * @param vertices
 * @param spatialReference
 * @param symbol
 * @param attributes
 * @returns
 */
export const createRectGraphic = (
  vertices?: any[],
  spatialReference?: any,
  symbol?: __esri.Symbol,
  attributes?: any
) => {
  const rect = createRect(vertices, spatialReference);
  return (
    rect &&
    createGraphic({
      geometry: rect,
      symbol:
        symbol ||
        (setSymbol('polygon', {
          outlineColor: '#ff0000',
          outlineWidth: 1
        }) as any),
      attributes
    })
  );
};
/**
 * 创建一个文本框
 * @param screenpoint
 * @param telport
 * @returns
 */
export const createTextDraw = (screenpoint?: any, telport?: string) => {
  telport = telport || 'body';
  const container = document.querySelector(telport);
  const radom = moment().valueOf().toFixed(0);
  const textarea = document.createElement('textarea');
  textarea.id = radom;
  textarea.cols = 27;
  textarea.rows = 3;
  textarea.style.position = 'absolute';
  textarea.style.top = (screenpoint?.y || -1000) + 'px';
  textarea.style.left = (screenpoint?.x || -1000) + 'px';
  textarea.style.width = '250px';
  textarea.style.height = '60px';
  textarea.style.backgroundColor = 'transparent';
  textarea.style.borderWidth = '3px';
  textarea.style.borderColor = 'red';
  textarea.style.borderStyle = 'solid';
  textarea.style.fontSize = '18px';
  textarea.style.color = '#fff';
  container?.append(
    textarea
    //   `<textarea id="${radom}"
    //  class="ui-widget-content textlabel" cols=27 rows=3
    //  style='position: absolute;top: "${screenpoint.y}px; left: ${screenpoint.x}px;
    //  z-index: 1;width:250px;
    //  height: 60px;background:transparent;
    //  border: solid;border-width: 3px;border-color: red;font-size:18px; color:#000;'></textarea >`
  );
  // const textarea = document.getElementById(radom)
  textarea?.focus();
  return textarea;
};
export const createGeometry = (
  type: 'point' | 'polyline' | 'polygon',
  vertices?: any[],
  spatialReference?: any
) => {
  if (!vertices) return;
  switch (type) {
    case 'point':
      return new Point({
        x: vertices[0][0],
        y: vertices[0][1],
        spatialReference
      });
    case 'polygon':
      return new Polygon({
        rings: vertices,
        spatialReference
      });
    case 'polyline':
      return new Polyline({
        paths: vertices,
        spatialReference
      });
    default:
      return new Point({
        x: vertices[0][0],
        y: vertices[0][1],
        spatialReference
      });
  }
};

/**
 * 计算面积
 * @param vertices
 * @param unit
 * @param spatialReference
 * @returns
 */
export const calcArea = (
  vertices: number[][],
  unit?: __esri.AreaUnits,
  spatialReference?: any
): number => {
  try {
    const geometry: any = createGeometry('polygon', vertices, spatialReference);
    if (!geometry) return 0;
    return (
      (geometryEngine as __esri.geometryEngine).planarArea(
        geometry,
        unit || 'square-meters'
      ) || 0
    );
  } catch (error) {
    return 0;
  }
};

/**
 * 计算长度
 * @param vertices
 * @param unit
 * @param spatialReference
 * @returns
 */
export const calcLength = (
  vertices: number[][],
  unit?:
    | 'meters'
    | 'feet'
    | 'kilometers'
    | 'miles'
    | 'nautical-miles'
    | 'yards'
    | number,
  spatialReference?: any
): number => {
  const geometry: any = createGeometry('polyline', vertices, spatialReference);
  if (!geometry) return 0;
  return geometryEngine.planarLength(geometry, unit || 'meters');
};
/**
 * 计算夹角角度
 * @param vertices 长度必须是三个，角度以第二个点为顶点计算
 * @param unit
 * @param spatialReference
 * @returns
 */
export const calcAngle = (vertices: number[][]): number => {
  // 只当有三个点时才能计算结果
  if (vertices?.length !== 3) return 0;
  // 起点到中间点的向量
  const point1: number[] = [];
  point1.push(vertices[0][0] - vertices[1][0]);
  point1.push(vertices[0][1] - vertices[1][1]);
  // 终点到中间点的向量
  const point2: number[] = [];
  point2.push(vertices[2][0] - vertices[1][0]);
  point2.push(vertices[2][1] - vertices[1][1]);
  let result: number;
  if (point1[0] * point2[1] === point1[1] * point2[0]) {
    result = 180;
  } else {
    // 两向量的点乘
    const pointMultiply: number = point1[0] * point2[0] + point1[1] * point2[1];
    // 求出夹角的弧度
    const angle = Math.acos(
      pointMultiply /
        Math.sqrt(point1[0] ** 2 + point1[1] ** 2) /
        Math.sqrt(point2[0] ** 2 + point2[1] ** 2)
    );
    // 转换为弧度
    result = (angle * 180) / Math.PI;
  }
  return result;
};

/**
 * 通过线的长度和与y轴的弧度来计算x、y方向的长度
 * @param length 长度
 * @param deg 弧度
 * @return {deltX:number,deltY: number}
 */
export const calcDeltaXY = (length: number, deg: number) => {
  const angle = (deg * Math.PI) / 180; // 转换成角度
  return {
    x: length * Math.sin(angle),
    y: length * Math.cos(angle)
  };
};
/**
 * 通过extent计算scale
 * @param extent
 * @returns
 */
export const getScaleOnExtent = (extent?: __esri.Extent) => {
  if (!extent) return;
  const dx = extent.width;
  const dy = extent.height;
  const length = Math.max(dx, dy);
  const radio = 1; // 180 / (Math.PI * 6378137);//实际距离换算到地图距离的参数 度/米
  // const scale = length / radio / 0.5
  const scale = length / radio;
  return scale;
};

/**
 * 判断两要素是否要同
 */
export const Equals = (
  geometry1: __esri.Geometry,
  geometry2: __esri.Geometry
) => {
  return geometryEngine.equals(geometry1, geometry2);
};

/**
 * 创建一个背景框
 * @param anchor
 * @param options
 * @returns
 */
export const createBgGraphic = (
  anchor: __esri.Point | undefined,
  options?: {
    fontSize?: number;
    yOffset?: number;
    radius?: number;
    borderColor?: any;
    borderWidth?: number;
    bgColor?: any;
    text?: string;
  },
  attributes?: any
) => {
  const fontSize = options?.fontSize || 10;
  const width = options?.text
    ? calcStringWidth(options?.text) * 0.5 * (fontSize + 3) + 4
    : 0; //
  const radius = options?.radius === undefined ? 6 : options?.radius || 0;
  const height = fontSize * 1.8;
  const size = Math.max(height, width);
  const borderG = new Graphic({
    geometry: anchor,
    symbol: new SimpleMarkerSymbol({
      color: options?.bgColor || '#ffff00',
      outline: {
        color: options?.borderColor || '#ff0000',
        width: options?.borderWidth === undefined ? 1 : options.borderWidth || 1
      },
      path:
        'M0' +
        ' ' +
        6 +
        'L0' +
        ' ' +
        (height - radius) +
        'Q0' +
        ' ' +
        height +
        ' ' +
        radius +
        ' ' +
        height +
        'L' +
        (width - radius) +
        ' ' +
        height +
        'Q' +
        width +
        ' ' +
        height +
        ' ' +
        width +
        ' ' +
        (height - radius) +
        'L' +
        width +
        ' ' +
        radius +
        'Q' +
        width +
        ' ' +
        '0' +
        ' ' +
        (width - radius) +
        ' ' +
        '0L' +
        radius +
        ' ' +
        '0Q0' +
        ' ' +
        '0' +
        ' ' +
        '0' +
        ' ' +
        radius,
      size,
      yoffset: options?.yOffset === undefined ? -15 : options.yOffset || 0
    }),
    attributes
  });
  return borderG;
};
