import{d as T,a8 as A,c as F,r as _,am as L,g as M,h as W,F as o,p as e,q as n,G as c,bh as i,aq as w,x as g,bz as x,bU as B,bW as S,J as H,L as O,C as U}from"./index-r0dFAfgr.js";import{f as Y}from"./DateFormatter-Bm9a68Ax.js";import{a as P}from"./circuitTaskFormRecord-CjbtiPXk.js";import{P as q}from"./config-C9CMv0E7.js";const z={class:"feedback-detail"},K={class:"task-info"},j={class:"stat-item"},G={class:"stat-value"},J={class:"stat-item"},Q={class:"stat-value"},X={class:"stat-item"},Z={class:"stat-value"},$={class:"stat-item"},tt={class:"stat-value"},et={class:"card-header"},st={class:"dialog-footer"},at=T({__name:"FeedbackDetailDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue","success"],setup(h,{emit:b}){const m=h,I=b,u=A({get:()=>m.modelValue,set:s=>I("update:modelValue",s)}),p=F(!1),a=_({totalItems:0,checkedItems:0,uncheckedItems:0,completionRate:0}),r=_({title:"",height:"400px",indexVisible:!0,columns:[{minWidth:120,label:"巡检地点",prop:"location"},{minWidth:120,label:"巡检位置",prop:"position"},{minWidth:200,label:"检验内容",prop:"content"},{minWidth:100,label:"检验结果",prop:"result",formatter:s=>({NOT_CHECKED:"未检查",NORMAL:"正常",ABNORMAL:"异常",NEED_REPAIR:"需维修"})[s.result]||s.result},{minWidth:150,label:"结果描述",prop:"resultDescription"},{minWidth:160,label:"检验时间",prop:"checkTime",formatter:s=>Y(s.checkTime,"YYYY-MM-DD HH:mm:ss")},{minWidth:120,label:"检验人员",prop:"checkUserId"}],dataList:[],loading:!1,pagination:{hide:!0}}),C=s=>{const t=q[s];return(t==null?void 0:t.text)||s},k=async()=>{var s;if((s=m.task)!=null&&s.code)try{p.value=!0,r.loading=!0;const t=await P(m.task.code);if(t!=null&&t.data){let l=t.data.data||t.data||[];l&&typeof l=="object"&&!Array.isArray(l)&&(l=l.data||l.records||l.content||[]);const d=Array.isArray(l)?l:[];r.dataList=d,y(d),console.log("表单记录数据:",d)}}catch(t){console.error("加载表单记录失败:",t),g.error("加载表单记录失败"),r.dataList=[]}finally{p.value=!1,r.loading=!1}},y=s=>{a.totalItems=s.length,a.checkedItems=s.filter(t=>t.result!=="NOT_CHECKED").length,a.uncheckedItems=a.totalItems-a.checkedItems,a.completionRate=a.totalItems>0?Math.round(a.checkedItems/a.totalItems*100):0},R=()=>{k()},D=()=>{g.info("导出报告功能开发中...")},v=()=>{u.value=!1};return L(u,s=>{var t;s&&((t=m.task)!=null&&t.code)?(r.dataList=[],a.totalItems=0,a.checkedItems=0,a.uncheckedItems=0,a.completionRate=0,k()):s||(r.dataList=[],a.totalItems=0,a.checkedItems=0,a.uncheckedItems=0,a.completionRate=0)}),(s,t)=>{const l=x,d=B,E=S,f=H,V=O;return M(),W(V,{modelValue:u.value,"onUpdate:modelValue":t[0]||(t[0]=N=>u.value=N),title:"巡检反馈详情",width:"80%","close-on-click-modal":!1,onClose:v},{footer:o(()=>[e("div",st,[n(f,{onClick:v},{default:o(()=>t[13]||(t[13]=[c("关闭")])),_:1}),n(f,{type:"primary",onClick:D},{default:o(()=>t[14]||(t[14]=[c("导出报告")])),_:1})])]),default:o(()=>[e("div",z,[n(l,{class:"task-info-card",shadow:"never"},{header:o(()=>t[1]||(t[1]=[e("div",{class:"card-header"},[e("span",null,"任务基本信息")],-1)])),default:o(()=>[e("div",K,[e("p",null,[t[2]||(t[2]=e("strong",null,"任务编号:",-1)),c(" "+i(s.task.code),1)]),e("p",null,[t[3]||(t[3]=e("strong",null,"任务名称:",-1)),c(" "+i(s.task.name),1)]),e("p",null,[t[4]||(t[4]=e("strong",null,"执行人员:",-1)),c(" "+i(s.task.receiveUserName),1)]),e("p",null,[t[5]||(t[5]=e("strong",null,"任务状态:",-1)),c(" "+i(C(s.task.status)),1)])])]),_:1}),n(l,{class:"feedback-stats-card",shadow:"never"},{header:o(()=>t[6]||(t[6]=[e("div",{class:"card-header"},[e("span",null,"表单反馈统计")],-1)])),default:o(()=>[n(E,{gutter:20},{default:o(()=>[n(d,{span:6},{default:o(()=>[e("div",j,[e("div",G,i(a.totalItems),1),t[7]||(t[7]=e("div",{class:"stat-label"},"总检查项",-1))])]),_:1}),n(d,{span:6},{default:o(()=>[e("div",J,[e("div",Q,i(a.checkedItems),1),t[8]||(t[8]=e("div",{class:"stat-label"},"已检查",-1))])]),_:1}),n(d,{span:6},{default:o(()=>[e("div",X,[e("div",Z,i(a.uncheckedItems),1),t[9]||(t[9]=e("div",{class:"stat-label"},"未检查",-1))])]),_:1}),n(d,{span:6},{default:o(()=>[e("div",$,[e("div",tt,i(a.completionRate)+"%",1),t[10]||(t[10]=e("div",{class:"stat-label"},"完成率",-1))])]),_:1})]),_:1})]),_:1}),n(l,{class:"form-records-card",shadow:"never"},{header:o(()=>[e("div",et,[t[12]||(t[12]=e("span",null,"巡检表单记录",-1)),n(f,{type:"primary",size:"small",onClick:R,loading:p.value},{default:o(()=>t[11]||(t[11]=[c(" 刷新 ")])),_:1},8,["loading"])])]),default:o(()=>[n(w,{config:r},null,8,["config"])]),_:1})])]),_:1},8,["modelValue"])}}}),it=U(at,[["__scopeId","data-v-0b49f99b"]]);export{it as default};
