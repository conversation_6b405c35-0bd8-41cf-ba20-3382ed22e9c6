package org.thingsboard.server.dao.sql.deviceType;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.purchase.Contract;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractPageRequest;

import java.util.List;

@Mapper
public interface ContractMapper extends BaseMapper<Contract> {
    IPage<Contract> findByPage(ContractPageRequest request);

    boolean update(Contract entity);

    List<String> getChildrenId(String id);
}
