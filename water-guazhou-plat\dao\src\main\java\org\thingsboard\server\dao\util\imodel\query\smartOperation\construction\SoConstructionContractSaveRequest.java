package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContract;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class SoConstructionContractSaveRequest extends SaveRequest<SoConstructionContract> {
    // 编号
    @NotNullOrEmpty
    private String code;

    // 合同名称
    @NotNullOrEmpty
    private String name;

    // 合同类型
    private String type;

    // 所属工程编号
    @NotNullOrEmpty
    private String constructionCode;

    // 甲方单位
    @NotNullOrEmpty
    private String firstpartOrganization;

    // 甲方代表
    @NotNullOrEmpty
    private String firstpartRepresentative;

    // 甲方代表联系电话
    private String firstpartPhone;

    // 乙方单位
    @NotNullOrEmpty
    private String secondpartOrganization;

    // 乙方联系人
    private String secondpartRepresentative;

    // 乙方联系电话
    private String secondpartPhone;

    // 合同金额
    @NotNullOrEmpty
    private BigDecimal cost;

    // 合同工期（开始时间）
    private Date workTimeBegin;

    // 合同工期（完成时间）
    private Date workTimeEnd;

    // 签署时间
    private Date signTime;

    // 详细说明
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    protected SoConstructionContract build() {
        SoConstructionContract entity = new SoConstructionContract();
        entity.setConstructionCode(constructionCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionContract update(String id) {
        SoConstructionContract entity = new SoConstructionContract();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionContract entity) {
        entity.setCode(code);
        entity.setName(name);
        entity.setType(type);
        entity.setFirstpartOrganization(firstpartOrganization);
        entity.setFirstpartRepresentative(firstpartRepresentative);
        entity.setFirstpartPhone(firstpartPhone);
        entity.setSecondpartOrganization(secondpartOrganization);
        entity.setSecondpartRepresentative(secondpartRepresentative);
        entity.setSecondpartPhone(secondpartPhone);
        entity.setCost(cost);
        entity.setWorkTimeBegin(workTimeBegin);
        entity.setWorkTimeEnd(workTimeEnd);
        entity.setSignTime(signTime);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
        entity.setUpdateTime(createTime());
        entity.setUpdateUser(currentUserUUID());
    }
}