<template>
  <Form
    ref="refForm"
    :config="FormConfig"
  />
</template>
<script lang="ts" setup>
import { IFormIns } from '@/components/type'

defineProps<{
  view?: __esri.MapView
}>()
const refForm = ref<IFormIns>()
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        type: 'simple',
        desc: '分屏数量'
      },
      fields: [
        {
          type: 'radio',
          label: '',
          field: 'count',
          options: [
            { label: '1屏', value: 1 },
            { label: '2屏', value: 2 },
            { label: '4屏', value: 4 },
            { label: '9屏', value: 9 }
          ],
          onChange: (params: any) => screenCountChange(params)
        }
      ]
    },
    {
      fieldset: {
        desc: '模式选择',
        type: 'simple'
      },
      fields: [
        {
          type: 'radio',
          label: '',
          field: 'type',
          options: [
            { label: '中心同步', value: 'center-sync' },
            { label: '缩放同步', value: 'zoom-sync' }
          ]
        }
      ]
    }
  ]
})
const screenCountChange = (params: any) => {
  console.log(params)
}
</script>
<style scoped lang="scss">
.workspace {
  width: 400px;
  height: 680px;
  max-height: calc(100% - 115px);
  position: absolute;
  left: calc(100% - 455px);
  top: 100px;
  overflow: hidden;
}
</style>
