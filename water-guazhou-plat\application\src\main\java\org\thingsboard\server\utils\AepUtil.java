package org.thingsboard.server.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.DeviceId;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * AEP 工具类
 */
@Component
public class AepUtil {

    @Value("${water.aep.loginUrl}")
    private String loginUrl;
    @Value("${water.aep.registerUrl}")
    private String registerUrl;
    @Value("${water.aep.appId}")
    private String appId;
    @Value("${water.aep.secret}")
    private String secret;

    public JSONObject register(String nodeId, JSONObject deviceInfo, String appKey, String token) {
        try {
            CloseableHttpClient httpClient = SSLHttpClients.createSSLClientDefault();
            HttpPost httpPost = new HttpPost(registerUrl + appKey);
            httpPost.setHeader("app_key", appKey);
            httpPost.setHeader("Authorization", token);

            JSONObject params = new JSONObject();
            params.put("nodeId", nodeId);
            params.put("deviceInfo", deviceInfo);
            StringEntity paramEntity = new StringEntity(params.toString(), StandardCharsets.UTF_8);
            httpPost.setEntity(paramEntity);

            CloseableHttpResponse execute = httpClient.execute(httpPost);
            String s = EntityUtils.toString(execute.getEntity());
            int statusCode = execute.getStatusLine().getStatusCode();
            JSONObject result = JSON.parseObject(s);
            if (statusCode == 200) {
                result.put("result", true);
            } else {
                result.put("result", false);
            }

            return result;
        } catch (Exception e) {
            JSONObject result = new JSONObject();
            result.put("result", false);
            result.put("error_code", 500);
            result.put("error_desc", "调用AEP平台注册设备失败!");

            return result;
        }
    }

    public JSONObject register(String nodeId, JSONObject deviceInfo, String token) {
        try {
            CloseableHttpClient httpClient = SSLHttpClients.createSSLClientDefault();
            HttpPost httpPost = new HttpPost(registerUrl + appId);
            httpPost.setHeader("app_key", appId);
            httpPost.setHeader("Authorization", token);
            httpPost.setHeader("Content-Type", "application/json");

            JSONObject params = new JSONObject();
            params.put("nodeId", nodeId);
            params.put("productId", "5b5184fc958d7941c8250e04");
            params.put("deviceInfo", deviceInfo);
            StringEntity paramEntity = new StringEntity(params.toString(), StandardCharsets.UTF_8);
            httpPost.setEntity(paramEntity);

            CloseableHttpResponse execute = httpClient.execute(httpPost);
            String s = EntityUtils.toString(execute.getEntity());
            int statusCode = execute.getStatusLine().getStatusCode();
            JSONObject result = JSON.parseObject(s);
            if (statusCode == 200) {
                result.put("result", true);
            } else {
                result.put("result", false);
                errorMessageProcess(result);
            }

            return result;
        } catch (Exception e) {
            JSONObject result = new JSONObject();
            result.put("result", false);
            result.put("error_code", 500);
            result.put("error_desc", "调用AEP平台注册设备失败!");

            return result;
        }
    }

    private void errorMessageProcess(JSONObject result) {
        String error_code = result.getString("error_code");
        if (StringUtils.isNotBlank(error_code)) {
            String error_desc = result.getString("error_desc");
            switch (error_code) {
                case "100416":
                    // 设备已经注册, 直接返回成功
                    result.put("result", true);
                    break;
                case "100426":
                    // 设备已被其他厂商注册, 请联系关联员
                    error_desc = "设备已被其他厂商注册, 请联系关联员";
                    break;
            }
            result.put("error_desc", error_desc);
        }
    }

    public JSONObject login(String appId, String secret) {
        try {
            CloseableHttpClient httpClient = SSLHttpClients.createSSLClientDefault();

            HttpPost httpPost = new HttpPost(loginUrl);

            List<NameValuePair> params = new ArrayList<>();
            NameValuePair nameValuePair1 = new BasicNameValuePair("appId", appId);
            NameValuePair nameValuePair2 = new BasicNameValuePair("secret", secret);
            params.add(nameValuePair1);
            params.add(nameValuePair2);

            UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(params, "utf8");

            httpPost.setEntity(formEntity);

            CloseableHttpResponse execute = httpClient.execute(httpPost);
            String s = EntityUtils.toString(execute.getEntity());
            int statusCode = execute.getStatusLine().getStatusCode();
            JSONObject result = JSON.parseObject(s);
            if (statusCode == 200) {
                result.put("result", true);
            } else {
                result.put("result", false);
            }

            return result;
        } catch (Exception e) {
            JSONObject result = new JSONObject();
            result.put("result", false);
            result.put("error_code", 500);
            result.put("error_desc", "调用AEP平台鉴权接口失败!");
            return result;
        }
    }

    public JSONObject login() {
        try {
            CloseableHttpClient httpClient = SSLHttpClients.createSSLClientDefault();

            HttpPost httpPost = new HttpPost(loginUrl);

            List<NameValuePair> params = new ArrayList<>();
            NameValuePair nameValuePair1 = new BasicNameValuePair("appId", appId);
            NameValuePair nameValuePair2 = new BasicNameValuePair("secret", secret);
            params.add(nameValuePair1);
            params.add(nameValuePair2);

            UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(params, "utf8");

            httpPost.setEntity(formEntity);

            CloseableHttpResponse execute = httpClient.execute(httpPost);
            String s = EntityUtils.toString(execute.getEntity());
            int statusCode = execute.getStatusLine().getStatusCode();
            JSONObject result = JSON.parseObject(s);
            if (statusCode == 200) {
                result.put("result", true);
            } else {
                result.put("result", false);
            }

            return result;
        } catch (Exception e) {
            JSONObject result = new JSONObject();
            result.put("result", false);
            result.put("error_code", 500);
            result.put("error_desc", "调用AEP平台鉴权接口失败!");
            return result;
        }
    }

    public static void main(String[] args) {
        String ids = "1ef5494dec30330b1efff093c83ce98,1ef5494df80fc00b1efff093c83ce98,1ef5494e03753b0b1efff093c83ce98,1ef5494e0f0dfb0b1efff093c83ce98,1ef5494e1acb5a0b1efff093c83ce98,1ef5494e26445d0b1efff093c83ce98,1ef5494e31e4700b1efff093c83ce98,1ef5494e3d86f40b1efff093c83ce98,1ef5494e4915f00b1efff093c83ce98,1ef5494e9efbaf0b1efff093c83ce98,1ef5494eaa59d70b1efff093c83ce98,1ef5494eb5edb50b1efff093c83ce98,1ef5494ec1755e0b1efff093c83ce98,1ef5494eccbffe0b1efff093c83ce98,1ef5494ed842c50b1efff093c83ce98,1ef5494ee3a8400b1efff093c83ce98,1ef5494eef325a0b1efff093c83ce98,1ef5494efaa1990b1efff093c83ce98,1ef5494f0613490b1efff093c83ce98,1ef5494f11876a0b1efff093c83ce98,1ef5494f1d054f0b1efff093c83ce98,1ef5494f2880c30b1efff093c83ce98,1ef5494f33fea80b1efff093c83ce98,1ef5494f3f6b760b1efff093c83ce98,1ef5494f4b01c50b1efff093c83ce98,1ef5494f566c220b1efff093c83ce98,1ef5494f61d67f0b1efff093c83ce98,1ef5494f6d45be0b1efff093c83ce98,1ef5494f78b28c0b1efff093c83ce98,1ef5494f840e430b1efff093c83ce98,1ef5494f8f6c6b0b1efff093c83ce98,1ef5494f9ad9390b1efff093c83ce98,1ef5494fa654ad0b1efff093c83ce98,1ef5494fb487ee0b1efff093c83ce98,1ef5494fc07b030b1efff093c83ce98,1ef5494fcc310f0b1efff093c83ce98,1ef5494fd7a7a10b1efff093c83ce98,1ef5494fe327f70b1efff093c83ce98,1ef5494fee8fe30b1efff093c83ce98,1ef5494ffa26320b1efff093c83ce98,1ef54950059f350b1efff093c83ce98,1ef54950115f050b1efff093c83ce98,1ef549501d9b580b1efff093c83ce98,1ef549502927e30b1efff093c83ce98,1ef5495034be320b1efff093c83ce98,1ef549504023ad0b1efff093c83ce98,1ef549504c2a4a0b1efff093c83ce98,1ef549505797180b1efff093c83ce98,1ef549506321320b1efff093c83ce98,1ef549506e9ca60b1efff093c83ce98,1ef549507a4dd00b1efff093c83ce98,1ef5495085fc890b1efff093c83ce98,1ef549509184320b1efff093c83ce98,1ef549509d307a0b1efff093c83ce98,1ef54950a8dcc20b1efff093c83ce98,1ef54950b46bbe0b1efff093c83ce98,1ef54950c04dbc0b1efff093c83ce98,1ef54950cbe40b0b1efff093c83ce98,1ef54950d75f7f0b1efff093c83ce98,1ef54950e2bda70b1efff093c83ce98,1ef54950ee25930b1efff093c83ce98,1ef54950f99e960b1efff093c83ce98,1ef54951050b640b1efff093c83ce98,1ef5495110cda50b1efff093c83ce98,1ef549511c5a300b1efff093c83ce98,1ef5495127eb9d0b1efff093c83ce98,1ef5495133586b0b1efff093c83ce98,1ef549513ebb750b1efff093c83ce98,1ef549514a2d250b1efff093c83ce98,1ef5495155d48b0b1efff093c83ce98,1ef549516161160b1efff093c83ce98,1ef549516ce8bf0b1efff093c83ce98,1ef54951784e3a0b1efff093c83ce98,1ef5495183ce900b1efff093c83ce98,1ef549518f2f290b1efff093c83ce98,1ef549519ab6d20b1efff093c83ce98,1ef54951a62af30b1efff093c83ce98,1ef54951b1a6670b1efff093c83ce98,1ef54951bd15a60b1efff093c83ce98,1ef54951c87b210b1efff093c83ce98,1ef54951d3f6950b1efff093c83ce98,1ef54951df6d270b1efff093c83ce98,1ef54951eb121c0b1efff093c83ce98,1ef54951f68b1f0b1efff093c83ce98,1ef54952021c8c0b1efff093c83ce98,1ef549520d98000b1efff093c83ce98,1ef5495219555f0b1efff093c83ce98,1ef5495224d5b50b1efff093c83ce98,1ef5495230560b0b1efff093c83ce98,1ef549523be0250b1efff093c83ce98,1ef54952478c6d0b1efff093c83ce98,1ef549525358720b1efff093c83ce98,1ef549525ee76e0b1efff093c83ce98,1ef549526aa25c0b1efff093c83ce98,1ef549527622b20b1efff093c83ce98,1ef5495281cc890b1efff093c83ce98,1ef549528d458c0b1efff093c83ce98,1ef5495298b9ad0b1efff093c83ce98,1ef54952a5182e0b1efff093c83ce98,1ef54952b0b5d00b1efff093c83ce98,1ef54952bc51010b1efff093c83ce98,1ef54952c84b690b1efff093c83ce98,1ef54952d3d3120b1efff093c83ce98,1ef54952df7f5a0b1efff093c83ce98,1ef54952eb10c70b1efff093c83ce98,1ef54952f6a9870b1efff093c83ce98,1ef549530255cf0b1efff093c83ce98,1ef549530de4cb0b1efff093c83ce98,1ef54953196c740b1efff093c83ce98,1ef549532500520b1efff093c83ce98,1ef549533091bf0b1efff093c83ce98,1ef549533c0d330b1efff093c83ce98,1ef549534794dc0b1efff093c83ce98,1ef549535312c10b1efff093c83ce98,1ef549535e9a6a0b1efff093c83ce98,1ef549536a10fc0b1efff093c83ce98,1ef549537571950b1efff093c83ce98,1ef5495380fbaf0b1efff093c83ce98,1ef549538c6fd00b1efff093c83ce98,1ef5495398284d0b1efff093c83ce98,1ef54953a3b4d80b1efff093c83ce98,1ef54953af80dd0b1efff093c83ce98,1ef54953baf9e00b1efff093c83ce98,1ef54953c670720b1efff093c83ce98,1ef54953d1e4930b1efff093c83ce98,1ef54953dd64e90b1efff093c83ce98,1ef54953e938410b1efff093c83ce98,1ef54953f4c25b0b1efff093c83ce98,1ef54954003b5e0b1efff093c83ce98,1ef549540bd9000b1efff093c83ce98,1ef549541767fc0b1efff093c83ce98,1ef549542311d30b1efff093c83ce98,1ef549542eaf750b1efff093c83ce98,1ef549543a323c0b1efff093c83ce98,1ef5495445bc560b1efff093c83ce98,1ef549545148e10b1efff093c83ce98,1ef549545cb0cd0b1efff093c83ce98,1ef5495468200c0b1efff093c83ce98,1ef54954739df10b1efff093c83ce98,1ef549547f16f40b1efff093c83ce98,1ef549548a86330b1efff093c83ce98,1ef5495496300a0b1efff093c83ce98,1ef54954a1c3e80b1efff093c83ce98,1ef54954ad49200b1efff093c83ce98,1ef54954b8d0c90b1efff093c83ce98,1ef54954c462360b1efff093c83ce98,1ef54954d063f10b1efff093c83ce98,1ef54954dcb63d0b1efff093c83ce98,1ef54954e858c10b1efff093c83ce98,1ef54954f3d4350b1efff093c83ce98,1ef54954ff4d380b1efff093c83ce98,1ef549550af22d0b1efff093c83ce98,1ef549551694b10b1efff093c83ce98,1ef54955226a7a0b1efff093c83ce98,1ef549552e38f00b1efff093c83ce98,1ef5495539d9030b1efff093c83ce98,1ef5495545631d0b1efff093c83ce98,1ef5495550ed370b1efff093c83ce98,1ef549555c467d0b1efff093c83ce98,1ef5495567c9440b1efff093c83ce98,1ef549557347290b1efff093c83ce98,1ef549557ed6250b1efff093c83ce98,1ef549558a3ba00b1efff093c83ce98,1ef5495595a5fd0b1efff093c83ce98,1ef54955a1376a0b1efff093c83ce98,1ef54955acab8b0b1efff093c83ce98,1ef54955b82e520b1efff093c83ce98,1ef54955c3c4a10b1efff093c83ce98,1ef54955cf3b330b1efff093c83ce98,1ef54955dabb890b1efff093c83ce98,1ef54955e636fd0b1efff093c83ce98,1ef54955f1c3880b1efff093c83ce98,1ef54955fd416d0b1efff093c83ce98,1ef54956090b010b1efff093c83ce98,1ef5495614f6c30b1efff093c83ce98,1ef549562163ea0b1efff093c83ce98,1ef549562d08df0b1efff093c83ce98,1ef54956386be90b1efff093c83ce98,1ef5495643f8740b1efff093c83ce98,1ef549564f78ca0b1efff093c83ce98,1ef549565b118a0b1efff093c83ce98,1ef549566680c90b1efff093c83ce98,1ef549567208720b1efff093c83ce98,1ef549567d7f040b1efff093c83ce98,1ef54956891a350b1efff093c83ce98,1ef5495694981a0b1efff093c83ce98,1ef54956a035bc0b1efff093c83ce98,1ef54956abb1300b1efff093c83ce98,1ef54956bb10bc0b1efff093c83ce98,1ef54956c68ea10b1efff093c83ce98,1ef54956d213d90b1efff093c83ce98,1ef54956e127b60b1efff093c83ce98,1ef54956ecc2e70b1efff093c83ce98,1ef54956f832260b1efff093c83ce98,1ef5495703b75e0b1efff093c83ce98,1ef549570f66170b1efff093c83ce98,1ef549571bee190b1efff093c83ce98,1ef549572778330b1efff093c83ce98,1ef54957330e820b1efff093c83ce98,1ef549573edcf80b1efff093c83ce98,1ef549574a7d0b0b1efff093c83ce98,1ef5495755ffd20b1efff093c83ce98,1ef5495761d32a0b1efff093c83ce98,1ef549576d9a4d0b1efff093c83ce98,1ef549577918320b1efff093c83ce98,1ef5495784a4bd0b1efff093c83ce98,1ef549579047410b1efff093c83ce98,1ef549579bc5260b1efff093c83ce98,1ef54957a731f40b1efff093c83ce98,1ef54957b2afd90b1efff093c83ce98,1ef54957be43b70b1efff093c83ce98,1ef54957c9dee80b1efff093c83ce98,1ef54957d549450b1efff093c83ce98,1ef54957e0c99b0b1efff093c83ce98,1ef54957ec49f10b1efff093c83ce98,1ef54957f7c5650b1efff093c83ce98,1ef549580359430b1efff093c83ce98,1ef549580ede7b0b1efff093c83ce98,1ef549581a7e8e0b1efff093c83ce98,1ef54958263bed0b1efff093c83ce98,1ef5495832699a0b1efff093c83ce98,1ef549583df3b40b1efff093c83ce98,1ef54958498c740b1efff093c83ce98,1ef549585542800b1efff093c83ce98,1ef5495860c0650b1efff093c83ce98,1ef549586c36f70b1efff093c83ce98,1ef5495877c3820b1efff093c83ce98,1ef549588321aa0b1efff093c83ce98,1ef549588e935a0b1efff093c83ce98,1ef549589a09ec0b1efff093c83ce98,1ef54958a606c50b1efff093c83ce98,1ef54958b2012d0b1efff093c83ce98,1ef54958bd72dd0b1efff093c83ce98,1ef54958c90e0e0b1efff093c83ce98,1ef54958d48bf30b1efff093c83ce98,1ef54958e01aef0b1efff093c83ce98,1ef54958eb96630b1efff093c83ce98,1ef54958f71b9b0b1efff093c83ce98,1ef54959028fbc0b1efff093c83ce98,1ef549590e17650b1efff093c83ce98,1ef5495919b5070b1efff093c83ce98,1ef54959254dc70b1efff093c83ce98,1ef5495930c1e80b1efff093c83ce98,1ef549593c4e730b1efff093c83ce98,1ef5495947dd6f0b1efff093c83ce98,1ef5495953d2f50b1efff093c83ce98,1ef549595f4e690b1efff093c83ce98,1ef549596acc4e0b1efff093c83ce98,1ef54959764a330b1efff093c83ce98,1ef5495981be540b1efff093c83ce98,1ef549598d6a9c0b1efff093c83ce98,1ef5495999083e0b1efff093c83ce98,1ef54959a4ccf00b1efff093c83ce98,1ef54959b060ce0b1efff093c83ce98,1ef54959bbe8770b1efff093c83ce98,1ef54959c799a10b1efff093c83ce98,1ef54959d334d20b1efff093c83ce98,1ef54959debc7b0b1efff093c83ce98,1ef54959ea357e0b1efff093c83ce98,1ef54959f5d3200b1efff093c83ce98,1ef5495a01a6780b1efff093c83ce98,1ef5495a0d21ec0b1efff093c83ce98,1ef5495a18a2420b1efff093c83ce98,1ef5495a2418d40b1efff093c83ce98,1ef5495a2fa7d00b1efff093c83ce98,1ef5495a3b20d30b1efff093c83ce98,1ef5495a46a87c0b1efff093c83ce98,1ef5495a5257350b1efff093c83ce98,1ef5495a5de14f0b1efff093c83ce98,1ef5495a69752d0b1efff093c83ce98,1ef5495a74f0a10b1efff093c83ce98,1ef5495a807abb0b1efff093c83ce98,1ef5495a8bfd820b1efff093c83ce98,1ef5495a97c7160b1efff093c83ce98,1ef5495aa358830b1efff093c83ce98,1ef5495aaedb4a0b1efff093c83ce98,1ef5495aba592f0b1efff093c83ce98,1ef5495ac620520b1efff093c83ce98,1ef5495ad1a3190b1efff093c83ce98,1ef5495add2d330b1efff093c83ce98,1ef5495ae89c720b1efff093c83ce98,1ef5495af413040b1efff093c83ce98,1ef5495aff95cb0b1efff093c83ce98,1ef5495b0b30fc0b1efff093c83ce98,1ef5495b169b590b1efff093c83ce98,1ef5495b2245300b1efff093c83ce98,1ef5495b2e1fdb0b1efff093c83ce98,1ef5495b3a1cb40b1efff093c83ce98,1ef5495b45def50b1efff093c83ce98,1ef5495b51690f0b1efff093c83ce98,1ef5495b5ce9650b1efff093c83ce98,1ef5495b68710e0b1efff093c83ce98,1ef5495b74000a0b1efff093c83ce98,1ef5495b7fa28e0b1efff093c83ce98,1ef5495b8b3b4e0b1efff093c83ce98,1ef5495b96fb1e0b1efff093c83ce98,1ef5495ba28a1a0b1efff093c83ce98,1ef5495bae22da0b1efff093c83ce98,1ef5495bba18600b1efff093c83ce98,1ef5495bc58a100b1efff093c83ce98,1ef5495bd0e8380b1efff093c83ce98,1ef5495bdc661d0b1efff093c83ce98,1ef5495be7e4020b1efff093c83ce98,1ef5495bf384150b1efff093c83ce98,1ef5495bff01fa0b1efff093c83ce98,1ef5495c0a82500b1efff093c83ce98,1ef5495c15f4000b1efff093c83ce98,1ef5495c2168210b1efff093c83ce98,1ef5495c2ce1240b1efff093c83ce98,1ef5495c3868cd0b1efff093c83ce98,1ef5495c43d80c0b1efff093c83ce98,1ef5495c4f86c50b1efff093c83ce98,1ef5495c5add9a0b1efff093c83ce98,1ef5495c665df00b1efff093c83ce98,1ef5495c71ecec0b1efff093c83ce98,1ef5495c7d5c2b0b1efff093c83ce98,1ef5495c88cb6a0b1efff093c83ce98,1ef5495c9453130b1efff093c83ce98,1ef5495c9fcc160b1efff093c83ce98,1ef5495cab56300b1efff093c83ce98,1ef5495cb6c56f0b1efff093c83ce98,1ef5495cc243540b1efff093c83ce98,1ef5495ccdcfdf0b1efff093c83ce98,1ef5495cd946710b1efff093c83ce98,1ef5495ce53bf70b1efff093c83ce98,1ef5495cf0c3a00b1efff093c83ce98,1ef5495cfc88520b1efff093c83ce98,1ef5495d080b190b1efff093c83ce98,1ef5495d13841c0b1efff093c83ce98,1ef5495d1eff900b1efff093c83ce98,1ef5495d2a73b10b1efff093c83ce98,1ef5495d35ecb40b1efff093c83ce98,1ef5495d41745d0b1efff093c83ce98,1ef5495d4cd0140b1efff093c83ce98,1ef5495d58554c0b1efff093c83ce98,1ef5495d63f55f0b1efff093c83ce98,1ef5495d6f90900b1efff093c83ce98,1ef5495d7b30a30b1efff093c83ce98,1ef5495d86ee020b1efff093c83ce98,1ef5495d929cbb0b1efff093c83ce98,1ef5495d9e15be0b1efff093c83ce98,1ef5495da99fd80b1efff093c83ce98,1ef5495db551020b1efff093c83ce98,1ef5495dc1293c0b1efff093c83ce98,1ef5495dcd399d0b1efff093c83ce98,1ef5495dd8d9b00b1efff093c83ce98,1ef5495de472700b1efff093c83ce98,1ef5495df00b300b1efff093c83ce98,1ef5495dfbc88f0b1efff093c83ce98,1ef5495e0737ce0b1efff093c83ce98,1ef5495e12d5700b1efff093c83ce98,1ef5495e1e70a10b1efff093c83ce98,1ef5495e29f84a0b1efff093c83ce98,1ef5495e35910a0b1efff093c83ce98,1ef5495e4124e80b1efff093c83ce98,1ef5495e4cc0190b1efff093c83ce98,1ef5495e5858d90b1efff093c83ce98,1ef5495e6416380b1efff093c83ce98,1ef5495e6f9b700b1efff093c83ce98,1ef5495e7b20a80b1efff093c83ce98,1ef5495e86b4860b1efff093c83ce98,1ef5495e921ee30b1efff093c83ce98,1ef5495e9dc3d80b1efff093c83ce98,1ef5495ea937f90b1efff093c83ce98,1ef5495eb4bac00b1efff093c83ce98,1ef5495ec062260b1efff093c83ce98,1ef5495ecc10df0b1efff093c83ce98,1ef5495ed796170b1efff093c83ce98,1ef5495ee30ca90b1efff093c83ce98,1ef5495eeea0870b1efff093c83ce98,1ef5495efa6efd0b1efff093c83ce98,1ef5495f05f1c40b1efff093c83ce98,1ef5495f11acb20b1efff093c83ce98,1ef5495f1d652f0b1efff093c83ce98,1ef5495f28d6df0b1efff093c83ce98,1ef5495f343c5a0b1efff093c83ce98,1ef5495f3fa9280b1efff093c83ce98,1ef5495f4b222b0b1efff093c83ce98,1ef5495f5682c40b1efff093c83ce98,1ef5495f61ed210b1efff093c83ce98,1ef5495f6d5c600b1efff093c83ce98,1ef5495f78cb9f0b1efff093c83ce98,1ef5495f84b7610b1efff093c83ce98,1ef5495f9010a70b1efff093c83ce98,1ef5495f9b95df0b1efff093c83ce98,1ef5495fa8168e0b1efff093c83ce98,1ef5495fb3c2d60b1efff093c83ce98,1ef5495fbf40bb0b1efff093c83ce98,1ef5495fcabc2f0b1efff093c83ce98,1ef5495fd63c850b1efff093c83ce98,1ef5495fe1a4710b1efff093c83ce98,1ef5495fed1d740b1efff093c83ce98,1ef5495ff887d10b1efff093c83ce98,1ef54960042cc60b1efff093c83ce98,1ef549600fbe330b1efff093c83ce98,1ef549601b2d720b1efff093c83ce98,1ef5496026a1930b1efff093c83ce98,1ef549603281200b1efff093c83ce98,1ef549603deb7d0b1efff093c83ce98,1ef5496049e3740b1efff093c83ce98,1ef5496055db6b0b1efff093c83ce98,1ef549606140e60b1efff093c83ce98,1ef549606ca6610b1efff093c83ce98,1ef54960780e4d0b1efff093c83ce98,1ef549608384df0b1efff093c83ce98,1ef549608efde20b1efff093c83ce98,1ef549609a74740b1efff093c83ce98,1ef54960a5dc600b1efff093c83ce98,1ef54960b1926c0b1efff093c83ce98,1ef54960bd746a0b1efff093c83ce98,1ef54960c8e61a0b1efff093c83ce98,1ef54960d468e10b1efff093c83ce98,1ef54960dfce5c0b1efff093c83ce98,1ef54960eb44ee0b1efff093c83ce98,1ef54960f6b1bc0b1efff093c83ce98,1ef54961021c190b1efff093c83ce98,1ef549610d951c0b1efff093c83ce98,1ef549611901ea0b1efff093c83ce98,1ef549612484b10b1efff093c83ce98,1ef549612fdb860b1efff093c83ce98,1ef549613b4d360b1efff093c83ce98,1ef549614714590b1efff093c83ce98,1ef5496152aaa80b1efff093c83ce98,1ef549615e76ad0b1efff093c83ce98,1ef5496169f9740b1efff093c83ce98,1ef549617588700b1efff093c83ce98,1ef549618128830b1efff093c83ce98,1ef549618ca3f70b1efff093c83ce98,1ef5496198e04a0b1efff093c83ce98,1ef54961a4b3a20b1efff093c83ce98,1ef54961b04ed30b1efff093c83ce98,1ef54961bbccb80b1efff093c83ce98,1ef54961c756d20b1efff093c83ce98,1ef54961d316a20b1efff093c83ce98,1ef54961dea0bc0b1efff093c83ce98,1ef54961ea0ffb0b1efff093c83ce98,1ef54961f58de00b1efff093c83ce98,1ef54962010bc50b1efff093c83ce98,1ef549620c9fa30b1efff093c83ce98,1ef54962181b170b1efff093c83ce98,1ef5496223dae70b1efff093c83ce98,1ef549622f5dae0b1efff093c83ce98,1ef549623aef1b0b1efff093c83ce98,1ef549624679350b1efff093c83ce98,1ef5496252e8cd0b1efff093c83ce98,1ef549625e75580b1efff093c83ce98,1ef549626a37990b1efff093c83ce98,1ef5496275cb770b1efff093c83ce98,1ef5496281929a0b1efff093c83ce98,1ef549628d48a60b1efff093c83ce98,1ef5496298f9d00b1efff093c83ce98,1ef54962a492900b1efff093c83ce98,1ef54962b03c670b1efff093c83ce98,1ef54962bbed910b1efff093c83ce98,1ef54962c7753a0b1efff093c83ce98,1ef54962d37e480b1efff093c83ce98,1ef54962df67990b1efff093c83ce98,1ef54962eb0c8e0b1efff093c83ce98,1ef54962f6a7bf0b1efff093c83ce98,1ef549630271530b1efff093c83ce98,1ef549630e07a20b1efff093c83ce98,1ef5496319a7b50b1efff093c83ce98,1ef549632569f60b1efff093c83ce98,1ef54963310a090b1efff093c83ce98,1ef549633ca2c90b1efff093c83ce98,1ef54963483b890b1efff093c83ce98,1ef549635435f10b1efff093c83ce98,1ef549635fac830b1efff093c83ce98,1ef549636b47b40b1efff093c83ce98,1ef5496377162a0b1efff093c83ce98,1ef5496382aeea0b1efff093c83ce98,1ef549638e64f60b1efff093c83ce98,1ef549639a113e0b1efff093c83ce98,1ef54963a5b1510b1efff093c83ce98,1ef54963b13b6b0b1efff093c83ce98,1ef54963bce0600b1efff093c83ce98,1ef54963c8f0c10b1efff093c83ce98,1ef54963d493450b1efff093c83ce98,1ef54963e03aab0b1efff093c83ce98,1ef54963ebc7360b1efff093c83ce98,1ef54963f778600b1efff093c83ce98,1ef5496402f8b60b1efff093c83ce98,1ef549640e9b3a0b1efff093c83ce98,1ef549641a22e30b1efff093c83ce98,1ef549642df9030b1efff093c83ce98,1ef5496439bdb50b1efff093c83ce98,1ef549644556750b1efff093c83ce98,1ef5496450c8250b1efff093c83ce98,1ef549645c4aec0b1efff093c83ce98,1ef5496467efe10b1efff093c83ce98,1ef549647397470b1efff093c83ce98,1ef549647f375a0b1efff093c83ce98,1ef549648ad4fc0b1efff093c83ce98,1ef54964968fea0b1efff093c83ce98,1ef54964a276ca0b1efff093c83ce98,1ef54964ae73a30b1efff093c83ce98,1ef54964b9f8db0b1efff093c83ce98,1ef54964c58a480b1efff093c83ce98,1ef54964d13de30b1efff093c83ce98,1ef54964dcca6e0b1efff093c83ce98,1ef54964e83c1e0b1efff093c83ce98,1ef54964f3c3c70b1efff093c83ce98,1ef54964ff3a590b1efff093c83ce98,1ef549650b10220b1efff093c83ce98,1ef549651681d20b1efff093c83ce98,1ef549652204990b1efff093c83ce98,1ef549652d76490b1efff093c83ce98,1ef549653905450b1efff093c83ce98,1ef5496544684f0b1efff093c83ce98,1ef5496550010f0b1efff093c83ce98,1ef549655b86470b1efff093c83ce98,1ef5496566e9510b1efff093c83ce98,1ef54965726c180b1efff093c83ce98,1ef549657e381d0b1efff093c83ce98,1ef5496589bfc60b1efff093c83ce98,1ef5496595476f0b1efff093c83ce98,1ef54965a0bb900b1efff093c83ce98,1ef54965ac37040b1efff093c83ce98";

        String[] split = ids.split(",");
        for (String s : split) {
            System.out.println(UUIDConverter.fromString(s));
        }
    }

}
