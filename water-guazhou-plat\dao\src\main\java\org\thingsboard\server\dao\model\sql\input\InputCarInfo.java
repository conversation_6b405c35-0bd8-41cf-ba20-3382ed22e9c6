package org.thingsboard.server.dao.model.sql.input;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_INPUT_CAR_INFO_TABLE)
@TableName(ModelConstants.TB_INPUT_CAR_INFO_TABLE)
@NoArgsConstructor
public class InputCarInfo {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_INPUT_CAR_INFO_CAR_NO)
    private String carNo;

    @Column(name = ModelConstants.TB_INPUT_CAR_INFO_PASSENGER)
    private Integer passenger;

    @Column(name = ModelConstants.TB_INPUT_CAR_INFO_EVENT)
    private String event;

    @Column(name = ModelConstants.TB_INPUT_CAR_INFO_IN_TIME)
    private Date inTime;

    @Column(name = ModelConstants.TB_INPUT_CAR_INFO_OUT_TIME)
    private Date outTime;

    @Column(name = ModelConstants.TB_INPUT_CAR_INFO_CREATE_USER)
    private String createUser;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    @TableField(exist = false)
    private String createUserName;


}
