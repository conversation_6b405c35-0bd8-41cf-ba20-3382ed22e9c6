package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxNews;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class WxNewsSaveRequest extends SaveRequest<WxNews> {
    // 标题
    @NotNullOrEmpty
    private String title;

    // 摘要
    @NotNullOrEmpty
    private String summary;

    // 内容
    @NotNullOrEmpty
    private String content;

    // 类别id
    @NotNullOrEmpty
    private String categoryId;

    // 封面图片
    @NotNullOrEmpty
    private String cover;

    // 排序编号
    private Integer orderNum;


    private String serialNo;


    @Override
    protected WxNews build() {
        WxNews entity = new WxNews();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected WxNews update(String id) {
        WxNews entity = new WxNews();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(WxNews entity) {
        entity.setTitle(title);
        entity.setSummary(summary);
        entity.setContent(content);
        entity.setCategoryId(categoryId);
        entity.setCover(cover);
        entity.setOrderNum(orderNum);
        entity.setSerialNo(serialNo);
    }

}