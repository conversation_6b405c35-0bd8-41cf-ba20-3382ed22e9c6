<template>
  <SLCard title="大用户占比">
    <template #query>
      <div class="">
        <Search ref="refSearch" :config="SearchConfig"> </Search>
      </div>
    </template>
    <div class="charts-box">
      <div ref="agriEcoDev" class="chart-box">
        <VChart ref="refChart1" :option="state.pieChartOption"></VChart>
      </div>
      <div ref="refDiv" class="chart-box">
        <FormTable v-if="state.showTable" :config="TableConfig"></FormTable>
        <VChart v-else ref="refChart2" :option="state.barOption"></VChart>
      </div>
    </div>
  </SLCard>
</template>
<script lang="ts" setup>
import { GetPartitionBigUserWaterPercent } from '@/api/mapservice/dma';
import { useDetector } from '@/hooks/echarts';
import { useAppStore } from '@/store';
import { ring } from '../echarts';

const refSearch = ref<ISearchIns>();
const state = reactive<{
  pieChartOption: any;
  barOption: any;
  showTable: boolean;
}>({
  pieChartOption: null,
  barOption: null,
  showTable: false
});

const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'month',
    grade: '1',
    tc: 'c'
  },
  filters: [
    {
      label: '',
      field: 'grade',
      type: 'select',
      options: [
        {
          label: '一级分区',
          value: '1'
        },
        {
          label: '二级分区',
          value: '2'
        }
      ],
      onChange: () => {
        refreshData();
      }
    },
    {
      label: '',
      field: 'type',
      type: 'radio-button',
      options: [
        // { label: '日', value: 'day' },
        { label: '月', value: 'month' },
        { label: '季度', value: 'quarter' },
        { label: '年', value: 'year' }
      ],
      onChange: () => {
        refreshData();
      }
    },
    {
      label: '',
      field: 'tc',
      type: 'radio-button',
      options: [
        { label: '', iconifyIcon: 'ep:pie-chart', value: 'c' },
        { label: '', iconifyIcon: 'ep:grid', value: 't' }
      ],
      onChange: (val) => {
        state.showTable = val === 't';
      }
    }
  ]
});

const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  columns: [{ label: '区域名称', prop: 'name' }],
  pagination: {
    hide: true
  }
});
const bar = (data: { name: string; total: number }[]) => {
  const xData: any[] = [];
  const yData = data.map((item) => {
    xData.push(item.name);
    return item.total;
  });
  return {
    backgroundColor: useAppStore().isDark ? 'transparent' : '#F4F7FA',
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: 60,
      right: 40,
      top: 40,
      bottom: 30,
      containLabel: true
    },
    xAxis: {
      axisLabel: {
        color: '#409EFF'
      },
      boundaryGap: false,
      type: 'category',
      data: xData,
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#409EFF'
      },
      name: '大用户用水量(m³)',
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#409EFF',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'bar',
        barWidth: 15,
        data: yData,
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  };
};

const refreshData = () => {
  const query = refSearch.value?.queryParams || {};
  GetPartitionBigUserWaterPercent({
    ...query
  }).then((res) => {
    const data = res.data.data || [];
    const ringData = data?.map((item) => {
      return {
        name: item.name,
        value: item.total,
        scale: item.rate
      };
    });
    state.pieChartOption = ring(ringData, 'm³');
    state.barOption = bar(data || []);
    TableConfig.columns = [
      { fixed: 'left', minWidth: 160, label: '区域名称', prop: 'name' },
      ...(data[0]?.x?.map((item) => ({
        minWidth: 120,
        label: item,
        prop: item
      })) || [])
    ];
    TableConfig.dataList = data.map((item) => {
      const obj = {
        name: item.name
      };
      item.x?.map((o, i) => {
        obj[o] = item.y?.[i];
      });
      return obj;
    });
  });
};
const detector = useDetector();
const refChart1 = ref();
const refChart2 = ref();
const refDiv = ref();
onMounted(() => {
  refreshData();
  detector.listenToMush(refDiv.value, () => {
    refChart1.value?.resize();
    refChart2.value?.resize();
  });
});
</script>
<style lang="scss" scoped>
.charts-box {
  width: 100%;
  height: 100%;
  display: flex;
  .chart-box {
    width: 50%;
    height: 100%;
  }
}
</style>
