package org.thingsboard.server.dao.smartProduction.guard.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRearrangeRecord;
import org.thingsboard.server.dao.smartProduction.guard.GuardRearrangeRecordService;
import org.thingsboard.server.dao.sql.smartProduction.guard.GuardRearrangeRecordMapper;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangeSwitchRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardRearrangeRecordPageRequest;

@Service
public class GuardRearrangeRecordServiceImpl implements GuardRearrangeRecordService {
    @Autowired
    private GuardRearrangeRecordMapper mapper;

    @Override
    public GuardRearrangeRecord findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<GuardRearrangeRecord> findAllConditional(GuardRearrangeRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean record(GuardArrangeSwitchRequest req) {
        return mapper.record(req);
    }

}
