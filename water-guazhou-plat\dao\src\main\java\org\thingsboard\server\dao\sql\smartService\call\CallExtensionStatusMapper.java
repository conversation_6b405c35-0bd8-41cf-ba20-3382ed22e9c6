package org.thingsboard.server.dao.sql.smartService.call;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.ExtensionStatusDTO;
import org.thingsboard.server.dao.model.sql.smartService.call.CallExtensionStatus;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface CallExtensionStatusMapper extends BaseMapper<CallExtensionStatus> {

    List<ExtensionStatusDTO> getList(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("tenantId") String tenantId);

    List<CallExtensionStatus> getDetail(@Param("seatsId") String seatsId, @Param("page") int page, @Param("size") int size);

    int getDetailCount(@Param("seatsId") String seatsId);
}
