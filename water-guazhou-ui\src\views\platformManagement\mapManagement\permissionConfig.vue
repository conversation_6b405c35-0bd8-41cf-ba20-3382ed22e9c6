<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, computed, nextTick } from 'vue'
import { 
  getPermissionConfigList, 
  addPermissionConfig, 
  editPermissionConfig, 
  deletePermissionConfig,
  getPermissionConfigDetail,
  getRoleList
} from '@/api/platformManagement/permissionConfig'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()
const refDialogForm = ref()
const roleOptions = ref([])

// 创建角色映射表，提高查找效率
const roleMap = computed(() => {
  const map = new Map()
  roleOptions.value.forEach(role => {
    map.set(role.value, role.label)
  })
  console.log('角色映射表:', map)
  return map
})

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '底图配置ID', 
      field: 'mapConfigId', 
      placeholder: '请输入底图配置ID',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '底图配置ID', prop: 'mapConfigId' },
    { 
      label: '角色', 
      prop: 'rolesDisplay',
      minWidth: 200
    },
    { label: '备注', prop: 'remark', showOverflowTooltip: true }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增权限配置',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '底图配置ID',
          field: 'mapConfigId',
          rules: [{ required: true, message: '请输入底图配置ID' }]
        },
        {
          type: 'select',
          label: '角色IDs',
          field: 'roles',
          placeholder: '请选择角色',
          multiple: true,
          clearable: true,
          filterable: true,
          get options() {
            return roleOptions.value
          },
          rules: [{ required: true, message: '请选择角色' }]
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark',
          placeholder: '请输入备注信息'
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      console.log('提交的原始参数:', params)
      console.log('roles字段类型:', typeof params.roles)
      console.log('roles字段值:', params.roles)
      
      // 处理roles字段：将数组转换为字符串格式
      const submitParams = { ...params }
      if (Array.isArray(submitParams.roles)) {
        submitParams.roles = submitParams.roles.join(',')
      }
      
      console.log('转换后的提交参数:', submitParams)
      console.log('转换后roles字段:', submitParams.roles)
      
      if (params.id) {
        await editPermissionConfig(submitParams)
        SLMessage.success('修改成功')
      } else {
        await addPermissionConfig(submitParams)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      console.error('提交失败:', error)
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  // 重置所有表单字段为可编辑状态
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
    
    // 特别处理角色字段，确保恢复为多选下拉选择
    if (field.field === 'roles') {
      field.type = 'select'
      field.multiple = true
      field.clearable = true
      field.filterable = true
      field.placeholder = '请选择角色'
      // 确保options能正确获取
      if (!field.options || typeof field.options !== 'function') {
        Object.defineProperty(field, 'options', {
          get() {
            return roleOptions.value
          },
          configurable: true
        })
      }
    }
  })
  
  // 恢复默认按钮配置
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'
  
  // 恢复提交函数
  DialogFormConfig.submit = async (params) => {
    try {
      console.log('提交的原始参数:', params)
      console.log('roles字段类型:', typeof params.roles)
      console.log('roles字段值:', params.roles)
      
      // 处理roles字段：将数组转换为字符串格式
      const submitParams = { ...params }
      if (Array.isArray(submitParams.roles)) {
        submitParams.roles = submitParams.roles.join(',')
      }
      
      console.log('转换后的提交参数:', submitParams)
      console.log('转换后roles字段:', submitParams.roles)
      
      if (params.id) {
        await editPermissionConfig(submitParams)
        SLMessage.success('修改成功')
      } else {
        await addPermissionConfig(submitParams)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      console.error('提交失败:', error)
      SLMessage.error('操作失败')
    }
  }
  
  // 清除自定义按钮配置
  DialogFormConfig.footerBtns = undefined
}

// 新增/编辑
const handleAdd = (row) => {
  resetDialogConfig()
  
  // 处理编辑时的数据
  let defaultValue = { ...(row || {}) }
  // 处理roles字段格式转换
  if (defaultValue.roles) {
    if (typeof defaultValue.roles === 'string') {
      // 如果是字符串（逗号分隔），转换为数组用于多选显示
      defaultValue.roles = defaultValue.roles.split(',').map(role => role.trim()).filter(role => role)
    }
    // 如果已经是数组，保持原样
  }
  
  console.log('编辑时的默认值:', defaultValue)
  console.log('roles字段处理结果:', defaultValue.roles)
  
  DialogFormConfig.title = row ? '编辑权限配置' : '新增权限配置'
  DialogFormConfig.defaultValue = defaultValue
  refDialogForm.value?.openDialog()
}

// 查看详情
const handleDetail = async (row) => {
  // 模拟详情数据，使用字符串格式的角色ID
  const mockDetailData = {
    id: row.id || '1',
    mapConfigId: row.mapConfigId || 'MAP001',
    roles: row.roles || '54426900-7b72-11ed-85ef-9fe6e6dd373e,5e356840-7b72-11ed-85ef-9fe6e6dd373e',
    remark: row.remark || '这是权限配置的详情数据'
  }

  try {
    console.log('获取详情，行数据:', row)
    
    const res = await getPermissionConfigDetail(row.id)
    console.log('详情API响应:', res)
    
    let detailData = null
    
    // 处理不同的数据结构
    if (res.data) {
      if (res.data.data) {
        detailData = res.data.data
      } else {
        detailData = res.data
      }
    } else if (res) {
      detailData = res
    }
    
    console.log('解析后的详情数据:', detailData)
    
    // 如果没有获取到详情数据，使用模拟数据
    if (!detailData) {
      console.log('使用模拟详情数据')
      detailData = mockDetailData
    }
    
    // 处理roles字段显示 - 转换为角色名称显示
    if (detailData.roles) {
      if (typeof detailData.roles === 'string') {
        // 如果是字符串格式（逗号分隔），转换为角色名称
        const roleIds = detailData.roles.split(',').map(id => id.trim()).filter(id => id)
        const roleNames = roleIds.map(roleId => {
          const roleName = roleMap.value.get(roleId)
          return roleName || roleId
        }).filter(name => name)
        detailData.roles = roleNames.join(', ')
      } else if (Array.isArray(detailData.roles)) {
        // 如果是数组格式（兼容性），转换为角色名称
        const roleNames = detailData.roles.map(roleId => {
          const roleName = roleMap.value.get(roleId)
          return roleName || roleId
        }).filter(name => name)
        detailData.roles = roleNames.join(', ')
      }
    }
    
    // 先重置配置
    resetDialogConfig()
    
    // 设置详情模式
    DialogFormConfig.title = '权限配置详情'
    DialogFormConfig.defaultValue = { ...detailData }
    
    console.log('设置的详情数据:', DialogFormConfig.defaultValue)
    
    // 修改角色字段为文本显示模式
    const rolesField = DialogFormConfig.group[0].fields.find(field => field.field === 'roles')
    if (rolesField) {
      rolesField.type = 'input'
      rolesField.disabled = true
      rolesField.readonly = true
    }
    
    // 设置为只读模式 - 对不同类型字段采用不同的禁用方式
    DialogFormConfig.group[0].fields.forEach(field => {
      if (field.type === 'select') {
        field.readonly = true
        field.disabled = true
      } else {
        field.disabled = true
      }
    })
    
    // 隐藏提交按钮，只显示关闭按钮 - 尝试多种方法
    DialogFormConfig.showSubmit = false
    DialogFormConfig.showCancel = true
    DialogFormConfig.cancel = true
    DialogFormConfig.cancelText = '关闭' 
    DialogFormConfig.submitText = undefined // 设置为undefined
    DialogFormConfig.submit = undefined // 设置为undefined
    DialogFormConfig.submitting = false
    DialogFormConfig.footerBtns = [
      {
        text: '关闭',
        type: 'default',
        click: () => {
          refDialogForm.value?.closeDialog()
        }
      }
    ]
    
    console.log('详情模式DialogFormConfig配置:', {
      showSubmit: DialogFormConfig.showSubmit,
      showCancel: DialogFormConfig.showCancel,
      cancel: DialogFormConfig.cancel,
      cancelText: DialogFormConfig.cancelText,
      submitText: DialogFormConfig.submitText,
      submit: DialogFormConfig.submit,
      footerBtns: DialogFormConfig.footerBtns
    })
    
    refDialogForm.value?.openDialog()
    
  } catch (error) {
    console.error('获取详情失败:', error)
    console.log('API调用失败，使用模拟详情数据')
    
    // 处理模拟数据的roles字段
    if (mockDetailData.roles) {
      if (typeof mockDetailData.roles === 'string') {
        // 如果是字符串格式，转换为角色名称
        const roleIds = mockDetailData.roles.split(',').map(id => id.trim()).filter(id => id)
        const roleNames = roleIds.map(roleId => {
          const roleName = roleMap.value.get(roleId)
          return roleName || roleId
        }).filter(name => name)
        mockDetailData.roles = roleNames.join(', ')
      } else if (Array.isArray(mockDetailData.roles)) {
        // 如果是数组格式（兼容性）
        const roleNames = mockDetailData.roles.map(roleId => {
          const roleName = roleMap.value.get(roleId)
          return roleName || roleId
        }).filter(name => name)
        mockDetailData.roles = roleNames.join(', ')
      }
    }
    
    // 先重置配置
    resetDialogConfig()
    
    // 设置详情模式
    DialogFormConfig.title = '权限配置详情'
    DialogFormConfig.defaultValue = { ...mockDetailData }
    
    // 修改角色字段为文本显示模式
    const rolesField = DialogFormConfig.group[0].fields.find(field => field.field === 'roles')
    if (rolesField) {
      rolesField.type = 'input'
      rolesField.disabled = true
      rolesField.readonly = true
    }
    
    // 设置为只读模式
    DialogFormConfig.group[0].fields.forEach(field => {
      if (field.type === 'select') {
        field.readonly = true
        field.disabled = true
      } else {
        field.disabled = true
      }
    })
    
    // 隐藏提交按钮，只显示关闭按钮 - 尝试多种方法
    DialogFormConfig.showSubmit = false
    DialogFormConfig.showCancel = true
    DialogFormConfig.cancel = true
    DialogFormConfig.cancelText = '关闭' 
    DialogFormConfig.submitText = undefined // 设置为undefined
    DialogFormConfig.submit = undefined // 设置为undefined
    DialogFormConfig.submitting = false
    DialogFormConfig.footerBtns = [
      {
        text: '关闭',
        type: 'default',
        click: () => {
          refDialogForm.value?.closeDialog()
        }
      }
    ]
    
    console.log('详情模式DialogFormConfig配置:', {
      showSubmit: DialogFormConfig.showSubmit,
      showCancel: DialogFormConfig.showCancel,
      cancel: DialogFormConfig.cancel,
      cancelText: DialogFormConfig.cancelText,
      submitText: DialogFormConfig.submitText,
      submit: DialogFormConfig.submit,
      footerBtns: DialogFormConfig.footerBtns
    })
    
    refDialogForm.value?.openDialog()
    
    SLMessage.error('API调用失败，当前显示模拟数据')
  }
}

// 删除
const handleDelete = (row) => {
  SLConfirm('确定删除？', '删除提示')
    .then(async () => {
      try {
        const ids = row
          ? [row.id]
          : TableConfig.selectList?.map(item => item.id) || []

        if (!ids.length) {
          SLMessage.warning('请选择要删除的数据')
          return
        }

        const res = await deletePermissionConfig(ids)
        if (res.data) {
          SLMessage.success('删除成功')
          refreshData()
        } else {
          SLMessage.error('删除失败')
        }
      } catch (error) {
        SLMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 刷新数据
const refreshData = async () => {
  // 模拟数据 - 用于测试显示，使用字符串格式的角色ID
  const mockData = [
    {
      id: '1',
      mapConfigId: 'MAP001',
      roles: '54426900-7b72-11ed-85ef-9fe6e6dd373e,5e356840-7b72-11ed-85ef-9fe6e6dd373e',
      remark: '管理员权限配置'
    },
    {
      id: '2', 
      mapConfigId: 'MAP002',
      roles: '625773a0-7b72-11ed-85ef-9fe6e6dd373e,66b8aae0-7b72-11ed-85ef-9fe6e6dd373e',
      remark: '普通用户权限配置'
    }
  ]

  try {
    const query = refSearch.value?.queryParams
    console.log('请求参数:', {
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(query || {})
    })
    
    const res = await getPermissionConfigList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(query || {})
    })
    
    console.log('API响应数据:', res)
    
    // 处理不同的数据结构
    if (res.data) {
      // 情况1: res.data.records 和 res.data.total
      if (res.data.records) {
        TableConfig.dataList = res.data.records || []
        TableConfig.pagination.total = res.data.total || 0
      }
      // 情况2: res.data.data.records 和 res.data.data.total  
      else if (res.data.data && res.data.data.records) {
        TableConfig.dataList = res.data.data.records || []
        TableConfig.pagination.total = res.data.data.total || 0
      }
      // 情况3: res.data 直接是数组
      else if (Array.isArray(res.data)) {
        TableConfig.dataList = res.data
        TableConfig.pagination.total = res.data.length
      }
      // 情况4: res.data.data 是数组
      else if (Array.isArray(res.data.data)) {
        TableConfig.dataList = res.data.data
        TableConfig.pagination.total = res.data.data.length
      }
      else {
        console.warn('未知的数据结构:', res.data)
        TableConfig.dataList = []
        TableConfig.pagination.total = 0
      }
    }
    // 情况5: 直接是数组格式
    else if (Array.isArray(res)) {
      TableConfig.dataList = res
      TableConfig.pagination.total = res.length
    }
    else {
      console.warn('无法解析的响应格式:', res)
      TableConfig.dataList = []
      TableConfig.pagination.total = 0
    }
    
    console.log('解析后的数据:', TableConfig.dataList)
    console.log('总数:', TableConfig.pagination.total)
    
    // 处理角色显示数据
    TableConfig.dataList = processTableData(TableConfig.dataList)
    console.log('处理后的表格数据:', TableConfig.dataList)
    
    // 如果没有数据，使用模拟数据进行测试
    if (TableConfig.dataList.length === 0) {
      console.log('使用模拟数据进行测试')
      const processedMockData = processTableData(mockData)
      TableConfig.dataList = processedMockData
      TableConfig.pagination.total = mockData.length
    }
    
  } catch (error) {
    console.error('获取数据失败:', error)
    console.log('API调用失败，使用模拟数据')
    
    // API调用失败时使用模拟数据
    const processedMockData = processTableData(mockData)
    TableConfig.dataList = processedMockData
    TableConfig.pagination.total = mockData.length
    
    SLMessage.error('API调用失败，当前显示模拟数据')
  }
}

// 获取角色选项数据
const getRoleOptions = async () => {
  // 模拟角色数据 - 使用真实的角色ID和名称
  const mockRoleData = [
    { label: '管理员', value: '54426900-7b72-11ed-85ef-9fe6e6dd373e' },
    { label: '高管层', value: '5e356840-7b72-11ed-85ef-9fe6e6dd373e' },
    { label: '巡检养护', value: '625773a0-7b72-11ed-85ef-9fe6e6dd373e' },
    { label: '调度中心', value: '66b8aae0-7b72-11ed-85ef-9fe6e6dd373e' },
    { label: '客户服务', value: '6a75d9f0-7b72-11ed-85ef-9fe6e6dd373e' },
    { label: '收费员', value: '6d72c640-7b72-11ed-85ef-9fe6e6dd373e' },
    { label: '抄表员', value: '7051ca50-7b72-11ed-85ef-9fe6e6dd373e' },
    { label: '演示账号', value: '8f419d10-ab87-11ed-8712-5bb0dc03abd0' },
    { label: '甘谷水务', value: '971bc320-bc09-11ed-a461-fb299c9b23b7' },
    { label: '盐亭水务', value: '6558f810-bc92-11ed-a461-fb299c9b23b7' }
  ]

  try {
    const res = await getRoleList()
    console.log('角色API响应:', res)
    
    let roleData = []
    
    // 处理不同的数据结构
    if (res.data) {
      if (Array.isArray(res.data)) {
        roleData = res.data
      } else if (res.data.data && Array.isArray(res.data.data)) {
        roleData = res.data.data
      } else if (res.data.records && Array.isArray(res.data.records)) {
        roleData = res.data.records
      }
    } else if (Array.isArray(res)) {
      roleData = res
    }
    
    console.log('原始角色数据:', roleData)
    
    // 转换数据格式为下拉选项需要的格式
    // 处理嵌套的ID结构：item.id.id
    const processedOptions = roleData.map(item => {
      const roleId = item.id?.id || item.id || item.roleId || item.value || item.code
      const roleName = item.name || item.roleName || item.label || item.title
      
      console.log('处理角色项:', { 
        原始数据: item, 
        提取ID: roleId, 
        提取名称: roleName 
      })
      
      return {
        label: roleName,
        value: roleId
      }
    }).filter(option => option.label && option.value) // 过滤掉无效选项
    
    // 去重处理，确保选项值的唯一性
    const uniqueOptions = []
    const seenValues = new Set()
    
    processedOptions.forEach(option => {
      if (!seenValues.has(option.value)) {
        seenValues.add(option.value)
        uniqueOptions.push(option)
      }
    })
    
    roleOptions.value = uniqueOptions
    
    console.log('解析后的角色选项:', roleOptions.value)
    console.log('角色选项数量:', roleOptions.value.length)
    
    // 如果没有获取到数据，使用模拟数据
    if (roleOptions.value.length === 0) {
      console.log('使用模拟角色数据')
      roleOptions.value = mockRoleData
    }
    
    // 角色选项加载完成后，如果表格已有数据，强制刷新表格显示
    if (TableConfig.dataList.length > 0) {
      console.log('角色选项已更新，重新处理表格数据')
      // 重新处理表格数据中的角色显示
      TableConfig.dataList = processTableData(TableConfig.dataList)
      console.log('重新处理后的表格数据:', TableConfig.dataList)
    }
    
  } catch (error) {
    console.error('获取角色数据失败:', error)
    console.log('API调用失败，使用模拟角色数据')
    roleOptions.value = mockRoleData
    SLMessage.error('获取角色数据失败，当前显示模拟数据')
    
    // 即使使用模拟数据，也要刷新表格
    if (TableConfig.dataList.length > 0) {
      console.log('使用模拟数据，重新处理表格数据')
      // 重新处理表格数据中的角色显示
      TableConfig.dataList = processTableData(TableConfig.dataList)
      console.log('重新处理后的表格数据:', TableConfig.dataList)
    }
  }
}

// 处理表格数据，添加角色显示字段
const processTableData = (dataList) => {
  return dataList.map(row => {
    const processedRow = { ...row }
    
    // 处理角色显示
    if (row.roles) {
      let roleNames = []
      
      // 优先处理字符串格式（逗号分隔的角色ID）
      if (typeof row.roles === 'string' && row.roles) {
        const roleIds = row.roles.split(',').map(id => id.trim()).filter(id => id)
        console.log(`处理行数据角色ID数组:`, roleIds)
        
        roleNames = roleIds.map(roleId => {
          const roleName = roleMap.value.get(roleId)
          console.log(`映射角色ID ${roleId}:`, roleName || '未找到')
          return roleName || roleId
        }).filter(name => name)
      }
      // 处理数组格式（兼容性）
      else if (Array.isArray(row.roles)) {
        console.log(`处理数组格式的角色:`, row.roles)
        
        roleNames = row.roles.map(roleId => {
          const roleName = roleMap.value.get(roleId)
          console.log(`映射角色ID ${roleId}:`, roleName || '未找到')
          return roleName || roleId
        }).filter(name => name)
      }
      
      console.log(`最终角色名称数组:`, roleNames)
      
      if (roleNames.length === 0) {
        processedRow.rolesDisplay = '-'
      } else if (roleNames.length > 3) {
        const displayRoles = roleNames.slice(0, 3)
        processedRow.rolesDisplay = `🏷️ ${displayRoles.join(' | ')} 等 (共${roleNames.length}个)`
      } else {
        processedRow.rolesDisplay = `🏷️ ${roleNames.join(' | ')}`
      }
    } else {
      processedRow.rolesDisplay = '-'
    }
    
    return processedRow
  })
}

onMounted(async () => {
  // 先加载角色选项数据，再加载表格数据
  await getRoleOptions()
  console.log('角色选项加载完成，开始加载表格数据')
  
  // 测试角色映射功能
  console.log('=== 角色映射测试 ===')
  console.log('角色选项:', roleOptions.value)
  console.log('角色映射表:', roleMap.value)
  
  // 测试几个ID的映射
  const testIds = ['54426900-7b72-11ed-85ef-9fe6e6dd373e', '5e356840-7b72-11ed-85ef-9fe6e6dd373e']
  testIds.forEach(id => {
    const name = roleMap.value.get(id)
    console.log(`测试映射 ${id} -> ${name}`)
  })
  
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  flex: 1;
  margin-top: 16px;
}

// 表格行高优化，为角色列提供更多空间
:deep(.el-table__row) {
  .el-table__cell {
    padding: 12px 0;
    
    // 角色列特殊样式
    &:nth-child(2) {
      font-size: 13px;
      line-height: 1.4;
      color: #374151;
      
      // 角色文本样式
      .cell {
        padding: 4px 8px;
      }
    }
  }
}

// 角色列文本优化
:deep(.el-table .cell) {
  word-break: break-word;
  line-height: 1.5;
}
</style> 