<!-- 巡检配置 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refForm"
      :config="FormConfig"
    >
    </DialogForm>
  </div>
</template>
<script lang="ts" setup>
import { Plus, Refresh, Search } from '@element-plus/icons-vue'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'
import { SLConfirm } from '@/utils/Message.js'
import { circuitConfigList, addCircuitConfig, delCircuitConfig } from '@/api/headwatersManage/waterInspection'
import { projectType } from '../data/data'

const refForm = ref<IDialogFormIns>()
const refSearch = ref<ICardSearchIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    { type: 'select', label: '项目类型', field: 'itemType', options: projectType },
    { type: 'input', label: '项目名称', field: 'name' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', svgIcon: shallowRef(Search), click: () => refreshData() },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '添加',
          svgIcon: shallowRef(Plus),
          click: () => {
            FormConfig.title = '新增'
            handleAddEdit()
          }
        }
      ]
    }
  ]
})
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: [
    { label: '项目分类', prop: 'itemType' },
    { label: '项目名称', prop: 'name' },
    { label: '巡检方法', prop: 'method' },
    { label: '巡检要求', prop: 'require' }
  ],
  operations: [
    { perm: true,
      text: '修改',
      click: row => {
        FormConfig.title = '修改'
        handleAddEdit(row)
      }
    },
    { perm: true, text: '删除', type: 'danger', click: row => handleDelete(row) }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
const FormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  title: '新增',
  defaultValue: {
  },
  group: [
    {
      fields: [
        { type: 'select', label: '项目分类', field: 'itemType', options: projectType, clearable: false },
        { type: 'input', label: '项目名称', field: 'name', rules: [{ required: true, message: '请输入项目名称' }], placeholder: '请输入项目名称' },
        { type: 'textarea', label: '巡检方法', field: 'method', rules: [{ required: true, message: '请输入巡检方法' }], placeholder: '请输入巡检方法' },
        { type: 'textarea', label: '巡检要求', field: 'require', rules: [{ required: true, message: '请输入巡检要求' }], placeholder: '请输入巡检要求' }
      ]
    }
  ]
})
const handleAddEdit = (row?: any) => {
  FormConfig.defaultValue = {
    ...row || {}
  }
  FormConfig.submit = (params: any) => {
    SLConfirm('确定提交？', '提示信息').then(() => {
      params.type = '二供泵房'
      params.id = row ? row.id : null
      addCircuitConfig(params).then(() => {
        refForm.value?.resetForm()
        refForm.value?.closeDialog()
        refreshData()
      })
    }).catch(() => {
      //
    })
  }
  refForm.value?.openDialog()
}

// 删除配置
const handleDelete = (row?: any) => {
  SLConfirm('确定删除?', '提示信息').then(() => {
    delCircuitConfig(row.id).then(() => {
      refreshData()
    })
  }).catch(() => {
    //
  })
}
// 获取配置数据列表
const refreshData = async () => {
  const query = refSearch.value?.queryParams || {}
  const params = {
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    type: '二供泵房',
    ...query
  }
  const result = await circuitConfigList(params)
  console.log(result.data.data.total)
  TableConfig.pagination.total = result.data?.data.total
  TableConfig.dataList = result.data?.data.data
}

onMounted(async () => {
  refreshData()
})
</script>
<style lang="scss" scoped>

</style>
