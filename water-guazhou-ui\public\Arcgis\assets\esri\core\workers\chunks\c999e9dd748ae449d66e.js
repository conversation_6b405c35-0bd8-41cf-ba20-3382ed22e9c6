"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6584,9880],{53073:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(75148),a=n(59987),s=n(91136),i=n(90961),l=n(90658),u=n(8667);class o extends s.Z{constructor(e){super(e),this._relation="",this._relationGeom=null,this._relationString="",this.declaredClass="esri.arcade.featureset.actions.SpatialFilter",this._relationString=e.relationString,this._parent=e.parentfeatureset,this._maxProcessing=40,this._relation=e.relation,this._relationGeom=e.relationGeom}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._parent._getFilteredSet("esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,null,null,e);return this._checkCancelled(e),this._wset=new i.Z(t._candidates.slice(0),t._known.slice(0),t._ordered,this._clonePageDefinition(t.pagesDefinition)),this._wset}return this._wset}_isInFeatureSet(e){let t=this._parent._isInFeatureSet(e);return t===l.dj.NotInFeatureSet?t:(t=this._idstates[e],void 0===t?l.dj.Unknown:t)}_getFeature(e,t,n){return this._parent._getFeature(e,t,n)}_getFeatures(e,t,n,r){return this._parent._getFeatures(e,t,n,r)}_featureFromCache(e){return this._parent._featureFromCache(e)}async executeSpatialRelationTest(e){if(null===e.geometry)return!1;switch(this._relation){case"esriSpatialRelEnvelopeIntersects":{const t=(0,l.SV)(this._relationGeom),n=(0,l.SV)(e.geometry);return(0,u.kK)(t,n)}case"esriSpatialRelIntersects":return(0,u.kK)(this._relationGeom,e.geometry);case"esriSpatialRelContains":return(0,u.r3)(this._relationGeom,e.geometry);case"esriSpatialRelOverlaps":return(0,u.Nm)(this._relationGeom,e.geometry);case"esriSpatialRelWithin":return(0,u.uh)(this._relationGeom,e.geometry);case"esriSpatialRelTouches":return(0,u.W4)(this._relationGeom,e.geometry);case"esriSpatialRelCrosses":return(0,u.jU)(this._relationGeom,e.geometry);case"esriSpatialRelRelation":return(0,u.LV)(this._relationGeom,e.geometry,this._relationString??"")}}async _fetchAndRefineFeatures(e,t,n){const r=new i.Z([],e,!1,null),a=Math.min(t,e.length);await(this._parent?._getFeatures(r,-1,a,n)),this._checkCancelled(n);const s=[];for(let t=0;t<a;t++){const n=this._parent._featureFromCache(e[t]);s.push(await this.executeSpatialRelationTest(n))}for(let n=0;n<t;n++)!0===s[n]?this._idstates[e[n]]=l.dj.InFeatureSet:this._idstates[e[n]]=l.dj.NotInFeatureSet;return"success"}async _getFilteredSet(e,t,n,r,a){await this._ensureLoaded();const s=await this._parent._getFilteredSet("esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,n,r,a);let l;return this._checkCancelled(a),l=null!==t?new i.Z(s._candidates.slice(0).concat(s._known.slice(0)),[],s._ordered,this._clonePageDefinition(s.pagesDefinition)):new i.Z(s._candidates.slice(0),s._known.slice(0),s._ordered,this._clonePageDefinition(s.pagesDefinition)),l}async _stat(e,t,n,r,a,s,i){if(""!==n)return{calculated:!1};const l=await this._parent._stat(e,t,"esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,a,s,i);return!1===l.calculated?null===a&&""===n&&null===r?this._manualStat(e,t,s,i):{calculated:!1}:l}async _canDoAggregates(e,t,n,r,a){return""===n&&null===r&&null!==this._parent&&this._parent._canDoAggregates(e,t,"esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,a)}async _getAggregatePagesDataSourceDefinition(e,t,n,r,s,i,l){if(null===this._parent)throw new a.EN(a.H9.NeverReach);return this._parent._getAggregatePagesDataSourceDefinition(e,t,"esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,s,i,l)}static registerAction(){s.Z._featuresetFunctions.intersects=function(e){return null==e?new r.Z({parentfeatureset:this}):new o({parentfeatureset:this,relation:"esriSpatialRelIntersects",relationGeom:e})},s.Z._featuresetFunctions.envelopeIntersects=function(e){return null==e?new r.Z({parentfeatureset:this}):new o({parentfeatureset:this,relation:"esriSpatialRelEnvelopeIntersects",relationGeom:e})},s.Z._featuresetFunctions.contains=function(e){return null==e?new r.Z({parentfeatureset:this}):new o({parentfeatureset:this,relation:"esriSpatialRelContains",relationGeom:e})},s.Z._featuresetFunctions.overlaps=function(e){return null==e?new r.Z({parentfeatureset:this}):new o({parentfeatureset:this,relation:"esriSpatialRelOverlaps",relationGeom:e})},s.Z._featuresetFunctions.within=function(e){return null==e?new r.Z({parentfeatureset:this}):new o({parentfeatureset:this,relation:"esriSpatialRelWithin",relationGeom:e})},s.Z._featuresetFunctions.touches=function(e){return null==e?new r.Z({parentfeatureset:this}):new o({parentfeatureset:this,relation:"esriSpatialRelTouches",relationGeom:e})},s.Z._featuresetFunctions.crosses=function(e){return null==e?new r.Z({parentfeatureset:this}):new o({parentfeatureset:this,relation:"esriSpatialRelCrosses",relationGeom:e})},s.Z._featuresetFunctions.relate=function(e,t){return null==e?new r.Z({parentfeatureset:this}):new o({parentfeatureset:this,relation:"esriSpatialRelRelation",relationGeom:e,relationString:t})}}}},75148:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(59987),a=n(91136),s=n(90961),i=n(90658);class l extends a.Z{constructor(e){super(e),this.declaredClass="esri.layers.featureset.sources.Empty",this._maxProcessing=1e3,this._wset=new s.Z([],[],!1,null),this._parent=e.parentfeatureset,this._databaseType=i.Bj.Standardised}async _getSet(){return this._wset}optimisePagingFeatureQueries(){}_isInFeatureSet(){return i.dj.NotInFeatureSet}async _getFeature(){throw new r.EN(r.H9.NeverReach)}async queryAttachments(){return[]}async _getFeatures(){return"success"}_featureFromCache(){return null}async _fetchAndRefineFeatures(){throw new r.EN(r.H9.NeverReach)}async _getFilteredSet(){return new s.Z([],[],!1,null)}_stat(e,t,n,r,a,s,i){return this._manualStat(e,t,s,i)}async _canDoAggregates(){return!1}}},91136:(e,t,n)=>{n.d(t,{Z:()=>g});var r=n(85839),a=n(48039),s=n(59987),i=n(70586);class l{constructor(e,t){this._lastId=-1,this._progress=t,this._parent=e}reset(){this._lastId=-1}nextBatch(e){if(null!==this._parent._mainSetInUse)return this._parent._mainSetInUse.then((t=>this.nextBatch(e)),(t=>this.nextBatch(e)));const t={returnpromise:null,hasset:!1},n=[];return t.returnpromise=new Promise(((r,a)=>{this._parent._getSet(this._progress).then((s=>{const l=(0,i.s3)(s._known,"known");let u=l.length-1;if("GETPAGES"===l[l.length-1]&&(u-=1),this._lastId+e>u&&l.length>0&&"GETPAGES"===l[l.length-1])return void this._parent._expandPagedSet(s,this._parent._maxQueryRate(),0,0,this._progress).then((n=>{t.hasset=!0,this._parent._mainSetInUse=null,this.nextBatch(e).then(r,a)}),(e=>{t.hasset=!0,this._parent._mainSetInUse=null,a(e)}));const o=(0,i.s3)(s._candidates,"candidates");if(u>=this._lastId+e||0===o.length){for(let t=0;t<e;t++){const e=t+this._lastId+1;if(e>=l.length)break;n[t]=l[e]}return this._lastId+=n.length,0===n.length&&(t.hasset=!0,this._parent._mainSetInUse=null,r([])),void this._parent._getFeatureBatch(n,this._progress).then((e=>{t.hasset=!0,this._parent._mainSetInUse=null,r(e)}),(e=>{t.hasset=!0,this._parent._mainSetInUse=null,a(e)}))}this._parent._refineSetBlock(s,this._parent._maxProcessingRate(),this._progress).then((()=>{t.hasset=!0,this._parent._mainSetInUse=null,this.nextBatch(e).then(r,a)}),(e=>{t.hasset=!0,this._parent._mainSetInUse=null,a(e)}))}),(e=>{t.hasset=!0,this._parent._mainSetInUse=null,a(e)}))})),!1===t.hasset&&(this._parent._mainSetInUse=t.returnpromise,t.hasset=!0),t.returnpromise}next(){if(null!==this._parent._mainSetInUse)return this._parent._mainSetInUse.then((e=>this.next()),(e=>this.next()));const e={returnpromise:null,hasset:!1};return e.returnpromise=new Promise(((t,n)=>{this._parent._getSet(this._progress).then((r=>{const a=(0,i.s3)(r._known,"known");this._lastId<a.length-1?"GETPAGES"===a[this._lastId+1]?this._parent._expandPagedSet(r,this._parent._maxQueryRate(),0,0,this._progress).then((t=>(e.hasset=!0,this._parent._mainSetInUse=null,this.next()))).then(t,n):(this._lastId+=1,this._parent._getFeature(r,a[this._lastId],this._progress).then((n=>{e.hasset=!0,this._parent._mainSetInUse=null,t(n)}),(t=>{e.hasset=!0,this._parent._mainSetInUse=null,n(t)}))):(0,i.s3)(r._candidates,"candidates").length>0?this._parent._refineSetBlock(r,this._parent._maxProcessingRate(),this._progress).then((()=>{e.hasset=!0,this._parent._mainSetInUse=null,this.next().then(t,n)}),(t=>{e.hasset=!0,this._parent._mainSetInUse=null,n(t)})):(e.hasset=!0,this._parent._mainSetInUse=null,t(null))}),(t=>{e.hasset=!0,this._parent._mainSetInUse=null,n(t)}))})),!1===e.hasset&&(this._parent._mainSetInUse=e.returnpromise,e.hasset=!0),e.returnpromise}async count(){if(-1!==this._parent._totalCount)return this._parent._totalCount;const e=await this._parent._getSet(this._progress),t=await this._refineAllSets(e);return this._parent._totalCount=t._known.length,this._parent._totalCount}async _refineAllSets(e){if(e._known.length>0&&"GETPAGES"===e._known[e._known.length-1])return await this._parent._expandPagedSet(e,this._parent._maxQueryRate(),0,1,this._progress),this._refineAllSets(e);if(e._candidates.length>0){if("GETPAGES"===e._known[e._candidates.length-1])return await this._parent._expandPagedSet(e,this._parent._maxQueryRate(),0,2,this._progress),this._refineAllSets(e);const t=await this._parent._refineSetBlock(e,this._parent._maxProcessingRate(),this._progress);return t._candidates.length>0?this._refineAllSets(t):t}return e}}var u=n(90961),o=n(90658),c=n(95002),h=n(95330),d=n(41534),f=n(8667),_=n(82971),p=n(99514);class g{constructor(e){this.recentlyUsedQueries=null,this.featureSetQueryInterceptor=null,this._idstates=[],this._parent=null,this._wset=null,this._mainSetInUse=null,this._maxProcessing=200,this._maxQuery=500,this._totalCount=-1,this._databaseType=o.Bj.NotEvaluated,this._databaseTypeProbed=null,this.declaredRootClass="esri.arcade.featureset.support.FeatureSet",this._featureCache=[],this.typeIdField=null,this.types=null,this.fields=null,this.geometryType="",this.objectIdField="",this.globalIdField="",this.spatialReference=null,this.hasM=!1,this.hasZ=!1,this._transparent=!1,this.loaded=!1,this._loadPromise=null,this._fieldsIndex=null,this._dateFieldIndex=null,e&&e.lrucache&&(this.recentlyUsedQueries=e.lrucache),e&&e.interceptor&&(this.featureSetQueryInterceptor=e.interceptor)}optimisePagingFeatureQueries(e){this._parent&&this._parent.optimisePagingFeatureQueries(e)}_hasMemorySource(){return!0}prop(e,t){return void 0===t?this[e]:(void 0!==this[e]&&(this[e]=t),this)}end(){return null!==this._parent&&!0===this._parent._transparent?this._parent.end():this._parent}_ensureLoaded(){return this.load()}load(){return null===this._loadPromise&&(this._loadPromise=this.loadImpl()),this._loadPromise}async loadImpl(){return!0===this._parent?.loaded?(this._initialiseFeatureSet(),this):(await(this._parent?.load()),this._initialiseFeatureSet(),this)}_initialiseFeatureSet(){null!==this._parent?(this.fields=this._parent.fields.slice(0),this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types):(this.fields=[],this.typeIdField="",this.objectIdField="",this.globalIdField="",this.spatialReference=new _.Z({wkid:4326}),this.geometryType=o.Qk.point)}getField(e,t){let n;return(t=t||this.fields)&&(e=e.toLowerCase(),t.some((t=>(t&&t.name.toLowerCase()===e&&(n=t),!!n)))),n}getFieldsIndex(){return null===this._fieldsIndex&&(this._fieldsIndex=new p.Z(this.fields)),this._fieldsIndex}_maxProcessingRate(){return null!==this._parent?Math.min(this._maxProcessing,this._parent._maxProcessingRate()):Math.min(this._maxProcessing,this._maxQueryRate())}_maxQueryRate(){return null!==this._parent?Math.max(this._maxQuery,this._parent._maxQueryRate()):this._maxQuery}_checkCancelled(e){if(null!=e&&e.aborted)throw new s.EN(s.H9.Cancelled)}nativeCapabilities(){return this._parent.nativeCapabilities()}async _canDoAggregates(e,t,n,r,a){return null!==this._parent&&this._parent._canDoAggregates(e,t,n,r,a)}async _getAggregatePagesDataSourceDefinition(e,t,n,r,a,i,l){if(null===this._parent)throw new s.EN(s.H9.NeverReach);return this._parent._getAggregatePagesDataSourceDefinition(e,t,n,r,a,i,l)}async _getAgregagtePhysicalPage(e,t,n){if(null===this._parent)throw new s.EN(s.H9.NeverReach);return this._parent._getAgregagtePhysicalPage(e,t,n)}async databaseType(){if(this._databaseType===o.Bj.NotEvaluated){if(null!==a.Z.applicationCache){const e=a.Z.applicationCache.getDatabaseType(this._cacheableFeatureSetSourceKey());if(null!==e)return e}if(null!==this._databaseTypeProbed)return this._databaseTypeProbed;try{this._databaseTypeProbed=this._getDatabaseTypeImpl(),null!==a.Z.applicationCache&&a.Z.applicationCache.setDatabaseType(this._cacheableFeatureSetSourceKey(),this._databaseTypeProbed)}catch(e){throw null!==a.Z.applicationCache&&a.Z.applicationCache.clearDatabaseType(this._cacheableFeatureSetSourceKey()),e}return this._databaseTypeProbed}return this._databaseType}async _getDatabaseTypeImpl(){const e=[{thetype:o.Bj.SqlServer,testwhere:"(CAST( '2015-01-01' as DATETIME) = CAST( '2015-01-01' as DATETIME)) AND OBJECTID<0"},{thetype:o.Bj.Oracle,testwhere:"(TO_DATE('2003-11-18','YYYY-MM-DD') = TO_DATE('2003-11-18','YYYY-MM-DD')) AND OBJECTID<0"},{thetype:o.Bj.StandardisedNoInterval,testwhere:"(date '2015-01-01 10:10:10' = date '2015-01-01 10:10:10') AND OBJECTID<0"}];for(const t of e)if(!0===await this._runDatabaseProbe(t.testwhere))return t.thetype;return o.Bj.StandardisedNoInterval}_cacheableFeatureSetSourceKey(){return"MUSTBESET"}async _runDatabaseProbe(e){if(null!==this._parent)return this._parent._runDatabaseProbe(e);throw new s.EN(s.H9.NotImplemented)}isTable(){return this._parent?.isTable()??!1}_featureFromCache(e){if(void 0!==this._featureCache[e])return this._featureCache[e]}_isInFeatureSet(e){return o.dj.Unknown}_getSet(e){throw new s.EN(s.H9.NotImplemented)}async _getFeature(e,t,n){if(this._checkCancelled(n),void 0!==this._featureFromCache(t))return this._featureFromCache(t);if(await this._getFeatures(e,t,this._maxProcessingRate(),n),this._checkCancelled(n),void 0!==this._featureFromCache(t))return this._featureFromCache(t);throw new s.EN(s.H9.MissingFeatures)}async _getFeatureBatch(e,t){this._checkCancelled(t);const n=new u.Z([],e,!1,null),r=[];await this._getFeatures(n,-1,e.length,t),this._checkCancelled(t);for(const t of e)void 0!==this._featureFromCache(t)&&r.push(this._featureFromCache(t));return r}async _getFeatures(e,t,n,r){return"success"}_getFilteredSet(e,t,n,r,a){throw new s.EN(s.H9.NotImplemented)}async _refineSetBlock(e,t,n){if(!0===this._checkIfNeedToExpandCandidatePage(e,this._maxQueryRate()))return await this._expandPagedSet(e,this._maxQueryRate(),0,0,n),this._refineSetBlock(e,t,n);this._checkCancelled(n);const r=e._candidates.length;this._refineKnowns(e,t);let a=r-e._candidates.length;if(0===e._candidates.length)return e;if(a>=t)return e;if(await this._refineIfParentKnown(e,t-a,n),this._checkCancelled(n),this._refineKnowns(e,t-a),a=r-e._candidates.length,a<t&&e._candidates.length>0){const r=t-a,s=this._prepareFetchAndRefineSet(e._candidates);return await this._fetchAndRefineFeatures(s,s.length>r?r:e._candidates.length,n),this._checkCancelled(n),this._refineKnowns(e,t-a),e}return e}_fetchAndRefineFeatures(e,t,n){return null}_prepareFetchAndRefineSet(e){const t=[];for(let n=0;n<e.length;n++)this._isPhysicalFeature(e[n])&&t.push(e[n]);return t}_isPhysicalFeature(e){return null===this._parent||this._parent._isPhysicalFeature(e)}_refineKnowns(e,t){let n=0,r=null;const a=[];t=this._maxQueryRate();for(let s=0;s<e._candidates.length&&"GETPAGES"!==e._candidates[s];s++){let i=!1;const l=this._candidateIdTransform(e._candidates[s]);l!==e._candidates[s]&&(i=!0);const u=this._isInFeatureSet(l);if(u===o.dj.InFeatureSet)!0===i?e._known.includes(l)||(e._known.push(l),n+=1):(e._known.push(e._candidates[s]),n+=1),null===r?r={start:s,end:s}:r.end===s-1?r.end=s:(a.push(r),r={start:s,end:s});else if(u===o.dj.NotInFeatureSet)null===r?r={start:s,end:s}:r.end===s-1?r.end=s:(a.push(r),r={start:s,end:s}),n+=1;else if(u===o.dj.Unknown&&(n+=1,!0===e._ordered))break;if(n>=t)break}null!==r&&a.push(r);for(let t=a.length-1;t>=0;t--)e._candidates.splice(a[t].start,a[t].end-a[t].start+1)}_refineIfParentKnown(e,t,n){const r=new u.Z([],[],e._ordered,null);return r._candidates=e._candidates.slice(0),this._parent._refineSetBlock(r,t,n)}_candidateIdTransform(e){return this._parent._candidateIdTransform(e)}_checkIfNeedToExpandKnownPage(e,t){if(null===e.pagesDefinition)return!1;let n=0;for(let r=e._lastFetchedIndex;r<e._known.length;r++){if("GETPAGES"===e._known[r])return!0;if(void 0===this._featureCache[e._known[r]]&&(n+=1,n>=t))break}return!1}_checkIfNeedToExpandCandidatePage(e,t){if(null===e.pagesDefinition)return!1;let n=0;for(let r=0;r<e._candidates.length;r++){if("GETPAGES"===e._candidates[r])return!0;if(n+=1,n>=t)break}return!1}async _expandPagedSet(e,t,n,r,a){if(null===this._parent)throw new s.EN(s.H9.NotImplemented);return this._parent._expandPagedSet(e,t,n,r,a)}async _expandPagedSetFeatureSet(e,t,n,r,a){if(e._known.length>0&&"GETPAGES"===e._known[e._known.length-1]&&(r=1),0===r&&e._candidates.length>0&&"GETPAGES"===e._candidates[e._candidates.length-1]&&(r=2),0===r)return"finished";const s=await this._getPage(e,r,a);return n+s<t?this._expandPagedSet(e,t,n+s,0,a):"success"}async _getPage(e,t,n){const r=1===t?e._known:e._candidates;if(e.pagesDefinition.internal.set.length>e.pagesDefinition.resultOffset||!0===e.pagesDefinition.internal.fullyResolved){r.length=r.length-1;let t=0;for(let n=0;n<e.pagesDefinition.resultRecordCount&&!(e.pagesDefinition.resultOffset+n>=e.pagesDefinition.internal.set.length);n++)r[r.length]=e.pagesDefinition.internal.set[e.pagesDefinition.resultOffset+n],t++;e.pagesDefinition.resultOffset+=t;let n=!1;return!0===e.pagesDefinition.internal.fullyResolved&&e.pagesDefinition.internal.set.length<=e.pagesDefinition.resultOffset&&(n=!0),!1===n&&r.push("GETPAGES"),t}return await this._getPhysicalPage(e,t,n),this._getPage(e,t,n)}_getPhysicalPage(e,t,n){return null}_clonePageDefinition(e){return null===this._parent?null:this._parent._clonePageDefinition(e)}_first(e){return this.iterator(e).next()}first(e){return this._first(e)}async calculateStatistic(e,t,n,r){await this._ensureLoaded();let a=await this._stat(e,t,"",null,null,n,r);return!1===a.calculated&&(a=await this._manualStat(e,t,n,r)),a.result}async _manualStat(e,t,n,r){let a=null;switch(e.toLowerCase()){case"count":return a=await(0,c.QX)(this,r),{calculated:!0,result:a};case"distinct":return a=await(0,c.EB)(this,t,n,r),{calculated:!0,result:a};case"avg":case"mean":return a=await(0,c.J6)(this,t,r),{calculated:!0,result:a};case"stdev":return a=await(0,c.m)(this,t,r),{calculated:!0,result:a};case"variance":return a=await(0,c.CA)(this,t,r),{calculated:!0,result:a};case"sum":return a=await(0,c.Sm)(this,t,r),{calculated:!0,result:a};case"min":return a=await(0,c.VV)(this,t,r),{calculated:!0,result:a};case"max":return a=await(0,c.Fp)(this,t,r),{calculated:!0,result:a};default:return{calculated:!0,result:0}}}async _stat(e,t,n,r,a,s,i){const l=await this._parent._stat(e,t,n,r,a,s,i);return!1===l.calculated?null===a&&""===n&&null===r?this._manualStat(e,t,s,i):{calculated:!1}:l}_unionAllGeomSelf(e){const t=this.iterator(this._defaultTracker(e)),n=[];return new Promise(((e,r)=>{this._unionShapeInBatches(n,t,e,r)}))}_unionAllGeom(e){return new Promise(((t,n)=>{const r=this.iterator(this._defaultTracker(e));this._unionShapeInBatches([],r,t,n)}))}_unionShapeInBatches(e,t,n,r){t.next().then((a=>{try{null!==a&&null!==a.geometry&&e.push(a.geometry),e.length>30||null===a&&e.length>1?(0,f.G0)(e).then((s=>{try{null===a?n(s):(e=[s],this._unionShapeInBatches(e,t,n,r))}catch(e){r(e)}}),r):null===a?1===e.length?n(e[0]):n(null):this._unionShapeInBatches(e,t,n,r)}catch(e){r(e)}}),r)}iterator(e){return new l(this,e)}intersection(e,t=!1){return g._featuresetFunctions.intersection.bind(this)(e,t)}difference(e,t=!1,n=!0){return g._featuresetFunctions.difference.bind(this)(e,t,n)}symmetricDifference(e,t=!1,n=!0){return g._featuresetFunctions.symmetricDifference.bind(this)(e,t,n)}morphShape(e,t,n="unknown",r=null){return g._featuresetFunctions.morphShape.bind(this)(e,t,n,r)}morphShapeAndAttributes(e,t,n="unknown"){return g._featuresetFunctions.morphShapeAndAttributes.bind(this)(e,t,n)}union(e,t=!1){return g._featuresetFunctions.union.bind(this)(e,t)}intersects(e){return g._featuresetFunctions.intersects.bind(this)(e)}envelopeIntersects(e){return g._featuresetFunctions.envelopeIntersects.bind(this)(e)}contains(e){return g._featuresetFunctions.contains.bind(this)(e)}overlaps(e){return g._featuresetFunctions.overlaps.bind(this)(e)}relate(e,t){return g._featuresetFunctions.relate.bind(this)(e,t)}within(e){return g._featuresetFunctions.within.bind(this)(e)}touches(e){return g._featuresetFunctions.touches.bind(this)(e)}top(e){return g._featuresetFunctions.top.bind(this)(e)}crosses(e){return g._featuresetFunctions.crosses.bind(this)(e)}buffer(e,t,n,r=!0){return g._featuresetFunctions.buffer.bind(this)(e,t,n,r)}filter(e,t=null){return g._featuresetFunctions.filter.bind(this)(e,t)}orderBy(e){return g._featuresetFunctions.orderBy.bind(this)(e)}dissolve(e,t){return g._featuresetFunctions.dissolve.bind(this)(e,t)}groupby(e,t){return g._featuresetFunctions.groupby.bind(this)(e,t)}reduce(e,t=null,n){return new Promise(((r,a)=>{this._reduceImpl(this.iterator(this._defaultTracker(n)),e,t,0,r,a,0)}))}_reduceImpl(e,t,n,r,a,s,i){try{if(++i>1e3)return void setTimeout((()=>{i=0,this._reduceImpl(e,t,n,r,a,s,i)}));e.next().then((l=>{try{if(null===l)a(n);else{const u=t(n,l,r,this);(0,h.y8)(u)?u.then((n=>{this._reduceImpl(e,t,n,r+1,a,s,i)}),s):this._reduceImpl(e,t,u,r+1,a,s,i)}}catch(e){s(e)}}),s)}catch(e){s(e)}}removeField(e){return g._featuresetFunctions.removeField.bind(this)(e)}addField(e,t,n=null){return g._featuresetFunctions.addField.bind(this)(e,t,n)}sumArea(e,t=!1,n){const r=(0,o.EI)(e);return this.reduce(((e,n)=>null===n.geometry?0:t?(0,f.Y4)(n.geometry,r).then((t=>e+t)):(0,f.CJ)(n.geometry,r).then((t=>e+t))),0,n)}sumLength(e,t=!1,n){const r=(0,o.Lz)(e);return this.reduce(((e,n)=>null===n.geometry?0:t?(0,f.kQ)(n.geometry,r).then((t=>e+t)):(0,f.sz)(n.geometry,r).then((t=>e+t))),0,n)}_substituteVars(e,t){if(null!==t){const n={};for(const e in t)n[e.toLowerCase()]=t[e];e.parameters=n}}async distinct(e,t=1e3,n=null,r){await this.load();const a=d.WhereClause.create(e,this.getFieldsIndex());return this._substituteVars(a,n),this.calculateStatistic("distinct",a,t,this._defaultTracker(r))}async min(e,t=null,n){await this.load();const r=d.WhereClause.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("min",r,-1,this._defaultTracker(n))}async max(e,t=null,n){await this.load();const r=d.WhereClause.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("max",r,-1,this._defaultTracker(n))}async avg(e,t=null,n){await this.load();const r=d.WhereClause.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("avg",r,-1,this._defaultTracker(n))}async sum(e,t=null,n){await this.load();const r=d.WhereClause.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("sum",r,-1,this._defaultTracker(n))}async stdev(e,t=null,n){await this.load();const r=d.WhereClause.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("stdev",r,-1,this._defaultTracker(n))}async variance(e,t=null,n){await this.load();const r=d.WhereClause.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("variance",r,-1,this._defaultTracker(n))}async count(e){return await this.load(),this.calculateStatistic("count",d.WhereClause.create("1",this.getFieldsIndex()),-1,this._defaultTracker(e))}_defaultTracker(e){return e||{aborted:!1}}forEach(e,t){return new Promise(((n,r)=>{this._forEachImpl(this.iterator(this._defaultTracker(t)),e,this,n,r,0)}))}_forEachImpl(e,t,n,r,a,s){try{if(++s>1e3)return void setTimeout((()=>{s=0,this._forEachImpl(e,t,n,r,a,s)}),0);e.next().then((i=>{try{if(null===i)r(n);else{const l=t(i);null==l?this._forEachImpl(e,t,n,r,a,s):(0,h.y8)(l)?l.then((()=>{try{this._forEachImpl(e,t,n,r,a,s)}catch(e){a(e)}}),a):this._forEachImpl(e,t,n,r,a,s)}}catch(e){a(e)}}),a)}catch(e){a(e)}}convertToJSON(e){const t={layerDefinition:{geometryType:this.geometryType,fields:[]},featureSet:{features:[],geometryType:this.geometryType}};for(let e=0;e<this.fields.length;e++)t.layerDefinition.fields.push((0,o.Sh)(this.fields[e]));return this.reduce(((e,n)=>{const r={geometry:n.geometry&&n.geometry.toJSON(),attributes:{}};for(const e in n.attributes)r.attributes[e]=n.attributes[e];return t.featureSet.features.push(r),1}),0,e).then((()=>t))}castToText(e=!1){return"object, FeatureSet"}queryAttachments(e,t,n,r,a){return this._parent.queryAttachments(e,t,n,r,a)}serviceUrl(){return this._parent.serviceUrl()}subtypes(){return this.typeIdField?{subtypeField:this.typeIdField,subtypes:this.types?this.types.map((e=>({name:e.name,code:e.id}))):[]}:null}relationshipMetaData(){return this._parent.relationshipMetaData()}get gdbVersion(){return this._parent?this._parent.gdbVersion:""}schema(){const e=[];for(const t of this.fields)e.push((0,o.Sh)(t));return{objectIdField:this.objectIdField,globalIdField:this.globalIdField,geometryType:void 0===o.q2[this.geometryType]?"esriGeometryNull":o.q2[this.geometryType],fields:e}}async convertToText(e,t){if("schema"===e)return await this._ensureLoaded(),JSON.stringify(this.schema());if("featureset"===e){await this._ensureLoaded();const e=[];await this.reduce(((t,n)=>{const r={geometry:n.geometry?n.geometry.toJSON():null,attributes:n.attributes};return null!==r.geometry&&r.geometry.spatialReference&&delete r.geometry.spatialReference,e.push(r),1}),0,t);const n=this.schema();return n.features=e,n.spatialReference=this.spatialReference.toJSON(),JSON.stringify(n)}return this.castToText()}getFeatureByObjectId(e,t){return this._parent.getFeatureByObjectId(e,t)}getOwningSystemUrl(){return this._parent.getOwningSystemUrl()}getIdentityUser(){return this._parent.getIdentityUser()}getRootFeatureSet(){return null!==this._parent?this._parent.getRootFeatureSet():this}getDataSourceFeatureSet(){return null!==this._parent?this._parent.getDataSourceFeatureSet():this}castAsJson(e=null){return"keeptype"===e?.featureset?this:"none"===e?.featureset?null:{type:"FeatureSet"}}async castAsJsonAsync(e=null,t=null){if("keeptype"===t?.featureset)return this;if("schema"===t?.featureset)return await this._ensureLoaded(),JSON.parse(JSON.stringify(this.schema()));if("none"===t?.featureset)return null;await this._ensureLoaded();const n=[];await this.reduce(((e,r)=>{const a={geometry:r.geometry?!0===t?.keepGeometryType?r.geometry:r.geometry.toJSON():null,attributes:r.attributes};return null!==a.geometry&&a.geometry.spatialReference&&!0!==t?.keepGeometryType&&delete a.geometry.spatialReference,n.push(a),1}),0,e);const r=this.schema();return r.features=n,r.spatialReference=!0===t?.keepGeometryType?this.spatialReference:this.spatialReference?.toJSON(),r}get dateTimeReferenceFieldIndex(){return null===this._dateFieldIndex&&(this._dateFieldIndex=r.nu.create(this.getFieldsIndex(),this)),this._dateFieldIndex}fieldTimeZone(e){return this.dateTimeReferenceFieldIndex.fieldTimeZone(e)}get preferredTimeReference(){return this._parent?.preferredTimeReference??null}get dateFieldsTimeReference(){return this._parent?.dateFieldsTimeReference??null}get datesInUnknownTimezone(){return this._parent.datesInUnknownTimezone}get editFieldsInfo(){return this._parent?.editFieldsInfo??null}get timeInfo(){return this._parent?.timeInfo??null}}g._featuresetFunctions={}},90961:(e,t,n)=>{n.d(t,{Z:()=>r});class r{constructor(e,t,n,r){this._lastFetchedIndex=0,this._ordered=!1,this.pagesDefinition=null,this._candidates=e,this._known=t,this._ordered=n,this.pagesDefinition=r}}},48039:(e,t,n)=>{n.d(t,{Z:()=>r});class r{constructor(){this._databaseTypeMetaData={},this._layerInfo={}}clearDatabaseType(e){void 0===this._databaseTypeMetaData[e]&&delete this._databaseTypeMetaData[e]}getDatabaseType(e){return"MUSTBESET"===e||void 0===this._databaseTypeMetaData[e]?null:this._databaseTypeMetaData[e]}setDatabaseType(e,t){this._databaseTypeMetaData[e]=t}getLayerInfo(e){return void 0===this._layerInfo[e]?null:this._layerInfo[e]}setLayerInfo(e,t){this._layerInfo[e]=t}clearLayerInfo(e){void 0!==this._layerInfo[e]&&delete this._layerInfo[e]}}r.applicationCache=null},36515:(e,t,n)=>{n.d(t,{$e:()=>h,DT:()=>m,TE:()=>E,XF:()=>o,bB:()=>c,fz:()=>f,hq:()=>T,mA:()=>_,oX:()=>p,vR:()=>g,y5:()=>I,zR:()=>u});var r=n(48853),a=n(90658),s=n(41534),i=n(59987),l=(n(85839),n(17126));function u(e,t){return d(e?.parseTree,t,e?.parameters)}function o(e,t,n){return d(e,t,n)}function c(e,t,n,r){return s.WhereClause.create(d(e.parseTree,a.Bj.Standardised,e.parameters,t,n),r)}function h(e,t,n="AND"){return s.WhereClause.create("(("+u(e,a.Bj.Standardised)+")"+n+"("+u(t,a.Bj.Standardised)+"))",e.fieldsIndex)}function d(e,t,n,r=null,s=null,l=null){let u,o,c,h;switch(e.type){case"interval":return E(d(e.value,t,n,r,s,l),e.qualifier,e.op);case"case-expression":{let a=" CASE ";"simple"===e.format&&(a+=d(e.operand,t,n,r,s,l));for(let i=0;i<e.clauses.length;i++)a+=" WHEN "+d(e.clauses[i].operand,t,n,r,s,l)+" THEN "+d(e.clauses[i].value,t,n,r,s,l);return null!==e.else&&(a+=" ELSE "+d(e.else,t,n,r,s,l)),a+=" END ",a}case"parameter":{const r=n[e.value.toLowerCase()];if("string"==typeof r)return"'"+n[e.value.toLowerCase()].toString().replace(/'/g,"''")+"'";if((0,a.J_)(r))return p(r,t,l);if((0,a.NP)(r))return _(r,t,l);if(r instanceof Array){const e=[];for(let n=0;n<r.length;n++)"string"==typeof r[n]?e.push("'"+r[n].toString().replace(/'/g,"''")+"'"):(0,a.J_)(r[n])?e.push(p(r[n],t,l)):(0,a.NP)(r[n])?e.push(_(r[n],t,l)):e.push(r[n].toString());return e}return r.toString()}case"expression-list":o=[];for(const a of e.value)o.push(d(a,t,n,r,s,l));return o;case"unary-expression":return" ( NOT "+d(e.expr,t,n,r,s,l)+" ) ";case"binary-expression":switch(e.operator){case"AND":return" ("+d(e.left,t,n,r,s,l)+" AND "+d(e.right,t,n,r,s,l)+") ";case"OR":return" ("+d(e.left,t,n,r,s,l)+" OR "+d(e.right,t,n,r,s,l)+") ";case"IS":if("null"!==e.right.type)throw new i.eS(i.f.UnsupportedIsRhs);return" ("+d(e.left,t,n,r,s,l)+" IS NULL )";case"ISNOT":if("null"!==e.right.type)throw new i.eS(i.f.UnsupportedIsRhs);return" ("+d(e.left,t,n,r,s,l)+" IS NOT NULL )";case"IN":return u=[],"expression-list"===e.right.type?(u=d(e.right,t,n,r,s)," ("+d(e.left,t,n,r,s,l)+" IN ("+u.join(",")+")) "):(h=d(e.right,t,n,r,s,l),h instanceof Array?" ("+d(e.left,t,n,r,s,l)+" IN ("+h.join(",")+")) ":" ("+d(e.left,t,n,r,s,l)+" IN ("+h+")) ");case"NOT IN":return u=[],"expression-list"===e.right.type?(u=d(e.right,t,n,r,s)," ("+d(e.left,t,n,r,s,l)+" NOT IN ("+u.join(",")+")) "):(h=d(e.right,t,n,r,s,l),h instanceof Array?" ("+d(e.left,t,n,r,s,l)+" NOT IN ("+h.join(",")+")) ":" ("+d(e.left,t,n,r,s,l)+" NOT IN ("+h+")) ");case"BETWEEN":return c=d(e.right,t,n,r,s,l)," ("+d(e.left,t,n,r,s,l)+" BETWEEN "+c[0]+" AND "+c[1]+" ) ";case"NOTBETWEEN":return c=d(e.right,t,n,r,s,l)," ("+d(e.left,t,n,r,s,l)+" NOT BETWEEN "+c[0]+" AND "+c[1]+" ) ";case"LIKE":return""!==e.escape?" ("+d(e.left,t,n,r,s,l)+" LIKE "+d(e.right,t,n,r,s,l)+" ESCAPE '"+e.escape+"') ":" ("+d(e.left,t,n,r,s,l)+" LIKE "+d(e.right,t,n,r,s,l)+") ";case"NOT LIKE":return""!==e.escape?" ("+d(e.left,t,n,r,s,l)+" NOT LIKE "+d(e.right,t,n,r,s,l)+" ESCAPE '"+e.escape+"') ":" ("+d(e.left,t,n,r,s,l)+" NOT LIKE "+d(e.right,t,n,r,s,l)+") ";case"<>":case"<":case">":case">=":case"<=":case"=":case"*":case"-":case"+":case"/":return" ("+d(e.left,t,n,r,s,l)+" "+e.operator+" "+d(e.right,t,n,r,s,l)+") ";case"||":return" ("+d(e.left,t,n,r,s,l)+" "+(t===a.Bj.SqlServer?"+":e.operator)+" "+d(e.right,t,n,r,s,l)+") "}throw new i.eS(i.f.UnsupportedOperator,{operator:e.operator});case"null":return"null";case"boolean":return!0===e.value?"1":"0";case"string":return"'"+e.value.toString().replace(/'/g,"''")+"'";case"timestamp":case"date":return p(e.value,t,l);case"number":return e.value.toString();case"current-time":return g("date"===e.mode,t);case"column-reference":return r&&r.toLowerCase()===e.column.toLowerCase()?"("+s+")":e.column;case"data-type":return e.value;case"function":{const a=d(e.args,t,n,r,s,l);return f(e.name,a,t)}}throw new i.eS(i.f.UnsupportedSyntax,{node:e.type})}function f(e,t,n){switch(e.toLowerCase().trim()){case"cos":case"sin":case"tan":case"cosh":case"tanh":case"sinh":case"acos":case"asin":case"atan":case"floor":case"log10":case"log":case"abs":if(1!==t.length)throw new i.eS(i.f.InvalidFunctionParameters,{function:e.toLowerCase().trim()});return`${e.toUpperCase().trim()}(${t[0]})`;case"ceiling":case"ceil":if(1!==t.length)throw new i.eS(i.f.InvalidFunctionParameters,{function:"ceiling"});switch(n){case a.Bj.Standardised:case a.Bj.StandardisedNoInterval:}return"CEILING("+t[0]+")";case"mod":case"power":case"nullif":if(2!==t.length)throw new i.eS(i.f.InvalidFunctionParameters,{function:e.toLowerCase().trim()});return`${e.toUpperCase().trim()}(${t[0]},${t[1]})`;case"round":if(2===t.length)return"ROUND("+t[0]+","+t[1]+")";if(1===t.length)return"ROUND("+t[0]+")";throw new i.eS(i.f.InvalidFunctionParameters,{function:"round"});case"truncate":if(t.length<1||t.length>2)throw new i.eS(i.f.InvalidFunctionParameters,{function:"truncate"});return n===a.Bj.SqlServer?"ROUND("+t[0]+(1===t.length?"0":","+t[1])+",1)":"TRUNCATE("+t[0]+(1===t.length?")":","+t[1]+")");case"char_length":case"len":if(1!==t.length)throw new i.eS(i.f.InvalidFunctionParameters,{function:"char_length"});switch(n){case a.Bj.SqlServer:return"LEN("+t[0]+")";case a.Bj.Oracle:return"LENGTH("+t[0]+")";default:return"CHAR_LENGTH("+t[0]+")"}case"coalesce":case"concat":{if(t.length<1)throw new i.eS(i.f.InvalidFunctionParameters,{function:e.toLowerCase()});let n=e.toUpperCase().trim()+"(";for(let e=0;e<t.length;e++)0!==e&&(n+=","),n+=t[e];return n+=")",n}case"lower":case"lcase":if(1!==t.length)throw new i.eS(i.f.InvalidFunctionParameters,{function:"lower"});return"LOWER("+t[0]+")";case"upper":case"ucase":if(1!==t.length)throw new i.eS(i.f.InvalidFunctionParameters,{function:"upper"});return"UPPER("+t[0]+")";case"substring":{let e="";switch(n){case a.Bj.Oracle:return e="SUBSTR("+t[0]+","+t[1],3===t.length&&(e+=","+t[2]),e+=")",e;case a.Bj.SqlServer:return e=3===t.length?"SUBSTRING("+t[0]+","+t[1]+","+t[2]+")":"SUBSTRING("+t[0]+",  "+t[1]+", LEN("+t[0]+") - "+t[1]+")",e;default:return e="SUBSTRING("+t[0]+" FROM "+t[1],3===t.length&&(e+=" FOR "+t[2]),e+=")",e}}case"extract":return"EXTRACT("+t[0].replace(/\'/g,"")+" FROM "+t[1]+")";case"cast":{let e="";switch(n){case a.Bj.Oracle:switch(t[1].type){case"date":e="DATE";break;case"float":e="DOUBLE";break;case"integer":e="INTEGER";break;case"real":e="REAL";break;case"smallint":e="SMALLINT";break;case"timestamp":e="TIMESTAMP";break;case"varchar":e="VARCHAR("+t[1].size.toString()+")"}return`CAST(${t[0]} AS ${e})`;case a.Bj.Postgres:switch(t[1].type){case"date":e="DATE";break;case"float":e="DOUBLE PRECISION";break;case"integer":e="INT";break;case"real":e="REAL";break;case"smallint":e="SMALLINT";break;case"timestamp":e="TIMESTAMP";break;case"varchar":e="VARCHAR("+t[1].size.toString()+")"}return`CAST(${t[0]} AS ${e})`;case a.Bj.SqlServer:switch(t[1].type){case"date":e="DATE";break;case"float":e="FLOAT";break;case"integer":e="INT";break;case"real":e="REAL";break;case"smallint":e="SMALLINT";break;case"timestamp":e="DATETIME";break;case"varchar":e="VARCHAR("+t[1].size.toString()+")"}return`CAST(${t[0]} AS ${e})`;default:switch(t[1].type){case"date":e="DATE";break;case"float":e="FLOAT";break;case"integer":e="INTEGER";break;case"real":e="REAL";break;case"smallint":e="SMALLINT";break;case"timestamp":e="TIMESTAMP";break;case"varchar":e="VARCHAR("+t[1].size.toString()+")"}return`CAST(${t[0]} AS ${e})`}}}throw new i.eS(i.f.InvalidFunctionParameters,{function:e})}function _(e,t,n,s){n?.outputTimeReference&&(e=r.iG.arcadeDateAndZoneToArcadeDate(e,n?.outputTimeReference));const i=e.toDateTime(),l=0===i.hour&&0===i.minute&&0===i.second&&0===i.millisecond;switch(t){case a.Bj.FILEGDB:case a.Bj.Standardised:case a.Bj.StandardisedNoInterval:return l?`date '${i.toFormat("yyyy-LL-dd")}'`:`date '${i.toFormat("yyyy-LL-dd HH:mm:ss")}'`;case a.Bj.Oracle:return l?`TO_DATE('${i.toFormat("yyyy-LL-dd")}','YYYY-MM-DD')`:`TO_DATE('${i.toFormat("yyyy-LL-dd HH:mm:ss")}','YYYY-MM-DD HH24:MI:SS')`;case a.Bj.SqlServer:return`'${i.toFormat(l?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;case a.Bj.PGDB:return`#${i.toFormat(l?"LL-dd-yyyy":"LL-dd-yyyy HH:mm:ss")}#`;case a.Bj.Postgres:return`TIMESTAMP '${i.toFormat(l?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;default:return`date '${i.toFormat("yyyy-LL-dd HH:mm:ss")}'`}}function p(e,t,n,r){let s=(0,a.J_)(e)?l.ou.fromJSDate(e):l.ou.fromSQL(e);const i=0===s.hour&&0===s.minute&&0===s.second&&0===s.millisecond;switch(n?.inputTimeReference&&(s=l.ou.fromObject({day:s.day,year:s.year,month:s.month,hour:s.hour,minute:s.minute,second:s.second,millisecond:s.millisecond},{zone:n.inputTimeReference})),n?.outputTimeReference&&(s=s.setZone(n.outputTimeReference)),t){case a.Bj.FILEGDB:case a.Bj.Standardised:case a.Bj.StandardisedNoInterval:return i?`date '${s.toFormat("yyyy-LL-dd")}'`:`date '${s.toFormat("yyyy-LL-dd HH:mm:ss")}'`;case a.Bj.Oracle:return i?`TO_DATE('${s.toFormat("yyyy-LL-dd")}','YYYY-MM-DD')`:`TO_DATE('${s.toFormat("yyyy-LL-dd HH:mm:ss")}','YYYY-MM-DD HH24:MI:SS')`;case a.Bj.SqlServer:return`'${s.toFormat(i?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;case a.Bj.PGDB:return`#${s.toFormat(i?"LL-dd-yyyy":"LL-dd-yyyy HH:mm:ss")}#`;case a.Bj.Postgres:return`TIMESTAMP '${s.toFormat(i?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;default:return`date '${s.toFormat("yyyy-LL-dd HH:mm:ss")}'`}}function g(e,t){switch(t){case a.Bj.FILEGDB:case a.Bj.Standardised:case a.Bj.StandardisedNoInterval:case a.Bj.Oracle:return e?"CURRENT_DATE":"CURRENT_TIMESTAMP";case a.Bj.SqlServer:return e?"CAST(GETDATE() AS DATE)":"GETDATE()";case a.Bj.PGDB:case a.Bj.Postgres:default:return e?"CURRENT_DATE":"CURRENT_TIMESTAMP"}}function m(e,t,n={}){const r={},a={},s={esriFieldTypeSmallInteger:"integer",esriFieldTypeInteger:"integer",esriFieldTypeSingle:"double",esriFieldTypeDouble:"double",esriFieldTypeString:"string",esriFieldTypeDate:"date",esriFieldTypeOID:"integer",esriFieldTypeGUID:"guid",esriFieldTypeGlobalID:"guid",oid:"integer",long:"integer","small-integer":"integer",integer:"integer",single:"double",double:"double",date:"date",guid:"guid",globalid:"guid",string:"string"};for(const e of t){const t=e.type?s[e.type]:void 0;r[e.name.toLowerCase()]=void 0===t?"":t}for(const e in n){const t=s[n[e]];a[e.toLowerCase()]=void 0===t?"":t}switch(y(r,e.parseTree,e.parameters,a)){case"double":return"double";case"integer":return"integer";case"date":return"date";case"string":return"string";case"global-id":case"guid":return"guid"}return""}function y(e,t,n,r){let s;switch(t.type){case"interval":return"integer";case"case-expression":{const a=[];if("simple"===t.format){for(let s=0;s<t.clauses.length;s++)a.push(y(e,t.clauses[s].value,n,r));null!==t.else&&a.push(y(e,t.else,n,r))}else{for(let s=0;s<t.clauses.length;s++)a.push(y(e,t.else,n,r));null!==t.else&&a.push(y(e,t.else,n,r))}return w(a)}case"parameter":{const e=r[t.value.toLowerCase()];if(void 0===e&&n){const e=n[t.value.toLowerCase()];if(void 0===e)return"";if(null===e)return"";if("string"==typeof e||e instanceof String)return"string";if("boolean"==typeof e)return"boolean";if((0,a.J_)(e))return"date";if((0,a.NP)(e))return"date";if("number"==typeof e)return e%1==0?"integer":"double"}return void 0===e?"":e}case"expression-list":{const a=[];for(const s of t.value)a.push(y(e,s,n,r));return a}case"unary-expression":return"boolean";case"binary-expression":switch(t.operator){case"AND":case"OR":case"IN":case"NOT IN":case"BETWEEN":case"NOTBETWEEN":case"LIKE":case"NOT LIKE":case"<>":case"<":case">":case">=":case"<=":case"=":return"boolean";case"IS":case"ISNOT":if("null"!==t.right.type)throw new i.eS(i.f.UnsupportedIsRhs);return"boolean";case"*":case"-":case"+":case"/":return w([y(e,t.left,n,r),y(e,t.right,n,r)]);case"||":return"string";default:throw new i.eS(i.f.UnsupportedOperator,{operator:t.operator})}case"null":return"";case"boolean":return"boolean";case"string":return"string";case"number":return null===t.value?"":t.value%1==0?"integer":"double";case"date":case"timestamp":case"current-time":return"date";case"column-reference":{const n=e[t.column.toLowerCase()];return void 0===n?"":n}case"function":switch(t.name.toLowerCase()){case"cast":switch(t.args?.value[1]?.value.type??""){case"integer":case"smallint":return"integer";case"real":case"float":return"double";case"date":case"timestamp":return"date";case"varchar":return"string";default:return""}case"position":case"extract":case"char_length":case"mod":return"integer";case"round":if(s=y(e,t.args,n,r),s instanceof Array){if(s.length<=0)return"double";s=s[0]}return s;case"sign":return"integer";case"ceiling":case"floor":case"abs":return s=y(e,t.args,n,r),s instanceof Array&&(s=w(s)),"integer"===s||"double"===s?s:"double";case"area":case"length":case"log":case"log10":case"sin":case"cos":case"tan":case"asin":case"acos":case"atan":case"cosh":case"sinh":case"tanh":case"power":return"double";case"substring":case"trim":case"concat":case"lower":case"upper":return"string";case"truncate":return"double";case"nullif":case"coalesce":return s=y(e,t.args,n,r),s instanceof Array?s.length>0?s[0]:"":s}return""}throw new i.eS(i.f.UnsupportedSyntax,{node:t.type})}const S={boolean:1,string:2,integer:3,double:4,date:5};function w(e){if(e){let t="";for(const n of e)""!==n&&(t=""===t||S[t]<S[n]?n:t);return t}return""}function T(e,t){return F(e.parseTree,t)}function I(e){return"column-reference"===e?.parseTree.type}function F(e,t){if(null==e)return!1;switch(e.type){case"when-clause":return F(e.operand,t)||F(e.value,t);case"case-expression":for(const n of e.clauses)if(F(n,t))return!0;return!("simple"!==e.format||!F(e.operand,t))||!(null===e.else||!F(e.else,t));case"parameter":case"null":case"boolean":case"date":case"timestamp":case"string":case"number":return!1;case"expression-list":for(const n of e.value)if(F(n,t))return!0;return!1;case"unary-expression":return F(e.expr,t);case"binary-expression":return F(e.left,t)||F(e.right,t);case"column-reference":return t.toLowerCase()===e.column.toLowerCase();case"function":return F(e.args,t)}return!1}function b(e){let t="";return t+=e.period.toUpperCase(),t}function E(e,t,n){let r="";return r="interval-period"===t.type?b(t):b(t.start)+" TO "+b(t.end),"INTERVAL "+n+" "+e+" "+r}},95002:(e,t,n)=>{n.d(t,{CA:()=>p,EB:()=>w,Fp:()=>f,J6:()=>_,QX:()=>y,Sm:()=>m,VV:()=>d,g3:()=>c,m:()=>g,tj:()=>h});var r=n(59987),a=n(90658),s=n(36515);function i(e){let t=0;for(let n=0;n<e.length;n++)t+=e[n];return t/e.length}function l(e){const t=i(e);let n=0;for(let r=0;r<e.length;r++)n+=(t-e[r])**2;return n/e.length}function u(e){const t=i(e);let n=0;for(let r=0;r<e.length;r++)n+=(t-e[r])**2;return n/(e.length-1)}function o(e){let t=0;for(let n=0;n<e.length;n++)t+=e[n];return t}function c(e){switch(e.toLowerCase()){case"distinct":return"distinct";case"avg":case"mean":return"avg";case"min":return"min";case"sum":return"sum";case"max":return"max";case"stdev":case"stddev":return"stddev";case"var":case"variance":return"var";case"count":return"count"}return""}function h(e,t,n=1e3){switch(e.toLowerCase()){case"distinct":return function(e,t){const n=[],r={},s=[];for(let i=0;i<e.length;i++){if(void 0!==e[i]&&null!==e[i]){const t=e[i];if((0,a.hj)(t)||(0,a.HD)(t))void 0===r[t]&&(n.push(t),r[t]=1);else{let e=!1;for(let n=0;n<s.length;n++)!0===(0,a.tt)(s[n],t)&&(e=!0);!1===e&&(s.push(t),n.push(t))}}if(n.length>=t&&-1!==t)return n}return n}(t,n);case"avg":case"mean":return i(t);case"min":return Math.min.apply(Math,t);case"sum":return o(t);case"max":return Math.max.apply(Math,t);case"stdev":case"stddev":return Math.sqrt(l(t));case"var":case"variance":return l(t);case"count":return t.length}return 0}async function d(e,t,n){const r=await S(e,t,n,!0);return 0===r.length?null:Math.min.apply(Math,r)}async function f(e,t,n){const r=await S(e,t,n,!0);return 0===r.length?null:Math.max.apply(Math,r)}async function _(e,t,n){let r="";t&&!(0,s.y5)(t)&&(r=(0,s.DT)(t,e.fields));const a=await S(e,t,n,!0);if(0===a.length)return null;const l=i(a);return null===l?l:"integer"===r?function(e){return e=+e,isFinite(e)?e-e%1||(e<0?-0:0===e?e:0):e}(l):l}async function p(e,t,n){const r=await S(e,t,n,!0);return 0===r.length?null:u(r)}async function g(e,t,n){const r=await S(e,t,n,!0);return 0===r.length?null:Math.sqrt(u(r))}async function m(e,t,n){const r=await S(e,t,n,!0);return 0===r.length?null:o(r)}async function y(e,t){return e.iterator(t).count()}async function S(e,t,n,a=!1){const s=e.iterator(n),i=[],l={ticker:0};let u=await s.next();for(;null!==u;){if(l.ticker++,n.aborted)throw new r.EN(r.H9.Cancelled);l.ticker%100==0&&(l.ticker=0,await new Promise((e=>{setTimeout(e,0)})));const e=t?.calculateValue(u);null===e?!1===a&&(i[i.length]=e):i[i.length]=e,u=await s.next()}return i}async function w(e,t,n=1e3,a=null){const s=e.iterator(a),i=[],l={},u={ticker:0};let o=await s.next();for(;null!==o;){if(u.ticker++,a&&a.aborted)throw new r.EN(r.H9.Cancelled);u.ticker%100==0&&(u.ticker=0,await new Promise((e=>{setTimeout(e,0)})));const e=t?.calculateValue(o);if(null!=e&&void 0===l[e]&&(i.push(e),l[e]=1),i.length>=n&&-1!==n)return i;o=await s.next()}return i}},99880:(e,t,n)=>{n.d(t,{V:()=>u});var r=n(68773),a=(n(3172),n(20102)),s=n(92604),i=n(17452);const l=s.Z.getLogger("esri.assets");function u(e){if(!r.Z.assetsPath)throw l.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new a.Z("assets:path-not-set","config.assetsPath is not set");return(0,i.v_)(r.Z.assetsPath,e)}},8667:(e,t,n)=>{n.d(t,{CJ:()=>B,Cz:()=>k,D$:()=>x,ED:()=>S,G0:()=>R,Gg:()=>I,JI:()=>b,LV:()=>T,Nm:()=>w,Sp:()=>v,TE:()=>_,U1:()=>C,W4:()=>m,Y4:()=>G,cv:()=>D,e5:()=>E,f3:()=>L,fS:()=>p,j2:()=>P,jU:()=>f,kK:()=>g,kQ:()=>j,og:()=>F,oq:()=>c,r3:()=>d,rd:()=>N,sz:()=>O,uh:()=>y,wf:()=>A,z7:()=>h}),n(66577);var r=n(78346),a=(n(94139),n(33955));function s(e){return Array.isArray(e)?e[0]?.spatialReference:e?.spatialReference}function i(e){return e?Array.isArray(e)?e.map(i):e.toJSON?e.toJSON():e:e}function l(e){return Array.isArray(e)?e.map((e=>(0,a.im)(e))):(0,a.im)(e)}let u;async function o(e,t){return(await async function(){return u||(u=(0,r.bA)("geometryEngineWorker",{strategy:"distributed"})),u}()).invoke("executeGEOperation",{operation:e,parameters:i(t)})}async function c(e,t){return l(await o("clip",[s(e),e,t]))}async function h(e,t){return l(await o("cut",[s(e),e,t]))}function d(e,t){return o("contains",[s(e),e,t])}function f(e,t){return o("crosses",[s(e),e,t])}function _(e,t,n){return o("distance",[s(e),e,t,n])}function p(e,t){return o("equals",[s(e),e,t])}function g(e,t){return o("intersects",[s(e),e,t])}function m(e,t){return o("touches",[s(e),e,t])}function y(e,t){return o("within",[s(e),e,t])}function S(e,t){return o("disjoint",[s(e),e,t])}function w(e,t){return o("overlaps",[s(e),e,t])}function T(e,t,n){return o("relate",[s(e),e,t,n])}function I(e){return o("isSimple",[s(e),e])}async function F(e){return l(await o("simplify",[s(e),e]))}async function b(e,t=!1){return l(await o("convexHull",[s(e),e,t]))}async function E(e,t){return l(await o("difference",[s(e),e,t]))}async function v(e,t){return l(await o("symmetricDifference",[s(e),e,t]))}async function A(e,t){return l(await o("intersect",[s(e),e,t]))}async function R(e,t=null){const n=function(e,t){let n;return Array.isArray(e)?n=e:(n=[],n.push(e),null!=t&&n.push(t)),n}(e,t);return l(await o("union",[s(n),n]))}async function D(e,t,n,r,a,i){return l(await o("offset",[s(e),e,t,n,r,a,i]))}async function L(e,t,n,r=!1){const a=[s(e),e,t,n,r];return l(await o("buffer",a))}async function N(e,t,n,r,a,i){const u=[s(e),e,t,n,r,a,i];return l(await o("geodesicBuffer",u))}async function C(e,t,n){if(null==e)throw new M;const r=e.spatialReference;if(null==(n=n??function(e){return"xmin"in e?e.center:"x"in e?e:e.extent?.center}(e)))throw new M;const a=e.constructor.fromJSON(await o("rotate",[r,e,t,n]));return a.spatialReference=r,a}async function x(e,t,n,r){return l(await o("generalize",[s(e),e,t,n,r]))}async function k(e,t,n){return l(await o("densify",[s(e),e,t,n]))}async function P(e,t,n,r=0){return l(await o("geodesicDensify",[s(e),e,t,n,r]))}function B(e,t){return o("planarArea",[s(e),e,t])}function O(e,t){return o("planarLength",[s(e),e,t])}function G(e,t,n){return o("geodesicArea",[s(e),e,t,n])}function j(e,t,n){return o("geodesicLength",[s(e),e,t,n])}class M extends Error{constructor(){super("Illegal Argument Exception")}}},94443:(e,t,n)=>{n.d(t,{ME:()=>f,Su:()=>_,tz:()=>d});var r=n(20102),a=n(95330),s=n(70171);const i=/^([a-z]{2})(?:[-_]([A-Za-z]{2}))?$/,l={ar:!0,bg:!0,bs:!0,ca:!0,cs:!0,da:!0,de:!0,el:!0,en:!0,es:!0,et:!0,fi:!0,fr:!0,he:!0,hr:!0,hu:!0,id:!0,it:!0,ja:!0,ko:!0,lt:!0,lv:!0,nb:!0,nl:!0,pl:!0,"pt-BR":!0,"pt-PT":!0,ro:!0,ru:!0,sk:!0,sl:!0,sr:!0,sv:!0,th:!0,tr:!0,uk:!0,vi:!0,"zh-CN":!0,"zh-HK":!0,"zh-TW":!0};function u(e){return l[e]??!1}const o=[],c=new Map;function h(e){for(const t of c.keys())p(e.pattern,t)&&c.delete(t)}function d(e){return o.includes(e)||(h(e),o.unshift(e)),{remove(){const t=o.indexOf(e);t>-1&&(o.splice(t,1),h(e))}}}async function f(e){const t=(0,s.Kd)();c.has(e)||c.set(e,async function(e,t){const n=[];for(const r of o)if(p(r.pattern,e))try{return await r.fetchMessageBundle(e,t)}catch(e){n.push(e)}if(n.length)throw new r.Z("intl:message-bundle-error",`Errors occurred while loading "${e}"`,{errors:n});throw new r.Z("intl:no-message-bundle-loader",`No loader found for message bundle "${e}"`)}(e,t));const n=c.get(e);return n&&await g.add(n),n}function _(e){if(!i.test(e))return null;const t=i.exec(e);if(null===t)return null;const[,n,r]=t,a=n+(r?"-"+r.toUpperCase():"");return u(a)?a:u(n)?n:null}function p(e,t){return"string"==typeof e?t.startsWith(e):e.test(t)}(0,s.Ze)((()=>{c.clear()}));const g=new class{constructor(){this._numLoading=0,this._dfd=null}async waitForAll(){this._dfd&&await this._dfd.promise}add(e){return this._increase(),e.then((()=>this._decrease()),(()=>this._decrease())),this.waitForAll()}_increase(){this._numLoading++,this._dfd||(this._dfd=(0,a.dD)())}_decrease(){this._numLoading=Math.max(this._numLoading-1,0),this._dfd&&0===this._numLoading&&(this._dfd.resolve(),this._dfd=null)}}}}]);