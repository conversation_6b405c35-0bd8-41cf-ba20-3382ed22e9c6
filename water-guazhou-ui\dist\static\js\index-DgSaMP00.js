import{_ as S}from"./TreeBox-DDD2iwoR.js";import{_ as I}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as M}from"./CardTable-rdWOL4_6.js";import{_ as P}from"./CardSearch-CB_HNR-Q.js";import{_ as R}from"./index-BJ-QPYom.js";import{d as w,M as L,b3 as j,c as m,s as q,r as f,x as g,S as N,o as V,g as Y,h as B,F as _,q as c,i as p,a9 as K,b7 as W}from"./index-r0dFAfgr.js";import{I as l}from"./common-CvK_P_ao.js";import{f as A}from"./DateFormatter-Bm9a68Ax.js";import{c as H}from"./equipmentManage-DuoY00aj.js";import{p as O,d as $,g as U}from"./malfunctionRepair-CM_eL_AA.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const le=w({__name:"index",setup(z){const{$btnPerms:d}=L(),h=j(),s=m(),u=m(),b=m({filters:[{label:"故障类别名称",field:"name",type:"input",labelWidth:"100px"},{type:"btn-group",btns:[{perm:!0,text:"查询",icon:l.QUERY,click:()=>o()},{type:"default",perm:!0,text:"重置",svgIcon:q(W),click:()=>{var e;(e=u.value)==null||e.resetForm(),o()}},{perm:!0,text:"新建",icon:l.ADD,type:"success",click:()=>x("新建")}]}]}),a=f({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"类别名称",prop:"name"},{label:"故障类别说明",prop:"remark"},{label:"添加人",prop:"creatorName"},{label:"添加时间",prop:"createTime",formatter:e=>A(e.createTime,"YYYY-MM-DD HH:mm")}],operationWidth:"300px",operations:[{type:"primary",text:"编辑",icon:l.EDIT,perm:d("RoleManageEdit"),click:e=>D(e)},{type:"primary",text:"故障信息",icon:l.DETAIL,perm:d("RoleManageEdit"),click:e=>y(e)},{type:"danger",text:"删除",perm:d("RoleManageDelete"),icon:l.DELETE,click:e=>k(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,o()}}}),r=f({title:"新增",labelWidth:"120px",dialogWidth:"500px",submit:e=>{r.submitting=!0,e.serialId=i.currentProject.serialId;let t="新增成功";e.id&&(t="修改成功"),O(e).then(()=>{var n;r.submitting=!1,g.success(t),o(),(n=s.value)==null||n.closeDialog()})},defaultValue:{},group:[{fields:[{type:"textarea",label:"故障类别名称",field:"name",rules:[{required:!0,message:"请输入故障类别名称"}]},{type:"textarea",label:"故障类别说明",field:"remark",rules:[{required:!0,message:"请输入故障类别说明"}]}]}]}),i=f({title:" ",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{i.currentProject=e,o()}}),x=e=>{var t;r.title=e,r.defaultValue={},(t=s.value)==null||t.openDialog()},D=e=>{var t;r.title="编辑",r.defaultValue={...e||{}},(t=s.value)==null||t.openDialog()},y=e=>{h.push({name:"knowledgeBaseDetail",query:{id:e.id,title:e.name}})},k=e=>{N("确定删除指定故障类别?","删除提示").then(()=>{$([e.id]).then(()=>{g.success("删除成功"),o()})})};function T(){H().then(e=>{i.data=K(e.data.data||[]),i.currentProject=e.data.data[0],o()})}function o(){var t;const e={size:a.pagination.limit,page:a.pagination.page,deviceTypeId:i.currentProject.id,...((t=u.value)==null?void 0:t.queryParams)||{}};U(e).then(n=>{a.dataList=n.data.data.data||[],a.pagination.total=n.data.data.total||0})}return V(async()=>{T()}),(e,t)=>{const n=R,C=P,v=M,E=I,F=S;return Y(),B(F,null,{tree:_(()=>[c(n,{"tree-data":p(i)},null,8,["tree-data"])]),default:_(()=>[c(C,{ref_key:"refSearch",ref:u,config:p(b)},null,8,["config"]),c(v,{config:p(a),class:"card-table"},null,8,["config"]),c(E,{ref_key:"refForm",ref:s,config:p(r)},null,8,["config"])]),_:1})}}});export{le as default};
