<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.largeScreen.LargeScreenConfigMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.largeScreen.LargeScreenConfig">
        select * from tb_large_screen_config
        <where>
            tenant_id = #{param.tenantId}
            <if test="param.name != null and param.name != ''">
                and name like '%'||#{param.name}||'%'
            </if>
            <if test="param.type != null and param.type != ''">
                and type like '%'||#{param.type}||'%'
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>