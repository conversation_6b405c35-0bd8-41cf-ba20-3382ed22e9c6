package org.thingsboard.server.dao.guard;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrange;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangeQuickArrangeRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangeSaveRequest;

import java.util.List;
import java.util.Map;

public interface GuardArrangeService {
    GuardArrange findById(String id);

    Map<String, List<GuardArrange>> findAllConditional(GuardArrangePageRequest request);

    GuardArrange save(GuardArrangeSaveRequest entity);

    boolean delete(String id);

    boolean quickArrange(GuardArrangeQuickArrangeRequest req, String userId, String tenantId);

    boolean detectArrangeOverride(GuardArrangeQuickArrangeRequest req);

    GuardArrange getCurrentGuard(String tenantId);
}
