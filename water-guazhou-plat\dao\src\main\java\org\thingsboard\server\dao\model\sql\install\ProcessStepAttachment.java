package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 报装流程类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_install_process_step_attachment")
@Data
public class ProcessStepAttachment {
    @TableId
    private String id;

    private String mainId;

    private transient String mainName;

    private String stepId;

    private transient String stepName;

    private String code;

    private String name;

    private Integer num;

    private String remark;

    private String tenantId;

}
