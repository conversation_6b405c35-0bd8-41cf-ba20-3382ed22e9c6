package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectSettlement;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectSettlementPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectSettlementSaveRequest;

public interface SoProjectSettlementService {
    /**
     * 分页条件查询项目结算信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoProjectSettlement> findAllConditional(SoProjectSettlementPageRequest request);

    /**
     * 保存项目结算信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoProjectSettlement save(SoProjectSettlementSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoProjectSettlement entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
