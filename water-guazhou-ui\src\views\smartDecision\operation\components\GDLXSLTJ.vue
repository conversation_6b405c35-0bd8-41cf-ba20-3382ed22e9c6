<template>
  <VChart :option="state.workorderTypeOption"></VChart>
</template>
<script lang="ts" setup>
import { GetWorkOrderCountStatistic } from '@/api/workorder'

const state = reactive<{
  workorderTypeOption: any
}>({
  workorderTypeOption: null
})

const generateWorkorderTypeOption = (data: { value: number; name: string }[]) => {
  return {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 'center',
      pageIconColor: '#fff',
      pageTextStyle: { color: '#fff' },
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 100,
            fontSize: 12
          },
          value: {
            align: 'right',
            width: 40,
            fontSize: 12,
            color: '#00ff00'
          }
        }
      },
      data: data.map(item => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            if (name === data[i].name) {
              return '{name| ' + name + '}' + '{value| ' + data[i].value + '}'
            }
          }
        }
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '70%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        // emphasis: {
        //   label: {
        //     show: true,
        //     fontSize: 40,
        //     fontWeight: 'bold'
        //   }
        // },
        labelLine: {
          show: false
        },
        data
      }
    ]
  }
}
const refreshData = () => {
  GetWorkOrderCountStatistic({
    fromTime: moment().startOf('y').valueOf(),
    toTime: moment().valueOf(),
    statisticType: true
  }).then(res => {
    const data = res.data?.data?.types?.data || []
    state.workorderTypeOption = generateWorkorderTypeOption(
      data.map(item => {
        return {
          name: item.key,
          value: item.value
        }
      })
    )
  })
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped></style>
