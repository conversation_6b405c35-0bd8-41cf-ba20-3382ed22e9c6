export const filterTime: any[] = [
  { label: '0时', value: 0 },
  { label: '1时', value: 1 },
  { label: '2时', value: 2 },
  { label: '3时', value: 3 },
  { label: '4时', value: 4 },
  { label: '5时', value: 5 },
  { label: '6时', value: 6 },
  { label: '7时', value: 7 },
  { label: '8时', value: 8 },
  { label: '9时', value: 9 },
  { label: '10时', value: 10 },
  { label: '11时', value: 11 },
  { label: '12时', value: 12 },
  { label: '13时', value: 13 },
  { label: '14时', value: 14 },
  { label: '15时', value: 15 },
  { label: '16时', value: 16 },
  { label: '17时', value: 17 },
  { label: '18时', value: 18 },
  { label: '19时', value: 19 },
  { label: '20时', value: 20 },
  { label: '21时', value: 21 },
  { label: '22时', value: 22 },
  { label: '23时', value: 23 }
]
export const detailAttrColumns: any = [
  [
    { label: '监测点名称', prop: 'name', value: '-' },
    { label: '类型', prop: 'type', value: '-' }
  ],
  [
    { label: '安装地址', prop: 'address', value: '-' },
    { label: '经纬度', prop: 'location', value: '-' }
  ],
  [
    { label: '压力', prop: 'pressure', value: '-' }
  ]
]

// columns: [
//   { prop: 'name1', label: '监测点名称', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '读取时间', minWidth: 120, align: 'center', sortable: true, formatter: (row: any, val: any) => { return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '' } },
//   { prop: 'name2', label: 'RTU编号', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '压力', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '瞬时流量', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '累计流量', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '本日流量', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '昨日流量', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '本月流量', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '本年流量', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '电池电压', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '信号强度', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '水表厂家', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '口径', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '安装位置', minWidth: 120, align: 'center', sortable: true },
//   { prop: 'name2', label: '安装时间', minWidth: 120, align: 'center', sortable: true, formatter: (row: any, val: any) => { return val ? dayjs(val).format('YYYY-MM-DD') : '' } }
// ],
