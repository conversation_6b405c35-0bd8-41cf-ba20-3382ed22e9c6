import * as echarts from 'echarts'

function Color() {
  const r = Math.floor(Math.random() * 255)
  const g = Math.floor(Math.random() * 255)
  const b = Math.floor(Math.random() * 255)
  return 'rgb(' + r + ',' + g + ',' + b + ')'
}

// 环形饼图
export function 环形饼图(data: any[] = [{ name: 'cs', value: 10 }]) {
  const color = [
    '#00ffff',
    '#00cfff',
    '#006ced',
    '#ffe000',
    '#ffa800',
    '#ff5b00',
    '#ff3000'
  ]
  for (let i = 0; i < data.length; i++) {
    data[i].label = { color: color[i] }
  }
  const option = {
    tooltip: {
      trigger: 'item'
    },
    color: [
      '#00ffff',
      '#00cfff',
      '#006ced',
      '#ffe000',
      '#ffa800',
      '#ff5b00',
      '#ff3000'
    ],
    series: [
      {
        type: 'pie',
        radius: ['25%', '45%'],
        data
      }
    ]
  }
  return option
}

//
export function 单色渐变柱状图(Data: any = { data: [], xdata: [] }) {
  const option = {
    color: [
      new echarts.graphic.LinearGradient(
        0,
        1,
        0,
        0,
        [
          {
            offset: 0,
            color: '#50f5c5'
          },
          {
            offset: 1,
            color: '#eefc08'
          }
        ],
        false
      )
    ],
    grid: {
      left: '5%',
      right: '0%',
      top: '10%',
      bottom: '5%',
      containLabel: true
    },
    tooltip: {
      show: 'true',
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    yAxis: {
      type: 'value',
      axisTick: {
        show: true
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#ccc'
        }
      },
      axisLabel: {
        show: true,
        color: '#ccc',
        fontSize: 18
      },
      splitLine: {
        show: false
      }
    },
    xAxis: [
      {
        axisTick: {
          show: false
        },

        type: 'category',
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true,
          color: '#ccc',
          interval: 0
        },

        splitLine: {
          show: false,
          lineStyle: {
            type: 'dashed'
          }
        },
        data: Data.xdata
      }
    ],
    series: [
      {
        name: '数量',
        type: 'bar',
        barWidth: 15,
        label: {
          normal: {
            show: false,
            position: 'right',
            textStyle: {
              color: '#ccc',
              fontSize: 14
            }
          }
        },
        data: Data.data
      }
    ]
  }
  return option
}

export function 两层圆环(
  value: any[] = [
    {
      name: '测试',
      value: 20
    }
  ],
  title?: string
) {
  const img = 'data:image/png;base64,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'

  const data = [] as any[]
  const color = [
    '#00ffff',
    '#00cfff',
    '#006ced',
    '#ffe000',
    '#ffa800',
    '#ff5b00',
    '#ff3000'
  ]
  for (let i = 0; i < value.length; i++) {
    data.push(
      {
        value: value[i].value,
        name: value[i].name,
        itemStyle: {
          normal: {
            borderWidth: 5,
            shadowBlur: 20,
            borderColor: color[i],
            shadowColor: color[i]
          }
        },
        label: {
          color: color[i]
        }
      },
      {
        value: 2,
        name: '',
        itemStyle: {
          normal: {
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            color: 'rgba(0, 0, 0, 0)',
            borderColor: 'rgba(0, 0, 0, 0)',
            borderWidth: 0
          }
        }
      }
    )
  }
  const seriesOption = [
    {
      name: '',
      type: 'pie',
      clockWise: false,
      radius: ['60%', '65%'],
      hoverAnimation: false,
      itemStyle: {
        normal: {
          label: {
            show: true,
            formatter(params) {
              let percent = 0
              let total = 0
              for (let i = 0; i < value.length; i++) {
                total += value[i].value
              }
              percent = parseInt(((params.value / total) * 100).toFixed(0))
              if (params.name !== '') {
                return (
                  `${params.name}：${percent}%` + `\n` + `\n${params.value}万m³`
                )
              }
              return ''
            }
          }
        }
      },
      data
    }
  ]
  const option = {
    title: {
      text: title || '',
      left: 'center',
      top: 'center',
      textStyle: {
        color: '#fff'
      }
    },
    color,
    graphic: {
      elements: [
        {
          type: 'image',
          z: 3,
          style: {
            image: img,
            width: 100,
            height: 100
          },
          left: 'center',
          top: 'center',
          position: [0, 0]
        }
      ]
    },
    tooltip: {
      show: false
    },
    toolbox: {
      show: false
    },
    series: seriesOption
  }
  return option
}

// 渐变柱状图
export function 渐变柱状图(
  Data: any = { xdata: [], data: [[], [], []], legend: [] }
) {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
      }
    },
    grid: {
      left: 20,
      right: 20,
      bottom: 20,
      top: 30,
      containLabel: true
    },
    legend: {
      type: 'scroll',
      data: Data.legend,
      right: 20,
      top: 0,
      textStyle: {
        color: '#fff'
      },
      itemWidth: 12,
      itemHeight: 10
      // itemGap: 35
    },
    xAxis: {
      type: 'category',
      data: Data.xdata,
      axisLine: {
        lineStyle: {
          color: 'white'
        }
      },
      axisLabel: {
        // interval: 0,
        // rotate: 40,
        textStyle: {
          fontFamily: 'Microsoft YaHei'
        }
      }
    },

    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
        lineStyle: {
          color: 'white'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255,255,255,0.3)'
        }
      },
      axisLabel: {}
    },
    dataZoom: [
      {
        show: true,
        height: 12,
        xAxisIndex: [0],
        bottom: '8%',
        start: 10,
        end: 90,
        handleIcon:
          'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
        handleSize: '110%',
        handleStyle: {
          color: '#d3dee5'
        },
        textStyle: {
          color: '#fff'
        },
        borderColor: '#90979c'
      },
      {
        type: 'inside',
        show: true,
        height: 15,
        start: 1,
        end: 35
      }
    ],
    series: [] as any[]
  }
  Data.data.forEach((item, index) => {
    option.series.push({
      name: Data.legend[index],
      type: 'bar',
      barWidth: '3%',
      itemStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: Color()
            },
            {
              offset: 1,
              color: Color()
            }
          ]),
          barBorderRadius: 12
        }
      },
      data: item
    })
  })

  return option
}

// 饼图
export function 饼图(data: any[] = [{ name: 'cs', value: 10 }]) {
  const color = [
    '#00ffff',
    '#00cfff',
    '#006ced',
    '#ffe000',
    '#ffa800',
    '#ff5b00',
    '#ff3000'
  ]
  for (let i = 0; i < data.length; i++) {
    data[i].label = { color: color[i] }
  }
  const option = {
    tooltip: {
      trigger: 'item'
    },
    color: [
      '#00ffff',
      '#00cfff',
      '#006ced',
      '#ffe000',
      '#ffa800',
      '#ff5b00',
      '#ff3000'
    ],
    series: [
      {
        name: '服务类型',
        type: 'pie',
        radius: '80%',
        data
      }
    ]
  }
  return option
}

// 仪表盘
export function 仪表盘(
  Data = {
    max: 100,
    value: 0,
    title: ''
  }
) {
  const option = {
    title: {
      text:
        parseInt(((Data.value * 100) / Data.max).toString())
        + '%\n'
        + Data.title,
      top: 'center',
      left: 'center',
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        type: 'gauge',
        center: ['50%', '50%'],
        startAngle: 210,
        endAngle: -30,
        min: 0,
        max: Data.max || 100,
        splitNumber: 1,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            {
              offset: 0,
              color: '#198fc0'
            },
            {
              offset: 1,
              color: '#0ec4d6'
            }
          ])
        },
        progress: {
          show: true,
          width: 30
        },
        pointer: {
          show: false
        },
        axisLine: {
          lineStyle: {
            width: 30
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12
        },
        anchor: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          show: false
        },
        data: [
          {
            value: Data.value
          }
        ]
      }
    ]
  }
  return option
}

// 两层圆环非渐变
export function 两层圆环非渐变(
  Data: any = [[{ name: 'cs', value: 12 }], [{ name: 'cs1', value: 22 }]]
) {
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      left: 'right',
      top: 'center',
      orient: 'verticalAlign',
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '人员',
        type: 'pie',
        selectedMode: 'single',
        radius: ['40%', '50%'],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: Data[0]
      },
      {
        name: '人员',
        type: 'pie',
        radius: ['60%', '70%'],
        labelLine: {
          length: 30
        },
        label: {
          show: false
        },
        data: Data[1]
      }
    ]
  }
  return option
}

// 兰丁格尔图
export function 兰丁格尔(data: any = []) {
  const option = {
    legend: {
      top: 'center',
      left: 'right',
      orient: 'verticalAlign',
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        name: 'Nightingale Chart',
        type: 'pie',
        radius: [50, 80],
        center: ['50%', '50%'],
        roseType: 'area',
        label: {
          show: false
        },
        data
      }
    ]
  }
  return option
}

// 柱状3D渐变
export function 柱状3D渐变(
  Data: any = {
    data: [0, 1230, 425],
    xdata: ['本年话务总量', '本年人工话务量', '每万客户呼入量']
  }
) {
  const option = {
    grid: {
      top: '10%',
      left: '5%',
      bottom: '5%',
      right: '5%',
      containLabel: true
    },
    tooltip: {
      show: true
    },
    animation: false,
    xAxis: [
      {
        type: 'category',
        data: Data.xdata,
        axisTick: {
          alignWithLabel: true
        },
        nameTextStyle: {
          color: '#82b0ec'
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#82b0ec'
          }
        },
        axisLabel: {
          textStyle: {
            color: '#fff'
          },
          margin: 30
        }
      }
    ],
    yAxis: [
      {
        show: true,
        type: 'value',
        axisLabel: {
          textStyle: {
            color: '#fff'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#0c2c5a'
          }
        },
        axisLine: {
          show: true
        }
      }
    ],
    series: [
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [20, 10],
        symbolOffset: [0, -6], // 上部椭圆
        symbolPosition: 'end',
        z: 12,
        color: '#2DB1EF',
        data: Data.data
      },
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [20, 10],
        symbolOffset: [0, 7], // 下部椭圆
        // "barWidth": "20",
        z: 12,
        color: '#2DB1EF',
        data: Data.data
      },
      {
        type: 'bar',
        // silent: true,
        barWidth: '20',
        barGap: '10%', // Make series be overlap
        barCateGoryGap: '10%',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [
              {
                offset: 0,
                color: '#38B2E6'
              },
              {
                offset: 1,
                color: '#31c2ff'
              }
            ]),
            opacity: 0.8
          }
        },
        data: Data.data
      }
    ]
  }
  return option
}

// 柱状3D立方体
export function 柱状3D立方体() {
  const CubeLeft = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath(ctx: any, shape) {
      const xAxisPoint = shape.xAxisPoint
      const c0 = [shape.x, shape.y]
      const c1 = [shape.x - 9, shape.y - 9]
      const c2 = [xAxisPoint[0] - 9, xAxisPoint[1] - 9]
      const c3 = [xAxisPoint[0], xAxisPoint[1]]
      ctx
        .moveTo(c0[0], c0[1])
        .lineTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .closePath()
    }
  })
  const CubeRight = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath(ctx: any, shape) {
      const xAxisPoint = shape.xAxisPoint
      const c1 = [shape.x, shape.y]
      const c2 = [xAxisPoint[0], xAxisPoint[1]]
      const c3 = [xAxisPoint[0] + 18, xAxisPoint[1] - 9]
      const c4 = [shape.x + 18, shape.y - 9]
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath()
    }
  })
  const CubeTop = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath(ctx: any, shape) {
      const c1 = [shape.x, shape.y]
      const c2 = [shape.x + 18, shape.y - 9]
      const c3 = [shape.x + 9, shape.y - 18]
      const c4 = [shape.x - 9, shape.y - 9]
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath()
    }
  })
  echarts.graphic.registerShape('CubeLeft', CubeLeft)
  echarts.graphic.registerShape('CubeRight', CubeRight)
  echarts.graphic.registerShape('CubeTop', CubeTop)
  const VALUE = [2012, 1230, 3790, 2349]
  const option = {
    grid: {
      top: '10%',
      left: '5%',
      bottom: '20%',
      right: '5%',
      containLabel: true
    },
    title: {
      text: '',
      top: 32,
      left: 18,
      textStyle: {
        color: '#00F6FF',
        fontSize: 24
      }
    },
    xAxis: {
      type: 'category',
      data: ['德州', '德城区', '陵城区', '禹城市'],
      axisLine: {
        show: false,
        lineStyle: {
          color: 'white'
        }
      },
      axisLabel: {
        fontSize: 10
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      boundaryGap: ['20%', '20%']
    },
    series: [
      {
        type: 'custom',
        renderItem: (params, api): any => {
          const location = api.coord([api.value(0), api.value(1)])
          return {
            type: 'group',
            children: [
              {
                type: 'CubeLeft',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#3B80E2'
                    },
                    {
                      offset: 1,
                      color: '#49BEE5'
                    }
                  ])
                }
              },
              {
                type: 'CubeRight',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#3B80E2'
                    },
                    {
                      offset: 1,
                      color: '#49BEE5'
                    }
                  ])
                }
              },
              {
                type: 'CubeTop',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#3B80E2'
                    },
                    {
                      offset: 1,
                      color: '#49BEE5'
                    }
                  ])
                }
              }
            ]
          }
        },
        data: VALUE
      },
      {
        type: 'bar',
        label: {
          normal: {
            show: true,
            position: 'top',
            fontSize: 16,
            color: '#fff',
            offset: [4, -25]
          }
        },
        itemStyle: {
          color: 'transparent'
        },
        data: VALUE
      }
    ]
  }

  return option
}
