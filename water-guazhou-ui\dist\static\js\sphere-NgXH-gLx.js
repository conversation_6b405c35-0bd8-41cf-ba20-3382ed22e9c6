import{T as D}from"./index-r0dFAfgr.js";import{aE as L,i as F}from"./Point-WxyopZva.js";import{et as G,gi as H,gj as J,gk as U,aN as v,af as _,aO as b,aW as h,ac as w,ae as y,ab as u,aQ as B,aP as p,aT as f,aV as T,gl as tt,a0 as et,d0 as Q,aS as it,a$ as rt,a_ as st,b0 as nt,gm as E,aR as Y,b2 as ot}from"./MapView-DaoQedLH.js";import{n as ct}from"./mat3f64-BVJGbF0t.js";import{n as at}from"./mat4f64-BCm7QTSd.js";import{n as ut}from"./quatf64-QCogZAoR.js";var $;(function(t){t[t.X=0]="X",t[t.Y=1]="Y",t[t.Z=2]="Z"})($||($={}));class ft{constructor(e){this._allocator=e,this._items=[],this._itemsPtr=0,this._grow()}get(){return this._itemsPtr===0&&L(()=>this._reset()),this._itemsPtr===this._items.length&&this._grow(),this._items[this._itemsPtr++]}_reset(){const e=Math.min(3*Math.max(8,this._itemsPtr),this._itemsPtr+3*Z);this._items.length=Math.min(e,this._items.length),this._itemsPtr=0}_grow(){for(let e=0;e<Math.max(8,Math.min(this._items.length,Z));e++)this._items.push(this._allocator())}}const Z=1024;let P=class g{constructor(e,i,r){this._itemByteSize=e,this._itemCreate=i,this._buffers=new Array,this._items=new Array,this._itemsPtr=0,this._itemsPerBuffer=Math.ceil(r/this._itemByteSize)}get(){this._itemsPtr===0&&L(()=>this._reset());const e=Math.floor(this._itemsPtr/this._itemsPerBuffer);for(;this._buffers.length<=e;){const i=new ArrayBuffer(this._itemsPerBuffer*this._itemByteSize);for(let r=0;r<this._itemsPerBuffer;++r)this._items.push(this._itemCreate(i,r*this._itemByteSize));this._buffers.push(i)}return this._items[this._itemsPtr++]}_reset(){const e=2*(Math.floor(this._itemsPtr/this._itemsPerBuffer)+1);for(;this._buffers.length>e;)this._buffers.pop(),this._items.length=this._buffers.length*this._itemsPerBuffer;this._itemsPtr=0}static createVec2f64(e=l){return new g(16,H,e)}static createVec3f64(e=l){return new g(24,J,e)}static createVec4f64(e=l){return new g(32,U,e)}static createMat3f64(e=l){return new g(72,ct,e)}static createMat4f64(e=l){return new g(128,at,e)}static createQuatf64(e=l){return new g(32,ut,e)}get test(){return{size:this._buffers.length*this._itemsPerBuffer*this._itemByteSize}}};const l=4*G.KILOBYTES,Zt=P.createVec2f64(),c=P.createVec3f64(),qt=P.createVec4f64();P.createMat3f64();const ht=P.createMat4f64(),Lt=P.createQuatf64();function S(t){return t?q(v(t.origin),v(t.direction)):q(_(),_())}function q(t,e){return{origin:t,direction:e}}function Qt(t,e){const i=_t.get();return i.origin=t,i.direction=e,i}function Xt(t,e=S()){return mt(t.origin,t.direction,e)}function kt(t,e,i=S()){return b(i.origin,t),h(i.direction,e,t),i}function mt(t,e,i=S()){return b(i.origin,t),b(i.direction,e),i}function It(t,e){const i=w(c.get(),y(c.get(),t.direction),h(c.get(),e,t.origin));return u(i,i)}function gt(t,e,i){const r=u(t.direction,h(i,e,t.origin));return B(i,t.origin,p(i,t.direction,r)),i}const _t=new ft(()=>S());function Kt(t,e,i){const r=u(t,e)/u(t,t);return p(i,t,r)}function Nt(t,e){return u(t,e)/f(t)}function lt(t,e){const i=u(t,e)/(f(t)*f(e));return-T(i)}function Wt(t,e,i){y(M,t),y(R,e);const r=u(M,R),s=T(r),n=w(M,M,R);return u(n,i)<0?2*Math.PI-s:s}const M=_(),R=_();function C(){return tt()}function X(t,e=C()){return et(e,t)}function $t(t,e){return Q(t[0],t[1],t[2],e)}function pt(t){return t}function Pt(t){t[0]=t[1]=t[2]=t[3]=0}function dt(t,e){return t[0]=t[1]=t[2]=0,t[3]=e,t}function z(t){return t[3]}function Mt(t){return t}function bt(t,e,i,r){return Q(t,e,i,r)}function wt(t,e,i){return t!==i&&b(i,t),i[3]=t[3]+e,i}function yt(t,e,i){return F.getLogger("esri.geometry.support.sphere").error("sphere.setExtent is not yet supported"),t===i?i:X(t,i)}function V(t,e,i){if(D(e))return!1;const{origin:r,direction:s}=e,n=Bt;n[0]=r[0]-t[0],n[1]=r[1]-t[1],n[2]=r[2]-t[2];const o=s[0]*s[0]+s[1]*s[1]+s[2]*s[2];if(o===0)return!1;const a=2*(s[0]*n[0]+s[1]*n[1]+s[2]*n[2]),d=a*a-4*o*(n[0]*n[0]+n[1]*n[1]+n[2]*n[2]-t[3]*t[3]);if(d<0)return!1;const j=Math.sqrt(d);let m=(-a-j)/(2*o);const A=(-a+j)/(2*o);return(m<0||A<m&&A>0)&&(m=A),!(m<0)&&(i&&(i[0]=r[0]+s[0]*m,i[1]=r[1]+s[1]*m,i[2]=r[2]+s[2]*m),!0)}const Bt=_();function St(t,e){return V(t,e,null)}function zt(t,e,i){if(V(t,e,i))return i;const r=k(t,e,c.get());return B(i,e.origin,p(c.get(),e.direction,it(e.origin,r)/f(e.direction))),i}function k(t,e,i){const r=c.get(),s=ht.get();w(r,e.origin,e.direction);const n=z(t);w(i,r,e.origin),p(i,i,1/f(i)*n);const o=K(t,e.origin),a=lt(e.origin,i);return rt(s,a+o,r),st(i,i,s),i}function Vt(t,e,i){return V(t,e,i)?i:(gt(e,t,i),I(t,i,i))}function I(t,e,i){const r=h(c.get(),e,t),s=p(c.get(),r,t[3]/f(r));return B(i,s,t)}function At(t,e){const i=h(c.get(),e,t),r=nt(i),s=t[3]*t[3];return Math.sqrt(Math.abs(r-s))}function K(t,e){const i=h(c.get(),e,t),r=f(i),s=z(t),n=s+Math.abs(s-r);return T(s/n)}const x=_();function N(t,e,i,r){const s=h(x,e,t);switch(i){case $.X:{const n=E(s,x)[2];return Y(r,-Math.sin(n),Math.cos(n),0)}case $.Y:{const n=E(s,x),o=n[1],a=n[2],d=Math.sin(o);return Y(r,-d*Math.cos(a),-d*Math.sin(a),Math.cos(o))}case $.Z:return y(r,s);default:return}}function W(t,e){const i=h(O,e,t);return f(i)-t[3]}function Rt(t,e,i,r){const s=W(t,e),n=N(t,e,$.Z,O),o=p(O,n,i-s);return B(r,e,o)}function xt(t,e){const i=ot(t,e),r=z(t);return i<=r*r}const O=_(),Ot=C();Object.freeze(Object.defineProperty({__proto__:null,altitudeAt:W,angleToSilhouette:K,axisAt:N,clear:Pt,closestPoint:Vt,closestPointOnSilhouette:k,containsPoint:xt,copy:X,create:C,distanceToSilhouette:At,elevate:wt,fromCenterAndRadius:$t,fromRadius:dt,fromValues:bt,getCenter:Mt,getRadius:z,intersectRay:V,intersectRayClosestSilhouette:zt,intersectsRay:St,projectPoint:I,setAltitudeAt:Rt,setExtent:yt,tmpSphere:Ot,wrap:pt},Symbol.toStringTag,{value:"Module"}));export{Pt as C,bt as E,xt as N,C as R,z as T,St as V,X as _,Xt as a,It as b,c,S as d,ht as e,Nt as f,lt as g,kt as h,Kt as i,Wt as j,Mt as k,$ as n,Lt as o,Qt as p,qt as r,ft as s,Zt as t,pt as w};
