package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderLevel;
import org.thingsboard.server.dao.util.imodel.query.AwareCurrentUserUUID;
import org.thingsboard.server.dao.util.imodel.query.AwareTenantUUID;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderSaveRequest;

import java.util.Date;

@Getter
@Setter
public class SMCircuitTaskReportRequest implements AwareCurrentUserUUID, AwareTenantUUID {
    // 关键点id
    @NotNullOrEmpty
    private String pointId;

    // 有无隐患
    private Boolean isPitfall = false;

    // 工单标题
    @NotNullOrEmpty(condition = "isPitfall")
    private String title;

    // 紧急程度
    @NotNullOrEmpty(condition = "isPitfall")
    private String level;

    // 工单类型
    @NotNullOrEmpty(condition = "isPitfall")
    private String type;

    // 任务编号
    @NotNullOrEmpty
    private String code;

    // 处理级别
//    private WorkOrderLevel processLevel;

    // 处理级别
    private Integer processLevel;

    // 处理级别
    private String processLevelLabel;

    // 来源
    private String source;

    // 上报人
    private String uploadUserId;

    // 上报人电话
    private String uploadPhone;

    // 上报人户号
    private String uploadNo;

    // 上报人地址
    private String uploadAddress;

    // 抄送人，多个用逗号隔开
    private String ccUserId;

    // 描述/备注
    private String remark;

    // 现场视频，多个用逗号分隔（最多2）
    private String videoUrl;

    // 现场音频，多个用逗号分隔（最多2）
    private String audioUrl;

    // 现场图片，多个用逗号分隔
    private String imgUrl;

    // 其他附件，多个用逗号分隔（最多2）
    private String otherFileUrl;

    // 地理位置，目前规则为经纬度使用逗号隔开
    private String coordinate;

    // 地理位置名称
    private String coordinateName;

    private String currentUserId;

    private String tenantId;

    public WorkOrderSaveRequest buildWorkOrderSaveTemplate() {
        WorkOrderSaveRequest entity = new WorkOrderSaveRequest();
        entity.setTitle(title);
        entity.setSource(source);
        // entity.setOrganizerId(currentUserId);
        entity.setLevel(level);
        entity.setType(type);
        entity.setCoordinate(coordinate);
        entity.setCoordinateName(coordinateName);
        // entity.setAddress();
        entity.setRemark(remark);
        entity.setVideoUrl(videoUrl);
        entity.setAudioUrl(audioUrl);
        entity.setImgUrl(imgUrl);
        entity.setOtherFileUrl(otherFileUrl);
        entity.setUploadPhone(uploadPhone);
        entity.setUploadNo(uploadNo);
        entity.setUploadAddress(uploadAddress);
        entity.setIsDirectDispatch("false");
        // entity.setProjectId();
        entity.setCcUserId(ccUserId);
        // entity.setStepProcessUserId();
        entity.setProcessLevel(processLevel);
        entity.setProcessLevelLabel(processLevelLabel);
        entity.tenantId(tenantId);
        entity.currentUserId(currentUserId);
        return entity;

    }

    @Override
    public void currentUserId(String userId) {
        this.currentUserId = userId;
    }

    @Override
    public void tenantId(String uuid) {
        tenantId = uuid;
    }

    @SuppressWarnings("unused")
    public boolean isPitfall() {
        return isPitfall;
    }

}
