import { request } from '@/plugins/axios/gis';
import { requestPipe } from '@/plugins/axios/gisService';
import { useGisStore } from '@/store';
/**
 * 查询管线图层信息
 * @returns
 */
export const QueryPipeDataService = () => {
  return requestPipe({
    method: 'post',
    url: window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService + '?f=pjson'
  });
};
/**
 * 查询管网图层信息
 * @returns
 */
export const QueryPipeLayerInfos = () => {
  return requestPipe({
    method: 'get',
    url: window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService+ '/layers?f=pjson'
  })
}
/**
 * 统计管线
 * @param params
 * @returns
 */
export const PipeStatistics = (params: {
  usertoken?: string;
  layerids?: string;
  group_fields?: string;
  statistic_field?: string;
  statistic_type?: string;
  where?: string;
  geometry?: any;
  f?: 'pjson';
}) => {
  return requestPipe({
    url:
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService +
      '/exts/TFGeo/apiSOE/statistic',
    method: 'get',
    params: {
      usertoken: useGisStore().gToken,
      f: 'pjson',
      where: '1=1',
      ...(params || {})
    }
  });
};
/**
 * 查询图层的缩放范围
 * @param params
 * @returns
 */
export const GexExtent = (params: {
  usertoken?: string;
  layerid?: string | number;
  where?: string;
  f: 'pjson';
}) => {
  return requestPipe({
    url:
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService +
      '/exts/TFGeo/apiSOE/getExtent',
    method: 'get',
    params
  });
};
/**
 * 获取管网图例
 */
export const GetPipeLegends = () => {
  return requestPipe({
    url:
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService + '/legend?f=pjson',
    method: 'get'
  });
};
/**
 * 沿线查询
 * @param params
 * @returns
 */
export const GetPipeAttachment = (params: {
  flagOIDs: string;
  outputLayers: string;
}) => {
  return request({
    url: '/api/gis/AlongPipeQuery',
    method: 'post',
    params
  });
};
/**
 * 按管径统计管长
 * @returns
 */
export const GetPipeLengthByDiameterRange = () => {
  return request({
    url: '/api/gis/PipeByRangeDiameter',
    method: 'post'
  });
};
/**
 * 管线按年份分类统计
 * @returns
 */
export const GetPipeLengthByYear = () => {
  return request({
    url: '/api/gis/PipeByBuildYear',
    method: 'post'
  });
};
