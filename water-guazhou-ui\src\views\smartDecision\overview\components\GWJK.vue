<template>
  <div class="scroll-list" :class="props.size" :style="computedStyleVars">
    <ul ref="refHeader" class="list-header">
      <li class="table-header-row">
        <div
          v-for="(col, i) in state.header"
          :key="i"
          class="table-cell"
          :style="{
            minWidth: col.minWidth + 'px'
          }"
        >
          {{ col.label }}
        </div>
      </li>
    </ul>
    <div v-if="state.data.length" class="list-wrapper">
      <VueScroll :data="state.data" :class-option="state.classOption">
        <ul ref="refTable">
          <li v-for="(item, i) in state.data" :key="i" class="table-body-row">
            <div
              v-for="(col, j) in state.header"
              :key="j"
              class="table-cell"
              :style="{
                minWidth: col.minWidth + 'px'
              }"
              :title="item[col.prop] ?? '--'"
            >
              <div v-if="col.isRank" class="isRank">
                <span :style="{ color: col.color }">{{
                  item[col.prop] ?? '--'
                }}</span
                ><span v-if="col.unit" style="margin-left: 2px">{{
                  col.unit
                }}</span>
              </div>
              <template v-else>
                <span :style="{ color: col.color }">{{
                  item[col.prop] ?? '--'
                }}</span
                ><span v-if="item.unit" style="margin-left: 2px">{{
                  item.unit
                }}</span>
              </template>
            </div>
          </li>
        </ul>
      </VueScroll>
    </div>
    <div v-else class="empty">暂无数据</div>
  </div>
</template>
<script lang="ts" setup>
import { GetStationDynamicRealtimeData } from '@/api/shuiwureports/bengzhan';

const props = defineProps<{
  size?: 'small';
  height?: number;
}>();
const computedStyleVars = computed(() => {
  return {
    '--wrapper-height':
      (props.height ? props.height : props.size === 'small' ? 160 : 240) + 'px'
  };
});
const state = reactive<{
  classOption: any;
  header: {
    label: string;
    prop: string;
    minWidth?: number;
    unit?: string;
    isRank?: boolean;
    color?: string;
  }[];
  data: Record<string, any>[];
}>({
  classOption: {
    step: 0.2,
    limitMoveNum: 5
  },
  header: [
    { label: '序号', prop: 'sort', minWidth: 60, isRank: true },
    { label: '监测点', prop: 'name', minWidth: 160 },
    {
      label: '瞬时流量',
      prop: 'instance_flow',
      unit: 'm³/h',
      minWidth: 130,
      color: '#58FEF4'
    },
    {
      label: '压力',
      prop: 'pressure',
      minWidth: 100,
      color: '#FFEE93',
      unit: 'MPa'
    }
  ],
  data: []
});
const refHeader = ref<HTMLElement>();
const refTable = ref<HTMLElement>();
const refreshData = async () => {
  try {
    const res = await GetStationDynamicRealtimeData({
      stationType: '压力监测站,测流压站'
    });
    const data: any[] = res.data || [];
    state.data = data
      .map((item) => {
        const instanceFlowRow =
          item.dataList?.find((o) => o.property === 'Instantaneous_flow') || {};
        const pressure =
          item.dataList?.find((o) => o.property === 'pressure') || {};
        return {
          sort: instanceFlowRow?.value ?? 0,
          name: item.name,
          instance_flow:
            (instanceFlowRow?.value ?? '--') +
            ' ' +
            (instanceFlowRow?.unit ?? ''),
          pressure: (pressure?.value ?? '--') + ' ' + (pressure?.unit ?? ''),
          delta: '-2.19',
          color: '#FF6565'
        };
      })
      .sort((a, b) => {
        return b.sort - a.sort;
      })
      .map((item, i) => {
        item.sort = i + 1;
        return item;
      });
    state.data = [...state.data];
  } catch (error) {
    console.log(error);
  }
};
onMounted(() => {
  refreshData();
});
</script>
<style lang="scss" scoped>
.empty {
  display: grid;
  place-items: center;
  color: #77c0fa;
  height: calc(100% - 28px);
  font-size: 14px;
}
ul {
  list-style: none;
}
ul,
li {
  padding: 0;
  margin: 0;
}
.scroll-list {
  overflow: hidden;
  font-size: 12px;
  padding: 12px;
  height: var(--wrapper-height);
  &.small {
    height: 160px;
  }
  .list-wrapper {
    height: calc(100% - 28px);
    overflow: hidden;
  }
  .list-header {
    height: 28px;
    color: #b8d2ff;
    text-align: left;
  }
  .table-body-row {
    height: 36px;
    &:nth-child(even) {
      background-color: rgba(0, 60, 163, 0.66);
    }
    &:nth-child(odd) {
      background-color: rgba(43, 101, 249, 0.11);
    }
  }
}

.table-header-row,
.table-body-row {
  height: 28px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
}

.table-cell {
  padding: 2px 4px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  .isRank {
    width: 20px;
    height: 20px;
    margin: auto;
    border: 2px solid rgba(139, 208, 255, 1);
    border-radius: 4px;
    background: linear-gradient(
      rgba(139, 208, 255, 1) 0,
      rgba(109, 196, 255, 0.26) 60%,
      #b8d2ff
    );
  }
}
</style>
