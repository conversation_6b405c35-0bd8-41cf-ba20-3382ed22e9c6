<!-- gis巡检人员 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards
      v-model="cardsvalue"
      :span="12"
      style="margin-bottom: 10px"
    ></Cards>
    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components'
import { IFormIns } from '@/components/type'
import { getWaterSupplyTree } from '@/api/company_org'
import { formatTree } from '@/utils/GlobalHelper'
import { useUserLocation } from '@/hooks/arcgis'

const userlocation = useUserLocation()
const emit = defineEmits(['highlightMark', 'addMarks'])
const props = defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const refForm = ref<IFormIns>()

const cardsvalue = ref([
  { label: '0 人', value: '巡检人员数' },
  { label: '0 人', value: '外出人员数' }
])

const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          defaultExpandAll: true,
          field: 'departmentId',
          checkStrictly: true,
          options: [],
          onChange: () => refreshData(),
          extraFormItem: [
            {
              type: 'input',
              field: 'userName',
              appendBtns: [
                {
                  perm: true,
                  isTextBtn: true,
                  text: '刷新',
                  click: () => refreshData()
                }
              ]
            }
          ]
        },
        {
          type: 'tree',
          checkStrictly: true,
          options: [],
          props: {
            label: 'userName'
          },
          nodeKey: 'userId',
          nodeClick: (data: any) => {
            emit('highlightMark', props.menu, data?.userId)
          }
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const refreshData = async () => {
  const query = refForm.value?.dataForm || {}
  const users = await userlocation.getLatestXunJianRenYuanCoords({
    ...query
  })
  const tree = FormConfig.group[0].fields[1] as IFormTree
  tree && (tree.options = users.data)
  const pops = userlocation.generatePops(
    props.view,
    users.data,
    false,
    (id: string) => {
      emit('highlightMark', props.menu, id)
    }
  )
  pops.map(item => {
    item.attributes.path = props.menu.path
  })
  cardsvalue.value[0].label = (users.data?.length || 0) + ' 人'
  cardsvalue.value[1].label = 0 + ' 人'
  emit('addMarks', {
    windows: pops
  })
}
const initTree = () => {
  getWaterSupplyTree(2).then(res => {
    const tree = FormConfig.group[0].fields[0] as IFormTree
    tree
      && (tree.options = formatTree(res.data.data || [], {
        label: 'name',
        value: 'id',
        id: 'id',
        children: 'children'
      }))
    refForm.value
      && (refForm.value.dataForm.departmentId = tree.options?.[0].value)
    refreshData()
  })
}
onMounted(() => {
  initTree()
})
</script>

<style lang="scss" scoped>
.el-tabs {
  :deep(.el-tabs__content) {
    height: calc(100% - 40px);
  }
  :deep(.el-tab-pane) {
    height: 100%;
  }
}
.chart-wrapper {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
  flex-direction: row;
  .tabs {
    flex: 1;
  }
}
</style>
