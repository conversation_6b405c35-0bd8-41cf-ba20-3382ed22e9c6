import{d as m,c as l,g as t,n as d,p as _,bh as p,i as h,h as u,F as f,G as C,an as S,J as k,C as g}from"./index-r0dFAfgr.js";const x={class:"scheme-header"},B=m({__name:"SchemeHeader",props:{title:{}},emits:["schemeClick"],setup(s,{emit:a}){const o=s,c=a,n=()=>{c("schemeClick")},r=l(window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme??!1);return(I,e)=>{const i=k;return t(),d("div",x,[_("span",null,p(o.title),1),h(r)?(t(),u(i,{key:0,size:"small",onClick:n},{default:f(()=>e[0]||(e[0]=[C(" 方案管理 ")])),_:1})):S("",!0)])}}}),v=g(B,[["__scopeId","data-v-54e332d8"]]);export{v as default};
