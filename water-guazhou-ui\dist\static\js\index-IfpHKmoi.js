import{_ as I}from"./TreeBox-DDD2iwoR.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as P}from"./CardSearch-CB_HNR-Q.js";import{_ as B}from"./index-BJ-QPYom.js";import E from"./stationDetailMonitoring-amDn42AY.js";import{d as M,r as l,c as _,l as N,bH as x,b as n,bE as D,S as j,o as F,g,h as L,F as T,q as c,n as $,aB as q,C as A}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as H}from"./usePartition-DkcY9fQ2.js";import{E as R}from"./index-0NlGN6gS.js";import{_ as V}from"./NewOrder.vue_vue_type_script_setup_true_lang-DAnrmOVe.js";import{f as v}from"./DateFormatter-Bm9a68Ax.js";import{E as G,a as O}from"./lossControl-DNefZk8I.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./useDetector-BRcb7GRN.js";import"./padStart-BKfyZZDO.js";import"./minBy-DBQvPu-j.js";import"./_baseExtremum-UssVWohW.js";import"./_baseSum-Cz9yialR.js";import"./_baseLt-svgXHEqw.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./FormMap-BGaXSqQF.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./utils-D5nxoMq3.js";import"./useUser-Blb5V02j.js";import"./config-DqqM5K5L.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const Z=M({__name:"index",setup(z){const u=l({isDetail:!1}),h=_(),f=_(),i=l({data:[],loading:!0,title:"选择分区",expandOnClickNode:!1,treeNodeHandleClick:async t=>{i.currentProject!==t&&(i.currentProject=t,a())}}),W=l({defaultParams:{date:N().format(x)},filters:[{type:"date",label:"日期",field:"date",clearable:!1,format:x},{type:"input",label:"分区名称",field:"name"},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>a()},{type:"default",perm:!0,text:"重置",iconifyIcon:"ep:refresh",click:()=>{var t;(t=f.value)==null||t.resetForm()}},{perm:!0,text:"工单",type:"success",iconifyIcon:"ep:plus",click:()=>{var t,e;(t=r.selectList)!=null&&t.length?r.selectList.length>1?n.warning("只能选择一条数据"):(e=b.value)==null||e.openDialog():n.warning("请选择一条数据")}},{perm:!0,type:"warning",text:"导出",iconifyIcon:"ep:download",click:()=>{var t;(t=h.value)==null||t.exportTable()}}]}]}),r=l({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"partitionName",label:"分区名称",minWidth:220},{prop:"statusName",label:"分区状态",minWidth:120},{prop:"userNum",label:"用户数(户)",minWidth:120},{prop:"inlet",label:"进水口(个)",minWidth:120},{prop:"supplyTotal",label:"供水量",unit:"(m³)",minWidth:120},{prop:"minTime",label:"夜间最小流时间",minWidth:160,sortable:!0,formatter(t,e){return v(e,D)}},{prop:"minFlow",label:"夜间最小流",unit:"(m³/h)",minWidth:220,sortable:!0},{prop:"minValue",label:"夜间最小水量值",unit:"(m³)",minWidth:210,sortable:!0},{label:"基准值时间",prop:"incrTime",minWidth:160,formatter(t,e){return v(e,D)}},{label:"基准值(m³/h)",prop:"incrBase",minWidth:140},{label:"黄色预警值",prop:"incrWarn",minWidth:140},{label:"红色预警值",prop:"incrError",minWidth:140}],operations:[{text:"切评估",isTextBtn:!0,perm:!0,iconifyIcon:"ep:edit",click:t=>w(t)}],operationWidth:"100px",handleRowDbClick(t){r.currentRow=t,u.isDetail=!0},handleSelectChange(t){r.selectList=t},singleSelect:!0,select(t){var e;(e=r.selectList)!=null&&e.length&&r.selectList.findIndex(o=>o.partitionId===t.partitionId)!==-1?r.selectList=[]:r.selectList=[t]},pagination:{refreshData:({page:t,size:e})=>{r.pagination.page=t,r.pagination.limit=e,a()}}}),w=t=>{j("是否将状态修改为评估中?","提示信息").then(async()=>{try{(await G({id:t.partitionId,status:R.PingGuZhong})).data.code===200?(n.success("操作成功"),a()):n.success("操作失败")}catch{n.error("操作失败")}}).catch(()=>{})},b=_(),a=async()=>{var o,p,m,s;if(!i.currentProject)return;const t=((o=f.value)==null?void 0:o.queryParams)||{},e=await O({...t,partitionId:(p=i.currentProject)==null?void 0:p.value});r.dataList=((m=e.data)==null?void 0:m.data)||[],r.pagination.total=((s=e.data)==null?void 0:s.total)||0},d=H();return F(async()=>{const t=d.getTree(),e=d.getList();await Promise.all([t,e]),i.data=d.Tree.value||[],i.currentProject=i.data[0],a()}),(t,e)=>{const o=B,p=P,m=S,s=I;return g(),L(s,null,{tree:T(()=>[c(o,{ref:"refTree","tree-data":i},null,8,["tree-data"])]),default:T(()=>{var y,k;return[u.isDetail?(g(),L(E,{key:0,device:r.currentRow,onHiddenLoading:e[0]||(e[0]=C=>i.loading=!1),onBack:e[1]||(e[1]=C=>u.isDetail=!1)},null,8,["device"])):(g(),$(q,{key:1},[c(p,{ref_key:"refSearch",ref:f,config:W},null,8,["config"]),c(m,{ref_key:"refTable",ref:h,class:"card-table",config:r},null,8,["config"])],64)),c(V,{ref_key:"refDialog",ref:b,"default-values":{partitionId:(k=(y=r.selectList)==null?void 0:y[0])==null?void 0:k.partitionId}},null,8,["default-values"])]}),_:1})}}}),$e=A(Z,[["__scopeId","data-v-365d9eab"]]);export{$e as default};
