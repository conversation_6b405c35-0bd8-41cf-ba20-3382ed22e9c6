package org.thingsboard.server.dao.smartService.system;

import org.thingsboard.server.dao.model.sql.smartService.system.SystemWorkOrderType;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 知识库公告
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface SystemWorkOrderTypeService {

    SystemWorkOrderType save(SystemWorkOrderType systemWorkOrderType);

    IstarResponse delete(List<String> ids);

    List getTree(String isDel, String tenantId);

    IstarResponse deleteHard(List<String> ids);
}
