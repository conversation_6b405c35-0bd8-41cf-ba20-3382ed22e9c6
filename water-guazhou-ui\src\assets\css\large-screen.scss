// 1、2 屏 共用样式
.large-screen-custom-container, .large-screen-custom2-container {
  height: 100%;
  width: 100%;
  overflow: auto;
  color: #fff;
  font-family: '黑体';
  font-family: 'Adobe Heiti Std R';
  background: #182955;
  .screen-main-container {
    height: 1075px;
    width: 1915px;
  }
  // 头部
  .header-box {
    height: 70px;
    width: 100%;
    margin-top: 10px;
    position: relative;
    .head-title {
      text-align: center;
      font-size: 30px;
      margin: 5px 0 0 0;
      padding-top: 10px;
    }
    .header-bg {
      position: absolute;
      top: 0;
      left: 0;
      height: 80px;
      margin: 0 30px;
      width: calc(100% - 60px);
      background: url('~@/assets/images/other/custom/header-bg.png') center center no-repeat;
      background-size: 100% 100%;
    }
    .full-screen {
      position: absolute;
      top: 5px;
      left: 30px;
      width: 30px;
      height: 30px;
      margin: 0 0;
      cursor: pointer;
      background: url('~@/assets/images/other/quanping.png') center center no-repeat;
      background-size: 100% 100%;
    }
    .return-page {
      position: absolute;
      top: 5px;
      left: 70px;
      width: 40px;
      height: 30px;
      margin: 0 0;
      font-size: 12px;
      color: #07b2e9;
      cursor: pointer;
      text-align: center;
      line-height: 28px;
      border: 1px solid #3541b4;
      border-radius: 5px;
    }
  }
  .main-box-container {
    width: 100%;
    height: calc(100% - 80px);
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
  }

  // 公共控制
  .panel-data-container {
    .panel-box {
      width: 100%;
      padding: 15px;
      border-radius: 5px;
      margin: 25px 0 0 0;
      background-color: #1d3164;
      .box-title {
        font-size: 18px;
        padding-left: 10px;
        // margin: 0 10px 10px 0;
        line-height: 18px;
        margin: 4px 10px 14px 0;
        color: rgb(129, 232, 254);
        border-left: 2px solid rgb(129, 232, 254);
      }
      .box-content {
        width: 100%;
        height: calc(100% - 30px);
      }
    }
  }
  // ======= 左边 =======
  .left-data-container {
    width: 31%; // 1252px
    height: 100%;
  }
  // ======= 中间 =======
  .center-map-container {
    width: 35%;
    .center-box1 {
      .float-box {
        width: 100%;
        height: 0;
        position: relative;
      }
      .show-search-box {
        position: absolute;
        z-index: 10;
        top: 10px;
        left: 10px;
        width: 90%;
        height: 30px;
        display: flex;
        color: #d1d1d1;
        line-height: 26px;
        text-align: center;
        border-radius: 5px;
        align-items: center;
        // border: 1px solid #013b6d;
        .el-icon-refresh-left {
          font-size: 16px;
          margin: auto 0;
          cursor: pointer;
        }
        .large-s-select {
          width: 200px;
          margin-right: 10px;
        }
        .large-s-select-theme {
          width: 100px;
        }
      }
    }
    .center-box2 {
      .box-content {
        .farming-item-box {
          font-size: 14px;
          opacity: 0;
          height: 28px;
          margin: 10px 0;
          display: flex;
          cursor: pointer;
          overflow: hidden;
          align-items: center;
          justify-content: space-between;
          -webkit-animation: rotateWord 18s linear infinite 0s;
          -moz-animation: rotateWord 18s linear infinite 0s;
          animation: rotateWord 15s linear infinite 0s;
          .f-name {
            width: 500px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            color: rgba(149, 171, 219, 1);
            height: 100%;
            padding: 5px 0 0 30px;
            background: url('~@/assets/images/other/custom/green.png') left center no-repeat;
            background-size: 28px 100%;
          }
          .f-time {
            color: rgba(104, 147, 255, 1);
          }
        }
        .f-i-option {
          opacity: 1;
        }
      }
    }
  }
  // ======= 右边 =======
  .right-data-container {
    width: 31%;
    .video-plate {
      position: relative;
      .to-video-btn {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 30px;
        height: 30px;
        font-size: 20px;
        cursor: pointer;
        text-align: center;
        .iconfont {
          font-size: 20px;
          color: #81e8fe;
          color: #a7e332;
        }
      }
    }
    .alarm-plate {
      .alarm-palte-box {
        .el-table__body-wrapper {
          &::-webkit-scrollbar {/*滚动条整体样式*/
            width: 3px;     /*高宽分别对应横竖滚动条的尺寸*/
            height: 3px;
          }
          &::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
            border-radius: 3px;
            box-shadow: inset 0 0 3px rgba(128, 219, 255, 0.2);
            // background: $other-color1;
            background: #868d9e;
          }
          &::-webkit-scrollbar-track {/*滚动条里面轨道*/
            box-shadow: inset 0 0 3px rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            background: #d3d3d3;
          }
        }
      }
    }
  }
  .l-charts-box {
    height: 100%;
    .echarts {
      height: 100%;
      width: 100%;
      canvas {
        height: 100%;
        width: 100%;
      }
    }
  }

  .showinfo-popup-modal {
    background: transparent;
  }
}

// 1屏 样式
.large-screen-custom-container {
  .main-box-container {
    // =============== 1屏 左边 ================================
    .left-data-container {
      .left-box1 {
        height: 170px;
        margin-top: 15px;
        .box-content {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: flex-end;
          .item-info-box {
            width: 43%;
            display: flex;
            font-size: 18px;
            align-items: center;
            margin: 10px 10px 10px 0;
            .iconfont {
              font-size: 24px;
              margin-top: -5px;
            }
            .i-label {
              color: #fff;
              margin-left: 10px;
            }
          }
        }
      }
      .left-box2 {
        height: 350px;
        .l-charts-box {
          width: 100%;
          height: 100%;
        }
      }
      .left-box3 {
        height: 390px;
        p {
          margin: 0 0;
        }
        .a-total-box {
          height: 30px;
          display: flex;
          margin-top: 20px;
          justify-content: flex-end;
          .a-total {
            width: 44%;
            font-size: 16px;
            padding: 0 10px 10px 20px;
            background: url('~@/assets/images/other/custom/line-bg.png') left bottom no-repeat;
            background-size: 80% 12px;
          }
          .a-t-value {
            color: #E73851;
          }
        }
        .top-title {
          font-size: 18px;
          margin: 30px 0;
        }
        .top-item-box {
          height: 35px;
          display: flex;
          padding: 0 10px;
          margin: 5px 0 8px 0;
          align-items: center;
          justify-content: space-between;
          .top-name {
            height: 100%;
            width: 36%;
            font-size: small;
            padding: 5px 0 0 30px;
          }
          .top-progress {
            width: 60%;
          }
        }
        .top1-box {
          .top-name {
            background: url('~@/assets/images/other/custom/alarm-t1.png') left center no-repeat;
          }
        }
        .top2-box {
          .top-name {
            background: url('~@/assets/images/other/custom/alarm-t2.png') left center no-repeat;
          }
        }
        .top3-box {
          .top-name {
            background: url('~@/assets/images/other/custom/alarm-t3.png') left center no-repeat;
          }
        }
        .top4-box {
          .top-name {
            background: url('~@/assets/images/other/custom/alarm-t4.png') left center no-repeat;
          }
        }
        .top5-box {
          .top-name {
            background: url('~@/assets/images/other/custom/alarm-t5.png') left center no-repeat;
          }
        }
        .top-item-box {
          .top-name {
            background-size: 14px 16px;
          }
        }

      }
    }

    // =============== 1屏 中间 ===============
    .center-map-container {
      .center-box1 {
        height: 640px;
        margin-top: 30px;
        .box-content {
          position: relative;
        }
        .l-charts-box {
          height: 100%;
          .echart-map-visible {
            height: 100%;
            width: 100%;
          }
        }
      }
      .center-box2 {
        height: 280px;
      }
    }

    // =============== 1屏 右边 ================================
    .right-data-container {
      .weather-plate {
        height: 200px;
        margin: 10px 0 0 0;
        .weather-info-box {
          display: flex;
          height: calc(100% - 28px);
          .w-info-box {
            width: 20%;
            height: 100%;
            display: flex;
            text-align: center;
            justify-content: center;
            flex-direction: column;
            padding: 5px 0;
            background-image: linear-gradient(to bottom, #16427a, #173e71);
            &:hover {
              background-image: linear-gradient(to bottom, #0E368E, #173b71);
            }
            .w-i-name {
              font-size: 12px;
            }
            .w-i-second {
              margin-top: 3px;
              color: #CFDEFF;
              font-size: 10px;
              font-weight: normal;
            }
            .w-i-weather {
              font-size: 12px;
              color: #fff;
              margin-top: 5px;
              font-weight: normal;
            }
            .w-i-temperature {
              color: #F8B62D;
            }
          }
          .w-info-current {
            background-image: linear-gradient(to bottom, #16397a, #173b71);
          }
        }
      }
      .video-plate {
        height: 475px;
        margin-top: 15px;
        .video-plate-box {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          overflow: hidden;
          .video-info {
            width: 47%;
            height: 200px;
            padding: 5px 0;
            .video-box {
              width: 100%;
              height: calc(100% - 30px);
            }
          }
          .v-name {
            text-align: center;
            color: #cee8fe;
            margin-top: 10px;
          }
        }
      }
      .alarm-plate {
        height: 250px;
        // .alarm-palte-box {
        //   height: calc(100% - 30px);
        //   overflow: hidden;
        // }
        .img-box {
          display: flex;
          align-items: center;
        }
        .img-icon {
          margin-right: 10px;
          height: 30px;
          width: 30px;
          background: url('~@/assets/images/other/alarm.gif') center center no-repeat;
          background-size: 100% 100%;
        }
        .alarm-text {
          color: #51e5d7;
        }
      }
    }
  }
}

// 2屏 样式
.large-screen-custom2-container {
  .main-box-container {
    // =============== 2屏 左边 ================================
    .left-data-container {
      .left-box1 {
        height: 160px;
        margin-top: 15px;
        .box-content {
          display: flex;
          align-items: center;
          justify-content: space-around;
          .item-info-box {
            width: 23%;
            height: 100%;
            text-align: center;
            .i-name {
              margin: 0 0;
              font-size: 14px;
            }
            .i-value-gif {
              width: 80%;
              font-size: 18px;
              color: #3AFFE4;
              line-height: 70px;
              height: calc(100% - 30px);
              margin: 5px auto 0 auto;
              background: url('~@/assets/images/other/custom/dynamic-bg.gif') center center no-repeat;
              background-size: 100% 100%;
            }
          }
        }
      }
      .left-box21 {
        height: 125px;
        display: flex;
        width: 100%;
        padding: 15px;
        border-radius: 5px;
        margin: 25px 0 0 0;
        background-color: #1d3164;
        .device-left {
          width: 180px;
          height: 100%;
          .box-title {
            font-size: 18px;
            padding-left: 10px;
            // margin: 0 10px 10px 0;
            line-height: 18px;
            margin: 4px 10px 14px 0;
            color: rgb(129, 232, 254);
            border-left: 2px solid rgb(129, 232, 254);
          }
          .on-line-rate {
            margin: 0 0;
            height: 60px;
            font-size: 32px;
            color: #F8B62D;
            line-height: 60px;
            text-align: center;
          }
        }
        .device-right {
          // width: calc(100% - 180px);
          // height: 100%;
          height: 95px;
          width: 360px;
          .echarts {
            height: 100%;
            width: 100%;
            canvas {
              height: 100%;
              width: 100%;
            }
          }
        }
        // .pie-3-chart-png {
        //   background: url('~@/assets/images/other/custom/pie-3.png') center center no-repeat;
        //   background-size: 100% 100%;
        // }
      }
      .left-box3 {
        height: 628px;
        position: relative;
        .current-d-name {
          position: absolute;
          top: 15px;
          right: 15px;
          color: #bee4f8;
          font-size: 16px;
        }
        .environmental-var {
          height: 55%;
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
          .e-v-box {
            width: 120px;
            height: 60px;
            display: flex;
            margin: 5px 8px;
            align-items: center;
            background-color: #143276;
            .e-v-icon {
              width: 36px;
              height: 36px;
              text-align: right;
              padding-right: 3px;
              .iconfont {
                color: #bee4f8;
                font-size: 24px;
                line-height: 30px;
              }
            }
            .e-v-info {
              width: 84px;
              text-align: center;
              .e-v-name {
                margin: 5px 0;
              }
              .e-v-valueunit {
                margin: 5px 0;
                color: #fff;
                font-size: 12px;
              }
              .e-v-value {
                color: #F8B62D;
                font-size: 16px;
              }
            }
          }
          .active-e-v {
            background-color: rgba(5,181, 233,0.5);
          }
        }
        .e-value-line {
          height: 45%;
          width: 100%;
        }
      }
    }

    // =============== 2屏 中间 ===============
    .center-map-container {
      .center-box1 {
        height: 640px;
        margin-top: 30px;
        padding: 10px;
        .map-layers-btn {
          color: #818dff;
          padding: 0 10px;
          cursor: pointer;
          background: #091443;
          border: 1px solid #8894ff;
        }
        .current-l {
          color: #81e6ff;
          border-color: #81e6ff;
        }
        .m-l-btn1 {
          border-right-color: #81e6ff;
          border-radius: 5px 0 0 5px;
        }
        .m-l-btn2 {
          border-left-color: #81e6ff;
          border-radius: 0 5px 5px 0;
        }
        .box-content {
          height: 100%;
          width: 100%;
          // padding: 10px;
          #map-container {
            height: 100%;
            width: 100%;
          }
        }
      }
      .center-box2 {
        height: 280px;
      }
    }

    // =============== 2屏 右边 ================================
    .right-data-container {
      .video-plate {
        height: 550px;
        margin-top: 15px;
        overflow: hidden;
        .video-plate-box {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .video-info {
            width: 47%;
            height: 240px;
            padding: 5px 0;
            .video-box {
              width: 100%;
              height: calc(100% - 30px);
            }
          }
          .v-name {
            text-align: center;
            color: #cee8fe;
            margin-top: 10px;
          }
        }
      }
      .alarm-plate {
        height: 385px;
        // .alarm-palte-box {
        //   height: calc(100% - 30px);
        //   overflow: hidden;
        // }
        .img-box {
          display: flex;
          align-items: center;
        }
        .img-icon {
          margin-right: 10px;
          height: 30px;
          width: 30px;
          background: url('~@/assets/images/other/alarm.gif') center center no-repeat;
          background-size: 100% 100%;
        }
        .alarm-text {
          color: #51e5d7;
        }
      }
    }
  }
}

// 全屏样式控制
.large-screen-page-full-screen {
  z-index: 9999999;
  position: fixed;
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
}

// 天气背景图
.tianqi-baoxue {
  background: url('~@/assets/images/other/tianqi/baoxue.png') center center no-repeat;
}
.tianqi-baoyu {
  background: url('~@/assets/images/other/tianqi/baoyu.png') center center no-repeat;
}
.tianqi-dabaoyu {
  background: url('~@/assets/images/other/tianqi/dabaoyu.png') center center no-repeat;
}
.tianqi-daxue {
  background: url('~@/assets/images/other/tianqi/daxue.png') center center no-repeat;
}
.tianqi-dayu {
  background: url('~@/assets/images/other/tianqi/dayu.png') center center no-repeat;
}
.tianqi-dongyu {
  background: url('~@/assets/images/other/tianqi/dongyu.png') center center no-repeat;
}
.tianqi-duoyun {
  background: url('~@/assets/images/other/tianqi/duoyun.png') center center no-repeat;
}
.tianqi-fuchen {
  background: url('~@/assets/images/other/tianqi/fuchen.png') center center no-repeat;
}
.tianqi-leizhenyu {
  background: url('~@/assets/images/other/tianqi/leizhenyu.png') center center no-repeat;
}
.tianqi-leizhenyubanyoubingbao {
  background: url('~@/assets/images/other/tianqi/leizhenyubanyoubingbao.png') center center no-repeat;
}
.tianqi-mai {
  background: url('~@/assets/images/other/tianqi/mai.png') center center no-repeat;
}
.tianqi-qiangshachenbao {
  background: url('~@/assets/images/other/tianqi/qiangshachenbao.png') center center no-repeat;
}
.tianqi-qing {
  background: url('~@/assets/images/other/tianqi/qing.png') center center no-repeat;
}
.tianqi-shachenbao {
  background: url('~@/assets/images/other/tianqi/shachenbao.png') center center no-repeat;
}
.tianqi-tedabaoyu {
  background: url('~@/assets/images/other/tianqi/tedabaoyu.png') center center no-repeat;
}
.tianqi-wu {
  background: url('~@/assets/images/other/tianqi/wu.png') center center no-repeat;
}
.tianqi-xiaoyu {
  background: url('~@/assets/images/other/tianqi/xiaoyu.png') center center no-repeat;
}
.tianqi-xiaoxue {
  background: url('~@/assets/images/other/tianqi/xiaoxue.png') center center no-repeat;
}
.tianqi-yangsha {
  background: url('~@/assets/images/other/tianqi/yangsha.png') center center no-repeat;
}
.tianqi-yin {
  background: url('~@/assets/images/other/tianqi/yin.png') center center no-repeat;
}
.tianqi-yujiaxue {
  background: url('~@/assets/images/other/tianqi/yujiaxue.png') center center no-repeat;
}
.tianqi-zhenxue {
  background: url('~@/assets/images/other/tianqi/zhenxue.png') center center no-repeat;
}
.tianqi-zhenyu {
  background: url('~@/assets/images/other/tianqi/zhenyu.png') center center no-repeat;
}
.tianqi-zhongxue {
  background: url('~@/assets/images/other/tianqi/zhongxue.png') center center no-repeat;
}
.tianqi-zhongyu {
  background: url('~@/assets/images/other/tianqi/zhongyu.png') center center no-repeat;
}
.w-i-tianqi {
  width: 28px;
  min-height: 26px;
  margin: 5px auto 0 auto;
  background-size: 100% 100%;
}

// 动画 动效
@-webkit-keyframes rotateWord {
  0% { opacity: 0; }
  2% { opacity: 0.8; -webkit-transform: translateY(-10px); }
  5% { opacity: 0.3; -webkit-transform: translateY(-5px);}
  17% { opacity: 1; -webkit-transform: translateY(0px); }
  87% { opacity: 1; -webkit-transform: translateY(0px); }
  95% { opacity: 0.3; -webkit-transform: translateY(10px); }
  100% { opacity: 0; }
}

@-moz-keyframes rotateWord {
  0% { opacity: 0; }
  2% { opacity: 0.8; -moz-transform: translateY(-10px); }
  5% { opacity: 0.3; -moz-transform: translateY(-5px);}
  17% { opacity: 1; -moz-transform: translateY(0px); }
  87% { opacity: 1; -moz-transform: translateY(0px); }
  95% { opacity: 0.3; -moz-transform: translateY(10px); }
  100% { opacity: 0; }
}

@keyframes rotateWord {
  0% { opacity: 0; }
  2% { opacity: 0.8; transform: translateY(-10px); }
  5% { opacity: 0.3; transform: translateY(-5px);}
  17% { opacity: 1; transform: translateY(0px); }
  87% { opacity: 1; transform: translateY(0px); }
  95% { opacity: 0.3; transform: translateY(10px); }
  100% { opacity: 0; }
}
