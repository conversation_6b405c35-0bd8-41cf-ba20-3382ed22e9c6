import{_ as G}from"./index-C9hz-UZb.js";import{d as H,M,a6 as U,r as g,c as v,am as J,bF as c,a8 as W,s as T,j as C,bB as Q,bu as $,ay as X,g as Y,n as Z,q as f,i as m,F as _,cs as q,bo as D,bR as N,p as A,dF as ee,dA as te,aq as ae,al as ne,b7 as re,aj as le,C as se}from"./index-r0dFAfgr.js";import{_ as oe}from"./CardSearch-CB_HNR-Q.js";import{l as ie}from"./echart-Cn-w9IL8.js";import{e as ce,a as de}from"./queryStatistics-CQ9DBM08.js";import{u as ue}from"./useStation-DJgnSZIA.js";import{f as pe}from"./formartColumn-D5r7JJ2G.js";import{b as me}from"./zhandian-YaGuQZe6.js";import"./Search-NSrhrIa_.js";const fe={class:"wrapper"},ye=H({__name:"index",setup(he){const{$messageWarning:w}=M(),I=U(),{getStationTree:P,getStationTreeByDisabledType:V,getStationAttrGroups:E}=ue(),s=g({type:"date",chartOption:null,stationTree:[],activeName:"echarts",data:null,checkedKeys:[]}),j=[{label:"1 m",value:"1m"},{label:"5 m",value:"5m"},{label:"10 m",value:"10m"},{label:"15 m",value:"15m"},{label:"1小时",value:"hour"}],x=v(),k=v(),y=v(),B=v();let h=g([]);const O=g({data:[],title:"区域划分",showCheckbox:!0,defaultExpandAll:!0,accordion:!1,checkedKeys:[],handleCheck:(e,t)=>{console.log(t.checkedNodes,t.checkedKeys),O.checkedKeys=t.checkedKeys||[],O.checkedNodes=t.checkedNodes||[],S()},nodeExpand:async(e,t)=>{var a;if(((a=e.data)==null?void 0:a.type)==="Station"&&e.children[0].id===0){const r=await E(e.id,!0);t.data.children=r}}});J(()=>s.activeName,()=>{s.activeName==="echarts"&&L()});const R=g({defaultParams:{queryType:"15m",type:"day",year:[c().format(),c().format()],month:[c().format(),c().format()],day:[c().startOf("day").format(),c().format()]},filters:[{type:"select-tree",label:"监测点:",multiple:!0,field:"attributeId",clearable:!1,showCheckbox:!0,lazy:!0,options:W(()=>s.stationTree),lazyLoad:(e,t)=>{var a,r;if(e.level===0)return t([]);if(((a=e.data.children)==null?void 0:a.length)>0)return t(e.data.children);if(e.isLeaf)return t([]);if((r=e.data)!=null&&r.isLeaf)return t([]);me({stationId:e.data.id}).then(o=>{var i;const l=(i=o.data)==null?void 0:i.map(u=>({label:u.type,value:"",id:"",children:u.attrList.map(n=>({label:n.name,value:n.id,id:n.id,isLeaf:!0}))}));return t(l)})}},{type:"radio-button",field:"type",options:[{label:"日",value:"day"},{label:"月",value:"month"},{label:"年",value:"year"}],label:"时间频率"},{type:"datetimerange",label:"选择日期",field:"day",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择日期",field:"month",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择日期",field:"year",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="day"}},{type:"select",label:"时间间隔:",field:"queryType",clearable:!1,allowCreate:!0,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"},options:j,itemContainerStyle:{width:"180px"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var t;d.pagination.page=1;const e=((t=y.value)==null?void 0:t.queryParams)||{};e.attributeId&&e.attributeId.length>0?S():w("选择监测点")},svgIcon:T(ne)},{type:"default",perm:!0,text:"重置",svgIcon:T(re),click:()=>{var e;(e=y.value)==null||e.resetForm()}},{text:"导出",perm:!0,type:"warning",svgIcon:T(le),hide:()=>s.activeName!=="list",click:()=>z()}]}]}),d=g({loading:!1,dataList:[],columns:[],operations:[],pagination:{refreshData:({page:e,size:t})=>{d.pagination.page=e,d.pagination.limit=t,d.dataList=h==null?void 0:h.slice((e-1)*t,e*t)}}}),z=()=>{var e;if(d.dataList.length>0){const t=((e=y.value)==null?void 0:e.queryParams)||{};console.log(t);const[a,r]=t[t.type]||[];let o=0,l=0;t.type==="day"?(o=a?c(a).valueOf():"",l=r?c(r).valueOf():""):(o=a?c(a).startOf(t.type).valueOf():"",l=r?c(r).endOf(t.type).valueOf():"");const i={attributes:t.attributeId.join(","),queryType:t.type==="month"?"day":t.type==="year"?"month":t.queryType,start:o,end:l};ce(i).then(u=>{const n=window.URL.createObjectURL(u.data),p=document.createElement("a");p.style.display="none",p.href=n,p.setAttribute("download","数据对比表.xlsx"),document.body.appendChild(p),p.click()})}else w("无数据导出")},S=()=>{var i;d.loading=!0;const e=((i=y.value)==null?void 0:i.queryParams)||{};console.log(e);let t=0,a=0;const[r,o]=e[e.type]||[];e.type==="day"?(t=r?c(r).valueOf():"",a=o?c(o).valueOf():""):(t=r?c(r).startOf(e.type).valueOf():"",a=o?c(o).endOf(e.type).valueOf():"");const l={attributes:e.attributeId.join(","),queryType:e.type==="month"?"day":e.type==="year"?"month":e.queryType,start:t,end:a};de(l).then(u=>{var b;const n=(b=u.data)==null?void 0:b.data;s.data=n,h=n==null?void 0:n.tableDataList;const p=pe(n==null?void 0:n.tableInfo);d.columns=p,d.dataList=h.slice(0*20,1*20),d.pagination.total=n==null?void 0:n.tableDataList.length,d.loading=!1,L()})},F=()=>{var e;(e=x.value)==null||e.resize()},L=()=>{var a,r,o;const e=ie();e.series=[];const t={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:C().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:C().isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.xAxis.data=(a=s.data)==null?void 0:a.tableDataList.map(l=>l.ts),(r=s.data)==null||r.tableInfo.map((l,i)=>{var u;if(l.columnValue!=="ts"){const n=JSON.parse(JSON.stringify(t));n.name=l.columnName,n.data=(u=s.data)==null?void 0:u.tableDataList.map(b=>b[l.columnValue]);const p=l.columnName.split("--")[2]+(l.unit?"("+l.unit+")":"");i===1?e.yAxis[0].name=p:i>1&&(e.yAxis.find(K=>K.name===p)||(n.yAxisIndex=i-1,e.grid.right=50*(i-1),e.yAxis.push({position:"right",alignTicks:!0,type:"value",name:p,offset:50*(i-2),axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}))),e.series.push(n)}}),(o=x.value)==null||o.clear(),Q(()=>{k.value&&I.listenTo(k.value,()=>{s.chartOption=e,F()})})};return $(async()=>{const e=await P("水厂");await V(e,["Project","Station"],!1,"Station"),s.stationTree=e,console.log(" state.stationTree ",s.stationTree)}),(e,t)=>{const a=oe,r=ee,o=te,l=X("VChart"),i=ae,u=G;return Y(),Z("div",fe,[f(a,{ref_key:"cardSearch",ref:y,config:m(R)},null,8,["config"]),f(u,{class:"card",title:" "},{right:_(()=>[f(o,{modelValue:m(s).activeName,"onUpdate:modelValue":t[0]||(t[0]=n=>m(s).activeName=n)},{default:_(()=>[f(r,{label:"echarts"},{default:_(()=>[f(m(q),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),f(r,{label:"list"},{default:_(()=>[f(m(q),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:_(()=>[D(A("div",{ref_key:"agriEcoDev",ref:k,class:"chart-box"},[f(l,{ref_key:"refChart",ref:x,theme:m(C)().isDark?"dark":"light",option:m(s).chartOption},null,8,["theme","option"])],512),[[N,m(s).activeName==="echarts"]]),D(A("div",null,[f(i,{ref_key:"refCardTable",ref:B,class:"card-table",config:m(d)},null,8,["config"])],512),[[N,m(s).activeName==="list"]])]),_:1})])}}}),Oe=se(ye,[["__scopeId","data-v-efaefb7c"]]);export{Oe as default};
