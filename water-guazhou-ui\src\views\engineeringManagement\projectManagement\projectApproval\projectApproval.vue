<!-- 工程管理-项目管理-项目立项 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refDeviceForm" :config="deviceConfig"></DialogForm>
    <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
    <DialogForm ref="refAddDeviceForm" :config="AdddeviceConfig"></DialogForm>
    <SLDrawer ref="refDetail" :config="detailConfig">
      <detail :config="data.selected" :basic="basicConfig" :show="1"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';

import {
  getProjectList,
  postProject,
  getprojectExport,
  deleteprojectExport,
  deleteDeviceItemExport
} from '@/api/engineeringManagement/projectManagement';
import { getProjectType } from '@/api/engineeringManagement/manage';
import {
  getDeviceList,
  postProjectDevice,
  getProjectDevice
} from '@/api/engineeringManagement/device';
import useGlobal from '@/hooks/global/useGlobal';
import { formatDate } from '@/utils/DateFormatter';
import detail from '../../components/detail.vue';
import { traverse } from '@/utils/GlobalHelper';
import { SLConfirm } from '@/utils/Message';

const { $btnPerms } = useGlobal();
const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();
const refDetail = ref<ISLDrawerIns>();
const refDeviceForm = ref<IDialogFormIns>();
const refAddDeviceForm = ref<IDialogFormIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '项目编号', field: 'code', type: 'input' },
    { label: '项目名称', field: 'name', type: 'input' },
    {
      label: '项目类别',
      field: 'typeId',
      type: 'select',
      options: computed(() => data.projectType) as any
    },
    { label: '启动时间', field: 'time', type: 'daterange', format: 'x' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '新增',
          type: 'success',
          icon: ICONS.ADD,
          click: () => clickCreatedRole()
        },
        {
          type: 'default',
          perm: true,
          text: '导出',
          icon: ICONS.DOWNLOAD,
          click: () => {
            getprojectExport().then((res) => {
              const url = window.URL.createObjectURL(res.data);
              const link = document.createElement('a');
              link.style.display = 'none';
              link.href = url;
              link.setAttribute('download', `项目立项.xlsx`);
              document.body.appendChild(link);
              link.click();
            });
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '项目编号', prop: 'code' },
    { label: '项目名称', prop: 'name' },
    { label: '项目类别', prop: 'typeName' },
    { label: '项目概算(万元)', prop: 'estimate' },
    {
      label: '启动时间',
      prop: 'startTime',
      formatter: (row) => formatDate(row.startTime, 'YYYY-MM-DD')
    },
    {
      label: '预计结束时间',
      prop: 'expectEndTime',
      formatter: (row) => formatDate(row.expectEndTime, 'YYYY-MM-DD')
    },
    { label: '项目负责人', prop: 'principal' },
    { label: '设备状态', prop: 'cs' }
  ],
  operationWidth: '350px',
  operations: [
    {
      isTextBtn: false,
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => {
        data.selected = row;
        refDetail.value?.openDrawer();
      }
    },
    {
      isTextBtn: false,
      text: '查看设备',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => {
        deviceConfig.defaultValue = { projectCode: row.code };
        data.getProjectDeviceValue();
        refDeviceForm.value?.openDialog();
      }
    },
    {
      isTextBtn: false,
      type: 'success',
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => clickEdit(row)
    },
    {
      disabled: (row) => !row.canBeDelete,
      isTextBtn: false,
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => {
        SLConfirm('确定删除？', '提示信息').then(() => {
          deleteprojectExport(row.id)
            .then((res) => {
              if (res.data?.code === 200) {
                ElMessage.success('删除成功');
                refreshData();
              } else {
                ElMessage.warning('删除失败');
              }
            })
            .catch((error) => {
              ElMessage.warning(error);
            });
        });
      }
    },
    {
      isTextBtn: false,
      text: '添加设备',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => {
        const table = AdddeviceConfig.group[0].fields.find(
          (item) => item.type === 'table'
        ) as IFormTable;
        table.config.selectList = [];
        AdddeviceConfig.defaultValue = { projectCode: row.code };
        data.geDeviceListValue();
        refAddDeviceForm.value?.openDialog();
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const deviceConfig = reactive<IDialogFormConfig>({
  title: '查看设备',
  labelWidth: '130px',
  dialogWidth: '1000px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'table',
          field: 'deviceTable',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceInformation) as any,
            columns: [
              { label: '设备名称', prop: 'deviceName' },
              { label: '设备编码', prop: 'serialId' },
              { label: '设备型号', prop: 'model' },
              { label: '所属大类', prop: 'deviceTopTypeName' },
              { label: '所属分类', prop: 'deviceType' },
              { label: '设备标识', prop: 'mark' },
              { label: '计量单位', prop: 'unit' },
              { label: '清单总量', prop: 'amount' }
            ],
            operations: [
              {
                perm: true,
                text: '删除',
                icon: ICONS.DELETE,
                click: (row) => {
                  SLConfirm('确定删除该设备', '删除提示').then(() => {
                    deleteDeviceItemExport(row.id)
                      .then(() => {
                        ElMessage.success('删除成功');
                        data.getProjectDeviceValue();
                      })
                      .catch((error) => {
                        ElMessage.warning(error);
                      });
                  });
                }
              }
            ],
            pagination: {
              page: 1,
              limit: 20,
              total: 0,
              refreshData: ({ page, size }) => {
                const table = deviceConfig.group[0].fields.find(
                  (item) => item.type === 'table'
                ) as IFormTable;
                table.config.pagination.page = page;
                table.config.pagination.limit = size;
                data.getProjectDeviceValue();
              }
            }
          }
        }
      ]
    }
  ]
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加项目',
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    if (
      params.expectEndTime &&
      params.startTime &&
      params.expectEndTime < params.startTime
    ) {
      ElMessage.warning('预计结束时间不能小于开始时间');
      return;
    }
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    postProject(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
          refreshData();
        } else {
          ElMessage.warning(text + '失败');
        }
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '项目编号',
          field: 'code'
        },
        {
          xs: 12,
          type: 'input',
          label: '项目名称',
          field: 'name',
          rules: [{ required: true, message: '请输入项目名称' }]
        },
        {
          xs: 12,
          type: 'select',
          label: '项目类别',
          field: 'typeId',
          options: computed(() => data.projectType) as any
        },
        {
          xs: 12,
          type: 'input',
          label: '项目规模',
          field: 'scale'
        },
        {
          xs: 12,
          type: 'input',
          label: '项目负责人',
          field: 'principal',
          rules: [{ required: true, message: '请输入项目负责人' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '联系电话',
          field: 'phone'
        },
        {
          xs: 12,
          type: 'input',
          label: '项目单位',
          field: 'organization',
          rules: [{ required: true, message: '请输入项目单位' }]
        },
        {
          xs: 12,
          type: 'number',
          label: '项目概算(万元)',
          field: 'estimate'
        },
        {
          xs: 12,
          type: 'date',
          format: 'x',
          label: '项目启动时间',
          field: 'startTime'
        },
        {
          xs: 12,
          type: 'date',
          format: 'x',
          label: '项目预计结算时间',
          field: 'expectEndTime'
        },
        {
          type: 'input',
          label: '详细地址',
          field: 'address'
        },
        {
          type: 'input',
          label: '项目概况',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

const AdddeviceConfig = reactive<IDialogFormConfig>({
  title: '添加设备',
  labelWidth: '80px',
  dialogWidth: '1000px',
  defaultValue: {},
  submitting: false,
  submit: (params: any, status: boolean) => {
    if (status) {
      data.geDeviceListValue(params);
    } else {
      let status = false;
      const table = AdddeviceConfig.group[0].fields.find(
        (item) => item.type === 'table'
      ) as IFormTable;
      if (table.config.selectList?.length === 0) {
        ElMessage.warning('请选中设备');
        status = true;
      }
      const val: any = table.config.selectList?.map((item) => {
        if (item.amount === 0 || !item.amount) {
          ElMessage.warning('数量最少为1台');
          status = true;
        }
        return {
          serialId: item.serialId,
          amount: item.amount || 0
        };
      });
      if (status) {
        return;
      }
      AdddeviceConfig.submitting = true;
      postProjectDevice(params.projectCode, val)
        .then((res) => {
          AdddeviceConfig.submitting = false;
          if (res.data.code === 200) {
            ElMessage.success('添加成功');
            refAddDeviceForm.value?.closeDialog();
            refreshData();
          } else {
            ElMessage.warning('添加失败');
          }
        })
        .catch((error) => {
          AdddeviceConfig.submitting = false;
          ElMessage.warning(error);
        });
    }
  },
  group: [
    {
      fields: [
        {
          xs: 6,
          type: 'input',
          field: 'serialId',
          label: '设备编码'
        },
        {
          xs: 6,
          type: 'input',
          field: 'name',
          label: '设备名称'
        },
        {
          xs: 6,
          type: 'input',
          field: 'model',
          label: '设备型号'
        },
        {
          xs: 6,
          type: 'btn-group',
          btns: [
            {
              text: '查询',
              perm: true,
              click: () => {
                refAddDeviceForm.value?.Submit(true);
              }
            },
            {
              text: '重置',
              perm: true,
              type: 'default',
              click: () => {
                refAddDeviceForm.value?.resetForm();
                data.geDeviceListValue();
              }
            }
          ]
        },
        {
          type: 'table',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceInformation) as any,
            handleSelectChange: (val) => {
              const table = AdddeviceConfig.group[0].fields.find(
                (item) => item.type === 'table'
              ) as IFormTable;
              table.config.selectList = val;
            },
            selectList: [],
            columns: [
              { label: '设备名称', prop: 'name' },
              { label: '设备编码', prop: 'serialId' },
              { label: '设备型号', prop: 'model' },
              { label: '所属大类', prop: 'topTypeName' },
              { label: '所属分类', prop: 'typeName' },
              { label: '设备标识', prop: 'mark' },
              { label: '计量单位', prop: 'unit' },
              {
                label: '申请数量',
                prop: 'amount',
                minWidth: '120px',
                formItemConfig: {
                  type: 'number',
                  field: 'amount',
                  min: 0
                }
              }
            ],
            pagination: {
              page: 1,
              limit: 20,
              total: 0,
              refreshData: ({ page, size }) => {
                const table = AdddeviceConfig.group[0].fields.find(
                  (item) => item.type === 'table'
                ) as IFormTable;
                table.config.pagination.page = page;
                table.config.pagination.limit = size;
                data.geDeviceListValue();
              }
            }
          }
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  group: [],
  width: '80%',
  modalClass: 'lightColor',
  cancel: false
});

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => data.selected) as any,
  border: false,
  direction: 'horizontal',
  column: 1,
  title: '项目基础信息',
  fields: [
    { type: 'text', label: '项目编号:', field: 'code' },
    { type: 'text', label: '项目名称:', field: 'name' },
    { type: 'text', label: '项目类别:', field: 'typeName' },
    { type: 'text', label: '项目规模:', field: 'scale' },
    { type: 'text', label: '项目单位:', field: 'organization' },
    { type: 'text', label: '项目负责人:', field: 'principal' },
    { type: 'text', label: '联系电话:', field: 'phone' },
    { type: 'text', label: '项目概算(万元):', field: 'estimate' },
    {
      type: 'text',
      label: '项目启动时间:',
      field: 'startTimeName',
      formatter: (row) => dayjs(row).format('YYYY-MM-DD')
    },
    {
      type: 'text',
      label: '项目预计结束时间:',
      field: 'expectEndTimeName',
      formatter: (row) => dayjs(row).format('YYYY-MM-DD')
    },
    { type: 'text', label: '详细地址:', field: 'address' },
    { type: 'text', label: '项目概况:', field: 'remark' },
    { type: 'text', label: '创建人:', field: 'creatorName' },
    { type: 'text', label: '创建时间:', field: 'createTimeName' },
    { type: 'text', label: '最后更新人:', field: 'updateUserName' },
    { type: 'text', label: '最后更新时间:', field: 'updateTimeName' }
  ]
});

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增';
  addOrUpdateConfig.defaultValue = {};
  refForm.value?.openDialog();
};

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑';
  addOrUpdateConfig.defaultValue = { ...(row || {}) };
  refForm.value?.openDialog();
};

const data = reactive({
  // 项目类别
  projectType: [],
  // 设备信息
  deviceInformation: [],
  selected: {},
  getOptions: () => {
    getProjectType({ page: 1, size: -1 }).then((res) => {
      data.projectType = traverse(res.data.data.data || []);
    });
  },
  geDeviceListValue: (val?: any) => {
    data.deviceInformation = [];
    const table = AdddeviceConfig.group[0].fields.find(
      (item) => item.type === 'table'
    ) as IFormTable;
    const params = {
      page: table.config.pagination.page || 1,
      size: table.config.pagination.limit || 20,
      ...val
    };
    getDeviceList(params).then((res) => {
      data.deviceInformation = res.data.data.data || [];
      table.config.pagination.total = res.data.data.total || 0;
    });
  },
  getProjectDeviceValue: () => {
    data.deviceInformation = [];
    const table = deviceConfig.group[0].fields.find(
      (item) => item.type === 'table'
    ) as IFormTable;
    const params = {
      page: table.config.pagination.page || 1,
      size: table.config.pagination.limit || 20
    };
    getProjectDevice(deviceConfig.defaultValue?.projectCode, params).then(
      (res) => {
        data.deviceInformation = res.data.data.data || [];
        table.config.pagination.total = res.data.data.total || 0;
      }
    );
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  };
  if (params?.time) {
    params.startTimeFrom = params.time[0];
    params.startTimeTo = params.time[1];
    delete params.time;
  }
  getProjectList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  data.getOptions();
  data.geDeviceListValue();
});
</script>
