package org.thingsboard.server.dao.smartService.call;

import org.thingsboard.server.common.data.page.PageData;

import java.util.List;

/**
 * 知识库公告
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface CallLogService {
    PageData getList(String phone, Long startTime, Long endTime, String source, String area, String type, String topic, String seatsId, String status, String direction, int page, int size, String tenantId);

    List getQueueMonitor(String day);
}
