"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9296],{16453:(t,e,r)=>{r.d(e,{R:()=>m,w:()=>I});var s=r(43697),i=r(15923),o=r(70586),a=r(41103),l=r(22974),n=r(31263);class u{constructor(){this._propertyOriginMap=new Map,this._originStores=new Array(n.kk),this._values=new Map,this.multipleOriginsSupported=!0}clone(t){const e=new u,r=this._originStores[n.s3.DEFAULTS];r&&r.forEach(((t,r)=>{e.set(r,(0,l.d9)(t),n.s3.DEFAULTS)}));for(let r=n.s3.SERVICE;r<n.kk;r++){const s=this._originStores[r];s&&s.forEach(((s,i)=>{t&&t.has(i)||e.set(i,(0,l.d9)(s),r)}))}return e}get(t,e){const r=void 0===e?this._values:this._originStores[e];return r?r.get(t):void 0}keys(t){const e=null==t?this._values:this._originStores[t];return e?[...e.keys()]:[]}set(t,e,r=n.s3.USER){let s=this._originStores[r];if(s||(s=new Map,this._originStores[r]=s),s.set(t,e),!this._values.has(t)||(0,o.j0)(this._propertyOriginMap.get(t))<=r){const s=this._values.get(t);return this._values.set(t,e),this._propertyOriginMap.set(t,r),s!==e}return!1}delete(t,e=n.s3.USER){const r=this._originStores[e];if(!r)return;const s=r.get(t);if(r.delete(t),this._values.has(t)&&this._propertyOriginMap.get(t)===e){this._values.delete(t);for(let r=e-1;r>=0;r--){const e=this._originStores[r];if(e&&e.has(t)){this._values.set(t,e.get(t)),this._propertyOriginMap.set(t,r);break}}}return s}has(t,e){const r=void 0===e?this._values:this._originStores[e];return!!r&&r.has(t)}revert(t,e){for(;e>0&&!this.has(t,e);)--e;const r=this._originStores[e],s=r&&r.get(t),i=this._values.get(t);return this._values.set(t,s),this._propertyOriginMap.set(t,e),i!==s}originOf(t){return this._propertyOriginMap.get(t)||n.s3.DEFAULTS}forEach(t){this._values.forEach(t)}}var p=r(50549),d=r(1153),h=r(52011);const c=t=>{let e=class extends t{constructor(...t){super(...t);const e=(0,o.j0)((0,d.vw)(this)),r=e.store,s=new u;e.store=s,(0,a.M)(e,r,s)}read(t,e){(0,p.i)(this,t,e)}getAtOrigin(t,e){const r=g(this),s=(0,n.M9)(e);if("string"==typeof t)return r.get(t,s);const i={};return t.forEach((t=>{i[t]=r.get(t,s)})),i}originOf(t){return(0,n.x3)(this.originIdOf(t))}originIdOf(t){return g(this).originOf(t)}revert(t,e){const r=g(this),s=(0,n.M9)(e),i=(0,d.vw)(this);let o;o="string"==typeof t?"*"===t?r.keys(s):[t]:t,o.forEach((t=>{i.invalidate(t),r.revert(t,s),i.commit(t)}))}};return e=(0,s._)([(0,h.j)("esri.core.ReadOnlyMultiOriginJSONSupport")],e),e};function g(t){return(0,d.vw)(t).store}let f=class extends(c(i.Z)){};f=(0,s._)([(0,h.j)("esri.core.ReadOnlyMultiOriginJSONSupport")],f);var y=r(76169);const v=t=>{let e=class extends t{constructor(...t){super(...t)}clear(t,e="user"){return _(this).delete(t,(0,n.M9)(e))}write(t={},e){return(0,y.c)(this,t=t||{},e),t}setAtOrigin(t,e,r){(0,d.vw)(this).setAtOrigin(t,e,(0,n.M9)(r))}removeOrigin(t){const e=_(this),r=(0,n.M9)(t),s=e.keys(r);for(const t of s)e.originOf(t)===r&&e.set(t,e.get(t,r),n.s3.USER)}updateOrigin(t,e){const r=_(this),s=(0,n.M9)(e),i=this.get(t);for(let e=s+1;e<n.kk;++e)r.delete(t,e);r.set(t,i,s)}toJSON(t){return this.write({},t)}};return e=(0,s._)([(0,h.j)("esri.core.WriteableMultiOriginJSONSupport")],e),e.prototype.toJSON.isDefaultToJSON=!0,e};function _(t){return(0,d.vw)(t).store}const m=t=>{let e=class extends(v(c(t))){constructor(...t){super(...t)}};return e=(0,s._)([(0,h.j)("esri.core.MultiOriginJSONSupport")],e),e};let I=class extends(m(i.Z)){};I=(0,s._)([(0,h.j)("esri.core.MultiOriginJSONSupport")],I)},39296:(t,e,r)=>{r.r(e),r.d(e,{default:()=>h});var s=r(43697),i=r(20102),o=r(16453),a=r(1654),l=r(5600),n=(r(75215),r(67676),r(52011)),u=r(87085),p=r(16859);let d=class extends((0,p.I)((0,o.R)(u.Z))){constructor(t){super(t),this.resourceInfo=null,this.type="unsupported"}initialize(){this.addResolvingPromise(new Promise(((t,e)=>{(0,a.Os)((()=>{const t=this.resourceInfo&&(this.resourceInfo.layerType||this.resourceInfo.type);let r="Unsupported layer type";t&&(r+=" "+t),e(new i.Z("layer:unsupported-layer-type",r,{layerType:t}))}))})))}read(t,e){const r={resourceInfo:t};null!=t.id&&(r.id=t.id),null!=t.title&&(r.title=t.title),super.read(r,e)}write(t){return Object.assign(t||{},this.resourceInfo,{id:this.id})}};(0,s._)([(0,l.Cb)({readOnly:!0})],d.prototype,"resourceInfo",void 0),(0,s._)([(0,l.Cb)({type:["show","hide"]})],d.prototype,"listMode",void 0),(0,s._)([(0,l.Cb)({json:{read:!1},readOnly:!0,value:"unsupported"})],d.prototype,"type",void 0),d=(0,s._)([(0,n.j)("esri.layers.UnsupportedLayer")],d);const h=d},16859:(t,e,r)=>{r.d(e,{I:()=>O});var s=r(43697),i=r(68773),o=r(40330),a=r(3172),l=r(66643),n=r(20102),u=r(92604),p=r(70586),d=r(95330),h=r(17452),c=r(5600),g=(r(75215),r(67676),r(71715)),f=r(52011),y=r(30556),v=r(84230),_=r(65587),m=r(15235),I=r(86082),w=r(14661);const O=t=>{let e=class extends t{constructor(){super(...arguments),this.resourceReferences={portalItem:null,paths:[]},this.userHasEditingPrivileges=!0,this.userHasFullEditingPrivileges=!1,this.userHasUpdateItemPrivileges=!1}destroy(){this.portalItem=(0,p.SC)(this.portalItem)}set portalItem(t){t!==this._get("portalItem")&&(this.removeOrigin("portal-item"),this._set("portalItem",t))}readPortalItem(t,e,r){if(e.itemId)return new m.default({id:e.itemId,portal:r&&r.portal})}writePortalItem(t,e){t&&t.id&&(e.itemId=t.id)}async loadFromPortal(t,e){if(this.portalItem&&this.portalItem.id)try{const s=await r.e(8062).then(r.bind(r,18062));return(0,d.k_)(e),await s.load({instance:this,supportedTypes:t.supportedTypes,validateItem:t.validateItem,supportsData:t.supportsData,layerModuleTypeMap:t.layerModuleTypeMap},e)}catch(t){throw(0,d.D_)(t)||u.Z.getLogger(this.declaredClass).warn(`Failed to load layer (${this.title}, ${this.id}) portal item (${this.portalItem.id})\n  ${t}`),t}}async finishLoadEditablePortalLayer(t){this._set("userHasEditingPrivileges",await this._fetchUserHasEditingPrivileges(t).catch((t=>((0,d.r9)(t),!0))))}async _setUserPrivileges(t,e){if(!i.Z.userPrivilegesApplied)return this.finishLoadEditablePortalLayer(e);if(this.url)try{const{features:{edit:r,fullEdit:s},content:{updateItem:i}}=await this._fetchUserPrivileges(t,e);this._set("userHasEditingPrivileges",r),this._set("userHasFullEditingPrivileges",s),this._set("userHasUpdateItemPrivileges",i)}catch(t){(0,d.r9)(t)}}async _fetchUserPrivileges(t,e){let r=this.portalItem;if(!t||!r||!r.loaded||r.sourceUrl)return this._fetchFallbackUserPrivileges(e);const s=t===r.id;if(s&&r.portal.user)return(0,w.Ss)(r);let i,a;if(s)i=r.portal.url;else try{i=await(0,v.oP)(this.url,e)}catch(t){(0,d.r9)(t)}if(!i||!(0,h.Zo)(i,r.portal.url))return this._fetchFallbackUserPrivileges(e);try{const t=(0,p.pC)(e)?e.signal:null;a=await(o.id?.getCredential(`${i}/sharing`,{prompt:!1,signal:t}))}catch(t){(0,d.r9)(t)}if(!a)return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}};try{if(s?await r.reload():(r=new m.default({id:t,portal:{url:i}}),await r.load(e)),r.portal.user)return(0,w.Ss)(r)}catch(t){(0,d.r9)(t)}return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}}}async _fetchFallbackUserPrivileges(t){let e=!0;try{e=await this._fetchUserHasEditingPrivileges(t)}catch(t){(0,d.r9)(t)}return{features:{edit:e,fullEdit:!1},content:{updateItem:!1}}}async _fetchUserHasEditingPrivileges(t){const e=this.url?o.id?.findCredential(this.url):null;if(!e)return!0;const r=S.credential===e?S.user:await this._fetchEditingUser(t);return S.credential=e,S.user=r,(0,p.Wi)(r)||null==r.privileges||r.privileges.includes("features:user:edit")}async _fetchEditingUser(t){const e=this.portalItem?.portal?.user;if(e)return e;const r=o.id.findServerInfo(this.url??"");if(!r?.owningSystemUrl)return null;const s=`${r.owningSystemUrl}/sharing/rest`,i=_.Z.getDefault();if(i&&i.loaded&&(0,h.Fv)(i.restUrl)===(0,h.Fv)(s))return i.user;const n=`${s}/community/self`,u=(0,p.pC)(t)?t.signal:null,d=await(0,l.q6)((0,a.default)(n,{authMode:"no-prompt",query:{f:"json"},signal:u}));return d.ok?I.default.fromJSON(d.value.data):null}read(t,e){e&&(e.layer=this),super.read(t,e)}write(t,e){const r=e&&e.portal,s=this.portalItem&&this.portalItem.id&&(this.portalItem.portal||_.Z.getDefault());return r&&s&&!(0,h.tm)(s.restUrl,r.restUrl)?(e.messages&&e.messages.push(new n.Z("layer:cross-portal",`The layer '${this.title} (${this.id})' cannot be persisted because it refers to an item on a different portal than the one being saved to. To save, set layer.portalItem to null or save to the same portal as the item associated with the layer`,{layer:this})),null):super.write(t,{...e,layer:this})}};return(0,s._)([(0,c.Cb)({type:m.default})],e.prototype,"portalItem",null),(0,s._)([(0,g.r)("web-document","portalItem",["itemId"])],e.prototype,"readPortalItem",null),(0,s._)([(0,y.c)("web-document","portalItem",{itemId:{type:String}})],e.prototype,"writePortalItem",null),(0,s._)([(0,c.Cb)({clonable:!1})],e.prototype,"resourceReferences",void 0),(0,s._)([(0,c.Cb)({type:Boolean,readOnly:!0})],e.prototype,"userHasEditingPrivileges",void 0),(0,s._)([(0,c.Cb)({type:Boolean,readOnly:!0})],e.prototype,"userHasFullEditingPrivileges",void 0),(0,s._)([(0,c.Cb)({type:Boolean,readOnly:!0})],e.prototype,"userHasUpdateItemPrivileges",void 0),e=(0,s._)([(0,f.j)("esri.layers.mixins.PortalLayer")],e),e},S={credential:null,user:null}}}]);