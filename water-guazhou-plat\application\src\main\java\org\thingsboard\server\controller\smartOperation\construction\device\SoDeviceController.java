package org.thingsboard.server.controller.smartOperation.construction.device;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDevice;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDevicePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDeviceSaveRequest;
import org.thingsboard.server.dao.construction.device.SoDeviceService;
import org.thingsboard.server.dao.construction.device.SoDeviceTypeService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.List;

import static org.thingsboard.server.common.data.exception.ThingsboardErrorCode.GENERAL;

@IStarController2
@RequestMapping("/api/so/device")
public class SoDeviceController extends BaseController {
    @Autowired
    private SoDeviceService service;

    @Autowired
    private SoDeviceTypeService typeService;


    @GetMapping
    public IPage<SoDevice> findAllConditional(SoDevicePageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public SoDevice save(@RequestBody SoDeviceSaveRequest req) throws ThingsboardException {
        checkSerialId(req.getSerialId(), req.getTypeSerialId(), req.tenantId(), req.getId());
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoDeviceSaveRequest req, @PathVariable String id) throws ThingsboardException {
        checkSerialId(req.getSerialId(), req.getTypeSerialId(),id, req.tenantId());
        return service.update(req.unwrap(id));
    }

    private void checkSerialId(String serialId, String typeSerialId, String tenantId, String id) throws ThingsboardException {
        if (serialId.startsWith("00000000")) {
            throw new ThingsboardException("只有类别末级可添加设备信息", GENERAL);
        }

        if (!typeService.existsBySerialId(typeSerialId, tenantId)) {
            throw new ThingsboardException("类别不存在", GENERAL);
        } else {
            if (typeService.getDepthBySerialId(typeSerialId, tenantId) != 3) {
                throw new ThingsboardException("只有类别末级可添加设备信息", GENERAL);
            }
        }

        if (service.isSerialIdExists(serialId, id, tenantId)) {
            throw new ThingsboardException("编码重复", GENERAL);
        }
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

    @DeleteMapping
    public boolean delete(List<String> idList) {
        return service.deleteBatch(idList);
    }
}