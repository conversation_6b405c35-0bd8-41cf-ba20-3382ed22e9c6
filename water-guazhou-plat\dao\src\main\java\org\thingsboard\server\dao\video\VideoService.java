/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.video;

import org.springframework.data.domain.Page;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.ny.NyVideoBO;
import org.thingsboard.server.dao.model.DTO.TreeNodeDTO;
import org.thingsboard.server.dao.model.DTO.VideoSaveDTO;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.util.imodel.query.video.VideoMonitoringPageRequest;

import java.util.List;

public interface VideoService {

    VideoEntity save(VideoEntity videoEntity);

    List<VideoEntity> findByProject(String projectId, String name, String isBind, String groupId);

    boolean delete(String id);

    boolean deleteAll(List<String> ids);

    VideoEntity save(NyVideoBO nyVideoBO, TenantId tenantId);

    List<VideoEntity> findByProjectAndType(String projectId, String type);

    List<VideoEntity> findByType(String type, TenantId tenantId);

    List<VideoEntity> findAll(String name, String tenantId);

    void batchSave(VideoSaveDTO videoEntityList, String tenantId);

    List<TreeNodeDTO> getTreeByProjectId(String projectId, String tenantId);

    /**
     * 分页查询视频列表
     *
     * @param request 分页查询条件
     * @return 分页数据
     */
    Page<VideoEntity> findByPage(VideoMonitoringPageRequest request);
}
