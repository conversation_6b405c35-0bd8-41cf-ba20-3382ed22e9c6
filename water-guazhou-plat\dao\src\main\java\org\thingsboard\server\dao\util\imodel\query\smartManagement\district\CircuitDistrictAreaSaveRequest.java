package org.thingsboard.server.dao.util.imodel.query.smartManagement.district;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrictArea;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class CircuitDistrictAreaSaveRequest extends SaveRequest<SMCircuitDistrictArea> {
    // 类型：区域或路线
    @NotNullOrEmpty
    private String type;

    // 片区名称
    @NotNullOrEmpty
    private String name;

    // 所属片区Id
    @NotNullOrEmpty
    private String districtId;

    // 缓冲距离
    private String buffer;

    // 点阵JSON
    @NotNullOrEmpty
    private String points;

    @Override
    protected SMCircuitDistrictArea build() {
        SMCircuitDistrictArea entity = new SMCircuitDistrictArea();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SMCircuitDistrictArea update(String id) {
        SMCircuitDistrictArea entity = new SMCircuitDistrictArea();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SMCircuitDistrictArea entity) {
        entity.setType(type);
        entity.setName(name);
        entity.setDistrictId(districtId);
        entity.setPoints(points);
        entity.setBuffer(buffer);
    }
}