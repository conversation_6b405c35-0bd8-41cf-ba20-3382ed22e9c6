import { QueryListParam } from '@/common/types/common';
import {
  IWorkOrderAcceptanceParams,
  IWorkOrderComfirmParams,
  IWorkOrderListParams,
  IWorkOrder,
  IWorkOrderSaveParams,
  ICounterOrderParams
} from '@/common/types/workorder';
import request from '@/plugins/axios';
/**
 * 工单验证
 * @param params
 * @returns
 */
export const acceptanceOrder = (params?: IWorkOrderAcceptanceParams) =>
  request({
    method: 'post',
    url: '/api/workOrder/acceptance',
    data: params
  });
/**
 * 工单提交验证
 * @param params
 * @returns
 */
export const processOrder = (params?: IWorkOrder) =>
  request({
    method: 'post',
    url: '/api/workOrder/process',
    data: params
  });

/**
 * 工单接单
 * @param params
 * @returns
 */
export const confirmOrder = (params?: IWorkOrderComfirmParams) =>
  request({
    method: 'post',
    url: '/api/workOrder/confirm',
    data: params
  });
/**
 * 工单详情
 * @param id
 * @returns
 */
export const orderDetail = (id: string) =>
  request({
    method: 'get',
    url: `/api/workOrder/detail/${id}`
  });
/**
 * 查询工单列表
 * @param params
 * @returns
 */
export const orderList = (params: IWorkOrderListParams) => {
  let paramsString = '?';
  for (const key in params) {
    paramsString += `&${key}=${params[key]}`;
  }
  return request({
    method: 'get',
    url: `/api/workOrder/list${paramsString}`
  });
};

export const follow = (orderId?: string) =>
  request({
    method: 'post',
    url: '/api/workOrder/follow',
    data: {
      orderId
    }
  });

/**
 * 添加工单
 * @param params
 * @returns
 */
export const PostWorkOrder = (params: any) => {
  return request({
    url: '/api/workOrder',
    method: 'post',
    data: params
  });
};

/**
 * 查询工单明细
 * @param order_id
 * @returns
 */
export const GetWorkOrderDetail = (order_id: any) => {
  return request({
    url: `/api/workOrder/${order_id}`,
    method: 'get'
  });
};

/**
 * 接收工单
 * @param order_id
 * @param params
 * @returns
 */
export const ReceiveWorkOrder = (order_id: string) => {
  return request({
    url: `/api/workOrder/${order_id}/receive`,
    method: 'post',
    data: {
      processRemark: '',
      processAdditionalInfo: JSON.stringify({})
    }
  });
};
/**
 * 审核工单
 * @param order_id
 * @param params
 * @returns
 */
export const VerifyWorkOrder = (order_id: string, params: any) => {
  return request({
    url: `/api/workOrder/${order_id}/verify`,
    method: 'post',
    data: params
  });
};

/**
 * 添加工单流程
 * @param order_id
 * @param params
 * @returns
 */
export const AddWorkOrderStage = (order_id: string, params: any) => {
  return request({
    url: `/api/workOrder/${order_id}/stage`,
    method: 'post',
    data: params
  });
};
/**
 * 查询单个工单的工作流列表
 * @param order_id
 * @returns
 */
export const GetWorkOrderStages = (order_id: any) => {
  return request({
    url: `/api/workOrder/${order_id}/stages`,
    method: 'get'
  });
};
/**
 * 查询分页工单列表
 * @param params
 * @returns
 */
export const GetWorkOrderPage = (params: any) => {
  return request({
    url: '/api/workOrder',
    method: 'get',
    params
  });
};
/**
 * 查询设备资产分页工单列表
 * @param params
 * @returns
 */
export const GetEquipmentWorkOrderPage = (params: any) => {
  return request({
    url: '/api/fault/report/workOrder',
    method: 'get',
    params
  });
};
/**
 * 查询分页工单列表
 * @param params
 * @returns
 */
export const GetWorkOrderPageMine = (params: any) => {
  return request({
    url: '/api/workOrder/my',
    method: 'get',
    params
  });
};
/**
 * 派单
 * @param params
 * @returns
 */
export const DispatchWorkOrder = (
  order_id: string,
  params: {
    processUserId: string;
    stepProcessUserId: string;
    processLevel: string;
  }
) => {
  return request({
    url: `/api/workOrder/${order_id}/assign`,
    method: 'post',
    data: params
  });
};
/**
 * 更改处理人
 * @param params
 * @returns
 */
export const ChangeWorkOrderProcesser = (
  order_id: string,
  params: {
    stepProcessUserId: string;
  }
) => {
  return request({
    url: `/api/workOrder/${order_id}/reassign`,
    method: 'post',
    data: params
  });
};
/**
 * 终止工单
 * @param params
 * @returns
 */
export const TerminateWorkOrder = (
  order_id: string,
  params: {
    processRemark: string;
    processAdditionalInfo: string;
  }
) => {
  return request({
    url: `/api/workOrder/${order_id}/terminate`,
    method: 'post',
    data: params
  });
};
/**
 * 退单审核
 * @param params
 * @returns
 */
export const ChargeBackWorkOrder = (
  order_id: string,
  params: {
    processRemark: string;
    processAdditionalInfo: string;
    stage: string;
  }
) => {
  return request({
    url: `/api/workOrder/${order_id}/chargeback`,
    method: 'post',
    data: params
  });
};
/**
 * 申请退回工单
 * @param params
 * @returns
 */
export const ChargeBackWorkOrderApply = (
  order_id: string,
  params: {
    processRemark: string;
    processAdditionalInfo: string;
    nextProcessUserId: string;
  }
) => {
  return request({
    url: `/api/workOrder/${order_id}/chargebackRequest`,
    method: 'post',
    data: params
  });
};

/**
 * 复审工单
 * @param params
 * @returns
 */
export const ReviewWorkOrder = (
  order_id: string,
  params: {
    processRemark: string;
    processAdditionalInfo: string;
    nextProcessUserId: string;
    stage: string;
  }
) => {
  return request({
    url: `/api/workOrder/${order_id}/verify`,
    method: 'post',
    data: params
  });
};
/**
 * 查询工单数量统计
 * @param params
 * @returns
 */
export const GetWorkOrderCountStatistic = (params: {
  fromTime: number;
  timeUnit?: string;
  toTime: number;
  statisticOrganizer?: boolean;
  statisticType?: boolean;
}) => {
  return request({
    url: '/api/workOrder/count',
    method: 'get',
    params
  });
};

/**
 *
 * @param params
 * @returns
 */
export const HandOverWorkOrderApply = (
  order_id: string,
  params: {
    processAdditionalInfo: string;
    nextProcessUserId: string;
    expectUserId: string;
  }
) => {
  return request({
    url: `/api/workOrder/${order_id}/handoverRequest`,
    method: 'post',
    data: params
  });
};

/**
 * 申请协作
 * @param params
 * @returns
 */
export const collaborativeApplication = (
  id: string,
  params: {
    nextProcessUserId: string;
  }
) => {
  return request({
    url: `/api/workOrder/${id}/collaborate`,
    method: 'post',
    data: params
  });
};

/**
 * 查询工单完成率统计
 * @param params
 * @returns
 */
export const GetCompleteRatio = (params?: {
  fromTime: string | number;
  toTime: string | number;
  processUserId?: string;
}) => {
  return request({
    url: '/api/workOrder/completeCount',
    method: 'get',
    params
  });
};

/**
 * 查询工单类型
 * @returns
 */
export const getWorkOrderTypeList = (status?: string) => {
  return request({
    url: `/api/workOrderType/list`,
    method: 'get',
    params: {
      status
    }
  });
};

/**
 * 查询工单紧急程度列表
 * @returns
 */
export const getWorkOrderEmergencyLevelList = (status?: string) => {
  return request({
    url: `/api/workOrderEmergencyLevel/list`,
    method: 'get',
    params: {
      status
    }
  });
};

/**
 * 查询工单处理级别列表
 * @returns
 */
export const getWorkOrderProcessLevelList = (status?: string) => {
  return request({
    url: `/api/workOrderProcessLevel/list`,
    method: 'get',
    params: {
      status
    }
  });
};
