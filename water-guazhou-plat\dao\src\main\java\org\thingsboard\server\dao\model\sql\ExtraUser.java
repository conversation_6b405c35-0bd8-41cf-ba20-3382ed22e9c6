package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;

/**
 * (ExtraUser)实体类
 *
 * <AUTHOR>
 * @since 2020-05-09 11:30:25
 */
@Data
@Entity
@Table(name = ModelConstants.EXTRA_USER_TABLE)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class ExtraUser {
    private static final long serialVersionUID = 720140561605514897L;

    @Id
    private String id;
    /**
     * 名称
     */
    @Column(name = ModelConstants.DEVICE_TEMPLATE_NAME)
    private String name;
    /**
     * 电话
     */
    @Column(name = ModelConstants.PHONE_PROPERTY)
    private String phone;
    /**
     * 邮件
     */
    @Column(name = ModelConstants.EMAIL_PROPERTY)
    private String email;
    /**
     * 最后修改时间
     */
    @Column(name = ModelConstants.DATASOURCE_UPDATE_TIME)
    private Long updateTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}