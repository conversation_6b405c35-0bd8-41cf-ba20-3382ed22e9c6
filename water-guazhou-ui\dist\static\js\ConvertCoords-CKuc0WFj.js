import{_ as W}from"./index-C9hz-UZb.js";import{d as N,r as p,c as w,s as _,S as R,g as d,h as g,F as b,q as m,i as t,p as c,n as B,c5 as E,_ as D,ak as M,bq as O,dk as U,C as K}from"./index-r0dFAfgr.js";import{w as $,b as y}from"./Point-WxyopZva.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{a as q,n as A}from"./project-DUuzYgGl.js";import{S}from"./SideDrawer-CBntChyn.js";import J from"./CoordinateConvertVM-CXz2l4X9.js";import P from"./GeoJsonConverter-Cblskjmk.js";import{v as k}from"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcLayout-CHnHL9Pv.js";import"./ArcView-DpMnCY82.js";import"./geometryEngineBase-BhsKaODW.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./CoordinateConverter.vue_vue_type_script_setup_true_lang-P4M5h95Z.js";const j={class:"coord-root"},H={key:0,class:"coord-converter"},z={class:"left"},Q={class:"center"},X={class:"right"},Y=N({__name:"ConvertCoords",setup(Z){const x=p({type:"tabs",tabType:"border-card",tabs:[{label:"坐标系转换",value:"1"},{label:"度分秒转十进制",value:"2"},{label:"几何转换",value:"3"}]}),h=w(),C=w(),e=p({drawerShow:!0,converterType:"1"}),n=p({height:"none",indexVisible:!0,dataList:[{id:k(),x:"",y:""}],columns:[{label:"x",prop:"x",formItemConfig:{type:"input-number"}},{label:"y",prop:"y",formItemConfig:{type:"input-number"}}],operationHeader:{type:"btn-group",btns:[{perm:!0,text:"新增",isTextBtn:!0,svgIcon:_(M),click:()=>F()}]},operations:[{perm:!0,text:"删除",svgIcon:_(O),type:"danger",click:a=>T(a)}],pagination:{hide:!0}}),u=p({height:"none",indexVisible:!0,dataList:[],columns:[{label:"x",prop:"x",formItemConfig:{type:"input-number"}},{label:"y",prop:"y",formItemConfig:{type:"input-number"}}],pagination:{hide:!0}}),G=p({labelWidth:100,group:[{fields:[{type:"select",field:"wkid",label:"原始坐标系",options:[{label:"Beijong_1954_3_Degree_GK_CM_114E(2435)",value:"2435"},{label:"CGCS2000_3_Degree_GK_CM_114E(4547)",value:"4547"},{label:"GCS_WGS_1984(4326)",value:"4326"}]},{type:"table",config:n}]}],defaultValue:{wkid:"2435"}}),I=p({labelWidth:100,group:[{fields:[{type:"select",field:"wkid",label:"转换坐标系",options:[{label:"WGS_1984_Web_Mercator_Auxiliary_Sphere(3857)",value:"3857"},{label:"CGCS2000_3_Degree_GK_CM_114E(4547)",value:"4547"},{label:"GCS_WGS_1984(4326)",value:"4326"}]},{type:"table",config:u}]}],defaultValue:{wkid:"3857"}}),L=p({gutter:0,labelPosition:"top",labelWidth:100,group:[{fields:[{type:"btn-group",btns:[{perm:!0,text:"转换坐标",svgIcon:_(U),styles:{width:"100%"},click:()=>V()}]}]}]}),V=()=>{var l,r;u.dataList=[];const a=(l=h.value)==null?void 0:l.dataForm.wkid,o=(r=C.value)==null?void 0:r.dataForm.wkid,f=n.dataList.map(s=>new $({x:s.x,y:s.y,spatialReference:new y({wkid:a})})),i=new q;i.outSpatialReference=new y({wkid:o}),i.geometries=f,A(window.SITE_CONFIG.GIS_CONFIG.gisUtilitiesService+window.SITE_CONFIG.GIS_CONFIG.gisGeometryService,i).then(s=>{s.map(v=>{u.dataList.push({...v.toJSON()})})})},F=()=>{n.dataList.push({id:k(),x:"",y:""})},T=a=>{R("确定删除？","删除提示").then(()=>{n.dataList=n.dataList.filter(o=>o.id!==a.id)}).catch(()=>{})};return(a,o)=>{const f=E,i=D,l=W;return d(),g(l,{class:"coord-card"},{default:b(()=>[m(f,{modelValue:t(e).converterType,"onUpdate:modelValue":o[0]||(o[0]=r=>t(e).converterType=r),config:t(x),class:"tabs"},null,8,["modelValue","config"]),c("div",j,[t(e).converterType==="1"?(d(),B("div",H,[c("div",z,[m(S,{modelValue:t(e).drawerShow,"onUpdate:modelValue":o[1]||(o[1]=r=>t(e).drawerShow=r),title:"原始数据",width:500,"hide-bar":!0,direction:"rtl"},{default:b(()=>[m(i,{ref_key:"refFormLeft",ref:h,config:t(G)},null,8,["config"])]),_:1},8,["modelValue"])]),c("div",Q,[m(i,{config:t(L)},null,8,["config"])]),c("div",X,[m(S,{modelValue:t(e).drawerShow,"onUpdate:modelValue":o[2]||(o[2]=r=>t(e).drawerShow=r),title:"转换结果",width:500,"hide-bar":!0,direction:"ltr"},{default:b(()=>[m(i,{ref_key:"refFormRight",ref:C,config:t(I)},null,8,["config"])]),_:1},8,["modelValue"])])])):t(e).converterType==="3"?(d(),g(P,{key:1})):(d(),g(J,{key:2}))])]),_:1})}}}),Uo=K(Y,[["__scopeId","data-v-ac820083"]]);export{Uo as default};
