import { RouteRecordRaw } from 'vue-router';

export const authManageTest: RouteRecordRaw = {
  path: 'authManageTest',
  name: 'authManageTest',
  meta: { title: '权限管理', icon: 'iconfont icon-camera' },
  children: [
    {
      path: 'menuManageTest',
      name: 'menuManageTest',
      meta: { title: '菜单管理', icon: 'iconfont icon-camera' },
      component: () => import('@/views/sysManage/menuManage/index.vue')
    },
    {
      path: 'applicationManageTest',
      name: 'applicationManageTest',
      meta: { title: '应用管理', icon: 'iconfont icon-camera' },
      component: () => import('@/views/sysManage/applicationManage.vue')
    },
    {
      path: 'sourceManageTest',
      name: 'sourceManageTest',
      meta: { title: '资源管理', icon: 'iconfont icon-camera' },
      component: () => import('@/views/sysManage/sourceManage.vue')
    },
    {
      path: 'userManage',
      name: 'userManage',
      component: () => import('@/views/userManage/index.vue'),
      meta: {
        title: '用户管理',
        icon: 'iconfont icon-shezhi',
        roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
      }
    },
    {
      path: 'roleManage',
      name: 'roleManage',
      component: () => import('@/views/roleManage/index.vue'),
      meta: {
        title: '角色管理',
        icon: 'iconfont icon-shezhi',
        roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
      }
    },
    {
      path: 'globalConfig',
      name: 'globalConfig',
      component: () => import('@/views/sysManage/GlobalConfig/index.vue'),
      meta: {
        title: '全局配置',
        icon: 'iconfont icon-shezhi',
        roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'SYS_ADMIN']
      }
    },
    {
      path: 'XTGL_messageType',
      name: 'XTGL_messageType',
      component: () =>
        import('@/views/systemConfiguration/messageType/messageType.vue'),
      meta: {
        title: '消息配置',
        icon: 'iconfont icon-shezhi',
        roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'SYS_ADMIN']
      }
    },
    {
      path: 'sysSetting',
      name: 'sysSetting',
      component: () => import('@/views/sysManage/sysSetting.vue'),
      meta: {
        title: '界面配置',
        icon: 'iconfont icon-shezhi',
        roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'SYS_ADMIN']
      }
    }
  ]
};
