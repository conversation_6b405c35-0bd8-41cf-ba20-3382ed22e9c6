package org.thingsboard.server.dao.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.StationCircuitPlanListRequest;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitPlan;

@Mapper
public interface StationCircuitPlanMapper extends BaseMapper<StationCircuitPlan> {

    IPage<StationCircuitPlan> findList(IPage<StationCircuitPlan> page, @Param("param") StationCircuitPlanListRequest request);

}
