package org.thingsboard.server.dao.sql.maintainCircuit.maintain;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskC;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface MaintainTaskCMapper extends BaseMapper<MaintainTaskC> {

    List<MaintainTaskC> getList(String mainId);

    MaintainTaskC selectFirstByDeviceLabelCode(@Param("deviceLabelCode") String deviceLabelCode, @Param("thisTime") Date time);

    int countByLabelCode(@Param("deviceLabelCode") String deviceLabelCode);

    List<Map> getNowYearMaintainByDeviceLabelCode(@Param("deviceLabelCode") String deviceLabelCode, @Param("nowYear") Date nowYear);

    MaintainTaskC selectLatestByDeviceLabelCode(@Param("deviceLabelCode") String deviceLabelCode);
}
