package org.thingsboard.server.dao.fault;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.fault.FaultReport;
import org.thingsboard.server.dao.model.sql.fault.FaultReportC;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;
import java.util.Map;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface FaultReportService {

    FaultReport save(FaultReport faultReport);

    IstarResponse delete(List<String> ids);

    List<FaultReportC> getDeviceList(String workOrderId);

    void isFault(String id);

    FaultReport getByWorkOrderId(String id);

    Map statistics(String deviceLabelCode);

    PageData<Map> getRepairList(String deviceLabelCode, int page, int size);
}
