import{H as i,D as u}from"./DPlayer.min-_HMH7IVX.js";import{d,c as a,o as p,bA as _,g as m,n as v,p as f,C as y}from"./index-r0dFAfgr.js";const h={class:"video_box"},I=d({__name:"DPlayer",props:{videoInfo:{}},setup(l){const s=l,e=new i,t=a(),n=a();function r(){n.value=new u({element:t.value,loop:!1,autoplay:!0,preventClickToggle:!0,live:s.videoInfo.videoUrl.live,mutex:!1,video:{url:s.videoInfo.videoUrl.m3u8uri,type:"customHls",customType:{customHls(o,c){e.loadSource(o.src),e.attachMedia(o)}}}})}return p(()=>{r()}),_(()=>{e.stopLoad(),e.detachMedia(),n.value=null}),(o,c)=>(m(),v("div",h,[f("div",{ref_key:"DPlayerIns",ref:t,style:{width:"100%",height:"100%"}},null,512)]))}}),D=y(I,[["__scopeId","data-v-6715c622"]]);export{D as _};
