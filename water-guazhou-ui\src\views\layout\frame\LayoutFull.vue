<template>
  <div class="app-wrapper">
    <Navbar class="app-navbar"></Navbar>
    <section
      id="app-main"
      class="app-main"
    >
      <router-view v-slot="{ Component }">
        <transition name="fade">
          <component :is="Component" />
        </transition>
      </router-view>
    </section>
  </div>
</template>

<script lang="ts" setup>
import Navbar from './components/Navbar.vue'
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
// @import 'src/styles/mixin.scss';
.app-main {
  /*50 = navbar  */
  height: 100%;
  width: 100%;
  position: absolute;
  overflow: hidden;
  top: 0;
  left: 0;
  z-index: 0;
}
.app-navbar {
  z-index: 1;
  position: absolute;
  width: 100%;
}
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
}
</style>
