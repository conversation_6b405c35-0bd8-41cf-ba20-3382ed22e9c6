package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStage;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;

import java.util.Set;

public class MutableWorkOrderCountOfStatusRequest extends WorkOrderCountOfStatusRequest {
    private Set<WorkOrderStatus> activeSet;

    @Setter
    private boolean self;

    @Override
    public String valid(IStarHttpRequest request) {
        if (self) {
            setProcessUserId(request.getUserId());
        }
        return super.valid(request);
    }

    @Override
    public Set<WorkOrderStatus> getActiveStageSet() {
        return activeSet;
    }

    public WorkOrderCountOfStatusRequest loadSet(WorkOrderStage stage) {
        activeSet = stage.getStatusBetween();
        return this;
    }

    public WorkOrderCountOfStatusRequest unloadSet() {
        activeSet = null;
        return this;
    }
}
