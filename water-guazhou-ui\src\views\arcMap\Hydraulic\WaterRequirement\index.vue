<!--
  节点需水量
 -->
<template>
  <RightDrawerMap :title="'节点需水量'">
    <SliderBar></SliderBar>
    <el-divider></el-divider>
    <HydraulicPanel
      :header="['需水量分级(m³/h)', '图层控制', '定位']"
      :legends="[
        { label: '>6m³/h', value: 2, checked: true },
        { label: '5~6m³/h', value: 3342, checked: true },
        { label: '4~5m³/h', value: 154, checked: true },
        { label: '3~4m³/h', value: 211, checked: true },
        { label: '1~3m³/h', value: 184, checked: true },
        { label: '1~2m³/h', value: 184, checked: true },
        { label: '0~1m³/h', value: 184, checked: true }
      ]"
      :unit="'个'"
    ></HydraulicPanel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import SliderBar from '../components/SliderBar.vue'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import HydraulicPanel from '../components/HydraulicPanel.vue'
</script>
<style lang="scss" scoped></style>
