import{s as u,d as it,cN as at,c as rt,r as ot,g as m,h as I,F as _,p as w,q as H,i as l,an as T,bh as A,aw as st,n as f,G as nt,aB as y,aJ as L,cn as E,cs as lt,b as B,bt as pt,bB as S,dc as R,X as ct,C as mt}from"./index-r0dFAfgr.js";import{u as dt,_ as ht}from"./useRightDrawer-DizfuIz2.js";import{g as ut,m as ft}from"./MapView-DaoQedLH.js";import{w as yt}from"./Point-WxyopZva.js";import{p as gt,g as U,h as wt}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as $,a as vt}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as F}from"./ToolHelper-BiiInOzB.js";import{b as kt}from"./ViewHelper-BGCZjxXH.js";import bt from"./RightDrawerMap-D5PhmGFO.js";import{u as Ct}from"./useHighLight-DPevRAc5.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{a as _t}from"./URLHelper-B9aplt5w.js";import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{A as Mt,a as It,K as Tt,L as At,h as Lt,i as Nt,I as jt,v as Pt,y as Ht,z as St,C as Dt,D as xt}from"./index-DeAQQ1ej.js";import Ot from"./OneMapMenuBar-DLrw-4TL.js";import{S as v}from"./data-CLo2TII-.js";import"./IdentifyHelper-RJWmLn49.js";import"./identify-4SBo5EZk.js";import"./scaleUtils-DgkF6NQH.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ExportImageParameters-BiedgHNY.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./fieldconfig-Bk3o1wi7.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./index-BggOjNGp.js";const Gt=()=>{var d,k;const b={path:"GIS一张图",meta:{title:"GIS一张图",icon:""},children:[{path:"ssjk",meta:{title:"实时监控",icon:"iconfont icon-liuliang"},children:[{path:"ssjk-quanbu",component:u(Mt),detailComponent:u(It),meta:{title:"全部",alias:"全部站点"}},{path:"ssjk-sc",component:u(Tt),detailComponent:u(At),meta:{title:"污水处理厂",alias:"污水处理厂列表"}},{path:"ssjk-szjcd",component:u(Lt),detailComponent:u(Nt),meta:{title:"水质监测点",alias:"水质监测点"}}]},{path:"rycl",meta:{title:"人员车辆",icon:"iconfont icon-yonghuguanli"},children:[{path:"rycl-xjry",component:u(jt),detailComponent:u(Pt),meta:{title:"巡检人员",alias:"巡检人员列表"}}]},{path:"ywlc",meta:{title:"业务流程",icon:"iconfont icon-20shiguchuli"},children:[{path:"ywlc-gdlc",component:u(Ht),detailComponent:u(St),meta:{title:"工单流程",alias:"工单列表"}},{path:"ywlc-xjyh",component:u(Dt),detailComponent:u(xt),meta:{title:"巡检养护",alias:"巡检列表"}}]}]},g=window.SITE_CONFIG.ONE_MAP_CONFIG.hiddenMenus;g!=null&&g.length&&(b.children=(d=b.children)==null?void 0:d.filter(e=>g.indexOf(e.path)===-1));const c=window.SITE_CONFIG.ONE_MAP_CONFIG.hiddenSubMenus;return c!=null&&c.length&&((k=b.children)==null||k.map(e=>{var n;e.children=(n=e.children)==null?void 0:n.filter(N=>c.indexOf(N.path)===-1)})),b},Wt={class:"custom-menubar"},zt={class:"one-map-wrapper"},Et={key:0,class:"one-map-wrapper__closed"},Bt={class:"left-main"},Rt={key:0,class:"right"},Ut={class:"right-title"},$t={class:"right-main overlay-y"},Ft=it({__name:"OneMap_wushui",setup(b){const{proxy:g}=at(),c=dt({widthOption:{opend:540,more:1540}}),d=rt(),k=Ct(),e=ot({title:"",curPath:"",detailTitle:"",pageConfig:[],layerIds:[],layerInfos:[],menus:[],pops:[],windows:[],detailType:"",pipePickLayerid:[]}),n={layers:[]},N=t=>{var i,a,o,r;if(t.path==="sphy"){window.open(t.meta.url);return}((i=e.hMenu)==null?void 0:i.path)!==t.path&&t.path!==((a=e.hMenu)==null?void 0:a.path)&&((o=n.view)==null||o.map.removeMany(n.layers),k.destroy(),n.layers.length=0,e.pops.length=0,e.hMenu=t,(r=t.children)!=null&&r.length&&j(t.children[0],!0))},j=(t,i)=>{var o,r;e.curPath=t.path,e.title=t.meta.title,e.pipePickLayerid=e.layerInfos.filter(s=>s.layername===e.title).map(s=>s.layerid)||[];let a=n.layers.find(s=>s.id===t.path);if(!a){const s=t.path.startsWith("ssjk");a=$(n.view,{id:s?"ssjk":t.path,title:s?"站点":t.meta.title}),a&&n.layers.push(a),Z()}a&&(a.visible=i),(o=d.value)==null||o.closeAllPop(),c.drawerLevel.value==="closed"?(r=d.value)==null||r.toggleDrawer(!0):c.drawerLevel.value="opened"},D=async t=>{var a,o;if(!t)return;n.highlightGraphic=t;const i=(t==null?void 0:t.attributes.id)||"";e.detailType=((a=t==null?void 0:t.attributes)==null?void 0:a.path)||"",await S(),e.detailType&&((o=d.value)==null||o.openPop(i),c.drawerLevel.value==="more"&&P())},J=async(t,i)=>{var r;c.drawerLevel.value="more",((r=d.value)==null?void 0:r.isCustomOpened())||await R(600);const o=g.$refs["refDetail"+e.detailType];o==null||o[0].refreshDetail(n.view,t,{where:i})},V=t=>{var a,o;const i=g.$refs["refDetail"+t.path];i!=null&&i.length&&i[0].refreshDetail((o=(a=n.highlightGraphic)==null?void 0:a.attributes)==null?void 0:o.row)},P=async(t,...i)=>{var r,s;const a=t||((s=(r=n.highlightGraphic)==null?void 0:r.attributes)==null?void 0:s.row);e.detailType==="ssjk-quanbu"&&q(a),c.drawerLevel.value="more",await R(600),await S();const o=g.$refs["refDetail"+e.detailType];o!=null&&o.length&&o[0].refreshDetail(a,...i)},Z=()=>{k.bindHoverHighLight(n.view,n.layers,D,0)},q=t=>{if(e.detailType==="ssjk-quanbu")switch(t==null?void 0:t.type){case v.BENGZHAN:e.detailType="ssjk-ecgs";break;case v.CHELIUYAZHAN:case v.LIULIANGJIANCEZHAN:case v.YALIJIANCEZHAN:e.detailType="ssjk-znsb";break;case v.DAYONGHU:e.detailType="ssjk-dyhjcd";break;case v.SHUICHANGE:e.detailType="ssjk-sc";break;case v.SHUIYUANDI:e.detailType="ssjk-sy";break;case v.SHUIZHIJIANCEZHAN:e.detailType="ssjk-szjcd";break}},Y=async t=>{var i,a;if((i=d.value)==null||i.closeAllPop(),t.results.length){const o=t.results[0],r=o.feature,s=r.attributes.OBJECTID,h=gt(r.geometry)||[];e.windows=[{visible:!1,x:h[0],y:h[1],title:o.layerName+"("+r.attributes.新编号+")",attributes:{row:r.attributes,id:s}}],await U(n.view,r,{avoidHighlight:!0,zoom:15}),(a=d.value)==null||a.openPop(s)}else B.warning("没有查询到设备")},K=()=>{kt(n.view,async t=>{var i,a,o,r;if(((a=(i=t.results)==null?void 0:i[0])==null?void 0:a.type)==="graphic"){if(F("pointer"),!e.detailType){(o=d.value)==null||o.closeAllPop();return}await S(),P()}else F(""),(r=d.value)==null||r.closeAllPop()})},Q=async(t,i,a,o)=>{if(t.path.startsWith("sbzc"))e.detailTitle=(t.meta.title||"")+(a?"("+a+")":""),e.detailType=t.path||"",J(t.meta.title,i);else if(t.path==="ywlc-xjyh")e.detailType=t.path||"",P(i,n.view,e.layerInfos);else{const r=n.layers.find(h=>t.path.startsWith("ssjk")?h.id==="ssjk":h.id===t.path),s=r==null?void 0:r.graphics.find(h=>{var p;return((p=h.attributes)==null?void 0:p.id)===i});if(!r||!s||!n.view){B.warning("暂无位置信息");return}await U(n.view,s,{avoidHighlight:!0,zoom:o||15}),k.highlight(n.view,s,D,0)}},X=t=>{const i=e.curPath;if(!i)return;e.pops=e.pops.filter(p=>{var M;return((M=p.attributes.customConfig)==null?void 0:M.path)!==i});const a=(t==null?void 0:t.windows)||[];i.startsWith("ssjk")||i.startsWith("sbzc")?e.pops=a:e.pops.push(...a);const o=$(n.view,{id:i.startsWith("ssjk")?"ssjk":i});if(!o)return;o.removeAll();const r=a.filter(p=>p.symbolConfig),s=r.map(p=>{var C,x,O,G,W,z;return wt(p.x,p.y,{picUrl:((C=p.symbolConfig)==null?void 0:C.url)||_t(),spatialReference:(x=n.view)==null?void 0:x.spatialReference,attributes:p.attributes,picSize:[((O=p.symbolConfig)==null?void 0:O.width)||20,((G=p.symbolConfig)==null?void 0:G.height)||25],xOffset:((W=p.symbolConfig)==null?void 0:W.xoffset)||0,yOffset:((z=p.symbolConfig)==null?void 0:z.yoffset)||13})}),h=r.map(p=>{var C;return new ut({geometry:new yt({x:p.x,y:p.y,spatialReference:(C=n.view)==null?void 0:C.spatialReference}),symbol:new ft({font:{size:8,weight:"bold"},yoffset:-15,color:"#ffffff",text:p.title,lineHeight:.5,backgroundColor:"rgba(213,123,11,0.3)"}),attributes:{notHighlight:!0}})});o.addMany(s),o.addMany(h)},tt=async()=>{var i,a;e.layerIds=vt(n.view);const t=await ct(e.layerIds);e.layerInfos=((a=(i=t.data)==null?void 0:i.result)==null?void 0:a.rows)||[]},et=t=>{var a;n.view=t,console.log(t==null?void 0:t.spatialReference),e.menus=((a=Gt())==null?void 0:a.children)||[],e.hMenu=e.menus[0];const i=e.hMenu.children||[];j(i[0],!0),K(),tt()};return(t,i)=>{const a=ht,o=pt;return m(),I(bt,{ref_key:"refMap",ref:d,title:"","hide-coords":!1,pops:l(e).pops,windows:l(e).windows,"right-drawer-absolute":l(c).drawerAbsolute.value,"right-drawer-width":l(c).dialogWidth.value,"before-collapse":l(c).beforeCollapse,"right-drawer-min-width":l(c).drawerMinWidth.value,onMapLoaded:et,onPopToggle:i[2]||(i[2]=(r,s)=>r.visible=s)},{"map-bars":_(()=>{var r;return[w("div",Wt,[H(Ot,{menus:l(e).menus,onChange:N,onItemClick:j},null,8,["menus"])]),((r=l(e).hMenu)==null?void 0:r.path)==="sbzc"?(m(),I(a,{key:0,"add-to-view":!0,"auto-start":!0,"layer-ids":l(e).pipePickLayerid,onPicked:Y},null,8,["layer-ids"])):T("",!0)]}),"detail-header":_(()=>[w("span",null,A(l(e).detailTitle),1)]),"detail-default":_(()=>i[3]||(i[3]=[])),default:_(()=>[w("div",zt,[w("div",{class:st(["left overlay-y",l(c).drawerLevel.value])},[l(c).drawerLevel.value==="closed"?(m(),f("div",Et,A(l(e).title),1)):(m(),f(y,{key:1},[H(o,{size:"large",type:"simple",class:"left-title"},{default:_(()=>[nt(A(l(e).title),1)]),_:1}),w("div",Bt,[(m(!0),f(y,null,L(l(e).menus,r=>(m(),f(y,{key:r.path},[(m(!0),f(y,null,L(r.children,s=>(m(),f(y,{key:s.path},[s.path===l(e).curPath&&s.component?(m(),I(E(s.component),{key:0,menu:s,view:n.view,onHighlightMark:Q,onAddMarks:X},null,40,["menu","view"])):T("",!0)],64))),128))],64))),128))])],64))],2),l(c).drawerLevel.value==="more"?(m(),f("div",Rt,[w("div",Ut,[w("span",null,A(l(e).detailTitle),1),H(l(lt),{icon:"mdi:close",class:"more-close",onClick:i[0]||(i[0]=r=>l(c).drawerLevel.value="opened")})]),w("div",$t,[(m(!0),f(y,null,L(l(e).menus,r=>(m(),f(y,{key:r.path},[(m(!0),f(y,null,L(r.children,s=>(m(),f(y,{key:s.path},[l(e).detailType===s.path&&s.detailComponent?(m(),I(E(s.detailComponent),{key:0,ref_for:!0,ref:"refDetail"+s.path,onMounted:h=>V(s),onRefresh:i[1]||(i[1]=h=>l(e).detailTitle=h.title)},null,40,["onMounted"])):T("",!0)],64))),128))],64))),128))])])):T("",!0)])]),_:1},8,["pops","windows","right-drawer-absolute","right-drawer-width","before-collapse","right-drawer-min-width"])}}}),aa=mt(Ft,[["__scopeId","data-v-d7799a31"]]);export{aa as default};
