package org.thingsboard.server.dao.util.imodel.query;

import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.*;

public interface GeneralTaskRequest extends Requestible {
    default GeneralTaskStatus getCreateStatus() {
        return PENDING;
    }

    default GeneralTaskStatus getAssignStatus() {
        return RECEIVED;
    }

    default GeneralTaskStatus getVerifyStatus() {
        return VERIFY;
    }

    default GeneralTaskStatus getCompleteStatus() {
        return APPROVED;
    }

    default GeneralTaskStatus getRejectStatus() {
        return REJECTED;
    }

}
