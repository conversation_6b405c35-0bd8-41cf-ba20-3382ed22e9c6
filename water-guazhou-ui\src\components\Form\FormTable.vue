<template>
  <div
    v-if="config"
    class="table-wrapper"
    :class="config.height === 'none' ? 'table-height-auto' : ''"
  >
    <div v-if="!inCard" class="title-wrapper">
      <span v-if="config.title" class="title" :style="config.titleStyle">{{
        config.title
      }}</span>
      <div
        v-if="config.titleRight?.length"
        class="right-wrapper"
        :style="config.titleRightStyle"
      >
        <template v-for="(rightitem, k) in config.titleRight" :key="k">
          <div
            class="right_default"
            :class="rightitem.className"
            :style="rightitem.style"
          >
            <template v-for="(obj, z) in rightitem.items" :key="z">
              <FormItem
                v-if="obj.field"
                v-model="state.titleQueryParams[obj.field]"
                :config="obj"
                :size="size"
                @change="
                  (val: any) =>
                    config.handleQuery && config.handleQuery(val, obj)
                "
              />
              <FormItem
                v-else
                :config="obj"
                :size="size"
                @change="
                  (val: any) =>
                    config.handleQuery && config.handleQuery(val, obj)
                "
              />
            </template>
          </div>
        </template>
      </div>
    </div>

    <div class="table-container" :class="heightClass">
      <slot name="prefix">
        <div v-if="config.tableTitle" class="table-title">
          <span class="title">{{ config.tableTitle }}</span>
        </div>
      </slot>
      <el-table
        id="print"
        ref="refElTable"
        v-loading="config.loading"
        style="width: 100%"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        class="table-custom"
        :size="size || config.size"
        :border="config.border ?? true"
        :row-key="
          config.rowKey ||
          ((row) => {
            return row.id;
          })
        "
        :stripe="config.stripe"
        :data="newValue"
        :height="config.height === 'none' ? undefined : config.height || '100%'"
        :max-height="
          config.maxHeight === 'none' ? undefined : config.maxHeight || '100%'
        "
        :span-method="config.spanMethod"
        :show-summary="config.showSummary"
        :default-expand-all="config.defaultExpandAll"
        :summary-method="
          (params: any) => config.showSummary && getSummaries(params)
        "
        :highlight-current-row="config.highlightCurrentRow !== false"
        :current-row-key="config.currentRowKey"
        :tree-props="config.treeProps"
        :default-sort="
          (config.defaultSort as any) || {
            prop: 'orderNum',
            order: 'descending'
          }
        "
        :fit="config.fit"
        :show-header="config.showHeader"
        :row-class-name="config.rowClassName"
        :row-style="config.rowStyle"
        :cell-class-name="config.cellClassName"
        :cell-style="config.cellStyle"
        :header-row-class-name="config.headerRowClassName"
        :header-row-style="config.headerRowStyle"
        :header-cell-class-name="config.headerCellClassName"
        :header-cell-style="config.headerCellStyle"
        :empty-text="config.emptyText"
        :tooltip-effect="config.tooltipEffect"
        @row-dblclick="
          (row, column, event) => doubleClickEvent(row, column, event)
        "
        @row-click="config.handleRowClick"
        @selection-change="config.handleSelectChange"
        @select="handleSelect"
      >
        <el-table-column v-if="config.expandable" type="expand">
          <template #default="propsa">
            <component
              :is="config.expandComponent"
              :config="propsa.row"
              @extendedReturn="config.extendedReturn"
            ></component>
          </template>
        </el-table-column>
        <!-- checkbox 根据是否配置了 selectChange 选项来控制显隐 -->
        <el-table-column
          v-if="config.handleSelectChange || config.singleSelect"
          :align="'center'"
          type="selection"
          :reserve-selection="true"
          :selectable="config.selectable"
          width="55px"
        >
        </el-table-column>
        <!-- 序号列 根据 indexVisible 判断是否有序号列 -->

        <el-table-column
          v-if="config.indexVisible"
          :label="
            typeof config.indexVisible === 'boolean'
              ? '序号'
              : config.indexVisible?.label || '序号'
          "
          type="index"
          :align="
            typeof config.indexVisible === 'boolean'
              ? 'center'
              : config.indexVisible.align || 'center'
          "
          :width="
            typeof config.indexVisible === 'boolean'
              ? '55px'
              : config.indexVisible.width || '55px'
          "
          :fixed="
            typeof config.indexVisible === 'boolean'
              ? 'left'
              : config.indexVisible.fixed || 'left'
          "
        ></el-table-column>
        <el-table-column
          v-if="config.sort"
          :label="'排行'"
          :width="'80px'"
          :prop="config.sort.prop"
          align="center"
        >
          <template #default="scope">
            <span
              v-if="scope.$index + 1 === 1"
              :class="{ 'sort-1': config.sort?.showBackground }"
              >{{ scope.$index + 1 }}</span
            >
            <span
              v-else-if="scope.$index + 1 === 2"
              :class="{ 'sort-2': config.sort?.showBackground }"
              >{{ scope.$index + 1 }}</span
            >
            <span
              v-else-if="scope.$index + 1 === 3"
              :class="{ 'sort-3': config.sort?.showBackground }"
              >{{ scope.$index + 1 }}</span
            >
            <span v-else>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <template v-for="item in config.columns" :key="item.prop">
          <FormTableColumn
            v-if="item.headerFormItemConfig?.field"
            v-model="state.titleQueryParams[item.headerFormItemConfig.field]"
            :config="item"
            :row="state.titleQueryParams"
            @change="
              (val: any) =>
                config.handleQuery &&
                config.handleQuery(val, item.headerFormItemConfig)
            "
          ></FormTableColumn>
          <FormTableColumn
            v-else
            :config="item"
            :row="state.titleQueryParams"
          ></FormTableColumn>
        </template>

        <!-- 操作 -->
        <el-table-column
          v-if="config.operations && operationVisible"
          label="操作"
          :fixed="config.operationFixed || 'right'"
          :header-align="config.operationHeaderAlign || 'left'"
          :align="'center'"
          :width="config.operationWidth || '300px'"
        >
          <template v-if="config.operationHeader" #header>
            <template v-if="config.operationHeader">
              <FormItem
                v-if="config.operationHeader?.field"
                v-model="state.titleQueryParams[config.operationHeader.field]"
                :row="state.titleQueryParams"
                :config="config.operationHeader"
                :size="size"
                @change="
                  (val: any) =>
                    config.handleQuery &&
                    config.handleQuery(val, config.operationHeader)
                "
              ></FormItem>
              <FormItem
                v-else
                :size="size"
                :row="state.titleQueryParams"
                :config="config.operationHeader"
              ></FormItem>
            </template>
          </template>
          <template #default="scope">
            <div class="operation-btn-box">
              <template v-for="(btn, i) in config.operations" :key="i">
                <component
                  :is="btn.component"
                  v-if="btn.perm && btn.component"
                  :row="scope.row"
                  :operation="btn"
                />

                <el-dropdown
                  v-else-if="btn.items"
                  :key="i"
                  trigger="click"
                  @command="(row) => btn.click && btn.click(row, scope.row)"
                >
                  <Button
                    :text="btn.isTextBtn !== false"
                    :config="btn"
                    :size="btn.size || 'small'"
                    :row="scope.row"
                    :index="scope.$index"
                  ></Button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-for="(key, index) in btn.items"
                        :key="index"
                        :v-show="key.show"
                        :command="key.value"
                      >
                        {{ key.label }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <Button
                  v-else
                  :text="btn.isTextBtn !== false"
                  :config="btn"
                  :size="btn.size || 'small'"
                  :row="scope.row"
                  :index="scope.$index"
                ></Button>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <slot name="suffix"></slot>
    </div>
    <Pagination
      class="Pagination"
      :config="config.pagination"
      :refresh-data="config.pagination.refreshData"
    />
  </div>
</template>

<script lang="ts" setup>
import { BigNumber } from 'bignumber.js';
import { fileStrToArr } from '@/utils/GlobalHelper';
import { TrueExcel } from '@/utils/exportExcel';

const props = defineProps<{
  config: ITable;
  size?: ISize;
  modelValue?: any;
  inCard?: boolean;
  isPagination?: boolean;
}>()
const emit = defineEmits(['change', 'update:modelValue']);

const newValue = computed(() => {
  return props.config.dataList;
});
const heightClass = computed(() => {
  if (props.config.height === 'none') return 'table-height-auto';
  let count = 0;
  !props.inCard &&
    (props.config.title || props.config.titleRight?.length) &&
    count++;
  !props.config.pagination.hide && count++;
  return 'table-full-' + count;
});
// el-table组件实例
const refElTable =
  ref<InstanceType<(typeof import('element-plus'))['ElTable']>>();

const state = reactive<{
  titleQueryParams: any;
}>({
  titleQueryParams: {}
});
// 是否有有效的operation
const operationVisible = computed(() =>
  props.config.operations?.some((item) => item.perm)
);
const pic = (image?: any, preview?: boolean) => {
  if (!image) return preview ? [] : '';
  const images = image instanceof Array ? image : fileStrToArr(image);
  return preview ? images : images[0];
};
const exportTable = (
  name?: string,
  beforeExport?: (excel: TrueExcel) => void
) => {
  const excel = new TrueExcel();
  excel.addElTable(refElTable.value);
  beforeExport?.(excel);
  excel.export(name);
};
/**
 * 设置选中项
 */
const toggleRowSelection = (flag?: boolean) => {
  if (props.config.selectList?.length === 0) {
    refElTable.value?.clearSelection();
  }
  props.config.selectList?.forEach((item: any) => {
    refElTable.value?.toggleRowSelection(item, flag !== false);
  });
};

// 双击事件
const doubleClickEvent = (row, column, event) => {
  if (props.config.doubleSelect === undefined || props.config.doubleSelect) {
    [...(props.config?.selectList || []), row].forEach((item: any) => {
      refElTable.value?.toggleRowSelection(item, true);
    });
  }
  props.config?.handleRowDbClick &&
    props.config?.handleRowDbClick(row, column, event);
};

const getSummaries = (param: any) => {
  const { data } = param;
  if (props.config.summaryMethod) {
    return props.config.summaryMethod(props.config.columns, data);
  }
  const sums: string[] = [];
  props.config.columns.forEach((column, i) => {
    let index = i;
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (props.config.indexVisible) index++;
    if (props.config.sort) index++;
    if (props.config.handleSelectChange) index++;
    if (column.summary) {
      const values = data.map((item) => Number(item[column.prop]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return Number(new BigNumber(prev).plus(curr));
          }
          return prev;
        }, 0)}`;
      } else {
        sums[index] = '-';
      }
    } else {
      sums[index] = '-';
    }
  });

  return sums;
};
/**
 * 处理单选情况
 */
const handleSelect = (selection: any[], row: any) => {
  if (props.config.singleSelect) {
    const isChecked = props.config.rowKey
      ? selection.findIndex((item) => {
          const rowKey = props.config.rowKey || '';
          return item[rowKey] === row[rowKey];
        }) !== -1
      : undefined;
    props.config.selectList?.forEach((item: any) => {
      refElTable.value?.toggleRowSelection(item, false);
    });
    refElTable.value?.toggleRowSelection(row, !!isChecked);
    props.config.select && props.config.select(row, isChecked);
  } else {
    props.config.select && props.config.select(row);
  }
};

/**
 * 表单序号处理
 */
// const indexMethod = index => {
//   if (props.config.indexAccumulative) {
//     return ((props.config.pagination.page || 1) - 1) * (props.config.pagination.limit || 0) + index + 1
//   }
//   return index + 1
// }

watch(
  () => props.config.selectList,
  () => toggleRowSelection()
);
watch(
  () => props.config.dataList,
  (newVal: any) => {
    if (!newVal) newVal = [];
    emit('change', newVal);
    emit('update:modelValue', newVal);
    refElTable.value?.setCurrentRow(props.config.currentRow);
  },
  {
    deep: true
  }
);
watchEffect(() => {
  props.config.currentRowKey &&
    props.config.highlightCurrentRow &&
    refElTable.value?.setCurrentRow(props.config.currentRow);
});
onMounted(() => {
  toggleRowSelection();
});
defineExpose({
  refElTable,
  operationVisible,
  getSummaries,
  pic,
  exportTable,
  ...toRefs(state)
});
</script>

<style lang="scss" scoped>
.table-wrapper {
  height: 100%;
  width: 100%;
  &.table-height-auto {
    height: auto;
  }
}

.table-container {
  height: calc(100% - 40px);
  width: 100%;
  position: relative;
  &.table-height-auto {
    height: auto;
  }
  &.table-full-0 {
    height: 100%;
  }
  &.table-full-1 {
    height: calc(100% - 38px);
  }
  &.table-full-2 {
    height: calc(100% - 76px);
  }
  
  // 美化表格样式
  :deep(.el-table) {
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    font-size: 13px;
    
    &::before {
      height: 0; // 移除默认底部边框
    }
    
    // 单元格内容过长时显示省略号
    .cell {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    // 压缩复选框列宽度
    .el-table-column--selection .cell {
      padding-left: 10px;
      padding-right: 10px;
    }
    
    // 表头样式优化
    .el-table__header {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
        padding: 8px 0;
        height: 40px;
        
        &.is-leaf {
          border-bottom: 1px solid #EBEEF5;
        }
      }
    }
    
    // 表格行样式优化
    .el-table__row {
      transition: all 0.3s ease;
      
      &:hover {
        background-color: rgba(0, 127, 255, 0.05) !important;
        transform: translateZ(0);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        z-index: 1;
      }
      
      &.current-row {
        background-color: rgba(0, 127, 255, 0.1) !important;
        
        td {
          background-color: transparent !important;
        }
      }
      
      // 单元格样式优化
      td {
        padding: 6px 0;
        transition: all 0.3s;
        border-bottom: 1px solid #EBEEF5;
        height: 38px;
        
        .cell {
          font-size: 14px;
          color: #606266;
          line-height: 1.4;
        }
      }
    }
    
    // 斑马纹优化
    .el-table__row--striped {
      td {
        background-color: #fafafa;
      }
    }
    
    // 无数据状态优化
    .el-table__empty-block {
      min-height: 120px;
      
      .el-table__empty-text {
        color: #909399;
      }
    }
  }
}

.title-wrapper,
.right-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  // margin-bottom: 16px;
}
.title-wrapper {
  .title {
    display: inline;
    flex: 1;
    white-space: nowrap;
    word-break: keep-all;
    color: #606266;
    font-size: 18px;
    font-weight: 600;
    position: relative;
    padding-left: 12px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #409EFF;
      border-radius: 2px;
    }
  }
}
.table-title {
  height: 40px;
  font-size: 16px;
  text-align: center;
  background-color: #f5f7fa;
  border: 1px solid #EBEEF5;
  border-bottom: none;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  
  .title {
    line-height: 48px;
    color: #303133;
    font-weight: 600;
  }
}

.operation-btn-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 6px;

  :deep(.el-button) {
    margin: 0;
    padding: 5px 10px;
    
    &.is-text {
      padding: 5px 6px;
      transition: all 0.3s;
      position: relative;
      
      &:hover {
        background-color: rgba(64, 158, 255, 0.1);
        color: #409EFF;
        transform: translateY(-1px);
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: 2px;
        left: 50%;
        width: 0;
        height: 1px;
        background-color: #409EFF;
        transition: all 0.3s;
        transform: translateX(-50%);
      }
      
      &:hover::after {
        width: 80%;
      }
    }
  }
}

.sort-1,
.sort-2,
.sort-3 {
  display: inline-block;
  border-radius: 12px;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  color: white;
  font-weight: bold;
}

.sort-1 {
  background: linear-gradient(135deg, #ff5a5a, #ff3838);
}

.sort-2 {
  background: linear-gradient(135deg, #ff955a, #ff7b38);
}

.sort-3 {
  background: linear-gradient(135deg, #fed75a, #ffc938);
}

.right_default {
  margin-bottom: 8px;
}

.Pagination {
  margin-top: 2px;
  text-align: right;
  padding-right: 16px;
  
  :deep(.el-pagination) {
    justify-content: flex-end;
    
    .el-pagination__total,
    .el-pagination__sizes,
    .el-pagination__jump {
      margin-right: 12px;
    }
    
    .el-pagination__sizes .el-input .el-input__inner {
      height: 28px;
      line-height: 28px;
    }
    
    button {
      min-width: 32px;
      height: 32px;
      line-height: 32px;
      background-color: #f4f4f5;
      border-radius: 4px;
      margin: 0 4px;
      
      &:hover {
        color: #409EFF;
      }
      
      &.is-active {
        background-color: #409EFF;
        color: white;
      }
    }
  }
}

// 优化操作按钮
:deep(.operation-btn-box) {
  .el-button {
    &.is-text {
      &:hover {
        transform: translateY(-1px);
      }
    }
    
    &--primary {
      position: relative;
      overflow: hidden;
      
      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.2);
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      &:hover:after {
        transform: translateX(0);
      }
    }
  }
}
</style>
