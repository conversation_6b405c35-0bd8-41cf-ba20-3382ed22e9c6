import{d as P,c,r as G,a8 as h,o as O,x as r,g as v,h as A,F as a,p as l,q as s,G as E,n as D,aJ as W,aB as $,bh as k,an as j,eJ as z,aK as Q,aL as X,I as Z,c2 as ee,H as te,K as ae,bU as le,bW as se,J as oe,L as ne,C as ie}from"./index-r0dFAfgr.js";/* empty css                 */import{b as re,p as de}from"./conservationWaterLevel-BIi1yWt3.js";import{g as ue}from"./index-C7go6VEC.js";const ce={class:"analysis-content"},me={key:0,class:"stats-section"},pe={class:"stat-item"},ve={class:"stat-value"},fe={class:"stat-item"},_e={class:"stat-value"},ge={class:"stat-item"},ye={class:"stat-value"},be={class:"dialog-footer"},we=P({__name:"AnalysisDialog",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","confirm"],setup(R,{emit:L}){const S=R,f=L,_=c(!1),g=c(!1),p=c(),x=c([]),u=c(null),n=G({stationId:"",description:""}),i=c([]),C=h({get:()=>S.visible,set:t=>f("update:visible",t)}),y=h(()=>i.value&&Array.isArray(i.value)&&i.value.length===2),B={stationId:[{required:!0,message:"请选择测点",trigger:"change"}]};O(()=>{T()});const T=async()=>{try{const t=await ue({type:"水源地"});t.status===200&&(x.value=t.data.data||[])}catch(t){console.error("加载测点列表失败:",t),r.error("加载测点列表失败")}},F=t=>{t&&Array.isArray(t)&&t.length===2?i.value=[t[0],t[1]]:i.value=[]},M=async()=>{if(!n.stationId){r.warning("请先选择测点");return}if(!y.value){r.warning("请选择完整的时间范围");return}try{g.value=!0;const[t,e]=i.value,m=await re(n.stationId,t,e);if(m.data.code===200){const d=m.data.data;u.value={totalRecords:d.totalrecords||0,timeSpan:Math.ceil((e-t)/(24*60*60*1e3)),avgLevel:d.avggroundwaterlevel?Number(d.avggroundwaterlevel).toFixed(3):"0.000"},d.totalrecords===0?r.warning("选择的时间范围内没有数据，无法进行分析"):r.success(`找到 ${d.totalrecords} 条数据，可以进行分析`)}}catch(t){console.error("数据预览失败:",t),r.error("数据预览失败")}finally{g.value=!1}},N=async()=>{if(p.value)try{if(await p.value.validate(),!n.stationId){r.warning("请选择测点");return}if(!y.value){r.warning("请选择完整的时间范围");return}_.value=!0;const[t,e]=i.value;(await de(n.stationId,t,e)).data.code===200&&(r.success("分析任务已启动，请稍后查看结果"),f("confirm"),b())}catch(t){console.error("启动分析失败:",t),r.error("启动分析失败")}finally{_.value=!1}},b=()=>{var t;(t=p.value)==null||t.resetFields(),i.value=[],u.value=null,f("update:visible",!1)};return(t,e)=>{const m=z,d=Q,U=X,w=Z,Y=ee,q=te,H=ae,V=le,J=se,I=oe,K=ne;return v(),A(K,{modelValue:C.value,"onUpdate:modelValue":e[3]||(e[3]=o=>C.value=o),title:"智能分析",width:"600px","close-on-click-modal":!1,onClose:b},{footer:a(()=>[l("div",be,[s(I,{onClick:b},{default:a(()=>e[9]||(e[9]=[E("取消")])),_:1}),s(I,{type:"primary",onClick:M,loading:g.value},{default:a(()=>e[10]||(e[10]=[E(" 数据预览 ")])),_:1},8,["loading"]),s(I,{type:"success",onClick:N,loading:_.value,disabled:!n.stationId||!y.value},{default:a(()=>e[11]||(e[11]=[E(" 开始分析 ")])),_:1},8,["loading","disabled"])])]),default:a(()=>[l("div",ce,[s(m,{title:"智能分析说明",type:"info",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}},{default:a(()=>e[4]||(e[4]=[l("div",null,[l("p",null,"通过原水液位变化反馈，引入智能化算法，分析地下水涵养水位，给出地下水涵养建议"),l("p",null,"分析内容包括：涵养系数、涵养潜力、风险评估、涵养建议等")],-1)])),_:1}),s(H,{ref_key:"formRef",ref:p,model:n,rules:B,"label-width":"100px"},{default:a(()=>[s(w,{label:"选择测点",prop:"stationId",required:""},{default:a(()=>[s(U,{modelValue:n.stationId,"onUpdate:modelValue":e[0]||(e[0]=o=>n.stationId=o),placeholder:"请选择测点",filterable:"",style:{width:"100%"}},{default:a(()=>[(v(!0),D($,null,W(x.value,o=>(v(),A(d,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(w,{label:"分析周期"},{default:a(()=>[s(Y,{modelValue:i.value,"onUpdate:modelValue":e[1]||(e[1]=o=>i.value=o),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"x","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)],onChange:F,style:{width:"100%"}},null,8,["modelValue","default-time"])]),_:1}),s(w,{label:"分析说明"},{default:a(()=>[s(q,{modelValue:n.description,"onUpdate:modelValue":e[2]||(e[2]=o=>n.description=o),type:"textarea",rows:3,placeholder:"请输入分析说明（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),u.value?(v(),D("div",me,[e[8]||(e[8]=l("div",{class:"stats-title"},"数据统计",-1)),s(J,{gutter:20},{default:a(()=>[s(V,{span:8},{default:a(()=>[l("div",pe,[e[5]||(e[5]=l("div",{class:"stat-label"},"数据条数",-1)),l("div",ve,k(u.value.totalRecords),1)])]),_:1}),s(V,{span:8},{default:a(()=>[l("div",fe,[e[6]||(e[6]=l("div",{class:"stat-label"},"时间跨度",-1)),l("div",_e,k(u.value.timeSpan)+"天",1)])]),_:1}),s(V,{span:8},{default:a(()=>[l("div",ge,[e[7]||(e[7]=l("div",{class:"stat-label"},"平均水位",-1)),l("div",ye,k(u.value.avgLevel)+"m",1)])]),_:1})]),_:1})])):j("",!0)])]),_:1},8,["modelValue"])}}}),xe=ie(we,[["__scopeId","data-v-3d707f58"]]);export{xe as default};
