package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TASK_TRACK_HISTORY_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class TaskTrackHistoryEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TASK_TRACK_HISTORY_CONTENT_ID)
    private String contentId;

    @Column(name = ModelConstants.TASK_TRACK_HISTORY_LOCATION)
    private String location;

    @Column(name = ModelConstants.TASK_TRACK_HISTORY_TS)
    private Date ts;

    @Column(name = ModelConstants.TASK_TRACK_HISTORY_USER_ID)
    private String userId;

}
