import{d as J,c as G,r as v,b as g,X as Q,W as q,Q as V,g as z,n as U,q as T,i as b,_ as W,C as X}from"./index-r0dFAfgr.js";import{w as $}from"./MapView-DaoQedLH.js";import{s as H}from"./FeatureHelper-Da16o0mu.js";import{c as K}from"./GPHelper-fLrvVD-A.js";import{e as Y,i as Z}from"./IdentifyHelper-RJWmLn49.js";import{g as ee,e as te,a as re}from"./LayerHelper-Cn-iiqxI.js";import{e as ae,i as ie}from"./QueryHelper-ILO3qZqg.js";import{s as k}from"./ToolHelper-BiiInOzB.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import"./Point-WxyopZva.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import oe from"./PipeDetail-CTBPYFJW.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./scaleUtils-DgkF6NQH.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./project-DUuzYgGl.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ExportImageParameters-BiedgHNY.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./DateFormatter-Bm9a68Ax.js";import"./config-fy91bijz.js";const se=J({__name:"PathAnalys",props:{view:{},telport:{}},setup(P){const I=G(),o=P,a=v({curOperate:"",tabs:[]}),t={pickedFeatures:[],graphicsLayer:void 0,resultLayer:void 0,mapClick:void 0},N=G(),y=v({columns:[{label:"管线类型",prop:"layerName"},{label:"管线编号",prop:"OBJECTID"}],dataList:[],pagination:{hide:!0}}),L=v({columns:[{label:"图层名称",prop:"layername"},{label:"管线数量",prop:"count"},{label:"管线长度",prop:"length",formatter:(e,r)=>r+" 米"}],dataList:[],pagination:{hide:!0}}),j=v({group:[{fieldset:{desc:"选取管线"},fields:[{type:"btn-group",btns:[{perm:!0,type:"warning",text:()=>a.curOperate==="picking"?"正在选取管线":"点击并依次选取两条管线",click:()=>M(),loading:()=>a.curOperate==="picking",disabled:()=>a.curOperate==="analysing",styles:{width:"100%"}}]},{type:"table",style:{height:"120px"},label:"所选管线概览",config:y}]},{fieldset:{desc:"执行分析"},fields:[{type:"btn-group",btns:[{perm:!0,text:()=>a.curOperate==="analysing"?"正在分析":"开始分析",click:()=>B(),disabled:()=>a.curOperate==="analysing"||y.dataList.length!==2,loading:()=>a.curOperate==="analysing",styles:{width:"100%"}}]}]},{fieldset:{desc:"分析结果"},fields:[{type:"checkbox",field:"viewInMap",options:[{label:"地图显示",value:"viewInMap"}],onChange:e=>{t.resultLayer&&(t.resultLayer.visible=!!e.length)}},{type:"table",style:{height:"80"},config:L},{type:"btn-group",itemContainerStyle:{marginTop:"20px",marginBottom:"8px"},btns:[{perm:!0,text:()=>a.curOperate==="detailing"?"正在查询":"查看详细结果",loading:()=>a.curOperate==="detailing",disabled:()=>a.curOperate==="analysing"||a.curOperate==="detailing",click:()=>E(),styles:{width:"100%"}}]},{type:"btn-group",btns:[{perm:!0,text:"清除所有",type:"danger",disabled:()=>a.curOperate==="analysing",click:()=>S(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12,defaultValue:{viewInMap:["viewInMap"]}}),D=async()=>{var e;o.view&&((e=I.value)==null||e.extentTo(o.view))},E=()=>{var e;a.curOperate="detailing",(e=I.value)==null||e.openDialog()},M=()=>{var e,r;o.view&&(k("crosshair"),y.dataList=[],t.pickedFeatures=[],a.curOperate="picking",t.graphicsLayer=ee(o.view,{id:"path-analys",title:"最短路径分析标注"}),(e=t.graphicsLayer)==null||e.removeAll(),t.resultLayer&&o.view.map.remove(t.resultLayer),t.mapClick=(r=o.view)==null?void 0:r.on("click",async i=>{var s;try{await R(i),y.dataList.length>=2&&(y.dataList.length=2,(s=t.mapClick)==null||s.remove(),k(""),t.mapClick=void 0,a.curOperate="picked")}catch{g.error("范围内没有管线")}}))},B=async()=>{var e,r,i,s,p,d,u,n;try{t.resultLayer&&((e=o.view)==null||e.map.remove(t.resultLayer)),a.curOperate="analysing";const c=(i=(r=(await Q(t.pickedFeatures[0].layerId)).data)==null?void 0:r.result)==null?void 0:i.rows,m=await K(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPathAnalysGPService,{startclassname:(c==null?void 0:c.length)&&c[0].layerdbname,startid:(s=t.pickedFeatures[0])==null?void 0:s.feature.attributes.OBJECTID,endclassname:(c==null?void 0:c.length)&&c[0].layerdbname,endid:(p=t.pickedFeatures[1])==null?void 0:p.feature.attributes.OBJECTID,usertoken:q().gToken});if(await m.waitForJobCompletion(),m.jobStatus==="job-succeeded"){t.jobId=m.jobId,t.resultLayer=await m.fetchResultMapImageLayer(m.jobId),t.resultLayer.title="最短路径分析结果";const h=te(o.view);(d=o.view)==null||d.map.add(t.resultLayer,h);const l=(await m.fetchResultData("summary")).value;(l==null?void 0:l.code)!==1e4?g.error(l.error):(t.resultSummary=(u=l==null?void 0:l.result)==null?void 0:u.summary,A((n=l==null?void 0:l.result)==null?void 0:n.summary)),a.tabs=t.resultSummary.layersummary.map(w=>({label:w.layername,name:w.layername,data:[]})),await x(a.tabs,0)}else m.jobStatus==="job-cancelled"?g.info("已取消分析"):m.jobStatus==="job-cancelling"?g.info("任务正在取消"):m.jobStatus==="job-failed"&&g.info("分析失败，请联系管理员")}catch(f){a.curOperate="picked",console.dir(f),g.info("分析失败，请联系管理员");return}a.curOperate="analysed"},x=async(e,r)=>{if(r<e.length){const i=e[r];i.data=await C(i.name,0),r<e.length-1&&await x(e,++r)}},C=async(e,r)=>{var i;try{let s=await ae((((i=t.resultLayer)==null?void 0:i.url)||"")+"/"+r,ie({where:"layername='"+e+"'",orderByFields:["OBJECTID asc"],returnGeometry:!1}));return s===null&&(s=await C(e,++r)),s}catch{return[]}},R=async e=>{var r,i;if(o.view)try{const p=(r=(await Y(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,Z({tolerance:3,geometry:e.mapPoint,mapExtent:o.view.extent,width:o.view.width,layerIds:re(o.view)}))).results)==null?void 0:r.filter(d=>{var u,n;return((n=(u=d.feature)==null?void 0:u.geometry)==null?void 0:n.type)==="polyline"})[0];if(p){const u=(p&&[p]||[]).map(n=>({...n.feature.attributes||{},layerId:n.layerId,layerName:n.layerName}));if(y.dataList.push(...u),t.pickedFeatures.push(p),p){const n=p.feature;n&&(n.symbol=H("polyline")),(i=t.graphicsLayer)==null||i.add(n)}}else g.warning("没有查询到管线")}catch{console.log("拾取失败")}},A=e=>{var n,f,c,m,h,O,l,w,F,_;L.dataList=e.layersummary||[];let r=e.xmin||((f=(n=o.view)==null?void 0:n.extent)==null?void 0:f.xmin),i=e.xmax||((m=(c=o.view)==null?void 0:c.extent)==null?void 0:m.xmax),s=e.ymin||((O=(h=o.view)==null?void 0:h.extent)==null?void 0:O.ymin),p=e.ymax||((w=(l=o.view)==null?void 0:l.extent)==null?void 0:w.ymax);const d=i-r,u=p-s;r-=d/2,i+=d/2,s-=u/2,p+=u/2,(_=o.view)==null||_.goTo(new $({xmin:r,ymin:s,xmax:i,ymax:p,spatialReference:(F=o.view)==null?void 0:F.spatialReference}))},S=()=>{var e,r,i;t.graphicsLayer&&((e=o.view)==null||e.map.remove(t.graphicsLayer)),t.resultLayer&&((r=o.view)==null||r.map.remove(t.resultLayer)),(i=t.mapClick)==null||i.remove(),t.mapClick=void 0,y.dataList=[],t.pickedFeatures=[],L.dataList=[],t.resultSummary=void 0,k("")};return V(()=>{S()}),(e,r)=>{const i=W;return z(),U("div",null,[T(i,{ref_key:"refForm",ref:N,config:b(j)},null,8,["config"]),T(oe,{ref_key:"refDetail",ref:I,tabs:b(a).tabs,telport:e.telport,onRefreshed:r[0]||(r[0]=()=>b(a).curOperate="viewingDetail"),onRefreshing:r[1]||(r[1]=()=>b(a).curOperate="detailing"),onClose:r[2]||(r[2]=()=>b(a).curOperate==="analysed"),onRowdblclick:D},null,8,["tabs","telport"])])}}}),Nt=X(se,[["__scopeId","data-v-5b43af10"]]);export{Nt as default};
