/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.virtual;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.thingsboard.server.common.data.id.VirtualId;

import java.math.BigDecimal;
import java.util.HashMap;

@Data
@EqualsAndHashCode(callSuper = true)
public class VirtualData extends Virtual {

    private HashMap<String, BigDecimal> data;

    public VirtualData(){}


    public VirtualData(Virtual virtual){
        this.id=virtual.getId();
        this.setFormula(virtual.getFormula());
        this.setVirtualGroup(virtual.getVirtualGroup());
        this.setName(virtual.getName());
        this.setSerialNumber(virtual.getSerialNumber());
        this.setTenantId(virtual.getTenantId());
        this.setType(virtual.getType());
        this.setUnit(virtual.getUnit());
        this.setVirtualType(virtual.getVirtualType());
    }

    public VirtualData(VirtualId virtualId) {
        super(virtualId);
    }
}
