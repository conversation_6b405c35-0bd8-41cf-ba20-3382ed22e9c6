<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.deviceType.DeviceTypeAttrMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           serial_id,
                           code,
                           "name",
                           remark,
                           creator,
                           create_time,
                           update_time,
                           tenant_id<!--@sql from m_device_type_attr -->
    </sql>
    <sql id="Named_Column_List">
        <!--@sql select -->attr.id,
                           attr.serial_id,
                           attr.code,
                           attr."name",
                           attr.remark,
                           attr.creator,
                           attr.create_time,
                           attr.update_time,
                           attr.tenant_id<!--@sql from m_device_type_attr attr -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.deviceManage.DeviceTypeAttr">
        <result column="id" property="id"/>
        <result column="serial_id" property="serialId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Named_Column_List"/>
        from m_device_type_attr attr,
             m_device_type type
        <where>
            attr.serial_id = type.serial_id
            <if test="name != null and name != ''">
                and attr.name like '%' || #{name} || '%'
            </if>
            <if test="serialId != null and serialId != '' and serialId != topSerialId">
                and device_type_is_type_by_code_like(attr.serial_id, #{serialId}, #{tenantId})
            </if>
            <if test="code != null and code != ''">
                and code like '%' || #{code} || '%'
            </if>
            <if test="fromTime != null">
                and attr.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and attr.create_time &lt;= #{toTime}
            </if>
            <if test="creator != null and creator != ''">
                and attr.creator = #{creator}
            </if>
            <!--    type    -->
            <if test="typeName != null and typeName != ''">
                and type.name like '%' || #{typeName} || '%'
            </if>
            and attr.tenant_id = #{tenantId}
            and type.tenant_id = #{tenantId}
        </where>
        order by attr.create_time
    </select>

    <update id="update">
        update m_device_type_attr
        <set>
            <if test="serialId != null">
                serial_id = #{serialId},
            </if>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            update_time = now(),
        </set>
        where id = #{id}
    </update>
</mapper>