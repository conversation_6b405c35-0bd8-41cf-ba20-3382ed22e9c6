package org.thingsboard.server.dao.model.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class  UserChangeLogListRequest {

    private int page;

    private int size;

    private String executor;// 操作人

    private String userCode;// 用户编号

    private String userName;// 用户名称

    private String orgId;

    private List<String> orgIdList;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;// 执行时间范围的开始时间

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;// 执行时间范围的结束时间

    private String tenantId;

}
