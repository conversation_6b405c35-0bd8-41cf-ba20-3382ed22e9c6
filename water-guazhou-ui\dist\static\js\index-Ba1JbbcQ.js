import{d as z,c as S,r as y,a0 as T,bB as j,o as F,g as r,n as c,q as l,F as h,p as a,aB as v,aJ as w,i as C,cs as G,bh as p,aw as D,j as M,cU as V,b6 as O,C as U}from"./index-r0dFAfgr.js";import{_ as $}from"./index-C9hz-UZb.js";import{_ as A}from"./Search-NSrhrIa_.js";import{u as H}from"./useStation-DJgnSZIA.js";import{c as J,d as Q}from"./monitoringOverview-DvKhtmcR.js";import R from"./stationDetailMonitoring-D3m0WhoN.js";import{G as Y}from"./Group1-DZYehK7n.js";import{I as K}from"./common-CvK_P_ao.js";import"./zhandian-YaGuQZe6.js";import"./CardSearch-CB_HNR-Q.js";import"./index-B69llYYW.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";import"./URLHelper-B9aplt5w.js";/* empty css                         */import"./echart-BHaUp-st.js";import"./headwaterMonitoring-BgK7jThW.js";const X={class:"wrapper"},Z={class:"statistics"},tt={class:"item-inner"},et={style:{"padding-right":"8px"}},at={class:"title"},st={class:"title_card"},ot={class:"card-box"},nt={class:"monitor-title"},it={class:"title"},rt=["onClick"],ct={class:"monitor-table"},lt={class:"monitor-name"},pt={class:"monitor"},dt={style:{"padding-left":"10px"}},ut=z({__name:"index",setup(mt){const{getStationTree:P}=H(),_=S([]),b=S(),t=y({keywords:"",drawerTitle:"",monitor:null,Statistics:[{className:"text-orange",title:"总供水量",count:0,unit:"m³",icon:"ic:outline-water-drop",type:"totalWaterSupply"},{className:"text-green",title:"昨日供水量",count:0,unit:"m³",icon:"subway:refresh-time",type:"yesterdayWaterSupply"},{className:"text-blue",title:"今日供水量",count:0,unit:"m³",icon:"subway:time-2",type:"todayWaterSupply"},{className:"text-green",title:"上月供水量",count:0,unit:"m³",icon:"ri:calendar-event-line",type:"lastMonthWaterSupply"},{className:"text-blue",title:"本月供水量",count:0,unit:"m³",icon:"ri:calendar-line",type:"monthWaterSupply"},{className:"text-red",title:"报警数",count:0,unit:"个",icon:"ph:bell-simple-bold",type:"alarmNum"}],stationChangeData:[],dataGroup:[],activeName:"全部",tabsList:[],stationTree:[],treeDataType:"Project",stationId:"",icon:Y}),k=S(),W=y({title:"",labelWidth:"130px",width:1400,group:[],cancel:!1,onClosed:()=>{t.stationId="",t.treeDataType}}),f=y({data:[],loading:!0,title:"区域划分",expandOnClickNode:!1,treeNodeHandleClick:async e=>{f.currentProject!==e&&(f.currentProject=e,t.treeDataType=e.data.type,t.treeDataType==="Project"?(T().SET_selectedProject(e),_.value=[],t.stationChangeData=[],await N()):j(()=>{t.stationId=e.id}))}}),B=e=>{var d;(d=k.value)==null||d.openDrawer(),j(()=>{t.drawerTitle=e.title,t.stationId=e.id,t.monitor=e,t.treeDataType="Station"})},q=y({filters:[{type:"input",label:"",field:"keywords",placeholder:"请输入关键字进行筛选",btn:{icon:K.QUERY,type:"primary",perm:!0,click:()=>{var s;const d=(((s=b.value)==null?void 0:s.queryParams)||{}).keywords||"";t.dataGroup=t.stationChangeData.filter(i=>i.title.includes(d))}}}]}),I=y({type:"tabs",tabType:"simple",stretch:!1,tabs:[],handleTabClick:e=>{if(t.dataGroup=_.value,t.activeName=e.props.name,e.props.name!=="全部"){const d=t.stationChangeData.filter(s=>s.id===e.props.name||s.title==="全部");t.dataGroup=[],d.map(s=>{var u;const i=(u=s==null?void 0:s.monitorData)==null?void 0:u.map(o=>({...o,stationId:e.props.name}));t.dataGroup=t.dataGroup.concat(i)})}}}),N=async e=>{var i,u;await J({projectId:(i=T().selectedProject)==null?void 0:i.value}).then(o=>{const n=o.data.data||{};t.Statistics.map(m=>{m.count=n[m.type]??0})});const s=(u=(await Q({projectId:e==null?void 0:e.value})).data)==null?void 0:u.data;console.log(s),I.tabs=[{value:"全部",label:"全部"}],s.map(o=>{I.tabs.push({value:o.stationId,label:o.name}),t.stationChangeData.push({id:o.stationId,title:o.name,monitorData:o==null?void 0:o.dataGroup}),console.log(t.stationChangeData),_.value=t.stationChangeData,console.log("dddd",_.value),t.dataGroup=_.value})};return F(async()=>{const e=await P("泵站");f.data=e,f.currentProject=e[0],await N()}),(e,d)=>{const s=A,i=$,u=V,o=O;return r(),c("div",X,[l(i,{overlay:""},{default:h(()=>[a("div",Z,[(r(!0),c(v,null,w(t.Statistics,(n,m)=>(r(),c("div",{key:m,class:"statistics-item"},[a("div",tt,[a("div",et,[l(C(G),{icon:n.icon,class:"iconClass",style:{"font-size":"16px"}},null,8,["icon"])]),a("span",at,p(n.title),1),a("span",{class:D(["count",n.className])},p(n.count),3),a("span",{class:D(["unit",n.className])},p(n.unit),3)])]))),128))]),a("div",st,[l(s,{ref_key:"refSearch",ref:b,class:"serch",config:q},null,8,["config"])])]),_:1}),a("div",ot,[a("div",{class:D(["card-item",{isDark:C(M)().isDark}])},[(r(!0),c(v,null,w(t.dataGroup,(n,m)=>(r(),c("div",{key:m,class:"card-content"},[l(i,{title:" ",class:"inner-card left"},{query:h(()=>[a("div",nt,[a("div",it,[l(u,{src:t.icon,style:{width:"35px",height:"36px"}},null,8,["src"]),a("span",null,p(n.title),1)]),a("div",{onClick:x=>B(n)},[l(C(G),{icon:"ph:warning-circle-bold",style:{color:"#4F7DB8","font-size":"18px"}})],8,rt)])]),default:h(()=>[a("div",ct,[(r(!0),c(v,null,w(n.monitorData,(x,L)=>(r(),c("div",{key:L,style:{width:"100%","text-align":"center"}},[a("div",lt,p(x.name),1),a("div",pt,[(r(!0),c(v,null,w(x.dataList,(g,E)=>(r(),c("div",{key:E,class:"box-1"},[a("div",null,p(g.propertyName)+" "+p(g.unit?"("+g.unit+")":""),1),a("div",null,p(g.value||"无"),1)]))),128))])]))),128))])]),_:2},1024)]))),128))],2)]),l(o,{ref_key:"refDrawer",ref:k,config:W},{title:h(()=>[l(u,{src:t.icon,style:{width:"35px",height:"36px"}},null,8,["src"]),a("span",dt,p(t.drawerTitle),1)]),default:h(()=>[l(R,{"station-id":t.stationId,monitor:t.monitor},null,8,["station-id","monitor"])]),_:1},8,["config"])])}}}),Gt=U(ut,[["__scopeId","data-v-8d591866"]]);export{Gt as default};
