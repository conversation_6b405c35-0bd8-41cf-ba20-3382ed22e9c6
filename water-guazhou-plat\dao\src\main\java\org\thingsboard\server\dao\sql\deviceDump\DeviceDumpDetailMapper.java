package org.thingsboard.server.dao.sql.deviceDump;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.deviceDump.DeviceDumpDetail;
import org.thingsboard.server.dao.util.imodel.query.deviceDump.DeviceDumpDetailPageRequest;

import java.util.List;

@Mapper
public interface DeviceDumpDetailMapper extends BaseMapper<DeviceDumpDetail> {
    IPage<DeviceDumpDetail> findByPage(DeviceDumpDetailPageRequest request);

    boolean update(DeviceDumpDetail entity);

    int saveAll(List<DeviceDumpDetail> list);

    int updateAll(List<DeviceDumpDetail> list);

    int removeAllByMainId(String id);
}
