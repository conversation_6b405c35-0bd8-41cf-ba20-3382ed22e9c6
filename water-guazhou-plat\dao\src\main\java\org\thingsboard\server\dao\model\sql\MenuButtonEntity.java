package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * 菜单按钮
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MENU_BUTTON)
@NoArgsConstructor
@AllArgsConstructor
public class MenuButtonEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.MENU_BUTTON_MENU_ID)
    private String menuId;

    @Column(name = ModelConstants.MENU_BUTTON_NAME)
    private String name;

    @Column(name = ModelConstants.MENU_BUTTON_PERMISSIONS)
    private String permissions;

    @Column(name = ModelConstants.MENU_BUTTON_ICON)
    private String icon;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.MENU_BUTTON_ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.MENU_POOL_ORDER_NUM)
    private Integer orderNum;



}
