package org.thingsboard.server.dao.model.sql.statistic;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class StatisticCountAndPercent {
    private int count;

    private int total;

    private double percent;

    public StatisticCountAndPercent() {

    }

    public StatisticCountAndPercent(int count, int total) {
        this.count = count;
        this.total = total;
        this.percent = total == 0 ? 0 : (double) count / total;
    }
}
