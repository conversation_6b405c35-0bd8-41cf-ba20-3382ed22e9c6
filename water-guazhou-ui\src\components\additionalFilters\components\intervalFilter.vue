<template>
  <el-form-item label="数据间隔">
    <el-radio-group
      v-model="option"
      style="height:48px;display:flex;align-items:center"
      :change="optionChange"
    >
      <el-radio
        style="margin-bottom:0"
        :label="'day'"
      >
        日
      </el-radio>
      <el-radio
        style="margin-bottom:0"
        :label="'month'"
      >
        月
      </el-radio>
      <el-radio
        style="margin-bottom:0"
        :label="'year'"
      >
        年
      </el-radio>
    </el-radio-group>
  </el-form-item>
</template>

<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['config'],
  data() {
    return {
      option: 'day'
    }
  },
  methods: {
    optionChange() {
      // this.config.handleChange(val)
      // this.$emit('')
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
