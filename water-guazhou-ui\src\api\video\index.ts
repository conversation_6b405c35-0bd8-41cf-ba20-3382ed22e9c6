import { APPKEY } from '@/common/constants/videos';
import {
  ISaveVideo,
  ISignVideoParam,
  IVideoLive,
  IVideoRecord,
  IVideoSource
} from '@/common/types/video';
import request, { videoAxios } from '@/plugins/axios/video';

/** save User */
// TODO: Need ReFacotr
export function saveVideo(params) {
  return request({
    url: '/api/video/save',
    method: 'post',
    data: params
  });
}

// 获取项目下视频
export function getVideo(projectId, params?: any) {
  return request({
    url: `/api/video/findByProject/${projectId}`,
    method: 'get',
    params
  });
}
export function deleteVideo(id) {
  return request({
    url: `/api/video/delete/${id}`,
    method: 'delete'
  });
}

export function deleteVideoList(params) {
  return request({
    url: '/api/video/delete/all',
    method: 'post',
    data: params
  });
}

// -----------  能力视频相关 /api  -----------

// 生成 能力视频appkey
export function getVideoNLSign(params) {
  return request({
    url: '/api/aep/sign',
    method: 'post',
    data: params
  });
}

export function NLVideoCall(params) {
  return request({
    url: '/api/aep/call',
    method: 'post',
    data: params
  });
}

// 能力智慧视频
export function getNLDevice(params) {
  return videoAxios({
    url: `/api/dict/device/select?appkey=${APPKEY}`,
    method: 'post',
    data: params
  });
}

// 能力预览
export function mediaLive(params) {
  return videoAxios({
    url: `/api/dict/media/live?appkey=${APPKEY}`,
    method: 'post',
    data: params
  });
}

// 能力 云台方向控制 /device/ctrl/ptz
export function ctrlChangePtz(params) {
  return videoAxios({
    url: `/api/dict/device/ctrl/ptz?appkey=${APPKEY}`,
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'application/JSON'
    }
  });
}

// 能力 聚焦控制 /device/ctrl/zoom
export function ctrlChangeZoom(params) {
  return videoAxios({
    url: `/api/dict/device/ctrl/zoom?appkey=${APPKEY}`,
    method: 'post',
    data: params
  });
}

// 能力 光圈控制 /device/ctrl/aperture
export function ctrlChangeAperture(params) {
  return videoAxios({
    url: `/api/dict/device/ctrl/aperture?appkey=${APPKEY}`,
    method: 'post',
    data: params
  });
}

// 能力 存储空间信息查询 /space/status
export function getSpaceStatus(params) {
  return videoAxios({
    url: `/api/dict/space/status?appkey=${APPKEY}`,
    method: 'post',
    data: params
  });
}

// 能力 云端录像查询 /storage/find/videorecord
export function getVideorecord(params) {
  return videoAxios({
    url: `/api/dict/storage/find/videorecord?appkey=${APPKEY}`,
    method: 'post',
    data: params
  });
}

// 获取播放源
export function getVideosName(params?: string) {
  return request({
    url: '/api/video/camaraList',
    method: 'get',
    params
  });
}

// 获取视频地址
export function getvideourl(params: string) {
  return request({
    url: `/api/video/getPreviewUrl/${params}`,
    method: 'get'
  });
}

// 获取视频地址Code
export function getVideoUrlCode(code: string) {
  return request({
    url: `/api/video/getPreviewUrlByCode/${code}`,
    method: 'get'
  });
}

// 获取视频地址New
export function getVideoUrlById(id: string) {
  return request({
    url: `/api/video/getPreviewUrlById/${id}`,
    method: 'get'
  });
}

// 视频云台功能
export function ctrlNewChangeZoom(params) {
  return request({
    url: `/api/video/controlling`,
    method: 'post',
    data: params
  });
}

// 回放
export function getPlayback(params: {
  id: string;
  start?: number | null;
  end?: number | null;
}) {
  return request({
    url: `/api/video/getPlayback`,
    method: 'get',
    params
  });
}

// rtsp转m3u8
export function getHlv(params: { rtspUrl: string }) {
  return request({
    url: `/api/rtsp/hlv`,
    method: 'get',
    params
  });
}

// ----------------------------------------
// 保存视频到我们系统
export function saveNyVideo(params) {
  return request({
    url: '/api/video/video/save',
    method: 'post',
    data: params
  });
}

// 我们系统保存的能力视频  /api/video/findByProjectAndType/{projectId}/NY
export function findByProjectAndType(projectId, params?: any) {
  return request({
    url: `/api/video/findByProject/${projectId}`,
    method: 'get',
    params
  });
}

// 地图 企业下所有视频  /api/video/findByTenantIdAndType/NY
export function findByTenantIdAndType() {
  return request({
    url: '/api/video/findByTenantIdAndType/NY',
    method: 'get'
  });
}

/** ********************************
 * add by lichao
 * 20210929
 *
 */

/**
 * 保存视频
 * @param params
 * @returns
 */
export const SuYuan_SaveVideo = (params: ISaveVideo) =>
  request({
    url: '/api/video/save',
    method: 'post',
    data: params
  });
/**
 * 根据项目id和类型获取视频列表
 * @param projectId
 * @param type
 * @returns
 */
export const SuYuan_GetVideos = (projectId: string, params?: any) =>
  request({
    url: `/api/video/findByProject/${projectId}`,
    method: 'get',
    params
  });
/**
 * 获取视频源列表
 * @param params
 * @returns
 */
export const SuYuan_GetVideoSources = (params: IVideoSource) =>
  videoAxios({
    url: `/api/dict/device/select?appkey=${APPKEY}`,
    method: 'post',
    data: params
  });
/**
 * 注册视频
 * @param params
 * @returns sign
 */
export const SuYuan_SignVideo = (params: ISignVideoParam) =>
  request({
    url: '/api/aep/sign',
    method: 'post',
    data: params
  });
/**
 * 请求在线视频
 * @param appkey
 * @param params
 * @returns
 */
export const SuYuan_GetVideoLive = (params: IVideoLive) =>
  videoAxios({
    url: `/api/dict/media/live?appkey=${APPKEY}`,
    method: 'post',
    data: params
  });
/**
 * 获取视频记录
 * @param appkey
 * @param params
 * @returns
 */
export const SuYuan_VideoRecords = (params: IVideoRecord) =>
  videoAxios({
    url: `/api/dict/storage/find/videorecord?appkey=${APPKEY}`,
    method: 'post',
    data: params
  });
/**
 * 根据id数组批量删除视频信息
 * @param ids
 * @returns
 */
export const SuYuan_DeleteVideo = (ids: string[]) =>
  request({
    url: '/api/video/delete/all',
    method: 'post',
    data: ids
  });
/** ******************************** */
// 品牌监控配置

export const getVideoConfig = () =>
  request({
    url: '/api/video/config',
    method: 'get'
  });

export const saveConfig = (params: any) =>
  request({
    url: '/api/video/config/save',
    method: 'post',
    data: params
  });

// 录入/保存摄像头
export const saveCamera = (params: {
  // 项目id 保存到项目时使用
  projectId: string;
  // 分组id  添加到分组时使用
  groupId: string;
  // 摄像头列表
  videoList: {
    // 摄像头id
    id: string;
    // 名称
    name: string;
    // 序号
    orderNum: string;
    // ...
    [key: string]: any;
  }[];
}) =>
  request({
    url: '/api/video/batchSave',
    method: 'post',
    data: params
  });

// 获取分组树
export const getCameraGroupTree = (params: {
  // 项目id
  projectId: string;
}) =>
  request({
    url: `/api/video/group/tree/${params.projectId}`,
    method: 'get'
  });

// 新增/修改分组
export const postCameraGroup = (params: {
  //
  id?: string;
  // 分组名
  name: string;
  // 上级id
  parentId: string;
  // 序号
  orderNum: number;
}) =>
  request({
    url: '/api/video/group/save',
    method: 'post',
    data: params
  });

// 删除分组
export const deleteCameraGroup = (id: string) =>
  request({
    url: `/api/video/group/delete/${id}`,
    method: 'delete'
  });

// 摄像头树（包含项目和分组）
export const getCameraTree = () =>
  request({
    url: `/api/video/tree`,
    method: 'get'
  });

// 获取分组下视频列表
export const getGroupVideoList = (params: {
  // 项目id
  projectId: string;
  // 分组id
  groupId: string;
}) =>
  request({
    url: `/api/video/findList`,
    method: 'get',
    params
  });

// 获取视频监控列表（分页）
export const getVideoMonitoringList = (params: {
  // 页码
  page: number;
  // 每页条数
  size: number;
  // 设备名称
  name?: string;
  // 设备编号
  serialNumber?: string;
  // 地址
  location?: string;
  // 项目ID
  projectId?: string;
}) =>
  request({
    url: `/api/video/findByPage`,
    method: 'get',
    params
  });
