import{ab as De,j as Z,u as se,a9 as Re,E as Ae,i as ie,e as F,y as Q,a as Pe}from"./Point-WxyopZva.js";import{hp as Me,bY as Le,bs as L,eu as Be,gs as fe,dI as _e,dJ as pe,dM as Oe,dL as ge,dG as H,ht as Ue,A as X,bB as ke,z as Ve,g as Ee,d1 as ze,eo as $e,bC as re}from"./MapView-DaoQedLH.js";import{T as B,R as p,aO as U,cl as He,aW as q,a5 as Fe}from"./index-r0dFAfgr.js";import{c as qe,l as Ne,k as Qe}from"./widget-BcWKanF2.js";import{E as P,I as D}from"./enums-BRzLM11V.js";import{U as We,d as oe}from"./pe-B8dP0-Ut.js";import{t as M}from"./Rect-CUzevAry.js";import{P as me,G as be,D as je,F as I,O as z,I as ne,R as $,C as we,E as Je}from"./enums-BDQrMlcz.js";import{E as xe}from"./Texture-BYqObwfn.js";import{e as Ge}from"./rasterizingUtils-BGZonnNf.js";import{E as C,f as E}from"./FramebufferObject-8j9PRuxE.js";import{e as W,t as Ye,c as Ke}from"./config-MDUrh2eL.js";import{r as Se}from"./TiledDisplayObject-C5kAiJtw.js";import{n as Ze,l as k,r as Xe,i as ee,a as R}from"./StyleDefinition-Bnnz5uyC.js";import{T as O}from"./enums-L38xj_2E.js";import{i as et}from"./TileContainer-CC8_A7ZF.js";import{l as ae}from"./StyleRepository-CdCHyVhB.js";import{f as tt,u as st}from"./LayerView-BSt9B8Gh.js";import"./ProgramTemplate-tdUBoAol.js";import{r as it}from"./Container-BwXq1a-x.js";import{t as rt}from"./VertexElementDescriptor-BOD-G50G.js";import"./floatRGBA-PQQNbO39.js";import"./enums-B5k73o5q.js";import"./WGLContainer-Dyx9110G.js";import"./definitions-826PWLuy.js";import"./vec4f32-CjrfB-0a.js";import"./color-DAS1c3my.js";import"./number-CoJp78Rz.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./utils-DPUVnAXL.js";import"./GeometryUtils-BRRfazic.js";import"./earcut-BJup91r2.js";import"./GeometryUtils-B7ExOJII.js";class N{constructor(e,s){this._width=0,this._height=0,this._free=[],this._width=e,this._height=s,this._free.push(new M(0,0,e,s))}get width(){return this._width}get height(){return this._height}allocate(e,s){if(e>this._width||s>this._height)return new M;let t=null,i=-1;for(let r=0;r<this._free.length;++r){const o=this._free[r];e<=o.width&&s<=o.height&&(t===null||o.y<=t.y&&o.x<=t.x)&&(t=o,i=r)}return t===null?new M:(this._free.splice(i,1),t.width<t.height?(t.width>e&&this._free.push(new M(t.x+e,t.y,t.width-e,s)),t.height>s&&this._free.push(new M(t.x,t.y+s,t.width,t.height-s))):(t.width>e&&this._free.push(new M(t.x+e,t.y,t.width-e,t.height)),t.height>s&&this._free.push(new M(t.x,t.y+s,e,t.height-s))),new M(t.x,t.y,e,s))}release(e){for(let s=0;s<this._free.length;++s){const t=this._free[s];if(t.y===e.y&&t.height===e.height&&t.x+t.width===e.x)t.width+=e.width;else if(t.x===e.x&&t.width===e.width&&t.y+t.height===e.y)t.height+=e.height;else if(e.y===t.y&&e.height===t.height&&e.x+e.width===t.x)t.x=e.x,t.width+=e.width;else{if(e.x!==t.x||e.width!==t.width||e.y+e.height!==t.y)continue;t.y=e.y,t.height+=e.height}this._free.splice(s,1),this.release(e)}this._free.push(e)}}let le=class{constructor(e,s,t){this.width=0,this.height=0,this._dirties=[],this._glyphData=[],this._currentPage=0,this._glyphIndex={},this._textures=[],this._rangePromises=new Map,this.width=e,this.height=s,this._glyphSource=t,this._binPack=new N(e-4,s-4),this._glyphData.push(new Uint8Array(e*s)),this._dirties.push(!0),this._textures.push(void 0)}getGlyphItems(e,s){const t=[],i=this._glyphSource,r=new Set,o=1/256;for(const a of s){const l=Math.floor(a*o);r.add(l)}const n=[];return r.forEach(a=>{if(a<=256){const l=e+a;if(this._rangePromises.has(l))n.push(this._rangePromises.get(l));else{const u=i.getRange(e,a).then(()=>{this._rangePromises.delete(l)},()=>{this._rangePromises.delete(l)});this._rangePromises.set(l,u),n.push(u)}}}),Promise.all(n).then(()=>{let a=this._glyphIndex[e];a||(a={},this._glyphIndex[e]=a);for(const l of s){const u=a[l];if(u){t[l]={sdf:!0,rect:u.rect,metrics:u.metrics,page:u.page,code:l};continue}const y=i.getGlyph(e,l);if(!y||!y.metrics)continue;const d=y.metrics;let c;if(d.width===0)c=new M(0,0,0,0);else{const f=d.width+6,g=d.height+2*3;let m=f%4?4-f%4:4,w=g%4?4-g%4:4;m===1&&(m=5),w===1&&(w=5),c=this._binPack.allocate(f+m,g+w),c.isEmpty&&(this._dirties[this._currentPage]||(this._glyphData[this._currentPage]=null),this._currentPage=this._glyphData.length,this._glyphData.push(new Uint8Array(this.width*this.height)),this._dirties.push(!0),this._textures.push(void 0),this._binPack=new N(this.width-4,this.height-4),c=this._binPack.allocate(f+m,g+w));const b=this._glyphData[this._currentPage],S=y.bitmap;let v,T;if(S)for(let A=0;A<g;A++){v=f*A,T=this.width*(c.y+A+1)+c.x;for(let x=0;x<f;x++)b[T+x+1]=S[v+x]}}a[l]={rect:c,metrics:d,tileIDs:null,page:this._currentPage},t[l]={sdf:!0,rect:c,metrics:d,page:this._currentPage,code:l},this._dirties[this._currentPage]=!0}return t})}removeGlyphs(e){for(const s in this._glyphIndex){const t=this._glyphIndex[s];if(!t)continue;let i;for(const r in t)if(i=t[r],i.tileIDs.delete(e),i.tileIDs.size===0){const o=this._glyphData[i.page],n=i.rect;let a,l;for(let u=0;u<n.height;u++)for(a=this.width*(n.y+u)+n.x,l=0;l<n.width;l++)o[a+l]=0;delete t[r],this._dirties[i.page]=!0}}}bind(e,s,t,i=0){this._textures[t]||(this._textures[t]=new xe(e,{pixelFormat:me.ALPHA,dataType:be.UNSIGNED_BYTE,width:this.width,height:this.height},new Uint8Array(this.width*this.height)));const r=this._textures[t];r.setSamplingMode(s),this._dirties[t]&&r.setData(this._glyphData[t]),e.bindTexture(r,i),this._dirties[t]=!1}dispose(){this._binPack=null;for(const e of this._textures)e&&e.dispose();this._textures.length=0}},G=class{constructor(e){if(this._metrics=[],this._bitmaps=[],e)for(;e.next();)switch(e.tag()){case 1:{const s=e.getMessage();for(;s.next();)switch(s.tag()){case 3:{const t=s.getMessage();let i,r,o,n,a,l,u;for(;t.next();)switch(t.tag()){case 1:i=t.getUInt32();break;case 2:r=t.getBytes();break;case 3:o=t.getUInt32();break;case 4:n=t.getUInt32();break;case 5:a=t.getSInt32();break;case 6:l=t.getSInt32();break;case 7:u=t.getUInt32();break;default:t.skip()}t.release(),i&&(this._metrics[i]={width:o,height:n,left:a,top:l,advance:u},this._bitmaps[i]=r);break}default:s.skip()}s.release();break}default:e.skip()}}getMetrics(e){return this._metrics[e]}getBitmap(e){return this._bitmaps[e]}},ot=class{constructor(){this._ranges=[]}getRange(e){return this._ranges[e]}addRange(e,s){this._ranges[e]=s}},he=class{constructor(e){this._glyphInfo={},this._baseURL=e}getRange(e,s){const t=this._getFontStack(e);if(t.getRange(s))return Promise.resolve();const i=256*s,r=i+255;if(this._baseURL){const o=this._baseURL.replace("{fontstack}",e).replace("{range}",i+"-"+r);return We(o,{responseType:"array-buffer"}).then(n=>{t.addRange(s,new G(new Me(new Uint8Array(n.data),new DataView(n.data))))}).catch(()=>{t.addRange(s,new G)})}return t.addRange(s,new G),Promise.resolve()}getGlyph(e,s){const t=this._getFontStack(e);if(!t)return;const i=Math.floor(s/256);if(i>256)return;const r=t.getRange(i);return r?{metrics:r.getMetrics(s),bitmap:r.getBitmap(s)}:void 0}_getFontStack(e){let s=this._glyphInfo[e];return s||(s=this._glyphInfo[e]=new ot),s}};const nt="dasharray-";let Y=class Te{constructor(e,s,t=0){this._size=[],this._mosaicsData=[],this._textures=[],this._dirties=[],this._maxItemSize=0,this._currentPage=0,this._pageWidth=0,this._pageHeight=0,this._mosaicRects={},this.pixelRatio=1,(e<=0||s<=0)&&console.error("Sprites mosaic defaultWidth and defaultHeight must be greater than zero!"),this._pageWidth=e,this._pageHeight=s,t>0&&(this._maxItemSize=t),this._binPack=new N(e-4,s-4)}dispose(){this._binPack=null,this._mosaicRects={};for(const e of this._textures)e&&e.dispose();this._textures.length=0}getWidth(e){return e>=this._size.length?-1:this._size[e][0]}getHeight(e){return e>=this._size.length?-1:this._size[e][1]}getPageSize(e){return e>=this._size.length?null:this._size[e]}setSpriteSource(e){if(this.dispose(),this.pixelRatio=e.devicePixelRatio,this._mosaicsData.length===0){this._binPack=new N(this._pageWidth-4,this._pageHeight-4);const s=Math.floor(this._pageWidth),t=Math.floor(this._pageHeight),i=new Uint32Array(s*t);this._mosaicsData[0]=i,this._dirties.push(!0),this._size.push([this._pageWidth,this._pageHeight]),this._textures.push(void 0)}this._sprites=e}getSpriteItem(e,s=!1){let t,i,r=this._mosaicRects[e];if(r)return r;if(!this._sprites||this._sprites.loadStatus!=="loaded"||(e&&e.startsWith(nt)?([t,i]=this._rasterizeDash(e),s=!0):t=this._sprites.getSpriteInfo(e),!t||!t.width||!t.height||t.width<0||t.height<0))return null;const o=t.width,n=t.height,[a,l,u]=this._allocateImage(o,n);return a.width<=0?null:(this._copy(a,t,l,u,s,i),r={rect:a,width:o,height:n,sdf:t.sdf,simplePattern:!1,pixelRatio:t.pixelRatio,page:l},this._mosaicRects[e]=r,r)}getSpriteItems(e){const s={};for(const t of e)s[t.name]=this.getSpriteItem(t.name,t.repeat);return s}getMosaicItemPosition(e,s){const t=this.getSpriteItem(e,s),i=t&&t.rect;if(!i)return null;i.width=t.width,i.height=t.height;const r=t.width,o=t.height,n=2;return{tl:[i.x+n,i.y+n],br:[i.x+n+r,i.y+n+o],page:t.page}}bind(e,s,t=0,i=0){if(t>=this._size.length||t>=this._mosaicsData.length)return;this._textures[t]||(this._textures[t]=new xe(e,{pixelFormat:me.RGBA,dataType:be.UNSIGNED_BYTE,wrapMode:je.CLAMP_TO_EDGE,width:this._size[t][0],height:this._size[t][1]},new Uint8Array(this._mosaicsData[t].buffer)));const r=this._textures[t];r.setSamplingMode(s),this._dirties[t]&&r.setData(new Uint8Array(this._mosaicsData[t].buffer)),e.bindTexture(r,i),this._dirties[t]=!1}static _copyBits(e,s,t,i,r,o,n,a,l,u,y){let d=i*s+t,c=a*o+n;if(y){c-=o;for(let _=-1;_<=u;_++,d=((_+u)%u+i)*s+t,c+=o)for(let f=-1;f<=l;f++)r[c+f]=e[d+(f+l)%l]}else for(let _=0;_<u;_++){for(let f=0;f<l;f++)r[c+f]=e[d+f];d+=s,c+=o}}_copy(e,s,t,i,r,o){if(!this._sprites||this._sprites.loadStatus!=="loaded"||t>=this._mosaicsData.length)return;const n=new Uint32Array(o?o.buffer:this._sprites.image.buffer),a=this._mosaicsData[t];a&&n||console.error("Source or target images are uninitialized!");const l=2,u=o?s.width:this._sprites.width;Te._copyBits(n,u,s.x,s.y,a,i[0],e.x+l,e.y+l,s.width,s.height,r),this._dirties[t]=!0}_allocateImage(e,s){e+=2,s+=2;const t=Math.max(e,s);if(this._maxItemSize&&this._maxItemSize<t){const n=new M(0,0,e,s);return this._mosaicsData.push(new Uint32Array(e*s)),this._dirties.push(!0),this._size.push([e,s]),this._textures.push(void 0),[n,this._mosaicsData.length-1,[e,s]]}let i=e%4?4-e%4:4,r=s%4?4-s%4:4;i===1&&(i=5),r===1&&(r=5);const o=this._binPack.allocate(e+i,s+r);return o.width<=0?(this._dirties[this._currentPage]||(this._mosaicsData[this._currentPage]=null),this._currentPage=this._mosaicsData.length,this._mosaicsData.push(new Uint32Array(this._pageWidth*this._pageHeight)),this._dirties.push(!0),this._size.push([this._pageWidth,this._pageHeight]),this._textures.push(void 0),this._binPack=new N(this._pageWidth-4,this._pageHeight-4),this._allocateImage(e,s)):[o,this._currentPage,[this._pageWidth,this._pageHeight]]}_rasterizeDash(e){const s=/\[(.*?)\]/,t=e.match(s);if(!t)return null;const i=t[1].split(",").map(Number),r=e.slice(e.lastIndexOf("-")+1),[o,n,a]=Ge(i,r);return[{x:0,y:0,width:n,height:a,sdf:!0,pixelRatio:1},new Uint8Array(o.buffer)]}},at=class{constructor(e,s,t){this._layer=e,this._styleRepository=s,this.devicePixelRatio=t,this._spriteMosaic=null,this._glyphMosaic=null,this._connection=null}destroy(){var e;(e=this._connection)==null||e.close(),this._connection=null,this._styleRepository=null,this._layer=null,this._spriteMosaic=null,this._glyphMosaic=null}get spriteMosaic(){return this._spriteSourcePromise.then(()=>this._spriteMosaic)}get glyphMosaic(){return this._glyphMosaic}async start(e){this._spriteSourcePromise=this._layer.loadSpriteSource(this.devicePixelRatio,e),this._spriteSourcePromise.then(i=>{this._spriteMosaic=new Y(1024,1024,250),this._spriteMosaic.setSpriteSource(i)});const s=this._layer.currentStyleInfo.glyphsUrl,t=new he(s?oe(s,{...this._layer.customParameters,token:this._layer.apiKey}):null);this._glyphMosaic=new le(1024,1024,t),this._broadcastPromise=Le("WorkerTileHandler",{client:this,schedule:e.schedule,signal:e.signal}).then(i=>{if(this._connection=i,this._layer&&!this._connection.closed){const r=i.broadcast("setStyle",this._layer.currentStyleInfo.style,e);Promise.all(r).catch(o=>De(o))}})}async updateStyle(e){return await this._broadcastPromise,this._broadcastPromise=Promise.all(this._connection.broadcast("updateStyle",e)),this._broadcastPromise}setSpriteSource(e){const s=new Y(1024,1024,250);return s.setSpriteSource(e),this._spriteMosaic=s,this._spriteSourcePromise=Promise.resolve(e),s}async setStyle(e,s){await this._broadcastPromise,this._styleRepository=e,this._spriteSourcePromise=this._layer.loadSpriteSource(this.devicePixelRatio,null),this._spriteSourcePromise.then(i=>{this._spriteMosaic=new Y(1024,1024,250),this._spriteMosaic.setSpriteSource(i)});const t=new he(this._layer.currentStyleInfo.glyphsUrl?oe(this._layer.currentStyleInfo.glyphsUrl,{...this._layer.customParameters,token:this._layer.apiKey}):null);return this._glyphMosaic=new le(1024,1024,t),this._broadcastPromise=Promise.all(this._connection.broadcast("setStyle",s)),this._broadcastPromise}fetchTileData(e,s){return this._getRefKeys(e,s).then(t=>{const i=this._layer.sourceNameToSource,r=[];for(const o in i)r.push(o);return this._getSourcesData(r,t,s)})}parseTileData(e,s){const t=e&&e.data;if(!t)return Promise.resolve(null);const{sourceName2DataAndRefKey:i,transferList:r}=t;return Object.keys(i).length===0?Promise.resolve(null):this._broadcastPromise.then(()=>this._connection.invoke("createTileAndParse",{key:e.key.id,sourceName2DataAndRefKey:i,styleLayerUIDs:e.styleLayerUIDs},{...s,transferList:r}))}async getSprites(e){return await this._spriteSourcePromise,this._spriteMosaic.getSpriteItems(e)}getGlyphs(e){return this._glyphMosaic.getGlyphItems(e.font,e.codePoints)}async _getTilePayload(e,s,t){const i=L.pool.acquire(e.id),r=this._layer.sourceNameToSource[s],{level:o,row:n,col:a}=i;L.pool.release(i);try{return{protobuff:await r.requestTile(o,n,a,t),sourceName:s}}catch(l){if(Z(l))throw l;return{protobuff:null,sourceName:s}}}_getRefKeys(e,s){const t=this._layer.sourceNameToSource,i=new Array;for(const r in t){const o=t[r].getRefKey(e,s);i.push(o)}return se(i)}_getSourcesData(e,s,t){const i=[];for(let r=0;r<s.length;r++)if(s[r].value==null||e[r]==null)i.push(null);else{const o=this._getTilePayload(s[r].value,e[r],t);i.push(o)}return se(i).then(r=>{const o={},n=[];for(let a=0;a<r.length;a++){const l=r[a].value;if(l&&l.protobuff&&l.protobuff.byteLength>0){const u=s[a].value.id;o[l.sourceName]={refKey:u,protobuff:l.protobuff},n.push(l.protobuff)}}return{sourceName2DataAndRefKey:o,transferList:n}})}};const ce=512,lt=1e-6,ht=(h,e)=>h+1/(1<<2*e);class ct{constructor(e,s){this._tiles=new Map,this._tileCache=new Be(40,t=>t.dispose()),this._viewSize=[0,0],this._visibleTiles=new Map,this.acquireTile=e.acquireTile,this.releaseTile=e.releaseTile,this.tileInfoView=e.tileInfoView,this._container=s}destroy(){for(const[e,s]of this._tiles)s.dispose();this._tiles=null,this._tileCache.clear(),this._tileCache=null}update(e){this._updateCacheSize(e);const s=this.tileInfoView,t=s.getTileCoverage(e.state,0,"smallest"),{spans:i,lodInfo:r}=t,{level:o}=r,n=this._tiles,a=new Set,l=new Set;for(const{row:y,colFrom:d,colTo:c}of i)for(let _=d;_<=c;_++){const f=L.getId(o,y,r.normalizeCol(_),r.getWorldForColumn(_)),g=this._getOrAcquireTile(f);a.add(f),g.processed()?this._addToContainer(g):l.add(new L(f))}for(const[y,d]of n)d.isCoverage=a.has(y);for(const y of l)this._findPlaceholdersForMissingTiles(y,a);let u=!1;for(const[y,d]of n)d.neededForCoverage=a.has(y),d.neededForCoverage||d.isHoldingForFade&&s.intersects(t,d.key)&&a.add(y),d.isFading&&(u=!0);for(const[y,d]of this._tiles)a.has(y)||this._releaseTile(y);return fe.pool.release(t),!u}clear(){this._tiles.clear(),this._tileCache.clear(),this._visibleTiles.clear()}clearCache(){this._tileCache.clear()}_findPlaceholdersForMissingTiles(e,s){const t=[];for(const[r,o]of this._tiles)this._addPlaceholderChild(t,o,e,s);const i=t.reduce(ht,0);Math.abs(1-i)<lt||this._addPlaceholderParent(e.id,s)}_addPlaceholderChild(e,s,t,i){s.key.level<=t.level||!s.hasData()||dt(t,s.key)&&(this._addToContainer(s),i.add(s.id),e.push(s.key.level-t.level))}_addPlaceholderParent(e,s){const t=this._tiles;let i=e;for(;;){if(i=ut(i),!i||s.has(i))return;const r=t.get(i);if(r&&r.hasData())return this._addToContainer(r),void s.add(r.id)}}_getOrAcquireTile(e){let s=this._tiles.get(e);return s||(s=this._tileCache.pop(e),s||(s=this.acquireTile(new L(e))),this._tiles.set(e,s),s)}_releaseTile(e){const s=this._tiles.get(e);this.releaseTile(s),this._removeFromContainer(s),this._tiles.delete(e),s.hasData()?this._tileCache.put(e,s,1):s.dispose()}_addToContainer(e){let s;const t=[],i=this._container;if(i.contains(e))return;const r=this._visibleTiles;for(const[o,n]of r)this._canConnectDirectly(e,n)&&t.push(n),B(s)&&this._canConnectDirectly(n,e)&&(s=n);if(p(s)){for(const o of t)s.childrenTiles.delete(o),e.childrenTiles.add(o),o.parentTile=e;s.childrenTiles.add(e),e.parentTile=s}else for(const o of t)e.childrenTiles.add(o),o.parentTile=e;r.set(e.id,e),i.addChild(e)}_removeFromContainer(e){if(this._visibleTiles.delete(e.id),this._container.removeChild(e),p(e.parentTile)){e.parentTile.childrenTiles.delete(e);for(const s of e.childrenTiles)p(e.parentTile)&&e.parentTile.childrenTiles.add(s)}for(const s of e.childrenTiles)s.parentTile=e.parentTile;e.parentTile=null,e.childrenTiles.clear()}_canConnectDirectly(e,s){const t=e.key;let{level:i,row:r,col:o,world:n}=s.key;const a=this._visibleTiles;for(;i>0;){if(i--,r>>=1,o>>=1,t.level===i&&t.row===r&&t.col===o&&t.world===n)return!0;if(a.has(`${i}/${r}/${o}/${n}`))return!1}return!1}_updateCacheSize(e){const s=e.state.size;if(s[0]===this._viewSize[0]&&s[1]===this._viewSize[1])return;const t=Math.ceil(s[0]/ce)+1,i=Math.ceil(s[1]/ce)+1;this._viewSize[0]=s[0],this._viewSize[1]=s[1],this._tileCache.maxSize=5*t*i}}function ut(h){const[e,s,t,i]=h.split("/"),r=parseInt(e,10);return r===0?null:`${r-1}/${parseInt(s,10)>>1}/${parseInt(t,10)>>1}/${parseInt(i,10)}`}function dt(h,e){const s=e.level-h.level;return h.row===e.row>>s&&h.col===e.col>>s&&h.world===e.world}let yt=class{constructor(e){this.xTile=0,this.yTile=0,this.hash=0,this.priority=1,this.colliders=[],this.textVertexRanges=[],this.iconVertexRanges=[],this.tile=e}},ft=class{constructor(){this.tileSymbols=[],this.parts=[{startTime:0,startOpacity:0,targetOpacity:0,show:!1},{startTime:0,startOpacity:0,targetOpacity:0,show:!1}],this.show=!1}};function ue(h,e,s,t,i,r){const o=s-i;if(o>=0)return(e>>o)+(t-(r<<o))*(h>>o);const n=-o;return e-(r-(t<<n))*(h>>n)<<n}let Ie=class{constructor(e,s,t){this._rows=Math.ceil(s/t),this._columns=Math.ceil(e/t),this._cellSize=t,this.cells=new Array(this._rows);for(let i=0;i<this._rows;i++){this.cells[i]=new Array(this._columns);for(let r=0;r<this._columns;r++)this.cells[i][r]=[]}}getCell(e,s){const t=Math.min(Math.max(Math.floor(s/this._cellSize),0),this._rows-1),i=Math.min(Math.max(Math.floor(e/this._cellSize),0),this._columns-1);return this.cells[t]&&this.cells[t][i]||null}getCellSpan(e,s,t,i){return[Math.min(Math.max(Math.floor(e/this._cellSize),0),this.columns-1),Math.min(Math.max(Math.floor(s/this._cellSize),0),this.rows-1),Math.min(Math.max(Math.floor(t/this._cellSize),0),this.columns-1),Math.min(Math.max(Math.floor(i/this._cellSize),0),this.rows-1)]}get cellSize(){return this._cellSize}get columns(){return this._columns}get rows(){return this._rows}};function _t(h,e,s,t,i,r){const o=e[t++];for(let n=0;n<o;n++){const a=new yt(r);a.xTile=e[t++],a.yTile=e[t++],a.hash=e[t++],a.priority=e[t++];const l=e[t++];for(let d=0;d<l;d++){const c=e[t++],_=e[t++],f=e[t++],g=e[t++],m=!!e[t++],w=e[t++],b=s[t++],S=s[t++],v=e[t++],T=e[t++];a.colliders.push({xTile:c,yTile:_,dxPixels:f,dyPixels:g,hard:m,partIndex:w,width:v,height:T,minLod:b,maxLod:S})}const u=h[t++];for(let d=0;d<u;d++)a.textVertexRanges.push([h[t++],h[t++]]);const y=h[t++];for(let d=0;d<y;d++)a.iconVertexRanges.push([h[t++],h[t++]]);i.push(a)}return t}function pt(h,e,s){for(const[t,i]of h.symbols)gt(h,e,s,i,t)}function gt(h,e,s,t,i){const r=h.layerData.get(i);if(r.type===P.SYMBOL){for(const o of t){const n=o.unique;let a;if(o.selectedForRendering){const l=n.parts[0],u=l.startOpacity,y=l.targetOpacity;h.allSymbolsFadingOut=h.allSymbolsFadingOut&&y===0;const d=Math.floor(127*u)|y<<7;a=d<<24|d<<16|d<<8|d}else a=0;for(const[l,u]of o.iconVertexRanges)for(let y=l;y<l+u;y+=4)r.iconOpacity[y/4]=a;if(o.selectedForRendering){const l=n.parts[1],u=l.startOpacity,y=l.targetOpacity;h.allSymbolsFadingOut=h.allSymbolsFadingOut&&y===0;const d=Math.floor(127*u)|y<<7;a=d<<24|d<<16|d<<8|d}else a=0;for(const[l,u]of o.textVertexRanges)for(let y=l;y<l+u;y+=4)r.textOpacity[y/4]=a}r.lastOpacityUpdate=e,r.opacityChanged=!0}}class J{constructor(e,s){this.layerUIDs=[],this.isDestroyed=!1,this._data=e,this.memoryUsed=e.byteLength;let t=1;const i=new Uint32Array(e);this.layerUIDs=[];const r=i[t++];for(let o=0;o<r;o++)this.layerUIDs[o]=i[t++];this.bufferDataOffset=t,s&&(this.layer=s.getStyleLayerByUID(this.layerUIDs[0]))}get isPreparedForRendering(){return B(this._data)}get offset(){return this.bufferDataOffset}destroy(){this.isDestroyed||(this.doDestroy(),this.isDestroyed=!0)}prepareForRendering(e){B(this._data)||(this.doPrepareForRendering(e,this._data,this.bufferDataOffset),this._data=null)}}let mt=class extends J{constructor(e,s){super(e,s),this.type=P.LINE,this.lineIndexStart=0,this.lineIndexCount=0;const t=new Uint32Array(e);let i=this.bufferDataOffset;this.lineIndexStart=t[i++],this.lineIndexCount=t[i++];const r=t[i++];if(r>0){const o=new Map;for(let n=0;n<r;n++){const a=t[i++],l=t[i++],u=t[i++];o.set(a,[l,u])}this.patternMap=o}this.bufferDataOffset=i}hasData(){return this.lineIndexCount>0}triangleCount(){return this.lineIndexCount/3}doDestroy(){p(this.lineVertexArrayObject)&&this.lineVertexArrayObject.dispose(),p(this.lineVertexBuffer)&&this.lineVertexBuffer.dispose(),p(this.lineIndexBuffer)&&this.lineIndexBuffer.dispose(),this.lineVertexArrayObject=null,this.lineVertexBuffer=null,this.lineIndexBuffer=null,this.memoryUsed=0}doPrepareForRendering(e,s,t){const i=new Uint32Array(s),r=new Int32Array(i.buffer),o=i[t++];this.lineVertexBuffer=C.createVertex(e,I.STATIC_DRAW,new Int32Array(r.buffer,4*t,o)),t+=o;const n=i[t++];this.lineIndexBuffer=C.createIndex(e,I.STATIC_DRAW,new Uint32Array(i.buffer,4*t,n)),t+=n;const a=this.layer.lineMaterial;this.lineVertexArrayObject=new E(e,a.getAttributeLocations(),a.getLayoutInfo(),{geometry:this.lineVertexBuffer},this.lineIndexBuffer)}},bt=class extends J{constructor(e,s){super(e,s),this.type=P.FILL,this.fillIndexStart=0,this.fillIndexCount=0,this.outlineIndexStart=0,this.outlineIndexCount=0;const t=new Uint32Array(e);let i=this.bufferDataOffset;this.fillIndexStart=t[i++],this.fillIndexCount=t[i++],this.outlineIndexStart=t[i++],this.outlineIndexCount=t[i++];const r=t[i++];if(r>0){const o=new Map;for(let n=0;n<r;n++){const a=t[i++],l=t[i++],u=t[i++];o.set(a,[l,u])}this.patternMap=o}this.bufferDataOffset=i}hasData(){return this.fillIndexCount>0||this.outlineIndexCount>0}triangleCount(){return(this.fillIndexCount+this.outlineIndexCount)/3}doDestroy(){p(this.fillVertexArrayObject)&&this.fillVertexArrayObject.dispose(),p(this.fillVertexBuffer)&&this.fillVertexBuffer.dispose(),p(this.fillIndexBuffer)&&this.fillIndexBuffer.dispose(),this.fillVertexArrayObject=null,this.fillVertexBuffer=null,this.fillIndexBuffer=null,p(this.outlineVertexArrayObject)&&this.outlineVertexArrayObject.dispose(),p(this.outlineVertexBuffer)&&this.outlineVertexBuffer.dispose(),p(this.outlineIndexBuffer)&&this.outlineIndexBuffer.dispose(),this.outlineVertexArrayObject=null,this.outlineVertexBuffer=null,this.outlineIndexBuffer=null,this.memoryUsed=0}doPrepareForRendering(e,s,t){const i=new Uint32Array(s),r=new Int32Array(i.buffer),o=i[t++];this.fillVertexBuffer=C.createVertex(e,I.STATIC_DRAW,new Int32Array(r.buffer,4*t,o)),t+=o;const n=i[t++];this.fillIndexBuffer=C.createIndex(e,I.STATIC_DRAW,new Uint32Array(i.buffer,4*t,n)),t+=n;const a=i[t++];this.outlineVertexBuffer=C.createVertex(e,I.STATIC_DRAW,new Int32Array(r.buffer,4*t,a)),t+=a;const l=i[t++];this.outlineIndexBuffer=C.createIndex(e,I.STATIC_DRAW,new Uint32Array(i.buffer,4*t,l)),t+=l;const u=this.layer,y=u.fillMaterial,d=u.outlineMaterial;this.fillVertexArrayObject=new E(e,y.getAttributeLocations(),y.getLayoutInfo(),{geometry:this.fillVertexBuffer},this.fillIndexBuffer),this.outlineVertexArrayObject=new E(e,d.getAttributeLocations(),d.getLayoutInfo(),{geometry:this.outlineVertexBuffer},this.outlineIndexBuffer)}};class wt extends J{constructor(e,s,t){super(e,s),this.type=P.SYMBOL,this.iconPerPageElementsMap=new Map,this.glyphPerPageElementsMap=new Map,this.symbolInstances=[],this.isIconSDF=!1,this.opacityChanged=!1,this.lastOpacityUpdate=0,this.symbols=[];const i=new Uint32Array(e),r=new Int32Array(e),o=new Float32Array(e);let n=this.bufferDataOffset;this.isIconSDF=!!i[n++];const a=i[n++];for(let d=0;d<a;d++){const c=i[n++],_=i[n++],f=i[n++];this.iconPerPageElementsMap.set(c,[_,f])}const l=i[n++];for(let d=0;d<l;d++){const c=i[n++],_=i[n++],f=i[n++];this.glyphPerPageElementsMap.set(c,[_,f])}const u=i[n++],y=i[n++];this.iconOpacity=new Int32Array(u),this.textOpacity=new Int32Array(y),n=_t(i,r,o,n,this.symbols,t),this.bufferDataOffset=n}hasData(){return this.iconPerPageElementsMap.size>0||this.glyphPerPageElementsMap.size>0}triangleCount(){let e=0;for(const[s,t]of this.iconPerPageElementsMap)e+=t[1];for(const[s,t]of this.glyphPerPageElementsMap)e+=t[1];return e/3}doDestroy(){p(this.iconVertexArrayObject)&&this.iconVertexArrayObject.dispose(),p(this.iconVertexBuffer)&&this.iconVertexBuffer.dispose(),p(this.iconOpacityBuffer)&&this.iconOpacityBuffer.dispose(),p(this.iconIndexBuffer)&&this.iconIndexBuffer.dispose(),this.iconVertexArrayObject=null,this.iconVertexBuffer=null,this.iconOpacityBuffer=null,this.iconIndexBuffer=null,p(this.textVertexArrayObject)&&this.textVertexArrayObject.dispose(),p(this.textVertexBuffer)&&this.textVertexBuffer.dispose(),p(this.textOpacityBuffer)&&this.textOpacityBuffer.dispose(),p(this.textIndexBuffer)&&this.textIndexBuffer.dispose(),this.textVertexArrayObject=null,this.textVertexBuffer=null,this.textOpacityBuffer=null,this.textIndexBuffer=null,this.memoryUsed=0}updateOpacityInfo(){if(!this.opacityChanged)return;this.opacityChanged=!1;const e=U(this.iconOpacity),s=U(this.iconOpacityBuffer);e.length>0&&e.byteLength===s.size&&s.setSubData(e,0,0,e.length);const t=U(this.textOpacity),i=U(this.textOpacityBuffer);t.length>0&&t.byteLength===i.size&&i.setSubData(t,0,0,t.length)}doPrepareForRendering(e,s,t){const i=new Uint32Array(s),r=new Int32Array(i.buffer),o=i[t++];this.iconVertexBuffer=C.createVertex(e,I.STATIC_DRAW,new Int32Array(r.buffer,4*t,o)),t+=o;const n=i[t++];this.iconIndexBuffer=C.createIndex(e,I.STATIC_DRAW,new Uint32Array(i.buffer,4*t,n)),t+=n;const a=i[t++];this.textVertexBuffer=C.createVertex(e,I.STATIC_DRAW,new Int32Array(r.buffer,4*t,a)),t+=a;const l=i[t++];this.textIndexBuffer=C.createIndex(e,I.STATIC_DRAW,new Uint32Array(i.buffer,4*t,l)),t+=l,this.iconOpacityBuffer=C.createVertex(e,I.STATIC_DRAW,U(this.iconOpacity).buffer),this.textOpacityBuffer=C.createVertex(e,I.STATIC_DRAW,U(this.textOpacity).buffer);const u=this.layer,y=u.iconMaterial,d=u.textMaterial;this.iconVertexArrayObject=new E(e,y.getAttributeLocations(),y.getLayoutInfo(),{geometry:this.iconVertexBuffer,opacity:this.iconOpacityBuffer},this.iconIndexBuffer),this.textVertexArrayObject=new E(e,d.getAttributeLocations(),d.getLayoutInfo(),{geometry:this.textVertexBuffer,opacity:this.textOpacityBuffer},this.textIndexBuffer)}}class xt extends J{constructor(e,s){super(e,s),this.type=P.CIRCLE,this.circleIndexStart=0,this.circleIndexCount=0;const t=new Uint32Array(e);let i=this.bufferDataOffset;this.circleIndexStart=t[i++],this.circleIndexCount=t[i++],this.bufferDataOffset=i}hasData(){return this.circleIndexCount>0}triangleCount(){return this.circleIndexCount/3}doDestroy(){p(this.circleVertexArrayObject)&&this.circleVertexArrayObject.dispose(),p(this.circleVertexBuffer)&&this.circleVertexBuffer.dispose(),p(this.circleIndexBuffer)&&this.circleIndexBuffer.dispose(),this.circleVertexArrayObject=null,this.circleVertexBuffer=null,this.circleIndexBuffer=null,this.memoryUsed=0}doPrepareForRendering(e,s,t){const i=new Uint32Array(s),r=new Int32Array(i.buffer),o=i[t++];this.circleVertexBuffer=C.createVertex(e,I.STATIC_DRAW,new Int32Array(r.buffer,4*t,o)),t+=o;const n=i[t++];this.circleIndexBuffer=C.createIndex(e,I.STATIC_DRAW,new Uint32Array(i.buffer,4*t,n)),t+=n;const a=this.layer.circleMaterial;this.circleVertexArrayObject=new E(e,a.getAttributeLocations(),a.getLayoutInfo(),{geometry:this.circleVertexBuffer},this.circleIndexBuffer)}}let St=class Ce extends Se{constructor(e,s,t,i,r,o,n,a=null){super(e,s,t,i,r,o,4096,4096),this._memCache=a,this.type="vector-tile",this._referenced=0,this._hasSymbolBuckets=!1,this._memoryUsedByLayerData=0,this.layerData=new Map,this.layerCount=0,this.status="loading",this.allSymbolsFadingOut=!1,this.lastOpacityUpdate=0,this.symbols=new Map,this.isCoverage=!1,this.neededForCoverage=!1,this.decluttered=!1,this.invalidating=!1,this.parentTile=null,this.childrenTiles=new Set,this._processed=!1,this._referenced=1,this.styleRepository=n,this.id=e.id}get hasSymbolBuckets(){return this._hasSymbolBuckets}get isFading(){return this._hasSymbolBuckets&&performance.now()-this.lastOpacityUpdate<W}get isHoldingForFade(){return this._hasSymbolBuckets&&(!this.allSymbolsFadingOut||performance.now()-this.lastOpacityUpdate<W)}get wasRequested(){return this.status==="errored"||this.status==="loaded"||this.status==="reloading"}setData(e){this.changeDataImpl(e),this.requestRender(),this.ready(),this.invalidating=!1,this._processed=!0}deleteLayerData(e){let s=!1;for(const t of e)if(this.layerData.has(t)){const i=this.layerData.get(t);this._memoryUsedByLayerData-=i.memoryUsed,i.type===P.SYMBOL&&this.symbols.has(t)&&(this.symbols.delete(t),s=!0),i.destroy(),this.layerData.delete(t),this.layerCount--}p(this._memCache)&&this._memCache.updateSize(this.key.id,this,this._memoryUsedByLayerData),s&&this.emit("symbols-changed"),this.requestRender()}processed(){return this._processed}hasData(){return this.layerCount>0}dispose(){this.status!=="unloaded"&&(Tt.delete(this),Ce._destroyRenderBuckets(this.layerData),this.layerData=null,this.layerCount=0,this._memoryUsedByLayerData=0,this.destroy(),this.status="unloaded")}release(){return--this._referenced==0&&(this.dispose(),this.stage=null,!0)}retain(){++this._referenced}get referenced(){return this._referenced}get memoryUsage(){return(this._memoryUsedByLayerData+256)/(this._referenced||1)}changeDataImpl(e){let s=!1;if(e){const{bucketsWithData:t,emptyBuckets:i}=e,r=this._createRenderBuckets(t);if(i&&i.byteLength>0){const o=new Uint32Array(i);for(const n of o)this._deleteLayerData(n)}for(const[o,n]of r)this._deleteLayerData(o),n.type===P.SYMBOL&&(this.symbols.set(o,n.symbols),s=!0),this._memoryUsedByLayerData+=n.memoryUsed,this.layerData.set(o,n),this.layerCount++;p(this._memCache)&&this._memCache.updateSize(this.key.id,this,this._memoryUsedByLayerData)}this._hasSymbolBuckets=!1;for(const[t,i]of this.layerData)i.type===P.SYMBOL&&(this._hasSymbolBuckets=!0);s&&this.emit("symbols-changed")}attachWithContext(e){this.stage={context:e,trashDisplayObject(s){s.processDetach()},untrashDisplayObject:()=>!1}}setTransform(e){super.setTransform(e);const s=this.resolution/(e.resolution*e.pixelRatio),t=this.width/this.rangeX*s,i=this.height/this.rangeY*s,r=[0,0];e.toScreen(r,[this.x,this.y]);const o=this.transforms.tileUnitsToPixels;_e(o),pe(o,o,r),Oe(o,o,Math.PI*e.rotation/180),ge(o,o,[t,i,1])}_createTransforms(){return{dvs:H(),tileMat3:H(),tileUnitsToPixels:H()}}static _destroyRenderBuckets(e){if(!e)return;const s=new Set;e.forEach(t=>{s.has(t)||(t.destroy(),s.add(t))}),e.clear()}_createRenderBuckets(e){const s=new Map,t=new Map;for(const i of e){const r=this._deserializeBucket(i,t);for(const o of r.layerUIDs)s.set(o,r)}return s}_deserializeBucket(e,s){let t=s.get(e);if(t)return t;switch(new Uint32Array(e)[0]){case P.FILL:t=new bt(e,this.styleRepository);break;case P.LINE:t=new mt(e,this.styleRepository);break;case P.SYMBOL:t=new wt(e,this.styleRepository,this);break;case P.CIRCLE:t=new xt(e,this.styleRepository)}return s.set(e,t),t}_deleteLayerData(e){if(!this.layerData.has(e))return;const s=this.layerData.get(e);this._memoryUsedByLayerData-=s.memoryUsed,s.destroy(),this.layerData.delete(e),this.layerCount--}};const Tt=new Map;function It(h,e,s,t,i,r){const{iconRotationAlignment:o,textRotationAlignment:n,iconTranslate:a,iconTranslateAnchor:l,textTranslate:u,textTranslateAnchor:y}=t;let d=0;for(const c of h.colliders){const[_,f]=c.partIndex===0?a:u,g=c.partIndex===0?l:y,m=c.minLod<=r&&r<=c.maxLod;d+=m?0:1,c.enabled=m,c.xScreen=c.xTile*i[0]+c.yTile*i[3]+i[6],c.yScreen=c.xTile*i[1]+c.yTile*i[4]+i[7],g===Xe.MAP?(c.xScreen+=s*_-e*f,c.yScreen+=e*_+s*f):(c.xScreen+=_,c.yScreen+=f),k.VIEWPORT===(c.partIndex===0?o:n)?(c.dxScreen=c.dxPixels,c.dyScreen=c.dyPixels):(c.dxScreen=s*(c.dxPixels+c.width/2)-e*(c.dyPixels+c.height/2)-c.width/2,c.dyScreen=e*(c.dxPixels+c.width/2)+s*(c.dyPixels+c.height/2)-c.height/2)}h.colliders.length>0&&d===h.colliders.length&&(h.unique.show=!1)}let Ct=class{constructor(e,s,t,i,r,o){this._symbols=e,this._styleRepository=i,this._zoom=r,this._currentLayerCursor=0,this._currentSymbolCursor=0,this._styleProps=new Map,this._allNeededMatrices=new Map,this._gridIndex=new Ie(s,t,Ye),this._si=Math.sin(Math.PI*o/180),this._co=Math.cos(Math.PI*o/180);for(const n of e)for(const a of n.symbols)this._allNeededMatrices.has(a.tile)||this._allNeededMatrices.set(a.tile,Ue(a.tile.transforms.tileUnitsToPixels))}work(e){const s=this._gridIndex;function t(r){const o=r.xScreen+r.dxScreen,n=r.yScreen+r.dyScreen,a=o+r.width,l=n+r.height,[u,y,d,c]=s.getCellSpan(o,n,a,l);for(let _=y;_<=c;_++)for(let f=u;f<=d;f++){const g=s.cells[_][f];for(const m of g){const w=m.xScreen+m.dxScreen,b=m.yScreen+m.dyScreen,S=w+m.width,v=b+m.height;if(!(a<w||o>S||l<b||n>v))return!0}}return!1}const i=performance.now();for(;this._currentLayerCursor<this._symbols.length;this._currentLayerCursor++,this._currentSymbolCursor=0){const r=this._symbols[this._currentLayerCursor],o=this._getProperties(r.styleLayerUID);for(;this._currentSymbolCursor<r.symbols.length;this._currentSymbolCursor++){if(this._currentSymbolCursor%100==99&&performance.now()-i>e)return!1;const n=r.symbols[this._currentSymbolCursor];if(!n.unique.show)continue;It(n,this._si,this._co,o,this._allNeededMatrices.get(n.tile),this._zoom);const a=n.unique;if(!a.show)continue;const{iconAllowOverlap:l,iconIgnorePlacement:u,textAllowOverlap:y,textIgnorePlacement:d}=o;for(const c of n.colliders){if(!c.enabled)continue;const _=a.parts[c.partIndex];_.show&&!(c.partIndex?y:l)&&t(c)&&(c.hard?a.show=!1:_.show=!1)}if(a.show)for(const c of n.colliders){if(!c.enabled||(c.partIndex?d:u)||!a.parts[c.partIndex].show)continue;const _=c.xScreen+c.dxScreen,f=c.yScreen+c.dyScreen,g=_+c.width,m=f+c.height,[w,b,S,v]=this._gridIndex.getCellSpan(_,f,g,m);for(let T=b;T<=v;T++)for(let A=w;A<=S;A++)this._gridIndex.cells[T][A].push(c)}}}return!0}_getProperties(e){const s=this._styleProps.get(e);if(s)return s;const t=this._zoom,i=this._styleRepository.getStyleLayerByUID(e),r=i.getLayoutValue("symbol-placement",t)!==Ze.POINT;let o=i.getLayoutValue("icon-rotation-alignment",t);o===k.AUTO&&(o=r?k.MAP:k.VIEWPORT);let n=i.getLayoutValue("text-rotation-alignment",t);n===k.AUTO&&(n=r?k.MAP:k.VIEWPORT);const a=i.getPaintValue("icon-translate",t),l=i.getPaintValue("icon-translate-anchor",t),u=i.getPaintValue("text-translate",t),y=i.getPaintValue("text-translate-anchor",t),d={iconAllowOverlap:i.getLayoutValue("icon-allow-overlap",t),iconIgnorePlacement:i.getLayoutValue("icon-ignore-placement",t),textAllowOverlap:i.getLayoutValue("text-allow-overlap",t),textIgnorePlacement:i.getLayoutValue("text-ignore-placement",t),iconRotationAlignment:o,textRotationAlignment:n,iconTranslateAnchor:l,iconTranslate:a,textTranslateAnchor:y,textTranslate:u};return this._styleProps.set(e,d),d}};function vt(h,e){if(h.priority-e.priority)return h.priority-e.priority;const s=h.tile.key,t=e.tile.key;return s.world-t.world?s.world-t.world:s.level-t.level?s.level-t.level:s.row-t.row?s.row-t.row:s.col-t.col?s.col-t.col:h.xTile-e.xTile?h.xTile-e.xTile:h.yTile-e.yTile}let Dt=class{get running(){return this._running}constructor(e,s,t,i,r,o){this._visibleTiles=e,this._symbolRepository=s,this._createCollisionJob=t,this._assignTileSymbolsOpacity=i,this._symbolLayerSorter=r,this._isLayerVisible=o,this._selectionJob=null,this._selectionJobCompleted=!1,this._collisionJob=null,this._collisionJobCompleted=!1,this._opacityJob=null,this._opacityJobCompleted=!1,this._running=!0}setScreenSize(e,s){this._screenWidth===e&&this._screenHeight===s||this.restart(),this._screenWidth=e,this._screenHeight=s}restart(){this._selectionJob=null,this._selectionJobCompleted=!1,this._collisionJob=null,this._collisionJobCompleted=!1,this._opacityJob=null,this._opacityJobCompleted=!1,this._running=!0}continue(e){if(this._selectionJob||(this._selectionJob=this._createSelectionJob()),!this._selectionJobCompleted){const s=performance.now();if(!this._selectionJob.work(e)||(this._selectionJobCompleted=!0,(e=Math.max(0,e-(performance.now()-s)))===0))return!1}if(this._collisionJob||(this._collisionJob=this._createCollisionJob(this._selectionJob.sortedSymbols,this._screenWidth,this._screenHeight)),!this._collisionJobCompleted){const s=performance.now();if(!this._collisionJob.work(e)||(this._collisionJobCompleted=!0,(e=Math.max(0,e-(performance.now()-s)))===0))return!1}if(this._opacityJob||(this._opacityJob=this._createOpacityJob()),!this._opacityJobCompleted){const s=performance.now();if(!this._opacityJob.work(e)||(this._opacityJobCompleted=!0,(e=Math.max(0,e-(performance.now()-s)))===0))return!1}return this._running=!1,!0}_createSelectionJob(){const e=this._symbolRepository.uniqueSymbols;for(let a=0;a<e.length;a++){const l=e[a];for(let u=0;u<l.uniqueSymbols.length;u++){const y=l.uniqueSymbols[u];for(const d of y.tileSymbols)d.selectedForRendering=!1}}const s=[];let t=0,i=0;const r=this._isLayerVisible;function o(a){let l;const u=performance.now();for(;i<e.length;i++,t=0){const y=e[i],d=y.styleLayerUID;if(!r(d)){s[i]||(s[i]={styleLayerUID:d,symbols:[]});continue}s[i]=s[i]||{styleLayerUID:d,symbols:[]};const c=s[i];for(;t<y.uniqueSymbols.length;t++){if(l=y.uniqueSymbols[t],t%100==99&&performance.now()-u>a)return!1;let _=null,f=!1,g=!1;for(const m of l.tileSymbols)if(!g||!f){const w=m.tile;(!_||w.isCoverage||w.neededForCoverage&&!f)&&(_=m,(w.neededForCoverage||w.isCoverage)&&(g=!0),w.isCoverage&&(f=!0))}if(_.selectedForRendering=!0,g){c.symbols.push(_),l.show=!0;for(const m of l.parts)m.show=!0}else l.show=!1}}for(const y of s)y.symbols.sort(vt);return!0}const n=this._symbolLayerSorter;return{work:o,get sortedSymbols(){return s.sort(n)}}}_createOpacityJob(){const e=this._assignTileSymbolsOpacity,s=this._visibleTiles;let t=0;function i(r,o){const n=r.symbols;for(const[a,l]of n)Rt(l,o);e(r,o);for(const a of r.childrenTiles)i(a,o)}return{work(r){const o=performance.now();for(;t<s.length;t++){if(performance.now()-o>r)return!1;const n=s[t];p(n.parentTile)||i(n,performance.now())}return!0}}}};function Rt(h,e){for(const s of h){const t=s.unique;for(const i of t.parts){const r=i.targetOpacity>.5?1:-1;i.startOpacity+=r*((e-i.startTime)/W),i.startOpacity=Math.min(Math.max(i.startOpacity,0),1),i.startTime=e,i.targetOpacity=t.show&&i.show?1:0}}}const At=32,Pt=8,Mt=64;class Lt{constructor(e,s,t){this.tileCoordRange=e,this._visibleTiles=s,this._createUnique=t,this._tiles=new Map,this._uniqueSymbolsReferences=new Map}get uniqueSymbols(){return B(this._uniqueSymbolLayerArray)&&(this._uniqueSymbolLayerArray=this._createUniqueSymbolLayerArray()),this._uniqueSymbolLayerArray}add(e,s){this._uniqueSymbolLayerArray=null;let t=this._tiles.get(e.id);t||(t={symbols:new Map},this._tiles.set(e.id,t));const i=new Map;if(s)for(const n of s)t.symbols.has(n)&&(i.set(n,t.symbols.get(n)),t.symbols.delete(n));else for(const[n,a]of e.layerData)t.symbols.has(n)&&(i.set(n,t.symbols.get(n)),t.symbols.delete(n));this._removeSymbols(i);const r=e.symbols,o=new Map;for(const[n,a]of r){let l=a.length;if(l>=At){let u=this.tileCoordRange;do u/=2,l/=4;while(l>Pt&&u>Mt);const y=new Ie(this.tileCoordRange,this.tileCoordRange,u);o.set(n,{flat:a,index:y}),t.symbols.set(n,{flat:a,index:y});for(const d of a)y.getCell(d.xTile,d.yTile).push(d)}else o.set(n,{flat:a}),t.symbols.set(n,{flat:a})}this._addSymbols(e.key,r)}deleteStyleLayers(e){this._uniqueSymbolLayerArray=null;for(const[s,t]of this._tiles){const i=new Map;for(const r of e)t.symbols.has(r)&&(i.set(r,t.symbols.get(r)),t.symbols.delete(r));this._removeSymbols(i),t.symbols.size===0&&this._tiles.delete(s)}}removeTile(e){this._uniqueSymbolLayerArray=null;const s=this._tiles.get(e.id);if(!s)return;const t=new Map;for(const[i,r]of e.symbols)s.symbols.has(i)&&(t.set(i,s.symbols.get(i)),s.symbols.delete(i));this._removeSymbols(t),s.symbols.size===0&&this._tiles.delete(e.id)}_removeSymbols(e){for(const[s,{flat:t}]of e)for(const i of t){const r=i.unique,o=r.tileSymbols,n=o.length-1;for(let a=0;a<n;a++)if(o[a]===i){o[a]=o[n];break}if(o.length=n,n===0){const a=this._uniqueSymbolsReferences.get(s);a.delete(r),a.size===0&&this._uniqueSymbolsReferences.delete(s)}i.unique=null}}_addSymbols(e,s){if(s.size===0)return;const t=this._visibleTiles;for(const i of t)i.parentTile||i.key.world!==e.world||i.key.level===e.level&&!i.key.equals(e)||this._matchSymbols(i,e,s);for(const[i,r]of s)for(const o of r)if(B(o.unique)){const n=this._createUnique();o.unique=n,n.tileSymbols.push(o);let a=this._uniqueSymbolsReferences.get(i);a||(a=new Set,this._uniqueSymbolsReferences.set(i,a)),a.add(n)}}_matchSymbols(e,s,t){if(e.key.level>s.level){const r=e.key.level-s.level;if(e.key.row>>r!==s.row||e.key.col>>r!==s.col)return}if(s.level>e.key.level){const r=s.level-e.key.level;if(s.row>>r!==e.key.row||s.col>>r!==e.key.col)return}if(s.equals(e.key)){for(const r of e.childrenTiles)this._matchSymbols(r,s,t);return}const i=new Map;for(const[r,o]of t){const n=[];for(const y of o){const d=ue(this.tileCoordRange,y.xTile,s.level,s.col,e.key.level,e.key.col),c=ue(this.tileCoordRange,y.yTile,s.level,s.row,e.key.level,e.key.row);d>=0&&d<this.tileCoordRange&&c>=0&&c<this.tileCoordRange&&n.push({symbol:y,xTransformed:d,yTransformed:c})}const a=[],l=e.key.level<s.level?1:1<<e.key.level-s.level,u=this._tiles.get(e.id).symbols.get(r);if(u){const y=u.flat;for(const d of n){let c,_=!1;const f=d.xTransformed,g=d.yTransformed;c=p(u.index)?u.index.getCell(f,g):y;const m=d.symbol,w=m.hash;for(const b of c)if(w===b.hash&&Math.abs(f-b.xTile)<=l&&Math.abs(g-b.yTile)<=l){const S=b.unique;m.unique=S,S.tileSymbols.push(m),_=!0;break}_||a.push(m)}}a.length>0&&i.set(r,a)}for(const r of e.childrenTiles)this._matchSymbols(r,s,i)}_createUniqueSymbolLayerArray(){const e=this._uniqueSymbolsReferences,s=new Array(e.size);let t,i=0;for(const[r,o]of e){const n=new Array(o.size);t=0;for(const a of o)n[t++]=a;s[i]={styleLayerUID:r,uniqueSymbols:n},i++}return s}}const Bt=.5,de=1e-6;class Ot extends qe{constructor(e,s){super(),this.styleRepository=e,this._tileToHandle=new Map,this._viewState={scale:0,rotation:0,center:[0,0],size:[0,0]},this._declutterViewState={scale:0,rotation:0,center:[0,0],size:[0,0]},this._completed=!1,this._symbolRepository=new Lt(4096,s,()=>new ft),this._symbolDeclutterer=new Dt(s,this._symbolRepository,(t,i,r)=>new Ct(t,i,r,this.styleRepository,this._zoom,this._viewState.rotation),(t,i)=>{t.allSymbolsFadingOut=!0,t.lastOpacityUpdate=i,pt(t,i,!0),t.decluttered=!0,t.requestRender()},(t,i)=>this.styleRepository.getStyleLayerByUID(t.styleLayerUID).z-this.styleRepository.getStyleLayerByUID(i.styleLayerUID).z,t=>{const i=this.styleRepository.getStyleLayerByUID(t);if(this._zoom+de<i.minzoom||this._zoom-de>=i.maxzoom)return!1;const r=i.getLayoutProperty("visibility");return!r||r.getValue()!==ee.NONE})}addTile(e){e.decluttered=!1,this._tileToHandle.set(e,e.on("symbols-changed",()=>{this._symbolRepository.add(e),this.restartDeclutter()})),this._symbolRepository.add(e),this.restartDeclutter()}removeTile(e){const s=this._tileToHandle.get(e);s&&(this._symbolRepository.removeTile(e),this.restartDeclutter(),s.remove(),this._tileToHandle.delete(e))}update(e,s){return this._zoom=e,this._viewState={scale:s.scale,rotation:s.rotation,center:[s.center[0],s.center[1]],size:[s.size[0],s.size[1]]},this._continueDeclutter(),this._completed}restartDeclutter(){this._completed=!1,this._symbolDeclutterer.restart(),this._notifyUnstable()}clear(){this._completed=!1,this._symbolRepository=null,this._symbolDeclutterer.restart(),this._tileToHandle.forEach(e=>e.remove()),this._tileToHandle.clear()}get stale(){return this._zoom!==this._declutterZoom||this._viewState.size[0]!==this._declutterViewState.size[0]||this._viewState.size[1]!==this._declutterViewState.size[1]||this._viewState.scale!==this._declutterViewState.scale||this._viewState.rotation!==this._declutterViewState.rotation}deleteStyleLayers(e){this._symbolRepository.deleteStyleLayers(e)}_continueDeclutter(){this._completed&&!this.stale||(this._symbolDeclutterer.running||(this._declutterZoom=this._zoom,this._declutterViewState.center[0]=this._viewState.center[0],this._declutterViewState.center[1]=this._viewState.center[1],this._declutterViewState.rotation=this._viewState.rotation,this._declutterViewState.scale=this._viewState.scale,this._declutterViewState.size[0]=this._viewState.size[0],this._declutterViewState.size[1]=this._viewState.size[1],this._symbolDeclutterer.restart()),this._symbolDeclutterer.setScreenSize(this._viewState.size[0],this._viewState.size[1]),this._completed=this._symbolDeclutterer.continue(Ke),this._completed&&this._scheduleNotifyStable())}_scheduleNotifyStable(){p(this._stableNotificationHandle)&&clearTimeout(this._stableNotificationHandle),this._stableNotificationHandle=setTimeout(()=>{this._stableNotificationHandle=null,this.emit("fade-complete")},(1+Bt)*W)}_notifyUnstable(){p(this._stableNotificationHandle)&&(clearTimeout(this._stableNotificationHandle),this._stableNotificationHandle=null),this.emit("fade-start")}}let Ut=class extends Se{_createTransforms(){return{dvs:H(),tileMat3:H()}}};const j=1e-6;function ye(h,e){if(h){const s=h.getLayoutProperty("visibility");if(!s||s.getValue()!==ee.NONE&&(h.minzoom===void 0||h.minzoom<e+j)&&(h.maxzoom===void 0||h.maxzoom>=e-j))return!0}return!1}class kt extends et{constructor(e){super(e),this._backgroundTiles=[],this._pointToCallbacks=new Map}destroy(){var e,s;this.removeAllChildren(),(e=this._spriteMosaic)==null||e.dispose(),this._spriteMosaic=null,(s=this._glyphMosaic)==null||s.dispose(),this._glyphMosaic=null,p(this._symbolFader)&&(this._symbolFader.clear(),this._symbolFader=null),this._styleRepository=null,this._backgroundTiles=[],this._pointToCallbacks.clear()}setStyleResources(e,s,t){if(this._spriteMosaic=e,this._glyphMosaic=s,this._styleRepository=t,B(this._symbolFader)){const i=new Ot(this._styleRepository,this.children);i.on("fade-start",()=>{this.emit("fade-start"),this.requestRender()}),i.on("fade-complete",()=>{this.emit("fade-complete"),this.requestRender()}),this._symbolFader=i}U(this._symbolFader).styleRepository=t}setSpriteMosaic(e){var s;(s=this._spriteMosaic)==null||s.dispose(),this._spriteMosaic=e}deleteStyleLayers(e){p(this._symbolFader)&&this._symbolFader.deleteStyleLayers(e)}async hitTest(e){const s=Re();return this._pointToCallbacks.set(e,s),this.requestRender(),s.promise}enterTileInvalidation(){for(const e of this.children)e.invalidating=!0}createRenderParams(e){return{...super.createRenderParams(e),renderPass:null,styleLayer:null,styleLayerUID:-1,glyphMosaic:this._glyphMosaic,spriteMosaic:this._spriteMosaic,hasClipping:!!this._clippingInfos}}doRender(e){!this.visible||e.drawPhase!==O.MAP&&e.drawPhase!==O.DEBUG||this._spriteMosaic===void 0||super.doRender(e)}addChild(e){return super.addChild(e),p(this._symbolFader)?this._symbolFader.addTile(e):e.decluttered=!0,this.requestRender(),e}removeChild(e){return p(this._symbolFader)&&this._symbolFader.removeTile(e),this.requestRender(),super.removeChild(e)}renderChildren(e){const{drawPhase:s}=e;if(s!==O.DEBUG){if(this._doRender(e),this._pointToCallbacks.size>0){e.drawPhase=O.HITTEST;const t=e.painter.effects.hittestVTL;t.bind(e),this._doRender(e),t.draw(e,this._pointToCallbacks),t.unbind(e),e.drawPhase=s}}else super.renderChildren(e)}removeAllChildren(){for(let e=0;e<this.children.length;e++){const s=this.children[e];p(this._symbolFader)&&this._symbolFader.removeTile(s),s.dispose()}super.removeAllChildren()}getStencilTarget(){return this.children.filter(e=>e.neededForCoverage&&e.hasData())}restartDeclutter(){p(this._symbolFader)&&this._symbolFader.restartDeclutter()}_doRender(e){const{context:s}=e,t=this._styleRepository;if(!t)return;const i=t.layers;let r=!0;e.drawPhase===O.HITTEST&&(r=!1),t.backgroundBucketIds.length>0&&(e.renderPass="background",this._renderBackgroundLayers(e,t.backgroundBucketIds)),super.renderChildren(e),e.drawPhase===O.MAP&&this._fade(e.displayLevel,e.state);const o=this.children.filter(n=>n.visible&&n.hasData());if(!o||o.length===0)return s.bindVAO(),s.setStencilTestEnabled(!0),void s.setBlendingEnabled(!0);for(const n of o)n.triangleCount=0;s.setStencilWriteMask(0),s.setColorMask(!0,!0,!0,!0),s.setStencilOp(z.KEEP,z.KEEP,z.REPLACE),s.setStencilTestEnabled(!0),s.setBlendingEnabled(!1),s.setDepthTestEnabled(!0),s.setDepthWriteEnabled(!0),s.setDepthFunction(ne.LEQUAL),s.setClearDepth(1),s.clear(s.gl.DEPTH_BUFFER_BIT),e.renderPass="opaque";for(let n=i.length-1;n>=0;n--)this._renderStyleLayer(i[n],e,o);s.setDepthWriteEnabled(!1),s.setBlendingEnabled(r),s.setBlendFunctionSeparate($.ONE,$.ONE_MINUS_SRC_ALPHA,$.ONE,$.ONE_MINUS_SRC_ALPHA),e.renderPass="translucent";for(let n=0;n<i.length;n++)this._renderStyleLayer(i[n],e,o);s.bindVAO(),s.setStencilTestEnabled(!0),s.setBlendingEnabled(!0)}_fade(e,s){p(this._symbolFader)&&(this._symbolFader.update(e,s)||this.requestRender())}_renderStyleLayer(e,s,t){const{painter:i,renderPass:r}=s;if(e===void 0)return;const o=e.getLayoutProperty("visibility");if(o&&o.getValue()===ee.NONE)return;let n;switch(e.type){case R.BACKGROUND:return;case R.FILL:if(r!=="opaque"&&s.renderPass!=="translucent")return;n="vtlFill";break;case R.LINE:if(r!=="translucent")return;n="vtlLine";break;case R.CIRCLE:if(r!=="translucent")return;n="vtlCircle";break;case R.SYMBOL:if(r!=="translucent")return;n="vtlSymbol"}if(t=e.type===R.SYMBOL?t.filter(l=>l.decluttered):t.filter(l=>l.neededForCoverage),n!=="vtlSymbol"){const l=s.displayLevel;if(t.length===0||e.minzoom!==void 0&&e.minzoom>=l+j||e.maxzoom!==void 0&&e.maxzoom<l-j)return}const a=e.uid;s.styleLayerUID=a,s.styleLayer=e;for(const l of t)if(l.layerData.has(a)){i.renderObjects(s,t,n);break}}_renderBackgroundLayers(e,s){const{context:t,displayLevel:i,painter:r,state:o}=e,n=this._styleRepository;let a=!1;for(const b of s)if(n.getLayerById(b).type===R.BACKGROUND&&ye(n.getLayerById(b),i)){a=!0;break}if(!a)return;const l=this._tileInfoView.getTileCoverage(e.state,0,"smallest"),{spans:u,lodInfo:y}=l,{level:d}=y,c=X(),_=[];if(this._renderPasses){const b=this._renderPasses[0];p(this._clippingInfos)&&(b.brushes[0].prepareState(e),b.brushes[0].drawMany(e,this._clippingInfos))}const f=this._backgroundTiles;let g,m=0;for(const{row:b,colFrom:S,colTo:v}of u)for(let T=S;T<=v;T++){if(m<f.length)g=f[m],g.key.set(d,b,y.normalizeCol(T),y.getWorldForColumn(T)),this._tileInfoView.getTileBounds(c,g.key,!1),g.x=c[0],g.y=c[3],g.resolution=this._tileInfoView.getTileResolution(d);else{const A=new L(d,b,y.normalizeCol(T),y.getWorldForColumn(T)),x=this._tileInfoView.getTileBounds(X(),A),ve=this._tileInfoView.getTileResolution(d);g=new Ut(A,ve,x[0],x[3],512,512,4096,4096),f.push(g)}g.setTransform(o),_.push(g),m++}t.setStencilWriteMask(0),t.setColorMask(!0,!0,!0,!0),t.setStencilOp(z.KEEP,z.KEEP,z.REPLACE),t.setStencilFunction(ne.EQUAL,0,255);let w=!0;e.drawPhase===O.HITTEST&&(w=!1),t.setStencilTestEnabled(w);for(const b of s){const S=n.getLayerById(b);S.type===R.BACKGROUND&&ye(S,i)&&(e.styleLayerUID=S.uid,e.styleLayer=S,r.renderObjects(e,_,"vtlBackground"))}fe.pool.release(l)}}const Vt={geometry:[new rt("a_PositionAndFlags",3,we.SHORT,0,6)]},te=new Map;te.set("a_PositionAndFlags",0);const Et={vsPath:"debug/overlay",fsPath:"debug/overlay",attributes:te};class K extends it{constructor(e){super(),this._conf=e}static makeFlags(e,s){return e|s<<2}_createTransforms(){return{dvs:H()}}doRender(e){this._updateTransforms(e),this._ensureResources(e);const{context:s}=e;s.useProgram(this._program),this._program.setUniformMatrix3fv("u_dvsMat3",this.transforms.dvs),this._program.setUniform4fv("u_colors",this._conf.getColors(e)),this._program.setUniform1fv("u_opacities",this._conf.getOpacities(e));const{vertexData:t,indexData:i}=this._conf.getMesh(e);this._vertexBuffer.setData(t),this._indexBuffer.setData(i),s.bindVAO(this._vertexArray),s.setBlendingEnabled(!0),s.setBlendFunction($.ONE,$.ONE_MINUS_SRC_ALPHA),s.setDepthTestEnabled(!1),s.setStencilTestEnabled(!1),s.setColorMask(!0,!0,!0,!0),s.drawElements(Je.TRIANGLES,i.length,we.UNSIGNED_INT,0)}onDetach(){this._vertexArray=He(this._vertexArray)}_updateTransforms(e){_e(this.transforms.dvs),pe(this.transforms.dvs,this.transforms.dvs,[-1,1]),ge(this.transforms.dvs,this.transforms.dvs,[2/e.state.size[0],-2/e.state.size[1],1])}_ensureResources(e){const{context:s}=e;this._program||(this._program=e.painter.materialManager.getProgram(Et)),this._vertexBuffer||(this._vertexBuffer=C.createVertex(s,I.STREAM_DRAW)),this._indexBuffer||(this._indexBuffer=C.createIndex(s,I.STREAM_DRAW)),this._vertexArray||(this._vertexArray=new E(s,te,Vt,{geometry:this._vertexBuffer},this._indexBuffer))}}class zt extends ke{constructor(){super(...arguments),this._fullCacheLodInfos=null,this._levelByScale={}}getTileParentId(e){const s=L.pool.acquire(e),t=s.level===0?null:L.getId(s.level-1,s.row>>1,s.col>>1,s.world);return L.pool.release(s),t}getTileCoverage(e,s,t){const i=super.getTileCoverage(e,s,t);if(!i)return i;const r=1<<i.lodInfo.level;return i.spans=i.spans.filter(o=>o.row>=0&&o.row<r),i}scaleToLevel(e){if(this._fullCacheLodInfos||this._initializeFullCacheLODs(this._lodInfos),this._levelByScale[e])return this._levelByScale[e];{const s=this._fullCacheLodInfos;if(e>s[0].scale)return s[0].level;let t,i;for(let r=0;r<s.length-1;r++)if(i=s[r+1],e>i.scale)return t=s[r],t.level+(t.scale-e)/(t.scale-i.scale);return s[s.length-1].level}}_initializeFullCacheLODs(e){let s;if(e[0].level===0)s=e.map(t=>({level:t.level,resolution:t.resolution,scale:t.scale}));else{const t=this.tileInfo.size[0],i=this.tileInfo.spatialReference;s=Ve.create({size:t,spatialReference:i}).lods.map(r=>({level:r.level,resolution:r.resolution,scale:r.scale}))}for(let t=0;t<s.length;t++)this._levelByScale[s[t].scale]=s[t].level;this._fullCacheLodInfos=s}}let V=class extends tt(st){constructor(){super(...arguments),this._styleChanges=[],this._fetchQueue=null,this._parseQueue=null,this._tileHandlerPromise=null,this._isTileHandlerReady=!1,this._collisionOverlay=null,this.fading=!1,this._getCollidersMesh=h=>{const{pixelRatio:e}=h.state;let s=0;const t=[],i=[];for(const r of this._vectorTileContainer.children)if(r.symbols)for(const[o,n]of r.symbols)for(const a of n)for(const l of a.colliders){const u=(l.xScreen+l.dxScreen)*e,y=(l.yScreen+l.dyScreen)*e,d=l.width*e,c=l.height*e,_=a.unique.parts[l.partIndex].targetOpacity>.5;if(!_&&this.layer.showCollisionBoxes!=="all")continue;const f=3,g=1,m=3,w=0,b=_?2:0,S=_?3:0,v=K.makeFlags(b,S);t.push(u,y,v,u+d,y,v,u,y+c,v,u+d,y+c,v),i.push(s+0,s+1,s+2,s+1,s+3,s+2),s+=4;const T=_?f:g,A=_?m:w,x=K.makeFlags(T,A);t.push(u,y,x,u+d,y,x,u,y+1,x,u+d,y+1,x),i.push(s+0,s+1,s+2,s+1,s+3,s+2),s+=4,t.push(u,y+c-1,x,u+d,y+c-1,x,u,y+c,x,u+d,y+c,x),i.push(s+0,s+1,s+2,s+1,s+3,s+2),s+=4,t.push(u,y,x,u+1,y,x,u,y+c,x,u+1,y+c,x),i.push(s+0,s+1,s+2,s+1,s+3,s+2),s+=4,t.push(u+d-1,y,x,u+d,y,x,u+d-1,y+c,x,u+d,y+c,x),i.push(s+0,s+1,s+2,s+1,s+3,s+2),s+=4}return{vertexData:new Int16Array(t),indexData:new Uint32Array(i)}},this._getCollidersColors=()=>[1,.5,0,1,1,0,0,1,0,1,.5,1,0,.5,0,1],this._getCollidersOpacities=()=>[.05,.01,.15,.2]}async hitTest(h,e){if(!this._tileHandlerPromise)return null;await this._tileHandlerPromise;const s=await this._vectorTileContainer.hitTest(e);if(!s||s.length===0)return null;const t=s[0]-1,i=this._styleRepository,r=i.getStyleLayerByUID(t);if(!r)return null;const o=i.getStyleLayerIndex(r.id);return[{type:"graphic",mapPoint:h,layer:this.layer,graphic:new Ee({attributes:{layerId:o,layerName:r.id,layerUID:t},layer:this.layer,sourceLayer:this.layer})}]}update(h){if(this._tileHandlerPromise&&this._isTileHandlerReady)return h.pixelRatio!==this._tileHandler.devicePixelRatio?(this._start(),void(this._tileHandler.devicePixelRatio=h.pixelRatio)):void(this._styleChanges.length>0?this._tileHandlerPromise=this._applyStyleChanges():(this._fetchQueue.pause(),this._parseQueue.pause(),this._fetchQueue.state=h.state,this._parseQueue.state=h.state,this._tileManager.update(h)||this.requestUpdate(),this._parseQueue.resume(),this._fetchQueue.resume()))}attach(){const{style:h}=this.layer.currentStyleInfo;this._styleRepository=new ae(h),this._tileInfoView=new zt(this.layer.tileInfo,this.layer.fullExtent),this._vectorTileContainer=new kt(this._tileInfoView),this._tileHandler=new at(this.layer,this._styleRepository,window.devicePixelRatio||1),this.container.addChild(this._vectorTileContainer),this._start(),this.addAttachHandles([this._vectorTileContainer.on("fade-start",()=>{this.fading=!0,this.notifyChange("updating"),this.requestUpdate()}),this._vectorTileContainer.on("fade-complete",()=>{var e;(e=this._collisionOverlay)==null||e.requestRender(),this.fading=!1,this.notifyChange("updating"),this.requestUpdate()}),Ne(()=>this.layer.showCollisionBoxes,e=>{e!=="none"?this._collisionOverlay||(this._collisionOverlay=new K({getMesh:this._getCollidersMesh,getColors:this._getCollidersColors,getOpacities:this._getCollidersOpacities}),this.container.addChild(this._collisionOverlay)):this._collisionOverlay&&(this.container.removeChild(this._collisionOverlay),this._collisionOverlay=null),this.container.requestRender()},Qe),this.layer.on("paint-change",e=>{if(e.isDataDriven)this._styleChanges.push({type:D.PAINTER_CHANGED,data:e}),this.notifyChange("updating"),this.requestUpdate();else{const s=this._styleRepository,t=s.getLayerById(e.layer);if(!t)return;const i=t.type===R.SYMBOL;s.setPaintProperties(e.layer,e.paint),i&&this._vectorTileContainer.restartDeclutter(),this._vectorTileContainer.requestRender()}}),this.layer.on("layout-change",e=>{const s=this._styleRepository,t=s.getLayerById(e.layer);if(!t)return;const i=ze(t.layout,e.layout);if(!B(i)){if($e(i,"visibility")&&$t(i)===1)return s.setLayoutProperties(e.layer,e.layout),t.type===R.SYMBOL&&this._vectorTileContainer.restartDeclutter(),void this._vectorTileContainer.requestRender();this._styleChanges.push({type:D.LAYOUT_CHANGED,data:e}),this.notifyChange("updating"),this.requestUpdate()}}),this.layer.on("style-layer-visibility-change",e=>{const s=this._styleRepository,t=s.getLayerById(e.layer);t&&(s.setStyleLayerVisibility(e.layer,e.visibility),t.type===R.SYMBOL&&this._vectorTileContainer.restartDeclutter(),this._vectorTileContainer.requestRender())}),this.layer.on("style-layer-change",e=>{this._styleChanges.push({type:D.LAYER_CHANGED,data:e}),this.notifyChange("updating"),this.requestUpdate()}),this.layer.on("delete-style-layer",e=>{this._styleChanges.push({type:D.LAYER_REMOVED,data:e}),this.notifyChange("updating"),this.requestUpdate()}),this.layer.on("load-style",()=>this._loadStyle()),this.layer.on("spriteSource-change",e=>{this._newSpriteSource=e.spriteSource,this._styleChanges.push({type:D.SPRITES_CHANGED,data:null});const s=this._styleRepository.layers;for(const t of s)switch(t.type){case R.SYMBOL:t.getLayoutProperty("icon-image")&&this._styleChanges.push({type:D.LAYOUT_CHANGED,data:{layer:t.id,layout:t.layout}});break;case R.LINE:t.getPaintProperty("line-pattern")&&this._styleChanges.push({type:D.PAINTER_CHANGED,data:{layer:t.id,paint:t.paint,isDataDriven:t.isPainterDataDriven()}});break;case R.FILL:t.getLayoutProperty("fill-pattern")&&this._styleChanges.push({type:D.PAINTER_CHANGED,data:{layer:t.id,paint:t.paint,isDataDriven:t.isPainterDataDriven()}})}this.notifyChange("updating"),this.requestUpdate()})])}detach(){this._stop(),this.container.removeAllChildren(),this._vectorTileContainer=q(this._vectorTileContainer),this._tileHandler=q(this._tileHandler)}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this._collisionOverlay&&this._vectorTileContainer.restartDeclutter(),this.requestUpdate()}supportsSpatialReference(h){var e;return Ae((e=this.layer.tileInfo)==null?void 0:e.spatialReference,h)}canResume(){let h=super.canResume();const{currentStyleInfo:e}=this.layer;if(h&&(e!=null&&e.layerDefinition)){const s=this.view.scale,{minScale:t,maxScale:i}=e.layerDefinition;e&&e.layerDefinition&&(t&&t<s&&(h=!1),i&&i>s&&(h=!1))}return h}isUpdating(){const h=this._vectorTileContainer.children;return!this._isTileHandlerReady||!this._fetchQueue||!this._parseQueue||this._fetchQueue.updating||this._parseQueue.updating||h.length>0&&h.some(e=>e.invalidating)||this.fading}acquireTile(h){var s;const e=this._createVectorTile(h);return(s=this._tileHandlerPromise)==null||s.then(()=>{this._fetchQueue.push(e.key).then(t=>this._parseQueue.push({key:e.key,data:t})).then(t=>{e.once("attach",()=>this.requestUpdate()),e.setData(t),this.requestUpdate(),this.notifyChange("updating")}).catch(t=>{this.notifyChange("updating"),Z(t)||ie.getLogger(this.declaredClass).error(t)})}),e}releaseTile(h){const e=h.key.id;this._fetchQueue.abort(e),this._parseQueue.abort(e),this.requestUpdate()}_start(){if(this._stop(),this._tileManager=new ct({acquireTile:s=>this.acquireTile(s),releaseTile:s=>this.releaseTile(s),tileInfoView:this._tileInfoView},this._vectorTileContainer),!this.layer.currentStyleInfo)return;const h=new AbortController,e=this._tileHandler.start({signal:h.signal}).then(()=>{this._fetchQueue=new re({tileInfoView:this._tileInfoView,process:(s,t)=>this._getTileData(s,t),concurrency:15}),this._parseQueue=new re({tileInfoView:this._tileInfoView,process:(s,t)=>this._parseTileData(s,t),concurrency:8}),this.requestUpdate(),this._isTileHandlerReady=!0});this._tileHandler.spriteMosaic.then(s=>{this._vectorTileContainer.setStyleResources(s,this._tileHandler.glyphMosaic,this._styleRepository),this.requestUpdate()}),this._tileHandlerAbortController=h,this._tileHandlerPromise=e}_stop(){if(!this._tileHandlerAbortController||!this._vectorTileContainer)return;const h=this._tileHandlerAbortController;h&&h.abort(),this._tileHandlerPromise=null,this._isTileHandlerReady=!1,this._fetchQueue=q(this._fetchQueue),this._parseQueue=q(this._parseQueue),this._tileManager=q(this._tileManager),this._vectorTileContainer.removeAllChildren()}async _getTileData(h,e){const s=await this._tileHandler.fetchTileData(h,e);return this.notifyChange("updating"),s}async _parseTileData(h,e){return this._tileHandler.parseTileData(h,e)}async _applyStyleChanges(){this._isTileHandlerReady=!1,this._fetchQueue.pause(),this._parseQueue.pause(),this._fetchQueue.clear(),this._parseQueue.clear(),this._tileManager.clearCache();const h=this._styleChanges;try{await this._tileHandler.updateStyle(h)}catch(o){ie.getLogger(this.declaredClass).error("error applying vector-tiles style update",o.message),this._fetchQueue.resume(),this._parseQueue.resume(),this._isTileHandlerReady=!0}const e=this._styleRepository,s=[];h.forEach(o=>{if(o.type!==D.LAYER_REMOVED)return;const n=o.data,a=e.getLayerById(n.layer);a&&s.push(a.uid)});const t=[];let i;h.forEach(o=>{const n=o.type,a=o.data;switch(n){case D.PAINTER_CHANGED:e.setPaintProperties(a.layer,a.paint),i=a.layer;break;case D.LAYOUT_CHANGED:e.setLayoutProperties(a.layer,a.layout),i=a.layer;break;case D.LAYER_REMOVED:return void e.deleteStyleLayer(a.layer);case D.LAYER_CHANGED:e.setStyleLayer(a.layer,a.index),i=a.layer.id;break;case D.SPRITES_CHANGED:this._vectorTileContainer.setSpriteMosaic(this._tileHandler.setSpriteSource(this._newSpriteSource)),this._newSpriteSource=null,i=null}const l=e.getLayerById(i);l&&t.push(l.uid)});const r=this._vectorTileContainer.children;if(s.length>0){this._vectorTileContainer.deleteStyleLayers(s);for(const o of r)o.deleteLayerData(s)}if(this._fetchQueue.resume(),this._parseQueue.resume(),t.length>0){const o=[];for(const n of r){const a=this._fetchQueue.push(n.key).then(l=>this._parseQueue.push({key:n.key,data:l,styleLayerUIDs:t})).then(l=>n.setData(l));o.push(a)}await Promise.all(o)}this._styleChanges=[],this._isTileHandlerReady=!0,this.notifyChange("updating"),this.requestUpdate()}async _loadStyle(){const{style:h}=this.layer.currentStyleInfo,e=Fe(h);this._isTileHandlerReady=!1,this._fetchQueue.pause(),this._parseQueue.pause(),this._fetchQueue.clear(),this._parseQueue.clear(),this.notifyChange("updating"),this._styleRepository=new ae(e),this._vectorTileContainer.destroy(),this._tileManager.clear(),this._tileHandlerAbortController.abort(),this._tileHandlerAbortController=new AbortController;const{signal:s}=this._tileHandlerAbortController;try{this._tileHandlerPromise=this._tileHandler.setStyle(this._styleRepository,e),await this._tileHandlerPromise}catch(i){if(!Z(i))throw i}if(s.aborted)return this._fetchQueue.resume(),this._parseQueue.resume(),this._isTileHandlerReady=!0,this.notifyChange("updating"),void this.requestUpdate();const t=await this._tileHandler.spriteMosaic;this._vectorTileContainer.setStyleResources(t,this._tileHandler.glyphMosaic,this._styleRepository),this._fetchQueue.resume(),this._parseQueue.resume(),this._isTileHandlerReady=!0,this.notifyChange("updating"),this.requestUpdate()}_createVectorTile(h){const e=this._tileInfoView.getTileBounds(X(),h),s=this._tileInfoView.getTileResolution(h.level);return new St(h,s,e[0],e[3],512,512,this._styleRepository)}};function $t(h){if(B(h))return 0;switch(h.type){case"partial":return Object.keys(h.diff).length;case"complete":return Math.max(Object.keys(h.oldValue).length,Object.keys(h.newValue).length);case"collection":return Object.keys(h.added).length+Object.keys(h.changed).length+Object.keys(h.removed).length}}F([Q()],V.prototype,"_fetchQueue",void 0),F([Q()],V.prototype,"_parseQueue",void 0),F([Q()],V.prototype,"_isTileHandlerReady",void 0),F([Q()],V.prototype,"fading",void 0),V=F([Pe("esri.views.2d.layers.VectorTileLayerView2D")],V);const Os=V;export{Os as default};
