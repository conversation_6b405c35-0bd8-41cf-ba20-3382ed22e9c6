<!-- 保养班组 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <SLDrawer
      ref="refForm"
      :config="addOrUpdateConfig"
    ></SLDrawer>
    <SLDrawer
      ref="refForm1"
      :config="addUser"
    ></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import teamTable from './components/teamTable.vue'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import { formatDate } from '@/utils/DateFormatter'
import { getInspectionTeamSerch, postInspectionTeam, deleteInspectionTeam } from '@/api/equipment_assets/equipmentInspection'
import { removeSlash } from '@/utils/removeIdSlash'
import { getWaterSupplyTree } from '@/api/company_org'
import { getUserList } from '@/api/user/index'
import { traverse, uniqueFunc } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const refForm = ref<ISLDrawerIns>()

const refForm1 = ref<ISLDrawerIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '班组名称', field: 'name', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          type: 'success',
          text: '新增',
          icon: ICONS.ADD,
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: false,
  indexVisible: true,
  expandable: true,
  expandComponent: shallowRef(teamTable),
  columns: [
    { label: '班组名称', prop: 'name' },
    { label: '备注', prop: 'remark' },
    { label: '创建人', prop: 'creatorName' },
    { label: '创建时间', prop: 'createTime', formatter: row => formatDate(row.createTime, 'YYYY-MM-DD HH:mm') }
  ],
  operations: [
    {
      type: 'primary',
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.EDIT,
      click: row => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      icon: ICONS.DELETE,
      perm: $btnPerms('RoleManageDelete'),
      click: row => haneleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  labelWidth: '130px',
  submit: (params: any) => {
    let val = '添加成功'
    if (params.id) { val = '修改成功' }
    if (!params.maintainCircuitTeamCList || params.maintainCircuitTeamCList?.length === 0) {
      ElMessage.warning('请添加班组人员')
      return
    }
    params.type = '保养班组'
    postInspectionTeam(params).then(() => {
      ElMessage.success(val)
      refreshData()
      refForm.value?.closeDrawer()
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 12,
          type: 'input',
          label: '班组名称',
          field: 'name',
          rules: [{ required: true, message: '请输入班组名称' }]
        },
        {
          xl: 18,
          type: 'textarea',
          label: '备注/说明',
          field: 'remark'
        },
        {
          type: 'table',
          field: 'maintainCircuitTeamCList',
          config: {
            indexVisible: true,
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '添加班组成员',
                        perm: true,
                        click: () => {
                          refForm1.value?.openDrawer()
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            height: '350px',
            dataList: computed(() => data.selectUser) as any,
            columns: [
              {
                label: '成员账户',
                prop: 'email'
              }, {
                label: '成员名称',
                prop: 'userName'
              }

            ],
            operations: [
              {
                text: '移除',
                type: 'danger',
                icon: ICONS.DELETE,
                perm: $btnPerms('RoleManageDelete'),
                click: row => {
                  data.selectUser = data.selectUser.filter(item => item.userId !== row.userId)
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 用户选择
const addUser = reactive<IDrawerConfig>({
  title: '添加成员',
  labelWidth: '100px',
  width: 500,
  submit: (params: any) => {
    if (params.userId === undefined) {
      ElMessage.warning('请选择成员')
    }
    data.UserList.forEach((item:any) => {
      if (item.value === params.userId) {
        params.userName = item.userName
        data.selectUser.push(item)
        refForm1.value?.closeDrawer()
      }
    })
    data.selectUser = uniqueFunc(data.selectUser, 'userId')
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          label: '执行部门',
          field: 'key1',
          defaultExpandAll: true,
          checkStrictly: true,
          options: computed(() => data.WaterSupplyTree) as any,
          onChange: row => { data.getUserListValue(row) }
        },
        {
          type: 'select',
          label: '执行人员',
          field: 'userId',
          options: computed(() => data.UserList) as any
        }
      ]
    }
  ]
})

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增班组'
  data.selectUser = []
  addOrUpdateConfig.defaultValue = { }
  refForm.value?.openDrawer()
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '修改班组'
  data.selectUser = row.maintainCircuitTeamCList
  addOrUpdateConfig.defaultValue = { ...(row) || {} }
  refForm.value?.openDrawer()
}

const haneleDelete = (row: { id: string }) => {
  SLConfirm('确定删除该班组, 是否继续?', '删除提示').then(() => {
    deleteInspectionTeam([row.id]).then(() => {
      ElMessage.success('删除成功')
      refreshData()
    })
  })
}

const data = reactive({
  // 当前选中用户
  selectUser: [] as any[],
  // 请购部门
  WaterSupplyTree: [],

  // 用户列表
  UserList: [] as any[],

  getWaterSupplyTreeValue: () => {
    const depth = 2
    getWaterSupplyTree(depth).then(res => {
      data.WaterSupplyTree = traverse(res.data.data || [])
    })
  },
  getUserListValue: (pid:string) => {
    getUserList({ pid }).then(res => {
      const value = res.data.data.data || []
      data.UserList = value.map(item => {
        return { label: item.firstName, value: removeSlash(item.id.id), userName: item.firstName, email: item.email, userId: removeSlash(item.id.id) }
      })
    })
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    type: '保养班组',
    name: '',
    ...(refSearch.value?.queryParams || {})
  }
  getInspectionTeamSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data || 0
  })
}

onMounted(() => {
  refreshData()
  data.getWaterSupplyTreeValue()
})
</script>
