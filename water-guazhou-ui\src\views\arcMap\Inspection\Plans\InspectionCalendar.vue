<template>
  <div class="inspection-calendar">
    <div class="calendar-header">
      <div class="header-left">
        <el-button-group>
          <el-button @click="prevMonth" :icon="ArrowLeft" type="primary" plain>上个月</el-button>
          <el-button @click="today" type="primary">今天</el-button>
          <el-button @click="nextMonth" type="primary" plain>下个月<el-icon class="el-icon--right"><ArrowRight /></el-icon></el-button>
        </el-button-group>
      </div>
      <div class="header-title">
        <h2>{{ formatMonthTitle(date) }} 巡检任务日历</h2>
      </div>

    </div>


    <div class="main-content">
      <!-- 日历容器 -->
      <div class="calendar-container">
        <!-- 加载指示器 -->
        <div v-if="loading" class="loading-overlay">
          <el-loading background="rgba(255, 255, 255, 0.8)" :text="'加载中...'" />
        </div>

        <!-- 月视图 -->
        <el-calendar ref="calendarRef" v-model="date">
          <!-- 自定义头部 -->
          <template #header="{ date }">
            <div class="el-calendar-header-custom">
              <el-button-group>
                <el-button size="small" @click="prevMonth" :icon="ArrowLeft"></el-button>
                <el-button size="small" @click="today">今天</el-button>
                <el-button size="small" @click="nextMonth" :icon="ArrowRight"></el-button>
              </el-button-group>
              <span class="el-calendar__title">{{ formatMonthTitle(date) }}</span>
            </div>
          </template>

          <!-- 自定义日期单元格 -->
          <template #dateCell="{ data }">
            <div
              class="calendar-day"
              @click.stop="showDayTasks(data.day)"
              :data-date="moment(data.day).format('YYYY-MM-DD')"
              :class="{
                'has-tasks': getTasksForDate(data.day).length > 0,
                'is-loading': loading && selectedDate && moment(selectedDate).format('YYYY-MM-DD') === moment(data.day).format('YYYY-MM-DD'),
                'is-selected': selectedDate && moment(selectedDate).format('YYYY-MM-DD') === moment(data.day).format('YYYY-MM-DD')
              }"
            >
              <div class="day-header">
                <span>{{ data.day.split('-').slice(2).join('') }}</span>
                <el-badge v-if="getTasksForDate(data.day).length > 0" :value="getTasksForDate(data.day).length" type="primary" />
              </div>
              <div class="day-content">
                <el-scrollbar max-height="150px" v-if="getTasksForDate(data.day).length > 0">
                  <div
                    v-for="task in getTasksForDate(data.day).slice(0, 2)"
                    :key="task.id"
                    class="task-item"
                    :class="getTaskStatusClass(task)"
                  >
                    <el-tooltip
                      :content="`${task.name} (${task.receiveUserName})`"
                      placement="top"
                      :show-after="300"
                    >
                      <div class="task-title">{{ task.name }}</div>
                    </el-tooltip>
                    <div class="task-assignee">{{ task.receiveUserName }}</div>
                  </div>
                  <div v-if="getTasksForDate(data.day).length > 2" class="more-tasks">
                    <el-button
                      type="primary"
                      size="small"
                      @click.stop="showDayTasks(data.day)"
                    >
                      查看更多 ({{ getTasksForDate(data.day).length - 2 }})
                    </el-button>
                  </div>
                </el-scrollbar>
                <div v-else class="no-tasks">
                  <el-empty :image-size="40" description="无任务"></el-empty>
                </div>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>

      <!-- 当日任务列表 -->
      <div v-if="selectedDate" class="day-tasks-panel">
        <div class="day-tasks-header">
          <h3>{{ formatSelectedDate() }} 任务列表</h3>
          <div>
            <el-button type="success" size="small" @click="refreshTaskList" style="margin-right: 10px">
              <el-icon><RefreshRight /></el-icon> 刷新
            </el-button>
          </div>
        </div>

        <div class="day-tasks-list">
          <el-table :data="dayTasks" style="width: 100%" border stripe v-loading="loading">
            <el-table-column prop="name" label="任务名称" min-width="120">
              <template #default="{ row }">
                <el-tooltip :content="row.name" placement="top" :show-after="300">
                  <span class="task-name-cell">{{ row.name }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="code" label="任务编号" width="120"></el-table-column>
            <el-table-column prop="planCircleName" label="计划周期" width="100">
              <template #default="{ row }">
                <el-tooltip :content="row.planCircleName || '未指定'" placement="top" :show-after="300">
                  <span>{{ row.planCircleName || '未指定' }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="districtAreaName" label="区域" width="120"></el-table-column>
            <el-table-column prop="moveType" label="巡检方式" width="100"></el-table-column>
            <el-table-column prop="receiveUserName" label="接收人员" width="100"></el-table-column>
            <el-table-column prop="receiveUserDepartmentName" label="所属部门" width="120">
              <template #default="{ row }">
                {{ row.receiveUserDepartmentName || '未指定' }}
              </template>
            </el-table-column>
            <el-table-column prop="collaborateUserName" label="协作人员" width="100">
              <template #default="{ row }">
                {{ row.collaborateUserName || '无' }}
              </template>
            </el-table-column>
            <el-table-column label="开始时间" width="160">
              <template #default="{ row }">
                {{ moment(row.beginTime).format('YYYY-MM-DD HH:mm') }}
              </template>
            </el-table-column>
            <el-table-column label="结束时间" width="160">
              <template #default="{ row }">
                {{ moment(row.endTime).format('YYYY-MM-DD HH:mm') }}
              </template>
            </el-table-column>
            <el-table-column prop="keyPointCount" label="关键点数" width="90"></el-table-column>
            <el-table-column prop="deviceCount" label="设备数" width="90"></el-table-column>
            <el-table-column prop="presentState" label="到位状态" width="100"></el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getTaskStatusType(row)" size="small">
                  {{ getTaskStatusText(row) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="120">
              <template #default="{ row }">
                <el-tooltip :content="row.remark || '无'" placement="top" :show-after="300">
                  <span>{{ row.remark || '无' }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="pagination-container" v-if="pagination.total > 0">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              background
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ArrowLeft, ArrowRight, RefreshRight } from '@element-plus/icons-vue'
import { GetPatrolTaskList } from '@/api/patrol'
import { SLMessage } from '@/utils/Message'
import moment from 'moment'
const date = ref(new Date())
const calendarRef = ref()
// 定义任务类型
interface Task {
  id: string
  code: string
  planId: string
  districtAreaId: string
  districtAreaName: string
  isNormalPlan: boolean
  isNeedFeedback: boolean
  moveType: string
  devices: string
  specialDevices: string | null
  name: string
  receiveUserId: string
  receiveUserName: string
  receiveUserDepartmentId: string | null
  receiveUserDepartmentName: string
  collaborateUserId: string | null
  collaborateUserName: string | null
  presentDistance: string | null
  remark: string | null
  presentState: string
  statusName: string
  status: string
  fallbackState: string
  keyPointCount: number
  deviceCount: number
  creator: string
  creatorName: string
  creatorDepartmentId: string | null
  creatorDepartmentName: string
  createTime: string
  beginTime: string
  endTime: string
  tenantId: string
  planCircle: string
  planCircleName: string
}

// 定义任务状态类型
type TaskStatus = 'pending' | 'in-progress' | 'completed' | 'overdue'

const selectedDate = ref<string | null>(null)
const dayTasks = ref<Task[]>([])
const tasks = ref<Task[]>([])
const loading = ref(false)

// 分页相关
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 获取任务列表（用于日历单元格显示）
const fetchTasks = async (specificDate?: Date | string) => {
  loading.value = true
  try {
    // 如果提供了特定日期，则查询该日期的任务，否则查询当天的任务
    const targetDate = specificDate ? moment(specificDate) : moment()
    const dateStr = targetDate.format('YYYY-MM-DD')

    const params = {
      beginTime: dateStr,
      endTime: dateStr,
      page: 1,
      size: 100 // 用于日历单元格显示，需要获取所有任务数量
    }

    const res = await GetPatrolTaskList(params)

    if (res.data.code === 200) {
      const fetchedTasks = res.data.data.data || []
      tasks.value = fetchedTasks

      // 如果是当天查询，同时更新日任务列表和选中日期
      if (!specificDate || moment(specificDate).isSame(moment(), 'day')) {
        selectedDate.value = dateStr
        // 重置分页并获取任务
        pagination.currentPage = 1
        await fetchDayTasks()
      }
    } else {
      SLMessage.error(res.data.message || '获取任务列表失败')
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    SLMessage.error('系统错误')
  } finally {
    loading.value = false
  }
}

// 获取指定日期的任务
const getTasksForDate = (dateStr: string): Task[] => {
  // 格式化日期
  const targetDate = moment(dateStr).format('YYYY-MM-DD')

  // 从当前加载的任务中筛选
  const result = tasks.value.filter(task => {
    const taskStartDate = moment(task.beginTime).format('YYYY-MM-DD')
    const taskEndDate = moment(task.endTime).format('YYYY-MM-DD')

    // 检查目标日期是否是任务的开始日期或结束日期
    return taskStartDate === targetDate || taskEndDate === targetDate
  })

  return result
}

// 获取任务状态类名
const getTaskStatusClass = (task: Task): string => {
  return `task-status-${getTaskStatus(task)}`
}

// 获取任务状态
const getTaskStatus = (task: Task): TaskStatus => {
  const now = moment()
  const startTime = moment(task.beginTime)
  const endTime = moment(task.endTime)

  if (now.isBefore(startTime)) {
    return 'pending' // 未开始
  } else if (now.isAfter(endTime)) {
    return task.status === 'COMPLETED' ? 'completed' : 'overdue' // 已完成或已逾期
  } else {
    return 'in-progress' // 进行中
  }
}

// 获取任务状态文本
const getTaskStatusText = (task: Task): string => {
  const status = getTaskStatus(task)
  switch (status) {
    case 'pending':
      return '未开始'
    case 'in-progress':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'overdue':
      return '已逾期'
    default:
      return '未知状态'
  }
}

// 获取任务状态对应的 Element Plus Tag 类型
const getTaskStatusType = (task: Task): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const status = getTaskStatus(task)
  switch (status) {
    case 'pending':
      return 'info'
    case 'in-progress':
      return 'success'
    case 'completed':
      return 'primary'
    case 'overdue':
      return 'danger'
    default:
      return 'info'
  }
}



// 显示日期任务列表
const showDayTasks = async (day: string): Promise<void> => {
  try {
    // 确保日期格式正确
    let formattedDay = '';

    // 处理不同格式的日期输入
    if (typeof day === 'string') {
      if (day.match(/^\d{1,2}$/)) {
        // 如果只是日期数字（如 "28"），添加当前年月
        const currentMonth = moment(date.value).format('YYYY-MM')
        formattedDay = moment(`${currentMonth}-${day.padStart(2, '0')}`).format('YYYY-MM-DD')
      } else {
        // 否则尝试解析完整日期
        formattedDay = moment(day).format('YYYY-MM-DD')
      }
    } else {
      formattedDay = moment(new Date()).format('YYYY-MM-DD')
    }

    // 设置选中日期
    selectedDate.value = formattedDay

    // 重置分页
    pagination.currentPage = 1

    // 查询任务
    await fetchDayTasks()

    // 等待DOM更新
    await nextTick()

    // 手动添加选中样式
    setTimeout(() => {
      // 移除所有单元格的选中样式
      const allDayCells = document.querySelectorAll('.calendar-day')
      allDayCells.forEach(cell => {
        cell.classList.remove('is-selected')
      })

      // 查找并添加选中样式
      const dayCell = document.querySelector(`.calendar-day[data-date="${formattedDay}"]`) as HTMLElement
      if (dayCell) {
        dayCell.classList.add('is-selected')

        // 滚动到选中的单元格
        dayCell.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }, 100)
  } catch (error) {
    console.error('显示日期任务时出错:', error)
    SLMessage.error('显示任务列表时出错')
    loading.value = false
  }
}

// 格式化月视图标题
const formatMonthTitle = (date: Date | string): string => {
  try {
    return moment(date).format('YYYY年MM月');
  } catch (error) {
    return moment(new Date()).format('YYYY年MM月'); // 出错时返回当前月份
  }
}

// 格式化选中日期
const formatSelectedDate = (): string => {
  if (!selectedDate.value) return ''

  try {
    return moment(selectedDate.value).format('YYYY年MM月DD日');
  } catch (error) {
    return moment(new Date()).format('YYYY年MM月DD日'); // 出错时返回当前日期
  }
}

// 上个月
const prevMonth = async () => {
  try {
    // 计算上个月的日期
    const prevMonthDate = moment(date.value).subtract(1, 'month')

    // 设置为上个月的1号
    const firstDayOfMonth = prevMonthDate.startOf('month').format('YYYY-MM-DD')

    // 更新日历月份
    date.value = prevMonthDate.toDate()

    // 直接调用 showDayTasks 来显示1号的任务
    // 这会自动设置 selectedDate 并获取任务
    await showDayTasks(firstDayOfMonth)

    // 强制刷新日历视图
    if (calendarRef.value) {
      // 触发日历组件的重新渲染
      calendarRef.value.$forceUpdate()
    }

    // 等待DOM更新
    await nextTick()

    // 查找并滚动到1号单元格
    setTimeout(() => {
      // 直接查找1号单元格
      const firstDayCell = document.querySelector(`.calendar-day[data-date="${firstDayOfMonth}"]`) as HTMLElement

      // 如果找到了1号单元格，滚动到它
      if (firstDayCell) {
        firstDayCell.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }, 100)
  } catch (error) {
    console.error('切换到上个月时出错:', error)
    SLMessage.error('切换月份失败')
  }
}

// 下个月
const nextMonth = async () => {
  try {
    // 计算下个月的日期
    const nextMonthDate = moment(date.value).add(1, 'month')

    // 设置为下个月的1号
    const firstDayOfMonth = nextMonthDate.startOf('month').format('YYYY-MM-DD')

    // 更新日历月份
    date.value = nextMonthDate.toDate()

    // 直接调用 showDayTasks 来显示1号的任务
    // 这会自动设置 selectedDate 并获取任务
    await showDayTasks(firstDayOfMonth)

    // 强制刷新日历视图
    if (calendarRef.value) {
      // 触发日历组件的重新渲染
      calendarRef.value.$forceUpdate()
    }

    // 等待DOM更新
    await nextTick()

    // 查找并滚动到1号单元格
    setTimeout(() => {
      // 直接查找1号单元格
      const firstDayCell = document.querySelector(`.calendar-day[data-date="${firstDayOfMonth}"]`) as HTMLElement

      // 如果找到了1号单元格，滚动到它
      if (firstDayCell) {
        firstDayCell.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }, 100)
  } catch (error) {
    console.error('切换到下个月时出错:', error)
    SLMessage.error('切换月份失败')
  }
}

// 今天
const today = async () => {
  try {
    // 获取今天的日期
    const todayDate = new Date()

    // 格式化今天的日期
    const todayStr = moment(todayDate).format('YYYY-MM-DD')

    // 更新日历月份
    date.value = todayDate

    // 直接调用 showDayTasks 来显示今天的任务
    // 这会自动设置 selectedDate 并获取任务
    await showDayTasks(todayStr)

    // 强制刷新日历视图
    if (calendarRef.value) {
      // 触发日历组件的重新渲染
      calendarRef.value.$forceUpdate()
    }

    // 等待DOM更新
    await nextTick()

    // 查找并滚动到今天的单元格
    setTimeout(() => {
      // 直接查找今天的单元格
      const todayCell = document.querySelector(`.calendar-day[data-date="${todayStr}"]`) as HTMLElement

      // 如果找到了今天的单元格，滚动到它
      if (todayCell) {
        todayCell.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }, 100)
  } catch (error) {
    console.error('切换到今天时出错:', error)
    SLMessage.error('切换日期失败')
  }
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchDayTasks()
}

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchDayTasks()
}

// 获取当日任务（分页）
const fetchDayTasks = async () => {
  if (!selectedDate.value) {
    return
  }

  loading.value = true
  try {
    const params = {
      beginTime: selectedDate.value,
      endTime: selectedDate.value,
      page: pagination.currentPage,
      size: pagination.pageSize
    }

    const res = await GetPatrolTaskList(params)

    if (res.data.code === 200) {
      // 获取API返回的任务
      let fetchedTasks = res.data.data.data || []

      // 严格过滤，只保留当天的任务（beginTime或endTime必须是当天）
      fetchedTasks = fetchedTasks.filter((task: Task) => {
        const taskBeginDate = moment(task.beginTime).format('YYYY-MM-DD')
        const taskEndDate = moment(task.endTime).format('YYYY-MM-DD')
        return taskBeginDate === selectedDate.value || taskEndDate === selectedDate.value
      })

      dayTasks.value = fetchedTasks
      // 更新总记录数为过滤后的实际任务数量，而不是API返回的总数
      pagination.total = fetchedTasks.length
    } else {
      SLMessage.error(res.data.message || '获取任务列表失败')
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    SLMessage.error('系统错误')
  } finally {
    loading.value = false
  }
}

// 刷新任务列表
const refreshTaskList = async () => {
  if (!selectedDate.value) {
    SLMessage.warning('请先选择日期')
    return
  }

  // 重置分页
  pagination.currentPage = 1

  // 查询任务
  await fetchDayTasks()
}

onMounted(() => {
  // 初始化时只查询当天的任务
  const today = moment().format('YYYY-MM-DD')
  fetchTasks(today)

  // 添加日历日期单元格点击事件监听器
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement
    if (target.classList.contains('el-calendar-day') ||
        (target.parentElement && target.parentElement.classList.contains('el-calendar-day'))) {

      // 获取日期文本
      let dateText = ''
      if (target.classList.contains('el-calendar-day')) {
        dateText = target.textContent?.trim() || ''
      } else if (target.parentElement && target.parentElement.classList.contains('el-calendar-day')) {
        dateText = target.parentElement.textContent?.trim() || ''
      }

      // 获取当前月份并构建完整日期
      const currentMonth = moment(date.value).format('YYYY-MM')
      const fullDate = `${currentMonth}-${dateText.padStart(2, '0')}`

      // 触发显示任务
      showDayTasks(fullDate)
    }
  })
})
</script>

<style lang="scss" scoped>
.inspection-calendar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .main-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
  }

  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    margin-bottom: 16px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .header-title {
      h2 {
        margin: 0;
        font-size: 22px;
        font-weight: 600;
        color: #303133;
        background: linear-gradient(90deg, #409EFF, #67C23A);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
        display: flex;
        align-items: center;

        .subtitle {
          font-size: 14px;
          font-weight: normal;
          margin-left: 10px;
          background: none;
          -webkit-background-clip: initial;
          background-clip: initial;
          -webkit-text-fill-color: initial;
          color: #909399;
          padding: 2px 8px;
          border-radius: 4px;
          background-color: #f0f2f5;
        }
      }
    }
  }

  .calendar-filters {
    margin-bottom: 16px;
    padding: 16px 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    :deep(.el-form-item__label) {
      font-weight: 500;
    }

    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper) {
      box-shadow: 0 0 0 1px #dcdfe6 inset;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px #409EFF inset;
      }
    }

    :deep(.el-button) {
      border-radius: 4px;
      font-weight: 500;

      &.el-button--primary {
        background: linear-gradient(90deg, #409EFF, #67C23A);
        border: none;

        &:hover {
          background: linear-gradient(90deg, #66b1ff, #85ce61);
        }
      }
    }
  }

  .calendar-container {
    flex: 0 0 auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    padding: 16px;
    margin-bottom: 20px;

    :deep(.el-calendar) {
      height: 100%;

      .el-calendar__header {
        display: none; // 隐藏默认头部，使用自定义头部
      }

      .el-calendar-header-custom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        margin-bottom: 16px;
        border-bottom: 1px solid #ebeef5;

        .el-calendar__title {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }

        .el-button-group {
          .el-button {
            border-radius: 4px;
            margin-right: 8px;

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }

      .el-calendar__body {
        padding: 0;
      }

      .el-calendar-table {
        border-collapse: separate;
        border-spacing: 8px;

        th {
          text-align: center;
          padding: 12px 0;
          font-weight: 600;
          color: #606266;
        }

        td {
          border: 1px solid #ebeef5;
          border-radius: 8px;
          padding: 0;
          vertical-align: top;

          &.is-today {
            .day-header {
              background-color: rgba(64, 158, 255, 0.1);
              color: #409EFF;
              font-weight: 700;
            }
          }

          &.is-selected {
            .day-header {
              background-color: #409EFF;
              color: #fff;
            }
          }
        }
      }

      .el-calendar-day {
        height: auto;
        padding: 0;
      }
    }

    .calendar-day {
      height: 100%;
      min-height: 120px;
      display: flex;
      flex-direction: column;
      cursor: pointer;
      transition: all 0.3s;
      position: relative;
      z-index: 1;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: transparent;
        z-index: -1;
        transition: all 0.3s;
      }

      &:hover {
        background-color: rgba(64, 158, 255, 0.1);
        transform: scale(1.02);
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

        &::after {
          background-color: rgba(64, 158, 255, 0.05);
        }
      }

      &:active {
        transform: scale(0.98);
        background-color: rgba(64, 158, 255, 0.2);
      }

      &.has-tasks {
        background-color: rgba(103, 194, 58, 0.05);
        border: 1px dashed #67C23A;
        animation: pulse 2s infinite;

        @keyframes pulse {
          0% { box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4); }
          70% { box-shadow: 0 0 0 5px rgba(103, 194, 58, 0); }
          100% { box-shadow: 0 0 0 0 rgba(103, 194, 58, 0); }
        }

        &:hover {
          background-color: rgba(103, 194, 58, 0.1);
          border: 1px solid #67C23A;
          animation: none;
        }
      }

      &.is-loading {
        position: relative;
        background-color: rgba(64, 158, 255, 0.05);
        border: 1px dashed #409EFF;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(255, 255, 255, 0.6);
          z-index: 10;
        }
      }

      &.is-selected {
        background-color: rgba(64, 158, 255, 0.25);
        border: 2px solid #409EFF;
        transform: scale(1.03);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        z-index: 10;
        position: relative;

        .day-header {
          background-color: #409EFF;
          color: white;
          font-weight: 700;
        }

        &::after {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          border: 2px solid rgba(64, 158, 255, 0.5);
          border-radius: 6px;
          pointer-events: none;
          animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
          0% { box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4); }
          70% { box-shadow: 0 0 0 6px rgba(64, 158, 255, 0); }
          100% { box-shadow: 0 0 0 0 rgba(64, 158, 255, 0); }
        }
      }

      .day-header {
        padding: 8px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
        color: #606266;
        border-bottom: 1px solid #ebeef5;
        transition: background-color 0.3s;

        span {
          flex: 1;
          text-align: right;
          margin-right: 5px;
        }
      }

      .day-content {
        flex: 1;
        padding: 8px;
        overflow: auto;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #c0c4cc;
          border-radius: 2px;
        }
      }
    }

    .task-item {
      margin-bottom: 8px;
      padding: 8px 10px;
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
      }

      .task-title {
        font-weight: 600;
        margin-bottom: 4px;
      }

      .task-assignee {
        font-size: 11px;
        color: #606266;
        display: flex;
        align-items: center;

        &:before {
          content: '';
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 4px;
          background-color: #909399;
        }
      }

      .task-time {
        font-size: 11px;
        color: #606266;
        margin-top: 4px;
        display: flex;
        align-items: center;

        &:before {
          content: '';
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 4px;
          background-color: #909399;
        }
      }
    }

    .task-status-pending {
      background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
      border-left: 3px solid #1890ff;

      .task-assignee:before,
      .task-time:before {
        background-color: #1890ff;
      }
    }

    .task-status-in-progress {
      background: linear-gradient(135deg, #f6ffed 0%, #f9fff5 100%);
      border-left: 3px solid #52c41a;

      .task-assignee:before,
      .task-time:before {
        background-color: #52c41a;
      }
    }

    .task-status-completed {
      background: linear-gradient(135deg, #f9f0ff 0%, #fcf6ff 100%);
      border-left: 3px solid #722ed1;

      .task-assignee:before,
      .task-time:before {
        background-color: #722ed1;
      }
    }

    .task-status-overdue {
      background: linear-gradient(135deg, #fff2f0 0%, #fff7f6 100%);
      border-left: 3px solid #f5222d;

      .task-assignee:before,
      .task-time:before {
        background-color: #f5222d;
      }
    }

    .no-tasks {
      height: 100%;
      min-height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #909399;
      font-size: 12px;

      :deep(.el-empty) {
        padding: 10px 0;

        .el-empty__image {
          width: 40px;
          height: 40px;
        }

        .el-empty__description {
          margin-top: 5px;
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .more-tasks {
      text-align: center;
      padding: 5px 0;
      font-size: 12px;
      color: #409EFF;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .day-tasks-panel {
      flex: 1;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
      padding: 0;
      overflow: hidden;
      animation: fadeIn 0.3s ease-in-out;
      border: 1px solid #ebeef5;
      margin-bottom: 20px;

      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
      }

      .day-tasks-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #ebeef5;
        background: linear-gradient(90deg, #f5f7fa, #e4e7ed);

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;

          &:before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 18px;
            background: linear-gradient(to bottom, #409EFF, #67C23A);
            margin-right: 10px;
            border-radius: 2px;
          }
        }
      }

      .day-tasks-list {
        padding: 16px;

        :deep(.el-table) {
          --el-table-border-color: #ebeef5;
          --el-table-header-bg-color: #f5f7fa;

          .el-table__header th {
            font-weight: 600;
            color: #606266;
            background-color: #f5f7fa;
          }

          .task-name-cell {
            display: inline-block;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .no-tasks-message {
          padding: 30px 0;
        }

        .pagination-container {
          margin-top: 20px;
          display: flex;
          justify-content: center;

          :deep(.el-pagination) {
            padding: 0;
            margin: 0;
            font-weight: normal;

            .el-pagination__total {
              font-weight: normal;
            }

            .el-pagination__sizes {
              margin-left: 15px;
            }

            .btn-prev, .btn-next {
              border: 1px solid #dcdfe6;
              border-radius: 4px;
            }

            .el-pager li {
              border: 1px solid #dcdfe6;
              border-radius: 4px;
              margin: 0 3px;

              &.is-active {
                background-color: #409EFF;
                color: #fff;
                border-color: #409EFF;
              }
            }
          }
        }
      }
    }
  }



  .task-details {
    padding: 16px;

    .detail-item {
      margin-bottom: 16px;
      display: flex;

      .detail-label {
        font-weight: 600;
        color: #606266;
        width: 100px;
        flex-shrink: 0;
      }

      .detail-value {
        flex: 1;
        color: #303133;
      }

      .status-pending {
        color: #1890ff;
        font-weight: 600;
      }

      .status-in-progress {
        color: #52c41a;
        font-weight: 600;
      }

      .status-completed {
        color: #722ed1;
        font-weight: 600;
      }

      .status-overdue {
        color: #f5222d;
        font-weight: 600;
      }
    }
  }

  :deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;

    .el-dialog__header {
      margin: 0;
      padding: 16px 20px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;

      .el-dialog__title {
        font-weight: 600;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style>