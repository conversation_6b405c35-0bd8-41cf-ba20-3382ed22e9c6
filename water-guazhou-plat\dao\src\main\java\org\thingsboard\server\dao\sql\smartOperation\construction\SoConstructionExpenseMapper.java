package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionExpense;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionExpenseContainer;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionExpensePageRequest;

import java.util.List;

@Mapper
public interface SoConstructionExpenseMapper extends BaseMapper<SoConstructionExpense> {
    List<SoConstructionExpenseContainer> findByPage(SoConstructionExpensePageRequest request);

    boolean update(SoConstructionExpense entity);

    boolean updateFully(SoConstructionExpense entity);

    boolean isCodeExists(@Param("code") String code, @Param("tenantId") String tenantId, @Param("id") String id);

    long countByPage(SoConstructionExpensePageRequest request);

    String getConstructionCodeById(String id);

}
