package org.thingsboard.server.dao.purchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.purchase.ContractDetail;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractDetailSaveRequest;

import java.util.List;

public interface ContractDetailService {
    /**
     * 分页条件查询合同单条目（实际要购入合同中的设备）
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<ContractDetail> findAllConditional(ContractDetailPageRequest request);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(ContractDetail entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否成功
     */
    boolean deleteAll(List<String> idList);

    /**
     * 通过父级id获取所有合同条目
     *
     * @param mainId 唯一标识
     * @return 所有合同条目
     */
    List<ContractDetail> getAllByMainId(String mainId);

    /**
     * 保存合同单条目
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    List<ContractDetail> saveAll(List<ContractDetailSaveRequest> entity);

    /**
     * 通过父级id删除所有
     *
     * @param mainId 唯一标识
     * @param idList 实体的id列表，为空列表或null时删除所有
     * @return 是否成功
     */
    boolean removeAllByMainOnIdNotIn(String mainId, List<String> idList);

}
