package org.thingsboard.server.dao.model.sql.base;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-模型类型对象 base_template_type
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@ApiModel(value = "模型类型", description = "平台管理-模型类型实体类")
@Data
public class BaseTemplateType {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String name;

    /**
     * 模板编码
     */
    @ApiModelProperty(value = "模板编码")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    private String type;

    /**
     * 绑定动画（json数据）
     */
    @ApiModelProperty(value = "绑定动画（json数据）")
    private String bindingAnimation;

    /**
     * 默认图元
     */
    @ApiModelProperty(value = "默认图元")
    private String defaultGraphicElement;

    /**
     * 样式配置（json数组）
     */
    @ApiModelProperty(value = "样式配置（json数组）")
    private String styleConfiguration;

    /**
     * 分辨率适配
     */
    @ApiModelProperty(value = "分辨率适配")
    private String resolutionAdaptation;
}
