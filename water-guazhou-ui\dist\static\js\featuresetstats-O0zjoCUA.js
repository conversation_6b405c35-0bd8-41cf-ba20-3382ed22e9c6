import{t as p,e as F}from"./executionError-BOo4jP8A.js";import{B as h,G as d,J as m,v as A,V as l,a3 as c,y as i}from"./arcadeUtils-1twpZNeO.js";import{f as g}from"./WhereClause-CNjGNHY9.js";import"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./arcadeTimeUtils-CyWQANWo.js";import"./number-Q7BpbuNy.js";async function s(n,r,a,t,o,e){if(t.length===1){if(m(t[0]))return c(n,t[0],i(t[1],-1));if(l(t[0]))return c(n,t[0].toArray(),i(t[1],-1))}else if(t.length===2){if(m(t[0]))return c(n,t[0],i(t[1],-1));if(l(t[0]))return c(n,t[0].toArray(),i(t[1],-1));if(d(t[0])){const u=await t[0].load(),f=await y(g.create(t[1],u.getFieldsIndex()),e,o);return t[0].calculateStatistic(n,f,i(t[2],1e3),r.abortSignal)}}else if(t.length===3&&d(t[0])){const u=await t[0].load(),f=await y(g.create(t[1],u.getFieldsIndex()),e,o);return t[0].calculateStatistic(n,f,i(t[2],1e3),r.abortSignal)}return c(n,t,-1)}async function y(n,r,a){const t=n.getVariables();if(t.length>0){const o=[];for(let u=0;u<t.length;u++){const f={name:t[u]};o.push(await r.evaluateIdentifier(a,f))}const e={};for(let u=0;u<t.length;u++)e[t[u]]=o[u];return n.parameters=e,n}return n}function J(n){n.mode==="async"&&(n.functions.stdev=function(r,a){return n.standardFunctionAsync(r,a,(t,o,e)=>s("stdev",t,o,e,r,n))},n.functions.variance=function(r,a){return n.standardFunctionAsync(r,a,(t,o,e)=>s("variance",t,o,e,r,n))},n.functions.average=function(r,a){return n.standardFunctionAsync(r,a,(t,o,e)=>s("mean",t,o,e,r,n))},n.functions.mean=function(r,a){return n.standardFunctionAsync(r,a,(t,o,e)=>s("mean",t,o,e,r,n))},n.functions.sum=function(r,a){return n.standardFunctionAsync(r,a,(t,o,e)=>s("sum",t,o,e,r,n))},n.functions.min=function(r,a){return n.standardFunctionAsync(r,a,(t,o,e)=>s("min",t,o,e,r,n))},n.functions.max=function(r,a){return n.standardFunctionAsync(r,a,(t,o,e)=>s("max",t,o,e,r,n))},n.functions.count=function(r,a){return n.standardFunctionAsync(r,a,(t,o,e)=>{if(h(e,1,1,r,a),d(e[0]))return e[0].count(t.abortSignal);if(m(e[0])||A(e[0]))return e[0].length;if(l(e[0]))return e[0].length();throw new p(r,F.InvalidParameter,a)})})}export{J as registerFunctions};
