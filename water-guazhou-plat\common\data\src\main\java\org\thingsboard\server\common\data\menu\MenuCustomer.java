/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.menu;

import lombok.EqualsAndHashCode;
import org.thingsboard.server.common.data.BaseData;
import org.thingsboard.server.common.data.id.MenuCustomerId;
import org.thingsboard.server.common.data.id.MenuTenantId;

@EqualsAndHashCode(callSuper = true)
public class MenuCustomer extends BaseData<MenuCustomerId> {

    private MenuCustomerId parentId;
    private String name;
    private String perms;
    private String icon;
    private Integer orderNum;
    private String defaultName;
    private String url;
    private Integer status;
    private Integer flagDelete;
    private MenuTenantId menuTenantId;
    private String additionalInfo;

    public MenuCustomer() {}

    public MenuCustomer(MenuCustomerId id){
        super(id);
    }

    public MenuCustomer(MenuCustomer menuCustomer) {
        super();
        this.parentId = menuCustomer.getParentId();
        this.name = menuCustomer.getName();
        this.perms = menuCustomer.getPerms();
        this.icon = menuCustomer.getIcon();
        this.orderNum = menuCustomer.getOrderNum();
        this.defaultName = menuCustomer.getDefaultName();
        this.url = menuCustomer.getUrl();
        this.status = menuCustomer.getStatus();
        this.flagDelete = menuCustomer.getFlagDelete();
        this.menuTenantId = menuCustomer.getMenuTenantId();
        this.additionalInfo = menuCustomer.getAdditionalInfo();
    }

    public MenuCustomerId getParentId() {
        return parentId;
    }

    public void setParentId(MenuCustomerId parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPerms() {
        return perms;
    }

    public void setPerms(String perms) {
        this.perms = perms;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getDefaultName() {
        return defaultName;
    }

    public void setDefaultName(String defaultName) {
        this.defaultName = defaultName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getFlagDelete() {
        return flagDelete;
    }

    public void setFlagDelete(Integer flagDelete) {
        this.flagDelete = flagDelete;
    }

    public MenuTenantId getMenuTenantId() {
        return menuTenantId;
    }

    public void setMenuTenantId(MenuTenantId menuTenantId) {
        this.menuTenantId = menuTenantId;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }
}
