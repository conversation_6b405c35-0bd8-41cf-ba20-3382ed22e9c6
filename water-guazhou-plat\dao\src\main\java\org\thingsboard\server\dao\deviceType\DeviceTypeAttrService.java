package org.thingsboard.server.dao.deviceType;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceTypeAttr;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypeAttrPageRequest;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypeAttrSaveRequest;

import java.util.List;

public interface DeviceTypeAttrService {
    /**
     * 分页条件查询设备类型属性
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DeviceTypeAttr> findAllConditional(DeviceTypeAttrPageRequest request);

    /**
     * 保存设备类型属性
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    DeviceTypeAttr save(DeviceTypeAttrSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(DeviceTypeAttr entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 通过序列号获取所有设备类型属性
     *
     * @param serialId 序列号
     * @param tenantId 客户id
     * @param type 类型
     * @return 设备类型属性
     */
    List<DeviceTypeAttr> getListBySerialId(String serialId, String tenantId, String type);

}
