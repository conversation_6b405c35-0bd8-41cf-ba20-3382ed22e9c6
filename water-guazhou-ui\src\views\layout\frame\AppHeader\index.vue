<template>
  <div class="app-header">
    <LogoBar class="app-logobar"></LogoBar>
    <MenuBar
      v-if="alarmView"
      class="app-header-menu"
      @menu-click="isCurrentApp => emit('menu-click', isCurrentApp)"
    >
    </MenuBar>
    <div
      v-if="!alarmView"
      class="app-header-menu"
    >
      <span class="login-info">上次登录时间：{{ state.lastLoginDate }}</span>
      <span class="login-info margin-l">上次登录IP：{{ state.lastLoginIp }}</span>
    </div>
    <ToolBar class="user_menu"></ToolBar>
  </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store'
import { hasPermission } from '@/utils/RouterHelper'
import LogoBar from './LogoBar.vue'
import MenuBar from './MenuBar.vue'
import ToolBar from './ToolBar.vue'

const emit = defineEmits(['menu-click'])
const alarmView = computed(() => !hasPermission(['SYS_ADMIN']))
const state = reactive<{
  lastLoginDate: string
  lastLoginIp: string
}>({
  lastLoginDate: '',
  lastLoginIp: ''
})

</script>
<style lang="scss" scoped>
.app-header {
  display: flex;
  background-color: #2e3449;
  height: 52px;
  align-items: center;
  width: 100%;
  position: relative;

  .app-logobar {
    width: 280px;
  }

  .app-header-divider {
    height: 30px;
    width: 1px;

    &.right {
      margin-left: 20px;
      width: 330px;
      /* background-color: #1c223a; */
      // margin-right: 10px;
      color: #fff;
      line-height: 30px;
      font-size: 28px;
    }

    &.left {
      background-color: #505b82;
      margin-left: 10px;
    }
  }

  .app-header-menu {
    width: calc(100% - 500px);
  }

  .login-info {
    color: #fff;
    font-size: 14px;
  }

  .margin-l {
    margin-left: 30px;
  }
}

.user_menu {
  position: absolute;
  right: 0;
}
</style>
