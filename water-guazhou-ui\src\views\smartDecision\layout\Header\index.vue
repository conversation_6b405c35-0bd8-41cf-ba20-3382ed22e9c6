<template>
  <div
    class="header"
    @mouseover="handleMouseOver"
    @mouseleave="handleMouseLeave"
  >
    <div class="header__title">
      <span class="text"> {{ state.title }} </span>
    </div>
    <div class="header__footer">
      <div class="left"></div>
      <div class="right"></div>
    </div>
    <Weather class="header__weather" :logo="props.logo"></Weather>
    <Timer class="header__timer"></Timer>
    <ExitFullScreen
      :collapsed="collapsed"
      @fullscreen="handleFullScreen"
    ></ExitFullScreen>
  </div>
</template>
<script lang="ts" setup>
import Timer from './components/Timer.vue';
import ExitFullScreen from './components/ExitFullScreen.vue';
import Weather from './components/Weather.vue';

const props = defineProps<{
  title?: string;
  logo?: string;
}>();
const emit = defineEmits(['fullscreen']);
const collapsed = ref<boolean>(true);
const state = reactive<{
  title: string;
}>({
  title:
    props.title ||
    window.SITE_CONFIG?.SMART_DECISION_CONFIG?.title ||
    '智慧水务可视化管控平台'
});
const handleMouseOver = () => {
  collapsed.value = false;
};
const handleMouseLeave = () => {
  collapsed.value = true;
};
const isFullScreen = ref<boolean>(false);
const handleFullScreen = (flag?: boolean) => {
  emit('fullscreen', flag);
  if (flag !== undefined) {
    isFullScreen.value = true;
  } else {
    isFullScreen.value = !!document.fullscreenElement;
  }
};
</script>
<style lang="scss" scoped>
.header {
  width: 100%;
  height: 112px;
  position: relative;
  overflow: hidden;
  // background: radial-gradient(
  //   53.64% 66.68% at 47.72% 33.32%,
  //   rgba(11, 63, 199, 0.74) 4.25%,
  //   rgba(21, 24, 74, 0.78) 34.9%,
  //   #080b34 92.57%
  // );
  .header__title {
    width: 100%;
    height: 100%;
    font-family: 'FZLanTingHeiS-H-GB';
    font-style: normal;
    font-weight: 600;
    font-size: 32px;
    line-height: 55px;
    background: url('./imgs/header_bg.png') no-repeat;
    background-position: 50% 0;
    /* identical to box height */
    .text {
      background: linear-gradient(180deg, #ffffff 24.19%, #adc2e0 79.05%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      // text-fill-color: transparent;
      position: absolute;
      left: 50%;
      top: 0;
      transform: translateX(-52%);
      padding: 8px;
    }
  }
  .header__footer {
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 20px;
    padding: 0 48px;
    .left,
    .right {
      width: 30%;
      height: 20px;
    }
    .left {
      background: url('./imgs/header_footer_left.png') no-repeat 0 100%;
    }
    .right {
      background: url('./imgs/header_footer_right.png') no-repeat 100% 100%;
    }
  }
  .header__timer {
    position: absolute;
    right: 54px;
    top: 45px;
  }
  .header__weather {
    position: absolute;
    left: 54px;
    top: 45px;
  }
}
</style>
