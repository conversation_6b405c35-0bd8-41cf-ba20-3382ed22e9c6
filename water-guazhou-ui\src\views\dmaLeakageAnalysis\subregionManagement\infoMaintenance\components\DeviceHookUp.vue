<template>
  <div class="device-hookup">
    <div class="left">
      <SLTree :tree-data="DeviceHookUpTree"></SLTree>
    </div>
    <div class="right overlay-y">
      <Search
        ref="refSearch_All"
        :config="DeviceHookUpSearch_All"
      ></Search>
      <FormTable
        class="right-table"
        :config="DeviceHookUpTable_All"
      ></FormTable>
      <Search
        ref="refSearch_New"
        :config="DeviceHookUpSearch_New"
      ></Search>
      <FormTable
        class="right-table"
        :config="DeviceHookUpTable_New"
      ></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { EditPen, Search as SearchIcon } from '@element-plus/icons-vue'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import {
  DeleteDMADevices,
  DMADirection,
  GetDMADeviceList,
  GetDM<PERSON>HookedDevices,
  HookupDMADevice,
  PostDMADeviceDirection,
  DeviceTypes
} from '@/api/mapservice/dma'
import { ISearchIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { formatDate } from '@/utils/DateFormatter'

const refSearch_All = ref<ISearchIns>()
const refSearch_New = ref<ISearchIns>()
const props = defineProps<{
  tree: NormalOption[]
  currentTreeNode: NormalOption
}>()
const DeviceHookUpTree = reactive<SLTreeConfig>({
  data: props.tree,
  currentProject: props.currentTreeNode,
  treeNodeHandleClick: data => {
    DeviceHookUpTree.currentProject = data
    refreshDeviceHookUpAllData()
    refreshDeviceHookUpNewData()
  }
})
const DeviceHookUpSearch_All = reactive<ISearch>({
  filters: [
    // { type: 'input', label: '编号', field: 'code' },
    { type: 'input', label: '名称', field: 'name' },
    // { type: 'input', label: 'RTU编号', field: 'code' },
    {
      type: 'select',
      label: '设备类型',
      field: 'type',
      options: [
        { label: '流量计', value: '1' },
        { label: '压力计', value: '2' }
        // { label: '大用户', value: '3' }
      ]
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => {
            refreshDeviceHookUpAllData()
            refreshDeviceHookUpNewData()
          },
          loading: computed(() => DeviceHookUpTable_All.loading) as any
        },
        {
          perm: true,
          text: '添加',
          svgIcon: shallowRef(CirclePlus),
          click: () => handleAdd(),
          disabled: computed(() => DeviceHookUpTable_All.loading) as any
        }
      ]
    }
  ],
  defaultParams: {
    type: '1'
  }
})
const DeviceHookUpTable_All = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      DeviceHookUpTable_All.pagination.page = page
      DeviceHookUpTable_All.pagination.limit = size
      refreshDeviceHookUpAllData()
    }
  },
  columns: [
    { label: '设备', prop: 'name' },
    { label: '类型', prop: 'deviceTypeName' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row, val) => formatDate(val)
    }
    // { label: '设备来源', prop: 'code' }
  ],
  handleSelectChange: rows => {
    DeviceHookUpTable_All.selectList = rows || []
  }
})
const refreshDeviceHookUpAllData = async () => {
  try {
    DeviceHookUpTable_All.loading = true
    const res = await GetDMADeviceList({
      page: DeviceHookUpTable_All.pagination.page || 1,
      size: DeviceHookUpTable_All.pagination.limit || 20,
      partitionId: DeviceHookUpTree.currentProject.value,
      ...(refSearch_All.value?.queryParams || {})
    })
    const data = res.data.data || {}
    DeviceHookUpTable_All.dataList = data.data
    DeviceHookUpTable_All.pagination.total = data.total || 0
  } catch (error) {
    SLMessage.error('查询失败')
  }
  DeviceHookUpTable_All.loading = false
}
const DeviceHookUpSearch_New = reactive<ISearch>({
  filters: [
    // { type: 'input', label: '编号', field: 'code' },
    { type: 'input', label: '名称', field: 'name' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          loading: computed(() => DeviceHookUpTable_New.loading) as any,
          click: () => refreshDeviceHookUpNewData()
        },
        {
          perm: true,
          text: '移出',
          svgIcon: shallowRef(Delete),
          type: 'danger',
          disabled: computed(() => DeviceHookUpTable_New.loading) as any,
          click: () => handleRemove()
        }
      ]
    }
  ]
})
const DeviceHookUpTable_New = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      DeviceHookUpTable_New.pagination.page = page
      DeviceHookUpTable_New.pagination.limit = size
      refreshDeviceHookUpNewData()
    }
  },
  columns: [
    // { label: '编号', prop: 'code' },
    { label: '名称', prop: 'name' },
    {
      label: '类型',
      prop: 'type',
      formatter: (row, val) => DeviceTypes[val] || val
    },
    {
      label: '水流方向',
      prop: 'direction',
      tag: true,
      tagColor: (row: any, val: string) => {
        return val === '1' || val === '3' ? '#318DFF' : '#f56c6c'
      },
      formatter: (row, val) => DMADirection[val] || '- -'
    }
  ],
  handleSelectChange: rows => {
    DeviceHookUpTable_New.selectList = rows || []
  },
  operations: [
    {
      perm: true,
      text: '变更方向',
      svgIcon: shallowRef(EditPen),
      click: row => handleDirectionChange(row.id)
    }
  ]
})
const handleDirectionChange = id => {
  SLConfirm('确定变更方向？', '提示信息').then(async () => {
    try {
      const res = await PostDMADeviceDirection(id)
      if (res.data.code === 200) {
        SLMessage.success('操作成功')
        refreshDeviceHookUpNewData()
      } else {
        SLMessage.error(res.data.message)
      }
    } catch (error) {
      SLMessage.error('操作失败')
    }
  })
}
const refreshDeviceHookUpNewData = async () => {
  if (!DeviceHookUpTree.currentProject) {
    // SLMessage.warning('请选择一个分区')
    DeviceHookUpTable_New.dataList = []
    DeviceHookUpTable_New.pagination.total = 0
    return
  }
  try {
    DeviceHookUpTable_New.loading = true
    const res = await GetDMAHookedDevices({
      page: DeviceHookUpTable_New.pagination.page || 1,
      size: DeviceHookUpTable_New.pagination.limit || 20,
      partitionId: DeviceHookUpTree.currentProject?.id,
      ...(refSearch_New.value?.queryParams || {}),
      type: refSearch_All.value?.queryParams?.type
    })
    const data = res.data.data || {}
    DeviceHookUpTable_New.dataList = data.data
    DeviceHookUpTable_New.pagination.total = data.total || 0
  } catch (error) {
    SLMessage.error('查询失败')
  }
  DeviceHookUpTable_New.loading = false
}
const handleAdd = async () => {
  const selected = DeviceHookUpTable_All.selectList || []
  if (!selected.length) {
    SLMessage.warning('请选择要添加的设备')
    return
  }
  try {
    DeviceHookUpTable_All.loading = true
    DeviceHookUpTable_New.loading = true
    const res = await HookupDMADevice(
      selected.map(item => {
        return {
          partitionId: DeviceHookUpTree.currentProject?.id,
          deviceId: item.id,
          type: refSearch_All.value?.queryParams?.type
        }
      })
    )
    if (res.data.code === 200) {
      refreshDeviceHookUpAllData()
      refreshDeviceHookUpNewData()
    } else {
      SLMessage.error(res.data.message)
    }
  } catch (error) {
    SLMessage.error('添加失败')
  }
  DeviceHookUpTable_All.loading = false
  DeviceHookUpTable_New.loading = false
}
const handleRemove = async () => {
  const selected = DeviceHookUpTable_New.selectList || []
  if (!selected.length) {
    SLMessage.warning('请选择要移出的设备')
    return
  }
  SLConfirm('确定移出？', '提示信息')
    .then(async () => {
      try {
        DeviceHookUpTable_All.loading = true
        DeviceHookUpTable_New.loading = true
        const res = await DeleteDMADevices(selected.map(item => item.id))
        if (res.data.code === 200) {
          refreshDeviceHookUpAllData()
          refreshDeviceHookUpNewData()
        } else {
          SLMessage.error(res.data.message)
        }
      } catch (error) {
        SLMessage.error('移出失败')
      }
      DeviceHookUpTable_All.loading = false
      DeviceHookUpTable_New.loading = false
    })
    .catch(() => {
      //
    })
}
onMounted(() => {
  refreshDeviceHookUpAllData()
  refreshDeviceHookUpNewData()
})
</script>
<style lang="scss" scoped>
.device-hookup {
  display: flex;
  border-collapse: collapse;
  .left,
  .right,
  .right-table {
    border-collapse: collapse;
    border: 1px solid var(--el-border-color);
  }
  .left {
    width: 300px;
  }
  .right {
    width: calc(100% - 300px);
    border-left: none;
    .right-search {
      margin: 8px 0;
    }
    .right-table {
      height: 260px;
      border-left: none;
      border-right: none;
      &:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
