<template>
  <!-- 角色管理 -->
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    />
    <!-- <AddOrUpdateDialog
      v-if="addOrUpdateConfig.visible"
      :config="addOrUpdateConfig"
      @refreshData="refreshData"
    /> -->
    <Cataloge
      v-if="cataloge.visible"
      :table-config="cataloge"
    />
    <BtnPermDialog
      ref="refBtnPermDialog"
      :role-id="btnPermDialog.roleId"
      :cur-role="btnPermDialog.curRole"
      @handleclose="handleBtnPermDialogClose"
    />
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import BtnPermDialog from './components/btnpermissions.vue'
import Cataloge from './components/rolemenu.vue'
import { getRolesByPage, deleteRole, saveRole } from '@/api/menu'
import { ICONS } from '@/common/constans/common'
import { useUserStore } from '@/store'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refDialogForm = ref<IDialogFormIns>()

const { $btnPerms } = useGlobal()
const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '搜索', field: 'name', type: 'input' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '添加角色',
          icon: ICONS.ADD,
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
})

const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [{ prop: 'name', label: '角色名称' }],
  // indexVisible: true,
  operations: [
    {
      text: '菜单授权',
      icon: 'iconfont icon-menu',
      perm: $btnPerms('RoleManageMenuAuth'),
      click: row => menuAuthorization(row)
    },
    {
      text: '按钮权限',
      icon: 'iconfont icon-quanxian',
      perm: $btnPerms('RoleManageButtonAuth'),
      click: row => btnAuthorization(row)
    },
    {
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-bianji',
      click: row => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      icon: 'iconfont icon-shanchu',
      perm: $btnPerms('RoleManageDelete'),
      click: row => haneleDelete(row)
    }
  ],
  operationWidth: '400px',
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
// const addOrUpdateConfig = ref<AOUConfig>({
//   visible: false,
//   title: '添加角色',
//   labelWidth: '130px',
//   close: () => {
//     addOrUpdateConfig.value.visible = false
//   },
//   addUrl: 'api/role/saveRole',
//   editUrl: 'api/role/saveRole',
//   defaultValue: {},
//   columns: [
//     {
//       type: 'input',
//       label: '角色名称',
//       key: 'name',
//       rules: [{ required: true, message: '请输入角色名称' }]
//     }
//   ]
// })

const cataloge = reactive<{
  visible: boolean
  roleId: string
  close:() => any
    }>({
      visible: false,
      roleId: '',
      close: () => (cataloge.visible = false)
    })
const btnPermDialog = reactive<{
  roleId: string
  curRole: any
}>({
  roleId: '',
  curRole: {}
})
const refSearch = ref<ICardSearchIns>()
const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  }
  const res = await getRolesByPage(params)
  console.log(res.data)
  TableConfig.dataList = res.data.data || []
  TableConfig.pagination.total = res.data.total || 0
}
const DialogFormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 450,
  title: '添加角色',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '角色名称',
          field: 'name',
          rules: [{ required: true, message: '请输入角色名称' }]
        }
      ]
    }
  ],
  submit: async params => {
    try {
      await saveRole(params)
      SLMessage.success('操作成功')
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})
const clickCreatedRole = () => {
  if (isCUSTOMER_USER.value) {
    SLMessage.error('无权限')
    return
  }
  DialogFormConfig.defaultValue = {}
  DialogFormConfig.title = '添加角色'
  refDialogForm.value?.openDialog()
}
// 点击菜单授权
const menuAuthorization = (row: { id: { id: any } }) => {
  if (isCUSTOMER_USER.value) {
    SLMessage.error('无权限')
    return
  }
  cataloge.roleId = row.id.id
  cataloge.visible = true
}
const refBtnPermDialog = ref<InstanceType<typeof BtnPermDialog>>()
// 按钮权限
const btnAuthorization = (row: { id: { id: string } }) => {
  btnPermDialog.curRole = row
  btnPermDialog.roleId = row.id.id
  refBtnPermDialog.value?.openDialog()
}
const handleBtnPermDialogClose = () => {
  refreshData()
  refBtnPermDialog.value?.closeDialog()
}

const clickEdit = (row: any) => {
  if (isCUSTOMER_USER.value) {
    SLMessage.error('无权限')
    return
  }
  DialogFormConfig.defaultValue = {
    ...(row || {}),
    id: row?.id?.id
  }
  DialogFormConfig.title = '编辑角色'
  refDialogForm.value?.openDialog()
}

const haneleDelete = (row: { id: { id: any } }) => {
  if (isCUSTOMER_USER.value) {
    SLMessage.error('无权限')
    return
  }
  SLConfirm('确定删除该角色, 是否继续?', '删除提示').then(() => {
    // 删除角色方法
    deleteRole(row.id.id)
      .then(() => {
        refreshData()
        SLMessage.success('删除成功')
      }).catch(error => {
        SLMessage.warning(error)
      })
  })
}
const isCUSTOMER_USER = ref<boolean>(false)
onMounted(() => {
  isCUSTOMER_USER.value = useUserStore().roles[0] === 'CUSTOMER_USER'
  refreshData()
})
</script>

<style lang="scss" scoped>
.tree-right-detail-box {
  height: 100%;
  width: 100%;
  padding: 15px;
}
.cardTable {
  height: calc(100% - 90px);
}
.role-manage-h-container {
  margin: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  overflow-y: auto;

  .content-box-container {
    padding: 10px;
    height: calc(100% - 60px);

    .operate-btn {
      button {
        border-radius: 20px;
      }
    }
  }

  .filter-box {
    display: flex;
    height: 48px;
    margin-bottom: 10px;
    justify-content: space-between;

    .box-btns {
      display: flex;
      align-items: center;
    }

    .filter-input {
      width: 200px;
      margin-right: 10px;
    }
  }
}

:deep(.el-tabs__header) {
  min-width: 184px;
}
</style>
