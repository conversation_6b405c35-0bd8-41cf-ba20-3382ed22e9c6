import{_ as n}from"./CardTable-rdWOL4_6.js";import{d as c}from"./manage-BReaEVJk.js";import{d as p,r as l,o as i,g as m,h as d,i as u,C as _}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";const f=p({__name:"gcwcqk",props:{id:{}},setup(t){const o=t,a=l({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"业务阶段",prop:"scopeName"},{label:"开始时间",prop:"startTimeName"},{label:"结束时间",prop:"completeTimeName"},{label:"处理人",prop:"processUserName"},{label:"完成状态",prop:"statusName",tag:!0,tagColor:e=>e.statusName==="完成"?"#67C23A":"#409EFF",formatter:e=>e.statusName}],dataList:[],pagination:{hide:!0}}),s=async()=>{c(o.id).then(e=>{a.dataList=e.data.data||[]})};return i(()=>{s()}),(e,g)=>{const r=n;return m(),d(r,{title:"工程完成情况",config:u(a),class:"card-table"},null,8,["config"])}}}),x=_(f,[["__scopeId","data-v-2315e7be"]]);export{x as default};
