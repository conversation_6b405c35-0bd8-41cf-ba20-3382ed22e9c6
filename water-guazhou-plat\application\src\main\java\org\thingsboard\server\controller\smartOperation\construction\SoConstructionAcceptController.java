package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionAccept;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionAcceptPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionAcceptSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.construction.SoConstructionAcceptService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/constructionAccept")
public class SoConstructionAcceptController extends BaseController {
    @Autowired
    private SoConstructionAcceptService service;


    @GetMapping
    public IPage<SoConstructionAccept> findAllConditional(SoConstructionAcceptPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoConstructionAcceptPageRequest request) {
        return ExcelFileInfo.of("工程验收管理列表", findAllConditional(request).getRecords())
                .nextTitle("constructionCode", "工程编号")
                .nextTitle("constructionName", "工程名称")
                .nextTitle("constructionTypeName", "工程类别")
                .nextTitle("projectName", "所属项目")
                .nextTitle("beginTimeName", "开始日期")
                .nextTitle("endTimeName", "完成日期")
                .nextTitle("creatorName", "创建人")
                .nextTitle("createTimeName", "创建时间");
    }

    @PostMapping
    public SoConstructionAccept save(@RequestBody SoConstructionAcceptSaveRequest req) {
        req.preCheckUpdate(() -> !service.isComplete(req.getConstructionCode(), req.tenantId()), "已完成不可更改");
        return service.save(req);
    }

    @PostMapping("/{constructionCode}/complete")
    public boolean complete(@PathVariable String constructionCode) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.complete(constructionCode, userId, tenantId);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionAcceptSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}