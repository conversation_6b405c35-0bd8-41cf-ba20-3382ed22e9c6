import{_ as it}from"./FormMap-BGaXSqQF.js";import{d as ot,c as m,bF as d,a8 as O,bX as rt,r as H,o as lt,ay as st,g as y,n as T,q as r,F as p,p as i,bh as _,aB as A,aJ as V,h as q,i as c,G as nt,t as dt,a9 as N,bU as mt,bW as pt,bz as ct,du as ft,dv as ut,C as gt}from"./index-r0dFAfgr.js";/* empty css                         */import{f as P}from"./DateFormatter-Bm9a68Ax.js";import{_ as C}from"./Search-NSrhrIa_.js";import{g as yt,I as _t}from"./meterReading-a_0Q9Mjn.js";import{d as v}from"./chart-BKY3iX5C.js";import{j as vt,k as ht,b as B,l as bt}from"./zhandian-YaGuQZe6.js";import{d as kt}from"./index-D9ERhRP6.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./utils-D5nxoMq3.js";import"./WSPlayer-B40t_qqh.js";import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const It={class:"cards"},Tt={class:"title"},Ct={class:"title_item"},Yt={class:"title"},xt={class:"title_item"},Dt={class:"echarts",style:{height:"350px","overflow-y":"auto",padding:"5px 0px"}},St={style:{display:"flex","justify-content":"space-between"}},Mt={style:{width:"30%",height:"350px"}},wt={style:{width:"68%",height:"350px"}},Lt={class:"echarts"},Et={class:"flex"},Ot={class:"item"},Ht={style:{width:"100%",height:"350px"}},At={class:"echarts"},Vt={class:"item"},qt={class:"videoDiv"},Nt=ot({__name:"detail",props:{config:{}},setup(F){var L;const Y=m([]),G=[{label:"提醒报警",value:"1"},{label:"重要报警",value:"2"},{label:"紧急报警",value:"3"}],R=[{label:"液位异常",value:"1"},{label:"水质异常",value:"2"},{label:"设备故障",value:"3"},{label:"通讯异常",value:"4"},{label:"流量异常",value:"5"},{label:"控制异常",value:"6"},{label:"设备健康",value:"7"},{label:"其他",value:"8"}],j=[{label:"未处理",value:"1"},{label:"处理中",value:"2"},{label:"已处理",value:"3"}],o=F,h=m((L=o.config.location)==null?void 0:L.split(",").map(t=>+t)),x=m(),D=m(),S=m(),b=m(),z=m([{label:"报警类型",value:"alarmType",format:t=>{var e;return((e=R.find(a=>a.value===t))==null?void 0:e.label)||"-"}},{label:"触发时间",value:"time",format:t=>P(t,"YYYY-MM-DD HH:mm:ss")||"-"},{label:"持续时间",value:"time",format:t=>{if(o.config.endTime){const e=d(o.config.endTime).diff(t,"date");return Q(e)}return"-"}},{label:"报警等级",value:"alarmLevel",format:t=>{var e;return((e=G.find(a=>a.value===t))==null?void 0:e.label)||"-"}},{label:"处理状态",value:"processStatus",format:t=>{var e;return((e=j.find(a=>a.value===t))==null?void 0:e.label)||"-"}},{label:"处理建议",value:"processMethod",format:t=>t||"-"},{label:"处理结果",value:"processStatus",format:t=>t==="3"?"已处理":"未完成"}]),U=m({defaultParams:{time:[d().add(-1,"d").startOf("day").valueOf(),d().add(2,"d").startOf("day").valueOf()],queryType:"30m",attributeId:o.config.stationAttrId},filters:[{label:"",field:"time",type:"datetimerange",format:"x"},{hidden:window.SITE_CONFIG.SITENAME==="yanting"&&(o.config.alarmType==="日用量报警"||o.config.alarmType==="月用量报警"),label:"选择变量",field:"attributeId",type:"select-tree",options:O(()=>l.stationTree),onChange(){u()}}],operations:[{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>{u()}}]}]}),M=m({defaultParams:{time:[d().add(-1,"d").startOf("day").valueOf(),d().add(2,"d").startOf("day").valueOf()],queryType:"30m",attributeId:""},filters:[{label:"",field:"time",type:"datetimerange",format:"x"},{hidden:window.SITE_CONFIG.SITENAME==="yanting"&&(o.config.alarmType==="日用量报警"||o.config.alarmType==="月用量报警"),label:"选择变量",field:"attributeId",type:"select-tree",options:O(()=>l.stationTreeLinkage),onChange(){u()}}],operations:[{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>{u()}}]}]}),J=m({defaultParams:{time:[d().add(-1,"y").format("YYYY-MM-DD HH:mm:ss"),d().format("YYYY-MM-DD HH:mm:ss")]},filters:[{label:"",field:"time",type:"datetimerange",format:"YYYY-MM-DD HH:mm:ss"}],operations:[{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>{w()}}]}]});function Q(t){const e=[{label:"天",key:864e5},{label:"小时",key:36e5},{label:"分钟",key:6e4},{label:"秒",key:1e3}];let a="";for(const s in e)t>=e[s].key&&(a+=parseInt(t/e[s].key+"")+e[s].label,t%=e[s].key);return a}const u=async()=>{var a,s;const t={stationId:o.config.stationId,attributeId:o.config.stationAttrId,filterStart:0,filterEnd:24,...((a=x.value)==null?void 0:a.queryParams)||{}};t.time&&(t.start=t.time[0],t.end=t.time[1],delete t.time);const e=rt(l.stationTree,"children","id",t.attributeId);window.SITE_CONFIG.SITENAME==="yanting"&&(o.config.alarmType==="日用量报警"||o.config.alarmType==="月用量报警")?(t.beginYm=d(t.start).format("YYYY-MM-DD"),t.endYm=d(t.end).format("YYYY-MM-DD"),t.remoteMeterCode=(s=o.config)==null?void 0:s.deviceId,yt(t).then(n=>{l.detailChart=v(n.data.data.data||[],"表底","1")})):vt(t).then(n=>{l.detailChart=v(n.data.data||[],e.label)})};function W(){B({stationId:o.config.stationId}).then(t=>{l.stationTree=N(t.data||[],"children",{children:"attrList",label:"name",value:"id"}),l.stationTree=k(l.stationTree),u()})}function X(){B({stationId:g.config.stationId}).then(t=>{var e;l.stationTreeLinkage=N(t.data||[],"children",{children:"attrList",label:"name",value:"id"}),l.stationTreeLinkage=k(l.stationTreeLinkage),M.value.defaultParams={time:[d().add(-1,"d").startOf("day").valueOf(),d().add(2,"d").startOf("day").valueOf()],queryType:"30m",attributeId:g.config.remoteStationAttrId};debugger;(e=D.value)==null||e.resetForm(),u()})}function k(t){return t.map(a=>(a.children&&a.children.length!==0?(a.label=a.type,a.value="",a.disabled=!0,k(a.children)):a.disabled=!1,a))}const l=H({stationTree:[],detailChart:v(),stationTreeLinkage:[],detailChartLinkage:v()}),w=()=>{var e;const t=((e=S.value)==null?void 0:e.queryParams)||{};ht({stationId:o.config.stationId,page:1,size:999,startTime:t.time[0],endTime:t.time[1],attr:o.config.attr}).then(a=>{var s;Y.value=(s=a.data.data)==null?void 0:s.data.map(n=>({...n,time:P(n.time,"YYYY-MM-DD HH:mm:ss")}))})},g=H({config:{},videoConfig:{status:!1,url:"",talkurl:"",type:"customHls",key:null}}),$=()=>{bt(o.config.alarmRuleId).then(t=>{debugger;g.config=t.data.data.data||{},X(),K(g.config.remoteVideoId)})},K=t=>{kt(t).then(e=>{g.videoConfig={status:!0,url:e.data.data.data??"",talkurl:"",type:Z(e.data.data.data),key:t.id}})},Z=t=>t.indexOf("ws")!==-1?"ws":"customHls";return lt(()=>{setTimeout(()=>{var t;(t=b.value)==null||t.resize()},400),W(),console.log("props.config",o.config),w(),$()}),(t,e)=>{const a=mt,s=pt,n=ct,tt=ft,et=ut,at=it,E=st("VChart");return y(),T("div",It,[r(n,{shadow:"never",class:"cards_card"},{default:p(()=>[r(s,{gutter:20},{default:p(()=>[r(a,{span:24},{default:p(()=>[i("div",Tt,[e[1]||(e[1]=i("span",null,"报警内容",-1)),i("div",Ct,_(o.config.alarmInfo||"-"),1)])]),_:1}),(y(!0),T(A,null,V(c(z),(f,I)=>(y(),q(a,{key:I,span:6},{default:p(()=>[i("div",Yt,[i("span",null,_(f.label)+"：",1),i("div",xt,_(f.format(o.config[f.value]||"")),1)])]),_:2},1024))),128))]),_:1})]),_:1}),r(n,{shadow:"never",class:"cards_card"},{default:p(()=>[e[2]||(e[2]=i("div",{class:"card_title"},[i("span",null,"报警历史")],-1)),i("div",null,[r(C,{ref_key:"refHistorySearch",ref:S,config:c(J),style:{"margin-left":"-20px"}},null,8,["config"]),i("div",Dt,[r(et,{style:{"max-width":"600px"}},{default:p(()=>[(y(!0),T(A,null,V(c(Y),(f,I)=>(y(),q(tt,{key:I,placement:"top",timestamp:f.time},{default:p(()=>[nt(_(f.alarmInfo),1)]),_:2},1032,["timestamp"]))),128))]),_:1})])])]),_:1}),r(n,{shadow:"never",class:"cards_card"},{default:p(()=>[e[3]||(e[3]=i("div",{class:"card_title"},[i("span",null,"数据分析")],-1)),i("div",St,[i("div",Mt,[r(at,{modelValue:c(h),"onUpdate:modelValue":e[0]||(e[0]=f=>dt(h)?h.value=f:null),disabled:!0,readonly:!0},null,8,["modelValue"])]),i("div",wt,[r(C,{ref_key:"refSearch",ref:x,config:c(U),style:{"margin-left":"-20px"}},null,8,["config"]),i("div",Lt,[r(E,{ref_key:"refChart",ref:b,option:c(l).detailChart},null,8,["option"])])])])]),_:1}),r(n,{shadow:"never",class:"cards_card"},{default:p(()=>[i("div",Et,[i("div",Ot,[e[4]||(e[4]=i("div",{class:"card_title"},[i("span",null,"联动监测点")],-1)),i("div",Ht,[r(C,{ref_key:"refSearchLinkage",ref:D,config:c(M),style:{"margin-left":"-20px"}},null,8,["config"]),i("div",At,[r(E,{ref_key:"refChart",ref:b,option:c(l).detailChartLinkage},null,8,["option"])])])]),i("div",Vt,[e[5]||(e[5]=i("div",{class:"card_title"},[i("span",null,"联动监测点")],-1)),i("div",qt,[r(_t,{video:c(g).videoConfig},null,8,["video"])])])])]),_:1})])}}}),ga=gt(Nt,[["__scopeId","data-v-51d4e335"]]);export{ga as default};
