import { request } from '@/plugins/axios'

// 保养统计
// 条件查询保养统计信息
export const getStatisticsSerch = (params?: {
  startTime?: string
  endTime?: string
}) => request({
  url: `/api/maintain/task/m/statistics`,
  method: 'get',
  params
})

// 保养规范
// 分页条件查询保养规范信息
export const getstandardSerch = (params?: {
  page: number | undefined
  size: number | undefined
  keywords?: string
  deviceTypeId?: string
}) => request({
  url: `/api/maintain/standard/m`,
  method: 'get',
  params
})

// 添加/修改保养规范
export const poststandard = (params?: {
  id?: string
  serialId: string
  method: string
  remark: string
}) => request({
  url: `/api/maintain/standard/m`,
  method: 'post',
  data: params
})

// 删除保养规范
export const deletestandard = (ids: string[]) => request({
  url: `/api/maintain/standard/m`,
  method: 'delete',
  data: ids
})

// 保养计划
// 分页条件查询保养计划信息
export const getMaintenancePlanSerch = (params?: {
  page: number | undefined
  size: number | undefined
  planName?: string
  teamName?: string
  userName?: string
  startStartTime?: string | string[]
  startEndTime?: string
  endStartTime?: string
  endEndTime?: string | string[]
}) => request({
  url: `/api/maintain/plan/m`,
  method: 'get',
  params
})

// 查询保养计划信息详情
export const getMaintenancePlanSerchDetail = (id:string) => request({
  url: `/api/maintain/plan/m/detail/${id}`,
  method: 'get'
})

// 添加/修改保养计划
export const postMaintenancePlan = (params?: {
  id?: string
  name: string
  teamId: string
  userId: string
  startTime: string
  executionDays: string
  intervalDays: string
  executionNum: string
  reviewer: string
  remark: string
  maintainPlanCList: any[]
}) => request({
  url: `/api/maintain/plan/m`,
  method: 'post',
  data: params
})

// 删除保养计划
export const deleteMaintenancePlan = (ids: string[]) => request({
  url: `/api/maintain/plan/m`,
  method: 'delete',
  data: ids
})

// 审核保养计划
export const putMaintenancePlan = (params?: {
  id: string
  status: string
}) => request({
  url: `/api/maintain/plan/m/reviewer`,
  method: 'post',
  data: params
})

// 保养任务
// 分页条件查询保养任务信息
export const getMaintenanceTaskSerch = (params?: {
  page: number | undefined
  size: number | undefined
  TaskName?: string
  teamName?: string
  userName?: string
  startStartTime?: string
  startEndTime?: string
  endStartTime?: string
  endEndTime?: string
}) => request({
  url: `/api/maintain/task/m`,
  method: 'get',
  params
})

// 获取保养任务详情
export const getMaintenanceTaskDetails = (id:string) => request({
  url: `/api/maintain/task/m/detail/${id}`,
  method: 'get'
})

// 添加/修改保养任务
export const postMaintenanceTask = (params?: {
  id?: string
  name: string
  teamId: string
  userId: string
  startTime: string
  executionDays: string
  intervalDays: string
  executionNum: string
  reviewer: string
  remark: string
  maintainTaskCList: any[]
}) => request({
  url: `/api/maintain/task/m`,
  method: 'post',
  data: params
})

// 删除保养任务
export const deleteMaintenanceTask = (ids: string[]) => request({
  url: `/api/maintain/task/m`,
  method: 'delete',
  data: ids
})

// 审核保养任务
export const putMaintenanceTask = (params?: {
  id: string
  status: string
}) => request({
  url: `/api/maintain/task/m/reviewer`,
  method: 'post',
  data: params
})

export const postMaintainance = (params?: {
  id: string
  img: string
  status: number
  remark: string
}) => request({
  url: `/api/maintain/task/c`,
  method: 'post',
  data: params
})
