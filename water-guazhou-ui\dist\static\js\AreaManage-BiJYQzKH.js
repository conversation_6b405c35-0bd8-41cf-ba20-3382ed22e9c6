import{_ as ue}from"./Panel-DyoxrWMd.js";import{d as fe,c as d,r as b,s as P,S as H,b as n,bB as A,a1 as de,o as ye,Q as ge,g as G,h as S,F as B,p as $,q as L,i as m,an as R,aw as E,_ as he,aq as be,bq as j,ak as ve,bM as ke,X as we,C as _e}from"./index-r0dFAfgr.js";import{g as v}from"./MapView-DaoQedLH.js";import{w as z}from"./Point-WxyopZva.js";import{g as Q,a as U,s as x}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as K,a as Pe}from"./LayerHelper-Cn-iiqxI.js";import{q as Ae,c as Le}from"./QueryHelper-ILO3qZqg.js";import{u as xe}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import{D as Ce,G as De,c as Fe,d as Ie,A as Ge,e as Se,f as Be,a as Me,b as Oe}from"./area-Bpl-8n1R.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import Re from"./RightDrawerMap-D5PhmGFO.js";import Ke from"./AreaTreeNode-DOLKeGfa.js";import"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Te={class:"form-wrapper"},qe={class:"table-box"},Ne=fe({__name:"AreaManage",setup(Ve){const X=d(),Y=d(),k=d(),g=d(),y=d(),h=d(),M=d(),w=d(),t={},i=b({tabs:[],loading:!1,layerIds:[],layerInfos:[],isMounted:!1}),_=b({dataList:[],columns:[{minWidth:120,label:"关键点名称",prop:"name"},{minWidth:160,label:"创建时间",prop:"createTime"},{minWidth:120,label:"备注",prop:"remark"}],operations:[{perm:!0,text:"删除",type:"danger",svgIcon:P(j),click:e=>ee(e)}],handleRowClick:e=>Z(e),pagination:{refreshData:({page:e,size:r})=>{_.pagination.page=e||1,_.pagination.limit=r||20,D()}}}),Z=e=>{var a;const r=new z({latitude:e.lat,longitude:e.lon,spatialReference:(a=t.view)==null?void 0:a.spatialReference});Q(t.view,new v({geometry:r}))},ee=e=>{e&&H("确定删除?","提示信息").then(()=>{Ce(e==null?void 0:e.id).then(r=>{r.data.code===200?(n.success(r.data.message),D()):n.error(r.data.message)}).catch(()=>{n.error("系统错误")})}).catch(()=>{})},T=b({group:[{fields:[{type:"input",field:"keyword",placeholder:"输入关键字筛选"},{type:"tree",field:"area",style:{height:"200px"},filterBy:"keyword",nodeClick:async e=>{var r,a,o;(r=t.graphicsLayer)==null||r.removeAll(),(a=t.bufferLayer)==null||a.removeAll(),(o=t.keypointLayer)==null||o.removeAll(),e.data.layer===2&&(i.curDistrict=e,De(e.value).then(s=>{var c,u,f,I,J;const p=e.data.type==="区域"?"polygon":"polyline";if(!s.data.data)return;(c=t.bufferLayer)==null||c.removeAll(),(u=t.graphicsLayer)==null||u.removeAll();const l=JSON.parse(s.data.data);if(l.bufferGeometry){t.bufferGeometry=U("polygon",l.bufferGeometry.rings,l.bufferGeometry.spatialReference);const O=new v({geometry:t.bufferGeometry,symbol:x(((f=t.bufferGeometry)==null?void 0:f.type)||"polygon",{color:[0,255,0,.1],outlineWidth:1,outlineColor:"#00ff00"})});(I=t.bufferLayer)==null||I.add(O)}if(l.geometry){const O=U(p,p==="polygon"?l.geometry.rings:l.geometry.paths,l.geometry.spatialReference);t.graphic=new v({geometry:O,symbol:x(p)}),(J=t.graphicsLayer)==null||J.add(t.graphic),Q(t.view,t.graphic,{avoidHighlight:!0})}}),D())},customNode:P(Ke),expandOnClickNode:!1,customProps:{iconBtns:[{isTextBtn:!0,perm:e=>e.data.layer!==2,svgIcon:P(ve),click:e=>re(e)},{isTextBtn:!0,perm:e=>e.data.parentId!==null,svgIcon:P(ke),click:e=>oe(e)},{isTextBtn:!0,type:"danger",perm:e=>e.data.parentId!==null,svgIcon:P(j),click:e=>ie(e)}],textBtns:[{isTextBtn:!0,perm:e=>{var r;return console.log(e),((r=e.path)==null?void 0:r.length)===2?!1:e.data.layer===1&&e.data.parentId!==null},text:"添加区域/路线",click:e=>ae(e)},{isTextBtn:!0,perm:e=>e.data.layer===2&&e.data.parentId!==null,text:"添加关键点",click:e=>te(e)}]},options:[]}]},{fieldset:{desc:"片区信息"},fields:[]}],labelPosition:"top",gutter:12}),te=async e=>{var r,a,o;i.curDistrict=e,C(),V.defaultValue={areaId:(r=i.curDistrict)==null?void 0:r.value},(a=w.value)==null||a.Open(),await A(),(o=M.value)==null||o.resetForm()},re=async e=>{var r,a;i.curArea=e,i.curAreaOp="添加片区",q.defaultValue={parentId:e.value},C(),(r=g.value)==null||r.Open(),await A(),(a=k.value)==null||a.resetForm()},ae=async e=>{var r,a;i.curArea=e,i.curDistrictOp="添加区域/路线",N.defaultValue={districtId:e.value,type:"区域",buffer:e.data.buffer},C(),(r=h.value)==null||r.Open(),await A(),(a=y.value)==null||a.resetForm()},C=()=>{var e,r,a;(e=g.value)==null||e.Close(),(r=w.value)==null||r.Close(),(a=h.value)==null||a.Close()},oe=async e=>{var r,a;C(),e.data.layer===1?(i.curAreaOp="编辑片区",(r=g.value)==null||r.Open(),await A(),k.value&&(k.value.dataForm=e.data)):e.data.layer===2&&(i.curDistrictOp="编辑区域",(a=h.value)==null||a.Open(),await A(),y.value&&(y.value.dataForm=e.data))},ie=e=>{H("确定删除？","提示信息").then(async()=>{try{const r=e.data.layer===1?await Fe(e.value):await Ie(e.value);r.data.code===200?(n.success(r.data.message),F()):n.error(r.data.message)}catch{n.error("操作失败")}}).catch(()=>{})},q=b({gutter:0,group:[{fields:[{type:"input",label:"片区名称",field:"name",rules:[{required:!0,message:"请输入片区名称"}]},{type:"textarea",label:"备注",field:"remark"},{type:"btn-group",btns:[{perm:!0,text:"取消",type:"default",styles:{marginLeft:"auto"},click:()=>{var e;return(e=g.value)==null?void 0:e.Close()}},{perm:!0,text:"确定",click:()=>{var e;return(e=k.value)==null?void 0:e.Submit()}}]}]}],labelPosition:"right",submit:async e=>{var r;try{const a=await Ge(e);a.data.code===200?(n.success(a.data.message),F(),(r=g.value)==null||r.Close()):n.error(a.data.message)}catch{n.error("系统错误")}}}),N=b({title:"添加区域/路线",gutter:0,draggable:!0,dialogWidth:300,model:!1,labelPosition:"top",group:[{fields:[{type:"radio-button",field:"type",label:"类型",options:[{label:"区域",value:"区域"},{label:"路线",value:"路线"}],rules:[{required:!0,message:"请选择类型"}]},{type:"input",label:"名称",field:"name",rules:[{required:!0,message:"请输入名称"}]},{handleHidden:(e,r,a)=>{a.hidden=e.type!=="路线"},type:"input-number",label:"缓冲距离",field:"buffer",append:"米"},{type:"btn-group",btns:[{perm:!0,text:"绘制",styles:{width:"100%"},type:"success",click:()=>se()}]},{type:"btn-group",btns:[{perm:!0,text:"取消",type:"default",styles:{width:"100%"},click:()=>{var e;return(e=h.value)==null?void 0:e.Close()}},{perm:!0,text:"确定",styles:{width:"100%"},click:()=>{var e;return(e=y.value)==null?void 0:e.Submit()}}]}]}],submit:async e=>{var r,a,o,s;try{if(!t.graphic){n.warning("请先绘制图形");return}const p={geometry:(a=(r=t.graphic)==null?void 0:r.geometry)==null?void 0:a.toJSON(),bufferGeometry:(o=t.bufferGeometry)==null?void 0:o.toJSON()},l={...e,points:JSON.stringify(p)},c=await Se(l);c.data.code===200?(n.success(c.data.message),F(),(s=h.value)==null||s.Close()):n.error(c.data.message)}catch{n.error("系统错误")}},defaultValue:{type:"区域",buffer:30}}),se=()=>{var r,a,o,s;(r=t.graphicsLayer)==null||r.removeAll(),(a=t.bufferLayer)==null||a.removeAll(),t.graphic=void 0,t.bufferGeometry=void 0;const e=(o=y.value)==null?void 0:o.dataForm.type;t.graphicsLayer&&t.sketch&&(t.sketch.layer=t.graphicsLayer),(s=t.sketch)==null||s.create(e==="路线"?"polyline":"polygon")},V=b({group:[{fields:[{type:"input",label:"名称",field:"name",rules:[{required:!0,message:"请输入名称"}]},{type:"btn-group",btns:[{perm:!0,text:"绘制",styles:{width:"100%"},type:"success",click:()=>ne()}]},{type:"textarea",label:"备注",field:"remark"},{type:"btn-group",btns:[{perm:!0,text:"取消",type:"default",styles:{width:"100%"},click:()=>{var e;return(e=w.value)==null?void 0:e.Close()}},{perm:!0,text:"确定",styles:{width:"100%"},click:()=>{var e;return(e=M.value)==null?void 0:e.Submit()}}]}]}],labelPosition:"top",gutter:0,defaultValue:{},submit:e=>{if(!t.keyPoint){n.warning("请先绘制关键点");return}const r={...e,lon:t.keyPoint.longitude,lat:t.keyPoint.latitude};Be(r).then(a=>{var o;a.data.code===200?(n.success(a.data.message),D(),(o=w.value)==null||o.Close()):n.error(a.data.message)}).catch(()=>{n.error("添加失败")})}}),D=()=>{var e;Me({areaId:(e=i.curDistrict)==null?void 0:e.value}).then(r=>{var o;(o=t.keypointLayer)==null||o.removeAll();const a=r.data.data;_.dataList=(a==null?void 0:a.data)||[],_.pagination.total=(a==null?void 0:a.total)||0,a==null||a.data.map(s=>{var u,f;const p=new z({longitude:s.lon,latitude:s.lat,spatialReference:(u=t.view)==null?void 0:u.spatialReference}),l=new v({geometry:p,symbol:x("point",{outlineWidth:1,outlineColor:"#00ffff",color:"#ffffff"})}),c=new v({geometry:p,symbol:x("text",{text:s.name})});(f=t.keypointLayer)==null||f.addMany([l,c])})}).catch(()=>{n.error("刷新关键点失败")})},ne=()=>{var e,r;(e=t.keypointLayer)==null||e.removeAll(),t.keyPoint=void 0,t.keypointLayer&&t.sketch&&(t.sketch.layer=t.keypointLayer),(r=t.sketch)==null||r.create("point")},le=async()=>{var r,a;i.layerIds=Pe(t.view);const e=await we(i.layerIds);i.layerInfos=((a=(r=e.data)==null?void 0:r.result)==null?void 0:a.rows)||[]},{initSketch:pe,destroySketch:ce}=xe(),W=e=>{var a,o,s;if(e.state!=="complete")return;const r=e.graphics[0];if(r.geometry.type==="point")t.keyPoint=r.geometry;else if(t.graphic=r,r.geometry.type==="polyline"){const p=(a=y.value)==null?void 0:a.dataForm.buffer;p&&Ae(Le({bufferSpatialReference:(o=t.view)==null?void 0:o.spatialReference,distances:[p],geometries:[r.geometry],outSpatialReference:(s=t.view)==null?void 0:s.spatialReference,geodesic:!0,unit:"meters",unionResults:!1})).then(l=>{var c,u,f;if(t.bufferGeometry=l[0],l.length){const I=new v({geometry:t.bufferGeometry,symbol:x(t.bufferGeometry.type,{color:[0,255,0,.1],outlineWidth:1,outlineColor:"#00ff00"})});(c=t.bufferLayer)==null||c.removeAll(),(u=t.bufferLayer)==null||u.add(I),(f=t.sketch)==null||f.complete()}})}},me=async e=>{t.view=e,t.bufferLayer=K(t.view,{id:"area-buffer",title:"缓冲区"}),t.graphicsLayer=K(t.view,{id:"area-manage",title:"片区"}),t.keypointLayer=K(t.view,{id:"key-point",title:"关键点"}),t.sketch=pe(t.view,t.graphicsLayer,{createCallBack:W,updateCallBack:W}),await le()},F=()=>{Oe().then(e=>{const r=T.group[0].fields[1],a=e.data.data;r.options=de([a])})};return ye(()=>{i.isMounted=!0,F()}),ge(()=>{ce()}),(e,r)=>{const a=he,o=be,s=ue;return G(),S(Re,{ref_key:"refMap",ref:Y,title:"片区管理","right-drawer-width":600,onMapLoaded:me},{default:B(()=>[$("div",Te,[L(a,{ref_key:"refForm",ref:X,config:m(T)},null,8,["config"]),$("div",qe,[L(o,{config:m(_)},null,8,["config"])])]),m(i).isMounted?(G(),S(s,{key:0,ref_key:"refPanelArea",ref:g,title:m(i).curAreaOp,telport:"#arcmap-wrapper",draggable:!0,class:"area-panel"},{default:B(()=>[L(a,{ref_key:"refFormArea",ref:k,config:m(q)},null,8,["config"])]),_:1},8,["title"])):R("",!0),m(i).isMounted?(G(),S(s,{key:1,ref_key:"refPanelDistrict",ref:h,title:m(i).curDistrictOp,telport:"#arcmap-wrapper",draggable:!0,class:E("district-panel")},{default:B(()=>[L(a,{ref_key:"refFormDistrict",ref:y,config:m(N)},null,8,["config"])]),_:1},8,["title"])):R("",!0),m(i).isMounted?(G(),S(s,{key:2,ref_key:"refPanelKeyPoint",ref:w,title:"添加关键点",telport:"#arcmap-wrapper",draggable:!0,class:E("keypoint-panel")},{default:B(()=>[L(a,{ref_key:"refFormKeyPoint",ref:M,config:m(V)},null,8,["config"])]),_:1},512)):R("",!0)]),_:1},512)}}}),Er=_e(Ne,[["__scopeId","data-v-c9a4813f"]]);export{Er as default};
