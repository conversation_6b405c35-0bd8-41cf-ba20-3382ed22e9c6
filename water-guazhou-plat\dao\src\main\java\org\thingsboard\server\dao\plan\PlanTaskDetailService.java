package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.plan.PlanTaskDetail;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskDetailCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskDetailSaveRequest;

import java.util.List;

public interface PlanTaskDetailService {
    /**
     * 分页条件查询盘点任务条目
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<PlanTaskDetail> findAllConditional(PlanTaskDetailPageRequest request);

    List<PlanTaskDetail> saveAll(List<PlanTaskDetailSaveRequest> entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(PlanTaskDetail entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否成功
     */
    boolean deleteAll(List<String> idList);

    /**
     * 完成任务条目
     *
     * @param req 明细
     * @return 是否成功
     */
    boolean complete(PlanTaskDetailCompleteRequest req);

    /**
     * 通过父级id删除所有
     *
     * @param id 唯一标识
     * @return 是否成功
     */
    boolean deleteByMainId(String id);

    /**
     * 重置任务条目的状态
     *
     * @param id 唯一标识
     * @return 是否成功
     */
    boolean reset(String id);

    /**
     * 通过父级id删除所有
     *
     * @param id 唯一标识
     * @return 是否成功
     */
    boolean removeAllByMainId(String id);

}
