package org.thingsboard.server.dao.model.sql.smartOperation.project;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.*;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.util.imodel.response.NameDisplayableEnum;

public enum SoGeneralSystemScope implements NameDisplayableEnum {
    SO_PROJECT("项目立项", SoProject.class),

    SO_CONSTRUCTION("单项工程", SoConstruction.class),
    SO_CONSTRUCTION_DESIGN("设计管理", SoConstructionDesign.class),
    SO_CONSTRUCTION_ESTIMATE("工程预算", SoConstructionEstimate.class),
    SO_CONSTRUCTION_VISA("签证单", SoConstructionVisa.class),
    SO_CONSTRUCTION_CONTRACT("合同管理", SoConstructionContract.class),
    SO_CONSTRUCTION_EXPENSE("费用管理", SoConstructionExpense.class),
    SO_CONSTRUCTION_APPLY("实施管理", SoConstructionApply.class),
    SO_CONSTRUCTION_ACCEPT("验收管理", SoConstructionAccept.class),
    SO_CONSTRUCTION_SETTLEMENT("工程结算", SoConstructionSettlement.class),

    SO_DEVICE_ITEM("设备项", SoDeviceItem.class);


    private final String displayName;
    private final Class<?> taskInfoTableClass;

    SoGeneralSystemScope(String displayName, Class<?> taskInfoTableClass) {
        this.displayName = displayName;
        this.taskInfoTableClass = taskInfoTableClass;
    }

    public boolean getIsProject() {
        return this == SoGeneralSystemScope.SO_PROJECT;
    }

    public String getDisplayName() {
        return displayName;
    }

    public boolean getIsConstruction() {
        return this == SoGeneralSystemScope.SO_CONSTRUCTION;
    }

    public boolean getIsConstructionContract() {
        return this == SoGeneralSystemScope.SO_CONSTRUCTION_CONTRACT;
    }

    public boolean getIsConstructionApply() {
        return this == SoGeneralSystemScope.SO_CONSTRUCTION_APPLY;
    }

    public TableInfo getTaskInfoTable() {
        return SqlHelper.table(taskInfoTableClass);
    }
}
