package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDevice;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SoDevicePageRequest extends AdvancedPageableQueryEntity<SoDevice, SoDevicePageRequest> {

    // 设备编码
    private String serialId;

    // 设备类别编码
    private String typeSerialId;

    // 名称
    private String name;

    // 型号
    private String model;

}
