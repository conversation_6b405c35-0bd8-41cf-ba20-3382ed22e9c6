"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5159],{3920:(e,t,s)=>{s.d(t,{p:()=>l,r:()=>u});var r=s(43697),o=s(15923),i=s(61247),n=s(5600),a=s(52011),p=s(72762);const l=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new i.Z}get updatingHandles(){return this._get("updatingHandles")||new p.t}};return(0,r._)([(0,n.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,r._)([(0,n.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,r._)([(0,a.j)("esri.core.HandleOwner")],t),t};let u=class extends(l(o.Z)){};u=(0,r._)([(0,a.j)("esri.core.HandleOwner")],u)},42033:(e,t,s)=>{s.d(t,{E:()=>o,_:()=>i});var r=s(70586);async function o(e,t){const{WhereClause:r}=await s.e(1534).then(s.bind(s,41534));return r.create(e,t)}function i(e,t){return(0,r.pC)(e)?(0,r.pC)(t)?`(${e}) AND (${t})`:e:t}},72762:(e,t,s)=>{s.d(t,{t:()=>d});var r=s(43697),o=s(15923),i=s(61247),n=s(70586),a=s(17445),p=s(1654),l=s(5600),u=s(52011);let d=class extends o.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new i.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,s={}){return this._installWatch(e,t,s,a.YP)}addWhen(e,t,s={}){return this._installWatch(e,t,s,a.gx)}addOnCollectionChange(e,t,{initial:s=!1,final:r=!1}={}){const o=++this._handleId;return this._handles.add([(0,a.on)(e,"after-changes",this._createSyncUpdatingCallback(),a.Z_),(0,a.on)(e,"change",t,{onListenerAdd:s?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:r?e=>t({added:[],removed:e.toArray()}):void 0})],o),{remove:()=>this._handles.remove(o)}}addPromise(e){if((0,n.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(c)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const s=()=>this._handles.remove(t);return e.then(s,s),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,s={},r){const o=++this._handleId;s.sync||this._installSyncUpdatingWatch(e,o);const i=r(e,t,s);return this._handles.add(i,o),{remove:()=>this._handles.remove(o)}}_installSyncUpdatingWatch(e,t){const s=this._createSyncUpdatingCallback(),r=(0,a.YP)(e,s,{sync:!0,equals:()=>!1});return this._handles.add(r,t),r}_createSyncUpdatingCallback(){return()=>{this._handles.remove(c),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,p.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(c))})),c)}}};(0,r._)([(0,l.Cb)({readOnly:!0})],d.prototype,"updating",void 0),d=(0,r._)([(0,u.j)("esri.core.support.WatchUpdatingTracking")],d);const c=-42},54295:(e,t,s)=>{s.d(t,{V:()=>n});var r=s(43697),o=s(5600),i=(s(75215),s(67676),s(52011));const n=e=>{let t=class extends e{get apiKey(){return this._isOverridden("apiKey")?this._get("apiKey"):"portalItem"in this?this.portalItem?.apiKey:null}set apiKey(e){null!=e?this._override("apiKey",e):(this._clearOverride("apiKey"),this.clear("apiKey","user"))}};return(0,r._)([(0,o.Cb)({type:String})],t.prototype,"apiKey",null),t=(0,r._)([(0,i.j)("esri.layers.mixins.APIKeyMixin")],t),t}},17287:(e,t,s)=>{s.d(t,{Y:()=>l});var r=s(43697),o=s(92604),i=s(70586),n=s(5600),a=(s(75215),s(67676),s(52011)),p=s(66677);const l=e=>{let t=class extends e{get title(){if(this._get("title")&&"defaults"!==this.originOf("title"))return this._get("title");if(this.url){const e=(0,p.Qc)(this.url);if((0,i.pC)(e)&&e.title)return e.title}return this._get("title")||""}set title(e){this._set("title",e)}set url(e){this._set("url",(0,p.Nm)(e,o.Z.getLogger(this.declaredClass)))}};return(0,r._)([(0,n.Cb)()],t.prototype,"title",null),(0,r._)([(0,n.Cb)({type:String})],t.prototype,"url",null),t=(0,r._)([(0,a.j)("esri.layers.mixins.ArcGISService")],t),t}},70082:(e,t,s)=>{s.d(t,{Z:()=>d});var r=s(43697),o=s(2368),i=s(35454),n=s(96674),a=s(5600),p=(s(75215),s(67676),s(52011));const l=new i.X({esriFeatureEditToolAutoCompletePolygon:"auto-complete-polygon",esriFeatureEditToolCircle:"circle",esriFeatureEditToolEllipse:"ellipse",esriFeatureEditToolFreehand:"freehand",esriFeatureEditToolLine:"line",esriFeatureEditToolNone:"none",esriFeatureEditToolPoint:"point",esriFeatureEditToolPolygon:"polygon",esriFeatureEditToolRectangle:"rectangle",esriFeatureEditToolArrow:"arrow",esriFeatureEditToolTriangle:"triangle",esriFeatureEditToolLeftArrow:"left-arrow",esriFeatureEditToolRightArrow:"right-arrow",esriFeatureEditToolUpArrow:"up-arrow",esriFeatureEditToolDownArrow:"down-arrow"});let u=class extends((0,o.J)(n.wq)){constructor(e){super(e),this.name=null,this.description=null,this.drawingTool=null,this.prototype=null,this.thumbnail=null}};(0,r._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"name",void 0),(0,r._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"description",void 0),(0,r._)([(0,a.Cb)({json:{read:l.read,write:l.write}})],u.prototype,"drawingTool",void 0),(0,r._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"prototype",void 0),(0,r._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"thumbnail",void 0),u=(0,r._)([(0,p.j)("esri.layers.support.FeatureTemplate")],u);const d=u},16451:(e,t,s)=>{s.d(t,{Z:()=>y});var r=s(43697),o=s(2368),i=s(96674),n=s(5600),a=(s(75215),s(67676),s(71715)),p=s(52011),l=s(30556),u=s(72729),d=s(70082);let c=class extends((0,o.J)(i.wq)){constructor(e){super(e),this.id=null,this.name=null,this.domains=null,this.templates=null}readDomains(e){const t={};for(const s of Object.keys(e))t[s]=(0,u.im)(e[s]);return t}writeDomains(e,t){const s={};for(const t of Object.keys(e))e[t]&&(s[t]=e[t]?.toJSON());t.domains=s}};(0,r._)([(0,n.Cb)({json:{write:!0}})],c.prototype,"id",void 0),(0,r._)([(0,n.Cb)({json:{write:!0}})],c.prototype,"name",void 0),(0,r._)([(0,n.Cb)({json:{write:!0}})],c.prototype,"domains",void 0),(0,r._)([(0,a.r)("domains")],c.prototype,"readDomains",null),(0,r._)([(0,l.c)("domains")],c.prototype,"writeDomains",null),(0,r._)([(0,n.Cb)({type:[d.Z],json:{write:!0}})],c.prototype,"templates",void 0),c=(0,r._)([(0,p.j)("esri.layers.support.FeatureType")],c);const y=c},56765:(e,t,s)=>{s.d(t,{Z:()=>u});var r,o=s(43697),i=s(46791),n=s(96674),a=s(5600),p=(s(75215),s(67676),s(52011));let l=r=class extends n.wq{constructor(e){super(e),this.floorField=null,this.viewAllMode=!1,this.viewAllLevelIds=new i.Z}clone(){return new r({floorField:this.floorField,viewAllMode:this.viewAllMode,viewAllLevelIds:this.viewAllLevelIds})}};(0,o._)([(0,a.Cb)({type:String,json:{write:!0}})],l.prototype,"floorField",void 0),(0,o._)([(0,a.Cb)({json:{read:!1,write:!1}})],l.prototype,"viewAllMode",void 0),(0,o._)([(0,a.Cb)({json:{read:!1,write:!1}})],l.prototype,"viewAllLevelIds",void 0),l=r=(0,o._)([(0,p.j)("esri.layers.support.LayerFloorInfo")],l);const u=l},72064:(e,t,s)=>{s.d(t,{h:()=>d});var r=s(80442),o=s(70586),i=s(66677);const n={name:"supportsName",size:"supportsSize",contentType:"supportsContentType",keywords:"supportsKeywords",exifInfo:"supportsExifInfo"};function a(e,t,s){return!!(e&&e.hasOwnProperty(t)?e[t]:s)}function p(e,t,s){return e&&e.hasOwnProperty(t)?e[t]:s}function l(e){const t=e?.supportedSpatialAggregationStatistics?.map((e=>e.toLowerCase()));return{envelope:!!t?.includes("envelopeaggregate"),centroid:!!t?.includes("centroidaggregate"),convexHull:!!t?.includes("convexhullaggregate")}}function u(e,t){const s=e?.supportedOperationsWithCacheHint?.map((e=>e.toLowerCase()));return!!s?.includes(t.toLowerCase())}function d(e,t){return{analytics:c(e),attachment:y(e),data:h(e),metadata:m(e),operations:g(e.capabilities,e,t),query:f(e,t),queryRelated:w(e),queryTopFeatures:v(e),editing:S(e)}}function c(e){return{supportsCacheHint:u(e.advancedQueryCapabilities,"queryAnalytics")}}function y(e){const t=e.attachmentProperties,s={supportsName:!1,supportsSize:!1,supportsContentType:!1,supportsKeywords:!1,supportsExifInfo:!1,supportsCacheHint:u(e.advancedQueryCapabilities,"queryAttachments"),supportsResize:a(e,"supportsAttachmentsResizing",!1)};return t&&Array.isArray(t)&&t.forEach((e=>{const t=n[e.name];t&&(s[t]=!!e.isEnabled)})),s}function h(e){return{isVersioned:a(e,"isDataVersioned",!1),supportsAttachment:a(e,"hasAttachments",!1),supportsM:a(e,"hasM",!1),supportsZ:a(e,"hasZ",!1)}}function m(e){return{supportsAdvancedFieldProperties:a(e,"supportsFieldDescriptionProperty",!1)}}function g(e,t,s){const r=e?e.toLowerCase().split(",").map((e=>e.trim())):[],n=s?(0,i.Qc)(s):null,p=r.includes((0,o.pC)(n)&&"MapServer"===n.serverType?"data":"query"),l=r.includes("editing")&&!t.datesInUnknownTimezone;let u=l&&r.includes("create"),d=l&&r.includes("delete"),c=l&&r.includes("update");const y=r.includes("changetracking"),h=t.advancedQueryCapabilities;return l&&!(u||d||c)&&(u=d=c=!0),{supportsCalculate:a(t,"supportsCalculate",!1),supportsTruncate:a(t,"supportsTruncate",!1),supportsValidateSql:a(t,"supportsValidateSql",!1),supportsAdd:u,supportsDelete:d,supportsEditing:l,supportsChangeTracking:y,supportsQuery:p,supportsQueryAnalytics:a(h,"supportsQueryAnalytic",!1),supportsQueryAttachments:a(h,"supportsQueryAttachments",!1),supportsQueryTopFeatures:a(h,"supportsTopFeaturesQuery",!1),supportsResizeAttachments:a(t,"supportsAttachmentsResizing",!1),supportsSync:r.includes("sync"),supportsUpdate:c,supportsExceedsLimitStatistics:a(t,"supportsExceedsLimitStatistics",!1)}}function f(e,t){const s=e.advancedQueryCapabilities,o=e.ownershipBasedAccessControlForFeatures,n=e.archivingInfo,d=e.currentVersion,c=t?.includes("MapServer"),y=!c||d>=(0,r.Z)("mapserver-pbf-version-support"),h=(0,i.M8)(t),m=new Set((e.supportedQueryFormats??"").split(",").map((e=>e.toLowerCase().trim())));return{supportsStatistics:a(s,"supportsStatistics",e.supportsStatistics),supportsPercentileStatistics:a(s,"supportsPercentileStatistics",!1),supportsSpatialAggregationStatistics:a(s,"supportsSpatialAggregationStatistics",!1),supportedSpatialAggregationStatistics:l(s),supportsCentroid:a(s,"supportsReturningGeometryCentroid",!1),supportsDistance:a(s,"supportsQueryWithDistance",!1),supportsDistinct:a(s,"supportsDistinct",e.supportsAdvancedQueries),supportsExtent:a(s,"supportsReturningQueryExtent",!1),supportsGeometryProperties:a(s,"supportsReturningGeometryProperties",!1),supportsHavingClause:a(s,"supportsHavingClause",!1),supportsOrderBy:a(s,"supportsOrderBy",e.supportsAdvancedQueries),supportsPagination:a(s,"supportsPagination",!1),supportsQuantization:a(e,"supportsCoordinatesQuantization",!1),supportsQuantizationEditMode:a(e,"supportsQuantizationEditMode",!1),supportsQueryGeometry:a(e,"supportsReturningQueryGeometry",!1),supportsResultType:a(s,"supportsQueryWithResultType",!1),supportsMaxRecordCountFactor:a(s,"supportsMaxRecordCountFactor",!1),supportsSqlExpression:a(s,"supportsSqlExpression",!1),supportsStandardizedQueriesOnly:a(e,"useStandardizedQueries",!1),supportsTopFeaturesQuery:a(s,"supportsTopFeaturesQuery",!1),supportsQueryByOthers:a(o,"allowOthersToQuery",!0),supportsHistoricMoment:a(n,"supportsQueryWithHistoricMoment",!1),supportsFormatPBF:y&&m.has("pbf"),supportsDisjointSpatialRelationship:a(s,"supportsDisjointSpatialRel",!1),supportsCacheHint:a(s,"supportsQueryWithCacheHint",!1)||u(s,"query"),supportsDefaultSpatialReference:a(s,"supportsDefaultSR",!1),supportsCompactGeometry:h,supportsFullTextSearch:a(s,"supportsFullTextSearch",!1),maxRecordCountFactor:p(e,"maxRecordCountFactor",void 0),maxRecordCount:p(e,"maxRecordCount",void 0),standardMaxRecordCount:p(e,"standardMaxRecordCount",void 0),tileMaxRecordCount:p(e,"tileMaxRecordCount",void 0)}}function w(e){const t=e.advancedQueryCapabilities,s=a(t,"supportsAdvancedQueryRelated",!1);return{supportsPagination:a(t,"supportsQueryRelatedPagination",!1),supportsCount:s,supportsOrderBy:s,supportsCacheHint:u(t,"queryRelated")}}function v(e){return{supportsCacheHint:u(e.advancedQueryCapabilities,"queryTopFilter")}}function S(e){const t=e.ownershipBasedAccessControlForFeatures;return{supportsGeometryUpdate:a(e,"allowGeometryUpdates",!0),supportsGlobalId:a(e,"supportsApplyEditsWithGlobalIds",!1),supportsReturnServiceEditsInSourceSpatialReference:a(e,"supportsReturnServiceEditsInSourceSR",!1),supportsRollbackOnFailure:a(e,"supportsRollbackOnFailureParameter",!1),supportsUpdateWithoutM:a(e,"allowUpdateWithoutMValues",!1),supportsUploadWithItemId:a(e,"supportsAttachmentsByUploadId",!1),supportsDeleteByAnonymous:a(t,"allowAnonymousToDelete",!0),supportsDeleteByOthers:a(t,"allowOthersToDelete",!0),supportsUpdateByAnonymous:a(t,"allowAnonymousToUpdate",!0),supportsUpdateByOthers:a(t,"allowOthersToUpdate",!0)}}},51706:(e,t,s)=>{var r,o;function i(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function n(e){return null!=e&&!isNaN(e)&&isFinite(e)}function a(e){return e.valueExpression?r.Expression:e.field&&"string"==typeof e.field?r.Field:r.Unknown}function p(e,t){const s=t||a(e),i=e.valueUnit||"unknown";return s===r.Unknown?o.Constant:e.stops?o.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?o.ClampedLinear:"unknown"===i?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?o.Proportional:o.Additive:o.Identity:o.RealWorldSize}s.d(t,{PS:()=>a,QW:()=>p,RY:()=>r,hL:()=>o,iY:()=>i,qh:()=>n}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(r||(r={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(o||(o={}))},28694:(e,t,s)=>{s.d(t,{p:()=>i});var r=s(70586),o=s(69285);function i(e,t,s){if(!s||!s.features||!s.hasZ)return;const i=(0,o.k)(s.geometryType,t,e.outSpatialReference);if(!(0,r.Wi)(i))for(const e of s.features)i(e.geometry)}},56545:(e,t,s)=>{s.d(t,{Z:()=>c});var r,o=s(43697),i=s(96674),n=s(22974),a=s(5600),p=s(75215),l=s(52011),u=s(30556);let d=r=class extends i.wq{constructor(e){super(e),this.attachmentTypes=null,this.attachmentsWhere=null,this.cacheHint=void 0,this.keywords=null,this.globalIds=null,this.name=null,this.num=null,this.objectIds=null,this.returnMetadata=!1,this.size=null,this.start=null,this.where=null}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10}clone(){return new r((0,n.d9)({attachmentTypes:this.attachmentTypes,attachmentsWhere:this.attachmentsWhere,cacheHint:this.cacheHint,keywords:this.keywords,where:this.where,globalIds:this.globalIds,name:this.name,num:this.num,objectIds:this.objectIds,returnMetadata:this.returnMetadata,size:this.size,start:this.start}))}};(0,o._)([(0,a.Cb)({type:[String],json:{write:!0}})],d.prototype,"attachmentTypes",void 0),(0,o._)([(0,a.Cb)({type:String,json:{read:{source:"attachmentsDefinitionExpression"},write:{target:"attachmentsDefinitionExpression"}}})],d.prototype,"attachmentsWhere",void 0),(0,o._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],d.prototype,"cacheHint",void 0),(0,o._)([(0,a.Cb)({type:[String],json:{write:!0}})],d.prototype,"keywords",void 0),(0,o._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"globalIds",void 0),(0,o._)([(0,a.Cb)({json:{write:!0}})],d.prototype,"name",void 0),(0,o._)([(0,a.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],d.prototype,"num",void 0),(0,o._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"objectIds",void 0),(0,o._)([(0,a.Cb)({type:Boolean,json:{default:!1,write:!0}})],d.prototype,"returnMetadata",void 0),(0,o._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"size",void 0),(0,o._)([(0,a.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],d.prototype,"start",void 0),(0,o._)([(0,u.c)("start"),(0,u.c)("num")],d.prototype,"writeStart",null),(0,o._)([(0,a.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],d.prototype,"where",void 0),d=r=(0,o._)([(0,l.j)("esri.rest.support.AttachmentQuery")],d),d.from=(0,p.se)(d);const c=d},74889:(e,t,s)=>{s.d(t,{Z:()=>S});var r,o=s(43697),i=s(66577),n=s(38171),a=s(35454),p=s(96674),l=s(22974),u=s(70586),d=s(5600),c=(s(75215),s(71715)),y=s(52011),h=s(30556),m=s(82971),g=s(33955),f=s(1231);const w=new a.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let v=r=class extends p.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const s=m.Z.fromJSON(t.spatialReference),r=[];for(let t=0;t<e.length;t++){const o=e[t],i=n.Z.fromJSON(o),a=o.geometry&&o.geometry.spatialReference;(0,u.pC)(i.geometry)&&!a&&(i.geometry.spatialReference=s);const p=o.aggregateGeometries,l=i.aggregateGeometries;if(p&&(0,u.pC)(l))for(const e in l){const t=l[e],r=p[e]?.spatialReference;(0,u.pC)(t)&&!r&&(t.spatialReference=s)}r.push(i)}return r}writeGeometryType(e,t,s,r){if(e)return void w.write(e,t,s,r);const{features:o}=this;if(o)for(const e of o)if(e&&(0,u.pC)(e.geometry))return void w.write(e.geometry.type,t,s,r)}readQueryGeometry(e,t){if(!e)return null;const s=!!e.spatialReference,r=(0,g.im)(e);return r&&!s&&t.spatialReference&&(r.spatialReference=m.Z.fromJSON(t.spatialReference)),r}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:s}=this;if(s)for(const e of s)if(e&&(0,u.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new r(this.cloneProperties())}cloneProperties(){return(0,l.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let s=0;s<t.features.length;s++){const r=t.features[s];if(r.geometry){const t=e&&e[s];r.geometry=t&&t.toJSON()||r.geometry}}return t}quantize(e){const{scale:[t,s],translate:[r,o]}=e,i=this.features,n=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-r)/t)),(e=>Math.round((o-e)/s)));for(let e=0,t=i.length;e<t;e++)n?.((0,u.Wg)(i[e].geometry))||(i.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:s}=this;if(!s)return this;const{translate:[r,o],scale:[i,n]}=s,a=this._getHydrationFunction(e,(e=>e*i+r),(e=>o-e*n));for(const{geometry:e}of t)(0,u.pC)(e)&&a&&a(e);return this.transform=null,this}_quantizePoints(e,t,s){let r,o;const i=[];for(let n=0,a=e.length;n<a;n++){const a=e[n];if(n>0){const e=t(a[0]),n=s(a[1]);e===r&&n===o||(i.push([e-r,n-o]),r=e,o=n)}else r=t(a[0]),o=s(a[1]),i.push([r,o])}return i.length>0?i:null}_getQuantizationFunction(e,t,s){return"point"===e?e=>(e.x=t(e.x),e.y=s(e.y),e):"polyline"===e||"polygon"===e?e=>{const r=(0,g.oU)(e)?e.rings:e.paths,o=[];for(let e=0,i=r.length;e<i;e++){const i=r[e],n=this._quantizePoints(i,t,s);n&&o.push(n)}return o.length>0?((0,g.oU)(e)?e.rings=o:e.paths=o,e):null}:"multipoint"===e?e=>{const r=this._quantizePoints(e.points,t,s);return r&&r.length>0?(e.points=r,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,s){return"point"===e?e=>{e.x=t(e.x),e.y=s(e.y)}:"polyline"===e||"polygon"===e?e=>{const r=(0,g.oU)(e)?e.rings:e.paths;let o,i;for(let e=0,n=r.length;e<n;e++){const n=r[e];for(let e=0,r=n.length;e<r;e++){const r=n[e];e>0?(o+=r[0],i+=r[1]):(o=r[0],i=r[1]),r[0]=t(o),r[1]=s(i)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=s(e.ymin),e.xmax=t(e.xmax),e.ymax=s(e.ymax)}:"multipoint"===e?e=>{const r=e.points;let o,i;for(let e=0,n=r.length;e<n;e++){const n=r[e];e>0?(o+=n[0],i+=n[1]):(o=n[0],i=n[1]),n[0]=t(o),n[1]=s(i)}}:null}};(0,o._)([(0,d.Cb)({type:String,json:{write:!0}})],v.prototype,"displayFieldName",void 0),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],v.prototype,"exceededTransferLimit",void 0),(0,o._)([(0,d.Cb)({type:[n.Z],json:{write:!0}})],v.prototype,"features",void 0),(0,o._)([(0,c.r)("features")],v.prototype,"readFeatures",null),(0,o._)([(0,d.Cb)({type:[f.Z],json:{write:!0}})],v.prototype,"fields",void 0),(0,o._)([(0,d.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:w.read}}})],v.prototype,"geometryType",void 0),(0,o._)([(0,h.c)("geometryType")],v.prototype,"writeGeometryType",null),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],v.prototype,"hasM",void 0),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],v.prototype,"hasZ",void 0),(0,o._)([(0,d.Cb)({types:i.qM,json:{write:!0}})],v.prototype,"queryGeometry",void 0),(0,o._)([(0,c.r)("queryGeometry")],v.prototype,"readQueryGeometry",null),(0,o._)([(0,d.Cb)({type:m.Z,json:{write:!0}})],v.prototype,"spatialReference",void 0),(0,o._)([(0,h.c)("spatialReference")],v.prototype,"writeSpatialReference",null),(0,o._)([(0,d.Cb)({json:{write:!0}})],v.prototype,"transform",void 0),v=r=(0,o._)([(0,y.j)("esri.rest.support.FeatureSet")],v),v.prototype.toJSON.isDefaultToJSON=!0;const S=v},58333:(e,t,s)=>{s.d(t,{ET:()=>i,I4:()=>o,eG:()=>p,lF:()=>n,lj:()=>u,qP:()=>a,wW:()=>l});const r=[252,146,31,255],o={type:"esriSMS",style:"esriSMSCircle",size:6,color:r,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},i={type:"esriSLS",style:"esriSLSSolid",width:.75,color:r},n={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},a={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},p={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},l={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},u={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);