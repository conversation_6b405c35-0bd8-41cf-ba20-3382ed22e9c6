<template>
  <div
    class="bottom-box"
    :title="'漏损构成'"
  >
    <div class="chart">
      <VChart
        ref="refChart1"
        :option="两层圆环(lsgcData)"
      ></VChart>
    </div>
    <div class="chart">
      <VChart
        ref="refChart2"
        :option="渐变柱状图(LeakageData)"
      ></VChart>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { 两层圆环, 渐变柱状图 } from '../../operation/charts'
import { lsgcData, LeakageData } from '../../smartDecisionData'
import { useDetector } from '@/hooks/echarts'

const refChart1 = ref()
const refChart2 = ref()
const detector = useDetector()
onMounted(() => {
  detector.listenToMush(document.documentElement, () => {
    refChart1.value?.resize()
    refChart2.value?.resize()
  })
})
</script>
<style lang="scss" scoped>
.bottom-box {
  width: 100%;
  height: 100%;
  .chart {
    width: 100%;
    height: 50%;
  }
}
</style>
