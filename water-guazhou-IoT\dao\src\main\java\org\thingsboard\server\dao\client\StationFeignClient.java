package org.thingsboard.server.dao.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.StationAttrDTO;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.model.sql.WaterPumpEntity;

import java.util.List;

@Controller
@FeignClient(name = "base-service", configuration = {FeignConfig.class})
public interface StationFeignClient {

    @GetMapping("api/station/{id}")
    StationEntity get(@PathVariable("id") String id);

    @GetMapping("api/station/list")
    PageData<StationEntity> list(@RequestParam("page") int page, @RequestParam("size") int size, @RequestParam("type") String type, @RequestParam("projectId") String projectId);

    @GetMapping("api/station/findAttrById")
    StationAttrEntity findAttrById(@RequestParam("attributeId") String attributeId);

    @GetMapping("api/station/findAttrByIdList")
    List<StationAttrEntity> findAttrByIdList(@RequestParam("attributes") String attributes);

    @GetMapping("api/station/attrList")
    List<StationAttrEntity> getAttrList(@RequestParam("stationId") String stationId, @RequestParam("type") String type);

    @GetMapping("api/station/allAttrList")
    List<StationAttrDTO> getAllAttrList(@RequestParam("stationId") String stationId);

    @GetMapping("api/station/stationAttrList")
    List<StationAttrEntity> getStationAllAttrList(@RequestParam("stationId") String stationId);

    @GetMapping("api/station/attrListByStationIds")
    List<StationAttrEntity> getAttrList(@RequestParam("stationId") List<String> stationId, @RequestParam("type") String type);

    @GetMapping("api/station/findByStationIdList")
    List<StationEntity> findByStationIdList(@RequestParam("stationType") String stationType, @RequestParam("stationIdList") List<String> stationIdList);

    @GetMapping("api/station/findByIdList")
    List<StationEntity> findByStationIdList(@RequestParam("stationIdList") String stationIds);
}
