<template>
  <div class="wrapper-content">
    <div class="top">
      <CardSearch
        ref="cardSearch"
        :config="SearchConfig"
      />
      <FormTable
        ref="refTable"
        class="card-table"
        :config="TableConfig"
      ></FormTable>
    </div>
    <div class="bottom">
      <SLCard
        :title="`${props.device?.partitionName}--小流分析图`"
        overlay
        class="chart-lef"
      >
        <div
          ref="echartsDiv"
          class="chart-box t"
        >
          <VChart
            ref="refChart1"
            :option="state.chartOption"
          ></VChart>
        </div>
      </SLCard>
      <SLCard
        :title="`${props.device?.partitionName}--24小时流量分析图`"
        overlay
        class="chart-right"
      >
        <div
          ref="echartsDiv"
          class="chart-box"
        >
          <VChart
            ref="refChart2"
            :option="state.chartOption1"
          ></VChart>
        </div>
      </SLCard>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { maxBy, meanBy, minBy, padStart } from 'lodash-es'
import { IECharts } from '@/plugins/echart'
import { useDetector } from '@/hooks/echarts'
import { useAppStore } from '@/store'
import { formatterDate, formatterMonth, formatterYear } from '@/utils/GlobalHelper'
import { GetDMALossControlDetail, GetDMALossControlFlowAnalys } from '@/api/mapservice/dma'

const emit = defineEmits(['hiddenLoading', 'update:model-value', 'back'])
const props = defineProps<{
  device?: Record<string, any>
}>()
const state = reactive<{
  chartOption: any
  chartOption1: any
}>({
  chartOption: null,
  chartOption1: null
})

const refTable = ref<ICardTableIns>()
const echartsDiv = ref<any>()
const cardSearch = ref<ISearchIns>()

const handleHidden = (params, query, config) => {
  config.hidden = params.type !== config.field
}
// 搜索栏初始化配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    month: moment().format(formatterMonth),
    year: moment().format(formatterYear),
    day: [moment().subtract(1, 'M').format(formatterDate), moment().format(formatterDate)],
    type: 'month'
  },
  filters: [
    {
      type: 'radio-button',
      label: '选择方式',
      field: 'type',
      options: [
        { label: '按年', value: 'year' },
        { label: '按月', value: 'month' },
        { label: '按日期', value: 'day' }
      ]
    },
    {
      handleHidden,
      type: 'month',
      label: '',
      field: 'month'
    },
    {
      handleHidden,
      type: 'year',
      label: '',
      field: 'year'
    },
    {
      handleHidden,
      type: 'daterange',
      label: '',
      field: 'day'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          iconifyIcon: 'ep:search'
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable()
          }
        },
        {
          perm: true,
          type: 'default',
          text: '返回',
          iconifyIcon: 'ep:d-arrow-left',
          click: () => {
            emit('back')
          }
        }
      ]
    }
  ],
  moreFilters: []
})

// 出水列表
const TableConfig = reactive<ITable>({
  loading: false,
  indexVisible: true,
  dataList: [],
  columns: [
    { prop: 'partitionName', label: '日期', minWidth: 120 },
    {
      prop: 'supplyTotal',
      label: '供水量',
      unit: '(m³)',
      minWidth: 120,
      sortable: true,
      formatter(row, value) {
        return value?.toFixed(2)
      }
    },
    {
      prop: 'minFlowTimeHour',
      label: '最小流时间',
      unit: '(h)',
      minWidth: 140,
      sortable: true,
      formatter(row, value) {
        if (row['partitionName'] === '平均') return ' '
        return padStart(value, 2, '0') + ':00'
      }
    },
    {
      prop: 'minFlow',
      label: 'MNF夜间最小流',
      unit: '(m³/h)',
      minWidth: 220,
      sortable: true,
      formatter(row, value) {
        return value?.toFixed(2)
      }
    },
    {
      prop: 'minValueTimeHour',
      label: '最小水量值时间',
      unit: '',
      minWidth: 160,
      sortable: true,
      formatter(row, value) {
        if (row['partitionName'] === '平均') return ' '
        return padStart(value, 2, '0') + ':00'
      }
    },
    {
      prop: 'minValue',
      label: '夜间最小水量值',
      unit: '(m³)',
      minWidth: 210,
      sortable: true,
      formatter(row, value) {
        return value?.toFixed(2)
      }
    },
    {
      prop: 'legalUseWater',
      label: '用户合法用水量',
      unit: '(m³/h)',
      minWidth: 210,
      sortable: true,
      formatter(row, value) {
        return value?.toFixed(2)
      }
    },
    {
      prop: 'netNightFlow',
      label: '净夜间流量',
      unit: '(m³/h)',
      minWidth: 180,
      sortable: true,
      formatter(row, value) {
        return value?.toFixed(2)
      }
    },
    {
      prop: 'lossWater',
      label: '漏失水量',
      unit: '(m³)',
      minWidth: 240,
      sortable: true,
      formatter(row, value) {
        return value?.toFixed(2)
      }
    },
    {
      prop: 'avgDayFlow',
      label: '日均流量',
      unit: '(m³/h)',
      minWidth: 150,
      sortable: true,
      formatter(row, value) {
        return value?.toFixed(2)
      }
    },
    {
      prop: 'unitPipeNightFlowMin',
      label: '单位管线夜间小流',
      unit: '(m³/h/km)',
      minWidth: 240,
      sortable: true,
      formatter(row, value) {
        return value?.toFixed(2)
      }
    },
    {
      prop: 'mnfDivideAvgDayFlow',
      label: 'MNF/日均流量',
      unit: '(%)',
      minWidth: 180,
      sortable: true,
      formatter(row, value) {
        return value?.toFixed(2)
      }
    },
    {
      prop: 'lossValuation',
      label: '漏损评估',
      tag: true,
      tagColor(row, val) {
        return val === '一般' ? '#e6a23c' : val === '较好' ? '#318DFF' : val === '较差' ? '#f56c6c' : ''
      }
    }
  ],
  pagination: {
    hide: true
  },
  handleRowClick(row) {
    if (['最小', '最大', '平均'].indexOf(row['partitionName']) !== -1) return
    TableConfig.currentRow = row
    initDayOption()
  }
})
// 站点历史数据查询
const refreshData = async () => {
  const query = cardSearch.value?.queryParams || {}
  const params = {
    date: query.type === 'month' ? query.month : query.type === 'year' ? query.year : undefined,
    partitionId: props.device?.partitionId,
    type: query.type,
    start: query.type === 'day' ? query.day?.[0] : undefined,
    end: query.type === 'day' ? query.day?.[1] : undefined
  }
  const res = await GetDMALossControlDetail(params)
  const data = res.data?.data || []
  const max = { partitionName: '最小' }
  const min = { partitionName: '最大' }
  const ave = { partitionName: '平均' }
  TableConfig.columns.map(item => {
    if (!item.sortable) return
    max[item.prop] = maxBy(data, (row: any) => row[item.prop])?.[item.prop]
    min[item.prop] = minBy(data, (row: any) => row[item.prop])?.[item.prop]
    ave[item.prop] = meanBy(data, (row: any) => row[item.prop])
    isNaN(ave[item.prop]) && (ave[item.prop] = undefined)
  })
  data.push(min, max, ave)
  TableConfig.dataList = data
  TableConfig.currentRow = TableConfig.dataList[0]
  refreshChart()
}

watch(
  () => props.device,
  () => {
    refreshData()
  }
)
const initMonthOption = () => {
  refChart1.value?.clear()
  const xData = TableConfig.dataList.map(item => item.partitionName) || []
  const data1 = TableConfig.dataList.map(item => item.lossWater?.toFixed(2)) || []
  const data2 = TableConfig.dataList.map(item => item.legalUseWater?.toFixed(2)) || []
  const data3 = TableConfig.dataList.map(item => item.netNightFlow?.toFixed(2)) || []
  const data4 = TableConfig.dataList.map(item => item.minFlow?.toFixed(2)) || []

  const option = {
    title: {
      text: ''
    },
    grid: {
      left: 50,
      right: 50,
      top: 50,
      bottom: 80
    },
    legend: {
      type: 'scroll',
      textStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: xData
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 30
      },
      {
        start: 0,
        end: 10
      }
    ],
    yAxis: [
      {
        position: 'left',
        type: 'value',
        name: '水量(m³)',
        axisLine: {
          show: true,
          lineStyle: {
            // color: '#ffffff' // '#333'
            types: 'solid'
          }
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#656b84' // 更改坐标轴文字颜色
            // fontSize: 14 //更改坐标轴文字大小
          }
        },
        splitLine: {
          lineStyle: {
            color: useAppStore().isDark ? '#303958' : '#ccc',
            type: [5, 10],
            dashOffset: 5
          }
        }
      },
      {
        position: 'right',
        type: 'value',
        name: '流量(m³/h)',
        axisLine: {
          show: true,
          lineStyle: {
            // color: '#ffffff' // '#333'
            types: 'solid'
          }
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#656b84' // 更改坐标轴文字颜色
            // fontSize: 14 //更改坐标轴文字大小
          }
        },
        splitLine: {
          lineStyle: {
            color: useAppStore().isDark ? '#303958' : '#ccc',
            type: [5, 10],
            dashOffset: 5
          }
        }
      }
    ],
    series: [
      {
        name: '漏失水量',
        type: 'bar',
        barWidth: 10,
        data: data1
      },
      {
        name: '合法用水',
        type: 'line',
        smooth: true,
        symboe: 'none',
        barWidth: 10,
        data: data2,
        yAxisIndex: 1
      },
      {
        name: '净夜间流量',
        type: 'line',
        smooth: true,
        symboe: 'none',
        barWidth: 10,
        data: data3,
        yAxisIndex: 1
      },
      {
        name: '夜间最小流',
        type: 'line',
        smooth: true,
        symboe: 'none',
        yAxisIndex: 1,
        data: data4
      }
    ]
  }
  state.chartOption = option
}
const initDayOption = async () => {
  try {
    refChart2.value?.clear()
    const res = await GetDMALossControlFlowAnalys({
      date: TableConfig.currentRow?.partitionName,
      partitionId: props.device?.partitionId
    })
    const time: any[] = []
    const supply: any[] = []
    const flow: any[] = []
    const data = res.data?.data || []
    data.map(item => {
      time.push(item.time)
      supply.push(item.supply)
      flow.push(item.flow)
    })
    const option = {
      title: {
        text: ''
      },
      grid: {
        left: 50,
        right: 50,
        top: 50,
        bottom: 80
      },
      legend: {
        type: 'scroll',
        textStyle: {
          color: '#666',
          fontSize: 12
        }
      },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        data: time || []
      },
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 30
        },
        {
          start: 0,
          end: 10
        }
      ],
      yAxis: [
        {
          position: 'left',
          type: 'value',
          name: '水量(m³)',
          axisLine: {
            show: true,
            lineStyle: {
              // color: '#ffffff' // '#333'
              types: 'solid'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#656b84' // 更改坐标轴文字颜色
              // fontSize: 14 //更改坐标轴文字大小
            }
          },
          splitLine: {
            lineStyle: {
              color: useAppStore().isDark ? '#303958' : '#ccc',
              type: [5, 10],
              dashOffset: 5
            }
          }
        },
        {
          position: 'right',
          type: 'value',
          name: '流量(m³/h)',
          axisLine: {
            show: true,
            lineStyle: {
              // color: '#ffffff' // '#333'
              types: 'solid'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#656b84' // 更改坐标轴文字颜色
              // fontSize: 14 //更改坐标轴文字大小
            }
          },
          splitLine: {
            lineStyle: {
              color: useAppStore().isDark ? '#303958' : '#ccc',
              type: [5, 10],
              dashOffset: 5
            }
          }
        }
      ],
      series: [
        {
          name: '供水量',
          type: 'bar',
          barWidth: 10,
          data: supply || []
        },
        {
          name: '瞬时流量',
          type: 'line',
          smooth: true,
          symboe: 'none',
          barWidth: 10,
          data: flow || [],
          yAxisIndex: 1
        }
      ]
    }
    state.chartOption1 = option
  } catch (error) {
    //
  }
}
const refChart1 = ref<IECharts>()
const refChart2 = ref<IECharts>()
// 加载图表
const refreshChart = async () => {
  initDayOption()
  initMonthOption()
  emit('hiddenLoading')
}
const detector = useDetector()
onMounted(() => {
  refreshData()
  detector.listenToMush(echartsDiv.value, () => {
    refChart1.value?.resize()
    refChart2.value?.resize()
  })
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
  width: 100%;
  .card-table {
    height: calc(50vh - 100px);
    width: 100%;
  }

  .bottom {
    height: calc(50vh - 100px);
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    .chart-box {
      height: 100%;
      width: 100%;
    }

    .chart-lef {
      width: calc(50% - 5px);
    }
    .chart-right {
      width: calc(50% - 5px);
    }
  }
}
</style>
