import{d as m,a8 as v,ab as u,g as _,n as o,aB as d,aJ as b,p as g,q as D,bh as p,i as f,cU as h,C as x}from"./index-r0dFAfgr.js";const y="/static/png/1-<PERSON><PERSON>ej-.png",z="/static/png/10-rzqSLNtM.png",B="/static/png/11-uTkSRJ7r.png",G="/static/png/12-Czl-N3EC.png",L="/static/png/2-o8R9dKVt.png",N="/static/png/3-XTW9-HoE.png",R="/static/png/4-BeXVPhu_.png",S="/static/png/5-9b3Jxoh5.png",k="/static/png/6-GdXTm-mF.png",C="/static/png/7-DlQzyWrY.png",E="/static/png/8-DSuyMeGx.png",U="/static/png/9-DWRz5MRp.png",V={class:"device-group"},w={class:"img"},F={class:"info"},I={class:"count"},J={class:"text"},M=m({__name:"DeviceGroup",props:{devieData:{}},setup(c){const t=n=>{var a;return(a=new URL(Object.assign({"../imgs/1.png":y,"../imgs/10.png":z,"../imgs/11.png":B,"../imgs/12.png":G,"../imgs/2.png":L,"../imgs/3.png":N,"../imgs/4.png":R,"../imgs/5.png":S,"../imgs/6.png":k,"../imgs/7.png":C,"../imgs/8.png":E,"../imgs/9.png":U})[`../imgs/${n}`],import.meta.url))==null?void 0:a.href},e=c,l=v(()=>{var s;const n=u(((s=e.devieData)==null?void 0:s.pipeLength)??0);return{listData:[{img:t("7.png"),value:n.value.toFixed(2)+n.unit,name:"管线长度(米)"},{img:t("8.png"),value:e.devieData.waterQuality||0,name:"水质监测点(个)"},{img:t("9.png"),value:e.devieData.pressure||0,name:"测压点(个)"},{img:t("10.png"),value:e.devieData.bigUser||0,name:"大用户远传(个)"},{img:t("11.png"),value:e.devieData.secondary||0,name:"泵站(个)"},{img:t("12.png"),value:e.devieData.flow||0,name:"测流点(个)"}]}});return(n,s)=>{const a=h;return _(),o("ul",V,[(_(!0),o(d,null,b(f(l).listData,(i,r)=>(_(),o("li",{key:r,class:"device-group-item"},[g("div",w,[D(a,{fit:"contain",src:i.img},null,8,["src"])]),g("div",F,[g("div",I,p(i.value),1),g("div",J,p(i.name),1)])]))),128))])}}}),W=x(M,[["__scopeId","data-v-1d40ffd1"]]);export{W as default};
