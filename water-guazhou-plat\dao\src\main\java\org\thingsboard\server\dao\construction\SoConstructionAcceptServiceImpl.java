package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionAccept;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionAcceptMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionAcceptPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionAcceptSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionTaskInfoSaveRequest;

import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal.SO_CONSTRUCTION_ACCEPT_JOURNAL;
import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope.SO_CONSTRUCTION_ACCEPT;

@Service
public class SoConstructionAcceptServiceImpl extends BasicSoConstructionTaskDriveService<SoConstructionAccept> implements SoConstructionAcceptService {
    @Autowired
    private SoConstructionAcceptMapper mapper;

    @Override
    public IPage<SoConstructionAccept> findAllConditional(SoConstructionAcceptPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoConstructionAccept save(SoConstructionAcceptSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> {
            recordService.recordCreate(entity, entity.getConstructionCode(), SO_CONSTRUCTION_ACCEPT_JOURNAL);
            taskInfoService.save(SoConstructionTaskInfoSaveRequest.of(entity, SO_CONSTRUCTION_ACCEPT, e.getConstructionCode()));
            return mapper.save(e);
        }, mapper::updateFully);
    }

    @Override
    public boolean update(SoConstructionAccept entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean complete(String constructionCode, String userId, String tenantId) {
        boolean success = taskInfoService.markAsComplete(constructionCode, tenantId, getCurrentScope());
        if (success) {
            recordService.recordComplete(tenantId, userId, constructionCode, getCurrentJournalType());
        }
        return success;
    }

    @Override
    public boolean isComplete(String id) {
        return taskInfoService.isComplete(id, getCurrentScope());
    }

    @Override
    public boolean isComplete(String constructionCode, String tenantId) {
        return taskInfoService.isComplete(constructionCode, tenantId, getCurrentScope());
    }

    @Override
    public SoGeneralSystemScope getCurrentScope() {
        return SO_CONSTRUCTION_ACCEPT;
    }

    @Override
    public SoGeneralSystemJournal getCurrentJournalType() {
        return SO_CONSTRUCTION_ACCEPT_JOURNAL;
    }

    @Override
    public BaseMapper<SoConstructionAccept> getDirectMapper() {
        return mapper;
    }
}
