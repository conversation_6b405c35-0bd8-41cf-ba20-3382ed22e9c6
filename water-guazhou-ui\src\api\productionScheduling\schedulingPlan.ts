import request from '@/plugins/axios'

// 调度方案(日常方案)
// 获取调度方案列表
export function getDispatchMethodList(params: {
    name?:string
    type?:string
    dateLabel?:string
    timeFrom?:string
    timeTo?:string
    waterSupplyFrom?:string
    waterSupplyTo?:string
    weatherType?:string
    powerConsumptionFrom?:string
    powerConsumptionTo?:string
    fromTime?:string
    toTime?:string
    page:number|undefined
    size:number|undefined
}) {
  return request({
    url: `/api/sp/dispatchMethod`,
    method: 'GET',
    params
  })
}

// 新增修改调度方案
export function postDispatchMethod(params: {
    id?:string
    name:string
    type:string
    dateLabel:string
    stationId?:string
    time:string
    remark:string
    weatherType?:string
    maxTemperature?:string
    minTemperature?:string
    rainfall?:string
    relativeHumidity?:string
    waterSupply?:string
    powerConsumption?:string
    waterLevel?:string
}) {
  return request({
    url: `/api/sp/dispatchMethod`,
    method: 'POST',
    data: params
  })
}

/** 删除调度方案 */
export function deleteDispatchMethod(id:string) {
  return request({
    url: `/api/sp/dispatchMethod/${id}`,
    method: 'delete'
  })
}

/** 调度方案状态 */
export function postDispatchMethodStatus(id:string, enabled:boolean) {
  return request({
    url: `/api/sp/dispatchMethod/switchEnabled/${id}/${enabled}`,
    method: 'post'
  })
}
