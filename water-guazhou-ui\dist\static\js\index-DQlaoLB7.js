import p from"./index-DNNRpU7M.js";import m from"./pipeNetwork-BqQE7-IR.js";import e from"./pipeNetwork_yintao-BYp38UEF.js";import a from"./pipeNetwork_wudang-DyhXGzxs.js";import{d as s,r as n,g as t,h as o,F as _,i}from"./index-r0dFAfgr.js";import"./index-pS52AAh1.js";import"./index-DAiX9D4h.js";import"./Timer-DD6yFqCB.js";import"./padStart-BKfyZZDO.js";import"./ExitFullScreen-BAhtug-v.js";import"./Weather-C85tAM-i.js";import"./Map-BtiwaWSD.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./FeatureHelper-Da16o0mu.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./arcWidgetButton-0glIxrt7.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./useWidgets-BRE-VQU9.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./useStation-DJgnSZIA.js";import"./zhandian-YaGuQZe6.js";import"./data-CLo2TII-.js";import"./index-BggOjNGp.js";import"./TitleCard-BgReUNwX.js";import"./TitleHeader-CBWfLOPA.js";import"./CXCFX-D3VS1APB.js";import"./useDetector-BRcb7GRN.js";import"./echart-C9Tas6tA.js";import"./statistics-CeyexT_5.js";import"./DeviceGroup-DfMgqr6q.js";import"./DeviceStatic-DPI9NTSP.js";import"./6-4nR55Xef.js";import"./index-CknacZq4.js";import"./FQTJ-bBdN7OyF.js";import"./usePartition-DkcY9fQ2.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./GSGWKJTJ-Bf5Vp2iZ.js";import"./LLJK-DMALGaHM.js";import"./LSPH-DmSHTeYD.js";import"./LightPlat-BpCirFph.js";import"./YLJC-CfYIPw6m.js";import"./index-BlG8PIOK.js";import"./GDLXSLTJ.vue_vue_type_script_setup_true_lang-DM08ggXp.js";import"./index-CpGhZCTT.js";import"./QXGZL.vue_vue_type_script_setup_true_lang-BtNd_TOq.js";import"./XJWCL-BWRNvTUh.js";import"./onemap-CEunQziB.js";import"./SCGY_yintao-aR2fjUFb.js";/* empty css                         */import"./img_8-BTVxQQlz.js";import"./ArcView-DpMnCY82.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./DeviceStatic_wudang-BJtrH5pg.js";const Lo=s({__name:"index",setup(f){const r=n({siteName:window.SITE_CONFIG.SITENAME});return(c,u)=>(t(),o(p,{"show-headers":!0,"show-bars":!1},{default:_(()=>[i(r).siteName==="yintao"?(t(),o(e,{key:0})):i(r).siteName==="wudang"?(t(),o(a,{key:1})):(t(),o(m,{key:2}))]),_:1}))}});export{Lo as default};
