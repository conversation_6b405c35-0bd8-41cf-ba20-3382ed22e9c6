<!-- 统一工单-工单中心-工单管理 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable ref="refTable" class="card-table" :config="TableConfig" />
    <SLDrawer
      ref="refFormChangeDealer"
      :config="FormConfig_ChangeDealer"
    ></SLDrawer>
    <SLDrawer ref="refForm_Stop" :config="FormConfig_Stop"></SLDrawer>
    <SLDrawer ref="refdetail" :config="detailConfig">
      <detail :id="selectedId"></detail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { Download, Refresh } from '@element-plus/icons-vue';
import { onMounted, reactive, ref, shallowRef } from 'vue';
import { SLConfirm, SLMessage } from '@/utils/Message';
import OrderStepTagsVue from './components/OrderStepTags.vue';
import detail from './components/detail.vue';
import {
  getEmergencyLevelOpetions,
  getFromOptions,
  getOrderTypeOptions,
  initOrderTableColumns,
  StatusForDisableStop
} from './config';
import {
  ChangeWorkOrderProcesser,
  GetWorkOrderPage,
  TerminateWorkOrder
} from '@/api/workorder';
import useUser from '@/hooks/user/useUser';
import { formatterDate } from '@/utils/GlobalHelper';
import useStopWorkOrder from './hooks/useStopWorkOrder';

const { getUserOptions } = useUser();
const refSearch = ref<ICardSearchIns>();
const refTable = ref<ICardTableIns>();
const refFormChangeDealer = ref<ISLDrawerIns>();
const refForm_Stop = ref<ISLDrawerIns>();

const refdetail = ref<ISLDrawerIns>();

// 明细弹框
const detailConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  cancel: false,
  className: 'lightColor',
  group: []
});

const selectedId = ref<string>('');

const SearchConfig = reactive<ISearch>({
  filters: [
    { field: 'title', label: '标题', type: 'input' },

    { field: 'date', label: '发起时间', type: 'daterange' },
    {
      field: 'source',
      label: '来源',
      type: 'select',
      options: getFromOptions()
    },
    {
      field: 'level',
      label: '紧急程度',
      type: 'select',
      options: getEmergencyLevelOpetions()
    },
    {
      field: 'type',
      label: '类型',
      type: 'select-tree',
      options: getOrderTypeOptions()
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          text: '查询',
          icon: 'iconfont icon-chaxun',
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => resetForm()
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          svgIcon: shallowRef(Download),
          click: () => exportTable()
        }
      ]
    }
  ],
  handleSearch: () => refreshData(),
  defaultParams: {
    date: [
      moment().subtract(1, 'M').format(formatterDate),
      moment().format(formatterDate)
    ]
  }
});
const TableConfig = reactive<ICardTable>({
  expandable: true,
  indexVisible: true,
  defaultExpandAll: true,
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows;
  },
  expandComponent: shallowRef(OrderStepTagsVue),
  columns: initOrderTableColumns(),
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  },
  operationWidth: 220,
  operations: [
    {
      perm: true,
      text: '详情',
      isTextBtn: true,
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      text: '变更处理人',
      isTextBtn: true,
      disabled: (row: any): boolean => {
        return (
          !row.status ||
          ['PENDING', 'ASSIGN', 'APPROVED', 'TERMINATED', 'SUBMIT'].indexOf(
            row.status
          ) !== -1
        );
      },
      click: (row) => {
        TableConfig.currentRow = row;
        refFormChangeDealer.value?.openDrawer();
      }
    },
    {
      perm: true,
      text: '终止',
      isTextBtn: true,
      disabled: (row: any): boolean =>
        StatusForDisableStop.includes(row.status),
      click: (row) => {
        TableConfig.currentRow = row;
        refForm_Stop.value?.openDrawer();
      }
    }
  ]
});
const FormConfig_ChangeDealer = reactive<IDrawerConfig>({
  width: 500,
  title: '变更处理人',
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '新的处理人：',
          field: 'stepProcessUserId',
          rules: [{ required: true, message: '请选择新的处理人' }],
          options: []
        }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定变更？', '提示信息')
      .then(async () => {
        FormConfig_ChangeDealer.submitting = true;
        try {
          const res = await ChangeWorkOrderProcesser(
            TableConfig.currentRow.id,
            params
          );
          if (res.data.code === 200) {
            SLMessage.success('操作成功');
            refreshData();
            refFormChangeDealer.value?.closeDrawer();
          } else {
            SLMessage.error(res.data.err || '操作失败');
          }
        } catch (error) {
          SLMessage.error('系统错误');
        }
        FormConfig_ChangeDealer.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const { FormConfig_Stop } = useStopWorkOrder(async (params: any) => {
  const res = await TerminateWorkOrder(TableConfig.currentRow?.id, params);
  if (res.data.code === 200) {
    SLMessage.success('操作成功');
    refreshData();
    refForm_Stop.value?.closeDrawer();
  } else {
    SLMessage.error(res.data.err || '操作失败');
  }
});
const handleDetail = (row: any) => {
  selectedId.value = row.id || '';
  detailConfig.title = row.serialNo;
  refdetail.value?.openDrawer();
  // router.push({
  //   name: 'WorkOrderDetail',
  //   query: {
  //     id: row.id
  //   }
  // })
};
// const handleHide = (row: any) => {
//   SLConfirm('确定隐藏？', '提示信息')
//     .then(() => {
//       row.hide = true
//     })
//     .catch(() => {
//       //
//     })
// }

// const handleStop = (row: any) => {
//   SLConfirm('确定终止？', '提示信息')
//     .then(() => {
//       row.status = 'stop'
//     })
//     .catch(() => {
//       //
//     })
// }
const refreshData = async () => {
  TableConfig.loading = true;
  try {
    const query = refSearch.value?.queryParams || {};
    const [fromTime, toTime] =
      query.date?.length === 2
        ? [
            moment(query.date[0], formatterDate).valueOf(),
            moment(query.date[1], formatterDate).endOf('D').valueOf()
          ]
        : [
            moment().subtract(1, 'M').startOf('D').valueOf(),
            moment().endOf('D').valueOf()
          ];
    const params: any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query,
      fromTime,
      toTime
    };
    delete params.date;
    const res = await GetWorkOrderPage(params);
    const data = res.data?.data;
    TableConfig.dataList = data.data;
    TableConfig.pagination.total = data.total;
  } catch (error) {
    //
  }
  TableConfig.loading = false;
};
const resetForm = () => {
  // SearchConfig.defaultParams = {}
  refSearch.value?.resetForm();
  refreshData();
};
const exportTable = () => {
  refTable.value?.exportTable();
};
const initUserOption = async () => {
  const res = await getUserOptions(false, { authority: 'CUSTOMER_USER' });
  const field = FormConfig_ChangeDealer.group[0].fields[0] as IFormSelect;
  field.options = res;
};
onMounted(() => {
  initUserOption();
  refreshData();
});
// const
</script>
<style lang="scss"></style>
