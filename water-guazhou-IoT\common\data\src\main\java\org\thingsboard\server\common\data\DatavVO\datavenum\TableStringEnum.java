/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.DatavVO.datavenum;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum TableStringEnum {
    ALARM_COUNT("报警统计"),
    ALARM_TOTAL("总报警数"),
    ACTIVE_ACK("已解除"),
    CLEARED_ACK("未解除"),
    TABLE_STATISTICS("报警统计"),
    TABLE_VALUE("数值")
    ;
    private String str;

    public String getStr() {
        return str;
    }

    public void setStr(String str) {
        this.str = str;
    }
}
