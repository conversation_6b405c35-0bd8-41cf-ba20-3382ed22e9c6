<template>
  <template v-for="(item, index) in btns" :key="index">
    <div v-if="item.type === 'interval'" class="interval"></div>
    <el-tooltip
      v-else
      class="box-item"
      effect="dark"
      :content="item.title"
      placement="top"
    >
      <el-button text @click="item.onclick && item.onclick(item)">
        <Icon :icon="item.icon" style="font-size: 20px"></Icon>
      </el-button>
    </el-tooltip>
  </template>
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue';

const emit = defineEmits(['changeFullScreen', 'setView', 'CloseAll']);

const btns = ref<
  { title?: string; onclick?: Function; icon: string; type?: string }[]
>([
  {
    title: '全部关闭',
    onclick: (val) => {
      emit('CloseAll');
    },
    icon: 'material-symbols:tab-close'
  },
  {
    type: 'interval',
    icon: ''
  },
  {
    title: '一分屏',
    onclick: (val) => {
      emit('setView', 24, 1);
    },
    icon: 'ph:number-square-one-fill'
  },
  {
    title: '四分屏',
    onclick: (val) => {
      emit('setView', 12, 4);
    },
    icon: 'ph:number-square-four-fill'
  },
  {
    title: '九分屏',
    onclick: (val) => {
      emit('setView', 8, 9);
    },
    icon: 'ph:number-square-nine-fill'
  },
  {
    type: 'interval',
    icon: ''
  },
  {
    title: '最大化',
    onclick: (val) => {
      emit('changeFullScreen');
    },
    icon: 'tabler:arrows-maximize'
  }
]);
</script>

<style lang="scss" scoped>
.btns {
  display: flex;
  align-items: center;
}

.el-button {
  margin: 0px;
  height: 100%;
}

.interval {
  width: 1px;
  height: 80%;
  background-color: rgb(173, 173, 173);
}
</style>
