"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9971],{92835:(e,t,s)=>{s.d(t,{Z:()=>m});var i,r=s(43697),l=s(96674),n=s(70586),o=s(35463),a=s(5600),h=(s(75215),s(67676),s(71715)),u=s(52011),c=s(30556);let p=i=class extends l.wq{static get allTime(){return d}static get empty(){return f}constructor(e){super(e),this.end=null,this.start=null}readEnd(e,t){return null!=t.end?new Date(t.end):null}writeEnd(e,t){t.end=e?e.getTime():null}get isAllTime(){return this.equals(i.allTime)}get isEmpty(){return this.equals(i.empty)}readStart(e,t){return null!=t.start?new Date(t.start):null}writeStart(e,t){t.start=e?e.getTime():null}clone(){return new i({end:this.end,start:this.start})}equals(e){if(!e)return!1;const t=(0,n.pC)(this.start)?this.start.getTime():this.start,s=(0,n.pC)(this.end)?this.end.getTime():this.end,i=(0,n.pC)(e.start)?e.start.getTime():e.start,r=(0,n.pC)(e.end)?e.end.getTime():e.end;return t===i&&s===r}expandTo(e){if(this.isEmpty||this.isAllTime)return this.clone();const t=(0,n.yw)(this.start,(t=>(0,o.JE)(t,e))),s=(0,n.yw)(this.end,(t=>{const s=(0,o.JE)(t,e);return t.getTime()===s.getTime()?s:(0,o.Nm)(s,1,e)}));return new i({start:t,end:s})}intersection(e){if(!e)return this.clone();if(this.isEmpty||e.isEmpty)return i.empty;if(this.isAllTime)return e.clone();if(e.isAllTime)return this.clone();const t=(0,n.R2)(this.start,-1/0,(e=>e.getTime())),s=(0,n.R2)(this.end,1/0,(e=>e.getTime())),r=(0,n.R2)(e.start,-1/0,(e=>e.getTime())),l=(0,n.R2)(e.end,1/0,(e=>e.getTime()));let o,a;if(r>=t&&r<=s?o=r:t>=r&&t<=l&&(o=t),s>=r&&s<=l?a=s:l>=t&&l<=s&&(a=l),null!=o&&null!=a&&!isNaN(o)&&!isNaN(a)){const e=new i;return e.start=o===-1/0?null:new Date(o),e.end=a===1/0?null:new Date(a),e}return i.empty}offset(e,t){if(this.isEmpty||this.isAllTime)return this.clone();const s=new i,{start:r,end:l}=this;return(0,n.pC)(r)&&(s.start=(0,o.Nm)(r,e,t)),(0,n.pC)(l)&&(s.end=(0,o.Nm)(l,e,t)),s}union(e){if(!e||e.isEmpty)return this.clone();if(this.isEmpty)return e.clone();if(this.isAllTime||e.isAllTime)return d.clone();const t=(0,n.pC)(this.start)&&(0,n.pC)(e.start)?new Date(Math.min(this.start.getTime(),e.start.getTime())):null,s=(0,n.pC)(this.end)&&(0,n.pC)(e.end)?new Date(Math.max(this.end.getTime(),e.end.getTime())):null;return new i({start:t,end:s})}};(0,r._)([(0,a.Cb)({type:Date,json:{write:{allowNull:!0}}})],p.prototype,"end",void 0),(0,r._)([(0,h.r)("end")],p.prototype,"readEnd",null),(0,r._)([(0,c.c)("end")],p.prototype,"writeEnd",null),(0,r._)([(0,a.Cb)({readOnly:!0,json:{read:!1}})],p.prototype,"isAllTime",null),(0,r._)([(0,a.Cb)({readOnly:!0,json:{read:!1}})],p.prototype,"isEmpty",null),(0,r._)([(0,a.Cb)({type:Date,json:{write:{allowNull:!0}}})],p.prototype,"start",void 0),(0,r._)([(0,h.r)("start")],p.prototype,"readStart",null),(0,r._)([(0,c.c)("start")],p.prototype,"writeStart",null),p=i=(0,r._)([(0,u.j)("esri.TimeExtent")],p);const d=new p,f=new p({start:void 0,end:void 0}),m=p},5732:(e,t,s)=>{s.d(t,{c:()=>i});var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},46791:(e,t,s)=>{s.d(t,{Z:()=>x});var i,r=s(43697),l=s(3894),n=s(32448),o=s(22974),a=s(70586),h=s(71143);!function(e){e[e.ADD=1]="ADD",e[e.REMOVE=2]="REMOVE",e[e.MOVE=4]="MOVE"}(i||(i={}));var u,c=s(1654),p=s(5600),d=s(75215),f=s(52421),m=s(52011),g=s(58971),v=s(10661);const y=new h.Z(class{constructor(){this.target=null,this.cancellable=!1,this.defaultPrevented=!1,this.item=void 0,this.type=void 0}preventDefault(){this.cancellable&&(this.defaultPrevented=!0)}reset(e){this.defaultPrevented=!1,this.item=e}},void 0,(e=>{e.item=null,e.target=null,e.defaultPrevented=!1,e.cancellable=!1})),_=()=>{};function b(e){return e?e instanceof Z?e.toArray():e.length?Array.prototype.slice.apply(e):[]:[]}function w(e){if(e&&e.length)return e[0]}function C(e,t,s,i){const r=Math.min(e.length-s,t.length-i);let l=0;for(;l<r&&e[s+l]===t[i+l];)l++;return l}function E(e,t,s,i){t&&t.forEach(((t,r,l)=>{e.push(t),E(e,s.call(i,t,r,l),s,i)}))}const T=new Set,D=new Set,I=new Set,M=new Map;let O=0,Z=u=class extends n.Z.EventedAccessor{static isCollection(e){return null!=e&&e instanceof u}constructor(e){super(e),this._chgListeners=[],this._notifications=null,this._timer=null,this._observable=new v.s,this.length=0,this._items=[],Object.defineProperty(this,"uid",{value:O++})}normalizeCtorArgs(e){return e?Array.isArray(e)||e instanceof u?{items:e}:e:{}}destroy(){this.removeAll()}*[Symbol.iterator](){yield*this.items}get items(){return(0,g.it)(this._observable),this._items}set items(e){this._emitBeforeChanges(i.ADD)||(this._splice(0,this.length,b(e)),this._emitAfterChanges(i.ADD))}hasEventListener(e){return"change"===e?this._chgListeners.length>0:this._emitter.hasEventListener(e)}on(e,t){if("change"===e){const e=this._chgListeners,s={removed:!1,callback:t};return e.push(s),this._notifications&&this._notifications.push({listeners:e.slice(),items:this._items.slice(),changes:[]}),{remove(){this.remove=_,s.removed=!0,e.splice(e.indexOf(s),1)}}}return this._emitter.on(e,t)}once(e,t){const s=this.on(e,t);return{remove(){s.remove()}}}add(e,t){if((0,g.it)(this._observable),this._emitBeforeChanges(i.ADD))return this;const s=this.getNextIndex(t??null);return this._splice(s,0,[e]),this._emitAfterChanges(i.ADD),this}addMany(e,t=this._items.length){if((0,g.it)(this._observable),!e||!e.length)return this;if(this._emitBeforeChanges(i.ADD))return this;const s=this.getNextIndex(t);return this._splice(s,0,b(e)),this._emitAfterChanges(i.ADD),this}at(e){if((0,g.it)(this._observable),(e=Math.trunc(e)||0)<0&&(e+=this.length),!(e<0||e>=this.length))return this._items[e]}removeAll(){if((0,g.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return[];const e=this._splice(0,this.length)||[];return this._emitAfterChanges(i.REMOVE),e}clone(){return(0,g.it)(this._observable),this._createNewInstance({items:this._items.map(o.d9)})}concat(...e){(0,g.it)(this._observable);const t=e.map(b);return this._createNewInstance({items:this._items.concat(...t)})}drain(e,t){if((0,g.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return;const s=(0,a.j0)(this._splice(0,this.length)),r=s.length;for(let i=0;i<r;i++)e.call(t,s[i],i,s);this._emitAfterChanges(i.REMOVE)}every(e,t){return(0,g.it)(this._observable),this._items.every(e,t)}filter(e,t){let s;return(0,g.it)(this._observable),s=2===arguments.length?this._items.filter(e,t):this._items.filter(e),this._createNewInstance({items:s})}find(e,t){return(0,g.it)(this._observable),this._items.find(e,t)}findIndex(e,t){return(0,g.it)(this._observable),this._items.findIndex(e,t)}flatten(e,t){(0,g.it)(this._observable);const s=[];return E(s,this,e,t),new u(s)}forEach(e,t){return(0,g.it)(this._observable),this._items.forEach(e,t)}getItemAt(e){return(0,g.it)(this._observable),this._items[e]}getNextIndex(e){(0,g.it)(this._observable);const t=this.length;return(e=e??t)<0?e=0:e>t&&(e=t),e}includes(e,t=0){return(0,g.it)(this._observable),this._items.includes(e,t)}indexOf(e,t=0){return(0,g.it)(this._observable),this._items.indexOf(e,t)}join(e=","){return(0,g.it)(this._observable),this._items.join(e)}lastIndexOf(e,t=this.length-1){return(0,g.it)(this._observable),this._items.lastIndexOf(e,t)}map(e,t){(0,g.it)(this._observable);const s=this._items.map(e,t);return new u({items:s})}reorder(e,t=this.length-1){(0,g.it)(this._observable);const s=this.indexOf(e);if(-1!==s){if(t<0?t=0:t>=this.length&&(t=this.length-1),s!==t){if(this._emitBeforeChanges(i.MOVE))return e;this._splice(s,1),this._splice(t,0,[e]),this._emitAfterChanges(i.MOVE)}return e}}pop(){if((0,g.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return;const e=w(this._splice(this.length-1,1));return this._emitAfterChanges(i.REMOVE),e}push(...e){return(0,g.it)(this._observable),this._emitBeforeChanges(i.ADD)||(this._splice(this.length,0,e),this._emitAfterChanges(i.ADD)),this.length}reduce(e,t){(0,g.it)(this._observable);const s=this._items;return 2===arguments.length?s.reduce(e,t):s.reduce(e)}reduceRight(e,t){(0,g.it)(this._observable);const s=this._items;return 2===arguments.length?s.reduceRight(e,t):s.reduceRight(e)}remove(e){return(0,g.it)(this._observable),this.removeAt(this.indexOf(e))}removeAt(e){if((0,g.it)(this._observable),e<0||e>=this.length||this._emitBeforeChanges(i.REMOVE))return;const t=w(this._splice(e,1));return this._emitAfterChanges(i.REMOVE),t}removeMany(e){if((0,g.it)(this._observable),!e||!e.length||this._emitBeforeChanges(i.REMOVE))return[];const t=e instanceof u?e.toArray():e,s=this._items,r=[],l=t.length;for(let e=0;e<l;e++){const i=t[e],l=s.indexOf(i);if(l>-1){const i=1+C(t,s,e+1,l+1),n=this._splice(l,i);n&&n.length>0&&r.push.apply(r,n),e+=i-1}}return this._emitAfterChanges(i.REMOVE),r}reverse(){if((0,g.it)(this._observable),this._emitBeforeChanges(i.MOVE))return this;const e=this._splice(0,this.length);return e&&(e.reverse(),this._splice(0,0,e)),this._emitAfterChanges(i.MOVE),this}shift(){if((0,g.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return;const e=w(this._splice(0,1));return this._emitAfterChanges(i.REMOVE),e}slice(e=0,t=this.length){return(0,g.it)(this._observable),this._createNewInstance({items:this._items.slice(e,t)})}some(e,t){return(0,g.it)(this._observable),this._items.some(e,t)}sort(e){if((0,g.it)(this._observable),!this.length||this._emitBeforeChanges(i.MOVE))return this;const t=(0,a.j0)(this._splice(0,this.length));return arguments.length?t.sort(e):t.sort(),this._splice(0,0,t),this._emitAfterChanges(i.MOVE),this}splice(e,t,...s){(0,g.it)(this._observable);const r=(t?i.REMOVE:0)|(s.length?i.ADD:0);if(this._emitBeforeChanges(r))return[];const l=this._splice(e,t,s)||[];return this._emitAfterChanges(r),l}toArray(){return(0,g.it)(this._observable),this._items.slice()}toJSON(){return(0,g.it)(this._observable),this.toArray()}toLocaleString(){return(0,g.it)(this._observable),this._items.toLocaleString()}toString(){return(0,g.it)(this._observable),this._items.toString()}unshift(...e){return(0,g.it)(this._observable),!e.length||this._emitBeforeChanges(i.ADD)||(this._splice(0,0,e),this._emitAfterChanges(i.ADD)),this.length}_createNewInstance(e){return new this.constructor(e)}_splice(e,t,s){const i=this._items,r=this.itemType;let l,n;if(!this._notifications&&this.hasEventListener("change")&&(this._notifications=[{listeners:this._chgListeners.slice(),items:this._items.slice(),changes:[]}],this._timer&&this._timer.remove(),this._timer=(0,c.Os)((()=>this._dispatchChange()))),t){if(n=i.splice(e,t),this.hasEventListener("before-remove")){const t=y.acquire();t.target=this,t.cancellable=!0;for(let s=0,r=n.length;s<r;s++)l=n[s],t.reset(l),this.emit("before-remove",t),t.defaultPrevented&&(n.splice(s,1),i.splice(e,0,l),e+=1,s-=1,r-=1);y.release(t)}if(this.length=this._items.length,this.hasEventListener("after-remove")){const e=y.acquire();e.target=this,e.cancellable=!1;const t=n.length;for(let s=0;s<t;s++)e.reset(n[s]),this.emit("after-remove",e);y.release(e)}}if(s&&s.length){if(r){const e=[];for(const t of s){const s=r.ensureType(t);null==s&&null!=t||e.push(s)}s=e}const t=this.hasEventListener("before-add"),l=this.hasEventListener("after-add"),n=e===this.length;if(t||l){const r=y.acquire();r.target=this,r.cancellable=!0;const o=y.acquire();o.target=this,o.cancellable=!1;for(const a of s)t?(r.reset(a),this.emit("before-add",r),r.defaultPrevented||(n?i.push(a):i.splice(e++,0,a),this._set("length",i.length),l&&(o.reset(a),this.emit("after-add",o)))):(n?i.push(a):i.splice(e++,0,a),this._set("length",i.length),o.reset(a),this.emit("after-add",o));y.release(o),y.release(r)}else{if(n)for(const e of s)i.push(e);else i.splice(e,0,...s);this._set("length",i.length)}}return(s&&s.length||n&&n.length)&&this._notifyChangeEvent(s,n),n}_emitBeforeChanges(e){let t=!1;if(this.hasEventListener("before-changes")){const s=y.acquire();s.target=this,s.cancellable=!0,s.type=e,this.emit("before-changes",s),t=s.defaultPrevented,y.release(s)}return t}_emitAfterChanges(e){if(this.hasEventListener("after-changes")){const t=y.acquire();t.target=this,t.cancellable=!1,t.type=e,this.emit("after-changes",t),y.release(t)}this._observable.notify()}_notifyChangeEvent(e,t){this.hasEventListener("change")&&this._notifications&&this._notifications[this._notifications.length-1].changes.push({added:e,removed:t})}_dispatchChange(){if(this._timer&&(this._timer.remove(),this._timer=null),!this._notifications)return;const e=this._notifications;this._notifications=null;for(const t of e){const e=t.changes;T.clear(),D.clear(),I.clear();for(const{added:t,removed:s}of e){if(t)if(0===I.size&&0===D.size)for(const e of t)T.add(e);else for(const e of t)D.has(e)?(I.add(e),D.delete(e)):I.has(e)||T.add(e);if(s)if(0===I.size&&0===T.size)for(const e of s)D.add(e);else for(const e of s)T.has(e)?T.delete(e):(I.delete(e),D.add(e))}const s=l.Z.acquire();T.forEach((e=>{s.push(e)}));const i=l.Z.acquire();D.forEach((e=>{i.push(e)}));const r=this._items,n=t.items,o=l.Z.acquire();if(I.forEach((e=>{n.indexOf(e)!==r.indexOf(e)&&o.push(e)})),t.listeners&&(s.length||i.length||o.length)){const e={target:this,added:s,removed:i,moved:o},r=t.listeners.length;for(let s=0;s<r;s++){const i=t.listeners[s];i.removed||i.callback.call(this,e)}}l.Z.release(s),l.Z.release(i),l.Z.release(o)}T.clear(),D.clear(),I.clear()}};Z.ofType=e=>{if(!e)return u;if(M.has(e))return M.get(e);let t=null;if("function"==typeof e)t=e.prototype.declaredClass;else if(e.base)t=e.base.prototype.declaredClass;else for(const s in e.typeMap){const i=e.typeMap[s].prototype.declaredClass;t?t+=` | ${i}`:t=i}let s=class extends u{};return(0,r._)([(0,f.c)({Type:e,ensureType:"function"==typeof e?(0,d.se)(e):(0,d.N7)(e)})],s.prototype,"itemType",void 0),s=(0,r._)([(0,m.j)(`esri.core.Collection<${t}>`)],s),M.set(e,s),s},(0,r._)([(0,p.Cb)()],Z.prototype,"length",void 0),(0,r._)([(0,p.Cb)()],Z.prototype,"items",null),Z=u=(0,r._)([(0,m.j)("esri.core.Collection")],Z);const x=Z},52421:(e,t,s)=>{function i(e){return(t,s)=>{t[s]=e}}s.d(t,{c:()=>i})},35463:(e,t,s)=>{s.d(t,{JE:()=>n,Nm:()=>l,rJ:()=>o}),s(80442);const i={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},r={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function l(e,t,s){const i=new Date(e.getTime());if(t&&s){const e=r[s],{getter:l,setter:n,multiplier:o}=e;if("months"===s){const e=function(e,t){const s=new Date(e,t+1,1);return s.setDate(0),s.getDate()}(i.getFullYear(),i.getMonth()+t);i.getDate()>e&&i.setDate(e)}i[n](i[l]()+t*o)}return i}function n(e,t){switch(t){case"milliseconds":return new Date(e.getTime());case"seconds":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case"minutes":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case"hours":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case"days":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case"weeks":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case"months":return new Date(e.getFullYear(),e.getMonth(),1);case"years":return new Date(e.getFullYear(),0,1);case"decades":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case"centuries":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function o(e,t,s){return 0===e?0:e*i[t]/i[s]}},16199:(e,t,s)=>{s.r(t),s.d(t,{default:()=>O});var i,r=s(43697),l=(s(66577),s(3172)),n=s(20102),o=s(16453),a=s(19153),h=s(17452),u=s(5600),c=(s(75215),s(67676),s(71715)),p=s(52011),d=s(30556),f=s(87085),m=s(71612),g=s(38009),v=s(16859),y=s(34760),_=s(72965),b=s(90082),w=s(39450),C=s(11145),E=s(5833),T=s(6570),D=s(82971),I=s(94139);let M=i=class extends((0,m.h)((0,y.Q)((0,_.M)((0,g.q)((0,v.I)((0,o.R)(f.Z))))))){constructor(...e){super(...e),this.copyright="",this.fullExtent=new T.Z(-20037508.342787,-20037508.34278,20037508.34278,20037508.342787,D.Z.WebMercator),this.legendEnabled=!1,this.isReference=null,this.popupEnabled=!1,this.spatialReference=D.Z.WebMercator,this.subDomains=null,this.tileInfo=new C.Z({size:[256,256],dpi:96,format:"png8",compressionQuality:0,origin:new I.Z({x:-20037508.342787,y:20037508.342787,spatialReference:D.Z.WebMercator}),spatialReference:D.Z.WebMercator,lods:[new w.Z({level:0,scale:591657527.591555,resolution:156543.033928}),new w.Z({level:1,scale:295828763.795777,resolution:78271.5169639999}),new w.Z({level:2,scale:147914381.897889,resolution:39135.7584820001}),new w.Z({level:3,scale:73957190.948944,resolution:19567.8792409999}),new w.Z({level:4,scale:36978595.474472,resolution:9783.93962049996}),new w.Z({level:5,scale:18489297.737236,resolution:4891.96981024998}),new w.Z({level:6,scale:9244648.868618,resolution:2445.98490512499}),new w.Z({level:7,scale:4622324.434309,resolution:1222.99245256249}),new w.Z({level:8,scale:2311162.217155,resolution:611.49622628138}),new w.Z({level:9,scale:1155581.108577,resolution:305.748113140558}),new w.Z({level:10,scale:577790.554289,resolution:152.874056570411}),new w.Z({level:11,scale:288895.277144,resolution:76.4370282850732}),new w.Z({level:12,scale:144447.638572,resolution:38.2185141425366}),new w.Z({level:13,scale:72223.819286,resolution:19.1092570712683}),new w.Z({level:14,scale:36111.909643,resolution:9.55462853563415}),new w.Z({level:15,scale:18055.954822,resolution:4.77731426794937}),new w.Z({level:16,scale:9027.977411,resolution:2.38865713397468}),new w.Z({level:17,scale:4513.988705,resolution:1.19432856685505}),new w.Z({level:18,scale:2256.994353,resolution:.597164283559817}),new w.Z({level:19,scale:1128.497176,resolution:.298582141647617}),new w.Z({level:20,scale:564.248588,resolution:.14929107082380833}),new w.Z({level:21,scale:282.124294,resolution:.07464553541190416}),new w.Z({level:22,scale:141.062147,resolution:.03732276770595208}),new w.Z({level:23,scale:70.5310735,resolution:.01866138385297604})]}),this.type="web-tile",this.urlTemplate=null,this.wmtsInfo=null}normalizeCtorArgs(e,t){return"string"==typeof e?{urlTemplate:e,...t}:e}load(e){const t=this.loadFromPortal({supportedTypes:["WMTS"]},e).then((()=>{let e="";if(this.urlTemplate)if(this.spatialReference.equals(this.tileInfo.spatialReference)){const t=new h.R9(this.urlTemplate);!(this.subDomains&&this.subDomains.length>0)&&t.authority?.includes("{subDomain}")&&(e="is missing 'subDomains' property")}else e="spatialReference must match tileInfo.spatialReference";else e="is missing the required 'urlTemplate' property value";if(e)throw new n.Z("web-tile-layer:load",`WebTileLayer (title: '${this.title}', id: '${this.id}') ${e}`)}));return this.addResolvingPromise(t),Promise.resolve(this)}get levelValues(){const e=[];if(!this.tileInfo)return null;for(const t of this.tileInfo.lods)e[t.level]=t.levelValue||t.level;return e}readSpatialReference(e,t){return e||t.fullExtent&&t.fullExtent.spatialReference&&D.Z.fromJSON(t.fullExtent.spatialReference)}get tileServers(){if(!this.urlTemplate)return null;const e=[],{urlTemplate:t,subDomains:s}=this,i=new h.R9(t),r=i.scheme?i.scheme+"://":"//",l=r+i.authority+"/",n=i.authority;if(n?.includes("{subDomain}")){if(s&&s.length>0&&n.split(".").length>1)for(const t of s)e.push(r+n.replace(/\{subDomain\}/gi,t)+"/")}else e.push(l);return e.map((e=>("/"!==e.charAt(e.length-1)&&(e+="/"),e)))}get urlPath(){if(!this.urlTemplate)return null;const e=this.urlTemplate,t=new h.R9(e),s=(t.scheme?t.scheme+"://":"//")+t.authority+"/";return e.substring(s.length)}readUrlTemplate(e,t){return e||t.templateUrl}writeUrlTemplate(e,t){e&&(0,h.oC)(e)&&(e="https:"+e),e&&(e=e.replace(/\{z\}/gi,"{level}").replace(/\{x\}/gi,"{col}").replace(/\{y\}/gi,"{row}"),e=(0,h.Fv)(e)),t.templateUrl=e}fetchTile(e,t,s,i={}){const{signal:r}=i,n=this.getTileUrl(e,t,s),o={responseType:"image",signal:r,query:{...this.refreshParameters}};return(0,l.default)(n,o).then((e=>e.data))}async fetchImageBitmapTile(e,t,s,r={}){const{signal:o}=r;if(this.fetchTile!==i.prototype.fetchTile){const i=await this.fetchTile(e,t,s,r);try{return createImageBitmap(i)}catch(i){throw new n.Z("request:server",`Unable to load tile ${e}/${t}/${s}`,{error:i,level:e,row:t,col:s})}}const a=this.getTileUrl(e,t,s),h={responseType:"blob",signal:o,query:{...this.refreshParameters}},{data:u}=await(0,l.default)(a,h);return(0,b.g)(u,a)}getTileUrl(e,t,s){const{levelValues:i,tileServers:r,urlPath:l}=this;if(!i||!r||!l)return"";const n=i[e];return r[t%r.length]+(0,a.gx)(l,{level:n,z:n,col:s,x:s,row:t,y:t})}};(0,r._)([(0,u.Cb)({type:String,value:"",json:{write:!0}})],M.prototype,"copyright",void 0),(0,r._)([(0,u.Cb)({type:T.Z,json:{write:!0},nonNullable:!0})],M.prototype,"fullExtent",void 0),(0,r._)([(0,u.Cb)({readOnly:!0,json:{read:!1,write:!1}})],M.prototype,"legendEnabled",void 0),(0,r._)([(0,u.Cb)({type:["show","hide"]})],M.prototype,"listMode",void 0),(0,r._)([(0,u.Cb)({json:{read:!0,write:!0}})],M.prototype,"blendMode",void 0),(0,r._)([(0,u.Cb)()],M.prototype,"levelValues",null),(0,r._)([(0,u.Cb)({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],M.prototype,"isReference",void 0),(0,r._)([(0,u.Cb)({type:["WebTiledLayer"],value:"WebTiledLayer"})],M.prototype,"operationalLayerType",void 0),(0,r._)([(0,u.Cb)({readOnly:!0,json:{read:!1,write:!1}})],M.prototype,"popupEnabled",void 0),(0,r._)([(0,u.Cb)({type:D.Z})],M.prototype,"spatialReference",void 0),(0,r._)([(0,c.r)("spatialReference",["spatialReference","fullExtent.spatialReference"])],M.prototype,"readSpatialReference",null),(0,r._)([(0,u.Cb)({type:[String],json:{write:!0}})],M.prototype,"subDomains",void 0),(0,r._)([(0,u.Cb)({type:C.Z,json:{write:!0}})],M.prototype,"tileInfo",void 0),(0,r._)([(0,u.Cb)({readOnly:!0})],M.prototype,"tileServers",null),(0,r._)([(0,u.Cb)({json:{read:!1}})],M.prototype,"type",void 0),(0,r._)([(0,u.Cb)()],M.prototype,"urlPath",null),(0,r._)([(0,u.Cb)({type:String,json:{origins:{"portal-item":{read:{source:"url"}}}}})],M.prototype,"urlTemplate",void 0),(0,r._)([(0,c.r)("urlTemplate",["urlTemplate","templateUrl"])],M.prototype,"readUrlTemplate",null),(0,r._)([(0,d.c)("urlTemplate",{templateUrl:{type:String}})],M.prototype,"writeUrlTemplate",null),(0,r._)([(0,u.Cb)({type:E.B,json:{write:!0}})],M.prototype,"wmtsInfo",void 0),M=i=(0,r._)([(0,p.j)("esri.layers.WebTileLayer")],M);const O=M},16859:(e,t,s)=>{s.d(t,{I:()=>C});var i=s(43697),r=s(68773),l=s(40330),n=s(3172),o=s(66643),a=s(20102),h=s(92604),u=s(70586),c=s(95330),p=s(17452),d=s(5600),f=(s(75215),s(67676),s(71715)),m=s(52011),g=s(30556),v=s(84230),y=s(65587),_=s(15235),b=s(86082),w=s(14661);const C=e=>{let t=class extends e{constructor(){super(...arguments),this.resourceReferences={portalItem:null,paths:[]},this.userHasEditingPrivileges=!0,this.userHasFullEditingPrivileges=!1,this.userHasUpdateItemPrivileges=!1}destroy(){this.portalItem=(0,u.SC)(this.portalItem)}set portalItem(e){e!==this._get("portalItem")&&(this.removeOrigin("portal-item"),this._set("portalItem",e))}readPortalItem(e,t,s){if(t.itemId)return new _.default({id:t.itemId,portal:s&&s.portal})}writePortalItem(e,t){e&&e.id&&(t.itemId=e.id)}async loadFromPortal(e,t){if(this.portalItem&&this.portalItem.id)try{const i=await s.e(8062).then(s.bind(s,18062));return(0,c.k_)(t),await i.load({instance:this,supportedTypes:e.supportedTypes,validateItem:e.validateItem,supportsData:e.supportsData,layerModuleTypeMap:e.layerModuleTypeMap},t)}catch(e){throw(0,c.D_)(e)||h.Z.getLogger(this.declaredClass).warn(`Failed to load layer (${this.title}, ${this.id}) portal item (${this.portalItem.id})\n  ${e}`),e}}async finishLoadEditablePortalLayer(e){this._set("userHasEditingPrivileges",await this._fetchUserHasEditingPrivileges(e).catch((e=>((0,c.r9)(e),!0))))}async _setUserPrivileges(e,t){if(!r.Z.userPrivilegesApplied)return this.finishLoadEditablePortalLayer(t);if(this.url)try{const{features:{edit:s,fullEdit:i},content:{updateItem:r}}=await this._fetchUserPrivileges(e,t);this._set("userHasEditingPrivileges",s),this._set("userHasFullEditingPrivileges",i),this._set("userHasUpdateItemPrivileges",r)}catch(e){(0,c.r9)(e)}}async _fetchUserPrivileges(e,t){let s=this.portalItem;if(!e||!s||!s.loaded||s.sourceUrl)return this._fetchFallbackUserPrivileges(t);const i=e===s.id;if(i&&s.portal.user)return(0,w.Ss)(s);let r,n;if(i)r=s.portal.url;else try{r=await(0,v.oP)(this.url,t)}catch(e){(0,c.r9)(e)}if(!r||!(0,p.Zo)(r,s.portal.url))return this._fetchFallbackUserPrivileges(t);try{const e=(0,u.pC)(t)?t.signal:null;n=await(l.id?.getCredential(`${r}/sharing`,{prompt:!1,signal:e}))}catch(e){(0,c.r9)(e)}if(!n)return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}};try{if(i?await s.reload():(s=new _.default({id:e,portal:{url:r}}),await s.load(t)),s.portal.user)return(0,w.Ss)(s)}catch(e){(0,c.r9)(e)}return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}}}async _fetchFallbackUserPrivileges(e){let t=!0;try{t=await this._fetchUserHasEditingPrivileges(e)}catch(e){(0,c.r9)(e)}return{features:{edit:t,fullEdit:!1},content:{updateItem:!1}}}async _fetchUserHasEditingPrivileges(e){const t=this.url?l.id?.findCredential(this.url):null;if(!t)return!0;const s=E.credential===t?E.user:await this._fetchEditingUser(e);return E.credential=t,E.user=s,(0,u.Wi)(s)||null==s.privileges||s.privileges.includes("features:user:edit")}async _fetchEditingUser(e){const t=this.portalItem?.portal?.user;if(t)return t;const s=l.id.findServerInfo(this.url??"");if(!s?.owningSystemUrl)return null;const i=`${s.owningSystemUrl}/sharing/rest`,r=y.Z.getDefault();if(r&&r.loaded&&(0,p.Fv)(r.restUrl)===(0,p.Fv)(i))return r.user;const a=`${i}/community/self`,h=(0,u.pC)(e)?e.signal:null,c=await(0,o.q6)((0,n.default)(a,{authMode:"no-prompt",query:{f:"json"},signal:h}));return c.ok?b.default.fromJSON(c.value.data):null}read(e,t){t&&(t.layer=this),super.read(e,t)}write(e,t){const s=t&&t.portal,i=this.portalItem&&this.portalItem.id&&(this.portalItem.portal||y.Z.getDefault());return s&&i&&!(0,p.tm)(i.restUrl,s.restUrl)?(t.messages&&t.messages.push(new a.Z("layer:cross-portal",`The layer '${this.title} (${this.id})' cannot be persisted because it refers to an item on a different portal than the one being saved to. To save, set layer.portalItem to null or save to the same portal as the item associated with the layer`,{layer:this})),null):super.write(e,{...t,layer:this})}};return(0,i._)([(0,d.Cb)({type:_.default})],t.prototype,"portalItem",null),(0,i._)([(0,f.r)("web-document","portalItem",["itemId"])],t.prototype,"readPortalItem",null),(0,i._)([(0,g.c)("web-document","portalItem",{itemId:{type:String}})],t.prototype,"writePortalItem",null),(0,i._)([(0,d.Cb)({clonable:!1})],t.prototype,"resourceReferences",void 0),(0,i._)([(0,d.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasEditingPrivileges",void 0),(0,i._)([(0,d.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasFullEditingPrivileges",void 0),(0,i._)([(0,d.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasUpdateItemPrivileges",void 0),t=(0,i._)([(0,m.j)("esri.layers.mixins.PortalLayer")],t),t},E={credential:null,user:null}},34760:(e,t,s)=>{s.d(t,{Q:()=>v});var i=s(43697),r=s(92604),l=s(95330),n=s(5600),o=(s(75215),s(67676),s(52011)),a=s(46791),h=(s(80442),s(20102),s(26258),s(87538));const u=new a.Z,c=new WeakMap;function p(e){return null!=e&&"object"==typeof e&&"refreshInterval"in e&&"refresh"in e}function d(e,t){return Number.isFinite(e)&&Number.isFinite(t)?t<=0?e:d(t,e%t):0}let f=0,m=0;function g(){const e=Date.now();for(const t of u)t.refreshInterval&&e-(c.get(t)??0)+5>=6e4*t.refreshInterval&&(c.set(t,e),t.refresh(e))}(0,h.EH)((()=>{const e=Date.now();let t=0;for(const s of u)t=d(Math.round(6e4*s.refreshInterval),t),s.refreshInterval?c.get(s)||c.set(s,e):c.delete(s);if(t!==m){if(m=t,clearInterval(f),0===m)return void(f=0);f=setInterval(g,m)}}));const v=e=>{let t=class extends e{constructor(...e){super(...e),this.refreshInterval=0,this.refreshTimestamp=0,this._debounceHasDataChanged=(0,l.Ds)((()=>this.hasDataChanged())),this.when().then((()=>{!function(e){p(e)&&u.push(e)}(this)}),(()=>{}))}destroy(){p(this)&&u.includes(this)&&u.remove(this)}get refreshParameters(){return{_ts:this.refreshTimestamp||null}}refresh(e=Date.now()){(0,l.R8)(this._debounceHasDataChanged()).then((t=>{t&&this._set("refreshTimestamp",e),this.emit("refresh",{dataChanged:t})}),(e=>{r.Z.getLogger(this.declaredClass).error(e),this.emit("refresh",{dataChanged:!1,error:e})}))}async hasDataChanged(){return!0}};return(0,i._)([(0,n.Cb)({type:Number,cast:e=>e>=.1?e:e<=0?0:.1,json:{write:!0}})],t.prototype,"refreshInterval",void 0),(0,i._)([(0,n.Cb)({readOnly:!0})],t.prototype,"refreshTimestamp",void 0),(0,i._)([(0,n.Cb)()],t.prototype,"refreshParameters",null),t=(0,i._)([(0,o.j)("esri.layers.mixins.RefreshableLayer")],t),t}},39450:(e,t,s)=>{s.d(t,{Z:()=>u});var i,r=s(43697),l=s(96674),n=s(5600),o=s(75215),a=(s(67676),s(52011));let h=i=class extends l.wq{constructor(e){super(e),this.cols=null,this.level=0,this.levelValue=null,this.origin=null,this.resolution=0,this.rows=null,this.scale=0}clone(){return new i({cols:this.cols,level:this.level,levelValue:this.levelValue,resolution:this.resolution,rows:this.rows,scale:this.scale})}};(0,r._)([(0,n.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],h.prototype,"cols",void 0),(0,r._)([(0,n.Cb)({type:o.z8,json:{write:!0}})],h.prototype,"level",void 0),(0,r._)([(0,n.Cb)({type:String,json:{write:!0}})],h.prototype,"levelValue",void 0),(0,r._)([(0,n.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],h.prototype,"origin",void 0),(0,r._)([(0,n.Cb)({type:Number,json:{write:!0}})],h.prototype,"resolution",void 0),(0,r._)([(0,n.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],h.prototype,"rows",void 0),(0,r._)([(0,n.Cb)({type:Number,json:{write:!0}})],h.prototype,"scale",void 0),h=i=(0,r._)([(0,a.j)("esri.layers.support.LOD")],h);const u=h},11145:(e,t,s)=>{s.d(t,{Z:()=>D});var i,r=s(43697),l=s(35454),n=s(96674),o=s(70586),a=s(67900),h=s(5600),u=s(75215),c=(s(67676),s(71715)),p=s(52011),d=s(30556),f=s(94139),m=s(82971),g=s(24470),v=s(8744),y=s(40488),_=s(39450),b=s(43077);const w=new l.X({PNG:"png",PNG8:"png8",PNG24:"png24",PNG32:"png32",JPEG:"jpg",JPG:"jpg",DIB:"dib",TIFF:"tiff",EMF:"emf",PS:"ps",PDF:"pdf",GIF:"gif",SVG:"svg",SVGZ:"svgz",Mixed:"mixed",MIXED:"mixed",LERC:"lerc",LERC2D:"lerc2d",RAW:"raw",pbf:"pbf"});let C=i=class extends n.wq{static create(e={}){const{resolutionFactor:t=1,scales:s,size:r=256,spatialReference:l=m.Z.WebMercator,numLODs:n=24}=e;if(!(0,v.JY)(l)){const e=[];if(s)for(let t=0;t<s.length;t++){const i=s[t];e.push(new _.Z({level:t,scale:i,resolution:i}))}else{let t=5e-4;for(let s=n-1;s>=0;s--)e.unshift(new _.Z({level:s,scale:t,resolution:t})),t*=2}return new i({dpi:96,lods:e,origin:new f.Z(0,0,l),size:[r,r],spatialReference:l})}const o=(0,v.C5)(l),h=e.origin?new f.Z({x:e.origin.x,y:e.origin.y,spatialReference:l}):new f.Z(o?{x:o.origin[0],y:o.origin[1],spatialReference:l}:{x:0,y:0,spatialReference:l}),u=1/(39.37*(0,a.c9)(l)*96),c=[];if(s)for(let e=0;e<s.length;e++){const t=s[e],i=t*u;c.push(new _.Z({level:e,scale:t,resolution:i}))}else{let e=(0,v.sT)(l)?512/r*591657527.5917094:256/r*591657527.591555;const s=Math.ceil(n/t);c.push(new _.Z({level:0,scale:e,resolution:e*u}));for(let i=1;i<s;i++){const s=e/2**t,r=s*u;c.push(new _.Z({level:i,scale:s,resolution:r})),e=s}}return new i({dpi:96,lods:c,origin:h,size:[r,r],spatialReference:l})}constructor(e){super(e),this.dpi=96,this.format=null,this.origin=null,this.minScale=0,this.maxScale=0,this.size=null,this.spatialReference=null}get isWrappable(){const{spatialReference:e,origin:t}=this;if(e&&t){const s=(0,v.C5)(e);return e.isWrappable&&!!s&&Math.abs(s.origin[0]-t.x)<=s.dx}return!1}readOrigin(e,t){return f.Z.fromJSON({spatialReference:t.spatialReference,...e})}set lods(e){let t=0,s=0;const i=[],r=this._levelToLOD={};e&&(t=-1/0,s=1/0,e.forEach((e=>{i.push(e.scale),t=e.scale>t?e.scale:t,s=e.scale<s?e.scale:s,r[e.level]=e}))),this._set("scales",i),this._set("minScale",t),this._set("maxScale",s),this._set("lods",e),this._initializeUpsampleLevels()}readSize(e,t){return[t.cols,t.rows]}writeSize(e,t){t.cols=e[0],t.rows=e[1]}zoomToScale(e){const t=this.scales;if(e<=0)return t[0];if(e>=t.length-1)return t[t.length-1];const s=Math.floor(e),i=s+1;return t[s]/(t[s]/t[i])**(e-s)}scaleToZoom(e){const t=this.scales,s=t.length-1;let i=0;for(;i<s;i++){const s=t[i],r=t[i+1];if(s<=e)return i;if(r===e)return i+1;if(s>e&&r<e)return i+Math.log(s/e)/Math.log(s/r)}return i}snapScale(e,t=.95){const s=this.scaleToZoom(e);return s%Math.floor(s)>=t?this.zoomToScale(Math.ceil(s)):this.zoomToScale(Math.floor(s))}tileAt(e,t,s,i){const r=this.lodAt(e);if(!r)return null;let l,n;if("number"==typeof t)l=t,n=s;else if((0,v.fS)(t.spatialReference,this.spatialReference))l=t.x,n=t.y,i=s;else{const e=(0,y.iV)(t,this.spatialReference);if((0,o.Wi)(e))return null;l=e.x,n=e.y,i=s}const a=r.resolution*this.size[0],h=r.resolution*this.size[1];return i||(i=new b.f(null,0,0,0,(0,g.Ue)())),i.level=e,i.row=Math.floor((this.origin.y-n)/h+.001),i.col=Math.floor((l-this.origin.x)/a+.001),this.updateTileInfo(i),i}updateTileInfo(e,t=i.ExtrapolateOptions.NONE){let s=this.lodAt(e.level);if(!s&&t===i.ExtrapolateOptions.POWER_OF_TWO){const t=this.lods[this.lods.length-1];t.level<e.level&&(s=t)}if(!s)return;const r=e.level-s.level,l=s.resolution*this.size[0]/2**r,n=s.resolution*this.size[1]/2**r;e.id=`${e.level}/${e.row}/${e.col}`,e.extent||(e.extent=(0,g.Ue)()),e.extent[0]=this.origin.x+e.col*l,e.extent[1]=this.origin.y-(e.row+1)*n,e.extent[2]=e.extent[0]+l,e.extent[3]=e.extent[1]+n}upsampleTile(e){const t=this._upsampleLevels[e.level];return!(!t||-1===t.parentLevel||(e.level=t.parentLevel,e.row=Math.floor(e.row/t.factor+.001),e.col=Math.floor(e.col/t.factor+.001),this.updateTileInfo(e),0))}getTileBounds(e,t){const s=this.lodAt(t.level);if(null==s)return null;const{resolution:i}=s,r=i*this.size[0],l=i*this.size[1];return e[0]=this.origin.x+t.col*r,e[1]=this.origin.y-(t.row+1)*l,e[2]=e[0]+r,e[3]=e[1]+l,e}lodAt(e){return this._levelToLOD?.[e]??null}clone(){return i.fromJSON(this.write({}))}getOrCreateCompatible(e,t){if(256===this.size[0]&&256===this.size[1])return 256===e?this:null;const s=[],r=this.lods.length;for(let e=0;e<r;e++){const i=this.lods[e],r=i.resolution*t;s.push(new _.Z({level:i.level,scale:i.scale,resolution:r}))}return new i({size:[e,e],dpi:this.dpi,format:this.format,compressionQuality:this.compressionQuality,origin:this.origin,spatialReference:this.spatialReference,lods:s})}_initializeUpsampleLevels(){const e=this.lods;this._upsampleLevels=[];let t=null;for(let s=0;s<e.length;s++){const i=e[s];this._upsampleLevels[i.level]={parentLevel:t?t.level:-1,factor:t?t.resolution/i.resolution:0},t=i}}};var E,T;(0,r._)([(0,h.Cb)({type:Number,json:{write:!0}})],C.prototype,"compressionQuality",void 0),(0,r._)([(0,h.Cb)({type:Number,json:{write:!0}})],C.prototype,"dpi",void 0),(0,r._)([(0,h.Cb)({type:String,json:{read:w.read,write:w.write,origins:{"web-scene":{read:!1,write:!1}}}})],C.prototype,"format",void 0),(0,r._)([(0,h.Cb)({readOnly:!0})],C.prototype,"isWrappable",null),(0,r._)([(0,h.Cb)({type:f.Z,json:{write:!0}})],C.prototype,"origin",void 0),(0,r._)([(0,c.r)("origin")],C.prototype,"readOrigin",null),(0,r._)([(0,h.Cb)({type:[_.Z],value:null,json:{write:!0}})],C.prototype,"lods",null),(0,r._)([(0,h.Cb)({readOnly:!0})],C.prototype,"minScale",void 0),(0,r._)([(0,h.Cb)({readOnly:!0})],C.prototype,"maxScale",void 0),(0,r._)([(0,h.Cb)({readOnly:!0})],C.prototype,"scales",void 0),(0,r._)([(0,h.Cb)({cast:e=>Array.isArray(e)?e:"number"==typeof e?[e,e]:[256,256]})],C.prototype,"size",void 0),(0,r._)([(0,c.r)("size",["rows","cols"])],C.prototype,"readSize",null),(0,r._)([(0,d.c)("size",{cols:{type:u.z8},rows:{type:u.z8}})],C.prototype,"writeSize",null),(0,r._)([(0,h.Cb)({type:m.Z,json:{write:!0}})],C.prototype,"spatialReference",void 0),C=i=(0,r._)([(0,p.j)("esri.layers.support.TileInfo")],C),(T=(E=C||(C={})).ExtrapolateOptions||(E.ExtrapolateOptions={}))[T.NONE=0]="NONE",T[T.POWER_OF_TWO=1]="POWER_OF_TWO";const D=C},43077:(e,t,s)=>{s.d(t,{f:()=>i});class i{constructor(e,t,s,i,r){this.id=e,this.level=t,this.row=s,this.col=i,this.extent=r}}},5833:(e,t,s)=>{s.d(t,{B:()=>h});var i,r=s(43697),l=s(96674),n=s(22974),o=s(5600),a=(s(75215),s(52011));let h=i=class extends l.wq{constructor(e){super(e)}clone(){return new i({customLayerParameters:(0,n.d9)(this.customLayerParameters),customParameters:(0,n.d9)(this.customParameters),layerIdentifier:this.layerIdentifier,tileMatrixSet:this.tileMatrixSet,url:this.url})}};(0,r._)([(0,o.Cb)({json:{type:Object,write:!0}})],h.prototype,"customLayerParameters",void 0),(0,r._)([(0,o.Cb)({json:{type:Object,write:!0}})],h.prototype,"customParameters",void 0),(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],h.prototype,"layerIdentifier",void 0),(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],h.prototype,"tileMatrixSet",void 0),(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],h.prototype,"url",void 0),h=i=(0,r._)([(0,a.j)("esri.layer.support.WMTSLayerInfo")],h)},90082:(e,t,s)=>{s.d(t,{g:()=>r});var i=s(20102);async function r(e,t){try{return await createImageBitmap(e)}catch(e){throw new i.Z("request:server",`Unable to load ${t}`,{url:t,error:e})}}}}]);