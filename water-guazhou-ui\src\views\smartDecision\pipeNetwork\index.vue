<!-- 智慧管网 -->
<template>
  <Layout
    :show-headers="true"
    :show-bars="false"
  >
    <pipeNetwork_yintao v-if="state.siteName === 'yintao'"></pipeNetwork_yintao>
    <pipeNetwork_wudang v-else-if="state.siteName === 'wudang'"></pipeNetwork_wudang>
    <pipeNetWork v-else></pipeNetWork>
  </Layout>
</template>

<script lang="ts" setup>
import Layout from '../layout/index.vue'
import pipeNetWork from './pipeNetwork.vue'
import pipeNetwork_yintao from './pipeNetwork_yintao.vue'
import pipeNetwork_wudang from './pipeNetwork_wudang.vue'

const state = reactive<{ siteName: ISiteName }>({
  siteName: window.SITE_CONFIG.SITENAME
})
</script>

<style lang="scss" scoped></style>
