package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.request.SystemNotifyListRequest;
import org.thingsboard.server.dao.notify.SystemNotifyService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

@RestController
@RequestMapping("api/systemNotify")
public class SystemNotifyController extends BaseController {

    @Autowired
    private SystemNotifyService systemNotifyService;

    @GetMapping("list")
    public IstarResponse findList(SystemNotifyListRequest request) throws ThingsboardException {
        return IstarResponse.ok(systemNotifyService.findList(request, getTenantId()));
    }

    @PostMapping("readAll")
    public IstarResponse readAll(@RequestBody JSONObject param) throws ThingsboardException {
        String type = param.getString("type");
        String to = param.getString("to");

        if (StringUtils.isBlank(type) || StringUtils.isBlank(to)) {
            return IstarResponse.error("参数异常, 一键已读失败!");
        }
        systemNotifyService.readAll(type, to, getTenantId());

        return IstarResponse.ok();
    }

    @PostMapping("read")
    public IstarResponse readOne(@RequestBody JSONObject param) throws ThingsboardException {
        String id = param.getString("id");

        if (StringUtils.isBlank(id) ) {
            return IstarResponse.error("参数异常, 已读失败!");
        }
        systemNotifyService.readOne(id, getTenantId());

        return IstarResponse.ok();
    }

    @GetMapping("notifyCount")
    public IstarResponse notifyCount(@RequestParam String to, @RequestParam(required = false, defaultValue = "0") String status) throws ThingsboardException {
        return IstarResponse.ok(systemNotifyService.notifyCount(to, status, getTenantId()));
    }


}
