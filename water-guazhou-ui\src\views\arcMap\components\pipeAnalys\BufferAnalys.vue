<template>
  <div>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <PipeDetail
      ref="refDetail"
      :query-params="staticState.queryParams"
      :tabs="state.tabs"
      :telport="telport"
      @refreshed="() => (state.curOperate = 'viewing')"
      @refreshing="() => (state.curOperate = 'detailing')"
      @close="() => (state.curOperate = 'analysed')"
      @rowdblclick="handleLocate"
    ></PipeDetail>
  </div>
</template>
<script lang="ts" setup>
import { IFormIns } from '@/components/type'
import {
  createGraphic,
  createPoint,
  createPolygon,
  createPolyline,
  excuteQueryForIds,
  getGraphicLayer,
  initDrawer,
  initQueryParams,
  setMapCursor,
  setSymbol,
  createGeometry,
  queryBufferPolygon,
  initBufferParams
} from '@/utils/MapHelper'
import { SLMessage } from '@/utils/Message'
import { QueryPipeDataService } from '@/api/mapservice/pipe'
import PipeDetail from '../common/PipeDetail.vue'

const props = defineProps<{
  view?: __esri.MapView
  telport?: string
}>()
const state = reactive<{
  curOperate:
    | 'drawing'
    | 'drawed'
    | 'analysing'
    | 'analysed'
    | 'detailing'
    | 'viewing'
    | ''

  tabs: { label: string; name: string; data: any }[]
  curType: 'point' | 'polyline' | 'polygon'
}>({
  curOperate: 'drawing',

  tabs: [],
  curType: 'point'
})
const staticState: {
  drawer?: __esri.Draw
  drawAction?: __esri.DrawAction
  bufferLayer?: __esri.GraphicsLayer
  graphicsLayer?: __esri.GraphicsLayer
  geometry?: __esri.Geometry
  vertices?: any[]
  queryParams: {
    geometry?: __esri.Polygon
    where?: string
  }
} = {
  queryParams: {
    geometry: undefined,
    where: undefined
  }
}
const refForm = ref<IFormIns>()
const refDetail = ref<InstanceType<typeof PipeDetail>>()
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  group: [
    {
      fieldset: {
        desc: '分析类型'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              styles: {
                width: '100%'
              },
              perm: true,
              type: () => (state.curType === 'point' ? 'primary' : 'default'),
              text: '点要素',
              disabled: () => state.curOperate === 'analysing'
                || state.curOperate === 'detailing',
              click: () => {
                state.curType = 'point'
                clearGraphicsLayer()
                clearBufferLayer()
                reset()
              }
            },
            {
              styles: {
                width: '100%'
              },
              perm: true,
              type: () => (state.curType === 'polyline' ? 'primary' : 'default'),
              text: '线要素',
              disabled: () => state.curOperate === 'analysing'
                || state.curOperate === 'detailing',
              click: () => {
                state.curType = 'polyline'
                clearGraphicsLayer()
                clearBufferLayer()
                reset()
              }
            },
            {
              styles: {
                width: '100%'
              },
              perm: true,
              type: () => (state.curType === 'polygon' ? 'primary' : 'default'),
              text: '面要素',
              disabled: () => state.curOperate === 'analysing'
                || state.curOperate === 'detailing',
              click: () => {
                state.curType = 'polygon'
                clearGraphicsLayer()
                clearBufferLayer()
                reset()
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '缓冲距离'
      },
      fields: [
        { type: 'input-number', field: 'distance', append: '米' },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginTop: '20px',
            marginBottom: '5px'
          },
          btns: [
            {
              perm: true,
              text: () => (state.curOperate === 'analysing' ? '正在分析' : '开始分析'),
              loading: () => state.curOperate === 'analysing',
              disabled: () => state.curOperate !== 'drawed'
                && state.curOperate !== 'analysed'
                && state.curOperate !== 'viewing',
              click: () => startAnalys(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
        // {
        //   type: 'btn-group',
        //   btns: [
        //     {
        //       perm: true,
        //       text: () => (state.curOperate === 'detailing' ? '正在获取详情数据' : '查看详情'),
        //       loading: () => state.curOperate === 'detailing',
        //       disabled: () => state.curOperate !== 'analysed',
        //       click: () => {
        //         state.curOperate = 'detailing'
        //         refDetail.value?.openDialog()
        //       },
        //       styles: {
        //         width: '100%'
        //       }
        //     }
        //   ]
        // }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {
    type: 'point',
    distance: 0
  }
})
const clearGraphicsLayer = () => {
  if (!props.view) return
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'buffer-analys-mark',
    title: '缓冲区分析标注'
  })
  staticState.graphicsLayer?.removeAll()
  staticState.geometry = undefined
  staticState.vertices = undefined
}
const clearBufferLayer = () => {
  if (!props.view) return
  staticState.bufferLayer = getGraphicLayer(props.view, {
    id: 'buffer-analys-result',
    title: '缓冲区分析结果'
  })
  staticState.bufferLayer?.removeAll()
  staticState.queryParams.geometry = undefined
}
const reset = () => {
  if (!props.view) return
  state.curOperate = 'drawing'
  setMapCursor('crosshair')
  const type = state.curType
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
  staticState.drawer = initDrawer(props.view)
  staticState.drawAction = staticState.drawer?.create(type)
  type !== 'point'
    && staticState.drawAction?.on(['vertex-add', 'cursor-update'], updateVertices)
  staticState.drawAction?.on('draw-complete', e => {
    updateVertices(e)
    staticState.vertices = e.vertices
    setMapCursor('')
    const geometry = createGeometry(
      type,
      staticState.vertices,
      props.view?.spatialReference
    )
    staticState.geometry = geometry
    state.curOperate = 'drawed'
  })
}
const updateVertices = e => {
  const type = state.curType
  const graphic = type === 'polyline'
    ? createPolyline(e.vertices, props.view?.spatialReference)
    : type === 'polygon'
      ? e.vertices.length < 3
        ? createPolyline(e.vertices, props.view?.spatialReference)
        : createPolygon(e.vertices, props.view?.spatialReference)
      : createPoint(e.vertices, props.view?.spatialReference)
  staticState.graphicsLayer?.removeAll()
  graphic && staticState.graphicsLayer?.add(graphic)
}
const startAnalys = async () => {
  state.curOperate = 'analysing'
  if (!staticState.vertices) {
    SLMessage.warning('请选绘制图形')
    state.curOperate = 'drawing'
    return
  }
  clearBufferLayer()
  const distance = refForm.value?.dataForm?.distance || '0'
  state.tabs = []
  try {
    distance === '0' ? await doQuery() : await doBuffer()
    nextTick(() => {
      state.curOperate = 'detailing'
      refDetail.value?.openDialog()
    })
  } catch (error) {
    SLMessage.error('系统错误')
    state.curOperate = 'drawed'
  }
}
const doBuffer = async () => {
  if (!staticState.geometry) return
  const distance = refForm.value?.dataForm?.distance || 0
  const polygons = await queryBufferPolygon(
    initBufferParams({
      bufferSpatialReference: props.view?.spatialReference,
      distances: [distance],
      geometries: [staticState.geometry],
      outSpatialReference: props.view?.spatialReference,
      geodesic: true,
      unit: 'meters',
      unionResults: false
    })
  )
  staticState.queryParams.geometry = polygons[0]
  const graphic = createGraphic({
    geometry: polygons[0],
    symbol: setSymbol('polygon', {
      color: [0, 182, 153, 0.2],
      outlineColor: '#00B699',
      outlineWidth: 1
    })
  })
  staticState.bufferLayer?.add(graphic)
  const layers = await getLayers()
  await getTab(layers, 0)
}
const handleLocate = async () => {
  props.view && refDetail.value?.extentTo(props.view)
}
const doQuery = async () => {
  const layers = await getLayers()
  staticState.queryParams.geometry = staticState.geometry as any
  await getTab(layers, 0)
}
const getLayers = async () => {
  const res = await QueryPipeDataService()
  return res.data.layers?.filter(item => !item.subLayerIds)
}
const getTab = async (layers: any[], index: number) => {
  try {
    const id = layers[index]?.id
    const tab = layers[index]?.name
    const oidUrl = window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService
    const alloids = await excuteQueryForIds(
      `${oidUrl}/${id}`,
      initQueryParams({
        orderByFields: ['OBJECTID asc'],
        ...(staticState.queryParams || {})
      })
    )
    if (alloids !== null) {
      state.tabs.push({ label: tab, name: tab, data: alloids })
    }
    if (index < layers.length - 1) {
      await getTab(layers, ++index)
    }
  } catch (error) {
    console.log('发生错误，停止递归')
  }
}
const setQueryParams = () => {
  // const ranges = store.gis.gUserInfo?.range?.split(';')
  // const rangeStr = ranges?.map(range => "'" + range + "'").join(',')
  // staticState.queryParams.where = ranges?.length
  //   ? 'MANAGEDEPT_CODE in (' + rangeStr + ')'
  //   : undefined
}
const destroy = () => {
  setMapCursor('')
  staticState.drawer?.destroy()
  staticState.drawAction?.destroy()
  staticState.bufferLayer && props.view?.map.remove(staticState.bufferLayer)
  staticState.graphicsLayer && props.view?.map.remove(staticState.graphicsLayer)
}
onMounted(() => {
  setQueryParams()
  clearGraphicsLayer()
  clearBufferLayer()
  reset()
})
onBeforeUnmount(() => {
  destroy()
})
</script>
<style lang="scss" scoped></style>
