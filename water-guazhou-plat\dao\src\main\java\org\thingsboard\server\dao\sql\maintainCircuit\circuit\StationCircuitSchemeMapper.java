package org.thingsboard.server.dao.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.StationCircuitPlanListRequest;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitScheme;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitTask;

@Mapper
public interface StationCircuitSchemeMapper extends BaseMapper<StationCircuitScheme> {

    IPage<StationCircuitScheme> findList(IPage<StationCircuitScheme> page, @Param("name") String name,
                                         @Param("stationType") String stationType, @Param("tenantId") String tenantId);

}
