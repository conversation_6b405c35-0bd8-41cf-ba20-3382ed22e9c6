package org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpMaintenanceStandard;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

@Getter
@Setter
public class PumpMaintenanceStandardSaveRequest extends SaveRequest<PumpMaintenanceStandard> {
    // 文件类型
    private String type;

    // 文件名称
    private String name;

    // 录入人
    private String inputUserName;

    // 备注
    private String remark;

    // 文件url
    private String file;

    @Override
    protected PumpMaintenanceStandard build() {
        PumpMaintenanceStandard entity = new PumpMaintenanceStandard();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected PumpMaintenanceStandard update(String id) {
        PumpMaintenanceStandard entity = new PumpMaintenanceStandard();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(PumpMaintenanceStandard entity) {
        entity.setType(type);
        entity.setName(name);
        entity.setInputUserName(inputUserName);
        entity.setRemark(remark);
        entity.setFile(file);
    }
}