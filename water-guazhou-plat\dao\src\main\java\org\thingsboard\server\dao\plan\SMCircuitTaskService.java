package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.*;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.model.sql.statistic.StatisticItem;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.*;

import java.util.List;

public interface SMCircuitTaskService {
    /**
     * 分页条件查询巡检任务
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SMCircuitTaskResponse> findAllConditional(SMCircuitTaskPageRequest request);

    /**
     * 保存巡检任务
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SMCircuitTask save(SMCircuitTaskSaveRequest entity);

    /**
     * 批量保存
     *
     * @param entities 实体列表
     * @return 保存好的数据
     */
    List<SMCircuitTask> saveAll(List<SMCircuitTaskSaveRequest> entities);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SMCircuitTask entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 分派任务
     *
     * @param req 详细信息
     * @return 是否成功
     */
    boolean assign(SMCircuitTaskAssignRequest req);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否成功
     */
    boolean deleteAll(List<String> idList);

    /**
     * 批量分派
     *
     * @param req 详细分派信息
     * @return 是否成功
     */
    boolean assignBatch(SMCircuitTaskBatchAssignRequest req);

    /**
     * 批量审核
     *
     * @param req 审核信息
     * @return 是否成功
     */
    boolean auditBatch(SMCircuitTaskBatchAuditRequest req);

    /**
     * 上报工单
     *
     * @param req 工单上报详情
     * @return 是否成功
     */
    boolean report(SMCircuitTaskReportRequest req);

    /**
     * 查询隐患工单信息，即{@link this#report 上报的工单}
     *
     * @param request 筛选条件
     * @return 隐患工单信息
     */
    IPage<SMCircuitTaskWorkOrderPitfallResponse> findPitfallWorkorderInfo(SMCircuitTaskWorkOrderPitfallPageRequest request);

    /**
     * 分时分类（时间段内不同类型分别有多少工单）或分类分时统计工单（类型在不同时间段分别有多少工单）
     *
     * @param req 统计请求
     * @return 统计信息
     */
    StatisticItem findWorkOrderTrend(SMCircuitTaskWorkOrderTrendRequest req);

    /**
     * 分时分类（时间段内不同类型分别有多少工单）或分类分时统计不同用户的工单数量（类型在不同时间段分别有多少工单）
     *
     * @param req 统计请求
     * @return 统计信息
     */
    StatisticItem countByUser(SMCircuitTaskWorkOrderStatisticUserRequest req);

    /**
     * 完成巡检任务
     *
     * @param req 请求
     * @return 是否成功
     */
    boolean complete(SMCircuitTaskCompleteRequest req);

    /**
     * 将巡检任务的id批量转换为code
     *
     * @param idList id列表
     * @return code列表
     */
    List<String> getBatchCode(List<String> idList);

    /**
     * 获取指定任务状态的统计信息，其中
     * {@link GeneralTaskStatusStatistic#getTotal() 任务总数}用于查看用户总共接收了多少任务，
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}用于查看处于此状态的任务有多少个，
     * {@link GeneralTaskStatusStatistic#getPercent() 指定状态任务数}为
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}/{@link GeneralTaskStatusStatistic#getTotal() 任务总数}
     * 计算而出的结果
     *
     * @param userId 用户id
     * @param status 任务状态
     * @return 指定任务状态的统计信息
     */
    GeneralTaskStatusStatistic countStatusByUser(String userId, GeneralTaskStatus status);

    /**
     * 获取指定任务状态的统计信息，其中
     * {@link GeneralTaskStatusStatistic#getTotal() 任务总数}用于查看用户总共接收了多少任务，
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}用于查看处于此状态的任务有多少个，
     * {@link GeneralTaskStatusStatistic#getPercent() 指定状态任务数}为
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}/{@link GeneralTaskStatusStatistic#getTotal() 任务总数}
     * 计算而出的结果
     *
     * @param userId 用户id
     * @param status 任务状态
     * @return 指定任务状态的统计信息
     */
    GeneralTaskStatusStatistic countStatusByUser(String userId, List<GeneralTaskStatus> status);

    /**
     * 获取{@link GeneralTaskProcessingAndCompleteCount#getProcessingCount() 进行状态}和
     * {@link GeneralTaskProcessingAndCompleteCount#getCompleteCount() 完成状态}的任务数量
     *
     * @param userId 用户id
     * @return 进行状态和完成状态的任务数量
     */
    GeneralTaskProcessingAndCompleteCount SMCircuitTaskProcessingAndCompleteCount(String userId);


    /**
     * 通过关键点/关键设备查询单个隐患工单
     *
     * @param taskCode 任务id
     * @param pointId  关键点/关键设备id
     * @param tenantId 租户id
     * @return 隐患工单
     */
    SMCircuitTaskWorkOrderPitfallResponse findPitfallWorkorderInfoByPointId(String taskCode, String pointId, String tenantId);

    SMCircuitTaskResponse findById(String id);

    boolean isComplete(String code, String tenantId);

    boolean canBeComplete(String id);

    /**
     * 查询巡检用户（树）
     *
     * @param tenantId 租户id
     * @param type     人员类型
     * @return 单位-部门-巡检用户树
     */
    List<Organization> findGroupedUser(String tenantId, DataConstants.PEOPLE_TYPE type);

    /**
     * 巡检用户统计
     *
     * @param tenantId 租户id
     * @param type     人员类型
     * @return 统计结果
     */
    SMCircuitUserStatistic statisticUser(String tenantId, DataConstants.PEOPLE_TYPE type);

    Integer completeTotal();

    String arrivalRate();

    String feedbackRate();
}
