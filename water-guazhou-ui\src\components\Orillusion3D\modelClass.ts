import {
  Scene3D,
  Engine3D,
  Object3D,
  WorldPanel,
  BillboardType,
  TextAnchor,
  UIImage,
  UITextField,
  Vector3,
  Color,
  AtmosphericComponent,
  Camera3D,
  HoverCameraController,
  View3D,
  ViewPanel
} from '@orillusion/core'
import { Stats } from '@orillusion/stats'

/**
 * canvas 视口
 */
export interface ScenesInit {
  /** 配置视口，显示组件，不传默认全屏幕 */
  canvas?: any
  /** 统计面板 */
  stats?: boolean
}

/**
* 模型配置
*/
export interface ModelType {
  /** 模型链接 */
  url: string
  /** 场景 */
  scenes: Scenes
}

/**
* 文本配置
*/
export interface TextType {
  /** 控制面板 */
  enableUICanvas?: boolean,
  /** 渲染模式 */
  renderingMode?: 'ViewPanel' | 'WorldSpace'

  /** 字体大小 */
  fontSize?: number
  /** 背景框大小 */
  resize?: [number, number]
  /** 相对中心点位置 */
  setXY?: [number, number]
  /** 文本设置 */
  text: {
      /** 显示文本 */
      text?: string
      /** 文本框大小 */
      resize?: [number, number]
      /** 相对中心点位置 */
      setXY?: [number, number]
      /** 字体大小 */
      fontSize?: number
      /** 字体颜色 */
      color?: Color
  }[]
}

/**
 * 场景类
 * 必须提供的
 */
export class Scenes {
  scene!: Scene3D

  panel!: WorldPanel

  position!: Vector3

  scene3D!: Scene3D

  /** 视口，用于显示的组件 */
  canvas: any

  /** 统计面板 */
  stats!: boolean

  camera!: Camera3D

  controller!: HoverCameraController

  // 构造方法
  constructor(params: ScenesInit) {
    this.canvas = params?.canvas || null
    this.stats = params.stats || false

    Engine3D.setting.shadow.autoUpdate = true
    Engine3D.setting.shadow.updateFrameRate = 1
    Engine3D.setting.shadow.shadowBound = 20
    Engine3D.setting.shadow.shadowBias = 0.0001
  }

  /**
     * 运行
     */
  async run() {
    if (this.canvas) { await Engine3D.init({ canvasConfig: { canvas: this.canvas } }) } else { await Engine3D.init({ }) }
    // 创建场景根元素
    this.scene3D = new Scene3D()
    // 添加大气散射天空组件
    this.scene3D.addComponent(AtmosphericComponent)

    // 新建摄像机实例
    const cameraObj = new Object3D()
    const camera = cameraObj.addComponent(Camera3D)
    // 根据窗口大小设置摄像机视角
    camera.perspective(60, window.innerWidth / window.innerHeight, 1, 5000.0)
    // 设置相机控制器
    const controller = camera.object3D.addComponent(HoverCameraController)
    controller.setCamera(0, 0, 15)
    this.controller = controller
    // 添加相机节点
    this.scene3D.addChild(cameraObj)

    // 创建View3D对象
    const view = new View3D()
    // 指定渲染的场景
    view.scene = this.scene3D
    // 指定使用的相机
    view.camera = camera
    this.camera = camera

    // 统计面板
    this.stats && this.scene3D.addComponent(Stats)

    // 开始渲染
    Engine3D.startRenderView(view)
  }
}

export class Model {
  Scenes!: Scenes

  data!: Object3D

  panel!: WorldPanel | ViewPanel

  constructor(params: ModelType) {
    this.Scenes = params.scenes
    this.addModel(params.url)
  }

  // 添加模型
  async addModel(url?: string) {
    setTimeout(async () => {
      this.data = await Engine3D.res.loadGltf(url || 'https://cdn.orillusion.com/PBR/Duck/Duck.gltf', {

      })
      this.Scenes.scene3D.addChild(this.data)
    }, 500)
  }
}

/**
 * 加载文本框
 * @param Scenes 渲染的节点 一般默认为Scenes
 * @param params 文本框及文本配置
 */
export async function addText(Scenes: Scenes, params: TextType = { renderingMode: 'ViewPanel', text: [] }) {
  // 加载字体信息
  await Engine3D.res.loadFont('https://cdn.orillusion.com/fnt/0.fnt')
  const canvas = Scenes.scene3D.view.enableUICanvas()

  // panel
  // 设置渲染模式
  const panel = (params.renderingMode === undefined || params.renderingMode === 'ViewPanel') ? new Object3D().addComponent(ViewPanel) : new Object3D().addComponent(WorldPanel)
  // 空间格式
  panel.billboard = BillboardType.BillboardXYZ
  // add to canvas
  canvas.addChild(panel.object3D)
  //
  panel.object3D.localScale = new Vector3(1, 1, 1)

  // poi
  const panelRoot = new Object3D()
  panelRoot.x = params.resize?.[0] || 0
  panelRoot.y = params.resize?.[1] || 0
  panel.object3D.addChild(panelRoot)

  const image = panelRoot.addComponent(UIImage)
  image.uiTransform.resize(params.resize?.[0] || 0, params.resize?.[1] || 0)
  image.uiTransform.setXY(params.setXY?.[0] || 0, params.setXY?.[1] || 0)
  image.color = new Color(1, 1, 1, 0.5)
  image.isShadowless = true

  params.text.forEach(item => {
    const textQuad = new Object3D()
    panelRoot.addChild(textQuad)
    const text = textQuad.addComponent(UITextField)
    text.uiTransform.resize(item.resize?.[0] || 0, item.resize?.[1] || 0)
    text.uiTransform.setXY(item.setXY?.[0] || 0, item.setXY?.[1] || 0)
    text.alignment = TextAnchor.UpperLeft
    text.text = item.text || ''
    text.fontSize = item.fontSize || 16
    text.color = item.color || new Color(0, 0, 0, 1)
  })
}
