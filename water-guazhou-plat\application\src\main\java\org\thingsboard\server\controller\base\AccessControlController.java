package org.thingsboard.server.controller.base;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.accessControl.AccessControlService;
import org.thingsboard.server.dao.model.sql.AccessControl;
import org.thingsboard.server.dao.model.sql.VideoEntity;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("api/accessControl")
public class AccessControlController extends BaseController {

    @Autowired
    private AccessControlService accessControlService;

    @GetMapping("list")
    public PageData<AccessControl> list(@RequestParam int page, @RequestParam int size,
                                        @RequestParam(required = false) String projectId) throws ThingsboardException {
        return accessControlService.findList(page, size, projectId, getTenantId());
    }

    @PostMapping
    public AccessControl save(@RequestBody AccessControl entity) throws ThingsboardException {
        // 使用新的保存方法，支持视频关联
        return accessControlService.saveWithVideos(entity, getTenantId());
    }

    @GetMapping("{id}")
    public AccessControl get(@PathVariable String id) {
        // 使用新的查询方法，包含关联的视频信息
        return accessControlService.findByIdWithVideos(id);
    }

    @DeleteMapping("remove")
    public List<String> remove(@RequestBody List<String> ids) {
        accessControlService.remove(ids);
        return ids;
    }

    /**
     * 获取可用的视频列表（用于门禁关联选择）
     */
    @GetMapping("availableVideos")
    public List<VideoEntity> getAvailableVideos(@RequestParam(required = false) String projectId) throws ThingsboardException {
        return accessControlService.getAvailableVideos(projectId, getTenantId());
    }

}
