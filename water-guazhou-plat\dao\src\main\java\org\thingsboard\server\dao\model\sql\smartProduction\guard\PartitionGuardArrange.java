package org.thingsboard.server.dao.model.sql.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Deprecated
public class PartitionGuardArrange {
    private Date dayTime;

    private List<GuardArrange> arranges = new ArrayList<>();

    public PartitionGuardArrange(Date dayTime) {
        this.dayTime = dayTime;
    }

    public void addArrangement(GuardArrange arrangement) {
        arranges.add(arrangement);
    }

    public void addArrangement(List<GuardArrange> arranges) {
        this.arranges.addAll(arranges);
    }

}
