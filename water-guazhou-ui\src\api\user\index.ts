import request from '@/plugins/axios';

export * from './setting';
export const saveUser = (params: any) => {
  if (params.id) {
    return request({
      url: '/api/updateUser',
      method: 'post',
      data: params
    });
  }
  return request({
    url: `/api/newUser?istarCreatePasswordUrl=${encodeURIComponent(
      'https://ems.istarscloud.com/createPassword'
    )}`,
    method: 'post',
    data: params
  });
};

export function getTenantAdminsAndSys(tenantId) {
  return request({
    url: `/api/tenant/adminAndsys/${tenantId}?limit=9999999`,
    method: 'get'
  });
}

// TODO: Refactor
export function getTenantUsers(tenantId) {
  return request({
    url: `/api/tenant/users/${tenantId}?limit=9999999`,
    method: 'get'
  });
}

export function getTenantUsersByPage(params) {
  return request({
    url: '/api/tenant/users/list',
    method: 'get',
    params
  });
}

export function deleteUser(userId) {
  return request({
    url: `/api/user/${userId}`,
    method: 'delete'
  });
}

export function deleteUsers(data) {
  return request({
    url: '/api/users',
    method: 'delete',
    data
  });
}

// 获取单个用户信息
export function getUser(userId) {
  return request({
    url: `/api/user/${userId}`,
    method: 'get'
  });
}

// 拿用户拥有的区域划分的  /api//api/project/relation/project/USER/{userId}
export function getUserProject(userId) {
  return request({
    url: `/api/project/relation/project/USER/${userId}`,
    method: 'get'
  });
}

// 获取所用用户  /api/user/getAll
export function getAllUser() {
  return request({
    url: '/api/user/getAll',
    method: 'get'
  });
}

// 冻结用户 get //api/user/disable/{userId}  // uuid都是不带-的
export function disableUser(userId) {
  return request({
    url: `/api/user/disable/${userId}`,
    method: 'get'
  });
}
// 解冻用户 get //api/user/enable/{userId}
export function enableUser(userId) {
  return request({
    url: `/api/user/enable/${userId}`,
    method: 'get'
  });
}

// 重置密码
export function resetPWD(userId) {
  return request({
    url: `/api/auth/resetUserPassword/${userId}`,
    method: 'post'
  });
}

// 导入用户列表
export function importUserList(data) {
  return request({
    url: '/api/user/import',
    method: 'post',
    data
  });
}

// 导入用户列表
export function exportUserList() {
  return request({
    url: '/api/user/export',
    method: 'get',
    responseType: 'blob'
  });
}

// 获取用户列表(部门)
// const params = {
//   pid: string,
//   name?: string,
//   roleId?: string,
//   status: boolean,
//   page: string | number,
//   size: string | number
// }
export function getUserList(params) {
  return request({
    url: '/api/user/getAllByPid',
    method: 'get',
    params
  });
}

/**
 * 根据认证类型获取用户列表
 * @param {Object} params - 请求参数
 * @param {string} params.authType - 认证类型
 * @returns {Promise} 返回请求的Promise对象
 */
export function getUserslistByAuth(params: { authType: string }) {
  return request({
    url: '/api/tenant/users/listByAuth',
    method: 'get',
    params
  });
}
