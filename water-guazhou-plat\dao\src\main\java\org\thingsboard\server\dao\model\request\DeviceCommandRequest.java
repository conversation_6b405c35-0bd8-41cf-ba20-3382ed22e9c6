package org.thingsboard.server.dao.model.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class DeviceCommandRequest {

    private int page;

    private int size;

    private String commandType;

    private String deviceKey;

    private String optionUser;

    @DateTimeFormat(pattern = "yyyy-MM-ss HH:mm:ss")
    private Date beginTime;

    @DateTimeFormat(pattern = "yyyy-MM-ss HH:mm:ss")
    private Date endTime;

}
