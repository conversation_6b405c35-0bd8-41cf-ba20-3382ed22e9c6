<template>
  <div class="aou-wrapper overlay-y">
    <div class="search-box">
      <Search
        ref="refSearch"
        :config="SearchConfig"
      ></Search>
    </div>
    <div class="table-box">
      <InlineForm
        ref="refSearch_Device"
        :config="SearchConfig_Device"
        style="padding: 10px 0"
      />
      <FormTable :config="TableConfig_Device"></FormTable>
    </div>
    <div class="table-box">
      <Search
        ref="refSearch_Attr"
        :config="SearchConfig_Attr"
        style="padding: 10px 0"
      />
      <FormTable :config="TableConfig_Attr"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  AddDmaPlantMonitor,
  EditDmaPlantMonitorDirection,
  EditDmaPlantMonitorOrder,
  GetDmaPlantMonitorDevices,
  GetDmaPlantMonitorList,
  RemoveDmaPlantMonitorPoint
} from '@/api/mapservice/dma'
import { IInlineFormIns, ISearchIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { SupplyDirections } from '../config'

const refSearch = ref<ISearchIns>()
const SearchConfig = reactive<ISearch>({
  filters: [{ type: 'input', label: '名称', field: 'name' }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshDevice()
        }
      ]
    }
  ]
})
const refSearch_Device = ref<IInlineFormIns>()
const SearchConfig_Device = reactive<IFormConfig>({
  labelPosition: 'right',
  labelWidth: 90,
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '水厂名称',
          clearable: false,
          field: 'factoryName',
          rules: [{ required: true, message: '请输入水厂名称' }]
        },
        {
          type: 'select',
          label: '供水类型',
          field: 'direction',
          options: [
            { label: '供水', value: '4' },
            { label: '进水', value: '3' }
          ],
          rules: [{ required: true, message: '请选择供水类型' }]
        },
        { type: 'input-number', label: '排序编号', field: 'orderNum' }
      ]
    }
  ],
  submit: params => {
    if (!TableConfig_Device.currentRow) {
      SLMessage.error('请先选择要添加的设备')
      return
    }
    AddDmaPlantMonitor({
      ...params,
      deviceId: TableConfig_Device.currentRow.id
    })
      .then(res => {
        if (res.data.code === 200) {
          SLMessage.success('添加成功')
          refreshAttr()
          refreshDevice()
        } else {
          SLMessage.error('添加失败')
        }
      })
      .catch(() => {
        SLMessage.error('添加失败')
      })
  }
})
const TableConfig_Device = reactive<ITable>({
  height: 250,
  pagination: {
    refreshData({ page, size }) {
      TableConfig_Device.pagination.page = page
      TableConfig_Device.pagination.limit = size
      refreshDevice()
    }
  },
  operationWidth: 60,
  operations: [
    {
      perm: true,
      text: '添加',
      iconifyIcon: 'ep:circle-plus',
      type: 'success',
      click: row => addMonitor(row)
    }
  ],
  dataList: [],
  columns: [
    { label: '编号', prop: 'id' },
    { label: '名称', prop: 'name' },
    { label: '设备类型', prop: 'deviceTypeName' }
  ]
})
const refreshDevice = () => {
  const query = refSearch.value?.queryParams || {}
  GetDmaPlantMonitorDevices({
    page: TableConfig_Device.pagination.page || 1,
    size: TableConfig_Device.pagination.limit || 20,
    name: query.name
  }).then(res => {
    const data = res.data.data || {}
    TableConfig_Device.dataList = data.data || []
    TableConfig_Device.pagination.total = data.total || 0
  })
}
const refSearch_Attr = ref<ISearchIns>()
const SearchConfig_Attr = reactive<ISearch>({
  filters: [
    { type: 'input', label: '水厂名称', field: 'name' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshAttr()
        }
      ]
    }
  ]
})
const TableConfig_Attr = reactive<ITable>({
  height: 250,
  pagination: {
    hide: true,
    refreshData({ page, size }) {
      TableConfig_Attr.pagination.page = page
      TableConfig_Attr.pagination.limit = size
      refreshAttr()
    }
  },
  dataList: [],
  columns: [
    { label: '水厂名称', prop: 'factoryName' },
    { label: '编号', prop: 'deviceId' },
    {
      label: '排序',
      prop: 'orderNum',
      formItemConfig: {
        type: 'input-number',
        onChange: (value, row) => changeOrder(value, row)
      }
    },
    { label: '流量计名称', prop: 'deviceName' },
    {
      label: '供水方向',
      prop: 'direction',
      formatter(row, value) {
        return SupplyDirections[value]
      }
    }
  ],
  operationWidth: 150,
  operations: [
    {
      perm: true,
      text: '变更方向',
      iconifyIcon: 'ep:edit-pen',
      click: row => changeDirection(row)
    },
    {
      perm: true,
      text: '移出',
      type: 'danger',
      iconifyIcon: 'ep:delete',
      click: row => removeRow(row)
    }
  ]
})
const refreshAttr = async () => {
  const res = await GetDmaPlantMonitorList({
    name: refSearch_Attr.value?.queryParams?.name
  })
  TableConfig_Attr.dataList = res.data.data
}
const changeDirection = async (row: any) => {
  try {
    const res = await EditDmaPlantMonitorDirection(row.id)
    if (res.data.code === 200) {
      SLMessage.success('修改成功')
      refreshAttr()
    } else {
      SLMessage.error(res.data.message)
    }
  } catch (error) {
    SLMessage.error('修改失败')
  }
}
const changeOrder = async (value: any, row: any) => {
  try {
    const res = await EditDmaPlantMonitorOrder({
      id: row.id,
      orderNum: value
    })
    if (res.data.code === 200) {
      SLMessage.success('修改成功')
      refreshAttr()
    }
  } catch (error) {
    SLMessage.error('操作失败')
  }
}
const removeRow = async row => {
  const ids = row ? [row.id] : []
  if (!ids.length) {
    SLMessage.warning('请先选择要移除的监测点')
    return
  }
  SLConfirm('确定移出?', '提示信息')
    .then(async () => {
      try {
        await RemoveDmaPlantMonitorPoint(ids)
        refreshAttr()
        refreshDevice()
        SLMessage.success('移出成功')
      } catch (error) {
        SLMessage.error('移出失败')
      }
    })
    .catch(() => {
      //
    })
}
const addMonitor = async row => {
  if (!row) return
  TableConfig_Device.currentRow = row
  refSearch_Device.value?.Submit()
}
onMounted(() => {
  refreshDevice()
  refreshAttr()
})
</script>
<style lang="scss" scoped>
.aou-wrapper {
  width: 100%;
  height: 600px;
  padding-right: 8px;
  border: 1px solid var(--el-border-color-lighter);
  .search-box {
    padding: 12px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  .table-box {
    border-bottom: 1px solid var(--el-border-color-lighter);
    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
