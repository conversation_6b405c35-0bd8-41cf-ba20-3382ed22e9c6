package org.thingsboard.server.dao.report;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.report.ReportDatabase;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-06-07
 */
public interface ReportDatabaseService {

    ReportDatabase save(ReportDatabase reportDatabase);

    PageData<ReportDatabase> getList(int page, int size, String name, String tenantId);

    void delete(List<String> ids);

    boolean testConnect(ReportDatabase reportDatabase);

    IstarResponse testSql(String databaseId, String sql, JSONObject param);

    IstarResponse testSqlById(String id, JSONObject param);

}
