package org.thingsboard.server.dao.shuiwu.assets;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.UserEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsInventoryCEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsInventoryEntity;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsAccountRepository;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsInventoryCRepository;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsInventoryRepository;
import org.thingsboard.server.dao.sql.user.UserRepository;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
@Service
@Slf4j
public class AssetsInventoryServiceImpl implements AssetsInventoryService{
    @Autowired
    private AssetsInventoryRepository assetsInventoryRepository;
    @Autowired
    private AssetsInventoryCRepository assetsInventoryCRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private AssetsAccountRepository assetsAccountRepository;

    @Override
    public PageData getPage(JSONObject params) {
        int page = 0;
        int limit = 10;
        String inventoryNo = "";
        String status = "";
        String reviewerId = "";
        Long start = 0l;
        Long end = System.currentTimeMillis();

        if (params.getInteger("page") != null) {
            page = params.getInteger("page") - 1;
        }

        if (params.getInteger("limit") != null) {
            limit = params.getInteger("limit");
        }

        if (StringUtils.isNotBlank(params.getString("inventoryNo"))) {
            inventoryNo = params.getString("inventoryNo");
        }
        inventoryNo = "%" + inventoryNo + "%";

        if (StringUtils.isNotBlank(params.getString("status"))) {
            status = params.getString("status");
        }
        status = "%" + status + "%";

        if (StringUtils.isNotBlank(params.getString("reviewerId"))) {
            reviewerId = params.getString("reviewerId");
        }
        reviewerId = "%" + reviewerId + "%";

        if (params.getLong("start") != null) {
            start = params.getLong("start");
        }

        if (params.getLong("end") != null) {
            end = params.getLong("end");
        }

        String tenantId = params.getString("tenantId");
        PageRequest pageRequest = new PageRequest(page, limit, new Sort(Sort.Direction.DESC, "createTime"));

        Page<AssetsInventoryEntity> inventoryEntityPage = assetsInventoryRepository.findAllByInventoryNoLikeAndInventoryPersonIdsLikeAndTenantIdLikeAndCreateTimeBetween(inventoryNo, reviewerId, tenantId, start, end, pageRequest);
        PageData<AssetsInventoryEntity> pageData = new PageData<>(inventoryEntityPage.getTotalElements(), inventoryEntityPage.getContent());

        for (AssetsInventoryEntity assetsInventoryEntity : pageData.getData()) {
            // 获取盘点人
            if (StringUtils.isNotBlank(assetsInventoryEntity.getInventoryPersonIds())) {
                String[] assetsStrings = assetsInventoryEntity.getInventoryPersonIds().split(",");
                String inventoryPersonNames = "";
                String userName;
                for (String userId : assetsStrings) {
                    UserEntity userEntity = userRepository.findOne(userId);
                    userName = userId;
                    if (userEntity != null && StringUtils.isNotBlank(userEntity.getFirstName())) {
                        userName = userEntity.getFirstName();
                    }
                    inventoryPersonNames = inventoryPersonNames + "," + userName;
                }
                if (inventoryPersonNames.length() > 0) {
                    inventoryPersonNames = inventoryPersonNames.substring(1);
                    assetsInventoryEntity.setInventoryPersonNames(inventoryPersonNames);
                }
            }

            // 状态  0未盘点  1部分盘点  2已盘点
            String allStatus = "0";
            boolean have0 = false;
            boolean have1 = false;
            List<AssetsInventoryCEntity> allByPidOrderByCreateTimeDesc = assetsInventoryCRepository.findAllByPidOrderByCreateTimeDesc(assetsInventoryEntity.getId());
            for (AssetsInventoryCEntity assetsInventoryCEntity : allByPidOrderByCreateTimeDesc) {
                if ("0".equals(assetsInventoryCEntity.getStatus())) have0 = true;
                if ("2".equals(assetsInventoryCEntity.getStatus())) have1 = true;
                // 盘点人
                if (StringUtils.isNotBlank(assetsInventoryCEntity.getInventoryPersonId())) {
                    UserEntity one = userRepository.findOne(assetsInventoryCEntity.getInventoryPersonId());
                    if (one != null) {
                        assetsInventoryCEntity.setInventoryPersonName(one.getFirstName());
                    }
                }

                // 获取设备台账信息
                if (StringUtils.isBlank(assetsInventoryCEntity.getDeviceId())){ continue;}

                AssetsAccountEntity assetsAccount = assetsAccountRepository.findOne(assetsInventoryCEntity.getDeviceId());
                if (assetsAccount == null) continue;

                // 设备编号
                assetsInventoryCEntity.setDeviceNo(assetsAccount.getDeviceNo());
                // 设备名称
                assetsInventoryCEntity.setDeviceName(assetsAccount.getDeviceName());
                // 规格型号
                assetsInventoryCEntity.setSpecificationModel(assetsAccount.getSpecificationModel());
            }
            assetsInventoryEntity.setInventoryCList(allByPidOrderByCreateTimeDesc);

            if (have0 && !have1) allStatus = "0";
            if (have0 && have1) allStatus = "1";
            if (!have0 && have1) allStatus = "2";

            assetsInventoryEntity.setStatus(allStatus);
        }
        return pageData;

    }

    @Override
    public AssetsInventoryEntity save(AssetsInventoryEntity inventoryEntity, String userId) throws ThingsboardException {
        if (StringUtils.isBlank(inventoryEntity.getId())) {
            inventoryEntity.setCreateTime(System.currentTimeMillis());
        }
        // 盘点编号
        if (StringUtils.isBlank(inventoryEntity.getInventoryNo())) {
            SimpleDateFormat format = new SimpleDateFormat("yyMMddHHmmssSSS");
            String deviceNo = "PD" + format.format(new Date());
            inventoryEntity.setInventoryNo(deviceNo);

        }

        inventoryEntity.setUpdateTime(System.currentTimeMillis());
        if (StringUtils.isBlank(inventoryEntity.getInventoryPersonIds())) {
            inventoryEntity.setInventoryPersonIds("");
        }

        assetsInventoryRepository.save(inventoryEntity);

        // 设备盘点
        if (inventoryEntity.getInventoryCList() != null && inventoryEntity.getInventoryCList().size() > 0) {
            for (AssetsInventoryCEntity assetsInventoryCEntity : inventoryEntity.getInventoryCList()) {
                if (StringUtils.isBlank(assetsInventoryCEntity.getId())) {
                    assetsInventoryCEntity.setCreateTime(System.currentTimeMillis());
                }
                assetsInventoryCEntity.setPid(inventoryEntity.getId());
                assetsInventoryCEntity.setUpdateTime(System.currentTimeMillis());
                assetsInventoryCEntity.setTenantId(inventoryEntity.getTenantId());

                // 状态
                if (StringUtils.isBlank(assetsInventoryCEntity.getStatus())) {
                    assetsInventoryCEntity.setStatus("0");
                }

                // 盘点人
                if (StringUtils.isBlank(assetsInventoryCEntity.getInventoryPersonId())) {
                    assetsInventoryCEntity.setInventoryPersonId("");
                }

                // 第一次盘点
                if ("2".equals(assetsInventoryCEntity.getStatus())) {
                    AssetsInventoryCEntity one = assetsInventoryCRepository.findOne(assetsInventoryCEntity.getId());
                    if (one != null && !"2".equals(one.getStatus())) {
                        // 是否为盘点人
                        if (!inventoryEntity.getInventoryPersonIds().contains(userId)) {
                            throw new ThingsboardException("您没有该盘点权限！", ThingsboardErrorCode.GENERAL);
                        }
                        assetsInventoryCEntity.setInventoryPersonId(userId);
                    }
                }

                // 备注
                if (StringUtils.isBlank(assetsInventoryCEntity.getRemark())) {
                    assetsInventoryCEntity.setRemark("");
                }

                assetsInventoryCRepository.save(assetsInventoryCEntity);
            }
        }

        return inventoryEntity;
    }

    @Override
    public void delete(List<String> idList) {
        for (String id : idList) {
            // 查找
            assetsInventoryRepository.delete(id);
        }
    }
}
