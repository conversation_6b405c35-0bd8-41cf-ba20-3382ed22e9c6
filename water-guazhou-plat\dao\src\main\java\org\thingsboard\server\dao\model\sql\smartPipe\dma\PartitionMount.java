package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 * 分区挂接
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_PARTITION_MOUNT_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PartitionMount {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(exist = false)
    private String name;

    @TableField(ModelConstants.PIPE_PARTITION_MOUNT_PARTITION_ID)
    private String partitionId;

    @TableField(ModelConstants.PIPE_PARTITION_MOUNT_DEVICE_ID)
    private String deviceId;

    @TableField(ModelConstants.PIPE_PARTITION_MOUNT_DIRECTION)
    private String direction;

    @TableField(ModelConstants.PIPE_PARTITION_MOUNT_TYPE)
    private String type;

    @TableField(ModelConstants.PIPE_PARTITION_MOUNT_IS_ACCOUNT)
    private String isAccount;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
