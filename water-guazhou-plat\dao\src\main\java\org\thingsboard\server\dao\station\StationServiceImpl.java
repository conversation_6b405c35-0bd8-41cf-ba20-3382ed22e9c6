package org.thingsboard.server.dao.station;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.*;
import org.thingsboard.server.dao.model.VO.StationWarnSetVO;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRule;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.sql.alarmV2.AlarmRuleMapper;
import org.thingsboard.server.dao.sql.station.StationRepository;
import org.thingsboard.server.dao.util.ExcelImportUtil;
import org.thingsboard.server.dao.util.InfluxUtil;
import org.thingsboard.server.dao.util.TreeUtil;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.CacheConstants.STATION_ATTR_CACHE;
import static org.thingsboard.server.common.data.CacheConstants.STATION_CACHE;

@Slf4j
@Service
public class StationServiceImpl implements StationService {

    @Autowired
    private StationRepository stationRepository;

    @Autowired
    private StationAttrService stationAttrService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private InfluxUtil influxUtil;

    @Autowired
    private AlarmRuleMapper alarmRuleMapper;

    @Override
    @Transactional
    @CacheEvict(cacheNames = {STATION_CACHE, STATION_ATTR_CACHE}, allEntries = true)
    public void save(StationEntity station) {
        StationEntity save = stationRepository.save(station);

        // 保存站点变量
        List<StationAttrDTO> dtoList = station.getStationAttrInfo();
        String stationId = save.getId();
        // 先删除
        stationAttrService.deleteByStationId(stationId);
        for (StationAttrDTO dto : dtoList) {
            String type = dto.getType();
            List<StationAttrEntity> attrList = dto.getAttrList();

            for (StationAttrEntity attr : attrList) {
                attr.setStationId(stationId);
                attr.setType(type);
                attr.setTenantId(station.getTenantId());
                stationAttrService.save(attr);
            }
        }
    }

    @Override
    public PageData<StationEntity> list(int page, int size, JSONObject params, TenantId tenantId) {
        PageRequest pageRequest = new PageRequest(page - 1, size, new Sort(Sort.Direction.ASC, "createTime", "orderNum"));
        String type = params.getString("type");
        List<String> typeList = new ArrayList<>();
        if (StringUtils.isBlank(type)) {
            typeList.add("水厂");
            typeList.add("泵站");
            typeList.add("压力监测站");
            typeList.add("水池监测站");
            typeList.add("污水处理厂");
            typeList.add("流量监测站");
            typeList.add("水质监测站");
            typeList.add("水源地");
            typeList.add("消防栓");
            typeList.add("热水井");
            typeList.add("测流压站");
            typeList.add("大用户");
            typeList.add("阀门");
            typeList.add("医院");
            typeList.add("公安局");
            typeList.add("游客服务中");
            typeList.add("地标");
            typeList.add("派出所");
            typeList.add("旅馆");
            typeList.add("温泉酒店");
        } else {
            typeList = Arrays.asList(type.split(","));
        }
        String projectId = params.getString("projectId");
        List<String> projectIdList = new ArrayList<>();
        List<ProjectEntity> list = new ArrayList<>();
        if (StringUtils.isBlank(projectId)) {
            list = projectService.findByTenantId(tenantId);
        } else {
            list = projectService.findAllChild(tenantId, projectId);
        }
        projectIdList = list.stream().map(ProjectEntity::getId).collect(Collectors.toList());
        projectIdList.add(projectId);

        Page<StationEntity> pageResult = stationRepository.findList(UUIDConverter.fromTimeUUID(tenantId.getId()), typeList, projectIdList, pageRequest);

        List<StationEntity> content = pageResult.getContent();
        for (StationEntity stationEntity : content) {
            stationEntity.setInfo(JSON.parseObject(stationEntity.getAdditionalInfo()));
        }

        return new PageData<>(pageResult.getTotalElements(), content);
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames = STATION_CACHE, allEntries = true)
    public void remove(List<String> ids) {
        for (String id : ids) {
            stationRepository.delete(id);
            // 删除站点的关联关系表数据
            stationAttrService.deleteByStationId(id);
        }
    }

    @Override
    public List<StationAttrEntity> getAttrList(String stationId, String type) {
        return stationAttrService.getList(stationId, type);
    }

    @Override
    public List<StationAttrEntity> getAttrList(List<String> stationIdList, String type) {
        return stationAttrService.getList(stationIdList, type);
    }

    @Override
    public StationEntity findById(String id) {
        return stationRepository.findOne(id);
    }

    @Override
    public List<StationEntity> findByStationIdList(String stationType, List<String> stationIdList) {
        if (StringUtils.isBlank(stationType)) {
            stationType = "%%";
        }
        return stationRepository.findByIdInOrderByOrderNum(stationIdList);
    }

    @Override
    public List<StationAttrDTO> getAllAttrList(String stationId) {
        // 查询站点的类型分组
        List<String> groupTypeList = stationAttrService.groupByStationId(stationId);
        Map<String, List<StationAttrEntity>> typeMap = new LinkedHashMap<>();
        for (String type : groupTypeList) {
            typeMap.put(type, new ArrayList<>());
        }

        // 统计各个类型的动态属性列表
        List<StationAttrEntity> stationAttrList = stationAttrService.findByStation(stationId);
        for (StationAttrEntity stationAttr : stationAttrList) {
            String type = stationAttr.getType();
            List<StationAttrEntity> typeList = typeMap.get(type);
            if (typeList == null) {
                typeList = new ArrayList<>();
            }
            typeList.add(stationAttr);

            typeMap.put(type, typeList);
        }

        // 处理map返回
        List<StationAttrDTO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<StationAttrEntity>> entry : typeMap.entrySet()) {
            String type = entry.getKey();
            List<StationAttrEntity> typeList = entry.getValue();

            StationAttrDTO obj = new StationAttrDTO();
            obj.setType(type);
            obj.setAttrList(typeList);

            resultList.add(obj);
        }

        return resultList;
    }

    @Override
    public List<String> getAttrGroupNames(String stationId) {
        return stationAttrService.groupByStationId(stationId);
    }

    @Override
    @Cacheable(cacheNames = STATION_CACHE, key = "{#stationId}")
    public List<StationAttrEntity> getStationAllAttrList(String stationId) {
        List<StationAttrDTO> allAttrList = this.getAllAttrList(stationId);
        if (allAttrList == null || allAttrList.isEmpty()) {
            return new ArrayList<>();
        }

        List<StationAttrEntity> resultList = new ArrayList<>();
        for (StationAttrDTO stationAttrDTO : allAttrList) {
            List<StationAttrEntity> attrList = stationAttrDTO.getAttrList();
            if (attrList != null) {
                resultList.addAll(attrList);
            }
        }

        return resultList;
    }

    @Override
    public Object getRange(String stationId) {
        List<StationAttrEntity> stationAttrList = stationAttrService.findByStation(stationId);
        Map<String, String> rangeMap = new HashMap<>();

        for (StationAttrEntity stationAttr : stationAttrList) {
            String attr = stationAttr.getAttr();
            String range = stationAttr.getRange();
            if (StringUtils.isNotBlank(range)) {
                String[] rangeArray = range.split(",");
                if (StringUtils.isNotBlank(rangeArray[1])) {
                    rangeMap.put(attr, rangeArray[1]);
                } else if (StringUtils.isNotBlank(rangeArray[0])) {
                    rangeMap.put(attr, rangeArray[0]);
                }
            }
        }

        return rangeMap;
    }

    @Override
    public StationAttrEntity findAttrById(String attributeId) {
        return stationAttrService.findById(attributeId);
    }

    @Override
    public List<StationAttrEntity> findAttrByIdList(String attributes) {
        return stationAttrService.findByIdIn(Arrays.stream(attributes.split(",")).collect(Collectors.toList()));
    }

    @Override
    @Cacheable(cacheNames = STATION_CACHE, key = "{#stationIdList}")
    public List<StationEntity> findByStationIdList(List<String> stationIdList) {
        return stationRepository.findByIdInOrderByOrderNum(stationIdList);
    }

    @Override
    public List<TreeNodeDTO> simpleTree(String type, TenantId tenantId) {
        // 查询项目列表
        List<ProjectEntity> projectList = projectService.findByTenantId(tenantId);

        // 查询站点列表
        List<StationEntity> stationList;
        if (StringUtils.isNotBlank(type)) {
            List<String> typeList = Arrays.asList(type.split(","));
            stationList = stationRepository.findByTypeInAndTenantIdOrderByOrderNum(typeList, UUIDConverter.fromTimeUUID(tenantId.getId()));
        } else {
            stationList = stationRepository.findByTenantIdOrderByOrderNum(UUIDConverter.fromTimeUUID(tenantId.getId()));
        }

        // 转换为数节点对象
        List<TreeNodeDTO> projectNodeList = projectList.stream().map(project -> TreeNodeDTO.builder()
                .id(project.getId())
                .parentId(project.getParentId())
                .name(project.getName())
                .type(DataConstants.TREE_NODE_TYPE.PROJECT.getValue())
                .typeName(DataConstants.TREE_NODE_TYPE.PROJECT.getName())
                .nodeDetail(project)
                .build()).collect(Collectors.toList());

        List<TreeNodeDTO> stationNodeList = stationList.stream().map(station -> TreeNodeDTO.builder()
                .id(station.getId())
                .parentId(station.getProjectId())
                .name(station.getName())
                .type(DataConstants.TREE_NODE_TYPE.STATION.getValue())
                .typeName(DataConstants.TREE_NODE_TYPE.STATION.getName())
                .nodeDetail(station)
                .build()).collect(Collectors.toList());

        List<TreeNodeDTO> nodeList = new ArrayList<>();
        nodeList.addAll(projectNodeList);
        nodeList.addAll(stationNodeList);

        // 组成树
        return TreeUtil.listToTree(nodeList, DataConstants.ROOT_PROJECT_PARENT_ID);
    }

    @Override
    @Transactional
    public void importStation(MultipartFile file, TenantId tenantId) throws ThingsboardException {
        if (file.getOriginalFilename() == null || (!file.getOriginalFilename().endsWith("xlsx") && !file.getOriginalFilename().endsWith("xls"))) {
            throw new ThingsboardException("上传文件错误", ThingsboardErrorCode.GENERAL);
        }
        XSSFWorkbook workbook;
        try {
            workbook = new XSSFWorkbook(file.getInputStream());
        } catch (IOException e) {
            throw new ThingsboardException("上传文件错误", ThingsboardErrorCode.GENERAL);
        }

        List<StationImportDTO> stationImportList = ExcelImportUtil.importExcel(workbook, StationImportDTO.class);
        List<StationEntity> stationList = stationImportList
                .stream()
                .map(stationImportDTO -> {
                    StationEntity station = new StationEntity();
                    BeanUtils.copyProperties(stationImportDTO, station);
                    station.setCreateTime(new Date());
                    station.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
                    return station;
                })
                .filter(station -> StringUtils.isNotBlank(station.getName()) && StringUtils.isNotBlank(station.getType()) && StringUtils.isNotBlank(station.getProjectId()))
                .collect(Collectors.toList());

        // 保存站点
        stationRepository.save(stationList);
    }

    @Override
    public List<CountObjDTO> typeCount(List<String> typeList, String projectId, TenantId tenantId) {
        if (typeList.isEmpty()) {
            typeList.add("水厂");
            typeList.add("泵站");
            typeList.add("压力监测站");
            typeList.add("水池监测站");
            typeList.add("污水处理厂");
            typeList.add("流量监测站");
            typeList.add("水质监测站");
            typeList.add("消防栓");
            typeList.add("热水井");
            typeList.add("水源地");
            typeList.add("测流压站");
            typeList.add("大用户");
        }
        List<CountObjDTO> counts;
        if (StringUtils.isBlank(projectId)) {
            counts = stationRepository.typeCount(typeList, UUIDConverter.fromTimeUUID(tenantId.getId()));
        } else {
            counts = stationRepository.typeCountProjectId(typeList, UUIDConverter.fromTimeUUID(tenantId.getId()), projectId);
        }

        // 结果处理
        Map<String, CountObjDTO> countMap = counts.stream().collect(Collectors.toMap(CountObjDTO::getKey, count -> count));
        List<CountObjDTO> resultList = new ArrayList<>();
        for (String type : typeList) {
            CountObjDTO countObj = countMap.get(type);
            if (countObj == null) {
                countObj = new CountObjDTO(type, 0);
            }
            resultList.add(countObj);
        }

        return resultList;
    }

    @Override
    @Cacheable(cacheNames = STATION_CACHE, key = "{#tenantId}")
    public List<StationEntity> findByTenantId(TenantId tenantId) {
        return stationRepository.findByTenantIdOrderByOrderNum(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public BigDecimal getWaterPermit(String name, String tenantId) {
        // 获取水源地站点
        if (StringUtils.isBlank(name) || name.contains("overview")) {
            name = "%%";
        }
        List<StationEntity> stationList = stationRepository.findAllByNameLikeAndTypeAndTenantId(name, "水源地", tenantId);
        BigDecimal water = BigDecimal.ZERO;
        for (StationEntity stationEntity : stationList) {
            try {
                water = water.add(JSONObject.parseObject(stationEntity.getAdditionalInfo()).getBigDecimal("waterPermit"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return water;
    }

    @Override
    public StationWarnSetVO getWarnReference(String deviceId, String attr, Long start, Long end, Double diffValue) {
        List<String> deviceIdList = new ArrayList<>();
        String deviceAttr = deviceId + "." + attr;
        deviceIdList.add(deviceId + "." + attr);
        if (start == null) {
            start = LocalDate.now().minusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        }
        if (end == null) {
            end = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        }
        if (end - start < (25 * 60 * 60 * 1000)) {
            start = LocalDate.now().minusMonths(3L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            end = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        }
        JSONObject data = influxUtil.getData(deviceIdList, start, end, "30m");
        List<Object> collect = data.values().stream().collect(Collectors.toList());
        List<BigDecimal> values = new ArrayList<>();
        for (Object o : collect) {
            if (((JSONObject) o).get(deviceAttr) == null) {
                continue;
            }
            values.add(((JSONObject) o).getBigDecimal(deviceAttr));
        }
        StationWarnSetVO result = new StationWarnSetVO();
        int dataNum = values.size();
        result.setDataNum(dataNum);

        // 数据处理 如果最大值减最小值大于0.2,则去掉最大值最小值，循环比较，指到最大值小值差小于0.2则返回最大值最小值差
        if (diffValue == null) {
            diffValue = 0.2;
        }
        if (values.size() > 1) {
            BigDecimal max = values.stream().max(BigDecimal::compareTo).get();
            BigDecimal min = values.stream().min(BigDecimal::compareTo).get();
            result.setMax(max);
            result.setMin(min);
            while (max.subtract(min).compareTo(new BigDecimal(diffValue)) > 0 && values.size() > 1) {
                values.remove(max);
                values.remove(min);
                max = values.stream().max(BigDecimal::compareTo).get();
                min = values.stream().min(BigDecimal::compareTo).get();
            }
            result.setUpLimit(max);
            result.setDownLimit(min);
        }

        return result;
    }

    @Override
    public IstarResponse batchSet(JSONObject params, String tenantId) {
        String type = params.getString("type");
        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("类型不能为空");
        }
        String attr = params.getString("attr");
        if (StringUtils.isBlank(attr)) {
            return IstarResponse.error("属性不能为空");
        }
        String alarmLevel = params.getString("alarmLevel");
        if (StringUtils.isBlank(alarmLevel)) {
            alarmLevel = "3";
        }
        String alarmType = params.getString("alarmType");
        if (StringUtils.isBlank(alarmType)) {
            alarmType = "10";
        }
        String processMethod = params.getString("processMethod");
        if (StringUtils.isBlank(processMethod)) {
            processMethod = "请及时处理";
        }
        String reAlarmType = params.getString("reAlarmType");
        if (StringUtils.isBlank(reAlarmType)) {
            reAlarmType = "1";
        }
        String ruleType = params.getString("ruleType");
        if (StringUtils.isBlank(ruleType)) {
            ruleType = "6";
        }
        String status = params.getString("status");
        if (StringUtils.isBlank(status)) {
            status = "1";
        }

        Long start = params.getLong("start");
        Long end = params.getLong("end");
        Double diffValue = params.getDouble("diffValue");

        List<StationEntity> stationList = stationRepository.findAllByNameLikeAndTypeAndTenantId("%%", type, tenantId);
        if (stationList.size() == 0) {
            return IstarResponse.error("未找到相关站点");
        }
        Map<String, String> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, StationEntity::getName));
        // 查找站点下的属性
        List<AlarmRule> alarmRuleList = new ArrayList<>();
        AlarmRule alarmRule;
        List<String> stationAttrIdList = new ArrayList<>();
        List<String> stationIdList = stationList.stream().map(a -> a.getId()).collect(Collectors.toList());
            List<StationAttrEntity> attrList = stationAttrService.findByStationIdInAndAttr(stationIdList, attr);
            for (StationAttrEntity stationAttrEntity : attrList) {
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                StationWarnSetVO warnReference = this.getWarnReference(deviceId, attr, start, end, diffValue);

                alarmRule = new AlarmRule();
                alarmRule.setStationId(stationAttrEntity.getStationId());
                alarmRule.setStationAttrId(stationAttrEntity.getId());
                alarmRule.setAlarmType(alarmType);
                alarmRule.setTitle(stationMap.get(stationAttrEntity.getStationId()));
                alarmRule.setSendWay("{\"app\":true,\"sms\":false,\"wechat\":false}");
                alarmRule.setReAlarmType(reAlarmType);
                alarmRule.setType("1");
                alarmRule.setTenantId(tenantId);
                alarmRule.setCreateTime(new Date());
                alarmRule.setProcessMethod(processMethod);
                alarmRule.setAlarmLevel(alarmLevel);
                alarmRule.setStatus(status);
                alarmRule.setRuleType(ruleType);
                JSONObject object = new JSONObject();
                object.put("x", warnReference.getDownLimit() + "");
                object.put("y", warnReference.getUpLimit() + "");
                alarmRule.setRuleParam(object.toJSONString());

                alarmRuleList.add(alarmRule);
                stationAttrIdList.add(stationAttrEntity.getId());
            }

            // 删除
        if (alarmRuleList.size() > 0) {
            QueryWrapper<AlarmRule> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.in("station_attr_id", stationAttrIdList);

            alarmRuleMapper.delete(deleteWrapper);
            alarmRuleMapper.batchInsert(alarmRuleList);
        }

        return IstarResponse.ok();
    }

    @Override
    public IstarResponse getReferenceValue(JSONObject params, String tenantId) {
        String attr = params.getString("attr");
        if (StringUtils.isBlank(attr)) {
            return IstarResponse.error("属性不能为空");
        }
        List<String> stationIdList = params.getObject("stationIdList", List.class);

        Long start = params.getLong("start");
        Long end = params.getLong("end");
        Double diffValue = params.getDouble("referenceValue");

        List<StationEntity> stationList = stationRepository.findByIdInOrderByOrderNum(stationIdList);
        if (stationList.size() == 0) {
            return IstarResponse.error("未找到相关站点");
        }
        Map<String, String> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, StationEntity::getName));
        // 查找站点下的属性
        List<AlarmRuleStationParamDTO> alarmRuleList = new ArrayList<>();
        AlarmRuleStationParamDTO alarmRule;
        List<StationAttrEntity> attrList = stationAttrService.findByStationIdInAndAttr(stationIdList, attr);
        for (StationAttrEntity stationAttrEntity : attrList) {
            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
            StationWarnSetVO warnReference = this.getWarnReference(deviceId, attr, start, end, diffValue);
            alarmRule = new AlarmRuleStationParamDTO();
            alarmRule.setAttr(attr);
            alarmRule.setDataNum(warnReference.getDataNum());
            alarmRule.setStationId(stationAttrEntity.getStationId());
            alarmRule.setStationName(stationMap.get(stationAttrEntity.getStationId()));
            alarmRule.setDeviceId(deviceId);
            alarmRule.setDeviceName(stationMap.get(stationAttrEntity.getStationId()));
            alarmRule.setUpLimitValue(warnReference.getUpLimit());
            alarmRule.setDownLimitValue(warnReference.getDownLimit());
            alarmRule.setMaxValue(warnReference.getMax());
            alarmRule.setMinValue(warnReference.getMin());
            alarmRule.setType("1");
            alarmRuleList.add(alarmRule);

        }
        return IstarResponse.ok(alarmRuleList);
    }
}
