import{d as c,c as u,r as f,u as r,S as d,b as g,g as S,h as y,i as b,_ as v}from"./index-r0dFAfgr.js";const m=c({__name:"SMART_DECISION_CONFIG",setup(I){const l=u(),a=f({group:[{fields:[{type:"input",label:"GISWebApi",field:"gisApi",placeholder:"http://xxx:8001/webapi"},{type:"input",label:"GISSDKKey",field:"gisApiKey"},{type:"input",label:"GISSDKAPI",field:"gisSDK",placeholder:"http://www.xxx.com/arcgis_js_api/javascript/4.26"},{type:"input",label:"天地图Key",field:"gisTdtToken"},{type:"input",label:"GIS服务地址",field:"gisService",placeholder:"http://xxx:6080/arcgis/rest/services/ANQING/"},{type:"input",label:"GIS代理地址",field:"gisProxyService",placeholder:"/arcgis/rest/services/ANQING/"},{type:"input",label:"地图默认中心点坐标",field:"gisDefaultCenter",placeholder:"107.889659, 26.939568"},{type:"number",label:"地图默认缩放级别",field:"gisDefaultZoom",placeholder:"14"},{type:"input",label:"管线切片地图服务路径",field:"gisPipeDataService"},{type:"input",label:"管线动态地图服务路径",field:"gisPipeDynamicService"},{type:"input",label:"管线要素服务路径",field:"gisPipeFeatureServiceFeatureServer"},{type:"input",label:"管线要素地图服务路径",field:"gisPipeFeatureServiceMapServer"},{type:"input",label:"爆管分析路径",field:"gisBurstGPService"},{type:"input",label:"连通性分析路径",field:"gisConnectGPService"},{type:"input",label:"沿线分析路径",field:"gisPathAnalysGPService"},{type:"input",label:"关阀分析路径",field:"gisShutValveAnalysGPService"},{type:"input",label:"关阀分析路径",field:"gisShutValveAnalysGPService"},{type:"input",label:"二次关阀分析路径",field:"gisExtendShutValveAnalysGPService"},{type:"input",label:"放大镜服务路径",field:"gisFangDaGPService"},{type:"input",label:"几何服务路径（Utilities）",field:"gisGeometryService"},{type:"input",label:"模板打印服务路径",field:"gisPrintTemplatePath"},{type:"input",label:"地图打印服务路径",field:"gisPrintGPService"},{type:"input",label:"打印工具服务路径",field:"gisPrintingToolsGPService"}]},{fields:[{type:"btn-group",btns:[{perm:!0,text:"保存",click:()=>{var e;return(e=l.value)==null?void 0:e.Submit()}},{perm:!0,text:"重置",click:()=>{var e;return(e=l.value)==null?void 0:e.resetForm()}}]}]}],labelPosition:"right",labelWidth:"100px",defaultValue:{},submit:e=>{({...s()},p(e))}}),n=()=>{var e,i,t;return{...((e=window.SITE_CONFIG)==null?void 0:e.GIS_CONFIG)||{},...((t=(i=r().tenantInfo)==null?void 0:i.additionalInfo)==null?void 0:t.GIS_CONFIG)||{}}},s=()=>{var e;return{...window.SITE_CONFIG||{},...((e=r().tenantInfo)==null?void 0:e.additionalInfo)||{}}},p=e=>{d("确定保存？","提示信息").then(async()=>{a.submitting=!0;try{console.log(e),await r().InitTenantInfo(),o()}catch(i){g.error("保存失败"),console.log(i)}}).catch(()=>{})},o=()=>{a.defaultValue=n()};return(e,i)=>{const t=v;return S(),y(t,{ref_key:"refForm",ref:l,config:b(a)},null,8,["config"])}}});export{m as _};
