package org.thingsboard.server.controller;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.DTO.StationAttrDTO;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.station.StationService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("api/station")
public class StationController extends BaseController {

    @Autowired
    private StationService stationService;

    @GetMapping("{id}")
    public StationEntity get(@PathVariable String id) {
        return stationService.findById(id);
    }

    @GetMapping("list")
    public PageData<StationEntity> list(int page, int size,
                                        @RequestParam(required = false, defaultValue = "") String type,
                                        @RequestParam(required = false, defaultValue = "") String projectId) throws ThingsboardException {
        JSONObject params = new JSONObject();
        params.put("type", type);
        params.put("projectId", projectId);
        return stationService.list(page, size, params, getTenantId());
    }

    @GetMapping("findByStationIdList")
    public List<StationEntity> findByStationIdList(@RequestParam("stationType") String stationType, @RequestParam("stationIdList") List<String> stationIdList) {
        return stationService.findByStationIdList(stationType, stationIdList);
    }

    @GetMapping("findByIdList")
    public List<StationEntity> findByStationIdList(@RequestParam("stationIdList") String stationIds) {
        return stationService.findByStationIdList(Arrays.stream(stationIds.split(",")).collect(Collectors.toList()));
    }

    @GetMapping("findAttrById")
    public StationAttrEntity findAttrById(@RequestParam("attributeId") String attributeId) {
        return stationService.findAttrById(attributeId);
    }

    @GetMapping("findAttrByIdList")
    public List<StationAttrEntity> findAttrByIdList(@RequestParam("attributes") String attributes) {
        return stationService.findAttrByIdList(attributes);
    }


    @GetMapping("attrList")
    public List<StationAttrEntity> getAttrList(@RequestParam String stationId, @RequestParam String type) {
        return stationService.getAttrList(stationId, type);
    }

    @GetMapping("attrListByStationIds")
    public List<StationAttrEntity> getAttrList(@RequestParam List<String> stationId, @RequestParam String type) {
        return stationService.getAttrList(stationId, type);
    }

    @GetMapping("allAttrList")
    public List<StationAttrDTO> getAllAttrList(@RequestParam String stationId) {
        return stationService.getAllAttrList(stationId);
    }

    @GetMapping("stationAttrList")
    public List<StationAttrEntity> getStationAllAttrList(@RequestParam String stationId) {
        return stationService.getStationAllAttrList(stationId);
    }

    @GetMapping("attrGroupNames")
    public List<String> getAttrGroupNames(@RequestParam String stationId) {
        return stationService.getAttrGroupNames(stationId);
    }

    @GetMapping("getRange")
    public Object getRange(@RequestParam String stationId) {
        return stationService.getRange(stationId);
    }


    @PostMapping("save")
    public StationEntity save(@RequestBody StationEntity entity) throws ThingsboardException {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        }
        entity.setAdditionalInfo(entity.getInfo().toString());
        stationService.save(entity);
        return entity;
    }

    @DeleteMapping("remove")
    public List<String> remove(@RequestBody List<String> ids) {
        stationService.remove(ids);

        return ids;
    }

    @GetMapping("simpleTree")
    public IstarResponse simpleTree(@RequestParam(required = false) String type) throws ThingsboardException {
        return IstarResponse.ok(stationService.simpleTree(type, getTenantId()));
    }

    @PostMapping("import")
    public IstarResponse importStation(@RequestParam("file") MultipartFile file) throws ThingsboardException {
        stationService.importStation(file, getTenantId());
        return IstarResponse.ok();
    }

    @GetMapping("typeCount")
    public IstarResponse typeCount(@RequestParam(required = false) String types, @RequestParam(required = false) String projectId) throws ThingsboardException {
        List<String> typeList = new ArrayList<>();
        if (StringUtils.isNotBlank(types)) {
            typeList.addAll(Arrays.asList(types.split(",")));
        }
        return IstarResponse.ok(stationService.typeCount(typeList, projectId, getTenantId()));
    }

    /**
     * 盐亭定制 取水许可方量
     */
    @GetMapping("waterPermit")
    public IstarResponse getWaterPermit(String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(stationService.getWaterPermit(name, tenantId));
    }

    /**
     * 获取报警参考值
     */
    @GetMapping("warnReference")
    public IstarResponse getWarnReference(String stationId, String attr, Long start, Long end, Double diffValue) {
        return IstarResponse.ok(stationService.getWarnReference(stationId, attr, start, end, diffValue));
    }

    @PostMapping("batchSet")
    public IstarResponse batchSet(@RequestBody JSONObject params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return stationService.batchSet(params, tenantId);
    }

    @PostMapping("referenceValue")
    public IstarResponse getReferenceValue(@RequestBody JSONObject params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return stationService.getReferenceValue(params, tenantId);
    }

}
