// 智慧生产=二供管理-泵站巡检养护 /api
import request from '@/plugins/axios';

// 查询站点养护方案列表
export function getSchemeList(params?: any) {
  return request({
    url: '/api/stationCircuitScheme/list',
    method: 'get',
    params
  });
}

// 保存泵站养护方案
export function saveScheme(params?: any) {
  return request({
    url: '/api/stationCircuitScheme',
    method: 'post',
    data: params
  });
}

// 删除泵站养护方案
export function delScheme(params?: any) {
  return request({
    url: '/api/stationCircuitScheme/remove',
    method: 'delete',
    data: params
  });
}

// 查询泵房养护任务列表
export function getTaskList(params?: any) {
  return request({
    url: '/api/stationCircuitTask/list',
    method: 'get',
    params
  });
}

// 删除泵房养护任务
export function delTask(ids?: string[]) {
  return request({
    url: '/api/stationCircuitTask/remove',
    method: 'delete',
    data: ids || []
  });
}

// 新增泵房养护任务
export function saveTask(params?: any) {
  return request({
    url: '/api/stationCircuitTask',
    method: 'post',
    data: params
  });
}

// 完成泵房养护任务
export function completeTask(params?: any) {
  return request({
    url: '/api/stationCircuitTask/complete',
    method: 'post',
    data: params
  });
}

// 审核泵房养护任务
export function auditTask(params?: any) {
  return request({
    url: '/api/stationCircuitTask/audit',
    method: 'post',
    data: params
  });
}

// 接收泵房养护任务
export function receiveTask(params?: any) {
  return request({
    url: '/api/stationCircuitTask/receive',
    method: 'post',
    data: params
  });
}

// 查询泵房养护计划列表
export function getPlanList(params?: any) {
  return request({
    url: '/api/stationCircuitPlan/list',
    method: 'get',
    params
  });
}

// 删除泵房养护计划
export function delPlan(ids?: string[]) {
  return request({
    url: '/api/stationCircuitPlan/remove',
    method: 'delete',
    data: ids || []
  });
}

// 新增泵房养护计划
export function savePlan(params?: any) {
  return request({
    url: '/api/stationCircuitPlan',
    method: 'post',
    data: params
  });
}
