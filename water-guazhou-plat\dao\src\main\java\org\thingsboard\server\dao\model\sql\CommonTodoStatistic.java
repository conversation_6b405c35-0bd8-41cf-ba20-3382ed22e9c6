package org.thingsboard.server.dao.model.sql;

public class CommonTodoStatistic {
    // 活动二供巡检数量
    private final Integer circuitTaskCount;

    // 活动二供巡检数量
    private final Integer maintainTaskCount;

    // 活动巡检任务数量
    private final Integer smCircuitTaskCount;

    // 活动养护任务数量
    private final Integer smMaintainTaskCount;

    // 活动工单数量
    private final Integer workOrderCount;

    // 总数
    private final Integer total;

    public CommonTodoStatistic(Integer circuitTaskCount, Integer maintainTaskCount, Integer smCircuitTaskCount, Integer smMaintainTaskCount, Integer workOrderCount) {
        this.circuitTaskCount = circuitTaskCount;
        this.maintainTaskCount = maintainTaskCount;
        this.smCircuitTaskCount = smCircuitTaskCount;
        this.smMaintainTaskCount = smMaintainTaskCount;
        this.workOrderCount = workOrderCount;
        total = circuitTaskCount + smCircuitTaskCount + smMaintainTaskCount + workOrderCount;
    }

    public Integer getCircuitTaskCount() {
        return circuitTaskCount;
    }

    public Integer getSmCircuitTaskCount() {
        return smCircuitTaskCount;
    }

    public Integer getSmMaintainTaskCount() {
        return smMaintainTaskCount;
    }

    public Integer getWorkOrderCount() {
        return workOrderCount;
    }

    public Integer getTotal() {
        return total;
    }
}
