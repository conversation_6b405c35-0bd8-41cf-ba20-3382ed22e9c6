package org.thingsboard.server.dao.repair;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.MaintenanceStandardDetailDTO;
import org.thingsboard.server.dao.model.sql.MaintenanceStandardEntity;
import org.thingsboard.server.dao.sql.repair.MaintenanceStandardRepository;

import java.util.List;

@Slf4j
@Service
public class MaintenanceStandardServiceImpl implements MaintenanceStandardService {

    @Autowired
    private MaintenanceStandardRepository maintenanceStandardRepository;

    @Override
    public MaintenanceStandardEntity findById(String id) {
        MaintenanceStandardEntity entity = maintenanceStandardRepository.findOne(id);
        String detail = entity.getDetail();
        entity.setDetailList(JSON.parseArray(detail, MaintenanceStandardDetailDTO.class));
        return entity;
    }

    @Override
    public PageData<MaintenanceStandardEntity> findList(int page, int size, String name, String deviceType, User currentUser) {
        // 分页参数
        PageRequest pageable = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");

        Page<MaintenanceStandardEntity> pageResult = maintenanceStandardRepository.findList(name, UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()), deviceType, pageable);

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public MaintenanceStandardEntity save(MaintenanceStandardEntity entity) {
        List<MaintenanceStandardDetailDTO> detailList = entity.getDetailList();
        entity.setDetail(JSON.toJSONString(detailList));
        return maintenanceStandardRepository.save(entity);
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            maintenanceStandardRepository.delete(id);
        }
    }

    @Override
    public List<MaintenanceStandardEntity> findAll(String name, TenantId tenantId) {
        return maintenanceStandardRepository.findAll(name, UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public List<MaintenanceStandardEntity> findAll(String name, String tenantId) {
        return maintenanceStandardRepository.findAll(name, tenantId);
    }

}
