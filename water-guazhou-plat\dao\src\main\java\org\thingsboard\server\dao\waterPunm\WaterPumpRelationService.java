package org.thingsboard.server.dao.waterPunm;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.WaterPumpRelationEntity;

import java.util.List;

public interface WaterPumpRelationService {
    List<WaterPumpRelationEntity> findByWaterPumpId(String waterPumpId);

    List<WaterPumpRelationEntity> findByTenantId(TenantId tenantId);

    List<WaterPumpRelationEntity> findByType(String type, TenantId tenantId);
}
