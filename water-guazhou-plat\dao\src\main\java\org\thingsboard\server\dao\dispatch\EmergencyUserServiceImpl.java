package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyUser;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.sql.department.OrganizationMapper;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.EmergencyUserMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyUserPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyUserSaveRequest;

import java.util.Date;
import java.util.List;

@Service
public class EmergencyUserServiceImpl implements EmergencyUserService {
    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private EmergencyUserMapper mapper;

    @Override
    public IPage<EmergencyUser> findAllConditional(EmergencyUserPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public EmergencyUser save(EmergencyUserSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(EmergencyUser entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean sync(String userId, String tenantId) {
        // mapper.clear(tenantId);
        List<EmergencyUser> emergencyUsers = mapper.selectFromUserEntity(tenantId);
        return mapper.saveAll(emergencyUsers, userId, tenantId, new Date()) > 0;
    }

    @Override
    public List<Organization> treeUser(String tenantId, Integer depth) {
        List<Organization> roots = organizationMapper.findRoots(tenantId);
        if (depth > 2)
            return QueryUtil.buildTree(roots,
                    pid -> organizationMapper.findChildren(pid, tenantId),
                    pid -> departmentMapper.findChildren(pid, tenantId),
                    mapper::findByDepartmentId);

        return QueryUtil.buildTree(roots,
                pid -> organizationMapper.findChildren(pid, tenantId),
                pid -> departmentMapper.findChildren(pid, tenantId));
    }
}
