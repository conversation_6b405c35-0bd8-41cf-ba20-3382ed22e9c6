import{d as m,r as o,a8 as r,bF as n,g as y,n as b,q as t,F as f,i as c,aB as x,bz as u,C as g}from"./index-r0dFAfgr.js";import{d as _}from"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const C=m({__name:"fygl",props:{config:{}},setup(d){const l=d,p=o({defaultValue:r(()=>l.config),border:!0,direction:"horizontal",column:2,title:"费用明细基本信息",fields:[{type:"text",label:"资金编号:",field:"code"},{type:"text",label:"支付方式:",field:"paymentType"},{type:"text",label:"支付金额:",field:"cost"},{type:"text",label:"费用类型:",field:"type"},{type:"text",label:"一审审核金额(万元):",field:"firstVerifyCost"},{type:"text",label:"一审结算单位:",field:"firstVerifyOrganization"},{type:"text",label:"二审审核金额(万元):",field:"secondVerifyCost"},{type:"text",label:"二审结算单位:",field:"secondVerifyOrganization"},{type:"text",label:"代收款信息:",field:"payeeInfo"},{type:"text",label:"报批时间:",field:"approvalTime",formatter:e=>n(e).format("YYYY-MM-DD HH:mm:ss")},{type:"text",label:"提交财务处理时间:",field:"submitFinanceTime",formatter:e=>n(e).format("YYYY-MM-DD HH:mm:ss")},{type:"text",label:"申请人:",field:"creatorName"},{type:"text",label:"收款单位:",field:"payeeOrganization"},{type:"text",label:"说明:",field:"remark"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName"},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]}),s=o({defaultValue:r(()=>l.config),border:!0,direction:"horizontal",column:2,title:"所属合同信息",fields:[{type:"text",label:"合同编号:",field:"contractCode"},{type:"text",label:"合同名称:",field:"contractName"},{type:"text",label:"合同类型:",field:"contractTypeName"},{type:"text",label:"合同金额(万元):",field:"contractCost"},{type:"text",label:"合同工期(开始时间):",field:"workTimeBeginName"},{type:"text",label:"合同工期(完成时间):",field:"workTimeEndName"}]});return(e,N)=>{const a=_,i=u;return y(),b(x,null,[t(i,{class:"card"},{default:f(()=>[t(a,{config:c(p)},null,8,["config"])]),_:1}),t(i,{class:"card"},{default:f(()=>[t(a,{config:c(s)},null,8,["config"])]),_:1})],64)}}}),k=g(C,[["__scopeId","data-v-95f217c5"]]);export{k as default};
