import{m as t}from"./index-r0dFAfgr.js";function n(a,e){return t({url:`/api/alarm/getAlarmRealTime/page${e?`/${e}`:""}`,method:"get",params:a})}function l(a){return t({url:"/api/alarm/getAlarm/projectId/page",method:"post",data:a})}function o(a){return t({url:"/api/alarm/confirm",method:"post",data:a})}function i(a){return t({url:"/api/alarm/clear",method:"post",data:a})}function m(a){return t({url:`/api/alarm/getAlarmHistoryByDevice?deviceId=${a.deviceId}&alarmType=${a.type}`,method:"get"})}function u(a,e){return t({url:`/api/alarmJson/linkedUser/${e}`,method:"post",data:a})}function s(a){return t({url:`/api/alarmJson/linkedUser/${a}`,method:"get"})}function d(a){return t({url:`/api/alarmJson/linkedExtraUser/${a}`,method:"get"})}function p(a,e){return t({url:`/api/alarmJson/linkedExtraUser/${e}`,method:"post",data:a})}function c(a){return t({url:"/api/alarmJson",method:"get"})}function g(a,e){return t({url:`/api/alarmJson/project/page/${a}`,method:"get",params:e})}function f(a){return t({url:"/api/alarmJson/save",method:"post",data:a})}function h(a){return t({url:`/api/alarmJson/delete?id=${a}`,method:"delete"})}export{o as a,s as b,i as c,d,u as e,p as f,m as g,c as h,n as i,g as j,h as k,l,f as s};
