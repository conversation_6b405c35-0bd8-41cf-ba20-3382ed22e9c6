// 智慧生产=水源管理-统计分析 api
import request from '@/plugins/axios';

// 【供水量分析】按项目查询项目下站点的供水情况分析
export function getWaterSupplyReport(params?: any) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyReport',
    method: 'get',
    params
  });
}

// 查询单个水源地的单耗报表
export function getWaterSupplyConsumptionReport(params?: any) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyConsumptionReport',
    method: 'get',
    params
  });
}

// 查询多个水源地的供水量报表
export function getWaterSupplyDetailReport(params?: any) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyDetailReport',
    method: 'get',
    params
  });
}

// 导出多个水源地的供水量报表
export function exportWaterSupplyDetailReport(params?: any) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyDetailReport/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 泵机分析-查询多个水源地的进出水数据分析
export function getWaterOutletAndInletReport(params?: any) {
  return request({
    url: '/istar/api/water/source/getWaterOutletAndInletReport',
    method: 'get',
    params
  });
}

// 查询所有水源地的电耗数据（本期及上期供水量、耗电量、单耗分析）
export function getWaterSupplyAndEnergyData(params?: any) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyAndEnergyData',
    method: 'get',
    params
  });
}

// 查询单个水源地的耗电量数据（本期上期供水量耗电量曲线、本期上期吨水电耗曲线）
export function getWaterSupplyAndEnergyDataDetail(params?: any) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyAndEnergyDataDetail',
    method: 'get',
    params
  });
}

// 查询水源地运行概况统计数据
export function getWaterSourceOperationOverview(params?: any) {
  return request({
    url: '/istar/api/water/source/getOperationOverview',
    method: 'get',
    params
  });
}
