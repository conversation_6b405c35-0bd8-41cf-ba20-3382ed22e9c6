package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionExpense;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class SoConstructionExpenseSaveRequest extends SaveRequest<SoConstructionExpense> {
    // 编号
    @NotNullOrEmpty
    private String code;

    // 所属工程编号
    @NotNullOrEmpty
    private String constructionCode;

    // 所属合同编号
    @NotNullOrEmpty
    private String contractCode;

    // 费用类型
    @NotNullOrEmpty
    private String type;

    // 金额，万元
    @NotNullOrEmpty
    private BigDecimal cost;

    // 支付方式
    private String paymentType;

    // 报批时间
    @NotNullOrEmpty
    private Date approvalTime;

    // 提交财务处理时间
    @NotNullOrEmpty
    private Date submitFinanceTime;

    // 一审审核金额，万元
    private BigDecimal firstVerifyCost;

    // 一审结算单位
    private String firstVerifyOrganization;

    // 二审审核金额，万元
    private BigDecimal secondVerifyCost;

    // 二审结算单位
    private String secondVerifyOrganization;

    // 代收款信息
    private String payeeInfo;

    // 收款单位
    private String payeeOrganization;

    // 说明
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    public String valid(IStarHttpRequest request) {
        if(BigDecimal.ZERO.compareTo(cost) > 0) {
            return "金额应该大于0";
        }

        return super.valid(request);
    }

    @Override
    protected SoConstructionExpense build() {
        SoConstructionExpense entity = new SoConstructionExpense();
        entity.setCode(code);
        entity.setConstructionCode(constructionCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionExpense update(String id) {
        SoConstructionExpense entity = new SoConstructionExpense();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionExpense entity) {
        entity.setContractCode(contractCode);
        entity.setType(type);
        entity.setCost(cost);
        entity.setPaymentType(paymentType);
        entity.setApprovalTime(approvalTime);
        entity.setSubmitFinanceTime(submitFinanceTime);
        entity.setFirstVerifyCost(firstVerifyCost);
        entity.setFirstVerifyOrganization(firstVerifyOrganization);
        entity.setSecondVerifyCost(secondVerifyCost);
        entity.setSecondVerifyOrganization(secondVerifyOrganization);
        entity.setPayeeInfo(payeeInfo);
        entity.setPayeeOrganization(payeeOrganization);
        entity.setRemark(remark);
        entity.setAttachments(attachments);

        entity.setUpdateUser(currentUserUUID());
        entity.setUpdateTime(createTime());
    }
}