import{_ as m}from"./CardTable-rdWOL4_6.js";import{_ as g}from"./CardSearch-CB_HNR-Q.js";import{z as d,C as _,c as f,r as s,b as u,o as h,g as b,n as y,q as c}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function S(i){return d({url:"/api/base/performance/list",method:"get",params:i})}const x={class:"wrapper"},C={__name:"apiPerformance",setup(i){const p=f(),l=s({labelWidth:"100px",filters:[{type:"input",label:"方法名称",field:"methodName",placeholder:"请输入方法名称",onChange:()=>r()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>r()}]}],defaultParams:{}}),t=s({columns:[{label:"ID",prop:"id"},{label:"用户ID",prop:"userId"},{label:"方法名称",prop:"methodName"},{label:"类名",prop:"className"},{label:"执行时长",prop:"executionTime"},{label:"调用时间",prop:"callTime",formatter:e=>new Date(e.callTime).toLocaleString()},{label:"是否成功",prop:"success"},{label:"接口描述",prop:"description"}],dataList:[],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{t.pagination.page=e,r()},handleSize:e=>{t.pagination.limit=e,r()}}}),r=async()=>{var e,n;try{const o=await S({page:t.pagination.page,size:t.pagination.limit,...((e=p.value)==null?void 0:e.queryParams)||{}}),a=((n=o.data)==null?void 0:n.data)||o;t.dataList=a.records||a,t.pagination.total=a.total||a.length||0,a.records&&a.records.length>0&&(t.pagination.page=a.current||1)}catch{u.error("数据加载失败")}};return h(()=>{r()}),(e,n)=>{const o=g,a=m;return b(),y("div",x,[c(o,{ref_key:"refSearch",ref:p,config:l},null,8,["config"]),c(a,{class:"card-table",config:t},null,8,["config"])])}}},v=_(C,[["__scopeId","data-v-966ae49c"]]);export{v as default};
