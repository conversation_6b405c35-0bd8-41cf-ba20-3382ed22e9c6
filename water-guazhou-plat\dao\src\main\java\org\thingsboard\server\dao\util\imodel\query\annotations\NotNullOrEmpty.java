package org.thingsboard.server.dao.util.imodel.query.annotations;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Documented
public @interface NotNullOrEmpty {
    /**
     * 条件方法，调用当前对象返回布尔值的指定方法，返回为true时才执行非空判定
     * 默认对应名称方法不存在时返回true
     *
     * @return 当前对象返回布尔值的指定方法名
     */
    String condition() default "";

    /**
     * 在非空检查前是否对字段值自动进行trim操作，去除两端的空格，默认为true
     *
     * @return 是否对获取的字符串执行trim()操作
     */
    boolean trim() default true;

    /**
     * TODO: [LFT] 唯一性验证
     * 字段对应数据库表中的数据
     * 当table不为空时会检查该字段值在数据库表中的唯一性
     *
     * @return 唯一字段对应的数据库表
     */
    String table() default "";

    /**
     * TODO: [LFT] 存在性验证
     * 字段对应其它数据库表中的字段，表名与字段名使用:隔开，如user:id，代表该字段对应user表中的id字段，默认id可以省略
     * 当table不为空时会检查该字段值在关联数据库表中是否存在
     *
     * @return 关联字段对应的数据库表
     */
    String refTable() default "";

    /**
     * TODO: [LFT] 枚举验证
     * 若标注于String类型数据上进行枚举名称验证
     * 若标注于整型数据上进行数值范围验证
     *
     * @return 对应的枚举类型验证
     */
    Class<?> enumType() default Void.class;

    /**
     * @return 被最外层验证实体对象包裹时是否忽略验证
     */
    boolean parentIgnore() default false;
}
