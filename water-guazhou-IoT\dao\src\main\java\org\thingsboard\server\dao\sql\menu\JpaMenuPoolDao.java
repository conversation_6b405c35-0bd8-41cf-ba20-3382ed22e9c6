/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.menu;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.menu.MenuPoolDao;
import org.thingsboard.server.dao.model.sql.MenuPoolEntity;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.ArrayList;
import java.util.List;

@Component
@SqlDao
@Slf4j
public class JpaMenuPoolDao extends JpaAbstractDao<MenuPoolEntity, MenuPool> implements MenuPoolDao {


    @Autowired
    private MenuPoolRepository menuPoolRepository;

    @Override
    protected Class<MenuPoolEntity> getEntityClass() {
        return MenuPoolEntity.class;
    }

    @Override
    protected CrudRepository<MenuPoolEntity, String> getCrudRepository() {
        return menuPoolRepository;
    }

    @Override
    public List<MenuPool> findByParentId(MenuPoolId parentId) {
        return DaoUtil.convertDataList(
                menuPoolRepository.findByParentIdOrderByOrderNumDesc(UUIDConverter.fromTimeUUID(parentId.getId()))
        );
    }

    @Override
    public List<MenuPool> getSelectableTree(MenuPoolId parentId) {
        List menuPoolEntityList = menuPoolRepository.findByParentId(UUIDConverter.fromTimeUUID(parentId.getId()));

        List<MenuPool> menuPoolList = new ArrayList<>();
        for (Object o : menuPoolEntityList) {
            Object[] data = (Object[]) o;
            MenuPool menuPool = new MenuPool();
            menuPool.setId(new MenuPoolId(UUIDConverter.fromString((String) data[0])));
            menuPool.setDefaultName((String) data[1]);

            menuPoolList.add(menuPool);
        }

        return menuPoolList;
    }

    @Override
    public List<MenuPool> findByType(Integer type) {
        return DaoUtil.convertDataList(
                menuPoolRepository.findByType(type)
        );
    }
}
