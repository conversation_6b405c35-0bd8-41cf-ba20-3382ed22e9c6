package org.thingsboard.server.dao.model.sql.gis;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_GIS_LABEL_TABLE)
@TableName(ModelConstants.TB_GIS_LABEL_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class GisLabel {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_GIS_LABEL_NAME)
    public String name;

    @Column(name = ModelConstants.TB_GIS_LABEL_CREATEUSER)
    public String createuser;

    @Column(name = ModelConstants.TB_GIS_LABEL_CREATETIME)
    public Date createtime;

    @Column(name = ModelConstants.TB_GIS_LABEL_UPDATETIME)
    public Date updatetime;

    @Column(name = ModelConstants.TB_GIS_LABEL_DESCRIPTION)
    public String description;

    @Column(name = ModelConstants.TB_GIS_LABEL_AVAILABLE)
    public String available;

    @Column(name = ModelConstants.TB_GIS_LABEL_GEOMTYPE)
    public String geomtype;

    @Column(name = ModelConstants.TB_GIS_LABEL_GEOM)
    public String geom;

    @Column(name = ModelConstants.TB_GIS_LABEL_STYLE)
    public String style;

    @Column(name = ModelConstants.TB_GIS_LABEL_POINTCOLOR)
    public String pointcolor;

    @Column(name = ModelConstants.TB_GIS_LABEL_POINTSIZE)
    public BigDecimal pointsize;

    @Column(name = ModelConstants.TB_GIS_LABEL_LINECOLOR)
    public String linecolor;

    @Column(name = ModelConstants.TB_GIS_LABEL_LINEWIDTH)
    public BigDecimal linewidth;

    @Column(name = ModelConstants.TB_GIS_LABEL_FILLCOLOR)
    public String fillcolor;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    @TableField(exist = false)
    private String createuserName;
}
