<template>
  <div
    class="exit-fullscreen"
    :class="[collapsed ? 'collapsed' : '']"
    @click="emit('fullscreen')"
  >
    <Icon :icon="isFullScreen ? 'mdi:exit-to-app' : 'mdi:fullscreen'"></Icon>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue';

const emit = defineEmits(['fullscreen']);
const props = defineProps<{
  collapsed: boolean;
}>();
const isFullScreen = ref<boolean>(false);
watch(
  () => props.collapsed,
  () => {
    isFullScreen.value = !!document.fullscreenElement;
  }
);
onMounted(async () => {
  await nextTick();
  // if (!document.fullscreenElement) emit('fullscreen', true)
});
</script>
<style lang="scss" scoped>
.exit-fullscreen {
  position: absolute;
  width: 50px;
  height: 30px;
  top: 27px;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  transition: all 0.5s ease-in-out;
  background-image: url('../imgs/exit_bg.png');
  background-repeat: no-repeat;
  cursor: pointer;
  &.collapsed {
    right: -50px;
  }
}
</style>
