package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicy;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalPolicyPageRequest;

@Mapper
public interface SsPortalPolicyMapper extends BaseMapper<SsPortalPolicy> {
    IPage<SsPortalPolicy> findByPage(SsPortalPolicyPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalPolicy entity);

    boolean updateFully(SsPortalPolicy entity);

    boolean canBeDelete(String id);

}
