<template>
  <div class="value-item">
    <p class="label">
      <span>{{ props.label }}</span>
    </p>
    <p>
      <span class="value">{{ props.value }}</span><span class="unit">{{ props.unit }}</span>
    </p>
    <p class="rate">
      <Icon :icon="props.icon"></Icon>
      <span class="rate-value">{{ props.rate }}%</span>
      <span class="rate-suffix">较上个时刻增加</span>
    </p>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'

const props = defineProps<{
  label: string
  value: any
  rate: string
  unit: string
  icon: string
}>()
</script>
<style lang="scss" scoped>
.value-item {
  height: 86px;
  .label {
    font-size: 14px;
    line-height: 2;
  }
  .value {
    font-size: 32px;
    font-weight: bolder;
  }
  .unit {
    font-size: 14px;
    margin-left: 8px;
    font-weight: bold;
  }
  .rate {
    font-size: 12px;
    color: rgb(44, 235, 203);
  }
  .rate-value {
    font-size: 14px;
  }
  .rate-suffix {
    margin-left: 8px;
    color: #a1a1a1;
  }
}
</style>
