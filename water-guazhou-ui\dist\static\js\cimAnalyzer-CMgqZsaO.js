import{J as $,K as A,G as rt,F as St,I as R,A as U,L as Ot,iL as ji,iM as Ze,iN as Ki,iO as Zi,iI as Qi,iP as tr,iQ as er,iR as sr,cx as Ct,dK as ir,h$ as Qe,eD as fe,ic as Bs,eF as ts,iS as xs,e0 as Me,ig as Ws,iT as Xe,ie as Ms,cb as rr,iU as Tt,cF as Nt,iV as Us,iW as Js,iX as qs,iY as js,iZ as pe,i_ as _e,i$ as nr,j0 as or,fO as ar,j1 as k,j2 as hr,j3 as lr,j4 as cr,j5 as be,j6 as ur,fz as Ks,j7 as fr,j8 as mr,l as Zs,j9 as j,ja as Oe,jb as ls,jc as pr,cH as _r,cI as Qs,jd as ge}from"./MapView-DaoQedLH.js";import{s as ti}from"./fontUtils-BuXIMW9g.js";import{a5 as N,T as Z,R as V,fu as ei,aO as dr,a4 as gr}from"./index-r0dFAfgr.js";import{b as cs,i as us,ap as yr,au as z}from"./Point-WxyopZva.js";import{C as Pr}from"./BidiEngine-CsUYIMdL.js";import{t as si,c as ii,n as Se}from"./GeometryUtils-B7ExOJII.js";import{O as bt,P as Ae,R as T,k as J,B as Xt,A as Ce,X as nt,o as dt,Y as He,Z as $t,K as we,U as ht,w as lt,C as xr,l as Mr,a as br,u as Sr,y as Cr,b as bs,c as wr,m as kr,f as Ss,i as Ee,e as De,n as Ye}from"./enums-B5k73o5q.js";import{t as Yt,e as ke}from"./alignmentUtils-CkNI7z7C.js";import{d as ri,f as ni,W as oi}from"./definitions-826PWLuy.js";import{w as q}from"./number-CoJp78Rz.js";import{t as vr}from"./Rect-CUzevAry.js";import{i as Rt}from"./callExpressionWithFeature-DgtD4TSq.js";import{o as Ir}from"./floatRGBA-PQQNbO39.js";const Ve=new Pr;function fs(o){if(o==null)return["",!1];if(!Ve.hasBidiChar(o))return[o,!1];let t;return t=Ve.checkContextual(o)==="rtl"?"IDNNN":"ICNNN",[Ve.bidiTransform(o,t,"VLYSN"),!0]}let Ft=class{constructor(){this.setIdentity()}getAngle(){return(this.rz==null||this.rz===0&&this.rzCos!==1&&this.rzSin!==0)&&(this.rz=Math.atan2(this.rzSin,this.rzCos)),this.rz}setIdentity(){this.tx=0,this.ty=0,this.tz=0,this.s=1,this.rx=0,this.ry=0,this.rz=0,this.rzCos=1,this.rzSin=0}setTranslate(t,e){this.tx=t,this.ty=e}setTranslateZ(t){this.tz=t}setRotateCS(t,e){this.rz=void 0,this.rzCos=t,this.rzSin=e}setRotate(t){this.rz=t,this.rzCos=void 0,this.rzSin=void 0}setRotateY(t){this.ry=t}setScale(t){this.s=t}setMeasure(t){this.m=t}};const ai=512;let F,es=class{constructor(t){this._geometry=t}next(){const t=this._geometry;return this._geometry=null,t}};function ms(o,t){let e,s;F||(F=new ii(0,0,0,1)),F.reset(si.Polygon),F.setPixelMargin(t+1),F.setExtent(ai);for(const r of o.rings)if(r&&!(r.length<3)){e=r[0][0],s=-r[0][1],F.moveTo(e,s);for(let n=1;n<r.length;n++)e=r[n][0],s=-r[n][1],F.lineTo(e,s);F.close()}const i=F.result(!1);if(i){const r=[];for(const n of i){const a=[];r.push(a);for(const h of n)a.push([h.x,-h.y])}return{rings:r}}return{rings:[]}}function hi(o,t){let e,s;F||(F=new ii(0,0,0,1)),F.reset(si.LineString),F.setPixelMargin(t+1),F.setExtent(ai);for(const r of o.paths)if(r&&!(r.length<2)){e=r[0][0],s=-r[0][1],F.moveTo(e,s);for(let n=1;n<r.length;n++)e=r[n][0],s=-r[n][1],F.lineTo(e,s)}const i=F.result(!1);if(i){const r=[];for(const n of i){const a=[];r.push(a);for(const h of n)a.push([h.x,-h.y])}return{paths:r}}return{paths:[]}}let Lr=class{applyColorSubstituition(t,e){if(!e)return t;this._rasterizationCanvas||(this._rasterizationCanvas=document.createElement("canvas"));const{width:s,height:i}=t,r=this._rasterizationCanvas,n=r.getContext("2d");t!==r&&(r.width=s,r.height=i,n.drawImage(t,0,0,s,i));const a=n.getImageData(0,0,s,i).data;if(e){for(const l of e)if(l&&l.oldColor&&l.oldColor.length===4&&l.newColor&&l.newColor.length===4){const[c,u,f,m]=l.oldColor,[_,p,d,g]=l.newColor;if(c===_&&u===p&&f===d&&m===g)continue;for(let y=0;y<a.length;y+=4)c===a[y]&&u===a[y+1]&&f===a[y+2]&&m===a[y+3]&&(a[y]=_,a[y+1]=p,a[y+2]=d,a[y+3]=g)}}const h=new ImageData(a,s,i);return n.putImageData(h,0,0),r}tintImageData(t,e){if(!e||e.length<4)return t;this._rasterizationCanvas||(this._rasterizationCanvas=document.createElement("canvas"));const{width:s,height:i}=t,r=this._rasterizationCanvas,n=r.getContext("2d");t!==r&&(r.width=s,r.height=i,n.drawImage(t,0,0,s,i));const a=n.getImageData(0,0,s,i),h=new Uint8Array(a.data),l=[e[0]/255,e[1]/255,e[2]/255,e[3]/255];for(let u=0;u<h.length;u+=4)h[u+0]*=l[0],h[u+1]*=l[1],h[u+2]*=l[2],h[u+3]*=l[3];const c=new ImageData(new Uint8ClampedArray(h.buffer),s,i);return n.putImageData(c,0,0),r}};function Tr(o){const t=N(o);return $r(t),t}function li(o){o&&(St(o)?o.y=-o.y:$(o)?Cs(o.rings):A(o)?Cs(o.paths):rt(o)&&ci(o.points))}function ci(o){if(o){const t=o.length;for(let e=0;e<t;e++)o[e][1]=-o[e][1]}}function Cs(o){if(o)for(const t of o)ci(t)}function ui(o){if(o)for(let t=o.length-1;t>0;--t)o[t][0]-=o[t-1][0],o[t][1]-=o[t-1][1]}function ws(o){if(o)for(const t of o)ui(t)}function fi(o){if(o){const t=o.length;for(let e=1;e<t;++e)o[e][0]+=o[e-1][0],o[e][1]+=o[e-1][1]}}function ks(o){if(o)for(const t of o)fi(t)}function $r(o){o&&($(o)?ks(o.rings):A(o)?ks(o.paths):rt(o)&&fi(o.points),li(o))}function Ar(o){o&&(li(o),$(o)?ws(o.rings):A(o)?ws(o.paths):rt(o)&&ui(o.points))}function Nr(o){if(o)for(const t of o)ss(t)}function ss(o){o&&o.reverse()}function is(o,t,e){return[o[0]+(t[0]-o[0])*e,o[1]+(t[1]-o[1])*e]}function zr(o){return!(!o||o.length===0)&&o[0][0]===o[o.length-1][0]&&o[0][1]===o[o.length-1][1]}function me(o){return o[4]}function zt(o,t){o[4]=t}let ye=class{constructor(t,e,s,i=0){this.isClosed=!1,this.multiPath=null,this.acceptPolygon=e,this.acceptPolyline=s,this.geomUnitsPerPoint=i,this.pathCount=-1,this.pathIndex=-1,this.iteratePath=!1,t&&($(t)?e&&(this.multiPath=t.rings,this.isClosed=!0):A(t)?s&&(this.multiPath=t.paths,this.isClosed=!1):R(t)&&e&&(this.multiPath=mi(t).rings,this.isClosed=!0),this.multiPath&&(this.pathCount=this.multiPath.length)),this.internalPlacement=new Ft}next(){if(!this.multiPath)return null;for(;this.iteratePath||this.pathIndex<this.pathCount-1;){this.iteratePath||this.pathIndex++;const t=this.processPath(this.multiPath[this.pathIndex]);if(t)return t}return this.pathCount=-1,this.pathIndex=-1,this.multiPath=null,null}},Re=class{constructor(t,e,s,i=0){this.isClosed=!1,this.multiPath=null,this.inputGeometries=t,this.acceptPolygon=e,this.acceptPolyline=s,this.geomUnitsPerPoint=i,this.pathCount=-1,this.pathIndex=-1,this.iteratePath=!1}next(){for(;;){if(!this.multiPath){let t=this.inputGeometries.next();for(;t;){if($(t)?this.acceptPolygon&&(this.multiPath=t.rings,this.isClosed=!0):A(t)?this.acceptPolyline&&(this.multiPath=t.paths,this.isClosed=!1):R(t)&&this.acceptPolygon&&(this.multiPath=mi(t).rings,this.isClosed=!0),this.multiPath){this.pathCount=this.multiPath.length,this.pathIndex=-1;break}t=this.inputGeometries.next()}if(!this.multiPath)return null}for(;this.iteratePath||this.pathIndex<this.pathCount-1;){this.iteratePath||this.pathIndex++;const t=this.processPath(this.multiPath[this.pathIndex]);if(t)return t}this.pathCount=-1,this.pathIndex=-1,this.multiPath=null}}};function mi(o){return{rings:[[[o.xmin,o.ymin],[o.xmin,o.ymax],[o.xmax,o.ymax],[o.xmax,o.ymin],[o.xmin,o.ymin]]]}}let pi=class Vt{static local(){return Vt.instance===null&&(Vt.instance=new Vt),Vt.instance}execute(t,e,s,i,r){return new Er(t,e,s)}};pi.instance=null;let Er=class{constructor(t,e,s){this._inputGeometries=t,this._angleTolerance=e.angleTolerance!==void 0?e.angleTolerance:120,this._maxCosAngle=Math.cos((1-Math.abs(this._angleTolerance)/180)*Math.PI)}next(){let t=this._inputGeometries.next();for(;t;){if($(t)){this._isClosed=!0;const e=N(t);return this._processMultipath(e.rings),e}if(A(t)){this._isClosed=!1;const e=N(t);return this._processMultipath(e.paths),e}if(R(t)){if(this._maxCosAngle)return t;this._isClosed=!0;const e=[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]];return this._processPath(e),{rings:[e]}}t=this._inputGeometries.next()}return null}_processMultipath(t){if(t)for(const e of t)this._processPath(e)}_processPath(t){if(t){let e,s,i,r,n,a,h=t.length,l=t[0];this._isClosed&&++h;for(let c=1;c<h;++c){let u;u=this._isClosed&&c===h-1?t[0]:t[c];const f=u[0]-l[0],m=u[1]-l[1],_=Math.sqrt(f*f+m*m);c>1&&_>0&&i>0&&(e*f+s*m)/_/i<=this._maxCosAngle&&zt(l,1),c===1&&(r=f,n=m,a=_),_>0&&(l=u,e=f,s=m,i=_)}this._isClosed&&i>0&&a>0&&(e*r+s*n)/a/i<=this._maxCosAngle&&zt(t[0],1)}}};const de=.03;let Et=class{constructor(){this._path=[]}path(){return this._path}addPath(t,e){e||t.reverse(),Array.prototype.push.apply(this._path,t),e||t.reverse()}static mergePath(t,e){e&&Array.prototype.push.apply(t,e)}startPath(t){this._path.push(t)}lineTo(t){this._path.push(t)}close(){const t=this._path;t.length>1&&(t[0][0]===t[t.length-1][0]&&t[0][1]===t[t.length-1][1]||t.push([t[0][0],t[0][1]]))}},ct=class{constructor(t=0,e=!1){}normalize(t){const e=Math.sqrt(t[0]*t[0]+t[1]*t[1]);e!==0&&(t[0]/=e,t[1]/=e)}calculateLength(t,e){const s=e[0]-t[0],i=e[1]-t[1];return Math.sqrt(s*s+i*i)}calculateSegLength(t,e){return this.calculateLength(t[e],t[e+1])}calculatePathLength(t){let e=0;const s=t?t.length:0;for(let i=0;i<s-1;++i)e+=this.calculateSegLength(t,i);return e}calculatePathArea(t){let e=0;const s=t?t.length:0;for(let i=0;i<s-1;++i)e+=(t[i+1][0]-t[i][0])*(t[i+1][1]+t[i][1]);return e/2}getCoord2D(t,e,s){return[t[0]+(e[0]-t[0])*s,t[1]+(e[1]-t[1])*s]}getSegCoord2D(t,e,s){return this.getCoord2D(t[e],t[e+1],s)}getAngle(t,e,s){const i=e[0]-t[0],r=e[1]-t[1];return Math.atan2(r,i)}getSegAngle(t,e,s){return this.getAngle(t[e],t[e+1],s)}getAngleCS(t,e,s){const i=e[0]-t[0],r=e[1]-t[1],n=Math.sqrt(i*i+r*r);return n>0?[i/n,r/n]:[1,0]}getSegAngleCS(t,e,s){return this.getAngleCS(t[e],t[e+1],s)}cut(t,e,s,i){return[s<=0?t[e]:this.getSegCoord2D(t,e,s),i>=1?t[e+1]:this.getSegCoord2D(t,e,i)]}addSegment(t,e,s){s&&t.push(e[0]),t.push(e[1])}getSubCurve(t,e,s){const i=[];return this.appendSubCurve(i,t,e,s)?i:null}appendSubCurve(t,e,s,i){const r=e?e.length-1:0;let n=0,a=!0,h=0;for(;h<r;){const l=this.calculateSegLength(e,h);if(l!==0){if(a){if(n+l>s){const c=(s-n)/l;let u=1,f=!1;n+l>=i&&(u=(i-n)/l,f=!0);const m=this.cut(e,h,c,u);if(m&&this.addSegment(t,m,a),f)break;a=!1}}else{if(n+l>i){const c=this.cut(e,h,0,(i-n)/l);c&&this.addSegment(t,c,a);break}this.addSegment(t,[e[h],e[h+1]],a)}n+=l,++h}else++h}return!0}getCIMPointAlong(t,e){const s=t?t.length-1:0;let i=0,r=-1;for(;r<s;){++r;const n=this.calculateSegLength(t,r);if(n!==0){if(i+n>e){const a=(e-i)/n;return this.getCoord2D(t[r],t[r+1],a)}i+=n}}return null}isEmpty(t,e){if(!t||t.length<=1)return!0;const s=t?t.length-1:0;let i=-1;for(;i<s;)if(++i,t[i+1][0]!==t[i][0]||t[i+1][1]!==t[i][1]||e&&t[i+1][2]!==t[i][2])return!1;return!0}offset(t,e,s,i,r){if(!t||t.length<2)return null;let n=0,a=t[n++],h=n;for(;n<t.length;){const f=t[n];f[0]===a[0]&&f[1]===a[1]||(n!==h&&(t[h]=t[n]),a=t[h++]),n++}const l=t[0][0]===t[h-1][0]&&t[0][1]===t[h-1][1];if(l&&--h,h<(l?3:2))return null;const c=[];a=l?t[h-1]:null;let u=t[0];for(let f=0;f<h;f++){const m=f===h-1?l?t[0]:null:t[f+1];if(a)if(m){const _=[m[0]-u[0],m[1]-u[1]];this.normalize(_);const p=[u[0]-a[0],u[1]-a[1]];this.normalize(p);const d=p[0]*_[1]-p[1]*_[0],g=p[0]*_[0]+p[1]*_[1];if(d===0&&g===1){u=m;continue}if(d>=0==e<=0){if(g<1){const y=[_[0]-p[0],_[1]-p[1]];this.normalize(y);const x=Math.sqrt((1+g)/2);if(x>1/i){const P=-Math.abs(e)/x;c.push([u[0]-y[0]*P,u[1]-y[1]*P])}}}else switch(s){case bt.Mitered:{const y=Math.sqrt((1+g)/2);if(y>0&&1/y<i){const x=[_[0]-p[0],_[1]-p[1]];this.normalize(x);const P=Math.abs(e)/y;c.push([u[0]-x[0]*P,u[1]-x[1]*P]);break}}case bt.Bevelled:c.push([u[0]+p[1]*e,u[1]-p[0]*e]),c.push([u[0]+_[1]*e,u[1]-_[0]*e]);break;case bt.Rounded:if(g<1){c.push([u[0]+p[1]*e,u[1]-p[0]*e]);const y=Math.floor(2.5*(1-g));if(y>0){const x=1/y;let P=x;for(let M=1;M<y;M++,P+=x){const S=[p[1]*(1-P)+_[1]*P,-p[0]*(1-P)-_[0]*P];this.normalize(S),c.push([u[0]+S[0]*e,u[1]+S[1]*e])}}c.push([u[0]+_[1]*e,u[1]-_[0]*e])}break;case bt.Square:default:if(d<0)c.push([u[0]+(p[1]+p[0])*e,u[1]+(p[1]-p[0])*e]),c.push([u[0]+(_[1]-_[0])*e,u[1]-(_[0]+_[1])*e]);else{const y=Math.sqrt((1+Math.abs(g))/2),x=[_[0]-p[0],_[1]-p[1]];this.normalize(x);const P=e/y;c.push([u[0]-x[0]*P,u[1]-x[1]*P])}}}else{const _=[u[0]-a[0],u[1]-a[1]];this.normalize(_),c.push([u[0]+_[1]*e,u[1]-_[0]*e])}else{const _=[m[0]-u[0],m[1]-u[1]];this.normalize(_),c.push([u[0]+_[1]*e,u[1]-_[0]*e])}a=u,u=m}return c.length<(l?3:2)?null:(l&&c.push([c[0][0],c[0][1]]),c)}};const Be=1.7320508075688772,Or=5,Rr=Ae.OpenEnded;let _i=class Bt{static local(){return Bt.instance===null&&(Bt.instance=new Bt),Bt.instance}execute(t,e,s,i,r){return new Fr(t,e,s)}};_i.instance=null;let Fr=class extends Re{constructor(t,e,s){super(t,!1,!0),this._curveHelper=new ct,this._width=(e.width!==void 0?e.width:Or)*s,this._arrowType=e.geometricEffectArrowType!==void 0?e.geometricEffectArrowType:e.arrowType!==void 0?e.arrowType:Rr,this._offsetFlattenError=de*s}processPath(t){switch(this._arrowType){case Ae.OpenEnded:default:return this._constructSimpleArrow(t,!0);case Ae.Block:return this._constructSimpleArrow(t,!1);case Ae.Crossed:return this._constructCrossedArrow(t)}}_constructSimpleArrow(t,e){const s=this._curveHelper.calculatePathLength(t);let i=this._width;s<2*i&&(i=s/2);const r=this._curveHelper.getSubCurve(t,0,s-i);if(!r)return null;const n=i/2;if(this._curveHelper.isEmpty(r,!1))return null;const a=this._constructOffset(r,-n);if(!a)return null;const h=this._constructOffset(r,n);if(!h)return null;const l=this._constructArrowBasePoint(a,-n/2);if(!l)return null;const c=this._constructArrowBasePoint(h,n/2);if(!c)return null;const u=t[t.length-1];e||(this._makeControlPoint(h,!0),this._makeControlPoint(a,!0));const f=new Et;return f.addPath(h,!0),f.lineTo(c),this._makeControlPoint(f.path()),f.lineTo(u),this._makeControlPoint(f.path()),f.lineTo(l),this._makeControlPoint(f.path()),f.addPath(a,!1),e?{paths:[f.path()]}:(f.close(),{rings:[f.path()]})}_constructCrossedArrow(t){const e=this._curveHelper.calculatePathLength(t);let s=this._width;e<s*(1+Be+1)&&(s=e/(1+Be+1));const i=this._curveHelper.getSubCurve(t,0,e-s*(1+Be));if(!i)return null;const r=s/2;if(this._curveHelper.isEmpty(i,!1))return null;const n=this._constructOffset(i,r);if(!n)return null;const a=this._constructOffset(i,-r);if(!a)return null;const h=this._curveHelper.getSubCurve(t,0,e-s);if(!h||this._curveHelper.isEmpty(h,!1))return null;const l=this._constructOffset(h,r);if(!l)return null;const c=this._constructOffset(h,-r);if(!c)return null;const u=l[l.length-1],f=this._constructArrowBasePoint(l,r/2);if(!f)return null;const m=c[c.length-1],_=this._constructArrowBasePoint(c,-r/2);if(!_)return null;const p=t[t.length-1];this._makeControlPoint(n,!1),this._makeControlPoint(a,!1);const d=new Et;return d.addPath(n,!0),this._makeControlPoint(d.path()),d.lineTo(m),d.lineTo(_),this._makeControlPoint(d.path()),d.lineTo(p),this._makeControlPoint(d.path()),d.lineTo(f),this._makeControlPoint(d.path()),d.lineTo(u),this._makeControlPoint(d.path()),d.addPath(a,!1),{paths:[d.path()]}}_constructOffset(t,e){return this._curveHelper.offset(t,e,bt.Rounded,4,this._offsetFlattenError)}_constructArrowBasePoint(t,e){if(!t||t.length<2)return null;const s=t[t.length-2],i=t[t.length-1],r=[i[0]-s[0],i[1]-s[1]];return this._curveHelper.normalize(r),[i[0]+r[1]*e,i[1]-r[0]*e]}_makeControlPoint(t,e=!1){zt(e?t[0]:t[t.length-1],1)}},di=class Wt{static local(){return Wt.instance===null&&(Wt.instance=new Wt),Wt.instance}execute(t,e,s,i,r){return new Gr(t,e,s,i,r)}};di.instance=null;let Gr=class{constructor(t,e,s,i,r){this._inputGeometries=t,this._tileKey=i,this._geometryEngine=r,this._curveHelper=new ct,this._size=(e.size!==void 0?e.size:1)*s,this._offsetFlattenError=de*s}next(){let t;for(;t=this._inputGeometries.next();){if(this._size===0)return t;if(R(t)){if(this._size>0){const i=[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]],r=this._curveHelper.offset(i,this._size,bt.Rounded,4,this._offsetFlattenError);if(r)return{rings:[r]}}else if(this._size<0&&Math.min(t.xmax-t.xmin,t.ymax-t.ymin)+2*this._size>0)return{xmin:t.xmin-this._size,xmax:t.xmax+this._size,ymin:t.ymin-this._size,ymax:t.ymax+this._size}}const e=this._geometryEngine;if(Z(e))return null;let s=t;if((!$(t)||!this._tileKey||(s=ms(t,Math.abs(this._size)+1),s&&s.rings&&s.rings.length!==0))&&(!A(t)||!this._tileKey||(s=hi(t,Math.abs(this._size)+1),s&&s.paths&&s.paths.length!==0)))return e.buffer(cs.WebMercator,s,this._size,1)}return null}},gi=class Ut{static local(){return Ut.instance===null&&(Ut.instance=new Ut),Ut.instance}execute(t,e,s,i,r){return new Xr(t,e,s)}};gi.instance=null;let Xr=class{constructor(t,e,s){this._defaultPointSize=20,this._inputGeometries=t,this._geomUnitsPerPoint=s,this._rule=e.rule??T.FullGeometry,this._defaultSize=this._defaultPointSize*s}next(){let t;for(;t=this._inputGeometries.next();){let e;if(St(t)?e=this._processGeom([[[t.x,t.y]]]):rt(t)?e=this._processGeom([t.points]):A(t)?e=this._processGeom(t.paths):$(t)&&(e=this._processGeom(t.rings)),e&&e.length)return{paths:e}}return null}_clone(t){return[t[0],t[1]]}_mid(t,e){return[(t[0]+e[0])/2,(t[1]+e[1])/2]}_mix(t,e,s,i){return[t[0]*e+s[0]*i,t[1]*e+s[1]*i]}_add(t,e){return[t[0]+e[0],t[1]+e[1]]}_add2(t,e,s){return[t[0]+e,t[1]+s]}_sub(t,e){return[t[0]-e[0],t[1]-e[1]]}_dist(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}_norm(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}_normalize(t,e=1){const s=e/this._norm(t);t[0]*=s,t[1]*=s}_leftPerpendicular(t){const e=-t[1],s=t[0];t[0]=e,t[1]=s}_leftPerp(t){return[-t[1],t[0]]}_rightPerpendicular(t){const e=t[1],s=-t[0];t[0]=e,t[1]=s}_rightPerp(t){return[t[1],-t[0]]}_dotProduct(t,e){return t[0]*e[0]+t[1]*e[1]}_crossProduct(t,e){return t[0]*e[1]-t[1]*e[0]}_rotateDirect(t,e,s){const i=t[0]*e-t[1]*s,r=t[0]*s+t[1]*e;t[0]=i,t[1]=r}_makeCtrlPt(t){const e=[t[0],t[1]];return zt(e,1),e}_addAngledTicks(t,e,s,i){const r=this._sub(s,e);this._normalize(r);const n=this._crossProduct(r,this._sub(i,e));let a;a=n>0?this._rightPerp(r):this._leftPerp(r);const h=Math.abs(n)/2,l=[];l.push([e[0]+(a[0]-r[0])*h,e[1]+(a[1]-r[1])*h]),l.push(e),l.push(s),l.push([s[0]+(a[0]+r[0])*h,s[1]+(a[1]+r[1])*h]),t.push(l)}_addBezier2(t,e,s,i,r){if(r--==0)return void t.push(i);const n=this._mid(e,s),a=this._mid(s,i),h=this._mid(n,a);this._addBezier2(t,e,n,h,r),this._addBezier2(t,h,a,i,r)}_addBezier3(t,e,s,i,r,n){if(n--==0)return void t.push(r);const a=this._mid(e,s),h=this._mid(s,i),l=this._mid(i,r),c=this._mid(a,h),u=this._mid(h,l),f=this._mid(c,u);this._addBezier3(t,e,a,c,f,n),this._addBezier3(t,f,u,l,r,n)}_add90DegArc(t,e,s,i,r){const n=r??this._crossProduct(this._sub(s,e),this._sub(i,e))>0,a=this._mid(e,s),h=this._sub(a,e);n?this._leftPerpendicular(h):this._rightPerpendicular(h),a[0]+=h[0],a[1]+=h[1],this._addBezier3(t,e,this._mix(e,.33333,a,.66667),this._mix(s,.33333,a,.66667),s,4)}_addArrow(t,e,s){const i=e[0],r=e[1],n=e[e.length-1],a=this._sub(i,r);this._normalize(a);const h=this._crossProduct(a,this._sub(n,r)),l=.5*h,c=this._leftPerp(a),u=[n[0]-c[0]*h,n[1]-c[1]*h],f=e.length-1,m=[];m.push(s?[-c[0],-c[1]]:c);let _=[-a[0],-a[1]];for(let p=1;p<f-1;p++){const d=this._sub(e[p+1],e[p]);this._normalize(d);const g=this._dotProduct(d,_),y=this._crossProduct(d,_),x=Math.sqrt((1+g)/2),P=this._sub(d,_);this._normalize(P),P[0]/=x,P[1]/=x,m.push(y<0?[-P[0],-P[1]]:P),_=d}m.push(this._rightPerp(_));for(let p=m.length-1;p>0;p--)t.push([e[p][0]+m[p][0]*l,e[p][1]+m[p][1]*l]);t.push([u[0]+m[0][0]*l,u[1]+m[0][1]*l]),t.push([u[0]+m[0][0]*h,u[1]+m[0][1]*h]),t.push(i),t.push([u[0]-m[0][0]*h,u[1]-m[0][1]*h]),t.push([u[0]-m[0][0]*l,u[1]-m[0][1]*l]);for(let p=1;p<m.length;p++)t.push([e[p][0]-m[p][0]*l,e[p][1]-m[p][1]*l])}_cp2(t,e,s){return t.length>=2?t[1]:this._add2(t[0],e*this._defaultSize,s*this._defaultSize)}_cp3(t,e,s,i){if(t.length>=3)return t[2];const r=this._mix(t[0],1-s,e,s),n=this._sub(e,t[0]);return this._normalize(n),this._rightPerpendicular(n),[r[0]+n[0]*i*this._defaultSize,r[1]+n[1]*i*this._defaultSize]}_arrowPath(t){if(t.length>2)return t;const e=t[0],s=this._cp2(t,-4,0),i=this._sub(e,s);this._normalize(i);const r=this._rightPerp(i);return[e,s,[e[0]+(r[0]-i[0])*this._defaultSize,e[1]+(r[1]-i[1])*this._defaultSize]]}_arrowLastSeg(t){const e=t[0],s=this._cp2(t,-4,0);let i;if(t.length>=3)i=t[t.length-1];else{const r=this._sub(e,s);this._normalize(r);const n=this._rightPerp(r);i=[e[0]+(n[0]-r[0])*this._defaultSize,e[1]+(n[1]-r[1])*this._defaultSize]}return[s,i]}_processGeom(t){if(!t)return null;const e=[];for(const s of t){if(!s||s.length===0)continue;const i=s.length;let r=s[0];switch(this._rule){case T.PerpendicularFromFirstSegment:{const n=this._cp2(s,0,-1),a=this._cp3(s,n,.5,4),h=[];h.push(a),h.push(this._mid(r,n)),e.push(h);break}case T.ReversedFirstSegment:{const n=this._cp2(s,0,-1);e.push([n,r]);break}case T.PerpendicularToSecondSegment:{const n=this._cp2(s,-4,1),a=this._cp3(s,n,.882353,-1.94),h=[];h.push(this._mid(n,a)),h.push(r),e.push(h);break}case T.SecondSegmentWithTicks:{const n=this._cp2(s,-4,1),a=this._cp3(s,n,.882353,-1.94),h=this._sub(a,n);let l;l=this._crossProduct(h,this._sub(r,n))>0?this._rightPerp(l):this._leftPerp(h);const c=[];c.push([n[0]+(l[0]-h[0])/3,n[1]+(l[1]-h[1])/3]),c.push(n),c.push(a),c.push([a[0]+(l[0]+h[0])/3,a[1]+(l[1]+h[1])/3]),e.push(c);break}case T.DoublePerpendicular:{const n=this._cp2(s,0,-1),a=this._cp3(s,n,.5,3),h=this._mid(r,n),l=this._sub(h,a);this._normalize(l);const c=this._crossProduct(l,this._sub(r,a));this._leftPerpendicular(l);const u=[];u.push(r),u.push([a[0]+l[0]*c,a[1]+l[1]*c]),e.push(u);const f=[];f.push([a[0]-l[0]*c,a[1]-l[1]*c]),f.push(n),e.push(f);break}case T.OppositeToFirstSegment:{const n=this._cp2(s,0,-1),a=this._cp3(s,n,.5,3),h=this._mid(r,n),l=this._sub(h,a);this._normalize(l);const c=this._crossProduct(l,this._sub(r,a));this._leftPerpendicular(l);const u=[];u.push([a[0]+l[0]*c,a[1]+l[1]*c]),u.push([a[0]-l[0]*c,a[1]-l[1]*c]),e.push(u);break}case T.TriplePerpendicular:{const n=this._cp2(s,0,-1),a=this._cp3(s,n,.5,4),h=this._mid(r,n),l=this._sub(h,a);this._normalize(l);const c=this._crossProduct(l,this._sub(r,a));this._leftPerpendicular(l);const u=[];u.push([a[0]+l[0]*c*.8,a[1]+l[1]*c*.8]),u.push([h[0]+.8*(r[0]-h[0]),h[1]+.8*(r[1]-h[1])]),e.push(u),e.push([a,h]);const f=[];f.push([a[0]-l[0]*c*.8,a[1]-l[1]*c*.8]),f.push([h[0]+.8*(n[0]-h[0]),h[1]+.8*(n[1]-h[1])]),e.push(f);break}case T.HalfCircleFirstSegment:{const n=this._cp2(s,0,-1),a=this._cp3(s,n,.5,4),h=this._mid(r,n);let l=this._sub(n,r);const c=Math.cos(Math.PI/18),u=Math.sin(Math.PI/18),f=Math.sqrt((1+c)/2),m=Math.sqrt((1-c)/2),_=[];let p;this._crossProduct(l,this._sub(a,r))>0?(_.push(r),l=this._sub(r,h),p=n):(_.push(n),l=this._sub(n,h),p=r),this._rotateDirect(l,f,m),l[0]/=f,l[1]/=f;for(let d=1;d<=18;d++)_.push(this._add(h,l)),this._rotateDirect(l,c,u);_.push(p),e.push(_);break}case T.HalfCircleSecondSegment:{const n=this._cp2(s,0,-1),a=this._cp3(s,n,1,-1);let h=this._sub(r,n);this._normalize(h);const l=this._crossProduct(h,this._sub(a,n))/2;this._leftPerpendicular(h);const c=[n[0]+h[0]*l,n[1]+h[1]*l];h=this._sub(n,c);const u=Math.cos(Math.PI/18);let f=Math.sin(Math.PI/18);l>0&&(f=-f);const m=[n];for(let _=1;_<=18;_++)this._rotateDirect(h,u,f),m.push(this._add(c,h));e.push(m);break}case T.HalfCircleExtended:{const n=this._cp2(s,0,-2),a=this._cp3(s,n,1,-1);let h;if(i>=4)h=s[3];else{const _=this._sub(r,n);h=this._add(a,_)}const l=this._dist(n,a)/2/.75,c=this._sub(n,r);this._normalize(c,l);const u=this._sub(a,h);this._normalize(u,l);const f=[h,a];e.push(f);const m=[this._clone(a)];this._addBezier3(m,a,this._add(a,u),this._add(n,c),n,4),m.push(r),e.push(m);break}case T.OpenCircle:{const n=this._cp2(s,-2,0),a=this._sub(n,r),h=Math.cos(Math.PI/18),l=-Math.sin(Math.PI/18),c=[n];for(let u=1;u<=33;u++)this._rotateDirect(a,h,l),c.push(this._add(r,a));e.push(c);break}case T.CoverageEdgesWithTicks:{const n=this._cp2(s,0,-1);let a,h;if(i>=3)a=s[2];else{const f=this._sub(n,r),m=this._leftPerp(f);a=[r[0]+m[0]-.25*f[0],r[1]+m[1]-.25*f[1]]}if(i>=4)h=s[3];else{const f=this._mid(r,n),m=this._sub(r,n);this._normalize(m),this._leftPerpendicular(m);const _=this._crossProduct(m,this._sub(a,f));this._rightPerpendicular(m),h=[a[0]+m[0]*_*2,a[1]+m[1]*_*2]}const l=this._sub(n,r);let c,u;c=this._crossProduct(l,this._sub(a,r))>0?this._rightPerp(l):this._leftPerp(l),u=[],u.push(a),u.push(r),u.push([r[0]+(c[0]-l[0])/3,r[1]+(c[1]-l[1])/3]),e.push(u),c=this._crossProduct(l,this._sub(h,n))>0?this._rightPerp(c):this._leftPerp(l),u=[],u.push([n[0]+(c[0]+l[0])/3,n[1]+(c[1]+l[1])/3]),u.push(n),u.push(h),e.push(u);break}case T.GapExtentWithDoubleTicks:{const n=this._cp2(s,0,2),a=this._cp3(s,n,0,1);let h;if(i>=4)h=s[3];else{const l=this._sub(n,r);h=this._add(a,l)}this._addAngledTicks(e,r,n,this._mid(a,h)),this._addAngledTicks(e,a,h,this._mid(r,n));break}case T.GapExtentMidline:{const n=this._cp2(s,2,0),a=this._cp3(s,n,0,1);let h;if(i>=4)h=s[3];else{const c=this._sub(n,r);h=this._add(a,c)}const l=[];l.push(this._mid(r,a)),l.push(this._mid(n,h)),e.push(l);break}case T.Chevron:{const n=this._cp2(s,-1,-1);let a;if(i>=3)a=s[2];else{const h=this._sub(n,r);this._leftPerpendicular(h),a=this._add(r,h)}e.push([n,this._makeCtrlPt(r),a]);break}case T.PerpendicularWithArc:{const n=this._cp2(s,0,-2),a=this._cp3(s,n,.5,-1);let h=this._sub(n,r);const l=this._norm(h);h[0]/=l,h[1]/=l;const c=this._crossProduct(h,this._sub(a,r));let u=this._dotProduct(h,this._sub(a,r));u<.05*l?u=.05*l:u>.95*l&&(u=.95*l);const f=[r[0]+h[0]*u,r[1]+h[1]*u];this._leftPerpendicular(h);let m=[];m.push([f[0]-h[0]*c,f[1]-h[1]*c]),m.push([f[0]+h[0]*c,f[1]+h[1]*c]),e.push(m);const _=[n[0]+h[0]*c,n[1]+h[1]*c];h=this._sub(n,_);const p=Math.cos(Math.PI/18);let d=Math.sin(Math.PI/18);c<0&&(d=-d),m=[r,n];for(let g=1;g<=9;g++)this._rotateDirect(h,p,d),m.push(this._add(_,h));e.push(m);break}case T.ClosedHalfCircle:{const n=this._cp2(s,2,0),a=this._mid(r,n),h=this._sub(n,a),l=Math.cos(Math.PI/18),c=Math.sin(Math.PI/18),u=[r,n];for(let f=1;f<=18;f++)this._rotateDirect(h,l,c),u.push(this._add(a,h));e.push(u);break}case T.TripleParallelExtended:{const n=this._cp2(s,0,-2),a=this._cp3(s,n,1,-2),h=this._mid(r,n),l=this._sub(a,n);this._normalize(l);const c=Math.abs(this._crossProduct(l,this._sub(h,n)))/2,u=this._dist(n,a),f=[n,r];f.push([r[0]+l[0]*u*.5,r[1]+l[1]*u*.5]),e.push(f);const m=[];m.push([h[0]-l[0]*c,h[1]-l[1]*c]),m.push([h[0]+l[0]*u*.375,h[1]+l[1]*u*.375]),zt(m[m.length-1],1),m.push([h[0]+l[0]*u*.75,h[1]+l[1]*u*.75]),e.push(m);const _=[n,a];e.push(_);break}case T.ParallelWithTicks:{const n=this._cp2(s,3,0),a=this._cp3(s,n,.5,-1),h=this._sub(a,n);this._normalize(h);const l=this._crossProduct(h,this._sub(a,r));this._leftPerpendicular(h),this._addAngledTicks(e,r,n,a),this._addAngledTicks(e,this._mix(r,1,h,l),this._mix(n,1,h,l),this._mid(r,n));break}case T.Parallel:{const n=this._cp2(s,3,0),a=this._cp3(s,n,.5,-1),h=this._sub(n,r);this._normalize(h);const l=this._leftPerp(h),c=this._crossProduct(h,this._sub(a,r));let u=[r,n];e.push(u),u=[],u.push([r[0]+l[0]*c,r[1]+l[1]*c]),u.push([n[0]+l[0]*c,n[1]+l[1]*c]),e.push(u);break}case T.PerpendicularToFirstSegment:{const n=this._cp2(s,3,0),a=this._cp3(s,n,.5,-1),h=this._mid(r,n),l=this._sub(n,r);this._normalize(l);const c=this._crossProduct(l,this._sub(a,r));this._leftPerpendicular(l);const u=[];u.push([h[0]-l[0]*c*.25,h[1]-l[1]*c*.25]),u.push([h[0]+l[0]*c*1.25,h[1]+l[1]*c*1.25]),e.push(u);break}case T.ParallelOffset:{const n=this._cp2(s,3,0),a=this._cp3(s,n,.5,-1),h=this._sub(n,r);this._normalize(h);const l=this._crossProduct(h,this._sub(a,r));this._leftPerpendicular(h);const c=[];c.push([r[0]-h[0]*l,r[1]-h[1]*l]),c.push([n[0]-h[0]*l,n[1]-h[1]*l]),e.push(c);const u=[];u.push([r[0]+h[0]*l,r[1]+h[1]*l]),u.push([n[0]+h[0]*l,n[1]+h[1]*l]),e.push(u);break}case T.OffsetOpposite:{const n=this._cp2(s,3,0),a=this._cp3(s,n,.5,-1),h=this._sub(n,r);this._normalize(h);const l=this._crossProduct(h,this._sub(a,r));this._leftPerpendicular(h);const c=[];c.push([r[0]-h[0]*l,r[1]-h[1]*l]),c.push([n[0]-h[0]*l,n[1]-h[1]*l]),e.push(c);break}case T.OffsetSame:{const n=this._cp2(s,3,0),a=this._cp3(s,n,.5,-1),h=this._sub(n,r);this._normalize(h);const l=this._crossProduct(h,this._sub(a,r));this._leftPerpendicular(h);const c=[];c.push([r[0]+h[0]*l,r[1]+h[1]*l]),c.push([n[0]+h[0]*l,n[1]+h[1]*l]),e.push(c);break}case T.CircleWithArc:{let n=this._cp2(s,3,0);const a=this._cp3(s,n,.5,-1);let h,l;if(i>=4)h=s[3],l=this._crossProduct(this._sub(h,n),this._sub(a,n))>0;else{h=n,l=this._crossProduct(this._sub(h,r),this._sub(a,r))>0;const _=24*this._geomUnitsPerPoint,p=this._sub(h,r);this._normalize(p,_);const d=Math.sqrt(2)/2;this._rotateDirect(p,d,l?d:-d),n=this._add(r,p)}const c=this._sub(n,r),u=Math.cos(Math.PI/18),f=Math.sin(Math.PI/18),m=[n];for(let _=1;_<=36;_++)this._rotateDirect(c,u,f),m.push(this._add(r,c));this._add90DegArc(m,n,h,a,l),zt(m[m.length-8],1),e.push(m);break}case T.DoubleJog:{let n,a,h=this._cp2(s,-3,1);if(n=i>=3?s[2]:this._add(r,this._sub(r,h)),i>=4)a=s[3];else{const g=r;r=h,a=n;const y=this._dist(r,g),x=this._dist(a,g);let P=30*this._geomUnitsPerPoint;.5*y<P&&(P=.5*y),.5*x<P&&(P=.5*x),h=this._mix(r,P/y,g,(y-P)/y),n=this._mix(a,P/x,g,(x-P)/x)}const l=this._mid(r,h),c=this._mid(a,n),u=this._dist(r,h),f=this._dist(n,a);let m=Math.min(u,f)/8;m=Math.min(m,24*this._geomUnitsPerPoint);const _=Math.cos(Math.PI/4);let p=this._sub(r,h);this._normalize(p,m),this._crossProduct(p,this._sub(a,h))>0?this._rotateDirect(p,_,-_):this._rotateDirect(p,_,_);let d=[];d.push(h),d.push(this._add(l,p)),d.push(this._sub(l,p)),d.push(r),e.push(d),p=this._sub(a,n),this._normalize(p,m),this._crossProduct(p,this._sub(r,n))<0?this._rotateDirect(p,_,_):this._rotateDirect(p,_,-_),d=[],d.push(n),d.push(this._add(c,p)),d.push(this._sub(c,p)),d.push(a),e.push(d);break}case T.PerpendicularOffset:{const n=this._cp2(s,-4,1),a=this._cp3(s,n,.882353,-1.94),h=this._sub(a,n);this._crossProduct(h,this._sub(r,n))>0?this._rightPerpendicular(h):this._leftPerpendicular(h);const l=[h[0]/8,h[1]/8],c=this._sub(this._mid(n,a),l);e.push([c,r]);break}case T.LineExcludingLastSegment:{const n=this._arrowPath(s),a=[];let h=n.length-2;for(;h--;)a.push(n[h]);e.push(a);break}case T.MultivertexArrow:{const n=this._arrowPath(s),a=[];this._addArrow(a,n,!1),e.push(a);break}case T.CrossedArrow:{const n=this._arrowPath(s),a=[];this._addArrow(a,n,!0),e.push(a);break}case T.ChevronArrow:{const[n,a]=this._arrowLastSeg(s),h=10*this._geomUnitsPerPoint,l=this._sub(r,n);this._normalize(l);const c=this._crossProduct(l,this._sub(a,n)),u=this._leftPerp(l),f=[a[0]-u[0]*c*2,a[1]-u[1]*c*2],m=[];m.push([a[0]+l[0]*h,a[1]+l[1]*h]),m.push(r),m.push([f[0]+l[0]*h,f[1]+l[1]*h]),e.push(m);break}case T.ChevronArrowOffset:{const[n,a]=this._arrowLastSeg(s),h=this._sub(r,n);this._normalize(h);const l=this._crossProduct(h,this._sub(a,n));this._leftPerpendicular(h);const c=[a[0]-h[0]*l,a[1]-h[1]*l],u=[];u.push([c[0]+h[0]*l*.5,c[1]+h[1]*l*.5]),u.push(this._mid(c,r)),u.push([c[0]-h[0]*l*.5,c[1]-h[1]*l*.5]),e.push(u);break}case T.PartialFirstSegment:{const[n,a]=this._arrowLastSeg(s),h=this._sub(r,n);this._normalize(h);const l=this._crossProduct(h,this._sub(a,n));this._leftPerpendicular(h);const c=[a[0]-h[0]*l,a[1]-h[1]*l];e.push([n,c]);break}case T.Arch:{const n=this._cp2(s,0,-1),a=this._cp3(s,n,.5,1),h=this._sub(r,n),l=this._mix(a,1,h,.55),c=this._mix(a,1,h,-.55),u=[r];this._addBezier2(u,r,l,a,4),this._addBezier2(u,a,c,n,4),e.push(u);break}case T.CurvedParallelTicks:{const n=this._cp2(s,-4,1),a=this._cp3(s,n,.882353,-1.94),h=this._sub(a,n);this._crossProduct(h,this._sub(r,n))>0?this._rightPerpendicular(h):this._leftPerpendicular(h);const l=[h[0]/8,h[1]/8],c=this._sub(this._mid(n,a),l),u=this._sub(this._mix(n,.75,a,.25),l),f=this._sub(this._mix(n,.25,a,.75),l),m=[n];this._addBezier2(m,n,u,c,3),this._addBezier2(m,c,f,a,3),e.push(m);for(let _=0;_<8;_++){const p=m[2*_+1],d=[this._clone(p)];d.push(this._add(p,[h[0]/4,h[1]/4])),e.push(d)}break}case T.Arc90Degrees:{const n=this._cp2(s,0,-1),a=this._cp3(s,n,.5,1),h=[n];this._add90DegArc(h,n,r,a),e.push(h);break}case T.FullGeometry:default:e.push(s)}}return e}},yi=class Jt{static local(){return Jt.instance===null&&(Jt.instance=new Jt),Jt.instance}execute(t,e,s,i,r){return new Hr(t,e,s)}};yi.instance=null;let Hr=class extends Re{constructor(t,e,s){super(t,!0,!0),this._curveHelper=new ct,this._beginCut=(e.beginCut!==void 0?e.beginCut:1)*s,this._endCut=(e.endCut!==void 0?e.endCut:1)*s,this._middleCut=(e.middleCut!==void 0?e.middleCut:0)*s,this._invert=e.invert!==void 0&&e.invert,this._beginCut<0&&(this._beginCut=0),this._endCut<0&&(this._endCut=0),this._middleCut<0&&(this._middleCut=0)}processPath(t){const e=this._beginCut,s=this._endCut,i=this._middleCut,r=this._curveHelper.calculatePathLength(t),n=[];if(this._invert){if(!(e===0&&s===0&&i===0))if(e+s+i>=r)n.push(t);else{let a=this._curveHelper.getSubCurve(t,0,e);a&&n.push(a),a=this._curveHelper.getSubCurve(t,.5*(r-i),.5*(r+i)),a&&n.push(a),a=this._curveHelper.getSubCurve(t,r-s,s),a&&n.push(a)}}else if(e===0&&s===0&&i===0)n.push(t);else if(!(e+s+i>=r))if(i===0){const a=this._curveHelper.getSubCurve(t,e,r-s);a&&n.push(a)}else{let a=this._curveHelper.getSubCurve(t,e,.5*(r-i));a&&n.push(a),a=this._curveHelper.getSubCurve(t,.5*(r+i),r-s),a&&n.push(a)}return n.length===0?null:{paths:n}}};const vs=1e-7;let Fe=class{constructor(){this._values=[],this.extPtGap=0,this.ctrlPtGap=0,this._length=0,this._currentValue=0}isEmpty(){return this._values.length===0}size(){return this._values.length}init(t,e,s=!0){if(this._setEmpty(),!t||t.length===0)return!1;for(let i=0;i<t.length;i++){let r=Math.abs(t[i]);s&&r<vs&&(r=vs),this._values.push(r),this._length+=r}return e&&1&t.length&&(this._length*=2),this._length!==0&&(this.ctrlPtGap=this.extPtGap=0,this._currentValue=-1,!0)}scale(t){const e=this._values?this._values.length:0;for(let s=0;s<e;++s)this._values[s]*=t;this._length*=t,this.extPtGap*=t,this.ctrlPtGap*=t}addValue(t){this._length+=t,this._values.push(t)}firstValue(){return this._values[0]}lastValue(){return this._values[this._values.length-1]}nextValue(){return this._currentValue++,this._currentValue===this._values.length&&(this._currentValue=0),this._values[this._currentValue]}reset(){this._currentValue=-1}length(){return this._length}_setEmpty(){this.extPtGap=this.ctrlPtGap=this._length=0,this._currentValue=-1,this._values.length=0}},ot=class{constructor(){this.pt=null,this.ca=0,this.sa=0}};var _t;(function(o){o[o.FAIL=0]="FAIL",o[o.END=1]="END",o[o.CONTINUE=2]="CONTINUE"})(_t||(_t={}));let ve=class{constructor(){this.reset()}reset(){this.segment=-1,this.segmentLength=0,this.abscissa=0,this.isPathEnd=!1,this.isPartEnd=!1}isValid(){return this.segment!==-1}copyTo(t){t.segment=this.segment,t.segmentLength=this.segmentLength,t.abscissa=this.abscissa,t.isPathEnd=this.isPathEnd,t.isPartEnd=this.isPartEnd}},Ge=class extends ct{constructor(t=0,e=!1){super(t,e),this._tolerance=de,this._currentPosition=new ve}updateTolerance(t){this._tolerance=de*t}init(t,e,s=!0){return s?(this._patternLength=e.length(),this._partExtPtGap=e.extPtGap,this._partCtrlPtGap=e.ctrlPtGap):(this._patternLength=0,this._partExtPtGap=0,this._partCtrlPtGap=0),this._currentPosition.reset(),this._partSegCount=0,this._path=t,this._seg=-1,this._setPosAtNextPart()}curPositionIsValid(){return this._currentPosition.isValid()}nextPosition(t,e=_t.FAIL){const s=new ve;return!!this._nextPosition(t,s,null,e)&&(s.copyTo(this._currentPosition),!0)}curPointAndAngle(t){t.pt=this._getPoint(this._currentPosition);const[e,s]=this._getAngle(this._currentPosition);t.ca=e,t.sa=s}nextPointAndAngle(t,e,s=_t.FAIL){const i=new ve;if(!this._nextPosition(t,i,null,s))return!1;i.copyTo(this._currentPosition),e.pt=this._getPoint(i);const[r,n]=this._getAngle(i);return e.ca=r,e.sa=n,!0}nextCurve(t){if(t===0)return null;const e=[],s=new ve;return this._nextPosition(t,s,e,_t.END)?(s.copyTo(this._currentPosition),e):null}isPathEnd(){return this._currentPosition.isPathEnd}getPathEnd(){if(this._currentPosition.segment===-1)throw new Error("missing segment");return this._path[this._currentPosition.segment+1]}_nextPosition(t,e,s,i){if(this._currentPosition.isPathEnd)return!1;let r=this._currentPosition.abscissa;for(this._currentPosition.segmentLength>0&&(r/=this._currentPosition.segmentLength),this._currentPosition.copyTo(e);e.abscissa+t*this._partLengthRatio>e.segmentLength+this._tolerance;){if(s){if(s.length===0)if(r===0){const a=this._path[e.segment];s.push([a[0],a[1]])}else s.push(this.getSegCoord2D(this._path,e.segment,r));const n=this._path[e.segment+1];s.push([n[0],n[1]])}if(r=0,t-=(e.segmentLength-e.abscissa)/this._partLengthRatio,this._partSegCount)e.segment=this._nextSegment(),e.segmentLength=this.calculateSegLength(this._path,e.segment),e.abscissa=0,this._partSegCount--;else{if(!this._setPosAtNextPart())return i!==_t.FAIL&&(e.segmentLength=this.calculateSegLength(this._path,e.segment),e.isPartEnd=!0,i===_t.END?(e.abscissa=e.segmentLength,e.isPathEnd=!0):e.abscissa=e.segmentLength+t,!0);this._currentPosition.copyTo(e)}}if(e.abscissa+=t*this._partLengthRatio,s){if(s.length===0)if(r===0){const a=this._path[e.segment];s.push([a[0],a[1]])}else s.push(this.getSegCoord2D(this._path,e.segment,r));const n=e.abscissa/e.segmentLength;if(n===1){const a=this._path[e.segment+1];s.push([a[0],a[1]])}else s.push(this.getSegCoord2D(this._path,e.segment,n))}return this._partSegCount||Math.abs(e.abscissa-e.segmentLength)<this._tolerance&&(e.isPathEnd=this._partIsLast,e.isPartEnd=!0),!0}_getPoint(t){if(t.segment===-1)throw new Error("missing segment");const e=t.segmentLength<=0?0:t.abscissa/t.segmentLength;return this.getSegCoord2D(this._path,t.segment,e)}_getAngle(t){if(t.segment===-1)throw new Error("missing segment");const e=t.segmentLength<=0?0:t.abscissa/t.segmentLength;return this.getSegAngleCS(this._path,t.segment,e)}_setPosAtNextPart(){for(;this._partSegCount;)this._hasNextSegment()&&this._nextSegment(),this._partSegCount--;if(!this._hasNextSegment())return!1;for(this._partLength=0,this._partIsLast=!0,this._partSegCount=0;this._hasNextSegment();)if(this._partLength+=this.calculateSegLength(this._path,this._nextSegment()),this._partSegCount++,me(this._path[this._getEndPointIndex()])===1){this._partIsLast=!this._hasNextSegment();break}let t=this._partSegCount;for(;t;)this._previousSegment(),--t;this._currentPosition.segment=this._nextSegment(),this._currentPosition.segmentLength=this.calculateSegLength(this._path,this._currentPosition.segment),this._currentPosition.abscissa=0,this._currentPosition.isPathEnd=this._currentPosition.isPartEnd=!1,--this._partSegCount;const e=this._getStartPointIndex();this._ctrlPtBegin=me(this._path[e])===1;let s=e+this._partSegCount+1;if(s>=this._path.length&&(s=0),this._ctrlPtEnd=me(this._path[s])===1,this._patternLength>0){const i=this._ctrlPtBegin?this._partCtrlPtGap:this._partExtPtGap,r=this._ctrlPtEnd?this._partCtrlPtGap:this._partExtPtGap;let n=Math.round((this._partLength-(i+r))/this._patternLength);n<=0&&(n=i+r>0?0:1),this._partLengthRatio=this._partLength/(i+r+n*this._patternLength),this._partLengthRatio<.01&&(this._partLengthRatio=1)}else this._partLengthRatio=1;return!0}_hasNextSegment(){return this._seg<this._path.length-2}_previousSegment(){return--this._seg}_nextSegment(){return++this._seg}_getStartPointIndex(){return this._seg}_getEndPointIndex(){return this._seg+1}},Pi=class qt{static local(){return qt.instance===null&&(qt.instance=new qt),qt.instance}execute(t,e,s,i,r){return new Dr(t,e,s)}};Pi.instance=null;let Dr=class extends Re{constructor(t,e,s){super(t,!0,!0),this._firstCurve=null,this._walker=new Ge,this._walker.updateTolerance(s),this._endings=e.lineDashEnding,this._customDashPos=-(e.offsetAlongLine??0)*s,this._offsetAtEnd=(e.customEndingOffset??0)*s,this._pattern=new Fe,this._pattern.init(e.dashTemplate,!0),this._pattern.scale(s)}processPath(t){if(this._pattern.length()===0)return this.iteratePath=!1,{paths:[t]};if(!this.iteratePath){let i=!0;switch(this._endings){case J.HalfPattern:case J.HalfGap:default:this._pattern.extPtGap=0;break;case J.FullPattern:this.isClosed||(this._pattern.extPtGap=.5*this._pattern.firstValue());break;case J.FullGap:this.isClosed||(this._pattern.extPtGap=.5*this._pattern.lastValue());break;case J.NoConstraint:this.isClosed||(i=!1);break;case J.Custom:this.isClosed||(this._pattern.extPtGap=.5*this._offsetAtEnd)}const r=this._walker.calculatePathLength(t);if(this._pattern.isEmpty()||r<.1*this._pattern.length())return{paths:[t]};if(!this._walker.init(t,this._pattern,i))return{paths:[t]}}let e;if(this.iteratePath)e=this._pattern.nextValue();else{let i;switch(this._endings){case J.HalfPattern:default:i=.5*this._pattern.firstValue();break;case J.HalfGap:i=.5*-this._pattern.lastValue();break;case J.FullGap:i=-this._pattern.lastValue();break;case J.FullPattern:i=0;break;case J.NoConstraint:case J.Custom:i=-this._customDashPos}let r=i/this._pattern.length();r-=Math.floor(r),i=r*this._pattern.length(),this._pattern.reset(),e=this._pattern.nextValue();let n=!1;for(;i>=e;)i-=e,e=this._pattern.nextValue(),n=!n;e-=i,n?(this._walker.nextPosition(e),e=this._pattern.nextValue()):this.isClosed&&(this._firstCurve=this._walker.nextCurve(e),e=this._pattern.nextValue(),this._walker.nextPosition(e),e=this._pattern.nextValue())}let s=this._walker.nextCurve(e);return s?this._walker.isPathEnd()?(this.iteratePath=!1,this._firstCurve&&(this._firstCurve.splice(0,1),Et.mergePath(s,this._firstCurve),this._firstCurve=null)):(e=this._pattern.nextValue(),!this._walker.nextPosition(e)||this._walker.isPathEnd()?(this.iteratePath=!1,this._firstCurve&&(s=this._firstCurve,this._firstCurve=null)):this.iteratePath=!0):(this.iteratePath=!1,s=this._firstCurve,this._firstCurve=null),{paths:[s]}}},xi=class jt{static local(){return jt.instance===null&&(jt.instance=new jt),jt.instance}execute(t,e,s,i,r){return new Yr(t,e,s,i,r)}};xi.instance=null;let Yr=class{constructor(t,e,s,i,r){switch(this._inputGeometries=t,this._tileKey=i,this._geometryEngine=r,this._width=(e.width!==void 0?e.width:2)*s,e.method){case Xt.Mitered:case Xt.Bevelled:case Xt.Rounded:case Xt.TrueBuffer:case Xt.Square:}this._option=e.option}next(){let t;for(;t=this._inputGeometries.next();){if(R(t)&&this._width>0){if(Math.min(t.xmax-t.xmin,t.ymax-t.ymin)-2*this._width<0)return t;const e=[];return e.push([[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]),e.push([[t.xmin+this._width,t.ymin+this._width],[t.xmax-this._width,t.ymin+this._width],[t.xmax-this._width,t.ymax-this._width],[t.xmin+this._width,t.ymax-this._width],[t.xmin+this._width,t.ymin+this._width]]),{rings:e}}if($(t)){let e=null;const s=this._geometryEngine;let i=t;if(this._tileKey&&(i=ms(t,Math.abs(this._width)+1),!i||!i.rings||i.rings.length===0))continue;if(V(s)&&(e=s.buffer(cs.WebMercator,i,-this._width,1)),this._width>0){const r=[];for(const n of t.rings)n&&r.push(n);if(e)for(const n of e.rings)n&&r.push(n.reverse());if(r.length)return{rings:r}}}}return null}},Mi=class Kt{static local(){return Kt.instance===null&&(Kt.instance=new Kt),Kt.instance}execute(t,e,s,i,r){return new Vr(t,e,s)}};Mi.instance=null;let Vr=class extends Re{constructor(t,e,s){super(t,!1,!0),this._curveHelper=new ct,this._length=(e.length!==void 0?e.length:20)*s,this._angle=e.angle!==void 0?e.angle:225,this._position=e.position!==void 0?e.position:50,this._length<0&&(this._length=-this._length),this._position<20&&(this._position=20),this._position>80&&(this._position=80),this._mirror=!1}processPath(t){if(this._curveHelper.isEmpty(t,!1))return null;const e=t[0],s=t[t.length-1],i=[s[0]-e[0],s[1]-e[1]];this._curveHelper.normalize(i);const r=[e[0]+(s[0]-e[0])*this._position/100,e[1]+(s[1]-e[1])*this._position/100],n=Math.cos((90-this._angle)/180*Math.PI);let a=Math.sin((90-this._angle)/180*Math.PI);return this._mirror&&(a=-a),this._mirror=!this._mirror,{paths:[[e,[r[0]-this._length/2*n,r[1]-this._length/2*a],[r[0]+this._length/2*n,r[1]+this._length/2*a],s]]}}},bi=class Zt{static local(){return Zt.instance===null&&(Zt.instance=new Zt),Zt.instance}execute(t,e,s,i,r){return new Br(t,e,s)}};bi.instance=null;let Br=class{constructor(t,e,s){this._inputGeometries=t,this._offsetX=e.offsetX!==void 0?e.offsetX*s:0,this._offsetY=e.offsetY!==void 0?-e.offsetY*s:0}next(){let t=this._inputGeometries.next();for(;t;){if(R(t))return{xmin:t.xmin+this._offsetX,xmax:t.xmax+this._offsetX,ymin:t.ymin+this._offsetY,ymax:t.ymax+this._offsetY};if($(t)){const e=N(t);return this._moveMultipath(e.rings,this._offsetX,this._offsetY),e}if(A(t)){const e=N(t);return this._moveMultipath(e.paths,this._offsetX,this._offsetY),e}if(rt(t)){const e=N(t);return this._movePath(e.points,this._offsetX,this._offsetY),e}if(St(t))return{x:t.x+this._offsetX,y:t.y+this._offsetY};t=this._inputGeometries.next()}return null}_moveMultipath(t,e,s){if(t)for(const i of t)this._movePath(i,e,s)}_movePath(t,e,s){if(t)for(const i of t)i[0]+=e,i[1]+=s}},Si=class Qt{static local(){return Qt.instance===null&&(Qt.instance=new Qt),Qt.instance}execute(t,e,s,i,r){return new Wr(t,e,s,i,r)}};Si.instance=null;let Wr=class{constructor(t,e,s,i,r){this._inputGeometries=t,this._tileKey=i,this._geometryEngine=r,this._curveHelper=new ct,this._offset=(e.offset??1)*s,this._method=e.method,this._option=e.option,this._offsetFlattenError=de*s}next(){let t;for(;t=this._inputGeometries.next();){if(this._offset===0)return t;if(R(t)){if(this._method===bt.Rounded&&this._offset>0){const i=[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]],r=this._curveHelper.offset(i,-this._offset,this._method,4,this._offsetFlattenError);return r?{rings:[r]}:null}if(Math.min(t.xmax-t.xmin,t.ymax-t.ymin)+2*this._offset>0)return{xmin:t.xmin-this._offset,xmax:t.xmax+this._offset,ymin:t.ymin-this._offset,ymax:t.ymax+this._offset}}const e=this._geometryEngine;if(Z(e))return null;let s=t;if($(t)){if(this._tileKey&&(s=ms(t,Math.abs(this._offset)+1),!s||!s.rings||s.rings.length===0))continue}else if(A(t)&&this._tileKey&&(s=hi(t,Math.abs(this._offset)+1),!s||!s.paths||s.paths.length===0))continue;return e.offset(cs.WebMercator,s,-this._offset,1,this._method,4,this._offsetFlattenError)}return null}},Ci=class te{static local(){return te.instance===null&&(te.instance=new te),te.instance}execute(t,e,s,i,r){return new Ur(t,e,s)}};Ci.instance=null;let Ur=class{constructor(t,e,s){this._inputGeometries=t,this._reverse=e.reverse===void 0||e.reverse}next(){let t=this._inputGeometries.next();for(;t;){if(!this._reverse)return t;if(A(t)){const e=N(t);return Nr(e.paths),e}t=this._inputGeometries.next()}return null}},wi=class ee{static local(){return ee.instance===null&&(ee.instance=new ee),ee.instance}execute(t,e,s,i,r){return new Jr(t,e,s)}};wi.instance=null;let Jr=class{constructor(t,e,s){this._inputGeometries=t,this._rotateAngle=e.angle!==void 0?e.angle*Math.PI/180:0}next(){let t=this._inputGeometries.next();for(;t;){if(this._rotateAngle===0)return t;const e=U();Ot(e,t);const s=(e[2]+e[0])/2,i=(e[3]+e[1])/2;if(R(t)){const r={rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]};return this._rotateMultipath(r.rings,s,i),r}if($(t)){const r=N(t);return this._rotateMultipath(r.rings,s,i),r}if(A(t)){const r=N(t);return this._rotateMultipath(r.paths,s,i),r}if(rt(t)){const r=N(t);return this._rotatePath(r.points,s,i),r}if(St(t))return t;t=this._inputGeometries.next()}return null}_rotateMultipath(t,e,s){if(t)for(const i of t)this._rotatePath(i,e,s)}_rotatePath(t,e,s){if(t){const i=Math.cos(this._rotateAngle),r=Math.sin(this._rotateAngle);for(const n of t){const a=n[0]-e,h=n[1]-s;n[0]=e+a*i-h*r,n[1]=s+a*r+h*i}}}},ki=class se{static local(){return se.instance===null&&(se.instance=new se),se.instance}execute(t,e,s,i,r){return new qr(t,e,s)}};ki.instance=null;let qr=class{constructor(t,e,s){this._inputGeometries=t,this._xFactor=e.xScaleFactor!==void 0?e.xScaleFactor:1.15,this._yFactor=e.yScaleFactor!==void 0?e.yScaleFactor:1.15}next(){let t=this._inputGeometries.next();for(;t;){if(this._xFactor===1&&this._yFactor===1)return t;const e=U();Ot(e,t);const s=(e[2]+e[0])/2,i=(e[3]+e[1])/2;if(R(t)){const r={rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]};return this._scaleMultipath(r.rings,s,i),r}if($(t)){const r=N(t);return this._scaleMultipath(r.rings,s,i),r}if(A(t)){const r=N(t);return this._scaleMultipath(r.paths,s,i),r}if(rt(t)){const r=N(t);return this._scalePath(r.points,s,i),r}if(St(t))return t;t=this._inputGeometries.next()}return null}_scaleMultipath(t,e,s){if(t)for(const i of t)this._scalePath(i,e,s)}_scalePath(t,e,s){if(t)for(const i of t){const r=(i[0]-e)*this._xFactor,n=(i[1]-s)*this._yFactor;i[0]=e+r,i[1]=s+n}}},vi=class ie{static local(){return ie.instance===null&&(ie.instance=new ie),ie.instance}execute(t,e,s,i,r){return new jr(t,e,s)}};vi.instance=null;let jr=class{constructor(t,e,s){this._inputGeometries=t,this._height=(e.amplitude!==void 0?e.amplitude:2)*s,this._period=(e.period!==void 0?e.period:3)*s,this._style=e.waveform,this._height<=0&&(this._height=Math.abs(this._height)),this._period<=0&&(this._period=Math.abs(this._period)),this._pattern=new Fe,this._pattern.addValue(this._period),this._pattern.addValue(this._period),this._walker=new Ge,this._walker.updateTolerance(s)}next(){let t=this._inputGeometries.next();for(;t;){if(this._height===0||this._period===0)return t;if(A(t)){const e=this._processGeom(t.paths);if(e.length)return{paths:e}}if($(t)){const e=this._processGeom(t.rings);if(e.length)return{rings:e}}t=this._inputGeometries.next()}return null}_processGeom(t){const e=[];for(const s of t)if(this._walker.init(s,this._pattern))switch(this._style){case Ce.Sinus:default:e.push(this._constructCurve(s,!1));break;case Ce.Square:e.push(this._constructSquare(s));break;case Ce.Triangle:e.push(this._constructTriangle(s));break;case Ce.Random:e.push(this._constructCurve(s,!0))}else e.push(s);return e}_constructCurve(t,e){const s=new Et,i=this._walker.calculatePathLength(t);let r=Math.round(i/this._period);r===0&&(r=1);const n=r*16+1,a=i/r,h=this._period/16,l=1/n,c=2*Math.PI*i/a,u=2*Math.PI*Math.random(),f=2*Math.PI*Math.random(),m=2*Math.PI*Math.random(),_=.75-Math.random()/2,p=.75-Math.random()/2,d=new ot;this._walker.curPointAndAngle(d),s.startPath(d.pt);let g=0;for(;;){if(!this._walker.nextPointAndAngle(h,d)){s.lineTo(t[t.length-1]);break}{const y=g;let x;if(g+=l,e){const P=this._height/2*(1+.3*Math.sin(_*c*y+u));x=P*Math.sin(c*y+f),x+=P*Math.sin(p*c*y+m),x/=2}else x=.5*this._height*Math.sin(.5*c*y);s.lineTo([d.pt[0]-x*d.sa,d.pt[1]+x*d.ca])}}return s.path()}_constructSquare(t){const e=new Et,s=this._walker.calculatePathLength(t);Math.round(s/this._period);let i=!0;for(;;){let r=!1;if(this._walker.curPositionIsValid()){const n=new ot;this._walker.curPointAndAngle(n);const a=new ot;if(this._walker.nextPointAndAngle(this._period,a)){const h=new ot;this._walker.nextPointAndAngle(this._period,h)&&(i?(e.startPath(n.pt),i=!1):e.lineTo(n.pt),e.lineTo([n.pt[0]-this._height/2*n.sa,n.pt[1]+this._height/2*n.ca]),e.lineTo([a.pt[0]-this._height/2*a.sa,a.pt[1]+this._height/2*a.ca]),e.lineTo([a.pt[0]+this._height/2*a.sa,a.pt[1]-this._height/2*a.ca]),e.lineTo([h.pt[0]+this._height/2*h.sa,h.pt[1]-this._height/2*h.ca]),r=!0)}}if(!r){e.lineTo(this._walker.getPathEnd());break}}return e.path()}_constructTriangle(t){const e=new Et,s=this._walker.calculatePathLength(t);Math.round(s/this._period);let i=!0;for(;;){let r=!1;if(this._walker.curPositionIsValid()){const n=new ot;this._walker.curPointAndAngle(n);const a=new ot;if(this._walker.nextPointAndAngle(this._period/2,a)){const h=new ot;this._walker.nextPointAndAngle(this._period,h)&&(this._walker.nextPosition(this._period/2)&&(i?(e.startPath(n.pt),i=!1):e.lineTo(n.pt),e.lineTo([a.pt[0]-this._height/2*a.sa,a.pt[1]+this._height/2*a.ca]),e.lineTo([h.pt[0]+this._height/2*h.sa,h.pt[1]-this._height/2*h.ca])),r=!0)}}if(!r){e.lineTo(this._walker.getPathEnd());break}}return e.path()}},Ii=class re{static local(){return re.instance===null&&(re.instance=new re),re.instance}execute(t,e,s,i,r){return new Kr(t,e,s)}};Ii.instance=null;let Kr=class extends ye{constructor(t,e,s){super(t,!0,!0),this._geometryWalker=new Ge,this._geometryWalker.updateTolerance(s),this._angleToLine=e.angleToLine??!0,this._offset=(e.offset?e.offset:0)*s,this._originalEndings=e.endings,this._offsetAtEnd=(e.customEndingOffset?e.customEndingOffset:0)*s,this._position=-(e.offsetAlongLine?e.offsetAlongLine:0)*s,this._pattern=new Fe,this._pattern.init(e.placementTemplate,!1),this._pattern.scale(s),this._endings=this._originalEndings}processPath(t){if(this._pattern.isEmpty())return null;let e;if(this.iteratePath)e=this._pattern.nextValue();else{this._originalEndings===nt.WithFullGap&&this.isClosed?this._endings=nt.WithMarkers:this._endings=this._originalEndings,this._pattern.extPtGap=0;let i,r=!0;switch(this._endings){case nt.NoConstraint:i=-this._position,i=this._adjustPosition(i),r=!1;break;case nt.WithHalfGap:default:i=-this._pattern.lastValue()/2;break;case nt.WithFullGap:i=-this._pattern.lastValue(),this._pattern.extPtGap=this._pattern.lastValue();break;case nt.WithMarkers:i=0;break;case nt.Custom:i=-this._position,i=this._adjustPosition(i),this._pattern.extPtGap=.5*this._offsetAtEnd}if(!this._geometryWalker.init(t,this._pattern,r))return null;this._pattern.reset();let n=0;for(;i>n;)i-=n,n=this._pattern.nextValue();n-=i,e=n,this.iteratePath=!0}const s=new ot;return this._geometryWalker.nextPointAndAngle(e,s)?this._endings===nt.WithFullGap&&this._geometryWalker.isPathEnd()?(this.iteratePath=!1,null):this._endings===nt.WithMarkers&&this._geometryWalker.isPathEnd()&&(this.iteratePath=!1,this.isClosed)?null:(this.internalPlacement.setTranslate(s.pt[0]-this._offset*s.sa,s.pt[1]+this._offset*s.ca),this._angleToLine&&this.internalPlacement.setRotateCS(s.ca,s.sa),this.internalPlacement):(this.iteratePath=!1,null)}_adjustPosition(t){let e=t/this._pattern.length();return e-=Math.floor(e),e*this._pattern.length()}},Li=class ne{static local(){return ne.instance===null&&(ne.instance=new ne),ne.instance}execute(t,e,s,i,r){return new Zr(t,e,s)}};Li.instance=null;let Zr=class extends ye{constructor(t,e,s){super(t,!1,!0),this._curveHelper=new ct,this._angleToLine=e.angleToLine===void 0||e.angleToLine,this._offset=e.offset!==void 0?e.offset*s:0,this._type=e.extremityPlacement,this._position=e.offsetAlongLine!==void 0?e.offsetAlongLine*s:0,this._beginProcessed=!1}processPath(t){let e;switch(this._type){case dt.Both:default:this._beginProcessed?(e=this._atExtremities(t,this._position,!1),this._beginProcessed=!1,this.iteratePath=!1):(e=this._atExtremities(t,this._position,!0),this._beginProcessed=!0,this.iteratePath=!0);break;case dt.JustBegin:e=this._atExtremities(t,this._position,!0);break;case dt.JustEnd:e=this._atExtremities(t,this._position,!1);case dt.None:}return e}_atExtremities(t,e,s){const i=t.length;if(i<2)return null;const r=s?1:i-2,n=s?i:-1,a=s?1:-1;let h,l=0,c=s?t[0]:t[i-1];for(let u=r;u!==n;u+=a){h=c,c=t[u];const f=this._curveHelper.calculateLength(h,c);if(l+f>e){const m=(e-l)/f,[_,p]=this._curveHelper.getAngleCS(h,c,m),d=is(h,c,m);return this.internalPlacement.setTranslate(d[0]-this._offset*p,d[1]+this._offset*_),this._angleToLine&&this.internalPlacement.setRotateCS(-_,-p),this.internalPlacement}l+=f}return null}},Ti=class oe{static local(){return oe.instance===null&&(oe.instance=new oe),oe.instance}execute(t,e,s,i,r){return new Qr(t,e,s)}};Ti.instance=null;let Qr=class extends ye{constructor(t,e,s){super(t,!0,!0),this._walker=new Ge,this._walker.updateTolerance(s),this._angleToLine=e.angleToLine===void 0||e.angleToLine,this._offset=e.offset!==void 0?e.offset*s:0,this._beginGap=e.beginPosition!==void 0?e.beginPosition*s:0,this._endGap=e.endPosition!==void 0?e.endPosition*s:0,this._flipFirst=e.flipFirst===void 0||e.flipFirst,this._pattern=new Fe,this._pattern.init(e.positionArray,!1,!1),this._subPathLen=0,this._posCount=this._pattern.size(),this._isFirst=!0,this._prevPos=0}processPath(t){if(this._pattern.isEmpty())return null;let e;if(this.iteratePath){const a=this._pattern.nextValue()*this._subPathLen,h=this._beginGap+a;e=h-this._prevPos,this._prevPos=h}else{if(this._posCount=this._pattern.size(),this._isFirst=!0,this._prevPos=0,this._subPathLen=this._walker.calculatePathLength(t)-this._beginGap-this._endGap,this._subPathLen<0)return this.iteratePath=!1,null;if(!this._walker.init(t,this._pattern,!1))return null;this._pattern.reset();const a=this._pattern.nextValue()*this._subPathLen,h=this._beginGap+a;e=h-this._prevPos,this._prevPos=h,this.iteratePath=!0}const s=new ot;if(!this._walker.nextPointAndAngle(e,s,_t.END))return this.iteratePath=!1,null;this.internalPlacement.setTranslate(s.pt[0]-this._offset*s.sa,s.pt[1]+this._offset*s.ca);const i=this._isFirst&&this._flipFirst;let r,n;return this._angleToLine?(r=s.ca,n=s.sa):(r=1,n=0),i&&(r=-r,n=-n),this.internalPlacement.setRotateCS(r,n),this._isFirst=!1,this._posCount--,this._posCount===0&&(this.iteratePath=!1),this.internalPlacement}};const mt=512,tn=10,en=12,Is=25,et=24;function sn(o){return o.rings!==void 0}let $i=class ae{static local(){return ae.instance===null&&(ae.instance=new ae),ae.instance}execute(t,e,s,i,r){return new rn(t,e,s,i,r)}};$i.instance=null;let rn=class Pt{constructor(t,e,s,i,r){if(this._xMin=0,this._xMax=0,this._yMin=0,this._yMax=0,this._currentX=0,this._currentY=0,this._accelerationMap=null,this._testInsidePolygon=!1,this._verticalSubdivision=!0,this._stepX=Math.abs(e.stepX??16)*s,this._stepY=Math.abs(e.stepY??16)*s,this._stepX!==0&&this._stepY!==0&&t&&sn(t)&&t.rings){if(this._gridType=e.gridType??He.Fixed,this._gridType===He.Random){const n=e.seed??13,a=1;this._randomLCG=new ei(n*a),this._randomness=(e.randomness??100)/100,this._gridAngle=0,this._shiftOddRows=!1,this._cosAngle=1,this._sinAngle=0,this._offsetX=0,this._offsetY=0,this._buildRandomValues()}else{if(this._randomness=0,this._gridAngle=e.gridAngle??0,this._shiftOddRows=e.shiftOddRows??!1,this._offsetX=(e.offsetX??0)*s,this._offsetY=(e.offsetY??0)*s,this._cosAngle=Math.cos(this._gridAngle/180*Math.PI),this._sinAngle=-Math.sin(this._gridAngle/180*Math.PI),this._stepX)if(this._offsetX<0)for(;this._offsetX<-.5*this._stepX;)this._offsetX+=this._stepX;else for(;this._offsetX>=.5*this._stepX;)this._offsetX-=this._stepX;if(this._stepY)if(this._offsetY<0)for(;this._offsetY<-.5*this._stepY;)this._offsetY+=this._stepY;else for(;this._offsetY>=.5*this._stepY;)this._offsetY-=this._stepY}if(this._graphicOriginX=0,this._graphicOriginY=0,i!=null){const[n,a,h]=i.split("/"),l=parseFloat(a),c=parseFloat(h);this._graphicOriginX=-c*mt,this._graphicOriginY=l*mt,this._testInsidePolygon=!0}this._internalPlacement=new Ft,this._calculateMinMax(t),this._geometry=t}}next(){return this._geometry?this._nextInside():null}_buildRandomValues(){if(!Pt._randValues){Pt._randValues=[];for(let t=0;t<et;t++)for(let e=0;e<et;e++)Pt._randValues.push(this._randomLCG.getFloat()),Pt._randValues.push(this._randomLCG.getFloat())}}_calculateMinMax(t){let e,s,i,r,n,a,h,l,c,u,f,m,_,p;this._xMin=0,this._xMax=0,this._yMin=0,this._yMax=0,h=l=_=f=Number.MAX_VALUE,c=u=p=m=-Number.MAX_VALUE;const d=this._cosAngle!==1;let g=0;for(const P of t.rings){const M=P?P.length:0;for(let S=0;S<M;S++)a=P[S][0],n=P[S][1],e=a-this._graphicOriginX-this._offsetX,s=n-this._graphicOriginY-this._offsetY,d?(i=this._cosAngle*e-this._sinAngle*s,r=this._sinAngle*e+this._cosAngle*s):(i=e,r=s),h=Math.min(h,i),c=Math.max(c,i),l=Math.min(l,r),u=Math.max(u,r),f=Math.min(f,n),m=Math.max(m,n),_=Math.min(_,a),p=Math.max(p,a),g++}f=f!==Number.MAX_VALUE?f:-mt-this._stepY,m=m!==-Number.MAX_VALUE?m:this._stepY,_=_!==Number.MAX_VALUE?_:-this._stepX,p=p!==-Number.MAX_VALUE?p:mt+this._stepX;const y=m-f,x=p-_;if(this._verticalSubdivision=y>=x,this._polygonMin=this._verticalSubdivision?f:_,this._testInsidePolygon){let P=0-this._graphicOriginX-this._offsetX-this._stepX,M=mt-this._graphicOriginX-this._offsetX+this._stepX,S=-mt-this._graphicOriginY-this._offsetY-this._stepY,w=0-this._graphicOriginY-this._offsetY+this._stepY;if(d){const I=[[P,S],[P,w],[M,S],[M,w]];P=S=Number.MAX_VALUE,M=w=-Number.MAX_VALUE;for(const v of I){const C=this._cosAngle*v[0]-this._sinAngle*v[1],L=this._sinAngle*v[0]+this._cosAngle*v[1];P=Math.min(P,C),M=Math.max(M,C),S=Math.min(S,L),w=Math.max(w,L)}}h=h!==Number.MAX_VALUE?Math.max(h,P):P,l=l!==Number.MAX_VALUE?Math.max(l,S):S,c=c!==-Number.MAX_VALUE?Math.min(c,M):M,u=u!==-Number.MAX_VALUE?Math.min(u,w):w}this._xMin=Math.round(h/this._stepX),this._xMax=Math.round(c/this._stepX),this._yMin=Math.round(l/this._stepY),this._yMax=Math.round(u/this._stepY),this._currentX=this._xMax+1,this._currentY=this._yMin-1,this._testInsidePolygon&&g>en&&(y>Is||x>Is)&&this._buildAccelerationMap(t,_,p,f,m)}_buildAccelerationMap(t,e,s,i,r){const{rings:n}=t,a=new Map,h=this._verticalSubdivision,l=h?r-i:s-e;let c=Math.ceil(l/tn);if(c<=1)return;const u=Math.floor(l/c);let f,m,_,p,d,g,y,x,P,M;c++,this._delta=u,h?(x=-mt-this._stepY,P=this._stepY,M=i):(x=-this._stepX,P=mt+this._stepX,M=e);for(let S=0;S<n.length;S++)if(f=n[S],!(f.length<2))for(let w=1;w<f.length;w++){if(m=f[w-1],_=f[w],h){if(m[1]===_[1]||m[1]<x&&_[1]<x||m[1]>P&&_[1]>P)continue;p=Math.min(m[1],_[1]),d=Math.max(m[1],_[1])}else{if(m[0]===_[0]||m[0]<x&&_[0]<x||m[0]>P&&_[0]>P)continue;p=Math.min(m[0],_[0]),d=Math.max(m[0],_[0])}for(;p<d;)g=Math.floor((p-M)/u),Ls(g,S,w,a),p+=u;y=Math.floor((d-M)/u),y>g&&Ls(y,S,w,a)}this._accelerationMap=a}_nextInside(){for(;;){if(this._currentX>this._xMax){if(this._currentY++,this._currentY>this._yMax)return null;this._currentX=this._xMin,this._shiftOddRows&&this._currentY%2&&this._currentX--}let t=this._currentX*this._stepX+this._offsetX;this._shiftOddRows&&this._currentY%2&&(t+=.5*this._stepX);const e=this._currentY*this._stepY+this._offsetY;let s,i;if(this._currentX++,this._gridType===He.Random){const r=(this._currentX%et+et)%et,n=(this._currentY%et+et)%et;s=this._graphicOriginX+t+this._stepX*this._randomness*(.5-Pt._randValues[n*et+r])*2/3,i=this._graphicOriginY+e+this._stepY*this._randomness*(.5-Pt._randValues[n*et+r+1])*2/3}else s=this._graphicOriginX+this._cosAngle*t+this._sinAngle*e,i=this._graphicOriginY-this._sinAngle*t+this._cosAngle*e;if(!this._testInsidePolygon||this._isInsidePolygon(s,i,this._geometry))return this._internalPlacement.setTranslate(s,i),this._internalPlacement}}_isInsidePolygon(t,e,s){const{rings:i}=s;if(Z(this._accelerationMap))return nn(t,e,s);const r=this._verticalSubdivision,n=r?e:t,a=Math.floor((n-this._polygonMin)/this._delta),h=this._accelerationMap.get(a);if(!h)return!1;let l,c,u,f,m,_=0;for(const p of h){m=p[0];const d=i[m];if(f=p[1],l=d[f-1],c=d[f],r){if(l[1]>e==c[1]>e)continue;u=(c[0]-l[0])*(e-l[1])-(c[1]-l[1])*(t-l[0])}else{if(l[0]>t==c[0]>t)continue;u=(c[1]-l[1])*(t-l[0])-(c[0]-l[0])*(e-l[1])}u>0?_++:_--}return _!==0}};function nn(o,t,e){const{rings:s}=e;let i,r,n,a=0;for(const h of s){i=h.length;for(let l=1;l<i;++l)r=h[l-1],n=h[l],r[1]>t!=n[1]>t&&((n[0]-r[0])*(t-r[1])-(n[1]-r[1])*(o-r[0])>0?a++:a--)}return a!==0}function Ls(o,t,e,s){let i=s.get(o);i||(i=[],s.set(o,i)),i.push([t,e])}const on=.001;let Ai=class he{static local(){return he.instance===null&&(he.instance=new he),he.instance}execute(t,e,s,i,r){return new an(t,e,s)}};Ai.instance=null;let an=class extends ye{constructor(t,e,s){super(t,!0,!0),this._curveHelper=new ct,this._angleToLine=e.angleToLine===void 0||e.angleToLine,this._offset=e.offset!==void 0?e.offset*s:0,this._relativeTo=e.relativeTo,this._position=e.startPointOffset!==void 0?e.startPointOffset*s:0,this._epsilon=on*s}processPath(t){const e=this._position;if(this._relativeTo===$t.SegmentMidpoint){for(this.iteratePath||(this._segmentCount=t.length,this._curSegment=1,this.iteratePath=!0);this._curSegment<this._segmentCount;){const i=this._curSegment;this._curSegment++;const r=t[i-1],n=t[i],a=this._curveHelper.calculateLength(r,n);if(a<this._epsilon)continue;const h=.5+this._position/a,[l,c]=this._curveHelper.getAngleCS(r,n,h),u=is(r,n,h);return this.internalPlacement.setTranslate(u[0]-this._offset*c,u[1]+this._offset*l),this._angleToLine&&this.internalPlacement.setRotateCS(l,c),this.internalPlacement}return this.iteratePath=!1,null}this._relativeTo===$t.LineEnd&&ss(t);const s=this.onLine(t,e);return this._relativeTo===$t.LineEnd&&ss(t),s}onLine(t,e){let s,i=!1;switch(this._relativeTo){case $t.LineMiddle:default:s=this._curveHelper.calculatePathLength(t)/2+e;break;case $t.LineBeginning:s=e;break;case $t.LineEnd:s=e,i=!0}const r=t.length;let n,a=0,h=t[0];for(let l=1;l<r;++l){n=h,h=t[l];const c=this._curveHelper.calculateLength(n,h);if(a+c>s){const u=(s-a)/c,[f,m]=this._curveHelper.getAngleCS(n,h,u),_=is(n,h,u),p=i?-this._offset:this._offset;return this.internalPlacement.setTranslate(_[0]-p*m,_[1]+p*f),this._angleToLine&&(i?this.internalPlacement.setRotateCS(-f,-m):this.internalPlacement.setRotateCS(f,m)),this.internalPlacement}a+=c}return null}},Ni=class le{static local(){return le.instance===null&&(le.instance=new le),le.instance}execute(t,e,s,i,r){return new ln(t,e,s)}};Ni.instance=null;const hn=1e-15;let ln=class extends ye{constructor(t,e,s){super(t,!0,!0),this._curveHelper=new ct,this._angleToLine=e.angleToLine===void 0||e.angleToLine,this._offset=e.offset!==void 0?e.offset*s:0,this._endPoints=e.placeOnEndPoints===void 0||e.placeOnEndPoints,this._controlPoints=e.placeOnControlPoints===void 0||e.placeOnControlPoints,this._regularVertices=e.placeOnRegularVertices===void 0||e.placeOnRegularVertices,this._tags=[],this._tagIterator=0}processPath(t){if(this.iteratePath||(this._preparePath(t),this.iteratePath=!0),this._tagIterator>=this._tags.length)return this._tags.length=0,this._tagIterator=0,this.iteratePath=!1,null;const e=this._tags[this._tagIterator];this._angleToLine&&this.internalPlacement.setRotate(e[2]);let s=e[0],i=e[1];if(this._offset!==0){const r=Math.cos(e[2]),n=Math.sin(e[2]);s-=this._offset*n,i+=this._offset*r}return this.internalPlacement.setTranslate(s,i),this._tagIterator++,this.internalPlacement}_preparePath(t){this._tags.length=0,this._tagIterator=0;const e=zr(t),s=t.length-1;let i,r,n=0,a=0,h=0,l=0,c=0;for(;n<s;){n++,i=t[n-1],r=t[n];const u=me(i),f=me(r);(this._angleToLine||this._offset!==0)&&(l=this._curveHelper.getAngle(i,r,0)),n===1?e?(a=l,h=u):(this._endPoints||this._controlPoints&&u===1)&&this._tags.push([i[0],i[1],l]):u===1?this._controlPoints&&this._tags.push([i[0],i[1],Ie(c,l)]):this._regularVertices&&this._tags.push([i[0],i[1],Ie(c,l)]),(this._angleToLine||this._offset!==0)&&(c=this._curveHelper.getAngle(i,r,1)),n===s&&(e?f===1||h===1?this._controlPoints&&this._tags.push([r[0],r[1],Ie(c,a)]):this._regularVertices&&this._tags.push([r[0],r[1],Ie(c,a)]):(this._endPoints||this._controlPoints&&f===1)&&this._tags.push([r[0],r[1],c]))}this._tagIterator=0}};function Ie(o,t){const e=Math.PI;for(;Math.abs(t-o)>e+2*hn;)t-o>e?t-=2*e:t+=2*e;return(o+t)/2}let cn=class{constructor(t=un){this._data=[],this._compare=t}get size(){return this._data.length}enqueue(t){if(t==null)return;const{_data:e,_compare:s}=this;e.push(t);let i=e.length-1>>>0;const r=e[i];for(;i>0;){const n=i-1>>1,a=e[n];if(!(s(a,r)<=0))break;e[n]=r,e[i]=a,i=n}}dequeue(){const{_data:t,_compare:e}=this,s=t[0],i=t.pop();if(t.length===0)return s;t[0]=i;let r=0;const n=t.length,a=t[0];let h,l,c=null;for(;;){const u=2*r+1,f=2*r+2;if(c=null,u<n&&(h=t[u],e(h,a)>0&&(c=u)),f<n&&(l=t[f],(c===null&&e(l,a)<=0||c!==null&&e(l,h)<=0)&&(c=f)),c===null)break;t[r]=t[c],t[c]=a,r=c}return s}};const un=(o,t)=>o<t?-1:o>t?1:0,fn=100*222045e-21;function mn(o){const{rings:t}=o;if(!t||t.length===0)return null;const e=Ot(U(),o);if(!e)return null;const s=4*(Math.abs(e[0])+Math.abs(e[2])+Math.abs(e[1])+Math.abs(e[3])+1)*fn;let i=0,r=0;for(let C=0;C<t.length;C++){const L=ji(t[C]);L>r&&(r=L,i=C)}if(Math.abs(r)<=2*s*s){const C=Ze(U(),t[i]);return[(C[0]+C[2])/2,(C[1]+C[3])/2]}const n=Ki(t[i],!1,U());if(n===null)return null;if(t.length===1&&t[0].length<4)return n;const a=[[NaN,NaN],[NaN,NaN],[NaN,NaN],[NaN,NaN]],h=[NaN,NaN,NaN,NaN],l=[NaN,NaN,NaN,NaN];let c=!1,u=Ht(n,o,!0);u.distance===0&&(c=!0,a[0][0]=n[0],a[0][1]=n[1],u=Ht(n,o,!1)),h[0]=u.distance,l[0]=0;const f=[NaN,NaN];let m=!1,_=.25,p=-1;const d=Ze(U(),t[i]);let g=NaN;do if(g=NaN,a[1]=We(o,Ue(d[0],d[2],_),s,e),isNaN(a[1][0])||isNaN(a[1][1])||(u=Ht(a[1],o,!1),g=u.distance),!isNaN(g)&&g>s&&Ne(a[1],o))m=!0,h[1]=g,l[1]=xt(a[1],n);else if(!isNaN(g)&&g>p&&(p=g,f[0]=a[1][0],f[1]=a[1][1]),_-=.01,_<.1){if(!(p>=0))break;m=!0,h[1]=p,a[1][0]=f[0],a[1][1]=f[1],l[1]=xt(a[1],n)}while(!m);m=!1,_=.5,p=-1;let y=.01,x=1;do if(g=NaN,a[2]=We(o,Ue(d[0],d[2],_),s,e),isNaN(a[2][0])||isNaN(a[2][1])||(u=Ht(a[2],o,!1),g=u.distance),!isNaN(g)&&g>s&&Ne(a[2],o))m=!0,h[2]=g,l[2]=xt(a[2],n);else if(!isNaN(g)&&g>p)p=g,f[0]=a[2][0],f[1]=a[2][1];else if(g>p&&(p=g,f[0]=a[2][0],f[1]=a[2][1]),_=.5+y*x,y+=.01,x*=-1,_<.3||_>.7){if(!(p>=0))break;m=!0,h[2]=p,a[2][0]=f[0],a[2][1]=f[1],l[2]=xt(a[2],n)}while(!m);m=!1,_=.75,p=-1;do if(g=NaN,a[3]=We(o,Ue(d[0],d[2],_),s,e),isNaN(a[3][0])||isNaN(a[3][1])||(u=Ht(a[3],o,!1),g=u.distance),!isNaN(g)&&g>s&&Ne(a[3],o))m=!0,h[3]=g,l[3]=xt(a[3],n);else if(g>p&&(p=g,f[0]=a[3][0],f[1]=a[3][1]),_+=.01,_>.9){if(!(p>=0))break;m=!0,h[3]=p,a[3][0]=f[0],a[3][1]=f[1],l[3]=xt(a[3],n)}while(!m);const P=[0,1,2,3],M=c?0:1;let S;for(let C=M;C<4;C++)for(let L=M;L<3;L++){const O=l[L],X=l[L+1];dn(O,X)>0&&(S=P[L],P[L]=P[L+1],P[L+1]=S,l[L]=X,l[L+1]=O)}let w=M,I=0,v=0;for(let C=M;C<4;C++){switch(C){case 0:v=2*h[P[C]];break;case 1:v=1.66666666*h[P[C]];break;case 2:v=1.33333333*h[P[C]];break;case 3:v=h[P[C]]}v>I&&(I=v,w=P[C])}return a[w]}function Ne(o,t){const{rings:e}=t;let s=0;for(const i of e){const r=i.length;for(let n=1;n<r;++n){const a=i[n-1],h=i[n];a[1]>o[1]!=h[1]>o[1]&&((h[0]-a[0])*(o[1]-a[1])-(h[1]-a[1])*(o[0]-a[0])>0?s++:s--)}}return s!==0}function Ht(o,t,e){if(e&&Ne(o,t))return{coord:o,distance:0};let s=1/0,i=0,r=0;const n=[0,0],{rings:a}=t;for(const h of a)if(!(h.length<2))for(let l=0;l<h.length-1;l++){Zi(n,o,h,l);const c=xt(o,n);c<s&&(s=c,i=n[0],r=n[1])}return{coord:[i,r],distance:Math.sqrt(s)}}function We(o,t,e,s){const i=[t,0];let r=1/0,n=1/0,a=!1,h=!1;const l=[[t,s[1]-1],[t,s[3]+1]],c=[0,0],u=[0,0],f=[0,0],m=[[0,0],[0,0]],_=U(),{rings:p}=o;for(const d of p)if(!(d.length<2))for(let g=1;g<d.length;g++){if(m[0][0]=d[g-1][0],m[0][1]=d[g-1][1],m[1][0]=d[g][0],m[1][1]=d[g][1],pn(_,m)===null||(u[0]=l[0][0],u[1]=l[0][1],f[0]=l[1][0],f[1]=l[1][1],_n(_,u,f)===0)||!Qi(l[0],l[1],m[0],m[1],c))continue;const y=c[1];r>n?y<r&&(r=y,a=!0):y<n&&(n=y,h=!0)}return a&&h?i[1]=(r+n)/2:i[0]=i[1]=NaN,i}function pn(o,t){if(t.length<2)return null;o||(o=U());const[e,s]=t[0],[i,r]=t[1];return o[0]=Math.min(e,i),o[1]=Math.min(s,r),o[2]=Math.max(e,i),o[3]=Math.max(s,r),o}const Le=1,Te=4,Ts=3,$s=12;function _n(o,t,e){let s=st(t,o),i=st(e,o);const r=o[0],n=o[1],a=o[2],h=o[3];if(s&i)return 0;if(!(s|i))return 4;const l=(s?1:0)|(i?2:0);do{const c=e[0]-t[0],u=e[1]-t[1];if(c>u)s&Ts?(s&Le?(t[1]+=u*(r-t[0])/c,t[0]=r):(t[1]+=u*(a-t[0])/c,t[0]=a),s=st(t,o)):i&Ts?(i&Le?(e[1]+=u*(r-e[0])/c,e[0]=r):(e[1]+=u*(a-e[0])/c,e[0]=a),i=st(e,o)):s?(s&Te?(t[0]+=c*(n-t[1])/u,t[1]=n):(t[0]+=c*(h-t[1])/u,t[1]=h),s=st(t,o)):(i&Te?(e[0]+=c*(n-e[1])/u,e[1]=n):(e[0]+=c*(h-e[1])/u,e[1]=h),i=st(e,o));else if(s&$s?(s&Te?(t[0]+=c*(n-t[1])/u,t[1]=n):(t[0]+=c*(h-t[1])/u,t[1]=h),s=st(t,o)):i&$s?(i&Te?(e[0]+=c*(n-e[1])/u,e[1]=n):(e[0]+=c*(h-e[1])/u,e[1]=h),i=st(e,o)):s?(s&Le?(t[1]+=u*(r-t[0])/c,t[0]=r):(t[1]+=u*(a-t[0])/c,t[0]=a),s=st(t,o)):(i&Le?(e[1]+=u*(r-e[0])/c,e[0]=r):(e[1]+=u*(a-e[0])/c,e[0]=a),i=st(e,o)),s&i)return 0}while(s|i);return l}function st(o,t){return(o[0]<t[0]?1:0)|(o[0]>t[2]?1:0)<<1|(o[1]<t[1]?1:0)<<2|(o[1]>t[3]?1:0)<<3}function Ue(o,t,e){return o+(t-o)*e}function xt(o,t){return(o[0]-t[0])*(o[0]-t[0])+(o[1]-t[1])*(o[1]-t[1])}function dn(o,t){if(o<t)return-1;if(o>t)return 1;if(o===t)return 0;const e=isNaN(o),s=isNaN(t);return e<s?-1:e>s?1:0}let At=class{constructor(t,e,s,i){this.x=t,this.y=e,this.cellSize=s,this.distancefromCellCenter=er(t,e,i),this.maxDistanceToPolygon=this.distancefromCellCenter+this.cellSize*Math.SQRT2}};const gn=1,yn=100;function Pn(o){if(!o||!o.rings||o.rings.length===0)return null;const t=Ze(U(),o.rings[0]);if(!t)return null;const e=t[2]-t[0],s=t[3]-t[1];if(e===0||s===0)return[t[0]+e/2,t[1]+s/2];const i=Math.max(Math.min(e,s)/yn,gn),r=new cn((m,_)=>_.maxDistanceToPolygon-m.maxDistanceToPolygon),n=Math.min(e,s);let a=n/2,h=0,l=0;for(h=t[0];h<t[2];h+=n)for(l=t[1];l<t[3];l+=n)r.enqueue(new At(h+a,l+a,a,o));const c=tr(o.rings,!1);if(c===null)return null;let u,f=new At(c[0],c[1],0,o);for(;r.size>0;)u=dr(r.dequeue()),u.distancefromCellCenter>f.distancefromCellCenter&&(f=u),u.maxDistanceToPolygon-f.distancefromCellCenter<=i||(a=u.cellSize/2,r.enqueue(new At(u.x-a,u.y-a,a,o)),r.enqueue(new At(u.x+a,u.y-a,a,o)),r.enqueue(new At(u.x-a,u.y+a,a,o)),r.enqueue(new At(u.x+a,u.y+a,a,o)));return[f.x,f.y]}function xn(o){return o.rings!==void 0}let zi=class ce{static local(){return ce.instance===null&&(ce.instance=new ce),ce.instance}execute(t,e,s,i,r){return new Mn(t,e,s)}};zi.instance=null;let Mn=class{constructor(t,e,s){this._geometry=t,this._offsetX=e.offsetX!==void 0?e.offsetX*s:0,this._offsetY=e.offsetY!==void 0?e.offsetY*s:0,this._method=e.method!==void 0?e.method:we.OnPolygon,this._internalPlacement=new Ft}next(){const t=this._geometry;return this._geometry=null,t&&xn(t)?this._polygonCenter(t):null}_polygonCenter(t){let e=!1;switch(this._method){case we.CenterOfMass:{const s=sr(t);s&&(this._internalPlacement.setTranslate(s[0]+this._offsetX,s[1]+this._offsetY),e=!0)}break;case we.BoundingBoxCenter:{const s=U();Ot(s,t),s&&(this._internalPlacement.setTranslate((s[2]+s[0])/2+this._offsetX,(s[3]+s[1])/2+this._offsetY),e=!0)}break;case we.OnPolygon:default:{let s;s=gr("polylabel-placement-enabled")?Pn(t):mn(t),s!==null&&(this._internalPlacement.setTranslate(s[0]+this._offsetX,s[1]+this._offsetY),e=!0)}}return e?this._internalPlacement:null}};function rs(o){if(!o)return null;switch(o.type){case"CIMGeometricEffectAddControlPoints":return pi.local();case"CIMGeometricEffectArrow":return _i.local();case"CIMGeometricEffectBuffer":return di.local();case"CIMGeometricEffectControlMeasureLine":return gi.local();case"CIMGeometricEffectCut":return yi.local();case"CIMGeometricEffectDashes":return Pi.local();case"CIMGeometricEffectDonut":return xi.local();case"CIMGeometricEffectJog":return Mi.local();case"CIMGeometricEffectMove":return bi.local();case"CIMGeometricEffectOffset":return Si.local();case"CIMGeometricEffectReverse":return Ci.local();case"CIMGeometricEffectRotate":return wi.local();case"CIMGeometricEffectScale":return ki.local();case"CIMGeometricEffectWave":return vi.local()}return null}function bn(o){if(!o)return null;switch(o.type){case"CIMMarkerPlacementAlongLineSameSize":return Ii.local();case"CIMMarkerPlacementAtExtremities":return Li.local();case"CIMMarkerPlacementAtRatioPositions":return Ti.local();case"CIMMarkerPlacementInsidePolygon":return $i.local();case"CIMMarkerPlacementOnLine":return Ai.local();case"CIMMarkerPlacementOnVertices":return Ni.local();case"CIMMarkerPlacementPolygonCenter":return zi.local()}return null}function Je(o){const t=o.getFrame(0);if(t instanceof HTMLImageElement||t instanceof HTMLCanvasElement)return t;const e=document.createElement("canvas");e.width=o.width,e.height=o.height;const s=e.getContext("2d");return t instanceof ImageData?s.putImageData(t,0,0):s.drawImage(t,0,0),e}let Ei=class{constructor(t=0,e=0,s=0,i=0){this.x=t,this.y=e,this.width=s,this.height=i}get isEmpty(){return this.width<=0||this.height<=0}union(t){this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.width=Math.max(this.width,t.width),this.height=Math.max(this.height,t.height)}};function Sn(o){return`rgb(${o.slice(0,3).toString()})`}function Cn(o){return`rgba(${o.slice(0,3).toString()},${o[3]})`}let Oi=class{constructor(t){t&&(this._textRasterizationCanvas=t)}rasterizeText(t,e){this._textRasterizationCanvas||(this._textRasterizationCanvas=document.createElement("canvas"));const s=this._textRasterizationCanvas,i=s.getContext("2d");this._setFontProperties(i,e),this._parameters=e,this._textLines=t.split(/\r?\n/),this._lineHeight=this._computeLineHeight();const r=this._computeTextWidth(i,e),{decoration:n,weight:a}=e.font;this._lineThroughWidthOffset=n&&n==="line-through"?.1*this._lineHeight:0;const h=this._lineHeight*this._textLines.length;s.width=r+2*this._lineThroughWidthOffset,s.height=h,this._renderedLineHeight=Math.round(this._lineHeight*e.pixelRatio),this._renderedHaloSize=e.halo.size*e.pixelRatio,this._renderedWidth=r*e.pixelRatio,this._renderedHeight=h*e.pixelRatio,this._lineThroughWidthOffset*=e.pixelRatio;const l=e.color??[0,0,0,0],c=e.halo&&e.halo.color?e.halo.color:[0,0,0,0];this._fillStyle=Cn(l),this._haloStyle=Sn(c);const u=this._renderedLineHeight,f=this._renderedHaloSize;i.save(),i.clearRect(0,0,s.width,s.height),this._setFontProperties(i,e);const m=wn(i.textAlign,this._renderedWidth)+f,_=f,p=f>0;let d=this._lineThroughWidthOffset,g=0;p&&this._renderHalo(i,m,_,d,g,e),g+=_,d+=m;for(const I of this._textLines)p?(i.globalCompositeOperation="destination-out",i.fillStyle="rgb(0, 0, 0)",i.fillText(I,d,g),i.globalCompositeOperation="source-over",i.fillStyle=this._fillStyle,i.fillText(I,d,g)):(i.fillStyle=this._fillStyle,i.fillText(I,d,g)),n&&n!=="none"&&this._renderDecoration(i,d,g,n,a),g+=u;i.restore();const y=this._renderedWidth+2*this._lineThroughWidthOffset,x=this._renderedHeight,P=i.getImageData(0,0,y,x),M=new Uint8Array(P.data);if(e.premultiplyColors){let I;for(let v=0;v<M.length;v+=4)I=M[v+3]/255,M[v]=M[v]*I,M[v+1]=M[v+1]*I,M[v+2]=M[v+2]*I}let S,w;switch(e.horizontalAlignment){case"left":S=-.5;break;case"right":S=.5;break;default:S=0}switch(e.verticalAlignment){case"bottom":w=-.5;break;case"top":w=.5;break;default:w=0}return{size:[y,x],image:new Uint32Array(M.buffer),sdf:!1,simplePattern:!1,anchorX:S,anchorY:w,canvas:s}}_renderHalo(t,e,s,i,r,n){const a=this._renderedWidth,h=this._renderedHeight;this._haloRasterizationCanvas||(this._haloRasterizationCanvas=document.createElement("canvas")),this._haloRasterizationCanvas.width=a,this._haloRasterizationCanvas.height=h;const l=this._haloRasterizationCanvas,c=l.getContext("2d");c.clearRect(0,0,a,h),this._setFontProperties(c,n);const{decoration:u,weight:f}=n.font;c.fillStyle=this._haloStyle,c.strokeStyle=this._haloStyle,c.lineJoin="round",this._renderHaloNative(c,e,s,u,f),t.globalAlpha=this._parameters.halo.color[3],t.drawImage(l,0,0,a,h,i,r,a,h),t.globalAlpha=1}_renderHaloNative(t,e,s,i,r){const n=this._renderedLineHeight,a=this._renderedHaloSize;for(const h of this._textLines){const l=2*a,c=5,u=.1;for(let f=0;f<c;f++){const m=(1-(c-1)*u+f*u)*l;t.lineWidth=m,t.strokeText(h,e,s),i&&i!=="none"&&this._renderDecoration(t,e,s,i,r,m)}s+=n}}_setFontProperties(t,e){const s=Math.max(e.size,.5),i=e.font,r=`${i.style} ${i.weight} ${Ct(s*e.pixelRatio).toFixed(1)}px ${i.family}, sans-serif`;let n;switch(t.font=r,t.textBaseline="top",e.horizontalAlignment){case"left":default:n="left";break;case"right":n="right";break;case"center":n="center"}t.textAlign=n}computeTextSize(t,e){this._textRasterizationCanvas||(this._textRasterizationCanvas=document.createElement("canvas"));const s=this._textRasterizationCanvas,i=s.getContext("2d");this._setFontProperties(i,e),this._parameters=e,this._textLines=t.split(/\r?\n/),this._lineHeight=this._computeLineHeight();const r=this._computeTextWidth(i,e),n=this._lineHeight*this._textLines.length;return s.width=r,s.height=n,[r*e.pixelRatio,n*e.pixelRatio]}_computeTextWidth(t,e){let s=0;for(const r of this._textLines)s=Math.max(s,t.measureText(r).width);const i=e.font;return(i.style==="italic"||i.style==="oblique"||typeof i.weight=="string"&&(i.weight==="bold"||i.weight==="bolder")||typeof i.weight=="number"&&i.weight>600)&&(s+=.3*t.measureText("w").width),s+=2*this._parameters.halo.size,Math.round(s)}_computeLineHeight(){let t=1.275*this._parameters.size;const e=this._parameters.font.decoration;return e&&e==="underline"&&(t*=1.3),Math.round(t+2*this._parameters.halo.size)}_renderDecoration(t,e,s,i,r,n){const a=.9*this._lineHeight,h=r==="bold"?.06:r==="bolder"?.09:.04;switch(t.textAlign){case"center":e-=this._renderedWidth/2;break;case"right":e-=this._renderedWidth}const l=t.textBaseline;if(i==="underline")switch(l){case"top":s+=a;break;case"middle":s+=a/2}else if(i==="line-through")switch(l){case"top":s+=a/1.5;break;case"middle":s+=a/3}const c=n?1.5*n:Math.ceil(a*h);t.save(),t.beginPath(),t.strokeStyle=t.fillStyle,t.lineWidth=c,t.moveTo(e-this._lineThroughWidthOffset,s),t.lineTo(e+this._renderedWidth+2*this._lineThroughWidthOffset,s),t.stroke(),t.restore()}};function wn(o,t){return o==="center"?.5*t:o==="right"?t:0}let ue=class ns{constructor(t,e,s,i){this.center=ir(t,e),this.centerT=Qe(),this.halfWidth=s/2,this.halfHeight=i/2,this.width=s,this.height=i}get x(){return this.center[0]}get y(){return this.center[1]}get blX(){return this.center[0]+this.halfWidth}get blY(){return this.center[1]+this.halfHeight}get trX(){return this.center[0]-this.halfWidth}get trY(){return this.center[1]-this.halfHeight}get xmin(){return this.x-this.halfWidth}get xmax(){return this.x+this.halfWidth}get ymin(){return this.y-this.halfHeight}get ymax(){return this.y+this.halfHeight}set x(t){this.center[0]=t}set y(t){this.center[1]=t}clone(){return new ns(this.x,this.y,this.width,this.height)}serialize(t){return t.writeF32(this.center[0]),t.writeF32(this.center[1]),t.push(this.width),t.push(this.height),t}findCollisionDelta(t,e=4){const s=Math.abs(t.centerT[0]-this.centerT[0]),i=Math.abs(t.centerT[1]-this.centerT[1]),r=(t.halfWidth+this.halfWidth+e)/s,n=(t.halfHeight+this.halfHeight+e)/i,a=Math.min(r,n);return Math.log2(a)}extend(t){const e=Math.min(this.xmin,t.xmin),s=Math.min(this.ymin,t.ymin),i=Math.max(this.xmax,t.xmax)-e,r=Math.max(this.ymax,t.ymax)-s,n=e+i/2,a=s+r/2;this.width=i,this.height=r,this.halfWidth=i/2,this.halfHeight=r/2,this.x=n,this.y=a}static deserialize(t){const e=t.readF32(),s=t.readF32(),i=t.readInt32(),r=t.readInt32();return new ns(e,s,i,r)}};const ps=26,Ri=4,kn=ps+Ri,vn=ps-6,As=3,D=8,In=Math.PI/180,pt=8,Ln=1.5;let Fi=class{constructor(t,e,s,i){this._rotationT=fe(),this._xBounds=0,this._yBounds=0,this.minZoom=0,this.maxZoom=255,this._bounds=null;const r=s.rect,n=new Float32Array(8);t*=i,e*=i;const a=s.code?r.width*i:s.metrics.width,h=s.code?r.height*i:s.metrics.height;this.width=a,this.height=h,n[0]=t,n[1]=e,n[2]=t+a,n[3]=e,n[4]=t,n[5]=e+h,n[6]=t+a,n[7]=e+h,this._data=n,this._setTextureCoords(r),this._scale=i,this._mosaic=s,this.x=t,this.y=e,this.maxOffset=Math.max(t+a,e+h)}get mosaic(){return this._mosaic}set angle(t){this._angle=t,Bs(this._rotationT,-t),this._setOffsets(this._data)}get angle(){return this._angle}get xTopLeft(){return this._data[0]}get yTopLeft(){return this._data[1]}get xBottomRight(){return this._data[6]}get yBottomRight(){return this._data[7]}get texcoords(){return this._texcoords}get textureBinding(){return this._mosaic.textureBinding}get offsets(){return this._offsets||this._setOffsets(this._data),this._offsets}get char(){return String.fromCharCode(this._mosaic.code)}get code(){return this._mosaic.code}get bounds(){if(!this._bounds){const{height:t,width:e}=this._mosaic.metrics,s=e*this._scale,i=Math.abs(t)*this._scale,r=new Float32Array(8);r[0]=this.x,r[1]=this.y,r[2]=this.x+s,r[3]=this.y,r[4]=this.x,r[5]=this.y+i,r[6]=this.x+s,r[7]=this.y+i;const n=ts(fe(),this._rotationT,this._transform);xs(r,r,n);let a=1/0,h=1/0,l=0,c=0;for(let p=0;p<4;p++){const d=r[2*p],g=r[2*p+1];a=Math.min(a,d),h=Math.min(h,g),l=Math.max(l,d),c=Math.max(c,g)}const u=l-a,f=c-h,m=a+u/2,_=h+f/2;this._bounds=new ue(m,_,u,f)}return this._bounds}setTransform(t){this._transform=t,this._offsets=null}_setOffsets(t){this._offsets||(this._offsets={upperLeft:0,upperRight:0,lowerLeft:0,lowerRight:0});const e=this._offsets,s=new Float32Array(8),i=ts(fe(),this._rotationT,this._transform);xs(s,t,i),e.upperLeft=q(s[0]*D,s[1]*D),e.upperRight=q(s[2]*D,s[3]*D),e.lowerLeft=q(s[4]*D,s[5]*D),e.lowerRight=q(s[6]*D,s[7]*D)}_setTextureCoords({x:t,y:e,width:s,height:i}){this._texcoords={upperLeft:q(t,e),upperRight:q(t+s,e),lowerLeft:q(t,e+i),lowerRight:q(t+s,e+i)}}};const Tn=(o,t)=>({code:0,page:0,sdf:!0,rect:new vr(0,0,11,8),textureBinding:t,metrics:{advance:0,height:4,width:o,left:0,top:0}});function Dt(o,t){return o.forEach(e=>Ws(e,e,t)),{upperLeft:q(D*o[0][0],D*o[0][1]),upperRight:q(D*o[1][0],D*o[1][1]),lowerLeft:q(D*o[2][0],D*o[2][1]),lowerRight:q(D*o[3][0],D*o[3][1])}}let $n=class{constructor(t,e,s){this._rotation=0,this._decorate(t,e,s),this.glyphs=t,this.bounds=this._createBounds(t),this.isMultiline=e.length>1,this._hasRotation=s.angle!==0,this._transform=this._createGlyphTransform(this.bounds,s),this._borderLineSize=s.borderLineSize,(s.borderLineSize||s.hasBackground)&&([this.bounds,this.background]=this.shapeBackground(this._transform));for(const i of t)i.setTransform(this._transform)}setRotation(t){if(t===0&&this._rotation===0)return;this._rotation=t;const e=this._transform,s=Bs(fe(),t);ts(e,s,e);for(const i of this.glyphs)i.setTransform(this._transform)}_decorate(t,e,s){if(!s.decoration||s.decoration==="none"||!t.length)return;const i=s.scale,r=s.decoration==="underline"?kn:vn,n=t[0].textureBinding;for(const a of e){const h=a.startX*i,l=a.startY*i,c=(a.width+a.glyphWidthEnd)*i;t.push(new Fi(h,l+r*i,Tn(c,n),1))}}shapeBackground(t){const e=Ct(this._borderLineSize||0),s=(Ln+e)/2,i=this._borderLineSize?s:0,{xmin:r,ymin:n,xmax:a,ymax:h,x:l,y:c,width:u,height:f}=this.bounds,m=[r-pt,n-pt],_=[a+pt,n-pt],p=[r-pt,h+pt],d=[a+pt,h+pt],g=Dt([[m[0]-s,m[1]-s],[_[0]+s,_[1]-s],[m[0]+i,m[1]+i],[_[0]-i,_[1]+i]],t),y=Dt([[p[0]+i,p[1]-i],[d[0]-i,d[1]-i],[p[0]-s,p[1]+s],[d[0]+s,d[1]+s]],t),x=Dt([[m[0]-s,m[1]-s],[m[0]+i,m[1]+i],[p[0]-s,p[1]+s],[p[0]+i,p[1]-i]],t),P=Dt([[_[0]-i,_[1]+i],[_[0]+s,_[1]-s],[d[0]-i,d[1]-i],[d[0]+s,d[1]+s]],t),M={main:Dt([m,_,p,d],t),top:g,bot:y,left:x,right:P};return[new ue(l,c,u+2*s,f+2*s),M]}get boundsT(){const t=this.bounds,e=Me(Qe(),t.x,t.y);if(Ws(e,e,this._transform),this._hasRotation){const s=Math.max(t.width,t.height);return new ue(e[0],e[1],s,s)}return new ue(e[0],e[1],t.width,t.height)}_createBounds(t){let e=1/0,s=1/0,i=0,r=0;for(const h of t)e=Math.min(e,h.xTopLeft),s=Math.min(s,h.yTopLeft),i=Math.max(i,h.xBottomRight),r=Math.max(r,h.yBottomRight);const n=i-e,a=r-s;return new ue(e+n/2,s+a/2,n,a)}_createGlyphTransform(t,e){const s=In*e.angle,i=fe(),r=Qe();return Xe(i,i,Me(r,e.xOffset,-e.yOffset)),e.isCIM?Ms(i,i,s):(Xe(i,i,Me(r,t.x,t.y)),Ms(i,i,s),Xe(i,i,Me(r,-t.x,-t.y))),i}};class $e{constructor(t,e,s,i,r,n){this.glyphWidthEnd=0,this.startX=0,this.startY=0,this.start=Math.max(0,Math.min(e,s)),this.end=Math.max(0,Math.max(e,s)),this.end<t.length&&(this.glyphWidthEnd=t[this.end].metrics.width),this.width=i,this.yMin=r,this.yMax=n}}const os=o=>o===10,Ns=o=>o===32;function An(o,t,e){const s=new Array,i=1/e.scale,r=e.maxLineWidth*i,n=t?o.length-1:0,a=t?-1:o.length,h=t?-1:1;let l=n,c=0,u=0,f=l,m=f,_=0,p=1/0,d=0;for(;l!==a;){const{code:y,metrics:x}=o[l],P=Math.abs(x.top);if(os(y)||Ns(y)||(p=Math.min(p,P),d=Math.max(d,P+x.height)),os(y))l!==n&&(s.push(new $e(o,f,l-h,c,p,d)),p=1/0,d=0),c=0,f=l+h,m=l+h,u=0;else if(Ns(y))m=l+h,u=0,_=x.advance,c+=x.advance;else if(c>r){if(m!==f){const M=m-2*h;c-=_,s.push(new $e(o,f,M,c-u,p,d)),p=1/0,d=0,f=m,c=u}else s.push(new $e(o,f,l-h,c,p,d)),p=1/0,d=0,f=l,m=l,c=0;c+=x.advance,u+=x.advance}else c+=x.advance,u+=x.advance;l+=h}const g=new $e(o,f,l-h,c,p,d);return g.start>=0&&g.end<o.length&&s.push(g),s}function Nn(o,t){let e=0;for(let r=0;r<o.length;r++){const{width:n}=o[r];e=Math.max(n,e)}const s=t.decoration==="underline"?Ri:0,i=o[0].yMin;return{x:0,y:i,height:o[o.length-1].yMax+t.lineHeight*(o.length-1)+s-i,width:e}}function Gi(o,t,e){const s=e.scale,i=new Array,r=An(o,t,e),n=Nn(r,e),{vAlign:a,hAlign:h}=e,l=a===Yt.Baseline?1:0,c=l?0:a-1,u=(1-l)*-n.y+c*(n.height/2)+(l?1:0)*-ps;for(let f=0;f<r.length;f++){const{start:m,end:_,width:p}=r[f];let d=-1*(h+1)*(p/2)-As;const g=f*e.lineHeight+u-As;r[f].startX=d,r[f].startY=g;for(let y=m;y<=_;y++){const x=o[y];if(os(x.code))continue;const P=new Fi(d+x.metrics.left,g-x.metrics.top,x,s);d+=x.metrics.advance,i.push(P)}}return new $n(i,r,e)}const yt=Math.PI/180,zn=10,Xi=us.getLogger("esri.symbols.cim.CIMSymbolDrawHelper");class G{constructor(t){this._t=t}static createIdentity(){return new G([1,0,0,0,1,0])}clone(){const t=this._t;return new G(t.slice())}transform(t){const e=this._t;return[e[0]*t[0]+e[1]*t[1]+e[2],e[3]*t[0]+e[4]*t[1]+e[5]]}static createScale(t,e){return new G([t,0,0,0,e,0])}scale(t,e){const s=this._t;return s[0]*=t,s[1]*=t,s[2]*=t,s[3]*=e,s[4]*=e,s[5]*=e,this}scaleRatio(){return Math.sqrt(this._t[0]*this._t[0]+this._t[1]*this._t[1])}static createTranslate(t,e){return new G([0,0,t,0,0,e])}translate(t,e){const s=this._t;return s[2]+=t,s[5]+=e,this}static createRotate(t){const e=Math.cos(t),s=Math.sin(t);return new G([e,-s,0,s,e,0])}rotate(t){return G.multiply(this,G.createRotate(t),this)}angle(){const t=this._t[0],e=this._t[3],s=Math.sqrt(t*t+e*e);return[t/s,e/s]}static multiply(t,e,s){const i=t._t,r=e._t,n=i[0]*r[0]+i[3]*r[1],a=i[1]*r[0]+i[4]*r[1],h=i[2]*r[0]+i[5]*r[1]+r[2],l=i[0]*r[3]+i[3]*r[4],c=i[1]*r[3]+i[4]*r[4],u=i[2]*r[3]+i[5]*r[4]+r[5],f=s._t;return f[0]=n,f[1]=a,f[2]=h,f[3]=l,f[4]=c,f[5]=u,s}invert(){const t=this._t;let e=t[0]*t[4]-t[1]*t[3];if(e===0)return new G([0,0,0,0,0,0]);e=1/e;const s=(t[1]*t[5]-t[2]*t[4])*e,i=(t[2]*t[3]-t[0]*t[5])*e,r=t[4]*e,n=-t[1]*e,a=-t[3]*e,h=t[0]*e;return new G([r,n,s,a,h,i])}}let _s=class{constructor(t,e){this._resourceManager=t,this._transfos=[],this._sizeTransfos=[],this._geomUnitsPerPoint=1,this._placementPool=new yr(Ft,void 0,void 0,100),this._earlyReturn=!1,this._mapRotation=0,this._transfos.push(e||G.createIdentity()),this._sizeTransfos.push(e?e.scaleRatio():1)}setTransform(t,e){this._transfos=[t||G.createIdentity()],this._sizeTransfos=[e||(t?t.scaleRatio():1)]}setGeomUnitsPerPoint(t){this._geomUnitsPerPoint=t}transformPt(t){return this._transfos[this._transfos.length-1].transform(t)}transformSize(t){return t*this._sizeTransfos[this._sizeTransfos.length-1]}reverseTransformPt(t){return this._transfos[this._transfos.length-1].invert().transform(t)}reverseTransformSize(t){return t/this._sizeTransfos[this._sizeTransfos.length-1]}getTransformAngle(){return this._transfos[this._transfos.length-1].angle()}geomUnitsPerPoint(){return this.isEmbedded()?1:this._geomUnitsPerPoint}isEmbedded(){return this._transfos.length>1}back(){return this._transfos[this._transfos.length-1]}push(t,e){const s=e?t.scaleRatio():1;G.multiply(t,this.back(),t),this._transfos.push(t),this._sizeTransfos.push(this._sizeTransfos[this._sizeTransfos.length-1]*s)}pop(){this._transfos.splice(-1,1),this._sizeTransfos.splice(-1,1)}drawSymbol(t,e,s){if(t)switch(t.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":this.drawMultiLayerSymbol(t,e);break;case"CIMTextSymbol":this.drawTextSymbol(t,e,s)}}drawMultiLayerSymbol(t,e){if(!t||!e)return;const s=t.symbolLayers;if(!s)return;const i=t.effects;if(i&&i.length>0){const r=this.executeEffects(i,e);if(r){let n=r.next();for(;n;)this.drawSymbolLayers(s,n),n=r.next()}}else this.drawSymbolLayers(s,e)}executeEffects(t,e){const s=this._resourceManager.geometryEngine;let i=new es(e);for(const r of t){const n=rs(r);n&&(i=n.execute(i,r,this.geomUnitsPerPoint(),null,s))}return i}drawSymbolLayers(t,e){let s=t.length;for(;s--;){const i=t[s];if(!i||i.enable===!1)continue;const r=i.effects;if(r&&r.length>0){const n=this.executeEffects(r,e);if(n){let a=null;for(;(a=n.next())&&(this.drawSymbolLayer(i,a),!this._earlyReturn););}}else this.drawSymbolLayer(i,e);if(this._earlyReturn)return}}drawSymbolLayer(t,e){switch(t.type){case"CIMSolidFill":this.drawSolidFill(e,t.color);break;case"CIMHatchFill":this.drawHatchFill(e,t);break;case"CIMPictureFill":this.drawPictureFill(e,t);break;case"CIMGradientFill":this.drawGradientFill(e,t);break;case"CIMSolidStroke":this.drawSolidStroke(e,t.color,t.width,t.capStyle,t.joinStyle,t.miterLimit);break;case"CIMPictureStroke":this.drawPictureStroke(e,t);break;case"CIMGradientStroke":this.drawGradientStroke(e,t);break;case"CIMCharacterMarker":case"CIMPictureMarker":case"CIMVectorMarker":this.drawMarkerLayer(t,e)}}drawHatchFill(t,e){const s=this._buildHatchPolyline(e,t,this.geomUnitsPerPoint());s&&(this.pushClipPath(t),this.drawMultiLayerSymbol(e.lineSymbol,s),this.popClipPath())}drawPictureFill(t,e){}drawGradientFill(t,e){}drawPictureStroke(t,e){}drawGradientStroke(t,e){}drawMarkerLayer(t,e){const s=t.markerPlacement;if(s){const i=bn(s);if(i){const r=s.type==="CIMMarkerPlacementInsidePolygon"||s.type==="CIMMarkerPlacementPolygonCenter"&&s.clipAtBoundary;r&&this.pushClipPath(e);const n=i.execute(e,s,this.geomUnitsPerPoint(),null,this._resourceManager.geometryEngine);if(n){let a=null;for(;(a=n.next())&&(this.drawMarker(t,a),!this._earlyReturn););}r&&this.popClipPath()}}else{const i=this._placementPool.acquire();if(St(e))i.tx=e.x,i.ty=e.y,this.drawMarker(t,i);else if($(e)){const r=ar(e);r&&([i.tx,i.ty]=r,this.drawMarker(t,i))}else for(const r of e.points)if(i.tx=r[0],i.ty=r[1],this.drawMarker(t,i),this._earlyReturn)break;this._placementPool.release(i)}}drawMarker(t,e){switch(t.type){case"CIMCharacterMarker":case"CIMPictureMarker":this.drawPictureMarker(t,e);break;case"CIMVectorMarker":this.drawVectorMarker(t,e)}}drawPictureMarker(t,e){if(!t)return;const s=this._resourceManager.getResource(t.url),i=t.size??10;if(Z(s)||i<=0)return;const r=s.width,n=s.height;if(!r||!n)return;const a=r/n,h=t.scaleX??1,l=G.createIdentity(),c=t.anchorPoint;if(c){let d=c.x,g=c.y;t.anchorPointUnits!=="Absolute"&&(d*=i*a*h,g*=i),l.translate(-d,-g)}let u=t.rotation??0;t.rotateClockwise&&(u=-u),this._mapRotation&&(u+=this._mapRotation),u&&l.rotate(u*yt);let f=t.offsetX??0,m=t.offsetY??0;if(f||m){if(this._mapRotation){const d=yt*this._mapRotation,g=Math.cos(d),y=Math.sin(d),x=f*y+m*g;f=f*g-m*y,m=x}l.translate(f,m)}const _=this.geomUnitsPerPoint();_!==1&&l.scale(_,_);const p=e.getAngle();p&&l.rotate(p),l.translate(e.tx,e.ty),this.push(l,!1),this.drawImage(t,i),this.pop()}drawVectorMarker(t,e){if(!t)return;const s=t.markerGraphics;if(!s)return;const i=t.size??10,r=t.frame,n=r?r.ymax-r.ymin:0,a=i&&n?i/n:1,h=G.createIdentity();r&&h.translate(.5*-(r.xmax+r.xmin),.5*-(r.ymax+r.ymin));const l=t.anchorPoint;if(l){let p=l.x,d=l.y;t.anchorPointUnits!=="Absolute"?r&&(p*=r.xmax-r.xmin,d*=r.ymax-r.ymin):(p/=a,d/=a),h.translate(-p,-d)}a!==1&&h.scale(a,a);let c=t.rotation??0;t.rotateClockwise&&(c=-c),this._mapRotation&&(c+=this._mapRotation),c&&h.rotate(c*yt);let u=t.offsetX??0,f=t.offsetY??0;if(u||f){if(this._mapRotation){const p=yt*this._mapRotation,d=Math.cos(p),g=Math.sin(p),y=u*g+f*d;u=u*d-f*g,f=y}h.translate(u,f)}const m=this.geomUnitsPerPoint();m!==1&&h.scale(m,m);const _=e.getAngle();_&&h.rotate(_),h.translate(e.tx,e.ty),this.push(h,t.scaleSymbolsProportionally);for(const p of s)if(p&&p.symbol&&p.geometry||Xi.error("Invalid marker graphic",p),this.drawSymbol(p.symbol,p.geometry,p.textString),this._earlyReturn)break;this.pop()}drawTextSymbol(t,e,s){if(!t||!St(e)||(t.height??10)<=0)return;const i=G.createIdentity();let r=t.angle??0;r=-r,r&&i.rotate(r*yt);const n=t.offsetX??0,a=t.offsetY??0;(n||a)&&i.translate(n,a);const h=this.geomUnitsPerPoint();h!==1&&i.scale(h,h),i.translate(e.x,e.y),this.push(i,!1),this.drawText(t,s),this.pop()}_buildHatchPolyline(t,e,s){let i=(t.separation!==void 0?t.separation:4)*s,r=t.rotation!==void 0?t.rotation:0;if(i===0)return null;i<0&&(i=-i);let n=0;const a=.5*i;for(;n>a;)n-=i;for(;n<-a;)n+=i;const h=U();Ot(h,e),h[0]-=a,h[1]-=a,h[2]+=a,h[3]+=a;const l=[[h[0],h[1]],[h[0],h[3]],[h[2],h[3]],[h[2],h[1]]];for(;r>180;)r-=180;for(;r<0;)r+=180;const c=Math.cos(r*yt),u=Math.sin(r*yt),f=-i*u,m=i*c;let _,p,d,g;n=(t.offsetX!==void 0?t.offsetX*s:0)*u-(t.offsetY!==void 0?t.offsetY*s:0)*c,_=d=Number.MAX_VALUE,p=g=-Number.MAX_VALUE;for(const I of l){const v=I[0],C=I[1],L=c*v+u*C,O=-u*v+c*C;_=Math.min(_,L),d=Math.min(d,O),p=Math.max(p,L),g=Math.max(g,O)}d=Math.floor(d/i)*i;let y=c*_-u*d-f*n/i,x=u*_+c*d-m*n/i,P=c*p-u*d-f*n/i,M=u*p+c*d-m*n/i;const S=1+Math.round((g-d)/i),w=[];for(let I=0;I<S;I++)y+=f,x+=m,P+=f,M+=m,w.push([[y,x],[P,M]]);return{paths:w}}},En=class extends _s{constructor(t,e){super(t,e),this.reset()}reset(){this._xmin=this._ymin=1/0,this._xmax=this._ymax=-1/0,this._clipCount=0}envelope(){return new Ei(this._xmin,this._ymin,this._xmax-this._xmin,this._ymax-this._ymin)}bounds(){return rr(this._xmin,this._ymin,this._xmax,this._ymax)}drawSolidFill(t){if(t&&!(this._clipCount>0))if($(t))this._processPath(t.rings,0);else if(A(t))this._processPath(t.paths,0);else if(R(t)){const e=Mt(t);e&&this._processPath(e.rings,0)}else console.error("drawSolidFill Unexpected geometry type!")}drawSolidStroke(t,e,s){if(!t||this._clipCount>0)return;const i=.5*this.transformSize(s??0);if($(t))this._processPath(t.rings,i);else if(A(t))this._processPath(t.paths,i);else if(R(t)){const r=Mt(t);r&&this._processPath(r.rings,i)}else console.error("drawSolidStroke unexpected geometry type!")}drawMarkerLayer(t,e){$(e)&&t.markerPlacement&&(t.markerPlacement.type==="CIMMarkerPlacementInsidePolygon"||t.markerPlacement.type==="CIMMarkerPlacementPolygonCenter"&&t.markerPlacement.clipAtBoundary)?this._processPath(e.rings,0):super.drawMarkerLayer(t,e)}drawHatchFill(t,e){this.drawSolidFill(t)}drawPictureFill(t,e){this.drawSolidFill(t)}drawGradientFill(t,e){this.drawSolidFill(t)}drawPictureStroke(t,e){this.drawSolidStroke(t,null,e.width)}drawGradientStroke(t,e){this.drawSolidStroke(t,null,e.width)}pushClipPath(t){this.drawSolidFill(t),this._clipCount++}popClipPath(){this._clipCount--}drawImage(t,e){const{url:s}=t,i=t.scaleX??1;let r=i*e,n=e;const a=this._resourceManager.getResource(s);!e&&V(a)&&(r=i*a.width,n=a.height),this._merge(this.transformPt([-r/2,-n/2]),0),this._merge(this.transformPt([-r/2,n/2]),0),this._merge(this.transformPt([r/2,-n/2]),0),this._merge(this.transformPt([r/2,n/2]),0)}drawText(t,e){if(!e||e.length===0)return;this._textRasterizer||(this._textRasterizer=new Oi);const s=Vi(t),[i,r]=this._textRasterizer.computeTextSize(e,s);let n=0;switch(t.horizontalAlignment){case"Left":n=i/2;break;case"Right":n=-i/2}let a=0;switch(t.verticalAlignment){case"Bottom":a=r/2;break;case"Top":a=-r/2;break;case"Baseline":a=r/6}this._merge(this.transformPt([-i/2+n,-r/2+a]),0),this._merge(this.transformPt([-i/2+n,r/2+a]),0),this._merge(this.transformPt([i/2+n,-r/2+a]),0),this._merge(this.transformPt([i/2+n,r/2+a]),0)}_processPath(t,e){if(t)for(const s of t){const i=s?s.length:0;if(i>1){this._merge(this.transformPt(s[0]),e);for(let r=1;r<i;r++)this._merge(this.transformPt(s[r]),e)}}}_merge(t,e){t[0]-e<this._xmin&&(this._xmin=t[0]-e),t[0]+e>this._xmax&&(this._xmax=t[0]+e),t[1]-e<this._ymin&&(this._ymin=t[1]-e),t[1]+e>this._ymax&&(this._ymax=t[1]+e)}},Ta=class extends _s{constructor(){super(...arguments),this._searchPoint=[0,0],this._searchDistPoint=0,this._textInfo=null}hitTest(t,e,s,i,r,n){const a=n*Ct(1);this.setTransform(),this.setGeomUnitsPerPoint(a),this._searchPoint=[(t[0]+t[2])/2,(t[1]+t[3])/2],this._searchDistPoint=(t[2]-t[0])/2/a,this._textInfo=i;const h=e&&(e.type==="CIMPointSymbol"&&e.angleAlignment!=="Map"||e.type==="CIMTextSymbol");return this._mapRotation=h?r:0,this._earlyReturn=!1,this.drawSymbol(e,s),this._earlyReturn}drawSolidFill(t,e){this._hitTestFill(t)}drawHatchFill(t,e){this._hitTestFill(t)}drawPictureFill(t,e){this._hitTestFill(t)}drawGradientFill(t,e){this._hitTestFill(t)}drawSolidStroke(t,e,s,i,r,n){this._hitTestStroke(t,s)}drawPictureStroke(t,e){this._hitTestStroke(t,e.width)}drawGradientStroke(t,e){this._hitTestStroke(t,e.width)}drawMarkerLayer(t,e){t.markerPlacement&&(t.markerPlacement.type==="CIMMarkerPlacementInsidePolygon"||t.markerPlacement.type==="CIMMarkerPlacementPolygonCenter"&&t.markerPlacement.clipAtBoundary)?this._hitTestFill(e):super.drawMarkerLayer(t,e)}pushClipPath(t){}popClipPath(){}drawImage(t,e){const{url:s}=t,i=t.scaleX??1,r=this._resourceManager.getResource(s);if(Z(r)||r.height===0||e===0)return;const n=e*this.geomUnitsPerPoint(),a=n*i*(r.width/r.height),h=this.reverseTransformPt(this._searchPoint),l=this._searchDistPoint;Math.abs(h[0])<a/2+l&&Math.abs(h[1])<n/2+l&&(this._earlyReturn=!0)}drawText(t,e){var y,x;const s=this._textInfo;if(!s)return;const i=s.get(t);if(!i)return;const{text:r,mosaicItem:n}=i;if(!((y=n==null?void 0:n.glyphMosaicItems)!=null&&y.length))return;const a=t.height??zn,{lineGapType:h,lineGap:l}=t,c=h?Yi(h,l??0,a):0,u=fs(r)[1],f=n.glyphMosaicItems,m=((x=t.callout)==null?void 0:x.type)==="CIMBackgroundCallout",_=Gi(f,u,{scale:a/ri,angle:0,xOffset:0,yOffset:0,hAlign:Hi(t.horizontalAlignment),vAlign:Di(t.verticalAlignment),maxLineWidth:512,lineHeight:ni*Math.max(.25,Math.min(c||1,4)),decoration:t.font.decoration||"none",isCIM:!0,hasBackground:m}),p=this.reverseTransformPt(this._searchPoint),d=p[0],g=p[1];for(const P of _.glyphs)if(d>P.xTopLeft&&d<P.xBottomRight&&g>-P.yBottomRight&&g<-P.yTopLeft){this._earlyReturn=!0;break}}_hitTestFill(t){let e=null;if(R(t)){const i=t;e=[[[i.xmin,i.ymin],[i.xmin,i.ymax],[i.xmax,i.ymax],[i.xmax,i.ymin],[i.xmin,i.ymin]]]}else if($(t))e=t.rings;else{if(!A(t))return;e=t.paths}const s=this.reverseTransformPt(this._searchPoint);if(this._pointInPolygon(s,e)&&(this._earlyReturn=!0),!this._earlyReturn){const i=this.reverseTransformSize(this._searchDistPoint)*this.geomUnitsPerPoint();this._nearLine(s,e,i)&&(this._earlyReturn=!0)}}_hitTestStroke(t,e){let s=null;if(R(t)){const a=t;s=[[[a.xmin,a.ymin],[a.xmin,a.ymax],[a.xmax,a.ymax],[a.xmax,a.ymin],[a.xmin,a.ymin]]]}else if($(t))s=t.rings;else{if(!A(t))return;s=t.paths}const i=this.reverseTransformPt(this._searchPoint),r=(e??0)*this.geomUnitsPerPoint(),n=this.reverseTransformSize(this._searchDistPoint)*this.geomUnitsPerPoint();this._nearLine(i,s,r/2+n)&&(this._earlyReturn=!0)}_pointInPolygon(t,e){let s=0;for(const i of e){const r=i.length;for(let n=1;n<r;n++){const a=i[n-1],h=i[n];a[1]>t[1]!=h[1]>t[1]&&((h[0]-a[0])*(t[1]-a[1])-(h[1]-a[1])*(t[0]-a[0])>0?s++:s--)}}return s!==0}_nearLine(t,e,s){for(const i of e){const r=i.length;for(let n=1;n<r;n++){const a=i[n-1],h=i[n];let l=(h[0]-a[0])*(h[0]-a[0])+(h[1]-a[1])*(h[1]-a[1]);if(l===0)continue;l=Math.sqrt(l);const c=((h[0]-a[0])*(t[1]-a[1])-(h[1]-a[1])*(t[0]-a[0]))/l;if(Math.abs(c)<s){const u=((h[0]-a[0])*(t[0]-a[0])+(h[1]-a[1])*(t[1]-a[1]))/l;if(u>-s&&u<l+s)return!0}}}return!1}},On=class extends _s{constructor(t,e,s,i){super(e,s),this._applyAdditionalRenderProps=i,this._colorSubstitutionHelper=new Lr,this._ctx=t}drawSolidFill(t,e){if(!t)return;if($(t))this._buildPath(t.rings,!0);else if(A(t))this._buildPath(t.paths,!0);else if(R(t))this._buildPath(Mt(t).rings,!0);else{if(!rt(t))return;console.log("CanvasDrawHelper.drawSolidFill - No implementation!")}const s=this._ctx;s.fillStyle=typeof e=="string"?e:"rgba("+Math.round(e[0])+","+Math.round(e[1])+","+Math.round(e[2])+","+(e[3]??255)/255+")",s.fill("evenodd")}drawSolidStroke(t,e,s,i,r,n){if(!t||!e||s===0)return;if($(t))this._buildPath(t.rings,!0);else if(A(t))this._buildPath(t.paths,!1);else{if(!R(t))return void console.log("CanvasDrawHelper.drawSolidStroke isn't implemented!");this._buildPath(Mt(t).rings,!0)}const a=this._ctx;a.strokeStyle=typeof e=="string"?e:"rgba("+Math.round(e[0])+","+Math.round(e[1])+","+Math.round(e[2])+","+(e[3]??255)/255+")",a.lineWidth=Math.max(this.transformSize(s),.5),this._setCapStyle(i),this._setJoinStyle(r),a.miterLimit=n,a.stroke()}pushClipPath(t){if(this._ctx.save(),$(t))this._buildPath(t.rings,!0);else if(A(t))this._buildPath(t.paths,!0);else{if(!R(t))return;this._buildPath(Mt(t).rings,!0)}this._ctx.clip("evenodd")}popClipPath(){this._ctx.restore()}drawImage(t,e){const{colorSubstitutions:s,url:i,tintColor:r}=t,n=t.scaleX??1,a=this._resourceManager.getResource(i);if(Z(a))return;let h=e*(a.width/a.height),l=e;e||(h=a.width,l=a.height);const c=Tt(i)||"src"in a&&Tt(a.src);let u="getFrame"in a?Je(a):a;s&&(u=this._colorSubstitutionHelper.applyColorSubstituition(u,s)),this._applyAdditionalRenderProps&&!c&&r&&(u=this._colorSubstitutionHelper.tintImageData(u,r));const f=this.transformPt([0,0]),[m,_]=this.getTransformAngle(),p=this.transformSize(1),d=this._ctx;d.save(),d.setTransform({m11:n*p*m,m12:n*p*_,m21:-p*_,m22:p*m,m41:f[0],m42:f[1]}),d.drawImage(u,-h/2,-l/2,h,l),d.restore()}drawText(t,e){if(!e||e.length===0)return;this._textRasterizer||(this._textRasterizer=new Oi);const s=Vi(t);s.size*=this.transformSize(Nt(1));const i=this._textRasterizer.rasterizeText(e,s);if(!i)return;const{size:r,anchorX:n,anchorY:a,canvas:h}=i,l=r[0]*(n+.5),c=r[1]*(a-.5),u=this._ctx,f=this.transformPt([0,0]),[m,_]=this.getTransformAngle(),p=1;u.save(),u.setTransform({m11:p*m,m12:p*_,m21:-p*_,m22:p*m,m41:f[0]-p*l,m42:f[1]+p*c}),u.drawImage(h,0,0),u.restore()}drawPictureFill(t,e){if(!t)return;let{colorSubstitutions:s,height:i,offsetX:r,offsetY:n,rotation:a,scaleX:h,tintColor:l,url:c}=e;const u=this._resourceManager.getResource(c);if(Z(u))return;if($(t))this._buildPath(t.rings,!0);else if(A(t))this._buildPath(t.paths,!0);else if(R(t))this._buildPath(Mt(t).rings,!0);else{if(!rt(t))return;console.log("CanvasDrawHelper.drawPictureFill - No implementation!")}const f=this._ctx,m=Tt(c)||"src"in u&&Tt(u.src);let _,p="getFrame"in u?Je(u):u;if(s&&(p=this._colorSubstitutionHelper.applyColorSubstituition(p,s)),this._applyAdditionalRenderProps){m||l&&(p=this._colorSubstitutionHelper.tintImageData(p,l)),_=f.createPattern(p,"repeat");const d=this.transformSize(1);a||(a=0),r?r*=d:r=0,n?n*=d:n=0,i&&(i*=d);const g=i?i/u.height:1,y=h&&i?h*i/u.width:1;if(a!==0||g!==1||y!==1||r!==0||n!==0){const x=new DOMMatrix;x.rotateSelf(0,0,-a).translateSelf(r,n).scaleSelf(y,g,1),_.setTransform(x)}}else _=f.createPattern(p,"repeat");f.save(),f.fillStyle=_,f.fill("evenodd"),f.restore()}drawPictureStroke(t,e){if(!t)return;let{colorSubstitutions:s,capStyle:i,joinStyle:r,miterLimit:n,tintColor:a,url:h,width:l}=e;const c=this._resourceManager.getResource(h);if(Z(c))return;let u;if($(t))u=t.rings;else if(A(t))u=t.paths;else{if(!R(t))return rt(t)?void console.log("CanvasDrawHelper.drawPictureStroke - No implementation!"):void 0;u=Mt(t).rings}l||(l=c.width);const f=Tt(h)||"src"in c&&Tt(c.src);let m="getFrame"in c?Je(c):c;s&&(m=this._colorSubstitutionHelper.applyColorSubstituition(m,s)),this._applyAdditionalRenderProps&&(f||a&&(m=this._colorSubstitutionHelper.tintImageData(m,a)));const _=Math.max(this.transformSize(Ct(l)),.5),p=_/m.width,d=this._ctx,g=d.createPattern(m,"repeat-y");let y,x;d.save(),this._setCapStyle(i),this._setJoinStyle(r),n!==void 0&&(d.miterLimit=n),d.lineWidth=_;for(let P of u)if(P=N(P),Gn(P),P&&!(P.length<=1)){y=this.transformPt(P[0]);for(let M=1;M<P.length;M++){x=this.transformPt(P[M]);const S=Rn(y,x),w=new DOMMatrix;w.translateSelf(0,y[1]-_/2).scaleSelf(p,p,1).rotateSelf(0,0,90-S),g.setTransform(w),d.strokeStyle=g,d.beginPath(),d.moveTo(y[0],y[1]),d.lineTo(x[0],x[1]),d.stroke(),y=x}}d.restore()}_buildPath(t,e){const s=this._ctx;if(s.beginPath(),t)for(const i of t){const r=i?i.length:0;if(r>1){let n=this.transformPt(i[0]);s.moveTo(n[0],n[1]);for(let a=1;a<r;a++)n=this.transformPt(i[a]),s.lineTo(n[0],n[1]);e&&s.closePath()}}}_setCapStyle(t){switch(t){case ht.Butt:this._ctx.lineCap="butt";break;case ht.Round:this._ctx.lineCap="round";break;case ht.Square:this._ctx.lineCap="square"}}_setJoinStyle(t){switch(t){case lt.Bevel:this._ctx.lineJoin="bevel";break;case lt.Round:this._ctx.lineJoin="round";break;case lt.Miter:this._ctx.lineJoin="miter"}}};function Rn(o,t){const e=t[0]-o[0],s=t[1]-o[1];return 180/Math.PI*Math.atan2(s,e)}const Mt=o=>o?{spatialReference:o.spatialReference,rings:[[[o.xmin,o.ymin],[o.xmin,o.ymax],[o.xmax,o.ymax],[o.xmax,o.ymin],[o.xmin,o.ymin]]]}:null,Hi=o=>{switch(o){case"Left":return ke.Left;case"Right":return ke.Right;case"Center":return ke.Center;case"Justify":return Xi.warnOnce("Horizontal alignment 'justify' is not implemented. Falling back to 'center'."),ke.Center}},Di=o=>{switch(o){case"Top":return Yt.Top;case"Center":return Yt.Center;case"Bottom":return Yt.Bottom;case"Baseline":return Yt.Baseline}},Yi=(o,t,e)=>{switch(o){case"ExtraLeading":return 1+t/e;case"Multiple":return t;case"Exact":return t/e}};function Vi(o,t=1){const e=Us(o),s=Js(o.fontStyleName),i=ti(o.fontFamilyName),{weight:r,style:n}=s,a=t*(o.height||5),h=qs(o.horizontalAlignment),l=js(o.verticalAlignment),c=pe(o),u=_e(o.haloSymbol),f=u?t*(0|o.haloSize):0;return{color:c,size:a,horizontalAlignment:h,verticalAlignment:l,font:{family:i,style:nr(n),weight:or(r),decoration:e},halo:{size:f||0,color:u,style:n},pixelRatio:1,premultiplyColors:!0}}const Fn=1e-4;function Gn(o){let t,e,s,i,r,n=o[0],a=1;for(;a<o.length;)t=o[a][0]-n[0],e=o[a][1]-n[1],i=t!==0?e/t:Math.PI/2,s!==void 0&&i-s<=Fn?(o.splice(a-1,1),n=r):(r=n,n=o[a],a++),s=i}const Bi=Math.PI,Xn=Bi/2,Hn=4,zs=4,Dn=10,it=96/72,Es=Math.PI/180,as=us.getLogger("esri.symbols.cim.CIMSymbolHelper");function Na(o){if(!o||!o.type)return null;let t;switch(o.type){case"cim":return o.data;case"web-style":return o;case"simple-marker":{const e=at.fromSimpleMarker(o);if(!e)return null;t=e;break}case"picture-marker":t=at.fromPictureMarker(o);break;case"simple-line":t=at.fromSimpleLineSymbol(o);break;case"simple-fill":t=at.fromSimpleFillSymbol(o);break;case"picture-fill":t=at.fromPictureFillSymbol(o);break;case"text":t=at.fromTextSymbol(o)}return{type:"CIMSymbolReference",symbol:t}}function ze(o,t,e){switch(t.type){case"CIMSymbolReference":return ze(o,t.symbol,e);case"CIMPointSymbol":e==null&&(e={x:0,y:0}),o.drawSymbol(t,e);break;case"CIMLineSymbol":e==null&&(e={paths:[[[0,0],[10,0]]]}),o.drawSymbol(t,e);break;case"CIMPolygonSymbol":e==null&&(e={rings:[[[0,0],[0,10],[10,10],[10,0],[0,0]]]}),o.drawSymbol(t,e);break;case"CIMTextSymbol":{const s={x:0,y:0};o.drawSymbol(t,s);break}case"CIMVectorMarker":{const s=new Ft;o.drawMarker(t,s);break}}return o.envelope()}function Yn(o){if(!o)return 0;switch(o.type){case"CIMMarkerPlacementAlongLineSameSize":case"CIMMarkerPlacementAlongLineRandomSize":case"CIMMarkerPlacementAtExtremities":case"CIMMarkerPlacementAtMeasuredUnits":case"CIMMarkerPlacementAtRatioPositions":case"CIMMarkerPlacementOnLine":case"CIMMarkerPlacementOnVertices":return Math.abs(o.offset);default:return 0}}function Vn(o){if(!o)return 0;switch(o.type){case"CIMGeometricEffectArrow":return Math.abs(.5*o.width);case"CIMGeometricEffectBuffer":return Math.abs(o.size);case"CIMGeometricEffectExtension":case"CIMGeometricEffectRadial":return Math.abs(o.length);case"CIMGeometricEffectJog":return Math.abs(.5*o.length);case"CIMGeometricEffectMove":return Math.max(Math.abs(k(o.offsetX)),Math.abs(k(o.offsetY)));case"CIMGeometricEffectOffset":case"CIMGeometricEffectOffsetTangent":return Math.abs(o.offset);case"CIMGeometricEffectRegularPolygon":return Math.abs(o.radius);case"CIMGeometricEffectRotate":case"CIMGeometricEffectScale":default:return 0;case"CIMGeometricEffectTaperedPolygon":return .5*Math.max(Math.abs(o.fromWidth),Math.abs(o.toWidth));case"CIMGeometricEffectWave":return Math.abs(o.amplitude)}}function Os(o){if(!o)return 0;let t=0;for(const e of o)t+=Vn(e);return t}let za=class{getSymbolInflateSize(t,e,s,i,r){return t||(t=[0,0,0,0]),e?this._getInflateSize(t,e,s,i,r):t}static safeSize(t){const e=Math.max(Math.abs(t[0]),Math.abs(t[2])),s=Math.max(Math.abs(t[1]),Math.abs(t[3]));return Math.sqrt(e*e+s*s)}_vectorMarkerBounds(t,e,s,i){let r=!0;const n=U();if(e&&e.markerGraphics)for(const a of e.markerGraphics){const h=[0,0,0,0];a.geometry&&(Ot(n,a.geometry),h[0]=0,h[1]=0,h[2]=0,h[3]=0,this.getSymbolInflateSize(h,a.symbol,s,0,i),n[0]+=h[0],n[1]+=h[1],n[2]+=h[2],n[3]+=h[3],r?(t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],r=!1):(t[0]=Math.min(t[0],n[0]),t[1]=Math.min(t[1],n[1]),t[2]=Math.max(t[2],n[2]),t[3]=Math.max(t[3],n[3])))}return t}_getInflateSize(t,e,s,i,r){if(jn(e)){const n=this._getLayersInflateSize(t,e.symbolLayers,s,i,r),a=Os(e.effects);return a>0&&(n[0]-=a,n[1]-=a,n[2]+=a,n[3]+=a),n}return this._getTextInflatedSize(t,e,r)}_getLayersInflateSize(t,e,s,i,r){let n=!0;if(!e)return t;for(const a of e){if(!a)continue;let h=[0,0,0,0];switch(a.type){case"CIMSolidFill":case"CIMPictureFill":case"CIMHatchFill":case"CIMGradientFill":break;case"CIMSolidStroke":case"CIMPictureStroke":case"CIMGradientStroke":{const c=a;let u=c.width;u!=null&&(c.capStyle===ht.Square||c.joinStyle===lt.Miter?u/=1.4142135623730951:u/=2,h[0]=-u,h[1]=-u,h[2]=u,h[3]=u);break}case"CIMCharacterMarker":case"CIMVectorMarker":case"CIMPictureMarker":{const c=a;if(a.type==="CIMVectorMarker"){const p=a;if(h=this._vectorMarkerBounds(h,p,s,r),p.frame){const d=(p.frame.xmin+p.frame.xmax)/2,g=(p.frame.ymin+p.frame.ymax)/2;if(h[0]-=d,h[1]-=g,h[2]-=d,h[3]-=g,p.size!=null){const y=p.size/(p.frame.ymax-p.frame.ymin);h[0]*=y,h[1]*=y,h[2]*=y,h[3]*=y}}}else if(a.type==="CIMPictureMarker"){const p=a,d=s.getResource(p.url);let g=1;if(V(d)&&d.height&&(g=d.width/d.height),c.size!=null){const y=c.size/2,x=c.size*g*p.scaleX/2;h=[-x,-y,x,y]}}else if(c.size!=null){const p=c.size/2;h=[-p,-p,p,p]}if(c.anchorPoint){let p,d;c.anchorPointUnits==="Absolute"?(p=c.anchorPoint.x,d=c.anchorPoint.y):(p=c.anchorPoint.x*(h[2]-h[0]),d=c.anchorPoint.y*(h[3]-h[1])),h[0]-=p,h[1]-=d,h[2]-=p,h[3]-=d}let u=k(c.rotation);if(c.rotateClockwise&&(u=-u),i&&(u-=i),u){const p=Es*u,d=Math.cos(p),g=Math.sin(p),y=U([Se,Se,-Se,-Se]);be(y,[h[0]*d-h[1]*g,h[0]*g+h[1]*d]),be(y,[h[0]*d-h[3]*g,h[0]*g+h[3]*d]),be(y,[h[2]*d-h[1]*g,h[2]*g+h[1]*d]),be(y,[h[2]*d-h[3]*g,h[2]*g+h[3]*d]),h=y}let f=k(c.offsetX),m=k(c.offsetY);if(i){const p=Es*i,d=Math.cos(p),g=Math.sin(p),y=f*g+m*d;f=f*d-m*g,m=y}h[0]+=f,h[1]+=m,h[2]+=f,h[3]+=m;const _=Yn(c.markerPlacement);_>0&&(h[0]-=_,h[1]-=_,h[2]+=_,h[3]+=_);break}}const l=Os(a.effects);l>0&&(h[0]-=l,h[1]-=l,h[2]+=l,h[3]+=l),n?(t[0]=h[0],t[1]=h[1],t[2]=h[2],t[3]=h[3],n=!1):(t[0]=Math.min(t[0],h[0]),t[1]=Math.min(t[1],h[1]),t[2]=Math.max(t[2],h[2]),t[3]=Math.max(t[3],h[3]))}return t}_getTextInflatedSize(t,e,s){var p,d;const i=e.height??Dn;if(t[0]=-i/2,t[1]=-i/2,t[2]=i/2,t[3]=i/2,!s)return t;const r=s.get(e);if(!r)return t;const{text:n,mosaicItem:a}=r;if(!((p=a==null?void 0:a.glyphMosaicItems)!=null&&p.length))return t;const{lineGapType:h,lineGap:l}=e,c=h?Yi(h,l??0,i):0,u=fs(n)[1],f=a.glyphMosaicItems,m=((d=e.callout)==null?void 0:d.type)==="CIMBackgroundCallout",_=Gi(f,u,{scale:i/ri,angle:k(e.angle),xOffset:k(e.offsetX),yOffset:k(e.offsetY),hAlign:Hi(e.horizontalAlignment),vAlign:Di(e.verticalAlignment),maxLineWidth:512,lineHeight:ni*Math.max(.25,Math.min(c||1,4)),decoration:e.font.decoration||"none",isCIM:!0,hasBackground:m}).boundsT;return t[0]=_.x-_.halfWidth,t[1]=-_.y-_.halfHeight,t[2]=_.x+_.halfWidth,t[3]=-_.y+_.halfHeight,t}},at=class hs{static getEnvelope(t,e,s){if(!t)return null;const i=new En(s);if(Array.isArray(t)){let r;for(const n of t)r?r.union(ze(i,n,e)):r=ze(i,n,e);return r}return ze(i,t,e)}static getTextureAnchor(t,e){const s=this.getEnvelope(t,null,e);if(!s)return[0,0,0];const i=(s.x+.5*s.width)*it,r=(s.y+.5*s.height)*it,n=s.width*it+2,a=s.height*it+2;return[-i/n,-r/a,a]}static rasterize(t,e,s,i,r=!0){const n=s||this.getEnvelope(e,null,i);if(!n)return[null,0,0,0,0];const a=(n.x+.5*n.width)*it,h=(n.y+.5*n.height)*it;t.width=n.width*it,t.height=n.height*it,s||(t.width+=2,t.height+=2);const l=t.getContext("2d"),c=G.createScale(it,-it);c.translate(.5*t.width-a,.5*t.height+h);const u=new On(l,i,c);switch(e.type){case"CIMPointSymbol":{const _={type:"point",x:0,y:0};u.drawSymbol(e,_);break}case"CIMVectorMarker":{const _=new Ft;u.drawMarker(e,_);break}}const f=l.getImageData(0,0,t.width,t.height),m=new Uint8Array(f.data);if(r){let _;for(let p=0;p<m.length;p+=4)_=m[p+3]/255,m[p]=m[p]*_,m[p+1]=m[p+1]*_,m[p+2]=m[p+2]*_}return[m,t.width,t.height,-a/t.width,-h/t.height]}static fromTextSymbol(t){const{angle:e,color:s,font:i,haloColor:r,haloSize:n,horizontalAlignment:a,kerning:h,text:l,verticalAlignment:c,xoffset:u,yoffset:f,backgroundColor:m,borderLineColor:_,borderLineSize:p}=t;let d,g,y,x,P,M;i&&(d=i.family,g=i.style,y=i.weight,x=i.size,P=i.decoration);let S=!1;return l&&(S=fs(l)[1]),(m||p)&&(M={type:"CIMBackgroundCallout",margin:null,backgroundSymbol:{type:"CIMPolygonSymbol",symbolLayers:[{type:"CIMSolidFill",color:W(m)},{type:"CIMSolidStroke",color:W(_),width:p}]},accentBarSymbol:null,gap:null,leaderLineSymbol:null,lineStyle:null}),{type:"CIMPointSymbol",symbolLayers:[{type:"CIMVectorMarker",enable:!0,anchorPointUnits:"Relative",dominantSizeAxis3D:"Y",size:10,billboardMode3D:"FaceNearPlane",frame:{xmin:-5,ymin:-5,xmax:5,ymax:5},markerGraphics:[{type:"CIMMarkerGraphic",geometry:{x:0,y:0},symbol:{type:"CIMTextSymbol",angle:e,blockProgression:xr.BTT,depth3D:1,extrapolateBaselines:!0,fontEffects:Mr.Normal,fontEncoding:br.Unicode,fontFamilyName:d||"Arial",fontStyleName:Un(g,y),fontType:Sr.Unspecified,haloSize:n,height:x,hinting:Cr.Default,horizontalAlignment:Bn(a??"center"),kerning:h,letterWidth:100,ligatures:!0,lineGapType:"Multiple",offsetX:k(u),offsetY:k(f),strikethrough:P==="line-through",underline:P==="underline",symbol:{type:"CIMPolygonSymbol",symbolLayers:[{type:"CIMSolidFill",enable:!0,color:W(s)}]},haloSymbol:{type:"CIMPolygonSymbol",symbolLayers:[{type:"CIMSolidFill",enable:!0,color:W(r)}]},shadowColor:[0,0,0,255],shadowOffsetX:1,shadowOffsetY:1,textCase:"Normal",textDirection:S?bs.RTL:bs.LTR,verticalAlignment:Wn(c??"baseline"),verticalGlyphOrientation:wr.Right,wordSpacing:100,billboardMode3D:kr.FaceNearPlane,callout:M},textString:l}],scaleSymbolsProportionally:!0,respectFrame:!0}],scaleX:1,angleAlignment:"Display"}}static fromPictureFillSymbol(t){const{height:e,outline:s,width:i,xoffset:r,xscale:n,yoffset:a,yscale:h}=t,l=[],c={type:"CIMPolygonSymbol",symbolLayers:l};if(s){const{cap:p,join:d,miterLimit:g,width:y}=s;l.push({type:"CIMSolidStroke",color:W(s.color),capStyle:qe(p),joinStyle:je(d),miterLimit:g,width:y})}let u=t.url;t.type==="esriPFS"&&t.imageData&&(u=t.imageData);const f="angle"in t?t.angle??0:0,m=(i??0)*(n||1),_=(e??0)*(h||1);return l.push({type:"CIMPictureFill",invertBackfaceTexture:!1,scaleX:1,textureFilter:Ss.Picture,tintColor:null,url:u,height:_,width:m,offsetX:k(r),offsetY:k(a),rotation:k(-f),colorSubstitutions:null}),c}static fromSimpleFillSymbol(t){const{color:e,style:s,outline:i}=t,r=[],n={type:"CIMPolygonSymbol",symbolLayers:r};let a=null;if(i){const{cap:h,join:l,style:c}=i;c!=="solid"&&c!=="none"&&c!=="esriSLSSolid"&&c!=="esriSLSNull"&&(a=[{type:"CIMGeometricEffectDashes",dashTemplate:Rs(c,h),lineDashEnding:"NoConstraint",scaleDash:!0,offsetAlongLine:null}]),r.push({type:"CIMSolidStroke",color:W(i.color),capStyle:qe(h),joinStyle:je(l),miterLimit:i.miterLimit,width:i.width,effects:a})}if(s&&s!=="solid"&&s!=="none"&&s!=="esriSFSSolid"&&s!=="esriSFSNull"){const h={type:"CIMLineSymbol",symbolLayers:[{type:"CIMSolidStroke",color:W(e),capStyle:ht.Butt,joinStyle:lt.Miter,width:.75}]};let l=0;const c=Nt(Kn(s)?8:10);switch(s){case"vertical":case"esriSFSVertical":l=90;break;case"forward-diagonal":case"esriSFSForwardDiagonal":case"diagonal-cross":case"esriSFSDiagonalCross":l=-45;break;case"backward-diagonal":case"esriSFSBackwardDiagonal":l=45;break;case"cross":case"esriSFSCross":l=0}r.push({type:"CIMHatchFill",lineSymbol:h,offsetX:0,offsetY:0,rotation:l,separation:c}),s==="cross"||s==="esriSFSCross"?r.push({type:"CIMHatchFill",lineSymbol:N(h),offsetX:0,offsetY:0,rotation:90,separation:c}):s!=="diagonal-cross"&&s!=="esriSFSDiagonalCross"||r.push({type:"CIMHatchFill",lineSymbol:N(h),offsetX:0,offsetY:0,rotation:45,separation:c})}else!s||s!=="solid"&&s!=="esriSFSSolid"||r.push({type:"CIMSolidFill",enable:!0,color:W(e)});return n}static fromSimpleLineSymbol(t){const{cap:e,color:s,join:i,marker:r,miterLimit:n,style:a,width:h}=t;let l=null;a!=="solid"&&a!=="none"&&a!=="esriSLSSolid"&&a!=="esriSLSNull"&&(l=[{type:"CIMGeometricEffectDashes",dashTemplate:Rs(a,e),lineDashEnding:"NoConstraint",scaleDash:!0,offsetAlongLine:null}]);const c=[];if(r){let u;switch(r.placement){case"begin-end":u=dt.Both;break;case"begin":u=dt.JustBegin;break;case"end":u=dt.JustEnd;break;default:u=dt.None}const f=hs.fromSimpleMarker(r,h,s).symbolLayers[0];f.markerPlacement={type:"CIMMarkerPlacementAtExtremities",angleToLine:!0,offset:0,extremityPlacement:u,offsetAlongLine:0},c.push(f)}return a!=="none"&&a!=="esriSLSNull"&&c.push({type:"CIMSolidStroke",color:W(s),capStyle:qe(e),joinStyle:je(i),miterLimit:n,width:h,effects:l}),{type:"CIMLineSymbol",symbolLayers:c}}static fromPictureMarker(t){const{angle:e,height:s,width:i,xoffset:r,yoffset:n}=t;let a=t.url;return t.type==="esriPMS"&&t.imageData&&(a=t.imageData),{type:"CIMPointSymbol",symbolLayers:[{type:"CIMPictureMarker",invertBackfaceTexture:!1,scaleX:1,textureFilter:Ss.Picture,tintColor:null,url:a,size:s,width:i,offsetX:k(r),offsetY:k(n),rotation:k(-e)}]}}static fromSimpleMarker(t,e,s){const{style:i}=t,r=t.color??s;if(i==="path"){const l=[];if("outline"in t&&t.outline){const f=t.outline;l.push({type:"CIMSolidStroke",enable:!0,width:Ct(Math.round(Nt(f.width))),color:W(f.color)})}l.push({type:"CIMSolidFill",enable:!0,color:W(r),path:t.path});const[c,u]=Fs("square");return{type:"CIMPointSymbol",symbolLayers:[{type:"CIMVectorMarker",enable:!0,rotation:k(-t.angle),size:k(t.size||6),offsetX:k(t.xoffset),offsetY:k(t.yoffset),frame:c,markerGraphics:[{type:"CIMMarkerGraphic",geometry:u,symbol:{type:"CIMPolygonSymbol",symbolLayers:l}}]}]}}const[n,a]=Fs(i);let h;if(a&&n){const l=[];if("outline"in t&&t.outline){const u=t.outline;l.push({type:"CIMSolidStroke",enable:!0,width:u.width!=null&&u.width>.667?Ct(Math.round(Nt(u.width))):u.width,color:W(u.color)})}else!e||t.type!=="line-marker"||t.style!=="cross"&&t.style!=="x"||l.push({type:"CIMSolidStroke",enable:!0,width:e,color:W(r)});l.push({type:"CIMSolidFill",enable:!0,color:W(r)});const c={type:"CIMPolygonSymbol",symbolLayers:l};h={type:"CIMPointSymbol",symbolLayers:[{type:"CIMVectorMarker",enable:!0,rotation:k(-t.angle),size:k(t.size||6*e),offsetX:k(t.xoffset),offsetY:k(t.yoffset),frame:n,markerGraphics:[{type:"CIMMarkerGraphic",geometry:a,symbol:c}]}]}}return h}static fromCIMHatchFill(t,e){var h;const s=e*(t.separation??Hn),i=s/2,r=N(t.lineSymbol);(h=r.symbolLayers)==null||h.forEach(l=>{var c;switch(l.type){case"CIMSolidStroke":l.width!=null&&(l.width*=e),(c=l.effects)==null||c.forEach(u=>{u.type==="CIMGeometricEffectDashes"&&(u.dashTemplate=u.dashTemplate.map(f=>f*e))});break;case"CIMVectorMarker":{l.size!=null&&(l.size*=e);const u=l.markerPlacement;u!=null&&"placementTemplate"in u&&(u.placementTemplate=u.placementTemplate.map(f=>f*e));break}}});let n=this._getLineSymbolPeriod(r)||zs;for(;n<zs;)n*=2;const a=n/2;return{type:"CIMVectorMarker",frame:{xmin:-a,xmax:a,ymin:-i,ymax:i},markerGraphics:[{type:"CIMMarkerGraphic",geometry:{paths:[[[-a,0],[a,0]]]},symbol:r}],size:s}}static fetchResources(t,e,s){if(t&&e)switch(t.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":{const i=t.symbolLayers;if(!i)return;for(const r of i)switch(Qn(r,e,s),r.type){case"CIMPictureFill":case"CIMHatchFill":case"CIMGradientFill":case"CIMPictureStroke":case"CIMGradientStroke":case"CIMCharacterMarker":case"CIMPictureMarker":"url"in r&&r.url&&s.push(e.fetchResource(r.url,null));break;case"CIMVectorMarker":{const n=r.markerGraphics;if(!n)continue;for(const a of n)if(a){const h=a.symbol;h&&hs.fetchResources(h,e,s)}}}}}}static _getLineSymbolPeriod(t){if(t){const e=this._getEffectsRepeat(t.effects);if(e)return e;if(t.symbolLayers){for(const s of t.symbolLayers)if(s){const i=this._getEffectsRepeat(s.effects);if(i)return i;switch(s.type){case"CIMCharacterMarker":case"CIMPictureMarker":case"CIMVectorMarker":case"CIMObjectMarker3D":case"CIMglTFMarker3D":{const r=this._getPlacementRepeat(s.markerPlacement);if(r)return r}}}}}return 0}static _getEffectsRepeat(t){if(t){for(const e of t)if(e)switch(e.type){case"CIMGeometricEffectDashes":{const s=e.dashTemplate;if(s&&s.length){let i=0;for(const r of s)i+=r;return 1&s.length&&(i*=2),i}break}case"CIMGeometricEffectWave":return e.period;default:as.error(`unsupported geometric effect type ${e.type}`)}}return 0}static _getPlacementRepeat(t){if(t)switch(t.type){case"CIMMarkerPlacementAlongLineSameSize":case"CIMMarkerPlacementAlongLineRandomSize":case"CIMMarkerPlacementAlongLineVariableSize":{const e=t.placementTemplate;if(e&&e.length){let s=0;for(const i of e)s+=+i;return 1&e.length&&(s*=2),s}break}}return 0}static fromCIMInsidePolygon(t){const e=t.markerPlacement,s={...t};s.markerPlacement=null,s.anchorPoint=null;const i=Math.abs(e.stepX),r=Math.abs(e.stepY),n=(e.randomness??100)/100;let a,h,l,c;if(e.gridType==="Random"){const u=Nt(oi),f=Math.max(Math.floor(u/i),1),m=Math.max(Math.floor(u/r),1);a=f*i/2,h=m*r/2,l=2*h;const _=new ei(e.seed),p=n*i/1.5,d=n*r/1.5;c=[];for(let g=0;g<f;g++)for(let y=0;y<m;y++){const x=g*i-a+p*(.5-_.getFloat()),P=y*r-h+d*(.5-_.getFloat());c.push({x,y:P}),g===0&&c.push({x:x+2*a,y:P}),y===0&&c.push({x,y:P+2*h})}}else e.shiftOddRows===!0?(a=i/2,h=r,l=2*r,c=[{x:-a,y:0},{x:a,y:0},{x:0,y:h},{x:0,y:-h}]):(a=i/2,h=r/2,l=r,c=[{x:-i,y:0},{x:0,y:-r},{x:-i,y:-r},{x:0,y:0},{x:i,y:0},{x:0,y:r},{x:i,y:r},{x:-i,y:r},{x:i,y:-r}]);return{type:"CIMVectorMarker",frame:{xmin:-a,xmax:a,ymin:-h,ymax:h},markerGraphics:c.map(u=>({type:"CIMMarkerGraphic",geometry:u,symbol:{type:"CIMPointSymbol",symbolLayers:[s]}})),size:l}}static getSize(t){if(t)switch(t.type){case"CIMTextSymbol":return t.height;case"CIMPointSymbol":{let e=0;if(t.symbolLayers){for(const s of t.symbolLayers)if(s)switch(s.type){case"CIMCharacterMarker":case"CIMPictureMarker":case"CIMVectorMarker":case"CIMObjectMarker3D":case"CIMglTFMarker3D":{const i=s.size;i!=null&&i>e&&(e=i);break}}}return e}case"CIMLineSymbol":case"CIMPolygonSymbol":{let e=0;if(t.symbolLayers){for(const s of t.symbolLayers)if(s)switch(s.type){case"CIMSolidStroke":case"CIMPictureStroke":case"CIMGradientStroke":{const i=s.width;i!=null&&i>e&&(e=i);break}case"CIMCharacterMarker":case"CIMPictureMarker":case"CIMVectorMarker":case"CIMObjectMarker3D":case"CIMglTFMarker3D":if(s.markerPlacement&&hr(s.markerPlacement)){const i=s.size;i!=null&&i>e&&(e=i)}}}return e}}}static getMarkerScaleRatio(t){if(t&&t.type==="CIMVectorMarker"&&t.scaleSymbolsProportionally!==!1&&t.frame&&t.size!=null){const e=t.frame.ymax-t.frame.ymin;return t.size/e}return 1}},wt=class E{static findApplicableOverrides(t,e,s){if(t&&e){if(t.primitiveName){let i=!1;for(const r of s)if(r.primitiveName===t.primitiveName){i=!0;break}if(!i)for(const r of e)r.primitiveName===t.primitiveName&&s.push(r)}switch(t.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":if(t.effects)for(const i of t.effects)E.findApplicableOverrides(i,e,s);if(t.symbolLayers)for(const i of t.symbolLayers)E.findApplicableOverrides(i,e,s);break;case"CIMTextSymbol":break;case"CIMSolidStroke":case"CIMPictureStroke":case"CIMGradientStroke":case"CIMSolidFill":case"CIMPictureFill":case"CIMHatchFill":case"CIMGradientFill":case"CIMVectorMarker":case"CIMCharacterMarker":case"CIMPictureMarker":if(t.effects)for(const i of t.effects)E.findApplicableOverrides(i,e,s);if(t.markerPlacement&&E.findApplicableOverrides(t.markerPlacement,e,s),t.type==="CIMVectorMarker"){if(t.markerGraphics)for(const i of t.markerGraphics)E.findApplicableOverrides(i,e,s),E.findApplicableOverrides(i.symbol,e,s)}else t.type==="CIMCharacterMarker"?E.findApplicableOverrides(t.symbol,e,s):t.type==="CIMHatchFill"?E.findApplicableOverrides(t.lineSymbol,e,s):t.type==="CIMPictureMarker"&&E.findApplicableOverrides(t.animatedSymbolProperties,e,s)}}}static findEffectOverrides(t,e,s){var r;if(!e||!t)return;const i=t.length;for(let n=0;n<i;n++){const a=(r=t[n])==null?void 0:r.primitiveName;if(a){let h=!1;for(const l of s)if(l.primitiveName===a){h=!0;break}if(!h)for(const l of e)l.primitiveName===a&&s.push(l)}}}static async resolveSymbolOverrides(t,e,s,i,r,n,a){if(!t||!t.symbol)return null;let{symbol:h,primitiveOverrides:l}=t;const c=!!l;if(!c&&!i)return h;h=N(h);let u=!0;if(e||(e={attributes:{}},u=!1),c){if(u||(l=l.filter(f=>{var m;return!((m=f.valueExpressionInfo)!=null&&m.expression.includes("$feature"))})),a||(l=l.filter(f=>{var m;return!((m=f.valueExpressionInfo)!=null&&m.expression.includes("$view"))})),l.length>0){const f=ur(e.attributes);await E.evaluateOverrides(l,e,{spatialReference:s,fields:f,geometryType:r},n,a)}E.applyOverrides(h,l)}return i&&E.applyDictionaryTextOverrides(h,e,i),h}static async evaluateOverrides(t,e,s,i,r){if(!e)return;let n;for(const a of t){const h=a.valueExpressionInfo;if(h&&s&&s.geometryType){n||(n=[]),a.value=void 0;const l=Ks(h.expression,s.spatialReference,s.fields).then(c=>{a.value=Rt(c,e,{$view:r},s.geometryType,i)});n.push(l)}}n!==void 0&&n.length>0&&await Promise.all(n)}static applyDictionaryTextOverrides(t,e,s,i="Normal"){if(t&&t.type)switch(t.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":case"CIMTextSymbol":{const r=t.symbolLayers;if(!r)return;for(const n of r)n&&n.type==="CIMVectorMarker"&&E.applyDictionaryTextOverrides(n,e,s,t.type==="CIMTextSymbol"?t.textCase:i)}break;case"CIMVectorMarker":{const r=t.markerGraphics;if(!r)return;for(const n of r)n&&E.applyDictionaryTextOverrides(n,e,s)}break;case"CIMMarkerGraphic":{const r=t.textString;if(r&&r.includes("[")){const n=fr(r,s);t.textString=mr(e,n,i)}}}}static applyOverrides(t,e,s,i){if(t.primitiveName){for(const r of e)if(r.primitiveName===t.primitiveName){const n=Zn(r.propertyName);if(i&&i.push({cim:t,nocapPropertyName:n,value:t[n]}),r.expression&&(r.value=E.toValue(r.propertyName,r.expression)),s){let a=!1;for(const h of s)h.primitiveName===t.primitiveName&&(a=!0);a||s.push(r)}V(r.value)&&(t[n]=r.value)}}switch(t.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":if(t.effects)for(const r of t.effects)E.applyOverrides(r,e,s,i);if(t.symbolLayers)for(const r of t.symbolLayers)E.applyOverrides(r,e,s,i);break;case"CIMTextSymbol":break;case"CIMSolidStroke":case"CIMSolidFill":case"CIMVectorMarker":if(t.effects)for(const r of t.effects)E.applyOverrides(r,e,s,i);if(t.type==="CIMVectorMarker"&&t.markerGraphics)for(const r of t.markerGraphics)E.applyOverrides(r,e,s,i),E.applyOverrides(r.symbol,e,s,i)}}static restoreOverrides(t){for(const e of t)e.cim[e.nocapPropertyName]=e.value}static buildOverrideKey(t){let e="";for(const s of t)s.value!==void 0&&(e+=`${s.primitiveName}${s.propertyName}${JSON.stringify(s.value)}`);return e}static toValue(t,e){if(t==="DashTemplate")return e.split(" ").map(s=>Number(s));if(t==="Color"){const s=new Zs(e).toRgba();return s[3]*=255,s}return e}};const qe=o=>{if(!o)return ht.Butt;switch(o){case"butt":return ht.Butt;case"square":return ht.Square;case"round":return ht.Round}},je=o=>{if(!o)return lt.Miter;switch(o){case"miter":return lt.Miter;case"round":return lt.Round;case"bevel":return lt.Bevel}},Bn=o=>{if(Z(o))return"Center";switch(o){case"left":return"Left";case"right":return"Right";case"center":return"Center"}},Wn=o=>{if(Z(o))return"Center";switch(o){case"baseline":return"Baseline";case"top":return"Top";case"middle":return"Center";case"bottom":return"Bottom"}},W=o=>{if(!o)return[0,0,0,0];const{r:t,g:e,b:s,a:i}=o;return[t,e,s,255*i]},Un=(o,t)=>{const e=Jn(t),s=qn(o);return e&&s?`${e}-${s}`:`${e}${s}`},Jn=o=>{if(!o)return"";switch(o.toLowerCase()){case"bold":case"bolder":return"bold"}return""},qn=o=>{if(!o)return"";switch(o.toLowerCase()){case"italic":case"oblique":return"italic"}return""},Rs=(o,t)=>{const e=t==="butt";switch(o){case"dash":case"esriSLSDash":return e?[4,3]:[3,4];case"dash-dot":case"esriSLSDashDot":return e?[4,3,1,3]:[3,4,0,4];case"dot":case"esriSLSDot":return e?[1,3]:[0,4];case"long-dash":case"esriSLSLongDash":return e?[8,3]:[7,4];case"long-dash-dot":case"esriSLSLongDashDot":return e?[8,3,1,3]:[7,4,0,4];case"long-dash-dot-dot":case"esriSLSDashDotDot":return e?[8,3,1,3,1,3]:[7,4,0,4,0,4];case"short-dash":case"esriSLSShortDash":return e?[4,1]:[3,2];case"short-dash-dot":case"esriSLSShortDashDot":return e?[4,1,1,1]:[3,2,0,2];case"short-dash-dot-dot":case"esriSLSShortDashDotDot":return e?[4,1,1,1,1,1]:[3,2,0,2,0,2];case"short-dot":case"esriSLSShortDot":return e?[1,1]:[0,2];case"solid":case"esriSLSSolid":case"none":return as.error("Unexpected: style does not require rasterization"),[0,0];default:return as.error(`Tried to rasterize SLS, but found an unexpected style: ${o}!`),[0,0]}};function jn(o){return o.symbolLayers!==void 0}const Fs=o=>{let s,i;const r=o;if(r==="circle"||r==="esriSMSCircle"){let a=Math.acos(.995),h=Math.ceil(Bi/a/4);h===0&&(h=1),a=Xn/h,h*=4;const l=[];l.push([50,0]);for(let c=1;c<h;c++)l.push([50*Math.cos(c*a),-50*Math.sin(c*a)]);l.push([50,0]),s={rings:[l]},i={xmin:-50,ymin:-50,xmax:50,ymax:50}}else if(r==="cross"||r==="esriSMSCross")s={rings:[[[0,50],[0,0],[50,0],[50,-0],[0,-0],[0,-50],[-0,-50],[-0,-0],[-50,-0],[-50,0],[-0,0],[-0,50],[0,50]]]},i={xmin:-50,ymin:-50,xmax:50,ymax:50};else if(r==="diamond"||r==="esriSMSDiamond")s={rings:[[[-50,0],[0,50],[50,0],[0,-50],[-50,0]]]},i={xmin:-50,ymin:-50,xmax:50,ymax:50};else if(r==="square"||r==="esriSMSSquare")s={rings:[[[-50,-50],[-50,50],[50,50],[50,-50],[-50,-50]]]},i={xmin:-50,ymin:-50,xmax:50,ymax:50};else if(r==="x"||r==="esriSMSX")s={rings:[[[0,0],[50,50],[50,50],[0,0],[50,-50],[50,-50],[0,-0],[-50,-50],[-50,-50],[-0,0],[-50,50],[-50,50],[0,0]]]},i={xmin:-50,ymin:-50,xmax:50,ymax:50};else if(r==="triangle"||r==="esriSMSTriangle"){const n=57.735026918962575,a=-n,h=2/3*100,l=h-100;s={rings:[[[a,l],[0,h],[n,l],[a,l]]]},i={xmin:a,ymin:l,xmax:n,ymax:h}}else r==="arrow"&&(s={rings:[[[-50,50],[50,0],[-50,-50],[-33,-20],[-33,20],[-50,50]]]},i={xmin:-50,ymin:-50,xmax:50,ymax:50});return[i,s]},Kn=o=>o==="vertical"||o==="horizontal"||o==="cross"||o==="esriSFSCross"||o==="esriSFSVertical"||o==="esriSFSHorizontal",Zn=o=>o&&o.charAt(0).toLowerCase()+o.substr(1);function Qn(o,t,e){if(!(!o.effects||V(t.geometryEngine))){if(t.geometryEnginePromise)return void e.push(t.geometryEnginePromise);lr(o.effects)&&(t.geometryEnginePromise=cr(),e.push(t.geometryEnginePromise),t.geometryEnginePromise.then(s=>t.geometryEngine=s))}}const Gs=.05;function to(o){return Math.max(Math.round(o/Gs),1)*Gs}const eo=new Set(["StartTimeOffset","Duration","RepeatDelay"]);function so(o,t){return eo.has(t)?to(o):o}function io(o){var t;if(!o)return null;switch(o.type){case"CIMPointSymbol":{const e=o.symbolLayers;return e&&e.length===1?io(e[0]):null}case"CIMVectorMarker":{const e=o.markerGraphics;if(!e||e.length!==1)return null;const s=e[0];if(!s)return null;const i=s.geometry;if(!i)return null;const r=s.symbol;return!r||r.type!=="CIMPolygonSymbol"&&r.type!=="CIMLineSymbol"||(t=r.symbolLayers)!=null&&t.some(n=>!!n.effects)?null:{geom:i,asFill:r.type==="CIMPolygonSymbol"}}case"sdf":return{geom:o.geom,asFill:o.asFill}}return null}function ro(o){return o?o.rings?o.rings:o.paths?o.paths:o.xmin!==void 0&&o.ymin!==void 0&&o.xmax!==void 0&&o.ymax!==void 0?[[[o.xmin,o.ymin],[o.xmin,o.ymax],[o.xmax,o.ymax],[o.xmax,o.ymin],[o.xmin,o.ymin]]]:null:null}function no(o){let t=1/0,e=-1/0,s=1/0,i=-1/0;for(const r of o)for(const n of r)n[0]<t&&(t=n[0]),n[0]>e&&(e=n[0]),n[1]<s&&(s=n[1]),n[1]>i&&(i=n[1]);return new Ei(t,s,e-t,i-s)}function Xs(o){let t=1/0,e=-1/0,s=1/0,i=-1/0;for(const r of o)for(const n of r)n[0]<t&&(t=n[0]),n[0]>e&&(e=n[0]),n[1]<s&&(s=n[1]),n[1]>i&&(i=n[1]);return[t,s,e,i]}function Wi(o){return o?o.rings?Xs(o.rings):o.paths?Xs(o.paths):R(o)?[o.xmin,o.ymin,o.xmax,o.ymax]:null:null}function Ui(o,t,e,s,i){const[r,n,a,h]=o;if(a<r||h<n)return[0,0,0];const l=a-r,c=h-n,u=128,f=1,m=Math.floor(.5*(.5*u-f)),_=(u-2*(m+f))/Math.max(l,c),p=Math.round(l*_)+2*m,d=Math.round(c*_)+2*m;let g=1;t&&(g=d/_/(t.ymax-t.ymin));let y=0,x=0,P=1;s&&(i?t&&e&&t.ymax-t.ymin>0&&(P=(t.xmax-t.xmin)/(t.ymax-t.ymin),y=s.x/(e*P),x=s.y/e):(y=s.x,x=s.y)),t&&(y=.5*(t.xmax+t.xmin)+y*(t.xmax-t.xmin),x=.5*(t.ymax+t.ymin)+x*(t.ymax-t.ymin)),y-=r,x-=n,y*=_,x*=_,y+=m,x+=m;let M=y/p-.5,S=x/d-.5;return i&&e&&(M*=e*P,S*=e),[g,M,S]}function Oa(o){const t=ro(o.geom),e=no(t),s=128,i=1,r=Math.floor(.5*(.5*s-i)),n=(s-2*(r+i))/Math.max(e.width,e.height),a=Math.round(e.width*n)+2*r,h=Math.round(e.height*n)+2*r,l=[];for(const u of t)if(u&&u.length>1){const f=[];for(const m of u){let[_,p]=m;_-=e.x,p-=e.y,_*=n,p*=n,_+=r-.5,p+=r-.5,o.asFill?f.push([_,p]):f.push([Math.round(_),Math.round(p)])}if(o.asFill){const m=f.length-1;f[0][0]===f[m][0]&&f[0][1]===f[m][1]||f.push(f[0])}l.push(f)}const c=oo(l,a,h,r);return o.asFill&&ao(l,a,h,r,c),[ho(c,r),a,h]}function oo(o,t,e,s){const i=t*e,r=new Array(i),n=s*s+1;for(let a=0;a<i;++a)r[a]=n;for(const a of o){const h=a.length;for(let l=1;l<h;++l){const c=a[l-1],u=a[l];let f,m,_,p;c[0]<u[0]?(f=c[0],m=u[0]):(f=u[0],m=c[0]),c[1]<u[1]?(_=c[1],p=u[1]):(_=u[1],p=c[1]);let d=Math.floor(f)-s,g=Math.floor(m)+s,y=Math.floor(_)-s,x=Math.floor(p)+s;d<0&&(d=0),g>t&&(g=t),y<0&&(y=0),x>e&&(x=e);const P=u[0]-c[0],M=u[1]-c[1],S=P*P+M*M;for(let w=d;w<g;w++)for(let I=y;I<x;I++){let v,C,L=(w-c[0])*P+(I-c[1])*M;L<0?(v=c[0],C=c[1]):L>S?(v=u[0],C=u[1]):(L/=S,v=c[0]+L*P,C=c[1]+L*M);const O=(w-v)*(w-v)+(I-C)*(I-C),X=(e-I-1)*t+w;O<r[X]&&(r[X]=O)}}}for(let a=0;a<i;++a)r[a]=Math.sqrt(r[a]);return r}function ao(o,t,e,s,i){for(const r of o){const n=r.length;for(let a=1;a<n;++a){const h=r[a-1],l=r[a];let c,u,f,m;h[0]<l[0]?(c=h[0],u=l[0]):(c=l[0],u=h[0]),h[1]<l[1]?(f=h[1],m=l[1]):(f=l[1],m=h[1]);let _=Math.floor(c),p=Math.floor(u)+1,d=Math.floor(f),g=Math.floor(m)+1;_<s&&(_=s),p>t-s&&(p=t-s),d<s&&(d=s),g>e-s&&(g=e-s);for(let y=d;y<g;++y){if(h[1]>y==l[1]>y)continue;const x=(e-y-1)*t;for(let P=_;P<p;++P)P<(l[0]-h[0])*(y-h[1])/(l[1]-h[1])+h[0]&&(i[x+P]=-i[x+P]);for(let P=s;P<_;++P)i[x+P]=-i[x+P]}}}}function ho(o,t){const e=2*t,s=o.length,i=new Uint8Array(4*s);for(let r=0;r<s;++r){const n=.5-o[r]/e;Ir(n,i,4*r)}return i}const lo=96/72;class Hs{static executeEffects(t,e,s,i){const r=Tr(e),n=lo;let a=new es(r);for(const h of t){const l=rs(h);l&&(a=l.execute(a,h,n,s,i))}return a}static next(t){const e=t.next();return Ar(e),e}static applyEffects(t,e,s){if(!t)return e;let i=new es(e);for(const a of t){const h=rs(a);h&&(i=h.execute(i,a,1,null,s))}let r,n=null;for(;r=i.next();)n?A(n)?A(r)&&n.paths.push(...r.paths):$(n)&&$(r)&&n.rings.push(...r.rings):n=r;return n}}function co(o,t){let e;if(typeof o=="string")e=z(o+`-seed(${t})`);else{let s=12;e=o^t;do e=107*(e>>8^e)+s|0;while(--s!=0)}return(1+e/(1<<31))/2}function uo(o){return Math.floor(co(o,fo)*mo)}const fo=53290320,mo=10,Ji=us.getLogger("esri.symbols.cim.cimAnalyzer");function ds(o){switch(o){case"Butt":return De.BUTT;case"Square":return De.SQUARE;default:return De.ROUND}}function gs(o){switch(o){case"Bevel":return Ye.BEVEL;case"Miter":return Ye.MITER;default:return Ye.ROUND}}function Ds(o,t,e,s){let i;o[t]?i=o[t]:(i={},o[t]=i),i[e]=s}function Ys(o){const t=o.markerPlacement;return t&&t.angleToLine?Ee.MAP:Ee.SCREEN}async function Ra(o,t,e,s,i){const r=s??[];if(!o)return r;let n,a;const h={};if(o.type!=="CIMSymbolReference")return Ji.error("Expect cim type to be 'CIMSymbolReference'"),r;if(n=o.symbol,a=o.primitiveOverrides,a){const c=[];for(const u of a){const f=u.valueExpressionInfo;if(f&&t){const m=f.expression,_=Ks(m,t.spatialReference,t.fields).then(p=>{Z(p)||Ds(h,u.primitiveName,u.propertyName,p)});c.push(_)}else u.value!=null&&Ds(h,u.primitiveName,u.propertyName,u.value)}c.length>0&&await Promise.all(c)}const l=[];switch(at.fetchResources(n,e,l),l.length>0&&await Promise.all(l),n==null?void 0:n.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":po(n,a,h,t,r,e,!!i)}return r}function po(o,t,e,s,i,r,n){if(!o)return;const a=o.symbolLayers;if(!a)return;const h=o.effects;let l=Ee.SCREEN;const c=at.getSize(o)??0;o.type==="CIMPointSymbol"&&o.angleAlignment==="Map"&&(l=Ee.MAP);let u=a.length;for(;u--;){const f=a[u];if(!f||f.enable===!1)continue;let m;h&&h.length&&(m=[...h]);const _=f.effects;_&&_.length&&(h?m.push(..._):m=[..._]);const p=[];let d;wt.findEffectOverrides(m,t,p),d=p.length>0?Lo(m,p,e,s):m;const g=[];switch(wt.findApplicableOverrides(f,t,g),f.type){case"CIMSolidFill":_o(f,d,e,g,s,i);break;case"CIMPictureFill":go(f,d,e,g,s,r,i);break;case"CIMHatchFill":yo(f,d,e,g,s,i);break;case"CIMGradientFill":Po(f,d,e,g,s,i);break;case"CIMSolidStroke":xo(f,d,e,g,s,i,o.type==="CIMPolygonSymbol",c);break;case"CIMPictureStroke":Mo(f,d,e,g,s,i,o.type==="CIMPolygonSymbol",c);break;case"CIMGradientStroke":bo(f,d,e,g,s,i,o.type==="CIMPolygonSymbol",c);break;case"CIMCharacterMarker":if(Ke(f,d,e,g,s,i))break;break;case"CIMPictureMarker":if(Ke(f,d,e,g,s,i))break;o.type==="CIMLineSymbol"&&(l=Ys(f)),So(f,d,e,g,s,r,i,l,c);break;case"CIMVectorMarker":if(Ke(f,d,e,g,s,i))break;o.type==="CIMLineSymbol"&&(l=Ys(f)),Co(f,d,e,g,s,i,r,l,c,n);break;default:Ji.error("Cannot analyze CIM layer",f.type)}}}function _o(o,t,e,s,i,r){const n=o.primitiveName,a=j(o.color),[h,l]=K(s,n,t,null,null),c=z(JSON.stringify(o)+l).toString();r.push({type:"fill",templateHash:c,materialHash:h?()=>c:c,cim:o,materialOverrides:null,colorLocked:!!o.colorLocked,color:b(n,e,"Color",i,a,Q),height:0,angle:0,offsetX:0,offsetY:0,scaleX:1,effects:t,applyRandomOffset:!1,sampleAlphaOnly:!0})}function go(o,t,e,s,i,r,n){const a=o.primitiveName,h=Oe(o),[l,c]=K(s,a,t,null,null),u=z(JSON.stringify(o)+c).toString(),f=z(`${o.url}${JSON.stringify(o.colorSubstitutions)}`).toString();let m=k(o.scaleX);if("width"in o&&typeof o.width=="number"){const _=o.width;let p=1;const d=r.getResource(o.url);V(d)&&(p=d.width/d.height),m/=p*(o.height/_)}n.push({type:"fill",templateHash:u,materialHash:l?()=>f:f,cim:o,materialOverrides:null,colorLocked:!!o.colorLocked,effects:t,color:b(a,e,"TintColor",i,h,Q),height:b(a,e,"Height",i,o.height),scaleX:b(a,e,"ScaleX",i,m),angle:b(a,e,"Rotation",i,k(o.rotation)),offsetX:b(a,e,"OffsetX",i,k(o.offsetX)),offsetY:b(a,e,"OffsetY",i,k(o.offsetY)),url:o.url,applyRandomOffset:!1,sampleAlphaOnly:!1})}function yo(o,t,e,s,i,r){var d,g;const n=["Rotation","OffsetX","OffsetY"],a=s.filter(y=>y.primitiveName!==o.primitiveName||!n.includes(y.propertyName)),h=o.primitiveName;let[l,c]=K(s,h,t,null,null);const u=z(JSON.stringify(o)+c).toString(),f=z(`${o.separation}${JSON.stringify(o.lineSymbol)}`).toString();let m={r:255,g:255,b:255,a:1},_=!1;const p=(g=(d=o.lineSymbol)==null?void 0:d.symbolLayers)==null?void 0:g.find(y=>{var x;return y.type==="CIMSolidStroke"&&((x=e[y.primitiveName])==null?void 0:x.Color)!=null});if(p){m=j(p.color),m=b(p.primitiveName,e,"Color",i,m,Q);const y=typeof m=="function";l=l||y,_=p.color!=null||y}r.push({type:"fill",templateHash:u,materialHash:l?Pe(f,e,a,i):f,cim:o,materialOverrides:a,colorLocked:!!o.colorLocked,effects:t,color:m,height:b(h,e,"Separation",i,o.separation),scaleX:1,angle:b(h,e,"Rotation",i,k(o.rotation)),offsetX:b(h,e,"OffsetX",i,k(o.offsetX)),offsetY:b(h,e,"OffsetY",i,k(o.offsetY)),applyRandomOffset:!1,sampleAlphaOnly:!0,hasUnresolvedReplacementColor:!_})}function Po(o,t,e,s,i,r){const n=o.primitiveName,[a,h]=K(s,n,t,null,null),l=z(JSON.stringify(o)+h).toString();r.push({type:"fill",templateHash:l,materialHash:a?Pe(l,e,s,i):l,cim:o,materialOverrides:null,colorLocked:!!o.colorLocked,effects:t,color:{r:128,g:128,b:128,a:1},height:0,angle:0,offsetX:0,offsetY:0,scaleX:1,applyRandomOffset:!1,sampleAlphaOnly:!1})}function xo(o,t,e,s,i,r,n,a){const h=o.primitiveName,l=j(o.color),c=o.width!=null?o.width:4,u=ds(o.capStyle),f=gs(o.joinStyle),m=o.miterLimit,[_,p]=K(s,h,t,null,null),d=z(JSON.stringify(o)+p).toString();let g,y;if(t&&t instanceof Array&&t.length>0){const x=t[t.length-1];if(x.type==="CIMGeometricEffectDashes"&&x.lineDashEnding==="NoConstraint"&&x.offsetAlongLine===null){const P=(t=[...t]).pop();g=P.dashTemplate,y=P.scaleDash}}r.push({type:"line",templateHash:d,materialHash:_?()=>d:d,cim:o,materialOverrides:null,isOutline:n,colorLocked:!!o.colorLocked,effects:t,color:b(h,e,"Color",i,l,Q),width:b(h,e,"Width",i,c),cap:b(h,e,"CapStyle",i,u),join:b(h,e,"JoinStyle",i,f),miterLimit:m&&b(h,e,"MiterLimit",i,m),referenceWidth:a,zOrder:ys(o.name),dashTemplate:g,scaleDash:y,sampleAlphaOnly:!0})}function Mo(o,t,e,s,i,r,n,a){const h=z(`${o.url}${JSON.stringify(o.colorSubstitutions)}`).toString(),l=o.primitiveName,c=Oe(o),u=o.width!=null?o.width:4,f=ds(o.capStyle),m=gs(o.joinStyle),_=o.miterLimit,[p,d]=K(s,l,t,null,null),g=z(JSON.stringify(o)+d).toString();r.push({type:"line",templateHash:g,materialHash:p?()=>h:h,cim:o,materialOverrides:null,isOutline:n,colorLocked:!!o.colorLocked,effects:t,color:b(l,e,"TintColor",i,c,Q),width:b(l,e,"Width",i,u),cap:b(l,e,"CapStyle",i,f),join:b(l,e,"JoinStyle",i,m),miterLimit:_&&b(l,e,"MiterLimit",i,_),referenceWidth:a,zOrder:ys(o.name),dashTemplate:null,scaleDash:!1,url:o.url,sampleAlphaOnly:!1})}function bo(o,t,e,s,i,r,n,a){const h=o.primitiveName,l=o.width!=null?o.width:4,c=ds(o.capStyle),u=gs(o.joinStyle),f=o.miterLimit,[m,_]=K(s,h,t,null,null),p=z(JSON.stringify(o)+_).toString();r.push({type:"line",templateHash:p,materialHash:m?Pe(p,e,s,i):p,cim:o,materialOverrides:null,isOutline:n,colorLocked:!!o.colorLocked,effects:t,color:{r:128,g:128,b:128,a:1},width:b(h,e,"Width",i,l),cap:b(h,e,"CapStyle",i,c),join:b(h,e,"JoinStyle",i,u),miterLimit:f&&b(h,e,"MiterLimit",i,f),referenceWidth:a,zOrder:ys(o.name),dashTemplate:null,scaleDash:!1,sampleAlphaOnly:!1})}function Ke(o,t,e,s,i,r){const{markerPlacement:n,type:a}=o;if(!n||n.type!=="CIMMarkerPlacementInsidePolygon")return!1;if(a==="CIMVectorMarker"||a==="CIMPictureMarker"){const M=o.primitiveName;if(M){const[w,I]=K(s,M,t,null,null);if(w)return!1}const S=n.primitiveName;if(S){const[w,I]=K(s,S,t,null,null);if(w)return!1}if(a==="CIMVectorMarker"){const{markerGraphics:w}=o;if(w)for(const I of w){const{symbol:v}=I;if((v==null?void 0:v.type)==="CIMPolygonSymbol"&&v.symbolLayers){const{symbolLayers:C}=v;for(const L of C)if(L.type==="CIMSolidStroke")return!1}}}else{const{animatedSymbolProperties:w}=o;if(w)return!1}}const h=n,l=Math.abs(h.stepX),c=Math.abs(h.stepY);if(l===0||c===0)return!0;const u=["Rotation","OffsetX","OffsetY"],f=s.filter(M=>M.primitiveName!==o.primitiveName||!u.includes(M.propertyName)),m="url"in o&&typeof o.url=="string"?o.url:void 0,[_,p]=K(s,h.primitiveName,t,null,null),d=z(JSON.stringify(o)+p).toString();let g,y,x=null;if(n.gridType==="Random"){const M=Nt(oi),S=Math.max(Math.floor(M/l),1),w=Math.max(Math.floor(M/c),1);g=c*w,x=I=>I?I*w:0,y=S*l/g}else n.shiftOddRows?(g=2*c,x=M=>M?2*M:0,y=l/c*.5):(g=c,x=null,y=l/c);const P=Oe(o);return r.push({type:"fill",templateHash:d,materialHash:_?Pe(d,e,f,i):d,cim:o,materialOverrides:f,colorLocked:!!o.colorLocked,effects:t,color:b(h.primitiveName,e,"TintColor",i,P,Q),height:b(h.primitiveName,e,"StepY",i,g,x),scaleX:y,angle:b(h.primitiveName,e,"GridAngle",i,h.gridAngle),offsetX:b(h.primitiveName,e,"OffsetX",i,k(h.offsetX)),offsetY:b(h.primitiveName,e,"OffsetY",i,k(h.offsetY)),url:m,applyRandomOffset:n.gridType==="Random",sampleAlphaOnly:!m,hasUnresolvedReplacementColor:!0}),!0}function So(o,t,e,s,i,r,n,a,h){const l=o.primitiveName,c=k(o.size);let u=k(o.scaleX,1);const f=k(o.rotation),m=k(o.offsetX),_=k(o.offsetY),p=Oe(o),d=z(`${o.url}${JSON.stringify(o.colorSubstitutions)}${JSON.stringify(o.animatedSymbolProperties)}`).toString(),g=qi(o.markerPlacement,s,e,i),y=To(o.animatedSymbolProperties,s,e,i),[x,P]=K(s,l,t,g,y),M=z(JSON.stringify(o)+P).toString(),S=o.anchorPoint??{x:0,y:0};if("width"in o&&typeof o.width=="number"){const v=o.width;let C=1;const L=r.getResource(o.url);V(L)&&(C=L.width/L.height),u/=C*(c/v)}function w(v,C){return V(y)?Qs(y,v,C):null}const I=o.animatedSymbolProperties&&o.animatedSymbolProperties.randomizeStartTime===!0?(v,C,L,O)=>{const X=uo(O??0),ut=w(v,C);return d+`-MATERIALGROUP(${X})-ASP(${JSON.stringify(ut)})`}:x?(v,C)=>{const L=w(v,C);return d+`-ASP(${JSON.stringify(L)})`}:d;n.push({type:"marker",templateHash:M,materialHash:I,cim:o,materialOverrides:null,colorLocked:!!o.colorLocked,effects:t,scaleSymbolsProportionally:!1,alignment:a,size:b(l,e,"Size",i,c),scaleX:b(l,e,"ScaleX",i,u),rotation:b(l,e,"Rotation",i,f),offsetX:b(l,e,"OffsetX",i,m),offsetY:b(l,e,"OffsetY",i,_),color:b(l,e,"TintColor",i,p,Q),anchorPoint:{x:S.x,y:-S.y},isAbsoluteAnchorPoint:o.anchorPointUnits!=="Relative",outlineColor:{r:0,g:0,b:0,a:0},outlineWidth:0,frameHeight:0,rotateClockwise:!!o.rotateClockwise,referenceSize:h,sizeRatio:1,markerPlacement:g,url:o.url,animatedSymbolProperties:y})}function Co(o,t,e,s,i,r,n,a,h,l){const c=o.markerGraphics;if(!c)return;let u=0;if(o.scaleSymbolsProportionally){const m=o.frame;m&&(u=m.ymax-m.ymin)}const f=qi(o.markerPlacement,s,e,i);for(const m of c)if(m){const _=m.symbol;if(!_)continue;switch(_.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":ko(o,t,f,null,m,s,e,i,r,n,a,h,u,!!l);break;case"CIMTextSymbol":wo(o,t,f,m,e,s,i,r,a,h,u)}}}function wo(o,t,e,s,i,r,n,a,h,l,c){const u=[];wt.findApplicableOverrides(s,r,u);const f=s.geometry;if(!("x"in f)||!("y"in f))return;const m=s.symbol,_=Us(m),p=Js(m.fontStyleName),d=ti(m.fontFamilyName);m.font={family:d,decoration:_,...p};const g=o.frame,y=f.x-.5*(g.xmin+g.xmax),x=f.y-.5*(g.ymin+g.ymax),P=o.size/c,M=o.primitiveName,S=k(m.height)*P,w=k(m.angle),I=k(o.offsetX)+(k(m.offsetX)+y)*P,v=k(o.offsetY)+(k(m.offsetY)+x)*P,C=j(pe(m));let L=j(_e(m)),O=ls(m)??0;O||(L=j(pe(m.haloSymbol)),m.haloSize&&(O=m.haloSize*P));let X=null,ut=null,kt=0;if(m.callout&&m.callout.type==="CIMBackgroundCallout"){const gt=m.callout;if(gt.backgroundSymbol){const Lt=gt.backgroundSymbol.symbolLayers;if(Lt)for(const Y of Lt)Y.type==="CIMSolidFill"?X=j(Y.color):Y.type==="CIMSolidStroke"&&(ut=j(Y.color),kt=k(Y.width))}}const[vt,tt]=K(r,M,t,e,null),It=JSON.stringify(o.effects)+Number(o.colorLocked).toString()+JSON.stringify(o.anchorPoint)+o.anchorPointUnits+JSON.stringify(o.markerPlacement)+o.size.toString(),Gt=z(JSON.stringify(s)+It+tt).toString();let H=b(s.primitiveName,i,"TextString",n,s.textString??"",pr,m.textCase);if(H==null)return;const{fontStyleName:ft}=m,B=d+(ft?"-"+ft.toLowerCase():"-regular"),xe=B;typeof H=="string"&&H.includes("[")&&m.fieldMap&&(H=_r(m.fieldMap,H,m.textCase)),a.push({type:"text",templateHash:Gt,materialHash:vt||typeof H=="function"||H.match(/\[(.*?)\]/)?(gt,Lt,Y)=>xe+"-"+Qs(H,gt,Lt,Y):xe+"-"+z(H),cim:m,materialOverrides:null,colorLocked:!!o.colorLocked,effects:t,alignment:h,anchorPoint:{x:o.anchorPoint?o.anchorPoint.x:0,y:o.anchorPoint?o.anchorPoint.y:0},isAbsoluteAnchorPoint:o.anchorPointUnits!=="Relative",fontName:B,decoration:_,weight:b(M,i,"Weight",n,p.weight),style:b(M,i,"Size",n,p.style),size:b(M,i,"Size",n,S),angle:b(M,i,"Rotation",n,w),offsetX:b(M,i,"OffsetX",n,I),offsetY:b(M,i,"OffsetY",n,v),horizontalAlignment:qs(m.horizontalAlignment),verticalAlignment:js(m.verticalAlignment),text:H,color:C,outlineColor:L,outlineSize:O,backgroundColor:X,borderLineColor:ut,borderLineWidth:kt,referenceSize:l,sizeRatio:1,markerPlacement:e})}function ko(o,t,e,s,i,r,n,a,h,l,c,u,f,m){const _=i.symbol,p=_.symbolLayers;if(!p)return;if(m)return void Vs(o,t,e,s,i,n,r,a,h,l,c,u,f);let d=p.length;if($o(p))return void vo(o,t,e,s,i,p,r,n,a,h,c,u,f);const g=Hs.applyEffects(_.effects,i.geometry,l.geometryEngine);if(g)for(;d--;){const y=p[d];if(y&&y.enable!==!1)switch(y.type){case"CIMSolidFill":case"CIMSolidStroke":{const x=Hs.applyEffects(y.effects,g,l.geometryEngine),P=Wi(x);if(!P)continue;const M=o.anchorPointUnits!=="Relative",[S,w,I]=Ui(P,o.frame,o.size,o.anchorPoint,M),v=y.type==="CIMSolidFill",C={type:"sdf",geom:x,asFill:v},L=o.primitiveName,O=k(o.size)??10,X=k(o.rotation),ut=k(o.offsetX),kt=k(o.offsetY),vt=y.path,tt=y.primitiveName,It=j(v?pe(y):_e(y)),Gt=v?{r:0,g:0,b:0,a:0}:j(_e(y)),H=ls(y)??0;if(!v&&!H)break;let ft=!1,B="";for(const Y of r)Y.primitiveName!==tt&&Y.primitiveName!==L||(Y.value!==void 0?B+=`-${Y.primitiveName}-${Y.propertyName}-${JSON.stringify(Y.value)}`:Y.valueExpressionInfo&&(ft=!0));(V(t)&&typeof t=="function"||V(e)&&typeof e=="function")&&(ft=!0);const xe=JSON.stringify({...o,markerGraphics:null}),gt=z(JSON.stringify(C)+vt).toString(),Lt={type:"marker",templateHash:z(JSON.stringify(i)+JSON.stringify(y)+xe+B).toString(),materialHash:ft?()=>gt:gt,cim:C,materialOverrides:null,colorLocked:!!o.colorLocked,effects:t,scaleSymbolsProportionally:!!o.scaleSymbolsProportionally,alignment:c,anchorPoint:{x:w,y:I},isAbsoluteAnchorPoint:M,size:b(o.primitiveName,n,"Size",a,O),rotation:b(o.primitiveName,n,"Rotation",a,X),offsetX:b(o.primitiveName,n,"OffsetX",a,ut),offsetY:b(o.primitiveName,n,"OffsetY",a,kt),scaleX:1,frameHeight:f,rotateClockwise:!!o.rotateClockwise,referenceSize:u,sizeRatio:S,color:b(tt,n,"Color",a,It,Q),outlineColor:b(tt,n,"Color",a,Gt,Q),outlineWidth:b(tt,n,"Width",a,H),markerPlacement:e,animatedSymbolProperties:s,path:vt};h.push(Lt);break}default:Vs(o,t,e,s,i,n,r,a,h,l,c,u,f)}}}function vo(o,t,e,s,i,r,n,a,h,l,c,u,f){const m=i.geometry,_=r[0],p=r[1],d=Wi(m);if(!d)return;const g=o.anchorPointUnits!=="Relative",[y,x,P]=Ui(d,o.frame,o.size,o.anchorPoint,g),M={type:"sdf",geom:m,asFill:!0},S=o.primitiveName,w=k(o.size),I=k(o.rotation),v=k(o.offsetX),C=k(o.offsetY),L=p.path,O=p.primitiveName,X=_.primitiveName,ut=j(pe(p)),kt=j(_e(_)),vt=ls(_)??0;let tt=!1,It="";for(const B of n)B.primitiveName!==O&&B.primitiveName!==X&&B.primitiveName!==S||(B.value!==void 0?It+=`-${B.primitiveName}-${B.propertyName}-${JSON.stringify(B.value)}`:B.valueExpressionInfo&&(tt=!0));V(e)&&typeof e=="function"&&(tt=!0);const Gt=JSON.stringify({...o,markerGraphics:null}),H=z(JSON.stringify(M)+L).toString(),ft={type:"marker",templateHash:z(JSON.stringify(i)+JSON.stringify(p)+JSON.stringify(_)+Gt+It).toString(),materialHash:tt?()=>H:H,cim:M,materialOverrides:null,colorLocked:!!o.colorLocked,effects:t,scaleSymbolsProportionally:!!o.scaleSymbolsProportionally,alignment:c,anchorPoint:{x,y:P},isAbsoluteAnchorPoint:g,size:b(o.primitiveName,a,"Size",h,w),rotation:b(o.primitiveName,a,"Rotation",h,I),offsetX:b(o.primitiveName,a,"OffsetX",h,v),offsetY:b(o.primitiveName,a,"OffsetY",h,C),scaleX:1,frameHeight:f,rotateClockwise:!!o.rotateClockwise,referenceSize:u,sizeRatio:y,color:b(O,a,"Color",h,ut,Q),outlineColor:b(X,a,"Color",h,kt,Q),outlineWidth:b(X,a,"Width",h,vt),markerPlacement:e,path:L,animatedSymbolProperties:s};l.push(ft)}function Vs(o,t,e,s,i,r,n,a,h,l,c,u,f){const m=Io(o,i),_=["Rotation","OffsetX","OffsetY"],p=n.filter(C=>C.primitiveName!==o.primitiveName||!_.includes(C.propertyName));let d="";for(const C of n)C.value!==void 0&&(d+=`-${C.primitiveName}-${C.propertyName}-${JSON.stringify(C.value)}`);const[g,y,x]=at.getTextureAnchor(m,l),P=o.primitiveName,M=k(o.rotation),S=k(o.offsetX),w=k(o.offsetY),I=z(JSON.stringify(m)+d).toString(),v={type:"marker",templateHash:I,materialHash:p.length>0||V(t)&&typeof t=="function"?Pe(I,r,p,a):I,cim:m,materialOverrides:p,colorLocked:!!o.colorLocked,effects:t,scaleSymbolsProportionally:!!o.scaleSymbolsProportionally,alignment:c,anchorPoint:{x:g,y},isAbsoluteAnchorPoint:!1,size:k(o.size),rotation:b(P,r,"Rotation",a,M),offsetX:b(P,r,"OffsetX",a,S),offsetY:b(P,r,"OffsetY",a,w),color:{r:255,g:255,b:255,a:1},outlineColor:{r:0,g:0,b:0,a:0},outlineWidth:0,scaleX:1,frameHeight:f,rotateClockwise:!!o.rotateClockwise,referenceSize:u,sizeRatio:x/Ct(o.size),markerPlacement:e,animatedSymbolProperties:s,avoidSDFRasterization:!0};h.push(v)}function Io(o,t){return{type:o.type,enable:!0,name:o.name,colorLocked:o.colorLocked,primitiveName:o.primitiveName,anchorPoint:o.anchorPoint,anchorPointUnits:o.anchorPointUnits,offsetX:0,offsetY:0,rotateClockwise:o.rotateClockwise,rotation:0,size:o.size,billboardMode3D:o.billboardMode3D,depth3D:o.depth3D,frame:o.frame,markerGraphics:[t],scaleSymbolsProportionally:o.scaleSymbolsProportionally,respectFrame:o.respectFrame,clippingPath:o.clippingPath}}function ys(o){if(o&&o.indexOf("Level_")===0){const t=parseInt(o.substr(6),10);if(!isNaN(t))return t}return 0}function Q(o){if(!o||o.length===0)return null;const t=new Zs(o).toRgba();return{r:t[0],g:t[1],b:t[2],a:t[3]}}function b(o,t,e,s,i,r,n){if(o==null)return i;const a=t[o];if(a){const h=a[e];if(typeof h=="string"||typeof h=="number"||h instanceof Array)return r?r.call(null,h,n):h;if(h!=null&&h instanceof ge&&(s!=null&&s.geometryType))return(l,c,u)=>{let f=Rt(h,l,{$view:u},s.geometryType,c);return f!==null&&r&&(f=r.call(null,f,n)),f!==null?f:i}}return i}function Ps(o){return o&&o.charAt(0).toLowerCase()+o.substr(1)}function Lo(o,t,e,s){for(const i of t)if(i.valueExpressionInfo&&(s!=null&&s.geometryType)){const r=e[i.primitiveName]&&e[i.primitiveName][i.propertyName];r instanceof ge&&(i.fn=(n,a,h)=>Rt(r,n,{$view:h},s.geometryType,a))}return(i,r,n)=>{for(const h of t)h.fn&&(h.value=h.fn(i,r,n));const a=[];for(let h of o){const l=h==null?void 0:h.primitiveName;if(l){let c=!1;for(const u of t)if(u.primitiveName===l){const f=Ps(u.propertyName);u.value!=null&&u.value!==h[f]&&(c||(h=N(h),c=!0),h[f]=u.value)}}a.push(h)}return a}}function qi(o,t,e,s){const i=[];if(wt.findApplicableOverrides(o,t,i),o==null||i.length===0)return o;for(const r of i)if(r.valueExpressionInfo&&(s!=null&&s.geometryType)){const n=e[r.primitiveName]&&e[r.primitiveName][r.propertyName];n instanceof ge&&(r.fn=(a,h,l)=>Rt(n,a,{$view:l},s.geometryType,h))}return(r,n,a)=>{for(const c of i)c.fn&&(c.value=c.fn(r,n,a));const h=N(o),l=o.primitiveName;for(const c of i)if(c.primitiveName===l){const u=Ps(c.propertyName);c.value!=null&&c.value!==h[u]&&(h[u]=c.value)}return h}}function To(o,t,e,s){const i=[];if(wt.findApplicableOverrides(o,t,i),o==null||i.length===0)return o;for(const r of i)if(r.valueExpressionInfo&&(s!=null&&s.geometryType)){const n=e[r.primitiveName]&&e[r.primitiveName][r.propertyName];n instanceof ge&&(r.fn=(a,h,l)=>Rt(n,a,{$view:l},s.geometryType,h))}return(r,n,a)=>{for(const c of i)c.fn&&(c.value=c.fn(r,n,a));const h=N(o),l=o.primitiveName;for(const c of i)if(c.primitiveName===l){const u=Ps(c.propertyName);if(c.value!=null){const f=so(c.value,c.propertyName);f!==h[u]&&(h[u]=f)}}return h}}function Pe(o,t,e,s){for(const i of e)if(i.valueExpressionInfo&&(s!=null&&s.geometryType)){const r=t[i.primitiveName]&&t[i.primitiveName][i.propertyName];r instanceof ge&&(i.fn=(n,a,h)=>Rt(r,n,{$view:h},s.geometryType,a))}return(i,r,n)=>{for(const a of e)a.fn&&(a.value=a.fn(i,r,n));return z(o+wt.buildOverrideKey(e)).toString()}}function Fa(o,t){if(!t||t.length===0)return o;const e=N(o);return wt.applyOverrides(e,t),e}function K(o,t,e,s,i){let r=!1,n="";for(const a of o)a.primitiveName===t&&(a.value!==void 0?n+=`-${a.primitiveName}-${a.propertyName}-${JSON.stringify(a.value)}`:a.valueExpressionInfo&&(r=!0));return V(e)&&typeof e=="function"&&(r=!0),V(s)&&typeof s=="function"&&(r=!0),V(i)&&typeof i=="function"&&(r=!0),[r,n]}const $o=o=>o&&o.length===2&&o[0].enable&&o[1].enable&&o[0].type==="CIMSolidStroke"&&o[1].type==="CIMSolidFill"&&!o[0].effects&&!o[1].effects;export{G as O,Gi as S,Ta as V,On as W,Na as Z,Oi as a,fs as b,Rs as c,Oa as d,uo as e,co as f,bn as g,Tr as h,at as i,Ra as j,Hs as k,za as o,Fa as p,io as r,wt as s,Ei as t};
