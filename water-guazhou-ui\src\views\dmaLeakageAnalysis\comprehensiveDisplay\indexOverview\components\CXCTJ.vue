<template>
  <SLCard title="产销差统计">
    <template #title>
      <div class="card-header">
        <div class="left">
          <Icon icon="material-symbols:water-drop-outline" /><span>产销差统计</span>
        </div>
        <inline-form
          ref="refProSaleForm"
          style="width: auto"
          :config="psConfig"
        ></inline-form>
      </div>
    </template>
    <div
      ref="refDiv"
      class="chart-box"
    >
      <VChart
        ref="refChart"
        :option="state.barChartOption"
        :theme="useAppStore().isDark ? 'blackBackground' : 'whiteBackground'"
      ></VChart>
    </div>
  </SLCard>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { GetPartionProSaleDeltaStatistic } from '@/api/mapservice/dma'
import { useDetector } from '@/hooks/echarts'
import { useAppStore } from '@/store'

const state = reactive<{
  barChartOption: any
}>({
  barChartOption: null
})
const refProSaleForm = ref<IInlineFormIns>()
const psConfig = reactive<IFormConfig>({
  defaultValue: {
    dateType: 'quarter'
  },
  group: [
    {
      fields: [
        {
          type: 'radio-button',
          field: 'dateType',
          options: [
            { label: '季度', value: 'quarter' },
            { label: '年', value: 'year' }
          ],
          onChange: () => refreshData()
        }
      ]
    }
  ]
})
const refreshData = () => {
  const type = refProSaleForm.value?.dataForm.dateType
  GetPartionProSaleDeltaStatistic({
    type
  })
    .then(res => {
      const data = res.data.data || {}
      const x = type === 'year' ? data.x.map(item => item + '月') : data.x
      state.barChartOption = {
        legend: {
          left: 'right',
          top: 'top',
          type: 'scroll',
          textStyle: {
            color: useAppStore().isDark ? '#fff' : '#333',
            fontSize: 12
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: 10,
          right: 30,
          top: 50,
          bottom: 20,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: x || [],

          axisLabel: {
            show: true,
            textStyle: {
              // color: '#656b84' // 更改坐标轴文字颜色
              // fontSize: 14 //更改坐标轴文字大小
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        yAxis: [
          {
            position: 'left',
            type: 'value',
            name: 'm³',
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                // color: '#656b84' // 更改坐标轴文字颜色
                // fontSize: 14 //更改坐标轴文字大小
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              lineStyle: {
                type: 'dashed'
              }
            }
          },
          {
            type: 'value',
            name: '产销差（%）',

            axisLabel: {
              show: true,
              textStyle: {
                // color: '#656b84' // 更改坐标轴文字颜色
                // fontSize: 14 //更改坐标轴文字大小
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '供水量',
            type: 'bar',
            barWidth: 15,
            data: data.supply || []
          },
          {
            name: '售水量',
            type: 'bar',
            barWidth: 15,
            data: data.sale || []
          },
          {
            name: '产销差',
            type: 'line',
            yAxisIndex: 1,
            data: data.nrw || []
          }
        ]
      }
    })
    .catch(() => {
      //
    })
}
const detector = useDetector()
const refChart = ref()
const refDiv = ref()
onMounted(() => {
  refreshData()
  detector.listenToMush(refDiv.value, () => {
    refChart.value?.resize()
  })
})
</script>
<style lang="scss" scoped>
.chart-box {
  width: 100%;
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  word-break: keep-all;
  justify-content: space-between;
  width: 100%;
  .left {
    display: flex;
    align-items: center;
  }
  :deep(.el-form-item--default) {
    margin-bottom: 0;
  }
}
</style>
