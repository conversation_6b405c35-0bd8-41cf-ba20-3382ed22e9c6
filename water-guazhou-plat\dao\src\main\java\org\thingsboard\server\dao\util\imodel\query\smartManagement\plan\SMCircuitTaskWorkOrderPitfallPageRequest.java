package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskWorkOrderPitfallResponse;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderStagibleRequest;

import java.util.EnumSet;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus.*;

@Getter
@Setter
public class SMCircuitTaskWorkOrderPitfallPageRequest extends AdvancedPageableQueryEntity<SMCircuitTaskWorkOrderPitfallResponse, SMCircuitTaskWorkOrderPitfallPageRequest> implements WorkOrderStagibleRequest {
    // 状态区间 0-待处理 1-处理中 2-已处理
    private Integer statusStage;

    // 工单类型
    private String type;

    // 关键字 任务编号/事件编号/上报人员/事件地址
    private String keyword;

    // 处理人Id
    private String processUserId;


    public EnumSet<WorkOrderStatus> getStatusStage() {
        if (statusStage == null) {
            return null;
        }
        switch (statusStage) {
            case 0:
                return EnumSet.range(PENDING, ASSIGN);
            case 1:
                return EnumSet.range(RESOLVING, HANDOVER_REVIEW);
            case 2:
                return EnumSet.range(APPROVED, TERMINATED);
        }

        return null;
    }

}
