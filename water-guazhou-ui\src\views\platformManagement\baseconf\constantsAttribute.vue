<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getConstantsAttributeList
} from '@/api/platformManagement/constantsAttribute'
import { SLMessage } from '@/utils/Message'

const refSearch = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '类型', 
      field: 'type', 
      placeholder: '请输入类型',
      onChange: () => refreshData()
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '类型', prop: 'type' },
    { label: '键', prop: 'key' },
    { label: '值', prop: 'value' }
  ],
  dataList: [],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

// 刷新数据
const refreshData = async () => {
  try {
    const res = await getConstantsAttributeList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
    if (responseData.records && responseData.records.length > 0) {
      TableConfig.pagination.page = responseData.current || 1
    }
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.wrapper {
  padding: 20px;
}

.card-table {
  margin-top: 20px;
}
</style>