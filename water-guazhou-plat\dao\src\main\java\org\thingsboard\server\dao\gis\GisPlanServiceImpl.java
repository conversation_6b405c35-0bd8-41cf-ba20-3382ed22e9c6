package org.thingsboard.server.dao.gis;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.gis.GisPlan;
import org.thingsboard.server.dao.sql.gis.GisPlanRepository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class GisPlanServiceImpl implements GisPlanService {

    @Autowired
    private GisPlanRepository gisPlanRepository;

    @Override
    public void save(GisPlan entity) {
        entity.setCreateTime(new Date());
        gisPlanRepository.save(entity);
    }

    @Override
    public PageData<GisPlan> findList(int page, int size, String name, String type, User currentUser) {
        List<String> userIds = new ArrayList<>();
        userIds.add(UUIDConverter.fromTimeUUID(currentUser.getUuidId()));
        userIds.add("all");
        PageRequest pageRequest = new PageRequest(page - 1, size, new Sort(Sort.Direction.DESC, "createTime"));
        if (StringUtils.isNotBlank(type)) {
            Page<GisPlan> pageData = gisPlanRepository.findByNameLikeAndTypeAndUserIdIn("%" + name + "%", type, userIds, pageRequest);
            return new PageData<>(pageData.getTotalElements(), pageData.getContent());
        } else {
            Page<GisPlan> pageData = gisPlanRepository.findByNameLikeAndUserIdIn("%" + name + "%", userIds, pageRequest);
            return new PageData<>(pageData.getTotalElements(), pageData.getContent());
        }
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            gisPlanRepository.delete(id);
        }
    }
}
