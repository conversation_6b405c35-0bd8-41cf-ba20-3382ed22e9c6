package org.thingsboard.server.controller.smartManagement.maintain;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTaskItem;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemReportRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemSaveRequest;
import org.thingsboard.server.dao.maintain.SMMaintainTaskItemService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/sm/maintainTaskItem")
public class SMMaintainTaskItemController extends BaseController {
    @Autowired
    private SMMaintainTaskItemService service;


    @GetMapping
    public IPage<SMMaintainTaskItem> findAllConditional(SMMaintainTaskItemPageRequest request) {
        return service.findAllConditional(request);
    }

    // @PostMapping
    public SMMaintainTaskItem save(@RequestBody SMMaintainTaskItemSaveRequest req) {
        return service.save(req);
    }

    @PostMapping("/{id}/report")
    public boolean report(@RequestBody SMMaintainTaskItemReportRequest req, @PathVariable String id) {
        req.setTaskItemId(id);
        return service.report(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SMMaintainTaskItemSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}