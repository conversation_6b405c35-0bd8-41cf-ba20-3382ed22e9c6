package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBasePermissionConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BasePermissionConfiguration;
import org.thingsboard.server.dao.sql.base.BasePermissionConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BasePermissionConfigurationPageRequest;

/**
 * 平台管理-权限配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class BasePermissionConfigurationServiceImpl implements IBasePermissionConfigurationService {

    @Autowired
    private BasePermissionConfigurationMapper basePermissionConfigurationMapper;

    /**
     * 查询平台管理-权限配置
     *
     * @param id 平台管理-权限配置主键
     * @return 平台管理-权限配置
     */
    @Override
    public BasePermissionConfiguration selectBasePermissionConfigurationById(String id) {
        return basePermissionConfigurationMapper.selectBasePermissionConfigurationById(id);
    }

    /**
     * 查询平台管理-权限配置列表
     *
     * @param basePermissionConfiguration 平台管理-权限配置
     * @return 平台管理-权限配置
     */
    @Override
    public IPage<BasePermissionConfiguration> selectBasePermissionConfigurationList(BasePermissionConfigurationPageRequest basePermissionConfiguration) {
        return basePermissionConfigurationMapper.selectBasePermissionConfigurationList(basePermissionConfiguration);
    }

    /**
     * 新增平台管理-权限配置
     *
     * @param basePermissionConfiguration 平台管理-权限配置
     * @return 结果
     */
    @Override
    public int insertBasePermissionConfiguration(BasePermissionConfiguration basePermissionConfiguration) {
        basePermissionConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return basePermissionConfigurationMapper.insertBasePermissionConfiguration(basePermissionConfiguration);
    }

    /**
     * 修改平台管理-权限配置
     *
     * @param basePermissionConfiguration 平台管理-权限配置
     * @return 结果
     */
    @Override
    public int updateBasePermissionConfiguration(BasePermissionConfiguration basePermissionConfiguration) {
        return basePermissionConfigurationMapper.updateBasePermissionConfiguration(basePermissionConfiguration);
    }

    /**
     * 批量删除平台管理-权限配置
     *
     * @param ids 需要删除的平台管理-权限配置主键
     * @return 结果
     */
    @Override
    public int deleteBasePermissionConfigurationByIds(List<String> ids) {
        return basePermissionConfigurationMapper.deleteBasePermissionConfigurationByIds(ids);
    }

    /**
     * 删除平台管理-权限配置信息
     *
     * @param id 平台管理-权限配置主键
     * @return 结果
     */
    @Override
    public int deleteBasePermissionConfigurationById(String id) {
        return basePermissionConfigurationMapper.deleteBasePermissionConfigurationById(id);
    }
}
