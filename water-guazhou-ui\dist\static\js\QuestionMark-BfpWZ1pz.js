import{d as _,r as y,o as R,Q as T,g as z,n as C,q as D,i as I,_ as S}from"./index-r0dFAfgr.js";import{g as B}from"./MapView-DaoQedLH.js";import{w as h}from"./Point-WxyopZva.js";import{j as E,s as G,k as M,l as P,m as x,n as q,o as F}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as Q}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as g,i as $}from"./ToolHelper-BiiInOzB.js";import{e as j}from"./ViewHelper-BGCZjxXH.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./fieldconfig-Bk3o1wi7.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const at=_({__name:"QuestionMark",props:{view:{},telport:{}},setup(b){const i=b,u=y({curType:""}),t={},l=y({singleSelect:!0,select:(e,r)=>{l.selectList=r?[e]:[]},handleSelectChange:e=>{l.selectList=e},handleRowDbClick:async e=>{await j(i.view,e.extent)},dataList:[],columns:[{label:"名称",prop:"name"},{label:"创建日期",prop:"createDate"},{label:"状态",prop:"status"}],pagination:{refreshData:({page:e,size:r})=>{l.pagination.page=e,l.pagination.limit=r,L()},layout:"total, sizes, jumper"}}),k=y({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"添加疑问标识"},fields:[{type:"btn-group",label:"绘制工具：",btns:[{perm:!0,text:"",type:"default",size:"large",iconifyIcon:"ep:crop",click:()=>d("rectangle")},{perm:!0,text:"",type:"default",size:"large",iconifyIcon:"mdi:ellipse-outline",click:()=>d("ellipse")},{perm:!1,text:"",type:"default",size:"large",iconifyIcon:"mdi:arrow-top-right-bold-outline",click:()=>{console.log("a")}},{perm:!0,text:"",type:"default",size:"large",iconifyIcon:"mdi:chart-timeline-variant",click:()=>d("polyline")},{perm:!0,text:"",type:"default",size:"large",iconifyIcon:"material-symbols:text-fields",click:()=>d("text")},{perm:!0,text:"",type:"default",size:"large",iconifyIcon:"ep:delete",click:()=>A()}]},{type:"input",label:"标识名称：",field:"name",placeholder:"请输入当前标识名称"},{type:"textarea",field:"content",label:"标识内容：",placeholder:"请输入当前标识内容"},{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},type:"success",text:"保存疑问标识",click:()=>{}}]}]},{fieldset:{desc:"我的疑问标识"},fields:[{type:"table",style:{height:"400px"},config:l},{type:"btn-group",style:{marginTop:"15px"},btns:[{perm:!0,type:"danger",styles:{width:"100%"},text:"删除选定的疑问标识",click:()=>{}}]}]}]}),d=e=>{var r,o,s,c,a,n;i.view&&(u.curType=e,e=e==="text"?"point":e,g("crosshair"),(r=t.drawAction)==null||r.destroy(),(o=t.drawer)==null||o.destroy(),t.drawer=$(i.view),t.drawAction=(s=t.drawer)==null?void 0:s.create(e,{mode:["ellipse","polyline"].indexOf(e)!==-1?"freehand":"click"}),u.curType==="text"?(c=t.drawAction)==null||c.on("draw-complete",p=>{var m;if(t.vertices=p.vertices,p.vertices.length){const f=(m=i.view)==null?void 0:m.toScreen(new h({x:p.vertices[0][0],y:p.vertices[0][1],spatialReference:i.view.spatialReference}));t.textArea=E(f,i.telport),t.textArea.addEventListener("blur",v)}g("")}):((a=t.drawAction)==null||a.on(["vertex-add","cursor-update"],w),(n=t.drawAction)==null||n.on("draw-complete",p=>{w(p),g("")})))},v=()=>{var r,o,s,c;if(!((r=t.vertices)!=null&&r.length))return;const e=((o=t.textArea)==null?void 0:o.value)||"";if(t.textArea){if(!i.telport)return;const a=document.querySelector(i.telport);t.textArea.removeEventListener("blur",v),a==null||a.removeChild(t.textArea)}if(e){const a=new B({geometry:new h({x:t.vertices[0][0],y:t.vertices[0][1],spatialReference:(s=i.view)==null?void 0:s.spatialReference}),symbol:G("text",{text:e,textColor:"#ff0000",yOffset:-8})});(c=t.graphicsLayer)==null||c.add(a)}},w=e=>{var s,c,a,n,p,m,f;const r=u.curType;console.log(e);const o=r==="ellipse"?M(e.vertices,(s=i.view)==null?void 0:s.spatialReference):r==="rectangle"?P(e):r==="polyline"?x(e.vertices,(c=i.view)==null?void 0:c.spatialReference):r==="polygon"?e.vertices.length<3?x(e.vertices,(a=i.view)==null?void 0:a.spatialReference):q(e.vertices,(n=i.view)==null?void 0:n.spatialReference):F(e.vertices,(p=i.view)==null?void 0:p.spatialReference);(m=t.graphicsLayer)==null||m.removeAll(),o&&((f=t.graphicsLayer)==null||f.add(o)),t.graphics=o},A=()=>{var e;(e=t.graphicsLayer)==null||e.removeAll(),t.graphics=void 0},L=()=>{};return R(()=>{i.view&&(t.graphicsLayer=Q(i.view,{id:"question-mark-layer",title:"疑问标识"}))}),T(()=>{var e,r,o;t.graphicsLayer&&((e=i.view)==null||e.map.remove(t.graphicsLayer)),(r=t.drawAction)==null||r.destroy(),(o=t.drawer)==null||o.destroy()}),(e,r)=>{const o=S;return z(),C("div",null,[D(o,{ref:"refForm",config:I(k)},null,8,["config"])])}}});export{at as default};
