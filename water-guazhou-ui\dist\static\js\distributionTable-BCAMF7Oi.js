import{_ as w}from"./index-C9hz-UZb.js";import{d as S,M as N,a6 as V,c as u,r as k,am as A,bF as f,s as B,o as M,ay as q,g as E,n as P,q as c,i,F as v,G as j,bh as z,p as F,j as G,bB as L,dt as R,al as $,C as W}from"./index-r0dFAfgr.js";import{_ as X}from"./Search-NSrhrIa_.js";import{l as H}from"./echart-DxEZmJvB.js";import{a as J}from"./flowMonitoring-DtJlPj0G.js";import{f as K}from"./DateFormatter-Bm9a68Ax.js";const Q={class:"view overlay-y"},U=S({__name:"distributionTable",props:{stationName:{},stationId:{}},setup(C){const{$messageWarning:x}=N(),D=V(),y=u(!1),_=u(),r=C,b=u(),g=u(),l=k({chartOption:null,data:{},columns:[[{label:"站点名称",prop:"name",value:"无数据"},{label:"站点类型",prop:"type",value:"无数据"}],[{label:"站点地址",prop:"address",value:"无数据"},{label:"坐标定位",prop:"location",value:"无数据"}],[{label:"更新时间",prop:"createTime",value:"无数据",formatter:t=>K(t.createTime)},{label:"备注",prop:"remark",value:"无数据"}]]});A(()=>r.stationId,()=>{h()});const I=k({defaultParams:{date:[f().add(-7,"day").format(),f().format()]},filters:[{type:"daterange",label:"日期",field:"date",clearable:!1},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:B($),click:()=>{r.stationId?h():x("请选择监测点")}}]}]}),O=t=>{var s,o,n;const e=H();e.series=[];for(const a in t){const p=(s=t[a])==null?void 0:s.map(d=>d.value),m=(o=t[a])==null?void 0:o.map(d=>d.ts.substring(d.ts.length-2,20)+":00"),T={name:a,smooth:!0,data:p,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.series.push(T),e.yAxis[0].name="流量(m³)",e.xAxis.data=m}(n=_.value)==null||n.clear(),L(()=>{D.listenTo(g.value,()=>{var a;l.chartOption=e,(a=_.value)==null||a.resize()})})},h=async()=>{var p,m;y.value=!0;const t=((p=b.value)==null?void 0:p.queryParams)||{},[e,s]=t.date,o={start:e?f(e).startOf("day").valueOf():null,end:s?f(s).endOf("day").valueOf():null,stationId:r.stationId},a=(m=(await J(o)).data)==null?void 0:m.data;l.data=a==null?void 0:a.stationInfo,O(a==null?void 0:a.lineData),y.value=!1};return M(async()=>{r.stationId&&await h()}),(t,e)=>{const s=X,o=R,n=w,a=q("VChart");return E(),P("div",Q,[c(s,{ref_key:"cardSearch",ref:b,config:i(I)},null,8,["config"]),c(n,{class:"card-table overlay-y",title:" "},{title:v(()=>[j(z(r.stationName),1)]),default:v(()=>[c(o,{columns:i(l).columns,data:i(l).data},null,8,["columns","data"])]),_:1}),c(n,{class:"card-ehcarts",title:"图表分析"},{default:v(()=>[F("div",{ref_key:"agriEcoDev",ref:g,class:"chart-box"},[c(a,{ref_key:"refChart",ref:_,theme:i(G)().isDark?"dark":"",option:i(l).chartOption},null,8,["theme","option"])],512)]),_:1})])}}}),oa=W(U,[["__scopeId","data-v-6c9a2e57"]]);export{oa as default};
