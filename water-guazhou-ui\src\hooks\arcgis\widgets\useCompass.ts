import Compass from '@arcgis/core/widgets/Compass.js'

export const useCompass = () => {
  let compass: __esri.Compass | undefined
  const init = (view: __esri.MapView, widgetPosition?: string) => {
    const compass = new Compass({
      view
    })
    view.ui?.add(compass, widgetPosition || 'top-left')
    return compass
  }
  const destroy = () => {
    compass?.destroy()
  }
  onUnmounted(() => {
    destroy()
  })
  return {
    init
  }
}

export default useCompass
