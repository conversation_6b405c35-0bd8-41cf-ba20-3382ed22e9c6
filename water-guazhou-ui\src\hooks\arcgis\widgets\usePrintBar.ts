import Expand from '@arcgis/core/widgets/Expand.js'
import Print from '@arcgis/core/widgets/Print.js'

export const usePrintBar = () => {
  let print: __esri.Print | undefined
  let prExpand: __esri.Expand | undefined
  const init = (
    view: __esri.MapView,
    url?: string,
    widgetPosition?: string
  ) => {
    print = new Print({
      view,
      printServiceUrl: url
    })
    prExpand = new Expand({
      view,
      content: print,
      expandTooltip: '打印'
    }) as __esri.Expand
    view.ui?.add(prExpand, widgetPosition || 'top-right')
    return prExpand
  }
  const destroy = () => {
    print?.destroy()
    prExpand?.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init
  }
}
export default usePrintBar
