package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class LiquidatedDamagesReduceReportDTO {

    private String orgName;

    private String meterBookCode;

    private String meterBookName;

    private String userCode;

    private String userName;

    private String ym;

    /**
     * 应收水费
     */
    private BigDecimal receivableMoney;

    /**
     * 应收违约金
     */
    private BigDecimal receivablePenalty;

    /**
     * 已收违约金
     */
    private BigDecimal receivedPenalty;

    /**
     * 已减免/暂缓违约金
     */
    private BigDecimal reducePenalty;

}
