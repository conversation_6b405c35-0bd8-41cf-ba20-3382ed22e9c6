import{_ as z}from"./TreeBox-DDD2iwoR.js";import{d as F,M as N,c as y,s as V,x as o,r as _,a8 as k,a9 as D,S as w,o as O,g as S,h as C,F as v,q as x,i as p,b6 as q,b7 as M}from"./index-r0dFAfgr.js";import{_ as R}from"./CardTable-rdWOL4_6.js";import{_ as Y}from"./CardSearch-CB_HNR-Q.js";import{_ as B}from"./index-BJ-QPYom.js";import{I as u}from"./common-CvK_P_ao.js";import{j as W,k as $,g as A,l as J,c as U}from"./equipmentManage-DuoY00aj.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const ie=F({__name:"index",setup(G){const{$btnPerms:h}=N(),m=y(),b=y(),L=y(new Date().toString()),E=y({filters:[{label:"设备编码",field:"serialId",type:"input"},{label:"设备名称",field:"name",type:"input"},{label:"设备型号",field:"model",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:u.QUERY,click:()=>c()},{type:"default",perm:!0,text:"重置",svgIcon:V(M),click:()=>{var e;(e=b.value)==null||e.resetForm(),c()}},{perm:!0,text:"新建",icon:u.ADD,type:"success",click:()=>T("新建")},{perm:!0,text:"批量删除",icon:u.DELETE,type:"danger",click:()=>{var e;(e=l.selectList)!=null&&e.length?I():o.warning("请选中需要删除的设备属性")}}]}]}),l=_({defaultExpandAll:!0,indexVisible:!0,selectList:[],handleSelectChange:e=>{l.selectList=e},columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"linkedType"},{label:"设备标识",prop:"label"},{label:"计量单位",prop:"unit"}],operationWidth:"200px",operations:[{type:"primary",text:"编辑",icon:u.EDIT,perm:h("RoleManageEdit"),click:e=>P(e)},{type:"danger",text:"删除",perm:h("RoleManageDelete"),icon:u.DELETE,click:e=>I(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:a})=>{l.pagination.page=e,l.pagination.limit=a,c()}}}),i=_({title:"新增",labelWidth:"120px",submit:e=>{e.serialId=r.prepend+e.serialIdNum;for(const t in l.dataList)if(e.serialId===l.dataList[t].serialId&&e.serialId!==r.serialId){o.warning("设备编码重复");return}const a={};for(const t in e)t.indexOf("autoField.")!==-1&&(a[t]=e[t]);e.autoField=JSON.stringify(a),W(e).then(t=>{var n;t.data.code===500?o.success(t.data.message):((n=m.value)==null||n.closeDrawer(),o.success("操作成功"),c())}).catch(t=>{o.warning(t)})},defaultValue:{},group:[{fields:[{text:"基本信息",type:"divider"},{xl:24,type:"select-tree",label:"所属类别",field:"typeId",checkStrictly:!0,options:k(()=>D(s.data)),readonly:!0},{xl:24,type:"hint",text:"注：编码规则(长度14)>=12(级别1)+001(级别2)+001(级别3)+000000(设备编码)"},{xl:8,type:"input",label:"设备编码",field:"serialIdNum",prepend:k(()=>r.prepend),rules:[{required:!0,message:"请输入设备编码"},{min:6,max:6,message:"编码为6位"}]},{xl:8,type:"input",label:"设备名称",field:"name",rules:[{required:!0,message:"请输入设备名称"}]},{xl:8,type:"input",label:"设备型号",field:"model",rules:[{required:!0,message:"请输入设备型号"}]},{xl:8,type:"input",label:"设备标识",field:"label"},{xl:8,type:"number",label:"使用年限",field:"useYear",min:0},{xl:8,type:"number",label:"保养周期(天)",labelWidth:"100px",field:"maintenanceCycle",min:0},{xl:8,type:"input",label:"计量单位",field:"unit"},{xl:8,type:"number",label:"最小在库量",field:"minStock",min:0},{xl:16,type:"textarea",label:"备注",field:"remark"},{text:"自定义属性",type:"divider"},{text:"上传文件",type:"divider"},{type:"file",label:"设备文件",field:"files"},{type:"image",label:"设备图像",field:"images"}]}]}),s=_({title:" ",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{s.currentProject=e,c()}}),T=e=>{var a;if(!s.currentProject.id){o.warning("请选中设备类型");return}if(s.currentProject.level!=="3"){o.warning("请选择末级，分组无法添加");return}i.group[0].fields.length>16&&i.group[0].fields.splice(13,i.group[0].fields.length-16),r.getCustomizeValue(s.currentProject.serialId),r.prepend=s.currentProject.serialId.slice(0,8),i.title=e,i.defaultValue={typeId:s.currentProject.id||"",useYear:"0",maintenanceCycle:"0",minStock:"0"},(a=m.value)==null||a.openDrawer()},P=e=>{var t;i.title="编辑",r.serialId=e.serialId,r.prepend=e.serialId.slice(0,8),e.serialIdNum=e.serialId.slice(8,14),i.group[0].fields.length>16&&i.group[0].fields.splice(13,i.group[0].fields.length-16),r.getCustomizeValue(e.typeSerialId);const a=JSON.parse(e.autoField);i.defaultValue={useYear:"0",maintenanceCycle:"0",minStock:"0",...e||{},...a},(t=m.value)==null||t.openDrawer()},I=e=>{w("确定删除选中的设备?","删除提示").then(()=>{var t;let a=[];e?a=[e.id]:a=((t=l==null?void 0:l.selectList)==null?void 0:t.map(n=>n.id))??[],$(a).then(()=>{o.success("删除成功"),c()})})};function j(){U().then(e=>{s.data=D(e.data.data||[]),c()})}const r=_({prepend:"",serialId:"",customize:[],getCustomizeValue:e=>{r.customize=[],A(e).then(a=>{(a.data.data||[]).forEach(n=>{const d={xl:8,type:"input",label:n.name,field:"autoField."+n.code};r.customize.push(d)}),i.group[0].fields.splice(13,0,...r.customize)})}}),c=async()=>{var a;const e={size:l.pagination.limit,page:l.pagination.page,typeId:s.currentProject.id||"",name:"",model:"",serialId:"",...(a=b.value)==null?void 0:a.queryParams};J(e).then(t=>{var n,d,f,g;l.dataList=((d=(n=t==null?void 0:t.data)==null?void 0:n.data)==null?void 0:d.data)||[],l.pagination.total=((g=(f=t==null?void 0:t.data)==null?void 0:f.data)==null?void 0:g.total)||0})};return O(async()=>{j()}),(e,a)=>{const t=B,n=Y,d=R,f=q,g=z;return S(),C(g,null,{tree:v(()=>[x(t,{"tree-data":p(s)},null,8,["tree-data"])]),default:v(()=>[x(n,{ref_key:"refSearch",ref:b,config:p(E)},null,8,["config"]),x(d,{config:p(l),class:"card-table"},null,8,["config"]),(S(),C(f,{key:p(L),ref_key:"refForm",ref:m,config:p(i)},null,8,["config"]))]),_:1})}}});export{ie as default};
