package org.thingsboard.server.dao.smartService.call;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.ExtensionStatusDTO;
import org.thingsboard.server.dao.model.sql.smartService.call.CallExtensionStatus;
import org.thingsboard.server.dao.sql.smartService.call.CallExtensionStatusMapper;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class CallExtensionStatusServiceImpl implements CallExtensionStatusService {
    @Autowired
    private CallExtensionStatusMapper callExtensionStatusMapper;

    @Override
    public List<ExtensionStatusDTO> getList(Long startTime, Long endTime, String tenantId) {

        List<ExtensionStatusDTO> blacklists = callExtensionStatusMapper.getList(startTime, endTime, tenantId);

        return blacklists;

    }

    @Override
    public PageData<CallExtensionStatus> getDetail(String seatsId, int page, int size) {
        List<CallExtensionStatus> callExtensionStatuses = callExtensionStatusMapper.getDetail(seatsId, page, size);

        int total = callExtensionStatusMapper.getDetailCount(seatsId);

        return new PageData<>(total, callExtensionStatuses);
    }

}
