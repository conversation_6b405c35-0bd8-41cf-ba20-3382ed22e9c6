package org.thingsboard.server.controller.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.store.StoreInRecordDetail;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordDetailSaveRequest;
import org.thingsboard.server.dao.store.StoreInRecordDetailService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/StoreInRecordDetail")
public class StoreInRecordDetailController extends BaseController {
    @Autowired
    private StoreInRecordDetailService service;


    @GetMapping
    public IPage<StoreInRecordDetail> findAllConditional(StoreInRecordDetailPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public List<StoreInRecordDetail> save(@RequestBody List<StoreInRecordDetailSaveRequest> req) throws ThingsboardException {
        return service.save(req, null);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody StoreInRecordDetailSaveRequest req, @PathVariable String id) {
        return service.update(req.update(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}