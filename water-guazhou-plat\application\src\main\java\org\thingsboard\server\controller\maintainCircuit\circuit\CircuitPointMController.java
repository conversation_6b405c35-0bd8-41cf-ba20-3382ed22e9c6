package org.thingsboard.server.controller.maintainCircuit.circuit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintainCircuit.circuit.CircuitPointMService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitPointM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@RestController
@RequestMapping("api/circuit/point/m")
public class CircuitPointMController extends BaseController {

    @Autowired
    private CircuitPointMService circuitPointMService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String name,
                                 int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(circuitPointMService.getList(name, page, size, tenantId));
    }

    @GetMapping("detail/{mainId}")
    public IstarResponse getDetail(@PathVariable String mainId) {
        return IstarResponse.ok(circuitPointMService.getDetail(mainId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody CircuitPointM circuitPointM) throws ThingsboardException {
        // 审核过不许修改
        circuitPointM.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        circuitPointM.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(circuitPointMService.save(circuitPointM));
    }


    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return circuitPointMService.delete(ids);
    }
}
