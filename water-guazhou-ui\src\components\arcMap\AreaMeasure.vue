<template>
  <Form ref="refForm" :config="FormConfig"></Form>
</template>
<script lang="ts" setup>
import { calcArea, getGraphicLayer, getSubLayerIds } from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import { queryLayerClassName } from '@/api/mapservice';
import { useSketch } from '@/hooks/arcgis';

const props = defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();
const state = reactive<{
  measuring: boolean;
  disabled: boolean;
}>({
  measuring: false,
  disabled: true
});
const staticState: {
  sketch?: __esri.SketchViewModel;
  queryGeometry?: __esri.Geometry;
  graphicsLayer?: __esri.GraphicsLayer;
} = {};
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  labelWidth: 60,
  group: [
    {
      fields: [
        {
          type: 'text',
          field: 'area',
          label: '面积',
          style: {
            marginRight: '12px'
          },
          extraFormItem: [
            {
              type: 'select',
              field: 'unit',
              width: '100px',
              options: [
                // { label: '英亩', value: 'acres' },
                { label: '公顷', value: 'hectares' },
                // { label: '平方英尺', value: 'square-feet' },
                { label: '平方米', value: 'square-meters' },
                // { label: '平方码', value: 'square-yards' },
                { label: '平方公里', value: 'square-kilometers' }
                // { label: '平方英里', value: 'square-miles' }
              ],
              onChange: () => resolveMeasure()
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '新测量',
              type: 'default',
              loading: () => state.measuring,
              disabled: () => state.disabled,
              click: () => startMeasure()
            }
          ]
        }
      ]
    }
  ],
  gutter: 12,
  defaultValue: {
    unit: 'square-meters'
  }
});
const { initSketch, destroySketch } = useSketch();

const resolveMeasure = async () => {
  if (!staticState.queryGeometry || !refForm.value) return;
  state.measuring = true;
  try {
    const res = calcArea(
      (staticState.queryGeometry as __esri.Polygon).rings[0],
      refForm.value.dataForm.unit || 'square-meters',
      props.view?.spatialReference
    );
    refForm.value.dataForm.area = res.toFixed(2);
  } catch (error) {
    console.dir(error);
  }
  state.measuring = false;
};
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    staticState.queryGeometry = result.graphics[0]?.geometry;
    resolveMeasure();
  }
};
const startMeasure = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.sketch?.create('polygon');
};
const initPipeLayerOption = async () => {
  if (!props.view) return;
  const sublayers = getSubLayerIds(props.view);
  const layersres = await queryLayerClassName(sublayers);
  const layers = layersres.data?.result?.rows || [];
  const pipeLayerOption: any[] = [];
  layers.map((item) => {
    if (
      item.geometrytype === 'esriGeometryPolyline' ||
      item.layername.indexOf('立管') > -1
    ) {
      pipeLayerOption?.push({
        label: item.layername,
        value: item.layerid,
        id: item.layerid,
        data: item
      });
    }
  });
  if (!pipeLayerOption.length) {
    SLMessage.error('未加载管线服务,功能不可用');
    return;
  }
  state.disabled = false;

  const field = FormConfig.group[0].fields[0] as IFormSelect;
  field && (field.options = pipeLayerOption);
  refForm.value &&
    (refForm.value.dataForm.pipeLayer = pipeLayerOption[0]?.value);
};
onMounted(async () => {
  if (!props.view) return;
  initPipeLayerOption();
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'pipe-length',
    title: '管线面积测量'
  });
  staticState.sketch = initSketch(props.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  });
});
onBeforeUnmount(() => {
  destroySketch();
  staticState.graphicsLayer &&
    props.view?.map.remove(staticState.graphicsLayer);
});
</script>
<style lang="scss" scoped></style>
