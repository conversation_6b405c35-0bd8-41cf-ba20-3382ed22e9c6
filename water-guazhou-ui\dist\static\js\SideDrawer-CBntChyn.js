import{d as m,g as l,n as d,q as h,i,cs as _,aw as v,C as b,j as f,a8 as B,p as o,ax as a,G as V,bh as g,h as k,an as S,av as C}from"./index-r0dFAfgr.js";const $=m({__name:"FolderBtn",props:{direction:{},collapsed:{type:Boolean},barPosition:{}},emits:["collapse"],setup(c,{emit:n}){const r=n;return(s,t)=>(l(),d("div",{class:v(["control-fold-btn",["control-fold-btn-"+(s.direction||"ltr"),"control-fold-btn-"+(s.barPosition||"center")]]),onClick:t[0]||(t[0]=p=>r("collapse"))},[h(i(_),{icon:s.collapsed?"ep:caret-right":"ep:caret-left"},null,8,["icon"])],2))}}),D=b($,[["__scopeId","data-v-938227ba"]]),N={key:0,class:"right-drawer"},P={class:"right-drawer-title"},I={class:"title-container"},W={class:"title-wrapper"},z={class:"title-icon"},F={class:"title-text"},j={class:"right-drawer-content overlay-y"},q=m({__name:"SideDrawer",props:{hideBar:{type:Boolean},direction:{},modelValue:{type:Boolean},theme:{},barPosition:{},absolute:{type:Boolean},width:{},title:{},minWidth:{},padding:{}},emits:["collapse"],setup(c,{emit:n}){const r=f(),s=n,t=c,p=B(()=>({"--width":(t.width||350)+"px","--collapsedWidth":-(t.width||350)+(t.modelValue?0:t.minWidth||0)+"px"})),y=()=>{s("collapse",!t.modelValue)};return(e,u)=>{const w=D;return l(),d("div",{class:v(["layout-drawer",["layout-drawer-"+(e.direction||"ltr"),e.modelValue===!0?"":"collapsed",e.theme||(i(r).isDark?"dark":"light"),e.absolute?"absolute":""]]),style:C(i(p))},[e.title?(l(),d("div",N,[o("div",P,[o("div",I,[o("div",W,[o("div",z,[a(e.$slots,"title-icon",{},()=>[h(i(_),{icon:"ep:monitor"})],!0)]),o("div",F,[a(e.$slots,"title",{},()=>[V(g(e.title),1)],!0)]),u[0]||(u[0]=o("div",{class:"title-indicator"},null,-1))])])]),o("div",j,[a(e.$slots,"default",{},void 0,!0)])])):a(e.$slots,"default",{key:1},void 0,!0),e.hideBar!==!0?(l(),k(w,{key:2,direction:e.direction,collapsed:e.modelValue,"bar-position":e.barPosition,onCollapse:y},null,8,["direction","collapsed","bar-position"])):S("",!0)],6)}}}),E=b(q,[["__scopeId","data-v-b359c055"]]);export{E as S};
