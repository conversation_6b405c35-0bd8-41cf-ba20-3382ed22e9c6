package org.thingsboard.server.dao.feignService;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.dao.model.sql.AppType;

import java.util.List;

@Component
@FeignClient("base-service")
public interface AppTypeApi {

    @GetMapping("api/app/type/{id}")
    AppType findById(@PathVariable String id);

    @GetMapping("api/app/type/list")
    List<AppType> findAllList();

    @PostMapping("api/app/type")
    AppType save(@RequestBody AppType appType);

    @PutMapping("api/app/type")
    AppType update(@RequestBody AppType appType);

    @DeleteMapping("api/app/type/{id}")
    AppType delete(@PathVariable String id);
}
