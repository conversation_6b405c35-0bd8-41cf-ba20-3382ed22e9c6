# ArcGIS World Imagery 底图集成说明

## 概述

本次更新将ArcGIS Online的World Imagery服务集成到系统中，作为新的底图选项。该服务提供高质量的卫星影像数据，可以作为天地图影像的替代方案。

## 主要更改

### 1. 新增图层创建函数

**文件位置：**
- `src/hooks/arcgis/useLayer.ts`
- `src/utils/arcgis/layers.ts`

**新增函数：**
```typescript
const createArcGISImageryLayer = (params?: {
  color?: any;
  filter?: string;
}) => {
  const tiledLayer = new TintLayer({
    urlTemplate: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{level}/{row}/{col}',
    subDomains: [],
    color: params?.color,
    filter: params?.filter
  });
  return tiledLayer;
};
```

### 2. 更新类型定义

**文件位置：** `src/global.d.ts`

**更新内容：**
- 在 `IFormGisConfig` 类型中添加 `'arcgis_imagery'` 选项
- 在 `IGISCONFIG` 类型中添加 `'arcgis_imagery'` 选项

### 3. 修改地图初始化逻辑

**文件位置：** `src/utils/arcgis/utils/ViewHelper.ts`

**更新内容：**
- 添加对 `arcgis_imagery` 底图类型的支持
- 根据底图类型选择不同的图层创建方法

### 4. 更新底图切换组件

**文件位置：** `src/hooks/arcgis/widgets/useBasemapGallary.ts`

**更新内容：**
- 添加ArcGIS World Imagery作为新的底图选项
- 更新天地图影像的标题为"天地图影像"以区分

### 5. 更新用户主题设置

**文件位置：** `src/views/accountManage/components/ThemeInfo.vue`

**更新内容：**
- 在地图底图选项中添加"ArcGIS影像"选项

### 6. 更新全局配置

**文件位置：** `public/config.js`

**更新内容：**
- 将默认底图设置为 `'arcgis_imagery'`
- 更新表单默认配置

## 使用方法

### 1. 通过配置设置默认底图

在 `public/config.js` 中修改：
```javascript
GIS_CONFIG: {
  gisDefaultBaseMap: 'arcgis_imagery', // 设置为ArcGIS World Imagery
  // ... 其他配置
}
```

### 2. 通过用户界面切换底图

用户可以通过以下方式切换底图：
- 地图界面右上角的底图切换按钮
- 用户账户管理 → 主题信息 → 地图底图设置

### 3. 通过代码动态切换

```typescript
// 在组件中使用
const { createArcGISImageryLayer } = useLayer();
const arcgisLayer = createArcGISImageryLayer();
```

## 底图选项对比

| 底图类型 | 服务提供商 | 特点 | 适用场景 |
|---------|-----------|------|----------|
| `vec_w` | 天地图 | 矢量底图，加载快 | 一般地图展示 |
| `img_w` | 天地图 | 卫星影像，中文标注 | 需要中文标注的影像图 |
| `arcgis_imagery` | ArcGIS Online | 高质量卫星影像 | 需要高质量影像的场景 |

## 注意事项

1. **网络访问**：ArcGIS World Imagery服务需要能够访问 `https://services.arcgisonline.com`
2. **性能考虑**：ArcGIS服务可能在某些网络环境下加载较慢
3. **标注服务**：ArcGIS World Imagery不包含中文标注，如需标注可叠加天地图注记图层
4. **坐标系**：服务使用Web墨卡托投影（EPSG:3857）

## 兼容性

- ✅ ArcGIS地图组件
- ✅ L7地图组件（大屏）
- ✅ 用户主题设置
- ✅ 底图切换工具
- ✅ 表单地图组件

## 回滚方案

如需回滚到天地图影像，只需将配置中的 `gisDefaultBaseMap` 改回 `'img_w'` 即可。 