<!-- 孤立点检查 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'孤立点检查'"
    @map-loaded="onMapLoaded"
    @detail-refreshed="state.loading = false"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { getSubLayerIds } from '@/utils/MapHelper'
import { IFormIns } from '@/components/type'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { CheckIsolatedPoint } from '@/api/mapservice/pipeCheck'

const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerInfos: any[]
  layerIds: any[]
  curType: 'ellipse' | 'rectangle' | 'polygon' | ''
}>({
  tabs: [],
  curType: '',
  layerInfos: [],
  layerIds: [],
  loading: false
})
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
  drawer?: __esri.Draw
  drawAction?: __esri.DrawAction
  queryParams: {
    geometry?: __esri.Geometry
    where?: string
  }
} = {
  queryParams: {
    geometry: undefined,
    where: '1=1'
  }
}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'layer',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: true,
          label: '选择图层',
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value',
          handleCheckChange: (data, isChecked) => {
            if (isChecked) {
              refForm.value && (refForm.value.dataForm.layerid = [data.value])
            }
          }
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: () => (state.loading ? '正在检查，过程稍长，请耐心等待！' : '检查'),
              styles: {
                width: '100%'
              },
              loading: () => state.loading,
              click: () => startQuery()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    length: 1
  }
})

const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
  const field = FormConfig.group.find(item => item.id === 'layer')
    ?.fields[0] as IFormTree
  const points = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPoint')
    .map(item => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      }
    })
  // const lines = state.layerInfos
  //   .filter(item => item.geometrytype === 'esriGeometryPolyline')
  //   .map(item => {
  //     return {
  //       label: item.layername,
  //       value: item.layerid,
  //       data: item
  //     }
  //   })
  field
    && (field.options = [
      { label: '管点类', value: -1, children: points, disabled: true }
      // { label: '管线类', value: -2, children: lines,disabled: true }
    ])
  if (points.length === 1) {
    refForm.value
      && (refForm.value.dataForm.layerid = points.map(item => item.value))
  }
}
const startQuery = async () => {
  SLMessage.info('正在检查，请稍候...')
  const layerid = refForm.value?.dataForm.layerid[0]
  if (layerid === undefined) {
    SLMessage.warning('请先选择一个图层')
    return
  }
  state.loading = true
  try {
    state.loading = true
    state.tabs.length = 0
    const layername = state.layerInfos.find(
      item => item.layerid === layerid
    )?.layername
    const res = await CheckIsolatedPoint({
      layer: layername
    })
    if (!res.data.result?.length) {
      SLMessage.success('没有相关数据')
    }
    const tabs = [
      {
        label: layername,
        name: layername,
        data: res.data.result,
        layerid
      }
    ]
    await refMap.value?.refreshDetail(tabs)
  } catch (error) {
    console.log(error)
    state.loading = false
    SLMessage.error('检查失败')
  }
}
const onMapLoaded = view => {
  staticState.view = view
  getLayerInfo()
}
onBeforeUnmount(() => {
  staticState.graphicsLayer?.removeAll()
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
})
</script>
<style lang="scss" scoped></style>
