<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionDesignAmendMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
        code,
        design_code,
        type,
        remark,
        attachments,
        creator,
        create_time,
        tenant_id<!--@sql from so_construction_design_amend -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesignAmend">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="design_code" property="designCode"/>
        <result column="type" property="type"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_construction_design_amend
        <where>
            <if test="designCode != null and designCode != ''">
                design_code = #{designCode}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time
    </select>

    <update id="update">
        update so_construction_design_amend
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments}
            </if>
        </set>
        where id=#{id}
    </update>

    <update id="updateFully">
        update so_construction_design_amend
        set type = #{type},
        remark = #{remark},
        attachments = #{attachments}
        where id = #{id}
    </update>
</mapper>