package org.thingsboard.server.dao.maintain;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskAssignRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskSaveRequest;

import java.util.List;

public interface SMMaintainTaskService {
    /**
     * 分页条件查询养护管理任务
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SMMaintainTask> findAllConditional(SMMaintainTaskPageRequest request);

    /**
     * 保存养护管理任务
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SMMaintainTask save(SMMaintainTaskSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SMMaintainTask entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 分派任务
     *
     * @param req 详细信息
     * @return 是否成功
     */
    boolean assign(SMMaintainTaskAssignRequest req);

    /**
     * 完成任务
     *
     * @param req 详细信息
     * @return 是否成功
     */
    boolean complete(SMMaintainTaskCompleteRequest req);

    /**
     * 获取指定任务状态的统计信息，其中
     * {@link GeneralTaskStatusStatistic#getTotal() 任务总数}用于查看用户总共接收了多少任务，
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}用于查看处于此状态的任务有多少个，
     * {@link GeneralTaskStatusStatistic#getPercent() 指定状态任务数}为
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}/{@link GeneralTaskStatusStatistic#getTotal() 任务总数}
     * 计算而出的结果
     *
     * @param userId 用户id
     * @param status 任务状态
     * @return 指定任务状态的统计信息
     */
    GeneralTaskStatusStatistic countStatusByUser(String userId, GeneralTaskStatus status);

    /**
     * 获取指定任务状态的统计信息，其中
     * {@link GeneralTaskStatusStatistic#getTotal() 任务总数}用于查看用户总共接收了多少任务，
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}用于查看处于此状态的任务有多少个，
     * {@link GeneralTaskStatusStatistic#getPercent() 指定状态任务数}为
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}/{@link GeneralTaskStatusStatistic#getTotal() 任务总数}
     * 计算而出的结果
     *
     * @param userId 用户id
     * @param status 任务状态
     * @return 指定任务状态的统计信息
     */
    GeneralTaskStatusStatistic countStatusByUser(String userId, List<GeneralTaskStatus> status);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否成功
     */
    boolean deleteAll(List<String> idList);

    JSONObject statusCount(TenantId tenantId);
}
