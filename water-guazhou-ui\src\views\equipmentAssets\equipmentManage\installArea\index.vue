<!-- 安装区域 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
    <DialogForm
      ref="imageForm"
      :config="addImageConfig"
    ></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { postAreaTree, getAreaTreeSearch, deleteArea } from '@/api/equipment_assets/equipmentManage'

import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import { objectFlattening } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const refForm = ref<IDialogFormIns>()

const imageForm = ref<IDialogFormIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '名称', field: 'name', type: 'input' },
    { label: '简称', field: 'shortName', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '新建顶级区域',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole('新建顶级区域')
        }
      ]
    }
  ] })

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '区域编号', prop: 'serialId' },
    { label: '区域名称', prop: 'name' },
    // { label: '别名', prop: 'nickName' },
    { label: '区域简称', prop: 'nickName' },
    { label: '排序', prop: 'orderNum' },
    { label: '备注', prop: 'remark' }
  ],
  operationWidth: '300px',
  operations: [
    {
      type: 'success',
      text: '新增子集',
      icon: ICONS.ADD,
      perm: $btnPerms('RoleManageAdd'),
      click: row => clickCreatedRole('新增子集', row)
    },
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EXPORT,
      perm: $btnPerms('RoleManageEdit'),
      click: row => clickEdit(row)
    },
    {
      type: 'primary',
      text: '图片信息',
      icon: ICONS.EXPORT,
      perm: $btnPerms('RoleManageEdit'),
      click: row => addImage(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: row => haneleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  dialogWidth: '500px',
  labelWidth: '100px',
  submit: (params:any) => {
    postAreaTree(params).then(res => {
      ElMessage.success(res.data.message)
      refForm.value?.closeDialog()
      refreshData()
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          readonly: true,
          type: 'input',
          label: '编码',
          field: 'serialId',
          rules: [{ required: true, message: '请输入编码' }]
        },
        {
          type: 'input',
          label: '名称',
          field: 'name',
          rules: [{ required: true, message: '请输入名称' }]
        },

        {
          type: 'input',
          label: '简称',
          field: 'nickName',
          rules: [{ required: true, message: '请输入简称' }]
        },

        {
          type: 'input',
          label: '排序',
          field: 'orderNum',
          rules: [{ required: true, message: '请输入排序' }]
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        }

      ]
    }
  ]
})

const addImageConfig = reactive<IDialogFormConfig>({
  title: '新增',
  dialogWidth: '500px',
  labelWidth: '100px',
  submit: (params:any) => {
    postAreaTree(params).then(() => {
      ElMessage.success('上传成功')
      imageForm.value?.closeDialog()
      refreshData()
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'image',
          label: '区域图像',
          field: 'img',
          limit: 1
        }
      ]
    }
  ]
})

const clickCreatedRole = (title?:any, row?: { [x: string]: any }) => {
  addOrUpdateConfig.title = title || '编辑'
  addOrUpdateConfig.defaultValue = { parentId: row?.id || '', serialId: 'QY' + moment(new Date()).format('YYYYMMDDHHmmss') }
  refForm.value?.openDialog()
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  addOrUpdateConfig.defaultValue = { ...(row) || {} }
  refForm.value?.openDialog()
}

const addImage = (row: { [x: string]: any }) => {
  addImageConfig.defaultValue = { ...(row) || {} }
  imageForm.value?.openDialog()
}

const haneleDelete = (row: { id: any }) => {
  SLConfirm('确定删除该安装区域, 是否继续?', '删除提示').then(() => {
    deleteArea([row.id])
      .then(() => {
        refreshData()
        ElMessage.success('删除成功')
      })
      .catch(err => {
        ElMessage.error(err.data.message)
      })
  })
}

const data = reactive({
  areaList: [] as any
})

const refreshData = () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    shortName: '',
    ...(refSearch.value?.queryParams || {})
  }
  getAreaTreeSearch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    data.areaList = objectFlattening(TableConfig.dataList, 'children', 'serialId')
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>

<style scoped>
.card-table :deep(.el-table__expand-icon--expanded){
  width: 6px;
}
</style>
