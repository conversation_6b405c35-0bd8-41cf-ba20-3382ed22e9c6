package org.thingsboard.server.dao.sql.msg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.MsgTemplateEntity;

import java.util.List;

@Mapper
public interface MsgTemplateMapper extends BaseMapper<MsgTemplateEntity> {

    Page<MsgTemplateEntity> getList(IPage<MsgTemplateEntity> iPage, @Param("configId") String configId, @Param("name") String name, @Param("tenantId") String tenantId);

    List<MsgTemplateEntity> selectByConfigId(@Param("configId") String configId);
}
