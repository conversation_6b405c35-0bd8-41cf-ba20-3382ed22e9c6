package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitTaskReport;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class CircuitTaskReportPageRequest extends AdvancedPageableQueryEntity<CircuitTaskReport, CircuitTaskReportPageRequest> {
    // 巡检任务code
    @NotNullOrEmpty
    private String taskCode;

    // 类型：关键点、设备、专项设备
    private String type;
}
