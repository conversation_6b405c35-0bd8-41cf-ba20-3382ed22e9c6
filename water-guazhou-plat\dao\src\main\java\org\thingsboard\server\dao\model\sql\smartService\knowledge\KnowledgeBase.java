package org.thingsboard.server.dao.model.sql.smartService.knowledge;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 班组主表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-26
 */
@TableName("tb_service_knowledge_base")
@Data
public class KnowledgeBase {
    
    @TableId
    private String id;

    private String typeId;

    private transient String typeName;

    private String title;

    private String content;

    private Date createTime;

    private Date updateTime;

    private String tenantId;
}
