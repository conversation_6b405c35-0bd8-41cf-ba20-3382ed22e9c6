<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BasePushTemplateConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BasePushTemplateConfiguration" id="BasePushTemplateConfigurationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectBasePushTemplateConfigurationVo">
        select id, name, type, title, content, status, create_time from base_push_template_configuration
    </sql>

    <select id="selectBasePushTemplateConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BasePushTemplateConfiguration" resultMap="BasePushTemplateConfigurationResult">
        <include refid="selectBasePushTemplateConfigurationVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>
    
    <select id="selectBasePushTemplateConfigurationById" parameterType="String" resultMap="BasePushTemplateConfigurationResult">
        <include refid="selectBasePushTemplateConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBasePushTemplateConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BasePushTemplateConfiguration">
        insert into base_push_template_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBasePushTemplateConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BasePushTemplateConfiguration">
        update base_push_template_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBasePushTemplateConfigurationById" parameterType="String">
        delete from base_push_template_configuration where id = #{id}
    </delete>

    <delete id="deleteBasePushTemplateConfigurationByIds" parameterType="String">
        delete from base_push_template_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>