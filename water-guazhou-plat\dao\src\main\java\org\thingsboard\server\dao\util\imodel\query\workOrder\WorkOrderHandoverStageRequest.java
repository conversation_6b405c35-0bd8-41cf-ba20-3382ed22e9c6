package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderDetail;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class WorkOrderHandoverStageRequest extends WorkOrderStageRequest {
    @NotNullOrEmpty
    private String expectUserId;

    @Override
    public String valid(IStarHttpRequest request) {
        if (isNullOrEmpty(getNextProcessUserId()))
            return "未指定审核人";

        expectUserId = parseUUID(expectUserId);

        return super.valid(request);
    }

    @Override
    public WorkOrderDetail process(WorkOrder order, boolean forceStepProcessUser) {
        WorkOrderDetail detail = super.process(order, forceStepProcessUser);
        detail.setNextProcessUserId(expectUserId);
        return detail;
    }
}
