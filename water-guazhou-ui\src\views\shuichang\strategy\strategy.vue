<!-- 优化策略 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable class="card-table" :config="TableConfig" />
    <DialogForm ref="refForm" :config="addOrUpdateConfig"> </DialogForm>
    <DialogForm ref="refWindControl" :config="windControlConfig"> </DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { getWaterSupplyTree } from '@/api/company_org';
import {
  GetStationAttrs,
  deleteAlarmRules,
  getAlarmRules,
  postAlarmRules
} from '@/api/shuiwureports/zhandian';
import { getCameraTree } from '@/api/video';
import { getPriceVersionCode } from '@/api/waterPriceManagement/version';
import { ICONS } from '@/common/constans/common';
import useGlobal from '@/hooks/global/useGlobal';
import useStation from '@/hooks/station/useStation';
import { SLConfirm } from '@/utils/Message';
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

import { formatDate } from '@/utils/DateFormatter';
import { traverse } from '@/utils/GlobalHelper';
import { alarmType } from './data';
import { GeneralTable } from '@/utils/GeneralProcessing';

const { $btnPerms } = useGlobal();
const { getStationTree, getStationTreeByDisabledType } = useStation();

const refForm = ref<IDialogFormIns>();
const refWindControl = ref<IDialogFormIns>();
const refSearch = ref<ICardSearchIns>();

const Conditions = [
  { label: '数值等于X', value: '1' },
  { label: '数值不等于X', value: '2' },
  { label: '数值高于X', value: '3' },
  { label: '数低于X', value: '4' },
  { label: '数值在X和Y之间', value: '5' },
  { label: '数值不在X与Y之间', value: '6' },
  { label: '数值超过M分钟等于X', value: '7' },
  { label: '数值超过M分钟不等于X', value: '8' },
  { label: '数值超过M分钟高于X', value: '9' },
  { label: '数值超过M分钟低于X', value: '10' },
  { label: '数值在X和Y之间超过M分钟', value: '11' },
  { label: '数值不在X和Y之间超过M分钟', value: '12' }
];

const windConditions = [
  { label: '高于', value: '1' },
  { label: '低于', value: '2' },
  { label: '等于', value: '3' },
  { label: '不等于', value: '4' }
];

const alarmLevel = [
  { label: '提醒报警', value: '1' },
  { label: '重要报警', value: '2' },
  { label: '紧急报警', value: '3' }
];

const state = reactive({
  type: '1'
});

const cardSearchConfig = ref<ISearch>({
  defaultParams: { type: '1' },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '普通规则', value: '1' },
        { label: '风控规则', value: '2' }
      ],
      label: '',
      onChange: (type: any) => {
        state.type = type;
        cardSearchConfig.value.defaultParams = { type };
        refSearch.value?.resetForm();
        refreshData();
      }
    },
    { label: '变量名称', field: 'name', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '规则名称', prop: 'title' },
    { label: '站点变量名称', prop: 'stationAttrName' },
    {
      label: '触发条件',
      prop: 'ruleType',
      formatter: (row) =>
        Conditions.find((item) => item.value === row.ruleType)?.label,
      hidden: computed(() => state.type === '2') as any
    },
    {
      label: '触发条件',
      prop: 'ruleType',
      formatter: (row) =>
        windConditions.find((item) => item.value === row.ruleType)?.label,
      hidden: computed(() => state.type === '1') as any
    },
    {
      label: '报警等级',
      prop: 'alarmLevel',
      formatter: (row) =>
        alarmLevel.find((item) => item.value === row.alarmLevel)?.label,
      hidden: computed(() => state.type === '2') as any
    },
    {
      label: '报警类型',
      prop: 'alarmType',
      formatter: (row) =>
        alarmType.find((item) => item.value === row.alarmType)?.label,
      hidden: computed(() => state.type === '2') as any
    },
    {
      label: '处理建议',
      prop: 'processMethod',
      hidden: computed(() => state.type === '2') as any
    },
    {
      label: 'x',
      prop: 'x',
      formatter: (row) => row.ruleParamObj?.x,
      hidden: computed(() => state.type === '2') as any
    },
    {
      label: 'y',
      prop: 'y',
      formatter: (row) => row.ruleParamObj?.y,
      hidden: computed(() => state.type === '2') as any
    },
    {
      label: 'm',
      prop: 'm',
      formatter: (row) => row.ruleParamObj?.m,
      hidden: computed(() => state.type === '2') as any
    },
    {
      label: '低风险值',
      prop: 'x',
      formatter: (row) => row.ruleParamObj?.x,
      hidden: computed(() => state.type === '1') as any
    },
    {
      label: '中风险值',
      prop: 'y',
      formatter: (row) => row.ruleParamObj?.y,
      hidden: computed(() => state.type === '1') as any
    },
    {
      label: '高风险值',
      prop: 'm',
      formatter: (row) => row.ruleParamObj?.m,
      hidden: computed(() => state.type === '1') as any
    },
    { label: '联动采集变量ID', prop: 'remoteStationAttrId' },
    { label: '联动监控点ID', prop: 'remoteVideoId' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      label: '是否启用',
      prop: 'status',
      formatter: (row) => (row.status === '1' ? '启用' : '停用')
    }
    // { label: '租户ID', prop: 'tenantId' }
  ],
  operationWidth: '160px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  dialogWidth: '1000px',
  labelWidth: '100px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增成功';
    if (params.id) text = '修改成功';
    params['ruleParamObj'] = {
      x: params.x,
      y: params.y,
      m: params.m
    };
    delete params.x;
    delete params.y;
    delete params.m;
    params.type = state.type;
    const sendTypes = { app: false, sms: false, wechat: false };
    params.sendWay.map((type) => {
      sendTypes[type] = true;
    });
    params.sendWay = JSON.stringify(sendTypes);
    const param = {
      ...params,
      msgList: params.msgList?.map((item: string) => {
        return {
          userId: item
        };
      }),
      appList: params.appList?.map((item: string) => {
        return {
          userId: item
        };
      })
    };
    postAlarmRules(param)
      .then(() => {
        addOrUpdateConfig.submitting = false;
        refForm.value?.closeDialog();
        ElMessage.success(text);
        refreshData();
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {
    reAlarmType: '1'
  },
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '规则名称',
          field: 'title'
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '监测点:',
          multiple: false,
          field: 'stationAttrId',
          clearable: false,
          showCheckbox: false,
          lazy: true,
          options: [],
          lazyLoad: (node, resolve) => {
            if (node.level === 0) return resolve(data.stationTree);
            if (node.data.children?.length > 0) {
              return resolve(node.data.children);
            }
            if (node.isLeaf) return resolve([]);
            if (node.data?.isLeaf) return resolve([]);
            GetStationAttrs({ stationId: node.data.id }).then((res) => {
              const newAttrs = res.data?.map((attr) => {
                return {
                  label: attr.type,
                  value: '',
                  id: '',
                  children: attr.attrList.map((attr) => {
                    return {
                      label: attr.name,
                      value: attr.id,
                      id: attr.id,
                      isLeaf: true
                    };
                  })
                };
              });
              return resolve(newAttrs);
            });
          }
        },
        {
          type: 'divider',
          text: '规则设置'
        },
        {
          xs: 12,
          type: 'select',
          label: '触发条件',
          field: 'ruleType',
          rules: [{ required: true, message: '请输入触发条件' }],
          options: Conditions
        },
        {
          xs: 12,
          type: 'input-number',
          label: 'x',
          field: 'x',
          rules: [{ required: true, message: '请输入x' }]
        },
        {
          xs: 12,
          handleHidden(params, query, formItem) {
            formItem.hidden =
              ['5', '6', '11', '12'].indexOf(params.ruleType) === -1;
          },
          type: 'input-number',
          label: 'y',
          field: 'y',
          rules: [{ required: true, message: '请输入y' }]
        },
        {
          xs: 12,
          handleHidden(params, query, formItem) {
            formItem.hidden =
              ['7', '8', '9', '10', '11', '12'].indexOf(params.ruleType) === -1;
          },
          type: 'input-number',
          label: 'm',
          field: 'm',
          rules: [{ required: true, message: '请输入m' }]
        },
        {
          xs: 12,
          type: 'select',
          label: '开启状态',
          field: 'status',
          options: [
            { label: '启用', value: '1' },
            { label: '停用', value: '2' }
          ]
        },
        {
          xs: 12,
          type: 'select',
          label: '报警等级',
          field: 'alarmLevel',
          rules: [{ required: true, message: '请输入报警等级' }],
          options: alarmLevel
        },
        {
          xs: 12,
          type: 'select',
          label: '报警类型',
          field: 'alarmType',
          options: alarmType
        },
        {
          xs: 24,
          type: 'checkbox',
          label: '发送方式',
          field: 'sendWay',
          options: [
            { label: 'APP', value: 'app' },
            { label: '短信', value: 'sms' },
            { label: '微信', value: 'wechat' }
          ]
        },
        {
          type: 'divider',
          text: '处理建议'
        },
        {
          xs: 12,
          type: 'input',
          label: '处理建议',
          field: 'processMethod'
        },
        {
          type: 'divider',
          text: '其它设置'
        },
        {
          xs: 12,
          type: 'select',
          label: '报警机制',
          field: 'reAlarmType',
          options: [
            { label: '未解除/恢复期前,不重复提醒', value: '1' },
            { label: '报警恢复之前', value: '2' }
          ]
        },
        {
          xs: 12,
          handleHidden(params, query, formItem) {
            formItem.hidden = params.reAlarmType !== '2';
          },
          type: 'input-number',
          label: '间隔时间',
          field: 'reAlarmValue',
          rules: [{ required: true, message: '请输入间隔时间' }]
        },
        {
          xs: 12,
          handleHidden(params, query, formItem) {
            formItem.hidden = params.reAlarmType !== '2';
          },
          type: 'select',
          label: '时间单位',
          field: 'reAlarmUnit',
          rules: [{ required: true, message: '请输入时间单位' }],
          options: [
            { label: '分钟', value: 'minute' },
            { label: '小时', value: 'hour' },
            { label: '天', value: 'day' }
          ]
        },
        {
          type: 'divider',
          text: '联动采集'
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '监测点:',
          multiple: false,
          field: 'remoteStationAttrId',
          clearable: false,
          showCheckbox: false,
          lazy: true,
          options: [],
          lazyLoad: (node, resolve) => {
            if (node.level === 0) return resolve(data.stationTree);
            if (node.data.children?.length > 0) {
              return resolve(node.data.children);
            }

            if (node.isLeaf) return resolve([]);
            if (node.data?.isLeaf) return resolve([]);
            GetStationAttrs({ stationId: node.data.id }).then((res) => {
              const newAttrs = res.data?.map((attr) => {
                return {
                  label: attr.type,
                  value: '',
                  id: '',
                  children: attr.attrList.map((attr) => {
                    return {
                      label: attr.name,
                      value: attr.id,
                      id: attr.id,
                      isLeaf: true
                    };
                  })
                };
              });
              return resolve(newAttrs);
            });
          }
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '选择监控点',
          field: 'remoteVideoId',
          options: computed(() => data.videoTree) as any
        },
        {
          type: 'divider',
          text: '告警推送配置'
        },
        {
          xl: 12,
          type: 'department-user',
          multiple: true,
          label: '短信提醒用户',
          field: 'msgList'
        },
        {
          xl: 12,
          type: 'department-user',
          multiple: true,
          label: 'APP推送用户',
          field: 'appList'
        }
      ]
    }
  ]
});

const windControlConfig = reactive<IDialogFormConfig>({
  title: '新增',
  dialogWidth: '1000px',
  labelWidth: '100px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增成功';
    if (params.id) text = '修改成功';
    params['ruleParamObj'] = {
      x: params.x,
      y: params.y,
      m: params.m,
      x_process_method: params.x_process_method,
      y_process_method: params.y_process_method,
      m_process_method: params.m_process_method
    };
    delete params.x;
    delete params.y;
    delete params.m;
    delete params.x_process_method;
    delete params.y_process_method;
    delete params.m_process_method;
    params.type = state.type;
    const sendTypes = { app: false, sms: false, wechat: false };
    params.sendWay.map((type) => {
      sendTypes[type] = true;
    });
    params.sendWay = JSON.stringify(sendTypes);
    postAlarmRules(params)
      .then(() => {
        addOrUpdateConfig.submitting = false;
        refWindControl.value?.closeDialog();
        ElMessage.success(text);
        refreshData();
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {
    reAlarmType: '1'
  },
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '规则名称',
          field: 'title'
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '监测点:',
          multiple: false,
          field: 'stationAttrId',
          clearable: false,
          showCheckbox: false,
          lazy: true,
          options: [],
          lazyLoad: (node, resolve) => {
            if (node.level === 0) return resolve(data.stationTree);
            if (node.data.children?.length > 0) {
              return resolve(node.data.children);
            }

            if (node.isLeaf) return resolve([]);
            if (node.data?.isLeaf) return resolve([]);
            GetStationAttrs({ stationId: node.data.id }).then((res) => {
              const newAttrs = res.data?.map((attr) => {
                return {
                  label: attr.type,
                  value: '',
                  id: '',
                  children: attr.attrList.map((attr) => {
                    return {
                      label: attr.name,
                      value: attr.id,
                      id: attr.id,
                      isLeaf: true
                    };
                  })
                };
              });
              return resolve(newAttrs);
            });
          }
        },
        {
          xs: 12,
          type: 'select',
          label: '开启状态',
          field: 'status',
          options: [
            { label: '启用', value: '1' },
            { label: '停用', value: '2' }
          ]
        },
        {
          type: 'divider',
          text: '报警设置'
        },
        {
          xs: 12,
          type: 'select',
          label: '触发条件',
          field: 'ruleType',
          rules: [{ required: true, message: '请输入触发条件' }],
          options: windConditions
        },
        {
          xs: 24,
          type: 'checkbox',
          label: '发送方式',
          field: 'sendWay',
          options: [
            { label: 'APP', value: 'app' },
            { label: '短信', value: 'sms' },
            { label: '微信', value: 'wechat' }
          ]
        },
        {
          type: 'divider',
          text: '风控设置'
        },
        {
          xs: 10,
          type: 'input',
          label: '低风险触发值',
          field: 'x'
        },
        {
          xs: 14,
          placeholder: '请输入处理建议',
          labelWidth: '10px',
          type: 'input',
          label: '',
          field: 'x_process_method'
        },
        {
          xs: 10,
          type: 'input',
          label: '中风险触发值',
          field: 'y'
        },
        {
          xs: 14,
          placeholder: '请输入处理建议',
          labelWidth: '10px',
          type: 'input',
          label: '',
          field: 'y_process_method'
        },
        {
          xs: 10,
          type: 'input',
          label: '高风险触发值',
          field: 'm'
        },
        {
          xs: 14,
          placeholder: '请输入处理建议',
          labelWidth: '10px',
          type: 'input',
          label: '',
          field: 'm_process_method'
        },
        {
          type: 'divider',
          text: '其它设置'
        },
        {
          xs: 12,
          type: 'select',
          label: '报警机制',
          field: 'reAlarmType',
          options: [
            { label: '未解除/恢复期前,不重复提醒', value: '1' },
            { label: '报警恢复之前', value: '2' }
          ]
        },
        {
          xs: 12,
          handleHidden(params, query, formItem) {
            formItem.hidden = params.reAlarmType !== '2';
          },
          type: 'input-number',
          label: '间隔时间',
          field: 'reAlarmValue',
          rules: [{ required: true, message: '请输入间隔时间' }]
        },
        {
          xs: 12,
          handleHidden(params, query, formItem) {
            formItem.hidden = params.reAlarmType !== '2';
          },
          type: 'select',
          label: '时间单位',
          field: 'reAlarmUnit',
          rules: [{ required: true, message: '请输入时间单位' }],
          options: [
            { label: '分钟', value: 'minute' },
            { label: '小时', value: 'hour' },
            { label: '天', value: 'day' }
          ]
        },
        {
          type: 'divider',
          text: '联动采集'
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '监测点:',
          multiple: false,
          field: 'remoteStationAttrId',
          clearable: false,
          showCheckbox: false,
          lazy: true,
          options: [],
          lazyLoad: (node, resolve) => {
            if (node.level === 0) return resolve(data.stationTree);
            if (node.data.children?.length > 0) {
              return resolve(node.data.children);
            }

            if (node.isLeaf) return resolve([]);
            if (node.data?.isLeaf) return resolve([]);
            GetStationAttrs({ stationId: node.data.id }).then((res) => {
              const newAttrs = res.data?.map((attr) => {
                return {
                  label: attr.type,
                  value: '',
                  id: '',
                  children: attr.attrList.map((attr) => {
                    return {
                      label: attr.name,
                      value: attr.id,
                      id: attr.id,
                      isLeaf: true
                    };
                  })
                };
              });
              return resolve(newAttrs);
            });
          },
          onChange: (val, v1, v2) => {
            debugger;
          }
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '选择监控点',
          field: 'remoteVideoId',
          options: computed(() => data.videoTree) as any
        }
      ]
    }
  ]
});

const clickCreatedRole = () => {
  if (state.type === '1') {
    addOrUpdateConfig.title = '新增';
    addOrUpdateConfig.defaultValue = { reAlarmType: '1', type: '1' };
    refForm.value?.openDialog();
  } else {
    windControlConfig.title = '新增';
    windControlConfig.defaultValue = { reAlarmType: '1', type: '2' };
    refWindControl.value?.openDialog();
  }
};

const clickEdit = (row: { [x: string]: any }) => {
  if (row.sendWay && !Array.isArray(row.sendWay)) {
    const sendWays = JSON.parse(row?.sendWay ?? '{}');
    row.sendWay = [];
    for (const key in sendWays) {
      if (sendWays[key]) {
        row.sendWay.push(key);
      }
    }
  }
  console.log(row.sendWay);
  if (state.type === '1') {
    addOrUpdateConfig.title = '编辑';
    if (row.ruleParamObj) {
      row = { ...row, ...row.ruleParamObj };
    }
    const msgList = row.msgList?.map((item) => item.userId);
    const appList = row.appList?.map((item) => item.userId);
    addOrUpdateConfig.defaultValue = { ...(row || {}), msgList, appList };
    refForm.value?.openDialog();
  } else {
    windControlConfig.title = '编辑';
    if (row.ruleParamObj) {
      row = { ...row, ...row.ruleParamObj };
    }
    windControlConfig.defaultValue = { ...(row || {}) };
    refWindControl.value?.openDialog();
  }
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除指定优化策略?', '删除提示').then(() => {
    deleteAlarmRules([row.id])
      .then((res) => {
        if (res.data?.code === 200) {
          ElMessage.success('删除成功');
          refreshData();
        } else {
          ElMessage.warning('删除失败');
        }
      })
      .catch((err) => {
        ElMessage.warning(err);
      });
  });
};

const data = reactive({
  // 单位
  unitList: [],
  //   树
  stationTree: [] as any[],
  // 水价版本
  codeList: [] as any[],
  // 视频树
  videoTree: [],
  // 获取单位
  getWaterSupplyTreeValue: () => {
    const depth = 1;
    getWaterSupplyTree(depth).then((res) => {
      data.unitList = traverse(res.data.data || []);
    });
  },
  // 获取水价版本编号
  getPriceVersionCodeValue: () => {
    getPriceVersionCode({ status: 'ON' }).then((res) => {
      const value = res.data.data || [];
      data.codeList = [];
      value.forEach((element) => {
        data.codeList.push({ label: element, value: element });
      });
    });
  },
  getVideoTree: () => {
    GeneralTable({}, getCameraTree).then((res) => {
      debugger;
      data.videoTree = traverse(res.data, 'children');
    });
  }
});

const refreshData = async () => {
  TableConfig.dataList = [];
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    type: state.type,
    ...(refSearch.value?.queryParams || {})
  };
  getAlarmRules(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onBeforeMount(async () => {
  const treeData = (await getStationTree()) as any[];
  await getStationTreeByDisabledType(
    treeData,
    ['Project', 'Station'],
    false,
    'Station'
  );
  data.stationTree = treeData;
});

onMounted(async () => {
  await refreshData();
  data.getWaterSupplyTreeValue();
  data.getPriceVersionCodeValue();
  data.getVideoTree();
});

onActivated(() => {
  data.getWaterSupplyTreeValue();
  data.getPriceVersionCodeValue();
  data.getVideoTree();
});
</script>
