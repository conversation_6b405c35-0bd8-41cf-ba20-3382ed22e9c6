<!-- 消息群发 -->
<template>
  <div
    class="wrapper"
    :class="{ isDark: useAppStore().isDark }"
  >
    <div class="flex mag_bot_14">
      <SLCard style="height:350px ;width: 280px; ">
        <SLTree :tree-data="templateTreeData" />
      </SLCard>
      <SLCard
        style="height:350px ;width: 300px;"
        class="mag_left_14"
      >
        <SLTree :tree-data="userTreeData" />
      </SLCard>
      <SLCard
        style="height:350px ;width: calc(100% - 600px); padding: 10px;"
        class="mag_left_14"
      >
        <div class="header-wrapper">
          <el-button
            type="success"
            @click="addUser"
          >
            添加收件人
          </el-button>
        </div>
        <br>
        <el-form
          :model="form"
          label-width="120px"
          label-position="top"
        >
          <el-form-item label="收件人">
            <el-tag
              v-for="tag in form.name"
              :key="tag.name"
              class="mr-4 mb-4"
              closable
              @close="handleClose(tag)"
            >
              {{ tag.name }}
            </el-tag>
          </el-form-item>
          <el-form-item label="发送信息内容">
            <el-input
              v-model="form.content"
              type="textarea"
            />
          </el-form-item>
          <el-form-item label="">
            <el-date-picker
              v-model="form.sendTime"
              type="date"
              placeholder="预计发送时间"
            />
          </el-form-item>

          <el-form-item>
            <!-- <el-button
              type="primary"
              @click="addUser"
            >
              保存模板
            </el-button> -->
            <el-button
              type="success"
              @click="sendMessage"
            >
              发送信息
            </el-button>
          </el-form-item>
        </el-form>
      </SLCard>
    </div>
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      class="card-table"
      :config="TableConfig"
    />
    <DialogForm
      ref="refForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Plus, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { ICONS } from '@/common/constans/common'
import { SMS_status } from '../data'
import {
  getSMS_templateList,
  getEmergencyUserTree,
  getMessageRecordList,
  sendSms
} from '@/api/productionScheduling/emergencyDispatch'
import { getSmsTemplateList } from '@/api/smsManage/sms'
import { useAppStore } from '@/store'
import { traverse } from '@/utils/GlobalHelper'

const refForm = ref<IDialogFormIns>()

const refSearch = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '发送内容', field: 'content', type: 'input' },
    { label: '开始时间', field: 'fromTime', type: 'date' },
    { label: '结束时间', field: 'toTime', type: 'date' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ]
})

// 内容模板
const templateTreeData = reactive<SLTreeConfig>({
  title: '内容模板',
  data: [],
  currentProject: {},
  isFilterTree: true,
  treeNodeHandleClick: data => {
    templateTreeData.currentProject = data
    form.content = data.content
    form.templateId = data.id
  }
})

// 用户
const userTreeData = reactive<SLTreeConfig>({
  title: '用户',
  data: [],
  currentProject: {},
  isFilterTree: true,
  treeNodeHandleClick: data => {
    if (data.layer !== 3) { return }
    userTreeData.currentProject = data
    form.name.push({ name: data.name, phone: data.phone })
    const hash = {}
    form.name = form.name.reduce((item, next) => {
      hash[next.name] ? '' : hash[next.name] = true && item.push(next)
      return item
    }, [])
  }
})

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加收件人',
  dialogWidth: 500,
  labelWidth: '100px',
  submitting: false,
  submit: (params: any) => {
    form.name.push(params)
    refForm.value?.closeDialog()
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '用户名',
          field: 'name',
          rules: [{ required: true, message: '请输入用户名' }]
        },
        {
          type: 'input-number',
          label: '电话',
          field: 'phone',
          rules: [{ required: true, message: '请输入电话' }]
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '短信接收人', prop: 'receiveUserName' },
    { label: '消息内容', prop: 'content' },
    { label: '接收人手机号码', prop: 'receivePhone' },
    { label: '操作人组织', prop: 'sendUserOrganizationName' },
    { label: '操作人部门', prop: 'sendUserDepartmentName' },
    { label: '操作人', prop: 'sendUserName' },
    { label: '发送时间', prop: 'sendTime' },
    { label: '是否成功', prop: 'status', formatter: row => SMS_status[row.status] || '' }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const form = reactive({
  name: [] as any[],
  content: '',
  templateId: '',
  sendTime: '',
  receiveUserIdList: [] as string[],
  receiveUserPhoneList: [] as string[]
})

const handleClose = (tag: string) => {
  form.name.splice(form.name.indexOf(tag), 1)
}
// 添加信息
function addUser() {
  addOrUpdateConfig.defaultValue = { content: form.content }
  refForm.value?.openDialog()
}

function sendMessage() {
  form.receiveUserIdList = []
  form.receiveUserPhoneList = []
  form.name.forEach(item => {
    form.receiveUserIdList.push(item.name)
    form.receiveUserPhoneList.push(item.phone)
  })
  form.sendTime = dayjs(form.sendTime).valueOf() as any
  sendSms(form).then(() => {
    ElMessage.success('发送成功')
    refreshData()
  }).catch(error => {
    ElMessage.warning(error)
  })
}

function replace(
  val
) {
  val.map(obj => {
    if (obj) {
      if (obj.children && obj.children.length) {
        replace(obj.children)
      } else if (obj.layer === 3) obj.icon = ICONS.ADD
    }
    return obj
  })
  return val
}

function init() {
  // 内容模板
  // getSMS_templateList({ size: -1, page: 1 }).then(res => {
  //   templateTreeData.data = res.data.data.data || []
  // })
  // 内容模板
  getSmsTemplateList({ page: 1, size: 9999 }).then(res => {
    templateTreeData.data = res.data.data.data || []
  })
  getEmergencyUserTree(3).then(res => {
    shallowRef(Plus)
    userTreeData.data = traverse(res.data.data || [], 'children', { label: 'name', value: 'id' })
    userTreeData.data = replace(userTreeData.data)
  })
}

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})

  }
  getMessageRecordList(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(async () => {
  init()
  refreshData()
})
</script>

<style lang="scss" scoped>
.mag_left_14 {
  margin-left: 14px;
}

.mag_bot_14 {
  margin-bottom: 14px;
}

.card-table {
  height: calc(100% - 440px);
}

.mr-4 {
  margin-right: 4px;
}

.mb-4 {
  margin-bottom: 4px;
}
</style>
