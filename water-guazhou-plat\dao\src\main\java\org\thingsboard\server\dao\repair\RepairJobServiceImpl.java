package org.thingsboard.server.dao.repair;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.DTO.TriggerDTO;
import org.thingsboard.server.dao.model.sql.*;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.orderWork.WorkOrderService;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.shuiwu.assets.AssetsAccountService;
import org.thingsboard.server.dao.sql.repair.RepairJobCRepository;
import org.thingsboard.server.dao.sql.repair.RepairJobRepository;
import org.thingsboard.server.dao.sql.repair.RepairJobTriggerRepository;

import java.util.*;

@Slf4j
@Service
public class RepairJobServiceImpl implements RepairJobService {

    @Autowired
    private RepairJobCRepository repairJobCRepository;
    @Autowired
    private RepairJobRepository repairJobRepository;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private RepairStandardService repairStandardService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private RepairJobTriggerRepository repairJobTriggerRepository;
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private AssetsAccountService assetsAccountService;

    @Override
    public RepairJobEntity detail(String id, TenantId tenantId) {
        // 查询主表
        RepairJobEntity repairJob = repairJobRepository.findOne(id);

        // 查询子表
        if (repairJob != null) {
            // 查询项目列表
            List<ProjectEntity> projectList = projectService.findByTenantId(tenantId);
            Map<String, ProjectEntity> projectMap = new HashMap<>();
            if (projectList != null && projectList.size() > 0) {
                projectList.forEach(p -> projectMap.put(p.getId(), p));
            }

            // 查询设备台账列表
            List<AssetsAccountEntity> assetsAccountList = assetsAccountService.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
            Map<String, AssetsAccountEntity> assetsAccountMap = new HashMap<>();
            if (assetsAccountList != null && assetsAccountList.size() > 0) {
                assetsAccountList.forEach(d -> assetsAccountMap.put(d.getId(), d));
            }

            // 查询检修项目列表
            List<RepairStandardEntity> repairStandardList = repairStandardService.findAll("", tenantId);
            Map<String, RepairStandardEntity> repairStandardMap = new HashMap<>();
            if (repairStandardList != null && repairStandardList.size() > 0) {
                repairStandardList.forEach(r -> repairStandardMap.put(r.getId(), r));
            }

            if ("2".equals(repairJob.getType())) {// 触发性任务
                RepairJobTriggerEntity trigger = repairJobTriggerRepository.findByMainId(id);
                if (trigger != null) {
                    String detail = trigger.getDetail();
                    trigger.setTriggerList(JSON.parseArray(detail, TriggerDTO.class));

                    ProjectEntity project = projectMap.get(trigger.getProjectId());
                    if (project != null) {
                        trigger.setProjectName(project.getName());
                    }

                    AssetsAccountEntity assetsAccountEntity = assetsAccountMap.get(trigger.getDeviceId());
                    if (assetsAccountEntity != null) {
                        trigger.setDeviceName(assetsAccountEntity.getDeviceName());
                    }
                }

                repairJob.setTrigger(trigger);
            } else {//固定日期和预防性
                List<RepairJobCEntity> jobList = repairJobCRepository.findByMainId(id);
                if (jobList != null && jobList.size() > 0) {
                    for (RepairJobCEntity child : jobList) {
                        ProjectEntity project = projectMap.get(child.getProjectId());
                        if (project != null) {
                            child.setProjectName(project.getName());
                        }

                        AssetsAccountEntity assetsAccountEntity = assetsAccountMap.get(child.getDeviceId());
                        if (assetsAccountEntity != null) {
                            child.setDeviceName(assetsAccountEntity.getDeviceName());
                        }

                        RepairStandardEntity repairStandard = repairStandardMap.get(child.getStandardName());
                        if (repairStandard != null) {
                            child.setStandardName(repairStandard.getName());
                        }
                    }

                    repairJob.setJobList(jobList);
                }
            }
        }

        return repairJob;
    }

    @Override
    public PageData<RepairJobEntity> findList(int page, int size, String name, String deviceId, User currentUser) {
        // 分页参数
        PageRequest pageable = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");

        Page<RepairJobEntity> pageResult = repairJobRepository.findList(name, UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()), deviceId, pageable);
        List<RepairJobEntity> content = pageResult.getContent();

        List<WorkOrderEntity> orderList = workOrderService.findByTenantIdAndType(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()), "1");
        Map<String, WorkOrderEntity> orderMap = new HashMap<>();

        if (orderList != null) {
            orderList.forEach(order -> orderMap.put(order.getContentId(), order));
        }

        for (RepairJobEntity RepairJobEntity : content) {
            WorkOrderEntity order = orderMap.get(RepairJobEntity.getId());
            if (order != null) {
                RepairJobEntity.setOrderId(order.getId());
                RepairJobEntity.setCode(order.getCode());
            }
        }

        return new PageData<>(pageResult.getTotalElements(), content);
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            // 删除主表
            repairJobRepository.delete(id);

            // 删除子表
            repairJobCRepository.removeByMainId(id);
        }
    }

    @Override
    public RepairJobEntity faultReport(RepairJobEntity entity, User currentUser) {
        Date now = new Date();
        // 保存主表
        entity.setCreator(currentUser.getFirstName());
        entity.setCreateTime(now);
        entity.setExecuteTime(now);
        entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));
        entity.setStatus("1");// 未开始
        entity.setType("4");// 故障报修

        entity = repairJobRepository.save(entity);

        // 保存子表
        List<RepairJobCEntity> jobList = entity.getJobList();
        if (jobList != null && jobList.size() > 0) {
            for (RepairJobCEntity job : jobList) {
                job.setMainId(entity.getId());
                job.setTenantId(entity.getTenantId());
                job.setCreateTime(entity.getCreateTime());
                job.setStatus("1");// 未开始
                job.setOrderNumber(1);
            }

            repairJobCRepository.save(jobList);
        }

        return entity;
    }

    @Override
    public RepairJobEntity save(RepairJobEntity job) {
        return repairJobRepository.save(job);
    }

    @Override
    public void save(List<RepairJobCEntity> childList) {
        repairJobCRepository.save(childList);
    }

    @Override
    public List<RepairJobEntity> findJobByType(String type) {
        return repairJobRepository.findByType(type);
    }

    @Override
    public RepairJobTriggerEntity findTrigger(String mainId) {
        return repairJobTriggerRepository.findByMainId(mainId);
    }

    @Override
    public void save(RepairJobTriggerEntity jobC) {
        repairJobTriggerRepository.save(jobC);
    }

    @Override
    public RepairJobEntity findById(String contentId) {
        return repairJobRepository.findOne(contentId);
    }

    @Override
    public Object groupByPriority(String deviceId) {
        List list = repairJobRepository.groupByPriority(deviceId);
        List<JSONObject> result = new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            for (Object o : list) {
                Object[] objects = (Object[]) o;
                JSONObject data = new JSONObject();
                data.put("type", objects[0]);
                data.put("count", objects[1]);

                result.add(data);
            }
        }

        return result;
    }
}
