const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/MapView-DaoQedLH.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/Point-WxyopZva.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js"])))=>i.map(i=>d[i]);
import{R as w,a3 as S,T as A,a4 as $}from"./index-r0dFAfgr.js";import{e as m,y as g,a as V,v as E,x as T,s as F,N as O,$ as H,u as I}from"./Point-WxyopZva.js";import{g as L,bk as Q,y as P,w as U,bE as M}from"./MapView-DaoQedLH.js";import{a as z}from"./widget-BcWKanF2.js";import{r as j}from"./scaleUtils-DgkF6NQH.js";import{n as k}from"./floorFilterUtils-DZ5C6FQv.js";import{s as G}from"./drapedUtils-DJwxIB1g.js";import{f as q}from"./identify-4SBo5EZk.js";import{u as N}from"./IdentifyResult-4DxLVhTm.js";import{d as D,s as C}from"./popupUtils-BjdidZV3.js";let x=null;function oe(t,r){return r.type==="tile"||r.type==="map-image"}let f=class extends E{constructor(t){super(t),this._featuresResolutions=new WeakMap,this.highlightGraphics=null,this.highlightGraphicUpdated=null,this.updateHighlightedFeatures=T(async r=>{this.destroyed||this.updatingHandles.addPromise(this._updateHighlightedFeaturesGeometries(r).catch(()=>{}))})}initialize(){const t=r=>{this.updatingHandles.addPromise(this._updateHighlightedFeaturesSymbols(r).catch(()=>{})),this.updateHighlightedFeatures(this._highlightGeometriesResolution)};this.addHandles([z(()=>this.highlightGraphics,"change",r=>t(r.added),{onListenerAdd:r=>t(r)})])}async fetchPopupFeatures(t,r){var a,o;const{layerView:{layer:e,view:{scale:i}}}=this;if(!t)throw new F("fetchPopupFeatures:invalid-area","Nothing to fetch without area",{layer:e});const s=W(e.sublayers,i,r);if(!s.length)return[];const n=await J(e,s);if(!((((o=(a=e.capabilities)==null?void 0:a.operations)==null?void 0:o.supportsIdentify)??!0)&&e.version>=10.5)&&!n)throw new F("fetchPopupFeatures:not-supported","query operation is disabled for this service",{layer:e});return n?this._fetchPopupFeaturesUsingQueries(t,s,r):this._fetchPopupFeaturesUsingIdentify(t,s,r)}clearHighlights(){var t;(t=this.highlightGraphics)==null||t.removeAll()}highlight(t){const r=this.highlightGraphics;if(!r)return{remove(){}};let e=null;if(t instanceof L?e=[t]:Q.isCollection(t)&&t.length>0?e=t.toArray():Array.isArray(t)&&t.length>0&&(e=t),e=e==null?void 0:e.filter(w),!e||!e.length)return{remove:()=>{}};for(const i of e){const s=i.sourceLayer;s!=null&&"geometryType"in s&&s.geometryType==="point"&&(i.visible=!1)}return r.addMany(e),{remove:()=>{r.removeMany(e??[])}}}async _updateHighlightedFeaturesSymbols(t){const{layerView:{view:r},highlightGraphics:e,highlightGraphicUpdated:i}=this;if(e&&i)for(const s of t){const n=s.sourceLayer&&"renderer"in s.sourceLayer&&s.sourceLayer.renderer;s.sourceLayer&&"geometryType"in s.sourceLayer&&s.sourceLayer.geometryType==="point"&&n&&"getSymbolAsync"in n&&n.getSymbolAsync(s).then(async a=>{var l;a||(a=new P);let o=null;const h="visualVariables"in n?(l=n.visualVariables)==null?void 0:l.find(u=>u.type==="size"):void 0;h&&(x||(x=(await S(async()=>{const{getSize:u}=await import("./MapView-DaoQedLH.js").then(c=>c.m6);return{getSize:u}},__vite__mapDeps([0,1,2,3,4,5]))).getSize),o=x(h,s,{view:r.type,scale:r.scale,shape:a.type==="simple-marker"?a.style:null})),o||(o="width"in a&&"height"in a&&a.width!=null&&a.height!=null?Math.max(a.width,a.height):"size"in a?a.size:16),e.includes(s)&&(s.symbol=new P({style:"square",size:o,xoffset:"xoffset"in a?a.xoffset:0,yoffset:"yoffset"in a?a.yoffset:0}),i(s,"symbol"),s.visible=!0)})}}async _updateHighlightedFeaturesGeometries(t){const{layerView:{layer:r,view:e},highlightGraphics:i,highlightGraphicUpdated:s}=this;if(this._highlightGeometriesResolution=t,!s||!(i!=null&&i.length)||!r.capabilities.operations.supportsQuery)return;const n=this._getTargetResolution(t),a=new Map;for(const l of i)if(!this._featuresResolutions.has(l)||this._featuresResolutions.get(l)>n){const u=l.sourceLayer;O(a,u,()=>new Map).set(l.getObjectId(),l)}const o=Array.from(a,([l,u])=>{const c=l.createQuery();return c.objectIds=[...u.keys()],c.outFields=[l.objectIdField],c.returnGeometry=!0,c.maxAllowableOffset=n,c.outSpatialReference=e.spatialReference,l.queryFeatures(c)}),h=await Promise.all(o);if(!this.destroyed)for(const{features:l}of h)for(const u of l){const c=u.sourceLayer,p=a.get(c).get(u.getObjectId());p&&i.includes(p)&&(p.geometry=u.geometry,s(p,"geometry"),this._featuresResolutions.set(p,n))}}_getTargetResolution(t){const r=t*H(this.layerView.view.spatialReference),e=r/16;return e<=10?0:t/r*e}async _fetchPopupFeaturesUsingIdentify(t,r,e){const i=await this._createIdentifyParameters(t,r,e);if(A(i))return[];const{results:s}=await q(this.layerView.layer.parsedUrl,i);return s.map(n=>n.feature)}async _createIdentifyParameters(t,r,e){const{floors:i,layer:s,timeExtent:n,view:{spatialReference:a,scale:o}}=this.layerView,h=w(e)?e.event:null;if(!r.length)return null;await Promise.all(r.map(({sublayer:d})=>d.load().catch(()=>{})));const l=Math.min($("mapservice-popup-identify-max-tolerance"),s.allSublayers.reduce((d,y)=>y.renderer?G({renderer:y.renderer,event:h}):d,2)),u=this.createFetchPopupFeaturesQueryGeometry(t,l),c=j(o,a),p=Math.round(u.width/c),b=new U({xmin:u.center.x-c*p,ymin:u.center.y-c*p,xmax:u.center.x+c*p,ymax:u.center.y+c*p,spatialReference:u.spatialReference});return new N({floors:i,gdbVersion:"gdbVersion"in s?s.gdbVersion:void 0,geometry:t,height:p,layerOption:"popup",mapExtent:b,returnGeometry:!0,spatialReference:a,sublayers:s.sublayers,timeExtent:n,tolerance:l,width:p})}async _fetchPopupFeaturesUsingQueries(t,r,e){const{layerView:{floors:i,timeExtent:s}}=this,n=w(e)?e.event:null,a=r.map(async({sublayer:o,popupTemplate:h})=>{if(await o.load().catch(()=>{}),o.capabilities&&!o.capabilities.operations.supportsQuery)return[];const l=o.createQuery(),u=G({renderer:o.renderer,event:n}),c=this.createFetchPopupFeaturesQueryGeometry(t,u);if(l.geometry=c,l.outFields=await D(o,h),l.timeExtent=s,i){const v=i.clone(),_=k(v,o);w(_)&&(l.where=l.where?`(${l.where}) AND (${_})`:_)}const p=this._getTargetResolution(c.width/u),b=await B(h),d=o.geometryType==="point"||b&&b.arcadeUtils.hasGeometryOperations(h);d||(l.maxAllowableOffset=p);let{features:y}=await o.queryFeatures(l);const R=d?0:p;y=await K(o,y);for(const v of y)this._featuresResolutions.set(v,R);return y});return(await I(a)).reverse().reduce((o,h)=>h.value?[...o,...h.value]:o,[]).filter(o=>o!=null)}};function W(t,r,e){const i=[],s=n=>{const a=n.minScale===0||r<=n.minScale,o=n.maxScale===0||r>=n.maxScale;if(n.visible&&a&&o){if(n.sublayers)n.sublayers.forEach(s);else if(n.popupEnabled){const h=C(n,{...e,defaultPopupTemplateEnabled:!1});w(h)&&i.unshift({sublayer:n,popupTemplate:h})}}};return((t==null?void 0:t.toArray())??[]).reverse().map(s),i}function B(t){var r;return(r=t.expressionInfos)!=null&&r.length||Array.isArray(t.content)&&t.content.some(e=>e.type==="expression")?M():Promise.resolve()}async function J(t,r){var e,i;if((i=(e=t.capabilities)==null?void 0:e.operations)!=null&&i.supportsQuery)return!0;try{return await Promise.any(r.map(({sublayer:s})=>s.load().then(()=>s.capabilities.operations.supportsQuery)))}catch{return!1}}async function K(t,r){const e=t.renderer;return e&&"defaultSymbol"in e&&!e.defaultSymbol&&(r=e.valueExpression?await Promise.all(r.map(i=>e.getSymbolAsync(i).then(s=>s?i:null))).then(i=>i.filter(s=>s!=null)):r.filter(i=>e.getSymbol(i)!=null)),r}m([g({constructOnly:!0})],f.prototype,"createFetchPopupFeaturesQueryGeometry",void 0),m([g({constructOnly:!0})],f.prototype,"layerView",void 0),m([g({constructOnly:!0})],f.prototype,"highlightGraphics",void 0),m([g({constructOnly:!0})],f.prototype,"highlightGraphicUpdated",void 0),m([g({constructOnly:!0})],f.prototype,"updatingHandles",void 0),f=m([V("esri.views.layers.support.MapService")],f);export{oe as P,f as S};
