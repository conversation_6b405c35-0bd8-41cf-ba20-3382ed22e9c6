import Layout from '@/views/layout/frame/Layout.vue';

export default [
  // DMA分析
  {
    path: '/dma/dmaanalyze',
    component: Layout,
    hidden: false,
    alwaysShow: true,
    name: 'dma',
    meta: {
      title: 'DMA分析',
      icon: 'iconfont icon-xun<PERSON><PERSON><PERSON>',
      roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
    },
    children: [
      {
        path: 'waterBalanceAnalysis',
        name: 'waterBalanceAnalysis',
        meta: { title: '水平衡分析' },
        component: () =>
          import(
            '@/views/dma/dmaanalyze/waterBalanceAnalysis/waterBalanceAnalysis.vue'
          )
      },
      {
        path: 'poorProductionAndSales',
        name: 'poorProductionAndSales',
        meta: { title: '产销差排行' },
        component: () =>
          import(
            '@/views/dma/dmaanalyze/PoorProductionAndSales/poorProductionAndSales.vue'
          )
      },
      {
        path: 'leakageRanking',
        name: 'leakageRanking',
        meta: { title: '漏损排行' },
        component: () =>
          import('@/views/dma/dmaanalyze/leakageRanking/leakageRanking.vue')
      },
      {
        path: 'trafficAnalysis',
        name: 'trafficAnalysis',
        meta: { title: '夜间最小流量分析' },
        component: () =>
          import('@/views/dma/dmaanalyze/trafficAnalysis/trafficAnalysis.vue')
      },
      {
        path: 'pipeNetworkLeakage',
        name: 'pipeNetworkLeakage',
        meta: { title: '管网漏损分析' },
        component: () =>
          import(
            '@/views/dma/dmaanalyze/pipeNetworkLeakage/pipeNetworkLeakage.vue'
          )
      }
    ]
  },

  // 预警告警
  {
    path: '/dma/earlyWarningAlerts',
    component: Layout,
    hidden: true,
    alwaysShow: true,
    name: 'earlyWarningAlerts',
    meta: {
      title: '预警告警',
      icon: 'iconfont icon-xunjianguanli',
      roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
    },
    children: [
      {
        path: 'earlyWarning',
        name: 'earlyWarning',
        meta: { title: '预警管理' },
        component: () =>
          import('@/views/dma/earlyWarningAlerts/earlyWarning/earlyWarning.vue')
      },
      {
        path: 'alertList',
        name: 'alertList',
        meta: { title: '预警列表' },
        component: () =>
          import('@/views/dma/earlyWarningAlerts/alertList/alertList.vue')
      }
    ]
  },

  // 统计分析
  {
    path: '/pipeNetwork/statistics',
    component: Layout,
    hidden: true,
    alwaysShow: true,
    name: 'pipeNetwork',
    meta: {
      title: '统计分析',
      icon: 'iconfont icon-xunjianguanli',
      roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
    },
    children: [
      {
        path: 'assetStatistics',
        name: 'assetStatistics',
        meta: { title: '管网资产统计' },
        component: () =>
          import(
            '@/views/pipeNetwork/statistics/assetStatistics/assetStatistics.vue'
          )
      },
      {
        path: 'trafficReport',
        name: 'trafficReport',
        meta: { title: '流量报表' },
        component: () =>
          import(
            '@/views/pipeNetwork/statistics/trafficReport/trafficReport.vue'
          )
      },
      {
        path: 'pressure',
        name: 'pressure',
        meta: { title: '压力报表' },
        component: () =>
          import('@/views/pipeNetwork/statistics/pressure/pressure.vue')
      }
    ]
  }
];
