<template>
  <div></div>
</template>
<script lang="ts" setup>
import { useSketch } from '@/hooks/arcgis';
import { getGraphicLayer } from '@/utils/MapHelper';

type ISketchOperateType = 'update' | 'create' | 'delete' | 'undo' | 'redo';
const emit = defineEmits(['update', 'create', 'delete', 'undo', 'redo']);
const props = defineProps<{
  layerid: any;
  layername: any;
  multiple?: boolean;
  drawEnd?: (graphics: __esri.Graphic[]) => any;
}>();
const view = inject('view') as __esri.MapView | undefined;
const graphicsLayer: __esri.GraphicsLayer | undefined = getGraphicLayer(view, {
  id: props.layerid,
  title: props.layername
});
const { sketch, initSketch, destroySketch } = useSketch();
const initDraw = (
  type: 'point' | 'multipoint' | 'polyline' | 'polygon' | 'rectangle' | 'circle'
) => {
  if (!view) return;
  !props.multiple && graphicsLayer?.removeAll();
  sketch.value?.create(type);
};
const clear = () => {
  graphicsLayer?.removeAll();
};
initSketch(view, graphicsLayer, {
  updateCallBack: (res) => emitDrawEnd('update', res),
  createCallBack: (res) => emitDrawEnd('create', res),
  delCallBack: (res) => emitDrawEnd('delete', res),
  undoCallBack: (res) => emitDrawEnd('undo', res),
  redoCallBack: (res) => emitDrawEnd('redo', res)
});
const emitDrawEnd = (
  type: ISketchOperateType,
  res: ISketchHandlerParameter
) => {
  emit(type, res, graphicsLayer?.graphics.toArray() || []);
  if (res.state === 'complete' || res.state === 'cancel') {
    props.drawEnd?.(graphicsLayer?.graphics.toArray() || []);
  }
};
const getGraphics = () => {
  return graphicsLayer?.graphics.toArray() || [];
};
const setGraphics = (graphics: __esri.Graphic[]) => {
  graphicsLayer?.removeAll();
  graphicsLayer?.addMany(graphics);
};
defineExpose({
  clear,
  initDraw,
  getGraphics,
  setGraphics
});

onBeforeUnmount(() => {
  graphicsLayer && view?.map.remove(graphicsLayer);
  graphicsLayer?.removeAll();
  graphicsLayer?.destroy();
  destroySketch();
});
</script>
<style lang="scss" scoped></style>
