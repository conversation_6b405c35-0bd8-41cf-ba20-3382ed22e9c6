/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.msg;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.MsgConfigEntity;
import org.thingsboard.server.dao.model.sql.MsgSendLogEntity;
import org.thingsboard.server.dao.model.sql.MsgTemplateEntity;
import org.thingsboard.server.dao.sql.msg.MsgSendLogMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MsgSendLogServiceImpl implements MsgSendLogService {


    @Autowired
    private MsgSendLogMapper msgSendLogMapper;

    @Override
    public MsgSendLogEntity save(MsgSendLogEntity msgSendLogEntity) {
        if (StringUtils.isBlank(msgSendLogEntity.getId())) {
            msgSendLogEntity.setCreateTime(new Date());
            msgSendLogMapper.insert(msgSendLogEntity);

            return msgSendLogEntity;
        }

        msgSendLogMapper.updateById(msgSendLogEntity);
        return msgSendLogEntity;
    }

    @Override
    public PageData<MsgSendLogEntity> getList(int page, int size, Long start, Long end, String templateName, String phone, String status, String tenantId) {
        IPage<MsgSendLogEntity> iPage = new Page<>(page, size);

        Page<MsgSendLogEntity> list = msgSendLogMapper.getList(iPage, start, end, templateName, phone, status, tenantId);
        return new PageData<>(list.getTotal(), list.getRecords());
    }

    @Override
    public boolean delete(List<String> ids) {
        boolean result = true;
        msgSendLogMapper.deleteBatchIds(ids);
        return result;
    }
}
