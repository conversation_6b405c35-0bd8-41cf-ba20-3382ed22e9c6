$calcite-fonts-path: "./fonts/" !default;
$calcite-fonts-display: auto !default;

/*
 ┌─────────────┐
 │ Basic Latin │
 └─────────────┘
 Basic Latin, Latin-1 Supplement, General Punctuation,  Superscripts and Subscripts,
 Currency Symbols, Letterlike Symbols, Mathematical Operators
 */
@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}77156710-6a58-4606-b189-b4185e75967b.woff2") format("woff2"),
       url("#{$calcite-fonts-path}3d5260a1-e4cd-4567-80ed-69d23c40355f.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}77caabd3-1877-4634-85c8-8e398a093b99.woff2") format("woff2"),
       url("#{$calcite-fonts-path}e388ac99-8c6a-4451-8690-1d15b4d45adb.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}014f2daa-c310-4a36-b9fd-79a8e0c48d44.woff2") format("woff2"),
       url("#{$calcite-fonts-path}12b00842-ec20-4c7f-aa72-802fb00f6cc4.woff") format("woff");
  font-weight: 400;
  font-style: italic;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}b0b84e4d-2164-45c7-a674-1662f19f3ba6-basic.woff2") format("woff2"),
       url("#{$calcite-fonts-path}e91d1bbf-3fea-45e2-b003-a22b12ce6e5f-basic.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}e78b17bb-11fb-4860-8d66-4ee0d0c1e117.woff2") format("woff2"),
       url("#{$calcite-fonts-path}d4ffabb3-dd7c-472a-bdfb-6700383c6354.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  font-display: $calcite-fonts-display;
}

/*
 ┌──────────────────┐
 │ Latin Extended   │
 └──────────────────┘
 Latin Extended A, Latin Extended B, Spacing Modifier Characters, Latin Extended Additional
 */
@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}77156710-6a58-4606-b189-b4185e75967b-ext.woff2") format("woff2"),
       url("#{$calcite-fonts-path}3d5260a1-e4cd-4567-80ed-69d23c40355f-ext.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  unicode-range: U+0100-017F, U+0180-024F, U+1E00-1EFF, U+02B0-02FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}77caabd3-1877-4634-85c8-8e398a093b99-ext.woff2") format("woff2"),
       url("#{$calcite-fonts-path}e388ac99-8c6a-4451-8690-1d15b4d45adb-ext.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  unicode-range: U+0100-017F, U+0180-024F, U+1E00-1EFF, U+02B0-02FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}014f2daa-c310-4a36-b9fd-79a8e0c48d44-ext.woff2") format("woff2"),
       url("#{$calcite-fonts-path}12b00842-ec20-4c7f-aa72-802fb00f6cc4-ext.woff") format("woff");
  font-weight: 400;
  font-style: italic;
  unicode-range: U+0100-017F, U+0180-024F, U+1E00-1EFF, U+02B0-02FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}b0b84e4d-2164-45c7-a674-1662f19f3ba6-ext.woff2") format("woff2"),
       url("#{$calcite-fonts-path}e91d1bbf-3fea-45e2-b003-a22b12ce6e5f-ext.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  unicode-range: U+0100-017F, U+0180-024F, U+1E00-1EFF, U+02B0-02FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}e78b17bb-11fb-4860-8d66-4ee0d0c1e117-ext.woff2") format("woff2"),
       url("#{$calcite-fonts-path}d4ffabb3-dd7c-472a-bdfb-6700383c6354-ext.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  unicode-range: U+0100-017F, U+0180-024F, U+1E00-1EFF, U+02B0-02FF;
  font-display: $calcite-fonts-display;
}

/*
 ┌───────┐
 │ Greek │
 └───────┘
 Greek and Coptic
 */
@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}77156710-6a58-4606-b189-b4185e75967b-greek.woff2") format("woff2"),
       url("#{$calcite-fonts-path}3d5260a1-e4cd-4567-80ed-69d23c40355f-greek.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  unicode-range: U+0370-03FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}77caabd3-1877-4634-85c8-8e398a093b99-greek.woff2") format("woff2"),
       url("#{$calcite-fonts-path}e388ac99-8c6a-4451-8690-1d15b4d45adb-greek.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  unicode-range: U+0370-03FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}014f2daa-c310-4a36-b9fd-79a8e0c48d44-greek.woff2") format("woff2"),
       url("#{$calcite-fonts-path}12b00842-ec20-4c7f-aa72-802fb00f6cc4-greek.woff") format("woff");
  font-weight: 400;
  font-style: italic;
  unicode-range: U+0370-03FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}a1049d00-54ad-4589-95b8-d353f7ab52f0-greek.woff2") format("woff2"),
       url("#{$calcite-fonts-path}13faf0ae-dcab-4d1c-9c08-f9ca339b6023-greek.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  unicode-range: U+0370-03FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}e78b17bb-11fb-4860-8d66-4ee0d0c1e117-greek.woff2") format("woff2"),
       url("#{$calcite-fonts-path}d4ffabb3-dd7c-472a-bdfb-6700383c6354-greek.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  unicode-range: U+0370-03FF;
  font-display: $calcite-fonts-display;
}

/*
 ┌──────────┐
 │ Cyrillic │
 └──────────┘
 Cyrillic
 */
@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}174d458a-81e0-4174-9473-35e3bf0a613c.woff2") format("woff2"),
       url("#{$calcite-fonts-path}57a79aa3-9b06-4ba7-a9a4-2b766d826ecf.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  unicode-range: U+0400-04FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}7db1f672-3a8f-4d19-9c49-7f61aed450b5.woff2") format("woff2"),
       url("#{$calcite-fonts-path}4ab86b35-c0c2-42b5-98ad-4b6eba66b197.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  unicode-range: U+0400-04FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}b17468ea-cf53-4635-984b-4d930a68ed4d.woff2") format("woff2"),
       url("#{$calcite-fonts-path}4d1d0d0d-9ea6-4117-901f-8b32ca1ab936.woff") format("woff");
  font-weight: 400;
  font-style: italic;
  unicode-range: U+0400-04FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}b0b84e4d-2164-45c7-a674-1662f19f3ba6.woff2") format("woff2"),
       url("#{$calcite-fonts-path}e91d1bbf-3fea-45e2-b003-a22b12ce6e5f.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  unicode-range: U+0400-04FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}40d36b4a-60c6-460a-bf43-4c948c23563e.woff2") format("woff2"),
       url("#{$calcite-fonts-path}45b78f45-e639-4836-8612-e0892e120f14.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  unicode-range: U+0400-04FF;
  font-display: $calcite-fonts-display;
}

/*
 ┌──────────┐
 │ Georgian │
 └──────────┘
 Georgian
 */
@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}281f890c-8412-4ee3-84ed-8b5d062d2ab8.woff2") format("woff2"),
       url("#{$calcite-fonts-path}5729f02e-f6b0-4f35-8ee5-c2cffa65fa76.woff") format("woff");
  font-weight: 300; /* there is no 300 in georgian, so use the same files as 400 instead */
  font-style: normal;
  unicode-range: U+10A0-10FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}281f890c-8412-4ee3-84ed-8b5d062d2ab8.woff2") format("woff2"),
       url("#{$calcite-fonts-path}5729f02e-f6b0-4f35-8ee5-c2cffa65fa76.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  unicode-range: U+10A0-10FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}1fed34fa-250a-4d32-9f1d-42f978a2e0b2.woff2") format("woff2"),
       url("#{$calcite-fonts-path}e57662ff-b1ef-4122-88c1-61bbdabeb365.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  unicode-range: U+10A0-10FF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}2200dfff-da50-40b0-bc12-5e4b872a1998.woff2") format("woff2"),
       url("#{$calcite-fonts-path}dc10b3bd-5076-4df5-a5f5-e5961f4a6938.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  unicode-range: U+10A0-10FF;
  font-display: $calcite-fonts-display;
}

/*
 ┌────────┐
 │ Arabic │
 └────────┘
 Arabic, Arabic Presentation Forms A, Arabic Presentation Forms B
 */
@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}2a1ae9a5-b6b5-405c-b660-bbdf1b356952.woff2") format("woff2"),
       url("#{$calcite-fonts-path}a8aeea1b-1a9d-45b7-8ad9-7c71824599e2.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  unicode-range: U+0600-06FF, U+FB50-FDFF, U+FE70-FEFF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}6ea5fa46-5311-450b-8744-288a30c55348.woff2") format("woff2"),
       url("#{$calcite-fonts-path}d9e4040d-32ff-4a1c-ac04-927a781da1f5.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  unicode-range: U+0600-06FF, U+FB50-FDFF, U+FE70-FEFF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}97694c53-4e94-4f9e-969b-a148adfcdcfd.woff2") format("woff2"),
       url("#{$calcite-fonts-path}8b01637a-f445-4f10-92ea-b84a355f7690.woff") format("woff");
  font-weight: 500; /* there is no 500 in Neue Helvetica Arabic, so use the same files as 700 instead */
  font-style: normal;
  unicode-range: U+0600-06FF, U+FB50-FDFF, U+FE70-FEFF;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}97694c53-4e94-4f9e-969b-a148adfcdcfd.woff2") format("woff2"),
       url("#{$calcite-fonts-path}8b01637a-f445-4f10-92ea-b84a355f7690.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  unicode-range: U+0600-06FF, U+FB50-FDFF, U+FE70-FEFF;
  font-display: $calcite-fonts-display;
}

/*
 ┌────────┐
 │ Hebrew │
 └────────┘
 Hebrew, Alphabetic Presentation Forms
 */
@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}31da4b04-f98a-4b5f-b545-a31d26da99e5.woff2") format("woff2"),
       url("#{$calcite-fonts-path}d98fb015-7ef6-404f-a58a-5c9242d79770.woff") format("woff");
  font-weight: 300; /* there is no 300 in hebrew, so use the same files as 400 instead */
  font-style: normal;
  unicode-range: U+0590-05FF, U+FB00-FB4F;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}31da4b04-f98a-4b5f-b545-a31d26da99e5.woff2") format("woff2"),
       url("#{$calcite-fonts-path}d98fb015-7ef6-404f-a58a-5c9242d79770.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  unicode-range: U+0590-05FF, U+FB00-FB4F;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}32a2c5cf-6736-44a6-a276-49ba7e030944.woff2") format("woff2"),
       url("#{$calcite-fonts-path}fa71df11-7b19-4baf-8ff7-3537dea718f0.woff") format("woff");
  font-weight: 400;
  font-style: italic;
  unicode-range: U+0590-05FF, U+FB00-FB4F;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}a9eaf4d3-6427-42df-9306-3ea1270f7b1a.woff2") format("woff2"),
       url("#{$calcite-fonts-path}f4a085c3-1c64-4fc0-a598-26f3e658c2b0.woff") format("woff");
  font-weight: 500; /* Helvetica Hebrew doesn't have 500 so use 700 weight files */
  font-style: normal;
  unicode-range: U+0590-05FF, U+FB00-FB4F;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}a9eaf4d3-6427-42df-9306-3ea1270f7b1a.woff2") format("woff2"),
       url("#{$calcite-fonts-path}f4a085c3-1c64-4fc0-a598-26f3e658c2b0.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  unicode-range: U+0590-05FF, U+FB00-FB4F;
  font-display: $calcite-fonts-display;
}

/*
 ┌───────┐
 │ Hindi │
 └───────┘
 Devangari
 */
@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}94aa531e-7746-4df0-bb6e-349891f2eda5.woff2") format("woff2"),
       url("#{$calcite-fonts-path}121524c1-8d82-4155-bfb3-fd2f15f09e93.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  unicode-range: U+0900-097F;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}3ae1e25e-3aa6-4061-a016-a079159f9d65.woff2") format("woff2"),
       url("#{$calcite-fonts-path}f1799750-0952-403f-8108-b2402eed0f62.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  unicode-range: U+0900-097F;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}41331c3c-3759-4462-8695-33c9a21b6a5b.woff2") format("woff2"),
       url("#{$calcite-fonts-path}31e0c094-e345-4a54-a797-d5f1a5885572.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  unicode-range: U+0900-097F;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "Avenir Next";
  src: url("#{$calcite-fonts-path}41331c3c-3759-4462-8695-33c9a21b6a5b.woff2") format("woff2"),
       url("#{$calcite-fonts-path}31e0c094-e345-4a54-a797-d5f1a5885572.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  unicode-range: U+0900-097F;
  font-display: $calcite-fonts-display;
}

/*
 ┌───────┐
 │ Thai  │
 └───────┘
 Thai
 SST Thai
 */
// @font-face {
//   font-family: "Avenir Next";
//   src: url("#{$calcite-fonts-path}8a65abc7-6463-4895-b3e6-5e203b80b7e2.woff2") format("woff2"),
//        url("#{$calcite-fonts-path}89370128-6a74-471d-ba04-fa0b59bc8ed3.woff") format("woff");
//   font-weight: 300;
//   font-style: normal;
//   unicode-range: U+0E00-0E7F;
// }

// @font-face {
//   font-family: "Avenir Next";
//   src: url("#{$calcite-fonts-path}dd7b7d4e-21ab-4ac0-b26a-d032c992f3c5.woff2") format("woff2"),
//        url("#{$calcite-fonts-path}ae454969-59fb-4d42-aecb-dea30459bc7e.woff") format("woff");
//   font-weight: 400;
//   font-style: normal;
//   unicode-range: U+0E00-0E7F;
// }

// @font-face {
//   font-family: "Avenir Next";
//   src: url("#{$calcite-fonts-path}75e2f3b1-1402-4916-8392-4db9ababea7b.woff2") format("woff2"),
//        url("#{$calcite-fonts-path}c744f2a2-d6a9-4c18-b0ec-f17376acde3e.woff") format("woff");
//   font-weight: 500;
//   font-style: normal;
//   unicode-range: U+0E00-0E7F;
// }

// @font-face {
//   font-family: "Avenir Next";
//   src: url("#{$calcite-fonts-path}4e4befe6-3f94-41d8-886c-1a6831818c6a.woff2") format("woff2"),
//        url("#{$calcite-fonts-path}61f13f40-3829-413c-b6f5-b11abb272edc.woff") format("woff");
//   font-weight: 700;
//   font-style: normal;
//   unicode-range: U+0E00-0E7F;
// }

/*
 ┌────────────┐
 │ Vietnamese │
 └────────────┘
 In Vietnamese, we render the entire page in SST
 These font files include Basic, Extended, _and_ Vietnamese characters
*/
@font-face {
  font-family: "SST Vietnamese";
  src: url("#{$calcite-fonts-path}c4cc9032-7eee-4a6e-ae8b-f384b1349bcf.woff2") format("woff2"),
       url("#{$calcite-fonts-path}1b3078ef-2971-4c95-b6ca-13ab528758cb.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "SST Vietnamese";
  src: url("#{$calcite-fonts-path}c1905e2e-a1cb-49de-9bb0-ce3c5ffc85ae.woff2") format("woff2"),
       url("#{$calcite-fonts-path}341bcc5e-7ac0-44ff-819d-5887892eab1b.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "SST Vietnamese";
  src: url("#{$calcite-fonts-path}18629a56-2ec3-4470-a65f-f82d7ec4d41b.woff2") format("woff2"),
       url("#{$calcite-fonts-path}439a2ded-d7a4-42d4-8660-ef8765fa7486.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: $calcite-fonts-display;
}

@font-face {
  font-family: "SST Vietnamese";
  src: url("#{$calcite-fonts-path}4daa2125-53c6-4da8-9614-8a1049eaccc2.woff2") format("woff2"),
       url("#{$calcite-fonts-path}0763eab1-d6ed-4c73-afb0-895f930df099.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  font-display: $calcite-fonts-display;
}
