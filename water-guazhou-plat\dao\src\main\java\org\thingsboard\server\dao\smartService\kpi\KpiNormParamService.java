package org.thingsboard.server.dao.smartService.kpi;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormParam;

import java.util.List;

/**
 * 黑名单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface KpiNormParamService {
    PageData getList(String source, String type, String code, String name, int page, int size, Boolean enabled, String tenantId);

    KpiNormParam save(KpiNormParam kpiNormParam);

    int delete(List<String> ids);

    boolean checkCode(KpiNormParam kpiNormParam);
}
