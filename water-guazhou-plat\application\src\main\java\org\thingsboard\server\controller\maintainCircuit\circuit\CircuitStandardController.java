package org.thingsboard.server.controller.maintainCircuit.circuit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintainCircuit.circuit.CircuitStandardService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitStandard;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-07
 */
@RestController
@RequestMapping("api/circuit/standard/m")
public class CircuitStandardController extends BaseController {

    @Autowired
    private CircuitStandardService circuitStandardService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String deviceTypeId, @RequestParam(required = false, defaultValue = "") String keywords,
                                 int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(circuitStandardService.getList(deviceTypeId, keywords, page, size, tenantId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody CircuitStandard circuitStandard) throws ThingsboardException {
        circuitStandard.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        circuitStandard.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(circuitStandardService.save(circuitStandard));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return circuitStandardService.delete(ids);
    }
}
