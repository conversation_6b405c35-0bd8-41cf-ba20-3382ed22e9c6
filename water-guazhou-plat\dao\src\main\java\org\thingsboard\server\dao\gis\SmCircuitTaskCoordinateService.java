package org.thingsboard.server.dao.gis;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SmCircuitTaskCoordinate;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SmCircuitTaskCoordinatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SmCircuitTaskCoordinateSaveRequest;

public interface SmCircuitTaskCoordinateService {
    /**
     * 分页条件查询任务巡检路径
     * @param request 分页条件查询条件
     * @return 任务巡检路径
     */
    IPage<SmCircuitTaskCoordinate> findAllConditional(SmCircuitTaskCoordinatePageRequest request);

    /**
     * 保存
     * @param entity 实体
     * @return 保存好的实体
     */
    SmCircuitTaskCoordinate save(SmCircuitTaskCoordinateSaveRequest entity);

    /**
     * 增量更新巡检路径，基本不可能会用到
     * @param entity 补丁实体
     * @return 是否更新成功
     */
    boolean update(SmCircuitTaskCoordinate entity);

    /**
     * 删除某个巡检路径点，基本不可能会用到
     * @param id 路径点id
     * @return 是否删除成功
     */
    boolean delete(String id);

}
