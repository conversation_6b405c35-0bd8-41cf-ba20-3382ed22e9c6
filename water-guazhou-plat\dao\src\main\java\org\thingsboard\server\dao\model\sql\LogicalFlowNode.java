package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;

@Data
@Entity
@EqualsAndHashCode
@Table(name = ModelConstants.TABLE_LOGICAL_FLOW_NODE)
public class LogicalFlowNode {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.LOGICAL_FLOW_NODE_PARENT_ID)
    private String parentId;

    /**
     * 节点类型
     */
    @Column(name = ModelConstants.LOGICAL_FLOW_NODE_TYPE)
    private String type;

    /**
     * 需要执行的js
     */
    @Column(name = ModelConstants.LOGICAL_FLOW_NODE_SCRIPT)
    private String script;

    /**
     * 当前节点的相关参数
     * 持续: 持续时间
     * 间隔: 间隔
     */
    @Column(name = ModelConstants.LOGICAL_FLOW_NODE_PARAM)
    private String param;

    /**
     * 节点执行顺序(仅按次序执行需要使用)
     */
    @Column(name = ModelConstants.LOGICAL_FLOW_NODE_ORDER)
    private Integer order;

    @Column(name = ModelConstants.CREATE_TIME)
    private Long createTime;

    /**
     * 逻辑流程ID
     */
    @Column(name = ModelConstants.LOGICAL_FLOW_ID)
    private String logicalFlowId;

    @Column(name = ModelConstants.ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.NAME)
    private String name;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.LOGICAL_FLOW_NODE_REMARK)
    private String remark;

}
