import{_ as U}from"./ArcView-DpMnCY82.js";import{d as F,r as G,ad as A,am as I,o as P,Q as $,g,n as v,p as h,q as k,i as d,an as N,e4 as R,C as S,c as w,h as B,F as z,t as M}from"./index-r0dFAfgr.js";import{g as E,n as L}from"./MapView-DaoQedLH.js";import{w as O}from"./Point-WxyopZva.js";import{g as T}from"./URLHelper-B9aplt5w.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as W}from"./ViewHelper-BGCZjxXH.js";import{_ as j}from"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import{I as q}from"./utils-D5nxoMq3.js";const Q={class:"location-search-box"},D={class:"location-input-box"},H={key:0,class:"message-text"},J=F({__name:"ArcLocatePicker",props:{modelValue:{},showInput:{type:Boolean},row:{},disabled:{type:[Boolean,Function]},readonly:{type:[Boolean,Function]}},emits:["change","update:modelValue","loaded"],setup(y,{expose:V,emit:b}){var u,m;const i=b,r=y,o=G({lon:(u=r.modelValue)==null?void 0:u[0],lat:(m=r.modelValue)==null?void 0:m[1]}),n=A("view"),s={},_=e=>{n&&(s.centerMark=new E({geometry:e,symbol:new L({url:T("水厂.png"),width:25,height:30,yoffset:15})}),n.graphics.removeAll(),n.graphics.add(s.centerMark))},a=()=>{l(!0),i("update:modelValue",[o.lon,o.lat]),i("change",[o.lon,o.lat])};I(()=>r.modelValue,()=>{var e,t,f,p,C;if(((e=r.modelValue)==null?void 0:e.length)===2){if(((t=r.modelValue)==null?void 0:t[0])===o.lon&&((f=r.modelValue)==null?void 0:f[1])===o.lat)return;o.lon=(p=r.modelValue)==null?void 0:p[0],o.lat=(C=r.modelValue)==null?void 0:C[1],l(!0)}});const l=e=>{const t=new O({longitude:o.lon,latitude:o.lat,spatialReference:n==null?void 0:n.spatialReference});e?n==null||n.goTo({zoom:16,target:t},{duration:500}).then(()=>{_(t)}):_(t)};return P(()=>{l(!0),W(n,e=>{if(console.log(e),!e.screenPoint||!n)return;const t=n==null?void 0:n.toMap(e.screenPoint);o.lon=t==null?void 0:t.longitude,o.lat=t==null?void 0:t.latitude,i("update:modelValue",[o.lon,o.lat]),i("change",[o.lon,o.lat]),l()})}),$(()=>{var e,t;(e=s.extentWatcher)==null||e.remove(),(t=s.stationaryWatcher)==null||t.remove()}),V({getView:()=>n}),(e,t)=>{const f=R;return g(),v("div",Q,[h("div",D,[t[2]||(t[2]=h("span",{class:"location-label"},"经度：",-1)),k(f,{modelValue:d(o).lon,"onUpdate:modelValue":t[0]||(t[0]=p=>d(o).lon=p),disabled:typeof e.disabled=="function"?e.disabled(d(o).lat,e.row,{type:"form-map"}):e.disabled,readonly:typeof e.readonly=="function"?e.readonly(d(o).lat,e.row,{type:"form-map"}):e.readonly,precision:4,size:"small",class:"location-input",onChange:a},null,8,["modelValue","disabled","readonly"]),t[3]||(t[3]=h("span",{class:"location-label margin-l-10"},"纬度：",-1)),k(f,{modelValue:d(o).lat,"onUpdate:modelValue":t[1]||(t[1]=p=>d(o).lat=p),disabled:typeof e.disabled=="function"?e.disabled(d(o).lat,e.row,{type:"form-map"}):e.disabled,readonly:typeof e.readonly=="function"?e.readonly(d(o).lat,e.row,{type:"form-map"}):e.readonly,precision:4,size:"small",class:"location-input",onChange:a},null,8,["modelValue","disabled","readonly"])]),e.disabled?N("",!0):(g(),v("p",H,"提示：请点击地图 设置位置信息"))])}}}),K=S(J,[["__scopeId","data-v-0c22960b"]]),X=F({__name:"FormMap",props:{modelValue:{},showInput:{type:Boolean},row:{},disabled:{type:[Boolean,Function]},readonly:{type:[Boolean,Function]},handleInverseGeocodeing:{type:Function},mapConfig:{}},emits:["change","update:modelValue","loaded"],setup(y,{expose:V,emit:b}){const i=w(),r=b,o=y,n=G({...window.SITE_CONFIG.FORM.GIS_CONFIG,...o.mapConfig}),s=w(o.modelValue);return I(()=>o.modelValue,a=>{s.value=a}),I(()=>s.value,a=>{var l,c;a!==o.modelValue&&(r("update:modelValue",a),r("change",a),q({lon:(l=(a==null?void 0:a[0])||"0")==null?void 0:l.toString(),lat:(c=(a==null?void 0:a[1])||"0")==null?void 0:c.toString()}).then(u=>{var m;(m=o.handleInverseGeocodeing)==null||m.call(o,u,o.row)}))}),V({getView:()=>{var a;return(a=i.value)==null?void 0:a.getView()}}),(a,l)=>{const c=j,u=K,m=U;return g(),B(m,{ref_key:"refMap",ref:i,modelValue:d(s),"onUpdate:modelValue":l[1]||(l[1]=e=>M(s)?s.value=e:null),class:"location-map","map-config":d(n),onLoaded:l[2]||(l[2]=e=>r("loaded",e))},{default:z(()=>[k(c),a.showInput?(g(),B(u,{key:0,modelValue:d(s),"onUpdate:modelValue":l[0]||(l[0]=e=>M(s)?s.value=e:null),class:"locate-search-box"},null,8,["modelValue"])):N("",!0)]),_:1},8,["modelValue","map-config"])}}}),pe=S(X,[["__scopeId","data-v-b9ba3865"]]);export{pe as _};
