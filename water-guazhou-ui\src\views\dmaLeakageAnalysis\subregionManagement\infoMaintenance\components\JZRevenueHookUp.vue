<template>
  <div class="manual-hookuo-revenue">
    <div class="left">
      <div class="title">总数据</div>
      <Search ref="refSearchLeft" class="search" :config="Search_Left"></Search>
      <FormTable class="table-box" :config="Table_Left"></FormTable>
    </div>
    <div class="center">
      <div class="btn">
        <el-button type="primary" @click="hookUpUser">
          <Icon :icon="'ep:d-arrow-right'"></Icon>
        </el-button>
      </div>
      <div class="btn">
        <el-button type="primary" @click="hookDownUser">
          <Icon :icon="'ep:d-arrow-left'"></Icon>
        </el-button>
      </div>
      <div class="btn">
        <el-button type="success" @click="hooksaveUser"> 保存 </el-button>
      </div>
    </div>
    <div class="right">
      <div class="title">已挂接数据</div>
      <FormTable class="table-box" :config="Table_Right"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import {
  GetDMAUserList,
  GetDMAUserPartitionList,
  postDMAUserList
} from '@/api/mapservice/dma';
import { SLMessage } from '@/utils/Message';

const refSearchLeft = ref<ISearchIns>();

const props = defineProps<{ currentTreeNode?: NormalOption }>();

const state = reactive({
  data: []
});

const Search_Left = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: '名称',
      field: 'MeterReadingNO'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => {
            const key = refSearchLeft.value?.queryParams.MeterReadingNO.trim();
            Table_Left.dataList = state.data.filter((item: any) => {
              return item.MeterReadingNO.includes(key);
            });
          }
        }
      ]
    }
  ]
});

const Table_Left = reactive<ITable>({
  height: 400,
  pagination: {
    hide: true
  },
  dataList: [],
  columns: [
    { minWidth: 120, label: 'ID', prop: 'MeterReadingID', fixed: 'left' },
    { minWidth: 120, label: '名称', prop: 'MeterReadingNO', fixed: 'left' }
  ],
  handleSelectChange: (rows) => {
    Table_Left.selectList = rows || [];
  }
});

const Table_Right = reactive<ITable>({
  height: 400,
  pagination: {
    hide: true
  },
  dataList: [],
  columns: [
    { minWidth: 120, label: 'ID', prop: 'MeterReadingID', fixed: 'left' },
    { minWidth: 120, label: '名称', prop: 'MeterReadingNO', fixed: 'left' }
  ],
  handleSelectChange: (rows) => {
    Table_Right.selectList = rows || [];
  }
});

function findItemsByIds(items, targetIds) {
  return items.filter((item) => targetIds.includes(item.MeterReadingID));
}

function removeDuplicatesByProperty(items, property) {
  const seen = new Map();
  const uniqueItems: any[] = [];

  for (const item of items) {
    if (!seen.has(item[property])) {
      seen.set(item[property], true);
      uniqueItems.push(item);
    }
  }

  return uniqueItems;
}

const refreshLeftData = async () => {
  Table_Left.loading = true;
  try {
    const res = await GetDMAUserList();
    const data = res.data.data || {};
    state.data = data || [];
    Table_Left.dataList = data || [];
    refreshRightData();
  } catch (error) {
    //
  }
  Table_Left.loading = false;
};
const refreshRightData = async () => {
  Table_Right.loading = true;
  try {
    const res = await GetDMAUserPartitionList(props.currentTreeNode?.value);

    const data = (res.data.data.bookId || '').split(',');
    Table_Right.dataList = findItemsByIds(Table_Left.dataList, data);
  } catch (error) {
    //
  }
  Table_Right.loading = false;
};
const hookUpUser = async () => {
  const users = Table_Left.selectList || [];
  if (!users.length) {
    SLMessage.warning('请选择要挂接的用户');
    return;
  }
  try {
    Table_Right.dataList = removeDuplicatesByProperty(
      [...Table_Right.dataList, ...users],
      'MeterReadingID'
    );
    Table_Left.selectList = [];
  } catch (error) {
    SLMessage.error('操作失败');
  }
};
const hookDownUser = async () => {
  const users = Table_Right.selectList || [];
  if (!users.length) {
    SLMessage.warning('请先选择要取消挂接的用户');
    return;
  }
  try {
    Table_Right.dataList = Table_Right.dataList.filter(
      (item) => !users.includes(item)
    );
    Table_Right.selectList = [];
  } catch (error) {
    SLMessage.error('操作失败');
  }
};

const hooksaveUser = async () => {
  const params = {
    id: props.currentTreeNode?.id || '',
    bookId: Table_Right.dataList.map((item) => item.MeterReadingID).join(','),
    name: props.currentTreeNode?.label || ''
  };
  postDMAUserList(params).then((res) => {
    if (res.data.code == 200) {
      SLMessage.success('操作成功');
      refreshRightData();
    }
  });
};
onMounted(() => {
  refreshLeftData();
});
</script>
<style lang="scss" scoped>
.manual-hookuo-revenue {
  display: flex;
  .title {
    background-color: var(--el-bg-color);
    height: 36px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    border-bottom: 1px solid var(--el-border-color);
    margin-bottom: 8px;
    font-weight: bold;
    font-size: 16px;
  }
  .left,
  .right {
    width: calc(50% - 50px);
    border: 1px solid var(--el-border-color);
    .search {
      margin-bottom: 8px;
    }
  }
  .center {
    width: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .btn {
      padding: 12px 0;
    }
  }
  .table-box {
    height: calc(100% - 40px);
  }
}
</style>
