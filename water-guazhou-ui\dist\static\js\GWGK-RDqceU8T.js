import{_ as i}from"./compound-uu7Zq9wL.js";import m from"./RoundTargetItem-BwABRiCQ.js";import u from"./ScrollList-C0VFzhoB.js";import{d,r as p,g as e,n as o,p as a,aB as r,aJ as f,h as v,aw as _,i as n,q as g,C as w}from"./index-r0dFAfgr.js";import"./TargetItem-MDYkIAGN.js";import"./index-BlG8PIOK.js";const x={class:"gwgk-info"},k=d({__name:"GWGK",setup(B){const t=p({data:[{name:"管线长度",value:"-",scale:"-"},{name:"巡检次数",value:"30",scale:"100%"},{name:"抢修次数",value:"1135",scale:"100%"},{name:"阀门数量",value:"-",scale:"-"}],info:[{value:"0",label:"综合漏损率(%)",scale:"0%",text:"同比",status:"down",rows:[1,3],className:"total-loss-rate"},{value:"100.00",label:"漏损率(%)",scale:"0%",text:"同比",status:"up",rows:[1,3],className:"loss-rate"}]});return(N,s)=>(e(),o(r,null,[a("div",x,[(e(!0),o(r,null,f(n(t).info,(l,c)=>(e(),v(m,{key:c,config:l,class:_(l.className)},null,8,["config","class"]))),128)),s[0]||(s[0]=a("img",{width:"220",class:"circle-image",src:i,alt:""},null,-1)),s[1]||(s[1]=a("div",{class:"prosale-ratio"},[a("div",{class:"value"}," 0.00 % "),a("div",{class:"text"}," 产销差率 ")],-1))]),g(u,{data:n(t).data},null,8,["data"])],64))}}),V=w(k,[["__scopeId","data-v-7cfba9b6"]]);export{V as default};
