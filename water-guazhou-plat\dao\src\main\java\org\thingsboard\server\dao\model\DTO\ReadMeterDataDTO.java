package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.thingsboard.server.dao.model.sql.revenue.ReadMeterData;

@EqualsAndHashCode(callSuper = true)
@Data
public class ReadMeterDataDTO extends ReadMeterData {

    private String userId;

    private String userName;// 用户名称

    private String meterCopyUserName;// 抄表员名称

    private String userAddress;// 用户地址

    private String meterBookId;// 册本ID

    private String meterBookCode;// 册本编号

    private String meterBookName;// 册本名称

    private String waterCategory;// 用水类别

    private String waterCategoryName;// 用水类别

    private String meterCode;// 水表编号

    private String meterType;// 水表类型

    private String meterTypeName;// 水表类型

    private String caliber;// 水表口径

    private String orgName;// 供水单位名称

    private String executeUser;// 抄表员

}
