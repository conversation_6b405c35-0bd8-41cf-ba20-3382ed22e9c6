package org.thingsboard.server.dao.util.imodel.query.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.plan.PlanDetail;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class PlanDetailSaveRequest extends SaveRequest<PlanDetail> {

    // 盘点计划主表ID
    @NotNullOrEmpty(refTable = "plan", parentIgnore = true)
    private String mainId;

    // 设备编码
    @NotNullOrEmpty
    private String serialId;

    // 货架ID
    private String shelvesId;

    public PlanDetail build() {
        PlanDetail entity = new PlanDetail();

        entity.setTenantId(tenantId());
        entity.setMainId(mainId);
        commonSet(entity);
        return entity;
    }

    public PlanDetail update(String id) {
        disallowUpdate();
        PlanDetail entity = new PlanDetail();
        entity.setId(id);
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    private void commonSet(PlanDetail entity) {
        entity.setSerialId(serialId);
        entity.setShelvesId(shelvesId);
    }
}