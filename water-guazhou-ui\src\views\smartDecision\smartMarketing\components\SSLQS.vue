<!-- 费量趋势 -->
<template>
  <div class="chart-wrapper">
    <VChart ref="refChart" :option="state.option"></VChart>
  </div>
</template>
<script lang="ts" setup>
const state = reactive<{ option: any }>({
  option: {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      textStyle: {
        color: 'rgb(60, 148, 221)'
      },
      bottom: 20
    },
    grid: {
      containLabel: true,
      left: 20,
      top: 40,
      right: 20,
      bottom: 40
    },
    xAxis: {
      type: 'category',
      data: ['01月', '02月', '03月', '04月'],
      boundaryGap: false
    },
    yAxis: [
      {
        type: 'value',
        name: 'm³',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: 'rgb(60, 148, 221)'
          }
        }
      },
      {
        type: 'value',
        name: '元',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '水量',
        type: 'bar',
        xAxisIndex: 0,
        data: [100, 200, 300, 100],
        barWidth: 15
      },
      {
        name: '同期水量',
        type: 'bar',
        yAxisIndex: 0,
        data: [178, 321, 456, 211],
        barWidth: 15
      },
      {
        name: '水费',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'none',
        data: [200, 400, 600, 200]
      }
    ]
  }
});
</script>
<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style>
