import{d as X,a6 as Z,c as w,r as k,bB as x,o as tt,ay as at,g as A,h as et,F as S,p as P,q as D,bo as ot,n as it,i as rt,j as st,_ as nt,bt as lt,aq as pt,c5 as mt,br as ct,a0 as ut,C as dt}from"./index-r0dFAfgr.js";import{w as z}from"./Point-WxyopZva.js";import{u as ft}from"./useStation-DJgnSZIA.js";import{d as gt}from"./zhandian-YaGuQZe6.js";import yt from"./RightDrawerMap-D5PhmGFO.js";import"./MapView-DaoQedLH.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as vt}from"./ViewHelper-BGCZjxXH.js";import{s as bt}from"./flowMonitoring-DtJlPj0G.js";import{a as ht}from"./headwaterMonitoring-BgK7jThW.js";import{p as B,l as wt}from"./echart-Bd1EZNhy.js";import{r as j}from"./data-fN2LTS5W.js";import{a as Dt}from"./onemap-CEunQziB.js";import{g as $}from"./URLHelper-B9aplt5w.js";import"./ArcView-DpMnCY82.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./widget-BcWKanF2.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./DateFormatter-Bm9a68Ax.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const _t={class:"content"},kt={class:"right-box"},Rt=X({__name:"index",setup(Ct){const q=Z(),G=w(),{getAllStationOption:U}=ft(),I=w(),L=w(),T=w(!0),N=w(),W=k([]),Y=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],b=w([]),s=k({lineOption:null,pieOption:null,stationStatus:[],curRow:{},activeName:null,data:null,stationLocation:[],tableData:[],windows:[]}),f=k({type:"tabs",tabType:"simple",tabs:[],handleTabClick:i=>{const r=f.tabs.find(l=>l.value===i.props.name)||{};R(s.curRow,r.data)}}),h={},_=async i=>{var e,n,c,g,d,v;const r=y.dataList.find(o=>o.stationId===i),l=s.tableData.find(o=>o.stationId===i);f.tabs=(e=l==null?void 0:l.dataList)==null?void 0:e.map((o,u)=>({label:o.propertyName,value:o.property+u,data:o})),s.activeName=(n=f.tabs[0])==null?void 0:n.value,s.curRow=r,R(r,(c=f.tabs[0])==null?void 0:c.data);let a;if(!i)a=(g=b.value)==null?void 0:g[0];else if(a=b.value.find(o=>{var u;return((u=o.attributes)==null?void 0:u.stationId)===i&&o.symbolType==="marker"}),a&&h.view){const o=new z({longitude:a.longitude,latitude:a.latitude,spatialReference:h.view.spatialReference});await h.view.goTo({target:o,zoom:15})}if(!a)return;const p=a.attributes,t=((d=(await gt(p.id)).data)==null?void 0:d.map(o=>(o.label=o.propertyName,o.value,o)))||[];s.windows.length=0,s.windows.push({visible:!1,x:a.longitude,y:a.latitude,offsetY:-30,title:p.name,attributes:{values:t,id:p.id}}),await x(),(v=I.value)==null||v.openPop(p.id)},M=k({group:[{id:"chart",fieldset:{desc:"压力监测设备",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:j(),style:{height:"150px"}}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),y=k({loading:!0,dataList:[],columns:[],highlightCurrentRow:!0,currentRowKey:"stationId",handleRowClick:async i=>{var a,p,m;const r=b.value.find(t=>{var e;return((e=t.attributes)==null?void 0:e.stationId)===i.stationId&&t.symbolType==="marker"});if(r&&h.view){const t=new z({longitude:r.longitude,latitude:r.latitude,spatialReference:h.view.spatialReference});await h.view.goTo({target:t,zoom:15})}const l=s.tableData.find(t=>t.stationId===i.stationId);f.tabs=(a=l==null?void 0:l.dataList)==null?void 0:a.map((t,e)=>({label:t.propertyName,value:t.property+e,data:t})),s.activeName=(p=f.tabs[0])==null?void 0:p.value,R((m=f.tabs[0])==null?void 0:m.data),y.currentRow=i,s.curRow=i,_(i.stationId)},pagination:{hide:!0}}),E=async i=>{var a,p;console.log("=== addMarks 开始执行 ===");const r=await U("压力监测站,测流压站");s.stationStatus=r,console.log("压力监测站",r),b.value=[],console.log("清空聚合数据，当前长度:",b.value.length);const l=[];r.map((m,t)=>{var v,o;const e=m.data,n=(v=e==null?void 0:e.location)==null?void 0:v.split(","),c=(o=s.stationStatus)==null?void 0:o.find(u=>u.id===m.id),g=c.status==="online"?$("压力监测站.png"):$("压力监测站.png"),d={id:`marker_${t}`,name:e.name||"未命名站点",longitude:parseFloat((n==null?void 0:n[0])||"0"),latitude:parseFloat((n==null?void 0:n[1])||"0"),attributes:{ObjectID:t+1,name:e.name||"未命名站点",id:e.id,stationId:e.id,stationType:e.type,location:e.location,status:(c==null?void 0:c.status)||"unknown"},symbolType:"marker",symbolConfig:{width:25,height:30,yoffset:15,url:g}};l.push(d)}),console.log("=== 准备设置 clusterData ==="),console.log("simpleDataArray length:",l.length),console.log("simpleDataArray:",l),b.value=l,console.log("=== clusterData 已设置 ==="),console.log("clusterDataRef.value length:",b.value.length),s.stationLocation=r,s.curRow=(a=r[0])==null?void 0:a.data,y.currentRow=s.curRow,_((p=s.curRow)==null?void 0:p.id)},K=async()=>{var i;bt({stationType:"压力监测站,测流压站",projectId:(i=ut().selectedProject)==null?void 0:i.value}).then(r=>{var l,a,p,m,t,e,n,c;if(s.tableData=r.data,W){const g=(a=(l=s.tableData[0])==null?void 0:l.dataList)==null?void 0:a.map(o=>({prop:o.property,label:o.propertyName,unit:o.unit,minWidth:120}));y.loading=!1;const d=(p=s.tableData)==null?void 0:p.map(o=>{var V,F;const u=(V=o.dataList)==null?void 0:V.find(C=>C.property==="pressure"),O={name:o.name,stationId:o.stationId,time:u?u.time:"-",unit:u?u.unit:"-"};return(F=o==null?void 0:o.dataList)==null||F.map(C=>{O[C.property+""]=C.value}),O});console.log("newData--",d),E(),y.columns=[{prop:"name",label:"名称",minWidth:200}].concat(g),y.dataList=d,s.activeName=(m=s.tableData[0])==null?void 0:m.dataList[0],y.currentRow=d[0];const v=s.tableData.find(o=>{var u;return o.stationId===((u=d[0])==null?void 0:u.stationId)});f.tabs=(t=v==null?void 0:v.dataList)==null?void 0:t.map((o,u)=>({label:o.propertyName,value:o.property+u,data:o})),s.activeName=(e=f.tabs[0])==null?void 0:e.value,R((n=f.tabs[0])==null?void 0:n.data),_((c=s.tableData[0])==null?void 0:c.stationId)}else y.loading=!1,T.value=!1}),await H()},H=async()=>{var m,t,e;const i=await Dt({status:""}),r=M.group[0].fields[0],l=((t=(m=i.data)==null?void 0:m.data)==null?void 0:t.length)||0,a=[],p=(e=i.data)==null?void 0:e.data;p==null||p.map(n=>{let c=a.find(d=>d.status===n.status);const{label:g}=Y.find(d=>d.name===n.status)||{};c?c.value++:(c={name:g,status:n.status,nameAlias:g,value:1,scale:"0%"},a.push(c))}),a.map(n=>(n.scale=l===0?"0%":Number(n.value)/l*100+"%",n)),r&&(r.option=j(a,"个"))},R=async(i,r)=>{console.log("resetPanel",i,r),(i||r)&&(y.currentRow=i,await J(r||i),x(async()=>{s.pieOption=B()}),T.value=!1)},J=async i=>{var t,e;console.log("refuseChart",i);const l=(t=(await ht({deviceId:i.deviceId,attr:i.property})).data)==null?void 0:t.data,a=wt(200,l.todayDataList.map(n=>n.ts),40,40),p=[{name:"前天",key:"beforeYesterdayDataList"},{name:"昨天",key:"yesterdayDataList"},{name:"今天",key:"todayDataList"}];a.yAxis[0].name=i.propertyName.concat(i.unit?"("+i.unit+")":"");const m=p.map(n=>{const c=l[n.key].map(g=>g.value);return{name:n.name,smooth:!0,data:c,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}});a.series=m,(e=L.value)==null||e.clear(),await x(()=>{N.value&&q.listenTo(N.value,()=>{var n;s.lineOption=a,s.pieOption=B(),(n=L.value)==null||n.resize()})})};tt(async()=>{});const Q=async i=>{var r;h.view=i,(r=I.value)==null||r.toggleCustomDetail(!1),await K(),vt(h.view,l=>{var p,m;const a=(p=l.results)==null?void 0:p[0];if(a){if(a.type==="graphic"){const t=(m=a.graphic)==null?void 0:m.attributes;(t!=null&&t.stationId||t!=null&&t.id)&&_(t.stationId||t.id)}else if(a.type==="feature"&&a.layer&&a.layer.title==="点聚合图层"){const t=a.feature,e=t==null?void 0:t.attributes;(e!=null&&e.stationId||e!=null&&e.id)&&_(e.stationId||e.id)}}})};return(i,r)=>{const l=nt,a=lt,p=pt,m=mt,t=at("VChart"),e=ct;return A(),et(yt,{ref_key:"refMap",ref:I,title:"压力监测总览",windows:s.windows,"hide-detail-close":!0,"hide-layer-list":!0,"right-drawer-width":550,"enable-cluster":!0,"cluster-radius":100,"cluster-min-size":24,"cluster-max-size":60,"cluster-graphics":b.value,onMapLoaded:Q},{"detail-header":S(()=>r[1]||(r[1]=[])),"detail-default":S(()=>r[2]||(r[2]=[])),default:S(()=>{var n;return[P("div",_t,[D(l,{ref:"refForm",config:M},null,8,["config"]),D(a,{type:"underline",title:"压力数据监测"}),P("div",kt,[D(p,{ref_key:"refCard",ref:G,class:"table-box",config:y},null,8,["config"])]),D(a,{type:"underline",title:(((n=s.curRow)==null?void 0:n.name)||"")+"近三天运行曲线"},null,8,["title"]),ot((A(),it("div",{ref_key:"echartsDiv",ref:N,class:"right-box bottom"},[D(m,{modelValue:s.activeName,"onUpdate:modelValue":r[0]||(r[0]=c=>s.activeName=c),config:f},null,8,["modelValue","config"]),D(t,{ref_key:"refChart",ref:L,theme:rt(st)().isDark?"dark":"light",option:s.lineOption},null,8,["theme","option"])])),[[e,T.value]])])]}),_:1},8,["windows","cluster-graphics"])}}}),Oe=dt(Rt,[["__scopeId","data-v-d6500fd9"]]);export{Oe as default};
