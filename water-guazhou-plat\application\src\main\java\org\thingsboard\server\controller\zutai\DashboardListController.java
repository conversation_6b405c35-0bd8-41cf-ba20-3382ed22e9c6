package org.thingsboard.server.controller.zutai;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.zutai.DashboardListEntity;
import org.thingsboard.server.dao.zutai.DashboardListService;

import java.util.Map;

/**
 * 组态列表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
@RestController
@RequestMapping("api/zutai/dashboard-list")
public class DashboardListController extends BaseController {
    @Autowired
    private DashboardListService dashboardListService;


    /**
     * 列表
     * @param map
     * @return
     * @throws ThingsboardException
     */
    @GetMapping
    public PageData getList(@RequestParam Map map) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        map.put("tenantId", tenantId);
        return dashboardListService.getList(map);
    }

    /**
     * 保存
     * @param dashboardListEntity
     * @return
     * @throws ThingsboardException
     */
    @PostMapping
    public DashboardListEntity save(@RequestBody DashboardListEntity dashboardListEntity) throws ThingsboardException {
        dashboardListEntity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return dashboardListService.save(dashboardListEntity);
    }

    /**
     * 删除
     * @param id
     */

    @DeleteMapping
    public void delete(@RequestParam String id) {
        dashboardListService.delete(id);
    }
}
