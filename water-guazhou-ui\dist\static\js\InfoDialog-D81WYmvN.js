import{_ as w}from"./index-qoWsDjz-.js";import{d as S,c as p,am as V,o as E,i as t,g as r,h as s,F as o,q as n,t as N,an as c,bl as B,bm as M,bU as R,bV as x,bW as D}from"./index-r0dFAfgr.js";import{_ as T}from"./deviceInfo.vue_vue_type_script_setup_true_lang-D5q8lzUm.js";import F from"./repairinfo-DgFr9NYT.js";import L from"./maintenanceinfo-tc4mDUfU.js";import P from"./patrolinfo-SPbFoRLm.js";import"./ledgerManagement-CkhtRd8m.js";import"./equipmentManage-DuoY00aj.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./charts-CPgX3ymz.js";import"./DateFormatter-Bm9a68Ax.js";const Y=S({__name:"InfoDialog",props:{width:{type:[String,Number],default:"60%"},visible:{type:Boolean,default:!1},title:{type:String,default:""},close:{type:Function,default:()=>{}},currentId:{type:String,default:""},readonly:{type:Boolean,default:!1},deviceNo:{type:String,default:""},id:{type:String,default:""},serialId:{type:String,default:""}},emits:["sendMsgIds","sendMsgList"],setup(_){const e=_,l=p({title:e.title||"设备详情",visible:e.visible,scrollbar:!1,cancel:{handler:async()=>await e.close()}});V(()=>e.visible,()=>{l.value.visible=e.visible,l.value.cancel={handler:async()=>await e.close()},l.value.width=e.width},{deep:!0,immediate:!0});const a=p("deviceinfo");E(async()=>{b()});const i=p({currentId:"",url:""}),b=async()=>{i.value.currentId=e.currentId},g=d=>{i.value.currentId=d},y=d=>{console.log("tab ",d)};return(d,m)=>{const u=B,v=M,f=R,I=x,h=D,C=w;return t(l).visible?(r(),s(C,{key:0,config:t(l)},{default:o(()=>[n(h,{gutter:20},{default:o(()=>[n(f,{span:3},{default:o(()=>[n(v,{modelValue:t(a),"onUpdate:modelValue":m[0]||(m[0]=k=>N(a)?a.value=k:null),"tab-position":"left",onTabClick:y},{default:o(()=>[n(u,{label:"设备信息",name:"deviceinfo"}),n(u,{label:"维修信息",name:"repairinfo"}),n(u,{label:"保养信息",name:"maintenanceinfo"}),n(u,{label:"巡检信息",name:"patrolinfo"})]),_:1},8,["modelValue"])]),_:1}),n(f,{span:21},{default:o(()=>[n(I,{height:"100%"},{default:o(()=>[t(a)==="deviceinfo"?(r(),s(T,{key:0,id:e.id,"serial-id":e.serialId,onChangeCurrentId:g},null,8,["id","serial-id"])):c("",!0),t(a)==="repairinfo"?(r(),s(F,{key:1,id:t(i).currentId},null,8,["id"])):c("",!0),t(a)==="maintenanceinfo"?(r(),s(L,{key:2,id:t(i).currentId},null,8,["id"])):c("",!0),t(a)==="patrolinfo"?(r(),s(P,{key:3,id:t(i).currentId},null,8,["id"])):c("",!0)]),_:1})]),_:1})]),_:1})]),_:1},8,["config"])):c("",!0)}}});export{Y as default};
