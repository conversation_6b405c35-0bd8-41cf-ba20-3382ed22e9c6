package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroup;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class GuardGroupPageRequest extends PageableQueryEntity<GuardGroup> {
    // 地点id
    @NotNullOrEmpty
    private String placeId;

}
