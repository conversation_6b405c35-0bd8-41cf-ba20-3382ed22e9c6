package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.plan.Plan;
import org.thingsboard.server.dao.model.sql.plan.PlanTask;
import org.thingsboard.server.dao.sql.plan.PlanMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanDetailSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanPageRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskDetailSaveRequest;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class PlanServiceImpl implements PlanService {
    @Autowired
    private PlanMapper mapper;

    @Autowired
    private PlanDetailService planDetailService;

    @Autowired
    private PlanTaskService planTaskService;

    @Autowired
    private PlanTaskDetailService planTaskDetailService;

    @Override
    public IPage<Plan> findAllConditional(PlanPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public Plan save(PlanSaveRequest entity) {
        Plan plan = QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);

        List<PlanDetailSaveRequest> items = entity.getItems(plan.getId());
        if (items != null) { // 已禁止更新，不会进入此代码块
            // noinspection Convert2MethodRef 删除所有未在列表中的设备采购单条目
            planDetailService.removeAllByMainOnIdNotIn(entity.getId(),
                    items.stream().map(x -> x.getId()).filter(x -> x != null).collect(Collectors.toList()));
            planDetailService.saveAll(items);
        }

        // 生成计划的任务
        for (PlanTask planTask : entity.generatePlans()) {
            planTask = planTaskService.save(planTask);
            List<PlanTaskDetailSaveRequest> planItems = entity.generatePlanItems(planTask.getId());
            planTaskDetailService.saveAll(planItems);
        }

        return plan;
    }

    @Override
    public boolean update(Plan entity) {
        return mapper.update(entity);
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        planDetailService.deleteAllByMainId(id, null);
        return mapper.deleteById(id) > 0;
    }

}
