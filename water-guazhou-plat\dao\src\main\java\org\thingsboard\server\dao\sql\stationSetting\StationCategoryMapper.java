package org.thingsboard.server.dao.sql.stationSetting;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.StationCategoryListRequest;
import org.thingsboard.server.dao.model.sql.stationSetting.StationCategory;

@Mapper
public interface StationCategoryMapper extends BaseMapper<StationCategory> {
    IPage<StationCategory> findList(IPage<StationCategory> pageRequest, @Param("param") StationCategoryListRequest request);
}
