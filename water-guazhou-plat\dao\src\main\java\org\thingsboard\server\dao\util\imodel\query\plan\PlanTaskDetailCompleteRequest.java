package org.thingsboard.server.dao.util.imodel.query.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.Requestible;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class PlanTaskDetailCompleteRequest implements Requestible {
    // id
    private String id;

    // 盘点数量（正常）
    @NotNullOrEmpty
    private Double normalNum;

    // 盘点数量（异常）
    @NotNullOrEmpty
    private Double exceptionNum;

    // 盘点说明
    private String remark;
}
