<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.kpi.KpiNormSystemConfigMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormSystemConfig">
        select a.*, b.name as normName, a.seats_id as seatsName
        from tb_service_kpi_norm_system_config a
        left join tb_service_kpi_norm_param b on a.norm_id = b.id
        <where>
            <if test="month != null and month != ''">
                and a.month = #{month}
            </if>
            <if test="seatsId != null and seatsId != ''">
                and a.seats_id = #{seatsId}
            </if>
            <if test="score != null and score != ''">
                and a.score = #{score}
            </if>
        </where>
        order by a.order_num
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_kpi_norm_system_config a
        left join tb_service_kpi_norm_param b on a.norm_id = b.id
        <where>
            <if test="month != null and month != ''">
                and a.month = #{month}
            </if>
            <if test="seatsId != null and seatsId != ''">
                and a.seats_id = #{seatsId}
            </if>
            <if test="score != null and score != ''">
                and a.score = #{score}
            </if>
        </where>
    </select>
</mapper>