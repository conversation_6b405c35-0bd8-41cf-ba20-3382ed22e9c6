import{d,r,g as m,n as i,p as o,q as n,i as t,c3 as u,C as p}from"./index-r0dFAfgr.js";/* empty css                  */const V={class:"Parameters"},c={class:"slider-demo-block"},_={class:"slider-demo-block"},b={class:"slider-demo-block"},v={class:"slider-demo-block"},f=d({__name:"Parameters",setup(k){const e=r({brightness:50,chroma:50,saturation:50,contrast:50});return(g,s)=>{const l=u;return m(),i("div",V,[o("div",c,[s[4]||(s[4]=o("span",{class:"demonstration"},"亮度",-1)),n(l,{modelValue:t(e).brightness,"onUpdate:modelValue":s[0]||(s[0]=a=>t(e).brightness=a)},null,8,["modelValue"])]),o("div",_,[s[5]||(s[5]=o("span",{class:"demonstration"},"色度",-1)),n(l,{modelValue:t(e).chroma,"onUpdate:modelValue":s[1]||(s[1]=a=>t(e).chroma=a)},null,8,["modelValue"])]),o("div",b,[s[6]||(s[6]=o("span",{class:"demonstration"},"饱和度",-1)),n(l,{modelValue:t(e).saturation,"onUpdate:modelValue":s[2]||(s[2]=a=>t(e).saturation=a)},null,8,["modelValue"])]),o("div",v,[s[7]||(s[7]=o("span",{class:"demonstration"},"对比度",-1)),n(l,{modelValue:t(e).contrast,"onUpdate:modelValue":s[3]||(s[3]=a=>t(e).contrast=a)},null,8,["modelValue"])])])}}}),B=p(f,[["__scopeId","data-v-2e5c44b0"]]);export{B as default};
