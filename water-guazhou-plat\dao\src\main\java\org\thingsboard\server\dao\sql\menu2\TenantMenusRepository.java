package org.thingsboard.server.dao.sql.menu2;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.TenantMenus;

import java.util.List;

public interface TenantMenusRepository extends JpaRepository<TenantMenus, String> {
    @Query("SELECT a FROM TenantMenus a WHERE a.parentId = ?1 AND a.tenantId = ?2 ORDER BY a.orderNum DESC")
    List<TenantMenus> getSelectableTree(String root, String tenantId);

    List<TenantMenus> findByParentIdAndTenantId(String id, String tenantId);

    List<TenantMenus> findByIdInAndTenantId(List<String> menuIdList, String tenantId);

    List<TenantMenus> findByTenantIdOrderByOrderNumDesc(String tenantId);

    void deleteByTenantId(String tenantId);
}
