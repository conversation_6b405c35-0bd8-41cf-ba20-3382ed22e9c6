<template>
  <el-upload
    ref="uploadFile"
    class="deviceImportBtn"
    action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
    :show-file-list="false"
    :on-change="handleChange"
    :auto-upload="false"
    accept=".json"
  >
    <el-button
      class="ml-3"
      type="default"
      plain
    >
      导入JSON
    </el-button>
  </el-upload>
</template>

<script lang="ts" setup>

const props = defineProps<{
  config: IButton
}>()

// 导入JSON
const handleChange = (files, filesList) => {
  const fileList = filesList
  const reader = new FileReader() // 新建一个FileReader
  reader.readAsText(files.raw, 'UTF-8') // 读取文件
  reader.onload = async function (evt:any) { // 读取文件完毕执行此函数
    const dataJson = JSON.parse(evt.target.result)

    const formData = new window.FormData()
    formData.append('file', dataJson)
    props.config.click && props.config.click(dataJson)
  }
}
</script>

<style lang="scss" scoped>
.deviceImportBtn {
  margin: 0 12px;
  font-size: 16px !important;
  display: inline-block;
}
</style>
