import{R as m,T as P}from"./index-r0dFAfgr.js";import{F as d,K as L,J as M,G as A,I as F}from"./MapView-DaoQedLH.js";const f=(t,n,r)=>[n,r],a=(t,n,r)=>[n,r,t[2]],p=(t,n,r)=>[n,r,t[2],t[3]];function R(t){return t?{originPosition:t.originPosition==="upper-left"?"upperLeft":t.originPosition==="lower-left"?"lowerLeft":t.originPosition,scale:t.tolerance?[t.tolerance,t.tolerance]:[1,1],translate:m(t.extent)?[t.extent.xmin,t.extent.ymax]:[0,0]}:null}function h({scale:t,translate:n},r){return Math.round((r-n[0])/t[0])}function g({scale:t,translate:n},r){return Math.round((n[1]-r)/t[1])}function x(t,n,r){const u=[];let o,i,e,l;for(let c=0;c<r.length;c++){const s=r[c];c>0?(e=h(t,s[0]),l=g(t,s[1]),e===o&&l===i||(u.push(n(s,e-o,l-i)),o=e,i=l)):(o=h(t,s[0]),i=g(t,s[1]),u.push(n(s,o,i)))}return u.length>0?u:null}function G(t,n,r,u){return x(t,f,n)}function I(t,n,r,u){const o=[],i=f;for(let e=0;e<n.length;e++){const l=x(t,i,n[e]);l&&l.length>=3&&o.push(l)}return o.length?o:null}function T(t,n,r,u){const o=[],i=f;for(let e=0;e<n.length;e++){const l=x(t,i,n[e]);l&&l.length>=2&&o.push(l)}return o.length?o:null}function y({scale:t,translate:n},r){return r*t[0]+n[0]}function z({scale:t,translate:n},r){return n[1]-r*t[1]}function $(t,n,r){const u=new Array(r.length);if(!r.length)return u;const[o,i]=t.scale;let e=y(t,r[0][0]),l=z(t,r[0][1]);u[0]=n(r[0],e,l);for(let c=1;c<r.length;c++){const s=r[c];e+=s[0]*o,l-=s[1]*i,u[c]=n(s,e,l)}return u}function w(t,n,r){const u=new Array(r.length);for(let o=0;o<r.length;o++)u[o]=$(t,n,r[o]);return u}function b(t,n,r,u){return $(t,r?u?p:a:u?a:f,n)}function q(t,n,r,u){return w(t,r?u?p:a:u?a:f,n)}function v(t,n,r,u){return w(t,r?u?p:a:u?a:f,n)}function B(t,n,r,u,o){return n.xmin=h(t,r.xmin),n.ymin=g(t,r.ymin),n.xmax=h(t,r.xmax),n.ymax=g(t,r.ymax),n}function C(t,n,r,u,o){return n.points=G(t,r.points)??[],n}function E(t,n,r,u,o){return n.x=h(t,r.x),n.y=g(t,r.y),n!==r&&(u&&(n.z=r.z),o&&(n.m=r.m)),n}function J(t,n,r,u,o){const i=I(t,r.rings);return i?(n.rings=i,n):null}function K(t,n,r,u,o){const i=T(t,r.paths);return i?(n.paths=i,n):null}function S(t,n){return t&&n?d(n)?E(t,{},n,!1,!1):L(n)?K(t,{},n):M(n)?J(t,{},n):A(n)?C(t,{},n):F(n)?B(t,{},n):null:null}function U(t,n,r,u,o){return m(r)&&(n.points=b(t,r.points,u,o)),n}function V(t,n,r,u,o){return P(r)||(n.x=y(t,r.x),n.y=z(t,r.y),n!==r&&(u&&(n.z=r.z),o&&(n.m=r.m))),n}function j(t,n,r,u,o){return m(r)&&(n.rings=v(t,r.rings,u,o)),n}function k(t,n,r,u,o){return m(r)&&(n.paths=q(t,r.paths,u,o)),n}export{j as B,k as C,E as O,S as U,U as q,R as s,V as v};
