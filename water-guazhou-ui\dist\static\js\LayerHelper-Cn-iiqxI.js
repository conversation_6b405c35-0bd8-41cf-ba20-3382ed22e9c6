import{H as p}from"./MapView-DaoQedLH.js";import{h as l}from"./GraphicsLayer-DTrBRwJQ.js";import{_ as c}from"./AnimatedLinesLayer-B2VbV4jv.js";import{Q as o}from"./pipe-nogVzCHG.js";import{X as u}from"./index-r0dFAfgr.js";const G=(r,e)=>{if(e!=null&&e.id){let a=r==null?void 0:r.map.findLayerById(e.id);return a||(a=new l({...e||{}}),r==null||r.map.add(a)),a}},f=r=>new p({outFields:["*"],...r}),F=(r,e)=>{if(e.id){let a=r.map.findLayerById(e.id);return a||(e.type==="GraphicsLayer"?a=new l({id:e.id,title:e.title}):e.type==="MapImageLayer"&&(a=new c({url:e.url,id:e.id,title:e.title})),a&&r.map.add(a)),a}},h=r=>{let e=100;return r==null||r.map.layers.forEach((a,t)=>{a.id==='"pipelayer"'&&e>t&&(e=t)}),e},i=(r,e,a,t)=>{var d;const s=r==null?void 0:r.map.findLayerById("pipelayer"),n=[];return(d=s==null?void 0:s.sublayers)==null||d.filter(y=>(e===void 0||y.visible===e)&&(t?y.title===t:!0)).map(y=>{n.push(y.id)}),n},O=async r=>{var s;return(((s=(await o()).data)==null?void 0:s.layers)||[]).findIndex(n=>n.name===r)},C=async r=>{var n,d;if(!r)return[];const e=[],a=i(r);return(((d=(n=(await u(a)).data)==null?void 0:n.result)==null?void 0:d.rows)||[]).map(y=>{(y.geometrytype==="esriGeometryPolyline"||y.layername.indexOf("立管")>-1)&&(e==null||e.push({label:y.layername,value:y.layername,id:y.layerid,data:y}))}),e},E=async r=>{var s,n;if(!r)return[];const e=i(r);return(((n=(s=(await u(e)).data)==null?void 0:s.result)==null?void 0:n.rows)||[]).map(d=>({label:d.layername,value:d.layerid,id:d.layerid,data:d}))},N=(r,e)=>{const a=r==null?void 0:r.map.findLayerById("pipelayer");a&&a.refresh()},P=async r=>{const e=new c({id:"pipelayer",title:"管网",url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService});return r==null||r.map.add(e),await e.when(),r==null||r.goTo(e.fullExtent),e},_=async(r,e,a)=>{const t=f({url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer+"/"+r});try{const s=await t.applyEdits(e,a);return t.destroy(),s}catch(s){throw t.destroy(),new Error(s)}};export{i as a,E as b,C as c,F as d,h as e,_ as f,G as g,P as h,O as q,N as r};
