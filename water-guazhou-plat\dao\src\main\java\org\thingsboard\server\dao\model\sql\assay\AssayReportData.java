package org.thingsboard.server.dao.model.sql.assay;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 水质-化验报告-数据录入
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSAY_REPORT_DATA_TABLE)
@TableName(ModelConstants.ASSAY_REPORT_DATA_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class AssayReportData {

    @Id
    @TableId
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_TITLE)
    private String title;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_CODE)
    private String code;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_REPORT_TYPE)
    private String reportType;

    @TableField(exist = false)
    private String reportTypeName;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_WATER_SAMPLE_TYPE)
    private String waterSampleType;

    @TableField(exist = false)
    private String waterSampleTypeName;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_WATER_SAMPLE_NAME)
    private String waterSampleName;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_WATER_SAMPLE_STATE)
    private String waterSampleState;

    @TableField(exist = false)
    private String waterSampleStateName;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_COLLECT_TIME)
    private Date collectTime;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_COLLECT_PERSON)
    private String collectPerson;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_COLLECT_ENVIRONMENT)
    private String collectEnvironment;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_WEATHER)
    private String weather;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_TEMPERATURE)
    private String temperature;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_HUMIDITY)
    private String humidity;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_WATER_SAMPLE_CODE)
    private String waterSampleCode;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_SEND_CHECK_TIME)
    private Date sendCheckTime;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_CHECK_TIME_START)
    private Date checkTimeStart;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_CHECK_TIME_END)
    private Date checkTimeEnd;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_REPORT_TIME)
    private Date reportTime;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_FILE)
    private String file;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_CHECK_BASIS)
    private String checkBasis;

    @TableField(value = ModelConstants.REMARK)
    private String remark;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_IS_QUALIFIED)
    private String isQualified;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_IS_COMPLETE)
    private String isComplete;

    @TableField(value = ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(value = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
