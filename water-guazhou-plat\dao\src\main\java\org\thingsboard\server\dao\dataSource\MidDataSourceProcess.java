package org.thingsboard.server.dao.dataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.DefaultMapObject;
import org.thingsboard.server.common.data.dataSource.DataFromDataSource;
import org.thingsboard.server.common.data.dataSource.DataSourceRequest;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.OriginDataEntity;
import org.thingsboard.server.dao.origin.OriginDataService;
import org.thingsboard.server.dao.timeseries.TimeseriesService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/27 14:20
 */
@Service
public class MidDataSourceProcess {

    @Autowired
    private OriginDataService originDataService;

    @Autowired
    private TimeseriesService timeseriesService;

    /**
     * 获取中间变量数据
     *
     * @param dataSourceEntity  数据源
     * @param dataSourceRequest 获取数据请求
     * @return 转换为数据源数据
     */
    public DataFromDataSource processMidDataSource(DataSourceEntity dataSourceEntity, DataSourceRequest dataSourceRequest) {
        DataFromDataSource dataFromDataSource = new DataFromDataSource();
        dataFromDataSource.setDataSourceName(dataSourceEntity.getSourceName());
        dataFromDataSource.setDataSourceId(dataSourceEntity.getId());
        List<OriginDataEntity> data = originDataService.getOriginDataFormIdAndTime(dataSourceEntity.getId(), dataSourceRequest.getStartTime(), dataSourceRequest.getEndTime());
        List<DefaultMapObject> dps = new ArrayList<>();
        List<String> dates = DateUtils.findDates(dataSourceRequest.getStartTime(), dataSourceRequest.getStartTime(), dataSourceRequest.getInterval());

        data.forEach(originDataEntity -> dps.add(DefaultMapObject.builder().ts(String.valueOf(originDataEntity.getUpdateTime())).tm(DateUtils.date2Str(originDataEntity.getUpdateTime(),DateUtils.DATE_FORMATE_DEFAULT)).value(originDataEntity.getValue()).build()));
        dataFromDataSource.setData(dps);
        return dataFromDataSource;

    }


    /**
     * 获取常用变量数据的最后一次数据
     *
     * @param dataSourceEntity 数据源
     * @return 转换为数据源数据
     */
    public DataFromDataSource processPreparationLastDataSource(DataSourceEntity dataSourceEntity) {
        DataFromDataSource dataFromDataSource = new DataFromDataSource();
        dataFromDataSource.setDataSourceName(dataSourceEntity.getSourceName());
        dataFromDataSource.setDataSourceId(dataSourceEntity.getId());
        TsKvEntry tsKvEntry = timeseriesService.findLatestByKey(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), dataSourceEntity.getPreparation());
        if (tsKvEntry != null&&null!=tsKvEntry.getValueAsString()) {
            dataFromDataSource.setData(Collections.singletonList(DefaultMapObject.builder().ts(String.valueOf(tsKvEntry.getTs())).value(tsKvEntry.getValueAsString()).build()));
        }else {
            dataFromDataSource.setData(Collections.singletonList(DefaultMapObject.builder().build()));
        }
        return dataFromDataSource;
    }


    /**
     * 获取统计变量数据的最后一次数据
     *
     * @param dataSourceEntity 数据源
     * @return 转换为数据源数据
     */
    public DataFromDataSource processLastDataSource(DataSourceEntity dataSourceEntity) {
        DataFromDataSource dataFromDataSource = new DataFromDataSource();
        dataFromDataSource.setDataSourceName(dataSourceEntity.getSourceName());
        dataFromDataSource.setDataSourceId(dataSourceEntity.getId());
        TsKvEntry tsKvEntry = timeseriesService.findLatestByKey(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), dataSourceEntity.getProperty());
        if (tsKvEntry != null) {
            dataFromDataSource.setData(Collections.singletonList(DefaultMapObject.builder().ts(String.valueOf(tsKvEntry.getTs())).value(tsKvEntry.getValueAsString()).build()));
        }
        return dataFromDataSource;
    }


    /**
     * 获取中间变量数据的最后一次数据
     *
     * @param dataSourceEntity 数据源
     * @return 转换为数据源数据
     */
    public DataFromDataSource processLastMidDataSource(DataSourceEntity dataSourceEntity) {
        DataFromDataSource dataFromDataSource = new DataFromDataSource();
        dataFromDataSource.setDataSourceName(dataSourceEntity.getSourceName());
        dataFromDataSource.setDataSourceId(dataSourceEntity.getId());
        OriginDataEntity dataEntity = originDataService.getLastOriginDataFormId(dataSourceEntity.getId());
        if (dataEntity != null) {
            dataFromDataSource.setData(Collections.singletonList(DefaultMapObject.builder().ts(String.valueOf(dataEntity.getUpdateTime())).value(dataEntity.getValue()).build()));
        }
        return dataFromDataSource;
    }


    /**
     * 获取系统变量数据的最后一次数据
     *
     * @param dataSourceEntity 数据源
     * @return 转换为数据源数据
     */
    public DataFromDataSource processSystemLastDataSource(DataSourceEntity dataSourceEntity) {
        DataFromDataSource dataFromDataSource = new DataFromDataSource();
        dataFromDataSource.setDataSourceName(dataSourceEntity.getSourceName());
        dataFromDataSource.setDataSourceId(dataSourceEntity.getId());
            dataFromDataSource.setData(Collections.singletonList(DefaultMapObject.builder().ts(String.valueOf(System.currentTimeMillis())).value(String.valueOf(DateUtils.getSysDataSource(System.currentTimeMillis(),dataSourceEntity.getProperty()))).build()));
        return dataFromDataSource;
    }
}
