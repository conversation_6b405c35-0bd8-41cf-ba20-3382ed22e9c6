package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionArchive;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionArchiveMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionArchivePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionArchiveSaveRequest;

import static org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus.PROCESSING;

@Service
public class SoConstructionArchiveServiceImpl implements SoConstructionArchiveService {
    @Autowired
    private SoConstructionArchiveMapper mapper;

    @Override
    public IPage<SoConstructionArchive> findAllConditional(SoConstructionArchivePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoConstructionArchive save(SoConstructionArchiveSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> mapper.save(e), mapper::updateFully);
    }

    @Override
    public boolean update(SoConstructionArchive entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean isComplete(String constructionCode, String tenantId) {
        return mapper.isComplete(constructionCode, tenantId, PROCESSING);
    }

}
