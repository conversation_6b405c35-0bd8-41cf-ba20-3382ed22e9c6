package org.thingsboard.server.dao.model.sql.stationSetting;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 站点档案
 */
@Data
@TableName(value = "tb_station_archives")
public class StationArchives {

    private String id;

    private String stationId;

    private String type;

    private String categoryId;

    private String data;

    private String file;

    private Date createTime;

    private String tenantId;


}
