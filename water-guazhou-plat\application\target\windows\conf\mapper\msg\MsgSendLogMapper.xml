<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.msg.MsgSendLogMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.MsgSendLogEntity">
        select a.*, b.name as templateName, c.first_name as creatorName
        from tb_msg_send_log a
        left join tb_msg_template b on a.template_id = b.id
        left join tb_user c on a.creator = c.id
        <where>
             a.tenant_id = #{tenantId}
            <if test="start != null and start != ''">
                and a.create_time &gt;= to_timestamp(#{start} / 1000)
            </if>
            <if test="end != null and end != ''">
                and a.create_time &lt;= to_timestamp(#{end} / 1000)
            </if>
            <if test="templateName != null and templateName != ''">
                and b.name like '%'|| #{templateName} || '%'
            </if>
            <if test="phone != null and phone != ''">
                and a.phone like '%'|| #{phone} || '%'
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>