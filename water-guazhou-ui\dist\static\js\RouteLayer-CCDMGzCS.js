import{e as r,y as s,a as L,W as M,o as P,m as Z,i as Ve,s as C,h as it,w as ze,b as V,a4 as nt,q as Me,j as at,at as lt}from"./Point-WxyopZva.js";import{T as $,fB as pt,R as f,aO as se,aP as ut,aS as xe,fU as ct}from"./index-r0dFAfgr.js";import{gX as k,y as H,a as we,S as yt,cN as O,d9 as ve,b$ as He,g as Q,b as Be,w as Ke,bH as G,aq as dt,cO as Ie,d6 as mt,ar as ft,bk as j,c as ht,t as vt,Q as wt,R as gt,V as St,f as bt,u as Bt,hk as Tt,aH as Nt,aI as Rt,aM as Pt,eh as It,bI as $t,_ as At,bM as Ot,e as Lt}from"./MapView-DaoQedLH.js";import{a as jt}from"./widget-BcWKanF2.js";import{U as ie,u as Te,L as Dt}from"./pe-B8dP0-Ut.js";import{i as _t}from"./originUtils-DOOsZebp.js";import{U as X,y as $e,s as Ct,R as kt,r as Qe,A as Ae,c as Mt,d as xt,n as Jt,w as Ft,S as Ut,e as Je,i as Et,k as Gt,j as qt,o as Wt,l as Zt,b as ne,h as ae,O as ee,g as te,f as re,D as oe,a as ue,m as Vt}from"./Stop-DopqXNrA.js";import{a as zt,t as Ht}from"./commonProperties-DqNQ4F00.js";import"./multiOriginJSONSupportUtils-C0wm8_Yw.js";let q=class extends M{constructor(t){super(t),this.break=new H({color:[255,255,255],size:12,outline:{color:[0,122,194],width:3}}),this.first=new H({color:[0,255,0],size:20,outline:{color:[255,255,255],width:4}}),this.unlocated=new H({color:[255,0,0],size:12,outline:{color:[255,255,255],width:3}}),this.last=new H({color:[255,0,0],size:20,outline:{color:[255,255,255],width:4}}),this.middle=new H({color:[51,51,51],size:12,outline:{color:[0,122,194],width:3}}),this.waypoint=new H({color:[255,255,255],size:12,outline:{color:[0,122,194],width:3}})}};r([s({types:k})],q.prototype,"break",void 0),r([s({types:k})],q.prototype,"first",void 0),r([s({types:k})],q.prototype,"unlocated",void 0),r([s({types:k})],q.prototype,"last",void 0),r([s({types:k})],q.prototype,"middle",void 0),r([s({types:k})],q.prototype,"waypoint",void 0),q=r([L("esri.layers.support.RouteStopSymbols")],q);const Xe=q;let U=class extends M{constructor(t){super(t),this.directionLines=new we({color:[0,122,194],width:6}),this.directionPoints=new H({color:[255,255,255],size:6,outline:{color:[0,122,194],width:2}}),this.pointBarriers=new H({style:"x",size:10,outline:{color:[255,0,0],width:3}}),this.polygonBarriers=new yt({color:[255,170,0,.6],outline:{width:7.5,color:[255,0,0,.6]}}),this.polylineBarriers=new we({width:7.5,color:[255,85,0,.7]}),this.routeInfo=new we({width:8,color:[20,89,127]}),this.stops=new Xe}};r([s({types:k})],U.prototype,"directionLines",void 0),r([s({types:k})],U.prototype,"directionPoints",void 0),r([s({types:k})],U.prototype,"pointBarriers",void 0),r([s({types:k})],U.prototype,"polygonBarriers",void 0),r([s({types:k})],U.prototype,"polylineBarriers",void 0),r([s({types:k})],U.prototype,"routeInfo",void 0),r([s({type:Xe})],U.prototype,"stops",void 0),U=r([L("esri.layers.support.RouteSymbols")],U);const Ye=U;let J=class extends M{constructor(t){super(t),this.dataType=null,this.name=null,this.parameterNames=null,this.restrictionUsageParameterName=null,this.timeNeutralAttributeName=null,this.trafficSupport=null,this.units=null,this.usageType=null}};r([s({type:String})],J.prototype,"dataType",void 0),r([O(X,{ignoreUnknown:!1})],J.prototype,"name",void 0),r([s({type:[String]})],J.prototype,"parameterNames",void 0),r([s({type:String})],J.prototype,"restrictionUsageParameterName",void 0),r([O($e,{ignoreUnknown:!1})],J.prototype,"timeNeutralAttributeName",void 0),r([s({type:String})],J.prototype,"trafficSupport",void 0),r([O(Ct)],J.prototype,"units",void 0),r([O(kt)],J.prototype,"usageType",void 0),J=r([L("esri.rest.support.NetworkAttribute")],J);const Kt=J;let z=class extends M{constructor(t){super(t),this.buildTime=null,this.name=null,this.networkAttributes=null,this.networkSources=null,this.state=null}};r([s({type:Number})],z.prototype,"buildTime",void 0),r([s({type:String})],z.prototype,"name",void 0),r([s({type:[Kt]})],z.prototype,"networkAttributes",void 0),r([s()],z.prototype,"networkSources",void 0),r([s({type:String})],z.prototype,"state",void 0),z=r([L("esri.rest.support.NetworkDataset")],z);const Qt=z;let A=class extends M{constructor(t){super(t),this.accumulateAttributeNames=null,this.attributeParameterValues=null,this.currentVersion=null,this.defaultTravelMode=null,this.directionsLanguage=null,this.directionsLengthUnits=null,this.directionsSupportedLanguages=null,this.directionsTimeAttribute=null,this.hasZ=null,this.impedance=null,this.networkDataset=null,this.supportedTravelModes=null}readAccumulateAttributes(t){return $(t)?null:t.map(o=>X.fromJSON(o))}writeAccumulateAttributes(t,o,i){!$(t)&&t.length&&(o[i]=t.map(n=>X.toJSON(n)))}readDefaultTravelMode(t,o){var n,u;const i=((n=o.supportedTravelModes)==null?void 0:n.find(({id:p})=>p===o.defaultTravelMode))??((u=o.supportedTravelModes)==null?void 0:u.find(({itemId:p})=>p===o.defaultTravelMode));return i?Ae.fromJSON(i):null}};r([s()],A.prototype,"accumulateAttributeNames",void 0),r([P("accumulateAttributeNames")],A.prototype,"readAccumulateAttributes",null),r([Z("accumulateAttributeNames")],A.prototype,"writeAccumulateAttributes",null),r([s()],A.prototype,"attributeParameterValues",void 0),r([s()],A.prototype,"currentVersion",void 0),r([s()],A.prototype,"defaultTravelMode",void 0),r([P("defaultTravelMode",["defaultTravelMode","supportedTravelModes"])],A.prototype,"readDefaultTravelMode",null),r([s()],A.prototype,"directionsLanguage",void 0),r([O(Qe)],A.prototype,"directionsLengthUnits",void 0),r([s()],A.prototype,"directionsSupportedLanguages",void 0),r([O($e,{ignoreUnknown:!1})],A.prototype,"directionsTimeAttribute",void 0),r([s()],A.prototype,"hasZ",void 0),r([O(X,{ignoreUnknown:!1})],A.prototype,"impedance",void 0),r([s({type:Qt})],A.prototype,"networkDataset",void 0),r([s({type:[Ae]})],A.prototype,"supportedTravelModes",void 0),A=r([L("esri.rest.support.NetworkServiceDescription")],A);const Xt=A,Yt=Ve.getLogger("esri.rest.networkService");function ce(e,t,o,i){i[o]=[t.length,t.length+e.length],e.forEach(n=>{t.push(n.geometry)})}function er(e,t){for(let o=0;o<t.length;o++){const i=e[t[o]];if(i&&i.length)for(const n of i)n.z=void 0}Yt.warnOnce(`The remote Network Analysis service is powered by a network dataset which is not Z-aware.
Z-coordinates of the input geometry are ignored.`)}function tr(e,t){for(let o=0;o<t.length;o++){const i=e[t[o]];if(i&&i.length){for(const n of i)if(f(n)&&n.hasZ)return!0}}return!1}async function et(e,t,o){if(!e)throw new C("network-service:missing-url","Url to Network service is missing");const i=ve({f:"json",token:t},o),{data:n}=await ie(e,i),u=n.currentVersion>=10.4?or(e,t,o):rr(e,o),{defaultTravelMode:p,supportedTravelModes:y}=await u;return n.defaultTravelMode=p,n.supportedTravelModes=y,Xt.fromJSON(n)}async function rr(e,t){var S,d,a;const o=ve({f:"json"},t),{data:i}=await ie(e.replace(/\/rest\/.*$/i,"/info"),o);if(!i||!i.owningSystemUrl)return{supportedTravelModes:[],defaultTravelMode:null};const{owningSystemUrl:n}=i,u=Te(n)+"/sharing/rest/portals/self",{data:p}=await ie(u,o),y=pt("helperServices.routingUtilities.url",p);if(!y)return{supportedTravelModes:[],defaultTravelMode:null};const v=He(n),T=/\/solve$/i.test(v.path)?"Route":/\/solveclosestfacility$/i.test(v.path)?"ClosestFacility":"ServiceAreas",l=ve({f:"json",serviceName:T},t),g=Te(y)+"/GetTravelModes/execute",R=await ie(g,l),B=[];let h=null;if((d=(S=R==null?void 0:R.data)==null?void 0:S.results)!=null&&d.length){const N=R.data.results;for(const w of N)if(w.paramName==="supportedTravelModes"){if((a=w.value)!=null&&a.features){for(const{attributes:I}of w.value.features)if(I){const x=JSON.parse(I.TravelMode);B.push(x)}}}else w.paramName==="defaultTravelMode"&&(h=w.value)}return{supportedTravelModes:B,defaultTravelMode:h}}async function or(e,t,o){try{const i=ve({f:"json",token:t},o),n=Te(e)+"/retrieveTravelModes",{data:{supportedTravelModes:u,defaultTravelMode:p}}=await ie(n,i);return{supportedTravelModes:u,defaultTravelMode:p}}catch(i){throw new C("network-service:retrieveTravelModes","Could not get to the NAServer's retrieveTravelModes.",{error:i})}}const Fe=new it({0:"informative",1:"process-definition",2:"process-start",3:"process-stop",50:"warning",100:"error",101:"empty",200:"abort"});let fe=class extends zt{constructor(t){super(t),this.type=null}};r([s({type:String,json:{read:Fe.read,write:Fe.write}})],fe.prototype,"type",void 0),fe=r([L("esri.rest.support.NAMessage")],fe);const sr=fe;let le=class extends M{constructor(t){super(t)}};r([s({json:{read:{source:"string"}}})],le.prototype,"text",void 0),r([O(Mt,{name:"stringType"})],le.prototype,"type",void 0),le=r([L("esri.rest.support.DirectionsString")],le);const tt=le;let W=class extends M{constructor(e){super(e),this.arriveTime=null,this.arriveTimeOffset=null,this.geometry=null,this.strings=null}readArriveTimeOffset(e,t){return xt(t.ETA,t.arriveTimeUTC)}readGeometry(e,t){return ze.fromJSON(t.point)}};r([s({type:Date,json:{read:{source:"arriveTimeUTC"}}})],W.prototype,"arriveTime",void 0),r([s()],W.prototype,"arriveTimeOffset",void 0),r([P("arriveTimeOffset",["arriveTimeUTC","ETA"])],W.prototype,"readArriveTimeOffset",null),r([s({type:ze})],W.prototype,"geometry",void 0),r([P("geometry",["point"])],W.prototype,"readGeometry",null),r([s({type:[tt]})],W.prototype,"strings",void 0),W=r([L("esri.rest.support.DirectionsEvent")],W);const ir=W;function nr(e){if($(e)||e==="")return null;let t=0,o=0,i=0,n=0;const u=[];let p,y,v,T,l,g,R,B,h=0,S=0,d=0;if(l=e.match(/((\+|\-)[^\+\-\|]+|\|)/g),l||(l=[]),parseInt(l[h],32)===0){h=2;const a=parseInt(l[h],32);h++,g=parseInt(l[h],32),h++,1&a&&(S=l.indexOf("|")+1,R=parseInt(l[S],32),S++),2&a&&(d=l.indexOf("|",S)+1,B=parseInt(l[d],32),d++)}else g=parseInt(l[h],32),h++;for(;h<l.length&&l[h]!=="|";){p=parseInt(l[h],32)+t,h++,t=p,y=parseInt(l[h],32)+o,h++,o=y;const a=[p/g,y/g];S&&(T=parseInt(l[S],32)+i,S++,i=T,a.push(T/R)),d&&(v=parseInt(l[d],32)+n,d++,n=v,a.push(v/B)),u.push(a)}return{paths:[u],hasZ:S>0,hasM:d>0}}let Y=class extends Q{constructor(t){super(t),this.events=null,this.strings=null}readGeometry(t,o){const i=nr(o.compressedGeometry);return f(i)?Be.fromJSON(i):null}};r([s({type:[ir]})],Y.prototype,"events",void 0),r([P("geometry",["compressedGeometry"])],Y.prototype,"readGeometry",null),r([s({type:[tt]})],Y.prototype,"strings",void 0),Y=r([L("esri.rest.support.DirectionsFeature")],Y);const ar=Y;function lr(e,t){if(e.length===0)return new Be({spatialReference:t});const o=[];for(const p of e)for(const y of p.paths)o.push(...y);const i=[];o.forEach((p,y)=>{y!==0&&p[0]===o[y-1][0]&&p[1]===o[y-1][1]||i.push(p)});const{hasM:n,hasZ:u}=e[0];return new Be({hasM:n,hasZ:u,paths:[i],spatialReference:t})}let D=class extends G{constructor(t){super(t),this.extent=null,this.features=[],this.geometryType="polyline",this.routeId=null,this.routeName=null,this.totalDriveTime=null,this.totalLength=null,this.totalTime=null}readFeatures(t,o){if(!t)return[];const i=o.summary.envelope.spatialReference??o.spatialReference,n=i&&V.fromJSON(i);return t.map(u=>{const p=ar.fromJSON(u);if(f(p.geometry)&&(p.geometry.spatialReference=n),f(p.events))for(const y of p.events)f(y.geometry)&&(y.geometry.spatialReference=n);return p})}get mergedGeometry(){return this.features?lr(this.features.map(({geometry:t})=>se(t)),this.extent.spatialReference):null}get strings(){return this.features.map(({strings:t})=>t).flat().filter(f)}};r([s({type:Ke,json:{read:{source:"summary.envelope"}}})],D.prototype,"extent",void 0),r([s({nonNullable:!0})],D.prototype,"features",void 0),r([P("features")],D.prototype,"readFeatures",null),r([s()],D.prototype,"geometryType",void 0),r([s({readOnly:!0})],D.prototype,"mergedGeometry",null),r([s()],D.prototype,"routeId",void 0),r([s()],D.prototype,"routeName",void 0),r([s({value:null,readOnly:!0})],D.prototype,"strings",null),r([s({json:{read:{source:"summary.totalDriveTime"}}})],D.prototype,"totalDriveTime",void 0),r([s({json:{read:{source:"summary.totalLength"}}})],D.prototype,"totalLength",void 0),r([s({json:{read:{source:"summary.totalTime"}}})],D.prototype,"totalTime",void 0),D=r([L("esri.rest.support.DirectionsFeatureSet")],D);const pr=D;let _=class extends M{constructor(t){super(t),this.directionLines=null,this.directionPoints=null,this.directions=null,this.route=null,this.routeName=null,this.stops=null,this.traversedEdges=null,this.traversedJunctions=null,this.traversedTurns=null}};r([s({type:G,json:{write:!0}})],_.prototype,"directionLines",void 0),r([s({type:G,json:{write:!0}})],_.prototype,"directionPoints",void 0),r([s({type:pr,json:{write:!0}})],_.prototype,"directions",void 0),r([s({type:Q,json:{write:!0}})],_.prototype,"route",void 0),r([s({type:String,json:{write:!0}})],_.prototype,"routeName",void 0),r([s({type:[Q],json:{write:!0}})],_.prototype,"stops",void 0),r([s({type:G,json:{write:!0}})],_.prototype,"traversedEdges",void 0),r([s({type:G,json:{write:!0}})],_.prototype,"traversedJunctions",void 0),r([s({type:G,json:{write:!0}})],_.prototype,"traversedTurns",void 0),_=r([L("esri.rest.support.RouteResult")],_);const ur=_;function ge(e){return e?G.fromJSON(e).features.filter(f):[]}let F=class extends M{constructor(t){super(t),this.messages=null,this.pointBarriers=null,this.polylineBarriers=null,this.polygonBarriers=null,this.routeResults=null}readPointBarriers(t,o){return ge(o.barriers)}readPolylineBarriers(t){return ge(t)}readPolygonBarriers(t){return ge(t)}};r([s({type:[sr]})],F.prototype,"messages",void 0),r([s({type:[Q]})],F.prototype,"pointBarriers",void 0),r([P("pointBarriers",["barriers"])],F.prototype,"readPointBarriers",null),r([s({type:[Q]})],F.prototype,"polylineBarriers",void 0),r([P("polylineBarriers")],F.prototype,"readPolylineBarriers",null),r([s({type:[Q]})],F.prototype,"polygonBarriers",void 0),r([P("polygonBarriers")],F.prototype,"readPolygonBarriers",null),r([s({type:[ur]})],F.prototype,"routeResults",void 0),F=r([L("esri.rest.support.RouteSolveResult")],F);const cr=F;function ye(e){return e instanceof G}async function yr(e,t,o){const i=[],n=[],u={},p={},y=He(e),{path:v}=y;ye(t.stops)&&ce(t.stops.features,n,"stops.features",u),ye(t.pointBarriers)&&ce(t.pointBarriers.features,n,"pointBarriers.features",u),ye(t.polylineBarriers)&&ce(t.polylineBarriers.features,n,"polylineBarriers.features",u),ye(t.polygonBarriers)&&ce(t.polygonBarriers.features,n,"polygonBarriers.features",u);const T=await dt(n);for(const B in u){const h=u[B];i.push(B),p[B]=T.slice(h[0],h[1])}if(tr(p,i)){let B=null;try{B=await et(v,t.apiKey,o)}catch{}B&&!B.hasZ&&er(p,i)}for(const B in p)p[B].forEach((h,S)=>{t.get(B)[S].geometry=h});const l={...o,query:{...y.query,...Jt(t),f:"json"}},g=v.endsWith("/solve")?v:`${v}/solve`,{data:R}=await ie(g,l);return dr(R)}function dr(e){const{barriers:t,directionLines:o,directionPoints:i,directions:n,messages:u,polygonBarriers:p,polylineBarriers:y,routes:v,stops:T,traversedEdges:l,traversedJunctions:g,traversedTurns:R}=e,B=a=>{const N=S.find(I=>I.routeName===a);if(f(N))return N;const w={routeId:S.length+1,routeName:a};return S.push(w),w},h=a=>{const N=S.find(I=>I.routeId===a);if(f(N))return N;const w={routeId:a,routeName:null};return S.push(w),w},S=[];v==null||v.features.forEach((a,N)=>{a.geometry.spatialReference=v.spatialReference;const w=a.attributes.Name,I=N+1;S.push({routeId:I,routeName:w,route:a})}),n==null||n.forEach(a=>{const{routeName:N}=a;B(N).directions=a});const d=((T==null?void 0:T.features.every(a=>$(a.attributes.RouteName)))??!1)&&S.length>0?S[0].routeName:null;return T==null||T.features.forEach(a=>{var N;a.geometry&&((N=a.geometry).spatialReference??(N.spatialReference=T.spatialReference));const w=d??a.attributes.RouteName,I=B(w);I.stops??(I.stops=[]),I.stops.push(a)}),o==null||o.features.forEach(a=>{const N=a.attributes.RouteID,w=h(N),{geometryType:I,spatialReference:x}=o;w.directionLines??(w.directionLines={features:[],geometryType:I,spatialReference:x}),w.directionLines.features.push(a)}),i==null||i.features.forEach(a=>{const N=a.attributes.RouteID,w=h(N),{geometryType:I,spatialReference:x}=i;w.directionPoints??(w.directionPoints={features:[],geometryType:I,spatialReference:x}),w.directionPoints.features.push(a)}),l==null||l.features.forEach(a=>{const N=a.attributes.RouteID,w=h(N),{geometryType:I,spatialReference:x}=l;w.traversedEdges??(w.traversedEdges={features:[],geometryType:I,spatialReference:x}),w.traversedEdges.features.push(a)}),g==null||g.features.forEach(a=>{const N=a.attributes.RouteID,w=h(N),{geometryType:I,spatialReference:x}=g;w.traversedJunctions??(w.traversedJunctions={features:[],geometryType:I,spatialReference:x}),w.traversedJunctions.features.push(a)}),R==null||R.features.forEach(a=>{const N=a.attributes.RouteID,w=h(N);w.traversedTurns??(w.traversedTurns={features:[]}),w.traversedTurns.features.push(a)}),cr.fromJSON({routeResults:S,barriers:t,polygonBarriers:p,polylineBarriers:y,messages:u})}let E=class extends Ie(M){constructor(e){super(e),this.doNotLocateOnRestrictedElements=null,this.geometry=null,this.geometryType=null,this.name=null,this.spatialRelationship=null,this.type="layer",this.where=null}};r([s({type:Boolean,json:{write:!0}})],E.prototype,"doNotLocateOnRestrictedElements",void 0),r([s({types:mt,json:{read:ft,write:!0}})],E.prototype,"geometry",void 0),r([O(Ft)],E.prototype,"geometryType",void 0),r([s({type:String,json:{name:"layerName",write:!0}})],E.prototype,"name",void 0),r([O(Ut,{name:"spatialRel"})],E.prototype,"spatialRelationship",void 0),r([s({type:String,json:{write:!0}})],E.prototype,"type",void 0),r([s({type:String,json:{write:!0}})],E.prototype,"where",void 0),E=r([L("esri.rest.support.DataLayer")],E);const mr=E;var Ne;let he=Ne=class extends G{constructor(e){super(e),this.doNotLocateOnRestrictedElements=null}clone(){return new Ne({doNotLocateOnRestrictedElements:this.doNotLocateOnRestrictedElements,...this.cloneProperties()})}};r([s({type:Boolean,json:{write:!0}})],he.prototype,"doNotLocateOnRestrictedElements",void 0),he=Ne=r([L("esri.rest.support.NetworkFeatureSet")],he);const fr=he;let pe=class extends Ie(M){constructor(e){super(e),this.doNotLocateOnRestrictedElements=null,this.url=null}};r([s({type:Boolean,json:{write:!0}})],pe.prototype,"doNotLocateOnRestrictedElements",void 0),r([s({type:String,json:{write:!0}})],pe.prototype,"url",void 0),pe=r([L("esri.rest.support.NetworkUrl")],pe);const hr=pe;var Re;function vr(e){return e&&"type"in e}function wr(e){return e&&"features"in e&&"doNotLocateOnRestrictedElements"in e}function gr(e){return e&&"url"in e}function Sr(e){return e&&"features"in e}function br(e){return vr(e)?mr.fromJSON(e):gr(e)?hr.fromJSON(e):wr(e)?fr.fromJSON(e):Sr(e)?G.fromJSON(e):null}function de(e,t,o){f(e)&&(t[o]=j.isCollection(e)?{features:e.toArray().map(i=>i.toJSON())}:e.toJSON())}let c=Re=class extends Ie(M){constructor(e){super(e),this.accumulateAttributes=null,this.apiKey=null,this.attributeParameterValues=null,this.directionsLanguage=null,this.directionsLengthUnits=null,this.directionsOutputType=null,this.directionsStyleName=null,this.directionsTimeAttribute=null,this.findBestSequence=null,this.geometryPrecision=null,this.geometryPrecisionM=null,this.geometryPrecisionZ=null,this.ignoreInvalidLocations=null,this.impedanceAttribute=null,this.outputGeometryPrecision=null,this.outputGeometryPrecisionUnits=null,this.outputLines="true-shape",this.outSpatialReference=null,this.overrides=null,this.pointBarriers=null,this.polygonBarriers=null,this.polylineBarriers=null,this.preserveFirstStop=null,this.preserveLastStop=null,this.preserveObjectID=null,this.restrictionAttributes=null,this.restrictUTurns=null,this.returnBarriers=!1,this.returnDirections=!1,this.returnPolygonBarriers=!1,this.returnPolylineBarriers=!1,this.returnRoutes=!0,this.returnStops=!1,this.returnTraversedEdges=null,this.returnTraversedJunctions=null,this.returnTraversedTurns=null,this.returnZ=!0,this.startTime=null,this.startTimeIsUTC=!0,this.stops=null,this.timeWindowsAreUTC=null,this.travelMode=null,this.useHierarchy=null,this.useTimeWindows=null}static from(e){return nt(Re,e)}readAccumulateAttributes(e){return $(e)?null:e.map(t=>X.fromJSON(t))}writeAccumulateAttributes(e,t,o){!$(e)&&e.length&&(t[o]=e.map(i=>X.toJSON(i)))}writePointBarriers(e,t,o){de(e,t,o)}writePolygonBarrier(e,t,o){de(e,t,o)}writePolylineBarrier(e,t,o){de(e,t,o)}readRestrictionAttributes(e){return $(e)?null:e.map(t=>Je.fromJSON(t))}writeRestrictionAttributes(e,t,o){!$(e)&&e.length&&(t[o]=e.map(i=>Je.toJSON(i)))}readStartTime(e,t){const{startTime:o}=t;return $(o)?null:o==="now"?"now":new Date(o)}writeStartTime(e,t){$(e)||(t.startTime=e==="now"?"now":e.getTime())}readStops(e,t){return br(t.stops)}writeStops(e,t,o){de(e,t,o)}};r([s({type:[String],json:{name:"accumulateAttributeNames",write:!0}})],c.prototype,"accumulateAttributes",void 0),r([P("accumulateAttributes")],c.prototype,"readAccumulateAttributes",null),r([Z("accumulateAttributes")],c.prototype,"writeAccumulateAttributes",null),r([s(Ht)],c.prototype,"apiKey",void 0),r([s({json:{write:!0}})],c.prototype,"attributeParameterValues",void 0),r([s({type:String,json:{write:!0}})],c.prototype,"directionsLanguage",void 0),r([O(Qe)],c.prototype,"directionsLengthUnits",void 0),r([O(Et)],c.prototype,"directionsOutputType",void 0),r([O(Gt)],c.prototype,"directionsStyleName",void 0),r([O($e,{name:"directionsTimeAttributeName",ignoreUnknown:!1})],c.prototype,"directionsTimeAttribute",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"findBestSequence",void 0),r([s({type:Number,json:{write:!0}})],c.prototype,"geometryPrecision",void 0),r([s({type:Number,json:{write:!0}})],c.prototype,"geometryPrecisionM",void 0),r([s({type:Number,json:{write:!0}})],c.prototype,"geometryPrecisionZ",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"ignoreInvalidLocations",void 0),r([O(X,{name:"impedanceAttributeName",ignoreUnknown:!1})],c.prototype,"impedanceAttribute",void 0),r([s({type:Number,json:{write:!0}})],c.prototype,"outputGeometryPrecision",void 0),r([O(qt)],c.prototype,"outputGeometryPrecisionUnits",void 0),r([O(Wt)],c.prototype,"outputLines",void 0),r([s({type:V,json:{name:"outSR",write:!0}})],c.prototype,"outSpatialReference",void 0),r([s({json:{write:!0}})],c.prototype,"overrides",void 0),r([s({json:{name:"barriers",write:!0}})],c.prototype,"pointBarriers",void 0),r([Z("pointBarriers")],c.prototype,"writePointBarriers",null),r([s({json:{write:!0}})],c.prototype,"polygonBarriers",void 0),r([Z("polygonBarriers")],c.prototype,"writePolygonBarrier",null),r([s({json:{write:!0}})],c.prototype,"polylineBarriers",void 0),r([Z("polylineBarriers")],c.prototype,"writePolylineBarrier",null),r([s({type:Boolean,json:{write:!0}})],c.prototype,"preserveFirstStop",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"preserveLastStop",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"preserveObjectID",void 0),r([s({type:[String],json:{name:"restrictionAttributeNames",write:!0}})],c.prototype,"restrictionAttributes",void 0),r([P("restrictionAttributes")],c.prototype,"readRestrictionAttributes",null),r([Z("restrictionAttributes")],c.prototype,"writeRestrictionAttributes",null),r([O(Zt)],c.prototype,"restrictUTurns",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"returnBarriers",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"returnDirections",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"returnPolygonBarriers",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"returnPolylineBarriers",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"returnRoutes",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"returnStops",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"returnTraversedEdges",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"returnTraversedJunctions",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"returnTraversedTurns",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"returnZ",void 0),r([s({type:Date,json:{type:Number,write:!0}})],c.prototype,"startTime",void 0),r([P("startTime")],c.prototype,"readStartTime",null),r([Z("startTime")],c.prototype,"writeStartTime",null),r([s({type:Boolean,json:{write:!0}})],c.prototype,"startTimeIsUTC",void 0),r([s({json:{write:!0}})],c.prototype,"stops",void 0),r([P("stops")],c.prototype,"readStops",null),r([Z("stops")],c.prototype,"writeStops",null),r([s({type:Boolean,json:{write:!0}})],c.prototype,"timeWindowsAreUTC",void 0),r([s({type:Ae,json:{write:!0}})],c.prototype,"travelMode",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"useHierarchy",void 0),r([s({type:Boolean,json:{write:!0}})],c.prototype,"useTimeWindows",void 0),c=Re=r([L("esri.rest.support.RouteParameters")],c);const Ue=c;function Se(e){return e.length?e:null}function Pe(e){switch(e){case"esriGeometryPoint":return{type:"esriSMS",style:"esriSMSCircle",size:12,color:[0,0,0,0],outline:Pe("esriGeometryPolyline")};case"esriGeometryPolyline":return{type:"esriSLS",style:"esriSLSSolid",width:1,color:[0,0,0,0]};case"esriGeometryPolygon":return{type:"esriSFS",style:"esriSFSNull",outline:Pe("esriGeometryPolyline")}}}function me(e){return"layers"in e}function Br(e){return e.declaredClass==="esri.rest.support.FeatureSet"}function Tr(e){return e.declaredClass==="esri.rest.support.NetworkFeatureSet"}function Nr(e,t,o){var Oe,Le,je,De,_e,Ce;const i=(Oe=t.networkDataset)==null?void 0:Oe.networkAttributes,n=(i==null?void 0:i.filter(({usageType:m})=>m==="cost"))??[],u=o.travelMode??t.defaultTravelMode;if($(u))return void ot.warn("route-layer:missing-travel-mode","The routing service must have a default travel mode or one must be specified in the route parameter.");const{timeAttributeName:p,distanceAttributeName:y}=u,v=n.find(({name:m})=>m===p),T=n.find(({name:m})=>m===y),l=((Le=se(o.travelMode))==null?void 0:Le.impedanceAttributeName)??se(o.impedanceAttribute)??t.impedance,g=v==null?void 0:v.units,R=T==null?void 0:T.units;if(!g||!R)throw new C("routelayer:unknown-impedance-units","the units of either the distance or time impedance are unknown");const B=o.directionsLanguage??t.directionsLanguage,h=se(o.accumulateAttributes)??t.accumulateAttributeNames??[],S=new Set(n.filter(({name:m})=>m===p||m===y||m===l||m!=null&&h.includes(m)).map(({name:m})=>m)),d=m=>{for(const ke in m)S.has(ke)||delete m[ke]};for(const m of e.pointBarriers)f(m.costs)&&(m.addedCost=m.costs[l]??0,d(m.costs));for(const m of e.polygonBarriers)f(m.costs)&&(m.scaleFactor=m.costs[l]??1,d(m.costs));for(const m of e.polylineBarriers)f(m.costs)&&(m.scaleFactor=m.costs[l]??1,d(m.costs));const{routeInfo:a}=e,{findBestSequence:N,preserveFirstStop:w,preserveLastStop:I,startTimeIsUTC:x,timeWindowsAreUTC:st}=o;a.analysisSettings=new Vt({accumulateAttributes:h,directionsLanguage:B,findBestSequence:N,preserveFirstStop:w,preserveLastStop:I,startTimeIsUTC:x,timeWindowsAreUTC:st,travelMode:u}),a.totalDuration=K(((je=a.totalCosts)==null?void 0:je[p])??0,g),a.totalDistance=be(((De=a.totalCosts)==null?void 0:De[y])??0,R),a.totalLateDuration=K(((_e=a.totalViolations)==null?void 0:_e[p])??0,g),a.totalWaitDuration=K(((Ce=a.totalWait)==null?void 0:Ce[p])??0,g),f(a.totalCosts)&&d(a.totalCosts),f(a.totalViolations)&&d(a.totalViolations),f(a.totalWait)&&d(a.totalWait);for(const m of e.stops)f(m.serviceCosts)&&(m.serviceDuration=K(m.serviceCosts[p]??0,g),m.serviceDistance=be(m.serviceCosts[y]??0,R),d(m.serviceCosts)),f(m.cumulativeCosts)&&(m.cumulativeDuration=K(m.cumulativeCosts[p]??0,g),m.cumulativeDistance=be(m.cumulativeCosts[y]??0,R),d(m.cumulativeCosts)),f(m.violations)&&(m.lateDuration=K(m.violations[p]??0,g),d(m.violations)),f(m.wait)&&(m.waitDuration=K(m.wait[p]??0,g),d(m.wait))}async function Ee(e){const t=V.WGS84;return await At(e.spatialReference,t),Ot(e,t)}function K(e,t){switch(t){case"seconds":return e/60;case"hours":return 60*e;case"days":return 60*e*24;default:return e}}function be(e,t){return t==="decimal-degrees"||t==="points"||t==="unknown"?e:lt(e,t,"meters")}function Rr(e){const{attributes:t,geometry:o,popupTemplate:i,symbol:n}=e.toGraphic().toJSON();return{attributes:t,geometry:o,popupInfo:i,symbol:n}}const Pr=j.ofType(ne),Ir=j.ofType(ae),Ge=j.ofType(ee),qe=j.ofType(te),We=j.ofType(re),Ze=j.ofType(oe),rt="esri.layers.RouteLayer",ot=Ve.getLogger(rt);let b=class extends ht(vt(wt(gt(St(bt(Lt)))))){constructor(e){super(e),this._cachedServiceDescription=null,this._featureCollection=null,this._type="Feature Collection",this.defaultSymbols=new Ye,this.directionLines=null,this.directionPoints=null,this.featureCollectionType="route",this.legendEnabled=!1,this.maxScale=0,this.minScale=0,this.pointBarriers=new Ge,this.polygonBarriers=new qe,this.polylineBarriers=new We,this.routeInfo=null,this.spatialReference=V.WGS84,this.stops=new Ze,this.type="route";const t=()=>{this._setStopSymbol(this.stops)};this.addHandles(jt(()=>this.stops,"change",t,{sync:!0,onListenerAdd:t}))}writeFeatureCollectionWebmap(e,t,o,i){const n=[this._writePolygonBarriers(),this._writePolylineBarriers(),this._writePointBarriers(),this._writeRouteInfo(),this._writeDirectionLines(),this._writeDirectionPoints(),this._writeStops()].filter(y=>!!y),u=n.map((y,v)=>v),p=i.origin==="web-map"?"featureCollection.layers":"layers";ut(p,n,t),t.opacity=this.opacity,t.visibility=this.visible,t.visibleLayers=u}readDirectionLines(e,t){return this._getNetworkFeatures(t,"DirectionLines",o=>ne.fromGraphic(o))}readDirectionPoints(e,t){return this._getNetworkFeatures(t,"DirectionPoints",o=>ae.fromGraphic(o))}get fullExtent(){const e=new Ke({xmin:-180,ymin:-90,xmax:180,ymax:90,spatialReference:V.WGS84});if(f(this.routeInfo)&&f(this.routeInfo.geometry))return this.routeInfo.geometry.extent??e;if($(this.stops))return e;const t=this.stops.filter(n=>f(n.geometry));if(t.length<2)return e;const{spatialReference:o}=t.getItemAt(0).geometry;if($(o))return e;const i=t.toArray().map(n=>{const u=n.geometry;return[u.x,u.y]});return new Bt({points:i,spatialReference:o}).extent}readMaxScale(e,t){var i,n;const o=(n=me(t)?t.layers:(i=t.featureCollection)==null?void 0:i.layers)==null?void 0:n.find(u=>f(u.layerDefinition.maxScale));return(o==null?void 0:o.layerDefinition.maxScale)??0}readMinScale(e,t){var i,n;const o=(n=me(t)?t.layers:(i=t.featureCollection)==null?void 0:i.layers)==null?void 0:n.find(u=>f(u.layerDefinition.minScale));return(o==null?void 0:o.layerDefinition.minScale)??0}readPointBarriers(e,t){return this._getNetworkFeatures(t,"Barriers",o=>ee.fromGraphic(o))}readPolygonBarriers(e,t){return this._getNetworkFeatures(t,"PolygonBarriers",o=>te.fromGraphic(o))}readPolylineBarriers(e,t){return this._getNetworkFeatures(t,"PolylineBarriers",o=>re.fromGraphic(o))}readRouteInfo(e,t){const o=this._getNetworkFeatures(t,"RouteInfo",i=>ue.fromGraphic(i));return o.length>0?o.getItemAt(0):null}readSpatialReference(e,t){var y,v;const o=me(t)?t.layers:(y=t.featureCollection)==null?void 0:y.layers;if(!(o!=null&&o.length))return V.WGS84;const{layerDefinition:i,featureSet:n}=o[0],u=n.features[0],p=((v=se(u==null?void 0:u.geometry))==null?void 0:v.spatialReference)??n.spatialReference??i.spatialReference??i.extent.spatialReference??Me;return V.fromJSON(p)}readStops(e,t){return this._getNetworkFeatures(t,"Stops",o=>oe.fromGraphic(o),o=>this._setStopSymbol(o))}get title(){return f(this.routeInfo)&&f(this.routeInfo.name)?this.routeInfo.name:"Route"}set title(e){this._overrideIfSome("title",e)}get url(){return xe.routeServiceUrl}set url(e){e!=null?this._set("url",Tt(e,ot)):this._set("url",xe.routeServiceUrl)}load(e){return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Feature Collection"]},e)),Promise.resolve(this)}removeAll(){this.removeResult(),this.pointBarriers.removeAll(),this.polygonBarriers.removeAll(),this.polylineBarriers.removeAll(),this.stops.removeAll()}removeResult(){f(this.directionLines)&&(this.directionLines.removeAll(),this._set("directionLines",null)),f(this.directionPoints)&&(this.directionPoints.removeAll(),this._set("directionPoints",null)),f(this.routeInfo)&&this._set("routeInfo",null)}async save(){await this.load();const{fullExtent:e,portalItem:t}=this;if(!t)throw new C("routelayer:portal-item-not-set","save() requires to the layer to have a portal item");if(!t.id)throw new C("routelayer:portal-item-not-saved","Please use saveAs() first to save the routelayer");if(t.type!=="Feature Collection")throw new C("routelayer:portal-item-wrong-type",'Portal item needs to have type "Feature Collection"');if($(this.routeInfo))throw new C("routelayer:route-unsolved","save() requires a solved route");const{portal:o}=t;await o.signIn(),o.user||await t.reload();const{itemUrl:i,itemControl:n}=t;if(n!=="admin"&&n!=="update")throw new C("routelayer:insufficient-permissions","To save this layer, you need to be the owner or an administrator of your organization");const u={messages:[],origin:"portal-item",portal:o,url:i?Dt(i):void 0,writtenProperties:[]},p=this.write(void 0,u);return t.extent=await Ee(e),t.title=this.title,await t.update({data:p}),t}async saveAs(e,t={}){var y;if(await this.load(),$(this.routeInfo))throw new C("routelayer:route-unsolved","saveAs() requires a solved route");const o=Nt.from(e).clone();o.extent??(o.extent=await Ee(this.fullExtent)),o.id=null,o.portal??(o.portal=Rt.getDefault()),o.title??(o.title=this.title),o.type="Feature Collection",o.typeKeywords=["Data","Feature Collection",Pt.MULTI_LAYER,"Route Layer"];const{portal:i}=o,n={messages:[],origin:"portal-item",portal:i,url:null,writtenProperties:[]};await i.signIn();const u=t==null?void 0:t.folder,p=this.write(void 0,n);return await((y=i.user)==null?void 0:y.addItem({item:o,folder:u,data:p})),this.portalItem=o,_t(n),n.portalItem=o,o}async solve(e,t){const o=(e==null?void 0:e.stops)??this.stops,i=(e==null?void 0:e.pointBarriers)??Se(this.pointBarriers),n=(e==null?void 0:e.polylineBarriers)??Se(this.polylineBarriers),u=(e==null?void 0:e.polygonBarriers)??Se(this.polygonBarriers);if($(o))throw new C("routelayer:undefined-stops","the route layer must have stops defined in the route parameters.");if((Br(o)||Tr(o))&&o.features.length<2||j.isCollection(o)&&o.length<2)throw new C("routelayer:insufficent-stops","the route layer must have two or more stops to solve a route.");if(j.isCollection(o))for(const d of o)d.routeName=null;const p=e==null?void 0:e.apiKey,y=this.url,v=await this._getServiceDescription(y,p,t),T=(e==null?void 0:e.travelMode)??v.defaultTravelMode,l=se(e==null?void 0:e.accumulateAttributes)??[];f(T)&&(l.push(T.distanceAttributeName),T.timeAttributeName&&l.push(T.timeAttributeName));const g={startTime:new Date},R={accumulateAttributes:l,directionsOutputType:"featuresets",ignoreInvalidLocations:!0,pointBarriers:i,polylineBarriers:n,polygonBarriers:u,preserveFirstStop:!0,preserveLastStop:!0,returnBarriers:!!i,returnDirections:!0,returnPolygonBarriers:!!u,returnPolylineBarriers:!!n,returnRoutes:!0,returnStops:!0,stops:o},B=e?Ue.from(e):new Ue;for(const d in g)B[d]==null&&(B[d]=g[d]);let h;B.set(R);try{h=await yr(y,B,t)}catch(d){throw at(d)?d:new C("routelayer:failed-route-request","the routing request failed",{error:d})}const S=this._toRouteLayerSolution(h);return this._isOverridden("title")||(this.title=ct(S.routeInfo.name,"Route")),Nr(S,v,B),S}update(e){const{stops:t,directionLines:o,directionPoints:i,pointBarriers:n,polylineBarriers:u,polygonBarriers:p,routeInfo:y}=e;this.set({stops:t,pointBarriers:n,polylineBarriers:u,polygonBarriers:p}),this._set("directionLines",o),this._set("directionPoints",i),this._set("routeInfo",y),f(y.geometry)&&(this.spatialReference=y.geometry.spatialReference)}_getNetworkFeatures(e,t,o,i){var h,S;const n=(S=me(e)?e.layers:(h=e.featureCollection)==null?void 0:h.layers)==null?void 0:S.find(d=>d.layerDefinition.name===t);if($(n))return new j;const{layerDefinition:u,popupInfo:p,featureSet:y}=n,v=u.drawingInfo.renderer,{features:T}=y,l=y.spatialReference??u.spatialReference??u.extent.spatialReference??Me,g=v&&It(v),R=V.fromJSON(l),B=T.map(d=>{const a=Q.fromJSON(d);f(a.geometry)&&f(d.geometry)&&$(d.geometry.spatialReference)&&(a.geometry.spatialReference=R);const N=o(a);return N.symbol??(N.symbol=(g==null?void 0:g.getSymbol(a))??this._getNetworkSymbol(t)),N.popupTemplate??(N.popupTemplate=p&&$t.fromJSON(p)),N});return i&&B.some(d=>!d.symbol)&&i(B),new j(B)}_getNetworkSymbol(e){switch(e){case"Barriers":return this.defaultSymbols.pointBarriers;case"DirectionPoints":return this.defaultSymbols.directionPoints;case"DirectionLines":return this.defaultSymbols.directionLines;case"PolylineBarriers":return this.defaultSymbols.polylineBarriers;case"PolygonBarriers":return this.defaultSymbols.polygonBarriers;case"RouteInfo":return this.defaultSymbols.routeInfo;case"Stops":return null}}async _getServiceDescription(e,t,o){if(f(this._cachedServiceDescription)&&this._cachedServiceDescription.url===e)return this._cachedServiceDescription.serviceDescription;const i=await et(e,t,o);return this._cachedServiceDescription={serviceDescription:i,url:e},i}_setStopSymbol(e){if(!e||e.length===0||$(this.defaultSymbols.stops)||e.every(l=>f(l.symbol)))return;const{first:t,last:o,middle:i,unlocated:n,waypoint:u,break:p}=this.defaultSymbols.stops;if($(this.routeInfo)||e.length===1)return void e.forEach((l,g)=>{switch(g){case 0:l.symbol=t;break;case e.length-1:l.symbol=o;break;default:l.symbol=i}});const y=e.map(l=>l.sequence).filter(l=>f(l)),v=Math.min(...y),T=Math.max(...y);for(const l of e)l.sequence!==v?l.sequence!==T?l.status==="ok"||l.status==="not-located-on-closest"?l.locationType!=="waypoint"?l.locationType!=="break"?l.symbol=i:l.symbol=p:l.symbol=u:l.symbol=n:l.symbol=o:l.symbol=t}_toRouteLayerSolution(e){var T,l,g,R,B,h,S;const t=(T=e.routeResults[0].stops)==null?void 0:T.map(d=>oe.fromJSON(d.toJSON()));this._setStopSymbol(t);const o=new Ze(t),i=new qe((l=e.polygonBarriers)==null?void 0:l.map(d=>{const a=te.fromJSON(d.toJSON());return a.symbol=this.defaultSymbols.polygonBarriers,a})),n=new We((g=e.polylineBarriers)==null?void 0:g.map(d=>{const a=re.fromJSON(d.toJSON());return a.symbol=this.defaultSymbols.polylineBarriers,a})),u=new Ge((R=e.pointBarriers)==null?void 0:R.map(d=>{const a=ee.fromJSON(d.toJSON());return a.symbol=this.defaultSymbols.pointBarriers,a})),p=(B=e.routeResults[0].route)==null?void 0:B.toJSON(),y=ue.fromJSON(p);y.symbol=this.defaultSymbols.routeInfo;const v=new Ir((h=e.routeResults[0].directionPoints)==null?void 0:h.features.map(d=>{const a=ae.fromJSON(d.toJSON());return a.symbol=this.defaultSymbols.directionPoints,a}));return{directionLines:new Pr((S=e.routeResults[0].directionLines)==null?void 0:S.features.map(d=>{const a=ne.fromJSON(d.toJSON());return a.symbol=this.defaultSymbols.directionLines,a})),directionPoints:v,pointBarriers:u,polygonBarriers:i,polylineBarriers:n,routeInfo:y,stops:o}}_writeDirectionLines(){return this._writeNetworkFeatures(this.directionLines,this.defaultSymbols.directionLines,"esriGeometryPolyline",ne.fields,ne.popupInfo,"DirectionLines","Direction Lines")}_writeDirectionPoints(){return this._writeNetworkFeatures(this.directionPoints,this.defaultSymbols.directionPoints,"esriGeometryPoint",ae.fields,ae.popupInfo,"DirectionPoints","Direction Points")}_writeNetworkFeatures(e,t,o,i,n,u,p){if($(e)||!e.length)return null;const y=this.spatialReference.toJSON(),{fullExtent:v,maxScale:T,minScale:l}=this;return{featureSet:{features:e.toArray().map(g=>Rr(g)),geometryType:o,spatialReference:y},layerDefinition:{capabilities:"Query,Update,Editing",drawingInfo:{renderer:{type:"simple",symbol:f(t)?t.toJSON():Pe(o)}},extent:v.toJSON(),fields:i,geometryType:o,hasM:!1,hasZ:!1,maxScale:T,minScale:l,name:u,objectIdField:"ObjectID",spatialReference:y,title:p,type:"Feature Layer",typeIdField:""},popupInfo:n}}_writePointBarriers(){return this._writeNetworkFeatures(this.pointBarriers,this.defaultSymbols.pointBarriers,"esriGeometryPoint",ee.fields,ee.popupInfo,"Barriers","Point Barriers")}_writePolygonBarriers(){return this._writeNetworkFeatures(this.polygonBarriers,this.defaultSymbols.polygonBarriers,"esriGeometryPolygon",te.fields,te.popupInfo,"PolygonBarriers","Polygon Barriers")}_writePolylineBarriers(){return this._writeNetworkFeatures(this.polylineBarriers,this.defaultSymbols.polylineBarriers,"esriGeometryPolyline",re.fields,re.popupInfo,"PolylineBarriers","Line Barriers")}_writeRouteInfo(){return this._writeNetworkFeatures(f(this.routeInfo)?new j([this.routeInfo]):null,this.defaultSymbols.routeInfo,"esriGeometryPolyline",ue.fields,ue.popupInfo,"RouteInfo","Route Details")}_writeStops(){const e=this._writeNetworkFeatures(this.stops,null,"esriGeometryPoint",oe.fields,oe.popupInfo,"Stops","Stops");if($(e))return null;const{stops:t}=this.defaultSymbols,o=f(t)&&f(t.first)&&t.first.toJSON(),i=f(t)&&f(t.middle)&&t.middle.toJSON(),n=f(t)&&f(t.last)&&t.last.toJSON();return e.layerDefinition.drawingInfo.renderer={type:"uniqueValue",field1:"Sequence",defaultSymbol:i,uniqueValueInfos:[{value:"1",symbol:o,label:"First Stop"},{value:`${this.stops.length}`,symbol:n,label:"Last Stop"}]},e}};r([s({readOnly:!0,json:{read:!1,origins:{"portal-item":{write:{allowNull:!0,ignoreOrigin:!0}},"web-map":{write:{overridePolicy(){return{allowNull:!0,ignoreOrigin:this.portalItem==null}}}}}}})],b.prototype,"_featureCollection",void 0),r([Z(["web-map","portal-item"],"_featureCollection")],b.prototype,"writeFeatureCollectionWebmap",null),r([s({readOnly:!0,json:{read:!1,origins:{"web-map":{write:{target:"type",overridePolicy(){return{ignoreOrigin:this.portalItem!=null}}}}}}})],b.prototype,"_type",void 0),r([s({nonNullable:!0,type:Ye})],b.prototype,"defaultSymbols",void 0),r([s({readOnly:!0})],b.prototype,"directionLines",void 0),r([P(["web-map","portal-item"],"directionLines",["layers","featureCollection.layers"])],b.prototype,"readDirectionLines",null),r([s({readOnly:!0})],b.prototype,"directionPoints",void 0),r([P(["web-map","portal-item"],"directionPoints",["layers","featureCollection.layers"])],b.prototype,"readDirectionPoints",null),r([s({readOnly:!0,json:{read:!1,origins:{"web-map":{write:{ignoreOrigin:!0}}}}})],b.prototype,"featureCollectionType",void 0),r([s({readOnly:!0})],b.prototype,"fullExtent",null),r([s({json:{origins:{"web-map":{name:"featureCollection.showLegend"}},write:!0}})],b.prototype,"legendEnabled",void 0),r([s({type:["show","hide"]})],b.prototype,"listMode",void 0),r([s({type:Number,nonNullable:!0,json:{write:!1}})],b.prototype,"maxScale",void 0),r([P(["web-map","portal-item"],"maxScale",["layers","featureCollection.layers"])],b.prototype,"readMaxScale",null),r([s({type:Number,nonNullable:!0,json:{write:!1}})],b.prototype,"minScale",void 0),r([P(["web-map","portal-item"],"minScale",["layers","featureCollection.layers"])],b.prototype,"readMinScale",null),r([s({type:["ArcGISFeatureLayer"],value:"ArcGISFeatureLayer"})],b.prototype,"operationalLayerType",void 0),r([s({nonNullable:!0,type:j.ofType(ee)})],b.prototype,"pointBarriers",void 0),r([P(["web-map","portal-item"],"pointBarriers",["layers","featureCollection.layers"])],b.prototype,"readPointBarriers",null),r([s({nonNullable:!0,type:j.ofType(te)})],b.prototype,"polygonBarriers",void 0),r([P(["web-map","portal-item"],"polygonBarriers",["layers","featureCollection.layers"])],b.prototype,"readPolygonBarriers",null),r([s({nonNullable:!0,type:j.ofType(re)})],b.prototype,"polylineBarriers",void 0),r([P(["web-map","portal-item"],"polylineBarriers",["layers","featureCollection.layers"])],b.prototype,"readPolylineBarriers",null),r([s({readOnly:!0})],b.prototype,"routeInfo",void 0),r([P(["web-map","portal-item"],"routeInfo",["layers","featureCollection.layers"])],b.prototype,"readRouteInfo",null),r([s({type:V})],b.prototype,"spatialReference",void 0),r([P(["web-map","portal-item"],"spatialReference",["layers","featureCollection.layers"])],b.prototype,"readSpatialReference",null),r([s({nonNullable:!0,type:j.ofType(oe)})],b.prototype,"stops",void 0),r([P(["web-map","portal-item"],"stops",["layers","featureCollection.layers"])],b.prototype,"readStops",null),r([s()],b.prototype,"title",null),r([s({readOnly:!0,json:{read:!1}})],b.prototype,"type",void 0),r([s()],b.prototype,"url",null),b=r([L(rt)],b);const zr=b;export{zr as default};
