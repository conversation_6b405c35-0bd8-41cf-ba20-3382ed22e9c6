<!-- 报警分析 -->
<template>
  <div class="wrapper">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="always" class="analyze_card">
          <div class="title_view">
            <span
              style="
                font-weight: 900;
                color: var(--el-text-color-secondary);
                font-size: 18px;
              "
              >报警分析</span
            >
            <div>
              <el-date-picker
                v-model="search.date"
                type="month"
                placeholder="请选择日期"
                style="margin-right: 10px"
                @change="refreshData"
              />
              <el-button @click="thisMonth"> 本月 </el-button>
              <el-button type="primary" @click="exportData"> 导出 </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="wrapper_content">
      <el-col :span="18">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-card shadow="always" class="analyze_card">
              <div class="card_row">
                <div class="card_item">
                  <div class="card_item_title">{{ state.showDate }}月报警情况</div>
                  <div class="card_item_subtitle">
                    {{ state.showDate }}月报警情况概览：共计报警{{ state.total }}次
                  </div>
                  <div class="card_item_divider"></div>
                  <div class="card_item_stats flex">
                    <div class="alarm_stats">
                      <span>{{ state.showDate }}月共计报警(次)</span>
                      <div class="alarm_value">{{ state.total }}</div>
                    </div>
                    <div class="alarm_stats">
                      <span>{{ state.showDate }}月紧急报警(次)</span>
                      <div class="alarm_value">{{ state.emergencyAlarm }}</div>
                    </div>
                  </div>
                </div>
                
                <div class="card_item">
                  <div class="card_item_title">
                    <div>{{ state.showDate }}月报警类型分布</div>
                    <el-button key="" text>
                      <el-icon>
                        <MoreFilled />
                      </el-icon>
                    </el-button>
                  </div>
                  <div class="chart_container">
                    <VChart 
                      :option="state.LXFB" 
                      :autoresize="true"
                    ></VChart>
                  </div>
                </div>
                
                <div class="card_item">
                  <div class="card_item_title">
                    <div>{{ state.showDate }}月报警等级分布</div>
                    <el-button key="" text>
                      <el-icon>
                        <MoreFilled />
                      </el-icon>
                    </el-button>
                  </div>
                  <div class="chart_container">
                    <VChart 
                      :option="state.DJFB"
                      :autoresize="true"
                    ></VChart>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-row
              :gutter="20"
              :class="{ greater_than_three: !(state.tableList.length > 3) }"
            >
              <template v-for="(item, i) in state.tableList" :key="i">
                <el-col :span="arrangement()">
                  <cardTableV
                    :ref="(el) => setRef(el, i)"
                    :title="item.title"
                    :value="item.list"
                    :total="state.tableList.length"
                  ></cardTableV>
                </el-col>
              </template>
            </el-row>
          </el-col>
        </el-row>
      </el-col>

      <el-col :span="6">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-card shadow="always" class="analyze_card" style="height: 735px">
              <div class="card_title">
                <div>紧急报警排序</div>
              </div>
              <el-tabs
                v-model="tabs.activeName"
                class="demo-tabs"
                @tab-change="handleClick"
              >
                <template v-for="(item, i) in tabs.keys" :key="i">
                  <el-tab-pane :label="item" :name="item" />
                </template>
              </el-tabs>
              <div>
                <template v-for="(item, i) in state.tabsList" :key="i">
                  <div class="sort">
                    <div style="line-height: 30px; width: 250px">
                      <span style="margin-right: 10px">{{ i + 1 }}</span
                      ><span>{{ item.label }}</span>
                    </div>
                    <div style="width: 360px; height: 30px">
                      <el-progress :percentage="item.scale">
                        <el-button text style="width: 50px">
                          {{ item.scale }}次
                        </el-button>
                      </el-progress>
                    </div>
                  </div>
                </template>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { MoreFilled } from '@element-plus/icons-vue';
import { setBT, setBJPX } from './components/chart';
import {
  getAlarmAnalysis,
  GetAlarmRankByStation
} from '@/api/shuiwureports/zhandian';
import cardTableV from './components/analyzecardTable.vue';

const search = reactive({
  date: dayjs().format('YYYY-MM')
});

const selectRef = ref({}) as any;

const state = reactive({
  LXFB: setBT(),
  DJFB: setBT(),
  BJPX: setBJPX(),
  showDate: computed(() => dayjs(search.date).month() + 1),
  total: 0,
  emergencyAlarm: 0,
  tableList: [] as any[],
  tabsList: [] as any[]
});

const tabs = reactive({
  activeName: '水厂',
  keys: ['水厂', '泵站', '流量监测站', '压力监测站', '水质监测站'],
  data: [[], [], []] as any[]
});

// const TableConfig = reactive<ICardTable>({
//   defaultExpandAll: true,
//   indexVisible: true,
//   rowKey: 'id',
//   columns: [
//     { label: '规则名称', prop: 'title' },
//     { label: '站点变量ID', prop: 'stationAttrId' }
//   ],
//   operationWidth: '160px',
//   operations: [

//   ],
//   dataList: [],
//   pagination: {
//     hide: true
//   }
// })

const handleClick = (tab: any) => {
  initTabsData(tab);
};

function initEcharts(val) {
  const LXFB = {
    name: ['管网报警', '泵站报警', '水厂报警'],
    data: [0, 0, 0]
  };
  for (const i in val.alarmData) {
    switch (val.alarmData[i].title) {
      case '管网':
        LXFB.data[0] += val.alarmData[i].list.length || 0;
        break;
      case '泵站':
        LXFB.data[1] += val.alarmData[i].list.length || 0;
        break;
      case '水厂':
        LXFB.data[2] += val.alarmData[i].list.length || 0;
        break;
      default:
        break;
    }
  }
  state.LXFB = setBT(LXFB);

  const DJFB = {
    name: ['提醒报警', '重要报警', '紧急报警'],
    data: [0, 0, 0]
  };
  let data: any[] = [];
  val.alarmData.forEach((item) => {
    data = [...data, ...item.list];
  });
  data.forEach((item) => {
    DJFB.data[item.alarmLevel - 1] += 1;
  });

  state.emergencyAlarm = DJFB.data[2];
  state.DJFB = setBT(DJFB);
}

function initTabsData(type: string) {
  const params = {
    startTime: dayjs(search.date).valueOf(),
    endTime: dayjs(search.date).add(1, 'M').valueOf(),
    stationType: type
  };
  GetAlarmRankByStation(params).then((res) => {
    let sum = 0;
    (res.data.data || []).forEach((item) => {
      sum += item.count;
    });
    state.tabsList = (res.data.data || []).map((item) => {
      return {
        label: item.key,
        value: ((item.count / sum) * 100).toFixed(2),
        scale: item.count
      };
    });
  });
}

function arrangement() {
  switch (state.tableList.length) {
    case 0:
      return 24;
    case 1:
      return 24;
    case 2:
      return 12;
    case 3:
      return 8;
    default:
      return 8;
  }
}

// 动态设置ref
const setRef = (el, item) => {
  console.log(el, item);
  if (el) {
    selectRef.value[item] = el;
  }
};

function exportData() {
  state.tableList.forEach((item, i) => {
    selectRef.value[i].reftable.exportTable();
  });
}

function thisMonth() {
  search.date = dayjs().format('YYYY-MM');
  refreshData();
}

function refreshData() {
  const params = {
    startTime: dayjs(search.date).valueOf(),
    endTime: dayjs(search.date).add(1, 'M').valueOf()
  };
  getAlarmAnalysis(params).then((res) => {
    if (res.data.code === 200) {
      state.total = res.data.data.total || 0;
      state.tableList = res.data.data.alarmData || [];

      initEcharts(res.data.data);

      initTabsData(tabs.activeName);
    }
  });
}

onMounted(() => {
  refreshData();
  initTabsData('水厂');
});
</script>

<style lang="scss" scoped>
.wrapper_content {
  height: calc(100vh - 200px); /* 减去可能的顶部导航栏高度 */
  overflow-y: auto;
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.el-col {
  margin-bottom: 16px;
}

// 调整row的间距
:deep(.el-row) {
  margin-left: -10px !important;
  margin-right: -10px !important;
  
  & > .el-col {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
}

.analyze_card {
  padding: -20px;
  transition: all 0.3s ease;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
  margin-bottom: 16px;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08) !important;
  }

  :deep(.el-card__body) {
    padding: 18px;
  }

  .title_view {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
  }
}

.card_row {
  display: flex;
  gap: 15px;
  width: 100%;
  min-height: 300px;
  
  @media screen and (max-width: 1400px) {
    flex-direction: column;
    
    .card_item {
      margin-bottom: 15px;
      height: 300px;
    }
  }
  
  .card_item {
    flex: 1;
    padding: 15px;
    border-radius: 4px;
    background-color: #fafbfe;
    min-width: 0; // 防止flex子项溢出
    display: flex;
    flex-direction: column;
    
    /* 取消不平衡的flex比例 */
    &:nth-child(1), &:nth-child(2), &:nth-child(3) {
      flex: 1;
    }
    
    .card_item_title {
      display: flex;
      font-weight: 600;
      line-height: 32px;
      justify-content: space-between;
      color: var(--el-text-color-secondary);
      font-size: 18px;
      margin-bottom: 10px;
      background: linear-gradient(90deg, #1e88e5, #0d47a1);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      
      div {
        background: linear-gradient(90deg, #1e88e5, #0d47a1);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    
    .card_item_subtitle {
      color: #7e7e7e;
      margin-bottom: 15px;
      font-size: 14px;
    }
    
    .card_item_divider {
      height: 1px;
      background: linear-gradient(90deg, #e0e6f0, transparent);
      margin-bottom: 15px;
    }
    
    .card_item_stats {
      width: 100%;
      justify-content: flex-start;
      gap: 15px;
      margin-top: 10px;

      .alarm_stats {
        background-color: #f2f7ff;
        padding: 15px;
        border-radius: 6px;
        border-left: 3px solid #4a83f7;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        flex: 1;
        
        span {
          color: #4a5269;
          font-weight: 500;
          display: block;
          margin-bottom: 8px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .alarm_value {
          font-size: 28px;
          font-weight: 600;
          line-height: 36px;
          color: #4a83f7;
        }
      }
    }
    
    .chart_container {
      height: 260px; // 进一步增加高度
      width: 100%;
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      padding: 4px;
      flex: 1;
      
      :deep(.echarts) {
        height: 100% !important;
        width: 100% !important;
      }
    }
  }
}

.card_title {
  display: flex;
  font-weight: 600;
  line-height: 32px;
  justify-content: space-between;
  color: var(--el-text-color-secondary);
  font-size: 18px;
  margin-bottom: 5px;
  
  div {
    background: linear-gradient(90deg, #1e88e5, #0d47a1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.card_cutoff {
  background: #f4f7fe;
  height: 70px;
  line-height: 70px;
  text-align: center;
  margin-bottom: 20px;
}

.sort {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.greater_than_three {
  height: calc(100%);
  overflow-y: auto;
  margin-right: -15px;
}

.flex {
  display: flex;
  align-items: center;
}
</style>
