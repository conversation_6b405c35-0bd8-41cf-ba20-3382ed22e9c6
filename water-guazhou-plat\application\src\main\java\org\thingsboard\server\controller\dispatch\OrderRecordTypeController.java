package org.thingsboard.server.controller.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.dispatch.OrderRecordTypeService;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordType;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordTypeSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping({"/api/sp/orderRecordType"})
public class OrderRecordTypeController extends BaseController {
    @Autowired
    private OrderRecordTypeService service;

    public OrderRecordTypeController() {
    }

    @GetMapping({"/{id}"})
    public OrderRecordType findById(@PathVariable String id) {
        return this.service.findById(id);
    }

    @GetMapping
    public IPage<OrderRecordType> findAllConditional(OrderRecordTypePageRequest request) {
        return this.service.findAllConditional(request);
    }

    @PostMapping
    public OrderRecordType save(@RequestBody OrderRecordTypeSaveRequest req) {
        return this.service.save(req);
    }

    @PatchMapping({"/{id}"})
    public boolean edit(@RequestBody OrderRecordTypeSaveRequest req, @PathVariable String id) {
        return this.service.update((OrderRecordType) req.unwrap(id));
    }

    @DeleteMapping({"/{id}"})
    public boolean delete(@PathVariable String id) {
        return this.service.delete(id);
    }
}
