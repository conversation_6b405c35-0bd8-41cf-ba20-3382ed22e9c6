"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[8244],{38171:(e,t,s)=>{s.d(t,{Z:()=>f});var i=s(43697),r=s(66577),l=s(51773),o=s(9790),n=s(2368),a=s(96674),u=s(70586),p=s(99001),c=s(5600),d=(s(75215),s(67676),s(52011)),h=s(33955);function y(e){if(!(0,u.pC)(e))return null;const t={};for(const s in e){const i=e[s];i&&(t[s]=i.toJSON())}return 0!==Object.keys(t).length?t:null}let m=class extends((0,n.J)(a.wq)){constructor(...e){super(...e),this.isAggregate=!1,this.layer=null,this.popupTemplate=null,this.sourceLayer=null,Object.defineProperty(this,"uid",{value:(0,p.D)(),configurable:!0})}normalizeCtorArgs(e,t,s,i){return e&&!e.declaredClass?e:{geometry:e,symbol:t,attributes:s,popupTemplate:i}}set aggregateGeometries(e){const t=this._get("aggregateGeometries");JSON.stringify(t)!==JSON.stringify(e)&&this._set("aggregateGeometries",e)}set attributes(e){const t=this._get("attributes");t!==e&&(this._set("attributes",e),this._notifyLayer("attributes",t,e))}set geometry(e){const t=this._get("geometry");t!==e&&(this._set("geometry",e),this._notifyLayer("geometry",t,e))}set symbol(e){const t=this._get("symbol");t!==e&&(this._set("symbol",e),this._notifyLayer("symbol",t,e))}set visible(e){const t=this._get("visible");t!==e&&(this._set("visible",e),this._notifyLayer("visible",t,e))}getEffectivePopupTemplate(e=!1){if(this.popupTemplate)return this.popupTemplate;for(const t of[this.sourceLayer,this.layer])if(t){if("popupTemplate"in t&&t.popupTemplate)return t.popupTemplate;if(e&&"defaultPopupTemplate"in t&&(0,u.pC)(t.defaultPopupTemplate))return t.defaultPopupTemplate}return null}getAttribute(e){return this.attributes?.[e]}setAttribute(e,t){if(this.attributes){const s=this.getAttribute(e);this.attributes[e]=t,this._notifyLayer("attributes",s,t,e)}else this.attributes={[e]:t},this._notifyLayer("attributes",void 0,t,e)}getObjectId(){return this.sourceLayer&&"objectIdField"in this.sourceLayer&&this.sourceLayer.objectIdField?this.getAttribute(this.sourceLayer.objectIdField):null}toJSON(){return{aggregateGeometries:y(this.aggregateGeometries),geometry:(0,u.pC)(this.geometry)?this.geometry.toJSON():null,symbol:(0,u.pC)(this.symbol)?this.symbol.toJSON():null,attributes:{...this.attributes},popupTemplate:this.popupTemplate&&this.popupTemplate.toJSON()}}notifyGeometryChanged(){this._notifyLayer("geometry",this.geometry,this.geometry)}notifyMeshTransformChanged(){(0,u.pC)(this.geometry)&&"mesh"===this.geometry.type&&this._notifyLayer("transform",this.geometry.transform,this.geometry.transform)}_notifyLayer(e,t,s,i){if(!this.layer||!("graphicChanged"in this.layer))return;const r={graphic:this,property:e,oldValue:t,newValue:s};"attributes"===e&&(r.attributeName=i),this.layer.graphicChanged(r)}};(0,i._)([(0,c.Cb)({value:null,json:{read:function(e){if(!e)return null;const t={};for(const s in e){const i=(0,h.im)(e[s]);i&&(t[s]=i)}return 0!==Object.keys(t).length?t:null}}})],m.prototype,"aggregateGeometries",null),(0,i._)([(0,c.Cb)({value:null})],m.prototype,"attributes",null),(0,i._)([(0,c.Cb)({value:null,types:r.qM,json:{read:h.im}})],m.prototype,"geometry",null),(0,i._)([(0,c.Cb)({type:Boolean})],m.prototype,"isAggregate",void 0),(0,i._)([(0,c.Cb)({clonable:"reference"})],m.prototype,"layer",void 0),(0,i._)([(0,c.Cb)({type:l.Z})],m.prototype,"popupTemplate",void 0),(0,i._)([(0,c.Cb)({clonable:"reference"})],m.prototype,"sourceLayer",void 0),(0,i._)([(0,c.Cb)({value:null,types:o.LB})],m.prototype,"symbol",null),(0,i._)([(0,c.Cb)({type:Boolean,value:!0})],m.prototype,"visible",null),m=(0,i._)([(0,d.j)("esri.Graphic")],m),(m||(m={})).generateUID=p.D;const f=m},74085:(e,t,s)=>{function i(e){}s.d(t,{Bg:()=>i}),s(80442)},16050:(e,t,s)=>{s.d(t,{Z:()=>q});var i,r=s(43697),l=s(9790),o=s(35454),n=s(22974),a=s(92604),u=s(70586),p=s(5600),c=s(90578),d=s(36030),h=s(71715),y=s(52011),m=s(30556),f=s(75215),b=s(35671),v=s(5499),g=s(41733),w=s(32984),_=s(9833),V=s(66338),S=s(59266);const x="percent-of-total",C="field",I=new o.X({esriNormalizeByLog:"log",esriNormalizeByPercentOfTotal:x,esriNormalizeByField:C}),j=(0,f.se)(w.Z);let z=i=class extends((0,g.W)(v.Z)){constructor(e){super(e),this._compiledValueExpression={valueExpression:null,compiledFunction:null},this.backgroundFillSymbol=null,this.classBreakInfos=null,this.defaultLabel=null,this.defaultSymbol=null,this.field=null,this.isMaxInclusive=!0,this.legendOptions=null,this.normalizationField=null,this.normalizationTotal=null,this.type="class-breaks",this.valueExpression=null,this.valueExpressionTitle=null,this._set("classBreakInfos",[])}readClassBreakInfos(e,t,s){if(!Array.isArray(e))return;let i=t.minValue;return e.map((e=>{const t=new w.Z;return t.read(e,s),null==t.minValue&&(t.minValue=i),null==t.maxValue&&(t.maxValue=t.minValue),i=t.maxValue,t}))}writeClassBreakInfos(e,t,s,i){const r=e.map((e=>e.write({},i)));this._areClassBreaksConsecutive()&&r.forEach((e=>delete e.classMinValue)),t[s]=r}castField(e){return null==e?e:"function"==typeof e?(a.Z.getLogger(this.declaredClass).error(".field: field must be a string value"),null):(0,f.Zs)(e)}get minValue(){return this.classBreakInfos&&this.classBreakInfos[0]&&this.classBreakInfos[0].minValue||0}get normalizationType(){let e=this._get("normalizationType");const t=!!this.normalizationField,s=null!=this.normalizationTotal;return t||s?(e=t&&C||s&&x||null,t&&s&&a.Z.getLogger(this.declaredClass).warn("warning: both normalizationField and normalizationTotal are set!")):e!==C&&e!==x||(e=null),e}set normalizationType(e){this._set("normalizationType",e)}addClassBreakInfo(e,t,s){let i=null;i="number"==typeof e?new w.Z({minValue:e,maxValue:t,symbol:(0,l.se)(s)}):j((0,n.d9)(e)),this.classBreakInfos.push(i),1===this.classBreakInfos.length&&this.notifyChange("minValue")}removeClassBreakInfo(e,t){const s=this.classBreakInfos.length;for(let i=0;i<s;i++){const s=[this.classBreakInfos[i].minValue,this.classBreakInfos[i].maxValue];if(s[0]===e&&s[1]===t){this.classBreakInfos.splice(i,1);break}}}getBreakIndex(e,t){return this.valueExpression&&((0,u.Wi)(t)||(0,u.Wi)(t.arcade))&&a.Z.getLogger(this.declaredClass).warn(""),this.valueExpression?this._getBreakIndexForExpression(e,t):this._getBreakIndexForField(e)}async getClassBreakInfo(e,t){let s=t;this.valueExpression&&((0,u.Wi)(t)||(0,u.Wi)(t.arcade))&&(s={...s,arcade:await(0,S.LC)()});const i=this.getBreakIndex(e,s);return-1!==i?this.classBreakInfos[i]:null}getSymbol(e,t){if(this.valueExpression&&((0,u.Wi)(t)||(0,u.Wi)(t.arcade)))return void a.Z.getLogger(this.declaredClass).error("#getSymbol()","Please use getSymbolAsync if valueExpression is used");const s=this.getBreakIndex(e,t);return s>-1?this.classBreakInfos[s].symbol:this.defaultSymbol}async getSymbolAsync(e,t){let s=t;if(this.valueExpression&&((0,u.Wi)(t)||(0,u.Wi)(t.arcade))){const e=await(0,S.LC)(),{arcadeUtils:t}=e;t.hasGeometryOperations(this.valueExpression)&&await t.enableGeometryOperations(),s={...s,arcade:e}}const i=this.getBreakIndex(e,s);return i>-1?this.classBreakInfos[i].symbol:this.defaultSymbol}getSymbols(){const e=[];return this.classBreakInfos.forEach((t=>{t.symbol&&e.push(t.symbol)})),this.defaultSymbol&&e.push(this.defaultSymbol),e}getAttributeHash(){return this.visualVariables&&this.visualVariables.reduce(((e,t)=>e+t.getAttributeHash()),"")}getMeshHash(){const e=JSON.stringify(this.backgroundFillSymbol),t=JSON.stringify(this.defaultSymbol),s=`${this.normalizationField}.${this.normalizationType}.${this.normalizationTotal}`;return`${e}.${t}.${this.classBreakInfos.reduce(((e,t)=>e+t.getMeshHash()),"")}.${s}.${this.field}.${this.valueExpression}`}get arcadeRequired(){return this.arcadeRequiredForVisualVariables||!!this.valueExpression}clone(){return new i({field:this.field,backgroundFillSymbol:this.backgroundFillSymbol&&this.backgroundFillSymbol.clone(),defaultLabel:this.defaultLabel,defaultSymbol:this.defaultSymbol&&this.defaultSymbol.clone(),valueExpression:this.valueExpression,valueExpressionTitle:this.valueExpressionTitle,classBreakInfos:(0,n.d9)(this.classBreakInfos),isMaxInclusive:this.isMaxInclusive,normalizationField:this.normalizationField,normalizationTotal:this.normalizationTotal,normalizationType:this.normalizationType,visualVariables:(0,n.d9)(this.visualVariables),legendOptions:(0,n.d9)(this.legendOptions),authoringInfo:this.authoringInfo&&this.authoringInfo.clone()})}async collectRequiredFields(e,t){const s=[this.collectVVRequiredFields(e,t),this.collectSymbolFields(e,t)];await Promise.all(s)}async collectSymbolFields(e,t){const s=[...this.getSymbols().map((s=>s.collectRequiredFields(e,t))),(0,b.io)(e,t,this.valueExpression)];(0,b.AB)(e,t,this.field),(0,b.AB)(e,t,this.normalizationField),await Promise.all(s)}_getBreakIndexForExpression(e,t){const{viewingMode:s,scale:i,spatialReference:r,arcade:l}=(0,u.Pt)(t,{}),{valueExpression:o}=this;let n=this._compiledValueExpression.valueExpression===o?this._compiledValueExpression.compiledFunction:null;const a=(0,u.Wg)(l).arcadeUtils;if(!n){const e=a.createSyntaxTree(o);n=a.createFunction(e),this._compiledValueExpression.compiledFunction=n}this._compiledValueExpression.valueExpression=o;const p=a.executeFunction(n,a.createExecContext(e,a.getViewInfo({viewingMode:s,scale:i,spatialReference:r})));return this._getBreakIndexfromInfos(p)}_getBreakIndexForField(e){const t=this.field,s=e.attributes,i=this.normalizationType;let r=parseFloat(s[t]);if(i){const e=this.normalizationTotal,t=parseFloat(this.normalizationField?s[this.normalizationField]:void 0);if("log"===i)r=Math.log(r)*Math.LOG10E;else if(i!==x||null==e||isNaN(e)){if(i===C&&!isNaN(t)){if(isNaN(r)||isNaN(t))return-1;r/=t}}else r=r/e*100}return this._getBreakIndexfromInfos(r)}_getBreakIndexfromInfos(e){const t=this.isMaxInclusive;if(null!=e&&"number"==typeof e&&!isNaN(e))for(let s=0;s<this.classBreakInfos.length;s++){const i=[this.classBreakInfos[s].minValue,this.classBreakInfos[s].maxValue];if(i[0]<=e&&(t?e<=i[1]:e<i[1]))return s}return-1}_areClassBreaksConsecutive(){const e=this.classBreakInfos,t=e.length;for(let s=1;s<t;s++)if(e[s-1].maxValue!==e[s].minValue)return!1;return!0}};(0,r._)([(0,p.Cb)(_.KK)],z.prototype,"backgroundFillSymbol",void 0),(0,r._)([(0,p.Cb)({type:[w.Z]})],z.prototype,"classBreakInfos",void 0),(0,r._)([(0,h.r)("classBreakInfos")],z.prototype,"readClassBreakInfos",null),(0,r._)([(0,m.c)("classBreakInfos")],z.prototype,"writeClassBreakInfos",null),(0,r._)([(0,p.Cb)({type:String,json:{write:!0}})],z.prototype,"defaultLabel",void 0),(0,r._)([(0,p.Cb)(_.Gn)],z.prototype,"defaultSymbol",void 0),(0,r._)([(0,p.Cb)({type:String,json:{write:!0}})],z.prototype,"field",void 0),(0,r._)([(0,c.p)("field")],z.prototype,"castField",null),(0,r._)([(0,p.Cb)({type:Boolean})],z.prototype,"isMaxInclusive",void 0),(0,r._)([(0,p.Cb)({type:V.I,json:{write:!0}})],z.prototype,"legendOptions",void 0),(0,r._)([(0,p.Cb)({type:Number,readOnly:!0,value:null,json:{read:!1,write:{overridePolicy(){return 0!==this.classBreakInfos.length&&this._areClassBreaksConsecutive()?{enabled:!0}:{enabled:!1}}}}})],z.prototype,"minValue",null),(0,r._)([(0,p.Cb)({type:String,json:{write:!0}})],z.prototype,"normalizationField",void 0),(0,r._)([(0,p.Cb)({type:Number,cast:e=>(0,f.q9)(e),json:{write:!0}})],z.prototype,"normalizationTotal",void 0),(0,r._)([(0,p.Cb)({type:I.apiValues,value:null,json:{type:I.jsonValues,read:I.read,write:I.write}})],z.prototype,"normalizationType",null),(0,r._)([(0,d.J)({classBreaks:"class-breaks"})],z.prototype,"type",void 0),(0,r._)([(0,p.Cb)({type:String,json:{write:!0}})],z.prototype,"valueExpression",void 0),(0,r._)([(0,p.Cb)({type:String,json:{write:!0}})],z.prototype,"valueExpressionTitle",void 0),z=i=(0,r._)([(0,y.j)("esri.renderers.ClassBreaksRenderer")],z);const q=z},5499:(e,t,s)=>{s.d(t,{Z:()=>c});var i=s(43697),r=s(35454),l=s(96674),o=s(5600),n=(s(75215),s(67676),s(52011)),a=s(69237);const u=new r.X({simple:"simple",uniqueValue:"unique-value",classBreaks:"class-breaks",heatmap:"heatmap",dotDensity:"dot-density",dictionary:"dictionary",pieChart:"pie-chart"},{ignoreUnknown:!0});let p=class extends l.wq{constructor(e){super(e),this.authoringInfo=null,this.type=null}async getRequiredFields(e){if(!this.collectRequiredFields)return[];const t=new Set;return await this.collectRequiredFields(t,e),Array.from(t).sort()}getSymbol(e,t){}async getSymbolAsync(e,t){}getSymbols(){return[]}getAttributeHash(){return JSON.stringify(this)}getMeshHash(){return JSON.stringify(this)}};(0,i._)([(0,o.Cb)({type:a.Z,json:{write:!0}})],p.prototype,"authoringInfo",void 0),(0,i._)([(0,o.Cb)({type:u.apiValues,readOnly:!0,json:{type:u.jsonValues,read:!1,write:{writer:u.write,ignoreOrigin:!0}}})],p.prototype,"type",void 0),p=(0,i._)([(0,n.j)("esri.renderers.Renderer")],p);const c=p},81571:(e,t,s)=>{s.d(t,{Z:()=>$});var i=s(43697),r=s(9790),l=s(20102),o=s(22974),n=s(92604),a=s(70586),u=s(78286),p=s(17445),c=s(5600),d=s(90578),h=s(36030),y=s(71715),m=s(52011),f=s(30556),b=s(22862),v=s(75215),g=s(35671),w=s(65587),_=s(5499),V=s(41733),S=s(9833),x=s(66338),C=s(2368),I=s(96674);s(67676);let j=class extends((0,C.J)(I.wq)){constructor(e){super(e),this.value=null,this.value2=null,this.value3=null}};(0,i._)([(0,c.Cb)(S.GY)],j.prototype,"value",void 0),(0,i._)([(0,c.Cb)(S.GY)],j.prototype,"value2",void 0),(0,i._)([(0,c.Cb)(S.GY)],j.prototype,"value3",void 0),j=(0,i._)([(0,m.j)("esri.renderers.support.UniqueValue")],j);const z=j;let q=class extends((0,C.J)(I.wq)){constructor(e){super(e),this.description=null,this.label=null,this.symbol=null,this.values=null}castValues(e){if(null==e)return null;const t=typeof(e=Array.isArray(e)?e:[e])[0];return"string"===t||"number"===t?e.map((e=>new z({value:e}))):"object"===t?e[0]instanceof z?e:e.map((e=>new z(e))):null}};(0,i._)([(0,c.Cb)({type:String,json:{write:!0}})],q.prototype,"description",void 0),(0,i._)([(0,c.Cb)({type:String,json:{write:!0}})],q.prototype,"label",void 0),(0,i._)([(0,c.Cb)(S.Gn)],q.prototype,"symbol",void 0),(0,i._)([(0,c.Cb)({type:[z],json:{type:[[String]],read:{reader:e=>e?e.map((e=>new z({value:e[0],value2:e[1],value3:e[2]}))):null},write:{writer:(e,t)=>{const s=[];for(const t of e){const e=[t.value,t.value2,t.value3].filter(a.pC).map((e=>e.toString()));s.push(e)}t.values=s}}}})],q.prototype,"values",void 0),(0,i._)([(0,d.p)("values")],q.prototype,"castValues",null),q=(0,i._)([(0,m.j)("esri.renderers.support.UniqueValueClass")],q);const F=q;let E=class extends((0,C.J)(I.wq)){constructor(e){super(e),this.heading=null,this.classes=null}};(0,i._)([(0,c.Cb)({type:String,json:{write:!0}})],E.prototype,"heading",void 0),(0,i._)([(0,c.Cb)({type:[F],json:{write:!0}})],E.prototype,"classes",void 0),E=(0,i._)([(0,m.j)("esri.renderers.support.UniqueValueGroup")],E);const T=E;var k,O=s(44262),Z=s(59266),D=s(25929),U=s(27883),B=s(59390);const L="esri.renderers.UniqueValueRenderer",M=n.Z.getLogger(L),N="uvInfos-watcher",R="uvGroups-watcher",A=(0,v.se)(O.Z);let G=k=class extends((0,V.W)(_.Z)){constructor(e){super(e),this._valueInfoMap={},this._isDefaultSymbolDerived=!1,this._isInfosSource=null,this.type="unique-value",this.backgroundFillSymbol=null,this.orderByClassesEnabled=!1,this.valueExpressionTitle=null,this.legendOptions=null,this.defaultLabel=null,this.portal=null,this.styleOrigin=null,this.diff={uniqueValueInfos(e,t){if(!e&&!t)return;if(!e||!t)return{type:"complete",oldValue:e,newValue:t};let s=!1;const i={type:"collection",added:[],removed:[],changed:[],unchanged:[]};for(let r=0;r<t.length;r++){const l=e.find((e=>e.value===t[r].value));l?(0,b.Hg)(l,t[r])?(i.changed.push({type:"complete",oldValue:l,newValue:t[r]}),s=!0):i.unchanged.push({oldValue:l,newValue:t[r]}):(i.added.push(t[r]),s=!0)}for(let r=0;r<e.length;r++)t.find((t=>t.value===e[r].value))||(i.removed.push(e[r]),s=!0);return s?i:void 0}},this._set("uniqueValueInfos",[]),this._set("uniqueValueGroups",[])}get _cache(){return{compiledFunc:null}}set field(e){this._set("field",e),this._updateFieldDelimiter(),this._updateUniqueValues()}castField(e){return null==e||"function"==typeof e?e:(0,v.Zs)(e)}writeField(e,t,s,i){"string"==typeof e?t[s]=e:i&&i.messages?i.messages.push(new l.Z("property:unsupported","UniqueValueRenderer.field set to a function cannot be written to JSON")):M.error(".field: cannot write field to JSON since it's not a string value")}set field2(e){this._set("field2",e),this._updateFieldDelimiter(),this._updateUniqueValues()}set field3(e){this._set("field3",e),this._updateUniqueValues()}set valueExpression(e){this._set("valueExpression",e),this._updateUniqueValues()}set defaultSymbol(e){this._isDefaultSymbolDerived=!1,this._set("defaultSymbol",e)}set fieldDelimiter(e){this._set("fieldDelimiter",e),this._updateUniqueValues()}readPortal(e,t,s){return s.portal||w.Z.getDefault()}readStyleOrigin(e,t,s){if(t.styleName)return Object.freeze({styleName:t.styleName});if(t.styleUrl){const e=(0,D.f)(t.styleUrl,s);return Object.freeze({styleUrl:e})}}writeStyleOrigin(e,t,s,i){e.styleName?t.styleName=e.styleName:e.styleUrl&&(t.styleUrl=(0,D.t)(e.styleUrl,i))}set uniqueValueGroups(e){this.styleOrigin?M.error("#uniqueValueGroups=","Cannot modify unique value groups of a UniqueValueRenderer created from a web style"):(this._set("uniqueValueGroups",e),this._updateInfosFromGroups(),this._isInfosSource=!1,this._watchUniqueValueGroups())}set uniqueValueInfos(e){this.styleOrigin?M.error("#uniqueValueInfos=","Cannot modify unique value infos of a UniqueValueRenderer created from a web style"):(this._set("uniqueValueInfos",e),this._updateValueInfoMap(),this._updateGroupsFromInfos(),this._isInfosSource=!0,this._watchUniqueValueInfos())}addUniqueValueInfo(e,t){if(this.styleOrigin)return void M.error("#addUniqueValueInfo()","Cannot modify unique value infos of a UniqueValueRenderer created from a web style");let s;s="object"==typeof e?A(e):new O.Z({value:e,symbol:(0,r.se)(t)}),this.uniqueValueInfos?.push(s),this._valueInfoMap[s.value]=s,this._updateGroupsFromInfos(),this._isInfosSource=!0,this._watchUniqueValueInfos()}removeUniqueValueInfo(e){if(this.styleOrigin)return void M.error("#removeUniqueValueInfo()","Cannot modify unique value infos of a UniqueValueRenderer created from a web style");const t=this.uniqueValueInfos;if(t)for(let s=0;s<t.length;s++)if(t[s].value===e+""){delete this._valueInfoMap[e],t.splice(s,1);break}this._updateGroupsFromInfos(),this._isInfosSource=!0,this._watchUniqueValueInfos()}async getUniqueValueInfo(e,t){let s=t;return this.valueExpression&&((0,a.Wi)(t)||(0,a.Wi)(t.arcade))&&(s={...s,arcade:await(0,Z.LC)()}),this._getUniqueValueInfo(e,s)}getSymbol(e,t){if(this.valueExpression&&((0,a.Wi)(t)||(0,a.Wi)(t.arcade)))return void M.error("#getSymbol()","Please use getSymbolAsync if valueExpression is used");const s=this._getUniqueValueInfo(e,t);return s&&s.symbol||this.defaultSymbol}async getSymbolAsync(e,t){let s=t;if(this.valueExpression&&((0,a.Wi)(s)||(0,a.Wi)(s.arcade))){const e=await(0,Z.LC)(),{arcadeUtils:t}=e;t.hasGeometryOperations(this.valueExpression)&&await t.enableGeometryOperations(),s={...s,arcade:e}}const i=this._getUniqueValueInfo(e,s);return i&&i.symbol||this.defaultSymbol}getSymbols(){const e=[];for(const t of this.uniqueValueInfos??[])t.symbol&&e.push(t.symbol);return this.defaultSymbol&&e.push(this.defaultSymbol),e}getAttributeHash(){return this.visualVariables&&this.visualVariables.reduce(((e,t)=>e+t.getAttributeHash()),"")}getMeshHash(){const e=JSON.stringify(this.backgroundFillSymbol),t=JSON.stringify(this.defaultSymbol),s=this.uniqueValueInfos?.reduce(((e,t)=>e+t.getMeshHash()),"");return`${e}.${t}.${s}.${this.field}.${this.field2}.${this.field3}.${this.fieldDelimiter}.${this.valueExpression}`}clone(){const e=new k({field:this.field,field2:this.field2,field3:this.field3,defaultLabel:this.defaultLabel,defaultSymbol:(0,o.d9)(this.defaultSymbol),orderByClassesEnabled:this.orderByClassesEnabled,valueExpression:this.valueExpression,valueExpressionTitle:this.valueExpressionTitle,fieldDelimiter:this.fieldDelimiter,visualVariables:(0,o.d9)(this.visualVariables),legendOptions:(0,o.d9)(this.legendOptions),authoringInfo:this.authoringInfo&&this.authoringInfo.clone(),backgroundFillSymbol:(0,o.d9)(this.backgroundFillSymbol)});this._isDefaultSymbolDerived&&(e._isDefaultSymbolDerived=!0),e._set("portal",this.portal);const t=(0,o.d9)(this.uniqueValueInfos),s=(0,o.d9)(this.uniqueValueGroups);return this.styleOrigin&&(e._set("styleOrigin",Object.freeze((0,o.d9)(this.styleOrigin))),Object.freeze(t),Object.freeze(s)),e._set("uniqueValueInfos",t),e._updateValueInfoMap(),e._set("uniqueValueGroups",s),e._isInfosSource=this._isInfosSource,e._watchUniqueValueInfosAndGroups(),e}get arcadeRequired(){return this.arcadeRequiredForVisualVariables||!!this.valueExpression}async collectRequiredFields(e,t){const s=[this.collectVVRequiredFields(e,t),this.collectSymbolFields(e,t)];await Promise.all(s)}async collectSymbolFields(e,t){const s=[...this.getSymbols().map((s=>s.collectRequiredFields(e,t))),(0,g.io)(e,t,this.valueExpression)];(0,g.AB)(e,t,this.field),(0,g.AB)(e,t,this.field2),(0,g.AB)(e,t,this.field3),await Promise.all(s)}populateFromStyle(){return(0,U.n2)(this.styleOrigin,{portal:this.portal}).then((e=>{const t=[];return this._valueInfoMap={},e&&e.data&&Array.isArray(e.data.items)&&e.data.items.forEach((s=>{const i=new B.Z({styleUrl:e.styleUrl,styleName:e.styleName,portal:this.portal,name:s.name});this.defaultSymbol||s.name!==e.data.defaultItem||(this.defaultSymbol=i,this._isDefaultSymbolDerived=!0);const r=new O.Z({value:s.name,symbol:i});t.push(r),this._valueInfoMap[s.name]=r})),this._set("uniqueValueInfos",Object.freeze(t)),this._updateGroupsFromInfos(!0),this._isInfosSource=null,this._watchUniqueValueInfos(),!this.defaultSymbol&&this.uniqueValueInfos?.length&&(this.defaultSymbol=this.uniqueValueInfos[0].symbol,this._isDefaultSymbolDerived=!0),this}))}_updateFieldDelimiter(){this.field&&this.field2&&!this.fieldDelimiter&&this._set("fieldDelimiter",",")}_updateUniqueValues(){null!=this._isInfosSource&&(this._isInfosSource?this._updateGroupsFromInfos():this._updateInfosFromGroups())}_updateValueInfoMap(){this._valueInfoMap={};const{uniqueValueInfos:e}=this;if(e)for(const t of e)this._valueInfoMap[t.value+""]=t}_watchUniqueValueInfosAndGroups(){this._watchUniqueValueInfos(),this._watchUniqueValueGroups()}_watchUniqueValueInfos(){this.removeHandles(N);const{uniqueValueInfos:e}=this;if(e){const t=[];for(const s of e)t.push((0,p.YP)((()=>({symbol:s.symbol,value:s.value,label:s.label,description:s.description})),((e,t)=>{e!==t&&(this._updateGroupsFromInfos(),this._isInfosSource=!0)}),{sync:!0}));this.addHandles(t,N)}}_watchUniqueValueGroups(){this.removeHandles(R);const{uniqueValueGroups:e}=this;if(e){const t=[];for(const s of e){t.push((0,p.YP)((()=>({classes:s.classes})),((e,t)=>{e!==t&&(this._updateInfosFromGroups(),this._isInfosSource=!1)}),{sync:!0}));for(const e of s.classes??[])t.push((0,p.YP)((()=>({symbol:e.symbol,values:e.values,label:e.label,description:e.description})),((e,t)=>{e!==t&&(this._updateInfosFromGroups(),this._isInfosSource=!1)}),{sync:!0}))}this.addHandles(t,R)}}_updateInfosFromGroups(){if(!this.uniqueValueGroups)return this._set("uniqueValueInfos",null),this._updateValueInfoMap(),void this._watchUniqueValueInfos();const e=[],{field:t,field2:s,field3:i,fieldDelimiter:r,uniqueValueGroups:l,valueExpression:o}=this;if(!t&&!o)return this._set("uniqueValueInfos",e),this._updateValueInfoMap(),void this._watchUniqueValueInfos();const n=!(!t||!s);for(const t of l)for(const l of t.classes??[]){const{symbol:t,label:o,values:a,description:u}=l;for(const l of a??[]){const{value:a,value2:p,value3:c}=l,d=[a];s&&d.push(p),i&&d.push(c);const h=n?d.join(r||""):d[0];e.push(new O.Z({symbol:t,label:o,value:h,description:u}))}}this._set("uniqueValueInfos",e),this._updateValueInfoMap(),this._watchUniqueValueInfos()}_updateGroupsFromInfos(e=!1){if(!this.uniqueValueInfos)return this._set("uniqueValueGroups",null),void this._watchUniqueValueGroups();const{field:t,field2:s,valueExpression:i,fieldDelimiter:r,uniqueValueInfos:l}=this;if(!t&&!i||!l.length)return this._set("uniqueValueGroups",[]),void this._watchUniqueValueGroups();const o=!(!t||!s),n=l.map((e=>{const{symbol:t,label:s,value:i,description:l}=e,[n,a,u]=o?i?.toString()?.split(r||"")||[]:[i];return new F({symbol:t,label:s,description:l,values:[new z({value:n,value2:a,value3:u})]})})),a=[new T({classes:n})];e&&Object.freeze(a),this._set("uniqueValueGroups",a),this._watchUniqueValueGroups()}_getUniqueValueInfo(e,t){return this.valueExpression?this._getUnqiueValueInfoForExpression(e,t):this._getUnqiueValueInfoForFields(e)}_getUnqiueValueInfoForExpression(e,t){const{viewingMode:s,scale:i,spatialReference:r,arcade:l}=(0,a.Pt)(t,{});let o=this._cache.compiledFunc;const n=(0,a.Wg)(l).arcadeUtils;if(!o){const e=n.createSyntaxTree(this.valueExpression);o=n.createFunction(e),this._cache.compiledFunc=o}const u=n.executeFunction(o,n.createExecContext(e,n.getViewInfo({viewingMode:s,scale:i,spatialReference:r})));return this._valueInfoMap[u+""]}_getUnqiueValueInfoForFields(e){const t=this.field,s=e.attributes;let i;if("function"!=typeof t&&this.field2){const e=this.field2,r=this.field3,l=[];t&&l.push(s[t]),e&&l.push(s[e]),r&&l.push(s[r]),i=l.join(this.fieldDelimiter||"")}else"function"==typeof t?i=t(e):t&&(i=s[t]);return this._valueInfoMap[i+""]}static fromPortalStyle(e,t){const s=new k(t&&t.properties);s._set("styleOrigin",Object.freeze({styleName:e})),s._set("portal",t&&t.portal||w.Z.getDefault());const i=s.populateFromStyle();return i.catch((t=>{M.error(`#fromPortalStyle('${e}'[, ...])`,"Failed to create unique value renderer from style name",t)})),i}static fromStyleUrl(e,t){const s=new k(t&&t.properties);s._set("styleOrigin",Object.freeze({styleUrl:e}));const i=s.populateFromStyle();return i.catch((t=>{M.error(`#fromStyleUrl('${e}'[, ...])`,"Failed to create unique value renderer from style URL",t)})),i}};(0,i._)([(0,c.Cb)({readOnly:!0})],G.prototype,"_cache",null),(0,i._)([(0,h.J)({uniqueValue:"unique-value"})],G.prototype,"type",void 0),(0,i._)([(0,c.Cb)(S.KK)],G.prototype,"backgroundFillSymbol",void 0),(0,i._)([(0,c.Cb)({value:null,json:{type:String,read:{source:"field1"},write:{target:"field1"}}})],G.prototype,"field",null),(0,i._)([(0,d.p)("field")],G.prototype,"castField",null),(0,i._)([(0,f.c)("field")],G.prototype,"writeField",null),(0,i._)([(0,c.Cb)({type:String,value:null,json:{write:!0}})],G.prototype,"field2",null),(0,i._)([(0,c.Cb)({type:String,value:null,json:{write:!0}})],G.prototype,"field3",null),(0,i._)([(0,c.Cb)({type:Boolean,json:{name:"drawInClassOrder",default:!1,write:!0,origins:{"web-scene":{write:!1}}}})],G.prototype,"orderByClassesEnabled",void 0),(0,i._)([(0,c.Cb)({type:String,value:null,json:{write:!0}})],G.prototype,"valueExpression",null),(0,i._)([(0,c.Cb)({type:String,json:{write:!0}})],G.prototype,"valueExpressionTitle",void 0),(0,i._)([(0,c.Cb)({type:x.I,json:{write:!0}})],G.prototype,"legendOptions",void 0),(0,i._)([(0,c.Cb)({type:String,json:{write:!0}})],G.prototype,"defaultLabel",void 0),(0,i._)([(0,c.Cb)((0,u.RH)({...S.Gn},{json:{write:{overridePolicy(){return{enabled:!this._isDefaultSymbolDerived}}},origins:{"web-scene":{write:{overridePolicy(){return{enabled:!this._isDefaultSymbolDerived}}}}}}}))],G.prototype,"defaultSymbol",null),(0,i._)([(0,c.Cb)({type:String,value:null,json:{write:!0}})],G.prototype,"fieldDelimiter",null),(0,i._)([(0,c.Cb)({type:w.Z,readOnly:!0})],G.prototype,"portal",void 0),(0,i._)([(0,y.r)("portal",["styleName"])],G.prototype,"readPortal",null),(0,i._)([(0,c.Cb)({readOnly:!0,json:{write:{enabled:!1,overridePolicy:()=>({enabled:!0})}}})],G.prototype,"styleOrigin",void 0),(0,i._)([(0,y.r)("styleOrigin",["styleName","styleUrl"])],G.prototype,"readStyleOrigin",null),(0,i._)([(0,f.c)("styleOrigin",{styleName:{type:String},styleUrl:{type:String}})],G.prototype,"writeStyleOrigin",null),(0,i._)([(0,c.Cb)({type:[T],json:{read:{source:["uniqueValueGroups","uniqueValueInfos"],reader:(e,t,s)=>(t.uniqueValueGroups||function(e){const{field1:t,field2:s,field3:i,fieldDelimiter:r,uniqueValueInfos:l,valueExpression:o}=e,n=!(!t||!s);return[{classes:(l??[]).map((e=>{const{symbol:l,label:a,value:u,description:p}=e,[c,d,h]=n?u?.toString()?.split(r||"")||[]:[u],y=[];return(t||o)&&y.push(c),s&&y.push(d),i&&y.push(h),{symbol:l,label:a,values:[y],description:p}}))}]}(t)).map((e=>T.fromJSON(e,s)))},write:{overridePolicy(){return this.styleOrigin?{enabled:!1}:{enabled:!0}}}}})],G.prototype,"uniqueValueGroups",null),(0,i._)([(0,c.Cb)({type:[O.Z],json:{read:!1,write:{overridePolicy(){return this.styleOrigin?{enabled:!1}:{enabled:!0}}}}})],G.prototype,"uniqueValueInfos",null),G=k=(0,i._)([(0,m.j)(L)],G);const $=G},41733:(e,t,s)=>{s.d(t,{W:()=>U});var i=s(43697),r=s(70586),l=s(5600),o=s(75215),n=(s(67676),s(71715)),a=s(52011),u=s(30556),p=s(35671);const c=/^hash\(\$feature\['((\\'|[^'])+)'\]\) \* 8\.381e-8$/;var d,h=s(77997),y=s(21849);let m=d=class extends h.Z{constructor(e){super(e),this.type="color",this.normalizationField=null}get cache(){return{ipData:this._interpolateData(),hasExpression:!!this.valueExpression,compiledFunc:null}}set stops(e){e&&Array.isArray(e)&&(e=e.filter((e=>!!e))).sort(((e,t)=>e.value-t.value)),this._set("stops",e)}clone(){return new d({field:this.field,normalizationField:this.normalizationField,valueExpression:this.valueExpression,valueExpressionTitle:this.valueExpressionTitle,stops:this.stops&&this.stops.map((e=>e.clone())),legendOptions:this.legendOptions&&this.legendOptions.clone()})}getAttributeHash(){return`${super.getAttributeHash()}-${this.normalizationField}`}_interpolateData(){return this.stops&&this.stops.map((e=>e.value||0))}};(0,i._)([(0,l.Cb)({readOnly:!0})],m.prototype,"cache",null),(0,i._)([(0,l.Cb)({type:["color"],json:{type:["colorInfo"]}})],m.prototype,"type",void 0),(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],m.prototype,"normalizationField",void 0),(0,i._)([(0,l.Cb)({type:[y.Z],json:{write:!0}})],m.prototype,"stops",null),m=d=(0,i._)([(0,a.j)("esri.renderers.visualVariables.ColorVariable")],m);const f=m;var b,v=s(96674),g=s(65242);let w=b=class extends v.wq{constructor(e){super(e),this.label=null,this.opacity=null,this.value=null}readOpacity(e,t){return(0,g.b)(t.transparency)}writeOpacity(e,t,s){t[s]=(0,g.a)(e)}clone(){return new b({label:this.label,opacity:this.opacity,value:this.value})}};(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],w.prototype,"label",void 0),(0,i._)([(0,l.Cb)({type:Number,json:{type:o.z8,write:{target:"transparency"}}})],w.prototype,"opacity",void 0),(0,i._)([(0,n.r)("opacity",["transparency"])],w.prototype,"readOpacity",null),(0,i._)([(0,u.c)("opacity")],w.prototype,"writeOpacity",null),(0,i._)([(0,l.Cb)({type:Number,json:{write:!0}})],w.prototype,"value",void 0),w=b=(0,i._)([(0,a.j)("esri.renderers.visualVariables.support.OpacityStop")],w);const _=w;var V;let S=V=class extends h.Z{constructor(e){super(e),this.type="opacity",this.normalizationField=null}get cache(){return{ipData:this._interpolateData(),hasExpression:!!this.valueExpression,compiledFunc:null}}set stops(e){e&&Array.isArray(e)&&(e=e.filter((e=>!!e))).sort(((e,t)=>e.value-t.value)),this._set("stops",e)}clone(){return new V({field:this.field,normalizationField:this.normalizationField,valueExpression:this.valueExpression,valueExpressionTitle:this.valueExpressionTitle,stops:this.stops&&this.stops.map((e=>e.clone())),legendOptions:this.legendOptions&&this.legendOptions.clone()})}getAttributeHash(){return`${super.getAttributeHash()}-${this.normalizationField}`}_interpolateData(){return this.stops&&this.stops.map((e=>e.value||0))}};(0,i._)([(0,l.Cb)({readOnly:!0})],S.prototype,"cache",null),(0,i._)([(0,l.Cb)({type:["opacity"],json:{type:["transparencyInfo"]}})],S.prototype,"type",void 0),(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],S.prototype,"normalizationField",void 0),(0,i._)([(0,l.Cb)({type:[_],json:{write:!0}})],S.prototype,"stops",null),S=V=(0,i._)([(0,a.j)("esri.renderers.visualVariables.OpacityVariable")],S);const x=S;var C=s(89513),I=s(23847),j=s(15923),z=s(35454),q=s(92604),F=s(20941);const E={color:f,size:I.Z,opacity:x,rotation:C.Z},T=new z.X({colorInfo:"color",transparencyInfo:"opacity",rotationInfo:"rotation",sizeInfo:"size"}),k=/^\[([^\]]+)\]$/i;let O=class extends j.Z{constructor(){super(...arguments),this.colorVariables=null,this.opacityVariables=null,this.rotationVariables=null,this.sizeVariables=null}set visualVariables(e){if(this._resetVariables(),(e=e&&e.filter((e=>!!e)))&&e.length){for(const t of e)switch(t.type){case"color":this.colorVariables.push(t);break;case"opacity":this.opacityVariables.push(t);break;case"rotation":this.rotationVariables.push(t);break;case"size":this.sizeVariables.push(t)}this.sizeVariables.length&&this.sizeVariables.some((e=>!!e.target))&&e.sort(((e,t)=>{let s=null;return s=e.target===t.target?0:e.target?1:-1,s}));for(let t=0;t<e.length;t++)e[t].index=t;this._set("visualVariables",e)}else this._set("visualVariables",e)}readVariables(e,t,s){const{rotationExpression:i,rotationType:r}=t,l=i&&i.match(k),o=l&&l[1];if(o&&(e||(e=[]),e.push({type:"rotationInfo",rotationType:r,field:o})),e)return e.map((e=>{const t=T.read(e.type),i=E[t];i||(q.Z.getLogger(this.declaredClass).warn(`Unknown variable type: ${t}`),s&&s.messages&&s.messages.push(new F.Z("visual-variable:unsupported",`visualVariable of type '${t}' is not supported`,{definition:e,context:s})));const r=new i;return r.read(e,s),r}))}writeVariables(e,t){const s=[];for(const i of e){const e=i.toJSON(t);e&&s.push(e)}return s}_resetVariables(){this.colorVariables=[],this.opacityVariables=[],this.rotationVariables=[],this.sizeVariables=[]}};(0,i._)([(0,l.Cb)()],O.prototype,"visualVariables",null),O=(0,i._)([(0,a.j)("esri.renderers.visualVariables.VisualVariableFactory")],O);const Z=O,D={base:h.Z,key:"type",typeMap:{opacity:x,color:f,rotation:C.Z,size:I.Z}},U=e=>{let t=class extends e{constructor(){super(...arguments),this._vvFactory=new Z}set visualVariables(e){this._vvFactory.visualVariables=e,this._set("visualVariables",this._vvFactory.visualVariables)}readVisualVariables(e,t,s){return this._vvFactory.readVariables(e,t,s)}writeVisualVariables(e,t,s,i){t[s]=this._vvFactory.writeVariables(e,i)}get arcadeRequiredForVisualVariables(){if(!this.visualVariables)return!1;for(const e of this.visualVariables)if(e.arcadeRequired)return!0;return!1}hasVisualVariables(e,t){return e?this.getVisualVariablesForType(e,t).length>0:this.getVisualVariablesForType("size",t).length>0||this.getVisualVariablesForType("color",t).length>0||this.getVisualVariablesForType("opacity",t).length>0||this.getVisualVariablesForType("rotation",t).length>0}getVisualVariablesForType(e,t){const s=this.visualVariables;return s?s.filter((s=>s.type===e&&("string"==typeof t?s.target===t:!1!==t||!s.target))):[]}async collectVVRequiredFields(e,t){let s=[];this.visualVariables&&(s=s.concat(this.visualVariables));for(const i of s)i&&(i.field&&(0,p.AB)(e,t,i.field),i.normalizationField&&(0,p.AB)(e,t,i.normalizationField),i.valueExpression&&(B(i.valueExpression,e,t)||await(0,p.io)(e,t,i.valueExpression)))}};return(0,i._)([(0,l.Cb)({types:[D],value:null,json:{write:!0}})],t.prototype,"visualVariables",null),(0,i._)([(0,n.r)("visualVariables",["visualVariables","rotationType","rotationExpression"])],t.prototype,"readVisualVariables",null),(0,i._)([(0,u.c)("visualVariables")],t.prototype,"writeVisualVariables",null),t=(0,i._)([(0,a.j)("esri.renderers.mixins.VisualVariablesMixin")],t),t};function B(e,t,s){const i=function(e){return e.match(c)?.[1]?.replace(/\\'/g,"'")??null}(e);return!!(0,r.pC)(i)&&((0,p.AB)(t,s,i),!0)}},69237:(e,t,s)=>{s.d(t,{Z:()=>T});var i,r=s(43697),l=s(35454),o=s(96674),n=s(22974),a=s(5600),u=s(75215),p=s(71715),c=s(52011);s(67676);let d=i=class extends o.wq{constructor(e){super(e),this.minValue=0,this.maxValue=0}clone(){return new i({minValue:this.minValue,maxValue:this.maxValue})}};var h;(0,r._)([(0,a.Cb)({type:Number,json:{write:!0}})],d.prototype,"minValue",void 0),(0,r._)([(0,a.Cb)({type:Number,json:{write:!0}})],d.prototype,"maxValue",void 0),d=i=(0,r._)([(0,c.j)("esri.renderer.support.AuthoringInfoClassBreakInfo")],d);let y=h=class extends o.wq{constructor(e){super(e),this.field="",this.normalizationField="",this.label="",this.classBreakInfos=[]}clone(){return new h({field:this.field,normalizationField:this.normalizationField,label:this.label,classBreakInfos:(0,n.d9)(this.classBreakInfos)})}};(0,r._)([(0,a.Cb)({type:String,json:{write:!0}})],y.prototype,"field",void 0),(0,r._)([(0,a.Cb)({type:String,json:{write:!0}})],y.prototype,"normalizationField",void 0),(0,r._)([(0,a.Cb)({type:String,json:{write:!0}})],y.prototype,"label",void 0),(0,r._)([(0,a.Cb)({type:[d],json:{write:!0}})],y.prototype,"classBreakInfos",void 0),y=h=(0,r._)([(0,c.j)("esri.renderers.support.AuthoringInfoFieldInfo")],y);var m,f=s(90578);const b=new l.X({percentTotal:"percent-of-total",ratio:"ratio",percent:"percent"}),v=new l.X({sizeInfo:"size",colorInfo:"color",transparencyInfo:"opacity",rotationInfo:"rotation"}),g={key:e=>"number"==typeof e?"number":"string",typeMap:{number:Number,string:String},base:null},w=["high-to-low","above-and-below","centered-on","extremes"],_=[...new Set(["high-to-low","above-and-below","centered-on","extremes","90-10","above","below","high-to-low","above-and-below","90-10","above","below"])],V=["seconds","minutes","hours","days","months","years"];let S=m=class extends o.wq{constructor(e){super(e),this.endTime=null,this.field=null,this.maxSliderValue=null,this.minSliderValue=null,this.startTime=null,this.type=null,this.units=null}castEndTime(e){return"string"==typeof e||"number"==typeof e?e:null}castStartTime(e){return"string"==typeof e||"number"==typeof e?e:null}get style(){return"color"===this.type?this._get("style"):null}set style(e){this._set("style",e)}get theme(){return"color"===this.type||"size"===this.type?this._get("theme")||"high-to-low":null}set theme(e){this._set("theme",e)}clone(){return new m({endTime:this.endTime,field:this.field,maxSliderValue:this.maxSliderValue,minSliderValue:this.minSliderValue,startTime:this.startTime,style:this.style,theme:this.theme,type:this.type,units:this.units})}};(0,r._)([(0,a.Cb)({types:g,json:{write:!0}})],S.prototype,"endTime",void 0),(0,r._)([(0,f.p)("endTime")],S.prototype,"castEndTime",null),(0,r._)([(0,a.Cb)({type:String,json:{write:!0}})],S.prototype,"field",void 0),(0,r._)([(0,a.Cb)({type:Number,json:{write:!0}})],S.prototype,"maxSliderValue",void 0),(0,r._)([(0,a.Cb)({type:Number,json:{write:!0}})],S.prototype,"minSliderValue",void 0),(0,r._)([(0,a.Cb)({types:g,json:{write:!0}})],S.prototype,"startTime",void 0),(0,r._)([(0,f.p)("startTime")],S.prototype,"castStartTime",null),(0,r._)([(0,a.Cb)({type:b.apiValues,value:null,json:{type:b.jsonValues,read:b.read,write:b.write}})],S.prototype,"style",null),(0,r._)([(0,a.Cb)({type:_,value:null,json:{type:_,origins:{"web-scene":{type:w,write:{writer:(e,t)=>{w.includes(e)&&(t.theme=e)}}}},write:!0}})],S.prototype,"theme",null),(0,r._)([(0,a.Cb)({type:v.apiValues,json:{type:v.jsonValues,read:v.read,write:v.write}})],S.prototype,"type",void 0),(0,r._)([(0,a.Cb)({type:V,json:{type:V,write:!0}})],S.prototype,"units",void 0),S=m=(0,r._)([(0,c.j)("esri.renderers.support.AuthoringInfoVisualVariable")],S);const x=S;var C,I=s(94593);const j=new l.X({esriClassifyDefinedInterval:"defined-interval",esriClassifyEqualInterval:"equal-interval",esriClassifyManual:"manual",esriClassifyNaturalBreaks:"natural-breaks",esriClassifyQuantile:"quantile",esriClassifyStandardDeviation:"standard-deviation"}),z=new l.X({pieChart:"pie-chart",classedSize:"class-breaks-size",classedColor:"class-breaks-color",univariateColorSize:"univariate-color-size",relationship:"relationship",predominance:"predominance",dotDensity:"dot-density",flow:"flow"}),q=new l.X({classedSize:"class-breaks-size",classedColor:"class-breaks-color",univariateColorSize:"univariate-color-size",relationship:"relationship",predominance:"predominance",dotDensity:"dot-density"}),F=["inches","feet","yards","miles","nautical-miles","millimeters","centimeters","decimeters","meters","kilometers","decimal-degrees"];let E=C=class extends o.wq{constructor(e){super(e),this.colorRamp=null,this.fadeRatio=null,this.isAutoGenerated=!1,this.lengthUnit=null,this.maxSliderValue=null,this.minSliderValue=null,this.visualVariables=null}get classificationMethod(){const e=this._get("classificationMethod"),t=this.type;return t&&"relationship"!==t?"class-breaks-size"===t||"class-breaks-color"===t?e||"manual":null:e}set classificationMethod(e){this._set("classificationMethod",e)}readColorRamp(e){return e?(0,I.i)(e):void 0}get fields(){return this.type&&"predominance"!==this.type?null:this._get("fields")}set fields(e){this._set("fields",e)}get field1(){return this.type&&"relationship"!==this.type?null:this._get("field1")}set field1(e){this._set("field1",e)}get field2(){return this.type&&"relationship"!==this.type?null:this._get("field2")}set field2(e){this._set("field2",e)}get flowTheme(){return"flow"===this.type?this._get("flowTheme"):null}set flowTheme(e){this._set("flowTheme",e)}get focus(){return this.type&&"relationship"!==this.type?null:this._get("focus")}set focus(e){this._set("focus",e)}get numClasses(){return this.type&&"relationship"!==this.type?null:this._get("numClasses")}set numClasses(e){this._set("numClasses",e)}get statistics(){return"univariate-color-size"===this.type&&"above-and-below"===this.univariateTheme?this._get("statistics"):null}set statistics(e){this._set("statistics",e)}get standardDeviationInterval(){const e=this.type;return e&&"relationship"!==e&&"class-breaks-size"!==e&&"class-breaks-color"!==e||this.classificationMethod&&"standard-deviation"!==this.classificationMethod?null:this._get("standardDeviationInterval")}set standardDeviationInterval(e){this._set("standardDeviationInterval",e)}get type(){return this._get("type")}set type(e){let t=e;"classed-size"===e?t="class-breaks-size":"classed-color"===e&&(t="class-breaks-color"),this._set("type",t)}get univariateSymbolStyle(){return"univariate-color-size"===this.type&&"above-and-below"===this.univariateTheme?this._get("univariateSymbolStyle"):null}set univariateSymbolStyle(e){this._set("univariateSymbolStyle",e)}get univariateTheme(){return"univariate-color-size"===this.type?this._get("univariateTheme"):null}set univariateTheme(e){this._set("univariateTheme",e)}clone(){return new C({classificationMethod:this.classificationMethod,colorRamp:(0,n.d9)(this.colorRamp),fadeRatio:(0,n.d9)(this.fadeRatio),fields:this.fields&&this.fields.slice(0),field1:(0,n.d9)(this.field1),field2:(0,n.d9)(this.field2),isAutoGenerated:this.isAutoGenerated,focus:this.focus,numClasses:this.numClasses,maxSliderValue:this.maxSliderValue,minSliderValue:this.minSliderValue,lengthUnit:this.lengthUnit,statistics:this.statistics,standardDeviationInterval:this.standardDeviationInterval,type:this.type,visualVariables:this.visualVariables&&this.visualVariables.map((e=>e.clone())),univariateSymbolStyle:this.univariateSymbolStyle,univariateTheme:this.univariateTheme,flowTheme:this.flowTheme})}};(0,r._)([(0,a.Cb)({type:j.apiValues,value:null,json:{type:j.jsonValues,read:j.read,write:j.write,origins:{"web-document":{default:"manual",type:j.jsonValues,read:j.read,write:j.write}}}})],E.prototype,"classificationMethod",null),(0,r._)([(0,a.Cb)({types:I.V,json:{write:!0}})],E.prototype,"colorRamp",void 0),(0,r._)([(0,p.r)("colorRamp")],E.prototype,"readColorRamp",null),(0,r._)([(0,a.Cb)({json:{write:!0,origins:{"web-scene":{write:!1,read:!1}}}})],E.prototype,"fadeRatio",void 0),(0,r._)([(0,a.Cb)({type:[String],value:null,json:{write:!0}})],E.prototype,"fields",null),(0,r._)([(0,a.Cb)({type:y,value:null,json:{write:!0}})],E.prototype,"field1",null),(0,r._)([(0,a.Cb)({type:y,value:null,json:{write:!0}})],E.prototype,"field2",null),(0,r._)([(0,a.Cb)({type:["flow-line","wave-front"],value:null,json:{write:!0,origins:{"web-scene":{write:!1}}}})],E.prototype,"flowTheme",null),(0,r._)([(0,a.Cb)({type:["HH","HL","LH","LL"],value:null,json:{write:!0}})],E.prototype,"focus",null),(0,r._)([(0,a.Cb)({type:Boolean,json:{write:!0,default:!1,origins:{"web-scene":{write:!1}}}})],E.prototype,"isAutoGenerated",void 0),(0,r._)([(0,a.Cb)({type:Number,value:null,json:{type:u.z8,write:!0}})],E.prototype,"numClasses",null),(0,r._)([(0,a.Cb)({type:F,json:{type:F,read:!1,write:!1,origins:{"web-scene":{read:!0,write:!0}}}})],E.prototype,"lengthUnit",void 0),(0,r._)([(0,a.Cb)({type:Number,json:{write:!0,origins:{"web-scene":{write:!1,read:!1}}}})],E.prototype,"maxSliderValue",void 0),(0,r._)([(0,a.Cb)({type:Number,json:{write:!0,origins:{"web-scene":{write:!1,read:!1}}}})],E.prototype,"minSliderValue",void 0),(0,r._)([(0,a.Cb)({type:Object,value:null,json:{write:!0,origins:{"web-scene":{write:!1,read:!1}}}})],E.prototype,"statistics",null),(0,r._)([(0,a.Cb)({type:[.25,.33,.5,1],value:null,json:{type:[.25,.33,.5,1],write:!0}})],E.prototype,"standardDeviationInterval",null),(0,r._)([(0,a.Cb)({type:z.apiValues,value:null,json:{type:z.jsonValues,read:z.read,write:z.write,origins:{"web-scene":{type:q.jsonValues,write:{writer:q.write,overridePolicy:e=>({enabled:"flow"!==e})}}}}})],E.prototype,"type",null),(0,r._)([(0,a.Cb)({type:[x],json:{write:!0}})],E.prototype,"visualVariables",void 0),(0,r._)([(0,a.Cb)({type:["caret","circle-caret","arrow","circle-arrow","plus-minus","circle-plus-minus","square","circle","triangle","happy-sad","thumb","custom"],value:null,json:{write:!0,origins:{"web-scene":{write:!1}}}})],E.prototype,"univariateSymbolStyle",null),(0,r._)([(0,a.Cb)({type:["high-to-low","above-and-below","above","below","90-10"],value:null,json:{write:!0,origins:{"web-scene":{write:!1}}}})],E.prototype,"univariateTheme",null),E=C=(0,r._)([(0,c.j)("esri.renderers.support.AuthoringInfo")],E);const T=E},32984:(e,t,s)=>{s.d(t,{Z:()=>p});var i,r=s(43697),l=s(96674),o=s(5600),n=(s(75215),s(67676),s(52011)),a=s(9833);let u=i=class extends l.wq{constructor(e){super(e),this.description=null,this.label=null,this.minValue=null,this.maxValue=0,this.symbol=null}clone(){return new i({description:this.description,label:this.label,minValue:this.minValue,maxValue:this.maxValue,symbol:this.symbol?this.symbol.clone():null})}getMeshHash(){const e=JSON.stringify(this.symbol);return`${this.minValue}.${this.maxValue}.${e}`}};(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],u.prototype,"description",void 0),(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],u.prototype,"label",void 0),(0,r._)([(0,o.Cb)({type:Number,json:{read:{source:"classMinValue"},write:{target:"classMinValue"}}})],u.prototype,"minValue",void 0),(0,r._)([(0,o.Cb)({type:Number,json:{read:{source:"classMaxValue"},write:{target:"classMaxValue"}}})],u.prototype,"maxValue",void 0),(0,r._)([(0,o.Cb)(a.Gn)],u.prototype,"symbol",void 0),u=i=(0,r._)([(0,n.j)("esri.renderers.support.ClassBreakInfo")],u);const p=u},66338:(e,t,s)=>{s.d(t,{I:()=>a});var i,r=s(43697),l=s(96674),o=s(5600),n=(s(75215),s(67676),s(52011));let a=i=class extends l.wq{constructor(){super(...arguments),this.title=null}clone(){return new i({title:this.title})}};(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],a.prototype,"title",void 0),a=i=(0,r._)([(0,n.j)("esri.renderers.support.LegendOptions")],a)},44262:(e,t,s)=>{s.d(t,{Z:()=>p});var i,r=s(43697),l=s(96674),o=s(5600),n=(s(75215),s(67676),s(52011)),a=s(9833);let u=i=class extends l.wq{constructor(e){super(e),this.description=null,this.label=null,this.symbol=null,this.value=null}clone(){return new i({value:this.value,description:this.description,label:this.label,symbol:this.symbol?this.symbol.clone():null})}getMeshHash(){const e=JSON.stringify(this.symbol&&this.symbol.toJSON());return`${this.value}.${e}`}};(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],u.prototype,"description",void 0),(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],u.prototype,"label",void 0),(0,r._)([(0,o.Cb)(a.Gn)],u.prototype,"symbol",void 0),(0,r._)([(0,o.Cb)(a.GY)],u.prototype,"value",void 0),u=i=(0,r._)([(0,n.j)("esri.renderers.support.UniqueValueInfo")],u);const p=u},9833:(e,t,s)=>{s.d(t,{GY:()=>p,Gn:()=>a,KK:()=>u});var i=s(9790),r=s(63213),l=s(21878),o=s(89164),n=s(86114);const a={types:i.QT,json:{write:{writer:l.cW},origins:{"web-scene":{types:i.f_,write:{writer:l.cW},read:{reader:(0,r.d)({types:i.f_})}}}}},u={types:{base:o.Z,key:"type",typeMap:{"simple-fill":i.LB.typeMap["simple-fill"],"picture-fill":i.LB.typeMap["picture-fill"],"polygon-3d":i.LB.typeMap["polygon-3d"]}},json:{write:{writer:l.cW},origins:{"web-scene":{type:n.Z,write:{writer:l.cW}}}}},p={cast:e=>null==e||"string"==typeof e||"number"==typeof e?e:`${e}`,json:{type:String,write:{writer:(e,t)=>{t.value=e?.toString()}}}}},89513:(e,t,s)=>{s.d(t,{Z:()=>c});var i,r=s(43697),l=s(20102),o=s(5600),n=(s(75215),s(67676),s(52011)),a=s(30556),u=s(77997);let p=i=class extends u.Z{constructor(e){super(e),this.axis=null,this.type="rotation",this.rotationType="geographic",this.valueExpressionTitle=null}get cache(){return{hasExpression:!!this.valueExpression,compiledFunc:null}}writeValueExpressionTitleWebScene(e,t,s,i){if(i&&i.messages){const e=`visualVariables[${this.index}]`;i.messages.push(new l.Z("property:unsupported",this.type+"VisualVariable.valueExpressionTitle is not supported in Web Scene. Please remove this property to save the Web Scene.",{instance:this,propertyName:e+".valueExpressionTitle",context:i}))}}clone(){return new i({axis:this.axis,rotationType:this.rotationType,field:this.field,valueExpression:this.valueExpression,valueExpressionTitle:this.valueExpressionTitle,legendOptions:this.legendOptions&&this.legendOptions.clone()})}};(0,r._)([(0,o.Cb)({readOnly:!0})],p.prototype,"cache",null),(0,r._)([(0,o.Cb)({type:["heading","tilt","roll"],json:{origins:{"web-scene":{default:"heading",write:!0}}}})],p.prototype,"axis",void 0),(0,r._)([(0,o.Cb)({type:["rotation"],json:{type:["rotationInfo"]}})],p.prototype,"type",void 0),(0,r._)([(0,o.Cb)({type:["geographic","arithmetic"],json:{write:!0,origins:{"web-document":{write:!0,default:"geographic"}}}})],p.prototype,"rotationType",void 0),(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],p.prototype,"valueExpressionTitle",void 0),(0,r._)([(0,a.c)("web-scene","valueExpressionTitle")],p.prototype,"writeValueExpressionTitleWebScene",null),p=i=(0,r._)([(0,n.j)("esri.renderers.visualVariables.RotationVariable")],p);const c=p},23847:(e,t,s)=>{s.d(t,{Z:()=>j});var i,r=s(43697),l=s(20102),o=s(35454),n=s(92604),a=s(62357),u=s(5600),p=s(90578),c=s(71715),d=s(52011),h=s(30556),y=s(77997),m=s(30856),f=(s(75215),s(67676),s(58439));let b=i=class extends f.Z{constructor(){super(...arguments),this.customValues=null}clone(){return new i({title:this.title,showLegend:this.showLegend,customValues:this.customValues&&this.customValues.slice(0)})}};(0,r._)([(0,u.Cb)({type:[Number],json:{write:!0}})],b.prototype,"customValues",void 0),b=i=(0,r._)([(0,d.j)("esri.renderers.visualVariables.support.SizeVariableLegendOptions")],b);const v=b;var g,w=s(51706),_=s(28101);const V=new o.X({width:"width",depth:"depth",height:"height",widthAndDepth:"width-and-depth",all:"all"}),S=new o.X({unknown:"unknown",inch:"inches",foot:"feet",yard:"yards",mile:"miles","nautical-mile":"nautical-miles",millimeter:"millimeters",centimeter:"centimeters",decimeter:"decimeters",meter:"meters",kilometer:"kilometers","decimal-degree":"decimal-degrees"});function x(e){if(null!=e)return"string"==typeof e||"number"==typeof e?(0,a.t_)(e):"size"===e.type?(0,w.iY)(e)?e:(delete(e={...e}).type,new I(e)):void 0}function C(e,t,s){if("object"!=typeof e)return e;const i=new I;return i.read(e,s),i}let I=g=class extends y.Z{constructor(e){super(e),this.axis=null,this.legendOptions=null,this.normalizationField=null,this.scaleBy=null,this.target=null,this.type="size",this.useSymbolValue=null,this.valueExpression=null,this.valueRepresentation=null,this.valueUnit=null}get cache(){return{ipData:this._interpolateData(),hasExpression:!!this.valueExpression,compiledFunc:null,isScaleDriven:null!=this.valueExpression&&_.B3.test(this.valueExpression)}}set expression(e){n.Z.getLogger(this.declaredClass).warn("'expression' is deprecated since version 4.2. Use 'valueExpression' instead. The only supported expression is 'view.scale'."),"view.scale"===e?(this.valueExpression="$view.scale",this._set("expression",e)):this._set("expression",null)}set index(e){(0,w.iY)(this.maxSize)&&(this.maxSize.index=`visualVariables[${e}].maxSize`),(0,w.iY)(this.minSize)&&(this.minSize.index=`visualVariables[${e}].minSize`),this._set("index",e)}get inputValueType(){return(0,w.PS)(this)}set maxDataValue(e){e&&this.stops&&(n.Z.getLogger(this.declaredClass).warn("cannot set maxDataValue when stops is not null."),e=null),this._set("maxDataValue",e)}set maxSize(e){e&&this.stops&&(n.Z.getLogger(this.declaredClass).warn("cannot set maxSize when stops is not null."),e=null),this._set("maxSize",e)}castMaxSize(e){return x(e)}readMaxSize(e,t,s){return C(e,0,s)}set minDataValue(e){e&&this.stops&&(n.Z.getLogger(this.declaredClass).warn("cannot set minDataValue when stops is not null."),e=null),this._set("minDataValue",e)}set minSize(e){e&&this.stops&&(n.Z.getLogger(this.declaredClass).warn("cannot set minSize when stops is not null."),e=null),this._set("minSize",e)}castMinSize(e){return x(e)}readMinSize(e,t,s){return C(e,0,s)}get arcadeRequired(){return!!this.valueExpression||null!=this.minSize&&"object"==typeof this.minSize&&this.minSize.arcadeRequired||null!=this.maxSize&&"object"==typeof this.maxSize&&this.maxSize.arcadeRequired}set stops(e){null==this.minDataValue&&null==this.maxDataValue&&null==this.minSize&&null==this.maxSize?e&&Array.isArray(e)&&(e=e.filter((e=>!!e))).sort(((e,t)=>e.value-t.value)):e&&(n.Z.getLogger(this.declaredClass).warn("cannot set stops when one of minDataValue, maxDataValue, minSize or maxSize is not null."),e=null),this._set("stops",e)}get transformationType(){return(0,w.QW)(this,this.inputValueType)}readValueExpression(e,t){return e||t.expression&&"$view.scale"}writeValueExpressionWebScene(e,t,s,i){if("$view.scale"===e){if(i&&i.messages){const e=this.index,t="string"==typeof e?e:`visualVariables[${e}]`;i.messages.push(new l.Z("property:unsupported",this.type+"VisualVariable.valueExpression = '$view.scale' is not supported in Web Scene. Please remove this property to save the Web Scene.",{instance:this,propertyName:t+".valueExpression",context:i}))}}else t[s]=e}readValueUnit(e){return e?S.read(e):null}clone(){return new g({axis:this.axis,field:this.field,valueExpression:this.valueExpression,valueExpressionTitle:this.valueExpressionTitle,maxDataValue:this.maxDataValue,maxSize:(0,w.iY)(this.maxSize)?this.maxSize.clone():this.maxSize,minDataValue:this.minDataValue,minSize:(0,w.iY)(this.minSize)?this.minSize.clone():this.minSize,normalizationField:this.normalizationField,stops:this.stops&&this.stops.map((e=>e.clone())),target:this.target,useSymbolValue:this.useSymbolValue,valueRepresentation:this.valueRepresentation,valueUnit:this.valueUnit,legendOptions:this.legendOptions&&this.legendOptions.clone()})}flipSizes(){if(this.transformationType===w.hL.ClampedLinear){const{minSize:e,maxSize:t}=this;return this.minSize=t,this.maxSize=e,this}if(this.transformationType===w.hL.Stops){const e=this.stops;if(!e)return this;const t=e.map((e=>e.size)).reverse(),s=e.length;for(let i=0;i<s;i++)e[i].size=t[i];return this}return this}getAttributeHash(){return`${super.getAttributeHash()}-${this.target}-${this.normalizationField}`}_interpolateData(){return this.stops&&this.stops.map((e=>e.value||0))}};(0,r._)([(0,u.Cb)({readOnly:!0})],I.prototype,"cache",null),(0,r._)([(0,u.Cb)({type:V.apiValues,json:{type:V.jsonValues,origins:{"web-map":{read:!1}},read:V.read,write:V.write}})],I.prototype,"axis",void 0),(0,r._)([(0,u.Cb)({type:String,value:null,json:{read:!1}})],I.prototype,"expression",null),(0,r._)([(0,u.Cb)()],I.prototype,"index",null),(0,r._)([(0,u.Cb)({type:String,readOnly:!0})],I.prototype,"inputValueType",null),(0,r._)([(0,u.Cb)({type:v,json:{write:!0}})],I.prototype,"legendOptions",void 0),(0,r._)([(0,u.Cb)({type:Number,value:null,json:{write:!0}})],I.prototype,"maxDataValue",null),(0,r._)([(0,u.Cb)({type:Number,value:null,json:{write:!0}})],I.prototype,"maxSize",null),(0,r._)([(0,p.p)("maxSize")],I.prototype,"castMaxSize",null),(0,r._)([(0,c.r)("maxSize")],I.prototype,"readMaxSize",null),(0,r._)([(0,u.Cb)({type:Number,value:null,json:{write:!0}})],I.prototype,"minDataValue",null),(0,r._)([(0,u.Cb)({type:Number,value:null,json:{write:!0}})],I.prototype,"minSize",null),(0,r._)([(0,p.p)("minSize")],I.prototype,"castMinSize",null),(0,r._)([(0,c.r)("minSize")],I.prototype,"readMinSize",null),(0,r._)([(0,u.Cb)({type:String,json:{write:!0}})],I.prototype,"normalizationField",void 0),(0,r._)([(0,u.Cb)({readOnly:!0})],I.prototype,"arcadeRequired",null),(0,r._)([(0,u.Cb)({type:String})],I.prototype,"scaleBy",void 0),(0,r._)([(0,u.Cb)({type:[m.Z],value:null,json:{write:!0}})],I.prototype,"stops",null),(0,r._)([(0,u.Cb)({type:["outline"],json:{write:!0}})],I.prototype,"target",void 0),(0,r._)([(0,u.Cb)({type:String,readOnly:!0})],I.prototype,"transformationType",null),(0,r._)([(0,u.Cb)({type:["size"],json:{type:["sizeInfo"]}})],I.prototype,"type",void 0),(0,r._)([(0,u.Cb)({type:Boolean,json:{write:!0,origins:{"web-map":{read:!1}}}})],I.prototype,"useSymbolValue",void 0),(0,r._)([(0,u.Cb)({type:String,json:{write:!0}})],I.prototype,"valueExpression",void 0),(0,r._)([(0,c.r)("valueExpression",["valueExpression","expression"])],I.prototype,"readValueExpression",null),(0,r._)([(0,h.c)("web-scene","valueExpression")],I.prototype,"writeValueExpressionWebScene",null),(0,r._)([(0,u.Cb)({type:["radius","diameter","area","width","distance"],json:{write:!0}})],I.prototype,"valueRepresentation",void 0),(0,r._)([(0,u.Cb)({type:S.apiValues,json:{write:S.write,origins:{"web-map":{read:!1},"web-scene":{write:!0},"portal-item":{write:!0}}}})],I.prototype,"valueUnit",void 0),(0,r._)([(0,c.r)("valueUnit")],I.prototype,"readValueUnit",null),I=g=(0,r._)([(0,d.j)("esri.renderers.visualVariables.SizeVariable")],I);const j=I},77997:(e,t,s)=>{s.d(t,{Z:()=>y});var i=s(43697),r=s(35454),l=s(96674),o=s(92604),n=s(5600),a=s(90578),u=s(52011),p=s(75215),c=s(58439);const d=new r.X({colorInfo:"color",transparencyInfo:"opacity",rotationInfo:"rotation",sizeInfo:"size"});let h=class extends l.wq{constructor(e){super(e),this.index=null,this.type=null,this.field=null,this.valueExpression=null,this.valueExpressionTitle=null,this.legendOptions=null}castField(e){return null==e?e:"function"==typeof e?(o.Z.getLogger(this.declaredClass).error(".field: field must be a string value"),null):(0,p.Zs)(e)}get arcadeRequired(){return!!this.valueExpression}clone(){}getAttributeHash(){return`${this.type}-${this.field}-${this.valueExpression}`}};(0,i._)([(0,n.Cb)()],h.prototype,"index",void 0),(0,i._)([(0,n.Cb)({type:d.apiValues,readOnly:!0,json:{read:d.read,write:d.write}})],h.prototype,"type",void 0),(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],h.prototype,"field",void 0),(0,i._)([(0,a.p)("field")],h.prototype,"castField",null),(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],h.prototype,"valueExpression",void 0),(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],h.prototype,"valueExpressionTitle",void 0),(0,i._)([(0,n.Cb)({readOnly:!0})],h.prototype,"arcadeRequired",null),(0,i._)([(0,n.Cb)({type:c.Z,json:{write:!0}})],h.prototype,"legendOptions",void 0),h=(0,i._)([(0,u.j)("esri.renderers.visualVariables.VisualVariable")],h);const y=h},21849:(e,t,s)=>{s.d(t,{Z:()=>d});var i,r=s(43697),l=s(22303),o=s(96674),n=s(5600),a=s(75215),u=(s(67676),s(52011)),p=s(30556);let c=i=class extends o.wq{constructor(e){super(e),this.color=null,this.label=null,this.value=null}writeValue(e,t,s){t[s]=e??0}clone(){return new i({color:this.color&&this.color.clone(),label:this.label,value:this.value})}};(0,r._)([(0,n.Cb)({type:l.Z,json:{type:[a.z8],write:!0}})],c.prototype,"color",void 0),(0,r._)([(0,n.Cb)({type:String,json:{write:!0}})],c.prototype,"label",void 0),(0,r._)([(0,n.Cb)({type:Number,json:{write:{writerEnsuresNonNull:!0}}})],c.prototype,"value",void 0),(0,r._)([(0,p.c)("value")],c.prototype,"writeValue",null),c=i=(0,r._)([(0,u.j)("esri.renderers.visualVariables.support.ColorStop")],c);const d=c},30856:(e,t,s)=>{s.d(t,{Z:()=>p});var i,r=s(43697),l=s(96674),o=s(62357),n=s(5600),a=(s(75215),s(67676),s(52011));let u=i=class extends l.wq{constructor(e){super(e),this.label=null,this.size=null,this.value=null}clone(){return new i({label:this.label,size:this.size,value:this.value})}};(0,r._)([(0,n.Cb)({type:String,json:{write:!0}})],u.prototype,"label",void 0),(0,r._)([(0,n.Cb)({type:Number,cast:o.t_,json:{write:!0}})],u.prototype,"size",void 0),(0,r._)([(0,n.Cb)({type:Number,json:{write:!0}})],u.prototype,"value",void 0),u=i=(0,r._)([(0,a.j)("esri.renderers.visualVariables.support.SizeStop")],u);const p=u},58439:(e,t,s)=>{s.d(t,{Z:()=>u});var i,r=s(43697),l=s(5600),o=(s(75215),s(67676),s(52011)),n=s(66338);let a=i=class extends n.I{constructor(){super(...arguments),this.showLegend=null}clone(){return new i({title:this.title,showLegend:this.showLegend})}};(0,r._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],a.prototype,"showLegend",void 0),a=i=(0,r._)([(0,o.j)("esri.renderers.visualVariables.support.VisualVariableLegendOptions")],a);const u=a},28101:(e,t,s)=>{s.d(t,{B3:()=>c,PR:()=>f,cM:()=>d}),s(22303);var i=s(38171),r=s(74085),l=s(92604),o=s(70586),n=s(99282),a=s(51706);const u=l.Z.getLogger("esri.renderers.visualVariables.support.visualVariableUtils"),p=(new i.Z,Math.PI),c=/^\s*(return\s+)?\$view\.scale\s*(;)?\s*$/i;function d(e,t,s){const i="visualVariables"in e&&e.visualVariables?e.visualVariables.find((e=>"rotation"===e.type)):e;if(!i)return;if("esri.renderers.visualVariables.RotationVariable"!==i.declaredClass)return void u.warn("The visualVariable should be an instance of esri.renderers.visualVariables.RotationVariable");const r=i.axis||"heading",l="heading"===r&&"arithmetic"===i.rotationType?90:0,n="heading"===r&&"arithmetic"===i.rotationType?-1:1,a="number"==typeof t?null:t,p=a&&a.attributes,c=i.field,{hasExpression:d}=i.cache;let h=i.cache.compiledFunc,y=0;if(!c&&!d)return y;if(d){if((0,o.Wi)(s)||(0,o.Wi)(s.arcade))return void u.error("Use of arcade expressions requires an arcade context");const e={viewingMode:s.viewingMode,scale:s.scale,spatialReference:s.spatialReference},t=s.arcade.arcadeUtils,r=t.getViewInfo(e),l=t.createExecContext(a,r);if(!h){const e=t.createSyntaxTree(i.valueExpression);h=t.createFunction(e),i.cache.compiledFunc=h}y=t.executeFunction(h,l)}else p&&(y=p[c]||0);return y="number"!=typeof y||isNaN(y)?null:l+n*y,y}function h(e,t,s){const i="visualVariables"in e&&e.visualVariables?e.visualVariables.find((e=>"size"===e.type)):e;if(!i)return;if("esri.renderers.visualVariables.SizeVariable"!==i.declaredClass)return void u.warn("The visualVariable should be an instance of esri.renderers.visualVariables.SizeVariable");const r=function(e,t,s,i,r){switch(t.transformationType){case a.hL.Additive:return function(e,t,s,i){return e+((y(t.minSize,s,i)||t.minDataValue)??0)}(e,t,s,i);case a.hL.Constant:return function(e,t,s){const i=e.stops;let r=i&&i.length&&i[0].size;return null==r&&(r=e.minSize),y(r,t,s)}(t,s,i);case a.hL.ClampedLinear:return function(e,t,s,i){const r=(e-t.minDataValue)/(t.maxDataValue-t.minDataValue),l=y(t.minSize,s,i),n=y(t.maxSize,s,i),a=(0,o.pC)(i)?i.shape:void 0;if(e<=t.minDataValue)return l;if(e>=t.maxDataValue)return n;if(null==l||null==n)return null;if("area"===t.scaleBy&&a){const e="circle"===a,t=e?p*(l/2)**2:l*l,s=t+r*((e?p*(n/2)**2:n*n)-t);return e?2*Math.sqrt(s/p):Math.sqrt(s)}return l+r*(n-l)}(e,t,s,i);case a.hL.Proportional:return function(e,t,s,i){const r=(0,o.pC)(i)?i.shape:void 0,l=e/t.minDataValue,n=y(t.minSize,s,i),a=y(t.maxSize,s,i);let u=null;return u="circle"===r?2*Math.sqrt(l*(n/2)**2):"square"===r||"diamond"===r||"image"===r?Math.sqrt(l*n**2):l*n,m(u,n,a)}(e,t,s,i);case a.hL.Stops:return function(e,t,s,i,r){const[l,o,n]=function(e,t){if(!t)return;let s=0,i=t.length-1;return t.some(((t,r)=>e<t?(i=r,!0):(s=r,!1))),[s,i,(e-t[s])/(t[i]-t[s])]}(e,r);if(l===o)return y(t.stops?.[l].size,s,i);{const e=y(t.stops?.[l].size,s,i);return e+(y(t.stops?.[o].size,s,i)-e)*n}}(e,t,s,i,r);case a.hL.RealWorldSize:return function(e,t,s,i){const r=((0,o.pC)(i)&&i.resolution?i.resolution:1)*n.a[t.valueUnit],l=y(t.minSize,s,i),a=y(t.maxSize,s,i),{valueRepresentation:u}=t;let c=null;return c="area"===u?2*Math.sqrt(e/p)/r:"radius"===u||"distance"===u?2*e/r:e/r,m(c,l,a)}(e,t,s,i);case a.hL.Identity:return e;case a.hL.Unknown:return null}}(function(e,t,s){const i="number"==typeof t,r=i?null:t,l=r&&r.attributes;let n=i?t:null;const{isScaleDriven:p}=e.cache;let c=e.cache.compiledFunc;if(p){const t=(0,o.pC)(s)?s.scale:void 0,i=(0,o.pC)(s)?s.view:void 0;n=null==t||"3d"===i?function(e){let t=null,s=null;const i=e.stops;return i?(t=i[0].value,s=i[i.length-1].value):(t=e.minDataValue||0,s=e.maxDataValue||0),(t+s)/2}(e):t}else if(!i)switch(e.inputValueType){case a.RY.Expression:{if((0,o.Wi)(s)||(0,o.Wi)(s.arcade))return void u.error("Use of arcade expressions requires an arcade context");const t={viewingMode:s.viewingMode,scale:s.scale,spatialReference:s.spatialReference},i=s.arcade.arcadeUtils,l=i.getViewInfo(t),a=i.createExecContext(r,l);if(!c){const t=i.createSyntaxTree(e.valueExpression);c=i.createFunction(t),e.cache.compiledFunc=c}n=i.executeFunction(c,a);break}case a.RY.Field:l&&(n=l[e.field]);break;case a.RY.Unknown:n=null}if(!(0,a.qh)(n))return null;if(i||!e.normalizationField)return n;const d=l?parseFloat(l[e.normalizationField]):null;return(0,a.qh)(d)&&0!==d?n/d:null}(i,t,s),i,t,s,i.cache.ipData);return null==r||isNaN(r)?0:r}function y(e,t,s){return null==e?null:(0,a.iY)(e)?h(e,t,s):(0,a.qh)(e)?e:null}function m(e,t,s){return(0,a.qh)(s)&&e>s?s:(0,a.qh)(t)&&e<t?t:e}function f(e,t,s){const i=["proportional","proportional","proportional"];for(const l of e){const e=l.useSymbolValue?"symbol-value":h(l,t,s);switch(l.axis){case"width":i[0]=e;break;case"depth":i[1]=e;break;case"height":i[2]=e;break;case"width-and-depth":i[0]=e,i[1]=e;break;case"all":case void 0:case null:i[0]=e,i[1]=e,i[2]=e;break;default:(0,r.Bg)(l.axis)}}return i}},98046:(e,t,s)=>{s.d(t,{Z:()=>h});var i,r=s(43697),l=s(22303),o=s(22974),n=s(5600),a=s(75215),u=s(36030),p=s(52011),c=s(899);let d=i=class extends c.Z{constructor(e){super(e),this.algorithm=null,this.fromColor=null,this.toColor=null,this.type="algorithmic"}clone(){return new i({fromColor:(0,o.d9)(this.fromColor),toColor:(0,o.d9)(this.toColor),algorithm:this.algorithm})}};(0,r._)([(0,u.J)({esriCIELabAlgorithm:"cie-lab",esriHSVAlgorithm:"hsv",esriLabLChAlgorithm:"lab-lch"})],d.prototype,"algorithm",void 0),(0,r._)([(0,n.Cb)({type:l.Z,json:{type:[a.z8],write:!0}})],d.prototype,"fromColor",void 0),(0,r._)([(0,n.Cb)({type:l.Z,json:{type:[a.z8],write:!0}})],d.prototype,"toColor",void 0),(0,r._)([(0,n.Cb)({type:["algorithmic"]})],d.prototype,"type",void 0),d=i=(0,r._)([(0,p.j)("esri.rest.support.AlgorithmicColorRamp")],d);const h=d},899:(e,t,s)=>{s.d(t,{Z:()=>a});var i=s(43697),r=s(96674),l=s(5600),o=(s(75215),s(67676),s(52011));let n=class extends r.wq{constructor(e){super(e),this.type=null}};(0,i._)([(0,l.Cb)({readOnly:!0,json:{read:!1,write:!0}})],n.prototype,"type",void 0),n=(0,i._)([(0,o.j)("esri.rest.support.ColorRamp")],n);const a=n},1515:(e,t,s)=>{s.d(t,{Z:()=>c});var i,r=s(43697),l=s(22974),o=s(5600),n=(s(75215),s(52011)),a=s(98046),u=s(899);let p=i=class extends u.Z{constructor(e){super(e),this.colorRamps=null,this.type="multipart"}clone(){return new i({colorRamps:(0,l.d9)(this.colorRamps)})}};(0,r._)([(0,o.Cb)({type:[a.Z],json:{write:!0}})],p.prototype,"colorRamps",void 0),(0,r._)([(0,o.Cb)({type:["multipart"]})],p.prototype,"type",void 0),p=i=(0,r._)([(0,n.j)("esri.rest.support.MultipartColorRamp")],p);const c=p},94593:(e,t,s)=>{s.d(t,{V:()=>o,i:()=>n});var i=s(98046),r=s(899),l=s(1515);const o={key:"type",base:r.Z,typeMap:{algorithmic:i.Z,multipart:l.Z}};function n(e){return e&&e.type?"algorithmic"===e.type?i.Z.fromJSON(e):"multipart"===e.type?l.Z.fromJSON(e):null:null}},72245:(e,t,s)=>{s.d(t,{K3:()=>r,Rx:()=>l});var i=s(80442);const r=()=>!!(0,i.Z)("enable-feature:force-wosr"),l=()=>!!(0,i.Z)("enable-feature:SceneLayer-editing")},21878:(e,t,s)=>{s.d(t,{im:()=>C,cW:()=>_,vX:()=>V});var i=s(9790),r=s(20102),l=s(70586),o=s(84230),n=s(87223),a=s(59390),u=s(42143),p=s(4095),c=s(98587),d=s(77987),h=s(37898),y=s(20256),m=s(3456),f=s(86114),b=s(78724),v=s(20825);const g={retainId:!1,ignoreDrivers:!1,hasLabelingContext:!0};function w(e,t=g){if(!e)return{symbol:null};const{retainId:s=g.retainId,ignoreDrivers:l=g.ignoreDrivers,hasLabelingContext:o=g.hasLabelingContext,retainCIM:n=g.retainCIM}=t;let w=null;if((0,i.dU)(e)||e instanceof a.Z)w=e.clone();else if("cim"===e.type){const t=e.data?.symbol?.type;if("CIMPointSymbol"!==t)return{error:new r.Z("symbol-conversion:unsupported-cim-symbol",`CIM symbol of type '${t||"unknown"}' is unsupported in 3D`,{symbol:e})};w=n?e.clone():u.Z.fromCIMSymbol(e)}else if(e instanceof p.Z)w=c.Z.fromSimpleLineSymbol(e);else if(e instanceof d.Z)w=u.Z.fromSimpleMarkerSymbol(e);else if(e instanceof h.Z)w=u.Z.fromPictureMarkerSymbol(e);else if(e instanceof y.Z)w=t.geometryType&&"mesh"===t.geometryType?m.Z.fromSimpleFillSymbol(e):f.Z.fromSimpleFillSymbol(e);else{if(!(e instanceof b.Z))return{error:new r.Z("symbol-conversion:unsupported-2d-symbol",`2D symbol of type '${e.type||e.declaredClass}' is unsupported in 3D`,{symbol:e})};w=o?v.Z.fromTextSymbol(e):u.Z.fromTextSymbol(e)}if(s&&w&&"cim"!==w.type&&(w.id=e.id),l&&(0,i.dU)(w))for(let e=0;e<w.symbolLayers.length;++e)w.symbolLayers.getItemAt(e)._ignoreDrivers=!0;return{symbol:w}}function _(e,t,s,i){const r=x(e,{},{context:i,isLabelSymbol:!1});(0,l.pC)(r)&&(t[s]=r)}function V(e,t,s,i){const r=x(e,{},{context:i,isLabelSymbol:!0});(0,l.pC)(r)&&(t[s]=r)}function S(e){return e instanceof n.Z||e instanceof a.Z}function x(e,t,s){if((0,l.Wi)(e))return null;const{context:i,isLabelSymbol:n}=s,a=i?.origin,u=i?.messages;if("web-scene"===a&&!S(e)){const s=w(e,{retainCIM:!0,hasLabelingContext:n});return(0,l.pC)(s.symbol)?s.symbol.write(t,i):(u?.push(new r.Z("symbol:unsupported",`Symbols of type '${e.declaredClass}' are not supported in scenes. Use 3D symbology instead when working with WebScene and SceneView`,{symbol:e,context:i,error:s.error})),null)}return("web-map"===a||"portal-item"===a&&!(0,o.A2)(i?.layer))&&S(e)?(u?.push(new r.Z("symbol:unsupported",`Symbols of type '${e.declaredClass}' are not supported in web maps and portal items. Use 2D symbology and CIMSymbol instead when working with MapView`,{symbol:e,context:i})),null):e.write(t,i)}function C(e,t){return(0,i.S9)(e,null,t)}},27883:(e,t,s)=>{s.d(t,{EJ:()=>m,KV:()=>h,n2:()=>d,v9:()=>y,wm:()=>v});var i=s(3172),r=s(20102),l=s(70586),o=s(95330),n=s(17452),a=s(65587),u=s(41253),p=s(72245);let c={};function d(e,t,s){return e&&(0,l.pC)(e.styleUrl)?async function(e,t){try{return{data:(await m(e,t)).data,baseUrl:(0,n.Yd)(e),styleUrl:e}}catch(e){return(0,o.r9)(e),null}}(e.styleUrl,s):e&&(0,l.pC)(e.styleName)?function(e,t,s){const i=(0,l.pC)(t.portal)?t.portal:a.Z.getDefault();let o;const n=`${i.url} - ${i.user&&i.user.username} - ${e}`;return c[n]||(c[n]=function(e,t,s){return t.load(s).then((()=>{const i=new u.Z({disableExtraQuery:!0,query:`owner:${f} AND type:${b} AND typekeywords:"${e}"`});return t.queryItems(i,s)})).then((({results:t})=>{let i=null;const l=e.toLowerCase();if(t&&Array.isArray(t))for(const e of t){const t=e.typeKeywords?.some((e=>e.toLowerCase()===l));if(t&&e.type===b&&e.owner===f){i=e;break}}if(!i)throw new r.Z("symbolstyleutils:style-not-found",`The style '${e}' could not be found`,{styleName:e});return i.load(s)}))}(e,i,s).then((e=>(o=e,e.fetchData()))).then((t=>({data:t,baseUrl:o.itemUrl??"",styleName:e})))),c[n]}(e.styleName,t,s):Promise.reject(new r.Z("symbolstyleutils:style-url-and-name-missing","Either styleUrl or styleName is required to resolve a style"))}function h(e){return null===e||"CIMSymbolReference"===e.type?e:{type:"CIMSymbolReference",symbol:e}}function y(e,t){if("cimRef"===t)return e.cimRef;if(e.formatInfos&&!(0,p.K3)())for(const t of e.formatInfos)if("gltf"===t.type)return t.href;return e.webRef}function m(e,t){const s={responseType:"json",query:{f:"json"},...t};return(0,i.default)((0,n.Fv)(e),s)}const f="esri_en",b="Style",v="https://cdn.arcgis.com/sharing/rest/content/items/220936cc6ed342c9937abd8f180e7d1e/resources/styles/cim/{SymbolName}.json?f=json"}}]);