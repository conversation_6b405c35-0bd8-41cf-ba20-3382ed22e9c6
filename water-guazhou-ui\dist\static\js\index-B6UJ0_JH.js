import{d as J,c as N,r as Q,o as K,bF as G,bv as X,x as i,y as Z,C as _,g as V,n as U,q as t,F as s,G as p,p as c,bo as x,h as M,bh as ee,aB as ae,aJ as re,H as te,I as le,c2 as oe,aK as ue,aL as se,J as ne,K as de,N as ie,O as me,P as pe,e4 as fe,L as ce,bK as ge,br as ye}from"./index-r0dFAfgr.js";import{x as b}from"./xlsx-rVJkW9yq.js";import{g as Fe,a as be,d as Ce,s as Pe,e as we,i as Ee}from"./pressure-DUrGd62l.js";const ve=J({name:"WaterPlantPressure",setup(){const r=N(null),a=N(null),d=N({id:"",waterPlantId:"",waterPlantName:"",pressure:0,recordTime:"",remark:"",deviceSerial:"",dataSource:"手动填报"}),L={waterPlantId:[{required:!0,message:"请选择水厂",trigger:"change"}],pressure:[{required:!0,message:"请输入出厂水压",trigger:"blur"}],recordTime:[{required:!0,message:"请选择记录时间",trigger:"change"}]},l=Q({queryParams:{page:1,size:10,waterPlantType:"",waterPlantName:"",startTime:"",endTime:"",dataSource:""},dateRange:[],loading:!1,pressureList:[],total:0,dialogTitle:"",dialogVisible:!1,waterPlantOptions:[],importDialogVisible:!1,fileList:[],importData:[]}),y=async()=>{l.loading=!0;try{const e=await Fe(l.queryParams);e&&e.data&&e.data.code===200&&e.data.data?e.data.data.data&&Array.isArray(e.data.data.data)?(l.pressureList=e.data.data.data,l.total=e.data.data.total||0):(l.pressureList=[],l.total=0):(l.pressureList=[],l.total=0)}catch{i.error("获取水厂压力列表失败"),l.pressureList=[],l.total=0}finally{l.loading=!1}},C=async()=>{try{const e=await be({page:1,size:1e3});let o=[];e&&e.data?e.data.data&&Array.isArray(e.data.data)?o=e.data.data:e.data.code===200?e.data.data&&Array.isArray(e.data.data.data)?o=e.data.data.data:e.data.data&&Array.isArray(e.data.data.records)?o=e.data.data.records:Array.isArray(e.data.data)&&(o=e.data.data):Array.isArray(e.data)?o=e.data:e.data.records&&Array.isArray(e.data.records)&&(o=e.data.records):Array.isArray(e)&&(o=e),l.waterPlantOptions=o}catch{i.error("获取水厂列表失败")}},f=()=>{l.queryParams.page=1,y()},E=()=>{var e;(e=r.value)==null||e.resetFields(),l.dateRange=[],l.queryParams.startTime="",l.queryParams.endTime="",f()},P=e=>{e?(l.queryParams.startTime=e[0],l.queryParams.endTime=e[1]):(l.queryParams.startTime="",l.queryParams.endTime="")},v=e=>{l.queryParams.size=e,y()},m=e=>{l.queryParams.page=e,y()},D=()=>{l.dialogTitle="新增水厂压力信息";const e=Date.now();d.value={id:"",waterPlantId:"",waterPlantName:"",pressure:0,recordTime:e,remark:"",deviceSerial:"",dataSource:"手动填报"},l.dialogVisible=!0},g=e=>{try{l.dialogTitle="编辑水厂压力信息";const o=JSON.parse(JSON.stringify(e));let n;o.recordTime&&typeof o.recordTime=="number"?n=o.recordTime:n=Date.now(),d.value={id:o.id,waterPlantId:o.waterPlantId||"",waterPlantName:o.waterPlantName||"",pressure:o.pressure!==null&&o.pressure!==void 0?Number(o.pressure):0,deviceSerial:o.deviceSerial||"",dataSource:o.dataSource||"手动填报",remark:o.remark||"",recordTime:n},console.log("编辑时的ID:",d.value.id),l.dialogVisible=!0}catch{i.error("编辑数据时出错")}},k=e=>{Z.confirm("确认删除该水厂压力信息吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await Ce(e.id),i.success("删除成功"),y()}catch{i.error("删除失败")}}).catch(()=>{})},A=e=>{try{const o=l.waterPlantOptions.find(n=>n.id===e);o&&(d.value.waterPlantName=o.name||o.waterPlantName||"")}catch{i.error("选择水厂时出错")}},h=()=>{try{const e=a.value;e&&e.validate(async o=>{if(o)try{const n={...d.value,id:d.value.id||void 0,waterPlantId:d.value.waterPlantId?d.value.waterPlantId.toString():"",pressure:d.value.pressure!==null?Number(d.value.pressure):0,recordTime:d.value.recordTime?String(d.value.recordTime):String(Date.now()),deviceSerial:d.value.deviceSerial||"",dataSource:d.value.dataSource||"手动填报"};console.log("提交的数据:",n),await Pe(n),i.success("保存成功"),l.dialogVisible=!1,y()}catch{i.error("保存失败")}})}catch{i.error("表单验证失败")}},B=()=>{l.importDialogVisible=!0,l.fileList=[],l.importData=[]},T=async()=>{try{const e=await we(l.queryParams),o=new Blob([e.data],{type:"application/vnd.ms-excel"}),n=document.createElement("a");n.href=URL.createObjectURL(o),n.download="水厂压力信息.xlsx",n.click(),URL.revokeObjectURL(n.href)}catch{i.error("导出失败")}},S=e=>{const o=e.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||e.type==="application/vnd.ms-excel",n=e.size/1024/1024<10;return o?n?!0:(i.error("上传文件大小不能超过10MB!"),!1):(i.error("上传文件只能是Excel格式!"),!1)},u=e=>{const o=e.file,n=new FileReader;n.onload=W=>{const j=W.target.result,I=b.read(j,{type:"array"}),Y=I.SheetNames[0],$=I.Sheets[Y],H=b.utils.sheet_to_json($);l.importData=H.map(w=>{let F=w.记录时间;return F&&typeof F=="string"?F=new Date(F).getTime():F=new Date().getTime(),{waterPlantName:w.水厂名称,pressure:Number(w["出厂水压(MPa)"]||0),recordTime:F,remark:w.备注||"",deviceSerial:w.设备编号||"",dataSource:"手动填报"}})},n.readAsArrayBuffer(o)},q=()=>{i.warning("最多只能上传1个文件")},z=()=>{l.importData=[]},R=()=>{const e=[{水厂名称:"示例水厂","出厂水压(MPa)":1.02,记录时间:"2023-01-01 08:00:00",设备编号:"DEV001",备注:"示例备注"}],o=b.utils.json_to_sheet(e),n=b.utils.book_new();b.utils.book_append_sheet(n,o,"水厂压力信息"),b.writeFile(n,"水厂压力信息导入模板.xlsx")},O=async()=>{if(l.importData.length===0){i.warning("请先上传文件");return}try{await Ee(l.importData),i.success("导入成功"),l.importDialogVisible=!1,y()}catch{i.error("导入失败")}};return K(()=>{y(),C()}),{queryForm:r,form:a,formData:d,rules:L,dayjs:G,...X(l),handleQuery:f,resetQuery:E,handleDateRangeChange:P,handleSizeChange:v,handleCurrentChange:m,handleAdd:D,handleEdit:g,handleDelete:k,handleWaterPlantChange:A,submitForm:h,handleImport:B,handleExport:T,beforeUpload:S,handleUpload:u,handleExceed:q,handleRemove:z,downloadTemplate:R,confirmImport:O}}}),De={class:"app-container"},Be={class:"table-toolbar"},Ve={class:"pagination-container"},ke={class:"dialog-footer"},Ae={class:"import-template"},he={class:"dialog-footer"};function Te(r,a,d,L,l,y){const C=te,f=le,E=oe,P=ue,v=se,m=ne,D=de,g=ie,k=me,A=pe,h=fe,B=ce,T=ge,S=ye;return V(),U("div",De,[t(D,{model:r.queryParams,ref:"queryForm",inline:!0,class:"search-form"},{default:s(()=>[t(f,{label:"名称筛选："},{default:s(()=>[t(C,{modelValue:r.queryParams.waterPlantName,"onUpdate:modelValue":a[0]||(a[0]=u=>r.queryParams.waterPlantName=u),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),t(f,{label:"日期范围："},{default:s(()=>[t(E,{modelValue:r.dateRange,"onUpdate:modelValue":a[1]||(a[1]=u=>r.dateRange=u),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",onChange:r.handleDateRangeChange},null,8,["modelValue","onChange"])]),_:1}),t(f,{label:"数据来源："},{default:s(()=>[t(v,{modelValue:r.queryParams.dataSource,"onUpdate:modelValue":a[2]||(a[2]=u=>r.queryParams.dataSource=u),placeholder:"请选择",clearable:""},{default:s(()=>[t(P,{label:"自动采集",value:"自动采集"}),t(P,{label:"手动填报",value:"手动填报"})]),_:1},8,["modelValue"])]),_:1}),t(f,null,{default:s(()=>[t(m,{type:"primary",onClick:r.handleQuery},{default:s(()=>a[13]||(a[13]=[p("查询")])),_:1},8,["onClick"]),t(m,{onClick:r.resetQuery},{default:s(()=>a[14]||(a[14]=[p("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),c("div",Be,[t(m,{type:"primary",onClick:r.handleAdd},{default:s(()=>a[15]||(a[15]=[p("新增")])),_:1},8,["onClick"]),t(m,{type:"success",onClick:r.handleImport},{default:s(()=>a[16]||(a[16]=[p("导入")])),_:1},8,["onClick"]),t(m,{type:"warning",onClick:r.handleExport},{default:s(()=>a[17]||(a[17]=[p("导出")])),_:1},8,["onClick"])]),x((V(),M(k,{data:r.pressureList,border:""},{default:s(()=>[t(g,{type:"index",label:"序号",width:"60",align:"center"}),t(g,{label:"水厂名称",prop:"waterPlantName",align:"center"}),t(g,{label:"设备编号",prop:"deviceSerial",align:"center"}),t(g,{label:"出厂水压/MPa",prop:"pressure",align:"center"}),t(g,{label:"记录时间",align:"center"},{default:s(u=>[p(ee(u.row.recordTime?r.dayjs(Number(u.row.recordTime)).format("YYYY-MM-DD HH:mm:ss"):""),1)]),_:1}),t(g,{label:"数据来源",prop:"dataSource",align:"center"}),t(g,{label:"备注",prop:"remark",align:"center"}),t(g,{label:"操作",align:"center",width:"200"},{default:s(u=>[t(m,{type:"primary",size:"small",onClick:q=>r.handleEdit(u.row)},{default:s(()=>a[18]||(a[18]=[p("编辑")])),_:2},1032,["onClick"]),t(m,{type:"danger",size:"small",onClick:q=>r.handleDelete(u.row)},{default:s(()=>a[19]||(a[19]=[p("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[S,r.loading]]),c("div",Ve,[t(A,{onSizeChange:r.handleSizeChange,onCurrentChange:r.handleCurrentChange,"current-page":r.queryParams.page,"page-sizes":[10,20,50,100],"page-size":r.queryParams.size,layout:"total, sizes, prev, pager, next, jumper",total:r.total},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"])]),t(B,{title:r.dialogTitle,modelValue:r.dialogVisible,"onUpdate:modelValue":a[9]||(a[9]=u=>r.dialogVisible=u),width:"500px","append-to-body":"","destroy-on-close":""},{footer:s(()=>[c("div",ke,[t(m,{onClick:a[8]||(a[8]=u=>r.dialogVisible=!1)},{default:s(()=>a[21]||(a[21]=[p("取 消")])),_:1}),t(m,{type:"primary",onClick:r.submitForm},{default:s(()=>a[22]||(a[22]=[p("确 定")])),_:1},8,["onClick"])])]),default:s(()=>[t(D,{ref:"form",model:r.formData,rules:r.rules,"label-width":"100px"},{default:s(()=>[t(f,{label:"水厂名称",prop:"waterPlantName"},{default:s(()=>[t(v,{modelValue:r.formData.waterPlantId,"onUpdate:modelValue":a[3]||(a[3]=u=>r.formData.waterPlantId=u),placeholder:"请选择水厂",filterable:"",onChange:r.handleWaterPlantChange},{default:s(()=>[(V(!0),U(ae,null,re(r.waterPlantOptions,u=>(V(),M(P,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),t(f,{label:"设备编号",prop:"deviceSerial"},{default:s(()=>[t(C,{modelValue:r.formData.deviceSerial,"onUpdate:modelValue":a[4]||(a[4]=u=>r.formData.deviceSerial=u),placeholder:"请输入设备编号"},null,8,["modelValue"])]),_:1}),t(f,{label:"出厂水压",prop:"pressure"},{default:s(()=>[t(h,{modelValue:r.formData.pressure,"onUpdate:modelValue":a[5]||(a[5]=u=>r.formData.pressure=u),precision:2,step:.1,min:0,placeholder:"请输入出厂水压(MPa)"},null,8,["modelValue"]),a[20]||(a[20]=c("span",{class:"unit"},"MPa",-1))]),_:1}),t(f,{label:"记录时间",prop:"recordTime"},{default:s(()=>[t(E,{modelValue:r.formData.recordTime,"onUpdate:modelValue":a[6]||(a[6]=u=>r.formData.recordTime=u),type:"datetime",placeholder:"选择日期时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"x",clearable:!1,editable:!1},null,8,["modelValue"])]),_:1}),t(f,{label:"备注",prop:"remark"},{default:s(()=>[t(C,{modelValue:r.formData.remark,"onUpdate:modelValue":a[7]||(a[7]=u=>r.formData.remark=u),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(B,{title:"导入水厂压力信息",modelValue:r.importDialogVisible,"onUpdate:modelValue":a[12]||(a[12]=u=>r.importDialogVisible=u),width:"500px","append-to-body":""},{footer:s(()=>[c("div",he,[t(m,{onClick:a[11]||(a[11]=u=>r.importDialogVisible=!1)},{default:s(()=>a[26]||(a[26]=[p("取 消")])),_:1}),t(m,{type:"primary",onClick:r.confirmImport},{default:s(()=>a[27]||(a[27]=[p("确 定")])),_:1},8,["onClick"])])]),default:s(()=>[t(T,{class:"upload-demo",drag:"",action:"#","http-request":r.handleUpload,"before-upload":r.beforeUpload,"file-list":r.fileList,limit:1,"on-exceed":r.handleExceed,"on-remove":r.handleRemove,accept:".xlsx, .xls"},{tip:s(()=>a[23]||(a[23]=[c("div",{class:"el-upload__tip"},"只能上传xlsx/xls文件，且不超过10MB",-1)])),default:s(()=>[a[24]||(a[24]=c("i",{class:"el-icon-upload"},null,-1)),a[25]||(a[25]=c("div",{class:"el-upload__text"},[p("将文件拖到此处，或"),c("em",null,"点击上传")],-1))]),_:1},8,["http-request","before-upload","file-list","on-exceed","on-remove"]),c("div",Ae,[c("a",{onClick:a[10]||(a[10]=(...u)=>r.downloadTemplate&&r.downloadTemplate(...u))},"下载导入模板")])]),_:1},8,["modelValue"])])}const Le=_(ve,[["render",Te],["__scopeId","data-v-23617d67"]]);export{Le as default};
