<!-- 分区供水报表 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    ></CardTable>
  </TreeBox>
</template>
<script lang="ts" setup>
import {
  GetDmaPartitionSupplyReport,
  GetDmaPartitionSupplyReportHeader
} from '@/api/mapservice/dma/statisticAnalys/partitionSupply'
import { ICardTableIns, ISearchIns } from '@/components/type'
import { usePartition } from '@/hooks/arcgis'
import {
  formatterDate,
  formatterMonth,
  formatterYear
} from '@/utils/GlobalHelper'

const refTable = ref<ICardTableIns>()
const refSearch = ref<ISearchIns>()
const TreeData = reactive<SLTreeConfig>({
  data: [],
  title: '选择分区',
  expandOnClickNode: false,
  treeNodeHandleClick: (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      TreeData.currentProject = data
      refreshData()
    }
  }
})
const handleHidden = (params, query, config) => (config.hidden = params.type !== config.field)
// 列表模式搜索配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'month',
    year: moment().format(formatterYear),
    month: moment().format(formatterMonth),
    day: [moment().format(formatterDate), moment().format(formatterDate)]
  },
  filters: [
    {
      type: 'select',
      field: 'type',
      clearable: false,
      options: [
        { label: '按年', value: 'year' },
        { label: '按月', value: 'month' },
        { label: '按时间段', value: 'day' }
      ],
      label: '选择方式'
    },
    {
      handleHidden,
      type: 'year',
      label: '',
      field: 'year',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden,
      type: 'month',
      label: '',
      field: 'month',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden,
      type: 'daterange',
      label: '',
      field: 'day',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'primary',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ]
})
const initTableColumns = (
  title: string,
  subColumns: IFormTableColumn[]
): IFormTableColumn[] => {
  return [
    {
      prop: 'title',
      label: title,
      align: 'center',
      subColumns: [
        {
          prop: 'date',
          label: '日期',
          minWidth: 120,
          align: 'center',
          fixed: 'left'
        },
        {
          prop: 'sum',
          label: '总水量',
          minWidth: 120,
          align: 'center',
          formatter(row, value) {
            return value?.toFixed(2)
          }
        },
        {
          prop: 'title',
          label: '子分区',
          align: 'center',
          subColumns: [
            ...(subColumns || []),
            {
              prop: 'sumAll',
              label: '合计',
              minWidth: 160,
              align: 'center',
              formatter(row, value) {
                return value?.toFixed(2)
              }
            }
          ]
        },
        {
          prop: 'difference',
          label: '差值',
          minWidth: 120,
          align: 'center',
          fixed: 'right',
          formatter(row, value) {
            return value?.toFixed(2)
          }
        }
      ]
    }
  ]
}
// 列表
const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: initTableColumns('分区供水量报表', []),
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
      // TableConfig.dataList = TableConfig.slice((page - 1) * size, page * size)
    }
  }
})

// 数据获取
const refreshData = async () => {
  if (!TreeData.currentProject) return
  try {
    const query = refSearch.value?.queryParams || {}
    TableConfig.loading = true
    const p1 = GetDmaPartitionSupplyReportHeader({
      partitionId: TreeData.currentProject?.value
    })

    const p2 = GetDmaPartitionSupplyReport({
      partitionId: TreeData.currentProject?.value,
      type: query.type,
      date:
        query.type === 'month'
          ? query.month
          : query.type === 'year'
            ? query.year
            : undefined,
      start: query.type === 'day' ? query.day?.[0] : undefined,
      end: query.type === 'day' ? query.day?.[1] : undefined
    })
    const res = await Promise.all([p1, p2])
    const row3Columns:IFormTableColumn[] = res[0].data?.data?.map(item => {
      return {
        label: item,
        prop: item,
        minWidth: 160,
        formatter(row, value) {
          return value?.toFixed(2)
        }
      }
    })
    const subColumns = initTableColumns(
      TreeData.currentProject?.label,
      row3Columns
    )
    TableConfig.columns = subColumns
    const data: any[] = res[1]?.data?.data || []
    const length = data.length
    const total: any = {
      date: '合计',
      sumAll: data.reduce((prev, cur) => cur['sumAll'] + prev, 0),
      sum: data.reduce((prev, cur) => cur['sum'] + prev, 0),
      difference: data.reduce((prev, cur) => cur['difference'] + prev, 0)
    }
    const ave: any = {
      date: '平均',
      sumAll: length ? total['sumAll'] / length : 0,
      sum: length ? total['sum'] / length : 0,
      difference: length ? total['difference'] / length : 0
    }
    row3Columns?.map(item => {
      total[item.prop] = length
        ? data.reduce((prev, cur) => {
          return prev + cur[item.prop]
        }, 0)
        : 0
      ave[item.prop] = length ? total[item.prop] / length : 0
    })
    data.push(total)
    data.push(ave)
    TableConfig.dataList = data
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
const partition = usePartition()
onMounted(async () => {
  await partition.getTree()
  TreeData.data = partition.Tree.value
  TreeData.currentProject = TreeData.data[0]
  refreshData()
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}
</style>
