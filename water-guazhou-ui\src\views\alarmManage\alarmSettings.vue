<template>
  <!-- 告警设置 -->
  <TreeBox v-loading="!!TreeData.loading">
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch ref="refSearch" :config="SearchConfig" />
    <CardTable :config="TableConfig" class="card-table" />
    <SettingsDialog
      v-if="dialogConfig.visible"
      :dialog-config="dialogConfig"
      @save="Save"
    ></SettingsDialog>
    <ConnectDialog
      v-if="connectDialog.visible"
      :connect-dialog="connectDialog"
    ></ConnectDialog>
  </TreeBox>
</template>

<script lang="ts" setup>
import { saveAlarm, deleteAlarm, getAlarmSettingList } from '@/api/alarm'; // getAlarmList,
import { getDevice } from '@/api/device';
import { removeSlash } from '@/utils/removeIdSlash';
import SettingsDialog from './components/settingsDialog.vue';
import ConnectDialog from './components/connectDialog.vue';
import useGlobal from '@/hooks/global/useGlobal';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { useBusinessStore } from '@/store';

const businessStore = useBusinessStore();
const { $btnPerms } = useGlobal();
const refSearch = ref<ICardSearchIns>();
const state = reactive<{
  devices: any[];
  ents: any;
  severityColor: any;
}>({
  devices: [],
  ents: new Map(),
  severityColor: {
    提示: 'rgb(85,204,244)',
    次要: 'rgb(255,216,0)',
    重要: '#f58717',
    紧急: 'rgb(245,75,23)',
    严重: '#FF0000'
  }
});
const TreeData = reactive<SLTreeConfig>({
  title: '区域划分',
  data: businessStore.projectList,
  loading: false,
  isFilterTree: true,
  currentProject: businessStore.selectedProject,
  treeNodeHandleClick: (data) => {
    // 设置当前选中项目信息
    TreeData.currentProject = data;
    businessStore.SET_selectedProject(data);
    refreshData();
  }
});
const SearchConfig = reactive<ISearch>({
  defaultParams: { keyword: '' },
  filters: [
    { label: '搜索', field: 'keyword', type: 'input' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          icon: 'iconfont icon-chaxun',
          text: '查询',
          click: () => refreshData()
        },
        {
          perm: $btnPerms('AlarmSettingAdd'),
          type: 'primary',
          icon: 'iconfont icon-jia',
          text: '添加告警',
          click: () => addAlarmSet()
        }
      ]
    }
  ]
});
const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  // selectList: [],
  // tableSelectChangeHandle: val => (TableConfig.selectList = val),
  columns: [
    { minWidth: 200, prop: 'name', label: '告警名称' },
    { minWidth: 120, prop: 'dName', label: '告警设备', width: 180 },
    { minWidth: 120, prop: 'attributeName', label: '监测数据', width: 150 },
    { minWidth: 120, prop: 'alarmTypeName', label: '告警类型' },
    { minWidth: 120, prop: 'cycleName', label: '周期', width: 120 },
    { minWidth: 120, prop: 'alarmValue', label: '告警触发值', width: 120 },
    { minWidth: 120, prop: 'recoverSet', label: '恢复类型', width: 120 },
    { minWidth: 120, prop: 'recoverValue', label: '恢复触发值', width: 120 },
    {
      minWidth: 120,
      prop: 'severity',
      label: '告警级别',
      cellStyle: (row) => ({
        color: row.severityColor
      })
    },
    {
      minWidth: 120,
      prop: 'period',
      label: '有效时间段',
      icon: 'el-icon-time',
      iconStyle: {
        color: '#69e850'
      },
      width: 200
    },
    { minWidth: 120, prop: 'alarmRemarks', label: '告警描述' }
  ],
  operations: [
    {
      text: '关联联系人',

      isTextBtn: true,
      perm: $btnPerms('AlarmSettingConnectPerson'),
      icon: 'iconfont icon-xiangqing',
      click: (row) => connect(row)
    },
    {
      text: '编辑',
      isTextBtn: true,
      perm: $btnPerms('AlarmSettingEdit'),
      icon: 'iconfont icon-bianji',
      click: (row) => editAlarmSet(row)
    },
    {
      text: '删除',
      isTextBtn: true,
      type: 'danger',
      perm: $btnPerms('AlarmSettingDelete'),
      icon: 'iconfont icon-shanchu',
      click: (row) => handleDelete(row)
    }
  ],
  operationWidth: '280px',
  // operationFixed: 'right',
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.limit = size;
      TableConfig.pagination.page = page;
      refreshData();
    }
  }
});
const connectDialog = reactive<any>({
  visible: false,
  tableData: [],
  close: () => (connectDialog.visible = false)
});

const dialogConfig = reactive<any>({
  visible: false,
  temp: {},
  project: {},
  deviceList: [],
  close: () => (dialogConfig.visible = false)
});
const connect = (row) => {
  connectDialog.visible = true;
  connectDialog.tableData = row;
};
// 点击项目获取项目信息
const refreshData = async (isFirst?: boolean) => {
  TableConfig.loading = true;
  try {
    const paramsObj = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20
    };
    if (!isFirst) Object.assign(paramsObj, refSearch.value?.queryParams || {});
    // todo: 获取设备 存,然后或者对应告警
    const res = await getAlarmSettingList(
      TreeData.currentProject?.value,
      paramsObj
    );
    const list = await handleAlarm(res.data);
    console.log(list, 'listlistlistlist');
    TableConfig.dataList = list;
    TableConfig.pagination.total = res.data.total;
  } catch (error) {
    //
  }
  TableConfig.loading = false;
};
// 告警信息处理
const handleAlarm = async (val) => {
  state.devices = [];
  let list = val.data;
  TableConfig.pagination.total = val.data?.length;
  // 获取设备name id
  if (state.devices.length === 0 || state.ents.size === 0) {
    const tempDevice = await getDevice(TreeData.currentProject.id);
    tempDevice.data.forEach((v) => {
      const deviceInfo = {
        label: v.name,
        value: v.id.id
      };
      state.ents.set(v.id.id, v.name);
      state.devices.push(deviceInfo);
    });
  }
  const date = {
    day: '日',
    month: '月',
    year: '年'
  };
  list = list.map((listItem) => {
    const resultItem: any = {};
    listItem.alarmValue = listItem.details.alarmSetValue;
    listItem.recoverValue = listItem.details.recoverSetValue;
    listItem.alarmTypeName = listItem.details.setAlarmType;
    listItem.recoverSet = listItem.details.rType;
    listItem.alarmRemarks = listItem.details.alarmRemarks;
    listItem.attributeName = listItem.details.attributeName;
    listItem.severityColor = state.severityColor[listItem.severity];
    for (const item in listItem) {
      resultItem[item] = listItem[item];
    }
    resultItem.isCycle = listItem.isCycle || false;
    if (listItem.cycle) {
      resultItem.cycleName = date[listItem.cycle];
      resultItem.recoverSet = '-';
      resultItem.recoverValue = '-';
    } else {
      resultItem.cycleName = '';
      resultItem.cycle = null;
    }
    resultItem.dName = state.ents.get(listItem.deviceId.id);
    resultItem.name = listItem.name;
    return resultItem;
  });
  const compare = function (obj1, obj2) {
    const val1 = obj1.createdTime;
    const val2 = obj2.createdTime;
    if (val1 < val2) {
      return 1;
    }
    if (val1 > val2) {
      return -1;
    }
    return 0;
  };
  list = list.sort(compare);
  return list;
};

// 批量删除设置
const handleDelete = async (row) => {
  SLConfirm('确定删除该告警, 是否继续?', '删除提示').then(async () => {
    // let ids = []
    // if (row) {
    //   ids = [removeSlash(row.id.id)]
    // } else {
    //   ids = TableConfig.selectList.map(item => removeSlash(item.id.id))
    // }
    const res = await deleteAlarm(removeSlash(row.id.id));
    if (res.status === 200) {
      SLMessage.success('删除成功');
      refreshData();
    }
  });
};
// 点击 添加告警设置
const addAlarmSet = () => {
  dialogConfig.project = TreeData.currentProject;
  dialogConfig.temp = {};
  dialogConfig.deviceList = state.devices;
  dialogConfig.visible = true;
};
// 点击 编辑告警设置
const editAlarmSet = (item) => {
  dialogConfig.project = TreeData.currentProject;
  dialogConfig.temp = item;
  dialogConfig.deviceList = state.devices;
  dialogConfig.visible = true;
};
// dialog 保存告警
const Save = (params) => {
  params.projectId = TreeData.currentProject.id;
  saveAlarm(params).then(() => {
    SLMessage.success('保存成功');
    TableConfig.pagination.page = 1;
    refreshData();
  });
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.card-search {
  margin-bottom: 20px;
}
input {
  height: 48px;
}
.card-table {
  height: calc(100% - 80px);
}
</style>
