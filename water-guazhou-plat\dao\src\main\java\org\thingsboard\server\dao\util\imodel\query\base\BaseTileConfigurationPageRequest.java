package org.thingsboard.server.dao.util.imodel.query.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 公共管理平台-瓦片数据配置对象 base_tile_configuration
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@ApiModel(value = "瓦片数据配置", description = "公共管理平台-瓦片数据配置实体类")
@Data
public class BaseTileConfigurationPageRequest extends PageableQueryEntity<BaseTileConfiguration> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 服务描述
     */
    @ApiModelProperty(value = "服务描述")
    private String description;

    /**
     * 瓦片类型
     */
    @ApiModelProperty(value = "瓦片类型")
    private String type;

    /**
     * 瓦片服务url
     */
    @ApiModelProperty(value = "瓦片服务url")
    private String url;

    /**
     * 瓦片格式
     */
    @ApiModelProperty(value = "瓦片格式")
    private String format;

    /**
     * 瓦片尺寸
     */
    @ApiModelProperty(value = "瓦片尺寸")
    private String tileSize;

    /**
     * 坐标系标准
     */
    @ApiModelProperty(value = "坐标系标准")
    private String matrixSet;

    /**
     * 最小缩放等级
     */
    @ApiModelProperty(value = "最小缩放等级")
    private String minZoomLevel;

    /**
     * 最大缩放等级
     */
    @ApiModelProperty(value = "最大缩放等级")
    private String maxZoomLevel;

    /**
     * 透明度（0-1）
     */
    @ApiModelProperty(value = "透明度（0-1）")
    private String opacity;

    /**
     * 亮度调整（0-2）
     */
    @ApiModelProperty(value = "亮度调整（0-2）")
    private String brightness;
}
