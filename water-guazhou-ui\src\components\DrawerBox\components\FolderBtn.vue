<!-- 按钮组件 -->
<template>
  <div
    class="control-fold-btn"
    :class="[
      'control-fold-btn-' + (direction || 'ltr'),
      'control-fold-btn-' + (barPosition || 'center')
    ]"
    @click="emit('collapse')"
  >
    <Icon :icon="collapsed ? 'ep:caret-right' : 'ep:caret-left'"></Icon>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'

const emit = defineEmits<{(e: 'collapse'): any }>()
defineProps<{
  direction?: 'ltr' | 'rtl' | 'btt' | 'ttb'
  collapsed: boolean
  barPosition?: 'left' | 'top' | 'center' | 'bottom' | 'right'
}>()
</script>
<style lang="scss" scoped>
.dark {
  .control-fold-btn {
    &.control-fold-btn-rtl,
    &.control-fold-btn-ltr,
    &.control-fold-btn-btt,
    &.control-fold-btn-ttb {
      background: url('~@/assets/images/bar_black.png') 0 0 / 24px 80px
        no-repeat;
    }
  }
}
.control-fold-btn {
  position: absolute;
  background-color: var(--el-bg-color);
  z-index: 1;
  margin: 0 0;
  cursor: pointer;
  background: url('~@/assets/images/bar_light.png') 0 0 / 24px 80px no-repeat;
  // border-radius: 5px 0 0 5px;
  //切角
  // background: linear-gradient(150deg, transparent 12px, var(--el-bg-color) 0)
  //     top left,
  //   linear-gradient(25deg, transparent 12px, var(--el-bg-color) 0) bottom left;
  // background-size: 100% 50%;
  // background-repeat: no-repeat;
  // 阴影效果
  // filter: drop-shadow(-1px 0 2px rgba(0, 13, 77, 0.25));
  cursor: pointer;
  width: 24px;
  height: 80px;
  padding: 33px 0;
  vertical-align: middle;
  text-align: center;
  .fold-icon {
    width: 14px;
    height: 14px;
    font-size: 14px;
    // margin-left: -1px;
  }
  &.control-fold-btn-top {
    top: 15%;
  }
  &.control-fold-btn-bottom {
    bottom: 15%;
  }
  &.control-fold-btn-left {
    left: 40px;
  }
  &.control-fold-btn-right {
    right: 40px;
  }
  &.control-fold-btn-ltr {
    border-left: none;
    right: -24px;
    transform: rotate(180deg);
  }
  &.control-fold-btn-rtl {
    border-right: none;
    left: -24px;
  }

  &.control-fold-btn-btt {
    border-bottom: none;
    top: -52px;
    transform: rotate(90deg);
  }
  &.control-fold-btn-ttb {
    bottom: -52px;
    border-top: none;
    transform: rotate(-90deg);
  }
  &.control-fold-btn-center {
    &.control-fold-btn-ttb {
      left: 50%;
      transform: translateX(-50%) rotate(-90deg);
    }
    &.control-fold-btn-btt {
      left: 50%;
      transform: translateX(-50%) rotate(90deg);
    }
    &.control-fold-btn-rtl {
      top: 50%;
      transform: translateY(-50%);
      margin: auto 0;
    }
    &.control-fold-btn-ltr {
      top: 50%;
      transform: translateY(-50%) rotate(180deg);
      margin: auto 0;
    }
  }
}
</style>
