import{d as f,r as _,o as S,g as o,n as i,aB as g,aJ as x,q as p,h as v,F as y,av as C,i as O,cs as E,an as b,aH as h,cE as I,C as k}from"./index-r0dFAfgr.js";import{l as B}from"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const T={class:"step-wrapper"},z=f({__name:"OrderStepTags",props:{config:{}},setup(d){const u=d,s=_({OrderStatus:[],CurStatus:void 0}),m=()=>{var a;let e=B();s.CurStatus=e.find(t=>t.value===u.config.status),((a=s.CurStatus)==null?void 0:a.value)==="REJECTED"?e=e.filter(t=>t.value!=="APPROVED"):e=e.filter(t=>t.value!=="REJECTED"),e.findIndex(t=>t.value===u.config.status)===-1?s.OrderStatus=[{perm:!0,text:u.config.statusName,size:"small"}]:s.OrderStatus=e.map(t=>({perm:!0,text:t.label,size:"small"}))};S(()=>{m()});const l=(e,c)=>{var t;const a=s.OrderStatus.findIndex(n=>{var r;return n.text===((r=s.CurStatus)==null?void 0:r.label)});return e.text===((t=s.CurStatus)==null?void 0:t.label)?"success":c<=a?"primary":"info"};return(e,c)=>{const a=h,t=I;return o(),i("div",T,[(o(!0),i(g,null,x(s.OrderStatus,(n,r)=>(o(),i("div",{key:r,class:"step-item"},[p(a,{config:n,type:l(n,r)},null,8,["config","type"]),r!==s.OrderStatus.length-1?(o(),v(t,{key:0,style:{width:"40px",height:"100%","font-size":"24px"}},{default:y(()=>[p(O(E),{icon:"ep:more",style:C({color:l(n,r)==="primary"?"#79bbff":"#b1b3b8"})},null,8,["style"])]),_:2},1024)):b("",!0)]))),128))])}}}),R=k(z,[["__scopeId","data-v-c515714e"]]);export{R as default};
