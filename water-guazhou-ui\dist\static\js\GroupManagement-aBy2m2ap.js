import{_ as V}from"./TreeBox-DDD2iwoR.js";import{_ as B}from"./index-C9hz-UZb.js";import{d as G,c as I,r as i,a0 as m,a8 as N,y as j,a9 as g,o as P,g as D,h as E,F as o,p,q as a,i as _,G as v,bU as L,_ as M,J as S,bW as w,C as A}from"./index-r0dFAfgr.js";import{_ as U}from"./index-BJ-QPYom.js";import{U as $,G as q,a as H}from"./GeneralProcessing-CQ8i9ijT.js";import{p as y,h as W,i as J}from"./index-D9ERhRP6.js";const O={class:"tree-box"},R={class:"form_box"},z=G({__name:"GroupManagement",setup(K){const r=I(),l=i({AreaTree:[],title:""}),d=i({title:"区域划分",data:m().projectList,currentProject:m().selectedProject,isFilterTree:!0,treeNodeHandleClick:e=>{var t;d.currentProject=e,u(),n.defaultValue={parentId:e.id},(t=r.value)==null||t.resetForm()}}),f=i({title:"分组管理",isFilterTree:!0,data:[],nodeOperations:[{iconifyIcon:"material-symbols:add",perm:!0,type:"success",text:"新增分组",click:e=>{var t;l.title="新增分组",n.defaultValue={parentId:e.id},(t=r.value)==null||t.resetForm()}},{iconifyIcon:"material-symbols:delete-outline",perm:!0,type:"danger",text:"删除",click:e=>{$(e.id,W,"确认删除该分组").then(()=>{var t;n.defaultValue={},(t=r.value)==null||t.resetForm(),u()})}}],treeNodeHandleClick:e=>{var t;l.title=e.name,n.defaultValue={...e,parentId:e.parentId,orderNum:e.nodeDetail.orderNum},(t=r.value)==null||t.resetForm()}}),n=i({labelWidth:"80px",group:[{fields:[{readonly:!0,type:"select-tree",label:"上级分组",field:"parentId",options:N(()=>l.AreaTree)},{type:"input",label:"名称",field:"name",rules:[{required:!0,message:"请输入名称"}]},{type:"input-number",label:"排序",field:"orderNum"}]}],submit:e=>{let t=e.id?"修改":"添加";j.confirm(`确定${t}分组,是否继续?`,`${t}提示`,{confirmButtonText:"确定",cancelButtonText:"取消",type:"success"}).then(()=>{q(e,y,y,n).then(()=>{var s;n.defaultValue={},(s=r.value)==null||s.resetForm(),u()})})},defaultValue:{}}),T=()=>{var e;(e=r.value)!=null&&e.dataForm&&(r.value.dataForm.name="")},F=()=>{var e;(e=r.value)==null||e.Submit()},u=()=>{const e={projectId:d.currentProject.id};H(e,J).then(t=>{f.data=t.data;const s=[...m().projectList],c=g(t.data);l.AreaTree=g([...c,...s],"children",{label:"label",value:"id"})})};return P(()=>{u()}),(e,t)=>{const s=U,c=L,x=M,b=S,C=w,h=B,k=V;return D(),E(k,null,{tree:o(()=>[p("div",O,[a(s,{ref:"refTree","tree-data":_(d)},null,8,["tree-data"])])]),default:o(()=>[a(h,{style:{height:"100%"}},{default:o(()=>[a(C,{gutter:20},{default:o(()=>[a(c,{span:6},{default:o(()=>[a(s,{ref:"refTree","tree-data":_(f)},null,8,["tree-data"])]),_:1}),a(c,{span:18},{default:o(()=>[p("div",R,[a(x,{ref_key:"refForm",ref:r,config:_(n)},null,8,["config"]),p("div",null,[a(b,{type:"primary",onClick:T},{default:o(()=>t[0]||(t[0]=[v("重置")])),_:1}),a(b,{type:"success",onClick:F},{default:o(()=>t[1]||(t[1]=[v("保存")])),_:1})])])]),_:1})]),_:1})]),_:1})]),_:1})}}}),re=A(z,[["__scopeId","data-v-bb79503f"]]);export{re as default};
