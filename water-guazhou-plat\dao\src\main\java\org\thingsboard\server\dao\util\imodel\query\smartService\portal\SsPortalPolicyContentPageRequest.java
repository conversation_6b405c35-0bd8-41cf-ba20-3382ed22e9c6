package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicyContent;

@Getter
@Setter
public class SsPortalPolicyContentPageRequest extends AdvancedPageableQueryEntity<SsPortalPolicyContent, SsPortalPolicyContentPageRequest> {
    // 法规类型
    private String typeId;

    // 标题
    private String title;

}
