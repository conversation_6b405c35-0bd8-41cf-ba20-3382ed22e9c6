<!-- gis用户报装 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards
      v-model="cardsvalue"
      :span="12"
      style="margin-bottom: 10px"
    ></Cards>
    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components'
import { IFormIns } from '@/components/type'

const emit = defineEmits(['highlightMark', 'addMarks'])
const props = defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const refForm = ref<IFormIns>()

const cardsvalue = ref([
  { label: '0 %', value: '报装总数' },
  { label: '0 %', value: '完成率' }
])
const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    hide: true
  },
  columns: [
    {
      label: '项目编号',
      prop: 'key1',
      width: 90
    },
    {
      label: '名称',
      prop: 'key2'
    },
    {
      label: '流程',
      prop: 'key3'
    }
  ],
  handleRowClick: row => {
    emit('highlightMark', props.menu, row.id)
  }
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          field: 'layer',
          appendBtns: [
            {
              perm: true,
              isTextBtn: true,
              text: '刷新',
              click: () => refreshData()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const refreshData = () => {
  //
}
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  height: calc(100% - 115px);
}
</style>
