import{d as F,c as Y,r as g,l as m,b as u,bB as M,o as I,g as _,n as C,q as b,i as l,_ as S,C as L}from"./index-r0dFAfgr.js";import h from"./PipeDetail-CTBPYFJW.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import{e as k,i as T}from"./QueryHelper-ILO3qZqg.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./DateFormatter-Bm9a68Ax.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./config-fy91bijz.js";const B=F({__name:"CompleteTimeSearch",props:{view:{},telport:{}},setup(O){const c=O,n=Y(),f=Y(),t=g({pipeLayerOption:[],curOperate:"",tabs:[]}),y=g({group:[{fieldset:{desc:"图层名称"},fields:[{type:"select",field:"layer",options:[]}]},{fieldset:{desc:"起止时间"},fields:[{type:"daterange",field:"date"}]},{fields:[{type:"btn-group",btns:[{perm:!0,loading:()=>t.curOperate==="detailing",text:()=>t.curOperate==="detailing"?"正在查询":"查询",click:()=>D(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12,defaultValue:{date:[m().subtract(6,"M").format("YYYY-MM-DD"),m().format("YYYY-MM-DD")]}}),v=()=>{var a,i,d,s;if(!c.view)return;const r=(a=c.view)==null?void 0:a.map.findLayerById("pipelayer");t.pipeLayerOption=[],(i=r==null?void 0:r.sublayers)==null||i.map(o=>{var p;(p=t.pipeLayerOption)==null||p.push({label:o.title,value:o.title,id:o.id})});const e=y.group[0].fields[0];e&&(e.options=t.pipeLayerOption),(d=n.value)!=null&&d.dataForm&&(n.value.dataForm.layer=t.pipeLayerOption&&((s=t.pipeLayerOption[0])==null?void 0:s.value))},D=async()=>{var s;const{date:r}=((s=n.value)==null?void 0:s.dataForm)||{},[e,a]=(r==null?void 0:r.length)===2?[m(r[0]).format("YYYY/MM/DD"),m(r[1]).format("YYYY/MM/DD")]:[m().subtract(6,"M").format("YYYY/MM/DD"),m().format("YYYY/MM/DD")],i=t.pipeLayerOption.find(o=>{var p;return o.label===((p=n.value)==null?void 0:p.dataForm.layer)});if(!i){u.warning("请选择图层"),t.curOperate="";return}const d="FINISHDATE between date '"+e+"' and date '"+a+"'";try{t.curOperate="detailing";const o=await k(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+i.id,T({returnGeometry:!1,where:d,orderByFields:["OBJECTID asc"]}));o!=null&&o.length?(t.tabs=[{label:i.label,name:i.label,id:i.id,data:o}],M(()=>{var p;(p=f.value)==null||p.openDialog()})):(u.info("查询结果为空"),t.curOperate="",t.tabs=[])}catch{u.error("查询失败，请检查查询条件是否正确"),t.curOperate=""}},w=async()=>{var r;c.view&&((r=f.value)==null||r.extentTo(c.view))};return I(()=>{v()}),(r,e)=>{const a=S;return _(),C("div",null,[b(a,{ref_key:"refForm",ref:n,config:l(y)},null,8,["config"]),b(h,{ref_key:"refDetail",ref:f,tabs:l(t).tabs,telport:r.telport,onRefreshed:e[0]||(e[0]=()=>l(t).curOperate="viewingDetail"),onRefreshing:e[1]||(e[1]=()=>l(t).curOperate="detailing"),onClose:e[2]||(e[2]=i=>l(t).curOperate=""),onRowdblclick:w},null,8,["tabs","telport"])])}}}),er=L(B,[["__scopeId","data-v-8ab94330"]]);export{er as default};
