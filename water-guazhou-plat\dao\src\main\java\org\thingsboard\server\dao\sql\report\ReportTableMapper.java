package org.thingsboard.server.dao.sql.report;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.report.ReportTable;

@Mapper
public interface ReportTableMapper extends BaseMapper<ReportTable> {
    @Select("select * from tb_report_table where pid = #{pid}")
    ReportTable selectByPid(@Param("pid") String pid);

    void update(@Param("param") ReportTable reportTable);
}
