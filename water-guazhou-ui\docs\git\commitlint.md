# git提交校验

## 提交规范初始化

依赖：

- [commitizen](https://www.npmjs.com/package/commitizen)

  commitizen 是一个实现规范提交说明的工具

  使用 commitizen 在项目中生成符合 AngularJS 规范的提交说明，并初始化 cz-conventional-changelog 适配器

    ```cmd
    npx commitizen init cz-conventional-changelog --save --save-exact
    ```

上面操作效果如下：

1. 在项目中安装 cz-conventional-changelog 适配器依赖
2. 将适配器依赖保存到 package.json 的 devDependencies 对象中
3. 在 package.json 中新增 config.commitizen 字段，主要用于配置 cz 工具的适配器路径

## git提交校验工具

依赖：

- [@commitlint/cli](https://www.npmjs.com/package/@commitlint/cli)
- [@commitlint/config-conventional](https://www.npmjs.com/package/@commitlint/config-conventional)

1. 在根目录下添加commitlint.config.js并设置校验规则

    ```js
    module.exports = {
    extends: ['@commitlint/config-conventional']
    };
    ```

2. 在package.json中更新如下内容

    ```json
    {
        "scripts": {
            "commit:comment": "引导设置规范化的提交信息",
            "commit": "cz"
        }
    }
    ```

## 自动化配置

依赖：

- [husky](https://www.npmjs.com/package/husky)

- [lint-staged](https://www.npmjs.com/package/lint-staged)

1. 安装husky

    ```cmd
    yarn add husky -D
    ```

2. 初始化husky

    在package.json的scripts中更新如下代码：

    ```json
    "prepare": "husky install"
    ```
