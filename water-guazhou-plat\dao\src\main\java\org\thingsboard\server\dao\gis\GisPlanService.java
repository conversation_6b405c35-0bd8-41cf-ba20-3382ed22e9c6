package org.thingsboard.server.dao.gis;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.gis.GisPlan;

import java.util.List;

public interface GisPlanService {
    void save(GisPlan entity);

    PageData<GisPlan> findList(int page, int size, String name, String type, User currentUser);

    void remove(List<String> ids);
}
