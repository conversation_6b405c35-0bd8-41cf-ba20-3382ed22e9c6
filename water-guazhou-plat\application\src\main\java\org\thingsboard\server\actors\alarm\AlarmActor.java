/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.actors.alarm;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.thingsboard.rule.engine.api.MailService;
import org.thingsboard.server.actors.ActorSystemContext;
import org.thingsboard.server.actors.ruleChain.RuleChainManagerActor;
import org.thingsboard.server.actors.service.ContextBasedCreator;
import org.thingsboard.server.actors.shared.rulechain.SystemRuleChainManager;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.VO.AlarmLinkedUser;
import org.thingsboard.server.common.data.alarm.*;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.utils.AlarmUtils;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.common.msg.TbActorMsg;
import org.thingsboard.server.dao.alarm.AlarmService;
import org.thingsboard.server.dao.alarmV2.AlarmCenterService;
import org.thingsboard.server.dao.attributes.AttributesService;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.DeviceLogEntity;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;
import org.thingsboard.server.dao.msg.JinzhouMsgSendService;
import org.thingsboard.server.dao.msgLog.DeviceLogService;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.station.StationAttrService;
import org.thingsboard.server.dao.station.StationService;
import org.thingsboard.server.dao.tenant.TenantService;
import org.thingsboard.server.dao.user.UserCredentialsDao;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.mail.MailExecutorService;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class AlarmActor extends RuleChainManagerActor {

    @Autowired
    private AttributesService attributesService;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private AlarmService alarmService;

    @Autowired
    private UserService userService;

    private ScheduledExecutorService scheduler;

    @Autowired
    private MailExecutorService mailExecutorService;

    @Autowired
    private JinzhouMsgSendService jinzhouMsgSendService;

    @Autowired
    private UserCredentialsDao userCredentialsDao;

    @Autowired
    private MailService mailService;

    @Autowired
    private AlarmCenterService alarmCenterService;

    @Autowired
    private StationService stationService;

    @Autowired
    private StationAttrService stationAttrService;

    private ProjectService projectService;

    private DeviceLogService deviceLogService;

    private ProjectRelationService projectRelationService;


    private List<ScheduledExecutorService> scheduledExecutorServices = new ArrayList<>();

    private AlarmActor(ActorSystemContext systemContext) {
        super(systemContext, new SystemRuleChainManager(systemContext));
        attributesService = systemContext.getAttributesService();
        tenantService = systemContext.getTenantService();
        deviceService = systemContext.getDeviceService();
        alarmService = systemContext.getAlarmService();
        userService = systemContext.getUserService();
        userCredentialsDao = systemContext.getUserCredentialsDao();
        mailService = systemContext.getMailService();
        mailExecutorService = systemContext.getMailExecutor();
        projectService = systemContext.getProjectService();
        deviceLogService = systemContext.getDeviceLogService();
        projectRelationService = systemContext.getProjectRelationService();


    }

    @Override
    public void preStart() {

    }


    public static class ActorCreator extends ContextBasedCreator<AlarmActor> {
        private static final long serialVersionUID = 1L;

        public ActorCreator(ActorSystemContext context) {
            super(context);
        }

        @Override
        public AlarmActor create() throws Exception {
            return new AlarmActor(context);
        }
    }

    @Override
    public void onReceive(Object msg) {
        super.onReceive(msg);
        log.info("接收到重置task请求，将在1分钟内重启task");
//        if (scheduledExecutorServices.size() > 0)
////            scheduledExecutorServices.forEach(scheduler -> {
////                scheduler.shutdownNow();
////            });
        if (scheduler != null)
            scheduler.shutdownNow();
        start();
    }

    private void start() {
        List<Tenant> tenants = tenantService.findAll();
        log.info("开启掉线报警检测ACTOR");
        scheduler = Executors.newSingleThreadScheduledExecutor();
        tenants.stream().forEach(tenant -> {
            try {
                Optional<AttributeKvEntry> attributeKvEntry = attributesService.find(tenant.getId(), tenant.getId(), DataConstants.SERVER_SCOPE, ModelConstants.ALARM_TIME).get();
                int defaultInterval = attributeKvEntry.isPresent() ? Integer.parseInt(attributeKvEntry.get().getValueAsString()) : 15;
                scheduler.scheduleAtFixedRate(() -> {
                    checkAlarm(tenant.getId(), 5 * 1000 * 60);
                }, 0, defaultInterval * 1000 * 60, TimeUnit.MILLISECONDS);
                // scheduledExecutorServices.add(scheduler);
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (ExecutionException e) {
                e.printStackTrace();
            }

        });
    }

    @Override
    protected boolean process(TbActorMsg msg) {

        return true;
    }


    @Override
    protected void broadcast(Object msg) {
        super.broadcast(msg);
    }


    /**
     * 检查是否掉线
     *
     * @param tenantId
     * @param time
     */
    private void checkAlarm(TenantId tenantId, long time) {
        List<Device> devices = deviceService.findAllByTenantId(tenantId).stream()
                .filter(device -> !device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_PORT)
                        && !device.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME)
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO))
                .collect(Collectors.toList());
        // V1报警
        devices.forEach(device -> {
            try {
                AlarmReport alarmReport = new AlarmReport();
                alarmReport.setDeviceName(device.getName());
                alarmReport.setTime(DateUtils.date2Str(System.currentTimeMillis(), DateUtils.DATE_FORMATE_DEFAULT_2));
                Optional<AttributeKvEntry> attributeKvEntry = attributesService.find(device.getTenantId(), device.getId(), DataConstants.SERVER_SCOPE, ModelConstants.LAST_UPDATE_TIME).get();
                long lastUpDateTime = attributeKvEntry.isPresent() ? attributeKvEntry.get().getLongValue().get() : System.currentTimeMillis() - DateUtils.DAY_TIME;
                //当设备上次上线时间距离现在超过15分组，触发掉线报警
                if (System.currentTimeMillis() - lastUpDateTime > time) {
                    //确认当前是否有未解除的掉线报警
                    List<Alarm> alarms = alarmService.findOnlineByTypeAndDevice(device.getId(), ModelConstants.ALARM_TYPE_OFFLINE).get();
                    if (alarms != null && alarms.size() > 0) {
                        try {
                            Alarm alarm = alarms.get(0);
                            if (alarm.getStatus() == AlarmStatus.RESTORE_ACK) {
                                alarm.setStatus(AlarmStatus.CONFIRM_UNACK);
                                ArrayNode objectNode = (ArrayNode) alarm.getDetails().get(DataConstants.ALARM_RECORDING);
                                objectNode.add(JacksonUtil.toJsonNode(new AlarmConf(System.currentTimeMillis(), "设备掉线", DataConstants.ALARM)));
                                ObjectNode details = (ObjectNode) alarm.getDetails();
                                details.put(DataConstants.ALARM_RECORDING, AlarmUtils.convertAlarm(objectNode));
                                alarm.setDetails(details);
                                alarmService.createOrUpdateAlarm(alarm);
                                sendEmailAndSms(alarm, device, alarmReport);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else {
                        Alarm alm = new Alarm();
                        alm.setStartTs(System.currentTimeMillis());
                        alm.setType(ModelConstants.ALARM_TYPE_OFFLINE);
                        alm.setOriginator(device.getId());
                        alm.setStatus(AlarmStatus.CONFIRM_UNACK);
                        alm.setTenantId(tenantId);
                        JsonNode jsonNode = JacksonUtil.toJsonNode(new AlarmType(ModelConstants.ALARM_TYPE_OFFLINE));
                        ObjectNode objectNode = (ObjectNode) jsonNode;
                        List<AlarmConf> confs = new ArrayList<>();
                        confs.add(new AlarmConf(System.currentTimeMillis(), "设备掉线", DataConstants.ALARM));
                        objectNode.put(DataConstants.ALARM_RECORDING, JacksonUtil.toJsonNode(confs));
                        alm.setDetails(objectNode);
                        alm.setSeverity("紧急");
                        Alarm result = alarmService.createOrUpdateAlarm(alm);
                        sendEmailAndSms(result, device, alarmReport);
                    }
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (ExecutionException e) {
                e.printStackTrace();
            }
        });
        // V2报警
        checkAlarmV2(devices, tenantId, time);
    }

    /**
     * 离线报警V2
     *
     * @param devices 设备列表
     * @param tenantId 租户ID
     * @param time 离线标准, 多久没收到数据判定为设备离线
     */
    private void checkAlarmV2(List<Device> devices, TenantId tenantId, long time) {
        List<StationEntity> stationList = stationService.findByTenantId(tenantId);
        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));
        devices.forEach(device -> {
            try {
                Optional<AttributeKvEntry> attributeKvEntry = attributesService.find(device.getTenantId(), device.getId(), DataConstants.SERVER_SCOPE, ModelConstants.LAST_UPDATE_TIME).get();
                long lastUpDateTime = attributeKvEntry.isPresent() ? attributeKvEntry.get().getLongValue().get() : System.currentTimeMillis() - DateUtils.DAY_TIME;
                if (System.currentTimeMillis() - lastUpDateTime > time) {
                    // 检查是否已存在报警
                    Integer count = alarmCenterService.countAlarmByAlarmRuleId(UUIDConverter.fromTimeUUID(device.getUuidId()));
                    if (count == 0) {// 未有离线告警
                        // 查询该设备相关站点
                        List<StationAttrEntity> stationAttrList = stationAttrService.findByDeviceId(device.getUuidId().toString());
                        List<String> stationIdList = stationAttrList.stream()
                                .map(StationAttrEntity::getStationId)
                                .distinct()
                                .collect(Collectors.toList());

                        Date now = new Date();
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        if (stationIdList.size() > 0) {
                            for (String stationId : stationIdList) {
                                StationEntity station = stationMap.get(stationId);
                                if (station == null) {
                                    continue;
                                }
                                AlarmCenter alarmCenter = new AlarmCenter();
                                alarmCenter.setStationId(stationId);
                                alarmCenter.setTitle(station.getName() + "的[" + device.getName() + "]" + "-离线");
                                alarmCenter.setTime(now);
                                alarmCenter.setAlarmType("离线告警");
                                alarmCenter.setAlarmLevel("3");// 紧急告警
                                alarmCenter.setAlarmInfo(station.getName() + "的[" + device.getName() + "]设备在" + dateFormat.format(now) + "监测到离线, 请及时查看处理!");
                                alarmCenter.setAlarmStatus(DataConstants.ALARMV2_ALARM_STATUS.NEW.getValue());// 报警中
                                alarmCenter.setProcessStatus(DataConstants.ALARMV2_PROCESS_STATUS.NEW.getValue());// 未处理
                                alarmCenter.setTenantId(UUIDConverter.fromTimeUUID(device.getTenantId().getId()));
                                alarmCenter.setAlarmRuleId(UUIDConverter.fromTimeUUID(device.getUuidId()));
                                alarmCenter.setDeviceId(UUIDConverter.fromTimeUUID(device.getUuidId()));

                                alarmCenterService.save(alarmCenter);
                                sendSms(alarmCenter, device.getTenantId());
                            }
                        }

                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }


    @Value("${msg.version}")
    private String msgVersion;

    private void sendSms(AlarmCenter alarmCenter, TenantId tenantId) {
        List<User> users = userService.findUserByTenant(tenantId);
        List<User> sendSmsUser = new ArrayList<>();
        users.forEach(user -> {
            //新增在进行报警时，先判断该用户是否已经激活
            if (userCredentialsDao.findByUserId(user.getTenantId(), user.getUuidId()) == null
                    || !userCredentialsDao.findByUserId(user.getTenantId(), user.getUuidId()).isEnabled()) {
                return;
            }
            try {
                Map info = JacksonUtil.fromString(user.getAdditionalInfo().asText(), Map.class);
                if (info.get(ModelConstants.ALARM_FORM_SMS) != null && info.get(ModelConstants.ALARM_FORM_SMS).equals(ModelConstants.ALARM_RELEASE_NOT)) {
                    sendSmsUser.add(user);
                }
            } catch (Exception e) {
                e.getLocalizedMessage();
            }

        });
        if ("jinzhou".equals(msgVersion)) {// 锦州短信
            mailExecutorService.execute(() -> {
                sendSmsUser.forEach(user -> {
                    if (user.getPhone()!=null) {
                        jinzhouMsgSendService.sendMsg(user.getPhone(), alarmCenter.getAlarmInfo());
                    }
                });
            });
        }

    }


    private void sendEmailAndSms(Alarm alarm, Device device, AlarmReport alarmReport) {
        List<User> users = userService.findUserByTenant(device.getTenantId());
        List<User> sendEmailUser = new ArrayList<>();
        List<User> sendSmsUser = new ArrayList<>();
        users.forEach(user -> {
            //新增在进行报警时，先判断该用户是否已经激活
            if (userCredentialsDao.findByUserId(user.getTenantId(), user.getUuidId()) == null
                    || !userCredentialsDao.findByUserId(user.getTenantId(), user.getUuidId()).isEnabled()) {
                return;
            }
            try {
                Map info = JacksonUtil.fromString(user.getAdditionalInfo().asText(), Map.class);
                if (info.get(ModelConstants.ALARM_FORM_EMAIL) != null && info.get(ModelConstants.ALARM_FORM_EMAIL).equals(ModelConstants.ALARM_RELEASE_NOT))
                    sendEmailUser.add(user);
                if (info.get(ModelConstants.ALARM_FORM_SMS) != null && info.get(ModelConstants.ALARM_FORM_SMS).equals(ModelConstants.ALARM_RELEASE_NOT))
                    sendSmsUser.add(user);
            } catch (Exception e) {
                e.getLocalizedMessage();
            }

        });
        String subject = "掉线报警提醒";
        String emailBody = getEmailBody(device, alarm);
        createDeviceLog(device);
        mailExecutorService.execute(() -> {
            sendEmailUser.forEach(user -> {
                if (user.getEmail() != null) {
                    AlarmLinkedUser alarmLinkedUser = AlarmLinkedUser.builder()
                            .userName(user.getName() + "(" + user.getName() + ")")
                            .email(user.getEmail())
                            .phone(user.getPhone())
                            .tenantId(user.getTenantId())
                            .build();
                    mailService.sendEmail(alarmLinkedUser, subject, emailBody, DataConstants.LOG_TYPE_DEVICE_OFFLINE);

                }
            });

        });
        mailExecutorService.execute(() -> {
            sendSmsUser.forEach(user -> {
                if (user.getPhone()!=null) {
                    mailService.sendOfflineSMS(user, alarmReport);
                }
            });
        });
    }


    public String getEmailBody(Device device, Alarm alarm) {
        String body = "您的设备" + device.getName() + "于" + DateUtils.date2Str(new Date(alarm.getStartTs()), DateUtils.DATE_FORMATE_DEFAULT) + "触发掉线报警，请尽快前往解除！";
        return body;
    }


    private void createDeviceLog(Device device) {
        List<ProjectEntity> projectEntity = projectRelationService.findProjectRelationByEntityTypeAndEntityId(DataConstants.ProjectRelationEntityType.DEVICE.name(), UUIDConverter.fromTimeUUID(device.getUuidId()));
        if (projectEntity != null && projectEntity.size() > 0) {
            DeviceLogEntity deviceLogEntity = DeviceLogEntity.builder()
                    .deviceName(device.getName())
                    .status(DataConstants.DEVICE_LOG_OFFLINE)
                    .updateTime(System.currentTimeMillis())
                    .projectId(projectEntity.get(0).getId())
                    .tenantId(UUIDConverter.fromTimeUUID(device.getTenantId().getId()))
                    .deviceId(UUIDConverter.fromTimeUUID(device.getUuidId()))
                    .projectName(projectEntity.get(0).getName())
                    .build();
            deviceLogService.save(deviceLogEntity);
        }

    }

}
