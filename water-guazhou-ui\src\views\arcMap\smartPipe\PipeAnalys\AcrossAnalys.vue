<!-- 横剖面分析 -->
<template>
  <div ref="refBox" class="across-page">
    <RightDrawerMap
      ref="refMap"
      :title="'横剖面分析'"
      :full-content="true"
      @map-loaded="onMapLoaded"
      @detail-refreshed="state.curOperate = 'analysed'"
    >
      <Form ref="refForm" :config="FormConfig"> </Form>
      <Panel
        v-if="state.mounted"
        ref="refChartPanel"
        :telport="refBox"
        custom-class="gis-across-analys-panel"
        title="横剖面分析结果"
      >
        <div class="chart-box">
          <VChart ref="refChart" :option="state.chartOption"></VChart>
        </div>
      </Panel>
    </RightDrawerMap>
  </div>
</template>
<script lang="ts" setup>
import { max, min } from 'lodash-es';
import Graphic from '@arcgis/core/Graphic';
import { generate4548Graphic, getGraphicLayer } from '@/utils/MapHelper';
import { HorizentalSectionAnalysis } from '@/api/mapservice/pipeAnalys';
import { useAppStore, useGisStore } from '@/store';
import { SLMessage } from '@/utils/Message';
import { IECharts } from '@/plugins/echart';
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';
import { useSketch } from '@/hooks/arcgis';

const refBox = ref<HTMLDivElement>();
const refChartPanel = ref<IPanelIns>();
const refChart = ref<IECharts>();
const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const state = reactive<{
  mounted: boolean;
  curOperate:
    | 'picking'
    | 'picked'
    | 'analysing'
    | 'analysed'
    | 'detailing'
    | 'viewingDetail'
    | '';
  tabs: { label: string; name: string; data?: any }[];
  chartOption: any;
}>({
  mounted: false,
  curOperate: '',
  tabs: [],
  chartOption: null
});
const staticState: {
  view?: __esri.MapView;
  sketch?: __esri.SketchViewModel;
  geometry?: __esri.Polyline;
  graphicsLayer?: __esri.GraphicsLayer;
  soeResult: {
    Depth: number;
    Diameter: number;
    Distance: number;
    FromPointDefinition: string;
    FromPointNameDefinition: string;
    ID: string;
    Material: string;
    PipeLineDefinition: string;
    PipeLineNameDefinition: string;
    PipePointDefinition: string;
    PipePointNameDefinition: string;
    ToPointDefinition: string;
    ToPointNameDefinition: string;
    X: number;
    Y: number;
    Z: number;
    ZGround: number;
  }[];
  pipeInfo: {
    ID: string[];
    ZGround: number[];
    Distance: number[];
    Depth: number[];
    Diameter: number[];
    Z: number[];
    Material: string[];
  };
} = {
  soeResult: [],
  pipeInfo: {
    ID: [],
    ZGround: [],
    Distance: [],
    Depth: [],
    Diameter: [],
    Z: [],
    Material: []
  }
};
const refForm = ref<IFormIns>();
const TableConfig = reactive<ITable>({
  columns: [
    { label: '设备类型', prop: 'name' },
    { label: '数量', prop: 'count', formatter: (row) => row.data?.length }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制剖面线'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'warning',
              loading: () => state.curOperate === 'analysing',
              disabled: () => state.curOperate === 'analysing',
              text: () =>
                state.curOperate === 'picking'
                  ? '正在绘制剖面线'
                  : state.curOperate === 'analysing'
                    ? '分析中'
                    : '点击绘制剖面线',
              click: () => startDraw(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '分析结果'
      },
      fields: [
        {
          type: 'table',
          style: {
            height: '250px'
          },
          config: TableConfig
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginTop: '20px',
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              type: 'success',
              text: () =>
                state.curOperate === 'analysing' ? '正在分析...' : '开始分析',
              loading: () => state.curOperate === 'analysing',
              disabled: () => state.curOperate === 'picking',
              click: () => startAnalys(),
              styles: {
                width: '100%'
              }
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '详细信息',
              disabled: () =>
                state.curOperate !== 'viewingDetail' &&
                state.curOperate !== 'analysed',
              loading: () => state.curOperate === 'detailing',
              click: () => viewDetail(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
});
const startDraw = async () => {
  if (!staticState.view) return;
  clearGraphicsLayer();
  refChartPanel.value?.Close();
  state.curOperate = 'picking';
  staticState.sketch?.create('polyline');
};
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll();
  refMap.value?.clearDetailData();
};

const startAnalys = async () => {
  if (!staticState.view) {
    SLMessage.error('地图服务未就绪，请稍候再试');
    return;
  }
  if (!staticState.geometry?.paths?.length) {
    SLMessage.warning('请先绘制剖面线');
    return;
  }
  state.curOperate = 'analysing';
  try {
    const g = new Graphic({
      geometry: staticState.geometry
    });
    const submitG =
      window.SITE_CONFIG.SITENAME === 'qingyang' ? generate4548Graphic(g) : g;
    const geo = submitG.geometry as __esri.Polyline;
    const res = await HorizentalSectionAnalysis({
      UserToken: useGisStore().gToken || '',
      X1: geo.paths[0][0][0],
      X2: geo.paths[0][1][0],
      Y1: geo.paths[0][0][1],
      Y2: geo.paths[0][1][1],
      f: 'pjson'
    });

    staticState.soeResult = res.data?.Values || [];
    if (res.data.Status !== 'successed') {
      SLMessage.error('分析失败');
      state.curOperate = '';
      return;
    }
    state.tabs = [];

    staticState.pipeInfo.ID = [];
    staticState.pipeInfo.ZGround = [];
    staticState.pipeInfo.Distance = [];
    staticState.pipeInfo.Depth = [];
    staticState.pipeInfo.Diameter = [];
    staticState.pipeInfo.Z = [];
    staticState.pipeInfo.Material = [];
    staticState.soeResult.map((item) => {
      const nameoid = (
        item.PipeLineNameDefinition || item.PipePointNameDefinition
      )?.split(':');
      let layername = '';
      let oid = '';
      if (nameoid.length === 2) {
        layername = nameoid[0];
        oid = nameoid[1];
        const tab = state.tabs.find((l) => l.label === layername);
        if (!tab) {
          state.tabs.push({ label: layername, name: layername, data: [oid] });
        } else {
          tab.data.push(oid);
        }
      }
      staticState.pipeInfo.ID.push(item.ID);
      staticState.pipeInfo.ZGround.push(item.ZGround);
      staticState.pipeInfo.Distance.push(item.Distance);
      staticState.pipeInfo.Depth.push(item.Depth);
      staticState.pipeInfo.Diameter.push(item.Diameter);
      staticState.pipeInfo.Z.push(item.Z);
      staticState.pipeInfo.Material.push(item.Material);
    });

    if (!state.tabs.length) {
      state.curOperate = 'analysed';
      SLMessage.warning('无相关数据');
      return;
    }
    TableConfig.dataList = state.tabs;
    refChart.value?.clear();
    state.chartOption = null;
    refChartPanel.value?.Open();
    state.chartOption = initChartOption();
    nextTick(() => {
      refChart.value?.resize();
    });
  } catch (error) {
    SLMessage.error('系统错误');
    state.curOperate = '';
  }
  state.curOperate = 'analysed';
};
const initChartOption = () => {
  const lineData: any[] = [];
  const points = staticState.pipeInfo.ZGround.map((item, i) => {
    const placePoint = [
      staticState.pipeInfo.Distance[i],
      staticState.pipeInfo.ZGround[i]
    ];
    lineData.push(placePoint);
    return [staticState.pipeInfo.Distance[i], staticState.pipeInfo.Z[i]];
  });
  const diameterMax = max(staticState.pipeInfo.Diameter) || 0;
  const diameterMin = min(staticState.pipeInfo.Diameter) || 0;
  const symbolSizeMin = 5;
  const symbolSizeMax = 10;
  const option = {
    legend: {
      // selectedMode: false,
      textStyle: {
        color: useAppStore().isDark ? '#fff' : ''
      }
    },
    toolbox: {
      show: true,
      feature: {
        // mark: { show: true },
        // restore: { show: true },
        saveAsImage: { show: true, title: '保存为图片' }
      }
    },
    dataZoom: [
      {
        show: true,
        type: 'inside',
        start: 0,
        end: 100,
        textStyle: {
          color: '#666'
        }
      },
      {
        start: 0,
        end: 100
      }
    ],
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        if (params.seriesName === '地面') {
          return (
            '地面点高程：' +
            staticState.pipeInfo.ZGround[params.dataIndex].toFixed(2)
          );
        }
        if (params[0].seriesName === '管点') {
          return (
            '管径：' +
            staticState.pipeInfo.Diameter[params[0].dataIndex] +
            '<br/>' +
            '材质: ' +
            staticState.pipeInfo.Material[params[0].dataIndex] +
            '<br/>' +
            '埋深: ' +
            Number(staticState.pipeInfo.Depth[params[0].dataIndex].toFixed(2)) /
              8
          );
        }
      }
    },
    xAxis: {
      type: 'value', // 坐标类型
      // data: xAxisData,
      show: true, // 是否显示
      position: 'bottom', // 位置
      name: '距离', // 坐标轴的名称
      nameLocation: 'end', // 坐标轴名称位置
      nameTextStyle: {}, //
      boundaryGap: [0, 0],
      min: null,
      max: null,
      color: '#A9D2E1',
      axisLabel: {
        formatter: '{value}m',
        textStyle: {
          color: '#666'
        }
      },
      splitLine: {
        show: false,
        color: '#00ff00'
      },
      splitArea: {
        show: false
      }
    },
    yAxis: {
      name: '高程',
      type: 'value',
      scale: true,
      color: '#A9D2E1',
      axisLabel: {
        formatter: '{value}m',
        textStyle: {
          color: '#666'
        }
      },
      splitArea: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#FFFFFF',
          opacity: 0.2,
          type: 'dashed'
        }
      }
    },
    grid: {
      left: 100
    },
    series: [
      {
        name: '管点',
        type: 'scatter',
        tooltip: {
          trigger: 'axis'
        },

        legendHoverLink: true,
        symbol: 'emptyCircle',
        symbolSize(value) {
          let index;
          for (let i = 0; i < points.length; i++) {
            if (points[i][1] === value[1]) {
              index = i;
            }
          }
          // 优化管径展示
          if (diameterMax === diameterMin) {
            return symbolSizeMin;
          }
          return (
            symbolSizeMin +
            ((staticState.pipeInfo.Diameter[index] - diameterMin) /
              (diameterMax - diameterMin)) *
              (symbolSizeMax - symbolSizeMin)
          );
        },
        // 点对应的文本
        label: {
          show: true,
          formatter: (params) => {
            //
            return staticState.pipeInfo.ID[params.dataIndex];
          },
          position: 'bottom',
          textStyle: {
            color: '#fff',
            align: 'right',
            baseline: 'bottom',
            fontSize: '10px'
          }
        },
        itemStyle: {
          color: 'red',
          borderWidth: 2,
          borderColor: '#070707'
        },
        emphasis: {
          utenStyle: {
            color: '#aa0000',
            borderWidth: 2,
            borderColor: '#070707'
          }
        },
        data: points
      },

      {
        name: '地面',
        type: 'line',
        clickable: true, // 数据图形是否可点击，默认开启，如果没有click事件响应可以关闭
        // itemStyle: null,
        data: lineData,
        markePoint: {},

        itemStyle: {
          lineStyle: { width: 2, color: '#aaaaaa' }, // 线样式
          areaStyle: { color: '#AE6F39', type: 'default' }
        },
        label: {
          show: true,
          formatter: (params) => {
            if (
              params.data[0] <
                staticState.pipeInfo.Distance[params.dataIndex - 1] + 5 &&
              params.dataIndex > 0
            ) {
              const blank = '';
              return blank;
            }
            return params.data[1].toFixed(1);
          }
        },
        markeLine: {
          data: [{ type: 'average', name: '平均高程' }]
        },

        stack: null, //
        xAxisIndexs: 0,
        yAxisIndex: 0,
        symbol: null, // 'circle' | 'rectangle' | 'triangle' | 'diamond' |'emptyCircle' | 'emptyRectangle' | 'emptyTriangle' | 'emptyDiamond'
        symbolSize: 2 | 4,
        symbolRotate: null, // 标志图形旋转角度[-180,180]
        showAllSymbol: false,
        // smooth: true, //smooth为true时lineStyle不支持虚线
        dataFilter: 'nearst', // 'nearest', 'min', 'max', 'average'
        legendHoverLink: true // 是否启用图例（legend）hover时的联动响应（高亮显示）
      }
    ]
  };
  return option;
};

const viewDetail = () => {
  state.curOperate = 'detailing';
  refMap.value?.refreshDetail(state.tabs);
};
const { initSketch, destroySketch } = useSketch();
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    staticState.geometry = result?.graphics[0]?.geometry as __esri.Polyline;
    state.curOperate = 'picked';
  }
};
const onMapLoaded = (view) => {
  state.mounted = true;
  staticState.view = view;
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-analys-across',
    title: '横剖面分析'
  });
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  });
};
watch(
  () => useAppStore().isDark,
  () => {
    initChartOption();
  }
);
onBeforeUnmount(() => {
  refChartPanel.value?.Close();
  staticState.graphicsLayer?.removeAll();
  destroySketch();
});
</script>
<style lang="scss" scoped>
.chart-box {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.gis-across-analys-panel {
  width: 600px;
  height: 450px;
  position: absolute;
  left: calc(50% - 300px);
  top: 150px;
  overflow: hidden;
}
.across-page {
  width: 100%;
  height: 100%;
}
</style>
