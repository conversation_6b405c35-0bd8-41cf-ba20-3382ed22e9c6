package org.thingsboard.server.dao.sql.plan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.plan.PlanDetail;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanDetailPageRequest;

import java.util.List;

@Mapper
public interface PlanDetailMapper extends BaseMapper<PlanDetail> {
    IPage<PlanDetail> findByPage(PlanDetailPageRequest request);

    boolean update(PlanDetail detail);

    boolean saveAll(List<PlanDetail> list);

    boolean updateAll(List<PlanDetail> list);

    int deleteAllByMainId(@Param("id") String id, @Param("idList") List<String> idList);

}
