<template>
  <div class="main">
    <div class="card zutai-card">
      <!-- <FramePage
        :url="''"
        :token-field="'token'"
        :disable-full-screen="true"
        ></FramePage> -->
      <!-- <div class="flow-line">
        <div
          class="dot"
          v-for="item in 100"
          :style="{ left: item * 4 + '%', animationDelay: item * 0.1 + 's' }"
        ></div>
      </div> -->
      <!-- 提升泵房 -->
      <div style="position: relative; width: 100%; height: 100%">
        <img
          class="sprite-line"
          src="../../imgs/原水04.gif"
          alt=""
        />
        <img
          class="sprite-line"
          src="../../imgs/原水02.gif"
          alt=""
        />
        <img
          class="sprite-line"
          src="../../imgs/原水03.gif"
          alt=""
        />
        <img
          class="sprite-line"
          src="../../imgs/原水01.gif"
          alt=""
        />
        <!-- 网格池开关阀 -->
        <img
          class="sprite-line"
          src="../../imgs/沉淀02.gif"
          alt=""
        />
        <img
          class="sprite-line"
          src="../../imgs/沉淀03.gif"
          alt=""
        />
        <img
          class="sprite-line"
          src="../../imgs/沉淀01.gif"
          alt=""
        />
        <!-- 加药间 -->
        <img
          class="sprite-line"
          src="../../imgs/加药01.gif"
          alt=""
        />
        <img
          class="sprite-line"
          src="../../imgs/加药02.gif"
          alt=""
        />
        <!-- 消毒间 -->
        <img
          class="sprite-line"
          src="../../imgs/消毒03.gif"
          alt=""
        />
        <img
          class="sprite-line"
          src="../../imgs/消毒02.gif"
          alt=""
        />
        <img
          class="sprite-line"
          src="../../imgs/消毒01.gif"
          alt=""
        />
        <!-- 清水池 -->
        <img
          class="sprite-line"
          src="../../imgs/清水02.gif"
          alt=""
        />
        <img
          class="sprite-line"
          src="../../imgs/清水01.gif"
          alt=""
        />
        <img
          class="sprite-line"
          src="../../imgs/清水03.gif"
          alt=""
        />
      </div>
      <div
        style="
          position: absolute;
          top: 7%;
          left: 1%;
          color: #00f8ff;
          font-weight: 600;
        "
      >
        去周河高位水池
      </div>

      <div
        style="
          position: absolute;
          top: 11%;
          left: 4%;
          color: #00f8ff;
          font-weight: 600;
        "
      >
        水库来水
      </div>

      <div
        style="
          position: absolute;
          top: 21%;
          right: 6%;
          color: #00f8ff;
          font-weight: 600;
        "
      >
        自流管道
      </div>
      <div class="card-content sk" style="">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">水库</span>
        </div>
        <!-- <div class="row">
          <div class="label">水位：</div>
          <div class="value">
            {{ realTimeValue['水库出水口水位监测']?.level }}
          </div>
        </div> -->
      </div>

      <div class="card-content psj" style="">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">配水井</span>
        </div>
        <!-- <div class="row">
          <div class="label">1#瞬时流量：</div>
          <div class="value">
            {{ realTimeValue['进水流量计1']?.Instantaneous_flow }}
          </div>
        </div>
        <div class="row">
          <div class="label">1#压力:</div>
          <div class="value">
            {{ realTimeValue['原水池提升泵压力1']?.pressure }}
          </div>
        </div>
        <div class="row">
          <div class="label">2#瞬时流量：</div>
          <div class="value">
            {{ realTimeValue['进水流量计2']?.Instantaneous_flow }}
          </div>
        </div>
        <div class="row">
          <div class="label">2#压力:</div>
          <div class="value">
            {{ realTimeValue['原水池提升泵压力2']?.pressure }}
          </div>
        </div>
        <div class="row">
          <div class="label">3#瞬时流量:</div>
          <div class="value">
            {{ realTimeValue['进水流量计3']?.Instantaneous_flow }}
          </div>
        </div>
        <div class="row">
          <div class="label">3#压力:</div>
          <div class="value">
            {{ realTimeValue['原水池提升泵压力3']?.pressure }}
          </div>
        </div> -->
      </div>
      <div class="card-content" style="top: 45%; left: 13%; width: 140px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">沉淀池1</span>
        </div>
        <!-- <div class="row">
          <div class="label">水位:</div>
          <div class="value">{{ realTimeValue['网格反应池液位1']?.level }}</div>
        </div> -->
      </div>
      <!-- 沉淀池1 -->
      <div class="status" style="bottom: 33%; left: 18%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="card-content" style="top: 33%; left: 15%; width: 120px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">加药间</span>
        </div>
      </div>
      <div class="status" style="top: 30%; left: 20%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="status" style="top: 30%; left: 18%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="card-content" style="top: 45%; left: 23%; width: 140px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">沉淀池2</span>
        </div>
        <!-- <div class="row">
          <div class="label">水位:</div>
          <div class="value">{{ realTimeValue['网格反应池液位2']?.level }}</div>
        </div> -->
      </div>

      <!-- 沉淀池2 -->
      <div class="status" style="bottom: 33%; left: 28%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="card-content" style="top: 80%; left: 9%; width: 120px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">消毒间</span>
        </div>
      </div>
      <div class="status" style="bottom: 9%; left: 12.5%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="status" style="bottom: 14%; left: 12.8%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="card-content" style="bottom: 22%; left: 17%; width: 120px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">无阀滤池</span>
        </div>
      </div>

      <div class="card-content" style="top: 20%; left: 40%; width: 140px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">原水池1</span>
        </div>
        <!-- <div class="row">
          <div class="label">水位:</div>
          <div class="value">{{ realTimeValue['原水池液位1']?.level }}</div>
        </div> -->
      </div>

      <div class="card-content" style="top: 52%; left: 40%; width: 140px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">原水池2</span>
        </div>
        <!-- <div class="row">
          <div class="label">水位:</div>
          <div class="value">{{ realTimeValue['原水池液位2']?.level }}</div>
        </div> -->
      </div>

      <div class="card-content" style="top: 11%; left: 29%; width: 120px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">提升泵室</span>
        </div>
      </div>
      <!-- 提升泵状态 -->
      <div class="status" style="top: 15%; left: 32.9%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="status" style="top: 18%; left: 32.7%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="status" style="top: 21%; left: 32.5%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="card-content" style="top: 23%; left: 32%; width: 120px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">进水口</span>
        </div>
        <!-- <div class="row">
          <div class="label">流量:</div>
          <div class="value">
            {{ realTimeValue['进水流量']?.Instantaneous_flow }}
          </div>
        </div>

        <div class="row">
          <div class="label">浊度:</div>
          <div class="value">{{ realTimeValue['进水水质']?.turbidity }}</div>
        </div>

        <div class="row">
          <div class="label">PH:</div>
          <div class="value">{{ realTimeValue['进水水质']?.ph }}</div>
        </div> -->
      </div>

     <!-- <div class="card-content" style="top: 3%; right: 21%; width: 180px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">生活用水</span>
        </div>
         <div class="row">
          <div class="label">顺时流量:</div>
          <div class="value">
            {{ realTimeValue['清水池出水流量']?.Instantaneous_flow }}
          </div>
        </div>
        <div class="row">
          <div class="label">压力</div>
          <div class="value">
            {{ realTimeValue['清水池出水压力1']?.pressure }}
          </div>
        </div>
      </div> -->

     <!-- <div class="card-content" style="top: 3%; right: 12%; width: 150px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">出水水质</span>
        </div>
         <div class="row">
          <div class="label">余氯:</div>
          <div class="value">{{ realTimeValue['清水池']?.remainder }}</div>
        </div>
        <div class="row">
          <div class="label">浊度:</div>
          <div class="value">
            {{ realTimeValue['清水池']?.turbidity }}
          </div>
        </div>
        <div class="row">
          <div class="label">PH:</div>
          <div class="value">{{ realTimeValue['清水池']?.ph }}</div>
        </div> 
      </div>-->
      <!-- 出水泵状态 -->
      <div class="status" style="top: 28%; right: 15.5%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="status" style="top: 28%; right: 14%">
        <img
          :src="run"
          style="width: 15px; height: 15px"
        />
      </div>
      <div class="card-content" style="top: 43%; right: 12%; width: 120px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">出水泵室</span>
        </div>
      </div>

      <!--<div class="card-content" style="top: 3%; right: 1%; width: 180px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">消防管道</span>
        </div>
         <div class="row">
          <div class="label">顺时流量:</div>
          <div class="value">
            {{ realTimeValue['清水池出水流量计2']?.Instantaneous_flow }}
          </div>
        </div>
        <div class="row">
          <div class="label">压力</div>
          <div class="value">
            {{ realTimeValue['清水池出水压力2']?.pressure }}
          </div>
        </div> 
      </div>-->
      <div class="card-content zh" style="">
        <div class="card-title" style="width: 120px">
          <span style="color: #d8feff; text-align: center">周河村高位水池</span>
        </div>
        <!-- <div class="row">
          <div class="label">水位:</div>
          <div class="value">
            {{ realTimeValue['周河村高位水池']?.level }}
          </div>
        </div> -->
      </div>

      <div class="card-content" style="top: 76%; right: 9%; width: 140px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">清水池1</span>
        </div>
        <!-- <div class="row">
          <div class="label">水位:</div>
          <div class="value">{{ realTimeValue['清水池液位1']?.level }}</div>
        </div> -->
      </div>

      <div class="card-content" style="top: 60%; right: 10%; width: 140px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">清水池2</span>
        </div>
        <!-- <div class="row">
          <div class="label">水位:</div>
          <div class="value">{{ realTimeValue['清水池液位2']?.level }}</div>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getDatasList } from '@/api/device';
import { removeSlash } from '@/utils/removeIdSlash';

import { useBusinessStore } from '@/store';
import stop from '../../imgs/水泵_停止.png';
import run from '../../imgs/水泵_在线.gif';
import { useRouter } from 'vue-router';
const businessStore = useBusinessStore();
const realTimeValue = ref<any>({});
const Interval = ref<any>();
const refreshData = (devices: any) => {
  devices.forEach((devcie) => {
    console.log('ID', removeSlash(devcie.id.id));
    getDatasList(removeSlash(devcie.id.id), {
      page: 1,
      size: 9999999
    }).then((res) => {
      const data = res.data.data;
      realTimeValue.value[devcie.name] = {};
      data.map((item) => {
        realTimeValue.value[devcie.name][item.property] =
          `${item.value}${item.unit || ''}`;
      });
    });
  });

  // realTimeValue.value[devcie.name] = res.data.data?.map(item => {
  //   return {
  //     [item.property]: `${item.value}${item.unit}`,
  //   }
  // })
  console.log(realTimeValue.value);
};

const getValue = (property, deviceId) => {
  return realTimeValue.value.find(
    (item) => item.property === property && item.deviceId === deviceId
  );
};
const initData = () => {
  console.log(businessStore.projectList);
  const projectId = businessStore.projectList[0].id as string;

};
/** 浏览器窗口监听（当点击审核跳转其他系统后，数据被审批完了，再次从浏览器窗口切换，请求接口） */
// const visibilitychange = () => {
//   document.addEventListener('visibilitychange', function () {
//     if (document.visibilityState === 'hidden') {
//       clearInterval(Interval.value);
//     } else {
//       initData();
//       Interval.value = setInterval(() => {
//         initData();
//       }, 30000); // 查询方法 - 方法中写调用接口代码
//     }
//   });
// };

// 声明 router
const router = useRouter();

// 监听路由变化
watch(
  () => router.currentRoute.value.path,
  (newValue, oldValue) => {
    console.log('新路由', newValue);
    console.log('旧路由', oldValue);
    if (oldValue != undefined) {
      if (newValue == '/home' || newValue == '/SCSC01/SCSC010301') {
        if (oldValue !== '/home' && oldValue != '/SCSC01/SCSC010301') {
          initData();
          Interval.value = setInterval(() => {
            initData();
          }, 30000);
        }
      } else {
        console.log('清除定时器');
        clearInterval(Interval.value);
      }
    }
  },
  { immediate: true }
);
onMounted(() => {
  initData();
  Interval.value = setInterval(() => {
    initData();
  }, 30000);
});

onBeforeUnmount(() => {
  console.log('清除定时器');
  clearInterval(Interval.value);
});
</script>
<style lang="scss" scoped>

.main {
  width: 100%;
  height: 100%;
  position: relative;
  background: rgb(3, 17, 45);

  .content {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 20px;
    padding-top: 81px;
    color: rgba(184, 210, 255, 1);
  }

  .card {
    position: absolute;
  }

  .zutai-card {
    width: 100%;
    height: 100%;
    border: 1px solid rgba(0, 191, 252, 0.4);
    background: url(../../imgs/总工艺图.png) 0 0 /100% 100% no-repeat;
  }

  .cqjj-card {
    width: 745px;
    height: 315px;
    top: 746px;
    left: 26px;
  }

  .szjc-card {
    width: 994px;
    height: 315px;
    top: 746px;
    left: 783px;
  }

  .ssqx-card {
    width: 738px;
    height: 315px;
    top: 105px;
    left: 1790px;
  }

  .nhjc-card {
    width: 738px;
    height: 315px;
    top: 436px;
    left: 1790px;
  }

  .rgsl-card {
    width: 738px;
    height: 294px;
    top: 767px;
    left: 1790px;
  }

  .card-content {
    position: absolute;
    width: 190px;
    border-radius: 4px;
    padding: 0 5px 10px 5px;
    box-shadow: 0px 0px 8px 0px #00d9ff inset;
    background: linear-gradient(
      rgba(30, 105, 158, 0.1) 0%,
      rgba(30, 105, 158, 0.6) 100%
    );

    .card-title {
      // transform: translateX(-50%);
      font-size: 12px;
      line-height: 18px;
      width: 93px;
      height: 18px;
      margin: 0 auto;
      text-align: center;
      border-radius: 0 0 10px 10px;
      background: linear-gradient(
        180deg,
        rgba(116, 229, 255, 0.2) 0%,
        rgba(16, 229, 255, 0.6) 100%
      );
    }

    .row {
      display: flex;
      font-size: 12px;
      color: #d8feff;
      padding: 6px 10px 0px 10px;

      .value {
        padding-left: 4px;
      }
    }
  }
  .psj {
    top: 25%;
    left: 17%;
  }
  .zh {
    top: 1%;
    left: 8%;
    width: 140px;
  }
  .sk {
    top: 14%;
    left: 7%;
    width: 140px;
  }
  // .card-content::before {
  //   content: '';
  //   transform: translateX(-50%);
  //   width: 100px;
  //   height: 10px;
  //   background-color: rgba(173, 216, 230, 0.8); /* 顶部的浅蓝色区域 */
  //   border-radius: 0 0 10px 10px;
  // }
  .sprite-line {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
  }
}
.status {
  width: 15px;
  height: 15px;
  position: absolute;
}
// .flow-line {
//   position: relative;
//   width: 72%;
//   height: 4px; /* 线条高度 */
//   top: 11%;
//   left: 10%;
//   background: #333; /* 线条颜色 */
//   overflow: hidden; /* 隐藏超出部分 */
// }

// .dot {
//   position: absolute;
//   width: 16px;
//   height: 6px;
//   background: #0f0; /* 点的颜色 */
//   animation: flow 1s linear infinite; /* 快速滚动 */
// }

// @keyframes flow {
//   0% {
//     transform: translateX(0); /* 从起始位置开始 */
//     opacity: 1;
//   }
//   80% {
//     transform: translateX(0); /* 从起始位置开始 */
//     opacity: 0;
//   }
//   100% {
//     transform: translateX(100%); /* 向左滚动至外部 */
//     opacity: 0;
//   }
// }
</style>
