import{aO as c,b3 as m,b4 as s}from"./MapView-DaoQedLH.js";import{v as i,y as b,x as p}from"./quat-CM9ioDFt.js";import{e as a}from"./quatf64-QCogZAoR.js";function u(r=$){return[r[0],r[1],r[2],r[3]]}function k(r,n,t=u()){return c(t,r),t[3]=n,t}function l(r,n,t=u()){return i(o,r,e(r)),i(f,n,e(n)),b(o,f,o),x(t,m(p(t,o)))}function w(r){return r}function y(r){return r[3]}function e(r){return s(r[3])}function x(r,n){return r[3]=n,r}const $=[0,0,1,0],o=a(),f=a();u();export{u as a,w as g,k,y as l,l as v,e as x};
