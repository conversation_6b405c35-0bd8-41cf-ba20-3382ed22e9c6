package org.thingsboard.server.dao.response.workOrder;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class WorkOrderCollaborationEntityResponse {
    // id
    private String id;

    // 需要协作的工单ID
    private String orderId;

    // 协作工单发起类型。直接发起工单协作/申请协作
    private String type;

    // 申请原因
    private String remark;

    // 申请人ID
    private String userId;

    // 申请时间
    private String time;

    // 状态。待审核/通过/未通过
    private String status;

    // 新协作工单元数据。JSON格式的需要生成的新的协作工单的数据。
    private String additionalInfo;

    // 租户ID
    private String tenantId;
}
