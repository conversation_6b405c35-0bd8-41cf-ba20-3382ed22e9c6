/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.role;

import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.RoleId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.role.MenuRole;
import org.thingsboard.server.dao.model.sql.MenuRoleEntity;
import org.thingsboard.server.dao.role.MenuRoleDao;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;
import java.util.UUID;

@Component
@SqlDao
@Slf4j
public class JpaMenuRoleDao extends JpaAbstractDao<MenuRoleEntity, MenuRole> implements MenuRoleDao {

    @Autowired
    private MenuRoleRepository menuRoleRepository;

    @Override
    protected void setSearchText(MenuRoleEntity entity) {
        super.setSearchText(entity);
    }

    @Override
    public MenuRole save(MenuRole domain) {
        return super.save(domain);
    }

    @Override
    public MenuRole findById(UUID key) {
        return super.findById(key);
    }

//    @Override
//    public ListenableFuture<MenuRole> findByIdAsync(UUID key) {
//        return super.findByIdAsync(key);
//    }

    @Override
    public boolean removeById(UUID id) {
        return super.removeById(id);
    }

    @Override
    public List<MenuRole> find() {
        return super.find();
    }

    @Override
    protected Class<MenuRoleEntity> getEntityClass() {
        return MenuRoleEntity.class;
    }

    @Override
    protected CrudRepository<MenuRoleEntity, String> getCrudRepository() {
        return menuRoleRepository;
    }

    @Override
    public List<String> findByUserId(UserId id) {
        return menuRoleRepository.findByUserId(UUIDConverter.fromTimeUUID(id.getId()));
    }

    @Override
    public List<String> findByRoleId(RoleId roleId) {
        return menuRoleRepository.findByRoleId(UUIDConverter.fromTimeUUID(roleId.getId()));
    }

    @Override
    public void deleteByRoleId(RoleId roleId) {
        menuRoleRepository.deleteByRoleId(UUIDConverter.fromTimeUUID(roleId.getId()));
    }
}
