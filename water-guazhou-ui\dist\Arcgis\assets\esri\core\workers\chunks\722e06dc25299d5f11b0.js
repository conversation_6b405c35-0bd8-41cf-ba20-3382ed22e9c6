"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[3660],{92835:(e,t,s)=>{s.d(t,{Z:()=>f});var i,r=s(43697),n=s(96674),o=s(70586),a=s(35463),l=s(5600),u=(s(75215),s(67676),s(71715)),h=s(52011),c=s(30556);let d=i=class extends n.wq{static get allTime(){return p}static get empty(){return _}constructor(e){super(e),this.end=null,this.start=null}readEnd(e,t){return null!=t.end?new Date(t.end):null}writeEnd(e,t){t.end=e?e.getTime():null}get isAllTime(){return this.equals(i.allTime)}get isEmpty(){return this.equals(i.empty)}readStart(e,t){return null!=t.start?new Date(t.start):null}writeStart(e,t){t.start=e?e.getTime():null}clone(){return new i({end:this.end,start:this.start})}equals(e){if(!e)return!1;const t=(0,o.pC)(this.start)?this.start.getTime():this.start,s=(0,o.pC)(this.end)?this.end.getTime():this.end,i=(0,o.pC)(e.start)?e.start.getTime():e.start,r=(0,o.pC)(e.end)?e.end.getTime():e.end;return t===i&&s===r}expandTo(e){if(this.isEmpty||this.isAllTime)return this.clone();const t=(0,o.yw)(this.start,(t=>(0,a.JE)(t,e))),s=(0,o.yw)(this.end,(t=>{const s=(0,a.JE)(t,e);return t.getTime()===s.getTime()?s:(0,a.Nm)(s,1,e)}));return new i({start:t,end:s})}intersection(e){if(!e)return this.clone();if(this.isEmpty||e.isEmpty)return i.empty;if(this.isAllTime)return e.clone();if(e.isAllTime)return this.clone();const t=(0,o.R2)(this.start,-1/0,(e=>e.getTime())),s=(0,o.R2)(this.end,1/0,(e=>e.getTime())),r=(0,o.R2)(e.start,-1/0,(e=>e.getTime())),n=(0,o.R2)(e.end,1/0,(e=>e.getTime()));let a,l;if(r>=t&&r<=s?a=r:t>=r&&t<=n&&(a=t),s>=r&&s<=n?l=s:n>=t&&n<=s&&(l=n),null!=a&&null!=l&&!isNaN(a)&&!isNaN(l)){const e=new i;return e.start=a===-1/0?null:new Date(a),e.end=l===1/0?null:new Date(l),e}return i.empty}offset(e,t){if(this.isEmpty||this.isAllTime)return this.clone();const s=new i,{start:r,end:n}=this;return(0,o.pC)(r)&&(s.start=(0,a.Nm)(r,e,t)),(0,o.pC)(n)&&(s.end=(0,a.Nm)(n,e,t)),s}union(e){if(!e||e.isEmpty)return this.clone();if(this.isEmpty)return e.clone();if(this.isAllTime||e.isAllTime)return p.clone();const t=(0,o.pC)(this.start)&&(0,o.pC)(e.start)?new Date(Math.min(this.start.getTime(),e.start.getTime())):null,s=(0,o.pC)(this.end)&&(0,o.pC)(e.end)?new Date(Math.max(this.end.getTime(),e.end.getTime())):null;return new i({start:t,end:s})}};(0,r._)([(0,l.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"end",void 0),(0,r._)([(0,u.r)("end")],d.prototype,"readEnd",null),(0,r._)([(0,c.c)("end")],d.prototype,"writeEnd",null),(0,r._)([(0,l.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isAllTime",null),(0,r._)([(0,l.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isEmpty",null),(0,r._)([(0,l.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"start",void 0),(0,r._)([(0,u.r)("start")],d.prototype,"readStart",null),(0,r._)([(0,c.c)("start")],d.prototype,"writeStart",null),d=i=(0,r._)([(0,h.j)("esri.TimeExtent")],d);const p=new d,_=new d({start:void 0,end:void 0}),f=d},5732:(e,t,s)=>{s.d(t,{c:()=>i});var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},25929:(e,t,s)=>{s.d(t,{M:()=>f,a:()=>m,e:()=>p,f:()=>n,i:()=>c,p:()=>d,r:()=>a,t:()=>o,w:()=>l});var i=s(70586),r=s(17452);function n(e,t){const s=t&&t.url&&t.url.path;if(e&&s&&(e=(0,r.hF)(e,s,{preserveProtocolRelative:!0}),t.portalItem&&t.readResourcePaths)){const s=(0,r.PF)(e,t.portalItem.itemUrl);null!=s&&h.test(s)&&t.readResourcePaths.push(t.portalItem.resourceFromPath(s).path)}return _(e,t&&t.portal)}function o(e,t,s=f.YES){if(null==e)return e;!(0,r.YP)(e)&&t&&t.blockedRelativeUrls&&t.blockedRelativeUrls.push(e);let i=(0,r.hF)(e);if(t){const s=t.verifyItemRelativeUrls&&t.verifyItemRelativeUrls.rootPath||t.url&&t.url.path;if(s){const n=_(s,t.portal),o=_(i,t.portal);i=(0,r.PF)(o,n,n),null!=i&&i!==o&&i!==e&&t.verifyItemRelativeUrls&&t.verifyItemRelativeUrls.writtenUrls.push(i)}}return i=p(i,t?.portal),(0,r.YP)(i)&&(i=(0,r.Fv)(i)),t?.resources&&t?.portalItem&&!(0,r.YP)(i)&&!(0,r.HK)(i)&&s===f.YES&&t.resources.toKeep.push({resource:t.portalItem.resourceFromPath(i),compress:!1}),i}function a(e,t,s){return n(e,s)}function l(e,t,s,i){const r=o(e,i);void 0!==r&&(t[s]=r)}const u=/\/items\/([^\/]+)\/resources\/(.*)/,h=/^\.\/resources\//;function c(e){return(e?.match(u)??null)?.[1]??null}function d(e){const t=e?.match(u)??null;if(null==t)return null;const s=t[2],n=s.lastIndexOf("/");if(-1===n){const{path:e,extension:t}=(0,r.fZ)(s);return{prefix:null,filename:e,extension:(0,i.Wg)(t)}}const{path:o,extension:a}=(0,r.fZ)(s.slice(n+1));return{prefix:s.slice(0,n),filename:o,extension:(0,i.Wg)(a)}}function p(e,t){return t&&!t.isPortal&&t.urlKey&&t.customBaseUrl?(0,r.Ie)(e,`${t.urlKey}.${t.customBaseUrl}`,t.portalHostname):e}function _(e,t){if(!t||t.isPortal||!t.urlKey||!t.customBaseUrl)return e;const s=`${t.urlKey}.${t.customBaseUrl}`,i=(0,r.TI)();return(0,r.D6)(i,`${i.scheme}://${s}`)?(0,r.Ie)(e,t.portalHostname,s):(0,r.Ie)(e,s,t.portalHostname)}var f,g;(g=f||(f={}))[g.YES=0]="YES",g[g.NO=1]="NO";const m=Object.freeze(Object.defineProperty({__proto__:null,get MarkKeep(){return f},ensureMainOnlineDomain:p,fromJSON:n,itemIdFromResourceUrl:c,prefixAndFilenameFromResourceUrl:d,read:a,toJSON:o,write:l},Symbol.toStringTag,{value:"Module"}))},2368:(e,t,s)=>{s.d(t,{J:()=>h,j:()=>c});var i=s(43697),r=s(15923),n=(s(80442),s(22974)),o=(s(92604),s(70586)),a=s(31263),l=s(1153),u=s(52011);const h=e=>{let t=class extends e{clone(){const e=(0,o.s3)((0,l.vw)(this),"unable to clone instance of non-accessor class"),t=e.metadatas,s=e.store,i={},r=new Map;for(const e in t){const o=t[e],l=s?.originOf(e),u=o.clonable;if(o.readOnly||!1===u||l!==a.s3.USER&&l!==a.s3.DEFAULTS&&l!==a.s3.WEB_MAP&&l!==a.s3.WEB_SCENE)continue;const h=this[e];let c=null;c="function"==typeof u?u(h):"reference"===u?h:(0,n.Vo)(h),null!=h&&null==c||(l===a.s3.DEFAULTS?r.set(e,c):i[e]=c)}const u=new(0,Object.getPrototypeOf(this).constructor)(i);if(r.size){const e=(0,l.vw)(u)?.store;if(e)for(const[t,s]of r)e.set(t,s,a.s3.DEFAULTS)}return u}};return t=(0,i._)([(0,u.j)("esri.core.Clonable")],t),t};let c=class extends(h(r.Z)){};c=(0,i._)([(0,u.j)("esri.core.Clonable")],c)},3920:(e,t,s)=>{s.d(t,{p:()=>u,r:()=>h});var i=s(43697),r=s(15923),n=s(61247),o=s(5600),a=s(52011),l=s(72762);const u=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new n.Z}get updatingHandles(){return this._get("updatingHandles")||new l.t}};return(0,i._)([(0,o.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,i._)([(0,o.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,i._)([(0,a.j)("esri.core.HandleOwner")],t),t};let h=class extends(u(r.Z)){};h=(0,i._)([(0,a.j)("esri.core.HandleOwner")],h)},13867:(e,t,s)=>{s.d(t,{Z:()=>r});var i=s(69801);class r{constructor(e,t){this._storage=new i.WJ,this._storage.maxSize=e,t&&this._storage.registerRemoveFunc("",t)}put(e,t,s){this._storage.put(e,t,s,1)}pop(e){return this._storage.pop(e)}get(e){return this._storage.get(e)}clear(){this._storage.clearAll()}destroy(){this._storage.destroy()}get maxSize(){return this._storage.maxSize}set maxSize(e){this._storage.maxSize=e}}},69801:(e,t,s)=>{s.d(t,{WJ:()=>l,Xq:()=>a});var i,r,n=s(70586),o=s(44553);(r=i||(i={}))[r.ALL=0]="ALL",r[r.SOME=1]="SOME";class a{constructor(e,t,s){this._namespace=e,this._storage=t,this._removeFunc=!1,this._hit=0,this._miss=0,this._storage.register(this),this._namespace+=":",s&&(this._storage.registerRemoveFunc(this._namespace,s),this._removeFunc=!0)}destroy(){this._storage.clear(this._namespace),this._removeFunc&&this._storage.deregisterRemoveFunc(this._namespace),this._storage.deregister(this),this._storage=null}get namespace(){return this._namespace.slice(0,-1)}get hitRate(){return this._hit/(this._hit+this._miss)}get size(){return this._storage.size}get maxSize(){return this._storage.maxSize}resetHitRate(){this._hit=this._miss=0}put(e,t,s,i=0){this._storage.put(this._namespace+e,t,s,i)}get(e){const t=this._storage.get(this._namespace+e);return void 0===t?++this._miss:++this._hit,t}pop(e){const t=this._storage.pop(this._namespace+e);return void 0===t?++this._miss:++this._hit,t}updateSize(e,t,s){this._storage.updateSize(this._namespace+e,t,s)}clear(){this._storage.clear(this._namespace)}clearAll(){this._storage.clearAll()}getStats(){return this._storage.getStats()}resetStats(){this._storage.resetStats()}}class l{constructor(e=10485760){this._maxSize=e,this._db=new Map,this._size=0,this._hit=0,this._miss=0,this._removeFuncs=new o.Z,this._users=new o.Z}destroy(){this.clearAll(),this._removeFuncs.clear(),this._users.clear(),this._db=null}register(e){this._users.push(e)}deregister(e){this._users.removeUnordered(e)}registerRemoveFunc(e,t){this._removeFuncs.push([e,t])}deregisterRemoveFunc(e){this._removeFuncs.filterInPlace((t=>t[0]!==e))}get size(){return this._size}get maxSize(){return this._maxSize}set maxSize(e){this._maxSize=Math.max(e,0),this._checkSizeLimit()}put(e,t,s,r){const n=this._db.get(e);if(n&&(this._size-=n.size,this._db.delete(e),n.entry!==t&&this._notifyRemove(e,n.entry,i.ALL)),s>this._maxSize)return void this._notifyRemove(e,t,i.ALL);if(void 0===t)return void console.warn("Refusing to cache undefined entry ");if(!s||s<0)return void console.warn("Refusing to cache entry with invalid size "+s);const o=1+Math.max(r,-3)- -3;this._db.set(e,{entry:t,size:s,lifetime:o,lives:o}),this._size+=s,this._checkSizeLimit()}updateSize(e,t,s){const r=this._db.get(e);if(r&&r.entry===t){for(this._size-=r.size;s>this._maxSize;){const r=this._notifyRemove(e,t,i.SOME);if(!((0,n.pC)(r)&&r>0))return void this._db.delete(e);s=r}r.size=s,this._size+=s,this._checkSizeLimit()}}pop(e){const t=this._db.get(e);if(t)return this._size-=t.size,this._db.delete(e),++this._hit,t.entry;++this._miss}get(e){const t=this._db.get(e);if(void 0!==t)return this._db.delete(e),t.lives=t.lifetime,this._db.set(e,t),++this._hit,t.entry;++this._miss}getStats(){const e={Size:Math.round(this._size/1048576)+"/"+Math.round(this._maxSize/1048576)+"MB","Hit rate":Math.round(100*this._getHitRate())+"%",Entries:this._db.size.toString()},t={},s=new Array;this._db.forEach(((e,i)=>{const r=e.lifetime;s[r]=(s[r]||0)+e.size,this._users.forAll((s=>{const r=s.namespace;if(i.startsWith(r)){const s=t[r]||0;t[r]=s+e.size}}))}));const i={};this._users.forAll((e=>{const s=e.namespace;if(!isNaN(e.hitRate)&&e.hitRate>0){const r=t[s]||0;t[s]=r,i[s]=Math.round(100*e.hitRate)+"%"}else i[s]="0%"}));const r=Object.keys(t);r.sort(((e,s)=>t[s]-t[e])),r.forEach((s=>e[s]=Math.round(t[s]/2**20)+"MB / "+i[s]));for(let t=s.length-1;t>=0;--t){const i=s[t];i&&(e["Priority "+(t+-3-1)]=Math.round(i/this.size*100)+"%")}return e}resetStats(){this._hit=this._miss=0,this._users.forAll((e=>e.resetHitRate()))}clear(e){this._db.forEach(((t,s)=>{s.startsWith(e)&&(this._size-=t.size,this._db.delete(s),this._notifyRemove(s,t.entry,i.ALL))}))}clearAll(){this._db.forEach(((e,t)=>this._notifyRemove(t,e.entry,i.ALL))),this._size=0,this._db.clear()}_getHitRate(){return this._hit/(this._hit+this._miss)}_notifyRemove(e,t,s){let i;return this._removeFuncs.some((r=>{if(e.startsWith(r[0])){const e=r[1](t,s);return"number"==typeof e&&(i=e),!0}return!1})),i}_checkSizeLimit(){if(!(this._size<=this._maxSize))for(const[e,t]of this._db){if(this._db.delete(e),t.lives<=1){this._size-=t.size;const s=this._notifyRemove(e,t.entry,i.SOME);(0,n.pC)(s)&&s>0&&(this._size+=s,t.lives=t.lifetime,t.size=s,this._db.set(e,t))}else--t.lives,this._db.set(e,t);if(this._size<=.9*this.maxSize)return}}}},66643:(e,t,s)=>{s.d(t,{Ed:()=>u,UI:()=>h,mt:()=>_,q6:()=>p,vr:()=>f});var i=s(43697),r=s(15923),n=s(70586),o=s(95330),a=s(5600),l=(s(75215),s(67676),s(52011));function u(e,t,s){return(0,o.as)(e.map(((e,i)=>t.apply(s,[e,i]))))}async function h(e,t,s){return(await(0,o.as)(e.map(((e,i)=>t.apply(s,[e,i]))))).map((e=>e.value))}function c(e){return{ok:!0,value:e}}function d(e){return{ok:!1,error:e}}async function p(e){if((0,n.Wi)(e))return{ok:!1,error:new Error("no promise provided")};try{return c(await e)}catch(e){return d(e)}}async function _(e){try{return c(await e)}catch(e){return(0,o.r9)(e),d(e)}}function f(e,t){return new g(e,t)}let g=class extends r.Z{get value(){return e=this._result,(0,n.pC)(e)&&!0===e.ok?e.value:null;var e}get error(){return e=this._result,(0,n.pC)(e)&&!1===e.ok?e.error:null;var e}get finished(){return(0,n.pC)(this._result)}constructor(e,t){super({}),this._result=null,this._abortHandle=null,this.abort=()=>{this._abortController=(0,n.IM)(this._abortController)},this.remove=this.abort,this._abortController=new AbortController;const{signal:s}=this._abortController;this.promise=e(s),this.promise.then((e=>{this._result=c(e),this._cleanup()}),(e=>{this._result=d(e),this._cleanup()})),this._abortHandle=(0,o.fu)(t,this.abort)}normalizeCtorArgs(){return{}}destroy(){this.abort()}_cleanup(){this._abortHandle=(0,n.hw)(this._abortHandle),this._abortController=null}};(0,i._)([(0,a.Cb)()],g.prototype,"value",null),(0,i._)([(0,a.Cb)()],g.prototype,"error",null),(0,i._)([(0,a.Cb)()],g.prototype,"finished",null),(0,i._)([(0,a.Cb)()],g.prototype,"promise",void 0),(0,i._)([(0,a.Cb)()],g.prototype,"_result",void 0),g=(0,i._)([(0,l.j)("esri.core.asyncUtils.ReactiveTask")],g)},43090:(e,t,s)=>{function i(e){return 32+e.length}function r(e){if(!e)return 0;let t=l;for(const s in e)if(e.hasOwnProperty(s)){const r=e[s];switch(typeof r){case"string":t+=i(r);break;case"number":t+=16;break;case"boolean":t+=4}}return t}function n(e){if(!e)return 0;if(Array.isArray(e))return function(e){const t=e.length;if(0===t||"number"==typeof e[0])return 32+8*t;let s=u;for(let i=0;i<t;i++)s+=o(e[i]);return s}(e);let t=l;for(const s in e)e.hasOwnProperty(s)&&(t+=o(e[s]));return t}function o(e){switch(typeof e){case"object":return n(e);case"string":return i(e);case"number":return 16;case"boolean":return 4;default:return 8}}function a(e,t){return u+e.length*t}s.d(t,{Ul:()=>n,Y8:()=>h,do:()=>a,f2:()=>r});const l=32,u=32;var h;!function(e){e[e.KILOBYTES=1024]="KILOBYTES",e[e.MEGABYTES=1048576]="MEGABYTES",e[e.GIGABYTES=1073741824]="GIGABYTES"}(h||(h={}))},24133:(e,t,s)=>{s.d(t,{Q:()=>a});var i=s(67676),r=s(70586),n=s(44553),o=s(88764);class a{constructor(e=9,t){this._compareMinX=c,this._compareMinY=d,this._toBBox=e=>e,this._maxEntries=Math.max(4,e||9),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),t&&("function"==typeof t?this._toBBox=t:this._initFormat(t)),this.clear()}destroy(){this.clear(),b.prune(),C.prune(),w.prune(),x.prune()}all(e){this._all(this._data,e)}search(e,t){let s=this._data;const i=this._toBBox;if(y(e,s))for(b.clear();s;){for(let r=0,n=s.children.length;r<n;r++){const n=s.children[r],o=s.leaf?i(n):n;y(e,o)&&(s.leaf?t(n):m(e,o)?this._all(n,t):b.push(n))}s=b.pop()}}collides(e){let t=this._data;const s=this._toBBox;if(!y(e,t))return!1;for(b.clear();t;){for(let i=0,r=t.children.length;i<r;i++){const r=t.children[i],n=t.leaf?s(r):r;if(y(e,n)){if(t.leaf||m(e,n))return!0;b.push(r)}}t=b.pop()}return!1}load(e){if(!e.length)return this;if(e.length<this._minEntries){for(let t=0,s=e.length;t<s;t++)this.insert(e[t]);return this}let t=this._build(e.slice(0,e.length),0,e.length-1,0);if(this._data.children.length)if(this._data.height===t.height)this._splitRoot(this._data,t);else{if(this._data.height<t.height){const e=this._data;this._data=t,t=e}this._insert(t,this._data.height-t.height-1,!0)}else this._data=t;return this}insert(e){return e&&this._insert(e,this._data.height-1),this}clear(){return this._data=new F([]),this}remove(e){if(!e)return this;let t,s=this._data,n=null,o=0,a=!1;const l=this._toBBox(e);for(w.clear(),x.clear();s||w.length>0;){if(s||(s=(0,r.j0)(w.pop()),n=w.data[w.length-1],o=x.pop()??0,a=!0),s.leaf&&(t=(0,i.cq)(s.children,e,s.children.length,s.indexHint),-1!==t))return s.children.splice(t,1),w.push(s),this._condense(w),this;a||s.leaf||!m(s,l)?n?(o++,s=n.children[o],a=!1):s=null:(w.push(s),x.push(o),o=0,n=s,s=s.children[0])}return this}toJSON(){return this._data}fromJSON(e){return this._data=e,this}_all(e,t){let s=e;for(C.clear();s;){if(!0===s.leaf)for(const e of s.children)t(e);else C.pushArray(s.children);s=C.pop()??null}}_build(e,t,s,i){const r=s-t+1;let n=this._maxEntries;if(r<=n){const i=new F(e.slice(t,s+1));return l(i,this._toBBox),i}i||(i=Math.ceil(Math.log(r)/Math.log(n)),n=Math.ceil(r/n**(i-1)));const o=new T([]);o.height=i;const a=Math.ceil(r/n),u=a*Math.ceil(Math.sqrt(n));v(e,t,s,u,this._compareMinX);for(let r=t;r<=s;r+=u){const t=Math.min(r+u-1,s);v(e,r,t,a,this._compareMinY);for(let s=r;s<=t;s+=a){const r=Math.min(s+a-1,t);o.children.push(this._build(e,s,r,i-1))}}return l(o,this._toBBox),o}_chooseSubtree(e,t,s,i){for(;i.push(t),!0!==t.leaf&&i.length-1!==s;){let s,i=1/0,r=1/0;for(let n=0,o=t.children.length;n<o;n++){const o=t.children[n],a=p(o),l=f(e,o)-a;l<r?(r=l,i=a<i?a:i,s=o):l===r&&a<i&&(i=a,s=o)}t=s||t.children[0]}return t}_insert(e,t,s){const i=this._toBBox,r=s?e:i(e);w.clear();const n=this._chooseSubtree(r,this._data,t,w);for(n.children.push(e),h(n,r);t>=0&&w.data[t].children.length>this._maxEntries;)this._split(w,t),t--;this._adjustParentBBoxes(r,w,t)}_split(e,t){const s=e.data[t],i=s.children.length,r=this._minEntries;this._chooseSplitAxis(s,r,i);const n=this._chooseSplitIndex(s,r,i);if(!n)return void console.log("  Error: assertion failed at PooledRBush._split: no valid split index");const o=s.children.splice(n,s.children.length-n),a=s.leaf?new F(o):new T(o);a.height=s.height,l(s,this._toBBox),l(a,this._toBBox),t?e.data[t-1].children.push(a):this._splitRoot(s,a)}_splitRoot(e,t){this._data=new T([e,t]),this._data.height=e.height+1,l(this._data,this._toBBox)}_chooseSplitIndex(e,t,s){let i,r,n;i=r=1/0;for(let o=t;o<=s-t;o++){const t=u(e,0,o,this._toBBox),a=u(e,o,s,this._toBBox),l=g(t,a),h=p(t)+p(a);l<i?(i=l,n=o,r=h<r?h:r):l===i&&h<r&&(r=h,n=o)}return n}_chooseSplitAxis(e,t,s){const i=e.leaf?this._compareMinX:c,r=e.leaf?this._compareMinY:d;this._allDistMargin(e,t,s,i)<this._allDistMargin(e,t,s,r)&&e.children.sort(i)}_allDistMargin(e,t,s,i){e.children.sort(i);const r=this._toBBox,n=u(e,0,t,r),o=u(e,s-t,s,r);let a=_(n)+_(o);for(let i=t;i<s-t;i++){const t=e.children[i];h(n,e.leaf?r(t):t),a+=_(n)}for(let i=s-t-1;i>=t;i--){const t=e.children[i];h(o,e.leaf?r(t):t),a+=_(o)}return a}_adjustParentBBoxes(e,t,s){for(let i=s;i>=0;i--)h(t.data[i],e)}_condense(e){for(let t=e.length-1;t>=0;t--){const s=e.data[t];if(0===s.children.length)if(t>0){const r=e.data[t-1],n=r.children;n.splice((0,i.cq)(n,s,n.length,r.indexHint),1)}else this.clear();else l(s,this._toBBox)}}_initFormat(e){const t=["return a"," - b",";"];this._compareMinX=new Function("a","b",t.join(e[0])),this._compareMinY=new Function("a","b",t.join(e[1])),this._toBBox=new Function("a","return {minX: a"+e[0]+", minY: a"+e[1]+", maxX: a"+e[2]+", maxY: a"+e[3]+"};")}}function l(e,t){u(e,0,e.children.length,t,e)}function u(e,t,s,i,r){r||(r=new F([])),r.minX=1/0,r.minY=1/0,r.maxX=-1/0,r.maxY=-1/0;for(let n,o=t;o<s;o++)n=e.children[o],h(r,e.leaf?i(n):n);return r}function h(e,t){e.minX=Math.min(e.minX,t.minX),e.minY=Math.min(e.minY,t.minY),e.maxX=Math.max(e.maxX,t.maxX),e.maxY=Math.max(e.maxY,t.maxY)}function c(e,t){return e.minX-t.minX}function d(e,t){return e.minY-t.minY}function p(e){return(e.maxX-e.minX)*(e.maxY-e.minY)}function _(e){return e.maxX-e.minX+(e.maxY-e.minY)}function f(e,t){return(Math.max(t.maxX,e.maxX)-Math.min(t.minX,e.minX))*(Math.max(t.maxY,e.maxY)-Math.min(t.minY,e.minY))}function g(e,t){const s=Math.max(e.minX,t.minX),i=Math.max(e.minY,t.minY),r=Math.min(e.maxX,t.maxX),n=Math.min(e.maxY,t.maxY);return Math.max(0,r-s)*Math.max(0,n-i)}function m(e,t){return e.minX<=t.minX&&e.minY<=t.minY&&t.maxX<=e.maxX&&t.maxY<=e.maxY}function y(e,t){return t.minX<=e.maxX&&t.minY<=e.maxY&&t.maxX>=e.minX&&t.maxY>=e.minY}function v(e,t,s,i,n){const a=[t,s];for(;a.length;){const t=(0,r.j0)(a.pop()),s=(0,r.j0)(a.pop());if(t-s<=i)continue;const l=s+Math.ceil((t-s)/i/2)*i;(0,o.q)(e,l,s,t,n),a.push(s,l,l,t)}}const b=new n.Z,C=new n.Z,w=new n.Z,x=new n.Z({deallocator:void 0});class E{constructor(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0}}class S extends E{constructor(){super(...arguments),this.height=1,this.indexHint=new i.SO}}class F extends S{constructor(e){super(),this.children=e,this.leaf=!0}}class T extends S{constructor(e){super(),this.children=e,this.leaf=!1}}},17445:(e,t,s)=>{s.d(t,{N1:()=>d,YP:()=>l,Z_:()=>f,gx:()=>u,nn:()=>g,on:()=>c,tX:()=>m});var i=s(91460),r=s(50758),n=s(70586),o=s(95330),a=s(26258);function l(e,t,s={}){return h(e,t,s,p)}function u(e,t,s={}){return h(e,t,s,_)}function h(e,t,s={},i){let r=null;const o=s.once?(e,s)=>{i(e)&&((0,n.hw)(r),t(e,s))}:(e,s)=>{i(e)&&t(e,s)};if(r=(0,a.aQ)(e,o,s.sync,s.equals),s.initial){const t=e();o(t,t)}return r}function c(e,t,s,o={}){let a=null,u=null,h=null;function c(){a&&u&&(u.remove(),o.onListenerRemove?.(a),a=null,u=null)}function d(e){o.once&&o.once&&(0,n.hw)(h),s(e)}const p=l(e,((e,s)=>{c(),(0,i.vT)(e)&&(a=e,u=(0,i.on)(e,t,d),o.onListenerAdd?.(e))}),{sync:o.sync,initial:!0});return h=(0,r.kB)((()=>{p.remove(),c()})),h}function d(e,t){return function(e,t,s){if((0,o.Hc)(s))return Promise.reject((0,o.zE)());const i=e();if(t?.(i))return Promise.resolve(i);let a=null;function l(){a=(0,n.hw)(a)}return new Promise(((i,n)=>{a=(0,r.AL)([(0,o.fu)(s,(()=>{l(),n((0,o.zE)())})),h(e,(e=>{l(),i(e)}),{sync:!1,once:!0},t??p)])}))}(e,_,t)}function p(e){return!0}function _(e){return!!e}s(87538);const f={sync:!0},g={initial:!0},m={sync:!0,initial:!0}},72762:(e,t,s)=>{s.d(t,{t:()=>c});var i=s(43697),r=s(15923),n=s(61247),o=s(70586),a=s(17445),l=s(1654),u=s(5600),h=s(52011);let c=class extends r.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new n.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,s={}){return this._installWatch(e,t,s,a.YP)}addWhen(e,t,s={}){return this._installWatch(e,t,s,a.gx)}addOnCollectionChange(e,t,{initial:s=!1,final:i=!1}={}){const r=++this._handleId;return this._handles.add([(0,a.on)(e,"after-changes",this._createSyncUpdatingCallback(),a.Z_),(0,a.on)(e,"change",t,{onListenerAdd:s?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:i?e=>t({added:[],removed:e.toArray()}):void 0})],r),{remove:()=>this._handles.remove(r)}}addPromise(e){if((0,o.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(d)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const s=()=>this._handles.remove(t);return e.then(s,s),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,s={},i){const r=++this._handleId;s.sync||this._installSyncUpdatingWatch(e,r);const n=i(e,t,s);return this._handles.add(n,r),{remove:()=>this._handles.remove(r)}}_installSyncUpdatingWatch(e,t){const s=this._createSyncUpdatingCallback(),i=(0,a.YP)(e,s,{sync:!0,equals:()=>!1});return this._handles.add(i,t),i}_createSyncUpdatingCallback(){return()=>{this._handles.remove(d),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,l.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(d))})),d)}}};(0,i._)([(0,u.Cb)({readOnly:!0})],c.prototype,"updating",void 0),c=(0,i._)([(0,h.j)("esri.core.support.WatchUpdatingTracking")],c);const d=-42},35463:(e,t,s)=>{s.d(t,{JE:()=>o,Nm:()=>n,rJ:()=>a}),s(80442);const i={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},r={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function n(e,t,s){const i=new Date(e.getTime());if(t&&s){const e=r[s],{getter:n,setter:o,multiplier:a}=e;if("months"===s){const e=function(e,t){const s=new Date(e,t+1,1);return s.setDate(0),s.getDate()}(i.getFullYear(),i.getMonth()+t);i.getDate()>e&&i.setDate(e)}i[o](i[n]()+t*a)}return i}function o(e,t){switch(t){case"milliseconds":return new Date(e.getTime());case"seconds":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case"minutes":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case"hours":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case"days":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case"weeks":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case"months":return new Date(e.getFullYear(),e.getMonth(),1);case"years":return new Date(e.getFullYear(),0,1);case"decades":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case"centuries":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function a(e,t,s){return 0===e?0:e*i[t]/i[s]}},37549:(e,t,s)=>{s.d(t,{H:()=>a});var i=s(80442),r=s(24133),n=s(24470);const o={minX:0,minY:0,maxX:0,maxY:0};class a{constructor(){this._indexInvalid=!1,this._boundsToLoad=[],this._boundsById=new Map,this._idByBounds=new Map,this._index=new r.Q(9,(0,i.Z)("esri-csp-restrictions")?e=>({minX:e[0],minY:e[1],maxX:e[2],maxY:e[3]}):["[0]","[1]","[2]","[3]"]),this._loadIndex=()=>{if(this._indexInvalid){const e=new Array(this._idByBounds.size);let t=0;this._idByBounds.forEach(((s,i)=>{e[t++]=i})),this._indexInvalid=!1,this._index.clear(),this._index.load(e)}else this._boundsToLoad.length&&(this._index.load(Array.from(new Set(this._boundsToLoad.filter((e=>this._idByBounds.has(e)))))),this._boundsToLoad.length=0)}}get fullBounds(){if(!this._boundsById.size)return null;const e=(0,n.cS)();for(const t of this._boundsById.values())t&&(e[0]=Math.min(t[0],e[0]),e[1]=Math.min(t[1],e[1]),e[2]=Math.max(t[2],e[2]),e[3]=Math.max(t[3],e[3]));return e}get valid(){return!this._indexInvalid}clear(){this._indexInvalid=!1,this._boundsToLoad.length=0,this._boundsById.clear(),this._idByBounds.clear(),this._index.clear()}delete(e){const t=this._boundsById.get(e);this._boundsById.delete(e),t&&(this._idByBounds.delete(t),this._indexInvalid||this._index.remove(t))}forEachInBounds(e,t){this._loadIndex(),function(e,t,s){(function(e){o.minX=e[0],o.minY=e[1],o.maxX=e[2],o.maxY=e[3]})(t),e.search(o,s)}(this._index,e,(e=>t(this._idByBounds.get(e))))}get(e){return this._boundsById.get(e)}has(e){return this._boundsById.has(e)}invalidateIndex(){this._indexInvalid||(this._indexInvalid=!0,this._boundsToLoad.length=0)}set(e,t){if(!this._indexInvalid){const t=this._boundsById.get(e);t&&(this._index.remove(t),this._idByBounds.delete(t))}this._boundsById.set(e,t),t&&(this._idByBounds.set(t,e),this._indexInvalid||(this._boundsToLoad.push(t),this._boundsToLoad.length>5e4&&this._loadIndex()))}}},57191:(e,t,s)=>{s.d(t,{Z:()=>m});var i=s(20102),r=s(32448),n=s(92604),o=s(70586),a=s(60437),l=s(24470),u=s(98732),h=s(37549),c=s(29730),d=s(70272),p=s(5428);const _={getObjectId:e=>e.objectId,getAttributes:e=>e.attributes,getAttribute:(e,t)=>e.attributes[t],cloneWithGeometry:(e,t)=>new d.u_(t,e.attributes,null,e.objectId),getGeometry:e=>e.geometry,getCentroid:(e,t)=>((0,o.Wi)(e.centroid)&&(e.centroid=(0,c.Y)(new p.Z,e.geometry,t.hasZ,t.hasM)),e.centroid)};var f=s(11490);const g=(0,a.Ue)();class m{constructor(e){this.geometryInfo=e,this._boundsStore=new h.H,this._featuresById=new Map,this._markedIds=new Set,this.events=new r.Z,this.featureAdapter=_}get geometryType(){return this.geometryInfo.geometryType}get hasM(){return this.geometryInfo.hasM}get hasZ(){return this.geometryInfo.hasZ}get numFeatures(){return this._featuresById.size}get fullBounds(){return this._boundsStore.fullBounds}get storeStatistics(){let e=0;return this._featuresById.forEach((t=>{(0,o.pC)(t.geometry)&&t.geometry.coords&&(e+=t.geometry.coords.length)})),{featureCount:this._featuresById.size,vertexCount:e/(this.hasZ?this.hasM?4:3:this.hasM?3:2)}}getFullExtent(e){if((0,o.Wi)(this.fullBounds))return null;const[t,s,i,r]=this.fullBounds;return{xmin:t,ymin:s,xmax:i,ymax:r,spatialReference:(0,f.S2)(e)}}add(e){this._add(e),this._emitChanged()}addMany(e){for(const t of e)this._add(t);this._emitChanged()}clear(){this._featuresById.clear(),this._boundsStore.clear(),this._emitChanged()}removeById(e){const t=this._featuresById.get(e);return t?(this._remove(t),this._emitChanged(),t):null}removeManyById(e){this._boundsStore.invalidateIndex();for(const t of e){const e=this._featuresById.get(t);e&&this._remove(e)}this._emitChanged()}forEachBounds(e,t){for(const s of e){const e=this._boundsStore.get(s.objectId);e&&t((0,a.JR)(g,e))}}getFeature(e){return this._featuresById.get(e)}has(e){return this._featuresById.has(e)}forEach(e){this._featuresById.forEach((t=>e(t)))}forEachInBounds(e,t){this._boundsStore.forEachInBounds(e,(e=>{t(this._featuresById.get(e))}))}startMarkingUsedFeatures(){this._boundsStore.invalidateIndex(),this._markedIds.clear()}sweep(){let e=!1;this._featuresById.forEach(((t,s)=>{this._markedIds.has(s)||(e=!0,this._remove(t))})),this._markedIds.clear(),e&&this._emitChanged()}_emitChanged(){this.events.emit("changed",void 0)}_add(e){if(!e)return;const t=e.objectId;if(null==t)return void n.Z.getLogger("esri.layers.graphics.data.FeatureStore").error(new i.Z("featurestore:invalid-feature","feature id is missing",{feature:e}));const s=this._featuresById.get(t);let r;if(this._markedIds.add(t),s?(e.displayId=s.displayId,r=this._boundsStore.get(t),this._boundsStore.delete(t)):(0,o.pC)(this.onFeatureAdd)&&this.onFeatureAdd(e),(0,o.Wi)(e.geometry)||!e.geometry.coords||!e.geometry.coords.length)return this._boundsStore.set(t,null),void this._featuresById.set(t,e);r=(0,u.$)((0,o.pC)(r)?r:(0,l.Ue)(),e.geometry,this.geometryInfo.hasZ,this.geometryInfo.hasM),(0,o.pC)(r)&&this._boundsStore.set(t,r),this._featuresById.set(t,e)}_remove(e){(0,o.pC)(this.onFeatureRemove)&&this.onFeatureRemove(e);const t=e.objectId;return this._markedIds.delete(t),this._boundsStore.delete(t),this._featuresById.delete(t),e}}},1231:(e,t,s)=>{s.d(t,{Z:()=>g});var i,r=s(43697),n=s(35454),o=s(96674),a=s(5600),l=s(75215),u=(s(67676),s(36030)),h=s(71715),c=s(52011),d=s(72729),p=s(86719);const _=new n.X({binary:"binary",coordinate:"coordinate",countOrAmount:"count-or-amount",dateAndTime:"date-and-time",description:"description",locationOrPlaceName:"location-or-place-name",measurement:"measurement",nameOrTitle:"name-or-title",none:"none",orderedOrRanked:"ordered-or-ranked",percentageOrRatio:"percentage-or-ratio",typeOrCategory:"type-or-category",uniqueIdentifier:"unique-identifier"});let f=i=class extends o.wq{constructor(e){super(e),this.alias=null,this.defaultValue=void 0,this.description=null,this.domain=null,this.editable=!0,this.length=-1,this.name=null,this.nullable=!0,this.type=null,this.valueType=null,this.visible=!0}readDescription(e,{description:t}){let s=null;try{s=t?JSON.parse(t):null}catch(e){}return s?.value??null}readValueType(e,{description:t}){let s=null;try{s=t?JSON.parse(t):null}catch(e){}return s?_.fromJSON(s.fieldValueType):null}clone(){return new i({alias:this.alias,defaultValue:this.defaultValue,description:this.description,domain:this.domain&&this.domain.clone()||null,editable:this.editable,length:this.length,name:this.name,nullable:this.nullable,type:this.type,valueType:this.valueType,visible:this.visible})}};(0,r._)([(0,a.Cb)({type:String,json:{write:!0}})],f.prototype,"alias",void 0),(0,r._)([(0,a.Cb)({type:[String,Number],json:{write:{allowNull:!0}}})],f.prototype,"defaultValue",void 0),(0,r._)([(0,a.Cb)()],f.prototype,"description",void 0),(0,r._)([(0,h.r)("description")],f.prototype,"readDescription",null),(0,r._)([(0,a.Cb)({types:d.V5,json:{read:{reader:d.im},write:!0}})],f.prototype,"domain",void 0),(0,r._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],f.prototype,"editable",void 0),(0,r._)([(0,a.Cb)({type:l.z8,json:{write:!0}})],f.prototype,"length",void 0),(0,r._)([(0,a.Cb)({type:String,json:{write:!0}})],f.prototype,"name",void 0),(0,r._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],f.prototype,"nullable",void 0),(0,r._)([(0,u.J)(p.v)],f.prototype,"type",void 0),(0,r._)([(0,a.Cb)()],f.prototype,"valueType",void 0),(0,r._)([(0,h.r)("valueType",["description"])],f.prototype,"readValueType",null),(0,r._)([(0,a.Cb)({type:Boolean,json:{read:!1}})],f.prototype,"visible",void 0),f=i=(0,r._)([(0,c.j)("esri.layers.support.Field")],f);const g=f},39450:(e,t,s)=>{s.d(t,{Z:()=>h});var i,r=s(43697),n=s(96674),o=s(5600),a=s(75215),l=(s(67676),s(52011));let u=i=class extends n.wq{constructor(e){super(e),this.cols=null,this.level=0,this.levelValue=null,this.origin=null,this.resolution=0,this.rows=null,this.scale=0}clone(){return new i({cols:this.cols,level:this.level,levelValue:this.levelValue,resolution:this.resolution,rows:this.rows,scale:this.scale})}};(0,r._)([(0,o.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],u.prototype,"cols",void 0),(0,r._)([(0,o.Cb)({type:a.z8,json:{write:!0}})],u.prototype,"level",void 0),(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],u.prototype,"levelValue",void 0),(0,r._)([(0,o.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],u.prototype,"origin",void 0),(0,r._)([(0,o.Cb)({type:Number,json:{write:!0}})],u.prototype,"resolution",void 0),(0,r._)([(0,o.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],u.prototype,"rows",void 0),(0,r._)([(0,o.Cb)({type:Number,json:{write:!0}})],u.prototype,"scale",void 0),u=i=(0,r._)([(0,l.j)("esri.layers.support.LOD")],u);const h=u},11145:(e,t,s)=>{s.d(t,{Z:()=>S});var i,r=s(43697),n=s(35454),o=s(96674),a=s(70586),l=s(67900),u=s(5600),h=s(75215),c=(s(67676),s(71715)),d=s(52011),p=s(30556),_=s(94139),f=s(82971),g=s(24470),m=s(8744),y=s(40488),v=s(39450),b=s(43077);const C=new n.X({PNG:"png",PNG8:"png8",PNG24:"png24",PNG32:"png32",JPEG:"jpg",JPG:"jpg",DIB:"dib",TIFF:"tiff",EMF:"emf",PS:"ps",PDF:"pdf",GIF:"gif",SVG:"svg",SVGZ:"svgz",Mixed:"mixed",MIXED:"mixed",LERC:"lerc",LERC2D:"lerc2d",RAW:"raw",pbf:"pbf"});let w=i=class extends o.wq{static create(e={}){const{resolutionFactor:t=1,scales:s,size:r=256,spatialReference:n=f.Z.WebMercator,numLODs:o=24}=e;if(!(0,m.JY)(n)){const e=[];if(s)for(let t=0;t<s.length;t++){const i=s[t];e.push(new v.Z({level:t,scale:i,resolution:i}))}else{let t=5e-4;for(let s=o-1;s>=0;s--)e.unshift(new v.Z({level:s,scale:t,resolution:t})),t*=2}return new i({dpi:96,lods:e,origin:new _.Z(0,0,n),size:[r,r],spatialReference:n})}const a=(0,m.C5)(n),u=e.origin?new _.Z({x:e.origin.x,y:e.origin.y,spatialReference:n}):new _.Z(a?{x:a.origin[0],y:a.origin[1],spatialReference:n}:{x:0,y:0,spatialReference:n}),h=1/(39.37*(0,l.c9)(n)*96),c=[];if(s)for(let e=0;e<s.length;e++){const t=s[e],i=t*h;c.push(new v.Z({level:e,scale:t,resolution:i}))}else{let e=(0,m.sT)(n)?512/r*591657527.5917094:256/r*591657527.591555;const s=Math.ceil(o/t);c.push(new v.Z({level:0,scale:e,resolution:e*h}));for(let i=1;i<s;i++){const s=e/2**t,r=s*h;c.push(new v.Z({level:i,scale:s,resolution:r})),e=s}}return new i({dpi:96,lods:c,origin:u,size:[r,r],spatialReference:n})}constructor(e){super(e),this.dpi=96,this.format=null,this.origin=null,this.minScale=0,this.maxScale=0,this.size=null,this.spatialReference=null}get isWrappable(){const{spatialReference:e,origin:t}=this;if(e&&t){const s=(0,m.C5)(e);return e.isWrappable&&!!s&&Math.abs(s.origin[0]-t.x)<=s.dx}return!1}readOrigin(e,t){return _.Z.fromJSON({spatialReference:t.spatialReference,...e})}set lods(e){let t=0,s=0;const i=[],r=this._levelToLOD={};e&&(t=-1/0,s=1/0,e.forEach((e=>{i.push(e.scale),t=e.scale>t?e.scale:t,s=e.scale<s?e.scale:s,r[e.level]=e}))),this._set("scales",i),this._set("minScale",t),this._set("maxScale",s),this._set("lods",e),this._initializeUpsampleLevels()}readSize(e,t){return[t.cols,t.rows]}writeSize(e,t){t.cols=e[0],t.rows=e[1]}zoomToScale(e){const t=this.scales;if(e<=0)return t[0];if(e>=t.length-1)return t[t.length-1];const s=Math.floor(e),i=s+1;return t[s]/(t[s]/t[i])**(e-s)}scaleToZoom(e){const t=this.scales,s=t.length-1;let i=0;for(;i<s;i++){const s=t[i],r=t[i+1];if(s<=e)return i;if(r===e)return i+1;if(s>e&&r<e)return i+Math.log(s/e)/Math.log(s/r)}return i}snapScale(e,t=.95){const s=this.scaleToZoom(e);return s%Math.floor(s)>=t?this.zoomToScale(Math.ceil(s)):this.zoomToScale(Math.floor(s))}tileAt(e,t,s,i){const r=this.lodAt(e);if(!r)return null;let n,o;if("number"==typeof t)n=t,o=s;else if((0,m.fS)(t.spatialReference,this.spatialReference))n=t.x,o=t.y,i=s;else{const e=(0,y.iV)(t,this.spatialReference);if((0,a.Wi)(e))return null;n=e.x,o=e.y,i=s}const l=r.resolution*this.size[0],u=r.resolution*this.size[1];return i||(i=new b.f(null,0,0,0,(0,g.Ue)())),i.level=e,i.row=Math.floor((this.origin.y-o)/u+.001),i.col=Math.floor((n-this.origin.x)/l+.001),this.updateTileInfo(i),i}updateTileInfo(e,t=i.ExtrapolateOptions.NONE){let s=this.lodAt(e.level);if(!s&&t===i.ExtrapolateOptions.POWER_OF_TWO){const t=this.lods[this.lods.length-1];t.level<e.level&&(s=t)}if(!s)return;const r=e.level-s.level,n=s.resolution*this.size[0]/2**r,o=s.resolution*this.size[1]/2**r;e.id=`${e.level}/${e.row}/${e.col}`,e.extent||(e.extent=(0,g.Ue)()),e.extent[0]=this.origin.x+e.col*n,e.extent[1]=this.origin.y-(e.row+1)*o,e.extent[2]=e.extent[0]+n,e.extent[3]=e.extent[1]+o}upsampleTile(e){const t=this._upsampleLevels[e.level];return!(!t||-1===t.parentLevel||(e.level=t.parentLevel,e.row=Math.floor(e.row/t.factor+.001),e.col=Math.floor(e.col/t.factor+.001),this.updateTileInfo(e),0))}getTileBounds(e,t){const s=this.lodAt(t.level);if(null==s)return null;const{resolution:i}=s,r=i*this.size[0],n=i*this.size[1];return e[0]=this.origin.x+t.col*r,e[1]=this.origin.y-(t.row+1)*n,e[2]=e[0]+r,e[3]=e[1]+n,e}lodAt(e){return this._levelToLOD?.[e]??null}clone(){return i.fromJSON(this.write({}))}getOrCreateCompatible(e,t){if(256===this.size[0]&&256===this.size[1])return 256===e?this:null;const s=[],r=this.lods.length;for(let e=0;e<r;e++){const i=this.lods[e],r=i.resolution*t;s.push(new v.Z({level:i.level,scale:i.scale,resolution:r}))}return new i({size:[e,e],dpi:this.dpi,format:this.format,compressionQuality:this.compressionQuality,origin:this.origin,spatialReference:this.spatialReference,lods:s})}_initializeUpsampleLevels(){const e=this.lods;this._upsampleLevels=[];let t=null;for(let s=0;s<e.length;s++){const i=e[s];this._upsampleLevels[i.level]={parentLevel:t?t.level:-1,factor:t?t.resolution/i.resolution:0},t=i}}};var x,E;(0,r._)([(0,u.Cb)({type:Number,json:{write:!0}})],w.prototype,"compressionQuality",void 0),(0,r._)([(0,u.Cb)({type:Number,json:{write:!0}})],w.prototype,"dpi",void 0),(0,r._)([(0,u.Cb)({type:String,json:{read:C.read,write:C.write,origins:{"web-scene":{read:!1,write:!1}}}})],w.prototype,"format",void 0),(0,r._)([(0,u.Cb)({readOnly:!0})],w.prototype,"isWrappable",null),(0,r._)([(0,u.Cb)({type:_.Z,json:{write:!0}})],w.prototype,"origin",void 0),(0,r._)([(0,c.r)("origin")],w.prototype,"readOrigin",null),(0,r._)([(0,u.Cb)({type:[v.Z],value:null,json:{write:!0}})],w.prototype,"lods",null),(0,r._)([(0,u.Cb)({readOnly:!0})],w.prototype,"minScale",void 0),(0,r._)([(0,u.Cb)({readOnly:!0})],w.prototype,"maxScale",void 0),(0,r._)([(0,u.Cb)({readOnly:!0})],w.prototype,"scales",void 0),(0,r._)([(0,u.Cb)({cast:e=>Array.isArray(e)?e:"number"==typeof e?[e,e]:[256,256]})],w.prototype,"size",void 0),(0,r._)([(0,c.r)("size",["rows","cols"])],w.prototype,"readSize",null),(0,r._)([(0,p.c)("size",{cols:{type:h.z8},rows:{type:h.z8}})],w.prototype,"writeSize",null),(0,r._)([(0,u.Cb)({type:f.Z,json:{write:!0}})],w.prototype,"spatialReference",void 0),w=i=(0,r._)([(0,d.j)("esri.layers.support.TileInfo")],w),x=w||(w={}),(E=x.ExtrapolateOptions||(x.ExtrapolateOptions={}))[E.NONE=0]="NONE",E[E.POWER_OF_TWO=1]="POWER_OF_TWO";const S=w},43077:(e,t,s)=>{s.d(t,{f:()=>i});class i{constructor(e,t,s,i,r){this.id=e,this.level=t,this.row=s,this.col=i,this.extent=r}}},66677:(e,t,s)=>{s.d(t,{B5:()=>c,DR:()=>p,G:()=>b,M8:()=>g,Nm:()=>m,Qc:()=>d,XG:()=>y,a7:()=>f,ld:()=>_,wH:()=>v});var i=s(70586),r=s(17452),n=s(25929);const o={mapserver:"MapServer",imageserver:"ImageServer",featureserver:"FeatureServer",sceneserver:"SceneServer",streamserver:"StreamServer",vectortileserver:"VectorTileServer"},a=Object.values(o),l=new RegExp(`^((?:https?:)?\\/\\/\\S+?\\/rest\\/services\\/(.+?)\\/(${a.join("|")}))(?:\\/(?:layers\\/)?(\\d+))?`,"i"),u=new RegExp(`^((?:https?:)?\\/\\/\\S+?\\/([^\\/\\n]+)\\/(${a.join("|")}))(?:\\/(?:layers\\/)?(\\d+))?`,"i"),h=/(.*?)\/(?:layers\/)?(\d+)\/?$/i;function c(e){return!!l.test(e)}function d(e){if((0,i.Wi)(e))return null;const t=(0,r.mN)(e),s=t.path.match(l)||t.path.match(u);if(!s)return null;const[,n,a,h,c]=s,d=a.indexOf("/");return{title:_(-1!==d?a.slice(d+1):a),serverType:o[h.toLowerCase()],sublayer:null!=c&&""!==c?parseInt(c,10):null,url:{path:n}}}function p(e){const t=(0,r.mN)(e).path.match(h);return t?{serviceUrl:t[1],sublayerId:Number(t[2])}:null}function _(e){return(e=e.replace(/\s*[/_]+\s*/g," "))[0].toUpperCase()+e.slice(1)}function f(e,t){const s=[];if(e){const t=d(e);(0,i.pC)(t)&&t.title&&s.push(t.title)}if(t){const e=_(t);s.push(e)}if(2===s.length){if(s[0].toLowerCase().includes(s[1].toLowerCase()))return s[0];if(s[1].toLowerCase().includes(s[0].toLowerCase()))return s[1]}return s.join(" - ")}function g(e){if(!e)return!1;const t=(e=e.toLowerCase()).includes(".arcgis.com/"),s=e.includes("//services")||e.includes("//tiles")||e.includes("//features");return t&&s}function m(e,t){return e?(0,r.Qj)((0,r.Hu)(e,t)):e}function y(e){let{url:t}=e;if(!t)return{url:t};t=(0,r.Hu)(t,e.logger);const s=(0,r.mN)(t),n=d(s.path);let o;if((0,i.pC)(n))null!=n.sublayer&&null==e.layer.layerId&&(o=n.sublayer),t=n.url.path;else if(e.nonStandardUrlAllowed){const e=p(s.path);(0,i.pC)(e)&&(t=e.serviceUrl,o=e.sublayerId)}return{url:(0,r.Qj)(t),layerId:o}}function v(e,t,s,i,o){(0,n.w)(t,i,"url",o),i.url&&null!=e.layerId&&(i.url=(0,r.v_)(i.url,s,e.layerId.toString()))}function b(e){if(!e)return!1;const t=e.toLowerCase(),s=t.includes("/services/"),i=t.includes("/mapserver/wmsserver"),r=t.includes("/imageserver/wmsserver"),n=t.includes("/wmsserver");return s&&(i||r||n)}},72729:(e,t,s)=>{s.d(t,{im:()=>E,V5:()=>x}),s(80442);var i,r=s(43697),n=s(22974),o=s(5600),a=(s(75215),s(36030)),l=s(52011),u=s(96674);s(67676);let h=i=class extends u.wq{constructor(e){super(e),this.name=null,this.code=null}clone(){return new i({name:this.name,code:this.code})}};(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],h.prototype,"name",void 0),(0,r._)([(0,o.Cb)({type:[String,Number],json:{write:!0}})],h.prototype,"code",void 0),h=i=(0,r._)([(0,l.j)("esri.layers.support.CodedValue")],h);const c=new(s(35454).X)({inherited:"inherited",codedValue:"coded-value",range:"range"});let d=class extends u.wq{constructor(e){super(e),this.name=null,this.type=null}};(0,r._)([(0,o.Cb)({type:String,json:{write:!0}})],d.prototype,"name",void 0),(0,r._)([(0,a.J)(c)],d.prototype,"type",void 0),d=(0,r._)([(0,l.j)("esri.layers.support.Domain")],d);const p=d;var _;let f=_=class extends p{constructor(e){super(e),this.codedValues=null,this.type="coded-value"}getName(e){let t=null;if(this.codedValues){const s=String(e);this.codedValues.some((e=>(String(e.code)===s&&(t=e.name),!!t)))}return t}clone(){return new _({codedValues:(0,n.d9)(this.codedValues),name:this.name})}};(0,r._)([(0,o.Cb)({type:[h],json:{write:!0}})],f.prototype,"codedValues",void 0),(0,r._)([(0,a.J)({codedValue:"coded-value"})],f.prototype,"type",void 0),f=_=(0,r._)([(0,l.j)("esri.layers.support.CodedValueDomain")],f);const g=f;var m;s(92604),s(20102);let y=m=class extends p{constructor(e){super(e),this.type="inherited"}clone(){return new m}};(0,r._)([(0,a.J)({inherited:"inherited"})],y.prototype,"type",void 0),y=m=(0,r._)([(0,l.j)("esri.layers.support.InheritedDomain")],y);const v=y;var b;let C=b=class extends p{constructor(e){super(e),this.maxValue=null,this.minValue=null,this.type="range"}clone(){return new b({maxValue:this.maxValue,minValue:this.minValue,name:this.name})}};(0,r._)([(0,o.Cb)({type:Number,json:{type:[Number],read:{source:"range",reader:(e,t)=>t.range&&t.range[1]},write:{enabled:!1,overridePolicy(){return{enabled:null!=this.maxValue&&null==this.minValue}},target:"range",writer(e,t,s){t[s]=[this.minValue||0,e]}}}})],C.prototype,"maxValue",void 0),(0,r._)([(0,o.Cb)({type:Number,json:{type:[Number],read:{source:"range",reader:(e,t)=>t.range&&t.range[0]},write:{target:"range",writer(e,t,s){t[s]=[e,this.maxValue||0]}}}})],C.prototype,"minValue",void 0),(0,r._)([(0,a.J)({range:"range"})],C.prototype,"type",void 0),C=b=(0,r._)([(0,l.j)("esri.layers.support.RangeDomain")],C);const w=C,x={key:"type",base:p,typeMap:{range:C,"coded-value":g,inherited:v}};function E(e){if(!e||!e.type)return null;switch(e.type){case"range":return w.fromJSON(e);case"codedValue":return g.fromJSON(e);case"inherited":return v.fromJSON(e)}return null}},86719:(e,t,s)=>{s.d(t,{v:()=>i});const i=new(s(35454).X)({esriFieldTypeSmallInteger:"small-integer",esriFieldTypeInteger:"integer",esriFieldTypeSingle:"single",esriFieldTypeDouble:"double",esriFieldTypeLong:"long",esriFieldTypeString:"string",esriFieldTypeDate:"date",esriFieldTypeOID:"oid",esriFieldTypeGeometry:"geometry",esriFieldTypeBlob:"blob",esriFieldTypeRaster:"raster",esriFieldTypeGUID:"guid",esriFieldTypeGlobalID:"global-id",esriFieldTypeXML:"xml"})},99282:(e,t,s)=>{s.d(t,{a:()=>n});var i=s(67900),r=s(68441);const n={inches:(0,i.En)(1,"meters","inches"),feet:(0,i.En)(1,"meters","feet"),"us-feet":(0,i.En)(1,"meters","us-feet"),yards:(0,i.En)(1,"meters","yards"),miles:(0,i.En)(1,"meters","miles"),"nautical-miles":(0,i.En)(1,"meters","nautical-miles"),millimeters:(0,i.En)(1,"meters","millimeters"),centimeters:(0,i.En)(1,"meters","centimeters"),decimeters:(0,i.En)(1,"meters","decimeters"),meters:(0,i.En)(1,"meters","meters"),kilometers:(0,i.En)(1,"meters","kilometers"),"decimal-degrees":1/(0,i.ty)(1,"meters",r.sv.radius)}},28694:(e,t,s)=>{s.d(t,{p:()=>n});var i=s(70586),r=s(69285);function n(e,t,s){if(!s||!s.features||!s.hasZ)return;const n=(0,r.k)(s.geometryType,t,e.outSpatialReference);if(!(0,i.Wi)(n))for(const e of s.features)n(e.geometry)}},86787:(e,t,s)=>{s.d(t,{Z:()=>b});var i,r=s(43697),n=s(35454),o=s(96674),a=s(70586),l=s(5600),u=(s(75215),s(67676),s(71715)),h=s(52011),c=s(30556),d=s(35671);let p=i=class extends o.wq{constructor(e){super(e)}async collectRequiredFields(e,t){return(0,d.io)(e,t,this.expression)}clone(){return new i({expression:this.expression,title:this.title})}equals(e){return this.expression===e.expression&&this.title===e.title}};(0,r._)([(0,l.Cb)({type:String,json:{write:!0}})],p.prototype,"expression",void 0),(0,r._)([(0,l.Cb)({type:String,json:{write:!0}})],p.prototype,"title",void 0),p=i=(0,r._)([(0,h.j)("esri.layers.support.FeatureExpressionInfo")],p);const _=p;var f,g=s(12541);const m=(0,n.w)()({onTheGround:"on-the-ground",relativeToGround:"relative-to-ground",relativeToScene:"relative-to-scene",absoluteHeight:"absolute-height"}),y=new n.X({foot:"feet",kilometer:"kilometers",meter:"meters",mile:"miles","us-foot":"us-feet",yard:"yards"});let v=f=class extends o.wq{constructor(e){super(e),this.offset=null}readFeatureExpressionInfo(e,t){return null!=e?e:t.featureExpression&&0===t.featureExpression.value?{expression:"0"}:void 0}writeFeatureExpressionInfo(e,t,s,i){t[s]=e.write({},i),"0"===e.expression&&(t.featureExpression={value:0})}get mode(){const{offset:e,featureExpressionInfo:t}=this;return this._isOverridden("mode")?this._get("mode"):(0,a.pC)(e)||t?"relative-to-ground":"on-the-ground"}set mode(e){this._override("mode",e)}set unit(e){this._set("unit",e)}write(e,t){return this.offset||this.mode||this.featureExpressionInfo||this.unit?super.write(e,t):null}clone(){return new f({mode:this.mode,offset:this.offset,featureExpressionInfo:this.featureExpressionInfo?this.featureExpressionInfo.clone():void 0,unit:this.unit})}equals(e){return this.mode===e.mode&&this.offset===e.offset&&this.unit===e.unit&&(0,a._W)(this.featureExpressionInfo,e.featureExpressionInfo)}};(0,r._)([(0,l.Cb)({type:_,json:{write:!0}})],v.prototype,"featureExpressionInfo",void 0),(0,r._)([(0,u.r)("featureExpressionInfo",["featureExpressionInfo","featureExpression"])],v.prototype,"readFeatureExpressionInfo",null),(0,r._)([(0,c.c)("featureExpressionInfo",{featureExpressionInfo:{type:_},"featureExpression.value":{type:[0]}})],v.prototype,"writeFeatureExpressionInfo",null),(0,r._)([(0,l.Cb)({type:m.apiValues,nonNullable:!0,json:{type:m.jsonValues,read:m.read,write:{writer:m.write,isRequired:!0}}})],v.prototype,"mode",null),(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],v.prototype,"offset",void 0),(0,r._)([(0,l.Cb)({type:g.f9,json:{type:String,read:y.read,write:y.write}})],v.prototype,"unit",null),v=f=(0,r._)([(0,h.j)("esri.layers.support.ElevationInfo")],v);const b=v},12541:(e,t,s)=>{s.d(t,{Z7:()=>r,f9:()=>n});var i=s(99282);function r(e){return 1/(i.a[e]||1)}const n=function(){const e=Object.keys(i.a);return e.sort(),e}()},65967:(e,t,s)=>{s.r(t),s.d(t,{default:()=>Ce});var i=s(43697),r=s(32448),n=s(61247),o=s(70586),a=s(95330),l=s(17445),u=s(5600),h=(s(75215),s(67676)),c=s(52011),d=s(72762),p=s(82971),_=s(57191),f=s(50245),g=s(11145),m=s(14165),y=s(86787),v=(s(80442),s(13867)),b=s(30175),C=s(67900),w=s(12541);function x(e=!1,t){if(e){const{elevationInfo:e,alignPointsInFeatures:s,spatialReference:i}=t;return new S(e,s,i)}return new E}class E{async alignCandidates(e,t){return e}notifyElevationSourceChange(){}}class S{constructor(e,t,s){this._elevationInfo=e,this._alignPointsInFeatures=t,this.spatialReference=s,this._alignmentsCache=new v.Z(1024),this._cacheVersion=0,this._metersPerVerticalUnit=(0,C._R)(s)}async alignCandidates(e,t){const s=this._elevationInfo;return(0,o.pC)(s)&&"absolute-height"===s.mode&&!s.featureExpressionInfo?(this._alignAbsoluteElevationCandidates(e,s),e):this._alignComputedElevationCandidates(e,t)}notifyElevationSourceChange(){this._alignmentsCache.clear(),this._cacheVersion++}_alignAbsoluteElevationCandidates(e,t){const{offset:s,unit:i}=t;if((0,o.Wi)(s))return;const r=s*((0,w.Z7)(i??"meters")/this._metersPerVerticalUnit);for(const t of e)switch(t.type){case"edge":t.start.z+=r,t.end.z+=r;continue;case"vertex":t.target.z+=r;continue}}async _alignComputedElevationCandidates(e,t){const s=new Map;for(const t of e)(0,b.s1)(s,t.objectId,I).push(t);const[i,r,n]=this._prepareQuery(s),o=await this._alignPointsInFeatures(i,t);if((0,a.k_)(t),n!==this._cacheVersion)return this._alignComputedElevationCandidates(e,t);this._applyCacheAndResponse(i,o,r);const{drapedObjectIds:l,failedObjectIds:u}=o,h=[];for(const t of e){const{objectId:e}=t;l.has(e)&&"edge"===t.type&&(t.draped=!0),u.has(e)||h.push(t)}return h}_prepareQuery(e){const t=[],s=[];for(const[i,r]of e){const e=[];for(const t of r)this._addToQueriesOrCachedResult(i,t.target,e,s),"edge"===t.type&&(this._addToQueriesOrCachedResult(i,t.start,e,s),this._addToQueriesOrCachedResult(i,t.end,e,s));0!==e.length&&t.push({objectId:i,points:e})}return[t,s,this._cacheVersion]}_addToQueriesOrCachedResult(e,t,s,i){const r=T(e,t),n=this._alignmentsCache.get(r);(0,o.pC)(n)?i.push(new F(t,n)):s.push(t)}_applyCacheAndResponse(e,{elevations:t,drapedObjectIds:s,failedObjectIds:i},r){for(const e of r)e.apply();let n=0;const o=this._alignmentsCache;for(const{objectId:r,points:a}of e){if(i.has(r)){n+=a.length;continue}const e=!s.has(r);for(const s of a){const i=T(r,s),a=t[n++];s.z=a,e&&o.put(i,a,1)}}}}class F{constructor(e,t){this.point=e,this.z=t}apply(){this.point.z=this.z}}function T(e,{x:t,y:s,z:i}){return`${e}-${t}-${s}-${i??0}}`}function I(){return[]}class O{filter(e,t){return t}notifyElevationSourceChange(){}}class R{filter(e,t){const{point:s,distance:i}=e,{z:r}=s;if(null==r)return t;if(0===t.length)return t;const n=function(e){return"number"==typeof e?{x:e,y:e,z:e}:e}(i),o=this._updateCandidatesTo3D(t,s,n).filter(M);return o.sort(D),o}_updateCandidatesTo3D(e,t,s){for(const i of e)switch(i.type){case"edge":A(i,t,s);continue;case"vertex":B(i,t,s);continue}return e}}function M(e){return e.distance<=1}function z(e=!1){return e?new R:new O}function A(e,t,{x:s,y:i,z:r}){const{start:n,end:o,target:a}=e;e.draped||function(e,t,s,i){const r=i.x-s.x,n=i.y-s.y,o=i.z-s.z,a=r*r+n*n+o*o,l=(t.x-s.x)*r+(t.y-s.y)*n+o*(t.z-s.z),u=Math.min(1,Math.max(0,l/a)),h=s.x+r*u,c=s.y+n*u,d=s.z+o*u;e.x=h,e.y=c,e.z=d}(a,t,n,o);const l=(t.x-a.x)/s,u=(t.y-a.y)/i,h=(t.z-a.z)/r;e.distance=Math.sqrt(l*l+u*u+h*h)}function B(e,t,{x:s,y:i,z:r}){const{target:n}=e,o=(t.x-n.x)/s,a=(t.y-n.y)/i,l=(t.z-n.z)/r,u=Math.sqrt(o*o+a*a+l*l);e.distance=u}function D(e,t){return e.distance-t.distance}var j=s(22974),P=s(19153);function N(e=!1,t){return e?new U(t):new H}class H{async fetch(){return[]}notifySymbologyChange(){}}class U{constructor(e){this._getSymbologyCandidates=e,this._candidatesCache=new v.Z(1024),this._cacheVersion=0}async fetch(e,t){if(0===e.length)return[];const s=[],i=[],r=this._candidatesCache;for(const t of e){const e=Y(t),n=r.get(e);if(n)for(const e of n)i.push((0,j.d9)(e));else s.push(t),r.put(e,[],1)}if(0===s.length)return i;const n=this._cacheVersion,{candidates:o,sourceCandidateIndices:l}=await this._getSymbologyCandidates(s,t);if((0,a.k_)(t),n!==this._cacheVersion)return this.fetch(e,t);const u=[],{length:h}=o;for(let e=0;e<h;++e){const t=o[e],i=Y(s[l[e]]),n=r.get(i);n.push(t),r.put(i,n,n.length),u.push((0,j.d9)(t))}return i.concat(u)}notifySymbologyChange(){this._candidatesCache.clear(),this._cacheVersion++}}function Y(e){switch(e.type){case"vertex":{const{objectId:t,target:s}=e,i=`${t}-vertex-${s.x}-${s.y}-${s.z??0}`;return(0,P.hP)(i).toString()}case"edge":{const{objectId:t,start:s,end:i}=e,r=`${t}-edge-${s.x}-${s.y}-${s.z??0}-to-${i.x}-${i.y}-${i.z??0}`;return(0,P.hP)(r).toString()}default:return""}}var k=s(15923);let Z=class extends k.Z{constructor(){super(...arguments),this.updating=!1,this._pending=[]}push(e,t){this._pending.push({promise:e,callback:t}),1===this._pending.length&&this._process()}_process(){if(!this._pending.length)return void(this.updating=!1);this.updating=!0;const e=this._pending[0];e.promise.then((t=>e.callback(t))).catch((()=>{})).then((()=>{this._pending.shift(),this._process()}))}};(0,i._)([(0,u.Cb)()],Z.prototype,"updating",void 0),Z=(0,i._)([(0,c.j)("esri.core.AsyncSequence")],Z);var L,V=s(66643),W=s(3920),X=s(50758),q=s(92604),J=s(6570),G=s(24470),$=s(98732),Q=s(66677),K=s(78760),ee=s(34599);class te{constructor(e,t){this.data=e,this.resolution=t,this.state={type:L.CREATED},this.alive=!0}process(e){switch(this.state.type){case L.CREATED:return this.state=this._gotoFetchCount(this.state,e),this.state.task.promise.then(e.resume,e.resume);case L.FETCH_COUNT:break;case L.FETCHED_COUNT:return this.state=this._gotoFetchFeatures(this.state,e),this.state.task.promise.then(e.resume,e.resume);case L.FETCH_FEATURES:break;case L.FETCHED_FEATURES:this.state=this._goToDone(this.state,e);case L.DONE:}return null}get debugInfo(){return{data:this.data,featureCount:this._featureCount,state:this._stateToString}}get _featureCount(){switch(this.state.type){case L.CREATED:case L.FETCH_COUNT:return 0;case L.FETCHED_COUNT:return this.state.featureCount;case L.FETCH_FEATURES:return this.state.previous.featureCount;case L.FETCHED_FEATURES:return this.state.features.length;case L.DONE:return this.state.previous.features.length}}get _stateToString(){switch(this.state.type){case L.CREATED:return"created";case L.FETCH_COUNT:return"fetch-count";case L.FETCHED_COUNT:return"fetched-count";case L.FETCH_FEATURES:return"fetch-features";case L.FETCHED_FEATURES:return"fetched-features";case L.DONE:return"done"}}_gotoFetchCount(e,t){return{type:L.FETCH_COUNT,previous:e,task:(0,V.vr)((async e=>{const s=await(0,V.mt)(t.fetchCount(this,e));this.state.type===L.FETCH_COUNT&&(this.state=this._gotoFetchedCount(this.state,s.ok?s.value:1/0))}))}}_gotoFetchedCount(e,t){return{type:L.FETCHED_COUNT,featureCount:t,previous:e}}_gotoFetchFeatures(e,t){return{type:L.FETCH_FEATURES,previous:e,task:(0,V.vr)((async s=>{const i=await(0,V.mt)(t.fetchFeatures(this,e.featureCount,s));this.state.type===L.FETCH_FEATURES&&(this.state=this._gotoFetchedFeatures(this.state,i.ok?i.value:[]))}))}}_gotoFetchedFeatures(e,t){return{type:L.FETCHED_FEATURES,previous:e,features:t}}_goToDone(e,t){return t.finish(this,e.features),{type:L.DONE,previous:e}}reset(){const e=this.state;switch(this.state={type:L.CREATED},e.type){case L.CREATED:case L.FETCHED_COUNT:case L.FETCHED_FEATURES:case L.DONE:break;case L.FETCH_COUNT:case L.FETCH_FEATURES:e.task.abort()}}intersects(e){return!(!(0,o.Wi)(e)&&this.data.extent)||((0,G.oJ)(e,se),(0,G.kK)(this.data.extent,se))}}!function(e){e[e.CREATED=0]="CREATED",e[e.FETCH_COUNT=1]="FETCH_COUNT",e[e.FETCHED_COUNT=2]="FETCHED_COUNT",e[e.FETCH_FEATURES=3]="FETCH_FEATURES",e[e.FETCHED_FEATURES=4]="FETCHED_FEATURES",e[e.DONE=5]="DONE"}(L||(L={}));const se=(0,G.Ue)();let ie=class extends W.r{get _minimumVerticesPerFeature(){switch(this.store?.featureStore.geometryType){case"esriGeometryPoint":case"esriGeometryMultipoint":return 1;case"esriGeometryPolygon":return 4;case"esriGeometryPolyline":return 2}}set filter(e){const t=this._get("filter"),s=this._filterProperties(e);JSON.stringify(t)!==JSON.stringify(s)&&this._set("filter",s)}set customParameters(e){const t=this._get("customParameters");JSON.stringify(t)!==JSON.stringify(e)&&this._set("customParameters",e)}get _configuration(){return{filter:this.filter,customParameters:this.customParameters,tileInfo:this.tileInfo,tileSize:this.tileSize}}set tileInfo(e){const t=this._get("tileInfo");t!==e&&((0,o.pC)(e)&&(0,o.pC)(t)&&JSON.stringify(e)===JSON.stringify(t)||(this._set("tileInfo",e),this.store.tileInfo=e))}set tileSize(e){this._get("tileSize")!==e&&this._set("tileSize",e)}get updating(){return this.updatingExcludingEdits||this._pendingEdits.updating}get updatingExcludingEdits(){return this.updatingHandles.updating}get hasZ(){return this.store.featureStore.hasZ}constructor(e){super(e),this.tilesOfInterest=[],this.availability=0,this._pendingTiles=new Map,this._pendingEdits=new Z,this._pendingEditsAbortController=new AbortController}initialize(){this._initializeFetchExtent(),this.updatingHandles.add((()=>this._configuration),(()=>this.refresh())),this.updatingHandles.add((()=>this.tilesOfInterest),((e,t)=>{(0,h.fS)(e,t,(({id:e},{id:t})=>e===t))||this._process()}),l.Z_)}destroy(){this._pendingTiles.forEach((e=>this._deletePendingTile(e))),this._pendingTiles.clear(),this.store.destroy(),this.tilesOfInterest.length=0,this._pendingEditsAbortController.abort(),this._pendingEditsAbortController=null}refresh(){this.store.refresh(),this._pendingTiles.forEach((e=>this._deletePendingTile(e))),this._process()}applyEdits(e){this._pendingEdits.push(e,(async e=>{if(0===e.addedFeatures.length&&0===e.updatedFeatures.length&&0===e.deletedFeatures.length)return;for(const[,e]of this._pendingTiles)e.reset();const t={...e,deletedFeatures:e.deletedFeatures.map((({objectId:e,globalId:t})=>e&&-1!==e?e:this._lookupObjectIdByGlobalId(t)))};await this.updatingHandles.addPromise(this.store.processEdits(t,((e,t)=>this._queryFeaturesById(e,t)),this._pendingEditsAbortController.signal)),this._processPendingTiles()}))}_initializeFetchExtent(){if(!this.capabilities.query.supportsExtent||!(0,Q.M8)(this.url))return;const e=(0,V.vr)((async e=>{try{const t=await(0,ee.Vr)(this.url,new m.Z({where:"1=1",outSpatialReference:this.spatialReference,cacheHint:!!this.capabilities.query.supportsCacheHint||void 0}),{query:this._configuration.customParameters,signal:e});this.store.extent=J.Z.fromJSON(t.data?.extent)}catch(e){(0,a.r9)(e),q.Z.getLogger(this.declaredClass).warn("Failed to fetch data extent",e)}}));this.updatingHandles.addPromise(e.promise.then((()=>this._process()))),this.handles.add((0,X.kB)((()=>e.abort())))}get debugInfo(){return{numberOfFeatures:this.store.featureStore.numFeatures,tilesOfInterest:this.tilesOfInterest,pendingTiles:Array.from(this._pendingTiles.values()).map((e=>e.debugInfo)),storedTiles:this.store.debugInfo}}_process(){this._markTilesNotAlive(),this._createPendingTiles(),this._deletePendingTiles(),this._processPendingTiles()}_markTilesNotAlive(){for(const[,e]of this._pendingTiles)e.alive=!1}_createPendingTiles(){const e=this._collectMissingTilesInfo();if(this._setAvailability((0,o.Wi)(e)?1:e.coveredArea/e.fullArea),!(0,o.Wi)(e))for(const{data:t,resolution:s}of e.missingTiles){const e=this._pendingTiles.get(t.id);e?(e.resolution=s,e.alive=!0):this._createPendingTile(t,s)}}_collectMissingTilesInfo(){let e=null;for(let t=this.tilesOfInterest.length-1;t>=0;t--){const s=this.tilesOfInterest[t],i=this.store.process(s,((e,t)=>this._verifyTileComplexity(e,t)));(0,o.Wi)(e)?e=i:e.prepend(i)}return e}_deletePendingTiles(){for(const[,e]of this._pendingTiles)e.alive||this._deletePendingTile(e)}_processPendingTiles(){const e={fetchCount:(e,t)=>this._fetchCount(e,t),fetchFeatures:(e,t,s)=>this._fetchFeatures(e,t,s),finish:(e,t)=>this._finishPendingTile(e,t),resume:()=>this._processPendingTiles()};if(this._ensureFetchAllCounts(e))for(const[,t]of this._pendingTiles)this._verifyTileComplexity(this.store.getFeatureCount(t.data),t.resolution)&&this.updatingHandles.addPromise(t.process(e))}_verifyTileComplexity(e,t){return this._verifyVertexComplexity(e)&&this._verifyFeatureDensity(e,t)}_verifyVertexComplexity(e){return e*this._minimumVerticesPerFeature<oe}_verifyFeatureDensity(e,t){if((0,o.Wi)(this.tileInfo))return!1;const s=this.tileSize*t;return e*(ae/(s*s))<le}_ensureFetchAllCounts(e){let t=!0;for(const[,s]of this._pendingTiles)s.state.type<L.FETCHED_COUNT&&this.updatingHandles.addPromise(s.process(e)),s.state.type<=L.FETCH_COUNT&&(t=!1);return t}_finishPendingTile(e,t){this.store.add(e.data,t),this._deletePendingTile(e),this._updateAvailability()}_updateAvailability(){const e=this._collectMissingTilesInfo();this._setAvailability((0,o.Wi)(e)?1:e.coveredArea/e.fullArea)}_setAvailability(e){this._set("availability",e)}_createPendingTile(e,t){const s=new te(e,t);return this._pendingTiles.set(e.id,s),s}_deletePendingTile(e){e.reset(),this._pendingTiles.delete(e.data.id)}async _fetchCount(e,t){return this.store.fetchCount(e.data,this.url,this._createCountQuery(e),{query:this.customParameters,timeout:ne,signal:t})}async _fetchFeatures(e,t,s){let i=0;const r=[];let n=0,a=t;for(;;){const l=this._createFeaturesQuery(e),u=this._setPagingParameters(l,i,a),{features:h,exceededTransferLimit:c}=await this._queryFeatures(l,s);u&&(i+=(0,o.Wg)(l.num)),n+=h.length;for(const e of h)r.push(e);if(a=t-n,!u||!c||a<=0)return r}}_filterProperties(e){return(0,o.Wi)(e)?{where:"1=1",gdbVersion:void 0,timeExtent:void 0}:{where:e.where||"1=1",timeExtent:e.timeExtent,gdbVersion:e.gdbVersion}}_lookupObjectIdByGlobalId(e){const t=this.globalIdField,s=this.objectIdField;if((0,o.Wi)(t))throw new Error("Expected globalIdField to be defined");let i=null;if(this.store.featureStore.forEach((r=>{e===r.attributes[t]&&(i=r.objectId??r.attributes[s])})),(0,o.Wi)(i))throw new Error(`Expected to find a feature with globalId ${e}`);return i}_queryFeaturesById(e,t){const s=this._createFeaturesQuery();return s.objectIds=e,this._queryFeatures(s,t)}_queryFeatures(e,t){return this.capabilities.query.supportsFormatPBF?this._queryFeaturesPBF(e,t):this._queryFeaturesJSON(e,t)}async _queryFeaturesPBF(e,t){const{sourceSpatialReference:s}=this,{data:i}=await(0,ee.qp)(this.url,e,new K.J({sourceSpatialReference:s}),{query:this._configuration.customParameters,timeout:ne,signal:t});return(0,$.lM)(i)}async _queryFeaturesJSON(e,t){const{sourceSpatialReference:s}=this,{data:i}=await(0,ee.JT)(this.url,e,s,{query:this._configuration.customParameters,timeout:ne,signal:t});return(0,$.h_)(i,this.objectIdField)}_createCountQuery(e){const t=this._createBaseQuery(e);return this.capabilities.query.supportsCacheHint&&(t.cacheHint=!0),t}_createFeaturesQuery(e=null){const t=this._createBaseQuery(e);return t.outFields=this.globalIdField?[this.globalIdField,this.objectIdField]:[this.objectIdField],t.returnGeometry=!0,(0,o.pC)(e)&&(this.capabilities.query.supportsResultType?t.resultType="tile":this.capabilities.query.supportsCacheHint&&(t.cacheHint=!0)),t}_createBaseQuery(e){const t=new m.Z({returnZ:this.hasZ,returnM:!1,geometry:(0,o.pC)(this.tileInfo)&&(0,o.pC)(e)?(0,G.HH)(e.data.extent,this.tileInfo.spatialReference):void 0}),s=this._configuration.filter;return(0,o.pC)(s)&&(t.where=s.where,t.gdbVersion=s.gdbVersion,t.timeExtent=s.timeExtent),t.outSpatialReference=this.spatialReference,t}_setPagingParameters(e,t,s){if(!this.capabilities.query.supportsPagination)return!1;const{supportsMaxRecordCountFactor:i,supportsCacheHint:r,tileMaxRecordCount:n,maxRecordCount:o,supportsResultType:a}=this.capabilities.query,l=i?m.Z.MAX_MAX_RECORD_COUNT_FACTOR:1,u=l*((a||r)&&n?n:o||re);return e.start=t,i?(e.maxRecordCountFactor=Math.min(l,Math.ceil(s/u)),e.num=Math.min(s,e.maxRecordCountFactor*u)):e.num=Math.min(s,u),!0}};(0,i._)([(0,u.Cb)({constructOnly:!0})],ie.prototype,"url",void 0),(0,i._)([(0,u.Cb)({constructOnly:!0})],ie.prototype,"objectIdField",void 0),(0,i._)([(0,u.Cb)({constructOnly:!0})],ie.prototype,"globalIdField",void 0),(0,i._)([(0,u.Cb)({constructOnly:!0})],ie.prototype,"capabilities",void 0),(0,i._)([(0,u.Cb)({constructOnly:!0})],ie.prototype,"sourceSpatialReference",void 0),(0,i._)([(0,u.Cb)({constructOnly:!0})],ie.prototype,"spatialReference",void 0),(0,i._)([(0,u.Cb)({constructOnly:!0})],ie.prototype,"store",void 0),(0,i._)([(0,u.Cb)({readOnly:!0})],ie.prototype,"_minimumVerticesPerFeature",null),(0,i._)([(0,u.Cb)()],ie.prototype,"filter",null),(0,i._)([(0,u.Cb)()],ie.prototype,"customParameters",null),(0,i._)([(0,u.Cb)({readOnly:!0})],ie.prototype,"_configuration",null),(0,i._)([(0,u.Cb)()],ie.prototype,"tileInfo",null),(0,i._)([(0,u.Cb)()],ie.prototype,"tileSize",null),(0,i._)([(0,u.Cb)()],ie.prototype,"tilesOfInterest",void 0),(0,i._)([(0,u.Cb)({readOnly:!0})],ie.prototype,"updating",null),(0,i._)([(0,u.Cb)({readOnly:!0})],ie.prototype,"updatingExcludingEdits",null),(0,i._)([(0,u.Cb)({readOnly:!0})],ie.prototype,"availability",void 0),(0,i._)([(0,u.Cb)()],ie.prototype,"hasZ",null),ie=(0,i._)([(0,c.j)("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTiledFetcher")],ie);const re=2e3,ne=6e5,oe=1e6,ae=25,le=1;var ue=s(43090),he=s(37549),ce=s(43077);class de{constructor(){this._store=new Map,this._byteSize=0}set(e,t){this.delete(e),this._store.set(e,t),this._byteSize+=t.byteSize}delete(e){const t=this._store.get(e);return!!this._store.delete(e)&&(null!=t&&(this._byteSize-=t.byteSize),!0)}get(e){return this._used(e),this._store.get(e)}has(e){return this._used(e),this._store.has(e)}clear(){this._store.clear()}applyByteSizeLimit(e,t){for(const[s,i]of this._store){if(this._byteSize<=e)break;this.delete(s),t(i)}}values(){return this._store.values()}[Symbol.iterator](){return this._store[Symbol.iterator]()}_used(e){const t=this._store.get(e);t&&(this._store.delete(e),this._store.set(e,t))}}let pe=class extends k.Z{constructor(e){super(e),this.tileInfo=null,this.extent=null,this.maximumByteSize=10*ue.Y8.MEGABYTES,this._tileBounds=new he.H,this._tiles=new de,this._refCounts=new Map,this._tileFeatureCounts=new Map,this._tmpBoundingRect=(0,G.Ue)()}add(e,t){const s=[];for(const e of t)this._referenceFeature(e.objectId)===ve.ADDED&&s.push(e);this._addTileStorage(e,new Set(t.map((e=>e.objectId))),function(e){return e.reduce(((e,t)=>e+function(e){return 32+function(e){if((0,o.Wi)(e))return 0;const t=(0,ue.do)(e.lengths,4);return 32+(0,ue.do)(e.coords,8)+t}(e.geometry)+(0,ue.f2)(e.attributes)}(t)),0)}(t)),this.featureStore.addMany(s),this._tiles.applyByteSizeLimit(this.maximumByteSize,(e=>this._removeTileStorage(e)))}destroy(){this.clear(),this._tileFeatureCounts.clear()}clear(){this.featureStore.clear(),this._tileBounds.clear(),this._tiles.clear(),this._refCounts.clear()}refresh(){this.clear(),this._tileFeatureCounts.clear()}processEdits(e,t,s){return this._processEditsDelete(e.deletedFeatures.concat(e.updatedFeatures)),this._processEditsRefetch(e.addedFeatures.concat(e.updatedFeatures),t,s)}_addTileStorage(e,t,s){const i=e.id;this._tiles.set(i,new _e(e,t,s)),this._tileBounds.set(i,e.extent),this._tileFeatureCounts.set(i,t.size)}_remove({id:e}){const t=this._tiles.get(e);t&&this._removeTileStorage(t)}_removeTileStorage(e){const t=[];for(const s of e.objectIds)this._unreferenceFeature(s)===ve.REMOVED&&t.push(s);this.featureStore.removeManyById(t);const s=e.data.id;this._tiles.delete(s),this._tileBounds.delete(s)}_processEditsDelete(e){this.featureStore.removeManyById(e);for(const[,t]of this._tiles){for(const s of e)t.objectIds.delete(s);this._tileFeatureCounts.set(t.data.id,t.objectIds.size)}for(const t of e)this._refCounts.delete(t)}async _processEditsRefetch(e,t,s){const i=(await t(e,s)).features,{hasZ:r,hasM:n}=this.featureStore;for(const e of i){const t=(0,$.$)(this._tmpBoundingRect,e.geometry,r,n);(0,o.Wi)(t)||this._tileBounds.forEachInBounds(t,(t=>{const s=this._tiles.get(t);this.featureStore.add(e);const i=e.objectId;s.objectIds.has(i)||(s.objectIds.add(i),this._referenceFeature(i),this._tileFeatureCounts.set(s.data.id,s.objectIds.size))}))}}process(e,t=(()=>!0)){if((0,o.Wi)(this.tileInfo)||!e.extent||(0,o.pC)(this.extent)&&!(0,G.kK)((0,G.oJ)(this.extent,this._tmpBoundingRect),e.extent))return new ge(e);if(this._tiles.has(e.id))return new ge(e);const s=this._createTileTree(e,this.tileInfo);return this._simplify(s,t,null,0,1),this._collectMissingTiles(e,s,this.tileInfo)}get debugInfo(){return Array.from(this._tiles.values()).map((({data:e})=>({data:e,featureCount:this._tileFeatureCounts.get(e.id)||0})))}getFeatureCount(e){return this._tileFeatureCounts.get(e.id)??0}async fetchCount(e,t,s,i){const r=this._tileFeatureCounts.get(e.id);if(null!=r)return r;const n=await(0,ee.hH)(t,s,i);return this._tileFeatureCounts.set(e.id,n.data.count),n.data.count}_createTileTree(e,t){const s=new fe(e.level,e.row,e.col);return t.updateTileInfo(s,g.Z.ExtrapolateOptions.POWER_OF_TWO),this._tileBounds.forEachInBounds(e.extent,(i=>{const r=this._tiles.get(i)?.data;r&&this._tilesAreRelated(e,r)&&this._populateChildren(s,r,t,this._tileFeatureCounts.get(r.id)||0)})),s}_tilesAreRelated(e,t){if(!e||!t)return!1;if(e.level===t.level)return e.row===t.row&&e.col===t.col;const s=e.level<t.level,i=s?e:t,r=s?t:e,n=1<<r.level-i.level;return Math.floor(r.row/n)===i.row&&Math.floor(r.col/n)===i.col}_populateChildren(e,t,s,i){const r=t.level-e.level-1;if(r<0)return void(e.isLeaf=!0);const n=t.row>>r,a=t.col>>r,l=e.row<<1,u=a-(e.col<<1)+(n-l<<1),h=e.children[u];if((0,o.pC)(h))this._populateChildren(h,t,s,i);else{const r=new fe(e.level+1,n,a);s.updateTileInfo(r,g.Z.ExtrapolateOptions.POWER_OF_TWO),e.children[u]=r,this._populateChildren(r,t,s,i)}}_simplify(e,t,s,i,r){const n=r*r;if(e.isLeaf)return t(this.getFeatureCount(e),r)?0:(this._remove(e),(0,o.pC)(s)&&(s.children[i]=null),n);const a=r/2,l=a*a;let u=0;for(let s=0;s<e.children.length;s++){const i=e.children[s];u+=(0,o.pC)(i)?this._simplify(i,t,e,s,a):l}return 0===u?this._mergeChildren(e):1-u/n<ye&&(this._purge(e),(0,o.pC)(s)&&(s.children[i]=null),u=n),u}_mergeChildren(e){const t=new Set;let s=0;this._forEachLeaf(e,(e=>{const i=this._tiles.get(e.id);if(i){s+=i.byteSize;for(const e of i.objectIds)t.has(e)||(t.add(e),this._referenceFeature(e));this._remove(e)}})),this._addTileStorage(e,t,s),e.isLeaf=!0,e.children[0]=e.children[1]=e.children[2]=e.children[3]=null,this._tileFeatureCounts.set(e.id,t.size)}_forEachLeaf(e,t){for(const s of e.children)(0,o.Wi)(s)||(s.isLeaf?t(s):this._forEachLeaf(s,t))}_purge(e){if(!(0,o.Wi)(e))if(e.isLeaf)this._remove(e);else for(let t=0;t<e.children.length;t++){const s=e.children[t];this._purge(s),e.children[t]=null}}_collectMissingTiles(e,t,s){const i=new me(s,e,this.extent);return this._collectMissingTilesRecurse(t,i,1),i.info}_collectMissingTilesRecurse(e,t,s){if(e.isLeaf)return;if(!e.hasChildren)return void t.addMissing(e.level,e.row,e.col,s);const i=s/2;for(let s=0;s<e.children.length;s++){const r=e.children[s];(0,o.Wi)(r)?t.addMissing(e.level+1,(e.row<<1)+((2&s)>>1),(e.col<<1)+(1&s),i):this._collectMissingTilesRecurse(r,t,i)}}_referenceFeature(e){const t=(this._refCounts.get(e)||0)+1;return this._refCounts.set(e,t),1===t?ve.ADDED:ve.UNCHANGED}_unreferenceFeature(e){const t=(this._refCounts.get(e)||0)-1;return 0===t?(this._refCounts.delete(e),ve.REMOVED):(t>0&&this._refCounts.set(e,t),ve.UNCHANGED)}get test(){return{tiles:Array.from(this._tiles.values()).map((e=>`${e.data.id}:[${Array.from(e.objectIds)}]`)),featureReferences:Array.from(this._refCounts.keys()).map((e=>`${e}:${this._refCounts.get(e)}`))}}};(0,i._)([(0,u.Cb)({constructOnly:!0})],pe.prototype,"featureStore",void 0),(0,i._)([(0,u.Cb)()],pe.prototype,"tileInfo",void 0),(0,i._)([(0,u.Cb)()],pe.prototype,"extent",void 0),(0,i._)([(0,u.Cb)()],pe.prototype,"maximumByteSize",void 0),pe=(0,i._)([(0,c.j)("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTileStore")],pe);class _e{constructor(e,t,s){this.data=e,this.objectIds=t,this.byteSize=s}}class fe{constructor(e,t,s){this.level=e,this.row=t,this.col=s,this.isLeaf=!1,this.extent=null,this.children=[null,null,null,null]}get hasChildren(){return!this.isLeaf&&((0,o.pC)(this.children[0])||(0,o.pC)(this.children[1])||(0,o.pC)(this.children[2])||(0,o.pC)(this.children[3]))}}class ge{constructor(e,t=[]){this.missingTiles=t,this.fullArea=0,this.coveredArea=0,this.fullArea=(0,G.SO)(e.extent),this.coveredArea=this.fullArea}prepend(e){this.missingTiles=e.missingTiles.concat(this.missingTiles),this.coveredArea+=e.coveredArea,this.fullArea+=e.fullArea}}class me{constructor(e,t,s){this._tileInfo=e,this._extent=null,this.info=new ge(t),(0,o.pC)(s)&&(this._extent=(0,G.oJ)(s))}addMissing(e,t,s,i){const r=new ce.f(null,e,t,s);this._tileInfo.updateTileInfo(r,g.Z.ExtrapolateOptions.POWER_OF_TWO),(0,o.Wi)(r.extent)||(0,o.pC)(this._extent)&&!(0,G.kK)(this._extent,r.extent)||(this.info.missingTiles.push({data:r,resolution:i}),this.info.coveredArea-=(0,G.SO)(r.extent))}}const ye=.18751;var ve;!function(e){e[e.ADDED=0]="ADDED",e[e.REMOVED=1]="REMOVED",e[e.UNCHANGED=2]="UNCHANGED"}(ve||(ve={}));let be=class extends r.Z.EventedAccessor{constructor(){super(...arguments),this._isInitializing=!0,this.remoteClient=null,this._whenSetup=(0,a.dD)(),this._elevationAligner=x(),this._elevationFilter=z(),this._symbologyCandidatesFetcher=N(),this._handles=new n.Z,this._updatingHandles=new d.t,this._editsUpdatingHandles=new d.t,this._pendingApplyEdits=new Map,this._alignPointsInFeatures=async(e,t)=>{const s={points:e},i=await this.remoteClient.invoke("alignElevation",s,{signal:t});return(0,a.k_)(t),i},this._getSymbologyCandidates=async(e,t)=>{const s={candidates:e,spatialReference:this._spatialReference.toJSON()},i=await this.remoteClient.invoke("getSymbologyCandidates",s,{signal:t});return(0,a.k_)(t),i}}get updating(){return this.updatingExcludingEdits||this._editsUpdatingHandles.updating||this._featureFetcher.updating}get updatingExcludingEdits(){return this._featureFetcher.updatingExcludingEdits||this._isInitializing||this._updatingHandles.updating}destroy(){this._featureFetcher?.destroy(),this._queryEngine?.destroy(),this._featureStore?.clear(),this._handles?.destroy()}async setup(e){if(this.destroyed)return{result:{}};const{geometryType:t,objectIdField:s,timeInfo:i,fields:r}=e.serviceInfo,{hasZ:n}=e,a=p.Z.fromJSON(e.spatialReference);this._spatialReference=a,this._featureStore=new _.Z({...e.serviceInfo,hasZ:n,hasM:!1}),this._queryEngine=new f.q({spatialReference:e.spatialReference,featureStore:this._featureStore,geometryType:t,fields:r,hasZ:n,hasM:!1,objectIdField:s,timeInfo:i}),this._featureFetcher=new ie({store:new pe({featureStore:this._featureStore}),url:e.serviceInfo.url,objectIdField:e.serviceInfo.objectIdField,globalIdField:e.serviceInfo.globalIdField,capabilities:e.serviceInfo.capabilities,spatialReference:a,sourceSpatialReference:p.Z.fromJSON(e.serviceInfo.spatialReference)});const u="3d"===e.configuration.viewType;return this._elevationAligner=x(u,{elevationInfo:(0,o.pC)(e.elevationInfo)?y.Z.fromJSON(e.elevationInfo):null,alignPointsInFeatures:this._alignPointsInFeatures,spatialReference:a}),this._elevationFilter=z(u),this._handles.add([(0,l.YP)((()=>this._featureFetcher.availability),(e=>this.emit("notify-availability",{availability:e})),l.Z_),(0,l.YP)((()=>this.updating),(()=>this._notifyUpdating()))]),this._whenSetup.resolve(),this._isInitializing=!1,this.configure(e.configuration)}async configure(e){return await this._updatingHandles.addPromise(this._whenSetup.promise),this._updateFeatureFetcherConfiguration(e),{result:{}}}async fetchCandidates(e,t){await this._whenSetup.promise,(0,a.k_)(t);const s=function(e){return{point:e.point,mode:e.mode,distance:e.distance,types:e.types,query:(0,o.pC)(e.filter)?e.filter:{where:"1=1"}}}(e),i=(0,o.pC)(t)?t.signal:null,r=await this._queryEngine.executeQueryForSnapping(s,i);(0,a.k_)(i);const n=await this._elevationAligner.alignCandidates(r.candidates,i);(0,a.k_)(i);const l=await this._symbologyCandidatesFetcher.fetch(n,i);(0,a.k_)(i);const u=0===l.length?n:n.concat(l);return{result:{candidates:this._elevationFilter.filter(s,u)}}}async updateTiles(e,t){return await this._updatingHandles.addPromise(this._whenSetup.promise),(0,a.k_)(t),this._featureFetcher.tileSize=e.tileSize,this._featureFetcher.tilesOfInterest=e.tiles,this._featureFetcher.tileInfo=(0,o.pC)(e.tileInfo)?g.Z.fromJSON(e.tileInfo):null,we}async refresh(e,t){return await this._updatingHandles.addPromise(this._whenSetup.promise),(0,a.k_)(t),this._featureFetcher.refresh(),we}async whenNotUpdating(e,t){return await this._updatingHandles.addPromise(this._whenSetup.promise),(0,a.k_)(t),await(0,l.N1)((()=>!this.updatingExcludingEdits),t),(0,a.k_)(t),we}async getDebugInfo(e,t){return(0,a.k_)(t),{result:this._featureFetcher.debugInfo}}async beginApplyEdits(e,t){this._updatingHandles.addPromise(this._whenSetup.promise),(0,a.k_)(t);const s=(0,a.dD)();return this._pendingApplyEdits.set(e.id,s),this._featureFetcher.applyEdits(s.promise),this._editsUpdatingHandles.addPromise(s.promise),we}async endApplyEdits(e,t){const s=this._pendingApplyEdits.get(e.id);return s&&s.resolve(e.edits),(0,a.k_)(t),we}async notifyElevationSourceChange(e,t){return this._elevationAligner.notifyElevationSourceChange(),we}async notifySymbologyChange(e,t){return this._symbologyCandidatesFetcher.notifySymbologyChange(),we}async setSymbologySnappingSupported(e){return this._symbologyCandidatesFetcher=N(e,this._getSymbologyCandidates),we}_updateFeatureFetcherConfiguration(e){this._featureFetcher.filter=(0,o.pC)(e.filter)?m.Z.fromJSON(e.filter):null,this._featureFetcher.customParameters=e.customParameters}_notifyUpdating(){this.emit("notify-updating",{updating:this.updating})}};(0,i._)([(0,u.Cb)({readOnly:!0})],be.prototype,"updating",null),(0,i._)([(0,u.Cb)({readOnly:!0})],be.prototype,"updatingExcludingEdits",null),(0,i._)([(0,u.Cb)()],be.prototype,"_isInitializing",void 0),be=(0,i._)([(0,c.j)("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceSnappingSourceWorker")],be);const Ce=be,we={result:{}}}}]);