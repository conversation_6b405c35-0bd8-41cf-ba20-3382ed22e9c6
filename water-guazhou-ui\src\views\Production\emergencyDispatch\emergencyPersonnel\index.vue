<!-- 应急人员 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      class="card-table"
      :config="TableConfig"
    />
    <SLDrawer
      ref="refForm"
      :config="addOrUpdateConfig"
    ></SLDrawer>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import { getWaterSupplyTree } from '@/api/company_org'
import { getEmergencyUserTree, getEmergencyUserList, postEmergencyUser, deleteEmergencyUser, postEmergencyUserUpdate } from '@/api/productionScheduling/emergencyDispatch'
import { traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refForm = ref<ISLDrawerIns>()

const refSearch = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '名称', field: 'name', type: 'input' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole()
        },
        {
          perm: true,
          text: '同步平台人员',
          icon: ICONS.SEND,
          type: 'success',
          click: () => {
            SLConfirm('确定同步平台人员？', '同步提示').then(() => {
              postEmergencyUserUpdate().then(() => {
                ElMessage.success('同步成功')
                refreshData()
              }).catch(error => {
                ElMessage.warning(error)
              })
            })
          }
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '组织', prop: 'deptName' },
    // { label: '人员编号', prop: 'stationName' },
    { label: '人员名称', prop: 'name' },
    { label: '联系方式', prop: 'phone' }
  ],
  operationWidth: '240px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: row => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: row => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  width: '500px',
  labelWidth: '100px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true
    let text = '新增成功'
    if (params.id) text = '修改成功'
    postEmergencyUser(params).then(() => {
      addOrUpdateConfig.submitting = false
      refForm.value?.closeDrawer()
      ElMessage.success(text)
      refreshData()
    }).catch(error => {
      addOrUpdateConfig.submitting = false
      ElMessage.warning(error)
    })
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '用户名称',
          field: 'name',
          rules: [{ required: true, message: '请输入用户名称' }]
        },
        {
          type: 'input-number',
          label: '联系方式',
          field: 'phone',
          rules: [{ required: true, message: '请输入联系方式' }]
        },
        {
          type: 'select-tree',
          label: '所属组织',
          field: 'deptId',
          checkStrictly: true,
          options: computed(() => data.WaterSupplyTree) as any,
          rules: [{ required: true, message: '请输入所属组织' }]
        }
      ]
    }
  ]
})

const TreeData = reactive<SLTreeConfig>({
  title: ' ',
  data: [],
  currentProject: {},
  isFilterTree: true,
  treeNodeHandleClick: data => {
    // 设置当前选中项目信息
    TreeData.currentProject = data
    refreshData()
  }
})

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增'
  addOrUpdateConfig.defaultValue = { deptId: TreeData.currentProject.id || {} }
  refForm.value?.openDrawer()
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  addOrUpdateConfig.defaultValue = { category: row.parentId, ...(row) || {} }
  refForm.value?.openDrawer()
}

const handleDelete = (row?: any) => {
  SLConfirm('确定删除该应急人员', '删除提示').then(() => {
    deleteEmergencyUser(row.id).then(() => {
      ElMessage.success('删除成功')
      refreshData()
    }).catch(error => {
      ElMessage.error(error.toString())
    })
  })
}

const data = reactive({
  // 部门
  WaterSupplyTree: [],
  // 获取部门
  getWaterSupplyTreeValue: () => {
    const depth = 2
    getWaterSupplyTree(depth).then(res => {
      data.WaterSupplyTree = traverse(res.data.data || [])
    })
  }
})

function init() {
  getEmergencyUserTree(2).then(res => {
    TreeData.data = traverse(res.data.data || [])
    TreeData.currentProject = res.data.data[0]
  })
}

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    deptId: TreeData.currentProject.id,
    ...(refSearch.value?.queryParams || {})
  }
  getEmergencyUserList(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(async () => {
  init()
  data.getWaterSupplyTreeValue()
})

</script>
