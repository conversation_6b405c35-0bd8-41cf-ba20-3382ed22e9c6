<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.safeProduction.ProductionRunScoreMapper">
    <select id="findList"
            resultType="org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionRunScore">
        SELECT
        a.*
        FROM sp_run_score a
        <where>
            <if test="param.year != null and param.year != ''">
                AND a.month like '%' || #{param.year} || '%'
            </if>
            <if test="param.type != null and param.type != ''">
                and a.type = #{param.type}
            </if>
        </where>
        order by type, month
    </select>
    <select id="yearSum" resultType="com.alibaba.fastjson.JSONObject">
        select sum(value) as value, type
        from sp_run_score
        where month like '%' || #{year} || '%'
        group by type
    </select>
</mapper>