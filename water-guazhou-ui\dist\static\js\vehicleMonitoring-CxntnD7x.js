import{d as u,c as s,r as i,o as _,g as f,n as g,q as n,i as e,t as b,p as h,_ as v,aq as k,C as y}from"./index-r0dFAfgr.js";import{C}from"./index-CcDafpIP.js";const x={class:"onemap-panel-wrapper"},F={class:"table-box"},M=u({__name:"vehicleMonitoring",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(V){const r=s(),o=s([{label:"0 辆",value:"车辆总数"},{label:"0 辆",value:"外出数量"}]),a=i({dataList:[],pagination:{hide:!0},columns:[{minWidth:120,label:"车牌号",prop:"key1"},{minWidth:120,label:"品牌",prop:"key2"},{minWidth:120,label:"用车人",prop:"key3"},{minWidth:120,label:"状态",prop:"key4"}],handleRowClick:t=>{a.currentRow=t}}),p=i({group:[{fields:[{type:"input",field:"layer",append:"刷新"}]}],labelPosition:"top",gutter:12});return _(()=>{}),(t,l)=>{const c=v,d=k;return f(),g("div",x,[n(e(C),{modelValue:e(o),"onUpdate:modelValue":l[0]||(l[0]=m=>b(o)?o.value=m:null),span:12,style:{"margin-bottom":"10px"}},null,8,["modelValue"]),n(c,{ref_key:"refForm",ref:r,config:e(p)},null,8,["config"]),h("div",F,[n(d,{config:e(a)},null,8,["config"])])])}}}),B=y(M,[["__scopeId","data-v-df5d902f"]]);export{B as default};
