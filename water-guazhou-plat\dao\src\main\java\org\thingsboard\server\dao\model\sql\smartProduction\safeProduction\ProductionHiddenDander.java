package org.thingsboard.server.dao.model.sql.smartProduction.safeProduction;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName("sp_production_hidden_danger")
@NoArgsConstructor
@AllArgsConstructor
public class ProductionHiddenDander {

    private String id;

    private String code;

    private String name;

    private String level;

    private String remark;

    private String status;

    private String creator;

    @TableField(exist = false)
    private String creatorName;

    private Date createTime;

    private String processUser;

    @TableField(exist = false)
    private String processUserName;

    private Date processTime;

    private String processRemark;

    private String tenantId;

    private String workOrderId;

    private String reviewRemark;

    private Date reviewTime;


    @TableField(exist = false)
    private String workOrderCode;

    @TableField(exist = false)
    private String workOrderName;

}
