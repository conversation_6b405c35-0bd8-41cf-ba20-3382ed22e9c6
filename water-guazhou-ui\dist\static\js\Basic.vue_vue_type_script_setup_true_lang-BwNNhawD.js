import{d as f,c as p,r as c,u as r,S as u,b as d,g as m,h as b,i as _,_ as g}from"./index-r0dFAfgr.js";const h=f({__name:"Basic",setup(y){var o;const t=p(),n=c({group:[{fields:[{type:"input",label:"网站名称",field:"SITENAME"},{type:"input",label:"后端接口地址",field:"apiURL",readonly:!0},{type:"input",label:"bim接口地址",field:"bimUrl"},{type:"input",label:"rtsp视频流地址",field:"rtspUrl"},{type:"input",label:"营收（旧版本）接口地址",field:"yinshouUrl"},{type:"input",label:"fineReport接口地址",field:"fineReportURL"},{type:"input",label:"radarImg接口地址",field:"radarImgUrl"}]},{fields:[{type:"btn-group",btns:[{perm:!0,text:"保存",click:()=>{var e;return(e=t.value)==null?void 0:e.Submit()}},{perm:!0,text:"重置",click:()=>{var e;return(e=t.value)==null?void 0:e.resetForm()}}]}]}],labelPosition:"right",labelWidth:"100px",defaultValue:{...window.SITE_CONFIG||{},...((o=r().tenantInfo)==null?void 0:o.additionalInfo)||{}},submit:e=>{i(e)}}),i=e=>{u("确定保存？","提示信息").then(async()=>{n.submitting=!0;try{console.log(e),await r().InitTenantInfo(),l()}catch(a){d.error("保存失败"),console.log(a)}}).catch(()=>{})},l=()=>{var e;n.defaultValue={...window.SITE_CONFIG||{},...((e=r().tenantInfo)==null?void 0:e.additionalInfo)||{}}};return(e,a)=>{const s=g;return m(),b(s,{ref_key:"refForm",ref:t,config:_(n)},null,8,["config"])}}});export{h as _};
