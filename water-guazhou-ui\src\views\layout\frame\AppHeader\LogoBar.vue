<template>
  <div class="app-logo">
    <!-- 使用 SVG 组件来显示 SVG 图片 -->
    <div v-if="isSvg" class="svg-container" @click="gotoHome">
      <component :is="svgComponent" />
    </div>
    <!-- 如果不是 SVG，使用普通图片 -->
    <img v-else :src="appStore.logo" alt="logo" @click="gotoHome" />
  </div>
</template>
<script lang="ts" setup>
import { getLogo } from '@/api/tenant';
import { useAppStore } from '@/store';
import { useRouter } from 'vue-router';
import { onMounted, ref, computed, h } from 'vue';

const appStore = useAppStore();
const router = useRouter();

// 判断是否是 SVG 格式
const isSvg = computed(() => {
  const logoUrl = appStore.logo;
  return typeof logoUrl === 'string' && logoUrl.toLowerCase().endsWith('.svg');
});

// 动态创建 SVG 组件
const svgContent = ref('');
const svgComponent = computed(() => {
  if (!svgContent.value) return null;

  // 使用 h 函数创建组件
  return h('div', {
    innerHTML: svgContent.value,
    class: 'svg-content'
  });
});

// 加载 SVG 内容
const loadSvgContent = async (url) => {
  try {
    const response = await fetch(url);
    if (response.ok) {
      const text = await response.text();
      // 检查是否是有效的 SVG
      if (text.includes('<svg')) {
        svgContent.value = text;
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error('Failed to load SVG:', error);
    return false;
  }
};

const gotoHome = () => {
  router.push({ path: '/home' });
};

onMounted(async () => {
  try {
    const res = await getLogo();
    appStore.ToggleLogo(res.data);

    // 如果是 SVG，尝试加载 SVG 内容
    if (isSvg.value) {
      await loadSvgContent(appStore.logo);
    }
  } catch (error) {
    console.error('Failed to load logo:', error);
  }
  // appStore.SetAppVersion()
});
</script>
<style lang="scss" scoped>
.app-logo {
  height: 100%;

  img {
    cursor: pointer;
    height: 100%;
    width: 100%;
  }

  .svg-container {
    cursor: pointer;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    :deep(svg) {
      height: 100%;
      width: 100%;
    }
  }

  .svg-content {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
