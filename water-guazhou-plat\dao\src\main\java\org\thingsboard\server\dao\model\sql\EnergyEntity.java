/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.energy.Energy;
import org.thingsboard.server.common.data.id.EnergyId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.SearchTextEntity;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static org.thingsboard.server.dao.model.ModelConstants.*;

/**
 * <AUTHOR>
 * @date 2018-12-12
 */


@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ENERGY)
public class EnergyEntity extends BaseSqlEntity<Energy> implements SearchTextEntity<Energy> {
    @Column(name = ALARM_TENANT_ID_PROPERTY)
    private String tenantId;
    @Column(name = ENERGY_UNIT)
    private String unit;
    @Column(name = ENERGY_TYPE)
    private String energyType;
    @Column(name = ENERGY_NAME)
    private String energyName;
    @Column(name = ENERGY_ENABLE)
    private boolean enable;
    @Column(name = SEARCH_TEXT_PROPERTY)
    private String searchText;
    @Column(name = ENTITY_SEQUENCE)
    private Integer sequence;

    @Type(type = "json")
    @Column(name = ModelConstants.ASSET_ADDITIONAL_INFO_PROPERTY)
    private JsonNode additionalInfo;

    public EnergyEntity() {
    }

    @Override
    public String getSearchTextSource() {
        return energyName;
    }

    @Override
    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

    public EnergyEntity(Energy energy) {
        if (energy.getId() != null)
            this.setId(energy.getId().getId());
        this.tenantId = UUIDConverter.fromTimeUUID(energy.getTenantId().getId());
        this.energyName = energy.getEnergyName();
        this.energyType = energy.getEnergyType();
        this.unit = energy.getUnit();
        this.enable = energy.isEnable();
        this.sequence = energy.getSequence();
        this.additionalInfo = energy.getAdditionalInfo();
        this.searchText = energy.getSearchText();
    }

    @Override
    public Energy toData() {
        Energy energy = new Energy(new EnergyId(UUIDConverter.fromString(id)));
        if (tenantId != null) {
            energy.setTenantId(new TenantId(UUIDConverter.fromString(tenantId)));
        }
        energy.setEnergyName(this.energyName);
        energy.setEnergyType(this.energyType);
        energy.setUnit(this.unit);
        energy.setEnable(this.enable);
        energy.setSequence(this.sequence);
        energy.setAdditionalInfo(this.additionalInfo);
        return energy;
    }
}
