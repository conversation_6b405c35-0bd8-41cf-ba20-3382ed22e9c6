<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitConfigMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           "type",
                           item_type,
                           "name",
                           "method",
                           "require",
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_circuit_config -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitConfig">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="item_type" property="itemType"/>
        <result column="name" property="name"/>
        <result column="method" property="method"/>
        <result column="require" property="require"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_circuit_config cfg
        <where>
            <if test="type != null and type != ''">
                and "type" = #{type}
            </if>
            <if test="itemType != null and itemType != ''">
                and item_type in (
                <foreach collection="itemType.split(',')" item="element" separator=",">
                    #{element}
                </foreach>)
            </if>
            <if test="name != null and name != ''">
                and "name" like '%'|| #{name} ||'%'
            </if>
            <if test="templateId != null and templateId != ''">
                and (select count(id) > 0
                     from sp_circuit_template template
                     where template.settings like '%' || cfg.id || '%' and template.id = #{templateId})
            </if>
            <!--            <if test="method != null and method != ''">-->
            <!--                and "method" = #{method}-->
            <!--            </if>-->
            <!--            <if test="require != null and require != ''">-->
            <!--                and "require" = #{require}-->
            <!--            </if>-->
            <!--            <if test="creator != null and creator != ''">-->
            <!--                and creator = #{creator}-->
            <!--            </if>-->
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sp_circuit_config
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="itemType != null">
                item_type = #{itemType},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="method != null">
                method = #{method},
            </if>
            <if test="require != null">
                require = #{require},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getTypes" resultType="java.lang.String">
        select distinct item_type
        from sp_circuit_config
        where tenant_id = #{tenantId}
    </select>

    <select id="getItemTypes" resultType="java.lang.String">
        select circuit_config_resolve_multi_id(#{multiId})
    </select>
</mapper>