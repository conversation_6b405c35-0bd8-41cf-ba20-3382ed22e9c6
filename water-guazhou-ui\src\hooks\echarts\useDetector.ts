import detector from 'element-resize-detector'

export const useDetector = () => {
  const resizer = detector()
  let element: any
  const listenTo = (div?: HTMLElement, refChart?: any) => {
    div && resizer.listenTo(div, () => refChart.value?.resize?.())
    element = div
  }
  const listenToMush = (
    div?: HTMLElement,
    resize?: (e?: HTMLElement) => any
  ) => {
    if (!div) return
    element = div
    div && resizer.listenTo(div, e => resize?.(e))
  }
  onBeforeUnmount(() => {
    resizer.removeAllListeners(element)
  })
  return {
    listenTo,
    listenToMush
  }
}
export default useDetector
