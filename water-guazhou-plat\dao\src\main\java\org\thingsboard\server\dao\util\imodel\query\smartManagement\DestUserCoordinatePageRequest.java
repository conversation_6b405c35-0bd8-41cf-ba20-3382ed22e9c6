package org.thingsboard.server.dao.util.imodel.query.smartManagement;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinate;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class DestUserCoordinatePageRequest extends AdvancedPageableQueryEntity<UserCoordinate, DestUserCoordinatePageRequest> {
    // 用户id
    @NotNullOrEmpty
    private String userId;
}
