package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.*;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.request.PipeSaleWaterReportRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.*;
import org.thingsboard.server.dao.sql.revenue.CopyDataReadMeterDataMapper;
import org.thingsboard.server.dao.sql.smartPipe.PartitionMountMapper;
import org.thingsboard.server.dao.sql.smartPipe.PipeCopyDataReadMeterDataMapper;
import org.thingsboard.server.dao.sql.smartPipe.PipePartitionTotalFlowMapper;
import org.thingsboard.server.dao.util.InfluxUtil;
import org.thingsboard.server.dao.util.TimeUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class PipeStatisticsReportServiceImpl implements PipeStatisticsReportService {

    @Autowired
    private PartitionService partitionService;

    @Autowired
    private PartitionMountMapper partitionMountMapper;

    @Autowired
    private PipeCopyDataReadMeterDataMapper pipeCopyDataReadMeterDataMapper;

    @Autowired
    private InfluxUtil influxUtil;

    @Autowired
    private PipePartitionCustService pipePartitionCustService;

    @Autowired
    private CopyDataReadMeterDataMapper copyDataReadMeterDataMapper;

    @Autowired
    private PartitionLossEvaluateService partitionLossEvaluateService;

    @Autowired
    private PartitionLossPointService partitionLossPointService;
    @Autowired
    private PipePartitionTotalFlowMapper pipePartitionTotalFlowMapper;

    @Override
    // @Cacheable(cacheNames = "nrwReport", key = "{#type,#date,#start,#end,#tenantId,#partitionIdList}")
    public JSONObject getNrwReport(String type, String date, String start, String end, String tenantId, List<String> partitionIdList) {
        // 先找所有分区
        Map queryMap = new HashMap();
        queryMap.put("tenantId", tenantId);
        queryMap.put("type", "2");
        queryMap.put("partitionIdList", partitionIdList);
        List<Partition> partitionList = partitionService.getAll(queryMap);

        if (partitionIdList == null || partitionIdList.size() == 0) {
            partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        }
        Long startTime;
        Long endTime;

        Map<String, LocalDate> localDateByType = TimeUtils.getLocalDateByType(type, date, start, end);
        LocalDate startDate = localDateByType.get("start");
        LocalDate endDate = localDateByType.get("end");

        startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        List<String> headerList = new ArrayList<>();
        int endIndex = 4;
        if ("year".equals(type) || "month".equals(type)) {
            endIndex = 7;
            while (startDate.isBefore(endDate)) {
                headerList.add(DateTimeFormatter.ofPattern("yyyy-MM").format(startDate));
                startDate = startDate.plusMonths(1L);
            }
        } else if ("yearInterval".equals(type)) {
            while (startDate.isBefore(endDate)) {
                headerList.add(DateTimeFormatter.ofPattern("yyyy").format(startDate));
                startDate = startDate.plusYears(1L);
            }
        }

        // 分区供水量
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "in", tenantId);
        List<String> deviceIdList = new ArrayList<>();
        Map<String, String> deviceIdPartitionIdMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceIdList.add(partitionMount.getDeviceId());
            deviceIdPartitionIdMap.put(partitionMount.getDeviceId(), partitionMount.getPartitionId());
        }

        Map<String, JSONObject> supplyResult = new HashMap<>();
        JSONObject supplyTempObject;
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal sale = BigDecimal.ZERO;
        List<PipePartitionTotalFlow> data = pipePartitionTotalFlowMapper.getAllByTime(startTime, endTime, tenantId, "3", deviceIdList);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        for (PipePartitionTotalFlow dataFlow : data) {
            date = format.format(dataFlow.getCollectTime()).substring(0, endIndex);
            supplyTempObject = supplyResult.get(deviceIdPartitionIdMap.get(dataFlow.getDeviceId()));
            if (supplyTempObject == null) {
                supplyTempObject = new JSONObject();
                supplyResult.put(deviceIdPartitionIdMap.get(dataFlow.getDeviceId()), supplyTempObject);
            }
            if (supplyTempObject.get(date) == null) {
                supplyTempObject.put(date, BigDecimal.ZERO);
            }
            BigDecimal add = supplyTempObject.getBigDecimal(date).add(dataFlow.getValue());
            total = total.add(dataFlow.getValue());
            supplyTempObject.put(date, add);
        }

        // 分区售水量
        List<PipeCopyDataReadMeterData> pipeCopyDataReadMeterDataList = pipeCopyDataReadMeterDataMapper.getListByPartitionId(partitionIdList, startTime, endTime);
        Map<String, JSONObject> saleResult = new HashMap<>();
        for (PipeCopyDataReadMeterData pipeCopyDataReadMeterData : pipeCopyDataReadMeterDataList) {
            if (StringUtils.isBlank(pipeCopyDataReadMeterData.getYm()) || pipeCopyDataReadMeterData.getTotalWater() == null) {
                continue;
            }
            pipeCopyDataReadMeterData.setYm(pipeCopyDataReadMeterData.getYm().substring(0, 4) + "-" + pipeCopyDataReadMeterData.getYm().substring(4));
            date = pipeCopyDataReadMeterData.getYm().substring(0, endIndex);
            supplyTempObject = saleResult.get(pipeCopyDataReadMeterData.getPartitionId());
            if (supplyTempObject == null) {
                supplyTempObject = new JSONObject();
                saleResult.put(pipeCopyDataReadMeterData.getPartitionId(), supplyTempObject);
            }
            if (supplyTempObject.get(date) == null) {
                supplyTempObject.put(date, BigDecimal.ZERO);
            }
            BigDecimal add = supplyTempObject.getBigDecimal(date).add(pipeCopyDataReadMeterData.getTotalWater());
            sale = sale.add(pipeCopyDataReadMeterData.getTotalWater());
            supplyTempObject.put(date, add);
        }

        List dataList = new ArrayList<>();
        List dataListForApp = new ArrayList<>();
        JSONObject resultObject;
        JSONObject resultObjectForApp;
        JSONObject saleTempObject;
        BigDecimal totalSupply;
        BigDecimal totalSale;
        BigDecimal rootNRW = BigDecimal.ZERO;
        for (Partition partition : partitionList) {
            totalSupply = BigDecimal.ZERO;
            totalSale = BigDecimal.ZERO;
            resultObject = new JSONObject();
            resultObjectForApp = new JSONObject();
            resultObject.put("name", partition.getName());
            resultObject.put("data", new JSONArray());
            resultObjectForApp.put("name", partition.getName());
            if (DataConstants.PARTITION_TYPE.METERING.getValue().equals(partition.getType())) {
                resultObjectForApp.put("type", "计量分区");
            } else {
                resultObjectForApp.put("type", "DMA分区");
            }

            // 数据处理
            supplyTempObject = supplyResult.get(partition.getId());
            saleTempObject = saleResult.get(partition.getId());
            if (supplyTempObject == null) {
                supplyTempObject = new JSONObject();
            }
            if (saleTempObject == null) {
                saleTempObject = new JSONObject();
            }
            for (String header : headerList) {
                if (supplyTempObject.get(header) == null) {
                    supplyTempObject.put(header, BigDecimal.ZERO);
                }
                if (saleTempObject.get(header) == null) {
                    saleTempObject.put(header, BigDecimal.ZERO);
                }
                try {
                    resultObject.getJSONArray("data").add((supplyTempObject.getBigDecimal(header).subtract(saleTempObject.getBigDecimal(header))).multiply(BigDecimal.valueOf(100)).divide(supplyTempObject.getBigDecimal(header), 2, RoundingMode.HALF_UP));
                } catch (Exception e) {
                    resultObject.getJSONArray("data").add(BigDecimal.ZERO);
                }
                totalSupply = totalSupply.add(supplyTempObject.getBigDecimal(header));
                totalSale = totalSale.add(saleTempObject.getBigDecimal(header));
            }
            try {
                BigDecimal nrw = (totalSupply.subtract(totalSale)).multiply(BigDecimal.valueOf(100)).divide(totalSupply, 2, RoundingMode.HALF_UP);
                resultObject.getJSONArray("data").add(nrw);
                resultObjectForApp.put("nrw", nrw);
                if (StringUtils.isBlank(partition.getPid())) {
                    rootNRW = nrw;
                }
            } catch (Exception e) {
                resultObjectForApp.put("nrw", BigDecimal.ZERO);
                resultObject.getJSONArray("data").add(BigDecimal.ZERO);
            }
            dataListForApp.add(resultObjectForApp);
            dataList.add(resultObject);
        }

        BigDecimal lastMonthNrw = BigDecimal.ZERO;
        try {
            lastMonthNrw = (total.subtract(sale)).multiply(BigDecimal.valueOf(100)).divide(total, 2, RoundingMode.HALF_UP);
        } catch (Exception e) {
        }

        headerList.add("年产销差");

        JSONObject result = new JSONObject();
        result.put("lastMonthNrw", lastMonthNrw);
        result.put("data", dataList);
        result.put("header", headerList);
        result.put("rootNRW", rootNRW);
        result.put("partitionNum", dataListForApp.size());
        result.put("dataForApp", dataListForApp);
        return result;
    }

    @Override
    @Cacheable(cacheNames = "getNrwDetailReport", key = "{#date,#type,#start,#end,#tenantId,#partitionIdList}")
    public List<PipeNrwReportDetailDTO> getNrwDetailReport(String partitionIds, String type, String date, String start, String end, String tenantId) {
        Map<String, LocalDate> localDateByType = TimeUtils.getLocalDateByType(type, date, start, end);
        LocalDate startDate = localDateByType.get("start");
        LocalDate endDate = localDateByType.get("end");
        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        if (StringUtils.isBlank(partitionIds)) {
            partitionIds = partitionService.getAllId(tenantId).stream().collect(Collectors.joining(","));
        }

        List<String> partitionIdList = Arrays.asList(partitionIds.split(","));
        List<Partition> partitionList = partitionService.getListByIdIn(partitionIdList).stream().filter(a -> "5".equals(a.getStatus())).collect(Collectors.toList());
        Map<String, PipeNrwReportDetailDTO> resultMap = new LinkedHashMap<>();
        // 挂接用户数
        Map<String, Integer> userNumMap = partitionService.getUserNum(partitionIdList);

        PipeNrwReportDetailDTO reportDetailDTO;
        for (Partition partition : partitionList) {
            reportDetailDTO = new PipeNrwReportDetailDTO();
            reportDetailDTO.setPartitionId(partition.getId());
            reportDetailDTO.setPartitionName(partition.getName());
            reportDetailDTO.setUserNum(userNumMap.get(partition.getId()));

            resultMap.put(partition.getId(), reportDetailDTO);
        }


        // 分区供水量
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "in", tenantId);
        List<String> deviceIdList = new ArrayList<>();
        Map<String, String> deviceIdPartitionIdMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceIdList.add(partitionMount.getDeviceId() + ".total_flow");
            deviceIdPartitionIdMap.put(partitionMount.getDeviceId() + ".total_flow", partitionMount.getPartitionId());
        }

        Map<String, PipeNrwReportDetailDTO> supplyResult = new HashMap<>();
        JSONObject data = influxUtil.getData(deviceIdList, startTime, endTime, "1nc");
        List<String> timeList = data.keySet().stream().collect(Collectors.toList());
        for (String time : timeList) {
            if (data.getJSONObject(time) == null) {
                continue;
            }
            for (String deviceId : deviceIdList) {
                if (data.getJSONObject(time).getBigDecimal(deviceId) == null) {
                    continue;
                }
                reportDetailDTO = resultMap.get(deviceIdPartitionIdMap.get(deviceId));
                reportDetailDTO.setSupplyTotal(reportDetailDTO.getSupplyTotal().add(data.getJSONObject(time).getBigDecimal(deviceId)));
                // 进水量
                reportDetailDTO.setInWater(reportDetailDTO.getSupplyTotal());
            }
        }

        // 分区修正售水量
        List<PipeCopyDataReadMeterData> pipeCopyDataReadMeterDataList = pipeCopyDataReadMeterDataMapper.getListByPartitionId(partitionIdList, startTime, endTime);
        Map<String, PipeNrwReportDetailDTO> saleResult = new HashMap<>();
        for (PipeCopyDataReadMeterData pipeCopyDataReadMeterData : pipeCopyDataReadMeterDataList) {
            if (StringUtils.isBlank(pipeCopyDataReadMeterData.getYm()) || pipeCopyDataReadMeterData.getTotalWater() == null) {
                continue;
            }
            reportDetailDTO = resultMap.get(pipeCopyDataReadMeterData.getPartitionId());
            reportDetailDTO.setCorrectUseWater(reportDetailDTO.getCorrectUseWater().add(pipeCopyDataReadMeterData.getCorrectWater()));
        }

        // 出水量
        partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "out", tenantId);
        deviceIdList = new ArrayList<>();
        deviceIdPartitionIdMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceIdList.add(partitionMount.getDeviceId() + ".total_flow");
            deviceIdPartitionIdMap.put(partitionMount.getDeviceId() + ".total_flow", partitionMount.getPartitionId());
        }

        supplyResult = new HashMap<>();
        data = influxUtil.getData(deviceIdList, startTime, endTime, "1nc");
        timeList = data.keySet().stream().collect(Collectors.toList());
        for (String time : timeList) {
            if (data.getJSONObject(time) == null) {
                continue;
            }
            for (String deviceId : deviceIdList) {
                if (data.getJSONObject(time).getBigDecimal(deviceId) == null) {
                    continue;
                }
                reportDetailDTO = resultMap.get(deviceIdPartitionIdMap.get(deviceId));
                reportDetailDTO.setOutWater(reportDetailDTO.getOutWater().add(data.getJSONObject(time).getBigDecimal(deviceId)));
            }
        }

        List<PipeNrwReportDetailDTO> result = resultMap.values().stream().map(a -> {
            // 校准产销差
            try {
                a.setCorrectNrwRate((a.getSupplyTotal().subtract(a.getCorrectUseWater())).multiply(BigDecimal.valueOf(100)).divide(a.getSupplyTotal()));
            } catch (Exception e) {
                a.setCorrectNrwRate(a.getSupplyTotal());
            }
            a.setNoIncomeWater(a.getSupplyTotal().subtract(a.getCorrectUseWater()));

            return a;
        }).collect(Collectors.toList());

        return result;
    }

    @Override
    @Cacheable(cacheNames = "getNrwDetailReportDetail", key = "{#partitionId,#type,#date,#start,#end,#tenantId}")
    public List<PipeNrwReportDetailDTO> getNrwDetailReportDetail(String partitionId, String type, String date, String start, String end, String tenantId) {
        Map<String, LocalDate> localDateByType = TimeUtils.getLocalDateByType(type, date, start, end);
        LocalDate startDate = localDateByType.get("start");
        LocalDate endDate = localDateByType.get("end");
        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        Partition partition = partitionService.getById(partitionId);
        if (partition == null) {
            return new ArrayList<>();
        }


        // 挂接用户数
        Map<String, Integer> userNumMap = partitionService.getUserNum(Collections.singletonList(partitionId));

        Map<String, PipeNrwReportDetailDTO> resultMap = new LinkedHashMap<>();
        PipeNrwReportDetailDTO reportDetailDTO;
        while (startDate.isBefore(endDate)) {
            reportDetailDTO = new PipeNrwReportDetailDTO();
            reportDetailDTO.setPartitionId(DateTimeFormatter.ofPattern("yyyy-MM").format(startDate));
            reportDetailDTO.setPartitionName(partition.getName());
            reportDetailDTO.setUserNum(userNumMap.get(partition.getId()));
            resultMap.put(DateTimeFormatter.ofPattern("yyyy-MM").format(startDate), reportDetailDTO);

            startDate = startDate.plusMonths(1L);
        }

        // 分区供水量
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(Collections.singletonList(partitionId), "1", "in", tenantId);
        List<String> deviceIdList = new ArrayList<>();
        Map<String, String> deviceIdPartitionIdMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceIdList.add(partitionMount.getDeviceId() + ".total_flow");
            deviceIdPartitionIdMap.put(partitionMount.getDeviceId() + ".total_flow", partitionMount.getPartitionId());
        }

        Map<String, PipeNrwReportDetailDTO> supplyResult = new HashMap<>();
        JSONObject data = influxUtil.getData(deviceIdList, startTime, endTime, "1nc");
        List<String> timeList = data.keySet().stream().collect(Collectors.toList());
        for (String time : timeList) {
            if (data.getJSONObject(time) == null) {
                continue;
            }
            date = time.substring(0, 7);
            for (String deviceId : deviceIdList) {
                if (data.getJSONObject(time).getBigDecimal(deviceId) == null) {
                    continue;
                }
                reportDetailDTO = resultMap.get(time);
                reportDetailDTO.setSupplyTotal(reportDetailDTO.getSupplyTotal().add(data.getJSONObject(time).getBigDecimal(deviceId)));
                // 进水量
                reportDetailDTO.setInWater(reportDetailDTO.getSupplyTotal());
            }
        }

        // 分区修正售水量
        List<PipeCopyDataReadMeterData> pipeCopyDataReadMeterDataList = pipeCopyDataReadMeterDataMapper.getListByPartitionId(Collections.singletonList(partitionId), startTime, endTime);
        for (PipeCopyDataReadMeterData pipeCopyDataReadMeterData : pipeCopyDataReadMeterDataList) {
            if (StringUtils.isBlank(pipeCopyDataReadMeterData.getYm()) || pipeCopyDataReadMeterData.getTotalWater() == null) {
                continue;
            }
            reportDetailDTO = resultMap.get(pipeCopyDataReadMeterData.getYm().substring(0, 4) + "-" + pipeCopyDataReadMeterData.getYm().substring(4, 6));
            reportDetailDTO.setCorrectUseWater(reportDetailDTO.getCorrectUseWater().add(pipeCopyDataReadMeterData.getCorrectWater()));
        }

        // 出水量
        partitionMountList = partitionMountMapper.getAllByPartitionId(Collections.singletonList(partitionId), "1", "out", tenantId);
        deviceIdList = new ArrayList<>();
        deviceIdPartitionIdMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceIdList.add(partitionMount.getDeviceId() + ".total_flow");
            deviceIdPartitionIdMap.put(partitionMount.getDeviceId() + ".total_flow", partitionMount.getPartitionId());
        }

        data = influxUtil.getData(deviceIdList, startTime, endTime, "1nc");
        timeList = data.keySet().stream().collect(Collectors.toList());
        for (String time : timeList) {
            if (data.getJSONObject(time) == null) {
                continue;
            }
            for (String deviceId : deviceIdList) {
                if (data.getJSONObject(time).getBigDecimal(deviceId) == null) {
                    continue;
                }
                reportDetailDTO = resultMap.get(time);
                reportDetailDTO.setOutWater(reportDetailDTO.getOutWater().add(data.getJSONObject(time).getBigDecimal(deviceId)));
            }
        }

        List<PipeNrwReportDetailDTO> result = resultMap.values().stream().map(a -> {
            // 校准产销差
            try {
                a.setCorrectNrwRate((a.getSupplyTotal().subtract(a.getCorrectUseWater())).multiply(BigDecimal.valueOf(100)).divide(a.getSupplyTotal()));
            } catch (Exception e) {
                a.setCorrectNrwRate(a.getSupplyTotal());
            }
            a.setNoIncomeWater(a.getSupplyTotal().subtract(a.getCorrectUseWater()));

            return a;
        }).collect(Collectors.toList());

        return result;
    }

    @Override
    public List<String> getPartitionSupplyHeader(String partitionId, String tenantId) {
        // 查找下级分区
        List<Partition> partitionList = partitionService.getchildByPid(partitionId, tenantId);
        if (partitionList.size() == 0) {
            Partition partition = partitionService.getById(partitionId);
            if (partition != null) {
                partitionList.add(partition);
            }
        }
        if (partitionList.size() == 0) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();
        for (Partition partition : partitionList) {
            result.add(partition.getName());
            result.add(partition.getName() + "占比(%)");
        }
        return result;
    }

    @Override
    public List<JSONObject> getPartitionSupply(String partitionId, String type, String date, String start, String end, String tenantId) {
        List<String> partitionSupplyHeader = this.getPartitionSupplyHeader(partitionId, tenantId);
        if (partitionSupplyHeader.size() == 0) {
            return new ArrayList<>();
        }
        List<Partition> partitionList = partitionService.getchildByPid(partitionId, tenantId);
        List<String> partitionIdList = new ArrayList<>();
        Map<String, String> partitionNameMap = new HashMap<>();
        for (Partition partition : partitionList) {
            partitionIdList.add(partition.getId());
            partitionNameMap.put(partition.getId(), partition.getName());
        }
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "in", tenantId);

        String typeTemp = type;
        if ("month".equals(typeTemp)) {
            typeTemp = "monthOne";
        }

        Map<String, LocalDate> localDateByType = TimeUtils.getLocalDateByType(typeTemp, date, start, end);
        LocalDate startDate = localDateByType.get("start");
        LocalDate endDate = localDateByType.get("end");
        Map<String, String> deviceMap = new HashMap<>();
        List<String> deviceList = new ArrayList<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceList.add(partitionMount.getDeviceId() + ".total_flow");
            deviceMap.put(partitionMount.getDeviceId() + ".total_flow", partitionNameMap.get(partitionMount.getPartitionId()));
        }
        Map<String, JSONObject> resultMap = new LinkedHashMap<>();
        JSONObject dataMap;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String influxType = "1d";
        switch (type) {
            case "month":
            case "day":
                formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                break;
            case "year":
                formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                influxType = "1nc";
        }

        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        while (startDate.isBefore(endDate)) {
            dataMap = new JSONObject();
            dataMap.put("date", formatter.format(startDate));
            for (String header : partitionSupplyHeader) {
                dataMap.put(header, BigDecimal.ZERO);
            }

            dataMap.put("sum", BigDecimal.ZERO);
            dataMap.put("difference", BigDecimal.ZERO);
            dataMap.put("sumAll", BigDecimal.ZERO);
            resultMap.put(formatter.format(startDate), dataMap);

            if ("year".equals(type)) {
                startDate = startDate.plusMonths(1L);
            } else {
                startDate = startDate.plusDays(1L);
            }
        }

        JSONObject data = influxUtil.getData(deviceList, startTime, endTime, influxType);

        List<String> timeList = data.keySet().stream().collect(Collectors.toList());
        BigDecimal value;
        for (String time : timeList) {
            if (data.get(time) == null) {
                continue;
            }
            dataMap = resultMap.get(time);
            for (String deviceId : deviceList) {
                value = data.getJSONObject(time).getBigDecimal(deviceId);
                if (value == null || deviceMap.get(deviceId) == null) {
                    continue;
                }
                if (dataMap.getBigDecimal(deviceMap.get(deviceId)) == null) {
                    dataMap.put(deviceMap.get(deviceId), BigDecimal.ZERO);
                }
                dataMap.put(deviceMap.get(deviceId), dataMap.getBigDecimal(deviceMap.get(deviceId)).add(value));

                dataMap.put("sum", dataMap.getBigDecimal("sum").add(value));
                dataMap.put("sumAll", dataMap.getBigDecimal("sumAll").add(value));
                dataMap.put("difference", dataMap.getBigDecimal("sum").subtract(dataMap.getBigDecimal("sumAll")));
            }
            // 分区供水占比
            for (String header : partitionSupplyHeader) {
                try {
                    if (header.contains("占比(%)")) {
                        continue;
                    }

                    dataMap.put(header + "占比(%)", dataMap.getBigDecimal(header).multiply(BigDecimal.valueOf(100)).divide(dataMap.getBigDecimal("sumAll")));
                } catch (Exception e) {
                    dataMap.put(header + "占比(%)", BigDecimal.ZERO);
                }
            }
        }


        return resultMap.values().stream().collect(Collectors.toList());
    }

    @Override
    public JSONObject getPartitionInOutWater(String partitionId, String type, String date, String start, String end, String tenantId) {
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(Collections.singletonList(partitionId), "1", "", tenantId);
        List<String> hearderList = new ArrayList<>();

        String typeTemp = type;
        if ("month".equals(typeTemp)) {
            typeTemp = "monthOne";
        }

        Map<String, LocalDate> localDateByType = TimeUtils.getLocalDateByType(typeTemp, date, start, end);
        LocalDate startDate = localDateByType.get("start");
        LocalDate endDate = localDateByType.get("end");
        Map<String, String> deviceMap = new HashMap<>();
        List<String> deviceList = new ArrayList<>();
        String header;
        for (PartitionMount partitionMount : partitionMountList) {
            deviceList.add(partitionMount.getDeviceId() + ".total_flow");

            header = partitionMount.getName();
            switch (DataConstants.PARTITION_MOUNT_DIRECTION.getByValue(partitionMount.getDirection())) {
                case POSITIVE_IN_NEGATIVE_OUT:
                    header = header + "(" + DataConstants.PARTITION_MOUNT_DIRECTION.POSITIVE_IN_NEGATIVE_OUT.getName() + ")";
                    break;
                case NEGATIVE_IN_POSITIVE_OUT:
                    header = header + "(" + DataConstants.PARTITION_MOUNT_DIRECTION.NEGATIVE_IN_POSITIVE_OUT.getName() + ")";
                    break;
                case IN:
                    header = header + "(" + DataConstants.PARTITION_MOUNT_DIRECTION.IN.getName() + ")";
                    break;
                case OUT:
                    header = header + "(" + DataConstants.PARTITION_MOUNT_DIRECTION.OUT.getName() + ")";
                    break;
            }
            deviceMap.put(partitionMount.getDeviceId() + ".total_flow", header);
            hearderList.add(header);
        }
        Map<String, JSONObject> resultMap = new LinkedHashMap<>();
        JSONObject dataMap;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String influxType = "1d";
        switch (type) {
            case "month":
            case "day":
                formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                break;
            case "year":
                formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                influxType = "1nc";
        }


        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        while (startDate.isBefore(endDate)) {
            dataMap = new JSONObject();
            dataMap.put("date", formatter.format(startDate));
            for (String header1 : hearderList) {
                dataMap.put(header1, BigDecimal.ZERO);
            }
            if ("year".equals(type)) {
                startDate = startDate.plusMonths(1L);
            } else {
                startDate = startDate.plusDays(1L);
            }
            resultMap.put(formatter.format(startDate), dataMap);
        }

        JSONObject data = influxUtil.getData(deviceList, startTime, endTime, influxType);

        List<String> timeList = data.keySet().stream().collect(Collectors.toList());
        BigDecimal value;
        for (String time : timeList) {
            if (data.get(time) == null) {
                continue;
            }
            dataMap = resultMap.get(time);
            for (String deviceId : deviceList) {
                value = data.getJSONObject(time).getBigDecimal(deviceId);
                if (value == null) {
                    continue;
                }
                if (dataMap.getBigDecimal(deviceMap.get(deviceId)) == null) {
                    dataMap.put(deviceMap.get(deviceId), BigDecimal.ZERO);
                }
                dataMap.put(deviceMap.get(deviceId), dataMap.getBigDecimal(deviceMap.get(deviceId)).add(value));

            }
        }

        JSONObject result = new JSONObject();
        result.put("header", hearderList);
        result.put("data", resultMap.values().stream().collect(Collectors.toList()));

        return result;
    }

    @Override
    public List<PipeDayFlowReportDTO> getDayFlow(String partitionIds, String day, String tenantId) {
        if (StringUtils.isBlank(partitionIds)) {
            partitionIds = partitionService.getAllId(tenantId).stream().collect(Collectors.joining(","));
        }

        List<String> partitionIdList = Arrays.asList(partitionIds.split(","));
        List<Partition> partitionList = partitionService.getListByIdIn(partitionIdList);
        Map<String, PipeDayFlowReportDTO> resultMap = new LinkedHashMap<>(); // 原始值
        Map<String, PipeDayFlowReportDTO> chainCompareMap = new LinkedHashMap<>(); // 环比
        Map<String, PipeDayFlowReportDTO> sameCompareMap = new LinkedHashMap<>(); // 同比
        // 上级分区
        Map<String, PipeDayFlowReportDTO> parentResultMap = new LinkedHashMap<>(); // 原始值
        Map<String, PipeDayFlowReportDTO> parentChainCompareMap = new LinkedHashMap<>(); // 环比
        Map<String, PipeDayFlowReportDTO> parentSameCompareMap = new LinkedHashMap<>(); // 同比
        // 上级分区映射关系
        Map<String, String> parentIdMap = new HashMap<>();
        List<String> parentPartitionIdList = new ArrayList<>();
        PipeDayFlowReportDTO pipeDayFlowReportDTO;
        for (Partition partition : partitionList) {
            pipeDayFlowReportDTO = new PipeDayFlowReportDTO();
            pipeDayFlowReportDTO.setPartitionType(DataConstants.PARTITION_TYPE.getByValue(partition.getType()).getName());
            pipeDayFlowReportDTO.setPartitionName(partition.getName());
            resultMap.put(partition.getId(), pipeDayFlowReportDTO);
            if (StringUtils.isNotBlank(partition.getPid())) {
                parentIdMap.put(partition.getId(), partition.getPid());
                parentPartitionIdList.add(partition.getPid());

                parentResultMap.put(partition.getPid(), new PipeDayFlowReportDTO());
                parentChainCompareMap.put(partition.getPid(), new PipeDayFlowReportDTO());
                parentSameCompareMap.put(partition.getPid(), new PipeDayFlowReportDTO());
            }

            // 环比
            chainCompareMap.put(partition.getId(), new PipeDayFlowReportDTO());
            // 同比
            sameCompareMap.put(partition.getId(), new PipeDayFlowReportDTO());
        }

        Map<String, String> deviceMap = new HashMap<>();
        List<String> deviceList = new ArrayList<>();
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "in", tenantId);
        for (PartitionMount partitionMount : partitionMountList) {
            deviceMap.put(partitionMount.getDeviceId() + ".total_flow", partitionMount.getPartitionId());
            deviceList.add(partitionMount.getDeviceId() + ".total_flow");
        }

        List<Long> startTimeList = new ArrayList<>();
        List<Long> endTimeList = new ArrayList<>();
        // 环比
        List<Long> chainStartTimeList = new ArrayList<>();
        List<Long> chainEndTimeList = new ArrayList<>();
        // 同比
        List<Long> sameStartTimeList = new ArrayList<>();
        List<Long> sameEndTimeList = new ArrayList<>();
        // 本日流量
        LocalDate startDate = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = startDate.plusDays(1L);
        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        startTimeList.add(startTime);
        endTimeList.add(endTime);
        // 环比
        LocalDate tempStartDate = startDate.minusDays(1L);
        LocalDate tempEndDate = endDate.minusDays(1L);
        startTime = tempStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = tempEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        chainStartTimeList.add(startTime);
        chainEndTimeList.add(endTime);
        // 同比
        tempStartDate = startDate.minusYears(1L);
        tempEndDate = endDate.minusYears(1L);
        startTime = tempStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = tempEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        sameStartTimeList.add(startTime);
        sameEndTimeList.add(endTime);

        // 本旬流量
        tempStartDate = startDate.minusDays(10L);
        startTime = tempStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        startTimeList.add(startTime);
        endTimeList.add(endTime);
        // 环比
        tempStartDate = startDate.minusDays(10L);
        tempEndDate = endDate.minusDays(10L);
        startTime = tempStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = tempEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        chainStartTimeList.add(startTime);
        chainEndTimeList.add(endTime);
        // 同比
        tempStartDate = startDate.minusDays(10L).minusYears(1L);
        tempEndDate = endDate.minusYears(1L);
        startTime = tempStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = tempEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        sameStartTimeList.add(startTime);
        sameEndTimeList.add(endTime);

        // 本月流量
        tempStartDate = LocalDate.of(startDate.getYear(), startDate.getMonthValue(), 1);
        tempEndDate = tempStartDate.plusMonths(1L);
        startTime = tempStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = tempEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        startTimeList.add(startTime);
        endTimeList.add(endTime);
        // 环比
        tempStartDate = tempStartDate.minusMonths(1L);
        tempEndDate = tempEndDate.minusMonths(1L);
        startTime = tempStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = tempEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        chainStartTimeList.add(startTime);
        chainEndTimeList.add(endTime);
        // 同比
        tempStartDate = tempStartDate.plusMonths(1L).minusYears(1L);
        tempEndDate = tempEndDate.plusMonths(1L).minusYears(1L);
        startTime = tempStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = tempEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        sameStartTimeList.add(startTime);
        sameEndTimeList.add(endTime);

        // 本年流量
        tempStartDate = LocalDate.of(startDate.getYear(), 1, 1);
        tempEndDate = tempStartDate.plusYears(1L);
        startTime = tempStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = tempEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        startTimeList.add(startTime);
        endTimeList.add(endTime);
        // 同比
        tempStartDate = tempStartDate.minusYears(1L);
        tempEndDate = tempEndDate.minusYears(1L);
        startTime = tempStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = tempEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        sameStartTimeList.add(startTime);
        sameEndTimeList.add(endTime);
        // 环比占位
        chainStartTimeList.add(startTime);
        chainEndTimeList.add(endTime);

        // 时间类型
        List<String> timeTypeList = Arrays.asList("1d", "1d", "1nc", "1yc");
        List<String> fieldsList = Arrays.asList("dayFlow", "tenDaysFlow", "monthFlow", "yearFlow");
        Field declaredField;
        Class<PipeDayFlowReportDTO> pipeDayFlowReportDTOClass = PipeDayFlowReportDTO.class;

        List<Map<String, PipeDayFlowReportDTO>> resultMapList = new ArrayList<>();
        resultMapList.add(resultMap);
        resultMapList.add(chainCompareMap);
        resultMapList.add(sameCompareMap);
        List<List<Long>> startTimeListList = new ArrayList<>();
        startTimeListList.add(startTimeList);
        startTimeListList.add(chainStartTimeList);
        startTimeListList.add(sameStartTimeList);

        List<List<Long>> endTimeListList = new ArrayList<>();
        endTimeListList.add(endTimeList);
        endTimeListList.add(chainEndTimeList);
        endTimeListList.add(sameEndTimeList);

        Map<String, PipeDayFlowReportDTO> tempMap;

        // 时间优化
        ExecutorService executorService = Executors.newCachedThreadPool();

        // 三个类型循环
        BigDecimal flow;
        List<String> timeList;
        JSONObject data;
        for (int j = 0; j < resultMapList.size(); j++) {
            tempMap = resultMapList.get(j);
            for (int i = 0; i < startTimeList.size(); i++) {
                int finalJ = j;
                int finalI = i;
                Map<String, PipeDayFlowReportDTO> finalTempMap = tempMap;
                executorService.execute(() -> {
                    JSONObject data1 = influxUtil.getData(deviceList, startTimeListList.get(finalJ).get(finalI), endTimeListList.get(finalJ).get(finalI), timeTypeList.get(finalI));
                    if (data1 != null) {
                        List<String> timeList1 = data1.keySet().stream().collect(Collectors.toList());
                        for (String time : timeList1) {
                            for (String deviceId : deviceList) {
                                if (data1.getJSONObject(time) == null || data1.getJSONObject(time).getBigDecimal(deviceId) == null) {
                                    continue;
                                }
                                BigDecimal flow1 = data1.getJSONObject(time).getBigDecimal(deviceId);
                                try {
                                    Field declaredField1 = pipeDayFlowReportDTOClass.getDeclaredField(fieldsList.get(finalI));
                                    declaredField1.setAccessible(true);
                                    declaredField1.set(finalTempMap.get(deviceMap.get(deviceId)), ((BigDecimal) declaredField1.get(finalTempMap.get(deviceMap.get(deviceId)))).add(flow1));
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                });
            }
        }

        // 父级流量
        List<String> parentDeviceList = new ArrayList<>();
        List<PartitionMount> parentPartitionMountList = partitionMountMapper.getAllByPartitionId(parentPartitionIdList, "1", "in", tenantId);
        if (parentPartitionMountList.size() > 0) {
            for (PartitionMount partitionMount : parentPartitionMountList) {
                parentDeviceList.add(partitionMount.getDeviceId() + ".total_flow");
            }
        }

        resultMapList.clear();
        resultMapList.add(parentResultMap);
        resultMapList.add(parentChainCompareMap);
        resultMapList.add(parentSameCompareMap);

        // 三个类型循环
        for (int j = 0; j < resultMapList.size(); j++) {
            tempMap = resultMapList.get(j);
            for (int i = 0; i < startTimeList.size(); i++) {
                int finalI = i;
                Map<String, PipeDayFlowReportDTO> finalTempMap1 = tempMap;
                int finalJ = j;
                executorService.submit(() -> {
                    JSONObject data1 = influxUtil.getData(parentDeviceList, startTimeListList.get(finalJ).get(finalI), endTimeListList.get(finalJ).get(finalI), timeTypeList.get(finalI));
                    if (data1 != null) {
                        List<String> timeList1 = data1.keySet().stream().collect(Collectors.toList());
                        for (String time : timeList1) {
                            for (String deviceId : deviceList) {
                                if (data1.getJSONObject(time) == null || data1.getJSONObject(time).getBigDecimal(deviceId) == null) {
                                    continue;
                                }
                                BigDecimal flow1 = data1.getJSONObject(time).getBigDecimal(deviceId);
                                try {
                                    Field declaredField1 = pipeDayFlowReportDTOClass.getDeclaredField(fieldsList.get(finalI));
                                    declaredField1.setAccessible(true);
                                    declaredField1.set(finalTempMap1.get(deviceMap.get(deviceId)), ((BigDecimal) declaredField1.get(finalTempMap1.get(deviceMap.get(deviceId)))).add(flow1));
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                });
            }
        }
        // 等待线程执行完
        executorService.shutdown();
        while (!executorService.isTerminated()) {

        }

        // 结果处理
        Iterator<String> iterator = resultMap.keySet().iterator();
        String partitionId;
        PipeDayFlowReportDTO tempDTO;
        PipeDayFlowReportDTO parentTempDTO;
        PipeDayFlowReportDTO parentDTO;
        BigDecimal value;
        List<String> changeRateFields = Arrays.asList("dayChangeRate", "tenDaysChangeRate", "monthChangeRate");
        List<String> huanbiRateFields = Arrays.asList("dayFlowCompareToLastDayRate", "tenDaysFlowCompareToLastTenDaysRate", "monthFlowCompareToLastMonthRate");
        List<String> tongbiRateFields = Arrays.asList("dayFlowCompareToLastYearRate", "tenDaysFlowCompareToLastYearRate", "monthFlowCompareToLastYearRate", "yearFlowCompareToLastYearRate");
        List<String> divideParentRateFields = Arrays.asList("dayFlowDivideParentRate", "tenDaysFlowDivideParentRate", "monthFlowDivideParentRate", "yearFlowDivideParentRate");
        List<String> divideParentCompareLastDayRateFields = Arrays.asList("dayFlowDivideParentCompareToLastDayRate", "tenDaysFlowDivideParentCompareToLastTenDaysRate", "monthFlowDivideParentCompareToLastMonthRate");
        List<String> divideParentCompareLastYearRateFields = Arrays.asList("dayFlowDivideParentCompareToLastYearRate", "tenDaysFlowDivideParentCompareToLastYearRate", "monthFlowDivideParentCompareToLastYearRate", "yearFlowDivideParentCompareToLastYearRate");
        Field flowField;
        while (iterator.hasNext()) {
            partitionId = iterator.next();
            for (int i = 0; i < fieldsList.size(); i++) {
                try {
                    flowField = pipeDayFlowReportDTOClass.getDeclaredField(fieldsList.get(i));
                    flowField.setAccessible(true);
                    // 日
                    pipeDayFlowReportDTO = resultMap.get(partitionId);
                    tempDTO = chainCompareMap.get(partitionId);
                    // 环比
                    try {
                        declaredField = pipeDayFlowReportDTOClass.getDeclaredField(huanbiRateFields.get(i));
                        declaredField.setAccessible(true);
                        declaredField.set(pipeDayFlowReportDTO, ((BigDecimal) flowField.get(pipeDayFlowReportDTO)).subtract((BigDecimal) flowField.get(tempDTO)).multiply(BigDecimal.valueOf(100)).divide((BigDecimal) flowField.get(tempDTO), 2, RoundingMode.HALF_UP));
                    } catch (Exception e) {
                    }
                    // 增减率
                    try {
                        declaredField = pipeDayFlowReportDTOClass.getDeclaredField(changeRateFields.get(i));
                        declaredField.setAccessible(true);
                        declaredField.set(pipeDayFlowReportDTO, ((BigDecimal) flowField.get(pipeDayFlowReportDTO)).subtract((BigDecimal) flowField.get(tempDTO)).multiply(BigDecimal.valueOf(100)).divide((BigDecimal) flowField.get(pipeDayFlowReportDTO), 2, RoundingMode.HALF_UP));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    // 同比去年
                    try {
                        declaredField = pipeDayFlowReportDTOClass.getDeclaredField(tongbiRateFields.get(i));
                        declaredField.setAccessible(true);
                        tempDTO = sameCompareMap.get(partitionId);
                        declaredField.set(pipeDayFlowReportDTO, ((BigDecimal) flowField.get(pipeDayFlowReportDTO)).subtract((BigDecimal) flowField.get(tempDTO)).multiply(BigDecimal.valueOf(100)).divide((BigDecimal) flowField.get(tempDTO), 2, RoundingMode.HALF_UP));
                    } catch (Exception e) {
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                // 同上级比
                try {
                    flowField = pipeDayFlowReportDTOClass.getDeclaredField(fieldsList.get(i));
                    flowField.setAccessible(true);
                    pipeDayFlowReportDTO = resultMap.get(partitionId);
                    tempDTO = parentResultMap.get(partitionId);
                    // 占上级比
                    try {
                        declaredField = pipeDayFlowReportDTOClass.getDeclaredField(divideParentRateFields.get(i));
                        declaredField.setAccessible(true);
                        declaredField.set(pipeDayFlowReportDTO, ((BigDecimal) flowField.get(pipeDayFlowReportDTO)).multiply(BigDecimal.valueOf(100)).divide((BigDecimal) flowField.get(tempDTO), 2, RoundingMode.HALF_UP));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    // 占上级比环比
                    try {
                        // 环比值
                        tempDTO = chainCompareMap.get(partitionId);
                        parentTempDTO = parentChainCompareMap.get(parentIdMap.get(partitionId));
                        declaredField = pipeDayFlowReportDTOClass.getDeclaredField(divideParentRateFields.get(i));
                        declaredField.setAccessible(true);
                        declaredField.set(tempDTO, ((BigDecimal) flowField.get(tempDTO)).multiply(BigDecimal.valueOf(100)).divide((BigDecimal) flowField.get(parentTempDTO), 2, RoundingMode.HALF_UP));

                        flowField = pipeDayFlowReportDTOClass.getDeclaredField(divideParentRateFields.get(i));
                        flowField.setAccessible(true);

                        declaredField = pipeDayFlowReportDTOClass.getDeclaredField(divideParentCompareLastDayRateFields.get(i));
                        declaredField.setAccessible(true);
                        declaredField.set(pipeDayFlowReportDTO, ((BigDecimal) flowField.get(pipeDayFlowReportDTO)).subtract((BigDecimal) flowField.get(tempDTO)).multiply(BigDecimal.valueOf(100)).divide((BigDecimal) flowField.get(pipeDayFlowReportDTO), 2, RoundingMode.HALF_UP));
                    } catch (Exception e) {
                    }

                    // 同比去年
                    try {
                        // 同比值
                        tempDTO = sameCompareMap.get(partitionId);
                        parentTempDTO = parentSameCompareMap.get(parentIdMap.get(partitionId));
                        declaredField = pipeDayFlowReportDTOClass.getDeclaredField(divideParentRateFields.get(i));
                        declaredField.setAccessible(true);
                        declaredField.set(tempDTO, ((BigDecimal) flowField.get(tempDTO)).multiply(BigDecimal.valueOf(100)).divide((BigDecimal) flowField.get(parentTempDTO), 2, RoundingMode.HALF_UP));

                        flowField = pipeDayFlowReportDTOClass.getDeclaredField(divideParentRateFields.get(i));
                        flowField.setAccessible(true);

                        declaredField = pipeDayFlowReportDTOClass.getDeclaredField(divideParentCompareLastYearRateFields.get(i));
                        declaredField.setAccessible(true);
                        declaredField.set(pipeDayFlowReportDTO, ((BigDecimal) flowField.get(pipeDayFlowReportDTO)).subtract((BigDecimal) flowField.get(tempDTO)).multiply(BigDecimal.valueOf(100)).divide((BigDecimal) flowField.get(pipeDayFlowReportDTO), 2, RoundingMode.HALF_UP));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return resultMap.values().stream().collect(Collectors.toList());
    }

    @Override
    public JSONObject getSaleWater(PipeSaleWaterReportRequest request) {
        JSONObject result = new JSONObject();

        // 获取月份
        LocalDate startDate = LocalDate.of(Integer.valueOf(request.getYear()), 1, 1);
        LocalDate endDate = startDate.plusYears(1L);
        if (startDate.getYear() == LocalDate.now().getYear()) {
            endDate = LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonthValue(), 1).plusMonths(1L);
        }
        List<JSONObject> monthList = new ArrayList<>();
        JSONObject monthObject;
        LocalDate tempDate = startDate;
        while (tempDate.isBefore(endDate)) {
            monthObject = new JSONObject();
            monthObject.put("label", tempDate.getMonthValue() + "月");
            monthObject.put("value", "month" + tempDate.getMonthValue());
            monthList.add(monthObject);
            tempDate = tempDate.plusMonths(1L);
        }

        // 所有下级分区
        List<String> partitionIdList = new ArrayList<>();
        partitionService.getAllChildId(request.getPartitionId(), partitionIdList);
        PartitionCustRequest partitionCustRequest = new PartitionCustRequest();
        partitionCustRequest.setPage(request.getPage());
        partitionCustRequest.setSize(request.getSize());
        partitionCustRequest.setCustCode(request.getCustCode());
        partitionCustRequest.setCustName(request.getCustName());
        partitionCustRequest.setMeterBookId(request.getMeterBookId());
        partitionCustRequest.setWaterCategory(request.getWaterCategory());
        partitionCustRequest.setPartitionId(request.getPartitionId());
        partitionCustRequest.setTenantId(request.getTenantId());
        PageData<PipePartitionCust> pageData = pipePartitionCustService.getList(partitionCustRequest);
        List<PipePartitionCust> data = pageData.getData();
        result.put("total", pageData.getTotal());
        result.put("monthList", monthList);

        if (data.size() == 0) {
            result.put("dataList", new ArrayList<>());
            return result;
        }
        Map<String, JSONObject> custDataMap = new HashMap<>();
        // 售水数据初始化
        JSONObject tempData;
        List<String> custCodeList = new ArrayList<>();
        for (PipePartitionCust pipePartitionCust : data) {
            tempData = new JSONObject();
            tempData.put("custCode", pipePartitionCust.getCustCode());
            tempData.put("custName", pipePartitionCust.getCustName());
            tempData.put("partitionName", pipePartitionCust.getPartitionName());
            tempData.put("WaterCategory", pipePartitionCust.getWaterCategory());
            tempData.put("meterBookName", pipePartitionCust.getMeterBookName());

            for (JSONObject month : monthList) {
                tempData.put(month.getString("value"), BigDecimal.ZERO);
            }

            tempData.put("sum", BigDecimal.ZERO);
            tempData.put("avg", BigDecimal.ZERO);

            custDataMap.put(pipePartitionCust.getCustCode(), tempData);
            custCodeList.add(pipePartitionCust.getCustCode());
        }
        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        List<ReadMeterDataDTO> readMeterDataDTOList = copyDataReadMeterDataMapper.getListByCustCodeList(custCodeList, startTime, endTime, request.getTenantId());
        String month;
        for (ReadMeterDataDTO readMeterDataDTO : readMeterDataDTOList) {
            month = "month" + readMeterDataDTO.getDataTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getMonthValue();
            try {
                custDataMap.get(readMeterDataDTO.getUserCode()).put(month, custDataMap.get(readMeterDataDTO.getUserCode()).getBigDecimal(month).add(readMeterDataDTO.getTotalWater()));
                // 合计
                custDataMap.get(readMeterDataDTO.getUserCode()).put("sum", custDataMap.get(readMeterDataDTO.getUserCode()).getBigDecimal("sum").add(readMeterDataDTO.getTotalWater()));

            } catch (Exception e) {
            }
        }

        // 数据处理
        List<JSONObject> dataList = custDataMap.values().stream().collect(Collectors.toList());
        for (JSONObject jsonObject : dataList) {
            try {
                jsonObject.put("avg", jsonObject.getBigDecimal("sum").divide(BigDecimal.valueOf(monthList.size()), 2, RoundingMode.HALF_UP));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        result.put("total", pageData.getTotal());
        result.put("dataList", dataList);
        result.put("monthList", monthList);

        return result;
    }

    @Override
    public List<DMAOverviewDTO> getDMAOverview(String status, String partitionName, String tenantId) {
        List<DMAOverviewDTO> partitionList = partitionService.getDMAOverview(status, partitionName, tenantId);
        if (partitionList.size() == 0) {
            return new ArrayList<>();
        }

        List<String> partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        // 注册用户数
        Map<String, Integer> userNum = partitionService.getUserNum(partitionIdList);
        // 大用户数
        Map<String, Integer> bigUserNum = partitionService.getMountNum(partitionIdList, "2", "");
        // 进水口数量
        Map<String, Integer> inWaterNum = partitionService.getMountNum(partitionIdList, "1", "in");
        // 出水口数量
        Map<String, Integer> outWaterNum = partitionService.getMountNum(partitionIdList, "1", "out");
        for (DMAOverviewDTO dmaOverviewDTO : partitionList) {
            dmaOverviewDTO.setRevenueUserNum(userNum.get(dmaOverviewDTO.getId()));
            dmaOverviewDTO.setBigUserNum(bigUserNum.get(dmaOverviewDTO.getId()));
            dmaOverviewDTO.setInWaterNum(inWaterNum.get(dmaOverviewDTO.getId()));
            dmaOverviewDTO.setOutWaterNum(outWaterNum.get(dmaOverviewDTO.getId()));
            try {
                dmaOverviewDTO.setStatusName(DataConstants.PARTITION_STATUS.getByValue(dmaOverviewDTO.getStatus()).getName());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return partitionList;
    }

    @Override
    public JSONObject getBigUser(Integer grade, String type, String date, String start, String end, String tenantId) {
        if (grade == null) {
            grade = 1;
        }
        List<Partition> partitionList = partitionService.getRootIdNameList(tenantId);
        List<String> partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        List<JSONObject> headerList = new ArrayList<>();
        JSONObject header;
        for (int i = 0; i < grade; i++) {
            partitionList = partitionService.getAllIdNameByPidIn(partitionIdList, "");
            if (partitionList.size() == 0) {
                return new JSONObject();
            }
            partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        }

        // 分区挂接大用户数
        Map<String, Integer> mountNum = partitionService.getMountNum(partitionIdList, "3", "");

        for (Partition partition : partitionList) {
            header = new JSONObject();

            int num = 0;
            if (mountNum.get(partition.getId()) != null) {
                num = mountNum.get(partition.getId());
            }

            header.put("label", partition.getName() + "(" + num + ")");
            header.put("value", partition.getId());
            headerList.add(header);
        }
        JSONObject result = new JSONObject();
        result.put("header", headerList);

        String typeTemp = type;
        if ("month".equals(typeTemp)) {
            typeTemp = "monthOne";
        }

        Map<String, LocalDate> localDateByType = TimeUtils.getLocalDateByType(typeTemp, date, start, end);
        LocalDate startDate = localDateByType.get("start");
        LocalDate endDate = localDateByType.get("end");
        JSONObject dataResult;
        Map<String, JSONObject> dataResultMap = new LinkedHashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String influxType = "1d";
        switch (type) {
            case "month":
            case "day":
                formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                break;
            case "year":
                formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                influxType = "1nc";
        }


        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        while (startDate.isBefore(endDate)) {
            dataResult = new JSONObject();
            dataResult.put("date", formatter.format(startDate));
            dataResult.put("totalUseWater", BigDecimal.ZERO);
            dataResult.put("totalSupplyWater", BigDecimal.ZERO);
            dataResult.put("rate", BigDecimal.ZERO);
            for (JSONObject object : headerList) {
                dataResult.put(object.getString("value") + "_supply", BigDecimal.ZERO);
                dataResult.put(object.getString("value") + "_use", BigDecimal.ZERO);
                dataResult.put(object.getString("value") + "_rate", BigDecimal.ZERO);
            }
            if ("year".equals(type)) {
                startDate = startDate.plusMonths(1L);
            } else {
                startDate = startDate.plusDays(1L);
            }
            dataResultMap.put(formatter.format(startDate), dataResult);
        }

        // 供水设备
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "in", tenantId);
        List<String> deviceIdList = new ArrayList<>();
        Map<String, String> deviceIdPartitionMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceIdList.add(partitionMount.getDeviceId() + ".total_flow");
            deviceIdPartitionMap.put(partitionMount.getDeviceId() + ".total_flow", partitionMount.getPartitionId());
        }

        JSONObject data = influxUtil.getData(deviceIdList, startTime, endTime, influxType);
        List<String> timeList = data.keySet().stream().collect(Collectors.toList());
        String partitionId;
        for (String time : timeList) {
            for (String deviceId : deviceIdList) {
                if (data.getJSONObject(time) == null || data.getJSONObject(time).getBigDecimal(deviceId) == null) {
                    continue;
                }
                BigDecimal flow = data.getJSONObject(time).getBigDecimal(deviceId);
                try {
                    partitionId = deviceIdPartitionMap.get(deviceId);
                    dataResultMap.get(time).put(partitionId + "_supply", dataResultMap.get(time).getBigDecimal(partitionId + "_supply").add(flow));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        // 大用户设备
        partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "3", "in", tenantId);
        deviceIdList = new ArrayList<>();
        deviceIdPartitionMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceIdList.add(partitionMount.getDeviceId() + ".total_flow");
            deviceIdPartitionMap.put(partitionMount.getDeviceId() + ".total_flow", partitionMount.getPartitionId());
        }

        data = influxUtil.getData(deviceIdList, startTime, endTime, influxType);
        timeList = data.keySet().stream().collect(Collectors.toList());
        for (String time : timeList) {
            for (String deviceId : deviceIdList) {
                if (data.getJSONObject(time) == null || data.getJSONObject(time).getBigDecimal(deviceId) == null) {
                    continue;
                }
                BigDecimal flow = data.getJSONObject(time).getBigDecimal(deviceId);
                try {
                    partitionId = deviceIdPartitionMap.get(deviceId);
                    dataResultMap.get(time).put(partitionId + "_use", dataResultMap.get(time).getBigDecimal(partitionId + "_use").add(flow));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        // 数据处理
        List<JSONObject> dataList = dataResultMap.values().stream().collect(Collectors.toList());
        for (JSONObject subData : dataList) {
            for (JSONObject head : headerList) {
                subData.put("totalUseWater", subData.getBigDecimal("totalUseWater").add(subData.getBigDecimal(head.getString("value") + "_use")));
                subData.put("totalSupplyWater", subData.getBigDecimal("totalUseWater").add(subData.getBigDecimal(head.getString("value") + "_supply")));

                // 占比
                try {
                    subData.put(head.getString("value") + "_rate", subData.getBigDecimal(head.getString("value") + "_use").divide(subData.getBigDecimal(head.getString("value") + "_supply"), 2, RoundingMode.HALF_UP));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            // 总占比
            try {
                subData.put("rate", subData.getBigDecimal("totalUseWater").divide(subData.getBigDecimal("totalSupplyWater"), 2, RoundingMode.HALF_UP));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        result.put("dataList", dataList);
        return result;
    }

    @Override
    public List<PressurePassRateDTO> getPressurePassRate(String name, String date, String tenantId) {
        List<Partition> partitionList = partitionService.getAll(DataConstants.PARTITION_TYPE.DMA.getValue(), "", name, tenantId);

        if (partitionList.size() == 0) {
            return new ArrayList<>();
        }

        Map<String, PressurePassRateDTO> resultMap = new LinkedHashMap<>();
        Map<String, Partition> partitionMap = new HashMap<>();
        PressurePassRateDTO pressurePassRateDTO;
        List<String> partitionIdList = new ArrayList<>();
        for (Partition partition : partitionList) {
            pressurePassRateDTO = new PressurePassRateDTO();
            pressurePassRateDTO.setId(partition.getId());
            pressurePassRateDTO.setName(partition.getName());

            partitionIdList.add(partition.getId());
            resultMap.put(partition.getId(), pressurePassRateDTO);
            partitionMap.put(partition.getId(), partition);
        }

        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "2", "", tenantId);
        if (partitionMountList.size() == 0) {
            return resultMap.values().stream().collect(Collectors.toList());
        }

        List<String> deviceIdList = new ArrayList<>();
        Map<String, String> devicePartitionIdMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceIdList.add(partitionMount.getDeviceId() + ".pressure");
            devicePartitionIdMap.put(partitionMount.getDeviceId() + ".pressure", partitionMount.getPartitionId());
        }

        LocalDate startDate = LocalDate.parse(date + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = startDate.plusMonths(1L);
        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;

        JSONObject data = influxUtil.getData(deviceIdList, startTime, endTime, "1h");
        List<String> timeList = data.keySet().stream().collect(Collectors.toList());
        String partitionId;
        Partition partition;
        for (String time : timeList) {
            for (String deviceId : deviceIdList) {
                if (data.getJSONObject(time) == null || data.getJSONObject(time).getBigDecimal(deviceId) == null) {
                    continue;
                }
                BigDecimal flow = data.getJSONObject(time).getBigDecimal(deviceId);

                partitionId = devicePartitionIdMap.get(deviceId);
                pressurePassRateDTO = resultMap.get(partitionId);
                // 总数量
                pressurePassRateDTO.setNum(pressurePassRateDTO.getNum() + 1);

                if (pressurePassRateDTO.getMinValue() == null) {
                    pressurePassRateDTO.setMinValue(flow);
                }
                if (pressurePassRateDTO.getMaxValue() == null) {
                    pressurePassRateDTO.setMaxValue(flow);
                }

                // 最小值
                if (pressurePassRateDTO.getMinValue().compareTo(flow) > 0) {
                    pressurePassRateDTO.setMinValue(flow);
                }
                // 最大值
                if (pressurePassRateDTO.getMaxValue().compareTo(flow) < 0) {
                    pressurePassRateDTO.setMaxValue(flow);
                }
                // 合格数
                partition = partitionMap.get(partitionId);
                if (partition.getMinPassPressure() == null || partition.getMaxPassPressure() == null) {
                    if (flow.compareTo(partition.getMinPassPressure()) >= 0 && flow.compareTo(partition.getMaxPassPressure()) <= 0) {
                        pressurePassRateDTO.setPassNum(pressurePassRateDTO.getPassNum());
                    }
                }
            }
        }

        List<PressurePassRateDTO> pressurePassRateDTOList = resultMap.values().stream().collect(Collectors.toList());
        for (PressurePassRateDTO pressurePassRateDTO1 : pressurePassRateDTOList) {
            // 合格率
            try {
                pressurePassRateDTO1.setPassRate(BigDecimal.valueOf(pressurePassRateDTO1.getPassNum()).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(pressurePassRateDTO1.getNum())));
            } catch (Exception e) {
            }
        }

        return pressurePassRateDTOList;
    }

    @Override
    public List<LossWaterCompareToLastYearDTO> lossWaterCompareToLastYear(String name, String date, String tenantId) {
        List<Partition> partitionList = partitionService.getAll(DataConstants.PARTITION_TYPE.DMA.getValue(), "", name, tenantId);

        if (partitionList.size() == 0) {
            return new ArrayList<>();
        }

        Map<String, Partition> partitionMap = new HashMap<>();
        List<String> partitionIdList = new ArrayList<>();
        int minHour = 2;
        int maxHour = 4;
        for (Partition partition : partitionList) {
            partitionMap.put(partition.getId(), partition);

            if (partition.getMinFlowStartHour() != null && partition.getMinFlowStartHour() < minHour) {
                minHour = partition.getMinFlowStartHour();
            }

            if (partition.getMinFlowEndHour() != null && partition.getMinFlowEndHour() > maxHour) {
                maxHour = partition.getMinFlowEndHour();
            }
            partitionIdList.add(partition.getId());
        }
        Map<String, Integer> userNum = partitionService.getUserNum(partitionIdList);

        // 今年漏失量
        LocalDate startDate = LocalDate.parse(date + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = startDate.plusMonths(1L);
        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        Map<String, PartitionNotNormalLossEvaluateDTO> thisLossWater = partitionLossEvaluateService.getLossWater(partitionIdList, partitionMap, startTime, endTime, userNum, tenantId, minHour, maxHour);

        // 去年漏失量
        startDate = startDate.minusYears(1L);
        endDate = endDate.minusYears(1L);
        startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        Map<String, PartitionNotNormalLossEvaluateDTO> lastYearLossWater = partitionLossEvaluateService.getLossWater(partitionIdList, partitionMap, startTime, endTime, userNum, tenantId, minHour, maxHour);

        LossWaterCompareToLastYearDTO lossWaterCompareToLastYearDTO;
        List<LossWaterCompareToLastYearDTO> result = new ArrayList<>();
        for (Partition partition : partitionList) {
            lossWaterCompareToLastYearDTO = new LossWaterCompareToLastYearDTO();
            lossWaterCompareToLastYearDTO.setId(partition.getId());
            lossWaterCompareToLastYearDTO.setName(partition.getName());
            lossWaterCompareToLastYearDTO.setStatus(partition.getStatus());
            try {
                lossWaterCompareToLastYearDTO.setStatusName(DataConstants.PARTITION_STATUS.getByValue(partition.getStatus()).getName());
            } catch (Exception e) {
            }
            if (thisLossWater.get(partition.getId()) != null) {
                lossWaterCompareToLastYearDTO.setLossWater(thisLossWater.get(partition.getId()).getLossWater());
            }
            try {
                BigDecimal value = (thisLossWater.get(partition.getId()).getLossWater().subtract(lastYearLossWater.get(partition.getId()).getLossWater())).multiply(BigDecimal.valueOf(100)).divide(lastYearLossWater.get(partition.getId()).getLossWater(), 2, RoundingMode.HALF_UP);
                lossWaterCompareToLastYearDTO.setChangeRate(value);
            } catch (Exception e) {
                e.printStackTrace();
            }
            lossWaterCompareToLastYearDTO.setMonth(date);
            result.add(lossWaterCompareToLastYearDTO);
        }
        return result;
    }

    @Override
    public List<JSONObject> getLossPoint(String name, String tenantId) {
        return partitionLossPointService.getLossPointReport(name, tenantId);
    }

    @Override
    public JSONObject getNrwForApp(String tenantId) {
        JSONObject jsonObject = this.getNrwReport("year", LocalDate.now().getYear() + "", "", "", tenantId, new ArrayList<>());
        JSONObject result = new JSONObject();

        result.put("rootNRW", jsonObject.get("rootNRW"));
        result.put("partitionNum", jsonObject.get("partitionNum"));
        result.put("dataForApp", jsonObject.get("dataForApp"));

        return result;
    }

    @Override
    public List<JSONObject> getNrwDetailHP(String tenantId) {
        WaterOverviewHPDTO waterOverviewDTO = new WaterOverviewHPDTO();
        // 获取根分区下的入口表
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByTenantId(tenantId, "in");
        if (partitionMountList.size() == 0) {
            return new ArrayList<>();
        }
        List<String> deviceList = partitionMountList.stream().map(a -> a.getDeviceId()).collect(Collectors.toList());
        List<JSONObject> result = new ArrayList<>();
        // 本年
        LocalDate start = LocalDate.now().withDayOfYear(1);
        LocalDate end = start.plusYears(1L);
        ExecutorService executorService = Executors.newFixedThreadPool(12);
        while (start.isBefore(end)) {
            JSONObject subResult = new JSONObject();
            LocalDate finalStart = start;
            executorService.execute(() -> {
                try {
                    // 供水量
                    String type = DataConstants.DMA_TOTAL_FLOW_TYPE.MONTH.getValue();
                    Long startTime = finalStart.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    Long endTime = finalStart.plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    BigDecimal quarterSupply = pipePartitionTotalFlowMapper.sumByTime(startTime, endTime, tenantId, type, deviceList);
                    subResult.put("supply", quarterSupply);
                    // 售水量 只统计分区关联的
                    BigDecimal quarterSale = copyDataReadMeterDataMapper.sumByTime(startTime, endTime, tenantId);
                    subResult.put("sale", quarterSale);
                    BigDecimal nrw = BigDecimal.ZERO;
                    try {
                        nrw = quarterSupply.subtract(quarterSale).multiply(BigDecimal.valueOf(100)).divide(quarterSupply, 2, RoundingMode.FLOOR);
                    } catch (Exception e) {
                    }
                    subResult.put("nrw", nrw);
                } catch (Exception e) {
                    subResult.put("supply", 0);
                    subResult.put("sale", 0);
                    subResult.put("nrw", 0);
                    e.printStackTrace();
                }
            });
            result.add(subResult);
            start = start.plusMonths(1L);
        }
        executorService.shutdown();
        while (!executorService.isTerminated()) {

        }
        return result;
    }
}