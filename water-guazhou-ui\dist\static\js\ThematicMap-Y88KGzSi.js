import{d as E,r as w,c as V,b as u,W as H,o as M,Q as P,g as Q,n as R,q as U,i as j,_ as z}from"./index-r0dFAfgr.js";import{GetFieldConfig as W,GetFieldUniqueValue as J}from"./fieldconfig-Bk3o1wi7.js";import{c as $}from"./pipe-nogVzCHG.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import{s as I}from"./ToolHelper-BiiInOzB.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{d as A}from"./LayerHelper-Cn-iiqxI.js";import{a as K,i as X}from"./QueryHelper-ILO3qZqg.js";import{e as _}from"./ViewHelper-BGCZjxXH.js";import"./widget-BcWKanF2.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const gt=E({__name:"ThematicMap",props:{view:{}},setup(k){const c=k,r=w({curOperate:"",pipeLayerOption:[],curNode:void 0,thematicIndex:0,layerFields:{}}),y={thematicLayers:[]},p=V(),o=w({dataList:[],indexVisible:!0,handleSelectChange:e=>{o.selectList=e;const i=(e==null?void 0:e.map(t=>t.id))||[];y.thematicLayers.map(t=>{t.layer.visible=i.indexOf(t.id)!==-1})},handleRowDbClick:e=>{_(c.view,e.extent)},columns:[{label:"专题图名称",prop:"name"}],pagination:{hide:!0,refreshData:({page:e,size:i})=>{o.pagination.page=e,o.pagination.limit=i,B()},layout:"total, sizes, jumper"}}),f=w({labelPosition:"top",gutter:12,group:[{fields:[{type:"tabs",tabs:[{label:"专题图设置",value:"setting"},{label:"专题图列表",value:"list"}],field:"type"}]},{handleHidden:(e,i,t)=>{t.hidden=e.type!=="setting"},fieldset:{desc:"图层名称："},fields:[{type:"select",options:[],field:"layer"}]},{handleHidden:(e,i,t)=>{t.hidden=e.type!=="setting"},fieldset:{desc:"专题图名称："},fields:[{type:"input",field:"name"}]},{handleHidden:(e,i,t)=>{t.hidden=e.type!=="setting"},fieldset:{desc:"图层字段"},fields:[{type:"list",data:[],className:"sql-list-wrapper",setData:async(e,i)=>{if(!i.layer)return;const t=await S(i.layer);e.data=t},setDataBy:"layer",displayField:"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:e=>{r.curNode=e,n(e.name)}}]},{handleHidden:(e,i,t)=>{t.hidden=e.type!=="setting"},id:"field-construct",fieldset:{desc:"构建查询语句"},fields:[{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{n("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{n("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{n(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{n("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{n("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{n("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{n("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{n("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:e=>{n("'"+e+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>r.curOperate==="uniqueing"?"正在获取唯一值":"获取唯一值",loading:()=>r.curOperate==="uniqueing",disabled:()=>r.curOperate==="viewing",styles:{width:"100%",borderRadius:"0"},click:()=>G()}]}]}]}]},{handleHidden:(e,i,t)=>{t.hidden=e.type!=="setting"},fieldset:{desc:"组合查询条件"},fields:[{type:"textarea",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",btns:[{perm:!0,text:"清除",type:"danger",disabled:()=>r.curOperate==="viewing",click:()=>N(),styles:{width:"100%"}},{perm:!0,text:()=>r.curOperate==="viewing"?"正在处理中":"显示",disabled:()=>r.curOperate==="viewing",loading:()=>r.curOperate==="viewing",click:()=>T(),styles:{width:"100%"}}]}]},{handleHidden:(e,i,t)=>{t.hidden=e.type!=="list"},fields:[{style:{height:"500px"},type:"table",config:o},{type:"btn-group",itemContainerStyle:{marginTop:"15px"},btns:[{perm:!0,type:"danger",text:"删除选中专题图",styles:{width:"100%"},click:()=>{var t,s;const e=(t=o.selectList)==null?void 0:t.map(a=>a.id),i=y.thematicLayers.filter(a=>(e==null?void 0:e.indexOf(a.id))!==-1).map(a=>a.layer);(s=c.view)==null||s.map.removeMany(i),o.dataList=o.dataList.filter(a=>(e==null?void 0:e.indexOf(a.id))===-1),o.selectList=[]}}]}]}],defaultValue:{type:"setting"}}),C=()=>{var t,s,a,d;if(!c.view)return;const e=(t=c.view)==null?void 0:t.map.findLayerById("pipelayer");r.pipeLayerOption=[],(s=e==null?void 0:e.sublayers)==null||s.map(l=>{var m;(m=r.pipeLayerOption)==null||m.push({label:l.title,value:l.title,id:l.id})});const i=f.group[0].fields[0];i&&(i.options=r.pipeLayerOption),(a=p.value)!=null&&a.dataForm&&(p.value.dataForm.layer=r.pipeLayerOption&&((d=r.pipeLayerOption[0])==null?void 0:d.value))},S=async e=>{var s,a;if(r.layerFields[e])return r.layerFields[e];const t=((a=(s=(await W(e)).data)==null?void 0:s.result)==null?void 0:a.rows)||[];return r.layerFields[e]=t,t},G=async()=>{var e,i;if(r.curNode){r.curOperate="uniqueing";try{const t=(e=r.pipeLayerOption.find(l=>{var m;return l.label===((m=p.value)==null?void 0:m.dataForm.layer)}))==null?void 0:e.id,s=await J({layerid:t,field_name:r.curNode.name}),a=(i=f.group.find(l=>l.id==="field-construct"))==null?void 0:i.fields[0].extraFormItem,d=a&&a[0];d&&(d.data=s.data.result.rows)}catch{u.error("获取唯一值失败")}r.curOperate=""}},n=e=>{var t;if(!p.value)return;(t=p.value)!=null&&t.dataForm||(p.value.dataForm={});const i=p.value.dataForm.sql||" ";p.value.dataForm.sql=i+e+" "},N=()=>{var e;(e=p.value)!=null&&e.dataForm&&(p.value.dataForm.sql="")},T=async()=>{var t,s,a,d,l,m,v,F,O;if(!c.view)return;const e=(s=(t=p.value)==null?void 0:t.dataForm)==null?void 0:s.layer;if(!e){u.warning("请选择图层");return}if(!((a=p.value)!=null&&a.dataForm.name)){u.warning("请输入专题图名称");return}const i=(l=(d=p.value)==null?void 0:d.dataForm)==null?void 0:l.sql;if(!i){u.warning("请选择查询条件");return}I("loading"),r.curOperate="viewing";try{const g=(m=r.pipeLayerOption.find(x=>x.label===e))==null?void 0:m.id,D=window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+g;await K(D,X({where:i,returnGeometry:!0,outFields:((v=r.layerFields[e])==null?void 0:v.map(x=>x.name))||["*"]}));const h="Thematic_"+r.thematicIndex++,b=A(c.view,{id:h,title:(F=p.value)==null?void 0:F.dataForm.name,type:"MapImageLayer",url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService}),L=(await $({usertoken:H().gToken,layerid:g,where:i,f:"pjson"})).data.result;b.when(()=>{_(c.view,L)}),y.thematicLayers.push({id:h,layer:b});const q={id:h,name:p.value.dataForm.name,sql:i||"1=1",extent:L};o.dataList.push(q),(O=o.selectList)==null||O.push(q)}catch{u.error("显示失败，请联系管理员")}r.curOperate="",I("")},B=()=>{};return M(()=>{C()}),P(()=>{r.curOperate=""}),(e,i)=>{const t=z;return Q(),R("div",null,[U(t,{ref_key:"refForm",ref:p,config:j(f)},null,8,["config"])])}}});export{gt as default};
