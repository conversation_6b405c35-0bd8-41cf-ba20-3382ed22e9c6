package org.thingsboard.server.controller.waterSource.inspection;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlan;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlanResponse;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitPlanPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitPlanSaveRequest;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.dao.circuit.CircuitPlanService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/sp/circuitPlan")
public class CircuitPlanController extends BaseController {
    @Autowired
    private CircuitPlanService service;


    @GetMapping
    public IPage<CircuitPlanResponse> findAllConditional(CircuitPlanPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public CircuitPlan save(@RequestBody CircuitPlanSaveRequest plan) {
        return service.save(plan);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody CircuitPlanSaveRequest plan, @PathVariable String id) {
        return service.update(plan.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

    @DeleteMapping
    public boolean deleteAll(@RequestBody List<String> idList) {
        if (idList.size() == 0) {
            ExceptionUtils.silentThrow("至少需要选择一个需要删除的计划");
        }
        return service.deleteAll(idList);
    }
}