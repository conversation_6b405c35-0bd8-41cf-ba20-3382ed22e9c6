import{o as l}from"./_commonjsHelpers-DCkdB7M8.js";import{r as u}from"./_commonjs-dynamic-modules-DfYEAvWy.js";function c(e,o){for(var n=0;n<o.length;n++){const r=o[n];if(typeof r!="string"&&!Array.isArray(r)){for(const t in r)if(t!=="default"&&!(t in e)){const i=Object.getOwnPropertyDescriptor(r,t);i&&Object.defineProperty(e,t,i.get?i:{enumerable:!0,get:()=>r[t]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var s,d,a={},m={get exports(){return a},set exports(e){a=e}};s=m,(d=function(e,o){Object.defineProperty(o,"__esModule",{value:!0}),o.default={_decimalSeparator:",",_thousandSeparator:" ",_percentPrefix:null,_percentSuffix:"%",_date_millisecond:"mm:ss SSS",_date_second:"HH:mm:ss",_date_minute:"HH:mm",_date_hour:"HH:mm",_date_day:"dd MMM",_date_week:"ww",_date_month:"MMM",_date_year:"yyyy",_duration_millisecond:"SSS",_duration_second:"ss",_duration_minute:"mm",_duration_hour:"hh",_duration_day:"dd",_duration_week:"ww",_duration_month:"MM",_duration_year:"yyyy",_era_ad:"н.э.",_era_bc:"до н.э.",A:"У",P:"В",AM:"утра",PM:"вечера","A.M.":"до полудня","P.M.":"после полудня",January:"января",February:"февраля",March:"марта",April:"апреля",May:"мая",June:"июня",July:"июля",August:"августа",September:"сентября",October:"октября",November:"ноября",December:"декабря",Jan:"янв.",Feb:"февр.",Mar:"март",Apr:"апр.","May(short)":"май",Jun:"июнь",Jul:"июль",Aug:"авг.",Sep:"сент.",Oct:"окт.",Nov:"нояб.",Dec:"дек.",Sunday:"воскресенье",Monday:"понедельник",Tuesday:"вторник",Wednesday:"среда",Thursday:"четверг",Friday:"пятница",Saturday:"суббота",Sun:"вс.",Mon:"пн.",Tue:"вт.",Wed:"ср.",Thu:"чт.",Fri:"пт.",Sat:"сб.",_dateOrd:function(n){return"-ое"},"Zoom Out":"Уменьшить",Play:"Старт",Stop:"Стоп",Legend:"Легенда","Click, tap or press ENTER to toggle":"Щелкните, коснитесь или нажмите ВВОД, чтобы переключить",Loading:"Идет загрузка",Home:"Начало",Chart:"График","Serial chart":"Серийная диаграмма","X/Y chart":"Диаграмма X/Y","Pie chart":"Круговая диаграмма","Gauge chart":"Датчик-диаграмма","Radar chart":"Лепестковая диаграмма","Sankey diagram":"Диаграмма Сэнки","Chord diagram":"Диаграмма Chord","Flow diagram":"Диаграмма флоу","TreeMap chart":"Иерархическая диаграмма",Series:"Серия","Candlestick Series":"Серия-подсвечник","Column Series":"Столбчатая серия","Line Series":"Линейная серия","Pie Slice Series":"Круговая серия","X/Y Series":"X/Y серия",Map:"Карта","Press ENTER to zoom in":"Нажмите ВВОД чтобу увеличить","Press ENTER to zoom out":"Нажмите ВВОД чтобы уменьшить","Use arrow keys to zoom in and out":"Используйте клавиши-стрелки чтобы увеличить и уменьшить","Use plus and minus keys on your keyboard to zoom in and out":"Используйте клавиши плюс и минус на клавиатуре чтобы увеличить и уменьшить",Export:"Экспортировать",Image:"Изображение",Data:"Данные",Print:"Печатать","Click, tap or press ENTER to open":"Щелкните, коснитесь или нажмите ВВОД чтобы открыть","Click, tap or press ENTER to print.":"Щелкните, коснитесь или нажмите ВВОД чтобы распечатать","Click, tap or press ENTER to export as %1.":"Щелкните, коснитесь или нажмите ВВОД чтобы экспортировать как %1",'To save the image, right-click this link and choose "Save picture as..."':'Чтобы сохранить изображение, щелкните правой кнопкой на ссылке и выберите "Сохранить изображение как..."','To save the image, right-click thumbnail on the left and choose "Save picture as..."':'Чтобы сохранить изображение, щелкните правой кнопкой на картинке слева и выберите "Сохранить изображение как..."',"(Press ESC to close this message)":"(Нажмите ESC чтобы закрыть это сообщение)","Image Export Complete":"Экспорт изображения завершен","Export operation took longer than expected. Something might have gone wrong.":"Экспортирование заняло дольше, чем планировалось. Возможно что-то пошло не так.","Saved from":"Сохранено из",PNG:"PNG",JPG:"JPG",GIF:"GIF",SVG:"SVG",PDF:"PDF",JSON:"JSON",CSV:"CSV",XLSX:"XLSX","Use TAB to select grip buttons or left and right arrows to change selection":"Используйте клавишу TAB, чтобы выбрать рукоятки или клавиши стрелок влево и вправо, чтобы изменить выделение","Use left and right arrows to move selection":"Используйте стрелки влево-вправо, чтобы передвинуть выделение","Use left and right arrows to move left selection":"Используйте стрелки влево-вправо, чтобы передвинуть левое выделение","Use left and right arrows to move right selection":"Используйте стрелки влево-вправо, чтобы передвинуть правое выделение","Use TAB select grip buttons or up and down arrows to change selection":"Используйте TAB, чтобы выбрать рукоятки или клавиши вверх-вниз, чтобы изменить выделение","Use up and down arrows to move selection":"Используйте стрелки вверх-вниз, чтобы передвинуть выделение","Use up and down arrows to move lower selection":"Используйте стрелки вверх-вниз, чтобы передвинуть нижнее выделение","Use up and down arrows to move upper selection":"Используйте стрелки вверх-вниз, чтобы передвинуть верхнее выделение","From %1 to %2":"От %1 до %2","From %1":"От %1","To %1":"До %1","No parser available for file: %1":"Нет анализатора для файла: %1","Error parsing file: %1":"Ошибка при разборе файла: %1","Unable to load file: %1":"Не удалось загрузить файл: %1","Invalid date":"Некорректная дата"}}(u,a))!==void 0&&(s.exports=d);const h=c({__proto__:null,default:l(a)},[a]);export{h as r};
