/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.alarm;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.ListenableFuture;
import org.thingsboard.server.common.data.alarm.*;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.TimePageData;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * Created by ashvayka on 11.05.17.
 */
public interface AlarmService {

    Alarm createOrUpdateAlarm(Alarm alarm);

    Boolean deleteAlarm(TenantId tenantId, AlarmId alarmId);

    ListenableFuture<Boolean> ackAlarm(TenantId tenantId, AlarmId alarmId, long ackTs);

    ListenableFuture<Boolean> clearAlarm(TenantId tenantId, AlarmId alarmId, JsonNode details, long ackTs);

    ListenableFuture<Alarm> findAlarmByIdAsync(AlarmId alarmId);

    ListenableFuture<AlarmInfo> findAlarmInfoByIdAsync(TenantId tenantId, AlarmId alarmId);

    ListenableFuture<TimePageData<AlarmInfo>> findAlarms(TenantId tenantId, AlarmQuery query);

    String findHighestAlarmSeverity(TenantId tenantId, EntityId entityId, AlarmSearchStatus alarmSearchStatus,
                                    AlarmStatus alarmStatus);

    List<Alarm> findClearAlarmByTenantAndLevel(TenantId tenantId, List<DeviceId> deviceId, String type, String level, String status, long start, long end);

    List<Alarm> findClearAlarmByProjectIdAndLevel(String projectId, List<DeviceId> deviceId, String type, String level, String status, long start, long end);

    ListenableFuture<Alarm> findLatestByOriginatorAndType(TenantId tenantId, EntityId originator, String type);

    ListenableFuture<List<Alarm>> findHistoryAlarm(TenantId tenantId, long start, long end);

    ListenableFuture<List<Alarm>> findHistoryAlarmByDevice(DeviceId deviceId, String alarmType);

    ListenableFuture<List<Alarm>> findRealTimeAlarm(TenantId tenantId, long start, long end);

    ListenableFuture<List<Alarm>> findRealTimeAlarm(String projectId, long start, long end);

    ListenableFuture<List<Alarm>> findHistoryByDeviceId(DeviceId deviceId, long start, long end);

    ListenableFuture<List<Alarm>> findOnlineByTypeAndDevice(DeviceId deviceId, String type);

    ListenableFuture<List<Alarm>> findOnlineByLevelAndDevice(DeviceId deviceId, String level);

    void deleteAlarmByDevice(DeviceId deviceId);

    void checkDeviceOffLineStatus(DeviceId deviceId);

    List<Alarm> findUnClearByJsonId(AlarmJsonId id, String type);

    List<Alarm> findOnlineAlarmByJsonId(AlarmJsonId id, String type);

    List<Alarm> findUnRestoreAlarmByJsonId(AlarmJsonId id, String type);

    ListenableFuture<List<Alarm>> findAlarmByJsonId(AlarmJsonId alarmJsonId);

    /**
     * 获取平台下未解除报警数量
     * @return 未解除报警数量
     */
    long getUnClearAlarm();

    /**
     * 获取企业下未解除报警数量
     * @param tenantId 企业ID
     * @return 企业未解除报警数量
     */
    long getUnClearAlarmByTenantId(TenantId tenantId);

    /**
     * 获取项目下为解除报警数量
     * @param projectId 项目ID
     * @return 项目下未解除报警数量
     */
    long getUnClearAlarmByProjectId(String projectId);

    /**
     * 获取设备报警数量
     * @param deviceId 设备ID
     * @return 设备报警总数
     */
    long getAllAlarmByDeviceId(String deviceId);

    /**
     * 获取设备未解除报警数量
     * @param deviceId 设备ID
     * @return 设备未解除报警数量
     */
    long getRealTimeAlarmByDeviceId(String deviceId);

    /**
     * 获取设备历史报警数量
     * @param deviceId 设备ID
     * @return 设备历史报警数量
     */
    long getHistoryAlarmByDeviceId(String deviceId);

    /**
     * 获取平台下告警总数
     * @return 告警数量
     */
    long getAllAlarmByRoot();

    /**
     * 获取企业下所有报警总数
     * @param tenantId 企业ID
     * @return 企业告警数量
     */
    long getAllAlarmByTenant(TenantId tenantId);

    /**
     * 获取项目下所有报警总数
     * @param projectId 项目ID
     * @return 项目告警数量
     */
    long getAllAlarmByProject(String projectId);


    ListenableFuture<List<Alarm>> findRealTimeAlarmAll(String projectId, long start, long end) throws ExecutionException, InterruptedException;

    /**
     * 获取项目下所有报警总数
     * @param projectId 项目ID
     * @param startTime 开始时间
     * @param endTime  结束时间
     * @return 项目告警数量
     */
    Long getAllAlarmByProjectAndTime(String projectId, Long startTime, Long endTime);

    Long getAllAlarmByTenantAndTime(TenantId tenantId, long startTime, long endTime);

    List<Alarm> findAlarmsByProjectIdAndTs(List<String> projectIdList, long start, long end);

    ListenableFuture<List<Alarm>> getAlarmRealTimeByDevice(DeviceId deviceId, long start, long end);

    List<Alarm> findAlarmByStation(String stationId, long start, long end);

    Alarm save(Alarm alarm);
}
