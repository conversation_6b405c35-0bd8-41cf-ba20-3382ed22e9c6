/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.objects;

import lombok.Data;
import org.thingsboard.server.common.data.constantsAttribute.ObjectMap;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Data
public class ResourceData {
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 属性名称
     */
    private String prop;
    /**
     * 最后一次数据的时间
     */
    private String time;
    /**
     * 最后一次的数据
     */
    private String value;

    /**
     * 节点状态
     */
    private String status;


    /**
     * assetId
     */
    private String assetId;

    /**
     *  编号
     */
    private String number;

    private String propName;

    private String propUnit;

    private ArrayList<ObjectMap> timeData;


    public ResourceData() {
    }

    public ResourceData(String deviceId, String time, String value, String order, String status, String number,String prop) {
        this.deviceId = deviceId;
        this.time = time;
        this.value = value;
        this.status = status;
        this.number = number;
        this.prop = prop;

    }

}
