import{D as E}from"./DrawerBox-CLde5xC8.js";import{_ as J}from"./ArcLayout-CHnHL9Pv.js";import{_ as K}from"./ArcDraw.vue_vue_type_script_setup_true_lang-Hk5OVbKn.js";import{O as Q,a as Y,b as H,_ as X}from"./ArcSqlGenerator-CrJVg5vk.js";import{d as Z,c as m,eY as ee,r as C,a8 as re,b as w,bB as te,o as oe,g as ie,h as ae,F as x,q as f,i as a,p as le,_ as ne,C as pe}from"./index-r0dFAfgr.js";import me from"./DetailTable-Dc-xAY7v.js";import se from"./PipeMedias-B3OYskTt.js";import{_ as ce}from"./UploadForm.vue_vue_type_script_setup_true_lang-0ZMp4wRG.js";import{QueryByPolygon as de}from"./wfsUtils-DXofo3da.js";import"./SideDrawer-CBntChyn.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./Videor.vue_vue_type_script_setup_true_lang-EsHlP83o.js";/* empty css                         */import"./engineeringDocuments-DYprVB7x.js";import"./config-B_00vVdd.js";const ue={class:"detail-wrapper"},fe=Z({__name:"index",setup(ye){const b=m(),S=m(),L=m(),B=m(),y=m(),k=m(),i=m(),q=m(),D={};ee("pipeMediasRef",b);const t=C({tabs:[],curOperate:"",panelTitle:"",currentRow:void 0,geoServerLayers:[]}),F=C({dataList:[],height:170,columns:[{minWidth:60,label:"逻辑",prop:"logic",formatter(e,r){return Q[r]}},{minWidth:100,label:"字段",prop:"field",formatter(e,r){return Y[r]}},{minWidth:70,label:"运行符",prop:"calc",formatter(e,r){return H[r]}},{minWidth:80,label:"值",prop:"value"}],pagination:{hide:!0}}),A=C({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",iconifyIcon:"mdi:shape-polygon-plus",click:()=>{var e;return(e=y.value)==null?void 0:e.initDraw("polygon")}},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",iconifyIcon:"ep:crop",click:()=>{var e;return(e=y.value)==null?void 0:e.initDraw("rectangle")}},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",iconifyIcon:"mdi:ellipse-outline",click:()=>{var e;return(e=y.value)==null?void 0:e.initDraw("circle")}},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",iconifyIcon:"ep:delete",click:()=>{var e;return(e=y.value)==null?void 0:e.clear()}}]}]},{fieldset:{desc:"选择图层"},fields:[{type:"tree",options:re(()=>t.geoServerLayers&&t.geoServerLayers.length>0?t.geoServerLayers:[]),checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value",handleCheckChange:(e,r)=>{r&&(t.panelTitle=e.label,i.value&&(i.value.dataForm.layerid=[e.value]))}}]},{id:"field-construct",fieldset:{desc:"属性过滤"},fields:[{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},text:"添加条件",iconifyIcon:"ep:plus",click:()=>{var e;(e=k.value)==null||e.openDialog()}},{perm:!0,text:"清除条件",type:"danger",iconifyIcon:"ep:delete",disabled:()=>t.curOperate==="detailing",click:()=>j(),styles:{width:"100%"}}]},{type:"table",config:F}]},{fields:[{type:"btn-group",btns:[{perm:!0,text:()=>t.curOperate==="detailing"?"正在查询":"查询",disabled:()=>t.curOperate==="detailing",loading:()=>t.curOperate==="detailing",click:()=>I(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12}),M=e=>{var r;debugger;t.currentRow=e,(r=S.value)==null||r.openDialog()},P=()=>{var e;(e=b.value)==null||e.refreshData()},U=e=>{var r;t.currentRow=e,(r=b.value)==null||r.refreshData()},j=()=>{var e,r;(e=i.value)!=null&&e.dataForm&&(i.value.dataForm.sql=""),F.dataList=[],(r=k.value)==null||r.clear()},z=e=>{var r;console.log(e),F.dataList=e.list,(r=i.value)!=null&&r.dataForm&&(i.value.dataForm.sql=e.sql)},I=async()=>{var e,r,p,s,c,l,g;try{t.tabs.length=0,t.curOperate="detailing";const o=(e=i.value)==null?void 0:e.dataForm.layerid;if(!(o!=null&&o.length))w.warning("请选择图层");else{(p=(r=q.value)==null?void 0:r.refPanel)==null||p.Toggle(!0);try{const d=((c=(s=i.value)==null?void 0:s.dataForm)==null?void 0:c.sql)||null,_=(l=y.value)==null?void 0:l.getGraphics();let h=null;if(_&&_.length>0){const n=_[0];if(n.geometry.type==="polygon")h=n.geometry.rings[0];else if(n.geometry.hasOwnProperty("extent")){const V=n.geometry.extent,{xmin:O,ymin:T,xmax:R,ymax:G}=V;h=[[O,T],[R,T],[R,G],[O,G],[O,T]]}}const v=t.panelTitle;if(!v){w.error("图层名称不能为空"),t.curOperate="";return}const u=await de(v,h,d);if(u&&u.data&&u.data.features){const n=u.data.features;if(n.length===0){w.warning("未查询到数据"),t.curOperate="";return}await te(),(g=L.value)==null||g.refreshDetail(D.view,{layerid:o[0],layername:v,oids:n}),t.tabs=n}else w.warning("查询结果为空")}catch(d){console.error("GeoServer查询失败:",d),w.error("查询失败: "+(d.message||"未知错误"))}}}catch(o){w.error(o.message)}t.curOperate=""},W=()=>{var e,r,p,s,c;try{const l=(c=(s=(p=(r=(e=D.view)==null?void 0:e.layerViews)==null?void 0:r.items)==null?void 0:p[1])==null?void 0:s.layer)==null?void 0:c.sublayers;if(l&&l.items){let g=l.items.map(o=>({label:o.name,value:o.name}));t.geoServerLayers=g}}catch(l){console.error("获取GeoServer图层信息失败:",l)}},$=async e=>{D.view=e,setTimeout(()=>{W()},1e3)},N=e=>{if(e){let r=t.tabs.filter(p=>p.properties.id===e.id)[0];return r==null?void 0:r.geometry}else return null};return oe(()=>{var e;(e=B.value)==null||e.toggleDrawer("rtl",!0)}),(e,r)=>{const p=ne,s=X,c=K,l=J,g=E;return ie(),ae(g,{ref_key:"refDrawerBox",ref:B,"right-drawer":!0,"right-drawer-title":"多媒体上传"},{right:x(()=>[f(p,{ref_key:"refForm",ref:i,config:a(A)},null,8,["config"]),f(s,{ref_key:"refSql",ref:k,"layer-name":a(t).panelTitle,onSubmit:z},null,8,["layer-name"])]),default:x(()=>{var o,d,_;return[f(l,{ref_key:"refArcLayout",ref:q,"panel-telport":"","panel-title":"多媒体上传查询结果"+(a(t).panelTitle?"-"+a(t).panelTitle:""),onMapLoaded:$},{"detail-default":x(()=>{var h,v,u;return[le("div",ue,[f(me,{ref_key:"refDetailTable",ref:L,class:"pipe-list",operations:[{perm:!0,text:"多媒体上传",click:n=>M(n)}],onRefreshData:I,onRowClick:U},null,8,["operations"]),f(se,{ref_key:"refPipeMedias",ref:b,class:"media-tabs",objectid:(h=a(t).currentRow)==null?void 0:h.OBJECTID,layerid:(u=(v=a(i))==null?void 0:v.dataForm.layerid)==null?void 0:u[0],layername:a(t).panelTitle},null,8,["objectid","layerid","layername"])])]}),default:x(()=>[f(c,{ref_key:"refArcDraw",ref:y,layerid:"query-area",layername:"查询范围"},null,512)]),_:1},8,["panel-title"]),f(ce,{ref_key:"refUploadForm",ref:S,objectid:(o=a(t).currentRow)==null?void 0:o.OBJECTID,layerid:(_=(d=a(i))==null?void 0:d.dataForm.layerid)==null?void 0:_[0],layername:a(t).panelTitle,geo:N(a(t).currentRow),onUploaded:P},null,8,["objectid","layerid","layername","geo"])]}),_:1},512)}}}),mt=pe(fe,[["__scopeId","data-v-12a4bff9"]]);export{mt as default};
