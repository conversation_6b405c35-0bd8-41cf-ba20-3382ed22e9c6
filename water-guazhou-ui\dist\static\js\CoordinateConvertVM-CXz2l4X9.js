import{d as E,c as v,r as f,s as h,S as F,o as D,Q as N,g as d,n as c,p,q as i,F as s,i as e,G as x,h as R,an as C,aB as U,aJ as q,bh as y,aq as z,_ as M,dF as A,dA as G,ak as $,bq as j,dk as H,C as J}from"./index-r0dFAfgr.js";import{b as P}from"./Point-WxyopZva.js";import{S as Q,E as K}from"./coordinateFormatter-C2XOyrWt.js";import{S as L}from"./SideDrawer-CBntChyn.js";import{v as V}from"./v4-SoommWqA.js";import"./pe-B8dP0-Ut.js";const O={class:"coord-converter"},X={class:"center"},Y={class:"right"},Z={key:1},tt={class:"list"},et=E({__name:"CoordinateConvertVM",setup(ot){const b=v(),_=v(800),a=f({drawerShow:!0,resultType:"table"}),l=f({indexVisible:!0,dataList:[{id:V(),name:"柑子院一块表",y:"29°45′37.570678″N",x:"106°26′04.257814″E"}],columns:[{minWidth:200,label:"名称",prop:"name",formItemConfig:{type:"input"}},{minWidth:200,label:"x",prop:"x",formItemConfig:{type:"input"}},{minWidth:200,label:"y",prop:"y",formItemConfig:{type:"input"}}],operationHeader:{type:"btn-group",btns:[{perm:!0,text:"新增",isTextBtn:!0,svgIcon:h($),click:()=>T()}]},operations:[{perm:!0,text:"删除",svgIcon:h(j),type:"danger",click:o=>I(o)}],pagination:{hide:!0}}),u=f({indexVisible:!0,dataList:[],columns:[{minWidth:200,label:"名称",prop:"name"},{minWidth:200,label:"x",prop:"x",formItemConfig:{type:"input"}},{minWidth:200,label:"y",prop:"y",formItemConfig:{type:"input"}}],pagination:{hide:!0}}),k=f({gutter:0,labelWidth:"auto",group:[{fields:[{type:"btn-group",btns:[{perm:!0,text:"转换坐标",svgIcon:h(H),styles:{width:"100%"},click:()=>S()}]}]}]}),S=()=>{u.dataList=[],Q().then(()=>{const o=l.dataList.map(t=>({point:K([t.y,t.x].join(","),new P({wkid:4326})),...t}));u.dataList=o.map(t=>{var r,m;return{x:(r=t.point)==null?void 0:r.longitude,y:(m=t.point)==null?void 0:m.latitude,name:t.name}})})},T=()=>{l.dataList.push({id:V(),x:"",y:""})},I=o=>{F("确定删除？","删除提示").then(()=>{l.dataList=l.dataList.filter(t=>t.id!==o.id)}).catch(()=>{})},g=()=>{var o;_.value=((o=b.value)==null?void 0:o.clientWidth)??800};return D(()=>{g(),window.addEventListener("resize",g)}),N(()=>{window.removeEventListener("resize",g)}),(o,t)=>{const r=z,m=M,w=A,W=G;return d(),c("div",O,[p("div",{ref_key:"refLeftDiv",ref:b,class:"left"},[i(L,{modelValue:e(a).drawerShow,"onUpdate:modelValue":t[0]||(t[0]=n=>e(a).drawerShow=n),title:"原始数据",width:e(_),"hide-bar":!0,direction:"rtl"},{default:s(()=>[i(r,{config:e(l)},null,8,["config"])]),_:1},8,["modelValue","width"])],512),p("div",X,[i(m,{config:e(k)},null,8,["config"])]),p("div",Y,[i(L,{modelValue:e(a).drawerShow,"onUpdate:modelValue":t[2]||(t[2]=n=>e(a).drawerShow=n),title:"转换结果",width:e(_),"hide-bar":!0,direction:"ltr"},{title:s(()=>[t[5]||(t[5]=p("span",{class:"title",style:{"margin-right":"auto"}},"转换结果",-1)),i(W,{modelValue:e(a).resultType,"onUpdate:modelValue":t[1]||(t[1]=n=>e(a).resultType=n)},{default:s(()=>[i(w,{label:"table"},{default:s(()=>t[3]||(t[3]=[x(" 表格 ")])),_:1}),i(w,{label:"list"},{default:s(()=>t[4]||(t[4]=[x(" 文本 ")])),_:1})]),_:1},8,["modelValue"])]),default:s(()=>[e(a).resultType==="table"?(d(),R(r,{key:0,config:e(u)},null,8,["config"])):C("",!0),e(a).resultType==="list"?(d(),c("div",Z,[p("ul",tt,[(d(!0),c(U,null,q(e(u).dataList,(n,B)=>(d(),c("li",{key:B},y(n.name)+" "+y(n.x)+","+y(n.y),1))),128))])])):C("",!0)]),_:1},8,["modelValue","width"])])])}}}),dt=J(et,[["__scopeId","data-v-b965cc0f"]]);export{dt as default};
