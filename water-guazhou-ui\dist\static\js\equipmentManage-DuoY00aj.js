import{z as t}from"./index-r0dFAfgr.js";const i=()=>t({url:"/api/deviceType/tree",method:"get"}),s=e=>t({url:`/api/deviceTypeAttr/list/${e}`,method:"get"}),r=e=>t({url:"/api/deviceType",method:"get",params:e}),o=e=>t({url:"/api/deviceType",method:"post",data:e}),c=(e,a)=>t({url:`/api/deviceType/${e}`,method:"patch",data:a}),d=e=>t({url:`/api/deviceType/${e}`,method:"delete"}),l=e=>t({url:"/api/deviceTypeAttr",method:"get",params:e}),u=e=>t({url:"/api/device/m",method:"get",params:e}),h=e=>t({url:"/api/device/m",method:"post",data:e}),m=e=>t({url:"/api/device/m",method:"delete",data:e}),n=e=>t({url:"/api/deviceTypeAttr",method:"post",data:e}),v=e=>t({url:`/api/deviceTypeAttr/${e}`,method:"delete"}),g=(e,a)=>t({url:`/api/deviceTypeAttr/${e}`,method:"patch",data:a}),T=e=>t({url:"/api/area/tree/page",method:"get",params:e}),y=e=>t({url:"/api/area",method:"post",data:e}),A=e=>t({url:"/api/area",method:"delete",data:e}),D=e=>t({url:"/api/supplier",method:"get",params:e}),S=e=>t({url:"/api/supplier",method:"delete",data:e}),$=e=>t({url:"/api/supplier",method:"post",data:e}),f=e=>t({url:`/api/supplier/goods/getByMainId/${e}`,method:"get"}),q=e=>t({url:`/api/supplier/qualifications/getByMainId/${e}`,method:"get"});export{n as a,l as b,i as c,v as d,c as e,o as f,s as g,d as h,r as i,h as j,m as k,u as l,$ as m,S as n,f as o,g as p,q,D as r,T as s,y as t,A as u};
