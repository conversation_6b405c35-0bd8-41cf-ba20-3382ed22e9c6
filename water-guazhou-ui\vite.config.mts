import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import path from 'path';
import AutoImport from 'unplugin-auto-import/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import UnpluginComponents from 'unplugin-vue-components/vite';
import { defineConfig, loadEnv } from 'vite';
import { createHtmlPlugin } from 'vite-plugin-html';
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer';
import { viteExternalsPlugin } from 'vite-plugin-externals';
import resolveExternalsPlugin from 'vite-plugin-resolve-externals';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const isPro = mode === 'production';
  return {
    plugins: [
      // VueDevTools(),
      vue(),
      vueJsx(),
      viteExternalsPlugin({
        cesium: 'Cesium'
      }),

      createHtmlPlugin({
        minify: isPro,
        entry: '/src/main.ts',
        inject: {
          data: {
            ...env
          }
        }
      }),
      AutoImport({
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
          /\.md$/ // .md
        ],
        eslintrc: {
          enabled: false
        },
        dirs: ['./src/plugins/dayjs'],
        imports: [
          'vue',
          'pinia',
          'vue-router',
          {
            // vue: ['ComputedRef'],
            '@vueuse/core': [
              // named imports
              'useMouse', // import { useMouse } from '@vueuse/core',
              'useDark',
              'useToggle',
              'useBluetooth',
              // alias
              ['useFetch', 'useMyFetch'] // import { useFetch as useMyFetch } from '@vueuse/core',
            ],

            moment: [['default', 'moment']]
          }
        ]
      }),
      UnpluginComponents({
        dirs: ['src/components'],
        resolvers: [ElementPlusResolver()]
      }),

      ViteImageOptimizer({
        logStats: false
      }),
      resolveExternalsPlugin({
        cesium: 'cesium'
      })
    ],
    resolve: {
      alias: [
        {
          find: '@',
          replacement: path.resolve(__dirname, 'src')
        },
        {
          find: '~@',
          replacement: path.resolve(__dirname, 'src')
        }
      ]
    },
    optimizeDeps: {
      include: [
        '@/../lib/vform/designer.umd.js',
        '@/../lib/vform/render.umd.js'
      ]
    },
    server: {
      host: '0.0.0.0',
      port: 5000,
      hmr: true,
      proxy: {
        '/arcgis/rest/services/ANQING/': {
          target: 'http://***********:28090', // 本地
          changeOrigin: true,
          rewrite(path) {
            return path;
          }
        },
        '/authentication':{
          target: 'http://localhost:8081',
          changeOrigin: true
        },
        '/api': {
          target:'http://localhost:8081',
          // target:'http://***********:8003/',
          // ws: true, //代理websockets/
          changeOrigin: true // 是否跨域，虚拟的站点需要更管origin
        },
        '/istar': {
          target:'http://localhost:8081',
          changeOrigin: true // 是否跨域，虚拟的站点需要更管origin
        },
        '/geoserver': {
          target: 'http://***********:18080',
          changeOrigin: true
        }
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          charset: false,
          api: 'modern-compiler'
        }
      }
    },
    build: {
      commonjsOptions: {
        include: /node_modules|lib/
      },
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]'
        }
      }
    }
  };
});
