import { useWaterPoint } from '@/hooks/arcgis'
import {
  createPictureMarker,
  excuteIdentify,
  getGraphicLayer,
  getNeerestPoint,
  getSubLayerIds,
  // gotoAndHighLight,
  initIdentifyParams,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper'
import { SLMessage } from '@/utils/Message'
import { getPipeImageUrl } from '@/utils/URLHelper'

export const useIdentify = () => {
  const isPicking = ref<boolean>(false)
  const waterPoint = useWaterPoint('viewDiv')
  const staticState: {
    view?: __esri.MapView
    markLayer?: __esri.GraphicsLayer
    mapClick?: __esri.Handle
    identifyResult?: any
  } = {}
  const init = (mapView?: __esri.MapView) => {
    destroy()
    staticState.view = mapView
    waterPoint.watchExtent(staticState.view)
    setMapCursor('crosshair')
    isPicking.value = true
    staticState.markLayer = getGraphicLayer(staticState.view, {
      id: 'burst-analys',
      title: '爆管标注'
    })
    staticState.mapClick = staticState.view?.on('click', async e => {
      staticState.markLayer?.removeAll()
      await doIdentify(e)
    })
  }

  const doIdentify = async (e: any) => {
    if (!staticState.view) return
    try {
      const queryParams = initIdentifyParams()
      queryParams.layerIds = getSubLayerIds(staticState.view, true)
      queryParams.geometry = e.mapPoint
      queryParams.mapExtent = staticState.view.extent
      const res = await excuteIdentify(
        window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
        queryParams
      )
      staticState.markLayer?.removeAll()
      const result = res.results?.filter(
        item => item.feature?.geometry?.type === 'polyline'
      )
      if (!result?.length) {
        SLMessage.warning('没有查询到管线')
        isPicking.value = false
        return
      }
      staticState.identifyResult = result[0]
      const burstPoint = result && getNeerestPoint(result[0]?.feature?.geometry, e.mapPoint)
      const picturemark = burstPoint
        && createPictureMarker(burstPoint.x, burstPoint.y, {
          picUrl: getPipeImageUrl('poi_burst.png'),
          spatialReference: staticState.view?.spatialReference,
          yOffset: 8
        })
      const feature = result[0].feature
      feature && (feature.symbol = setSymbol(feature.geometry.type) as any)
      result.length && staticState.markLayer?.add(feature)
      waterPoint.add(
        staticState.view,
        {
          id: feature.attributes.OBJECTID,
          point: burstPoint
        },
        {
          color: '#ff0000'
        }
      )
      staticState.markLayer?.add(picturemark)
      setMapCursor('')
      isPicking.value = false
      // gotoAndHighLight(staticState.view, feature)

      staticState.mapClick?.remove()
      staticState.mapClick = undefined
      const results = result.map(item => {
        return {
          layerName: item.layerName,
          layerId: item.layerId,
          value: item.feature.attributes?.OBJECTID,
          SID: item.feature.attributes?.['新编号'],
          attributes: item.feature?.attributes
        }
      }) || []
      TableConfig.value.dataList = (results.length && [results[0]]) || []
    } catch (error) {
      TableConfig.value.dataList = []
      staticState.markLayer?.removeAll()
      isPicking.value = false
    }
  }

  const TableConfig = ref<ITable>({
    columns: [
      { label: '管线类型', prop: 'layerName' },
      { label: '管线编号', prop: 'SID' }
    ],
    dataList: [],
    pagination: {
      hide: true
    }
  })
  const destroy = () => {
    setMapCursor('')
    TableConfig.value.dataList = []
    staticState.mapClick?.remove()
    staticState.markLayer && staticState.view?.map.remove(staticState.markLayer)
    staticState.mapClick = undefined
    staticState.identifyResult = undefined
    staticState.view = undefined
    waterPoint.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init,
    destroy,
    isPicking,
    TableConfig,
    staticState
  }
}

export default useIdentify
