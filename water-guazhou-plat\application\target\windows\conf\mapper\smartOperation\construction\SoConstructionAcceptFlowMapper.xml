<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionAcceptFlowMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        code,
        construction_apply_code,
        work_name,
        work_stage,
        begin_time,
        end_time,
        head_user,
        head_user_phone,
        work_content,
        remark,
        creator,
        create_time,
        tenant_id
        <!--@sql from so_construction_apply_flow -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyFlow">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="construction_apply_code" property="constructionApplyCode"/>
        <result column="work_name" property="workName"/>
        <result column="work_stage" property="workStage"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="head_user" property="headUser"/>
        <result column="head_user_phone" property="headUserPhone"/>
        <result column="work_content" property="workContent"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_construction_apply_flow
        <where>
            <if test="constructionApplyCode != null and constructionApplyCode != ''">
                construction_apply_code  = #{constructionApplyCode}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="updateFully">
        update so_construction_apply_flow
        set work_name       = #{workName},
            work_stage      = #{workStage},
            begin_time      = #{beginTime},
            end_time        = #{endTime},
            head_user       = #{headUser},
            head_user_phone = #{headUserPhone},
            work_content    = #{workContent},
            remark          = #{remark}
        where id = #{id}
    </update>
</mapper>