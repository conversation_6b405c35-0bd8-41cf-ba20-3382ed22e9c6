<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.guard.GuardRecordMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        place_id,
        (select address from guard_place where id = #{placeId})   place_name,
        deliver_group_id,
        deliver_group_name,
        deliver_group_user,
        case
            when length(deliver_group_user) > 0 then
                (select string_agg(coalesce(first_name, '用户不存在'), '、')
                 from regexp_split_to_table(deliver_group_user, ',') temp(uid)
                          left join tb_user
                                    on tb_user.id = temp.uid) end deliver_group_user_name,
        handle_group_id,
        handle_group_name,
        handle_group_user,
        case
            when length(handle_group_user) > 0 then
                (select string_agg(coalesce(first_name, '用户不存在'), '、')
                 from regexp_split_to_table(handle_group_user, ',') temp(uid)
                          left join tb_user
                                    on tb_user.id = temp.uid) end handle_group_user_name,
        class_name,
        begin_time,
        end_time,
        remark,
        exchange_user_id,
        exchange_time,
        creator,
        create_time,
        tenant_id
        <!--@sql from guard_record -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRecord">
        <result column="id" property="id"/>
        <result column="place_id" property="placeId"/>
        <result column="place_name" property="placeName"/>
        <result column="deliver_group_id" property="deliverGroupId"/>
        <result column="deliver_group_name" property="deliverGroupName"/>
        <result column="deliver_group_user" property="deliverGroupUser"/>
        <result column="deliver_group_user_name" property="deliverGroupUserName"/>
        <result column="handle_group_id" property="handleGroupId"/>
        <result column="handle_group_name" property="handleGroupName"/>
        <result column="handle_group_user" property="handleGroupUser"/>
        <result column="handle_group_user_name" property="handleGroupUserName"/>
        <result column="class_name" property="className"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="remark" property="remark"/>
        <result column="exchange_user_id" property="exchangeUserId"/>
        <result column="exchange_time" property="exchangeTime"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from guard_record
        <where>
            <if test="placeId != null and placeId != ''">
                place_id = #{placeId}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

</mapper>