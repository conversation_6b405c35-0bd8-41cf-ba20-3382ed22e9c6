import{d as R,c as C,am as h,i as d,r as N,a8 as M,em as P,en as g,b as k,eo as U,ep as B,cO as F,eq as V,g as f,n as I,p,q as y,F as q,aB as w,aJ as L,h as O,_ as H,bV as D,aH as Y,C as z}from"./index-r0dFAfgr.js";import G from"./Preview-C8ycjeyy.js";const J={class:"aou-form"},W={class:"form-main"},$={class:"form-content"},j={class:"form-footer"},K=R({__name:"AouForm",props:{menuParentId:{},menuRootId:{},sources:{},menus:{},menu:{}},emits:["success"],setup(S,{emit:x}){const T=x,r=C(),n=S;h(()=>n.menus,()=>{const e=s.group[0].fields.find(t=>t.field==="parentId"&&t.type==="select-tree");e&&(e.options=[{id:n.menuRootId,label:"根级菜单",value:n.menuRootId,children:[...d(n.menus)||[]]}])});const A=N([{perm:!0,type:"default",text:"重置",click:()=>_()},{perm:!0,type:"primary",text:"提交",click:()=>{var e;(e=r.value)==null||e.Submit()}}]),s=N({group:[{fields:[{type:"input",label:"标题:",field:"title",xs:12},{type:"select-tree",label:"父级菜单:",field:"parentId",xs:12,checkStrictly:!0,options:[]},{type:"icon-selector",label:"图标",field:"icon",xs:12},{type:"image",label:"自定义图标",field:"url",size:"small",multiple:!1,limit:1,xs:12},{type:"input-number",label:"序号",field:"orderNum",xs:12},{type:"input",label:"路由名称（唯一）:",field:"name"},{type:"input",label:"路由路径:",field:"path",rules:[{required:!0,message:"请输入路由路径"}]},{type:"radio-button",label:"菜单类型",field:"menutype",rules:[{required:!0,message:"请选择菜单类型"}],options:[{label:"菜单",value:"menu"},{label:"页面",value:"page"},{label:"iframe",value:"iframe"}]},{handleHidden(e,t,l){l.hidden=e.menutype!=="menu"},type:"radio-button",label:"布局组件：",field:"layout",options:[{label:"根级菜单",value:"Layout"},{label:"中间层菜单",value:"LayoutParentView"},{label:"无侧边菜单",value:"LayoutFull"}]},{handleHidden(e,t,l){l.hidden=e.menutype!=="iframe"},xs:12,type:"input",label:"iframe路径：",field:"iframe"},{handleHidden(e,t,l){l.hidden=e.menutype!=="iframe"},xs:12,type:"input",label:"token字段名（指定路径中携带的token的字段名,不填则表示不需要带token）：",field:"tokenField"},{handleHidden(e,t,l){l.hidden=e.menutype!=="page"},xs:12,type:"select",label:"资源路径（views目录）（不带文件后缀）:",field:"component",options:M(()=>n.sources||[])},{type:"checkbox",label:"访问权限:",field:"roles",options:[{label:"TENANT_ADMIN",value:"TENANT_ADMIN"},{label:"TENANT_SYS",value:"TENANT_SYS"},{label:"CUSTOMER_USER",value:"CUSTOMER_USER"}]},{type:"radio",label:"是否隐藏:",field:"hidden",options:[{label:"是",value:!0},{label:"否",value:!1}]},{type:"radio",label:"开启缓存:",field:"keepAlive",options:[{label:"是",value:!0},{label:"否",value:!1}]}]}],labelPosition:"top",defaultValue:{},submit:async e=>{debugger;const t=await P({data:{id:e.id,type:"109",orderNum:e.orderNum===0?"0":e.orderNum?e.orderNum+"":e.orderNum,path:e.path,name:e.name,url:e.url,meta:{title:e.title,icon:e.icon,roles:e.roles,hidden:e.hidden,keepAlive:e.keepAlive||!1},component:e.menutype==="menu"?e.layout||e.component:e.menutype==="iframe"?g+"?url="+encodeURIComponent(e.iframe||"")+"&tokenField="+(e.tokenField||""):e.component},parentId:e.parentId});t.data?(k.success(t.data.message||"操作成功"),T("success")):k.error(t.data.message||"操作失败")}}),E=e=>{if(e===void 0)return;const t=["Layout","LayoutParentView","LayoutFull"];return e.startsWith(g)&&e.indexOf("?")!==-1?"iframe":t.indexOf(e)!==-1?"menu":"page"};h(()=>n.menu,()=>{_()});const _=async()=>{var e,t,l,c,m,a,u;if(s.defaultValue={orderNum:"0",parentId:n.menuParentId,icon:U,roles:["TENANT_ADMIN","TENANT_SYS","CUSTOMER_USER"]},n.menu){const o=await B(n.menu.value);let b,i=(e=o==null?void 0:o.data)==null?void 0:e.component;const v=E(i);v==="iframe"&&(b=(t=F(i,"tokenField"))==null?void 0:t.value.toString(),i=V(i,"tokenField"),i=decodeURIComponent(((c=(l=F(i,"url"))==null?void 0:l.value)==null?void 0:c.toString())||"")),s.defaultValue={...s.defaultValue,...o.data,...(m=o.data)==null?void 0:m.meta,layout:(a=o==null?void 0:o.data)==null?void 0:a.component,iframe:i,tokenField:b,menutype:v}}(u=r.value)==null||u.resetForm()};return(e,t)=>{const l=H,c=D,m=Y;return f(),I("div",J,[p("div",W,[y(c,null,{default:q(()=>{var a,u;return[p("div",$,[y(l,{ref_key:"refForm",ref:r,config:d(s)},null,8,["config"]),y(G,{type:(a=d(r))==null?void 0:a.dataForm.menutype,source:(u=d(r))==null?void 0:u.dataForm.component},null,8,["type","source"])])]}),_:1})]),p("div",j,[(f(!0),I(w,null,L(d(A),(a,u)=>(f(),O(m,{key:u,config:a},null,8,["config"]))),128))])])}}}),Z=z(K,[["__scopeId","data-v-21086487"]]);export{Z as default};
