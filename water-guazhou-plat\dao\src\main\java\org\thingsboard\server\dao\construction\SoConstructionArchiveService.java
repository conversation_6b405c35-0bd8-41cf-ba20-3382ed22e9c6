package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionArchive;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionArchivePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionArchiveSaveRequest;

public interface SoConstructionArchiveService {
    /**
     * 分页条件查询工程归档信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoConstructionArchive> findAllConditional(SoConstructionArchivePageRequest request);

    /**
     * 保存工程归档信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoConstructionArchive save(SoConstructionArchiveSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoConstructionArchive entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 是否已完成
     *
     * @param constructionCode 编号
     * @param tenantId         客户id
     * @return 是否已完成
     */
    boolean isComplete(String constructionCode, String tenantId);

}
