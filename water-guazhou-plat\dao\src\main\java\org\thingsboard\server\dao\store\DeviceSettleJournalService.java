package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.store.DeviceSettleJournal;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceSettleJournalPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceSettleJournalSaveRequest;

import java.util.List;

public interface DeviceSettleJournalService {
    /**
     * 分页条件查询设备安装信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DeviceSettleJournal> findAllConditional(DeviceSettleJournalPageRequest request);

    /**
     * 保存设备安装信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    DeviceSettleJournal save(DeviceSettleJournalSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(DeviceSettleJournal entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);


    /**
     * 获取所有安装位置的描述
     *
     * @param tenantId 客户id
     * @return 安装位置的描述
     */
    List<String> findSettleNames(String tenantId);

}
