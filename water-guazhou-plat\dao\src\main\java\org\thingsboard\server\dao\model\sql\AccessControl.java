package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.springframework.format.annotation.DateTimeFormat;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_ACCESS_CONTROL_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class AccessControl {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_ACCESS_CONTROL_NAME)
    private String name;

    @Column(name = ModelConstants.TB_ACCESS_CONTROL_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.TB_ACCESS_CONTROL_DOOR_NAME)
    private String doorName;

    @Column(name = ModelConstants.TB_ACCESS_CONTROL_INSTALL_ADDRESS)
    private String installAddress;

    @Column(name = ModelConstants.TB_ACCESS_CONTROL_INSTALLER_NAME)
    private String installerName;

    @Column(name = ModelConstants.TB_ACCESS_CONTROL_INSTALL_TIME)
    private Date installTime;

    @Column(name = ModelConstants.TB_ACCESS_CONTROL_REMARK)
    private String remark;

    @Column(name = ModelConstants.TB_ACCESS_CONTROL_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.TB_ACCESS_CONTROL_TENANT_ID)
    private String tenantId;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    /**
     * 关联的视频列表（非数据库字段）
     */
    @Transient
    private List<VideoEntity> videoList;

    /**
     * 关联的视频ID列表（用于前端传递，非数据库字段）
     */
    @Transient
    private List<String> videoIds;

}
