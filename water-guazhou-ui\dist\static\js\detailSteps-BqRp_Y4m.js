import{d as v,c as x,r as h,am as E,g as i,n as o,h as c,F as l,aB as O,aJ as g,i as p,q as C,dw as k,dx as B,C as I}from"./index-r0dFAfgr.js";/* empty css                */import{l as w}from"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const y={class:"detailSteps"},D=v({__name:"detailSteps",props:{config:{}},setup(d){const n=d,u=x(0),s=h({OrderStatus:[],CurStatus:void 0}),f=()=>{var r;let e=w();s.CurStatus=e.find(t=>t.value===n.config.status),((r=s.CurStatus)==null?void 0:r.value)==="REJECTED"?e=e.filter(t=>t.value!=="APPROVED"):e=e.filter(t=>t.value!=="REJECTED"),e.findIndex(t=>t.value===n.config.status)===-1?s.OrderStatus=[{perm:!0,text:n.config.statusName}]:s.OrderStatus=e.map(t=>({perm:!0,text:t.label})),m()};function m(){var e;u.value=0;for(let a=0;a<s.OrderStatus.length;a++){if(((e=s.CurStatus)==null?void 0:e.label)===s.OrderStatus[a].text)return;u.value+=1}}return E(()=>n.config.status,()=>{f()}),(e,a)=>{const r=k,t=B;return i(),o("div",y,[s.OrderStatus.length>1?(i(),c(t,{key:0,class:"steps",active:p(u),"finish-status":"finish",simple:"","process-status":"success"},{default:l(()=>[(i(!0),o(O,null,g(s.OrderStatus,(_,S)=>(i(),c(r,{key:S,title:_.text||""},null,8,["title"]))),128))]),_:1},8,["active"])):(i(),c(t,{key:1,class:"steps",active:p(u),"finish-status":"finish",simple:"","process-status":"success"},{default:l(()=>[C(r,{title:"已终止"})]),_:1},8,["active"]))])}}}),P=I(D,[["__scopeId","data-v-cc02a623"]]);export{P as default};
