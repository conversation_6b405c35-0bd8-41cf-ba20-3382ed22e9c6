import{T as I,R as f}from"./index-r0dFAfgr.js";import{bF as T,bG as b}from"./MapView-DaoQedLH.js";async function E(e,s=e.popupTemplate){var l,o;if(I(s))return[];const i=await s.getRequiredFields(e.fieldsIndex),{lastEditInfoEnabled:c}=s,{objectIdField:t,typeIdField:n,globalIdField:a,relationships:u}=e;if(i.includes("*"))return["*"];const m=c?await T(e):[],p=b(e.fieldsIndex,[...i,...m]);return n&&p.push(n),p&&t&&((l=e.fieldsIndex)!=null&&l.has(t))&&!p.includes(t)&&p.push(t),p&&a&&((o=e.fieldsIndex)!=null&&o.has(a))&&!p.includes(a)&&p.push(a),u&&u.forEach(h=>{var r;const{keyField:d}=h;p&&d&&((r=e.fieldsIndex)!=null&&r.has(d))&&!p.includes(d)&&p.push(d)}),p}function y(e,s){return e.popupTemplate?e.popupTemplate:f(s)&&s.defaultPopupTemplateEnabled&&f(e.defaultPopupTemplate)?e.defaultPopupTemplate:null}export{E as d,y as s};
