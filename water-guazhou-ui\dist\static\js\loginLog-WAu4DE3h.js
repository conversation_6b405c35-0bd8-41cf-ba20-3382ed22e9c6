import{_ as y}from"./CardTable-rdWOL4_6.js";import{_ as v}from"./CardSearch-CB_HNR-Q.js";import{d as b,u as T,r as S,l as d,c as f,bu as N,g as D,n as E,q as _,x as M,C as Y}from"./index-r0dFAfgr.js";import{a as k}from"./index-BiPwaSSe.js";import{f as L}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const x={class:"wrapper"},C=b({__name:"loginLog",setup(P){const c=T(),p=S({filters:[{label:"搜索",field:"keyword",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>i()}]}],defaultParams:{timerange:[d().subtract(1,"days").format("YYYY-MM-DD HH:mm:ss"),d().format("YYYY-MM-DD HH:mm:ss")]}}),e=f({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"time",label:"时间",icon:"iconfont icon-shijian",iconStyle:{color:"#69E850",display:"inline-block","font-size":"16px"}},{prop:"name",label:"用户名"},{prop:"authority",label:"用户权限"},{prop:"options",label:"操作类型"},{prop:"typeDescription",label:"操作内容"}],pagination:{page:1,limit:20,total:0,handleSize:a=>{e.value.pagination&&(e.value.pagination.limit=a),i()},handlePage:a=>{e.value.pagination&&(e.value.pagination.page=a),i()}}}),s=f(null),i=async()=>{var n,o;e.value.loading=!0;const a={};s.value&&Object.assign(a,s.value.queryParams);const l={page:(n=e.value.pagination)==null?void 0:n.page,size:(o=e.value.pagination)==null?void 0:o.limit,keyword:a.keyword};try{k(l).then(r=>{if(r.status!==200)return;console.log(r);let g=[];g=r.data.data.map(u=>{const t=u;return t.name=u.firstName,t.time=L(u.createTime),t.authority==="TENANT_ADMIN"?t.authority="企业管理人员":t.authority==="TENANT_SYS"?t.authority="企业配置人员":t.authority==="CUSTOMER_USER"?t.authority="企业用户":t.authority==="SYS_ADMIN"&&(t.authority="超级管理员"),t.info==="Login"&&(t.info="登录"),t}),e.value.pagination&&(e.value.pagination.total=r.data.total),e.value.dataList=g,e.value.loading=!1})}catch{e.value.loading=!1}},m=f(!1),h=()=>{var n;const a=c.tenantList;if(!a.length){M.info("该账户下没有企业信息");return}const l=(n=p.filters)==null?void 0:n.find(o=>o.field==="");l&&(l.options=a.map(o=>({label:o.title,value:o.id}))),s.value.queryParams.tenantId=a[0].id,i()};return N(()=>{var a;if(m.value=c.roles[0]==="TENANT_SUPPORT"||c.roles[0]==="TENANT_PROMOTE",m.value){(a=p.filters)==null||a.splice(1,0,{label:"企业",field:"tenantId",type:"select",options:[]}),h();return}i()}),(a,l)=>{const n=v,o=y;return D(),E("div",x,[_(n,{ref_key:"cardSearch",ref:s,config:p},null,8,["config"]),_(o,{config:e.value,class:"card-table"},null,8,["config"])])}}}),j=Y(C,[["__scopeId","data-v-0b9406f7"]]);export{j as default};
