package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyUser;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class EmergencyUserPageRequest extends AdvancedPageableQueryEntity<EmergencyUser, EmergencyUserPageRequest> {
    // 人员名称，模糊
    private String name;

    // 所属部门
    private String deptId;
}
