import{_ as f}from"./CardTable-rdWOL4_6.js";import{_ as d}from"./CardSearch-CB_HNR-Q.js";import{d as u,c as i,s as _,r as g,o as b,g as h,n as y,q as l,i as p,b7 as k}from"./index-r0dFAfgr.js";import{I as x}from"./common-CvK_P_ao.js";import{d as C}from"./projectManagement-CDcrrCQ1.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const v={class:"wrapper"},B=u({__name:"memorabilia",setup(N){const n=i(),s=i({filters:[{label:"项目编号",field:"code",type:"input"},{label:"项目名称",field:"name",type:"input"},{label:"记录时间",field:"time",type:"daterange"}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"重置",svgIcon:_(k),click:()=>{var e;(e=n.value)==null||e.resetForm(),o()}},{perm:!0,text:"查询",icon:x.QUERY,click:()=>o()}]}]}),a=g({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"项目编号",prop:"code"},{label:"项目名称",prop:"name"},{label:"项目类别",prop:"typeName"},{label:"项目大事记",prop:"detail"},{label:"大事记备注",prop:"remark"},{label:"记录人",prop:"creatorName"},{label:"记录时间",prop:"createTimeName"}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,o()}}}),o=async()=>{var t;const e={size:a.pagination.limit||20,page:a.pagination.page||1,...((t=n.value)==null?void 0:t.queryParams)||{}};e.time&&(e.fromTime=e.time[0],e.toTime=e.time[1],delete e.time),C(e).then(r=>{a.dataList=r.data.data.data||[],a.pagination.total=r.data.data.total||0})};function c(){o()}return b(()=>{c()}),(e,t)=>{const r=d,m=f;return h(),y("div",v,[l(r,{ref_key:"refSearch",ref:n,config:p(s)},null,8,["config"]),l(m,{config:p(a),class:"card-table"},null,8,["config"])])}}});export{B as default};
