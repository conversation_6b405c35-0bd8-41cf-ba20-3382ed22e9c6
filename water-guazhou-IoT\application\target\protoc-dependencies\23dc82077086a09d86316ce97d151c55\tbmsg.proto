/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
syntax = "proto3";
package msgqueue;

option java_package = "org.thingsboard.server.common.msg.gen";
option java_outer_classname = "MsgProtos";

message TbMsgMetaDataProto {
    map<string, string> data = 1;
}

message TbMsgTransactionDataProto {
    string id = 1;
    string entityType = 2;
    int64 entityIdMSB = 3;
    int64 entityIdLSB = 4;
}

message TbMsgProto {
    string id = 1;
    string type = 2;
    string entityType = 3;
    int64 entityIdMSB = 4;
    int64 entityIdLSB = 5;

    int64 ruleChainIdMSB = 6;
    int64 ruleChainIdLSB = 7;

    int64 ruleNodeIdMSB = 8;
    int64 ruleNodeIdLSB = 9;
    int64 clusterPartition = 10;

    TbMsgMetaDataProto metaData = 11;

    TbMsgTransactionDataProto transactionData = 12;

    int32 dataType = 13;
    string data = 14;

}