package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseProductAuthorization;
import org.thingsboard.server.dao.util.imodel.query.base.BaseMessageConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.query.base.BaseProductAuthorizationPageRequest;

import java.util.List;

/**
 * 平台管理-产品授权Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Mapper
public interface BaseProductAuthorizationMapper {
    /**
     * 查询平台管理-产品授权
     *
     * @param id 平台管理-产品授权主键
     * @return 平台管理-产品授权
     */
    public BaseProductAuthorization selectBaseProductAuthorizationById(String id);

    /**
     * 查询平台管理-产品授权列表
     *
     * @param baseProductAuthorization 平台管理-产品授权
     * @return 平台管理-产品授权集合
     */
    public IPage<BaseProductAuthorization> selectBaseProductAuthorizationList(BaseProductAuthorizationPageRequest baseProductAuthorization);

    /**
     * 新增平台管理-产品授权
     *
     * @param baseProductAuthorization 平台管理-产品授权
     * @return 结果
     */
    public int insertBaseProductAuthorization(BaseProductAuthorization baseProductAuthorization);

    /**
     * 修改平台管理-产品授权
     *
     * @param baseProductAuthorization 平台管理-产品授权
     * @return 结果
     */
    public int updateBaseProductAuthorization(BaseProductAuthorization baseProductAuthorization);

    /**
     * 删除平台管理-产品授权
     *
     * @param id 平台管理-产品授权主键
     * @return 结果
     */
    public int deleteBaseProductAuthorizationById(String id);

    /**
     * 批量删除平台管理-产品授权
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseProductAuthorizationByIds(@Param("array") List<String> ids);
}
