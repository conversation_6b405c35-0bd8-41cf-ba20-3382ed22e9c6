import {
  DeletePartition,
  GetDM<PERSON>HookedDevices,
  GetHookedDeviceTree,
  GetPartitionDetail,
  GetPartitionList,
  GetPartitionTree,
  PostPartition
} from '@/api/mapservice/dma'
import { formatTree } from '@/utils/GlobalHelper'
import { createGeometry, createGraphic, getGraphicLayer, setSymbol } from '@/utils/MapHelper'
import { SLConfirm, SLMessage } from '@/utils/Message'

export const usePartition = () => {
  const Tree = ref<any[]>([])
  const List = ref<any[]>([])
  const DeviceTree = ref<NormalOption[]>([])
  const getTree = async () => {
    try {
      const res = await GetPartitionTree()
      Tree.value = formatTree(res.data || [], undefined, [], undefined, item => {
        if (!item.path) return item
        if (item.data?.type === '1') {
          item.iconifyIcon = 'ep:folder'
          item.iconStyle = {
            color: 'orange'
          }
        } else if (item.data?.type === '2') {
          item.iconifyIcon = 'ep:collection-tag'
          item.iconStyle = {
            color: '#318DFF'
          }
        } else {
          item.iconifyIcon = 'ep:collection-tag'
          item.iconStyle = {
            color: '#318DFF'
          }
        }
        return item
      })
      return res
    } catch (error) {
      console.log('获取分区树失败')
    }
  }
  const getList = async (params?: { name?: string }) => {
    try {
      const res = await GetPartitionList(params)
      List.value = res.data || []
      return res
    } catch (error) {
      console.log('获取分区列表失败')
    }
  }
  const Delete = async (ids: string[]) => {
    const confirm = await SLConfirm('提示信息', '确定删除？')
    console.log(confirm)
    if (confirm === 'confirm') {
      try {
        await DeletePartition(ids)
        SLMessage.success('删除成功')
        await getTree()
      } catch (error: any) {
        console.log(error)
        SLMessage.error('删除失败')
      }
    }
  }
  const Post = async (params: any) => {
    const confirm = await SLConfirm('提示信息', '确定提交？')
    if (confirm === 'confirm') {
      try {
        const res = await PostPartition(params)
        if (res.data.code === 200) {
          SLMessage.success('提交成功')
          getTree()
        } else {
          SLMessage.error('提交失败')
        }
      } catch (error) {
        SLMessage.error('提交失败')
      }
    }
  }
  const TreeProps = () => {
    return {
      label: 'label',
      children: 'children'
    }
  }

  let partitionLayer: __esri.GraphicsLayer | undefined
  const getPartitionGraphic = (id: string) => {
    return partitionLayer?.graphics.find(item => item.attributes?.id === id)
  }
  const refreshPartitions = async (view?: __esri.MapView) => {
    await getList()
    partitionLayer = getGraphicLayer(view, {
      id: 'partition-dma-layer',
      title: '分区'
    })
    const gs: __esri.Graphic[] = []
    List.value.map(item => {
      if (!item.geom) return
      const rings = JSON.parse(item.geom)
      const g = createGraphic({
        geometry: createGeometry('polygon', rings, view?.spatialReference),
        symbol: setSymbol('polygon', {
          color: item.rangeColor,
          outlineColor: item.borderColor
        }),
        attributes: item
      })
      g && gs.push(g)
    }) || []
    partitionLayer?.removeAll()
    partitionLayer?.addMany(gs)
  }
  let curPartitionLayer: __esri.GraphicsLayer | undefined
  const extentToPartition = (view?: __esri.MapView, range?: number[][][]) => {
    if (!view || !range?.length) return
    curPartitionLayer = getGraphicLayer(view, {
      id: 'partition-cur-layer',
      title: '当前分区'
    })
    curPartitionLayer?.removeAll()
    const g = createGraphic({
      geometry: createGeometry('polygon', range, view?.spatialReference),
      symbol: setSymbol('polygon')
    })
    if (!g) return
    curPartitionLayer?.add(g)
    view?.goTo(g)
  }
  const Devices = ref<any[]>([])
  const getDevices = async (
    params: IQueryPagerParams & {
      partitionId?: string
      name?: string
      type?: string
    }
  ) => {
    const res = await GetDMAHookedDevices(params)
    Devices.value = res.data?.data?.data
    return res.data?.data
  }
  const getDeviceTree = async (params: { type: 'flow' | 'pressure' }) => {
    try {
      const res = await GetHookedDeviceTree(params)
      DeviceTree.value = formatTree(res.data.data || [], undefined, [], undefined, item => {
        if (!item.path) return item
        if (item.data.type === '1') {
          item.iconifyIcon = 'ep:folder'
          item.iconStyle = {
            color: '#e6a23c'
          }
        } else if (item.data.type === '2') {
          item.iconifyIcon = 'ep:collection-tag'
          item.iconStyle = {
            color: '#318DFF'
          }
        } else {
          item.iconifyIcon = 'ep:location-information'
          item.iconStyle = {
            color: '#38ceff'
          }
        }
        return item
      })
      return res
    } catch (error) {
      console.log('获取分区树失败')
    }
  }
  const getPartitionInfo = async (partitionId: string) => {
    const res = await GetPartitionDetail(partitionId)
    return res
  }
  const createPartitionGraphic = (view?: __esri.MapView, range?: number[][][]) => {
    if (!range || !view) return
    const g = createGraphic({
      geometry: createGeometry('polygon', range, view.spatialReference),
      symbol: setSymbol('polygon')
    })
    return g
  }
  return {
    getList,
    getTree,
    List,
    Tree,
    TreeProps,
    Devices,
    getDevices,
    DeviceTree,
    getDeviceTree,
    Delete,
    Post,
    refreshPartitions,
    getPartitionGraphic,
    extentToPartition,
    getPartitionInfo,
    createPartitionGraphic
  }
}
