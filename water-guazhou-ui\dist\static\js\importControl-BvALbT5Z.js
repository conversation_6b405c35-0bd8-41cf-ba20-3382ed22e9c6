import{b as c,at as T}from"./index-r0dFAfgr.js";import{s as C}from"./index-BggOjNGp.js";const x={NBMQTT:["name","propertyCategory","statType","propertyType","unit","sampleDeviation","dataOffset","samplingMax","samplingMin","sampleCoef","unitCoef","range","formulaProperty","group"],MQTT:["name","propertyCategory","statType","propertyType","unit","sampleDeviation","dataOffset","samplingMax","samplingMin","sampleCoef","unitCoef","range","formulaProperty","group"],MODBUS:["name","propertyCategory","statType","dataType","propertyType","unit","registerType","functionCode","registerAddress","byteCount","bitPosition","registerSignFlag","sampleDeviation","order","byteOrder","dataOffset","samplingMax","samplingMin","sampleCoef","unitCoef","range","formulaProperty","group"],DTU:["name","propertyCategory","statType","dataType","propertyType","unit","registerType","functionCode","registerAddress","byteCount","bitPosition","registerSignFlag","sampleDeviation","order","byteOrder","dataOffset","samplingMax","samplingMin","sampleCoef","unitCoef","range","formulaProperty","group"],NBDTU:["name","propertyCategory","statType","dataType","propertyType","unit","registerType","functionCode","registerAddress","byteCount","bitPosition","registerSignFlag","sampleDeviation","order","byteOrder","dataOffset","samplingMax","samplingMin","sampleCoef","unitCoef","range","formulaProperty","group"]};async function h(e,l,f,r){const o=[],s=l.name.split(".");if(s[s.length-1]!=="xlsx"){c.error("仅支持Excel文件数据上传! 请下载模板后在模板编辑");return}const t=new FileReader;t.readAsArrayBuffer(l),t.onloadend=()=>{const n=t.result;new T.Workbook().xlsx.load(n).then(m=>{m.worksheets[0].eachRow((p,a)=>{const i={};console.log("全：",p),a>2&&(console.log("有效",a,p),x[f].forEach((g,y)=>{i[g]=p.values[y+1]}),o.push(i))}),console.log(o,"dataList"),r.protocolList=o,e.$message("保存中 请稍后"),C(r).then(()=>{e.$message("保存成功"),e.uploadDis=!1,e.getTemlate()})})}}async function D(e,l,f,r){const o=[],s=l.get("file"),t=s.name.split(".");if(t[t.length-1]!=="xlsx"){c.error("仅支持Excel文件数据上传! 请下载模板后在模板编辑");return}const n=new FileReader;n.readAsArrayBuffer(s),n.onloadend=()=>{const d=n.result;new T.Workbook().xlsx.load(d).then(u=>{u.worksheets[0].eachRow((a,i)=>{const g={};console.log("全：",a),i>2&&(console.log("有效",i,a),x[f].forEach((y,b)=>{g[y]=a.values[b+1]}),o.push(g))}),console.log(o,"dataList"),r.protocolList=o,e.$message("保存中 请稍后"),C(r).then(()=>{e.$message("保存成功"),e.uploadDis=!1,e.getTemlate()})})}}export{h as i,D as n};
