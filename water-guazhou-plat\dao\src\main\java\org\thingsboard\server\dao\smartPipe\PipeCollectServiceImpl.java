package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PipeCollectRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollect;
import org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollectData;
import org.thingsboard.server.dao.model.sql.smartService.system.SystemWorkOrderType;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderDetail;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.orderWork.NewlyWorkOrderService;
import org.thingsboard.server.dao.sql.smartPipe.PipeCollectDataMapper;
import org.thingsboard.server.dao.sql.smartPipe.PipeCollectMapper;
import org.thingsboard.server.dao.sql.smartService.system.SystemWorkOrderTypeMapper;
import org.thingsboard.server.dao.sql.workOrder.NewlyWorkOrderMapper;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderDetailMapper;
import org.thingsboard.server.dao.util.RedisUtil;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderAssignRequest;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderStageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

@Service
public class PipeCollectServiceImpl implements PipeCollectService {

    @Autowired
    protected SystemWorkOrderTypeMapper systemWorkOrderTypeMapper;

    @Autowired
    private NewlyWorkOrderMapper newlyWorkOrderMapper;

    @Autowired
    private NewlyWorkOrderService newlyWorkOrderService;

    @Autowired
    private WorkOrderDetailMapper workOrderDetailMapper;

    @Autowired
    private PipeCollectMapper pipeCollectMapper;

    @Autowired
    private PipeCollectDataMapper pipeCollectDataMapper;

    @Override
    public PipeCollect save(PipeCollect pipeCollect) {
        if (StringUtils.isBlank(pipeCollect.getId())) {
            pipeCollect.setCreateTime(new Date());
            pipeCollect.setCode(RedisUtil.nextId(DataConstants.REDIS_KEY.PIPE_COLLECT, ""));

            // 生成工单
            WorkOrder workOrder = new WorkOrder();
            workOrder.setCreateTime(new Date());
            if (StringUtils.isNotBlank(pipeCollect.getProcessUser())) {
                pipeCollect.setStatus("1");
                workOrder.setStatus(WorkOrderStatus.ASSIGN);
            } else {
                pipeCollect.setStatus("0");
                workOrder.setStatus(WorkOrderStatus.PENDING);
            }
            SystemWorkOrderType systemWorkOrderType = systemWorkOrderTypeMapper.selectByName("管网采集");
            if (systemWorkOrderType != null) {
                workOrder.setTitle(systemWorkOrderType.getName());
                workOrder.setType(systemWorkOrderType.getName());
            } else {
                workOrder.setTitle(pipeCollect.getName());
                workOrder.setType("管网采集");
            }

            workOrder.setAddress(pipeCollect.getAddress());
            workOrder.setRemark(pipeCollect.getRemark());
            // 工单来源
            workOrder.setSource("管网采集");
            workOrder.setAddress(pipeCollect.getAddress());
            workOrder.setLevel("一般");
            workOrder.setReceiveDepartmentId(pipeCollect.getProcessUserDepartmentId());
            workOrder.setUploadUserId(pipeCollect.getCreator());
            // workOrder.setUploadPhone(callWorkOrder.getPhone());
            workOrder.setUploadAddress(pipeCollect.getAddress());

            workOrder.setProcessUserId(pipeCollect.getCreator());
            workOrder.setDirectDispatch(false);
            workOrder.setUploadUserId(pipeCollect.getCreator());
            workOrder.setOrganizerId(pipeCollect.getCreator());
            workOrder.setTenantId(pipeCollect.getTenantId());
            workOrder.setProcessLevel(12 * 60);
            workOrder.setStepProcessUserId(pipeCollect.getProcessUser());
            workOrder.setProcessUserId(pipeCollect.getProcessUser());
            workOrder.setEstimatedFinishTime(new Date(workOrder.getCreateTime().getTime() + (workOrder.getProcessLevel() * 60 * 1000)));
            workOrder.setSerialNo(RedisUtil.nextId(DataConstants.REDIS_KEY.WORK_ORDER, ""));
            newlyWorkOrderMapper.insert(workOrder);
            if (StringUtils.isNotBlank(pipeCollect.getProcessUser())) {
                WorkOrderAssignRequest workOrderAssignRequest = new WorkOrderAssignRequest();

                workOrderAssignRequest.setProcessLevel(workOrder.getProcessLevel());
                workOrderAssignRequest.setStepProcessUserId(workOrder.getStepProcessUserId());
                WorkOrderDetail detail = workOrderAssignRequest.process(workOrder);
                detail.setProcessUserId(pipeCollect.getCreator());
                detail.setProcessRemark("管网采集派发");
                workOrderDetailMapper.insert(detail);
            }
            newlyWorkOrderMapper.updateById(workOrder);

            pipeCollect.setWorkOrderId(workOrder.getId());
            pipeCollectMapper.insert(pipeCollect);

        } else {
            pipeCollectMapper.updateById(pipeCollect);
        }
        return pipeCollect;
    }

    @Override
    public PageData<PipeCollect> getList(PipeCollectRequest request) {
        IPage<PipeCollect> iPage = new Page<>(request.getPage(), request.getSize());
        iPage = pipeCollectMapper.getList(iPage, request);
        return new PageData<>(iPage.getTotal(), iPage.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        pipeCollectMapper.deleteBatchIds(ids);
    }

    @Override
    public IstarResponse assign(JSONObject params) {
        String id = params.getString("id");
        String userId = params.getString("userId");
        String processUser = params.getString("processUser");
        String reviewUser = params.getString("reviewUser");
        PipeCollect pipeCollect = pipeCollectMapper.selectById(id);
        if (pipeCollect == null) {
            return IstarResponse.error("该工程不存在");
        }
        pipeCollect.setProcessUser(processUser);
        pipeCollect.setReviewUser(reviewUser);
        pipeCollect.setStatus("1");
        pipeCollectMapper.updateById(pipeCollect);

        WorkOrder workOrder = newlyWorkOrderService.find(pipeCollect.getWorkOrderId());
        workOrder.setProcessUserId(processUser);
        workOrder.setStepProcessUserId(processUser);
        newlyWorkOrderService.updateById(workOrder);
        WorkOrderStageRequest request = new WorkOrderStageRequest();
        request.setStage(WorkOrderStatus.ASSIGN.name());
        request.setNextProcessUserId(processUser);
        newlyWorkOrderService.addStage(pipeCollect.getWorkOrderId(), request);

        return IstarResponse.ok();
    }

    @Override
    public List<String> getLayerIdList(String mainId) {
        return pipeCollectDataMapper.getLayerIdList(mainId);
    }

    @Override
    public IstarResponse receive(String id, String userId) {
        PipeCollect pipeCollect = pipeCollectMapper.selectById(id);
        if (pipeCollect == null) {
            return IstarResponse.error("该工程不存在");
        }
        if (!userId.equals(pipeCollect.getProcessUser())) {
            return IstarResponse.error("您没有处理权限");
        }
        pipeCollect.setReceiveTime(new Date());
        pipeCollect.setStatus("2");
        pipeCollectMapper.updateById(pipeCollect);

        WorkOrderStageRequest request = new WorkOrderStageRequest();
        request.setStage(WorkOrderStatus.PROCESSING.name());
        newlyWorkOrderService.addStage(pipeCollect.getWorkOrderId(), request);

        return IstarResponse.ok();
    }

    @Override
    public IstarResponse submit(JSONObject params) {
        String id = params.getString("id");
        String userId = params.getString("userId");
        String remark = params.getString("remark");
        String status = params.getString("status");
        PipeCollect pipeCollect = pipeCollectMapper.selectById(id);
        if (pipeCollect == null) {
            return IstarResponse.error("该工程不存在");
        }
        if (!userId.equals(pipeCollect.getProcessUser())) {
            return IstarResponse.error("您没有处理权限");
        }
        pipeCollect.setProcessTime(new Date());
        pipeCollect.setProcessRemark(remark);
        pipeCollect.setStatus("3");
        pipeCollectMapper.updateById(pipeCollect);

        WorkOrderStageRequest request = new WorkOrderStageRequest();
        request.setStage(WorkOrderStatus.SUBMIT.name());
        request.setNextProcessUserId(pipeCollect.getReviewUser());
        request.setProcessRemark(remark);
        newlyWorkOrderService.addStage(pipeCollect.getWorkOrderId(), request);

        return IstarResponse.ok();
    }

    @Override
    public IstarResponse review(JSONObject params) {
        String id = params.getString("id");
        String userId = params.getString("userId");
        String remark = params.getString("remark");
        String status = params.getString("status");
        PipeCollect pipeCollect = pipeCollectMapper.selectById(id);
        if (pipeCollect == null) {
            return IstarResponse.error("该工程不存在");
        }
        if (!userId.equals(pipeCollect.getProcessUser())) {
            return IstarResponse.error("您没有处理权限");
        }

        pipeCollect.setReviewTime(new Date());
        pipeCollect.setReviewRemark(remark);
        pipeCollect.setStatus(status);
        pipeCollectMapper.updateById(pipeCollect);

        WorkOrderStageRequest request = new WorkOrderStageRequest();
        if ("4".equals(status)) {
            request.setStage(WorkOrderStatus.COMPLETE.name());
            // 设置状态为入库
            PipeCollectData pipeCollectData = new PipeCollectData();
            pipeCollectData.setIsStorage("1");
            QueryWrapper<PipeCollectData> pipeCollectDataQueryWrapper = new QueryWrapper<>();
            pipeCollectDataQueryWrapper.eq("main_id", id);
            pipeCollectDataMapper.update(pipeCollectData, pipeCollectDataQueryWrapper);
        } else {
            request.setStage(WorkOrderStatus.REJECTED.name());
        }
        request.setProcessRemark(remark);
        newlyWorkOrderService.addStage(pipeCollect.getWorkOrderId(), request);

        return IstarResponse.ok();
    }

    @Override
    public IstarResponse storage(String id) {
        PipeCollect pipeCollect = pipeCollectMapper.selectById(id);
        if ("6".equals(pipeCollect.getStatus())) {
            return IstarResponse.ok("入库成功");
        }
        if (!"4".equals(pipeCollect.getStatus())) {
            return IstarResponse.error("当前状态不允许入库");
        }
        pipeCollect.setStatus("6");
        pipeCollectMapper.updateById(pipeCollect);

        // 数据入库
        pipeCollectMapper.storage(id);
        return IstarResponse.ok("入库成功");
    }
}
