<template>
  <SLDrawer ref="refDrawer" :config="DrawerConfig">
    <AppMenuSearch @change="handleKeyWordChange" @close="close"></AppMenuSearch>
    <AppHeadMenu ref="refAppHeadMenu"></AppHeadMenu>
    <AppSubMenu ref="refAppSubMenu"></AppSubMenu>
  </SLDrawer>
</template>
<script lang="ts" setup>
import SLDrawer from '@/components/SLDrawer/index.vue';
import { useAppStore } from '@/store';
import AppHeadMenu from './AppHeadMenu.vue';
import AppMenuSearch from './AppMenuSearch.vue';
import AppSubMenu from './AppSubMenu.vue';

const appStore = useAppStore();
const refDrawer = ref<InstanceType<typeof SLDrawer>>();
const DrawerConfig = reactive<IDrawerConfig>({
  direction: 'ttb',
  withHeader: false,
  cancel: false,
  width: 'auto',
  className: 'headerbar-menu-drawer',
  group: [],
  onClosed: () => {
    appStore.TOGGLE_menuShow(false);
  }
});
const refAppHeadMenu = ref<InstanceType<typeof AppHeadMenu>>();
const refAppSubMenu = ref<InstanceType<typeof AppSubMenu>>();
const handleKeyWordChange = (val: string) => {
  if (!refAppHeadMenu.value) return;
  refAppHeadMenu.value.keyword = val;
  if (!refAppSubMenu.value) return;
  refAppSubMenu.value.keyword = val;
};
const close = () => {
  refDrawer.value?.toggle(false);
};
watch(
  () => appStore.menuShow,
  (value) => {
    refDrawer.value?.toggle(value);
  }
);
watch(
  () => appStore.subMenuShow,
  () => {
    //
  }
);
onMounted(() => {
  // 处理当菜单横纵切换时菜单不显示的bug
  refDrawer.value?.toggle(appStore.menuShow);
});
</script>
<style lang="scss" scoped>
.headerbar-menu {
  font-family: 'PingFang SC';
}
</style>
