import{P as a,h as c}from"./AnimatedLinesLayer-B2VbV4jv.js";import{Q as l}from"./index-r0dFAfgr.js";const f=()=>{let e,t;const s=(n,u)=>{var o;if(n)return e=new a({view:n,multipleSelectionEnabled:!0,listItemCreatedFunction:async p=>{const r=p.item;r.layer.type!=="group"&&(r.panel={content:"legend",open:!1})}}),t=new c({view:n,content:e,expandTooltip:"图层管理"}),t&&((o=n.ui)==null||o.add(t,u||"top-right")),t},i=()=>{e==null||e.destroy(),t==null||t.destroy()};return l(()=>{i()}),{init:s}};export{f as u};
