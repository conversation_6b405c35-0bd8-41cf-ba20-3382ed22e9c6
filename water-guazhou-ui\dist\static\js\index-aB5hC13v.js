import{_ as O}from"./ArcDraw.vue_vue_type_script_setup_true_lang-Hk5OVbKn.js";import{d as z,c as h,r as C,bH as _,l as x,b as n,S as G,a8 as l,o as R,g as j,h as T,F as J,q as k,i as S,aq as V,_ as $,C as B}from"./index-r0dFAfgr.js";import{_ as W}from"./Search-NSrhrIa_.js";import{g as P}from"./MapView-DaoQedLH.js";import{b as H,c as E,d as K}from"./engineeringDocuments-DYprVB7x.js";import Q from"./RightDrawerMap-D5PhmGFO.js";import{f as U}from"./DateFormatter-Bm9a68Ax.js";import{q as X}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as Y}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import"./arcWidgetButton-0glIxrt7.js";import"./widget-BcWKanF2.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Z=z({__name:"index",setup(w){const D=h(),M=h(),s=h(),g=h(),o=h(!1),N=C({size:"small",filters:[{type:"input",label:"工程名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>b()},{perm:!0,text:"添加",iconifyIcon:"ep:plus",click:()=>F()}]}],defaultParams:{}}),c=C({columns:[{label:"工程名称",prop:"name"},{label:"竣工日期",prop:"completeDate",formatter(e,t){return U(t,_)}},{label:"长度",prop:"length"},{label:"管网进度",prop:"status"}],dataList:[],operationWidth:200,operations:[{perm:!0,text:"编辑",iconifyIcon:"ep:edit-pen",circle:!0,click:e=>F(e)},{perm:!0,text:"定位",iconifyIcon:"ep:location",circle:!0,type:"success",click:e=>q(e)},{perm:!0,text:"删除",iconifyIcon:"ep:delete",circle:!0,type:"danger",click:e=>A(e)}],pagination:{refreshData:({page:e,size:t})=>{c.pagination.page=e||1,c.pagination.limit=t||20,b()}}}),F=e=>{var m,r,a,d,y,v;if(o.value=!1,(m=u.graphicsLayer)==null||m.removeAll(),(r=s.value)==null||r.clear(),f.defaultValue={...e?{...e,buildDate:e.buildDate?x(e.buildDate).format(_):void 0,completeDate:e.completeDate?x(e.completeDate).format(_):void 0}:{status:"规划"}},(a=g.value)==null||a.toggleCustomDetail(!0),(d=D.value)==null||d.resetForm(),!e)return;const t=e==null?void 0:e.geo;if(!t){n.error("暂无位置信息");return}const p=JSON.parse(t).map(L=>P.fromJSON(L));(y=s.value)==null||y.setGraphics(p),(v=u.view)==null||v.goTo(p)},q=e=>{var m,r,a,d,y,v;const t=e.geo;if(o.value=!0,f.defaultValue={...e?{...e,buildDate:e.buildDate?x(e.buildDate).format(_):void 0,completeDate:e.completeDate?x(e.completeDate).format(_):void 0}:{status:"规划"}},(m=D.value)==null||m.resetForm(),!t){n.error("暂无位置信息");return}const p=JSON.parse(t).map(L=>P.fromJSON(L));(r=u.graphicsLayer)==null||r.removeAll(),(a=u.graphicsLayer)==null||a.addMany(p),(d=u.view)==null||d.goTo(p),(y=s.value)==null||y.clear(),(v=g.value)==null||v.toggleCustomDetail(!0)},A=e=>{if(!(e?[e.id]:[]).length){n.error("请选择要删除的数据");return}G("确定删除？","提示").then(async()=>{try{const i=await H([e.id]);i.data.code===200?(n.success("删除成功"),b(),c.currentRow=void 0):(n.error("删除失败"),console.log(i.data.message))}catch{n.error("删除失败")}})},b=()=>{var t;const e=((t=M.value)==null?void 0:t.queryParams)||{};E({page:c.pagination.page||1,size:c.pagination.limit||20,...e}).then(i=>{c.dataList=i.data.data.data||[],c.pagination.total=i.data.data.total||0}).catch(i=>{console.log(i)})},f=C({gutter:0,size:"small",group:[{fields:[{disabled:l(()=>o.value),type:"input",label:"工程名称",field:"name",rules:[{required:!0,message:"请输入工程名称"}]},{type:"btn-group",label:"建设管网",btns:[{perm:!0,text:"绘制",type:"success",disabled:l(()=>o.value),click:()=>{var e;(e=s.value)==null||e.initDraw("polyline")}},{perm:!0,text:"清除",disabled:l(()=>o.value),type:"danger",click:()=>{G("确定清除绘制吗?","提示").then(()=>{var e;(e=s.value)==null||e.clear()}).catch(()=>{})}}]},{disabled:l(()=>o.value),type:"input",label:"建设单位",field:"createDept"},{disabled:l(()=>o.value),type:"input",label:"设计单位",field:"designDept"},{disabled:l(()=>o.value),type:"input",label:"施工单位",field:"buildDept"},{readonly:l(()=>o.value),type:"date",label:"施工日期",field:"buildDate"},{readonly:l(()=>o.value),type:"date",label:"竣工日期",field:"completeDate"},{disabled:l(()=>o.value),type:"input",label:"主要材料",field:"mainMaterial"},{readonly:l(()=>o.value),type:"input-number",label:"主要口径",field:"caliber"},{readonly:l(()=>o.value),type:"radio",label:"管网进度",field:"status",options:[{label:"规划",value:"规划"},{label:"建设",value:"建设"},{label:"完工",value:"完工"}]},{readonly:l(()=>o.value),type:"file",label:"附件",field:"file"},{readonly:l(()=>o.value),type:"btn-group",style:{justifyContent:"right"},btns:[{perm:!0,text:()=>o.value?"关闭":"取消",size:"default",type:"default",disabled:()=>f.submitting===!0,click:()=>{var e;(e=g.value)==null||e.toggleCustomDetail(!1)}},{perm:()=>!o.value,text:"确定",size:"default",type:"primary",loading:()=>f.submitting===!0,click:()=>{var e;(e=D.value)==null||e.Submit()}}]}]}],labelPosition:"right",labelWidth:"100px",defaultValue:{progress:"1"},submit:e=>{var p,m;console.log(e);const t=(p=s.value)==null?void 0:p.getGraphics().map(r=>r.toJSON());console.log(t),f.submitting=!0;const i=(m=s.value)==null?void 0:m.getGraphics().map(r=>{var a;return X(r.geometry.paths[0],"meters",(a=u.view)==null?void 0:a.spatialReference)});K({...e,length:i==null?void 0:i.reduce((r,a)=>r+a,0),geo:t?JSON.stringify(t):void 0}).then(r=>{var a;r.data.code===200?(n.success("添加成功"),b(),(a=g.value)==null||a.toggleCustomDetail(!1)):(n.error("添加失败"),console.log(r.data.message))}).catch(r=>{n.error("添加失败"),console.log(r)}).finally(()=>{f.submitting=!1})}}),u={view:void 0,graphicsLayer:void 0},I=e=>{u.view=e,u.graphicsLayer=Y(e,{id:"engi-pipes",title:"建设管网"})};return R(()=>{b()}),(e,t)=>{const i=W,p=V,m=$,r=O;return j(),T(Q,{ref_key:"refRightDrawerMap",ref:g,title:"工程录入","right-drawer-width":800,"panel-custom-class":"engineering-panel","detail-title":"添加工程",onDetailClosed:t[0]||(t[0]=a=>{var d;return(d=S(s))==null?void 0:d.clear()}),onMapLoaded:I},{"detail-default":J(()=>[k(m,{ref_key:"refForm",ref:D,config:S(f)},null,8,["config"])]),"map-bars":J(()=>[k(r,{ref_key:"refArcDraw",ref:s,layerid:"drawing-pipes",layername:"绘制管网",multiple:!0},null,512)]),default:J(()=>[k(i,{ref_key:"refSearch",ref:M,config:S(N)},null,8,["config"]),k(p,{class:"table-box",config:S(c)},null,8,["config"])]),_:1},512)}}}),ir=B(Z,[["__scopeId","data-v-029b288e"]]);export{ir as default};
