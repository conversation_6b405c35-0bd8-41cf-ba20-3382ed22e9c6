package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderWebSetting;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderWebSettingMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class WorkOrderWebSettingServiceImpl implements WorkOrderWebSettingService {

    @Autowired
    private WorkOrderWebSettingMapper workOrderWebSettingMapper;


    @Override
    public List<WorkOrderWebSetting> findList(TenantId tenantId) {
        QueryWrapper<WorkOrderWebSetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId()));
        return workOrderWebSettingMapper.selectList(queryWrapper);
    }

    @Override
    public void save(WorkOrderWebSetting entity) {
        if (StringUtils.isNotBlank(entity.getId())) {
            workOrderWebSettingMapper.updateById(entity);
        } else {
            entity.setCreateTime(new Date());
            workOrderWebSettingMapper.insert(entity);
        }
    }


}
