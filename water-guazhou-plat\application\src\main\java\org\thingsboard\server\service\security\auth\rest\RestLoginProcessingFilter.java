/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.security.auth.rest;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.optionLog.OptionLog;
import org.thingsboard.server.common.data.optionLog.OptionType;
import org.thingsboard.server.dao.optionLog.OptionLogService;
import org.thingsboard.server.dao.sql.user.UserCredentialsRepository;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.security.exception.AuthMethodNotSupportedException;
import org.thingsboard.server.service.security.model.UserPrincipal;
import org.thingsboard.server.service.utils.Aes;
import org.thingsboard.server.service.utils.IPUtils;
import sun.rmi.runtime.Log;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Base64;
import java.util.Date;

@Slf4j
public class RestLoginProcessingFilter extends AbstractAuthenticationProcessingFilter {

    private final AuthenticationSuccessHandler successHandler;
    private final AuthenticationFailureHandler failureHandler;

    private final ObjectMapper objectMapper;

    @Autowired
    private OptionLogService optionLogService;

    @Autowired
    private UserCredentialsRepository userCredentialsRepository;

    private CloseableHttpClient httpClient = HttpClients.createDefault();

    @Value("${login.yingshou}")
    private String YingShouLoginUrl;

    public RestLoginProcessingFilter(String defaultProcessUrl, AuthenticationSuccessHandler successHandler,
                                     AuthenticationFailureHandler failureHandler, ObjectMapper mapper) {
        super(defaultProcessUrl);
        this.successHandler = successHandler;
        this.failureHandler = failureHandler;
        this.objectMapper = mapper;
    }

    /**
     * 登录认证
     * @param request
     * @param response
     * @return
     * @throws AuthenticationException
     * @throws IOException
     * @throws ServletException
     */
    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException, IOException, ServletException {
        if (!HttpMethod.POST.name().equals(request.getMethod())) {
            if(log.isDebugEnabled()) {
                log.debug("Authentication method not supported. Request method: " + request.getMethod());
            }
            throw new AuthMethodNotSupportedException("Authentication method not supported");
        }
        /*boolean b = checkLoginTime();
        if (!b) {
            throw new AuthenticationServiceException("登录错误次数过多, 请稍后重试!");
        }*/
        LoginRequest loginRequest;
        try {
            loginRequest = objectMapper.readValue(request.getReader(), LoginRequest.class);
        } catch (Exception e) {
            throw new AuthenticationServiceException("提交的登录参数错误!");
        }
        /*if (StringUtils.isBlank(loginRequest.getVerifyCode())) {
            throw new AuthenticationServiceException("验证码不能为空!");
        }*/

        if (StringUtils.isBlank(loginRequest.getSerialNo())) {
            if (StringUtils.isBlank(loginRequest.getUsername()) || StringUtils.isBlank(loginRequest.getPassword())) {
                throw new AuthenticationServiceException("账号或密码不能为空!");
            }
        }
        /*String requestId = loginRequest.getRequestId();
        String verifyCode = DataConstants.VERIFY_CODE_MAP.get(requestId);
        if (!loginRequest.getVerifyCode().equalsIgnoreCase(verifyCode)) {
            DataConstants.VERIFY_CODE_MAP.remove(requestId);
            throw new AuthenticationServiceException("验证码错误!");
        }
        DataConstants.VERIFY_CODE_MAP.remove(requestId);*/

        String serialNo = loginRequest.getSerialNo();
        if (StringUtils.isNotBlank(serialNo)) {
            try {
                serialNo = new StringBuffer(serialNo).reverse().toString();
                Base64.Decoder decoder = Base64.getDecoder();
                serialNo = new String(decoder.decode(serialNo));
                log.info("ID卡号 [{}]", serialNo);
            } catch (Exception e) {
                throw new AuthenticationServiceException("IC卡号错误!");
            }

            UserPrincipal principal = new UserPrincipal(UserPrincipal.Type.SERIAL_NO, serialNo);

            UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(principal, serialNo);

            return this.getAuthenticationManager().authenticate(token);
        } else {
            // 账号密码解密
            String username = null;
            String password = null;
            String usernameStr = loginRequest.getUsername();
            String passwordStr = loginRequest.getPassword();
            try {
                log.info("解密前账号:[{}]", usernameStr);
                log.info("解密前密码:[{}]", passwordStr);

                /*Base64.Decoder decoder = Base64.getDecoder();
                String passwordDecode = new StringBuffer(passwordStr).reverse().toString();
                password = new String(decoder.decode(passwordDecode));
                if (!usernameStr.contains("@")) {
                    String usernameDecode = new StringBuffer(usernameStr).reverse().toString();
                    username = new String(decoder.decode(usernameDecode));
                    log.info("解密后账号:[{}]", username);
                } else {
                    username = usernameStr;
                }*/

                password = Aes.aesDecrypt(passwordStr);
                if (!usernameStr.contains("@")) {
                    username = Aes.aesDecrypt(usernameStr);
                    log.info("解密后账号:[{}]", username);
                } else {
                    username = usernameStr;
                }
             //   log.info("解密后密码:[{}]", password);
            } catch (Exception e) {
                password = passwordStr;
//                throw new AuthenticationServiceException("账号或密码错误!");
            }

            /*try {
                if (!username.contains("@")) {
                    // 使用base64倒序解密
                    username = new String(Base64.getDecoder().decode(StringUtils.reverse(username)));
                    password = new String(Base64.getDecoder().decode(StringUtils.reverse(password)));
                }
            } catch (Exception e) {
                
                throw new AuthenticationServiceException("账号或密码错误!");
            }*/

            UserPrincipal principal = new UserPrincipal(UserPrincipal.Type.USER_NAME, username);

            UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(principal, password);

            // 登录营收
            /*HttpPost httpPost = new HttpPost(YingShouLoginUrl + "username=" + username + "&password=" + password);
            CloseableHttpResponse execute = httpClient.execute(httpPost);
            Header[] allHeaders = execute.getAllHeaders();
            for (Header allHeader : allHeaders) {
                response.setHeader(allHeader.getName(), allHeader.getValue());
            }*/

            // TODO GIS登录

            return this.getAuthenticationManager().authenticate(token);
        }


    }

    private boolean checkLoginTime() {
        //获取request
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        //获取IP地址
        String ipAddr = IPUtils.getIpAddr(request);
        if (StringUtils.isBlank(ipAddr)) {
            return true;
        }

        if (DataConstants.LOGIN_TIME_CHECK.containsKey(ipAddr)) {
            Integer integer = DataConstants.LOGIN_TIME_CHECK.get(ipAddr);
            if (integer < 10) {
                return true;
            }
        } else {
            return true;
        }
        // 冻结账号


        return false;
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response, FilterChain chain,
                                            Authentication authResult) throws IOException, ServletException {
        saveLog(authResult, IPUtils.getIpAddr(request));

        successHandler.onAuthenticationSuccess(request, response, authResult);
    }

    /**
     * 保存登陆日志
     * @param authResult
     * @param ipAddr
     */
    private void saveLog(Authentication authResult, String ipAddr) {
        User user = (User) authResult.getPrincipal();
        OptionLog log = new OptionLog();
        log.setUserId(user.getId());
        log.setTenantId(user.getTenantId());
        log.setAuthority(user.getAuthority().toString());
        log.setFirstName(user.getFirstName() + " (" + user.getSearchText() + ")");
        log.setOptions(OptionType.LOGIN.name());
        log.setInfo(OptionType.LOGIN.getDescription());
        log.setCreateTime(System.currentTimeMillis());
        log.setType("2");// 登录日志
        JSONObject additionalInfo = new JSONObject();
        additionalInfo.put("ip", ipAddr);
        log.setAdditionalInfo(JacksonUtil.toJsonNode(additionalInfo));

        optionLogService.save(log);
    }

    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                              AuthenticationException failed) throws IOException, ServletException {
        SecurityContextHolder.clearContext();
        failureHandler.onAuthenticationFailure(request, response, failed);
    }
}
