import{_ as L}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as Y}from"./CardTable-rdWOL4_6.js";import{_ as v}from"./CardSearch-CB_HNR-Q.js";import{z as u,C as M,c as D,r as m,bF as p,b as i,S as T,o as q,g as H,n as A,q as f}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function k(s){return u({url:"/api/base/database/connection/list",method:"get",params:s})}function Q(s){return u({url:"/api/base/database/connection/getDetail",method:"get",params:{id:s}})}function w(s){return u({url:"/api/base/database/connection/add",method:"post",data:s})}function S(s){return u({url:"/api/base/database/connection/edit",method:"post",data:s})}function B(s){return u({url:"/api/base/database/connection/deleteIds",method:"delete",data:s})}const E={class:"wrapper"},F={__name:"databaseConnection",setup(s){const g=D(),d=D(),x=(e,a,t)=>{if(!a){t(new Error("请输入端口号"));return}const r=Number(a);isNaN(r)||r<1||r>65535?t(new Error("端口号必须为1-65535之间的数字")):t()},C=m({labelWidth:"100px",filters:[{type:"input",label:"方案名称",field:"name",placeholder:"请输入方案名称",onChange:()=>c()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>c()},{perm:!0,type:"primary",text:"新增",click:()=>y()},{perm:!0,type:"danger",text:"批量删除",click:()=>_()}]}],defaultParams:{}}),l=m({columns:[{label:"主键ID",prop:"id"},{label:"方案名称",prop:"name"},{label:"数据库类型",prop:"dbType"},{label:"服务器地址",prop:"dbHost"},{label:"端口号",prop:"dbPort"},{label:"创建时间",prop:"createdAt",render:e=>p(e.createdAt).format("YYYY-MM-DD HH:mm")},{label:"更新时间",prop:"updatedAt",render:e=>p(e.updatedAt).format("YYYY-MM-DD HH:mm")}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>P(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>y(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>_(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{l.pagination.page=e,c()},handleSize:e=>{l.pagination.limit=e,c()}},handleSelectChange:e=>{l.selectList=e||[]}}),o=m({title:"新增数据库连接",group:[{fields:[{type:"input",label:"方案名称",field:"name",rules:[{required:!0,message:"请输入方案名称"}]},{type:"select",label:"数据库类型",field:"dbType",options:[{label:"MySQL",value:"MySQL"},{label:"Oracle",value:"Oracle"},{label:"PostgreSQL",value:"PostgreSQL"},{label:"SQL Server",value:"SQL Server"}],rules:[{required:!0,message:"请选择数据库类型"}]},{type:"input",label:"服务器地址",field:"dbHost",rules:[{required:!0,message:"请输入服务器地址"}]},{type:"input",label:"端口号",field:"dbPort",rules:[{required:!0,message:"请输入端口号"},{validator:x,trigger:"blur"}]},{type:"input",label:"数据库名称",field:"dbName",rules:[{required:!0,message:"请输入数据库名称"}]},{type:"input",label:"用户名",field:"dbUser",rules:[{required:!0,message:"请输入用户名"}]},{type:"password",label:"密码",field:"dbPwd",rules:[{required:!0,message:"请输入密码"}],showPassword:!0},{type:"textarea",label:"初始化SQL脚本",field:"initScriptDb",placeholder:"请输入数据库初始化SQL脚本",inputStyle:{height:"100px"}},{type:"textarea",label:"方案描述",field:"description",placeholder:"请输入方案描述"}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var a;try{console.log("提交参数（加密前）:",e);const t={...e,dbPwd:btoa(e.dbPwd)};console.log("提交参数（加密后）:",t),e.id?(await S(t),i.success("修改成功")):(await w(t),i.success("新增成功")),(a=d.value)==null||a.closeDialog(),c()}catch{i.error("操作失败")}}}),h=()=>{o.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),o.showSubmit=!0,o.showCancel=!0,o.cancelText="取消",o.submitText="确定",o.submit=async e=>{var a;try{e.id?(await S(e),i.success("修改成功")):(await w(e),i.success("新增成功")),(a=d.value)==null||a.closeDialog(),c()}catch{i.error("操作失败")}},o.footerBtns=void 0},y=e=>{var a;h(),o.title=e?"编辑数据库连接":"新增数据库连接",o.defaultValue={...e||{}},(a=d.value)==null||a.openDialog()},P=async e=>{var a,t;try{const r=await Q(e.id);let n=((a=r.data)==null?void 0:a.data)||r.data||r;n.dbPwd&&(n={...n,dbPwd:atob(n.dbPwd)}),h(),o.title="数据库连接详情",o.defaultValue={...n},o.group[0].fields.forEach(b=>{b.disabled=!0,b.type==="password"&&(b.showPassword=!1)}),o.showSubmit=!1,o.cancelText="关闭",(t=d.value)==null||t.openDialog()}catch{i.error("获取详情失败")}},_=e=>{T("确定删除？","删除提示").then(async()=>{var t;const a=e?[e.id]:((t=l.selectList)==null?void 0:t.map(r=>r.id))||[];if(!a.length){i.warning("请选择要删除的数据");return}await B(a),i.success("删除成功"),c()}).catch(()=>{})},c=async()=>{var e,a;try{const t=await k({page:l.pagination.page,size:l.pagination.limit,...((e=g.value)==null?void 0:e.queryParams)||{}}),r=((a=t.data)==null?void 0:a.data)||t.data||t;l.dataList=r.records||r,l.pagination.total=r.total||r.length||0,l.dataList.forEach(n=>{n.createdAt=p(n.createdAt).format("YYYY-MM-DD HH:mm"),n.updatedAt=p(n.updatedAt).format("YYYY-MM-DD HH:mm")})}catch{i.error("数据加载失败")}};return q(()=>{c()}),(e,a)=>{const t=v,r=Y,n=L;return H(),A("div",E,[f(t,{ref_key:"refSearch",ref:g,config:C},null,8,["config"]),f(r,{class:"card-table",config:l},null,8,["config"]),f(n,{ref_key:"refDialogForm",ref:d,config:o},null,8,["config"])])}}},j=M(F,[["__scopeId","data-v-a73a8245"]]);export{j as default};
