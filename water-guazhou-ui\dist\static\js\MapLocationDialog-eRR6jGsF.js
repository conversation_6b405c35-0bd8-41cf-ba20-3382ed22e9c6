import{d as F,c as w,am as k,bB as $,b as p,Q as T,g as l,h as b,F as i,p as s,q as r,G as V,i as g,eM as x,an as h,bh as N,n as u,eN as z,eO as I,cE as S,J as G,L as U,C as q}from"./index-r0dFAfgr.js";import{er as J,es as O,y as Q,bI as j,g as A}from"./MapView-DaoQedLH.js";import{w as H}from"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";const K={class:"map-location-container"},P={class:"address-info"},R={class:"info-item"},W={class:"value"},X={key:0,class:"info-item"},Y={class:"value"},Z={class:"map-container"},oo={key:0,class:"no-location"},eo={key:1,class:"map-view"},so={key:0,class:"map-loading"},to={key:1,id:"mapViewDiv",class:"map-container-inner"},ao={class:"dialog-footer"},no=F({__name:"MapLocationDialog",props:{modelValue:{type:Boolean},address:{},coordinates:{}},emits:["update:modelValue"],setup(B,{emit:C}){const n=B,D=C,c=w(!1),d=w(!1);let t=null,f=null;k(()=>n.modelValue,e=>{c.value=e,e&&n.coordinates&&$(()=>{L()})}),k(()=>c.value,e=>{D("update:modelValue",e)});const L=async()=>{if(n.coordinates){d.value=!0;try{const e=n.coordinates.split(",");if(e.length!==2){p.error("坐标格式错误"),d.value=!1;return}const o=parseFloat(e[0]),a=parseFloat(e[1]);if(isNaN(o)||isNaN(a)){p.error("坐标数据无效"),d.value=!1;return}const m=new J({basemap:"streets-navigation-vector"});t=new O({container:"mapViewDiv",map:m,center:[o,a],zoom:15}),await t.when();const _=new H({longitude:o,latitude:a}),v=new Q({color:[226,119,40],outline:{color:[255,255,255],width:2},size:12}),E=new j({title:"事件位置",content:`
        <div>
          <p><strong>地址：</strong>${n.address}</p>
          <p><strong>经度：</strong>${o}</p>
          <p><strong>纬度：</strong>${a}</p>
        </div>
      `});f=new A({geometry:_,symbol:v,popupTemplate:E}),t.graphics.add(f),d.value=!1}catch(e){console.error("地图初始化失败:",e),p.error("地图加载失败，请稍后重试"),d.value=!1}}},M=()=>{if(!(!n.coordinates||!t))try{const e=n.coordinates.split(","),o=parseFloat(e[0]),a=parseFloat(e[1]);t.goTo({center:[o,a],zoom:15}),p.success("已重新定位到事件位置")}catch(e){console.error("重新定位失败:",e),p.error("重新定位失败")}},y=()=>{c.value=!1,t&&(t.destroy(),t=null,f=null)};return T(()=>{t&&t.destroy()}),(e,o)=>{const a=S,m=G,_=U;return l(),b(_,{modelValue:c.value,"onUpdate:modelValue":o[0]||(o[0]=v=>c.value=v),title:"事件地址定位",width:"900px","before-close":y,class:"map-location-dialog"},{footer:i(()=>[s("span",ao,[r(m,{onClick:y},{default:i(()=>o[6]||(o[6]=[V("关闭")])),_:1}),e.coordinates?(l(),b(m,{key:0,type:"primary",onClick:M},{default:i(()=>[r(a,null,{default:i(()=>[r(g(x))]),_:1}),o[7]||(o[7]=V(" 重新定位 "))]),_:1})):h("",!0)])]),default:i(()=>[s("div",K,[s("div",P,[s("div",R,[o[1]||(o[1]=s("span",{class:"label"},"地址：",-1)),s("span",W,N(e.address||"暂无地址信息"),1)]),e.coordinates?(l(),u("div",X,[o[2]||(o[2]=s("span",{class:"label"},"坐标：",-1)),s("span",Y,N(e.coordinates),1)])):h("",!0)]),s("div",Z,[e.coordinates?(l(),u("div",eo,[d.value?(l(),u("div",so,[r(a,{class:"is-loading"},{default:i(()=>[r(g(I))]),_:1}),o[5]||(o[5]=s("span",null,"地图加载中...",-1))])):(l(),u("div",to))])):(l(),u("div",oo,[r(a,{class:"location-icon"},{default:i(()=>[r(g(z))]),_:1}),o[3]||(o[3]=s("p",{class:"no-location-text"},"暂无位置信息",-1)),o[4]||(o[4]=s("p",{class:"no-location-desc"},"该事件未设置地理坐标，无法在地图上显示位置",-1))]))])])]),_:1},8,["modelValue"])}}}),uo=q(no,[["__scopeId","data-v-84f4931b"]]);export{uo as default};
