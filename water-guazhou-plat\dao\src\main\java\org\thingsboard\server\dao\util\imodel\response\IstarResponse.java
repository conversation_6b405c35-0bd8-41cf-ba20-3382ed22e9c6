package org.thingsboard.server.dao.util.imodel.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;

import java.io.Serializable;

/**
 * 水务返回对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IstarResponse implements Responsible, Serializable {

    private int code;

    private String message;

    private Object data;

    @Override
    public Object postProcess(ReturnHelper returnHelper, Object arg) {
        data = returnHelper.process(data, arg);
        return this;
    }


    public static IstarResponse build(int code, String message, Object data) {
        return new IstarResponse(code, message, data);
    }

    public static IstarResponse ok(String message, Object data) {
        return new IstarResponse(200, message, data);
    }

    public static IstarResponse ok(Object data) {
        return new IstarResponse(200, "操作成功!", data);
    }

    public static IstarResponse ok() {
        return new IstarResponse(200, "操作成功!", null);
    }

    public static IstarResponse error(String message) {
        return new IstarResponse(500, message, null);
    }

    public static IstarResponse error() {
        return new IstarResponse(500, "操作失败!", null);
    }

    public static IstarResponse falseError() {
        return new IstarResponse(500, "操作失败!", false);
    }

}
