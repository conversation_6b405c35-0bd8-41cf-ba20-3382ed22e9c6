package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrange;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class GuardArrangePageRequest extends AdvancedPageableQueryEntity<GuardArrange, GuardArrangePageRequest> {
    // 地点id
    private String placeId;

}
