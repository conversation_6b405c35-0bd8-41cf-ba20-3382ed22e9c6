<template>
  <el-scrollbar>
    <div class="main">
      <div class="wrapper">
        <div class="left">
          <SLCard
            title=""
            style="height: 130px; border-radius: 12px"
            :class="{ isDark: appStore.isDark }"
          >
            <div class="date-bg">
              <div style="display: flex">
                <el-avatar
                  :size="80"
                  src="@/assets/images/user-log.png"
                  icon="Avatar"
                />
                <div class="userInfo">
                  <div class="userName">
                    {{ userInfo.lastName }}，{{ state.hour }}
                  </div>
                  <div class="weather">
                    {{ dayjs(new Date()).format('YYYY年MM月DD日') }}
                    {{ weather.week }}
                    <el-icon :size="14" style="margin-left: 22px">
                      <Location />
                    </el-icon>
                    {{ weather.city }} {{ weather.weather }}，{{
                      weather.temperature
                    }}℃
                  </div>
                </div>
              </div>
            </div>
          </SLCard>
          <SLCard
            title=" "
            style="height: 386px; margin-top: 15px; border-radius: 12px"
          >
            <template #title>
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  flex: 1;
                  align-items: center;
                "
              >
                <div
                  :style="{ color: appStore.isDark ? '#fff' : '' }"
                  style="padding-left: 10px"
                >
                  日程安排
                </div>
                <div style="font-weight: 600; font-size: 16px">
                  {{ state.singleContract }}
                </div>
                <div>
                  <el-icon
                    :size="20"
                    style="cursor: pointer"
                    @click="addSchedule"
                  >
                    <Plus />
                  </el-icon>
                </div>
              </div>
            </template>
            <div class="date-weather">
              <el-icon style="cursor: pointer" @click="previousWeek">
                <ArrowLeftBold />
              </el-icon>
              <div
                v-for="(date, index) in weekDate"
                :key="index"
                class="week-style"
              >
                <div>
                  <span>{{ weekNames[date.week] }}</span>
                </div>
                <div v-if="currentDay === date.time" class="today">
                  今
                  <div v-if="false" class="dot"></div>
                </div>
                <div
                  v-else
                  :class="currentChooseDay === date.time ? 'today' : ''"
                  @click="changeDay(date.time)"
                >
                  {{ date.day }}
                  <div v-if="false" class="dot"></div>
                </div>
              </div>
              <el-icon style="cursor: pointer" @click="nextWeek">
                <ArrowRightBold />
              </el-icon>
            </div>
            <div class="list-con">
              <div v-if="scheduleLost.length > 0">
                <div
                  v-for="(index, i) in scheduleLost"
                  :key="i"
                  class="list-card"
                  :style="
                    appStore.isDark
                      ? { background: index % 2 === 0 ? '#222536' : '#313748' }
                      : { background: index % 2 === 0 ? '#F8F9FF' : '#F3F9FF' }
                  "
                >
                  <div class="schedule-title">
                    <div class="name">
                      {{ index.title }}
                    </div>
                    <div class="desc">
                      {{ index.content }}
                    </div>
                  </div>
                  <div class="schedule-num">日程{{ i + 1 }}</div>
                </div>
              </div>
              <div v-else style="text-align: center; margin-top: 7%">
                <img
                  src="~@/assets/images/home/<USER>"
                  style="width: 191px; height: 138px"
                />
              </div>
            </div>
          </SLCard>
          <SLCard title=" " class="duty">
            <template #title>
              <div style="width: 100%; color: #000; padding-left: 10px">
                <div :style="{ color: appStore.isDark ? '#fff' : '' }">
                  值班中心
                </div>
              </div>
            </template>
            <div class="duty-info" style="">
              <div
                class="d-bg"
                style="
                  padding: 20px;
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                "
              >
                <div
                  class="info"
                  style="display: flex; justify-content: space-between"
                >
                  <div class="inf0-1" style="width: 45%; line-height: 24px">
                    <div class="dut-t">值班地点</div>
                    <div style="color: #ffffff; font-size: 14px">
                      {{ state.CurrentGuard.placeName }}
                    </div>
                  </div>

                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      width: 55%;
                    "
                  >
                    <div class="inf0-2" style="line-height: 24px">
                      <div class="dut-t">值班班次</div>
                      <div style="color: #ffffff; font-size: 14px">
                        {{ state.CurrentGuard.className }}（{{
                          state.CurrentGuard.beginTime
                        }}
                        - {{ state.CurrentGuard.endTime }} ）
                      </div>
                    </div>
                    <div class="but" style="cursor: pointer">值班签到</div>
                  </div>
                </div>
                <div class="inf01">
                  <span class="dut-t"> 值班班长：</span>
                  <el-tag
                    effect="plain"
                    class="ml-2"
                    color="#549ce1"
                    style="color: #ffffff"
                    hit
                  >
                    {{ state.CurrentGuard.headName }}
                  </el-tag>
                </div>
                <div class="info2">
                  <span class="dut-t">值班地点：</span>
                  <el-tag
                    v-for="(item, index) in state.partnerNames"
                    :key="index"
                    effect="plain"
                    class="ml-2"
                    color="#549ce1"
                    style="color: #ffffff; margin-right: 8px"
                    hit
                  >
                    {{ item }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="duty-event">
              <div class="duty-event-title" @click="addConent">
                <img
                  src="../../assets/images/home/<USER>"
                  style="width: 40px; height: 40px"
                />
                <div class="text">事件记录</div>
              </div>
              <div class="duty-event-title">
                <img
                  src="../../assets/images/home/<USER>"
                  style="width: 40px; height: 40px"
                />
                <div class="text">交接班登记</div>
              </div>
            </div>
          </SLCard>
        </div>
        <div class="right">
          <SLCard
            title=" "
            style="
              height: 287px;
              border-radius: 12px;
              padding-left: 20px;
              padding-right: 20px;
            "
          >
            <template #title>
              <div
                class="right-bottom-title"
                style="
                  width: 100%;
                  display: flex;
                  justify-content: space-between;
                "
              >
                <div :style="{ color: appStore.isDark ? '#fff' : '' }">
                  常用功能
                </div>
                <div
                  class="more-arrow"
                  @click="
                    () => {
                      cataloge.visible = !cataloge.visible;
                    }
                  "
                >
                  <el-icon :size="20" style="cursor: pointer">
                    <Operation />
                  </el-icon>
                </div>
              </div>
            </template>
            <!-- <template #right>
          <el-icon :size="20" style="cursor: pointer;" @click="() => { cataloge.visible = !cataloge.visible }">
            <Operation />
          </el-icon>
        </template> -->
            <div class="app-content" style="overflow-y: auto">
              <template
                v-for="(item, index) in state.shortcutMenu"
                :key="index"
              >
                <div class="lattice" @click="gotoRouter(item)">
                  <div class="images">
                    <div class="image">
                      <img
                        src="~@/assets/images/home/<USER>"
                        style="width: 36px; height: 36px"
                      />
                    </div>
                    <div>
                      <span
                        class="m-title"
                        style="font-size: 14px; margin-top: 5px"
                        >{{ item.label }}</span
                      >
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </SLCard>
          <div class="right-bottom">
            <SLCard title=" " class="notice-card" style="border-radius: 12px">
              <template #title>
                <div
                  class="right-bottom-title"
                  style="
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                  "
                >
                  <div :style="{ color: appStore.isDark ? '#fff' : '' }">
                    通知公告
                  </div>
                  <div class="more-arrow" @click="showMoreNotices">
                    查看全部<el-icon :size="16">
                      <ArrowRight />
                    </el-icon>
                  </div>
                </div>
              </template>
              <div class="content">
                <ul
                  v-if="noticesList.lenght > 0"
                  style="padding: 0; height: 140px"
                >
                  <li
                    v-for="(item, index) in noticesList"
                    :key="index"
                    class="list"
                  >
                    <div>
                      {{ item.title }}
                    </div>
                    <div class="color-grey">
                      {{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}
                    </div>
                  </li>
                </ul>
                <div v-else style="text-align: center; margin-top: 10%">
                  <img
                    src="~@/assets/images/home/<USER>"
                    style="width: 150px; height: 108px"
                  />
                </div>
              </div>
            </SLCard>
            <SLCard title=" " class="alarm-card" style="border-radius: 12px">
              <template #title>
                <div
                  class="right-bottom-title"
                  style="
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                  "
                >
                  <div :style="{ color: appStore.isDark ? '#fff' : '' }">
                    报警列表
                  </div>
                  <div class="more-arrow" @click="toMoreAlarm">
                    查看全部<el-icon :size="16" color="#A5AECD">
                      <ArrowRight />
                    </el-icon>
                  </div>
                </div>
              </template>
              <div class="content">
                <ul style="padding: 0; overflow: auto; height: 140px">
                  <li
                    v-for="(item, index) in state.alarmData"
                    :key="index"
                    class="list"
                  >
                    <div>{{ item.statusType }} {{ item.remark }}</div>
                    <div class="color-grey">
                      {{ formatTime(item.createdTime) }}
                    </div>
                  </li>
                </ul>
              </div>
            </SLCard>
          </div>
          <div class="right-content">
            <SLCard
              title=" "
              class="book-card"
              style="border-radius: 12px; height: 184px"
            >
              <template #title>
                <div class="right-bottom-title" style="padding-left: 10px">
                  <div :style="{ color: appStore.isDark ? '#fff' : '' }">
                    单位通讯录
                  </div>
                  <div class="more-arrow" @click="showMoreNotices">
                    查看全部<el-icon :size="16">
                      <ArrowRight />
                    </el-icon>
                  </div>
                </div>
              </template>
              <div class="content">
                <div class="search" style="">
                  <div style="padding-top: 30px; display: flex; width: 100%">
                    <el-select
                      v-model="userVal"
                      filterable
                      remote
                      size="large"
                      reserve-keyword
                      value-key="id"
                      placeholder="输入用户姓名"
                      :remote-method="remoteMethod"
                      :loading="state.loading"
                      style="width: 90%; height: 56px"
                      @change="selectUser"
                    >
                      <el-option
                        v-for="(item, index) in options"
                        :key="index"
                        :label="item.firstName"
                        :value="item.id?.id"
                      />
                    </el-select>
                    <div>
                      <el-button
                        type="primary"
                        size="large"
                        class="but"
                        @click="searchUserInfo"
                      >
                        查询
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </SLCard>
          </div>
        </div>
      </div>

      <SLDrawer ref="refForm_Add" :config="drawerConfig"> </SLDrawer>
      <DialogForm ref="refForm" :config="dialogFormConfig">
        <el-calendar v-model="today" />
        <Form ref="refFormConfig" :config="formConfig"> </Form>
      </DialogForm>
      <DialogForm ref="refUserForm" :config="dialogUserFormConfig">
      </DialogForm>
      <Cataloge
        v-if="cataloge.visible"
        :table-config="cataloge"
        @refresh="init"
      />
      <DialogForm ref="refEventForm" :config="eventFormConfig"> </DialogForm>
    </div>
  </el-scrollbar>
</template>
<script lang="ts" setup>
import { ElMessage, dayjs } from 'element-plus';
import {
  Operation,
  ArrowRight,
  Plus,
  ArrowRightBold,
  ArrowLeftBold,
  Location
  // Edit, Delete
} from '@element-plus/icons-vue';
import { ref } from 'vue';
import axios from 'axios';
import { GetMenuTree } from '@/api/menu/source';
import { formatTree } from '@/utils/GlobalHelper';
import { useAppStore, useBusinessStore, useUserStore } from '@/store';
import { removeSlash } from '@/utils/removeIdSlash';
import { getapplications } from '@/api/portal';
import { getAlarmRealTimeList } from '@/api/alarm';
import { formatDate } from '@/utils/DateFormatter';

import Cataloge from './components/rolemenu.vue';
import router from '@/router';
import { getAllUser } from '@/api/operatingCharges/businessInstallation';
import {
  knowledgeNoticeList,
  postSchedule,
  getAgendaList
} from '@/api/CTI/knowledgeBase';
import useGlobal from '@/hooks/global/useGlobal';
import { refreshAllRoutes } from '@/utils/RouterHelper';
import {
  getDutyLogs,
  getGuardArrange,
  getCurrentGuard,
  saveGuardEventRecord
} from '@/api/productionScheduling/dutyManage';

const userStore = useUserStore();
const appStore = useAppStore();
const { $messageWarning, $messageSuccess, $messageError } = useGlobal();
const weekNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
const today = ref(new Date());
const userInfo = ref<any>({});
const refForm = ref<IDialogFormIns>();
const refEventForm = ref<IDialogFormIns>();
const refUserForm = ref<IDialogFormIns>();
const weekDate = ref<any>([]);
const scheduleLost = ref<any>([]);
const noticesList = ref<any>([]);
const refForm_Add = ref<ISLDrawerIns>();

const options = ref<any>([]);
const userList = ref<any>([]);
const weather = ref<any>({});
const userVal = ref<any>('');
const currentDay = ref<any>(dayjs().format('YYYY-MM-DD'));
const currentChooseDay = ref<any>(dayjs().format('YYYY-MM-DD'));

const refFormConfig = ref<IFormIns>();

const getWeekDate = () => {
  weekDate.value = [];
  for (let i = 1; i <= 7; i++) {
    weekDate.value.push({
      week: dayjs()
        .startOf('week')
        .add(i - state.weekNum, 'day')
        .day(),
      day: dayjs()
        .startOf('week')
        .add(i - state.weekNum, 'day')
        .format('DD'),
      time: dayjs()
        .startOf('week')
        .add(i - state.weekNum, 'day')
        .format('YYYY-MM-DD')
    });
  }
  state.singleContract = dayjs(weekDate.value[6].time).format('YYYY-MM');
};
const state = reactive<{
  apps: any[];
  menus: NormalOption[];
  drawerTitle: string;
  hour: string;
  todayTime: string;
  alarmData: any;
  loading: boolean;
  weekNum: number;
  shortcutMenu: any[];
  singleContract: string;
  CurrentGuard: any;
  partnerNames: any[];
}>({
  apps: [],
  menus: [],
  drawerTitle: '添加',
  todayTime: '添加',
  hour: '',
  alarmData: [],
  loading: false,
  weekNum: 0,
  shortcutMenu: [],
  singleContract: dayjs(new Date()).format('YYYY年MM月'),
  CurrentGuard: {},
  partnerNames: []
});

const cataloge = reactive<{
  visible: boolean;
  roleId: string;
  close: () => any;
}>({
  visible: false,
  roleId: '',
  close: () => (cataloge.visible = false)
});
// const btnPermDialog = reactive<{
//   roleId: string
//   curRole: any
// }>({
//   roleId: '',
//   curRole: {}
// })

const eventFormConfig = reactive<IDialogFormConfig>({
  title: '事件记录',
  dialogWidth: '900px',
  labelWidth: '100px',
  group: [
    {
      fields: [
        {
          label: '事件内容',
          field: 'content',
          type: 'textarea',
          rules: [
            {
              required: true,
              message: '请输入事件内容',
              trigger: 'blur'
            }
          ]
        }
      ]
    }
  ],
  submit: (param) => {
    saveGuardEventRecord({
      ...param,
      recordId: state.CurrentGuard.id
    })
      .then((res) => {
        if (res.data.code === 200) {
          $messageSuccess('添加成功');
        } else {
          $messageError('添加失败');
        }
      })
      .catch((err) => {
        $messageError(err);
      });
  }
});

const dialogFormConfig = reactive<IDialogFormConfig>({
  title: '新增日程安排',
  dialogWidth: '900px',
  labelWidth: '100px',
  group: [],
  submit: () => {
    refFormConfig.value?.Submit();
  }
});

// const classConfig = reactive<ICardTable>({
//   indexVisible: true,
//   columns: [
//     { label: '值班部门', prop: 'userDepartmentName' },
//     { label: '值班类型', prop: 'className' },
//     { label: '值班人员', prop: 'name' },
//     { label: '交班时间', prop: 'startTime', formatter: (row: any, val: any) => { return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '' } },
//     { label: '交班内容', prop: 'endTime' }
//   ],
//   operations: [
//     {
//       perm: true,
//       text: '编辑',
//       svgIcon: shallowRef(Edit),
//       click: row => {
//         FormConfig.title = '编辑'
//         handleEdit(row)
//       }
//     },
//     {
//       type: 'danger',
//       perm: true,
//       text: '删除',
//       svgIcon: shallowRef(Delete),
//       click: row => handleDelete(row)
//     }
//   ],
//   dataList: [],
//   pagination: {
//     refreshData: ({ page, size }) => {
//       TableConfig.pagination.limit = size
//       TableConfig.pagination.page = page
//       refreshData()
//     }
//   }
// })

// 通讯录详情
const dialogUserFormConfig = reactive<IDialogFormConfig>({
  title: '通讯录详情',
  group: [
    {
      fields: [
        {
          xs: 12,
          field: 'departmentName',
          type: 'text',
          readonly: true,
          labelWidth: '120px',
          label: '所属部门名称：'
        },
        {
          xs: 12,
          field: 'firstName',
          type: 'text',
          readonly: true,
          label: '姓名：'
        },
        {
          xs: 12,
          field: 'phone',
          type: 'text',
          readonly: true,
          label: '联系方式：'
        },
        {
          xs: 12,
          field: 'email',
          type: 'text',
          readonly: true,
          label: '邮箱地址：'
        }
      ]
    }
  ]
});

const formConfig = reactive<IFormConfig>({
  labelPosition: 'left',
  group: [
    {
      fields: [
        {
          field: 'title',
          type: 'input',
          label: '标题',
          rules: [{ required: true, message: '请输入标题' }]
        },
        {
          field: 'content',
          type: 'textarea',
          label: '行程内容',
          rules: [{ required: true, message: '请输入行程内容' }]
        }
      ]
    }
  ],
  submit(params) {
    params['time'] = today.value;
    postSchedule(params)
      .then((res) => {
        if (res.data.code === 200) {
          ElMessage.success('保存成功');
          refForm.value?.closeDialog();
        } else {
          ElMessage.warning('保存失败');
        }
      })
      .catch((error) => {
        ElMessage.warning(error);
      });
  }
});

const addSchedule = () => {
  refForm.value?.openDialog();
};
const addConent = () => {
  eventFormConfig.defaultValue = {};
  refEventForm.value?.openDialog();
};

// 查询指定日期的日程列表
const changeDay = (day: any) => {
  currentChooseDay.value = day;
  getAgendaList({ beginTime: day, endTime: day }).then((res) => {
    scheduleLost.value = res.data.data || [];
  });
};

// 格式化时间
const formatTime = (val: string) => {
  return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '';
};

// 查看上一周 日期
const previousWeek = () => {
  state.weekNum += 7;
  getWeekDate();
};

// 查看下一周 日期
const nextWeek = () => {
  state.weekNum -= 7;
  getWeekDate();
};

// 在高德地图jsapi获取浏览器定位  当前为精准定位
const getLocation = () => {
  axios
    .get('https://restapi.amap.com/v3/ip', {
      params: {
        key: '2ef4bde4346f3aef3799e28c465711e4'
      }
    })
    .then((res) => {
      // 获取天气信息
      axios
        .get('https://restapi.amap.com/v3/weather/weatherInfo?parameters', {
          params: {
            key: '2ef4bde4346f3aef3799e28c465711e4',
            city: res.data.adcode
          }
        })
        .then((res) => {
          weather.value = res.data.lives[0] ?? {};
        });
    });

  // AMap.plugin('AMap.Geolocation', () => {
  //   const geolocation = new AMap.Geolocation({
  //     // 是否使用高精度定位，默认：true
  //     enableHighAccuracy: true,
  //     // 设置定位超时时间，默认：无穷大
  //     timeout: 10000
  //   });
  //   // 获取用户当前的精确位置
  //   geolocation.getCurrentPosition((status: string, result: any) => {
  //     if (status === 'complete') {
  //       // 只能获取当前用户所在城市和城市的经纬度
  //       geolocation.getCityInfo((status, result) => {
  //         if (status === 'complete') {
  //           console.log(result.city);
  //           const nCity = result.city?.substring(
  //             0,
  //             result.city?.lastIndexOf('市')
  //           );
  //           axios
  //             .get(
  //               'https://v0.yiketianqi.com/api?unescape=1&version=v61&appid=33818939&appsecret=vNc1DmDc&city=' +
  //                 nCity
  //             )
  //             .then((res: any) => {
  //               weather.value = res.data;
  //             });
  //         } else {
  //           console.log(result);
  //         }
  //       });
  //     }
  //   });
  // });
};
// 搜索通讯录用户
const remoteMethod = async (query: string) => {
  if (query) {
    state.loading = true;
    const res = await getAllUser({ name: query });
    options.value = res.data?.data;
    userList.value = res.data?.data;
    console.log(options.value);
    state.loading = false;
  } else {
    options.value = [];
  }
};
// 获取告警列表
const refreshAlarmData = async () => {
  const res = await getAlarmRealTimeList(
    {
      start: moment().subtract(1, 'year').startOf('day').valueOf(),
      end: moment().valueOf(),
      page: 1,
      size: 20
    },
    useBusinessStore().navSelectedRange?.data?.id
  );

  state.alarmData = res.data?.data?.map((item) => {
    const record = item.details.record || [];
    let statusType = '';
    switch (item.type) {
      case 'scope':
        statusType = '【范围告警】';
        break;
      case 'change':
        statusType = '【变动告警】';
        break;
      case 'maintain':
        statusType = '【维持告警】';
        break;
      case 'offline':
        statusType = '【掉线报警】';
        break;
      default:
        break;
    }
    return {
      createdTime: item.createdTime,
      type: record[0]?.info,
      status: item.type,
      statusType,
      remark: (item.deviceName || '') + ' ' + (record[0]?.info || ''),
      time: formatDate(item.startTs)
    };
  });
};

// 选中查询用户
const selectUser = (value: string) => {
  console.log(value);
};
// 选中查询用户
const searchUserInfo = () => {
  console.log(userVal.value);
  if (userVal.value) {
    const userInfo = userList.value?.find(
      (user) => userVal.value === user.id.id
    );
    console.log(userInfo);
    if (userInfo) {
      dialogUserFormConfig.defaultValue = userInfo;
      refUserForm.value?.openDialog();
    } else {
      $messageWarning('无此用户');
    }
  } else {
    $messageWarning('输入用户姓名');
  }
};

// 查看更多报警信息 TODO
const toMoreAlarm = () => {
  router.push({ name: 'defaultRoute_BJZX' });
};

const getApplication = async () => {
  const tenantId = userStore.user?.tenantId?.id;
  if (!tenantId) return;
  const tenantid = removeSlash(tenantId);
  const res = await getapplications(tenantid, 'ALL');
  state.apps = res.data || [];
};

// 跳转公告路由
const showMoreNotices = () => {
  router.push({ name: 'defaultRoute_TZGG' });
};
// 查看所有的菜单
const showMenu = async () => {
  await getApplication();
  const res = await GetMenuTree();
  state.menus = formatTree(res.data || [], {
    id: 'id',
    value: 'id',
    label: 'label',
    children: 'children'
  });
  refForm_Add.value?.openDrawer();
};
// 菜单列表弹框
const drawerConfig = reactive<IDrawerConfig>({
  title: '功能选择',
  width: '40%',
  group: [
    {
      fields: [
        {
          type: 'tree',
          label: '',
          // style: {
          //   height: '500px'
          // },
          field: 'menuIdList',
          showCheckbox: true,
          options: computed(() => state.menus) as any
        }
      ]
    }
  ],
  onClose: () => {
    // refreshData()
  },
  submit: (params) => {
    console.log('params', params);
  }
});

// 通知公告
const refreshNotices = async () => {
  const params = {
    type: '公告板',
    startTime: dayjs().startOf('week').valueOf(),
    endTime: dayjs().endOf('week').valueOf(),
    size: 20,
    page: 1
  };
  const res = await knowledgeNoticeList(params);
  noticesList.value = res.data.data?.data;
};

// 值班管理
// const refreshGuardArrange = async () => {
//   const params = {
//     fromTime: dayjs().startOf('day').valueOf(),
//     toTime: dayjs().endOf('day').valueOf()
//   }
//   const res = await getDutyLogs(params)
//   const today = dayjs().format('YYYY-MM-DD')
//   const ids = res.data.data[today]?.map(d => { return d.id })
//   const res1 = await guardArrangePartner({
//     arrangeId: ids.join(','),
//     beginTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
//     endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
//   })

//   guardArrange.value = res1.data.data?.data
// }

const currentGuard = async () => {
  const res = await getCurrentGuard();
  state.CurrentGuard = res.data.data || {};
  const partnerNames = state.CurrentGuard.partnerNames;
  state.partnerNames = partnerNames?.split(',') || [];
};

onBeforeMount(() => {
  getApplication();
  getWeekDate();
  userInfo.value = userStore.user;
  const hour = parseInt(dayjs(today.value).format('HH'));
  state.hour = hour < 13 ? '上午好' : hour < 19 ? '下午好' : '晚上好';
  console.log('时间', state.hour);
  getLocation();
  refreshAlarmData();
  refreshNotices();
  changeDay(dayjs().format('YYYY-MM-DD'));
  init();
  currentGuard();
});

// 初始化常用功能
async function init() {
  await userStore.GetInfo();
  const value = (userStore.user as any).additionalInfo?.menu || [];
  for (const i in value) {
    state.shortcutMenu = [];
    value[i].forEach((item) => {
      if (item.children === false) {
        state.shortcutMenu.push({ ...item, id: i });
      }
    });
  }
}

function gotoRouter(params: any) {
  appStore.SET_appid(params.id);
  refreshAllRoutes().then(() => {
    router.push({ name: params.value, path: params.component });
  });
}
</script>
<style lang="scss" scoped>
.dark {
  .left {
    .list-con {
      border-top: 1px solid #3f3f3f;
    }

    .date-bg {
      background-image: url('../../assets/images/home/<USER>') !important;

      .userInfo {
        .userName {
          color: #ffffff !important;
        }
      }

      .weather {
        color: #ffffff !important;
        display: flex;
        align-items: center;
      }
    }

    .contacts {
      .search {
        border-top: 1px solid #3f3f3f;
      }
    }
    .duty {
      .duty-info {
        border-top: 1px solid #3f3f3f;
      }
    }
  }

  .right {
    .content {
      border-top: 1px solid #3f3f3f;
    }

    .app-content {
      border-top: 1px solid #3f3f3f;
    }
  }
}

.wrapper {
  display: flex;
  justify-content: space-between;
}

.left {
  width: 33%;

  .date-bg {
    padding: 0 34px;
    width: 100%;
    height: 100%;
    background-image: url('../../assets/images/home/<USER>');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .userInfo {
      display: flex;
      justify-content: center;
      flex-direction: column;
      line-height: 30px;
      padding-left: 20px;

      .userName {
        color: #2e3449;
        font-size: 20px;
        font-weight: 600;
      }

      .weather {
        display: flex;
        align-items: center;
        margin-top: 9px;
        color: #59617b;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
      }
    }
  }

  .duty {
    height: 340px;
    border-radius: 12px;
    margin-top: 15px;

    .dut-t {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
    }

    .but {
      box-shadow: 0 4px 4px 0 rgba(255, 255, 255, 0.25);
      width: 100px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background-color: #ffffff;
      color: #8b95df;
      border-radius: 6px;
    }

    .duty-title {
      color: #383f56;
      font-size: 20px;
      padding-left: 30px;
    }

    .duty-info {
      border-top: 1px solid #e8eaf0;
      padding-top: 20px;
      // width: 550px;
      margin: 0 auto;

      .d-bg {
        background-image: url('../../assets/images/home/<USER>');
        background-size: 100% 100%;
        // width: 550px;
        height: 168px;
        margin: 0 auto;
      }
    }

    .duty-event {
      padding-top: 10px;
      // width: 550px;
      margin: 0 auto;
      display: flex;
      justify-content: space-around;
      text-align: center;
      .duty-event-title {
        cursor: pointer;
      }
      .text {
        padding-top: 10px;
        font-weight: 600;
      }
    }
  }

  .contacts {
    height: 220px;
    padding: 60px 20px;
    margin-top: 10px;
    border-radius: 12px;

    .search {
      display: flex;
      justify-content: space-around;
      border-top: 1px solid #e8eaf0;
    }

    .input {
    }

    //.but{
    //  display: flex;height: 56px;width: 88px;font-size: 20px
    //}
  }

  .date-weather {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;

    .week-style {
      text-align: center;
      height: 90px;
      line-height: 40px;
      font-size: 14px;

      &:nth-child(1) {
        color: #59617b;
      }
    }

    .today {
      width: 29px;
      height: 29px;
      background: #318dff;
      color: #ffffff;
      margin-top: 4px;
      line-height: 30px;
      border-radius: 50%;

      .dot {
        margin-top: 6px;
      }
    }

    .dot {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background-color: red;
      margin: 0 auto;
    }
  }

  .list-con {
    border-top: 1px solid #e8eaf0;
    width: 100%;
    height: 75%;
    padding-top: 10px;
    margin-top: 10px;
    overflow-y: auto;

    .list-card {
      padding: 14px 20px;
      margin-bottom: 10px;
      height: 80px;
      background: #f3f9ff;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;

      .schedule-title {
        font-size: 14px;
        line-height: 24px;

        .name {
          font-weight: 600;
        }

        .desc {
          font-weight: 400;
        }
      }

      .schedule-num {
        color: #318dff;
        font-weight: 600;
        font-size: 14px;
      }
    }
  }
}

.right {
  width: 66.2%;

  .right-bottom-title {
    color: #383f56;
    font-size: 20px;
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .right-bottom {
    display: flex;
    justify-content: space-between;

    .notice-card {
      width: 49.3%;
      height: 230px;
      margin-top: 15px;
      padding: 60px 20px;
    }

    .book-card {
      width: 49.3%;
      height: 100%;
      margin-top: 15px;
      padding: 60px 20px;
    }

    .alarm-card {
      height: 230px;
      width: 49.3%;
      margin-top: 15px;
      padding: 60px 20px;
    }
  }

  .right-content {
    margin-top: 15px;
    width: 49.3%;
    height: 184px;
  }

  .class-card {
    height: 220px;
    margin-top: 10px;
    padding: 60px 20px;

    .class-content {
      height: 100%;
    }
  }

  .content {
    border-top: 1px solid #e8eaf0;

    .list {
      display: flex;
      height: 27px;
      justify-content: space-between;
      font-size: 14px;

      div:nth-child(1) {
        width: 330px;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        //color: #383F56;
      }

      div:nth-child(2) {
        width: 140px;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        text-align: right;
        //color: #383F56;
      }
    }
  }

  .app-content {
    border-top: 1px solid #e8eaf0;
    display: flex;
    justify-content: flex-start;
    text-align: center;
    flex-wrap: wrap;

    .lattice {
      width: 12.5%;
      padding-top: 10px;

      .images {
        text-align: center;
        height: 120px;

        .image {
          cursor: pointer;
          margin: 0 auto;
          width: 48px;
          height: 48px;
          background-color: #45ccde;
          text-align: center;
          line-height: 48px;
          border-radius: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  .color-grey {
    color: #c1c4cd;
  }
}

.more-arrow {
  font-size: 14px;
  color: #a5aecd;
  display: flex;
  align-items: center;
  cursor: pointer;
}

ul::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}

div::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}
</style>
