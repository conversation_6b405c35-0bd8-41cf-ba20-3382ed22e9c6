package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesignAmend;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoConstructionDesignAmendPageRequest extends AdvancedPageableQueryEntity<SoConstructionDesignAmend, SoConstructionDesignAmendPageRequest> {
    // 所属设计编号
    @NotNullOrEmpty
    private String designCode;

    // 所属工程编号
    @NotNullOrEmpty
    private String constructionCode;

}
