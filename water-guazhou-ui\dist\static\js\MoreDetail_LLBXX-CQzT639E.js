import{_ as w}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{z as b,d as W,c as h,r as m,b as l,S as k,o as M,g as v,n as F,q as u,i as g,aq as I,C as L}from"./index-r0dFAfgr.js";import{_ as S}from"./Search-NSrhrIa_.js";import{F as _,O as T}from"./options-D6DdN2k5.js";const C=r=>b({url:"/api/spp/dma/partition/flowMeter/list",method:"get",params:r}),q=r=>b({url:"/api/spp/dma/partition/flowMeter",method:"post",data:r}),P=r=>b({url:"/api/spp/dma/partition/flowMeter",method:"delete",data:r}),B={class:"table-wrapper"},O=W({__name:"MoreDetail_LLBXX",props:{partition:{}},setup(r){const c=r,d=h(),f=h(),x=m({filters:[{type:"select",label:"类型",field:"type",labelWidth:40,options:_},{type:"input",label:"水表编号",field:"code",clearable:!1},{type:"input",label:"水表类型",field:"meterType",clearable:!1},{type:"input",label:"水表厂家",field:"brand",clearable:!1}],operations:[{type:"btn-group",btns:[{perm:!0,iconifyIcon:"ep:search",text:"查询",type:"primary",click:()=>o()},{perm:!0,iconifyIcon:"ep:refresh",text:"重置",type:"default",click:()=>{var e;(e=d.value)==null||e.resetForm()}},{perm:!0,iconifyIcon:"ep:circle-plus",text:"新增",type:"success",click:()=>y()},{perm:!1,iconifyIcon:"ep:circle-plus",text:"自动生成",type:"success",click:()=>o()}]}]}),i=m({dataList:[],columns:[{minWidth:120,label:"类型",prop:"type",formatter(e,t){return T[t]??t}},{minWidth:120,label:"水表编号",prop:"code"},{minWidth:120,label:"安装位置",prop:"position"},{minWidth:120,label:"水表类型",prop:"meterType"},{minWidth:120,label:"水表厂家",prop:"brand"},{minWidth:120,label:"管径",prop:"caliber"},{minWidth:120,label:"管材",prop:"pipe"},{minWidth:120,label:"是否远传",prop:"isRemote",formatter(e,t){return t==="1"?"是":t==="0"?"否":""}},{minWidth:120,label:"安装年份",prop:"year"},{minWidth:120,label:"备注",prop:"remark"},{minWidth:130,label:"现场图片",prop:"img",image:!0}],pagination:{refreshData:({page:e,size:t})=>{i.pagination.page=e,i.pagination.limit=t,o()}},operations:[{perm:!0,text:"编辑",iconifyIcon:"ep:edit",click:e=>y(e)},{perm:!0,text:"删除",iconifyIcon:"ep:delete",type:"danger",click:e=>D(e)}]}),o=async()=>{var e,t;try{i.loading=!0;const a=((e=d.value)==null?void 0:e.queryParams)||{},s=(await C({...a,partitionId:(t=c.partition)==null?void 0:t.value,page:i.pagination.page||1,size:i.pagination.limit||20})).data.data||{};i.dataList=s.data||[],i.pagination.total=s.total||0}catch{}i.loading=!1},y=e=>{var t;p.defaultValue={...e||{}},p.title=e?"编辑流量表":"添加流量表",(t=f.value)==null||t.openDialog()},D=e=>{const t=e?[e.id]:[];if(!t.length){l.error("请选择要删除的数据");return}k("确定删除?","提示信息").then(async()=>{try{const a=await P(t);a.data.code===200?(l.success("删除成功"),o()):l.error(a.data.message)}catch{l.error("删除失败")}}).catch(()=>{})},p=m({title:"添加流量表",dialogWidth:600,labelPosition:"right",group:[{fields:[{lg:24,xl:12,type:"select",label:"类型",field:"type",options:_},{lg:24,xl:12,type:"input",label:"水表编号",field:"code"},{type:"textarea",label:"水表位置",field:"position"},{lg:24,xl:12,type:"input",label:"水表类型",field:"meterType"},{lg:24,xl:12,type:"input",label:"水表厂家",field:"brand"},{lg:24,xl:12,type:"input-number",label:"管径",field:"caliber"},{lg:24,xl:12,type:"input",label:"管材",field:"pipe"},{lg:24,xl:12,type:"radio",label:"是否远传",field:"isRemote",options:[{label:"是",value:"1"},{label:"否",value:"0"}]},{lg:24,xl:12,type:"year",label:"安装年份",field:"year"},{type:"textarea",label:"备注",field:"remark"},{type:"image",label:"现场图片",field:"img"}]}],submit:async e=>{var t,a;if(p.submitting=!0,!((t=c.partition)!=null&&t.value)){l.warning("请先选择分区");return}try{const n=await q({...e,partitionId:c.partition.value});n.data.code===200?(l.success("添加成功"),o(),(a=f.value)==null||a.closeDialog()):l.error(n.data.message)}catch{l.error("操作失败")}p.submitting=!1}});return M(()=>{o()}),(e,t)=>{const a=S,n=I,s=w;return v(),F("div",B,[u(a,{ref_key:"refSearch",ref:d,config:g(x),class:"search"},null,8,["config"]),u(n,{config:g(i),class:"table-box"},null,8,["config"]),u(s,{ref_key:"refDialog",ref:f,config:g(p)},null,8,["config"])])}}}),V=L(O,[["__scopeId","data-v-93492d97"]]);export{V as default};
