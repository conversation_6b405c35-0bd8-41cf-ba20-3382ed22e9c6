import{d as H,c as x,a8 as U,s as Y,r as $,ar as J,a9 as R,x as M,o as K,g as N,n as k,q as o,i as a,F as r,p as u,aj as Q,G as d,bh as m,an as E,t as j,cE as Z,J as X,cZ as ee,c6 as te,c_ as ae,H as le,L as oe,b7 as se,C as ne}from"./index-r0dFAfgr.js";/* empty css                             */import{_ as re}from"./CardTable-rdWOL4_6.js";import{_ as pe}from"./CardSearch-CB_HNR-Q.js";import{c as w,f as V}from"./data-XGHpLV70.js";import{I as ie}from"./common-CvK_P_ao.js";import{g as ue,a as de}from"./schedulingInstructions-DdZ9MUvq.js";import{c as me}from"./zhandian-YaGuQZe6.js";import{x as I}from"./xlsx-rVJkW9yq.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const ce={class:"wrapper"},fe={class:"detail-container"},be={class:"detail-header"},_e={class:"detail-content"},ye={class:"detail-section"},ve={class:"detail-section"},ge={key:0,class:"detail-section"},he={key:1,class:"detail-section"},xe={key:2,class:"detail-section"},Te={key:3,class:"detail-section"},Pe={class:"detail-section"},Ne={class:"schedule-impact-container"},ke={class:"impact-section"},we={class:"impact-section"},Ve={class:"impact-section"},Ce=H({__name:"index",setup(Se){const T=x(),C=x(!1),S=x(!1),l=x({}),b=x({accumulatedFlow:"1,250.5 m³",accumulatedPower:"2,850.2 kWh",runningTime:"18小时30分钟",totalPowerConsumption:"3,120.8 kWh",averageFlow:"69.5 m³/h",powerPerTon:"2.28 kWh/m³"}),B=x({filters:[{label:"发送时间",field:"time",type:"daterange"},{xl:8,label:"计划状态",field:"commandStatus",type:"radio-button",options:[{label:"全部",value:""},{label:"待发送",value:"PENDING"},{label:"待接收",value:"WAITING_RECEIVE"},{label:"待回复",value:"WAITING_REPLY"},{label:"已回复",value:"REPLIED"},{label:"已拒绝",value:"DECLINED"}]},{label:"发送人",field:"sendUserId",type:"department-user"},{type:"select-tree",label:"接收部门",checkStrictly:!0,options:U(()=>_.WaterSupplyTree),field:"receiveDeptId"},{type:"select",label:"指令类型",field:"type",options:U(()=>_.types)},{label:"指令查找",field:"sendContent",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:ie.QUERY,click:()=>D()},{type:"default",perm:!0,text:"重置",svgIcon:Y(se),click:()=>{var t;(t=T.value)==null||t.resetForm(),D()}}]}]}),g=$({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"指令类型",prop:"typeName"},{label:"指令状态",prop:"commandStatus",tag:!0,tagColor:t=>{var e;return((e=w[t.commandStatus])==null?void 0:e.color)||""},formatter:t=>{var e;return((e=w[t.commandStatus])==null?void 0:e.value)||""}},{label:"完成状态",prop:"completeStatus",tag:!0,tagColor:t=>{var e;return((e=V[t.completeStatus])==null?void 0:e.color)||""},formatter:t=>{var e;return((e=V[t.completeStatus])==null?void 0:e.value)||""}},{label:"发送站点",prop:"sendDeptName"},{label:"接收站点",prop:"receiveDeptName"},{label:"指令内容",prop:"sendContent"},{label:"执行时间",prop:"executionTime"},{label:"指令备注",prop:"remark"},{label:"发送人",prop:"sendUserName"},{label:"发送时间",prop:"sendTime"},{label:"接收人",prop:"receiveUserName"},{label:"接收时间",prop:"receiveTime"},{label:"回复内容",prop:"replyContent"},{label:"回复时间",prop:"replyTime"},{label:"拒绝原因",prop:"rejectRemark"}],operations:[{text:"详情",perm:!0,type:"primary",isTextBtn:!1,click:t=>G(t)},{text:"导出",perm:!0,type:"success",isTextBtn:!1,click:t=>q(t)},{text:"调度影响",perm:t=>t.typeName&&t.typeName.includes("泵组"),type:"warning",isTextBtn:!1,click:t=>O(t)}],operationWidth:"200px",dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{g.pagination.page=t,g.pagination.limit=e,D()}}}),_=$({WaterSupplyTree:[],getWaterSupplyTreeValue:()=>{J(2).then(e=>{_.WaterSupplyTree=R(e.data.data||[])})},types:[],getTypes:()=>{ue({size:-1,page:1}).then(e=>{_.types=R(e.data.data.data||[],"children",{label:"name",value:"id"})})},pumpList:[],getPumpList:()=>{me("泵站").then(t=>{const e=c=>{let f=[];return c.forEach(s=>{var n;s.type==="Station"&&((n=s.nodeDetail)==null?void 0:n.type)==="泵站"&&f.push({label:s.name,value:s.id}),s.children&&s.children.length>0&&(f=f.concat(e(s.children)))}),f};let p=t.data;t.data&&t.data.data&&(p=t.data.data),_.pumpList=e(p||[])}).catch(()=>{_.pumpList=[]})}}),D=async()=>{var e,p,c,f;const t={size:g.pagination.limit,page:g.pagination.page,...((e=T.value)==null?void 0:e.queryParams)||{}};t.time&&((p=t.time)==null?void 0:p.length)>1&&(t.sendTimeFrom=((c=T.value)==null?void 0:c.queryParams).time[0]||"",t.sendTimeTo=((f=T.value)==null?void 0:f.queryParams).time[1]||""),delete t.time,de(t).then(s=>{const n=s.data.data.data||[];n.forEach(v=>{F(v)}),g.dataList=n,g.pagination.total=s.data.data.total||0})},F=t=>{if(t.enablePumps){const e=t.enablePumps.split(",");t.enablePumpNames=e.map(p=>{const c=_.pumpList.find(f=>f.value===p.trim());return c?c.label:p}).join(", ")}else t.enablePumpNames="";if(t.disablePumps){const e=t.disablePumps.split(",");t.disablePumpNames=e.map(p=>{const c=_.pumpList.find(f=>f.value===p.trim());return c?c.label:p}).join(", ")}else t.disablePumpNames=""},G=t=>{l.value={...t},F(l.value),C.value=!0},q=t=>{W([t],`指令详情_${t.typeName}_${new Date().getTime()}`)},A=()=>{W([l.value],`指令详情_${l.value.typeName}_${new Date().getTime()}`)},O=t=>{l.value={...t},z(t),S.value=!0},z=t=>{var s,n;const e=Math.random()*1e3+500,p=e*(2+Math.random()),c=Math.floor(Math.random()*20)+8,f=Math.floor(Math.random()*60);if(b.value={accumulatedFlow:`${e.toFixed(1)} m³`,accumulatedPower:`${p.toFixed(1)} kWh`,runningTime:`${c}小时${f}分钟`,totalPowerConsumption:`${(p*1.1).toFixed(1)} kWh`,averageFlow:`${(e/c).toFixed(1)} m³/h`,powerPerTon:`${(p/e).toFixed(2)} kWh/m³`},t.typeName&&t.typeName.includes("泵组")){const y=1+((((s=t.enablePumps)==null?void 0:s.split(",").length)||0)+(((n=t.disablePumps)==null?void 0:n.split(",").length)||0))*.2;b.value.accumulatedFlow=`${(e*y).toFixed(1)} m³`,b.value.accumulatedPower=`${(p*y).toFixed(1)} kWh`,b.value.totalPowerConsumption=`${(p*y*1.1).toFixed(1)} kWh`,b.value.averageFlow=`${(e*y/c).toFixed(1)} m³/h`}},W=(t,e)=>{try{const p=t.map(s=>{var n,v;return{指令类型:s.typeName||"",指令状态:((n=w[s.commandStatus])==null?void 0:n.value)||"",完成状态:((v=V[s.completeStatus])==null?void 0:v.value)||"",发送站点:s.sendDeptName||"",接收站点:s.receiveDeptName||"",指令内容:s.sendContent||"",启用泵机:s.enablePumpNames||"",关闭泵机:s.disablePumpNames||"",执行时间:s.executionTime||"",指令备注:s.remark||"",发送人:s.sendUserName||"",发送时间:s.sendTime||"",接收人:s.receiveUserName||"",接收时间:s.receiveTime||"",回复内容:s.replyContent||"",回复时间:s.replyTime||"",结束时间:s.replyTime||"",拒绝原因:s.rejectRemark||""}}),c=I.utils.json_to_sheet(p),f=I.utils.book_new();I.utils.book_append_sheet(f,c,"指令详情"),I.writeFile(f,`${e}.xlsx`),M.success("导出成功")}catch(p){M.error("导出失败"),console.error("Export error:",p)}};return K(async()=>{_.getWaterSupplyTreeValue(),_.getTypes(),_.getPumpList(),D()}),(t,e)=>{const p=pe,c=re,f=Z,s=X,n=ee,v=te,y=ae,h=le,L=oe;return N(),k("div",ce,[o(p,{ref_key:"refSearch",ref:T,config:a(B)},null,8,["config"]),o(c,{class:"card-table",config:a(g)},null,8,["config"]),o(L,{modelValue:a(C),"onUpdate:modelValue":e[6]||(e[6]=i=>j(C)?C.value=i:null),title:"调度详情",width:"80%","close-on-click-modal":!1},{default:r(()=>[u("div",fe,[u("div",be,[o(s,{type:"primary",onClick:A},{default:r(()=>[o(f,null,{default:r(()=>[o(a(Q))]),_:1}),e[8]||(e[8]=d(" 导出 "))]),_:1})]),u("div",_e,[u("div",ye,[e[9]||(e[9]=u("h3",null,"基本信息",-1)),o(y,{column:3,border:""},{default:r(()=>[o(n,{label:"指令类型"},{default:r(()=>[d(m(a(l).typeName),1)]),_:1}),o(n,{label:"指令状态"},{default:r(()=>{var i;return[o(v,{color:(i=a(w)[a(l).commandStatus])==null?void 0:i.color},{default:r(()=>{var P;return[d(m((P=a(w)[a(l).commandStatus])==null?void 0:P.value),1)]}),_:1},8,["color"])]}),_:1}),o(n,{label:"完成状态"},{default:r(()=>{var i;return[o(v,{color:(i=a(V)[a(l).completeStatus])==null?void 0:i.color},{default:r(()=>{var P;return[d(m((P=a(V)[a(l).completeStatus])==null?void 0:P.value),1)]}),_:1},8,["color"])]}),_:1}),o(n,{label:"发送站点"},{default:r(()=>[d(m(a(l).sendDeptName),1)]),_:1}),o(n,{label:"接收站点"},{default:r(()=>[d(m(a(l).receiveDeptName),1)]),_:1}),o(n,{label:"执行时间"},{default:r(()=>[d(m(a(l).executionTime),1)]),_:1}),o(n,{label:"发送人"},{default:r(()=>[d(m(a(l).sendUserName),1)]),_:1}),o(n,{label:"发送时间"},{default:r(()=>[d(m(a(l).sendTime),1)]),_:1}),o(n,{label:"接收人"},{default:r(()=>[d(m(a(l).receiveUserName),1)]),_:1}),o(n,{label:"接收时间"},{default:r(()=>[d(m(a(l).receiveTime),1)]),_:1}),o(n,{label:"回复时间"},{default:r(()=>[d(m(a(l).replyTime),1)]),_:1}),o(n,{label:"结束时间"},{default:r(()=>[d(m(a(l).replyTime),1)]),_:1}),o(n,{label:"已完成"},{default:r(()=>[d(m(a(l).completeStatus==="COMPLETED"?"是":"否"),1)]),_:1})]),_:1})]),u("div",ve,[e[10]||(e[10]=u("h3",null,"指令内容",-1)),o(h,{modelValue:a(l).sendContent,"onUpdate:modelValue":e[0]||(e[0]=i=>a(l).sendContent=i),type:"textarea",rows:3,readonly:""},null,8,["modelValue"])]),a(l).enablePumps||a(l).disablePumps?(N(),k("div",ge,[e[11]||(e[11]=u("h3",null,"启用泵机",-1)),o(h,{modelValue:a(l).enablePumpNames,"onUpdate:modelValue":e[1]||(e[1]=i=>a(l).enablePumpNames=i),readonly:"",placeholder:"无启用泵机"},null,8,["modelValue"])])):E("",!0),a(l).enablePumps||a(l).disablePumps?(N(),k("div",he,[e[12]||(e[12]=u("h3",null,"关闭泵机",-1)),o(h,{modelValue:a(l).disablePumpNames,"onUpdate:modelValue":e[2]||(e[2]=i=>a(l).disablePumpNames=i),readonly:"",placeholder:"无关闭泵机"},null,8,["modelValue"])])):E("",!0),a(l).replyContent?(N(),k("div",xe,[e[13]||(e[13]=u("h3",null,"回复内容",-1)),o(h,{modelValue:a(l).replyContent,"onUpdate:modelValue":e[3]||(e[3]=i=>a(l).replyContent=i),type:"textarea",rows:3,readonly:""},null,8,["modelValue"])])):E("",!0),a(l).rejectRemark?(N(),k("div",Te,[e[14]||(e[14]=u("h3",null,"拒绝原因",-1)),o(h,{modelValue:a(l).rejectRemark,"onUpdate:modelValue":e[4]||(e[4]=i=>a(l).rejectRemark=i),type:"textarea",rows:2,readonly:""},null,8,["modelValue"])])):E("",!0),u("div",Pe,[e[15]||(e[15]=u("h3",null,"指令备注",-1)),o(h,{modelValue:a(l).remark,"onUpdate:modelValue":e[5]||(e[5]=i=>a(l).remark=i),type:"textarea",rows:2,readonly:"",placeholder:"无备注"},null,8,["modelValue"])])])])]),_:1},8,["modelValue"]),o(L,{modelValue:a(S),"onUpdate:modelValue":e[7]||(e[7]=i=>j(S)?S.value=i:null),title:"调度影响",width:"70%","close-on-click-modal":!1},{default:r(()=>[u("div",Ne,[u("div",ke,[e[16]||(e[16]=u("h3",null,"累计流量",-1)),o(y,{column:3,border:""},{default:r(()=>[o(n,{label:"累计流量"},{default:r(()=>[d(m(a(b).accumulatedFlow),1)]),_:1}),o(n,{label:"累计电量"},{default:r(()=>[d(m(a(b).accumulatedPower),1)]),_:1}),o(n,{label:"运行时长"},{default:r(()=>[d(m(a(b).runningTime),1)]),_:1})]),_:1})]),u("div",we,[e[17]||(e[17]=u("h3",null,"供水总量",-1)),o(y,{column:3,border:""},{default:r(()=>[o(n,{label:"耗电总量"},{default:r(()=>[d(m(a(b).totalPowerConsumption),1)]),_:1}),o(n,{label:"平均流量"},{default:r(()=>[d(m(a(b).averageFlow),1)]),_:1})]),_:1})]),u("div",Ve,[e[18]||(e[18]=u("h3",null,"吨水耗电",-1)),o(y,{column:1,border:""},{default:r(()=>[o(n,{label:"吨水耗电"},{default:r(()=>[d(m(a(b).powerPerTon),1)]),_:1})]),_:1})])])]),_:1},8,["modelValue"])])}}}),Be=ne(Ce,[["__scopeId","data-v-46f065ce"]]);export{Be as default};
