package org.thingsboard.server.dao.model.sql.base;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-权限配置对象 base_permission_configuration
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@ApiModel(value = "权限配置", description = "平台管理-权限配置实体类")
@Data
public class BasePermissionConfiguration {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 底图配置主键id
     */
    @ApiModelProperty(value = "底图配置主键id")
    private String mapConfigId;

    /**
     * 角色ids
     */
    @ApiModelProperty(value = "角色ids")
    private String roles;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
