package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeAccountCycleConfig;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-25
 */
public interface PipeAccountCycleConfigService {

    List<PipeAccountCycleConfig> getListByPartition(String month, String name, String partitionId);

    PipeAccountCycleConfig save(PipeAccountCycleConfig pipeAccountCycleConfig);

    void delete(List<String> idList);
}
