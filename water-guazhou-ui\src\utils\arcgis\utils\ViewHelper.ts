import { GetAllFieldConfig } from '@/api/mapservice/fieldconfig';
import { useLayer } from '@/hooks/arcgis';
import { useGisStore } from '@/store';
import dispatchCenterMapGeoJson from '@/views/dispatchCenter/cardCenter/beibei.json';
import Extent from '@arcgis/core/geometry/Extent';
import Point from '@arcgis/core/geometry/Point';
import Map from '@arcgis/core/Map';
import MapView from '@arcgis/core/views/MapView';
import { handleZoomToOverBig } from './BaseMapHelper';
import { createPolygon, setSymbol } from './FeatureHelper';
import { getGraphicLayer } from './LayerHelper';
import axios from 'axios'
/**
 * 初始化地图实例
 * @param el 指定地图容器
 * @param defaultCenter 默认地图中飞
 * @returns MapView
 */
export const initMap = (
  params?: {
    el: any;
  } & IFormGisConfig
) => {
  // const basemap = new Basemap({
  //   baseLayers: [
  //     new VectorTileLayer({
  //       apiKey: 'AAPKbf4e22c18798464f9f15f46254975c13V9DFM0Tj5VyxUNjISsHJoWwb0OgwvhLNnh1HcRrP0-KYTPP3QWTys-xKqahU2Qac',
  //       portalItem: {
  //         id: '5f3c07155abe43eea3504d7db9d6d1f2'
  //         // id: '888486087427436f98243259d2c4ab18'
  //       },
  //       id: 'basemap_darkblue'
  //     })
  //     // Basemap.fromId('osm')
  //   ],
  //   title: 'basemap',
  //   id: 'basemap'

  // })
  const { createTdtLayer } = useLayer();
  const tiledLayer = createTdtLayer({
    filter: params?.defaultFilter,
    type:
      params?.defaultBaseMap ||
      window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap ||
      'vec_w',
    color:
      params?.defaultFilterColor ||
      window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMapFilterColor
  });
  const tiledLayer_Poi = createTdtLayer({
    type: window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi || 'cva_w'
  });
  // const vtlLayer = new VectorTileLayer({
  //   // URL to the style of vector tiles
  //   portalItem: {
  //     id: '5f3c07155abe43eea3504d7db9d6d1f2',
  //     apiKey: window.SITE_CONFIG.GIS_CONFIG.gisApiKey,
  //     title: '深色街道图'
  //   }
  // })
  const referenceLayers =
    params?.showPoi !== false && window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi
      ? [tiledLayer_Poi]
      : [];
  const baseLayers = [tiledLayer, ...referenceLayers];
  const map = new Map({
    // satellite", "hybrid", "terrain", "oceans", "osm", "dark-gray-vector", "gray-vector", "streets-vector", "topo-vector", "streets-night-vector", "streets-relief-vector", "streets-navigation-vector"
    basemap: {
      baseLayers,
      // referenceLayers,
      title: '街道图'
      // portalItem: {
      //   title: '深色街道图',
      //   id: '5f3c07155abe43eea3504d7db9d6d1f2',
      //   apiKey: window.SITE_CONFIG.GIS_CONFIG.gisApiKey
      // }
    }
    // satellite'
  });
  const view: __esri.MapView = new MapView({
    container: params?.el,
    map,
    center: params?.defaultCenter ||
      window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter || [117.09, 30.64],
    zoom: params?.zoom || window.SITE_CONFIG.GIS_CONFIG.gisDefaultZoom || 11,
    highlightOptions: {
      fillOpacity: 0,
      haloOpacity: 0.7
    }
  });

  if (params?.minZoom !== undefined) {
    view.constraints.minZoom = params.minZoom;
  } else if (window.SITE_CONFIG.GIS_CONFIG.gisMinZoom) {
    view.constraints.minZoom = window.SITE_CONFIG.GIS_CONFIG.gisMinZoom;
  }
  if (params?.maxZoom !== undefined) {
    view.constraints.maxZoom = params.maxZoom;
  } else if (window.SITE_CONFIG.GIS_CONFIG.gisMaxZoom) {
    view.constraints.maxZoom = window.SITE_CONFIG.GIS_CONFIG.gisMaxZoom;
  }
  if (params?.disableMove || window.SITE_CONFIG.GIS_CONFIG.gisDisableMove) {
    disableMapMove(view);
  }
  handleZoomToOverBig(view, baseLayers);
  // 清除默认微件
  view.ui.remove('attribution');
  view.ui.remove('zoom');
  view.ui.padding = {
    bottom: 15,
    left: 30,
    right: 30,
    top: 15
  };
  if (window.SITE_CONFIG.GIS_CONFIG.gisShowBoundary) {
    getGraphicLayer(view, {
      id: 'project-area',
      title: window.SITE_CONFIG.GIS_CONFIG.gisBoundaryName
    });
    if (window.SITE_CONFIG.GIS_CONFIG.gisBoundaryFileUrl) {
      axios
        .get(window.SITE_CONFIG.GIS_CONFIG.gisBoundaryFileUrl)
        .then((res) => {
          console.log(res);
          addBoundaryToView(view, res.data.features);
        });
    } else {
      addBoundaryToView(view, dispatchCenterMapGeoJson.features);
    }
  }
  const gisStore = useGisStore();
  if (!gisStore.gLayerConfig) {
    GetAllFieldConfig()
      .then((res) => {
        gisStore.SET_gLayerConfig(res.data.result || []);
      })
      .catch(() => {
        console.log('获取gis服务字段配置失败');
      });
  }
  return view;
};
export const disableMapMove = (view: __esri.MapView) => {
  // 禁止通过键盘+-缩放地图
  view.on('key-down', (event) => {
    const prohibitedKeys = ['+', '-', 'Shift', '_', '='];
    const keyPressed = event.key;
    if (prohibitedKeys.indexOf(keyPressed) !== -1) {
      event.stopPropagation();
    }
  });
  // 禁止滚轮缩放地图
  view.on('mouse-wheel', (event) => {
    event.stopPropagation();
  });
  // 禁止双击放大
  view.on('double-click', (event) => {
    event.stopPropagation();
  });
  // 禁止Ctrl+双击缩小
  view.on('double-click', ['Control'], (event) => {
    event.stopPropagation();
  });
  // 禁止移动地图
  view.on('drag', (event) => {
    event.stopPropagation();
  });
  // 禁止通过上下左右箭头移动地图
  view.on('key-down', (event) => {
    // prevents panning with the arrow keys
    const keyPressed = event.key;
    if (keyPressed.slice(0, 5) === 'Arrow') {
      event.stopPropagation();
    }
  });
  // 禁止Shift+拖拽拉框放大
  view.on('drag', ['Shift'], (event) => {
    event.stopPropagation();
  });
  // 禁止Shift+Ctrl+拖拽拉框缩小

  view.on('drag', ['Shift', 'Control'], (event) => {
    event.stopPropagation();
  });
};
export const addBoundaryToView = (view: __esri.MapView, features: any[]) => {
  if (!features.length) return;
  const graphicsLayer = getGraphicLayer(view, {
    id: 'project-area',
    title: window.SITE_CONFIG.GIS_CONFIG.gisBoundaryName
  });
  const ring = features[0].geometry.coordinates[0][0].map((item) => {
    const point = new Point({
      longitude: item[0],
      latitude: item[1],
      spatialReference: view?.spatialReference
    });
    return [point.x, point.y];
  });
  const area = createPolygon(
    ring,
    view.spatialReference,
    setSymbol('polygon', {
      color: [0, 0, 0, 0],
      outlineWidth: 1
    })
  );

  const text = createPolygon(
    ring,
    view?.spatialReference,
    setSymbol('text', {
      text: window.SITE_CONFIG.GIS_CONFIG.gisBoundaryName
    })
  );
  text && graphicsLayer?.add(text);
  area && graphicsLayer?.add(area);
  extentTo(view, area?.geometry.extent);
};
/**
 * 绑定地图点击事件
 * @param mapView
 * @param callBack
 */
export const bindViewClick = (
  mapView?: __esri.MapView,
  callBack?: (res: __esri.HitTestResult) => void
) => {
  const mapClick = mapView?.on('click', (e) => {
    mapView.hitTest(e).then((response) => {
      // if (response.results[0]) {
      //   const graphic = response.results[0].graphic
      //   mapView.whenLayerView(graphic.layer).then((lyrView: any) => {
      callBack && callBack(response);
      //   })
      // }
    });
  });
  return mapClick;
};
/**
 * 绑定地图点双击事件
 * @param mapView
 * @param callBack
 */
export const bindViewDblClick = (
  mapView: __esri.MapView,
  callBack: (res: __esri.HitTestResult) => void
) => {
  mapView.on('double-click', (e) => {
    mapView.hitTest(e).then((response) => {
      // if (response.results[0]) {
      //   const graphic = response.results[0].graphic
      //   mapView.whenLayerView(graphic.layer).then((lyrView: any) => {
      callBack(response);
      //   })
      // }
    });
  });
};

/**
 * 缩放到指定范围
 * @param view 地图视图对象
 * @param extent 指定的范围
 * @param scale 对指定的范围进行缩放
 */
export const extentTo = (
  view?: __esri.MapView,
  extent?: any,
  fit?: boolean
) => {
  let xmin: number = extent?.xmin || 0;
  let xmax: number = extent?.xmax || 0;
  let ymin: number = extent?.ymin || 0;
  let ymax: number = extent?.ymax || 0;
  if (fit) {
    const width = xmax - xmin;
    const height = ymax - ymin;
    xmin -= width / 2;
    xmax += width / 2;
    ymin -= height / 2;
    ymax += height / 2;
  }
  return view?.goTo(
    new Extent({
      ...{ xmax, ymax, xmin, ymin },
      spatialReference: view.spatialReference
    })
  );
};
