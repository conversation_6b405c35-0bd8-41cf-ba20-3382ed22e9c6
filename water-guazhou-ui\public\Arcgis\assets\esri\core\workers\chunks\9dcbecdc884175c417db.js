"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[1534],{59987:(e,t,r)=>{r.d(t,{EN:()=>c,H9:()=>i,eS:()=>o,f:()=>n});var n,a,s=r(27535);(a=n||(n={})).InvalidFunctionParameters="InvalidFunctionParameters",a.UnsupportedSqlFunction="UnsupportedSqlFunction",a.UnsupportedOperator="UnsupportedOperator",a.UnsupportedSyntax="UnsupportedSyntax",a.UnsupportedIsRhs="UnsupportedIsRhs",a.UnsupportedIsLhs="UnsupportedIsLhs",a.InvalidDataType="InvalidDataType",a.CannotCastValue="CannotCastValue",a.MissingStatisticParameters="MissingStatisticParameters";const u={[n.MissingStatisticParameters]:"Statistic does not have 1 or 0 Parameters",[n.InvalidFunctionParameters]:"Invalid parameters for call to {function}",[n.UnsupportedIsLhs]:"Unsupported left hand expression in is statement",[n.UnsupportedIsRhs]:"Unsupported right hand expression in is statement",[n.UnsupportedOperator]:"Unsupported operator - {operator}",[n.UnsupportedSyntax]:"Unsupported syntax - {node}",[n.UnsupportedSqlFunction]:"Sql function not found = {function}",[n.InvalidDataType]:"Invalid sql data type",[n.CannotCastValue]:"Cannot cast value to the required data type"};class o extends Error{constructor(e,t){super((0,s.Hy)(u[e],t)),this.declaredRootClass="esri.arcade.featureset.support.sqlerror",Error.captureStackTrace&&Error.captureStackTrace(this,o)}}var i;!function(e){e.NeverReach="NeverReach",e.NotImplemented="NotImplemented",e.Cancelled="Cancelled",e.InvalidStatResponse="InvalidStatResponse",e.InvalidRequest="InvalidRequest",e.RequestFailed="RequestFailed",e.MissingFeatures="MissingFeatures",e.AggregationFieldNotFound="AggregationFieldNotFound",e.DataElementsNotFound="DataElementsNotFound"}(i||(i={}));const l={[i.Cancelled]:"Cancelled",[i.InvalidStatResponse]:"Invalid statistics response from service",[i.InvalidRequest]:"Invalid request",[i.RequestFailed]:"Request failed - {reason}",[i.MissingFeatures]:"Missing features",[i.AggregationFieldNotFound]:"Aggregation field not found",[i.DataElementsNotFound]:"Data elements not found on service",[i.NeverReach]:"Encountered unreachable logic",[i.NotImplemented]:"Not implemented"};class c extends Error{constructor(e,t){super((0,s.Hy)(l[e],t)),this.declaredRootClass="esri.arcade.featureset.support.featureseterror",Error.captureStackTrace&&Error.captureStackTrace(this,c)}}},41534:(e,t,r)=>{r.r(t),r.d(t,{WhereClause:()=>P,defaultAttributeAdapter:()=>j});var n=r(80442);function a(e,t){const r=s[e.toLowerCase()];return null!=r&&t>=r.minParams&&t<=r.maxParams}const s={min:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.min.apply(Math,e[0])},max:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.max.apply(Math,e[0])},avg:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:u(e[0])},sum:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:function(e){let t=0;for(let r=0;r<e.length;r++)t+=e[r];return t}(e[0])},stddev:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.sqrt(o(e[0]))},count:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:e[0].length},var:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:o(e[0])}};function u(e){let t=0;for(let r=0;r<e.length;r++)t+=e[r];return t/e.length}function o(e){const t=u(e),r=e.length;let n=0;for(const r of e)n+=(r-t)**2;return r>1?n/(r-1):0}var i=r(59987),l=r(19153),c=r(17126);class p{constructor(){this.op="+",this.day=0,this.second=0,this.hour=0,this.month=0,this.year=0,this.minute=0,this.millis=0}static _fixDefaults(e){if(null!==e.precision||null!==e.secondary)throw new Error("Primary and Secondary SqlInterval qualifiers not supported")}static _parseSecondsComponent(e,t){if(t.includes(".")){const r=t.split(".");e.second=parseFloat(r[0]),e.millis=parseInt(r[1],10)}else e.second=parseFloat(t)}static createFromMilliseconds(e){const t=new p;return t.second=e/1e3,t}static createFromValueAndQualifer(e,t,r){let n=null;const a=new p;if(a.op="-"===r?"-":"+","interval-period"===t.type){p._fixDefaults(t);const r=new RegExp("^[0-9]{1,}$");if("year"===t.period||"month"===t.period)throw new Error("Year-Month Intervals not supported");if("second"===t.period){if(!/^[0-9]{1,}([.]{1}[0-9]{1,}){0,1}$/.test(e))throw new Error("Illegal Interval");p._parseSecondsComponent(a,e)}else{if(!r.test(e))throw new Error("Illegal Interval");a[t.period]=parseFloat(e)}}else{if(p._fixDefaults(t.start),p._fixDefaults(t.end),"year"===t.start.period||"month"===t.start.period)throw new Error("Year-Month Intervals not supported");if("year"===t.end.period||"month"===t.end.period)throw new Error("Year-Month Intervals not supported");switch(t.start.period){case"day":switch(t.end.period){case"hour":if(n=new RegExp("^[0-9]{1,} [0-9]{1,}$"),!n.test(e))throw new Error("Illegal Interval");a[t.start.period]=parseFloat(e.split(" ")[0]),a[t.end.period]=parseFloat(e.split(" ")[1]);break;case"minute":if(n=new RegExp("^[0-9]{1,} [0-9]{1,2}:[0-9]{1,}$"),!n.test(e))throw new Error("Illegal Interval");{a[t.start.period]=parseFloat(e.split(" ")[0]);const r=e.split(" ")[1].split(":");a.hour=parseFloat(r[0]),a.minute=parseFloat(r[1])}break;case"second":if(n=new RegExp("^[0-9]{1,} [0-9]{1,2}:[0-9]{1,2}:[0-9]{1,}([.]{1}[0-9]{1,}){0,1}$"),!n.test(e))throw new Error("Illegal Interval");{a[t.start.period]=parseFloat(e.split(" ")[0]);const r=e.split(" ")[1].split(":");a.hour=parseFloat(r[0]),a.minute=parseFloat(r[1]),p._parseSecondsComponent(a,r[2])}break;default:throw new Error("Invalid Interval.")}break;case"hour":switch(t.end.period){case"minute":if(n=new RegExp("^[0-9]{1,}:[0-9]{1,}$"),!n.test(e))throw new Error("Illegal Interval");a.hour=parseFloat(e.split(":")[0]),a.minute=parseFloat(e.split(":")[1]);break;case"second":if(n=new RegExp("^[0-9]{1,}:[0-9]{1,2}:[0-9]{1,}([.]{1}[0-9]{1,}){0,1}$"),!n.test(e))throw new Error("Illegal Interval");{const t=e.split(":");a.hour=parseFloat(t[0]),a.minute=parseFloat(t[1]),p._parseSecondsComponent(a,t[2])}break;default:throw new Error("Invalid Interval.")}break;case"minute":if("second"!==t.end.period)throw new Error("Invalid Interval.");if(n=new RegExp("^[0-9]{1,}:[0-9]{1,}([.]{1}[0-9]{1,}){0,1}$"),!n.test(e))throw new Error("Illegal Interval");{const t=e.split(":");a.minute=parseFloat(t[0]),p._parseSecondsComponent(a,t[1])}break;default:throw new Error("Invalid Interval.")}}return a}valueInMilliseconds(){return("-"===this.op?-1:1)*(this.millis+1e3*this.second+60*this.minute*1e3+60*this.hour*60*1e3+24*this.day*60*60*1e3+this.month*(365/12)*24*60*60*1e3+365*this.year*24*60*60*1e3)}}function f(e,t){const r=h[e.toLowerCase()];if(null==r)throw new Error("Function Not Recognised");if(t.length<r.minParams||t.length>r.maxParams)throw new Error(`Invalid Parameter count for call to ${e.toUpperCase()}`);return r.evaluate(t)}function d(e){const t=new Date(e.getTime());return t.setHours(0,0,0,0),t}function v(e,t){if(e instanceof Date)return t?d(e):e;if("string"==typeof(r=e)||r instanceof String){const r=c.ou.fromSQL(e);if(r.isValid)return t?d(r.toJSDate()):r.toJSDate()}var r;throw new i.eS(i.f.CannotCastValue)}const h={extract:{minParams:2,maxParams:2,evaluate:([e,t])=>{if(null==t)return null;if(t instanceof Date)switch(e.toUpperCase()){case"SECOND":return t.getSeconds();case"MINUTE":return t.getMinutes();case"HOUR":return t.getHours();case"DAY":return t.getDate();case"MONTH":return t.getMonth()+1;case"YEAR":return t.getFullYear()}throw new Error("Invalid Parameter for call to EXTRACT")}},substring:{minParams:2,maxParams:3,evaluate:e=>{if(2===e.length){const[t,r]=e;return null==t||null==r?null:t.toString().substring(r-1)}if(3===e.length){const[t,r,n]=e;return null==t||null==r||null==n?null:n<=0?"":t.toString().substring(r-1,r+n-1)}}},position:{minParams:2,maxParams:2,evaluate:([e,t])=>null==e||null==t?null:t.indexOf(e)+1},trim:{minParams:2,maxParams:3,evaluate:e=>{const t=3===e.length,r=t?e[1]:" ",n=t?e[2]:e[1];if(null==r||null==n)return null;const a=`(${(0,l.Qs)(r)})`;switch(e[0]){case"BOTH":return n.replace(new RegExp(`^${a}*|${a}*$`,"g"),"");case"LEADING":return n.replace(new RegExp(`^${a}*`,"g"),"");case"TRAILING":return n.replace(new RegExp(`${a}*$`,"g"),"")}throw new Error("Invalid Parameter for call to TRIM")}},abs:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.abs(e[0])},ceiling:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.ceil(e[0])},floor:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.floor(e[0])},log:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.log(e[0])},log10:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.log(e[0])*Math.LOG10E},sin:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.sin(e[0])},cos:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.cos(e[0])},tan:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.tan(e[0])},asin:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.asin(e[0])},acos:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.acos(e[0])},atan:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.atan(e[0])},sign:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:e[0]>0?1:e[1]<0?-1:0},power:{minParams:2,maxParams:2,evaluate:e=>null==e[0]||null==e[1]?null:e[0]**e[1]},mod:{minParams:2,maxParams:2,evaluate:e=>null==e[0]||null==e[1]?null:e[0]%e[1]},round:{minParams:1,maxParams:2,evaluate:e=>{const t=e[0],r=2===e.length?10**e[1]:1;return null==t?null:Math.round(t*r)/r}},truncate:{minParams:1,maxParams:2,evaluate:e=>null==e[0]?null:1===e.length?parseInt(e[0].toFixed(0),10):parseFloat(e[0].toFixed(e[1]))},char_length:{minParams:1,maxParams:1,evaluate:e=>"string"==typeof e[0]||e[0]instanceof String?e[0].length:0},concat:{minParams:1,maxParams:1/0,evaluate:e=>{let t="";for(let r=0;r<e.length;r++){if(null==e[r])return null;t+=e[r].toString()}return t}},lower:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:e[0].toString().toLowerCase()},upper:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:e[0].toString().toUpperCase()},coalesce:{minParams:1,maxParams:1/0,evaluate:e=>{for(const t of e)if(null!==t)return t;return null}},cosh:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.cosh(e[0])},sinh:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.sinh(e[0])},tanh:{minParams:1,maxParams:1,evaluate:e=>null==e[0]?null:Math.tanh(e[0])},nullif:{minParams:2,maxParams:2,evaluate:e=>{const t=e[0],r=e[1];return(t instanceof Date?t.valueOf():t)===(r instanceof Date?r.valueOf():r)?null:t}},cast:{minParams:2,maxParams:2,evaluate:e=>{const t=e[0],r=e[1];if(null===t)return null;switch(r.type){case"integer":{const e=parseInt(t,10);if(isNaN(e))throw new i.eS(i.f.CannotCastValue);return e}case"smallint":{const e=parseInt(t,10);if(isNaN(e))throw new i.eS(i.f.CannotCastValue);if(e>32767||e<-32767)throw new i.eS(i.f.CannotCastValue);return e}case"float":case"real":{const e=parseFloat(t);if(isNaN(e))throw new i.eS(i.f.CannotCastValue);return e}case"date":return v(t,!0);case"timestamp":return v(t,!1);case"varchar":{const e=t.toString();if(e.length>r.size)throw new i.eS(i.f.CannotCastValue);return e}default:throw new i.eS(i.f.InvalidDataType)}}}};r(5732);var m,g,y={};g=function(){function e(t,r,n,a){var s=Error.call(this,t);return Object.setPrototypeOf&&Object.setPrototypeOf(s,e.prototype),s.expected=r,s.found=n,s.location=a,s.name="SyntaxError",s}function t(e,t,r){return r=r||" ",e.length>t?e:(t-=e.length,e+(r+=r.repeat(t)).slice(0,t))}return function(e,t){function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r}(e,Error),e.prototype.format=function(e){var r="Error: "+this.message;if(this.location){var n,a=null;for(n=0;n<e.length;n++)if(e[n].source===this.location.source){a=e[n].text.split(/\r\n|\n|\r/g);break}var s=this.location.start,u=this.location.source+":"+s.line+":"+s.column;if(a){var o=this.location.end,i=t("",s.line.toString().length," "),l=a[s.line-1],c=(s.line===o.line?o.column:l.length+1)-s.column||1;r+="\n --\x3e "+u+"\n"+i+" |\n"+s.line+" | "+l+"\n"+i+" | "+t("",s.column-1," ")+t("",c,"^")}else r+="\n at "+u}return r},e.buildMessage=function(e,t){var r={literal:function(e){return'"'+a(e.text)+'"'},class:function(e){var t=e.parts.map((function(e){return Array.isArray(e)?s(e[0])+"-"+s(e[1]):s(e)}));return"["+(e.inverted?"^":"")+t.join("")+"]"},any:function(){return"any character"},end:function(){return"end of input"},other:function(e){return e.description}};function n(e){return e.charCodeAt(0).toString(16).toUpperCase()}function a(e){return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(e){return"\\x0"+n(e)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(e){return"\\x"+n(e)}))}function s(e){return e.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(e){return"\\x0"+n(e)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(e){return"\\x"+n(e)}))}function u(e){return r[e.type](e)}return"Expected "+function(e){var t,r,n=e.map(u);if(n.sort(),n.length>0){for(t=1,r=1;t<n.length;t++)n[t-1]!==n[t]&&(n[r]=n[t],r++);n.length=r}switch(n.length){case 1:return n[0];case 2:return n[0]+" or "+n[1];default:return n.slice(0,-1).join(", ")+", or "+n[n.length-1]}}(e)+" but "+function(e){return e?'"'+a(e)+'"':"end of input"}(t)+" found."},{SyntaxError:e,parse:function(t,r){var n,a={},s=(r=void 0!==r?r:{}).grammarSource,u={start:Ge},o=Ge,i="''",l=/^[A-Za-z_\x80-\uFFFF]/,c=/^[A-Za-z0-9_]/,p=/^[A-Za-z0-9_.\x80-\uFFFF]/,f=/^[^']/,d=/^[0-9]/,v=/^[eE]/,h=/^[+\-]/,m=/^[ \t\n\r]/,g=/^[^`]/,y=Ve("!",!1),w=Ve("=",!1),N=Ve(">=",!1),I=Ve(">",!1),b=Ve("<=",!1),T=Ve("<>",!1),S=Ve("<",!1),x=Ve("!=",!1),A=Ve("+",!1),E=Ve("-",!1),C=Ve("||",!1),P=Ve("*",!1),_=Ve("/",!1),F=He([["A","Z"],["a","z"],"_",["","￿"]],!1,!1),L=He([["A","Z"],["a","z"],["0","9"],"_"],!1,!1),M=He([["A","Z"],["a","z"],["0","9"],"_",".",["","￿"]],!1,!1),R=Ve("@",!1),D=Ve("'",!1),O=Ve("N'",!1),J=Ve("''",!1),U=He(["'"],!0,!1),k=Ve(".",!1),$=He([["0","9"]],!1,!1),q=He(["e","E"],!1,!1),V=He(["+","-"],!1,!1),H=Ve("NULL",!0),B=Ve("TRUE",!0),j=Ve("FALSE",!0),z=Ve("IN",!0),G=Ve("IS",!0),Y=Ve("LIKE",!0),W=Ve("ESCAPE",!0),Z=Ve("NOT",!0),Q=Ve("AND",!0),K=Ve("OR",!0),X=Ve("BETWEEN",!0),ee=Ve("FROM",!0),te=Ve("FOR",!0),re=Ve("SUBSTRING",!0),ne=Ve("EXTRACT",!0),ae=Ve("TRIM",!0),se=Ve("POSITION",!0),ue=Ve("TIMESTAMP",!0),oe=Ve("DATE",!0),ie=Ve("LEADING",!0),le=Ve("TRAILING",!0),ce=Ve("BOTH",!0),pe=Ve("CAST",!0),fe=Ve("AS",!0),de=Ve("INTEGER",!0),ve=Ve("SMALLINT",!0),he=Ve("FLOAT",!0),me=Ve("REAL",!0),ge=Ve("VARCHAR",!0),ye=Ve("TO",!0),we=Ve("INTERVAL",!0),Ne=Ve("YEAR",!0),Ie=Ve("MONTH",!0),be=Ve("DAY",!0),Te=Ve("HOUR",!0),Se=Ve("MINUTE",!0),xe=Ve("SECOND",!0),Ae=Ve("CASE",!0),Ee=Ve("END",!0),Ce=Ve("WHEN",!0),Pe=Ve("THEN",!0),_e=Ve("ELSE",!0),Fe=Ve(",",!1),Le=Ve("(",!1),Me=Ve(")",!1),Re=He([" ","\t","\n","\r"],!1,!1),De=Ve("`",!1),Oe=He(["`"],!0,!1),Je=0,Ue=[{line:1,column:1}],ke=0,$e=[],qe=0;if("startRule"in r){if(!(r.startRule in u))throw new Error("Can't start parsing from rule \""+r.startRule+'".');o=u[r.startRule]}function Ve(e,t){return{type:"literal",text:e,ignoreCase:t}}function He(e,t,r){return{type:"class",parts:e,inverted:t,ignoreCase:r}}function Be(e){var r,n=Ue[e];if(n)return n;for(r=e-1;!Ue[r];)r--;for(n={line:(n=Ue[r]).line,column:n.column};r<e;)10===t.charCodeAt(r)?(n.line++,n.column=1):n.column++,r++;return Ue[e]=n,n}function je(e,t){var r=Be(e),n=Be(t);return{source:s,start:{offset:e,line:r.line,column:r.column},end:{offset:t,line:n.line,column:n.column}}}function ze(e){Je<ke||(Je>ke&&(ke=Je,$e=[]),$e.push(e))}function Ge(){var e,t;return e=Je,wr(),(t=We())!==a?(wr(),e=t):(Je=e,e=a),e}function Ye(){var e,t,r,n,s,u,o,i;if(e=Je,(t=We())!==a){for(r=[],n=Je,s=wr(),(u=mr())!==a?(o=wr(),(i=We())!==a?n=s=[s,u,o,i]:(Je=n,n=a)):(Je=n,n=a);n!==a;)r.push(n),n=Je,s=wr(),(u=mr())!==a?(o=wr(),(i=We())!==a?n=s=[s,u,o,i]:(Je=n,n=a)):(Je=n,n=a);e=function(e,t){var r={type:"expression-list"},n=function(e,t,r){return function(e,t){for(var r=[e],n=0;n<t.length;n++)r.push(t[n][3]);return r}(e,t)}(e,t);return r.value=n,r}(t,r)}else Je=e,e=a;return e}function We(){var e,t,r,n,s,u,o,i;if(e=Je,(t=Ze())!==a){for(r=[],n=Je,s=wr(),(u=Jt())!==a?(o=wr(),(i=Ze())!==a?n=s=[s,u,o,i]:(Je=n,n=a)):(Je=n,n=a);n!==a;)r.push(n),n=Je,s=wr(),(u=Jt())!==a?(o=wr(),(i=Ze())!==a?n=s=[s,u,o,i]:(Je=n,n=a)):(Je=n,n=a);e=function(e,t){return Tr(e,t)}(t,r)}else Je=e,e=a;return e}function Ze(){var e,t,r,n,s,u,o,i;if(e=Je,(t=Qe())!==a){for(r=[],n=Je,s=wr(),(u=Ot())!==a?(o=wr(),(i=Qe())!==a?n=s=[s,u,o,i]:(Je=n,n=a)):(Je=n,n=a);n!==a;)r.push(n),n=Je,s=wr(),(u=Ot())!==a?(o=wr(),(i=Qe())!==a?n=s=[s,u,o,i]:(Je=n,n=a)):(Je=n,n=a);e=function(e,t){return Tr(e,t)}(t,r)}else Je=e,e=a;return e}function Qe(){var e,r,n,s,u;return e=Je,(r=Dt())===a&&(r=Je,33===t.charCodeAt(Je)?(n="!",Je++):(n=a,0===qe&&ze(y)),n!==a?(s=Je,qe++,61===t.charCodeAt(Je)?(u="=",Je++):(u=a,0===qe&&ze(w)),qe--,u===a?s=void 0:(Je=s,s=a),s!==a?r=n=[n,s]:(Je=r,r=a)):(Je=r,r=a)),r!==a?(n=wr(),(s=Qe())!==a?e=function(e){return function(e,t){return{type:"unary-expression",operator:"NOT",expr:t}}(0,e)}(s):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=function(){var e,t,r;return e=Je,(t=tt())!==a?(wr(),(r=function(){var e;return(e=function(){var e,t,r,n,s,u;if(e=[],t=Je,r=wr(),(n=Ke())!==a?(s=wr(),(u=tt())!==a?t=r=[r,n,s,u]:(Je=t,t=a)):(Je=t,t=a),t!==a)for(;t!==a;)e.push(t),t=Je,r=wr(),(n=Ke())!==a?(s=wr(),(u=tt())!==a?t=r=[r,n,s,u]:(Je=t,t=a)):(Je=t,t=a);else e=a;return e!==a&&(e=function(e){return{type:"arithmetic",tail:e}}(e)),e}())===a&&(e=function(){var e,t,r,n;return e=Je,(t=et())!==a?(wr(),(r=gr())!==a?(wr(),(n=Ye())!==a?(wr(),yr()!==a?e=function(e,t){return{op:e,right:t}}(t,n):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=et())!==a?(wr(),(r=gr())!==a?(wr(),(n=yr())!==a?e=function(e){return{op:e,right:{type:"expression-list",value:[]}}}(t):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=et())!==a?(wr(),(r=ct())!==a?e=function(e,t){return{op:e,right:t}}(t,r):(Je=e,e=a)):(Je=e,e=a))),e}())===a&&(e=function(){var e,t,r,n,s,u;return e=Je,(t=Dt())!==a?(wr(),(r=Ut())!==a?(wr(),(n=tt())!==a?(wr(),(s=Ot())!==a?(wr(),(u=tt())!==a?e=function(e,t,r){return{op:"NOT"+e,right:{type:"expression-list",value:[t,r]}}}(r,n,u):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=Ut())!==a?(wr(),(r=tt())!==a?(wr(),(n=Ot())!==a?(wr(),(s=tt())!==a?e=function(e,t,r){return{op:e,right:{type:"expression-list",value:[t,r]}}}(t,r,s):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)),e}())===a&&(e=function(){var e,t,r,n;return e=Je,(t=Lt())!==a?(wr(),(r=Dt())!==a?(wr(),(n=tt())!==a?e=function(e,t){return{op:e+"NOT",right:t}}(t,n):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=Lt())!==a?(wr(),(r=tt())!==a?e=function(e,t){return{op:e,right:t}}(t,r):(Je=e,e=a)):(Je=e,e=a)),e}())===a&&(e=function(){var e,t,r,n;return e=Je,(t=Xe())!==a?(wr(),(r=gt())!==a?(wr(),Rt()!==a?(wr(),(n=yt())!==a?e=function(e,t,r){return{op:e,right:t,escape:r.value}}(t,r,n):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=Xe())!==a?(wr(),(r=gt())!==a?e=function(e,t){return{op:e,right:t,escape:""}}(t,r):(Je=e,e=a)):(Je=e,e=a)),e}()),e}())===a&&(r=null),e=function(e,t){return""==t||null==t||null==t?e:"arithmetic"==t.type?Tr(e,t.tail):br(t.op,e,t.right,t.escape)}(t,r)):(Je=e,e=a),e}()),e}function Ke(){var e;return">="===t.substr(Je,2)?(e=">=",Je+=2):(e=a,0===qe&&ze(N)),e===a&&(62===t.charCodeAt(Je)?(e=">",Je++):(e=a,0===qe&&ze(I)),e===a&&("<="===t.substr(Je,2)?(e="<=",Je+=2):(e=a,0===qe&&ze(b)),e===a&&("<>"===t.substr(Je,2)?(e="<>",Je+=2):(e=a,0===qe&&ze(T)),e===a&&(60===t.charCodeAt(Je)?(e="<",Je++):(e=a,0===qe&&ze(S)),e===a&&(61===t.charCodeAt(Je)?(e="=",Je++):(e=a,0===qe&&ze(w)),e===a&&("!="===t.substr(Je,2)?(e="!=",Je+=2):(e=a,0===qe&&ze(x)))))))),e}function Xe(){var e,t,r,n,s;return e=Je,t=Je,(r=Dt())!==a?(n=wr(),(s=Mt())!==a?t=r=[r,n,s]:(Je=t,t=a)):(Je=t,t=a),t!==a&&(t=function(e){return e[0]+" "+e[2]}(t)),(e=t)===a&&(e=Mt()),e}function et(){var e,t,r,n,s;return e=Je,t=Je,(r=Dt())!==a?(n=wr(),(s=Ft())!==a?t=r=[r,n,s]:(Je=t,t=a)):(Je=t,t=a),t!==a&&(t=function(e){return e[0]+" "+e[2]}(t)),(e=t)===a&&(e=Ft()),e}function tt(){var e,t,r,n,s,u,o,i;if(e=Je,(t=nt())!==a){for(r=[],n=Je,s=wr(),(u=rt())!==a?(o=wr(),(i=nt())!==a?n=s=[s,u,o,i]:(Je=n,n=a)):(Je=n,n=a);n!==a;)r.push(n),n=Je,s=wr(),(u=rt())!==a?(o=wr(),(i=nt())!==a?n=s=[s,u,o,i]:(Je=n,n=a)):(Je=n,n=a);e=function(e,t){return Tr(e,t)}(t,r)}else Je=e,e=a;return e}function rt(){var e;return 43===t.charCodeAt(Je)?(e="+",Je++):(e=a,0===qe&&ze(A)),e===a&&(45===t.charCodeAt(Je)?(e="-",Je++):(e=a,0===qe&&ze(E)),e===a&&("||"===t.substr(Je,2)?(e="||",Je+=2):(e=a,0===qe&&ze(C)))),e}function nt(){var e,t,r,n,s,u,o,i;if(e=Je,(t=st())!==a){for(r=[],n=Je,s=wr(),(u=at())!==a?(o=wr(),(i=st())!==a?n=s=[s,u,o,i]:(Je=n,n=a)):(Je=n,n=a);n!==a;)r.push(n),n=Je,s=wr(),(u=at())!==a?(o=wr(),(i=st())!==a?n=s=[s,u,o,i]:(Je=n,n=a)):(Je=n,n=a);e=function(e,t){return Tr(e,t)}(t,r)}else Je=e,e=a;return e}function at(){var e;return 42===t.charCodeAt(Je)?(e="*",Je++):(e=a,0===qe&&ze(P)),e===a&&(47===t.charCodeAt(Je)?(e="/",Je++):(e=a,0===qe&&ze(_))),e}function st(){var e,t;return(e=function(){var e;return(e=yt())===a&&(e=function(){var e,t,r,n;return e=Je,(t=function(){var e,t,r,n;return e=Je,(t=bt())!==a&&(r=Tt())!==a&&(n=St())!==a?e=function(e,t,r){return parseFloat(e+t+r)}(t,r,n):(Je=e,e=a),e===a&&(e=Je,(t=bt())!==a&&(r=Tt())!==a?e=function(e,t){return parseFloat(e+t)}(t,r):(Je=e,e=a),e===a&&(e=Je,(t=bt())!==a&&(r=St())!==a?e=function(e,t){return parseFloat(e+t)}(t,r):(Je=e,e=a),e===a&&(e=Je,(t=bt())!==a&&(t=function(e){return parseFloat(e)}(t)),e=t))),e}())!==a?(r=Je,qe++,n=ot(),qe--,n===a?r=void 0:(Je=r,r=a),r!==a?e=function(e){return{type:"number",value:e}}(t):(Je=e,e=a)):(Je=e,e=a),e}())===a&&(e=function(){var e,t;return e=Je,(t=Pt())!==a&&(t={type:"boolean",value:!0}),(e=t)===a&&(e=Je,(t=_t())!==a&&(t={type:"boolean",value:!1}),e=t),e}())===a&&(e=function(){var e;return(e=Ct())!==a&&(e={type:"null",value:null}),e}())===a&&(e=function(){var e,t;return e=Je,zt()!==a?(wr(),(t=gt())!==a?e=function(e){return{type:"date",value:e.value}}(t):(Je=e,e=a)):(Je=e,e=a),e}())===a&&(e=function(){var e,t;return e=Je,jt()!==a?(wr(),(t=gt())!==a?e=function(e){return{type:"timestamp",value:e.value}}(t):(Je=e,e=a)):(Je=e,e=a),e}())===a&&(e=ft()),e}())===a&&(e=function(){var e,t,r;return e=Je,Vt()!==a?(wr(),gr()!==a?(wr(),(t=function(){var e;return(e=sr())===a&&(e=ur())===a&&(e=or())===a&&(e=ir())===a&&(e=lr())===a&&(e=cr()),e}())!==a?(wr(),kt()!==a?(wr(),(r=We())!==a?(wr(),yr()!==a?e=function(e,t){return{type:"function",name:"extract",args:{type:"expression-list",value:[{type:"string",value:e},t]}}}(t,r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e}())===a&&(e=function(){var e,t,r,n,s,u,o;return e=Je,qt()!==a?(wr(),gr()!==a?(wr(),(t=We())!==a?(wr(),kt()!==a?(wr(),(r=We())!==a?(wr(),n=Je,(s=$t())!==a?(u=wr(),(o=We())!==a?n=s=[s,u,o,wr()]:(Je=n,n=a)):(Je=n,n=a),n===a&&(n=null),(s=yr())!==a?e=function(e,t,r){return{type:"function",name:"substring",args:{type:"expression-list",value:r?[e,t,r[2]]:[e,t]}}}(t,r,n):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e}())===a&&(e=function(){var e,t,r,n;return e=Je,Ht()!==a?(wr(),gr()!==a?(wr(),(t=pt())===a&&(t=null),wr(),(r=We())!==a?(wr(),kt()!==a?(wr(),(n=We())!==a?(wr(),yr()!==a?e=function(e,t,r){return{type:"function",name:"trim",args:{type:"expression-list",value:[{type:"string",value:e??"BOTH"},t,r]}}}(t,r,n):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,Ht()!==a?(wr(),gr()!==a?(wr(),(t=pt())===a&&(t=null),wr(),(r=We())!==a?(wr(),yr()!==a?e=function(e,t){return{type:"function",name:"trim",args:{type:"expression-list",value:[{type:"string",value:e??"BOTH"},t]}}}(t,r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)),e}())===a&&(e=function(){var e,t,r;return e=Je,Bt()!==a?(wr(),gr()!==a?(wr(),(t=We())!==a?(wr(),Ft()!==a?(wr(),(r=We())!==a?(wr(),yr()!==a?e=function(e,t){return{type:"function",name:"position",args:{type:"expression-list",value:[e,t]}}}(t,r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e}())===a&&(e=function(){var e,t,r;return e=Je,Zt()!==a?(wr(),gr()!==a?(wr(),(t=We())!==a?(wr(),Qt()!==a?(wr(),(r=function(){var e,t,r;return e=Je,(t=Kt())!==a&&(t={type:"data-type",value:{type:"integer"}}),(e=t)===a&&(e=Je,(t=Xt())!==a&&(t={type:"data-type",value:{type:"smallint"}}),(e=t)===a&&(e=Je,(t=er())!==a&&(t={type:"data-type",value:{type:"float"}}),(e=t)===a&&(e=Je,(t=tr())!==a&&(t={type:"data-type",value:{type:"real"}}),(e=t)===a&&(e=Je,(t=zt())!==a&&(t={type:"data-type",value:{type:"date"}}),(e=t)===a&&(e=Je,(t=jt())!==a&&(t={type:"data-type",value:{type:"timestamp"}}),(e=t)===a&&(e=Je,(t=rr())!==a?(wr(),gr()!==a?(wr(),(r=xt())!==a?(wr(),yr()!==a?e=function(e){return{type:"data-type",value:{type:"varchar",size:parseInt(e)}}}(r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a))))))),e}())!==a?(wr(),yr()!==a?e=function(e,t){return{type:"function",name:"cast",args:{type:"expression-list",value:[e,t]}}}(t,r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e}())===a&&(e=function(){var e,t,r;return e=Je,(t=Ir())!==a?(wr(),gr()!==a?(wr(),(r=Ye())===a&&(r=null),wr(),yr()!==a?e=function(e,t){return{type:"function",name:e,args:t||{type:"expression-list",value:[]}}}(t,r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e}())===a&&(e=function(){var e;return(e=function(){var e,t,r,n,s;if(e=Je,pr()!==a)if(wr(),(t=We())!==a){for(wr(),r=[],n=Nt();n!==a;)r.push(n),n=Nt();n=wr(),(s=fr())!==a?e=function(e,t){return{type:"case-expression",format:"simple",operand:e,clauses:t,else:null}}(t,r):(Je=e,e=a)}else Je=e,e=a;else Je=e,e=a;if(e===a)if(e=Je,pr()!==a)if(wr(),(t=We())!==a){for(wr(),r=[],n=Nt();n!==a;)r.push(n),n=Nt();n=wr(),(s=It())!==a?(wr(),fr()!==a?e=function(e,t,r){return{type:"case-expression",format:"simple",operand:e,clauses:t,else:r.value}}(t,r,s):(Je=e,e=a)):(Je=e,e=a)}else Je=e,e=a;else Je=e,e=a;return e}())===a&&(e=function(){var e,t,r,n;if(e=Je,pr()!==a){for(wr(),t=[],r=wt();r!==a;)t.push(r),r=wt();r=wr(),(n=fr())!==a?e=function(e){return{type:"case-expression",format:"searched",clauses:e,else:null}}(t):(Je=e,e=a)}else Je=e,e=a;if(e===a)if(e=Je,pr()!==a){for(wr(),t=[],r=wt();r!==a;)t.push(r),r=wt();r=wr(),(n=It())!==a?(wr(),fr()!==a?e=function(e,t){return{type:"case-expression",format:"searched",clauses:e,else:t.value}}(t,n):(Je=e,e=a)):(Je=e,e=a)}else Je=e,e=a;return e}()),e}())===a&&(e=function(){var e;return(e=function(){var e;return e=function(){var e,t,r,n;if(e=Je,(t=ot())!==a){for(r=[],n=lt();n!==a;)r.push(n),n=lt();e=function(e,t){return e+t.join("")}(t,r)}else Je=e,e=a;return e}(),e}())!==a&&(e=function(e){return/^CURRENT_DATE$/i.test(e)?{type:"current-time",mode:"date"}:/^CURRENT_TIMESTAMP$/i.test(e)?{type:"current-time",mode:"timestamp"}:{type:"column-reference",table:"",column:e}}(e)),e}())===a&&(e=ct())===a&&(e=Je,gr()!==a?(wr(),(t=We())!==a?(wr(),yr()!==a?e=function(e){return e.paren=!0,e}(t):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)),e}function ut(){var e,t,r,n;if(e=Je,(t=ot())!==a){for(r=[],n=it();n!==a;)r.push(n),n=it();e=function(e,t){return e+t.join("")}(t,r)}else Je=e,e=a;return e}function ot(){var e;return l.test(t.charAt(Je))?(e=t.charAt(Je),Je++):(e=a,0===qe&&ze(F)),e}function it(){var e;return c.test(t.charAt(Je))?(e=t.charAt(Je),Je++):(e=a,0===qe&&ze(L)),e}function lt(){var e;return p.test(t.charAt(Je))?(e=t.charAt(Je),Je++):(e=a,0===qe&&ze(M)),e}function ct(){var e,r,n;return e=Je,64===t.charCodeAt(Je)?(r="@",Je++):(r=a,0===qe&&ze(R)),r!==a&&(n=ut())!==a?e=r=[r,n]:(Je=e,e=a),e!==a&&(e=function(e){return{type:"parameter",value:e[1]}}(e)),e}function pt(){var e;return(e=Gt())===a&&(e=Yt())===a&&(e=Wt()),e}function ft(){var e,r,n,s;return e=Je,ar()!==a?(wr(),45===t.charCodeAt(Je)?(r="-",Je++):(r=a,0===qe&&ze(E)),r===a&&(43===t.charCodeAt(Je)?(r="+",Je++):(r=a,0===qe&&ze(A))),r!==a?(wr(),(n=gt())!==a?(wr(),(s=dt())!==a?e=function(e,t,r){return{type:"interval",value:t,qualifier:r,op:e}}(r,n,s):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,ar()!==a?(wr(),(r=gt())!==a?(wr(),(n=dt())!==a?e=function(e,t){return{type:"interval",value:e,qualifier:t,op:""}}(r,n):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)),e}function dt(){var e,t,r;return e=Je,(t=function(){var e,t,r;return e=Je,(t=vt())!==a?(wr(),gr()!==a?(wr(),(r=mt())!==a?(wr(),yr()!==a?e=function(e,t){return{type:"interval-period",period:e.value,precision:t,secondary:null}}(t,r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=vt())!==a&&(t=function(e){return{type:"interval-period",period:e.value,precision:null,secondary:null}}(t)),e=t),e}())!==a?(wr(),nr()!==a?(wr(),(r=function(){var e,t,r,n;return e=Je,(t=vt())!==a&&(t=function(e){return{type:"interval-period",period:e.value,precision:null,secondary:null}}(t)),(e=t)===a&&(e=Je,(t=cr())!==a?(wr(),gr()!==a?(wr(),(r=mt())!==a?(wr(),mr()!==a?(wr(),(n=ht())!==a?(wr(),yr()!==a?e=function(e,t){return{type:"interval-period",period:"second",precision:e,secondary:t}}(r,n):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=cr())!==a?(wr(),gr()!==a?(wr(),(r=mt())!==a?(wr(),yr()!==a?e=function(e){return{type:"interval-period",period:"second",precision:e,secondary:null}}(r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=cr())!==a&&(t={type:"interval-period",period:"second",precision:null,secondary:null}),e=t))),e}())!==a?e=function(e,t){return{type:"interval-qualifier",start:e,end:t}}(t,r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=function(){var e,t,r,n;return e=Je,(t=vt())!==a?(wr(),gr()!==a?(wr(),(r=ht())!==a?(wr(),yr()!==a?e=function(e,t){return{type:"interval-period",period:e.value,precision:t,secondary:null}}(t,r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=vt())!==a&&(t=function(e){return{type:"interval-period",period:e.value,precision:null,secondary:null}}(t)),(e=t)===a&&(e=Je,(t=cr())!==a?(wr(),gr()!==a?(wr(),(r=mt())!==a?(wr(),mr()!==a?(wr(),(n=ht())!==a?(wr(),yr()!==a?e=function(e,t){return{type:"interval-period",period:"second",precision:e,secondary:t}}(r,n):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=cr())!==a?(wr(),gr()!==a?(wr(),(r=ht())!==a?(wr(),yr()!==a?e=function(e){return{type:"interval-period",period:"second",precision:e,secondary:null}}(r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e===a&&(e=Je,(t=cr())!==a&&(t={type:"interval-period",period:"second",precision:null,secondary:null}),e=t)))),e}()),e}function vt(){var e,t;return e=Je,(t=or())!==a&&(t={type:"string",value:"day"}),(e=t)===a&&(e=Je,(t=ir())!==a&&(t={type:"string",value:"hour"}),(e=t)===a&&(e=Je,(t=lr())!==a&&(t={type:"string",value:"minute"}),(e=t)===a&&(e=Je,(t=ur())!==a&&(t={type:"string",value:"month"}),(e=t)===a&&(e=Je,(t=sr())!==a&&(t={type:"string",value:"year"}),e=t)))),e}function ht(){var e;return(e=xt())!==a&&(e=function(e){return parseFloat(e)}(e)),e}function mt(){var e;return(e=xt())!==a&&(e=function(e){return parseFloat(e)}(e)),e}function gt(){var e;return(e=yt())===a&&(e=ct()),e}function yt(){var e,r,n,s,u;if(e=Je,39===t.charCodeAt(Je)?(r="'",Je++):(r=a,0===qe&&ze(D)),r===a&&("N'"===t.substr(Je,2)?(r="N'",Je+=2):(r=a,0===qe&&ze(O))),r!==a){for(n=[],s=Je,t.substr(Je,2)===i?(u=i,Je+=2):(u=a,0===qe&&ze(J)),u!==a&&(u="'"),(s=u)===a&&(f.test(t.charAt(Je))?(s=t.charAt(Je),Je++):(s=a,0===qe&&ze(U)));s!==a;)n.push(s),s=Je,t.substr(Je,2)===i?(u=i,Je+=2):(u=a,0===qe&&ze(J)),u!==a&&(u="'"),(s=u)===a&&(f.test(t.charAt(Je))?(s=t.charAt(Je),Je++):(s=a,0===qe&&ze(U)));39===t.charCodeAt(Je)?(s="'",Je++):(s=a,0===qe&&ze(D)),s!==a?e=function(e){return{type:"string",value:e.join("")}}(n):(Je=e,e=a)}else Je=e,e=a;return e}function wt(){var e,t,r;return e=Je,dr()!==a?(wr(),(t=We())!==a?(wr(),vr()!==a?(wr(),(r=We())!==a?e=function(e,t){return{type:"when-clause",operand:e,value:t}}(t,r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e}function Nt(){var e,t,r;return e=Je,dr()!==a?(wr(),(t=We())!==a?(wr(),vr()!==a?(wr(),(r=We())!==a?e=function(e,t){return{type:"when-clause",operand:e,value:t}}(t,r):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a)):(Je=e,e=a),e}function It(){var e,t;return e=Je,hr()!==a?(wr(),(t=We())!==a?e=function(e){return{type:"else-clause",value:e}}(t):(Je=e,e=a)):(Je=e,e=a),e}function bt(){var e,r,n;return(e=xt())===a&&(e=Je,45===t.charCodeAt(Je)?(r="-",Je++):(r=a,0===qe&&ze(E)),r===a&&(43===t.charCodeAt(Je)?(r="+",Je++):(r=a,0===qe&&ze(A))),r!==a&&(n=xt())!==a?e=function(e,t){return e[0]+t}(r,n):(Je=e,e=a)),e}function Tt(){var e,r,n;return e=Je,46===t.charCodeAt(Je)?(r=".",Je++):(r=a,0===qe&&ze(k)),r!==a?((n=xt())===a&&(n=null),e=function(e){return"."+(null!=e?e:"")}(n)):(Je=e,e=a),e}function St(){var e,t,r;return e=Je,(t=Et())!==a&&(r=xt())!==a?e=function(e,t){return e+t}(t,r):(Je=e,e=a),e}function xt(){var e,t;if(e=[],(t=At())!==a)for(;t!==a;)e.push(t),t=At();else e=a;return e!==a&&(e=function(e){return e.join("")}(e)),e}function At(){var e;return d.test(t.charAt(Je))?(e=t.charAt(Je),Je++):(e=a,0===qe&&ze($)),e}function Et(){var e,r,n;return e=Je,v.test(t.charAt(Je))?(r=t.charAt(Je),Je++):(r=a,0===qe&&ze(q)),r!==a?(h.test(t.charAt(Je))?(n=t.charAt(Je),Je++):(n=a,0===qe&&ze(V)),n===a&&(n=null),e=function(e,t){return"e"+(null===t?"":t)}(0,n)):(Je=e,e=a),e}function Ct(){var e,r,n,s;return e=Je,"null"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(H)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e=r=[r,n]:(Je=e,e=a)):(Je=e,e=a),e}function Pt(){var e,r,n,s;return e=Je,"true"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(B)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e=r=[r,n]:(Je=e,e=a)):(Je=e,e=a),e}function _t(){var e,r,n,s;return e=Je,"false"===t.substr(Je,5).toLowerCase()?(r=t.substr(Je,5),Je+=5):(r=a,0===qe&&ze(j)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e=r=[r,n]:(Je=e,e=a)):(Je=e,e=a),e}function Ft(){var e,r,n,s;return e=Je,"in"===t.substr(Je,2).toLowerCase()?(r=t.substr(Je,2),Je+=2):(r=a,0===qe&&ze(z)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="IN":(Je=e,e=a)):(Je=e,e=a),e}function Lt(){var e,r,n,s;return e=Je,"is"===t.substr(Je,2).toLowerCase()?(r=t.substr(Je,2),Je+=2):(r=a,0===qe&&ze(G)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="IS":(Je=e,e=a)):(Je=e,e=a),e}function Mt(){var e,r,n,s;return e=Je,"like"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(Y)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="LIKE":(Je=e,e=a)):(Je=e,e=a),e}function Rt(){var e,r,n,s;return e=Je,"escape"===t.substr(Je,6).toLowerCase()?(r=t.substr(Je,6),Je+=6):(r=a,0===qe&&ze(W)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="ESCAPE":(Je=e,e=a)):(Je=e,e=a),e}function Dt(){var e,r,n,s;return e=Je,"not"===t.substr(Je,3).toLowerCase()?(r=t.substr(Je,3),Je+=3):(r=a,0===qe&&ze(Z)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="NOT":(Je=e,e=a)):(Je=e,e=a),e}function Ot(){var e,r,n,s;return e=Je,"and"===t.substr(Je,3).toLowerCase()?(r=t.substr(Je,3),Je+=3):(r=a,0===qe&&ze(Q)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="AND":(Je=e,e=a)):(Je=e,e=a),e}function Jt(){var e,r,n,s;return e=Je,"or"===t.substr(Je,2).toLowerCase()?(r=t.substr(Je,2),Je+=2):(r=a,0===qe&&ze(K)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="OR":(Je=e,e=a)):(Je=e,e=a),e}function Ut(){var e,r,n,s;return e=Je,"between"===t.substr(Je,7).toLowerCase()?(r=t.substr(Je,7),Je+=7):(r=a,0===qe&&ze(X)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="BETWEEN":(Je=e,e=a)):(Je=e,e=a),e}function kt(){var e,r,n,s;return e=Je,"from"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(ee)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="FROM":(Je=e,e=a)):(Je=e,e=a),e}function $t(){var e,r,n,s;return e=Je,"for"===t.substr(Je,3).toLowerCase()?(r=t.substr(Je,3),Je+=3):(r=a,0===qe&&ze(te)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="FOR":(Je=e,e=a)):(Je=e,e=a),e}function qt(){var e,r,n,s;return e=Je,"substring"===t.substr(Je,9).toLowerCase()?(r=t.substr(Je,9),Je+=9):(r=a,0===qe&&ze(re)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="SUBSTRING":(Je=e,e=a)):(Je=e,e=a),e}function Vt(){var e,r,n,s;return e=Je,"extract"===t.substr(Je,7).toLowerCase()?(r=t.substr(Je,7),Je+=7):(r=a,0===qe&&ze(ne)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="EXTRACT":(Je=e,e=a)):(Je=e,e=a),e}function Ht(){var e,r,n,s;return e=Je,"trim"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(ae)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="TRIM":(Je=e,e=a)):(Je=e,e=a),e}function Bt(){var e,r,n,s;return e=Je,"position"===t.substr(Je,8).toLowerCase()?(r=t.substr(Je,8),Je+=8):(r=a,0===qe&&ze(se)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="POSITION":(Je=e,e=a)):(Je=e,e=a),e}function jt(){var e,r,n,s;return e=Je,"timestamp"===t.substr(Je,9).toLowerCase()?(r=t.substr(Je,9),Je+=9):(r=a,0===qe&&ze(ue)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="TIMESTAMP":(Je=e,e=a)):(Je=e,e=a),e}function zt(){var e,r,n,s;return e=Je,"date"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(oe)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="DATE":(Je=e,e=a)):(Je=e,e=a),e}function Gt(){var e,r,n,s;return e=Je,"leading"===t.substr(Je,7).toLowerCase()?(r=t.substr(Je,7),Je+=7):(r=a,0===qe&&ze(ie)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="LEADING":(Je=e,e=a)):(Je=e,e=a),e}function Yt(){var e,r,n,s;return e=Je,"trailing"===t.substr(Je,8).toLowerCase()?(r=t.substr(Je,8),Je+=8):(r=a,0===qe&&ze(le)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="TRAILING":(Je=e,e=a)):(Je=e,e=a),e}function Wt(){var e,r,n,s;return e=Je,"both"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(ce)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="BOTH":(Je=e,e=a)):(Je=e,e=a),e}function Zt(){var e,r,n,s;return e=Je,"cast"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(pe)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="CAST":(Je=e,e=a)):(Je=e,e=a),e}function Qt(){var e,r,n,s;return e=Je,"as"===t.substr(Je,2).toLowerCase()?(r=t.substr(Je,2),Je+=2):(r=a,0===qe&&ze(fe)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="AS":(Je=e,e=a)):(Je=e,e=a),e}function Kt(){var e,r,n,s;return e=Je,"integer"===t.substr(Je,7).toLowerCase()?(r=t.substr(Je,7),Je+=7):(r=a,0===qe&&ze(de)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="INTEGER":(Je=e,e=a)):(Je=e,e=a),e}function Xt(){var e,r,n,s;return e=Je,"smallint"===t.substr(Je,8).toLowerCase()?(r=t.substr(Je,8),Je+=8):(r=a,0===qe&&ze(ve)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="SMALLINT":(Je=e,e=a)):(Je=e,e=a),e}function er(){var e,r,n,s;return e=Je,"float"===t.substr(Je,5).toLowerCase()?(r=t.substr(Je,5),Je+=5):(r=a,0===qe&&ze(he)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="FLOAT":(Je=e,e=a)):(Je=e,e=a),e}function tr(){var e,r,n,s;return e=Je,"real"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(me)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="REAL":(Je=e,e=a)):(Je=e,e=a),e}function rr(){var e,r,n,s;return e=Je,"varchar"===t.substr(Je,7).toLowerCase()?(r=t.substr(Je,7),Je+=7):(r=a,0===qe&&ze(ge)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="VARCHAR":(Je=e,e=a)):(Je=e,e=a),e}function nr(){var e,r,n,s;return e=Je,"to"===t.substr(Je,2).toLowerCase()?(r=t.substr(Je,2),Je+=2):(r=a,0===qe&&ze(ye)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="TO":(Je=e,e=a)):(Je=e,e=a),e}function ar(){var e,r,n,s;return e=Je,"interval"===t.substr(Je,8).toLowerCase()?(r=t.substr(Je,8),Je+=8):(r=a,0===qe&&ze(we)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="INTERVAL":(Je=e,e=a)):(Je=e,e=a),e}function sr(){var e,r,n,s;return e=Je,"year"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(Ne)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="YEAR":(Je=e,e=a)):(Je=e,e=a),e}function ur(){var e,r,n,s;return e=Je,"month"===t.substr(Je,5).toLowerCase()?(r=t.substr(Je,5),Je+=5):(r=a,0===qe&&ze(Ie)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="MONTH":(Je=e,e=a)):(Je=e,e=a),e}function or(){var e,r,n,s;return e=Je,"day"===t.substr(Je,3).toLowerCase()?(r=t.substr(Je,3),Je+=3):(r=a,0===qe&&ze(be)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="DAY":(Je=e,e=a)):(Je=e,e=a),e}function ir(){var e,r,n,s;return e=Je,"hour"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(Te)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="HOUR":(Je=e,e=a)):(Je=e,e=a),e}function lr(){var e,r,n,s;return e=Je,"minute"===t.substr(Je,6).toLowerCase()?(r=t.substr(Je,6),Je+=6):(r=a,0===qe&&ze(Se)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="MINUTE":(Je=e,e=a)):(Je=e,e=a),e}function cr(){var e,r,n,s;return e=Je,"second"===t.substr(Je,6).toLowerCase()?(r=t.substr(Je,6),Je+=6):(r=a,0===qe&&ze(xe)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="SECOND":(Je=e,e=a)):(Je=e,e=a),e}function pr(){var e,r,n,s;return e=Je,"case"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(Ae)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="CASE":(Je=e,e=a)):(Je=e,e=a),e}function fr(){var e,r,n,s;return e=Je,"end"===t.substr(Je,3).toLowerCase()?(r=t.substr(Je,3),Je+=3):(r=a,0===qe&&ze(Ee)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="END":(Je=e,e=a)):(Je=e,e=a),e}function dr(){var e,r,n,s;return e=Je,"when"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(Ce)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="WHEN":(Je=e,e=a)):(Je=e,e=a),e}function vr(){var e,r,n,s;return e=Je,"then"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(Pe)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="THEN":(Je=e,e=a)):(Je=e,e=a),e}function hr(){var e,r,n,s;return e=Je,"else"===t.substr(Je,4).toLowerCase()?(r=t.substr(Je,4),Je+=4):(r=a,0===qe&&ze(_e)),r!==a?(n=Je,qe++,s=it(),qe--,s===a?n=void 0:(Je=n,n=a),n!==a?e="ELSE":(Je=e,e=a)):(Je=e,e=a),e}function mr(){var e;return 44===t.charCodeAt(Je)?(e=",",Je++):(e=a,0===qe&&ze(Fe)),e}function gr(){var e;return 40===t.charCodeAt(Je)?(e="(",Je++):(e=a,0===qe&&ze(Le)),e}function yr(){var e;return 41===t.charCodeAt(Je)?(e=")",Je++):(e=a,0===qe&&ze(Me)),e}function wr(){var e,t;for(e=[],t=Nr();t!==a;)e.push(t),t=Nr();return e}function Nr(){var e;return m.test(t.charAt(Je))?(e=t.charAt(Je),Je++):(e=a,0===qe&&ze(Re)),e}function Ir(){var e,r,n,s;if(e=Je,(e=r=ut())===a)if(e=Je,96===t.charCodeAt(Je)?(r="`",Je++):(r=a,0===qe&&ze(De)),r!==a){if(n=[],g.test(t.charAt(Je))?(s=t.charAt(Je),Je++):(s=a,0===qe&&ze(Oe)),s!==a)for(;s!==a;)n.push(s),g.test(t.charAt(Je))?(s=t.charAt(Je),Je++):(s=a,0===qe&&ze(Oe));else n=a;n!==a?(96===t.charCodeAt(Je)?(s="`",Je++):(s=a,0===qe&&ze(De)),s!==a?e=function(e){return e.join("")}(n):(Je=e,e=a)):(Je=e,e=a)}else Je=e,e=a;return e}function br(e,t,r,n){var a={type:"binary-expression",operator:e,left:t,right:r};return void 0!==n&&(a.escape=n),a}function Tr(e,t){for(var r=e,n=0;n<t.length;n++)r=br(t[n][1],r,t[n][3]);return r}if((n=o())!==a&&Je===t.length)return n;throw n!==a&&Je<t.length&&ze({type:"end"}),function(t,r,n){return new e(e.buildMessage(t,r),t,r,n)}($e,ke<t.length?t.charAt(ke):null,ke<t.length?je(ke,ke+1):je(ke,ke))}}},(m={get exports(){return y},set exports(e){y=e}}).exports&&(m.exports=g());class w{static parse(e){return y.parse(e)}}const N=/^(\d{4})-(\d{1,2})-(\d{1,2})$/,I=/^(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2}(\.[0-9]+)?)$/,b=/^(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2}(\.[0-9]+)?)(\+|\-)(\d{1,2}):(\d{1,2})$/,T=/^(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2})(\+|\-)(\d{1,2}):(\d{1,2})$/,S=/^(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2})$/,x=new Set(["current_timestamp","current_date","current_time"]);function A(e,t){return(e+="").length>=t?e:new Array(t-e.length+1).join("0")+e}function E(e,t,r="0",n="0",a="0",s="0",u="",o="0",i="0"){if("+"===u||"-"===u){const l=`${A(parseInt(e,10),4)}-${A(parseInt(t,10),2)}-${A(parseInt(r,10),2)}`;let c="";parseFloat(s)<10&&(c="0");const p=`${A(parseInt(n,10),2)}:${A(parseInt(a,10),2)}:${c+parseFloat(s).toString()}`,f=`${u}${A(parseInt(o,10),2)}:${A(parseInt(i,10),2)}`;return new Date(l+"T"+p+f)}return new Date(parseInt(e,10),parseInt(t,10)-1,parseInt(r,10),parseInt(n,10),parseInt(a,10),parseFloat(s))}class C{static makeBool(e){return L(e)}static featureValue(e,t,r,n){return B(e,t,r,n)}static equalsNull(e){return null===e}static applyLike(e,t,r){return $(e,t,r)}static ensureArray(e){return M(e)}static applyIn(e,t){return J(e,t)}static currentDate(){const e=new Date;return e.setHours(0,0,0,0),e}static makeSqlInterval(e,t,r){return p.createFromValueAndQualifer(e,t,r)}static convertInterval(e){return e instanceof p?e.valueInMilliseconds():e}static currentTimestamp(){return new Date}static compare(e,t,r){return V(e,t,r)}static calculate(e,t,r){return H(e,t,r)}static makeComparable(e){return q(e)}static evaluateFunction(e,t){return f(e,t)}static lookup(e,t){const r=t[e];return void 0===r?null:r}static between(e,t){return null==e||null==t[0]||null==t[1]?null:e>=t[0]&&e<=t[1]}static notbetween(e,t){return null==e||null==t[0]||null==t[1]?null:e<t[0]||e>t[1]}static ternaryNot(e){return R(e)}static ternaryAnd(e,t){return D(e,t)}static ternaryOr(e,t){return O(e,t)}}class P{constructor(e,t){this.fieldsIndex=t,this._datefields={},this.parameters={},this._hasDateFunctions=void 0,this.parseTree=w.parse(e);const{isStandardized:r,isAggregate:n,referencedFieldNames:a}=this._extractExpressionInfo(t);this._referencedFieldNames=a,this.isStandardized=r,this.isAggregate=n}static create(e,t){return new P(e,t)}get fieldNames(){return this._referencedFieldNames}testSet(e,t=j){const r={};for(const n of this.fieldNames)r[n]=e.map((e=>t.getAttribute(e,n)));return!!this._evaluateNode(this.parseTree,{attributes:r},j)}calculateValue(e,t=j){const r=this._evaluateNode(this.parseTree,e,t);return r instanceof p?r.valueInMilliseconds()/864e5:r}calculateValueCompiled(e,t=j){return null!=this.parseTree._compiledVersion?this.parseTree._compiledVersion(e,this.parameters,t,this._datefields):(0,n.Z)("esri-csp-restrictions")?this.calculateValue(e,t):(this._compileMe(),this.parseTree._compiledVersion(e,this.parameters,t,this._datefields))}testFeature(e,t=j){return!!this._evaluateNode(this.parseTree,e,t)}testFeatureCompiled(e,t=j){return null!=this.parseTree._compiledVersion?!!this.parseTree._compiledVersion(e,this.parameters,t,this._datefields):(0,n.Z)("esri-csp-restrictions")?this.testFeature(e,t):(this._compileMe(),!!this.parseTree._compiledVersion(e,this.parameters,t,this._datefields))}get hasDateFunctions(){return null!=this._hasDateFunctions||(this._hasDateFunctions=!1,this._visitAll(this.parseTree,(e=>{"current-time"===e.type?this._hasDateFunctions=!0:"function"===e.type&&(this._hasDateFunctions=this._hasDateFunctions||x.has(e.name.toLowerCase()))}))),this._hasDateFunctions}getFunctions(){const e=new Set;return this._visitAll(this.parseTree,(t=>{"function"===t.type&&e.add(t.name.toLowerCase())})),Array.from(e)}getExpressions(){const e=new Map;return this._visitAll(this.parseTree,(t=>{if("function"===t.type){const r=t.name.toLowerCase(),n=t.args.value[0];if("column-reference"===n.type){const t=n.column,a=`${r}-${t}`;e.has(a)||e.set(a,{aggregateType:r,field:t})}}})),[...e.values()]}getVariables(){const e=new Set;return this._visitAll(this.parseTree,(t=>{"parameter"===t.type&&e.add(t.value.toLowerCase())})),Array.from(e)}_compileMe(){const e="return this.convertInterval("+this.evaluateNodeToJavaScript(this.parseTree)+")";this.parseTree._compiledVersion=new Function("feature","lookups","attributeAdapter","datefields",e).bind(C)}_extractExpressionInfo(e){const t=[],r=new Set;let n=!0,s=!0;return this._visitAll(this.parseTree,(u=>{switch(u.type){case"column-reference":{const n=e?.get(u.column);let a,s;n?a=s=n.name??"":(s=u.column,a=s.toLowerCase()),n&&n.name&&("date"===n.type||"esriFieldTypeDate"===n.type)&&(this._datefields[n.name]=1),r.has(a)||(r.add(a),t.push(s)),u.column=s;break}case"function":{const{name:e,args:t}=u,r=t.value.length;n&&(n=function(e,t){const r=h[e.toLowerCase()];return null!=r&&t>=r.minParams&&t<=r.maxParams}(e,r)),s&&(s=a(e,r));break}}})),{referencedFieldNames:Array.from(t),isStandardized:n,isAggregate:s}}_visitAll(e,t){if(null!=e)switch(t(e),e.type){case"when-clause":this._visitAll(e.operand,t),this._visitAll(e.value,t);break;case"case-expression":for(const r of e.clauses)this._visitAll(r,t);"simple"===e.format&&this._visitAll(e.operand,t),null!==e.else&&this._visitAll(e.else,t);break;case"expression-list":for(const r of e.value)this._visitAll(r,t);break;case"unary-expression":this._visitAll(e.expr,t);break;case"binary-expression":this._visitAll(e.left,t),this._visitAll(e.right,t);break;case"function":this._visitAll(e.args,t)}}evaluateNodeToJavaScript(e){switch(e.type){case"interval":return"this.makeSqlInterval("+this.evaluateNodeToJavaScript(e.value)+", "+JSON.stringify(e.qualifier)+","+JSON.stringify(e.op)+")";case"case-expression":{let t="";if("simple"===e.format){const r="this.makeComparable("+this.evaluateNodeToJavaScript(e.operand)+")";t="( ";for(let n=0;n<e.clauses.length;n++)t+=" ("+r+" === this.makeComparable("+this.evaluateNodeToJavaScript(e.clauses[n].operand)+")) ? ("+this.evaluateNodeToJavaScript(e.clauses[n].value)+") : ";null!==e.else?t+=this.evaluateNodeToJavaScript(e.else):t+="null",t+=" )"}else{t="( ";for(let r=0;r<e.clauses.length;r++)t+=" this.makeBool("+this.evaluateNodeToJavaScript(e.clauses[r].operand)+")===true ? ("+this.evaluateNodeToJavaScript(e.clauses[r].value)+") : ";null!==e.else?t+=this.evaluateNodeToJavaScript(e.else):t+="null",t+=" )"}return t}case"parameter":return"this.lookup("+JSON.stringify(e.value.toLowerCase())+",lookups)";case"expression-list":{let t="[";for(const r of e.value)"["!==t&&(t+=","),t+=this.evaluateNodeToJavaScript(r);return t+="]",t}case"unary-expression":return"this.ternaryNot("+this.evaluateNodeToJavaScript(e.expr)+")";case"binary-expression":switch(e.operator){case"AND":return"this.ternaryAnd("+this.evaluateNodeToJavaScript(e.left)+","+this.evaluateNodeToJavaScript(e.right)+" )";case"OR":return"this.ternaryOr("+this.evaluateNodeToJavaScript(e.left)+","+this.evaluateNodeToJavaScript(e.right)+" )";case"IS":if("null"!==e.right.type)throw new Error("Unsupported RHS for IS");return"this.equalsNull("+this.evaluateNodeToJavaScript(e.left)+")";case"ISNOT":if("null"!==e.right.type)throw new Error("Unsupported RHS for IS");return"(!(this.equalsNull("+this.evaluateNodeToJavaScript(e.left)+")))";case"IN":return"this.applyIn("+this.evaluateNodeToJavaScript(e.left)+",this.ensureArray("+this.evaluateNodeToJavaScript(e.right)+"))";case"NOT IN":return"this.ternaryNot(this.applyIn("+this.evaluateNodeToJavaScript(e.left)+",this.ensureArray("+this.evaluateNodeToJavaScript(e.right)+")))";case"BETWEEN":return"this.between("+this.evaluateNodeToJavaScript(e.left)+","+this.evaluateNodeToJavaScript(e.right)+")";case"NOTBETWEEN":return"this.notbetween("+this.evaluateNodeToJavaScript(e.left)+","+this.evaluateNodeToJavaScript(e.right)+")";case"LIKE":return"this.applyLike("+this.evaluateNodeToJavaScript(e.left)+","+this.evaluateNodeToJavaScript(e.right)+","+JSON.stringify(e.escape)+")";case"NOT LIKE":return"this.ternaryNot(this.applyLike("+this.evaluateNodeToJavaScript(e.left)+","+this.evaluateNodeToJavaScript(e.right)+","+JSON.stringify(e.escape)+"))";case"<>":case"<":case">":case">=":case"<=":case"=":return"this.compare("+JSON.stringify(e.operator)+","+this.evaluateNodeToJavaScript(e.left)+","+this.evaluateNodeToJavaScript(e.right)+")";case"*":case"-":case"+":case"/":case"||":return"this.calculate("+JSON.stringify(e.operator)+","+this.evaluateNodeToJavaScript(e.left)+","+this.evaluateNodeToJavaScript(e.right)+")"}throw new Error("Not Supported Operator "+e.operator);case"null":case"boolean":case"string":case"number":return JSON.stringify(e.value);case"date":return"(new Date("+F(e.value).getTime().toString()+"))";case"timestamp":return"(new Date("+_(e.value).getTime().toString()+"))";case"current-time":return"date"===e.mode?"this.currentDate()":"this.currentTimestamp()";case"column-reference":return"this.featureValue(feature,"+JSON.stringify(e.column)+",datefields,attributeAdapter)";case"function":return"this.evaluateFunction("+JSON.stringify(e.name)+","+this.evaluateNodeToJavaScript(e.args)+")"}throw new Error("Unsupported sql syntax "+e.type)}_evaluateNode(e,t,r){switch(e.type){case"interval":{const n=this._evaluateNode(e.value,t,r);return p.createFromValueAndQualifer(n,e.qualifier,e.op)}case"case-expression":if("simple"===e.format){const n=q(this._evaluateNode(e.operand,t,r));for(let a=0;a<e.clauses.length;a++)if(n===q(this._evaluateNode(e.clauses[a].operand,t,r)))return this._evaluateNode(e.clauses[a].value,t,r);if(null!==e.else)return this._evaluateNode(e.else,t,r)}else{for(let n=0;n<e.clauses.length;n++)if(L(this._evaluateNode(e.clauses[n].operand,t,r)))return this._evaluateNode(e.clauses[n].value,t,r);if(null!==e.else)return this._evaluateNode(e.else,t,r)}return null;case"parameter":return this.parameters[e.value.toLowerCase()];case"expression-list":{const n=[];for(const a of e.value)n.push(this._evaluateNode(a,t,r));return n}case"unary-expression":return R(this._evaluateNode(e.expr,t,r));case"binary-expression":switch(e.operator){case"AND":return D(this._evaluateNode(e.left,t,r),this._evaluateNode(e.right,t,r));case"OR":return O(this._evaluateNode(e.left,t,r),this._evaluateNode(e.right,t,r));case"IS":if("null"!==e.right.type)throw new Error("Unsupported RHS for IS");return null===this._evaluateNode(e.left,t,r);case"ISNOT":if("null"!==e.right.type)throw new Error("Unsupported RHS for IS");return null!==this._evaluateNode(e.left,t,r);case"IN":{const n=M(this._evaluateNode(e.right,t,r));return J(this._evaluateNode(e.left,t,r),n)}case"NOT IN":{const n=M(this._evaluateNode(e.right,t,r));return R(J(this._evaluateNode(e.left,t,r),n))}case"BETWEEN":{const n=this._evaluateNode(e.left,t,r),a=this._evaluateNode(e.right,t,r);return null==n||null==a[0]||null==a[1]?null:n>=q(a[0])&&n<=q(a[1])}case"NOTBETWEEN":{const n=this._evaluateNode(e.left,t,r),a=this._evaluateNode(e.right,t,r);return null==n||null==a[0]||null==a[1]?null:n<q(a[0])||n>q(a[1])}case"LIKE":return $(this._evaluateNode(e.left,t,r),this._evaluateNode(e.right,t,r),e.escape);case"NOT LIKE":return R($(this._evaluateNode(e.left,t,r),this._evaluateNode(e.right,t,r),e.escape));case"<>":case"<":case">":case">=":case"<=":case"=":return V(e.operator,this._evaluateNode(e.left,t,r),this._evaluateNode(e.right,t,r));case"-":case"+":case"*":case"/":case"||":return H(e.operator,this._evaluateNode(e.left,t,r),this._evaluateNode(e.right,t,r))}case"null":case"boolean":case"string":case"number":return e.value;case"date":return F(e.value);case"timestamp":return _(e.value);case"current-time":{const t=new Date;return"date"===e.mode&&t.setHours(0,0,0,0),t}case"column-reference":return B(t,e.column,this._datefields,r);case"data-type":return e.value;case"function":{const n=this._evaluateNode(e.args,t,r);return this.isAggregate?function(e,t){const r=s[e.toLowerCase()];if(null==r)throw new Error("Function Not Recognised");if(t.length<r.minParams||t.length>r.maxParams)throw new Error(`Invalid Parameter count for call to ${e.toUpperCase()}`);return r.evaluate(t)}(e.name,n):f(e.name,n)}}throw new Error("Unsupported sql syntax "+e.type)}}function _(e){let t=I.exec(e);if(null!==t){const[,e,r,n,a,s,u]=t;return E(e,r,n,a,s,u)}if(t=b.exec(e),null!==t){const[,e,r,n,a,s,u,o,i,l]=t;return E(e,r,n,a,s,u,o,i,l)}if(t=T.exec(e),null!==t){const[,e,r,n,a,s,u,o,i]=t;return E(e,r,n,a,s,"0",u,o,i)}if(t=S.exec(e),null!==t){const[,e,r,n,a,s]=t;return E(e,r,n,a,s)}if(t=N.exec(e),null!==t){const[,e,r,n]=t;return E(e,r,n)}throw new Error("SQL Invalid Timestamp")}function F(e){const t=N.exec(e);if(null===t)try{return _(e)}catch{throw new Error("SQL Invalid Date")}const[,r,n,a]=t;return new Date(parseInt(r,10),parseInt(n,10)-1,parseInt(a,10))}function L(e){return!0===e}function M(e){return Array.isArray(e)?e:[e]}function R(e){return null!==e?!0!==e:null}function D(e,t){return null!=e&&null!=t?!0===e&&!0===t:!1!==e&&!1!==t&&null}function O(e,t){return null!=e&&null!=t?!0===e||!0===t:!0===e||!0===t||null}function J(e,t){if(null==e)return null;let r=!1;for(const n of t)if(null==n)r=null;else if(e===n){r=!0;break}return r}const U="-[]/{}()*+?.\\^$|";var k;function $(e,t,r){return null==e?null:function(e,t){const r=t;let n="",a=k.Normal;for(let t=0;t<e.length;t++){const s=e.charAt(t);switch(a){case k.Normal:s===r?a=k.Escaped:U.includes(s)?n+="\\"+s:n+="%"===s?".*":"_"===s?".":s;break;case k.Escaped:U.includes(s)?n+="\\"+s:n+=s,a=k.Normal}}return new RegExp("^"+n+"$","m")}(t,r).test(e)}function q(e){return e instanceof Date?e.valueOf():e}function V(e,t,r){if(null==t||null==r)return null;const n=q(t),a=q(r);switch(e){case"<>":return n!==a;case"=":return n===a;case">":return n>a;case"<":return n<a;case">=":return n>=a;case"<=":return n<=a}}function H(e,t,r){if("||"===e)return f("concat",[t,r]);if(t instanceof p)if(r instanceof Date)switch(e){case"+":return new Date(t.valueInMilliseconds()+r.getTime());case"-":return t.valueInMilliseconds()-r.getTime();case"*":return t.valueInMilliseconds()*r.getTime();case"/":return t.valueInMilliseconds()/r.getTime()}else if(r instanceof p)switch(e){case"+":return p.createFromMilliseconds(t.valueInMilliseconds()+r.valueInMilliseconds());case"-":return p.createFromMilliseconds(t.valueInMilliseconds()-r.valueInMilliseconds());case"*":return t.valueInMilliseconds()*r.valueInMilliseconds();case"/":return t.valueInMilliseconds()/r.valueInMilliseconds()}else t=t.valueInMilliseconds();else if(r instanceof p)if(t instanceof Date)switch(e){case"+":return new Date(r.valueInMilliseconds()+t.getTime());case"-":return new Date(t.getTime()-r.valueInMilliseconds());case"*":return t.getTime()*r.valueInMilliseconds();case"/":return t.getTime()/r.valueInMilliseconds()}else r=r.valueInMilliseconds();else if(t instanceof Date&&"number"==typeof r)switch(r=24*r*60*60*1e3,t=t.getTime(),e){case"+":return new Date(t+r);case"-":return new Date(t-r);case"*":return new Date(t*r);case"/":return new Date(t/r)}else if(r instanceof Date&&"number"==typeof t)switch(t=24*t*60*60*1e3,r=r.getTime(),e){case"+":return new Date(t+r);case"-":return new Date(t-r);case"*":return new Date(t*r);case"/":return new Date(t/r)}switch(e){case"+":return t+r;case"-":return t-r;case"*":return t*r;case"/":return t/r}}function B(e,t,r,n){const a=n.getAttribute(e,t);return null!=a&&1===r[t]?new Date(a):a}!function(e){e[e.Normal=0]="Normal",e[e.Escaped=1]="Escaped"}(k||(k={}));const j={getAttribute:(e,t)=>(function(e){return e&&"object"==typeof e.attributes}(e)?e.attributes:e)[t]}}}]);