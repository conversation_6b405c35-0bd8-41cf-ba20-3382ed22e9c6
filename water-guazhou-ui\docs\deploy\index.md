# 发布部署

## 本地配置修改

### 接口修改

- 在public/config.prod.js中修改对应接口路径

## 打包配置

### 打包到子路径（通过非根路径访问）

- 只是修改了config.js或config.prod.js中的配置则 **不需要** 重新打包
- 部署目录与上一次打包不同时 **需要** 重新打包
- **修改部署目录**
  两个点：
  1. 修改vue.config.js中的publicPath为需要部署到的子目录，比如要部署到sw目录下，则修改为/sw/, **需要以斜杠(/)结尾**

  2. 同时要修改public文件夹中的 **index.html** 中的config.js配置的引用路径，比如修改为/sw/config.js , config.prod.js也如此

## 打包问题汇总

1. 执行yarn mf 时报错

报错'cross-env' 不是内部或外部命令，也不是可运行的程序

安装相关插件 `npm install -g cross-env increase-memory-limit`
