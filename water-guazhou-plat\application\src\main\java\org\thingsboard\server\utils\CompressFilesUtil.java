package org.thingsboard.server.utils;

import org.apache.commons.io.IOUtils;
import org.thingsboard.server.service.utils.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 多文件压缩工具类
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2020-12-02
 */
public class CompressFilesUtil {

    /**
     * 压缩为zip文件
     *
     * @param files 文件信息列表
     * @param FILE_PREFIX    文件路径前缀
     * @param response
     * @return
     */
    public static void compressToZip(List<File> files, String FILE_PREFIX, HttpServletResponse response, String title) {
        // 获取文件路径
        List<String> paths = new ArrayList<>();
        String path = "";
        // 文件列表接收流
        FileInputStream fileInputStream = null;
        // 压缩文件接收流
        FileInputStream fis = null;
        // 压缩文件处理类
        ZipEntry zipEntry = null;
        // 压缩文件对象
        File zipFile = new File(FILE_PREFIX + UUID.randomUUID().toString() + ".zip");
        // 压缩文件输出流
        ZipOutputStream zipOutputStream = null;
        try {
            zipOutputStream = new ZipOutputStream(new FileOutputStream(zipFile));
            // 遍历源文件数组
            for (File file : files) {
                // 将源文件数组中的当前文件读入 FileInputStream 流中
                fileInputStream = new FileInputStream(file);
                // 实例化 ZipEntry 对象，源文件数组中的当前文件
                zipEntry = new ZipEntry(file.getName());
                zipOutputStream.putNextEntry(zipEntry);
                // 该变量记录每次真正读的字节个数
                int len;
                // 定义每次读取的字节数组
                byte[] buffer = new byte[1024];
                while ((len = fileInputStream.read(buffer)) > 0) {
                    zipOutputStream.write(buffer, 0, len);
                }
            }

            // 关闭压缩文件输出流
            if (zipOutputStream != null) {
                try {
                    zipOutputStream.closeEntry();
                    zipOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            // 返回压缩文件给用户
            fis = new FileInputStream(zipFile);
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(title  + ".zip", "utf-8"));
            IOUtils.copy(fis, response.getOutputStream());
            response.flushBuffer();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭压缩文件读取流
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            // 关闭文件读取流
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();

                    // 删除本地生成的压缩文件
                    if (zipFile.exists()) {
                        zipFile.delete();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            // 删除生成的其他文件
            for (File file : files) {
                if (file != null && file.exists()) {
                    file.delete();
                }
            }
        }
    }

    /**
     * 带文件夹压缩
     * @param files
     * @param FILE_PREFIX
     * @param response
     * @param title
     */
    public static void compressToZipWithDir(List<Map<String, Object>> files, String FILE_PREFIX, HttpServletResponse response, String title) {
        // 获取文件路径
        List<String> paths = new ArrayList<>();
        String path = "";
        // 文件列表接收流
        FileInputStream fileInputStream = null;
        // 压缩文件接收流
        FileInputStream fis = null;
        // 压缩文件处理类
        ZipEntry zipEntry = null;
        // 压缩文件对象
        File zipFile = new File(FILE_PREFIX + UUID.randomUUID().toString() + ".zip");
        // 压缩文件输出流
        ZipOutputStream zipOutputStream = null;
        try {
            zipOutputStream = new ZipOutputStream(new FileOutputStream(zipFile));
            // 遍历源文件数组
            for (Map<String, Object> fileMap : files) {
                String dir = (String) fileMap.get("dir");
                File file = (File) fileMap.get("file");
                // 将源文件数组中的当前文件读入 FileInputStream 流中
                fileInputStream = new FileInputStream(file);
                // 实例化 ZipEntry 对象，源文件数组中的当前文件
                if (StringUtils.checkNotNull(dir)) {
                    zipEntry = new ZipEntry(dir + File.separator + file.getName());
                } else {
                    zipEntry = new ZipEntry(file.getName());
                }

                zipOutputStream.putNextEntry(zipEntry);
                // 该变量记录每次真正读的字节个数
                int len;
                // 定义每次读取的字节数组
                byte[] buffer = new byte[1024];
                while ((len = fileInputStream.read(buffer)) > 0) {
                    zipOutputStream.write(buffer, 0, len);
                }
            }

            // 关闭压缩文件输出流
            if (zipOutputStream != null) {
                try {
                    zipOutputStream.closeEntry();
                    zipOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            // 返回压缩文件给用户
            fis = new FileInputStream(zipFile);
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(title  + ".zip", "utf-8"));
            IOUtils.copy(fis, response.getOutputStream());
            response.flushBuffer();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭压缩文件读取流
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            // 关闭文件读取流
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();

                    // 删除本地生成的压缩文件
                    if (zipFile.exists()) {
                        zipFile.delete();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            // 删除生成的其他文件
            for (Map<String, Object> map : files) {
                File file = (File) map.get("file");
                if (file != null && file.exists()) {
                    file.delete();
                }
            }
        }
    }
}
