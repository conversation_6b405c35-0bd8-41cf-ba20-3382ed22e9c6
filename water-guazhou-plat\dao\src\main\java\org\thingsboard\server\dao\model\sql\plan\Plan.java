package org.thingsboard.server.dao.model.sql.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class Plan {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 计划名称
    private String name;

    // 执行人ID
    @ParseUsername(withDepartment = true)
    private String executionUserId;

    // 开始时间
    private Date startTime;

    // 结束时间
    private Date endTime;

    // 执行天数
    private Integer executionDays;

    // 间隔天数
    private Integer intervalDays;

    // 执行次数
    private Integer executionNum;

    // 盘点类型
    private String executionType;

    // 盘点目标仓库ID
    private String storehouseId;

    // 盘点目标仓库名称
    private String storehouseName;

    // 备注
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
