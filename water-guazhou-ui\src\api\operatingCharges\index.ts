// 营收-基本接口
import request from '@/plugins/axios';
import { traverse } from '@/utils/GlobalHelper';

/**
 * 营收下拉列表接口-标准代码
 * @param mainCode
 * @returns
 */
export const getSelectOptions = async (
  mainCode:
    | 'MeterCopyAttribute' // 抄表册属性
    | 'MeterCopyEventType' // 抄表事件类型
    | 'RangeTypeCode' // 阶梯水价层次
    | 'RangeFlag' // 阶梯水价标识
    | 'AddPriceType' // 附加价分类
    | 'SignPriceType' // 代征价分类
    | 'WaterTypeCode' // 用水类别
    | 'PriceVersionType' // 水价版本分类
    | 'WaterCategoryType' // 用水类别/水价类型
    | 'IndustryCategoryType' // 行业类别
    | 'UserType' // 用户类别
    | 'PaymentMethod' // 结算方式
    | 'PayType' // 缴费方式
    | 'BillType' // 票据类型
    | 'TransferCode' // 转供户标识
    | 'TemporaryWaterCode' // 临时用水标识
    | 'ContactType' // 联系人类型
    | 'BankType' // 银行分类名称 // 开户行
    | 'UserStatusType' // 用户状态
    | 'CalculationMethod' // 水量计算方式
    | 'LostType' // 漏损计算方式
    | 'SpecialUserType' // 特殊户类型
    | 'CertTypeCode' // 证件类型
    | 'MeterType' // 水表类型
    | 'MeterBrand' // 水表厂家
    | 'MeterCaliber' // 水表口径
    | 'MeterPositionType' // 安装位置
    | 'MeterInstallEnvironment' // 安装环境
    | 'ExpandingType' // 业扩报装类别
    | 'EditType' // 维护类型
    | 'ChangeType' // 收费类型
    | 'MsgTemplateType' // 短信类型
) => {
  const value = await request({
    url: `/api/revenue/sysCode/detailList?mainCode=${mainCode}`,
    method: 'get'
  });
  return traverse(value.data.data[mainCode].details || [], 'children', {
    label: 'name',
    value: 'key'
  });
};

/**
 * 查询所有的抄表册
 * @param params
 * @returns
 */
export const getMeterBookAll = async (
  params: {
    orgId?: string;
    label?: string;
    value?: string;
  } = { label: 'name', value: 'id' }
) => {
  const value = await request({
    url: `/api/revenue/meterBook/all`,
    method: 'GET',
    params
  });
  return traverse(value.data.data || [], 'children', {
    label: params.label || 'name',
    value: params.value || 'id'
  });
};
