package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionCustMeter;
import org.thingsboard.server.dao.smartPipe.PartitionCustMeterService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 智慧管网-分区户表信息
 */
@RestController
@RequestMapping("api/spp/dma/partition/custMeter")
public class PartitionCustMeterController extends BaseController {

    @Autowired
    private PartitionCustMeterService partitionCustMeterService;

    @PostMapping
    public IstarResponse save(@RequestBody PartitionCustMeter partitionCustMeter) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionCustMeter.setTenantId(tenantId);

        return IstarResponse.ok(partitionCustMeterService.save(partitionCustMeter));
    }

    @GetMapping("list")
    public IstarResponse getList(PartitionMountRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(partitionCustMeterService.getList(request));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        partitionCustMeterService.delete(ids);
        return IstarResponse.ok("删除成功");
    }
}
