import{_ as P}from"./TreeBox-DDD2iwoR.js";import{_ as N}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as E}from"./CardTable-rdWOL4_6.js";import{_ as R}from"./CardSearch-CB_HNR-Q.js";import{_ as O}from"./index-BJ-QPYom.js";import{d as M,M as q,c as h,a8 as c,s as F,r as u,bF as W,x as S,ar as I,a9 as d,bT as C,D,a1 as B,o as J,g as Y,h as z,F as T,q as m,i as f,b7 as $}from"./index-r0dFAfgr.js";import{I as y}from"./common-CvK_P_ao.js";import{r as G,s as H,c as Q}from"./equipmentManage-DuoY00aj.js";import{p as X,b as K,c as Z,d as w,e as ee,a as te}from"./ledgerManagement-CkhtRd8m.js";import{a as ae}from"./equipmentOutStock-BiNkB8x8.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const be=M({__name:"index",setup(le){const{$btnPerms:L}=q(),_=h(),v=h(),x=h(),k=h({filters:[{label:"标签编码",field:"deviceLabelCode",type:"input"},{label:"设备名称",field:"name",type:"input"},{label:"设备型号",field:"model",type:"input"},{label:"安装区域",field:"areaId",type:"select-tree",checkStrictly:!0,options:c(()=>r.installationArea)},{label:"安装位置",field:"address",type:"input"},{label:"供应商",field:"supplierId",type:"select-tree",checkStrictly:!0,options:c(()=>r.SupplierList)},{label:"所属项目",field:"projectId",type:"select-tree",checkStrictly:!0,options:c(()=>r.ConstructionProject)},{label:"仓库编号",field:"storehouseCode",type:"input"},{label:"仓库名称",field:"storehouseName",type:"input"},{label:"货架编号",field:"shelfCode",type:"input"},{label:"货架名称",field:"shelfName",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:y.QUERY,click:()=>o()},{type:"default",perm:!0,text:"重置",svgIcon:F($),click:()=>{var e;(e=x.value)==null||e.resetForm(),o()}},{perm:!0,text:"导出",icon:y.EXPORT,click:()=>o(!0)}]}]}),n=u({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"供应商",prop:"supplierName"},{label:"报废日期",prop:"scrappedTime",formatter:e=>e.scrappedTime?W(e.scrappedTime).format("YYYY-MM-DD"):""},{label:"施工项目",prop:"projectName"},{label:"所在仓库",prop:"storehouseName"}],operationWidth:"200px",operations:[{type:"primary",text:"安装信息",icon:y.DETAIL,perm:L("RoleManageEdit"),click:e=>U(e)},{type:"primary",text:"使用信息",perm:L("RoleManageEdit"),icon:y.DETAIL,click:e=>j(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{n.pagination.page=e,n.pagination.limit=t,o()}}}),g=u({title:"安装信息",labelWidth:"120px",dialogWidth:"700px",submit:e=>{let t="添加成功";e.id&&(t="修改成功"),X(e).then(()=>{var a;o(),S.success(t),(a=_.value)==null||a.closeDialog()})},defaultValue:{},group:[{fields:[{xl:12,type:"select-tree",label:"接收部门",field:"installUserDepartmentId",checkStrictly:!0,defaultExpandAll:!0,rules:[{required:!0,message:"请选择接收部门"}],autoFillOptions:e=>{I(2).then(t=>{e.options=d(t.data.data||[])})}},{xl:12,type:"select",label:"安装人员",field:"installUserId",options:[],setOptionBy:"installUserDepartmentId",setOptionMethod:(e,t)=>{t.installUserDepartmentId&&C({pid:t.installUserDepartmentId}).then(a=>{const l=a.data.data.data||[];e.options=l.map(i=>({label:i.firstName,value:D(i.id.id)}))})}},{xl:12,type:"select-tree",label:"所属项目",field:"projectId",checkStrictly:!0,options:c(()=>B(r.ConstructionProject,{label:"name",value:"id",children:"children",id:"id"}))},{xl:12,type:"date",label:"安装时间",field:"installTime",format:"x"},{xl:12,type:"select-tree",label:"安装区域",field:"installAddressId",rules:[{required:!0,message:"请选择安装区域"}],checkStrictly:!0,options:c(()=>r.installationArea)},{xl:24,type:"textarea",label:"安装位置",field:"address",rules:[{required:!0,message:"请输入安装位置"}]},{xl:24,type:"textarea",label:"备注/说明",field:"remark"}]}]}),b=u({title:"使用信息",labelWidth:"120px",dialogWidth:"500px",submit:e=>{let t="添加成功";e.id&&(t="修改成功"),K(e).then(()=>{var a;o(),S.success(t),(a=v.value)==null||a.closeDialog()})},defaultValue:{},group:[{fields:[{type:"select-tree",label:"使用部门",field:"departmentId",rules:[{required:!0,message:"请选择使用部门"}],checkStrictly:!0,defaultExpandAll:!0,autoFillOptions:e=>{I(2).then(t=>{e.options=d(t.data.data||[])})}},{type:"select",label:"使用人员",field:"userId",rules:[{required:!0,message:"请选择使用人员"}],options:[],setOptionBy:"departmentId",setOptionMethod:(e,t)=>{t.departmentId&&C({pid:t.departmentId}).then(a=>{const l=a.data.data.data||[];e.options=l.map(i=>({label:i.firstName,value:D(i.id.id)}))})}},{type:"date",label:"领用时间",field:"receiveTime",format:"x"},{type:"textarea",label:"备注/说明",field:"remark"}]}]}),p=u({title:"",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{p.currentProject=e,o()}}),U=e=>{g.title="安装信息",g.defaultValue={deviceLabelCode:e.deviceLabelCode},Z({deviceLabelCode:e.deviceLabelCode}).then(t=>{var l;const a=t.data.data.data||[{}];g.defaultValue={deviceLabelCode:e.deviceLabelCode,...a[0]},(l=_.value)==null||l.openDialog()})},j=e=>{b.title="使用信息",b.defaultValue={deviceLabelCode:e.deviceLabelCode},w({deviceLabelCode:e.deviceLabelCode}).then(t=>{var l;const a=t.data.data.data||[{}];b.defaultValue={deviceLabelCode:e.deviceLabelCode,...a[0]},(l=v.value)==null||l.openDialog()})},r=u({UserList:[],SupplierList:[],ConstructionProject:[],installationArea:[],getUserListValue:e=>{C({pid:e}).then(t=>{const a=t.data.data.data||[];r.UserList=a.map(l=>({label:l.firstName,value:D(l.id.id)}))})},getSupplierValue:()=>{G({page:1,size:99999}).then(t=>{r.SupplierList=d(t.data.data.data||[])})},getConstructionProjectValue:()=>{ae({page:1,size:99999}).then(t=>{r.ConstructionProject=d(t.data.data.data||[])})},getAreaTreeValue:()=>{H({page:1,size:99999,shortName:""}).then(t=>{r.installationArea=d(t.data.data.data||[])})},init:()=>{r.getSupplierValue(),r.getConstructionProjectValue(),r.getAreaTreeValue()}}),o=async e=>{var a;const t={size:n.pagination.limit,page:n.pagination.page,deviceTypeId:p.currentProject.id,scrapped:!0,...((a=x.value)==null?void 0:a.queryParams)||{}};if(e){ee(t).then(l=>{const i=window.URL.createObjectURL(l.data);console.log(i);const s=document.createElement("a");s.style.display="none",s.href=i,s.setAttribute("download","设备标签.xlsx"),document.body.appendChild(s),s.click()});return}te(t).then(l=>{n.dataList=l.data.data.data||[],n.pagination.total=l.data.data.total||0})};function V(){Q().then(e=>{p.data=d(e.data.data||[]),p.currentProject=e.data.data[0],o()})}return J(async()=>{V(),r.init()}),(e,t)=>{const a=O,l=R,i=E,s=N,A=P;return Y(),z(A,null,{tree:T(()=>[m(a,{"tree-data":f(p)},null,8,["tree-data"])]),default:T(()=>[m(l,{ref_key:"refSearch",ref:x,config:f(k)},null,8,["config"]),m(i,{config:f(n),class:"card-table"},null,8,["config"]),m(s,{ref_key:"InstallDialogRef",ref:_,config:f(g)},null,8,["config"]),m(s,{ref_key:"UseDialogRef",ref:v,config:f(b)},null,8,["config"])]),_:1})}}});export{be as default};
