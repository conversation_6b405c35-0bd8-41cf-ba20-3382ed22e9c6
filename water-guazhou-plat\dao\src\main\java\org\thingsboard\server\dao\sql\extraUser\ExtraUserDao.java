package org.thingsboard.server.dao.sql.extraUser;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.ExtraUser;

import javax.transaction.Transactional;
import java.util.List;

/**
 * (ExtraUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-09 11:30:35
 */
@Service
public class ExtraUserDao {

    @Autowired
    private ExtraUserRepository extraUserRepository;


    public ExtraUser save(ExtraUser extraUser) {
        return extraUserRepository.save(extraUser);
    }

    public List<ExtraUser> findByTenant(String tenantId) {
        return extraUserRepository.findByTenantIdOrderByUpdateTime(tenantId);
    }

    @Transactional
    public boolean delete(String id) {
        extraUserRepository.delete(id);
        return true;
    }

    @Transactional
    public boolean deleteAll(List<String> ids) {
        ids.forEach(id -> {
            extraUserRepository.delete(id);
        });
        return true;
    }

    public Integer checkName(String name, String tenantId) {
        return extraUserRepository.countByTenantIdAndAndName(tenantId, name);
    }


}