import { hexToRgba } from '@/utils/GlobalHelper'

export const colors = ['#49de35', '#4acffb', '#f44c04', '#A524DE', '#FF7F40']
export const titles = ['PH', '温度', '余氯', '浊度', '电导率']

export const initLineChartOption = (xData:string[], data: any[], color?: string, title?: string, mk?:number): any => {
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      top: 5,
      bottom: 15,
      left: 40,
      right: 40
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisTick: {
        show: false
      },
      axisLabel: {
        margin: 0
      },
      axisLine: {
        show: false
      },
      splitLine: {
        show: false
      },
      data: xData || []
    },
    yAxis: {
      type: 'value',
      splitNumber: 3,
      boundaryGap: false,
      axisLine: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: hexToRgba('#094055', 0.4)
        }
      }
    },
    series:
      {
        data: data || [],
        type: 'line',
        name: title,
        showSymbol: false,
        smooth: true,
        color,
        lineStyle: {
          colorBy: 'seriese',
          shadowColor: color
        },
        markLine: {
          symbol: 'none', // 去掉警戒线最后面的箭头
          label: {
            show: false,
            position: 'start' // 将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
          },
          data: [
            {
              silent: false, // 鼠标悬停事件  true没有，false有
              lineStyle: {
                // 警戒线的样式  ，虚实  颜色
                color
              },
              yAxis: mk || 0 // 警戒线的标注值，可以有多个yAxis,多条警示线   或者采用   {type : 'average', name: '平均值'}，type值有  max  min  average，分为最大，最小，平均值
            }]
        }
      }
  }
  return option
}
// 摸扎数据
export const getList = (index: number): string[] => {
  const data: string[] = []
  const basic = [7.7, 15, 0.2, 2, 1]
  for (let i = 0; i < 7; i++) {
    const a = Math.random() * basic[index] * 0.4 + basic[index]
    data.push(a.toFixed(2))
  }
  return data
}

// 监测点分析
export const JianCeDianFenXiTableData: any[] = [
  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  },

  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  },

  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  },

  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  },

  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  },

  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  },

  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  },

  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  },

  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  },

  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  },
  {
    date: '16:20:28',
    name: '唯亭三区',
    value: 5.77
  },
  {
    date: '16:20:28',
    name: '星湖街/独墅湖大道',
    value: 4.88
  },
  {
    date: '16:20:28',
    name: '华兴街西/城阳湖大道北',
    value: 6.35
  }
]
