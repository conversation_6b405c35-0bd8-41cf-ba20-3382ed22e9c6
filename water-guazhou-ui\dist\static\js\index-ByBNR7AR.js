import{_ as w}from"./TreeBox-DDD2iwoR.js";import{d as E,M as T,c as m,s as v,r as f,x as s,S as I,o as L,g as j,h as F,F as g,q as c,i as p,a9 as M,b6 as R,b7 as V}from"./index-r0dFAfgr.js";import{_ as q}from"./CardTable-rdWOL4_6.js";import{_ as N}from"./CardSearch-CB_HNR-Q.js";import{_ as B}from"./index-BJ-QPYom.js";import{I as u}from"./common-CvK_P_ao.js";import{h as $,i as A,j as G}from"./emergencyDispatch-BKfr3jS6.js";import{c as H}from"./zhandian-YaGuQZe6.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const te=E({__name:"index",setup(O){const{$btnPerms:_}=T(),l=m(),d=m(),b=m({filters:[{label:"预案名称",field:"name",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",icon:u.QUERY,click:()=>i()},{type:"default",perm:!0,text:"重置",svgIcon:v(V),click:()=>{var e;(e=d.value)==null||e.resetForm(),i()}},{perm:!0,text:"新建",icon:u.ADD,type:"success",click:()=>h()}]}]}),r=f({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"预案名称",prop:"name"},{label:"所属点位",prop:"stationName"},{label:"注意事项",prop:"remark"},{label:"处理步骤",prop:"content",isHtml:!0}],operationWidth:"240px",operations:[{type:"primary",text:"编辑",icon:u.EDIT,perm:_("RoleManageEdit"),click:e=>x(e)},{type:"danger",text:"删除",perm:_("RoleManageDelete"),icon:u.DELETE,click:e=>y(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,i()}}}),n=f({title:"新增",width:"800px",labelWidth:"100px",submitting:!1,submit:e=>{n.submitting=!0;let t="新增成功";e.id&&(t="修改成功"),e.stationId=o.currentProject.id,$(e).then(()=>{var a;n.submitting=!1,(a=l.value)==null||a.closeDrawer(),s.success(t),i()}).catch(a=>{n.submitting=!1,s.warning(a)})},defaultValue:{},group:[{fields:[{type:"input",label:"预案名称",field:"name",rules:[{required:!0,message:"请输入预案名称"}]},{type:"input",label:"注意事项",field:"remark"},{type:"wangeditor",label:"处理步骤",field:"content",rules:[{required:!0,message:"请输入预案名称"}]}]}]}),o=f({title:" ",data:[],currentProject:{},isFilterTree:!0,treeNodeHandleClick:e=>{o.currentProject=e,i()}}),h=()=>{var e;n.title="新增",n.defaultValue={},(e=l.value)==null||e.openDrawer()},x=e=>{var t;n.title="编辑",n.defaultValue={category:e.parentId,...e||{}},(t=l.value)==null||t.openDrawer()},y=e=>{I("确定删除该预案","删除提示").then(()=>{A(e.id).then(()=>{s.success("删除成功"),i()}).catch(t=>{s.error(t.toString())})})};function k(){H().then(e=>{o.data=M(e.data.data||[]),o.currentProject=e.data.data[0]})}const i=async()=>{var t;const e={size:r.pagination.limit,page:r.pagination.page,stationId:o.currentProject.id,...((t=d.value)==null?void 0:t.queryParams)||{}};G(e).then(a=>{r.dataList=a.data.data.data||[],r.pagination.total=a.data.data.total||0})};return L(async()=>{k()}),(e,t)=>{const a=B,D=N,C=q,S=R,P=w;return j(),F(P,null,{tree:g(()=>[c(a,{"tree-data":p(o)},null,8,["tree-data"])]),default:g(()=>[c(D,{ref_key:"refSearch",ref:d,config:p(b)},null,8,["config"]),c(C,{class:"card-table",config:p(r)},null,8,["config"]),c(S,{ref_key:"refForm",ref:l,config:p(n)},null,8,["config"])]),_:1})}}});export{te as default};
