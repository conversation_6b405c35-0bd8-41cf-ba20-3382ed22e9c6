import{d as T,a8 as $,r as j,c as k,am as b,g,h as F,F as d,p as i,q as o,G as _,n as z,aJ as G,aB as K,x,H as M,I as q,bU as H,bW as W,K as Q,aK as X,aL as Y,N as Z,J as ee,O as ae,L as le,C as oe}from"./index-r0dFAfgr.js";import{p as te}from"./pumpRoomInfo-DV75B9MO.js";const se={class:"scheme-dialog"},de={class:"pump-config-section"},me={class:"add-pump-btn"},ne={class:"scheme-description"},ie={class:"description-item"},pe={class:"description-item"},re={class:"dialog-footer"},ue=T({__name:"EditSchemeDialog",props:{visible:{type:Boolean,default:!1},pumpRoomId:{type:String,default:""},schemeData:{type:Object,default:()=>({})}},emits:["update:visible","confirm"],setup(E,{emit:R}){const m=E,w=R,v=$({get:()=>m.visible,set:a=>w("update:visible",a)}),t=j({id:"",schemeName:"",schemeCode:"",schemeDescription:"",schemeRemark:""}),u=k([]),r=k([{pumpName:"",fixedPower:"",fixedFlow:"",pumpDisplayName:""}]),V=async()=>{if(!m.pumpRoomId){u.value=[];return}try{const a=await te({page:1,size:1e3,pumpRoomId:m.pumpRoomId});console.log("泵机接口返回数据:",a);let e=[];a&&a.data&&a.data.code===200&&(a.data.data&&Array.isArray(a.data.data.data)?e=a.data.data.data:a.data.data&&Array.isArray(a.data.data.records)&&(e=a.data.data.records)),u.value=e.map(l=>({label:`${l.name} (${l.code})`,value:l.id,data:l})),console.log("解析后的泵机选项:",u.value)}catch(a){console.error("获取泵机列表失败:",a),x.error("获取泵机列表失败"),u.value=[]}},U=(a,e)=>{if(e){const l=u.value.find(p=>p.value===e);if(l&&l.data){if(l.data.performanceParameters)try{const p=JSON.parse(l.data.performanceParameters);a.fixedPower=p.power||"",a.fixedFlow=p.flow||""}catch(p){console.log("解析性能参数失败:",p)}a.pumpDisplayName=l.label}}else a.fixedPower="",a.fixedFlow="",a.pumpDisplayName=""},I=()=>{r.value.push({pumpName:"",fixedPower:"",fixedFlow:"",pumpDisplayName:""})},S=a=>{r.value.length>1&&r.value.splice(a,1)},y=()=>{if(m.schemeData&&Object.keys(m.schemeData).length>0){t.id=m.schemeData.id||"",t.schemeName=m.schemeData.schemeName||"",t.schemeCode=m.schemeData.schemeCode||"",t.schemeDescription=m.schemeData.schemeDescription||"",t.schemeRemark=m.schemeData.schemeRemark||"";try{const a=m.schemeData.pumpGroupConfig||"[]",e=JSON.parse(a);e.length>0&&(r.value=e.map(l=>({pumpName:l.pumpName||"",fixedPower:l.fixedPower||"",fixedFlow:l.fixedFlow||"",pumpDisplayName:l.pumpDisplayName||""})))}catch(a){console.error("解析泵组配置失败:",a)}}},N=()=>{v.value=!1,D()},B=()=>{if(!t.schemeName.trim()){x.warning("请输入方案名称");return}const a=r.value.filter(l=>l.pumpName&&l.fixedPower&&l.fixedFlow);if(a.length===0){x.warning("请至少配置一个泵组");return}const e={...t,pumpConfigs:a};console.log("提交编辑数据:",e),w("confirm",e),D()},D=()=>{t.id="",t.schemeName="",t.schemeCode="",t.schemeDescription="",t.schemeRemark="",r.value=[{pumpName:"",fixedPower:"",fixedFlow:"",pumpDisplayName:""}]};return b(()=>m.visible,a=>{a&&(y(),V())}),b(()=>m.pumpRoomId,a=>{a&&m.visible&&V()}),b(()=>m.schemeData,()=>{m.visible&&y()},{deep:!0}),(a,e)=>{const l=M,p=q,c=H,C=W,O=Q,P=X,L=Y,f=Z,h=ee,A=ae,J=le;return g(),F(J,{modelValue:v.value,"onUpdate:modelValue":e[4]||(e[4]=s=>v.value=s),title:"编辑泵组方案",width:"800px","before-close":N},{footer:d(()=>[i("div",re,[o(h,{onClick:N},{default:d(()=>e[10]||(e[10]=[_("取消")])),_:1}),o(h,{type:"primary",onClick:B},{default:d(()=>e[11]||(e[11]=[_("保存")])),_:1})])]),default:d(()=>[i("div",se,[o(O,{model:t,"label-width":"100px"},{default:d(()=>[o(C,{gutter:20},{default:d(()=>[o(c,{span:12},{default:d(()=>[o(p,{label:"方案名称:"},{default:d(()=>[o(l,{modelValue:t.schemeName,"onUpdate:modelValue":e[0]||(e[0]=s=>t.schemeName=s),placeholder:"请输入..."},null,8,["modelValue"])]),_:1})]),_:1}),o(c,{span:12},{default:d(()=>[o(p,{label:"方案编码:"},{default:d(()=>[o(l,{modelValue:t.schemeCode,"onUpdate:modelValue":e[1]||(e[1]=s=>t.schemeCode=s),readonly:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),i("div",de,[e[7]||(e[7]=i("div",{class:"section-title"},"泵组配置",-1)),o(A,{data:r.value,border:"",style:{width:"100%"}},{default:d(()=>[o(f,{prop:"pumpName",label:"启用水泵",width:"200"},{default:d(({row:s})=>[o(L,{modelValue:s.pumpName,"onUpdate:modelValue":n=>s.pumpName=n,placeholder:"请选择泵机",onChange:n=>U(s,n)},{default:d(()=>[o(P,{label:"请选择",value:""}),(g(!0),z(K,null,G(u.value,n=>(g(),F(P,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),o(f,{prop:"fixedPower",label:"额定功率",width:"150"},{default:d(({row:s})=>[o(l,{modelValue:s.fixedPower,"onUpdate:modelValue":n=>s.fixedPower=n,placeholder:"请输入..."},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(f,{prop:"fixedFlow",label:"额定流量",width:"150"},{default:d(({row:s})=>[o(l,{modelValue:s.fixedFlow,"onUpdate:modelValue":n=>s.fixedFlow=n,placeholder:"请输入..."},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(f,{label:"操作",width:"100"},{default:d(({$index:s})=>[o(h,{type:"primary",link:"",size:"small",onClick:n=>S(s)},{default:d(()=>e[5]||(e[5]=[_("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),i("div",me,[o(h,{type:"primary",onClick:I},{default:d(()=>e[6]||(e[6]=[_("添加水泵")])),_:1})])]),i("div",ne,[o(C,{gutter:20},{default:d(()=>[o(c,{span:12},{default:d(()=>[i("div",ie,[e[8]||(e[8]=i("label",null,"方案描述:",-1)),o(l,{modelValue:t.schemeDescription,"onUpdate:modelValue":e[2]||(e[2]=s=>t.schemeDescription=s),type:"textarea",rows:3,placeholder:"通过实时水泵运行计算组团"},null,8,["modelValue"])])]),_:1}),o(c,{span:12},{default:d(()=>[i("div",pe,[e[9]||(e[9]=i("label",null,"方案备注:",-1)),o(l,{modelValue:t.schemeRemark,"onUpdate:modelValue":e[3]||(e[3]=s=>t.schemeRemark=s),type:"textarea",rows:3,placeholder:"通过实时水泵运行计算组团"},null,8,["modelValue"])])]),_:1})]),_:1})])])]),_:1},8,["modelValue"])}}}),he=oe(ue,[["__scopeId","data-v-6ca21f4e"]]);export{he as default};
