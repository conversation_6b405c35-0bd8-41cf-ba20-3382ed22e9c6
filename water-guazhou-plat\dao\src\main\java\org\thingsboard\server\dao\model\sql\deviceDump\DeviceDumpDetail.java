package org.thingsboard.server.dao.model.sql.deviceDump;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse;
import org.thingsboard.server.dao.util.imodel.response.annotations.Flatten;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
public class DeviceDumpDetail {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 主表ID
    private String mainId;

    // 设备标签码
    private String deviceLabelCode;

    // 是否已报废
    private Boolean isDumped;

    // 租户ID
    @ParseTenantName
    private String tenantId;

    @Flatten
    @TableField(exist = false)
    private DeviceInfoResponse deviceInfoResponse;

}
