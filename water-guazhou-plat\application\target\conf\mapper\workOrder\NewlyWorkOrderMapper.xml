<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.workOrder.NewlyWorkOrderMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           serial_no,
                           title,
                           source,
                           organizer_id,
                           level,
                           type,
                           address,
                           remark,
                           video_url,
                           audio_url,
                           img_url,
                           other_file_url,
                           upload_user_id,
                           ifnull(upload_phone,
                                  (select phone from tb_user u where u.id = organizer_id)) as upload_phone,
                           upload_no,
                           upload_address,
                           is_direct_dispatch,
                           process_user_id,
                           receive_department_id,
                           process_level,
                           process_level_label,
                           estimated_finish_time,
                           complete_time,
                           status,
                           step_process_user_id,
                           coordinate,
                           coordinate_name,
                           create_time,
                           update_time,
                           project_id,
                           (select name from construction_project where id = project_id)   as project_name,
                           tenant_id,
                           cc_user_id,
                           user_resolve_multi_id(cc_user_id)                               as cc_user_name,
                           parent_id
        <!--@sql from work_order-->
    </sql>

    <sql id="Children_Column_List">
        <!--@sql select -->main.id,
                           main.serial_no,
                           main.title,
                           main.source,
                           main.organizer_id,
                           main.level,
                           main.type,
                           main.address,
                           main.remark,
                           main.video_url,
                           main.audio_url,
                           main.img_url,
                           main.other_file_url,
                           main.upload_user_id,
                           ifnull(main.upload_phone,
                                  (select phone from tb_user u where u.id = main.organizer_id)) as upload_phone,
                           main.upload_no,
                           main.upload_address,
                           main.is_direct_dispatch,
                           main.process_user_id,
                           main.receive_department_id,
                           main.process_level,
                           main.process_level_label,
                           main.estimated_finish_time,
                           main.complete_time,
                           main.status,
                           main.step_process_user_id,
                           main.create_time,
                           main.update_time,
                           main.project_id,
                           main.tenant_id,
                           main.cc_user_id,
                           main.parent_id,
        <include refid="org.thingsboard.server.dao.sql.workOrder.WorkOrderDetailMapper.Base_Column_List"/>
        <!--@sql from work_order main, work_order_details detail -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.workOrder.WorkOrder">
        <result column="id" property="id"/>
        <result column="serial_no" property="serialNo"/>
        <result column="title" property="title"/>
        <result column="source" property="source"/>
        <result column="organizer_id" property="organizerId"/>
        <result column="level" property="level"/>
        <result column="type" property="type"/>
        <result column="address" property="address"/>
        <result column="remark" property="remark"/>
        <result column="video_url" property="videoUrl"/>
        <result column="audio_url" property="audioUrl"/>
        <result column="img_url" property="imgUrl"/>
        <result column="other_file_url" property="otherFileUrl"/>
        <result column="upload_user_id" property="uploadUserId"/>
        <result column="upload_phone" property="uploadPhone"/>
        <result column="upload_no" property="uploadNo"/>
        <result column="upload_address" property="uploadAddress"/>
        <result column="is_direct_dispatch" property="isDirectDispatch"/>
        <result column="process_user_id" property="processUserId"/>
        <result column="receive_department_id" property="receiveDepartmentId"/>
        <result column="process_level" property="processLevel"/>
        <result column="process_level_label" property="processLevelLabel"/>
        <result column="estimated_finish_time" property="estimatedFinishTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="status" property="status"/>
        <result column="step_process_user_id" property="stepProcessUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="coordinate" property="coordinate"/>
        <result column="coordinate_name" property="coordinateName"/>
        <result column="project_id" property="projectId"/>
        <result column="project_name" property="projectName"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="cc_user_id" property="ccUserId"/>
        <result column="cc_user_name" property="ccUserName"/>
        <result column="parent_id" property="parentId"/>
    </resultMap>
    <select id="getNameById" resultType="java.lang.String">
        select title
        from work_order
        where id = #{id}
    </select>

    <select id="getPrevNextProcessUserId" resultType="java.lang.String">
        select next_process_user_id
        from (select next_process_user_id, row_number() over (order by process_time desc) rn
              from work_order_details
              where main_id = #{orderId}) t
        where rn = #{order}
    </select>

    <select id="getPrevProcessUserId" resultType="java.lang.String">
        select process_user_id
        from (select process_user_id, row_number() over (order by process_time desc) rn
              from work_order_details
              where main_id = #{orderId}) t
        where rn = #{order}
    </select>

    <select id="countByTypeTimed" resultType="org.thingsboard.server.dao.model.sql.statistic.StatisticLong">
        select w.type      as key,
               count(type) as value
        from work_order w
        where create_time >= #{from}
          and create_time &lt;= #{to}
          and w.tenant_id = #{tenantId}
        group by w.type
    </select>

    <select id="countByOrganizerTimed" resultType="org.thingsboard.server.dao.model.sql.statistic.StatisticLong">
        select u.first_name          as key,
               count(w.organizer_id) as value
        from tb_user u,
             work_order w
        where u.id = w.organizer_id
          and w.create_time >= #{from}
          and w.create_time &lt;= #{to}
          and w.tenant_id = #{tenantId}
        group by w.organizer_id, u.first_name
    </select>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from work_order
        <where>
            <if test="req.keyword != null and req.keyword != ''">
                and (
                        serial_no like '%' || #{req.keyword} || '%'
                    or title like '%' || #{req.keyword} || '%'
                    or remark like '%' || #{req.keyword} || '%'
                    or upload_address like '%' || #{req.keyword} || '%'
                    or upload_phone like '%' || #{req.keyword} || '%'
                )
            </if>
            <if test="req.deviceTypeId != null and req.deviceTypeId != ''">
                and work_order_is_relative_to_fault_report_with_device_type_id(id
                    , #{req.deviceTypeId})
            </if>
            <if test="req.deviceSerial != null and req.deviceSerial != ''">
                and work_order_is_relative_to_fault_report_with_device_serial_id_lk(id
                    , #{req.deviceSerial})
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                and work_order_is_relative_to_fault_report_with_device_name_like(id
                    , #{req.deviceName})
            </if>
            <if test="req.serialNo != null and req.serialNo != ''">
                and serial_no like '%' || #{req.serialNo} || '%'
            </if>
            <if test="req.title != null and req.title != ''">
                and title like '%' || #{req.title} || '%'
            </if>
            <if test="req.source != null and req.source != ''">
                and source = #{req.source}
            </if>
            <if test="req.organizerId != null and req.organizerId != ''">
                and organizer_id = #{req.organizerId}
            </if>
            <if test="req.level != null and req.level != ''">
                and level = #{req.level}
            </if>
            <if test="req.type != null and req.type != ''">
                and type = #{req.type}
            </if>
            <if test="req.address != null and req.address != ''">
                and address = #{req.address}
            </if>
            <if test="req.remark != null and req.remark != ''">
                and remark like '%' || #{req.remark} || '%'
            </if>
            <if test="req.uploadUserId != null and req.uploadUserId != ''">
                and upload_user_id = #{req.uploadUserId}
            </if>
            <if test="req.uploadPhone != null and req.uploadPhone != ''">
                and upload_phone = #{req.uploadPhone}
            </if>
            <if test="req.uploadNo != null and req.uploadNo != ''">
                and upload_no = #{req.uploadNo}
            </if>
            <if test="req.uploadAddress != null and req.uploadAddress != ''">
                and upload_address = #{req.uploadAddress}
            </if>
            <if test="req.processUserId != null and req.processUserId != ''">
                and process_user_id = #{req.processUserId}
            </if>
            <if test="req.status != null and req.status != ''">
                <choose>
                    <when test="req.status == 'CHARGEBACK'">
                        <!--@formatter:off-->
                        and (select count(1) > 0
                        from work_order_details
                        where process_time > (select max(process_time) from work_order_details where type = 'CHARGEBACK' and main_id = work_order.id))
                        <!--@formatter:on-->
                    </when>
                    <otherwise>
                        and status = #{req.status}
                    </otherwise>
                </choose>
            </if>
            <if test="req.stepProcessUserId != null and req.stepProcessUserId != ''">
                and step_process_user_id = #{req.stepProcessUserId}
            </if>
            <if test="req.fromTime != null">
                and create_time >= #{req.fromTime}
            </if>
            <if test="req.toTime != null">
                and create_time &lt;= #{req.toTime}
            </if>
            <if test="req.ccUserId != null and req.ccUserId != ''">
                and cc_user_id like '%' || #{req.ccUserId} || '%'
            </if>
            <if test="req.ownerId != null and req.ownerId != ''">
                and id in
                    (select distinct main_id from work_order_details where next_process_user_id = #{req.ownerId})
            </if>
            <if test="stages != null">
                and status in ( <foreach collection="stages" item="element" separator=",">
                #{element}
            </foreach>)
            </if>
            and tenant_id = #{req.tenantId}
        </where>
        order by create_time desc
        <if test="size > 0">
            offset #{offset} limit #{size}
        </if>
    </select>

    <select id="countByPageRequest" resultType="long">
        select count(1)
        from work_order
        <where>
            <if test="req.keyword != null and req.keyword != ''">
                and (
                        serial_no like '%' || #{req.keyword} || '%'
                    or title like '%' || #{req.keyword} || '%'
                    or remark like '%' || #{req.keyword} || '%'
                    or upload_address like '%' || #{req.keyword} || '%'
                    or upload_phone like '%' || #{req.keyword} || '%'
                )
            </if>
            <if test="req.deviceTypeId != null and req.deviceTypeId != ''">
                and work_order_is_relative_to_fault_report_with_device_type_id(id
                    , #{req.deviceTypeId})
            </if>
            <if test="req.deviceSerial != null and req.deviceSerial != ''">
                and work_order_is_relative_to_fault_report_with_device_serial_id_lk(id
                    , #{req.deviceSerial})
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                and work_order_is_relative_to_fault_report_with_device_name_like(id
                    , #{req.deviceName})
            </if>
            <if test="req.serialNo != null and req.serialNo != ''">
                and serial_no like '%' || #{req.serialNo} || '%'
            </if>
            <if test="req.title != null and req.title != ''">
                and title like '%' || #{req.title} || '%'
            </if>
            <if test="req.source != null and req.source != ''">
                and source = #{req.source}
            </if>
            <if test="req.organizerId != null and req.organizerId != ''">
                and organizer_id = #{req.organizerId}
            </if>
            <if test="req.level != null and req.level != ''">
                and level = #{req.level}
            </if>
            <if test="req.type != null and req.type != ''">
                and type = #{req.type}
            </if>
            <if test="req.address != null and req.address != ''">
                and address = #{req.address}
            </if>
            <if test="req.remark != null and req.remark != ''">
                and remark like '%' || #{req.remark} || '%'
            </if>
            <if test="req.uploadUserId != null and req.uploadUserId != ''">
                and upload_user_id = #{req.uploadUserId}
            </if>
            <if test="req.uploadPhone != null and req.uploadPhone != ''">
                and upload_phone = #{req.uploadPhone}
            </if>
            <if test="req.uploadNo != null and req.uploadNo != ''">
                and upload_no = #{req.uploadNo}
            </if>
            <if test="req.uploadAddress != null and req.uploadAddress != ''">
                and upload_address = #{req.uploadAddress}
            </if>
            <if test="req.processUserId != null and req.processUserId != ''">
                and process_user_id = #{req.processUserId}
            </if>
            <if test="req.status != null and req.status != ''">
                <if test="req.status != null and req.status != ''">
                    <choose>
                        <when test="req.status == 'CHARGEBACK'">
                            <!--@formatter:off-->
                            and (select count(1) > 0
                            from work_order_details
                            where process_time > (select min(process_time) from work_order_details where type = 'CHARGEBACK' and main_id = work_order.id))
                            <!--@formatter:on-->
                        </when>
                        <otherwise>
                            and status = #{req.status}
                        </otherwise>
                    </choose>
                </if>
            </if>
            <if test="req.stepProcessUserId != null and req.stepProcessUserId != ''">
                and step_process_user_id = #{req.stepProcessUserId}
            </if>
            <if test="req.fromTime != null">
                and create_time >= #{req.fromTime}
            </if>
            <if test="req.toTime != null">
                and create_time &lt;= #{req.toTime}
            </if>
            <if test="req.ccUserId != null and req.ccUserId != ''">
                and cc_user_id like '%' || #{req.ccUserId} || '%'
            </if>
            <if test="req.ownerId != null and req.ownerId != ''">
                and id in
                    (select distinct main_id from work_order_details where next_process_user_id = #{req.ownerId})
            </if>
            <if test="stages != null">
                and status in ( <foreach collection="stages" item="element" separator=",">
                #{element}
            </foreach>)
            </if>
            and tenant_id = #{req.tenantId}
        </where>
    </select>

    <select id="findById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from work_order main
        where main.id = #{id}
    </select>

    <resultMap id="faultCountMap" type="java.util.LinkedHashMap">
        <!--suppress s, i -->
        <result column="name" property="name"/>
        <!--suppress s, i -->
        <result column="totalNum" property="totalNum"/>
        <!--suppress s, i -->
        <result column="completeNum" property="completeNum"/>
        <!--suppress s, i -->
        <result column="notCompleteNum" property="notCompleteNum"/>
        <!--suppress s, i -->
        <result column="yiban" property="yiban"/>
        <!--suppress s, i -->
        <result column="jinji" property="jinji"/>
        <!--suppress s, i -->
        <result column="veryJinji" property="veryJinji"/>
    </resultMap>

    <select id="faultCount" resultMap="faultCountMap">
        select tu.first_name as name,
               count(wo1.id) as totalNum,
               count(wo2.id) as completeNum,
               count(wo3.id) as notCompleteNum,
               count(wo4.id) as yiban,
               count(wo5.id) as jinji,
               count(wo6.id) as veryJinji

        from tb_user tu
            left join work_order wo1 on tu.id = wo1.step_process_user_id and wo1.type = '维修' and wo1.source = '设备资产'
        <if test="level != null and level != ''">
            and wo1.level = #{level}
        </if>
        <if test="start != null">
            and wo1.create_time &gt;= #{start}
        </if>
        <if test="end != null">
            and wo1.complete_time &lt;= #{end}
        </if>
        left join work_order wo2
                  on tu.id = wo2.step_process_user_id and wo2.status = 'APPROVED' and wo1.id = wo2.id
        left join work_order wo3
                  on tu.id = wo3.step_process_user_id and wo3.status != 'APPROVED' and wo1.id = wo3.id
        left join work_order wo4 on tu.id = wo4.step_process_user_id and wo1.id = wo4.id and wo4."level" = '一般'
        left join work_order wo5 on tu.id = wo5.step_process_user_id and wo1.id = wo5.id and wo5."level" = '紧急'
        left join work_order wo6
                  on tu.id = wo6.step_process_user_id and wo1.id = wo6.id and wo6."level" = '非常紧急'
        where tu.tenant_id = #{tenantId}
        GROUP BY tu.id, tu.first_name
        order by totalNum desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="faultCountCount" resultType="int">
        select count(*) from (select count(*)

        from tb_user tu
            left join work_order wo1 on tu.id = wo1.step_process_user_id
        <if test="level != null and level != ''">
            and wo1.level = #{level}
        </if>
        <if test="start != null">
            and wo1.create_time &gt;= #{start}
        </if>
        <if test="end != null">
            and wo1.complete_time &lt;= #{end}
        </if>
        <if test="source != null and source != ''">
            and wo1.source = #{source}
        </if>
        left join work_order wo2
                  on tu.id = wo2.step_process_user_id and wo2.status = 'APPROVED' and wo1.id = wo2.id
        left join work_order wo3
                  on tu.id = wo3.step_process_user_id and wo3.status != 'APPROVED' and wo1.id = wo3.id
        left join work_order wo4 on tu.id = wo4.step_process_user_id and wo1.id = wo4.id and wo4."level" = '一般'
        left join work_order wo5 on tu.id = wo5.step_process_user_id and wo1.id = wo5.id and wo5."level" = '紧急'
        left join work_order wo6
                  on tu.id = wo6.step_process_user_id and wo1.id = wo6.id and wo6."level" = '非常紧急'
        where tu.tenant_id = #{tenantId}
        GROUP BY tu.id, tu.first_name) a
    </select>

    <select id="workTimeCount" resultType="java.util.Map">
        select tu.first_name                                                                           as name,
               coalesce(sum(floor(extract(epoch from (wo1.complete_time - tdfr.start_time)) / 60)), 0) as times
        from tb_user tu
            left join work_order wo1
                      on tu.id = wo1.step_process_user_id and wo1.complete_time is not null and wo1.type = '维修' and
                         wo1.source = '设备资产'
            left join tb_device_fault_report tdfr on wo1.id = tdfr.work_order_id and tdfr.start_time is not null
        <if test="start != null">
            and wo1.create_time &gt;= #{start}
        </if>
        <if test="end != null">
            and wo1.complete_time &lt;= #{end}
        </if>
        <if test="source != null and source != ''">
            and wo1.source = #{source}
        </if>
        where tu.tenant_id = #{tenantId}
        GROUP BY tu.id, tu.first_name
        order by times desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="workTimeCountCount" resultType="int">
        select count(*) from (select count(*)
        from tb_user tu
            left join work_order wo1 on tu.id = wo1.step_process_user_id and wo1.complete_time is not null
            left join tb_device_fault_report tdfr on wo1.id = tdfr.work_order_id and tdfr.start_time is not null
        <if test="start != null">
            and wo1.create_time &gt;= #{start}
        </if>
        <if test="end != null">
            and wo1.complete_time &lt;= #{end}
        </if>
        <if test="source != null and source != ''">
            and wo1.source = #{source}
        </if>
        where tu.tenant_id = #{tenantId}
        GROUP BY tu.id, tu.first_name) a
    </select>


    <select id="countByTypeTimedLimitedByProcessingUser"
            resultType="org.thingsboard.server.dao.model.sql.statistic.StatisticLong">
        select w.type      as key,
               count(type) as value
        from work_order w
        where create_time >= #{fromTime}
          and create_time &lt;= #{toTime}
          and w.tenant_id = #{tenantId}
        <if test="processUserId != null and processUserId != ''">
            and id in
            (select distinct main_id
             from work_order_details detail
            where detail.process_user_id = #{processUserId}
              and detail.type in
            <foreach collection="onProcessStage" item="element" open="(" close=")" separator=",">
                #{element}
            </foreach>
            )
        </if>
        group by w.type
    </select>

    <select id="countBetweenStage" resultType="int">
        select count(1)
        from work_order
        <where>
            <if test="fromTime != null">
                create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
            <if test="activeStageSet != null and activeStageSet.size() != 0">
                and status in
                <foreach collection="activeStageSet" item="element" open="(" close=")" separator=",">
                    #{element}
                </foreach>
            </if>
            <if test="processUserId != null and processUserId != ''">
                and step_process_user_id = #{processUserId}
            </if>
            <!--            <if test="activeStageSet != null">-->
            <!--                and status in-->
            <!--                <foreach collection="activeStageSet" item="element" open="(" close=")" separator=",">-->
            <!--                    #{element}-->
            <!--                </foreach>-->
            <!--            </if>-->
            <!--            <if test="processUserId != null and processUserId != ''">-->
            <!--                and id in-->
            <!--                (-->
            <!--                select distinct main_id-->
            <!--                from work_order_details detail-->
            <!--                where-->
            <!--                                detail.process_user_id = #{processUserId}-->
            <!--                <if test="activeStageSet != null and activeStageSet.size() != 0">-->
            <!--                    and detail.type in-->
            <!--                    <foreach collection="activeStageSet" item="element" open="(" close=")" separator=",">-->
            <!--                        #{element}-->
            <!--                    </foreach>-->
            <!--                </if>-->
            <!--                )-->
            <!--            </if>-->
        </where>
    </select>

    <select id="getAssignProcessUser" resultType="java.lang.String">
        select process_user_id
        from work_order_details
        where main_id = #{orderId}
        order by process_time desc
        limit 1
    </select>

    <select id="getPrevProcessUseStage" resultType="java.lang.String">
        select type
        from (select type, row_number() over (order by process_time desc) rn
              from work_order_details
              where main_id = #{orderId}) t
        where rn = #{order}
    </select>

    <select id="totalStatusOfUser" resultType="java.lang.Integer">
        select count(1)
        from work_order
        where step_process_user_id = #{userId}
          and status in
        <foreach collection="status" item="element" open="(" close=")" separator=",">
            #{element}
        </foreach>
    </select>

    <select id="totalOfUser" resultType="java.lang.Integer">
        select count(1)
        from work_order
        where step_process_user_id = #{userId}
    </select>

    <insert id="save">
        INSERT INTO work_order(id,
                               serial_no,
                               title,
                               source,
                               organizer_id,
                               level,
                               type,
                               address,
                               remark,
                               video_url,
                               audio_url,
                               img_url,
                               other_file_url,
                               upload_user_id,
                               upload_phone,
                               upload_no,
                               upload_address,
                               is_direct_dispatch,
                               process_user_id,
                               receive_department_id,
                               process_level,
                               estimated_finish_time,
                               complete_time,
                               status,
                               step_process_user_id,
                               create_time,
                               update_time,
                               project_id,
                               tenant_id,
                               cc_user_id,
                               parent_id,
                               coordinate,
                               coordinate_name)
        VALUES (#{id},
                #{serialNo},
                #{title},
                #{source},
                #{organizerId},
                #{level},
                #{type},
                #{address},
                #{remark},
                #{videoUrl},
                #{audioUrl},
                #{imgUrl},
                #{otherFileUrl},
                #{uploadUserId},
                #{uploadPhone},
                #{uploadNo},
                #{uploadAddress},
                #{isDirectDispatch},
                #{processUserId},
                #{receiveDepartmentId},
                #{processLevel},
                #{estimatedFinishTime},
                #{completeTime},
                #{status},
                #{stepProcessUserId},
                #{createTime},
                #{updateTime},
                #{projectId},
                #{tenantId},
                #{ccUserId},
                #{parentId},
                #{coordinate},
                #{coordinateName})
    </insert>

    <select id="canSubmit" resultType="boolean">
        <bind name="completeStage" value="@org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStage@COMPLETED"/>
        select count(1) = 0
        from work_order
        where parent_id = #{id}
          and status not in
        <foreach item="item" index="index" collection="completeStage.statusSet" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="onTimeCompleteStatistic" resultType="java.lang.String">
        with completeInfo as (select count(case when complete_time is not null then 1 end) complete_count,
                                     count(case
                                               when complete_time is not null and complete_time &lt;= estimated_finish_time
                                                   then 1 end)                             on_time_complete_count
                              from work_order
                              where tenant_id = #{tenantId})
        select '{"completeCount":' || complete_count || ',"onTimeCompleteCount":' || on_time_complete_count ||
               ',"onTimeCompleteRate":' ||
               round(on_time_complete_count * 1.0 / complete_count, 2) || '}'
        from completeInfo
    </select>

    <select id="byTimeStatistic" resultType="java.lang.String">
        with result as (select type typeName, count(1) amount
                        from work_order
                        where tenant_id = #{tenantId}
                        group by type)
        select '{' || rtrim(string_agg('"' || typeName || '":' || amount, ','), ',') || '}'
        from result
    </select>

    <select id="getVisitList" resultType="org.thingsboard.server.dao.model.DTO.WorkOrderVisitMsgDTO">
        select
        a.id,
        a.serial_no,
        a.title,
        a.source,
        a.organizer_id,
        a.level,
        a.type,
        a.address,
        a.remark,
        a.video_url,
        a.audio_url,
        a.img_url,
        a.other_file_url,
        a.upload_user_id,
        ifnull(upload_phone,
        (select phone from tb_user u where u.id = organizer_id)) as upload_phone,
        a.upload_no,
        a.upload_address,
        a.is_direct_dispatch,
        a.process_user_id,
        a.receive_department_id,
        a.process_level,
        a.estimated_finish_time,
        a.complete_time,
        a.status,
        a.step_process_user_id,
        a.coordinate,
        a.coordinate_name,
        a.create_time,
        a.update_time,
        a.project_id,
        (select name from construction_project where id = project_id)   as project_name,
        a.tenant_id,
        a.cc_user_id,
        user_resolve_multi_id(a.cc_user_id)                               as cc_user_name,
        a.parent_id,
        assign.process_remark as assignRemark,
        resolving.process_remark as resolvingRemark,
        arriving.process_remark as arrivingRemark,
        submit.process_remark as submitRemark,
        approved.process_remark as approvedRemark,
        back.send_time as sendTime,
               back.send_status as sendStatus,
               back.visit_time as visitTime,
               back.remark as sendRemark,
               coalesce(back.content, '4') as evaluateNum,
               case when coalesce(back.content, '4') = '1' then '满意'
                    when coalesce(back.content, '4') = '2' then '不满意'
                    when coalesce(back.content, '4') = '3' then '问题未处理'
                    when coalesce(back.content, '4') = '4' then '未评价'
               end as evaluateName,
               coalesce(back.send_status, '3') as sendStatus,
               case when coalesce(back.content, '3') = '1' then '发送成功'
                    when coalesce(back.content, '3') = '2' then '发送失败'
                    when coalesce(back.content, '3') = '3' then '未发送'
               end as sendStatusName
        from work_order a
        left join work_order_details assign on a.id = assign.main_id and assign.type = 'ASSIGN'
        left join work_order_details resolving on a.id = resolving.main_id and assign.type = 'RESOLVING'
        left join work_order_details arriving on a.id = arriving.main_id and assign.type = 'ARRIVING'
        left join work_order_details submit on a.id = submit.main_id and assign.type = 'SUBMIT'
        left join work_order_details approved on a.id = approved.main_id and assign.type = 'APPROVED'
        left join tb_msg_visit_back back on a.id = back.work_order_id
        <where>
            <if test="req.keyword != null and req.keyword != ''">
                and (
                a.serial_no like '%' || #{req.keyword} || '%'
                or a.title like '%' || #{req.keyword} || '%'
                or a.remark like '%' || #{req.keyword} || '%'
                or a.upload_address like '%' || #{req.keyword} || '%'
                or a.upload_phone like '%' || #{req.keyword} || '%'
                )
            </if>
            <if test="req.deviceTypeId != null and req.deviceTypeId != ''">
                and work_order_is_relative_to_fault_report_with_device_type_id(a.id
                , #{req.deviceTypeId})
            </if>
            <if test="req.deviceSerial != null and req.deviceSerial != ''">
                and work_order_is_relative_to_fault_report_with_device_serial_id_lk(a.id
                , #{req.deviceSerial})
            </if>
            <if test="req.deviceName != null and req.deviceName != ''">
                and work_order_is_relative_to_fault_report_with_device_name_like(a.id
                , #{req.deviceName})
            </if>
            <if test="req.serialNo != null and req.serialNo != ''">
                and a.serial_no like '%' || #{req.serialNo} || '%'
            </if>
            <if test="req.title != null and req.title != ''">
                and a.title like '%' || #{req.title} || '%'
            </if>
            <if test="req.source != null and req.source != ''">
                and a.source = #{req.source}
            </if>
            <if test="req.organizerId != null and req.organizerId != ''">
                and a.organizer_id = #{req.organizerId}
            </if>
            <if test="req.level != null and req.level != ''">
                and a.level = #{req.level}
            </if>
            <if test="req.type != null and req.type != ''">
                and a.type = #{req.type}
            </if>
            <if test="req.address != null and req.address != ''">
                and a.address = #{req.address}
            </if>
            <if test="req.remark != null and req.remark != ''">
                and a.remark like '%' || #{req.remark} || '%'
            </if>
            <if test="req.uploadUserId != null and req.uploadUserId != ''">
                and a.upload_user_id = #{req.uploadUserId}
            </if>
            <if test="req.uploadPhone != null and req.uploadPhone != ''">
                and a.upload_phone = #{req.uploadPhone}
            </if>
            <if test="req.uploadNo != null and req.uploadNo != ''">
                and a.upload_no = #{req.uploadNo}
            </if>
            <if test="req.uploadAddress != null and req.uploadAddress != ''">
                and a.upload_address = #{req.uploadAddress}
            </if>
            <if test="req.processUserId != null and req.processUserId != ''">
                and a.process_user_id = #{req.processUserId}
            </if>
            <if test="req.status != null and req.status != ''">
                <choose>
                    <when test="req.status == 'CHARGEBACK'">
                        <!--@formatter:off-->
                        and (select count(1) > 0
                        from work_order_details\
                        where a.process_time > (select max(process_time) from work_order_details where type = 'CHARGEBACK' and main_id = a.id))
                        <!--@formatter:on-->
                    </when>
                    <otherwise>
                        and a.status = #{req.status}
                    </otherwise>
                </choose>
            </if>
            <if test="req.stepProcessUserId != null and req.stepProcessUserId != ''">
                and a.step_process_user_id = #{req.stepProcessUserId}
            </if>
            <if test="req.fromTime != null">
                and a.create_time >= #{req.fromTime}
            </if>
            <if test="req.toTime != null">
                and a.create_time &lt;= #{req.toTime}
            </if>
            <if test="req.ccUserId != null and req.ccUserId != ''">
                and a.cc_user_id like '%' || #{req.ccUserId} || '%'
            </if>
            <if test="req.evaluate != null and req.evaluate != ''">
                <choose>
                    <when test="req.evaluate != '4'.toString()">
                        and back.content = #{req.evaluate}
                    </when>
                    <otherwise>
                        and (back.content is null or back.content = '')
                    </otherwise>
                </choose>
            </if>
            <if test="req.sendStatus != null and req.sendStatus != ''">
                <choose>
                    <when test="req.sendStatus != '3'.toString()">
                        and back.send_status = #{req.sendStatus}
                    </when>
                    <otherwise>
                        and (back.send_status is null or back.send_status = '')
                    </otherwise>
                </choose>
            </if>
            <if test="req.ownerId != null and req.ownerId != ''">
                and a.id in
                (select distinct main_id from work_order_details where next_process_user_id = #{req.ownerId})
            </if>
            and a.tenant_id = #{req.tenantId}
        </where>
        order by a.create_time desc
    </select>
</mapper>