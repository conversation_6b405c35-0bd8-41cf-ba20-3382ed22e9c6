<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BasePipeLineConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BasePipeLineConfiguration" id="BasePipeLineConfigurationResult">
        <result property="id"    column="id"    />
        <result property="pipeId"    column="pipe_id"    />
        <result property="rule"    column="rule"    />
        <result property="alarmThreshold"    column="alarm_threshold"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectBasePipeLineConfigurationVo">
        select id, pipe_id, rule, alarm_threshold, remark from base_pipe_line_configuration
    </sql>

    <select id="selectBasePipeLineConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BasePipeLineConfiguration" resultMap="BasePipeLineConfigurationResult">
        <include refid="selectBasePipeLineConfigurationVo"/>
        <where>  
            <if test="pipeId != null  and pipeId != ''"> and pipe_id = #{pipeId}</if>
            <if test="rule != null  and rule != ''"> and rule = #{rule}</if>
            <if test="alarmThreshold != null  and alarmThreshold != ''"> and alarm_threshold = #{alarmThreshold}</if>
        </where>
    </select>
    
    <select id="selectBasePipeLineConfigurationById" parameterType="String" resultMap="BasePipeLineConfigurationResult">
        <include refid="selectBasePipeLineConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBasePipeLineConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BasePipeLineConfiguration">
        insert into base_pipe_line_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pipeId != null">pipe_id,</if>
            <if test="rule != null">rule,</if>
            <if test="alarmThreshold != null">alarm_threshold,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pipeId != null">#{pipeId},</if>
            <if test="rule != null">#{rule},</if>
            <if test="alarmThreshold != null">#{alarmThreshold},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateBasePipeLineConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BasePipeLineConfiguration">
        update base_pipe_line_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="pipeId != null">pipe_id = #{pipeId},</if>
            <if test="rule != null">rule = #{rule},</if>
            <if test="alarmThreshold != null">alarm_threshold = #{alarmThreshold},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBasePipeLineConfigurationById" parameterType="String">
        delete from base_pipe_line_configuration where id = #{id}
    </delete>

    <delete id="deleteBasePipeLineConfigurationByIds" parameterType="String">
        delete from base_pipe_line_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>