package org.thingsboard.server.dao.area;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.AreaEntity;
import org.thingsboard.server.dao.sql.area.AreaMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class AreaServiceImpl implements AreaService {

    @Autowired
    private AreaMapper areaMapper;

    @Override
    public List getTree() {
        // 查找所有区域
        List<AreaEntity> areaEntityList = areaMapper.selectByMap(new HashMap<>());

        // 获取根节点
        List<AreaEntity> rootList = areaEntityList.stream().filter(a -> StringUtils.isBlank(a.getParentId())).collect(Collectors.toList());

        return rootList;
    }

    @Override
    public PageData getTree(String name, String shortName, int page, int size, String tenantId) {
        QueryWrapper<AreaEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        // queryWrapper.and(warrper -> warrper.like("name", name).or().like("short_name", shortName));
        queryWrapper.orderByAsc("order_num").orderByDesc("create_time");
        // 查找所有区域
        List<AreaEntity> areaEntityList = areaMapper.selectList(queryWrapper);

        // 获取根节点
        List<AreaEntity> rootList = areaEntityList.stream().filter(a -> StringUtils.isBlank(a.getParentId())).collect(Collectors.toList());


        // 构建树
        for (AreaEntity areaEntity : rootList) {
            this.buildTree(areaEntity, areaEntityList);
        }

        // 过滤
        rootList = this.containsName(rootList, name, shortName);

        return new PageData(rootList.size(), rootList.stream().skip((page - 1) * size).limit(size).collect(Collectors.toList()));
    }

    @Override
    public AreaEntity save(AreaEntity areaEntity) {
        areaEntity.setUpdateTime(new Date());
        if (StringUtils.isBlank(areaEntity.getSerialId())) {
            areaEntity.setSerialId("QY" + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));
        }
        if (StringUtils.isBlank(areaEntity.getId())) {
            areaEntity.setCreateTime(new Date());
            areaMapper.insert(areaEntity);
        } else {
            areaMapper.updateById(areaEntity);
        }
        return areaEntity;
    }

    @Override
    public String delete(List<String> ids) {
        areaMapper.deleteBatchIds(ids);
        return "删除成功";
    }

    @Override
    public IstarResponse bindImg(AreaEntity areaEntity) {
        if (StringUtils.isBlank(areaEntity.getId())) {
            return IstarResponse.error("id不能为空");
        }
        int i = areaMapper.updateById(areaEntity);
        if (i == 0) {
            return IstarResponse.error("绑定是失败，请检查id是否正确");
        }
        return IstarResponse.ok("绑定成功");
    }

    public List containsName(List<AreaEntity> areaEntityList, String name, String shortName) {

        return areaEntityList.stream().filter(a -> {
            a.setChildren(this.containsName(a.getChildren(), name, shortName));
            if (a.getChildren().size() > 0) {
                return true;
            }

            return (a.getName().contains(name) && a.getNickName().contains(shortName));
        }).collect(Collectors.toList());
    }

    private void buildTree(AreaEntity areaEntity, List<AreaEntity> areaEntityList) {
        areaEntity.setChildren(new ArrayList<>());
        for (AreaEntity tempEntity : areaEntityList) {
            if (StringUtils.isNotBlank(tempEntity.getParentId()) && tempEntity.getParentId().equals(areaEntity.getId())) {
                this.buildTree(tempEntity, areaEntityList);
                areaEntity.getChildren().add(tempEntity);
            }
        }
    }
}
