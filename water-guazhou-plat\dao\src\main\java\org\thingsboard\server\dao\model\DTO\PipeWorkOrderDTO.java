package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;

/**
 * 漏损工单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-31
 */
@Data
public class PipeWorkOrderDTO extends WorkOrder {

    private String receiveDepartmentName;

    private String partitionId;

    private String organizerName;

    private String processUserName;

    private String partitionName;


}
