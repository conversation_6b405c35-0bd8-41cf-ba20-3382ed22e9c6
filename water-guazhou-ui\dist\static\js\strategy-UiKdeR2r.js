import{_ as Y}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as H}from"./CardTable-rdWOL4_6.js";import{_ as R}from"./CardSearch-CB_HNR-Q.js";import{m as N,d as F,M as B,c as _,r as h,s as U,a8 as p,x as y,S as w,ar as G,a9 as O,bu as J,o as $,f as K,g as Q,n as Z,q as T,i as L,b7 as ee}from"./index-r0dFAfgr.js";import{n as k,b as A,o as te,q as le}from"./zhandian-YaGuQZe6.js";import{o as ae}from"./index-D9ERhRP6.js";import{I as C}from"./common-CvK_P_ao.js";import{u as ie}from"./useStation-DJgnSZIA.js";import{f as re}from"./DateFormatter-Bm9a68Ax.js";import{a as q}from"./data-quisimke.js";import{a as se}from"./GeneralProcessing-CQ8i9ijT.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function ne(P){return N({url:"/api/sb/priceVersion/allCode",method:"get",params:P})}const de={class:"wrapper"},Te=F({__name:"strategy",setup(P){const{$btnPerms:V}=B(),{getStationTree:M,getStationTreeByDisabledType:j}=ie(),g=_(),x=_(),v=_(),W=[{label:"数值等于X",value:"1"},{label:"数值不等于X",value:"2"},{label:"数值高于X",value:"3"},{label:"数低于X",value:"4"},{label:"数值在X和Y之间",value:"5"},{label:"数值不在X与Y之间",value:"6"},{label:"数值超过M分钟等于X",value:"7"},{label:"数值超过M分钟不等于X",value:"8"},{label:"数值超过M分钟高于X",value:"9"},{label:"数值超过M分钟低于X",value:"10"},{label:"数值在X和Y之间超过M分钟",value:"11"},{label:"数值不在X和Y之间超过M分钟",value:"12"}],S=[{label:"高于",value:"1"},{label:"低于",value:"2"},{label:"等于",value:"3"},{label:"不等于",value:"4"}],D=[{label:"提醒报警",value:"1"},{label:"重要报警",value:"2"},{label:"紧急报警",value:"3"}],r=h({type:"1"}),I=_({defaultParams:{type:"1"},filters:[{type:"radio-button",field:"type",options:[{label:"普通规则",value:"1"},{label:"风控规则",value:"2"}],label:"",onChange:e=>{var t;r.type=e,I.value.defaultParams={type:e},(t=v.value)==null||t.resetForm(),m()}},{label:"变量名称",field:"name",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:C.QUERY,click:()=>m()},{type:"default",perm:!0,text:"重置",svgIcon:U(ee),click:()=>{var e;(e=v.value)==null||e.resetForm(),m()}},{perm:!0,text:"新建",icon:C.ADD,type:"success",click:()=>X()}]}]}),f=h({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"规则名称",prop:"title"},{label:"站点变量名称",prop:"stationAttrName"},{label:"触发条件",prop:"ruleType",formatter:e=>{var t;return(t=W.find(l=>l.value===e.ruleType))==null?void 0:t.label},hidden:p(()=>r.type==="2")},{label:"触发条件",prop:"ruleType",formatter:e=>{var t;return(t=S.find(l=>l.value===e.ruleType))==null?void 0:t.label},hidden:p(()=>r.type==="1")},{label:"报警等级",prop:"alarmLevel",formatter:e=>{var t;return(t=D.find(l=>l.value===e.alarmLevel))==null?void 0:t.label},hidden:p(()=>r.type==="2")},{label:"报警类型",prop:"alarmType",formatter:e=>{var t;return(t=q.find(l=>l.value===e.alarmType))==null?void 0:t.label},hidden:p(()=>r.type==="2")},{label:"处理建议",prop:"processMethod",hidden:p(()=>r.type==="2")},{label:"x",prop:"x",formatter:e=>{var t;return(t=e.ruleParamObj)==null?void 0:t.x},hidden:p(()=>r.type==="2")},{label:"y",prop:"y",formatter:e=>{var t;return(t=e.ruleParamObj)==null?void 0:t.y},hidden:p(()=>r.type==="2")},{label:"m",prop:"m",formatter:e=>{var t;return(t=e.ruleParamObj)==null?void 0:t.m},hidden:p(()=>r.type==="2")},{label:"低风险值",prop:"x",formatter:e=>{var t;return(t=e.ruleParamObj)==null?void 0:t.x},hidden:p(()=>r.type==="1")},{label:"中风险值",prop:"y",formatter:e=>{var t;return(t=e.ruleParamObj)==null?void 0:t.y},hidden:p(()=>r.type==="1")},{label:"高风险值",prop:"m",formatter:e=>{var t;return(t=e.ruleParamObj)==null?void 0:t.m},hidden:p(()=>r.type==="1")},{label:"联动采集变量ID",prop:"remoteStationAttrId"},{label:"联动监控点ID",prop:"remoteVideoId"},{label:"创建时间",prop:"createTime",formatter:e=>re(e.createTime,"YYYY-MM-DD HH:mm:ss")},{label:"是否启用",prop:"status",formatter:e=>e.status==="1"?"启用":"停用"}],operationWidth:"160px",operations:[{type:"primary",text:"编辑",icon:C.EDIT,perm:V("RoleManageEdit"),click:e=>E(e)},{type:"danger",text:"删除",perm:V("RoleManageDelete"),icon:C.DELETE,click:e=>z(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{f.pagination.page=e,f.pagination.limit=t,m()}}}),c=h({title:"新增",dialogWidth:"1000px",labelWidth:"100px",submitting:!1,submit:e=>{var n,d;c.submitting=!0;let t="新增成功";e.id&&(t="修改成功"),e.ruleParamObj={x:e.x,y:e.y,m:e.m},delete e.x,delete e.y,delete e.m,e.type=r.type;const l={app:!1,sms:!1,wechat:!1};e.sendWay.map(a=>{l[a]=!0}),e.sendWay=JSON.stringify(l);const i={...e,msgList:(n=e.msgList)==null?void 0:n.map(a=>({userId:a})),appList:(d=e.appList)==null?void 0:d.map(a=>({userId:a}))};k(i).then(()=>{var a;c.submitting=!1,(a=g.value)==null||a.closeDialog(),y.success(t),m()}).catch(a=>{c.submitting=!1,y.warning(a)})},defaultValue:{reAlarmType:"1"},group:[{fields:[{xs:12,type:"input",label:"规则名称",field:"title"},{xs:12,type:"select-tree",label:"监测点:",multiple:!1,field:"stationAttrId",clearable:!1,showCheckbox:!1,lazy:!0,options:[],lazyLoad:(e,t)=>{var l,i;if(e.level===0)return t(s.stationTree);if(((l=e.data.children)==null?void 0:l.length)>0)return t(e.data.children);if(e.isLeaf)return t([]);if((i=e.data)!=null&&i.isLeaf)return t([]);A({stationId:e.data.id}).then(n=>{var a;const d=(a=n.data)==null?void 0:a.map(u=>({label:u.type,value:"",id:"",children:u.attrList.map(o=>({label:o.name,value:o.id,id:o.id,isLeaf:!0}))}));return t(d)})}},{type:"divider",text:"规则设置"},{xs:12,type:"select",label:"触发条件",field:"ruleType",rules:[{required:!0,message:"请输入触发条件"}],options:W},{xs:12,type:"input-number",label:"x",field:"x",rules:[{required:!0,message:"请输入x"}]},{xs:12,handleHidden(e,t,l){l.hidden=["5","6","11","12"].indexOf(e.ruleType)===-1},type:"input-number",label:"y",field:"y",rules:[{required:!0,message:"请输入y"}]},{xs:12,handleHidden(e,t,l){l.hidden=["7","8","9","10","11","12"].indexOf(e.ruleType)===-1},type:"input-number",label:"m",field:"m",rules:[{required:!0,message:"请输入m"}]},{xs:12,type:"select",label:"开启状态",field:"status",options:[{label:"启用",value:"1"},{label:"停用",value:"2"}]},{xs:12,type:"select",label:"报警等级",field:"alarmLevel",rules:[{required:!0,message:"请输入报警等级"}],options:D},{xs:12,type:"select",label:"报警类型",field:"alarmType",options:q},{xs:24,type:"checkbox",label:"发送方式",field:"sendWay",options:[{label:"APP",value:"app"},{label:"短信",value:"sms"},{label:"微信",value:"wechat"}]},{type:"divider",text:"处理建议"},{xs:12,type:"input",label:"处理建议",field:"processMethod"},{type:"divider",text:"其它设置"},{xs:12,type:"select",label:"报警机制",field:"reAlarmType",options:[{label:"未解除/恢复期前,不重复提醒",value:"1"},{label:"报警恢复之前",value:"2"}]},{xs:12,handleHidden(e,t,l){l.hidden=e.reAlarmType!=="2"},type:"input-number",label:"间隔时间",field:"reAlarmValue",rules:[{required:!0,message:"请输入间隔时间"}]},{xs:12,handleHidden(e,t,l){l.hidden=e.reAlarmType!=="2"},type:"select",label:"时间单位",field:"reAlarmUnit",rules:[{required:!0,message:"请输入时间单位"}],options:[{label:"分钟",value:"minute"},{label:"小时",value:"hour"},{label:"天",value:"day"}]},{type:"divider",text:"联动采集"},{xs:12,type:"select-tree",label:"监测点:",multiple:!1,field:"remoteStationAttrId",clearable:!1,showCheckbox:!1,lazy:!0,options:[],lazyLoad:(e,t)=>{var l,i;if(e.level===0)return t(s.stationTree);if(((l=e.data.children)==null?void 0:l.length)>0)return t(e.data.children);if(e.isLeaf)return t([]);if((i=e.data)!=null&&i.isLeaf)return t([]);A({stationId:e.data.id}).then(n=>{var a;const d=(a=n.data)==null?void 0:a.map(u=>({label:u.type,value:"",id:"",children:u.attrList.map(o=>({label:o.name,value:o.id,id:o.id,isLeaf:!0}))}));return t(d)})}},{xs:12,type:"select-tree",label:"选择监控点",field:"remoteVideoId",options:p(()=>s.videoTree)},{type:"divider",text:"告警推送配置"},{xl:12,type:"department-user",multiple:!0,label:"短信提醒用户",field:"msgList"},{xl:12,type:"department-user",multiple:!0,label:"APP推送用户",field:"appList"}]}]}),b=h({title:"新增",dialogWidth:"1000px",labelWidth:"100px",submitting:!1,submit:e=>{c.submitting=!0;let t="新增成功";e.id&&(t="修改成功"),e.ruleParamObj={x:e.x,y:e.y,m:e.m,x_process_method:e.x_process_method,y_process_method:e.y_process_method,m_process_method:e.m_process_method},delete e.x,delete e.y,delete e.m,delete e.x_process_method,delete e.y_process_method,delete e.m_process_method,e.type=r.type;const l={app:!1,sms:!1,wechat:!1};e.sendWay.map(i=>{l[i]=!0}),e.sendWay=JSON.stringify(l),k(e).then(()=>{var i;c.submitting=!1,(i=x.value)==null||i.closeDialog(),y.success(t),m()}).catch(i=>{c.submitting=!1,y.warning(i)})},defaultValue:{reAlarmType:"1"},group:[{fields:[{xs:12,type:"input",label:"规则名称",field:"title"},{xs:12,type:"select-tree",label:"监测点:",multiple:!1,field:"stationAttrId",clearable:!1,showCheckbox:!1,lazy:!0,options:[],lazyLoad:(e,t)=>{var l,i;if(e.level===0)return t(s.stationTree);if(((l=e.data.children)==null?void 0:l.length)>0)return t(e.data.children);if(e.isLeaf)return t([]);if((i=e.data)!=null&&i.isLeaf)return t([]);A({stationId:e.data.id}).then(n=>{var a;const d=(a=n.data)==null?void 0:a.map(u=>({label:u.type,value:"",id:"",children:u.attrList.map(o=>({label:o.name,value:o.id,id:o.id,isLeaf:!0}))}));return t(d)})}},{xs:12,type:"select",label:"开启状态",field:"status",options:[{label:"启用",value:"1"},{label:"停用",value:"2"}]},{type:"divider",text:"报警设置"},{xs:12,type:"select",label:"触发条件",field:"ruleType",rules:[{required:!0,message:"请输入触发条件"}],options:S},{xs:24,type:"checkbox",label:"发送方式",field:"sendWay",options:[{label:"APP",value:"app"},{label:"短信",value:"sms"},{label:"微信",value:"wechat"}]},{type:"divider",text:"风控设置"},{xs:10,type:"input",label:"低风险触发值",field:"x"},{xs:14,placeholder:"请输入处理建议",labelWidth:"10px",type:"input",label:"",field:"x_process_method"},{xs:10,type:"input",label:"中风险触发值",field:"y"},{xs:14,placeholder:"请输入处理建议",labelWidth:"10px",type:"input",label:"",field:"y_process_method"},{xs:10,type:"input",label:"高风险触发值",field:"m"},{xs:14,placeholder:"请输入处理建议",labelWidth:"10px",type:"input",label:"",field:"m_process_method"},{type:"divider",text:"其它设置"},{xs:12,type:"select",label:"报警机制",field:"reAlarmType",options:[{label:"未解除/恢复期前,不重复提醒",value:"1"},{label:"报警恢复之前",value:"2"}]},{xs:12,handleHidden(e,t,l){l.hidden=e.reAlarmType!=="2"},type:"input-number",label:"间隔时间",field:"reAlarmValue",rules:[{required:!0,message:"请输入间隔时间"}]},{xs:12,handleHidden(e,t,l){l.hidden=e.reAlarmType!=="2"},type:"select",label:"时间单位",field:"reAlarmUnit",rules:[{required:!0,message:"请输入时间单位"}],options:[{label:"分钟",value:"minute"},{label:"小时",value:"hour"},{label:"天",value:"day"}]},{type:"divider",text:"联动采集"},{xs:12,type:"select-tree",label:"监测点:",multiple:!1,field:"remoteStationAttrId",clearable:!1,showCheckbox:!1,lazy:!0,options:[],lazyLoad:(e,t)=>{var l,i;if(e.level===0)return t(s.stationTree);if(((l=e.data.children)==null?void 0:l.length)>0)return t(e.data.children);if(e.isLeaf)return t([]);if((i=e.data)!=null&&i.isLeaf)return t([]);A({stationId:e.data.id}).then(n=>{var a;const d=(a=n.data)==null?void 0:a.map(u=>({label:u.type,value:"",id:"",children:u.attrList.map(o=>({label:o.name,value:o.id,id:o.id,isLeaf:!0}))}));return t(d)})},onChange:(e,t,l)=>{debugger}},{xs:12,type:"select-tree",label:"选择监控点",field:"remoteVideoId",options:p(()=>s.videoTree)}]}]}),X=()=>{var e,t;r.type==="1"?(c.title="新增",c.defaultValue={reAlarmType:"1",type:"1"},(e=g.value)==null||e.openDialog()):(b.title="新增",b.defaultValue={reAlarmType:"1",type:"2"},(t=x.value)==null||t.openDialog())},E=e=>{var t,l,i,n;if(e.sendWay&&!Array.isArray(e.sendWay)){const d=JSON.parse((e==null?void 0:e.sendWay)??"{}");e.sendWay=[];for(const a in d)d[a]&&e.sendWay.push(a)}if(console.log(e.sendWay),r.type==="1"){c.title="编辑",e.ruleParamObj&&(e={...e,...e.ruleParamObj});const d=(t=e.msgList)==null?void 0:t.map(u=>u.userId),a=(l=e.appList)==null?void 0:l.map(u=>u.userId);c.defaultValue={...e||{},msgList:d,appList:a},(i=g.value)==null||i.openDialog()}else b.title="编辑",e.ruleParamObj&&(e={...e,...e.ruleParamObj}),b.defaultValue={...e||{}},(n=x.value)==null||n.openDialog()},z=e=>{w("确定删除指定优化策略?","删除提示").then(()=>{te([e.id]).then(t=>{var l;((l=t.data)==null?void 0:l.code)===200?(y.success("删除成功"),m()):y.warning("删除失败")}).catch(t=>{y.warning(t)})})},s=h({unitList:[],stationTree:[],codeList:[],videoTree:[],getWaterSupplyTreeValue:()=>{G(1).then(t=>{s.unitList=O(t.data.data||[])})},getPriceVersionCodeValue:()=>{ne({status:"ON"}).then(e=>{const t=e.data.data||[];s.codeList=[],t.forEach(l=>{s.codeList.push({label:l,value:l})})})},getVideoTree:()=>{se({},ae).then(e=>{debugger;s.videoTree=O(e.data,"children")})}}),m=async()=>{var t;f.dataList=[];const e={size:f.pagination.limit,page:f.pagination.page,type:r.type,...((t=v.value)==null?void 0:t.queryParams)||{}};le(e).then(l=>{f.dataList=l.data.data.data||[],f.pagination.total=l.data.data.total||0})};return J(async()=>{const e=await M();await j(e,["Project","Station"],!1,"Station"),s.stationTree=e}),$(async()=>{await m(),s.getWaterSupplyTreeValue(),s.getPriceVersionCodeValue(),s.getVideoTree()}),K(()=>{s.getWaterSupplyTreeValue(),s.getPriceVersionCodeValue(),s.getVideoTree()}),(e,t)=>{const l=R,i=H,n=Y;return Q(),Z("div",de,[T(l,{ref_key:"refSearch",ref:v,config:L(I)},null,8,["config"]),T(i,{class:"card-table",config:L(f)},null,8,["config"]),T(n,{ref_key:"refForm",ref:g,config:L(c)},null,8,["config"]),T(n,{ref_key:"refWindControl",ref:x,config:L(b)},null,8,["config"])])}}});export{Te as default};
