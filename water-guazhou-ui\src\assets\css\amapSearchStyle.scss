.amap-container {
  position: relative;

  .search-box {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 240px;
    height: 35px;

    .search-box-wrapper {
      input {
        height: 35px;
        margin: auto 5px auto 10px;
        border: 1px solid #eee;
      }
    }
  }

  .search-m-show {
    position: absolute;
    top: 40px;
    right: 5px;
    width: 150px;
    height: 30px;
    display: flex;
    padding: 0 10px;
    align-items: center;
    background-color: #fff;
    border-radius: 2px 3px 3px 2px;
    box-shadow: 0 2px 2px rgba(0, 0, 0, .15);
    z-index: 10;

    .s-text {
      margin: 0 0;
      font-size: small;
      line-height: 14px;
    }

    .s-label {
      color: #1b2d70;
    }

    .s-i-btn {
      cursor: pointer;
      color: #2447c5;
      padding: 2px 8px;
      border-radius: 5px;
      border: 1px solid rgb(197, 213, 255);
    }

    .el-icon-location {
      color: #1b2d70;
    }
  }
}

.location-map {
  width: 100%;
  height: 100%;
  position: relative;

  .location-search-box {
    z-index: 1;
    position: absolute;
    top: 5px;
    left: 5px;

  }
}
