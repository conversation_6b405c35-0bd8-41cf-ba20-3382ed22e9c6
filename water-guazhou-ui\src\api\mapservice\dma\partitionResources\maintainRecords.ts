import { request } from '@/plugins/axios'

/**
 * 查询维护记录列表
 * @param params
 * @returns
 */
export const GetDmaPartitionMaintainRecords = (
  params: IQueryPagerParams & {
    partitionId?: string
  }
) => {
  return request({
    url: '/api/spp/dma/partition/maintainRecords/list',
    method: 'get',
    params
  })
}
/**
 * 添加维护记录
 * @param params
 * @returns
 */
export const AddDmaPartitionMaintainRecords = (params: {
  partitionId: string
  maintainDate: string
  remark: string
  img: string
}) => {
  return request({
    url: '/api/spp/dma/partition/maintainRecords',
    method: 'post',
    data: params
  })
}
/**
 * 删除维护记录
 * @param ids
 * @returns
 */
export const DeleteDmaPartitionMaintainRecords = (ids: string[]) => {
  return request({
    url: '/api/spp/dma/partition/maintainRecords',
    method: 'delete',
    data: ids
  })
}
