<!-- 末级路由,处理缓存 -->
<template>
  <div class="layout-final">
    <router-view v-if="!useTagsStore().reloading" v-slot="{ Component }">
      <KeepAlive :max="useAppStore().keepAliveMax">
        <Transition mode="out-in" name="fade-slide" appear>
          <component :is="Component" :key="$route.name"></component>
        </Transition>
      </KeepAlive>
    </router-view>
  </div>
</template>

<script lang="ts" setup>
import { useAppStore, useTagsStore } from '@/store';
</script>

<style scoped>
.layout-final {
  width: 100%;
  height: 100%;
  position: relative;
}

.transition-container {
  width: 100%;
  height: 100%;
}

/* 路由切换动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
