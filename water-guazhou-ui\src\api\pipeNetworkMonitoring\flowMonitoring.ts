// 流量监测
import request from '@/plugins/axios';

// 查询指定项目下的指定类型的站点及其实时数据
export function stationView(params: {
  stationType: string;
  projectId: string;
  /**
   * 字段名显示成协议配置的名称
   */
  customName?: boolean;
}) {
  return request({
    url: '/istar/api/station/data/detailList/view',
    method: 'get',
    params
  });
}

// 【日流量、月流量、年流量】流量曲线
export function getFlowData(params: {
  stationIdList?: string;
  stationId?: string;
  queryType: string;
  date: number | string;
}) {
  return request({
    url: '/istar/api/flowMonitoringStation/flowData',
    method: 'get',
    params
  });
}

// 查询指定项目下的指定类型的站点及其实时数据
export function getStationView(params: {
  stationType: string;
  projectId: string;
}) {
  return request({
    url: '/istar/api/station/data/detailList/view',
    method: 'get',
    params
  });
}

// 【流量分析-配表分析】
export function getMeterConfigChart(params: {
  stationId: string;
  start?: number | null;
  end?: number | null;
}) {
  return request({
    url: '/istar/api/flowMonitoringStation/getMeterConfigChart',
    method: 'get',
    params
  });
}

// 【流量分析-峰谷分析】
export function getPeak(params: {
  stationId: string;
  start?: number | null;
  end?: number | null;
}) {
  return request({
    url: '/istar/api/flowMonitoringStation/getPeak',
    method: 'get',
    params
  });
}

// 【流量分析-同/环比曲线】
export function getRatio(params: {
  stationId: string;
  start?: number | null;
  end?: number | null;
  type: string;
}) {
  return request({
    url: '/istar/api/flowMonitoringStation/getRatio',
    method: 'get',
    params
  });
}
// 【流量分析-时段对比】
export function getFlowPeriod(params: {
  stationId: string;
  start?: number | null;
  end?: number | null;
  queryType: string;
}) {
  return request({
    url: '/istar/api/flowMonitoringStation/getPeriod',
    method: 'get',
    params
  });
}

// 【压力分析】
export function getPressureRatio(params: {
  stationId: string;
  start?: number | null;
  end?: number | null;
}) {
  return request({
    url: '/istar/api/pressureMonitoringStation/getPeriod',
    method: 'get',
    params
  });
}

// 【日流量、月流量、年流量】压力曲线
export function pressureFlowData(params: {
  stationId: string;
  queryType: string;
  date: string;
}) {
  return request({
    url: '/istar/api/pressureMonitoringStation/pressureData',
    method: 'get',
    params
  });
}
