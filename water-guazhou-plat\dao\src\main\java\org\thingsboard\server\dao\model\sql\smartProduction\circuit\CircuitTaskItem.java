package org.thingsboard.server.dao.model.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_circuit_task_item")
public class CircuitTaskItem {
    // id
    @TableId
    private String id;

    // 主表ID
    private String mainId;

    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    private String type;

    // 巡检项目分类
    private String itemType;

    // 巡检项目名称
    private String itemName;

    // 巡检方法
    private String itemMethod;

    // 巡检要求
    private String itemRequire;

    // 巡检结果。合格、不合格
    private String result;

    // 巡检结果备注
    private String resultRemark;

    // 巡检附件
    private String file;

    // 关联工单，若关联了工单，该字段有值
    private String workOrderId;

    // 提交巡检结果时间
    private Date executionTime;

    // 提交人
    @ParseUsername
    private String executionUserId;

    // 租户ID
    private String tenantId;

}
