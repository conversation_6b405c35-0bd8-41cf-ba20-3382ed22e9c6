<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseTemplateTypeMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseTemplateType" id="BaseTemplateTypeResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="description"    column="description"    />
        <result property="type"    column="type"    />
        <result property="bindingAnimation"    column="binding_animation"    />
        <result property="defaultGraphicElement"    column="default_graphic_element"    />
        <result property="styleConfiguration"    column="style_configuration"    />
        <result property="resolutionAdaptation"    column="resolution_adaptation"    />
    </resultMap>

    <sql id="selectBaseTemplateTypeVo">
        select id, name, code, description, type, binding_animation, default_graphic_element, style_configuration, resolution_adaptation from base_template_type
    </sql>

    <select id="selectBaseTemplateTypeList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseTemplateType" resultMap="BaseTemplateTypeResult">
        <include refid="selectBaseTemplateTypeVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="bindingAnimation != null  and bindingAnimation != ''"> and binding_animation = #{bindingAnimation}</if>
            <if test="defaultGraphicElement != null  and defaultGraphicElement != ''"> and default_graphic_element = #{defaultGraphicElement}</if>
            <if test="styleConfiguration != null  and styleConfiguration != ''"> and style_configuration = #{styleConfiguration}</if>
            <if test="resolutionAdaptation != null  and resolutionAdaptation != ''"> and resolution_adaptation = #{resolutionAdaptation}</if>
        </where>
    </select>
    
    <select id="selectBaseTemplateTypeById" parameterType="String" resultMap="BaseTemplateTypeResult">
        <include refid="selectBaseTemplateTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseTemplateType" parameterType="org.thingsboard.server.dao.model.sql.base.BaseTemplateType">
        insert into base_template_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="code != null">code,</if>
            <if test="description != null">description,</if>
            <if test="type != null">type,</if>
            <if test="bindingAnimation != null">binding_animation,</if>
            <if test="defaultGraphicElement != null">default_graphic_element,</if>
            <if test="styleConfiguration != null">style_configuration,</if>
            <if test="resolutionAdaptation != null">resolution_adaptation,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="code != null">#{code},</if>
            <if test="description != null">#{description},</if>
            <if test="type != null">#{type},</if>
            <if test="bindingAnimation != null">#{bindingAnimation},</if>
            <if test="defaultGraphicElement != null">#{defaultGraphicElement},</if>
            <if test="styleConfiguration != null">#{styleConfiguration},</if>
            <if test="resolutionAdaptation != null">#{resolutionAdaptation},</if>
         </trim>
    </insert>

    <update id="updateBaseTemplateType" parameterType="org.thingsboard.server.dao.model.sql.base.BaseTemplateType">
        update base_template_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
            <if test="description != null">description = #{description},</if>
            <if test="type != null">type = #{type},</if>
            <if test="bindingAnimation != null">binding_animation = #{bindingAnimation},</if>
            <if test="defaultGraphicElement != null">default_graphic_element = #{defaultGraphicElement},</if>
            <if test="styleConfiguration != null">style_configuration = #{styleConfiguration},</if>
            <if test="resolutionAdaptation != null">resolution_adaptation = #{resolutionAdaptation},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseTemplateTypeById" parameterType="String">
        delete from base_template_type where id = #{id}
    </delete>

    <delete id="deleteBaseTemplateTypeByIds" parameterType="String">
        delete from base_template_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>