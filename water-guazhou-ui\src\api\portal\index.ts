import request from '@/plugins/axios';

/**
 * 获取应用已选中的菜单列表
 * @param param
 * @returns
 */
export function getChecklist(param: string) {
  return request({
    url: `/api/tenantApplication/selectedMenuList?tenantApplicationId=${param}`,
    method: 'GET'
  });
}

/**
 * 获取当前企业全部的应用
 * @param tenantId
 * @param type 是否是全部应用，默认只返回PC端，值ALL返回全部
 * @returns AxiosPromise
 */
export function getapplications(tenantId: string, type?: 'ALL') {
  return request({
    url: `/api/tenantApplication/all?tenantId=${tenantId}&resourceType=${
      type || ''
    }`,
    method: 'GET'
  });
}

/**
 * 删除指定企业应用（支持批量删除）
 * @param params
 * @returns
 */
export function deleteapplications(params: string[]) {
  return request({
    url: '/api/tenantApplication/delete',
    method: 'delete',
    data: params
  });
}

/**
 * 添加修改应用
 * @param params
 * @returns
 */
export function addeditapp(params: any) {
  return request({
    url: '/api/tenantApplication/save',
    method: 'POST',
    data: params
  });
}

/**
 * 获取指定角色所拥有的企业应用权限列表
 * @param roleId 角色id
 * @returns
 */
export const getRoleTenantApplicationList = (roleId: string) =>
  request({
    url: `/api/role/getRoleTenantApplicationList?roleId=${roleId}`,
    method: 'get'
  });
/**
 * 设置或者取消特定角色的企业应用权限
 * @param params
 * @returns
 */
export const assignTenantApplicationToRole = (params: {
  roleId: string;
  tenantApplicationId: string;
}) =>
  request({
    url: '/api/role/assignTenantApplicationToRole',
    method: 'post',
    data: params
  });
