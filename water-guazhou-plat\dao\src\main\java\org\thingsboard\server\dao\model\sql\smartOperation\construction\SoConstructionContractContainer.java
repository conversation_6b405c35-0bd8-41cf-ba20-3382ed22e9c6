package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.math.BigDecimal;
import java.util.List;


@Getter
@Setter
@ResponseEntity
public class SoConstructionContractContainer {
    // 所属工程编号
    private String constructionCode;

    // 所属工程名称
    private String constructionName;

    // 所属工程类型id
    private String constructionTypeId;

    // 所属工程类型名称
    private String constructionTypeName;

    // 业主单位
    private String firstpartOrganization;

    // 设计单位
    private String secondpartOrganization;

    // 监理单位
    private String supervisorOrganization;

    // 建设单位
    private String constructionOrganization;

    // 合同总金额
    private BigDecimal contractTotalCost;

    // 当前状态
    @TableField(exist = false)
    private SoGeneralTaskStatus status;

    private List<SoConstructionContract> items;
    
}
