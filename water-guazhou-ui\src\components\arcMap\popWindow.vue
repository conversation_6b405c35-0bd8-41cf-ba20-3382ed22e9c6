<template>
  <div class="amap-pop-tag" :class="{ isDark: useAppStore().isDark }">
    <div v-if="config.title" class="header">
      <span class="title">{{ config.title }}</span>
      <span v-if="config.titleRight" class="right">{{
        config.titleRight
      }}</span>
      <el-icon class="close-wrapper" @click="$emit('close', config.id)">
        <Close />
      </el-icon>
    </div>
    <div class="map-content">
      <span v-if="config.valuesLabel" class="label">{{
        config.valuesLabel
      }}</span>
      <div
        class="scada-table-wrapper"
        :class="{ scrollable: config.values.length >= 4 }"
      >
        <table
          class="first-table scada-content"
          :style="{
            animationDuration: playtime + 's'
          }"
        >
          <tbody>
            <tr v-for="(item, i) in config.values" :key="i">
              <td :title="item.label">
                {{ item.label }}
              </td>

              <td :class="item.status">
                {{ item.value || '-' }} {{ item.unit }}
              </td>
            </tr>
          </tbody>
        </table>
        <table
          v-if="config.values.length >= 4"
          class="second-table scada-content"
          :style="{
            'animation-duration': playtime + 's'
          }"
        >
          <tbody>
            <tr v-for="(item, i) in config.values" :key="i">
              <td :title="item.label">
                {{ item.label }}
              </td>

              <td :class="item.status">
                {{ item.value || '-' }} {{ item.unit }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <template v-for="(item, i) in config.extraInfos" :key="i">
        <div class="extra-info-item">
          <span class="label">{{ item.label }}</span>
          <span
            class="value"
            :title="item.value?.toString()"
            :style="
              typeof item.data?.style === 'function'
                ? item.data.style(item)
                : item.data?.style
            "
            @click="() => item.data?.click && item.data.click(item)"
          >
            <el-icon v-if="item.icon">
              <i :class="item.icon"></i>
            </el-icon>
            {{ item.value }}
          </span>
        </div>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Close } from '@element-plus/icons-vue';
import { useAppStore } from '@/store';

defineEmits(['close']);
const props = defineProps<{
  config: {
    id: string;
    point: number[];
    title: string;
    titleRight?: string;
    /** table数据 */
    values: { label: string; value: any; status: string; unit: string }[];
    /** table标题 */
    valuesLabel?: string;
    extraInfos?: NormalOption[];
  };
}>();
const playtime = ref<number>(props.config.values.length);
</script>
<style lang="scss" scoped>
.amap-pop-tag {
  // display: none;
  background-color: #ddd;
  min-width: 150px;
  &.isDark {
    .scada-table-wrapper {
      .first-table,
      .second-table {
        td {
          &:first-child {
            background-color: #253953;
          }
          &:nth-child(2) {
            background-color: #182536;
          }
        }
      }
    }
  }
  .header {
    padding: 8px;
    height: 32px;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    align-content: center;
    background-color: #5094a8;
    .title {
      margin-right: auto;
    }
    .right {
      font-size: 12px;
      margin-left: 12px;
      line-height: 16px;
    }
    .close-wrapper {
      cursor: pointer;
    }
  }

  .map-content {
    padding: 8px;
    padding-bottom: 0;
  }
}
.extra-info-item {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  line-height: 25px;
}
.label {
  font-size: 12px;
  line-height: 25px;
  word-break: keep-all;
}
.value {
  max-width: 200px;
  word-break: keep-all;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.scada-table-wrapper {
  height: 75px;
  overflow: hidden;
  cursor: pointer;
  &.scrollable {
    &:hover {
      .first-table,
      .second-table {
        animation-play-state: paused;
      }
    }

    .first-table {
      animation-name: first-table;
      animation-duration: 10s;
      animation-timing-function: linear;
      animation-delay: 0s;
      animation-iteration-count: infinite;
      animation-direction: normal;
    }
    .second-table {
      animation-name: second-table;
      animation-duration: 10s;
      animation-timing-function: linear;
      animation-delay: 0s;
      animation-iteration-count: infinite;
      animation-direction: normal;
    }
  }
  .first-table,
  .second-table {
    width: 100%;
    td {
      padding: 0 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: keep-all;
      font-size: 12px;
      line-height: 25px;
      &:first-child {
        min-width: 60px;
        background-color: #caccce;
      }
      &:nth-child(2) {
        min-width: 100px;
        text-align: center;
        background-color: #aaaaaa;
      }
    }
  }
}

@keyframes first-table {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  100% {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    display: none;
  }
}

@keyframes second-table {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  100% {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    display: none;
  }
}
</style>
