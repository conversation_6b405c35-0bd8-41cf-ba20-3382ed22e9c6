<template>
  <div class="item_center_big_bg">
    <!-- 最后读取时间 -->
    <div style="position: absolute; top: 18px; left: 110px">
      {{ moment(new Date()).format('YYYY-MM-DD HH:mm:ss') }}
    </div>

    <!-- 进水浊度组 -->
    <div
      class="flex"
      style="position: absolute; top: 90px; left: 60px; height: 167px"
    >
      <div>7.13</div>
      <div>8.28</div>
      <div>0.039</div>
      <div>3454</div>
    </div>
    <!-- 1#沉淀池浊度 -->
    <div
      class="flex"
      style="position: absolute; top: 93px; left: 156px; height: 368px"
    >
      <div>2.237</div>
      <div>1.625</div>
      <div>3.798</div>
      <div>3.768</div>
      <div>0.47</div>
      <div>0.37</div>
      <div>0.656</div>
      <div>0.225</div>
    </div>
    <!-- 出厂PH -->
    <div
      class="flex"
      style="position: absolute; top: 426px; left: 60px; height: 216px"
    >
      <div>8.12</div>
      <div>0.89</div>
      <div>0.38</div>
      <div>0.303</div>
      <div>3885</div>
    </div>

    <!-- 滤池液位左 -->
    <div
      class="flex"
      style="position: absolute; top: 71px; left: 372px; height: 123px"
    >
      <div>0.731</div>
      <div>0.66</div>
      <div>0.693</div>
      <div>0.688</div>
    </div>
    <!-- 滤池液位右 -->
    <div
      class="flex"
      style="position: absolute; top: 71px; left: 477px; height: 123px"
    >
      <div>0.691</div>
      <div>0.684</div>
      <div>0.701</div>
      <div>0.682</div>
    </div>
    <!-- 反冲洗泵房 -->
    <div
      class="flex"
      style="position: absolute; top: 518px; left: 275px; height: 88px"
    >
      <div>0.472</div>
      <div>0</div>
      <div>0.21</div>
    </div>
    <!-- 回收池液位 -->
    <div style="position: absolute; bottom: 175px; left: 391px">3.994</div>
    <!-- 平衡池液位 -->
    <div style="position: absolute; top: 111px; left: 760px">5.285</div>
    <!-- 排泥池液位 -->
    <div style="position: absolute; top: 240px; left: 760px">2.27</div>
    <!-- 吸水井液位 -->
    <div style="position: absolute; bottom: 57px; left: 607px">4.2</div>
    <!-- 送水泵房 -->
    <div
      class="flex"
      style="position: absolute; top: 482px; left: 804px; height: 158px"
    >
      <div>30</div>
      <div>0</div>
      <div>0</div>
      <div>0</div>
      <div>0</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import moment from 'moment';
</script>

<style lang="scss" scoped>
.item_center_big_bg {
  position: relative;
  width: 900px;
  height: 655px;
  background-size: 100% 100%;
  background-position: top;
  background-repeat: no-repeat;
  div {
    font-size: 14px;
  }
}

.flex {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
