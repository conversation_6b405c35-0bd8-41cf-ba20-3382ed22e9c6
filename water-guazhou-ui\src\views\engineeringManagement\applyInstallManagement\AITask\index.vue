<!--包装任务-->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="searchConfig" />
    <CardTable ref="refTable" :config="tableConfig" class="card-table" />
    <DialogForm ref="refForm" :config="formConfig" />
    <SLDrawer ref="refDetail" :config="detailDrawerConfig">
      <detail :task-info="state.taskInfo" :task-id="'1'"></detail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { Delete, Edit, Plus, Search, View } from '@element-plus/icons-vue';
import { SLConfirm } from '@/utils/Message';
import { processTypeList } from '@/api/engineeringManagement/process';
import useGlobal from '@/hooks/global/useGlobal';
import {
  applyInstallList,
  delApplyInstall,
  ediApplyInstall
} from '@/api/engineeringManagement/applyInstall';
import { formatDate } from '@/utils/DateFormatter';
import detail from './components/detail.vue';

const { $messageError, $messageSuccess, $messageWarning } = useGlobal();
const refTable = ref<ICardTableIns>();
const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();
const refDetail = ref<ISLDrawerIns>();
const state = reactive<{
  taskInfo: string;
}>({
  taskInfo: ''
});
// 新增按钮
const searchConfig = reactive<ISearch>({
  filters: [
    {
      label: '状态',
      field: 'status',
      type: 'select',
      options: [
        { label: '进行中', value: '进行中' },
        { label: '已完成', value: '已完成' },
        { label: '已结束', value: '已结束' }
      ]
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '新增',
          svgIcon: shallowRef(Plus),
          click: () => handleAddEdit()
        },
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => refreshData()
        }
      ]
    }
  ]
});

// 数据列表配置
const tableConfig = reactive<ICardTable>({
  loading: true,
  indexVisible: true,
  columns: [
    {
      label: '申请日期',
      prop: 'createTime',
      minWidth: 120,
      align: 'center',
      formatter: (row) => formatDate(row.createTime)
    },
    {
      label: '申请编号',
      prop: 'code',
      minWidth: 120,
      align: 'center',
      handleClick: (row) => {
        state.taskInfo = row;
        detailDrawerConfig.title = row.code;
        refDetail.value?.openDrawer();
      },
      cellStyle: { color: '#409eff' }
    },
    { label: '工程地址', prop: 'address', minWidth: 120, align: 'center' },
    { label: '工程类型', prop: 'typeName', minWidth: 120, align: 'center' },
    { label: '当前步骤', prop: 'currentStep', minWidth: 120, align: 'center' },
    { label: '当前状态', prop: 'status', minWidth: 120, align: 'center' }
  ],
  dataList: [],
  operationFixed: 'right',
  operationWidth: 260,
  operations: [
    {
      perm: true,
      text: '查看',
      isTextBtn: false,
      type: 'success',
      svgIcon: shallowRef(View),
      click: (row) => {
        detailDrawerConfig.title = row.code;
        refDetail.value?.openDrawer();
        state.taskInfo = row;
      }
    },
    {
      perm: true,
      text: '编辑',
      isTextBtn: false,
      svgIcon: shallowRef(Edit),
      click: (row) => handleAddEdit(row)
    },
    {
      perm: true,
      text: '删除',
      isTextBtn: false,
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: (row) => handleDelProcessStep([row.id])
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      tableConfig.pagination.page = page;
      tableConfig.pagination.limit = size;
      refreshData();
    }
  }
});
// 弹框表单配置
const formConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: 120,
  dialogWidth: 500,
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '工程类型',
          field: 'type',
          options: [],
          rules: [{ required: true, message: '请选择工程类型' }],
          placeholder: '请选择工程类型'
        },
        {
          type: 'input',
          label: '工程地址',
          field: 'address',
          rules: [{ required: true, message: '请填写工程地址' }],
          placeholder: '请填写工程地址'
        }
      ]
    }
  ]
});
// 查看流程详情
const detailDrawerConfig = reactive<IDrawerConfig>({
  title: '',
  cancel: false,
  width: '80%',
  group: []
});
// 附件弹框配置
const handleAddEdit = async (row?: any) => {
  const type = formConfig.group[0].fields?.find(
    (field) => field.field === 'type'
  ) as IFormSelect;
  const result = await processTypeList({ page: 1, size: 9999 });
  type.options = result.data?.data.data.map((data) => {
    return {
      label: data.name,
      value: data.id
    };
  });
  formConfig.defaultValue = {
    ...(row || {})
  };
  formConfig.submit = (params: any) => {
    SLConfirm('确定提交？', '提示信息').then(() => {
      formConfig.submitting = true;
      params = {
        ...params,
        id: row ? row.id : null
      };
      ediApplyInstall(params)
        .then(() => {
          refForm.value?.closeDialog();
          formConfig.submitting = false;
          $messageSuccess('保存成功');
          refreshData();
        })
        .catch((error) => {
          $messageError(error);
          formConfig.submitting = false;
        });
    });
  };
  refForm.value?.openDialog();
};
// 删除数据
const handleDelProcessStep = (ids: string[]) => {
  SLConfirm('确定删除？', '提示信息').then(() => {
    delApplyInstall(ids)
      .then(() => {
        $messageSuccess('删除成功');
        refreshData();
      })
      .catch((error) => {
        $messageWarning(error);
      });
  });
};

// 刷新数据
const refreshData = async () => {
  tableConfig.loading = true;
  const query = refSearch.value?.queryParams || {};
  const params = {
    ...query,
    page: tableConfig.pagination.page || 1,
    size: tableConfig.pagination.limit || 20
  };
  const result = await applyInstallList(params);
  const data = result.data?.data?.data;
  tableConfig.pagination.total = result.data?.data?.total;
  tableConfig.dataList = data;
  tableConfig.loading = false;
};

onMounted(async () => {
  // 获取部门树
  await refreshData();
});
</script>
