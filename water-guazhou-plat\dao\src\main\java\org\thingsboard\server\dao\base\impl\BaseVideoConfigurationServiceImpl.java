package org.thingsboard.server.dao.base.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseVideoConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseVideoConfiguration;
import org.thingsboard.server.dao.sql.base.BaseVideoConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseVideoConfigurationPageRequest;

/**
 * 平台管理-视频管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class BaseVideoConfigurationServiceImpl implements IBaseVideoConfigurationService {

    @Autowired
    private BaseVideoConfigurationMapper baseVideoConfigurationMapper;

    /**
     * 查询平台管理-视频管理
     *
     * @param id 平台管理-视频管理主键
     * @return 平台管理-视频管理
     */
    @Override
    public BaseVideoConfiguration selectBaseVideoConfigurationById(String id) {
        return baseVideoConfigurationMapper.selectBaseVideoConfigurationById(id);
    }

    /**
     * 查询平台管理-视频管理列表
     *
     * @param baseVideoConfiguration 平台管理-视频管理
     * @return 平台管理-视频管理
     */
    @Override
    public IPage<BaseVideoConfiguration> selectBaseVideoConfigurationList(BaseVideoConfigurationPageRequest baseVideoConfiguration) {
        return baseVideoConfigurationMapper.selectBaseVideoConfigurationList(baseVideoConfiguration);
    }

    /**
     * 新增平台管理-视频管理
     *
     * @param baseVideoConfiguration 平台管理-视频管理
     * @return 结果
     */
    @Override
    public int insertBaseVideoConfiguration(BaseVideoConfiguration baseVideoConfiguration) {
        return baseVideoConfigurationMapper.insertBaseVideoConfiguration(baseVideoConfiguration);
    }

    /**
     * 修改平台管理-视频管理
     *
     * @param baseVideoConfiguration 平台管理-视频管理
     * @return 结果
     */
    @Override
    public int updateBaseVideoConfiguration(BaseVideoConfiguration baseVideoConfiguration) {
        return baseVideoConfigurationMapper.updateBaseVideoConfiguration(baseVideoConfiguration);
    }

    /**
     * 批量删除平台管理-视频管理
     *
     * @param ids 需要删除的平台管理-视频管理主键
     * @return 结果
     */
    @Override
    public int deleteBaseVideoConfigurationByIds(List<String> ids) {
        return baseVideoConfigurationMapper.deleteBaseVideoConfigurationByIds(ids);
    }

    /**
     * 删除平台管理-视频管理信息
     *
     * @param id 平台管理-视频管理主键
     * @return 结果
     */
    @Override
    public int deleteBaseVideoConfigurationById(String id) {
        return baseVideoConfigurationMapper.deleteBaseVideoConfigurationById(id);
    }
}
