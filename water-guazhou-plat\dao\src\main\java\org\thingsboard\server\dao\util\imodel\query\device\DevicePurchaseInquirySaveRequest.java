package org.thingsboard.server.dao.util.imodel.query.device;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseInquiry;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class DevicePurchaseInquirySaveRequest extends SaveRequest<DevicePurchaseInquiry> {
    // 采购单子表ID
    @NotNullOrEmpty
    private String purchaseDetailId;

    // 供应商ID
    @NotNullOrEmpty
    private String supplierId;

    // 联系人
    @NotNullOrEmpty
    private String contact;

    // 联系方式
    @NotNullOrEmpty
    private String contactPhone;

    // 单价
    @NotNullOrEmpty
    private Double price;

    // 意向供应商
    @NotNullOrEmpty
    private Boolean intentionSupplier = false;

    // 附件
    private String file;

    // 询价日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inquiryTime;

    @Override
    protected DevicePurchaseInquiry build() {
        DevicePurchaseInquiry entity = new DevicePurchaseInquiry();
        entity.setTenantId(tenantId());
        entity.setCreateTime(createTime());
        entity.setPurchaseDetailId(purchaseDetailId);
        commonSet(entity);
        return entity;
    }

    @Override
    protected DevicePurchaseInquiry update(String id) {
        DevicePurchaseInquiry entity = new DevicePurchaseInquiry();
        entity.setId(id);
        commonSet(entity);
        entity.setTenantId(tenantId());
        return entity;
    }

    private void commonSet(DevicePurchaseInquiry entity) {
        entity.setSupplierId(supplierId);
        entity.setContact(contact);
        entity.setContactPhone(contactPhone);
        entity.setPrice(price);
        entity.setIntentionSupplier(intentionSupplier);
        entity.setFile(file);
        entity.setInquiryTime(inquiryTime);
    }
}