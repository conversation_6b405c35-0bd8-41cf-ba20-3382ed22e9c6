package org.thingsboard.server.dao.model.sql.smartProduction.sludge;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_sludge")
// 污泥分析
public class Sludge {
    // id
    @TableId
    private String id;

    private Date time;

    @TableField(exist = false)
    private String timeMonth;

    private String carNo;

    private BigDecimal gross;

    private BigDecimal tare;

    private BigDecimal net;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

}
