<!-- 电耗分析 -->
<template>
  <div class="wrapper">
    <CardSearch ref="cardSearch" :config="cardSearchConfig" />
    <div class="main">
      <div class="left">
        <CardTable
          ref="refCardTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
      <div class="right">
        <!-- 详情列表 -->
        <SLCard class="card-chart" title=" ">
          <template #title>
            <div class="card-title">
              <span class="title"> {{ state.detailTitle }}</span>
            </div>
          </template>
          <CardTable
            ref="refDetail"
            class="card-table"
            :config="detailCardTableConfig"
          />
        </SLCard>
        <!-- 详情折线图 -->
        <SLCard title=" " class="card-chart">
          <template #title>
            <div class="card-title">
              <span class="title"> {{ state.zxTitle }}</span>
            </div>
          </template>
          <div ref="zxDiv" v-loading="totalLoading" class="chart-box">
            <!-- 折线图 -->
            <VChart
              ref="refzxChart"
              :theme="appStore.isDark ? 'dark' : 'light'"
              :option="state.chartOption"
            ></VChart>
          </div>
        </SLCard>
        <!-- 详情树状图 -->
        <SLCard title=" " class="card-chart">
          <template #title>
            <div class="card-title">
              <span class="title"> {{ state.szTitle }}</span>
            </div>
          </template>
          <div ref="szDiv" v-loading="totalLoading" class="chart-box">
            <!-- 树状图 -->
            <VChart
              ref="refszChart"
              :theme="appStore.isDark ? 'dark' : 'light'"
              :option="state.szChartOption"
            >
            </VChart>
          </div>
        </SLCard>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs';
import elementResizeDetectorMaker from 'element-resize-detector';
import { Download, Search } from '@element-plus/icons-vue';
import { useAppStore } from '@/store';
import { lineOption, barOption } from '../../echartsData/echart';
import { IECharts } from '@/plugins/echart';
import {
  getWaterSupplyAndEnergyData,
  getWaterSupplyAndEnergyDataDetail
} from '@/api/headwatersManage/statisticalAnalysis';
import { TrueExcel } from '@/utils/exportExcel';

const appStore = useAppStore();
const excel = new TrueExcel();
const erd = elementResizeDetectorMaker();
const state = reactive<{
  queryType: 'day' | 'month' | 'year';
  chartOption: any;
  szChartOption: any;
  detailTitle: string;
  zxTitle: string;
  szTitle: string;
}>({
  queryType: 'day',
  chartOption: null,
  szChartOption: null,
  detailTitle: '',
  zxTitle: '',
  szTitle: ''
});
const totalLoading = ref<boolean>(true);
const refzxChart = ref<IECharts>();
const refszChart = ref<IECharts>();
const cardSearch = ref<ICardSearchIns>();
const refCardTable = ref<ICardTableIns>();
const refDetail = ref<ICardTableIns>();
const zxDiv = ref<any>();
const szDiv = ref<any>();
// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    queryType: 'day',
    date: dayjs().format()
  },
  filters: [
    { type: 'input', label: '水源名字', field: 'name' },
    {
      type: 'radio-button',
      field: 'queryType',
      options: [
        { label: '日分析', value: 'day' },
        { label: '月分析', value: 'month' },
        { label: '年分析', value: 'year' }
      ],
      label: '分析类型',
      onChange: (val: any) => {
        const date = cardSearchConfig.filters?.find(
          (filter) => filter.field === 'date'
        ) as any;
        date.type =
          val === 'month' ? 'month' : val === 'year' ? 'year' : 'date';
        state.queryType = val;
      }
    },
    { type: 'date', label: '选择时间', field: 'date', clearable: false },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          // perm: $btnPerms('user_manage_addUser'),
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportWaterQuality()
        }
      ]
    }
  ]
});

// 定义动态表头初始化数据
// let weekDate = reactive<IFormTableColumn[]>([])

// 初始化水源列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  highlightCurrentRow: true,
  columns: [
    { prop: 'name', label: '水源名称', minWidth: 100 },
    { prop: 'totalFlow', label: '取水量', unit: '(m³)', minWidth: 100 },
    { prop: 'energy', label: '用电量', unit: '(kw.h)', minWidth: 120 },
    {
      prop: 'totalFlow',
      label: '本期吨水电耗',
      unit: '(kw.h/m³)',
      minWidth: 180
    },
    {
      prop: 'lastTimeUnitConsumption',
      label: '上期吨水电耗',
      unit: '(kw.h/m³)',
      minWidth: 180
    },
    {
      prop: 'differenceValue',
      label: '吨水电耗差值',
      unit: '(kw.h/m³)',
      minWidth: 180
    },
    { prop: 'changeRate', label: '变化率', unit: '(%)' }
  ],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  handleRowClick: (row: any) => showDetail(row),
  pagination: {
    hide: true
  }
});

// 选择水源电耗时间消耗列表
const detailCardTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  currentRowKey: 'id',
  highlightCurrentRow: false,
  columns: [
    { prop: 'date', label: '时间', unit: '(日)', width: '100px' },
    { prop: 'totalFlow', label: '取水量', unit: '(m³)', width: '120px' },
    { prop: 'energy', label: '用电量', unit: '(kw.h)', width: '120px' },
    {
      prop: 'unitConsumption',
      label: '吨位耗电',
      unit: '(kw.h/m³)',
      minWidth: 150
    },
    {
      prop: 'differenceValue',
      label: '吨水电耗差值',
      unit: '(kw.h/m³)',
      minWidth: 180
    },
    { prop: 'changeRate', label: '变化率', unit: '(%)', width: '100px' }
  ],
  operations: [],
  showSummary: false,
  pagination: {
    hide: true
  },
  spanMethod: ({ rowIndex, columnIndex }) => {
    let rowspan = 1;
    let colspan = 1;
    if (columnIndex === 4 || columnIndex === 5) {
      if (rowIndex === 0) {
        rowspan = detailCardTableConfig.columns.length - 1;
        colspan = 1;
      } else {
        rowspan = 0;
        colspan = 0;
      }
    }
    return {
      rowspan,
      colspan
    };
  }
});

onMounted(() => {
  refreshData();
});

// 刷新列表 模拟数据
const refreshData = () => {
  cardTableConfig.loading = true;
  const queryParams = (cardSearch.value?.queryParams as any) || {};
  const params: any = {
    queryType: queryParams.queryType,
    start: dayjs(queryParams.date).startOf(state.queryType).valueOf(),
    end: dayjs(queryParams.date).endOf(state.queryType).valueOf(),
    name: queryParams.name
  };

  getWaterSupplyAndEnergyData(params).then((res) => {
    detailCardTableConfig.loading = true;
    const data = res.data.data;
    cardTableConfig.dataList = data;
    cardTableConfig.currentRow = data[0];
    showDetail(data[0]);
  });
};

// 点击列表行显示详情信息
const showDetail = (row: any) => {
  const queryParams = (cardSearch.value?.queryParams as any) || {};
  state.detailTitle = row.name + '数据详情';
  state.zxTitle = row.name + '取水量、用电量曲线';
  state.szTitle = row.name + '吨水电耗曲线';
  const datailDataList: any = [
    {
      date: row.time,
      totalFlow: row.totalFlow,
      energy: row.energy,
      unitConsumption: row.unitConsumption,
      differenceValue: row.differenceValue,
      changeRate: row.changeRate
    },
    {
      date: row.lastTime,
      totalFlow: row.lastTimeTotalFlow,
      energy: row.lastTimeEnergy,
      unitConsumption: row.lastTimeUnitConsumption,
      differenceValue: row.differenceValue,
      changeRate: row.changeRate
    }
  ];
  detailCardTableConfig.dataList = datailDataList;
  const params: any = {
    stationId: row.stationId,
    queryType: queryParams.queryType,
    start: dayjs(queryParams.date).startOf(state.queryType).valueOf(),
    end: dayjs(queryParams.date).endOf(state.queryType).valueOf()
  };
  totalLoading.value = true;
  getWaterSupplyAndEnergyDataDetail(params).then((res) => {
    const data = res.data.data;
    refuseChart(data);
  });
  cardTableConfig.loading = false;
  detailCardTableConfig.loading = false;
};

// 加载图表
const refuseChart = (data: any) => {
  totalLoading.value = true;
  const flowList = data.flowList;
  const dataX: string[] = [];
  for (const i in flowList) {
    dataX.push(
      state.queryType === 'day'
        ? parseInt(i) + '时'
        : state.queryType === 'month'
          ? parseInt(i) + 1 + '日'
          : parseInt(i) + 1 + '月'
    );
  }
  // 取水量、用电量曲线
  const zxChartOption = lineOption();
  zxChartOption.yAxis[0].name = '电耗(kw/h)';
  zxChartOption.xAxis.data = dataX;
  zxEcharts(data, zxChartOption);

  // 吨水电耗曲线
  const szChartOption = barOption();
  szChartOption.xAxis.data = dataX;
  szEcharts(data, szChartOption);
};

// 折线图图表
const zxEcharts = (data: any, option: any) => {
  console.log(data.lastTimeFlowList);
  option.series = [];
  const serie = {
    name: '',
    smooth: true,
    data: [],
    type: 'line',
    markPoint: {
      data: [
        {
          type: 'max',
          name: '最大值',
          label: {
            fontSize: 12,
            color: appStore.isDark ? '#ffffff' : '#000000'
          }
        },
        {
          type: 'min',
          name: '最小值',
          label: {
            color: appStore.isDark ? '#ffffff' : '#000000'
          }
        }
      ]
    }
  };
  const lastTimeFlowList = data?.lastTimeFlowList as any[];
  const lastTimeFlowSerie = JSON.parse(JSON.stringify(serie));
  lastTimeFlowSerie.data = lastTimeFlowList.map((item) => item.value);
  lastTimeFlowSerie.name = '取水量' + lastTimeFlowList[0].ts;
  option.series.push(lastTimeFlowSerie);

  const flowList = data?.flowList as any[];
  const flowSerie = JSON.parse(JSON.stringify(serie));
  flowSerie.data = flowList;
  flowSerie.name = '取水量' + flowList[0].ts;
  option.series.push(flowSerie);

  const energyList = data?.energyList as any[];
  const energySerie = JSON.parse(JSON.stringify(serie));
  energySerie.yAxisIndex = 1;
  energySerie.data = energyList.map((item) => item.value);
  energySerie.name = '耗电量' + energyList[0].ts;
  option.series.push(energySerie);

  const lastTimeEnergyList = data?.lastTimeEnergyList as any[];
  const lastTimeEnergySerie = JSON.parse(JSON.stringify(serie));
  lastTimeEnergySerie.yAxisIndex = 1;
  lastTimeEnergySerie.data = lastTimeEnergyList.map((item) => item.value);
  lastTimeEnergySerie.name = '耗电量' + lastTimeEnergyList[0].ts;
  option.series.push(lastTimeEnergySerie);

  console.log('这显然图', option.series);
  refzxChart.value?.clear();
  nextTick(() => {
    if (zxDiv.value) {
      erd.listenTo(zxDiv.value, () => {
        state.chartOption = option;
        refzxChart.value?.resize();
      });
    }
  });
};

// 树状图图表
const szEcharts = (data: any, option: any) => {
  option.series = [];
  const serie = {
    name: '',
    type: 'bar',
    barMaxWidth: 40,
    data: [],
    markPoint: {
      data: [
        {
          type: 'max',
          name: '最大值',
          label: {
            fontSize: 12,
            color: appStore.isDark ? '#ffffff' : '#000000'
          }
        },
        {
          type: 'min',
          name: '最小值',
          label: {
            color: appStore.isDark ? '#ffffff' : '#000000'
          }
        }
      ]
    }
  };
  const lastTimeUnitConsumptionList =
    data?.lastTimeUnitConsumptionList as any[];
  const lastTimeUnitConsumptionSerie = JSON.parse(JSON.stringify(serie));
  lastTimeUnitConsumptionSerie.data = lastTimeUnitConsumptionList.map(
    (item) => item.value
  );
  lastTimeUnitConsumptionSerie.name =
    '吨水能耗' + lastTimeUnitConsumptionList[0].ts;
  option.series.push(lastTimeUnitConsumptionSerie);

  const unitConsumption = data?.unitConsumption as any[];
  const unitConsumptionSerie = JSON.parse(JSON.stringify(serie));
  unitConsumptionSerie.data = unitConsumption.map((item) => item.value);
  unitConsumptionSerie.name = '吨水能耗' + unitConsumption[0].ts;
  option.series.push(unitConsumptionSerie);

  refszChart.value?.clear();
  nextTick(() => {
    const options: any = { callOnAdd: true };
    const erd = elementResizeDetectorMaker(options);
    if (zxDiv.value) {
      erd.listenTo(szDiv.value, () => {
        state.szChartOption = option;
        refszChart.value?.resize();
      });
    }
  });
  totalLoading.value = false;
};

// 导出水量报告
const _exportWaterQuality = () => {
  // const width = refzxChart.value?.getWidth()
  // const height = refzxChart.value?.getHeight()
  // const img1 = {
  //   src: refzxChart.value?.getDataURL({
  //     type: 'png',
  //     pixelRatio: window.devicePixelRatio || 1
  //   }),
  //   width: 900,
  //   height: 400
  // }
  // const img2 = {
  //   src: refszChart.value?.getDataURL({
  //     type: 'png',
  //     pixelRatio: window.devicePixelRatio || 1
  //   }),
  //   width: 900,
  //   height: 400
  // }
  excel.addElTable(refCardTable.value);
  // excel.addImage(img1)
  // excel.addImage(img2)
  excel.export();
};
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  justify-content: space-between;
  height: 100%;

  .left {
    width: 550px;
    height: 100%;
    min-width: 46%;
  }

  .right {
    height: calc(100% - 100px);
    width: calc(54% - 15px);

    .card-table {
      height: 100%;
    }
  }
}

.card-title {
  font-size: 16px;
}

.card-chart {
  width: 100%;
  height: calc(33% - 15px);
  margin-bottom: 15px;

  &:first-child {
    height: calc(33% - 15px);
  }

  &:last-child {
    height: 34%;
    margin-top: 15px;
    margin-bottom: 0;
  }
}

.chart-box {
  width: 100%;
  height: 100%;
}
</style>
