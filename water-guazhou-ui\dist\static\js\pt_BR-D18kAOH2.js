import{o as u}from"./_commonjsHelpers-DCkdB7M8.js";import{r as m}from"./_commonjs-dynamic-modules-DfYEAvWy.js";function l(e,a){for(var i=0;i<a.length;i++){const o=a[i];if(typeof o!="string"&&!Array.isArray(o)){for(const r in o)if(r!=="default"&&!(r in e)){const s=Object.getOwnPropertyDescriptor(o,r);s&&Object.defineProperty(e,r,s.get?s:{enumerable:!0,get:()=>o[r]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var n,d,t={},p={get exports(){return t},set exports(e){t=e}};n=p,(d=function(e,a){Object.defineProperty(a,"__esModule",{value:!0}),a.default={_decimalSeparator:",",_thousandSeparator:".",_percentPrefix:null,_percentSuffix:"%",_date_millisecond:"mm:ss SSS",_date_second:"HH:mm:ss",_date_minute:"HH:mm",_date_hour:"HH:mm",_date_day:"dd MMM",_date_week:"ww",_date_month:"MMM",_date_year:"yyyy",_duration_millisecond:"SSS",_duration_second:"ss",_duration_minute:"mm",_duration_hour:"hh",_duration_day:"dd",_duration_week:"ww",_duration_month:"MM",_duration_year:"yyyy",_era_ad:"DC",_era_bc:"AC",A:"",P:"",AM:"",PM:"","A.M.":"","P.M.":"",January:"Janeiro",February:"Fevereiro",March:"Março",April:"Abril",May:"Maio",June:"Junho",July:"Julho",August:"Agosto",September:"Setembro",October:"Outubro",November:"Novembro",December:"Dezembro",Jan:"Jan",Feb:"Fev",Mar:"Mar",Apr:"Abr","May(short)":"Mai",Jun:"Jun",Jul:"Jul",Aug:"Ago",Sep:"Set",Oct:"Out",Nov:"Nov",Dec:"Dez",Sunday:"Domingo",Monday:"Segunda-feira",Tuesday:"Terça-feira",Wednesday:"Quarta-feira",Thursday:"Quinta-feira",Friday:"Sexta-feira",Saturday:"Sábado",Sun:"Dom",Mon:"Seg",Tue:"Ter",Wed:"Qua",Thu:"Qui",Fri:"Sex",Sat:"Sáb",_dateOrd:function(i){return"º"},"Zoom Out":"Reduzir Zoom",Play:"Play",Stop:"Parar",Legend:"Legenda","Click, tap or press ENTER to toggle":"Clique, toque ou pressione ENTER para alternar",Loading:"Carregando",Home:"Início",Chart:"Gráfico","Serial chart":"Gráfico Serial","X/Y chart":"Gráfico XY","Pie chart":"Gráfico de Pizza","Gauge chart":"Gráfico Indicador","Radar chart":"Gráfico de Radar","Sankey diagram":"Diagrama Sankey","Chord diagram":"Diagram Chord","Flow diagram":"Diagrama Flow","TreeMap chart":"Gráfico de Mapa de Árvore",Series:"Séries","Candlestick Series":"Séries do Candlestick","Column Series":"Séries de Colunas","Line Series":"Séries de Linhas","Pie Slice Series":"Séries de Fatias de Pizza","X/Y Series":"Séries de XY",Map:"Mapa","Press ENTER to zoom in":"Pressione ENTER para aumentar o zoom","Press ENTER to zoom out":"Pressione ENTER para diminuir o zoom","Use arrow keys to zoom in and out":"Use as setas para diminuir ou aumentar o zoom","Use plus and minus keys on your keyboard to zoom in and out":"Use as teclas mais ou menos no seu teclado para diminuir ou aumentar o zoom",Export:"Exportar",Image:"Imagem",Data:"Dados",Print:"Imprimir","Click, tap or press ENTER to open":"Clique, toque ou pressione ENTER para abrir","Click, tap or press ENTER to print.":"Clique, toque ou pressione ENTER para imprimir","Click, tap or press ENTER to export as %1.":"Clique, toque ou pressione ENTER para exportar como %1.",'To save the image, right-click this link and choose "Save picture as..."':'Para salvar a imagem, clique no link com o botão da direira e escolha "Salvar imagem como..."','To save the image, right-click thumbnail on the left and choose "Save picture as..."':'Para salvar, clique na imagem à esquerda com o botão direito e escolha "Salvar imagem como..."',"(Press ESC to close this message)":"(Pressione ESC para fechar esta mensagem)","Image Export Complete":"A exportação da imagem foi completada","Export operation took longer than expected. Something might have gone wrong.":"A exportação da imagem demorou mais do que o experado. Algo deve ter dado errado.","Saved from":"Salvo de",PNG:"",JPG:"",GIF:"",SVG:"",PDF:"",JSON:"",CSV:"",XLSX:"","Use TAB to select grip buttons or left and right arrows to change selection":"Use TAB para selecionar os botões ou setas para a direita ou esquerda para mudar a seleção","Use left and right arrows to move selection":"Use as setas para a esquerda ou direita para mover a seleção","Use left and right arrows to move left selection":"Use as setas para a esquerda ou direita para mover a seleção da esquerda","Use left and right arrows to move right selection":"Use as setas para a esquerda ou direita para mover a seleção da direita","Use TAB select grip buttons or up and down arrows to change selection":"Use TAB para selecionar os botões ou setas para cima ou para baixo para mudar a seleção","Use up and down arrows to move selection":"Use as setas para cima ou para baixo para mover a seleção","Use up and down arrows to move lower selection":"Use as setas para cima ou para baixo para mover a seleção de baixo","Use up and down arrows to move upper selection":"Use as setas para cima ou para baixo para mover a seleção de cima","From %1 to %2":"De %1 até %2","From %1":"De %1","To %1":"Até %1","No parser available for file: %1":"Não há um interpretador para este arquivo: %1","Error parsing file: %1":"Erro analizando o arquivo: %1","Unable to load file: %1":"O arquivo não pôde ser carregado: %1","Invalid date":"Data inválida"}}(m,t))!==void 0&&(n.exports=d);const S=l({__proto__:null,default:u(t)},[t]);export{S as p};
