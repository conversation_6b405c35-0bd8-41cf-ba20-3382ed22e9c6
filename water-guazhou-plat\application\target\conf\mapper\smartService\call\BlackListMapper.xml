<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.call.BlacklistMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.call.Blacklist">
        select a.*
        from tb_service_call_blacklist a
        where a.phone like '%'||#{keywords}||'%' or a.remark like '%'||#{keywords}||'%'
        order by a.id
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_call_blacklist a
        where a.phone like '%'||#{keywords}||'%' or a.remark like '%'||#{keywords}||'%'
    </select>
</mapper>