package org.thingsboard.server.dao.store;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.store.*;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceStorageJournalCheckOutRequest;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceStorageJournalPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.RestDeviceInfoPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.RestDeviceStorageJournalPageRequest;

import java.util.Date;
import java.util.List;

public interface DeviceStorageJournalService {
    /**
     * 分页条件查询设备台账
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DeviceStorageJournalResponse> findAllConditional(DeviceStorageJournalPageRequest request);

    /**
     * 保存设备台账
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    DeviceStorageJournal save(DeviceStorageJournal entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(DeviceStorageJournal entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量保存
     *
     * @param journals 实体列表
     * @return 保存好的数据
     */
    List<DeviceStorageJournal> saveAll(List<DeviceStorageJournal> journals);

    /**
     * 单个设备检出
     *
     * @param id             唯一标识
     * @param storeOutItemId 检出到那个出库单条目
     * @return 是否成功
     */
    @Deprecated
    boolean checkout(String id, String storeOutItemId);

    /**
     * 批量检出设备
     *
     * @param request 明细
     * @return 是否成功
     */
    boolean checkoutAll(DeviceStorageJournalCheckOutRequest request);

    /**
     * 检入单项设备
     *
     * @param id 出库单条目id
     * @return 是否成功
     */
    boolean checkinViaStoreOut(String id);

    /**
     * 批量检入多项设备
     *
     * @param remove 出库单条目id
     * @return 是否成功
     */
    boolean checkinAllViaStoreOut(List<String> remove);

    /**
     * 设备余量信息查询（自动分割）
     *
     * @param request 分页请求
     * @return 被分割的余量信息
     */
    IPage<MainRestDeviceStorageJournal> findRestConditional(RestDeviceStorageJournalPageRequest request);

    /**
     * 设备余量信息查询
     *
     * @param request 分页请求
     * @return 余量信息
     */
    IPage<RestDeviceStorageJournal> findRestWithoutSplitConditional(RestDeviceStorageJournalPageRequest request);

    /**
     * 剩余设备信息查询
     *
     * @param request 分页请求
     * @return 余量信息
     */
    IPage<RestDeviceStorageInfo> findRestDeviceInfoConditional(RestDeviceInfoPageRequest request);

    /**
     * 获取库存设备详细信息
     *
     * @param id 唯一标识
     * @return 详细信息
     */
    DetailDeviceStorageInfo detail(String id);

    /**
     * 更新报废时间，目前会在报废设备模块中批量报废，一般不会单个报废
     *
     * @param deviceLabelCode 库存设备编号
     * @param receiveTime     领用时间
     * @param tenantId        客户id
     * @return 是否成功
     */
    @Deprecated
    boolean updateScrappedTime(String deviceLabelCode, Date receiveTime, String tenantId);

    boolean canOperate(String storeOutId, String currentUserId);

    void collect(JSONObject params);
}
