package org.thingsboard.server.dao.maintain;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTask;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTaskItem;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemReportRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemSaveRequest;

import java.util.List;

public interface SMMaintainTaskItemService {
    /**
     * 分页条件查询养护管理任务任务条目
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SMMaintainTaskItem> findAllConditional(SMMaintainTaskItemPageRequest request);

    /**
     * 保存养护管理任务任务条目
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SMMaintainTaskItem save(SMMaintainTaskItemSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SMMaintainTaskItem entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量保存
     *
     * @param items 实体信息
     * @return 保存好的数据
     */
    List<SMMaintainTaskItem> saveAll(List<SMMaintainTaskItemSaveRequest> items);

    /**
     * 上报任务完成信息
     *
     * @param req 详细信息
     * @return 是否成功
     */
    boolean report(SMMaintainTaskItemReportRequest req);

    /**
     * 通过其父级{@link SMMaintainTask#getId()} 任务id}来删除
     *
     * @param id 父级的唯一表示
     * @return 是否成功
     */
    boolean removeAllByTaskId(String id);

}
