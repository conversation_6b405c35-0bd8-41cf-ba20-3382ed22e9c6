/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.telemetryAttribute;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.thingsboard.server.common.data.HasName;
import org.thingsboard.server.common.data.HasTenantId;
import org.thingsboard.server.common.data.SearchTextBasedWithAdditionalInfo;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TelemetryAttributeId;
import org.thingsboard.server.common.data.id.TenantId;

@Data
@EqualsAndHashCode(callSuper = true)
public class TelemetryAttribute extends SearchTextBasedWithAdditionalInfo<TelemetryAttributeId> implements HasTenantId, HasName {
    private TenantId tenantId;
    private DeviceId deviceId;
    //属性类别
    private String attributeType;
    //协议名称
    private String agreementName;
    //滤波次数
    private String harmonicNumber;
    //显示名称
    private String showName;
    //数据类型
    private String dataType;
    //数据偏移量
    private String dataOffset;
    //采样系数
    private String coefficient;
    //采样最小值
    private String minValue;
    //采样最大值
    private String maxValue;
    //公式
    private String formula;
    //统计数据类型
    private String statistics;
    //单位
    private String unit;
    //单位系数
    private String unitCoefficient;
    //字节顺序
    private String byteOrder;

    public TelemetryAttribute(TelemetryAttributeId id){
        super(id);
    }

    @Override
    public String getSearchText() {
        return showName;
    }


    @Override
    public String getName() {
        return showName;
    }
}
