<?xml version="1.0" encoding="UTF-8" ?>
<!--

    Copyright © 2016-2019 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<!DOCTYPE configuration>
<configuration>

    <appender name="fileLogAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${BASE}\logs/install.log</file>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${BASE}\logs/install.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{ISO8601} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.thingsboard.server.install" level="INFO">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="org.thingsboard.server.service.install" level="INFO">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="org.thingsboard.server.ThingsboardInstallApplication" level="ERROR">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="org.apache.tomcat.jdbc.pool" level="ERROR">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="org.thingsboard.server" level="INFO" />
    <logger name="akka" level="INFO" />

    <root level="INFO">
        <appender-ref ref="fileLogAppender"/>
    </root>

</configuration>
