package org.thingsboard.server.dao.gis;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisFileListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisFile;
import org.thingsboard.server.dao.sql.gis.GisFileMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class GisFileServiceImpl implements GisFileService {

    @Autowired
    private GisFileMapper gisFileMapper;

    @Override
    public void save(GisFile entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setUploadTime(new Date());
            gisFileMapper.insert(entity);
        } else {
            gisFileMapper.updateById(entity);
        }
    }

    @Override
    public PageData<GisFile> findList(GisFileListRequest request, TenantId tenantId) {
        Page<GisFile> pageRequest = new Page<>(request.getPage(), request.getSize());
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        IPage<GisFile> pageResult = gisFileMapper.findList(pageRequest, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void remove(List<String> ids) {
        gisFileMapper.deleteBatchIds(ids);
    }

    @Override
    public void changeStatus(List<String> ids, String status) {
        GisFile param = new GisFile();
        param.setStatus(status);
        QueryWrapper<GisFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        gisFileMapper.update(param, queryWrapper);
    }
}
