package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyFlow;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class SoConstructionApplyFlowSaveRequest extends SaveRequest<SoConstructionApplyFlow> {
    // 工作编号
    @NotNullOrEmpty
    private String code;

    // 所属工程实施编号
    @NotNullOrEmpty
    private String constructionApplyCode;

    // 工作名称
    @NotNullOrEmpty
    private String workName;

    // 阶段
    @NotNullOrEmpty
    private String workStage;

    // 开始时间
    @NotNullOrEmpty
    private Date beginTime;

    // 完成时间
    @NotNullOrEmpty
    private Date endTime;

    // 负责人
    private String headUser;

    // 负责人电话
    private String headUserPhone;

    // 工作内容
    private String workContent;

    // 说明
    private String remark;

    @Override
    protected SoConstructionApplyFlow build() {
        SoConstructionApplyFlow entity = new SoConstructionApplyFlow();
        entity.setTenantId(tenantId());
        entity.setCreateTime(createTime());
        entity.setCode(code);
        entity.setConstructionApplyCode(constructionApplyCode);
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionApplyFlow update(String id) {
        SoConstructionApplyFlow entity = new SoConstructionApplyFlow();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionApplyFlow entity) {
        entity.setWorkName(workName);
        entity.setWorkStage(workStage);
        entity.setBeginTime(beginTime);
        entity.setEndTime(endTime);
        entity.setHeadUser(headUser);
        entity.setHeadUserPhone(headUserPhone);
        entity.setWorkContent(workContent);
        entity.setRemark(remark);
        entity.setCreator(currentUserUUID());
    }

}