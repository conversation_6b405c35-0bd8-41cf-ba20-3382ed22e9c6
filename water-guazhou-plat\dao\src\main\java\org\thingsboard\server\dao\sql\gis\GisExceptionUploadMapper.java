package org.thingsboard.server.dao.sql.gis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.GisExceptionUploadListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisExceptionUpload;

@Mapper
public interface GisExceptionUploadMapper extends BaseMapper<GisExceptionUpload> {
    IPage<GisExceptionUpload> findList(IPage<GisExceptionUpload> pageRequest, @Param("param") GisExceptionUploadListRequest request);
}
