import{r as n,u as d,a8 as t,a9 as s}from"./index-r0dFAfgr.js";import{f as o}from"./DateFormatter-Bm9a68Ax.js";import{g as m}from"./OutsideWorkOrder-C6s8joBt.js";import{g as c,a as b,b as u}from"./index-CpGhZCTT.js";const a=n({workOrderResourceList:[],workOrderTypeList:[],WorkOrderEmergencyLevelList:[],WorkOrderProcessLevelList:[]});function E(){m({status:"1",page:1,size:999}).then(e=>{a.workOrderResourceList=s(e.data.data.data||[],"children",{label:"name",value:"name"})}),c("1").then(e=>{a.workOrderTypeList=s(e.data.data||[],"children",{label:"name",value:"name"})}),b("1").then(e=>{a.WorkOrderEmergencyLevelList=s(e.data.data||[],"children",{label:"name",value:"id"})}),u("1").then(e=>{a.WorkOrderProcessLevelList=s(e.data.data||[],"children",{label:"name",value:"name"})})}E();var p;(p=d().user)!=null&&p.name;const O=()=>[{minWidth:180,prop:"serialNo",label:"工单编号"},{minWidth:100,prop:"title",label:"标题"},{minWidth:120,prop:"source",label:"来源"},{minWidth:120,prop:"type",label:"类型"},{minWidth:120,prop:"level",label:"紧急程度",tag:!0,tagColor:e=>{var r;return((r=a.WorkOrderEmergencyLevelList.find(l=>l.value===e.level))==null?void 0:r.color)||""},formatter:e=>{var r;return(r=a.WorkOrderEmergencyLevelList.find(l=>l.value===e.level))==null?void 0:r.label}},{minWidth:100,prop:"organizerName",label:"发起人"},{minWidth:160,prop:"createTime",label:"发起时间",formatter:e=>o(e.createTime)},{minWidth:120,prop:"processUserName",label:"处理人"},{minWidth:120,prop:"processLevelLabel",label:"处理级别",formatter:e=>I(e.processLevel)},{minWidth:160,prop:"estimatedFinishTime",label:"预计完成时间",formatter:e=>o(e.estimatedFinishTime)},{minWidth:160,prop:"completeTime",label:"完成时间",formatter:e=>o(e.completeTime)},{minWidth:160,prop:"updateTime",label:"最后更新时间",formatter:e=>o(e.updateTime)}],T=()=>t(()=>a.workOrderResourceList),P=()=>t(()=>a.WorkOrderEmergencyLevelList),W=e=>e==="紧急"?"orange":e==="非常紧急"?"red":"green",G=()=>t(()=>a.workOrderTypeList);var v=(e=>(e.待派单="PENDING",e.待接单="ASSIGN",e.处理中="RESOLVING",e.到场="ARRIVING",e.处理="PROCESSING",e.待审核="SUBMIT",e.审核中="CHARGEBACK_REVIEW",e.复审中="REVIEW",e.审核退回="REJECTED",e.审核通过="APPROVED",e.已退单="CHARGEBACK",e.已终止="TERMINATED",e))(v||{});const f={PENDING_REVIEW:"待审核",ASSIGNED:"已分派",PROCESSING:"处理中",COMPLETED:"已完成",REJECTED:"已驳回",PENDING:"待派单",ASSIGN:"待接单",RESOLVING:"处理中",ARRIVING:"到场",SUBMIT:"待审核",CHARGEBACK_REVIEW:"审核中",REVIEW:"复审中",APPROVED:"审核通过",CHARGEBACK:"已退单",TERMINATED:"已终止"},R=e=>{const r=[{value:"PENDING",label:"待派单"},{value:"ASSIGN",label:"待接单"},{value:"RESOLVING",label:"处理中"},{value:"ARRIVING",label:"到场"},{value:"PROCESSING",label:"处理"},{value:"SUBMIT",label:"待审核"},{value:"CHARGEBACK_REVIEW",label:"审核中"},{value:"REVIEW",label:"复审中"},{value:"REJECTED",label:"审核退回"},{value:"APPROVED",label:"审核通过"},{value:"CHARGEBACK",label:"已退单"},{value:"TERMINATED",label:"已终止"}];return e&&r.unshift({label:"全部",value:""}),r},g=e=>{var r;return(r=R().find(l=>l.value===e))==null?void 0:r.label},D=["APPROVED","CHARGEBACK","TERMINATED"],h=e=>[{label:"发起",value:"PENDING"},{label:"派单",value:"ASSIGN"},{label:"接单",value:"RESOLVING"},{label:"到场",value:"ARRIVING"},{label:"处理",value:"PROCESSING"},{label:"完成",value:"SUBMIT"},{label:"审核通过",value:"APPROVED"},{label:"审核退回",value:"REJECTED"}],C=e=>[{label:"待签收",value:"PENDING"},{label:"已签收",value:"ASSIGN"},{label:"处理中",value:"RESOLVING"},{label:"提交上报",value:"SUBMIT"},{label:"已完成",value:"APPROVED"}],S=()=>t(()=>a.WorkOrderProcessLevelList),I=e=>{var r;return((r=a.WorkOrderProcessLevelList.find(l=>l.hourTime*60+l.dayTime*1440+l.minuteTime===e))==null?void 0:r.label)||e},k=e=>{const r=a.WorkOrderProcessLevelList.find(l=>l.value===e);if(r)return r.dayTime*1440+r.hourTime*60+r.minuteTime},V=e=>{const r=a.WorkOrderEmergencyLevelList.find(l=>l.value===e);return r==null?void 0:r.label},y=e=>{switch(e){case"CREATE":return[[{label:"标题",prop:"title",cols:5}],[{label:"发起人员",prop:"organizerName"},{label:"紧急程度",prop:"level",tag:!0,colorTag:r=>{var l;return((l=a.WorkOrderEmergencyLevelList.find(i=>i.id===r.level))==null?void 0:l.color)||""},formatter:r=>{var l;return(l=a.WorkOrderEmergencyLevelList.find(i=>i.id===r.level))==null?void 0:l.label}},{label:"来源",prop:"source"}],[{label:"类型",prop:"type"},{label:"处理级别",prop:"processLevelLabel"},{label:"上报人电话",prop:"uploadPhone"}],[{label:"地址",prop:"address",cols:5}],[{label:"描述",prop:"remark",cols:5}],[{label:"直接分派",prop:"isDirectDispatch",formatter:r=>r.isDirectDispatch===!0?"是":"否"},{label:"预计完成时间",prop:"estimatedFinishTime",cols:3}],[{label:"图片",prop:"imgUrl",image:!0,cols:5}],[{label:"录音",prop:"audioUrl",audio:!0,cols:5}],[{label:"视频",prop:"videoUrl",video:!0,cols:5}],[{label:"附件",prop:"otherFileUrl",file:!0,cols:5}]];case"ARRIVING":case"PROCESSING":case"TERMINATED":return[[{label:"处理人",prop:"nextProcessUserName"}],[{label:"备注",prop:"processRemark"}],[{label:"图片",prop:"imgUrl",image:!0,cols:5}],[{label:"录音",prop:"audioUrl",audio:!0,cols:5}],[{label:"视频",prop:"videoUrl",video:!0,cols:5}],[{label:"附件",prop:"otherFileUrl",file:!0,cols:5}]];case"SUBMIT":return[[{label:"完成人",prop:"processUserName"},{label:"指定审核人",prop:"nextProcessUserName"}],[{label:"备注",prop:"processRemark",cols:3}],[{label:"图片",prop:"imgUrl",image:!0,cols:5}],[{label:"录音",prop:"audioUrl",audio:!0,cols:5}],[{label:"视频",prop:"videoUrl",video:!0,cols:5}],[{label:"附件",prop:"otherFileUrl",file:!0,cols:5}]];case"HANDOVER_REVIEW":return[[{label:"申请人",prop:"processUserName"},{label:"转发至",prop:"nextProcessUserName"},{label:"指定审核人",prop:"expectUsername"}],[{label:"备注",prop:"processRemark",cols:5}]];case"CHARGEBACK_REVIEW":return[[{label:"申请人",prop:"processUserName"}],[{label:"审核人",prop:"nextProcessUserName"}]];case"REVIEW":return[[{label:"指定复审人",prop:"nextProcessUserName"}],[{label:"备注",prop:"processRemark"}]];case"APPROVED":case"REJECTED":case"CHARGEBACK":return[[{label:"审核人",prop:"processUserName"}],[{label:"审核备注",prop:"processRemark"}]];case"REASSIGN":return[[{label:"处理人",prop:"nextProcessUserName"}]];default:return[]}};export{v as E,D as S,S as W,G as a,T as b,V as c,W as d,f as e,k as f,P as g,R as h,g as i,O as j,y as k,h as l,C as m};
