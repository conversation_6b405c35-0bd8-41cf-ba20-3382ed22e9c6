package org.thingsboard.server.dao.model.sql.revenue;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 远传表-抄表数据库
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.REMOTE_METER_DATA_TABLE)
@TableName(ModelConstants.REMOTE_METER_DATA_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class RemoteMeterData {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.REMOTE_METER_DATA_CUST_CODE)
    private String custCode;

    @Column(name = ModelConstants.REMOTE_METER_DATA_METER_CODE)
    private String meterCode;

    @Column(name = ModelConstants.REMOTE_METER_DATA_REMOTE_METER_CODE)
    private String remoteMeterCode;

    @Column(name = ModelConstants.REMOTE_METER_DATA_VALUE)
    private BigDecimal value;

    @TableField(exist = false)
    private String lastId;

    @TableField(exist = false)
    private BigDecimal lastValue;

    @TableField(exist = false)
    private Date lastDate;

    @Column(name = ModelConstants.REMOTE_METER_DATA_VALVE_STATE)
    private String valveState;

    @Column(name = ModelConstants.REMOTE_METER_DATA_UPLOAD_TIME)
    private Date uploadTime;

    @Column(name = ModelConstants.REMOTE_METER_DATA_DATA_TIME)
    private Date dataTime;

    @Column(name = ModelConstants.REMOTE_METER_DATA_TYPE)
    private String type;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;



}
