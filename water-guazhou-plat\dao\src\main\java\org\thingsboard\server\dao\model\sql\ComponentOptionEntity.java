package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.COMPONENT_STORAGE_OPTION_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class ComponentOptionEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.COMPONENT_STORAGE_OPTION_TYPE)
    private String type;

    @Column(name = ModelConstants.COMPONENT_STORAGE_OPTION_CODE)
    private String code;

    @Column(name = ModelConstants.COMPONENT_STORAGE_OPTION_USERNAME)
    private String username;

    @Column(name = ModelConstants.COMPONENT_STORAGE_OPTION_TIME)
    private Date time;

    @Column(name = ModelConstants.COMPONENT_STORAGE_OPTION_REMARK)
    private String remark;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.CREATOR_PROPERTY)
    private String creator;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private String nameList;

    @Transient
    private List<ComponentOptionCEntity> details;
}
