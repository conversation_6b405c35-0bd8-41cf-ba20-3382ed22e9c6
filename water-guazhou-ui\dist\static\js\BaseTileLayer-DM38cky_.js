import{b as f,s as y,e as o,y as n,a as b}from"./Point-WxyopZva.js";import{U as d}from"./pe-B8dP0-Ut.js";import"./index-r0dFAfgr.js";import{c as v,t as w,q as U,z as u,w as x,A as I,B,e as q}from"./MapView-DaoQedLH.js";import{e as M}from"./imageBitmapUtils-Db1drMDc.js";var m;const l=new B("0/0/0",0,0,0,void 0);let i=m=class extends v(w(U(q))){constructor(){super(...arguments),this.tileInfo=u.create({spatialReference:f.WebMercator,size:256}),this.type="base-tile",this.fullExtent=new x(-20037508342787e-6,-2003750834278e-5,2003750834278e-5,20037508342787e-6,f.WebMercator),this.spatialReference=f.WebMercator}getTileBounds(e,t,r,a){const s=a||I();return l.level=e,l.row=t,l.col=r,l.extent=s,this.tileInfo.updateTileInfo(l),l.extent=void 0,s}fetchTile(e,t,r,a={}){const{signal:s}=a,p=this.getTileUrl(e,t,r),c={responseType:"image",signal:s,query:{...this.refreshParameters}};return d(p??"",c).then(h=>h.data)}async fetchImageBitmapTile(e,t,r,a={}){const{signal:s}=a;if(this.fetchTile!==m.prototype.fetchTile){const g=await this.fetchTile(e,t,r,a);try{return createImageBitmap(g)}catch(T){throw new y("request:server",`Unable to load tile ${e}/${t}/${r}`,{error:T,level:e,row:t,col:r})}}const p=this.getTileUrl(e,t,r)??"",c={responseType:"blob",signal:s,query:{...this.refreshParameters}},{data:h}=await d(p,c);return M(h,p)}getTileUrl(){throw new y("basetilelayer:gettileurl-not-implemented","getTileUrl() is not implemented")}};o([n({type:u})],i.prototype,"tileInfo",void 0),o([n({type:["show","hide"]})],i.prototype,"listMode",void 0),o([n({readOnly:!0,value:"base-tile"})],i.prototype,"type",void 0),o([n({nonNullable:!0})],i.prototype,"fullExtent",void 0),o([n()],i.prototype,"spatialReference",void 0),i=m=o([b("esri.layers.BaseTileLayer")],i);const E=i;export{E as j};
