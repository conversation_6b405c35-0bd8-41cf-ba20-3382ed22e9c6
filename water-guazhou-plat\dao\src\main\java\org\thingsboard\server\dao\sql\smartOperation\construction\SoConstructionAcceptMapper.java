package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionAccept;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionAcceptPageRequest;

@Mapper
public interface SoConstructionAcceptMapper extends BaseMapper<SoConstructionAccept> {
    IPage<SoConstructionAccept> findByPage(SoConstructionAcceptPageRequest request);

    boolean update(SoConstructionAccept entity);

    boolean updateFully(SoConstructionAccept entity);

    int save(SoConstructionAccept soConstructionAccept);

    @Override
    default int insert(SoConstructionAccept entity) {
        return save(entity);
    }

    String getConstructionCodeById(String id);

    String getIdByConstructionCodeAndTenantId(@Param("constructionCode") String constructionCode, @Param("tenantId") String tenantId);


}
