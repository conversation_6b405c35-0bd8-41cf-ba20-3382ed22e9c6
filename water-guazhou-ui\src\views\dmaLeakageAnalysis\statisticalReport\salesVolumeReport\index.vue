<!-- 售水量报表 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    ></CardTable>
  </TreeBox>
</template>
<script lang="ts" setup>
import { getWaterSupplyTree } from '@/api/company_org'
import { GetDmaPartitionSaleReport } from '@/api/mapservice/dma'
import { getMeterBookAll, getSelectOptions } from '@/api/operatingCharges'
import { ICardSearchIns, ICardTableIns } from '@/components/type'
import { usePartition } from '@/hooks/arcgis'
import { formatterYear, traverse } from '@/utils/GlobalHelper'

const refTable = ref<ICardTableIns>()
const refSearch = ref<ICardSearchIns>()
const TreeData = reactive<SLTreeConfig>({
  data: [],
  title: '选择分区',
  expandOnClickNode: false,
  treeNodeHandleClick: (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      TreeData.currentProject = data
      refreshData()
    }
  }
})

// 列表模式搜索配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'year',
    year: moment().format(formatterYear)
  },
  filters: [
    {
      hidden: true,
      type: 'select',
      label: '选择方式',
      clearable: false,
      field: 'type',
      options: [{ label: '按年', value: 'year' }]
    },
    {
      type: 'year',
      label: '选择年份',
      format: 'YYYY',
      clearable: false,
      field: 'year',
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      type: 'input',
      label: '用户户号',
      field: 'custCode',
      clearable: false
    },
    {
      type: 'select',
      label: '册本名称',
      field: 'meterBookId',
      options: []
    },
    {
      type: 'select',
      label: '用水性质',
      field: 'WaterCategory',
      autoFillOptions(config) {
        getSelectOptions('WaterCategoryType').then(res => {
          config.options = res || []
        })
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          loading: () => !!TableConfig.loading,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ]
})
const initTableColumns = (subColumns?: IFormTableColumn[]): IFormTableColumn[] => {
  return [
    { prop: 'custName', label: '用户名称', minWidth: 120 },
    { prop: 'custCode', label: '用户户号', minWidth: 120 },
    // { prop: 'address', label: '用户地址', minWidth: 140 },
    { prop: 'partitionName', label: '挂接名称', minWidth: 120 },
    { prop: 'meterBookName', label: '册本名称', minWidth: 120 },
    { prop: 'WaterCategory', label: '用水性质', minWidth: 120 },
    {
      prop: 'title',
      label: '销水量(m³)',
      align: 'center',
      subColumns: [
        ...(subColumns
          || Array.from({ length: moment().get('M') + 1 }).map((item, i) => {
            return {
              label: i + 1 + '月',
              prop: i + 1 + '月',
              minWidth: 120
            }
          })),
        { prop: 'sum', label: '合计', minWidth: 120 },
        { prop: 'avg', label: '平均', minWidth: 120 }
      ]
    }
  ]
}
// 列表
const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: initTableColumns(),
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

// 数据获取
const refreshData = async () => {
  try {
    TableConfig.loading = false
    const query = refSearch.value?.queryParams || {}
    const res = await GetDmaPartitionSaleReport({
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      partitionId: TreeData.currentProject?.value,
      ...query
    })
    const data = res.data?.data
    TableConfig.pagination.total = data.total || 0
    TableConfig.dataList = data.dataList || []
    TableConfig.columns = initTableColumns(
      data.monthList?.map(item => {
        return {
          label: item.label,
          prop: item.value,
          minWidth: 120
        }
      })
    )
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
const partition = usePartition()

onMounted(async () => {
  await partition.getTree()
  TreeData.data = partition.Tree.value
  refreshData()
  getWaterSupplyTree(1).then(res => {
    const unit = res.data?.data[0]?.id

    getMeterBookAll({ orgId: unit, value: 'code' }).then(res => {
      const field = SearchConfig.filters?.find(item => item.field === 'meterBookId') as IFormSelect | undefined
      if (field) {
        field.options = traverse(res) || []
      }
    })
  })
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}
</style>
