package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PipeCollectRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollect;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

public interface PipeCollectService {

    PipeCollect save(PipeCollect pipeCollect);

    PageData<PipeCollect> getList(PipeCollectRequest request);

    void delete(List<String> ids);

    IstarResponse receive(String id, String userId);

    IstarResponse submit(JSONObject params);

    IstarResponse review(JSONObject params);

    IstarResponse storage(String id);

    IstarResponse assign(JSONObject params);

    List<String> getLayerIdList(String mainId);
}
