package org.thingsboard.server.dao.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.thingsboard.server.common.data.alarm.Alarm;

import java.util.List;

@Controller
@FeignClient(name = "base-service", configuration = {FeignConfig.class})
public interface AlarmFeignClient {

    @GetMapping("api/alarm/station/{stationId}")
    List<Alarm> findAlarmByStation(@PathVariable("stationId") String stationId,
                                          @RequestParam(required = false, value = "start") Long start,
                                          @RequestParam(required = false, value = "end") Long end);

}
