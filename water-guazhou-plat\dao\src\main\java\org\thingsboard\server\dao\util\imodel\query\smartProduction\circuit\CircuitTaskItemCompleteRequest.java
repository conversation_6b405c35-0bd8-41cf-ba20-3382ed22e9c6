package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.AwareCurrentUserUUID;
import org.thingsboard.server.dao.util.imodel.query.Requestible;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class CircuitTaskItemCompleteRequest implements Requestible, AwareCurrentUserUUID {
    private String id;

    // 巡检结果。合格、不合格
    @NotNullOrEmpty
    private String result;

    // 关联工单Id
    @NotNullOrEmpty
    private String workOrderId;

    // 巡检结果备注
    private String resultRemark;

    // 巡检附件
    private String file;

    private String userId;

    @Override
    public void currentUserId(String uuid) {
        userId = uuid;
    }
}
