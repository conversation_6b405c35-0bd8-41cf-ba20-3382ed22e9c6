<template>
  <div class="wrapper">
    <SLCard title="" style="height: 100%">
      <template #title>
        <!-- <Search
          ref="refSearch"
          :config="SearchConfig"
        >
        </Search>-->
      </template>
      <historyVideo v-if="type === 'history'"></historyVideo>
      <realTimeVideo v-if="type === 'real'"></realTimeVideo>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import historyVideo from './components/historyVideo.vue';
import realTimeVideo from './components/realTimeVideo.vue';

const type = ref<string>('real');
const refSearch = ref<ISearchIns>();
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'tabs',
      label: '',
      field: 'type',
      tabType: 'simple',
      tabs: [
        { label: '视频预览', value: 'real' },
        { label: '视频回放', value: 'history' }
      ],
      onChange: (val) => {
        type.value = val;
      }
    }
  ],
  defaultParams: {
    type: 'real'
  }
});
</script>
<style lang="scss" scoped></style>
