package org.thingsboard.server.dao.smartProduction.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRearrangeRecord;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangeSwitchRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardRearrangeRecordPageRequest;

public interface GuardRearrangeRecordService {
    GuardRearrangeRecord findById(String id);

    IPage<GuardRearrangeRecord> findAllConditional(GuardRearrangeRecordPageRequest request);

    boolean delete(String id);

    /**
     * 通过调班请求来记录调班日志
     *
     * @param req 调班请求
     * @return 是否成功
     */
    boolean record(GuardArrangeSwitchRequest req);

}
