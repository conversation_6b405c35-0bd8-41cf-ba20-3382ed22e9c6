<!-- 精度统计 -->
<template>
  <RightDrawerMap :title="'拓展分析'">
    <Search :config="SearchConfig" />
    <el-divider />
    <FieldSet
      :icon="'mdi:view-dashboard'"
      :title="'压力监测'"
      :type="'simple'"
    ></FieldSet>
    <FormTable style="height: 400px" :config="TableConfig"></FormTable>
    <FieldSet :icon="'mdi:view-dashboard'" :title="'流量监测'" :type="'simple'">
    </FieldSet>
    <FormTable style="height: 400px" :config="TableConfig"></FormTable>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';

const SearchConfig = reactive<ISearch>({
  filters: [{ type: 'datetime', label: '日期', field: 'time' }],
  operations: [{ type: 'btn-group', btns: [{ perm: true, text: '查询' }] }],
  defaultParams: {}
});
const TableConfig = reactive<ITable>({
  height: 400,
  columns: [
    { prop: 'name', label: '监测点' },
    { prop: 'monitor', label: '监测值' },
    { prop: 'mock', label: '模拟值' },
    { prop: 'delta', label: '差值' }
  ],
  dataList: [],
  operations: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1;
      TableConfig.pagination.limit = size || 20;
      refreshData();
    }
  }
});
const refreshData = () => {
  //
};
</script>
<style lang="scss" scoped></style>
