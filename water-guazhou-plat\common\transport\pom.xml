<!--

    Copyright © 2016-2019 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.thingsboard</groupId>
        <version>2.3.0</version>
        <artifactId>common</artifactId>
    </parent>
    <groupId>org.thingsboard.common</groupId>
    <artifactId>transport</artifactId>
    <packaging>pom</packaging>

    <name>Thingsboard Server Commons</name>
    <url>https://thingsboard.io</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <main.dir>${basedir}/../..</main.dir>
    </properties>
    <modules>
        <module>transport-api</module>
        <module>mqtt</module>
        <module>http</module>
        <module>coap</module>
    </modules>

</project>
