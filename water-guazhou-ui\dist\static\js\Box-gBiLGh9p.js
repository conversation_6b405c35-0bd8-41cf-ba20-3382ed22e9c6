import{w as u,e as d,y as g,a as lt}from"./Point-WxyopZva.js";import{v as N,b as tt,u as _t,y as R,S as V,a as mt,A as M,bn as F,dF as U,g as I}from"./MapView-DaoQedLH.js";import{c as dt,f as gt,l as S,w as ut}from"./widget-BcWKanF2.js";import{aW as j,R as Z,T as k}from"./index-r0dFAfgr.js";import{m as q,h as Y,j as vt}from"./automaticLengthMeasurementUtils-DljoUgEz.js";import{f as yt,g as et,j as x,D as T,E as ft,F as Gt,n as xt}from"./AnimatedLinesLayer-B2VbV4jv.js";import{h as wt,D as bt}from"./GraphicMover-B3yTDkky.js";import{i as St,p as kt}from"./ExtentTooltipInfos-BUk_dezP.js";import{r as Rt}from"./TranslateTooltipInfos-D4yK3rJA.js";import"./pe-B8dP0-Ut.js";import"./spatialReferenceEllipsoidUtils-j_kxMN-4.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./GraphicManipulator-HH4WUKex.js";import"./drapedUtils-DJwxIB1g.js";function J(t){let i=0,e=0;const s=t.length;let r,o=t[e];for(e=0;e<s-1;e++)r=t[e+1],i+=(r[0]-o[0])*(r[1]+o[1]),o=r;return i>=0}function $(t,i,e,s){const r=[];for(const o of t){const h=o.slice(0);r.push(h);const c=i*(o[0]-s.x)-e*(o[1]-s.y)+s.x,a=e*(o[0]-s.x)+i*(o[1]-s.y)+s.y;h[0]=c,h[1]=a}return r}function E(t,i,e){const{hasM:s,hasZ:r,spatialReference:o}=t,h=i*Math.PI/180,c=Math.cos(h),a=Math.sin(h);if("xmin"in t&&(e=e??t.center,t=new N({spatialReference:o,rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]})),"paths"in t){e=e??t.extent.center;const p=[];for(const n of t.paths)p.push($(n,c,a,e));return new tt({hasM:s,hasZ:r,spatialReference:o,paths:p})}if("rings"in t){e=e??t.extent.center;const p=[];for(const n of t.rings){const l=J(n),v=$(n,c,a,e);J(v)!==l&&v.reverse(),p.push(v)}return new N({hasM:s,hasZ:r,spatialReference:o,rings:p})}if("x"in t){e=e??t;const p=new u({x:c*(t.x-e.x)-a*(t.y-e.y)+e.x,y:a*(t.x-e.x)+c*(t.y-e.y)+e.y,spatialReference:o});return t.z!=null&&(p.z=t.z),t.m!=null&&(p.m=t.m),p}return"points"in t?(e=e??t.extent.center,new _t({hasM:s,hasZ:r,points:$(t.points,c,a,e),spatialReference:o})):null}class Mt{constructor(i,e,s,r){this.graphics=i,this.mover=e,this.dx=s,this.dy=r,this.type="move-start"}}class It{constructor(i,e,s,r){this.graphics=i,this.mover=e,this.dx=s,this.dy=r,this.type="move"}}class W{constructor(i,e,s,r){this.graphics=i,this.mover=e,this.dx=s,this.dy=r,this.type="move-stop"}}class Ot{constructor(i,e,s){this.graphics=i,this.mover=e,this.angle=s,this.type="rotate-start"}}class Ct{constructor(i,e,s){this.graphics=i,this.mover=e,this.angle=s,this.type="rotate"}}class K{constructor(i,e,s){this.graphics=i,this.mover=e,this.angle=s,this.type="rotate-stop"}}class Ht{constructor(i,e,s,r){this.graphics=i,this.mover=e,this.xScale=s,this.yScale=r,this.type="scale-start"}}class Tt{constructor(i,e,s,r){this.graphics=i,this.mover=e,this.xScale=s,this.yScale=r,this.type="scale"}}class Q{constructor(i,e,s,r){this.graphics=i,this.mover=e,this.xScale=s,this.yScale=r,this.type="scale-stop"}}const _=yt.transformGraphics,w={centerIndicator:new R({style:"cross",size:_.center.size,color:_.center.color}),fill:{default:new V({color:_.fill.color,outline:{color:_.fill.outlineColor,join:"round",width:1}}),active:new V({color:_.fill.stagedColor,outline:{color:_.fill.outlineColor,join:"round",style:"dash",width:1}})},handles:{default:new R({style:"square",size:_.vertex.size,color:_.vertex.color,outline:{color:_.vertex.outlineColor,width:1}}),hover:new R({style:"square",size:_.vertex.hoverSize,color:_.vertex.hoverColor,outline:{color:_.vertex.hoverOutlineColor,width:1}})},rotator:{default:new R({style:"circle",size:_.vertex.size,color:_.vertex.color,outline:{color:_.vertex.outlineColor,width:1}}),hover:new R({style:"circle",size:_.vertex.hoverSize,color:_.vertex.hoverColor,outline:{color:_.vertex.hoverOutlineColor,width:1}})},rotatorLine:new mt({color:_.line.color,width:1})};let m=class extends dt.EventedAccessor{constructor(t){super(t),this._activeHandleGraphic=null,this._graphicAttributes={esriSketchTool:"box"},this._mover=null,this._centerGraphic=null,this._backgroundGraphic=null,this._vertexGraphics=[],this._rotateHandleGraphic=null,this._rotateGraphicOffset=20,this._angleOfRotation=0,this._rotateLineGraphic=null,this._startInfo=null,this._tooltip=null,this._totalDx=0,this._totalDy=0,this._xScale=1,this._yScale=1,this.type="box",this.callbacks={onMoveStart(){},onMove(){},onMoveStop(){},onScaleStart(){},onScale(){},onScaleStop(){},onRotateStart(){},onRotate(){},onRotateStop(){},onGraphicClick(){}},this.enableMovement=!0,this.enableRotation=!0,this.enableScaling=!0,this.graphics=[],this.highlightsEnabled=!0,this.layer=null,this.preserveAspectRatio=!1,this.showCenterGraphic=!0,this.symbols=w,this.tooltipOptions=new et,this.view=null,this._getBounds=(()=>{const i=M();return(e,s)=>{e[0]=Number.POSITIVE_INFINITY,e[1]=Number.POSITIVE_INFINITY,e[2]=Number.NEGATIVE_INFINITY,e[3]=Number.NEGATIVE_INFINITY;for(const r of s){if(!r)continue;let o,h,c,a;if(r.type==="point")o=c=r.x,h=a=r.y;else if(r.type==="multipoint"){const p=F(r);[o,h,c,a]=U(i,[p])}else if(r.type==="extent")[o,h,c,a]=[r.xmin,r.ymin,r.xmax,r.ymax];else{const p=F(r);[o,h,c,a]=U(i,p)}e[0]=Math.min(o,e[0]),e[1]=Math.min(h,e[1]),e[2]=Math.max(c,e[2]),e[3]=Math.max(a,e[3])}return e}})()}initialize(){const t=this.view;this._highlightHelper=new wt({view:t}),this._tooltip=new q({view:t}),this._setup(),this.addHandles([gt(()=>t==null?void 0:t.ready,()=>{const{layer:i,view:e}=this;xt(e,i)},{once:!0,initial:!0}),S(()=>this.preserveAspectRatio,()=>{this._activeHandleGraphic&&(this._scaleGraphic(this._activeHandleGraphic),this._updateGraphics(),this._updateTooltip(this._activeHandleGraphic))}),S(()=>t==null?void 0:t.scale,()=>{this._updateRotateGraphic(),this._updateRotateLineGraphic()}),S(()=>this.graphics,()=>this.refresh()),S(()=>this.layer,(i,e)=>{e&&this._resetGraphics(e),this.refresh()}),S(()=>this.highlightsEnabled,()=>{var i;(i=this._highlightHelper)==null||i.removeAll(),this._setUpHighlights()}),S(()=>this.tooltipOptions.enabled,i=>{this._tooltip=i?new q({view:this.view}):j(this._tooltip)},ut),this.on("move-start",i=>{var e,s;return(s=(e=this.callbacks)==null?void 0:e.onMoveStart)==null?void 0:s.call(e,i)}),this.on("move",i=>{var e,s;return(s=(e=this.callbacks)==null?void 0:e.onMove)==null?void 0:s.call(e,i)}),this.on("move-stop",i=>{var e,s;return(s=(e=this.callbacks)==null?void 0:e.onMoveStop)==null?void 0:s.call(e,i)}),this.on("rotate-start",i=>{var e,s;return(s=(e=this.callbacks)==null?void 0:e.onRotateStart)==null?void 0:s.call(e,i)}),this.on("rotate",i=>{var e,s;return(s=(e=this.callbacks)==null?void 0:e.onRotate)==null?void 0:s.call(e,i)}),this.on("rotate-stop",i=>{var e,s;return(s=(e=this.callbacks)==null?void 0:e.onRotateStop)==null?void 0:s.call(e,i)}),this.on("scale-start",i=>{var e,s;return(s=(e=this.callbacks)==null?void 0:e.onScaleStart)==null?void 0:s.call(e,i)}),this.on("scale",i=>{var e,s;return(s=(e=this.callbacks)==null?void 0:e.onScale)==null?void 0:s.call(e,i)}),this.on("scale-stop",i=>{var e,s;return(s=(e=this.callbacks)==null?void 0:e.onScaleStop)==null?void 0:s.call(e,i)})])}destroy(){this._reset(),this._tooltip=j(this._tooltip)}get state(){var e;const t=((e=this.view)==null?void 0:e.ready)??!1,i=this.graphics.length&&this.layer;return t&&i?"active":t?"ready":"disabled"}isUIGraphic(t){return this._vertexGraphics.includes(t)||t===this._backgroundGraphic||t===this._centerGraphic||t===this._rotateHandleGraphic||t===this._rotateLineGraphic}move(t,i){if(this._mover&&this.graphics.length){for(const e of this.graphics){const s=e.geometry,r=x(s,t,i,this.view);e.geometry=r}this.refresh(),this.emit("move-stop",new W(this.graphics,null,t,i))}}scale(t,i){if(this._mover&&this.graphics.length){for(const e of this.graphics){const s=e.geometry,r=T(s,t,i);e.geometry=r}this.refresh(),this.emit("scale-stop",new Q(this.graphics,null,t,i))}}rotate(t,i){if(this._mover&&this.graphics.length){if(!i){const e=this._vertexGraphics[1].geometry.x,s=this._vertexGraphics[3].geometry.y;i=new u(e,s,this.view.spatialReference)}for(const e of this.graphics){const s=e.geometry,r=E(s,t,i);e.geometry=r}this.refresh(),this.emit("rotate-stop",new K(this.graphics,null,t))}}refresh(){this._reset(),this._setup()}reset(){this.graphics=[]}_setup(){this.state==="active"&&(this._setUpHighlights(),this._setupGraphics(),this._setupMover(),this._updateGraphics())}_reset(){this._highlightHelper.removeAll(),this._resetGraphicStateVars(),this._resetGraphics(),this._updateTooltip(),this._mover&&this._mover.destroy(),this._mover=null,this.view.cursor="default"}_resetGraphicStateVars(){this._startInfo=null,this._activeHandleGraphic=null,this._totalDx=0,this._totalDy=0,this._xScale=1,this._yScale=1,this._angleOfRotation=0}_resetGraphics(t){(t=t||this.layer)&&(t.removeMany(this._vertexGraphics),t.remove(this._backgroundGraphic),t.remove(this._centerGraphic),t.remove(this._rotateHandleGraphic),t.remove(this._rotateLineGraphic)),this._vertexGraphics=[],this._backgroundGraphic=null,this._centerGraphic=null,this._rotateHandleGraphic=null,this._rotateLineGraphic=null}_setupMover(){let t=[];this.enableScaling&&(t=t.concat(this._vertexGraphics)),this.enableMovement&&(t=t.concat(this.graphics,this._backgroundGraphic)),this.enableRotation&&t.push(this._rotateHandleGraphic),this.showCenterGraphic&&t.push(this._centerGraphic),this._mover=new bt({enableMoveAllGraphics:!1,highlightsEnabled:!1,indicatorsEnabled:!1,view:this.view,graphics:t,callbacks:{onGraphicClick:i=>this._onGraphicClickCallback(i),onGraphicMoveStart:i=>this._onGraphicMoveStartCallback(i),onGraphicMove:i=>this._onGraphicMoveCallback(i),onGraphicMoveStop:i=>this._onGraphicMoveStopCallback(i),onGraphicPointerOver:i=>this._onGraphicPointerOverCallback(i),onGraphicPointerOut:i=>this._onGraphicPointerOutCallback(i)}})}_getStartInfo(t){const[i,e,s,r]=this._getBoxBounds(M()),o=Math.abs(s-i),h=Math.abs(r-e),c=(s+i)/2,a=(r+e)/2,{x:p,y:n}=t.geometry;return{width:o,height:h,centerX:c,centerY:a,startX:p,startY:n,graphicInfos:this._getGraphicInfos(),box:this._backgroundGraphic.geometry,rotate:this._rotateHandleGraphic.geometry}}_getGraphicInfos(){return this.graphics.map(t=>this._getGraphicInfo(t))}_getGraphicInfo(t){const i=t.geometry,[e,s,r,o]=this._getBounds(M(),[i]);return{width:Math.abs(r-e),height:Math.abs(o-s),centerX:(r+e)/2,centerY:(o+s)/2,geometry:i}}_onGraphicClickCallback(t){t.viewEvent.stopPropagation(),this.emit("graphic-click",t),this.callbacks.onGraphicClick&&this.callbacks.onGraphicClick(t)}_onGraphicMoveStartCallback(t){const{_angleOfRotation:i,_xScale:e,_yScale:s,_backgroundGraphic:r,_vertexGraphics:o,_rotateHandleGraphic:h,symbols:c}=this,a=t.graphic;this._resetGraphicStateVars(),this._hideGraphicsBeforeUpdate(),r.symbol=c.fill.active,this._startInfo=this._getStartInfo(a),this._updateTooltip(a,t.viewEvent),a===h?(this.view.cursor="grabbing",this.emit("rotate-start",new Ot(this.graphics,a,i))):o.includes(a)?(this._activeHandleGraphic=a,this.emit("scale-start",new Ht(this.graphics,a,e,s))):this.emit("move-start",new Mt(this.graphics,a,t.dx,t.dy))}_onGraphicMoveCallback(t){const i=t.graphic;if(this._startInfo)if(this._vertexGraphics.includes(i))this._scaleGraphic(i),this._updateTooltip(i,t.viewEvent),this.emit("scale",new Tt(this.graphics,i,this._xScale,this._yScale));else if(i===this._rotateHandleGraphic)this._rotateGraphic(i),this._updateTooltip(i,t.viewEvent),this.emit("rotate",new Ct(this.graphics,i,this._angleOfRotation));else{const{dx:e,dy:s}=t;this._totalDx+=e,this._totalDy+=s,this._moveGraphic(i,e,s),this._updateTooltip(i,t.viewEvent),this.emit("move",new It(this.graphics,i,e,s))}}_onGraphicMoveStopCallback(t){const i=t.graphic;if(!this._startInfo)return void this.refresh();const{_angleOfRotation:e,_totalDx:s,_totalDy:r,_xScale:o,_yScale:h,_vertexGraphics:c,_rotateHandleGraphic:a}=this;this.refresh(),i===a?(this.view.cursor="pointer",this.emit("rotate-stop",new K(this.graphics,i,e))):c.includes(i)?this.emit("scale-stop",new Q(this.graphics,i,o,h)):this.emit("move-stop",new W(this.graphics,i,s,r))}_onGraphicPointerOverCallback(t){const{_backgroundGraphic:i,_vertexGraphics:e,graphics:s,_rotateHandleGraphic:r,symbols:o,view:h}=this,c=t.graphic;if(c===r)return r.symbol=o.rotator.hover,h.cursor="pointer",void this._updateTooltip(c);if(s.includes(c)||c===i)return void(h.cursor="move");if(!e.includes(c))return void(h.cursor="pointer");this._updateTooltip(c),t.graphic.symbol=o.handles.hover;const a=h.rotation;let p,n=t.index;switch(n<8&&(a>=0&&a<45?n%=8:n=a>=45&&a<90?(n+1)%8:a>=90&&a<135?(n+2)%8:a>=135&&a<180?(n+3)%8:a>=180&&a<225?(n+4)%8:a>=225&&a<270?(n+5)%8:a>=270&&a<315?(n+6)%8:(n+7)%8),n){case 0:case 4:p="nwse-resize";break;case 1:case 5:p="ns-resize";break;case 2:case 6:p="nesw-resize";break;case 3:case 7:p="ew-resize";break;default:p="pointer"}h.cursor=p}_onGraphicPointerOutCallback(t){const{_vertexGraphics:i,_rotateHandleGraphic:e,symbols:s,view:r}=this;t.graphic===e?e.symbol=s.rotator.default:i.includes(t.graphic)&&(t.graphic.symbol=s.handles.default),r.cursor="default",this._updateTooltip()}_scaleGraphic(t){var A;const{_startInfo:i,_vertexGraphics:e,preserveAspectRatio:s,view:r}=this,{centerX:o,centerY:h,startX:c,startY:a}=i,{resolution:p,transform:n}=r.state,l=e.indexOf(t);l!==1&&l!==5||this._updateX(t,o),l!==3&&l!==7||this._updateY(t,h);const{x:v,y}=t.geometry,z=n[0]*v+n[2]*y+n[4],B=n[1]*v+n[3]*y+n[5],O=((A=i.graphicInfos)==null?void 0:A.map(b=>b.geometry))??[];if(s){const b=n[0]*o+n[2]*h+n[4],C=n[1]*o+n[3]*h+n[5],f=n[0]*c+n[2]*a+n[4],G=n[1]*c+n[3]*a+n[5];this._xScale=this._yScale=ft(b,C,f,G,z,B);for(const H of O){const L=O.indexOf(H);this.graphics[L].geometry=T(H,this._xScale,this._yScale,[o,h])}this._updateBackgroundGraphic()}else{const{width:b,height:C}=i;let f=v-c,G=a-y;if(l===1||l===5?f=0:l!==3&&l!==7||(G=0),f===0&&G===0)return;const H=b+(c>o?f:-1*f),L=C+(a<h?G:-1*G),it=o+f/2,st=h+G/2;this._xScale=H/b||1,this._yScale=L/C||1,l===1||l===5?this._xScale=1:l!==3&&l!==7||(this._yScale=1);const rt=(it-o)/p,ot=(st-h)/p,at=T(i.box,this._xScale,this._yScale);this._backgroundGraphic.geometry=x(at,rt,ot,r,!0);const{centerX:X,centerY:D}=this._getGraphicInfo(this._backgroundGraphic),ht=(X-o)/p,nt=-1*(D-h)/p;for(const P of O){const ct=O.indexOf(P),pt=T(P,this._xScale,this._yScale,[o,h]);this.graphics[ct].geometry=x(pt,ht,nt,r,!0)}this._centerGraphic.geometry=new u(X,D,r.spatialReference)}}_rotateGraphic(t){var v;const{centerX:i,centerY:e,startX:s,startY:r,box:o,rotate:h}=this._startInfo,c=this.view.spatialReference,a=new u(s,r,c),p=new u(i,e,c),n=t.geometry;this._angleOfRotation=Gt(a,n,p);const l=((v=this._startInfo.graphicInfos)==null?void 0:v.map(y=>y.geometry))??[];for(const y of l){const z=l.indexOf(y),B=E(y,this._angleOfRotation,p);this.graphics[z].geometry=B}this._backgroundGraphic.geometry=E(o,this._angleOfRotation,p),this._rotateHandleGraphic.geometry=E(h,this._angleOfRotation,p)}_moveGraphic(t,i,e){if(this.graphics.includes(t)){const s=this._backgroundGraphic.geometry;this._backgroundGraphic.geometry=x(s,i,e,this.view);for(const r of this.graphics)r!==t&&(r.geometry=x(r.geometry,i,e,this.view))}else if(t===this._centerGraphic){const s=this._backgroundGraphic.geometry;this._backgroundGraphic.geometry=x(s,i,e,this.view)}if(t===this._backgroundGraphic||t===this._centerGraphic)for(const s of this.graphics)s.geometry=x(s.geometry,i,e,this.view)}_setUpHighlights(){var t;this.highlightsEnabled&&this.graphics.length&&((t=this._highlightHelper)==null||t.add(this.graphics))}_setupGraphics(){const{_graphicAttributes:t,symbols:i}=this;this._centerGraphic=new I(null,i.centerIndicator,t),this.showCenterGraphic&&this.layer.add(this._centerGraphic),this._backgroundGraphic=new I(null,i.fill.default,t),this.layer.add(this._backgroundGraphic),this._rotateLineGraphic=new I(null,i.rotatorLine,t),this._rotateHandleGraphic=new I(null,i.rotator.default,t),this.enableRotation&&!this._hasExtentGraphic()&&this.layer.addMany([this._rotateLineGraphic,this._rotateHandleGraphic]);for(let e=0;e<8;e++)this._vertexGraphics.push(new I(null,i.handles.default,t));this.enableScaling&&this.layer.addMany(this._vertexGraphics)}_updateGraphics(){this._updateBackgroundGraphic(),this._updateHandleGraphics(),this._updateCenterGraphic(),this._updateRotateGraphic(),this._updateRotateLineGraphic()}_hideGraphicsBeforeUpdate(){this._centerGraphic.visible=!1,this._rotateHandleGraphic.visible=!1,this._rotateLineGraphic.visible=!1,this._vertexGraphics.forEach(t=>t.visible=!1)}_updateHandleGraphics(){const t=this._getCoordinates(!0);this._vertexGraphics.forEach((i,e)=>{const[s,r]=t[e];this._updateXY(i,s,r)})}_updateBackgroundGraphic(){const t=this._getCoordinates();this._backgroundGraphic.geometry=new N({rings:t,spatialReference:this.view.spatialReference})}_updateCenterGraphic(){const[t,i,e,s]=this._getBoxBounds(M()),r=(e+t)/2,o=(s+i)/2;this._centerGraphic.geometry=new u(r,o,this.view.spatialReference)}_updateRotateGraphic(){if(!this._vertexGraphics.length)return;const{x:t,y:i}=this._vertexGraphics[1].geometry,e=i+this.view.state.resolution*this._rotateGraphicOffset;this._rotateHandleGraphic.geometry=new u(t,e,this.view.spatialReference)}_updateRotateLineGraphic(){if(!this._vertexGraphics.length||!this._rotateHandleGraphic||!this._rotateHandleGraphic.geometry)return;const t=this._vertexGraphics[1].geometry,i=this._rotateHandleGraphic.geometry;this._rotateLineGraphic.geometry=new tt({paths:[[t.x,t.y],[i.x,i.y]],spatialReference:this.view.spatialReference})}_updateXY(t,i,e){t.geometry=new u(i,e,this.view.spatialReference)}_updateX(t,i){const e=t.geometry.y;t.geometry=new u(i,e,this.view.spatialReference)}_updateY(t,i){const e=t.geometry.x;t.geometry=new u(e,i,this.view.spatialReference)}_hasExtentGraphic(){return this.graphics.some(t=>t&&Z(t.geometry)&&t.geometry.type==="extent")}_getBoxBounds(t){const i=this.graphics.map(e=>e.geometry);return this._getBounds(t,i)}_getCoordinates(t){const[i,e,s,r]=this._getBoxBounds(M());if(t){const o=(i+s)/2,h=(r+e)/2;return[[i,r],[o,r],[s,r],[s,h],[s,e],[o,e],[i,e],[i,h]]}return[[i,r],[s,r],[s,e],[i,e]]}_updateTooltip(t,i){if(k(this._tooltip))return;if(!t)return void this._tooltip.clear();const{_backgroundGraphic:e,graphics:s,_vertexGraphics:r,_rotateHandleGraphic:o}=this;t===o?this._updateRotateTooltip():r.includes(t)?this._updateScaleTooltip():(s.includes(t)||t===e)&&this._updateMoveTooltip(i)}_updateRotateTooltip(){k(this._tooltip)||(this._tooltip.info=new St({tooltipOptions:this.tooltipOptions,angle:-this._angleOfRotation}))}_updateScaleTooltip(){const{_tooltip:t,_xScale:i,_yScale:e,tooltipOptions:s,view:r}=this;if(k(t))return;const o=r.spatialReference,h=this._getCoordinates(),c=new u(h[0][0],h[0][1],o),a=new u(h[1][0],h[1][1],o),p=new u(h[2][0],h[2][1],o),n=Y(c,a),l=Y(a,p);if(k(n)||k(l))return;const v=Math.abs(i),y=Math.abs(e);t.info=new kt({tooltipOptions:s,xScale:v,yScale:y,xSize:n,ySize:l})}_updateMoveTooltip(t){const{_tooltip:i,tooltipOptions:e,view:s}=this;if(k(i))return;const r=new Rt({tooltipOptions:e});if(t){const{x:o,y:h}=t.origin,c=s.toMap(t),a=s.toMap({x:o,y:h}),p=Y(a,c);r.distance=Z(p)?p:vt}i.info=r}};d([g()],m.prototype,"_tooltip",void 0),d([g({readOnly:!0})],m.prototype,"type",void 0),d([g()],m.prototype,"callbacks",void 0),d([g()],m.prototype,"enableMovement",void 0),d([g()],m.prototype,"enableRotation",void 0),d([g()],m.prototype,"enableScaling",void 0),d([g()],m.prototype,"graphics",void 0),d([g()],m.prototype,"highlightsEnabled",void 0),d([g()],m.prototype,"layer",void 0),d([g()],m.prototype,"preserveAspectRatio",void 0),d([g()],m.prototype,"showCenterGraphic",void 0),d([g({readOnly:!0})],m.prototype,"state",null),d([g({value:w,cast(t){const{centerIndicator:i=w.centerIndicator,fill:e=w.fill,handles:s=w.handles,rotator:r=w.rotator,rotatorLine:o=w.rotatorLine}=t||{};return{centerIndicator:i,fill:e,handles:s,rotator:r,rotatorLine:o}}})],m.prototype,"symbols",void 0),d([g({type:et})],m.prototype,"tooltipOptions",void 0),d([g({constructOnly:!0})],m.prototype,"view",void 0),m=d([lt("esri.views.draw.support.Box")],m);const je=m;export{je as default};
