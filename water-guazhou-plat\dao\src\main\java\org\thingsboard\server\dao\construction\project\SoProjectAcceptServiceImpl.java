package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectAccept;
import org.thingsboard.server.dao.sql.smartOperation.construction.project.SoProjectAcceptMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectAcceptPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectAcceptSaveRequest;

@Service
public class SoProjectAcceptServiceImpl implements SoProjectAcceptService {
    @Autowired
    private SoProjectAcceptMapper mapper;

    @Autowired
    private SoProjectOperateRecordService recordService;

    @Override
    public IPage<SoProjectAccept> findAllConditional(SoProjectAcceptPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoProjectAccept save(SoProjectAcceptSaveRequest entity) {
        entity.toUpdateModeOn(mapper.getIdByProjectCodeAndTenantId(entity.getProjectCode(), entity.tenantId()));
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> {
            // recordService.save(SoProjectOperateRecordSaveRequest.of(entity, entity.getProjectCode(), "添加%s验收", "项目验收"));
            return mapper.save(e);
        }, mapper::updateFully);
    }

    @Override
    public boolean update(SoProjectAccept entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
