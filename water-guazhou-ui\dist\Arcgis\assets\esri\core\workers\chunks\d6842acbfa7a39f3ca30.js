"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[7126],{17126:(t,e,n)=>{n.d(e,{ou:()=>Qn,Qf:()=>et,vF:()=>U,ld:()=>L});class r extends Error{}class s extends r{constructor(t){super(`Invalid DateTime: ${t.toMessage()}`)}}class i extends r{constructor(t){super(`Invalid Interval: ${t.toMessage()}`)}}class a extends r{constructor(t){super(`Invalid Duration: ${t.toMessage()}`)}}class o extends r{}class u extends r{constructor(t){super(`Invalid unit ${t}`)}}class l extends r{}class c extends r{constructor(){super("Zone is an abstract class")}}const h="numeric",d="short",m="long",f={year:h,month:h,day:h},y={year:h,month:d,day:h},g={year:h,month:d,day:h,weekday:d},w={year:h,month:m,day:h},p={year:h,month:m,day:h,weekday:m},v={hour:h,minute:h},T={hour:h,minute:h,second:h},S={hour:h,minute:h,second:h,timeZoneName:d},O={hour:h,minute:h,second:h,timeZoneName:m},b={hour:h,minute:h,hourCycle:"h23"},k={hour:h,minute:h,second:h,hourCycle:"h23"},M={hour:h,minute:h,second:h,hourCycle:"h23",timeZoneName:d},N={hour:h,minute:h,second:h,hourCycle:"h23",timeZoneName:m},D={year:h,month:h,day:h,hour:h,minute:h},E={year:h,month:h,day:h,hour:h,minute:h,second:h},V={year:h,month:d,day:h,hour:h,minute:h},x={year:h,month:d,day:h,hour:h,minute:h,second:h},I={year:h,month:d,day:h,weekday:d,hour:h,minute:h},C={year:h,month:m,day:h,hour:h,minute:h,timeZoneName:d},F={year:h,month:m,day:h,hour:h,minute:h,second:h,timeZoneName:d},Z={year:h,month:m,day:h,weekday:m,hour:h,minute:h,timeZoneName:m},$={year:h,month:m,day:h,weekday:m,hour:h,minute:h,second:h,timeZoneName:m};class L{get type(){throw new c}get name(){throw new c}get ianaName(){return this.name}get isUniversal(){throw new c}offsetName(t,e){throw new c}formatOffset(t,e){throw new c}offset(t){throw new c}equals(t){throw new c}get isValid(){throw new c}}let z=null;class q extends L{static get instance(){return null===z&&(z=new q),z}get type(){return"system"}get name(){return(new Intl.DateTimeFormat).resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(t,{format:e,locale:n}){return xt(t,e,n)}formatOffset(t,e){return Zt(this.offset(t),e)}offset(t){return-new Date(t).getTimezoneOffset()}equals(t){return"system"===t.type}get isValid(){return!0}}let A={};const j={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};let _={};class U extends L{static create(t){return _[t]||(_[t]=new U(t)),_[t]}static resetCache(){_={},A={}}static isValidSpecifier(t){return this.isValidZone(t)}static isValidZone(t){if(!t)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:t}).format(),!0}catch(t){return!1}}constructor(t){super(),this.zoneName=t,this.valid=U.isValidZone(t)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(t,{format:e,locale:n}){return xt(t,e,n,this.name)}formatOffset(t,e){return Zt(this.offset(t),e)}offset(t){const e=new Date(t);if(isNaN(e))return NaN;const n=(r=this.name,A[r]||(A[r]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:r,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),A[r]);var r;let[s,i,a,o,u,l,c]=n.formatToParts?function(t,e){const n=t.formatToParts(e),r=[];for(let t=0;t<n.length;t++){const{type:e,value:s}=n[t],i=j[e];"era"===e?r[i]=s:dt(i)||(r[i]=parseInt(s,10))}return r}(n,e):function(t,e){const n=t.format(e).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,s,i,a,o,u,l,c]=r;return[a,s,i,o,u,l,c]}(n,e);"BC"===o&&(s=1-Math.abs(s));let h=+e;const d=h%1e3;return h-=d>=0?d:1e3+d,(Dt({year:s,month:i,day:a,hour:24===u?0:u,minute:l,second:c,millisecond:0})-h)/6e4}equals(t){return"iana"===t.type&&t.name===this.name}get isValid(){return this.valid}}let H={},W={};function R(t,e={}){const n=JSON.stringify([t,e]);let r=W[n];return r||(r=new Intl.DateTimeFormat(t,e),W[n]=r),r}let Y={},J={},P=null;function G(t,e,n,r,s){const i=t.listingMode(n);return"error"===i?null:"en"===i?r(e):s(e)}class B{constructor(t,e,n){this.padTo=n.padTo||0,this.floor=n.floor||!1;const{padTo:r,floor:s,...i}=n;if(!e||Object.keys(i).length>0){const e={useGrouping:!1,...n};n.padTo>0&&(e.minimumIntegerDigits=n.padTo),this.inf=function(t,e={}){const n=JSON.stringify([t,e]);let r=Y[n];return r||(r=new Intl.NumberFormat(t,e),Y[n]=r),r}(t,e)}}format(t){if(this.inf){const e=this.floor?Math.floor(t):t;return this.inf.format(e)}return vt(this.floor?Math.floor(t):bt(t,3),this.padTo)}}class Q{constructor(t,e,n){let r;if(this.opts=n,t.zone.isUniversal){const e=t.offset/60*-1,s=e>=0?`Etc/GMT+${e}`:`Etc/GMT${e}`;0!==t.offset&&U.create(s).valid?(r=s,this.dt=t):(r="UTC",n.timeZoneName?this.dt=t:this.dt=0===t.offset?t:Qn.fromMillis(t.ts+60*t.offset*1e3))}else"system"===t.zone.type?this.dt=t:(this.dt=t,r=t.zone.name);const s={...this.opts};s.timeZone=s.timeZone||r,this.dtf=R(e,s)}format(){return this.dtf.format(this.dt.toJSDate())}formatToParts(){return this.dtf.formatToParts(this.dt.toJSDate())}resolvedOptions(){return this.dtf.resolvedOptions()}}class K{constructor(t,e,n){this.opts={style:"long",...n},!e&&yt()&&(this.rtf=function(t,e={}){const{base:n,...r}=e,s=JSON.stringify([t,r]);let i=J[s];return i||(i=new Intl.RelativeTimeFormat(t,e),J[s]=i),i}(t,n))}format(t,e){return this.rtf?this.rtf.format(t,e):function(t,e,n="always",r=!1){const s={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},i=-1===["hours","minutes","seconds"].indexOf(t);if("auto"===n&&i){const n="days"===t;switch(e){case 1:return n?"tomorrow":`next ${s[t][0]}`;case-1:return n?"yesterday":`last ${s[t][0]}`;case 0:return n?"today":`this ${s[t][0]}`}}const a=Object.is(e,-0)||e<0,o=Math.abs(e),u=1===o,l=s[t],c=r?u?l[1]:l[2]||l[1]:u?s[t][0]:t;return a?`${o} ${c} ago`:`in ${o} ${c}`}(e,t,this.opts.numeric,"long"!==this.opts.style)}formatToParts(t,e){return this.rtf?this.rtf.formatToParts(t,e):[]}}class X{static fromOpts(t){return X.create(t.locale,t.numberingSystem,t.outputCalendar,t.defaultToEN)}static create(t,e,n,r=!1){const s=t||ht.defaultLocale,i=s||(r?"en-US":P||(P=(new Intl.DateTimeFormat).resolvedOptions().locale,P)),a=e||ht.defaultNumberingSystem,o=n||ht.defaultOutputCalendar;return new X(i,a,o,s)}static resetCache(){P=null,W={},Y={},J={}}static fromObject({locale:t,numberingSystem:e,outputCalendar:n}={}){return X.create(t,e,n)}constructor(t,e,n,r){const[s,i,a]=function(t){const e=t.indexOf("-x-");-1!==e&&(t=t.substring(0,e));const n=t.indexOf("-u-");if(-1===n)return[t];{let e,r;try{e=R(t).resolvedOptions(),r=t}catch(s){const i=t.substring(0,n);e=R(i).resolvedOptions(),r=i}const{numberingSystem:s,calendar:i}=e;return[r,s,i]}}(t);this.locale=s,this.numberingSystem=e||i||null,this.outputCalendar=n||a||null,this.intl=function(t,e,n){return n||e?(t.includes("-u-")||(t+="-u"),n&&(t+=`-ca-${n}`),e&&(t+=`-nu-${e}`),t):t}(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=r,this.fastNumbersCached=null}get fastNumbers(){var t;return null==this.fastNumbersCached&&(this.fastNumbersCached=(!(t=this).numberingSystem||"latn"===t.numberingSystem)&&("latn"===t.numberingSystem||!t.locale||t.locale.startsWith("en")||"latn"===new Intl.DateTimeFormat(t.intl).resolvedOptions().numberingSystem)),this.fastNumbersCached}listingMode(){const t=this.isEnglish(),e=!(null!==this.numberingSystem&&"latn"!==this.numberingSystem||null!==this.outputCalendar&&"gregory"!==this.outputCalendar);return t&&e?"en":"intl"}clone(t){return t&&0!==Object.getOwnPropertyNames(t).length?X.create(t.locale||this.specifiedLocale,t.numberingSystem||this.numberingSystem,t.outputCalendar||this.outputCalendar,t.defaultToEN||!1):this}redefaultToEN(t={}){return this.clone({...t,defaultToEN:!0})}redefaultToSystem(t={}){return this.clone({...t,defaultToEN:!1})}months(t,e=!1,n=!0){return G(this,t,n,At,(()=>{const n=e?{month:t,day:"numeric"}:{month:t},r=e?"format":"standalone";return this.monthsCache[r][t]||(this.monthsCache[r][t]=function(t){const e=[];for(let n=1;n<=12;n++){const r=Qn.utc(2016,n,1);e.push(t(r))}return e}((t=>this.extract(t,n,"month")))),this.monthsCache[r][t]}))}weekdays(t,e=!1,n=!0){return G(this,t,n,Ht,(()=>{const n=e?{weekday:t,year:"numeric",month:"long",day:"numeric"}:{weekday:t},r=e?"format":"standalone";return this.weekdaysCache[r][t]||(this.weekdaysCache[r][t]=function(t){const e=[];for(let n=1;n<=7;n++){const r=Qn.utc(2016,11,13+n);e.push(t(r))}return e}((t=>this.extract(t,n,"weekday")))),this.weekdaysCache[r][t]}))}meridiems(t=!0){return G(this,void 0,t,(()=>Wt),(()=>{if(!this.meridiemCache){const t={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[Qn.utc(2016,11,13,9),Qn.utc(2016,11,13,19)].map((e=>this.extract(e,t,"dayperiod")))}return this.meridiemCache}))}eras(t,e=!0){return G(this,t,e,Pt,(()=>{const e={era:t};return this.eraCache[t]||(this.eraCache[t]=[Qn.utc(-40,1,1),Qn.utc(2017,1,1)].map((t=>this.extract(t,e,"era")))),this.eraCache[t]}))}extract(t,e,n){const r=this.dtFormatter(t,e).formatToParts().find((t=>t.type.toLowerCase()===n));return r?r.value:null}numberFormatter(t={}){return new B(this.intl,t.forceSimple||this.fastNumbers,t)}dtFormatter(t,e={}){return new Q(t,this.intl,e)}relFormatter(t={}){return new K(this.intl,this.isEnglish(),t)}listFormatter(t={}){return function(t,e={}){const n=JSON.stringify([t,e]);let r=H[n];return r||(r=new Intl.ListFormat(t,e),H[n]=r),r}(this.intl,t)}isEnglish(){return"en"===this.locale||"en-us"===this.locale.toLowerCase()||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}equals(t){return this.locale===t.locale&&this.numberingSystem===t.numberingSystem&&this.outputCalendar===t.outputCalendar}}let tt=null;class et extends L{static get utcInstance(){return null===tt&&(tt=new et(0)),tt}static instance(t){return 0===t?et.utcInstance:new et(t)}static parseSpecifier(t){if(t){const e=t.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(e)return new et(It(e[1],e[2]))}return null}constructor(t){super(),this.fixed=t}get type(){return"fixed"}get name(){return 0===this.fixed?"UTC":`UTC${Zt(this.fixed,"narrow")}`}get ianaName(){return 0===this.fixed?"Etc/UTC":`Etc/GMT${Zt(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(t,e){return Zt(this.fixed,e)}get isUniversal(){return!0}offset(){return this.fixed}equals(t){return"fixed"===t.type&&t.fixed===this.fixed}get isValid(){return!0}}class nt extends L{constructor(t){super(),this.zoneName=t}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function rt(t,e){if(dt(t)||null===t)return e;if(t instanceof L)return t;if("string"==typeof t){const n=t.toLowerCase();return"default"===n?e:"local"===n||"system"===n?q.instance:"utc"===n||"gmt"===n?et.utcInstance:et.parseSpecifier(n)||U.create(t)}return mt(t)?et.instance(t):"object"==typeof t&&t.offset&&"number"==typeof t.offset?t:new nt(t)}let st,it=()=>Date.now(),at="system",ot=null,ut=null,lt=null,ct=60;class ht{static get now(){return it}static set now(t){it=t}static set defaultZone(t){at=t}static get defaultZone(){return rt(at,q.instance)}static get defaultLocale(){return ot}static set defaultLocale(t){ot=t}static get defaultNumberingSystem(){return ut}static set defaultNumberingSystem(t){ut=t}static get defaultOutputCalendar(){return lt}static set defaultOutputCalendar(t){lt=t}static get twoDigitCutoffYear(){return ct}static set twoDigitCutoffYear(t){ct=t%100}static get throwOnInvalid(){return st}static set throwOnInvalid(t){st=t}static resetCaches(){X.resetCache(),U.resetCache()}}function dt(t){return void 0===t}function mt(t){return"number"==typeof t}function ft(t){return"number"==typeof t&&t%1==0}function yt(){try{return"undefined"!=typeof Intl&&!!Intl.RelativeTimeFormat}catch(t){return!1}}function gt(t,e,n){if(0!==t.length)return t.reduce(((t,r)=>{const s=[e(r),r];return t&&n(t[0],s[0])===t[0]?t:s}),null)[1]}function wt(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function pt(t,e,n){return ft(t)&&t>=e&&t<=n}function vt(t,e=2){let n;return n=t<0?"-"+(""+-t).padStart(e,"0"):(""+t).padStart(e,"0"),n}function Tt(t){return dt(t)||null===t||""===t?void 0:parseInt(t,10)}function St(t){return dt(t)||null===t||""===t?void 0:parseFloat(t)}function Ot(t){if(!dt(t)&&null!==t&&""!==t){const e=1e3*parseFloat("0."+t);return Math.floor(e)}}function bt(t,e,n=!1){const r=10**e;return(n?Math.trunc:Math.round)(t*r)/r}function kt(t){return t%4==0&&(t%100!=0||t%400==0)}function Mt(t){return kt(t)?366:365}function Nt(t,e){const n=(r=e-1)-12*Math.floor(r/12)+1;var r;return 2===n?kt(t+(e-n)/12)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function Dt(t){let e=Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,t.second,t.millisecond);return t.year<100&&t.year>=0&&(e=new Date(e),e.setUTCFullYear(e.getUTCFullYear()-1900)),+e}function Et(t){const e=(t+Math.floor(t/4)-Math.floor(t/100)+Math.floor(t/400))%7,n=t-1,r=(n+Math.floor(n/4)-Math.floor(n/100)+Math.floor(n/400))%7;return 4===e||3===r?53:52}function Vt(t){return t>99?t:t>ht.twoDigitCutoffYear?1900+t:2e3+t}function xt(t,e,n,r=null){const s=new Date(t),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(i.timeZone=r);const a={timeZoneName:e,...i},o=new Intl.DateTimeFormat(n,a).formatToParts(s).find((t=>"timezonename"===t.type.toLowerCase()));return o?o.value:null}function It(t,e){let n=parseInt(t,10);Number.isNaN(n)&&(n=0);const r=parseInt(e,10)||0;return 60*n+(n<0||Object.is(n,-0)?-r:r)}function Ct(t){const e=Number(t);if("boolean"==typeof t||""===t||Number.isNaN(e))throw new l(`Invalid unit value ${t}`);return e}function Ft(t,e){const n={};for(const r in t)if(wt(t,r)){const s=t[r];if(null==s)continue;n[e(r)]=Ct(s)}return n}function Zt(t,e){const n=Math.trunc(Math.abs(t/60)),r=Math.trunc(Math.abs(t%60)),s=t>=0?"+":"-";switch(e){case"short":return`${s}${vt(n,2)}:${vt(r,2)}`;case"narrow":return`${s}${n}${r>0?`:${r}`:""}`;case"techie":return`${s}${vt(n,2)}${vt(r,2)}`;default:throw new RangeError(`Value format ${e} is out of range for property format`)}}function $t(t){return function(t,e){return["hour","minute","second","millisecond"].reduce(((e,n)=>(e[n]=t[n],e)),{})}(t)}const Lt=["January","February","March","April","May","June","July","August","September","October","November","December"],zt=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],qt=["J","F","M","A","M","J","J","A","S","O","N","D"];function At(t){switch(t){case"narrow":return[...qt];case"short":return[...zt];case"long":return[...Lt];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}const jt=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],_t=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],Ut=["M","T","W","T","F","S","S"];function Ht(t){switch(t){case"narrow":return[...Ut];case"short":return[..._t];case"long":return[...jt];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}const Wt=["AM","PM"],Rt=["Before Christ","Anno Domini"],Yt=["BC","AD"],Jt=["B","A"];function Pt(t){switch(t){case"narrow":return[...Jt];case"short":return[...Yt];case"long":return[...Rt];default:return null}}function Gt(t,e){let n="";for(const r of t)r.literal?n+=r.val:n+=e(r.val);return n}const Bt={D:f,DD:y,DDD:w,DDDD:p,t:v,tt:T,ttt:S,tttt:O,T:b,TT:k,TTT:M,TTTT:N,f:D,ff:V,fff:C,ffff:Z,F:E,FF:x,FFF:F,FFFF:$};class Qt{static create(t,e={}){return new Qt(t,e)}static parseFormat(t){let e=null,n="",r=!1;const s=[];for(let i=0;i<t.length;i++){const a=t.charAt(i);"'"===a?(n.length>0&&s.push({literal:r,val:n}),e=null,n="",r=!r):r||a===e?n+=a:(n.length>0&&s.push({literal:!1,val:n}),n=a,e=a)}return n.length>0&&s.push({literal:r,val:n}),s}static macroTokenToFormatOpts(t){return Bt[t]}constructor(t,e){this.opts=e,this.loc=t,this.systemLoc=null}formatWithSystemDefault(t,e){return null===this.systemLoc&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(t,{...this.opts,...e}).format()}formatDateTime(t,e={}){return this.loc.dtFormatter(t,{...this.opts,...e}).format()}formatDateTimeParts(t,e={}){return this.loc.dtFormatter(t,{...this.opts,...e}).formatToParts()}formatInterval(t,e={}){return this.loc.dtFormatter(t.start,{...this.opts,...e}).dtf.formatRange(t.start.toJSDate(),t.end.toJSDate())}resolvedOptions(t,e={}){return this.loc.dtFormatter(t,{...this.opts,...e}).resolvedOptions()}num(t,e=0){if(this.opts.forceSimple)return vt(t,e);const n={...this.opts};return e>0&&(n.padTo=e),this.loc.numberFormatter(n).format(t)}formatDateTimeFromString(t,e){const n="en"===this.loc.listingMode(),r=this.loc.outputCalendar&&"gregory"!==this.loc.outputCalendar,s=(e,n)=>this.loc.extract(t,e,n),i=e=>t.isOffsetFixed&&0===t.offset&&e.allowZ?"Z":t.isValid?t.zone.formatOffset(t.ts,e.format):"",a=(e,r)=>n?function(t,e){return At(e)[t.month-1]}(t,e):s(r?{month:e}:{month:e,day:"numeric"},"month"),o=(e,r)=>n?function(t,e){return Ht(e)[t.weekday-1]}(t,e):s(r?{weekday:e}:{weekday:e,month:"long",day:"numeric"},"weekday"),u=e=>{const n=Qt.macroTokenToFormatOpts(e);return n?this.formatWithSystemDefault(t,n):e},l=e=>n?function(t,e){return Pt(e)[t.year<0?0:1]}(t,e):s({era:e},"era");return Gt(Qt.parseFormat(e),(e=>{switch(e){case"S":return this.num(t.millisecond);case"u":case"SSS":return this.num(t.millisecond,3);case"s":return this.num(t.second);case"ss":return this.num(t.second,2);case"uu":return this.num(Math.floor(t.millisecond/10),2);case"uuu":return this.num(Math.floor(t.millisecond/100));case"m":return this.num(t.minute);case"mm":return this.num(t.minute,2);case"h":return this.num(t.hour%12==0?12:t.hour%12);case"hh":return this.num(t.hour%12==0?12:t.hour%12,2);case"H":return this.num(t.hour);case"HH":return this.num(t.hour,2);case"Z":return i({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return i({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return i({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return t.zone.offsetName(t.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return t.zone.offsetName(t.ts,{format:"long",locale:this.loc.locale});case"z":return t.zoneName;case"a":return n?function(t){return Wt[t.hour<12?0:1]}(t):s({hour:"numeric",hourCycle:"h12"},"dayperiod");case"d":return r?s({day:"numeric"},"day"):this.num(t.day);case"dd":return r?s({day:"2-digit"},"day"):this.num(t.day,2);case"c":case"E":return this.num(t.weekday);case"ccc":return o("short",!0);case"cccc":return o("long",!0);case"ccccc":return o("narrow",!0);case"EEE":return o("short",!1);case"EEEE":return o("long",!1);case"EEEEE":return o("narrow",!1);case"L":return r?s({month:"numeric",day:"numeric"},"month"):this.num(t.month);case"LL":return r?s({month:"2-digit",day:"numeric"},"month"):this.num(t.month,2);case"LLL":return a("short",!0);case"LLLL":return a("long",!0);case"LLLLL":return a("narrow",!0);case"M":return r?s({month:"numeric"},"month"):this.num(t.month);case"MM":return r?s({month:"2-digit"},"month"):this.num(t.month,2);case"MMM":return a("short",!1);case"MMMM":return a("long",!1);case"MMMMM":return a("narrow",!1);case"y":return r?s({year:"numeric"},"year"):this.num(t.year);case"yy":return r?s({year:"2-digit"},"year"):this.num(t.year.toString().slice(-2),2);case"yyyy":return r?s({year:"numeric"},"year"):this.num(t.year,4);case"yyyyyy":return r?s({year:"numeric"},"year"):this.num(t.year,6);case"G":return l("short");case"GG":return l("long");case"GGGGG":return l("narrow");case"kk":return this.num(t.weekYear.toString().slice(-2),2);case"kkkk":return this.num(t.weekYear,4);case"W":return this.num(t.weekNumber);case"WW":return this.num(t.weekNumber,2);case"o":return this.num(t.ordinal);case"ooo":return this.num(t.ordinal,3);case"q":return this.num(t.quarter);case"qq":return this.num(t.quarter,2);case"X":return this.num(Math.floor(t.ts/1e3));case"x":return this.num(t.ts);default:return u(e)}}))}formatDurationFromString(t,e){const n=t=>{switch(t[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},r=Qt.parseFormat(e),s=r.reduce(((t,{literal:e,val:n})=>e?t:t.concat(n)),[]);return Gt(r,(t=>e=>{const r=n(e);return r?this.num(t.get(r),e.length):e})(t.shiftTo(...s.map(n).filter((t=>t)))))}}class Kt{constructor(t,e){this.reason=t,this.explanation=e}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}const Xt=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function te(...t){const e=t.reduce(((t,e)=>t+e.source),"");return RegExp(`^${e}$`)}function ee(...t){return e=>t.reduce((([t,n,r],s)=>{const[i,a,o]=s(e,r);return[{...t,...i},a||n,o]}),[{},null,1]).slice(0,2)}function ne(t,...e){if(null==t)return[null,null];for(const[n,r]of e){const e=n.exec(t);if(e)return r(e)}return[null,null]}function re(...t){return(e,n)=>{const r={};let s;for(s=0;s<t.length;s++)r[t[s]]=Tt(e[n+s]);return[r,null,n+s]}}const se=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,ie=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,ae=RegExp(`${ie.source}(?:${se.source}?(?:\\[(${Xt.source})\\])?)?`),oe=RegExp(`(?:T${ae.source})?`),ue=re("weekYear","weekNumber","weekDay"),le=re("year","ordinal"),ce=RegExp(`${ie.source} ?(?:${se.source}|(${Xt.source}))?`),he=RegExp(`(?: ${ce.source})?`);function de(t,e,n){const r=t[e];return dt(r)?n:Tt(r)}function me(t,e){return[{hours:de(t,e,0),minutes:de(t,e+1,0),seconds:de(t,e+2,0),milliseconds:Ot(t[e+3])},null,e+4]}function fe(t,e){const n=!t[e]&&!t[e+1],r=It(t[e+1],t[e+2]);return[{},n?null:et.instance(r),e+3]}function ye(t,e){return[{},t[e]?U.create(t[e]):null,e+1]}const ge=RegExp(`^T?${ie.source}$`),we=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function pe(t){const[e,n,r,s,i,a,o,u,l]=t,c="-"===e[0],h=u&&"-"===u[0],d=(t,e=!1)=>void 0!==t&&(e||t&&c)?-t:t;return[{years:d(St(n)),months:d(St(r)),weeks:d(St(s)),days:d(St(i)),hours:d(St(a)),minutes:d(St(o)),seconds:d(St(u),"-0"===u),milliseconds:d(Ot(l),h)}]}const ve={GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function Te(t,e,n,r,s,i,a){const o={year:2===e.length?Vt(Tt(e)):Tt(e),month:zt.indexOf(n)+1,day:Tt(r),hour:Tt(s),minute:Tt(i)};return a&&(o.second=Tt(a)),t&&(o.weekday=t.length>3?jt.indexOf(t)+1:_t.indexOf(t)+1),o}const Se=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function Oe(t){const[,e,n,r,s,i,a,o,u,l,c,h]=t,d=Te(e,s,r,n,i,a,o);let m;return m=u?ve[u]:l?0:It(c,h),[d,new et(m)]}const be=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,ke=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,Me=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function Ne(t){const[,e,n,r,s,i,a,o]=t;return[Te(e,s,r,n,i,a,o),et.utcInstance]}function De(t){const[,e,n,r,s,i,a,o]=t;return[Te(e,o,n,r,s,i,a),et.utcInstance]}const Ee=te(/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,oe),Ve=te(/(\d{4})-?W(\d\d)(?:-?(\d))?/,oe),xe=te(/(\d{4})-?(\d{3})/,oe),Ie=te(ae),Ce=ee((function(t,e){return[{year:de(t,e),month:de(t,e+1,1),day:de(t,e+2,1)},null,e+3]}),me,fe,ye),Fe=ee(ue,me,fe,ye),Ze=ee(le,me,fe,ye),$e=ee(me,fe,ye),Le=ee(me),ze=te(/(\d{4})-(\d\d)-(\d\d)/,he),qe=te(ce),Ae=ee(me,fe,ye),je={weeks:{days:7,hours:168,minutes:10080,seconds:604800,milliseconds:6048e5},days:{hours:24,minutes:1440,seconds:86400,milliseconds:864e5},hours:{minutes:60,seconds:3600,milliseconds:36e5},minutes:{seconds:60,milliseconds:6e4},seconds:{milliseconds:1e3}},_e={years:{quarters:4,months:12,weeks:52,days:365,hours:8760,minutes:525600,seconds:31536e3,milliseconds:31536e6},quarters:{months:3,weeks:13,days:91,hours:2184,minutes:131040,seconds:7862400,milliseconds:78624e5},months:{weeks:4,days:30,hours:720,minutes:43200,seconds:2592e3,milliseconds:2592e6},...je},Ue={years:{quarters:4,months:12,weeks:52.1775,days:365.2425,hours:8765.82,minutes:525949.2,seconds:525949.2*60,milliseconds:525949.2*60*1e3},quarters:{months:3,weeks:13.044375,days:91.310625,hours:2191.455,minutes:131487.3,seconds:525949.2*60/4,milliseconds:7889237999.999999},months:{weeks:4.3481250000000005,days:30.436875,hours:730.485,minutes:43829.1,seconds:2629746,milliseconds:2629746e3},...je},He=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],We=He.slice(0).reverse();function Re(t,e,n=!1){const r={values:n?e.values:{...t.values,...e.values||{}},loc:t.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||t.conversionAccuracy,matrix:e.matrix||t.matrix};return new Je(r)}function Ye(t,e,n,r,s){const i=t[s][n],a=e[n]/i,o=Math.sign(a)!==Math.sign(r[s])&&0!==r[s]&&Math.abs(a)<=1?function(t){return t<0?Math.floor(t):Math.ceil(t)}(a):Math.trunc(a);r[s]+=o,e[n]-=o*i}class Je{constructor(t){const e="longterm"===t.conversionAccuracy||!1;let n=e?Ue:_e;t.matrix&&(n=t.matrix),this.values=t.values,this.loc=t.loc||X.create(),this.conversionAccuracy=e?"longterm":"casual",this.invalid=t.invalid||null,this.matrix=n,this.isLuxonDuration=!0}static fromMillis(t,e){return Je.fromObject({milliseconds:t},e)}static fromObject(t,e={}){if(null==t||"object"!=typeof t)throw new l("Duration.fromObject: argument expected to be an object, got "+(null===t?"null":typeof t));return new Je({values:Ft(t,Je.normalizeUnit),loc:X.fromObject(e),conversionAccuracy:e.conversionAccuracy,matrix:e.matrix})}static fromDurationLike(t){if(mt(t))return Je.fromMillis(t);if(Je.isDuration(t))return t;if("object"==typeof t)return Je.fromObject(t);throw new l(`Unknown duration argument ${t} of type ${typeof t}`)}static fromISO(t,e){const[n]=function(t){return ne(t,[we,pe])}(t);return n?Je.fromObject(n,e):Je.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static fromISOTime(t,e){const[n]=function(t){return ne(t,[ge,Le])}(t);return n?Je.fromObject(n,e):Je.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static invalid(t,e=null){if(!t)throw new l("need to specify a reason the Duration is invalid");const n=t instanceof Kt?t:new Kt(t,e);if(ht.throwOnInvalid)throw new a(n);return new Je({invalid:n})}static normalizeUnit(t){const e={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[t?t.toLowerCase():t];if(!e)throw new u(t);return e}static isDuration(t){return t&&t.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(t,e={}){const n={...e,floor:!1!==e.round&&!1!==e.floor};return this.isValid?Qt.create(this.loc,n).formatDurationFromString(this,t):"Invalid Duration"}toHuman(t={}){const e=He.map((e=>{const n=this.values[e];return dt(n)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...t,unit:e.slice(0,-1)}).format(n)})).filter((t=>t));return this.loc.listFormatter({type:"conjunction",style:t.listStyle||"narrow",...t}).format(e)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let t="P";return 0!==this.years&&(t+=this.years+"Y"),0===this.months&&0===this.quarters||(t+=this.months+3*this.quarters+"M"),0!==this.weeks&&(t+=this.weeks+"W"),0!==this.days&&(t+=this.days+"D"),0===this.hours&&0===this.minutes&&0===this.seconds&&0===this.milliseconds||(t+="T"),0!==this.hours&&(t+=this.hours+"H"),0!==this.minutes&&(t+=this.minutes+"M"),0===this.seconds&&0===this.milliseconds||(t+=bt(this.seconds+this.milliseconds/1e3,3)+"S"),"P"===t&&(t+="T0S"),t}toISOTime(t={}){if(!this.isValid)return null;const e=this.toMillis();if(e<0||e>=864e5)return null;t={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...t};const n=this.shiftTo("hours","minutes","seconds","milliseconds");let r="basic"===t.format?"hhmm":"hh:mm";t.suppressSeconds&&0===n.seconds&&0===n.milliseconds||(r+="basic"===t.format?"ss":":ss",t.suppressMilliseconds&&0===n.milliseconds||(r+=".SSS"));let s=n.toFormat(r);return t.includePrefix&&(s="T"+s),s}toJSON(){return this.toISO()}toString(){return this.toISO()}toMillis(){return this.as("milliseconds")}valueOf(){return this.toMillis()}plus(t){if(!this.isValid)return this;const e=Je.fromDurationLike(t),n={};for(const t of He)(wt(e.values,t)||wt(this.values,t))&&(n[t]=e.get(t)+this.get(t));return Re(this,{values:n},!0)}minus(t){if(!this.isValid)return this;const e=Je.fromDurationLike(t);return this.plus(e.negate())}mapUnits(t){if(!this.isValid)return this;const e={};for(const n of Object.keys(this.values))e[n]=Ct(t(this.values[n],n));return Re(this,{values:e},!0)}get(t){return this[Je.normalizeUnit(t)]}set(t){return this.isValid?Re(this,{values:{...this.values,...Ft(t,Je.normalizeUnit)}}):this}reconfigure({locale:t,numberingSystem:e,conversionAccuracy:n,matrix:r}={}){return Re(this,{loc:this.loc.clone({locale:t,numberingSystem:e}),matrix:r,conversionAccuracy:n})}as(t){return this.isValid?this.shiftTo(t).get(t):NaN}normalize(){if(!this.isValid)return this;const t=this.toObject();return function(t,e){We.reduce(((n,r)=>dt(e[r])?n:(n&&Ye(t,e,n,e,r),r)),null)}(this.matrix,t),Re(this,{values:t},!0)}rescale(){return this.isValid?Re(this,{values:function(t){const e={};for(const[n,r]of Object.entries(t))0!==r&&(e[n]=r);return e}(this.normalize().shiftToAll().toObject())},!0):this}shiftTo(...t){if(!this.isValid)return this;if(0===t.length)return this;t=t.map((t=>Je.normalizeUnit(t)));const e={},n={},r=this.toObject();let s;for(const i of He)if(t.indexOf(i)>=0){s=i;let t=0;for(const e in n)t+=this.matrix[e][i]*n[e],n[e]=0;mt(r[i])&&(t+=r[i]);const a=Math.trunc(t);e[i]=a,n[i]=(1e3*t-1e3*a)/1e3;for(const t in r)He.indexOf(t)>He.indexOf(i)&&Ye(this.matrix,r,t,e,i)}else mt(r[i])&&(n[i]=r[i]);for(const t in n)0!==n[t]&&(e[s]+=t===s?n[t]:n[t]/this.matrix[s][t]);return Re(this,{values:e},!0).normalize()}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;const t={};for(const e of Object.keys(this.values))t[e]=0===this.values[e]?0:-this.values[e];return Re(this,{values:t},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return null===this.invalid}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(t){if(!this.isValid||!t.isValid)return!1;if(!this.loc.equals(t.loc))return!1;for(const r of He)if(e=this.values[r],n=t.values[r],!(void 0===e||0===e?void 0===n||0===n:e===n))return!1;var e,n;return!0}}const Pe="Invalid Interval";class Ge{constructor(t){this.s=t.start,this.e=t.end,this.invalid=t.invalid||null,this.isLuxonInterval=!0}static invalid(t,e=null){if(!t)throw new l("need to specify a reason the Interval is invalid");const n=t instanceof Kt?t:new Kt(t,e);if(ht.throwOnInvalid)throw new i(n);return new Ge({invalid:n})}static fromDateTimes(t,e){const n=Kn(t),r=Kn(e),s=function(t,e){return t&&t.isValid?e&&e.isValid?e<t?Ge.invalid("end before start",`The end of an interval must be after its start, but you had start=${t.toISO()} and end=${e.toISO()}`):null:Ge.invalid("missing or invalid end"):Ge.invalid("missing or invalid start")}(n,r);return null==s?new Ge({start:n,end:r}):s}static after(t,e){const n=Je.fromDurationLike(e),r=Kn(t);return Ge.fromDateTimes(r,r.plus(n))}static before(t,e){const n=Je.fromDurationLike(e),r=Kn(t);return Ge.fromDateTimes(r.minus(n),r)}static fromISO(t,e){const[n,r]=(t||"").split("/",2);if(n&&r){let t,s,i,a;try{t=Qn.fromISO(n,e),s=t.isValid}catch(r){s=!1}try{i=Qn.fromISO(r,e),a=i.isValid}catch(r){a=!1}if(s&&a)return Ge.fromDateTimes(t,i);if(s){const n=Je.fromISO(r,e);if(n.isValid)return Ge.after(t,n)}else if(a){const t=Je.fromISO(n,e);if(t.isValid)return Ge.before(i,t)}}return Ge.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static isInterval(t){return t&&t.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return null===this.invalidReason}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(t="milliseconds"){return this.isValid?this.toDuration(t).get(t):NaN}count(t="milliseconds"){if(!this.isValid)return NaN;const e=this.start.startOf(t),n=this.end.startOf(t);return Math.floor(n.diff(e,t).get(t))+1}hasSame(t){return!!this.isValid&&(this.isEmpty()||this.e.minus(1).hasSame(this.s,t))}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(t){return!!this.isValid&&this.s>t}isBefore(t){return!!this.isValid&&this.e<=t}contains(t){return!!this.isValid&&this.s<=t&&this.e>t}set({start:t,end:e}={}){return this.isValid?Ge.fromDateTimes(t||this.s,e||this.e):this}splitAt(...t){if(!this.isValid)return[];const e=t.map(Kn).filter((t=>this.contains(t))).sort(),n=[];let{s:r}=this,s=0;for(;r<this.e;){const t=e[s]||this.e,i=+t>+this.e?this.e:t;n.push(Ge.fromDateTimes(r,i)),r=i,s+=1}return n}splitBy(t){const e=Je.fromDurationLike(t);if(!this.isValid||!e.isValid||0===e.as("milliseconds"))return[];let n,{s:r}=this,s=1;const i=[];for(;r<this.e;){const t=this.start.plus(e.mapUnits((t=>t*s)));n=+t>+this.e?this.e:t,i.push(Ge.fromDateTimes(r,n)),r=n,s+=1}return i}divideEqually(t){return this.isValid?this.splitBy(this.length()/t).slice(0,t):[]}overlaps(t){return this.e>t.s&&this.s<t.e}abutsStart(t){return!!this.isValid&&+this.e==+t.s}abutsEnd(t){return!!this.isValid&&+t.e==+this.s}engulfs(t){return!!this.isValid&&this.s<=t.s&&this.e>=t.e}equals(t){return!(!this.isValid||!t.isValid)&&this.s.equals(t.s)&&this.e.equals(t.e)}intersection(t){if(!this.isValid)return this;const e=this.s>t.s?this.s:t.s,n=this.e<t.e?this.e:t.e;return e>=n?null:Ge.fromDateTimes(e,n)}union(t){if(!this.isValid)return this;const e=this.s<t.s?this.s:t.s,n=this.e>t.e?this.e:t.e;return Ge.fromDateTimes(e,n)}static merge(t){const[e,n]=t.sort(((t,e)=>t.s-e.s)).reduce((([t,e],n)=>e?e.overlaps(n)||e.abutsStart(n)?[t,e.union(n)]:[t.concat([e]),n]:[t,n]),[[],null]);return n&&e.push(n),e}static xor(t){let e=null,n=0;const r=[],s=t.map((t=>[{time:t.s,type:"s"},{time:t.e,type:"e"}])),i=Array.prototype.concat(...s).sort(((t,e)=>t.time-e.time));for(const t of i)n+="s"===t.type?1:-1,1===n?e=t.time:(e&&+e!=+t.time&&r.push(Ge.fromDateTimes(e,t.time)),e=null);return Ge.merge(r)}difference(...t){return Ge.xor([this].concat(t)).map((t=>this.intersection(t))).filter((t=>t&&!t.isEmpty()))}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:Pe}toLocaleString(t=f,e={}){return this.isValid?Qt.create(this.s.loc.clone(e),t).formatInterval(this):Pe}toISO(t){return this.isValid?`${this.s.toISO(t)}/${this.e.toISO(t)}`:Pe}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:Pe}toISOTime(t){return this.isValid?`${this.s.toISOTime(t)}/${this.e.toISOTime(t)}`:Pe}toFormat(t,{separator:e=" – "}={}){return this.isValid?`${this.s.toFormat(t)}${e}${this.e.toFormat(t)}`:Pe}toDuration(t,e){return this.isValid?this.e.diff(this.s,t,e):Je.invalid(this.invalidReason)}mapEndpoints(t){return Ge.fromDateTimes(t(this.s),t(this.e))}}class Be{static hasDST(t=ht.defaultZone){const e=Qn.now().setZone(t).set({month:12});return!t.isUniversal&&e.offset!==e.set({month:6}).offset}static isValidIANAZone(t){return U.isValidZone(t)}static normalizeZone(t){return rt(t,ht.defaultZone)}static months(t="long",{locale:e=null,numberingSystem:n=null,locObj:r=null,outputCalendar:s="gregory"}={}){return(r||X.create(e,n,s)).months(t)}static monthsFormat(t="long",{locale:e=null,numberingSystem:n=null,locObj:r=null,outputCalendar:s="gregory"}={}){return(r||X.create(e,n,s)).months(t,!0)}static weekdays(t="long",{locale:e=null,numberingSystem:n=null,locObj:r=null}={}){return(r||X.create(e,n,null)).weekdays(t)}static weekdaysFormat(t="long",{locale:e=null,numberingSystem:n=null,locObj:r=null}={}){return(r||X.create(e,n,null)).weekdays(t,!0)}static meridiems({locale:t=null}={}){return X.create(t).meridiems()}static eras(t="short",{locale:e=null}={}){return X.create(e,null,"gregory").eras(t)}static features(){return{relative:yt()}}}function Qe(t,e){const n=t=>t.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=n(e)-n(t);return Math.floor(Je.fromMillis(r).as("days"))}const Ke={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},Xe={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},tn=Ke.hanidec.replace(/[\[|\]]/g,"").split("");function en({numberingSystem:t},e=""){return new RegExp(`${Ke[t||"latn"]}${e}`)}function nn(t,e=(t=>t)){return{regex:t,deser:([t])=>e(function(t){let e=parseInt(t,10);if(isNaN(e)){e="";for(let n=0;n<t.length;n++){const r=t.charCodeAt(n);if(-1!==t[n].search(Ke.hanidec))e+=tn.indexOf(t[n]);else for(const t in Xe){const[n,s]=Xe[t];r>=n&&r<=s&&(e+=r-n)}}return parseInt(e,10)}return e}(t))}}const rn=`[ ${String.fromCharCode(160)}]`,sn=new RegExp(rn,"g");function an(t){return t.replace(/\./g,"\\.?").replace(sn,rn)}function on(t){return t.replace(/\./g,"").replace(sn," ").toLowerCase()}function un(t,e){return null===t?null:{regex:RegExp(t.map(an).join("|")),deser:([n])=>t.findIndex((t=>on(n)===on(t)))+e}}function ln(t,e){return{regex:t,deser:([,t,e])=>It(t,e),groups:e}}function cn(t){return{regex:t,deser:([t])=>t}}const hn={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour:{numeric:"h","2-digit":"hh"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};let dn=null;function mn(t,e){return Array.prototype.concat(...t.map((t=>function(t,e){if(t.literal)return t;const n=yn(Qt.macroTokenToFormatOpts(t.val),e);return null==n||n.includes(void 0)?t:n}(t,e))))}function fn(t,e,n){const r=mn(Qt.parseFormat(n),t),s=r.map((e=>function(t,e){const n=en(e),r=en(e,"{2}"),s=en(e,"{3}"),i=en(e,"{4}"),a=en(e,"{6}"),o=en(e,"{1,2}"),u=en(e,"{1,3}"),l=en(e,"{1,6}"),c=en(e,"{1,9}"),h=en(e,"{2,4}"),d=en(e,"{4,6}"),m=t=>{return{regex:RegExp((e=t.val,e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"))),deser:([t])=>t,literal:!0};var e},f=(f=>{if(t.literal)return m(f);switch(f.val){case"G":return un(e.eras("short",!1),0);case"GG":return un(e.eras("long",!1),0);case"y":return nn(l);case"yy":case"kk":return nn(h,Vt);case"yyyy":case"kkkk":return nn(i);case"yyyyy":return nn(d);case"yyyyyy":return nn(a);case"M":case"L":case"d":case"H":case"h":case"m":case"q":case"s":case"W":return nn(o);case"MM":case"LL":case"dd":case"HH":case"hh":case"mm":case"qq":case"ss":case"WW":return nn(r);case"MMM":return un(e.months("short",!0,!1),1);case"MMMM":return un(e.months("long",!0,!1),1);case"LLL":return un(e.months("short",!1,!1),1);case"LLLL":return un(e.months("long",!1,!1),1);case"o":case"S":return nn(u);case"ooo":case"SSS":return nn(s);case"u":return cn(c);case"uu":return cn(o);case"uuu":case"E":case"c":return nn(n);case"a":return un(e.meridiems(),0);case"EEE":return un(e.weekdays("short",!1,!1),1);case"EEEE":return un(e.weekdays("long",!1,!1),1);case"ccc":return un(e.weekdays("short",!0,!1),1);case"cccc":return un(e.weekdays("long",!0,!1),1);case"Z":case"ZZ":return ln(new RegExp(`([+-]${o.source})(?::(${r.source}))?`),2);case"ZZZ":return ln(new RegExp(`([+-]${o.source})(${r.source})?`),2);case"z":return cn(/[a-z_+-/]{1,256}?/i);default:return m(f)}})(t)||{invalidReason:"missing Intl.DateTimeFormat.formatToParts support"};return f.token=t,f}(e,t))),i=s.find((t=>t.invalidReason));if(i)return{input:e,tokens:r,invalidReason:i.invalidReason};{const[t,n]=function(t){return[`^${t.map((t=>t.regex)).reduce(((t,e)=>`${t}(${e.source})`),"")}$`,t]}(s),i=RegExp(t,"i"),[a,u]=function(t,e,n){const r=t.match(e);if(r){const t={};let e=1;for(const s in n)if(wt(n,s)){const i=n[s],a=i.groups?i.groups+1:1;!i.literal&&i.token&&(t[i.token.val[0]]=i.deser(r.slice(e,e+a))),e+=a}return[r,t]}return[r,{}]}(e,i,n),[l,c,h]=u?function(t){let e,n=null;return dt(t.z)||(n=U.create(t.z)),dt(t.Z)||(n||(n=new et(t.Z)),e=t.Z),dt(t.q)||(t.M=3*(t.q-1)+1),dt(t.h)||(t.h<12&&1===t.a?t.h+=12:12===t.h&&0===t.a&&(t.h=0)),0===t.G&&t.y&&(t.y=-t.y),dt(t.u)||(t.S=Ot(t.u)),[Object.keys(t).reduce(((e,n)=>{const r=(t=>{switch(t){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}})(n);return r&&(e[r]=t[n]),e}),{}),n,e]}(u):[null,null,void 0];if(wt(u,"a")&&wt(u,"H"))throw new o("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:r,regex:i,rawMatches:a,matches:u,result:l,zone:c,specificOffset:h}}}function yn(t,e){return t?Qt.create(e,t).formatDateTimeParts((dn||(dn=Qn.fromMillis(1555555555555)),dn)).map((e=>function(t,e){const{type:n,value:r}=t;if("literal"===n)return{literal:!0,val:r};const s=e[n];let i=hn[n];return"object"==typeof i&&(i=i[s]),i?{literal:!1,val:i}:void 0}(e,t))):null}const gn=[0,31,59,90,120,151,181,212,243,273,304,334],wn=[0,31,60,91,121,152,182,213,244,274,305,335];function pn(t,e){return new Kt("unit out of range",`you specified ${e} (of type ${typeof e}) as a ${t}, which is invalid`)}function vn(t,e,n){const r=new Date(Date.UTC(t,e-1,n));t<100&&t>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);const s=r.getUTCDay();return 0===s?7:s}function Tn(t,e,n){return n+(kt(t)?wn:gn)[e-1]}function Sn(t,e){const n=kt(t)?wn:gn,r=n.findIndex((t=>t<e));return{month:r+1,day:e-n[r]}}function On(t){const{year:e,month:n,day:r}=t,s=Tn(e,n,r),i=vn(e,n,r);let a,o=Math.floor((s-i+10)/7);return o<1?(a=e-1,o=Et(a)):o>Et(e)?(a=e+1,o=1):a=e,{weekYear:a,weekNumber:o,weekday:i,...$t(t)}}function bn(t){const{weekYear:e,weekNumber:n,weekday:r}=t,s=vn(e,1,4),i=Mt(e);let a,o=7*n+r-s-3;o<1?(a=e-1,o+=Mt(a)):o>i?(a=e+1,o-=Mt(e)):a=e;const{month:u,day:l}=Sn(a,o);return{year:a,month:u,day:l,...$t(t)}}function kn(t){const{year:e,month:n,day:r}=t;return{year:e,ordinal:Tn(e,n,r),...$t(t)}}function Mn(t){const{year:e,ordinal:n}=t,{month:r,day:s}=Sn(e,n);return{year:e,month:r,day:s,...$t(t)}}function Nn(t){const e=ft(t.year),n=pt(t.month,1,12),r=pt(t.day,1,Nt(t.year,t.month));return e?n?!r&&pn("day",t.day):pn("month",t.month):pn("year",t.year)}function Dn(t){const{hour:e,minute:n,second:r,millisecond:s}=t,i=pt(e,0,23)||24===e&&0===n&&0===r&&0===s,a=pt(n,0,59),o=pt(r,0,59),u=pt(s,0,999);return i?a?o?!u&&pn("millisecond",s):pn("second",r):pn("minute",n):pn("hour",e)}const En="Invalid DateTime",Vn=864e13;function xn(t){return new Kt("unsupported zone",`the zone "${t.name}" is not supported`)}function In(t){return null===t.weekData&&(t.weekData=On(t.c)),t.weekData}function Cn(t,e){const n={ts:t.ts,zone:t.zone,c:t.c,o:t.o,loc:t.loc,invalid:t.invalid};return new Qn({...n,...e,old:n})}function Fn(t,e,n){let r=t-60*e*1e3;const s=n.offset(r);if(e===s)return[r,e];r-=60*(s-e)*1e3;const i=n.offset(r);return s===i?[r,s]:[t-60*Math.min(s,i)*1e3,Math.max(s,i)]}function Zn(t,e){const n=new Date(t+=60*e*1e3);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function $n(t,e,n){return Fn(Dt(t),e,n)}function Ln(t,e){const n=t.o,r=t.c.year+Math.trunc(e.years),s=t.c.month+Math.trunc(e.months)+3*Math.trunc(e.quarters),i={...t.c,year:r,month:s,day:Math.min(t.c.day,Nt(r,s))+Math.trunc(e.days)+7*Math.trunc(e.weeks)},a=Je.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),o=Dt(i);let[u,l]=Fn(o,n,t.zone);return 0!==a&&(u+=a,l=t.zone.offset(u)),{ts:u,o:l}}function zn(t,e,n,r,s,i){const{setZone:a,zone:o}=n;if(t&&0!==Object.keys(t).length){const r=e||o,s=Qn.fromObject(t,{...n,zone:r,specificOffset:i});return a?s:s.setZone(o)}return Qn.invalid(new Kt("unparsable",`the input "${s}" can't be parsed as ${r}`))}function qn(t,e,n=!0){return t.isValid?Qt.create(X.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(t,e):null}function An(t,e){const n=t.c.year>9999||t.c.year<0;let r="";return n&&t.c.year>=0&&(r+="+"),r+=vt(t.c.year,n?6:4),e?(r+="-",r+=vt(t.c.month),r+="-",r+=vt(t.c.day)):(r+=vt(t.c.month),r+=vt(t.c.day)),r}function jn(t,e,n,r,s,i){let a=vt(t.c.hour);return e?(a+=":",a+=vt(t.c.minute),0===t.c.second&&n||(a+=":")):a+=vt(t.c.minute),0===t.c.second&&n||(a+=vt(t.c.second),0===t.c.millisecond&&r||(a+=".",a+=vt(t.c.millisecond,3))),s&&(t.isOffsetFixed&&0===t.offset&&!i?a+="Z":t.o<0?(a+="-",a+=vt(Math.trunc(-t.o/60)),a+=":",a+=vt(Math.trunc(-t.o%60))):(a+="+",a+=vt(Math.trunc(t.o/60)),a+=":",a+=vt(Math.trunc(t.o%60)))),i&&(a+="["+t.zone.ianaName+"]"),a}const _n={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},Un={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Hn={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Wn=["year","month","day","hour","minute","second","millisecond"],Rn=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],Yn=["year","ordinal","hour","minute","second","millisecond"];function Jn(t){const e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[t.toLowerCase()];if(!e)throw new u(t);return e}function Pn(t,e){const n=rt(e.zone,ht.defaultZone),r=X.fromObject(e),s=ht.now();let i,a;if(dt(t.year))i=s;else{for(const e of Wn)dt(t[e])&&(t[e]=_n[e]);const e=Nn(t)||Dn(t);if(e)return Qn.invalid(e);const r=n.offset(s);[i,a]=$n(t,r,n)}return new Qn({ts:i,zone:n,loc:r,o:a})}function Gn(t,e,n){const r=!!dt(n.round)||n.round,s=(t,s)=>(t=bt(t,r||n.calendary?0:2,!0),e.loc.clone(n).relFormatter(n).format(t,s)),i=r=>n.calendary?e.hasSame(t,r)?0:e.startOf(r).diff(t.startOf(r),r).get(r):e.diff(t,r).get(r);if(n.unit)return s(i(n.unit),n.unit);for(const t of n.units){const e=i(t);if(Math.abs(e)>=1)return s(e,t)}return s(t>e?-0:0,n.units[n.units.length-1])}function Bn(t){let e,n={};return t.length>0&&"object"==typeof t[t.length-1]?(n=t[t.length-1],e=Array.from(t).slice(0,t.length-1)):e=Array.from(t),[n,e]}class Qn{constructor(t){const e=t.zone||ht.defaultZone;let n=t.invalid||(Number.isNaN(t.ts)?new Kt("invalid input"):null)||(e.isValid?null:xn(e));this.ts=dt(t.ts)?ht.now():t.ts;let r=null,s=null;if(!n)if(t.old&&t.old.ts===this.ts&&t.old.zone.equals(e))[r,s]=[t.old.c,t.old.o];else{const t=e.offset(this.ts);r=Zn(this.ts,t),n=Number.isNaN(r.year)?new Kt("invalid input"):null,r=n?null:r,s=n?null:t}this._zone=e,this.loc=t.loc||X.create(),this.invalid=n,this.weekData=null,this.c=r,this.o=s,this.isLuxonDateTime=!0}static now(){return new Qn({})}static local(){const[t,e]=Bn(arguments),[n,r,s,i,a,o,u]=e;return Pn({year:n,month:r,day:s,hour:i,minute:a,second:o,millisecond:u},t)}static utc(){const[t,e]=Bn(arguments),[n,r,s,i,a,o,u]=e;return t.zone=et.utcInstance,Pn({year:n,month:r,day:s,hour:i,minute:a,second:o,millisecond:u},t)}static fromJSDate(t,e={}){const n=(r=t,"[object Date]"===Object.prototype.toString.call(r)?t.valueOf():NaN);var r;if(Number.isNaN(n))return Qn.invalid("invalid input");const s=rt(e.zone,ht.defaultZone);return s.isValid?new Qn({ts:n,zone:s,loc:X.fromObject(e)}):Qn.invalid(xn(s))}static fromMillis(t,e={}){if(mt(t))return t<-Vn||t>Vn?Qn.invalid("Timestamp out of range"):new Qn({ts:t,zone:rt(e.zone,ht.defaultZone),loc:X.fromObject(e)});throw new l(`fromMillis requires a numerical input, but received a ${typeof t} with value ${t}`)}static fromSeconds(t,e={}){if(mt(t))return new Qn({ts:1e3*t,zone:rt(e.zone,ht.defaultZone),loc:X.fromObject(e)});throw new l("fromSeconds requires a numerical input")}static fromObject(t,e={}){t=t||{};const n=rt(e.zone,ht.defaultZone);if(!n.isValid)return Qn.invalid(xn(n));const r=ht.now(),s=dt(e.specificOffset)?n.offset(r):e.specificOffset,i=Ft(t,Jn),a=!dt(i.ordinal),u=!dt(i.year),l=!dt(i.month)||!dt(i.day),c=u||l,h=i.weekYear||i.weekNumber,d=X.fromObject(e);if((c||a)&&h)throw new o("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(l&&a)throw new o("Can't mix ordinal dates with month/day");const m=h||i.weekday&&!c;let f,y,g=Zn(r,s);m?(f=Rn,y=Un,g=On(g)):a?(f=Yn,y=Hn,g=kn(g)):(f=Wn,y=_n);let w=!1;for(const t of f)dt(i[t])?i[t]=w?y[t]:g[t]:w=!0;const p=m?function(t){const e=ft(t.weekYear),n=pt(t.weekNumber,1,Et(t.weekYear)),r=pt(t.weekday,1,7);return e?n?!r&&pn("weekday",t.weekday):pn("week",t.week):pn("weekYear",t.weekYear)}(i):a?function(t){const e=ft(t.year),n=pt(t.ordinal,1,Mt(t.year));return e?!n&&pn("ordinal",t.ordinal):pn("year",t.year)}(i):Nn(i),v=p||Dn(i);if(v)return Qn.invalid(v);const T=m?bn(i):a?Mn(i):i,[S,O]=$n(T,s,n),b=new Qn({ts:S,zone:n,o:O,loc:d});return i.weekday&&c&&t.weekday!==b.weekday?Qn.invalid("mismatched weekday",`you can't specify both a weekday of ${i.weekday} and a date of ${b.toISO()}`):b}static fromISO(t,e={}){const[n,r]=function(t){return ne(t,[Ee,Ce],[Ve,Fe],[xe,Ze],[Ie,$e])}(t);return zn(n,r,e,"ISO 8601",t)}static fromRFC2822(t,e={}){const[n,r]=function(t){return ne(function(t){return t.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}(t),[Se,Oe])}(t);return zn(n,r,e,"RFC 2822",t)}static fromHTTP(t,e={}){const[n,r]=function(t){return ne(t,[be,Ne],[ke,Ne],[Me,De])}(t);return zn(n,r,e,"HTTP",e)}static fromFormat(t,e,n={}){if(dt(t)||dt(e))throw new l("fromFormat requires an input string and a format");const{locale:r=null,numberingSystem:s=null}=n,i=X.fromOpts({locale:r,numberingSystem:s,defaultToEN:!0}),[a,o,u,c]=function(t,e,n){const{result:r,zone:s,specificOffset:i,invalidReason:a}=fn(t,e,n);return[r,s,i,a]}(i,t,e);return c?Qn.invalid(c):zn(a,o,n,`format ${e}`,t,u)}static fromString(t,e,n={}){return Qn.fromFormat(t,e,n)}static fromSQL(t,e={}){const[n,r]=function(t){return ne(t,[ze,Ce],[qe,Ae])}(t);return zn(n,r,e,"SQL",t)}static invalid(t,e=null){if(!t)throw new l("need to specify a reason the DateTime is invalid");const n=t instanceof Kt?t:new Kt(t,e);if(ht.throwOnInvalid)throw new s(n);return new Qn({invalid:n})}static isDateTime(t){return t&&t.isLuxonDateTime||!1}static parseFormatForOpts(t,e={}){const n=yn(t,X.fromObject(e));return n?n.map((t=>t?t.val:null)).join(""):null}static expandFormat(t,e={}){return mn(Qt.parseFormat(t),X.fromObject(e)).map((t=>t.val)).join("")}get(t){return this[t]}get isValid(){return null===this.invalid}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?In(this).weekYear:NaN}get weekNumber(){return this.isValid?In(this).weekNumber:NaN}get weekday(){return this.isValid?In(this).weekday:NaN}get ordinal(){return this.isValid?kn(this.c).ordinal:NaN}get monthShort(){return this.isValid?Be.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?Be.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?Be.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?Be.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return!this.isOffsetFixed&&(this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset)}get isInLeapYear(){return kt(this.year)}get daysInMonth(){return Nt(this.year,this.month)}get daysInYear(){return this.isValid?Mt(this.year):NaN}get weeksInWeekYear(){return this.isValid?Et(this.weekYear):NaN}resolvedLocaleOptions(t={}){const{locale:e,numberingSystem:n,calendar:r}=Qt.create(this.loc.clone(t),t).resolvedOptions(this);return{locale:e,numberingSystem:n,outputCalendar:r}}toUTC(t=0,e={}){return this.setZone(et.instance(t),e)}toLocal(){return this.setZone(ht.defaultZone)}setZone(t,{keepLocalTime:e=!1,keepCalendarTime:n=!1}={}){if((t=rt(t,ht.defaultZone)).equals(this.zone))return this;if(t.isValid){let r=this.ts;if(e||n){const e=t.offset(this.ts),n=this.toObject();[r]=$n(n,e,t)}return Cn(this,{ts:r,zone:t})}return Qn.invalid(xn(t))}reconfigure({locale:t,numberingSystem:e,outputCalendar:n}={}){return Cn(this,{loc:this.loc.clone({locale:t,numberingSystem:e,outputCalendar:n})})}setLocale(t){return this.reconfigure({locale:t})}set(t){if(!this.isValid)return this;const e=Ft(t,Jn),n=!dt(e.weekYear)||!dt(e.weekNumber)||!dt(e.weekday),r=!dt(e.ordinal),s=!dt(e.year),i=!dt(e.month)||!dt(e.day),a=s||i,u=e.weekYear||e.weekNumber;if((a||r)&&u)throw new o("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(i&&r)throw new o("Can't mix ordinal dates with month/day");let l;n?l=bn({...On(this.c),...e}):dt(e.ordinal)?(l={...this.toObject(),...e},dt(e.day)&&(l.day=Math.min(Nt(l.year,l.month),l.day))):l=Mn({...kn(this.c),...e});const[c,h]=$n(l,this.o,this.zone);return Cn(this,{ts:c,o:h})}plus(t){return this.isValid?Cn(this,Ln(this,Je.fromDurationLike(t))):this}minus(t){return this.isValid?Cn(this,Ln(this,Je.fromDurationLike(t).negate())):this}startOf(t){if(!this.isValid)return this;const e={},n=Je.normalizeUnit(t);switch(n){case"years":e.month=1;case"quarters":case"months":e.day=1;case"weeks":case"days":e.hour=0;case"hours":e.minute=0;case"minutes":e.second=0;case"seconds":e.millisecond=0}if("weeks"===n&&(e.weekday=1),"quarters"===n){const t=Math.ceil(this.month/3);e.month=3*(t-1)+1}return this.set(e)}endOf(t){return this.isValid?this.plus({[t]:1}).startOf(t).minus(1):this}toFormat(t,e={}){return this.isValid?Qt.create(this.loc.redefaultToEN(e)).formatDateTimeFromString(this,t):En}toLocaleString(t=f,e={}){return this.isValid?Qt.create(this.loc.clone(e),t).formatDateTime(this):En}toLocaleParts(t={}){return this.isValid?Qt.create(this.loc.clone(t),t).formatDateTimeParts(this):[]}toISO({format:t="extended",suppressSeconds:e=!1,suppressMilliseconds:n=!1,includeOffset:r=!0,extendedZone:s=!1}={}){if(!this.isValid)return null;const i="extended"===t;let a=An(this,i);return a+="T",a+=jn(this,i,e,n,r,s),a}toISODate({format:t="extended"}={}){return this.isValid?An(this,"extended"===t):null}toISOWeekDate(){return qn(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:t=!1,suppressSeconds:e=!1,includeOffset:n=!0,includePrefix:r=!1,extendedZone:s=!1,format:i="extended"}={}){return this.isValid?(r?"T":"")+jn(this,"extended"===i,e,t,n,s):null}toRFC2822(){return qn(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return qn(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?An(this,!0):null}toSQLTime({includeOffset:t=!0,includeZone:e=!1,includeOffsetSpace:n=!0}={}){let r="HH:mm:ss.SSS";return(e||t)&&(n&&(r+=" "),e?r+="z":t&&(r+="ZZ")),qn(this,r,!0)}toSQL(t={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(t)}`:null}toString(){return this.isValid?this.toISO():En}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(t={}){if(!this.isValid)return{};const e={...this.c};return t.includeConfig&&(e.outputCalendar=this.outputCalendar,e.numberingSystem=this.loc.numberingSystem,e.locale=this.loc.locale),e}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(t,e="milliseconds",n={}){if(!this.isValid||!t.isValid)return Je.invalid("created by diffing an invalid DateTime");const r={locale:this.locale,numberingSystem:this.numberingSystem,...n},s=(o=e,Array.isArray(o)?o:[o]).map(Je.normalizeUnit),i=t.valueOf()>this.valueOf(),a=function(t,e,n,r){let[s,i,a,o]=function(t,e,n){const r=[["years",(t,e)=>e.year-t.year],["quarters",(t,e)=>e.quarter-t.quarter+4*(e.year-t.year)],["months",(t,e)=>e.month-t.month+12*(e.year-t.year)],["weeks",(t,e)=>{const n=Qe(t,e);return(n-n%7)/7}],["days",Qe]],s={},i=t;let a,o;for(const[u,l]of r)n.indexOf(u)>=0&&(a=u,s[u]=l(t,e),o=i.plus(s),o>e?(s[u]--,t=i.plus(s)):t=o);return[t,s,o,a]}(t,e,n);const u=e-s,l=n.filter((t=>["hours","minutes","seconds","milliseconds"].indexOf(t)>=0));0===l.length&&(a<e&&(a=s.plus({[o]:1})),a!==s&&(i[o]=(i[o]||0)+u/(a-s)));const c=Je.fromObject(i,r);return l.length>0?Je.fromMillis(u,r).shiftTo(...l).plus(c):c}(i?this:t,i?t:this,s,r);var o;return i?a.negate():a}diffNow(t="milliseconds",e={}){return this.diff(Qn.now(),t,e)}until(t){return this.isValid?Ge.fromDateTimes(this,t):this}hasSame(t,e){if(!this.isValid)return!1;const n=t.valueOf(),r=this.setZone(t.zone,{keepLocalTime:!0});return r.startOf(e)<=n&&n<=r.endOf(e)}equals(t){return this.isValid&&t.isValid&&this.valueOf()===t.valueOf()&&this.zone.equals(t.zone)&&this.loc.equals(t.loc)}toRelative(t={}){if(!this.isValid)return null;const e=t.base||Qn.fromObject({},{zone:this.zone}),n=t.padding?this<e?-t.padding:t.padding:0;let r=["years","months","days","hours","minutes","seconds"],s=t.unit;return Array.isArray(t.unit)&&(r=t.unit,s=void 0),Gn(e,this.plus(n),{...t,numeric:"always",units:r,unit:s})}toRelativeCalendar(t={}){return this.isValid?Gn(t.base||Qn.fromObject({},{zone:this.zone}),this,{...t,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...t){if(!t.every(Qn.isDateTime))throw new l("min requires all arguments be DateTimes");return gt(t,(t=>t.valueOf()),Math.min)}static max(...t){if(!t.every(Qn.isDateTime))throw new l("max requires all arguments be DateTimes");return gt(t,(t=>t.valueOf()),Math.max)}static fromFormatExplain(t,e,n={}){const{locale:r=null,numberingSystem:s=null}=n;return fn(X.fromOpts({locale:r,numberingSystem:s,defaultToEN:!0}),t,e)}static fromStringExplain(t,e,n={}){return Qn.fromFormatExplain(t,e,n)}static get DATE_SHORT(){return f}static get DATE_MED(){return y}static get DATE_MED_WITH_WEEKDAY(){return g}static get DATE_FULL(){return w}static get DATE_HUGE(){return p}static get TIME_SIMPLE(){return v}static get TIME_WITH_SECONDS(){return T}static get TIME_WITH_SHORT_OFFSET(){return S}static get TIME_WITH_LONG_OFFSET(){return O}static get TIME_24_SIMPLE(){return b}static get TIME_24_WITH_SECONDS(){return k}static get TIME_24_WITH_SHORT_OFFSET(){return M}static get TIME_24_WITH_LONG_OFFSET(){return N}static get DATETIME_SHORT(){return D}static get DATETIME_SHORT_WITH_SECONDS(){return E}static get DATETIME_MED(){return V}static get DATETIME_MED_WITH_SECONDS(){return x}static get DATETIME_MED_WITH_WEEKDAY(){return I}static get DATETIME_FULL(){return C}static get DATETIME_FULL_WITH_SECONDS(){return F}static get DATETIME_HUGE(){return Z}static get DATETIME_HUGE_WITH_SECONDS(){return $}}function Kn(t){if(Qn.isDateTime(t))return t;if(t&&t.valueOf&&mt(t.valueOf()))return Qn.fromJSDate(t);if(t&&"object"==typeof t)return Qn.fromObject(t);throw new l(`Unknown datetime argument: ${t}, of type ${typeof t}`)}}}]);