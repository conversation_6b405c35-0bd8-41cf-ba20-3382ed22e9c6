import{d as h,c as _,g as r,n as w,p as n,aw as B,bh as $,h as y,F as i,q as l,i as c,d5 as x,bw as d,an as b,cy as E,d6 as S,ax as M,av as N,cE as z,C as I}from"./index-r0dFAfgr.js";import{w as L}from"./Point-WxyopZva.js";const R={class:"arc-infowindow-wrapper"},q={class:"content-wrapper"},D=h({__name:"PopLayout",props:{title:{},x:{},y:{},longitude:{},latitude:{},offsetx:{},offsety:{},backgroundColor:{},status:{},showMore:{type:Boolean},showBack:{type:Boolean}},emits:["more","back","toggle"],setup(g,{expose:v,emit:C}){const a=_(),t=g,m=C,f=()=>{var e,o;(o=(e=a.value)==null?void 0:e.parentElement)==null||o.appendChild(a.value)},p=e=>{if(!a.value||!e)return;const o=new L({x:t==null?void 0:t.x,y:t==null?void 0:t.y,longitude:t==null?void 0:t.longitude,latitude:t==null?void 0:t.latitude,spatialReference:e==null?void 0:e.spatialReference}),s=e==null?void 0:e.toScreen(o);a.value.style.left=((s==null?void 0:s.x)||0)+(t.offsetx||0)+"px",a.value.style.top=((s==null?void 0:s.y)||0)+(t.offsety||0)-10+"px"},u=e=>{m("toggle",e)};return v({toggle:u,refreshPosition:p,highlight:f}),(e,o)=>{const s=z;return r(),w("div",{ref_key:"refContainer",ref:a,class:"arc-infowindow",style:N({backgroundColor:e.backgroundColor})},[n("div",R,[n("div",{class:"title-wrapper",onClick:f},[n("span",{class:B(["title",e.status])},$(e.title),3),e.showMore?(r(),y(s,{key:0,class:"btn btn-more",onClick:o[0]||(o[0]=d(k=>e.$emit("more"),["stop"]))},{default:i(()=>[l(c(x))]),_:1})):b("",!0),e.showBack?(r(),y(s,{key:1,class:"btn btn-back",onClick:o[1]||(o[1]=d(k=>e.$emit("back"),["stop"]))},{default:i(()=>[l(c(E))]),_:1})):b("",!0),l(s,{class:"btn btn-close",onClick:o[2]||(o[2]=d(k=>u(!1),["stop"]))},{default:i(()=>[l(c(S))]),_:1})]),n("div",q,[M(e.$slots,"default",{},void 0,!0)])])],4)}}}),A=I(D,[["__scopeId","data-v-cd3bc6d2"]]);export{A as default};
