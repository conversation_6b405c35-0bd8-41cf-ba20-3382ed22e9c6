package org.thingsboard.server.dao.sql.tenant;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.TenantApplicationEntity;
import org.thingsboard.server.dao.model.sql.TenantApplicationGuideEntity;

import java.util.List;

@Mapper
public interface TenantApplicationGuideMapper extends BaseMapper<TenantApplicationGuideEntity> {

    List<TenantApplicationGuideEntity> getList(@Param("tenantId") String tenantId);

    TenantApplicationGuideEntity getByApplicationId(@Param("applicationId") String applicationId);

    List<TenantApplicationEntity> getNotSetList(@Param("tenantId") String tenantId);
}
