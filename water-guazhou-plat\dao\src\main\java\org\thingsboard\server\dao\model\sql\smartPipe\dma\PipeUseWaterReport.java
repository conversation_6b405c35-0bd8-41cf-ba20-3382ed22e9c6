package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 水量填报
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_USE_WATER_REPORT_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PipeUseWaterReport {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_PARTITION_ID)
    private String partitionId;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_UPLOAD_NAME)
    private String uploadName;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_YM)
    private String ym;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_OWN_SUPPLY_WATER)
    private BigDecimal ownSupplyWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_BUY_SUPPLY_WATER)
    private BigDecimal buySupplyWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_BATCH_SALE_WATER)
    private BigDecimal batchSaleWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_SUPPLY_TOTAL_WATER)
    private BigDecimal supplyTotalWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FEE_METERING_USE_WATER)
    private BigDecimal feeMeteringUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_HUANWEI_USE_WATER)
    private BigDecimal huanweiUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_LVHUA_USE_WATER)
    private BigDecimal lvhuaUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_PIPE_USE_WATER)
    private BigDecimal pipeUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_SUNHUAI_USE_WATER)
    private BigDecimal sunhuaiUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_DINGLIANG_USE_WATER)
    private BigDecimal dingliangUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FEE_NO_METERING_USE_WATER)
    private BigDecimal feeNoMeteringUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_BANGONG_USE_WATER)
    private BigDecimal bangongUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_XIAOFANG_USE_WATER)
    private BigDecimal xiaofangUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_JIANMIAN_USE_WATER)
    private BigDecimal jianmianUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FREE_METERING_USE_WATER)
    private BigDecimal freeMeteringUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FREE_XIAOFANG_USE_WATER)
    private BigDecimal freeXiaofangUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FREE_WEIHU_USE_WATER)
    private BigDecimal freeWeihuUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FREE_BANGONG_USE_WATER)
    private BigDecimal freeBangongUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FREE_CHONGXI_USE_WATER)
    private BigDecimal freeChongxiUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FREE_NO_METERING_USE_WATER)
    private BigDecimal freeNoMeteringUseWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_USE_TOTAL_WATER)
    private BigDecimal useTotalWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FRONT_POINT_LEAK_WATER)
    private BigDecimal frontPointLeakWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FRONT_PIPE_LEAK_WATER)
    private BigDecimal frontPipeLeakWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_FRONT_LEAK_TOTAL_WATER)
    private BigDecimal frontLeakTotalWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_BACKEND_CHECKED_LEAK_WATER)
    private BigDecimal backendCheckedLeakWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_BACKEND_NO_CHECKED_WATER)
    private BigDecimal backendNoCheckedWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_BACKEND_LEAK_TOTAL_WATER)
    private BigDecimal backendLeakTotalWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_BACKGROUND_LEAK_WATER)
    private BigDecimal backgroundLeakWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_SHUIXIANG_LEAK_WATER)
    private BigDecimal shuixiangLeakWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_LEAK_TOTAL_WATER)
    private BigDecimal leakTotalWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_CUST_MISTAKE_LOSS_WATER)
    private BigDecimal custMistakeLossWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_NON_CUST_MISTAKE_LOSS_WATER)
    private BigDecimal nonCustMistakeLossWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_MISTAKE_LOSS_TOTAL_WATER)
    private BigDecimal mistakeLossTotalWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_TOUDAO_LOSS_WATER)
    private BigDecimal toudaoLossWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_COPY_METER_LOSS_WATER)
    private BigDecimal copyMeterLossWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_OTHER_LOSS_WATER)
    private BigDecimal otherLossWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_NO_REGISTER_LOSS_WATER)
    private BigDecimal noRegisterLossWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_PIPE_LOSS_WATER)
    private BigDecimal pipeLossWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_OTHER_LOSS_TOTAL_WATER)
    private BigDecimal otherLossTotalWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_LOSS_TOTAL_WATER)
    private BigDecimal lossTotalWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_DN75_PIPE_LENGTH)
    private BigDecimal dn75PipeLength = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_UNIT_SUPPLY_PIPE_LENGTH)
    private BigDecimal unitSupplyPipeLength = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_YEAR_AVG_PRESSURE)
    private BigDecimal yearAvgPressure = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_CUST_COPIED_WATER)
    private BigDecimal custCopiedWater = BigDecimal.ZERO;

    @TableField(ModelConstants.PIPE_USE_WATER_REPORT_MAX_FROZEN_SOIL_DEPTH)
    private BigDecimal maxFrozenSoilDepth = BigDecimal.ZERO;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.CREATOR)
    private String creator;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;
}
