import{_ as ie}from"./Search-NSrhrIa_.js";import{bF as x,d as pe,M as ce,r as b,j as de,c as D,s as c,S as R,x as w,bC as ue,o as me,g as fe,n as ge,q as r,i as d,F as m,p as E,aj as A,G as C,ca as V,u as ye,J as be,bK as he,I as _e,K as ve,aq as xe,al as $,b7 as z,ak as Fe,bM as O,bq as J,C as ke}from"./index-r0dFAfgr.js";import{_ as De}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as we}from"./CardTable-rdWOL4_6.js";import{_ as Te}from"./CardSearch-CB_HNR-Q.js";import{X}from"./xlsx-rVJkW9yq.js";import{a as qe,e as Le,b as Se,c as We,d as Ie,f as Ue,g as <PERSON>,h as Ne,i as Me,s as Ye}from"./pumpRoomInfo-DV75B9MO.js";import"./index-C9hz-UZb.js";const Re=(f,y,h)=>{/^[5A-Za-z0-9_]+$/.test(y)?h():h(new Error("只能输入英文,数字,下划线字符"))},K=[{label:"箱式",value:"箱式"},{label:"罐式",value:"罐式"},{label:"微型",value:"微型"},{label:"其他",value:"其他"}],Ee=[{type:"input",label:"泵房编码",field:"code",prop:"code",placeholder:"请输入泵房编码",rules:[{required:!0,message:"请输入泵房编码",trigger:"blur"},{validator:Re,trigger:"blur"}]},{type:"input",label:"泵房名称",field:"name",prop:"name",placeholder:"请输入泵房名称",rules:[{required:!0,message:"请输入泵房名称",trigger:"blur"}]},{type:"input",label:"泵房简称",field:"nickname",prop:"nickname",placeholder:"请输入泵房简称",rules:[{required:!0,message:"请输入泵房简称",trigger:"blur"}]},{type:"input",label:"厂家名称",field:"companyName",prop:"companyName",placeholder:"请输入厂家名称",rules:[{required:!0,message:"请输入厂家名称",trigger:"blur"}]}],Ae=[{type:"select",label:"供水类型",field:"supplyMethod",prop:"supplyMethod",placeholder:"请选择供水类型",options:K,rules:[{required:!0,message:"请选择供水类型",trigger:"blur"}]},{type:"input-number",label:"水箱个数",field:"waterBoxNum",prop:"supplyMethod",placeholder:"请输入水箱个数",rules:[{required:!0,message:"请输入水箱个数",trigger:"blur"}]},{type:"textarea",label:"地址",field:"address",prop:"address",placeholder:"请输入地址",rules:[{required:!0,message:"请输入地址",trigger:"blur"}]},{type:"date",label:"安装日期",field:"installDate",prop:"installDate",rules:[{required:!0,message:"请输入录入日期",trigger:"blur"}]},{type:"input",label:"安装人",field:"installUserName",prop:"installUserName",placeholder:"请输入安装人",rules:[{required:!0,message:"请输入安装人",trigger:"blur"}]},{type:"input-number",label:"采集频率(分钟)",labelWidth:"150px",field:"collectionFrequency",prop:"collectionFrequency",placeholder:"请输入采集频率(分钟)",rules:[{required:!0,message:"请输入采集频率(分钟)",trigger:"blur"}]},{type:"input-number",label:"存储频率(分钟)",labelWidth:"120px",field:"storageFrequency",prop:"storageFrequency",placeholder:"请输入存储频率(分钟)",rules:[{required:!0,message:"请输入存储频率(分钟)",trigger:"blur"}]}],Oe=[{type:"select",label:"供水类型",field:"supplyMethod",placeholder:"请选择泵房编码",options:K},{type:"input-number",label:"水箱个数",field:"waterBoxNum",placeholder:"请输入水箱个数"},{type:"input",label:"安装人",field:"installUserName",placeholder:"请输入安装人"},{type:"daterange",label:"安装日期",field:"installDateFrom"},{type:"daterange",label:"录入日期",field:"fromTime",format:"YYYY-MM-DD"},{type:"input-number",label:"采集频率（分钟）",labelWidth:"150px",field:"collectionFrequency",placeholder:"请输入采集频率(分钟)"},{type:"input-number",label:"存储频率（分钟）",labelWidth:"120px",field:"storageFrequency",placeholder:"请输入存储频率(分钟)"}],H=()=>{let f=Ee;return f=f.concat(Ae),f.push({type:"textarea",label:"备注",field:"remark",prop:"remark",placeholder:"请输入备注"}),f},He=[{label:"泵房编码",prop:"code",minWidth:120},{label:"泵房名称",prop:"name",minWidth:120},{label:"泵房简称",prop:"nickname",minWidth:120},{label:"厂家名称",prop:"companyName",minWidth:120},{label:"供水类型",prop:"supplyMethod",minWidth:120},{label:"水箱个数",prop:"waterBoxNum",minWidth:120},{label:"地址",prop:"address",minWidth:120},{label:"安装人",prop:"installUserName",minWidth:120},{label:"安装日期",prop:"installDate",minWidth:120,formatter:(f,y)=>x(y).format("YYYY-MM-DD")},{label:"录入日期",prop:"createTime",minWidth:120,formatter:(f,y)=>x(y).format("YYYY-MM-DD HH:mm:ss")},{label:"采集频率（分钟）",prop:"collectionFrequency",minWidth:140},{label:"存储频率（分钟）",prop:"storageFrequency",minWidth:140},{label:"备注",prop:"remark",minWidth:120}],G={泵房编码:"code",泵房名称:"name",泵房简称:"nickname",厂家名称:"companyName",水箱个数:"waterBoxNum",供水类型:"supplyMethod",地址:"address",安装日期:"installDate",安装人:"installUserName","采集频率（分钟）":"collectionFrequency","存储频率（分钟）":"storageFrequency",备注:"remark"},Be=f=>new Promise(y=>{const h=new FileReader;h.onload=l=>{const _=new Uint8Array(l.target.result),W=X.read(_,{type:"array"}),T=W.Sheets[W.SheetNames[0]],q=X.utils.sheet_to_json(T);y(q)},h.readAsArrayBuffer(f.raw)}),Pe={class:"wrapper"},je={class:"buttons"},Ve={class:"btns"},$e=pe({__name:"index",setup(f){const{$messageSuccess:y,$messageError:h}=ce(),l=b({dataList:[],fileType:"",fileActionUrl:de().actionUrl+"/file/api/upload/file",fileList:[],rowId:""}),_=D(),W=D(),T=D(),q=D(),I=D(),N=D(),B=D(),Z=b({filters:[{type:"input",label:"泵房编码",field:"code",placeholder:"请输入泵房编码"},{type:"input",label:"泵房名称",field:"name",placeholder:"请输入泵房名称"},{type:"input",label:"泵房简称",field:"nickname",placeholder:"请输入泵房简称"},...Oe],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:c($),click:()=>F()},{perm:!0,text:"重置",type:"default",svgIcon:c(z),click:()=>{var t;(t=I.value)==null||t.resetForm()}},{perm:!0,text:"添加",type:"success",svgIcon:c(Fe),click:()=>{L.title="新增",j()}},{perm:!0,text:"导入",type:"warning",svgIcon:c(V),click:()=>{var t;Y(),(t=T.value)==null||t.openDialog()}},{perm:!0,text:"导出",type:"danger",svgIcon:c(A),click:()=>ee()}]}]}),v=b({indexVisible:!0,columns:He.concat([{minWidth:120,label:"施工图纸",prop:"constructionFile",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",type:"default",svgIcon:c(O),click:t=>{var e;console.log(t.id),l.fileType="constructionFile",(e=q.value)==null||e.openDialog(),S(l.fileType,t.id)}}]}},{minWidth:120,label:"其他文件",prop:"otherFile",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",type:"default",svgIcon:c(O),click:t=>{var e;l.fileType="otherFile",(e=q.value)==null||e.openDialog(),S(l.fileType,t.id)}}]}}]),operations:[{perm:!0,text:"修改",svgIcon:c(O),click:t=>{L.title="修改",j(t)}},{perm:!0,text:"删除",type:"danger",svgIcon:c(J),click:t=>re(t)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{v.pagination.page=t,v.pagination.limit=e,F()}}}),L=b({dialogWidth:600,title:"新增",labelWidth:120,group:[{fields:H()}],submit:t=>{R("确定提交？","提示信息").then(()=>{L.title==="新增"?qe(t).then(()=>{var e,a,s;(a=(e=_.value)==null?void 0:e.refForm)==null||a.resetForm(),(s=_.value)==null||s.closeDialog(),w.success("提交成功"),F()}):Le(t).then(()=>{var e,a,s;(a=(e=_.value)==null?void 0:e.refForm)==null||a.resetForm(),(s=_.value)==null||s.closeDialog(),w.success("修改成功"),F()})}).catch(()=>{})}}),Q=b({title:"导入",dialogWidth:1200,group:[],cancel:!0,btns:[{perm:!0,text:"确定导入",click:()=>{l.dataList.length>0?(console.log(l.dataList),Se(l.dataList).then(t=>{var e,a;console.log(t.data),((e=t.data)==null?void 0:e.code)===200?(Y(),(a=T.value)==null||a.closeDialog(),w.success("提交成功"),F()):w.error("提交失败")}).catch(t=>{console.log(t),w.error("提交失败")})):w.warning("请导入正确的xlsx文件！")}}]}),ee=async()=>{var u;const t=((u=I.value)==null?void 0:u.queryParams)||{},[e,a]=t.installDateFrom,[s,o]=t.fromTime||[],i={...t,page:v.pagination.page||1,size:-1,installDateFrom:e?x(e).startOf("day").valueOf():null,installDateTo:a?x(a).startOf("day").valueOf():null,fromTime:s,toTime:o},n=await We(i),g=window.URL.createObjectURL(n.data);console.log(g);const p=document.createElement("a");p.style.display="none",p.href=g,p.setAttribute("download","泵房台账列表.xlsx"),document.body.appendChild(p),p.click()},te=async()=>{const t=await Me(),e=window.URL.createObjectURL(t.data),a=document.createElement("a");a.style.display="none",a.href=e,a.setAttribute("download","泵房台账模板.xlsx"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(a.href)},M=b({indexVisible:!0,columns:H(),dataList:[],pagination:{hide:!0}}),ae=b({dialogWidth:1050,title:"上传文件",labelWidth:120,group:[{fields:H()}]}),P=b({indexVisible:!0,columns:[{label:"文件",prop:"fileName",minWidth:120},{label:"上传时间",prop:"uploadTime",minWidth:120,formatter:(t,e)=>e?x(e).format("YYYY-MM-DD HH:mm:ss"):""}],dataList:[],operations:[{perm:!0,text:"下载",type:"primary",svgIcon:c(A),click:t=>{ue(t.fileAddress,t.fileName)}},{perm:!0,text:"删除",type:"danger",svgIcon:c(J),click:t=>{R("确定删除该附件, 是否继续?","删除提示").then(()=>{Ie(t.id).then(e=>{var a;((a=e.data)==null?void 0:a.code)===200?y("删除成功"):h("删除失败"),S(l.fileType,l.rowId)}).catch(e=>{h(e)})})}}],pagination:{hide:!0}}),le=b({defaultParams:{time:[]},filters:[{type:"input",label:"文件名",field:"fileName",placeholder:"请输入文件名"},{type:"daterange",label:"上传日期",field:"fromTime",format:"YYYY-MM-DD"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:c($),click:()=>S(l.fileType,l.rowId)},{perm:!0,text:"重置",type:"default",svgIcon:c(z),click:()=>{var t;(t=N.value)==null||t.resetForm()}}]}]}),Y=()=>{l.dataList=[],M.dataList=[]},oe=t=>{l.dataList=[],Be(t).then(e=>{e&&e.forEach(o=>{const i={};for(const n in o){if(typeof G[n]>"u"){w.info("请检查第一行表头是否为: 泵房编码/泵房名称/泵房简称/厂家名称/供水类型/水箱个数/地址/安装人/安装日期/采集频率/储存频率; 且每行均有对应数据!");return}i[G[n]]=o[n]}l.dataList.push(i)});const a=l.dataList.map(o=>JSON.stringify(o)),s=[...new Set(a)];M.dataList=s.map(o=>JSON.parse(o))})},j=t=>{var e;L.defaultValue=t?{...t,installDate:x(t.installDate).format()}:{},(e=_.value)==null||e.openDialog()},re=t=>{R("确定删除?","提示信息").then(()=>{Ue(t.id).then(()=>{F()})}).catch(()=>{})},se=async(t,e)=>{const a={fileName:e.name,fileAddress:e.response,label:l.fileType,host:l.rowId};Ye(a).then(()=>{S(l.fileType,l.rowId)})},S=async(t,e)=>{var g,p,u,U,k;const a=((g=N.value)==null?void 0:g.queryParams)||{fromTime:[]},[s,o]=a.fromTime||[],i={...a,host:e,label:t,fromTime:s,toTime:o,page:1,size:99999};l.rowId=e;const n=await Ce(i);console.log((u=(p=n.data)==null?void 0:p.data)==null?void 0:u.data),P.dataList=(k=(U=n.data)==null?void 0:U.data)==null?void 0:k.data},F=async()=>{var g,p,u;const t=((g=I.value)==null?void 0:g.queryParams)||{},[e,a]=t.installDateFrom,[s,o]=t.fromTime,i={...t,page:v.pagination.page||1,size:v.pagination.limit||20,installDateFrom:e?x(e).startOf("day").valueOf():null,installDateTo:a?x(a).startOf("day").valueOf():null,fromTime:s,toTime:o},n=await Ne(i);v.pagination.total=(p=n.data)==null?void 0:p.data.total,v.dataList=(u=n.data)==null?void 0:u.data.data};return me(async()=>{await F()}),(t,e)=>{const a=Te,s=we,o=De,i=be,n=he,g=_e,p=ve,u=xe,U=ie;return fe(),ge("div",Pe,[r(a,{ref_key:"refSearch",ref:I,config:d(Z)},null,8,["config"]),r(s,{ref_key:"refTable",ref:W,config:d(v),class:"card-table"},null,8,["config"]),r(o,{ref_key:"refForm",ref:_,config:d(L)},null,8,["config"]),r(o,{ref_key:"refUploadDialog",ref:T,config:d(Q),class:"upload-dialog"},{default:m(()=>[r(p,{ref:"formRef"},{default:m(()=>[r(g,null,{default:m(()=>[E("div",je,[r(i,{type:"primary",icon:d(A),plain:!0,onClick:te},{default:m(()=>e[1]||(e[1]=[C(" 下载模板 ")])),_:1},8,["icon"]),r(n,{ref:"upload",action:"action",accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","show-file-list":!0,"on-remove":Y,limit:1,"auto-upload":!1,"on-change":oe,class:"upload-demo"},{tip:m(()=>e[3]||(e[3]=[E("div",{class:"el-upload__tip"}," 只能导入xlsx文件, 请确保导入的文件单元格格式为文本! ",-1)])),default:m(()=>[r(i,{type:"primary",icon:d(V)},{default:m(()=>e[2]||(e[2]=[C(" 添加文件 ")])),_:1},8,["icon"])]),_:1},512)])]),_:1})]),_:1},512),r(u,{config:d(M)},null,8,["config"])]),_:1},8,["config"]),r(o,{ref_key:"refFileUpload",ref:q,config:d(ae)},{default:m(()=>[r(U,{ref_key:"uploadSearch",ref:N,config:d(le)},null,8,["config"]),E("div",Ve,[r(n,{ref_key:"uploadRef",ref:B,action:d(l).fileActionUrl,"show-file-list":!0,accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","auto-upload":!1,multiple:"",headers:{"X-Authorization":"Bearer "+d(ye)().token},"on-success":(k,ne)=>se(k,ne),class:"upload-demo"},{trigger:m(()=>[r(i,{type:"primary"},{default:m(()=>e[4]||(e[4]=[C(" 读取文件 ")])),_:1})]),_:1},8,["action","headers","on-success"]),r(i,{type:"success",onClick:e[0]||(e[0]=()=>{var k;return(k=d(B))==null?void 0:k.submit()})},{default:m(()=>e[5]||(e[5]=[C(" 上传 ")])),_:1})]),r(u,{config:d(P),class:"form-table"},null,8,["config"])]),_:1},8,["config"])])}}}),tt=ke($e,[["__scopeId","data-v-fa18ba62"]]);export{tt as default};
