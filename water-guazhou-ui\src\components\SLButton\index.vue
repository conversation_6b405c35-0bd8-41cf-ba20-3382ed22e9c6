<template>
  <el-button
    v-if="config.perm && (typeof config.perm === 'boolean' ? config.perm : config.perm(row))"
    :loading="
      config.loading && (typeof config.loading === 'boolean' ? config.loading : config.loading(row))
    "
    :size="config.size || 'default'"
    :text="config.isTextBtn"
    :color="typeof config.bgColor === 'function' ? config.bgColor(row) : config.bgColor"
    :style="{
      color:
        (config.color && (typeof config.color === 'string' ? config.color : config.color(row))) ||
        getButtonColor()
    }"
    :type="typeof config.type === 'function' ? config.type(row) : config.type || 'primary'"
    :disabled="
      config.disabled &&
        (typeof config.disabled === 'boolean' ? config.disabled : config.disabled(row))
    "
    @click="config.click && config.click(row)"
  >
    <i
      v-if="config.icon"
      :class="config.icon && (typeof config.icon === 'string' ? config.icon : config.icon(row))"
    ></i>
    {{ config.text && (typeof config.text === 'string' ? config.text : config.text(row)) }}
  </el-button>
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { useAppStore } from '@/store'
import { ISLOperation } from '../SLCardSearch/type'

export default defineComponent({
  name: 'SLButton',
  props: {
    row: {
      type: Object,
      default: () => {
        //
      }
    },
    config: {
      type: Object as PropType<ISLOperation>,
      default: () => {
        //
      }
    }
  },
  setup() {
    const getButtonColor = () => {
      return useAppStore().isDark ? '#fff' : '#333'
    }
    return {
      getButtonColor
    }
  }
})
</script>
<style scoped lang="scss"></style>
