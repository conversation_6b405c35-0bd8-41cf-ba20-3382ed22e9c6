<template>
  <div>
    <!-- <video
      ref="videoPlayer"
      class="video-js vjs-default-skin vjs-big-play-centered"
      controls
      preload="auto"
      style="width: 100%; height: auto"
      poster="无信息"
    >
      <source
        :src="src"
        type="application/x-mpegURL"
      />
    </video> -->
  </div>
</template>

<script>
// import 'videojs-contrib-hls'

export default {
  name: 'VideoPlayer',
  props: {
    src: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'm3u8'
    }
  },
  data() {
    return {
      player: null
    };
  }
  // mounted() {
  //   this.getVideo()
  //   this.player.play()
  // },
  // beforeUnmount() {
  //   if (this.player) {
  //     this.player.dispose()
  //   }
  // },
  // methods: {
  //   getVideo() {
  //     // this.player = videojs(this.$refs.videoPlayer)
  //   },
  //   play() {
  //     this.player.ready(function () {
  //       this.play()
  //     })
  //   }
  // }
};
</script>
