import{_ as H}from"./index-C9hz-UZb.js";import{d as M,j as G,M as U,a6 as J,bF as o,r as _,c as v,am as W,a8 as K,s as k,bB as Q,o as $,bu as X,ay as Y,g as Z,n as ee,q as f,i as m,F as b,cs as I,bo as N,bR as A,p as P,dF as te,dA as ae,aq as re,al as ne,b7 as le,aj as oe,C as se}from"./index-r0dFAfgr.js";import{_ as ie}from"./CardSearch-CB_HNR-Q.js";import{l as ce}from"./echart-DP2Lcm9i.js";import{e as de,a as ue}from"./queryStatistics-CQ9DBM08.js";import{u as pe}from"./useStation-DJgnSZIA.js";import{f as me}from"./formartColumn-D5r7JJ2G.js";import{b as fe}from"./zhandian-YaGuQZe6.js";import"./Search-NSrhrIa_.js";const ye={class:"wrapper"},he=M({__name:"index",setup(be){const g=G(),{$messageWarning:C}=U(),j=J(),{getStationTree:O,getStationTreeByDisabledType:w}=pe(),L=o().date(),s=_({type:"date",chartOption:null,activeName:"echarts",data:null,checkedKeys:[],stationTree:[]}),V=[{label:"1 m",value:"1m"},{label:"5 m",value:"5m"},{label:"10 m",value:"10m"},{label:"15 m",value:"15m"},{label:"1小时",value:"hour"}],x=v(),T=v(),y=v(),B=v();let h=_([]);W(()=>s.activeName,()=>{s.activeName==="echarts"&&q()});const S=_({defaultParams:{queryType:"15m",type:"day",year:[o().format(),o().format()],month:[o().format(),o().format()],day:[o().startOf("day").format(),o().format()]},filters:[{type:"select-tree",label:"监测点:",defaultExpandAll:!0,multiple:!0,field:"attributeId",clearable:!1,showCheckbox:!0,lazy:!0,options:K(()=>s.stationTree),lazyLoad:(e,t)=>{var a,n;if(e.level===0)return t([]);if((a=e.data.children)!=null&&a.length)return t(e.data.children);if(e.isLeaf)return t([]);if((n=e.data)!=null&&n.isLeaf)return t([]);fe({stationId:e.data.id}).then(i=>{var c;const l=(c=i.data)==null?void 0:c.map(p=>({label:p.type,value:"",id:"",children:p.attrList.map(r=>({label:r.name,value:r.id,id:r.id,isLeaf:!0}))}));return t(l)})}},{type:"radio-button",field:"type",options:[{label:"日",value:"day"},{label:"月",value:"month"},{label:"年",value:"year"}],label:"时间频率"},{type:"datetimerange",label:"选择日期",field:"day",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择日期",field:"month",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择日期",field:"year",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="day"}},{type:"select",label:"时间间隔:",field:"queryType",clearable:!1,options:V,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"},itemContainerStyle:{width:"180px"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var t;u.pagination.page=1;const e=((t=y.value)==null?void 0:t.queryParams)||{};e.attributeId&&e.attributeId.length>0?R():C("选择监测点")},svgIcon:k(ne)},{type:"default",perm:!0,text:"重置",svgIcon:k(le),click:()=>{var e;S.defaultParams={...S.defaultParams,date:[o().date(L-2),o().date(L)]},(e=y.value)==null||e.resetForm()}},{text:"导出",perm:!0,type:"warning",svgIcon:k(oe),hide:()=>s.activeName!=="list",click:()=>E()}]}]}),u=_({loading:!1,dataList:[],columns:[],operations:[],pagination:{layout:"total, prev, pager, next, jumper",refreshData:({page:e,size:t})=>{u.dataList=h==null?void 0:h.slice((e-1)*t,e*t),u.pagination.page=e,u.pagination.limit=t}}}),E=()=>{var e;if(u.dataList.length>0){const t=((e=y.value)==null?void 0:e.queryParams)||{};console.log(t);const[a,n]=t[t.type]||[];let i=0,l=0;t.type==="day"?(i=a?o(a).valueOf():"",l=n?o(n).valueOf():""):(i=a?o(a).startOf(t.type).valueOf():"",l=n?o(n).endOf(t.type).valueOf():"");const c={attributes:t.attributeId.join(","),queryType:t.type==="month"?"day":t.type==="year"?"month":t.queryType,start:i,end:l};de(c).then(p=>{const r=window.URL.createObjectURL(p.data),d=document.createElement("a");d.style.display="none",d.href=r,d.setAttribute("download","数据对比表.xlsx"),document.body.appendChild(d),d.click()})}else C("无数据导出")},R=()=>{var c;u.loading=!0;const e=((c=y.value)==null?void 0:c.queryParams)||{};console.log(e);let t=0,a=0;const[n,i]=e[e.type]||[];e.type==="day"?(t=n?o(n).valueOf():"",a=i?o(i).valueOf():""):(t=n?o(n).startOf(e.type).valueOf():"",a=i?o(i).endOf(e.type).valueOf():"");const l={attributes:e.attributeId.join(","),queryType:e.type==="month"?"day":e.type==="year"?"month":e.queryType,start:t,end:a};ue(l).then(p=>{var d;const r=(d=p.data)==null?void 0:d.data;s.data=r,h=r==null?void 0:r.tableDataList,u.columns=me(r==null?void 0:r.tableInfo),u.dataList=h.slice(0,20),u.pagination.total=r==null?void 0:r.tableDataList.length,u.loading=!1,q()})},z=()=>{var e;(e=x.value)==null||e.resize()},q=()=>{var a,n,i;const e=ce();e.series=[];const t={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:g.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:g.isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.xAxis.data=(a=s.data)==null?void 0:a.tableDataList.map(l=>l.ts),(n=s.data)==null||n.tableInfo.map((l,c)=>{var p;if(l.columnValue!=="ts"){const r=JSON.parse(JSON.stringify(t));r.name=l.columnName,r.data=(p=s.data)==null?void 0:p.tableDataList.map(D=>D[l.columnValue]);const d=l.columnName.split("--")[2]+(l.unit?"("+l.unit+")":"");c===1?e.yAxis[0].name=d:c>1&&(e.yAxis.find(F=>F.name===d)||(r.yAxisIndex=c-1,e.grid.right=70*(c-1),e.yAxis.push({position:"right",alignTicks:!0,type:"value",name:d,offset:70*(c-2),axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{color:"#656b84"}},splitLine:{lineStyle:{color:g.isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}}))),e.series.push(r)}}),(i=x.value)==null||i.clear(),Q(()=>{T.value&&j.listenTo(T.value,()=>{s.chartOption=e,z()})})};return $(async()=>{const e=await O("水源地");w(e,["Project","Station"],!0,"Station"),console.log("newTreeData",e)}),X(async()=>{const e=await O("水源地");await w(e,["Project","Station"],!1,"Station"),s.stationTree=e,console.log(" state.stationTree ",s.stationTree)}),(e,t)=>{const a=ie,n=te,i=ae,l=Y("VChart"),c=re,p=H;return Z(),ee("div",ye,[f(a,{ref_key:"cardSearch",ref:y,config:m(S)},null,8,["config"]),f(p,{class:"card",title:" "},{right:b(()=>[f(i,{modelValue:m(s).activeName,"onUpdate:modelValue":t[0]||(t[0]=r=>m(s).activeName=r)},{default:b(()=>[f(n,{label:"echarts"},{default:b(()=>[f(m(I),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),f(n,{label:"list"},{default:b(()=>[f(m(I),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:b(()=>[N(P("div",{ref_key:"agriEcoDev",ref:T,class:"chart-box"},[f(l,{ref_key:"refChart",ref:x,theme:m(g).isDark?"dark":"light",option:m(s).chartOption},null,8,["theme","option"])],512),[[A,m(s).activeName==="echarts"]]),N(P("div",null,[f(c,{ref_key:"refCardTable",ref:B,class:"card-table",config:m(u)},null,8,["config"])],512),[[A,m(s).activeName==="list"]])]),_:1})])}}}),we=se(he,[["__scopeId","data-v-da040b6f"]]);export{we as default};
