package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.auditRecord.AuditRecordService;
import org.thingsboard.server.dao.auditRecord.BO.AuditRecordBO;
import org.thingsboard.server.dao.model.sql.AuditRecordEntity;

import java.util.Map;

/**
 * 审计记录
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-01-19
 */
@RestController
@RequestMapping("api/auditRecord")
public class AuditRecordController extends BaseController {

    @Autowired
    private AuditRecordService auditRecordService;

    /**
     * 新增审计记录
     *
     * @param auditRecordEntity
     * @return
     */
    @PostMapping("save")
    public AuditRecordEntity save(@RequestBody AuditRecordEntity auditRecordEntity) throws ThingsboardException {
        auditRecordEntity.setCreatedTime(System.currentTimeMillis());
        auditRecordEntity.setCreated(getCurrentUser().getFirstName() + " (" + getCurrentUser().getEmail() + ")");
        return auditRecordService.save(auditRecordEntity);
    }

    /**
     * 按id查找审计记录
     *
     * @param id
     * @return
     */
    @GetMapping("{id}")
    public AuditRecordEntity getById(@PathVariable String id) {
        return auditRecordService.findById(id);
    }

    /**
     * 获取所有审计记录
     *
     * @return
     */
    @GetMapping("list")
    public Map<String, Object> getList(AuditRecordBO recordBO) {
        return auditRecordService.getList(recordBO);
    }

    /**
     * 删除审计记录
     *
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public Object deleteById(@PathVariable String id) {
        JSONObject result = new JSONObject();
        try {
            auditRecordService.deleteById(id);
            result.put("result", "删除成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.put("result", "删除失败");
        }

        return result;
    }

}
