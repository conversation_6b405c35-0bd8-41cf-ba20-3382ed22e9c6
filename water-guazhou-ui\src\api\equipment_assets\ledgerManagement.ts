import { request } from '@/plugins/axios';

// 设备台账
// 分页条件查询设备台账（废弃）
export const getDeviceStorageJournalSerch = (params?: {
  page?: number | undefined;
  size?: number | undefined;
  fromTime?: string;
  toTime?: string;
  serialId?: string;
  deviceLabelCode?: string;
  supplierId?: string;
  scrappedTime?: string;
  storehouseId?: string;
  shelvesId?: string;
  lastMaintainanceTime?: string;
  lastInspectionTime?: string;
}) =>
  request({
    url: `/api/deviceStorageJournal`,
    method: 'get',
    params
  });

// 导出设备台账
export const exportExcel = (params?: {
  page?: number | undefined;
  size?: number | undefined;
  fromTime?: string;
  toTime?: string;
  serialId?: string;
  deviceLabelCode?: string;
  supplierId?: string;
  scrappedTime?: string;
  storehouseId?: string;
  shelvesId?: string;
  lastMaintainanceTime?: string;
  lastInspectionTime?: string;
}) =>
  request({
    url: `/api/deviceStorageJournal/exportExcel`,
    method: 'get',
    responseType: 'blob',
    params
  });

// 分页条件查询余量设备台账(二维数组)
export const getDeviceStorageJournalEq = (params?: {
  page?: number | undefined;
  size?: number | undefined;
  serialId?: string;
  name?: string;
  model?: string;
  inStoreOnly?: boolean;
  shelvesId?: string;
  deviceTypeId?: string;
}) =>
  request({
    url: `/api/deviceStorageJournal/rest`,
    method: 'get',
    params
  });

// 分页条件查询余量设备台账(一维数组)
export const getDeviceStorageJournalAll = (params?: {
  page?: number | undefined;
  size?: number | undefined;
  serialId?: string;
  name?: string;
  model?: string;
  shelvesId?: string;
  storehouseId?: string;
  deviceTypeId?: string;
}) =>
  request({
    url: `/api/deviceStorageJournal/restWithoutSplit`,
    method: 'get',
    params
  });

// 设备标签
// 添加安装信息
export const postDeviceSettleJournal = (params?: {
  deviceLabelCode?: string;
  installUserId?: string;
  projectId?: string;
  installTime?: string;
  installAddressId?: string;
  address?: string;
  remark?: string;
}) =>
  request({
    url: `/api/deviceSettleJournal`,
    method: 'post',
    data: params
  });

// 获取安装信息
export const getDeviceSettleJournal = (params?: {
  deviceLabelCode?: string;
  departmentId?: string;
  userId?: string;
  receiveTime?: string;
  installAddressId?: string;
  remark?: string;
}) =>
  request({
    url: `/api/deviceSettleJournal`,
    method: 'get',
    params
  });

// 添加使用信息
export const postDeviceUsageJournal = (params?: {
  deviceLabelCode?: string;
  departmentId?: string;
  userId?: string;
  receiveTime?: string;
  remark?: string;
}) =>
  request({
    url: `/api/deviceUsageJournal`,
    method: 'post',
    data: params
  });

// 获取使用信息
export const getDeviceUsageJournal = (params?: {
  deviceLabelCode?: string;
  departmentId?: string;
  userId?: string;
  receiveTime?: string;
  remark?: string;
}) =>
  request({
    url: `/api/deviceUsageJournal`,
    method: 'get',
    params
  });

// 导出设备标签
export const exportDeviceLabelExcel = (params?: {
  page?: number | undefined;
  size?: number | undefined;
  fromTime?: string;
  toTime?: string;
  serialId?: string;
  deviceLabelCode?: string;
  supplierId?: string;
  scrappedTime?: string;
  storehouseId?: string;
  shelvesId?: string;
  lastMaintainanceTime?: string;
  lastInspectionTime?: string;
}) =>
  request({
    url: `/api/deviceStorageJournal/exportDeviceLabelExcel`,
    method: 'get',
    responseType: 'blob',
    params
  });

// 获取台账设备基础信息
export const getBasicInformation = (id: string) =>
  request({
    url: `/api/deviceStorageJournal/${id}/detail`,
    method: 'get'
  });

// 获取台账维修信息
export const getRepair = (deviceLabelCode: string) =>
  request({
    url: `/api/fault/report/statistics/${deviceLabelCode}`,
    method: 'get'
  });

// 获取台账维修记录
export const getRepairList = (deviceLabelCode: string) =>
  request({
    url: `/api/fault/report/list/${deviceLabelCode}`,
    method: 'get',
    params: {
      page: '1',
      size: '99999'
    }
  });

// 获取台账保养信息
export const getMaintainance = (deviceLabelCode: string) =>
  request({
    url: `/api/maintain/task/c/statistics/${deviceLabelCode}`,
    method: 'get'
  });

// 获取台账保养记录
export const getMaintainanceList = (deviceLabelCode: string) =>
  request({
    url: `/api/maintain/plan/m/list/${deviceLabelCode}`,
    method: 'get',
    params: {
      page: '1',
      size: '99999'
    }
  });

// 获取台账巡检信息
export const getCircuit = (deviceLabelCode: string) =>
  request({
    url: `/api/circuit/task/c/statistics/${deviceLabelCode}`,
    method: 'get'
  });

// 获取台账巡检记录
export const getCircuitList = (deviceLabelCode: string) =>
  request({
    url: `/api/circuit/plan/m/list/${deviceLabelCode}`,
    method: 'get',
    params: {
      page: '1',
      size: '99999'
    }
  });
