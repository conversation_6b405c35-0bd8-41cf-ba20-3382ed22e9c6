import{D as J}from"./DrawerBox-CLde5xC8.js";import{_ as V}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{d as q,c as w,r as C,b as p,S as U,bE as x,W as $,u as z,l as H,D as Q,o as j,g as K,h as X,F as _,q as g,i as y,p as k,bh as Z,_ as tt,aq as et,C as rt}from"./index-r0dFAfgr.js";import{_ as ot}from"./ArcLayout-CHnHL9Pv.js";import{D as it,G as at}from"./errorReport-D3BFqMSq.js";import{g as nt}from"./MapView-DaoQedLH.js";import{s as E,g as st}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as pt}from"./LayerHelper-Cn-iiqxI.js";import{a as mt,i as lt}from"./QueryHelper-ILO3qZqg.js";import{GetAllFieldConfig as ct}from"./fieldconfig-Bk3o1wi7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{q as ft,c as dt}from"./geoserverUtils-wjOSMa7E.js";import{t as ut,r as gt,E as yt}from"./ErrorPopTable-Cv-lIqKT.js";import{f as N}from"./DateFormatter-Bm9a68Ax.js";import"./SideDrawer-CBntChyn.js";import"./ArcView-DpMnCY82.js";import"./project-DUuzYgGl.js";import"./ViewHelper-BGCZjxXH.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./pipe-nogVzCHG.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./WMSLayer-mTaW758E.js";import"./widget-BcWKanF2.js";import"./scaleUtils-DgkF6NQH.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./imageBitmapUtils-Db1drMDc.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";const ht={class:"attr-table-box"},_t=q({__name:"SearchErrorReport",setup(bt){const S=w({hideFooter:!0,row:void 0,dataList:[],title:""}),v=w(),D=w(),b=w(),I=C({fieldConfig:[]}),c={identifyResults:[]},L=C({labelWidth:70,labelPosition:"right",group:[{fields:[{type:"radio-button",field:"createuser",options:[{label:"全部",value:""},{label:"我上报的",value:"myself"}],onChange:()=>h()},{type:"daterange",field:"reportTime",label:"上报时间"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>h(),iconifyIcon:"ep:search"},{perm:!0,text:"重置",type:"default",click:()=>W(),iconifyIcon:"ep:refresh"},{perm:!0,text:"导出",type:"default",click:()=>M(),iconifyIcon:"ep:download"},{perm:t=>t.createuser==="myself",text:"删除",type:"danger",click:()=>B(),iconifyIcon:"ep:delete"}]}]}],defaultValue:{reportTime:[],createuser:""}}),B=()=>{var e;const t=(e=n.selectList)==null?void 0:e.map(s=>s.id);if(!(t!=null&&t.length)){p.warning("请选择要删除的数据");return}U("确定删除？","提示信息").then(async()=>{try{(await it(t)).data.code===200?(p.success("删除成功"),h()):p.error("删除失败")}catch{p.error("删除失败")}}).catch(()=>{})},n=C({dataList:[],columns:[{minWidth:120,label:"设备类别",prop:"layer"},{minWidth:120,label:"上报人",prop:"uploadUserName"},{minWidth:120,label:"上报时间",prop:"uploadTime",formatter(t,e){return N(e,x)}},{width:120,label:"上报内容",prop:"uploadContent",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",isTextBtn:!0,click:t=>R(t)}]}},{minWidth:120,label:"审批人",prop:"approvalUserName"},{minWidth:120,label:"审批时间",prop:"approvalTime",formatter(t,e){return N(e,x)}},{width:120,label:"状态",prop:"status",tag:!0,align:"center",tagColor:t=>ut[t.status],formatter(t,e){return gt[e]||"待处理"}},{minWidth:120,label:"备注",prop:"remark"}],pagination:{refreshData:({page:t,size:e})=>{n.pagination.page=t,n.pagination.limit=e,h()}},handleSelectChange(t){n.selectList=t||[]}}),R=async t=>{var e,s,m,l,f,d;if(!t.fid){p.warning("缺少设备ID信息");return}try{const u=t.uploadContent?JSON.parse(t.uploadContent):[];let o=null;if(window.GIS_SERVER_SWITCH)try{const i=await ft({typeName:t.layer,id:t.fid,workspace:"guazhou",outputSRS:"EPSG:3857"});if(i.success&&i.feature){const a=i.feature,r=dt(a.geometry);o=new nt({geometry:r,attributes:a.properties||{}}),o.symbol=E(r.type)}else{p.warning("未找到设备信息");return}}catch(i){console.error("GeoServer查询失败:",i),p.error("查询设备信息失败");return}else{const i=(e=$().gLayerInfos)==null?void 0:e.find(T=>T.layername===t.layer),a=i==null?void 0:i.layerid;if(!i){p.warning("未找到图层信息");return}const r=await mt(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+a,lt({where:"OBJECTID = "+t.fid,outFields:["*"]}));if(r.features&&r.features.length>0)o=r.features[0],o.symbol=E(o.geometry.type);else{p.warning("未找到设备信息");return}}if(o){(s=c.graphicsLayer)==null||s.removeAll(),(m=c.graphicsLayer)==null||m.add(o),await st(c.view,o);const i=((l=I.fieldConfig.find(r=>r.layername===t.layer))==null?void 0:l.fields)||[],a=u.filter(r=>r.name!=="img").map(r=>({...i.find(A=>{var G;return A.name.toLocaleLowerCase()===((G=r.name)==null?void 0:G.toLocaleLowerCase())})||{},oldvalue:r.oldvalue,newvalue:r.newvalue}));c.identifyResults=[{feature:o,layerName:t.layer,layerId:t.fid}],S.value={hideFooter:!0,row:t,img:(f=u.find(r=>r.name==="img"))==null?void 0:f.newvalue,dataList:a,title:t.layer+"("+(o.attributes.SID||o.attributes.OBJECTID||t.fid)+")"},(d=v.value)==null||d.toggleDrawer("ltr",!0)}}catch(u){console.error("处理错误上报详情失败:",u),p.error("数据错误")}},F=z(),h=()=>{var l,f,d,u,o;const{createuser:t,reportTime:e}=((l=b.value)==null?void 0:l.dataForm)||{},s=e==null?void 0:e[0];let m=e==null?void 0:e[1];m&&(m=H(m,"YYYY-MM-DD").add(1,"d").format("YYYY-MM-DD")),at({page:n.pagination.page||1,size:n.pagination.limit||20,uploadUser:t===""?"":((d=(f=F.user)==null?void 0:f.id)==null?void 0:d.id)&&Q((o=(u=F.user)==null?void 0:u.id)==null?void 0:o.id),beginTime:s,endTime:m}).then(i=>{const a=i.data.data;n.dataList=(a==null?void 0:a.data)||[],n.pagination.total=(a==null?void 0:a.total)||0}).catch(()=>{n.dataList=[],n.pagination.total=0})},W=()=>{var t;(t=b.value)==null||t.resetForm()},M=()=>{var t;(t=D.value)==null||t.exportTable()},O=async()=>{const t=await ct();I.fieldConfig=t.data.result||[]},Y=async t=>{c.view=t},P=()=>{c.graphicsLayer=pt(c.view,{id:"error-report",title:"错误属性设备"})};return j(()=>{var t;h(),(t=v.value)==null||t.toggleDrawer("btt",!0),O()}),(t,e)=>{const s=ot,m=tt,l=et,f=V,d=J;return K(),X(d,{ref_key:"refDrawer",ref:v,"left-drawer":!0,"left-drawer-absolute":!1,"left-drawer-bar-hide":!1,"bottom-drawer":!0,"left-drawer-width":400,"left-drawer-title":" ","bottom-drawer-bar-position":"right","bottom-drawer-title":"属性上报列表"},{right:_(()=>[g(m,{ref_key:"refForm",ref:b,config:y(L)},null,8,["config"]),g(l,{config:y(n)},null,8,["config"])]),"left-title":_(()=>[k("span",null,Z(y(S).title),1)]),left:_(()=>[g(yt,{config:y(S)},null,8,["config"])]),bottom:_(()=>[g(f,{ref_key:"refForm",ref:b,config:y(L)},null,8,["config"]),k("div",ht,[g(l,{ref_key:"refTable",ref:D,config:y(n)},null,8,["config"])])]),default:_(()=>[g(s,{onMapLoaded:Y,onPipeLoaded:P})]),_:1},512)}}}),sr=rt(_t,[["__scopeId","data-v-d4bb6564"]]);export{sr as default};
