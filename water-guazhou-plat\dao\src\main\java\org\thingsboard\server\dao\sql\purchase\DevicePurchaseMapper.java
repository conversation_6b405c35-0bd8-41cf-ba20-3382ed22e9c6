package org.thingsboard.server.dao.sql.purchase;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchase;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchasePageRequest;

@Mapper
public interface DevicePurchaseMapper extends BaseMapper<DevicePurchase> {
    IPage<DevicePurchase> findByPage(DevicePurchasePageRequest request);

    boolean update(DevicePurchase attr);

    String getNameById(String id);
}
