/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.maintenance;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.MaintainRecordId;
import org.thingsboard.server.common.data.maintain.MaintainRecord;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintain.MaintainRecordService;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.util.List;

/**
 * 保养记录相关API
 */

@RestController
@RequestMapping("/api/maintainRecord")
public class MaintainRecordController extends BaseController {

    @Autowired
    private MaintainRecordService maintainRecordService;

    /**
     * 新增保养设定
     *
     * @param
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @PostMapping("/add")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_MAINTAIN_ADD)
    public ResponseEntity<Object> add(@RequestBody MaintainRecord body) throws ThingsboardException {
        JSONObject result = new JSONObject();
        HttpStatus httpStatus;
        // 校验参数
        if (body.getDeviceId() == null) {
            httpStatus = HttpStatus.BAD_REQUEST;
            result.put("err", "请指定保养设备");
            return ResponseEntity.status(httpStatus).body(result);
        }
        // 保存维修记录
        body.setTenantId(getTenantId());
        body = maintainRecordService.addMaintainRecord(body);
        return ResponseEntity.status(HttpStatus.OK).body(body);
    }

    /**
     * 根据tenantId查询保养记录
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @GetMapping("/findByTenant")
    @SysLog(detail = DataConstants.OPERATING_TYPE_MAINTAIN_RECORD_GET)
    public ResponseEntity<Object> findByTenantId() throws ThingsboardException {
        List<MaintainRecord> maintainList = maintainRecordService.findByTenantId(getTenantId());
        return ResponseEntity.ok(maintainList);
    }

    /**
     * 根据project查询保养记录
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @GetMapping("/findByProject")
    @SysLog(detail = DataConstants.OPERATING_TYPE_MAINTAIN_RECORD_GET)
    public ResponseEntity<Object> findByProjectId(@Param("projectId") String projectId) throws ThingsboardException {
        List<MaintainRecord> maintainList = maintainRecordService.findByProject(projectId);
        return ResponseEntity.ok(maintainList);
    }


    /**
     * 修改保养状态
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @PostMapping("changeStatus")
    public ResponseEntity<Object> changeStatus(@RequestBody JsonNode body) throws ThingsboardException {
        MaintainRecord maintainRecord = maintainRecordService.findById(new MaintainRecordId(UUIDConverter.fromString(body.get("maintainId").textValue())));
        if (maintainRecord != null) {
            maintainRecord.setStatus(body.get("status").textValue());
            maintainRecord.setAdditionalInfo(body.has("additionalInfo") ? body.get("additionalInfo") : null);
            maintainRecord.setMaintainUser(getCurrentUser().getId());
        }
        return ResponseEntity.ok(maintainRecordService.addMaintainRecord(maintainRecord));
    }


    /**
     * 根据设备查询保养记录
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @GetMapping("/findByDevice/{id}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_MAINTAIN_RECORD_GET)
    public ResponseEntity<Object> findByDeviceId(@PathVariable("id") String id) throws ThingsboardException {
        List<MaintainRecord> maintainList = maintainRecordService.findByDeviceId(new DeviceId(UUIDConverter.fromString(id)));
        return ResponseEntity.ok(maintainList);
    }

    /**
     * 根据tenantId查询保养记录
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @GetMapping("/findByTenantAndStatus/{status}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_MAINTAIN_RECORD_GET)
    public ResponseEntity<Object> findByTenantIdAndStatus(@PathVariable("status") String status) throws ThingsboardException {
        List<MaintainRecord> maintainList = maintainRecordService.findByTenantIdAndStatus(getTenantId(), status);
        return ResponseEntity.ok(maintainList);
    }

    /**
     * 根据tenantId查询保养记录
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @GetMapping("/findByProjectAndStatus/")
    @SysLog(detail = DataConstants.OPERATING_TYPE_MAINTAIN_RECORD_GET)
    public ResponseEntity<Object> findByProjectIdAndStatus(@Param("status") String status, @Param("projectId") String projectId) throws ThingsboardException {
        List<MaintainRecord> maintainList = maintainRecordService.findByProjectAndStatus(projectId, status);
        return ResponseEntity.ok(maintainList);
    }

}
