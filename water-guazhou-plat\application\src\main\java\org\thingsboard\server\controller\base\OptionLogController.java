/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.optionLog.OptionLog;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.optionLog.OptionLogService;
import org.thingsboard.server.dao.util.imodel.query.base.OptionLogListPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.security.model.SecurityUser;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/option")
public class OptionLogController extends BaseController {

    @Autowired
    private OptionLogService optionLogService;

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/log/getLogList/{options}",  method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_LOG_INFO)
    public List<OptionLog> getLogList(@PathVariable("options") String options,
                                      @RequestParam(required = false, defaultValue = "") String keyword,
                                      @RequestParam(required = false) String ip,
                                      @RequestParam(required = false) Long startTime,
                                      @RequestParam(required = false) Long endTime) throws ThingsboardException {
        checkParameter("options", options);
        try {
            SecurityUser user = getCurrentUser();
            return checkNotNull(optionLogService.getLogList(user.getTenantId(), keyword, ip, options, startTime, endTime, user.getAuthority().name()));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/log/getLogList/page/{options}",  method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_LOG_INFO)
    public PageData<OptionLog> getLogListByPage(@PathVariable("options") String options,
                                                @RequestParam int page, @RequestParam int size,
                                                @RequestParam(required = false, defaultValue = "") String keyword,
                                                @RequestParam(required = false) String ip,
                                                @RequestParam(required = false) Long startTime,
                                                @RequestParam(required = false) Long endTime) throws ThingsboardException {
        checkParameter("options", options);
        try {
            SecurityUser user = getCurrentUser();
            return checkNotNull(optionLogService.getLogListByPage(user.getTenantId(), keyword, ip, options, startTime, endTime, user.getAuthority().name(), page, size));
        } catch (Exception e) {
            throw handleException(e);
        }
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/log/getLogList/options",  method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_OPTIONS_LOG_INFO)
    public Map<String, Object> getOptionLogList(@RequestParam(required = false) Long startTime,
                                                @RequestParam(required = false) Long endTime,
                                                @RequestParam(required = false) String ip,
                                                @RequestParam(required = false, defaultValue = "") String keyword,
                                                @RequestParam(required = false, defaultValue = "1") Integer page,
                                                @RequestParam(required = false, defaultValue = "15") Integer size) throws ThingsboardException {
        try {
            SecurityUser user = getCurrentUser();
            PageRequest pageRequest = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");
            return checkNotNull(optionLogService.getOptionLogList(user.getTenantId(), startTime, endTime, user.getAuthority().name(), ip, keyword, pageRequest));
        } catch (Exception e) {
            throw handleException(e);
        }
    }


    /**
     * 日志审核
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/log/examine", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_OPTIONS_LOG_INFO)
    public String Examine(@RequestBody String[] ids) throws ThingsboardException {
        SecurityUser currentUser = getCurrentUser();
        String examineId = UUIDConverter.fromTimeUUID(currentUser.getId().getId());
        String examineName = currentUser.getLastName();
        String examineTenantId = UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId());
        Long examineTime = System.currentTimeMillis();
        for (String id : ids) {
            optionLogService.examine(id, examineId, examineName, examineTenantId, examineTime);
        }
        return "审核成功";
    }

    /**
     * 查询平台管理-日志列表
     */
    @MonitorPerformance(description = "平台管理-查询登录日志列表")
    @ApiOperation(value = "查询登录日志列表")
    @GetMapping("log/list")
    public IstarResponse list(OptionLogListPageRequest optionLogListPageRequest) {
        return IstarResponse.ok(optionLogService.selectLogList(optionLogListPageRequest));
    }

    /**
     * 查询平台管理-日志列表
     */
    @MonitorPerformance(description = "平台管理-查询日志列表")
    @ApiOperation(value = "查询日志列表")
    @GetMapping("log/operation/list")
    public IstarResponse operationList(OptionLogListPageRequest optionLogListPageRequest) {
        return IstarResponse.ok(optionLogService.selectOperationLogList(optionLogListPageRequest));
    }
}
