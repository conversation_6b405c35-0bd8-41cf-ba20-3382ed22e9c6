<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.call.MsgSendMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.call.MsgSend">
        select a.*, b.first_name as creatorName
        from tb_service_call_msg_send a left join tb_user b on a.creator = b.id
        where a.receive_phone like '%'||#{receivePhone}||'%'
        and (a.receive_person like '%'||#{keywords}||'%' or a.content like '%'||#{keywords}||'%' or b.first_name like '%'||#{keywords}||'%')
        and a.status like '%'||#{status}||'%'
        <if test="start != null">
        and a.create_time &gt;= to_timestamp(#{start} / 1000)
        </if>
        <if test="end != null">
            and a.create_time &lt;= to_timestamp(#{end} / 1000)
        </if>
        and a.tenant_id = #{tenantId}
        order by a.create_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_call_msg_send a left join tb_user b on a.creator = b.id
        where a.receive_phone like '%'||#{receivePhone}||'%'
        and (a.receive_person like '%'||#{keywords}||'%' or a.content like '%'||#{keywords}||'%' or b.first_name like '%'||#{keywords}||'%')
        and a.status like '%'||#{status}||'%'
        <if test="start != null">
            and a.create_time &gt;= to_timestamp(#{start} / 1000)
        </if>
        <if test="end != null">
            and a.create_time &lt;= to_timestamp(#{end} / 1000)
        </if>
        and a.tenant_id = #{tenantId}
    </select>
</mapper>