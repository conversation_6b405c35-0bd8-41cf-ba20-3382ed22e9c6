package org.thingsboard.server.dao.construction.device;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDevice;
import org.thingsboard.server.dao.sql.smartOperation.construction.device.SoDeviceMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDevicePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDeviceSaveRequest;

import java.util.List;

@Service
public class SoDeviceServiceImpl implements SoDeviceService {
    @Autowired
    private SoDeviceMapper mapper;


    @Override
    public IPage<SoDevice> findAllConditional(SoDevicePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoDevice save(SoDeviceSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SoDevice entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean isSerialIdExists(String serialId, String id, String tenantId) {
        return mapper.isSerialIdExists(serialId, id, tenantId);
    }

    @Override
    public boolean deleteBatch(List<String> idList) {
        return mapper.deleteBatchIds(idList) > 0;
    }

}
