package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionEstimate;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionEstimatePageRequest;

@Mapper
public interface SoConstructionEstimateMapper extends BaseMapper<SoConstructionEstimate> {
    IPage<SoConstructionEstimate> findByPage(SoConstructionEstimatePageRequest request);

    boolean update(SoConstructionEstimate entity);

    boolean updateFully(SoConstructionEstimate entity);

    int save(SoConstructionEstimate entity);

    @Override
    default int insert(SoConstructionEstimate entity) {
        return save(entity);
    }

    String getConstructionCodeById(String id);

    String getIdByConstructionCodeAndTenantId(@Param("constructionCode") String constructionCode, @Param("tenantId") String tenantId);


}
