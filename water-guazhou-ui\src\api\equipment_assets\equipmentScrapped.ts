import { request } from '@/plugins/axios';

// 报废单
// 分页条件查询报废单信息
export const getdeviceDumpSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
  name?: string;
  executionUserId?: string;
  storehouseId?: string;
  startTimeFrom?: string;
  startTimeTo?: string;
}) =>
  request({
    url: `/api/deviceDump`,
    method: 'get',
    params
  });

// 报废设备(动作)
export const postdump = (id: string) =>
  request({
    url: `/api/deviceDump/${id}/dump`,
    method: 'post'
  });

// 添加/修改报废单
export const postdeviceDump = (params?: {
  name: string;
  executionUserId: string;
  startTime?: string;
  executionDays: string;
  intervalDays?: string;
  executionNum?: string;
  executionType?: string;
  storehouseId?: string;
  remark?: string;
}) =>
  request({
    url: `/api/deviceDump`,
    method: 'post',
    data: params
  });

// 分页条件查询设备报废详情
export const getPlanTaskDetail = (params?: {
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
  mainId?: string;
  serialId?: string;
  shelvesId?: string;
}) =>
  request({
    url: `/api/deviceDumpDetail`,
    method: 'get',
    params
  });
