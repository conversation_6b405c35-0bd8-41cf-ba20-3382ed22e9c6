import request from '@/plugins/axios';

/**
 * 分页查询设备用户权限关联
 * @param params 查询参数
 */
export function getDeviceUserAuthList(params: any) {
  // 确保包含必要的分页参数
  const defaultParams = {
    page: 1,
    size: 10
  };

  return request({
    url: '/api/deviceAuth',
    method: 'get',
    params: { ...defaultParams, ...params }
  });
}

/**
 * 根据设备ID查询设备用户权限关联
 * @param deviceId 设备ID
 */
export function getDeviceUserAuthByDeviceId(deviceId: string) {
  return request({
    url: `/api/deviceAuth/device/${deviceId}`,
    method: 'get'
  });
}

/**
 * 根据用户ID查询设备用户权限关联
 * @param userId 用户ID
 */
export function getDeviceUserAuthByUserId(userId: string) {
  return request({
    url: `/api/deviceAuth/user/${userId}`,
    method: 'get'
  });
}

/**
 * 保存设备用户权限关联
 * @param data 设备用户权限关联
 */
export function saveDeviceUserAuth(data: any) {
  return request({
    url: '/api/deviceAuth',
    method: 'post',
    data
  });
}

/**
 * 批量保存设备用户权限关联
 * @param data 设备用户权限关联批量保存请求
 */
export function batchSaveDeviceUserAuth(data: any) {
  return request({
    url: '/api/deviceAuth/batch',
    method: 'post',
    data
  });
}

/**
 * 删除设备用户权限关联
 * @param id 设备用户权限关联ID
 */
export function deleteDeviceUserAuth(id: string) {
  return request({
    url: `/api/deviceAuth/${id}`,
    method: 'delete'
  });
}

/**
 * 根据设备ID删除设备用户权限关联
 * @param deviceId 设备ID
 */
export function deleteDeviceUserAuthByDeviceId(deviceId: string) {
  return request({
    url: `/api/deviceAuth/device/${deviceId}`,
    method: 'delete'
  });
}

/**
 * 检查用户是否有设备权限
 * @param deviceId 设备ID
 * @param userId 用户ID
 */
export function checkUserDeviceAuth(deviceId: string, userId: string) {
  return request({
    url: '/api/deviceAuth/check',
    method: 'get',
    params: { deviceId, userId }
  });
}

/**
 * 获取设备列表
 * @param params 查询参数
 */
export function getDeviceList(params: any) {
  // 使用统一的设备列表接口
  // 确保包含必要的分页参数
  const defaultParams = {
    page: 1,
    size: 10,
    typeId: '',
    serialId: '',
    name: '',
    model: ''
  };

  // 如果有组织ID，添加到查询参数中
  if (params.orgId) {
    defaultParams.orgId = params.orgId;
  }

  return request({
    url: '/api/device/m',
    method: 'get',
    params: { ...defaultParams, ...params }
  });
}

/**
 * 获取用户列表
 * @param params 查询参数
 */
export function getUserList(params: any) {
  // 使用getAll接口获取所有用户
  return request({
    url: '/api/user/getAll',
    method: 'get',
    params
  });
}
