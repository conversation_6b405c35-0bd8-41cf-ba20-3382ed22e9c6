package org.thingsboard.server.controller.waterSource.inspection;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitHandoverRecord;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitHandoverRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitHandoverRecordSaveRequest;
import org.thingsboard.server.dao.circuit.CircuitHandoverRecordService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/sp/circuitHandoverRecord")
public class CircuitHandoverRecordController extends BaseController {
    @Autowired
    private CircuitHandoverRecordService service;

    @GetMapping
    public IPage<CircuitHandoverRecord> findAllConditional(CircuitHandoverRecordPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public CircuitHandoverRecord save(@RequestBody CircuitHandoverRecordSaveRequest req) {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody CircuitHandoverRecordSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}