package org.thingsboard.server.dao.accessControl;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.AccessControl;
import org.thingsboard.server.dao.model.sql.VideoEntity;

import java.util.List;

public interface AccessControlService {
    PageData<AccessControl> findList(int page, int size, String projectId, TenantId tenantId);

    void save(AccessControl entity);

    AccessControl findById(String id);

    void remove(List<String> ids);

    /**
     * 保存门禁并关联视频
     */
    AccessControl saveWithVideos(AccessControl entity, TenantId tenantId);

    /**
     * 根据ID查询门禁并包含关联的视频信息
     */
    AccessControl findByIdWithVideos(String id);

    /**
     * 获取可用的视频列表（用于门禁关联选择）
     */
    List<VideoEntity> getAvailableVideos(String projectId, TenantId tenantId);
}
