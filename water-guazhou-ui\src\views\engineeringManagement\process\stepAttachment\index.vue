<!--资料类型-->
<template>
  <TreeBox v-loading="!!TreeData.loading">
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch ref="refSearch" :config="searchConfig" />
    <CardTable ref="refTable" :config="tableConfig" class="card-table" />
    <DialogForm ref="refForm" :config="formConfig" />
  </TreeBox>
</template>
<script lang="ts" setup>
import {
  Delete,
  Edit,
  Plus,
  Search as SearchIcon
} from '@element-plus/icons-vue';
import { useBusinessStore } from '@/store';
import { SLConfirm } from '@/utils/Message';
import {
  delStepAttachment,
  editStepAttachment,
  processStepList,
  processTypeList,
  stepAttachmentList
} from '@/api/engineeringManagement/process';
import { formatTree } from '@/utils/GlobalHelper';
import useGlobal from '@/hooks/global/useGlobal';

const { $messageError, $messageSuccess, $messageWarning } = useGlobal();
const refTable = ref<ICardTableIns>();
const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();

// const checkNum = (rule, value, callback) => {
//   if (value >= 1) {
//     callback()
//   } else {
//     callback(new Error('请填写数量'))
//   }
// }

// 新增按钮
const searchConfig = reactive<ISearch>({
  filters: [{ type: 'input', label: '节点名称', field: 'name' }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '新增',
          svgIcon: shallowRef(Plus),
          click: () => handleAddEdit()
        },
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        }
      ]
    }
  ]
});

// 数据列表配置
const tableConfig = reactive<ICardTable>({
  loading: true,
  indexVisible: true,
  columns: [
    { label: '编号', prop: 'code', minWidth: 120, align: 'center' },
    { label: '工程类型', prop: 'mainName', minWidth: 120, align: 'center' },
    { label: '步骤名称', prop: 'stepName', minWidth: 120, align: 'center' },
    { label: '附件名称', prop: 'name', minWidth: 120, align: 'center' },
    { label: '必须数量', prop: 'num', minWidth: 120, align: 'center' },
    { label: '详细说明', prop: 'remark', minWidth: 120, align: 'center' }
  ],
  dataList: [],
  operationFixed: 'right',
  operationWidth: 180,
  operations: [
    {
      perm: true,
      text: '编辑',
      isTextBtn: false,
      svgIcon: shallowRef(Edit),
      click: (row) => handleAddEdit(row)
    },
    {
      perm: true,
      text: '删除',
      isTextBtn: false,
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: (row) => handleDelDataType([row.id])
    }
  ],
  pagination: {
    hide: true
  }
});
// 弹框表单配置
const formConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: 120,
  dialogWidth: 500,
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '工程步骤',
          field: 'stepId',
          options: [],
          rules: [{ required: true, message: '请选择工程步骤' }]
        },
        {
          type: 'input',
          label: '附件名称',
          field: 'name',
          rules: [{ required: true, message: '请填写附件名称' }],
          placeholder: '请填写附件名称'
        },
        {
          type: 'input-number',
          label: '必须数量',
          field: 'num',
          rules: [{ required: true, message: '请填写数量' }],
          placeholder: '请填写数量'
        },
        {
          type: 'textarea',
          label: '详细说明',
          field: 'remark',
          placeholder: '请填写详细说明'
        }
      ]
    }
  ]
});
// 分类树
const TreeData = reactive<SLTreeConfig>({
  title: '工程类型',
  data: [],
  currentProject: {},
  isFilterTree: true,
  treeNodeHandleClick: (data) => {
    // 设置当前选中项目信息
    TreeData.currentProject = data;
    useBusinessStore().SET_selectedProject(data);
    refreshData();
  }
});
// 附件弹框配置
const handleAddEdit = async (row?: any) => {
  // 获取表单步骤数据列表
  const result = await stepList();
  const data = result.map((data) => {
    return { label: data.name, value: data.id };
  });
  const step = formConfig.group[0].fields.find(
    (field) => field.field === 'stepId'
  ) as IFormSelect;
  step.options = data;
  formConfig.defaultValue = {
    ...(row || {})
  };
  formConfig.submit = (params: any) => {
    SLConfirm('确定提交？', '提示信息').then(() => {
      formConfig.submitting = true;
      params = {
        ...params,
        mainId: TreeData.currentProject?.value
      };
      editStepAttachment(params)
        .then(() => {
          refForm.value?.closeDialog();
          formConfig.submitting = false;
          $messageSuccess('保存成功');
          refreshData();
        })
        .catch((error) => {
          $messageError(error);
          formConfig.submitting = false;
        });
    });
  };
  refForm.value?.openDialog();
};
// 删除数据
const handleDelDataType = (ids: string[]) => {
  SLConfirm('确定删除？', '提示信息').then(() => {
    delStepAttachment(ids)
      .then(() => {
        $messageSuccess('删除成功');
        refreshData();
      })
      .catch((error) => {
        $messageWarning(error);
      });
  });
};

// 刷新数据
const refreshData = async () => {
  tableConfig.loading = true;
  const query = refSearch.value?.queryParams || {};
  const params = {
    page: 1,
    size: 9999,
    mainId: TreeData.currentProject?.value,
    ...query
  };
  const result = await stepAttachmentList(params);
  tableConfig.dataList = result.data?.data?.data;
  tableConfig.loading = false;
};

// 刷新数据
const stepList = async () => {
  const params = {
    page: 1,
    size: 9999,
    mainId: TreeData.currentProject?.value
  };
  const result = await processStepList(params);
  return result.data?.data?.data;
};

onMounted(async () => {
  // 获取类型
  const result = await processTypeList({ page: 1, size: 9999 });
  const data = result.data?.data.data;
  TreeData.data = formatTree(data || []);
  TreeData.currentProject = TreeData.data[0];
  await refreshData();
});
</script>
