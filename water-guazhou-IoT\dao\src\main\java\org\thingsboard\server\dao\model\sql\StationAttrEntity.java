package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_STATION_ATTR_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class StationAttrEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_STATION_ATTR_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.TB_STATION_ATTR_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.TB_STATION_ATTR_ATTR)
    private String attr;

    @Column(name = ModelConstants.TB_STATION_ATTR_TYPE)
    private String type;

    @Column(name = ModelConstants.NAME)
    private String name;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.TB_STATION_ATTR_RANGE)
    private String range;

    @Column(name = ModelConstants.TB_STATION_ATTR_UNIT)
    private String unit;

}
