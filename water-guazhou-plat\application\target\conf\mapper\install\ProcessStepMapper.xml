<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.install.ProcessStepMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.install.ProcessStep">
        select a.*, b.name as mainName, c.name as formName
        from tb_install_process_step a left join tb_install_process_type b on a.main_id = b.id
        left join tb_form c on a.form_id = c.id
        where a.name like '%' || #{name} || '%'
          and a.tenant_id = #{tenantId}
          and a.main_id = #{mainId}
        order by a.order_num
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_install_process_step a
        where a.name like '%' || #{name} || '%'
          and a.tenant_id = #{tenantId}
          and a.main_id = #{mainId}
    </select>

    <select id="getOrderNum" resultType="int">
        select count(order_num) from tb_install_process_step where main_id = #{mainId}
    </select>

</mapper>