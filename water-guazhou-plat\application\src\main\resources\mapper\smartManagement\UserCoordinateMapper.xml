<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.UserCoordinateMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           user_id,
                           coordinate,
                           create_time,
                           tenant_id<!--@sql from user_coordinate -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinate">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="coordinate" property="coordinate"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <resultMap id="GroupResultMap" type="org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinateGroup">
        <result column="user_id" property="userId"/>
        <collection property="coordinates"
                    resultMap="BaseResultMap"/>
    </resultMap>

    <select id="findDestUserByPageConditional" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_coordinate
        <where>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
        </where>
        order by create_time
    </select>

    <select id="findByPage" resultMap="GroupResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_user
                 left join user_coordinate main
                           on tb_user.id = main.user_id
                 left join (select user_id, max(create_time) newest_time from user_coordinate group by user_id) newest
                           on main.user_id = newest.user_id
        <where>
            <if test="userName != null and userName != ''">
                and user_is_name_like_by_id(main.user_id, #{userName})
            </if>
            <if test="departmentId != null and departmentId != ''">
                and is_user_at_department(main.user_id, #{departmentId})
            </if>
            <if test="userTypeId != null and userTypeId != ''">
                and user_gis_type_is_type_by_user_id(main.user_id, #{userTypeId})
            </if>
            <if test="status != null">
                <choose>
                    <when test="status == 0">
                        and newest_time is null
                    </when>
                    <when test="status == 1">
                        and newest_time not between now() + (#{onlineThreshold} || ' hour')::interval and now()
                    </when>
                    <when test="status == 2">
                        and newest_time between now() + (#{onlineThreshold} || ' hour')::interval and now()
                    </when>
                </choose>
            </if>
            <if test="fromTime != null">
                and main.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and main.create_time &lt;= #{toTime}
            </if>
            and main.tenant_id = #{tenantId}
        </where>
        order by create_time
    </select>

    <select id="findNewest" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from (select coordinate.id,
                     coordinate.coordinate,
                     coordinate.create_time,
                     coordinate.tenant_id,
                     tb_user.id user_id
              from user_coordinate coordinate
                       join (select user_id, max(create_time) newest_time from user_coordinate group by user_id) newest
                            on coordinate.user_id = newest.user_id and coordinate.create_time = newest.newest_time
                       right join tb_user
                                  on coordinate.user_id = tb_user.id
              where tb_user.tenant_id = #{tenantId}) main
        <where>
            <if test="userName != null and userName != ''">
                and user_is_name_like_by_id(user_id, #{userName})
            </if>
            <if test="departmentId != null and departmentId != ''">
                and is_user_at_department(user_id, #{departmentId})
            </if>
            <if test="userTypeId != null and userTypeId != ''">
                and user_gis_type_is_type_by_user_id(user_id, #{userTypeId})
            </if>
            <if test="status != null">
                <choose>
                    <when test="status == 0">
                        and create_time is null
                    </when>
                    <when test="status == 1">
                        and create_time not between now() + (#{onlineThreshold} || ' hour')::interval and now()
                    </when>
                    <when test="status == 2">
                        and create_time between now() + (#{onlineThreshold} || ' hour')::interval and now()
                    </when>
                </choose>
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
        </where>
        order by create_time
    </select>
</mapper>