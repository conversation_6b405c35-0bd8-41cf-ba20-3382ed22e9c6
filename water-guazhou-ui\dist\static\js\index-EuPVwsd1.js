import{d as j,c as f,a8 as g,am as K,bB as T,ay as X,g as M,h as Z,F as r,n as tt,p as a,bh as l,aw as at,q as d,G as h,an as et,J as st,cu as lt,L as ot,C as nt}from"./index-r0dFAfgr.js";const it={key:0,class:"report-content"},dt={class:"info-section"},rt={class:"info-grid"},pt={class:"info-item"},ut={class:"value"},mt={class:"info-item"},ct={class:"value"},vt={class:"info-item"},yt={class:"value"},ft={class:"info-item"},gt={class:"value"},ht={class:"info-item"},bt={class:"info-item"},_t={class:"value"},Ct={class:"info-item"},St={class:"value"},Dt={class:"info-item"},xt={class:"value"},Lt={class:"info-item"},wt={class:"value"},Vt={class:"info-item"},kt={class:"value"},Bt={class:"info-item"},Ft={class:"value"},It={class:"info-item"},qt={class:"value"},$t={class:"chart-section"},zt={class:"chart-controls"},Ot={class:"chart-container"},At={class:"chart-placeholder"},Nt={class:"chart-placeholder"},Pt=j({__name:"index",props:{modelValue:{type:Boolean,default:!1},data:{default:null}},emits:["update:modelValue","close"],setup(H,{emit:E}){const Q=H,b=E,c=f(),v=f(),i=f("day"),p=g({get:()=>Q.modelValue,set:e=>b("update:modelValue",e)}),G=e=>{if(!e)return"yyyy-MM-dd";const t=new Date(e),s=t.getFullYear(),o=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");return`${s}-${o}-${n}`},R=e=>{switch(e){case"I类":return"quality-level-1";case"II类":return"quality-level-2";case"III类":return"quality-level-3";case"IV类":return"quality-level-4";case"V类":return"quality-level-5";default:return"quality-level-unknown"}},_=e=>{const t=[],s=e==="day"?24:e==="month"?30:12;for(let o=0;o<s;o++){let n="";e==="day"?n=`${o.toString().padStart(2,"0")}:00`:e==="month"?n=`${(o+1).toString().padStart(2,"0")}日`:n=`${(o+1).toString().padStart(2,"0")}月`,t.push(n)}return t},u=(e,t,s)=>Array.from({length:e},()=>Math.floor(Math.random()*(s-t+1))+t),W=g(()=>{const e=_(i.value),t=u(e.length,80,120),s=u(e.length,75,110);return{tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["进水流量","出水流量"],top:10},grid:{left:50,right:30,top:50,bottom:30},xAxis:{type:"category",data:e,axisLabel:{rotate:i.value==="day"?45:0}},yAxis:{type:"value",name:"流量(m³/h)",splitLine:{lineStyle:{type:"dashed"}}},series:[{name:"进水流量",type:"bar",data:t,itemStyle:{color:"#ffa500"}},{name:"出水流量",type:"bar",data:s,itemStyle:{color:"#4a90e2"}}]}}),J=g(()=>{const e=_(i.value),t=u(e.length,15,35),s=u(e.length,65,85).map(o=>o/10);return{tooltip:{trigger:"axis"},legend:{data:["COD","PH"],top:10},grid:{left:50,right:50,top:50,bottom:30},xAxis:{type:"category",data:e,axisLabel:{rotate:i.value==="day"?45:0}},yAxis:[{type:"value",name:"COD(mg/L)",position:"left",splitLine:{lineStyle:{type:"dashed"}}},{type:"value",name:"PH值",position:"right",min:6,max:9,splitLine:{show:!1}}],series:[{name:"COD",type:"line",yAxisIndex:0,data:t,smooth:!0,itemStyle:{color:"#ffa500"},lineStyle:{color:"#ffa500"}},{name:"PH",type:"line",yAxisIndex:1,data:s,smooth:!0,itemStyle:{color:"#67c23a"},lineStyle:{color:"#67c23a"}}]}}),U=()=>{b("close"),p.value=!1},y=e=>{i.value=e,T(()=>{var t,s;(t=c.value)==null||t.resize(),(s=v.value)==null||s.resize()})};return K(p,e=>{e&&T(()=>{var t,s;(t=c.value)==null||t.resize(),(s=v.value)==null||s.resize()})}),(e,t)=>{const s=st,o=lt,n=X("VChart"),Y=ot;return M(),Z(Y,{modelValue:p.value,"onUpdate:modelValue":t[3]||(t[3]=m=>p.value=m),title:"水质监控",width:"80%","before-close":U,class:"water-quality-dialog"},{default:r(()=>{var m,C,S,D,x,L,w,V,k,B,F,I,q,$,z,O,A,N;return[e.data?(M(),tt("div",it,[a("div",dt,[a("div",rt,[a("div",pt,[t[4]||(t[4]=a("span",{class:"label"},"采样点位",-1)),a("span",ut,l(e.data.stationName),1)]),t[16]||(t[16]=a("div",{class:"info-item"},[a("span",{class:"label"},"文本内容"),a("span",{class:"value"},"文本内容")],-1)),a("div",mt,[t[5]||(t[5]=a("span",{class:"label"},"抽样时间",-1)),a("span",ct,l(G(e.data.sampleTime)),1)]),t[17]||(t[17]=a("div",{class:"info-item"},[a("span",{class:"label"},"所属水厂"),a("span",{class:"value"},"文本内容")],-1)),a("div",vt,[t[6]||(t[6]=a("span",{class:"label"},"出水流量 m³/h",-1)),a("span",yt,l(((C=(m=e.data.indicators)==null?void 0:m.outflow)==null?void 0:C.toFixed(1))||"文本内容"),1)]),a("div",ft,[t[7]||(t[7]=a("span",{class:"label"},"进水流量 m³/h",-1)),a("span",gt,l(((D=(S=e.data.indicators)==null?void 0:S.inflow)==null?void 0:D.toFixed(1))||"文本内容"),1)]),a("div",ht,[t[8]||(t[8]=a("span",{class:"label"},"水质等级",-1)),a("span",{class:at(["value quality-level",R(e.data.waterQualityLevel)])},l(e.data.waterQualityLevel),3)]),a("div",bt,[t[9]||(t[9]=a("span",{class:"label"},"出水COD mg/L",-1)),a("span",_t,l(((L=(x=e.data.indicators)==null?void 0:x.cod)==null?void 0:L.toFixed(1))||"文本内容"),1)]),a("div",Ct,[t[10]||(t[10]=a("span",{class:"label"},"出水BODs mg/L",-1)),a("span",St,l(((V=(w=e.data.indicators)==null?void 0:w.bod5)==null?void 0:V.toFixed(1))||"文本内容"),1)]),t[18]||(t[18]=a("div",{class:"info-item"},[a("span",{class:"label"},"悬浮物 mg/L"),a("span",{class:"value"},"文本内容")],-1)),a("div",Dt,[t[11]||(t[11]=a("span",{class:"label"},"氨氮 mg/L",-1)),a("span",xt,l(((B=(k=e.data.indicators)==null?void 0:k.ammoniaNitrogen)==null?void 0:B.toFixed(3))||"文本内容"),1)]),a("div",Lt,[t[12]||(t[12]=a("span",{class:"label"},"总氮 mg/L",-1)),a("span",wt,l(((I=(F=e.data.indicators)==null?void 0:F.totalNitrogen)==null?void 0:I.toFixed(2))||"文本内容"),1)]),a("div",Vt,[t[13]||(t[13]=a("span",{class:"label"},"总磷 mg/L",-1)),a("span",kt,l((($=(q=e.data.indicators)==null?void 0:q.totalPhosphorus)==null?void 0:$.toFixed(3))||"文本内容"),1)]),a("div",Bt,[t[14]||(t[14]=a("span",{class:"label"},"PH值",-1)),a("span",Ft,l(((O=(z=e.data.indicators)==null?void 0:z.ph)==null?void 0:O.toFixed(1))||"文本内容"),1)]),a("div",It,[t[15]||(t[15]=a("span",{class:"label"},"粪大肠菌群数",-1)),a("span",qt,l(((N=(A=e.data.indicators)==null?void 0:A.fecalColiform)==null?void 0:N.toFixed(0))||"文本内容"),1)])])]),a("div",$t,[t[24]||(t[24]=a("h3",null,"曲线分析",-1)),a("div",zt,[d(o,null,{default:r(()=>[d(s,{type:i.value==="day"?"primary":"default",size:"small",onClick:t[0]||(t[0]=P=>y("day"))},{default:r(()=>t[19]||(t[19]=[h(" 日 ")])),_:1},8,["type"]),d(s,{type:i.value==="month"?"primary":"default",size:"small",onClick:t[1]||(t[1]=P=>y("month"))},{default:r(()=>t[20]||(t[20]=[h(" 月 ")])),_:1},8,["type"]),d(s,{type:i.value==="year"?"primary":"default",size:"small",onClick:t[2]||(t[2]=P=>y("year"))},{default:r(()=>t[21]||(t[21]=[h(" 年 ")])),_:1},8,["type"])]),_:1})]),a("div",Ot,[a("div",At,[t[22]||(t[22]=a("p",null,"进水流量 vs 出水流量 趋势图",-1)),d(n,{ref_key:"refBarChart",ref:c,option:W.value,class:"chart"},null,8,["option"])]),a("div",Nt,[t[23]||(t[23]=a("p",null,"COD vs PH 趋势图",-1)),d(n,{ref_key:"refLineChart",ref:v,option:J.value,class:"chart"},null,8,["option"])])])])])):et("",!0)]}),_:1},8,["modelValue"])}}}),Mt=nt(Pt,[["__scopeId","data-v-37a59b0d"]]);export{Mt as W};
