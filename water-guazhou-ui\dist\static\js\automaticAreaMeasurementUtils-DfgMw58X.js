import{T as j,R as W}from"./index-r0dFAfgr.js";import{i as C,o as l,r as v}from"./automaticLengthMeasurementUtils-DljoUgEz.js";import{ao as w,b as M}from"./Point-WxyopZva.js";import{r as U}from"./earcut-BJup91r2.js";import{af as p,aR as $,ev as k,ea as A,b0 as S,ew as x}from"./MapView-DaoQedLH.js";import{j as E,E as F,Y as G,K as H}from"./plane-BhzlJB-C.js";import{S as K}from"./triangle-lwOWqU0w.js";import{geodesicArea as T}from"./geometryEngine-OGzB5MRq.js";import{H as q}from"./AnimatedLinesLayer-B2VbV4jv.js";function Y(r,t=m()){return R(r,t)}function Z(r,t=m()){return R(r,t,!1)}function R(r,t,s=r.hasZ){const u=C(r.spatialReference),a=w(u);if(j(a))return null;const b=(i,o)=>!(o.length<2)&&($(i,o[0],o[1],s&&o[2]||0),!0);let h=0;for(const i of r.rings){const o=i.length;if(o<3)continue;const{positionsWorldCoords:e}=t;for(;e.length<o;)e.push(p());const g=z,f=$(B,0,0,0),y=1/o;for(let n=0;n<o;n++){if(!b(g,i[n])||!k(g,r.spatialReference,e[n],u))return null;A(f,f,e[n],y)}const d=E(e[0],e[1],f,F());if(S(G(d))===0)continue;for(let n=0;n<o;n++)H(d,f,e[n],e[n]);const c=D(e);for(let n=0;n<c.length;n+=3)h+=K(e[c[n]],e[c[n+1]],e[c[n+2]])}return l(h,a)}const z=p(),B=p();function m(){return{positionsWorldCoords:[]}}function D(r){return U(I(r),[],2)}function I(r){const t=new Float64Array(2*r.length);for(let s=0;s<r.length;++s){const u=r[s],a=2*s;t[a+0]=u[0],t[a+1]=u[1]}return t}function J(r){const{spatialReference:t}=r;return v(t,L,N,O,r)}function L(r){return l(Math.abs(q([r],"square-meters")[0]),"square-meters")}function N(r){try{return l(Math.abs(T(r,"square-meters")),"square-meters")}catch{return null}}function O(r){const t=[];return x(r,t)?l(Math.abs(q([{type:"polygon",rings:t,spatialReference:M.WGS84}],"square-meters")[0]),"square-meters"):null}function P(r,t,s=m()){if(t==="on-the-ground"){const u=J(r);return W(u)?u:Z(r,s)}return Y(r,s)}function sr(r,t=m()){return P(r,"on-the-ground",t)}export{P as i,sr as u};
