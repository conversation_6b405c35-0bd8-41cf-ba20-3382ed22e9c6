<template>
  <el-button
    :icon="Search"
    @click="extentTo"
  >
    缩放至
  </el-button>
</template>
<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'

const props = defineProps<{
  visible?: boolean
  config: {
    userId: string
    extentTo:(userId: string) => void
  }
}>()
const extentTo = () => {
  props.config.extentTo?.(props.config.userId)
}
</script>
<style lang="scss" scoped>
.pop-image {
  width: 400px;
}
.content {
  padding-right: 8px;
  width: 100%;
  height: 200px;
}
</style>
