package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseConnection;
import org.thingsboard.server.dao.model.sql.base.ConstantsAttributeList;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseConnectionPageRequest;
import org.thingsboard.server.dao.util.imodel.query.base.ConstantsAttributePageRequest;

import java.util.List;

/**
 * 平台管理-字典Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Mapper
public interface ConstantsMapper {

    IPage<ConstantsAttributeList> selectConstantsList(ConstantsAttributePageRequest constantsAttribute);

}
