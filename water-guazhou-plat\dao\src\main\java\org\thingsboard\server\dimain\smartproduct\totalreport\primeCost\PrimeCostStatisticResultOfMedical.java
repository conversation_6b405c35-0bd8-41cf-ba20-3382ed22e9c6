package org.thingsboard.server.dimain.smartproduct.totalreport.primeCost;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class PrimeCostStatisticResultOfMedical {
    // 工厂名称
    private String factory;

    // 药品名称
    private String product;

    // 药价，元/吨
    private BigDecimal medicalPrice;

    // 总用药量，吨
    private BigDecimal totalMedicalAmount;

    // 总药价，元
    private BigDecimal totalMedicalPrice;

    // 去年总药价
    private BigDecimal totalMedicalPriceLastYear;

    // 吨水成本（吨/元）
    private BigDecimal pricePerWater;


}
