package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.springframework.ui.Model;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DATA_BACKUP_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class DataBackup {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.DATA_BACKUP_TYPE)
    private String type;

    @Column(name = ModelConstants.DATA_BACKUP_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.DATA_BACKUP_CREATER)
    private String creater;

    @Column(name = ModelConstants.DATA_BACKUP_CREATE_BY)
    private String createBy;

    @Column(name = ModelConstants.DATA_BACKUP_END_TIME)
    private Long endTime;


}
