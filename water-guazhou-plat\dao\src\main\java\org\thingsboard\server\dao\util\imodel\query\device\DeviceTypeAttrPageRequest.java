package org.thingsboard.server.dao.util.imodel.query.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceTypeAttr;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceTypeConstants;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class DeviceTypeAttrPageRequest extends AdvancedPageableQueryEntity<DeviceTypeAttr, DeviceTypeAttrPageRequest> {
    // 设备类型编号
    private String serialId;

    // 属性编码
    private String code;

    // 属性名称
    private String name;

    // 属性名称
    private String typeName;

    // 创建人
    private String creator;

    public String getTopSerialId() {
        return DeviceTypeConstants.TOP_SERIAL_ID;
    }
}
