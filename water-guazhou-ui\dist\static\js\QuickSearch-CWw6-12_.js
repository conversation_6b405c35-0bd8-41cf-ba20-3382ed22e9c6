import{d as $,c as C,r as F,b as x,Q as q,g as Q,h as V,F as G,q as g,i as u,_ as z,X as A,C as P}from"./index-r0dFAfgr.js";import{g as H}from"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as J,a as U}from"./LayerHelper-Cn-iiqxI.js";import{g as W}from"./QueryHelper-ILO3qZqg.js";import{u as j}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{u as K}from"./useScheme-DcjSAE44.js";import X from"./RightDrawerMap-D5PhmGFO.js";import{_ as Y}from"./SchemeManage.vue_vue_type_script_setup_true_lang-fv9Irhyi.js";import{_ as Z}from"./SaveScheme.vue_vue_type_script_setup_true_lang-Bt-6iBz5.js";import ee from"./SchemeHeader-BLYQTCg3.js";import{QueryByPolygon as re}from"./wfsUtils-DXofo3da.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";const te=$({__name:"QuickSearch",setup(ie){const h=C(),l=C(),t=F({tabs:[],layerInfos:[],layerIds:[],loading:!1}),e={},v=F({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>t.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>S("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>t.loading,iconifyIcon:"ep:crop",click:()=>S("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",disabled:()=>t.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>S("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>t.loading,iconifyIcon:"ep:delete",click:()=>E()}]}]},{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!1,label:"选择图层",showCheckbox:!0,field:"layerid",nodeKey:"value"},{type:"btn-group",btns:[{perm:!0,text:"查询",styles:{width:"100%"},loading:()=>t.loading,click:()=>_()},{perm:window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,text:"保存方案",styles:{width:"100%"},click:()=>c.openSaveDialog()}]}]}],labelPosition:"top",gutter:12}),S=r=>{var i,a;e.view&&((i=e.graphicsLayer)==null||i.removeAll(),e.graphics=void 0,(a=b.value)==null||a.create(r))},E=()=>{var r;(r=e.graphicsLayer)==null||r.removeAll(),e.graphics=void 0},M=()=>{var r,i;if(GIS_SERVER_SWITCH){const a=(r=v.group.find(p=>p.id==="layer"))==null?void 0:r.fields[0];let s=((i=e.view)==null?void 0:i.layerViews.items[0].layer.sublayers).items.map(p=>({label:p.name,value:p.name}));a.options=s,l.value&&(l.value.dataForm.layerid=t.layerIds)}else t.layerIds=U(e.view),A(t.layerIds).then(a=>{var f,n,d;t.layerInfos=((n=(f=a.data)==null?void 0:f.result)==null?void 0:n.rows)||[];const m=(d=v.group.find(o=>o.id==="layer"))==null?void 0:d.fields[0],s=t.layerInfos.filter(o=>o.geometrytype==="esriGeometryPoint").map(o=>({label:o.layername,value:o.layerid,data:o})),p=t.layerInfos.filter(o=>o.geometrytype==="esriGeometryPolyline").map(o=>({label:o.layername,value:o.layerid,data:o}));m&&(m.options=[{label:"管点类",value:-1,children:s},{label:"管线类",value:-2,children:p}]),l.value&&(l.value.dataForm.layerid=t.layerIds)})},_=async()=>{var r,i,a,m,s,p,f;x.info("正在查询，请稍候...");try{if(t.loading=!0,t.tabs.length=0,GIS_SERVER_SWITCH){const n=((r=l.value)==null?void 0:r.dataForm.layerid)||[];debugger;re(n.join(","),(a=(i=e.graphics)==null?void 0:i.geometry)==null?void 0:a.rings[0]).then(d=>{var w;let o=d.data.features;const k=new Set;o.forEach(y=>{y.id.split(".")[0]!==void 0&&k.add(y.id.split(".")[0])}),Array.from(k).forEach(y=>{let L=o.filter(T=>T.id.split(".")[0]===y);t.tabs.push({name:y,label:`${y}(${L.length})`,data:L})}),(w=h.value)==null||w.refreshDetail(t.tabs)})}else{const n=((s=(m=l.value)==null?void 0:m.dataForm.layerid)==null?void 0:s.filter(d=>d>=0))||[];t.tabs=await W(n,t.layerInfos,{where:"1=1",geometry:(p=e.graphics)==null?void 0:p.geometry}),(f=h.value)==null||f.refreshDetail(t.tabs)}}catch(n){console.log(n),x.error("查询失败")}t.loading=!1},{initSketch:R,destroySketch:D,sketch:b}=j(),I=r=>{r.state==="complete"&&(e.graphics=r.graphics[0],console.log(JSON.stringify(e.graphics)))},N=async r=>{e.view=r,e.graphicsLayer=J(e.view,{id:"search-quick",title:"快速查询"}),R(e.view,e.graphicsLayer,{updateCallBack:I,createCallBack:I}),setTimeout(()=>{M()},1e3)},c=K("quick"),B=async r=>{var a,m,s,p;const i=c.parseScheme(r);(a=l.value)!=null&&a.dataForm&&(l.value.dataForm.layerid=i.layerid||[]),i.graphic&&(e.graphics=H.fromJSON(i.graphic),(m=b.value)==null||m.cancel(),(s=e.graphicsLayer)==null||s.removeAll(),(p=e.graphicsLayer)==null||p.add(e.graphics)),_()},O=r=>{var i;c.submitScheme({...r,type:c.schemeType.value,detail:JSON.stringify({layerid:((i=l.value)==null?void 0:i.dataForm.layerid)||[],graphic:e.graphics})})};return q(()=>{var r;e.graphicsLayer&&((r=e.view)==null||r.map.remove(e.graphicsLayer)),D()}),(r,i)=>{const a=z;return Q(),V(X,{ref_key:"refMap",ref:h,title:"快速查询",onMapLoaded:N},{"right-title":G(()=>[g(ee,{title:"快速查询",onSchemeClick:u(c).openManagerDialog},null,8,["onSchemeClick"])]),default:G(()=>[g(a,{ref_key:"refForm",ref:l,config:u(v)},null,8,["config"]),g(Y,{ref:u(c).getSchemeManageRef,type:u(c).schemeType.value,onRowClick:B},null,8,["type"]),g(Z,{ref:u(c).getSaveSchemeRef,onSubmit:O},null,512)]),_:1},512)}}}),dt=P(te,[["__scopeId","data-v-a368d83c"]]);export{dt as default};
