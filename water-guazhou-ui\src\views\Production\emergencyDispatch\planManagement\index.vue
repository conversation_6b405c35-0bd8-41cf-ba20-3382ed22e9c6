<!-- 预案管理 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable class="card-table" :config="TableConfig" />
    <SLDrawer ref="refForm" :config="addOrUpdateConfig"></SLDrawer>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import useGlobal from '@/hooks/global/useGlobal';
import { SLConfirm } from '@/utils/Message';
import {
  getPlanList,
  postPlan,
  deletePlan
} from '@/api/productionScheduling/emergencyDispatch';
import { GetStationTree } from '@/api/shuiwureports/zhandian';
import { traverse } from '@/utils/GlobalHelper';

const { $btnPerms } = useGlobal();

const refForm = ref<ISLDrawerIns>();

const refSearch = ref<ICardSearchIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '预案名称', field: 'name', type: 'input' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '预案名称', prop: 'name' },
    { label: '所属点位', prop: 'stationName' },
    { label: '注意事项', prop: 'remark' },
    { label: '处理步骤', prop: 'content', isHtml: true }
  ],
  operationWidth: '240px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  width: '800px',
  labelWidth: '100px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增成功';
    if (params.id) text = '修改成功';
    params.stationId = TreeData.currentProject.id;
    postPlan(params)
      .then(() => {
        addOrUpdateConfig.submitting = false;
        refForm.value?.closeDrawer();
        ElMessage.success(text);
        refreshData();
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '预案名称',
          field: 'name',
          rules: [{ required: true, message: '请输入预案名称' }]
        },
        {
          type: 'input',
          label: '注意事项',
          field: 'remark'
        },
        {
          type: 'wangeditor',
          label: '处理步骤',
          field: 'content',
          rules: [{ required: true, message: '请输入预案名称' }]
        }
      ]
    }
  ]
});

const TreeData = reactive<SLTreeConfig>({
  title: ' ',
  data: [],
  currentProject: {},
  isFilterTree: true,
  treeNodeHandleClick: (data) => {
    // 设置当前选中项目信息
    TreeData.currentProject = data;
    refreshData();
  }
});

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增';
  addOrUpdateConfig.defaultValue = {};
  refForm.value?.openDrawer();
};

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑';
  addOrUpdateConfig.defaultValue = { category: row.parentId, ...(row || {}) };
  refForm.value?.openDrawer();
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除该预案', '删除提示').then(() => {
    deletePlan(row.id)
      .then(() => {
        ElMessage.success('删除成功');
        refreshData();
      })
      .catch((error) => {
        ElMessage.error(error.toString());
      });
  });
};

function init() {
  GetStationTree().then((res) => {
    TreeData.data = traverse(res.data.data || []);
    TreeData.currentProject = res.data.data[0];
  });
}

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    stationId: TreeData.currentProject.id,
    ...(refSearch.value?.queryParams || {})
  };
  getPlanList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(async () => {
  init();
});
</script>
