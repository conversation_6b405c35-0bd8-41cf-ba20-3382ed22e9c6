<!-- 设备台账 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    />
    <SLDrawer
      ref="refdetail"
      :config="detailConfig"
    >
      <div class="item">
        <el-tabs
          v-model="activeName"
          @tab-click="handleClick"
        >
          <el-tab-pane
            label="设备信息"
            name="deviceinfo"
          >
          </el-tab-pane>
          <el-tab-pane
            label="维修信息"
            name="repairinfo"
          >
          </el-tab-pane>
          <el-tab-pane
            label="保养信息"
            name="maintenanceinfo"
          >
          </el-tab-pane>
          <el-tab-pane
            label="巡检信息"
            name="patrolinfo"
          >
          </el-tab-pane>
        </el-tabs>
        <el-scrollbar height="100%">
          <DeviceInfo
            v-if="activeName === 'deviceinfo'"
            :id="AddDialogConfig.id"
            :serial-id="AddDialogConfig.serialId"
          />
          <RepairInfo
            v-if="activeName === 'repairinfo'"
            :id="AddDialogConfig.currentId"
          />
          <MaintenanceInfo
            v-if="activeName === 'maintenanceinfo'"
            :id="AddDialogConfig.currentId"
          />
          <PatrolInfo
            v-if="activeName === 'patrolinfo'"
            :id="AddDialogConfig.currentId"
          />
        </el-scrollbar>
      </div>
    </SLDrawer>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
// 设备信息
import DeviceInfo from './components/deviceInfo.vue'
// 维修信息
import RepairInfo from './components/repairinfo.vue'
// 保养信息
import MaintenanceInfo from './components/maintenanceinfo.vue'
// 巡检信息
import PatrolInfo from './components/patrolinfo.vue'
import {
  getDeviceTypeTree,
  getAreaTreeSearch
} from '@/api/equipment_assets/equipmentManage'
import {
  getDeviceStorageJournalSerch,
  exportExcel
} from '@/api/equipment_assets/ledgerManagement'
import { traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refdetail = ref<ISLDrawerIns>()
// 当前选中
const activeName = ref('deviceinfo')

const detailConfig = reactive<IDrawerConfig>({
  title: 'asd',
  labelWidth: '120px',
  width: '1100px',
  group: []
})

const refSearch = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '设备名称', field: 'name', type: 'input' },
    {
      label: '设备标签编码',
      field: 'deviceLabelCode',
      type: 'input',
      labelWidth: '100px'
    },
    {
      label: '安装区域',
      field: 'areaId',
      type: 'select-tree',
      checkStrictly: true,
      options: computed(() => data.installationArea) as any
    },
    { label: '安装位置', field: 'address', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '导出',
          icon: ICONS.EXPORT,
          click: () => refreshData(true)
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '设备编码', prop: 'serialId' },
    { label: '设备标签码', prop: 'deviceLabelCode' },
    { label: '设备型号', prop: 'model' },
    { label: '设备名称', prop: 'name' },
    { label: '使用部门', prop: 'departmentName' },
    { label: '设备类型', prop: 'type' },
    { label: '设备供应商', prop: 'supplierName' },
    { label: '安装区域', prop: 'installAddressName' },
    { label: '安装位置', prop: 'detailInstallAddressName' }
  ],
  operations: [
    {
      type: 'primary',
      text: '详情',
      icon: ICONS.DETAIL,
      perm: $btnPerms('RoleManageEdit'),
      click: row => {
        detailConfig.title = row.name
        AddDialogConfig.serialId = row.serialId
        AddDialogConfig.title = row.name
        AddDialogConfig.currentId = row.deviceLabelCode
        AddDialogConfig.id = row.id

        activeName.value = ''
        refdetail.value?.openDrawer()
        setTimeout(() => {
          activeName.value = 'deviceinfo'
        }, 200)
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const TreeData = reactive<SLTreeConfig>({
  title: ' ',
  data: [],
  currentProject: {},
  expandOnClickNode: false,
  isFilterTree: true,
  treeNodeHandleClick: data => {
    TreeData.currentProject = data
    refreshData()
  }
})

const AddDialogConfig = reactive({
  visible: false,
  title: 'cs',
  currentId: '',
  deviceNo: '',
  id: '',
  serialId: '',
  readonly: false
})

const data = reactive({
  // 安装区域
  installationArea: [],

  // 获取安装区域
  getAreaTreeValue: () => {
    const params = {
      page: 1,
      size: 99999,
      shortName: ''
    }
    getAreaTreeSearch(params).then(res => {
      data.installationArea = traverse(res.data.data.data || [])
    })
  },

  init: () => {
    data.getAreaTreeValue()
  }
})

const handleClick = (tab: any) => {
  console.log('tab ', tab)
}

const refreshData = async (status?: boolean) => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    deviceTypeId: TreeData.currentProject.id || '',
    ...(refSearch.value?.queryParams || {})
  }
  if (status) {
    exportExcel(params).then(res => {
      console.log(res)

      const url = window.URL.createObjectURL(res.data)
      console.log(url)
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', `设备台账.xlsx`)
      document.body.appendChild(link)
      link.click()
    })
    return
  }
  getDeviceStorageJournalSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

function init() {
  getDeviceTypeTree().then(res => {
    TreeData.data = traverse(res.data.data || [])
    refreshData()
  })
}

onMounted(async () => {
  init()
  data.init()
})
</script>
