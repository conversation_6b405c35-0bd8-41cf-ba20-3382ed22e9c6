package org.thingsboard.server.dao.sql.fault;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.fault.FaultReportC;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface FaultReportCMapper extends BaseMapper<FaultReportC> {

    List<FaultReportC> getList(@Param("mainId") String mainId);

    int countByLabelCode(@Param("deviceLabelCode") String deviceLabelCode);

    FaultReportC selectLatestByLabelCode(String deviceLabelCode);


    FaultReportC selectFirstByDeviceLabelCode(@Param("deviceLabelCode") String deviceLabelCode);

    List<Map> getGradeCountByDeviceLabelCode(@Param("deviceLabelCode") String deviceLabelCode);

    List<Map> getNowYearRepairByDeviceLabelCode(@Param("deviceLabelCode") String deviceLabelCode, @Param("nowYear") Date nowYear);

    List<Map> getRepairList(@Param("deviceLabelCode") String deviceLabelCode, @Param("page") int page, @Param("size") int size);

    int getRepairListCount(@Param("deviceLabelCode") String deviceLabelCode);
}
