package org.thingsboard.server.dao.gis;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.gis.GisPeopleSettingEntity;

import java.util.List;

public interface GisPeopleSettingService {
    GisPeopleSettingEntity get(String id);

    /**
     * 分页查询gis人员配置信息
     *
     * @param page     当前页
     * @param size     每页记录数
     * @param name     按名称模糊查询
     * @param tenantId 租户ID
     * @return 数据
     */
    PageData<GisPeopleSettingEntity> findList(Integer page, Integer size, String name, TenantId tenantId);

    void save(GisPeopleSettingEntity entity);

    /**
     * 查询指定类型的人员配置
     *
     * @param type     类型
     * @param tenantId 租户ID
     * @return 数据
     */
    List<GisPeopleSettingEntity> findByType(String type, TenantId tenantId);
}
