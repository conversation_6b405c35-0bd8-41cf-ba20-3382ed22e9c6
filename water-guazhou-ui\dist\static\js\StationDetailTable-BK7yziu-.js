import{d as T,c as i,a8 as g,g as n,n as p,p as s,q as e,F as l,aB as _,aJ as b,h as F,G as A,bh as v,x as O,H as J,I as K,aK as L,aL as U,J as q,K as z,N as G,O as H,C as M}from"./index-r0dFAfgr.js";const P={class:"station-detail"},j={class:"detail-header"},Q={class:"actions"},R={class:"detail-content"},W={class:"station-name"},X=T({__name:"StationDetailTable",props:{detailData:{type:Array,default:()=>[]}},emits:["apply-scheme"],setup(u,{emit:h}){const f=u,y=h,m=i(""),d=i(""),E=i([{label:"方案A",value:"scheme_a"},{label:"方案B",value:"scheme_b"},{label:"方案C",value:"scheme_c"}]),V=g(()=>f.detailData.map(r=>r.stationId)),w=()=>{if(!d.value){O.warning("请选择要应用的方案");return}y("apply-scheme",{schemeId:d.value,stationIds:V.value})};return(r,t)=>{const I=J,c=K,N=L,B=U,C=q,S=z,o=G,k=H;return n(),p("div",P,[s("div",j,[t[3]||(t[3]=s("div",{class:"title"},"泵站运行",-1)),s("div",Q,[e(S,{inline:""},{default:l(()=>[e(c,{label:"当前方案:"},{default:l(()=>[e(I,{modelValue:m.value,"onUpdate:modelValue":t[0]||(t[0]=a=>m.value=a),disabled:""},null,8,["modelValue"])]),_:1}),e(c,{label:"应用方案:"},{default:l(()=>[e(B,{modelValue:d.value,"onUpdate:modelValue":t[1]||(t[1]=a=>d.value=a),placeholder:"请选择方案"},{default:l(()=>[(n(!0),p(_,null,b(E.value,a=>(n(),F(N,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,null,{default:l(()=>[e(C,{type:"primary",onClick:w},{default:l(()=>t[2]||(t[2]=[A("方案应用")])),_:1})]),_:1})]),_:1})])]),s("div",R,[(n(!0),p(_,null,b(u.detailData,(a,x)=>(n(),p("div",{key:x,class:"station-item"},[s("div",W,v(a.stationName),1),e(k,{data:a.pumps,border:"",stripe:"",size:"small"},{default:l(()=>[e(o,{prop:"pumpCode",label:"泵机编号",width:"100"}),e(o,{prop:"pumpName",label:"泵机名称",width:"120"}),e(o,{prop:"pumpType",label:"泵机型号",width:"120"}),e(o,{prop:"pumpNum",label:"泵数量",width:"80"}),e(o,{prop:"companyName",label:"厂家名称",width:"120"}),e(o,{label:"性能参数",width:"180"},{default:l(({row:D})=>[s("div",null,v(D.performanceParameters||"暂无数据"),1)]),_:1})]),_:2},1032,["data"])]))),128))])])}}}),Z=M(X,[["__scopeId","data-v-66863c56"]]);export{Z as default};
