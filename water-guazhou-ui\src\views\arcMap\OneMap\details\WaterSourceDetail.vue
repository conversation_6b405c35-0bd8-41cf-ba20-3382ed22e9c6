<template>
  <div class="one-map-detail">
    <div v-loading="state.detailLoading" class="row1">
      <FieldSet
        :size="'default'"
        :title="'水源监测'"
        :type="'simple'"
        class="row-title"
      ></FieldSet>
      <div class="pie-charts">
        <div ref="refChartDiv" class="pie-chart">
          <VChart ref="refChart1" :option="state.pieChart1"></VChart>
        </div>
        <div class="pie-chart">
          <VChart ref="refChart2" :option="state.pieChart2"></VChart>
        </div>
        <div class="pie-chart">
          <VChart ref="refChart3" :option="state.pieChart3"></VChart>
        </div>
      </div>
    </div>
    <div class="row2">
      <div class="detail-attrgrou-radio">
        <el-radio-group v-model="state.curRadio" @change="refreshRealtimeList">
          <el-radio
            v-for="(item, i) in state.radioGroup"
            :key="i"
            :label="item"
          >
            {{ item }}
          </el-radio>
        </el-radio-group>
      </div>
      <div class="detail-right">
        <div v-loading="state.detailLoading" class="list-items overlay-y">
          <div
            v-for="(item, i) in state.stationRealTimeData"
            :key="i"
            class="list-item"
          >
            <div class="item-label">
              {{ item.propertyName }}
            </div>
            <div class="item-content">
              {{ item.value || '--' }} {{ item.unit }}
            </div>
          </div>
        </div>
        <div v-loading="state.detailLoading" class="chart-box">
          <VChart ref="refChart4" :option="state.lineChartOption"></VChart>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { hourlyLine } from '../../components/components/chart';
import { GetWaterSourceSupplyDetail } from '@/api/mapservice/onemap';
import { transNumberUnit } from '@/utils/GlobalHelper';
import {
  GetStationAttrGroupNames,
  GetStationRealTimeDetail
} from '@/api/shuiwureports/zhandian';
import { gaugeOption } from '../../echarts';
import useDetector from '@/hooks/echarts/useDetector';
import { getWaterSupplyInfo } from '@/api/headwatersManage/headwaterMonitoring';

const emit = defineEmits(['refresh', 'mounted']);
const { proxy }: any = getCurrentInstance();
const resizer = useDetector();
const state = reactive<{
  curRow?: any;
  curRadio: string;
  radioGroup: any[];
  pieChart1: any;
  pieChart2: any;
  pieChart3: any;
  mapClick?: any;
  lineChartOption: any;
  stationRealTimeData: any[];
  detailLoading: boolean;
}>({
  curRadio: '',
  radioGroup: [],
  pieChart1: gaugeOption(0, { max: 100, title: '今日供水量(万m³)' }),
  pieChart2: gaugeOption(0, { max: 100, title: '昨日供水量(万m³)' }),
  pieChart3: gaugeOption(0, { max: 3000, title: '本月供水量(万m³)' }),
  lineChartOption: null,
  stationRealTimeData: [],
  detailLoading: false
});
const refChartDiv = ref<HTMLDivElement>();
const refreshDetail = async (row) => {
  if (row?.stationId !== undefined && row.stationId === state.curRow?.stationId)
    return;
  emit('refresh', { title: row.name });
  state.detailLoading = true;

  try {
    if (row.fromAllStation) {
      const res = await getWaterSupplyInfo();
      state.curRow = res.data?.data.find(
        (item) => item.stationId === row.stationId
      );
    } else {
      state.curRow = row;
    }
    const trans = (value) => {
      const val = transNumberUnit(value ?? 0);
      return { value: +val.value.toFixed(2), unit: val.unit };
    };
    const value1 = trans(state.curRow?.todayWaterSupply);
    const value2 = trans(state.curRow?.yesterdayWaterSupply);
    const value3 = trans(state.curRow?.monthWaterSupply);
    state.pieChart1 = gaugeOption(value1.value, {
      max: 100,
      title: '今日供水量(' + (value1.unit || '') + 'm³)'
    });
    state.pieChart2 = gaugeOption(value2.value, {
      max: 100,
      title: '昨日供水量(' + (value1.unit || '') + 'm³)'
    });
    state.pieChart3 = gaugeOption(value3.value, {
      max: 1000,
      title: '本月供水量(' + (value1.unit || '') + 'm³)'
    });
    resize();
    const p1 = GetWaterSourceSupplyDetail({
      stationId: row.stationId
    }).then((res) => {
      const data = res.data?.data;
      const pressure =
        data?.pressure?.map((item) => item.value?.toFixed(2)) || [];
      const flow =
        data?.Instantaneous_flow?.map((item) => item.value?.toFixed(2)) || [];
      state.lineChartOption = hourlyLine({
        line1: {
          data: flow,
          unit: 'm³/h',
          name: '瞬时流量'
        },
        line2: {
          data: pressure,
          unit: 'MPa',
          name: '压力'
        }
      });
      proxy.$refs['refChart4']?.resize();
    });
    const p2 = GetStationAttrGroupNames({ stationId: row.stationId }).then(
      (res) => {
        state.radioGroup = res.data || [];
        state.curRadio = state.radioGroup[0];
        refreshRealtimeList();
      }
    );
    Promise.all([p1, p2]).finally(() => {
      state.detailLoading = false;
    });
  } catch (error) {
    console.log(error);
    state.detailLoading = false;
  }
};
const refreshRealtimeList = async () => {
  if (state.curRadio) {
    if (state.curRow?.stationId === undefined) {
      state.stationRealTimeData = [];
      return;
    }
    const real = await GetStationRealTimeDetail(
      state.curRow.stationId,
      state.curRadio
    );
    state.stationRealTimeData = real.data || [];
  }
};
defineExpose({
  refreshDetail
});
const resize = () => {
  Array.from({ length: 3 }).map((item, i) => {
    proxy.$refs['refChart' + (i + 1)]?.resize();
  });
};
onMounted(() => {
  emit('mounted');
  resizer.listenToMush(refChartDiv.value, resize);
});
</script>
<style lang="scss" scoped>
// .dark,
// .darkblue {
//   .one-map-detail {
//     .row1,
//     .row2 {
//       background-color: rgb(16, 39, 60);
//     }
//   }
// }
.one-map-detail {
  .row1 {
    background-color: var(--el-bg-color);
    height: 370px;
    align-items: center;
    padding: 8px 8px 0 8px;
    margin-bottom: 20px;
    .row-title {
      margin: 0;
    }
    .pie-charts {
      display: flex;
      height: 346px;
    }
  }
  .row2 {
    background-color: var(--el-bg-color);
    height: 480px;
  }
  .pie-chart {
    width: 33.33%;
    height: 260px;
  }
}

.detail-attrgrou-radio {
  // background-color: rgba(21, 45, 68, 1);
  padding: 0 12px;
  height: 40px;
  align-items: center;
  display: flex;
}
.detail-right {
  display: flex;
  width: 100%;
  height: calc(100% - 40px);
  .list-items {
    min-width: 260px;
    height: 100%;
    width: 260px;
    padding: 8px 12px;
    .list-item {
      width: 100%;
      height: 38px;
      color: var(--el-color-primary);
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid var(--el-color-primary);
      margin-bottom: 20px;
      padding: 8px;
      :last-child {
        margin-bottom: 0;
      }
    }
  }
  .chart-box {
    height: 100%;
    width: 100%;
  }
}
</style>
