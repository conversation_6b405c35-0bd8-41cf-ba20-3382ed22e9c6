package org.thingsboard.server.service.statistics;

import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.dataSource.JobService;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.RestApiEntity;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.tenant.TenantService;
import org.thingsboard.server.service.utils.SchedulerUtils;

import java.util.Collections;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 2020/5/26 9:43
 */
@Service
@Slf4j
public class JobServiceImpl implements JobService {

    @Autowired
    private TenantService tenantService;

    @Autowired
    private ProjectService projectService;

    /**
     * 更新触发器配置
     */
    @Override
    public void modifyTrigger(DataSourceEntity dataSourceEntity, String originatorId) {
        try {
            if(dataSourceEntity.getType().equalsIgnoreCase(DataSourceType.RESTAPI_SOURCE.name())){
                return;
            }
            GroupMatcher<JobKey> matcher = GroupMatcher.anyJobGroup();
            Set<JobKey> jobKeys = SchedulerUtils.getSchedulerFactory().getScheduler().getJobKeys(matcher);
            JobKey jobKey = new JobKey(dataSourceEntity.getId(), originatorId);
            if (!jobKeys.contains(jobKey)) {
                log.info("新加入配置，开启新的定时任务");
                SchedulerUtils.startScheduler(Collections.singletonList(dataSourceEntity), SchedulerUtils.getSchedulerFactory().getScheduler(),
                        originatorId, getEntityType(originatorId), true);
                SchedulerUtils.startScheduler(Collections.singletonList(dataSourceEntity), SchedulerUtils.getSchedulerFactory().getScheduler(),
                        originatorId, getEntityType(originatorId), false);
            } else {
                TriggerKey key = TriggerKey.triggerKey(dataSourceEntity.getId(), originatorId);
                CronTrigger newTrigger = (CronTrigger) TriggerBuilder.newTrigger()
                        .withIdentity(key)
                        .withSchedule(CronScheduleBuilder.cronSchedule(SchedulerUtils.getSchedulerCron(dataSourceEntity.getFrequency(), false)))
                        .build();
                log.info("原有配置，更新定时任务");
                SchedulerUtils.getSchedulerFactory().getScheduler().rescheduleJob(key, newTrigger);
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }


    @Override
    public void modifyRestApi(RestApiEntity restApiEntity) {
        try {
            GroupMatcher<JobKey> matcher = GroupMatcher.anyJobGroup();
            Set<JobKey> jobKeys = SchedulerUtils.getSchedulerFactory().getScheduler().getJobKeys(matcher);
            JobKey jobKey = new JobKey(restApiEntity.getName(), restApiEntity.getId());
            if (!jobKeys.contains(jobKey)) {
                log.info("新加入配置，开启新的定时任务");
                SchedulerUtils.processRestApi(restApiEntity,SchedulerUtils.getSchedulerFactory().getScheduler());
            } else {
                TriggerKey key = TriggerKey.triggerKey(restApiEntity.getName(), restApiEntity.getId());
                CronTrigger newTrigger = (CronTrigger) TriggerBuilder.newTrigger()
                        .withIdentity(key)
                        .withSchedule(CronScheduleBuilder.cronSchedule(SchedulerUtils.getSchedulerCron(restApiEntity.getFrequency(),true)))
                        .build();
                log.info("原有配置，更新定时任务");
                SchedulerUtils.getSchedulerFactory().getScheduler().rescheduleJob(key, newTrigger);
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除调度配置
     */
    @Override
    public void deleteTrigger(DataSourceEntity dataSourceEntity, String originatorId) {
        try {
            JobKey key = new JobKey(dataSourceEntity.getSourceName(), originatorId);
            log.info("删除原有定时任务");
            SchedulerUtils.getSchedulerFactory().getScheduler().deleteJob(key);
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void modifyOffline(Device device) {
        try {
            GroupMatcher<JobKey> matcher = GroupMatcher.anyJobGroup();
            Set<JobKey> jobKeys = SchedulerUtils.getSchedulerFactory().getScheduler().getJobKeys(matcher);
            JobKey jobKey = new JobKey(UUIDConverter.fromTimeUUID(device.getUuidId()), UUIDConverter.fromTimeUUID(device.getTenantId().getId()));
            if (!jobKeys.contains(jobKey)) {
                log.info("添加新的设备，开启新的掉线任务判断");
                SchedulerUtils.startScheduler(Collections.singletonList(device), SchedulerUtils.getSchedulerFactory().getScheduler());
            } else {
                TriggerKey key = TriggerKey.triggerKey(UUIDConverter.fromTimeUUID(device.getUuidId()), UUIDConverter.fromTimeUUID(device.getTenantId().getId()));
                CronTrigger newTrigger = (CronTrigger) TriggerBuilder.newTrigger()
                        .withIdentity(key)
                        .withSchedule(CronScheduleBuilder.cronSchedule(SchedulerUtils.getSchedulerCron(device.getOfflineInterval(), false)))
                        .build();
                log.info("更新设备配置，更新定时任务");
                SchedulerUtils.getSchedulerFactory().getScheduler().rescheduleJob(key, newTrigger);
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void deleteOffline(Device device) {
        try {
            JobKey key = new JobKey(UUIDConverter.fromTimeUUID(device.getUuidId()), UUIDConverter.fromTimeUUID(device.getTenantId().getId()));
            log.info("删除原有定时任务");
            SchedulerUtils.getSchedulerFactory().getScheduler().deleteJob(key);
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据entity获取类别
     *
     * @param entityId
     * @return
     */
    private String getEntityType(String entityId) {
        if (entityId.equalsIgnoreCase(DataConstants.ROOT)) {
            return DataConstants.ROOT;
        } else if (tenantService.findTenantById(new TenantId(UUIDConverter.fromString(entityId))) != null) {
            return DataConstants.TENANT;
        } else if (projectService.findById(entityId) != null) {
            return DataConstants.PROJECT;
        } else {
            return DataConstants.DEVICE;
        }
    }


}
