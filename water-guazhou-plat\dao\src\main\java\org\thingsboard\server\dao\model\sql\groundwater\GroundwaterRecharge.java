package org.thingsboard.server.dao.model.sql.groundwater;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.math.BigDecimal;

/**
 * 地下水涵养水位
 */
@Data
@TableName("tb_groundwater_recharge")
@ApiModel(value = "地下水涵养水位", description = "地下水涵养水位分析和建议")
public class GroundwaterRecharge {

    @TableId
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
    
    @ApiModelProperty(value = "区域ID")
    private String areaId;
    
    @ApiModelProperty(value = "区域名称")
    @TableField(exist = false)
    private String areaName;
    
    @ApiModelProperty(value = "分析周期开始时间")
    private Date startTime;
    
    @ApiModelProperty(value = "分析周期结束时间")
    private Date endTime;
    
    @ApiModelProperty(value = "期初水位(米)")
    private BigDecimal initialLevel;
    
    @ApiModelProperty(value = "期末水位(米)")
    private BigDecimal finalLevel;
    
    @ApiModelProperty(value = "水位变化量(米)")
    private BigDecimal levelChange;
    
    @ApiModelProperty(value = "补给量(立方米)")
    private BigDecimal rechargeAmount;
    
    @ApiModelProperty(value = "降雨量(毫米)")
    private BigDecimal rainfallAmount;
    
    @ApiModelProperty(value = "地下水总消耗量(立方米)")
    private BigDecimal consumptionAmount;
    
    @ApiModelProperty(value = "建议补给量(立方米)")
    private BigDecimal suggestedRechargeAmount;
    
    @ApiModelProperty(value = "涵养水位建议")
    private String rechargeSuggestion;
    
    @ApiModelProperty(value = "分析结果")
    private String analysisResult;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    @ApiModelProperty(value = "分析状态(1-良好,2-一般,3-不足)")
    private Integer status;
    
    @ApiModelProperty(value = "创建人")
    private String creator;
} 