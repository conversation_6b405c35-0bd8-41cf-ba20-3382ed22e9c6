package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDevice;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoDeviceSaveRequest extends SaveRequest<SoDevice> {
    // 设备编码
    @NotNullOrEmpty
    private String serialId;

    // 设备类别编码
    private String typeSerialId;

    // 名称
    @NotNullOrEmpty
    private String name;

    // 型号
    @NotNullOrEmpty
    private String model;

    // 单位
    private String unit;

    // 标识
    private String mark;

    // 排序编号
    private Integer orderNum;

    // 备注
    private String remark;

    @Override
    public String valid(IStarHttpRequest request) {
        if (serialId.length() != 14) {
            return "非法的设备序列号";
        }
        typeSerialId = serialId.substring(0, 8) + "000000";
        return super.valid(request);
    }

    @Override
    protected SoDevice build() {
        SoDevice entity = new SoDevice();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoDevice update(String id) {
        SoDevice entity = new SoDevice();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoDevice entity) {
        entity.setSerialId(serialId);
        entity.setTypeSerialId(typeSerialId);
        entity.setName(name);
        entity.setMark(mark);
        entity.setModel(model);
        entity.setUnit(unit);
        entity.setOrderNum(orderNum);
        entity.setRemark(remark);
    }
}