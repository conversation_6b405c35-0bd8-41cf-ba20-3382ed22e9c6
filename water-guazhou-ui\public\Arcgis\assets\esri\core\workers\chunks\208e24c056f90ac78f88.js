"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[1916],{36030:(e,t,r)=>{r.d(t,{J:()=>i});var n=r(35454),s=r(5600);function i(e,t={}){const r=e instanceof n.X?e:new n.X(e,t),i={type:t?.ignoreUnknown??1?r.apiValues:String,json:{type:r.jsonValues,read:!t?.readOnly&&{reader:r.read},write:{writer:r.write}}};return void 0!==t?.readOnly&&(i.readOnly=!!t.readOnly),void 0!==t?.default&&(i.json.default=t.default),void 0!==t?.name&&(i.json.name=t.name),void 0!==t?.nonNullable&&(i.nonNullable=t.nonNullable),(0,s.Cb)(i)}},43090:(e,t,r)=>{function n(e){return 32+e.length}function s(e){if(!e)return 0;let t=l;for(const r in e)if(e.hasOwnProperty(r)){const s=e[r];switch(typeof s){case"string":t+=n(s);break;case"number":t+=16;break;case"boolean":t+=4}}return t}function i(e){if(!e)return 0;if(Array.isArray(e))return function(e){const t=e.length;if(0===t||"number"==typeof e[0])return 32+8*t;let r=u;for(let n=0;n<t;n++)r+=o(e[n]);return r}(e);let t=l;for(const r in e)e.hasOwnProperty(r)&&(t+=o(e[r]));return t}function o(e){switch(typeof e){case"object":return i(e);case"string":return n(e);case"number":return 16;case"boolean":return 4;default:return 8}}function a(e,t){return u+e.length*t}r.d(t,{Ul:()=>i,Y8:()=>c,do:()=>a,f2:()=>s});const l=32,u=32;var c;!function(e){e[e.KILOBYTES=1024]="KILOBYTES",e[e.MEGABYTES=1048576]="MEGABYTES",e[e.GIGABYTES=1073741824]="GIGABYTES"}(c||(c={}))},74085:(e,t,r)=>{function n(e){}r.d(t,{Bg:()=>n}),r(80442)},45091:(e,t,r)=>{r.d(t,{Z:()=>c});var n,s,i=r(80442),o=r(71143);(s=n||(n={}))[s.varint=0]="varint",s[s.fixed64=1]="fixed64",s[s.delimited=2]="delimited",s[s.fixed32=5]="fixed32",s[s.unknown=99]="unknown";const a=4294967296,l=new TextDecoder("utf-8"),u=(0,i.Z)("safari")||(0,i.Z)("ios")?6:(0,i.Z)("ff")?12:32;class c{constructor(e,t,r=0,s=(e?e.byteLength:0)){this._tag=0,this._dataType=n.unknown,this._init(e,t,r,s)}_init(e,t,r,n){this._data=e,this._dataView=t,this._pos=r,this._end=n}asUnsafe(){return this}clone(){return new c(this._data,this._dataView,this._pos,this._end)}pos(){return this._pos}move(e){this._pos=e}nextTag(e){for(;;){if(this._pos===this._end)return!1;const t=this._decodeVarint();if(this._tag=t>>3,this._dataType=7&t,!e||e===this._tag)break;this.skip()}return!0}next(){if(this._pos===this._end)return!1;const e=this._decodeVarint();return this._tag=e>>3,this._dataType=7&e,!0}empty(){return this._pos>=this._end}tag(){return this._tag}getInt32(){return this._decodeVarint()}getInt64(){return this._decodeVarint()}getUInt32(){let e=4294967295;return e=(127&this._data[this._pos])>>>0,this._data[this._pos++]<128?e:(e=(e|(127&this._data[this._pos])<<7)>>>0,this._data[this._pos++]<128?e:(e=(e|(127&this._data[this._pos])<<14)>>>0,this._data[this._pos++]<128?e:(e=(e|(127&this._data[this._pos])<<21)>>>0,this._data[this._pos++]<128?e:(e=(e|(15&this._data[this._pos])<<28)>>>0,this._data[this._pos++]<128?e:void 0))))}getUInt64(){return this._decodeVarint()}getSInt32(){const e=this.getUInt32();if(void 0!==e)return e>>>1^-(1&e)|0}getSInt64(){return this._decodeSVarint()}getBool(){const e=0!==this._data[this._pos];return this._skip(1),e}getEnum(){return this._decodeVarint()}getFixed64(){const e=this._dataView,t=this._pos,r=e.getUint32(t,!0)+e.getUint32(t+4,!0)*a;return this._skip(8),r}getSFixed64(){const e=this._dataView,t=this._pos,r=e.getUint32(t,!0)+e.getInt32(t+4,!0)*a;return this._skip(8),r}getDouble(){const e=this._dataView.getFloat64(this._pos,!0);return this._skip(8),e}getFixed32(){const e=this._dataView.getUint32(this._pos,!0);return this._skip(4),e}getSFixed32(){const e=this._dataView.getInt32(this._pos,!0);return this._skip(4),e}getFloat(){const e=this._dataView.getFloat32(this._pos,!0);return this._skip(4),e}getString(){const e=this._getLength(),t=this._pos,r=this._toString(this._data,t,t+e);return this._skip(e),r}getBytes(){const e=this._getLength(),t=this._pos,r=this._toBytes(this._data,t,t+e);return this._skip(e),r}getLength(){return this._getLengthUnsafe()}processMessageWithArgs(e,t,r,n){const s=this.getMessage(),i=e(s,t,r,n);return s.release(),i}processMessage(e){const t=this.getMessage(),r=e(t);return t.release(),r}getMessage(){const e=this._getLength(),t=c.pool.acquire();return t._init(this._data,this._dataView,this._pos,this._pos+e),this._skip(e),t}release(){c.pool.release(this)}dataType(){return this._dataType}skip(){switch(this._dataType){case n.varint:this._decodeVarint();break;case n.fixed64:this._skip(8);break;case n.delimited:this._skip(this._getLength());break;case n.fixed32:this._skip(4);break;default:throw new Error("Invalid data type!")}}skipLen(e){this._skip(e)}_skip(e){if(this._pos+e>this._end)throw new Error("Attempt to skip past the end of buffer!");this._pos+=e}_decodeVarint(){const e=this._data;let t=this._pos,r=0,n=0;if(this._end-t>=10)do{if(n=e[t++],r|=127&n,0==(128&n))break;if(n=e[t++],r|=(127&n)<<7,0==(128&n))break;if(n=e[t++],r|=(127&n)<<14,0==(128&n))break;if(n=e[t++],r|=(127&n)<<21,0==(128&n))break;if(n=e[t++],r+=268435456*(127&n),0==(128&n))break;if(n=e[t++],r+=34359738368*(127&n),0==(128&n))break;if(n=e[t++],r+=4398046511104*(127&n),0==(128&n))break;if(n=e[t++],r+=562949953421312*(127&n),0==(128&n))break;if(n=e[t++],r+=72057594037927940*(127&n),0==(128&n))break;if(n=e[t++],r+=0x8000000000000000*(127&n),0==(128&n))break;throw new Error("Varint too long!")}while(0);else{let s=1;for(;t!==this._end&&(n=e[t],0!=(128&n));)++t,r+=(127&n)*s,s*=128;if(t===this._end)throw new Error("Varint overrun!");++t,r+=n*s}return this._pos=t,r}_decodeSVarint(){const e=this._data;let t=this._pos,r=0,n=0;const s=1&e[t];if(this._end-t>=10)do{if(n=e[t++],r|=127&n,0==(128&n))break;if(n=e[t++],r|=(127&n)<<7,0==(128&n))break;if(n=e[t++],r|=(127&n)<<14,0==(128&n))break;if(n=e[t++],r|=(127&n)<<21,0==(128&n))break;if(n=e[t++],r+=268435456*(127&n),0==(128&n))break;if(n=e[t++],r+=34359738368*(127&n),0==(128&n))break;if(n=e[t++],r+=4398046511104*(127&n),0==(128&n))break;if(n=e[t++],r+=562949953421312*(127&n),0==(128&n))break;if(n=e[t++],r+=72057594037927940*(127&n),0==(128&n))break;if(n=e[t++],r+=0x8000000000000000*(127&n),0==(128&n))break;throw new Error("Varint too long!")}while(0);else{let s=1;for(;t!==this._end&&(n=e[t],0!=(128&n));)++t,r+=(127&n)*s,s*=128;if(t===this._end)throw new Error("Varint overrun!");++t,r+=n*s}return this._pos=t,s?-(r+1)/2:r/2}_getLength(){if(this._dataType!==n.delimited)throw new Error("Not a delimited data type!");return this._decodeVarint()}_getLengthUnsafe(){return this.getUInt32()}_toString(e,t,r){if((r=Math.min(this._end,r))-t>u){const n=e.subarray(t,r);return l.decode(n)}let n="",s="";for(let i=t;i<r;++i){const t=e[i];128&t?s+="%"+t.toString(16):(n+=decodeURIComponent(s)+String.fromCharCode(t),s="")}return s.length&&(n+=decodeURIComponent(s)),n}_toBytes(e,t,r){return r=Math.min(this._end,r),new Uint8Array(e.buffer,t,r-t)}}c.pool=new o.Z(c,void 0,(e=>{e._data=null,e._dataView=null}))},33955:(e,t,r)=>{r.d(t,{Ji:()=>g,YX:()=>c,aW:()=>d,im:()=>f,l9:()=>h,oU:()=>y,q9:()=>_,wp:()=>p});var n=r(70586),s=r(6570),i=r(9361),o=r(65091),a=r(94139),l=r(38913),u=r(58901);function c(e){return void 0!==e.xmin&&void 0!==e.ymin&&void 0!==e.xmax&&void 0!==e.ymax}function d(e){return void 0!==e.points}function p(e){return void 0!==e.x&&void 0!==e.y}function h(e){return void 0!==e.paths}function y(e){return void 0!==e.rings}function f(e){return(0,n.Wi)(e)?null:e instanceof i.Z?e:p(e)?a.Z.fromJSON(e):h(e)?u.Z.fromJSON(e):y(e)?l.Z.fromJSON(e):d(e)?o.Z.fromJSON(e):c(e)?s.Z.fromJSON(e):null}function g(e){return e?p(e)?"esriGeometryPoint":h(e)?"esriGeometryPolyline":y(e)?"esriGeometryPolygon":c(e)?"esriGeometryEnvelope":d(e)?"esriGeometryMultipoint":null:null}const m={esriGeometryPoint:a.Z,esriGeometryPolyline:u.Z,esriGeometryPolygon:l.Z,esriGeometryEnvelope:s.Z,esriGeometryMultipoint:o.Z};function _(e){return e&&m[e]||null}},82397:(e,t,r)=>{r.d(t,{G6:()=>g,Ie:()=>f,J9:()=>h,RF:()=>p,U1:()=>y,vY:()=>a});var n=r(70586);r(33955);const s=(e,t,r)=>[t,r],i=(e,t,r)=>[t,r,e[2]],o=(e,t,r)=>[t,r,e[2],e[3]];function a(e){return e?{originPosition:"upper-left"===e.originPosition?"upperLeft":"lower-left"===e.originPosition?"lowerLeft":e.originPosition,scale:e.tolerance?[e.tolerance,e.tolerance]:[1,1],translate:(0,n.pC)(e.extent)?[e.extent.xmin,e.extent.ymax]:[0,0]}:null}function l({scale:e,translate:t},r){return r*e[0]+t[0]}function u({scale:e,translate:t},r){return t[1]-r*e[1]}function c(e,t,r){const n=new Array(r.length);if(!r.length)return n;const[s,i]=e.scale;let o=l(e,r[0][0]),a=u(e,r[0][1]);n[0]=t(r[0],o,a);for(let e=1;e<r.length;e++){const l=r[e];o+=l[0]*s,a-=l[1]*i,n[e]=t(l,o,a)}return n}function d(e,t,r){const n=new Array(r.length);for(let s=0;s<r.length;s++)n[s]=c(e,t,r[s]);return n}function p(e,t,r,n,s){return t.x=function({scale:e,translate:t},r){return Math.round((r-t[0])/e[0])}(e,r.x),t.y=function({scale:e,translate:t},r){return Math.round((t[1]-r)/e[1])}(e,r.y),t!==r&&(n&&(t.z=r.z),s&&(t.m=r.m)),t}function h(e,t,r,a,l){return(0,n.pC)(r)&&(t.points=function(e,t,r,n){return c(e,r?n?o:i:n?i:s,t)}(e,r.points,a,l)),t}function y(e,t,r,s,i){return(0,n.Wi)(r)||(t.x=l(e,r.x),t.y=u(e,r.y),t!==r&&(s&&(t.z=r.z),i&&(t.m=r.m))),t}function f(e,t,r,a,l){return(0,n.pC)(r)&&(t.rings=function(e,t,r,n){return d(e,r?n?o:i:n?i:s,t)}(e,r.rings,a,l)),t}function g(e,t,r,a,l){return(0,n.pC)(r)&&(t.paths=function(e,t,r,n){return d(e,r?n?o:i:n?i:s,t)}(e,r.paths,a,l)),t}},86973:(e,t,r)=>{r.d(t,{M:()=>s,P:()=>i});var n=r(35454);const s=(0,n.w)()({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon"}),i=(0,n.w)()({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh"})},69285:(e,t,r)=>{r.d(t,{k:()=>o});var n=r(70586),s=r(67900),i=r(8744);function o(e,t,r){if((0,n.Wi)(t)||(0,n.Wi)(r)||r.vcsWkid||(0,i.fS)(t,r))return null;const o=(0,s._R)(t)/(0,s._R)(r);if(1===o)return null;switch(e){case"point":case"esriGeometryPoint":return e=>function(e,t){e&&null!=e.z&&(e.z*=t)}(e,o);case"polyline":case"esriGeometryPolyline":return e=>function(e,t){if(e)for(const r of e.paths)for(const e of r)e.length>2&&(e[2]*=t)}(e,o);case"polygon":case"esriGeometryPolygon":return e=>function(e,t){if(e)for(const r of e.rings)for(const e of r)e.length>2&&(e[2]*=t)}(e,o);case"multipoint":case"esriGeometryMultipoint":return e=>function(e,t){if(e)for(const r of e.points)r.length>2&&(r[2]*=t)}(e,o);case"extent":case"esriGeometryExtent":return e=>function(e,t){e&&null!=e.zmin&&null!=e.zmax&&(e.zmin*=t,e.zmax*=t)}(e,o);default:return null}}},1231:(e,t,r)=>{r.d(t,{Z:()=>g});var n,s=r(43697),i=r(35454),o=r(96674),a=r(5600),l=r(75215),u=(r(67676),r(36030)),c=r(71715),d=r(52011),p=r(72729),h=r(86719);const y=new i.X({binary:"binary",coordinate:"coordinate",countOrAmount:"count-or-amount",dateAndTime:"date-and-time",description:"description",locationOrPlaceName:"location-or-place-name",measurement:"measurement",nameOrTitle:"name-or-title",none:"none",orderedOrRanked:"ordered-or-ranked",percentageOrRatio:"percentage-or-ratio",typeOrCategory:"type-or-category",uniqueIdentifier:"unique-identifier"});let f=n=class extends o.wq{constructor(e){super(e),this.alias=null,this.defaultValue=void 0,this.description=null,this.domain=null,this.editable=!0,this.length=-1,this.name=null,this.nullable=!0,this.type=null,this.valueType=null,this.visible=!0}readDescription(e,{description:t}){let r=null;try{r=t?JSON.parse(t):null}catch(e){}return r?.value??null}readValueType(e,{description:t}){let r=null;try{r=t?JSON.parse(t):null}catch(e){}return r?y.fromJSON(r.fieldValueType):null}clone(){return new n({alias:this.alias,defaultValue:this.defaultValue,description:this.description,domain:this.domain&&this.domain.clone()||null,editable:this.editable,length:this.length,name:this.name,nullable:this.nullable,type:this.type,valueType:this.valueType,visible:this.visible})}};(0,s._)([(0,a.Cb)({type:String,json:{write:!0}})],f.prototype,"alias",void 0),(0,s._)([(0,a.Cb)({type:[String,Number],json:{write:{allowNull:!0}}})],f.prototype,"defaultValue",void 0),(0,s._)([(0,a.Cb)()],f.prototype,"description",void 0),(0,s._)([(0,c.r)("description")],f.prototype,"readDescription",null),(0,s._)([(0,a.Cb)({types:p.V5,json:{read:{reader:p.im},write:!0}})],f.prototype,"domain",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],f.prototype,"editable",void 0),(0,s._)([(0,a.Cb)({type:l.z8,json:{write:!0}})],f.prototype,"length",void 0),(0,s._)([(0,a.Cb)({type:String,json:{write:!0}})],f.prototype,"name",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],f.prototype,"nullable",void 0),(0,s._)([(0,u.J)(h.v)],f.prototype,"type",void 0),(0,s._)([(0,a.Cb)()],f.prototype,"valueType",void 0),(0,s._)([(0,c.r)("valueType",["description"])],f.prototype,"readValueType",null),(0,s._)([(0,a.Cb)({type:Boolean,json:{read:!1}})],f.prototype,"visible",void 0),f=n=(0,s._)([(0,d.j)("esri.layers.support.Field")],f);const g=f},72729:(e,t,r)=>{r.d(t,{im:()=>x,V5:()=>v}),r(80442);var n,s=r(43697),i=r(22974),o=r(5600),a=(r(75215),r(36030)),l=r(52011),u=r(96674);r(67676);let c=n=class extends u.wq{constructor(e){super(e),this.name=null,this.code=null}clone(){return new n({name:this.name,code:this.code})}};(0,s._)([(0,o.Cb)({type:String,json:{write:!0}})],c.prototype,"name",void 0),(0,s._)([(0,o.Cb)({type:[String,Number],json:{write:!0}})],c.prototype,"code",void 0),c=n=(0,s._)([(0,l.j)("esri.layers.support.CodedValue")],c);const d=new(r(35454).X)({inherited:"inherited",codedValue:"coded-value",range:"range"});let p=class extends u.wq{constructor(e){super(e),this.name=null,this.type=null}};(0,s._)([(0,o.Cb)({type:String,json:{write:!0}})],p.prototype,"name",void 0),(0,s._)([(0,a.J)(d)],p.prototype,"type",void 0),p=(0,s._)([(0,l.j)("esri.layers.support.Domain")],p);const h=p;var y;let f=y=class extends h{constructor(e){super(e),this.codedValues=null,this.type="coded-value"}getName(e){let t=null;if(this.codedValues){const r=String(e);this.codedValues.some((e=>(String(e.code)===r&&(t=e.name),!!t)))}return t}clone(){return new y({codedValues:(0,i.d9)(this.codedValues),name:this.name})}};(0,s._)([(0,o.Cb)({type:[c],json:{write:!0}})],f.prototype,"codedValues",void 0),(0,s._)([(0,a.J)({codedValue:"coded-value"})],f.prototype,"type",void 0),f=y=(0,s._)([(0,l.j)("esri.layers.support.CodedValueDomain")],f);const g=f;var m;r(92604),r(20102);let _=m=class extends h{constructor(e){super(e),this.type="inherited"}clone(){return new m}};(0,s._)([(0,a.J)({inherited:"inherited"})],_.prototype,"type",void 0),_=m=(0,s._)([(0,l.j)("esri.layers.support.InheritedDomain")],_);const b=_;var k;let w=k=class extends h{constructor(e){super(e),this.maxValue=null,this.minValue=null,this.type="range"}clone(){return new k({maxValue:this.maxValue,minValue:this.minValue,name:this.name})}};(0,s._)([(0,o.Cb)({type:Number,json:{type:[Number],read:{source:"range",reader:(e,t)=>t.range&&t.range[1]},write:{enabled:!1,overridePolicy(){return{enabled:null!=this.maxValue&&null==this.minValue}},target:"range",writer(e,t,r){t[r]=[this.minValue||0,e]}}}})],w.prototype,"maxValue",void 0),(0,s._)([(0,o.Cb)({type:Number,json:{type:[Number],read:{source:"range",reader:(e,t)=>t.range&&t.range[0]},write:{target:"range",writer(e,t,r){t[r]=[e,this.maxValue||0]}}}})],w.prototype,"minValue",void 0),(0,s._)([(0,a.J)({range:"range"})],w.prototype,"type",void 0),w=k=(0,s._)([(0,l.j)("esri.layers.support.RangeDomain")],w);const T=w,v={key:"type",base:h,typeMap:{range:w,"coded-value":g,inherited:b}};function x(e){if(!e||!e.type)return null;switch(e.type){case"range":return T.fromJSON(e);case"codedValue":return g.fromJSON(e);case"inherited":return b.fromJSON(e)}return null}},86719:(e,t,r)=>{r.d(t,{v:()=>n});const n=new(r(35454).X)({esriFieldTypeSmallInteger:"small-integer",esriFieldTypeInteger:"integer",esriFieldTypeSingle:"single",esriFieldTypeDouble:"double",esriFieldTypeLong:"long",esriFieldTypeString:"string",esriFieldTypeDate:"date",esriFieldTypeOID:"oid",esriFieldTypeGeometry:"geometry",esriFieldTypeBlob:"blob",esriFieldTypeRaster:"raster",esriFieldTypeGUID:"guid",esriFieldTypeGlobalID:"global-id",esriFieldTypeXML:"xml"})},77863:(e,t,r)=>{r.d(t,{G$:()=>x,K9:()=>P,O7:()=>d});var n=r(20102),s=r(70586),i=r(45091),o=r(5428),a=r(78760);const l=["esriFieldTypeSmallInteger","esriFieldTypeInteger","esriFieldTypeSingle","esriFieldTypeDouble","esriFieldTypeString","esriFieldTypeDate","esriFieldTypeOID","esriFieldTypeGeometry","esriFieldTypeBlob","esriFieldTypeRaster","esriFieldTypeGUID","esriFieldTypeGlobalID","esriFieldTypeXML"],u=["sqlTypeBigInt","sqlTypeBinary","sqlTypeBit","sqlTypeChar","sqlTypeDate","sqlTypeDecimal","sqlTypeDouble","sqlTypeFloat","sqlTypeGeometry","sqlTypeGUID","sqlTypeInteger","sqlTypeLongNVarchar","sqlTypeLongVarbinary","sqlTypeLongVarchar","sqlTypeNChar","sqlTypeNVarchar","sqlTypeOther","sqlTypeReal","sqlTypeSmallInt","sqlTypeSqlXml","sqlTypeTime","sqlTypeTimestamp","sqlTypeTimestamp2","sqlTypeTinyInt","sqlTypeVarbinary","sqlTypeVarchar"],c=["upperLeft","lowerLeft"];function d(e){return e>=l.length?null:l[e]}function p(e){return e>=u.length?null:u[e]}function h(e){return e>=c.length?null:c[e]}function y(e,t){return t>=e.geometryTypes.length?null:e.geometryTypes[t]}function f(e,t,r){const n=e.asUnsafe(),s=t.createPointGeometry(r);for(;n.next();)switch(n.tag()){case 3:{const e=n.getUInt32(),r=n.pos()+e;let i=0;for(;n.pos()<r;)t.addCoordinatePoint(s,n.getSInt64(),i++);break}default:n.skip()}return s}function g(e,t,r){const n=e.asUnsafe(),s=t.createGeometry(r),i=2+(r.hasZ?1:0)+(r.hasM?1:0);for(;n.next();)switch(n.tag()){case 2:{const e=n.getUInt32(),r=n.pos()+e;let i=0;for(;n.pos()<r;)t.addLength(s,n.getUInt32(),i++);break}case 3:{const e=n.getUInt32(),r=n.pos()+e;let o=0;for(t.allocateCoordinates(s);n.pos()<r;)t.addCoordinate(s,n.getSInt64(),o),o++,o===i&&(o=0);break}default:n.skip()}return s}function m(e){const t=e.asUnsafe(),r=new o.Z;let n="esriGeometryPoint";for(;t.next();)switch(t.tag()){case 2:{const e=t.getUInt32(),n=t.pos()+e;for(;t.pos()<n;)r.lengths.push(t.getUInt32());break}case 3:{const e=t.getUInt32(),n=t.pos()+e;for(;t.pos()<n;)r.coords.push(t.getSInt64());break}case 1:n=a.A[t.getEnum()];break;default:t.skip()}return{queryGeometry:r,queryGeometryType:n}}function _(e){const t=e.asUnsafe();for(;t.next();)switch(t.tag()){case 1:return t.getString();case 2:return t.getFloat();case 3:return t.getDouble();case 4:return t.getSInt32();case 5:return t.getUInt32();case 6:return t.getInt64();case 7:return t.getUInt64();case 8:return t.getSInt64();case 9:return t.getBool();default:return t.skip(),null}return null}function b(e){const t=e.asUnsafe(),r={type:d(0)};for(;t.next();)switch(t.tag()){case 1:r.name=t.getString();break;case 2:r.type=d(t.getEnum());break;case 3:r.alias=t.getString();break;case 4:r.sqlType=p(t.getEnum());break;case 5:default:t.skip();break;case 6:r.defaultValue=t.getString()}return r}function k(e){const t={},r=e.asUnsafe();for(;r.next();)switch(r.tag()){case 1:t.name=r.getString();break;case 2:t.isSystemMaintained=r.getBool();break;default:r.skip()}return t}function w(e,t,r,n){const s=t.createFeature(r);let i=0;for(;e.next();)switch(e.tag()){case 1:{const t=n[i++].name;s.attributes[t]=e.processMessage(_);break}case 2:s.geometry=e.processMessageWithArgs(g,t,r);break;case 4:s.centroid=e.processMessageWithArgs(f,t,r);break;default:e.skip()}return s}function T(e){const t=[1,1,1,1],r=e.asUnsafe();for(;r.next();)switch(r.tag()){case 1:t[0]=r.getDouble();break;case 2:t[1]=r.getDouble();break;case 4:t[2]=r.getDouble();break;case 3:t[3]=r.getDouble();break;default:r.skip()}return t}function v(e){const t=[0,0,0,0],r=e.asUnsafe();for(;r.next();)switch(r.tag()){case 1:t[0]=r.getDouble();break;case 2:t[1]=r.getDouble();break;case 4:t[2]=r.getDouble();break;case 3:t[3]=r.getDouble();break;default:r.skip()}return t}function x(e){const t={originPosition:h(0)},r=e.asUnsafe();for(;r.next();)switch(r.tag()){case 1:t.originPosition=h(r.getEnum());break;case 2:t.scale=r.processMessage(T);break;case 3:t.translate=r.processMessage(v);break;default:r.skip()}return t}function G(e){const t={},r=e.asUnsafe();for(;r.next();)switch(r.tag()){case 1:t.shapeAreaFieldName=r.getString();break;case 2:t.shapeLengthFieldName=r.getString();break;case 3:t.units=r.getString();break;default:r.skip()}return t}function S(e,t){const r=t.createSpatialReference();for(;e.next();)switch(e.tag()){case 1:r.wkid=e.getUInt32();break;case 5:r.wkt=e.getString();break;case 2:r.latestWkid=e.getUInt32();break;case 3:r.vcsWkid=e.getUInt32();break;case 4:r.latestVcsWkid=e.getUInt32();break;default:e.skip()}return r}function C(e,t){const r=t.createFeatureResult(),n=e.asUnsafe();r.geometryType=y(t,0);let s=!1;for(;n.next();)switch(n.tag()){case 1:r.objectIdFieldName=n.getString();break;case 3:r.globalIdFieldName=n.getString();break;case 4:r.geohashFieldName=n.getString();break;case 5:r.geometryProperties=n.processMessage(G);break;case 7:r.geometryType=y(t,n.getEnum());break;case 8:r.spatialReference=n.processMessageWithArgs(S,t);break;case 10:r.hasZ=n.getBool();break;case 11:r.hasM=n.getBool();break;case 12:r.transform=n.processMessage(x);break;case 9:{const e=n.getBool();r.exceededTransferLimit=e;break}case 13:t.addField(r,n.processMessage(b));break;case 15:s||(t.prepareFeatures(r),s=!0),t.addFeature(r,n.processMessageWithArgs(w,t,r,r.fields));break;case 2:r.uniqueIdField=n.processMessage(k);break;default:n.skip()}return t.finishFeatureResult(r),r}function F(e,t){const r={};let n=null;for(;e.next();)switch(e.tag()){case 4:n=e.processMessageWithArgs(m);break;case 1:r.featureResult=e.processMessageWithArgs(C,t);break;default:e.skip()}return(0,s.pC)(n)&&r.featureResult&&t.addQueryGeometry(r,n),r}function P(e,t){try{const r=2,n=new i.Z(new Uint8Array(e),new DataView(e)),s={};for(;n.next();)n.tag()===r?s.queryResult=n.processMessageWithArgs(F,t):n.skip();return s}catch(e){throw new n.Z("query:parsing-pbf","Error while parsing FeatureSet PBF payload",{error:e})}}},78760:(e,t,r)=>{r.d(t,{A:()=>l,J:()=>u});var n=r(67900),s=r(8744),i=r(70272),o=r(44876),a=r(5428);const l=["esriGeometryPoint","esriGeometryMultipoint","esriGeometryPolyline","esriGeometryPolygon"];class u{constructor(e){this._options=e,this.geometryTypes=l,this._coordinatePtr=0,this._vertexDimension=0}createFeatureResult(){return new o.Z}prepareFeatures(e){this._vertexDimension=2,e.hasZ&&this._vertexDimension++,e.hasM&&this._vertexDimension++}finishFeatureResult(e){if(!e||!e.features||!e.hasZ||!this._options.sourceSpatialReference||!e.spatialReference||(0,s.fS)(e.spatialReference,this._options.sourceSpatialReference)||e.spatialReference.vcsWkid)return;const t=(0,n._R)(this._options.sourceSpatialReference)/(0,n._R)(e.spatialReference);if(1!==t)for(const r of e.features){if(!(0,i.S6)(r))continue;const e=r.geometry.coords;for(let r=2;r<e.length;r+=3)e[r]*=t}}addFeature(e,t){e.features.push(t)}createFeature(){return new i.u_}createSpatialReference(){return{wkid:0}}createGeometry(){return new a.Z}addField(e,t){e.fields.push(t)}allocateCoordinates(e){e.coords.length=e.lengths.reduce(((e,t)=>e+t),0)*this._vertexDimension,this._coordinatePtr=0}addCoordinate(e,t){e.coords[this._coordinatePtr++]=t}addCoordinatePoint(e,t){e.coords.push(t)}addLength(e,t){e.lengths.push(t)}addQueryGeometry(e,t){e.queryGeometry=t.queryGeometry,e.queryGeometryType=t.queryGeometryType}createPointGeometry(){return new a.Z}}},45178:(e,t,r)=>{r.d(t,{C:()=>s});var n=r(77863);function s(e,t){const r=(0,n.K9)(e,t),s=r.queryResult.featureResult,i=r.queryResult.queryGeometry,o=r.queryResult.queryGeometryType;if(s&&s.features&&s.features.length&&s.objectIdFieldName){const e=s.objectIdFieldName;for(const t of s.features)t.attributes&&(t.objectId=t.attributes[e])}return s&&(s.queryGeometry=i,s.queryGeometryType=o),s}},61916:(e,t,r)=>{r.r(t),r.d(t,{default:()=>w});var n=r(74085),s=r(70586),i=r(99001),o=r(82971),a=r(69285),l=(r(43090),r(80442),r(60437),r(24470),r(82397),r(86973),r(1231));r(8744);class u{constructor(e,t,r){this.uid=e,this.geometry=t,this.attributes=r,this.visible=!0,this.objectId=null,this.centroid=null}}class c{constructor(){this.exceededTransferLimit=!1,this.features=[],this.fields=[],this.hasM=!1,this.hasZ=!1,this.geometryType=null,this.objectIdFieldName=null,this.globalIdFieldName=null,this.geometryProperties=null,this.geohashFieldName=null,this.spatialReference=null,this.transform=null}}var d=r(98732);function p(e,t){return t}function h(e,t,r,n){switch(r){case 0:return m(e,t+n,0);case 1:return"lowerLeft"===e.originPosition?m(e,t+n,1):function({translate:e,scale:t},r,n){return e[n]-r*t[n]}(e,t+n,1)}}function y(e,t,r,n){return 2===r?m(e,t,2):h(e,t,r,n)}function f(e,t,r,n){return 2===r?m(e,t,3):h(e,t,r,n)}function g(e,t,r,n){return 3===r?m(e,t,3):y(e,t,r,n)}function m({translate:e,scale:t},r,n){return e[n]+r*t[n]}class _{constructor(e){this._options=e,this.geometryTypes=["point","multipoint","polyline","polygon"],this._previousCoordinate=[0,0],this._transform=null,this._applyTransform=p,this._lengths=[],this._currentLengthIndex=0,this._toAddInCurrentPath=0,this._vertexDimension=0,this._coordinateBuffer=null,this._coordinateBufferPtr=0,this._attributesConstructor=class{}}createFeatureResult(){return new c}finishFeatureResult(e){if(this._options.applyTransform&&(e.transform=null),this._attributesConstructor=class{},this._coordinateBuffer=null,this._lengths.length=0,!e.hasZ)return;const t=(0,a.k)(e.geometryType,this._options.sourceSpatialReference,e.spatialReference);if(!(0,s.Wi)(t))for(const r of e.features)t(r.geometry)}createSpatialReference(){return new o.Z}addField(e,t){e.fields.push(l.Z.fromJSON(t));const r=e.fields.map((e=>e.name));this._attributesConstructor=function(){for(const e of r)this[e]=null}}addFeature(e,t){const r=this._options.maxStringAttributeLength?this._options.maxStringAttributeLength:0;if(r>0)for(const e in t.attributes){const n=t.attributes[e];"string"==typeof n&&n.length>r&&(t.attributes[e]="")}e.features.push(t)}addQueryGeometry(e,t){const{queryGeometry:r,queryGeometryType:n}=t,s=(0,d.$g)(r.clone(),r,!1,!1,this._transform),i=(0,d.di)(s,n,!1,!1);let o=null;switch(n){case"esriGeometryPoint":o="point";break;case"esriGeometryPolygon":o="polygon";break;case"esriGeometryPolyline":o="polyline";break;case"esriGeometryMultipoint":o="multipoint"}i.type=o,e.queryGeometryType=n,e.queryGeometry=i}prepareFeatures(e){switch(this._transform=e.transform??null,this._options.applyTransform&&e.transform&&(this._applyTransform=this._deriveApplyTransform(e)),this._vertexDimension=2,e.hasZ&&this._vertexDimension++,e.hasM&&this._vertexDimension++,e.geometryType){case"point":this.addCoordinate=(e,t,r)=>this.addCoordinatePoint(e,t,r),this.createGeometry=e=>this.createPointGeometry(e);break;case"polygon":this.addCoordinate=(e,t,r)=>this._addCoordinatePolygon(e,t,r),this.createGeometry=e=>this._createPolygonGeometry(e);break;case"polyline":this.addCoordinate=(e,t,r)=>this._addCoordinatePolyline(e,t,r),this.createGeometry=e=>this._createPolylineGeometry(e);break;case"multipoint":this.addCoordinate=(e,t,r)=>this._addCoordinateMultipoint(e,t,r),this.createGeometry=e=>this._createMultipointGeometry(e);break;case"mesh":case"extent":break;default:(0,n.Bg)(e.geometryType)}}createFeature(){return this._lengths.length=0,this._currentLengthIndex=0,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0,new u((0,i.D)(),null,new this._attributesConstructor)}allocateCoordinates(){const e=this._lengths.reduce(((e,t)=>e+t),0);this._coordinateBuffer=new Float64Array(e*this._vertexDimension),this._coordinateBufferPtr=0}addLength(e,t){0===this._lengths.length&&(this._toAddInCurrentPath=t),this._lengths.push(t)}createPointGeometry(e){const t={type:"point",x:0,y:0,spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM};return t.hasZ&&(t.z=0),t.hasM&&(t.m=0),t}addCoordinatePoint(e,t,r){const n=this._transform?this._applyTransform(this._transform,t,r,0):t;if(null!=n)switch(r){case 0:e.x=n;break;case 1:e.y=n;break;case 2:e.hasZ?e.z=n:e.m=n;break;case 3:e.m=n}}_transformPathLikeValue(e,t){let r=0;return t<=1&&(r=this._previousCoordinate[t],this._previousCoordinate[t]+=e),this._transform?this._applyTransform(this._transform,e,t,r):e}_addCoordinatePolyline(e,t,r){this._dehydratedAddPointsCoordinate(e.paths,t,r)}_addCoordinatePolygon(e,t,r){this._dehydratedAddPointsCoordinate(e.rings,t,r)}_addCoordinateMultipoint(e,t,r){0===r&&e.points.push([]);const n=this._transformPathLikeValue(t,r);e.points[e.points.length-1].push(n)}_createPolygonGeometry(e){return{type:"polygon",rings:[[]],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_createPolylineGeometry(e){return{type:"polyline",paths:[[]],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_createMultipointGeometry(e){return{type:"multipoint",points:[],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_dehydratedAddPointsCoordinate(e,t,r){0===r&&0==this._toAddInCurrentPath--&&(e.push([]),this._toAddInCurrentPath=this._lengths[++this._currentLengthIndex]-1,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0);const n=this._transformPathLikeValue(t,r),s=e[e.length-1],i=this._coordinateBuffer;if(i){if(0===r){const e=this._coordinateBufferPtr*Float64Array.BYTES_PER_ELEMENT;s.push(new Float64Array(i.buffer,e,this._vertexDimension))}i[this._coordinateBufferPtr++]=n}}_deriveApplyTransform(e){const{hasZ:t,hasM:r}=e;return t&&r?g:t?y:r?f:h}}var b=r(45178);class k{_parseFeatureQuery(e){const t=(0,b.C)(e.buffer,new _(e.options)),r={...t,spatialReference:t.spatialReference?.toJSON(),fields:t.fields?t.fields.map((e=>e.toJSON())):void 0};return Promise.resolve(r)}}function w(){return new k}}}]);