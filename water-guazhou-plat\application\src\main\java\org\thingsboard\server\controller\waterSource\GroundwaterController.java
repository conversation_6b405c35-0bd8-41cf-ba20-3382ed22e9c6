package org.thingsboard.server.controller.waterSource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.groundwater.GroundwaterService;
import org.thingsboard.server.dao.model.sql.groundwater.GroundwaterLevel;
import org.thingsboard.server.dao.model.sql.groundwater.GroundwaterRecharge;
import org.thingsboard.server.dao.model.request.GroundwaterLevelRequest;
import org.thingsboard.server.dao.model.request.GroundwaterRechargeRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 地下水涵养水位控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/groundwater")
@Api(tags = "地下水涵养水位")
public class GroundwaterController extends BaseController {

    @Autowired
    private GroundwaterService groundwaterService;

    @ApiOperation(value = "保存地下水水位数据")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @PostMapping("/level")
    public IstarResponse saveWaterLevel(@RequestBody GroundwaterLevel entity) {
        try {
            entity.setTenantId(getCurrentUser().getTenantId().toString());
            GroundwaterLevel result = groundwaterService.saveWaterLevel(entity);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("保存地下水水位数据失败", e);
            return IstarResponse.error("保存地下水水位数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询地下水水位数据")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @GetMapping("/level/list")
    public IstarResponse findWaterLevelList(@RequestParam Map<String, Object> params) {
        try {
            params.put("tenantId", getCurrentUser().getTenantId().toString());
            PageData<GroundwaterLevel> pageData = groundwaterService.findWaterLevelList(params);
            return IstarResponse.ok(pageData);
        } catch (Exception e) {
            log.error("查询地下水水位数据失败", e);
            return IstarResponse.error("查询地下水水位数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除地下水水位数据")
//    @PreAuthorize("hasAuthority('TENANT_ADMIN')")
    @DeleteMapping("/level")
    public IstarResponse removeWaterLevel(@RequestBody List<String> ids) {
        try {
            groundwaterService.removeWaterLevel(ids);
            return IstarResponse.ok();
        } catch (Exception e) {
            log.error("删除地下水水位数据失败", e);
            return IstarResponse.error("删除地下水水位数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取地下水水位详情")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @GetMapping("/level/{id}")
    public IstarResponse getWaterLevelById(@PathVariable String id) {
        try {
            GroundwaterLevel result = groundwaterService.getWaterLevelById(id);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("获取地下水水位详情失败", e);
            return IstarResponse.error("获取地下水水位详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新地下水水位数据")
//    @PreAuthorize("hasAuthority('TENANT_ADMIN')")
    @PostMapping("/level/update")
    public IstarResponse updateWaterLevel(@RequestBody GroundwaterLevel entity) {
        try {
//            entity.setTenantId(getCurrentUser().getTenantId().toString());
            GroundwaterLevel result = groundwaterService.updateWaterLevel(entity);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("更新地下水水位数据失败", e);
            return IstarResponse.error("更新地下水水位数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "查询地下水水位变化数据")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @GetMapping("/level/change")
    public IstarResponse getWaterLevelChangeData(
            @ApiParam(value = "测点ID") @RequestParam String stationId,
            @ApiParam(value = "开始时间") @RequestParam Long startTime,
            @ApiParam(value = "结束时间") @RequestParam Long endTime) {
        try {
            List<GroundwaterLevel> data = groundwaterService.getWaterLevelChangeData(
                    stationId,
                    new Date(startTime),
                    new Date(endTime)
            );
            return IstarResponse.ok(data);
        } catch (Exception e) {
            log.error("查询地下水水位变化数据失败", e);
            return IstarResponse.error("查询地下水水位变化数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "保存地下水涵养分析")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @PostMapping("/recharge")
    public IstarResponse saveRechargeAnalysis(@RequestBody GroundwaterRecharge entity) {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getCurrentUser().getTenantId().getId());
            entity.setTenantId(tenantId);
            entity.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getId().getId()));
            GroundwaterRecharge result = groundwaterService.saveRechargeAnalysis(entity);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("保存地下水涵养分析失败", e);
            return IstarResponse.error("保存地下水涵养分析失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询地下水涵养分析")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @GetMapping("/recharge/list")
    public IstarResponse findRechargeList(GroundwaterRechargeRequest request) {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getCurrentUser().getTenantId().getId());
            PageData<GroundwaterRecharge> pageData = groundwaterService.findRechargeList(request, tenantId);
            return IstarResponse.ok(pageData);
        } catch (Exception e) {
            log.error("查询地下水涵养分析数据失败", e);
            return IstarResponse.error("查询地下水涵养分析数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除地下水涵养分析")
//    @PreAuthorize("hasAuthority('TENANT_ADMIN')")
    @DeleteMapping("/recharge")
    public IstarResponse removeRechargeAnalysis(@RequestBody List<String> ids) {
        try {
            groundwaterService.removeRechargeAnalysis(ids);
            return IstarResponse.ok();
        } catch (Exception e) {
            log.error("删除地下水涵养分析数据失败", e);
            return IstarResponse.error("删除地下水涵养分析数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分析地下水涵养水位")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @GetMapping("/recharge/analyze")
    public IstarResponse analyzeGroundwaterRecharge(
            @ApiParam(value = "区域ID") @RequestParam String areaId,
            @ApiParam(value = "开始时间") @RequestParam Long startTime,
            @ApiParam(value = "结束时间") @RequestParam Long endTime) {
        try {
            GroundwaterRecharge result = groundwaterService.analyzeGroundwaterRecharge(
                    areaId,
                    new Date(startTime),
                    new Date(endTime)
            );
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("分析地下水涵养水位失败", e);
            return IstarResponse.error("分析地下水涵养水位失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取地下水水位统计数据")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER', 'SYS_ADMIN')")
    @GetMapping("/level/statistics")
    public IstarResponse getWaterLevelStatistics() {
        try {
            Map<String, Object> statistics = groundwaterService.getWaterLevelStatistics();
            return IstarResponse.ok(statistics);
        } catch (Exception e) {
            log.error("获取地下水水位统计数据失败", e);
            return IstarResponse.error("获取地下水水位统计数据失败: " + e.getMessage());
        }
    }
}