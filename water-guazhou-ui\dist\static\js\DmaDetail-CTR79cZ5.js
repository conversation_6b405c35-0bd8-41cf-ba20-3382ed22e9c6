import{d as V,cN as k,c as B,r as I,o as N,ay as O,g as f,n as h,bo as S,i as v,dy as b,p as _,q as w,br as A,C as E}from"./index-r0dFAfgr.js";import{h as R,b as i}from"./chart-wy3NEK2T.js";import{e as T}from"./onemap-CEunQziB.js";import{p as u}from"./padStart-BKfyZZDO.js";const X={class:"one-map-detail"},q={class:"left"},G={class:"right"},M={style:{width:"100%",height:"100%"}},U=V({__name:"DmaDetail",emits:["refresh","mounted"],setup($,{expose:x,emit:y}){const c=y,{proxy:D}=k(),g=R(),r=B(),o=I({lineChartOption1:i(Array.from({length:24}).map((a,t)=>u(t.toString(),2,"0")),[],{}),lineChartOption2:i(Array.from({length:30}).map((a,t)=>u(t.toString(),2,"0")),[],{}),stationRealTimeData:[],detailLoading:!1});x({refreshDetail:async a=>{c("refresh",{title:a.name}),o.detailLoading=!0,o.curRow=a,Array.from({length:2}).map((t,n)=>{var s;(s=D.$refs["refChart"+n])==null||s.resize()}),T({stationId:a.stationId}).then(t=>{var d,l,m,p;const n=((d=t.data.data.todayData)==null?void 0:d.map(e=>e.value))||[],s=((l=t.data.data.todayData)==null?void 0:l.map(e=>e.ts))||[];o.lineChartOption1=i(s,n,{unit:"m³",name:"日供水量",color1:"#ff0000"});const L=((m=t.data.data.monthData)==null?void 0:m.map(e=>e.value))||[],z=((p=t.data.data.monthData)==null?void 0:p.map(e=>e.ts))||[];o.lineChartOption2=i(L,z,{unit:"m³",name:"月供水量",color1:"#ff0000"})}).finally(()=>{o.detailLoading=!1})}}),N(()=>{c("mounted"),setTimeout(()=>{var a;window.addEventListener("resize",C),(a=r.value)==null||a.resize()},3e3)});function C(){var a;(a=r.value)==null||a.resize()}return(a,t)=>{const n=O("VChart"),s=A;return f(),h("div",X,[S((f(),h("div",q,t[0]||(t[0]=[b('<div class="flex" data-v-c1c349aa><span data-v-c1c349aa>区域总水量<text style="color:#42a0ff;" data-v-c1c349aa>0</text>m³</span><span data-v-c1c349aa>区域售水量<text style="color:#63c63a;" data-v-c1c349aa>0</text>m³/h</span></div><div class="flex" data-v-c1c349aa><span data-v-c1c349aa>夜间最小流量<text data-v-c1c349aa>0</text>m³/h</span><span data-v-c1c349aa> 挂接用户 <text data-v-c1c349aa>0</text> 户 </span><span data-v-c1c349aa>大用户数<text data-v-c1c349aa>0</text>户</span><span data-v-c1c349aa>大用户水量占比数<text data-v-c1c349aa>0</text>%</span></div>',2)]))),[[s,v(o).detailLoading]]),_("div",G,[_("div",M,[w(n,{ref_key:"refChart2",ref:r,autoresize:"",theme:"dark",option:v(g)},null,8,["option"])])])])}}}),P=E(U,[["__scopeId","data-v-c1c349aa"]]);export{P as default};
