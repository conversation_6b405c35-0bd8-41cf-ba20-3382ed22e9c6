package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionPipe;
import org.thingsboard.server.dao.sql.smartPipe.PartitionPipeMapper;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PartitionPipeServiceImpl implements PartitionPipeService {

    @Autowired
    private PartitionPipeMapper partitionPipeMapper;

    @Override
    public PartitionPipe save(PartitionPipe partitionPipe) {
        if (StringUtils.isBlank(partitionPipe.getId())) {
            partitionPipe.setCreateTime(new Date());
            partitionPipeMapper.insert(partitionPipe);
        } else {
            partitionPipeMapper.updateById(partitionPipe);
        }
        return partitionPipe;
    }


    @Override
    public PageData<PartitionPipe> getList(PartitionMountRequest request) {
        IPage<PartitionPipe> page = new Page<>(request.getPage(), request.getSize());
        IPage<PartitionPipe> result = partitionPipeMapper.getList(page, request);
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        partitionPipeMapper.deleteBatchIds(ids);
    }

}
