const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/CIMSymbolRasterizer-CqVpHbJI.js","static/js/MapView-DaoQedLH.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/Point-WxyopZva.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js","static/js/cimAnalyzer-CMgqZsaO.js","static/js/fontUtils-BuXIMW9g.js","static/js/BidiEngine-CsUYIMdL.js","static/js/GeometryUtils-B7ExOJII.js","static/js/enums-B5k73o5q.js","static/js/alignmentUtils-CkNI7z7C.js","static/js/definitions-826PWLuy.js","static/js/number-CoJp78Rz.js","static/js/Rect-CUzevAry.js","static/js/callExpressionWithFeature-DgtD4TSq.js","static/js/quantizationUtils-DtI9CsYu.js","static/js/floatRGBA-PQQNbO39.js","static/js/CIMResourceManager-FPmRy-nM.js","static/js/Rasterizer-CuAuGNQK.js","static/js/_commonjsHelpers-DCkdB7M8.js","static/js/rasterizingUtils-BGZonnNf.js","static/js/imageutils-KgbVacIV.js"])))=>i.map(i=>d[i]);
import{e as y,y as v,a as Xe,Q as ds,S as Oe,b as Cc,v as Ht,j as Xi,i as di,ap as Pc,E as _l,s as We,as as bl,Z as wc,am as vl,aY as ts,a3 as Oc,$ as Ec,aa as Rc,M as us,J as ca,u as Ac,aZ as Ic,bw as Tc,aA as Lc,w as Dc,a1 as $c,T as Gc}from"./Point-WxyopZva.js";import{T as b,R as d,aO as se,$ as pe,aX as Mc,hH as ps,a3 as wr,aY as ie,a5 as Ds,eS as Sl,b2 as $s,aW as K,hJ as pi,a4 as da,eR as gi,aZ as zc,a_ as wn,cl as On}from"./index-r0dFAfgr.js";import{B as xl,c as gs,l as $e,f as Cl,a as Vc,w as Pl,U as wl,k as Uc}from"./widget-BcWKanF2.js";import{l7 as Fc,l8 as Nc,l9 as Ol,l as ue,la as Ji,gG as Bc,eY as jc,lb as kc,eX as Hc,lc as Wc,eW as qc,ld as Yc,le as Zc,lf as Xc,lg as Jc,lh as Qc,bQ as _e,kR as Kc,ao as ed,an as td,jk as za,aR as D,li as Ie,be as hi,af as C,bd as ut,aO as de,je as id,jf as rd,a_ as ge,aQ as me,lj as El,k as B,kL as dt,lk as Rl,e7 as is,ll as Al,g_ as si,d0 as tt,dC as or,lm as Va,ln as sd,lo as ad,e0 as Ee,aA as be,bm as Ut,dK as mi,bP as Ua,cx as Re,lp as Il,gl as ms,lq as Tl,c8 as Ll,A as pt,lr as nd,ls as rs,v as Fa,ca as od,ge as ld,aP as ae,f_ as zi,lt as Dl,bT as lr,lu as hd,lv as cd,lw as Na,w as Ba,hI as xt,aY as ja,bi as dd,kJ as Ve,lx as it,ae as ne,aW as oe,ac as Et,ly as ud,lz as pd,b1 as hr,gO as V,kX as $l,bf as ka,l6 as gd,l0 as md,lA as fd,kU as yd,ex as Ha,eO as Gl,hr as Wa,dP as En,lB as _d,fs as Ml,gS as bd,fz as vd,lC as Sd,a0 as xd,ag as Ft,fT as Li,cF as ua,jr as Cd,a1 as ss,lD as Pd,lE as ri,lF as wd,ab as k,kK as Od,bg as zl,as as Vl,g6 as cr,gL as Ed,lG as Rd,jv as Or,c9 as qa,ct as Ad,cr as $r,cs as Gs,aT as dr,ji as Qi,hJ as Id,$ as Td,a$ as Ld,fV as as,lH as Rn,fY as Dd,fZ as pa,gV as Ul,jt as $d,jG as Gr,bO as Fl,g5 as Gd,jp as Md,aV as An,lI as Ms,lJ as zd,ea as In,D as Nl,j5 as Vd,a3 as Ud,gP as Fd,c3 as Bl,r as Nd,kf as Tn,lK as Bd,jw as Ln,lL as Ya,ev as ga,d1 as ma,lM as jd,lN as Dn,lO as $n,m as kd,lP as Hd,bE as jl,e as kl,ei as Gn,bU as Wd,lQ as Mn,gg as zs,cV as qd,ft as zn,k7 as Yd,aS as Vn,aU as Zd,lR as Xd,gI as Un,c_ as Hl,gF as Fn,db as Jd,g as Nn,bk as Qd}from"./MapView-DaoQedLH.js";import{M as Ui,R as Vi,v as Kd,z as eu,I as tu,b as iu}from"./dehydratedFeatures-CEuswj7y.js";import{a as ru}from"./elevationInfoUtils-5B4aSzEU.js";import{H as fa,I as ya,J as Er,b as Je,T as Rr,U as su,K as Wl,L as ql,X as Za,M as au,N as nu,P as ou,B as lu,F as Bn,Q as hu,R as cu,S as du,V as Ki,W as q,x as ui,Y as uu,A as fs,Z as pu,_ as gu,a0 as mu,a1 as ns,a2 as Ar,a3 as Ye,a4 as fu,n as yu,c as Yl,a5 as ys,a6 as kt,E as jn,a7 as Vs,a8 as kn,o as _u,$ as os,g as _a,h as ur,i as bu,a9 as er,aa as Di,ab as vu,ac as Su,ad as Zl,ae as Hn,af as xu,ag as Cu,ah as Pu,ai as wu,aj as Xl,z as Yi,p as Ou,m as ba,k as Eu,w as Wn,ak as qe,al as Ru,am as Au,an as Qr,ao as va,ap as Iu,s as Tu,aq as qn,ar as Lu,as as Yn,at as Zn,au as Xn,d as Du,u as $u,j as Jn,av as Qn,aw as Gu,ax as Mu,ay as zu,C as he,az as Vu,l as Uu,e as Fu,aA as Nu}from"./NativeLine.glsl-4J4u-lA2.js";import{aJ as Kr,aK as Bu,aL as ju}from"./AnimatedLinesLayer-B2VbV4jv.js";import{i as fi}from"./optimizedFeatureQueryEngineAdapter-VytK6WwF.js";import{e as j,r as ku}from"./mat4f64-BCm7QTSd.js";import{t as Ct,n as Ae,D as Jl,u as qt}from"./basicInterfaces-Dc_Mm1a-.js";import{Q as _s,u as Mt,W as $i,b as Pt,aq as tr,aA as Hu,a5 as bs,a6 as vs,a7 as Ss,a8 as ls,r as $,ac as Xa,ad as Ja,ah as Y,h as O,aw as Qa,ax as Ql,az as Kl,aB as Wu,aC as eh,ak as N,am as Nt,an as li,al as Ka,p as en,aD as qu,v as tn,aE as th,d as ih,f as Yu,aF as Zu,X as rn,j as Xu,P as sn,a as rh,a9 as hs,aa as Sa,ab as an,aG as Kn,aH as Ju,aI as Qu,aJ as Ku,aK as ep,a4 as eo,aj as tp,aL as sh,aM as ip,ar as rp,E as sp,aN as ap,aO as np,$ as ai,aP as Us,aQ as Fs,aR as op,R as lp,g as Zi,Z as to,U as hp,k as ah,w as nh,T as oh,L as cp,G as dp,aS as up,ai as lh,V as pp,aT as gp}from"./VertexColor.glsl-CUs3Tjt8.js";import"./triangle-lwOWqU0w.js";import{o as mp,n as Ns}from"./Indices-iFKW8TWb.js";import{O as f}from"./VertexAttribute-BAIQI41G.js";import{k as fp,w as yp,R as _p,p as xa}from"./sphere-NgXH-gLx.js";import{A as Bs,a as bp,f as hh}from"./edgeUtils-Duo9ihNA.js";import{T as Fi}from"./InterleavedLayout-EYSqXknm.js";import{I as io,R as Mr,D as ir,F as ch}from"./enums-BDQrMlcz.js";import{o as w,W as zt,b as xs,g as vp,_ as Gi,a as Ze,c as rr,A as nn,l as on,E as dh,i as Sp,f as xp,h as Cp,d as Pp,S as wp}from"./OrderIndependentTransparency-C5Ap76ew.js";import{r as pr}from"./earcut-BJup91r2.js";import{e as Cs}from"./mat3f64-BVJGbF0t.js";import{T as cs,i as gr,b as ro,l as mr,u as Op,h as Ep,c as fr,d as so,x as ln}from"./BufferView-BcX1hwIm.js";import{t as uh,r as Ca}from"./vec33-BEptSvzS.js";import{t as Rp,e as ao}from"./QueryEngineResult-D2Huf9Bb.js";import{l as ph,f as gh,n as ci,a as wt,t as Ap}from"./triangulationUtils-Da3LiW_b.js";import{E as yr,c as Ni,a as Ip,t as Tp,p as Lp,n as Dp,e as $p,h as Gp,u as Mp,i as Mi,Q as zp,I as Vp}from"./objectResourceUtils-vFx_v_2w.js";import{o as Up}from"./pe-B8dP0-Ut.js";import{i as Fp}from"./callExpressionWithFeature-DgtD4TSq.js";import{g as Np,c as Bp}from"./MeshComponent-CfWisxg8.js";import{O as jp,v as kp,j as Hp,V as Wp,k as qp}from"./projection-oyk5Uk7v.js";import{t as js}from"./resourceUtils-CLKdXOwM.js";import{n as Yp,o as Zp}from"./symbolColorUtils-ByJCrvqG.js";import{o as mh}from"./glUtil-D4FNL8tc.js";import{V as Pa,U as Qe}from"./Octree-s2cwrV3a.js";import{s as ee}from"./Util-sSNWzwlq.js";import{a as Xp,l as Jp,p as sr,_ as Qp,x as Kp,b as eg,Y as fh,V as wa,O as no,P as oo,A as tg}from"./plane-BhzlJB-C.js";import"./boundedPlane-DeyjpfhM.js";import{E as yh,R as ig,a as rg}from"./FramebufferObject-8j9PRuxE.js";import{h as sg}from"./PooledRBush-CoOUdN-a.js";import{v as ag,b as ng}from"./lineSegment-DQ0q5UHF.js";import{projectGeometry as og}from"./geometryServiceUtils-Byad9QfI.js";import{u as lg}from"./LayerView-BSt9B8Gh.js";import"./vec3f32-nZdmKIgz.js";import"./Texture-BYqObwfn.js";import"./floatRGBA-PQQNbO39.js";const hg=r=>{let e=class extends r{constructor(){super(...arguments),this.slicePlaneEnabled=!1,this.supportsHeightUnitConversion=!1}postscript(t){super.postscript(t),Fc(this.layer)&&this.addResolvingPromise(this._validateHeightModelInfo())}async _validateHeightModelInfo(){const t=new AbortController,i=t.signal;this.handles.add(ds(()=>t.abort())),await xl(()=>{var a;return(a=this.view.defaultsFromMap)==null?void 0:a.heightModelInfoReady},i),Oe(i);const s=Nc(this.layer,this.view.heightModelInfo,this.supportsHeightUnitConversion);if(s)throw s}canResume(){const t=this.layer&&"effectiveScaleRange"in this.layer?this.layer.effectiveScaleRange:null;return super.canResume()&&(!t||!t.minScale||!t.maxScale||t.minScale>=t.maxScale)}getSuspendInfo(){const t=super.getSuspendInfo(),i=this.layer&&"effectiveScaleRange"in this.layer?this.layer.effectiveScaleRange:null;return i&&i.minScale&&i.maxScale&&i.minScale<i.maxScale&&(t.outsideScaleRange=!0),t}};return y([v()],e.prototype,"view",void 0),y([v()],e.prototype,"slicePlaneEnabled",void 0),e=y([Xe("esri.views.3d.layers.LayerView3D")],e),e};async function cg(r,e,t,i,s){const{elevationProvider:a,renderCoordsHelper:n,spatialReference:o}=r,{elevationInfo:l}=e,h=fa(l,!0),c=await ya(h,o,s);Oe(s);const u=[],p=new Set,m=new Set;for(const{objectId:g,points:_}of i){const S=t(g);if(b(S)){for(const x of _)u.push(x[2]);p.add(g);continue}S.isDraped&&m.add(g);const P=S.graphic.geometry;ks.setFromElevationInfo(ru(P,l)),ks.updateFeatureExpressionInfoContext(c,S.graphic,e),Bi.spatialReference=r.spatialReference;for(const{x,y:A,z:E}of _)Bi.x=x,Bi.y=A,Bi.z=E??0,Er(Bi,a,ks,n,lo),u.push(lo.z)}return{elevations:u,drapedObjectIds:m,failedObjectIds:p}}const ks=new Je,Bi=Ui(0,0,0,Cc.WGS84),lo=new Rr;function Ps(r,e){if(!r||r.symbol)return null;const t=e&&e.renderer;return r&&d(t)&&t.getObservationRenderer?t.getObservationRenderer(r):t}function dg(r,e){if(d(r.symbol))return r.symbol;const t=Ps(r,e);return d(t)&&t.type!=="dot-density"?t.getSymbol(r,e):null}function ug(r,e){const t=Ps(r,e),i=dg(r,e);if(b(i))return null;const s={renderer:t,symbol:i};if(b(t)||!("visualVariables"in t)||!t.visualVariables)return s;const a=Ol(t,r,e)??[],n=["proportional","proportional","proportional"];for(const{variable:o,value:l}of a)switch(o.type){case"color":s.color=l.toRgba();break;case"size":if(o.target==="outline")s.outlineSize=l;else{const h=o.axis,c=o.useSymbolValue?"symbol-value":l;switch(h){case"width":n[0]=c;break;case"depth":n[1]=c;break;case"height":n[2]=c;break;case"width-and-depth":n[0]=n[1]=c;break;default:n[0]=n[1]=n[2]=c}}break;case"opacity":s.opacity=l;break;case"rotation":switch(o.axis){case"tilt":s.tilt=l;break;case"roll":s.roll=l;break;default:s.heading=l}}return n[0]==="proportional"&&n[1]==="proportional"&&n[2]==="proportional"||(s.size=n),s}async function pg(r,e){if(d(r.symbol))return r.symbol;const t=Ps(r,e);return d(t)?t.getSymbolAsync(r,e):null}async function gg(r,e){const t=Ps(r,e),i=await pg(r,e);if(!i)return null;const s={renderer:t,symbol:i};if(!t||!("visualVariables"in t)||!t.visualVariables)return s;const a=Ol(t,r,e)??[],n=["proportional","proportional","proportional"];for(const{variable:o,value:l}of a)if(o.type==="color")s.color=ue.toUnitRGBA(l);else if(o.type==="size")if(o.target==="outline")s.outlineSize=l;else{const h=o.axis,c=o.useSymbolValue?"symbol-value":l;h==="width"?n[0]=c:h==="depth"?n[1]=c:h==="height"?n[2]=c:n[0]=n[1]=h==="width-and-depth"?c:n[2]=c}else o.type==="opacity"?s.opacity=l:o.type==="rotation"&&o.axis==="tilt"?s.tilt=l:o.type==="rotation"&&o.axis==="roll"?s.roll=l:o.type==="rotation"&&(s.heading=l);return(isFinite(n[0])||isFinite(n[1])||isFinite(n[2]))&&(s.size=n),s}function mg(r,e=0){const t=r[e];return typeof t=="number"&&isFinite(t)?t:null}function fg(r){for(let e=0;e<3;e++){const t=r[e];if(typeof t=="number")return isFinite(t)?t:0}return 0}const yg=1.2,_g=Ji,bg=Bc.fromSimpleMarkerSymbol(jc),vg=kc.fromSimpleLineSymbol(Hc),Sg=Wc.fromSimpleFillSymbol(qc),xg=new Yc({symbolLayers:[new Zc({material:{color:Xc},edges:new Jc({size:"1px",color:Qc})})]});function Cg(r){if(b(r))return null;switch(r.type){case"mesh":return xg;case"point":case"multipoint":return bg;case"polyline":return vg;case"polygon":case"extent":return Sg}return null}let Pg=class{constructor(e,t){this.spatialReference=e,this._view=t}getElevation(e,t,i){return this._view.elevationProvider.getElevation(e,t,0,this.spatialReference,i)}async queryElevation(e,t,i,s,a){return this._view.elevationProvider.queryElevation(e,t,0,this.spatialReference,a,i,s)}};var ye,ze;(function(r){r[r.GRAPHIC=0]="GRAPHIC",r[r.LABEL=1]="LABEL",r[r._COUNT=2]="_COUNT"})(ye||(ye={})),function(r){r[r.USER_SETTING=0]="USER_SETTING",r[r.SCALE_RANGE=1]="SCALE_RANGE",r[r.FILTER=2]="FILTER",r[r.DECONFLICTION=3]="DECONFLICTION",r[r._COUNT=4]="_COUNT"}(ze||(ze={}));const ho=_e();let ht=class extends Ht{constructor(e){super(e),this.events=new gs,this.hasZ=null,this.hasM=null,this.objectIdField=null,this.featureAdapter={getAttribute:(t,i)=>"graphic"in t?t.graphic.attributes[i]:fi.getAttribute(t,i),getAttributes:t=>"graphic"in t?t.graphic.attributes:fi.getAttributes(t),getObjectId:t=>"graphic"in t?Vi(t.graphic,this.objectIdField)??void 0:fi.getObjectId(t),getGeometry:t=>"graphic"in t?t.getAsOptimizedGeometry(this.hasZ,this.hasM):fi.getGeometry(t),getCentroid:(t,i)=>{if("graphic"in t){let s=null;d(t.centroid)?s=t.centroid:t.graphic.geometry.type==="point"&&Kc(t.graphic.geometry,co,this.viewSpatialReference)&&(s=co);const a=new Array(2+(i.hasZ?1:0)+(i.hasM?1:0));return b(s)?(a[0]=0,a[1]=0,a[2]=0,a[3]=0):(a[0]=s.x,a[1]=s.y,i.hasZ&&(a[2]=s.hasZ?s.z:0),i.hasM&&(a[i.hasZ?3:2]=s.hasM?s.m:0)),new ed([],a)}return fi.getCentroid(t,i)},cloneWithGeometry:(t,i)=>"graphic"in t?new td(i,this.featureAdapter.getAttributes(t),null,this.featureAdapter.getObjectId(t)):fi.cloneWithGeometry(t,i)}}forEachInBounds(e,t){this.getSpatialIndex().forEachInBounds(e,t)}forEachBounds(e,t){const i=this.getSpatialIndex();for(const s of e){const a=this.featureAdapter.getObjectId(s);d(i.getBounds(a,ho))&&t(ho)}}};y([v({constructOnly:!0})],ht.prototype,"getSpatialIndex",void 0),y([v({constructOnly:!0})],ht.prototype,"forEach",void 0),y([v({constructOnly:!0})],ht.prototype,"hasZ",void 0),y([v({constructOnly:!0})],ht.prototype,"hasM",void 0),y([v({constructOnly:!0})],ht.prototype,"objectIdField",void 0),y([v({constructOnly:!0})],ht.prototype,"viewSpatialReference",void 0),y([v({constructOnly:!0})],ht.prototype,"featureSpatialReference",void 0),ht=y([Xe("esri.views.3d.layers.graphics.Graphics3DFeatureStore")],ht);const co={type:"point",x:0,y:0,hasZ:!1,hasM:!1,spatialReference:null};let wg=class{constructor(e,t,i){this.graphic=e,this.renderingInfo=t,this.layer=i}},Og=class{constructor(e,t){this.scheduler=e,this.schedule=t,this.sharedResources=null,this.streamDataRequester=null,this.elevationProvider=null,this.renderer=null,this.stage=null,this.clippingExtent=null,this.renderCoordsHelper=null,this.overlaySR=null,this.layer=null,this.drapeSourceRenderer=null,this.graphicsCoreOwner=null,this.localOriginFactory=null,this.featureExpressionInfoContext=null,this.screenSizePerspectiveEnabled=!0,this.slicePlaneEnabled=!1,this.physicalBasedRenderingEnabled=!1,this.skipHighSymbolLods=!1,this.isAsync=!1}};function hn(r){return d(r.mapPositions)}function _h(r,e,t,i,s){const a=r.stageObject,n=a.geometries;let o=0;for(const l of n){if(!hn(l))continue;const{update:h,averageGeometrySampledElevation:c}=vh(l,e,t,i,s);o+=c,h&&a.geometryVertexAttrsUpdated(l)}return o/n.length}function _r(r,e,t,i,s){var u;const a=r.stageObject,n=e.centerPointInElevationSR;let o=0;(u=a.metadata)!=null&&u.usesVerticalDistanceToGround?(i(n,we),su(a,we.verticalDistanceToGround),o=we.sampledElevation):(i(n,we),e.mode!=="absolute-height"&&(o=we.sampledElevation));const l=za(Eg,a.transformation),h=D(bh,l[12],l[13],l[14]);Ie.TESTS_DISABLE_OPTIMIZATIONS?(Pe[0]=n.x,Pe[1]=n.y,Pe[2]=we.z,hi(n.spatialReference,Pe,l,s.spatialReference)&&(a.transformation=l)):s.setAltitudeOfTransformation(we.z,l);const c=cn/s.unitInMeters;return(Math.abs(l[12]-h[0])>=c||Math.abs(l[13]-h[1])>=c||Math.abs(l[14]-h[2])>=c)&&(a.transformation=l),o}const Eg=j();function Rg(r,e,t,i,s){const a=r.graphics3DSymbolLayer.lodRenderer;if(b(a))return 0;const n=e.centerPointInElevationSR;i(n,we);const o=e.mode!=="absolute-height"?we.sampledElevation:0,l=a.instanceData,h=r.instanceIndex,c=Ig;l.getGlobalTransform(h,c);const u=D(bh,c[12],c[13],c[14]);Ie.TESTS_DISABLE_OPTIMIZATIONS?(Pe[0]=n.x,Pe[1]=n.y,Pe[2]=we.z,hi(n.spatialReference,Pe,c,s.spatialReference)&&l.setGlobalTransform(h,c)):s.setAltitudeOfTransformation(we.z,c);const p=cn/s.unitInMeters;return(Ie.TESTS_DISABLE_OPTIMIZATIONS||Math.abs(c[12]-u[0])>=p||Math.abs(c[13]-u[1])>=p||Math.abs(c[14]-u[2])>=p)&&l.setGlobalTransform(h,c),o}function Ag(r,e,t,i,s){const a=r.stageObject,n=a.geometries;if(n.length===0)return 0;let o=0,l=null,h=0,c=!1;for(const u of n){if(!hn(u))continue;const p=u.vertexAttributes.get(f.POSITION);if(p!==l){const{update:m,averageGeometrySampledElevation:g}=vh(u,e,t,i,s);h=g,l=p,c=m}c&&a.geometryVertexAttrsUpdated(u),o+=h}return o/n.length}const cn=.01,Pe=C(),Fe=C(),yi=C(),Ig=j(),bh=C(),we=new Rr;function vh(r,e,t,i,s){let a=!1;const n=r.shaderTransformation,o=e.requiresSampledElevationInfo;Fe[0]=n[12],Fe[1]=n[13],Fe[2]=n[14],r.invalidateBoundingInfo();const l=r.getMutableAttribute(f.POSITION),h=l.data,c=l.size,u=h.length/c,p=new Wl(r.mapPositions,t);let m=0,g=0;for(let _=0;_<u;_++){if(yi[0]=h[m],yi[1]=h[m+1],yi[2]=h[m+2],i(p,we),o&&(g+=we.sampledElevation),Ie.TESTS_DISABLE_OPTIMIZATIONS)h[m]=p.array[p.offset],h[m+1]=p.array[p.offset+1],h[m+2]=we.z,ut(h,t,m,h,s.spatialReference,m,1),h[m]-=Fe[0],h[m+1]-=Fe[1],h[m+2]-=Fe[2],a=!0;else{Pe[0]=h[m]+Fe[0],Pe[1]=h[m+1]+Fe[1],Pe[2]=h[m+2]+Fe[2],s.setAltitude(Pe,we.z),h[m]=Pe[0]-Fe[0],h[m+1]=Pe[1]-Fe[1],h[m+2]=Pe[2]-Fe[2];const S=cn/s.unitInMeters;(Math.abs(yi[0]-h[m])>=S||Math.abs(yi[1]-h[m+1])>=S||Math.abs(yi[2]-h[m+2])>=S)&&(a=!0)}m+=c,p.offset+=3}return g/=u,{update:a,averageGeometrySampledElevation:g}}let Tg=class{constructor(e,t,i){this.baseMaterial=e,this.edgeMaterials=t,this.properties=i}},gt=class{get isElevationSource(){return!(!this.stageObject.metadata||!this.stageObject.metadata.isElevationSource)}constructor(e,t,i,s,a,n,o,l=null){this.graphics3DSymbolLayer=e,this.stageObject=t,this._uniqueGeometries=i,this._uniqueMaterials=s,this._sharedResource=a,this.elevationAligner=n,this.elevationContext=o,this._edgeState=l,this.type="object3d",this._stageLayer=null,this._stage=null,this._visible=!1,this._addedToStage=!1,this.alignedSampledElevation=0,this.needsElevationUpdates=!1,this.useObjectOriginAsAttachmentOrigin=!1}initialize(e,t){this._stageLayer=t,this._stage=e,e.addMany(this._uniqueMaterials),e.addMany(this._uniqueGeometries),e.add(this.stageObject)}destroy(){const e=this._stage;this._stageLayer&&(e.removeMany(this._uniqueMaterials),e.removeMany(this._uniqueGeometries)),e.remove(this.stageObject),this._addedToStage&&(this._stageLayer.remove(this.stageObject),this._addedToStage=!1);const t=this._stage.renderer.ensureEdgeView();t.hasObject(this.stageObject)&&t.removeObject(this.stageObject),this.stageObject.dispose(),d(this._sharedResource)&&this._sharedResource.release(),this._visible=!1,this._stageLayer=null,this._stage=null}layerOpacityChanged(e,t){if(b(this._edgeState))return;const i=uo(this._edgeState.baseMaterial);let s=!1;for(const a of this._edgeState.edgeMaterials)a.objectTransparency!==i&&(a.objectTransparency=i,s=!0);s&&this._resetEdgeObject(t),this._stage.renderer.ensureEdgeView().updateAllComponentOpacities(this.stageObject,[e])}slicePlaneEnabledChanged(e,t){b(this._edgeState)||(this._stage.renderer.ensureEdgeView().updateAllComponentMaterials(this.stageObject,this._edgeState.edgeMaterials,{hasSlicePlane:e},!t),this._edgeState.properties.hasSlicePlane=e)}setVisibility(e){if(this._stage!=null&&this._visible!==e&&(this._visible=e,this.stageObject.visible=e,this._visible&&!this._addedToStage&&(this._stageLayer.add(this.stageObject),this._addedToStage=!0),d(this._edgeState))){const t=this._stage.renderer.ensureEdgeView();t.hasObject(this.stageObject)?t.updateObjectVisibility(this.stageObject,e):e&&this._addOrUpdateEdgeObject(t,!1)}}get visible(){return this._visible}alignWithElevation(e,t,i,s){if(this.elevationAligner==null)return;d(i)&&ql(this.elevationContext.featureExpressionInfoContext,i);const a=(n,o)=>Er(n,e,this.elevationContext,t,o);this.alignedSampledElevation=this.elevationAligner(this,this.elevationContext,se(e.spatialReference),a,t),this._resetEdgeObject(s)}alignWithAbsoluteElevation(e,t,i){const s=(a,n)=>{n.sampledElevation=e,n.verticalDistanceToGround=0,n.z=e};this.alignedSampledElevation=this.elevationAligner(this,this.elevationContext,null,s,t),this._resetEdgeObject(i)}getCenterObjectSpace(e=C()){return de(e,fp(this.stageObject.boundingVolumeObjectSpace.bounds))}getBoundingBoxObjectSpace(e=_e()){const t=this.stageObject.boundingVolumeObjectSpace;return id(e,t.min),rd(e,t.max),e}computeAttachmentOrigin(e){if(this.useObjectOriginAsAttachmentOrigin){const t=this.stageObject.transformation;e.render.origin[0]+=t[12],e.render.origin[1]+=t[13],e.render.origin[2]+=t[14],e.render.num++}else for(const t of this.stageObject.geometries)t.computeAttachmentOrigin(ft)&&(ge(ft,ft,this.stageObject.transformation),me(e.render.origin,e.render.origin,ft),e.render.num++)}async getProjectedBoundingBox(e,t,i,s,a){const n=this.getBoundingBoxObjectSpace(a),o=Lg,l=El(n)?1:o.length;for(let c=0;c<l;c++){const u=o[c];Rt[0]=n[u[0]],Rt[1]=n[u[1]],Rt[2]=n[u[2]],ge(Rt,Rt,this.stageObject.transformation),Yt[3*c+0]=Rt[0],Yt[3*c+1]=Rt[1],Yt[3*c+2]=Rt[2]}if(!e(Yt,0,l))return null;B(n);let h=null;this.calculateRelativeScreenBounds&&(h=this.calculateRelativeScreenBounds());for(let c=0;c<3*l;c+=3){for(let u=0;u<3;u++)n[u]=Math.min(n[u],Yt[c+u]),n[u+3]=Math.max(n[u+3],Yt[c+u]);h&&i.push({location:Yt.slice(c,c+3),screenSpaceBoundingRect:h})}if(t&&t.service&&this.elevationContext.mode!=="absolute-height"){dt(n,ft);const c=this.elevationContext.mode==="relative-to-scene"?"scene":"ground";let u=0;if(t.useViewElevation)u=pe(t.service.getElevation(ft[0],ft[1],c),0);else try{const p=Za(n,t.service.spatialReference,t);u=pe(await t.service.queryElevation(ft[0],ft[1],s,p,c),0)}catch{}Rl(n,0,0,-this.alignedSampledElevation+u)}return n}addObjectState(e,t){e===Ct.Highlight&&t.addObject(this.stageObject,this.stageObject.highlight()),e===Ct.MaskOccludee&&t.addObject(this.stageObject,this.stageObject.maskOccludee())}removeObjectState(e){e.removeObject(this.stageObject)}_resetEdgeObject(e){if(b(this._edgeState))return;const t=this._stage.renderer.ensureEdgeView();this._visible?this._addOrUpdateEdgeObject(t,e):t.removeObject(this.stageObject)}_addOrUpdateEdgeObject(e,t){const i=this._edgeState;if(b(i))return;const s=uo(i.baseMaterial);for(const a of i.edgeMaterials)a.objectTransparency=s;e.addOrUpdateObject3D(this.stageObject,i.edgeMaterials,i.properties,!t).then(()=>{var a;return(a=this._stageLayer)==null?void 0:a.sync()})}};function uo(r){return r.isVisible()?r.parameters.transparent?Bs.TRANSPARENT:Bs.OPAQUE:Bs.INVISIBLE}const Yt=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],Rt=C(),ft=C(),Lg=[[0,1,2],[3,1,2],[0,4,2],[3,4,2],[0,1,5],[3,1,5],[0,4,5],[3,4,5]];let Sh=class{constructor(e,t=null){this.labelText=t,this.elevationOffset=pe(e,0)}};var H;(function(r){r[r.Recreate_Symbol=0]="Recreate_Symbol",r[r.Recreate_Graphics=1]="Recreate_Graphics",r[r.Fast_Update=2]="Fast_Update"})(H||(H={}));let dn=class{constructor(e){this.schedule=e,this._abortController=null,this._loadStatus=Ge.LOADING,this._loadError=null,this._loader=null,this.logger=null}destroy(){this.abortLoad()}get loadStatus(){return this._loadStatus}load(e,t){return this._loadStatus===Ge.LOADED?(e&&e(),pe(this._loader,Promise.resolve())):this._loadStatus===Ge.FAILED?(t&&t(this._loadError),pe(this._loader,Promise.resolve())):(b(this._loader)&&(this._abortController=new AbortController,this._loader=this.doLoad(this._abortController.signal).then(()=>{this._abortController=null,this._loadStatus=Ge.LOADED},i=>{throw this._loadError=i,this._abortController=null,this._loadStatus=Ge.FAILED,!Xi(i)&&this.logger&&i.message&&this.logger.warn(i.message),i})),this._loader.then(e,t).catch(()=>{}),this._loader)}abortLoad(){d(this._abortController)?this._abortController=Mc(this._abortController):this._loadStatus===Ge.LOADING&&(this._loadStatus=Ge.FAILED),this._loader=null}};var Ge;(function(r){r[r.LOADING=0]="LOADING",r[r.LOADED=1]="LOADED",r[r.FAILED=2]="FAILED"})(Ge||(Ge={}));const xh=3,Oa=3,Ch=10;let Ph=class{constructor(e,t=null){this.geometry=e,this.textures=t}};function zr(r){const e=[];return r.levels.forEach(t=>t.components.forEach(i=>e.push(i.geometry.material))),ps(e)}function po(r){const e=new Array;return r.levels.forEach(t=>{t.components.forEach(i=>{d(i.textures)&&e.push(...i.textures)})}),ps(e)}function wh(r){const e=r.components.map(t=>t.geometry);return ps(e)}function go(r){const e=[];return r.levels.forEach(t=>{t.components.forEach(i=>{e.push(i.geometry)})}),ps(e)}function Dg(r){switch(r){case"sphere":case"cube":case"diamond":case"cylinder":case"cone":case"inverted-cone":case"tetrahedron":return!0}return!1}function Oh(r,e){const t=(i,s,a=!1)=>({levels:i.map(n=>{const o=s(n.tesselation);return a&&au(o),{components:[new Ph(o)],faceCount:o.indexCount/3,minScreenSpaceRadius:n.minScreenSpaceRadius}})});switch(r){case"sphere":return t([{tesselation:0,minScreenSpaceRadius:0},{tesselation:1,minScreenSpaceRadius:8},{tesselation:2,minScreenSpaceRadius:16},{tesselation:3,minScreenSpaceRadius:50},{tesselation:4,minScreenSpaceRadius:250}],i=>cu(e,.5,i));case"cube":return t([{tesselation:0,minScreenSpaceRadius:0}],()=>hu(e,1));case"cone":return t(Hs,i=>Bn(e,1,.5,i,!1),!0);case"inverted-cone":return t(Hs,i=>Bn(e,1,.5,i,!0),!0);case"cylinder":return t(Hs,i=>lu(e,1,.5,i,[0,0,1],[0,0,.5]));case"tetrahedron":return t([{tesselation:0,minScreenSpaceRadius:0}],()=>ou(e,1),!0);case"diamond":return t([{tesselation:0,minScreenSpaceRadius:0}],()=>nu(e,1),!0);default:return}}const Hs=[{tesselation:6,minScreenSpaceRadius:0},{tesselation:18,minScreenSpaceRadius:7},{tesselation:64,minScreenSpaceRadius:65}],un={primitivesPerFeature:0,primitivesPerCoordinate:0,drawCallsPerFeature:0,estimated:!0,memory:{bytesPerFeature:0,bytesPerCoordinate:0,bytesPerFeatureLabel:0,resourceBytes:0,draped:{bytesPerFeature:0,bytesPerFeatureLabel:0,bytesPerCoordinate:0}}};function $g(r){return r.type==="web-style"?un:pn(r.symbolLayers.toArray().map(e=>Eh(r,e)))}function pn(r){let e=0,t=0,i=0,s=!1,a=0;const n={bytesPerFeature:0,bytesPerFeatureLabel:0,bytesPerCoordinate:0,resourceBytes:0,draped:{bytesPerFeature:0,bytesPerFeatureLabel:0,bytesPerCoordinate:0}};for(const o of r)b(o)||(e+=o.primitivesPerFeature,t+=o.primitivesPerCoordinate,i+=o.drawCallsPerFeature,n.bytesPerFeature+=o.memory.bytesPerFeature,n.bytesPerFeatureLabel+=o.memory.bytesPerFeatureLabel,n.bytesPerCoordinate+=o.memory.bytesPerCoordinate,n.resourceBytes+=o.memory.resourceBytes,n.draped.bytesPerFeature+=o.memory.bytesPerFeature,n.draped.bytesPerFeatureLabel+=o.memory.bytesPerFeatureLabel,n.draped.bytesPerCoordinate+=o.memory.bytesPerCoordinate,s=s||o.estimated,++a);return{primitivesPerFeature:e,primitivesPerCoordinate:t,drawCallsPerFeature:i,estimated:s,memory:n,numComplexities:a}}function Gg(r){const e=pn(r);return e.numComplexities>0&&(e.primitivesPerFeature/=e.numComplexities,e.primitivesPerCoordinate/=e.numComplexities,e.drawCallsPerFeature/=e.numComplexities,e.memory.bytesPerFeature/=e.numComplexities,e.memory.bytesPerFeatureLabel/=e.numComplexities,e.memory.bytesPerCoordinate/=e.numComplexities,e.memory.resourceBytes/=e.numComplexities,e.memory.draped.bytesPerFeature/=e.numComplexities,e.memory.draped.bytesPerFeatureLabel/=e.numComplexities,e.memory.draped.bytesPerCoordinate/=e.numComplexities),e}const mo={};function Eh(r,e){const t=Rh(r,e),i=bp(e)?2:0;switch(e.type){case"extrude":return{primitivesPerFeature:-4,primitivesPerCoordinate:4,drawCallsPerFeature:i,estimated:!1,memory:t};case"fill":return r.type==="mesh-3d"?{primitivesPerFeature:0,primitivesPerCoordinate:0,drawCallsPerFeature:i,estimated:!1,memory:t}:d(e.outline)&&e.outline.size>0?{primitivesPerFeature:-4,primitivesPerCoordinate:3,drawCallsPerFeature:0,estimated:!1,memory:t}:{primitivesPerFeature:-2,primitivesPerCoordinate:1,drawCallsPerFeature:0,estimated:!1,memory:t};case"water":return{primitivesPerFeature:-2,primitivesPerCoordinate:1,drawCallsPerFeature:0,estimated:!1,memory:t};case"line":return{primitivesPerFeature:-2,primitivesPerCoordinate:2,drawCallsPerFeature:0,estimated:!1,memory:t};case"object":return e.resource&&e.resource.href?{primitivesPerFeature:16,primitivesPerCoordinate:0,drawCallsPerFeature:0,estimated:!0,memory:t}:{...Mg(e.resource&&e.resource.primitive||Al),memory:t};case"path":{let s=0,a=0;switch(e.profile){case"circle":s=Ch;break;case"quad":s=4;break;default:return void is(e.profile)}switch(e.join??"simple"){case"round":a=xh;break;case"miter":case"bevel":a=1;break;default:return}const n=2*s,o=s*a*2;let l=-2*o-n;switch(e.cap){case"none":break;case"butt":case"square":l+=2*(s-1);break;case"round":l+=2*(s*(Oa-1)*2+s);break;default:return}return{primitivesPerFeature:l,primitivesPerCoordinate:o+n,drawCallsPerFeature:0,estimated:!1,memory:t}}case"text":case"icon":return{primitivesPerFeature:2,primitivesPerCoordinate:0,drawCallsPerFeature:0,estimated:!1,memory:t};default:return}}function Rh(r,e){const t=r.type==="point-3d";switch(e.type){case"extrude":return e.edges&&e.edges.size>0?ve.EXTRUDE_EDGES:ve.EXTRUDE;case"fill":return d(e.outline)&&e.outline.size>0?ve.FILL_OUTLINE:ve.FILL;case"water":return ve.FILL;case"line":return e.join==="round"?ve.LINE_ROUND:ve.LINE_MITER;case"path":switch(e.join){case"round":switch(e.profile){case"circle":return ve.PATH_ROUND_CIRCLE;case"quad":return ve.PATH_ROUND_QUAD;default:return void is(e.profile)}case"miter":case"bevel":switch(e.profile){case"circle":return ve.PATH_MITER_CIRCLE;case"quad":return ve.PATH_MITER_QUAD;default:return void is(e.profile)}default:return}case"object":return t?ve.OBJECT_POINT:ve.OBJECT_POLYGON;case"icon":case"text":return t?ve.ICON_POINT:ve.ICON_POLYGON;default:return}}function Mg(r){let e=mo[r];if(e)return e;const t=Oh(r,null);return e={primitivesPerFeature:wh(t.levels[0]).reduce((i,s)=>i+s.indices.get(f.POSITION).length/3,0),primitivesPerCoordinate:0,drawCallsPerFeature:0,estimated:!1},mo[r]=e,e}const ve={ICON_POINT:{bytesPerFeature:3293.7707557538765,bytesPerFeatureLabel:1020.2764,bytesPerCoordinate:0,resourceBytes:0,draped:{bytesPerFeature:2213.51585392203,bytesPerFeatureLabel:1008.84664,bytesPerCoordinate:0}},ICON_POLYGON:{bytesPerFeature:4745.397589234022,bytesPerFeatureLabel:1006.5440666666666,bytesPerCoordinate:3.8971910718981526,resourceBytes:0,draped:{bytesPerFeature:3711.9299672493826,bytesPerFeatureLabel:991.1003999999999,bytesPerCoordinate:3.916858016273668}},OBJECT_POINT:{bytesPerFeature:1751.6405617660876,bytesPerFeatureLabel:1024.7827699999998,bytesPerCoordinate:0,resourceBytes:0,draped:{bytesPerFeature:1751.6405617660876,bytesPerFeatureLabel:1024.7827699999998,bytesPerCoordinate:0}},OBJECT_POLYGON:{bytesPerFeature:3265.877223973855,bytesPerFeatureLabel:1002.3207366666666,bytesPerCoordinate:4.003101524580855,resourceBytes:0,draped:{bytesPerFeature:3265.877223973855,bytesPerFeatureLabel:1002.3207366666666,bytesPerCoordinate:4.003101524580855}},LINE_MITER:{bytesPerFeature:5757.019675077085,bytesPerFeatureLabel:998.6150266666667,bytesPerCoordinate:24.456810187064043,resourceBytes:0,draped:{bytesPerFeature:4101.480288021862,bytesPerFeatureLabel:1007.7540599999999,bytesPerCoordinate:18.25629912730874}},LINE_ROUND:{bytesPerFeature:5770.95297166408,bytesPerFeatureLabel:1017.77396,bytesPerCoordinate:24.60919037084966,resourceBytes:0,draped:{bytesPerFeature:4089.5796612121826,bytesPerFeatureLabel:987.8320266666666,bytesPerCoordinate:18.43599029126731}},PATH_MITER_CIRCLE:{bytesPerFeature:36120.81813311945,bytesPerFeatureLabel:977.3815,bytesPerCoordinate:2713.2216607858863,resourceBytes:0,draped:{bytesPerFeature:36120.81813311945,bytesPerFeatureLabel:977.3815,bytesPerCoordinate:2713.2216607858863}},PATH_ROUND_CIRCLE:{bytesPerFeature:23268.96777866874,bytesPerFeatureLabel:985.2637,bytesPerCoordinate:5284.873051323177,resourceBytes:0,draped:{bytesPerFeature:23268.96777866874,bytesPerFeatureLabel:985.2637,bytesPerCoordinate:5284.873051323177}},PATH_MITER_QUAD:{bytesPerFeature:24548.629753007182,bytesPerFeatureLabel:964.6924,bytesPerCoordinate:2114.107276663994,resourceBytes:0,draped:{bytesPerFeature:24548.629753007182,bytesPerFeatureLabel:964.6924,bytesPerCoordinate:2114.107276663994}},PATH_ROUND_QUAD:{bytesPerFeature:34316.219663191616,bytesPerFeatureLabel:975.7985,bytesPerCoordinate:3331.3952550120293,resourceBytes:0,draped:{bytesPerFeature:34316.219663191616,bytesPerFeatureLabel:975.7985,bytesPerCoordinate:3331.3952550120293}},FILL:{bytesPerFeature:6141.501452970723,bytesPerFeatureLabel:1008.2056066666665,bytesPerCoordinate:9.669541106191478,resourceBytes:0,draped:{bytesPerFeature:4615.812654782311,bytesPerFeatureLabel:1004.2114200000001,bytesPerCoordinate:7.378043214430644}},FILL_OUTLINE:{bytesPerFeature:9038.575581319641,bytesPerFeatureLabel:1017.9996066666668,bytesPerCoordinate:14.96569682175086,resourceBytes:0,draped:{bytesPerFeature:6369.386021594582,bytesPerFeatureLabel:1008.9863733333334,bytesPerCoordinate:10.25287774000214}},EXTRUDE:{bytesPerFeature:20071.2337049912,bytesPerFeatureLabel:1019.49468,bytesPerCoordinate:49.39369614206849,resourceBytes:0,draped:{bytesPerFeature:20071.2337049912,bytesPerFeatureLabel:1019.49468,bytesPerCoordinate:49.39369614206849}},EXTRUDE_EDGES:{bytesPerFeature:22300.21739345088,bytesPerFeatureLabel:1017.4348466666665,bytesPerCoordinate:49.40242281963031,resourceBytes:0,draped:{bytesPerFeature:22300.21739345088,bytesPerFeatureLabel:1017.4348466666665,bytesPerCoordinate:49.40242281963031}}},_i=di.getLogger("esri.views.3d.layers.graphics.Graphics3DSymbolLayer");let mt=class extends dn{constructor(e,t,i,s){super(i.schedule),this.symbol=e,this.symbolLayer=t,this._context=i,this._elevationInfoOverride=null,this._ignoreDrivers=!1,this._drivenProperties={color:!1,opacity:!1,opacityAlwaysOpaque:!0,size:!1},this.complexity=null,this.logger=_i,this._elevationOptions={supportsOffsetAdjustment:!1,supportsOnTheGround:!0},this._renderPriority=s.renderPriority,this._renderPriorityStep=s.renderPriorityStep,this._elevationContext=new Je,this.complexity=this.computeComplexity(),this._ignoreDrivers=s.ignoreDrivers,this._ignoreDrivers||(this._drivenProperties=fo(this._context.renderer)),this._updateElevationContext()}getCachedSize(){return null}get extentPadding(){return 0}_drivenPropertiesChanged(e){if(this._ignoreDrivers)return!1;const t=this._drivenProperties,i=fo(e);return i.color!==t.color||i.opacity!==t.opacity||i.opacityAlwaysOpaque!==t.opacityAlwaysOpaque||i.size!==t.size}get needsDrivenTransparentPass(){return this._drivenProperties.opacity&&!this._drivenProperties.opacityAlwaysOpaque}_logGeometryCreationWarnings(e,t,i,s){const a=e.projectionSuccess,n="polygons"in e?e.polygons:null,o=`${s} geometry failed to be created`;let l=null;a?!this._logGeometryValidationWarnings(t,i,s)&&n&&n.length===0&&i==="rings"&&t.length>0&&t[0].length>2&&(l=`${o} (filled rings should use clockwise winding - try reversing the order of vertices)`):l=`${o} (failed to project geometry to view spatial reference)`,l&&_i.warnOncePerTick(l)}_logGeometryValidationWarnings(e,t,i){const s=`${i} geometry failed to be created`;return!e.length||e.length===1&&!e[0].length?(_i.warnOncePerTick(`${s} (no ${t} were defined)`),!0):(!Array.isArray(e)||!Array.isArray(e[0]))&&(_i.warnOncePerTick(`${s} (${t} should be defined as a 2D array)`),!0)}_validateGeometry(e,t=null,i=null){if(d(t)&&!t.includes(e.type))return this.logger.warn("unsupported geometry type for "+i+` symbol: ${e.type}`),!1;if(e.type==="point"){const s=e;if(!isFinite(s.x)||!isFinite(s.y))return _i.warn("point coordinate is not a valid number, graphic skipped"),!1}return!0}_defaultElevationInfoNoZ(){return zg}_defaultElevationInfoZ(){return Vg}_updateElevationContext(){d(this._elevationInfoOverride)?(this._elevationContext.setFromElevationInfo(this._elevationInfoOverride),this._elevationContext.updateFeatureExpressionInfoContext(null)):this._context.layer.elevationInfo?(this._elevationContext.setFromElevationInfo(this._context.layer.elevationInfo),this._elevationContext.updateFeatureExpressionInfoContext(this._context.featureExpressionInfoContext)):this._elevationContext.reset()}getDefaultElevationInfo(e){return e.hasZ?this._defaultElevationInfoZ():this._defaultElevationInfoNoZ()}getGeometryElevationMode(e,t=this.getDefaultElevationInfo(e)){return this._elevationContext.mode||t.mode}setElevationInfoOverride(e){this._elevationInfoOverride=e,this._updateElevationContext()}setGraphicElevationContext(e,t){const i=se(e.geometry),s=this.getDefaultElevationInfo(i);t.unit=this._elevationContext.unit!=null?this._elevationContext.unit:s.unit,t.mode=this.getGeometryElevationMode(i,s),t.offsetMeters=pe(this._elevationContext.meterUnitOffset,pe(s.offset,0));const a=!this._elevationOptions.supportsOnTheGround&&t.mode==="on-the-ground";a&&(t.mode="relative-to-ground",t.offsetMeters=0);const n=a?du:this._elevationContext.featureExpressionInfoContext;return t.updateFeatureExpressionInfoContext(n,e,this._context.layer),t}prepareSymbolLayerPatch(e){}updateGeometry(e,t){return!1}onRemoveGraphic(e){}_getLayerOpacity(){return this._context.graphicsCoreOwner&&"fullOpacity"in this._context.graphicsCoreOwner?this._context.graphicsCoreOwner.fullOpacity??0:this._context.layer.opacity??1}_getCombinedOpacity(e,t=yo){let i=1;return this.draped||(i*=this._getLayerOpacity()),this._drivenProperties.opacity||(d(e)?i*=e.a:t.hasIntrinsicColor||(i=0)),i}_getCombinedOpacityAndColor(e,t=yo){const i=this._getCombinedOpacity(e,t);if(this._drivenProperties.color)return Ki(null,i);const s=d(e)?ue.toUnitRGB(e):si;return Ki(s,i)}_getVertexOpacityAndColor(e,t=null){const i=this._drivenProperties.color?e.color:null,s=this._drivenProperties.opacity?e.opacity:null,a=Ki(i,s);return d(t)&&(a[0]*=t,a[1]*=t,a[2]*=t,a[3]*=t),a}isFastUpdatesEnabled(){return this._fastUpdates&&this._fastUpdates.enabled}computeComplexity(){return Eh(this.symbol,this.symbolLayer)}globalPropertyChanged(e,t,i){switch(e){case"opacity":return this.layerOpacityChanged(t,i),!0;case"elevationInfo":{const s=this._elevationContext.mode;return this._updateElevationContext(),this.layerElevationInfoChanged(t,i,s)!==q.RECREATE}case"slicePlaneEnabled":return this.slicePlaneEnabledChanged(t,i);case"physicalBasedRenderingEnabled":return this.physicalBasedRenderingChanged();case"pixelRatio":return this.pixelRatioChanged();case"skipHighSymbolLods":return this.skipHighSymbolLodsChanged();default:return!1}}updateGraphics3DGraphicElevationInfo(e,t,i){let s=q.UPDATE;return e.forEach(a=>{const n=t(a);if(d(n)){const o=a.graphic;this.setGraphicElevationContext(o,n.elevationContext),n.needsElevationUpdates=i(n.elevationContext.mode)}else s=q.RECREATE}),s}applyRendererDiff(e,t){return H.Recreate_Symbol}getFastUpdateAttrValues(e){if(!this._fastUpdates.enabled)return null;const t=this._fastUpdates.visualVariables,i=t.size?Ot(t.size.field,e):0,s=t.color?Ot(t.color.field,e):0,a=t.opacity?Ot(t.opacity.field,e):0;return tt(i,s,a,0)}get draped(){return this._draped}ensureDrapedStatus(e){return this._draped==null?(this._draped=e,!0):(e!==this.draped&&_i.warnOnce("A symbol can only produce either draped or non-draped visualizations. Use two separate symbol instances for draped and non-draped graphics if necessary."),!1)}test(){const e=()=>{var t,i,s,a,n,o,l,h,c,u,p,m;return{size:((s=(i=(t=this._fastUpdates)==null?void 0:t.visualVariables)==null?void 0:i.size)==null?void 0:s.field)??null,color:((o=(n=(a=this._fastUpdates)==null?void 0:a.visualVariables)==null?void 0:n.color)==null?void 0:o.field)??null,opacity:((c=(h=(l=this._fastUpdates)==null?void 0:l.visualVariables)==null?void 0:h.opacity)==null?void 0:c.field)??null,rotation:((m=(p=(u=this._fastUpdates)==null?void 0:u.visualVariables)==null?void 0:p.rotation)==null?void 0:m.field)??null}};return{drivenProperties:this._drivenProperties,getVisVarFields:e}}};function Ot(r,e){const t=r!=null?e.attributes[r]:0;return t!=null&&isFinite(t)?t:0}function fo(r){const e={color:!1,opacity:!1,opacityAlwaysOpaque:!0,size:!1};return r&&"visualVariables"in r&&r.visualVariables&&r.visualVariables.forEach(t=>{switch(t.type){case"color":if(e.color=!0,t.stops)for(let i=0;i<t.stops.length;i++){const s=t.stops[i].color;s&&(e.opacity=!0,s.a<1&&(e.opacityAlwaysOpaque=!1))}break;case"opacity":e.opacity=!0,e.opacityAlwaysOpaque=!1;break;case"size":e.size=!0}}),e}const zg={mode:"on-the-ground",offset:0,unit:"meters"},Vg={mode:"absolute-height",offset:0,unit:"meters"},yo={hasIntrinsicColor:!1};function gn(r,e,t,i,s,a){const n=r.clippingExtent;if(or(e,Vt,r.elevationProvider.spatialReference),d(n)&&!Va(n,Vt))return null;or(e,Vt,r.renderCoordsHelper.spatialReference);const o=r.localOriginFactory.getOrigin(Vt),l=new ui({castShadow:!1,metadata:{layerUid:r.layer.uid,graphicUid:s,usesVerticalDistanceToGround:!0}});return t.shaderTransformer=a,t.localOrigin=o,l.addGeometry(t),{object:l,sampledElevation:uu(l,e,r.elevationProvider,r.renderCoordsHelper,i)}}function br(r,e,t){const i=r.elevationContext,s=t.spatialReference;or(e,Vt,s),i.centerPointInElevationSR=Ui(Vt[0],Vt[1],e.hasZ?Vt[2]:0,d(s)?s:null)}function vr(r){switch(r.type){case"point":return r;case"polygon":case"extent":return fs(r);case"polyline":{const e=r.paths[0];if(!e||e.length===0)return null;const t=sd(e,ad(e)/2);return Ui(t[0],t[1],t[2],r.spatialReference)}case"mesh":return r.origin}return null}const Vt=C();function Ah(r){const e=new _s;e.include(pu),e.include(gu,r),e.include(Mt,r),e.attributes.add(f.UV0,"vec2");const{vertex:t,fragment:i}=e;return t.uniforms.add([new $i("viewport",(s,a)=>a.camera.fullViewport),new Pt("lineSize",(s,a)=>Math.ceil(s.size)*a.camera.pixelRatio),new tr("pixelToNDC",(s,a)=>Ee(bo,2/a.camera.fullViewport[2],2/a.camera.fullViewport[3])),new Pt("borderSize",(s,a)=>d(s.borderColor)?a.camera.pixelRatio:0),new tr("screenOffset",(s,a)=>Ee(bo,s.screenOffset[0]*a.camera.pixelRatio,s.screenOffset[1]*a.camera.pixelRatio))]),e.varyings.add("coverageSampling","vec4"),e.varyings.add("lineSizes","vec2"),r.hasMultipassGeometry&&e.varyings.add("depth","float"),r.hasScreenSizePerspective&&Hu(t),t.code.add(w`
    void main(void) {
      ProjectHUDAux projectAux;
      vec4 endPoint = projectPositionHUD(projectAux);

      vec3 vpos = projectAux.posModel;
      if (rejectBySlice(vpos)) {
        gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
        return;
      }
    ${r.occlusionTestEnabled?w`
      if (!testVisibilityHUD(endPoint)) {
        gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
        return;
      }`:""}

    ${r.hasScreenSizePerspective?w`
      vec4 perspectiveFactor = screenSizePerspectiveScaleFactor(projectAux.absCosAngle, projectAux.distanceToCamera, screenSizePerspectiveAlignment);
      vec2 screenOffsetScaled = applyScreenSizePerspectiveScaleFactorVec2(screenOffset, perspectiveFactor);
        `:w`
      vec2 screenOffsetScaled = screenOffset;
        `}
      // Add view dependent polygon offset to get exact same original starting point. This is mostly
      // used to get the correct depth value
      vec3 posView = (view * vec4(position, 1.0)).xyz;
      ${r.hasMultipassGeometry?"depth = posView.z;":""}

      applyHUDViewDependentPolygonOffset(auxpos1.w, projectAux.absCosAngle, posView);
      vec4 startPoint = proj * vec4(posView, 1.0);
      // Apply screen offset to both start and end point
      vec2 screenOffsetNorm = screenOffsetScaled * 2.0 / viewport.zw;
      startPoint.xy += screenOffsetNorm * startPoint.w;
      endPoint.xy += screenOffsetNorm * endPoint.w;
      // Align start and end to pixel origin
      vec4 startAligned = alignToPixelOrigin(startPoint, viewport.zw);
      vec4 endAligned = alignToPixelOrigin(endPoint, viewport.zw);
    ${r.depthHudEnabled?r.depthHudAlignStartEnabled?w`endAligned = vec4(endAligned.xy / endAligned.w * startAligned.w, startAligned.zw);`:w`startAligned = vec4(startAligned.xy / startAligned.w * endAligned.w, endAligned.zw);`:""}
      vec4 projectedPosition = mix(startAligned, endAligned, uv0.y);
      // The direction of the line in screen space
      vec2 screenSpaceDirection = normalize(endAligned.xy / endAligned.w - startAligned.xy / startAligned.w);
      vec2 perpendicularScreenSpaceDirection = vec2(screenSpaceDirection.y, -screenSpaceDirection.x);
    ${r.hasScreenSizePerspective?w`
      float lineSizeScaled = applyScreenSizePerspectiveScaleFactorFloat(lineSize, perspectiveFactor);
      float borderSizeScaled = applyScreenSizePerspectiveScaleFactorFloat(borderSize, perspectiveFactor);
        `:w`
      float lineSizeScaled = lineSize;
      float borderSizeScaled = borderSize;
        `}
      float halfPixelSize = lineSizeScaled * 0.5;
      // Calculate a pixel offset from the edge of the pixel, s.t. we keep the line aligned
      // to pixels if it has a full pixel size. Since pixel aligned biases to the bottom-left,
      // we bias the size to the right (for odd sizes) to balance out the bias. Grow sub-pixel
      // sizes towards the left or right s.t. there is a smooth transition (e.g. from 2 to 3 px).
      float halfWholePixelSize = floor(lineSizeScaled) * 0.5;
      float halfPixelSizeInt = floor(halfWholePixelSize);

      // Sub-pixel offset if we need to grow sub-pixels to the left
      float subpixelOffset = -fract(lineSizeScaled) * float(halfWholePixelSize > 0.0);

      // Pixel offset aligning to whole pixels and adding subpixel offset if needed
      float pixelOffset = -halfPixelSizeInt + subpixelOffset;

      // Compute full ndc offset, adding 1px padding for doing anti-aliasing and the border size
      float padding = 1.0 + borderSizeScaled;
      vec2 ndcOffset = (pixelOffset - padding + uv0.x * (lineSizeScaled + padding + padding)) * pixelToNDC;

      // Offset x/y from the center of the line in screen space
      projectedPosition.xy += perpendicularScreenSpaceDirection * ndcOffset * projectedPosition.w;

      // Compute a coverage varying which we can use in the fragment shader to determine
      // how much a pixel is actually covered by the line (i.e. to anti alias the line).
      // This works by computing two coordinates that can be linearly interpolated and then
      // subtracted to find out how far away from the line edge we are.
      float edgeDirection = (uv0.x * 2.0 - 1.0);

      float halfBorderSize = 0.5 * borderSizeScaled;
      float halfPixelSizeAndBorder = halfPixelSize + halfBorderSize;
      float outerEdgeCoverageSampler = edgeDirection * (halfPixelSizeAndBorder + halfBorderSize + 1.0);

      float isOneSided = float(lineSizeScaled < 2.0 && borderSize < 2.0);

      coverageSampling = vec4(
        // Edge coordinate
        outerEdgeCoverageSampler,

        // Border edge coordinate
        outerEdgeCoverageSampler - halfPixelSizeAndBorder * isOneSided,

        // Line offset
        halfPixelSize - 0.5,

        // Border offset
        halfBorderSize - 0.5 + halfPixelSizeAndBorder * (1.0 - isOneSided)
      );

      lineSizes = vec2(lineSizeScaled, borderSizeScaled);

      gl_Position = projectedPosition;
    }
  `),i.uniforms.add([new $i("uColor",s=>_o(s.color)),new $i("borderColor",s=>_o(s.borderColor))]),r.hasMultipassGeometry&&(i.include(mu,r),i.uniforms.add(new tr("inverseViewport",(s,a)=>a.inverseViewport))),i.code.add(w`
    void main() {
      ${r.hasMultipassGeometry?"if( geometryDepthTest(gl_FragCoord.xy * inverseViewport, depth) ){ discard; }":""}

      // Mix between line and border coverage offsets depending on whether we need
      // a border (based on the sidedness).
      vec2 coverage = min(1.0 - clamp(abs(coverageSampling.xy) - coverageSampling.zw, 0.0, 1.0), lineSizes);

      // Mix between border and line color based on the line coverage (conceptually the line
      // blends on top of the border background).
      //
      // Anti-alias by blending final result using the full (including optional border) coverage
      // and the color alpha
      float borderAlpha = uColor.a * borderColor.a * coverage.y;
      float colorAlpha = uColor.a * coverage.x;

      float finalAlpha = mix(borderAlpha, 1.0, colorAlpha);

    ${r.depthHudEnabled?w`
      if (finalAlpha < 0.01) {
        discard;
      }
      `:w`
      vec3 finalRgb = mix(borderColor.rgb * borderAlpha, uColor.rgb, colorAlpha);
      gl_FragColor = vec4(finalRgb, finalAlpha);
      `}
  }
  `),e}function _o(r){return d(r)?r:Ji}const bo=be(),Ug=Object.freeze(Object.defineProperty({__proto__:null,build:Ah},Symbol.toStringTag,{value:"Module"}));let Ih=class Th extends vs{initializeConfiguration(e,t){t.spherical=e.viewingMode===Ut.Global}initializeProgram(e){return new Ss(e.rctx,Th.shader.get().build(this.configuration),ls)}setPipelineState(e){const t=e?io.ALWAYS:io.LESS;return this.configuration.depthHudEnabled?zt({depthTest:{func:t},depthWrite:xs}):zt({blending:vp(Mr.ONE,Mr.SRC_ALPHA,Mr.ONE_MINUS_SRC_ALPHA,Mr.ONE_MINUS_SRC_ALPHA),depthTest:{func:t},colorWrite:Gi})}initializePipeline(){return this.setPipelineState(this.configuration.hasMultipassGeometry)}};Ih.shader=new bs(Ug,()=>wr(()=>Promise.resolve().then(()=>O_),void 0));let je=class extends Xa{constructor(){super(...arguments),this.screenCenterOffsetUnitsEnabled=ns.World,this.spherical=!1,this.occlusionTestEnabled=!0,this.hasVerticalOffset=!1,this.hasScreenSizePerspective=!1,this.depthHudEnabled=!1,this.depthHudAlignStartEnabled=!1,this.hasSlicePlane=!1,this.hasMultipassGeometry=!1}};y([$({count:ns.COUNT})],je.prototype,"screenCenterOffsetUnitsEnabled",void 0),y([$()],je.prototype,"spherical",void 0),y([$()],je.prototype,"occlusionTestEnabled",void 0),y([$()],je.prototype,"hasVerticalOffset",void 0),y([$()],je.prototype,"hasScreenSizePerspective",void 0),y([$()],je.prototype,"depthHudEnabled",void 0),y([$()],je.prototype,"depthHudAlignStartEnabled",void 0),y([$()],je.prototype,"hasSlicePlane",void 0),y([$()],je.prototype,"hasMultipassGeometry",void 0),y([$({constValue:!0})],je.prototype,"hasSliceInVertexProgram",void 0),y([$({constValue:!1})],je.prototype,"isDraped",void 0);let Vr=class Ea extends Ja{get uniqueMaterialIdentifier(){return this._uniqueMaterialIdentifier}constructor(e){super(e,new Lh),this._configuration=new je,this._uniqueMaterialIdentifier=Ea.uniqueMaterialIdentifier(this.parameters)}getPassParameters(){return this.parameters}getConfiguration(e,t){const i=(t==null?void 0:t.slot)!==Y.LINE_CALLOUTS;return this._configuration.occlusionTestEnabled=this.parameters.occlusionTest,this._configuration.hasVerticalOffset=d(this.parameters.verticalOffset),this._configuration.hasScreenSizePerspective=d(this.parameters.screenSizePerspective),this._configuration.depthHudEnabled=i,this._configuration.depthHudAlignStartEnabled=!!this.parameters.depthHUDAlignStart,this._configuration.screenCenterOffsetUnitsEnabled=this.parameters.centerOffsetUnits==="screen"?ns.Screen:ns.World,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.hasMultipassGeometry=t.multipassGeometry.enabled,this._configuration}intersect(){}requiresSlot(e,t){if(t===O.Color)switch(e){case Y.LINE_CALLOUTS:case Y.LINE_CALLOUTS_HUD_DEPTH:return!0}return!1}createGLMaterial(e){return new Fg(e)}createBufferWriter(){return new Bg}validateParameters(e){const t=Ea.uniqueMaterialIdentifier(e);t!==this._uniqueMaterialIdentifier&&(this._uniqueMaterialIdentifier=t)}static uniqueMaterialIdentifier(e){return JSON.stringify({screenOffset:e.screenOffset||[0,0],centerOffsetUnits:e.centerOffsetUnits||"world"})}},Fg=class extends Qa{beginSlot(e){return this.ensureTechnique(Ih,e)}},Lh=class extends Ql{constructor(){super(...arguments),this.screenOffset=Ua,this.color=[0,0,0,1],this.size=1,this.occlusionTest=!1,this.shaderPolygonOffset=1e-5,this.depthHUDAlignStart=!1,this.centerOffsetUnits="world",this.hasSlicePlane=!1}};const Ng=Fi().vec3f(f.POSITION).vec3f(f.NORMAL).vec2f(f.UV0).vec4f(f.AUXPOS1),vo=[mi(0,0),mi(1,0),mi(0,1),mi(1,0),mi(1,1),mi(0,1)];let Bg=class{constructor(){this.vertexBufferLayout=Ng}allocate(e){return this.vertexBufferLayout.createBuffer(e)}elementCount(e){return 6*e.indices.get(f.POSITION).length}write(e,t,i,s,a){Kl(i.indices.get(f.POSITION),i.vertexAttributes.get(f.POSITION).data,e,s.position,a,6),Wu(i.indices.get(f.NORMAL),i.vertexAttributes.get(f.NORMAL).data,t,s.normal,a,6),eh(i.indices.get(f.AUXPOS1),i.vertexAttributes.get(f.AUXPOS1).data,s.auxpos1,a,6);for(let n=0;n<vo.length;++n)s.uv0.setVec(a+n,vo[n])}},Dh=class $h extends mt{constructor(e,t){super(e,null,t,Hg),this._elevationOptions={supportsOffsetAdjustment:!0,supportsOnTheGround:!1},this.ensureDrapedStatus(!1)}async doLoad(){this._material=new Vr(this._materialParameters),this._context.stage.add(this._material)}destroy(){super.destroy(),this._context.stage.remove(this._material),this._material=null}_perInstanceMaterialParameters(e){const t=this._materialParameters;return t.screenOffset=e.screenOffset||Ua,t.centerOffsetUnits=e.centerOffsetUnits||"world",t}get _materialParameters(){const e=new Lh,t=this.symbol,i=t.callout;if(e.color=d(i.color)?ue.toUnitRGBA(i.color):[0,0,0,0],e.color[3]*=this._getLayerOpacity(),e.size=Re(i.size||0),t.verticalOffset){const{screenLength:n,minWorldLength:o,maxWorldLength:l}=t.verticalOffset;e.verticalOffset={screenLength:Re(n),minWorldLength:o||0,maxWorldLength:d(l)?l:1/0}}e.borderColor=d(i.border)&&d(i.border.color)?ue.toUnitRGBA(i.border.color):null;const s=t.symbolLayers.getItemAt(0).type==="object",a=t.type==="label-3d";return e.occlusionTest=!s,e.shaderPolygonOffset=s?0:void 0,e.depthHUDAlignStart=a,e.hasSlicePlane=this._context.slicePlaneEnabled,e.screenSizePerspective=this._context.screenSizePerspectiveEnabled?this._context.sharedResources.screenSizePerspectiveSettings:null,e}_defaultElevationInfoNoZ(){return kg}createGraphics3DGraphic(e){const t=e.renderingInfo,i=e.graphic,s=this.setGraphicElevationContext(i,new Je,t.elevationOffset||0),a=t.symbol,n=this._elevationContext.mode==="on-the-ground"&&(a.type==="cim"||!a.symbolLayers.some(l=>l.type==="object"||l.type==="text"));if(a.type!=="label-3d"&&n||a.type==="point-3d"&&a.symbolLayers.every(l=>l.type==="text"&&!Il(l)))return null;const o=fs(i.geometry);return b(o)?null:this._createAs3DShape(o,s,t,i.uid)}layerOpacityChanged(){d(this._material)&&this._material.setParameters(this._materialParameters)}layerElevationInfoChanged(e,t,i){const s=this._elevationContext.mode,a=Ar($h.elevationModeChangeTypes,i,s);return a!==q.UPDATE||e.forEach(n=>{const o=t(n);d(o)&&this.updateGraphicElevationContext(n.graphic,o)}),a}slicePlaneEnabledChanged(){return b(this._material)||this._material.setParameters({hasSlicePlane:this._context.slicePlaneEnabled}),!0}physicalBasedRenderingChanged(){return!0}pixelRatioChanged(){return!0}skipHighSymbolLodsChanged(){return!0}setGraphicElevationContext(e,t,i=0){const s=super.setGraphicElevationContext(e,t);return s.addOffsetRenderUnits(i),s}updateGraphicElevationContext(e,t){this.setGraphicElevationContext(e,t.elevationContext,d(t.metadata)?t.metadata.elevationOffset:0),t.needsElevationUpdates=Ye(t.elevationContext.mode)}computeComplexity(){return{primitivesPerFeature:2,primitivesPerCoordinate:0,drawCallsPerFeature:0,estimated:!1,memory:un.memory}}_createVertexData(e){const{translation:t,centerOffset:i}=e,s=new N(t?[t[0],t[1],t[2]]:[0,0,0],3,!0),a=new N(i?[i[0],i[1],i[2],i[3]]:[0,0,0,1],4,!0);return[[f.POSITION,s],[f.NORMAL,new N([0,0,1],3,!0)],[f.AUXPOS1,a]]}_getOrCreateMaterial(e){const t=this._perInstanceMaterialParameters(e),i=Vr.uniqueMaterialIdentifier(t);if(d(this._material)&&i===this._material.uniqueMaterialIdentifier)return{material:this._material,isUnique:!1};if(d(e.materialCollection)){let s=e.materialCollection.get(i);return b(s)&&(s=new Vr(t),e.materialCollection.add(i,s)),{material:s,isUnique:!1}}return{material:new Vr(t),isUnique:!0}}_createAs3DShape(e,t,i,s){const a=this._context.layer.uid,n=this._context.stage.renderView.getObjectAndLayerIdColor({graphicUid:s,layerUid:a}),o=this._getOrCreateMaterial(i),l=new Nt(o.material,this._createVertexData(i),jg,null,li.Point,n),h=gn(this._context,e,l,t,s);if(b(h))return null;const c=new gt(this,h.object,[l],o.isUnique?[o.material]:null,null,_r,t);return c.metadata=new Sh(i.elevationOffset),c.alignedSampledElevation=h.sampledElevation,c.needsElevationUpdates=Ye(t.mode),br(c,e,this._context.elevationProvider),c}};Dh.elevationModeChangeTypes={definedChanged:q.UPDATE,staysOnTheGround:q.UPDATE,onTheGroundChanged:q.RECREATE};const Ws=[0],jg=[[f.POSITION,Ws],[f.NORMAL,Ws],[f.AUXPOS1,Ws]],kg={mode:"relative-to-ground",offset:0},Hg={ignoreDrivers:!0,renderPriority:0,renderPriorityStep:1};let Wg=class{constructor(e,t,i=C(),s=ms(),a=be(),n="world",o=0,l=null){this.renderer=e,this.symbol=t,this.translation=i,this.centerOffset=s,this.screenOffset=a,this.centerOffsetUnits=n,this.elevationOffset=o,this.materialCollection=l}};const So=di.getLogger("esri.views.3d.layers.graphics.Graphics3DCalloutSymbolLayerFactory");function qg(r,e){if(!Tl(r))return So.error("Graphics3DCalloutSymbolLayerFactory#make",`symbol of type '${r.type}' does not support callouts`),null;if(!r.callout)return null;const t=Yg[r.callout.type];return t?new t(r,e):(So.error("Graphics3DCalloutSymbolLayerFactory#make",`unknown or unsupported callout type ${r.callout.type}`),null)}const Yg={line:Dh},xo=new Pc(Array,r=>Ll(r,nd),null,10,5),Zg=pt();let Xg=class{get labelLayers(){return this._labelLayers}get extent(){return this._extent}constructor(e,t,i,s,a){this.graphic=e,this.graphics3DSymbol=t,this.layers=i,this._labelLayers=new Array,this._auxiliaryLayers=new Array,this._visibilityFlags=Jg(ye._COUNT,ze._COUNT),this._featureExpressionFeature=null,this._optimizedGeometry={geometry:null,hasZ:!1,hasM:!1},this._extent=null,this.isElevationSource=!1,++t.referenced,this._featureExpressionFeature=a?fu(a,e,s):null;for(const n of i)d(n)&&(this.isElevationSource=this.isElevationSource||n.isElevationSource)}initialize(e,t){this._layer=t,this._stage=e,this._forEachSymbolLayerGraphic(i=>{i.initialize(e,t),i.setVisibility(this.isVisible())})}destroy(){this._forEachSymbolLayerGraphic(e=>e.destroy()),this.layers=null,this._auxiliaryLayers=null,--this.graphics3DSymbol.referenced,this.graphics3DSymbol=null}get destroyed(){return this.layers==null}clearLabelGraphics(){this._forEachLabelGraphic(e=>e.destroy()),this._labelLayers.length=0}addLabelGraphic(e,t,i){this._labelLayers.push(e),e.initialize(t,i),e.setVisibility(this.isVisible(ye.LABEL))}addAuxiliaryGraphic(e){this._auxiliaryLayers.push(e),this._layer&&(e.initialize(this._stage,this._layer),e.setVisibility(this.isVisible()))}get isDraped(){let e=!1;return this._forEachSymbolLayerGraphic(t=>{t.type==="draped"&&(e=!0)}),e}isVisible(e=ye.GRAPHIC,t){for(let i=0;i<=e;i++){const s=this._visibilityFlags[i];for(let a=0;a<s.length;++a)if(s[a]===!1&&a!==t)return!1}return!0}hasVisibilityFlag(e,t){return this._visibilityFlags[t][e]!=null}setVisibilityFlag(e,t,i){const s=this.isVisible(i);this._visibilityFlags[i][e]=t;const a=this.isVisible(i);if(s===a)return!1;if(i===ye.LABEL)this._forEachLabelGraphic(n=>n.setVisibility(a));else{this._forEachSymbolLayerGraphic(o=>o.setVisibility(a));const n=this.isVisible(ye.LABEL);this._forEachLabelGraphic(o=>o.setVisibility(n))}return!0}clearVisibilityFlag(e,t=ye.GRAPHIC){return this.setVisibilityFlag(e,void 0,t)}computeExtent(e){if(!this._extent){const t=this.graphic.geometry;if(b(t))return!1;this._extent=pt(),Kd(t,this._extent);const i=t.spatialReference;if(!_l(i,e)&&!rs(this._extent,i,this._extent,e))return this._extent=null,!1}return!0}getAsOptimizedGeometry(e,t){return d(this._optimizedGeometry.geometry)&&this._optimizedGeometry.hasZ===e&&this._optimizedGeometry.hasM===t||(this._optimizedGeometry.geometry=this._convertGraphicToOptimizedGeometry(this.graphic,e,t),this._optimizedGeometry.hasZ=e,this._optimizedGeometry.hasM=t),this._optimizedGeometry.geometry}_convertGraphicToOptimizedGeometry(e,t,i){let s=e.geometry;return s.type!=="mesh"&&s.type!=="extent"||(s=Fa.fromExtent(s.type==="mesh"?s.extent:s)),od(s,t,i)}get usedMemory(){let e=ld(this.graphic.attributes);return this._forEachSymbolLayerGraphic(t=>{const i=t.graphics3DSymbolLayer.complexity;if(b(i))return;const s=t.type==="draped"?i.memory.draped:i.memory;e+=s.bytesPerFeature,s.bytesPerCoordinate&&(e+=eu(this.graphic.geometry)*s.bytesPerCoordinate)}),e}computeAttachmentOrigin(){const e={render:{origin:C(),num:0},draped:{origin:be(),num:0}};for(const t of this.layers)b(t)||t.computeAttachmentOrigin(e);return e.render.num>1&&ae(e.render.origin,e.render.origin,1/e.render.num),e.draped.num>1&&zi(e.draped.origin,e.draped.origin,1/e.draped.num),e}async getProjectedBoundingBox(e,t,i,s,a){return a||(a={boundingBox:null,requiresDrapedElevation:!1,screenSpaceObjects:[]}),a.boundingBox?B(a.boundingBox):a.boundingBox=B(),a.requiresDrapedElevation=!1,await Dl(this.layers,async n=>{if(b(n))return;const o=n.type==="draped"?t:e,l=xo.acquire(),h=await n.getProjectedBoundingBox(o,i,a.screenSpaceObjects,s,l);isFinite(h[2])&&isFinite(h[5])||(a.requiresDrapedElevation=!0),h&&lr(a.boundingBox,l),xo.release(l)}),hd(a.boundingBox)||cd(Na(a.boundingBox,Zg))?a:null}needsElevationUpdates(){for(const e of this.layers)if(d(e)&&(e.type==="object3d"||e.type==="lod-instance")&&e.needsElevationUpdates)return!0;for(const e of this._labelLayers)if(e&&e.needsElevationUpdates)return!0;return!1}alignWithElevation(e,t,i){this._forEachRenderedGraphic(s=>{s.type!=="object3d"&&s.type!=="lod-instance"||s.alignWithElevation(e,t,this._featureExpressionFeature,i)})}alignWithAbsoluteElevation(e,t,i){this._forEachRenderedGraphic(s=>{s.type==="object3d"&&s.alignWithAbsoluteElevation(e,t,i)})}addObjectStateSet(e,t){this._forEachSymbolLayerGraphic(i=>i.addObjectState(e,t))}removeObjectState(e){this._forEachSymbolLayerGraphic(t=>t.removeObjectState(e))}_forEachGraphicList(e,t){e.forEach(i=>i&&t(i))}_forEachSymbolLayerGraphic(e){this._forEachGraphicList(this.layers,e),this._forEachGraphicList(this._auxiliaryLayers,e)}_forEachLabelGraphic(e){this._forEachGraphicList(this._labelLayers,e)}_forEachRenderedGraphic(e){this._forEachSymbolLayerGraphic(e),this._forEachLabelGraphic(e)}};function Jg(r,e){const t=new Array(r);for(let i=0;i<t.length;i++)t[i]=new Array(e);return t}function Co(r){const e=[[f.POSITION,r.indices]],t=[[f.POSITION,new N(r.attributeData.position,3,!0)]];return d(r.attributeData.color)&&(t.push([f.COLOR,new N(r.attributeData.color,4,!0)]),e.push([f.COLOR,new Array(r.indices.length).fill(0)])),d(r.attributeData.uvMapSpace)&&(t.push([f.UVMAPSPACE,new N(r.attributeData.uvMapSpace,4,!0)]),e.push([f.UVMAPSPACE,r.indices])),d(r.attributeData.boundingRect)&&(t.push([f.BOUNDINGRECT,new N(r.attributeData.boundingRect,9,!0)]),e.push([f.BOUNDINGRECT,r.indices])),new Nt(r.material,t,e,r.mapPositions,li.Mesh,r.attributeData.objectAndLayerIdColor)}function Po(r){const e=[[f.POSITION,r.indices],[f.UV0,r.indices]],t=[[f.POSITION,new N(r.attributeData.position,3,!0)],[f.UV0,new N(r.attributeData.uv0,2,!0)]];return new Nt(r.material,t,e,r.mapPositions)}function Sr(r){switch(r.type){case"extent":if(r instanceof Ba)return Fa.fromExtent(r);break;case"polygon":return r}return null}let ws=class{constructor(e,t,i){this.renderData=e,this.layerUid=t,this.graphicUid=i,this.outGeometries=new Array}};function mn(r,e,t,i){const s=ph(r.rings,!!r.hasZ,gh.CCW_IS_HOLE),a=ci(s.position.length),n=yu(s.position,r.spatialReference,0,a,0,s.position,0,s.position.length/3,e,t,i),o=n!=null;return new em(s.position,a,zh(s.polygons,s.position,a),Mh(s.outlines,s.position,a),o,n)}function Gh(r,e){const t=ph(r.rings,!1,gh.CCW_IS_HOLE),i=ut(t.position,r.spatialReference,0,t.position,e,0,t.position.length/3);for(let s=2;s<t.position.length;s+=3)t.position[s]=Yl;return{position:t.position,polygons:zh(t.polygons,t.position),outlines:Mh(t.outlines,t.position),projectionSuccess:i}}function Mh(r,e,t=null){return r.filter(({count:i})=>i>1).map(({index:i,count:s})=>{const a=3*i,n=3*s;return d(t)?new Vh(i,s,wt(e,a,n),wt(t,a,n)):new fn(i,s,wt(e,a,n))})}function zh(r,e,t=null){const i=new Array;for(const{index:s,count:a,holeIndices:n,pathLengths:o}of r){if(a<=1)continue;const l=3*s,h=3*a,c=n.map(p=>p-s),u=d(t)?new Qg(s,a,wt(e,3*s,3*a),wt(t,l,h),c,o):new Kg(s,a,wt(e,3*s,3*a),c,o);i.push(u)}return i}let fn=class{constructor(e,t,i){this.index=e,this.count=t,this.position=i}},Vh=class extends fn{constructor(e,t,i,s){super(e,t,i),this.mapPositions=s}},Qg=class extends Vh{constructor(e,t,i,s,a,n){super(e,t,i,s),this.holeIndices=a,this.pathLengths=n}},Kg=class extends fn{constructor(e,t,i,s,a){super(e,t,i),this.holeIndices=s,this.pathLengths=a}},em=class{constructor(e,t,i,s,a,n){this.position=e,this.mapPositions=t,this.polygons=i,this.outlines=s,this.projectionSuccess=a,this.sampledElevation=n}};const tm=["polygon","extent"];class im extends mt{constructor(e,t,i,s){super(e,t,i,s),this.ensureDrapedStatus(!1)}async doLoad(){if(!this._drivenProperties.size){const o=ys(this._getSymbolSize());if(o)throw new We("graphics3dextrudesymbollayer:invalid-size",o)}const e=ie(this.symbolLayer,"material","color"),t=this._getCombinedOpacityAndColor(e),i=xt(t),s=t[3],a=s<1||this.needsDrivenTransparentPass,n={usePBR:this._context.physicalBasedRenderingEnabled,isSchematic:!0,diffuse:i,ambient:i,opacity:s,transparent:a,cullFace:a?Ae.None:Ae.Back,hasVertexColors:!0,hasSlicePlane:this._context.slicePlaneEnabled,castShadows:this.symbolLayer.castShadows,offsetTransparentBackfaces:!0};this._material=new yr(n),this._bottomMaterial=new yr({...n,cullFace:Ae.Back}),this._context.stage.add(this._material),this._context.stage.add(this._bottomMaterial)}destroy(){super.destroy(),this._material&&(this._context.stage.remove(this._material),this._context.stage.remove(this._bottomMaterial))}createGraphics3DGraphic(e){const t=e.graphic;if(!this._validateGeometry(t.geometry,tm,this.symbolLayer.type))return null;const i=this._getVertexOpacityAndColor(e.renderingInfo,255),s=this.setGraphicElevationContext(t,new Je);return this._createAs3DShape(t,e.renderingInfo,i,s,t.uid)}layerOpacityChanged(e,t){const i=ie(this.symbolLayer,"material","color"),s=this._getCombinedOpacity(i),a=s<1||this.needsDrivenTransparentPass;this._material.setParameters({opacity:s,transparent:a}),this._bottomMaterial.setParameters({opacity:s,transparent:a});const n=this._getLayerOpacity();e.forEach(o=>{const l=t(o);d(l)&&l.layerOpacityChanged(n,this._context.isAsync)})}layerElevationInfoChanged(e,t){return this.updateGraphics3DGraphicElevationInfo(e,t,kt)}slicePlaneEnabledChanged(e,t){return this._material.setParameters({hasSlicePlane:this._context.slicePlaneEnabled}),this._bottomMaterial.setParameters({hasSlicePlane:this._context.slicePlaneEnabled}),e.forEach(i=>{const s=t(i);d(s)&&s.slicePlaneEnabledChanged(this._context.slicePlaneEnabled,this._context.isAsync)}),!0}physicalBasedRenderingChanged(){return this._material.setParameters({usePBR:this._context.physicalBasedRenderingEnabled,isSchematic:!0}),this._bottomMaterial.setParameters({usePBR:this._context.physicalBasedRenderingEnabled,isSchematic:!0}),!0}pixelRatioChanged(){return!0}skipHighSymbolLodsChanged(){return!0}_getExtrusionSize(e){let t;return t=e.size&&this._drivenProperties.size?mg(e.size,2)??0:this._getSymbolSize(),t/=this._context.renderCoordsHelper.unitInMeters,t}applyRendererDiff(e,t){return this._drivenPropertiesChanged(t)?H.Recreate_Symbol:H.Recreate_Graphics}async queryForSnapping(e,t,i,s){const a=this._getExtrusionSize(i)*this._context.renderCoordsHelper.unitInMeters/bl(t),{objectId:n,target:o}=e,l=Ds(o);switch(l.z=(l.z??0)+a,e.type){case"edge":{const{start:h,end:c}=e,u=Ds(h),p=Ds(c);return u.z=(u.z??0)+a,p.z=(p.z??0)+a,[ao(n,l,1/0,u,p)]}case"vertex":return[Rp(n,l,1/0),ao(n,o,1/0,o,l)];default:return[]}}_getSymbolSize(){return this.symbolLayer.size??1}_createAs3DShape(e,t,i,s,a){const n=Sr(e.geometry);if(b(n))return null;if(n.rings.length===0||!n.rings.some(Ue=>Ue.length>0))return this._logGeometryValidationWarnings(n.rings,"rings","ExtrudeSymbol3DLayer"),null;const o=mn(n,this._context.elevationProvider,this._context.renderCoordsHelper,s);this._logGeometryCreationWarnings(o,n.rings,"rings","ExtrudeSymbol3DLayer");const l=fs(n);if(b(l))return null;const h=new Array,c=_e(),u=j(),p=C(),m=this._context.renderCoordsHelper.viewingMode===Ut.Global;m||this._context.renderCoordsHelper.worldUpAtPosition(null,p),hi(n.spatialReference,[l.x,l.y,0],u,this._context.renderCoordsHelper.spatialReference);const g=j();ja(g,u);const _=Cs();dd(_,g);const{polygons:S,mapPositions:P,position:x}=o,A=x.length/3,E=new Float64Array(3*A*6),T=new Float64Array(3*A*6),G=new Float64Array(3*A*6),z=new Float64Array(1*A*6);let R=0;for(let Ue=0;Ue<S.length;++Ue){const Wt=S[Ue],Rs=Wt.count;if(this._context.clippingExtent&&(B(c),Ve(c,Wt.mapPositions),!it(c,this._context.clippingExtent)))continue;const Ir=pr(Wt.mapPositions,Wt.holeIndices,3);if(Ir.length===0)continue;const Sc=3*Rs*2+Ir.length,As=new Array(Sc),Is=new Array(Ir.length),Tr=6*Rs,Ts=3*E.BYTES_PER_ELEMENT,Lr=new cs(E.buffer,R*Ts,Ts,(R+Tr)*Ts),Ls=3*T.BYTES_PER_ELEMENT,Dr=new cs(T.buffer,R*Ls,Ls,(R+Tr)*Ls),Sn=new Float64Array(G.buffer,3*R*G.BYTES_PER_ELEMENT,3*Tr),xn=new Float64Array(z.buffer,1*R*z.BYTES_PER_ELEMENT,1*Tr),xc=this._getExtrusionSize(t);rm(x,P,Ir,Wt,Lr.typedBuffer,Sn,Dr.typedBuffer,xn,As,Is,xc,p,m),uh(Lr,Lr,g),Ca(Dr,Dr,_),R+=6*Rs;const Cn=this._context.stage.renderView.getObjectAndLayerIdColor({graphicUid:a,layerUid:this._context.layer.uid}),Pn=new lm(Lr.typedBuffer,Sn,Dr.typedBuffer,xn);h.push(wo(this._material,As,As.length-Is.length,Pn,i,Cn)),h.push(wo(this._bottomMaterial,Is,0,Pn,i,Cn))}if(h.length===0)return null;const L=new ui({geometries:h,metadata:{layerUid:this._context.layer.uid,graphicUid:a,isElevationSource:!0}});L.transformation=u;const M=hh(this.symbolLayer,{opacity:this._getLayerOpacity()}),le=d(M)?{baseMaterial:this._material,edgeMaterials:[M],properties:{mergeGeometries:!0,hasSlicePlane:this._context.slicePlaneEnabled}}:null,X=new gt(this,L,h,null,null,nm,s,le);return X.alignedSampledElevation=o.sampledElevation,X.needsElevationUpdates=kt(s.mode),X}}function wo(r,e,t,i,s,a){const n=new Array(e.length).fill(0),o=[[f.POSITION,new N(i.positions,3,!0)],[f.NORMAL,new N(i.normals,3,!0)],[f.COLOR,new N(s,4,!0)],[f.SIZE,new N(i.heights,1,!0)]],l=[[f.POSITION,e],[f.NORMAL,e],[f.COLOR,n]];return new Nt(r,o,l,i.elevation,li.Mesh,a,t)}function rm(r,e,t,i,s,a,n,o,l,h,c,u,p){const m=t.length/3;let g=0,_=2*i.count;sm(r,e,i.index,i.count,t,0,m,s,a,n,o,l,h,_,c,u,p);let S=2*i.count;_=0,Oo(s,a,o,n,g,i.pathLengths[0],i.count,S,l,_,c),S+=4*i.pathLengths[0],_+=2*i.pathLengths[0],g+=i.pathLengths[0];for(let P=1;P<i.pathLengths.length;++P)Oo(s,a,o,n,g,i.pathLengths[P],i.count,S,l,_,c),S+=4*i.pathLengths[P],_+=2*i.pathLengths[P],g+=i.pathLengths[P]}function sm(r,e,t,i,s,a,n,o,l,h,c,u,p,m,g,_,S){de(Se,_);const P=g>0?1:-1;let x=3*t,A=0,E=3*A,T=i,G=3*T;for(let L=0;L<i;++L)S&&(Se[0]=r[x+0],Se[1]=r[x+1],Se[2]=r[x+2],ne(Se,Se)),o[E+0]=r[x+0],o[E+1]=r[x+1],o[E+2]=r[x+2],l[E+0]=e[x+0],l[E+1]=e[x+1],l[E+2]=e[x+2],h[E+0]=-P*Se[0],h[E+1]=-P*Se[1],h[E+2]=-P*Se[2],c[A]=0,o[G+0]=r[x+0]+g*Se[0],o[G+1]=r[x+1]+g*Se[1],o[G+2]=r[x+2]+g*Se[2],l[G+0]=e[x+0],l[G+1]=e[x+1],l[G+2]=e[x+2],h[G+0]=P*Se[0],h[G+1]=P*Se[1],h[G+2]=P*Se[2],c[T]=g,E+=3,G+=3,x+=3,A+=1,T+=1;x=3*a,E=0,G=3*m;const z=g<0?Lo:To,R=g<0?To:Lo;for(let L=0;L<n;++L)p[E+0]=s[x+z[0]],p[E+1]=s[x+z[1]],p[E+2]=s[x+z[2]],u[G+0]=s[x+R[0]]+i,u[G+1]=s[x+R[1]]+i,u[G+2]=s[x+R[2]]+i,E+=3,G+=3,x+=3}function Ur(r,e,t,i,s,a,n){i[a]=i[n],n*=3,r[(a*=3)+0]=r[n+0],r[a+1]=r[n+1],r[a+2]=r[n+2],e[a+0]=e[n+0],e[a+1]=e[n+1],e[a+2]=e[n+2],t[a+0]=s[0],t[a+1]=s[1],t[a+2]=s[2]}const ji=C();function Oo(r,e,t,i,s,a,n,o,l,h,c){let u=s,p=s+1,m=s+n,g=s+n+1,_=o,S=o+1,P=o+2*a,x=o+2*a+1;c<0&&(u=s+n+1,g=s),h*=3;for(let A=0;A<a;++A)A===a-1&&(c>0?(p=s,g=s+n):(p=s,u=s+n)),am(r,u,p,m,ji),Ur(r,e,i,t,ji,_,u),Ur(r,e,i,t,ji,S,p),Ur(r,e,i,t,ji,P,m),Ur(r,e,i,t,ji,x,g),l[h++]=_,l[h++]=P,l[h++]=x,l[h++]=_,l[h++]=x,l[h++]=S,u++,p++,m++,g++,_+=2,S+=2,P+=2,x+=2}const qs=C(),Eo=C(),Ro=C(),Ao=C(),Io=C();function am(r,e,t,i,s){e*=3,t*=3,i*=3,D(qs,r[e++],r[e++],r[e++]),D(Eo,r[t++],r[t++],r[t++]),D(Ro,r[i++],r[i++],r[i++]),oe(Ao,Eo,qs),oe(Io,Ro,qs),Et(s,Io,Ao),ne(s,s)}const bi=C();function nm(r,e,t,i,s){const a=r.stageObject,n=a.geometries,o=n.length,l=e.mode!=="absolute-height";let h=0;const c=a.transformation,u=ud(j(),c);for(let p=0;p<o;p+=2){const m=n[p];if(!hn(m))continue;const g=m.getMutableAttribute(f.POSITION).data,_=m.vertexAttributes.get(f.SIZE).data,S=new Wl(m.mapPositions),P=g.length/3;let x=0,A=!1,E=0;for(let T=0;T<P;T++){bi[0]=g[x],bi[1]=g[x+1],bi[2]=g[x+2],i(S,Fr),l&&(E+=Fr.sampledElevation),Ie.TESTS_DISABLE_OPTIMIZATIONS?(D(Te,S.array[S.offset+0],S.array[S.offset+1],Fr.z+_[x/3]),d(t)&&s.toRenderCoords(Te,t,Te),ge(Te,Te,u)):(D(Te,g[x+0],g[x+1],g[x+2]),ge(Te,Te,c),s.setAltitude(Te,Fr.z+_[x/3]),ge(Te,Te,u)),g[x]=Te[0],g[x+1]=Te[1],g[x+2]=Te[2];const G=om/s.unitInMeters;(Math.abs(bi[0]-g[x])>=G||Math.abs(bi[1]-g[x+1])>=G||Math.abs(bi[2]-g[x+2])>=G)&&(A=!0),S.offset+=3,x+=3}A&&(m.invalidateBoundingInfo(),a.geometryVertexAttrsUpdated(n[p]),n[p+1].invalidateBoundingInfo(),a.geometryVertexAttrsUpdated(n[p+1])),h+=E/P}return h/o}const Te=C(),Se=C(),Fr=new Rr,To=[0,2,1],Lo=[0,1,2],om=.01;let lm=class{constructor(e,t,i,s){this.positions=e,this.elevation=t,this.normals=i,this.heights=s}},Os=class{constructor(e,t,i,s){this.graphics3DSymbolLayer=e,this.renderGeometries=t,this.boundingBox=i,this._drapeSourceRenderer=s,this.type="draped",this.stage=null,this._visible=!1,this._addedToStage=!1,this.isElevationSource=!1}initialize(e){this.stage=e}setVisibility(e){if(this.stage!=null&&this._visible!==e){if(this._visible=e,e&&!this._addedToStage)return this._addedToStage=!0,void this._drapeSourceRenderer.addGeometries(this.renderGeometries,jn.ADD);if(e||this._addedToStage){for(const t of this.renderGeometries)t.visible=this._visible;this._drapeSourceRenderer.modifyGeometries(this.renderGeometries,Vs.VISIBILITY)}}}destroy(){this.stage&&this._addedToStage&&this._drapeSourceRenderer.removeGeometries(this.renderGeometries,jn.REMOVE),this._addedToStage=!1,this._visible=!1,this.stage=null}getCenterObjectSpace(e=C()){return D(e,0,0,0)}getBoundingBoxObjectSpace(e=_e()){return B(e)}addObjectState(e,t){e===Ct.Highlight&&(this.renderGeometries.forEach(i=>{const s=i.geometry.addHighlight();t.addRenderGeometry(i,s,this)}),this._addedToStage&&this._drapeSourceRenderer.modifyGeometries(this.renderGeometries,Vs.HIGHLIGHT))}removeObjectState(e){this.renderGeometries.forEach(t=>{e.removeRenderGeometry(t)})}removeRenderGeometryObjectState(e,t){e.geometry.removeHighlight(t),this._addedToStage&&this._drapeSourceRenderer.modifyGeometries(this.renderGeometries,Vs.HIGHLIGHT)}computeAttachmentOrigin(e){for(const t of this.renderGeometries)t.geometry.computeAttachmentOrigin(vi)&&(e.draped.origin[0]+=vi[0],e.draped.origin[1]+=vi[1],e.draped.num++)}async getProjectedBoundingBox(e,t,i,s,a){B(a);for(let n=0;n<this.renderGeometries.length;n++){const o=this.renderGeometries[n];this._getRenderGeometryProjectedBoundingRect(o,e,Do,i),pd(a,Do)}if(t){let n;dt(a,vi);const o=Za(a,t.service.spatialReference,t);try{n=await t.service.queryElevation(vi[0],vi[1],s,o,"ground")}catch{}d(n)&&(a[2]=Math.min(a[2],n),a[5]=Math.max(a[5],n))}return a}_getRenderGeometryProjectedBoundingRect(e,t,i,s){if(this.boundingBox)Ll(rt,this.boundingBox);else{const a=e.boundingSphere,n=a[3];rt[0]=a[0]-n,rt[1]=a[1]-n,rt[2]=a[2]-n,rt[3]=a[0]+n,rt[4]=a[1]+n,rt[5]=a[2]+n}return t(rt,0,2),this.calculateRelativeScreenBounds&&s.push({location:dt(rt),screenSpaceBoundingRect:this.calculateRelativeScreenBounds()}),Na(rt,i)}};const Do=pt(),rt=_e(),vi=C();let hm=class{constructor(e,t,i,s=2048){this.text=e,this._alignment=t,this._parameters=i,this._maxSize=s,this._textWidths=[],this._lineWidths=[],this._renderPixelRatio=null,this._metricsCached=null,this.key=`TextRenderer-${this._parameters.key}-${this._alignment}--${e}`,this._lines=e.split(/\r?\n/)}get displayWidth(){return Math.ceil(this._displayWidth+2*this._backgroundHorizontalPadding)}get displayHeight(){const e=this._lineSpacing*(this._lines.length-1),t=this._lineHeight;return Math.ceil(e+t+2*this._haloSize+this._backgroundTopPadding+this._backgroundBottomPadding)}get renderedWidth(){return Math.ceil(this._toRenderUnit(this.displayWidth))}get renderedHeight(){return Math.ceil(this._toRenderUnit(this.displayHeight))}get firstRenderedBaselinePosition(){return this._toRenderUnit(this._firstLineYOffset+this._baselinePosition)}get _firstLineYOffset(){return this._backgroundTopPadding+this._haloSize}get _metrics(){if(b(this._metricsCached)){const e=Ra($o,Nr,Nr).getContext("2d");this._setFontProperties(e,this._fontSize);let t=2*this._haloSize;const i=this._parameters.definition.font;i.style!=="italic"&&i.style!=="oblique"&&i.weight!=="bold"&&i.weight!=="bolder"||(t+=.3*e.measureText("A").width),this._textWidths.length=0,this._lineWidths.length=0;let s,a,n=0,o=0,l=0;this._lines.forEach((g,_)=>{const S=e.measureText(g),P=S.width,x=P+t;this._textWidths.push(P),this._lineWidths.push(x),n=Math.max(n,x),o=Math.max(o,S.actualBoundingBoxAscent),l=Math.max(l,S.actualBoundingBoxDescent),_===0&&(s=S),_===this._lines.length-1&&(a=S)});const h=e.font;let c=Mo.get(h);if(!c){const g=e.measureText(dm);c=new pm(g.actualBoundingBoxAscent,g.actualBoundingBoxDescent),Mo.set(h,c)}o=Math.max(o,c.actualBoundingBoxAscent),l=Math.max(l,c.actualBoundingBoxDescent);const u=o+l,p=this._hasBackground?s.actualBoundingBoxAscent:o,m=this._hasBackground?a.actualBoundingBoxDescent:l;this._metricsCached=new um(o-p,l-m,u,n,o)}return this._metricsCached}get _lineSpacing(){return(this._lineHeight+this._linePadding)*this._parameters.definition.lineSpacingFactor}get _lineHeight(){return this._metrics.lineHeight}get _linePadding(){return this._lineHeight*cm}get _baselinePosition(){return this._metrics.baselinePosition}get _renderedFontSize(){return this._toRenderUnit(this._fontSize)}get _fontSize(){return this._parameters.definition.size}get _renderedHaloSize(){return this._toRenderUnit(this._haloSize)}get _haloSize(){return this._parameters.haloSize}get _backgroundHorizontalPadding(){return this._hasBackground?this._parameters.definition.background.padding[0]:0}get _backgroundVerticalPadding(){return this._hasBackground?this._parameters.definition.background.padding[1]:0}get _backgroundTopPadding(){return Math.max(0,this._backgroundVerticalPadding-this._metrics.paddingTop)}get _backgroundBottomPadding(){return Math.max(0,this._backgroundVerticalPadding-this._metrics.paddingBottom)}get _hasBackground(){return!!this._parameters.backgroundStyle}get renderPixelRatio(){if(b(this._renderPixelRatio)){const e=this._parameters.definition.pixelRatio;this._maxSize>0?this._renderPixelRatio=Math.min(e,Math.min(this._maxSize/this.displayWidth,this._maxSize/this.displayHeight)):this._renderPixelRatio=e}return this._renderPixelRatio}_getLineXOffset(e){switch(this._alignment){case Bt.Left:return this._backgroundHorizontalPadding;case Bt.Center:return(this.displayWidth-this._lineWidths[e])/2;case Bt.Right:return this.displayWidth-this._backgroundHorizontalPadding-this._lineWidths[e]}}render(e,t=0,i=0){e.save();const s=t/=this.renderPixelRatio,a=i/=this.renderPixelRatio,n=this._haloSize,o=this._firstLineYOffset;t+=n,i+=o+this._baselinePosition;const l=this._haloSize>0;l&&this._renderHalo(e,s,a,n,o),this._setFontProperties(e,this._renderedFontSize);for(let h=0;h<this._lines.length;++h){const c=this._lines[h],u=this._getLineXOffset(h);l&&(e.globalCompositeOperation="destination-out",e.fillStyle="rgb(0, 0, 0)",this._fillText(e,c,t+u,i),this._renderLineDecoration(e,t+u,i,this._textWidths[h])),e.globalCompositeOperation="source-over",e.fillStyle=this._parameters.textStyle,this._fillText(e,c,t+this._getLineXOffset(h),i),this._renderLineDecoration(e,t+u,i,this._textWidths[h]),i+=this._lineSpacing}if(Ie.TEXT_SHOW_BASELINE){e.strokeStyle=Go,e.setLineDash([2,2]),e.lineWidth=1;let h=a+o;for(let c=0;c<this._lines.length;++c){const u=h+this._baselinePosition;this._drawLine(e,[s,u],[s+this.displayWidth,u]),h+=this._lineSpacing}}if(Ie.TEXT_SHOW_BORDER&&(e.strokeStyle=Go,e.setLineDash([]),e.lineWidth=1,this._drawBox(e,[s,a],[this.displayWidth,this.displayHeight])),this._hasBackground){const h=this._parameters.definition.background.borderRadius*this.renderPixelRatio;this._roundedRect(e,s,a,h),e.globalCompositeOperation="destination-over",e.fillStyle=this._parameters.backgroundStyle,e.fill()}e.restore()}_renderLineDecoration(e,t,i,s,a=!1){if(this._parameters.definition.font.decoration==="none"||s===0)return;const n=1,o=Math.max(this._parameters.definition.size/16,n);switch(this._parameters.definition.font.decoration){case"underline":i+=2*o;break;case"line-through":i-=.33*this._baselinePosition}const l=a?this._haloSize:0;e.strokeStyle=a?this._parameters.haloStyle:this._parameters.textStyle,e.lineWidth=this._toRenderUnit(o+2*l),e.beginPath(),e.moveTo(this._toRenderUnit(t-l),this._toRenderUnit(i)),e.lineTo(this._toRenderUnit(t+s+l),this._toRenderUnit(i)),e.stroke()}_roundedRect(e,t,i,s){t=this._toRenderUnit(t),i=this._toRenderUnit(i);const a=this.renderedWidth,n=this.renderedHeight;s!==0?(s=hr(s,0,Math.floor(n/2)),e.beginPath(),e.moveTo(t,i+s),e.arcTo(t,i,t+s,i,s),e.lineTo(t+a-s,i),e.arcTo(t+a,i,t+a,i+s,s),e.lineTo(t+a,i+n-s),e.arcTo(t+a,i+n,t+a-s,i+n,s),e.lineTo(t+s,i+n),e.arcTo(t,i+n,t,i+n-s,s),e.closePath()):e.rect(t,i,a,n)}_renderHalo(e,t,i,s,a){const n=this.renderedWidth,o=this.renderedHeight,l=Ra($o,Math.max(n,Nr),Math.max(o,Nr)),h=l.getContext("2d");h.clearRect(0,0,n,o),this._setFontProperties(h,this._renderedFontSize),h.fillStyle=this._parameters.haloStyle,h.strokeStyle=this._parameters.haloStyle;const c=this._renderedHaloSize<3;h.lineJoin=c?"miter":"round",c?this._renderHaloEmulated(h,s,a):this._renderHaloNative(h,s,a);let u=a+this._baselinePosition;for(let p=0;p<this._lines.length;++p){const m=this._getLineXOffset(p);this._renderLineDecoration(h,s+m,u,this._textWidths[p],!0),u+=this._lineSpacing}e.globalAlpha=this._parameters.definition.halo.color[3],e.drawImage(l,0,0,n,o,this._toRenderUnit(t),this._toRenderUnit(i),n,o),e.globalAlpha=1}_renderHaloEmulated(e,t,i){i+=this._baselinePosition;for(let s=0;s<this._lines.length;++s){const a=this._lines[s],n=this._getLineXOffset(s);for(const[o,l]of Uh)this._fillText(e,a,t+n+this._haloSize*o,i+this._haloSize*l);i+=this._lineSpacing}}_renderHaloNative(e,t,i){const s=2*this._haloSize;i+=this._baselinePosition;for(let a=0;a<this._lines.length;++a){const n=this._lines[a],o=this._getLineXOffset(a),l=5,h=.1;for(let c=0;c<l;c++){const u=1-(l-1)*h+c*h;e.lineWidth=this._toRenderUnit(u*s),this._strokeText(e,n,t+o,i)}i+=this._lineSpacing}}_setFontProperties(e,t){e.font=this._parameters.fontString(t),e.textAlign="left",e.textBaseline="alphabetic"}get _displayWidth(){return this._metrics.displayWidth}_toRenderUnit(e){return e*this.renderPixelRatio}_toRoundedRenderUnit(e){return Math.round(e*this.renderPixelRatio)}_fillText(e,t,i,s){e.fillText(t,this._toRenderUnit(i),this._toRenderUnit(s))}_strokeText(e,t,i,s){e.strokeText(t,this._toRenderUnit(i),this._toRenderUnit(s))}_drawLine(e,t,i){e.beginPath(),e.moveTo(this._toRoundedRenderUnit(t[0])+.5,this._toRoundedRenderUnit(t[1])+.5),e.lineTo(this._toRoundedRenderUnit(i[0])+.5,this._toRoundedRenderUnit(i[1])+.5),e.stroke()}_drawBox(e,t,i){const s=this._toRenderUnit(t[0]),a=this._toRenderUnit(t[1]),n=this._toRenderUnit(i[0]),o=this._toRenderUnit(i[1]),l=Math.floor(s)+.5,h=Math.ceil(s+n)-.5,c=Math.floor(a)+.5,u=Math.ceil(a+o)-.5;e.beginPath(),e.moveTo(l,c),e.lineTo(h,c),e.lineTo(h,u),e.lineTo(l,u),e.lineTo(l,c),e.stroke()}};const Uh=[];for(let e=0;e<360;e+=360/16)Uh.push([Math.cos(Math.PI*e/180),Math.sin(Math.PI*e/180)]);var Bt;function Ra(r,e,t){return r.canvas||(r.canvas=document.createElement("canvas")),r.canvas.width=e,r.canvas.height=t,r.canvas}(function(r){r[r.Left=0]="Left",r[r.Center=1]="Center",r[r.Right=2]="Right"})(Bt||(Bt={}));const $o={canvas:null},cm=.2,Nr=512,Go="rgb(255, 0, 255, 0.5)",dm=(()=>{let r="";for(let e=32;e<127;e++)r+=String.fromCharCode(e);return r})();let um=class{constructor(e,t,i,s,a){this.paddingTop=e,this.paddingBottom=t,this.lineHeight=i,this.displayWidth=s,this.baselinePosition=a}},pm=class{constructor(e,t){this.actualBoundingBoxAscent=e,this.actualBoundingBoxDescent=t}};const Mo=new Map,gm=Object.freeze({left:0,center:.5,right:1}),es=Object.freeze({"bottom-left":V(0,0),bottom:V(.5,0),"bottom-right":V(1,0),left:V(0,.5),center:V(.5,.5),right:V(1,.5),"top-left":V(0,1),top:V(.5,1),"top-right":V(1,1)});function mm(r){switch(r){case"left":return Bt.Left;case"right":return Bt.Right;default:return Bt.Center}}function fm(r,e){switch(e){case"bottom":return r==="left"?"bottom-left":r==="right"?"bottom-right":"bottom";case"center":return r;case"top":return r==="left"?"top-left":r==="right"?"top-right":"top"}}function ym(r){return r==="middle"?"center":r}var ce,zo;function ki(r){return r!=null}function Si(r){return typeof r=="number"}function Es(r){return typeof r=="string"}function _m(r){return r==null||Es(r)}function bm(r,e,t,i=j()){const s=r||0,a=e||0,n=t||0;return s!==0&&gd(i,i,-s/180*Math.PI),a!==0&&md(i,i,a/180*Math.PI),n!==0&&fd(i,i,n/180*Math.PI),i}function At(r,e,t,i,s){const a=r.minSize,n=r.maxSize;if(r.expression)return!1;if(r.useSymbolValue){const o=i.symbolSize[t];return e.minSize[t]=o,e.maxSize[t]=o,e.offset[t]=e.minSize[t],e.factor[t]=0,e.type[t]=ce.DefinedSize,!0}if(ki(r.field))return ki(r.stops)?r.stops.length===2&&Si(r.stops[0].size)&&Si(r.stops[1].size)?(Vo(r.stops[0].size,r.stops[1].size,r.stops[0].value,r.stops[1].value,e,t),e.type[t]=ce.DefinedSize,!0):!1:Si(a)&&Si(n)&&ki(r.minDataValue)&&ki(r.maxDataValue)?(Vo(a,n,r.minDataValue,r.maxDataValue,e,t),e.type[t]=ce.DefinedSize,!0):En[r.valueUnit]!=null?(e.minSize[t]=-1/0,e.maxSize[t]=1/0,e.offset[t]=0,e.factor[t]=1/En[r.valueUnit],e.type[t]=ce.DefinedSize,!0):(r.valueUnit==="unknown",!1);if(!ki(r.field)){if(r.stops&&r.stops[0]&&Si(r.stops[0].size))return e.minSize[t]=r.stops[0].size,e.maxSize[t]=r.stops[0].size,e.offset[t]=e.minSize[t],e.factor[t]=0,e.type[t]=ce.DefinedSize,!0;if(Si(a))return e.minSize[t]=a,e.maxSize[t]=a,e.offset[t]=a,e.factor[t]=0,e.type[t]=ce.DefinedSize,!0}return!1}function Vo(r,e,t,i,s,a){const n=Math.abs(i-t)>0?(e-r)/(i-t):0;s.minSize[a]=n>0?r:e,s.maxSize[a]=n>0?e:r,s.offset[a]=r-t*n,s.factor[a]=n}function vm(r,e,t,i){if(r.normalizationField||r.valueRepresentation||!_m(r.field))return null;if(e.size){if(r.field)if(e.size.field){if(r.field!==e.size.field)return null}else e.size.field=r.field}else e.size={field:r.field,minSize:[0,0,0],maxSize:[0,0,0],offset:[0,0,0],factor:[0,0,0],type:[ce.Undefined,ce.Undefined,ce.Undefined]};let s;switch(r.axis){case"width":return s=At(r,e.size,0,t),s?e:null;case"height":return s=At(r,e.size,2,t),s?e:null;case"depth":return s=At(r,e.size,1,t),s?e:null;case"width-and-depth":return s=At(r,e.size,0,t),s&&At(r,e.size,1,t),s?e:null;case null:case void 0:case"all":return s=At(r,e.size,0,t),s=s&&At(r,e.size,1,t),s=s&&At(r,e.size,2,t),s?e:null;default:return`${r.axis}`,null}}function Sm(r,e,t){for(let s=0;s<3;++s){let a=e.unitInMeters;r.type[s]===ce.DefinedSize&&(a*=e.modelSize[s],r.type[s]=ce.DefinedScale),r.minSize[s]=r.minSize[s]/a,r.maxSize[s]=r.maxSize[s]/a,r.offset[s]=r.offset[s]/a,r.factor[s]=r.factor[s]/a}let i;if(r.type[0]!==ce.Undefined)i=0;else if(r.type[1]!==ce.Undefined)i=1;else{if(r.type[2]===ce.Undefined)return!1;i=2}for(let s=0;s<3;++s)r.type[s]===ce.Undefined&&(r.minSize[s]=r.minSize[i],r.maxSize[s]=r.maxSize[i],r.offset[s]=r.offset[i],r.factor[s]=r.factor[i],r.type[s]=r.type[i]);return!0}function Uo(r,e,t){r[4*e+0]=t.r/255,r[4*e+1]=t.g/255,r[4*e+2]=t.b/255,r[4*e+3]=t.a}function xm(r,e,t){if(r.normalizationField)return null;if(Es(r.field)){if(!r.stops)return null;{if(r.stops.length>8)return null;e.color={field:r.field,values:[0,0,0,0,0,0,0,0],colors:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]};const i=r.stops;for(let s=0;s<8;++s){const a=i[Math.min(s,i.length-1)];e.color.values[s]=a.value,Uo(e.color.colors,s,a.color)}}}else{if(!(r.stops&&r.stops.length>=0))return null;{const i=r.stops&&r.stops.length>=0&&r.stops[0].color;e.color={field:null,values:[0,0,0,0,0,0,0,0],colors:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]};for(let s=0;s<8;s++)e.color.values[s]=1/0,Uo(e.color.colors,s,i)}}return e}function Cm(r,e,t){if(r.normalizationField)return null;if(Es(r.field)){if(!r.stops)return null;{if(r.stops.length>8)return null;e.opacity={field:r.field,values:[0,0,0,0,0,0,0,0],opacityValues:[0,0,0,0,0,0,0,0]};const i=r.stops;for(let s=0;s<8;++s){const a=i[Math.min(s,i.length-1)];e.opacity.values[s]=a.value,e.opacity.opacityValues[s]=a.opacity}}}else{if(!(r.stops&&r.stops.length>=0))return null;{const i=r.stops&&r.stops.length>=0?r.stops[0].opacity:0;e.opacity={field:null,values:[0,0,0,0,0,0,0,0],opacityValues:[0,0,0,0,0,0,0,0]};for(let s=0;s<8;s++)e.opacity.values[s]=1/0,e.opacity.opacityValues[s]=i}}return e}function Ys(r,e,t){const i=t===2&&r.rotationType==="arithmetic";e.offset[t]=i?90:0,e.factor[t]=i?-1:1,e.type[t]=1}function Pm(r,e,t){if(!Es(r.field))return null;if(e.rotation){if(r.field)if(e.rotation.field){if(r.field!==e.rotation.field)return null}else e.rotation.field=r.field}else e.rotation={field:r.field,offset:[0,0,0],factor:[1,1,1],type:[0,0,0]};switch(r.axis){case"tilt":return Ys(r,e.rotation,0),e;case"roll":return Ys(r,e.rotation,1),e;case null:case void 0:case"heading":return Ys(r,e.rotation,2),e;default:return`${r.axis}`,null}}function Fh(r,e,t){if(!r)return null;const i=!e.supportedTypes||!!e.supportedTypes.size,s=!e.supportedTypes||!!e.supportedTypes.color,a=!e.supportedTypes||!!e.supportedTypes.rotation,n=!!e.supportedTypes&&!!e.supportedTypes.opacity,o=r.reduce((l,h)=>{if(!l)return l;if(h.valueExpression)return null;switch(h.type){case"size":return i?vm(h,l,e,t):l;case"color":return s?xm(h,l):l;case"opacity":return n?Cm(h,l):null;case"rotation":return a?Pm(h,l,t):l;default:return null}},{size:null,color:null,opacity:null,rotation:null});return!(r.length>0&&o)||o.size||o.color||o.opacity||o.rotation?o&&o.size&&!Sm(o.size,e)?null:o:null}function yn(r){return r&&r.size!=null}function xr(r,e){if(!r)return{enabled:!1};if(Ie.TESTS_DISABLE_FAST_UPDATES)return{enabled:!1};const t=Fh(r.visualVariables,e);return t?{enabled:!0,visualVariables:t,materialParameters:Nh(t,e),requiresShaderTransformation:yn(t)}:{enabled:!1}}function Cr(r,e,t){if(!e||!r.enabled)return!1;const i=r.visualVariables,s=Fh(e.visualVariables,t);return!!s&&!!(Br(i.size,s.size,"size")&&Br(i.color,s.color,"color")&&Br(i.rotation,s.rotation,"rotation")&&Br(i.opacity,s.opacity,"opacity"))&&(r.visualVariables=s,r.materialParameters=Nh(s,t),r.requiresShaderTransformation=yn(s),!0)}function Br(r,e,t){if(!!r!=!!e||r&&r.field!==e.field)return!1;if(r&&t==="rotation"){const i=r,s=e;for(let a=0;a<3;a++)if(i.type[a]!==s.type[a]||i.offset[a]!==s.offset[a]||i.factor[a]!==s.factor[a])return!1}return!0}function Nh(r,e){const t={vvSizeEnabled:!1,vvSizeMinSize:null,vvSizeMaxSize:null,vvSizeOffset:null,vvSizeFactor:null,vvSizeValue:null,vvColorEnabled:!1,vvColorValues:null,vvColorColors:null,vvOpacityEnabled:!1,vvOpacityValues:null,vvOpacityOpacities:null,vvSymbolAnchor:null,vvSymbolRotationMatrix:null},i=yn(r);return r&&r.size?(t.vvSizeEnabled=!0,t.vvSizeMinSize=r.size.minSize,t.vvSizeMaxSize=r.size.maxSize,t.vvSizeOffset=r.size.offset,t.vvSizeFactor=r.size.factor):r&&i&&(t.vvSizeValue=e.transformation.scale),r&&i&&(t.vvSymbolAnchor=e.transformation.anchor,t.vvSymbolRotationMatrix=Cs(),$l(ar),bm(e.transformation.rotation[2],e.transformation.rotation[0],e.transformation.rotation[1],ar),ka(t.vvSymbolRotationMatrix,ar)),r&&r.color&&(t.vvColorEnabled=!0,t.vvColorValues=r.color.values,t.vvColorColors=r.color.colors),r&&r.opacity&&(t.vvOpacityEnabled=!0,t.vvOpacityValues=r.opacity.values,t.vvOpacityOpacities=r.opacity.opacityValues),t}function Aa(r,e,t){if(!r.vvSizeEnabled)return t;za(It,t);const i=r.vvSymbolRotationMatrix;yd(ar,i[0],i[1],i[2],0,i[3],i[4],i[5],0,i[6],i[7],i[8],0,0,0,0,1),Ha(It,It,ar);for(let s=0;s<3;++s){const a=r.vvSizeOffset[s]+e[0]*r.vvSizeFactor[s];Fo[s]=hr(a,r.vvSizeMinSize[s],r.vvSizeMaxSize[s])}return Gl(It,It,Fo),Wa(It,It,r.vvSymbolAnchor),It}function wm(r,e,t){if(!e.vvSizeEnabled)return D(r,1,1,1);for(let i=0;i<3;++i){const s=e.vvSizeOffset[i]+t[0]*e.vvSizeFactor[i];r[i]=hr(s,e.vvSizeMinSize[i],e.vvSizeMaxSize[i])}return r}(function(r){r[r.Undefined=0]="Undefined",r[r.DefinedSize=1]="DefinedSize",r[r.DefinedScale=2]="DefinedScale"})(ce||(ce={})),function(r){r[r.Undefined=0]="Undefined",r[r.DefinedAngle=1]="DefinedAngle"}(zo||(zo={}));const It=j(),Fo=C(),ar=j(),Om=j(),No=Ft(0,0,1),Em=16,Rm=1.5,ni=bu,Am=[ni/2,ni/2,1-ni/2,1-ni/2],Im=[kn*ni,kn*ni];class Pr extends mt{getCachedSize(){return{size:this._getIconSize()}}constructor(e,t,i,s){super(e,t,i,s),this._cimLayers=null,this._cimSymbolMaterials=new Map,this._cimSymbolTextures=new Map,this._cimMaterialParametersInfo=null,this._cimRequiredFields=null,this._cimScaleFactorOrFunction=null,this._size=null,this._symbolTextureRatio=1,this._outlineSize=0,this._elevationOptions={supportsOffsetAdjustment:!0,supportsOnTheGround:!0}}async doLoad(e){this._validateOrThrow();const t=this._prepareMaterialParameters(),i=this._getPrimitive();if(d(i))this._prepareResourcesPrimitive(t,i);else{const s=_d(this.symbolLayer),a=Up(s);a&&a.mediaType==="application/json"?await this._prepareResourcesCIM(t,JSON.parse(a.data),e):await this._prepareResourcesHref(t,s,e)}}_validateOrThrow(){if(this._drivenProperties.size)return;const e=ys(this._getIconSize());if(e)throw new We("graphics3diconsymbollayer:invalid-size",e)}_getIconSize(){const e=this.symbolLayer,t=Math.round(e.size!=null?Re(e.size):Em);return this._drivenProperties.size?Math.max(t,64):t}_generateTextureCIM(e){const t=this._getGraphicHash(e);let i=t===""?null:this._cimSymbolTextures.get(t);if(!i){const s={scaleFactor:this._cimScaleFactorOrFunction},a=this._context.sharedResources.cimSymbolRasterizer.rasterizeCIMSymbol3D(this._cimLayers,e,"esriGeometryPoint",s,void 0,void 0);this._cimMaterialParametersInfo.anchorPosition=this._getAnchorPos("relative",a.anchorPosition);const n={width:a.imageData.width,height:a.imageData.height,powerOfTwoResizeMode:Jl.PAD};i=new Ka(a.imageData,n),this._cimSymbolTextures.set(t,i),this._context.stage.add(i)}return i}_computeSize(e,t){const i=e.width/e.height;return i>1?[t,Math.round(t/i)]:[Math.round(t*i),t]}_prepareMaterialParameters(){const e={anchorPosition:this._getAnchorPos(this.symbolLayer.anchor,this.symbolLayer.anchorPosition)},t=this.symbol;if(Tm(t)){const{screenLength:i,minWorldLength:s,maxWorldLength:a}=t.verticalOffset;e.verticalOffset={screenLength:Re(i),minWorldLength:s||0,maxWorldLength:d(a)?a:1/0}}return this._context.screenSizePerspectiveEnabled&&(e.screenSizePerspective=this._context.sharedResources.screenSizePerspectiveSettings),e.occlusionTest=!0,e.hasSlicePlane=this._context.slicePlaneEnabled,e}_prepareResourcesPrimitive(e,t){const i=this._getOutlineSize();if(Zs(t)&&i===0)throw new Error("Nothing to render");if(this._outlineSize=i,e.color=this._getFillColor(),e.outlineColor=this._getOutlineColor(),e.outlineSize=this._outlineSize,d(this._context.sharedResources.textures)){const a=this._context.sharedResources.textures.fromData(`${t}-icon`,()=>_u(t));this._texture=a.texture,this._releaseTexture=a,e.textureId=this._texture.id}e.textureIsSignedDistanceField=!0,e.distanceFieldBoundingBox=Am;const s=this._getIconSize();this._size=[s,s],this._symbolTextureRatio=1/ni,this._createMaterialAndAddToStage(e,this._context.stage)}async _prepareResourcesHref(e,t,i){this._outlineSize=this._getOutlineSize(),e.color=this._getFillColor(),e.outlineColor=this._getOutlineColor(),e.outlineSize=this._outlineSize,e.textureIsSignedDistanceField=!1;const s=this._getIconSize(),a=s*this._context.graphicsCoreOwner.view.state.rasterPixelRatio;if(d(this._context.sharedResources.textures)){const n=await Ml(this._context.sharedResources.textures.fromUrl(t,a,{signal:i}));if(n.ok===!1)throw wc(n.error),new We("graphics3diconsymbollayer:request-failed",`Failed to load (Request for icon resource failed: ${t})`);this._releaseTexture=n.value;const o=n.value.texture,l=o.params;this._size=this._computeSize(l,s),e.textureId=o.id}this._createMaterialAndAddToStage(e,this._context.stage)}async _prepareResourcesCIM(e,t,i){const s=new bd({data:t});if(!this._context.sharedResources.cimSymbolRasterizer){const c=(await wr(async()=>{const{CIMSymbolRasterizer:u}=await import("./CIMSymbolRasterizer-CqVpHbJI.js");return{CIMSymbolRasterizer:u}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]))).CIMSymbolRasterizer;Oe(i),this._context.sharedResources.cimSymbolRasterizer||(this._context.sharedResources.cimSymbolRasterizer=new c(this._context.renderCoordsHelper.spatialReference,!0))}const a=this._context.layer.fields?this._context.layer.fields.map(c=>c.toJSON()):null;let n,o;if(this._cimLayers=await this._context.sharedResources.cimSymbolRasterizer.analyzeCIMSymbol(s,a,this._context.renderer&&this._context.renderer.type==="dictionary"?this._context.renderer.fieldMap:null,"esriGeometryPoint",{signal:i}),this._context.renderer&&this._context.renderer.type==="dictionary"&&this._context.renderer.scaleExpression){const c=this._context.renderer;if(isNaN(c.scaleExpression)){const u=c.scaleExpression,p=await vd(u,this._context.layer.spatialReference,a);o=(m,g,_)=>{const S=Fp(p,m,{$view:_},"esriGeometryPoint",g);return S!==null?S:1}}else n=Number(c.scaleExpression)}this._cimScaleFactorOrFunction=n||o||1;const l=this._context.renderer?await this._context.renderer.getRequiredFields(this._context.layer.fieldsIndex):[];Oe(i);const h=this._context.layer.fieldsIndex;this._cimRequiredFields=l.map(c=>h.get(c).name),this._cimMaterialParametersInfo=e,this._cimMaterialParametersInfo.color=this._getFillColor(),this._cimMaterialParametersInfo.outlineColor=[0,0,0,0],this._cimMaterialParametersInfo.outlineSize=0,this._cimMaterialParametersInfo.textureIsSignedDistanceField=!1}_getPrimitive(){return this.symbolLayer.resource&&this.symbolLayer.resource.href?null:this.symbolLayer.resource&&this.symbolLayer.resource.primitive||Sd}_getOutlineSize(){let e=0;const t=this.symbolLayer;return d(t.outline)&&t.outline.size!=null?Math.max(Re(t.outline.size),0):(e=Zs(this._getPrimitive())?Rm:0,Math.max(e,0))}_getOutlineColor(){const e=this._getLayerOpacity(),t=this.symbolLayer,i=ie(t,"outline","color");if(d(i)){const s=ue.toUnitRGB(i),a=i.a*e;return[s[0],s[1],s[2],a]}return[0,0,0,0]}_getFillColor(){if(Zs(this._getPrimitive()))return _g;const e=b(this._getPrimitive()),t=ie(this.symbolLayer,"material","color");return this._getCombinedOpacityAndColor(t,{hasIntrinsicColor:e})}_getAnchorPos(e,t){return e==="relative"?V((t.x||0)+.5,.5-(t.y||0)):e in es?es[e]:es.center}_createMaterialAndAddToStage(e,t){if(this._cimLayers?this._fastUpdates={enabled:!1}:this._fastUpdates=xr(this._context.renderer,this._fastVisualVariableConvertOptions()),this._fastUpdates.enabled&&Object.assign(e,this._fastUpdates.materialParameters),this._cimLayers){let i=d(e.textureId)?this._cimSymbolMaterials.get(e.textureId):null;return i||(i=new os(e),this._cimSymbolMaterials.set(pe(e.textureId,0),i),t.add(i)),i}return this._material=new os(e),t.add(this._material),this._material}_setDrapingDependentMaterialParameters(){this.draped&&(this._forEachMaterial(e=>{e.setParameters({verticalOffset:null,screenSizePerspective:null,occlusionTest:!1,hasSlicePlane:!1,shaderPolygonOffset:0,isDraped:this.draped})}),this.layerOpacityChanged())}destroy(){super.destroy(),this._forEachMaterial(e=>this._context.stage.remove(e)),this._material=null,this._cimSymbolMaterials.clear(),this._cimSymbolTextures.forEach(e=>this._context.stage.remove(e)),this._cimSymbolTextures.clear(),this._releaseTexture=Sl(this._releaseTexture)}_getScaleFactor(e,t){if(this._drivenProperties.size&&e.size){for(let i=0;i<3;i++){const s=e.size[i];s&&s!=="symbol-value"&&s!=="proportional"&&(e.size[i]=Re(s))}if(e.size[0]==="symbol-value")return 1;if(isFinite(+e.size[0]))return+e.size[0]/t;if(isFinite(+e.size[2]))return+e.size[2]/t}return 1}createGraphics3DGraphic(e){const t=e.graphic;if(!this._validateGeometry(t.geometry))return null;let i,s=[0,0];if(this._cimLayers){if(!this._cimLayers.length)return null;const u=this._generateTextureCIM(t),p={textureId:u.id,...this._cimMaterialParametersInfo};i=this._createMaterialAndAddToStage(p,this._context.stage),s=[u.params.width,u.params.height]}else s=this._size,i=se(this._material);const a=vr(t.geometry);if(b(a))return this.logger.warn(`unsupported geometry type for icon symbol: ${t.geometry.type}`),null;const n=e.renderingInfo,o=this._getVertexOpacityAndColor(n);let l=1;if(!this._fastUpdates.enabled||!this._fastUpdates.visualVariables.size){const u=s[0]>s[1]?s[0]:s[1];l=this._getScaleFactor(n,u)}l*=this._symbolTextureRatio;const h=V(s[0]*l,s[1]*l),c=this.setGraphicElevationContext(t,new Je);return this.ensureDrapedStatus(c.mode==="on-the-ground")&&this._setDrapingDependentMaterialParameters(),this.draped?this._createAsOverlay(t,a,i,o,h,e.layer.uid):this._createAs3DShape(t,a,i,o,h,c,t.uid)}layerOpacityChanged(){const e=this._getFillColor(),t=this._getOutlineColor();this._forEachMaterial(i=>{i.setParameters({color:e}),i.setParameters({outlineColor:t})})}layerElevationInfoChanged(e,t,i){const s=this._elevationContext.mode,a=Ar(Pr.elevationModeChangeTypes,i,s);if(a!==q.UPDATE)return a;const n=Ye(s)||s==="absolute-height";return this.updateGraphics3DGraphicElevationInfo(e,t,()=>n)}slicePlaneEnabledChanged(){return this.draped||this._forEachMaterial(e=>{e.setParameters({hasSlicePlane:this._context.slicePlaneEnabled})}),!0}physicalBasedRenderingChanged(){return!0}pixelRatioChanged(){return!!this._getPrimitive()}skipHighSymbolLodsChanged(){return!0}applyRendererDiff(e,t){for(const i in e.diff){if(i!=="visualVariables"||!Cr(this._fastUpdates,t,this._fastVisualVariableConvertOptions()))return H.Recreate_Symbol;d(this._material)&&this._material.setParameters(this._fastUpdates.materialParameters)}return H.Fast_Update}_defaultElevationInfoNoZ(){return Lm}_createAs3DShape(e,t,i,s,a,n,o){const l=this.getFastUpdateAttrValues(e),h=l?_=>Aa(this._fastUpdates.materialParameters,l,_):void 0,c=this._context.layer.uid,u=this._context.stage.renderView.getObjectAndLayerIdColor({graphicUid:o,layerUid:c}),p=_a(i,No,null,s,a,Dm,null,l,u),m=gn(this._context,t,p,n,o,h);if(b(m))return null;const g=new gt(this,m.object,[p],null,null,_r,n);return g.alignedSampledElevation=m.sampledElevation,g.needsElevationUpdates=Ye(n.mode)||n.mode==="absolute-height",g.getScreenSize=this._createScreenSizeGetter(a,h),g.calculateRelativeScreenBounds=_=>i.calculateRelativeScreenBounds(g.getScreenSize(),1,_),br(g,t,this._context.elevationProvider),g}_createAsOverlay(e,t,i,s,a,n){i.renderPriority=this._renderPriority;const o=ms();or(t,o,this._context.overlaySR),o[2]=Yl;const l=this._context.clippingExtent;if(d(l)&&!Va(l,o))return null;const h=this.getFastUpdateAttrValues(e),c=h?_=>Aa(this._fastUpdates.materialParameters,h,_):void 0,u=this._context.stage.renderView.getObjectAndLayerIdColor({graphicUid:e.uid,layerUid:this._context.layer.uid}),p=_a(i,No,o,s,a,null,null,h,u),m=new ur(p,{layerUid:n,graphicUid:e.uid,shaderTransformer:c});o[3]=0,xd(m.boundingSphere,o);const g=new Os(this,[m],null,this._context.drapeSourceRenderer);return g.getScreenSize=this._createScreenSizeGetter(a,c),g.calculateRelativeScreenBounds=_=>i.calculateRelativeScreenBounds(g.getScreenSize(),1,_),g}_createScreenSizeGetter(e,t){const i=this._outlineSize+2;if(this._fastUpdates.enabled){const s=e[0]/this._symbolTextureRatio,a=e[1]/this._symbolTextureRatio;return(n=be())=>{const o=t(Om);return n[0]=o[0]*s+i,n[1]=o[5]*a+i,n}}{const s=e[0]/this._symbolTextureRatio+i,a=e[1]/this._symbolTextureRatio+i;return(n=be())=>(n[0]=s,n[1]=a,n)}}_fastVisualVariableConvertOptions(){const e=this._size[0]>this._size[1]?this._size[0]:this._size[1],t=Ft(e,e,e),i=ua(1),s=e*i;return{modelSize:t,symbolSize:Ft(s,s,s),unitInMeters:i,transformation:{anchor:Li,scale:si,rotation:Li}}}_getGraphicHash(e){let t="";for(const i of this._cimRequiredFields)t+=i+e.attributes[i];return t}_forEachMaterial(e){d(this._material)&&e(this._material),this._cimSymbolMaterials.forEach(e)}test(){return{...super.test(),material:this._material}}}function Tm(r){return r&&r.type==="point-3d"&&r.hasVisibleVerticalOffset()}function Zs(r){return!b(r)&&(r==="cross"||r==="x")}Pr.PRIMITIVE_SIZE=Im,Pr.elevationModeChangeTypes={definedChanged:q.UPDATE,staysOnTheGround:q.NONE,onTheGroundChanged:q.RECREATE};const Lm={mode:"relative-to-ground",offset:0},Dm=tt(0,0,0,1);function Ia(r){switch(r){case"butt":return er.BUTT;case"square":return er.SQUARE;case"round":return er.ROUND;default:return null}}function $m(r){return r==="diamond"?"kite":r}function Bh(r){const e=new _s,t=r.hasMultipassTerrain&&(r.output===O.Color||r.output===O.Alpha),i=r.space===Di.World;r.hasTip&&i&&e.extensions.add("GL_OES_standard_derivatives"),e.include(vu,r),e.include(Su,r),r.output===O.Depth&&e.include(en,r);const{vertex:s,fragment:a}=e;return a.include(qu),tn(s,r),e.attributes.add(f.POSITION,"vec3"),e.attributes.add(f.UV0,"vec2"),e.attributes.add(f.AUXPOS1,"vec3"),e.varyings.add("vColor","vec4"),e.varyings.add("vpos","vec3"),e.varyings.add("vUV","vec2"),e.varyings.add("vSize","float"),th(e),t&&e.varyings.add("depth","float"),r.hasTip&&e.varyings.add("vLineWidth","float"),s.uniforms.add([new tr("nearFar",(n,o)=>o.camera.nearFar),new $i("viewport",(n,o)=>o.camera.fullViewport)]),s.code.add(w`vec4 projectAndScale(vec4 pos) {
vec4 posNdc = proj * pos;
posNdc.xy *= viewport.zw / posNdc.w;
return posNdc;
}`),s.code.add(w`void clip(vec4 pos, inout vec4 prev) {
float vnp = nearFar[0] * 0.99;
if (prev.z > -nearFar[0]) {
float interpolation = (-vnp - pos.z) / (prev.z - pos.z);
prev = mix(pos, prev, interpolation);
}
}`),i?(e.attributes.add(f.NORMAL,"vec3"),ih(s),s.constants.add("tiltThreshold","float",.7),s.code.add(w`vec3 perpendicular(vec3 v) {
vec3 n = (viewNormal * vec4(normal.xyz, 1.0)).xyz;
vec3 n2 = cross(v, n);
vec3 forward = vec3(0.0, 0.0, 1.0);
float tiltDot = dot(forward, n);
return abs(tiltDot) < tiltThreshold ? n : n2;
}`)):s.code.add(w`vec2 perpendicular(vec2 v) {
return vec2(v.y, -v.x);
}`),s.code.add(w`
      #define vecN ${i?"vec3":"vec2"}

      vecN normalizedSegment(vecN pos, vecN prev) {
        vecN segment = pos - prev;
        float segmentLen = length(segment);

        // normalize or zero if too short
        return (segmentLen > 0.001) ? segment / segmentLen : ${i?"vec3(0.0, 0.0, 0.0)":"vec2(0.0, 0.0)"};
      }

      vecN displace(vecN pos, vecN prev, float displacementLen) {
        vecN segment = normalizedSegment(pos, prev);

        vecN displacementDirU = perpendicular(segment);
        vecN displacementDirV = segment;

        ${r.anchor===Zl.Tip?"pos -= 0.5 * displacementLen * displacementDirV;":""}

        return pos + displacementLen * (uv0.x * displacementDirU + uv0.y * displacementDirV);
      }
    `),r.space===Di.Screen&&(s.uniforms.add(new Yu("inverseProjectionMatrix",(n,o)=>o.camera.inverseProjectionMatrix)),s.code.add(w`vec3 inverseProject(vec4 posScreen) {
posScreen.xy = (posScreen.xy / viewport.zw) * posScreen.w;
return (inverseProjectionMatrix * posScreen).xyz;
}`),s.code.add(w`bool rayIntersectPlane(vec3 rayDir, vec3 planeOrigin, vec3 planeNormal, out vec3 intersection) {
float cos = dot(rayDir, planeNormal);
float t = dot(planeOrigin, planeNormal) / cos;
intersection = t * rayDir;
return abs(cos) > 0.001 && t > 0.0;
}`),s.uniforms.add(new Pt("perScreenPixelRatio",(n,o)=>o.camera.perScreenPixelRatio)),s.code.add(w`
      vec4 toFront(vec4 displacedPosScreen, vec3 posLeft, vec3 posRight, vec3 prev, float lineWidth) {
        // Project displaced position back to camera space
        vec3 displacedPos = inverseProject(displacedPosScreen);

        // Calculate the plane that we want the marker to lie in. Note that this will always be an approximation since ribbon lines are generally
        // not planar and we do not know the actual position of the displaced prev vertices (they are offset in screen space, too).
        vec3 planeNormal = normalize(cross(posLeft - posRight, posLeft - prev));
        vec3 planeOrigin = posLeft;

        ${r.hasCap?`
                if(prev.z > posLeft.z) {
                  vec2 diff = posLeft.xy - posRight.xy;
                  planeOrigin.xy += perpendicular(diff) / 2.0;
                }
              `:""};

        // Move the plane towards the camera by a margin dependent on the line width (approximated in world space). This tolerance corrects for the
        // non-planarity in most cases, but sharp joins can place the prev vertices at arbitrary positions so markers can still clip.
        float offset = lineWidth * perScreenPixelRatio;
        planeOrigin *= (1.0 - offset);

        // Intersect camera ray with the plane and make sure it is within clip space
        vec3 rayDir = normalize(displacedPos);
        vec3 intersection;
        if (rayIntersectPlane(rayDir, planeOrigin, planeNormal, intersection) && intersection.z < -nearFar[0] && intersection.z > -nearFar[1]) {
          return vec4(intersection.xyz, 1.0);
        }

        // Fallback: use depth of pos or prev, whichever is closer to the camera
        float minDepth = planeOrigin.z > prev.z ? length(planeOrigin) : length(prev);
        displacedPos *= minDepth / length(displacedPos);
        return vec4(displacedPos.xyz, 1.0);
      }
  `)),s.uniforms.add(new Pt("pixelRatio",(n,o)=>o.camera.pixelRatio)),Zu(e),s.code.add(w`void main(void) {
if (uv0.y == 0.0) {
gl_Position = vec4(1e038, 1e038, 1e038, 1.0);
}
else {
float lineWidth = getLineWidth();
float screenMarkerSize = getScreenMarkerSize();
vec4 pos  = view * vec4(position.xyz, 1.0);
vec4 prev = view * vec4(auxpos1.xyz, 1.0);
clip(pos, prev);`),i?(r.hideOnShortSegments&&s.code.add(w`if (areWorldMarkersHidden(pos, prev)) {
gl_Position = vec4(1e038, 1e038, 1e038, 1.0);
return;
}`),s.code.add(w`pos.xyz = displace(pos.xyz, prev.xyz, getWorldMarkerSize(pos));
vec4 displacedPosScreen = projectAndScale(pos);`)):(s.code.add(w`vec4 posScreen = projectAndScale(pos);
vec4 prevScreen = projectAndScale(prev);
vec4 displacedPosScreen = posScreen;
displacedPosScreen.xy = displace(posScreen.xy, prevScreen.xy, screenMarkerSize);`),r.space===Di.Screen&&s.code.add(w`vec2 displacementDirU = perpendicular(normalizedSegment(posScreen.xy, prevScreen.xy));
vec3 lineRight = inverseProject(posScreen + lineWidth * vec4(displacementDirU.xy, 0.0, 0.0));
vec3 lineLeft = pos.xyz + (pos.xyz - lineRight);
pos = toFront(displacedPosScreen, lineLeft, lineRight, prev.xyz, lineWidth);
displacedPosScreen = projectAndScale(pos);`)),s.code.add(w`
        ${t?"depth = pos.z;":""}
        linearDepth = calculateLinearDepth(nearFar,pos.z);

        // Convert back into NDC
        displacedPosScreen.xy = (displacedPosScreen.xy / viewport.zw) * displacedPosScreen.w;

        // Convert texture coordinate into [0,1]
        vUV = (uv0 + 1.0) / 2.0;

        ${i?"":"vUV *= displacedPosScreen.w;"}

        ${r.hasTip?"vLineWidth = lineWidth;":""}

        vSize = screenMarkerSize;
        vColor = getColor();

        // Use camera space for slicing
        vpos = pos.xyz;

        gl_Position = displacedPosScreen;
      }
    }
  `),t&&e.include(rn,r),e.include(Mt,r),a.uniforms.add([new $i("intrinsicColor",n=>n.color),new Xu("tex",n=>n.texture)]),a.include(sn),e.constants.add("texelSize","float",1/Hn),a.code.add(w`float markerAlpha(vec2 samplePos) {
samplePos += vec2(0.5, -0.5) * texelSize;
float sdf = rgba2float(texture2D(tex, samplePos)) - 0.5;
float distance = sdf * vSize;
distance -= 0.5;
return clamp(0.5 - distance, 0.0, 1.0);
}`),r.hasTip&&(e.constants.add("relativeMarkerSize","float",xu/Hn),e.constants.add("relativeTipLineWidth","float",Cu),a.code.add(w`
    float tipAlpha(vec2 samplePos) {
      // Convert coordinates s.t. they are in pixels and relative to the tip of an arrow marker
      samplePos -= vec2(0.5, 0.5 + 0.5 * relativeMarkerSize);
      samplePos *= vSize;

      float halfMarkerSize = 0.5 * relativeMarkerSize * vSize;
      float halfTipLineWidth = 0.5 * max(1.0, relativeTipLineWidth * vLineWidth);

      ${i?"halfTipLineWidth *= fwidth(samplePos.y);":""}

      float distance = max(abs(samplePos.x) - halfMarkerSize, abs(samplePos.y) - halfTipLineWidth);
      return clamp(0.5 - distance, 0.0, 1.0);
    }
  `)),e.constants.add("symbolAlphaCutoff","float",rh),a.code.add(w`
  void main() {
    discardBySlice(vpos);
    ${t?"terrainDepthTest(gl_FragCoord, depth);":""}

    vec4 finalColor = intrinsicColor * vColor;

    ${i?"vec2 samplePos = vUV;":"vec2 samplePos = vUV * gl_FragCoord.w;"}

    ${r.hasTip?"finalColor.a *= max(markerAlpha(samplePos), tipAlpha(samplePos));":"finalColor.a *= markerAlpha(samplePos);"}

    ${r.output===O.ObjectAndLayerIdColor?w`finalColor.a = 1.0;`:""}

    if (finalColor.a < symbolAlphaCutoff) {
      discard;
    }

    ${r.output===O.Alpha?w`gl_FragColor = vec4(finalColor.a);`:""}
    ${r.output===O.Color?w`gl_FragColor = highlightSlice(finalColor, vpos);`:""}
    ${r.output===O.Color&&r.transparencyPassType===Ze.Color?"gl_FragColor = premultiplyAlpha(gl_FragColor);":""}
    ${r.output===O.Highlight?w`gl_FragColor = vec4(1.0);`:""}
    ${r.output===O.Depth?w`outputDepth(linearDepth);`:""}
  }
  `),e}const Gm=Object.freeze(Object.defineProperty({__proto__:null,build:Bh},Symbol.toStringTag,{value:"Module"})),jh=new Map([[f.POSITION,0],[f.UV0,2],[f.AUXPOS1,3],[f.NORMAL,4],[f.COLOR,5],[f.COLORFEATUREATTRIBUTE,5],[f.SIZE,6],[f.SIZEFEATUREATTRIBUTE,6],[f.OPACITYFEATUREATTRIBUTE,7]]);let kh=class Hh extends vs{initializeProgram(e){return new Ss(e.rctx,Hh.shader.get().build(this.configuration),jh)}_makePipelineState(e,t){const i=this.configuration,s=e===Ze.NONE;return zt({blending:i.output===O.Color||i.output===O.Alpha?s?rr:nn(e):null,depthTest:{func:on(e)},depthWrite:s?i.writeDepth?xs:null:dh(e),colorWrite:Gi,stencilWrite:i.hasOccludees?hs:null,stencilTest:i.hasOccludees?t?Sa:an:null,polygonOffset:{factor:0,units:-10}})}initializePipeline(){return this.configuration.occluder&&(this._occluderPipelineTransparent=zt({blending:rr,depthTest:Kn,depthWrite:null,colorWrite:Gi,stencilWrite:null,stencilTest:Ju}),this._occluderPipelineOpaque=zt({blending:rr,depthTest:Kn,depthWrite:null,colorWrite:Gi,stencilWrite:Qu,stencilTest:Ku}),this._occluderPipelineMaskWrite=zt({blending:null,depthTest:ep,depthWrite:null,colorWrite:null,stencilWrite:hs,stencilTest:Sa})),this._occludeePipelineState=this._makePipelineState(this.configuration.transparencyPassType,!0),this._makePipelineState(this.configuration.transparencyPassType,!1)}getPipelineState(e,t){return t?this._occludeePipelineState:this.configuration.occluder?e===Y.TRANSPARENT_OCCLUDER_MATERIAL?this._occluderPipelineTransparent:e===Y.OCCLUDER_MATERIAL?this._occluderPipelineOpaque:this._occluderPipelineMaskWrite:super.getPipelineState(e,t)}};kh.shader=new bs(Gm,()=>wr(()=>Promise.resolve().then(()=>E_),void 0));let Mm=class extends Ja{constructor(e){super(e,new Vm),this._vertexAttributeLocations=jh,this._configuration=new Pu,this._layout=this.createLayout()}dispose(){}getConfiguration(e,t){return this._configuration.output=e,this._configuration.space=t.slot===Y.DRAPED_MATERIAL?Di.Draped:this.parameters.worldSpace?Di.World:Di.Screen,this._configuration.hideOnShortSegments=this.parameters.hideOnShortSegments,this._configuration.hasCap=this.parameters.cap!==er.BUTT,this._configuration.anchor=this.parameters.anchor,this._configuration.hasTip=this.parameters.hasTip,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.hasOccludees=this.parameters.hasOccludees,this._configuration.writeDepth=this.parameters.writeDepth,this._configuration.vvColor=this.parameters.vvColorEnabled,this._configuration.vvOpacity=this.parameters.vvOpacityEnabled,this._configuration.vvSize=this.parameters.vvSizeEnabled,this._configuration.occluder=this.parameters.renderOccluded===eo.OccludeAndTransparentStencil,this._configuration.transparencyPassType=t.transparencyPassType,this._configuration.hasMultipassTerrain=t.multipassTerrain.enabled,this._configuration.cullAboveGround=t.multipassTerrain.cullAboveGround,this._configuration}intersect(){}createLayout(){const e=Fi().vec3f(f.POSITION).vec2f(f.UV0).vec3f(f.AUXPOS1);return this.parameters.worldSpace&&e.vec3f(f.NORMAL),this.parameters.vvSizeEnabled?e.f32(f.SIZEFEATUREATTRIBUTE):e.f32(f.SIZE),this.parameters.vvColorEnabled?e.f32(f.COLORFEATUREATTRIBUTE):e.vec4f(f.COLOR),this.parameters.vvOpacityEnabled&&e.f32(f.OPACITYFEATUREATTRIBUTE),e}createBufferWriter(){return new Um(this._layout,this.parameters)}requiresSlot(e,t){return t===O.Color||t===O.Alpha||t===O.Highlight||t===O.Depth?e===Y.DRAPED_MATERIAL?!0:this.parameters.renderOccluded===eo.OccludeAndTransparentStencil?e===Y.OPAQUE_MATERIAL||e===Y.OCCLUDER_MATERIAL||e===Y.TRANSPARENT_OCCLUDER_MATERIAL:t===O.Color||t===O.Alpha?e===(this.parameters.writeDepth?Y.TRANSPARENT_MATERIAL:Y.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL):e===Y.OPAQUE_MATERIAL:!1}createGLMaterial(e){return new zm(e)}},zm=class extends tp{_updateParameters(e){return this.updateTexture(this._material.parameters.textureId),this._material.setParameters(this.textureBindParameters),this.ensureTechnique(kh,e)}_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&this._material.setParameters({hasOccludees:e.hasOccludees})}beginSlot(e){return this._output!==O.Color&&this._output!==O.Alpha||this._updateOccludeeState(e),this._updateParameters(e)}},Vm=class extends sh{constructor(){super(...arguments),this.width=0,this.color=[1,1,1,1],this.placement="end",this.cap=er.BUTT,this.anchor=Zl.Center,this.hasTip=!1,this.worldSpace=!1,this.hideOnShortSegments=!1,this.writeDepth=!0,this.hasSlicePlane=!1,this.vvFastUpdate=!1,this.hasOccludees=!1}},Um=class{constructor(e,t){this.vertexBufferLayout=e,this._parameters=t}allocate(e){return this.vertexBufferLayout.createBuffer(e)}elementCount(){return this._parameters.placement==="begin-end"?12:6}write(e,t,i,s,a){const n=i.vertexAttributes.get(f.POSITION).data,o=n.length/3;let l=[1,0,0];const h=i.vertexAttributes.get(f.NORMAL);this._parameters.worldSpace&&d(h)&&(l=h.data);let c=1,u=0;this._parameters.vvSizeEnabled?u=i.vertexAttributes.get(f.SIZEFEATUREATTRIBUTE).data[0]:i.vertexAttributes.has(f.SIZE)&&(c=i.vertexAttributes.get(f.SIZE).data[0]);let p=[1,1,1,1],m=0;this._parameters.vvColorEnabled?m=i.vertexAttributes.get(f.COLORFEATUREATTRIBUTE).data[0]:i.vertexAttributes.has(f.COLOR)&&(p=i.vertexAttributes.get(f.COLOR).data);let g=0;this._parameters.vvOpacityEnabled&&(g=i.vertexAttributes.get(f.OPACITYFEATUREATTRIBUTE).data[0]);const _=new Float32Array(s.buffer);let S=a*(this.vertexBufferLayout.stride/4);const P=(T,G,z,R)=>{if(_[S++]=T[0],_[S++]=T[1],_[S++]=T[2],_[S++]=z[0],_[S++]=z[1],_[S++]=G[0],_[S++]=G[1],_[S++]=G[2],this._parameters.worldSpace&&(_[S++]=l[0],_[S++]=l[1],_[S++]=l[2]),this._parameters.vvSizeEnabled?_[S++]=u:_[S++]=c,this._parameters.vvColorEnabled)_[S++]=m;else{const L=Math.min(4*R,p.length-4);_[S++]=p[L+0],_[S++]=p[L+1],_[S++]=p[L+2],_[S++]=p[L+3]}this._parameters.vvOpacityEnabled&&(_[S++]=g)};let x;(function(T){T[T.ASCENDING=1]="ASCENDING",T[T.DESCENDING=-1]="DESCENDING"})(x||(x={}));const A=(T,G)=>{const z=D(Fm,n[3*T],n[3*T+1],n[3*T+2]),R=Nm;let L=T+G;do D(R,n[3*L],n[3*L+1],n[3*L+2]),L+=G;while(Cd(z,R)&&L>=0&&L<o);e&&(ge(z,z,e),ge(R,R,e)),P(z,R,[-1,-1],T),P(z,R,[1,-1],T),P(z,R,[1,1],T),P(z,R,[-1,-1],T),P(z,R,[1,1],T),P(z,R,[-1,1],T)},E=this._parameters.placement;E!=="begin"&&E!=="begin-end"||A(0,x.ASCENDING),E!=="end"&&E!=="begin-end"||A(o-1,x.DESCENDING)}};const Fm=C(),Nm=C(),Bm=["polyline","polygon","extent"];let Wh=class qh extends mt{constructor(e,t,i,s){super(e,t,i,s)}async doLoad(){if(this._vvConvertOptions={modelSize:[1,1,1],symbolSize:[1,1,1],unitInMeters:1,transformation:{anchor:[0,0,0],scale:[1,1,1],rotation:[0,0,0]},supportedTypes:{size:!0,color:!0,opacity:!0,rotation:!1}},this._context.renderer&&this._context.renderer.visualVariables&&this._context.renderer.visualVariables.length>0?this._fastUpdates=xr(this._context.renderer,this._vvConvertOptions):this._fastUpdates={enabled:!1},!this._drivenProperties.size&&(this.symbolLayer.size!=null?this.symbolLayer.size:ua(1))<0)throw new We("graphics3dlinesymbollayer:invalid-size","Symbol sizes may not be negative values");this._markerTexture=d(this.symbolLayer.marker)&&d(this._context.sharedResources.textures)?wu(this._context.sharedResources.textures,$m(this.symbolLayer.marker.style)):null}_getMaterialParameters(e,t=!1){var a;const i=this._getCombinedOpacityAndColor(t&&this._markerColor||this._materialColor);this._patternHidesLine&&!t&&(i[3]=0);const s={width:this._computeMaterialWidth((a=this.symbolLayer)==null?void 0:a.size),color:i,hasPolygonOffset:!0,join:this.symbolLayer.join||"miter",cap:Ia(this.symbolLayer.cap||"butt"),hasSlicePlane:this._context.slicePlaneEnabled,isClosed:e,stipplePattern:Xl(this.symbolLayer.pattern),stippleScaleWithLineWidth:!0};return this._fastUpdates&&this._fastUpdates.visualVariables?{...s,...this._fastUpdates.materialParameters}:s}get _materialColor(){return $s(this.symbolLayer.material,e=>e.color)}get _markerColor(){return $s(this.symbolLayer.marker,e=>e.color)}get _lineMaterial(){return b(this._lineMaterialCached)&&(this._lineMaterialCached=new Yi(this._getMaterialParameters(!1)),this._context.stage.add(this._lineMaterialCached)),this._lineMaterialCached}get _ringMaterial(){return b(this._ringMaterialCached)&&(this._ringMaterialCached=new Yi(this._getMaterialParameters(!0)),this._context.stage.add(this._ringMaterialCached)),this._ringMaterialCached}get _wireframeLineMaterial(){return b(this._wireframeLineMaterialCached)&&(this._wireframeLineMaterialCached=new Yi({...this._getMaterialParameters(!1),wireframe:!0}),this._context.stage.add(this._wireframeLineMaterialCached)),this._wireframeLineMaterialCached}get _wireframeRingMaterial(){return b(this._wireframeRingMaterialCached)&&(this._wireframeRingMaterialCached=new Yi({...this._getMaterialParameters(!0),wireframe:!0}),this._context.stage.add(this._wireframeRingMaterialCached)),this._wireframeRingMaterialCached}get _markerMaterial(){return b(this._markerMaterialCached)&&d(this.symbolLayer.marker)&&d(this._markerTexture)&&(this._markerMaterialCached=new Mm({...this._getMaterialParameters(!1,!0),placement:this.symbolLayer.marker.placement,textureId:this._markerTexture.texture.id}),this._context.stage.add(this._markerMaterialCached)),this._markerMaterialCached}destroy(){super.destroy(),this._forEachMaterial(e=>this._context.stage.remove(e)),this._lineMaterialCached=null,this._ringMaterialCached=null,this._wireframeLineMaterialCached=null,this._wireframeRingMaterialCached=null,this._markerMaterialCached=null,this._markerTexture=Sl(this._markerTexture)}_getDrivenSize(e){return this._drivenProperties.size&&e.size?Re(fg(e.size)):1}_getSizeFeatureAttributeData(e){return this._fastUpdates.enabled&&this._fastUpdates.visualVariables.size?Ot(this._fastUpdates.visualVariables.size.field,e):null}_getDrivenColor(e){const t=tt(1,1,1,1);return this._drivenProperties.color&&e.color&&(t[0]=e.color[0],t[1]=e.color[1],t[2]=e.color[2],e.color.length>0&&(t[3]=e.color[3])),this._drivenProperties.opacity&&e.opacity&&(t[3]=e.opacity),t}_getColorFeatureAttributeData(e){return this._fastUpdates.enabled&&this._fastUpdates.visualVariables.color?Ot(this._fastUpdates.visualVariables.color.field,e):null}_getOpacityFeatureAttributeData(e){return this._fastUpdates.enabled&&this._fastUpdates.visualVariables.opacity?Ot(this._fastUpdates.visualVariables.opacity.field,e):null}createGraphics3DGraphic(e){const t=e.graphic;if(!this._validateGeometry(t.geometry,Bm,this.symbolLayer.type))return null;const i=this.setGraphicElevationContext(t,new Je);return this.ensureDrapedStatus(i.mode==="on-the-ground"),this.draped?this._createAsOverlay(e,this._context.layer.uid):this._createAs3DShape(e,i,t.uid)}applyRendererDiff(e,t){for(const i in e.diff){if(i!=="visualVariables"||!Cr(this._fastUpdates,t,this._vvConvertOptions))return H.Recreate_Symbol;this._forEachMaterial(s=>s.setParameters(this._fastUpdates.materialParameters))}return H.Fast_Update}prepareSymbolLayerPatch(e){var a,n;if(e.diff.type!=="partial")return;const t=e.diff.diff,i={};((a=t.size)==null?void 0:a.type)==="complete"&&(i.width=this._computeMaterialWidth(t.size.newValue),delete t.size),((n=t.cap)==null?void 0:n.type)==="complete"&&(i.cap=Ia(pe(t.cap.newValue,"butt")),delete t.cap);const s=this._prepareMarkerPatch(e,t);this._prepareMaterialPatch(e,t,s),e.symbolLayerStatePatches.push(()=>this._forEachMaterial(o=>o.setParameters(i)))}layerOpacityChanged(){this._forEachMaterial((e,t)=>this._updateMaterialLayerOpacity(e,t))}_forEachMaterial(e){d(this._lineMaterialCached)&&e(this._lineMaterialCached),d(this._ringMaterialCached)&&e(this._ringMaterialCached),d(this._wireframeLineMaterialCached)&&e(this._wireframeLineMaterialCached),d(this._wireframeRingMaterialCached)&&e(this._wireframeRingMaterialCached),d(this._markerMaterialCached)&&e(this._markerMaterialCached,!0)}_updateMaterialLayerOpacity(e,t=!1){const i=e.parameters.color,s=ie(this.symbolLayer,"material","color"),a=this._patternHidesLine&&!t?0:this._getCombinedOpacity(s),n=tt(i[0],i[1],i[2],a);e.setParameters({color:n})}layerElevationInfoChanged(e,t,i){const s=this._elevationContext.mode,a=Ar(qh.elevationModeChangeTypes,i,s);if(a!==q.UPDATE)return a;const n=Ye(s);return this.updateGraphics3DGraphicElevationInfo(e,t,()=>n)}slicePlaneEnabledChanged(){const e={hasSlicePlane:this._context.slicePlaneEnabled};return this._forEachMaterial(t=>t.setParameters(e)),!0}physicalBasedRenderingChanged(){return!0}pixelRatioChanged(){return!0}skipHighSymbolLodsChanged(){return!0}_getGeometryAsPolygonOrPolyline(e){switch(e.type){case"extent":if(e instanceof Ba)return Fa.fromExtent(e);break;case"polygon":case"polyline":return e}return null}_createAs3DShape(e,t,i){const s=e.graphic,a=this._getGeometryAsPolygonOrPolyline(s.geometry),n=a.type==="polygon"?a.rings:a.paths,o=new Array,l=_e(),h=Ou(a,this._context.elevationProvider,this._context.renderCoordsHelper,t),c=a.type==="polygon"?"rings":"paths";this._logGeometryCreationWarnings(h,n,c,"LineSymbol3DLayer");for(let m=0;m<h.lines.length;m++){const g=h.lines[m],_=g.position,S=g.mapPositions;if(d(this._context.clippingExtent)&&(B(l),Ve(l,S),!it(l,this._context.clippingExtent)))continue;const P=this._createGeometry(a.type==="polygon"?this._ringMaterial:this._lineMaterial,e,_,S,a.type,nr.ELEVATED,i);o.push(P),Ie.LINE_WIREFRAMES&&o.push(P.instantiate({material:a.type==="polygon"?this._wireframeRingMaterial:this._wireframeLineMaterial})),d(this._markerMaterial)&&o.push(P.instantiate({material:this._markerMaterial}))}if(o.length===0)return null;const u=new ui({geometries:o,castShadow:!1,metadata:{layerUid:this._context.layer.uid,graphicUid:i}}),p=new gt(this,u,o,null,null,Ag,t);return p.alignedSampledElevation=h.sampledElevation,p.needsElevationUpdates=Ye(t.mode),p}_createGeometry(e,t,i,s,a,n,o){const l=a==="polygon",h=this._fastUpdates.enabled&&this._fastUpdates.visualVariables.color,c=this._fastUpdates.enabled&&this._fastUpdates.visualVariables.size,u=this._context.stage.renderView.getObjectAndLayerIdColor({graphicUid:o,layerUid:this._context.layer.uid});return ba(e,{overlayInfo:n===nr.DRAPED?{spatialReference:this._context.overlaySR,renderCoordsHelper:this._context.renderCoordsHelper}:null,removeDuplicateStartEnd:l,mapPositions:s,attributeData:{position:i,size:c?null:this._getDrivenSize(t.renderingInfo),color:h?null:this._getDrivenColor(t.renderingInfo),sizeFeature:this._getSizeFeatureAttributeData(t.graphic),colorFeature:this._getColorFeatureAttributeData(t.graphic),opacityFeature:this._getOpacityFeatureAttributeData(t.graphic)}},u)}_createAsOverlay(e,t){const i=e.graphic,s=this._getGeometryAsPolygonOrPolyline(i.geometry),a=s.type==="polygon"?s.rings:s.paths,n=s.type==="polygon"?this._ringMaterial:this._lineMaterial;n.renderPriority=this._renderPriority;const o=Ie.LINE_WIREFRAMES?s.type==="polygon"?this._wireframeRingMaterial:this._wireframeLineMaterial:null,l=this._markerMaterial;d(o)&&(o.renderPriority=this._renderPriority-.001),d(l)&&(l.renderPriority=this._renderPriority-.002);const h=new Array,c=_e(),u=B(),p=Eu(s,this._context.overlaySR),m=s.type==="polygon"?"rings":"paths";this._logGeometryCreationWarnings(p,a,m,"LineSymbol3DLayer");for(const g of p.lines){if(B(c),Ve(c,g.position),!it(c,this._context.clippingExtent))continue;lr(u,c);const _=P=>{const x=this._createGeometry(P,e,g.position,void 0,s.type,nr.DRAPED,i.uid),A=new ur(x,{layerUid:t,graphicUid:i.uid});return h.push(A),A};if(d(l)){const P=_(l),x=se(this.symbolLayer.marker).placement;x!=="begin"&&x!=="begin-end"||Ve(c,g.position,0,1),x!=="end"&&x!=="begin-end"||Ve(c,g.position,g.position.length-3,1),this._updateBoundingSphere(P,c)}const S=_(n);if(this._updateBoundingSphere(S,c),Ie.LINE_WIREFRAMES){const P=_(o);this._updateBoundingSphere(P,c)}}return new Os(this,h,u,this._context.drapeSourceRenderer)}_updateBoundingSphere(e,t){ss(e.boundingSphere,.5*(t[0]+t[3]),.5*(t[1]+t[4]),0,.5*Math.sqrt((t[3]-t[0])*(t[3]-t[0])+(t[4]-t[1])*(t[4]-t[1])))}get _patternHidesLine(){const e=this.symbolLayer.pattern;return d(e)&&e.type==="style"&&e.style==="none"}_computeMaterialWidth(e){return e=pe(e,ua(1)),this._drivenProperties.size?this._fastUpdates.enabled&&this._fastUpdates.visualVariables.size?Re(1):1:Re(e)}_prepareMaterialPatch(e,t,i){var o;const s=t.material;if(b(s))return void(i.changed&&i.useMaterialColor&&this._patchMaterialColor(this._getCombinedOpacityAndColor(this._materialColor),this._markerMaterialCached,e));if(s.type==="collection")return;const a=s.type==="complete"?$s(s.newValue,l=>l.color):((o=s.diff.color)==null?void 0:o.type)==="complete"?s.diff.color.newValue:null,n=this._getCombinedOpacityAndColor(a);i.useMaterialColor&&this._patchMaterialColor(Pd(n),this._markerMaterialCached,e),this._patternHidesLine&&(n[3]=0),this._patchMaterialColor(n,this._lineMaterialCached,e),delete t.material}_prepareMarkerPatch(e,t){const i=t.marker;if(b(i)||i.type!=="partial"||d(i.diff.style)||d(i.diff.placement)||d(i.diff.color)&&i.diff.color.type!=="complete")return{changed:!1,useMaterialColor:b(this._markerColor)};const s=i.diff.color;if(b(s))return delete t.marker,{changed:!1,useMaterialColor:b(this._markerColor)};const a=se(s.newValue);return b(a)?(delete t.marker,{changed:!0,useMaterialColor:!0}):(this._patchMaterialColor(this._getCombinedOpacityAndColor(a),this._markerMaterialCached,e),delete t.marker,{changed:!0,useMaterialColor:!1})}_patchMaterialColor(e,t,i){b(t)||i.symbolLayerStatePatches.push(()=>t.setParameters({color:e}))}};var nr;Wh.elevationModeChangeTypes={definedChanged:q.RECREATE,staysOnTheGround:q.NONE,onTheGroundChanged:q.RECREATE},function(r){r[r.DRAPED=0]="DRAPED",r[r.ELEVATED=1]="ELEVATED"}(nr||(nr={}));const jm=["mesh"];class km extends mt{constructor(e,t,i,s){super(e,t,i,s),this._materials=new Map,this._textures=new Map,this.ensureDrapedStatus(!1)}async doLoad(){Ie.DRAW_MESH_GEOMETRY_NORMALS&&(this._debugVertexNormalMaterial=new Wn({color:[1,0,1,1]}),this._debugFaceNormalMaterial=new Wn({color:[0,1,1,1]}))}destroy(){super.destroy(),this._context.stage.removeMany(Array.from(this._materials.values(),e=>e.material)),this._context.stage.removeMany(Array.from(this._textures.values())),this._materials.clear(),this._textures.clear()}createGraphics3DGraphic(e){const t=e.graphic;if(!this._validateGeometry(t.geometry,jm,"fill on mesh-3d"))return null;const i=this.setGraphicElevationContext(t,new Je),s=e.renderingInfo;return this._createAs3DShape(t,s,i,t.uid)}layerOpacityChanged(e,t){const i=this._getLayerOpacity();this._materials.forEach(s=>{s.material.setParameters({layerOpacity:i});const a=s.material.parameters;this._setMaterialTransparentParameter(a,s),s.material.setParameters({transparent:a.transparent})}),e.forEach(s=>{const a=t(s);d(a)&&a.layerOpacityChanged(i,this._context.isAsync)})}layerElevationInfoChanged(e,t){return this.updateGraphics3DGraphicElevationInfo(e,t,kt)}slicePlaneEnabledChanged(e,t){return this._materials.forEach(i=>{i.material.setParameters({hasSlicePlane:this._context.slicePlaneEnabled})}),e.forEach(i=>{const s=t(i);d(s)&&s.slicePlaneEnabledChanged(this._context.slicePlaneEnabled,this._context.isAsync)}),!0}physicalBasedRenderingChanged(){const e=this._usePBR();return this._materials.forEach(t=>t.material.setParameters({usePBR:e})),!0}pixelRatioChanged(){return!0}skipHighSymbolLodsChanged(){return!0}_requiresSymbolVertexColors(){return this._drivenProperties.color||this._drivenProperties.opacity}_colorOrTextureUid(e){return b(e)?"-":e instanceof ue?e.toHex():e.contentHash}_materialPropertiesDefault(e,t){const i=this._requiresSymbolVertexColors(),s=!!e.vertexAttributes.color,a=!!e.vertexAttributes.tangent;return{hasSymbolVertexColors:i,hasVertexColors:s,hasVertexTangents:a,uid:`vc:${s},vt:${a},vct${t},svc:${i}`}}_materialProperties(e,t,i){const s=this._materialPropertiesDefault(e,i);if(!t.material)return s;const{color:a,colorTexture:n,normalTexture:o,doubleSided:l,alphaCutoff:h,alphaMode:c}=t.material,u=this._colorOrTextureUid(a),p=this._colorOrTextureUid(n),m=this._colorOrTextureUid(o);if(s.color=a,s.colorTexture=n,s.normalTexture=o,s.uid=`${s.uid},cmuid:${u},ctmuid:${p},ntmuid:${m},ds:${l},ac:${h},am:${c}`,t.material instanceof Bp){const{metallic:g,roughness:_,metallicRoughnessTexture:S,emissiveColor:P,emissiveTexture:x,occlusionTexture:A}=t.material,E=this._colorOrTextureUid(S),T=this._colorOrTextureUid(P),G=this._colorOrTextureUid(x),z=this._colorOrTextureUid(A);s.metallic=g,s.roughness=_,s.metallicRoughnessTexture=S,s.emissiveColor=P,s.emissiveTexture=x,s.occlusionTexture=A,s.colorTextureTransform=t.material.colorTextureTransform,s.normalTextureTransform=t.material.normalTextureTransform,s.emissiveTextureTransform=t.material.emissiveTextureTransform,s.occlusionTextureTransform=t.material.occlusionTextureTransform,s.metallicRoughnessTextureTransform=t.material.metallicRoughnessTextureTransform,s.uid=`${s.uid},mrm:${g},mrr:${_},mrt:${E},emuid:${T},etmuid:${G},otmuid:${z}`}return s}_setInternalColorValueParameters(e,t){t.diffuse=ue.toUnitRGB(e),t.opacity=e.a}_getLoadableTextureResource(e){return e.data?e.data:e.url}_getInternalTextureId(e){const t=this._getInternalTexture(e,qt.Opaque);return d(t)?t.id:null}_getInternalTexture(e,t){const i=this._getLoadableTextureResource(e);if(!i)return null;const s=`${e.contentHash}/${t}`;let a=this._textures.get(s);return a||(a=new Ka(js(i)?i.data:i,{mipmap:!0,wrap:this._castTextureWrap(e.wrap),noUnpackFlip:!0,preMultiplyAlpha:!js(i)&&t!==qt.Opaque,encoding:js(i)&&d(i.encoding)?i.encoding:void 0}),this._textures.set(s,a),this._context.stage.add(a),this._context.stage.loadImmediate(a)),a}_castTextureWrap(e="repeat"){if(typeof e=="string"){const t=this._castTextureWrapIndividual(e);return{s:t,t}}return{s:this._castTextureWrapIndividual(e.horizontal),t:this._castTextureWrapIndividual(e.vertical)}}_castTextureWrapIndividual(e){switch(e){case"clamp":return ir.CLAMP_TO_EDGE;case"mirror":return ir.MIRRORED_REPEAT;default:return ir.REPEAT}}_setInternalMaterialParameters(e,t){if(d(e.color)&&this._setInternalColorValueParameters(e.color,t),d(e.colorTexture)){const i=this._getInternalTexture(e.colorTexture,t.textureAlphaMode);d(i)?(t.textureId=i.id,t.textureAlphaPremultiplied=!!i.params.preMultiplyAlpha):t.textureId=void 0}d(e.normalTexture)&&(t.normalTextureId=this._getInternalTextureId(e.normalTexture)),d(e.emissiveColor)&&(t.emissiveFactor=ue.toUnitRGB(e.emissiveColor)),d(e.emissiveTexture)&&(t.emissiveTextureId=this._getInternalTextureId(e.emissiveTexture)),d(e.occlusionTexture)&&(t.occlusionTextureId=this._getInternalTextureId(e.occlusionTexture)),d(e.metallicRoughnessTexture)&&(t.metallicRoughnessTextureId=this._getInternalTextureId(e.metallicRoughnessTexture)),t.colorTextureTransformMatrix=Ni(e.colorTextureTransform),t.normalTextureTransformMatrix=Ni(e.normalTextureTransform),t.occlusionTextureTransformMatrix=Ni(e.occlusionTextureTransform),t.emissiveTextureTransformMatrix=Ni(e.emissiveTextureTransform),t.metallicRoughnessTextureTransformMatrix=Ni(e.metallicRoughnessTextureTransform)}_setExternalMaterialParameters(e){const t=this._drivenProperties.color;let i=d(this.symbolLayer.material)?this.symbolLayer.material.colorMixMode:null;if(t)e.externalColor=ri;else{const s=d(this.symbolLayer.material)?this.symbolLayer.material.color:null;d(s)?e.externalColor=ue.toUnitRGBA(s):(i=null,e.externalColor=ri)}i&&(e.colorMixMode=i),e.castShadows=!!this.symbolLayer.castShadows}_hasTransparentVertexColors(e){const t=e.vertexAttributes.color;if(b(t))return!1;for(let i=3;i<t.length;i+=4)if(t[i]!==255)return!0;return!1}_getOrCreateMaterial(e,t){var g,_,S;const i=(g=t.material)==null?void 0:g.color,s=(_=t.material)==null?void 0:_.colorTexture,a=(S=t.material)==null?void 0:S.alphaMode,n=a==="blend",o=a!=="opaque"&&(this._hasTransparentVertexColors(e)||d(i)&&i.a<1||d(s)&&s.transparent||n),l=this._materialProperties(e,t,o),h=this._materials.get(l.uid);if(h)return h.material;const c={material:null,isComponentTransparent:o,alphaMode:t.material?t.material.alphaMode:"opaque"},u=l.metallicRoughnessTexture==null&&l.metallic==null&&l.roughness==null,p={usePBR:this._usePBR(),isSchematic:u,hasVertexColors:l.hasVertexColors,hasSymbolColors:l.hasSymbolVertexColors,hasVertexTangents:l.hasVertexTangents,ambient:Li,diffuse:si,opacity:1,doubleSided:!0,doubleSidedType:"winding-order",cullFace:Ae.None,layerOpacity:this._getLayerOpacity(),hasSlicePlane:this._context.slicePlaneEnabled,initTextureTransparent:!0};u||(p.mrrFactors=[l.metallic!=null?l.metallic:1,l.roughness!=null?l.roughness:1,.5]),t.material&&(p.doubleSided=t.material.doubleSided,p.cullFace=t.material.doubleSided?Ae.None:Ae.Back,p.textureAlphaCutoff=t.material.alphaCutoff),this._setExternalMaterialParameters(p),this._setMaterialTransparentParameter(p,c),this._setInternalMaterialParameters(l,p);const m=new yr(p);return c.material=m,this._materials.set(l.uid,c),this._context.stage.add(m),m}_usePBR(){return this._context.physicalBasedRenderingEnabled}_setMaterialTransparentParameter(e,t){e.transparent=this.needsDrivenTransparentPass||t.isComponentTransparent||e.layerOpacity<1||e.opacity<1||e.externalColor&&e.externalColor[3]<1,t.alphaMode==="auto"?e.textureAlphaMode=e.transparent?qt.MaskBlend:qt.Opaque:e.textureAlphaMode=t.alphaMode==="opaque"?qt.Opaque:t.alphaMode==="mask"?qt.Mask:qt.Blend}_addDebugNormals(e,t){const i=t.length,s=e.spatialReference.isGeographic?20015077/180:1,a=.1*Math.max(e.extent.width*s,e.extent.height*s,e.extent.zmax-e.extent.zmin),n=[],o=[],l=[],h=[];for(let m=0;m<i;m++){const g=t[m],_=g.vertexAttributes.get(f.POSITION),S=g.vertexAttributes.get(f.NORMAL),P=g.indices.get(f.POSITION),x=g.indices.get(f.NORMAL),A=_.data,E=S.data;for(let T=0;T<P.length;T++){const G=3*P[T],z=3*x[T];for(let R=0;R<3;R++)n.push(A[G+R]);for(let R=0;R<3;R++)n.push(A[G+R]+E[z+R]*a);if(o.push(o.length),o.push(o.length),T%3==0){this._calculateFaceNormal(A,P,T,Pi),this._getFaceVertices(A,P,T,re,xi,Ci),me(re,re,xi),me(re,re,Ci),ae(re,re,1/3);for(let R=0;R<3;R++)l.push(re[R]);for(let R=0;R<3;R++)l.push(re[R]+Pi[R]*a);h.push(h.length),h.push(h.length)}}}const c=t[0].transformation,u=new Nt(this._debugVertexNormalMaterial,[[f.POSITION,new N(n,3,!0)]],[[f.POSITION,o]],null,li.Line);t.push(u),u.transformation=c;const p=new Nt(this._debugFaceNormalMaterial,[[f.POSITION,new N(l,3,!0)]],[[f.POSITION,h]],null,li.Line);p.transformation=c,t.push(p)}_createAs3DShape(e,t,i,s){const a=e.geometry;if(a.type!=="mesh")return null;const n=this._createGeometryInfo(a,t,s);if(b(n))return null;const{geometries:o,objectTransformation:l}=n;Ie.DRAW_MESH_GEOMETRY_NORMALS&&this._addDebugNormals(a,o);const h=new ui({geometries:o,metadata:{layerUid:this._context.layer.uid,graphicUid:s}});h.transformation=l;const c=hh(this.symbolLayer,{opacity:this._getLayerOpacity()}),u=d(c)?new Tg(o[0].material,[c],{mergeGeometries:!0,hasSlicePlane:this._context.slicePlaneEnabled}):null,p=new gt(this,h,o,null,null,_r,i,u);p.needsElevationUpdates=kt(i.mode),p.useObjectOriginAsAttachmentOrigin=!0,i.centerPointInElevationSR=this._getCenterPointInElevationSR(h);const{elevationProvider:m,renderCoordsHelper:g}=this._context,_=(S,P)=>Er(S,m,i,g,P);return p.alignedSampledElevation=_r(p,i,m.spatialReference,_,g),p}_getCenterPointInElevationSR(e){const t=Ui(0,0,0,d(this._context.elevationProvider.spatialReference)?this._context.elevationProvider.spatialReference:null);return wd([e.transformation[12],e.transformation[13],e.transformation[14]],this._context.renderCoordsHelper.spatialReference,t),t}_createComponentNormals(e,t,i,s){switch(i.shading||"flat"){default:case"source":return this._createComponentNormalsSource(e,t,i,s);case"flat":return this._createComponentNormalsFlat(e,s);case"smooth":return this._createComponentNormalsSmooth(e,s)}}_createComponentNormalsSource(e,t,i,s){if(b(t))return this._createComponentNormalsFlat(e,s);let a=!1;if(!i.trustSourceNormals)for(let n=0;n<s.length;n+=3){this._calculateFaceNormal(e,s,n,Pi);for(let o=0;o<3;o++){const l=3*s[n+o];re[0]=t[l+0],re[1]=t[l+1],re[2]=t[l+2],k(Pi,re)<0&&(t[l+0]=-t[l+0],t[l+1]=-t[l+1],t[l+2]=-t[l+2],a=!0)}}return new Xs(t,s,a)}_createComponentNormalsFlat(e,t){const i=qe(t.length),s=new Array(3*t.length);for(let a=0;a<t.length;a+=3){const n=this._calculateFaceNormal(e,t,a,Pi);for(let o=0;o<3;o++)i[a+o]=n[o],s[a+o]=a/3}return new Xs(i,s,!1)}_createComponentNormalsSmooth(e,t){const i={};for(let n=0;n<t.length;n+=3){const o=this._calculateFaceNormal(e,t,n,Pi);for(let l=0;l<3;l++){const h=t[n+l];let c=i[h];c||(c={normal:C(),count:0},i[h]=c),me(c.normal,c.normal,o),c.count++}}const s=qe(3*t.length),a=new Array(3*t.length);for(let n=0;n<t.length;n++){const o=i[t[n]];o.count!==1&&(ne(o.normal,o.normal),o.count=1);for(let l=0;l<3;l++)s[3*n+l]=o.normal[l];a[n]=n}return new Xs(s,a,!1)}_getFaceVertices(e,t,i,s,a,n){const o=3*t[i+0],l=3*t[i+1],h=3*t[i+2];s[0]=e[o+0],s[1]=e[o+1],s[2]=e[o+2],a[0]=e[l+0],a[1]=e[l+1],a[2]=e[l+2],n[0]=e[h+0],n[1]=e[h+1],n[2]=e[h+2]}_calculateFaceNormal(e,t,i,s){return this._getFaceVertices(e,t,i,re,xi,Ci),oe(xi,xi,re),oe(Ci,Ci,re),Et(re,xi,Ci),ne(s,re),s}_getOrCreateComponents(e){return pe(e.components,Hm)}_createPositionBuffer(e,t){let i=e.vertexAttributes.position;const s=t.reprojection===Me.ECEF?t.transformBeforeProject:null;if(d(s)&&(i=jp(i,new Float64Array(i.length),s)),t.reprojection===Me.NONE)return t.needsBufferCopy?new Float64Array(i):i;const a=d(s)?i:new Float64Array(i.length);return ut(i,e.spatialReference,0,a,this._context.renderCoordsHelper.spatialReference,0,i.length/3),a}_createNormalBuffer(e,t,i){let s=e.vertexAttributes.normal;if(b(s))return null;const a=i.reprojection===Me.ECEF?i.transformBeforeProject:null;if(d(a)&&(s=kp(s,new Float32Array(s.length),a)),this._context.graphicsCoreOwner.view.viewingMode==="local"||i.reprojection===Me.NONE)return i.needsBufferCopy&&e.vertexAttributes.normal===s?new Float32Array(s):s;const n=e.vertexAttributes.position,o=d(a)?s:new Float32Array(s.length);return Hp(s,n,t,e.spatialReference,o)}_createTangentBuffer(e,t,i){let s=e.vertexAttributes.tangent;if(b(s))return null;const a=i.reprojection===Me.ECEF?i.transformBeforeProject:null;if(d(a)&&(s=Wp(s,new Float32Array(s.length),a)),this._context.graphicsCoreOwner.view.viewingMode==="local"||i.reprojection===Me.NONE)return i.needsBufferCopy&&e.vertexAttributes.normal===s?new Float32Array(s):s;const n=e.vertexAttributes.position,o=d(a)?s:new Float32Array(s.length);return qp(s,n,t,e.spatialReference,o)}_createColorBuffer(e){return e.vertexAttributes.color}_createSymbolColorBuffer(e){if(this._requiresSymbolVertexColors()){const t=this._getVertexOpacityAndColor(e),i=Yp(ie(this.symbolLayer,"material","colorMixMode")),s=new Uint8Array(4);return Zp(t,i,s),s}return null}_createBuffers(e,t){const i=e.vertexAttributes&&e.vertexAttributes.position;if(!i)return this.logger.warn("Mesh geometry must contain position vertex attributes"),null;const s=e.vertexAttributes.normal,a=e.vertexAttributes.uv,n=e.vertexAttributes.tangent;if(d(s)&&s.length!==i.length)return this.logger.warn("Mesh normal vertex buffer must contain the same number of elements as the position buffer"),null;if(d(n)&&n.length/4!=i.length/3)return this.logger.warn("Mesh tangent vertex buffer must contain the same number of elements as the position buffer"),null;if(d(a)&&a.length/2!=i.length/3)return this.logger.warn("Mesh uv vertex buffer must contain the same number of elements as the position buffer"),null;const o=this._computeReprojectionInfo(e),l=this._createPositionBuffer(e,o),h=this._createColorBuffer(e),c=this._createSymbolColorBuffer(t),u=this._createNormalBuffer(e,l,o),p=this._createTangentBuffer(e,l,o);return{positionBuffer:l,normalBuffer:u,tangentBuffer:p,uvBuffer:a,colorBuffer:h,symbolColorBuffer:c,objectTransformation:o.reprojection===Me.NONE&&d(o.objectTransformation)?o.objectTransformation:this._transformOriginLocal(e,l,u,p),geometryTransformation:o.reprojection===Me.NONE&&d(o.geometryTransformation)?o.geometryTransformation:j()}}_computeReprojectionInfo(e){const t=d(e.transform),i=t&&e.transform.geographic||this._context.renderCoordsHelper.viewingMode===Ut.Local?Me.NONE:Me.ECEF;if(t){if(i===Me.NONE){const a=j();return hi(e.spatialReference,e.transform.origin,a,this._context.renderCoordsHelper.spatialReference),{reprojection:i,objectTransformation:a,geometryTransformation:ku(e.transform.localMatrix),needsBufferCopy:!1}}const s=Od(j(),e.transform.origin);return Ha(s,s,e.transform.localMatrix),{reprojection:i,transformBeforeProject:s,needsBufferCopy:!0}}return{reprojection:i,needsBufferCopy:!0}}_transformOriginLocal(e,t,i,s){const a=this._context.renderCoordsHelper.spatialReference,n=e.anchor;jr[0]=n.x,jr[1]=n.y,jr[2]=n.z;const o=j();hi(e.spatialReference,jr,o,a);const l=cs.fromTypedArray(t);if(ja(Bo,o),uh(l,l,Bo),d(i)||d(s)){if(ka(Hi,o),zl(Hi,Hi),d(i)){const h=gr.fromTypedArray(i);Ca(h,h,Hi)}if(d(s)){const h=gr.fromTypedArray(s,4*s.BYTES_PER_ELEMENT);Ca(h,h,Hi)}}return o}_validateFaces(e,t){const i=e.vertexAttributes.position.length/3,s=t.faces;if(s){let a=-1;for(let n=0;n<s.length;n++){const o=s[n];o>a&&(a=o)}if(i<=a)return this.logger.warn(`Vertex index ${a} is out of bounds of the mesh position buffer`),!1}else if(i%3!=0)return this.logger.warn("Mesh position buffer length must be a multiple of 9 if no component faces are defined (3 values per vertex * 3 vertices per triangle)"),!1;return!0}_getOrCreateFaces(e,t){return t.faces?t.faces:mp(e.vertexAttributes.position.length/3)}_isOutsideClippingArea(e){if(!this._context.clippingExtent)return!1;const t=e.vertexAttributes&&e.vertexAttributes.position;if(!t)return!1;const i=this._context.elevationProvider.spatialReference;let s;const a=t.length/3;return d(i)&&!e.spatialReference.equals(i)?(s=new Float64Array(t.length),ut(e.vertexAttributes.position,e.spatialReference,0,s,i,0,a)):s=t,B(Js),Ve(Js,s,0,a),!it(Js,this._context.clippingExtent)}_createGeometryInfo(e,t,i){if(!Vl(e.spatialReference,this._context.graphicsCoreOwner.view.spatialReference))return this.logger.warn("Geometry spatial reference is not compatible with the view"),null;if(this._isOutsideClippingArea(e))return null;const s=this._createBuffers(e,t);if(b(s))return null;const{positionBuffer:a,uvBuffer:n,colorBuffer:o,symbolColorBuffer:l,normalBuffer:h,tangentBuffer:c,objectTransformation:u,geometryTransformation:p}=s,m=this._getOrCreateComponents(e),g=new Array;let _=!1;for(const S of m){if(!this._validateFaces(e,S))return null;const P=this._getOrCreateFaces(e,S);if(P.length===0)continue;const x=this._createComponentNormals(a,h,S,P);x.didFlipNormals&&(_=!0);const A=[[f.POSITION,new N(a,3,!0)],[f.NORMAL,new N(x.normals,3,!0)]],E=[[f.POSITION,P],[f.NORMAL,x.indices]];d(o)&&(A.push([f.COLOR,new N(o,4,!0)]),E.push([f.COLOR,P])),d(l)&&(A.push([f.SYMBOLCOLOR,new N(l,4,!0)]),E.push([f.SYMBOLCOLOR,new Array(P.length).fill(0)])),d(n)&&(A.push([f.UV0,new N(n,2,!0)]),E.push([f.UV0,P])),d(c)&&(A.push([f.TANGENT,new N(c,4,!0)]),E.push([f.TANGENT,P]));const T=this._context.stage.renderView.getObjectAndLayerIdColor({graphicUid:i,layerUid:this._context.layer.uid}),G=this._getOrCreateMaterial(e,S),z=new Nt(G,A,E,null,li.Mesh,T);z.transformation=p,g.push(z)}return _&&this.logger.warn("Normals have been automatically flipped to be consistent with the counter clock wise face winding order. It is better to generate mesh geometries that have consistent normals."),{geometries:g,objectTransformation:u}}}let Xs=class{constructor(e,t,i){this.normals=e,this.indices=t,this.didFlipNormals=i}};const jr=C(),re=C(),xi=C(),Ci=C(),Pi=C(),Bo=j(),Hi=Cs(),Js=_e(),Hm=[new Np];var Me;(function(r){r[r.NONE=0]="NONE",r[r.ECEF=1]="ECEF"})(Me||(Me={}));let Wm=class{constructor(e,t,i,s){this.graphics3DSymbolLayer=e,this.instanceIndex=t,this.elevationAligner=i,this.elevationContext=s,this.type="lod-instance",this._highlights=new Set,this.alignedSampledElevation=0,this.isElevationSource=!1,this.needsElevationUpdates=!1}initialize(){}setVisibility(e){const t=this._lodRenderer.instanceData;e!==t.getVisible(this.instanceIndex)&&t.setVisible(this.instanceIndex,e)}destroy(){this.instanceIndex!=null&&(this._lodRenderer.instanceData.removeInstance(this.instanceIndex),this.graphics3DSymbolLayer.notifyDestroyGraphicLayer(this))}alignWithElevation(e,t,i){if(this.elevationAligner){ql(this.elevationContext.featureExpressionInfoContext,i);const s=(n,o)=>Er(n,e,this.elevationContext,t,o),a=this.elevationAligner(this,this.elevationContext,e.spatialReference,s,t);d(a)&&(this.alignedSampledElevation=a)}}getCenterObjectSpace(e=C()){return this._lodRenderer.instanceData.getCombinedLocalTransform(this.instanceIndex,st),ge(e,this._lodRenderer.baseBoundingSphere.center,st)}getBoundingBoxObjectSpace(e=_e()){this._lodRenderer.instanceData.getCombinedLocalTransform(this.instanceIndex,st);const t=this._lodRenderer.baseBoundingBox;B(e);for(let i=0;i<8;++i)D(Ne,1&i?t[3]:t[0],2&i?t[4]:t[1],4&i?t[5]:t[2]),ge(Ne,Ne,st),cr(e,Ne);return e}computeAttachmentOrigin(e){this._lodRenderer.instanceData.getGlobalTransform(this.instanceIndex,st),e.render.origin[0]+=st[12],e.render.origin[1]+=st[13],e.render.origin[2]+=st[14],e.render.num++}async getProjectedBoundingBox(e,t,i,s,a){const n=this.getBoundingBoxObjectSpace(a),o=qm,l=El(n)?1:o.length;this._lodRenderer.instanceData.getGlobalTransform(this.instanceIndex,st);for(let c=0;c<l;c++){const u=o[c];Ne[0]=n[u[0]],Ne[1]=n[u[1]],Ne[2]=n[u[2]],ge(Ne,Ne,st),Zt[3*c+0]=Ne[0],Zt[3*c+1]=Ne[1],Zt[3*c+2]=Ne[2]}if(!e(Zt,0,l))return null;B(n);let h=null;this.calculateRelativeScreenBounds&&(h=this.calculateRelativeScreenBounds());for(let c=0;c<3*l;c+=3){for(let u=0;u<3;u++)n[u]=Math.min(n[u],Zt[c+u]),n[u+3]=Math.max(n[u+3],Zt[c+u]);h&&i.push({location:Zt.slice(c,c+3),screenSpaceBoundingRect:h})}if(t&&(dt(n,Qs),this.elevationContext.mode!=="absolute-height")){let c;const u=Za(n,t.service.spatialReference,t);try{c=await t.service.queryElevation(Qs[0],Qs[1],s,u,"ground")}catch{}d(c)&&Rl(n,0,0,-this.alignedSampledElevation+c)}return n}addObjectState(e,t){if(e===Ct.Highlight){const i=new ip(e);this._addHighlightId(i),t.addExternal(s=>{this._removeHighlightId(s)},i)}}removeObjectState(e){this._highlights.forEach(t=>e.remove(t))}_addHighlightId(e){this._highlights.add(e),this._lodRenderer.instanceData.setHighlight(this.instanceIndex,!0)}_removeHighlightId(e){this._highlights.delete(e),this._lodRenderer.instanceData.setHighlight(this.instanceIndex,this._highlights.size>0)}get _lodRenderer(){return this.graphics3DSymbolLayer.lodRenderer}};const Zt=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],Ne=C(),Qs=C(),qm=[[0,1,2],[3,1,2],[0,4,2],[3,4,2],[0,1,5],[3,1,5],[0,4,5],[3,4,5]],st=j();function Ym(r){const e=new Array;return r.stageResources.geometries.forEach(t=>{const i=r.stageResources.textures;e.push(new Ph(t,i))}),{components:e,minScreenSpaceRadius:pe(r.lodThreshold,0),pivotOffset:r.pivotOffset}}function Zm(r){return{levels:r.map(e=>Ym(e))}}var F;function Xm(r){let e=Fi().mat4f64(f.LOCALTRANSFORM).mat4f64(f.GLOBALTRANSFORM).vec4f64(f.BOUNDINGSPHERE).vec3f64(f.MODELORIGIN).mat3f(f.MODEL).mat3f(f.MODELNORMAL).vec2f(f.MODELSCALEFACTORS);return r.includes("color")&&(e=e.vec4f(f.COLOR)),r.includes("featureAttribute")&&(e=e.vec4f(f.FEATUREATTRIBUTE)),e=e.u8(f.STATE).u8(f.LODLEVEL),r.includes(f.OBJECTANDLAYERIDCOLOR)&&(e=e.vec4u8(f.OBJECTANDLAYERIDCOLOR)),e.alignTo(8),e}(function(r){r[r.ALLOCATED=1]="ALLOCATED",r[r.DEFAULT_ACTIVE=2]="DEFAULT_ACTIVE",r[r.VISIBLE=4]="VISIBLE",r[r.HIGHLIGHT=8]="HIGHLIGHT",r[r.HIGHLIGHT_ACTIVE=16]="HIGHLIGHT_ACTIVE",r[r.REMOVE=32]="REMOVE",r[r.TRANSFORM_CHANGED=64]="TRANSFORM_CHANGED",r[r.ACTIVE=18]="ACTIVE"})(F||(F={}));let Jm=class{constructor(e){this.localTransform=e.getField(f.LOCALTRANSFORM,ro),this.globalTransform=e.getField(f.GLOBALTRANSFORM,ro),this.modelOrigin=e.getField(f.MODELORIGIN,cs),this.model=e.getField(f.MODEL,mr),this.modelNormal=e.getField(f.MODELNORMAL,mr),this.modelScaleFactors=e.getField(f.MODELSCALEFACTORS,Op),this.boundingSphere=e.getField(f.BOUNDINGSPHERE,Ep),this.color=e.getField(f.COLOR,fr),this.featureAttribute=e.getField(f.FEATUREATTRIBUTE,fr),this.state=e.getField(f.STATE,so),this.lodLevel=e.getField(f.LODLEVEL,so),this.objectAndLayerIdColor=e.getField(f.OBJECTANDLAYERIDCOLOR,ln)}},Ai=class extends Ht{constructor(e,t){super(e),this.events=new gs,this._capacity=0,this._size=0,this._next=0,this._buffer=null,this._view=null,this._layout=Xm(t)}get capacity(){return this._capacity}get size(){return this._size}get buffer(){return this._buffer.buffer}get view(){return this._view}addInstance(){this._size+1>this._capacity&&this._grow();const e=this._findSlot();return this._view.state.set(e,F.ALLOCATED),this._size++,this.events.emit("instances-changed"),e}removeInstance(e){const t=this._view.state;ee(e>=0&&e<this._capacity&&(t.get(e)&F.ALLOCATED)!=0,"invalid instance handle"),this._getStateFlag(e,F.ACTIVE)?this._setStateFlags(e,F.REMOVE):this.freeInstance(e),this.events.emit("instances-changed")}freeInstance(e){const t=this._view.state;ee(e>=0&&e<this._capacity&&(t.get(e)&F.ALLOCATED)!=0,"invalid instance handle"),t.set(e,0),this._size--}setLocalTransform(e,t,i=!0){this._view.localTransform.setMat(e,t),i&&this.updateModelTransform(e)}getLocalTransform(e,t){this._view.localTransform.getMat(e,t)}setGlobalTransform(e,t,i=!0){this._view.globalTransform.setMat(e,t),i&&this.updateModelTransform(e)}getGlobalTransform(e,t){this._view.globalTransform.getMat(e,t)}updateModelTransform(e){const t=this._view,i=Tt,s=Ke;t.localTransform.getMat(e,jo),t.globalTransform.getMat(e,Ks);const a=Ha(Ks,Ks,jo);D(i,a[12],a[13],a[14]),t.modelOrigin.setVec(e,i),ka(s,a),t.model.setMat(e,s);const n=Xp(Tt,a);n.sort(),t.modelScaleFactors.set(e,0,n[1]),t.modelScaleFactors.set(e,1,n[2]),Ed(s,s),zl(s,s),t.modelNormal.setMat(e,s),this._setStateFlags(e,F.TRANSFORM_CHANGED),this.events.emit("instance-transform-changed",{index:e})}getModelTransform(e,t){const i=this._view;i.model.getMat(e,Ke),i.modelOrigin.getVec(e,Tt),t[0]=Ke[0],t[1]=Ke[1],t[2]=Ke[2],t[3]=0,t[4]=Ke[3],t[5]=Ke[4],t[6]=Ke[5],t[7]=0,t[8]=Ke[6],t[9]=Ke[7],t[10]=Ke[8],t[11]=0,t[12]=Tt[0],t[13]=Tt[1],t[14]=Tt[2],t[15]=1}applyShaderTransformation(e,t){d(this.shaderTransformation)&&this.shaderTransformation.applyTransform(this,e,t)}getCombinedModelTransform(e,t){return this.getModelTransform(e,t),d(this.shaderTransformation)&&this.shaderTransformation.applyTransform(this,e,t),t}getCombinedLocalTransform(e,t){return this._view.localTransform.getMat(e,t),d(this.shaderTransformation)&&this.shaderTransformation.applyTransform(this,e,t),t}getCombinedMaxScaleFactor(e){let t=this._view.modelScaleFactors.get(e,1);if(d(this.shaderTransformation)){const i=this.shaderTransformation.scaleFactor(Tt,this,e);t*=Math.max(i[0],i[1],i[2])}return t}getCombinedMedianScaleFactor(e){let t=this._view.modelScaleFactors.get(e,0);if(d(this.shaderTransformation)){const i=this.shaderTransformation.scaleFactor(Tt,this,e);t*=Qm(i[0],i[1],i[2])}return t}getModel(e,t){this._view.model.getMat(e,t)}setFeatureAttribute(e,t){this._view.featureAttribute.setVec(e,t)}getFeatureAttribute(e,t){this._view.featureAttribute.getVec(e,t)}setColor(e,t){this._view.color.setVec(e,t)}setObjectAndLayerIdColor(e,t){this._view.objectAndLayerIdColor.setVec(e,t)}getColor(e,t){this._view.color.getVec(e,t)}setVisible(e,t){t!==this.getVisible(e)&&(this._setStateFlag(e,F.VISIBLE,t),this.events.emit("instance-visibility-changed",{index:e}))}getVisible(e){return this._getStateFlag(e,F.VISIBLE)}setHighlight(e,t){t!==this.getHighlight(e)&&(this._setStateFlag(e,F.HIGHLIGHT,t),this.events.emit("instance-highlight-changed"))}getHighlight(e){return this._getStateFlag(e,F.HIGHLIGHT)}getState(e){return this._view.state.get(e)}getLodLevel(e){return this._view.lodLevel.get(e)}countFlags(e){let t=0;for(let i=0;i<this._capacity;++i)this.getState(i)&e&&++t;return t}_setStateFlags(e,t){const i=this._view.state;t=i.get(e)|t,i.set(e,t)}_clearStateFlags(e,t){const i=this._view.state;t=i.get(e)&~t,i.set(e,t)}_setStateFlag(e,t,i){i?this._setStateFlags(e,t):this._clearStateFlags(e,t)}_getStateFlag(e,t){return!!(this._view.state.get(e)&t)}_grow(){const e=Math.max(Km,Math.floor(this._capacity*ef)),t=this._layout.createBuffer(e);if(this._buffer){const i=new Uint8Array(this._buffer.buffer);new Uint8Array(t.buffer).set(i)}this._capacity=e,this._buffer=t,this._view=new Jm(this._buffer)}_findSlot(){const e=this._view.state;let t=this._next;for(;e.get(t)&F.ALLOCATED;)t=t+1===this._capacity?0:t+1;return this._next=t+1===this._capacity?0:t+1,t}};function Qm(r,e,t){return Math.max(Math.min(r,e),Math.min(Math.max(r,e),t))}y([v({constructOnly:!0})],Ai.prototype,"shaderTransformation",void 0),y([v()],Ai.prototype,"_size",void 0),y([v({readOnly:!0})],Ai.prototype,"size",null),Ai=y([Xe("esri.views.3d.webgl-engine.lib.lodRendering.InstanceData")],Ai);const Km=1024,ef=2,Tt=C(),Ke=Cs(),jo=j(),Ks=j();let tf=class extends Pa{constructor(e,t){super(i=>yp(this._instanceData.view.boundingSphere.getVec(i,this._tmpSphere)),{maximumDepth:25}),this._tmpSphere=_p(),this._tmpMat4=j(),this._instanceData=e,this._boundingSphere=t}addInstance(e){const t=this._instanceData.view.boundingSphere,i=this._instanceData.getCombinedModelTransform(e,this._tmpMat4);ge(this._tmpSphere,this._boundingSphere.center,i),this._tmpSphere[3]=this._boundingSphere.radius*Jp(i),t.setVec(e,this._tmpSphere),this.add([e])}removeInstance(e){this.remove([e])}},rf=class{constructor(e,t){this._worldSpaceRadius=e,this._minScreenSpaceRadii=t}selectLevel(e,t,i){const s=i.computeScreenPixelSizeAt(e),a=this._worldSpaceRadius*t/s;let n=0;for(let o=1;o<this._minScreenSpaceRadii.length;++o)a>=this._minScreenSpaceRadii[o]&&(n=o);return n}},sf=class extends Ru{constructor(e,t,i,s,a,n){super(e,t),this.layerUid=e,this.graphicUid=t,this.geometryId=i,this.triangleNr=s,this.baseBoundingSphere=a,this.numLodLevels=n}},af=class{constructor(e,t){const i=e.renderContext.rctx,s=t.geometry;this._materialRepository=e.materialRepository,s.material.setParameters({instancedDoublePrecision:!0});const a=s.material.createBufferWriter(),n=a.vertexBufferLayout,o=a.elementCount(s),l=a.allocate(o);a.write(null,null,s,l,0),this.geometry=s,this.material=s.material,this.glMaterials=new Au(s.material,this._materialRepository),this.vertexBufferLayout=n,this.vbo=yh.createVertex(i,ch.STATIC_DRAW,l.buffer),this.vao=new rp(i,ls,{geometry:mh(n)},{geometry:this.vbo}),this.vertexCount=o}destroy(){this.glMaterials.destroy(),this.vbo.dispose(),this.vao.dispose()}get boundingInfo(){return this.geometry.boundingInfo}get triangleCount(){return this.vertexCount/3}intersect(e,t,i,s,a,n,o,l){const h=this.geometry.id;this.material.intersect(this.geometry,e.transform.transform,e,i,s,(c,u,p,m,g)=>{if(c>=0){if(t!=null&&!t(e.rayBegin,e.rayEnd,c))return;const _=new sf(n.layerUid,n.graphicUid(a),h,p,o,l);if((e.results.min.drapedLayerOrder==null||g>=e.results.min.drapedLayerOrder)&&(e.results.min.dist==null||c<e.results.min.dist)&&e.results.min.set(Qr.LOD,_,c,u,e.transform.transform,g),e.options.store!==va.MIN&&(e.results.max.drapedLayerOrder==null||g>=e.results.max.drapedLayerOrder)&&(e.results.max.dist==null||c>e.results.max.dist)&&e.results.max.set(Qr.LOD,_,c,u,e.transform.transform,g),e.options.store===va.ALL){const S=Iu(e.results.min.ray);S.set(Qr.LOD,_,c,u,e.transform.transform,g),e.results.all.push(S)}}})}},nf=class Yh{static async create(e,t,i){const s=await Promise.allSettled(t.components.map(n=>e.controller.schedule(()=>new af(e,n),i))),a=s.map(n=>n.status==="fulfilled"?n.value:null).filter(d);if(vl(i)||a.length!==s.length){a.forEach(n=>n.destroy()),Oe(i);for(const n of s)if(n.status==="rejected")throw n.reason}return new Yh(t.minScreenSpaceRadius,a)}constructor(e,t){this.minScreenSpaceRadius=e,this.components=t}destroy(){this.components.forEach(e=>e.destroy())}intersect(e,t,i,s,a,n,o){this.components.forEach(l=>l.intersect(e,t,i,s,a,n,this.boundingSphere,o))}get boundingBox(){if(b(this._boundingBox)){const e=B();this.components.forEach(t=>{d(t.boundingInfo)&&(cr(e,t.boundingInfo.bbMin),cr(e,t.boundingInfo.bbMax))}),this._boundingBox=e}return this._boundingBox}get boundingSphere(){if(b(this._boundingSphere)){const e=this.boundingBox,t=C();dt(e,t),this._boundingSphere={center:t,radius:.5*Rd(e)}}return this._boundingSphere}get triangleCount(){return this.components.reduce((e,t)=>e+t.triangleCount,0)}},of=class{constructor(e,t,i){this._elementSize=t,this._buffer=yh.createVertex(e,ch.STATIC_DRAW),this.resize(i)}destroy(){this._buffer.dispose()}get elementSize(){return this._elementSize}get capacity(){return this._capacity}get array(){return this._array}get buffer(){return this._buffer}get memoryUsage(){return{cpu:this._capacity*this._elementSize,gpu:this._capacity*this._elementSize}}copyRange(e,t,i,s=0){const a=new Uint8Array(this.array,e*this.elementSize,(t-e)*this.elementSize);new Uint8Array(i.array,s*this.elementSize).set(a)}transferAll(){this._buffer.setData(this._array)}transferRange(e,t){const i=e*this._elementSize,s=t*this._elementSize;this._buffer.setSubData(new Uint8Array(this._array),i,i,s)}resize(e){const t=e*this._elementSize,i=new ArrayBuffer(t);this._array&&(e>=this._capacity?new Uint8Array(i).set(new Uint8Array(this._array)):new Uint8Array(i).set(new Uint8Array(this._array).subarray(0,e*this._elementSize))),this._array=i,this._buffer.setSize(t),this._capacity=e}},lf=class{constructor(e){this.modelOriginHi=e.getField(f.MODELORIGINHI,gr),this.modelOriginLo=e.getField(f.MODELORIGINLO,gr),this.model=e.getField(f.MODEL,mr),this.modelNormal=e.getField(f.MODELNORMAL,mr),this.color=e.getField(f.INSTANCECOLOR,fr),this.featureAttribute=e.getField(f.INSTANCEFEATUREATTRIBUTE,fr),this.objectAndLayerIdColor=e.getField(f.OBJECTANDLAYERIDCOLOR_INSTANCED,ln)}},ko=class{constructor(e,t){this._headIndex=0,this._tailIndex=0,this._firstIndex=null,this._captureFirstIndex=!0,this._updating=!1,this._prevHeadIndex=0,this._resized=!1,this._rctx=e,this._instanceBufferLayout=t,this._elementSize=t.stride,this._capacity=1}destroy(){this._buffer&&this._buffer.destroy()}get buffer(){return this._buffer.buffer}get view(){return this._view}get capacity(){return this._capacity}get size(){const e=this._headIndex,t=this._tailIndex;return e>=t?e-t:e+this._capacity-t}get isEmpty(){return this._headIndex===this._tailIndex}get isFull(){return this._tailIndex===(this._headIndex+1)%this._capacity}get headIndex(){return this._headIndex}get tailIndex(){return this._tailIndex}get firstIndex(){return this._firstIndex}get memoryUsage(){return this._buffer?this._buffer.memoryUsage:{cpu:0,gpu:0}}reset(){this._headIndex=0,this._tailIndex=0,this._firstIndex=null}startUpdateCycle(){this._captureFirstIndex=!0}beginUpdate(){ee(!this._updating,"already updating"),this._updating=!0,this._prevHeadIndex=this._headIndex}endUpdate(){ee(this._updating,"not updating"),this.size<cf*this.capacity&&this._shrink(),this._resized?(this._buffer.transferAll(),this._resized=!1):this._transferRange(this._prevHeadIndex,this._headIndex),this._updating=!1}allocateHead(){ee(this._updating,"not updating"),this.isFull&&this._grow();const e=this.headIndex;return this._captureFirstIndex&&(this._firstIndex=e,this._captureFirstIndex=!1),this._incrementHead(),ee(this._headIndex!==this._tailIndex,"invalid pointers"),e}freeTail(){ee(this._updating,"not updating"),ee(this.size>0,"invalid size");const e=this._tailIndex===this._firstIndex;this._incrementTail(),e&&(this._firstIndex=this._tailIndex)}_grow(){const e=Math.max(Ho,Math.floor(this._capacity*hf));this._resize(e)}_shrink(){const e=Math.max(Ho,Math.floor(this._capacity*df));this._resize(e)}_resize(e){if(ee(this._updating,"not updating"),e===this._capacity)return;const t=new of(this._rctx,this._elementSize,e);if(this._buffer){this._firstIndex&&(this._firstIndex=(this._firstIndex+this._capacity-this._tailIndex)%this._capacity);const i=this.size,s=this._compactInstances(t);ee(s===i,"invalid compaction"),this._buffer.destroy(),this._tailIndex=0,this._headIndex=s,this._prevHeadIndex=0}this._resized=!0,this._capacity=e,this._buffer=t,this._view=new lf(this._instanceBufferLayout.createView(this._buffer.array))}_compactInstances(e){const t=this._headIndex,i=this._tailIndex;return i<t?(this._buffer.copyRange(i,t,e),t-i):i>t?(this._buffer.copyRange(i,this._capacity,e),t>0&&this._buffer.copyRange(0,t,e,this._capacity-i),t+(this._capacity-i)):0}_incrementHead(e=1){this._headIndex=(this._headIndex+e)%this._capacity}_incrementTail(e=1){this._tailIndex=(this._tailIndex+e)%this._capacity}_transferRange(e,t){e<t?this._buffer.transferRange(e,t):e>t&&(t>0&&this._buffer.transferRange(0,t),this._buffer.transferRange(e,this._capacity))}};const Ho=1024,hf=2,cf=.3,df=.5,uf=r=>{const e=r.baseBoundingSphere.radius,t=r.levels.map(i=>i.minScreenSpaceRadius);return new rf(e,t)};let ke=class extends Ht{constructor(e,t){super(e),this.type=Qr.LOD,this.isGround=!1,this._levels=[],this._defaultRenderInstanceData=[],this._highlightRenderInstanceData=[],this._instanceIndex=0,this._cycleStartIndex=0,this._slicePlane=!1,this._camera=new Tu,this._updateCyclesWithStaticCamera=-1,this._needFullCycle=!1,this.slots=[Y.OPAQUE_MATERIAL,Y.TRANSPARENT_MATERIAL],this.canRender=!0,this._instanceData=new Ai({shaderTransformation:e.shaderTransformation},e.optionalFields),this._frameTask=t.registerTask(Or.LOD_RENDERER,this)}initialize(){this._instanceBufferLayout=gf(this.optionalFields),this._glInstanceBufferLayout=mh(this._instanceBufferLayout,1),this._instanceData.events.on("instances-changed",()=>this._requestUpdateCycle()),this._instanceData.events.on("instance-transform-changed",({index:e})=>{this._requestUpdateCycle(),this.metadata.notifyGraphicGeometryChanged(e)}),this._instanceData.events.on("instance-visibility-changed",({index:e})=>{this._requestUpdateCycle(!0),this.metadata.notifyGraphicVisibilityChanged(e)}),this._instanceData.events.on("instance-highlight-changed",()=>this._requestUpdateCycle(!0))}destroy(){this._frameTask.remove()}get _enableLevelSelection(){return this.symbol.levels.length>1}get levels(){return this._levels}get baseBoundingBox(){return this._levels[this._levels.length-1].boundingBox}get baseBoundingSphere(){return this._levels[this._levels.length-1].boundingSphere}get baseMaterial(){return this._levels[this._levels.length-1].components[0].material}get slicePlaneEnabled(){return this._slicePlane}set slicePlaneEnabled(e){this._slicePlane=e}get layerUid(){return this.metadata.layerUid}get instanceData(){return this._instanceData}get memoryUsage(){const e={cpu:0,gpu:0};return this._defaultRenderInstanceData.forEach(t=>{const i=t.memoryUsage;e.cpu+=i.cpu,e.gpu+=i.gpu}),this._highlightRenderInstanceData.forEach(t=>{const i=t.memoryUsage;e.cpu+=i.cpu,e.gpu+=i.gpu}),e}get renderStats(){const e=this._instanceData.size,t=[];return this._levels.forEach((i,s)=>{const a=this._defaultRenderInstanceData[s],n=this._highlightRenderInstanceData[s],o=a.size+n.size,l=i.triangleCount;t.push({renderedInstances:o,renderedTriangles:o*l,trianglesPerInstance:l})}),{totalInstances:e,renderedInstances:t.reduce((i,s)=>i+s.renderedInstances,0),renderedTriangles:t.reduce((i,s)=>i+s.renderedTriangles,0),levels:t}}async initializeRenderContext(e,t){this._context=e;const i=e.renderContext.rctx,s=await Promise.allSettled(this.symbol.levels.map(n=>(this._defaultRenderInstanceData.push(new ko(i,this._instanceBufferLayout)),this._highlightRenderInstanceData.push(new ko(i,this._instanceBufferLayout)),nf.create(e,n,t)))),a=s.map(n=>n.status==="fulfilled"?n.value:null).filter(d);if(vl(t)||a.length!==s.length){a.forEach(n=>n.destroy()),Oe(t);for(const n of s)if(n.status==="rejected")throw n.reason}this._levels=a,this._levelSelector=uf(this)}uninitializeRenderContext(){this._invalidateOctree(),this._levels.forEach(e=>e.destroy()),this._defaultRenderInstanceData.forEach(e=>e.destroy()),this._highlightRenderInstanceData.forEach(e=>e.destroy())}get needsTransparentPass(){return this._levels.some(e=>e.components.some(t=>t.material.requiresSlot(Y.TRANSPARENT_MATERIAL,O.Color)))}get needsHighlight(){return this._highlightRenderInstanceData.some(e=>e.size>0)}prepareRender(e){if(!Ie.LOD_INSTANCE_RENDERER_DISABLE_UPDATES){if(this._enableLevelSelection){const t=e.bindParameters.contentCamera.equals(this._camera);this._camera.copyFrom(e.bindParameters.contentCamera),t||this._requestUpdateCycle()}this._needFullCycle&&(this.runTask(qa),this._needFullCycle=!1)}}render(e){!this.baseMaterial.isVisible()||!this.baseMaterial.isVisibleForOutput(e.output)||(e.rctx.bindVAO(),e.output!==O.Highlight&&e.output!==O.ShadowHighlight&&this._renderComponents(e,this._defaultRenderInstanceData),e.output!==O.ShadowExcludeHighlight&&this._renderComponents(e,this._highlightRenderInstanceData))}intersect(e,t,i,s){if(!this.baseMaterial.isVisible()||b(this._octree))return;const a=C();oe(a,s,i);const n=o=>{this._instanceData.getCombinedModelTransform(o,Yo),e.transform.set(Yo),ge(Zo,i,e.transform.inverse),ge(Xo,s,e.transform.inverse);const l=this._instanceData.getState(o),h=this._instanceData.getLodLevel(o),c=this._levels.length;ee((l&F.ACTIVE)!=0,"invalid instance state"),ee(h>=0&&h<c,"invaid lod level"),this._levels[h].intersect(e,t,Zo,Xo,o,this.metadata,c)};this.baseMaterial.parameters.verticalOffset?this._octree.forEach(n):this._octree.forEachAlongRay(i,a,n)}queryDepthRange(e){return this._queryDepthRangeOctree(e)}notifyShaderTransformationChanged(){this._invalidateOctree(),this._requestUpdateCycle()}get _octree(){var e;if(b(this._octreeCached)){const t=this._instanceData,i=(e=t.view)==null?void 0:e.state;if(!i)return null;this._octreeCached=new tf(t,this.baseBoundingSphere);for(let s=0;s<t.capacity;++s)i.get(s)&F.ACTIVE&&this._octreeCached.addInstance(s)}return this._octreeCached}_invalidateOctree(){this._octreeCached=K(this._octreeCached)}_queryDepthRangeOctree(e){if(b(this._octree))return{near:1/0,far:-1/0};const t=e.viewForward,i=this._octree.findClosest(t,Pa.DepthOrder.FRONT_TO_BACK,e.frustum),s=this._octree.findClosest(t,Pa.DepthOrder.BACK_TO_FRONT,e.frustum);if(i==null||s==null)return{near:1/0,far:-1/0};const a=e.eye,n=this._instanceData.view;n.boundingSphere.getVec(i,at),oe(at,at,a);const o=k(at,t)-at[3];n.boundingSphere.getVec(s,at),oe(at,at,a);const l=k(at,t)+at[3];return{near:Math.max(e.near,o),far:Math.min(e.far,l)}}_requestUpdateCycle(e=!1){this._updateCyclesWithStaticCamera=-1,this._cycleStartIndex=this._instanceIndex,e&&(this._needFullCycle=!0,this._context.requestRender())}_startUpdateCycle(){this._updateCyclesWithStaticCamera++,this._defaultRenderInstanceData.forEach(e=>e.startUpdateCycle()),this._highlightRenderInstanceData.forEach(e=>e.startUpdateCycle())}get running(){return this._instanceData.size>0&&this._updateCyclesWithStaticCamera<1}runTask(e){const{_enableLevelSelection:t,_camera:i,_levelSelector:s}=this;this._defaultRenderInstanceData.forEach(c=>c.beginUpdate()),this._highlightRenderInstanceData.forEach(c=>c.beginUpdate());const a=this._instanceData,n=a.view;let o=a.size;const l=a.capacity;let h=this._instanceIndex;for(let c=0;c<o&&!e.done;++c){h===this._cycleStartIndex&&this._startUpdateCycle();const u=n.state.get(h);let p=0;if(!(u&F.ALLOCATED)){h=h+1===l?0:h+1,o++;continue}const m=n.lodLevel.get(h);if(u&F.DEFAULT_ACTIVE&&this._defaultRenderInstanceData[m].freeTail(),u&F.HIGHLIGHT_ACTIVE&&this._highlightRenderInstanceData[m].freeTail(),u&F.REMOVE)a.freeInstance(h);else if(u&F.VISIBLE){let g=0;t&&(n.modelOrigin.getVec(h,qo),g=s.selectLevel(qo,a.getCombinedMedianScaleFactor(h),i)),p=u&~(F.ACTIVE|F.TRANSFORM_CHANGED),g>=0&&(u&F.HIGHLIGHT?(Wo(this._highlightRenderInstanceData[g],n,h),p|=F.HIGHLIGHT_ACTIVE):(Wo(this._defaultRenderInstanceData[g],n,h),p|=F.DEFAULT_ACTIVE)),n.state.set(h,p),n.lodLevel.set(h,g)}else p=u&~(F.ACTIVE|F.TRANSFORM_CHANGED),n.state.set(h,p);if(d(this._octreeCached)){const g=!!(u&F.ACTIVE),_=!!(p&F.ACTIVE);!g&&_?this._octreeCached.addInstance(h):g&&!_?this._octreeCached.removeInstance(h):g&&_&&u&F.TRANSFORM_CHANGED&&(this._octreeCached.removeInstance(h),this._octreeCached.addInstance(h))}h=h+1===l?0:h+1,e.madeProgress()}this._instanceIndex=h,this._defaultRenderInstanceData.forEach(c=>c.endUpdate()),this._highlightRenderInstanceData.forEach(c=>c.endUpdate()),this._context.requestRender()}_renderComponents(e,t){this.levels.forEach((i,s)=>{const a=i.components.map(n=>this._beginComponent(e,t[s],n));i.components.forEach((n,o)=>this._renderComponent(e,a[o],t[s],n,s))})}_beginComponent(e,t,i){const{bindParameters:s,rctx:a,output:n}=e;if(t.size===0||!i.material.requiresSlot(s.slot,e.output))return null;const o=i.glMaterials.load(a,s.slot,n);return d(o)?o.beginSlot(s):null}_renderComponent(e,t,i,s,a){if(b(t))return;const{bindParameters:n,rctx:o}=e,l=o.bindTechnique(t,s.material.parameters,n);o.bindVAO(s.vao),t.ensureAttributeLocations(s.vao),l.bindDraw(mf,n,s.material.parameters),Ie.LOD_INSTANCE_RENDERER_COLORIZE_BY_LEVEL&&e.output===O.Color&&(l.setUniform4fv("externalColor",Jo[Math.min(a,Jo.length-1)]),l.setUniform1i("colorMixMode",sp.replace));const h=o.capabilities.instancing,c=i.capacity,u=i.headIndex,p=i.tailIndex,m=i.firstIndex,g=this._glInstanceBufferLayout,_=(S,P)=>{ig(o,ls,i.buffer,g,S),h.drawArraysInstanced(t.primitiveType,0,s.vertexCount,P-S),rg(o,ls,i.buffer,g)};s.material.parameters.transparent&&m!=null?u>p?(ee(m>=p&&m<=u,"invalid firstIndex"),_(m,u),_(p,m)):u<p&&(m<=u?(ee(m>=0&&m<=u,"invalid firstIndex"),_(m,u),_(p,c),_(0,m)):(ee(m>=p&&m<=c,"invalid firstIndex"),_(m,c),_(0,u),_(p,m))):u>p?_(p,u):u<p&&(_(0,u),_(p,c)),o.bindVAO(null)}};function Wo(r,e,t){const i=r.allocateHead();pf(e,t,r.view,i)}function pf(r,e,t,i){ap(r.modelOrigin,e,t.modelOriginHi,t.modelOriginLo,i),t.model.copyFrom(i,r.model,e),t.modelNormal.copyFrom(i,r.modelNormal,e),r.color&&t.color&&t.color.copyFrom(i,r.color,e),r.objectAndLayerIdColor&&t.objectAndLayerIdColor&&t.objectAndLayerIdColor.copyFrom(i,r.objectAndLayerIdColor,e),r.featureAttribute&&t.featureAttribute&&t.featureAttribute.copyFrom(i,r.featureAttribute,e)}function gf(r){let e=Fi().vec3f(f.MODELORIGINHI).vec3f(f.MODELORIGINLO).mat3f(f.MODEL).mat3f(f.MODELNORMAL);return d(r)&&r.includes("color")&&(e=e.vec4f(f.INSTANCECOLOR)),d(r)&&r.includes("featureAttribute")&&(e=e.vec4f(f.INSTANCEFEATUREATTRIBUTE)),d(r)&&r.includes("objectAndLayerIdColor")&&(e=e.vec4u8(f.OBJECTANDLAYERIDCOLOR_INSTANCED)),e}y([v({constructOnly:!0})],ke.prototype,"symbol",void 0),y([v({constructOnly:!0})],ke.prototype,"optionalFields",void 0),y([v({constructOnly:!0})],ke.prototype,"metadata",void 0),y([v({constructOnly:!0})],ke.prototype,"shaderTransformation",void 0),y([v()],ke.prototype,"_instanceData",void 0),y([v()],ke.prototype,"_cycleStartIndex",void 0),y([v({readOnly:!0})],ke.prototype,"_enableLevelSelection",null),y([v()],ke.prototype,"_updateCyclesWithStaticCamera",void 0),y([v({readOnly:!0})],ke.prototype,"running",null),ke=y([Xe("esri.views.3d.webgl-engine.lib.lodRendering.LodRenderer")],ke);const qo=C(),at=ms(),Yo=j(),Zo=C(),Xo=C(),Jo=[tt(1,0,1,1),tt(0,0,1,1),tt(0,1,0,1),tt(1,1,0,1),tt(1,0,0,1)],mf=new Ip;class Qo{constructor(e,t,i,s,a,n,o,l,h,c,u,p){this.lodResources=e,this.lodRenderer=t,this.stageResources=i,this.originalMaterialParameters=s,this.resourceSize=a,this.isEsriSymbolResource=n,this.isWosr=o,this.resourceBoundingBox=l,this.symbolSize=h,this.extentPadding=c,this.physicalBasedRenderingEnabled=u,this.pivotOffset=p}}class ff extends mt{getCachedSize(){const[e,t,i]=d(this._resources)?this._resources.symbolSize:[1,1,1];return{width:e,depth:t,height:i}}constructor(e,t,i,s){super(e,t,i,s),this._resources=null,this._optionalFields=new Array,this._instanceIndexToGraphicUid=new Map,this._hasLoadedPBRTextures=!1,this._disposeResourceHandles=new Array,this.ensureDrapedStatus(!1),this._hasLoadedPBRTextures=i.physicalBasedRenderingEnabled}async doLoad(e){if(!this._drivenProperties.size&&ys(this.symbolLayer))throw new Error;const t=this.symbolLayer;if(this._isPrimitive){const i=t.resource?t.resource.primitive:Al;this._resources=await this._createResourcesForPrimitive(i,e)}else this._resources=await this._createResourcesForUrl(t.resource.href,e);this.layerOpacityChanged(),this.slicePlaneEnabledChanged(),this.physicalBasedRenderingChanged(),this.complexity=this.computeComplexity()}get extentPadding(){return d(this._resources)?this._resources.extentPadding:0}get _isPrimitive(){return!(this.symbolLayer.resource&&this.symbolLayer.resource.href)}get lodRenderer(){return d(this._resources)?this._resources.lodRenderer:null}_setMaterialTransparencyParams(e,t=ie(this.symbolLayer,"material","color")){const i=this._getCombinedOpacity(t),s=i<1||this.needsDrivenTransparentPass;return e.transparent=s,e.opacity=i,e.cullFace=s?Ae.None:Ae.Back,e}async _createResourcesForPrimitive(e,t){if(!Dg(e))throw new Error(`Unknown object symbol primitive: ${e}`);const i=this.symbolLayer,s=_e(Ad(e)),a=xt($r(s)),n=xt(Gs(a,i)),o=dr(n),l=!1,h=!1,c={usePBR:this._context.physicalBasedRenderingEnabled,isSchematic:!0,ambient:si,diffuse:si,hasSlicePlane:this._context.slicePlaneEnabled,hasSliceHighlight:!1,castShadows:this.symbolLayer.castShadows,offsetTransparentBackfaces:!this.symbolLayer.isPrimitive},u=!!c.usePBR;this._setMaterialTransparencyParams(c);const p=this.symbol;if(p.type==="point-3d"&&p.verticalOffset){const{screenLength:x,minWorldLength:A,maxWorldLength:E}=p.verticalOffset;c.verticalOffset={screenLength:Re(x),minWorldLength:A||0,maxWorldLength:d(E)?E:1/0},c.castShadows=!1}if(this._context.screenSizePerspectiveEnabled&&(c.screenSizePerspective=this._context.sharedResources.screenSizePerspectiveSettings),this._drivenProperties.color)c.externalColor=ri;else{const x=d(i.material)?i.material.color:null,A=d(x)?ue.toUnitRGBA(x):ri;c.externalColor=A}this._fastUpdates=xr(this._context.renderer,this._fastVisualVariableConvertOptions(s,n,a,pi)),c.instanced=["transformation"],this._fastUpdates.enabled?(Object.assign(c,this._fastUpdates.materialParameters),c.instanced.push("featureAttribute"),this._optionalFields.push("featureAttribute")):this._hasPerInstanceColor()&&(c.instanced.push("color"),this._optionalFields.push("color")),da("enable-feature:objectAndLayerId-rendering")&&(c.instanced.push("objectAndLayerIdColor"),this._optionalFields.push("objectAndLayerIdColor"));const m=new yr(c),g=Oh(e,m);if(!g)throw new Error(`Unknown object symbol primitive: ${e}`);const _=zr(g).map(x=>({opacity:1,transparent:x.parameters.transparent})),S=await this._createStageResources(g,u,t),P=await this._createLodRenderer(g,t);return new Qo(g,P,S,_,a,l,h,s,n,o,u,pi)}async _createResourcesForUrl(e,t){const i=["transformation"],s={materialParamsMixin:{instanced:i,hasSlicePlane:this._context.slicePlaneEnabled,castShadows:this.symbolLayer.castShadows},streamDataRequester:this._context.streamDataRequester,cache:this._context.sharedResources.objectResourceCache};this._fastUpdates=xr(this._context.renderer,this._fastVisualVariableConvertOptions(pi,pi,pi,pi)),this._fastUpdates.enabled?(Object.assign(s.materialParamsMixin,this._fastUpdates.materialParameters),i.push("featureAttribute"),this._optionalFields.push("featureAttribute")):this._hasPerInstanceColor()&&(i.push("color"),this._optionalFields.push("color")),da("enable-feature:objectAndLayerId-rendering")&&(i.push("objectAndLayerIdColor"),this._optionalFields.push("objectAndLayerIdColor"));const a=this.symbol;if(a.type==="point-3d"&&a.verticalOffset){const{screenLength:M,minWorldLength:le,maxWorldLength:X}=a.verticalOffset;s.materialParamsMixin.verticalOffset={screenLength:Re(M),minWorldLength:le||0,maxWorldLength:d(X)?X:1/0},s.materialParamsMixin.castShadows=!1}s.signal=t,s.usePBR=this._context.physicalBasedRenderingEnabled,s.skipHighLods=this._context.skipHighSymbolLods;const n=s.usePBR,o=await Tp(e,s),l=o.isEsriSymbolResource,h=o.isWosr,c=Zm(o.lods);c.levels.sort((M,le)=>M.minScreenSpaceRadius-le.minScreenSpaceRadius);const u=this._context,p=this.symbolLayer.material,m=this._getExternalColorParameters(p),g=ie(this.symbolLayer,"material","color"),_=this._getCombinedOpacity(g,{hasIntrinsicColor:!0}),S=this.needsDrivenTransparentPass,P=zr(c),x=zr(c).map(M=>({opacity:M.parameters.opacity||1,transparent:M.parameters.transparent}));P.forEach(M=>{const le=M.parameters;M.setParameters(m);const X=le.opacity*_,Ue=X<1||S||le.transparent;M.setParameters({opacity:X,transparent:Ue}),u.screenSizePerspectiveEnabled&&M.setParameters({screenSizePerspective:u.sharedResources.screenSizePerspectiveSettings})});const A=o.referenceBoundingBox,E=xt($r(A)),T=xt(c.levels[0].pivotOffset),G=xt(Gs(E,this.symbolLayer)),z=dr(G);Cr(this._fastUpdates,this._context.renderer,this._fastVisualVariableConvertOptions(A,G,E,T))&&P.forEach(M=>M.setParameters(this._fastUpdates.materialParameters));const R=await this._createStageResources(c,n,t),L=await this._createLodRenderer(c,t);return new Qo(c,L,R,x,E,l,h,A,G,z,n,T)}_addDisposeResource(e){this._disposeResourceHandles.push(e)}async _createStageResources(e,t,i){const s=this._context.stage,a=zr(e);t!==this._context.physicalBasedRenderingEnabled&&this.physicalBasedRenderingChanged(),s.addMany(a),this._addDisposeResource(()=>s.removeMany(a));const n=po(e);s.addMany(n),this._addDisposeResource(()=>s.removeMany(n)),await s.load(n,i),Oe(i);const o=go(e);return s.addMany(o),this._addDisposeResource(()=>s.removeMany(o)),{materials:a,textures:n,geometries:o}}async _createLodRenderer(e,t){const i=this._context.stage,s={layerUid:this._context.layer.uid,graphicUid:o=>this._instanceIndexToGraphicUid.get(o),notifyGraphicGeometryChanged:o=>this._context.notifyGraphicGeometryChanged(this._instanceIndexToGraphicUid.get(o)),notifyGraphicVisibilityChanged:o=>this._context.notifyGraphicVisibilityChanged(this._instanceIndexToGraphicUid.get(o))},a=this._fastUpdates.enabled?{applyTransform:(o,l,h)=>{o.getFeatureAttribute(l,kr),za(h,Aa(this._fastUpdates.materialParameters,kr,h))},scaleFactor:(o,l,h)=>(l.getFeatureAttribute(h,kr),wm(o,this._fastUpdates.materialParameters,kr))}:null,n=new ke({symbol:e,optionalFields:this._optionalFields,metadata:s,shaderTransformation:a},this._context.scheduler);return n.slicePlaneEnabled=this._context.slicePlaneEnabled,this._addDisposeResource(()=>{i.removeRenderPlugin(n),n.destroy()}),await i.addRenderPlugin(n.slots,n,t),n}_getExternalColorParameters(e){const t={};return this._drivenProperties.color?t.externalColor=ri:d(e)&&d(e.color)?t.externalColor=ue.toUnitRGBA(e.color):(t.externalColor=ri,t.colorMixMode="ignore"),t}destroy(){super.destroy(),this._cleanupResources()}_cleanupResources(){this._disposeResourceHandles.forEach(e=>e()),this._disposeResourceHandles.length=0,this._resources=null}createGraphics3DGraphic(e){const t=e.graphic;if(!this._validateGeometry(t.geometry))return null;const i=vr(t.geometry);if(b(i))return this.logger.warn(`unsupported geometry type for icon symbol: ${t.geometry.type}`),null;const s=this.setGraphicElevationContext(t,new Je),a=e.renderingInfo;return this._createAs3DShape(t,i,a,s,t.uid,e.layer.uid)}notifyDestroyGraphicLayer(e){this._instanceIndexToGraphicUid.delete(e.instanceIndex)}graphicLayerToGraphicId(){return 0}layerOpacityChanged(){if(b(this._resources))return;const e=this._drivenProperties.opacity,t=!this._isPrimitive,i=this._resources.stageResources.materials,s=this._resources.originalMaterialParameters;for(let a=0;a<i.length;a++){const n=i[a],o=ie(this.symbolLayer,"material","color"),l=s[a],h=this._getCombinedOpacity(o,{hasIntrinsicColor:t})*l.opacity,c=h<1||e||l.transparent;n.setParameters({opacity:h,transparent:c}),this._isPrimitive&&n.setParameters({cullFace:c?Ae.None:Ae.Back})}}layerElevationInfoChanged(e,t){return this.updateGraphics3DGraphicElevationInfo(e,t,kt)}slicePlaneEnabledChanged(){if(b(this._resources))return!0;this._resources.lodRenderer.slicePlaneEnabled=this._context.slicePlaneEnabled;for(const e of this._resources.stageResources.materials)e.setParameters({hasSlicePlane:this._context.slicePlaneEnabled});return!0}physicalBasedRenderingChanged(){if(b(this._resources))return!0;const{stageResources:e,isWosr:t}=this._resources;for(const i of e.materials)this._isPrimitive?i.setParameters({usePBR:this._context.physicalBasedRenderingEnabled,isSchematic:!0}):t||i.setParameters({usePBR:this._context.physicalBasedRenderingEnabled,isSchematic:!1});return this._hasLoadedPBRTextures!==!1||this._context.physicalBasedRenderingEnabled!==!0||(this._hasLoadedPBRTextures=!0,!1)}pixelRatioChanged(){return!0}skipHighSymbolLodsChanged(){return!1}applyRendererDiff(e,t){if(b(this._resources))return H.Recreate_Symbol;const{stageResources:{materials:i},lodRenderer:s,resourceBoundingBox:a,symbolSize:n,resourceSize:o,pivotOffset:l}=this._resources;for(const h in e.diff){if(h!=="visualVariables"||!Cr(this._fastUpdates,t,this._fastVisualVariableConvertOptions(a,n,o,l)))return H.Recreate_Symbol;for(const c of i)c.setParameters(this._fastUpdates.materialParameters);s.notifyShaderTransformationChanged()}return H.Fast_Update}computeComplexity(){if(b(this._resources))return super.computeComplexity();const e=this._resources.lodResources,t=wh(e.levels[0]).reduce((a,n)=>a+n.indices.get(f.POSITION).length,0)/3,i=a=>Array.from(a.vertexAttributes.values()).reduce((n,o)=>{var l;return n+(((l=o.data.buffer)==null?void 0:l.byteLength)??0)},0)+Array.from(a.indices.values()).reduce((n,o)=>n+(Array.isArray(o)?12*o.length:o.buffer.byteLength),0),s=po(e).reduce((a,n)=>a+(n.params.encoding&&n.params.encoding==="image/ktx2"?n.estimatedTexMemRequired:n.estimatedTexMemRequired/4),0)+go(e).reduce((a,n)=>a+i(n),0);return{primitivesPerFeature:t,primitivesPerCoordinate:0,drawCallsPerFeature:0,estimated:!1,memory:{...Rh(this.symbol,this.symbolLayer),resourceBytes:s}}}_hasLodRenderer(){return d(this._resources)}_createAs3DShape(e,t,i,s,a,n){if(!this._hasLodRenderer()||b(this._resources))return null;const o=this.getFastUpdateAttrValues(e),l=!this._fastUpdates.enabled&&this._hasPerInstanceColor()?Ki(i.color,i.opacity):null,h=this._context.clippingExtent;if(or(t,wi,this._context.elevationProvider.spatialReference),d(h)&&!Va(h,wi))return null;const c=this._requiresTerrainElevation(s),u=this._computeGlobalTransform(t,s,ea,Hr),p=this._computeLocalTransform(this._resources,this.symbolLayer,i,Ko),m=this._resources.lodRenderer.instanceData,g=m.addInstance();this._instanceIndexToGraphicUid.set(g,a),m.setLocalTransform(g,p,!1),m.setGlobalTransform(g,u),o&&m.setFeatureAttribute(g,o),l&&m.setColor(g,l),d(this._context.stage.renderView.objectAndLayerIdRenderHelper)&&m.setObjectAndLayerIdColor(g,this._context.stage.renderView.objectAndLayerIdRenderHelper.getObjectAndLayerIdColor({graphicUid:a,layerUid:n}));const _=new Wm(this,g,Rg,s);return c&&(_.alignedSampledElevation=Hr.sampledElevation),_.needsElevationUpdates=kt(s.mode),br(_,t,this._context.elevationProvider),_}_computeGlobalTransform(e,t,i,s){return Er(e,this._context.elevationProvider,t,this._context.renderCoordsHelper,s),wi[0]=e.x,wi[1]=e.y,wi[2]=s.z,hi(e.spatialReference,wi,i,this._context.renderCoordsHelper.spatialReference),i}_computeLocalTransform(e,t,i,s){return $l(s),this._applyObjectRotation(i,!1,s),this._applyObjectRotation(t,!0,s),this._applyObjectScale(e,i,s),this._applyAnchor(e,t,s),s}_applyObjectScale(e,t,i){if(this._fastUpdates.enabled&&this._fastUpdates.requiresShaderTransformation)return;const s=this._drivenProperties.size&&t.size?t.size:e.symbolSize,a=qn(s,e.symbolSize,e.resourceSize,this._context.renderCoordsHelper.unitInMeters);a[0]===1&&a[1]===1&&a[2]===1||Gl(i,i,a)}prepareSymbolLayerPatch(e){if(e.diff.type!=="partial")return;const t=e.diff.diff;this._preparePatchTransform(e,t),this._preparePatchColor(e,t)}updateGeometry(e,t){if(b(this._resources))return!0;const i=t&&vr(t);if(b(i))return!1;const s=this.getGeometryElevationMode(t);return e.elevationContext.mode===s&&(this._computeGlobalTransform(i,e.elevationContext,ea,Hr),this._requiresTerrainElevation(e.elevationContext)&&(e.alignedSampledElevation=Hr.sampledElevation),this._resources.lodRenderer.instanceData.setGlobalTransform(e.instanceIndex,ea,!0),br(e,i,this._context.elevationProvider),!0)}_preparePatchTransform(e,t){if(!(t.heading||t.tilt||t.roll||t.width||t.height||t.depth||t.anchor||t.anchorPosition)||b(this._resources))return;const i=(g,_,S)=>pe(g!=null&&g.type==="complete"?g.newValue:_,S),s=i(t.heading,this.symbolLayer.heading,0),a=i(t.tilt,this.symbolLayer.tilt,0),n=i(t.roll,this.symbolLayer.roll,0),o=i(t.width,this.symbolLayer.width,void 0),l=i(t.height,this.symbolLayer.height,void 0),h=i(t.depth,this.symbolLayer.depth,void 0),c=i(t.anchor,this.symbolLayer.anchor,void 0),u=i(t.anchorPosition,this.symbolLayer.anchorPosition,void 0);delete t.heading,delete t.tilt,delete t.roll,delete t.width,delete t.height,delete t.depth,delete t.anchor,delete t.anchorPosition;const p={heading:s,tilt:a,roll:n,anchor:c,anchorPosition:u},m=this._resources;this.loadStatus===Ge.LOADED&&e.symbolLayerStatePatches.push(()=>{m.symbolSize=xt(Gs(m.resourceSize,{width:o,height:l,depth:h,isPrimitive:this.symbolLayer.isPrimitive}))}),e.graphics3DGraphicPatches.push((g,_)=>{const S=this._computeLocalTransform(m,p,_,Ko),P=g.instanceIndex;m.lodRenderer.instanceData.setLocalTransform(P,S,!0)})}_preparePatchColor(e,t){if(!t.material||t.material.type!=="partial")return;const i=t.material.diff;if(!i.color||i.color.type!=="complete"||i.color.newValue==null||i.color.oldValue==null)return;const s=i.color.newValue,a=d(s)?ue.toUnitRGBA(s):ri;delete i.color;const n=this._resources;b(n)||e.graphics3DGraphicPatches.push(o=>{let l;this._hasPerInstanceColor()?(n.lodRenderer.instanceData.setColor(o.instanceIndex,a),l=this._setMaterialTransparencyParams({},s)):l=this._setMaterialTransparencyParams({externalColor:a},s);for(const h of n.stageResources.materials)h.setParameters(l)})}_requiresTerrainElevation(e){return e.mode!=="absolute-height"}_applyObjectRotation(e,t,i){if(!(this._fastUpdates.enabled&&this._fastUpdates.requiresShaderTransformation&&t))return Lu(e.heading,e.tilt,e.roll,i)}_computeAnchor(e,t,i){const s=C();switch(i.anchor){case"center":de(s,dt(e)),Qi(s,s);break;case"top":{const a=dt(e);D(s,-a[0],-a[1],-e[5]);break}case"bottom":{const a=dt(e);D(s,-a[0],-a[1],-e[2]);break}case"relative":{const a=dt(e),n=$r(e),o=i.anchorPosition,l=o?Ft(o.x,o.y,o.z):Li;Id(s,n,l),me(s,s,a),Qi(s,s);break}default:d(t)?Qi(s,t):de(s,Li)}return s}_applyAnchor(e,t,i){if(this._fastUpdates.enabled&&this._fastUpdates.requiresShaderTransformation)return;const s=this._computeAnchor(e.resourceBoundingBox,e.pivotOffset,t);s&&Wa(i,i,s)}_hasPerInstanceColor(){return this._drivenProperties.color||this._drivenProperties.opacity}_fastVisualVariableConvertOptions(e,t,i,s){const a=d(e)?xt($r(e)):si,n=d(e)?this._computeAnchor(e,s,this.symbolLayer):Li,o=this._context.renderCoordsHelper.unitInMeters,l=qn(d(t)?t:void 0,t,i,o),h=Ft(this.symbolLayer.tilt||0,this.symbolLayer.roll||0,this.symbolLayer.heading||0);return{modelSize:a,symbolSize:d(t)?t:si,unitInMeters:o,transformation:{anchor:n,scale:l,rotation:h}}}}const wi=C(),Ko=j(),ea=j(),kr=ms(),Hr=new Rr;function yf(r,e){return r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r}function _f(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=1,r}function Ta(r,e,t,i,s){return r[0]=e,r[1]=t,r[2]=i,r[3]=s,r}function bf(r,e){if(r===e){const t=e[1];r[1]=e[2],r[2]=t}else r[0]=e[0],r[1]=e[2],r[2]=e[1],r[3]=e[3];return r}function vf(r,e){const t=e[0],i=e[1],s=e[2],a=e[3];let n=t*a-s*i;return n?(n=1/n,r[0]=a*n,r[1]=-i*n,r[2]=-s*n,r[3]=t*n,r):null}function Sf(r,e){const t=e[0];return r[0]=e[3],r[1]=-e[1],r[2]=-e[2],r[3]=t,r}function xf(r){return r[0]*r[3]-r[2]*r[1]}function Zh(r,e,t){const i=e[0],s=e[1],a=e[2],n=e[3],o=t[0],l=t[1],h=t[2],c=t[3];return r[0]=i*o+a*l,r[1]=s*o+n*l,r[2]=i*h+a*c,r[3]=s*h+n*c,r}function Cf(r,e,t){const i=e[0],s=e[1],a=e[2],n=e[3],o=Math.sin(t),l=Math.cos(t);return r[0]=i*l+a*o,r[1]=s*l+n*o,r[2]=i*-o+a*l,r[3]=s*-o+n*l,r}function Pf(r,e,t){const i=e[0],s=e[1],a=e[2],n=e[3],o=t[0],l=t[1];return r[0]=i*o,r[1]=s*o,r[2]=a*l,r[3]=n*l,r}function wf(r,e){const t=Math.sin(e),i=Math.cos(e);return r[0]=i,r[1]=t,r[2]=-t,r[3]=i,r}function Of(r,e){return r[0]=e[0],r[1]=0,r[2]=0,r[3]=e[1],r}function Ef(r){return"mat2("+r[0]+", "+r[1]+", "+r[2]+", "+r[3]+")"}function Rf(r){return Math.sqrt(r[0]**2+r[1]**2+r[2]**2+r[3]**2)}function Af(r,e,t,i){return r[2]=i[2]/i[0],t[0]=i[0],t[1]=i[1],t[3]=i[3]-r[2]*t[1],[r,e,t]}function If(r,e,t){return r[0]=e[0]+t[0],r[1]=e[1]+t[1],r[2]=e[2]+t[2],r[3]=e[3]+t[3],r}function Xh(r,e,t){return r[0]=e[0]-t[0],r[1]=e[1]-t[1],r[2]=e[2]-t[2],r[3]=e[3]-t[3],r}function Tf(r,e){return r[0]===e[0]&&r[1]===e[1]&&r[2]===e[2]&&r[3]===e[3]}function Lf(r,e){const t=r[0],i=r[1],s=r[2],a=r[3],n=e[0],o=e[1],l=e[2],h=e[3],c=Td();return Math.abs(t-n)<=c*Math.max(1,Math.abs(t),Math.abs(n))&&Math.abs(i-o)<=c*Math.max(1,Math.abs(i),Math.abs(o))&&Math.abs(s-l)<=c*Math.max(1,Math.abs(s),Math.abs(l))&&Math.abs(a-h)<=c*Math.max(1,Math.abs(a),Math.abs(h))}function Df(r,e,t){return r[0]=e[0]*t,r[1]=e[1]*t,r[2]=e[2]*t,r[3]=e[3]*t,r}function $f(r,e,t,i){return r[0]=e[0]+t[0]*i,r[1]=e[1]+t[1]*i,r[2]=e[2]+t[2]*i,r[3]=e[3]+t[3]*i,r}const Gf=Zh,Mf=Xh;Object.freeze(Object.defineProperty({__proto__:null,LDU:Af,add:If,adjoint:Sf,copy:yf,determinant:xf,equals:Lf,exactEquals:Tf,frob:Rf,fromRotation:wf,fromScaling:Of,identity:_f,invert:vf,mul:Gf,multiply:Zh,multiplyScalar:Df,multiplyScalarAndAdd:$f,rotate:Cf,scale:Pf,set:Ta,str:Ef,sub:Mf,subtract:Xh,transpose:bf},Symbol.toStringTag,{value:"Module"}));let zf=class extends Nt{constructor(e,t,i,s,a,n,o,l){super(e,t,i,null,li.Mesh,l),this.path=s,this.geometrySR=a,this.upVectorAlignment=n,this.stencilWidth=o}};var jt;function Jh(r){return"upVectorAlignment"in r}(function(r){r[r.World=0]="World",r[r.Path=1]="Path"})(jt||(jt={}));function Qh(){return[1,0,0,1]}function Vf(r){return[r[0],r[1],r[2],r[3]]}function Uf(r,e,t,i){return[r,e,t,i]}function Ff(r,e){return new Float64Array(r,e,4)}Object.freeze(Object.defineProperty({__proto__:null,clone:Vf,create:Qh,createView:Ff,fromValues:Uf},Symbol.toStringTag,{value:"Module"}));function Kh(){return{up:C(),right:C()}}function Nf(r,e,t){ge(r.up,e.up,t),ge(r.right,e.right,t)}function Bf(r,e,t){Ee(r,k(t,e.right),k(t,e.up))}let jf=class{constructor(){this.pos=C(),this.posES=C(),this.vLeft=C(),this.vRight=C(),this.vMinSiblingLength=0,this.frame=Kh(),this.rotationFrameUp=C(),this.rotationRight=be(),this.rotationAngle=0,this.miterStretch=Qh(),this.maxStretchDistance=0}setFrameFromUpVector(e){de(this.frame.up,e),me(Ii,this.vLeft,this.vRight),ne(Ii,Ii),ae(La,this.frame.up,k(Ii,this.frame.up)),oe(qr,Ii,La),ne(qr,qr),Et(this.frame.right,qr,this.frame.up)}},ec=class{constructor(){this.vertices=[],this.vertexIndices=[],this.vertexNormals=[],this.poles=[],this.poleIndices=[]}addVertex(e,t){return this.vertices.push(Gr(e)),this.vertexNormals.push(Gr(t)),this.vertices.length-1}addPole(e,t=null){return this.poles.push({position:Gr(e),normal:t?Gr(t):null}),this.poles.length-1}addSegment(e,t=null){this.vertexIndices.push(e.v0),this.vertexIndices.push(e.v1),t&&(this.poleIndices.push(t.v0),this.poleIndices.push(t.v1))}get numSegments(){return this.vertexIndices.length/2}translate(e,t){for(const i of this.vertices)i[0]+=e,i[1]+=t;for(const i of this.poles)i.position[0]+=e,i.position[1]+=t}};function kf(r=20){const t=new ec,i={v0:0,v1:0};t.addPole(V(0,0));for(let a=0;a<r;++a){const n=2*a*Math.PI/r,o=Math.cos(n),l=Math.sin(n),h=V(o*.5,l*.5),c=V(o,l);t.addVertex(h,c)}for(let a=0;a<r-1;++a){const n={v0:a,v1:a+1};t.addSegment(n,i)}const s={v0:r-1,v1:0};return t.addSegment(s,i),t}function Hf(){const t=new ec,i=V(.5*-1,.5*-1),s=V(.5*1,.5*-1),a=V(.5*1,.5*1),n=V(.5*-1,.5*1),o=V(0,-1),l=V(1,0),h=V(0,1),c=V(-1,0);return t.addPole(V(0,.5*1),h),t.addPole(V(0,.5*1)),t.addPole(V(0,.5*-1)),t.addPole(V(0,.5*-1),o),t.addVertex(i,o),t.addVertex(s,o),t.addSegment({v0:0,v1:1},{v0:3,v1:3}),t.addVertex(s,l),t.addVertex(a,l),t.addSegment({v0:2,v1:3},{v0:2,v1:1}),t.addVertex(a,h),t.addVertex(n,h),t.addSegment({v0:4,v1:5},{v0:0,v1:0}),t.addVertex(n,c),t.addVertex(i,c),t.addSegment({v0:6,v1:7},{v0:1,v1:2}),t}let Wf=class{constructor(e){this.vertices=e,this.offset=C(),this.xform=j();const t=Math.floor((e.length-1)/2);de(this.offset,this.vertices[t].pos);for(const i of this.vertices)oe(i.pos,i.pos,this.offset);Wa(this.xform,this.xform,this.offset),this.updatePathVertexInformation()}updatePathVertexInformation(){const e=this.vertices.length,t=this.vertices[0];t.index=0,t.vLeft=C(),oe(t.vRight,this.vertices[1].pos,t.pos);let i=dr(t.vRight);t.vMinSiblingLength=i,ne(t.vRight,t.vRight);let s=t;for(let a=1;a<e;++a){const n=this.vertices[a];if(n.index=a,n.vLeft=s.vRight,a<e-1){oe(n.vRight,this.vertices[a+1].pos,n.pos);const o=dr(n.vRight);n.vMinSiblingLength=Math.min(i,o),i=o,ne(n.vRight,n.vRight)}else de(n.vRight,n.vLeft),n.vMinSiblingLength=i;s=n}}};function qf(r,e){let t=null;const i=r.vertices.length,s=.99619469809,a=C(),n=C(),o=C(),l=C(),h=C(),c=C(),u=sr();let p=r.vertices[0];de(n,e),D(a,0,1,0),Yn(p.vRight,n,a,a,o,n,s),de(p.frame.up,n),de(p.frame.right,o),t=p;for(let m=1;m<i;++m){p=r.vertices[m],me(h,p.vLeft,p.vRight);let g=dr(h);g>0?(g=1/Math.sqrt(g),h[0]=h[0]*g,h[1]=h[1]*g,h[2]=h[2]*g):(h[0]=p.vRight[0],h[1]=p.vRight[1],h[2]=p.vRight[2]),me(c,t.pos,t.frame.up),Qp(p.pos,h,u),Kp(u,xa(c,p.vLeft),l)?(oe(l,l,p.pos),ne(n,l),Et(o,h,n),ne(o,o)):Yn(h,t.frame.up,t.frame.right,a,o,n,s),de(p.frame.up,n),de(p.frame.right,o),t=p}}class Yf{numProfilesPerJoin(){return 1}extrude(e,t,i){for(let s=0;s<t.vertices.length;++s)i(e.index,e.frame,t.vertices[s],t.vertexNormals[s],!1)}}class ta{constructor(e=.8*Math.PI,t=1){this.cutoffAngle=e,this.numBendSubdivisions=t}numProfilesPerJoin(){return this.numBendSubdivisions+1}extrude(e,t,i){const s=ty;if(Math.abs(e.rotationAngle)>=this.cutoffAngle)for(let a=0;a<this.numBendSubdivisions+1;++a){Ld(il,.5*-e.rotationAngle+a*e.rotationAngle/this.numBendSubdivisions,e.rotationFrameUp),Nf(s,e.frame,il);for(let n=0;n<t.vertices.length;++n)as(t.vertices[n],e.rotationRight)*e.rotationAngle>=0?i(e.index,s,t.vertices[n],t.vertexNormals[n],!1):(Rn(oi,t.vertices[n],e.miterStretch),i(e.index,e.frame,oi,t.vertexNormals[n],!0))}else for(let a=0;a<this.numBendSubdivisions+1;++a)for(let n=0;n<t.vertices.length;++n){const o=as(t.vertices[n],e.rotationRight)*e.rotationAngle>=0;Rn(oi,t.vertices[n],e.miterStretch),i(e.index,e.frame,oi,t.vertexNormals[n],!o)}}}class _n{rebuildConnectingProfileGeometry(e,t,i){for(let s=0;s<t.vertices.length;++s)i(e.index,e.frame,t.vertices[s],t.vertexNormals[s],0,0)}}let el=class extends _n{constructor(){super()}getNumVertices(){return 0}getNumIndices(){return 0}rebuildCapGeometry(){}buildTopology(){}},Wr=class extends _n{constructor(e,t=0,i=!1){super(),this.profile=e,this.profilePlaneOffset=t,this.flip=i}getNumVertices(){return this.profile.vertices.length}getNumIndices(){return 3*this.profile.numSegments}rebuildConnectingProfileGeometry(e,t,i){for(let s=0;s<t.vertices.length;++s)i(e.index,e.frame,t.vertices[s],t.vertexNormals[s],this.profilePlaneOffset,0)}rebuildCapGeometry(e,t){const i=bn;Ee(i,0,0);const s=this.flip?1:-1;for(let a=0;a<this.profile.vertices.length;++a)t(e.index,e.frame,this.profile.vertices[a],i,this.profilePlaneOffset,s)}buildTopology(e,t){const i=this.vertexBufferStart+this.profile.vertexIndices[0];for(let s=1;s<this.profile.numSegments;++s){const a=this.profile.vertexIndices[2*s+0],n=this.profile.vertexIndices[2*s+1],o=this.vertexBufferStart+a,l=this.vertexBufferStart+n;this.flip?t(l,o,i):t(i,o,l)}}};class tl extends _n{constructor(e){super(),this.flip=!1,this.sign=0,this.breakNormals=!1,this.numSegments=3,this.profile=e.profile,this.flip=e.flip,this.sign=this.flip?1:-1,this.breakNormals=e.breakNormals,this.numSegments=e.subdivisions}getNumVertices(){let e=0;return e=this.profile.vertices.length*(this.numSegments-1),this.breakNormals&&(e+=this.profile.vertices.length),e+=this.profile.poles.length,e}getNumIndices(){let e=0;e+=2*this.profile.numSegments*(this.numSegments-1);for(let t=0;t<this.profile.numSegments;++t){const i=this.profile.vertexIndices[2*t+0],s=this.profile.vertexIndices[2*t+1];this.profile.poleIndices[i]===this.profile.poleIndices[s]?e+=1:e+=2}return 3*e}rebuildCapGeometry(e,t){const i=e.frame,s=.5*this.sign,a=oi,n=bn;Ee(n,0,0);for(let o=0;o<this.profile.poles.length;++o){const l=this.profile.poles[o];l.normal?t(e.index,i,l.position,l.normal,s,0):t(e.index,i,l.position,n,s,this.sign)}if(this.breakNormals)for(let o=0;o<this.profile.vertices.length;++o)t(e.index,i,this.profile.vertices[o],this.profile.vertexNormals[o],0,0);for(let o=0;o<this.numSegments-1;++o){const l=(1-(o+1)/this.numSegments)*Math.PI*.5,h=Math.sin(l),c=Math.cos(l);for(let u=0;u<this.profile.vertices.length;++u){const p=this.profile.poles[this.profile.poleIndices[u]];Dd(a,this.profile.vertices[u],p.position),zi(a,a,h),p.normal?(pa(a,a,p.position),t(e.index,i,a,p.normal,s*c,0)):(Ul(n,a),zi(n,n,h),pa(a,a,p.position),t(e.index,i,a,n,s*c,this.sign*c))}}}buildTopology(e,t){const i=this.breakNormals?this.vertexBufferStart+this.profile.poles.length:this.firstProfileVertexIndex,s=this.breakNormals?this.vertexBufferStart+this.profile.poles.length+this.profile.vertices.length:this.vertexBufferStart+this.profile.poles.length;for(let a=0;a<this.profile.numSegments;++a){const n=this.profile.vertexIndices[2*a+0],o=this.profile.vertexIndices[2*a+1],l=this.vertexBufferStart+this.profile.poleIndices[n],h=this.vertexBufferStart+this.profile.poleIndices[o];let c=i+n,u=i+o;for(let p=0;p<this.numSegments-1;++p){const m=s+p*this.profile.vertices.length+n,g=s+p*this.profile.vertices.length+o;this.flip?(t(m,u,c),t(u,m,g)):(t(c,u,m),t(g,m,u)),c=m,u=g}this.flip?(t(l,u,c),l!==h&&t(l,h,u)):(t(c,u,l),l!==h&&t(u,h,l))}}}class Zf{constructor(e,t,i,s,a,n={}){this.options=n,this._extrusionVertexCount=0,this.numExtrusionProfiles=0,this.numVerticesTotal=0,this.numNormalsTotal=0,this.profile=t,this.path=e,this.extruder=i,this.startCap=s,this.endCap=a;const o=this.path.vertices.length-2;this.numExtrusionProfiles=i.numProfilesPerJoin()*o+2,this.numVerticesTotal=t.vertices.length*this.numExtrusionProfiles,this.numNormalsTotal=this.numVerticesTotal,this.startCap.vertexBufferStart=this.numVerticesTotal;const l=this.startCap.getNumVertices();this.numVerticesTotal+=l,this.numNormalsTotal+=l,this.endCap.vertexBufferStart=this.numVerticesTotal;const h=this.endCap.getNumVertices();this.numVerticesTotal+=h,this.numNormalsTotal+=h,this.pathVertexData=qe(1*this.numVerticesTotal),this.profileRightAxisData=qe(4*this.numVerticesTotal),this.profileUpAxisData=qe(4*this.numVerticesTotal),this.profileVertexAndNormalData=qe(4*this.numVerticesTotal),this.originData=qe(3*this.path.vertices.length),this._rebuildGeometry(),this.buildTopology()}emitVertex(e,t,i,s,a){if(this.profileRightAxisData[4*this._extrusionVertexCount+0]=t.right[0],this.profileRightAxisData[4*this._extrusionVertexCount+1]=t.right[1],this.profileRightAxisData[4*this._extrusionVertexCount+2]=t.right[2],this.profileUpAxisData[4*this._extrusionVertexCount+0]=t.up[0],this.profileUpAxisData[4*this._extrusionVertexCount+1]=t.up[1],this.profileUpAxisData[4*this._extrusionVertexCount+2]=t.up[2],this.profileVertexAndNormalData[4*this._extrusionVertexCount+0]=i[0],this.profileVertexAndNormalData[4*this._extrusionVertexCount+1]=i[1],this.profileVertexAndNormalData[4*this._extrusionVertexCount+2]=s[0],this.profileVertexAndNormalData[4*this._extrusionVertexCount+3]=s[1],this.pathVertexData[this._extrusionVertexCount]=e,a){const n=this.path.vertices[e];this.profileRightAxisData[4*this._extrusionVertexCount+3]=n.rotationRight[0]*n.maxStretchDistance,this.profileUpAxisData[4*this._extrusionVertexCount+3]=n.rotationRight[1]*n.maxStretchDistance}else this.profileRightAxisData[4*this._extrusionVertexCount+3]=0,this.profileUpAxisData[4*this._extrusionVertexCount+3]=0;++this._extrusionVertexCount}emitCapVertex(e,t,i,s,a,n){this.profileRightAxisData[4*this._extrusionVertexCount+0]=t.right[0],this.profileRightAxisData[4*this._extrusionVertexCount+1]=t.right[1],this.profileRightAxisData[4*this._extrusionVertexCount+2]=t.right[2],this.profileUpAxisData[4*this._extrusionVertexCount+0]=t.up[0],this.profileUpAxisData[4*this._extrusionVertexCount+1]=t.up[1],this.profileUpAxisData[4*this._extrusionVertexCount+2]=t.up[2],this.profileVertexAndNormalData[4*this._extrusionVertexCount+0]=i[0],this.profileVertexAndNormalData[4*this._extrusionVertexCount+1]=i[1],this.profileVertexAndNormalData[4*this._extrusionVertexCount+2]=s[0],this.profileVertexAndNormalData[4*this._extrusionVertexCount+3]=s[1],this.pathVertexData[this._extrusionVertexCount]=e,this.profileRightAxisData[4*this._extrusionVertexCount+3]=a,this.profileUpAxisData[4*this._extrusionVertexCount+3]=n,++this._extrusionVertexCount}_rebuildGeometry(){const e=(i,s,a,n,o)=>this.emitVertex(i,s,a,n,o),t=(i,s,a,n,o,l)=>this.emitCapVertex(i,s,a,n,o,l);this._extrusionVertexCount=0;for(const i of this.path.vertices)this.originData[3*i.index+0]=i.pos[0],this.originData[3*i.index+1]=i.pos[1],this.originData[3*i.index+2]=i.pos[2];this.startCap.rebuildConnectingProfileGeometry(this.path.vertices[0],this.profile,t);for(let i=1;i<this.path.vertices.length-1;++i)this.extruder.extrude(this.path.vertices[i],this.profile,e);this.endCap.rebuildConnectingProfileGeometry(this.path.vertices[this.path.vertices.length-1],this.profile,t),this.startCap.rebuildCapGeometry(this.path.vertices[0],t),this.endCap.rebuildCapGeometry(this.path.vertices[this.path.vertices.length-1],t)}buildTopology(){const e=this.profile.vertices.length,t=this.profile.numSegments,i=this.numExtrusionProfiles-1;let s=3*(2*(t*i));this.startCap.indexBufferStart=s,this.startCap.firstProfileVertexIndex=0,s+=this.startCap.getNumIndices(),this.endCap.indexBufferStart=s,this.endCap.firstProfileVertexIndex=e*(this.numExtrusionProfiles-1);const a=new Array,n=new Array,o=new Array,l=(h,c,u)=>{a.push(h),a.push(c),a.push(u),n.push(h),n.push(c),n.push(u),o.push(this.pathVertexData[h]),o.push(this.pathVertexData[c]),o.push(this.pathVertexData[u])};for(let h=0;h<t;++h){const c=this.profile.vertexIndices[2*h],u=this.profile.vertexIndices[2*h+1];for(let p=0;p<i;++p){const m=p*e+c,g=(p+1)*e+u,_=p*e+u;l(m,(p+1)*e+c,g),l(m,g,_)}}this.startCap.buildTopology(this.path.vertices[0],l),this.endCap.buildTopology(this.path.vertices[this.path.vertices.length-1],l),this.vertexIndices=Ns(a),this.normalIndices=Ns(n),this.pathVertexIndices=Ns(o)}onPathChanged(){this._rebuildGeometry()}}class tc{constructor(e){this.builder=e}get xform(){return this.builder.path.xform}onPathChanged(){this.builder.onPathChanged()}}class ic extends tc{constructor(e){super(e),this.vertexAttributePosition=null,this.vertexAttributeNormal=null,this.vertexAttributeColor=null,this.vertexAttributePosition=qe(3*this.builder.numVerticesTotal),this.vertexAttributeNormal=qe(3*this.builder.numNormalsTotal),this.vertexAttributeColor=new Uint8Array(4),this.vertexAttributeColor[0]=255,this.vertexAttributeColor[1]=255,this.vertexAttributeColor[2]=255,this.vertexAttributeColor[3]=255}bakeVertexColors(e){this.vertexAttributeColor[0]=255*e[0],this.vertexAttributeColor[1]=255*e[1],this.vertexAttributeColor[2]=255*e[2],this.vertexAttributeColor[3]=255*(e.length>3?e[3]:1)}bake(e){this.size=e;for(let t=0;t<this.builder.numVerticesTotal;++t){let i=this.builder.pathVertexData[t];const s=i===0||i===this.builder.path.vertices.length-1;i*=3;const a=Jf;D(a,this.builder.originData[i++],this.builder.originData[i++],this.builder.originData[i]);const n=4*t,o=La,l=oi,h=Ii,c=Kf,u=ey;let p=0,m=0;if(D(c,this.builder.profileRightAxisData[n],this.builder.profileRightAxisData[n+1],this.builder.profileRightAxisData[n+2]),D(u,this.builder.profileUpAxisData[n],this.builder.profileUpAxisData[n+1],this.builder.profileUpAxisData[n+2]),Ee(l,this.builder.profileVertexAndNormalData[n]*e[0],this.builder.profileVertexAndNormalData[n+1]*e[1]),s)Et(h,u,c),p=this.builder.profileRightAxisData[n+3]*e[0],m=this.builder.profileUpAxisData[n+3];else{const _=bn,S=Qf;Ee(_,this.builder.profileRightAxisData[n+3],this.builder.profileUpAxisData[n+3]);const P=$d(_);Ul(_,_);const x=as(l,_);if(Math.abs(x)>P){Ee(S,-_[1],_[0]);const A=as(l,S);zi(_,_,P*Math.sign(x)),zi(S,S,A),pa(l,_,S)}D(h,0,0,0)}D(o,c[0]*l[0]+u[0]*l[1],c[1]*l[0]+u[1]*l[1],c[2]*l[0]+u[2]*l[1]),this.vertexAttributePosition[3*t+0]=a[0]+o[0]+h[0]*p,this.vertexAttributePosition[3*t+1]=a[1]+o[1]+h[1]*p,this.vertexAttributePosition[3*t+2]=a[2]+o[2]+h[2]*p;const g=oi;Ee(g,this.builder.profileVertexAndNormalData[n+2],this.builder.profileVertexAndNormalData[n+3]),this.vertexAttributeNormal[3*t+0]=c[0]*g[0]+u[0]*g[1]+h[0]*m,this.vertexAttributeNormal[3*t+1]=c[1]*g[0]+u[1]*g[1]+h[1]*m,this.vertexAttributeNormal[3*t+2]=c[2]*g[0]+u[2]*g[1]+h[2]*m}}createGeometryData(){const e=[[f.POSITION,this.builder.vertexIndices],[f.NORMAL,this.builder.normalIndices]],t=[[f.POSITION,new N(this.vertexAttributePosition,3,!0)],[f.NORMAL,new N(this.vertexAttributeNormal,3,!0)]];if(this.vertexAttributeColor){const i=this.builder.vertexIndices.length;e.push([f.COLOR,new Array(i).fill(0)]),t.push([f.COLOR,new N(this.vertexAttributeColor,4)])}return{vertexAttributes:t,indices:e}}onPathChanged(){super.onPathChanged(),this.bake(this.size)}intersect(e,t,i){const s=this.builder.vertexIndices,a=new N(this.vertexAttributePosition,3),n=s.length/3;np(e,t,0,n,s,a,void 0,void 0,i)}}class Xf extends tc{constructor(e,t,i,s){super(e),this.sizeAttributeValue=t,this.colorAttributeValue=i,this.opacityAttributeValue=s,this.vvData=null,this.baked=new ic(e),this.vvData=qe(4*this.builder.path.vertices.length);for(let a=0;a<this.builder.path.vertices.length;++a){this.vvData[4*a+0]=t,this.vvData[4*a+1]=i,this.vvData[4*a+2]=s;const n=a===0||a===this.builder.path.vertices.length-1;this.vvData[4*a+3]=n?1:0}}createGeometryData(){return{vertexAttributes:[[f.POSITION,new N(this.builder.originData,3,!0)],[f.PROFILERIGHT,new N(this.builder.profileRightAxisData,4,!0)],[f.PROFILEUP,new N(this.builder.profileUpAxisData,4,!0)],[f.PROFILEVERTEXANDNORMAL,new N(this.builder.profileVertexAndNormalData,4,!0)],[f.FEATUREVALUE,new N(this.vvData,4,!0)]],indices:[[f.POSITION,this.builder.pathVertexIndices],[f.PROFILERIGHT,this.builder.vertexIndices],[f.PROFILEUP,this.builder.vertexIndices],[f.PROFILEVERTEXANDNORMAL,this.builder.vertexIndices],[f.FEATUREVALUE,this.builder.pathVertexIndices]]}}}const Jf=C(),oi=be(),bn=be(),Qf=be(),La=C(),Ii=C(),Kf=C(),ey=C(),qr=C(),ty=Kh(),il=j(),ia=8;function iy(r,e){const t=f.FEATUREVALUE;r.attributes.add(t,"vec4");const i=r.vertex;i.code.add(w`
  bool isCapVertex() {
    return ${t}.w == 1.0;
  }
  `),i.uniforms.add(new tr("size",s=>s.size)),e.vvSize?(i.uniforms.add(new ai("vvSizeMinSize",s=>s.vvSizeMinSize)),i.uniforms.add(new ai("vvSizeMaxSize",s=>s.vvSizeMaxSize)),i.uniforms.add(new ai("vvSizeOffset",s=>s.vvSizeOffset)),i.uniforms.add(new ai("vvSizeFactor",s=>s.vvSizeFactor)),i.code.add(w`
    vec2 getSize() {
      return size * clamp(vvSizeOffset + ${t}.x * vvSizeFactor, vvSizeMinSize, vvSizeMaxSize).xz;
    }
    `)):i.code.add(w`vec2 getSize(){
return size;
}`),e.vvOpacity?(i.constants.add("vvOpacityNumber","int",ia),i.uniforms.add([new Us("vvOpacityValues",s=>s.vvOpacityValues,ia),new Us("vvOpacityOpacities",s=>s.vvOpacityOpacities,ia)]),i.code.add(w`
    vec4 applyOpacity(vec4 color) {
      float value = ${t}.z;
      if (value <= vvOpacityValues[0]) {
        return vec4( color.xyz, vvOpacityOpacities[0]);
      }

      for (int i = 1; i < vvOpacityNumber; ++i) {
        if (vvOpacityValues[i] >= value) {
          float f = (value - vvOpacityValues[i-1]) / (vvOpacityValues[i] - vvOpacityValues[i-1]);
          return vec4( color.xyz, mix(vvOpacityOpacities[i-1], vvOpacityOpacities[i], f));
        }
      }

      return vec4( color.xyz, vvOpacityOpacities[vvOpacityNumber - 1]);
    }
    `)):i.code.add(w`vec4 applyOpacity(vec4 color){
return color;
}`),e.vvColor?(i.constants.add("vvColorNumber","int",Fs),i.uniforms.add([new Us("vvColorValues",s=>s.vvColorValues,Fs),new op("vvColorColors",s=>s.vvColorColors,Fs)]),i.code.add(w`
    vec4 getColor() {
      float value = ${t}.y;
      if (value <= vvColorValues[0]) {
        return applyOpacity(vvColorColors[0]);
      }

      for (int i = 1; i < vvColorNumber; ++i) {
        if (vvColorValues[i] >= value) {
          float f = (value - vvColorValues[i-1]) / (vvColorValues[i] - vvColorValues[i-1]);
          return applyOpacity(mix(vvColorColors[i-1], vvColorColors[i], f));
        }
      }

      return applyOpacity(vvColorColors[vvColorNumber - 1]);
    }
    `)):i.code.add(w`vec4 getColor(){
return applyOpacity(vec4(1, 1, 1, 1));
}`),r.include(lp),r.attributes.add(f.PROFILERIGHT,"vec4"),r.attributes.add(f.PROFILEUP,"vec4"),r.attributes.add(f.PROFILEVERTEXANDNORMAL,"vec4"),i.code.add(w`vec3 calculateVPos() {
vec2 size = getSize();
vec3 origin = position;
vec3 right = profileRight.xyz;
vec3 up = profileUp.xyz;
vec3 forward = cross(up, right);
vec2 profileVertex = profileVertexAndNormal.xy * size;
vec2 profileNormal = profileVertexAndNormal.zw;
float positionOffsetAlongProfilePlaneNormal = 0.0;
float normalOffsetAlongProfilePlaneNormal = 0.0;`),i.code.add(w`if(!isCapVertex()) {
vec2 rotationRight = vec2(profileRight.w, profileUp.w);
float maxDistance = length(rotationRight);`),i.code.add(w`rotationRight = maxDistance > 0.0 ? normalize(rotationRight) : vec2(0, 0);
float rx = dot(profileVertex, rotationRight);
if (abs(rx) > maxDistance) {
vec2 rotationUp = vec2(-rotationRight.y, rotationRight.x);
float ry = dot(profileVertex, rotationUp);
profileVertex = rotationRight * maxDistance * sign(rx) + rotationUp * ry;
}
}else{
positionOffsetAlongProfilePlaneNormal = profileRight.w * size[0];
normalOffsetAlongProfilePlaneNormal = profileUp.w;
}
vec3 offset = right * profileVertex.x + up * profileVertex.y + forward * positionOffsetAlongProfilePlaneNormal;
return origin + offset;
}`),i.code.add(w`vec3 localNormal() {
vec3 right = profileRight.xyz;
vec3 up = profileUp.xyz;
vec3 forward = cross(up, right);
vec2 profileNormal = profileVertexAndNormal.zw;
vec3 normal = right * profileNormal.x + up * profileNormal.y;
if(isCapVertex()) {
normal += forward * profileUp.w;
}
return normal;
}`)}let ry=class extends sh{constructor(){super(...arguments),this.size=V(1,1)}};function rc(r){const e=new _s,{vertex:t,fragment:i}=e;switch(tn(t,r),e.varyings.add("vpos","vec3"),e.include(iy,r),r.output!==O.Color&&r.output!==O.Alpha||(e.include(Zi,r),e.include(to,r),e.include(hp,r),e.varyings.add("vnormal","vec3"),e.varyings.add("vcolor","vec4"),r.hasMultipassTerrain&&e.varyings.add("depth","float"),t.code.add(w`
      void main() {
        vpos = calculateVPos();
        vnormal = normalize(localNormal());

        ${r.hasMultipassTerrain?"depth = (view * vec4(vpos, 1.0)).z;":""}
        gl_Position = transformPosition(proj, view, vpos);

        ${r.output===O.Color?"forwardLinearDepth();":""}

        vcolor = getColor();
      }
    `)),e.include(rn,r),r.output){case O.Alpha:e.include(Mt,r),i.uniforms.add(new Pt("opacity",s=>s.opacity)),i.code.add(w`
      void main() {
        discardBySlice(vpos);
        ${r.hasMultipassTerrain?"terrainDepthTest(gl_FragCoord, depth);":""}
        float combinedOpacity = vcolor.a * opacity;
        gl_FragColor = vec4(combinedOpacity);
      }
    `);break;case O.Color:e.include(Mt,r),e.include(Lp,r),e.include(Dp,r),e.include(to,r),e.include($p,r),oh(i,r),Gp(i),Mp(i),i.uniforms.add([t.uniforms.get("localOrigin"),new ai("ambient",s=>s.ambient),new ai("diffuse",s=>s.diffuse),new ai("specular",s=>s.specular),new Pt("opacity",s=>s.opacity)]),i.include(sn),cp(i),i.code.add(w`
        void main() {
          discardBySlice(vpos);
          ${r.hasMultipassTerrain?"terrainDepthTest(gl_FragCoord, depth);":""}

          shadingParams.viewDirection = normalize(vpos - cameraPosition);
          shadingParams.normalView = vnormal;
          vec3 normal = shadingNormal(shadingParams);
          float ssao = evaluateAmbientOcclusionInverse();

          float additionalAmbientScale = additionalDirectedAmbientLight(vpos + localOrigin);
          vec3 additionalLight = ssao * mainLightIntensity * additionalAmbientScale * ambientBoostFactor * lightingGlobalFactor;
          ${r.receiveShadows?"float shadow = readShadowMap(vpos, linearDepth);":r.spherical?"float shadow = lightingGlobalFactor * (1.0 - additionalAmbientScale);":"float shadow = 0.0;"}
          vec3 albedo = vcolor.rgb * max(ambient, diffuse); // combine the old material parameters into a single one
          float combinedOpacity = vcolor.a * opacity;
          albedo += 0.25 * specular; // don't completely ignore specular for now

          vec3 shadedColor = evaluateSceneLighting(normal, albedo, shadow, 1.0 - ssao, additionalLight);
          gl_FragColor = vec4(shadedColor, combinedOpacity);
          gl_FragColor = highlightSlice(gl_FragColor, vpos);
          ${r.transparencyPassType===Ze.Color?"gl_FragColor = premultiplyAlpha(gl_FragColor);":""}
        }
      `);break;case O.Depth:case O.Shadow:case O.ShadowHighlight:case O.ShadowExcludeHighlight:e.include(Zi,r),nh(e),e.varyings.add("depth","float"),t.code.add(w`void main() {
vpos = calculateVPos();
gl_Position = transformPositionWithDepth(proj, view, vpos, nearFar, depth);
}`),e.include(Mt,r),e.include(en,r),i.code.add(w`void main() {
discardBySlice(vpos);
outputDepth(depth);
}`);break;case O.Normal:e.include(Zi,r),e.include(Zn,r),ih(t),e.varyings.add("vnormal","vec3"),t.code.add(w`void main(void) {
vpos = calculateVPos();
vnormal = normalize((viewNormal * vec4(localNormal(), 1.0)).xyz);
gl_Position = transformPosition(proj, view, vpos);
}`),e.include(Mt,r),i.code.add(w`void main() {
discardBySlice(vpos);
vec3 normal = normalize(vnormal);
if (gl_FrontFacing == false) normal = -normal;
gl_FragColor = vec4(vec3(0.5) + 0.5 * normal, 1.0);
}`);break;case O.Highlight:e.include(Zi,r),e.include(Zn,r),e.varyings.add("vnormal","vec3"),t.code.add(w`void main(void) {
vpos = calculateVPos();
gl_Position = transformPosition(proj, view, vpos);
}`),e.include(Mt,r),e.include(ah,r),i.code.add(w`void main() {
discardBySlice(vpos);
outputHighlight();
}`)}return e}const sy=Object.freeze(Object.defineProperty({__proto__:null,build:rc},Symbol.toStringTag,{value:"Module"})),sc=new Map([[f.POSITION,0],[f.PROFILERIGHT,1],[f.PROFILEUP,2],[f.PROFILEVERTEXANDNORMAL,3],[f.FEATUREVALUE,4]]);let ay=class extends ry{constructor(){super(...arguments),this.ambient=Ft(.2,.2,.2),this.diffuse=Ft(.8,.8,.8),this.specular=Ft(0,0,0),this.opacity=1}},ac=class nc extends vs{initializeConfiguration(e,t){t.hasWebGL2Context=e.rctx.type===Fl.WEBGL2,t.spherical=e.viewingMode===Ut.Global,t.doublePrecisionRequiresObfuscation=e.rctx.driverTest.doublePrecisionRequiresObfuscation.result}initializeProgram(e){return new Ss(e.rctx,nc.shader.get().build(this.configuration),sc)}initializePipeline(){const e=this.configuration.transparencyPassType,t=this.configuration,i=e===Ze.NONE,s=e===Ze.FrontFace;return zt({blending:t.output!==O.Color&&t.output!==O.Alpha||!t.transparent?null:i?rr:nn(e),culling:t.hasSlicePlane&&!t.transparent&&t.doubleSidedMode!==Mi.None?Sp:null,depthTest:{func:on(e)},depthWrite:i||s?xs:null,colorWrite:Gi,stencilWrite:t.hasOccludees?hs:null,stencilTest:t.hasOccludees?an:null,polygonOffset:i||s?null:xp})}};ac.shader=new bs(sy,()=>wr(()=>Promise.resolve().then(()=>R_),void 0));let J=class extends Xa{constructor(){super(...arguments),this.output=O.Color,this.doubleSidedMode=Mi.None,this.transparencyPassType=Ze.NONE,this.spherical=!1,this.receiveShadows=!1,this.receiveAmbientOcclusion=!1,this.vvSize=!1,this.vvColor=!1,this.vvOpacity=!1,this.hasSlicePlane=!1,this.transparent=!1,this.hasOccludees=!1,this.hasMultipassTerrain=!1,this.cullAboveGround=!1,this.doublePrecisionRequiresObfuscation=!1}};y([$({count:O.COUNT})],J.prototype,"output",void 0),y([$({count:Mi.COUNT})],J.prototype,"doubleSidedMode",void 0),y([$({count:Ze.COUNT})],J.prototype,"transparencyPassType",void 0),y([$()],J.prototype,"spherical",void 0),y([$()],J.prototype,"receiveShadows",void 0),y([$()],J.prototype,"receiveAmbientOcclusion",void 0),y([$()],J.prototype,"vvSize",void 0),y([$()],J.prototype,"vvColor",void 0),y([$()],J.prototype,"vvOpacity",void 0),y([$()],J.prototype,"hasSlicePlane",void 0),y([$()],J.prototype,"transparent",void 0),y([$()],J.prototype,"hasOccludees",void 0),y([$()],J.prototype,"hasMultipassTerrain",void 0),y([$()],J.prototype,"cullAboveGround",void 0),y([$()],J.prototype,"doublePrecisionRequiresObfuscation",void 0),y([$({constValue:dp.Disabled})],J.prototype,"pbrMode",void 0),y([$({constValue:!0})],J.prototype,"hasVvInstancing",void 0),y([$({constValue:!1})],J.prototype,"useCustomDTRExponentForWater",void 0),y([$({constValue:!1})],J.prototype,"useFillLights",void 0);let ny=class oc extends Ja{constructor(e){super(e,new ly),this.supportsEdges=!0,this._vertexAttributeLocations=sc,this._configuration=new J,this._vertexBufferLayout=oc.getVertexBufferLayout()}getConfiguration(e,t){return this._configuration.output=e,this._configuration.vvSize=this.parameters.vvSizeEnabled,this._configuration.vvColor=this.parameters.vvColorEnabled,this._configuration.vvOpacity=this.parameters.vvOpacityEnabled,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.transparent=this.parameters.transparent,this._configuration.hasOccludees=this.parameters.hasOccludees,e!==O.Color&&e!==O.Alpha||(this._configuration.doubleSidedMode=this.parameters.doubleSided&&this.parameters.doubleSidedType==="normal"?Mi.View:this.parameters.doubleSided&&this.parameters.doubleSidedType==="winding-order"?Mi.WindingOrder:Mi.None,this._configuration.receiveShadows=this.parameters.receiveShadows,this._configuration.receiveAmbientOcclusion=!!t.ssaoHelper.active&&this.parameters.receiveSSAO),this._configuration.transparencyPassType=t.transparencyPassType,this._configuration.hasMultipassTerrain=t.multipassTerrain.enabled,this._configuration.cullAboveGround=t.multipassTerrain.cullAboveGround,this._configuration}isVisibleForOutput(e){return e!==O.Shadow&&e!==O.ShadowExcludeHighlight&&e!==O.ShadowHighlight||this.parameters.castShadows}isVisible(){return super.isVisible()&&this.parameters.opacity>0}intersect(e,t,i,s,a,n){const o=e;if(!Jh(o))return;const l=o.path,h=[this.parameters.size[0],this.parameters.size[1]];if(this.parameters.vvSizeEnabled){const S=this.parameters.vvSizeOffset,P=this.parameters.vvSizeFactor,x=this.parameters.vvSizeMinSize,A=this.parameters.vvSizeMaxSize,E=l.sizeAttributeValue;h[0]*=hr(S[0]+E*P[0],x[0],A[0]),h[1]*=hr(S[2]+E*P[2],x[2],A[2])}const c=Math.max(h[0],h[1]),u=e.boundingInfo;if(b(u))return void this._intersectTriangles(l,h,s,a,n);const p=Gd(u.bbMin[0]-c,u.bbMin[1]-c,u.bbMin[2]-c,u.bbMax[0]+c,u.bbMax[1]+c,u.bbMax[2]+c),m=[a[0]-s[0],a[1]-s[1],a[2]-s[2]],g=Math.sqrt(m[0]*m[0]+m[1]*m[1]+m[2]*m[2]),_=[g/m[0],g/m[1],g/m[2]];up(p,s,_,i.tolerance)&&this._intersectTriangles(l,h,s,a,n)}_intersectTriangles(e,t,i,s,a){e.baked.size&&e.baked.size[0]===t[0]&&e.baked.size[1]===t[1]||e.baked.bake(t),e.baked.intersect(i,s,a)}createBufferWriter(){return new lh(this._vertexBufferLayout)}requiresSlot(e,t){switch(t){case O.Shadow:case O.ShadowHighlight:case O.ShadowExcludeHighlight:if(!this.parameters.castShadows)return!1;case O.Color:case O.Alpha:case O.Depth:case O.Normal:case O.Highlight:case O.ObjectAndLayerIdColor:return e===(this.parameters.transparent?Y.TRANSPARENT_MATERIAL:Y.OPAQUE_MATERIAL)||e===Y.DRAPED_MATERIAL;default:return!1}}createGLMaterial(e){return new oy(e)}static getVertexBufferLayout(){return Fi().vec3f(f.POSITION).vec4f(f.PROFILERIGHT).vec4f(f.PROFILEUP).vec4f(f.PROFILEVERTEXANDNORMAL).vec4f(f.FEATUREVALUE)}},oy=class extends Qa{_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&this._material.setParameters({hasOccludees:e.hasOccludees})}_updateShadowState(e){(b(this.technique)||e.shadowMap.enabled!==this.technique.configuration.receiveShadows)&&this._material.setParameters({receiveShadows:e.shadowMap.enabled})}beginSlot(e){return this._output!==O.Color&&this._output!==O.Alpha||(this._updateShadowState(e),this._updateOccludeeState(e)),this.ensureTechnique(ac,e)}},ly=class extends ay{constructor(){super(...arguments),this.doubleSided=!1,this.doubleSidedType="normal",this.receiveSSAO=!0,this.receiveShadows=!1,this.castShadows=!0,this.hasSlicePlane=!1,this.transparent=!1,this.hasOccludees=!1}};const hy=["polyline"];class cy extends mt{constructor(e,t,i,s){super(e,t,i,s),this._intrinsicSize=V(1,1),this._upVectorAlignment=jt.Path,this._stencilWidth=.1,this.ensureDrapedStatus(!1)}async doLoad(){const e=d(this.symbolLayer.width)?this.symbolLayer.width:this.symbolLayer.height,t=d(this.symbolLayer.height)?this.symbolLayer.height:e;this._vvConvertOptions={modelSize:[1,1,1],symbolSize:[e,1,t],unitInMeters:this._context.renderCoordsHelper.unitInMeters,transformation:{anchor:[0,0,0],scale:[1,1,1],rotation:[0,0,0]},supportedTypes:{size:!0,color:!0,opacity:!0,rotation:!1}},this._context.renderer&&this._context.renderer.visualVariables&&this._context.renderer.visualVariables.length>0?this._fastUpdates=xr(this._context.renderer,this._vvConvertOptions):this._fastUpdates={enabled:!1};const i=this.symbolLayer.anchor||"center";this._upVectorAlignment=this.symbolLayer.profileRotation==="heading"?jt.World:jt.Path;const s=this.symbolLayer.profile||"circle";switch(s){default:case"circle":this._profile=kf(Ch);break;case"quad":this._profile=Hf()}let a=[0,0];switch(i!=="center"&&(a={left:[.5,0],right:[-.5,0],top:[0,-.5],bottom:[0,.5]}[i],this._profile.translate(a[0],a[1])),this.symbolLayer.join){case"round":this._extruder=new ta(0,xh);break;case"bevel":this._extruder=new ta(0,1);break;case"miter":this._extruder=new ta(.8*Math.PI,1);break;default:this._extruder=new Yf}const n=this.symbolLayer.cap||"butt";switch(n){case"none":this._startCap=new el,this._endCap=new el;break;case"butt":default:this._startCap=new Wr(this._profile,0),this._endCap=new Wr(this._profile,0,!0);break;case"square":this._startCap=new Wr(this._profile,-.5),this._endCap=new Wr(this._profile,.5,!0);break;case"round":{const m=s==="quad";this._startCap=new tl({profile:this._profile,flip:!1,breakNormals:m,subdivisions:Oa}),this._endCap=new tl({profile:this._profile,flip:!0,breakNormals:m,subdivisions:Oa});break}}const o=ie(this.symbolLayer,"material","color"),l=this._getCombinedOpacityAndColor(o),h=xt(l),c=l[3],u=c<1||this.needsDrivenTransparentPass,p={diffuse:h,ambient:h,opacity:c,transparent:u,hasVertexColors:!1,hasSlicePlane:this._context.slicePlaneEnabled,castShadows:this.symbolLayer.castShadows,cullFace:u||n==="none"?Ae.None:Ae.Back,offsetTransparentBackfaces:!0};if(!this._drivenProperties.size&&(Ee(this._intrinsicSize,e,t),!Xn(this._intrinsicSize[0])||!Xn(this._intrinsicSize[1])))throw new We("graphics3dpathsymbollayer:invalid-size","Symbol sizes may not be negative values");if(this._fastUpdates.enabled&&this._fastUpdates.visualVariables.size||zi(this._intrinsicSize,this._intrinsicSize,1/this._context.renderCoordsHelper.unitInMeters),this._fastUpdates.enabled){const m={...p,...this._fastUpdates.materialParameters,size:Md(this._intrinsicSize)};this._material=new ny(m)}else p.hasVertexColors=this._drivenProperties.color||this._drivenProperties.opacity,this._material=new yr(p);this._material.setParameters({usePBR:this._context.physicalBasedRenderingEnabled,isSchematic:!0}),this._context.stage.add(this._material)}destroy(){super.destroy(),this._context.stage.remove(this._material),this._material=null}createGraphics3DGraphic(e){const t=e.graphic;if(!this._validateGeometry(t.geometry,hy,this.symbolLayer.type))return null;const i=this.setGraphicElevationContext(t,new Je),s=e.renderingInfo;return this._createAs3DShape(t,s,i,t.uid)}layerOpacityChanged(){const e=ie(this.symbolLayer,"material","color"),t=this._getCombinedOpacity(e),i=t<1||this.needsDrivenTransparentPass;this._material.setParameters({opacity:t,transparent:i})}layerElevationInfoChanged(e,t){return this.updateGraphics3DGraphicElevationInfo(e,t,kt)}slicePlaneEnabledChanged(){return this._material.setParameters({hasSlicePlane:this._context.slicePlaneEnabled}),!0}physicalBasedRenderingChanged(){return this._material.setParameters({usePBR:this._context.physicalBasedRenderingEnabled,isSchematic:!0}),!0}pixelRatioChanged(){return!0}skipHighSymbolLodsChanged(){return!0}applyRendererDiff(e,t){for(const i in e.diff){if(i!=="visualVariables"||!Cr(this._fastUpdates,t,this._vvConvertOptions))return H.Recreate_Symbol;this._material.setParameters(this._fastUpdates.materialParameters)}return H.Fast_Update}_getVertexData(e){let t=0;const i=e.paths,s=[],a=e.spatialReference,n=this._context.elevationProvider.spatialReference,o=this._context.renderCoordsHelper.spatialReference;for(const p of i)t+=p.length;const l=ci(3*t),h=ci(3*t);let c=0;for(const p of i){s.push({index:c,numVertices:p.length});for(const m of p)l[c++]=m[0],l[c++]=m[1],l[c++]=e.hasZ?m[2]:0}let u=!0;return d(n)&&!a.equals(n)&&(u=ut(l,a,0,l,n,0,t)),d(n)&&!n.equals(o)?ut(l,n,0,h,o,0,t):this._copyVertices(l,0,h,0,t),{pathVertexDataInfos:s,vertexDataES:l,vertexDataRS:h,projectionSuccess:u,terrainElevation:0}}_copyVertices(e,t,i,s,a){t*=3,s*=3;for(let n=0;n<a;++n)i[s++]=e[t++],i[s++]=e[t++],i[s++]=e[t++]}_createAs3DShape(e,t,i,s){const a=e.geometry,n=new Array,o=a.spatialReference,l=_e(),h=this._context.renderCoordsHelper;hc.spatialReference=o;const c=this._getVertexData(a);if(!c.projectionSuccess)return this.logger.warn("PathSymbol3DLayer geometry failed to be created (failed to project geometry to view spatial reference)"),null;if(c.pathVertexDataInfos.length===0)return a.paths.length!==0&&a.paths.some(g=>g.length>0)||this.logger.warn("PathSymbol3DLayer geometry failed to be created (no paths were defined)"),null;for(const g of c.pathVertexDataInfos){const _=g.index,S=g.numVertices;if(S<2||d(this._context.clippingExtent)&&(B(l),Ve(l,c.vertexDataES,3*_,S),!it(l,this._context.clippingExtent)))continue;const P=[];for(let L=_;L<_+3*S;){const M=L++,le=L++,X=L++,Ue=new jf;D(Ue.posES,c.vertexDataES[M],c.vertexDataES[le],c.vertexDataES[X]);const Wt=Du(Ue.posES,this._context.elevationProvider,i,h);D(U,c.vertexDataRS[M],c.vertexDataRS[le],c.vertexDataRS[X]),h.setAltitude(U,Wt),de(Ue.pos,U),P.push(Ue)}const x=new Wf(P);lc(x,this._upVectorAlignment,this._context.renderCoordsHelper);const A=new Zf(x,this._profile,this._extruder,this._startCap,this._endCap);let E=null;if(this._fastUpdates.enabled){const L=this._fastUpdates.visualVariables,M=L.size?Ot(L.size.field,e):0,le=L.color?Ot(L.color.field,e):0,X=L.opacity?Ot(L.opacity.field,e):0;E=new Xf(A,M,le,X)}else{const L=[this._intrinsicSize[0],this._intrinsicSize[1]];if(this._drivenProperties.size){const X=t.size;L[0]*=rl(X[0],X[2]==="symbol-value"?this.symbolLayer.height||0:X[2],this.symbolLayer.width||0),L[1]*=rl(X[2],X[0]==="symbol-value"?this.symbolLayer.width||0:X[0],this.symbolLayer.height||0)}let M;this._drivenProperties.color&&(M=t.color),this._drivenProperties.opacity&&t.opacity!=null&&(M=M?[M[0],M[1],M[2],t.opacity]:[1,1,1,t.opacity]);const le=new ic(A);le.bake(L),M&&le.bakeVertexColors(M),E=le}const{vertexAttributes:T,indices:G}=E.createGeometryData(),z=this._context.stage.renderView.getObjectAndLayerIdColor({graphicUid:s,layerUid:this._context.layer.uid}),R=new zf(this._material,T,G,E,o,this._upVectorAlignment,this._stencilWidth,z);R.transformation=E.xform,n.push(R)}if(n.length===0)return null;const u={layerUid:this._context.layer.uid,graphicUid:s},p=new ui({geometries:n,metadata:u}),m=new gt(this,p,n,null,null,uy,i);return m.alignedSampledElevation=c.terrainElevation,m.needsElevationUpdates=kt(i.mode),m}}function lc(r,e,t){switch(e){default:case jt.World:for(const i of r.vertices){me(U,i.pos,r.offset),t.worldUpAtPosition(U,U),i.setFrameFromUpVector(U),i.rotationFrameUp=i.frame.up,Ee(i.rotationRight,1,0),ae(U,i.frame.up,k(i.frame.up,i.vLeft)),oe(U,i.vLeft,U),Qi(U,U),ne(U,U),ae(Lt,i.frame.up,k(i.frame.up,i.vRight)),oe(Lt,i.vRight,Lt),ne(Lt,Lt),Et(sl,i.rotationFrameUp,i.vLeft);const s=Math.sign(k(sl,i.vRight));if(i.rotationAngle=s*(Math.PI-An(k(U,Lt))),Math.abs(i.rotationAngle)>0){const n=Ms(Math.cos(.5*i.rotationAngle));Ta(i.miterStretch,n-1+1,0,0,1)}const a=Math.PI-i.rotationAngle;i.maxStretchDistance=Math.abs(i.vMinSiblingLength/Math.cos(.5*a))}break;case jt.Path:me(U,r.vertices[0].pos,r.offset),t.worldUpAtPosition(U,U),qf(r,U);for(const i of r.vertices){const s=Math.sign(k(i.frame.right,i.vRight));Et(i.rotationFrameUp,i.vRight,i.vLeft),ae(i.rotationFrameUp,i.rotationFrameUp,s),ne(i.rotationFrameUp,i.rotationFrameUp);const a=k(i.rotationFrameUp,i.frame.up),n=k(i.rotationFrameUp,i.frame.right);if(ae(U,i.frame.up,-n),ae(Lt,i.frame.right,a),me(U,U,Lt),ne(U,U),Bf(i.rotationRight,i.frame,U),Qi(U,i.vLeft),i.rotationAngle=-s*(Math.PI-An(k(U,i.vRight))),Math.abs(i.rotationAngle)>0){const l=Ms(Math.cos(.5*i.rotationAngle));Ta(i.miterStretch,1+(l-1)*i.rotationRight[0]*i.rotationRight[0],(l-1)*i.rotationRight[0]*i.rotationRight[1],(l-1)*i.rotationRight[0]*i.rotationRight[1],1+(l-1)*i.rotationRight[1]*i.rotationRight[1])}const o=Math.PI-i.rotationAngle;i.maxStretchDistance=Math.abs(i.vMinSiblingLength*Ms(Math.cos(.5*o)))}}}function rl(r,e,t){switch(r){case"symbol-value":return t;case"proportional":return e;default:return r}}function dy(r,e,t,i){let s=0;for(const a of r.vertices)t(a.posES,ra),s+=ra.sampledElevation,me(U,a.pos,r.offset),i.setAltitude(U,ra.z),oe(a.pos,U,r.offset);return r.updatePathVertexInformation(),s/r.vertices.length}function uy(r,e,t,i,s){const a=r.stageObject,n=a.geometries;let o=0;py.spatialReference=s.spatialReference;for(const l of n){if(!Jh(l))continue;const h=l.path,c=h.builder.path,u=l.geometrySR;hc.spatialReference=u,o+=dy(c,e,i,s),l.upVectorAlignment!==jt.World&&lc(c,l.upVectorAlignment,s),h.onPathChanged(),l.invalidateBoundingInfo(),a.geometryVertexAttrsUpdated(l)}return o/n.length}const py=Ui(0,0,0,null),hc=Ui(0,0,0,null),U=C(),Lt=C(),sl=C(),ra=new Rr;function gy(r,e,t,i,s=1){if(t.isGeographic&&i===Ut.Global){const h=ci(e.length),c=e.length,u=ts(t);for(let p=0;p<c;p+=3)zd(e,p,h,p,u);e=h}Ee(Z,Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY);for(let h=0;h<e.length;h+=3)Z[0]=Math.min(Z[0],e[h]),Z[1]=Math.min(Z[1],e[h+1]);const a=Z[0]%s,n=Z[1]%s,o=Z[0]-a,l=Z[1]-n;for(let h=0;h<e.length;h+=3){const c=h/3*4;r[c]=(e[h]-o)/s,r[c+1]=(e[h+1]-l)/s,r[c+2]=o/s,r[c+3]=l/s}}function cc(r,e,t,i,s=1){D(Xt,1,0,0),D(Jt,0,1,0),D(Ei,0,0,1),yy(sa,t),my(t,al)&&fy(al,Xt,Jt,Ei,i,sa),Ee(Z,Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY),Ee(Qt,Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY);for(let c=0;c<t.length;c+=3){D(Oi,t[c],t[c+1],t[c+2]);const u=k(Xt,Oi),p=k(Jt,Oi);Z[0]=Math.min(Z[0],u),Z[1]=Math.min(Z[1],p),Qt[0]=Math.max(Qt[0],u),Qt[1]=Math.max(Qt[1],p)}const a=k(Ei,sa);na(Dt,Z[0],Z[1],a,Xt,Jt,Ei),na(Kt,Qt[0],Z[1],a,Xt,Jt,Ei),na(ei,Z[0],Qt[1],a,Xt,Jt,Ei),oe(Kt,Kt,Dt),ae(Kt,Kt,.5),oe(ei,ei,Dt),ae(ei,ei,.5),me(Dt,Dt,Kt),me(Dt,Dt,ei);const n=Z[0]%s,o=Z[1]%s,l=Z[0]-n,h=Z[1]-o;for(let c=0;c<t.length;c+=3){D(Oi,t[c],t[c+1],t[c+2]);const u=c/3,p=4*u;r[p]=(k(Xt,Oi)-l)/s,r[p+1]=(k(Jt,Oi)-h)/s,r[p+2]=l/s,r[p+3]=h/s;const m=9*u;for(let g=0;g<3;g++)e[m+g]=Dt[g],e[m+g+3]=Kt[g],e[m+g+6]=ei[g]}}const sa=C(),Oi=C(),al=sr(),Xt=C(),Jt=C(),Ei=C(),Z=be(),Qt=be(),Dt=C(),Kt=C(),ei=C();function my(r,e){const t=r.length/3-1;return eg(r,e,0,Math.floor(t/3),Math.floor(t*(2/3)))}function fy(r,e,t,i,s,a){d(s)?(s.basisMatrixAtPosition(a,nt),D(Yr,nt[0],nt[1],nt[2]),D(Zr,nt[4],nt[5],nt[6]),D(aa,nt[8],nt[9],nt[10])):(D(Yr,1,0,0),D(Zr,0,1,0),D(aa,0,0,1));const n=fh(r);k(n,aa)<0&&ae(n,n,-1),de(i,n);const o=k(n,Zr),l=k(n,Yr);Math.abs(o)<Math.abs(l)?(In(e,Yr,n,-l),ne(e,e),Et(t,e,n),ne(t,t),ae(t,t,-1)):(In(t,Zr,n,-o),ne(t,t),Et(e,t,n),ne(e,e))}const nt=j(),Yr=C(),Zr=C(),aa=C();function yy(r,e){D(Wi,0,0,0);for(let t=0;t<e.length-3;t+=3)Wi[0]+=e[t],Wi[1]+=e[t+1],Wi[2]+=e[t+2];ae(r,Wi,1/(e.length/3-1))}const Wi=C();function na(r,e,t,i,s,a,n){D(r,e*s[0]+t*a[0]+i*n[0],e*s[1]+t*a[1]+i*n[1],e*s[2]+t*a[2]+i*n[2])}var te;(function(r){r[r.Horizontal=0]="Horizontal",r[r.Vertical=1]="Vertical",r[r.Cross=2]="Cross",r[r.ForwardDiagonal=3]="ForwardDiagonal",r[r.BackwardDiagonal=4]="BackwardDiagonal",r[r.DiagonalCross=5]="DiagonalCross",r[r.COUNT=6]="COUNT"})(te||(te={}));const Da=.70710678118,nl=Da,_y=.08715574274;function dc(r){const e=new _s,t=r.hasMultipassTerrain&&(r.output===O.Color||r.output===O.Alpha);r.draped||e.extensions.add("GL_OES_standard_derivatives");const{vertex:i,fragment:s}=e;tn(i,r),e.include(Zi,r),e.include(pp,r),r.draped?i.uniforms.add(new Pt("worldToScreenRatio",(o,l)=>1/l.screenToPCSRatio)):e.attributes.add(f.BOUNDINGRECT,"mat3"),e.attributes.add(f.POSITION,"vec3"),e.attributes.add(f.UVMAPSPACE,"vec4"),e.varyings.add("vpos","vec3"),e.varyings.add("vuv","vec2"),t&&e.varyings.add("depth","float");const a=r.style===te.ForwardDiagonal||r.style===te.BackwardDiagonal||r.style===te.DiagonalCross;a&&i.code.add(w`
      const mat2 rotate45 = mat2(${w.float(Da)}, ${w.float(-nl)},
                                 ${w.float(nl)}, ${w.float(Da)});
    `),r.draped||(oh(i,r),i.uniforms.add(new Pt("worldToScreenPerDistanceRatio",(o,l)=>1/l.camera.perScreenPixelRatio)),i.code.add(w`vec3 projectPointToLineSegment(vec3 center, vec3 halfVector, vec3 point) {
float projectedLength = dot(halfVector, point - center) / dot(halfVector, halfVector);
return center + halfVector * clamp(projectedLength, -1.0, 1.0);
}`),i.code.add(w`vec3 intersectRayPlane(vec3 rayDir, vec3 rayOrigin, vec3 planeNormal, vec3 planePoint) {
float d = dot(planeNormal, planePoint);
float t = (d - dot(planeNormal, rayOrigin)) / dot(planeNormal, rayDir);
return rayOrigin + t * rayDir;
}`),i.code.add(w`
      float boundingRectDistanceToCamera() {
        vec3 center = vec3(boundingRect[0][0], boundingRect[0][1], boundingRect[0][2]);
        vec3 halfU = vec3(boundingRect[1][0], boundingRect[1][1], boundingRect[1][2]);
        vec3 halfV = vec3(boundingRect[2][0], boundingRect[2][1], boundingRect[2][2]);
        vec3 n = normalize(cross(halfU, halfV));

        vec3 viewDir = - vec3(view[0][2], view[1][2], view[2][2]);

        float viewAngle = dot(viewDir, n);
        float minViewAngle = ${w.float(_y)};

        if (abs(viewAngle) < minViewAngle) {
          // view direction is (almost) parallel to plane -> clamp it to min angle
          float normalComponent = sign(viewAngle) * minViewAngle - viewAngle;
          viewDir = normalize(viewDir + normalComponent * n);
        }

        // intersect view direction with infinite plane that contains bounding rect
        vec3 planeProjected = intersectRayPlane(viewDir, cameraPosition, n, center);

        // clip to bounds by projecting to u and v line segments individually
        vec3 uProjected = projectPointToLineSegment(center, halfU, planeProjected);
        vec3 vProjected = projectPointToLineSegment(center, halfV, planeProjected);

        // use to calculate the closest point to camera on bounding rect
        vec3 closestPoint = uProjected + vProjected - center;

        return length(closestPoint - cameraPosition);
      }
    `)),i.code.add(w`
    vec2 scaledUV() {
      vec2 uv = uvMapSpace.xy ${a?" * rotate45":""};
      vec2 uvCellOrigin = uvMapSpace.zw ${a?" * rotate45":""};

      ${r.draped?"":w`
            float distanceToCamera = boundingRectDistanceToCamera();
            float worldToScreenRatio = worldToScreenPerDistanceRatio / distanceToCamera;
          `}

      // Logarithmically discretize ratio to avoid jittering
      float step = 0.1;
      float discreteWorldToScreenRatio = log(worldToScreenRatio);
      discreteWorldToScreenRatio = ceil(discreteWorldToScreenRatio / step) * step;
      discreteWorldToScreenRatio = exp(discreteWorldToScreenRatio);

      vec2 uvOffset = mod(uvCellOrigin * discreteWorldToScreenRatio, ${w.float(r.patternSpacing)});
      return uvOffset + (uv * discreteWorldToScreenRatio);
    }
  `);const n=r.output===O.Depth;return n&&(e.include(en,r),nh(e),th(e)),i.code.add(w`
    void main(void) {
      vuv = scaledUV();
      vpos = position;
      ${t?"depth = (view * vec4(vpos, 1.0)).z;":""}
      forwardNormalizedVertexColor();
      gl_Position = ${n?w`transformPositionWithDepth(proj, view, vpos, nearFar, linearDepth);`:w`transformPosition(proj, view, vpos);`}
    }
  `),e.include(Mt,r),s.include(sn),r.draped&&s.uniforms.add(new Pt("texelSize",(o,l)=>1/l.camera.pixelRatio)),r.output===O.Highlight&&e.include(ah,r),t&&e.include(rn,r),r.output!==O.Highlight&&(s.code.add(w`
      const float lineWidth = ${w.float(r.lineWidth)};
      const float spacing = ${w.float(r.patternSpacing)};
      const float spacingINV = ${w.float(1/r.patternSpacing)};

      float coverage(float p, float txlSize) {
        p = mod(p, spacing);

        float halfTxlSize = txlSize / 2.0;

        float start = p - halfTxlSize;
        float end = p + halfTxlSize;

        float coverage = (ceil(end * spacingINV) - floor(start * spacingINV)) * lineWidth;
        coverage -= min(lineWidth, mod(start, spacing));
        coverage -= max(lineWidth - mod(end, spacing), 0.0);

        return coverage / txlSize;
      }
    `),r.draped||s.code.add(w`const int maxSamples = 5;
float sample(float p) {
vec2 dxdy = abs(vec2(dFdx(p), dFdy(p)));
float fwidth = dxdy.x + dxdy.y;
ivec2 samples = 1 + ivec2(clamp(dxdy, 0.0, float(maxSamples - 1)));
vec2 invSamples = 1.0 / vec2(samples);
float accumulator = 0.0;
for (int j = 0; j < maxSamples; j++) {
if(j >= samples.y) {
break;
}
for (int i = 0; i < maxSamples; i++) {
if(i >= samples.x) {
break;
}
vec2 step = vec2(i,j) * invSamples - 0.5;
accumulator += coverage(p + step.x * dxdy.x + step.y * dxdy.y, fwidth);
}
}
accumulator /= float(samples.x * samples.y);
return accumulator;
}`)),s.uniforms.add(new $i("uColor",o=>o.color)),s.code.add(w`
    void main() {
      discardBySlice(vpos);
      ${t?"terrainDepthTest(gl_FragCoord, depth);":""}
      vec4 color = ${r.hasVertexColors?"vColor * uColor;":"uColor;"}
      color = highlightSlice(color, vpos);

      ${r.output!==O.Highlight?w`color.a *= ${by(r)};`:""}

      ${r.output===O.ObjectAndLayerIdColor?w`color.a = 1.0;`:""}

      if (color.a < ${w.float(rh)}) {
        discard;
      }

      ${r.output===O.Alpha?w`gl_FragColor = vec4(color.a);`:""}

      ${r.output===O.Color?w`gl_FragColor = color; ${r.transparencyPassType===Ze.Color?"gl_FragColor = premultiplyAlpha(gl_FragColor);":""}`:""}
      ${r.output===O.Highlight?w`outputHighlight();`:""}
      ${r.output===O.Depth?w`outputDepth(linearDepth);`:""};
    }
  `),e}function by(r){function e(t){return r.draped?w`coverage(vuv.${t}, texelSize)`:w`sample(vuv.${t})`}switch(r.style){case te.ForwardDiagonal:case te.Horizontal:return e("y");case te.BackwardDiagonal:case te.Vertical:return e("x");case te.DiagonalCross:case te.Cross:return w`
        1.0 - (1.0 - ${e("x")}) * (1.0 - ${e("y")})
      `;default:return"0.0"}}const vy=Object.freeze(Object.defineProperty({__proto__:null,build:dc},Symbol.toStringTag,{value:"Module"}));let uc=class pc extends vs{initializeConfiguration(e,t){t.hasWebGL2Context=e.rctx.type===Fl.WEBGL2}initializeProgram(e){return new Ss(e.rctx,pc.shader.get().build(this.configuration),gc)}_setPipelineState(e,t){const i=this.configuration,s=e===Ze.NONE,a=e===Ze.FrontFace;return zt({blending:i.output===O.Color||i.output===O.Alpha?s?rr:nn(e):null,culling:Cp(i.cullFace),depthTest:{func:on(e)},depthWrite:s?i.writeDepth?xs:null:dh(e),colorWrite:Gi,stencilWrite:i.hasOccludees?hs:null,stencilTest:i.hasOccludees?t?Sa:an:null,polygonOffset:s||a?i.polygonOffset?Sy:null:Pp(i.enableOffset)})}initializePipeline(){return this._occludeePipelineState=this._setPipelineState(this.configuration.transparencyPassType,!0),this._setPipelineState(this.configuration.transparencyPassType,!1)}getPipelineState(e,t){return t?this._occludeePipelineState:super.getPipelineState(e,t)}};uc.shader=new bs(vy,()=>wr(()=>Promise.resolve().then(()=>A_),void 0));const Sy={factor:1,units:1};let fe=class extends Xa{constructor(){super(...arguments),this.output=O.Color,this.cullFace=Ae.None,this.transparencyPassType=Ze.NONE,this.hasSlicePlane=!1,this.hasVertexColors=!1,this.polygonOffset=!1,this.writeDepth=!0,this.hasOccludees=!1,this.enableOffset=!0,this.hasMultipassTerrain=!1,this.cullAboveGround=!1}};y([$({count:O.COUNT})],fe.prototype,"output",void 0),y([$({count:Ae.COUNT})],fe.prototype,"cullFace",void 0),y([$({count:te.COUNT})],fe.prototype,"style",void 0),y([$({count:Ze.COUNT})],fe.prototype,"transparencyPassType",void 0),y([$()],fe.prototype,"hasSlicePlane",void 0),y([$()],fe.prototype,"hasVertexColors",void 0),y([$()],fe.prototype,"polygonOffset",void 0),y([$()],fe.prototype,"writeDepth",void 0),y([$()],fe.prototype,"hasOccludees",void 0),y([$()],fe.prototype,"patternSpacing",void 0),y([$()],fe.prototype,"lineWidth",void 0),y([$()],fe.prototype,"enableOffset",void 0),y([$()],fe.prototype,"draped",void 0),y([$()],fe.prototype,"hasMultipassTerrain",void 0),y([$()],fe.prototype,"cullAboveGround",void 0);const gc=new Map([[f.POSITION,0],[f.COLOR,3],[f.UVMAPSPACE,4],[f.BOUNDINGRECT,5]]);let vn=class extends $u{constructor(e){super(e,new Py),this.supportsEdges=!0,this._vertexAttributeLocations=gc,this._configuration=new fe}getConfiguration(e,t){return this._configuration.output=e,this._configuration.cullFace=this.parameters.cullFace,this._configuration.hasVertexColors=this.parameters.hasVertexColors,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.polygonOffset=this.parameters.polygonOffset,this._configuration.writeDepth=this.parameters.writeDepth,this._configuration.style=this.parameters.style,this._configuration.patternSpacing=this.parameters.patternSpacing,this._configuration.lineWidth=this.parameters.lineWidth,this._configuration.draped=this.parameters.draped,this._configuration.transparencyPassType=t.transparencyPassType,this._configuration.enableOffset=t.camera.relativeElevation<wp,this._configuration.hasMultipassTerrain=t.multipassTerrain.enabled,this._configuration.cullAboveGround=t.multipassTerrain.cullAboveGround,this._configuration}requiresSlot(e,t){return t===O.Color||t===O.Alpha||t===O.Highlight||t===O.Depth&&this.parameters.writeLinearDepth?e===Y.DRAPED_MATERIAL?!0:t===O.Highlight?e===Y.OPAQUE_MATERIAL:e===(this.parameters.writeDepth?Y.TRANSPARENT_MATERIAL:Y.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL):!1}createGLMaterial(e){return new xy(e)}createBufferWriter(){const e=Fi().vec3f(f.POSITION).vec4u8(f.COLOR).vec4f(f.UVMAPSPACE);return this.parameters.draped||e.mat3f(f.BOUNDINGRECT),new Cy(e)}},xy=class extends Qa{_updateParameters(e){return this.ensureTechnique(uc,e)}_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&this._material.setParameters({hasOccludees:e.hasOccludees})}beginSlot(e){return this._output!==O.Color&&this._output!==O.Alpha||this._updateOccludeeState(e),this._updateParameters(e)}},Cy=class extends lh{write(e,t,i,s,a){for(const n of this.vertexBufferLayout.fieldNames){const o=i.vertexAttributes.get(n),l=i.indices.get(n);if(o&&l)switch(n){case f.POSITION:{ee(o.size===3);const h=s.getField(n,gr);h&&Kl(l,o.data,e,h,a);break}case f.COLOR:{ee(o.size===3||o.size===4);const h=s.getField(n,ln);h&&gp(l,o.data,o.size,h,a);break}case f.UVMAPSPACE:{ee(o.size===4);const h=s.getField(n,fr);h&&eh(l,o.data,h,a);break}case f.BOUNDINGRECT:{ee(o.size===9);const h=s.getField(n,mr);h&&this.writeBoundingRect(l,o.data,e,h,a);break}}}}writeBoundingRect(e,t,i,s,a){const n=i,o=s.typedBuffer,l=s.typedBufferStride,h=e.length;a*=l;for(let c=0;c<h;++c){const u=9*e[c],p=t[u],m=t[u+1],g=t[u+2];o[a]=n[0]*p+n[4]*m+n[8]*g+n[12],o[a+1]=n[1]*p+n[5]*m+n[9]*g+n[13],o[a+2]=n[2]*p+n[6]*m+n[10]*g+n[14];for(let _=3;_<9;++_)o[a+_]=t[u+_];a+=l}}},Py=class extends Ql{constructor(){super(...arguments),this.color=tt(1,1,1,1),this.writeDepth=!0,this.writeLinearDepth=!1,this.hasVertexColors=!1,this.polygonOffset=!1,this.hasSlicePlane=!1,this.cullFace=Ae.None,this.hasOccludees=!1,this.style=te.Cross,this.patternSpacing=10,this.lineWidth=1,this.draped=!0}};function wy(r,e,t){return Ey(Oy(r),e,t)}function Oy(r){return r&&r.pattern||null}function Ey(r,e,t){return d(r)?r.style==="none"||r.style==="solid"?(r.style==="none"&&(e.color=tt(0,0,0,0),e.transparent=!0),new Jn(e)):(e.style=Ry(r.style),e.draped=t.isDraped,new vn(e)):new Jn(e)}function Ry(r){switch(r){case"horizontal":return te.Horizontal;case"vertical":return te.Vertical;case"cross":return te.Cross;case"forward-diagonal":return te.ForwardDiagonal;case"backward-diagonal":return te.BackwardDiagonal;case"diagonal-cross":return te.DiagonalCross;default:return}}function Ay(r){return r.material instanceof vn&&!r.material.parameters.draped}function Iy(r,e){if(Ay(r)){const t=r.vertexAttributes.get(f.POSITION).data,i=r.getMutableAttribute(f.UVMAPSPACE).data,s=r.getMutableAttribute(f.BOUNDINGRECT).data;cc(i,s,t,e)}}function Ty(r,e,t,i,s){const a=_h(r,e,t,i,s),n=r.stageObject.geometries;for(const o of n)Iy(o,s);return a}const Ly=["polyline","polygon","extent"];let mc=class fc extends mt{constructor(e,t,i,s){super(e,t,i,s),this._needsUV=!1,this._hasOutline=!1}async doLoad(){}_ensureMaterials(){this._ensureFillMaterial(),this._ensureOutlineMaterial()}_ensureFillMaterial(){if(d(this._material))return;const e=ie(this.symbolLayer,"material","color"),t=this._getCombinedOpacityAndColor(e);this._material=wy(this.symbolLayer,{color:t,transparent:t[3]<1||this.needsDrivenTransparentPass,polygonOffset:!1,hasVertexColors:!0,writeLinearDepth:!0,hasSlicePlane:this._context.slicePlaneEnabled},{isDraped:this.draped}),this._needsUV=this._material instanceof vn,this._context.stage.add(this._material)}_ensureOutlineMaterial(){const e=this.symbolLayer.outline;if(d(this._outlineMaterial)||!this._isValidOutline(e))return;this._hasOutline=!0;const t=i=>{const s=Xl(e.pattern);return new Yi({width:i,color:this._getOutlineColor(),hasPolygonOffset:!0,hasSlicePlane:this._context.slicePlaneEnabled,isClosed:!0,stipplePattern:s,stippleScaleWithLineWidth:!0,cap:Ia(e.patternCap||"butt")})};this._outlineMaterial=t(Re(e.size)),this._context.stage.add(this._outlineMaterial)}_isValidOutline(e){return d(e)&&e.size!=null&&e.size>0&&d(e.color)&&(b(e.pattern)||e.pattern.type!=="style"||e.pattern.style!=="none")}destroy(){super.destroy(),this._context.stage.remove(this._material),this._material=null,this._context.stage.remove(this._outlineMaterial),this._outlineMaterial=null}createGraphics3DGraphic(e){const t=e.graphic;if(!this._validateGeometry(t.geometry,Ly,this.symbolLayer.type))return null;const i=this._getVertexOpacityAndColor(e.renderingInfo,255),s=this.setGraphicElevationContext(t,new Je);return this.ensureDrapedStatus(s.mode==="on-the-ground"),this._ensureMaterials(),this.draped?this._createAsOverlay(t,i):this._createAs3DShape(t,i,s)}layerOpacityChanged(){if(d(this._material)){const e=this._material.parameters.color,t=ie(this.symbolLayer,"material","color"),i=this._getCombinedOpacity(t);this._material.setParameters({color:[e[0],e[1],e[2],i],transparent:i<1||this.needsDrivenTransparentPass})}if(d(this._outlineMaterial)){const e=this._outlineMaterial.parameters.color;this._outlineMaterial.setParameters({color:[e[0],e[1],e[2],this._getOutlineOpacity()]})}}layerElevationInfoChanged(e,t,i){const s=this._elevationContext.mode,a=Ar(fc.elevationModeChangeTypes,i,s);if(a!==q.UPDATE)return a;const n=Ye(s);return this.updateGraphics3DGraphicElevationInfo(e,t,()=>n)}slicePlaneEnabledChanged(){if(d(this._material)&&this._material.setParameters({hasSlicePlane:this._context.slicePlaneEnabled}),d(this._outlineMaterial)){const e={hasSlicePlane:this._context.slicePlaneEnabled};this._outlineMaterial.setParameters(e)}return!0}physicalBasedRenderingChanged(){return!0}pixelRatioChanged(){return!0}skipHighSymbolLodsChanged(){return!0}_createAs3DShape(e,t,i){var c;const s=Sr(e.geometry);if(b(s))return null;const a=mn(s,this._context.elevationProvider,this._context.renderCoordsHelper,i),n=new Dy(a,t,this._context.layer.uid,e.uid),o=n.renderData.position.length/3;if(this._needsUV&&(n.uvMapSpace=qe(4*o,!0),n.boundingRect=ci(9*o,!0),cc(n.uvMapSpace,n.boundingRect,n.renderData.position,this._context.renderCoordsHelper)),n.objectAndLayerIdColor=(c=this._context.stage.renderView)==null?void 0:c.getObjectAndLayerIdColor(n),this._createAs3DShapeFill(n),this._hasOutline&&this._createAs3DShapeOutline(n),this._logGeometryCreationWarnings(n.renderData,s.rings,"rings","FillSymbol3DLayer"),n.outGeometries.length===0)return null;const l=new ui({geometries:n.outGeometries,castShadow:!1,metadata:{layerUid:this._context.layer.uid,graphicUid:e.uid}}),h=new gt(this,l,n.outGeometries,null,null,Ty,i);return h.alignedSampledElevation=n.renderData.sampledElevation,h.needsElevationUpdates=Ye(i.mode),h}_createAs3DShapeFill(e){const t=e.renderData.polygons;for(const{position:i,mapPositions:s,holeIndices:a,index:n,count:o}of t){if(d(this._context.clippingExtent)&&(B(Be),Ve(Be,s),!it(Be,this._context.clippingExtent)))continue;const l=pr(s,a,3);if(l.length===0)continue;const h=Co({material:se(this._material),indices:l,mapPositions:s,attributeData:{position:i,color:e.color,uvMapSpace:this._needsUV?Qn(e.uvMapSpace,4*n,4*o):null,boundingRect:this._needsUV?wt(e.boundingRect,9*n,9*o):null,objectAndLayerIdColor:e.objectAndLayerIdColor}});e.outGeometries.push(h)}}_createAs3DShapeOutline(e){if(!this._hasOutline)return;const t=e.renderData.outlines;for(let i=0;i<t.length;++i){const{mapPositions:s,position:a}=t[i];if(d(this._context.clippingExtent)&&(B(Be),Ve(Be,s),!it(Be,this._context.clippingExtent)))continue;const n=ba(se(this._outlineMaterial),{overlayInfo:null,removeDuplicateStartEnd:!0,mapPositions:s,attributeData:{position:a}},e.objectAndLayerIdColor),o=n.vertexAttributes.get(f.POSITION);o.data===a&&(o.data=Ap(a)),e.outGeometries.push(n)}}_createAsOverlay(e,t){var o;const i=Sr(e.geometry);if(b(i))return null;se(this._material).renderPriority=this._renderPriority+this._renderPriorityStep/2,d(this._outlineMaterial)&&(this._outlineMaterial.renderPriority=this._renderPriority);const s=Gh(i,this._context.overlaySR),a=new $y(s,t,this._context.layer.uid,e.uid),n=a.renderData.position.length/3;return this._needsUV&&(a.uvMapSpace=qe(4*n,!0),gy(a.uvMapSpace,a.renderData.position,this._context.overlaySR,this._context.graphicsCoreOwner.view.state.viewingMode)),a.outBoundingBox=B(),a.objectAndLayerIdColor=(o=this._context.stage.renderView)==null?void 0:o.getObjectAndLayerIdColor(a),this._createAsOverlayFill(a),this._hasOutline&&this._createAsOverlayOutline(a),this._logGeometryCreationWarnings(a.renderData,i.rings,"rings","FillSymbol3DLayer"),a.outGeometries.length===0?null:new Os(this,a.outGeometries,a.outBoundingBox,this._context.drapeSourceRenderer)}_createAsOverlayFill(e){const t=e.renderData.polygons;for(const{position:i,holeIndices:s,index:a,count:n}of t){const o=B(Be);if(Ve(o,i),!it(o,this._context.clippingExtent))continue;const l=pr(i,s,3);if(l.length===0)continue;lr(e.outBoundingBox,o);const h=Co({material:se(this._material),indices:l,attributeData:{position:i,color:e.color,uvMapSpace:this._needsUV?Qn(e.uvMapSpace,4*a,4*n):null,objectAndLayerIdColor:e.objectAndLayerIdColor}}),c=new ur(h,e);ss(c.boundingSphere,.5*(o[0]+o[3]),.5*(o[1]+o[4]),0,.5*Math.sqrt((o[3]-o[0])*(o[3]-o[0])+(o[4]-o[1])*(o[4]-o[1]))),e.outGeometries.push(c)}}_createAsOverlayOutline(e){if(!this._hasOutline)return;const t=e.renderData.outlines;for(let i=0;i<t.length;++i){const{position:s}=t[i];if(B(Be),Ve(Be,s),!it(Be,this._context.clippingExtent))continue;lr(e.outBoundingBox,Be);const a=ba(se(this._outlineMaterial),{overlayInfo:{spatialReference:this._context.overlaySR,renderCoordsHelper:this._context.renderCoordsHelper},removeDuplicateStartEnd:!0,attributeData:{position:s}},e.objectAndLayerIdColor),n=new ur(a,e),o=Be;ss(n.boundingSphere,.5*(o[0]+o[3]),.5*(o[1]+o[4]),0,.5*Math.sqrt((o[3]-o[0])*(o[3]-o[0])+(o[4]-o[1])*(o[4]-o[1]))),e.outGeometries.push(n)}}_getOutlineOpacity(){const e=ie(this.symbolLayer,"outline","color");return(this.draped?1:this._getLayerOpacity())*(d(e)?e.a:0)}_getOutlineColor(){const e=ie(this.symbolLayer,"outline","color"),t=this._getOutlineOpacity();return Ki(d(e)?ue.toUnitRGB(e):null,t)}test(){return{...super.test(),createAsOverlay:(e,t)=>this._createAsOverlay(e,t),createAs3DShape:(e,t,i)=>this._createAs3DShape(e,t,i)}}};mc.elevationModeChangeTypes={definedChanged:q.RECREATE,staysOnTheGround:q.NONE,onTheGroundChanged:q.RECREATE};const Be=_e();let Dy=class extends ws{constructor(e,t,i,s){super(e,i,s),this.color=t}},$y=class extends ws{constructor(e,t,i,s){super(e,i,s),this.color=t}},Gy=class{constructor(e=null,t="center",i="center",s=null,a=[0,0,0],n=0,o=[0,0,0,1],l=[0,0],h="world",c=0,u=0){this.verticalOffset=e,this.horizontalPlacement=t,this.verticalPlacement=i,this.text=s,this.translation=a,this.elevationOffset=n,this.centerOffset=o,this.screenOffset=l,this.centerOffsetUnits=h,this.displayWidth=c,this.displayHeight=u}},My=class yc{constructor(e){this.definition=e,this.key=JSON.stringify(e),this.haloSize=Math.round(e.halo.size),this.textStyle=this._colorToRGBA(e.color),this.haloStyle=this._colorToRGB(e.halo.color),this.backgroundStyle=e.background.color[3]!==0?this._colorToRGBA(e.background.color):null}fontString(e){const t=this.definition.font;return`${t.style} ${t.weight} ${e}px ${t.family}, sans-serif`}_colorToRGB(e){return`rgb(${e.slice(0,3).map(t=>Math.floor(255*t)).toString()})`}_colorToRGBA(e){return`rgba(${e.slice(0,3).map(t=>Math.floor(255*t)).toString()},${e[3]})`}static async fromSymbol(e,t){const i=ie(e,"material","color"),s=gi(i,Ji,ue.toUnitRGBA),a=gi(e.size,12,Re),n=e.lineHeight,o=d(e.background)?se(ue.toUnitRGBA(e.background.color)):Ji,l={family:gi(e.font,"sans-serif",m=>m.family),decoration:gi(e.font,"none",m=>m.decoration),weight:gi(e.font,"normal",m=>m.weight),style:gi(e.font,"normal",m=>m.style)},h=e.halo,c=d(h)&&d(h.color)&&h.size>0?{size:Re(h.size),color:ue.toUnitRGBA(h.color)}:{size:0,color:Ji},u=new yc({color:s,size:a,background:{color:o,padding:d(e.background)?[.65*a,.5*a]:[0,0],borderRadius:d(e.background)?a*(6/16):0},lineSpacingFactor:n,font:l,halo:c,pixelRatio:t}),p=u.fontString(a);try{await document.fonts.load(p)}catch{di.getLogger("esri.views.3d.webgl-engine.lib.TextRenderParameters").warnOnce(`Failed to preload font '${p}'. Some text symbology may be rendered using the default browser font.`)}return u}},zy=class{constructor(e,t,i){this._renderer=new hm(e,t,i)}get key(){return this._renderer.key}get baselineAnchorY(){return 1-this._renderer.firstRenderedBaselinePosition/this._renderer.renderedHeight}get displayWidth(){return this._renderer.displayWidth}get displayHeight(){return this._renderer.displayHeight}create(){const e=Ra(Vy,this._renderer.renderedWidth,this._renderer.renderedHeight),t=e.getContext("2d");return t.save(),this._renderer.render(t,0,0),t.restore(),new Ka(e,{wrap:{s:ir.CLAMP_TO_EDGE,t:ir.CLAMP_TO_EDGE},noUnpackFlip:!1,mipmap:!0,preMultiplyAlpha:!0,powerOfTwoResizeMode:Jl.PAD})}};const Vy={canvas:null},Uy=[0,0,1];class Fy extends mt{constructor(e,t,i,s){super(e,t,i,s),this._elevationOptions={supportsOffsetAdjustment:!0,supportsOnTheGround:!1},this.ensureDrapedStatus(!1)}async doLoad(){if(!this._drivenProperties.size){const e=ys(this.symbolLayer.size);if(e)throw new We("graphics3dtextsymbollayer:invalid-size",e)}await this._createTextRenderParameters()}async _createTextRenderParameters(){const e=this._context.graphicsCoreOwner.view.state.rasterPixelRatio;this._textRenderParameters=await My.fromSymbol(this.symbolLayer,e)}destroy(){super.destroy()}createGraphics3DGraphic(e){const t=e.graphic,i=vr(t.geometry);if(b(i))return this.logger.warn(`unsupported geometry type for text symbol: ${t.geometry.type}`),null;const s=this.symbolLayer.text;if(b(s)||s==="")return null;const a=Tl(this.symbol)&&this.symbol.hasVisibleVerticalOffset()?this.symbol.verticalOffset:null;if(d(a)&&!Il(this.symbolLayer))return this.logger.errorOncePerTick(`Callouts and vertical offset on text symbols are currently only supported with 'center' horizontal alignment (not with '${this.symbolLayer.horizontalAlignment}' alignment)`),null;const n=new Gy(a,this.symbolLayer.horizontalAlignment,ym(this.symbolLayer.verticalAlignment));return this._createAs3DShape(t,i,s,n)}createLabel(e,t,i,s){const a=e.graphic,n=vr(a.geometry);if(b(n))return this.logger.warn(`unsupported geometry type for label: ${a.geometry.type}`),null;const o=t.text;return!o||/^\s+$/.test(o)?null:this._createAs3DShape(a,n,o,t,i,s)}setGraphicElevationContext(e,t,i=0){const s=super.setGraphicElevationContext(e,t);return s.addOffsetRenderUnits(i),s}layerOpacityChanged(){return this.logger.warn("layer opacity change not yet implemented in Graphics3DTextSymbolLayer"),!1}layerElevationInfoChanged(e,t){return ol(e,t,(i,s)=>{this.updateGraphicElevationContext(s,i)}),q.UPDATE}slicePlaneEnabledChanged(e,t){return ol(e,t,i=>{for(const s of i.stageObject.geometries)s.material.setParameters({hasSlicePlane:this._context.slicePlaneEnabled})}),!0}physicalBasedRenderingChanged(){return!0}pixelRatioChanged(){return!1}skipHighSymbolLodsChanged(){return!0}updateGraphicElevationContext(e,t){const i=t.elevationContext;this.setGraphicElevationContext(e,i,d(t.metadata)?t.metadata.elevationOffset:0),t.needsElevationUpdates=Ye(i.mode)||i.mode==="absolute-height"}_defaultElevationInfoNoZ(){return By}_createAs3DShape(e,t,i,s,a,n){const o=this.setGraphicElevationContext(e,new Je,s.elevationOffset),l=ie(e.geometry,"type")==="polyline",h=e.uid;let c=null,u=null;if(b(n)){const R=mm(s.horizontalPlacement);c=new zy(i,R,this._textRenderParameters);let L=null;if(d(this._context.sharedResources.textures)){u=this._context.sharedResources.textures.fromData(c.key,()=>se(c).create(),()=>{d(L)&&L.release()});const M=this._context.stage.renderView.textureRepository.acquire(u.texture.id);if(b(M)||Oc(M))return u.release(),null;L=M}}const p=Ny(c,s),m={occlusionTest:!0,screenOffset:s.screenOffset,anchorPosition:p,polygonOffset:!0,color:[1,1,1,1],centerOffsetUnits:s.centerOffsetUnits,drawInSecondSlot:!0};if(d(n)?m.textureId=n.id:d(u)&&(m.textureId=u.texture.id),d(s.verticalOffset)){const{screenLength:R,minWorldLength:L,maxWorldLength:M}=s.verticalOffset;m.verticalOffset={screenLength:Re(R),minWorldLength:L||0,maxWorldLength:d(M)?M:1/0}}if(this._context.screenSizePerspectiveEnabled){const{screenSizePerspectiveSettings:R,screenSizePerspectiveSettingsLabels:L}=this._context.sharedResources;m.screenSizePerspective=L.overridePadding(this._textRenderParameters.haloSize+this._textRenderParameters.definition.background.padding[0]),m.screenSizePerspectiveAlignment=R}let g;if(l&&(m.shaderPolygonOffset=1e-4),m.hasSlicePlane=this._context.slicePlaneEnabled,d(a)){const R=JSON.stringify(m);g=a.get(R),b(g)&&(g=new os(m),a.add(R,g))}else g=new os(m);const _=s.translation,S=d(c)?V(c.displayWidth,c.displayHeight):Ua,P=s.centerOffset,x=_a(g,Uy,_,null,S,P,[0,0],null),A=gn(this._context,t,x,o,h);if(b(A))return null;const E=new gt(this,A.object,[x],b(a)?[g]:null,u,_r,o);E.alignedSampledElevation=A.sampledElevation,E.needsElevationUpdates=Ye(o.mode)||o.mode==="absolute-height";const{displayWidth:T,displayHeight:G}=d(c)?c:s;E.getScreenSize=(R=be())=>(R[0]=T,R[1]=G,R);const z=new Sh(s.elevationOffset,i);return E.metadata=z,br(E,t,this._context.elevationProvider),E}}function ol(r,e,t){r&&r.forEach(i=>{const s=e(i);d(s)&&t(s,i.graphic)})}function Ny(r,e){if(e.verticalPlacement==="baseline"){const i=gm[e.horizontalPlacement],s=d(r)?r.baselineAnchorY:0;return V(i,s)}const t=fm(e.horizontalPlacement,e.verticalPlacement);return es[t]}const By={mode:"relative-to-ground",offset:0},jy={"calm-small":{waveStrength:.005,perturbationStrength:.02,textureRepeat:12,waveVelocity:.01},"rippled-small":{waveStrength:.02,perturbationStrength:.09,textureRepeat:32,waveVelocity:.07},"slight-small":{waveStrength:.05,perturbationStrength:.07,textureRepeat:28,waveVelocity:.1},"moderate-small":{waveStrength:.075,perturbationStrength:.07,textureRepeat:24,waveVelocity:.1},"calm-medium":{waveStrength:.003125,perturbationStrength:.01,textureRepeat:8,waveVelocity:.02},"rippled-medium":{waveStrength:.035,perturbationStrength:.015,textureRepeat:12,waveVelocity:.07},"slight-medium":{waveStrength:.06,perturbationStrength:.015,textureRepeat:8,waveVelocity:.12},"moderate-medium":{waveStrength:.09,perturbationStrength:.03,textureRepeat:4,waveVelocity:.12},"calm-large":{waveStrength:.01,perturbationStrength:0,textureRepeat:4,waveVelocity:.05},"rippled-large":{waveStrength:.025,perturbationStrength:.01,textureRepeat:8,waveVelocity:.11},"slight-large":{waveStrength:.06,perturbationStrength:.02,textureRepeat:3,waveVelocity:.13},"moderate-large":{waveStrength:.14,perturbationStrength:.03,textureRepeat:2,waveVelocity:.15}},ky=["polyline","polygon","extent"];let $a=class ti extends mt{constructor(e,t,i,s){super(e,t,i,s)}async doLoad(){}destroy(){super.destroy(),this._context.stage.remove(this._material),this._material=null}createGraphics3DGraphic(e){const t=e.graphic;if(!this._validateGeometry(t.geometry,ky,this.symbolLayer.type))return null;const i=this.setGraphicElevationContext(t,new Je);return this.ensureDrapedStatus(i.mode==="on-the-ground"),this.ensureMaterial(),this.draped?this._createAsOverlay(t):this._createAs3DShape(t,i,t.uid)}ensureMaterial(){if(d(this._material))return;const e=new Gu,t=this.symbolLayer.color;d(t)&&(e.color=ue.toUnitRGBA(t));const i=this._getCombinedOpacity(t,{hasIntrinsicColor:!0});e.color=[e.color[0],e.color[1],e.color[2],i],e.transparent=i<1||this.needsDrivenTransparentPass,e.waveDirection=d(this.symbolLayer.waveDirection)?ti.headingVectorFromAngle(this.symbolLayer.waveDirection):V(0,0);const s=this.symbolLayer.waveStrength+"-"+this.symbolLayer.waterbodySize,a=jy[s];e.waveStrength=a.waveStrength,e.waveTextureRepeat=a.textureRepeat,e.waveVelocity=a.waveVelocity,e.flowStrength=a.perturbationStrength,e.hasSlicePlane=this._context.slicePlaneEnabled,e.isDraped=this.draped,this._material=new Mu(e),this._context.stage.add(this._material)}layerOpacityChanged(){if(b(this._material))return;const e=this._material.parameters.color,t=this._getCombinedOpacity(this.symbolLayer.color,{hasIntrinsicColor:!0}),i=t<1||this.needsDrivenTransparentPass;this._material.setParameters({color:[e[0],e[1],e[2],t],transparent:i})}layerElevationInfoChanged(e,t,i){const s=this._elevationContext.mode,a=Ar(ti.elevationModeChangeTypes,i,s);if(a!==q.UPDATE)return a;const n=Ye(s);return this.updateGraphics3DGraphicElevationInfo(e,t,()=>n)}slicePlaneEnabledChanged(){return d(this._material)&&this._material.setParameters({hasSlicePlane:this._context.slicePlaneEnabled}),!0}physicalBasedRenderingChanged(){return!0}pixelRatioChanged(){return!0}skipHighSymbolLodsChanged(){return!0}_createAs3DShape(e,t,i){const s=Sr(e.geometry);if(b(s))return null;const a=mn(s,this._context.elevationProvider,this._context.renderCoordsHelper,t),n=a.position.length/3,o=ci(2*n);this._createUVCoordsFromVertices(o,a.mapPositions,n,this._context.elevationProvider.spatialReference);const l=new Hy(a,o);if(this._create3DShapeGeometries(l),this._logGeometryCreationWarnings(l.renderData,s.rings,"rings","WaterSymbol3DLayer"),l.outGeometries.length===0)return null;const h=new ui({geometries:l.outGeometries,castShadow:!1,metadata:{layerUid:this._context.layer.uid,graphicUid:i}}),c=new gt(this,h,l.outGeometries,null,null,_h,t);return c.alignedSampledElevation=l.renderData.sampledElevation,c.needsElevationUpdates=Ye(t.mode),c}_createUVCoordsFromVertices(e,t,i,s){const a=Ec(s);Nl($t);for(let l=0;l<i;l++)Ee(ll,t[3*l],t[3*l+1]),Vd($t,ll);Ud($t,$t,a);const n=$t[0]%ti.unitSizeOfTexture,o=$t[1]%ti.unitSizeOfTexture;Xr[0]=$t[0]-n,Xr[1]=$t[1]-o;for(let l=0;l<i;l++)e[2*l]=(t[3*l]*a-Xr[0])/ti.unitSizeOfTexture,e[2*l+1]=(t[3*l+1]*a-Xr[1])/ti.unitSizeOfTexture}_create3DShapeGeometries(e){const t=e.renderData.polygons,i=e.uvCoords;for(const{count:s,index:a,position:n,mapPositions:o,holeIndices:l}of t){if(d(this._context.clippingExtent)&&(B(Gt),Ve(Gt,o),!it(Gt,this._context.clippingExtent)))continue;const h=pr(o,l,3);if(h.length===0)continue;const c=wt(i,2*a,2*s),u=Po({material:se(this._material),indices:h,mapPositions:o,attributeData:{position:n,uv0:c}});e.outGeometries.push(u)}}_createAsOverlay(e){const t=Sr(e.geometry);if(b(t))return null;se(this._material).renderPriority=this._renderPriority;const i=Gh(t,this._context.overlaySR),s=i.position.length/3,a=ci(2*s);this._createUVCoordsFromVertices(a,i.position,s,this._context.overlaySR);const n=new Wy(i,a,this._context.layer.uid,e.uid);return n.outBoundingBox=B(),this._createAsOverlayWater(n),this._logGeometryCreationWarnings(n.renderData,t.rings,"rings","WaterSymbol3DLayer"),n.outGeometries.length===0?null:new Os(this,n.outGeometries,n.outBoundingBox,this._context.drapeSourceRenderer)}_createAsOverlayWater(e){const t=e.uvCoords,i=e.renderData.polygons;for(const{position:s,holeIndices:a,index:n,count:o}of i){if(B(Gt),Ve(Gt,s),!it(Gt,this._context.clippingExtent))continue;lr(e.outBoundingBox,Gt);const l=pr(s,a,3);if(l.length===0)continue;const h=wt(t,2*n,2*o),c=Po({material:se(this._material),indices:l,attributeData:{position:s,uv0:h}}),u=new ur(c,e),p=Gt;ss(u.boundingSphere,.5*(p[0]+p[3]),.5*(p[1]+p[4]),0,.5*Math.sqrt((p[3]-p[0])*(p[3]-p[0])+(p[4]-p[1])*(p[4]-p[1]))),e.outGeometries.push(u)}}static headingVectorFromAngle(e){const t=be(),i=Fd(e);return t[0]=Math.sin(i),t[1]=Math.cos(i),t}test(){return{...super.test(),create3DShape:e=>this._createAs3DShape(e.graphic,e.elevationContext,e.graphicUid),ensureMaterial:()=>this.ensureMaterial()}}};$a.unitSizeOfTexture=100,$a.elevationModeChangeTypes={definedChanged:q.RECREATE,staysOnTheGround:q.NONE,onTheGroundChanged:q.RECREATE};const Xr=be(),$t=pt(),ll=be(),Gt=_e();class Hy extends ws{constructor(e,t){super(e,null,null),this.uvCoords=t}}class Wy extends ws{constructor(e,t,i,s){super(e,i,s),this.uvCoords=t}}function qy(r,e,t,i){const s=hl[r.type]&&hl[r.type][e.type]||Yy[e.type];return s?new s(r,e,t,i):(di.getLogger("esri.views.3d.layers.graphics.Graphics3DSymbolLayerFactory").error("GraphicsLayerFactory#make",`unknown symbol type ${e.type}`),null)}const Yy={icon:Pr,object:ff,line:Wh,path:cy,fill:mc,extrude:im,text:Fy,water:$a},hl={"mesh-3d":{fill:km}};class _c extends dn{set symbol(e){this._symbol=e,e.symbolLayers.forEach((t,i)=>{const s=this.symbolLayers[i];d(s)&&(s.symbol=e,s.symbolLayer=t)})}get symbol(){return this._symbol}constructor(e,t,i){super(t.schedule),this._symbol=e,this._context=t,this._backgroundLayers=i,this._destroyed=!1,this.symbolLayers=new Array,this.referenced=0,this._extentPadding=0}async doLoad(e){let t=this._symbol.symbolLayers;this._extentPadding=0,this._backgroundLayers&&(t=this._backgroundLayers.concat(t));const i=t.length;for(;this.symbolLayers.length<t.length;)this.symbolLayers.push(null);this.symbolLayers.length=t.length;const s=[];for(let a=0;a<i;a++){const n=t.getItemAt(a);if(n.enabled===!1)continue;Jr.renderPriority=1-(1+a)/i,Jr.renderPriorityStep=1/i,Jr.ignoreDrivers=n._ignoreDrivers;const o=qy(this.symbol,n,this._context,Jr),l=Rc(e,()=>{this.symbolLayers[a]=null,o.destroy()});l&&s.push(l),this.symbolLayers[a]=o}if(await Dl(this.symbolLayers,async(a,n)=>{if(d(a))try{await a.load(),this._extentPadding+=Math.max(this._extentPadding,a.extentPadding)}catch{this.symbolLayers[n]=null}}),s.forEach(a=>a.remove()),Oe(e),this.symbolLayers.length&&!this.symbolLayers.some(a=>!!a))throw new Error}getSymbolLayerSize(e){const t=this.symbolLayers[e];return d(t)?t.getCachedSize():null}get extentPadding(){return this._extentPadding}get symbologySnappingSupported(){return this.symbolLayers.some(e=>d(e)&&e.queryForSnapping)}createGraphics3DGraphic(e,t){const i=e.graphic,s=new Array(this.symbolLayers.length);for(let n=0;n<this.symbolLayers.length;n++){const o=this.symbolLayers[n];s[n]=d(o)?o.createGraphics3DGraphic(e):null}const a=this._context.arcade||this._context.featureExpressionInfoContext&&this._context.featureExpressionInfoContext.arcade&&this._context.featureExpressionInfoContext.arcade.modules||null;return new Xg(i,t||this,s,e.layer,a)}get complexity(){return pn(this.symbolLayers.map(e=>ie(e,"complexity")))}globalPropertyChanged(e,t){const i=this.symbolLayers.length;for(let s=0;s<i;s++){const a=this.symbolLayers[s],n=o=>{const l=o.layers[s];return l instanceof gt?l:null};if(d(a)&&!a.globalPropertyChanged(e,t,n))return!1}return!0}applyRendererDiff(e,t){return this.loadStatus!==Ge.LOADED?H.Recreate_Symbol:this.symbolLayers.reduce((i,s)=>i!==H.Recreate_Symbol&&d(s)?Math.min(i,s.applyRendererDiff(e,t)):i,H.Fast_Update)}prepareSymbolPatch(e){if(this.loadStatus===Ge.FAILED||e.diff.type!=="partial")return;const t=e.diff.diff;if(!t.symbolLayers||t.symbolLayers.type!=="partial")return;const i=t.symbolLayers.diff;this.symbolLayers.forEach((s,a)=>{if(b(s))return;const n=i[a];if(n){const o={diff:n,graphics3DGraphicPatches:[],symbolLayerStatePatches:[]};s.prepareSymbolLayerPatch(o),e.symbolStatePatches.push(...o.symbolLayerStatePatches),o.graphics3DGraphicPatches.length&&e.graphics3DGraphicPatches.push((l,h)=>{const c=l.layers[a];d(c)&&o.graphics3DGraphicPatches.forEach(u=>u(c,h))})}})}updateGeometry(e,t){for(let i=0;i<this.symbolLayers.length;i++){const s=this.symbolLayers[i];if(b(s))continue;const a=e.layers[i];if(b(a)||!s.updateGeometry(a,t))return!1}return!0}onRemoveGraphic(e){for(let t=0;t<this.symbolLayers.length;t++){const i=this.symbolLayers[t];if(b(i))continue;const s=e.layers[t];d(s)&&i.onRemoveGraphic(s)}}getFastUpdateStatus(){let e=0,t=0,i=0;return this.symbolLayers.forEach(s=>{b(s)||(s.loadStatus===Ge.LOADING?e++:s.isFastUpdatesEnabled()?i++:t++)}),{loading:e,slow:t,fast:i}}async queryForSnapping(e,t,i,s){const a=this.symbolLayers.filter(d).filter(o=>d(o.queryForSnapping)).map(o=>o.queryForSnapping(e,t,i,s)),n=await Promise.all(a);return Oe(s),n.flat()}destroy(){if(this.destroyed)console.error("Graphics3DSymbol.destroy called when already destroyed!");else{super.destroy();for(const e of this.symbolLayers)d(e)&&e.destroy();this.symbolLayers.length=0,this._destroyed=!0}}get destroyed(){return this._destroyed}}const Jr={renderPriority:0,renderPriorityStep:1,ignoreDrivers:!1};let Zy=class extends _c{constructor(e,t,i){super(e,t,i),this._calloutSymbolLayer=null,this.symbol.hasVisibleCallout()&&(this._calloutSymbolLayer=qg(this.symbol,t))}async doLoad(e){var i;const t=this._calloutSymbolLayer?Ml(this._calloutSymbolLayer.load()):null;try{await super.doLoad(e),Oe(e)}catch(s){throw(i=this._calloutSymbolLayer)==null||i.abortLoad(),s}t&&await t}destroy(){super.destroy(),this._calloutSymbolLayer=K(this._calloutSymbolLayer)}createGraphics3DGraphic(e,t){const i=super.createGraphics3DGraphic(e,t);if(d(this._calloutSymbolLayer)&&d(i)){const s=this._createCalloutGraphic(e);d(s)&&i.addAuxiliaryGraphic(s)}return i}globalPropertyChanged(e,t){return!!super.globalPropertyChanged(e,t)&&(!this._calloutSymbolLayer||this._calloutSymbolLayer.globalPropertyChanged(e,t,i=>this._getCalloutGraphicLayer(i)))}updateGeometry(e,t){const i=super.updateGeometry(e,t);if(i&&this._calloutSymbolLayer){const s=this._getCalloutGraphicLayer(e);if(s)return this._calloutSymbolLayer.updateGeometry(s,t)}return i}_createCalloutGraphic(e){const t=e.renderingInfo;return e.renderingInfo=new Wg(t.renderer,t.symbol),this._calloutSymbolLayer.createGraphics3DGraphic(e)}_getCalloutGraphicLayer(e){for(const t of e._auxiliaryLayers)if(t.graphics3DSymbolLayer===this._calloutSymbolLayer)return t}};function Xy(r,e,t){return r.type==="point-3d"?new Zy(r,e,t):new _c(r,e,t)}let Jy=class extends dn{constructor(e,t,i){super(t),this.symbol=e,this._convert=i,this.symbologySnappingSupported=!1,this.graphics3DSymbol=null,this.referenced=0}getSymbolLayerSize(e){return d(this.graphics3DSymbol)?this.graphics3DSymbol.getSymbolLayerSize(e):null}get symbolLayers(){return d(this.graphics3DSymbol)?this.graphics3DSymbol.symbolLayers:[]}get extentPadding(){return d(this.graphics3DSymbol)?this.graphics3DSymbol.extentPadding:0}async doLoad(e){const t=await this.symbol.fetchSymbol({signal:e});t.id=this.symbol.id,this.graphics3DSymbol=this._convert(t),d(this.graphics3DSymbol)&&await this.graphics3DSymbol.load()}createGraphics3DGraphic(e){return d(this.graphics3DSymbol)?this.graphics3DSymbol.createGraphics3DGraphic(e,this):null}get complexity(){return d(this.graphics3DSymbol)?this.graphics3DSymbol.complexity:un}globalPropertyChanged(e,t){return!!d(this.graphics3DSymbol)&&this.graphics3DSymbol.globalPropertyChanged(e,t)}applyRendererDiff(e,t){return d(this.graphics3DSymbol)?this.graphics3DSymbol.applyRendererDiff(e,t):H.Recreate_Symbol}prepareSymbolPatch(e){d(this.graphics3DSymbol)&&this.graphics3DSymbol.prepareSymbolPatch(e)}updateGeometry(e,t){return!!d(this.graphics3DSymbol)&&this.graphics3DSymbol.updateGeometry(e,t)}onRemoveGraphic(){}getFastUpdateStatus(){return d(this.graphics3DSymbol)?this.graphics3DSymbol.getFastUpdateStatus():{loading:1,fast:0,slow:0}}destroy(){d(this.graphics3DSymbol)&&this.graphics3DSymbol.destroy(),this.graphics3DSymbol=void 0,super.destroy()}get destroyed(){return this.graphics3DSymbol===void 0}},Qy=class{constructor(e){this._graphicsCore=e,this._idToState=new Map,this._states=new Set;const t=e.owner.layer&&e.owner.layer.objectIdField;t?(this._getGraphicId=i=>Vi(i,t),this._getGraphics3DGraphicById=i=>this._graphicsCore.getGraphics3DGraphicByObjectId(i)):(this._getGraphicId=i=>i.uid,this._getGraphics3DGraphicById=i=>this._graphicsCore.getGraphics3DGraphicById(i))}destroy(){this._idToState.clear(),this._states.forEach((e,t)=>this.remove(t))}add(e){const t={remove:()=>this.remove(e)};if(this._states.has(e))return t;const i=this._getGraphicId(e.graphic),s=this._getGraphics3DGraphicById(i);return this._states.has(e)||this._states.add(e),this._ensureStateList(i).push(e),e.displaying=!!d(s)&&s.isVisible(),e.isDraped=!!d(s)&&s.isDraped,e.tracking=!0,d(s)&&e.emit("changed"),t}remove(e){if(this._states.has(e)){if(this._idToState.size){const t=this._getGraphicId(e.graphic),i=this._idToState.get(t);i&&(zc(i,e),i.length===0&&this._idToState.delete(t))}this._states.delete(e),e.tracking=!1,e.displaying=!1}}addGraphic(e){this._forEachState(e,t=>{t.displaying=e.isVisible(),t.isDraped=e.isDraped,t.emit("changed")})}removeGraphic(e){this._forEachState(e,t=>{t.displaying=!1,t.isDraped=!1})}updateGraphicGeometry(e){this._forEachState(e,t=>t.emit("changed"))}updateGraphicVisibility(e){this._forEachState(e,t=>t.displaying=e.isVisible())}allGraphicsDeleted(){this._states.forEach(e=>{e.displaying=!1})}_ensureStateList(e){const t=this._idToState.get(e);if(t)return t;const i=new Array;return this._idToState.set(e,i),i}_forEachState(e,t){if(this._states.size===0||this._idToState.size===0)return;const i=this._getGraphicId(e.graphic),s=this._idToState.get(i);s!=null&&s.forEach(t)}},bt=class extends Ht{constructor(r){super(r),this._index=new sg(9,da("esri-csp-restrictions")?e=>({minX:e.extent[0],minY:e.extent[1],maxX:e.extent[2],maxY:e.extent[3]}):[".extent[0]",".extent[1]",".extent[2]",".extent[3]"]),this._missing=new Set,this._boundsByFeature=new Map,this.spatialReference=null,this.hasZ=null,this.hasM=null,this.objectIdField=null,this.updating=!1}setup(r){this._addMany(r)}destroy(){this._missing.clear(),this._index=K(this._index),this._boundsByFeature.clear(),this._boundsByFeature=null}update(){this._missing.size>0&&(this._addMany(Array.from(this._missing.values())),this.updating=!1,this._missing.clear())}get updatingRemaining(){return this._missing.size}queryGraphicUIDsInExtent(r,e,t){!b(e)&&e.equals(this.spatialReference)&&(ot.minX=r[0],ot.minY=r[1],ot.maxX=r[2],ot.maxY=r[3],this.update(),this._index.search(ot,i=>t(i.graphic.uid)))}add(r){this._missing.add(r),this.updating=!0}remove(r){if(this._missing.delete(r))return void(this.updating=this._missing.size>0);this._index.remove(r);const e=Vi(r.graphic,this._get("objectIdField"));e!=null&&this._boundsByFeature.delete(e)}_addMany(r){if(r.length===0)return;const e=this._get("objectIdField");for(const t of r){t.computeExtent(this.spatialReference);const i=Vi(t.graphic,e);i!=null&&this._boundsByFeature.set(i,t.extent)}this._index.load(r)}clear(){this._index.clear(),this._missing.clear(),this._boundsByFeature.clear(),this.updating=!1}forEachInBounds(r,e){ot.minX=r[0],ot.minY=r[1],ot.maxX=r[2],ot.maxY=r[3],this.update(),this._index.search(ot,t=>{e(t)})}getBounds(r,e){this.update();const t=this._boundsByFeature.get(r);return t?Bl(e,t):null}};y([v({constructOnly:!0})],bt.prototype,"spatialReference",void 0),y([v({constructOnly:!0})],bt.prototype,"hasZ",void 0),y([v({constructOnly:!0})],bt.prototype,"hasM",void 0),y([v({constructOnly:!0})],bt.prototype,"objectIdField",void 0),y([v()],bt.prototype,"updating",void 0),y([v({readOnly:!0})],bt.prototype,"updatingRemaining",null),bt=y([Xe("esri.views.3d.layers.graphics.SpatialIndex2D")],bt);const ot={minX:0,minY:0,maxX:0,maxY:0},Ky=1;let ii=class extends gs.EventedMixin(Ht){get spatialReference(){var e;return(e=this.view)==null?void 0:e.spatialReference}constructor(e){super(e),this._elevationOffset=0,this._layerHandes=new us}initialize(){this._renderCoordsHelper=this.view.renderCoordsHelper,this._intersectLayers=[this.stageLayer],this._intersector=zu(this.view.state.viewingMode),this._intersector.options.store=va.MIN;const e=this._computeLayerExtent(this.spatialReference,this.stageLayer);this._zmin=e[2],this._zmax=e[5];const t=this.stageLayer.events;this._layerHandes.add([t.on("layerObjectAdded",i=>this._objectChanged(i.object)),t.on("layerObjectRemoved",i=>this._objectChanged(i.object)),t.on("objectGeometryAdded",i=>this._objectChanged(i.object)),t.on("objectGeometryRemoved",i=>this._objectChanged(i.object)),t.on("objectGeometryUpdated",i=>this._objectChanged(i.object)),t.on("objectTransformation",i=>this._objectChanged(i))])}dispose(){this._layerHandes.destroy()}elevationInfoChanged(){const e=this.layer!=null?this.layer.elevationInfo:null;if(e!=null&&e.mode!=="on-the-ground"){const t=bl(this.layer.spatialReference),i=Nd(e.unit??"meters");this._elevationOffset=pe(e.offset,0)*i/t}else this._elevationOffset=0}getElevation(e,t,i,s){if(xe[0]=e,xe[1]=t,xe[2]=i,!this._renderCoordsHelper.toRenderCoords(xe,s,xe))return di.getLogger(this.declaredClass).error("could not project point for elevation alignment"),null;const a=this._elevationOffset,n=this._zmin+a,o=this._zmax+a;this._renderCoordsHelper.setAltitude(cl,o,xe),this._renderCoordsHelper.setAltitude(dl,n,xe);const l=h=>{var c;return!!((c=h.metadata)!=null&&c.isElevationSource)};return this._intersector.reset(cl,dl,null),this._intersector.intersect(this._intersectLayers,null,Ky,null,l),this._intersector.results.min.getIntersectionPoint(xe)?this._renderCoordsHelper.getAltitude(xe):null}_objectChanged(e){var n;const t=this.spatialReference;if(!((n=e.metadata)!=null&&n.isElevationSource)||b(t))return;B(yt);const i=e.metadata.lastValidElevationBB;i.isEmpty()||this._expandExtent(t,i.min,i.max,yt);const{min:s,max:a}=e.boundingVolumeWorldSpace;this._expandExtent(t,s,a,yt),Na(yt,Ga),this._zmin=Math.min(this._zmin,yt[2]),this._zmax=Math.max(this._zmax,yt[5]),oa.extent=Ga,oa.spatialReference=t,this.emit("elevation-change",oa),de(i.min,s),de(i.max,a)}_computeLayerExtent(e,t){return B(yt),d(e)&&t.objects.forAll(i=>this._expandExtent(e,i.boundingVolumeWorldSpace.min,i.boundingVolumeWorldSpace.max,yt)),yt}_expandExtent(e,t,i,s){for(let a=0;a<8;++a)xe[0]=1&a?t[0]:i[0],xe[1]=2&a?t[1]:i[1],xe[2]=4&a?t[2]:i[2],this._renderCoordsHelper.fromRenderCoords(xe,xe,e),cr(s,xe);return s}};y([v({constructOnly:!0})],ii.prototype,"layer",void 0),y([v({constructOnly:!0})],ii.prototype,"stageLayer",void 0),y([v({constructOnly:!0})],ii.prototype,"view",void 0),y([v()],ii.prototype,"spatialReference",null),ii=y([Xe("esri.views.3d.layers.support.StageLayerElevationProvider")],ii);const yt=B(),Ga=Nl(),oa={spatialReference:null,extent:Ga,context:"scene"},xe=C(),cl=C(),dl=C();function e_(r,e,t){if(b(r)||b(t))return!1;let i=!0;return Le[0]=r.xmin!=null?r.xmin:0,Le[1]=r.ymin!=null?r.ymin:0,Le[2]=r.zmin!=null?r.zmin:0,i=i&&ut(Le,r.spatialReference,0,Le,t,0,1),e[0]=Le[0],e[1]=Le[1],Le[0]=r.xmax!=null?r.xmax:0,Le[1]=r.ymax!=null?r.ymax:0,Le[2]=r.zmax!=null?r.zmax:0,i=i&&ut(Le,r.spatialReference,0,Le,t,0,1),e[2]=Le[0],e[3]=Le[1],r.xmin==null&&(e[0]=-1/0),r.ymin==null&&(e[1]=-1/0),r.xmax==null&&(e[2]=1/0),r.ymax==null&&(e[3]=1/0),i}const Le=C();var Ma;const la=C(),t_=_e(),bc="esri.views.3d.layers.graphics.Graphics3DCore",Ce=di.getLogger(bc);let I=Ma=class extends Ht{get _viewSpatialReference(){return this.owner.view.spatialReference}get spatialIndex(){var r;return this._spatialIndex||(this._spatialIndex=new bt({objectIdField:(r=this.owner.layer)==null?void 0:r.objectIdField,spatialReference:this._viewSpatialReference,hasZ:!!this.hasZ,hasM:!!this.hasM}),this._spatialIndex.setup(Array.from(this.graphics3DGraphics.values()))),this._spatialIndex.update(),this._spatialIndex}get numberOfGraphics(){return this._numberOfGraphics}get effectiveUpdatePolicy(){return d(this.currentRenderer)&&this.currentRenderer.type==="dictionary"?he.ASYNC:pe(this._forcedUpdatePolicy,this.preferredUpdatePolicy)}get featureStore(){return this._featureStore}get initializePromise(){return this._initializePromise}get scaleVisibility(){return this._scaleVisibility}get elevationAlignment(){return this._elevationAlignment}get objectStates(){return this._objectStates}get filterVisibility(){return this._filterVisibility}get updating(){var r;return!!(this._graphicsWaitingForSymbol.size>0||this.running||(r=this._elevationAlignment)!=null&&r.updating||d(this._scaleVisibility)&&this._scaleVisibility.updating||d(this._filterVisibility)&&this._filterVisibility.updating||this._rendererChangeAbortController||this._elevationInfoChangeAbortController||this._updatingPendingLoadedGraphicsChange||this._frameTask.updating||this._loadingSymbols>0)}get running(){var r;return this._pendingUpdates.size>0||!!((r=this._spatialIndex)!=null&&r.updating)}get suspendedOrOutsideOfView(){var r;return this.owner.suspended||!!((r=this.owner.suspendInfo)!=null&&r.outsideOfView)}get updatingRemaining(){var r,e;return this.updating?this._pendingUpdates.size+.1*(((r=this._spatialIndex)==null?void 0:r.updatingRemaining)||0)+.1*(((e=this._elevationAlignment)==null?void 0:e.updatingRemaining)||0):0}get displayFeatureLimit(){const r=this.owner&&this.owner.view&&this.owner.view.qualitySettings,e=r?r.graphics3D.minTotalNumberOfFeatures:0,t=r?r.graphics3D.maxTotalNumberOfFeatures:0,i=r?r.graphics3D.maxTotalNumberOfPrimitives:0,s=this.averageSymbolComplexity,a=Math.max(1,d(s)?s.primitivesPerFeature:1),n=d(s)&&s.drawCallsPerFeature>0?t/s.drawCallsPerFeature*.3:t,o=Math.ceil(i/a),l=Math.max(e,Math.min(t,o,n)),h=this._get("displayFeatureLimit");return h&&h.minimumTotalNumberOfFeatures===e&&h.maximumTotalNumberOfFeatures===t&&h.maximumTotalNumberOfPrimitives===i&&h.averageSymbolComplexity===s&&h.maximumNumberOfFeatures===l?h:{minimumTotalNumberOfFeatures:e,maximumTotalNumberOfFeatures:t,maximumTotalNumberOfPrimitives:i,averageSymbolComplexity:s,maximumNumberOfFeatures:l}}get averageSymbolComplexity(){const r=Gg(this._symbolComplexities),e=this._get("averageSymbolComplexity");return r.numComplexities===0||d(e)&&(r.estimated&&(e.primitivesPerFeature>=r.primitivesPerFeature||e.primitivesPerCoordinate>=r.primitivesPerCoordinate||e.drawCallsPerFeature>=r.drawCallsPerFeature)||e.primitivesPerFeature===r.primitivesPerFeature&&e.primitivesPerCoordinate===r.primitivesPerCoordinate&&e.drawCallsPerFeature===r.drawCallsPerFeature)?e:r}get usedMemory(){const r=d(this.averageSymbolComplexity)&&this.labelsEnabled?this.averageSymbolComplexity.memory.bytesPerFeatureLabel*this._numberOfGraphics:0,e=this._getSymbolComplexitiesUsed().reduce((t,i)=>t+i.memory.resourceBytes,0);return this._usedMemory+r+e}get usedMemoryPerGraphic(){if(this._usedMemory&&this._numberOfGraphics){const r=this._numberOfGraphics/(this._numberOfGraphics+Math.max(this._pendingAdds,this._pendingRemoves));return this._usedMemory/this._numberOfGraphics*r}if(d(this.averageSymbolComplexity)){const r=this.labelsEnabled?this.averageSymbolComplexity.memory.bytesPerFeatureLabel:0;return this.averageSymbolComplexity.memory.bytesPerFeature+r}return 0}get unprocessedMemoryEstimate(){return(this._pendingAdds-this._pendingRemoves)*this.usedMemoryPerGraphic}get _symbolComplexities(){return this.currentRenderer?this._getSymbolComplexitiesUsedOrRenderer(this.currentRenderer):this._getSymbolComplexitiesUsed()}get visible(){return this._visible}_getConvertedSymbol(r){var s;if(r.type==="web-style")return r.clone();const e=this._symbolConversionCache.get(r.id);if(d(e))return e;const t=Tn(r,{geometryType:((s=this.layer)==null?void 0:s.geometryType)??void 0,retainId:!0,hasLabelingContext:this._hasLabelingContext(r)}),i=t.symbol||null;return b(i)&&t.error&&Ce.error(t.error.message),this._symbolConversionCache.set(r.id,i),i}_getSymbolComplexitiesUsedOrRenderer(r){if(b(r))return[];const e=r.getSymbols(),t="backgroundFillSymbol"in r?r.backgroundFillSymbol:null;if(!(t||e&&e.length))return[];const i=[],s=this._getSymbolComplexityUsedOrRenderer(t);d(s)&&i.push(s);for(const a of e){const n=this._getSymbolComplexityUsedOrRenderer(a);d(n)&&i.push(n)}return i}_getSymbolComplexityUsedOrRenderer(r){if(b(r))return null;const e=this._symbols.get(r.id);if(d(e))return e.complexity;const t=this._getConvertedSymbol(r);return d(t)?$g(t):null}_getSymbolComplexitiesUsed(){const r=[];return this._symbols.forEach(e=>{d(e)&&r.push(e.complexity)}),r}get _objectIdField(){return this.layer.objectIdField}constructor(r){super(r),this._propertiesPool=new Bd({computedExtent:Ba},this),this.computedExtent=null,this.currentRenderer=null,this.rendererHasGeometryOperations=!1,this._graphicStateTracking=null,this.graphics3DGraphics=new Map,this.stageLayer=null,this.stage=null,this._graphicsDrapedUids=new Set,this._graphicsBySymbol=new Map,this._symbolConversionCache=new Map,this._symbols=new Map,this._graphicsWithoutSymbol=new Map,this._graphicsWaitingForSymbol=new Map,this._graphicsUpdateId=0,this._handles=new us,this._frameTask=Ln,this._suspendSymbolCleanup=!1,this._arcadeOnDemand=null,this._rendererChangeAbortController=null,this._elevationInfoChangeAbortController=null,this._initializeAbortController=null,this._scaleVisibility=null,this._filterVisibility=null,this._spatialIndex=null,this.extentPadding=0,this._updatingPendingLoadedGraphicsChange=null,this._featureStore=null,this._deconflictor=null,this._labeler=null,this._objectStates=null,this._viewElevationProvider=null,this._stageLayerElevationProvider=null,this._sharedSymbolResourcesOwnerHandle=null,this._whenGraphics3DGraphicRequests={},this._pendingUpdates=new Map,this._numberOfGraphics=0,this._numberOfGraphicsProvidingElevation=0,this._pendingAdds=0,this._pendingRemoves=0,this._applyPendingRemovesFirst=!1,this._loadingSymbols=0,this._pendingUpdatesPool=new ca({allocator:e=>e||new i_,deallocator:e=>(e.clear(),e)}),this._symbolWarningLogged=!1,this._geometryWarningLogged=!1,this._objectIdInvisibleSet=new Set,this._whenSymbolRemoved=new ca,this.preferredUpdatePolicy=he.SYNC,this._forcedUpdatePolicy=null,this.elevationFeatureExpressionEnabled=!0,this.owner=null,this.layer=null,this.graphicSymbolSupported=!0,this.getRenderingInfoWithoutRenderer=!1,this.setUidToIdOnAdd=!0,this.hasZ=null,this.hasM=null,this._usedMemory=0,this._visible=!1,this._startCreateGraphics=!1,this.symbolCreationContext=new Og(r.owner.view.resourceController.scheduler,(e,t)=>this._frameTask.schedule(e,t))}initialize(){this._featureStore=new ht({objectIdField:this.owner.layer&&this.owner.layer.objectIdField,hasZ:!!this.hasZ,hasM:!!this.hasM,viewSpatialReference:this._viewSpatialReference,featureSpatialReference:this.owner.featureSpatialReference,getSpatialIndex:()=>this.spatialIndex,forEach:t=>this.graphics3DGraphics.forEach(t)});const r=(t,i,s)=>this.spatialIndex.queryGraphicUIDsInExtent(t,i,s),{componentFactories:e}=this;if(d(e.elevationAlignment)){const t=e.elevationAlignment(this,r);this._elevationAlignment=t}if(d(e.scaleVisibility)){const t=e.scaleVisibility(this,r);this._scaleVisibility=t}if(d(e.filterVisibility)){const t=e.filterVisibility({featureStore:this._featureStore,getFeatureCount:()=>this.graphics3DGraphics.size,updateFeatureVisibilities:i=>this.modifyGraphics3DGraphicVisibilities(s=>s.setVisibilityFlag(ze.FILTER,i(Vi(s.graphic,this._objectIdField)),ye.GRAPHIC)),setAllFeaturesVisibility:i=>this.modifyGraphics3DGraphicVisibilities(s=>s.setVisibilityFlag(ze.FILTER,i,ye.GRAPHIC)),clearFeaturesVisibility:()=>this.modifyGraphics3DGraphicVisibilities(i=>i.clearVisibilityFlag(ze.FILTER))});this._filterVisibility=t}if(d(e.deconflictor)){const t=e.deconflictor(this);this._deconflictor=t}if(d(e.labeler)&&d(this._scaleVisibility)){const t=e.labeler(this,this._scaleVisibility);this._labeler=t}if(d(e.objectStates)){const t=e.objectStates(this);this._objectStates=t}this._initializeAbortController=new AbortController,this._initializePromise=this._initializeAsync()}async _initializeAsync(){var s,a,n,o;const r=(s=this._initializeAbortController)==null?void 0:s.signal,e=this.owner.view;this._viewElevationProvider=new Pg(this._viewSpatialReference,e),this._initializeStage(e,this.layer.uid);const t=e.sharedSymbolResources;this.symbolCreationContext.sharedResources=t,this._sharedSymbolResourcesOwnerHandle=t.addGraphicsOwner(this.owner),d(this.currentRenderer)&&(this.symbolCreationContext.renderer=this.currentRenderer),this.symbolCreationContext.stage=this.stage,this.symbolCreationContext.streamDataRequester=t.streamDataRequester,this.symbolCreationContext.renderCoordsHelper=e.renderCoordsHelper,this.symbolCreationContext.layer=this.layer,this.symbolCreationContext.graphicsCoreOwner=this.owner,this.symbolCreationContext.localOriginFactory=new Vu(e.renderSpatialReference),this.symbolCreationContext.elevationProvider=e.elevationProvider,this.symbolCreationContext.notifyGraphicGeometryChanged=l=>this.notifyGraphicGeometryChanged(l),this.symbolCreationContext.notifyGraphicVisibilityChanged=l=>this.notifyGraphicVisibilityChanged(l);const i=fa(this.layer.elevationInfo,this.elevationFeatureExpressionEnabled);if(this.symbolCreationContext.featureExpressionInfoContext=await ya(i,this._viewSpatialReference,r,Ce),Oe(r),this.symbolCreationContext.screenSizePerspectiveEnabled=e.screenSizePerspectiveEnabled&&!!this.layer.screenSizePerspectiveEnabled,this.symbolCreationContext.slicePlaneEnabled=!!this.owner.slicePlaneEnabled,this.symbolCreationContext.physicalBasedRenderingEnabled=!!((a=this.owner.view.qualitySettings)!=null&&a.physicallyBasedRenderingEnabled),this.symbolCreationContext.skipHighSymbolLods=!!((o=(n=this.owner.view.qualitySettings)==null?void 0:n.graphics3D)!=null&&o.skipHighSymbolLods),"drapeSourceType"in this.owner){const{owner:l}=this;this.symbolCreationContext.drapeSourceRenderer=e.basemapTerrain.overlayManager.registerGeometryDrapeSource(l),this._handles.add(ds(()=>e.basemapTerrain.overlayManager.unregisterDrapeSource(l)))}this._handles.add([$e(()=>this.suspendedOrOutsideOfView,()=>this._frameTask.reschedule(()=>this._updateLayerVisibility())),$e(()=>{var l;return[(l=this.layer)==null?void 0:l.screenSizePerspectiveEnabled,this.owner.view.screenSizePerspectiveEnabled]},()=>{var h;const l=e.screenSizePerspectiveEnabled&&!!this.layer.screenSizePerspectiveEnabled;l!==this.symbolCreationContext.screenSizePerspectiveEnabled&&(this.symbolCreationContext.screenSizePerspectiveEnabled=l,(h=this._labeler)==null||h.reset(),this.recreateAllGraphicsAndSymbols())}),$e(()=>this.owner.slicePlaneEnabled,l=>this._slicePlaneEnabledChange(!!l)),$e(()=>{var l;return(l=this.owner.view.state)==null?void 0:l.rasterPixelRatio},()=>this._pixelRatioChange()),$e(()=>{var l;return!!((l=this.owner.view.qualitySettings)!=null&&l.physicallyBasedRenderingEnabled)},l=>this._physicalBasedRenderingChange(l)),$e(()=>{var l,h;return!!((h=(l=this.owner.view.qualitySettings)==null?void 0:l.graphics3D)!=null&&h.skipHighSymbolLods)},l=>this._skipHighSymbolLoDsChange(l)),Cl(()=>{var l;return(l=e.basemapTerrain)==null?void 0:l.tilingScheme},l=>{if(!l.spatialReference.equals(this.symbolCreationContext.overlaySR)&&d(e.basemapTerrain.spatialReference)&&(this.symbolCreationContext.overlaySR=e.basemapTerrain.spatialReference),this._handles.has("loaded-graphics"))this.recreateAllGraphics();else{const h=()=>{var c;return(c=this.owner)==null?void 0:c.loadedGraphics};this._handles.add([Vc(h,"change",c=>{this._graphicsCollectionChanged(c),this._signalUpdatingDuringAsyncLoadedGraphicsChange()},{onListenerAdd:()=>{this.recreateAllGraphics(),this._signalUpdatingDuringAsyncLoadedGraphicsChange()}})],"loaded-graphics")}},{initial:!0}),$e(()=>this.effectiveUpdatePolicy,l=>{d(this.stageLayer)&&(this.stageLayer.updatePolicy=l),this.symbolCreationContext.isAsync=this.effectiveUpdatePolicy===he.ASYNC,l===he.SYNC&&this.runTask(qa)},Pl)]),this._frameTask=e.resourceController.scheduler.registerTask(Or.GRAPHICS_CORE,this),this.layer&&"featureReduction"in this.layer&&this._handles.add($e(()=>this.layer.featureReduction,()=>this._deconflictor.featureReductionChange())),this.notifyChange("averageSymbolComplexity"),this.rendererChange(this.owner.renderer).catch(()=>{}),this._initializeAbortController=null}_abortInitialize(){this._initializeAbortController&&(this._initializeAbortController.abort(),this._initializeAbortController=null)}destroy(){this._abortInitialize(),this._abortRendererChange(),this._abortElevationInfoChange(),this._frameTask.remove(),this._frameTask=Ln,this.owner.view.deconflictor.removeGraphicsOwner(this),this.owner.view.labeler.removeGraphicsOwner(this),this._elevationAlignment=K(this._elevationAlignment),this._scaleVisibility=K(this._scaleVisibility),this._filterVisibility=K(this._filterVisibility),this._deconflictor=null,this._labeler=null,this._objectStates=K(this._objectStates),this.clear(),this._featureStore=K(this._featureStore),this._updatingPendingLoadedGraphicsChange=wn(this._updatingPendingLoadedGraphicsChange),this._graphicStateTracking=K(this._graphicStateTracking),this.stage&&(this.stage.remove(this.stageLayer),this.stageLayer=null,this.stage=null),this._handles=K(this._handles),this._set("owner",null);for(const r in this._whenGraphics3DGraphicRequests)this._whenGraphics3DGraphicRequests[r].reject(new We("graphic:layer-destroyed","Layer has been destroyed"));this._whenGraphics3DGraphicRequests=null,this._sharedSymbolResourcesOwnerHandle=wn(this._sharedSymbolResourcesOwnerHandle),this._propertiesPool=K(this._propertiesPool),this._pendingUpdatesPool=null,this._symbolConversionCache.clear(),this._objectIdInvisibleSet.clear(),this._spatialIndex=K(this._spatialIndex)}clear(){var r,e;(r=this._objectStates)==null||r.allGraphicsDeleted(),d(this._graphicStateTracking)&&this._graphicStateTracking.allGraphicsDeleted(),this.graphics3DGraphics.forEach(t=>t.destroy()),(e=this._spatialIndex)==null||e.clear(),this.graphics3DGraphics.clear(),this._numberOfGraphics=0,this._usedMemory=0,this._updateLayerVisibility(),this._symbols.forEach(K),this._symbols.clear(),this._graphicsBySymbol.clear(),this._graphicsWithoutSymbol.clear(),this._graphicsWaitingForSymbol.clear(),this._pendingUpdates.clear(),this._pendingUpdatesPool.clear(),this._pendingAdds=0,this._pendingRemoves=0,this._applyPendingRemovesFirst=!1,this.notifyChange("updating"),this.notifyChange("running"),this.notifyChange("updatingRemaining"),this._featureStore.events.emit("changed")}_initializeStage(r,e){this.stage=r._stage,this.stageLayer=new Uu({pickable:!this.suspendedOrOutsideOfView,updatePolicy:this.effectiveUpdatePolicy},e),this.stage.add(this.stageLayer);const t=this.stageLayer.events;t.on("objectTransformation",i=>this.notifyGraphicGeometryChanged(i.metadata.graphicUid)),t.on("visibilityChanged",i=>this.notifyGraphicVisibilityChanged(i.metadata.graphicUid)),t.on("objectGeometryAdded",i=>this.notifyGraphicGeometryChanged(i.object.metadata.graphicUid)),t.on("objectGeometryRemoved",i=>this.notifyGraphicGeometryChanged(i.object.metadata.graphicUid)),t.on("objectGeometryUpdated",i=>this.notifyGraphicGeometryChanged(i.object.metadata.graphicUid))}notifyGraphicGeometryChanged(r){if(b(this._graphicStateTracking)||b(r))return;const e=this.graphics3DGraphics.get(r);e&&this._graphicStateTracking.updateGraphicGeometry(e)}notifyGraphicVisibilityChanged(r){if(b(this._graphicStateTracking)||b(r))return;const e=this.graphics3DGraphics.get(r);e&&this._graphicStateTracking.updateGraphicVisibility(e)}_updateLayerVisibility(){const r=this.displayFeatureLimit.maximumNumberOfFeatures,e=this._numberOfGraphics>r*r_,t=!this.suspendedOrOutsideOfView&&!e;t!==this._visible&&(this._visible=t,t?(this.stageLayer.pickable=!0,this.updateAllGraphicsVisibility()):(this.stageLayer.pickable=!1,this._hideAllGraphics()),this._updateStageLayerVisibility())}_updateStageLayerVisibility(){this.stageLayer.visible=this._visible&&(this.layer.opacity==null||this.layer.opacity>0)}getGraphics3DGraphicById(r){return r!=null?this.graphics3DGraphics.get(r):void 0}getGraphics3DGraphicByObjectId(r){var e;return(e=this.owner.layer)!=null&&e.objectIdField?this._findGraphics3DGraphicByObjectId(r):null}_getGraphicObjectID(r,e=this.owner.layer&&this.owner.layer.objectIdField){return Vi(r,e)}get graphics3DGraphicsByObjectID(){const r=this.owner.layer&&this.owner.layer.objectIdField;if(!r)return;const e=new Map;return this.graphics3DGraphics.forEach(t=>{if(!t)return;const i=t.graphic,s=this._getGraphicObjectID(i,r);d(s)&&e.set(s,t)}),e}get labelsEnabled(){return!(!this._labeler||!this._labeler.layerLabelsEnabled())}async updateLabelingInfo(r){const e=this._deconflictor&&this._deconflictor.labelingInfoChange(r),t=this._labeler&&this._labeler.labelingInfoChange(r);await Ac([e,t])}updateVisibilityInfo(){this._deconflictor&&this._deconflictor.labelingInfoChange(),this._labeler&&this._labeler.visibilityInfoChange()}get symbolUpdateType(){if(this._pendingUpdates.size>0)return"unknown";let r=0,e=0;return Ic(this._symbols,(t,i)=>{if(d(t)){const s=t.getFastUpdateStatus();if(s.loading>0)return!0;this._graphicsBySymbol.has(i)&&(e+=s.fast,r+=s.slow)}return!1})?"unknown":e>=0&&r===0?"fast":r>=0&&e===0?"slow":"mixed"}runTask(r){if(this._frameTask.processQueue(r),this._applyPendingUpdates(r),this.notifyChange("running"),this.running||this.notifyChange("updating"),this.notifyChange("updatingRemaining"),!r.hasProgressed)return Ya.YIELD}setObjectIdVisibility(r,e){e?this._objectIdInvisibleSet.delete(r):this._objectIdInvisibleSet.add(r);const t=this._findGraphics3DGraphicByObjectId(r);d(t)&&this._updateUserVisibility(t)}_findGraphics3DGraphicByObjectId(r){return Tc(this.graphics3DGraphics,e=>this._getGraphicObjectID(e.graphic)===r)}_updateUserVisibility(r){if(b(r))return!1;const e=r.graphic,t=this._getGraphicObjectID(e),i=e.visible&&!this.owner.suspended&&(b(t)||!this._objectIdInvisibleSet.has(t));return r.setVisibilityFlag(ze.USER_SETTING,i,ye.GRAPHIC)}_whenGraphics3DGraphic(r){const e=this.graphics3DGraphics.get(r.uid);if(e)return Promise.resolve(e);const t=this._whenGraphics3DGraphicRequests[r.uid];if(t)return t.promise;const i=Lc();return this._whenGraphics3DGraphicRequests[r.uid]=i,i.promise}async _boundsForGraphics3DGraphic(r,e){const t=this._viewSpatialReference,i=this.owner.view.renderSpatialReference,s=this.owner.view.basemapTerrain.spatialReference,a=(c,u,p)=>ut(c,i,u,c,t,u,p),n=(c,u,p)=>ut(c,s,u,c,t,u,p),o=this._viewElevationProvider?{service:this._viewElevationProvider,useViewElevation:!!d(e)&&!!e.useViewElevation,minDemResolution:d(e)?e.minDemResolution:null,minDemResolutionForPoints:this.owner.view.resolution}:null,l=await r.getProjectedBoundingBox(a,n,o,ie(e,"signal"));if(!l)return null;const h=l.boundingBox;if(l.requiresDrapedElevation){const c=this.symbolCreationContext.elevationProvider;if(c){dt(h,la);const u=pe(c.getElevation(la[0],la[1],0,t,"ground"),0);h[2]=Math.min(h[2],u),h[5]=Math.max(h[5],u)}}return{boundingBox:h,screenSpaceObjects:l.screenSpaceObjects}}async whenGraphicBounds(r,e){await xl(()=>{var a;return(a=this.owner)==null?void 0:a.loadedGraphics});const t=this.owner.layer&&this.owner.layer.objectIdField,i=this.owner.loadedGraphics.find(a=>a===r||t!=null&&a.attributes!=null&&r.attributes&&a.attributes[t]===r.attributes[t]);if(!i)throw new We("internal:graphic-not-part-of-view","Graphic is not part of this view");const s=await this._whenGraphics3DGraphic(i);return this._boundsForGraphics3DGraphic(s,e)}computeAttachmentOrigin(r,e){const t=this.graphics3DGraphics.get(r.uid);if(!t)return null;const i=t.computeAttachmentOrigin();if(i.render.num===0&&i.draped.num===0)return null;D(lt,0,0,0);let s=0;if(i.render.num>0){if(!ga(i.render.origin,this.symbolCreationContext.renderCoordsHelper.spatialReference,Ri,e))return null;me(lt,lt,Ri),s++}if(i.draped.num>0){const[a,n]=i.draped.origin,o=pe(this._viewElevationProvider.getElevation(a,n,"ground"),0);if(D(Ri,a,n,o),!ga(Ri,this._viewElevationProvider.spatialReference,Ri,e))return null;me(lt,lt,Ri),s++}return s>1&&ae(lt,lt,1/s),new Dc({x:lt[0],y:lt[1],z:lt[2],spatialReference:e})}getSymbolLayerSize(r,e){const t=this._symbols.get(r.id);if(b(t))throw new We("internal:symbol-not-part-of-view","Symbol is not part of this view");const i=r.symbolLayers.indexOf(e);if(i===-1)throw new We("internal:missing-symbol-layer","Symbol layer is not in symbol");const s=t.getSymbolLayerSize(i);if(b(s))throw new We("internal:missing-size","Symbol layer has no valid size");return s}_graphicsCollectionChanged(r){this._startCreateGraphics&&(this.add(r.added),this.remove(r.removed))}graphicUpdateHandler(r){const e=r.graphic.uid,t=this.graphics3DGraphics.get(e);if(!b(t)||!b(this._graphicsWithoutSymbol.get(e)))switch(r.property){case"visible":this._graphicUpdateVisibleHandler(t);break;case"geometry":this._graphicUpdateGeometryHandler(t,r);break;case"symbol":this._graphicUpdateSymbolHandler(t,r);break;case"attributes":break;case"transform":this._graphicUpdateTransformHandler(t,r)}}_graphicUpdateGeometryHandler(r,e){const t=e.graphic.geometry;if(b(t))return void this._recreateGraphic(e.graphic);if(b(r)){const s=e.graphic.symbol&&e.graphic.symbol.id;if(s){const a=this._symbols.get(s);if(d(a)&&a.loadStatus===Ge.LOADING)return}return void this._recreateGraphic(e.graphic)}const i=r.graphics3DSymbol;!b(e.newValue)&&i.updateGeometry(r,e.newValue)||this._recreateGraphic(r.graphic),this._expandComputedExtent(t)}_graphicUpdateSymbolHandler(r,e){const t=e.graphic,i=d(r)?r.graphics3DSymbol:d(e.oldValue)?this._symbols.get(e.oldValue.id):null;if(b(i)||b(e.newValue))return void this._recreateGraphic(t);const s=i.symbol,a=this._getConvertedSymbol(e.newValue);if(d(a)&&(a.type!==s.type||a.type==="web-style")||s.type==="web-style")return void this._recreateGraphic(t);const n=this._graphicsBySymbol.get(s.id);if(n&&n.size!==1)return void this._recreateGraphic(t);const o=ma(s,a);if(b(o))return void this._updateSymbolMapping(s.id,a);const l={diff:o,graphics3DGraphicPatches:[],symbolStatePatches:[]};if(i.prepareSymbolPatch(l),!jd(l.diff))return void this._recreateGraphic(t);const h=this._getRenderingInfo(t);if(b(h))return void this._recreateGraphic(t);const c=i.extentPadding;for(const u of l.symbolStatePatches)u();if(c!==i.extentPadding&&this._recomputeExtentPadding(),d(r))for(const u of l.graphics3DGraphicPatches)u(r,h);this._updateSymbolMapping(s.id,a)}_graphicUpdateVisibleHandler(r){this._updateUserVisibility(r)&&(this._labeler&&this.owner.view.labeler.setDirty(),this.owner.view.deconflictor.setDirty())}_graphicUpdateTransformHandler(r,e){}recreateGraphics(r){this._suspendSymbolCleanup=!0,this.remove(r),this.add(r),this._suspendSymbolCleanup=!1,this.effectiveUpdatePolicy===he.SYNC&&this._cleanupSymbols()}_recreateGraphic(r){this.recreateGraphics([r])}_beginGraphicUpdate(r){const e=this._graphicsUpdateId;return this._graphicsUpdateId++,this._graphicsWaitingForSymbol.set(r.uid,e),this._graphicsWaitingForSymbol.size===1&&this.notifyChange("updating"),e}_endGraphicUpdate(r){r&&(this._graphicsWaitingForSymbol.delete(r.uid),this._graphicsWaitingForSymbol.size===0&&(this._cleanupSymbols(),this.notifyChange("updating")))}_recomputeExtentPadding(){let r=0;this._symbols.forEach(e=>{d(e)&&(r=Math.max(r,e.extentPadding))}),this._set("extentPadding",r)}_expandComputedExtent(r){const e=t_,t=r.spatialReference;tu(r,e);const i=this._viewSpatialReference,s=Ma.tmpVec;if(_l(t,i)||Dn(e[0],e[1],0,t,s,i)&&(e[0]=s[0],e[1]=s[1],Dn(e[3],e[4],0,t,s,i),e[3]=s[0],e[4]=s[1]),!(isFinite(e[0])&&isFinite(e[3])&&isFinite(e[1])&&isFinite(e[4])))return;const a=this.computedExtent;let n=null;const o=isFinite(e[2])&&isFinite(e[5]),l=o&&(!a||a.zmin==null||e[2]<a.zmin),h=o&&(!a||a.zmax==null||e[5]>a.zmax);a?(e[0]<a.xmin||e[1]<a.ymin||e[3]>a.xmax||e[4]>a.ymax||l||h)&&(n=this._propertiesPool.get("computedExtent"),n.xmin=Math.min(e[0],a.xmin),n.ymin=Math.min(e[1],a.ymin),n.xmax=Math.max(e[3],a.xmax),n.ymax=Math.max(e[4],a.ymax),n.spatialReference=i):(n=this._propertiesPool.get("computedExtent"),n.xmin=e[0],n.ymin=e[1],n.xmax=e[3],n.ymax=e[4],n.spatialReference=i),n&&(l&&(n.zmin=e[2]),h&&(n.zmax=e[5]),this._set("computedExtent",n))}_abortElevationInfoChange(){this._elevationInfoChangeAbortController&&(this._elevationInfoChangeAbortController.abort(),this._elevationInfoChangeAbortController=null)}async elevationInfoChange(){var t,i;this._abortElevationInfoChange();const r=new AbortController;this._elevationInfoChangeAbortController=r;const e=fa(this.layer.elevationInfo,this.elevationFeatureExpressionEnabled);this.symbolCreationContext.featureExpressionInfoContext=await ya(e,this._viewSpatialReference,r.signal,Ce),Oe(r.signal),this._elevationInfoChangeAbortController=null,(t=this._labeler)==null||t.elevationInfoChange(),this.forEachGraphics3DSymbol((s,a,n)=>{s.globalPropertyChanged("elevationInfo",a)?a.forEach(o=>{const l=o.graphic,h=o.labelLayers;for(const c of h)c.graphics3DSymbolLayer.updateGraphicElevationContext(l,c)}):this._recreateSymbol(n)}),this.updateStageLayerElevationProvider(),(i=this._elevationAlignment)==null||i.elevationInfoChange()}updateStageLayerElevationProvider(){this._stageLayerElevationProvider?(this.layer.elevationInfo&&this.layer.elevationInfo.mode==="relative-to-scene"||this._numberOfGraphicsProvidingElevation===0)&&(this.owner.view.elevationProvider.unregister(this._stageLayerElevationProvider),this._stageLayerElevationProvider=On(this._stageLayerElevationProvider)):(!this.layer.elevationInfo||this.layer.elevationInfo&&this.layer.elevationInfo.mode!=="relative-to-scene")&&this._numberOfGraphicsProvidingElevation>0&&(this._stageLayerElevationProvider=new ii({layer:this.layer,stageLayer:this.stageLayer,view:this.owner.view}),this.owner.view.elevationProvider.register("scene",this._stageLayerElevationProvider))}_clearSymbolsAndGraphics(){var r,e,t,i;this.clear(),d(this._filterVisibility)&&this._filterVisibility.clear(),(r=this._labeler)==null||r.reset(),(e=this._deconflictor)==null||e.clear(),(t=this._elevationAlignment)==null||t.clear(),(i=this.stageLayer)==null||i.invalidateSpatialQueryAccelerator(),this._stageLayerElevationProvider&&(this.owner.view.elevationProvider.unregister(this._stageLayerElevationProvider),this._stageLayerElevationProvider=On(this._stageLayerElevationProvider))}startCreateGraphics(){this._startCreateGraphics=!0,this.recreateAllGraphics()}recreateAllGraphics(){this._recreateAllGraphics(!1)}recreateAllGraphicsAndSymbols(){this._recreateAllGraphics(!0)}_recreateAllGraphics(r=!1){if(!this._startCreateGraphics)return;const{loadedGraphics:e,view:t}=this.owner,i=t.basemapTerrain.tilingScheme&&e&&e.length?e.toArray():null;!r&&i||this._clearSymbolsAndGraphics(),this.symbolCreationContext.screenSizePerspectiveEnabled=this.owner.view.screenSizePerspectiveEnabled&&!!this.layer.screenSizePerspectiveEnabled,this.symbolCreationContext.slicePlaneEnabled=!!this.owner.slicePlaneEnabled,this._set("computedExtent",null),i&&(r?this.add(i):this.recreateGraphics(i))}_recreateSymbol(r){const e=this._graphicsBySymbol.get(r),t=[];e&&(e.forEach((s,a)=>{var o;const n=s.usedMemory;this._conditionalRemove(s,a),(o=this._spatialIndex)==null||o.remove(s),t.push(s.graphic),s.destroy(),this._removeGraphics3DGraphic(a,n),this._updateLayerVisibility(),this._featureStore.events.emit("changed")}),this._graphicsBySymbol.set(r,new Map));const i=this._symbols.get(r);K(i),this._symbols.delete(r),this.add(t)}_recreateGraphicsForSymbol(r){const e=this._graphicsBySymbol.get(r);if(e){const t=[];e.forEach(i=>t.push(i.graphic)),this.recreateGraphics(t)}}_conditionalRemove(r,e){var t,i,s;this._graphicsDrapedUids.delete(e),(t=this._objectStates)==null||t.removeGraphic(r),(i=this._labeler)==null||i.removeGraphic(r),(s=this._deconflictor)==null||s.removeGraphic(r),d(this._graphicStateTracking)&&this._graphicStateTracking.removeGraphic(r)}add(r){r&&r.length!==0&&(this.owner.view.basemapTerrain&&this.owner.view.basemapTerrain.tilingScheme?(this._updatePolicyForGraphics(r)===he.ASYNC?this._addDelayed(r):this._addImmediate(r),this.notifyChange("updating")):Ce.error("#add()","Cannot add graphics before terrain surface has been initialized"))}_updatePolicyForGraphics(r){if(this.effectiveUpdatePolicy===he.SYNC&&(this.layer.geometryType==="mesh"||this.layer.geometryType==null)){for(const e of r)if(d(e.geometry)&&e.geometry.type==="mesh"&&!e.geometry.loaded)return he.ASYNC}return this.effectiveUpdatePolicy}_addImmediate(r){this._geometryWarningLogged=!1,this._symbolWarningLogged=!1;for(const e of r)this._addGraphic(e,this._getRenderingInfo(e,Ce),he.SYNC);this._cleanupSymbols(),this._labeler&&(this.owner.view.labeler.setDirty(),this._cleanupSymbols()),this.owner.view.deconflictor.setDirty()}_addDelayed(r){var e;for(const t of r){const i=t.uid;let s=this._pendingUpdates.get(i);s?s.add?s.state!==Q.NEW&&((e=s.abortController)==null||e.abort()):this._pendingAdds++:(s=this._pendingUpdatesPool.pushNew(),this._pendingAdds++,this._pendingUpdates.set(i,s)),s.add=t}this.notifyChange("running"),this.notifyChange("updatingRemaining")}remove(r){this.effectiveUpdatePolicy===he.ASYNC?this._removeDelayed(r):this._removeImmediate(r),this.notifyChange("updating")}_removeImmediate(r){for(const e of r)this._removeGraphic(e);this._cleanupSymbols(),this._labeler&&this.owner.view.labeler.setDirty(),this.owner.view.deconflictor.setDirty()}_removeDelayed(r){var e;for(const t of r){const i=t.uid,s=this._pendingUpdates.get(i);if(s)s.add&&(s.remove?s.add=null:this._pendingUpdates.delete(i),s.state===Q.LOADING&&((e=s.abortController)==null||e.abort()),this._pendingAdds--);else{const a=this._pendingUpdatesPool.pushNew();a.remove=t,this._pendingUpdates.set(i,a),this._pendingRemoves++,this._applyPendingRemovesFirst=!0}}this._pendingUpdates.size===0&&this._finishPendingUpdates(),this.notifyChange("running"),this.notifyChange("updatingRemaining")}_finishPendingUpdates(){this._pendingUpdatesPool.clear(),this._cleanupSymbols(),(this._pendingAdds||this._pendingRemoves)&&Ce.warn("pendingAdds/Removes in inconsistent state!"),this._pendingAdds=0,this._pendingRemoves=0,this._applyPendingRemovesFirst=!1}_applyPendingUpdates(r){var e;if(this._geometryWarningLogged=!1,this._symbolWarningLogged=!1,this._pendingUpdates.size===0&&((e=this._spatialIndex)==null?void 0:e.updating))return this._spatialIndex.update(),void r.madeProgress();if(this._applyPendingRemovesFirst){this._applyPendingRemovesFirst=!1;for(const[t,i]of this._pendingUpdates){if(r.done){this._applyPendingRemovesFirst=!0;break}if(i.remove&&!i.add&&(this._pendingRemoves--,r.madeProgress(),this._removeGraphic(i.remove),i.remove=null,this._pendingUpdates.delete(t),this._pendingRemoves===0))break}}for(const[t,i]of this._pendingUpdates){if(r.done)break;i.add&&i.state===Q.NEW&&this._processPendingUpdateNew(i);let s=this.effectiveUpdatePolicy;if(!i.remove||i.add&&i.state!==Q.READY||(this._pendingRemoves--,r.madeProgress(),this._removeGraphic(i.remove),i.remove=null,s=he.SYNC),i.add)switch(i.state){case Q.READY:this._addGraphic(i.add,i.renderingInfo,s),i.add=null,this._pendingAdds--,r.madeProgress();break;case Q.REJECTED:i.add=null,this._pendingAdds--;case Q.LOADING:}i.remove==null&&i.add==null&&this._pendingUpdates.delete(t)}this._pendingUpdates.size===0&&(this._finishPendingUpdates(),this.notifyChange("running"))}_processPendingUpdateNew(r){if(!r.add)return void(r.state=Q.READY);const e=r.add.geometry;d(e)&&e.type==="mesh"&&!e.loaded?this._processPendingUpdateNewMesh(r,e):this._processPendingUpdateNewRenderingInfo(r)}async _processPendingUpdateNewMesh(r,e){r.state=Q.LOADING,r.abortController=new AbortController;const t=r.abortController.signal;try{await e.load({signal:t})}catch(i){return this._processPendingUpdateNewError(r,i)}r.abortController=null,this._processPendingUpdateNewRenderingInfo(r)}_processPendingUpdateNewError(r,e){r.abortController=null,Xi(e)?r.state=Q.NEW:r.state=Q.REJECTED}async _processPendingUpdateNewRenderingInfo(r){if(b(this.layer.renderer)||this.layer.renderer.type!=="dictionary")return r.renderingInfo=this._getRenderingInfo(r.add,Ce),void(r.state=Q.READY);r.state=Q.LOADING,r.abortController=new AbortController;let e=null;try{e=await this._getRenderingInfoAsync(r.add,{signal:r.abortController.signal})}catch(t){return r.abortController=null,void(Xi(t)?r.state=Q.NEW:r.state=Q.REJECTED)}b(e)||b(e.symbol)?(Ce&&!this._symbolWarningLogged&&(this._symbolWarningLogged=!0,Ce.warn(`Graphic in layer ${this.layer.id} has no symbol and will not render`)),r.renderingInfo=null):r.renderingInfo=e,r.state=Q.READY}_addGraphic(r,e,t){if(this._graphicsWithoutSymbol.set(r.uid,r),b(e)||b(e.symbol)||!iu(r))return;d(this.stage.renderView.objectAndLayerIdRenderHelper)&&this.setUidToIdOnAdd&&this.stage.renderView.objectAndLayerIdRenderHelper.setUidToObjectAndLayerId(r.objectId,r.uid,this.layer.id,this.layer.uid,!!this.layer.popupEnabled);const i=e.symbol,s=this.getOrCreateGraphics3DSymbol(i,e.renderer);if(b(s))return;this._expandComputedExtent(r.geometry);const a=this._beginGraphicUpdate(r),n=new wg(r,e,this.layer);let o=!1;const l=u=>{u===s.symbol.id&&(o=!0)};this._whenSymbolRemoved.push(l);const h=()=>{if(--this._loadingSymbols,!this.destroyed){if(this._whenSymbolRemoved.removeUnordered(l),this._graphicsWaitingForSymbol.get(r.uid)!==a||o||s.destroyed||this.graphicSymbolSupported&&r.symbol&&r.symbol.id!==s.symbol.id)--s.referenced,this._cleanupSymbols();else{const u=this._createGraphics3DGraphic(s,n);this._spatialIndex&&d(u)&&this._spatialIndex.add(u),--s.referenced,this._endGraphicUpdate(r)}this._featureStore.events.emit("changed"),this._labeler&&this.owner.view.labeler.setDirty()}},c=u=>{--this._loadingSymbols,this.destroyed||(this._whenSymbolRemoved.removeUnordered(l),o||(Xi(u)?this.add([r]):s.destroyed||this._endGraphicUpdate(r)))};++this._loadingSymbols,t===he.ASYNC?s.load(()=>this._frameTask.schedule(h),u=>this._frameTask.schedule(()=>c(u))):s.load(h,c)}_removeGraphic(r){var i,s;const e=r.uid,t=this.graphics3DGraphics.get(e);if(t){t.graphics3DSymbol.onRemoveGraphic(t);const a=t.usedMemory,n=t.isElevationSource;this._conditionalRemove(t,e),(i=this._spatialIndex)==null||i.remove(t);const o=t.graphics3DSymbol.symbol.id;(s=this._graphicsBySymbol.get(o))==null||s.delete(e),this._graphicsWithoutSymbol.delete(e),this._removeGraphics3DGraphic(e,a,n),t.destroy(),this._featureStore.events.emit("changed")}else this._graphicsWithoutSymbol.delete(e),this._graphicsWaitingForSymbol.delete(e),this._graphicsWaitingForSymbol.size===0&&(this._cleanupSymbols(),this.notifyChange("updating"))}_hasLabelingContext(r){if(r instanceof $n||r instanceof kd){const e=this.symbolCreationContext.layer;return!!e.labelingInfo&&e.labelingInfo.some(t=>t.symbol===r)}return!1}_hasValidSymbolCreationContext(r){return!(r instanceof $n&&!this._hasLabelingContext(r))||(Ce.error("LabelSymbol3D is only valid as part of a LabelClass. Using LabelSymbol3D as a renderer symbol is not supported."),!1)}_getRenderingInfo(r,e){const t=r.geometry;if(b(t))return e&&!this._geometryWarningLogged&&(this._geometryWarningLogged=!0,e.warn(`Graphic in layer ${this.layer.id} has no geometry and will not render`)),null;if(!Vl(t.spatialReference,this._viewSpatialReference))return e&&!this._geometryWarningLogged&&(this._geometryWarningLogged=!0,e.warn(`Graphic in layer ${this.layer.id} has incompatible spatial reference and will not render`)),null;if(!this.graphicSymbolSupported&&d(r.symbol))return e&&!this._symbolWarningLogged&&(this._symbolWarningLogged=!0,e.warn(`Graphic in layer ${this.layer.id} is not allowed to have a symbol, use a renderer instead`)),null;const i=this.rendererHasGeometryOperations?Kr(r,this.layer):r;let s;return this.owner.getRenderingInfo&&(this.getRenderingInfoWithoutRenderer||d(this.currentRenderer))?s=this.owner.getRenderingInfo(i,this.currentRenderer,this._arcadeOnDemand):s={symbol:i.symbol||Cg(i.geometry)},b(s)||b(s.symbol)?(e&&!this._symbolWarningLogged&&(this._symbolWarningLogged=!0,e.warn(`Graphic in layer ${this.layer.id} has no symbol and will not render`)),null):s}_getRenderingInfoAsync(r,e){const t=r.geometry;if(b(t))return Ce&&!this._geometryWarningLogged&&(this._geometryWarningLogged=!0,Ce.warn(`Graphic in layer ${this.layer.id} has no geometry and will not render`)),null;if(!this.graphicSymbolSupported&&d(r.symbol))return Ce&&!this._symbolWarningLogged&&(this._symbolWarningLogged=!0,Ce.warn(`Graphic in layer ${this.layer.id} is not allowed to have a symbol, use a renderer instead`)),null;const i=this.rendererHasGeometryOperations?Kr(r,this.layer):r;return this.owner.getRenderingInfoAsync(i,this.currentRenderer,this._arcadeOnDemand,e)}_createGraphics3DSymbol(r,e){if(!this._hasValidSymbolCreationContext(r))return null;const t=this._getConvertedSymbol(r);if(!t)return null;let i;if(d(e)&&"backgroundFillSymbol"in e&&e.backgroundFillSymbol){const a=Tn(e.backgroundFillSymbol,{ignoreDrivers:!0});d(a.symbol)&&a.symbol.type!=="web-style"&&a.symbol.type!=="cim"&&(i=a.symbol.symbolLayers)}const s=Xy(t,this.symbolCreationContext,i);return s.load(()=>{const a=s.extentPadding;a>this.extentPadding&&this._set("extentPadding",a),this.notifyChange("averageSymbolComplexity")},()=>{}),s}getOrCreateGraphics3DSymbol(r,e){let t=this._symbols.get(r.id);return t===void 0&&(t=r instanceof Hd?new Jy(r,i=>this._frameTask.schedule(i),i=>this._createGraphics3DSymbol(i,e)):this._createGraphics3DSymbol(r,e),this._symbols.set(r.id,t)),d(t)&&++t.referenced,t}trackGraphicState(r){return b(this._graphicStateTracking)&&(this._graphicStateTracking=new Qy(this)),this._graphicStateTracking.add(r)}_addGraphics3DGraphic(r){this._usedMemory+=r.usedMemory,this.graphics3DGraphics.set(r.graphic.uid,r),this._numberOfGraphics++,r.isElevationSource&&(this._numberOfGraphicsProvidingElevation++,this.updateStageLayerElevationProvider()),this._updateLayerVisibility()}_removeGraphics3DGraphic(r,e,t=!1){this._usedMemory-=e,this.graphics3DGraphics.delete(r),this._numberOfGraphics--,t&&(this._numberOfGraphicsProvidingElevation--,this.updateStageLayerElevationProvider()),this._updateLayerVisibility()}_createGraphics3DGraphic(r,e){var n,o,l;const t=e.graphic;if(this._graphicsWithoutSymbol.delete(t.uid),!this._symbols.has(r.symbol.id))return this.add([t]),null;if(this.graphics3DGraphics.has(t.uid))return null;const i=r.createGraphics3DGraphic(e);if(b(i))return null;this._addGraphics3DGraphic(i);const s=r.symbol.id;if(this._graphicsBySymbol.has(s)||this._graphicsBySymbol.set(s,new Map),this._graphicsBySymbol.get(s).set(t.uid,i),i.isDraped&&this._graphicsDrapedUids.add(t.uid),i.centroid=null,d(t.geometry)&&t.geometry.type!=="point"&&(i.centroid=fs(t.geometry,this._viewSpatialReference)),this._updateUserVisibility(i),d(this._scaleVisibility)&&this._scaleVisibility.updateVisibility(i),d(this._filterVisibility)){const{defaultVisibility:h}=this._filterVisibility;i.setVisibilityFlag(ze.FILTER,h,ye.GRAPHIC),h||this._filterVisibility.reapply()}(n=this._deconflictor)==null||n.addGraphic(i),(o=this._labeler)==null||o.addGraphic(i),(l=this._objectStates)==null||l.addGraphic(i),this._deconflictor&&this.owner.view.deconflictor.setInitialIconVisibilityFlag(this,i),i.initialize(this.stage,this.stageLayer,this.owner),d(this._graphicStateTracking)&&this._graphicStateTracking.addGraphic(i);const a=this._whenGraphics3DGraphicRequests[t.uid];return a&&(delete this._whenGraphics3DGraphicRequests[t.uid],a.resolve(i)),i}_abortRendererChange(){this._rendererChangeAbortController&&(this._rendererChangeAbortController.abort(),this._rendererChangeAbortController=null)}async rendererChange(r){if(this._abortRendererChange(),r!==this.currentRenderer)if(this._validateRenderer(r),b(r)&&this._currentRendererChange(null,!1),Bu(r))if(d(r)&&r.arcadeRequired){const e=new AbortController;this._rendererChangeAbortController=e;const{arcadeUtils:t}=await this._ensureArcade();Oe(e);const i=t.hasGeometryOperations(r);i&&(await t.enableGeometryOperations(),Oe(e)),this.effectiveUpdatePolicy===he.ASYNC?await this._frameTask.schedule(()=>this._currentRendererChange(r,i),e.signal):this._currentRendererChange(r,i),this._rendererChangeAbortController=null}else if(this.effectiveUpdatePolicy===he.ASYNC){const e=new AbortController;this._rendererChangeAbortController=e,await this._frameTask.schedule(()=>this._currentRendererChange(r,!1),e.signal),this._rendererChangeAbortController=null}else this._currentRendererChange(r,!1);else this._currentRendererChange(r,!1)}async _ensureArcade(){return b(this._arcadeOnDemand)?(this._arcadeOnDemand=await jl(),this._arcadeOnDemand):this._arcadeOnDemand}_currentRendererChange(r,e){this.currentRenderer=r,this.rendererHasGeometryOperations=e,this.symbolCreationContext.arcade=se(this._arcadeOnDemand);const t=this.symbolCreationContext.renderer;if(r===t)return;if(this._symbolConversionCache.clear(),b(r))return this.symbolCreationContext.renderer=null,void this.recreateAllGraphicsAndSymbols();const i=ma(t,r);this._updateUnchangedSymbolMappings(i,r,t),this.symbolCreationContext.renderer=r,b(i)||(i.type==="complete"?this.recreateAllGraphicsAndSymbols():i.type==="partial"&&(this._applyRendererDiff(i,r,t)?this._volatileGraphicsUpdated():this.recreateAllGraphicsAndSymbols()),this.notifyChange("averageSymbolComplexity"))}_diffHasSymbolChange(r){for(const e in r.diff)switch(e){case"visualVariables":case"defaultSymbol":case"uniqueValueInfos":break;case"uniqueValueGroups":case"authoringInfo":case"fieldDelimiter":delete r.diff[e];break;default:return!0}return!1}_applySymbolSetDiff(r,e,t){r=r||[],e=e||[];const i=[];for(const s of e){const a=this._graphicsBySymbol.get(s.id);a&&a.forEach((n,o)=>{const l=n.graphic,h=this.layer instanceof kl?this.layer:null,c=se(this._arcadeOnDemand);if(s===t.defaultSymbol&&t.getSymbol(Kr(l,h),{arcade:c})===t.defaultSymbol)return;const u=n.usedMemory;r.length||t.defaultSymbol?i.push(l):this._graphicsWithoutSymbol.set(o,l);const p=this.graphics3DGraphics.get(o);this._conditionalRemove(p,o),n.destroy(),a.delete(o),this._removeGraphics3DGraphic(o,u),this._updateLayerVisibility()}),this._whenSymbolRemoved.forAll(n=>n(s.id))}(r.length||i.length)&&(this._graphicsWithoutSymbol.forEach(s=>i.push(s)),this._graphicsWithoutSymbol.clear(),this.add(i)),this._cleanupSymbols(),this._labeler&&this.owner.view.labeler.setDirty(),this.owner.view.deconflictor.setDirty()}_applyUniqueValueRendererDiff(r,e,t){const i=r.diff.defaultSymbol,s=r.diff.uniqueValueInfos;if(i||s){const a=s?s.added.map(o=>o.symbol).filter(d):[],n=s?s.removed.map(o=>o.symbol).filter(d):[];if(s)for(let o=0;o<s.changed.length;o++)a.push(s.changed[o].newValue.symbol),n.push(s.changed[o].oldValue.symbol);return i?(t.defaultSymbol&&n.push(t.defaultSymbol),e.defaultSymbol&&a.push(e.defaultSymbol)):t.defaultSymbol&&a.length&&n.push(e.defaultSymbol),this._applySymbolSetDiff(a,n,e),delete r.diff.defaultSymbol,delete r.diff.uniqueValueInfos,!0}return!1}_calculateUnchangedSymbolMapping(r,e,t){var l;if((e==null?void 0:e.type)!=="unique-value"||(t==null?void 0:t.type)!=="unique-value"||d(r)&&r.type!=="partial")return[];const i=h=>d(h)?h.id:null,s=r&&r.diff,a=s&&s.defaultSymbol,n=s&&s.uniqueValueInfos;let o;if(n)o=n.unchanged.map(h=>({oldId:i(h.oldValue.symbol),newId:i(h.newValue.symbol)}));else{o=[];for(const h of t.uniqueValueInfos??[]){const c=i(h.symbol),u=(l=e.uniqueValueInfos)==null?void 0:l.find(p=>p.value===h.value);u&&c!==i(u.symbol)&&o.push({oldId:c,newId:i(u.symbol)})}}return!a&&t.defaultSymbol&&o.push({oldId:i(t.defaultSymbol),newId:i(e.defaultSymbol)}),o}_updateSymbolMapping(r,e){const t=d(e)&&e?typeof e=="string"?e:e.id:null;if(b(r)||r===t)return;const i=this._graphicsBySymbol.get(r);this._graphicsBySymbol.delete(r),i!==void 0&&this._graphicsBySymbol.set(t,i);const s=this._symbols.get(r);if(s!==void 0&&(this._symbols.delete(r),this._symbols.set(t,s),d(s))){const a=typeof e=="string"?null:e;d(a)?s.symbol=a:s.symbol.id=t}}_updateUnchangedSymbolMappings(r,e,t){const i=this._calculateUnchangedSymbolMapping(r,e,t);for(const{oldId:s,newId:a}of i)this._updateSymbolMapping(s,a)}_applyRendererDiff(r,e,t){if(this._diffHasSymbolChange(r))return!1;if(e instanceof Gn&&t instanceof Gn&&this._applyUniqueValueRendererDiff(r,e,t)&&Object.keys(r.diff).length===0)return!0;for(const[i]of this._graphicsBySymbol){const s=this._symbols.get(i);if(d(s))switch(s.applyRendererDiff(r,e)){case H.Recreate_Symbol:this._recreateSymbol(i);break;case H.Recreate_Graphics:this._recreateGraphicsForSymbol(i);case H.Fast_Update:}}return!0}opacityChange(){this.forEachGraphics3DSymbol((r,e)=>r.globalPropertyChanged("opacity",e)),this._updateStageLayerVisibility()}_slicePlaneEnabledChange(r){r!==this.symbolCreationContext.slicePlaneEnabled&&(this.symbolCreationContext.slicePlaneEnabled=r,this.stageLayer.sliceable=r,this.forEachGraphics3DSymbol((e,t)=>e.globalPropertyChanged("slicePlaneEnabled",t)),this._deconflictor&&this._deconflictor.slicePlaneEnabledChange(),this._labeler&&this._labeler.slicePlaneEnabledChange())}_physicalBasedRenderingChange(r){this.symbolCreationContext.physicalBasedRenderingEnabled=r,this.forEachGraphics3DSymbol((e,t,i)=>{e.globalPropertyChanged("physicalBasedRenderingEnabled",t)||this._recreateSymbol(i)})}_skipHighSymbolLoDsChange(r){this.symbolCreationContext.skipHighSymbolLods=r,this.forEachGraphics3DSymbol((e,t,i)=>{e.globalPropertyChanged("skipHighSymbolLods",t)||this._recreateSymbol(i)})}_pixelRatioChange(){this.forEachGraphics3DSymbol((r,e,t)=>{r.globalPropertyChanged("pixelRatio",e)||this._recreateSymbol(t)})}_signalUpdatingDuringAsyncLoadedGraphicsChange(){this._updatingPendingLoadedGraphicsChange&&this._updatingPendingLoadedGraphicsChange.remove(),this._updatingPendingLoadedGraphicsChange=$c(()=>{this._updatingPendingLoadedGraphicsChange=null})}setClippingExtent(r,e){const t=this.symbolCreationContext.clippingExtent,i=pt();return e_(r,i,e)?this.symbolCreationContext.clippingExtent=Bl(_e(),i):this.symbolCreationContext.clippingExtent=null,!Wd(this.symbolCreationContext.clippingExtent,t)}modifyGraphics3DGraphicVisibilities(r){var t;let e=!1;this.graphics3DGraphics.forEach(i=>{r(i)&&(e=!0)}),e&&((t=this.owner.view.labeler)==null||t.setDirty(),this.owner.view.deconflictor.setDirty())}forEachGraphics3DSymbol(r){for(const[e,t]of this._symbols){if(b(t))return;r(t,this._graphicsBySymbol.get(e)||s_,e)}}updateAllGraphicsVisibility(){d(this._filterVisibility)&&this._filterVisibility.reapply(),this.modifyGraphics3DGraphicVisibilities(r=>{const e=this._updateUserVisibility(r),t=d(this._scaleVisibility)&&this._scaleVisibility.updateVisibility(r);return e||t})}_hideAllGraphics(){this.modifyGraphics3DGraphicVisibilities(r=>r.setVisibilityFlag(ze.USER_SETTING,!1,ye.GRAPHIC))}_validateRenderer(r){var t;const e=ju(r,{geometryType:(t=this.layer)==null?void 0:t.geometryType});if(e){const i=`Renderer for layer '${this.layer.title?`${this.layer.title}, `:""}, id:${this.layer.id}' is not supported in a SceneView`;Ce.warn(i,e.message)}}_volatileGraphicsUpdated(){var r;(r=this._labeler)==null||r.reset(),this.stageLayer.shaderTransformationChanged(),this.notifyChange("updating")}_cleanupSymbols(){if(this._graphicsWaitingForSymbol.size>0||this._suspendSymbolCleanup)return;let r=!1;this._symbols.forEach((e,t)=>{if(b(e)||e.referenced>0)return;const i=this._graphicsBySymbol.get(t);i&&i.size!==0||(this._graphicsBySymbol.delete(t),this._symbols.delete(t),K(e),r=!0)}),r&&(this._recomputeExtentPadding(),this.notifyChange("averageSymbolComplexity"))}get test(){return{snapshotInternals:()=>({graphics:[...this.graphics3DGraphics.keys()].sort(),symbols:[...this._symbols.keys()].sort(),graphicsBySymbol:[...this._graphicsBySymbol.keys()].sort().map(r=>({symbolId:r,graphics:[...this._graphicsBySymbol.get(r).keys()].sort()})),graphicsWithoutSymbol:[...this._graphicsWithoutSymbol.keys()].sort(),graphicsDrapedUids:[...this._graphicsDrapedUids].sort(),pendingUpdates:this._pendingUpdates}),symbols:this._symbols,filterVisibility:this._filterVisibility,numPending:this._pendingUpdates.size,forceUpdatePolicy:r=>{this._forcedUpdatePolicy=r}}}get performanceInfo(){return{visible:this.graphics3DGraphics.size,missing:this._graphicsWithoutSymbol.size,pending:this._pendingUpdates.size}}};var Q;I.tmpVec=C(),y([v({readOnly:!0})],I.prototype,"computedExtent",void 0),y([v()],I.prototype,"currentRenderer",void 0),y([v()],I.prototype,"rendererHasGeometryOperations",void 0),y([v()],I.prototype,"_frameTask",void 0),y([v({readOnly:!0})],I.prototype,"_viewSpatialReference",null),y([v()],I.prototype,"_rendererChangeAbortController",void 0),y([v()],I.prototype,"_elevationInfoChangeAbortController",void 0),y([v()],I.prototype,"_initializeAbortController",void 0),y([v()],I.prototype,"_elevationAlignment",void 0),y([v()],I.prototype,"_scaleVisibility",void 0),y([v()],I.prototype,"_filterVisibility",void 0),y([v()],I.prototype,"_initializePromise",void 0),y([v()],I.prototype,"_spatialIndex",void 0),y([v({readOnly:!0})],I.prototype,"extentPadding",void 0),y([v()],I.prototype,"_updatingPendingLoadedGraphicsChange",void 0),y([v()],I.prototype,"_featureStore",void 0),y([v()],I.prototype,"_deconflictor",void 0),y([v()],I.prototype,"_labeler",void 0),y([v()],I.prototype,"_objectStates",void 0),y([v()],I.prototype,"_loadingSymbols",void 0),y([v()],I.prototype,"preferredUpdatePolicy",void 0),y([v()],I.prototype,"_forcedUpdatePolicy",void 0),y([v({readOnly:!0})],I.prototype,"effectiveUpdatePolicy",null),y([v({constructOnly:!0})],I.prototype,"elevationFeatureExpressionEnabled",void 0),y([v({constructOnly:!0})],I.prototype,"owner",void 0),y([v({constructOnly:!0})],I.prototype,"layer",void 0),y([v({constructOnly:!0})],I.prototype,"graphicSymbolSupported",void 0),y([v({constructOnly:!0})],I.prototype,"getRenderingInfoWithoutRenderer",void 0),y([v({constructOnly:!0})],I.prototype,"componentFactories",void 0),y([v({constructOnly:!0})],I.prototype,"setUidToIdOnAdd",void 0),y([v()],I.prototype,"featureStore",null),y([v()],I.prototype,"initializePromise",null),y([v()],I.prototype,"scaleVisibility",null),y([v()],I.prototype,"elevationAlignment",null),y([v()],I.prototype,"objectStates",null),y([v()],I.prototype,"filterVisibility",null),y([v({readOnly:!0})],I.prototype,"updating",null),y([v({readOnly:!0})],I.prototype,"running",null),y([v({readOnly:!0})],I.prototype,"suspendedOrOutsideOfView",null),y([v({readOnly:!0,dependsOn:[]})],I.prototype,"updatingRemaining",null),y([v({readOnly:!0,dependsOn:["owner.view.qualitySettings.graphics3D.maxTotalNumberOfPrimitives","owner.view.qualitySettings.graphics3D.maxTotalNumberOfFeatures","averageSymbolComplexity"]})],I.prototype,"displayFeatureLimit",null),y([v({readOnly:!0,dependsOn:[]})],I.prototype,"averageSymbolComplexity",null),y([v({constructOnly:!0})],I.prototype,"hasZ",void 0),y([v({constructOnly:!0})],I.prototype,"hasM",void 0),y([v()],I.prototype,"_objectIdField",null),I=Ma=y([Xe(bc)],I),function(r){r[r.NEW=0]="NEW",r[r.LOADING=1]="LOADING",r[r.READY=2]="READY",r[r.REJECTED=3]="REJECTED"}(Q||(Q={}));class i_{constructor(){this.add=null,this.renderingInfo=null,this.state=Q.NEW,this.abortController=null,this.remove=null}clear(){this.add=null,this.renderingInfo=null,this.state=Q.NEW,this.abortController=null,this.remove=null}}const r_=10,lt=C(),Ri=C(),s_=new Map,a_=.05;let n_=class{constructor(){this._extents=new ca({allocator:e=>e||pt()}),this._tmpExtent=pt(),this._dirty=!1}get empty(){return this._extents.length===0}get size(){return this._extents.length}clear(){this._extents.clear()}add(e){this._contains(e)||(this._removeContained(e),Mn(this._extents.pushNew(),e),this._dirty=!0)}pop(){return this._dirty&&this._mergeTight(),this._extents.pop()}merge(e){return this._mergeTight(e),e.hasProgressed}_mergeTight(e=qa){const t=this._extents,i=new Set;let s=0;for(;s!==t.length;){t.sort((a,n)=>a[0]-n[0]),s=t.length,i.clear();for(let a=0;a<t.length;++a){if(e.done)return;const n=t.getItemAt(a);if(n){for(let o=a+1;o<t.length;++o){const l=t.getItemAt(o);if(l==null||l[0]>=n[2])break;i.add(l)}i.forEach(o=>{if(n===o)return;if(o[2]<=n[0])return void i.delete(o);const l=zs(n),h=zs(o),c=this._tmpExtent;qd(n,o,c);const u=l+h;(zs(c)-u)/u<a_&&(Mn(n,c),i.delete(o),t.remove(o),e.madeProgress())}),i.add(n)}}}this._dirty=!1}_contains(e){return this._extents.some(t=>zn(t,e))}_removeContained(e){this._extents.filterInPlace(t=>!zn(e,t))}get test(){const e=this;return{containsPoint:t=>e._extents.some(i=>Yd(i,t))}}},vt=class extends Ht{constructor(e){super(e),this._dirtyExtents=new n_,this._globalDirty=!1,this._averageExtentUpdateSize=0,this._dirtyGraphicsSet=new Set,this._handles=new us,this._updateElevation=!1,this.graphicsCoreOwner=null,this.graphicsCore=null,this.events=new gs}initialize(){const e=this.elevationProvider,t=this.graphicsCoreOwner.view.resourceController.scheduler;this._handles.add([e.on("elevation-change",i=>this._elevationChanged(i)),$e(()=>this.graphicsCoreOwner.suspended,()=>this._suspendedChange()),t.registerTask(Or.ELEVATION_ALIGNMENT,this)])}destroy(){this._dirtyGraphicsSet.clear(),this._handles=K(this._handles),this.graphicsCoreOwner=null,this.graphicsCore=null,this.queryGraphicUIDsInExtent=null,this.elevationProvider=null}clear(){this._dirtyGraphicsSet.clear(),this.notifyChange("updating")}_suspendedChange(){this.graphicsCoreOwner.suspended===!0?this._updateElevation=!1:this.graphicsCoreOwner.suspended===!1&&this._updateElevation&&(this._globalDirty=!0,this.notifyChange("updating"))}elevationInfoChange(){this._globalDirty=!0,this.notifyChange("updating")}get updating(){return this.running}get running(){return this._dirtyGraphicsSet.size>0||this._dirtyExtents&&!this._dirtyExtents.empty||this._globalDirty}get updatingRemaining(){return this._dirtyGraphicsSet.size+this._dirtyExtents.size*this._averageExtentUpdateSize}runTask(e){for(this._globalDirty&&(this._markAllGraphicsElevationDirty(),this._globalDirty=!1,e.madeProgress()),e.run(()=>this._dirtyExtents.merge(e));this.running&&!e.done;)this._updateDirtyGraphics(e),this._updateDirtyExtents(e);this.notifyChange("updating")}_updateDirtyGraphics(e){const t=this.graphicsCoreOwner.view.renderCoordsHelper,i=this.graphicsCore.effectiveUpdatePolicy===he.ASYNC;for(const s of this._dirtyGraphicsSet.keys()){const a=this.graphicsCore.getGraphics3DGraphicById(s);if(this._dirtyGraphicsSet.delete(s),d(a)&&(a.alignWithElevation(this.elevationProvider,t,i),this.graphicsCoreOwner.view.deconflictor.setDirty(),e.madeProgress()),e.done)return}}_updateDirtyExtents(e){for(;!this._dirtyExtents.empty&&!e.done;){const t=this._dirtyExtents.pop(),i=this.elevationProvider.spatialReference;this.events.emit("invalidate-elevation",{extent:t,spatialReference:i});const s=this._dirtyGraphicsSet.size;this.queryGraphicUIDsInExtent(t,i,a=>{const n=this.graphicsCore.getGraphics3DGraphicById(a);d(n)&&n.needsElevationUpdates()&&this._dirtyGraphicsSet.add(a)}),this._averageExtentUpdateSize=.1*(this._dirtyGraphicsSet.size-s)+.9*this._averageExtentUpdateSize,e.madeProgress()}}_markAllGraphicsElevationDirty(){this._dirtyExtents.clear(),this._dirtyGraphicsSet.clear(),this.graphicsCore.graphics3DGraphics.forEach((e,t)=>this._dirtyGraphicsSet.add(t))}_elevationChanged(e){if(e.context==="scene"&&(!this.graphicsCore.layer.elevationInfo||this.graphicsCore.layer.elevationInfo.mode!=="relative-to-scene"))return;const{extent:t,spatialReference:i}=e;if(this.graphicsCoreOwner.suspended){if(!this._updateElevation){const s=this.graphicsCore.computedExtent;s&&t[2]>s.xmin&&t[0]<s.xmax&&t[3]>s.ymin&&t[1]<s.ymax&&(this._updateElevation=!0)}this.events.emit("invalidate-elevation",{extent:t,spatialReference:i})}else t[0]===-1/0?this._globalDirty=!0:this._dirtyExtents.add(t),this.notifyChange("updating")}};y([v()],vt.prototype,"graphicsCoreOwner",void 0),y([v()],vt.prototype,"graphicsCore",void 0),y([v()],vt.prototype,"queryGraphicUIDsInExtent",void 0),y([v()],vt.prototype,"elevationProvider",void 0),y([v({readOnly:!0})],vt.prototype,"updating",null),y([v({readOnly:!0})],vt.prototype,"updatingRemaining",null),vt=y([Xe("esri.views.3d.layers.graphics.Graphics3DElevationAlignment")],vt);const o_=vt;function l_(r,e,t,i){return u_(r,e,t,c_(i,e,t,!0))}const h_={dir:C(),len:0,clip:be()};function c_(r,e,t,i){const s=h_;return r?(t&&i&&(s.len=Vn(e,t)),de(s.dir,r)):(s.len=Vn(e,t),oe(s.dir,t,e),ae(s.dir,s.dir,1/s.len)),s}function d_(r,e,t){const i=k(fh(r),t.dir),s=-wa(r,e);if(s<0&&i>=0)return!1;if(i>-1e-6&&i<1e-6)return s>0;if((s<0||i<0)&&!(s<0&&i<0))return!0;const a=s/i;return i>0?a<t.clip[1]&&(t.clip[1]=a):a>t.clip[0]&&(t.clip[0]=a),t.clip[0]<=t.clip[1]}function u_(r,e,t,i){i.clip[0]=0,i.clip[1]=t?i.len:Number.MAX_VALUE;for(let s=0;s<r.length;s++)if(!d_(r[s],e,i))return!1;return!0}const vc=.5*Math.PI,p_=vc/Math.PI*180;class g_{constructor(e){this._renderCoordsHelper=e.renderCoordsHelper,this._extent=new Array(4),this._planes=new Array(6),this._maxSpan=0,this._center={origin:C(),direction:C()};for(let t=0;t<4;t++)this._extent[t]={origin:C(),direction:C(),cap:{next:null,direction:C()}},this._planes[t]=sr();this._planes[Qe.NEAR]=sr(),this._planes[Qe.FAR]=sr(),this._planesWithoutFar=this._planes.slice(0,5)}update(e,t,i,s=!0){const a=this._extent;this._toRenderBoundingExtent(e,t,i),me(this._center.origin,a[0].origin,a[2].origin),ae(this._center.origin,this._center.origin,.5),this._renderCoordsHelper.worldUpAtPosition(this._center.origin,this._center.direction),s||ae(this._center.direction,this._center.direction,-1);for(let n=0;n<4;n++){const o=a[n];this._renderCoordsHelper.worldUpAtPosition(o.origin,o.direction);const l=a[n===3?0:n+1];o.cap.next=l.origin,Zd(o.cap.direction,o.origin,l.origin),no(o.direction,o.cap.direction,o.origin,this._planes[n]),s||ae(o.direction,o.direction,-1)}no(a[0].cap.direction,a[1].cap.direction,a[0].origin,this._planes[Qe.NEAR]),s?oo(this._planes[Qe.NEAR],this._planes[Qe.FAR]):(tg(this._planes[Qe.FAR],this._planes[Qe.NEAR]),oo(this._planes[Qe.NEAR],this._planes[Qe.NEAR])),this._maxSpan=Math.max(Math.abs(e[0]-e[2]),Math.abs(e[1]-e[3])),this._maxSpanSpatialReference=t,this._minGlobalAltitude=.9*ts(this._maxSpanSpatialReference).radius}isVisibleInFrustum(e,t,i=!1){if(e==null)return!1;if(this._renderCoordsHelper.viewingMode===Ut.Global){const a=this._maxSpanSpatialReference.isGeographic?p_:vc*t;if(this._maxSpan>a)return!0;if(e.altitude!=null&&e.altitude>=this._minGlobalAltitude)return this._isVisibleInFrustumGlobal(e)}if(this._maxSpan===0){const a=this._extent[0];return!(i||!e.intersectsRay(xa(a.origin,a.direction)))}for(let a=0;a<this._extent.length;a++){const n=this._extent[a];if(!i&&e.intersectsRay(xa(n.origin,n.direction))||e.intersectsLineSegment(ng(n.origin,n.cap.next,f_),n.cap.direction))return!0}const s=i?this._planes:this._planesWithoutFar;for(let a=0;a<e.lines.length;a++){const n=e.lines[a];if(l_(s,n.origin,n.endpoint,n.direction))return!0}return!1}_toRenderBoundingExtentGlobal(e,t,i){Xd(e,et),et[2]=i,hi(t,et,ha,this._renderCoordsHelper.spatialReference),ja(ul,ha),B(De);for(const{x0:a,x1:n,y0:o,y1:l}of m_)for(let h=0;h<5;h++){const c=h/4;et[0]=Un(e[a],e[n],c),et[1]=Un(e[o],e[l],c),et[2]=i,ga(et,t,et,this._renderCoordsHelper.spatialReference),ge(et,et,ul),cr(De,et)}D(this._extent[0].origin,De[0],De[1],De[2]),D(this._extent[1].origin,De[3],De[1],De[2]),D(this._extent[2].origin,De[3],De[4],De[2]),D(this._extent[3].origin,De[0],De[4],De[2]);for(let a=0;a<4;++a)ge(this._extent[a].origin,this._extent[a].origin,ha)}_toRenderBoundingExtentLocal(e,t,i){rs(e,t,_t,this._renderCoordsHelper.spatialReference),D(this._extent[0].origin,_t[0],_t[1],i),D(this._extent[1].origin,_t[2],_t[1],i),D(this._extent[2].origin,_t[2],_t[3],i),D(this._extent[3].origin,_t[0],_t[3],i)}_toRenderBoundingExtent(e,t,i){switch(this._renderCoordsHelper.viewingMode){case Ut.Global:this._toRenderBoundingExtentGlobal(e,t,i);break;case Ut.Local:this._toRenderBoundingExtentLocal(e,t,i);break;default:is(this._renderCoordsHelper.viewingMode)}}_isVisibleInFrustumGlobal(e){if(wa(e.planes[Qe.NEAR],this._center.origin)<0&&k(this._center.direction,e.direction)<0)return!0;for(let t=0;t<4;t++){const i=this._extent[t];if(wa(e.planes[Qe.NEAR],i.origin)<0&&k(i.direction,e.direction)<0)return!0}return!1}}const m_=[{x0:0,y0:1,x1:2,y1:1},{x0:0,y0:3,x1:2,y1:3},{x0:0,y0:1,x1:0,y1:3},{x0:2,y0:1,x1:2,y1:3}],et=C(),ha=j(),ul=j(),De=_e(),_t=pt(),f_=ag(),y_=1.2;let Ti=class extends Ht{constructor(e){super(e),this.suspended=!1,this._extent=null,this._extentIntersectionDirty=!0,this._isVisibleBelowSurfaceInternal=!1,this._handles=new us,this.graphicsCoreOwner=null,this.updating=!0}initialize(){const{graphicsCoreOwner:e}=this;this._extentIntersection=new g_({renderCoordsHelper:e.view.renderCoordsHelper});const t=e.view,i=t.basemapTerrain,s=t.resourceController.scheduler;this._handles.add([t.on("resize",()=>this._viewChange()),$e(()=>t.state.camera,()=>this._viewChange(),wl),s.registerTask(Or.FRUSTUM_VISIBILITY,this),$e(()=>i.visibleElevationBounds,()=>this._elevationBoundsChange())]),t.viewingMode==="local"?this._isVisibleBelowSurface=!0:this._handles.add([$e(()=>{var a,n,o;return[i.baseOpacity,i.wireframe,(o=(n=(a=t.map)==null?void 0:a.ground)==null?void 0:n.navigationConstraint)==null?void 0:o.type]},()=>this._updateIsVisibleBelowSurface(),Uc)])}destroy(){this._set("graphicsCoreOwner",null),this._extent=null,this._extentIntersection=null,this._handles=K(this._handles)}_setDirty(){this.updating||this._set("updating",!0)}setExtent(e){this._extent=e,this._extentIntersectionDirty=!0,this._setDirty()}_viewChange(){this._setDirty()}_elevationBoundsChange(){this._setDirty(),this._extentIntersectionDirty=!0}set _isVisibleBelowSurface(e){this._isVisibleBelowSurfaceInternal=e,this._setDirty(),this._extentIntersectionDirty=!0}_updateIsVisibleBelowSurface(){var a,n;const e=this.graphicsCoreOwner.view,t=e.basemapTerrain,i=e.viewingMode==="local",s=((n=(a=e.map.ground)==null?void 0:a.navigationConstraint)==null?void 0:n.type)==="none";this._isVisibleBelowSurface=i||!t.opaque||s}_updateExtentIntersection(){if(!this._extentIntersectionDirty)return;this._extentIntersectionDirty=!1;const e=this.graphicsCoreOwner.view;let t;if(this._isVisibleBelowSurfaceInternal)t=-.3*ts(e.spatialReference).radius;else{const{min:i,max:s}=e.basemapTerrain.visibleElevationBounds;t=i-Math.max(1,(s-i)*(y_-1))}this._extentIntersection.update(this._extent,e.spatialReference,t)}get running(){return this.updating}runTask(e){if(this._set("updating",!1),!this._extent)return this._set("suspended",!1),Ya.YIELD;this._updateExtentIntersection();const t=this.graphicsCoreOwner.view.frustum,i=ts(this.graphicsCoreOwner.view.spatialReference).radius;this._set("suspended",!this._extentIntersection.isVisibleInFrustum(t,i)),e.madeProgress()}};y([v({readOnly:!0})],Ti.prototype,"suspended",void 0),y([v({constructOnly:!0})],Ti.prototype,"graphicsCoreOwner",void 0),y([v({readOnly:!0})],Ti.prototype,"updating",void 0),Ti=y([Xe("esri.views.3d.layers.graphics.Graphics3DFrustumVisibility")],Ti);const __=Ti;var ct;(function(r){r[r.Object=0]="Object",r[r.RenderGeometry=1]="RenderGeometry",r[r.External=2]="External",r[r.COUNT=3]="COUNT"})(ct||(ct={}));class b_{constructor(){this._items=[]}addObject(e,t){this._items.push({type:ct.Object,objectStateId:t,object:e})}addRenderGeometry(e,t,i){this._items.push({type:ct.RenderGeometry,objectStateId:t,renderGeometry:e,owner:i})}addExternal(e,t){this._items.push({type:ct.External,objectStateId:t,remove:e})}remove(e){for(let t=this._items.length-1;t>=0;--t){const i=this._items[t];i.objectStateId===e&&(this._removeObjectStateItem(i),this._items.splice(t,1))}}removeObject(e){for(let t=this._items.length-1;t>=0;--t){const i=this._items[t];i.type===ct.Object&&i.object===e&&(this._removeObjectStateItem(i),this._items.splice(t,1))}}removeRenderGeometry(e){for(let t=this._items.length-1;t>=0;--t){const i=this._items[t];i.type===ct.RenderGeometry&&i.renderGeometry===e&&(this._removeObjectStateItem(i),this._items.splice(t,1))}}removeAll(){this._items.forEach(e=>{this._removeObjectStateItem(e)}),this._items=[]}_removeObjectStateItem(e){switch(e.type){case ct.Object:e.objectStateId.channel===Ct.Highlight?e.object.removeHighlight(e.objectStateId):e.objectStateId.channel===Ct.MaskOccludee&&e.object.removeOcclude(e.objectStateId);break;case ct.RenderGeometry:e.owner.removeRenderGeometryObjectState(e.renderGeometry,e.objectStateId);break;case ct.External:e.remove(e.objectStateId)}}}class v_{constructor(e,t){this.stateType=e,this.objectIdField=t,this.objectStateSet=new b_,this.ids=new Set,this.paused=!1}hasGraphic(e){if(this.objectIdField){const t=e.graphic.attributes[this.objectIdField];return this.ids.has(t)}return this.ids.has(e.graphic.uid)}}let S_=class{constructor(e){this._graphicsCore=e,this._stateSets=new Array}destroy(){this.reset(),this._stateSets=null}reset(){this._stateSets&&(this._stateSets.forEach(e=>e.objectStateSet.removeAll()),this._stateSets.length=0)}acquireSet(e,t){const i=new v_(e,t);this._stateSets.push(i);const s=ds(()=>this.releaseSet(i));return{set:i,handle:s}}releaseSet(e){e.objectStateSet.removeAll();const t=this._stateSets?this._stateSets.indexOf(e):-1;t!==-1&&this._stateSets.splice(t,1)}_addObjectStateSet(e,t){e.addObjectStateSet(t.stateType,t.objectStateSet)}_removeObjectStateSet(e,t){e.removeObjectState(t.objectStateSet)}setUid(e,t){e.ids.add(t);const i=this._graphicsCore.graphics3DGraphics.get(t);i&&this._addObjectStateSet(i,e)}setUids(e,t){t.forEach(i=>this.setUid(e,i))}setObjectIds(e,t){t.forEach(i=>e.ids.add(i)),this._initializeSet(e)}addGraphic(e){this._stateSets.forEach(t=>{!t.paused&&t.hasGraphic(e)&&this._addObjectStateSet(e,t)})}removeGraphic(e){this._stateSets.forEach(t=>{t.hasGraphic(e)&&this._removeObjectStateSet(e,t)})}allGraphicsDeleted(){this._stateSets&&this._stateSets.forEach(e=>e.objectStateSet.removeAll())}_initializeSet(e){const t=this._graphicsCore.graphics3DGraphics;e.objectIdField?t.forEach(i=>{i&&e.hasGraphic(i)&&this._addObjectStateSet(i,e)}):e.ids.forEach(i=>{const s=t.get(i);s&&this._addObjectStateSet(s,e)})}get test(){return{states:this._stateSets}}};const pl=di.getLogger("esri.views.3d.layers.graphics.Graphics3DScaleVisibility");let He=class extends Hl{constructor(e){super(e),this._scaleRangeActive=!1,this._layerScaleRangeVisibilityQuery=!1,this._extent=null,this.graphicsCoreOwner=null,this.layer=null,this.queryGraphicUIDsInExtent=null,this.graphicsCore=null,this.basemapTerrain=null,this.layerScaleEnabled=!0,this.suspended=!1,this._dirty=!0}initialize(){this.updateScaleRangeActive();const e=this.graphicsCoreOwner.view.resourceController.scheduler;this.handles.add(e.registerTask(Or.SCALE_VISIBILITY,this)),this.updatingHandles.add(()=>this.layer.effectiveScaleRange,()=>this.layerMinMaxScaleChangeHandler())}destroy(){this.updatingHandles.removeAll(),this.handles.removeAll(),this._dirty=!1,this._extent=null,this.graphicsCoreOwner=null,this.layer=null,this.queryGraphicUIDsInExtent=null,this.graphicsCore=null,this.basemapTerrain=null}get updating(){return this._dirty||this.updatingHandles.updating}_setDirty(){this._dirty=!0}setExtent(e){const t=this.graphicsCoreOwner.view.spatialReference,i=this.graphicsCoreOwner.view.basemapTerrain.spatialReference;if(t===i)this._extent=e??null;else{const s=pt();rs(e,t,s,i)?this._extent=s:this._extent=null}this._setDirty()}scaleRangeActive(){return this._scaleRangeActive}updateScaleRangeActive(){const e=this.layer,t=e.effectiveScaleRange;let i=this.layerScaleEnabled&&t!=null&&gl(t.minScale,t.maxScale);e.labelingInfo&&!i&&(i=e.labelingInfo.some(a=>a&&gl(a.minScale??0,a.maxScale??0)));const s=this._scaleRangeActive!==i;return this._scaleRangeActive=i,i&&!this.handles.has(qi)&&this.basemapTerrain?(this.handles.add(this.basemapTerrain.on("scale-change",a=>this._scaleUpdateHandler(a)),qi),this.layerScaleEnabled&&this.handles.add(this.basemapTerrain.on("tiles-visibility-changed",()=>this._setDirty()),qi)):!i&&this.handles.has(qi)&&this.handles.remove(qi),s}get running(){return!(!this.graphicsCoreOwner.view.basemapTerrain||!this.updating)}runTask(e){const t=this.graphicsCoreOwner.view.basemapTerrain;if(this._extent&&t&&t.ready&&this._scaleRangeActive&&this.layerScaleEnabled){if(this._layerScaleRangeVisibilityQuery)return Ya.YIELD;{this._layerScaleRangeVisibilityQuery=!0;const{minScale:i,maxScale:s}=this.layer.effectiveScaleRange;t.queryVisibleScaleRange(this._extent,i,s,a=>this._finishUpdate(a))}}else this._finishUpdate(!0);e.madeProgress()}_finishUpdate(e){this._layerScaleRangeVisibilityQuery=!1,this._set("suspended",!e),this._dirty=!1}_visibleAtLayerScale(e){const t=this.layer.effectiveScaleRange;return!this.layerScaleEnabled||Fn(e,t.minScale||0,t.maxScale||0)}_visibleAtLabelScale(e,t){return Fn(e,t.minScale||0,t.maxScale||0)}_graphicScale(e){let t;return d(e.centroid)?t=e.centroid:d(e.graphic.geometry)&&e.graphic.geometry.type==="point"&&(t=e.graphic.geometry),t?this.graphicsCoreOwner.view.basemapTerrain?this.graphicsCoreOwner.view.basemapTerrain.getScale(t):1:null}_graphicVisible(e){if(!this.layerScaleEnabled)return!0;const t=this._graphicScale(e);return this._visibleAtLayerScale(t)}updateVisibility(e){if(this._scaleRangeActive){const t=this._graphicVisible(e);return e.setVisibilityFlag(ze.SCALE_RANGE,t,ye.GRAPHIC)}return!1}updateGraphicLabelScaleVisibility(e){if(!this._scaleRangeActive||!e.labelLayers||e.labelLayers.length===0)return!1;const t=this._graphicScale(e),i=this._updateLabelScaleVisibility(e,t);return i&&(this.graphicsCoreOwner.view.deconflictor.setDirty(),this.graphicsCoreOwner.view.labeler.setDirty()),i}_updateLabelScaleVisibility(e,t){if(!e.labelLayers||e.labelLayers.length===0)return!1;const i=e.labelLayers[0]._labelClass;if(i&&i.minScale!=null&&i.maxScale!=null){const s=this._visibleAtLabelScale(t,i);if(e.setVisibilityFlag(ze.SCALE_RANGE,s,ye.LABEL))return!0}return!1}_scaleUpdateHandler(e){if(this._setDirty(),!this.graphicsCore.visible)return;const t=e.extent,i=e.scale,s=this._visibleAtLayerScale(i);let a=!1;const n=this.graphicsCoreOwner.view.spatialReference,o=e.spatialReference;if(b(o))return void pl.error("scaleUpdate: Internal error, no SpatialReference given for tiles");const l=!o.equals(n);if(l&&!rs(t,o,ml,n))return void pl.error("scaleUpdate: Internal error, cannot project AABR from "+o+" to wkid "+n);const h=l?ml:t;this.queryGraphicUIDsInExtent(h,n,c=>{const u=this.graphicsCore.getGraphics3DGraphicById(c);if(b(u))return;const p=u.centroid;d(p)&&(t[0]>p.x||t[1]>p.y||t[2]<p.x||t[3]<p.y)||(u.setVisibilityFlag(ze.SCALE_RANGE,s,ye.GRAPHIC)&&(a=!0),this._updateLabelScaleVisibility(u,i)&&(a=!0))}),a&&(this.graphicsCoreOwner.view.deconflictor.setDirty(),this.graphicsCoreOwner.view.labeler.setDirty())}layerMinMaxScaleChangeHandler(){this.updateScaleRangeActive()&&!this._scaleRangeActive?this.graphicsCore.modifyGraphics3DGraphicVisibilities(e=>e.clearVisibilityFlag(ze.SCALE_RANGE)):this._scaleRangeActive&&this.graphicsCore.updateAllGraphicsVisibility(),this._setDirty()}};function gl(r,e){return r>0||e>0}y([v()],He.prototype,"graphicsCoreOwner",void 0),y([v()],He.prototype,"layer",void 0),y([v()],He.prototype,"queryGraphicUIDsInExtent",void 0),y([v()],He.prototype,"graphicsCore",void 0),y([v()],He.prototype,"basemapTerrain",void 0),y([v({constructOnly:!0})],He.prototype,"layerScaleEnabled",void 0),y([v({readOnly:!0})],He.prototype,"suspended",void 0),y([v({readOnly:!0})],He.prototype,"updating",null),y([v()],He.prototype,"_dirty",void 0),He=y([Xe("esri.views.3d.layers.graphics.Graphics3DScaleVisibility")],He);const qi="terrain-events",ml=pt(),x_=He;let W=class extends Hl{constructor(r){super(r),this.type="graphics-3d",this.graphicsCore=null,this.drapeSourceType=Fu.Features,this.scaleVisibilityEnabled=!1,this.frustumVisibilityEnabled=!1,this._suspendResumeExtent=null}initialize(){const{layer:r}=this,e="effectiveScaleRange"in r?r:null,t=this.scaleVisibilityEnabled&&d(e),i=new I({owner:this,layer:this.owner.layer,preferredUpdatePolicy:he.SYNC,graphicSymbolSupported:!0,componentFactories:{elevationAlignment:(s,a)=>new o_({graphicsCoreOwner:this,graphicsCore:s,queryGraphicUIDsInExtent:a,elevationProvider:this.view.elevationProvider}),scaleVisibility:t?(s,a)=>new x_({graphicsCoreOwner:this,layer:e,queryGraphicUIDsInExtent:a,graphicsCore:s,basemapTerrain:this.owner.view.basemapTerrain}):null,objectStates:s=>new S_(s)}});if(this._set("graphicsCore",i),this.frustumVisibilityEnabled&&this._set("frustumVisibility",new __({graphicsCoreOwner:this})),"fullOpacity"in this.owner){const s=this.owner;this.updatingHandles.add(()=>s.fullOpacity,()=>this.graphicsCore.opacityChange())}if("elevationInfo"in r){const s=r;this.updatingHandles.add(()=>s.elevationInfo,(a,n)=>{ma(a,n)&&this.updatingHandles.addPromise(this.graphicsCore.elevationInfoChange())})}this._set("initializePromise",this._initializeAsync()),this.updatingHandles.addPromise(this.initializePromise)}async _initializeAsync(){try{await this.graphicsCore.initializePromise}catch(r){if(Xi(r))return;throw r}this.destroyed||(this.handles.add($e(()=>this.view.clippingArea,()=>this._updateClippingExtent(),wl)),this._updateClippingExtent(),this._setupSuspendResumeExtent(),this.graphicsCore.startCreateGraphics())}destroy(){this.handles.removeAll(),this.updatingHandles.removeAll(),this._set("frustumVisibility",K(this.frustumVisibility)),this._set("graphicsCore",K(this.graphicsCore))}get layer(){return this.owner.layer}get view(){return this.owner.view}get scaleVisibility(){var r;return(r=this.graphicsCore)==null?void 0:r.scaleVisibility}get elevationAlignment(){var r;return(r=this.graphicsCore)==null?void 0:r.elevationAlignment}get objectStates(){var r;return(r=this.graphicsCore)==null?void 0:r.objectStates}get scaleVisibilitySuspended(){return!(!d(this.scaleVisibility)||!this.scaleVisibility.suspended)}get frustumVisibilitySuspended(){return d(this.frustumVisibility)&&this.frustumVisibility.suspended}get suspended(){return this.owner.suspended??!1}get updating(){var r;return!!((r=this.graphicsCore)!=null&&r.updating||d(this.scaleVisibility)&&this.scaleVisibility.updating||d(this.frustumVisibility)&&this.frustumVisibility.updating||this.updatingHandles.updating)}get graphics3DGraphics(){var r;return(r=this.graphicsCore)==null?void 0:r.graphics3DGraphics}get graphics3DGraphicsByObjectID(){var r;return(r=this.graphicsCore)==null?void 0:r.graphics3DGraphicsByObjectID}get loadedGraphics(){return this.owner.loadedGraphics}get fullOpacity(){return this.owner.fullOpacity??1}get slicePlaneEnabled(){return this.owner.slicePlaneEnabled}get updatePolicy(){return this.owner.updatePolicy}notifyGraphicGeometryChanged(r){this.graphicsCore.notifyGraphicGeometryChanged(r)}notifyGraphicVisibilityChanged(r){this.graphicsCore.notifyGraphicVisibilityChanged(r)}getRenderingInfo(r,e,t){const i=ug(r,{renderer:e,arcade:t});if(d(i)&&i.color){const s=i.color;s[0]=s[0]/255,s[1]=s[1]/255,s[2]=s[2]/255}return i}getRenderingInfoAsync(r,e,t,i){return gg(r,{renderer:e,arcade:t,...i})}getHit(r){if(this.owner.loadedGraphics){const e=this.owner.loadedGraphics.find(t=>t.uid===r);if(e){const t=this.layer instanceof kl?this.layer:null,i=Kr(e,t);return{type:"graphic",graphic:i,layer:i.layer}}}return null}whenGraphicBounds(r,e){return this.graphicsCore?this.graphicsCore.whenGraphicBounds(r,e):Promise.reject()}computeAttachmentOrigin(r,e){return this.graphicsCore?this.graphicsCore.computeAttachmentOrigin(r,e):null}getSymbolLayerSize(r,e){return this.graphicsCore?this.graphicsCore.getSymbolLayerSize(r,e):null}maskOccludee(r){const{set:e,handle:t}=this.objectStates.acquireSet(Ct.MaskOccludee,null);return this.objectStates.setUid(e,r.uid),t}highlight(r){if(r instanceof Jd)return fl;if(typeof r=="number")return this.highlight([r]);if(r instanceof Nn)return this.highlight([r]);if(r instanceof Qd&&(r=r.toArray()),Array.isArray(r)&&r.length>0){if(r[0]instanceof Nn){const e=r.map(s=>s.uid),{set:t,handle:i}=this.objectStates.acquireSet(Ct.Highlight,null);return this.objectStates.setUids(t,e),i}if(typeof r[0]=="number"){const e=r,{set:t,handle:i}=this.objectStates.acquireSet(Ct.Highlight,null);return this.objectStates.setObjectIds(t,e),i}}return fl}_setupSuspendResumeExtent(){const{scaleVisibility:r,frustumVisibility:e}=this;if(b(r)&&b(e))return;const t=({computedExtent:i,extentPadding:s})=>{this._suspendResumeExtent=Nu(i,this._suspendResumeExtent,yg,s),d(r)&&r.setExtent(this._suspendResumeExtent),d(e)&&e.setExtent(this._suspendResumeExtent)};this.handles.add($e(()=>{var i,s;return{computedExtent:(i=this.graphicsCore)==null?void 0:i.computedExtent,extentPadding:(s=this.graphicsCore)==null?void 0:s.extentPadding}},i=>t(i),Pl))}_updateClippingExtent(){const r=this.view.clippingArea;this.graphicsCore.setClippingExtent(r,this.view.spatialReference)&&this.graphicsCore.recreateAllGraphics()}};y([v()],W.prototype,"type",void 0),y([v({constructOnly:!0})],W.prototype,"owner",void 0),y([v()],W.prototype,"layer",null),y([v()],W.prototype,"view",null),y([v({constructOnly:!0})],W.prototype,"graphicsCore",void 0),y([v()],W.prototype,"scaleVisibility",null),y([v({constructOnly:!0})],W.prototype,"frustumVisibility",void 0),y([v()],W.prototype,"elevationAlignment",null),y([v()],W.prototype,"objectStates",null),y([v()],W.prototype,"scaleVisibilitySuspended",null),y([v({readOnly:!0})],W.prototype,"frustumVisibilitySuspended",null),y([v()],W.prototype,"suspended",null),y([v({readOnly:!0})],W.prototype,"updating",null),y([v()],W.prototype,"loadedGraphics",null),y([v()],W.prototype,"fullOpacity",null),y([v()],W.prototype,"slicePlaneEnabled",null),y([v()],W.prototype,"drapeSourceType",void 0),y([v()],W.prototype,"updatePolicy",null),y([v({constructOnly:!0})],W.prototype,"scaleVisibilityEnabled",void 0),y([v({constructOnly:!0})],W.prototype,"frustumVisibilityEnabled",void 0),y([v()],W.prototype,"initializePromise",void 0),W=y([Xe("esri.views.3d.layers.graphics.GraphicsProcessor")],W);const fl=ds();async function C_(r,e,t){if(b(r)||e.candidates.length===0)return yl;const i=r.graphics3DGraphicsByObjectID??r.graphics3DGraphics,s=[],a=[],{renderer:n}=r,o=d(n)&&"arcadeRequired"in n&&n.arcadeRequired?jl():null,l=async(g,{graphic:_,graphics3DSymbol:S})=>{const P=await o,x=await r.getRenderingInfoAsync(_,n,P,{signal:t});return b(x)?[]:S.queryForSnapping(g,c,x,t)},{candidates:h,spatialReference:c}=e;for(let g=0;g<h.length;++g){const _=h[g],{objectId:S}=_,P=typeof S=="number"?i==null?void 0:i.get(S):void 0;if(b(P))continue;const{graphics3DSymbol:x}=P;x.symbologySnappingSupported&&(s.push(l(_,P)),a.push(g))}if(s.length===0)return yl;const u=await Promise.all(s);Oe(t);const p=[],m=[];for(let g=0;g<u.length;++g){const _=u[g],S=a[g];for(const P of _)p.push(P),m.push(S)}return{candidates:p,sourceCandidateIndices:m}}const yl={candidates:[],sourceCandidateIndices:[]};function P_(r){const e=r.view.spatialReference,t=r.layer.fullExtent,i=d(t)&&t.spatialReference;if(b(t)||!i)return Promise.resolve(null);if(i.equals(e))return Promise.resolve(t.clone());const s=Gc(t,e);return d(s)?Promise.resolve(s):r.view.state.isLocal?og(t,e,r.layer.portalItem).then(a=>!r.destroyed&&a?a:null).catch(()=>null):Promise.resolve(null)}let St=class extends hg(lg){constructor(){super(...arguments),this.type="graphics-3d",this.symbologySnappingSupported=!0,this._slicePlaneEnabled=!1,this.fullExtentInLocalViewSpatialReference=null}initialize(){this._set("processor",new W({owner:this,scaleVisibilityEnabled:!0,frustumVisibilityEnabled:!0})),this.addResolvingPromise(this.processor.initializePromise),this.handles.add(this.layer.on("graphic-update",r=>this.processor.graphicsCore.graphicUpdateHandler(r))),this.addResolvingPromise(P_(this).then(r=>this.fullExtentInLocalViewSpatialReference=r)),this.layer.internal?this.notifyChange("updating"):this.handles.add(Cl(()=>{var r,e;return(e=(r=this.view)==null?void 0:r.basemapTerrain)==null?void 0:e.ready},()=>()=>this.notifyChange("updating"),{once:!0}))}destroy(){this.handles.removeAll(),this.updatingHandles.removeAll(),this._set("processor",K(this.processor))}get loadedGraphics(){return this.layer.graphics}get legendEnabled(){var r;return this.canResume()&&!((r=this.processor)!=null&&r.frustumVisibilitySuspended)}get slicePlaneEnabled(){const r=this.layer.internal;return this._slicePlaneEnabled&&!r}set slicePlaneEnabled(r){this._slicePlaneEnabled=r}getSuspendInfo(){var e,t;const r=super.getSuspendInfo();return r.outsideScaleRange=((e=this.processor)==null?void 0:e.scaleVisibilitySuspended)??!1,r.outsideOfView=((t=this.processor)==null?void 0:t.frustumVisibilitySuspended)??!1,r}async fetchPopupFeatures(r,e){var t;return((t=se(e))==null?void 0:t.clientGraphics)??[]}getHit(r){return this.processor.getHit(r)}whenGraphicBounds(r,e){return this.processor.whenGraphicBounds(r,e)}computeAttachmentOrigin(r,e){var t;return(t=this.processor)==null?void 0:t.computeAttachmentOrigin(r,e)}getSymbolLayerSize(r,e){return this.processor.getSymbolLayerSize(r,e)}queryGraphics(){return Promise.resolve(this.loadedGraphics)}maskOccludee(r){return this.processor.maskOccludee(r)}highlight(r){return this.processor.highlight(r)}async elevationAlignPointsInFeatures(r,e){const{processor:t}=this;if(b(t)||b(t.graphics3DGraphics))throw new We("graphicslayerview3d:missing-processor","A Graphics3D processor is needed to resolve graphics elevation.");const{graphics3DGraphics:i}=t,s=a=>typeof a=="number"?i.get(a):void 0;return cg(this.view,this.layer,s,r,e)}async queryForSymbologySnapping(r,e){return C_(this.processor,r,e)}get updatePolicy(){var r;return((r=this.processor)==null?void 0:r.graphicsCore.effectiveUpdatePolicy)||he.SYNC}canResume(){var r;return super.canResume()&&!((r=this.processor)!=null&&r.scaleVisibilitySuspended)}isUpdating(){var r,e,t;return!(!((r=this.processor)!=null&&r.updating)&&(this.layer.internal||(t=(e=this.view)==null?void 0:e.basemapTerrain)!=null&&t.ready))}get performanceInfo(){var r,e;return{displayedNumberOfFeatures:this.loadedGraphics.length,maximumNumberOfFeatures:-1,totalNumberOfFeatures:-1,nodes:0,core:null,updating:this.updating,elevationUpdating:((r=this.processor)==null?void 0:r.elevationAlignment.updating)??!1,visibilityFrustum:!((e=this.processor)!=null&&e.frustumVisibilitySuspended)}}getUsedMemory(){var r,e;return((e=(r=this.processor)==null?void 0:r.graphicsCore)==null?void 0:e.usedMemory)??0}getUnloadedMemory(){var r,e;return(e=(r=this.processor)==null?void 0:r.graphicsCore)==null?void 0:e.unprocessedMemoryEstimate}ignoresMemoryFactor(){return!0}};y([v()],St.prototype,"loadedGraphics",null),y([v({readOnly:!0})],St.prototype,"legendEnabled",null),y([v()],St.prototype,"layer",void 0),y([v({readOnly:!0})],St.prototype,"processor",void 0),y([v()],St.prototype,"_slicePlaneEnabled",void 0),y([v({type:Boolean})],St.prototype,"slicePlaneEnabled",null),St=y([Xe("esri.views.3d.layers.GraphicsLayerView3D")],St);const w_=St,Fv=Object.freeze(Object.defineProperty({__proto__:null,default:w_},Symbol.toStringTag,{value:"Module"})),O_=Object.freeze(Object.defineProperty({__proto__:null,build:Ah},Symbol.toStringTag,{value:"Module"})),Nv=Object.freeze(Object.defineProperty({__proto__:null,build:zp},Symbol.toStringTag,{value:"Module"})),Bv=Object.freeze(Object.defineProperty({__proto__:null,build:Vp},Symbol.toStringTag,{value:"Module"})),E_=Object.freeze(Object.defineProperty({__proto__:null,build:Bh},Symbol.toStringTag,{value:"Module"})),R_=Object.freeze(Object.defineProperty({__proto__:null,build:rc},Symbol.toStringTag,{value:"Module"})),A_=Object.freeze(Object.defineProperty({__proto__:null,build:dc},Symbol.toStringTag,{value:"Module"}));export{Nv as D,Fv as G,Bv as R};
