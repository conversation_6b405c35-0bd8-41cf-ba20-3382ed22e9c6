<template>
  <div class="navigation-container" v-if="routesLoaded">
    <canvas ref="bgCanvas" id="bg-canvas" class="background-canvas"></canvas>
    
    <div class="btn-1" :class="{ active: isActive.btn1 }" @mouseenter="handleMouseEnter('btn1')" @mouseleave="handleMouseLeave('btn1')" @click="navigateTo(systemRoutes['生产一张图'])">生产一张图</div>
    <div class="btn-2" :class="{ active: isActive.btn2 }" @mouseenter="handleMouseEnter('btn2')" @mouseleave="handleMouseLeave('btn2')" @click="navigateTo(systemRoutes['营收一张图'])">营收一张图</div>
    
    <!-- 第一个大圆柱对应的小圆柱 -->
    <div v-for="(subsystem, index) in mainSystems[0]?.subsystems || []" :key="'cylinder-1-' + index" 
         class="small-cylinder" 
         :class="{ 
           active: isActive['smallCylinder1' + (index + 1)], 
           ['small-cylinder-1-' + (index + 1)]: true 
         }" 
         @mouseenter="handleMouseEnter('smallCylinder1' + (index + 1))" 
         @mouseleave="handleMouseLeave('smallCylinder1' + (index + 1))" 
         @click.stop="navigateToSubsystem(mainSystems[0]?.id, subsystem)">
      <div class="small-cylinder-text vertical-text">{{ subsystem.meta.title }}</div>
      <div class="tooltip">{{ subsystem.meta.title }}</div>
    </div>
    
    <!-- 第二个大圆柱对应的小圆柱 -->
    <div v-for="(subsystem, index) in mainSystems[1]?.subsystems || []" :key="'cylinder-2-' + index" 
         class="small-cylinder" 
         :class="{ 
           active: isActive['smallCylinder2' + (index + 1)], 
           ['small-cylinder-2-' + (index + 1)]: true 
         }" 
         @mouseenter="handleMouseEnter('smallCylinder2' + (index + 1))" 
         @mouseleave="handleMouseLeave('smallCylinder2' + (index + 1))" 
         @click.stop="navigateToSubsystem(mainSystems[1]?.id, subsystem)">
      <div class="small-cylinder-text vertical-text">{{ subsystem.meta.title }}</div>
      <div class="tooltip">{{ subsystem.meta.title }}</div>
    </div>
    
    <!-- 第三个大圆柱对应的小圆柱 -->
    <div v-for="(subsystem, index) in mainSystems[2]?.subsystems || []" :key="'cylinder-3-' + index" 
         class="small-cylinder" 
         :class="{ 
           active: isActive['smallCylinder3' + (index + 1)], 
           ['small-cylinder-3-' + (index + 1)]: true 
         }" 
         @mouseenter="handleMouseEnter('smallCylinder3' + (index + 1))" 
         @mouseleave="handleMouseLeave('smallCylinder3' + (index + 1))" 
         @click.stop="navigateToSubsystem(mainSystems[2]?.id, subsystem)">
      <div class="small-cylinder-text vertical-text">{{ subsystem.meta.title }}</div>
      <div class="tooltip">{{ subsystem.meta.title }}</div>
    </div>
    
    <!-- 第四个大圆柱对应的小圆柱 -->
    <div v-for="(subsystem, index) in mainSystems[3]?.subsystems || []" :key="'cylinder-4-' + index" 
         class="small-cylinder" 
         :class="{ 
           active: isActive['smallCylinder4' + (index + 1)], 
           ['small-cylinder-4-' + (index + 1)]: true 
         }" 
         @mouseenter="handleMouseEnter('smallCylinder4' + (index + 1))" 
         @mouseleave="handleMouseLeave('smallCylinder4' + (index + 1))" 
         @click.stop="navigateToSubsystem(mainSystems[3]?.id, subsystem)">
      <div class="small-cylinder-text vertical-text">{{ subsystem.meta.title }}</div>
      <div class="tooltip">{{ subsystem.meta.title }}</div>
    </div>
    
    <!-- 大圆柱作为背景 -->
    <div class="cylinder-1" :class="{ active: isActive.cylinder1 }" @mouseenter="handleMouseEnter('cylinder1')" @mouseleave="handleMouseLeave('cylinder1')" @click="navigateTo(systemRoutes['生产一张图'])"></div>
    <div class="cylinder-2" :class="{ active: isActive.cylinder2 }" @mouseenter="handleMouseEnter('cylinder2')" @mouseleave="handleMouseLeave('cylinder2')" @click="navigateTo(systemRoutes['营收一张图'])"></div>
    <div class="cylinder-3" :class="{ active: isActive.cylinder3 }" @mouseenter="handleMouseEnter('cylinder3')" @mouseleave="handleMouseLeave('cylinder3')"></div>
    <div class="cylinder-4" :class="{ active: isActive.cylinder4 }" @mouseenter="handleMouseEnter('cylinder4')" @mouseleave="handleMouseLeave('cylinder4')"></div>
    
    <div class="logo">瓜州水务系统</div>
  </div>
  <div v-else class="loading-container">
    <div class="loading-spinner"></div>
    <div class="loading-text">系统加载中...</div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { initBackgroundEffect, EffectType } from './effects';
import { usePermissionStore, useUserStore } from '@/store';
import { refreshAllRoutes, filterAsyncRouter } from '@/utils/RouterHelper';
import { removeSlash } from '@/utils/removeIdSlash';

import { getapplications } from '@/api/portal';
import { GetApplicationAsyncRoutes } from '@/api/menu/source';

const router = useRouter();
const permissionStore = usePermissionStore();
const userStore = useUserStore();

// 特效清理函数
let cleanupEffect = null;

// 路由加载状态
const routesLoaded = ref(false);

// canvas引用
const bgCanvas = ref(null);

// 主系统列表
const mainSystems = ref([]);

// 子系统路由缓存
const subsystemRoutes = reactive({});

// 初始化背景特效
const initEffects = async () => {
  console.log('开始初始化背景特效...');
  
  // 等待下一个渲染周期
  await nextTick();
  
  // 使用ref引用检查canvas元素
  if (!bgCanvas.value) {
    console.error('无法找到canvas元素 (ref为null)');
    return;
  }
  
  // 检查DOM中是否存在canvas元素
  const canvasDom = document.getElementById('bg-canvas');
  if (!canvasDom) {
    console.error('无法通过ID找到canvas元素');
    return;
  }
  
  console.log('找到canvas元素:', bgCanvas.value);
  
  try {
    // 使用ref引用的方式初始化特效
    cleanupEffect = initBackgroundEffect(EffectType.WATER_PARTICLES, 'bg-canvas');
    console.log('背景特效初始化完成');
  } catch (error) {
    console.error('初始化背景特效失败:', error);
  }
};

// 监听路由加载状态，当加载完成时初始化特效
watch(routesLoaded, (newValue) => {
  if (newValue) {
    console.log('路由加载完成，准备初始化特效');
    // 等待DOM更新后初始化特效
    nextTick(() => {
      initEffects();
    });
  }
});

// 按钮状态
const isActive = reactive({
  cylinder1: false,
  cylinder2: false,
  cylinder3: false,
  cylinder4: false,
  smallCylinder11: false,
  smallCylinder12: false,
  smallCylinder21: false,
  smallCylinder22: false,
  smallCylinder31: false,
  smallCylinder32: false,
  smallCylinder41: false,
  smallCylinder42: false,
  btn1: false,
  btn2: false
});

// 子系统路由映射，默认路径
const systemRoutes = reactive({
  // 一张图系列 - 使用路由替换方式而不是查询参数
  '生产一张图': '/cockpit/production',
  '营收一张图': '/cockpit/revenue',
});

// 获取子系统路由并添加到Vue Router
const fetchSubsystems = async (applicationId) => {
  try {
    console.log(`获取系统 ${applicationId} 的子系统路由...`);
    
    // 如果已经获取过，直接使用缓存的数据
    if (subsystemRoutes[applicationId]) {
      console.log(`使用缓存的子系统数据:`, subsystemRoutes[applicationId]);
      return subsystemRoutes[applicationId];
    }
    
    const response = await GetApplicationAsyncRoutes(applicationId);
    
    if (response.data) {
      const systemIndex = mainSystems.value.findIndex(system => system.id === applicationId);
      
      if (systemIndex !== -1) {
        // 存储子系统路由
        subsystemRoutes[applicationId] = response.data;
        
        // 为主系统添加子系统信息
        mainSystems.value[systemIndex].subsystems = response.data
          .filter(route => route.children && route.children.length)
          .slice(0, 2); // 每个大系统只显示前两个子系统
        
        console.log(`获取到系统 ${applicationId} 的子系统:`, mainSystems.value[systemIndex].subsystems);
        
        return response.data;
      }
    }
    return [];
  } catch (error) {
    console.error(`获取系统 ${applicationId} 的子系统失败:`, error);
    return [];
  }
};

// 处理并添加路由到Vue Router
const addRoutesToRouter = async (routes) => {
  if (!routes || !routes.length) return false;
  
  try {
    // 处理并添加路由
    const asyncRouters = filterAsyncRouter(routes);
    if (asyncRouters && asyncRouters.length) {
      // 检查现有路由，避免重复添加
      const existingRoutes = router.getRoutes().map(r => r.name);
      
      let addedCount = 0;
      asyncRouters.forEach(route => {
        // 确保路由名称存在且不重复
        if (route.name && !existingRoutes.includes(route.name)) {
          // 确保路径格式正确，必须以斜杠开头
          if (!route.path.startsWith('/')) {
            route.path = '/' + route.path;
          }
          
          router.addRoute(route);
          console.log(`添加路由: ${route.path}, 名称: ${route.name}`);
          addedCount++;
        } else if (route.name) {
          console.log(`路由已存在，跳过添加: ${route.path}, 名称: ${route.name}`);
        } else {
          console.warn(`路由缺少名称，无法添加: ${route.path}`);
        }
      });
      
      return addedCount > 0;
    }
    return false;
  } catch (routeError) {
    console.error(`添加路由失败:`, routeError);
    return false;
  }
};

// 查找第一个叶子节点路由
const findFirstLeafRoute = (route) => {
  // 如果没有子路由或子路由为空，则当前路由就是叶子节点
  if (!route.children || route.children.length === 0) {
    return route;
  }
  
  // 找到第一个子路由，继续向下查找
  return findFirstLeafRoute(route.children[0]);
};

// 获取路由的完整路径
const getFullRoutePath = (route, parentPath = '') => {
  if (!route) return '';
  
  // 确保路径以斜杠开头
  let routePath = route.path.startsWith('/') ? route.path : `/${route.path}`;
  
  // 如果父路径为空或已经是完整路径，直接返回
  if (!parentPath || routePath.includes(parentPath)) {
    return routePath;
  }
  
  // 构建完整路径，避免路径重复
  const routeFirstSegment = routePath.split('/')[1];
  const parentLastSegment = parentPath.split('/').pop();
  
  if (parentLastSegment === routeFirstSegment) {
    // 避免路径段重复
    return `${parentPath}${routePath.substring(routePath.indexOf(routeFirstSegment) + routeFirstSegment.length)}`;
  } else {
    // 正常拼接
    return `${parentPath}${routePath}`;
  }
};

// 导航到子系统
const navigateToSubsystem = async (applicationId, subsystem) => {
  if (!subsystem || !subsystem.path) {
    console.warn('子系统路径未定义');
    return;
  }
  
  try {
    // 首先获取该子系统的路由数据
    const routes = await fetchSubsystems(applicationId);
    
    if (!routes || !routes.length) {
      console.error('%c【获取路由失败】', 'color: #e74c3c; font-weight: bold', '未能获取到路由数据');
      return;
    }
    
    // 添加路由到Vue Router
    console.log('%c【添加路由到Vue Router】', 'color: #3498db; font-weight: bold');
    const routesAdded = await addRoutesToRouter(routes);
    
    if (!routesAdded) {
      console.warn('%c【添加路由失败或无需添加】', 'color: #e67e22; font-weight: bold', '路由可能已存在或添加失败');
    }
    
    // 确保路径格式正确
    const parentPath = subsystem.path.startsWith('/') ? subsystem.path : `/${subsystem.path}`;
    
    // 查找第一个叶子节点路由
    let targetRoute = subsystem;
    let targetPath = parentPath;
    
    // 如果有子路由，递归查找叶子节点
    if (subsystem.children && subsystem.children.length > 0) {
      // 查找第一个叶子节点
      targetRoute = findFirstLeafRoute(subsystem);
      console.log('找到叶子节点路由:', targetRoute);
      
      // 构建完整路径
      targetPath = getFullRoutePath(targetRoute, parentPath);
    }
    
    console.log('最终导航路径:', targetPath);
    
    // 检查路由是否存在并输出详细信息
    const allRoutes = router.getRoutes();
    console.log('当前路由表大小:', allRoutes.length);
    
    // 查找匹配的路由
    const matchedRoute = allRoutes.find(route => {
      // 检查完整路径匹配
      if (route.path === targetPath) return true;
      // 检查父路径匹配
      if (route.path === parentPath) return true;
      return false;
    });
    
    // 输出匹配的路由详细信息
    if (matchedRoute) {
      
      // 直接导航到目标路由

      router.push({
        path: targetPath,
        replace: false
      }).then(() => {
        console.log('%c【导航成功】', 'color: #2ecc71; font-weight: bold');
      }).catch(err => {
        console.error('%c【导航失败】', 'color: #e74c3c; font-weight: bold', err);
        handleNavigationFailure(targetPath);
      });
    } else {
      console.log('%c【未找到匹配路由】', 'color: #e74c3c; font-weight: bold');
      
      // 搜索相似路由
      const similarRoutes = allRoutes.filter(route => 
        route.path.includes(targetPath.split('/').pop() || '') || 
        (targetPath.includes(route.path) && route.path !== '/')
      );
      
      if (similarRoutes.length > 0) {
        console.log('找到相似路由:', similarRoutes.map(r => ({ 
          路径: r.path, 
          名称: r.name 
        })));
      }
      
      // 路由不存在，尝试使用刷新路由的方法
      console.log('%c【尝试刷新路由】', 'color: #f39c12; font-weight: bold');
      await refreshAllRoutes();
      
      // 再次检查路由是否存在
      const updatedRoutes = router.getRoutes();
      const routeExistsAfterRefresh = updatedRoutes.some(route => 
        route.path === targetPath || route.path === parentPath
      );
      
      if (routeExistsAfterRefresh) {
        console.log('%c【刷新后找到路由】', 'color: #2ecc71; font-weight: bold');
        setTimeout(() => {
          router.push({
            path: targetPath,
            replace: false
          }).then(() => {
            console.log('%c【导航成功】', 'color: #2ecc71; font-weight: bold');
          }).catch(err => {
            console.error('%c【导航失败】', 'color: #e74c3c; font-weight: bold', err);
            handleNavigationFailure(targetPath);
          });
        }, 100);
      } else {
        console.error('%c【路由不存在】', 'color: #e74c3c; font-weight: bold', '即使刷新路由后也未找到目标路由');
        handleNavigationFailure(targetPath);
      }
    }
  } catch (err) {
    console.error('导航到子系统错误:', err);
  }
};

// 处理导航失败的情况
const handleNavigationFailure = (path) => {
  // 备用方案1：如果无法导航，尝试先跳转到首页再跳转目标
  console.log('尝试备用方案: 先到首页再到目标');
  router.push('/home').then(() => {
    setTimeout(() => {
      console.log('从首页跳转到目标路径');
      router.push(path);
    }, 100);
  }).catch(() => {
    // 最后的尝试 - 直接强制刷新页面
    if (confirm('系统导航出现问题，是否刷新页面?')) {
      window.location.href = `/#${path}`;
      setTimeout(() => {
        window.location.reload();
      }, 100);
    }
  });
};

// 初始化系统路由
const initSystemRoutes = async () => {
  try {
    // 确保用户信息已加载
    if (!userStore.user) {
      await userStore.GetInfo();
      await Promise.all([
        userStore.GetBtnPerms(),
        userStore.InitTenantInfo()
      ]);
    }
    
    // 加载路由
    await refreshAllRoutes();
    console.log('系统路由加载完成');
    
    // 获取大系统信息，但不获取子系统路由
    await fetchMainSystems();
    
    // 输出当前所有路由，检查是否已加载
    const allRoutes = router.getRoutes();
    console.log('初始化后所有路由列表:', allRoutes);
    
    // 标记路由已加载
    routesLoaded.value = true;
  } catch (error) {
    console.error('加载路由失败:', error);
  }
};

// 获取大系统列表
const fetchMainSystems = async () => {
  try {
    const tenantId = userStore.user?.tenantId?.id;
    if (!tenantId) return;
    const tenantid = removeSlash(tenantId);
    const response = await getapplications(tenantid);
    
    if (response.data) {
      // 只取前四个系统
      const systems = response.data.slice(0, 4).map(system => ({
        ...system,
        subsystems: []
      }));
      
      mainSystems.value = systems;
      console.log('获取到大系统列表:', systems);
      
      // 需要为每个系统获取子系统信息，但不加载路由
      await Promise.all(systems.map(async (system) => {
        try {
          console.log(`获取系统 ${system.id} 的子系统基本信息...`);
          const response = await GetApplicationAsyncRoutes(system.id);
          
          if (response.data) {
            // 只存储子系统信息，不添加路由
            const systemIndex = mainSystems.value.findIndex(s => s.id === system.id);
            if (systemIndex !== -1) {
              // 存储子系统信息但不处理路由
              subsystemRoutes[system.id] = response.data;
              
              // 为主系统添加子系统信息
              mainSystems.value[systemIndex].subsystems = response.data
                .filter(route => route.children && route.children.length)
                .slice(0, 2); // 每个大系统只显示前两个子系统
              
              console.log(`获取到系统 ${system.id} 的子系统:`, mainSystems.value[systemIndex].subsystems);
            }
          }
        } catch (error) {
          console.error(`获取系统 ${system.id} 的子系统基本信息失败:`, error);
        }
      }));
    }
  } catch (error) {
    console.error('获取大系统列表失败:', error);
  }
};

// 鼠标进入按钮
const handleMouseEnter = (id) => {
  isActive[id] = true;
};

// 鼠标离开按钮
const handleMouseLeave = (id) => {
  isActive[id] = false;
};

// 导航到指定路径
const navigateTo = (path) => {
  if (!path) {
    console.warn('路径未定义或不需要导航');
    return;
  }
  
  try {
    // 处理特殊情况：生产一张图和营收一张图
    if (typeof path === 'object' && path.path) {
      const routeConfig = {
        path: path.path,
        query: path.query || {}
      };
      
      console.log('导航到路径:', routeConfig.path, '参数:', routeConfig.query);
      router.push(routeConfig);
    } else {
      console.log('导航到路径:', path);
      router.push(path);
    }
  } catch (err) {
    console.error('导航错误:', err);
  }
};

onMounted(async () => {
  console.log('导航组件已挂载');
  
  // 不在这里初始化特效，改为在watch中监听routesLoaded状态变化时初始化
  
  // 初始化系统路由
  await initSystemRoutes();
});

onUnmounted(() => {
  // 清理特效
  if (cleanupEffect) {
    cleanupEffect();
  }
});
</script>

<style lang="scss" scoped>
.navigation-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background: url('./img/bg.svg') no-repeat;
  background-size: cover;
  
  .background-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none; // 确保不影响点击事件
    opacity: 0.8;
    display: block; /* 确保canvas显示为块级元素 */
  }
  
  .logo {
    position: absolute;
    top: 30px;
    left: 30px;
    font-family: 'MStiffHeiPRC-Bold', sans-serif;
    font-size: 36px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    z-index: 5;
    background: linear-gradient(to right, #42b9fb, #2181f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: fadeIn 1s ease-in-out forwards;
  }
  
  .btn-1, .btn-2 {
    position: absolute;
    font-family: 'MStiffHeiPRC-Bold', sans-serif;
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    z-index: 3;
    width: 265px;
    height: 76px;
    background: url('./img/btn.svg') no-repeat;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0;
    animation: fadeInButtons 0.8s ease-in-out forwards;
    
    &:hover {
      filter: brightness(1.2);
    }
    
    &.active {
      background: url('./img/btn-active.svg') no-repeat;
      background-size: cover;
      transform: scale(1.05);
      filter: drop-shadow(0 0 10px rgba(0, 160, 233, 0.7));
    }
  }
  
  .btn-1 {
    top: 285px;
    left: 600px;
    animation-delay: 0.2s;
  }
  
  .btn-2 {
    top: 285px;
    left: 1056px;
    animation-delay: 0.4s;
  }
  
  /* 大圆柱样式 */
  .cylinder-1, .cylinder-2, .cylinder-3, .cylinder-4 {
    position: absolute;
    height: 410px;
    width: 208px;
    background: url('./img/cylinder.svg') no-repeat;
    background-size: cover;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0;
    animation: fadeInCylinders 1s ease-in-out forwards;
    
    &:hover {
      filter: brightness(1.1);
    }
  }
  
  .cylinder-1{
    top: 454px;
    left: 478px;
    animation-delay: 0.5s;
  }
  
  .cylinder-2{
    top: 468px;
    left: 726px;
    animation-delay: 0.6s;
  }
  
  .cylinder-3{
    top: 468px;
    left: 971px;
    animation-delay: 0.7s;
  }
  
  .cylinder-4{
    top: 454px;
    left: 1234px;
    animation-delay: 0.8s;
  }
  
  /* 小圆柱的通用样式 */
  .small-cylinder {
    position: absolute;
    height: 338px;
    width: 94px;
    background: url('./img/smallCylinder.svg') no-repeat;
    background-size: contain;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    opacity: 0;
    animation: fadeInSmallCylinders 1s ease-in-out forwards;
    
    &:hover {
      filter: brightness(1.2);
      
      .tooltip {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .tooltip {
      position: absolute;
      bottom: -40px;
      left: 50%;
      transform: translateX(-50%) translateY(10px);
      background: rgba(0, 24, 48, 0.85);
      color: #fff;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 14px;
      white-space: nowrap;
      z-index: 10;
      opacity: 0;
      transition: all 0.3s ease;
      pointer-events: none;
      border: 1px solid rgba(0, 160, 233, 0.5);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      
      &:after {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: transparent transparent rgba(0, 24, 48, 0.85) transparent;
      }
    }
  }
  
  /* 为每个小圆柱单独设置位置，形成弧线 */
  .small-cylinder-1-1 {
    top: 488px;
    left: 478px;
    animation-delay: 0.9s;
  }
  
  .small-cylinder-1-2 {
    top: 528px;
    left: 593px;
    animation-delay: 1s;
  }
  
  .small-cylinder-2-1 {
    top: 548px;
    left: 726px;
    animation-delay: 1.1s;
  }
  
  .small-cylinder-2-2 {
    top: 558px;
    left: 841px;
    animation-delay: 1.2s;
  }
  
  .small-cylinder-3-1 {
    top: 558px;
    left: 971px;
    animation-delay: 1.3s;
  }
  
  .small-cylinder-3-2 {
    top: 548px;
    left: 1086px;
    animation-delay: 1.4s;
  }
  
  .small-cylinder-4-1 {
    top: 528px;
    left: 1234px;
    animation-delay: 1.5s;
  }
  
  .small-cylinder-4-2 {
    top: 488px;
    left: 1349px;
    animation-delay: 1.6s;
  }
  
  /* 小圆柱文字样式 */
  .small-cylinder-text {
    font-family: 'MStiffHeiPRC-Bold', sans-serif;
    font-size: 22px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    text-align: center;
    transition: all 0.3s ease;
  }
  
  /* 纵向文字样式 */
  .vertical-text {
    writing-mode: vertical-lr;
    text-orientation: upright;
    letter-spacing: 4px;
  }
  
  /* 大圆柱激活状态 */
  .active{
    background: url('./img/cylinder_active.svg') no-repeat;
    background-size: cover;
    transform: scale(1.05);
    filter: drop-shadow(0 0 15px rgba(0, 160, 233, 0.6));
  }
  
  /* 小圆柱激活状态 */
  .small-cylinder.active {
    background: url('./img/smallCylinder_active.svg') no-repeat;
    background-size: contain;
    transform: scale(1.05);
    filter: drop-shadow(0 0 10px rgba(0, 160, 233, 0.7));
    
    .small-cylinder-text {
      transform: scale(1.05);
      text-shadow: 0 0 8px rgba(0, 195, 255, 0.8);
    }
  }
  
  /* 添加进入动画 */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeInButtons {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeInCylinders {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeInSmallCylinders {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .navigation-container {
    .btn-1 {
      left: 500px;
    }
    
    .btn-2 {
      left: 900px;
    }
    
    .cylinder-1 {
      left: 400px;
    }
    
    .cylinder-2 {
      left: 600px;
    }
    
    .cylinder-3 {
      left: 800px;
    }
    
    .cylinder-4 {
      left: 1000px;
    }
    
    .small-cylinder-1-1 {
      left: 400px;
    }
    
    .small-cylinder-1-2 {
      left: 500px;
    }
    
    .small-cylinder-2-1 {
      left: 600px;
    }
    
    .small-cylinder-2-2 {
      left: 700px;
    }
    
    .small-cylinder-3-1 {
      left: 800px;
    }
    
    .small-cylinder-3-2 {
      left: 900px;
    }
    
    .small-cylinder-4-1 {
      left: 1000px;
    }
    
    .small-cylinder-4-2 {
      left: 1100px;
    }
  }
}

/* 加载状态样式 */
.loading-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #001529 0%, #003366 100%);
  
  .loading-spinner {
    width: 60px;
    height: 60px;
    border: 5px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: #00a0e9;
    border-left-color: #0077cc;
    animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
    margin-bottom: 20px;
    box-shadow: 0 0 20px rgba(0, 160, 233, 0.3);
  }
  
  .loading-text {
    color: #fff;
    font-size: 24px;
    font-family: 'MStiffHeiPRC-Bold', sans-serif;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: pulse 1.5s ease-in-out infinite alternate;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  @keyframes pulse {
    from { opacity: 0.7; }
    to { opacity: 1; }
  }
}
</style> 