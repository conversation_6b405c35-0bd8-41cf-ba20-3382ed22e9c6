import{_ as k}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{z as g,d as x,c as y,r as d,b as i,S as v,o as M,g as S,n as C,q as m,i as u,aB as I,aq as L,C as R}from"./index-r0dFAfgr.js";import{_ as q}from"./Search-NSrhrIa_.js";const P=n=>g({url:"/api/spp/dma/partition/maintainRecords/list",method:"get",params:n}),B=n=>g({url:"/api/spp/dma/partition/maintainRecords",method:"post",data:n}),F=n=>g({url:"/api/spp/dma/partition/maintainRecords",method:"delete",data:n}),W=x({__name:"MoreDetail_WHJL",props:{partition:{}},setup(n){const f=n,_=y(),p=y(),b=d({filters:[],operations:[{type:"btn-group",btns:[{perm:!0,iconifyIcon:"ep:circle-plus",text:"新增",type:"success",click:()=>h()}]}]}),o=d({dataList:[],columns:[{label:"日期",prop:"maintainDate"},{label:"维护内容",prop:"remark"},{label:"现场图片",prop:"img",image:!0}],pagination:{refreshData:({page:e,size:a})=>{o.pagination.page=e,o.pagination.limit=a,c()}},operations:[{perm:!0,text:"编辑",iconifyIcon:"ep:edit",click:e=>h(e)},{perm:!0,text:"删除",iconifyIcon:"ep:delete",type:"danger",click:e=>D(e)}]}),c=async()=>{var e,a;o.loading=!0;try{const t=((e=_.value)==null?void 0:e.queryParams)||{},l=(await P({...t,partitionId:(a=f.partition)==null?void 0:a.value,page:o.pagination.page||1,size:o.pagination.limit||20})).data.data||{};o.dataList=l.data||[],o.pagination.total=l.total||0}catch{}o.loading=!1},h=e=>{var a;s.defaultValue={...e||{}},s.title=e?"编辑维护记录":"添加维护记录",(a=p.value)==null||a.openDialog()},D=e=>{const a=e?[e.id]:[];if(!a.length){i.error("请选择要删除的数据");return}v("确定删除?","提示信息").then(async()=>{try{const t=await F(a);t.data.code===200?(i.success("删除成功"),c()):i.error(t.data.message)}catch{i.error("删除失败")}}).catch(()=>{})},s=d({title:"添加流量表",dialogWidth:600,labelPosition:"right",group:[{fields:[{type:"date",label:"日期",field:"maintainDate"},{type:"textarea",label:"维护内容",field:"remark"},{type:"image",label:"现场图片",field:"img"}]}],submit:async e=>{var a,t;s.submitting=!0;try{const r=await B({...e,partitionId:(a=f.partition)==null?void 0:a.value});r.data.code===200?(i.success("提交成功"),c(),(t=p.value)==null||t.closeDialog()):i.error(r.data.message)}catch{i.error("提交失败")}s.submitting=!1}});return M(()=>{c()}),(e,a)=>{const t=q,r=L,l=k;return S(),C(I,null,[m(t,{ref_key:"refSearch",ref:_,config:u(b),class:"search"},null,8,["config"]),m(r,{config:u(o),class:"table-box"},null,8,["config"]),m(l,{ref_key:"refDialog",ref:p,config:u(s)},null,8,["config"])],64)}}}),J=R(W,[["__scopeId","data-v-ca575ece"]]);export{J as default};
