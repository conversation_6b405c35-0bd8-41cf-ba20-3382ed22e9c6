import"./index-0NlGN6gS.js";import{u as p}from"./useDetector-BRcb7GRN.js";import{i as s}from"./echart-C9Tas6tA.js";import{d as m,r as _,c as d,o as f,ay as l,g as u,n as C,q as x,i as h,C as X}from"./index-r0dFAfgr.js";import{G as v}from"./statistics-CeyexT_5.js";const y={class:"cxcfx"},O=m({__name:"CXCFX",setup(k){const a=_({cxcOption:s()}),r=()=>{v({type:"year"}).then(t=>{const e=t.data.data||{},o=e.x.map(i=>i+"月");a.cxcOption=s(o,e.supply,e.sale,e.nrw)})},n=d(),c=p();return f(()=>{c.listenToMush(document.documentElement,()=>{var t;(t=n.value)==null||t.resize()}),r()}),(t,e)=>{const o=l("VChart");return u(),C("div",y,[x(o,{ref_key:"refChart",ref:n,option:h(a).cxcOption},null,8,["option"])])}}}),G=X(O,[["__scopeId","data-v-d4ea7089"]]);export{G as default};
