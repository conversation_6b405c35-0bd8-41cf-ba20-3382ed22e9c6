import Expand from '@arcgis/core/widgets/Expand.js'
import Legend from '@arcgis/core/widgets/Legend.js'

export const useLegend = () => {
  let legend: __esri.Legend | undefined
  let legendExpand: __esri.Expand | undefined
  const init = (view?: __esri.MapView, widgetPosition?: string, layerInfos?: __esri.LegendLayerInfos[]) => {
    if (!view) return
    legend = new Legend({
      view,
      basemapLegendVisible: false,
      layerInfos: layerInfos || []
    })
    legendExpand = new Expand({
      view,
      expandTooltip: '图例',
      content: legend
    })
    legendExpand && view.ui?.add(legendExpand, widgetPosition || 'top-right')
    watchLegend(view)
    return legend
  }
  const watchLegend = (view?: __esri.MapView) => {
    view?.map.allLayers.on('change', () => {
      if (!legend) return
      legend.layerInfos = view.map?.allLayers
        .map(item => {
          return {
            title: item.title,
            layer: item
          }
        })
        ?.toArray() || []
    })
  }
  const destroy = () => {
    legend?.destroy()
    legendExpand?.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init
  }
}
export default useLegend
