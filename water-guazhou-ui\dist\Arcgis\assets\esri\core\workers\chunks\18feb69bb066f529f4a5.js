"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[665,5935],{45378:(e,t,i)=>{i.r(t),i.d(t,{default:()=>Xt});var r=i(43697),s=i(51773),n=i(70586),o=i(16453),a=i(95330),l=i(5600),u=i(75215),p=(i(67676),i(52011)),c=i(30556),d=i(87085),m=i(66577),h=i(38171),y=i(30030),f=i(3172),g=i(20102),b=i(35454),R=i(22974),w=i(92604),v=i(17445),_=i(17452),C=i(71715),S=i(21506),x=i(35956),I=i(96674),F=i(40297);const j={StretchFunction:{arguments:{ComputeGamma:{isDataset:!1,isPublic:!1,name:"ComputeGam<PERSON>",type:"RasterFunctionVariable",value:!1},DRA:{isDataset:!1,isPublic:!1,name:"DRA",type:"RasterFunctionVariable",value:!1},EstimateStatsHistogram:{isDataset:!1,isPublic:!1,name:"EstimateStatsHistogram",type:"RasterFunctionVariable",value:!1},Gamma:{displayName:"Gamma",isDataset:!1,isPublic:!1,name:"Gamma",type:"RasterFunctionVariable"},Histograms:{isDataset:!1,isPublic:!1,name:"Histograms",type:"RasterFunctionVariable"},Max:{isDataset:!1,isPublic:!1,name:"Max",type:"RasterFunctionVariable",value:255},MaxPercent:{isDataset:!1,isPublic:!1,name:"MaxPercent",type:"RasterFunctionVariable",value:.5},Min:{isDataset:!1,isPublic:!1,name:"Min",type:"RasterFunctionVariable",value:0},MinPercent:{isDataset:!1,isPublic:!1,name:"MinPercent",type:"RasterFunctionVariable",value:.25},NumberOfStandardDeviations:{isDataset:!1,isPublic:!1,name:"NumberOfStandardDeviation",type:"RasterFunctionVariable",value:2},Raster:{isDataset:!0,isPublic:!1,name:"Raster",type:"RasterFunctionVariable"},SigmoidStrengthLevel:{isDataset:!1,isPublic:!1,name:"SigmoidStrengthLevel",type:"RasterFunctionVariable",value:2},Statistics:{isDataset:!1,isPublic:!1,name:"Statistics",type:"RasterFunctionVariable"},StretchType:{isDataset:!1,isPublic:!1,name:"StretchType",type:"RasterFunctionVariable",value:0},type:"StretchFunctionArguments",UseGamma:{isDataset:!1,isPublic:!1,name:"UseGamma",type:"RasterFunctionVariable",value:!1}},description:"Enhances an image by adjusting the range of values displayed. This does not alter the underlying pixel values. If a pixel has a value outside of the specified range, it will appear as either the minimum or maximum value.",function:{description:"Enhances an image by adjusting the range of values displayed. This does not alter the underlying pixel values. If a pixel has a value outside of the specified range, it will appear as either the minimum or maximum value.",name:"Stretch",pixelType:"UNKNOWN",type:"StretchFunction"},functionType:0,name:"Stretch",thumbnail:""},RemapFunction:{name:"Remap",description:"Changes pixel values by assigning new values to ranges of pixel values or using an external table.",function:{type:"RemapFunction",pixelType:"UNKNOWN",name:"Remap",description:"Changes pixel values by assigning new values to ranges of pixel values or using an external table."},arguments:{Raster:{name:"Raster",isPublic:!1,isDataset:!0,type:"RasterFunctionVariable"},UseTable:{name:"UseTable",isPublic:!1,isDataset:!1,value:!1,type:"RasterFunctionVariable"},InputRanges:{name:"InputRanges",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable",displayName:"Input Ranges"},OutputValues:{name:"OutputValues",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable",displayName:"Output Values"},NoDataRanges:{name:"NoDataRanges",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable",displayName:"NoData Ranges"},Table:{name:"Table",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},InputField:{name:"InputField",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},OutputField:{name:"OutputField",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},InputMaxField:{name:"InputMaxField",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},RemapTableType:{name:"RemapTableType",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},AllowUnmatched:{name:"AllowUnmatched",isPublic:!1,isDataset:!1,value:!0,type:"RasterFunctionVariable"},type:"RemapFunctionArguments"},functionType:0,thumbnail:""},ColormapFunction:{name:"Colormap",description:"Changes pixel values to display the raster data as either a grayscale or a red, green, blue (RGB) image, based on a colormap or a color ramp.",function:{type:"ColormapFunction",pixelType:"UNKNOWN",name:"Colormap",description:"Changes pixel values to display the raster data as either a grayscale or a red, green, blue (RGB) image, based on a colormap or a color ramp."},arguments:{Raster:{name:"Raster",isPublic:!1,isDataset:!0,type:"RasterFunctionVariable"},ColorSchemeType:{name:"ColorSchemeType",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},Colormap:{name:"Colormap",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},ColormapName:{name:"ColormapName",isPublic:!1,isDataset:!1,value:"Gray",type:"RasterFunctionVariable"},ColorRamp:{name:"ColorRamp",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},type:"ColormapFunctionArguments"},functionType:0,thumbnail:""},ShadedReliefFunction:{name:"Shaded Relief",description:"Creates a multiband, color coded, 3D representation of the surface, with the sun's relative position taken into account for shading the image.",function:{type:"ShadedReliefFunction",pixelType:"UNKNOWN",name:"Shaded Relief",description:"Creates a multiband, color coded, 3D representation of the surface, with the sun's relative position taken into account for shading the image."},arguments:{Raster:{name:"Raster",isPublic:!1,isDataset:!0,type:"RasterFunctionVariable"},ColorSchemeType:{name:"ColorSchemeType",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},ColorRamp:{name:"ColorRamp",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},HillshadeType:{name:"HillshadeType",isPublic:!1,isDataset:!1,value:0,type:"RasterFunctionVariable"},Colormap:{name:"Colormap",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},Azimuth:{name:"Azimuth",isPublic:!1,isDataset:!1,value:315,type:"RasterFunctionVariable"},Altitude:{name:"Altitude",isPublic:!1,isDataset:!1,value:45,type:"RasterFunctionVariable"},SlopeType:{name:"SlopeType",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},ZFactor:{name:"ZFactor",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},PSPower:{name:"PSPower",isPublic:!1,isDataset:!1,value:.664,type:"RasterFunctionVariable"},PSZFactor:{name:"PSZFactor",isPublic:!1,isDataset:!1,value:.024,type:"RasterFunctionVariable"},RemoveEdgeEffect:{name:"RemoveEdgeEffect",isPublic:!1,isDataset:!1,value:!1,type:"RasterFunctionVariable"},type:"ShadedReliefFunctionArguments"},functionType:0,thumbnail:""},HillshadeFunction:{name:"Hillshade",description:"Creates a 3D representation of the surface, with the sun's relative position taken into account for shading the image",function:{type:"HillshadeFunction",pixelType:"UNKNOWN",name:"Hillshade",description:"Creates a 3D representation of the surface, with the sun's relative position taken into account for shading the image"},arguments:{DEM:{name:"DEM",isPublic:!1,isDataset:!0,type:"RasterFunctionVariable"},HillshadeType:{name:"HillshadeType",isPublic:!1,isDataset:!1,value:0,type:"RasterFunctionVariable"},Azimuth:{name:"Azimuth",isPublic:!1,isDataset:!1,value:315,type:"RasterFunctionVariable"},Altitude:{name:"Altitude",isPublic:!1,isDataset:!1,value:45,type:"RasterFunctionVariable"},SlopeType:{name:"SlopeType",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},ZFactor:{name:"ZFactor",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},PSPower:{name:"PSPower",isPublic:!1,isDataset:!1,value:.664,type:"RasterFunctionVariable"},PSZFactor:{name:"PSZFactor",isPublic:!1,isDataset:!1,value:.024,type:"RasterFunctionVariable"},RemoveEdgeEffect:{name:"RemoveEdgeEffect",isPublic:!1,isDataset:!1,value:!1,type:"RasterFunctionVariable"},type:"HillshadeFunctionArguments"},functionType:0,thumbnail:""},ResampleFunction:{name:"Resample",description:"Changes the cell size of a raster.",function:{type:"ResampleFunction",pixelType:"UNKNOWN",name:"Resample",description:"Changes the cell size of a raster."},arguments:{Raster:{name:"Raster",isPublic:!1,isDataset:!0,type:"RasterFunctionVariable"},ResamplingType:{name:"ResamplingType",isPublic:!1,isDataset:!1,value:0,type:"RasterFunctionVariable"},InputCellSize:{name:"InputCellsize",isPublic:!1,isDataset:!1,value:{x:0,y:0},type:"RasterFunctionVariable"},OutputCellSize:{name:"OutputCellsize",isPublic:!1,isDataset:!1,value:{x:0,y:0},type:"RasterFunctionVariable"},type:"ResampleFunctionArguments"},functionType:0,thumbnail:""}};var T=i(81578),D=i(93010),N=i(75509),P=i(23847);const O=new Set(["u1","u2","u4","u8","s8","u16","s16"]),M={simple_scalar:"Simple Scalar",wind_barb:"Wind Barb",single_arrow:"Single Arrow",beaufort_kn:"Beaufort Wind (Knots)",beaufort_m:"Beaufort Wind (MetersPerSecond)",ocean_current_m:"Ocean Current (MetersPerSecond)",ocean_current_kn:"Ocean Current (Knots)"},V=new Set(["raster-stretch","unique-value","class-breaks","raster-shaded-relief","vector-field","raster-colormap"]);function A(e){return V.has(e.type)}function J(e,t){if(!e||!t)return(0,R.d9)(e||t);const i=(0,R.d9)(e);if(i.rasterFunctionDefinition&&t.rasterFunctionDefinition){const e=t.rasterFunctionDefinition;(e.thumbnail||e.thumbnailEx)&&(e.thumbnail=e.thumbnailEx=void 0),Z(i.rasterFunctionDefinition.arguments,t)}else"none"!==t.functionName?.toLowerCase()&&(q(i.functionArguments).Raster=t);return i}function Z(e,t){for(const i in e)"raster"===i.toLowerCase()&&("RasterFunctionVariable"===e[i].type?(e[i]=t.rasterFunctionDefinition,e[i].type="RasterFunctionTemplate"):"RasterFunctionTemplate"===e[i].type&&Z(e[i].arguments,t))}function E(e){const t=(0,R.d9)(j[e.functionName+"Function"]),i=e.functionArguments;for(const e in i)"raster"===e.toLowerCase()?(t.arguments[e]=E(i[e]),t.arguments[e].type="RasterFunctionTemplate"):"colormap"===e.toLowerCase()?(t.arguments[e].value=L(i[e]),t.arguments.ColorSchemeType.value=0):t.arguments[e].value=i[e];return t}function q(e){const t=e?.Raster;return t&&"esri.layers.support.RasterFunction"===t.declaredClass?q(t.functionArguments):e}const z={none:0,standardDeviation:3,histogramEqualization:4,minMax:5,percentClip:6,sigmoid:9};function G(e,t){const i=O.has(t)?(0,T.r)(t):null;return i&&e.push([Math.floor(i[0]-1),0,0,0],[Math.ceil(i[1]+1),0,0,0]),e}function H(e){if((0,n.Wi)(e))return;const{fields:t}=e,i=t&&t.find((e=>e&&e.name&&"value"===e.name.toLowerCase()));return i&&i.name}function B(e){const t=[];return e?.forEach((e=>{const i=e;if(Array.isArray(i))t.push(i);else{if(null==i.min||null==i.max)return;const e=[i.min,i.max,i.avg||0,i.stddev||0];t.push(e)}})),t}function L(e){const t=[],i=[];return e.forEach((e=>{t.push(e[0]),i.push((0,D.io)([...e.slice(1),255]))})),{type:"RasterColormap",values:t,colors:i}}var U,k=i(90578),Q=i(94139);const W=(0,b.w)()({MT_FIRST:"first",MT_LAST:"last",MT_MIN:"min",MT_MAX:"max",MT_MEAN:"mean",MT_BLEND:"blend",MT_SUM:"sum"}),$=(0,b.w)()({esriMosaicNone:"none",esriMosaicCenter:"center",esriMosaicNadir:"nadir",esriMosaicViewpoint:"viewpoint",esriMosaicAttribute:"attribute",esriMosaicLockRaster:"lock-raster",esriMosaicNorthwest:"northwest",esriMosaicSeamline:"seamline"});let X=U=class extends I.wq{constructor(e){super(e),this.ascending=!0,this.itemRenderingRule=null,this.lockRasterIds=null,this.method=null,this.multidimensionalDefinition=null,this.objectIds=null,this.operation=null,this.sortField=null,this.sortValue=null,this.viewpoint=null,this.where=null}readAscending(e,t){return null!=t.ascending?t.ascending:null==t.sortAscending||t.sortAscending}readMethod(e,t){return function(e){let t;switch(e?e.toLowerCase().replace("esrimosaic",""):""){case"byattribute":case"attribute":t="esriMosaicAttribute";break;case"lockraster":t="esriMosaicLockRaster";break;case"center":t="esriMosaicCenter";break;case"northwest":t="esriMosaicNorthwest";break;case"nadir":t="esriMosaicNadir";break;case"viewpoint":t="esriMosaicViewpoint";break;case"seamline":t="esriMosaicSeamline";break;default:t="esriMosaicNone"}return $.fromJSON(t)}(t.mosaicMethod||t.defaultMosaicMethod)}writeMultidimensionalDefinition(e,t,i){null!=e&&(e=e.filter((({variableName:e,dimensionName:t})=>e&&"*"!==e||t))).length&&(t[i]=e.map((e=>e.toJSON())))}readOperation(e,t){const i=t.mosaicOperation,r=t.mosaicOperator&&t.mosaicOperator.toLowerCase(),s=i||(r?W.toJSON(r):null);return W.fromJSON(s)||"first"}castSortValue(e){return null==e||"string"==typeof e||"number"==typeof e?e:`${e}`}clone(){return new U({ascending:this.ascending,itemRenderingRule:(0,R.d9)(this.itemRenderingRule),lockRasterIds:(0,R.d9)(this.lockRasterIds),method:this.method,multidimensionalDefinition:(0,R.d9)(this.multidimensionalDefinition),objectIds:(0,R.d9)(this.objectIds),operation:this.operation,sortField:this.sortField,sortValue:this.sortValue,viewpoint:(0,R.d9)(this.viewpoint),where:this.where})}};(0,r._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],X.prototype,"ascending",void 0),(0,r._)([(0,C.r)("ascending",["ascending","sortAscending"])],X.prototype,"readAscending",null),(0,r._)([(0,l.Cb)({type:F.Z,json:{write:!0}})],X.prototype,"itemRenderingRule",void 0),(0,r._)([(0,l.Cb)({type:[u.z8],json:{write:{overridePolicy(){return{enabled:"lock-raster"===this.method}}}}})],X.prototype,"lockRasterIds",void 0),(0,r._)([(0,l.Cb)({type:String,json:{type:$.jsonValues,write:{target:"mosaicMethod",writer:$.write}}})],X.prototype,"method",void 0),(0,r._)([(0,C.r)("method",["mosaicMethod","defaultMosaicMethod"])],X.prototype,"readMethod",null),(0,r._)([(0,l.Cb)({type:[x.Z],json:{write:!0}})],X.prototype,"multidimensionalDefinition",void 0),(0,r._)([(0,c.c)("multidimensionalDefinition")],X.prototype,"writeMultidimensionalDefinition",null),(0,r._)([(0,l.Cb)({type:[u.z8],json:{name:"fids",write:!0}})],X.prototype,"objectIds",void 0),(0,r._)([(0,l.Cb)({json:{type:W.jsonValues,read:{reader:W.read},write:{target:"mosaicOperation",writer:W.write}}})],X.prototype,"operation",void 0),(0,r._)([(0,C.r)("operation",["mosaicOperation","mosaicOperator"])],X.prototype,"readOperation",null),(0,r._)([(0,l.Cb)({type:String,json:{write:{overridePolicy(){return{enabled:"attribute"===this.method}}}}})],X.prototype,"sortField",void 0),(0,r._)([(0,l.Cb)({type:[String,Number],json:{write:{allowNull:!0,overridePolicy(){return{enabled:"attribute"===this.method,allowNull:!0}}}}})],X.prototype,"sortValue",void 0),(0,r._)([(0,k.p)("sortValue")],X.prototype,"castSortValue",null),(0,r._)([(0,l.Cb)({type:Q.Z,json:{write:!0}})],X.prototype,"viewpoint",void 0),(0,r._)([(0,l.Cb)({type:String,json:{write:!0}})],X.prototype,"where",void 0),X=U=(0,r._)([(0,p.j)("esri.layers.support.MosaicRule")],X);const K=X;var Y=i(69608);let ee=class extends I.wq{constructor(){super(...arguments),this.layer=null,this.compression=void 0,this.pixelType=void 0,this.lercVersion=2}get adjustAspectRatio(){return this.layer.adjustAspectRatio}writeAdjustAspectRatio(e,t,i){this.layer.version<10.3||(t[i]=e)}get bandIds(){return this.layer.bandIds}get compressionQuality(){return this.layer.compressionQuality}writeCompressionQuality(e,t,i){this.format&&this.format.toLowerCase().includes("jpg")&&null!=e&&(t[i]=e)}get compressionTolerance(){return this.layer.compressionTolerance}writeCompressionTolerance(e,t,i){"lerc"===this.format&&null!=e&&(t[i]=e)}get format(){return"vector-field"===this.layer.renderer?.type?"lerc":this.layer.format}get interpolation(){return this.layer.interpolation}get noData(){return this.layer.noData}get noDataInterpretation(){return this.layer.noDataInterpretation}writeLercVersion(e,t,i){"lerc"===this.format&&this.layer.version>=10.5&&(t[i]=e)}get version(){const e=this.layer;return e.commitProperty("bandIds"),e.commitProperty("format"),e.commitProperty("compressionQuality"),e.commitProperty("compressionTolerance"),e.commitProperty("interpolation"),e.commitProperty("noData"),e.commitProperty("noDataInterpretation"),e.commitProperty("mosaicRule"),e.commitProperty("renderingRule"),e.commitProperty("adjustAspectRatio"),e.commitProperty("pixelFilter"),e.commitProperty("definitionExpression"),e.commitProperty("multidimensionalSubset"),(this._get("version")||0)+1}set version(e){this._set("version",e)}get mosaicRule(){const e=this.layer;let t=e.mosaicRule;const i=e.definitionExpression;return t?i&&i!==t.where&&(t=t.clone(),t.where=i):i&&(t=new K({where:i})),t}get renderingRule(){const e=this.layer;let t=e.renderingRule;const i=e.pixelFilter,r=!e.format||e.format.includes("jpg")||e.format.includes("png");t=this._addResampleRasterFunction(t);const s=e.multidimensionalSubset?.areaOfInterest;return s&&(t=this._addClipFunction(t,s)),r&&!i&&"vector-field"!==e.renderer?.type&&(t=this.combineRendererWithRenderingRule(t)),t}combineRendererWithRenderingRule(e){const t=this.layer,{rasterInfo:i,renderer:r}=t;return e=e||t.renderingRule,r&&A(r)?J(function(e,t){switch(t=t||{},e.type){case"raster-stretch":return function(e,t){const i=t.convertToRFT,r=new F.Z;r.functionName="Stretch";const s=z[N.v.toJSON(e.stretchType)],n="u8",o={StretchType:s,Statistics:B(e.statistics??[]),DRA:e.dynamicRangeAdjustment,UseGamma:e.useGamma,Gamma:e.gamma,ComputeGamma:e.computeGamma};if(null!=e.outputMin&&(o.Min=e.outputMin),null!=e.outputMax&&(o.Max=e.outputMax),s===z.standardDeviation?(o.NumberOfStandardDeviations=e.numberOfStandardDeviations,r.outputPixelType=n):s===z.percentClip?(o.MinPercent=e.minPercent,o.MaxPercent=e.maxPercent,r.outputPixelType=n):s===z.minMax?r.outputPixelType=n:s===z.sigmoid&&(o.SigmoidStrengthLevel=e.sigmoidStrengthLevel),r.functionArguments=o,r.variableName="Raster",e.colorRamp){const s=e.colorRamp,n=new F.Z;if(i)n.functionArguments={ColorRamp:(0,D.pM)(s)};else{const i=(0,D.Uh)(s);if(i)n.functionArguments={colorRamp:i};else if(!t.convertColorRampToColormap||"algorithmic"!==s.type&&"multipart"!==s.type){const t=e.colorRamp.toJSON();"algorithmic"===t.type?t.algorithm=t.algorithm||"esriCIELabAlgorithm":"multipart"===t.type&&t.colorRamps?.length&&t.colorRamps.forEach((e=>e.algorithm=e.algorithm||"esriCIELabAlgorithm")),n.functionArguments={colorRamp:t}}else n.functionArguments={Colormap:(0,D.Jw)(s)}}return n.variableName="Raster",n.functionName="Colormap",n.functionArguments.Raster=r,i?new F.Z({rasterFunctionDefinition:E(n)}):n}return i?new F.Z({rasterFunctionDefinition:E(r)}):r}(e,t);case"class-breaks":return function(e,t){const i=[],r=[],s=[],o=[],a=1e-6,{pixelType:l,rasterAttributeTable:u}=t,p=(0,n.Wi)(u)?null:u.features,c=H(u);if(c&&p&&Array.isArray(p)&&e.classBreakInfos){e.classBreakInfos.forEach(((t,i)=>{const r=t.symbol?.color;let s;r?.a&&null!=t.minValue&&null!=t.maxValue&&p.forEach((n=>{null!=t.minValue&&null!=t.maxValue&&(s=n.attributes[e.field],(s>=t.minValue&&s<t.maxValue||i===e.classBreakInfos.length-1&&s>=t.minValue)&&o.push([n.attributes[c],r.r,r.g,r.b]))}))}));const i=l?G(o,l):o,r=new F.Z;return r.functionName="Colormap",r.functionArguments={},r.functionArguments.Colormap=i,r.variableName="Raster",t.convertToRFT?new F.Z({rasterFunctionDefinition:E(r)}):r}e.classBreakInfos.forEach(((e,t)=>{if(null==e.minValue||null==e.maxValue)return;const n=e.symbol&&e.symbol.color;n?.a?(0===t?i.push(e.minValue,e.maxValue+a):i.push(e.minValue+a,e.maxValue+a),r.push(t),o.push([t,n.r,n.g,n.b])):s.push(e.minValue,e.maxValue)}));const d=l?G(o,l):o,m=new F.Z;m.functionName="Remap",m.functionArguments={InputRanges:i,OutputValues:r,NoDataRanges:s},m.variableName="Raster";const h=new F.Z;return h.functionName="Colormap",h.functionArguments={Colormap:d,Raster:m},t.convertToRFT?new F.Z({rasterFunctionDefinition:E(h)}):h}(e,t);case"unique-value":return function(e,t){const i=[],{pixelType:r,rasterAttributeTable:s}=t,o=(0,n.Wi)(s)?null:s.features,a=H(s),l=e.defaultSymbol?.color?.toRgb(),u=e.uniqueValueInfos;if(u)if(o){if(a){const t=new Map;u.forEach((e=>{const i=e.value,r=e.symbol?.color;null!=i&&r&&r.a&&t.set(String(i),r.toRgb())}));const r=e.field;o.forEach((({attributes:e})=>{const s=String(e[r]),n=e[a],o=t.get(s);o?i.push([n,...o]):l&&i.push([n,...l])}))}}else for(let e=0;e<u.length;e++){const t=u[e],r=t.symbol?.color,s=+t.value;if(r?.a){if(isNaN(s))return null;i.push([s,r.r,r.g,r.b])}}const p=r&&i.length>0?G(i,r):i,c=new F.Z;return c.functionName="Colormap",c.functionArguments={},c.functionArguments.Colormap=p,c.variableName="Raster",t.convertToRFT?new F.Z({rasterFunctionDefinition:E(c)}):c}(e,t);case"raster-colormap":return function(e,t){const i=e.extractColormap();if(!i||0===i.length)return null;const{pixelType:r}=t,s=r?G(i,r):i,n=new F.Z;return n.functionName="Colormap",n.functionArguments={},n.functionArguments.Colormap=s,t.convertToRFT?new F.Z({rasterFunctionDefinition:E(n)}):n}(e,t);case"vector-field":return function(e,t){const i=new F.Z;i.functionName="VectorFieldRenderer";const{dataType:r,bandProperties:s}=t,n="vector-uv"===r;let o,a;s&&2===s.length&&(o=s.map((e=>e.BandName.toLowerCase())).indexOf("magnitude"),a=s.map((e=>e.BandName.toLowerCase())).indexOf("direction")),-1!==o&&null!==o||(o=0,a=1);const l="arithmetic"===e.rotationType?1:2,u="flow-from"===e.flowRepresentation?0:1,p=e.visualVariables?e.visualVariables.find((e=>"Magnitude"===e.field)):new P.Z,c={magnitudeBandID:o,directionBandID:a,isUVComponents:n,referenceSystem:l,massFlowAngleRepresentation:u,symbolTileSize:50,symbolTileSizeUnits:100,calculationMethod:"Vector Average",symbologyName:M[e.style.toLowerCase().replace("-","_")],minimumMagnitude:p.minDataValue,maximumMagnitude:p.maxDataValue,minimumSymbolSize:p.minSize,maximumSymbolSize:p.maxSize};return i.functionArguments=c,t.convertToRFT?new F.Z({rasterFunctionDefinition:E(i)}):i}(e,t);case"raster-shaded-relief":return function(e,t){const i=t.convertToRFT;if("elevation"!==t.dataType&&("generic"!==t.dataType||1!==t.bandCount||"s16"!==t.pixelType&&"s32"!==t.pixelType&&"f32"!==t.pixelType&&"f64"!==t.pixelType))return new F.Z;const r=new F.Z;r.functionName="Hillshade";const s="traditional"===e.hillshadeType?0:1,n="none"===e.scalingType?1:3,o={HillshadeType:s,SlopeType:n,ZFactor:e.zFactor};return 0===s&&(o.Azimuth=e.azimuth,o.Altitude=e.altitude),3===n&&(o.PSPower=e.pixelSizePower,o.PSZFactor=e.pixelSizeFactor),r.functionArguments=o,r.variableName="Raster",e.colorRamp&&(r.functionName="ShadedRelief",i?o.ColorRamp=(0,D.pM)(e.colorRamp):o.Colormap=(0,D.Jw)(e.colorRamp)),i?new F.Z({rasterFunctionDefinition:E(r)}):r}(e,t);case"flow":throw new Error("Unsupported rendering rule.")}}(r,{rasterAttributeTable:i.attributeTable,pixelType:i.pixelType,dataType:i.dataType,bandProperties:i.keyProperties?.BandProperties,convertColorRampToColormap:t.version<10.6,convertToRFT:!!e?.rasterFunctionDefinition,bandCount:i.bandCount}),e):e}_addResampleRasterFunction(e){if("vector-field"!==this.layer.renderer?.type||"Resample"===e?.functionName)return e;const t="esriImageServiceDataTypeVector-UV"===this.layer.serviceDataType?7:10,i=this.layer.serviceRasterInfo.pixelSize;let r=new F.Z({functionName:"Resample",functionArguments:{ResamplingType:t,InputCellSize:i}});return r=e?.rasterFunctionDefinition?new F.Z({rasterFunctionDefinition:E(r)}):r,J(r,e)}_addClipFunction(e,t){return J(new F.Z({functionName:"Clip",functionArguments:{ClippingGeometry:t.toJSON(),ClippingType:1}}),e)}};(0,r._)([(0,l.Cb)()],ee.prototype,"layer",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"adjustAspectRatio",null),(0,r._)([(0,c.c)("adjustAspectRatio")],ee.prototype,"writeAdjustAspectRatio",null),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"bandIds",null),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"compression",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"compressionQuality",null),(0,r._)([(0,c.c)("compressionQuality")],ee.prototype,"writeCompressionQuality",null),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"compressionTolerance",null),(0,r._)([(0,c.c)("compressionTolerance")],ee.prototype,"writeCompressionTolerance",null),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"format",null),(0,r._)([(0,l.Cb)({type:String,json:{read:{reader:Y.c.read},write:{writer:Y.c.write}}})],ee.prototype,"interpolation",null),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"noData",null),(0,r._)([(0,l.Cb)({type:String,json:{read:{reader:Y.k.read},write:{writer:Y.k.write}}})],ee.prototype,"noDataInterpretation",null),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"pixelType",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"lercVersion",void 0),(0,r._)([(0,c.c)("lercVersion")],ee.prototype,"writeLercVersion",null),(0,r._)([(0,l.Cb)({type:Number})],ee.prototype,"version",null),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"mosaicRule",null),(0,r._)([(0,l.Cb)({json:{write:!0}})],ee.prototype,"renderingRule",null),ee=(0,r._)([(0,p.j)("esri.layers.mixins.ExportImageServiceParameters")],ee);var te=i(1231),ie=i(99514),re=i(90082),se=i(29876),ne=i(5847),oe=i(48526),ae=i(88281),le=i(99815),ue=i(75993),pe=i(80676),ce=i(67058),de=i(72758),me=i(16306),he=i(11282),ye=i(82971);let fe=class extends I.wq{constructor(e){super(e),this.north=null,this.up=null,this.spatialReference=null}};(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],fe.prototype,"north",void 0),(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],fe.prototype,"up",void 0),(0,r._)([(0,l.Cb)({type:ye.Z,json:{write:!0}})],fe.prototype,"spatialReference",void 0),fe=(0,r._)([(0,p.j)("esri.rest.support.ImageAngleResult")],fe);const ge=fe;var be=i(67900);let Re=class extends I.wq{constructor(){super(...arguments),this.value=null,this.displayValue=null,this.uncertainty=null}};(0,r._)([(0,l.Cb)({type:Number,json:{read:!0,write:!0}})],Re.prototype,"value",void 0),(0,r._)([(0,l.Cb)({type:String,json:{read:!0,write:!0}})],Re.prototype,"displayValue",void 0),(0,r._)([(0,l.Cb)({type:Number,json:{read:!0,write:!0}})],Re.prototype,"uncertainty",void 0),Re=(0,r._)([(0,p.j)("esri.rest.support.ImageMeasureResultValue")],Re);let we=class extends Re{constructor(){super(...arguments),this.unit=null}};(0,r._)([(0,l.Cb)({type:String,json:{read:be.Jo.read,write:be.Jo.write}})],we.prototype,"unit",void 0),we=(0,r._)([(0,p.j)("esri.rest.support.ImageMeasureResultLengthValue")],we);let ve=class extends Re{constructor(){super(...arguments),this.unit=null}};(0,r._)([(0,l.Cb)({type:String,json:{read:be.gV.read,write:be.gV.write}})],ve.prototype,"unit",void 0),ve=(0,r._)([(0,p.j)("esri.rest.support.ImageMeasureResultAreaValue")],ve);let _e=class extends Re{constructor(){super(...arguments),this.unit=null}};(0,r._)([(0,l.Cb)({type:String,json:{read:be.CN.read,write:be.CN.write}})],_e.prototype,"unit",void 0),_e=(0,r._)([(0,p.j)("esri.rest.support.ImageMeasureResultAngleValue")],_e);let Ce=class extends I.wq{constructor(){super(...arguments),this.name=null,this.sensorName=null}};(0,r._)([(0,l.Cb)({type:String,json:{read:!0,write:!0}})],Ce.prototype,"name",void 0),(0,r._)([(0,l.Cb)({type:String,json:{read:!0,write:!0}})],Ce.prototype,"sensorName",void 0),Ce=(0,r._)([(0,p.j)("esri.rest.support.BaseImageMeasureResult")],Ce);let Se=class extends Ce{constructor(){super(...arguments),this.area=null,this.perimeter=null}};(0,r._)([(0,l.Cb)({type:ve,json:{read:!0,write:!0}})],Se.prototype,"area",void 0),(0,r._)([(0,l.Cb)({type:we,json:{read:!0,write:!0}})],Se.prototype,"perimeter",void 0),Se=(0,r._)([(0,p.j)("esri.rest.support.ImageAreaResult")],Se);const xe=Se;let Ie=class extends Ce{constructor(){super(...arguments),this.distance=null,this.azimuthAngle=null,this.elevationAngle=null}};(0,r._)([(0,l.Cb)({type:we,json:{read:!0,write:!0}})],Ie.prototype,"distance",void 0),(0,r._)([(0,l.Cb)({type:_e,json:{read:!0,write:!0}})],Ie.prototype,"azimuthAngle",void 0),(0,r._)([(0,l.Cb)({type:_e,json:{read:!0,write:!0}})],Ie.prototype,"elevationAngle",void 0),Ie=(0,r._)([(0,p.j)("esri.rest.support.ImageDistanceResult")],Ie);const Fe=Ie;let je=class extends Ce{constructor(){super(...arguments),this.height=null}};(0,r._)([(0,l.Cb)({type:we,json:{read:!0,write:!0}})],je.prototype,"height",void 0),je=(0,r._)([(0,p.j)("esri.rest.support.ImageHeightResult")],je);const Te=je;var De=i(74889);let Ne=class extends I.wq{constructor(){super(...arguments),this.catalogItemVisibilities=null,this.catalogItems=null,this.location=null,this.name=null,this.objectId=null,this.processedValues=null,this.properties=null,this.value=null}};(0,r._)([(0,l.Cb)({json:{write:!0}})],Ne.prototype,"catalogItemVisibilities",void 0),(0,r._)([(0,l.Cb)({type:De.Z,json:{write:!0}})],Ne.prototype,"catalogItems",void 0),(0,r._)([(0,l.Cb)({type:Q.Z,json:{write:!0}})],Ne.prototype,"location",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],Ne.prototype,"name",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],Ne.prototype,"objectId",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],Ne.prototype,"processedValues",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],Ne.prototype,"properties",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],Ne.prototype,"value",void 0),Ne=(0,r._)([(0,p.j)("esri.rest.support.ImageIdentifyResult")],Ne);const Pe=Ne;let Oe=class extends I.wq{constructor(){super(...arguments),this.geometries=null}};(0,r._)([(0,l.Cb)({json:{write:!0}})],Oe.prototype,"geometries",void 0),Oe=(0,r._)([(0,p.j)("esri.rest.support.ImagePixelLocationResult")],Oe);const Me=Oe;let Ve=class extends Ce{constructor(){super(...arguments),this.point=null}};(0,r._)([(0,l.Cb)({type:Q.Z,json:{name:"point.value",read:!0,write:!0}})],Ve.prototype,"point",void 0),Ve=(0,r._)([(0,p.j)("esri.rest.support.ImagePointResult")],Ve);const Ae=Ve;let Je=class extends I.wq{constructor(){super(...arguments),this.attributes=null,this.location=null,this.locationId=null,this.rasterId=null,this.resolution=null,this.pixelValue=null}};(0,r._)([(0,l.Cb)({json:{write:!0}})],Je.prototype,"attributes",void 0),(0,r._)([(0,l.Cb)({type:Q.Z,json:{write:!0}})],Je.prototype,"location",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],Je.prototype,"locationId",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],Je.prototype,"rasterId",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],Je.prototype,"resolution",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],Je.prototype,"pixelValue",void 0),Je=(0,r._)([(0,p.j)("esri.rest.support.ImageSample")],Je);const Ze=Je;let Ee=class extends I.wq{constructor(){super(...arguments),this.samples=null}};(0,r._)([(0,l.Cb)({type:[Ze],json:{write:!0}})],Ee.prototype,"samples",void 0),Ee=(0,r._)([(0,p.j)("esri.rest.support.ImageSampleResult")],Ee);const qe=Ee;function ze(e){const t=e?.time;if(t&&(null!=t.start||null!=t.end)){const i=[];null!=t.start&&i.push(t.start),null==t.end||i.includes(t.end)||i.push(t.end),e.time=i.join(",")}}async function Ge(e,t,i){const r=(0,he.en)(e),s=t.geometry?[t.geometry]:[],o=await(0,me.aX)(s),a=t.toJSON();ze(a);const l=o&&o[0];(0,n.pC)(l)&&(a.geometry=l.toJSON());const u=(0,he.cv)({...r.query,f:"json",...a});return(0,he.lA)(u,i)}async function He(e,t,i){const r=(0,he.en)(e),s=t.geometry?[t.geometry]:[];return(0,me.aX)(s).then((e=>{const s=t.toJSON(),o=e&&e[0];(0,n.pC)(o)&&(s.geometry=JSON.stringify(o.toJSON()));const a=(0,he.cv)({...r.query,f:"json",...s}),l=(0,he.lA)(a,i);return(0,f.default)(r.path+"/identify",l)})).then((e=>Pe.fromJSON(e.data)))}async function Be(e,t,i){const r=await Le(e,t,[t.fromGeometry,t.toGeometry],i);return Fe.fromJSON(r)}async function Le(e,t,i,r){const s=(0,he.en)(e),o=await(0,me.aX)(i),a=t.toJSON();(0,n.pC)(o[0])&&(a.fromGeometry=JSON.stringify(Ue(o[0]))),(0,n.pC)(o[1])&&(a.toGeometry=JSON.stringify(Ue(o[1])));const l=(0,he.cv)({...s.query,f:"json",...a}),u=(0,he.lA)(l,r),{data:p}=await(0,f.default)(s.path+"/measure",u);return p}function Ue(e){const t=e.toJSON();return e.spatialReference?.imageCoordinateSystem&&(t.spatialReference=ke(e.spatialReference)),t}function ke(e){const{imageCoordinateSystem:t}=e;if(t){const{id:e,referenceServiceName:i}=t;return null!=e?i?{icsid:e,icsns:i}:{icsid:e}:{ics:t}}return e.toJSON()}function Qe(e,t){const i=ke(e),{icsid:r,icsns:s,wkid:n}=i;return null!=r?null==s||t?.toLowerCase().includes("/"+s.toLowerCase()+"/")?`0:${r}`:JSON.stringify(i):n?n.toString():JSON.stringify(i)}i(80442),i(10158);var We,$e=i(4967),Xe=(i(68773),i(40330),i(8744),i(98732),i(6570)),Ke=(i(9361),i(65091)),Ye=i(38913),et=(i(58901),i(45091),i(14165)),tt=(i(98326),i(56545),i(41818)),it=i(5396),rt=(i(75935),i(28141),i(26059)),st=i(36679);let nt=We=class extends I.wq{constructor(e){super(e),this.angleNames=null,this.point=null,this.spatialReference=null,this.rasterId=null}clone(){return new We((0,R.d9)({angleNames:this.angleNames,point:this.point,spatialReference:this.spatialReference,rasterId:this.rasterId}))}};(0,r._)([(0,l.Cb)({type:[String],json:{name:"angleName",write:!0}})],nt.prototype,"angleNames",void 0),(0,r._)([(0,l.Cb)({type:Q.Z,json:{write:!0}})],nt.prototype,"point",void 0),(0,r._)([(0,l.Cb)({type:ye.Z,json:{write:!0}})],nt.prototype,"spatialReference",void 0),(0,r._)([(0,l.Cb)({type:u.z8,json:{write:!0}})],nt.prototype,"rasterId",void 0),nt=We=(0,r._)([(0,p.j)("esri.rest.support.ImageAngleParameters")],nt);const ot=nt;var at=i(33955);const lt=new b.X({esriMensurationPoint:"point",esriMensurationCentroid:"centroid",esriMensurationDistanceAndAngle:"distance-and-angle",esriMensurationAreaAndPerimeter:"area-and-perimeter",esriMensurationHeightFromBaseAndTop:"base-and-top",esriMensurationHeightFromBaseAndTopShadow:"base-and-top-shadow",esriMensurationHeightFromTopAndTopShadow:"top-and-top-shadow",esriMensurationPoint3D:"point-3D",esriMensurationCentroid3D:"centroid-3D",esriMensurationDistanceAndAngle3D:"distance-and-angle-3D",esriMensurationAreaAndPerimeter3D:"area-and-perimeter-3D"});let ut=class extends I.wq{constructor(){super(...arguments),this.type=null,this.measureOperation=null,this.mosaicRule=null,this.renderingRule=null,this.pixelSize=null,this.raster=void 0}};var pt;(0,r._)([(0,l.Cb)()],ut.prototype,"type",void 0),(0,r._)([(0,l.Cb)({type:lt.apiValues,json:{read:lt.read,write:lt.write}})],ut.prototype,"measureOperation",void 0),(0,r._)([(0,l.Cb)({type:K,json:{write:!0}})],ut.prototype,"mosaicRule",void 0),(0,r._)([(0,l.Cb)({type:F.Z,json:{write:!0}})],ut.prototype,"renderingRule",void 0),(0,r._)([(0,l.Cb)({type:Q.Z,json:{write:!0}})],ut.prototype,"pixelSize",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],ut.prototype,"raster",void 0),ut=(0,r._)([(0,p.j)("esri.rest.support.BaseImageMeasureParameters")],ut);let ct=pt=class extends ut{constructor(){super(...arguments),this.type="area-perimeter",this.geometry=null,this.is3D=!1,this.linearUnit="meters",this.areaUnit="square-meters"}writeGeometry(e,t,i){null!=e&&(t.geometryType=(0,at.Ji)(e),t[i]=e.toJSON())}get measureOperation(){return this.is3D?"area-and-perimeter-3D":"area-and-perimeter"}clone(){return new pt((0,R.d9)({geometry:this.geometry,is3D:this.is3D,linearUnit:this.linearUnit,areaUnit:this.areaUnit,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,raster:this.raster}))}};(0,r._)([(0,l.Cb)({types:m.qM,json:{name:"fromGeometry",read:!0,write:!0}})],ct.prototype,"geometry",void 0),(0,r._)([(0,c.c)("geometry")],ct.prototype,"writeGeometry",null),(0,r._)([(0,l.Cb)({type:lt.apiValues,json:{write:lt.write}})],ct.prototype,"measureOperation",null),(0,r._)([(0,l.Cb)({json:{read:!0}})],ct.prototype,"is3D",void 0),(0,r._)([(0,l.Cb)({type:String,json:{read:be.Jo.read,write:be.Jo.write}})],ct.prototype,"linearUnit",void 0),(0,r._)([(0,l.Cb)({type:String,json:{read:be.gV.read,write:be.gV.write}})],ct.prototype,"areaUnit",void 0),ct=pt=(0,r._)([(0,p.j)("esri.rest.support.ImageAreaParameters")],ct);const dt=ct;var mt;let ht=mt=class extends ut{constructor(){super(...arguments),this.type="distance-angle",this.fromGeometry=null,this.toGeometry=null,this.is3D=!1,this.linearUnit="meters",this.angularUnit="degrees"}writeFromGeometry(e,t,i){null!=e&&(t.geometryType=(0,at.Ji)(e),t[i]=e.toJSON())}get measureOperation(){return this.is3D?"distance-and-angle-3D":"distance-and-angle"}clone(){return new mt((0,R.d9)({fromGeometry:this.fromGeometry,toGeometry:this.toGeometry,is3D:this.is3D,linearUnit:this.linearUnit,angularUnit:this.angularUnit,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,raster:this.raster}))}};(0,r._)([(0,l.Cb)({type:Q.Z,json:{read:!0,write:!0}})],ht.prototype,"fromGeometry",void 0),(0,r._)([(0,c.c)("fromGeometry")],ht.prototype,"writeFromGeometry",null),(0,r._)([(0,l.Cb)({type:Q.Z,json:{read:!0,write:!0}})],ht.prototype,"toGeometry",void 0),(0,r._)([(0,l.Cb)({type:lt.apiValues,json:{write:lt.write}})],ht.prototype,"measureOperation",null),(0,r._)([(0,l.Cb)({json:{read:!0}})],ht.prototype,"is3D",void 0),(0,r._)([(0,l.Cb)({type:String,json:{read:be.Jo.read,write:be.Jo.write}})],ht.prototype,"linearUnit",void 0),(0,r._)([(0,l.Cb)({type:String,json:{read:be.CN.read,write:be.CN.write}})],ht.prototype,"angularUnit",void 0),ht=mt=(0,r._)([(0,p.j)("esri.rest.support.ImageDistanceParameters")],ht);const yt=ht;var ft;let gt=ft=class extends ut{constructor(){super(...arguments),this.type="height",this.fromGeometry=null,this.toGeometry=null,this.operationType="base-and-top",this.linearUnit="meters"}writeFromGeometry(e,t,i){null!=e&&(t.geometryType=(0,at.Ji)(e),t[i]=e.toJSON())}get measureOperation(){return this.operationType}clone(){return new ft((0,R.d9)({fromGeometry:this.fromGeometry,toGeometry:this.toGeometry,operationType:this.operationType,linearUnit:this.linearUnit,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,raster:this.raster}))}};(0,r._)([(0,l.Cb)({type:Q.Z,json:{read:!0}})],gt.prototype,"fromGeometry",void 0),(0,r._)([(0,c.c)("fromGeometry")],gt.prototype,"writeFromGeometry",null),(0,r._)([(0,l.Cb)({type:Q.Z,json:{read:!0,write:!0}})],gt.prototype,"toGeometry",void 0),(0,r._)([(0,l.Cb)({type:lt.apiValues,json:{write:lt.write}})],gt.prototype,"measureOperation",null),(0,r._)([(0,l.Cb)({json:{read:!0}})],gt.prototype,"operationType",void 0),(0,r._)([(0,l.Cb)({type:String,json:{read:be.Jo.read,write:be.Jo.write}})],gt.prototype,"linearUnit",void 0),gt=ft=(0,r._)([(0,p.j)("esri.rest.support.ImageHeightParameters")],gt);const bt=gt;var Rt,wt=i(92835);let vt=Rt=class extends I.wq{constructor(){super(...arguments),this.geometry=null,this.mosaicRule=null,this.renderingRule=null,this.pixelSize=null,this.raster=void 0,this.timeExtent=null}writeGeometry(e,t,i){null!=e&&(t.geometryType=(0,at.Ji)(e),t[i]=e.toJSON())}clone(){return new Rt((0,R.d9)({geometry:this.geometry,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,raster:this.raster,timeExtent:this.timeExtent}))}};(0,r._)([(0,l.Cb)({types:m.qM,json:{read:at.im}})],vt.prototype,"geometry",void 0),(0,r._)([(0,c.c)("geometry")],vt.prototype,"writeGeometry",null),(0,r._)([(0,l.Cb)({type:K,json:{write:!0}})],vt.prototype,"mosaicRule",void 0),(0,r._)([(0,l.Cb)({type:F.Z,json:{write:!0}})],vt.prototype,"renderingRule",void 0),(0,r._)([(0,l.Cb)({type:Q.Z,json:{write:!0}})],vt.prototype,"pixelSize",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],vt.prototype,"raster",void 0),(0,r._)([(0,l.Cb)({type:wt.Z,json:{read:{source:"time"},write:{target:"time"}}})],vt.prototype,"timeExtent",void 0),vt=Rt=(0,r._)([(0,p.j)("esri.rest.support.ImageHistogramParameters")],vt);const _t=vt;var Ct;let St=Ct=class extends I.wq{constructor(){super(...arguments),this.geometry=null,this.renderingRules=null,this.pixelSize=null,this.returnGeometry=!0,this.returnCatalogItems=!0,this.returnPixelValues=!0,this.maxItemCount=null,this.timeExtent=null,this.raster=void 0,this.viewId=void 0,this.processAsMultidimensional=!1}writeGeometry(e,t,i){null!=e&&(t.geometryType=(0,at.Ji)(e),t[i]=JSON.stringify(e.toJSON()))}set mosaicRule(e){let t=e;t&&t.mosaicMethod&&(t=K.fromJSON({...t.toJSON(),mosaicMethod:t.mosaicMethod,mosaicOperation:t.mosaicOperation})),this._set("mosaicRule",t)}writeMosaicRule(e,t,i){null!=e&&(t[i]=JSON.stringify(e.toJSON()))}set renderingRule(e){let t=e;t&&t.rasterFunction&&(t=F.Z.fromJSON({...t.toJSON(),rasterFunction:t.rasterFunction,rasterFunctionArguments:t.rasterFunctionArguments})),this._set("renderingRule",t)}writeRenderingRule(e,t,i){null!=e&&(t[i]=JSON.stringify(e.toJSON())),e.rasterFunctionDefinition&&(t[i]=JSON.stringify(e.rasterFunctionDefinition))}writeRenderingRules(e,t,i){null!=e&&(t[i]=JSON.stringify(e.map((e=>e.rasterFunctionDefinition||e.toJSON()))))}writePixelSize(e,t,i){null!=e&&(t[i]=JSON.stringify(e))}writeTimeExtent(e,t,i){if(null!=e){const r=(0,n.pC)(e.start)?e.start.getTime():null,s=(0,n.pC)(e.end)?e.end.getTime():null;t[i]=null!=r?null!=s?`${r},${s}`:`${r}`:null}}clone(){return new Ct((0,R.d9)({geometry:this.geometry,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,returnGeometry:this.returnGeometry,returnCatalogItems:this.returnCatalogItems,returnPixelValues:this.returnPixelValues,maxItemCount:this.maxItemCount,processAsMultidimensional:this.processAsMultidimensional,raster:this.raster,viewId:this.viewId,timeExtent:this.timeExtent}))}};(0,r._)([(0,l.Cb)({json:{write:!0}})],St.prototype,"geometry",void 0),(0,r._)([(0,c.c)("geometry")],St.prototype,"writeGeometry",null),(0,r._)([(0,l.Cb)({type:K,json:{write:!0}})],St.prototype,"mosaicRule",null),(0,r._)([(0,c.c)("mosaicRule")],St.prototype,"writeMosaicRule",null),(0,r._)([(0,l.Cb)({type:F.Z,json:{write:!0}})],St.prototype,"renderingRule",null),(0,r._)([(0,c.c)("renderingRule")],St.prototype,"writeRenderingRule",null),(0,r._)([(0,l.Cb)({type:[F.Z],json:{write:!0}})],St.prototype,"renderingRules",void 0),(0,r._)([(0,c.c)("renderingRules")],St.prototype,"writeRenderingRules",null),(0,r._)([(0,l.Cb)({type:Q.Z,json:{write:!0}})],St.prototype,"pixelSize",void 0),(0,r._)([(0,c.c)("pixelSize")],St.prototype,"writePixelSize",null),(0,r._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],St.prototype,"returnGeometry",void 0),(0,r._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],St.prototype,"returnCatalogItems",void 0),(0,r._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],St.prototype,"returnPixelValues",void 0),(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],St.prototype,"maxItemCount",void 0),(0,r._)([(0,l.Cb)({type:wt.Z,json:{write:{target:"time"}}})],St.prototype,"timeExtent",void 0),(0,r._)([(0,c.c)("timeExtent")],St.prototype,"writeTimeExtent",null),(0,r._)([(0,l.Cb)({json:{write:!0}})],St.prototype,"raster",void 0),(0,r._)([(0,l.Cb)({json:{write:!0}})],St.prototype,"viewId",void 0),(0,r._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],St.prototype,"processAsMultidimensional",void 0),St=Ct=(0,r._)([(0,p.j)("esri.rest.support.ImageIdentifyParameters")],St);const xt=St;var It;let Ft=It=class extends I.wq{constructor(){super(...arguments),this.geometries=null,this.rasterId=null}writeGeometry(e,t,i){t.geometries={geometryType:"esriGeometryPoint",geometries:e.map((e=>e.toJSON()))}}clone(){return new It({geometries:this.geometries?.map((e=>e.clone()))??[],rasterId:this.rasterId})}};(0,r._)([(0,l.Cb)({type:[Q.Z],json:{write:!0}})],Ft.prototype,"geometries",void 0),(0,r._)([(0,c.c)("geometries")],Ft.prototype,"writeGeometry",null),(0,r._)([(0,l.Cb)({type:u.z8,json:{write:!0}})],Ft.prototype,"rasterId",void 0),Ft=It=(0,r._)([(0,p.j)("esri.rest.support.ImagePixelLocationParameters")],Ft);const jt=Ft;var Tt;let Dt=Tt=class extends ut{constructor(){super(...arguments),this.type="point",this.geometry=null,this.is3D=!1}writeGeometry(e,t,i){null!=e&&(t.geometryType=(0,at.Ji)(e),t[i]=e.toJSON())}get measureOperation(){const{is3D:e,geometry:t}=this;return"point"===t.type?e?"point-3D":"point":e?"centroid-3D":"centroid"}clone(){return new Tt((0,R.d9)({geometry:this.geometry,is3D:this.is3D,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,raster:this.raster}))}};(0,r._)([(0,l.Cb)({types:m.qM,json:{name:"fromGeometry",read:at.im}})],Dt.prototype,"geometry",void 0),(0,r._)([(0,c.c)("geometry")],Dt.prototype,"writeGeometry",null),(0,r._)([(0,l.Cb)({type:lt.apiValues,json:{read:lt.read,write:lt.write}})],Dt.prototype,"measureOperation",null),(0,r._)([(0,l.Cb)({json:{read:!0}})],Dt.prototype,"is3D",void 0),Dt=Tt=(0,r._)([(0,p.j)("esri.rest.support.ImagePointParameters")],Dt);const Nt=Dt;var Pt;let Ot=Pt=class extends I.wq{constructor(){super(...arguments),this.geometry=null,this.interpolation="nearest",this.mosaicRule=null,this.outFields=null,this.pixelSize=null,this.returnFirstValueOnly=!0,this.sampleDistance=null,this.sampleCount=null,this.sliceId=null,this.timeExtent=null}writeGeometry(e,t,i){null!=e&&(t.geometryType=(0,at.Ji)(e),t[i]=e.toJSON())}set locations(e){if(e?.length){const t=new Ke.Z({spatialReference:e[0].spatialReference});t.points=e.map((e=>[e.x,e.y])),this._set("locations",e),this.geometry=t}}clone(){return new Pt((0,R.d9)({geometry:this.geometry,locations:this.locations,interpolation:this.interpolation,mosaicRule:this.mosaicRule,outFields:this.outFields,raster:this.raster,returnFirstValueOnly:this.returnFirstValueOnly,sampleDistance:this.sampleDistance,sampleCount:this.sampleCount,sliceId:this.sliceId,pixelSize:this.pixelSize,timeExtent:this.timeExtent}))}};(0,r._)([(0,l.Cb)({types:m.qM,json:{read:at.im}})],Ot.prototype,"geometry",void 0),(0,r._)([(0,c.c)("geometry")],Ot.prototype,"writeGeometry",null),(0,r._)([(0,l.Cb)()],Ot.prototype,"locations",null),(0,r._)([(0,l.Cb)({type:String,json:{type:Y.c.jsonValues,read:Y.c.read,write:Y.c.write}})],Ot.prototype,"interpolation",void 0),(0,r._)([(0,l.Cb)({type:K,json:{write:!0}})],Ot.prototype,"mosaicRule",void 0),(0,r._)([(0,l.Cb)({type:[String],json:{write:!0}})],Ot.prototype,"outFields",void 0),(0,r._)([(0,l.Cb)({type:Q.Z,json:{write:!0}})],Ot.prototype,"pixelSize",void 0),(0,r._)([(0,l.Cb)({type:String,json:{write:!0}})],Ot.prototype,"raster",void 0),(0,r._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],Ot.prototype,"returnFirstValueOnly",void 0),(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],Ot.prototype,"sampleDistance",void 0),(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],Ot.prototype,"sampleCount",void 0),(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],Ot.prototype,"sliceId",void 0),(0,r._)([(0,l.Cb)({type:wt.Z,json:{read:{source:"time"},write:{target:"time"}}})],Ot.prototype,"timeExtent",void 0),Ot=Pt=(0,r._)([(0,p.j)("esri.rest.support.ImageSampleParameters")],Ot);const Mt=Ot;var Vt=i(23808);const At=(0,b.w)()({U1:"u1",U2:"u2",U4:"u4",U8:"u8",S8:"s8",U16:"u16",S16:"s16",U32:"u32",S32:"s32",F32:"f32",F64:"f64",C64:"c64",C128:"c128",UNKNOWN:"unknown"}),Jt=new Set(["png","png8","png24","png32","jpg","bmp","gif","jpgpng","lerc","tiff"]),Zt=(0,l.Eg)(u.q9,{min:0,max:255}),Et=e=>{let t=class extends e{constructor(){super(...arguments),this._functionRasterInfos={},this._rasterJobHandler={instance:null,refCount:0,connectionPromise:null},this._cachedRendererJson=null,this._serviceSupportsMosaicRule=null,this._rasterAttributeTableFieldPrefix="Raster.",this.adjustAspectRatio=null,this.bandIds=void 0,this.capabilities=null,this.compressionQuality=void 0,this.compressionTolerance=.01,this.copyright=null,this.defaultMosaicRule=null,this.definitionExpression=null,this.exportImageServiceParameters=null,this.rasterInfo=null,this.fields=null,this.fullExtent=null,this.hasMultidimensions=!1,this.imageMaxHeight=4100,this.imageMaxWidth=4100,this.interpolation=void 0,this.minScale=0,this.maxScale=0,this.multidimensionalInfo=null,this.multidimensionalSubset=null,this.noData=null,this.noDataInterpretation=void 0,this.objectIdField=null,this.geometryType="polygon",this.typeIdField=null,this.types=[],this.pixelFilter=null,this.raster=void 0,this.sourceType=null,this.viewId=void 0,this.symbolizer=null,this.rasterFunctionInfos=null,this.serviceDataType=null,this.spatialReference=null,this.pixelType=null,this.serviceRasterInfo=null,this.sourceJSON=null,this.url=null,this.version=void 0}initialize(){this._set("exportImageServiceParameters",new ee({layer:this}))}readServiceSupportsMosaicRule(e,t){return this._isMosaicRuleSupported(t)}get _rasterFunctionNamesIndex(){const e=new Map;return!this.rasterFunctionInfos||(0,n.pC)(this.rasterFunctionInfos)&&this.rasterFunctionInfos.length<1||(0,n.pC)(this.rasterFunctionInfos)&&this.rasterFunctionInfos.forEach((t=>{e.set(t.name.toLowerCase().replace(/ /gi,"_"),t.name)})),e}readBandIds(e,t){if(Array.isArray(e)&&e.length>0&&e.every((e=>"number"==typeof e)))return e}readCapabilities(e,t){return this._readCapabilities(t)}writeCompressionQuality(e,t,i){null!=e&&"lerc"!==this.format&&(t[i]=e)}writeCompressionTolerance(e,t,i){"lerc"===this.format&&null!=e&&(t[i]=e)}readDefaultMosaicRule(e,t){return this._serviceSupportsMosaicRule?K.fromJSON(t):null}get fieldsIndex(){return this.fields?new ie.Z(this.fields):null}set format(e){e&&Jt.has(e.toLowerCase())&&this._set("format",e.toLowerCase())}readFormat(e,t){return"esriImageServiceDataTypeVector-UV"===t.serviceDataType||"esriImageServiceDataTypeVector-MagDir"===t.serviceDataType||null!=this.pixelFilter?"lerc":"jpgpng"}readMinScale(e,t){return null!=t.minLOD&&null!=t.maxLOD?e:0}readMaxScale(e,t){return null!=t.minLOD&&null!=t.maxLOD?e:0}set mosaicRule(e){let t=e;t&&t.mosaicMethod&&(t=K.fromJSON({...t.toJSON(),mosaicMethod:t.mosaicMethod,mosaicOperation:t.mosaicOperation})),this._set("mosaicRule",t)}readMosaicRule(e,t){const i=e||t.mosaicRule;return i?K.fromJSON(i):this._isMosaicRuleSupported(t)?K.fromJSON(t):null}writeMosaicRule(e,t,i){let r=this.mosaicRule;const s=this.definitionExpression;r?s&&s!==r.where&&(r=r.clone(),r.where=s):s&&(r=new K({where:s})),this._isValidCustomizedMosaicRule(r)&&(t[i]=r.toJSON())}writeNoData(e,t,i){null!=e&&"number"==typeof e&&(t[i]=Zt(e))}readObjectIdField(e,t){if(!e){const i=t.fields.filter((e=>"esriFieldTypeOID"===e.type||"oid"===e.type));e=i&&i[0]&&i[0].name}return e}get parsedUrl(){return(0,_.mN)(this.url)}readSourceType(e,t){return this._isMosaicDataset(t)?"mosaic-dataset":"raster-dataset"}set renderer(e){this.loaded&&(e=this._configRenderer(e)),this._set("renderer",e)}readRenderer(e,t,i){const r=t?.layerDefinition?.drawingInfo?.renderer,s=(0,y.ij)(r,i);return null==s?null:("vector-field"===s.type&&t.symbolTileSize&&!r.symbolTileSize&&(s.symbolTileSize=t.symbolTileSize),A(s)||w.Z.getLogger(this.declaredClass).warn("ArcGISImageService","Imagery layer doesn't support given renderer type."),s)}writeRenderer(e,t,i){t.layerDefinition=t.layerDefinition||{},t.layerDefinition.drawingInfo=t.layerDefinition.drawingInfo||{},t.layerDefinition.drawingInfo.renderer=e.toJSON(),"vector-field"===e.type&&(t.symbolTileSize=e.symbolTileSize)}get rasterFields(){const e=this._rasterAttributeTableFieldPrefix||"Raster.",t=new te.Z({name:"Raster.ItemPixelValue",alias:"Item Pixel Value",domain:null,editable:!1,length:50,type:"string"}),i=new te.Z({name:"Raster.ServicePixelValue",alias:"Service Pixel Value",domain:null,editable:!1,length:50,type:"string"}),r=new te.Z({name:"Raster.ServicePixelValue.Raw",alias:"Raw Service Pixel Value",domain:null,editable:!1,length:50,type:"string"});let s=this.fields?(0,R.d9)(this.fields):[];s.push(i),this.capabilities?.operations.supportsQuery&&this.fields&&this.fields.length>0&&s.push(t),this.version>=10.4&&(0,n.pC)(this.rasterFunctionInfos)&&this.rasterFunctionInfos.some((e=>"none"===e.name.toLowerCase()))&&s.push(r),(0,n.pC)(this.rasterFunctionInfos)&&this.rasterFunctionInfos.filter((e=>"none"!==e.name.toLowerCase())).forEach((e=>{s.push(new te.Z({name:"Raster.ServicePixelValue."+e.name,alias:e.name,domain:null,editable:!1,length:50,type:"string"}))})),this._isVectorDataSet()&&(s.push(new te.Z({name:"Raster.Magnitude",alias:"Magnitude",domain:null,editable:!1,type:"double"})),s.push(new te.Z({name:"Raster.Direction",alias:"Direction",domain:null,editable:!1,type:"double"})));const{attributeTable:o}=this.rasterInfo??{};if((0,n.pC)(o)){const t=o.fields.filter((e=>"esriFieldTypeOID"!==e.type&&"value"!==e.name.toLowerCase())).map((t=>{const i=(0,R.d9)(t);return i.name=e+t.name,i}));s=s.concat(t)}return s}set renderingRule(e){let t=e;t&&t.rasterFunction&&(t=F.Z.fromJSON({...t.toJSON(),rasterFunction:t.rasterFunction,rasterFunctionArguments:t.rasterFunctionArguments})),this._set("renderingRule",t)}readRenderingRule(e,t){const i=t.rasterFunctionInfos;return t.renderingRule||i&&i.length&&"None"!==i[0].name?this._isRFTJson(t.renderingRule)?F.Z.fromJSON({rasterFunctionDefinition:t.renderingRule}):F.Z.fromJSON(t.renderingRule||{rasterFunctionInfos:t.rasterFunctionInfos}):null}writeRenderingRule(e,t,i){const r=e.toJSON();r.rasterFunctionDefinition?t[i]=r.rasterFunctionDefinition:t[i]=r}readSpatialReference(e,t){const i=e||t.extent.spatialReference;return i?ye.Z.fromJSON(i):null}readPixelType(e){return At.fromJSON(e)||e}writePixelType(e,t,i){((0,n.Wi)(this.serviceRasterInfo)||this.pixelType!==this.serviceRasterInfo.pixelType)&&(t[i]=At.toJSON(e))}readVersion(e,t){let i=t.currentVersion;return i||(i=t.hasOwnProperty("fields")||t.hasOwnProperty("timeInfo")?10:9.3),i}applyFilter(e){let t=e;return this.pixelFilter&&(t=this._clonePixelData(e),this.pixelFilter(t)),t}async applyRenderer(e,t){let i=e;const{renderer:r,symbolizer:s,pixelFilter:n,bandIds:o}=this;if(!this._isPicture()&&r&&s&&!n){const n=JSON.stringify(this._cachedRendererJson)!==JSON.stringify(r.toJSON()),a=this._rasterJobHandler.instance;if(a){n&&(s.bind(),await a.updateSymbolizer(s,t),this._cachedRendererJson=r.toJSON());const l=await a.symbolize({bandIds:o,...e},t);i={extent:e.extent,pixelBlock:l}}else i={extent:e.extent,pixelBlock:s.symbolize({bandIds:o,...e})}}return i}destroy(){this._shutdownJobHandler()}increaseRasterJobHandlerUsage(){this._rasterJobHandler.refCount++}decreaseRasterJobHandlerUsage(){this._rasterJobHandler.refCount--,this._rasterJobHandler.refCount<=0&&this._shutdownJobHandler()}async computeAngles(e,t){if(!(await this._fetchCapabilities(t?.signal)).operations.supportsComputeAngles)throw new g.Z("imagery-layer:compute-angles","this operation is not supported on the input image service");return e=(0,u.TJ)(ot,e).clone(),async function(e,t,i){const r=t.toJSON();(0,n.pC)(r.angleName)&&(r.angleName=r.angleName.join(",")),(0,n.pC)(t.point)&&t.point.spatialReference?.imageCoordinateSystem&&(r.point.spatialReference=ke(t.point.spatialReference)),(0,n.pC)(t.spatialReference)&&t.spatialReference.imageCoordinateSystem&&(r.spatialReference=Qe(t.spatialReference));const s=(0,he.en)(e),o=(0,he.cv)({...s.query,f:"json",...r}),a=(0,he.lA)(o,i),{data:l}=await(0,f.default)(`${s.path}/computeAngles`,a);return l.spatialReference=l.spatialReference?null!=l.spatialReference.geodataXform?new ye.Z({wkid:0,imageCoordinateSystem:l.spatialReference}):ye.Z.fromJSON(l.spatialReference):null,"NaN"===l.north&&(l.north=null),"NaN"===l.up&&(l.up=null),new ge(l)}(this.url,e,this._getRequestOptions(t))}async computePixelSpaceLocations(e,t){if(!(await this._fetchCapabilities(t?.signal)).operations.supportsComputePixelLocation)throw new g.Z("imagery-layer:compute-pixel-space-locations","this operation is not supported on the input image service");return e=(0,u.TJ)(jt,e).clone(),async function(e,t,i){const r=t.toJSON(),{geometries:s}=t;if(s)for(let e=0;e<s.length;e++)s[e].spatialReference?.imageCoordinateSystem&&(r.geometries.geometries[e].spatialReference=ke(s[e].spatialReference));const n=(0,he.en)(e),o=(0,he.cv)({...n.query,f:"json",...r}),a=(0,he.lA)(o,i),{data:l}=await(0,f.default)(`${n.path}/computePixelLocation`,a);return Me.fromJSON(l)}(this.url,e,this._getRequestOptions(t))}async computeHistograms(e,t){if(!(await this._fetchCapabilities(t?.signal)).operations.supportsComputeHistograms)throw new g.Z("imagery-layer:compute-histograms","this operation is not supported on the input image service");return e=(0,u.TJ)(_t,e).clone(),this._applyMosaicAndRenderingRules(e),async function(e,t,i){const r=await Ge(e,t,i),s=(0,he.en)(e),{data:n}=await(0,f.default)(`${s.path}/computeHistograms`,r);return{histograms:n.histograms}}(this.url,e,this._getRequestOptions(t))}async computeStatisticsHistograms(e,t){if(!(await this._fetchCapabilities(t?.signal)).operations.supportsComputeStatisticsHistograms)throw new g.Z("imagery-layer:compute-statistics-histograms","this operation is not supported on the input image service");return e=(0,u.TJ)(_t,e).clone(),this._applyMosaicAndRenderingRules(e),async function(e,t,i){const r=await Ge(e,t,i),s=(0,he.en)(e),{data:n}=await(0,f.default)(`${s.path}/computeStatisticsHistograms`,r),{statistics:o}=n;return o?.length&&o.forEach((e=>{e.avg=e.mean,e.stddev=e.standardDeviation})),{statistics:o,histograms:n.histograms}}(this.url,e,this._getRequestOptions(t))}async measureHeight(e,t){const i=await this._fetchCapabilities(t?.signal);if(!("base-and-top"===e.operationType?i.mensuration.supportsHeightFromBaseAndTop:"base-and-top-shadow"===e.operationType?i.mensuration.supportsHeightFromBaseAndTopShadow:i.mensuration.supportsHeightFromTopAndTopShadow))throw new g.Z("imagery-layer:measure-height","this operation is not supported on the input image service");return e=(0,u.TJ)(bt,e).clone(),this._applyMosaicAndRenderingRules(e),async function(e,t,i){const r=await Le(e,t,[t.fromGeometry,t.toGeometry],i);return Te.fromJSON(r)}(this.url,e,this._getRequestOptions(t))}async measureAreaAndPerimeter(e,t){const i=await this._fetchCapabilities(t?.signal);if(!i.mensuration.supportsAreaAndPerimeter||e.is3D&&!i.mensuration.supports3D)throw new g.Z("imagery-layer:measure-area-and-perimeter","this operation is not supported on the input image service");return e=(0,u.TJ)(dt,e).clone(),this._applyMosaicAndRenderingRules(e),async function(e,t,i){const r=await Le(e,t,[t.geometry],i);return xe.fromJSON(r)}(this.url,e,this._getRequestOptions(t))}async measureDistanceAndAngle(e,t){const i=await this._fetchCapabilities(t?.signal);if(!i.mensuration.supportsDistanceAndAngle||e.is3D&&!i.mensuration.supports3D)throw new g.Z("imagery-layer:measure-distance-and-angle","this operation is not supported on the input image service");return e=(0,u.TJ)(yt,e).clone(),this._applyMosaicAndRenderingRules(e),Be(this.url,e,this._getRequestOptions(t))}async measurePointOrCentroid(e,t){const i=await this._fetchCapabilities(t?.signal);if(!i.mensuration.supportsPointOrCentroid||e.is3D&&!i.mensuration.supports3D)throw new g.Z("imagery-layer:measure-point-or-centroid","this operation is not supported on the input image service");return e=(0,u.TJ)(Nt,e).clone(),this._applyMosaicAndRenderingRules(e),async function(e,t,i){const r=await Le(e,t,[t.geometry],i);return Ae.fromJSON(r)}(this.url,e,this._getRequestOptions(t))}getField(e){const{fieldsIndex:t}=this;return(0,n.pC)(t)?t.get(e):void 0}getFieldDomain(e,t){const i=this.getField(e);return i?i.domain:null}async fetchImage(e,t,r,s={}){if(null==e||null==t||null==r)throw new g.Z("imagery-layer:fetch-image","Insufficient parameters for requesting an image. A valid extent, width and height values are required.");if(this.renderer||this.symbolizer){const e=await this.generateRasterInfo(this.renderingRule,{signal:s.signal});e&&(this.rasterInfo=e)}const n=this.getExportImageServiceParameters(e,t,r,s.timeExtent);if(null==n){if(s.requestAsImageElement&&this._canRequestImageElement(this.format)){const e=document.createElement("canvas");return e.width=t,e.height=r,s.returnImageBitmap?{imageBitmap:await(0,re.g)(e,`${i(this.parsedUrl)}/exportImage`)}:{imageOrCanvasElement:e}}const{bandIds:n,rasterInfo:o}=this,a=(n?.length||o.bandCount)??0,l=t*r,u=o.pixelType,p=[];for(let e=0;e<a;e++)p.push(ne.Z.createEmptyBand(u,l));return{pixelData:{pixelBlock:new ne.Z({width:t,height:r,pixels:p,mask:new Uint8Array(l),pixelType:u}),extent:e}}}const o=!!s.requestAsImageElement&&!this.pixelFilter,a=o&&!!s.returnImageBitmap,l={imageServiceParameters:n,imageProps:{extent:e,width:t,height:r,format:this.format},requestAsImageElement:o,returnImageBitmap:a,signal:s.signal};return this._requestArrayBuffer(l)}fetchKeyProperties(e){return(0,f.default)(i(this.parsedUrl)+"/keyProperties",{query:this._getQueryParams({renderingRule:this.version>=10.3?e?.renderingRule:null})}).then((e=>e.data))}fetchRasterAttributeTable(e){return this.version<10.1?Promise.reject(new g.Z("#fetchRasterAttributeTable()","Failed to get rasterAttributeTable")):(0,f.default)(i(this.parsedUrl)+"/rasterAttributeTable",{query:this._getQueryParams({renderingRule:this.version>=10.3?e?.renderingRule:null})}).then((e=>De.Z.fromJSON(e.data)))}getCatalogItemRasterInfo(e,t){const r={...t,query:this._getQueryParams()};return async function(e,t,i){const r=(0,he.en)(e),s=(0,he.cv)({...r?.query,f:"json"}),n=(0,he.lA)(s,i),o=`${r?.path}/${t}/info`,a=(0,f.default)(`${o}`,n),l=(0,f.default)(`${o}/keyProperties`,n),u=await Promise.allSettled([a,l]),p="fulfilled"===u[0].status?u[0].value.data:null,c="fulfilled"===u[1].status?u[1].value.data:null;let d=null;p.statistics?.length&&(d=p.statistics.map((e=>({min:e[0],max:e[1],avg:e[2],stddev:e[3]}))));const m=Xe.Z.fromJSON(p.extent),h=Math.ceil(m.width/p.pixelSizeX-.1),y=Math.ceil(m.height/p.pixelSizeY-.1),g=m.spatialReference,b=new Q.Z({x:p.pixelSizeX,y:p.pixelSizeY,spatialReference:g}),R=p.histograms?.length?p.histograms:null,w=new st.Z({origin:p.origin,blockWidth:p.blockWidth,blockHeight:p.blockHeight,firstPyramidLevel:p.firstPyramidLevel,maximumPyramidLevel:p.maxPyramidLevel});return new oe.Z({width:h,height:y,bandCount:p.bandCount,extent:m,spatialReference:g,pixelSize:b,pixelType:p.pixelType.toLowerCase(),statistics:d,histograms:R,keyProperties:c,storageInfo:w})}(i(this.parsedUrl),e,r)}async getCatalogItemICSInfo(e,t){const{data:r}=await(0,f.default)(i(this.parsedUrl)+"/"+e+"/info/ics",{query:this._getQueryParams(),...t}),s=r&&r.ics;if(!s)return;let n=null;try{n=(await(0,f.default)(i(this.parsedUrl)+"/"+e+"/info",{query:this._getQueryParams(),...t})).data.extent}catch{}if(!n||!n.spatialReference)return{ics:s,icsToPixelTransform:null,icsExtent:null,northDirection:null};const o=this.version>=10.7?(0,f.default)(i(this.parsedUrl)+"/"+e+"/info/icstopixel",{query:this._getQueryParams(),...t}).then((e=>e.data)).catch((()=>({}))):{},a=n.spatialReference,l={geometries:JSON.stringify({geometryType:"esriGeometryEnvelope",geometries:[n]}),inSR:a.wkid||JSON.stringify(a),outSR:"0:"+e},u=(0,f.default)(i(this.parsedUrl)+"/project",{query:this._getQueryParams(l),...t}).then((e=>e.data)).catch((()=>({}))),p=(n.xmin+n.xmax)/2,c=(n.ymax-n.ymin)/6,d=n.ymin+c,m=[];for(let e=0;e<5;e++)m.push({x:p,y:d+c*e});const h={geometries:JSON.stringify({geometryType:"esriGeometryPoint",geometries:m}),inSR:a.wkid||JSON.stringify(a),outSR:"0:"+e},y=(0,f.default)(i(this.parsedUrl)+"/project",{query:this._getQueryParams(h),...t}).then((e=>e.data)).catch((()=>({}))),g=await Promise.all([o,u,y]);let b=g[0].ipxf;if(null==b){const e=s.geodataXform?.xf_0;"topup"===e?.name?.toLowerCase()&&6===e?.coefficients?.length&&(b={affine:{name:"ics [sensor: Frame] to pixel (column, row) transformation",coefficients:e.coefficients,cellsizeRatio:0,type:"GeometricXform"}})}const R=Xe.Z.fromJSON(g[1]&&g[1].geometries&&g[1].geometries[0]);R&&(R.spatialReference=new ye.Z({wkid:0,imageCoordinateSystem:s}));const w=g[2].geometries?g[2].geometries.filter((e=>null!=e&&null!=e.x&&null!=e.y&&"NaN"!==e.x&&"NaN"!==e.y)):[],v=w.length;if(v<3)return{ics:s,icsToPixelTransform:b,icsExtent:R,northDirection:null};let _=0,C=0,S=0,x=0;for(let e=0;e<v;e++)_+=w[e].x,C+=w[e].y,S+=w[e].x*w[e].x,x+=w[e].x*w[e].y;const I=(v*x-_*C)/(v*S-_*_);let F=0;const j=w[4].x>w[0].x,T=w[4].y>w[0].y;return I===1/0?F=T?90:270:0===I?F=j?0:180:I>0?F=j?180*Math.atan(I)/Math.PI:180*Math.atan(I)/Math.PI+180:I<0&&(F=T?180+180*Math.atan(I)/Math.PI:360+180*Math.atan(I)/Math.PI),{ics:s,icsToPixelTransform:b,icsExtent:R,northDirection:F}}async generateRasterInfo(e,t){if(e=(0,u.TJ)(F.Z,e),this.serviceRasterInfo&&(!e||"none"===e.functionName?.toLowerCase()||this._isVectorFieldResampleFunction(e)))return this.serviceRasterInfo;const i=function(e){if(!e)return null;const t=JSON.stringify(e).match(/"rasterFunction":"(.*?")/gi)?.map((e=>e.replace('"rasterFunction":"',"").replace('"',"")));return t?t.join("/"):null}(e);if(!i)return null;if(this._functionRasterInfos[i])return this._functionRasterInfos[i];const r=this._generateRasterInfo(e,t);this._functionRasterInfos[i]=r;try{return await r}catch{return this._functionRasterInfos[i]=null,null}}getExportImageServiceParameters(e,t,r,s){const o=Qe((e=e.clone().shiftCentralMeridian()).spatialReference,i(this.parsedUrl));this.pixelType!==this.serviceRasterInfo.pixelType&&(this.exportImageServiceParameters.pixelType=this.pixelType);const a=this.exportImageServiceParameters.toJSON(),{bandIds:l,noData:u}=a;let{renderingRule:p}=a;const c=this.renderingRule?.rasterFunctionDefinition,d=!this.renderer||"raster-stretch"===this.renderer.type;if(l?.length&&this._hasRenderingRule(this.renderingRule)&&!c&&d){const e={rasterFunction:"ExtractBand",rasterFunctionArguments:{BandIds:l}};if("Stretch"===p.rasterFunction)e.rasterFunctionArguments.Raster=p.rasterFunctionArguments.Raster,p.rasterFunctionArguments.Raster=e;else if("Colormap"===p.rasterFunction){const t=p.rasterFunctionArguments.Raster;"Stretch"===t?.rasterFunction?(e.rasterFunctionArguments.Raster=t.rasterFunctionArguments.Raster,t.rasterFunctionArguments.Raster=e):(e.rasterFunctionArguments.Raster=t,p.rasterFunctionArguments.Raster=e)}else e.rasterFunctionArguments.Raster=p,p=e;a.bandIds=void 0}else a.bandIds=l?.join(",");u instanceof Array&&u.length>0&&(a.noData=u.join(","));const m=this._processMultidimensionalIntersection(null,s,this.exportImageServiceParameters.mosaicRule);if(m.isOutSide)return null;a.mosaicRule=(0,n.pC)(m.mosaicRule)?JSON.stringify(m.mosaicRule):null,s=m.timeExtent,a.renderingRule=this._getRenderingRuleString(F.Z.fromJSON(p));const h={};if((0,n.pC)(s)){const{start:e,end:t}=s.toJSON();e&&t&&e===t?h.time=""+e:null==e&&null==t||(h.time=`${e??"null"},${t??"null"}`)}return{bbox:e.xmin+","+e.ymin+","+e.xmax+","+e.ymax,bboxSR:o,imageSR:o,size:t+","+r,...a,...h}}async getSamples(e,t){if(!(await this._fetchCapabilities(t?.signal))?.operations.supportsGetSamples)throw new g.Z("imagery-layer:get-samples","getSamples operation is not supported on the input image service");e=(0,u.TJ)(Mt,e).clone();const{raster:i}=this;return i&&null==e.raster&&(e.raster=i),async function(e,t,i){const r=t.toJSON();ze(r),r.outFields?.length&&(r.outFields=r.outFields.join(","));const s=(await(0,me.aX)(t.geometry))?.[0];(0,n.pC)(s)&&(r.geometry=s.toJSON());const o=(0,he.en)(e),a=(0,he.cv)({...o.query,f:"json",...r}),l=(0,he.lA)(a,i),{data:u}=await(0,f.default)(`${o.path}/getSamples`,l),p=u?.samples?.map((e=>{const t="NaN"===e.value||""===e.value?null:e.value.split(" ").map((e=>Number(e)));return{...e,pixelValue:t}}));return qe.fromJSON({samples:p})}(this.url,e,this._getRequestOptions(t))}async identify(e,t){if(!(await this._fetchCapabilities(t?.signal)).operations.supportsIdentify)throw new g.Z("imagery-layer:identify","identify operation is not supported on the input image service");e=(0,u.TJ)(xt,e).clone();const i=this._processMultidimensionalIntersection(e.geometry,e.timeExtent,e.mosaicRule||this.mosaicRule);if(i.isOutSide)throw new g.Z("imagery-layer:identify","the request cannot be fulfilled when falling outside of the multidimensional subset");e.timeExtent=(0,n.Wg)(i.timeExtent),e.mosaicRule=(0,n.Wg)(i.mosaicRule);const{raster:r,renderingRule:s}=this;return s&&null==e.renderingRule&&(e.renderingRule=s),r&&null==e.raster&&(e.raster=r),He(this.url,e,this._getRequestOptions(t))}createQuery(){const e=new et.Z;return e.outFields=["*"],e.returnGeometry=!0,e.where=this.definitionExpression||"1=1",e}async queryRasters(e,t){return({query:e,requestOptions:t}=await this._prepareForQuery(e,t)),(0,$e.e)(this.url,e,t)}async queryObjectIds(e,t){return({query:e,requestOptions:t}=await this._prepareForQuery(e,t)),(0,it.G)(this.url,e,t)}async queryRasterCount(e,t){return({query:e,requestOptions:t}=await this._prepareForQuery(e,t)),(0,tt.P)(this.url,e,t)}async queryVisibleRasters(e,t){if(!e)throw new g.Z("imagery-layer: query-visible-rasters","missing query parameter");await this.load();const{pixelSize:i,returnDomainValues:r,returnTopmostRaster:s,showNoDataRecords:o}=t||{pixelSize:null,returnDomainValues:!1,returnTopmostRaster:!1,showNoDataRecords:!1};let a=!1,l=null,u=null;const p="raster.servicepixelvalue",c=this._rasterFunctionNamesIndex;if((0,n.pC)(e.outFields)&&(a=e.outFields.some((e=>!e.toLowerCase().includes(p))),this.version>=10.4)){const t=e.outFields.filter((e=>e.toLowerCase().includes(p)&&e.length>p.length)).map((e=>{const t=e.slice(p.length+1);return[this._updateRenderingRulesFunctionName(t,c),t]}));l=t.map((e=>new F.Z({functionName:e[0]}))),u=t.map((e=>e[1]));const{renderingRule:i}=this;0===l.length?i?.functionName?(l.push(i),u.push(i.functionName)):l=null:i?.functionName&&!l.some((e=>e.functionName===i.functionName))&&(l.push(i),u.push(i.functionName))}const d=(0,n.Wi)(e.outSpatialReference)||e.outSpatialReference.equals(this.spatialReference),{multidimensionalSubset:m}=this;let h=e.timeExtent||this.timeExtent;if(m){const{isOutside:t,intersection:i}=(0,le.W2)(m,{geometry:(0,n.Wg)(e.geometry),timeExtent:(0,n.Wg)(e.timeExtent),multidimensionalDefinition:this.exportImageServiceParameters.mosaicRule?.multidimensionalDefinition});if(t)throw new g.Z("imagery-layer:query-visible-rasters","the request cannot be fulfilled when falling outside of the multidimensional subset");i&&(0,n.pC)(i.timeExtent)&&(h=i.timeExtent)}const y=this._combineMosaicRuleWithTimeExtent(this.exportImageServiceParameters.mosaicRule,h),f=this._getQueryParams({geometry:e.geometry,timeExtent:h,mosaicRule:y,renderingRule:this.version<10.4?this.renderingRule:null,renderingRules:l,pixelSize:i,returnCatalogItems:a,returnGeometry:d,raster:this.raster,maxItemCount:s?1:null});delete f.f;const b=new xt(f);try{await this.generateRasterInfo(this.renderingRule);const i=await He(this.url,b,{signal:t?.signal,query:{...this.customParameters}}),s=e.outFields,l=null!=i.value&&i.value.toLowerCase().includes("nodata");if(!a||d||!i?.catalogItems?.features.length||!o&&l)return this._processVisibleRastersResponse(i,{returnDomainValues:r,templateRRFunctionNames:u,showNoDataRecords:o,templateFields:s});const p=this.objectIdField||"ObjectId",c=i.catalogItems?.features??[],m=c.map((e=>e.attributes?.[p])),h=new et.Z({objectIds:m,returnGeometry:!0,outSpatialReference:e.outSpatialReference,outFields:[p]}),y=await this.queryRasters(h);return y?.features?.length&&y.features.forEach((t=>{c.forEach((i=>{i.attributes[p]===t.attributes[p]&&(i.geometry=new Ye.Z(t.geometry),(0,n.pC)(e.outSpatialReference)&&(i.geometry.spatialReference=e.outSpatialReference))}))})),this._processVisibleRastersResponse(i,{returnDomainValues:r,templateRRFunctionNames:u,showNoDataRecords:o,templateFields:s})}catch{throw new g.Z("imagery-layer:query-visible-rasters","encountered error when querying visible rasters")}}async fetchVariableStatisticsHistograms(e,t){const r=(0,f.default)(i(this.parsedUrl)+"/statistics",{query:this._getQueryParams({variable:e}),signal:t}).then((e=>e.data?.statistics)),s=(0,f.default)(i(this.parsedUrl)+"/histograms",{query:this._getQueryParams({variable:e}),signal:t}).then((e=>e.data?.histograms)),n=await Promise.all([r,s]);return n[0]&&n[0].forEach((e=>{e.avg=e.mean,e.stddev=e.standardDeviation})),{statistics:n[0]||null,histograms:n[1]||null}}async createFlowMesh(e,t){const i=this._rasterJobHandler.instance;return i?i.createFlowMesh(e,t):(0,Vt.GE)(e.meshType,e.simulationSettings,e.flowData,(0,n.pC)(t.signal)?t.signal:(new AbortController).signal)}getMultidimensionalSubsetVariables(e){const t=e??this.serviceRasterInfo.multidimensionalInfo;return(0,le.jj)(this.multidimensionalSubset,t)}async _fetchService(e){await this._fetchServiceInfo(e),this.rasterInfo||(this.rasterInfo=this.serviceRasterInfo);const t=this.sourceJSON,r=(0,n.pC)(this.serviceRasterInfo)?Promise.resolve(this.serviceRasterInfo):(0,rt.g)(i(this.parsedUrl),t,{signal:e,query:this._getQueryParams()}).then((e=>(this._set("serviceRasterInfo",e),this._set("multidimensionalInfo",e.multidimensionalInfo),e))),s=this._hasRenderingRule(this.renderingRule)?this.generateRasterInfo(this.renderingRule,{signal:e}):null,o=this._getRasterFunctionInfos();return Promise.all([r,s,o]).then((e=>{e[1]?this._set("rasterInfo",e[1]):this._set("rasterInfo",e[0]),e[2]&&this._set("rasterFunctionInfos",e[2]),this.renderer&&!this._isSupportedRenderer(this.renderer)&&(this._set("renderer",null),w.Z.getLogger(this.declaredClass).warn("ArcGISImageService","Switching to the default renderer. Renderer applied is not valid for this Imagery Layer")),this._set("renderer",this._configRenderer(this.renderer)),this.addHandles([(0,v.YP)((()=>this.renderingRule),(e=>{(this.renderer||this.symbolizer||this.popupEnabled&&this.popupTemplate)&&this.generateRasterInfo(e).then((e=>{e&&(this.rasterInfo=e)}))}))]);const{serviceRasterInfo:t}=this;(0,n.pC)(t.multidimensionalInfo)&&this._updateMultidimensionalDefinition(t)}))}_combineMosaicRuleWithTimeExtent(e,t){const i=this.timeInfo,{multidimensionalInfo:r}=this.serviceRasterInfo;if((0,n.Wi)(e)||(0,n.Wi)(r)||(0,n.Wi)(t)||(0,n.Wi)(i?.startField))return e;const{startField:s}=i,o=r.variables.some((e=>e.dimensions.some((e=>e.name===s))))?s:"StdTime";if(e=e.clone(),"mosaic-dataset"===this.sourceType)return e.multidimensionalDefinition=e.multidimensionalDefinition?.filter((e=>e.dimensionName!==o)),this._cleanupMultidimensionalDefinition(e);e.multidimensionalDefinition=e.multidimensionalDefinition||[];const a=e.multidimensionalDefinition.filter((e=>e.dimensionName===o)),l=(0,n.pC)(t.start)?t.start.getTime():null,u=(0,n.pC)(t.end)?t.end.getTime():null,p=null==l||null==u||l===u,c=p?[l||u]:[[l,u]],d=this.version>=10.8;if(a.length)a.forEach((e=>{e.dimensionName===o&&(d?(e.dimensionName=null,e.isSlice=!1,e.values=[]):(e.isSlice=p,e.values=c))}));else if(!d){const t=e.multidimensionalDefinition.filter((e=>null!=e.variableName&&null==e.dimensionName));t.length?t.forEach((e=>{e.dimensionName=o,e.isSlice=p,e.values=c})):e.multidimensionalDefinition.push(new x.Z({variableName:"",dimensionName:o,isSlice:p,values:c}))}return this._cleanupMultidimensionalDefinition(e)}_cleanupMultidimensionalDefinition(e){return(0,n.Wi)(e)?null:(e.multidimensionalDefinition&&(e.multidimensionalDefinition=e.multidimensionalDefinition.filter((e=>!(!e.variableName&&!e.dimensionName))),0===e.multidimensionalDefinition.length&&(e.multidimensionalDefinition=null)),"mosaic-dataset"!==this.sourceType&&null==e.multidimensionalDefinition?null:e)}async _prepareForQuery(e,t){if(!(await this._fetchCapabilities(t?.signal)).operations.supportsQuery)throw new g.Z("imagery-layer:query-rasters","query operation is not supported on the input image service");return e=(0,n.pC)(e)?(0,u.TJ)(et.Z,e):this.createQuery(),t=this._getRequestOptions(t),this.raster&&(t.query={...t.query,raster:this.raster}),{query:e,requestOptions:t}}async _initJobHandler(){if(null!=this._rasterJobHandler.connectionPromise)return this._rasterJobHandler.connectionPromise;const e=new ae.Z;this._rasterJobHandler.connectionPromise=e.initialize().then((()=>{this._rasterJobHandler.instance=e}),(()=>{})),await this._rasterJobHandler.connectionPromise}_shutdownJobHandler(){this._rasterJobHandler.instance&&this._rasterJobHandler.instance.destroy(),this._rasterJobHandler.instance=null,this._rasterJobHandler.connectionPromise=null,this._rasterJobHandler.refCount=0,this._cachedRendererJson=null}_isSupportedRenderer(e){const{rasterInfo:t,renderingRule:i}=this;return"unique-value"===e.type&&this._hasRenderingRule(i)&&1===t?.bandCount&&["u8","s8"].includes(t.pixelType)||null!=t&&null!=e&&(0,ce.U0)(t).includes(e.type)}async _fetchCapabilities(e){return this.capabilities||await this._fetchServiceInfo(e),this.capabilities}async _fetchServiceInfo(e){let t=this.sourceJSON;if(!t){const{data:r,ssl:s}=await(0,f.default)(i(this.parsedUrl),{query:this._getQueryParams(),signal:e});t=r,this.sourceJSON=t,s&&(this.url=this.url.replace(/^http:/i,"https:"))}if(t.capabilities?.toLowerCase().split(",").map((e=>e.trim())).indexOf("tilesonly")>-1)throw new g.Z("imagery-layer:fetch-service-info","use ImageryTileLayer to open tiles-only image services");this.read(t,{origin:"service",url:this.parsedUrl})}_isMosaicDataset(e){return e.serviceSourceType?"esriImageServiceSourceTypeMosaicDataset"===e.serviceSourceType:e.fields?.length>0}_isMosaicRuleSupported(e){if(!e)return!1;const t=this._isMosaicDataset(e),i=e.currentVersion>=10.71&&e.hasMultidimensions&&!(e.fields?.length>1);return t||i}_isVectorFieldResampleFunction(e){if((0,n.Wi)(e))return!1;const{functionName:t,functionArguments:i}=e,r="resample"===t?.toLowerCase(),s=i?.ResampleType||i?.resampleType;return r&&(7===s||10===s)}_isPicture(){return!this.format||this.format.includes("jpg")||this.format.includes("png")}_configRenderer(e){const t=this._isPicture(),{rasterInfo:i}=this;if(!t&&!this.pixelFilter||this._isVectorDataSet()){if(!this.bandIds&&i.bandCount>=3){const e=(0,ce.YD)(i);!e||3===i.bandCount&&0===e[0]&&1===e[1]&&2===e[2]||(this.bandIds=e)}e||(e=(0,ce.Ob)(i,{bandIds:this.bandIds,variableName:this.renderingRule?null:this.mosaicRule?.multidimensionalDefinition?.[0].variableName}));const t=(0,ce.ol)(e.toJSON());this.symbolizer?(this.symbolizer.rendererJSON=t,this.symbolizer.rasterInfo=i):this.symbolizer=new de.Z({rendererJSON:t,rasterInfo:i}),this.symbolizer.bind().success||(this.symbolizer=null)}return e}_clonePixelData(e){return null==e?e:{extent:e.extent&&e.extent.clone(),pixelBlock:(0,n.pC)(e.pixelBlock)?e.pixelBlock.clone():null}}_getQueryParams(e){e&&(0,n.pC)(e.renderingRule)&&"string"!=typeof e.renderingRule&&(e.renderingRule=this._getRenderingRuleString(e.renderingRule));const{raster:t,viewId:i}=this;return{raster:t,viewId:i,f:"json",...e,...this.customParameters}}_getRequestOptions(e){return{...e,query:{...e?.query,...this.customParameters}}}_decodePixelBlock(e,t,i){return this._rasterJobHandler.instance?this._rasterJobHandler.instance.decode({data:e,options:t}):(0,ue.J)(e,t,i)}async _getRasterFunctionInfos(e){const t=this.sourceJSON.rasterFunctionInfos;return this.loaded?t:t&&this.version>=10.3?1===t.length&&"none"===t[0].name.toLowerCase()?t:(await(0,f.default)(i(this.parsedUrl)+"/rasterFunctionInfos",{query:this._getQueryParams(),signal:e})).data?.rasterFunctionInfos:null}_canRequestImageElement(e){return!this.pixelFilter&&(!e||e.includes("png"))}async _requestArrayBuffer(e){const{imageProps:t,requestAsImageElement:r,returnImageBitmap:s,signal:n}=e;if(r&&this._canRequestImageElement(t.format)){const r=`${i(this.parsedUrl)}/exportImage`,{data:o}=await(0,f.default)(r,{responseType:s?"blob":"image",query:this._getQueryParams({f:"image",...this.refreshParameters,...e.imageServiceParameters}),signal:n});return o instanceof Blob?{imageBitmap:await(0,re.g)(o,r),params:t}:{imageOrCanvasElement:o,params:t}}const o=this._initJobHandler(),a=(0,f.default)(i(this.parsedUrl)+"/exportImage",{responseType:"array-buffer",query:this._getQueryParams({f:"image",...e.imageServiceParameters}),signal:n}),l=(await Promise.all([a,o]))[0].data,u=t.format||"jpgpng";let p=u;if("bsq"!==p&&"bip"!==p&&(p=(0,ue.y)(l)),!p)throw new g.Z("imagery-layer:fetch-image","unsupported format signature "+String.fromCharCode.apply(null,new Uint8Array(l)));const c={signal:n},d="gif"===u||"bmp"===u||u.includes("png")&&("png"===p||"jpg"===p)?(0,ue.J)(l,{useCanvas:!0,...t},c):this._decodePixelBlock(l,{width:t.width,height:t.height,planes:null,pixelType:null,noDataValue:null,format:u},c);return{pixelData:{pixelBlock:await d,extent:t.extent},params:t}}_generateRasterInfo(e,t){const r={...t,query:this._getQueryParams()};return(0,rt.N)(i(this.parsedUrl),e,r)}_isValidCustomizedMosaicRule(e){return e&&JSON.stringify(e.toJSON())!==JSON.stringify(this.defaultMosaicRule?.toJSON())}_updateMultidimensionalDefinition(e){if(this._isValidCustomizedMosaicRule(this.mosaicRule))return;let t=(0,le.Tj)(e,{multidimensionalSubset:this.multidimensionalSubset});if((0,n.pC)(t)&&t.length>0){this.mosaicRule=this.mosaicRule||new K;const e=this.mosaicRule.multidimensionalDefinition;!this.sourceJSON.defaultVariableName&&this.renderingRule&&"none"!==this.renderingRule.functionName?.toLowerCase()&&t.forEach((e=>e.variableName="")),t=t.filter((({variableName:e,dimensionName:t})=>e&&"*"!==e||t)),!e?.length&&t.length&&(this.mosaicRule.multidimensionalDefinition=t)}}_processVisibleRastersResponse(e,t){t=t||{};const i=e.value,{templateRRFunctionNames:r,showNoDataRecords:s,returnDomainValues:o,templateFields:a}=t,l=e.processedValues;let u=e.catalogItems&&e.catalogItems.features,p=e.properties&&e.properties.Values&&e.properties.Values.map((e=>e.replace(/ /gi,", ")))||[];const c=this.objectIdField||"ObjectId",d="string"==typeof i&&i.toLowerCase().includes("nodata"),m=[];if(i&&!u&&!d){const e={};e[c]=0,p=[i],u=[new h.Z(this.fullExtent,null,e)]}if(!u)return[];let y,f,g;this._updateResponseFieldNames(u,a),d&&!s&&(u=[]);for(let e=0;e<u.length;e++){if(y=u[e],null!=i){if(f=p[e],g=this.renderingRule&&l&&l.length>0&&r&&r.length>0&&r.includes(this.renderingRule.functionName)?l[r.indexOf(this.renderingRule.functionName)]:i,"nodata"===f.toLowerCase()&&!s)continue;const t="Raster.ItemPixelValue",o="Raster.ServicePixelValue";y.attributes[t]=f,y.attributes[o]=g,this._updateFeatureWithMagDirValues(y,f);const a=this.fields&&this.fields.length>0;let u=this.renderingRule&&(0,n.pC)(this.serviceRasterInfo.attributeTable)?a?f:i:g;this.renderingRule||(u=a?f:i),this._updateFeatureWithRasterAttributeTableValues(y,u)}if(y.sourceLayer=y.layer=this,o&&this._updateFeatureWithDomainValues(y),r&&l&&r.length===l.length)for(let e=0;e<r.length;e++){const t="Raster.ServicePixelValue."+r[e];y.attributes[t]=l[e]}m.push(u[e])}return m}_processMultidimensionalIntersection(e,t,i){const{multidimensionalSubset:r}=this;if(!r)return{isOutSide:!1,timeExtent:t,mosaicRule:i=this._combineMosaicRuleWithTimeExtent(i,t)};if(r){const{isOutside:i,intersection:s}=(0,le.W2)(r,{geometry:e,timeExtent:t});if(i)return{isOutSide:!0,timeExtent:null,mosaicRule:null};s&&(0,n.pC)(s.timeExtent)&&(t=s.timeExtent)}if(i=this._combineMosaicRuleWithTimeExtent(i,t),(0,n.pC)(i)&&i.multidimensionalDefinition){const{isOutside:e}=(0,le.W2)(r,{multidimensionalDefinition:i.multidimensionalDefinition});if(e)return{isOutSide:!0,timeExtent:null,mosaicRule:null}}return{isOutSide:!1,timeExtent:t,mosaicRule:i}}_updateFeatureWithRasterAttributeTableValues(e,t){const i=this.rasterInfo.attributeTable||this.serviceRasterInfo.attributeTable;if((0,n.Wi)(i))return;const{features:r,fields:s}=i,o=s.map((e=>e.name)).filter((e=>"value"===e.toLowerCase())),a=o&&o[0];if(!a)return;const l=r.filter((e=>e.attributes[a]===(null!=t?parseInt(t,10):null)));l&&l[0]&&s.forEach((t=>{const i=this._rasterAttributeTableFieldPrefix+t.name;e.attributes[i]=l[0].attributes[t.name]}))}_updateFeatureWithMagDirValues(e,t){if(!this._isVectorDataSet())return;const i=t.split(/,\s*/).map((e=>parseFloat(e))),r=i.map((e=>[e])),s=i.map((e=>({minValue:e,maxValue:e,noDataValue:null}))),n=new ne.Z({height:1,width:1,pixelType:"f32",pixels:r,statistics:s});null!=this.pixelFilter&&this.pixelFilter({pixelBlock:n,extent:new Xe.Z(0,0,0,0,this.spatialReference)});const o="esriImageServiceDataTypeVector-MagDir"===this.serviceDataType?[n.pixels[0][0],n.pixels[1][0]]:(0,pe.Tg)([n.pixels[0][0],n.pixels[1][0]]);e.attributes["Raster.Magnitude"]=o[0],e.attributes["Raster.Direction"]=o[1]}_updateFeatureWithDomainValues(e){const t=this.fields&&this.fields.filter((e=>e.domain&&"coded-value"===e.domain.type));null!=t&&t.forEach((t=>{const i=e.attributes[t.name];if(null!=i){const r=t.domain.codedValues.find((e=>e.code===i));r&&(e.attributes[t.name]=r.name)}}))}_updateResponseFieldNames(e,t){if(!t||t.length<1)return;const i=this.fieldsIndex;(0,n.Wi)(i)||e.forEach((e=>{if(e&&e.attributes)for(const r of t){const t=i.get(r)?.name;t&&t!==r&&(e.attributes[r]=e.attributes[t],delete e.attributes[t])}}))}_getRenderingRuleString(e){if(e){let t=e.toJSON();return t=t.rasterFunctionDefinition??t,(t.thumbnail||t.thumbnailEx)&&(t.thumbnail=t.thumbnailEx=null),JSON.stringify(t)}return null}_hasRenderingRule(e){return null!=e&&null!=e.functionName&&"none"!==e.functionName.toLowerCase()}_updateRenderingRulesFunctionName(e,t){if(!e||e.length<1)return;if("Raw"===e)return e.replace("Raw","None");const i=e.toLowerCase().replace(/ /gi,"_");return t.has(i)?t.get(i):e}_isRFTJson(e){return e&&e.name&&e.arguments&&e.function&&e.hasOwnProperty("functionType")}_isVectorDataSet(){return"esriImageServiceDataTypeVector-UV"===this.serviceDataType||"esriImageServiceDataTypeVector-MagDir"===this.serviceDataType}_applyMosaicAndRenderingRules(e){const{raster:t,mosaicRule:i,renderingRule:r}=this;r&&null==e.renderingRule&&(e.renderingRule=r),i&&null==e.mosaicRule&&(e.mosaicRule=i),t&&null==e.raster&&(e.raster=t)}_readCapabilities(e){const t=e.capabilities?e.capabilities.toLowerCase().split(",").map((e=>e.trim())):["image","catalog"],{currentVersion:i,advancedQueryCapabilities:r,maxRecordCount:s}=e,n=t.includes("image"),o="esriImageServiceDataTypeElevation"===e.serviceDataType,a=!!(e.spatialReference||e.extent&&e.extent.spatialReference),l=t.includes("edit"),u=t.includes("mensuration")&&a,p=null==e.mensurationCapabilities?[]:e.mensurationCapabilities.toLowerCase().split(",").map((e=>e.trim())),c=u&&p.includes("basic");return{data:{supportsAttachment:!1},operations:{supportsComputeHistograms:n,supportsExportImage:n,supportsIdentify:n,supportsMeasure:u,supportsDownload:t.includes("download"),supportsQuery:t.includes("catalog")&&e.fields&&e.fields.length>0,supportsGetSamples:i>=10.2&&n,supportsProject:i>=10.3&&n,supportsComputeStatisticsHistograms:i>=10.4&&n,supportsQueryBoundary:i>=10.6&&n,supportsCalculateVolume:i>=10.7&&o,supportsComputePixelLocation:i>=10.7&&t.includes("catalog"),supportsComputeAngles:i>=10.91,supportsAdd:l,supportsDelete:l,supportsEditing:l,supportsUpdate:l,supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsChangeTracking:!1,supportsQueryAttachments:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsExceedsLimitStatistics:!1,supportsQueryAnalytics:!1,supportsQueryTopFeatures:!1},query:{maxRecordCount:s,maxRecordCountFactor:void 0,supportsStatistics:!!r?.supportsStatistics,supportsOrderBy:!!r?.supportsOrderBy,supportsDistinct:!!r?.supportsDistinct,supportsPagination:!!r?.supportsPagination,supportsStandardizedQueriesOnly:!!r?.useStandardizedQueries,supportsPercentileStatistics:!!r?.supportsPercentileStatistics,supportsCentroid:!!r?.supportsReturningGeometryCentroid,supportsDistance:!!r?.supportsQueryWithDistance,supportsExtent:!!r?.supportsReturningQueryExtent,supportsGeometryProperties:!!r?.supportsReturningGeometryProperties,supportsHavingClause:!!r?.supportsHavingClause,supportsQuantization:!1,supportsQuantizationEditMode:!1,supportsQueryGeometry:!1,supportsResultType:!1,supportsMaxRecordCountFactor:!1,supportsSqlExpression:!1,supportsTopFeaturesQuery:!1,supportsQueryByOthers:!1,supportsHistoricMoment:!1,supportsFormatPBF:!1,supportsDisjointSpatialRelationship:!1,supportsCacheHint:!1,supportsSpatialAggregationStatistics:!1,supportedSpatialAggregationStatistics:{envelope:!1,centroid:!1,convexHull:!1},supportsDefaultSpatialReference:!!r?.supportsDefaultSR,supportsFullTextSearch:!1,supportsCompactGeometry:!1,standardMaxRecordCount:void 0,tileMaxRecordCount:void 0},mensuration:{supportsDistanceAndAngle:c,supportsAreaAndPerimeter:c,supportsPointOrCentroid:c,supportsHeightFromBaseAndTop:u&&p.includes("base-top height"),supportsHeightFromBaseAndTopShadow:u&&p.includes("base-top shadow height"),supportsHeightFromTopAndTopShadow:u&&p.includes("top-top shadow height"),supports3D:u&&p.includes("3d")}}}};function i(e){return e?.path??""}return(0,r._)([(0,l.Cb)()],t.prototype,"_functionRasterInfos",void 0),(0,r._)([(0,l.Cb)()],t.prototype,"_rasterJobHandler",void 0),(0,r._)([(0,l.Cb)()],t.prototype,"_cachedRendererJson",void 0),(0,r._)([(0,l.Cb)({readOnly:!0})],t.prototype,"_serviceSupportsMosaicRule",void 0),(0,r._)([(0,C.r)("_serviceSupportsMosaicRule",["currentVersion","fields"])],t.prototype,"readServiceSupportsMosaicRule",null),(0,r._)([(0,l.Cb)()],t.prototype,"_rasterAttributeTableFieldPrefix",void 0),(0,r._)([(0,l.Cb)({readOnly:!0})],t.prototype,"_rasterFunctionNamesIndex",null),(0,r._)([(0,l.Cb)()],t.prototype,"adjustAspectRatio",void 0),(0,r._)([(0,l.Cb)({type:[u.z8],json:{write:!0}})],t.prototype,"bandIds",void 0),(0,r._)([(0,C.r)("bandIds")],t.prototype,"readBandIds",null),(0,r._)([(0,l.Cb)({readOnly:!0,json:{read:!1}})],t.prototype,"capabilities",void 0),(0,r._)([(0,C.r)("service","capabilities",["capabilities","currentVersion","serviceDataType"])],t.prototype,"readCapabilities",null),(0,r._)([(0,l.Cb)({type:Number})],t.prototype,"compressionQuality",void 0),(0,r._)([(0,c.c)("compressionQuality")],t.prototype,"writeCompressionQuality",null),(0,r._)([(0,l.Cb)({type:Number})],t.prototype,"compressionTolerance",void 0),(0,r._)([(0,c.c)("compressionTolerance")],t.prototype,"writeCompressionTolerance",null),(0,r._)([(0,l.Cb)({json:{read:{source:"copyrightText"}}})],t.prototype,"copyright",void 0),(0,r._)([(0,l.Cb)({readOnly:!0,dependsOn:["_serviceSupportsMosaicRule"]})],t.prototype,"defaultMosaicRule",void 0),(0,r._)([(0,C.r)("defaultMosaicRule",["defaultMosaicMethod"])],t.prototype,"readDefaultMosaicRule",null),(0,r._)([(0,l.Cb)({type:String,json:{name:"layerDefinition.definitionExpression",write:{enabled:!0,allowNull:!0}}})],t.prototype,"definitionExpression",void 0),(0,r._)([(0,l.Cb)({readOnly:!0,constructOnly:!0})],t.prototype,"exportImageServiceParameters",void 0),(0,r._)([(0,l.Cb)()],t.prototype,"rasterInfo",void 0),(0,r._)([(0,l.Cb)({readOnly:!0,type:[te.Z]})],t.prototype,"fields",void 0),(0,r._)([(0,l.Cb)({readOnly:!0})],t.prototype,"fieldsIndex",null),(0,r._)([(0,l.Cb)({type:["png","png8","png24","png32","jpg","bmp","gif","jpgpng","lerc","tiff"],json:{write:!0}})],t.prototype,"format",null),(0,r._)([(0,C.r)("service","format",["serviceDataType"])],t.prototype,"readFormat",null),(0,r._)([(0,l.Cb)({type:Xe.Z})],t.prototype,"fullExtent",void 0),(0,r._)([(0,l.Cb)({readOnly:!0})],t.prototype,"hasMultidimensions",void 0),(0,r._)([(0,l.Cb)({json:{read:{source:"maxImageHeight"}}})],t.prototype,"imageMaxHeight",void 0),(0,r._)([(0,l.Cb)({json:{read:{source:"maxImageWidth"}}})],t.prototype,"imageMaxWidth",void 0),(0,r._)([(0,l.Cb)({type:String,json:{type:Y.c.jsonValues,read:Y.c.read,write:Y.c.write}})],t.prototype,"interpolation",void 0),(0,r._)([(0,l.Cb)()],t.prototype,"minScale",void 0),(0,r._)([(0,C.r)("service","minScale")],t.prototype,"readMinScale",null),(0,r._)([(0,l.Cb)()],t.prototype,"maxScale",void 0),(0,r._)([(0,C.r)("service","maxScale")],t.prototype,"readMaxScale",null),(0,r._)([(0,l.Cb)({type:K})],t.prototype,"mosaicRule",null),(0,r._)([(0,C.r)("mosaicRule",["mosaicRule","defaultMosaicMethod"])],t.prototype,"readMosaicRule",null),(0,r._)([(0,c.c)("mosaicRule")],t.prototype,"writeMosaicRule",null),(0,r._)([(0,l.Cb)()],t.prototype,"multidimensionalInfo",void 0),(0,r._)([(0,l.Cb)({type:se.Z,json:{write:!0}})],t.prototype,"multidimensionalSubset",void 0),(0,r._)([(0,l.Cb)({json:{type:u.z8}})],t.prototype,"noData",void 0),(0,r._)([(0,c.c)("noData")],t.prototype,"writeNoData",null),(0,r._)([(0,l.Cb)({type:String,json:{type:Y.k.jsonValues,read:Y.k.read,write:Y.k.write}})],t.prototype,"noDataInterpretation",void 0),(0,r._)([(0,l.Cb)({type:String,readOnly:!0,json:{read:{source:["fields"]}}})],t.prototype,"objectIdField",void 0),(0,r._)([(0,C.r)("objectIdField")],t.prototype,"readObjectIdField",null),(0,r._)([(0,l.Cb)({})],t.prototype,"geometryType",void 0),(0,r._)([(0,l.Cb)({})],t.prototype,"typeIdField",void 0),(0,r._)([(0,l.Cb)({})],t.prototype,"types",void 0),(0,r._)([(0,l.Cb)({readOnly:!0})],t.prototype,"parsedUrl",null),(0,r._)([(0,l.Cb)({type:Function})],t.prototype,"pixelFilter",void 0),(0,r._)([(0,l.Cb)()],t.prototype,"raster",void 0),(0,r._)([(0,l.Cb)({readOnly:!0})],t.prototype,"sourceType",void 0),(0,r._)([(0,C.r)("sourceType",["serviceSourceType","fields"])],t.prototype,"readSourceType",null),(0,r._)([(0,l.Cb)()],t.prototype,"viewId",void 0),(0,r._)([(0,l.Cb)({types:y.dr,json:{name:"layerDefinition.drawingInfo.renderer",origins:{"web-scene":{types:y.FK,name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy:e=>({enabled:e&&"vector-field"!==e.type&&"flow"!==e.type})}}}}})],t.prototype,"renderer",null),(0,r._)([(0,C.r)("renderer")],t.prototype,"readRenderer",null),(0,r._)([(0,c.c)("renderer")],t.prototype,"writeRenderer",null),(0,r._)([(0,l.Cb)()],t.prototype,"symbolizer",void 0),(0,r._)([(0,l.Cb)(S.Oh)],t.prototype,"opacity",void 0),(0,r._)([(0,l.Cb)({readOnly:!0})],t.prototype,"rasterFields",null),(0,r._)([(0,l.Cb)({constructOnly:!0})],t.prototype,"rasterFunctionInfos",void 0),(0,r._)([(0,l.Cb)({type:F.Z})],t.prototype,"renderingRule",null),(0,r._)([(0,C.r)("renderingRule",["renderingRule","rasterFunctionInfos"])],t.prototype,"readRenderingRule",null),(0,r._)([(0,c.c)("renderingRule")],t.prototype,"writeRenderingRule",null),(0,r._)([(0,l.Cb)()],t.prototype,"serviceDataType",void 0),(0,r._)([(0,l.Cb)({readOnly:!0,type:ye.Z})],t.prototype,"spatialReference",void 0),(0,r._)([(0,C.r)("spatialReference",["spatialReference","extent"])],t.prototype,"readSpatialReference",null),(0,r._)([(0,l.Cb)({json:{type:At.jsonValues}})],t.prototype,"pixelType",void 0),(0,r._)([(0,C.r)("pixelType")],t.prototype,"readPixelType",null),(0,r._)([(0,c.c)("pixelType")],t.prototype,"writePixelType",null),(0,r._)([(0,l.Cb)({constructOnly:!0,type:oe.Z})],t.prototype,"serviceRasterInfo",void 0),(0,r._)([(0,l.Cb)()],t.prototype,"sourceJSON",void 0),(0,r._)([(0,l.Cb)(S.HQ)],t.prototype,"url",void 0),(0,r._)([(0,l.Cb)({readOnly:!0})],t.prototype,"version",void 0),(0,r._)([(0,C.r)("version",["currentVersion","fields","timeInfo"])],t.prototype,"readVersion",null),t=(0,r._)([(0,p.j)("esri.layers.mixins.ArcGISImageService")],t),t};var qt=i(17287),zt=i(71612),Gt=i(17017),Ht=i(38009),Bt=i(16859),Lt=i(34760),Ut=i(72965),kt=i(28294),Qt=i(60199),Wt=i(32163);let $t=class extends((0,zt.h)((0,kt.n)((0,Ut.M)((0,Ht.q)((0,Bt.I)(Et((0,Lt.Q)((0,Gt.N)((0,qt.Y)((0,o.R)(d.Z))))))))))){constructor(...e){super(...e),this.legendEnabled=!0,this.isReference=null,this.operationalLayerType="ArcGISImageServiceLayer",this.popupEnabled=!0,this.popupTemplate=null,this.type="imagery"}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}load(e){const t=(0,n.pC)(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Image Service"]},e).catch(a.r9).then((()=>this._fetchService(t)))),Promise.resolve(this)}writeOperationalLayerType(e,t,i){const r="vector-field"===this.renderer?.type;t[i]=r?"ArcGISImageServiceVectorLayer":"ArcGISImageServiceLayer"}get defaultPopupTemplate(){return this.createPopupTemplate()}createPopupTemplate(e){const t=this.rasterFields,i=this.title,r=new Set;let s=!1,n=!1;this.capabilities&&(s=this.capabilities.operations.supportsQuery&&this.fields&&this.fields.length>0,n="esriImageServiceDataTypeVector-UV"===this.serviceDataType||"esriImageServiceDataTypeVector-MagDir"===this.serviceDataType);const o=new Set;s&&o.add("raster.itempixelvalue");for(const e of t){const t=e.name.toLowerCase();o.has(t)||t.includes("raster.servicepixelvalue.")||r.add(e.name)}return n&&r.add("raster.magnitude").add("raster.direction"),(0,Wt.eZ)({fields:t,title:i},{...e,visibleFieldNames:r})}queryFeatures(e,t){return this.queryRasters(e,t).then((e=>{if(e?.features)for(const t of e.features)t.layer=t.sourceLayer=this;return e}))}queryFeatureCount(e,t){return this.queryRasterCount(e,t)}redraw(){this.emit("redraw")}serviceSupportsSpatialReference(e){return(0,Qt.D)(this,e)}};(0,r._)([(0,l.Cb)(S.rn)],$t.prototype,"legendEnabled",void 0),(0,r._)([(0,l.Cb)({type:["show","hide"]})],$t.prototype,"listMode",void 0),(0,r._)([(0,l.Cb)({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],$t.prototype,"isReference",void 0),(0,r._)([(0,l.Cb)({type:["ArcGISImageServiceLayer"],json:{origins:{"web-map":{type:["ArcGISImageServiceLayer","ArcGISImageServiceVectorLayer"],read:!1,write:{target:"layerType",ignoreOrigin:!0}}}}})],$t.prototype,"operationalLayerType",void 0),(0,r._)([(0,c.c)("web-map","operationalLayerType")],$t.prototype,"writeOperationalLayerType",null),(0,r._)([(0,l.Cb)(S.C_)],$t.prototype,"popupEnabled",void 0),(0,r._)([(0,l.Cb)({type:s.Z,json:{read:{source:"popupInfo"},write:{target:"popupInfo"}}})],$t.prototype,"popupTemplate",void 0),(0,r._)([(0,l.Cb)({readOnly:!0})],$t.prototype,"defaultPopupTemplate",null),(0,r._)([(0,l.Cb)({readOnly:!0,json:{read:!1}})],$t.prototype,"type",void 0),$t=(0,r._)([(0,p.j)("esri.layers.ImageryLayer")],$t);const Xt=$t},17287:(e,t,i)=>{i.d(t,{Y:()=>u});var r=i(43697),s=i(92604),n=i(70586),o=i(5600),a=(i(75215),i(67676),i(52011)),l=i(66677);const u=e=>{let t=class extends e{get title(){if(this._get("title")&&"defaults"!==this.originOf("title"))return this._get("title");if(this.url){const e=(0,l.Qc)(this.url);if((0,n.pC)(e)&&e.title)return e.title}return this._get("title")||""}set title(e){this._set("title",e)}set url(e){this._set("url",(0,l.Nm)(e,s.Z.getLogger(this.declaredClass)))}};return(0,r._)([(0,o.Cb)()],t.prototype,"title",null),(0,r._)([(0,o.Cb)({type:String})],t.prototype,"url",null),t=(0,r._)([(0,a.j)("esri.layers.mixins.ArcGISService")],t),t}},90082:(e,t,i)=>{i.d(t,{g:()=>s});var r=i(20102);async function s(e,t){try{return await createImageBitmap(e)}catch(e){throw new r.Z("request:server",`Unable to load ${t}`,{url:t,error:e})}}},60199:(e,t,i)=>{i.d(t,{D:()=>n});var r=i(66677);const s=[];function n(e,t){if((0,r.M8)(e.url??""))return!0;const{wkid:i}=t;for(const t of s){if((e.version??0)>=t[0])return!0;if("function"==typeof t[1]&&(t[1]=t[1]()),t[1].has(i))return!1}return!0}s.push([10.91,()=>{const e=new Set([9709,9716,9741,9761,9766]);for(let t=9712;t<=9713;t++)e.add(t);for(let t=9748;t<=9749;t++)e.add(t);for(let t=20904;t<=20932;t++)e.add(t);for(let t=21004;t<=21032;t++)e.add(t);for(let t=21207;t<=21264;t++)e.add(t);for(let t=21307;t<=21364;t++)e.add(t);for(let t=102759;t<=102760;t++)e.add(t);for(let t=102901;t<=102955;t++)e.add(t);return e}]),s.push([10.9,()=>{const e=new Set([9300,9354,9364,9367,9373,9377,9387,9456,9473,9498,9678,9680,29874,103599,103872,104028]);for(let t=9356;t<=9360;t++)e.add(t);for(let t=9404;t<=9407;t++)e.add(t);for(let t=9476;t<=9482;t++)e.add(t);for(let t=9487;t<=9494;t++)e.add(t);for(let t=9697;t<=9699;t++)e.add(t);return e}]),s.push([10.81,()=>{const e=new Set([9265,9333,103598,103699]);for(let t=9248;t<=9254;t++)e.add(t);for(let t=9271;t<=9273;t++)e.add(t);for(let t=9284;t<=9285;t++)e.add(t);for(let t=21453;t<=21463;t++)e.add(t);return e}]),s.push([10.8,()=>{const e=new Set([8088,8395,8428,8433,8531,8687,8692,8694,8699,8900,9003,9006,9009,9012,9017,9191]);for(let t=8035;t<=8036;t++)e.add(t);for(let t=8455;t<=8456;t++)e.add(t);for(let t=8518;t<=8529;t++)e.add(t);for(let t=8533;t<=8536;t++)e.add(t);for(let t=8538;t<=8540;t++)e.add(t);for(let t=8677;t<=8679;t++)e.add(t);for(let t=8902;t<=8903;t++)e.add(t);for(let t=8907;t<=8910;t++)e.add(t);for(let t=8949;t<=8951;t++)e.add(t);for(let t=8972;t<=8987;t++)e.add(t);for(let t=9039;t<=9040;t++)e.add(t);for(let t=9068;t<=9069;t++)e.add(t);for(let t=9140;t<=9141;t++)e.add(t);for(let t=9148;t<=9150;t++)e.add(t);for(let t=9153;t<=9159;t++)e.add(t);for(let t=9205;t<=9218;t++)e.add(t);for(let t=9221;t<=9222;t++)e.add(t);for(let t=54098;t<=54101;t++)e.add(t);return e}]),s.push([10.71,()=>{const e=new Set([6316]);for(let t=8351;t<=8353;t++)e.add(t);for(let t=9294;t<=9297;t++)e.add(t);for(let t=22619;t<=22621;t++)e.add(t);for(let t=103586;t<=103594;t++)e.add(t);return e}]),s.push([10.7,()=>{const e=new Set([8387,8391,8427,8545,8682,8685,8818,31370,104022,104024,104975]);for(let t=8065;t<=8068;t++)e.add(t);for(let t=8082;t<=8083;t++)e.add(t);for(let t=8379;t<=8385;t++)e.add(t);for(let t=8836;t<=8840;t++)e.add(t);for(let t=8857;t<=8860;t++)e.add(t);for(let t=53035;t<=53037;t++)e.add(t);for(let t=54090;t<=54091;t++)e.add(t);for(let t=102498;t<=102499;t++)e.add(t);return e}]),s.push([10.61,()=>new Set([102497])]),s.push([10.6,()=>{const e=new Set([7803,7805,7887,8086,8232,8237,8240,8246,8249,8252,8255,9019,9391]);for(let t=7755;t<=7787;t++)e.add(t);for(let t=7791;t<=7795;t++)e.add(t);for(let t=7799;t<=7801;t++)e.add(t);for(let t=7825;t<=7831;t++)e.add(t);for(let t=7877;t<=7878;t++)e.add(t);for(let t=7882;t<=7883;t++)e.add(t);for(let t=7991;t<=7992;t++)e.add(t);for(let t=8042;t<=8043;t++)e.add(t);for(let t=8058;t<=8059;t++)e.add(t);for(let t=8311;t<=8348;t++)e.add(t);for(let t=9060;t<=9067;t++)e.add(t);for(let t=102562;t<=102568;t++)e.add(t);for(let t=102799;t<=102900;t++)e.add(t);return e}]),s.push([10.51,()=>{const e=new Set([7683,7881,7886,7899,8888,9e3]);for(let t=8013;t<=8032;t++)e.add(t);for(let t=9053;t<=9057;t++)e.add(t);for(let t=104017;t<=104018;t++)e.add(t);for(let t=104971;t<=104974;t++)e.add(t);return e}]),s.push([10.5,()=>{const e=new Set([6962,7035,7037,7039,7041,7084,7086,7133,7798,102399]);for(let t=4087;t<=4088;t++)e.add(t);for(let t=5896;t<=5899;t++)e.add(t);for(let t=7005;t<=7007;t++)e.add(t);for(let t=7057;t<=7070;t++)e.add(t);for(let t=7073;t<=7082;t++)e.add(t);for(let t=7109;t<=7128;t++)e.add(t);for(let t=7844;t<=7859;t++)e.add(t);return e}])},41818:(e,t,i)=>{i.d(t,{P:()=>o});var r=i(11282),s=i(34599),n=i(14165);async function o(e,t,i){const o=(0,r.en)(e);return(0,s.hH)(o,n.Z.from(t),{...i}).then((e=>e.data.count))}},5396:(e,t,i)=>{i.d(t,{G:()=>o});var r=i(11282),s=i(34599),n=i(14165);async function o(e,t,i){const o=(0,r.en)(e);return(0,s.Ev)(o,n.Z.from(t),{...i}).then((e=>e.data.objectIds))}},4967:(e,t,i)=>{i.d(t,{F:()=>l,e:()=>a});var r=i(11282),s=i(34599),n=i(74889),o=i(14165);async function a(e,t,i){const r=await l(e,t,i);return n.Z.fromJSON(r)}async function l(e,t,i){const n=(0,r.en)(e),a={...i},l=o.Z.from(t),{data:u}=await(0,s.JT)(n,l,l.sourceSpatialReference,a);return u}},28694:(e,t,i)=>{i.d(t,{p:()=>n});var r=i(70586),s=i(69285);function n(e,t,i){if(!i||!i.features||!i.hasZ)return;const n=(0,s.k)(i.geometryType,t,e.outSpatialReference);if(!(0,r.Wi)(n))for(const e of i.features)n(e.geometry)}},98326:(e,t,i)=>{i.d(t,{Z:()=>c});var r,s=i(43697),n=i(96674),o=i(5600),a=i(75215),l=(i(67676),i(52011));const u={1:{id:1,rotation:0,mirrored:!1},2:{id:2,rotation:0,mirrored:!0},3:{id:3,rotation:180,mirrored:!1},4:{id:4,rotation:180,mirrored:!0},5:{id:5,rotation:-90,mirrored:!0},6:{id:6,rotation:90,mirrored:!1},7:{id:7,rotation:90,mirrored:!0},8:{id:8,rotation:-90,mirrored:!1}};let p=r=class extends n.wq{constructor(e){super(e),this.contentType=null,this.exifInfo=null,this.id=null,this.globalId=null,this.keywords=null,this.name=null,this.parentGlobalId=null,this.parentObjectId=null,this.size=null,this.url=null}get orientationInfo(){const{exifInfo:e}=this,t=function(e){const{exifInfo:t,exifName:i,tagName:r}=e;if(!t||!i||!r)return null;const s=t.find((e=>e.name===i));return s?function(e){const{tagName:t,tags:i}=e;if(!i||!t)return null;const r=i.find((e=>e.name===t));return r&&r.value||null}({tagName:r,tags:s.tags}):null}({exifName:"Exif IFD0",tagName:"Orientation",exifInfo:e});return u[t]||null}clone(){return new r({contentType:this.contentType,exifInfo:this.exifInfo,id:this.id,globalId:this.globalId,keywords:this.keywords,name:this.name,parentGlobalId:this.parentGlobalId,parentObjectId:this.parentObjectId,size:this.size,url:this.url})}};(0,s._)([(0,o.Cb)({type:String})],p.prototype,"contentType",void 0),(0,s._)([(0,o.Cb)()],p.prototype,"exifInfo",void 0),(0,s._)([(0,o.Cb)({readOnly:!0})],p.prototype,"orientationInfo",null),(0,s._)([(0,o.Cb)({type:a.z8})],p.prototype,"id",void 0),(0,s._)([(0,o.Cb)({type:String})],p.prototype,"globalId",void 0),(0,s._)([(0,o.Cb)({type:String})],p.prototype,"keywords",void 0),(0,s._)([(0,o.Cb)({type:String})],p.prototype,"name",void 0),(0,s._)([(0,o.Cb)({json:{read:!1}})],p.prototype,"parentGlobalId",void 0),(0,s._)([(0,o.Cb)({json:{read:!1}})],p.prototype,"parentObjectId",void 0),(0,s._)([(0,o.Cb)({type:a.z8})],p.prototype,"size",void 0),(0,s._)([(0,o.Cb)({json:{read:!1}})],p.prototype,"url",void 0),p=r=(0,s._)([(0,l.j)("esri.layers.support.AttachmentInfo")],p);const c=p},56545:(e,t,i)=>{i.d(t,{Z:()=>d});var r,s=i(43697),n=i(96674),o=i(22974),a=i(5600),l=i(75215),u=i(52011),p=i(30556);let c=r=class extends n.wq{constructor(e){super(e),this.attachmentTypes=null,this.attachmentsWhere=null,this.cacheHint=void 0,this.keywords=null,this.globalIds=null,this.name=null,this.num=null,this.objectIds=null,this.returnMetadata=!1,this.size=null,this.start=null,this.where=null}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10}clone(){return new r((0,o.d9)({attachmentTypes:this.attachmentTypes,attachmentsWhere:this.attachmentsWhere,cacheHint:this.cacheHint,keywords:this.keywords,where:this.where,globalIds:this.globalIds,name:this.name,num:this.num,objectIds:this.objectIds,returnMetadata:this.returnMetadata,size:this.size,start:this.start}))}};(0,s._)([(0,a.Cb)({type:[String],json:{write:!0}})],c.prototype,"attachmentTypes",void 0),(0,s._)([(0,a.Cb)({type:String,json:{read:{source:"attachmentsDefinitionExpression"},write:{target:"attachmentsDefinitionExpression"}}})],c.prototype,"attachmentsWhere",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],c.prototype,"cacheHint",void 0),(0,s._)([(0,a.Cb)({type:[String],json:{write:!0}})],c.prototype,"keywords",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],c.prototype,"globalIds",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],c.prototype,"name",void 0),(0,s._)([(0,a.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],c.prototype,"num",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],c.prototype,"objectIds",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{default:!1,write:!0}})],c.prototype,"returnMetadata",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],c.prototype,"size",void 0),(0,s._)([(0,a.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],c.prototype,"start",void 0),(0,s._)([(0,p.c)("start"),(0,p.c)("num")],c.prototype,"writeStart",null),(0,s._)([(0,a.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],c.prototype,"where",void 0),c=r=(0,s._)([(0,u.j)("esri.rest.support.AttachmentQuery")],c),c.from=(0,l.se)(c);const d=c},75935:(e,t,i)=>{i.r(t),i.d(t,{default:()=>h});var r,s=i(43697),n=(i(66577),i(96674)),o=i(22974),a=i(5600),l=i(75215),u=i(52011),p=i(30556),c=i(10158),d=i(82971);let m=r=class extends n.wq{constructor(e){super(e),this.cacheHint=void 0,this.dynamicDataSource=void 0,this.gdbVersion=null,this.geometryPrecision=void 0,this.historicMoment=null,this.maxAllowableOffset=void 0,this.objectIds=null,this.orderByFields=null,this.outFields=null,this.outSpatialReference=null,this.relationshipId=void 0,this.start=void 0,this.num=void 0,this.returnGeometry=!1,this.returnM=void 0,this.returnZ=void 0,this.where=null}_writeHistoricMoment(e,t){t.historicMoment=e&&e.getTime()}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10,this.start>0&&null==this.where&&(t.definitionExpression="1=1")}clone(){return new r((0,o.d9)({cacheHint:this.cacheHint,dynamicDataSource:this.dynamicDataSource,gdbVersion:this.gdbVersion,geometryPrecision:this.geometryPrecision,historicMoment:this.historicMoment&&new Date(this.historicMoment.getTime()),maxAllowableOffset:this.maxAllowableOffset,objectIds:this.objectIds,orderByFields:this.orderByFields,outFields:this.outFields,outSpatialReference:this.outSpatialReference,relationshipId:this.relationshipId,start:this.start,num:this.num,returnGeometry:this.returnGeometry,where:this.where,returnZ:this.returnZ,returnM:this.returnM}))}};(0,s._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],m.prototype,"cacheHint",void 0),(0,s._)([(0,a.Cb)({type:c.n,json:{write:!0}})],m.prototype,"dynamicDataSource",void 0),(0,s._)([(0,a.Cb)({type:String,json:{write:!0}})],m.prototype,"gdbVersion",void 0),(0,s._)([(0,a.Cb)({type:Number,json:{write:!0}})],m.prototype,"geometryPrecision",void 0),(0,s._)([(0,a.Cb)({type:Date})],m.prototype,"historicMoment",void 0),(0,s._)([(0,p.c)("historicMoment")],m.prototype,"_writeHistoricMoment",null),(0,s._)([(0,a.Cb)({type:Number,json:{write:!0}})],m.prototype,"maxAllowableOffset",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],m.prototype,"objectIds",void 0),(0,s._)([(0,a.Cb)({type:[String],json:{write:!0}})],m.prototype,"orderByFields",void 0),(0,s._)([(0,a.Cb)({type:[String],json:{write:!0}})],m.prototype,"outFields",void 0),(0,s._)([(0,a.Cb)({type:d.Z,json:{read:{source:"outSR"},write:{target:"outSR"}}})],m.prototype,"outSpatialReference",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],m.prototype,"relationshipId",void 0),(0,s._)([(0,a.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],m.prototype,"start",void 0),(0,s._)([(0,p.c)("start"),(0,p.c)("num")],m.prototype,"writeStart",null),(0,s._)([(0,a.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],m.prototype,"num",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],m.prototype,"returnGeometry",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],m.prototype,"returnM",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],m.prototype,"returnZ",void 0),(0,s._)([(0,a.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],m.prototype,"where",void 0),m=r=(0,s._)([(0,u.j)("esri.rest.support.RelationshipQuery")],m),m.from=(0,l.se)(m);const h=m},28141:(e,t,i)=>{i.d(t,{Z:()=>C});var r,s=i(43697),n=i(66577),o=i(92835),a=i(35454),l=i(96674),u=i(22974),p=i(70586),c=i(5600),d=i(75215),m=i(52011),h=i(30556),y=i(33955);i(67676);let f=r=class extends l.wq{constructor(e){super(e),this.groupByFields=void 0,this.topCount=void 0,this.orderByFields=void 0}clone(){return new r({groupByFields:this.groupByFields,topCount:this.topCount,orderByFields:this.orderByFields})}};(0,s._)([(0,c.Cb)({type:[String],json:{write:!0}})],f.prototype,"groupByFields",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{write:!0}})],f.prototype,"topCount",void 0),(0,s._)([(0,c.Cb)({type:[String],json:{write:!0}})],f.prototype,"orderByFields",void 0),f=r=(0,s._)([(0,m.j)("esri.rest.support.TopFilter")],f);const g=f;var b,R=i(82971);const w=new a.X({esriSpatialRelIntersects:"intersects",esriSpatialRelContains:"contains",esriSpatialRelCrosses:"crosses",esriSpatialRelDisjoint:"disjoint",esriSpatialRelEnvelopeIntersects:"envelope-intersects",esriSpatialRelIndexIntersects:"index-intersects",esriSpatialRelOverlaps:"overlaps",esriSpatialRelTouches:"touches",esriSpatialRelWithin:"within",esriSpatialRelRelation:"relation"}),v=new a.X({esriSRUnit_Meter:"meters",esriSRUnit_Kilometer:"kilometers",esriSRUnit_Foot:"feet",esriSRUnit_StatuteMile:"miles",esriSRUnit_NauticalMile:"nautical-miles",esriSRUnit_USNauticalMile:"us-nautical-miles"});let _=b=class extends l.wq{constructor(e){super(e),this.cacheHint=void 0,this.distance=void 0,this.geometry=null,this.geometryPrecision=void 0,this.maxAllowableOffset=void 0,this.num=void 0,this.objectIds=null,this.orderByFields=null,this.outFields=null,this.outSpatialReference=null,this.resultType=null,this.returnGeometry=!1,this.returnM=void 0,this.returnZ=void 0,this.start=void 0,this.spatialRelationship="intersects",this.timeExtent=null,this.topFilter=void 0,this.units=null,this.where="1=1"}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10}clone(){return new b((0,u.d9)({cacheHint:this.cacheHint,distance:this.distance,geometry:this.geometry,geometryPrecision:this.geometryPrecision,maxAllowableOffset:this.maxAllowableOffset,num:this.num,objectIds:this.objectIds,orderByFields:this.orderByFields,outFields:this.outFields,outSpatialReference:this.outSpatialReference,resultType:this.resultType,returnGeometry:this.returnGeometry,returnZ:this.returnZ,returnM:this.returnM,start:this.start,spatialRelationship:this.spatialRelationship,timeExtent:this.timeExtent,topFilter:this.topFilter,units:this.units,where:this.where}))}};(0,s._)([(0,c.Cb)({type:Boolean,json:{write:!0}})],_.prototype,"cacheHint",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{write:{overridePolicy:e=>({enabled:e>0})}}})],_.prototype,"distance",void 0),(0,s._)([(0,c.Cb)({types:n.qM,json:{read:y.im,write:!0}})],_.prototype,"geometry",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{write:!0}})],_.prototype,"geometryPrecision",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{write:!0}})],_.prototype,"maxAllowableOffset",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],_.prototype,"num",void 0),(0,s._)([(0,c.Cb)({json:{write:!0}})],_.prototype,"objectIds",void 0),(0,s._)([(0,c.Cb)({type:[String],json:{write:!0}})],_.prototype,"orderByFields",void 0),(0,s._)([(0,c.Cb)({type:[String],json:{write:!0}})],_.prototype,"outFields",void 0),(0,s._)([(0,c.Cb)({type:R.Z,json:{read:{source:"outSR"},write:{target:"outSR"}}})],_.prototype,"outSpatialReference",void 0),(0,s._)([(0,c.Cb)({type:String,json:{write:!0}})],_.prototype,"resultType",void 0),(0,s._)([(0,c.Cb)({json:{write:!0}})],_.prototype,"returnGeometry",void 0),(0,s._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],_.prototype,"returnM",void 0),(0,s._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],_.prototype,"returnZ",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],_.prototype,"start",void 0),(0,s._)([(0,h.c)("start"),(0,h.c)("num")],_.prototype,"writeStart",null),(0,s._)([(0,c.Cb)({type:String,json:{read:{source:"spatialRel",reader:w.read},write:{target:"spatialRel",writer:w.write}}})],_.prototype,"spatialRelationship",void 0),(0,s._)([(0,c.Cb)({type:o.Z,json:{write:!0}})],_.prototype,"timeExtent",void 0),(0,s._)([(0,c.Cb)({type:g,json:{write:!0}})],_.prototype,"topFilter",void 0),(0,s._)([(0,c.Cb)({type:String,json:{read:v.read,write:{writer:v.write,overridePolicy(e){return{enabled:(0,p.pC)(e)&&(0,p.pC)(this.distance)&&this.distance>0}}}}})],_.prototype,"units",void 0),(0,s._)([(0,c.Cb)({type:String,json:{write:!0}})],_.prototype,"where",void 0),_=b=(0,s._)([(0,m.j)("esri.rest.support.TopFeaturesQuery")],_),_.from=(0,d.se)(_);const C=_}}]);