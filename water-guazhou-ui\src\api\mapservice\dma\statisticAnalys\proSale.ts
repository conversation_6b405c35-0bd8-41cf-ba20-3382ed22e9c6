import { request } from '@/plugins/axios';

/**
 * 查询dma分区产销差报表
 * @returns
 */
export const GetDmaPSReport = (params: {
  type: 'year' | 'month' | 'yearInterval';
  /**
   * 先年的时候传
   */
  date: string;
  start: string;
  end: string;
}) => {
  return request({
    url: `/api/spp/statisticsReport/nrw`,
    method: 'get',
    params
  });
};
/**
 * 导出dma分区产销差报表
 * @returns
 */
export const ExportDmaPSReport = (params: {
  type: 'year' | 'month' | 'yearInterval';
  /**
   * 先年的时候传
   */
  date: string;
  start: string;
  end: string;
}) => {
  return request({
    url: `/api/spp/statisticsReport/nrwExport`,
    method: 'get',
    params,
    responseType: 'blob'
  });
};
/**
 * 产销差报表详情
 * @param params
 * @returns
 */
export const GetDmaPSReportDetail = (params: {
  type: 'year' | 'month';
  /**
   * 年的时候传
   */
  date: string;
  start: string;
  end: string;
  /**
   * 多个用逗号分隔
   */
  partitionIds?: string;
}) => {
  return request({
    url: '/api/spp/statisticsReport/nrwDetail',
    method: 'get',
    params
  });
};
/**
 * 导出产销差报表详情
 * @param params
 * @returns
 */
export const ExportDmaPSReportDetail = (params: {
  type: 'year' | 'month';
  /**
   * 年的时候传
   */
  date: string;
  start: string;
  end: string;
  /**
   * 多个用逗号分隔
   */
  partitionIds?: string;
}) => {
  return request({
    url: '/api/spp/statisticsReport/nrwDetailExport',
    method: 'get',
    params,
    responseType: 'blob'
  });
};
/**
 * 产销差报表详情的详情
 * @param params
 * @returns
 */
export const GetDmaPSReportDetailDetail = (params: {
  type: 'year' | 'month';
  /**
   * 年的时候传
   */
  date: string;
  start: string;
  end: string;
  partitionId: string;
}) => {
  return request({
    url: '/api/spp/statisticsReport/nrwDetail/detail',
    method: 'get',
    params
  });
};
/**
 * 导出产销差报表详情的详情
 * @param params
 * @returns
 */
export const ExportDmaPSReportDetailDetail = (params: {
  type: 'year' | 'month';
  /**
   * 年的时候传
   */
  date: string;
  start: string;
  end: string;
  partitionId: string;
}) => {
  return request({
    url: '/api/spp/statisticsReport/nrwDetail/detailExport',
    method: 'get',
    params,
    responseType: 'blob'
  });
};

/**
 * 锦州专用
 * 查询dma分区产销差报表
 * @returns
 */
export const GetYTDmaPSReport = (params: {
  type: 'year' | 'month' | 'yearInterval';
  /**
   * 先年的时候传
   */
  date: string;
  start: string;
  end: string;
}) => {
  return request({
    url: `/api/jinzhou/nrw`,
    method: 'get',
    params
  });
};

/**
 * 锦州专用
 * 导出dma分区产销差报表
 * @returns
 */
export const ExportYTDmaPSReport = (params: {
  type: 'year' | 'month' | 'yearInterval';
  /**
   * 先年的时候传
   */
  date: string;
  start: string;
  end: string;
}) => {
  return request({
    url: `/api/jinzhou/nrwExport`,
    method: 'get',
    params,
    responseType: 'blob'
  });
};

/**
 * 锦州专用
 * 产销差报表详情
 * @param params
 * @returns
 */
export const GetYTDmaPSReportDetail = (params: {
  type: 'year' | 'month';
  /**
   * 年的时候传
   */
  date: string;
  start: string;
  end: string;
  /**
   * 多个用逗号分隔
   */
  partitionIds?: string;
}) => {
  return request({
    url: '/api/jinzhou/nrwDetail',
    method: 'get',
    params
  });
};

/**
 * 锦州专用
 * 产销差报表详情的详情
 * @param params
 * @returns
 */
export const GetYTDmaPSReportDetailDetail = (params: {
  type: 'year' | 'month';
  /**
   * 年的时候传
   */
  date: string;
  start: string;
  end: string;
  partitionId: string;
}) => {
  return request({
    url: '/api/jinzhou/nrwDetail/detail',
    method: 'get',
    params
  });
};

// 远传表质量
export const GetPartitionProAndSaleByPeriod_jz = (params: {
  startDate: string;
  endDate: string;
}) => {
  return request({
    url: '/api/jinzhou/dmaBook/getMeterByBrand',
    method: 'get',
    params
  });
};

// 收费实时
export const GetPartitionProSaleDeltaStatistic = (params: {
  startDate: string;
  endDate: string;
}) => {
  return request({
    url: '/api/jinzhou/dmaBook/GetMeterConsumeByMonth',
    method: 'get',
    params
  });
};
