package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionEstimate;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.math.BigDecimal;

@Getter
@Setter
public class SoConstructionEstimateSaveRequest extends SaveRequest<SoConstructionEstimate> {
    // 所属工程编号
    @NotNullOrEmpty
    private String constructionCode;

    // 预算人
    @NotNullOrEmpty
    private String budgeter;

    // 预算金额，万元
    @NotNullOrEmpty
    private BigDecimal cost;

    // 地址
    private String address;

    // 备注
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    public String valid(IStarHttpRequest request) {
        if (BigDecimal.ZERO.compareTo(cost) > 0) {
            return "预算金额不能为负数";
        }

        return super.valid(request);
    }

    @Override
    protected SoConstructionEstimate build() {
        SoConstructionEstimate entity = new SoConstructionEstimate();
        entity.setConstructionCode(constructionCode);
        entity.setStatus(SoGeneralTaskStatus.PROCESSING);

        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionEstimate update(String id) {
        SoConstructionEstimate entity = new SoConstructionEstimate();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionEstimate entity) {
        entity.setBudgeter(budgeter);
        entity.setCost(cost);
        entity.setAddress(address);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
        entity.setUpdateTime(createTime());
        entity.setUpdateUser(currentUserUUID());
    }
}