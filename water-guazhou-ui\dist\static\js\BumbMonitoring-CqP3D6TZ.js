import{_ as It}from"./Panel-DyoxrWMd.js";import{a7 as st,d as Rt,c as I,r as v,o as Nt,Q as zt,ay as Dt,g as L,n as B,q as b,F as j,p as e,aw as R,bh as D,i as M,an as V,fI as Mt,aB as et,aJ as Vt,a0 as Gt,a2 as Jt,e7 as Wt,_ as Ot,aq as Qt,by as At,cE as Pt,a1 as Yt,D as qt,l as ot,C as jt}from"./index-r0dFAfgr.js";import{w as at}from"./Point-WxyopZva.js";import{e as Ut,d as U}from"./zhandian-YaGuQZe6.js";import{q as Zt}from"./index-cIaXVz1R.js";import{f as Ht}from"./DateFormatter-Bm9a68Ax.js";import{m as it,i as Et}from"./map-location-BX7km8Cl.js";import{u as Kt}from"./useStation-DJgnSZIA.js";import{h as $t,r as Xt}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as nt}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{b as te}from"./ViewHelper-BGCZjxXH.js";import"./v4-SoommWqA.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./fieldconfig-Bk3o1wi7.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const ee=T=>{let S={};return T>100?S={colorIn:"rgba(250,55,119,1)",colorOut:"rgba(250,55,119,0.6)"}:S={colorIn:"rgba(0,255,255,1)",colorOut:"rgba(0,255,255,0.6)"},{grid:{top:0,left:0,right:0,bottom:40},tooltip:{formatter:"{b} : {c} Mpa"},color:"#fff",series:[{type:"gauge",axisLabel:{show:!1},center:["50%","50%"],radius:"80%",splitNumber:10,axisTick:{show:!1},color:"#464646",splitLine:{show:!1},axisLine:{lineStyle:{color:[[T/100,S.colorIn],[1,"#111F42"]],width:5}},detail:{textStyle:{fontSize:25,fontWeight:700,color:"#fff"},offsetCenter:[0,0]},title:{textStyle:{fontSize:10,color:"#fff"},offsetCenter:[0,"50%"]},data:[{name:"Mpa",value:T,color:"#fff"}],pointer:{show:!1,showAbove:!1,length:0,width:0},animationDuration:4e3},{type:"gauge",center:["50%","50%"],radius:"80%",splitNumber:10,axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!1},itemStyle:{color:"#fff",shadowBlur:10,shadowOffsetX:2,shadowOffsetY:2},detail:{show:!1},data:[{value:T,name:"出水压力",color:"#fff"}],title:{textStyle:{color:"#fff",fontSize:12},offsetCenter:[0,"115%"]},pointer:{show:!1,showAbove:!1,length:0,width:0},axisLine:{show:!0,lineStyle:{color:[[1,S.colorOut]],opacity:1,width:1,shadowBlur:0,shadowColor:"#fff"}}}]}},oe=T=>({title:{text:"水位",textStyle:{color:"#fff",fontSize:12,fontWight:400},bottom:0,left:"center"},grid:{left:0,right:0,top:0,bottom:0},series:[{name:"水位",type:"liquidFill",data:[{value:.6,direction:"left",itemStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(125,205,239,0.80)"},{offset:1,color:"rgba(13,115,185,0.80)"}]}}},{value:.5,direction:"left",itemStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(125,205,239,0.80)"},{offset:1,color:"rgba(13,115,185,0.80)"}]}}},{value:.4,direction:"left",itemStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(125,205,239,0.80)"},{offset:1,color:"rgba(13,115,185,0.80)"}]}}}],radius:"80%",outline:{show:!1},backgroundStyle:{borderColor:"rgba(97,182,223,1)",borderWidth:1,color:"transparent"},label:{position:["50%","50%"],formatter(){return`${T} m`},fontWight:700,fontSize:25,color:"#fff"},center:["50%","50%"]}]}),ae=(T,N,S)=>({textStyle:{color:"#fff"},tooltip:{trigger:"axis"},legend:{show:!0,right:10,top:10,textStyle:{color:"#fff"}},xAxis:{type:"category",boundaryGap:!1,data:T||[],axisLine:{lineStyle:{color:"#fff"}},axisTick:{lineStyle:{color:"#fff"}},axisLabel:{textStyle:{color:"#ffffff"}}},yAxis:{type:"value",name:"供水量",nameLoacation:"top",splitLine:{lineStyle:{color:"#fff",opacity:.2}},splitArea:{show:!1},axisLine:{lineStyle:{color:"#fff"}},axisTick:{lineStyle:{color:"#fff"}},axisLabel:{textStyle:{color:"#ffffff"}},boundaryGap:[0,"100%"]},grid:{top:40,left:50,right:20,bottom:30},series:[{name:"昨日",type:"line",symbol:"none",sampling:"lttb",itemStyle:{color:"#D7A540"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(215,165,64,1)"},{offset:1,color:"rgba(215,165,64,0.1)"}],global:!1}},data:N||[]},{name:"今日",type:"line",symbol:"none",sampling:"lttb",itemStyle:{color:"#4098D7"},areaStyle:{color:new st(0,0,0,1,[{offset:0,color:"rgba(64,152,215,1)"},{offset:1,color:"rgba(64,152,215,0.1)"}])},data:S||[]}]}),ie=(T,N,S)=>({tooltip:{trigger:"axis"},xAxis:{type:"category",data:N||[],show:!0,boundaryGap:!1,color:"#fff",axisLabel:{textStyle:{color:"#ffffff"}},splitLine:{show:!1},splitArea:{show:!1}},yAxis:{name:"值",type:"value",scale:!0,color:"#ffffff",axisLabel:{textStyle:{color:"#ffffff"}},splitArea:{show:!1},splitLine:{lineStyle:{color:"#ffffff",opacity:.2,type:"dashed"}}},grid:{left:70,right:50,top:10,bottom:30},series:[{type:"line",symbol:!1,smooth:!0,name:T,itemStyle:{color:"#4098D7"},areaStyle:{color:new st(0,0,0,1,[{offset:0,color:"rgba(64,152,215,1)"},{offset:1,color:"rgba(64,152,215,0.1)"}])},data:S||[]}]}),ne=()=>[{id:"no1",name:"1#泵",status:"online",timetotal:"53:24:05"},{id:"no2",name:"2#泵",status:"online",timetotal:"72:15:33"},{id:"no3",name:"3#泵",status:"offline",timetotal:"21:24:05"},{id:"no4",name:"4#泵",status:"online",timetotal:"14:24:05"},{id:"no5",name:"5#泵",status:"online",timetotal:"56:24:05"},{id:"no6",name:"5#泵",status:"online",timetotal:"56:24:05"},{id:"no7",name:"5#泵",status:"online",timetotal:"56:24:05"},{id:"no8",name:"5#泵",status:"online",timetotal:"56:24:05"},{id:"no9",name:"5#泵",status:"online",timetotal:"56:24:05"},{id:"no10",name:"5#泵",status:"online",timetotal:"56:24:05"}],se=()=>[{minWidth:120,label:"监测点名称",prop:"name"},{minWidth:120,label:"流量",prop:"liuliang",formItemConfig:{type:"input",label:"",field:""}},{minWidth:120,label:"压力",prop:"yali",formItemConfig:{type:"input",label:"",field:""}},{minWidth:120,label:"余氯",prop:"yulv",formItemConfig:{type:"input",label:"",field:""}},{minWidth:120,label:"浊度",prop:"zhuodu",formItemConfig:{type:"input",label:"",field:""}},{minWidth:120,label:"PH",prop:"ph",formItemConfig:{type:"input",label:"",field:""}}],re=()=>[{id:"1",name:"1#监测点",liuliang:"20",yali:"40",yulv:"2.22",zhuodu:"0.05",ph:"6.9"},{id:"2",name:"2#监测点",liuliang:"",yali:"",yulv:"",zhuodu:"",ph:""},{id:"3",name:"3#监测点",liuliang:"",yali:"",yulv:"",zhuodu:"",ph:""},{id:"4",name:"4#监测点",liuliang:"",yali:"",yulv:"",zhuodu:"",ph:""}],Z="/static/png/bumb-Cp-F2WN8.png",le="/static/png/bumb1-DLww6oD0.png",ue={class:"panel-left"},ce={class:"chartbox top"},pe={class:"chart-left"},fe={class:"chart-right"},me={class:"attr-box"},de={class:"info"},ge={class:"attr-box"},he={class:"info"},be={class:"attr-box"},ye={class:"info"},ve={class:"chartbox bottom"},_e={class:"panel-center"},xe={key:0,class:"tabContent"},Te={class:"tab-pane-content ergong_xinxizonglan"},Ce={class:"left"},we=["src"],Se={class:"right"},Fe={key:1,class:"tabContent ergong_gongyiyu"},Le={class:"header"},Be={class:"gylist"},ke={class:"fullscreen"},Ie=["src"],Re={key:2,class:"tabContent ergong_yuanchengkongzhi"},Ne={class:"left"},ze=["src"],De={class:"right"},Me={class:"top"},Ve={class:"bottom"},Ge={class:"timetotal"},Je={class:"bottom-switch"},We={key:3,class:"tabContent"},Oe={key:4,class:"tabContent"},Qe={key:5,class:"tabContent"},Ae={class:"qushituChart"},Pe=Rt({__name:"BumbMonitoring",props:{view:{},telport:{}},setup(T){const{getAllStationOption:N,getStationAttrGroups:S}=Kt(),y=T,W=I(),H=I(),E=I(),O=I(),J=I(),z=I(),Q=I(),A=I(),k=v({TopLeftChart:null,TopRightChart:null,BottomChart:null,QuShiTuOption:null}),a=v({attrs:{remainder:"-",remainder_status:"yellow",turbidity:"-",turbidity_status:"purple",ph:"-",ph_status:"lightblue"},curTab:"xinxizonglan",xinxizonglan:[{name:"泵房名称",value:"一级泵站"},{name:"所在区域",value:"槽上水厂"},{name:"X坐标",value:1345222223e-3},{name:"Y坐标",value:345633.567},{name:"地址",value:"重庆市北碚区"},{name:"",value:"-"}],GongYiTuCurImg:Z,YuanChengKongZhi:ne(),curStation:void 0,stationGroups:[],dynamicColumns:[],marks:[],stationList:[]}),F={},p=v({height:300,dataList:[],columns:[{label:"名称",prop:"name"}],handleRowClick:o=>{$(o)},pagination:{hide:!0}}),rt=async()=>{var o;p.currentRow.stationId&&((o=Q.value)!=null&&o.dataForm&&(Q.value.dataForm.curTab=a.curTab),Y(),a.stationGroups=await S(p.currentRow.stationId),mt(),k.BottomChart=ae([0,3,6,9,12,15,18,21,24],[12389+Number((Math.random()*1e4).toFixed(2)),17607+Number((Math.random()*1e4).toFixed(2)),21263+Number((Math.random()*1e4).toFixed(2)),12123+Number((Math.random()*1e4).toFixed(2)),22534+Number((Math.random()*1e4).toFixed(2)),18456+Number((Math.random()*1e4).toFixed(2)),21767+Number((Math.random()*1e4).toFixed(2)),16213+Number((Math.random()*1e4).toFixed(2)),12123+Number((Math.random()*1e4).toFixed(2))],[12123+Number((Math.random()*1e4).toFixed(2)),15767+Number((Math.random()*1e4).toFixed(2)),16213+Number((Math.random()*1e4).toFixed(2)),19123+Number((Math.random()*1e4).toFixed(2)),22234+Number((Math.random()*1e4).toFixed(2)),18456+Number((Math.random()*1e4).toFixed(2)),21213+Number((Math.random()*1e4).toFixed(2)),15767+Number((Math.random()*1e4).toFixed(2)),11123+Number((Math.random()*1e4).toFixed(2))]))},K=v({columns:[],data:{}}),P=v({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"泵站列表"},fields:[{type:"table",style:{height:"300px"},config:p}]},{fieldset:{desc:"水站状态"},fields:[{type:"attr-table",config:K}]}]}),lt=async(o="泵站")=>{var i,l,m,f,h,c,d,_,C,w,g;if(!o){o=((f=(m=(l=(i=P.group[0])==null?void 0:i.fields[0])==null?void 0:l.tabs[0])==null?void 0:m.value)==null?void 0:f.toString())||"",(h=W.value)!=null&&h.dataForm&&(W.value.dataForm.type=o);return}(c=F.graphicsLayer)==null||c.removeAll(),(d=F.textLayer)==null||d.removeAll(),p.loading=!0;const t=await Ut({stationType:o,projectId:(C=(_=Gt().navSelectedRange)==null?void 0:_.data)==null?void 0:C.id});a.stationList=await N();const r=await Jt({type:"stationInfo",key:o});let n=[];try{n=r.data[0]?JSON.parse(r.data[0].value):[]}catch{n=[]}p.columns=pt(n.map(u=>({minWidth:120,label:u.label,prop:u.value}))),a.marks=[],p.dataList=((w=t.data)==null?void 0:w.map(u=>{var q;const x=(q=u.location)==null?void 0:q.split(",");u.status=u.status||"offline",u.x=x[0]&&parseFloat(x[0]),u.y=x[1]&&parseFloat(x[1]);const G={title:u.name,visible:!1,x:u.x,y:u.y,attributes:{row:u,id:u.stationId},symbolConfig:{url:it}};return a.marks.push(G),u}))||[],p.currentRow=p.dataList[0],ut(),ct(),Y(),p.loading=!1,(g=P.group[0].fields[0].options)==null||g.map(u=>{u.data.suffix=u.value===""?t.data.length:u.value==="online"?t.data.filter(x=>x.status==="online").length:u.value==="warning"?t.data.filter(x=>x.status==="warning").length:u.value==="offline"?t.data.filter(x=>x.status==="offline").length:0}),a.dynamicColumns=n},ut=async()=>{if(!p.currentRow)return;const t=(await U(p.currentRow.stationId,"泵阀")).data||[];K.columns=[[{label:p.currentRow.name,prop:"valves",formItemConfig:{type:"btn-group",style:{flexWrap:"wrap"},size:"small",btns:t==null?void 0:t.map(r=>({perm:!0,styles:{margin:"6px"},text:r.propertyName,type:parseInt(r.value||"0.0")===0?"default":"primary",click:()=>{}}))}}]]},ct=()=>{p.dataList.map(o=>{var s,i,l,m,f;const t=new at({longitude:o.x,latitude:o.y,spatialReference:(s=y.view)==null?void 0:s.spatialReference}),r=$t(t.x,t.y,{picUrl:it,spatialReference:(i=y.view)==null?void 0:i.spatialReference,attributes:{row:o}}),n=Xt(t.x,t.y,{text:o.name,spatialReference:(l=y.view)==null?void 0:l.spatialReference,yOffset:-20});(m=F.graphicsLayer)==null||m.add(r),(f=F.textLayer)==null||f.add(n)})},pt=o=>[{minWidth:120,label:"名称",prop:"name"},{minWidth:160,label:"时间",prop:"time"},...o||[],{width:70,label:"状态",prop:"pressure_status",formatter:(t,r)=>r==="online"?"在线":"离线"}],ft=()=>({initTopCharts:async()=>{var s,i,l,m,f,h,c,d,_,C,w;if(!((s=p.currentRow)!=null&&s.stationId))return;const t=await U(p.currentRow.stationId),r=(l=(i=t.data)==null?void 0:i.find(g=>g.property==="water_level"))==null?void 0:l.value,n=(f=(m=t.data)==null?void 0:m.find(g=>g.property==="pressure"))==null?void 0:f.value;k.TopLeftChart=ee(n||0),k.TopRightChart=oe(r||0),a.attrs.remainder=((c=(h=t.data)==null?void 0:h.find(g=>g.property==="remainder"))==null?void 0:c.value)||"-",a.attrs.turbidity=((_=(d=t.data)==null?void 0:d.find(g=>g.property==="turbidity"))==null?void 0:_.value)||"-",a.attrs.ph=((w=(C=t.data)==null?void 0:C.find(g=>g.property==="ph"))==null?void 0:w.value)||"-"}}),{initTopCharts:mt}=ft(),dt=()=>{const o=v({height:440,columns:[{label:"属性",prop:"name"},{label:"值",prop:"value"}],dataList:[],pagination:{hide:!0}});return{refreshBasicData:()=>{var s,i;const r=(s=p.currentRow)==null?void 0:s.stationId,n=(i=a.stationList.find(l=>l.id===r))==null?void 0:i.data;o.dataList=Et(n||p.currentRow)},TableConfig_xxzl:o}},gt=()=>{A.value&&Wt(A.value)},{TableConfig_xxzl:ht,refreshBasicData:Y}=dt(),bt=()=>{const o=async()=>{var i,l;const s=n.group[0].fields[0];s&&(s.options=a.stationGroups),n.defaultValue={type:(i=a.stationGroups[0])==null?void 0:i.value},(l=O.value)==null||l.resetForm(),t()},t=async()=>{var i,l,m,f,h;r.loading=!0;const s=((l=(i=O.value)==null?void 0:i.dataForm)==null?void 0:l.type)||((m=n.defaultValue)==null?void 0:m.type);if(!s||!p.currentRow)r.dataList=[];else try{const c=await U((f=p.currentRow)==null?void 0:f.stationId,s);r.dataList=((h=c.data)==null?void 0:h.map(d=>(d.value=(d.value||"-")+" "+(d.unit||""),d)))||[]}catch{}r.loading=!1},r=v({columns:[{minWidth:100,label:"名称",prop:"propertyName"},{minWidth:140,label:"时间",prop:"collectionTime",formatter:s=>Ht(s.collectionTime)},{minWidth:120,label:"监测值",prop:"value"}],pagination:{hide:!0},dataList:[]}),n=v({labelPosition:"top",gutter:0,group:[{fields:[{xs:18,type:"radio-button",label:"",field:"type",options:[],onChange:()=>t()},{xs:6,type:"btn-group",style:{justifyContent:"flex-end"},btns:[{perm:!0,type:"warning",text:"导出",click:()=>{var s;(s=E.value)==null||s.exportTable()}}]}]}]});return{FormConfig_SJBB:n,TableConfig_SJBB:r,initSJBBTags:o}},{FormConfig_SJBB:yt,TableConfig_SJBB:vt,initSJBBTags:_t}=bt(),xt=()=>{const o=async()=>{var i,l;const s=n.group[0].fields[0];s&&(s.options=a.stationGroups),n.defaultValue={attrs:"",group:(i=a.stationGroups[0])==null?void 0:i.value},(l=z.value)==null||l.resetForm(),t()},t=()=>{var f,h,c,d,_,C,w,g;const s=n.group[0].fields[0],i=(s==null?void 0:s.options)||[],l=n.group[0].fields[1],m=((h=(f=z.value)==null?void 0:f.dataForm)==null?void 0:h.group)||((c=n.defaultValue)==null?void 0:c.group);m&&l&&(l.options=Yt(((_=(d=i.find(u=>u.value===m))==null?void 0:d.data)==null?void 0:_.attrList)||[],{id:"id",value:"id",label:"name"})),n.defaultValue={...((C=z.value)==null?void 0:C.dataForm)||n.defaultValue||{},attrs:(l==null?void 0:l.options)&&((w=l==null?void 0:l.options[0])==null?void 0:w.id)||""},(g=z.value)==null||g.resetForm(),r()},r=async()=>{var f,h,c,d,_,C,w,g,u,x;(f=J.value)==null||f.clear(),(h=J.value)==null||h.resize();const s=((d=(c=z.value)==null?void 0:c.dataForm)==null?void 0:d.attrs)||((_=n.defaultValue)==null?void 0:_.attrs),i=(u=(g=(w=(C=n.group[0])==null?void 0:C.fields[1])==null?void 0:w.options)==null?void 0:g.find(G=>G.value===s))==null?void 0:u.data,l=[],m=[];if(i!=null&&i.deviceId&&(i!=null&&i.attr)){const G=qt(i.deviceId),X=(await Zt({start:ot().subtract(1,"d").valueOf(),end:ot().valueOf(),type:"15m",attributes:[G+"."+i.attr]})).data||{};for(const tt in X){l.push(tt);const kt=X[tt];m.push(Object.values(kt)[0])}}k.QuShiTuOption=ie(i==null?void 0:i.name,l,m),(x=J.value)==null||x.resize()},n=v({labelPosition:"top",group:[{fields:[{type:"radio-button",label:"",field:"group",options:[],onChange:()=>t()},{type:"radio-button",label:"",field:"attrs",options:[],onChange:()=>r()}]}],defaultValue:{group:"",attrs:""}});return{FormConfig_QST:n,initQSTTags:o}},{FormConfig_QST:Tt,initQSTTags:Ct}=xt(),wt=v({defaultValue:{curTab:a.curTab},labelPosition:"top",group:[{fields:[{type:"tabs",label:"",field:"curTab",tabType:"simple",tabs:[{label:"信息总览",value:"xinxizonglan"},{label:"工艺图",value:"gongyitu"},{label:"数据报表",value:"shujubaobiao"},{label:"趋势图",value:"qushitu"}],onChange:o=>{switch(a.curTab=o,o){case"shujubaobiao":_t();break;case"qushitu":Ct();break;case"xinxizonglan":Y();break}}}]}]}),St=v({type:"select",label:"",field:"",size:"small",options:[{label:"工艺一",value:Z},{label:"工艺二",value:le}],onChange:o=>{a.GongYiTuCurImg=o}}),Ft=v({type:"switch",label:"",size:"small",field:"",activeValue:"online",inActiveValue:"offline"}),Lt=v({height:390,columns:se(),dataList:re(),pagination:{hide:!0}}),Bt=v({group:[{fields:[{type:"btn-group",label:"",field:"",btns:[{type:"primary",text:"保存",perm:!0,styles:{marginLeft:"auto"}}]},{type:"table",label:"",field:"",config:Lt}]}]}),$=o=>{var t,r;p.currentRow=o,(t=H.value)==null||t.Open(),rt(),(r=y.view)==null||r.goTo(new at({longitude:o.x,latitude:o.y,spatialReference:y.view.spatialReference}))};return Nt(()=>{lt(),y.view&&(F.graphicsLayer=nt(y.view,{id:"bumb_monitoring",title:"泵站监控"}),F.textLayer=nt(y.view,{id:"bumb_monitoring_pois",title:"泵站监控-注记"}),y.view&&te(y.view,o=>{o.results.length&&o.results.map(t=>{var r,n;if(t.type==="graphic"){const s=(n=(r=t.graphic)==null?void 0:r.attributes)==null?void 0:n.row;$(s)}})}))}),zt(()=>{var o,t;F.graphicsLayer&&((o=y.view)==null||o.map.remove(F.graphicsLayer)),F.textLayer&&((t=y.view)==null||t.map.remove(F.textLayer))}),(o,t)=>{var f;const r=Dt("VChart"),n=Ot,s=Qt,i=At,l=Pt,m=It;return L(),B(et,null,[b(m,{ref_key:"refPanel",ref:H,"custom-class":"map-panel",title:(f=a.curStation)==null?void 0:f.name},{left:j(()=>[e("div",ue,[e("div",ce,[e("div",pe,[b(r,{option:k.TopLeftChart},null,8,["option"])]),e("div",fe,[b(r,{option:k.TopRightChart},null,8,["option"])])]),e("div",me,[t[1]||(t[1]=e("div",{class:"lefticon yellow"},[e("div",{class:"lefticon-text"},"CL")],-1)),t[2]||(t[2]=e("span",{class:"name"},"余氯",-1)),e("div",de,[e("span",{class:R(["count",a.attrs.remainder_status])},D(a.attrs.remainder),3),e("span",{class:R(["unit",a.attrs.remainder_status])},"mg/L",2)])]),e("div",ge,[t[3]||(t[3]=e("div",{class:"lefticon purple"},[e("div",{class:"lefticon-text"},"TUR")],-1)),t[4]||(t[4]=e("span",{class:"name"},"浊度",-1)),e("div",he,[e("span",{class:R(["count",a.attrs.turbidity_status])},D(a.attrs.turbidity),3),e("span",{class:R(["unit",a.attrs.turbidity_status])},"NTU",2)])]),e("div",be,[t[5]||(t[5]=e("div",{class:"lefticon lightblue"},[e("div",{class:"lefticon-text"},"PH")],-1)),t[6]||(t[6]=e("span",{class:"name"},"PH",-1)),e("div",ye,[e("span",{class:R(["count",a.attrs.ph_status])},D(a.attrs.ph),3),e("span",{class:R(["unit",a.attrs.ph_status])},null,2)])]),e("div",ve,[b(r,{option:k.BottomChart},null,8,["option"])])])]),default:j(()=>{var h;return[e("div",_e,[b(n,{ref_key:"refFormTabs",ref:Q,class:"tabsForm",config:wt},null,8,["config"]),a.curTab==="xinxizonglan"?(L(),B("div",xe,[e("div",Te,[e("div",Ce,[e("img",{src:(h=a.curStation)==null?void 0:h.imgs,alt:""},null,8,we)]),e("div",Se,[b(s,{config:M(ht)},null,8,["config"])])])])):V("",!0),a.curTab==="gongyitu"?(L(),B("div",Fe,[e("div",Le,[e("div",Be,[b(i,{modelValue:a.GongYiTuCurImg,"onUpdate:modelValue":t[0]||(t[0]=c=>a.GongYiTuCurImg=c),config:St},null,8,["modelValue","config"])]),e("div",ke,[b(l,{style:{cursor:"pointer"},onClick:gt},{default:j(()=>[b(M(Mt))]),_:1}),t[7]||(t[7]=e("span",null,"全屏",-1))])]),e("div",{ref_key:"refGongYiTuFrame",ref:A,class:"content"},[e("img",{ref:"refGongYiTu",width:"100%",height:"100%",src:a.GongYiTuCurImg,alt:""},null,8,Ie)],512)])):V("",!0),a.curTab==="yuanchengkongzhi"?(L(),B("div",Re,[e("div",Ne,[e("img",{width:"100%",height:"100%",src:M(Z),alt:""},null,8,ze)]),e("div",De,[(L(!0),B(et,null,Vt(a.YuanChengKongZhi,(c,d)=>(L(),B("div",{key:d,class:"right-item"},[e("div",Me,[e("span",{class:R(["name",c.status])},D(c.name),3),e("span",{class:R(["status",c.status])},D(c.statusName),3)]),e("div",Ve,[e("span",Ge,"运行时间："+D(c.timetotal||"-"),1),e("div",Je,[b(i,{modelValue:c.status,"onUpdate:modelValue":_=>c.status=_,config:Ft},null,8,["modelValue","onUpdate:modelValue","config"])])])]))),128))])])):V("",!0),a.curTab==="shujubaobiao"?(L(),B("div",We,[b(n,{ref_key:"refForm_SJBB",ref:O,config:M(yt)},null,8,["config"]),b(s,{ref_key:"refTableSJBB",ref:E,class:"sjbb-card-table",config:M(vt)},null,8,["config"])])):V("",!0),a.curTab==="baojinshezhi"?(L(),B("div",Oe,[b(n,{config:Bt},null,8,["config"])])):V("",!0),a.curTab==="qushitu"?(L(),B("div",Qe,[b(n,{ref_key:"refForm_QST",ref:z,config:M(Tt)},null,8,["config"]),e("div",Ae,[b(r,{ref_key:"refQSTChart",ref:J,option:k.QuShiTuOption},null,8,["option"])])])):V("",!0)])]}),_:1},8,["title"]),b(n,{ref_key:"refForm",ref:W,config:P},null,8,["config"])],64)}}}),ma=jt(Pe,[["__scopeId","data-v-f6b3bae7"]]);export{ma as default};
