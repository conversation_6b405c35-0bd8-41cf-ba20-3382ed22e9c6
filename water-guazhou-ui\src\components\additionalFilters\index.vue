<template>
  <transition name="slide-fade">
    <el-form
      v-if="config.visible"
      label-width="80px"
      size="default"
      :model="state.queryParams"
      class="drawFilterBox"
      :class="{ dark: appStore.isDark }"
    >
      <!-- 额外过滤选项 -->
      <div
        class="moreFilterBox"
        :class="{ flexFilterBox: config.inline }"
        :style="config.containerStyle"
      >
        <div
          v-for="filter in config.filters"
          :key="filter.key"
          :style="{ 'margin-right': config.itemRight }"
          class="filterItem"
        >
          <component
            :is="filter.component"
            v-if="filter.component"
            :filter="filter"
            :config="filter"
            @checkVal="getComponentVal"
          ></component>
          <div v-if="filter.type === 'cascader'">
            <el-form-item :label="filter.label">
              <el-cascader
                v-model="state.model[filter.key]"
                :options="filter.options"
                clearable
                :show-all-levels="false"
                :props="filter.props"
                collapse-tags
                :style="{
                  width: filter.width || '240px'
                }"
                @change="(val) => cascaderChange(val, filter.key, filter.props)"
              ></el-cascader>
            </el-form-item>
          </div>
          <el-form-item v-if="filter.type === 'select'" :label="filter.label">
            <el-select
              v-model="state.queryParams[filter.key]"
              :clearable="!filter.unClearable"
              :placeholder="'请选择'"
              :multiple="filter.multiple"
              :style="{
                width: filter.width || '240px'
              }"
              @change="selectChange(filter)"
            >
              <el-option
                v-for="(item, index) in filter.options"
                :key="index"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="filter.type === 'userByRole'"
            :label="filter.label"
          >
            <ChooseUserByRole
              width="240px"
              height="48px"
              @checkUsers="(users) => checkUsers(users, filter.key)"
            />
          </el-form-item>
          <el-form-item v-if="filter.type === 'input'" :label="filter.label">
            <el-input
              v-model="state.queryParams[filter.key]"
              :style="{
                width: filter.width || '240px'
              }"
              :placeholder="'请输入'"
            >
              <template #suffix>
                <i
                  v-if="filter.unit"
                  class="inputUnit"
                  style="margin-right: 10px"
                  >{{ filter.unit }}</i
                >
              </template>
            </el-input>
          </el-form-item>
        </div>
      </div>

      <div class="footer">
        <el-button @click="config.handleFilter"> 取 消 </el-button>
        <el-button type="primary" @click="submit"> 保 存 </el-button>
      </div>
    </el-form>
  </transition>
</template>

<script lang="ts" setup>
import ChooseUserByRole from '../chooseUserByRole/index.vue';
import { useAppStore } from '@/store';
const appStore = useAppStore();
const props = defineProps<{
  config: any;
}>();
const emit = defineEmits(['submit']);
const state = reactive({
  queryParams: {},
  model: {}
});

// 单选框选中时，调用外部方法
const selectChange = (filter: any) => {
  if (filter.handleChange) filter.handleChange(state.queryParams[filter.key]);
};

// 确认按钮 提交参数  调用外部方法
const submit = () => {
  console.log(state.queryParams);
  emit('submit', state.queryParams);
  props.config.handleFilter();
};

// 根据用户选择角色，确认时提交选中参数
const checkUsers = (users: any[], key: string) => {
  state.queryParams[key] = users.map((user) => user.id).join(',');
  emit('submit', state.queryParams);
};

// 联级选择器
const cascaderChange = (val: any, key: string, props: any) => {
  if (props?.multiple) {
    state.queryParams[key] = val.join(',');
  } else {
    state.queryParams[key] = val[val.length - 1];
  }

  emit('submit', state.queryParams);
};

// 获取自定义过滤器取值
const getComponentVal = (
  val: string | boolean | string[] | Date[],
  key: string
) => {
  state.queryParams[key] = val;
};

// 监听附加参数，参数改变时，提交给cardsearch
watch(
  () => state.queryParams,
  () => emit('submit', state.queryParams),
  { deep: true }
);

defineExpose({
  ...toRefs(state),
  selectChange,
  submit,
  checkUsers,
  cascaderChange,
  getComponentVal
});
</script>

<style lang="scss" scoped>
.moreFilterBox {
  padding: 20px 20px 0px 20px;
  border-bottom: 1px solid #dbdbdb;
}
.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter, .slide-fade-leave-to
/* .slide-fade-leave-active for below version 2.1.8 */ {
  transform: translateX(10px);
  opacity: 0;
}
.flexFilterBox {
  display: flex;
  flex-wrap: wrap;
  .filterItem {
    margin: 0 16px 12px 0;
  }
}
.drawFilterBox {
  overflow: hidden;
  position: absolute;
  background-color: var(--el-bg-color);
  box-shadow: 0px 3px 5px rgb(56, 53, 53);
  z-index: 15;
  //  ;
  width: 100%;
  left: 0;
  top: calc(100% + 1px);
  &.dark {
    background-color: #222536;
  }
  .footer {
    text-align: right;
    padding: 20px;
  }
}
// .boxNone {
//   height: 0;
// }
</style>
