@mixin featureTemplates() {
  $icon_size: 48px;
  $border: 1px solid $border-color;

  .esri-feature-templates {
    background: var(--calcite-ui-background);
  }

  .esri-feature-templates__list-item-icon {
    flex: 0;
    width: $icon_size;
    min-width: $icon_size;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .esri-feature-templates__list-item-icon > div {
    // TemplateItem thumbnail uses plain div as wrapper
    display: flex;
  }

  .esri-feature-templates__loader {
    margin: 0 auto;
    height: $icon_size;
    width: 32px;
    background: url(../base/images/loading-throb.gif) no-repeat center;
  }

  .esri-ui {
    & .esri-feature-templates {
      width: $panel-width;
    }

    // If FeatureTemplates is used _inside_ another widget, then .esri-feature-templates should fit inside it
    & .esri-widget .esri-feature-templates {
      width: 100%;
    }
  }
}

@if $include_FeatureTemplates == true {
  @include featureTemplates();
}
