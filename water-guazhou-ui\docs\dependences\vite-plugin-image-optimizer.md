# vite-plugin-image-optimizer 图片压缩工具

## 安装

`yarn add vite-plugin-image-optimizer -D`

## 相关依赖

此库需要依赖下面的依赖，可选，如果没有自动安装的话

- [sharp](https://www.npmjs.com/package/sharp)
  
- [svgo](https://www.npmjs.com/package/svgo)

## 问题汇总

1. sharp无法安装或长时间卡在安装进度条

    需要配置下面两个镜像环境：
    1. `yarn config set sharp_binary_host "https://npmmirror.com/mirrors/sharp"`
    2. `yarn config set sharp_libvips_binary_host "https://npmmirror.com/mirrors/sharp-libvips"`