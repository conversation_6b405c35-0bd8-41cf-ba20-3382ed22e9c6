"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[3668,9880],{93668:(e,t,r)=>{r.r(t),r.d(t,{default:()=>G});var s=r(43697),i=r(46791),a=r(70921),l=r(60235),n=r(96674),o=r(22974),c=r(83379),h=r(68668),p=r(92604),y=r(70586),g=r(95330),d=r(17452),u=r(5600),m=(r(75215),r(52011)),b=r(30556),f=r(82971),v=r(65587),_=r(15235),L=r(99880),M=r(94443);const S={streets:{id:"streets",classic:!0,deprecated:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/streets.jpg")},baseMapLayers:[{id:"streets-base-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Street Map",showLegend:!1,visibility:!0,opacity:1}]},satellite:{id:"satellite",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/satellite.jpg")},baseMapLayers:[{id:"satellite-base-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Imagery",showLegend:!1,visibility:!0,opacity:1}]},hybrid:{id:"hybrid",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/hybrid.jpg")},baseMapLayers:[{id:"hybrid-base-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Imagery",showLegend:!1,visibility:!0,opacity:1},{id:"hybrid-reference-layer",styleUrl:"https://cdn.arcgis.com/sharing/rest/content/items/30d6b8271e1849cd9c3042060001f425/resources/styles/root.json",layerType:"VectorTileLayer",title:"Hybrid Reference Layer",isReference:!0,showLegend:!1,visibility:!0,opacity:1}]},terrain:{id:"terrain",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/terrain.jpg")},baseMapLayers:[{id:"terrain-base-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/World_Terrain_Base/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Terrain Base",showLegend:!1,visibility:!0,opacity:1},{id:"terrain-reference-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/Reference/World_Reference_Overlay/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Reference Overlay",isReference:!0,showLegend:!1,visibility:!0,opacity:1}]},topo:{id:"topo",classic:!0,deprecated:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/topo.jpg")},baseMapLayers:[{id:"topo-base-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/World_Topo_Map/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Topo Map",showLegend:!1,visibility:!0,opacity:1}]},gray:{id:"gray",classic:!0,deprecated:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/gray.jpg")},baseMapLayers:[{id:"gray-base-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Light_Gray_Base/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Light Gray Base",showLegend:!1,visibility:!0,opacity:1},{id:"gray-reference-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Light_Gray_Reference/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Light Gray Reference",isReference:!0,showLegend:!1,visibility:!0,opacity:1}]},"dark-gray":{id:"dark-gray",classic:!0,deprecated:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/dark-gray.jpg")},baseMapLayers:[{id:"dark-gray-base-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Dark_Gray_Base/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Dark Gray Base",showLegend:!1,visibility:!0,opacity:1},{id:"dark-gray-reference-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Dark_Gray_Reference/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Dark Gray Reference",isReference:!0,showLegend:!1,visibility:!0,opacity:1}]},oceans:{id:"oceans",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/oceans.jpg")},baseMapLayers:[{id:"oceans-base-layer",url:"//services.arcgisonline.com/arcgis/rest/services/Ocean/World_Ocean_Base/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Ocean Base",showLegend:!1,visibility:!0,opacity:1},{id:"oceans-reference-layer",url:"//services.arcgisonline.com/arcgis/rest/services/Ocean/World_Ocean_Reference/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Ocean Reference",isReference:!0,showLegend:!1,visibility:!0,opacity:1}]},"national-geographic":{id:"national-geographic",classic:!0,deprecated:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/national-geographic.jpg")},baseMapLayers:[{id:"national-geographic-base-layer",url:"//services.arcgisonline.com/ArcGIS/rest/services/NatGeo_World_Map/MapServer",title:"NatGeo World Map",showLegend:!1,layerType:"ArcGISTiledMapServiceLayer",visibility:!0,opacity:1}]},osm:{id:"osm",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/osm.jpg")},baseMapLayers:[{id:"osm-base-layer",layerType:"OpenStreetMap",title:"Open Street Map",showLegend:!1,visibility:!0,opacity:1}]},"dark-gray-vector":{id:"dark-gray-vector",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/dark-gray-vector.jpg")},baseMapLayers:[{id:"dark-gray-base-layer",styleUrl:"https://cdn.arcgis.com/sharing/rest/content/items/5e9b3685f4c24d8781073dd928ebda50/resources/styles/root.json",layerType:"VectorTileLayer",title:"Dark Gray Base",visibility:!0,opacity:1},{id:"dark-gray-reference-layer",styleUrl:"https://cdn.arcgis.com/sharing/rest/content/items/747cb7a5329c478cbe6981076cc879c5/resources/styles/root.json",layerType:"VectorTileLayer",title:"Dark Gray Reference",isReference:!0,visibility:!0,opacity:1}]},"gray-vector":{id:"gray-vector",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/gray-vector.jpg")},baseMapLayers:[{id:"gray-base-layer",styleUrl:"https://cdn.arcgis.com/sharing/rest/content/items/291da5eab3a0412593b66d384379f89f/resources/styles/root.json",layerType:"VectorTileLayer",title:"Light Gray Base",visibility:!0,opacity:1},{id:"gray-reference-layer",styleUrl:"https://cdn.arcgis.com/sharing/rest/content/items/1768e8369a214dfab4e2167d5c5f2454/resources/styles/root.json",layerType:"VectorTileLayer",title:"Light Gray Reference",isReference:!0,visibility:!0,opacity:1}]},"streets-vector":{id:"streets-vector",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/streets-vector.jpg")},baseMapLayers:[{id:"streets-vector-base-layer",styleUrl:"//cdn.arcgis.com/sharing/rest/content/items/de26a3cf4cc9451298ea173c4b324736/resources/styles/root.json",layerType:"VectorTileLayer",title:"World Streets",visibility:!0,opacity:1}]},"topo-vector":{id:"topo-vector",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/topo-vector.jpg")},baseMapLayers:[{id:"world-hillshade-layer",url:"//services.arcgisonline.com/arcgis/rest/services/Elevation/World_Hillshade/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Hillshade",showLegend:!1,visibility:!0,opacity:1},{id:"topo-vector-base-layer",styleUrl:"//cdn.arcgis.com/sharing/rest/content/items/7dc6cea0b1764a1f9af2e679f642f0f5/resources/styles/root.json",layerType:"VectorTileLayer",title:"World Topo",visibility:!0,opacity:1}]},"streets-night-vector":{id:"streets-night-vector",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/streets-night.jpg")},baseMapLayers:[{id:"streets-night-vector-base-layer",styleUrl:"//cdn.arcgis.com/sharing/rest/content/items/86f556a2d1fd468181855a35e344567f/resources/styles/root.json",layerType:"VectorTileLayer",title:"World Streets Night",visibility:!0,opacity:1}]},"streets-relief-vector":{id:"streets-relief-vector",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/streets-relief.jpg")},baseMapLayers:[{id:"world-hillshade-layer",url:"//services.arcgisonline.com/arcgis/rest/services/Elevation/World_Hillshade/MapServer",layerType:"ArcGISTiledMapServiceLayer",title:"World Hillshade",showLegend:!1,visibility:!0,opacity:1},{id:"streets-relief-vector-base-layer",styleUrl:"//www.arcgis.com/sharing/rest/content/items/b266e6d17fc345b498345613930fbd76/resources/styles/root.json",title:"World Streets Relief",layerType:"VectorTileLayer",visibility:!0,opacity:1}]},"streets-navigation-vector":{id:"streets-navigation-vector",classic:!0,get thumbnailUrl(){return(0,L.V)("esri/images/basemap/streets-navigation.jpg")},baseMapLayers:[{id:"streets-navigation-vector-base-layer",styleUrl:"//cdn.arcgis.com/sharing/rest/content/items/63c47b7177f946b49902c24129b87252/resources/styles/root.json",layerType:"VectorTileLayer",title:"World Streets Navigation",visibility:!0,opacity:1}]},"arcgis-imagery":{get thumbnailUrl(){return(0,L.V)("esri/images/basemap/hybrid.jpg")},title:"Imagery Hybrid",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Imagery",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/World_Imagery/MapServer"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Imagery:Labels",title:"Hybrid Reference Layer",isReference:!0}]},"arcgis-imagery-standard":{get thumbnailUrl(){return(0,L.V)("esri/images/basemap/satellite.jpg")},title:"Imagery",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Imagery",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/World_Imagery/MapServer"}]},"arcgis-imagery-labels":{title:"Hybrid [Reference]",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Imagery:Labels",title:"Hybrid Reference Layer",isReference:!0}]},"arcgis-light-gray":{get thumbnailUrl(){return(0,L.V)("esri/images/basemap/gray-vector.jpg")},title:"Light Gray Canvas",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:LightGray:Base",title:"Light Gray Canvas Base"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:LightGray:Labels",title:"Light Gray Canvas Labels",isReference:!0}]},"arcgis-dark-gray":{get thumbnailUrl(){return(0,L.V)("esri/images/basemap/dark-gray.jpg")},title:"Dark Gray Canvas",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:DarkGray:Base",title:"Dark Gray Canvas Base"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:DarkGray:Labels",title:"Dark Gray Canvas Labels",isReference:!0}]},"arcgis-navigation":{get thumbnailUrl(){return(0,L.V)("esri/images/basemap/streets-navigation.jpg")},title:"Navigation",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Navigation",title:"World Navigation Map"}]},"arcgis-navigation-night":{title:"Navigation (Dark Mode)",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:NavigationNight",title:"World Navigation Map (Dark Mode)"}]},"arcgis-streets":{get thumbnailUrl(){return(0,L.V)("esri/images/basemap/streets-vector.jpg")},title:"Streets",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Streets",title:"World Street Map"}]},"arcgis-streets-night":{get thumbnailUrl(){return(0,L.V)("esri/images/basemap/streets-night.jpg")},title:"Streets (Night)",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:StreetsNight",title:"World Street Map (Night)"}]},"arcgis-streets-relief":{get thumbnailUrl(){return(0,L.V)("esri/images/basemap/streets-relief.jpg")},title:"Streets (with Relief)",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Hillshade",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/Elevation/World_Hillshade/MapServer"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:StreetsRelief:Base",title:"World Street Map (with Relief)"}]},"arcgis-topographic":{get thumbnailUrl(){return(0,L.V)("esri/images/basemap/topo.jpg")},title:"Topographic",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Hillshade",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/Elevation/World_Hillshade/MapServer"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Topographic:Base",title:"World Topographic Map"}]},"arcgis-oceans":{get thumbnailUrl(){return(0,L.V)("esri/images/basemap/oceans.jpg")},title:"Oceans",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Ocean Base",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/Ocean/World_Ocean_Base/MapServer"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Oceans:Labels",title:"World Ocean Reference",isReference:!0}]},"osm-standard":{title:"OpenStreetMap",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/OSM:Standard",title:"OpenStreetMap"}]},"osm-standard-relief":{title:"OpenStreetMap (with relief)",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Hillshade",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/Elevation/World_Hillshade/MapServer"},{styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/OSM:StandardRelief:Base",layerType:"VectorTileLayer",title:"OpenStreetMap Relief Base"}]},"osm-streets":{title:"OpenStreetMap (Streets)",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/OSM:Streets",title:"OpenStreetMap (Streets)"}]},"osm-streets-relief":{title:"OpenStreetMap (Streets with relief)",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Hillshade",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/Elevation/World_Hillshade/MapServer"},{styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/OSM:StreetsRelief:Base",layerType:"VectorTileLayer",title:"OpenStreetMap Relief Base"}]},"osm-light-gray":{title:"OpenStreetMap (Light Gray Canvas)",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/OSM:LightGray:Base",title:"OSM (Light Gray Base)"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/OSM:LightGray:Labels",title:"OSM (Light Gray Reference)",isReference:!0}]},"osm-dark-gray":{title:"OpenStreetMap (Dark Gray Canvas)",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/OSM:DarkGray:Base",title:"OSM (Dark Gray Base)"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/OSM:DarkGray:Labels",title:"OSM (Dark Gray Reference)",isReference:!0}]},"arcgis-terrain":{title:"Terrain with Labels",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Hillshade",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/Elevation/World_Hillshade/MapServer"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Terrain:Base",title:"World Terrain Base"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Terrain:Detail",title:"World Terrain Reference",isReference:!0}]},"arcgis-community":{title:"Community",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Community",title:"Community"}]},"arcgis-charted-territory":{title:"Charted Territory",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Hillshade",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/Elevation/World_Hillshade/MapServer"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:ChartedTerritory:Base",title:"Charted Territory"}]},"arcgis-colored-pencil":{title:"Colored Pencil",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:ColoredPencil",title:"Colored Pencil"}]},"arcgis-nova":{title:"Nova",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Nova",title:"Nova"}]},"arcgis-modern-antique":{title:"Modern Antique",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Hillshade",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/Elevation/World_Hillshade/MapServer"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:ModernAntique:Base",title:"Modern Antique"}]},"arcgis-midcentury":{title:"Mid-Century",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Midcentury",title:"Mid-Century"}]},"arcgis-newspaper":{title:"Newspaper",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:Newspaper",title:"Newspaper"}]},"arcgis-hillshade-light":{title:"Hillshade",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Hillshade",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/Elevation/World_Hillshade/MapServer"}]},"arcgis-hillshade-dark":{title:"Hillshade (Dark)",baseMapLayers:[{layerType:"ArcGISTiledMapServiceLayer",showLegend:!1,title:"World Hillshade (Dark)",url:"https://ibasemaps-api.arcgis.com/arcgis/rest/services/Elevation/World_Hillshade_Dark/MapServer"}]},"arcgis-human-geography":{title:"Human Geography",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:HumanGeography:Base",title:"Human Geography Base"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:HumanGeography:Detail",title:"Human Geography Detail",isReference:!0},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:HumanGeography:Label",title:"Human Geography Label",isReference:!0}]},"arcgis-human-geography-dark":{title:"Human Geography (Dark)",baseMapLayers:[{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:HumanGeographyDark:Base",title:"Human Geography Dark Base"},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:HumanGeographyDark:Detail",title:"Human Geography Dark Detail",isReference:!0},{layerType:"VectorTileLayer",styleUrl:"https://basemaps-api.arcgis.com/arcgis/rest/services/styles/ArcGIS:HumanGeographyDark:Label",title:"Human Geography Dark Label",isReference:!0}]}};var T,w=r(15650);let A=0;const I="esri.Basemap";let C=T=class extends((0,n.eC)(c.Z)){constructor(e){super(e),this.id=null,this.portalItem=null,this.spatialReference=null,this.thumbnailUrl=null,this.title="Basemap",this.id=Date.now().toString(16)+"-basemap-"+A++,this.baseLayers=new i.Z,this.referenceLayers=new i.Z;const t=e=>{e.parent&&e.parent!==this&&"remove"in e.parent&&e.parent.remove(e),e.parent=this,"elevation"===e.type&&p.Z.getLogger(this.declaredClass).error(`Layer '${e.title}, id:${e.id}' of type '${e.type}' is not supported as a basemap layer and will therefore be ignored.`)},r=e=>{e.parent=null};this.baseLayers.on("after-add",(e=>t(e.item))),this.referenceLayers.on("after-add",(e=>t(e.item))),this.baseLayers.on("after-remove",(e=>r(e.item))),this.referenceLayers.on("after-remove",(e=>r(e.item)))}initialize(){this.when().catch((e=>{p.Z.getLogger(this.declaredClass).error("#load()",`Failed to load basemap (title: '${this.title}', id: '${this.id}')`,e)})),this.resourceInfo&&this.read(this.resourceInfo.data,this.resourceInfo.context)}destroy(){const e=this.baseLayers.removeAll();for(const t of e)t.destroy();const t=this.referenceLayers.removeAll();for(const e of t)e.destroy();this.baseLayers.destroy(),this.referenceLayers.destroy(),this.portalItem?.destroy(),this.portalItem=null}normalizeCtorArgs(e){return e&&"resourceInfo"in e&&(this._set("resourceInfo",e.resourceInfo),delete(e={...e}).resourceInfo),e}set baseLayers(e){this._set("baseLayers",(0,a.Z)(e,this._get("baseLayers")))}_writeBaseLayers(e,t,r){const s=[];e?(r={...r,layerContainerType:"basemap"},this.baseLayers.forEach((e=>{const t=(0,w.Nw)(e,r.webmap?r.webmap.getLayerJSONFromResourceInfo(e):null,r);(0,y.pC)(t)&&s.push(t)})),this.referenceLayers.forEach((e=>{const t=(0,w.Nw)(e,r.webmap?r.webmap.getLayerJSONFromResourceInfo(e):null,r);(0,y.pC)(t)&&("scene"!==e.type&&(t.isReference=!0),s.push(t))})),t.baseMapLayers=s):t.baseMapLayers=s}set referenceLayers(e){this._set("referenceLayers",(0,a.Z)(e,this._get("referenceLayers")))}writeTitle(e,t){t.title=e||"Basemap"}load(e){return this.addResolvingPromise(this._loadFromSource(e)),Promise.resolve(this)}loadAll(){return(0,h.G)(this,(e=>{e(this.baseLayers,this.referenceLayers)}))}clone(){const e={id:this.id,title:this.title,portalItem:this.portalItem,baseLayers:this.baseLayers.slice(),referenceLayers:this.referenceLayers.slice()};return this.loaded&&(e.loadStatus="loaded"),new T({resourceInfo:this.resourceInfo}).set(e)}read(e,t){this.resourceInfo||this._set("resourceInfo",{data:e,context:t}),super.read(e,t)}write(e,t){return e=e||{},t&&t.origin||(t={origin:"web-map",...t}),super.write(e,t),!this.loaded&&this.resourceInfo&&this.resourceInfo.data.baseMapLayers&&(e.baseMapLayers=this.resourceInfo.data.baseMapLayers.map((e=>{const t=(0,o.d9)(e);return t.url&&(0,d.oC)(t.url)&&(t.url=`https:${t.url}`),t.templateUrl&&(0,d.oC)(t.templateUrl)&&(t.templateUrl=`https:${t.templateUrl}`),t}))),e}async _loadFromSource(e){const{resourceInfo:t,portalItem:r}=this;(0,g.k_)(e);const s=[];if(t){const r=t.context?t.context.url:null;if(s.push(this._loadLayersFromJSON(t.data,r,e)),t.data.id&&!t.data.title){const e=t.data.id;s.push(async function(e){if(!e)return;const t=e.includes("-vector")?e.slice(0,e.indexOf("-vector")):e,r=await(0,M.ME)("esri/t9n/basemaps");return r[e]||r[t]}(e).then((e=>{e&&this.read({title:e},t.context)})))}}else r&&s.push(this._loadFromItem(r,e));await Promise.all(s)}async _loadLayersFromJSON(e,t,s){const i=this.resourceInfo&&this.resourceInfo.context,a=this.portalItem&&this.portalItem.portal||i&&i.portal||null,l=i&&"web-scene"===i.origin?"web-scene":"web-map",{populateOperationalLayers:n}=await Promise.all([r.e(4547),r.e(24)]).then(r.bind(r,70024)),o=[];if((0,g.k_)(s),e.baseMapLayers&&Array.isArray(e.baseMapLayers)){const r={context:{origin:l,url:t,portal:a,layerContainerType:"basemap"},defaultLayerType:"DefaultTileLayer"},s=e=>"web-scene"===l&&"ArcGISSceneServiceLayer"===e.layerType||e.isReference,i=n(this.baseLayers,e.baseMapLayers.filter((e=>!s(e))),r);o.push(i);const c=n(this.referenceLayers,e.baseMapLayers.filter(s),r);o.push(c)}await(0,g.as)(o)}async _loadFromItem(e,t){const r=await e.load(t),s=await r.fetchData("json",t),i=(0,d.mN)(e.itemUrl??"");return this._set("resourceInfo",{data:s.baseMap??{},context:{origin:"Web Scene"===e.type?"web-scene":"web-map",portal:e.portal||v.Z.getDefault(),url:i}}),this.read(this.resourceInfo.data,this.resourceInfo.context),this.read({spatialReference:s.spatialReference},this.resourceInfo.context),this.read({title:e.title,thumbnailUrl:e.thumbnailUrl},{origin:"portal-item",portal:e.portal||v.Z.getDefault(),url:i}),this._loadLayersFromJSON(this.resourceInfo.data,i,t)}static fromId(e){const t=S[e];if(t){if(t.deprecated){let t=null;"dark-gray"===e?t="dark-gray-vector":"gray"===e?t="gray-vector":"streets"===e?t="streets-vector":"topo"===e&&(t="topo-vector"),(0,l.x9)(p.Z.getLogger(I),`The ${e} basemap has entered mature support and is no longer being updated.`,{replacement:t,see:"https://arcg.is/1iq8aD",warnOnce:!0})}return T.fromJSON(t)}return null}};(0,s._)([(0,u.Cb)({json:{write:{ignoreOrigin:!0,target:"baseMapLayers",writer(e,t,r,s){this._writeBaseLayers(e,t,s)}},origins:{"web-scene":{write:{ignoreOrigin:!0,target:{baseMapLayers:{type:i.Z}},writer(e,t,r,s){this._writeBaseLayers(e,t,s)}}}}}})],C.prototype,"baseLayers",null),(0,s._)([(0,u.Cb)({type:String,json:{origins:{"web-scene":{write:!0}}}})],C.prototype,"id",void 0),(0,s._)([(0,u.Cb)({type:_.default})],C.prototype,"portalItem",void 0),(0,s._)([(0,u.Cb)()],C.prototype,"referenceLayers",null),(0,s._)([(0,u.Cb)({readOnly:!0})],C.prototype,"resourceInfo",void 0),(0,s._)([(0,u.Cb)({type:f.Z})],C.prototype,"spatialReference",void 0),(0,s._)([(0,u.Cb)()],C.prototype,"thumbnailUrl",void 0),(0,s._)([(0,u.Cb)({type:String,json:{origins:{"web-scene":{write:{isRequired:!0}}}}})],C.prototype,"title",void 0),(0,s._)([(0,b.c)("title")],C.prototype,"writeTitle",null),C=T=(0,s._)([(0,m.j)(I)],C);const G=C},99880:(e,t,r)=>{r.d(t,{V:()=>o});var s=r(68773),i=(r(3172),r(20102)),a=r(92604),l=r(17452);const n=a.Z.getLogger("esri.assets");function o(e){if(!s.Z.assetsPath)throw n.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new i.Z("assets:path-not-set","config.assetsPath is not set");return(0,l.v_)(s.Z.assetsPath,e)}},46791:(e,t,r)=>{r.d(t,{Z:()=>E});var s,i=r(43697),a=r(3894),l=r(32448),n=r(22974),o=r(70586),c=r(71143);!function(e){e[e.ADD=1]="ADD",e[e.REMOVE=2]="REMOVE",e[e.MOVE=4]="MOVE"}(s||(s={}));var h,p=r(1654),y=r(5600),g=r(75215),d=r(52421),u=r(52011),m=r(58971),b=r(10661);const f=new c.Z(class{constructor(){this.target=null,this.cancellable=!1,this.defaultPrevented=!1,this.item=void 0,this.type=void 0}preventDefault(){this.cancellable&&(this.defaultPrevented=!0)}reset(e){this.defaultPrevented=!1,this.item=e}},void 0,(e=>{e.item=null,e.target=null,e.defaultPrevented=!1,e.cancellable=!1})),v=()=>{};function _(e){return e?e instanceof G?e.toArray():e.length?Array.prototype.slice.apply(e):[]:[]}function L(e){if(e&&e.length)return e[0]}function M(e,t,r,s){const i=Math.min(e.length-r,t.length-s);let a=0;for(;a<i&&e[r+a]===t[s+a];)a++;return a}function S(e,t,r,s){t&&t.forEach(((t,i,a)=>{e.push(t),S(e,r.call(s,t,i,a),r,s)}))}const T=new Set,w=new Set,A=new Set,I=new Map;let C=0,G=h=class extends l.Z.EventedAccessor{static isCollection(e){return null!=e&&e instanceof h}constructor(e){super(e),this._chgListeners=[],this._notifications=null,this._timer=null,this._observable=new b.s,this.length=0,this._items=[],Object.defineProperty(this,"uid",{value:C++})}normalizeCtorArgs(e){return e?Array.isArray(e)||e instanceof h?{items:e}:e:{}}destroy(){this.removeAll()}*[Symbol.iterator](){yield*this.items}get items(){return(0,m.it)(this._observable),this._items}set items(e){this._emitBeforeChanges(s.ADD)||(this._splice(0,this.length,_(e)),this._emitAfterChanges(s.ADD))}hasEventListener(e){return"change"===e?this._chgListeners.length>0:this._emitter.hasEventListener(e)}on(e,t){if("change"===e){const e=this._chgListeners,r={removed:!1,callback:t};return e.push(r),this._notifications&&this._notifications.push({listeners:e.slice(),items:this._items.slice(),changes:[]}),{remove(){this.remove=v,r.removed=!0,e.splice(e.indexOf(r),1)}}}return this._emitter.on(e,t)}once(e,t){const r=this.on(e,t);return{remove(){r.remove()}}}add(e,t){if((0,m.it)(this._observable),this._emitBeforeChanges(s.ADD))return this;const r=this.getNextIndex(t??null);return this._splice(r,0,[e]),this._emitAfterChanges(s.ADD),this}addMany(e,t=this._items.length){if((0,m.it)(this._observable),!e||!e.length)return this;if(this._emitBeforeChanges(s.ADD))return this;const r=this.getNextIndex(t);return this._splice(r,0,_(e)),this._emitAfterChanges(s.ADD),this}at(e){if((0,m.it)(this._observable),(e=Math.trunc(e)||0)<0&&(e+=this.length),!(e<0||e>=this.length))return this._items[e]}removeAll(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return[];const e=this._splice(0,this.length)||[];return this._emitAfterChanges(s.REMOVE),e}clone(){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.map(n.d9)})}concat(...e){(0,m.it)(this._observable);const t=e.map(_);return this._createNewInstance({items:this._items.concat(...t)})}drain(e,t){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const r=(0,o.j0)(this._splice(0,this.length)),i=r.length;for(let s=0;s<i;s++)e.call(t,r[s],s,r);this._emitAfterChanges(s.REMOVE)}every(e,t){return(0,m.it)(this._observable),this._items.every(e,t)}filter(e,t){let r;return(0,m.it)(this._observable),r=2===arguments.length?this._items.filter(e,t):this._items.filter(e),this._createNewInstance({items:r})}find(e,t){return(0,m.it)(this._observable),this._items.find(e,t)}findIndex(e,t){return(0,m.it)(this._observable),this._items.findIndex(e,t)}flatten(e,t){(0,m.it)(this._observable);const r=[];return S(r,this,e,t),new h(r)}forEach(e,t){return(0,m.it)(this._observable),this._items.forEach(e,t)}getItemAt(e){return(0,m.it)(this._observable),this._items[e]}getNextIndex(e){(0,m.it)(this._observable);const t=this.length;return(e=e??t)<0?e=0:e>t&&(e=t),e}includes(e,t=0){return(0,m.it)(this._observable),this._items.includes(e,t)}indexOf(e,t=0){return(0,m.it)(this._observable),this._items.indexOf(e,t)}join(e=","){return(0,m.it)(this._observable),this._items.join(e)}lastIndexOf(e,t=this.length-1){return(0,m.it)(this._observable),this._items.lastIndexOf(e,t)}map(e,t){(0,m.it)(this._observable);const r=this._items.map(e,t);return new h({items:r})}reorder(e,t=this.length-1){(0,m.it)(this._observable);const r=this.indexOf(e);if(-1!==r){if(t<0?t=0:t>=this.length&&(t=this.length-1),r!==t){if(this._emitBeforeChanges(s.MOVE))return e;this._splice(r,1),this._splice(t,0,[e]),this._emitAfterChanges(s.MOVE)}return e}}pop(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const e=L(this._splice(this.length-1,1));return this._emitAfterChanges(s.REMOVE),e}push(...e){return(0,m.it)(this._observable),this._emitBeforeChanges(s.ADD)||(this._splice(this.length,0,e),this._emitAfterChanges(s.ADD)),this.length}reduce(e,t){(0,m.it)(this._observable);const r=this._items;return 2===arguments.length?r.reduce(e,t):r.reduce(e)}reduceRight(e,t){(0,m.it)(this._observable);const r=this._items;return 2===arguments.length?r.reduceRight(e,t):r.reduceRight(e)}remove(e){return(0,m.it)(this._observable),this.removeAt(this.indexOf(e))}removeAt(e){if((0,m.it)(this._observable),e<0||e>=this.length||this._emitBeforeChanges(s.REMOVE))return;const t=L(this._splice(e,1));return this._emitAfterChanges(s.REMOVE),t}removeMany(e){if((0,m.it)(this._observable),!e||!e.length||this._emitBeforeChanges(s.REMOVE))return[];const t=e instanceof h?e.toArray():e,r=this._items,i=[],a=t.length;for(let e=0;e<a;e++){const s=t[e],a=r.indexOf(s);if(a>-1){const s=1+M(t,r,e+1,a+1),l=this._splice(a,s);l&&l.length>0&&i.push.apply(i,l),e+=s-1}}return this._emitAfterChanges(s.REMOVE),i}reverse(){if((0,m.it)(this._observable),this._emitBeforeChanges(s.MOVE))return this;const e=this._splice(0,this.length);return e&&(e.reverse(),this._splice(0,0,e)),this._emitAfterChanges(s.MOVE),this}shift(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const e=L(this._splice(0,1));return this._emitAfterChanges(s.REMOVE),e}slice(e=0,t=this.length){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.slice(e,t)})}some(e,t){return(0,m.it)(this._observable),this._items.some(e,t)}sort(e){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.MOVE))return this;const t=(0,o.j0)(this._splice(0,this.length));return arguments.length?t.sort(e):t.sort(),this._splice(0,0,t),this._emitAfterChanges(s.MOVE),this}splice(e,t,...r){(0,m.it)(this._observable);const i=(t?s.REMOVE:0)|(r.length?s.ADD:0);if(this._emitBeforeChanges(i))return[];const a=this._splice(e,t,r)||[];return this._emitAfterChanges(i),a}toArray(){return(0,m.it)(this._observable),this._items.slice()}toJSON(){return(0,m.it)(this._observable),this.toArray()}toLocaleString(){return(0,m.it)(this._observable),this._items.toLocaleString()}toString(){return(0,m.it)(this._observable),this._items.toString()}unshift(...e){return(0,m.it)(this._observable),!e.length||this._emitBeforeChanges(s.ADD)||(this._splice(0,0,e),this._emitAfterChanges(s.ADD)),this.length}_createNewInstance(e){return new this.constructor(e)}_splice(e,t,r){const s=this._items,i=this.itemType;let a,l;if(!this._notifications&&this.hasEventListener("change")&&(this._notifications=[{listeners:this._chgListeners.slice(),items:this._items.slice(),changes:[]}],this._timer&&this._timer.remove(),this._timer=(0,p.Os)((()=>this._dispatchChange()))),t){if(l=s.splice(e,t),this.hasEventListener("before-remove")){const t=f.acquire();t.target=this,t.cancellable=!0;for(let r=0,i=l.length;r<i;r++)a=l[r],t.reset(a),this.emit("before-remove",t),t.defaultPrevented&&(l.splice(r,1),s.splice(e,0,a),e+=1,r-=1,i-=1);f.release(t)}if(this.length=this._items.length,this.hasEventListener("after-remove")){const e=f.acquire();e.target=this,e.cancellable=!1;const t=l.length;for(let r=0;r<t;r++)e.reset(l[r]),this.emit("after-remove",e);f.release(e)}}if(r&&r.length){if(i){const e=[];for(const t of r){const r=i.ensureType(t);null==r&&null!=t||e.push(r)}r=e}const t=this.hasEventListener("before-add"),a=this.hasEventListener("after-add"),l=e===this.length;if(t||a){const i=f.acquire();i.target=this,i.cancellable=!0;const n=f.acquire();n.target=this,n.cancellable=!1;for(const o of r)t?(i.reset(o),this.emit("before-add",i),i.defaultPrevented||(l?s.push(o):s.splice(e++,0,o),this._set("length",s.length),a&&(n.reset(o),this.emit("after-add",n)))):(l?s.push(o):s.splice(e++,0,o),this._set("length",s.length),n.reset(o),this.emit("after-add",n));f.release(n),f.release(i)}else{if(l)for(const e of r)s.push(e);else s.splice(e,0,...r);this._set("length",s.length)}}return(r&&r.length||l&&l.length)&&this._notifyChangeEvent(r,l),l}_emitBeforeChanges(e){let t=!1;if(this.hasEventListener("before-changes")){const r=f.acquire();r.target=this,r.cancellable=!0,r.type=e,this.emit("before-changes",r),t=r.defaultPrevented,f.release(r)}return t}_emitAfterChanges(e){if(this.hasEventListener("after-changes")){const t=f.acquire();t.target=this,t.cancellable=!1,t.type=e,this.emit("after-changes",t),f.release(t)}this._observable.notify()}_notifyChangeEvent(e,t){this.hasEventListener("change")&&this._notifications&&this._notifications[this._notifications.length-1].changes.push({added:e,removed:t})}_dispatchChange(){if(this._timer&&(this._timer.remove(),this._timer=null),!this._notifications)return;const e=this._notifications;this._notifications=null;for(const t of e){const e=t.changes;T.clear(),w.clear(),A.clear();for(const{added:t,removed:r}of e){if(t)if(0===A.size&&0===w.size)for(const e of t)T.add(e);else for(const e of t)w.has(e)?(A.add(e),w.delete(e)):A.has(e)||T.add(e);if(r)if(0===A.size&&0===T.size)for(const e of r)w.add(e);else for(const e of r)T.has(e)?T.delete(e):(A.delete(e),w.add(e))}const r=a.Z.acquire();T.forEach((e=>{r.push(e)}));const s=a.Z.acquire();w.forEach((e=>{s.push(e)}));const i=this._items,l=t.items,n=a.Z.acquire();if(A.forEach((e=>{l.indexOf(e)!==i.indexOf(e)&&n.push(e)})),t.listeners&&(r.length||s.length||n.length)){const e={target:this,added:r,removed:s,moved:n},i=t.listeners.length;for(let r=0;r<i;r++){const s=t.listeners[r];s.removed||s.callback.call(this,e)}}a.Z.release(r),a.Z.release(s),a.Z.release(n)}T.clear(),w.clear(),A.clear()}};G.ofType=e=>{if(!e)return h;if(I.has(e))return I.get(e);let t=null;if("function"==typeof e)t=e.prototype.declaredClass;else if(e.base)t=e.base.prototype.declaredClass;else for(const r in e.typeMap){const s=e.typeMap[r].prototype.declaredClass;t?t+=` | ${s}`:t=s}let r=class extends h{};return(0,i._)([(0,d.c)({Type:e,ensureType:"function"==typeof e?(0,g.se)(e):(0,g.N7)(e)})],r.prototype,"itemType",void 0),r=(0,i._)([(0,u.j)(`esri.core.Collection<${t}>`)],r),I.set(e,r),r},(0,i._)([(0,y.Cb)()],G.prototype,"length",void 0),(0,i._)([(0,y.Cb)()],G.prototype,"items",null),G=h=(0,i._)([(0,u.j)("esri.core.Collection")],G);const E=G},32448:(e,t,r)=>{r.d(t,{Z:()=>o});var s=r(43697),i=r(15923),a=r(50758),l=r(52011);class n{constructor(){this._emitter=new n.EventEmitter(this)}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}}!function(e){class t{constructor(e=null){this._target=e,this._listenersMap=null}clear(){this._listenersMap&&this._listenersMap.clear(),this._listenersMap=null}emit(e,t){const r=this._listenersMap&&this._listenersMap.get(e);if(!r)return!1;const s=this._target||this;return[...r].forEach((e=>{e.call(s,t)})),r.length>0}on(e,t){if(Array.isArray(e)){const r=e.map((e=>this.on(e,t)));return(0,a.AL)(r)}if(e.includes(","))throw new TypeError("Evented.on() with a comma delimited string of event types is not supported");this._listenersMap||(this._listenersMap=new Map);const r=this._listenersMap.get(e)||[];return r.push(t),this._listenersMap.set(e,r),{remove:()=>{const r=this._listenersMap&&this._listenersMap.get(e)||[],s=r.indexOf(t);s>=0&&r.splice(s,1)}}}once(e,t){const r=this.on(e,(e=>{r.remove(),t.call(null,e)}));return r}hasEventListener(e){const t=this._listenersMap&&this._listenersMap.get(e);return null!=t&&t.length>0}}e.EventEmitter=t,e.EventedMixin=e=>{let r=class extends e{constructor(){super(...arguments),this._emitter=new t}destroy(){this._emitter.clear()}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}};return r=(0,s._)([(0,l.j)("esri.core.Evented")],r),r};let r=class extends i.Z{constructor(){super(...arguments),this._emitter=new n.EventEmitter(this)}destroy(){this._emitter.clear()}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}};r=(0,s._)([(0,l.j)("esri.core.Evented")],r),e.EventedAccessor=r}(n||(n={}));const o=n},52421:(e,t,r)=>{function s(e){return(t,r)=>{t[r]=e}}r.d(t,{c:()=>s})},10661:(e,t,r)=>{r.d(t,{s:()=>i});var s=r(42100);class i extends s.s{notify(){const e=this._observers;if(e&&e.length>0){const t=e.slice();for(const e of t)e.onInvalidated(),e.onCommitted()}}}},66643:(e,t,r)=>{r.d(t,{Ed:()=>c,UI:()=>h,mt:()=>d,q6:()=>g,vr:()=>u});var s=r(43697),i=r(15923),a=r(70586),l=r(95330),n=r(5600),o=(r(75215),r(67676),r(52011));function c(e,t,r){return(0,l.as)(e.map(((e,s)=>t.apply(r,[e,s]))))}async function h(e,t,r){return(await(0,l.as)(e.map(((e,s)=>t.apply(r,[e,s]))))).map((e=>e.value))}function p(e){return{ok:!0,value:e}}function y(e){return{ok:!1,error:e}}async function g(e){if((0,a.Wi)(e))return{ok:!1,error:new Error("no promise provided")};try{return p(await e)}catch(e){return y(e)}}async function d(e){try{return p(await e)}catch(e){return(0,l.r9)(e),y(e)}}function u(e,t){return new m(e,t)}let m=class extends i.Z{get value(){return e=this._result,(0,a.pC)(e)&&!0===e.ok?e.value:null;var e}get error(){return e=this._result,(0,a.pC)(e)&&!1===e.ok?e.error:null;var e}get finished(){return(0,a.pC)(this._result)}constructor(e,t){super({}),this._result=null,this._abortHandle=null,this.abort=()=>{this._abortController=(0,a.IM)(this._abortController)},this.remove=this.abort,this._abortController=new AbortController;const{signal:r}=this._abortController;this.promise=e(r),this.promise.then((e=>{this._result=p(e),this._cleanup()}),(e=>{this._result=y(e),this._cleanup()})),this._abortHandle=(0,l.fu)(t,this.abort)}normalizeCtorArgs(){return{}}destroy(){this.abort()}_cleanup(){this._abortHandle=(0,a.hw)(this._abortHandle),this._abortController=null}};(0,s._)([(0,n.Cb)()],m.prototype,"value",null),(0,s._)([(0,n.Cb)()],m.prototype,"error",null),(0,s._)([(0,n.Cb)()],m.prototype,"finished",null),(0,s._)([(0,n.Cb)()],m.prototype,"promise",void 0),(0,s._)([(0,n.Cb)()],m.prototype,"_result",void 0),m=(0,s._)([(0,o.j)("esri.core.asyncUtils.ReactiveTask")],m)},70921:(e,t,r)=>{r.d(t,{R:()=>a,Z:()=>i});var s=r(46791);function i(e,t,r=s.Z){return t||(t=new r),t===e||(t.removeAll(),(i=e)&&(Array.isArray(i)||"items"in i&&Array.isArray(i.items))?t.addMany(e):e&&t.add(e)),t;var i}function a(e){return e}},68668:(e,t,r)=>{r.d(t,{G:()=>n,w:()=>o});var s=r(66643),i=r(46791),a=r(83379),l=r(70586);async function n(e,t){return await e.load(),o(e,t)}async function o(e,t){const r=[],n=(...e)=>{for(const t of e)(0,l.Wi)(t)||(Array.isArray(t)?n(...t):i.Z.isCollection(t)?t.forEach((e=>n(e))):a.Z.isLoadable(t)&&r.push(t))};t(n);let o=null;if(await(0,s.UI)(r,(async e=>{const t=await(0,s.q6)(function(e){return"loadAll"in e&&"function"==typeof e.loadAll}(e)?e.loadAll():e.load());!1!==t.ok||o||(o=t)})),o)throw o.error;return e}},94443:(e,t,r)=>{r.d(t,{ME:()=>g,Su:()=>d,tz:()=>y});var s=r(20102),i=r(95330),a=r(70171);const l=/^([a-z]{2})(?:[-_]([A-Za-z]{2}))?$/,n={ar:!0,bg:!0,bs:!0,ca:!0,cs:!0,da:!0,de:!0,el:!0,en:!0,es:!0,et:!0,fi:!0,fr:!0,he:!0,hr:!0,hu:!0,id:!0,it:!0,ja:!0,ko:!0,lt:!0,lv:!0,nb:!0,nl:!0,pl:!0,"pt-BR":!0,"pt-PT":!0,ro:!0,ru:!0,sk:!0,sl:!0,sr:!0,sv:!0,th:!0,tr:!0,uk:!0,vi:!0,"zh-CN":!0,"zh-HK":!0,"zh-TW":!0};function o(e){return n[e]??!1}const c=[],h=new Map;function p(e){for(const t of h.keys())u(e.pattern,t)&&h.delete(t)}function y(e){return c.includes(e)||(p(e),c.unshift(e)),{remove(){const t=c.indexOf(e);t>-1&&(c.splice(t,1),p(e))}}}async function g(e){const t=(0,a.Kd)();h.has(e)||h.set(e,async function(e,t){const r=[];for(const s of c)if(u(s.pattern,e))try{return await s.fetchMessageBundle(e,t)}catch(e){r.push(e)}if(r.length)throw new s.Z("intl:message-bundle-error",`Errors occurred while loading "${e}"`,{errors:r});throw new s.Z("intl:no-message-bundle-loader",`No loader found for message bundle "${e}"`)}(e,t));const r=h.get(e);return r&&await m.add(r),r}function d(e){if(!l.test(e))return null;const t=l.exec(e);if(null===t)return null;const[,r,s]=t,i=r+(s?"-"+s.toUpperCase():"");return o(i)?i:o(r)?r:null}function u(e,t){return"string"==typeof e?t.startsWith(e):e.test(t)}(0,a.Ze)((()=>{h.clear()}));const m=new class{constructor(){this._numLoading=0,this._dfd=null}async waitForAll(){this._dfd&&await this._dfd.promise}add(e){return this._increase(),e.then((()=>this._decrease()),(()=>this._decrease())),this.waitForAll()}_increase(){this._numLoading++,this._dfd||(this._dfd=(0,i.dD)())}_decrease(){this._numLoading=Math.max(this._numLoading-1,0),this._dfd&&0===this._numLoading&&(this._dfd.resolve(),this._dfd=null)}}},84230:(e,t,r)=>{r.d(t,{A2:()=>n,S1:()=>p,fb:()=>l,ln:()=>y,oP:()=>h,rQ:()=>o,y2:()=>c});var s=r(40330),i=r(3172),a=r(70586);const l={Point:"SceneLayer","3DObject":"SceneLayer",IntegratedMesh:"IntegratedMeshLayer",PointCloud:"PointCloudLayer",Building:"BuildingSceneLayer"};function n(e){const t=e?.type;return"building-scene"===t||"integrated-mesh"===t||"point-cloud"===t||"scene"===t}function o(e){return"feature"===e?.type&&!e.url&&"memory"===e.source?.type}function c(e){return"feature"===e?.type&&"feature-layer"===e.source?.type}async function h(e,t){const r=s.id?.findServerInfo(e);if(null!=r?.currentVersion)return r.owningSystemUrl||null;const l=e.toLowerCase().indexOf("/rest/services");if(-1===l)return null;const n=`${e.substring(0,l)}/rest/info`,o=(0,a.pC)(t)?t.signal:null,{data:c}=await(0,i.default)(n,{query:{f:"json"},responseType:"json",signal:o});return c?.owningSystemUrl||null}function p(e){return function(e){if(!("capabilities"in e))return!1;switch(e.type){case"csv":case"feature":case"geojson":case"imagery":case"knowledge-graph-sublayer":case"ogc-feature":case"oriented-imagery":case"scene":case"subtype-group":case"subtype-sublayer":case"wfs":return!0;default:return!1}}(e)?"effectiveCapabilities"in e?e.effectiveCapabilities:e.capabilities:null}function y(e){return!!function(e){if(!("editingEnabled"in e))return!1;switch(e.type){case"csv":case"feature":case"geojson":case"oriented-imagery":case"scene":case"subtype-group":case"subtype-sublayer":return!0;default:return!1}}(e)&&("effectiveEditingEnabled"in e?e.effectiveEditingEnabled:e.editingEnabled)}},15650:(e,t,r)=>{r.d(t,{Nw:()=>y});var s=r(20102),i=r(22974),a=r(70586),l=r(78286),n=r(827),o=r(84230);const c=new Set(["bing-maps","imagery","imagery-tile","map-image","open-street-map","tile","unknown","unsupported","vector-tile","web-tile","wms","wmts"]),h=new Set(["csv","feature","geo-rss","geojson","group","imagery","imagery-tile","kml","map-image","map-notes","media","ogc-feature","route","subtype-group","tile","unknown","unsupported","vector-tile","web-tile","wfs","wms","wmts"]);function p(e,t){"maxScale"in e&&(t.maxScale=(0,n.k)(e.maxScale)??void 0),"minScale"in e&&(t.minScale=(0,n.k)(e.minScale)??void 0)}function y(e,t,r){if(!("write"in e)||!e.write)return r&&r.messages&&r.messages.push(new s.Z("layer:unsupported",`Layers (${e.title}, ${e.id}) of type '${e.declaredClass}' cannot be persisted`,{layer:e})),null;if(function(e,t){if(t.restrictedWebMapWriting){const r=function(e){return"basemap"===e.layerContainerType?c:"operational-layers"===e.layerContainerType?h:null}(t);return!(0,a.pC)(r)||r.has(e.type)&&!(0,o.rQ)(e)}return!0}(e,r)){const t={};return e.write(t,r)?t:null}return(0,a.pC)(t)&&function(e,t){if(function(e,t){if(t)if((0,o.rQ)(e)){const r=(0,l.hS)("featureCollection.layers",t),s=r&&r[0]&&r[0].layerDefinition;s&&p(e,s)}else"stream"===e.type?p(e,t.layerDefinition=t.layerDefinition||{}):"group"!==e.type&&p(e,t)}(e,t),t&&("blendMode"in e&&(t.blendMode=e.blendMode,"normal"===t.blendMode&&delete t.blendMode),t.opacity=(0,n.k)(e.opacity)??void 0,t.title=e.title||"Layer",t.visibility=e.visible,"legendEnabled"in e&&"wmts"!==e.type))if((0,o.rQ)(e)){const r=t.featureCollection;r&&(r.showLegend=e.legendEnabled)}else t.showLegend=e.legendEnabled}(e,t=(0,i.d9)(t)),t}}}]);