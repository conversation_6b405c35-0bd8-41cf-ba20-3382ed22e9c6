import{C as et,a6 as ot,c as k,r as x,v as at,bB as U,ay as it,g as F,h as E,F as u,n as N,p as d,G as g,bh as w,q as s,an as rt,aJ as nt,aB as st,i as lt,j as pt,_ as mt,bt as dt,c2 as ut,I as ct,aK as ft,aL as gt,J as ht,K as bt,aq as vt,c5 as yt,N as _t,c6 as wt,O as Dt}from"./index-r0dFAfgr.js";import{w as Tt}from"./Point-WxyopZva.js";import{g as I,n as Ct,m as kt}from"./MapView-DaoQedLH.js";import xt from"./RightDrawerMap-D5PhmGFO.js";import{g as Ft}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as Rt}from"./ViewHelper-BGCZjxXH.js";import{r as Y}from"./chart-wy3NEK2T.js";import{g as A}from"./URLHelper-B9aplt5w.js";import{l as Mt}from"./echart-UKlzIoq9.js";import"./widget-BcWKanF2.js";import"./ArcView-DpMnCY82.js";import"./geometryEngineBase-BhsKaODW.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";const Vt={class:"content"},Lt={class:"search-box"},Ot={class:"right-box"},St={key:0,class:"report-detail"},Ut={class:"report-info"},Et={class:"report-indicators"},Nt={class:"report-actions"},It={__name:"index",setup(Yt){const B=()=>Promise.resolve({data:[{id:"sample1",name:"东区采样点1",location:"116.405285,39.904989",sampleTime:"2023-05-10",testTime:"2023-05-12",testUnit:"水务局水质检测中心",reportId:"report001",indicators:[{name:"浑浊度",property:"turbidity",value:"0.8",unit:"NTU",standard:"≤1",status:"合格"},{name:"pH值",property:"ph",value:"7.2",unit:"",standard:"6.5-8.5",status:"合格"},{name:"余氯",property:"chlorine",value:"0.3",unit:"mg/L",standard:"0.3-0.5",status:"合格"},{name:"总硬度",property:"hardness",value:"280",unit:"mg/L",standard:"≤450",status:"合格"}]},{id:"sample2",name:"西区采样点1",location:"116.315285,39.894989",sampleTime:"2023-05-15",testTime:"2023-05-17",testUnit:"水务局水质检测中心",reportId:"report002",indicators:[{name:"浑浊度",property:"turbidity",value:"1.2",unit:"NTU",standard:"≤1",status:"超标"},{name:"pH值",property:"ph",value:"7.5",unit:"",standard:"6.5-8.5",status:"合格"},{name:"余氯",property:"chlorine",value:"0.4",unit:"mg/L",standard:"0.3-0.5",status:"合格"},{name:"总硬度",property:"hardness",value:"320",unit:"mg/L",standard:"≤450",status:"合格"}]},{id:"sample3",name:"南区采样点1",location:"116.405285,39.854989",sampleTime:"2023-05-20",testTime:"2023-05-22",testUnit:"水务局水质检测中心",reportId:"report003",indicators:[{name:"浑浊度",property:"turbidity",value:"0.7",unit:"NTU",standard:"≤1",status:"合格"},{name:"pH值",property:"ph",value:"7.8",unit:"",standard:"6.5-8.5",status:"合格"},{name:"余氯",property:"chlorine",value:"0.28",unit:"mg/L",standard:"0.3-0.5",status:"不足"},{name:"总硬度",property:"hardness",value:"310",unit:"mg/L",standard:"≤450",status:"合格"}]}]}),H=(e,t)=>{const i=p=>Array.from({length:6},(l,m)=>{const a=new Date().getMonth()-5+m,n=new Date;return n.setMonth(a),{time:n.toISOString().substring(0,7),value:(p+Math.random()*.5-.25).toFixed(2)}}),r={turbidity:i(.8),ph:i(7.2),chlorine:i(.35),hardness:i(300)};return Promise.resolve({data:r[t]||[]})},P=ot(),W=k(),y=k(),O=k(),R=k(),o=x({lineOption:null,curRow:{},activeName:null,windows:[],tableData:[],samplingData:[],searchForm:{month:new Date().toISOString().substring(0,7),indicator:"turbidity"},indicatorOptions:[{value:"turbidity",label:"浑浊度"},{value:"ph",label:"pH值"},{value:"chlorine",label:"余氯"},{value:"hardness",label:"总硬度"}],reportDetail:{}}),f={view:null},S=x({group:[{id:"chart",fieldset:{desc:"水质采样统计",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:Y(),style:{height:"150px"}}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),b=x({loading:!1,dataList:[],columns:[{prop:"name",label:"采样点名称",minWidth:120},{prop:"sampleTime",label:"采样时间",minWidth:100},{prop:"testTime",label:"化验时间",minWidth:100},{prop:"testUnit",label:"化验单位",minWidth:160},{prop:"report",label:"化验报告",minWidth:100,slot:{default:({row:e})=>at("el-button",{type:"primary",size:"small",onClick:()=>$(e)},"查看")}}],highlightCurrentRow:!0,currentRowKey:"id",handleRowClick:async e=>{var i;const t=(i=f.view)==null?void 0:i.graphics.find(r=>{var p,l;return((l=(p=r.attributes)==null?void 0:p.row)==null?void 0:l.id)===e.id});t&&await Ft(f.view,t,{zoom:15,avoidHighlight:!0}),o.curRow=e,L(e.id),V(e)},pagination:{hide:!0}}),z=x({type:"tabs",tabType:"simple",tabs:[{label:"近6个月趋势",value:"trend"}],handleTabClick:e=>{}}),M=async()=>{b.loading=!0;const e=await B();o.samplingData=e.data;const t=e.data.filter(i=>i.sampleTime.substring(0,7)===o.searchForm.month);b.dataList=t,b.loading=!1,j(t),Q(t)},$=e=>{var t;o.reportDetail=e,(t=y.value)==null||t.toggleCustomDetail(!0)},q=()=>{var e;(e=y.value)==null||e.toggleCustomDetail(!1),o.reportDetail={}},K=()=>{alert("下载报告: "+o.reportDetail.reportId)},V=async e=>{if(!e)return;const i=(await H(e.id,o.searchForm.indicator)).data,r=i.map(n=>n.time),p=i.map(n=>n.value),l=o.indicatorOptions.find(n=>n.value===o.searchForm.indicator),m=e.indicators.find(n=>n.property===o.searchForm.indicator),a=Mt(200,r,40,40);a.yAxis[0].name=(l==null?void 0:l.label)+(m!=null&&m.unit?"("+m.unit+")":""),a.series=[{name:e.name,smooth:!0,data:p,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}],o.lineOption=a,await U(()=>{R.value&&P.listenTo(R.value,()=>{var n;o.lineOption=a,(n=O.value)==null||n.resize()})})},Q=e=>{const t=e.length,i=e.reduce((l,m)=>{const a=m.indicators.every(n=>n.status==="合格");return l+(a?1:0)},0),r=[{name:"合格",nameAlias:"合格",value:i,scale:t?(i/t*100).toFixed(1)+"%":"0%"},{name:"不合格",nameAlias:"不合格",value:t-i,scale:t?((t-i)/t*100).toFixed(1)+"%":"0%"}],p=S.group[0].fields[0];p&&(p.option=Y(r,"个"))},G=()=>{M()},J=()=>{var e;(e=o.curRow)!=null&&e.id&&V(o.curRow)},j=e=>{var t,i;(i=(t=f.view)==null?void 0:t.graphics)==null||i.removeAll(),e.forEach(r=>{var D,T,C;const p=r.location.split(","),l=new Tt({longitude:p[0],latitude:p[1],spatialReference:(D=f.view)==null?void 0:D.spatialReference}),m=r.indicators.every(h=>h.status==="合格"),a=m?A("水质监测站.png"):A("水质监测站-警告.png"),n=new I({geometry:l,symbol:new Ct({width:25,height:30,yoffset:15,url:a}),attributes:{row:r}}),v=new I({geometry:l,symbol:new kt({yoffset:-15,color:m?"#00ff33":"#ff3300",text:r.name})});(C=(T=f.view)==null?void 0:T.graphics)==null||C.addMany([n,v])}),e.length>0&&L(e[0].id)},L=async e=>{var l,m;const t=o.samplingData.find(a=>a.id===e);if(!t)return;const i=b.dataList.find(a=>a.id===e);b.currentRow=i,o.curRow=t,V(t);const r=(l=f.view)==null?void 0:l.graphics.find(a=>{var n,v;return((v=(n=a.attributes)==null?void 0:n.row)==null?void 0:v.id)===e});if(!r)return;const p=r==null?void 0:r.geometry;o.windows.length=0,o.windows.push({visible:!1,x:p.x,y:p.y,offsetY:-30,title:t.name,attributes:{values:t.indicators.map(a=>({label:a.name,value:a.value+" "+a.unit,status:a.status})),id:t.id}}),await U(),(m=y.value)==null||m.openPop(t.id)},X=async e=>{var t;f.view=e,(t=y.value)==null||t.toggleCustomDetail(!1),await M(),Rt(f.view,i=>{var p,l,m;const r=(p=i.results)==null?void 0:p[0];if(r&&r.type==="graphic"){const a=(m=(l=r.graphic)==null?void 0:l.attributes)==null?void 0:m.row;L(a==null?void 0:a.id)}})};return(e,t)=>{const i=mt,r=dt,p=ut,l=ct,m=ft,a=gt,n=ht,v=bt,D=vt,T=yt,C=it("VChart"),h=_t,Z=wt,tt=Dt;return F(),E(xt,{ref_key:"refMap",ref:y,title:"水质分布",windows:o.windows,"hide-detail-close":!0,"hide-layer-list":!0,"right-drawer-width":540,onMapLoaded:X},{"detail-default":u(()=>[o.reportDetail.id?(F(),N("div",St,[t[11]||(t[11]=d("h3",null,"化验报告详情",-1)),d("div",Ut,[d("p",null,[t[4]||(t[4]=d("span",{class:"label"},"采样点:",-1)),g(" "+w(o.reportDetail.name),1)]),d("p",null,[t[5]||(t[5]=d("span",{class:"label"},"采样时间:",-1)),g(" "+w(o.reportDetail.sampleTime),1)]),d("p",null,[t[6]||(t[6]=d("span",{class:"label"},"化验时间:",-1)),g(" "+w(o.reportDetail.testTime),1)]),d("p",null,[t[7]||(t[7]=d("span",{class:"label"},"化验单位:",-1)),g(" "+w(o.reportDetail.testUnit),1)])]),d("div",Et,[t[8]||(t[8]=d("h4",null,"检测指标",-1)),s(tt,{data:o.reportDetail.indicators,border:"",style:{width:"100%"}},{default:u(()=>[s(h,{prop:"name",label:"指标名称"}),s(h,{prop:"value",label:"检测值"}),s(h,{prop:"unit",label:"单位"}),s(h,{prop:"standard",label:"标准值"}),s(h,{prop:"status",label:"状态"},{default:u(_=>[s(Z,{type:_.row.status==="合格"?"success":"danger"},{default:u(()=>[g(w(_.row.status),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])]),d("div",Nt,[s(n,{type:"primary",onClick:K},{default:u(()=>t[9]||(t[9]=[g("下载报告")])),_:1}),s(n,{onClick:q},{default:u(()=>t[10]||(t[10]=[g("关闭")])),_:1})])])):rt("",!0)]),default:u(()=>{var _;return[d("div",Vt,[s(i,{ref:"refForm",config:S},null,8,["config"]),s(r,{type:"underline",title:"水质采样查询"}),d("div",Lt,[s(v,{inline:!0,class:"demo-form-inline"},{default:u(()=>[s(l,{label:"采样月份"},{default:u(()=>[s(p,{modelValue:o.searchForm.month,"onUpdate:modelValue":t[0]||(t[0]=c=>o.searchForm.month=c),type:"month",placeholder:"选择月份",format:"YYYY-MM","value-format":"YYYY-MM",onChange:G},null,8,["modelValue"])]),_:1}),s(l,{label:"水质指标"},{default:u(()=>[s(a,{modelValue:o.searchForm.indicator,"onUpdate:modelValue":t[1]||(t[1]=c=>o.searchForm.indicator=c),placeholder:"请选择水质指标",onChange:J},{default:u(()=>[(F(!0),N(st,null,nt(o.indicatorOptions,c=>(F(),E(m,{key:c.value,label:c.label,value:c.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(l,null,{default:u(()=>[s(n,{type:"primary",onClick:M},{default:u(()=>t[3]||(t[3]=[g("查询")])),_:1})]),_:1})]),_:1})]),s(r,{type:"underline",title:"水质采样结果"}),d("div",Ot,[s(D,{ref_key:"refTable",ref:W,class:"table-box",config:b},null,8,["config"])]),s(r,{type:"underline",title:(((_=o.curRow)==null?void 0:_.name)||"")+"水质指标趋势分析"},null,8,["title"]),d("div",{ref_key:"echartsDiv",ref:R,class:"right-box bottom"},[s(T,{modelValue:o.activeName,"onUpdate:modelValue":t[2]||(t[2]=c=>o.activeName=c),config:z},null,8,["modelValue","config"]),s(C,{ref_key:"refChart",ref:O,theme:lt(pt)().isDark?"dark":"light",option:o.lineOption},null,8,["theme","option"])],512)])]}),_:1},8,["windows"])}}},zo=et(It,[["__scopeId","data-v-3334559f"]]);export{zo as default};
