package org.thingsboard.server.dao.model.sql.smartOperation.device;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;


@Getter
@Setter
@ResponseEntity
public class SoDeviceItem {
    // id
    private String id;

    // 所属作用域
    private SoGeneralSystemScope scope;

    // 所属标识（id、code等）
    private String identifier;

    // 设备编码
    private String serialId;

    // 设备名称
    @TableField(exist = false)
    private String deviceName;

    // 所属大类
    @TableField(exist = false)
    private String deviceTopTypeSerialId;

    // 所属大类名
    @TableField(exist = false)
    private String deviceTopTypeName;

    // 所属类别
    @TableField(exist = false)
    private String deviceTypeSerialId;

    // 所属类别
    @TableField(exist = false)
    private String deviceTypeName;

    // 所属类别
    @TableField(exist = false)
    private String deviceType;

    // 型号
    @TableField(exist = false)
    private String model;

    // 标识
    @TableField(exist = false)
    private String mark;

    // 单位
    @TableField(exist = false)
    private String unit;

    // 数量
    private Integer amount;

    // 剩余数量
    @TableField(exist = false)
    private Integer rest;

    // 项目编号
    @TableField(exist = false)
    private String projectCode;

    // 工程编号
    @TableField(exist = false)
    private String constructionCode;

    // 合同编号
    @TableField(exist = false)
    private String contractCode;

    // 实施编号
    @TableField(exist = false)
    private String applyCode;

    // 客户id
    private String tenantId;

}
