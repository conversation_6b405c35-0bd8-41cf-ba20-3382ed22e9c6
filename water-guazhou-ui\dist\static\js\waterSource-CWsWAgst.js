import{d as N,c as T,r as d,ab as B,s as L,am as R,j as V,o as P,g as U,n as j,q as k,i as C,p as q,_ as A,aq as $,C as z}from"./index-r0dFAfgr.js";import{w as E}from"./Point-WxyopZva.js";import{r as S}from"./chart-wy3NEK2T.js";import{P as Y}from"./index-CcDafpIP.js";import{g as G}from"./headwaterMonitoring-BgK7jThW.js";import{g as H}from"./URLHelper-B9aplt5w.js";const J={class:"onemap-panel-wrapper"},K={class:"table-box"},O=N({__name:"waterSource",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(I,{emit:W}){const u=W,c=I,m=T(),f=d({group:[{id:"chart",fieldset:{desc:"今日供水量各水源占比图",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:S(),style:{height:"150px"}}]},{fields:[{type:"input",field:"name",appendBtns:[{perm:!0,text:"刷新",click:()=>n()}],onChange:()=>n()}]}],labelPosition:"top",gutter:12}),o=d({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:a,size:e})=>{o.pagination.page=a,o.pagination.limit=e},layout:"total,sizes, jumper"},handleRowClick:a=>F(a),columns:[{minWidth:120,label:"名称",prop:"name",sortable:!0},{minWidth:150,label:"今日供水量(m³)",prop:"todayWaterSupply",sortable:!0},{minWidth:150,label:"更新时间",prop:"lastTime",sortable:!0}]}),g=d({dataList:[],columns:[],pagination:{refreshData:({page:a,size:e})=>{g.pagination.page=a,g.pagination.limit=e,n()}}}),n=async()=>{var a,e,r,l,h,_;o.loading=!0;try{const s=await G({name:(a=m.value)==null?void 0:a.dataForm.name});o.dataList=((e=s.data)==null?void 0:e.data)||[];const y=f.group[0].fields[0],D=(l=(r=s.data)==null?void 0:r.data)==null?void 0:l.reduce((t,p)=>t+Number(p.todayWaterSupply||"0"),0),b=[],M=((_=(h=s.data)==null?void 0:h.data)==null?void 0:_.map(t=>{var v,w;const p=B(t.todayWaterSupply||0),i=(v=t.location)==null?void 0:v.split(",");if((i==null?void 0:i.length)===2){const x=new E({longitude:i[0],latitude:i[1],spatialReference:(w=c.view)==null?void 0:w.spatialReference});b.push({visible:!1,id:t.stationId,x:x.x,y:x.y,offsetY:-40,title:t.name,customComponent:L(Y),customConfig:{info:{type:"attrs",imageUrl:t.imgs,stationId:t.stationId}},attributes:{path:c.menu.path,id:t.stationId,row:t},symbolConfig:{url:H("水源地.png")}})}return{name:t.name,value:t.todayWaterSupply,valueAlias:p.value.toFixed(2)+p.unit,scale:(Number(t.todayWaterSupply)/D*100).toFixed(2)+"%"}}))||[];y&&(y.option=S(M,"m³")),u("addMarks",{windows:b})}catch(s){console.dir(s)}o.loading=!1},F=async a=>{u("highlightMark",c.menu,a==null?void 0:a.stationId)};return R(()=>V().isDark,()=>{n()}),P(()=>{n()}),(a,e)=>{const r=A,l=$;return U(),j("div",J,[k(r,{ref_key:"refForm",ref:m,config:C(f)},null,8,["config"]),q("div",K,[k(l,{config:C(o)},null,8,["config"])])])}}}),oa=z(O,[["__scopeId","data-v-3f26ce57"]]);export{oa as default};
