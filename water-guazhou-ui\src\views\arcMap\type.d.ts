interface IPipeDetailTab {
  label: string
  name: string
  id?: string
  data?: any
}

interface IGISField {
  name: string
  alias: string
  group: string
  index: number
}
interface IGISFieldConfig {
  layerdbname: string
  layername: string
  objectid: boolean
  fields: IGISField[]
}

interface ILayerInfo {
  geometrytype: 'esriGeometryPolyline' | 'esriGeometryPoint'
  layerdbname: string
  layerid: number
  layername: string
}

type ISchemeType = 'pick' | 'quick' | 'line' | 'buffer' | 'manual' | 'condition'
