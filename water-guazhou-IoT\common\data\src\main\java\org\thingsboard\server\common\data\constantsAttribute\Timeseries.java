/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.constantsAttribute;

import lombok.Data;

@Data
public class Timeseries {
    private String tag;
    private String type;
    private String pollPeriod;
    private String functionCode;
    private String address;
    private String registerCount;
    private String bit;
    private String byteOrder;
    private String order;

    public Timeseries(){}

    public Timeseries(String tag, String type, String functionCode, String address, String registerCount, String bit, String byteOrder,String order) {
        this.tag = tag;
        this.type = type;
        this.functionCode = functionCode;
        this.address = address;
        this.registerCount = registerCount;
        this.bit = bit;
        this.byteOrder = byteOrder;
        this.order=order;
    }
}
