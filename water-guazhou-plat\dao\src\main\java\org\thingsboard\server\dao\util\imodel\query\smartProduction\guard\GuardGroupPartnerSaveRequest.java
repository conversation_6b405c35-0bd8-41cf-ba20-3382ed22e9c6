package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroupPartner;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class GuardGroupPartnerSaveRequest extends SaveRequest<GuardGroupPartner> {
    private String groupId;

    private String userId;


    @Override
    protected GuardGroupPartner build() {
        GuardGroupPartner entity = new GuardGroupPartner();
        commonSet(entity);
        return entity;
    }

    @Override
    protected GuardGroupPartner update(String id) {
        GuardGroupPartner entity = new GuardGroupPartner();
        commonSet(entity);
        return entity;
    }

    private void commonSet(GuardGroupPartner entity) {
        entity.setGroupId(groupId);
        entity.setUserId(userId);
    }

}