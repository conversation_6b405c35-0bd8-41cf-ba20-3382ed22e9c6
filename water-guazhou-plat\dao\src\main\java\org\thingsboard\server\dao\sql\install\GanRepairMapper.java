package org.thingsboard.server.dao.sql.install;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.GanInstallDTO;
import org.thingsboard.server.dao.model.sql.install.GanRepair;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-07-24
 */
@Mapper
public interface GanRepairMapper extends BaseMapper<GanRepair> {

    IPage<GanRepair> getList(IPage<GanRepair> page, @Param("param") GanInstallDTO ganInstallDTO);

    IPage<GanRepair> getMyComplete(Page<GanRepair> page1, @Param("param") GanInstallDTO ganInstallDTO);

    List<GanRepair> getNotCompleteList();

}
