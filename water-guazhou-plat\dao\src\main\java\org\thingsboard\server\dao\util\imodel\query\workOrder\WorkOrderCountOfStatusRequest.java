package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.query.TimeableQueryEntity;

import java.util.Set;

@Getter
@Setter
public abstract class WorkOrderCountOfStatusRequest extends TimeableQueryEntity implements WorkOrderStagibleRequest{
    // 处理人
    private String processUserId;

    public abstract Set<WorkOrderStatus> getActiveStageSet();

}
