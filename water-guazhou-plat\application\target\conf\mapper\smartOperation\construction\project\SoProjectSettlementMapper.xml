<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.project.SoProjectSettlementMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        report.id,
        project.code          project_code,
        project.name       as project_name,
        project.start_time as project_start_time,
        report.accept_time,
        report.cost,
        report.remark,
        report.attachments,
        report.creator,
        report.create_time,
        report.update_user,
        report.update_time,
        report.tenant_id,
        details.contract_total_cost,
        details.expense_total_cost
        <!--@sql from so_project_settlement report, so_project project, so_project_details details -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectSettlement">
        <result column="id" property="id"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="project_start_time" property="projectStartTime"/>
        <result column="accept_time" property="acceptTime"/>
        <result column="cost" property="cost"/>
        <result column="contract_total_cost" property="contractTotalCost"/>
        <result column="expense_total_cost" property="expenseTotalCost"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_project project
                 left join so_project_settlement report
                           on project.code = report.project_code and project.tenant_id = report.tenant_id
                 left join so_project_details details
                           on project.code = details.current_project_code and project.tenant_id = details.current_tenant_id
        <where>
            <if test="projectCode != null and projectCode != ''">
                and project_code = #{projectCode}
            </if>
            <if test="projectName != null and projectName != ''">
                and project.name like '%' || #{projectName} || '%'
            </if>
            and project.tenant_id = #{tenantId}
        </where>
        order by project.create_time desc
    </select>

    <update id="update">
        update so_project_settlement
        <set>
            <if test="acceptTime != null">
                accept_time = #{acceptTime},
            </if>
            <if test="cost != null">
                cost = #{cost},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="attachments != null and attachments != ''">
                attachments = #{attachments},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_project_settlement
        set accept_time = #{acceptTime},
            cost        = #{cost},
            remark      = #{remark},
            attachments = #{attachments}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO so_project_settlement(id,
                                          project_code,
                                          accept_time,
                                          cost,
                                          remark,
                                          attachments,
                                          creator,
                                          create_time,
                                          update_user,
                                          update_time,
                                          tenant_id)
        VALUES (#{id},
                #{projectCode},
                #{acceptTime},
                #{cost},
                #{remark},
                #{attachments},
                #{creator},
                #{createTime},
                #{updateUser},
                #{updateTime},
                #{tenantId})
    </insert>

    <select id="getIdByProjectCodeAndTenantId" resultType="java.lang.String">
        select id
        from so_project_settlement
        where project_code = #{projectCode}
          and tenant_id = #{tenantId}
    </select>
</mapper>