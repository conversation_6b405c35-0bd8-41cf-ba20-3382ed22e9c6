package org.thingsboard.server.dao.sql.largeScreen;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.largeScreen.LargeScreenConfig;
import org.thingsboard.server.dao.util.imodel.query.largeScreen.LargeScreenConfigRequest;

/**
 * 大屏配置
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2024-01-30
 */
@Mapper
public interface LargeScreenConfigMapper extends BaseMapper<LargeScreenConfig> {

    IPage<LargeScreenConfig> getList(IPage<LargeScreenConfig> custPage, @Param("param") LargeScreenConfigRequest largeScreenConfigRequest);

}
