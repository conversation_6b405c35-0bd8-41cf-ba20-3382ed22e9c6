import{C as I,M as T,g as b,h,F as o,q as l,G as u,H as V,I as v,aK as y,aL as k,J as U,K as B,L as D}from"./index-r0dFAfgr.js";import{m as x}from"./index-BggOjNGp.js";import{c as E}from"./formValidate-U0WTqY4Y.js";const{$message:r}=T(),M={name:"TpDialog",props:["dialogInfo"],emits:["getTemlate"],data(){return{form:{name:"",type:"",remark:"",additionalInfo:""},typeEdit:!1,currencyRules:E}},computed:{visible(){return this.dialogInfo.visible}},created(){const s=this.dialogInfo.template;for(const e in s)this.form[e]=s[e];this.form.type=this.dialogInfo.type},methods:{save(){this.$refs.form.validate(s=>{if(s){console.log(this.dialogInfo);for(const e of this.dialogInfo.data)if(this.dialogInfo.template.id&&e.id!==this.dialogInfo.template.id&&e.name===this.form.name||!this.dialogInfo.template.id&&e.name===this.form.name){r("协议模板名称不可重复");return}this.dialogInfo.template.id&&(this.form.id=this.dialogInfo.template.id,this.form.createTime=this.dialogInfo.template.createTime),x(this.form).then(()=>{this.$emit("getTemlate"),r({type:"success",message:"操作成功"}),this.dialogInfo.close()})}else return r.error("请按提示输入信息后再保存"),!1})}}};function C(s,e,d,N,t,n){const f=V,m=v,i=y,c=k,p=U,_=B,g=D;return b(),h(g,{modelValue:n.visible,"onUpdate:modelValue":e[3]||(e[3]=a=>n.visible=a),title:d.dialogInfo.currentTitle,class:"alarm-design","close-on-click-modal":!1},{default:o(()=>[l(_,{ref:"form",model:t.form,class:"template-form","label-width":"120px",width:"30%"},{default:o(()=>[l(m,{label:"模板名称",prop:"name",rules:t.currencyRules.text},{default:o(()=>[l(f,{modelValue:t.form.name,"onUpdate:modelValue":e[0]||(e[0]=a=>t.form.name=a),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1},8,["rules"]),l(m,{label:"协议类型",prop:"type"},{default:o(()=>[l(c,{modelValue:t.form.type,"onUpdate:modelValue":e[1]||(e[1]=a=>t.form.type=a),placeholder:"请选择协议类型",disabled:""},{default:o(()=>[l(i,{label:"MQTT协议",value:"MQTT"}),l(i,{label:"MODBUS协议",value:"MODBUS"}),l(i,{label:"DTU协议",value:"DTU"}),l(i,{label:"NB-DTU协议",value:"NBDTU"}),l(i,{label:"NB-MQTT协议",value:"NBMQTT"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"备注"},{default:o(()=>[l(f,{modelValue:t.form.remark,"onUpdate:modelValue":e[2]||(e[2]=a=>t.form.remark=a),type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),l(m,{label:""},{default:o(()=>[l(p,{type:"primary",onClick:n.save},{default:o(()=>e[4]||(e[4]=[u(" 保存 ")])),_:1},8,["onClick"]),l(p,{onClick:d.dialogInfo.close},{default:o(()=>e[5]||(e[5]=[u(" 取消 ")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}const O=I(M,[["render",C],["__scopeId","data-v-bed951a3"]]);export{O as default};
