package org.thingsboard.server.dao.largeScreen;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.ScadaConfig;
import org.thingsboard.server.dao.sql.largeScreen.ScadaConfigRepository;

import java.util.List;

@Slf4j
@Service
public class ScadaConfigServiceImpl implements ScadaConfigService {

    @Autowired
    private ScadaConfigRepository scadaConfigRepository;

    @Override
    public List<ScadaConfig> findList() {
        return scadaConfigRepository.findAll();
    }

    @Override
    public ScadaConfig findByCode(String code) {
        List<ScadaConfig> byCode = scadaConfigRepository.findByCode(code);
        if (byCode != null && byCode.size() > 0) {
            return byCode.get(0);
        }
        return null;
    }

    @Override
    public void save(ScadaConfig entity) {
        ScadaConfig scadaConfig = this.findByCode(entity.getCode());
        if (scadaConfig != null) {
            entity.setId(scadaConfig.getId());
        }
        scadaConfigRepository.save(entity);
    }

    @Override
    public ScadaConfig findByType(String id) {
        List<ScadaConfig> byType = scadaConfigRepository.findByType(id);
        if (byType != null && byType.size() > 0) {
            return byType.get(0);
        }
        return null;
    }
}
