import{d as R,c as T,r as C,s as w,o as V,am as A,j as L,g as P,n as U,q as I,i as W,p as j,ab as q,_ as $,aq as z,C as E}from"./index-r0dFAfgr.js";import{w as Y}from"./Point-WxyopZva.js";import{P as F}from"./index-CcDafpIP.js";import{r as S}from"./chart-wy3NEK2T.js";import{a as G}from"./monitoringOverview-DvKhtmcR.js";import{g as H}from"./URLHelper-B9aplt5w.js";const J={class:"onemap-panel-wrapper"},K={class:"table-box"},O=R({__name:"secondary",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(M,{emit:B}){const d=B,p=M,m=T(),u=C({group:[{id:"chart",fieldset:{desc:"今日供水量泵站占比",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:S(),style:{height:"150px"}}]},{id:"tab",fields:[{type:"input",field:"name",appendBtns:[{perm:!0,text:"刷新",click:()=>i()}],onChange:()=>i()}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),n=C({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:t,size:e})=>{n.pagination.page=t,n.pagination.limit=e},layout:"total,sizes, jumper"},handleRowClick:t=>N(t),columns:[{minWidth:120,label:"名称",prop:"name",sortable:!0},{minWidth:150,label:"今日送水量(m³)",prop:"todayWaterSupply",sortable:!0},{minWidth:160,label:"更新时间",prop:"lastTime",sortable:!0}]}),D=t=>{var o;const e=q(t);return(((o=e.value)==null?void 0:o.toFixed(2))||"--")+e.unit},i=async t=>{var e,o,l,f,g,h;n.loading=!0;try{const s=await G({name:(e=m.value)==null?void 0:e.dataForm.name});n.dataList=((o=s.data)==null?void 0:o.data)||[];const y=u.group[0].fields[0];let c=0;(f=(l=s.data)==null?void 0:l.data)==null||f.map(a=>c+=a.todayWaterSupply||0);const _=[],b=((h=(g=s.data)==null?void 0:g.data)==null?void 0:h.map(a=>{var v,x;const r=(v=a.location)==null?void 0:v.split(",");if((r==null?void 0:r.length)===2){const k=new Y({longitude:r[0],latitude:r[1],spatialReference:(x=p.view)==null?void 0:x.spatialReference});_.push({visible:!1,id:a.stationId,x:k.x,y:k.y,offsetY:-40,title:a.name,customComponent:w(F),customConfig:{info:{type:"attrs",imageUrl:a.imgs,stationId:a.stationId}},attributes:{path:p.menu.path,id:a.stationId,row:a},symbolConfig:{url:H("泵站.png")}})}return{name:a.name,nameAlias:a.name,value:a.todayWaterSupply,valueAlias:D(a.todayWaterSupply),scale:"0%"}}))||[];b.map(a=>(a.scale=c===0?"0%":(Number(a.value)/c*100).toFixed(2)+"%",a)),t&&y&&(y.option=S(b,"m³")),d("addMarks",{windows:_,customWinComp:w(F)})}catch(s){console.dir(s)}n.loading=!1},N=async t=>{d("highlightMark",p.menu,t==null?void 0:t.stationId)};return V(()=>{i(!0)}),A(()=>L().isDark,()=>i(!0)),(t,e)=>{const o=$,l=z;return P(),U("div",J,[I(o,{ref_key:"refForm",ref:m,config:W(u)},null,8,["config"]),j("div",K,[I(l,{config:W(n)},null,8,["config"])])])}}}),oa=E(O,[["__scopeId","data-v-eba945d8"]]);export{oa as default};
