import{d as j,r as w,c as z,S as V,b as h,a1 as $,o as K,Q as W,g as X,h as Y,i as Z,_ as ee}from"./index-r0dFAfgr.js";import{w as te}from"./MapView-DaoQedLH.js";import{SetFieldConfig as re,GetFieldConfig as k}from"./fieldconfig-Bk3o1wi7.js";import{f as P,c as ae,s as N,h as ie}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import{i as oe,e as se}from"./IdentifyHelper-RJWmLn49.js";import{a as ne,g as T}from"./LayerHelper-Cn-iiqxI.js";import{a as le,i as pe}from"./QueryHelper-ILO3qZqg.js";import{i as B,s as q}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import"./Point-WxyopZva.js";/* empty css                                                                      */import{a as me}from"./URLHelper-B9aplt5w.js";import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./pipe-nogVzCHG.js";const Gt=j({__name:"AttrAnnotation",props:{view:{}},setup(R){const s=R,d=w({pipeLayerOption:[],fieldOptions:[]}),i={drawer:void 0,queryParams:oe()},c=z(),g=w({data:[],columns:[]}),v=w({labelPosition:"top",gutter:12,group:[{fields:[{type:"tabs",field:"type",tabType:"simple",tabs:[{label:"属性标注",value:"anno"},{label:"设置",value:"setting"}]}]},{handleHidden:(e,a,t)=>{t.hidden=e.type!=="anno"},fieldset:{desc:"要素选择",right:[{style:{marginLeft:"auto"},items:[{type:"btn-group",label:"",btns:[{perm:!0,text:"框选",size:"small",click:()=>{var e,a,t,r,n,l,p;s.view&&((e=i.drawAction)==null||e.destroy(),(a=i.drawer)==null||a.destroy(),(t=i.graphicLayer)==null||t.removeAll(),i.drawer=B(s.view),i.drawAction=(r=i.drawer)==null?void 0:r.create("rectangle"),(n=i.drawAction)==null||n.on("vertex-add",x),(l=i.drawAction)==null||l.on("cursor-update",x),(p=i.drawAction)==null||p.on("draw-complete",o=>I(o,!0)))}}]}]}]},fields:[{type:"list",field:"name",label:"",highlightCurrentRow:!0,data:[],displayField:"layerName",valueField:"value",wrapperStyle:{height:"200px"},nodeClick:async e=>{C(e)},formatter:(e,a,t)=>{var r;return(a[t.displayField]||"")+"/"+(((r=a==null?void 0:a.attributes)==null?void 0:r.OBJECTID)||"")}}]},{handleHidden:(e,a,t)=>{t.hidden=e.type!=="anno"},fieldset:{desc:"详细"},fields:[{type:"attr-table",config:g}]},{handleHidden:(e,a,t)=>{t.hidden=e.type!=="setting"},fieldset:{desc:"图层选择"},fields:[{type:"select",field:"layer",options:[],onChange:()=>b()}]},{id:"visibleattrs",handleHidden:(e,a,t)=>{t.hidden=e.type!=="setting"},fieldset:{desc:"可见属性"},fields:[{type:"tree",style:{height:"300px"},field:"attrs",options:[],showCheckbox:!0,multiple:!0}],groupBtns:{styles:{marginBottom:"20px",display:"flex",justifyContent:"space-between"},btns:[{perm:!0,text:"全选",styles:{width:"100%"},click:()=>y("all")},{perm:!0,text:"全不选",styles:{width:"100%"},click:()=>y("none")},{perm:!0,text:"重置",type:"default",styles:{width:"100%"},click:()=>y()}]}},{handleHidden:(e,a,t)=>{t.hidden=e.type!=="setting"},fields:[{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},type:"success",text:"保存",click:()=>{var e;(e=c.value)==null||e.Submit()}}]}]}],defaultValue:{type:"anno",layer:""},submit:e=>{V("确定保存?","提示信息").then(async()=>{try{if(!e.layer)return;const a=d.fieldOptions.map(r=>(r.visible=e.attrs.indexOf(r.name)!==-1,r)),t=await re({layername:e.layer,fields:a,f:"pjson"});t.data.code===1e4?h.success(t.data.message||"保存成功"):h.error(t.data.message||"保存失败")}catch{h.error("系统错误")}}).catch(()=>{})}}),b=async()=>{var r,n,l,p;const e=(r=c.value)==null?void 0:r.dataForm.layer,a=(n=v.group.find(o=>o.id==="visibleattrs"))==null?void 0:n.fields[0],t=await k(e);d.fieldOptions=((p=(l=t.data)==null?void 0:l.result)==null?void 0:p.rows)||[],a&&(a.options=$(d.fieldOptions,{id:"name",label:"alias",value:"name"})),y()},y=e=>{var t;const a=e==="none"?[]:e==="all"?d.fieldOptions.map(r=>r.name):d.fieldOptions.filter(r=>r.visible===!0).map(r=>r.name);(t=c.value)!=null&&t.dataForm&&(c.value.dataForm.attrs=a)},x=e=>{var n,l,p;(n=s.view)==null||n.graphics.removeAll();const a=(e==null?void 0:e.vertices)||[],t=P(a,(l=s.view)==null?void 0:l.spatialReference),r=t&&ae({geometry:t,symbol:N("polygon")});r&&((p=s.view)==null||p.graphics.add(r))},I=(e,a)=>{var t;s.view&&(i.queryParams.layerIds=ne(s.view,!0),i.queryParams.geometry=a?P(e.vertices,(t=s.view)==null?void 0:t.spatialReference):e.mapPoint,i.queryParams.mapExtent=s.view.extent,s.view&&se(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,i.queryParams).then(r=>{var l,p;(l=s.view)==null||l.graphics.removeAll();const n=v.group[1].fields[0];n&&(n.data=((p=r.results)==null?void 0:p.map(o=>{var u,f;return{layerName:o.layerName,layerId:o.layerId,value:(u=o.feature.attributes)==null?void 0:u.OBJECTID,attributes:(f=o.feature)==null?void 0:f.attributes}}))||[],c.value&&(c.value.dataForm.name=n.data[0]),C(n.data[0]))}).catch(()=>{var r;(r=s.view)==null||r.graphics.removeAll()}))},E=e=>{i.drawer&&i.drawer.destroy(),i.drawer=B(e),i.graphicLayer=T(e,{id:"attr-annotation",title:"属性标注"}),i.mapClick=e.on("click",a=>{var r,n;(r=i.graphicLayer)==null||r.removeAll();const t=ie(a.mapPoint.x,a.mapPoint.y,{picUrl:me(),spatialReference:e==null?void 0:e.spatialReference,yOffset:8});(n=i.graphicLayer)==null||n.add(t),I(a)})},C=async e=>{var u,f,F,O,L,S,_;const{layerName:a,layerId:t,attributes:r}=e||{};if(!s.view)return;i.resultLayer=T(s.view,{id:"attr-annotation-result",title:"属性查询结果"});const l=((F=(f=(u=(await k(a)).data)==null?void 0:u.result)==null?void 0:f.rows)==null?void 0:F.filter(m=>m.visible===!0))||[],p=l.map(m=>m.name),o=await le(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+t,pe({where:"OBJECTID ="+((r==null?void 0:r.OBJECTID)||""),outFields:p}));if((O=o==null?void 0:o.features)!=null&&O.length){if(o.features[0].symbol=N(o.features[0].geometry.type),(L=i.resultLayer)==null||L.removeAll(),(S=i.resultLayer)==null||S.add(o.features[0]),o.features[0].geometry.type==="point")s.view.goTo(o.features[0].geometry);else{const m=o.features[0].geometry.extent,A=m.xmax-m.xmin,G=m.ymax-m.ymin,M=m.xmin-A/2,Q=m.xmax+A/2,J=m.ymin-G/2,U=m.ymax+G/2;(_=s.view)==null||_.goTo(new te({xmin:M,ymin:J,xmax:Q,ymax:U,spatialReference:s.view.spatialReference}))}g.data=o.features[0].attributes||[],g.columns=l.map(m=>[{label:m.alias,prop:m.name}])}},D=()=>{var e,a,t,r,n,l,p,o;(e=i.drawer)==null||e.destroy(),i.drawer=void 0,(a=i.drawAction)==null||a.destroy(),i.drawAction=void 0,(t=i.resultLayer)==null||t.removeAll(),i.resultLayer&&((r=s.view)==null||r.map.remove(i.resultLayer)),(n=i.graphicLayer)==null||n.removeAll(),(l=s.view)==null||l.graphics.removeAll(),i.graphicLayer&&((p=s.view)==null||p.map.remove(i.graphicLayer)),(o=i.mapClick)!=null&&o.remove&&i.mapClick.remove()},H=async()=>{var t,r,n,l;const e=(t=s.view)==null?void 0:t.map.findLayerById("pipelayer");d.pipeLayerOption=[],(r=e==null?void 0:e.sublayers)==null||r.map(p=>{var o;(o=d.pipeLayerOption)==null||o.push({label:p.title,value:p.title})});const a=v.group[3].fields[0];a&&(a.options=d.pipeLayerOption),(n=c.value)!=null&&n.dataForm&&(c.value.dataForm.layer=d.pipeLayerOption&&((l=d.pipeLayerOption[0])==null?void 0:l.value)),await b(),y()};return K(()=>{s.view&&E(s.view),H(),q("crosshair")}),W(()=>{D(),q("")}),(e,a)=>{const t=ee;return X(),Y(t,{ref_key:"refForm",ref:c,config:Z(v)},null,8,["config"])}}});export{Gt as default};
