<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.plan.SMCircuitPlanMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           sm_circuit_district_get_actual_top_root_id_by_area(district_area_id) as top_district_id,
                           district_area_id,
                           sm_circuit_district_area_get_name(district_area_id)                       as district_area_name,
                           is_normal_plan,
                           is_need_feedback,
                           move_type,
                           devices,
                           special_devices,
                           plan_circle,
                           remark,
                           inspection_config_id,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sm_circuit_plan -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlanResponse">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="top_district_id" property="topDistrictId"/>
        <result column="district_area_id" property="districtAreaId"/>
        <result column="district_area_name" property="districtAreaName"/>
        <result column="is_normal_plan" property="isNormalPlan"/>
        <result column="is_need_feedback" property="isNeedFeedback"/>
        <result column="move_type" property="moveType"/>
        <result column="devices" property="devices"/>
        <result column="special_devices" property="specialDevices"/>
        <result column="plan_circle" property="planCircle"/>
        <result column="remark" property="remark"/>
        <result column="inspection_config_id" property="inspectionConfigId"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_circuit_plan
        <where>
            <if test="keyword != null">
                and ("name" like '%' || #{keyword} || '%'
                or remark like '%' || #{keyword} || '%')
            </if>
            <if test="creator != null">
                and creator = #{creator}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sm_circuit_plan
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="districtAreaId != null">
                district_area_id = #{districtAreaId},
            </if>
            <if test="isNormalPlan != null">
                is_normal_plan = #{isNormalPlan},
            </if>
            <if test="isNeedFeedback != null">
                is_need_feedback = #{isNeedFeedback},
            </if>
            <if test="moveType != null">
                move_type = #{moveType},
            </if>
            <if test="devices != null">
                devices = #{devices},
            </if>
            <if test="specialDevices != null">
                special_devices = #{specialDevices},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="inspectionConfigId != null">
                inspection_config_id = #{inspectionConfigId},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="isPlanArranged" resultType="boolean">
        select count(1) > 0
        from sm_circuit_task
        where id = #{planId}
          and (begin_time between #{beginTime} and #{endTime} or end_time between #{beginTime} and #{endTime})
    </select>
</mapper>