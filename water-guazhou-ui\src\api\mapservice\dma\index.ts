export * from './dma';
export * from './hookupDevice';
export * from './minFlowConfig';
export * from './accountCycleConfig';
export * from './readMeterData';
export * from './userManage';
export * from './statistics';
export * from './useWaterCorrect';
export * from './lossControl';
export * from './statisticAnalys';
export const DMADirection = {
  1: '正进负出',
  2: '负进正出',
  3: '入口表',
  4: '出口表'
};

export const CopyMeterTypes = {
  1: '集抄系统',
  2: '非集抄系统',
  3: '集非混合'
};
/**
 * 抄表类型
 */
export const CopyMeterOptions = [
  { label: '集成系统', value: '1' },
  { label: '非集抄系统', value: '2' },
  { label: '集非混合', value: '3' }
];
export const CollectRates = [5, 15, 30, 60];
/**
 * 采集频率
 */
export const CollectRateOptions = [
  { label: '5', value: 5 },
  { label: '15', value: 15 },
  { label: '30', value: 30 },
  { label: '60', value: 60 }
];

export const UserTypes = {
  1: '商居混合',
  2: '居民区',
  3: '商业区'
};
/**
 * 用户类型
 */
export const UserTypeOptions = [
  { label: '商居混合', value: '1' },
  { label: '居民区', value: '2' },
  { label: '商业区', value: '3' }
];
export const ApartmentTypeOptions = [
  { label: '户改小区', value: '1' },
  { label: '商业小区', value: '2' },
  { label: '安置房', value: '3' }
];
export enum EDMATypes {
  JiLiangFenQu = '1',
  DMAFenQu = '2'
}
export const DMATypes = {
  1: '计量分区',
  2: 'DMA分区'
};
/**
 * 分区类型
 */
export const DMATypeOptions = [
  { label: '计量分区', value: '1' },
  { label: 'DMA分区', value: '2' }
];
export enum EDMAStatus {
  GuiHuaZhong = '1',
  JianZhiZhong = '2',
  PingGuZhong = '3',
  JianXiuLou = '4',
  YingYunZhong = '5'
}
/**
 * 分区状态
 */
export const DMAStatusOptions = [
  { label: '规则中', value: EDMAStatus.GuiHuaZhong },
  { label: '建制中', value: EDMAStatus.JianZhiZhong },
  { label: '评估中', value: EDMAStatus.PingGuZhong },
  { label: '检修漏', value: EDMAStatus.JianXiuLou },
  { label: '营运中', value: EDMAStatus.YingYunZhong }
];
export const DMAStatuss = () => {
  const obj = {};
  DMAStatusOptions.map((item) => {
    obj[item.value] = item.label;
  });
  return obj;
};
/**
 * 是否有机械表
 */
export const IsMachineMeterOptions = [
  { label: '是', value: true },
  { label: '否', value: false }
];
export const TimeOptions = Array.from({ length: 24 }).map((item, i) => {
  return {
    label: i + '点',
    value: i
  };
});
/**
 * DMA设备类型
 */
export enum EDeviceTypes {
  /**
   * 流量计
   */
  FLOW = '1',
  /**
   * 压力计
   */
  PRESSURE = '2',
  /**
   * 大用户
   */
  BIGUSER = '3'
}
/**
 * DMA设备类型
 */
export const DeviceTypes = {
  1: '流量计',
  2: '压力计',
  3: '大用户'
};
