<template>
  <table class="attr-table" :class="props.theme">
    <tbody>
      <template v-if="attributes?.length">
        <tr
          v-for="(attr, i) in attributes"
          :key="i"
          :class="{ link: props.link, notLink: attr.notLink }"
          @click="() => handleRowClick && handleRowClick(attr)"
        >
          <td class="name">
            <div class="cell">
              <span>{{ attr.label }}</span>
            </div>
          </td>
          <td
            class="value width100"
            :class="theme || (appStore.isDark ? 'isDark' : '')"
          >
            <div class="cell">
              <span>{{ attr.value }}</span>
            </div>
          </td>
        </tr>
      </template>
      <template v-if="rows?.length">
        <tr
          v-for="(row, i) in rows"
          :key="i"
          :class="props.link ? 'link' : ''"
          @click="() => handleRowClick && handleRowClick(row)"
        >
          <template v-for="(td, j) in row" :key="j">
            <td class="name">
              <div class="cell">
                <span>{{ td.label }}</span>
              </div>
            </td>
            <td
              class="value"
              :class="appStore.isDark ? 'isDark' : ''"
              :colspan="td.cols"
            >
              <div class="cell">
                <span>{{ td.value }}</span>
              </div>
            </td>
          </template>
        </tr>
      </template>
      <template v-if="columns?.length">
        <tr
          v-for="(row, i) in columns"
          :key="i"
          :class="{
            link: props.link
          }"
          @click="() => handleRowClick && handleRowClick(row)"
        >
          <template v-for="(td, j) in row" :key="j">
            <td
              class="name namewidth"
              :class="{
                notLink: td.notLink
              }"
            >
              <div class="cell">
                <span>{{ td.label }}</span>
              </div>
            </td>
            <td
              class="value"
              :class="appStore.isDark ? 'isDark' : ''"
              :colspan="td.cols"
            >
              <div class="cell">
                <AttrTableCellContent
                  :config="td"
                  :row="data"
                ></AttrTableCellContent>
              </div>
            </td>
          </template>
        </tr>
      </template>
      <template v-if="!attributes?.length && !rows?.length && !columns?.length">
        <div class="empty">暂无内容</div>
      </template>
    </tbody>
  </table>
</template>
<script lang="ts" setup>
import { useAppStore } from '@/store';

const appStore = useAppStore()
const emit = defineEmits<{ (e: 'row-click', row: any, data?: any) }>();
const props = defineProps<{
  attributes?: {
    label: string;
    value: any;
    data?: any;
    notLink?: boolean;
  }[];
  link?: boolean;
  rows?: IAttrTableRow[][];
  data?: any;
  columns?: IAttrTableRow[][];
  theme?: ITheme;
}>();
const handleRowClick = (row) => {
  emit('row-click', row, props.data);
};
</script>
<style lang="scss" scoped>
.attr-table {
  border-collapse: collapse;
  width: 100%;
  font-size: 14px;
  --border-color: var(--el-border-color-darker);
  --label-color: #9097c0;
  --value-color: var(--el-text-color-regular);
  &.darkblue {
    --border-color: var(--label-color);
    --value-color: #fff;
  }
  &.dark {
    --border-color: var(--label-color);
    --value-color: #fff;
  }
  tr {
    &.link {
      cursor: pointer;
      &.notLink {
        cursor: default;
      }
    }
    td {
      padding: 8px 12px;
      border: 1px solid var(--border-color);

      position: relative;
      min-width: 0;
      &.notLink {
        cursor: default;
      }
      .cell {
        min-width: 50px;
        white-space: nowrap;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        line-height: 23px;
      }
      &.value {
        color: var(--value-color);
        &.width100 {
          width: 100%;
        }
      }
      &.name {
        color: var(--label-color);
      }
    }
  }
}
.namewidth {
  width: 120px;
}
.empty {
  font-size: 14px;
  text-align: center;
}
.dark {
  .attr-table {
    tr {
      td {
        &.value {
          background-color: #1a1d2d;
          color: #fff;
        }
      }
    }
  }
}
</style>
