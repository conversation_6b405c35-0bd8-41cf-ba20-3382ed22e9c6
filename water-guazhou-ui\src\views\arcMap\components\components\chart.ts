import { useAppStore } from '@/store';
import { hexToRgba, transNumberUnit } from '@/utils/GlobalHelper';
import * as echarts from 'echarts';
// 饼图
export const ring = (
  data: {
    name: string;
    nameAlias?: string;
    value: string;
    valueAlias?: string;
    scale: string;
  }[] = [],
  unit?: string,
  prefix?: string,
  percision = 2
) => {
  const title = '总数';
  const formatNumber = function (num) {
    const reg = /(?=(\B)(\d{3})+$)/g;
    return num.toString().replace(reg, ',');
  };
  const total = data.reduce((a, b: any) => {
    return a + (parseFloat(b.value) || 0) * 1;
  }, 0);
  const transedTotal = transNumberUnit(total);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return (prefix || '') + params.name + ': ' + Number(params.value).toFixed(percision) + ' ' + unit;
      }
    },
    legend: {
      // selectedMode: false, // 取消图例上的点击事件
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      left: 'right',
      top: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      symbolKeepAspect: true,
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 190,
            fontSize: 12,
            color: useAppStore().isDark ? '#fff' : '#2A2A2A'
          },
          value: {
            align: 'left',
            width: 80,
            fontSize: 12,
            color: '#00ff00'
          },
          count: {
            align: 'left',
            width: 70,
            fontSize: 12
          },
          upRate: {
            align: 'left',
            fontSize: 12
          },
          downRate: {
            align: 'left',
            fontSize: 12,
            color: '#409EFF'
          }
        }
      },
      data: data.map((item) => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            const scale = data[i].scale?.substring(0, data[i].scale.length - 1);
            if (name === data[i].name) {
              return (
                '{name| '
                + (data[i].nameAlias || name)
                + '}'
                + '{value| '
                + (data[i].valueAlias || data[i].value)
                + ' '
                + (unit || '')
                + '}'
                + '{downRate| '
                + (formatNumber(Number(scale || '0').toFixed(percision)) + '%' || '')
                + '}'
              );
            }
          }
        }
      }
    },
    title: [
      {
        text:
          '{name|'
          + title
          + ((unit && '(' + transedTotal.unit + unit + ')') || '(' + transedTotal.unit + ')')
          + '}\n{val|'
          + formatNumber(transedTotal.value.toFixed(percision))
          + '}',
        top: 'center',
        left: '19%',
        textAlign: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 10,
              fontWeight: 'normal',
              padding: [8, 0],
              align: 'center',
              color: useAppStore().isDark ? '#fff' : '#2A2A2A'
            },
            val: {
              fontSize: 16,
              fontWeight: 'bold',
              color: useAppStore().isDark ? '#fff' : '#2A2A2A'
            }
          }
        }
      }
    ],
    series: [
      {
        type: 'pie',
        radius: ['45%', '60%'],
        center: ['20%', '50%'],
        data,
        hoverAnimation: true,
        label: {
          show: false,
          formatter: (params) => {
            return (
              '{icon|●}{name|'
              + params.name
              + '}{value|'
              + formatNumber(Number(params.value || '0').toFixed(percision))
              + '}'
            );
          },
          padding: [0, -100, 25, -100],
          rich: {
            icon: {
              fontSize: 16
            },
            name: {
              fontSize: 14,
              padding: [0, 10, 0, 4]
            },
            value: {
              fontSize: 18,
              fontWeight: 'bold'
            }
          }
        }
      }
    ]
  };
  return option;
};

// 折线图
export const hourlyLine = (config?: {
  line1: {
    data?: any[];
    unit?: string;
    name?: string;
    yAxisIndex?: number;
  };
  line2?: {
    data?: any[];
    unit?: string;
    name?: string;
    yAxisIndex?: number;
  };
  line3?: {
    data?: any[];
    unit?: string;
    name?: string;
    yAxisIndex?: number;
  };
}) => {
  const option = {
    title: {
      text: '24小时运行曲线',
      left: '30',
      textStyle: {
        color: '#fff'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: 70,
      right: 90,
      bottom: 30,
      top: 70
    },
    legend: {
      textStyle: {
        color: useAppStore().isDark ? '#fff' : '#7C8295'
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [
        '00',
        '01',
        '02',
        '03',
        '04',
        '05',
        '06',
        '07',
        '08',
        '09',
        '10',
        '11',
        '12',
        '13',
        '14',
        '15',
        '16',
        '17',
        '18',
        '19',
        '20',
        '21',
        '22',
        '23'
      ]
    },
    yAxis: [
      {
        name: (config?.line1.name || '') + (config?.line1.unit ? '(' + (config?.line1.unit || '') + ')' : ''),
        type: 'value',
        // axisLabel: {
        //   formatter: '{value} °C'
        // },
        splitLine: {
          lineStyle: {
            color: '#ffffff',
            opacity: 0.2,
            type: 'dashed'
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666'
          }
        },
        position: 'left'
      },
      ...[
        config?.line2
          ? {
            name: (config?.line2?.name || '') + (config?.line2?.unit ? '(' + (config?.line2.unit || '') + ')' : ''),
            type: 'value',
            // axisLabel: {
            //   formatter: '{value} °C'
            // },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#ffffff',
                opacity: 0.2,
                type: 'dashed'
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#666'
              }
            },
            alignTicks: true
          }
          : undefined
      ].filter((item) => !!item),
      ...[
        config?.line3
          ? {
            name: (config?.line3?.name || '') + (config?.line3?.unit ? '(' + (config?.line3.unit || '') + ')' : ''),
            type: 'value',
            // axisLabel: {
            //   formatter: '{value} °C'
            // },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#ffffff',
                opacity: 0.2,
                type: 'dashed'
              }
            },
            position: 'right',
            alignTicks: true,
            offset: 60,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#666'
              }
            }
          }
          : undefined
      ].filter((item) => !!item)
    ],
    series: [
      {
        name: config?.line1.name || '',
        type: 'line',
        data: config?.line1.data || [],
        markPoint: {
          data: [
            { type: 'max', name: 'Max' },
            { type: 'min', name: 'Min' }
          ]
        }
      },
      ...[
        config?.line2
          ? {
            yAxisIndex: config?.line2?.yAxisIndex ?? 1,
            name: config?.line2?.name || '',
            type: 'line',
            data: config?.line2?.data || [],
            markPoint: {
              data: [
                { type: 'max', name: 'Max' },
                { type: 'min', name: 'Min' }
              ]
            }
          }
          : {}
      ],
      ...[
        config?.line3
          ? {
            yAxisIndex: config?.line3?.yAxisIndex ?? 2,
            name: config?.line3?.name || '',
            type: 'line',
            data: config?.line3?.data || [],
            markPoint: {
              data: [
                { type: 'max', name: 'Max' },
                { type: 'min', name: 'Min' }
              ]
            }
          }
          : {}
      ]
    ]
  };
  return option;
};

// 堆叠柱状图
export function StackedPolylines(unit?: string) {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      left: 'right',
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: 50,
      top: 30,
      right: 30,
      bottom: 30
    },
    xAxis: [
      {
        type: 'category',
        data: ['2016', '2017', '2018', '2019', '2020', '2021', '2022']
      }
    ],
    yAxis: [
      {
        name: unit,
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#ffffff',
            opacity: 0.2,
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        name: '原始',
        type: 'bar',
        stack: 'Ad',
        emphasis: {
          focus: 'series'
        },
        data: [120, 132, 101, 134, 90, 230, 210]
      },
      {
        name: '改造',
        type: 'bar',
        stack: 'Ad',
        emphasis: {
          focus: 'series'
        },
        data: [10, 12, 11, 24, 20, 30, 10]
      },
      {
        name: '新增',
        type: 'bar',
        stack: 'Ad',
        emphasis: {
          focus: 'series'
        },
        data: [150, 232, 201, 154, 190, 330, 410]
      }
    ]
  };
  return option;
}

// 三仪表盘
export function dashboard() {
  const option = {
    backgroundColor: 'rgba(255,255,255,0)',
    tooltip: {
      formatter: '{a} <br/>{c} {b}'
    },
    toolbox: {
      show: true,
      feature: {
        restore: { show: true },
        saveAsImage: { show: true }
      }
    },
    series: [
      {
        name: '计划项目建成投产率',
        type: 'gauge',
        z: 3,
        min: 0,
        max: 100,
        splitNumber: 10,
        radius: '100%',
        axisLine: {
          // 坐标轴线
          lineStyle: {
            // 属性lineStyle控制线条样式
            width: 10,
            color: [
              [0.3, '#67e0e3'],
              [0.7, '#37a2da'],
              [1, '#fd666d']
            ]
          }
        },
        axisTick: {
          // 坐标轴小标记
          length: 15, // 属性length控制线长
          lineStyle: {
            // 属性lineStyle控制线条样式
            color: 'auto'
          }
        },
        splitLine: {
          // 分隔线
          length: 20, // 属性length控制线长
          color: '#232323'
        },
        title: {
          textStyle: {
            // 其余属性默认使用全局文本样式，详见TEXTSTYLE
            fontWeight: 'bolder',
            fontSize: 20,
            fontStyle: 'italic'
          }
        },
        detail: {
          textStyle: {
            // 其余属性默认使用全局文本样式，详见TEXTSTYLE
            fontWeight: 'bolder'
          }
        },
        data: [{ value: 40, name: '计划项目投产率（%）' }]
      },
      {
        name: '计划施工项目数（个）',
        type: 'gauge',
        center: ['22%', '55%'], // 默认全局居中
        radius: '65%',
        min: 0,
        max: 8000,
        endAngle: 45,
        splitNumber: 8,
        axisLine: {
          // 坐标轴线
          lineStyle: {
            // 属性lineStyle控制线条样式
            width: 8,
            color: [
              [0.3, '#67e0e3'],
              [0.7, '#37a2da'],
              [1, '#fd666d']
            ]
          }
        },
        axisTick: {
          // 坐标轴小标记
          length: 12, // 属性length控制线长
          lineStyle: {
            // 属性lineStyle控制线条样式
            color: 'auto'
          }
        },
        splitLine: {
          // 分隔线
          length: 20, // 属性length控制线长
          lineStyle: {
            // 属性lineStyle（详见lineStyle）控制线条样式
            color: 'auto'
          }
        },
        pointer: {
          width: 5
        },
        title: {
          offsetCenter: [0, '-30%'] // x, y，单位px
        },
        detail: {
          textStyle: {
            // 其余属性默认使用全局文本样式，详见TEXTSTYLE
            fontWeight: 'bolder'
          }
        },
        data: [{ value: 6458, name: '个' }]
      },
      {
        name: '计划项目投产个数（个）',
        type: 'gauge',
        center: ['79%', '55%'], // 默认全局居中
        radius: '65%',
        min: 0,
        max: 8000,
        startAngle: 140,
        endAngle: -45,
        splitNumber: 8,
        axisLine: {
          // 坐标轴线
          lineStyle: {
            // 属性lineStyle控制线条样式
            width: 8,
            color: [
              [0.3, '#67e0e3'],
              [0.7, '#37a2da'],
              [1, '#fd666d']
            ]
          }
        },
        axisTick: {
          // 坐标轴小标记
          length: 12, // 属性length控制线长
          lineStyle: {
            // 属性lineStyle控制线条样式
            color: 'auto'
          }
        },
        splitLine: {
          // 分隔线
          length: 20, // 属性length控制线长
          lineStyle: {
            // 属性lineStyle（详见lineStyle）控制线条样式
            color: 'auto'
          }
        },
        pointer: {
          width: 5
        },
        title: {
          offsetCenter: [0, '-30%'] // x, y，单位px
        },
        detail: {
          textStyle: {
            // 其余属性默认使用全局文本样式，详见TEXTSTYLE
            fontWeight: 'bolder'
          }
        },
        data: [{ value: 2583, name: '个' }]
      }
    ]
  };

  setInterval(() => {
    option.series[0].data[0].value = parseFloat((Math.random() * 100).toFixed(2));
    option.series[1].data[0].value = parseFloat((Math.random() * 7).toFixed(2));
    option.series[2].data[0].value = parseFloat((Math.random() * 2).toFixed(2));
  }, 2000);
  return option;
}

// 单仪表盘
export function onedashboard() {
  const option = {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%'
    },
    toolbox: {
      feature: {
        restore: {},
        saveAsImage: {}
      }
    },
    series: [
      {
        name: '业务指标',
        type: 'gauge',
        detail: {
          formatter: '{value}%'
        },
        data: [
          {
            value: 50,
            name: '完成率'
          }
        ],
        axisLine: {
          show: true,
          lineStyle: {
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0.1,
                    color: '#FFC600'
                  },
                  {
                    offset: 0.6,
                    color: '#30D27C'
                  },
                  {
                    offset: 1,
                    color: '#0B95FF'
                  }
                ])
              ]
            ]
          }
        }
      }
    ]
  };

  setInterval(() => {
    option.series[0].data[0].value = parseFloat((Math.random() * 100).toFixed(2));
  }, 2000);
  return option;
}

export function areaChart(
  xData: string[],
  Data: number[],
  options: {
    name?: string;
    unit?: string;
    color1?: string;
    color2?: string;
  }
) {
  const option = {
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData
    },
    tooltip: {
      show: true,
      trigger: 'axis'
    },
    yAxis: {
      type: 'value',
      name: (options.name || '') + (options.unit ? '(' + options.unit + ')' : '')
    },
    series: [
      {
        data: Data,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgba(options.color1 || '#63a4b9', 0.3) // 0% 处的颜色
              },
              {
                offset: 1,
                color: hexToRgba(options.color2 || options.color1 || '#63a4b9', 0.1) // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          }
        }
      }
    ]
  };
  return option;
}

// 柱状图横向
export function horizontalHistogram(title = '', label: string[] = [], data: number[] = []) {
  const option = {
    title: {
      text: title,
      textStyle: {
        color: useAppStore().isDark ? '#aaa' : '#333'
      }
    },
    grid: {
      left: 10,
      right: 40,
      bottom: 10,
      top: 30,
      containLabel: true
    },
    xAxis: [
      {
        splitLine: {
          show: false
        },
        type: 'value',
        show: false
      }
    ],
    yAxis: [
      {
        splitLine: {
          show: false
        },
        axisLine: {
          // y轴
          show: false
        },
        type: 'category',
        axisTick: {
          show: false
        },
        data: label,
        axisLabel: {}
      }
    ],
    series: [
      {
        name: '标准化',
        type: 'bar',
        barWidth: 10, // 柱子宽度
        label: {
          show: true,
          position: 'right', // 位置
          color: '#1CD8A8',
          fontSize: 14,
          fontWeight: 'bold', // 加粗
          distance: 5 // 距离
        }, // 柱子上方的数值
        itemStyle: {
          barBorderRadius: [20, 20, 20, 20], // 圆角（左上、右上、右下、左下）
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            ['#2FAEF2', '#1CD8A8'].map((color, offset) => ({
              color,
              offset
            }))
          ) // 渐变
        },
        data
      }
    ]
  };
  return option;
}

// 渐变单柱状图
export function oneHistogram(
  data: {
    name: string;
    nameAlias?: string;
    value: string;
    valueAlias?: string;
  }[] = [],
  unit?: string,
  color = [
    { offset: 1, color: '#83bff6' },
    { offset: 0, color: '#188df0' }
  ],
  name = '管长'
) {
  const xData = data.map((item) => item.nameAlias || item.name);
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: 50,
      top: 30,
      right: 30,
      bottom: 20
    },
    xAxis: {
      type: 'category',
      data: xData
    },
    yAxis: {
      name: unit,
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#ffffff',
          opacity: 0.2,
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name,
        type: 'bar',
        barWidth: 10,

        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 1, 0, color)
        },

        data,
        label: {
          show: true,
          position: 'top'
        }
      }
    ]
  };
  return option;
}

// 渐变圆柱
export function cylinder(
  data = [
    {
      name: '常规',
      value: 175.17
    },
    {
      name: '紧急',
      value: 148.35
    },
    {
      name: '疑难',
      value: 95.36
    }
  ]
) {
  const xAxisData = [] as any[];
  const seriesData1 = [] as any[];
  const barTopColor = '#02c3f1';
  const barBottomColor = 'rgba(2,195,241,0.1)';
  data.forEach((item) => {
    xAxisData.push(item.name);
    seriesData1.push(item.value);
    // sum += item.value
  });
  const option = {
    title: {
      text: '多色立体圆柱',
      top: 20,
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 20
      }
    },
    grid: {
      top: '25%',
      bottom: '15%'
    },
    xAxis: {
      data: xAxisData
    },
    yAxis: {},
    series: [
      {
        name: '柱顶部',
        type: 'pictorialBar',
        symbolSize: [26, 10],
        symbolOffset: [0, -5],
        z: 12,
        itemStyle: {
          normal: {
            color() {
              return barTopColor;
            }
          }
        },
        label: {
          show: true,
          position: 'top',
          fontSize: 16
        },
        symbolPosition: 'end',
        data: seriesData1
      },
      {
        name: '柱底部',
        type: 'pictorialBar',
        symbolSize: [26, 10],
        symbolOffset: [0, 5],
        z: 12,
        itemStyle: {
          normal: {
            color() {
              return barTopColor;
            }
          }
        },
        data: seriesData1
      },
      {
        name: '第一圈',
        type: 'pictorialBar',
        symbolSize: [47, 16],
        symbolOffset: [0, 11],
        z: 11,
        itemStyle: {
          normal: {
            color: 'transparent',
            borderColor: '#3ACDC5',
            borderWidth: 2
          }
        },
        data: seriesData1
      },
      {
        name: '第二圈',
        type: 'pictorialBar',
        symbolSize: [62, 22],
        symbolOffset: [0, 17],
        z: 10,
        itemStyle: {
          normal: {
            color: 'transparent',
            borderColor: barTopColor,
            borderWidth: 2
          }
        },
        data: seriesData1
      },
      {
        type: 'bar',
        itemStyle: {
          normal: {
            color() {
              return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 1,
                  color: barTopColor
                },
                {
                  offset: 0,
                  color: barBottomColor
                }
              ]);
            },
            opacity: 0.8
          }
        },
        z: 16,
        silent: true,
        barWidth: 26,
        barGap: '-100%', // Make series be overlap
        data: seriesData1
      }
    ]
  };
  return option;
}

/**
 * 渐变环图
 * @param title 标题
 * @param color 颜色
 * @param data 值
 * @returns
 */
export const linearRingPieOption = (title: string, color: string, data: any, unit?: string) => {
  const option = {
    color: [
      new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color
        },
        {
          offset: 1,
          color: 'rgba(21, 45, 68,0.5)'
        }
      ]),
      'rgba(21, 45, 68,0.5)'
    ],
    title: {
      text: title,
      bottom: 0,
      left: 'center',
      textStyle: {
        color: 'rgb(137, 168, 195)',
        fontSize: 13
      }
    },
    series: [
      {
        startAngle: -60,
        name: 'Access From',
        type: 'pie',
        radius: ['62%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'center',
          color: '#ffffff',
          formatter() {
            return (data || '--') + '\n' + (unit || '');
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '12',
            fontWeight: 'bold'
          }
        },
        data: [
          { value: 10, name: '' },
          { value: 4, name: '' }
        ]
      }
    ]
  };
  return option;
};
