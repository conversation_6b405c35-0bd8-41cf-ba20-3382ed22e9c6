import{d as J,c as T,r as $,am as E,bv as K,g as a,h as u,F as V,n as t,aJ as F,aB as r,av as b,p as L,bh as M,aw as z,i as d,an as p,bg as x,ax as A,bw as G,bx as X,by as Y,bt as Z,aq as j,bz as ee,I as ae,aH as ne,K as le}from"./index-r0dFAfgr.js";const oe={class:"title"},te={key:0,class:"right-wrapper"},ie=J({__name:"InlineForm",props:{config:{}},setup(R,{expose:O}){const m=R,q=T(),n=$({queryParams:{...m.config.defaultQuerys||{}},dataForm:{...m.config.defaultValue||{}}}),B=(o,c,i)=>{c.onChange&&c.onChange(o,c,i)},U=()=>{m.config.group.map(o=>{var c,i;o.handleHidden&&o.handleHidden(n.dataForm,n.queryParams,o),o.fields.map(f=>{f.handleHidden&&f.handleHidden(n.dataForm,n.queryParams,f)}),(i=(c=o.fieldset)==null?void 0:c.right)==null||i.map(f=>{f.items.map(y=>{y.handleHidden&&y.handleHidden(n.dataForm,n.queryParams,y)})})})};return E(()=>n.dataForm,()=>{m.config.static||U()},{deep:!0,immediate:!0}),E(()=>n.queryParams,()=>{m.config.static||U()}),O({resetForm:()=>{var o;n.dataForm={...m.config.defaultValue||{}},(o=q.value)==null||o.resetFields()},Submit:async()=>{var o,c;try{if(!await((o=q.value)==null?void 0:o.validate()))return!1;const f={},y=X({...n.dataForm});return m.config.group.map(P=>(P.fields.map(_=>(_.aInfo&&_.field&&(f[_.field]=y[_.field],delete y[_.field]),_)),P)),Object.values(f).length>0&&(y.additionalInfo=JSON.stringify(f)),m.config.submit&&await m.config.submit(y)}catch(i){const f=Object.keys(i);return(c=q.value)==null||c.scrollToField(f[0]),!1}},...K(n)}),(o,c)=>{const i=Y,f=Z,y=j,Q=ee,P=ae,_=ne,W=le;return a(),u(W,{ref_key:"refForm",ref:q,inline:!0,class:"inline-form",model:d(n).dataForm,size:o.config.size||"default","label-position":o.config.labelPosition||"left","label-width":o.config.labelWidth||"100px","validate-on-rule-change":!1,onSubmit:c[0]||(c[0]=G(()=>{},["prevent"]))},{default:V(()=>[(a(!0),t(r,null,F(o.config.group,(l,D)=>{var H,I,N;return a(),t(r,{key:D},[l.hidden?p("",!0):(a(),t("div",{key:0,style:b(l.styles),class:"inline-group"},[l.fieldset?(a(),u(f,{key:0,type:(H=l.fieldset)==null?void 0:H.type,style:b("width:"+(l.fieldset.width||"100%"))},{default:V(()=>{var e,C;return[L("span",oe,M((e=l.fieldset)==null?void 0:e.desc),1),(C=l.fieldset.right)!=null&&C.length?(a(!0),t(r,{key:0},F(l.fieldset.right,(w,s)=>(a(),t("div",{key:s,class:z(w.className)},[(a(!0),t(r,null,F(w.items,(h,S)=>(a(),t(r,{key:S},[h.hidden?p("",!0):(a(),t(r,{key:0},[h.field?(a(),u(i,{key:0,modelValue:d(n).queryParams[h.field],"onUpdate:modelValue":g=>d(n).queryParams[h.field]=g,config:h,onChange:g=>{var k;return((k=l.fieldset)==null?void 0:k.handleQuery)&&l.fieldset.handleQuery(g,d(n).queryParams,h,l)}},null,8,["modelValue","onUpdate:modelValue","config","onChange"])):(a(),u(i,{key:1,config:h,onChange:g=>{var k;return((k=l.fieldset)==null?void 0:k.handleQuery)&&l.fieldset.handleQuery(g,d(n).queryParams,h,l)}},null,8,["config","onChange"]))],64))],64))),128))],2))),128)):p("",!0)]}),_:2},1032,["type","style"])):p("",!0),(a(!0),t(r,null,F(l.fields,(e,C)=>{var w;return a(),t(r,{key:C},[e.hidden?p("",!0):(a(),t(r,{key:0},[e.type==="card-table"?(a(),u(Q,{key:0,style:b(e.itemContainerStyle||"width:100%;")},x({default:V(()=>[e.field?(a(),u(y,{key:0,modelValue:d(n).dataForm[e.field],"onUpdate:modelValue":s=>d(n).dataForm[e.field]=s,config:e.config,onChange:s=>B(s,e,l)},null,8,["modelValue","onUpdate:modelValue","config","onChange"])):p("",!0)]),_:2},[e.title||(w=e.titleRight)!=null&&w.length?{name:"header",fn:V(()=>{var s;return[(s=e.titleRight)!=null&&s.length?(a(),t("div",te,[(a(!0),t(r,null,F(e.titleRight,(h,S)=>(a(),t("div",{key:S,class:z(h.className)},[(a(!0),t(r,null,F(h.items,(g,k)=>(a(),t(r,{key:k},[g.hidden?p("",!0):(a(),t(r,{key:0},[g.field?(a(),u(i,{key:0,modelValue:d(n).queryParams[g.field],"onUpdate:modelValue":v=>d(n).queryParams[g.field]=v,config:g,onChange:v=>e.handleQuery&&e.handleQuery(v,e,l)},null,8,["modelValue","onUpdate:modelValue","config","onChange"])):(a(),u(i,{key:1,config:e,onChange:v=>e.handleQuery&&e.handleQuery(v,e,l)},null,8,["config","onChange"]))],64))],64))),128))],2))),128))])):p("",!0)]}),key:"0"}:void 0]),1032,["style"])):e.type==="table"?(a(),t(r,{key:1},[e.field?(a(),u(y,{key:0,modelValue:d(n).dataForm[e.field],"onUpdate:modelValue":s=>d(n).dataForm[e.field]=s,style:b(e.itemContainerStyle||"width:100%;"),config:e.config,onChange:s=>B(s,e,l)},null,8,["modelValue","onUpdate:modelValue","style","config","onChange"])):(a(),u(y,{key:1,style:b(e.itemContainerStyle||"width:100%;"),config:e.config},null,8,["style","config"]))],64)):(a(),u(P,{key:2,style:b(e.itemContainerStyle),class:z(e.className),label:e.label,prop:e.field,"label-width":e.labelWidth||o.config.labelWidth,rules:e.type!=="text"?e.rules:[]},{default:V(()=>[e.field?(a(),u(i,{key:0,modelValue:d(n).dataForm[e.field],"onUpdate:modelValue":s=>d(n).dataForm[e.field]=s,config:e,row:d(n).dataForm,size:o.config.size,onChange:s=>B(s,e,l)},{default:V(s=>[A(o.$slots,"fieldSlot",{config:s.config,row:d(n).dataForm})]),_:2},1032,["modelValue","onUpdate:modelValue","config","row","size","onChange"])):(a(),u(i,{key:1,config:e,row:d(n).dataForm,size:o.config.size},null,8,["config","row","size"]))]),_:2},1032,["style","class","label","prop","label-width","rules"]))],64))],64)}),128)),(I=l.groupBtns)!=null&&I.btns.length?(a(),t("div",{key:1,style:b(l.groupBtns.styles),class:z(l.groupBtns.className)},[(a(!0),t(r,null,F((N=l.groupBtns)==null?void 0:N.btns,(e,C)=>(a(),u(_,{key:C,config:e},null,8,["config"]))),128))],6)):p("",!0)],4))],64)}),128))]),_:3},8,["model","size","label-position","label-width"])}}});export{ie as _};
