package org.thingsboard.server.dao.util.imodel.response.exception.chain.handlers;

import org.thingsboard.server.dao.util.imodel.response.exception.CustomFailureException;
import org.thingsboard.server.dao.util.imodel.response.handler.CustomFailureHandler;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;
import org.thingsboard.server.dao.util.imodel.response.exception.chain.ExceptionHandler;
import org.thingsboard.server.dao.util.imodel.response.exception.chain.ExceptionResolvingChain;

public class CustomFailureExceptionHandler implements ExceptionHandler<CustomFailureException> {

    @Override
    public boolean canHandle(Throwable e) {
        return e instanceof CustomFailureException;
    }

    @Override
    public void handle(ExceptionResolvingChain chain, ReturnHelper wrap, CustomFailureException e) throws Throwable {
        try {
            CustomFailureHandler handler = wrap.getModel().failureCallback();
            if (handler != null)
                handler.invoke(e);
        } catch (CustomFailureException ignore) {

        } catch (Throwable ex) {
            chain.resolve(wrap, ex);
        }
    }
}
