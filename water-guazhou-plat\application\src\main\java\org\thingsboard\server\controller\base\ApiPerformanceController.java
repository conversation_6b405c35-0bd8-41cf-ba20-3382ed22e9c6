package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.model.sql.base.ApiPerformance;
import org.thingsboard.server.dao.optionLog.IApiPerformanceService;
import org.thingsboard.server.dao.util.imodel.query.base.ApiPerformancePageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

/**
 * 公共平台-服务监控Controller
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Api(tags = "平台管理-服务监控")
@RestController
@RequestMapping("api/base/performance")
public class ApiPerformanceController extends BaseController {

    @Autowired
    private IApiPerformanceService apiPerformanceService;

    /**
     * 查询公共平台-服务监控列表
     */
    @ApiOperation(value = "查询服务监控列表")
    @GetMapping("/list")
    public IstarResponse list(ApiPerformancePageRequest apiPerformance) {
        return IstarResponse.ok(apiPerformanceService.selectApiPerformanceList(apiPerformance));
    }

    /**
     * 获取公共平台-服务监控详细信息
     */
    @ApiOperation(value = "获取服务监控详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(apiPerformanceService.selectApiPerformanceById(id));
    }

    /**
     * 新增公共平台-服务监控
     */
    @ApiOperation(value = "新增服务监控")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody ApiPerformance apiPerformance) {
        return IstarResponse.ok(apiPerformanceService.insertApiPerformance(apiPerformance));
    }

    /**
     * 修改公共平台-服务监控
     */
    @ApiOperation(value = "修改服务监控")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody ApiPerformance apiPerformance) {
        return IstarResponse.ok(apiPerformanceService.updateApiPerformance(apiPerformance));
    }

    /**
     * 删除公共平台-服务监控
     */
    @ApiOperation(value = "删除服务监控")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(apiPerformanceService.deleteApiPerformanceByIds(ids));
    }
}
