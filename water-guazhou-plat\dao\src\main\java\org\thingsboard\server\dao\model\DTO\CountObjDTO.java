package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class CountObjDTO {

    private String key;

    private Integer count;

    public CountObjDTO(String key, Long count) {
        this.key = key;
        this.count = BigDecimal.valueOf(count).intValue();
    }

    public CountObjDTO(String key, Integer count) {
        this.key = key;
        this.count = count;
    }
}
