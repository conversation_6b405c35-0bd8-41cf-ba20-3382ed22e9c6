package org.thingsboard.server.dao.util;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 站点数据工具类
 */
public class StationDataUtil {

    /**
     * data map转换为数组
     *
     * @param dataMap 数据map
     * @return 数组数据
     */
    public static List<JSONObject> dataMapToList(LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap) {
        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> flowDataMap = entry.getValue();
            if (flowDataMap != null) {
                JSONObject dayFlow = new JSONObject();
                dayFlow.put("ts", key);
                BigDecimal flow = new BigDecimal("0");
                for (Map.Entry<String, BigDecimal> dataEntry : flowDataMap.entrySet()) {
                    if (dataEntry.getValue() != null) {
                        flow = flow.add(dataEntry.getValue());
                    }
                }
                dayFlow.put("value", flow);
                resultList.add(dayFlow);
            }
        }

        return resultList;
    }

    /**
     * data map转换为数组
     *
     * @param dataMap 数据map
     * @return 数组数据
     */
    public static List<JSONObject> dataMapToShortenTimeKeyList(LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap, String queryType) {
        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> flowDataMap = entry.getValue();
            if (flowDataMap != null) {
                JSONObject dayFlow = new JSONObject();
                dayFlow.put("ts", shortenTimeKey(key, queryType));
                BigDecimal flow = null;
                for (Map.Entry<String, BigDecimal> dataEntry : flowDataMap.entrySet()) {
                    if (dataEntry.getValue() != null) {
                        if (flow == null) {
                            flow = new BigDecimal("0");
                        }
                        flow = flow.add(dataEntry.getValue());
                    }
                }
                dayFlow.put("value", flow);
                resultList.add(dayFlow);
            }
        }

        return resultList;
    }

    /**
     * 缩短时间字符串
     *
     * @param time      时间字符串
     * @param queryType 查询数据间隔
     * @return 缩短后的时间字符串
     */
    public static String shortenTimeKey(String time, String queryType) {
        switch (queryType) {
            case "1m":
            case "5m":
            case "15m":
            case "30m":
                time = time.substring(time.length() - 5);
                break;
            case "1h":
            case "hour":
            case "day":
            case "month":
                time = time.substring(time.length() - 2);
                break;
        }
        return time;
    }

    /**
     * 给定时间字符串，获取其开始时间和结束时间范围
     */
    public static Map<String, Date> getTimeRange(String time, String queryType) throws Exception {
        Map<String, Date> result = new HashMap<>();
        // 本期开始时间
        Date startTime = null;
        // 本期结束时间
        Date endTime = null;

        // 处理时间
        SimpleDateFormat dataProcessDateFormat = null;
        SimpleDateFormat timeProcessDateFormat = null;
        Calendar instance = Calendar.getInstance();
        switch (queryType) {
            case "day":
                timeProcessDateFormat = new SimpleDateFormat("yyyy-MM-dd");

                // 本期时间
                startTime = timeProcessDateFormat.parse(time);
                instance.setTime(startTime);
                instance.set(Calendar.HOUR_OF_DAY, 23);
                instance.set(Calendar.MINUTE, 59);
                instance.set(Calendar.SECOND, 59);
                endTime = instance.getTime();

                break;
            case "month":
                timeProcessDateFormat = new SimpleDateFormat("yyyy-MM");

                // 本期时间
                startTime = timeProcessDateFormat.parse(time);
                instance.setTime(startTime);
                instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
                instance.set(Calendar.HOUR_OF_DAY, 23);
                instance.set(Calendar.MINUTE, 59);
                instance.set(Calendar.SECOND, 59);
                endTime = instance.getTime();
                break;
            case "year":
                timeProcessDateFormat = new SimpleDateFormat("yyyy");

                // 本期
                startTime = timeProcessDateFormat.parse(time);
                instance.setTime(startTime);
                instance.set(Calendar.MONTH, instance.getActualMaximum(Calendar.MONTH));
                instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
                instance.set(Calendar.HOUR_OF_DAY, 23);
                instance.set(Calendar.MINUTE, 59);
                instance.set(Calendar.SECOND, 59);
                endTime = instance.getTime();


                break;
            default:
                throw new ThingsboardException("非法的分析类型, 仅支持day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        result.put("start", startTime);
        result.put("end", endTime);

        return result;
    }

    /**
     * 给定时间字符串，获取其开始时间和结束时间范围
     */
    public static Map<String, Date> getTimeRange(Date time, String queryType) throws Exception {
        Map<String, Date> result = new HashMap<>();
        // 本期开始时间
        Date startTime = time;
        // 本期结束时间
        Date endTime = null;

        // 处理时间
        SimpleDateFormat dataProcessDateFormat = null;
        SimpleDateFormat timeProcessDateFormat = null;
        Calendar instance = Calendar.getInstance();
        switch (queryType) {
            case "day":
                timeProcessDateFormat = new SimpleDateFormat("yyyy-MM-dd");

                // 本期时间
                instance.setTime(startTime);
                instance.set(Calendar.HOUR_OF_DAY, 23);
                instance.set(Calendar.MINUTE, 59);
                instance.set(Calendar.SECOND, 59);
                endTime = instance.getTime();

                break;
            case "month":
                timeProcessDateFormat = new SimpleDateFormat("yyyy-MM");

                // 本期时间
                instance.setTime(startTime);
                instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
                instance.set(Calendar.HOUR_OF_DAY, 23);
                instance.set(Calendar.MINUTE, 59);
                instance.set(Calendar.SECOND, 59);
                endTime = instance.getTime();
                break;
            case "year":
                timeProcessDateFormat = new SimpleDateFormat("yyyy");

                // 本期
                instance.setTime(startTime);
                instance.set(Calendar.MONTH, instance.getActualMaximum(Calendar.MONTH));
                instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
                instance.set(Calendar.HOUR_OF_DAY, 23);
                instance.set(Calendar.MINUTE, 59);
                instance.set(Calendar.SECOND, 59);
                endTime = instance.getTime();


                break;
            default:
                throw new ThingsboardException("非法的分析类型, 仅支持day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        result.put("start", startTime);
        result.put("end", endTime);

        return result;
    }
}
