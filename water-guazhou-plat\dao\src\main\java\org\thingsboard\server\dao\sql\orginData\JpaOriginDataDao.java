package org.thingsboard.server.dao.sql.orginData;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.OriginDataEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/2 18:31
 */
@Service
public class JpaOriginDataDao {

    @Autowired
    private OriginRepository originRepository;

    public void saveOriginData(List<OriginDataEntity> originDataEntities) {
        originRepository.save(originDataEntities);
    }

    public OriginDataEntity saveOriginData(OriginDataEntity originDataEntity) {
       return originRepository.save(originDataEntity);
    }

    public List<OriginDataEntity> getOriginDataFormIdAndTime(String dataSourceId, long startTime, long endTime) {
        return originRepository.find(dataSourceId, startTime, endTime);
    }
    public OriginDataEntity getLastOriginDataFormId(String dataSourceId) {
        return originRepository.findFirstByDataSourceIdOrderByUpdateTimeDesc(dataSourceId);
    }

    public List<OriginDataEntity> getOriginDataFormId(String dataSourceId) {
        return originRepository.findByDataSourceId(dataSourceId);
    }

}
