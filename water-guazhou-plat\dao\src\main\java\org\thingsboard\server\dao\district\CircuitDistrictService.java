package org.thingsboard.server.dao.district;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrict;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictSaveRequest;

import java.util.List;

public interface CircuitDistrictService {
    /**
     * 分页条件查询片区
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SMCircuitDistrict> findAllConditional(CircuitDistrictPageRequest request);

    /**
     * 保存片区
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SMCircuitDistrict save(CircuitDistrictSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SMCircuitDistrict entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 查询指定客户的全局区域树，层级为 片区->区域/路线
     *
     * @param tenantId 客户id
     * @return 指定客户的全局区域树
     */
    SMCircuitDistrict findGlobalTree(String tenantId);

    /**
     * 查询指定客户的顶级片区
     *
     * @param tenantId 客户id
     * @return 所有的顶级片区
     */
    List<SMCircuitDistrict> findRoots(String tenantId);

    /**
     * 查询部分树（含父级）
     *
     * @param id       父级区域id
     * @param tenantId 客户id
     * @return 片区子树
     */
    SMCircuitDistrict findTreeByParentId(String id, String tenantId);

    /**
     * 查询子级树（不含父级）
     *
     * @param id       父级区域id
     * @param tenantId 客户id
     * @return 片区子树
     */
    List<SMCircuitDistrict> findChildrenTreeByParentId(String id, String tenantId);

}
