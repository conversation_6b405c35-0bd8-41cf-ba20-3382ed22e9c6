<template>
  <div id="earthContainer">

  </div>
</template>
<script>

// import store from '@/store'

let gy_images;
let selectArr = [];
export default {
  // setup() {
  //     return {

  //     }
  // },
  data() {
      return {
          leftClickMapReset: false,
          handlers: null,
          isHere: true
      }
  },
  watch: {
      leftClickMapReset() {
          let that = this;
          if (this.leftClickMapReset) {
              that.handlers = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
              that.handlers.setInputAction(function (e) {
                  if (that.isHere) {
                      that.$bus.emit('isHiddenBool', false);
                  }
              }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
          } else {
              that.$bus.emit('isHiddenBool', true);
              that.handlers.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);//移除事件
              that.handlers = null;
          }
      }
  },
  props: {

  },
  mounted() {
      let that = this;
      this.initEarth();
      this.controlVisible();
      viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);//禁用双击操作
      new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas).setInputAction((i) => {
          const t = viewer.scene.pick(i.position);
          if (t) {
              let tname = t.id.name;
              if (tname) {
                  that.$bus.emit("gridNameToInput", tname);
              }

          }
          if (selectArr[0]) {
              selectArr[0].color = { alpha: 1, blue: 1, green: 1, red: 1 };
              selectArr = [];
          }
          if (t && t.collection) {
              if (t.collection.name) {
                  t.primitive.color = Cesium.Color.YELLOW;
                  selectArr.push(t.primitive)
                  this.$bus.emit('primitiveId', t)
              }
          } else {
              let get = t.id.get();
            //   store.commit('setArea', get.gridId._value)
          }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this.$bus.on("isCascader", (isBool) => {
          this.leftClickMapReset = isBool;
          this.isHere = true;
      });
      this.$bus.on("ishere", (ishere) => {
          this.isHere = ishere;
      });
  },
  beforeUnmount() {
      window.scene && window.scene.destroy();

      //this.$bus.off('loaded');
      // AMapLoader.reset();
  },
  methods: {
      //使用异步mvt出不来
      initEarth(callback) {
          let that = this;
          Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.oaYfPD_JHJZT6Zo3zRwygnd9ozYH5U590WNHa6b8_3g';
          window.viewer = new Cesium.Viewer('earthContainer', {
              // baseLayer: Cesium.ImageryLayer.fromProviderAsync(Cesium.ArcGisMapServerImageryProvider.fromUrl('/ArcGIS/rest/services/World_Imagery/MapServer', {
              //     enablePickFeatures: false
              // }), {}),

              //地图服务
              imageryProvider: new Cesium.WebMapTileServiceImageryProvider({
                  url: 'http://t0.tianditu.gov.cn/img_w/wmts?tk=06f5c3f412ff578841f50203b7b8f490',
                  layer: 'img',
                  style: 'default',
                  format: 'tiles',
                  tileMatrixSetID: 'w',
                  maximumLevel: 18,
                  credit: new Cesium.Credit('© Tianditu', 'http://www.tianditu.gov.cn/')
              }),
              animation: false, //左下角的动画仪表盘
              baseLayerPicker: false, //右上角的图层选择按钮
              geocoder: false, //搜索框
              homeButton: false, //home按钮
              sceneModePicker: false, //模式切换按钮
              timeline: false, //底部的时间轴
              navigationHelpButton: false, //右上角的帮助按钮，
              fullscreenButton: false, //右下角的全屏按钮
              infoBox: false,//小弹窗
              selectionIndicator: false,
              zoomIndicatorContainer: false,
              navigation: false,//指南针
              shadows: false,//阴影
              shouldAnimate: true,
              scene3DOnly: true,//为true，则每个几何体实例将仅以3D渲染以节省GPU内存。

          });

          gy_images = viewer.imageryLayers.addImageryProvider(new Cesium.UrlTemplateImageryProvider({
              url: '/tiles-server/gy_images2/{z}/{x}/{y}.png'
          }))

          // MVTImageryProvider.fromUrl('http://city189.cn:4100/api/styles/style').then(provider => {
          //     const imageryLayer = viewer.imageryLayers.addImageryProvider(provider);
          // });

          // viewer.imageryLayers.addImageryProvider(new MVTImageryProvider({style : 'http://city189.cn:4100/api/styles/style',"sources": "style","version": 8,layers:'style'}));

          //viewer.scene.mode = Cesium.SceneMode.COLUMBUS_VIEW// 哥伦布视图
          viewer.scene.globe.enableLighting = true;//启用使用场景光源照亮地球
          viewer.scene.globe.depthTestAgainstTerrain = true;// 深度检测，开启就有遮挡效果了
          viewer._cesiumWidget._creditContainer.style.display = "none";  //	去除版权信息

          // postStageManager = new PostStageManager(viewer);// 在场景渲染的纹理或上一个后期处理阶段的输出上运行一个后期处理阶段。这里是加天气特效。
          // 多视锥减少drawcall，对数深度缓存也可以减少drawcall，在深度缓存中更好分分配数据，都是解决Z值冲突问题的，配合起来减少闪烁，但是多视锥配合深度缓冲可能有性能问题。
          // 可能有性能问题，所以配合起来使用的时候开启下面这个，将不必要的视锥清除，以比较好的配比解决Z值冲突问题。
          viewer.scene.logarithmicDepthBuffer = true;//是否使用对数深度缓冲区。启用此选项将允许在多视锥体中减少视锥体，从而提高性能。此属性依赖于支持的 fragmentDepth。
          //viewer.scene.debugShowFramesPerSecond = true;

          viewer.scene.globe.dynamicAtmosphereLighting = true;//启用对大气和雾的动态照明效果
          viewer.scene.globe.dynamicAtmosphereLightingFromSun = true;//动态氛围照明是否使用太阳方向而不是场景的光线方向


          // 黑白设置，没啥用
          // var collection = viewer.scene.postProcessStages;
          // var silhouette = collection.add(Cesium.PostProcessStageLibrary.createBlackAndWhiteStage ());
          // silhouette.enabled = true;
          // silhouette.uniforms.gradations =20; //调节黑白程度（1-20）
          // 亮度设置，其实这个是饱和度设置，3为最高
          var stages = viewer.scene.postProcessStages;
          // PostProcessStageLibrary包含用于创建常见的后期处理阶段的函数。createBrightnessStage此阶段具有一个统一的亮度，值可缩放每个像素的饱和度。
          viewer.scene.brightness = viewer.scene.brightness || stages.add(Cesium.PostProcessStageLibrary.createBrightnessStage());
          viewer.scene.brightness.enabled = true;
          viewer.scene.brightness.uniforms.brightness = 1.5;

          // // FeatureDetection一组用于检测当前浏览器是否支持各种功能的功能。
          // if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {//判断是否支持图像渲染像素化处理
          //     // devicePixelRatio 设备独立像素跟css像素的比率，即几个独立像素代表一个css像素
          //     var vtxf_dpr = window.devicePixelRatio;
          //     // 适度降低分辨率
          //     while (vtxf_dpr >= 2.0) {
          //         vtxf_dpr /= 2.0;
          //     }
          //     // 获取或设置渲染分辨率的缩放比例。小于1.0的值可以改善性能不佳的设备上的性能，而值大于1.0的设备将以更高的速度呈现分辨率，然后缩小比例，从而提高视觉保真度。例如，如果窗口小部件的尺寸为640x480，则将此值设置为0.5将导致场景以320x240渲染，然后在设置时按比例放大设置为2.0会导致场景以1280x960渲染，然后按比例缩小。
          //     viewer.resolutionScale = 1;
          //     //viewer.useBrowserRecommendedResolution = true;
          // }
          // //是否开启抗锯齿
          // viewer.scene.fxaa = true;
          // viewer.scene.postProcessStages.fxaa.enabled = true;

          this.initCamera();
          //
          // var tileset = await Cesium.Cesium3DTileset.fromUrl(
          //     '/tiles-server/GY/tileset.json', {}
          // );
          // readyPromise就是tileset渲染前一帧的末尾将被resolved的一个promise，他resolve了就说明tileset加载好了

          // viewer.scene.primitives.add(tileset);

          //左击断定是否serchlabel是否关闭
          // let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
          // handler.setInputAction(function (e) {
          //     that.$bus.emit('isHiddenBool', false);
          // }, Cesium.ScreenSpaceEventType.LEFT_CLICK);


      },
      resetMapToinit() {
          viewer.camera.flyTo({
              //destination: Cesium.Cartesian3.fromDegrees(Number(lnglat.lng), Number(lnglat.lat), 500.0), //设置位置
              destination: { x: -2194589.926714329, y: 4537167.489961663, z: 4007700.8945355695 },
              orientation: {
                  // 方向
                  heading: 6.273226573809088,
                  // 视角、倾斜角度
                  pitch: Cesium.Math.toRadians(-90),
                  roll: 0.0
              }
          })
      },
      initCamera() {
          // viewer.scene.camera.setView({
          //     destination: { x: -2194589.926714329, y: 4537167.489961663, z: 4007700.8945355695 },
          //     orientation: {
          //         heading: 6.273226573809088,
          //         pitch: -1.5706876796153595,//从上往下看为-90
          //         roll: 0
          //     },
          //     duration:5
          // })
          // east: 2.0293898740690866
          // north: 0.6782357760755783
          // south: 0.6717987231682411
          // west: 2.0131944662226493
          viewer.camera.setView({
              destination: Cesium.Rectangle(2.0131944662226493, 0.6717987231682411, 2.0293898740690866, 0.6782357760755783)
          });
      },

      // 监听地图缩放或放大来控制地图上添加的内容是否展示
      controlVisible() {
          let that = this;
          let tiandituTk = "d12c58b350e40ccb9367d2d53378af31"
          viewer.scene.screenSpaceCameraController.maximumZoomDistance = 67000;
          viewer.scene.preRender.addEventListener(function () {
              //视角改动监控
              let currentMagnitude = viewer.camera.getMagnitude();
              let _viewRectangle = viewer.camera.computeViewRectangle();
              // east: 2.0293898740690866
              // north: 0.6782357760755783
              // south: 0.6717987231682411
              // west: 2.0131944662226493
              // if(_viewRectangle.east > 2.0293898740690866){
              //     viewer.camera.setView({
              //         destination : Cesium.Rectangle(_viewRectangle.west, _viewRectangle.south, 2.0293898740690866, _viewRectangle.north)
              //     });
              // }
              if (currentMagnitude < 6382883.507027751) {
                  gy_images.show = false;
                  //注记
                  // that.annotionTxt_Layer=viewer.imageryLayers.addImageryProvider(new Cesium.WebMapTileServiceImageryProvider({
                  //     url:'http://t0.tianditu.com/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk='+tiandituTk,
                  //     //url: "http://t0.tianditu.com/cva_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=" +tiandituTk ,
                  //     layer: "tdtAnnoLayer",
                  //     style: "default",
                  //     format: "image/jpeg",
                  //     tileMatrixSetID: "GoogleMapsCompatible"
                  // }));
              } else {
                  // alert(2)
                  // if(that.annotionTxt_Layer!=null){
                  //     let retBool= viewer.imageryLayers.remove("tdtAnnoLayer");
                  //     //let retBool2= viewer.imageryLayers.removeAll();
                  //     //that.annotionTxt_Layer=null;
                  // }
                  gy_images.show = true;
              }
          });
          // viewer.scene.preRender.addEventListener(function() { 
          //     let _viewRectangle = viewer.camera.computeViewRectangle();

          // });
      },
  }
}

</script>
<style lang="scss" scoped>
#earthContainer {
  width: 100%;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
</style>
