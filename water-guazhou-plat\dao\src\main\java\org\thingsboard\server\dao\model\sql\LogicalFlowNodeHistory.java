package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;
import java.util.List;

@Data
@Entity
@EqualsAndHashCode
@Table(name = ModelConstants.TABLE_LOGICAL_FLOW_NODE_HISTORY)
public class LogicalFlowNodeHistory {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.HISTORY_ID)
    private String historyId;

    @Column(name = ModelConstants.LOGICAL_FLOW_NODE_ID)
    private String logicalFlowNodeId;

    @Column(name = ModelConstants.LOGICAL_FLOW_NODE_NAME)
    private String logicalFlowNodeName;

    @Column(name = ModelConstants.CREATED_TIME)
    private Long createdTime;

    @Column(name = ModelConstants.START_TIME)
    private Long startTime;

    @Column(name = ModelConstants.END_TIME)
    private Long endTime;

    @Column(name = ModelConstants.LOGICAL_FLOW_RUN_RESULT)
    private String result;

    @Column(name = ModelConstants.LOGICAL_FLOW_RUN_SCRIPT)
    private String script;

    @Column(name = ModelConstants.LOGICAL_FLOW_PARENT_NODE_ID)
    private String parentNodeId;

    @Column(name = ModelConstants.LOGICAL_FLOW_ID)
    private String logicalFlowId;

    @Transient
    private List<LogicalFlowNodeHistory> children;

}
