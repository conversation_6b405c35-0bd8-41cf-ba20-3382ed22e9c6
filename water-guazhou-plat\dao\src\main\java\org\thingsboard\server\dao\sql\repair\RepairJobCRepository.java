package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.RepairJobCEntity;

import java.util.List;

public interface RepairJobCRepository extends JpaRepository<RepairJobCEntity, String> {
    List<RepairJobCEntity> findByMainId(String mainId);

    @Modifying
    @Transactional
    void removeByMainId(String id);
}
