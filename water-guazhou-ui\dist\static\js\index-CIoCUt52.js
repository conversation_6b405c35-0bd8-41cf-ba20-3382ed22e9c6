import{d as h,M as w,r as p,c as b,a8 as C,bu as L,o as x,g as D,n as S,q as c,i as l,F as v,b6 as I}from"./index-r0dFAfgr.js";import{_ as T}from"./CardTable-rdWOL4_6.js";import{_ as q}from"./CardSearch-CB_HNR-Q.js";import B from"./detail-RxaxHEbW.js";import{I as M}from"./common-CvK_P_ao.js";import{p as N}from"./process-DWVjEFpZ.js";import{p as z}from"./applyInstall-D-IustB3.js";import{f as y}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./printUtils-C-AxhDcd.js";const E={class:"wrapper"},H=h({__name:"index",setup(V){w();const s=p({typeList:[],taskInfo:{}}),f=b(),m=b(),k=p({filters:[{label:"工程编号",field:"code",type:"input"},{label:"工程类型",field:"type",type:"select",options:C(()=>s.typeList)},{label:"步骤名称",field:"currentStep",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:M.QUERY,click:()=>i()}]}]}),d=p({title:"",cancel:!1,width:"80%",group:[],onClosed:()=>{i()}}),e=p({indexVisible:!0,columns:[{label:"工程编号",prop:"code"},{label:"任务类型",prop:"typeName"},{label:"当前步骤",prop:"currentStep"},{label:"任务地址",prop:"address"},{label:"接收时间",prop:"receiveTime",formatter:(t,a)=>y(a)},{label:"创建时间",prop:"createTime",formatter:(t,a)=>y(a)}],dataList:[],operationWidth:100,operations:[{perm:t=>t.status==="进行中",text:"处理",isTextBtn:!0,click:t=>{var a;d.title=t.code,(a=f.value)==null||a.openDrawer(),s.taskInfo=t}}],pagination:{refreshData:({page:t,size:a})=>{e.pagination.page=t,e.pagination.limit=a,i()}}});L(async()=>{var o,r;const t=await N({page:1,size:9999});s.typeList=(o=t.data)==null?void 0:o.data.data;const a=(r=t.data)==null?void 0:r.data.data;s.typeList=a.map(n=>({label:n.name,value:n.id}))});const i=async()=>{var r,n,u,_,g;const a={...((r=m.value)==null?void 0:r.queryParams)||{},status:"进行中",page:e.pagination.page||1,size:e.pagination.limit||20},o=await z(a);e.pagination.total=(u=(n=o.data)==null?void 0:n.data)==null?void 0:u.total,e.dataList=(g=(_=o.data)==null?void 0:_.data)==null?void 0:g.data};return x(async()=>{await i()}),(t,a)=>{const o=q,r=T,n=I;return D(),S("div",E,[c(o,{ref_key:"refSearch",ref:m,config:l(k)},null,8,["config"]),c(r,{class:"card-table",config:l(e)},null,8,["config"]),c(n,{ref_key:"refDetail",ref:f,config:l(d)},{default:v(()=>[c(B,{"task-info":l(s).taskInfo,"task-id":"1"},null,8,["task-info"])]),_:1},8,["config"])])}}});export{H as default};
