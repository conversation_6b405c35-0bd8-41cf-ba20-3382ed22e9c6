package org.thingsboard.server.dao.sql.gis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.GisProjectListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisProject;

@Mapper
public interface GisProjectMapper extends BaseMapper<GisProject> {
    IPage<GisProject> findList(IPage<GisProject> pageRequest, @Param("param") GisProjectListRequest request);
}
