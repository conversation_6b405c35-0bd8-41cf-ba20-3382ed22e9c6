<template>
  <div id="map-outer-box" ref="refDom" class="map-wrapper" @click="(e) => emit('click', e)">
    <ArcView ref="refMap" @loaded="onMapLoaded">
      <slot></slot>
      <slot name="map-bars"></slot>
      <ArcPipe @pipe-loaded="handlePipeLoaded"></ArcPipe>
      <ArcPoi></ArcPoi>
      <ArcLayerList></ArcLayerList>
      <ArcCoordinate></ArcCoordinate>
      <ArcScale></ArcScale>
      <ArcZoom></ArcZoom>
      <ArcLengthMeasure></ArcLengthMeasure>
      <ArcAreaMeasure></ArcAreaMeasure>
      <ArcPrint></ArcPrint>
      <ArcLegend></ArcLegend>
      <ArcBasemapGallary></ArcBasemapGallary>
      <ArcOverview></ArcOverview>
      <ArcHome ref="refHome"></ArcHome>
      <ArcStationWarning ref="refAlarms" @click="closeAllPop"></ArcStationWarning>
      <Panel
        v-if="state.mounted"
        ref="refPanel"
        :custom-class="props.panelClass || 'gis-detail-panel'"
        :telport="refDom"
        :draggable="props.panelDragable"
        :dragout="true"
        :position="props.panelPosition"
        :full-content="props.panelFullContent"
        :destroy-by-close="true"
        :after-open="props.panelOpend"
        :extra="props.penelExtra"
        :show-close="!props.hidePanelClose"
        :max-min="props.panelMaxMin === undefined ? true : props.panelMaxMin"
        :before-close="props.beforePanelClose"
        :title="panelTitle"
        :default-visible="props.panelDefaultVisible"
        :default-maxmin="props.panelDefaultMaxmin"
        :maxmin-changed="props.panelMaxminChanged"
      >
        <template #extra>
          <slot name="detail-extra"> </slot>
        </template>
        <template #header>
          <slot name="detail-header"></slot>
        </template>
        <template #default>
          <slot name="detail-default"></slot>
        </template>
      </Panel>
    </ArcView>
  </div>
</template>
<script lang="ts" setup>
interface EmitMethods {
  (e: 'click', event: MouseEvent): any;
  (e: 'pipeLoaded',layer): any;
  (e: 'alarm-click'): any;
  (e: 'mapLoaded', view: __esri.MapView): any;
  (e: 'mounted'): any;
}
const refDom = ref<HTMLDivElement>();
const emit = defineEmits<EmitMethods>();
const props = defineProps<{
  panelClass?: string;
  panelFullContent?: boolean;
  penelExtra?: boolean;
  hidePanelClose?: boolean;
  panelMaxMin?: boolean;
  panelTitle?: string;
  panelTelport?: string;
  panelDefaultVisible?: boolean;
  panelPosition?: 'bottom' | 'right' | 'left' | 'top' | '';
  panelDragable?: boolean;
  panelDefaultMaxmin?: 'max' | 'min' | 'normal';
  beforePanelClose?: (() => any) | undefined;
  panelOpend?: (() => any) | undefined;
  panelMaxminChanged?: (type: 'max' | 'min' | 'normal') => any;
}>();
const refPanel = ref<IPanelIns>();
const refAlarms = ref<IArcStationWarningIns>();
const refHome = ref<IArcHomeIns>();
const state = reactive<{
  mounted: boolean;
}>({
  mounted: false
});
const handlePipeLoaded = async (layer) => {
  emit('pipeLoaded',layer);
};
const onMapLoaded = async (view: __esri.MapView) => {
  await nextTick();
  emit('mapLoaded', view);
};
const toggleAlarmPop = (flag?: boolean) => {
  refAlarms.value?.togglePop(flag);
};
const closeAllPop = () => {
  emit('alarm-click');
};
// const togglePanel = (open?: boolean | undefined) => {
//   refPanel.value?.Toggle(open)
// }
defineExpose({
  toggleAlarmPop,
  refPanel
});
onActivated(() => {
  console.log('actived');
  state.mounted = true;
  nextTick().then(() => {
    emit('mounted');
  });
});
onMounted(() => {
  console.log('mounted');
  state.mounted = true;
  nextTick().then(() => {
    emit('mounted');
  });
});
onDeactivated(() => {
  console.log('deactived');
  state.mounted = false;
});
</script>
<style lang="scss" scoped>
.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  :deep(.esri-ui-top-left) {
    flex-flow: row;
  }

  :deep(.esri-ui-bottom-right) {
    flex-flow: column;
  }
}
</style>
