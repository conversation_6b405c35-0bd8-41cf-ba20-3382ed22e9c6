package org.thingsboard.server.dao.sql.area;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.AreaEntity;

import java.util.Collections;
import java.util.Map;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface AreaMapper extends BaseMapper<AreaEntity> {

    default String getTreePath(String id) {
        if (id == null)
            return null;
        return (String) getTreePathMap().getOrDefault(id, Collections.emptyMap()).get("path");
    }


    @MapKey("id")
    Map<String, Map<String, Object>> getTreePathMap();

}
