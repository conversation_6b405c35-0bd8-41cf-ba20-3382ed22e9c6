const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/schemaUtils-DLXXqxNF.js","static/js/Point-WxyopZva.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/MapView-DaoQedLH.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js","static/js/enums-L38xj_2E.js","static/js/color-DAS1c3my.js","static/js/enums-B5k73o5q.js","static/js/enums-BDQrMlcz.js","static/js/VertexElementDescriptor-BOD-G50G.js","static/js/number-CoJp78Rz.js","static/js/utils-DPUVnAXL.js","static/js/MaterialKey-BYd7cMLJ.js","static/js/alignmentUtils-CkNI7z7C.js","static/js/definitions-826PWLuy.js","static/js/visualVariablesUtils-7_6yXvXo.js","static/js/cimAnalyzer-CMgqZsaO.js","static/js/fontUtils-BuXIMW9g.js","static/js/BidiEngine-CsUYIMdL.js","static/js/GeometryUtils-B7ExOJII.js","static/js/Rect-CUzevAry.js","static/js/callExpressionWithFeature-DgtD4TSq.js","static/js/quantizationUtils-DtI9CsYu.js","static/js/floatRGBA-PQQNbO39.js","static/js/ExpandedCIM-C1laM-_7.js","static/js/util-DPgA-H2V.js"])))=>i.map(i=>d[i]);
import{bs as Rr,ib as de,ca as _e,cx as g,dN as Fr,ic as Vr,eD as Ot,b1 as sr,id as Or,dK as Dr,ie as Gr,e0 as Pt,ig as zt,h$ as fe,ao as Kr,cI as Tt,ih as Zr,ii as Nr,ij as nr,ik as or,aI as Xr,eQ as Ur,il as Hr,im as Yr,hX as qr,io as $e,ip as We,iq as Ie,eu as jr,fz as Qr}from"./MapView-DaoQedLH.js";import{a as Jt,h as Jr,o as nt,f as ti,d as ar,g as k,k as ei,i as ri,p as ii,n as st,l as si,m as ni,e as I,q as lr,X as oi,t as ai,u as hr,v as Dt,w as cr,A as ur}from"./definitions-826PWLuy.js";import{f as li,p as hi}from"./visualVariablesUtils-0WgcmuMn.js";import{E as P,S as Gt}from"./enums-L38xj_2E.js";import{R as te,c as F,g as z,k as ci,G as ui}from"./color-DAS1c3my.js";import{o as di}from"./tileUtils-B7X19rIS.js";import{b2 as _i,T as O,R as A,aO as me,$ as Et,a3 as dr}from"./index-r0dFAfgr.js";import{s as ot,i as xt,a9 as fi,S as pe,j as mi,am as Ce}from"./Point-WxyopZva.js";import{r as pi,i as xi}from"./libtess-lH4Jrtkh.js";import{i as Z,o as $t,e as ke}from"./enums-B5k73o5q.js";import{s as yi,r as _r,n as fr,i as gi,a as Mi,c as vi,o as wi}from"./alignmentUtils-CkNI7z7C.js";import{x as W,w as M}from"./number-CoJp78Rz.js";import{U as ee,Z as Ht,N as xe,C as Bt,w as mt,O as At,b as mr,P as bi,f as ye}from"./MaterialKey-BYd7cMLJ.js";import{g as Si,h as Li,S as Pi,k as Yt,b as Ee,p as zi,j as Ti}from"./cimAnalyzer-CMgqZsaO.js";import{c as re}from"./GeometryUtils-BRRfazic.js";import"./earcut-BJup91r2.js";import{c as pr,t as xr}from"./GeometryUtils-B7ExOJII.js";import{c as $i}from"./TurboLine-CDscS66C.js";import{i as Wi}from"./callExpressionWithFeature-DgtD4TSq.js";import{l as Ii}from"./ExpandedCIM-C1laM-_7.js";import{L as Ci}from"./pe-B8dP0-Ut.js";function R(o,t){if(o&&"name"in o){const e=o;return t&&t.error(new ot(e.name,e.message,e.details)),!1}return!0}const ki=1.25;let Wt=class{get length(){return this._pos}constructor(t,e){this._pos=0;const r=e?this._roundToNearest(e,t.BYTES_PER_ELEMENT):40;this._array=new ArrayBuffer(r),this._buffer=new t(this._array),this._ctor=t,this._i16View=new Int16Array(this._array)}_roundToNearest(t,e){const r=Math.round(t);return r+(e-r%e)}_ensureSize(t){if(this._pos+t>=this._buffer.length){const e=this._roundToNearest((this._array.byteLength+t*this._buffer.BYTES_PER_ELEMENT)*ki,this._buffer.BYTES_PER_ELEMENT),r=new ArrayBuffer(e),i=new this._ctor(r);i.set(this._buffer,0),this._array=r,this._buffer=i,this._i16View=new Int16Array(this._array)}}ensureSize(t){this._ensureSize(t)}writeF32(t){this._ensureSize(1);const e=this._pos;return new Float32Array(this._array,4*this._pos,1)[0]=t,this._pos++,e}push(t){this._ensureSize(1);const e=this._pos;return this._buffer[this._pos++]=t,e}writeFixed(t){this._buffer[this._pos++]=t}setValue(t,e){this._buffer[t]=e}i1616Add(t,e,r){this._i16View[2*t]+=e,this._i16View[2*t+1]+=r}getValue(t){return this._buffer[t]}incr(t){if(this._buffer.length<t)throw new Error("Increment index overflows the target buffer");this._buffer[t]++}decr(t){this._buffer[t]--}writeRegion(t){this._ensureSize(t.length);const e=this._pos;return this._buffer.set(t,this._pos),this._pos+=t.length,e}writeManyFrom(t,e,r){this._ensureSize(r-e);for(let i=e;i!==r;i++)this.writeFixed(t._buffer[i])}buffer(){const t=this._array.slice(0,4*this._pos);return this.destroy(),t}toArray(){const t=this._array,e=[];for(let r=0;r<t.byteLength/4;r++)e.push(t[r]);return e}seek(t){this._pos=t}destroy(){this._array=null,this._buffer=null}};const dt=new Map;function Ei(o,t,e){const{indicesPerRecord:r,multiplier:i,verticesPerRecord:s}=dt.get(o);return{recordBytes:e*Jt*Uint32Array.BYTES_PER_ELEMENT,indexBytes:i*r*e*Uint32Array.BYTES_PER_ELEMENT,vertexBytes:i*s*e*t}}dt.set(P.MARKER,{multiplier:1,indicesPerRecord:6,verticesPerRecord:4}),dt.set(P.LINE,{multiplier:1,indicesPerRecord:24,verticesPerRecord:8}),dt.set(P.FILL,{multiplier:1,indicesPerRecord:10,verticesPerRecord:10}),dt.set(P.TEXT,{multiplier:8,indicesPerRecord:6,verticesPerRecord:4}),dt.set(P.LABEL,{multiplier:8,indicesPerRecord:6,verticesPerRecord:4});let Be=class{constructor(t,e,r){this._start={index:0,vertex:0};const i=Ei(t,e,r),s=e/4;this.geometryType=t,this._records=new Wt(Int32Array,i.recordBytes),this._indices=new Wt(Uint32Array,i.indexBytes),this._vertices=new Wt(Uint32Array,i.vertexBytes),this._metrics=new Wt(Float32Array,0),this._strideInt=s}serialize(t){const e=this._records.buffer(),r=this._indices.buffer(),i=this._vertices.buffer(),s=this._metrics.length?this._metrics.buffer():null,n=4*this._strideInt;return t.push(e,r,i),{stride:n,records:e,indices:r,vertices:i,metrics:s}}get strideInt(){return this._strideInt}get recordCount(){return this._records.length/Jt}get vertexCount(){return this._vertices.length/this._strideInt}get indexCount(){return this._indices.length}get indexWriter(){return this._indices}get vertexWriter(){return this._vertices}get metricWriter(){return this._metrics}vertexEnsureSize(t){this._vertices.ensureSize(t)}indexEnsureSize(t){this._indices.ensureSize(t)}recordStart(){this._start.index=this._indices.length,this._start.vertex=this._vertices.length}recordEnd(t,e,r,i,s,n,a,l){this._records.push(t),this._records.push(e??0),this._records.push(r),this._records.push(i),this._records.push(s),this._records.push(n),this._records.push(a),this._records.writeF32(l)}writeIndex(t){this._indices.push(t)}writeVertex(t){this._vertices.push(t)}writeVertexF32(t){this._vertices.writeF32(t)}copyLastFrom(t,e,r){const i=t._records.length-Jt,s=t._records.getValue(i),n=t._records.getValue(i+1),a=t._records.getValue(i+2),l=t._records.getValue(i+4),h=t._records.getValue(i+6),u=t._records.getValue(i+7),c=this._vertices.length,f=(t._start.vertex-this._vertices.length)/this._strideInt,d=this._indices.length,_=this.vertexCount;for(let m=t._start.index;m!==t._indices.length;m++){const x=t._indices.getValue(m);this._indices.push(x-f)}for(let m=t._start.vertex;m!==t._vertices.length;m++){const x=t._vertices.getValue(m);this._vertices.push(x)}for(let m=c;m<=this._vertices.length;m+=this._strideInt)this._vertices.i1616Add(m,e,r);this._records.push(s),this._records.push(n),this._records.push(a),this._records.push(d),this._records.push(l),this._records.push(_),this._records.push(h),this._records.push(u)}};const Kt=1,ge=2,Zt=4,Me=8,ve=16,Nt=32,we=64,Xt=128;function Ae(o){switch(o){case Kt:case Me:case Nt:return-1;case ge:case we:return 0;case Zt:case ve:case Xt:return 1}}function Re(o){switch(o){case Kt:case ge:case Zt:return-1;case Me:case ve:return 0;case Nt:case we:case Xt:return 1}}const Fe=Kt|Me|Nt,Ve=Zt|ve|Xt,Oe=Kt|ge|Zt,De=Nt|we|Xt;let mn=class{constructor(t,e,r,i,s,n=0){this._hasAggregate=!1,this.hasRecords=!1,this._data={self:new Map,neighbors:new Array},this._version=0,this._current={geometryType:0,writer:null,overlaps:0,start:0,insertAfter:0,sortKey:0,id:0,materialKey:0,indexStart:0,vertStart:0,isDotDensity:!1,bufferingEnabled:!1,metricBoxLenPointer:0},this.hint=e,this.tileKey=t,this._hasAggregate=i,this._pixelBufferEnabled=s,this._version=n,this._symbologyType=r}get hasAggregates(){return this._hasAggregate}get hasPixelBufferEnabled(){return this._pixelBufferEnabled}serialize(t){const e=[];return e.push(this._serializeTileVertexData(this.tileKey,this.tileKey,this._data.self)),this._data.neighbors.forEach((r,i)=>{const s=1<<i,n=Ae(s),a=Re(s),l=di(new Rr(this.tileKey),n,a,t),h=this._serializeTileVertexData(this.tileKey,l.id,r.vertexData);h.message.bufferIds=r.displayIds,e.push(h)}),e}_serializeTileVertexData(t,e,r){var s,n,a,l,h;const i=new Array;return{message:{tileKeyOrigin:t,tileKey:e,data:{[P.MARKER]:(s=r.get(P.MARKER))==null?void 0:s.serialize(i),[P.FILL]:(n=r.get(P.FILL))==null?void 0:n.serialize(i),[P.LINE]:(a=r.get(P.LINE))==null?void 0:a.serialize(i),[P.TEXT]:(l=r.get(P.TEXT))==null?void 0:l.serialize(i),[P.LABEL]:(h=r.get(P.LABEL))==null?void 0:h.serialize(i)},version:this._version},transferList:i}}featureStart(t,e){this._current.insertAfter=t,this._current.sortKey=e}featureEnd(){}recordStart(t,e,r,i){this._current.writer=this._getVertexWriter(r),this._current.overlaps=0,this._current.indexStart=this._current.writer.indexCount,this._current.vertStart=this._current.writer.vertexCount,this._current.bufferingEnabled=i,this._current.id=t,this._current.materialKey=e,this._current.geometryType=r,this._current.isDotDensity=!1,this._current.writer.recordStart()}recordCount(){return this._current.writer.recordCount}vertexCount(){return this._current.writer.vertexCount}indexCount(){return this._current.writer.indexCount}vertexEnsureSize(t){this._current.writer.vertexEnsureSize(t)}indexEnsureSize(t){this._current.writer.indexEnsureSize(t)}vertexBounds(t,e,r,i){this._current.bufferingEnabled&&this._addOverlap(t,e,r,i)}vertexWrite(t){this._current.writer.writeVertex(t)}vertexWriteF32(t){this._current.writer.writeVertexF32(t)}vertexEnd(){}vertexWriter(){return this._current.writer.vertexWriter}indexWrite(t){this._current.writer.writeIndex(t)}indexWriter(){return this._current.writer.indexWriter}metricWriter(){return this._current.writer.metricWriter}metricStart(t,e,r,i,s,n,a,l){this._current.writer=this._getVertexWriter(P.LABEL);const h=this._current.writer.metricWriter;h.push(li(t)),h.push(e),h.push(r),h.push(i),h.push(s),h.push(n),h.push(a),h.push(l),h.push(255),this._current.metricBoxLenPointer=h.push(0)}metricEnd(){const t=this._current.writer.metricWriter;t.getValue(this._current.metricBoxLenPointer)===0&&t.seek(t.length-10)}metricBoxWrite(t,e,r,i){const s=this._current.writer.metricWriter;s.incr(this._current.metricBoxLenPointer),s.push(0),s.push(0),s.push(t),s.push(e),s.push(r),s.push(i)}recordEnd(){const t=this._current.vertStart,e=this._current.writer.vertexCount-t;if(!e)return!1;this.hasRecords=!0;const r=this._current.indexStart,i=this._current.writer.indexCount-r;if(this._current.writer.recordEnd(this._current.id,this._current.materialKey,this._current.insertAfter,r,i,t,e,this._current.sortKey),!this._pixelBufferEnabled||this._hasAggregate||this._current.overlaps===0||this._current.geometryType===P.LABEL)return!0;const s=this._current.writer;for(let n=0;n<8;n++){const a=1<<n;if(this._current.overlaps&a){this._data.neighbors[n]||(this._data.neighbors[n]={vertexData:new Map,displayIds:new Set});const l=this._data.neighbors[n],h=this._current.geometryType;if(!l.vertexData.has(h)){const _=te(h,this._symbologyType).geometry,m=new Be(h,_,Jr);l.vertexData.set(h,m)}const u=l.vertexData.get(this._current.geometryType),c=8,f=512*-Ae(a)*c,d=512*-Re(a)*c;u==null||u.copyLastFrom(s,f,d),l.displayIds.add(this._current.id)}}return!0}_addOverlap(t,e,r,i){const s=255^((t<0+r?Ve:t>=nt-r?Fe:Ve|Fe)|(e<0+i?De:e>=nt-i?Oe:De|Oe));this._current.overlaps|=s}_getVertexWriter(t){if(!this._data.self.has(t)){const e=this._data.self,r=te(t,this._symbologyType).geometry;e.set(t,new Be(t,r,this.hint.records))}return this._data.self.get(t)}};const X=0,U=100;function Ge(o,t,e){return o[0]=t[0]-e[0],o[1]=t[1]-e[1],o}function yr(o,t){return Math.sqrt(o*o+t*t)}function Ke(o){const t=yr(o[0],o[1]);o[0]/=t,o[1]/=t}function Bi(o,t){return yr(o[0]-t[0],o[1]-t[1])}function b(o){return typeof o=="function"}function ie(o=2){return 1/Math.max(o,1)}function tt(o,t){return[!!(o!=null&&o.minScale)&&t.scaleToZoom(o.minScale)||X,!!(o!=null&&o.maxScale)&&t.scaleToZoom(o.maxScale)||U]}function Ai(o,t){return o[t+1]}function gr(o){return o.length-1}function Ri(o){let t=0;for(let e=0;e<gr(o);e++)t+=Fi(o,e);return t}function Fi(o,t,e=1){const[r,i]=Ai(o,t);return Math.sqrt(r*r+i*i)*e}let Vi=class se{constructor(t,e,r,i,s){this._segments=t,this._index=e,this._distance=r,this._xStart=i,this._yStart=s,this._done=!1}static create(t){return new se(t,0,0,t[0][0],t[0][1])}clone(){return new se(this._segments,this._index,this._distance,this.xStart,this.yStart)}equals(t){return this._index===t._index||t._index===this._index-1&&(this._distance===0||t._distance===1)||t._index===this._index+1&&(this._distance===1||t._distance===0)}leq(t){return this._index<t._index||this._index===t._index&&this._distance<=t._distance}geq(t){return this._index>t._index||this._index===t._index&&this._distance>=t._distance}get _segment(){return this._segments[this._index+1]}get angle(){const t=this.dy,e=(0*t+-1*-this.dx)/(1*this.length);let r=Math.acos(e);return t>0&&(r=2*Math.PI-r),r}get xStart(){return this._xStart}get yStart(){return this._yStart}get x(){return this.xStart+this.distance*this.dx}get y(){return this.yStart+this.distance*this.dy}get dx(){return this._segment[0]}get dy(){return this._segment[1]}get xMidpoint(){return this.xStart+.5*this.dx}get yMidpoint(){return this.yStart+.5*this.dy}get xEnd(){return this.xStart+this.dx}get yEnd(){return this.yStart+this.dy}get length(){const{dx:t,dy:e}=this;return Math.sqrt(t*t+e*e)}get remainingLength(){return this.length*(1-this._distance)}get backwardLength(){return this.length*this._distance}get distance(){return this._distance}get done(){return this._done}hasPrev(){return this._index-1>=0}hasNext(){return this._index+1<gr(this._segments)}next(){return this.hasNext()?(this._xStart+=this.dx,this._yStart+=this.dy,this._distance=0,this._index+=1,this):null}prev(){return this.hasPrev()?(this._index-=1,this._xStart-=this.dx,this._yStart-=this.dy,this._distance=1,this):(this._done=!0,null)}_seekBackwards(t,e){const r=this.backwardLength;if(t<=r)return this._distance=(r-t)/this.length,this;let i=this.backwardLength;for(;this.prev();){if(i+this.length>t)return this._seekBackwards(t-i);i+=this.length}return this._distance=0,e?this:null}seek(t,e=!1){if(t<0)return this._seekBackwards(Math.abs(t),e);if(t<=this.remainingLength)return this._distance=(this.backwardLength+t)/this.length,this;let r=this.remainingLength;for(;this.next();){if(r+this.length>t)return this.seek(t-r,e);r+=this.length}return this._distance=1,e?this:null}};function Oi(o,t,e,r=!0){const i=Ri(o),s=Vi.create(o),n=i/2;if(!r)return s.seek(n),void e(s.clone(),0,n+0*t,i);const a=Math.max((i-t)/2,0),l=Math.floor(a/t),h=n-l*t;s.seek(h);for(let u=-l;u<=l;u++)s.x<512&&s.x>=0&&s.y<512&&s.y>=0&&e(s.clone(),u,n+u*t,i),s.seek(t)}function Di(o,t){const e=t;for(let r=0;r<o.length;r++){let i=o[r];const s=[];s.push(i[0]);for(let a=1;a<i.length;a++){let[l,h]=s[a-1];l+=i[a][0],h+=i[a][1],s.push([l,h])}Gi(s,e);const n=[];n.push(s[0]);for(let a=1;a<s.length;a++){const[l,h]=s[a-1],[u,c]=s[a],f=Math.round(u-l),d=Math.round(c-h);n.push([f,d])}o[r]=n,i=n}return o}function Gi(o,t){if(t<=0)return;const r=o.length;if(r<3)return;const i=[];let s=0;i.push(0);for(let c=1;c<r;c++)s+=Bi(o[c],o[c-1]),i.push(s);t=Math.min(t,.2*s);const n=[];n.push(o[0][0]),n.push(o[0][1]);const a=o[r-1][0],l=o[r-1][1],h=Ge([0,0],o[0],o[1]);Ke(h),o[0][0]+=t*h[0],o[0][1]+=t*h[1],Ge(h,o[r-1],o[r-2]),Ke(h),o[r-1][0]+=t*h[0],o[r-1][1]+=t*h[1];for(let c=1;c<r;c++)i[c]+=t;i[r-1]+=t;const u=.5*t;for(let c=1;c<r-1;c++){let f=0,d=0,_=0;for(let m=c-1;m>=0&&!(i[m+1]<i[c]-u);m--){const x=u+i[m+1]-i[c],p=i[m+1]-i[m],y=i[c]-i[m]<u?1:x/p;if(Math.abs(y)<1e-6)break;const v=y*y,w=y*x-.5*v*p,L=y*p/t,S=o[m+1],T=o[m][0]-S[0],$=o[m][1]-S[1];f+=L/w*(S[0]*y*x+.5*v*(x*T-p*S[0])-v*y*p*T/3),d+=L/w*(S[1]*y*x+.5*v*(x*$-p*S[1])-v*y*p*$/3),_+=L}for(let m=c+1;m<r&&!(i[m-1]>i[c]+u);m++){const x=u-i[m-1]+i[c],p=i[m]-i[m-1],y=i[m]-i[c]<u?1:x/p;if(Math.abs(y)<1e-6)break;const v=y*y,w=y*x-.5*v*p,L=y*p/t,S=o[m-1],T=o[m][0]-S[0],$=o[m][1]-S[1];f+=L/w*(S[0]*y*x+.5*v*(x*T-p*S[0])-v*y*p*T/3),d+=L/w*(S[1]*y*x+.5*v*(x*$-p*S[1])-v*y*p*$/3),_+=L}n.push(f/_),n.push(d/_)}n.push(a),n.push(l);for(let c=0,f=0;c<r;c++)o[c][0]=n[f++],o[c][1]=n[f++]}let Mr=class{static getPlacement(t,e,r,i,s){const n=Si(e);if(!n)return null;const a=Li(t);return n.execute(a,e,r,i,s)}};const ht=8,V=M(4,4),Rt=M(4,2),D=M(4,6),Ze=[Rt,Rt,D,D],Ne=[Rt,D,Rt,D],Ki=[D,D,V,V],Zi=[V,V,D,D],Ni=[D,V,D,V],Xi=[V,D,V,D],vr=o=>class extends o{constructor(...t){super(...t),this._isCIM=!1,this._vertexBoundsScale=1,this.geometryType=P.TEXT,this._aux=W(0,0,this._referenceSize,this._bitset)}bindTextInfo(t,e){t&&t.length?this._shapingInfo=_i(t,r=>Pi(r,e,{scale:this._scale,angle:this._angle,xOffset:this._xOffset,yOffset:this._yOffset,hAlign:this._xAlignD,vAlign:this._yAlignD,maxLineWidth:Math.max(32,Math.min(this._lineWidth,512)),lineHeight:ti*Math.max(.25,Math.min(this._lineHeight,4)),decoration:this._decoration,isCIM:this._isCIM,hasBackground:!!this._backgroundColor,borderLineSize:this._borderLineSize})):this._shapingInfo=null}_write(t,e,r,i){const s=e.getDisplayId();this._writeGeometry(t,e,s,r,i)}_writeGeometry(t,e,r,i,s){const n=this._shapingInfo;if(O(n))return;if(A(this._textPlacement)){const l=s??e.readLegacyGeometryForDisplay();return this._writePlacedText(t,r,l,n,i)}const a=s?de(_e(s),2):e.geometryType==="esriGeometryPolygon"?e.readCentroid():e.readGeometryForDisplay();if(!O(a)){if(a.isPoint){const[l,h]=a.coords;return!t.hasAggregates&&t.hasPixelBufferEnabled&&(l<0||l>=512||h<0||h>=512)?void 0:this._writeGlyphs(t,r,{x:l,y:h},n)}a.forEachVertex((l,h)=>this._writeGlyphs(t,r,{x:l,y:h},n))}}_writePlacedText(t,e,r,i,s){const n=me(this._textPlacement),a=Mr.getPlacement(r,n,g(1),t.tileKey,s.geometryEngine);if(!a)return;let l=a.next();for(;l!=null;){const h=-l.getAngle();i.setRotation(h);const u=l.tx,c=-l.ty;u<0||u>=512||c<0||c>=512||(this._writeGlyphs(t,e,{x:u,y:c},i),i.setRotation(-h)),l=a.next()}}_writeGlyphs(t,e,r,i){const s=ee.load(this._materialKey),n=M(Math.round(ht*r.x),Math.round(ht*r.y)),a=this._vertexBoundsScale,{bounds:l,background:h,glyphs:u}=i;u.length>0&&(this._borderLineColor||this._backgroundColor)&&(s.textureBinding=u[0].textureBinding,t.recordStart(e,s.data,this.geometryType,!0),this._writeBackgroundGeometry(t,e,r,l,h),t.recordEnd());const c=2*Math.max(l.width,l.height);for(const f of i.glyphs)s.textureBinding=f.textureBinding,t.recordStart(e,s.data,this.geometryType,!0),t.vertexBounds(r.x+l.x+this._xOffset,r.y+l.y-this._yOffset,c*a,c*a),this._writeVertices(t,e,n,f),t.recordEnd()}_writeGlyph(t,e,r,i,s){const n=ee.load(this._materialKey),a=M(Math.round(ht*r),Math.round(ht*i));n.textureBinding=s.textureBinding,t.recordStart(e,n.data,this.geometryType,!0);const l=s.bounds,h=this._vertexBoundsScale;t.vertexBounds(r+l.x*h,i+l.y*h,l.width*h,l.height*h),this._writeVertices(t,e,a,s),t.recordEnd()}_writeVertices(t,e,r,i){const s=t.vertexCount();this._writeVertexCommon(t,e,r,i),t.vertexWrite(i.offsets.upperLeft),t.vertexWrite(i.texcoords.upperLeft),t.vertexEnd(),this._writeVertexCommon(t,e,r,i),t.vertexWrite(i.offsets.upperRight),t.vertexWrite(i.texcoords.upperRight),t.vertexEnd(),this._writeVertexCommon(t,e,r,i),t.vertexWrite(i.offsets.lowerLeft),t.vertexWrite(i.texcoords.lowerLeft),t.vertexEnd(),this._writeVertexCommon(t,e,r,i),t.vertexWrite(i.offsets.lowerRight),t.vertexWrite(i.texcoords.lowerRight),t.vertexEnd(),t.indexWrite(s+0),t.indexWrite(s+1),t.indexWrite(s+2),t.indexWrite(s+1),t.indexWrite(s+3),t.indexWrite(s+2)}_writeVertexCommon(t,e,r,i){const s=this._color,n=this._haloColor,a=W(0,0,this._referenceSize,this._bitset),l=W(0,0,this._size,this._haloSize);t.vertexWrite(r),t.vertexWrite(e),t.vertexWrite(s),t.vertexWrite(n),t.vertexWrite(l),t.vertexWrite(a),t.vertexWrite(this._minMaxZoom)}_writeBackgroundVertex(t,e,r,i,s,n){const a=W(0,1,this._referenceSize,this._bitset),l=W(0,0,this._size,this._haloSize),h=W(0,0,0,0);t.vertexWrite(r),t.vertexWrite(e),t.vertexWrite(i),t.vertexWrite(h),t.vertexWrite(l),t.vertexWrite(a),t.vertexWrite(this._minMaxZoom),t.vertexWrite(s),t.vertexWrite(n),t.vertexEnd()}_writeBackgroundQuad(t,e,r,i,s,n){const a=t.vertexCount();this._writeBackgroundVertex(t,e,r,i,s.upperLeft,n[0]),this._writeBackgroundVertex(t,e,r,i,s.upperRight,n[1]),this._writeBackgroundVertex(t,e,r,i,s.lowerLeft,n[2]),this._writeBackgroundVertex(t,e,r,i,s.lowerRight,n[3]),t.indexWrite(a+0),t.indexWrite(a+1),t.indexWrite(a+2),t.indexWrite(a+1),t.indexWrite(a+3),t.indexWrite(a+2)}_writeBackgroundGeometry(t,e,r,i,s){const n=M(Math.round(ht*r.x),Math.round(ht*r.y)),{x:a,y:l,width:h,height:u}=i,c=2*Math.max(h,u);if(t.vertexBounds(r.x+a+this._xOffset,r.y+l-this._yOffset,c*this._vertexBoundsScale,c*this._vertexBoundsScale),this._backgroundColor){const f=[V,V,V,V];this._writeBackgroundQuad(t,e,n,this._backgroundColor,s.main,f)}if(this._borderLineColor||this._backgroundColor){const f=!!this._borderLineColor&&!!this._borderLineSize&&this._borderLineSize>0,[d,_,m,x,p]=f?[Ze,Ze,Ne,Ne,this._borderLineColor]:[Ki,Zi,Ni,Xi,this._backgroundColor];this._writeBackgroundQuad(t,e,n,p,s.top,d),this._writeBackgroundQuad(t,e,n,p,s.bot,_),this._writeBackgroundQuad(t,e,n,p,s.left,m),this._writeBackgroundQuad(t,e,n,p,s.right,x)}}};let Lt=class{constructor(){this._materialKey=null}bindFeature(t,e,r){}write(t,e,r,i){var a;if(O(this._effects)||((a=this._effects)==null?void 0:a.length)===0)return this._write(t,e,i);const s=Yt.executeEffects(this._effects,e.readLegacyGeometryForDisplay(),t.tileKey,i.geometryEngine);let n=Yt.next(s);for(;n;)this._write(t,e,i,n),n=Yt.next(s)}_write(t,e,r,i){}};const Ui=5;let ne=class oe extends vr(Lt){constructor(t,e,r,i,s,n,a,l,h,u,c,f,d,_,m,x,p,y,v,w,L,S,T,$){super(),this._xOffset=g(d),this._yOffset=g(_),this._decoration=u||"none",this._backgroundColor=S,this._borderLineColor=T,this._borderLineSize=$,this._color=s,this._haloColor=n,this._haloSize=Math.min(Math.floor(Ui*g(Fr(r))),127),this._size=Math.min(Math.round(g(e)),127);const G=Math.min(Math.round(g(i||e)),127);this._referenceSize=Math.round(Math.sqrt(256*G)),this._scale=this._size/ar,this._angle=f,this._justify=yi(a||"center"),this._xAlignD=_r(a||"center"),this._yAlignD=fr(l||"baseline"),this._baseline=(l||"baseline")==="baseline",this._bitset=(h===Z.MAP?1:0)|(c?1:0)<<1;const K=ee.load(t);K.sdf=!0,this._materialKey=K.data,this._lineWidth=g(m)||512,this._lineHeight=x||1,this._textPlacement=p,this._effects=y,this._isCIM=v??!1,this._minMaxZoom=M(Math.round(w*k),Math.round(L*k))}static fromText(t,e){var n,a;const r=(n=t.font)==null?void 0:n.size,i=new oe(t.materialKey,r,t.haloSize||0,r,t.color&&F(t.color)||0,t.haloColor&&F(t.haloColor)||0,t.horizontalAlignment,t.verticalAlignment,Z.SCREEN,(a=t.font)==null?void 0:a.decoration,!1,t.angle||0,t.xoffset||0,t.yoffset||0,t.lineWidth||0,t.lineHeight||0,null,null,!1,X,U,t.backgroundColor&&F(t.backgroundColor),t.borderLineColor&&F(t.borderLineColor),t.borderLineSize),[,s]=Ee(t.text);return i.bindTextInfo(e??[],s),i._vertexBoundsScale=t.maxVVSize&&r?t.maxVVSize/r:1,i}static fromCIMText(t,e,r){const i=t.scaleFactor||1,s=t.size*t.sizeRatio*i,[n,a]=tt(t.scaleInfo,r),l=new oe(t.materialKey,s,t.outlineSize*t.sizeRatio,t.referenceSize,z(t.color),z(t.outlineColor),t.horizontalAlignment,t.verticalAlignment,t.alignment,t.decoration,t.colorLocked??!1,t.angle,t.offsetX*t.sizeRatio*i,t.offsetY*t.sizeRatio*i,512,1,t.markerPlacement,t.effects,!0,n,a,t.backgroundColor?z(t.backgroundColor):void 0,t.borderLineColor?z(t.borderLineColor):void 0,t.borderLineWidth),[,h]=Ee(t.text);return l.bindTextInfo(e,h),l._vertexBoundsScale=t.maxVVSize?t.maxVVSize/s:1,l}};const wr=xt.getLogger("esri.views.2d.engine.webgl.WGLLabelTemplate"),Hi=(o,t="mapview-labeling")=>wr.error(new ot(t,o)),It=1,ct=0,Yi=4,qt=25;function qi(o,t){const e=!!o.minScale&&t.scaleToZoom(o.minScale)||0;return sr(e,0,25.5)}function ji(o,t){const e=!!o.maxScale&&t.scaleToZoom(o.maxScale)||255;return sr(e,0,25.5)}function Qi(o){const t=new Map;return e=>(t.has(e)||t.set(e,o(e)),t.get(e))}const Ji=Qi(o=>{let t=0;if(o===0)return 1/0;for(;!(o%2);)t++,o/=2;return t}),Ct=o=>Math.floor(127*o+127),et=o=>Math.floor(10*o),Q=o=>Math.round(o*(254/360));let Xe=class br extends ne{constructor(t,e,r,i){var c,f,d;super(t,(c=r.font)==null?void 0:c.size,r.haloSize||0,(f=r.font)==null?void 0:f.size,r.color&&F(r.color)||0,r.haloColor&&F(r.haloColor)||0,r.horizontalAlignment,r.verticalAlignment,gi(e.labelPlacement)?Z.MAP:Z.SCREEN,(d=r.font)==null?void 0:d.decoration,!1,r.angle||0,r.xoffset,r.yoffset,r.lineWidth,r.lineHeight,null,null,!1,null,null,r.backgroundColor&&F(r.backgroundColor),r.borderLineColor&&F(r.borderLineColor),r.borderLineSize),this._outLineLabelAngle=0,this._refPlacementPadding=0,this._refPlacementDirX=0,this._refPlacementDirY=0,this._refOffsetX=0,this._refOffsetY=0,this._zoomLevel=0,this.geometryType=P.LABEL,this._allowOverrun=e.allowOverrun??!1,this._repeatLabel=e.repeatLabel??!0,this._labelPosition=e.labelPosition??"curved";const s=qi(e,i),n=ji(e,i),a=e.labelPlacement,[l,h]=Mi(a);this._xAlignD=l,this._yAlignD=h,this._minZoom=s,this._maxZoom=n,this._minBackgroundZoom=s,this._maxBackgroundZoom=n,this._refPlacementPadding=g(r.haloSize)+ei,this._repeatLabelDistance=e.repeatLabelDistance?g(e.repeatLabelDistance):128;const u=Ht.load(t);u.sdf=!0,this._materialKey=u.data}static fromLabelClass(t,e){if(t.labelPlacement==="esriServerLinePlacementCenterAlong"){const r=t.symbol;r.xoffset=0,r.yoffset=0,r.angle=0,r.font.decoration="none"}return new br(t.materialKey,t,t.symbol,e)}get _shapedBox(){return me(this._shapingInfo).bounds}setZoomLevel(t){this._zoomLevel=t}bindReferenceTemplate(t){let e=vi(this._xAlignD),r=wi(this._yAlignD);if(this._refOffsetX=0,this._refOffsetY=0,O(t))return void(this._refSymbolAndPlacementOffset=W(0,0,Ct(e),Ct(r)));if(t.boundsType==="circle"&&(e||r)){const n=Math.sqrt(e*e+r*r);e/=n,r/=n}const i=Math.max(t.height,t.width),s=this._refPlacementPadding*Yi;this._refSymbolAndPlacementOffset=W(s,i,Ct(e),Ct(r)),this._referenceSize=i,this._refPlacementDirX=e,this._refPlacementDirY=r,this._refOffsetX=t.xOffset,this._refOffsetY=t.yOffset}_write(t,e){if(O(this._shapingInfo))return;const r=this._shapingInfo,i=e.getDisplayId(),s=e.geometryType==="esriGeometryPolygon"?e.readLegacyCentroid():e.readLegacyGeometry();if(s)switch(this._current={out:t,inId:i,inShaping:r,zoomLevel:this._zoomLevel},e.geometryType==="esriGeometryPolyline"&&this._labelPosition==="curved"&&(this._borderLineColor||this._backgroundColor)&&wr.warnOnce("TextSymbol properties 'borderLineColor', 'borderLineSize', and 'backgroundColor' are not supported in curved labels"),e.geometryType){case"esriGeometryPolyline":this._placeLineLabels(s);break;case"esriGeometryPoint":case"esriGeometryPolygon":this._placePointLabels(s);break;default:Hi(`Geometry of type ${e.geometryType} is not supported`)}}_isVisible(t,e){const r=et(this._current.zoomLevel);return et(t)<=r&&r<=et(e)}_placePointLabels(t){const{out:e,inId:r,inShaping:i}=this._current;this._writeGlyphs(e,r,t,i)}_placeLineLabels(t){const e=Di(t.paths,this._current.inShaping.bounds.width),r=this._placeSubdivGlyphs.bind(this),i=(this._shapedBox.width+this._repeatLabelDistance)/(1<<It);for(const s of e)Oi(s,i,r,this._repeatLabel)}_placeSubdivGlyphs(t,e,r,i){const s=Ji(e),n=this._shapedBox.width/(1<<It),a=Math.sqrt(this._repeatLabelDistance)/(1<<It),l=Math.min(r,i-r),h=this._current.inShaping.isMultiline?qt:Math.log2(l/(a+n/2)),u=e===0?h:Math.min(s,h),c=Math.max(this._minZoom,this._current.zoomLevel+It-u),f=this._current.zoomLevel-c,d=this._shapedBox.width/2*2**f;this._current.inShaping.isMultiline?e===0&&this._placeStraight(t,c):this._allowOverrun&&f<0?this._placeStraightAlong(t,this._minZoom):this._labelPosition==="parallel"?this._placeStraightAlong(t,c):this._labelPosition==="curved"&&this._placeCurved(t,c,d)}_placeStraight(t,e){const{out:r,inId:i,inShaping:s}=this._current,n=Math.ceil(t.angle*(180/Math.PI)%360),a=Math.ceil((t.angle*(180/Math.PI)+180)%360);this._outLineLabelAngle=Q(n),this._writeGlyphs(r,i,t,s,e),this._outLineLabelAngle=Q(a),this._writeGlyphs(r,i,t,s,e)}_placeCurved(t,e,r){const{out:i,inId:s}=this._current;i.metricStart(s,e,t.x,t.y,0,0,0,0);const n=t.clone(),a=t.angle*(180/Math.PI)%360,l=(t.angle*(180/Math.PI)+180)%360;this._outLineLabelAngle=Q(a),this._placeFirst(n,e,1),this._placeBack(t,n,e,r,1),this._placeForward(t,n,e,r,1),this._outLineLabelAngle=Q(l),this._placeFirst(n,e,0),this._placeBack(t,n,e,r,0),this._placeForward(t,n,e,r,0),i.metricEnd()}_placeStraightAlong(t,e){const{out:r,inId:i,inShaping:s}=this._current;r.metricStart(i,e,t.x,t.y,0,0,0,0);const n=t.clone(),a=t.angle*(180/Math.PI)%360,l=(t.angle*(180/Math.PI)+180)%360,h=s.glyphs.length>0&&(this._borderLineColor||this._backgroundColor);if(this._maxBackgroundZoom=qt,this._minBackgroundZoom=Math.max(e,0),h){const u=Ht.load(this._materialKey);u.textureBinding=s.glyphs[0].textureBinding;const c=Vr(Ot(),-t.angle),[f,d]=s.shapeBackground(c);this._outLineLabelAngle=Q(a),r.recordStart(i,u.data,this.geometryType,!0),this._writeBackgroundGeometry(r,i,t,f,d),r.recordEnd(),this._outLineLabelAngle=Q(l),r.recordStart(i,u.data,this.geometryType,!0),this._writeBackgroundGeometry(r,i,t,f,d),r.recordEnd()}this._outLineLabelAngle=Q(a),this._placeFirst(n,e,1,!0),this._outLineLabelAngle=Q(l),this._placeFirst(n,e,0,!0),r.metricEnd()}_placeBack(t,e,r,i,s){const n=t.clone();let a=t.backwardLength+ct;for(;n.prev()&&!(a>=i);)this._placeOnSegment(n,e,a,r,-1,s),a+=n.length+ct}_placeForward(t,e,r,i,s){const n=t.clone();let a=t.remainingLength+ct;for(;n.next()&&!(a>=i);)this._placeOnSegment(n,e,a,r,1,s),a+=n.length+ct}_placeFirst(t,e,r,i=!1){const s=t,n=this._current.inShaping,a=n.glyphs,l=this._current.zoomLevel,{out:h,inId:u}=this._current;for(const c of a){const f=c.x>n.bounds.x?r:1-r,d=f*t.remainingLength+(1-f)*t.backwardLength,_=Math.abs(c.x+c.width/2-n.bounds.x),m=Math.max(0,l+Math.log2(_/(d+ct))),x=Math.max(e,i?0:m);if(c.maxZoom=qt,c.angle=t.angle+(1-r)*Math.PI,c.minZoom=x,this._writeGlyph(h,u,s.x,s.y,c),r&&this._isVisible(c.minZoom,c.maxZoom)){const p=c.bounds;h.metricBoxWrite(p.center[0],p.center[1],p.width,p.height)}}}_placeOnSegment(t,e,r,i,s,n){const a=this._current.inShaping.glyphs,{out:l,inId:h}=this._current,u=this._current.inShaping,c=this._current.zoomLevel,f=t.dx/t.length,d=t.dy/t.length,_={x:t.x+r*-s*f,y:t.y+r*-s*d};for(const m of a){const x=m.x>u.bounds.x?n:1-n;if(!(x&&s===1||!x&&s===-1))continue;const p=Math.abs(m.x+m.width/2-u.bounds.x),y=Math.max(0,c+Math.log2(p/r)-.1),v=Math.max(i,c+Math.log2(p/(r+t.length+ct)));if(y!==0&&(m.angle=t.angle+(1-n)*Math.PI,m.minZoom=v,m.maxZoom=y,this._writeGlyph(l,h,_.x,_.y,m),n&&this._isVisible(m.minZoom,m.maxZoom))){const w=m.bounds,L=t.x-e.x,S=t.y-e.y;l.metricBoxWrite(w.center[0]+L,w.center[1]+S,w.width,w.height)}}}_writeGlyphs(t,e,r,i,s=this._minZoom){if(r.x<0||r.x>=512||r.y<0||r.y>=512)return;if(i.glyphs.length>0&&(this._borderLineColor||this._backgroundColor)){const c=Ht.load(this._materialKey);c.textureBinding=i.glyphs[0].textureBinding,t.recordStart(e,c.data,this.geometryType,!0),this._writeBackgroundGeometry(t,e,r,i.bounds,i.background),t.recordEnd()}const n=r.x+this._refOffsetX,a=r.y-this._refOffsetY;for(const c of i.glyphs)c.minZoom=s,c.maxZoom=this._maxZoom,this._writeGlyph(t,e,n,a,c);const l=this._refPlacementDirX,h=this._refPlacementDirY,u=i.boundsT;t.metricStart(e,s,n,a,l,h,this._referenceSize,this._materialKey),t.metricBoxWrite(u.center[0],u.center[1],u.width,u.height),t.metricEnd()}_writeVertexCommon(t,e,r,i){const s=this._color,n=this._haloColor,a=W(0,0,this._size,this._haloSize),l=Math.max(i.minZoom,this._minZoom),h=Math.min(i.maxZoom,this._maxZoom),u=W(et(l),et(h),this._outLineLabelAngle,0);t.vertexWrite(r),t.vertexWrite(e),t.vertexWrite(s),t.vertexWrite(n),t.vertexWrite(a),t.vertexWrite(this._refSymbolAndPlacementOffset),t.vertexWrite(u)}_writeBackgroundVertex(t,e,r,i,s,n){const a=W(0,0,this._size,this._haloSize),l=W(0,0,0,0),h=W(et(this._minBackgroundZoom),et(this._maxBackgroundZoom),this._outLineLabelAngle,1);t.vertexWrite(r),t.vertexWrite(e),t.vertexWrite(i),t.vertexWrite(l),t.vertexWrite(a),t.vertexWrite(this._refSymbolAndPlacementOffset),t.vertexWrite(h),t.vertexWrite(s),t.vertexWrite(n),t.vertexEnd()}};const Ue=3.14159265359/180,He=8,Sr=o=>class extends o{constructor(...t){super(...t),this.angle=0,this.xOffset=0,this.yOffset=0,this.width=0,this.height=0,this.boundsType="square",this._anchorX=0,this._anchorY=0,this._computedWidth=0,this._computedHeight=0,this._allowBorrowing=!0,this._vertexBoundsScaleX=1,this._vertexBoundsScaleY=1,this._offsets={xUpperLeft:0,yUpperLeft:0,xUpperRight:0,yUpperRight:0,xBottomLeft:0,yBottomLeft:0,xBottomRight:0,yBottomRight:0},this.geometryType=P.MARKER}_write(t,e,r,i){const s=e.getDisplayId();t.recordStart(s,this._materialKey,this.geometryType,!0),this._writeGeometry(t,e,s,r,i),t.recordEnd()}_writeGeometry(t,e,r,i,s){if(A(this._markerPlacement))return this._writePlacedMarkers(t,e,i,s);if(this._allowBorrowing=!0,!s&&e.geometryType==="esriGeometryPoint"){const a=e.getX(),l=e.getY();return!t.hasAggregates&&t.hasPixelBufferEnabled&&(a<0||a>=513||l<0||l>=513)?void 0:this._writeVertices(t,r,this._getPos(a,l),a,l)}const n=s?de(_e(s),2):e.geometryType==="esriGeometryPolygon"?e.readCentroid():e.readGeometryForDisplay();if(!O(n)){if(n.isPoint){const[a,l]=n.coords;return!t.hasAggregates&&t.hasPixelBufferEnabled&&(a<0||a>=512||l<0||l>=512)?void 0:this._writeVertices(t,r,this._getPos(a,l),a,l)}n.forEachVertex((a,l)=>{const h=2*nt;a<-h||a>=h||l<-h||l>=h||this._writeVertices(t,r,this._getPos(a,l),a,l)})}}_writePlacedMarkers(t,e,r,i){const s=i??e.readLegacyGeometryForDisplay(),n=Mr.getPlacement(s,me(this._markerPlacement),g(1),t.tileKey,r.geometryEngine);if(!n)return;this._allowBorrowing=e.geometryType!=="esriGeometryPolygon";const a=e.getDisplayId(),l=fe(),h=Ot(),u=-128,c=640;let f=n.next();for(;f!=null;){const d=f.tx,_=-f.ty;d>=u&&d<=c&&_>=u&&_<=c&&(this._applyTransformation(h,l,-f.getAngle()/Ue),this._writeVertices(t,a,this._getPos(d,_),d,_)),f=n.next()}}_writeVertices(t,e,r,i,s){const n=xe.load(this._materialKey);return n.symbologyType===Gt.HEATMAP?this._writeHeatmapVertices(t,e,r):this._writeMarkerVertices(t,e,n,r,i,s)}_writeMarkerVertices(t,e,r,i,s,n){const a=r.vvRotation,l=t.vertexCount();let h=this._computedWidth*this._vertexBoundsScaleX,u=this._computedHeight*this._vertexBoundsScaleY;if(this.angle){const c=Math.max(h,u);h=c,u=c}if(a){const c=Math.max(this.xOffset,this.yOffset);h+=c,u+=c}this._allowBorrowing&&t.vertexBounds(s+this.xOffset,n-this.yOffset,h,u),t.vertexWrite(i),t.vertexWrite(this._offsetUpperLeft),t.vertexWrite(this._texUpperLeft),t.vertexWrite(this._bitestAndDistRatio),t.vertexWrite(e),t.vertexWrite(this._fillColor),t.vertexWrite(this._outlineColor),t.vertexWrite(this._sizeOutlineWidth),t.vertexWrite(this._minMaxZoom),t.vertexEnd(),t.vertexWrite(i),t.vertexWrite(this._offsetUpperRight),t.vertexWrite(this._texUpperRight),t.vertexWrite(this._bitestAndDistRatio),t.vertexWrite(e),t.vertexWrite(this._fillColor),t.vertexWrite(this._outlineColor),t.vertexWrite(this._sizeOutlineWidth),t.vertexWrite(this._minMaxZoom),t.vertexEnd(),t.vertexWrite(i),t.vertexWrite(this._offsetBottomLeft),t.vertexWrite(this._texBottomLeft),t.vertexWrite(this._bitestAndDistRatio),t.vertexWrite(e),t.vertexWrite(this._fillColor),t.vertexWrite(this._outlineColor),t.vertexWrite(this._sizeOutlineWidth),t.vertexWrite(this._minMaxZoom),t.vertexEnd(),t.vertexWrite(i),t.vertexWrite(this._offsetBottomRight),t.vertexWrite(this._texBottomRight),t.vertexWrite(this._bitestAndDistRatio),t.vertexWrite(e),t.vertexWrite(this._fillColor),t.vertexWrite(this._outlineColor),t.vertexWrite(this._sizeOutlineWidth),t.vertexWrite(this._minMaxZoom),t.vertexEnd(),this._writeIndices(t,l)}_writeHeatmapVertices(t,e,r){const i=t.vertexCount();t.vertexWrite(r),t.vertexWrite(this._offsetUpperLeft),t.vertexWrite(e),t.vertexEnd(),t.vertexWrite(r),t.vertexWrite(this._offsetUpperRight),t.vertexWrite(e),t.vertexEnd(),t.vertexWrite(r),t.vertexWrite(this._offsetBottomLeft),t.vertexWrite(e),t.vertexEnd(),t.vertexWrite(r),t.vertexWrite(this._offsetBottomRight),t.vertexWrite(e),t.vertexEnd(),this._writeIndices(t,i)}_writeIndices(t,e){t.indexWrite(e+0),t.indexWrite(e+1),t.indexWrite(e+2),t.indexWrite(e+1),t.indexWrite(e+3),t.indexWrite(e+2)}_applyTransformation(t,e,r=0){Or(t,Dr(this.xOffset,-this.yOffset)),this.angle!=null&&this.angle+r!==0&&Gr(t,t,Ue*(this.angle+r));const i=this._computedWidth,s=this._computedHeight,n=-(.5+this._anchorX)*i,a=-(.5-this._anchorY)*s;Pt(e,n,a),zt(e,e,t),this._offsetUpperLeft=M(16*e[0],16*e[1]),this._offsets.xUpperLeft=e[0],this._offsets.yUpperLeft=e[1],Pt(e,n+i,a),zt(e,e,t),this._offsetUpperRight=M(16*e[0],16*e[1]),this._offsets.xUpperRight=e[0],this._offsets.yUpperRight=e[1],Pt(e,n,a+s),zt(e,e,t),this._offsetBottomLeft=M(16*e[0],16*e[1]),this._offsets.xBottomLeft=e[0],this._offsets.yBottomLeft=e[1],Pt(e,n+i,a+s),zt(e,e,t),this._offsetBottomRight=M(16*e[0],16*e[1]),this._offsets.xBottomRight=e[0],this._offsets.yBottomRight=e[1]}_getPos(t,e){return M(Math.round(He*t),Math.round(He*e))}};let yt=class gt extends Sr(Lt){constructor(t,e,r,i,s,n,a,l,h,u,c,f,d,_,m,x,p,y,v,w,L,S,T){super(),this.angle=i,this.height=a,this.width=n,this.xOffset=e*v,this.yOffset=r*v,this._markerPlacement=w,this._effects=L,this._anchorX=x,this._anchorY=p,this._minMaxZoom=M(Math.round(S*k),Math.round(T*k));const $=(_===Z.MAP?ri:ii)|(c?st:0)|(d?si:0)|(f?ni:0),G=m&&m.sdf,K=xe.load(t);K.sdf=G,K.pattern=!0,K.textureBinding=m.textureBinding,this._materialKey=K.data,this._fillColor=s,this._outlineColor=h,this._sizeOutlineWidth=W(Math.round(Math.min(Math.sqrt(128*n),255)),Math.round(Math.min(Math.sqrt(128*a),255)),Math.round(Math.min(Math.sqrt(128*u),255)),Math.round(Math.min(Math.sqrt(128*l),255)));const j=m.rect.x+I,N=m.rect.y+I,at=j+m.width,lt=N+m.height;this._offsets.xUpperLeft=j,this._offsets.yUpperLeft=N,this._offsets.xUpperRight=at,this._offsets.yUpperRight=N,this._offsets.xBottomLeft=j,this._offsets.yBottomLeft=lt,this._offsets.xBottomRight=at,this._offsets.yBottomRight=lt,K.symbologyType===Gt.PIE_CHART?(this._texUpperLeft=M(0,1),this._texUpperRight=M(1,1),this._texBottomLeft=M(0,0),this._texBottomRight=M(1,0)):(this._texUpperLeft=M(j,N),this._texUpperRight=M(at,N),this._texBottomLeft=M(j,lt),this._texBottomRight=M(at,lt)),n*=y,a*=y,n*=v,a*=v;const Er=Math.round(64*y);this._bitestAndDistRatio=M($,Er),this._computedWidth=n,this._computedHeight=a;const Br=fe(),Ar=Ot();this._applyTransformation(Ar,Br)}static fromCIMMarker(t,e,r){const i=e&&e.width||1,s=e&&e.height||1,n=t.size,a=i/s*t.scaleX,l=t.scaleSymbolsProportionally&&t.frameHeight?n/t.frameHeight:1,h=z(t.color),u=z(t.outlineColor),c=g(n),f=c*a,d=g(t.offsetX||0),_=g(t.offsetY||0),m=g(t.outlineWidth||0)*l,x=t.alignment||Z.SCREEN,p=g(t.referenceSize),[y,v]=tt(t.scaleInfo,r);let w=t.rotation||0;t.rotateClockwise||(w=-w);let L=0,S=0;const T=t.anchorPoint;T&&(t.isAbsoluteAnchorPoint?n&&(L=T.x/(n*a),S=T.y/n):(L=T.x,S=T.y));const $=new gt(t.materialKey,d,_,w,h,f,c,p,u,m,t.colorLocked,t.scaleSymbolsProportionally,!1,x,e,L,S,t.sizeRatio,Et(t.scaleFactor,1),t.markerPlacement,t.effects,y,v);return $._vertexBoundsScaleX=t.maxVVSize?t.maxVVSize/f:1,$._vertexBoundsScaleY=t.maxVVSize?t.maxVVSize/c:1,$}static fromPictureMarker(t,e){const r=Math.round(g(t.width)),i=Math.round(g(t.height)),s=lr,n=Math.round(g(t.xoffset||0)),a=Math.round(g(t.yoffset||0)),l=new gt(t.materialKey,n,a,t.angle,s,r,i,i,0,0,!1,!1,!1,Z.SCREEN,e,0,0,1,1,null,null,X,U);return l._vertexBoundsScaleX=t.maxVVSize?t.maxVVSize/t.width:1,l._vertexBoundsScaleY=t.maxVVSize?t.maxVVSize/t.height:1,l}static fromSimpleMarker(t,e){const r=F(t.color),i=Math.round(g(t.size)),s=i,n=Math.round(g(t.xoffset||0)),a=Math.round(g(t.yoffset||0)),l=t.style,h=t.outline,u=0|((h==null?void 0:h.color)&&F(h.color)),c=0|((h==null?void 0:h.width)&&Math.round(g(h.width))),f=new gt(t.materialKey,n,a,t.angle,r,i,s,s,u,c,!1,!1,l==="esriSMSCross"||l==="esriSMSX",Z.SCREEN,e,0,0,126/64,1,null,null,X,U);return f.boundsType=l==="esriSMSCircle"?"circle":"square",f._vertexBoundsScaleX=t.maxVVSize?t.maxVVSize/t.size:1,f._vertexBoundsScaleY=t.maxVVSize?t.maxVVSize/t.size:1,f}static fromLineSymbolMarker(t,e){const r=F(t.color),i=6,s=Math.round(g(i*t.lineWidth)),n=s,a=t.style==="cross"||t.style==="x";let l;switch(t.placement){case"begin-end":l=$t.Both;break;case"begin":l=$t.JustBegin;break;case"end":l=$t.JustEnd;break;default:l=$t.None}const h={type:"CIMMarkerPlacementAtExtremities",angleToLine:!0,offset:0,extremityPlacement:l,offsetAlongLine:0},u=new gt(t.materialKey,0,0,0,r,s,n,n/i,r,a?Math.round(g(t.lineWidth)):0,!1,!1,a,Z.MAP,e,0,0,126/64,1,h,null,X,U);return u.boundsType=t.style==="circle"?"circle":"square",u}};function ts(o,t,e,r,i,s,n){he=0;const a=(r-e)*s,l=i&&i.length,h=l?(i[0]-e)*s:a;let u,c,f,d,_,m=Lr(t,e,r,0,h,s,!0);if(m&&m.next!==m.prev){if(l&&(m=ss(t,e,r,i,m,s)),a>80*s){u=f=t[0+e*s],c=d=t[1+e*s];for(let x=s;x<h;x+=s){const p=t[x+e*s],y=t[x+1+e*s];u=Math.min(u,p),c=Math.min(c,y),f=Math.max(f,p),d=Math.max(d,y)}_=Math.max(f-u,d-c),_=_!==0?1/_:0}wt(m,o,s,u,c,_,n,0)}}function Lr(o,t,e,r,i,s,n){let a;if(n===hs(o,t,e,r,i,s)>0)for(let l=r;l<i;l+=s)a=Ye(l+t*s,o[l+t*s],o[l+1+t*s],a);else for(let l=i-s;l>=r;l-=s)a=Ye(l+t*s,o[l+t*s],o[l+1+t*s],a);return a&&it(a,a.next)&&(bt(a),a=a.next),a}function vt(o,t=o){if(!o)return o;let e,r=o;do if(e=!1,r.steiner||!it(r,r.next)&&C(r.prev,r,r.next)!==0)r=r.next;else{if(bt(r),r=t=r.prev,r===r.next)break;e=!0}while(e||r!==t);return t}function wt(o,t,e,r,i,s,n,a){if(!o)return;!a&&s&&(o=Pr(o,r,i,s));let l=o;for(;o.prev!==o.next;){const h=o.prev,u=o.next;if(s?rs(o,r,i,s):es(o))t.push(h.index/e+n),t.push(o.index/e+n),t.push(u.index/e+n),bt(o),o=u.next,l=u.next;else if((o=u)===l){a?a===1?wt(o=us(o,t,e,n),t,e,r,i,s,n,2):a===2&&ds(o,t,e,r,i,s,n):wt(vt(o),t,e,r,i,s,n,1);break}}}function es(o){const t=o.prev,e=o,r=o.next;if(C(t,e,r)>=0)return!1;let i=o.next.next;const s=i;let n=0;for(;i!==o.prev&&(n===0||i!==s);){if(n++,ft(t.x,t.y,e.x,e.y,r.x,r.y,i.x,i.y)&&C(i.prev,i,i.next)>=0)return!1;i=i.next}return!0}function rs(o,t,e,r){const i=o.prev,s=o,n=o.next;if(C(i,s,n)>=0)return!1;const a=i.x<s.x?i.x<n.x?i.x:n.x:s.x<n.x?s.x:n.x,l=i.y<s.y?i.y<n.y?i.y:n.y:s.y<n.y?s.y:n.y,h=i.x>s.x?i.x>n.x?i.x:n.x:s.x>n.x?s.x:n.x,u=i.y>s.y?i.y>n.y?i.y:n.y:s.y>n.y?s.y:n.y,c=ae(a,l,t,e,r),f=ae(h,u,t,e,r);let d=o.prevZ,_=o.nextZ;for(;d&&d.z>=c&&_&&_.z<=f;){if(d!==o.prev&&d!==o.next&&ft(i.x,i.y,s.x,s.y,n.x,n.y,d.x,d.y)&&C(d.prev,d,d.next)>=0||(d=d.prevZ,_!==o.prev&&_!==o.next&&ft(i.x,i.y,s.x,s.y,n.x,n.y,_.x,_.y)&&C(_.prev,_,_.next)>=0))return!1;_=_.nextZ}for(;d&&d.z>=c;){if(d!==o.prev&&d!==o.next&&ft(i.x,i.y,s.x,s.y,n.x,n.y,d.x,d.y)&&C(d.prev,d,d.next)>=0)return!1;d=d.prevZ}for(;_&&_.z<=f;){if(_!==o.prev&&_!==o.next&&ft(i.x,i.y,s.x,s.y,n.x,n.y,_.x,_.y)&&C(_.prev,_,_.next)>=0)return!1;_=_.nextZ}return!0}function Ye(o,t,e,r){const i=pt.create(o,t,e);return r?(i.next=r.next,i.prev=r,r.next.prev=i,r.next=i):(i.prev=i,i.next=i),i}function bt(o){o.next.prev=o.prev,o.prev.next=o.next,o.prevZ&&(o.prevZ.nextZ=o.nextZ),o.nextZ&&(o.nextZ.prevZ=o.prevZ)}function is(o){let t=o,e=o;do(t.x<e.x||t.x===e.x&&t.y<e.y)&&(e=t),t=t.next;while(t!==o);return e}function ss(o,t,e,r,i,s){const n=new Array;for(let a=0,l=r.length;a<l;a++){const h=Lr(o,t,e,r[a]*s,a<l-1?r[a+1]*s:e*s,s,!1);h===h.next&&(h.steiner=!0),n.push(is(h))}n.sort(cs);for(const a of n)i=ns(a,i);return i}function ns(o,t){const e=os(o,t);if(!e)return t;const r=Tr(e,o);return vt(r,r.next),vt(e,e.next)}function os(o,t){let e=t;const r=o.x,i=o.y;let s,n=-1/0;do{if(i<=e.y&&i>=e.next.y&&e.next.y!==e.y){const f=e.x+(i-e.y)*(e.next.x-e.x)/(e.next.y-e.y);if(f<=r&&f>n){if(n=f,f===r){if(i===e.y)return e;if(i===e.next.y)return e.next}s=e.x<e.next.x?e:e.next}}e=e.next}while(e!==t);if(!s)return null;if(r===n)return s.prev;const a=s,l=s.x,h=s.y;let u,c=1/0;for(e=s.next;e!==a;)r>=e.x&&e.x>=l&&r!==e.x&&ft(i<h?r:n,i,l,h,i<h?n:r,i,e.x,e.y)&&(u=Math.abs(i-e.y)/(r-e.x),(u<c||u===c&&e.x>s.x)&&St(e,o)&&(s=e,c=u)),e=e.next;return s}function Pr(o,t,e,r){let i;for(;i!==o;i=i.next){if(i=i||o,i.z===null&&(i.z=ae(i.x,i.y,t,e,r)),i.prev.next!==i||i.next.prev!==i)return i.prev.next=i,i.next.prev=i,Pr(o,t,e,r);i.prevZ=i.prev,i.nextZ=i.next}return o.prevZ.nextZ=null,o.prevZ=null,as(o)}function as(o){let t,e=1;for(;;){let r,i=o;o=null,t=null;let s=0;for(;i;){s++,r=i;let n=0;for(;n<e&&r;n++)r=r.nextZ;let a=e;for(;n>0||a>0&&r;){let l;n===0?(l=r,r=r.nextZ,a--):a!==0&&r?i.z<=r.z?(l=i,i=i.nextZ,n--):(l=r,r=r.nextZ,a--):(l=i,i=i.nextZ,n--),t?t.nextZ=l:o=l,l.prevZ=t,t=l}i=r}if(t.nextZ=null,e*=2,s<2)return o}}function C(o,t,e){return(t.y-o.y)*(e.x-t.x)-(t.x-o.x)*(e.y-t.y)}function zr(o,t,e,r){return!!(it(o,t)&&it(e,r)||it(o,r)&&it(e,t))||C(o,t,e)>0!=C(o,t,r)>0&&C(e,r,o)>0!=C(e,r,t)>0}function ls(o,t){let e=o;do{if(e.index!==o.index&&e.next.index!==o.index&&e.index!==t.index&&e.next.index!==t.index&&zr(e,e.next,o,t))return!0;e=e.next}while(e!==o);return!1}function hs(o,t,e,r,i,s){let n=0;for(let a=r,l=i-s;a<i;a+=s)n+=(o[l+t*s]-o[a+t*s])*(o[a+1+t*s]+o[l+1+t*s]),l=a;return n}function ft(o,t,e,r,i,s,n,a){return(i-n)*(t-a)-(o-n)*(s-a)>=0&&(o-n)*(r-a)-(e-n)*(t-a)>=0&&(e-n)*(s-a)-(i-n)*(r-a)>=0}function St(o,t){return C(o.prev,o,o.next)<0?C(o,t,o.next)>=0&&C(o,o.prev,t)>=0:C(o,t,o.prev)<0||C(o,o.next,t)<0}function ae(o,t,e,r,i){return(o=1431655765&((o=858993459&((o=252645135&((o=16711935&((o=32767*(o-e)*i)|o<<8))|o<<4))|o<<2))|o<<1))|(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=32767*(t-r)*i)|t<<8))|t<<4))|t<<2))|t<<1))<<1}function it(o,t){return o.x===t.x&&o.y===t.y}function cs(o,t){return o.x-t.x}function us(o,t,e,r){let i=o;do{const s=i.prev,n=i.next.next;!it(s,n)&&zr(s,i,i.next,n)&&St(s,n)&&St(n,s)&&(t.push(s.index/e+r),t.push(i.index/e+r),t.push(n.index/e+r),bt(i),bt(i.next),i=o=n),i=i.next}while(i!==o);return i}function ds(o,t,e,r,i,s,n){let a=o;do{let l=a.next.next;for(;l!==a.prev;){if(a.index!==l.index&&_s(a,l)){let h=Tr(a,l);return a=vt(a,a.next),h=vt(h,h.next),wt(a,t,e,r,i,s,n,0),void wt(h,t,e,r,i,s,n,0)}l=l.next}a=a.next}while(a!==o)}function _s(o,t){return o.next.index!==t.index&&o.prev.index!==t.index&&!ls(o,t)&&St(o,t)&&St(t,o)&&fs(o,t)}function fs(o,t){let e=o,r=!1;const i=(o.x+t.x)/2,s=(o.y+t.y)/2;do e.y>s!=e.next.y>s&&e.next.y!==e.y&&i<(e.next.x-e.x)*(s-e.y)/(e.next.y-e.y)+e.x&&(r=!r),e=e.next;while(e!==o);return r}function Tr(o,t){const e=pt.create(o.index,o.x,o.y),r=pt.create(t.index,t.x,t.y),i=o.next,s=t.prev;return o.next=t,t.prev=o,e.next=i,i.prev=e,r.next=e,e.prev=r,s.next=r,r.prev=s,r}class pt{constructor(){this.index=0,this.x=0,this.y=0,this.prev=null,this.next=null,this.z=null,this.prevZ=null,this.nextZ=null,this.steiner=!1}static create(t,e,r){const i=he<le.length?le[he++]:new pt;return i.index=t,i.x=e,i.y=r,i.prev=null,i.next=null,i.z=null,i.prevZ=null,i.nextZ=null,i.steiner=!1,i}}const le=new Array,ms=8096;let he=0;for(let o=0;o<ms;o++)le.push(new pt);const ps=1e-5,rt=new pr(0,0,0,1,0),ce=new pr(0,0,0,1,0);function qe(o,t,e){let r=0;for(let i=1;i<e;i++){const s=o[2*(t+i-1)],n=o[2*(t+i-1)+1];r+=(o[2*(t+i)]-s)*(o[2*(t+i)+1]+n)}return r}function xs(o,t,e,r,i){let s=0;const n=2;for(let a=e;a<r;a+=3){const l=(o[a]-i)*n,h=(o[a+1]-i)*n,u=(o[a+2]-i)*n;s+=Math.abs((t[l]-t[u])*(t[h+1]-t[l+1])-(t[l]-t[h])*(t[u+1]-t[l+1]))}return s}function ys(o,t){const{coords:e,lengths:r,hasIndeterminateRingOrder:i}=t,s=0,n=o;if(i)return!1;let a=0;for(let l=0;l<r.length;){let h=l,u=r[l],c=qe(e,a,u);const f=[];for(;++h<r.length;){const x=r[h],p=qe(e,a+u,x);if(!(p>0))break;c+=p,f.push(a+u),u+=x}const d=n.length;ts(n,e,a,a+u,f,2,s);const _=xs(n,e,d,n.length,s),m=Math.abs(c);if(Math.abs((_-m)/Math.max(1e-7,m))>ps)return n.length=0,!1;l=h,a+=u}return!0}function gs(o){const{coords:t,lengths:e}=o,{buffer:r}=pi(t,e);return r}function Ms(o,t,e){let r=0;for(let i=0;i<o.lengths.length;i++){const s=o.lengths[i];for(let n=0;n<s;n++){const a=o.coords[2*(n+r)],l=o.coords[2*(n+r)+1];if(a<t||a>e||l<t||l>e)return!0}r+=s}return!1}function vs(o,t){if(O(o))return null;if(!Ms(o,-128,nt+128))return o;rt.setPixelMargin(t),rt.reset(xr.Polygon);let e=0;for(let n=0;n<o.lengths.length;n++){const a=o.lengths[n];let l=o.coords[2*(0+e)],h=o.coords[2*(0+e)+1];rt.moveTo(l,h);for(let u=1;u<a;u++)l=o.coords[2*(u+e)],h=o.coords[2*(u+e)+1],rt.lineTo(l,h);rt.close(),e+=a}const r=rt.result(!1);if(!r)return null;const i=[],s=[];for(const n of r){let a=0;for(const l of n)s.push(l.x),s.push(l.y),a++;i.push(a)}return new Kr(i,s)}function ws(o,t){ce.setPixelMargin(t);const e=ce,r=-t,i=nt+t;let s=[],n=!1,a=0;for(;a<o.length;){const l=[],h=o[a];if(!h)return null;e.reset(xr.LineString);let[u,c]=h[0];if(n)e.moveTo(u,c);else{if(u<r||u>i||c<r||c>i){n=!0;continue}l.push({x:u,y:c})}let f=!1;const d=h.length;for(let _=1;_<d;++_)if(u+=h[_][0],c+=h[_][1],n)e.lineTo(u,c);else{if(u<r||u>i||c<r||c>i){f=!0;break}l.push({x:u,y:c})}if(f)n=!0;else{if(n){const _=e.resultWithStarts();if(_)for(const m of _)s.push(m)}else s.push({line:l,start:0});a++,n=!1}}return s=s.filter(l=>l.line.length>1),s.length===0?null:s}rt.setExtent(nt),ce.setExtent(nt);const Ft=8,B=16,je=65535,$r=o=>class extends o{constructor(...t){super(...t),this.tessellationProperties={},this._tessellationOptions={halfWidth:0,pixelCoordRatio:1,offset:0},this.geometryType=P.LINE}writeGeometry(t,e,r,i){this._writeGeometry(t,e,r,i)}_initializeTessellator(t){const e=Bt.load(this._materialKey),r=mt.load(this._materialKey),i=this._tessellationOptions,s=e.vvSizeFieldStops||e.vvSizeMinMaxValue||e.vvSizeScaleStops||e.vvSizeUnitValue,n=this.tessellationProperties._halfWidth<oi&&!t&&!s;this.tessellationProperties.minMaxZoom=this._minMaxZoom,i.wrapDistance=je,i.textured=this._isDashed||this._hasPattern,i.offset=this.tessellationProperties.offset,i.halfWidth=this.tessellationProperties._halfWidth;const a=n?0:1,l=At(r)?Ss:bs;this._lineTessellator=new $i(l(this.tessellationProperties,a,a),Ls(this.tessellationProperties),n)}_write(t,e,r,i){const s=e.geometryType==="esriGeometryPoint";t.recordStart(e.getDisplayId(),this._materialKey,this.geometryType,s),this._writeGeometry(t,e,i,s),t.recordEnd()}_writeGeometry(t,e,r,i){const s=r??e.readLegacyGeometryForDisplay(),n=this._getLines(s,i);O(n)||this._writeVertices(t,e,n)}_getLines(t,e){if(O(t))return null;const r=t.paths||t.rings;return O(r)?null:ws(r,e?256:16)}_writeVertices(t,e,r){const i=e.getDisplayId(),s=t.vertexCount(),n=this.tessellationProperties,a=this._tessellationOptions;n.out=t,n.id=i,n.indexCount=0,n.vertexCount=0,n.offset=s,a.capType=this._capType,a.joinType=this._joinType;const l=mt.load(this._materialKey);this.tessellationProperties.key=At(l)?l:Bt.load(this._materialKey);for(const{line:h,start:u}of r)a.initialDistance=u%je,this._lineTessellator.tessellate(h,a)}},bs=(o,t,e)=>(r,i,s,n,a,l,h,u,c,f,d)=>{const _=M(d,Math.ceil(B*o._halfWidth)),m=W(Math.round(B*h),Math.round(B*u),Math.round(B*c),Math.round(B*f)),x=W(B*a,B*l,0,o._bitset),p=o.out;return p.vertexBounds(r,i,t,e),p.vertexWrite(M(Ft*r,Ft*i)),p.vertexWrite(o.id),p.vertexWrite(o._fillColor),p.vertexWrite(m),p.vertexWrite(_),p.vertexWrite(o._tl),p.vertexWrite(o._br),p.vertexWrite(x),p.vertexWrite(M(Math.ceil(B*o._halfReferenceWidth),0)),p.vertexWrite(o.minMaxZoom),p.vertexEnd(),o.offset+o.vertexCount++},Ss=(o,t,e)=>(r,i,s,n,a,l,h,u,c,f,d)=>{const _=M(B*o._halfWidth,B*o._halfReferenceWidth),m=W(B*h+128,B*u+128,B*c+128,B*f+128),x=o.out,p=o._bitset<<24|o.id;x.vertexBounds(r,i,t,e),x.vertexWrite(M(Ft*r,Ft*i)),x.vertexWrite(p),x.vertexWrite(o._fillColor);const y=mr(o.key);return y||(x.vertexWrite(0),x.vertexWrite(0)),x.vertexWrite(0),x.vertexWrite(_),x.vertexWrite(m),y||x.vertexWrite(o.minMaxZoom),x.vertexEnd(),o.offset+o.vertexCount++},Ls=o=>(t,e,r)=>{const i=o.out;i.indexWrite(t),i.indexWrite(e),i.indexWrite(r),o.indexCount+=3};class q extends $r(Lt){constructor(t,e,r,i,s,n,a,l,h,u,c,f,d,_,m,x,p,y,v,w){super();const L=Bt.load(t);e&&(L.sdf=e.sdf,L.pattern=!0,L.textureBinding=e.textureBinding),this._capType=i,this._joinType=s,this._miterLimitCosine=ie(n),this.tessellationProperties._fillColor=a,this.tessellationProperties._tl=l,this.tessellationProperties._br=h,this._hasPattern=u,this._isDashed=c,this._zOrder=p,this._effects=y,this._minMaxZoom=M(Math.round(v*k),Math.round(w*k)),this._materialKey=L.data;const S=(d?st:0)|(_?ai:0)|(f?hr:0)|(m?Dt:0);this.tessellationProperties._bitset=S,this.tessellationProperties._halfWidth=.5*r,this.tessellationProperties._halfReferenceWidth=.5*x,this.tessellationProperties.offset=0,this._initializeTessellator(!1)}static fromCIMLine(t,e,r){const i=t.color,s=t.scaleFactor||1,n=!!t.dashTemplate;let a=t.cap;n&&a===ke.ROUND&&(a=ke.SQUARE);const l=t.join,h=g(t.width)*s,u=g(t.referenceWidth),c=g(t.miterLimit),f=i&&z(i)||0,[d,_]=tt(t.scaleInfo,r),m=!1;if(!e)return new q(t.materialKey,e,h,a,l,c,f,0,0,!1,n,t.scaleDash??!1,t.colorLocked??!1,m,t.sampleAlphaOnly,u,t.zOrder,t.effects,d,_);const{rect:x,width:p,height:y}=e,v=x.x+I,w=x.y+I,L=v+p,S=w+y,T=M(v,w),$=M(L,S),G=!1;return new q(t.materialKey,e,h,a,l,c,f,T,$,!0,n,t.scaleDash??!1,t.colorLocked??!1,G,t.sampleAlphaOnly,u,t.zOrder,t.effects,d,_)}static fromFillOutline(t){var r;const e=mt.load(t.materialKey);return At(e)&&t.outline&&((r=t.outline)==null?void 0:r.style)==="esriSLSSolid"?q.fromSimpleLine({hash:"",materialKey:t.materialKey,...t.outline},null,!0):null}static fromSimpleLine(t,e,r=!1){const{color:i}=t,s=t.style!=="esriSLSSolid"&&t.style!=="esriSLSNull",n=ci(t.cap||"round"),a=ui(t.join||"round");let l=i&&t.style!=="esriSLSNull"&&F(i)||0;t.style==="esriSLSNull"&&(l=0);const h=g(t.width),u=t.miterLimit;if(!e)return new q(t.materialKey,e,h,n,a,u,l,0,0,!1,s,!0,!1,r,!1,h,0,null,X,U);const{rect:c,width:f,height:d}=e,_=c.x+I,m=c.y+I,x=_+f,p=m+d,y=M(_,m),v=M(x,p);return new q(t.materialKey,e,h,n,a,u,l,y,v,!0,s,!0,!1,r,!1,h,0,null,X,U)}static fromPictureLineSymbol(t,e,r,i){return xt.getLogger("esri.views.2d.engine.webgl.WGLLineTemplate").error("PictureLineSymbol support does not exist!"),null}}const Ps=100,Qe=1,Wr=o=>class extends o{constructor(...t){super(...t),this.forceLibtess=!1,this._bitset=0,this._lineTemplate=null,this.geometryType=P.FILL}_maybeAddLineTemplate(t){this._lineTemplate=q.fromFillOutline(t)}_write(t,e,r,i){const s=e.geometryType==="esriGeometryPoint",n=mt.load(this._materialKey);t.recordStart(e.getDisplayId(),this._materialKey,this.geometryType,s),this._writeGeometry(t,e,n,i,s),At(n)&&A(this._lineTemplate)&&this._lineTemplate.writeGeometry(t,e,i,s),t.recordEnd()}_writeGeometry(t,e,r,i,s){const n=this._getGeometry(e,i,s);if(O(n))return;const a=[];if(!(n.maxLength>Ps)&&!this.forceLibtess&&ys(a,n))return void(a.length&&this._writeVertices(t,e,n.coords,n.lengths,r,a));const l=gs(n);this._writeVertices(t,e,l,[l.length/2],r)}_writeVertex(t,e,r,i,s,n){const a=M(Qe*i,Qe*s);if(t.vertexBounds(i,s,0,0),t.vertexWrite(a),t.vertexWrite(e),r.symbologyType===Gt.DOT_DENSITY)t.vertexWriteF32(1/Math.abs(n.readGeometryArea()));else{t.vertexWrite(this.fillColor);const l=mr(r);l||(t.vertexWrite(this.tl),t.vertexWrite(this.br)),t.vertexWrite(this.aux21),t.vertexWrite(this.aux22),t.vertexWrite(this.aux3),l||t.vertexWrite(this._minMaxZoom)}}_writeVertices(t,e,r,i,s,n){const a=e.getDisplayId(),l=this._bitset<<24|a,h=i.reduce((d,_)=>d+_),u=te(s.geometryType,s.symbologyType).geometry/4,c=t.vertexCount();t.vertexEnsureSize(u*h);let f=0;if(n)for(const d of n){const _=r[2*d],m=r[2*d+1];this._writeVertex(t,l,s,_,m,e),f++}else for(let d=0;d<r.length;d+=2){const _=Math.round(r[d]),m=Math.round(r[d+1]);this._writeVertex(t,l,s,_,m,e),f++}t.indexEnsureSize(f);for(let d=0;d<f;d++)t.indexWrite(d+c)}_getGeometry(t,e,r){const i=e?de(_e(e),2):t.readGeometryForDisplay();return i?vs(i,r?256:8):null}},Je=xt.getLogger("esri.views.2d.engine.webgl.WGLDynamicMeshTemplate");let Ut=class extends Lt{constructor(t){super(),this._ongoingMaterialRequestMap=new Map,this._materialCache=new Map,this._dynamicPropertyMap=new Map,this._cimLayer=t}analyze(t,e,r,i,s){if(s&&s.length===0)return null;const n=s&&s.length>0,a=e.readLegacyFeature(),l=e.getObjectId(),h=this._materialCache,u=this._cimLayer.materialHash;if(!u)return Je.error("A Dynamic mesh template must have a material hash value or function!"),Promise.reject(null);const c=typeof u=="function"?u(a,r,i,l):u,f=h.get(c);if(f!=null)return Promise.resolve(f);const d=this._ongoingMaterialRequestMap.get(c);if(d)return d;const _=this._cimLayer,m=zi(_.cim,this._cimLayer.materialOverrides);m.mosaicHash=c;const{type:x,url:p}=_,y={cim:m,type:x,mosaicHash:c,url:p,size:null,dashTemplate:null,text:null,fontName:null,objectId:l,animatedSymbolProperties:null};switch(x){case"marker":y.size=Tt(_.size,a,r,i),y.animatedSymbolProperties=Tt(_.animatedSymbolProperties,a,r,i);break;case"line":y.dashTemplate=_.dashTemplate;break;case"text":y.text=Tt(_.text,a,r,i),y.fontName=Tt(_.fontName,a,r,i)}const v=t.getMosaicItem(y,s).then(w=>(n||(this._ongoingMaterialRequestMap.delete(c),h.set(c,w)),w)).catch(w=>(this._ongoingMaterialRequestMap.delete(c),Je.error(".analyze()",w.message),null));return n||this._ongoingMaterialRequestMap.set(c,v),v}};const tr=128;let zs=class Ir extends Wr(Ut){constructor(t,e,r){var u;if(super(t),this._minMaxZoom=M(Math.round(e*k),Math.round(r*k)),b(t.color)){const c=(f,d,_)=>{const m=t.color(f,d,_);return m&&z(m)||0};this._dynamicPropertyMap.set("fillColor",c)}else{const c=t.color;this.fillColor=c&&z(c)||0}const i=((u=t.cim.placement)==null?void 0:u.type)==="CIMMarkerPlacementInsidePolygon"&&t.cim.placement.shiftOddRows?2:1,s=t.height;if(b(s)){const c=(f,d,_)=>s(f,d,_)*i;this._dynamicPropertyMap.set("_height",c)}else this._height=(s||0)*i;const n=t.offsetX;if(b(n)){const c=(f,d,_)=>g(n(f,d,_));this._dynamicPropertyMap.set("_offsetX",c)}else this._offsetX=g(n||0);const a=t.offsetY;if(b(a)){const c=(f,d,_)=>g(-a(f,d,_));this._dynamicPropertyMap.set("_offsetY",c)}else this._offsetY=g(-a||0);const l=t.scaleX;b(l)?this._dynamicPropertyMap.set("_scaleX",l):this._scaleX=l||1;const h=t.angle;if(b(h)){const c=(f,d,_)=>re(h(f,d,_));this._dynamicPropertyMap.set("_angle",c)}else this._angle=re(h)||0;if(A(t.effects)){const c=t.effects;b(c)?this._dynamicPropertyMap.set("_effects",c):this._effects=c}this._cimFillLayer=t,this._bitset=(t.colorLocked?st:0)|(t.applyRandomOffset?cr:0)|(t.sampleAlphaOnly?Dt:0)|(t.hasUnresolvedReplacementColor?ur:0),this._fillMaterialKey=t.materialKey}static fromCIMFill(t,e){const[r,i]=tt(t.scaleInfo,e);return new Ir(t,r,i)}bindFeature(t,e,r){const i=t.readLegacyFeature();this._dynamicPropertyMap.forEach((u,c)=>{this[c]=u(i,e,r)});const s=mt.load(this._fillMaterialKey),n=this._materialCache,a=(0,this._cimFillLayer.materialHash)(i,e,r),l=n.get(a);let h=null;if(l&&R(l.spriteMosaicItem)&&(h=l.spriteMosaicItem),h){const{rect:u,width:c,height:f}=h,d=u.x+I,_=u.y+I,m=d+c,x=_+f;let p=Math.round(g(this._height));p<=0&&(p=x-_);let y=Math.round(g(this._height/f*c||0));y<=0&&(y=m-d);const v=this._scaleX,w=1;this.tl=M(d,_),this.br=M(m,x),this.aux21=M(y,p),this.aux22=M(this._offsetX,this._offsetY),this.aux3=W(v*tr,w*tr,this._angle,0),s.sdf=h.sdf,s.pattern=!0,s.textureBinding=h.textureBinding}else this.tl=0,this.br=0,this.aux21=0,this.aux22=0,this.aux3=0,s.sdf=!1,s.pattern=!1,s.textureBinding=0;this._materialKey=s.data}},Ts=class Cr extends $r(Ut){constructor(t,e,r){super(t),this._minMaxZoom=M(Math.round(e*k),Math.round(r*k)),this._cimLineLayer=t;let i=0;b(t.width)||(i=.5*g(t.width));const s=(c,f,d)=>b(t.width)?.5*g(t.width(c,f,d)):i;this._dynamicPropertyMap.set("_halfWidth",s),b(t.cap)?this._dynamicPropertyMap.set("_capType",t.cap):this._capType=t.cap,b(t.join)?this._dynamicPropertyMap.set("_joinType",t.join):this._joinType=t.join;const n=t.color;if(b(n)){const c=(f,d,_)=>z(n(f,d,_));this._dynamicPropertyMap.set("_fillColor",c)}else this._fillColor=n&&z(n)||0;const a=t.miterLimit;if(b(a)){const c=(f,d,_)=>ie(a(f,d,_));this._dynamicPropertyMap.set("_miterLimitCosine",c)}else this._miterLimitCosine=ie(a);if(A(t.effects)){const c=t.effects;b(c)?this._dynamicPropertyMap.set("_effects",c):this._effects=c}this._scaleFactor=t.scaleFactor||1,this._isDashed=t.dashTemplate!=null;const l=t.colorLocked?st:0,h=t.scaleDash?hr:0,u=t.sampleAlphaOnly?Dt:0;this.tessellationProperties._bitset=l|h|u,this._materialKey=t.materialKey,this._initializeTessellator(!0)}static fromCIMLine(t,e){const[r,i]=tt(t.scaleInfo,e);return new Cr(t,r,i)}bindFeature(t,e,r){const i=t.readLegacyFeature();this._dynamicPropertyMap.forEach((u,c)=>{this[c]=u(i,e,r)}),this._halfWidth*=this._scaleFactor;const s=this._materialCache,n=(0,this._cimLineLayer.materialHash)(i,e,r),a=s.get(n);let l=null;if(a&&R(a.spriteMosaicItem)&&(l=a.spriteMosaicItem),l){this._hasPattern=!0;const{rect:u,width:c,height:f}=l,d=u.x+I,_=u.y+I,m=d+c,x=_+f;this.tessellationProperties._tl=M(d,_),this.tessellationProperties._br=M(m,x)}else this._hasPattern=!1,this.tessellationProperties._tl=0,this.tessellationProperties._br=0;this.tessellationProperties._fillColor=this._fillColor,this.tessellationProperties._halfWidth=this._halfWidth,this.tessellationProperties.offset=0,this.tessellationProperties._halfReferenceWidth=this.tessellationProperties._halfWidth;const h=Bt.load(this._materialKey);l&&(h.sdf=l.sdf,h.pattern=!0,h.textureBinding=l.textureBinding),this._materialKey=h.data}};const $s=fe(),Ws=Ot();class be extends Sr(Ut){constructor(t,e,r){super(t),this._cimMarkerLayer=t,this._minMaxZoom=M(Math.round(e*k),Math.round(r*k));const i=t.color;if(b(i)){const f=(d,_,m)=>z(i(d,_,m));this._dynamicPropertyMap.set("_fillColor",f)}else this._fillColor=z(i);const s=t.outlineColor;if(b(s)){const f=(d,_,m)=>z(s(d,_,m));this._dynamicPropertyMap.set("_outlineColor",f)}else this._outlineColor=z(s);const n=t.size;if(b(n)){const f=(d,_,m)=>g(n(d,_,m));this._dynamicPropertyMap.set("_size",f)}else this._size=g(n)||0;const a=t.scaleX;b(a)?this._dynamicPropertyMap.set("_scaleX",a):this._scaleX=a;const l=t.offsetX;if(b(l)){const f=(d,_,m)=>g(l(d,_,m));this._dynamicPropertyMap.set("xOffset",f)}else this.xOffset=g(l)||0;const h=t.offsetY;if(b(h)){const f=(d,_,m)=>g(h(d,_,m));this._dynamicPropertyMap.set("yOffset",f)}else this.yOffset=g(h)||0;const u=t.outlineWidth;if(b(u)){const f=(d,_,m)=>g(u(d,_,m));this._dynamicPropertyMap.set("_outlineWidth",f)}else this._outlineWidth=g(u)||0;const c=t.rotation;if(b(c)?this._dynamicPropertyMap.set("_angle",c):this._angle=c||0,A(t.effects)){const f=t.effects;b(f)?this._dynamicPropertyMap.set("_effects",f):this._effects=f}if(A(t.markerPlacement)){const f=t.markerPlacement;b(f)?this._dynamicPropertyMap.set("_markerPlacement",f):this._markerPlacement=f}this._scaleFactor=Et(t.scaleFactor,1),this._bitSet=(t.alignment===Z.MAP?1:0)|(t.colorLocked?1:0)<<1|(t.scaleSymbolsProportionally?1:0)<<3,this._materialKey=t.materialKey}static fromCIMMarker(t,e){const[r,i]=tt(t.scaleInfo,e);return new be(t,r,i)}bindFeature(t,e,r){const i=t.readLegacyFeature(),s=t.getObjectId();this._dynamicPropertyMap.forEach((at,lt)=>{this[lt]=at(i,e,r)});const n=this._cimMarkerLayer.materialHash,a=typeof n=="function"?n(i,e,r,s):n,l=this._materialCache.get(a);if(!l||!R(l.spriteMosaicItem)||!l.spriteMosaicItem)return void xt.getLogger("esri.views.2d.engine.webgl.WGLDynamicMarkerTemplate").error(new ot("mapview-cim","Encountered an error when binding feature"));const h=l.spriteMosaicItem,u=this._cimMarkerLayer.sizeRatio,c=h.width/h.height*this._scaleX,f=this._cimMarkerLayer.rotateClockwise?this._angle:-this._angle;let d=this._size,_=d*c;const m=this.xOffset,x=this.yOffset;this.xOffset*=this._scaleFactor,this.yOffset*=this._scaleFactor;const p=this._cimMarkerLayer.scaleSymbolsProportionally&&this._cimMarkerLayer.frameHeight?this._size/g(this._cimMarkerLayer.frameHeight):1,y=this._outlineWidth*p,v=g(this._cimMarkerLayer.referenceSize);let w=0,L=0;const S=this._cimMarkerLayer.anchorPoint;S&&(this._cimMarkerLayer.isAbsoluteAnchorPoint?this._size&&(w=g(S.x)/(this._size*c),L=g(S.y)/this._size):(w=S.x,L=S.y)),this._anchorX=w,this._anchorY=L,this._sizeOutlineWidth=W(Math.round(Math.min(Math.sqrt(128*_),255)),Math.round(Math.min(Math.sqrt(128*d),255)),Math.round(Math.min(Math.sqrt(128*y),255)),Math.round(Math.min(Math.sqrt(128*v),255))),this.angle=f;const T=Math.round(64*u);this._bitestAndDistRatio=M(this._bitSet,T);const $=h.rect.x+I,G=h.rect.y+I,K=$+h.width,j=G+h.height;this._texUpperLeft=M($,G),this._texUpperRight=M(K,G),this._texBottomLeft=M($,j),this._texBottomRight=M(K,j);const N=xe.load(this._materialKey);N.sdf=h.sdf,N.pattern=!0,N.textureBinding=h.textureBinding,this._materialKey=N.data,_*=u,d*=u,_*=this._scaleFactor,d*=this._scaleFactor,_*=h.rect.width/h.width,d*=h.rect.height/h.height,this._computedWidth=_,this._computedHeight=d,this._applyTransformation(Ws,$s),this.xOffset=m,this.yOffset=x}}function kr(o){if(o==null)return[];const t=new Array(o.length);for(let e=0;e<o.length;e++)t[e]=o.charCodeAt(e);return t}const er=5;function Is(o,t,e,r){return typeof o.text=="string"?o.text:typeof o.text=="function"?o.text(t,e,r)??"":""}class Se extends vr(Ut){constructor(t,e,r){super(t),this._horizontalAlignment="center",this._verticalAlignment="middle",this._textToGlyphs=new Map,this._minMaxZoom=M(Math.round(e*k),Math.round(r*k));const i=t.scaleFactor||1;this._cimTextLayer=t;const s=t.color;if(b(s)){const m=(x,p,y)=>z(s(x,p,y));this._dynamicPropertyMap.set("_color",m)}else this._color=z(s);const n=t.outlineColor;if(b(n)){const m=(x,p,y)=>z(n(x,p,y));this._dynamicPropertyMap.set("_haloColor",m)}else this._haloColor=z(n);let a;b(t.size)||(a=Math.min(Math.round(g(t.size*t.sizeRatio)),127));const l=(m,x,p)=>b(t.size)?Math.min(Math.round(g(t.size(m,x,p)*t.sizeRatio)),127):a;if(this._dynamicPropertyMap.set("_size",l),b(t.outlineSize)){const m=(x,p,y)=>Math.min(Math.floor(er*g(t.outlineSize(x,p,y)*t.sizeRatio)),127);this._dynamicPropertyMap.set("_haloSize",m)}else this._haloSize=Math.min(Math.floor(er*g(t.outlineSize*t.sizeRatio)),127);let h;b(t.offsetX)||(h=Math.round(g(t.offsetX*t.sizeRatio)));const u=(m,x,p)=>b(t.offsetX)?Math.round(g(t.offsetX(m,x,p)*t.sizeRatio)):h;let c;this._dynamicPropertyMap.set("_xOffset",u),b(t.offsetY)||(c=Math.round(g(t.offsetY*t.sizeRatio)));const f=(m,x,p)=>b(t.offsetY)?Math.round(g(t.offsetY(m,x,p)*t.sizeRatio)):c;if(this._dynamicPropertyMap.set("_yOffset",f),b(t.angle)?this._dynamicPropertyMap.set("_angle",t.angle):this._angle=t.angle,b(t.horizontalAlignment)?this._dynamicPropertyMap.set("_horizontalAlignment",t.horizontalAlignment):this._horizontalAlignment=t.horizontalAlignment,b(t.verticalAlignment)?this._dynamicPropertyMap.set("_verticalAlignment",t.verticalAlignment):this._verticalAlignment=t.verticalAlignment,A(t.effects)){const m=t.effects;b(m)?this._dynamicPropertyMap.set("_effects",m):this._effects=m}if(A(t.markerPlacement)){const m=t.markerPlacement;b(m)?this._dynamicPropertyMap.set("_markerPlacement",m):this._textPlacement=m}b(t.text)?this._dynamicPropertyMap.set("_text",t.text):this._text=t.text,this._backgroundColor=t.backgroundColor&&z(t.backgroundColor),this._borderLineColor=t.borderLineColor&&z(t.borderLineColor),this._borderLineSize=t.borderLineWidth,this._scaleFactor=i;const d=Math.min(Math.round(g(t.referenceSize*t.sizeRatio)),127);this._referenceSize=Math.round(Math.sqrt(256*d)),this._materialKey=t.materialKey;const _=bi.load(this._materialKey);_.sdf=!0,this._bitset=(t.alignment===Z.MAP?1:0)|(t.colorLocked?1:0)<<1,this._materialKey=_.data,this._decoration="none",this._lineHeight=1,this._lineWidth=512,this._isCIM=!0}static fromCIMText(t,e){const[r,i]=tt(t.scaleInfo,e);return new Se(t,r,i)}async analyze(t,e,r,i){const s=e.readLegacyFeature(),n=Is(this._cimTextLayer,s,r,i),a=await super.analyze(t,e,r,i,kr(n));return a&&a.glyphMosaicItems&&this._textToGlyphs.set(n,a.glyphMosaicItems),a}bindFeature(t,e,r){const i=t.readLegacyFeature();if(this._dynamicPropertyMap.forEach((n,a)=>{this[a]=n(i,e,r)}),!this._text||this._text.length===0)return void(this._shapingInfo=null);this._size*=this._scaleFactor,this._scale=this._size/ar,this._xOffset*=this._scaleFactor,this._yOffset*=this._scaleFactor,this._xAlignD=_r(Et(this._horizontalAlignment,"center")),this._yAlignD=fr(Et(this._verticalAlignment,"baseline"));const s=this._textToGlyphs.get(this._text)??[];this.bindTextInfo(s,!1)}}const ut=128;let jt=class _t extends Wr(Lt){constructor(t,e,r,i,s,n,a,l,h,u,c,f,d,_,m,x){super(),this._effects=_;const p=mt.load(t);e&&(p.sdf=e.sdf,p.pattern=!0,p.textureBinding=e.textureBinding),this.fillColor=r,this.tl=i,this.br=s,this.aux21=M(n,a),this.aux22=M(l,h),this.aux3=W(u,c,f,0),this._bitset=d,this._minMaxZoom=M(Math.round(m*k),Math.round(x*k)),this._materialKey=p.data}static fromCIMFill(t,e,r){const i=t.color,s=i&&z(i)||0,n=t.materialKey,[a,l]=tt(t.scaleInfo,r),h=(t.colorLocked?st:0)|(t.applyRandomOffset?cr:0)|(t.sampleAlphaOnly?Dt:0)|(t.hasUnresolvedReplacementColor?ur:0);if(!e)return new _t(n,null,s,0,0,0,0,0,0,0,0,0,h,t.effects,a,l);const{rect:u,width:c,height:f}=e,d=t.scaleX||1,_=u.x+I,m=u.y+I,x=_+c,p=m+f,y=g(t.height);let v=d*y;t.cim.type==="CIMHatchFill"&&(v*=c/f);let w=Math.round(y);w<=0&&(w=p-m);let L=Math.round(v);L<=0&&(L=x-_);const S=g(t.offsetX||0),T=g(-t.offsetY||0),$=M(_,m),G=M(x,p);return new _t(n,e,s,$,G,L,w,S,T,ut,ut,re(t.angle),h,t.effects,a,l)}static fromSimpleFill(t,e,r=!1){const{color:i}=t,s=i&&t.style!=="esriSFSNull"&&F(i)||0,n=r?st:0,a=t.materialKey;let l;if(e){const{rect:h,width:u,height:c,pixelRatio:f}=e,d=h.x+I,_=h.y+I,m=d+u,x=_+c,p=M(d,_),y=M(m,x);l=new _t(a,e,s,p,y,u/f,c/f,0,0,ut,ut,0,n,null,X,U)}else l=new _t(a,null,s,0,0,0,0,0,0,0,0,0,n,null,X,U);return l._maybeAddLineTemplate(t),l}static fromPictureFill(t,e,r=!1){const i=lr,{rect:s,width:n,height:a}=e,l=s.x+I,h=s.y+I,u=l+n,c=h+a,f=M(l,h),d=M(u,c),_=Math.round(g(t.width)),m=Math.round(g(t.height)),x=g(t.xoffset),p=g(-t.yoffset),y=t.materialKey,v=r?st:0,w=new _t(y,e,i,f,d,_,m,x,p,ut*t.xscale,ut*t.yscale,0,v,null,X,U);return w._maybeAddLineTemplate(t),w}},Cs=class{constructor(){this._resolver=null}isHeld(){return!!this._resolver}async acquire(){this._resolver?(await this._resolver.promise,await this.acquire()):this._resolver=fi()}release(){const t=this._resolver;this._resolver=null,t==null||t.resolve()}};async function ks(o,t,e){try{await o.acquire(),await t(e),o.release()}catch(r){throw o.release(),r}}async function Es(o,t,e){if(!o.name)throw new ot("style-symbol-reference-name-missing","Missing name in style symbol reference");if(o.styleName&&o.styleName==="Esri2DPointSymbolsStyle")return Bs(o,e);try{return As(await Zr(o,t,e),o.name,t,e)}catch(r){return pe(r),null}}async function Bs(o,t){const e=Nr.replace(/\{SymbolName\}/gi,o.name);try{const r=await nr(e,t);return or(r.data)}catch(r){return pe(r),null}}async function As(o,t,e,r){const i=o.data,s={portal:Xr.getDefault(),url:Ci(o.baseUrl),origin:"portal-item"},n=i.items.find(l=>l.name===t);if(!n)throw new ot("symbolstyleutils:symbol-name-not-found",`The symbol name '${t}' could not be found`,{symbolName:t});let a=Ur(Hr(n,"cimRef"),s);Yr()&&(a=qr(a));try{const l=await nr(a,r);return or(l.data)}catch(l){return pe(l),null}}const rr=async(o,t,e)=>new Ii(await Ti(o.data,t,e),o.data,o.rendererKey,o.maxVVSize);async function H(o,t,e,r){if(!o)return null;if(o.type==="cim")return rr(o,t,e);if(o.type==="web-style"){const i={type:"cim",data:await Es(o,null,r)??void 0,rendererKey:o.rendererKey,maxVVSize:o.maxVVSize};return rr(i,t,e)}return o}function kt(o){if(!o)return null;const{avoidSDFRasterization:t,type:e,cim:r,url:i,materialHash:s}=o,n={cim:r,type:e,mosaicHash:s,url:i,size:null,dashTemplate:null,path:null,text:null,fontName:null,animatedSymbolProperties:null,avoidSDFRasterization:t};switch(e){case"marker":n.size=o.size,n.path=o.path,n.animatedSymbolProperties=o.animatedSymbolProperties;break;case"line":n.dashTemplate=o.dashTemplate;break;case"text":n.text=o.text,n.fontName=o.fontName}return n}const E=xt.getLogger("esri.views.2d.engine.webgl.mesh.templates.WGLTemplateStore"),ir={sortKey:null,templates:new Array},Le={isOutline:!1,placement:null,symbologyType:Gt.DEFAULT,vvFlags:0},Rs={...$e,hash:JSON.stringify($e),materialKey:ye(P.MARKER,Le)},Fs={...We,hash:JSON.stringify(We),materialKey:ye(P.LINE,Le)},Vs={...Ie,hash:JSON.stringify(Ie),materialKey:ye(P.FILL,Le)};function Y(o,t){const e=o.length;return o.push(null),t.then(r=>o[e]=r),o}function Mt(o){return o!=null&&!!(1&o)}function Os(o){return o.name==="worker:port-closed"}class vn{constructor(t,e){this._idCounter=1,this._templateIdCounter=1,this._idToTemplateGroup=new Map,this._symbolToTemplate=new Map,this._fetchQueue=[],this._idToResolver=new Map,this._cimTemplateCache=new Map,this._cimAnalyses=[],this._lock=new Cs,this._fetchResource=t,this._tileInfo=e}get _markerError(){return this._errorTemplates.marker[0]}get _fillError(){return this._errorTemplates.fill[0]}get _lineError(){return this._errorTemplates.line[0]}get _textError(){return this._errorTemplates.line[0]}createTemplateGroup(t,e,r=null){this._initErrorTemplates();const i=t.hash,s=this._symbolToTemplate.get(i);if(s!=null)return s;const n=new Array,a={sortKey:r,templates:n};e&&this._createMeshTemplates(n,e,!0),this._createMeshTemplates(n,t,!1);const l=this._createGroupId(t.type==="expanded-cim"&&Ds(t));return this._idToTemplateGroup.set(l,a),this._symbolToTemplate.set(i,l),l}getTemplateGroup(t){return this._idToTemplateGroup.get(t)??ir}getDynamicTemplateGroup(t){return this._idToTemplateGroup.has(t)?(Mt(t)||E.error("mapview-template-store",`Id ${t} does not refer to a dynamic template`),this._idToTemplateGroup.get(t)):ir}getMosaicItem(t,e){const r=this._createTemplateId(),i=new Promise(s=>this._idToResolver.set(r,s));return this._fetchQueue.push({symbol:t,id:r,glyphIds:e}),i}finalize(t){return this._fetchQueue.length||this._lock.isHeld()?ks(this._lock,this._fetchAllQueuedResources.bind(this),t):Promise.resolve()}_initErrorTemplates(){this._errorTemplates||(this._errorTemplates={fill:this._createMeshTemplates([],Vs,!1),marker:this._createMeshTemplates([],Rs,!1),line:this._createMeshTemplates([],Fs,!1)})}_fetchAllQueuedResources(t){if(!this._fetchQueue.length)return Promise.resolve();const e=this._fetchQueue,r=this._cimAnalyses;return this._fetchQueue=[],this._cimAnalyses=[],Promise.all(r).then(()=>this._fetchResource(e,t).then(i=>{for(const{id:s,mosaicItem:n}of i)this._idToResolver.get(s)(n),this._idToResolver.delete(s)})).catch(i=>{mi(i)?this._fetchQueue=this._fetchQueue.concat(e):Os(i)||E.error(new ot("mapview-template-store","Unable to fetch requested texture resources",i))})}_createGroupId(t){return this._idCounter++<<1|(t?1:0)}_createTemplateId(){return this._templateIdCounter++}async _createSMS(t){const{spriteMosaicItem:e}=await this.getMosaicItem(t);return R(e,E)?yt.fromSimpleMarker(t,e):this._markerError}async _createPMS(t){const{spriteMosaicItem:e}=await this.getMosaicItem(t);return R(e,E)?yt.fromPictureMarker(t,e):this._markerError}async _createSFS(t,e){const{spriteMosaicItem:r}=await this.getMosaicItem(t);return R(r,E)?jt.fromSimpleFill(t,r,e):this._fillError}async _createPFS(t,e){const{spriteMosaicItem:r}=await this.getMosaicItem(t);return R(r,E)?jt.fromPictureFill(t,r,e):this._fillError}async _createSLS(t,e){const{spriteMosaicItem:r}=await this.getMosaicItem(t);return R(r,E)?q.fromSimpleLine(t,r):this._lineError}async _createLMS(t){const{spriteMosaicItem:e}=await this.getMosaicItem(t);return R(e,E)?yt.fromLineSymbolMarker(t,e):this._markerError}async _createTS(t){const{glyphMosaicItems:e}=await this.getMosaicItem(t);return ne.fromText(t,e??[])}async _createCIMText(t){const{glyphMosaicItems:e}=await this.getMosaicItem(kt(t),kr(t.text));return R(e,E)?ne.fromCIMText(t,e,this._tileInfo):this._textError}async _createCIMFill(t){const{spriteMosaicItem:e}=await this.getMosaicItem(kt(t));return R(e,E)?jt.fromCIMFill(t,e,this._tileInfo):this._fillError}async _createCIMLine(t){const{spriteMosaicItem:e}=await this.getMosaicItem(kt(t));return R(e,E)?q.fromCIMLine(t,e,this._tileInfo):this._lineError}async _createCIMMarker(t){const{spriteMosaicItem:e}=await this.getMosaicItem(kt(t));return R(e,E)?yt.fromCIMMarker(t,e,this._tileInfo):this._markerError}async _createCIM(t){const e=t.templateHash;let r=this._cimTemplateCache.get(e);if(r!=null)return r;switch(t.type){case"marker":r=await this._createCIMMarker(t);break;case"line":r=await this._createCIMLine(t);break;case"fill":r=await this._createCIMFill(t);break;case"text":r=await this._createCIMText(t)}return this._cimTemplateCache.set(e,r),r}async _createDynamicCIM(t){const e=t.templateHash;let r=this._cimTemplateCache.get(e);if(r!=null)return r;switch(t.type){case"marker":r=be.fromCIMMarker(t,this._tileInfo);break;case"line":r=Ts.fromCIMLine(t,this._tileInfo);break;case"fill":r=zs.fromCIMFill(t,this._tileInfo);break;case"text":r=Se.fromCIMText(t,this._tileInfo)}return this._cimTemplateCache.set(e,r),r}_createPrimitiveMeshTemplates(t,e,r){switch(e.type){case"esriSMS":return Y(t,this._createSMS(e));case"esriPMS":return Y(t,this._createPMS(e));case"esriSFS":return Y(t,this._createSFS(e,r));case"line-marker":return Y(t,this._createLMS(e));case"esriPFS":return Y(t,this._createPFS(e,r));case"esriSLS":return Y(t,this._createSLS(e,!1));case"esriTS":return Y(t,this._createTS(e));default:return E.error("Unable to create mesh template for unknown symbol type {: $ }{symbol.type}"),t}}_createMeshTemplates(t,e,r){if(e.type.includes("3d"))return E.error("3D symbols are not supported with MapView"),t;if(e.type==="expanded-cim"){for(const i of e.layers)typeof i.materialHash=="function"?Y(t,this._createDynamicCIM(i)):Y(t,this._createCIM(i));return t}if(e.type==="composite-symbol"){for(const i of e.layers)this._createPrimitiveMeshTemplates(t,i,r);return t}return e.type==="cim"||e.type==="label"||e.type==="web-style"?t:this._createPrimitiveMeshTemplates(t,e,r)}}const Ds=o=>{if(!o.layers)return!1;for(const t of o.layers)if(typeof t.materialHash=="function")return!0;return!1};class wn{constructor(t,e,r){this._loadPromise=xi(),this._geometryType=t,this._idField=e,this._templateStore=r}update(t,e){A(t.mesh.labels)&&(this._labelTemplates=this._createLabelTemplates(t.mesh.labels,e)),this._schema=t}_createLabelTemplates(t,e){const r=new Map;if(t.type==="simple"){for(const i of t.classes){const s=Xe.fromLabelClass(i,e);r.set(i.index,s)}return r}for(const i in t.classes){const s=t.classes[i];for(const n of s){const a=Xe.fromLabelClass(n,e);r.set(n.index,a)}}return r}get templates(){return this._templateStore}async analyze(t,e,r,i,s,n,a){if(Ce(a))return;let l;(r==null?void 0:r.type)==="dictionary"&&(l=await r.analyze(this._idField,t.copy(),e,s,n,a));let h=0;for(;t.next();){let u=null;if(u=l?l[h++]:A(i)&&hi(t.getDisplayId())&&t.readAttribute("cluster_count")!==1?i.match(this._idField,t,this._geometryType,s,n):r.match(this._idField,t,this._geometryType,s,n),t.setGroupId(u),Mt(u)){const c=this._templateStore.getDynamicTemplateGroup(u).templates;for(const f of c)f&&f.analyze&&f.analyze(this._templateStore,t,s,n)}}return await this._loadPromise,this._templateStore.finalize(a)}async analyzeGraphics(t,e,r,i,s,n){if(Ce(n))return;const a=t.getCursor();for(r&&await r.analyze(this._idField,a.copy(),e,i,s,n);a.next();){let l=a.getGroupId();if(l!=null&&l!==-1||(l=r==null?void 0:r.match(this._idField,a,a.geometryType,i,s),a.setGroupId(l)),Mt(l)){const h=this._templateStore.getDynamicTemplateGroup(l).templates;for(const u of h)u&&u.analyze&&u.analyze(this._templateStore,a,i,s)}a.setGroupId(l)}return await this._loadPromise,this._templateStore.finalize(n)}writeGraphic(t,e,r,i){const s=e.getGroupId(),n=e.getDisplayId(),a=this._templateStore.getTemplateGroup(s);if(t.featureStart(e.insertAfter,0),n!=null){if(Mt(s))for(const l of a.templates)l&&l.bindFeature(e,null,null);if(a){for(const l of a.templates)l&&l.write(t,e,r,i);t.featureEnd()}}}writeCursor(t,e,r,i,s,n,a){const l=e.getGroupId(),h=e.getDisplayId(),u=this._templateStore.getTemplateGroup(l),c=u.templates,f=this._getSortKeyValue(e,u);if(t.featureStart(0,f),h!=null&&c){if(Mt(l))for(const d of c)d.bindFeature(e,r,i);for(const d of c)d.write(t,e,s,a);if(A(n)&&t.hasRecords){const d=n&&this._findLabelRef(c);this._writeLabels(t,e,n,d,s,a)}t.featureEnd()}}_getSortKeyValue(t,e){const r=this._schema.mesh.sortKey;if(O(r))return 0;let i=0;return i=r.byRenderer===!0&&e.sortKey!=null?e.sortKey:r.fieldIndex!=null?t.getComputedNumericAtIndex(r.fieldIndex):r.field!=null?t.readAttribute(r.field):t.readAttribute(this._idField),i*=r.order==="asc"?1:-1,i==null||isNaN(i)?0:i}_findLabelRef(t){for(const e of t)if(e instanceof yt)return e;return null}_writeLabels(t,e,r,i,s,n){for(const a of r)if(A(a)&&a){const{glyphs:l,rtl:h,index:u}=a,c=this._labelTemplates.get(u);if(!c)continue;c.setZoomLevel(s),c.bindReferenceTemplate(i),c.bindTextInfo(l,h),c.write(t,e,null,n)}}}const ue=xt.getLogger("esri/views/2d/engine/webgl/util/Matcher");async function Gs(o,t,e,r){switch(o.type){case"simple":case"heatmap":return J.fromBasicRenderer(o,t,e,r);case"map":return ze.fromUVRenderer(o,t,e,r);case"interval":return Pe.fromCBRenderer(o,t,e,r);case"dictionary":return Te.fromDictionaryRenderer(o,t,e,r);case"pie-chart":return Vt.fromPieChartRenderer(o,t,e,r);case"subtype":return Vt.fromSubtypes(o,t,e,r)}}class J{constructor(){this.type="feature",this._defaultResult=null}static async fromBasicRenderer(t,e,r,i){const s=new J;if(t.symbol){const n=await H(t.symbol,r,i),a=e.createTemplateGroup(n,null);s.setDefault(a)}return s}static async fromPieChartRenderer(t,e,r,i){const s=new J;if(t.markerSymbol){const n=await H(t.markerSymbol,r,i);let a;t.fillSymbol&&(a=await H(t.fillSymbol,r,i));const l=e.createTemplateGroup(n,a);s.setDefault(l)}return s}size(){return 1}getDefault(){return this._defaultResult}setDefault(t){this._defaultResult=t}match(t,e,r,i,s){return this.getDefault()}async analyze(t,e,r,i,s,n){return null}}class Vt extends J{constructor(t,e){super(),this._subMatchers=t,this._subtypeField=e}static async fromSubtypes(t,e,r,i){const s=new Map,n=[];for(const a in t.renderers){const l=parseInt(a,10),h=Gs(t.renderers[a],e,r,i).then(u=>s.set(l,u));n.push(h)}return await Promise.all(n),new Vt(s,t.subtypeField)}match(t,e,r,i,s){const n=e.readAttribute(this._subtypeField),a=this._subMatchers.get(n);return a?a.match(t,e,r,i,s):null}}class Pe extends J{constructor(t,e,r,i){super(),this.type="interval",this._intervals=[],this._isMaxInclusive=e,this._fieldIndex=i,this._field=t,this._normalizationInfo=r}static async fromCBRenderer(t,e,r,i){const{isMaxInclusive:s,normalizationField:n,normalizationTotal:a,normalizationType:l}=t,h=t.field,u=new Pe(h,s,{normalizationField:n,normalizationTotal:a,normalizationType:l},t.fieldIndex),c=await H(t.backgroundFillSymbol,r,i);await Promise.all(t.intervals.map(async d=>{const _=await H(d.symbol,r,i),m=await e.createTemplateGroup(_,c),x={min:d.min,max:d.max};u.add(x,m)}));const f=await H(t.defaultSymbol,r,i);if(f){const d=await e.createTemplateGroup(f,c);u.setDefault(d)}return u}add(t,e){this._intervals.push({interval:t,result:e}),this._intervals.sort((r,i)=>r.interval.min-i.interval.min)}size(){return super.size()+this._intervals.length}match(t,e,r,i,s){if(this._fieldIndex==null&&!this._field)return this.getDefault();const n=this._fieldIndex!=null?e.getComputedNumericAtIndex(this._fieldIndex):this._getValueFromField(e);if(n==null||isNaN(n)||n===1/0||n===-1/0)return this.getDefault();for(let a=0;a<this._intervals.length;a++){const{interval:l,result:h}=this._intervals[a],u=n>=l.min,c=this._isMaxInclusive?n<=l.max:n<l.max;if(u&&c)return h}return this.getDefault()}_needsNormalization(){const t=this._normalizationInfo;return t&&(t.normalizationField||t.normalizationTotal||t.normalizationType)}_getValueFromField(t){const e=t.readAttribute(this._field);if(!this._needsNormalization()||e==null)return e;const{normalizationField:r,normalizationTotal:i,normalizationType:s}=this._normalizationInfo,n=t.readAttribute(r)??1;if(s)switch(s){case"esriNormalizeByField":return n?e/n:void 0;case"esriNormalizeByLog":return Math.log(e)*Math.LOG10E;case"esriNormalizeByPercentOfTotal":return e/i*100;default:return void ue.error(`Found unknown normalization type: ${s}`)}else ue.error("Normalization is required, but no type was set!")}}class ze extends J{constructor(t,e,r){super(),this.type="map",this._nullResult=null,this._resultsMap=new Map,this._fields=[],this._fieldsIndex=r,this._fields=t,this._seperator=e||""}static async fromUVRenderer(t,e,r,i){const s=t.fieldDelimiter,n=[t.field];t.field2&&n.push(t.field2),t.field3&&n.push(t.field3);const a=await H(t.backgroundFillSymbol,r,i),l=new ze(n,s,t.fieldIndex);await Promise.all(t.map.map(async(u,c)=>{const f=await H(u.symbol,r,i),d=c+1,_=await e.createTemplateGroup(f,a,d);u.value==="<Null>"?l.setNullResult(_):l.add(u.value,_)}));const h=await H(t.defaultSymbol,r,i);if(h){const u=Number.MAX_SAFE_INTEGER,c=await e.createTemplateGroup(h,a,u);l.setDefault(c)}return l}setNullResult(t){this._nullResult=t}add(t,e){this._resultsMap.set(t.toString(),e)}size(){return super.size()+this._resultsMap.size}match(t,e,r,i,s){if(this._fieldsIndex==null&&!this._fields)return this.getDefault();const n=this._fieldsIndex!=null?e.getComputedStringAtIndex(this._fieldsIndex):this._getValueFromFields(e);if(this._nullResult!==null&&(n==null||n===""||n==="<Null>"))return this._nullResult;if(n==null)return this.getDefault();const a=n.toString();return this._resultsMap.has(a)?this._resultsMap.get(a):this.getDefault()}_getValueFromFields(t){const e=[];for(const r of this._fields){const i=t.readAttribute(r);i==null||i===""?e.push("<Null>"):e.push(i)}return e.join(this._seperator)}}async function Ks(o,t){const e=o||1;if(typeof e=="number")return(i,s,n)=>e;const r=await Qr(e,t.spatialReference,t.fields);return(i,s,n)=>Wi(r,i,{$view:n},t.geometryType,s)||1}let Qt;async function Zs(){return Qt||(Qt=dr(()=>import("./schemaUtils-DLXXqxNF.js").then(o=>o.c),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27]))),Qt}class Te extends J{constructor(t,e,r,i,s,n){super(),this.type="dictionary",this._groupIdCache=new jr(100),this._loader=t,this._fieldMap=t.fieldMap,this._symbolFields=t.getSymbolFields(),this._templates=e,this._info=r,this._scaleFn=i,this._schemaUtilsModule=s,this._symbolOptions=n}static async fromDictionaryRenderer(t,e,r,i){const[{DictionaryLoader:s},n]=await Promise.all([dr(()=>import("./MapView-DaoQedLH.js").then(h=>h.m7),__vite__mapDeps([4,2,3,1,5,6])),Zs()]),a=new s(t.url,t.config,t.fieldMap);await a.fetchResources({spatialReference:r.spatialReference,fields:r.fields});const l=await Ks(t.scaleExpression,r);return new Te(a,e,r,l,n,t.symbolOptions)}async _analyzeFeature(t,e,r,i,s){const n=t.readLegacyFeature(),a=this._scaleFn(n,r,i),l=this._attributeHash(n)+"-"+a,h=this._groupIdCache.get(l);if(h)return h;const u={...i,spatialReference:this._info.spatialReference,abortOptions:s,fields:this._info.fields},c=await this._loader.getSymbolAsync(n,u),f=this._schemaUtilsModule.createSymbolSchema(c,this._symbolOptions),d=H(f,this._info,e,s).then(_=>{if((_==null?void 0:_.type)!=="expanded-cim")return ue.error(new ot("mapview-bad-type",`Found unexpected type ${_==null?void 0:_.type} in dictionary response`)),null;_.hash+="-"+a;for(const m of _.layers)m.scaleFactor=a,m.templateHash+="-"+a;return this._templates.createTemplateGroup(_,null)});return this._groupIdCache.put(l,d,1),d}async analyze(t,e,r,i,s,n){const a=e.getCursor(),l=[];for(;a.next();)l.push(this._analyzeFeature(a,r,i,s,n));return Promise.all(l).then(h=>h.filter(A))}match(t,e,r,i,s){return null}_attributeHash(t){var r;let e="";for(const i of this._symbolFields){const s=(r=this._fieldMap)==null?void 0:r[i];s&&(e+=t.attributes[s]+"-")}return e}}export{mn as E,kr as a,R as b,Wt as e,H as i,wn as n,Gs as o,vn as x};
