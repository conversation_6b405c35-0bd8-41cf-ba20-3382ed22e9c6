package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicyContent;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalPolicyContentMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalPolicyContentPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalPolicyContentSaveRequest;

@Service
public class SsPortalPolicyContentServiceImpl implements SsPortalPolicyContentService {
    @Autowired
    private SsPortalPolicyContentMapper mapper;

    @Override
    public SsPortalPolicyContent findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<SsPortalPolicyContent> findAllConditional(SsPortalPolicyContentPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SsPortalPolicyContent save(SsPortalPolicyContentSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalPolicyContent entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
