import{_ as h}from"./Panel-DyoxrWMd.js";import{d as v,c as _,g as n,h as c,F as p,ax as r,q as k,i as C,n as D,p as g,an as i,aq as y,C as B}from"./index-r0dFAfgr.js";import{_ as T}from"./Search-NSrhrIa_.js";import"./v4-SoommWqA.js";const $={key:0,class:"table-box"},w={class:"table_left"},x={class:"table_right"},N=v({__name:"openDialog",props:{telport:{},config:{}},emits:["close"],setup(u,{expose:m}){const a=_(),l=_(!1),t=u;return m({openDialog:async()=>{var e;l.value=!0,(e=a.value)==null||e.Open()},closeDialog:()=>{var e;l.value=!1,(e=a.value)==null||e.Close()}}),(e,q)=>{const d=T,f=y,b=h;return n(),c(b,{ref_key:"refPanel",ref:a,"custom-class":"gis-bottom-drawer",telport:e.telport,draggable:!1,"max-min":!1,"before-close":()=>e.$emit("close")},{header:p(()=>[r(e.$slots,"header",{},()=>{var o;return[k(d,{ref:"refTab",config:(o=t.config)==null?void 0:o.tabs},null,8,["config"])]},!0)]),default:p(()=>[C(l)?(n(),D("div",$,[g("div",w,[r(e.$slots,"left",{},()=>{var o,s;return[(o=t.config)!=null&&o.left?(n(),c(f,{key:0,config:(s=t.config)==null?void 0:s.left.TableConfig},null,8,["config"])):i("",!0)]},!0)]),g("div",x,[r(e.$slots,"right",{},()=>{var o,s;return[(o=t.config)!=null&&o.right?(n(),c(f,{key:0,config:(s=t.config)==null?void 0:s.right.TableConfig},null,8,["config"])):i("",!0)]},!0)])])):i("",!0)]),_:3},8,["telport","before-close"])}}}),I=B(N,[["__scopeId","data-v-9657aeb9"]]);export{I as default};
