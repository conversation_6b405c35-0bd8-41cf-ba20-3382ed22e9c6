import{G as y,a as I,b as R,c as b,d as B,e as D}from"./zhandian-YaGuQZe6.js";import{c as v,a0 as G,a1 as h,a2 as F}from"./index-r0dFAfgr.js";const C=()=>{const n=v([]);return{stations:n,getStations:async(i,p)=>{var S;const d=await y({page:1,size:999,projectId:p||"",type:i||""});return n.value=((S=d.data)==null?void 0:S.data)||[],n.value}}},J=()=>{const n=v([]);return{getRealTimeData:async(i,p)=>{if(i){const d=await B(i,p);n.value=d.data||[]}else n.value=[];return n.value},realtimeList:n}},N=()=>{const n=v([]),f=async d=>{var g,m;const S=await D({stationType:d.type||"",projectId:d.projectId||((m=(g=G().navSelectedRange)==null?void 0:g.data)==null?void 0:m.id)});return n.value=S.data,n.value},i=v([]);return{getLatestData:f,latestData:n,getLatestDataFields:async d=>{var g;const S=await F({type:"stationInfo",key:d});return(g=S.data)!=null&&g[0]?i.value=JSON.parse(S.data[0].value):i.value=[],i.value},latestDataFields:i}},O=()=>{const n=v([]),f=v([]),i=v([]),p=v([]),d=async(t,o,s)=>{var r,l,a;const c=((a=(l=(await y({page:1,size:999,projectId:o||((r=G().navSelectedRange)==null?void 0:r.value),type:t||""})).data)==null?void 0:l.data)==null?void 0:a.map(w=>(w.isLeaf=!0,w)))||[],u=h(c,{label:"name",id:"id",value:"id",isLeaf:"isLeaf"},void 0,"station");return s&&(f.value=u),u},S=async(t,o,s)=>{var r,l;const c=((l=(r=(await y({page:1,size:999,type:t||"",projectId:o||""})).data)==null?void 0:r.data)==null?void 0:l.map(a=>(a.isLeaf=!0,a)))||[],u=h(c,{label:"name",id:"id",value:"id",isLeaf:"isLeaf"},void 0,"station");return s&&(n.value=u),u},g=async(t,o)=>{const s=await I({stationId:t}),e=h(s.data||[],{label:"name",id:"id",value:"id",children:"children"},void 0,"stationAttr");return o&&(i.value=e),e},m=async(t,o)=>{const s=await R({stationId:t}),e=h(s.data||[],{label:"type",value:"type",id:"type",children:"attrList"});return o&&(p.value=e),e},j=async(t,o)=>{const s=await b(t),e=h(s.data.data||[],{label:"name",value:"id",id:"id",children:"children"},void 0,"project",c=>{var r,l;const u=t||((l=(r=c.data)==null?void 0:r.nodeDetail)==null?void 0:l.type);return console.log(u),c.icon=c.data.type==="Project"?"iconfont icon-ditutubiao-":c.data.type==="Station"?"iconfont icon-tubiaozhizuomoban":"",c});return console.log(e),e},A=async(t,o,s,e)=>{var c,u,r,l;for(const a in t)o!=null&&o.includes((c=t[a].data)==null?void 0:c.type)?(t[a].disabled=!0,((u=t[a].children)==null?void 0:u.length)>0?A(t[a].children,o,s,e):s&&((r=t[a].data)==null?void 0:r.type)===e&&(t[a].children=[{id:0}],A(t[a].children,o,s,e))):t[a].children||s&&((l=t[a].data)==null?void 0:l.type)===e&&(t[a].children=[{id:0}],A(t[a].children,o,s,e));return t};return{getStationOption:d,getAllStationOption:S,getStationAllAttrs:g,getStationAttrGroups:m,StationAllAttrs:i,StationList:f,AllStations:n,StationAttrGroups:p,getStationTree:j,getStationTreeByDisabledType:A}},P=O;export{C as a,J as b,N as c,O as d,P as u};
