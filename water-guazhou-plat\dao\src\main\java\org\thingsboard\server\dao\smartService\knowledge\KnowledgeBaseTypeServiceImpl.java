package org.thingsboard.server.dao.smartService.knowledge;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeBaseType;
import org.thingsboard.server.dao.sql.smartService.knowledge.KnowledgeBaseTypeMapper;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class KnowledgeBaseTypeServiceImpl implements KnowledgeBaseTypeService {

    @Autowired
    private KnowledgeBaseTypeMapper knowledgeBaseTypeMapper;

    @Override
    public List<KnowledgeBaseType> getList(String tenantId) {
        // 查找所有
        QueryWrapper<KnowledgeBaseType> wrapper = new QueryWrapper<>();
        wrapper.eq("tenant_id", tenantId);
        wrapper.orderByAsc("order_num");
        List<KnowledgeBaseType> list = knowledgeBaseTypeMapper.selectList(wrapper);

        // 筛选根类型
        List<KnowledgeBaseType> knowledgeBaseTypes = list.stream().filter(knowledgeBaseType -> StringUtils.isBlank(knowledgeBaseType.getPid())).collect(Collectors.toList());
        this.buildTree(knowledgeBaseTypes, list);

        return knowledgeBaseTypes;
    }

    @Override
    public KnowledgeBaseType save(KnowledgeBaseType knowledgeBaseType) {
        knowledgeBaseType.setUpdateTime(new Date());
        if (StringUtils.isBlank(knowledgeBaseType.getId())) {
            knowledgeBaseType.setCreateTime(new Date());
            knowledgeBaseTypeMapper.insert(knowledgeBaseType);
        } else {
            knowledgeBaseTypeMapper.updateById(knowledgeBaseType);
        }

        return knowledgeBaseType;
    }

    @Override
    public int delete(List<String> ids) {
        // 删除子分类
        List<String> allIds = new ArrayList<>();
        for (String id : ids) {
            this.getAllIdByPid(id, allIds);
        }
        return knowledgeBaseTypeMapper.deleteBatchIds(allIds);
    }

    public void getAllIdByPid(String id, List<String> allIds) {
        if (StringUtils.isBlank(id)) {
            return;
        }
        allIds.add(id);
        Map queryMap = new HashMap<String, String>();
        queryMap.put("pid", id);
        List<KnowledgeBaseType> list = knowledgeBaseTypeMapper.selectByMap(queryMap);
        for (KnowledgeBaseType knowledgeBaseType : list) {
            this.getAllIdByPid(knowledgeBaseType.getId(), allIds);
        }
    }

    private void buildTree(List<KnowledgeBaseType> knowledgeBaseTypes, List<KnowledgeBaseType> list) {
        for (KnowledgeBaseType parent : knowledgeBaseTypes) {
            List<KnowledgeBaseType> children = parent.getChildren();
            for (KnowledgeBaseType knowledgeBaseType : list) {
                if (parent.getId().equals(knowledgeBaseType.getPid())) {
                    children.add(knowledgeBaseType);
                }
            }
            this.buildTree(children, list);
        }
    }
}
