import{d as N,c as S,eX as L,M as w,r as y,x as m,a8 as u,bX as W,S as V,ar as j,a9 as C,D as q,o as A,g as R,n as B,q as d,i as h,F as c,p as P,ak as G,G as g,_ as M,J as O,cu as $,bz as z,C as U}from"./index-r0dFAfgr.js";import{_ as X}from"./CardTable-rdWOL4_6.js";import{c as D}from"./data-XGHpLV70.js";import{I as F}from"./common-CvK_P_ao.js";import{f as J,h as K,i as H,g as Q,a as Y}from"./schedulingInstructions-DdZ9MUvq.js";import{c as Z}from"./zhandian-YaGuQZe6.js";import"./index-C9hz-UZb.js";const ee={class:"wrapper"},te=N({__name:"index",setup(ae){const I=S(L.get("departmentId")||""),{$btnPerms:x}=w(),p=S(),o=y({defaultExpandAll:!0,indexVisible:!0,handleSelectChange:t=>{o.selectList=t},selectList:[],rowKey:"id",columns:[{label:"指令类型",prop:"typeName"},{label:"发送站点",prop:"sendDeptName"},{label:"接收站点",prop:"receiveDeptName"},{label:"指令内容",prop:"sendContent"},{label:"启用泵组",prop:"enablePumpNames",width:"120px"},{label:"关闭泵组",prop:"disablePumpNames",width:"120px"},{label:"执行时间",prop:"executionTime"},{label:"指令备注",prop:"remark"},{label:"指令状态",prop:"commandStatus",tag:!0,tagColor:t=>{var e;return((e=D[t.commandStatus])==null?void 0:e.color)||""},formatter:t=>{var e;return((e=D[t.commandStatus])==null?void 0:e.value)||""}}],operationWidth:"160px",operations:[{type:"primary",text:"发送",icon:F.SEND,perm:x("RoleManageEdit"),click:t=>_(t)},{type:"danger",text:"撤销",perm:x("RoleManageDelete"),icon:F.DELETE,click:t=>v(t)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{o.pagination.page=t,o.pagination.limit=e,f()}}}),T=y({labelWidth:"100px",submit:t=>{t.receiveDeptId=t.receiveDeptId.join(","),t.enablePumps&&Array.isArray(t.enablePumps)&&(t.enablePumps=t.enablePumps.join(",")),t.disablePumps&&Array.isArray(t.disablePumps)&&(t.disablePumps=t.disablePumps.join(",")),J([t]).then(()=>{var e;m.success("新增成功"),(e=p.value)==null||e.resetForm(),f()}).catch(e=>{m.warning(e)})},defaultValue:{sendDeptId:I.value,receiveDeptId:[],enablePumps:[],disablePumps:[]},group:[{fields:[{xl:6,type:"select",label:"指令类型",field:"type",options:u(()=>a.types),rules:[{required:!0,message:"请选择指令类型"}],onChange:t=>{var s,l,n;const e=W(a.types,"children","id",t)||{};(s=p.value)!=null&&s.dataForm&&(p.value.dataForm={...p.value.dataForm,receiveDeptId:e.deptIdList.split(",")||[]}),e.name==="泵组调度"||(l=e.name)!=null&&l.includes("泵组")?(a.pumpList.length===0&&a.getPumpList(),a.showPumpFields=!0):(a.showPumpFields=!1,(n=p.value)!=null&&n.dataForm&&(p.value.dataForm.enablePumps=[],p.value.dataForm.disablePumps=[]))}},{xl:6,type:"select-tree",label:"接收站点",field:"receiveDeptId",multiple:!0,defaultExpandAll:!0,checkStrictly:!0,options:u(()=>a.WaterSupplyTree),rules:[{required:!0,message:"请输入接收站点"}]},{xl:6,type:"input",label:"指令内容",field:"sendContent"},{xl:6,type:"select",label:"启用泵组",field:"enablePumps",multiple:!0,options:u(()=>a.pumpList),hidden:u(()=>!a.showPumpFields)},{xl:6,type:"select",label:"关闭泵组",field:"disablePumps",multiple:!0,options:u(()=>a.pumpList),hidden:u(()=>!a.showPumpFields)},{readonly:!0,xl:6,type:"select-tree",label:"发送站点",field:"sendDeptId",checkStrictly:!0,options:u(()=>a.WaterSupplyTree),rules:[{required:!0,message:"请输入发送站点"}]},{xl:6,type:"datetime",label:"执行时间",field:"executionTime",rules:[{required:!0,message:"请输入执行时间"}]},{xl:24,type:"textarea",label:"备注",field:"remark",rules:[{required:!0,message:"请输入备注"}]}]}]}),v=t=>{V("确定撤销该指令","撤销提示").then(()=>{var s;let e=[];t?e=[t.id]:(s=o.selectList)==null||s.forEach(l=>{e.push(l.id)}),K(e).then(()=>{m.success("撤销成功"),o.selectList=[],f()}).catch(l=>{m.error(l.toString())})})};function E(){var t;(t=p.value)==null||t.Submit()}function _(t){var s;let e=[];t?e=[t.id]:(s=o.selectList)==null||s.forEach(l=>{e.push(l.id)}),H(e).then(()=>{m.success("发送成功"),o.selectList=[],f()}).catch(l=>{m.warning(l)})}function k(){var t;(t=p.value)==null||t.resetForm()}const a=y({WaterSupplyTree:[],getWaterSupplyTreeValue:()=>{j(2).then(e=>{a.WaterSupplyTree=C(e.data.data||[])})},types:[],getTypes:()=>{Q({size:-1,page:1}).then(e=>{a.types=C(e.data.data.data||[],"children",{label:"name",value:"id"})})},pumpList:[],showPumpFields:!1,getPumpList:()=>{Z("泵站").then(t=>{const e=l=>{let n=[];return l.forEach(i=>{var r;i.type==="Station"&&((r=i.nodeDetail)==null?void 0:r.type)==="泵站"&&n.push({label:i.name,value:i.id}),i.children&&i.children.length>0&&(n=n.concat(e(i.children)))}),n};let s=t.data;t.data&&t.data.data&&(s=t.data.data),a.pumpList=e(s||[])}).catch(()=>{a.pumpList=[]})}}),f=async()=>{const t={size:o.pagination.limit,page:o.pagination.page,sendUserId:q(L.get("userId")||""),commandStatus:"PENDING"};Y(t).then(e=>{const s=e.data.data.data||[];s.forEach(l=>{if(l.enablePumps){const n=l.enablePumps.split(",");l.enablePumpNames=n.map(i=>{const r=a.pumpList.find(b=>b.value===i);return r?r.label:i}).join(", ")}if(l.disablePumps){const n=l.disablePumps.split(",");l.disablePumpNames=n.map(i=>{const r=a.pumpList.find(b=>b.value===i);return r?r.label:i}).join(", ")}}),o.dataList=s,o.pagination.total=e.data.data.total||0})};return A(async()=>{f(),a.getWaterSupplyTreeValue(),a.getTypes(),a.getPumpList(),a.showPumpFields=!1}),(t,e)=>{const s=X,l=M,n=O,i=$,r=z;return R(),B("div",ee,[d(s,{class:"card-table",config:h(o)},null,8,["config"]),d(r,{class:"box-card mag_Top_10",style:{height:"300px",overflow:"auto"}},{default:c(()=>[d(l,{ref_key:"refForm",ref:p,config:h(T)},null,8,["config"]),e[6]||(e[6]=P("br",null,null,-1)),e[7]||(e[7]=P("br",null,null,-1)),e[8]||(e[8]=P("br",null,null,-1)),d(i,{class:"ml-4"},{default:c(()=>[d(n,{icon:h(G),type:"primary",onClick:E},{default:c(()=>e[2]||(e[2]=[g(" 添加 ")])),_:1},8,["icon"]),d(n,{onClick:e[0]||(e[0]=b=>_())},{default:c(()=>e[3]||(e[3]=[g(" 发送 ")])),_:1}),d(n,{onClick:k},{default:c(()=>e[4]||(e[4]=[g(" 重置 ")])),_:1}),d(n,{onClick:e[1]||(e[1]=b=>v())},{default:c(()=>e[5]||(e[5]=[g(" 撤销 ")])),_:1})]),_:1})]),_:1})])}}}),de=U(te,[["__scopeId","data-v-17857060"]]);export{de as default};
