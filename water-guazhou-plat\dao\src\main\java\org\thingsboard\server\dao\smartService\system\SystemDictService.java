package org.thingsboard.server.dao.smartService.system;

import org.thingsboard.server.dao.model.sql.smartService.system.SystemDict;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 字典
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface SystemDictService {
    List<SystemDict> getList(String pid, String tenantId);

    SystemDict save(SystemDict systemDict);

    IstarResponse delete(List<String> ids);

    List getTree(String tenantId);

    boolean checkCode(SystemDict systemDict, String tenantId);
}
