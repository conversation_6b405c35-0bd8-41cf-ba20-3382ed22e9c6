"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[212],{79583:(t,e,E)=>{function _(t){switch(t){case"u8":case"i8":return 1;case"u16":case"i16":return 2;case"u32":case"i32":case"f32":return 4;case"f64":return 8}}function n(t){switch(t){case"u8":case"u16":case"u32":return!1;case"i8":case"i16":case"i32":case"f32":case"f64":return!0}}function T(t){switch(t){case"u8":case"u16":case"u32":case"i8":case"i16":case"i32":return!0;case"f32":case"f64":return!1}}function A(t){switch(t){case"u8":return 255;case"u16":return 65535;case"u32":return 4294967295;case"i8":return 127;case"i16":return 32767;case"i32":return 2147483647;case"f32":return 3402823e32;case"f64":return 179769e303}}E.d(e,{B3:()=>n,Op:()=>A,U:()=>T,n1:()=>_})},47545:(t,e,E)=>{E.d(e,{d:()=>T});var _=E(22021),n=E(54388);function T(t,e,E){const T=Array.isArray(t),N=T?t.length/e:t.byteLength/(4*e),o=T?t:new Uint32Array(t,0,N*e),s=E?.minReduction??0,O=E?.originalIndices||null,I=O?O.length:0,S=E?.componentOffsets||null;let c=0;if(S)for(let t=0;t<S.length-1;t++){const e=S[t+1]-S[t];e>c&&(c=e)}else c=N;const C=Math.floor(1.1*c)+1;(null==i||i.length<2*C)&&(i=new Uint32Array((0,_.Sf)(2*C)));for(let t=0;t<2*C;t++)i[t]=0;let a=0;const u=!!S&&!!O,f=u?I:N;let L=(0,n.$z)(N/3);const D=new Uint32Array(I),U=1.96;let l=0!==s?Math.ceil(4*U*U/(s*s)*s*(1-s)):f,M=1,P=S?S[1]:f;for(let t=0;t<f;t++){if(t===l){const e=1-a/t;if(e+U*Math.sqrt(e*(1-e)/t)<s)return null;l*=2}if(t===P){for(let t=0;t<2*C;t++)i[t]=0;if(O)for(let t=S[M-1];t<S[M];t++)D[t]=L[O[t]];P=S[++M]}const E=u?O[t]:t,_=E*e,n=r(o,_,e);let T=n%C,R=a;for(;0!==i[2*T+1];){if(i[2*T]===n){const t=i[2*T+1]-1;if(A(o,_,t*e,e)){R=L[t];break}}T++,T>=C&&(T-=C)}R===a&&(i[2*T]=n,i[2*T+1]=E+1,a++),L[E]=R}if(0!==s&&1-a/N<s)return null;if(u){for(let t=S[M-1];t<D.length;t++)D[t]=L[O[t]];L=(0,n.mi)(D)}const G=T?new Array(a):new Uint32Array(a*e);a=0;for(let t=0;t<f;t++)L[t]===a&&(R(o,(u?O[t]:t)*e,G,a*e,e),a++);if(O&&!u){const t=new Uint32Array(I);for(let e=0;e<t.length;e++)t[e]=L[O[e]];L=(0,n.mi)(t)}return{buffer:Array.isArray(G)?G:G.buffer,indices:L,uniqueCount:a}}function A(t,e,E,_){for(let n=0;n<_;n++)if(t[e+n]!==t[E+n])return!1;return!0}function R(t,e,E,_,n){for(let T=0;T<n;T++)E[_+T]=t[e+T]}function r(t,e,E){let _=0;for(let n=0;n<E;n++)_=t[e+n]+_|0,_=_+(_<<11)+(_>>>2)|0;return _>>>0}let i=null},65576:(t,e,E)=>{E.d(e,{U$:()=>R});var _=E(56481),n=E(79583);class T{constructor(t,e){this.layout=t,this.buffer="number"==typeof e?new ArrayBuffer(e*t.stride):e;for(const e of t.fieldNames){const E=t.fields.get(e);this[e]=new E.constructor(this.buffer,E.offset,this.stride)}}get stride(){return this.layout.stride}get count(){return this.buffer.byteLength/this.stride}get byteLength(){return this.buffer.byteLength}getField(t,e){const E=this[t];return E&&E.elementCount===e.ElementCount&&E.elementType===e.ElementType?E:null}slice(t,e){return new T(this.layout,this.buffer.slice(t*this.stride,e*this.stride))}copyFrom(t,e,E,_){const n=this.stride;if(n%4==0){const T=new Uint32Array(t.buffer,e*n,_*n/4);new Uint32Array(this.buffer,E*n,_*n/4).set(T)}else{const T=new Uint8Array(t.buffer,e*n,_*n);new Uint8Array(this.buffer,E*n,_*n).set(T)}}}class A{constructor(){this.stride=0,this.fields=new Map,this.fieldNames=[]}vec2f(t,e){return this._appendField(t,_.Eu,e),this}vec2f64(t,e){return this._appendField(t,_.q6,e),this}vec3f(t,e){return this._appendField(t,_.ct,e),this}vec3f64(t,e){return this._appendField(t,_.fP,e),this}vec4f(t,e){return this._appendField(t,_.ek,e),this}vec4f64(t,e){return this._appendField(t,_.Cd,e),this}mat3f(t,e){return this._appendField(t,_.gK,e),this}mat3f64(t,e){return this._appendField(t,_.ey,e),this}mat4f(t,e){return this._appendField(t,_.bj,e),this}mat4f64(t,e){return this._appendField(t,_.O1,e),this}vec4u8(t,e){return this._appendField(t,_.mc,e),this}f32(t,e){return this._appendField(t,_.ly,e),this}f64(t,e){return this._appendField(t,_.oS,e),this}u8(t,e){return this._appendField(t,_.D_,e),this}u16(t,e){return this._appendField(t,_.av,e),this}i8(t,e){return this._appendField(t,_.Hz,e),this}vec2i8(t,e){return this._appendField(t,_.Vs,e),this}vec2i16(t,e){return this._appendField(t,_.or,e),this}vec2u8(t,e){return this._appendField(t,_.xA,e),this}vec4u16(t,e){return this._appendField(t,_.v6,e),this}u32(t,e){return this._appendField(t,_.Nu,e),this}_appendField(t,e,E){const _=e.ElementCount*(0,n.n1)(e.ElementType),T=this.stride;this.fields.set(t,{size:_,constructor:e,offset:T,optional:E}),this.stride+=_,this.fieldNames.push(t)}alignTo(t){return this.stride=Math.floor((this.stride+t-1)/t)*t,this}hasField(t){return this.fieldNames.includes(t)}createBuffer(t){return new T(this,t)}createView(t){return new T(this,t)}clone(){const t=new A;return t.stride=this.stride,t.fields=new Map,this.fields.forEach(((e,E)=>t.fields.set(E,e))),t.fieldNames=this.fieldNames.slice(),t.BufferType=this.BufferType,t}}function R(){return new A}},5329:(t,e,E)=>{E.d(e,{K:()=>T});var _=E(35371),n=E(21968);function T(t,e=0){const E=t.stride;return t.fieldNames.filter((e=>{const E=t.fields.get(e).optional;return!(E&&E.glPadding)})).map((_=>{const T=t.fields.get(_),R=T.constructor.ElementCount,r=A(T.constructor.ElementType),i=T.offset,N=!(!T.optional||!T.optional.glNormalized);return new n.G(_,R,r,i,E,N,e)}))}function A(t){const e=R[t];if(e)return e;throw new Error("BufferType not supported in WebGL")}const R={u8:_.g.UNSIGNED_BYTE,u16:_.g.UNSIGNED_SHORT,u32:_.g.UNSIGNED_INT,i8:_.g.BYTE,i16:_.g.SHORT,i32:_.g.INT,f32:_.g.FLOAT}},54388:(t,e,E)=>{E.d(e,{$z:()=>T,DX:()=>N,mi:()=>n,p:()=>i});var _=E(1533);function n(t){if(Array.isArray(t)){if(t.length<_.DB)return t;for(const e of t)if(e>=65536)return new Uint32Array(t);return new Uint16Array(t)}if(t.length<_.DB)return Array.from(t);if(t.BYTES_PER_ELEMENT===Uint16Array.BYTES_PER_ELEMENT)return t;for(const e of t)if(e>=65536)return t;return new Uint16Array(t)}function T(t){const e=3*t;return e<=_.DB?new Array(e):e<=65536?new Uint16Array(e):new Uint32Array(e)}let A=(()=>{const t=new Uint32Array(131072);for(let e=0;e<t.length;++e)t[e]=e;return t})();const R=[0],r=(()=>{const t=new Uint16Array(65536);for(let e=0;e<t.length;++e)t[e]=e;return t})();function i(t){if(1===t)return R;if(t<_.DB)return Array.from(new Uint16Array(r.buffer,0,t));if(t<r.length)return new Uint16Array(r.buffer,0,t);if(t>A.length){const e=Math.max(2*A.length,t);A=new Uint32Array(e);for(let t=0;t<A.length;t++)A[t]=t}return new Uint32Array(A.buffer,0,t)}function N(t){if(1===t)return R;if(t<_.DB)return Array.from(new Uint16Array(r.buffer,0,t));if(t<r.length)return new Uint16Array(r.slice(0,t));if(t>A.length){const e=new Uint32Array(t);for(let t=0;t<e.length;t++)e[t]=t;return e}return new Uint32Array(A.slice(0,t))}},35065:(t,e,E)=>{var _;E.d(e,{T:()=>_}),function(t){t.POSITION="position",t.NORMAL="normal",t.UV0="uv0",t.AUXPOS1="auxpos1",t.AUXPOS2="auxpos2",t.COLOR="color",t.SYMBOLCOLOR="symbolColor",t.SIZE="size",t.TANGENT="tangent",t.OFFSET="offset",t.SUBDIVISIONFACTOR="subdivisionFactor",t.COLORFEATUREATTRIBUTE="colorFeatureAttribute",t.SIZEFEATUREATTRIBUTE="sizeFeatureAttribute",t.OPACITYFEATUREATTRIBUTE="opacityFeatureAttribute",t.DISTANCETOSTART="distanceToStart",t.UVMAPSPACE="uvMapSpace",t.BOUNDINGRECT="boundingRect",t.UVREGION="uvRegion",t.NORMALCOMPRESSED="normalCompressed",t.PROFILERIGHT="profileRight",t.PROFILEUP="profileUp",t.PROFILEVERTEXANDNORMAL="profileVertexAndNormal",t.FEATUREVALUE="featureValue",t.MODELORIGINHI="modelOriginHi",t.MODELORIGINLO="modelOriginLo",t.MODEL="model",t.MODELNORMAL="modelNormal",t.INSTANCECOLOR="instanceColor",t.INSTANCEFEATUREATTRIBUTE="instanceFeatureAttribute",t.LOCALTRANSFORM="localTransform",t.GLOBALTRANSFORM="globalTransform",t.BOUNDINGSPHERE="boundingSphere",t.MODELORIGIN="modelOrigin",t.MODELSCALEFACTORS="modelScaleFactors",t.FEATUREATTRIBUTE="featureAttribute",t.STATE="state",t.LODLEVEL="lodLevel",t.POSITION0="position0",t.POSITION1="position1",t.NORMALA="normalA",t.NORMALB="normalB",t.COMPONENTINDEX="componentIndex",t.VARIANTOFFSET="variantOffset",t.VARIANTSTROKE="variantStroke",t.VARIANTEXTENSION="variantExtension",t.U8PADDING="u8padding",t.U16PADDING="u16padding",t.SIDENESS="sideness",t.START="start",t.END="end",t.UP="up",t.EXTRUDE="extrude",t.OBJECTANDLAYERIDCOLOR="objectAndLayerIdColor",t.OBJECTANDLAYERIDCOLOR_INSTANCED="objectAndLayerIdColor_instanced"}(_||(_={}))},17288:(t,e,E)=>{E.d(e,{Hr:()=>N,dG:()=>i,tf:()=>A});var _=E(5329),n=E(65576),T=E(35065);const A=(0,n.U$)().vec3f(T.T.POSITION).u16(T.T.COMPONENTINDEX).u16(T.T.U16PADDING),R=(0,n.U$)().vec2u8(T.T.SIDENESS),r=((0,_.K)(R),(0,n.U$)().vec3f(T.T.POSITION0).vec3f(T.T.POSITION1).u16(T.T.COMPONENTINDEX).u8(T.T.VARIANTOFFSET,{glNormalized:!0}).u8(T.T.VARIANTSTROKE).u8(T.T.VARIANTEXTENSION,{glNormalized:!0}).u8(T.T.U8PADDING,{glPadding:!0}).u16(T.T.U16PADDING,{glPadding:!0})),i=r.clone().vec3f(T.T.NORMAL),N=r.clone().vec3f(T.T.NORMALA).vec3f(T.T.NORMALB);new Map([[T.T.POSITION0,0],[T.T.POSITION1,1],[T.T.COMPONENTINDEX,2],[T.T.VARIANTOFFSET,3],[T.T.VARIANTSTROKE,4],[T.T.VARIANTEXTENSION,5],[T.T.NORMAL,6],[T.T.NORMALA,6],[T.T.NORMALB,7],[T.T.SIDENESS,8]])},97411:(t,e,E)=>{E.d(e,{n:()=>N});var _=E(67676),n=E(22021),T=E(17896),A=E(65617);const R=-1;var r,i;function N(t,e,E,A=C){const r=t.vertices.position,i=t.vertices.componentIndex,N=(0,n.Vl)(A.anglePlanar),c=(0,n.Vl)(A.angleSignificantEdge),a=Math.cos(c),u=Math.cos(N),f=S.edge,L=f.position0,D=f.position1,U=f.faceNormal0,l=f.faceNormal1,M=I(t),P=function(t){const e=t.faces.length/3,E=t.faces,_=t.neighbors;let n=0;for(let t=0;t<e;t++){const e=_[3*t+0],T=_[3*t+1],A=_[3*t+2],r=E[3*t+0],i=E[3*t+1],N=E[3*t+2];n+=e===R||r<i?1:0,n+=T===R||i<N?1:0,n+=A===R||N<r?1:0}const T=new Int32Array(4*n);let A=0;for(let t=0;t<e;t++){const e=_[3*t+0],n=_[3*t+1],r=_[3*t+2],i=E[3*t+0],N=E[3*t+1],o=E[3*t+2];(e===R||i<N)&&(T[A++]=i,T[A++]=N,T[A++]=t,T[A++]=e),(n===R||N<o)&&(T[A++]=N,T[A++]=o,T[A++]=t,T[A++]=n),(r===R||o<i)&&(T[A++]=o,T[A++]=i,T[A++]=t,T[A++]=r)}return T}(t),G=P.length/4,F=e.allocate(G);let B=0;const d=G,h=E.allocate(d);let H=0,g=0,p=0;const m=(0,_.w6)(0,G),V=new Float32Array(G);V.forEach(((t,e,E)=>{r.getVec(P[4*e+0],L),r.getVec(P[4*e+1],D),E[e]=(0,T.i)(L,D)})),m.sort(((t,e)=>V[e]-V[t]));const w=new Array,y=new Array;for(let t=0;t<G;t++){const _=m[t],n=V[_],A=P[4*_+0],I=P[4*_+1],S=P[4*_+2],c=P[4*_+3],C=c===R;if(r.getVec(A,L),r.getVec(I,D),C)(0,T.s)(U,M[3*S+0],M[3*S+1],M[3*S+2]),(0,T.c)(l,U),f.componentIndex=i.get(A),f.cosAngle=(0,T.e)(U,l);else{if((0,T.s)(U,M[3*S+0],M[3*S+1],M[3*S+2]),(0,T.s)(l,M[3*c+0],M[3*c+1],M[3*c+2]),f.componentIndex=i.get(A),f.cosAngle=(0,T.e)(U,l),s(f,u))continue;f.cosAngle<-.9999&&(0,T.c)(l,U)}g+=n,p++,C||o(f,a)?(e.write(F,B++,f),w.push(n)):O(f,N)&&(E.write(h,H++,f),y.push(n))}const Y=new Float32Array(w.reverse()),X=new Float32Array(y.reverse());return{regular:{instancesData:e.trim(F,B),lodInfo:{lengths:Y}},silhouette:{instancesData:E.trim(h,H),lodInfo:{lengths:X}},averageEdgeLength:g/p}}function o(t,e){return t.cosAngle<e}function s(t,e){return t.cosAngle>e}function O(t,e){const E=(0,n.ZF)(t.cosAngle),_=S.fwd,A=S.ortho;return(0,T.r)(_,t.position1,t.position0),E*((0,T.e)((0,T.f)(A,t.faceNormal0,t.faceNormal1),_)>0?-1:1)>e}function I(t){const e=t.faces.length/3,E=t.vertices.position,_=t.faces,n=c.v0,A=c.v1,R=c.v2,r=new Float32Array(3*e);for(let t=0;t<e;t++){const e=_[3*t+0],i=_[3*t+1],N=_[3*t+2];E.getVec(e,n),E.getVec(i,A),E.getVec(N,R),(0,T.b)(A,A,n),(0,T.b)(R,R,n),(0,T.f)(n,A,R),(0,T.n)(n,n),r[3*t+0]=n[0],r[3*t+1]=n[1],r[3*t+2]=n[2]}return r}(i=r||(r={}))[i.SOLID=0]="SOLID",i[i.SKETCH=1]="SKETCH";const S={edge:{position0:(0,A.c)(),position1:(0,A.c)(),faceNormal0:(0,A.c)(),faceNormal1:(0,A.c)(),componentIndex:0,cosAngle:0},ortho:(0,A.c)(),fwd:(0,A.c)()},c={v0:(0,A.c)(),v1:(0,A.c)(),v2:(0,A.c)()},C={anglePlanar:4,angleSignificantEdge:35}},212:(t,e,E)=>{E.d(e,{Kl:()=>l,n_:()=>d,kY:()=>M,Yr:()=>B});var _=E(47545);function n(t,e,E){const _=e/3,n=new Uint32Array(E+1),T=new Uint32Array(E+1),A=(t,e)=>{t<e?n[t+1]++:T[e+1]++};for(let e=0;e<_;e++){const E=t[3*e],_=t[3*e+1],n=t[3*e+2];A(E,_),A(_,n),A(n,E)}let R=0,r=0;for(let t=0;t<E;t++){const e=n[t+1],E=T[t+1];n[t+1]=R,T[t+1]=r,R+=e,r+=E}const i=new Uint32Array(6*_),N=n[E],o=(t,e,E)=>{if(t<e){const _=n[t+1]++;i[2*_]=e,i[2*_+1]=E}else{const _=T[e+1]++;i[2*N+2*_]=t,i[2*N+2*_+1]=E}};for(let e=0;e<_;e++){const E=t[3*e],_=t[3*e+1],n=t[3*e+2];o(E,_,e),o(_,n,e),o(n,E,e)}const s=(t,e)=>{const E=2*t,_=e-t;for(let t=1;t<_;t++){const e=i[E+2*t],_=i[E+2*t+1];let n=t-1;for(;n>=0&&i[E+2*n]>e;n--)i[E+2*n+2]=i[E+2*n],i[E+2*n+3]=i[E+2*n+1];i[E+2*n+2]=e,i[E+2*n+3]=_}};for(let t=0;t<E;t++)s(n[t],n[t+1]),s(N+T[t],N+T[t+1]);const O=new Int32Array(3*_),I=(e,E)=>e===t[3*E]?0:e===t[3*E+1]?1:e===t[3*E+2]?2:-1,S=(t,e)=>{const E=I(t,e);O[3*e+E]=-1},c=(t,e,E,_)=>{const n=I(t,e);O[3*e+n]=_;const T=I(E,_);O[3*_+T]=e};for(let t=0;t<E;t++){let e=n[t];const E=n[t+1];let _=T[t];const A=T[t+1];for(;e<E&&_<A;){const E=i[2*e],n=i[2*N+2*_];E===n?(c(t,i[2*e+1],n,i[2*N+2*_+1]),e++,_++):E<n?(S(t,i[2*e+1]),e++):(S(n,i[2*N+2*_+1]),_++)}for(;e<E;)S(t,i[2*e+1]),e++;for(;_<A;)S(i[2*N+2*_],i[2*N+2*_+1]),_++}return O}var T=E(65576),A=E(35065),R=E(17288),r=E(77734),i=E(17896),N=E(65617),o=E(5329);class s{updateSettings(t){this.settings=t,this._edgeHashFunction=t.reducedPrecision?C:c}write(t,e,E){const _=this._edgeHashFunction(E);D.seed=_;const n=D.getIntRange(0,255),T=D.getIntRange(0,this.settings.variants-1),A=D.getFloat(),R=255*(.5*function(t,e){const E=t<0?-1:1;return Math.abs(t)**1.2*E}(-(1-Math.min(A/.7,1))+Math.max(0,A-.7)/(1-.7))+.5);t.position0.setVec(e,E.position0),t.position1.setVec(e,E.position1),t.componentIndex.set(e,E.componentIndex),t.variantOffset.set(e,n),t.variantStroke.set(e,T),t.variantExtension.set(e,R)}trim(t,e){return t.slice(0,e)}}const O=new Float32Array(6),I=new Uint32Array(O.buffer),S=new Uint32Array(1);function c(t){const e=O;e[0]=t.position0[0],e[1]=t.position0[1],e[2]=t.position0[2],e[3]=t.position1[0],e[4]=t.position1[1],e[5]=t.position1[2],S[0]=5381;for(let t=0;t<I.length;t++)S[0]=31*S[0]+I[t];return S[0]}function C(t){const e=O;e[0]=a(t.position0[0]),e[1]=a(t.position0[1]),e[2]=a(t.position0[2]),e[3]=a(t.position1[0]),e[4]=a(t.position1[1]),e[5]=a(t.position1[2]),S[0]=5381;for(let t=0;t<I.length;t++)S[0]=31*S[0]+I[t];return S[0]}function a(t){return Math.round(1e4*t)/1e4}class u{constructor(){this._commonWriter=new s}updateSettings(t){this._commonWriter.updateSettings(t)}allocate(t){return R.dG.createBuffer(t)}write(t,e,E){this._commonWriter.write(t,e,E),(0,i.a)(L,E.faceNormal0,E.faceNormal1),(0,i.n)(L,L),t.normal.setVec(e,L)}trim(t,e){return this._commonWriter.trim(t,e)}}u.Layout=R.dG,u.glLayout=(0,o.K)(R.dG,1);class f{constructor(){this._commonWriter=new s}updateSettings(t){this._commonWriter.updateSettings(t)}allocate(t){return R.Hr.createBuffer(t)}write(t,e,E){this._commonWriter.write(t,e,E),t.normalA.setVec(e,E.faceNormal0),t.normalB.setVec(e,E.faceNormal1)}trim(t,e){return this._commonWriter.trim(t,e)}}f.Layout=R.Hr,f.glLayout=(0,o.K)(R.Hr,1);const L=(0,N.c)(),D=new r.Z;var U=E(97411);function l(t){const e=M(t.data,t.skipDeduplicate,t.indices,t.indicesLength);return G.updateSettings(t.writerSettings),F.updateSettings(t.writerSettings),(0,U.n)(e,G,F)}function M(t,e,E,T){if(e){const e=n(E,T,t.count);return new P(E,T,e,t)}const A=(0,_.d)(t.buffer,t.stride/4,{originalIndices:E,originalIndicesLength:T}),r=n(A.indices,T,A.uniqueCount);return{faces:A.indices,facesLength:A.indices.length,neighbors:r,vertices:R.tf.createView(A.buffer)}}class P{constructor(t,e,E,_){this.faces=t,this.facesLength=e,this.neighbors=E,this.vertices=_}}const G=new u,F=new f,B=(0,T.U$)().vec3f(A.T.POSITION0).vec3f(A.T.POSITION1),d=(0,T.U$)().vec3f(A.T.POSITION0).vec3f(A.T.POSITION1).u16(A.T.COMPONENTINDEX).u16(A.T.U16PADDING,{glPadding:!0})},21968:(t,e,E)=>{E.d(e,{G:()=>_});class _{constructor(t,e,E,_,n,T=!1,A=0){this.name=t,this.count=e,this.type=E,this.offset=_,this.stride=n,this.normalized=T,this.divisor=A}}},35371:(t,e,E)=>{var _,n,T,A,R,r,i,N,o,s,O,I,S,c,C,a,u,f,L,D,U,l,M,P;E.d(e,{Br:()=>a,LR:()=>r,Lm:()=>U,Lu:()=>G,MX:()=>n,No:()=>S,OU:()=>l,Tg:()=>u,VI:()=>c,VY:()=>P,Wf:()=>i,_g:()=>M,cw:()=>O,db:()=>A,e8:()=>I,g:()=>N,l1:()=>f,lP:()=>C,q_:()=>F,qi:()=>D,w0:()=>R,wb:()=>o,xS:()=>s,zi:()=>T}),function(t){t[t.DEPTH_BUFFER_BIT=256]="DEPTH_BUFFER_BIT",t[t.STENCIL_BUFFER_BIT=1024]="STENCIL_BUFFER_BIT",t[t.COLOR_BUFFER_BIT=16384]="COLOR_BUFFER_BIT"}(_||(_={})),function(t){t[t.POINTS=0]="POINTS",t[t.LINES=1]="LINES",t[t.LINE_LOOP=2]="LINE_LOOP",t[t.LINE_STRIP=3]="LINE_STRIP",t[t.TRIANGLES=4]="TRIANGLES",t[t.TRIANGLE_STRIP=5]="TRIANGLE_STRIP",t[t.TRIANGLE_FAN=6]="TRIANGLE_FAN"}(n||(n={})),function(t){t[t.ZERO=0]="ZERO",t[t.ONE=1]="ONE",t[t.SRC_COLOR=768]="SRC_COLOR",t[t.ONE_MINUS_SRC_COLOR=769]="ONE_MINUS_SRC_COLOR",t[t.SRC_ALPHA=770]="SRC_ALPHA",t[t.ONE_MINUS_SRC_ALPHA=771]="ONE_MINUS_SRC_ALPHA",t[t.DST_ALPHA=772]="DST_ALPHA",t[t.ONE_MINUS_DST_ALPHA=773]="ONE_MINUS_DST_ALPHA",t[t.DST_COLOR=774]="DST_COLOR",t[t.ONE_MINUS_DST_COLOR=775]="ONE_MINUS_DST_COLOR",t[t.SRC_ALPHA_SATURATE=776]="SRC_ALPHA_SATURATE",t[t.CONSTANT_COLOR=32769]="CONSTANT_COLOR",t[t.ONE_MINUS_CONSTANT_COLOR=32770]="ONE_MINUS_CONSTANT_COLOR",t[t.CONSTANT_ALPHA=32771]="CONSTANT_ALPHA",t[t.ONE_MINUS_CONSTANT_ALPHA=32772]="ONE_MINUS_CONSTANT_ALPHA"}(T||(T={})),function(t){t[t.ADD=32774]="ADD",t[t.SUBTRACT=32778]="SUBTRACT",t[t.REVERSE_SUBTRACT=32779]="REVERSE_SUBTRACT"}(A||(A={})),function(t){t[t.ARRAY_BUFFER=34962]="ARRAY_BUFFER",t[t.ELEMENT_ARRAY_BUFFER=34963]="ELEMENT_ARRAY_BUFFER",t[t.UNIFORM_BUFFER=35345]="UNIFORM_BUFFER",t[t.PIXEL_PACK_BUFFER=35051]="PIXEL_PACK_BUFFER",t[t.PIXEL_UNPACK_BUFFER=35052]="PIXEL_UNPACK_BUFFER",t[t.COPY_READ_BUFFER=36662]="COPY_READ_BUFFER",t[t.COPY_WRITE_BUFFER=36663]="COPY_WRITE_BUFFER"}(R||(R={})),function(t){t[t.FRONT=1028]="FRONT",t[t.BACK=1029]="BACK",t[t.FRONT_AND_BACK=1032]="FRONT_AND_BACK"}(r||(r={})),function(t){t[t.CW=2304]="CW",t[t.CCW=2305]="CCW"}(i||(i={})),function(t){t[t.BYTE=5120]="BYTE",t[t.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",t[t.SHORT=5122]="SHORT",t[t.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",t[t.INT=5124]="INT",t[t.UNSIGNED_INT=5125]="UNSIGNED_INT",t[t.FLOAT=5126]="FLOAT"}(N||(N={})),function(t){t[t.NEVER=512]="NEVER",t[t.LESS=513]="LESS",t[t.EQUAL=514]="EQUAL",t[t.LEQUAL=515]="LEQUAL",t[t.GREATER=516]="GREATER",t[t.NOTEQUAL=517]="NOTEQUAL",t[t.GEQUAL=518]="GEQUAL",t[t.ALWAYS=519]="ALWAYS"}(o||(o={})),function(t){t[t.ZERO=0]="ZERO",t[t.KEEP=7680]="KEEP",t[t.REPLACE=7681]="REPLACE",t[t.INCR=7682]="INCR",t[t.DECR=7683]="DECR",t[t.INVERT=5386]="INVERT",t[t.INCR_WRAP=34055]="INCR_WRAP",t[t.DECR_WRAP=34056]="DECR_WRAP"}(s||(s={})),function(t){t[t.NEAREST=9728]="NEAREST",t[t.LINEAR=9729]="LINEAR",t[t.NEAREST_MIPMAP_NEAREST=9984]="NEAREST_MIPMAP_NEAREST",t[t.LINEAR_MIPMAP_NEAREST=9985]="LINEAR_MIPMAP_NEAREST",t[t.NEAREST_MIPMAP_LINEAR=9986]="NEAREST_MIPMAP_LINEAR",t[t.LINEAR_MIPMAP_LINEAR=9987]="LINEAR_MIPMAP_LINEAR"}(O||(O={})),function(t){t[t.CLAMP_TO_EDGE=33071]="CLAMP_TO_EDGE",t[t.REPEAT=10497]="REPEAT",t[t.MIRRORED_REPEAT=33648]="MIRRORED_REPEAT"}(I||(I={})),function(t){t[t.TEXTURE_2D=3553]="TEXTURE_2D",t[t.TEXTURE_CUBE_MAP=34067]="TEXTURE_CUBE_MAP",t[t.TEXTURE_3D=32879]="TEXTURE_3D",t[t.TEXTURE_CUBE_MAP_POSITIVE_X=34069]="TEXTURE_CUBE_MAP_POSITIVE_X",t[t.TEXTURE_CUBE_MAP_NEGATIVE_X=34070]="TEXTURE_CUBE_MAP_NEGATIVE_X",t[t.TEXTURE_CUBE_MAP_POSITIVE_Y=34071]="TEXTURE_CUBE_MAP_POSITIVE_Y",t[t.TEXTURE_CUBE_MAP_NEGATIVE_Y=34072]="TEXTURE_CUBE_MAP_NEGATIVE_Y",t[t.TEXTURE_CUBE_MAP_POSITIVE_Z=34073]="TEXTURE_CUBE_MAP_POSITIVE_Z",t[t.TEXTURE_CUBE_MAP_NEGATIVE_Z=34074]="TEXTURE_CUBE_MAP_NEGATIVE_Z",t[t.TEXTURE_2D_ARRAY=35866]="TEXTURE_2D_ARRAY"}(S||(S={})),function(t){t[t.DEPTH_COMPONENT=6402]="DEPTH_COMPONENT",t[t.DEPTH_STENCIL=34041]="DEPTH_STENCIL",t[t.ALPHA=6406]="ALPHA",t[t.RGB=6407]="RGB",t[t.RGBA=6408]="RGBA",t[t.LUMINANCE=6409]="LUMINANCE",t[t.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA",t[t.RED=6403]="RED",t[t.RG=33319]="RG",t[t.RED_INTEGER=36244]="RED_INTEGER",t[t.RG_INTEGER=33320]="RG_INTEGER",t[t.RGB_INTEGER=36248]="RGB_INTEGER",t[t.RGBA_INTEGER=36249]="RGBA_INTEGER"}(c||(c={})),function(t){t[t.RGBA4=32854]="RGBA4",t[t.R16F=33325]="R16F",t[t.RG16F=33327]="RG16F",t[t.RGB32F=34837]="RGB32F",t[t.RGBA16F=34842]="RGBA16F",t[t.R32F=33326]="R32F",t[t.RG32F=33328]="RG32F",t[t.RGBA32F=34836]="RGBA32F",t[t.R11F_G11F_B10F=35898]="R11F_G11F_B10F",t[t.RGB8=32849]="RGB8",t[t.RGBA8=32856]="RGBA8",t[t.RGB5_A1=32855]="RGB5_A1",t[t.R8=33321]="R8",t[t.RG8=33323]="RG8",t[t.R8I=33329]="R8I",t[t.R8UI=33330]="R8UI",t[t.R16I=33331]="R16I",t[t.R16UI=33332]="R16UI",t[t.R32I=33333]="R32I",t[t.R32UI=33334]="R32UI",t[t.RG8I=33335]="RG8I",t[t.RG8UI=33336]="RG8UI",t[t.RG16I=33337]="RG16I",t[t.RG16UI=33338]="RG16UI",t[t.RG32I=33339]="RG32I",t[t.RG32UI=33340]="RG32UI",t[t.RGB16F=34843]="RGB16F",t[t.RGB9_E5=35901]="RGB9_E5",t[t.SRGB8=35905]="SRGB8",t[t.SRGB8_ALPHA8=35907]="SRGB8_ALPHA8",t[t.RGB565=36194]="RGB565",t[t.RGBA32UI=36208]="RGBA32UI",t[t.RGB32UI=36209]="RGB32UI",t[t.RGBA16UI=36214]="RGBA16UI",t[t.RGB16UI=36215]="RGB16UI",t[t.RGBA8UI=36220]="RGBA8UI",t[t.RGB8UI=36221]="RGB8UI",t[t.RGBA32I=36226]="RGBA32I",t[t.RGB32I=36227]="RGB32I",t[t.RGBA16I=36232]="RGBA16I",t[t.RGB16I=36233]="RGB16I",t[t.RGBA8I=36238]="RGBA8I",t[t.RGB8I=36239]="RGB8I",t[t.R8_SNORM=36756]="R8_SNORM",t[t.RG8_SNORM=36757]="RG8_SNORM",t[t.RGB8_SNORM=36758]="RGB8_SNORM",t[t.RGBA8_SNORM=36759]="RGBA8_SNORM",t[t.RGB10_A2=32857]="RGB10_A2",t[t.RGB10_A2UI=36975]="RGB10_A2UI"}(C||(C={})),function(t){t[t.FLOAT=5126]="FLOAT",t[t.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",t[t.UNSIGNED_INT_24_8=34042]="UNSIGNED_INT_24_8",t[t.UNSIGNED_SHORT_4_4_4_4=32819]="UNSIGNED_SHORT_4_4_4_4",t[t.UNSIGNED_SHORT_5_5_5_1=32820]="UNSIGNED_SHORT_5_5_5_1",t[t.UNSIGNED_SHORT_5_6_5=33635]="UNSIGNED_SHORT_5_6_5",t[t.BYTE=5120]="BYTE",t[t.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",t[t.SHORT=5122]="SHORT",t[t.UNSIGNED_INT=5125]="UNSIGNED_INT",t[t.INT=5124]="INT",t[t.HALF_FLOAT=5131]="HALF_FLOAT",t[t.UNSIGNED_INT_2_10_10_10_REV=33640]="UNSIGNED_INT_2_10_10_10_REV",t[t.UNSIGNED_INT_10F_11F_11F_REV=35899]="UNSIGNED_INT_10F_11F_11F_REV",t[t.UNSIGNED_INT_5_9_9_9_REV=35902]="UNSIGNED_INT_5_9_9_9_REV",t[t.FLOAT_32_UNSIGNED_INT_24_8_REV=36269]="FLOAT_32_UNSIGNED_INT_24_8_REV"}(a||(a={})),function(t){t[t.DEPTH_COMPONENT16=33189]="DEPTH_COMPONENT16",t[t.STENCIL_INDEX8=36168]="STENCIL_INDEX8",t[t.DEPTH_STENCIL=34041]="DEPTH_STENCIL",t[t.DEPTH_COMPONENT24=33190]="DEPTH_COMPONENT24",t[t.DEPTH_COMPONENT32F=36012]="DEPTH_COMPONENT32F",t[t.DEPTH24_STENCIL8=35056]="DEPTH24_STENCIL8",t[t.DEPTH32F_STENCIL8=36013]="DEPTH32F_STENCIL8"}(u||(u={})),function(t){t[t.STATIC_DRAW=35044]="STATIC_DRAW",t[t.DYNAMIC_DRAW=35048]="DYNAMIC_DRAW",t[t.STREAM_DRAW=35040]="STREAM_DRAW",t[t.STATIC_READ=35045]="STATIC_READ",t[t.DYNAMIC_READ=35049]="DYNAMIC_READ",t[t.STREAM_READ=35041]="STREAM_READ",t[t.STATIC_COPY=35046]="STATIC_COPY",t[t.DYNAMIC_COPY=35050]="DYNAMIC_COPY",t[t.STREAM_COPY=35042]="STREAM_COPY"}(f||(f={})),function(t){t[t.FRAGMENT_SHADER=35632]="FRAGMENT_SHADER",t[t.VERTEX_SHADER=35633]="VERTEX_SHADER"}(L||(L={})),function(t){t[t.FRAMEBUFFER=36160]="FRAMEBUFFER",t[t.READ_FRAMEBUFFER=36008]="READ_FRAMEBUFFER",t[t.DRAW_FRAMEBUFFER=36009]="DRAW_FRAMEBUFFER"}(D||(D={})),function(t){t[t.TEXTURE=0]="TEXTURE",t[t.RENDER_BUFFER=1]="RENDER_BUFFER",t[t.CUBEMAP=2]="CUBEMAP"}(U||(U={})),function(t){t[t.NONE=0]="NONE",t[t.DEPTH_RENDER_BUFFER=1]="DEPTH_RENDER_BUFFER",t[t.STENCIL_RENDER_BUFFER=2]="STENCIL_RENDER_BUFFER",t[t.DEPTH_STENCIL_RENDER_BUFFER=3]="DEPTH_STENCIL_RENDER_BUFFER",t[t.DEPTH_STENCIL_TEXTURE=4]="DEPTH_STENCIL_TEXTURE"}(l||(l={})),function(t){t[t.Texture=0]="Texture",t[t.BufferObject=1]="BufferObject",t[t.VertexArrayObject=2]="VertexArrayObject",t[t.Shader=3]="Shader",t[t.Program=4]="Program",t[t.FramebufferObject=5]="FramebufferObject",t[t.Renderbuffer=6]="Renderbuffer",t[t.Sync=7]="Sync",t[t.COUNT=8]="COUNT"}(M||(M={})),function(t){t[t.COLOR_ATTACHMENT0=36064]="COLOR_ATTACHMENT0",t[t.COLOR_ATTACHMENT1=36065]="COLOR_ATTACHMENT1",t[t.COLOR_ATTACHMENT2=36066]="COLOR_ATTACHMENT2",t[t.COLOR_ATTACHMENT3=36067]="COLOR_ATTACHMENT3",t[t.COLOR_ATTACHMENT4=36068]="COLOR_ATTACHMENT4",t[t.COLOR_ATTACHMENT5=36069]="COLOR_ATTACHMENT5",t[t.COLOR_ATTACHMENT6=36070]="COLOR_ATTACHMENT6",t[t.COLOR_ATTACHMENT7=36071]="COLOR_ATTACHMENT7",t[t.COLOR_ATTACHMENT8=36072]="COLOR_ATTACHMENT8",t[t.COLOR_ATTACHMENT9=36073]="COLOR_ATTACHMENT9",t[t.COLOR_ATTACHMENT10=36074]="COLOR_ATTACHMENT10",t[t.COLOR_ATTACHMENT11=36075]="COLOR_ATTACHMENT11",t[t.COLOR_ATTACHMENT12=36076]="COLOR_ATTACHMENT12",t[t.COLOR_ATTACHMENT13=36077]="COLOR_ATTACHMENT13",t[t.COLOR_ATTACHMENT14=36078]="COLOR_ATTACHMENT14",t[t.COLOR_ATTACHMENT15=36079]="COLOR_ATTACHMENT15"}(P||(P={}));const G=33306;var F,B,d,h,H,g,p;!function(t){t[t.COMPRESSED_RGB_S3TC_DXT1_EXT=33776]="COMPRESSED_RGB_S3TC_DXT1_EXT",t[t.COMPRESSED_RGBA_S3TC_DXT1_EXT=33777]="COMPRESSED_RGBA_S3TC_DXT1_EXT",t[t.COMPRESSED_RGBA_S3TC_DXT3_EXT=33778]="COMPRESSED_RGBA_S3TC_DXT3_EXT",t[t.COMPRESSED_RGBA_S3TC_DXT5_EXT=33779]="COMPRESSED_RGBA_S3TC_DXT5_EXT",t[t.COMPRESSED_R11_EAC=37488]="COMPRESSED_R11_EAC",t[t.COMPRESSED_SIGNED_R11_EAC=37489]="COMPRESSED_SIGNED_R11_EAC",t[t.COMPRESSED_RG11_EAC=37490]="COMPRESSED_RG11_EAC",t[t.COMPRESSED_SIGNED_RG11_EAC=37491]="COMPRESSED_SIGNED_RG11_EAC",t[t.COMPRESSED_RGB8_ETC2=37492]="COMPRESSED_RGB8_ETC2",t[t.COMPRESSED_SRGB8_ETC2=37493]="COMPRESSED_SRGB8_ETC2",t[t.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=37494]="COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2",t[t.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=37495]="COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2",t[t.COMPRESSED_RGBA8_ETC2_EAC=37496]="COMPRESSED_RGBA8_ETC2_EAC",t[t.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=37497]="COMPRESSED_SRGB8_ALPHA8_ETC2_EAC"}(F||(F={})),function(t){t[t.FLOAT=5126]="FLOAT",t[t.FLOAT_VEC2=35664]="FLOAT_VEC2",t[t.FLOAT_VEC3=35665]="FLOAT_VEC3",t[t.FLOAT_VEC4=35666]="FLOAT_VEC4",t[t.INT=5124]="INT",t[t.INT_VEC2=35667]="INT_VEC2",t[t.INT_VEC3=35668]="INT_VEC3",t[t.INT_VEC4=35669]="INT_VEC4",t[t.BOOL=35670]="BOOL",t[t.BOOL_VEC2=35671]="BOOL_VEC2",t[t.BOOL_VEC3=35672]="BOOL_VEC3",t[t.BOOL_VEC4=35673]="BOOL_VEC4",t[t.FLOAT_MAT2=35674]="FLOAT_MAT2",t[t.FLOAT_MAT3=35675]="FLOAT_MAT3",t[t.FLOAT_MAT4=35676]="FLOAT_MAT4",t[t.SAMPLER_2D=35678]="SAMPLER_2D",t[t.SAMPLER_CUBE=35680]="SAMPLER_CUBE",t[t.UNSIGNED_INT=5125]="UNSIGNED_INT",t[t.UNSIGNED_INT_VEC2=36294]="UNSIGNED_INT_VEC2",t[t.UNSIGNED_INT_VEC3=36295]="UNSIGNED_INT_VEC3",t[t.UNSIGNED_INT_VEC4=36296]="UNSIGNED_INT_VEC4",t[t.FLOAT_MAT2x3=35685]="FLOAT_MAT2x3",t[t.FLOAT_MAT2x4=35686]="FLOAT_MAT2x4",t[t.FLOAT_MAT3x2=35687]="FLOAT_MAT3x2",t[t.FLOAT_MAT3x4=35688]="FLOAT_MAT3x4",t[t.FLOAT_MAT4x2=35689]="FLOAT_MAT4x2",t[t.FLOAT_MAT4x3=35690]="FLOAT_MAT4x3",t[t.SAMPLER_3D=35679]="SAMPLER_3D",t[t.SAMPLER_2D_SHADOW=35682]="SAMPLER_2D_SHADOW",t[t.SAMPLER_2D_ARRAY=36289]="SAMPLER_2D_ARRAY",t[t.SAMPLER_2D_ARRAY_SHADOW=36292]="SAMPLER_2D_ARRAY_SHADOW",t[t.SAMPLER_CUBE_SHADOW=36293]="SAMPLER_CUBE_SHADOW",t[t.INT_SAMPLER_2D=36298]="INT_SAMPLER_2D",t[t.INT_SAMPLER_3D=36299]="INT_SAMPLER_3D",t[t.INT_SAMPLER_CUBE=36300]="INT_SAMPLER_CUBE",t[t.INT_SAMPLER_2D_ARRAY=36303]="INT_SAMPLER_2D_ARRAY",t[t.UNSIGNED_INT_SAMPLER_2D=36306]="UNSIGNED_INT_SAMPLER_2D",t[t.UNSIGNED_INT_SAMPLER_3D=36307]="UNSIGNED_INT_SAMPLER_3D",t[t.UNSIGNED_INT_SAMPLER_CUBE=36308]="UNSIGNED_INT_SAMPLER_CUBE",t[t.UNSIGNED_INT_SAMPLER_2D_ARRAY=36311]="UNSIGNED_INT_SAMPLER_2D_ARRAY"}(B||(B={})),function(t){t[t.OBJECT_TYPE=37138]="OBJECT_TYPE",t[t.SYNC_CONDITION=37139]="SYNC_CONDITION",t[t.SYNC_STATUS=37140]="SYNC_STATUS",t[t.SYNC_FLAGS=37141]="SYNC_FLAGS"}(d||(d={})),function(t){t[t.UNSIGNALED=37144]="UNSIGNALED",t[t.SIGNALED=37145]="SIGNALED"}(h||(h={})),function(t){t[t.ALREADY_SIGNALED=37146]="ALREADY_SIGNALED",t[t.TIMEOUT_EXPIRED=37147]="TIMEOUT_EXPIRED",t[t.CONDITION_SATISFIED=37148]="CONDITION_SATISFIED",t[t.WAIT_FAILED=37149]="WAIT_FAILED"}(H||(H={})),function(t){t[t.SYNC_GPU_COMMANDS_COMPLETE=37143]="SYNC_GPU_COMMANDS_COMPLETE"}(g||(g={})),function(t){t[t.SYNC_FLUSH_COMMANDS_BIT=1]="SYNC_FLUSH_COMMANDS_BIT"}(p||(p={}))}}]);