package org.thingsboard.server.controller.waterSource.inspection;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTaskItem;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemSaveRequest;
import org.thingsboard.server.dao.circuit.CircuitTaskItemService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/circuitTaskItem")
public class CircuitTaskItemController extends BaseController {
    @Autowired
    private CircuitTaskItemService service;


    @GetMapping
    public IPage<CircuitTaskItem> findAllConditional(CircuitTaskItemPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public List<CircuitTaskItem> save(@RequestBody List<CircuitTaskItemSaveRequest> reqs) {
        return service.save(reqs);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody CircuitTaskItemSaveRequest req, @PathVariable String id) {
        return service.update(req.update(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

    @DeleteMapping
    public boolean delete(@RequestBody List<String> ids) {
        return service.deleteAll(ids);
    }

    @PostMapping("/{id}/complete")
    public boolean complete(@PathVariable String id, @RequestBody CircuitTaskItemCompleteRequest req) {
        req.setId(id);
        return service.complete(id, req);
    }
}