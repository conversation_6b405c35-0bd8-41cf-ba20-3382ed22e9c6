package org.thingsboard.server.dao.util.imodel.response.exception.chain.handlers;


import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.util.imodel.response.exception.ViewChangeException;
import org.thingsboard.server.dao.util.imodel.response.exception.chain.ExceptionHandler;
import org.thingsboard.server.dao.util.imodel.response.exception.chain.ExceptionResolvingChain;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;

public class ViewChangeExceptionHandler implements ExceptionHandler<ViewChangeException> {
    @Override
    public boolean canHandle(Throwable e) {
        return e instanceof ViewChangeException;
    }

    @Override
    public void handle(ExceptionResolvingChain chain, ReturnHelper wrap, ViewChangeException e) throws Throwable {
        String path = wrap.getModel().currentPath();
        if (path == null) {
            throw new ThingsboardException("cannot route to null path", ThingsboardErrorCode.GENERAL);
        }
        if (path.startsWith("redirect:")) {
            wrap.getResponse().sendRedirect(path.substring(9));
        }
        wrap.setValue(null);
    }
}
