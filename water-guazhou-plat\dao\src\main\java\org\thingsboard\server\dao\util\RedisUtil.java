package org.thingsboard.server.dao.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.DataConstants;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * redis工具
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-02-10
 */

@Component
public class RedisUtil {

    private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

    private static StringRedisTemplate stringRedisTemplate;

    public static String nextCustCode(DataConstants.REDIS_KEY custCode, String prefix) {
        String data = new SimpleDateFormat("yyMMdd").format(new Date());
        String suffix = stringRedisTemplate.opsForValue().increment(custCode.getValue(), 1) + "";

        // 五位数后缀
        while (suffix.length() < 4) {
            suffix = "0" + suffix;
        }

        // 拼接日期
        return prefix + data + suffix;
    }

    @Autowired
    private void setStringRedisTemplate(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     *
     * @param key 类型
     * @param keyPrefix 前缀
     * @return
     */
    public static String nextId(DataConstants.REDIS_KEY key, String keyPrefix) {
        String data = dateFormat.format(new Date());
        String suffix = stringRedisTemplate.opsForValue().increment(key.getValue(), 1) + "";

        // 五位数后缀
        while (suffix.length() < 5) {
            suffix = "0" + suffix;
        }

        // 拼接日期
        return keyPrefix + data + suffix;
    }
}
