// 工程管理-总验期
import request from '@/plugins/axios';

/**
 * 获取总验收
 * @returns
 */
export function getProjectAcceptList(params: {
  page: number;
  size: number;
  projectCode?: string;
  projectName?: string;
}) {
  return request({
    url: `/api/so/projectAccept`,
    method: 'get',
    params
  });
}

/**
 * 总验收添加/编辑
 * @returns
 */
export function postProjectAccept(params: {
  id?: string;
  projectCode: string;
  acceptTime: string;
  remark?: string;
  attachments?: string;
}) {
  return request({
    url: `/api/so/projectAccept`,
    method: 'post',
    data: params
  });
}

/**
 * 导出总验收
 * @returns
 */
export function getProjectAcceptExport() {
  return request({
    url: `/api/so/projectAccept/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 获取总结算
 * @returns
 */
export function getProjectSettlementList(params: {
  page: number;
  size: number;
  projectCode?: string;
  projectName?: string;
}) {
  return request({
    url: `/api/so/projectSettlement`,
    method: 'get',
    params
  });
}

/**
 * 总结算添加/编辑
 * @returns
 */
export function postProjectSettlement(params: {
  id?: string;
  projectCode: string;
  acceptTime: string;
  cost: string;
  remark?: string;
  attachments?: string;
}) {
  return request({
    url: `/api/so/projectSettlement`,
    method: 'post',
    data: params
  });
}

/**
 * 导出总结算
 * @returns
 */
export function getProjectSettlementExport() {
  return request({
    url: `/api/so/projectSettlement/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 获取总归档
 * @returns
 */
export function getProjectArchiveList(params: {
  page: number;
  size: number;
  projectCode?: string;
  projectName?: string;
}) {
  return request({
    url: `/api/so/projectArchive`,
    method: 'get',
    params
  });
}

/**
 * 总结算添加/编辑
 * @returns
 */
export function postProjectArchive(params: {
  id?: string;
  projectCode: string;
  archiveTime: string;
  remark?: string;
  attachments?: string;
}) {
  return request({
    url: `/api/so/projectArchive`,
    method: 'post',
    data: params
  });
}

/**
 * 导出总归档
 * @returns
 */
export function getProjectArchiveExport() {
  return request({
    url: `/api/so/projectArchive/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}
