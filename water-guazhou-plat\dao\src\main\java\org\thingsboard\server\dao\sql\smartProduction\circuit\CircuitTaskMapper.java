package org.thingsboard.server.dao.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskPageRequest;

import java.util.Collections;
import java.util.List;

@Mapper
public interface CircuitTaskMapper extends BaseMapper<CircuitTask> {
    IPage<CircuitTask> findByPage(CircuitTaskPageRequest request);

    boolean update(CircuitTask entity);

    @Override
    default int insert(CircuitTask entity) {
        return saveAll(Collections.singletonList(entity));
    }

    boolean receive(@Param("id") String id, @Param("userId") String userId, @Param("status") GeneralTaskStatus status);

    boolean sendVerify(@Param("id") String id, @Param("userId") String userId, @Param("auditUserId") String auditUserId, @Param("status") GeneralTaskStatus status);

    boolean verify(@Param("id") String id, @Param("allow") boolean allow, @Param("userId") String userId);

    int saveAll(List<CircuitTask> list);

    int updateAll(List<CircuitTask> list);


    Integer totalOfUser(String userId);

    Integer totalStatusOfUser(@Param("userId") String userId, @Param("status") List<GeneralTaskStatus> status);
}
