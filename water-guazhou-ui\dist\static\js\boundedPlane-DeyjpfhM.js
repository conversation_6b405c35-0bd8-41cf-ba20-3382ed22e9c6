import"./index-r0dFAfgr.js";import{i as sn}from"./Point-WxyopZva.js";import{ag as y,aN as O,aO as u,aP as l,aQ as p,aR as P,aS as an,aT as h,aU as cn,aV as un,ab as $,aW as j,aX as V,aY as gn,aZ as bn,a_ as m,a$ as fn,ae as C,b0 as J,af as N}from"./MapView-DaoQedLH.js";import{s as L,c as s,b as ln,f as M,n as _}from"./sphere-NgXH-gLx.js";import{e as Z}from"./mat4f64-BCm7QTSd.js";import{v as pn,A as W,M as dn}from"./lineSegment-DQ0q5UHF.js";import{p as v,E as mn,A as $n,O as F,F as hn,x as In,U as Pn,w as Nn,B as k,J as Mn,Y as w,L as wn}from"./plane-BhzlJB-C.js";const E=sn.getLogger("esri.views.3d.support.geometryUtils.boundedPlane");class Tn{constructor(){this.plane=v(),this.origin=N(),this.basis1=N(),this.basis2=N()}}const vn=Tn;function I(n=rn){return{plane:v(n.plane),origin:O(n.origin),basis1:O(n.basis1),basis2:O(n.basis2)}}function An(n,o,i){const t=qn.get();return t.origin=n,t.basis1=o,t.basis2=i,t.plane=mn(0,0,0,0),x(t),t}function A(n,o=I()){return G(n.origin,n.basis1,n.basis2,o)}function xn(n,o){u(o.origin,n.origin),u(o.basis1,n.basis1),u(o.basis2,n.basis2),$n(o.plane,n.plane)}function G(n,o,i,t=I()){return u(t.origin,n),u(t.basis1,o),u(t.basis2,i),x(t),Bn(t,"fromValues()"),t}function x(n){F(n.basis2,n.basis1,n.origin,n.plane)}function H(n,o,i){n!==i&&A(n,i);const t=l(s.get(),f(n),o);return p(i.origin,i.origin,t),i.plane[3]-=o,i}function Sn(n,o,i){return Q(o,i),H(i,R(n,n.origin),i),i}function Q(n,o=I()){const i=(n[2]-n[0])/2,t=(n[3]-n[1])/2;return P(o.origin,n[0]+i,n[1]+t,0),P(o.basis1,i,0,0),P(o.basis2,0,t,0),hn(0,0,1,0,o.plane),o}function U(n,o,i){return!!In(n.plane,o,i)&&tn(n,i)}function yn(n,o,i){if(U(n,o,i))return i;const t=D(n,o,s.get());return p(i,o.origin,l(s.get(),o.direction,an(o.origin,t)/h(o.direction))),i}function D(n,o,i){const t=T.get();en(n,o,t,T.get());let e=Number.POSITIVE_INFINITY;for(const r of z){const a=q(n,r,S.get()),g=s.get();if(Pn(t,a,g)){const c=cn(s.get(),o.origin,g),b=Math.abs(un($(o.direction,c)));b<e&&(e=b,u(i,g))}}return e===Number.POSITIVE_INFINITY?K(n,o,i):i}function K(n,o,i){if(U(n,o,i))return i;const t=T.get(),e=T.get();en(n,o,t,e);let r=Number.POSITIVE_INFINITY;for(const a of z){const g=q(n,a,S.get()),c=s.get();if(Nn(t,g,c)){const b=ln(o,c);if(!k(e,c))continue;b<r&&(r=b,u(i,c))}}return B(n,o.origin)<r&&nn(n,o.origin,i),i}function nn(n,o,i){const t=Mn(n.plane,o,s.get()),e=W(X(n,n.basis1),t,-1,1,s.get()),r=W(X(n,n.basis2),t,-1,1,s.get());return j(i,p(s.get(),e,r),n.origin),i}function on(n,o,i){const{origin:t,basis1:e,basis2:r}=n,a=j(s.get(),o,t),g=M(e,a),c=M(r,a),b=M(f(n),a);return P(i,g,c,b)}function B(n,o){const i=on(n,o,s.get()),{basis1:t,basis2:e}=n,r=h(t),a=h(e),g=Math.max(Math.abs(i[0])-r,0),c=Math.max(Math.abs(i[1])-a,0),b=i[2];return g*g+c*c+b*b}function On(n,o){return Math.sqrt(B(n,o))}function Vn(n,o){let i=Number.NEGATIVE_INFINITY;for(const t of z){const e=q(n,t,S.get()),r=dn(e,o);r>i&&(i=r)}return Math.sqrt(i)}function _n(n,o){return k(n.plane,o)&&tn(n,o)}function En(n,o,i,t){return Un(n,i,t)}function R(n,o){const i=-n.plane[3];return M(f(n),o)-i}function Yn(n,o,i,t){const e=R(n,o),r=l(Rn,f(n),i-e);return p(t,o,r),t}function Fn(n,o){return V(n.basis1,o.basis1)&&V(n.basis2,o.basis2)&&V(n.origin,o.origin)}function jn(n,o,i){return n!==i&&A(n,i),gn(d,o),bn(d,d),m(i.basis1,n.basis1,d),m(i.basis2,n.basis2,d),m(w(i.plane),w(n.plane),d),m(i.origin,n.origin,o),wn(i.plane,i.plane,i.origin),i}function Ln(n,o,i,t){return n!==t&&A(n,t),fn(Y,o,i),m(t.basis1,n.basis1,Y),m(t.basis2,n.basis2,Y),x(t),t}function f(n){return w(n.plane)}function Un(n,o,i){switch(o){case _.X:u(i,n.basis1),C(i,i);break;case _.Y:u(i,n.basis2),C(i,i);break;case _.Z:u(i,f(n))}return i}function tn(n,o){const i=j(s.get(),o,n.origin),t=J(n.basis1),e=J(n.basis2),r=$(n.basis1,i),a=$(n.basis2,i);return-r-t<0&&r-t<0&&-a-e<0&&a-e<0}function X(n,o){const i=S.get();return u(i.origin,n.origin),u(i.vector,o),i}function q(n,o,i){const{basis1:t,basis2:e,origin:r}=n,a=l(s.get(),t,o.origin[0]),g=l(s.get(),e,o.origin[1]);p(i.origin,a,g),p(i.origin,i.origin,r);const c=l(s.get(),t,o.direction[0]),b=l(s.get(),e,o.direction[1]);return l(i.vector,p(c,c,b),2),i}function Bn(n,o){Math.abs($(n.basis1,n.basis2)/(h(n.basis1)*h(n.basis2)))>1e-6&&E.warn(o,"Provided basis vectors are not perpendicular"),Math.abs($(n.basis1,f(n)))>1e-6&&E.warn(o,"Basis vectors and plane normal are not perpendicular"),Math.abs(-$(f(n),n.origin)-n.plane[3])>1e-6&&E.warn(o,"Plane offset is not consistent with plane origin")}function en(n,o,i,t){const e=f(n);F(e,o.direction,o.origin,i),F(w(i),e,o.origin,t)}const rn={plane:v(),origin:y(0,0,0),basis1:y(1,0,0),basis2:y(0,1,0)},T=new L(v),S=new L(pn),Rn=N(),qn=new L(()=>I()),z=[{origin:[-1,-1],direction:[1,0]},{origin:[1,-1],direction:[0,1]},{origin:[1,1],direction:[-1,0]},{origin:[-1,1],direction:[0,-1]}],d=Z(),Y=Z();Object.freeze(Object.defineProperty({__proto__:null,BoundedPlaneClass:vn,UP:rn,altitudeAt:R,axisAt:En,closestPoint:K,closestPointOnSilhouette:D,copy:A,copyWithoutVerify:xn,create:I,distance:On,distance2:B,distanceToSilhouette:Vn,elevate:H,equals:Fn,extrusionContainsPoint:_n,fromAABoundingRect:Q,fromValues:G,intersectRay:U,intersectRayClosestSilhouette:yn,normal:f,projectPoint:nn,projectPointLocal:on,rotate:Ln,setAltitudeAt:Yn,setExtent:Sn,transform:jn,updateUnboundedPlane:x,wrap:An},Symbol.toStringTag,{value:"Module"}));export{Q as $,x as J,I as W,A as Z,On as a};
