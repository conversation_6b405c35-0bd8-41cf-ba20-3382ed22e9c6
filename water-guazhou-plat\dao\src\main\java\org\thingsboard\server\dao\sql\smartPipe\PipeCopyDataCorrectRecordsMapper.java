package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.PartitionSupplyCorrectRecordsDTO;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataCorrectRecords;

/**
 * 用水量修正
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-10
 */
@Mapper
public interface PipeCopyDataCorrectRecordsMapper extends BaseMapper<PipeCopyDataCorrectRecords> {

    IPage<PipeCopyDataCorrectRecords> getCopyCorrectRecords(IPage<PipeCopyDataCorrectRecords> custPage, @Param("param") PartitionCustRequest partitionCustRequest);

    IPage<PartitionSupplyCorrectRecordsDTO> getSupplyTotalRecords(IPage<PartitionSupplyCorrectRecordsDTO> custPage, @Param("param") PartitionCustRequest partitionCustRequest);
}
