import{d as u,W as f,c as v,a8 as _,eY as w,o as g,bA as y,g as C,n as k,p as x,av as A,i as S,ax as M,C as V}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{i as B}from"./ViewHelper-BGCZjxXH.js";const D=u({__name:"ArcView",props:{mapConfig:{}},emits:["click","loaded"],setup(p,{expose:n,emit:m}){const c=f(),a=v(),r=m,s=p,l=_(()=>{var o;return{"--map-filter":(o=s.mapConfig)==null?void 0:o.defaultFilter}}),t=document.createElement("div");t.id="viewDiv",t.className="viewDiv";const e=B({el:t,...s.mapConfig});return w("view",e),g(async()=>{var o;await c.Auth(),(o=a.value)==null||o.appendChild(t),r("loaded",e)}),y(()=>{e.map.removeAll(),e.graphics.removeAll(),!e.destroyed&&e.destroy()}),n({getView:()=>e}),(o,i)=>(C(),k("div",{id:"arcmap-wrapper",class:"map-wrapper",onClick:i[0]||(i[0]=d=>r("click",d))},[x("div",{ref_key:"refMapWrapper",ref:a,class:"mapbox",style:A(S(l))},null,4),M(o.$slots,"default",{},void 0,!0)]))}}),j=V(D,[["__scopeId","data-v-3aaeac91"]]);export{j as _};
