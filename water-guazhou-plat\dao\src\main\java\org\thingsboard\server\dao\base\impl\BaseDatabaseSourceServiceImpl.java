package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseDatabaseSourceService;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseSource;
import org.thingsboard.server.dao.sql.base.BaseDatabaseSourceMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseSourcePageRequest;

/**
 * 平台管理-多数据源Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class BaseDatabaseSourceServiceImpl implements IBaseDatabaseSourceService {

    @Autowired
    private BaseDatabaseSourceMapper baseDatabaseSourceMapper;

    /**
     * 查询平台管理-多数据源
     *
     * @param id 平台管理-多数据源主键
     * @return 平台管理-多数据源
     */
    @Override
    public BaseDatabaseSource selectBaseDatabaseSourceById(String id) {
        return baseDatabaseSourceMapper.selectBaseDatabaseSourceById(id);
    }

    /**
     * 查询平台管理-多数据源列表
     *
     * @param baseDatabaseSource 平台管理-多数据源
     * @return 平台管理-多数据源
     */
    @Override
    public IPage<BaseDatabaseSource> selectBaseDatabaseSourceList(BaseDatabaseSourcePageRequest baseDatabaseSource) {
        return baseDatabaseSourceMapper.selectBaseDatabaseSourceList(baseDatabaseSource);
    }

    /**
     * 新增平台管理-多数据源
     *
     * @param baseDatabaseSource 平台管理-多数据源
     * @return 结果
     */
    @Override
    public int insertBaseDatabaseSource(BaseDatabaseSource baseDatabaseSource) {
        baseDatabaseSource.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseDatabaseSourceMapper.insertBaseDatabaseSource(baseDatabaseSource);
    }

    /**
     * 修改平台管理-多数据源
     *
     * @param baseDatabaseSource 平台管理-多数据源
     * @return 结果
     */
    @Override
    public int updateBaseDatabaseSource(BaseDatabaseSource baseDatabaseSource) {
        return baseDatabaseSourceMapper.updateBaseDatabaseSource(baseDatabaseSource);
    }

    /**
     * 批量删除平台管理-多数据源
     *
     * @param ids 需要删除的平台管理-多数据源主键
     * @return 结果
     */
    @Override
    public int deleteBaseDatabaseSourceByIds(List<String> ids) {
        return baseDatabaseSourceMapper.deleteBaseDatabaseSourceByIds(ids);
    }

    /**
     * 删除平台管理-多数据源信息
     *
     * @param id 平台管理-多数据源主键
     * @return 结果
     */
    @Override
    public int deleteBaseDatabaseSourceById(String id) {
        return baseDatabaseSourceMapper.deleteBaseDatabaseSourceById(id);
    }
}
