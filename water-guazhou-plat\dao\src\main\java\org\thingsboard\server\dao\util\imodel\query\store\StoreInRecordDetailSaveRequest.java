package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceStorageJournal;
import org.thingsboard.server.dao.model.sql.store.StoreInRecord;
import org.thingsboard.server.dao.model.sql.store.StoreInRecordDetail;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class StoreInRecordDetailSaveRequest extends SaveRequest<StoreInRecordDetail> {

    // 入库单主表ID
    @NotNullOrEmpty(parentIgnore = true)
    private String mainId;

    // 设备编码
    @NotNullOrEmpty
    private String serialId;

    // 货架ID
    @NotNullOrEmpty
    private String shelvesId;

    // 数量
    @NotNullOrEmpty
    private Integer num;

    // 单价
    @NotNullOrEmpty
    private Double price;

    // 税率
    @NotNullOrEmpty
    private Double taxRete;

    public StoreInRecordDetail build() {
        StoreInRecordDetail entity = new StoreInRecordDetail();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    public StoreInRecordDetail update(String id) {
        StoreInRecordDetail entity = new StoreInRecordDetail();
        entity.setId(id);
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    private void commonSet(StoreInRecordDetail entity) {
        entity.setMainId(mainId);
        entity.setSerialId(serialId);
        entity.setShelvesId(shelvesId);
        entity.setNum(Double.valueOf(num));
        entity.setPrice(price);
        entity.setTaxRete(taxRete);
    }

    public DeviceStorageJournal toDeviceStorageJournal(StoreInRecord record) {
        DeviceStorageJournal entity = new DeviceStorageJournal();
        entity.setSerialId(serialId);
        entity.setSupplierId(record.getSupplierId());
        entity.setStorehouseId(record.getStorehouseId());
        entity.setShelvesId(shelvesId);
        entity.setTenantId(tenantId());
        entity.setStoreInCode(record.getCode());
        return entity;
    }
}