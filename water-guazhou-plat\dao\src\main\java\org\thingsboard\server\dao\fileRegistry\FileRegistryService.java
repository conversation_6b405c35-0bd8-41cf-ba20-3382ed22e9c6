package org.thingsboard.server.dao.fileRegistry;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.dao.model.sql.fileRegistry.FileRegistry;
import org.thingsboard.server.dao.util.imodel.query.FileRegistryPageRequest;
import org.thingsboard.server.dao.util.imodel.query.FileRegistrySaveRequest;

import java.util.List;

public interface FileRegistryService {
    /**
     * 查询所有文件
     *
     * @param request 分页请求
     * @return 分页请求结果
     */
    IPage<FileRegistry> findAllConditional(FileRegistryPageRequest request);

    /**
     * 保存文件注册信息
     *
     * @param entity 文件注册信息
     * @return 保存好的文件注册信息
     */
    FileRegistry save(FileRegistrySaveRequest entity);

    /**
     * 更新文件注册信息
     *
     * @param entity 文件注册信息
     * @return 是否更新成功
     */
    boolean update(FileRegistry entity);

    /**
     * 删除文件注册信息
     *
     * @param id 文件注册信息id
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 保存所有文件注册信息
     *
     * @param constructionFiles 文件注册信息列表
     * @return 保存好的文件注册信息
     */
    List<FileRegistry> saveAll(List<FileRegistrySaveRequest> constructionFiles);

    /**
     * 删除所有label的host中的文件注册信息
     *
     * @param label 所属系统
     * @param host  所属单元
     * @return 是否删除成功
     */
    boolean removeAll(String label, String host);

    /**
     * 覆写指定label的host下的所有文件
     *
     * @param req 文件注册信息
     * @return 保存好的文件注册信息
     */
    List<FileRegistry> replace(List<FileRegistrySaveRequest> req);

    /**
     * 文件上传
     *
     * @param id       id，传入时执行更新
     * @param host     文件宿主
     * @param label    文件标签
     * @param file     源文件
     * @param tenantId 租户id
     * @return 文件注册信息
     */
    FileRegistry upload(String id, String host, String label, MultipartFile file, String tenantId);

    /**
     * 通过id获取文件注册信息
     *
     * @param id id
     * @return 文件注册信息
     */
    FileRegistry getById(String id);

}
