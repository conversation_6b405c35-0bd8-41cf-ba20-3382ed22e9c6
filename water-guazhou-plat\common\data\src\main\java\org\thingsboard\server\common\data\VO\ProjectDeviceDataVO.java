package org.thingsboard.server.common.data.VO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/7/7 11:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProjectDeviceDataVO {

    /**
     * 企业ID
     */
    private String projectId;
    /**
     * 企业名称
     */
    private String projectName;
    /**
     * 主机数量
     */
    private Long hostNumbers;
    /**
     * 从机数量
     */
    private Long deviceNumbers;
    /**
     * 报警总数
     */
    private Long alarmNumbers;


}
