package org.thingsboard.server.dao.model.sql.notify;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_SYSTEM_NOTIFY)
@TableName(ModelConstants.TB_SYSTEM_NOTIFY)
@NoArgsConstructor
@AllArgsConstructor
public class SystemNotify {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_SYSTEM_NOTIFY_CODE)
    private String code;

    @Column(name = ModelConstants.TB_SYSTEM_NOTIFY_TYPE)
    private String type;

    @Column(name = ModelConstants.TB_SYSTEM_NOTIFY_TOPIC)
    private String topic;

    @Column(name = ModelConstants.TB_SYSTEM_NOTIFY_TOPIC_TYPE)
    private String topicType;

    @Column(name = ModelConstants.TB_SYSTEM_NOTIFY_CONTENT)
    private String content;

    @Column(name = ModelConstants.TB_SYSTEM_NOTIFY_FROM)
    private String fromUser;

    @Column(name = ModelConstants.TB_SYSTEM_NOTIFY_TO)
    private String toUser;

    @Column(name = ModelConstants.TB_SYSTEM_NOTIFY_TIME)
    private Date time;

    @Column(name = ModelConstants.TB_SYSTEM_NOTIFY_STATUS)
    private String status;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;



}
