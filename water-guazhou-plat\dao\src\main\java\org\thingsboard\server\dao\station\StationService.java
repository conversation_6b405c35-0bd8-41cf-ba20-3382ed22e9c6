package org.thingsboard.server.dao.station;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.CountObjDTO;
import org.thingsboard.server.dao.model.DTO.StationAttrDTO;
import org.thingsboard.server.dao.model.DTO.TreeNodeDTO;
import org.thingsboard.server.dao.model.VO.StationWarnSetVO;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.math.BigDecimal;
import java.util.List;

public interface StationService {
    void save(StationEntity station);

    PageData<StationEntity> list(int page, int size, JSONObject params, TenantId tenantId);

    void remove(List<String> ids);

    List<StationAttrEntity> getAttrList(String stationId, String type);

    List<StationAttrEntity> getAttrList(List<String> stationIdList, String type);

    StationEntity findById(String id);

    List<StationEntity> findByStationIdList(String stationType, List<String> stationIdList);

    List<StationAttrDTO> getAllAttrList(String stationId);

    List<String> getAttrGroupNames(String stationId);

    List<StationAttrEntity> getStationAllAttrList(String stationId);

    Object getRange(String stationId);

    StationAttrEntity findAttrById(String attributeId);

    List<StationAttrEntity> findAttrByIdList(String attributes);

    List<StationEntity> findByStationIdList(List<String> stationIdList);

    /**
     * 查询站点并且以项目-站点的树形结构返回
     *
     * @param type     站点类型
     * @param tenantId 租户ID
     * @return 数据
     */
    List<TreeNodeDTO> simpleTree(String type, TenantId tenantId);

    /**
     * 导入站点excel
     *
     * @param file     excel文件
     * @param tenantId 租户ID
     */
    void importStation(MultipartFile file, TenantId tenantId) throws ThingsboardException;

    /**
     * 统计各个类型站点的数量
     *
     * @param typeList  统计指定类型
     * @param projectId
     * @param tenantId
     * @return 数据
     */
    List<CountObjDTO> typeCount(List<String> typeList, String projectId, TenantId tenantId);

    List<StationEntity> findByTenantId(TenantId tenantId);

    BigDecimal getWaterPermit(String name, String tenantId);

    StationWarnSetVO getWarnReference(String stationIdList, String attr, Long start, Long end, Double diffValue);

    IstarResponse batchSet(JSONObject params, String tenantId);

    IstarResponse getReferenceValue(JSONObject params, String tenantId);
}
