import{A as O}from"./index-BI1vGJja.js";import{d as N,c as V,g as u,n as f,p as d,bh as w,an as g,q as S,F as E,i as _,d6 as F,aw as v,av as W,aB as G,aJ as A,h as x,G as z,j as R,cE as $,C as j,eV as q,eW as J}from"./index-r0dFAfgr.js";const K={key:0,class:"header"},U={class:"title"},Q={key:0,class:"right"},X={class:"map-content"},Y={key:0,class:"label"},Z=["title"],ee=["title"],te={class:"label"},ne=["title","onClick"],oe=N({__name:"popWindow",props:{config:{}},emits:["close"],setup(l){const i=V(l.config.values.length);return(r,k)=>{const L=$;return u(),f("div",{class:v(["amap-pop-tag",{isDark:_(R)().isDark}])},[r.config.title?(u(),f("div",K,[d("span",U,w(r.config.title),1),r.config.titleRight?(u(),f("span",Q,w(r.config.titleRight),1)):g("",!0),S(L,{class:"close-wrapper",onClick:k[0]||(k[0]=a=>{var h;return r.$emit("close",(h=r.config)==null?void 0:h.id)})},{default:E(()=>[S(_(F))]),_:1})])):g("",!0),d("div",X,[r.config.valuesLabel?(u(),f("span",Y,w(r.config.valuesLabel),1)):g("",!0),d("div",{class:v(["scada-table-wrapper",{scrollable:r.config.values.length>=4}])},[d("table",{class:"first-table scada-content",style:W({animationDuration:_(i)+"s"})},[d("tbody",null,[(u(!0),f(G,null,A(r.config.values,(a,h)=>(u(),f("tr",{key:h},[d("td",{title:a.label},w(a.label),9,Z),d("td",{class:v(a.status)},w(a.value||"-")+" "+w(a.unit),3)]))),128))])],4),r.config.values.length>=4?(u(),f("table",{key:0,class:"second-table scada-content",style:W({"animation-duration":_(i)+"s"})},[d("tbody",null,[(u(!0),f(G,null,A(r.config.values,(a,h)=>(u(),f("tr",{key:h},[d("td",{title:a.label},w(a.label),9,ee),d("td",{class:v(a.status)},w(a.value||"-")+" "+w(a.unit),3)]))),128))])],4)):g("",!0)],2),(u(!0),f(G,null,A(r.config.extraInfos,(a,h)=>{var b,m,C;return u(),f("div",{key:h,class:"extra-info-item"},[d("span",te,w(a.label),1),d("span",{class:"value",title:(b=a.value)==null?void 0:b.toString(),style:W(typeof((m=a.data)==null?void 0:m.style)=="function"?a.data.style(a):(C=a.data)==null?void 0:C.style),onClick:()=>{var M;return((M=a.data)==null?void 0:M.click)&&a.data.click(a)}},[a.icon?(u(),x(L,{key:0},{default:E(()=>[d("i",{class:v(a.icon)},null,2)]),_:2},1024)):g("",!0),z(" "+w(a.value),1)],12,ne)])}),128))])],2)}}}),ae=j(oe,[["__scopeId","data-v-62c232bf"]]),fe=()=>{let l=null,e=null,i=null;const r=[],k=async(n,t)=>{e=await O.load({key:"312ea3a03da6d14c60ea71789d1848ae",version:"1.4.15",plugins:["AMap.Autocomplete","AMap.PlaceSearch","AMap.Scale","AMap.MapType","AMap.CircleEditor","AMap.Geocoder","AMap.DistrictSearch"],AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}});const o=[];if(t.mapLayer.forEach(s=>{switch(s){case"标准图层":o.push(new e.TileLayer);break;case"卫星图层":o.push(new e.TileLayer.Satellite);break;case"路网图层":o.push(new e.TileLayer.RoadNet);break;case"实时交通图层":o.push(new e.TileLayer.Traffic);break;case"楼块图层":o.push(new e.Buildings);break;default:o.push(new e.TileLayer);break}}),l=new e.Map(n,{zoom:11,layers:[new e.TileLayer.Satellite],center:t.center||[116.397428,39.90923]}),t.events)for(const s in t.events)l.on(s,()=>{t.events[s](l)});return t.search&&B(),l},L=(n,t,o)=>{if(e&&n.length===2&&n[0]&&n[1]){const s=new e.Marker({position:new e.LngLat(...n),...t||{}});return typeof o=="function"&&s.on("click",()=>o(s)),l.add(s),r.push(s),s}},a=(n,t,o)=>{const s=[];n.forEach((p,y)=>{t&&t.contentHandle&&(t.content=t.contentHandle(p,y),console.log(t.content),delete t.contentHandle);const I=new e.Marker({position:new e.LngLat(...p),...t||{}});typeof o=="function"&&I.on("click",()=>o(I)),s.push(I)}),console.log(s);const c=new e.OverlayGroup(s);return l.add(c),c},h=(n,t)=>{const o=n.map(c=>new e.LngLat(...c)),s=new e.Polyline({path:o,...t||{}});return l.add(s),s},b=(n,t)=>{const o=[];n.forEach(c=>{const p=c.map(y=>new e.LngLat(...y));o.push(new e.Polyline({path:p,...t||{}}))});const s=new e.OverlayGroup(o);return l.add(s),s},m=(n,t)=>{const o=new e.InfoWindow({anchor:"top",...t||{},offset:new e.Pixel(5,-40)});return o.open(l,n),o},C=(n,t)=>{const o=[];return n.forEach((c,p)=>{t&&t.contentHandle&&(t.content=t.contentHandle(c,p),console.log(t.content),delete t.contentHandle);const y=new e.InfoWindow({isCustom:!0,anchor:"top",...t||{},offset:new e.Pixel(5,-40)});y.open(l,c),o.push(y)}),new e.OverlayGroup(o)},M=(n,t)=>{const o=[];return n.forEach(c=>{const p=new e.InfoWindow({isCustom:!0,anchor:"bottom-center",content:c.content,...t||{},offset:new e.Pixel(5,-40)});p.open(l,c.point),o.push(p)}),new e.OverlayGroup(o)},P=n=>{const t=document.createElement("div"),o=S(ae,n);return o.props&&(o.props.onClose=H),o.appContext=q._context,J(o,t),t.firstElementChild},T=n=>{n.length===0&&l.setCenter(n)},B=()=>{if(!window.SITE_CONFIG.GIS_CONFIG.gisShowBoundary)return;const n=new e.DistrictSearch({level:"district",extensions:"all"}),t=[new e.LngLat(-360,90,!0),new e.LngLat(-360,-90,!0),new e.LngLat(360,-90,!0),new e.LngLat(360,90,!0)];n.search("北碚",(o,s)=>{const c=s.districtList[0].boundaries;if(c){const p=[t,...c],y=new e.Polygon({pathL:p,strokeColor:"#00ffff",strokeWeight:1,fillColor:"#353535",fillOpacity:.2});y.setPath(p),l.add(y)}})},D=n=>{const t=P({config:n}),o=new e.InfoWindow({content:t,isCustom:!0,anchor:"bottom-center",offset:new e.Pixel(5,-40)});return o.open(l,n.point),o},H=()=>{l==null||l.clearInfoWindow()};return{map:l,setMarker:L,setCenter:T,getCenter:()=>l.getCenter(),initAMap:k,heatmap:i,initHeatMap:n=>{console.log(e==null?void 0:e.Heatmap),l.plugin(["AMap.Heatmap"],()=>{i=new e.Heatmap(l,{radius:25,opacity:[0,.8]}),i.setDataSet({data:n||[],max:100})})},resetHeatMap:n=>{if(!i){console.log("热力图实例未初始化");return}i.setDataSet({data:n||[],max:100})},toggleHeatMap:n=>{if(!i){console.log("热力图实例未初始化");return}n?typeof(i==null?void 0:i.show)=="function"&&i.show():i.visible?i.hide():i.show()},AMap:e,setMarkers:a,setPolyline:h,setPolylines:b,setInfoWindow:m,setInfoWindows:C,clearInfoWindow:H,setMultiInfoWindows:M,setListInfoWindow:D,clear:()=>{r.map(n=>{n.setMap(null),n=null}),r.length=0},getGeocoder:(n,t)=>{const o=new e.Geocoder({city:"全国"}),s=l.getCenter();n==="1"&&o.getAddress([s.lng,s.lat],(c,p)=>{c==="complete"&&p.info==="OK"&&t(p.regeocode.formattedAddress)})}}};export{fe as u};
