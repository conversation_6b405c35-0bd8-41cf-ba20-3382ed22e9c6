import request from '@/plugins/axios'
import { formatDate } from '@/utils/DateFormatter'

/**
 * 获取用水分析列表
 * @param params 查询参数
 * @returns 
 */
export function getWaterAnalysisList(params: any) {
  // 处理日期格式
  const queryParams = { ...params }
  if (queryParams.startDate) {
    queryParams.startDate = formatDate(queryParams.startDate, 'YYYY-MM-DD')
  }
  if (queryParams.endDate) {
    queryParams.endDate = formatDate(queryParams.endDate, 'YYYY-MM-DD')
  }
  
  return request({
    url: '/api/leakDetection/waterAnalysis/list',
    method: 'get',
    params: queryParams
  })
}

/**
 * 获取异常详情数据
 * @param params 查询参数
 * @returns 
 */
export function getAbnormalDetail(params: {
  partitionId: string;
  type: 'flow' | 'meter';
  page: number;
  pageSize: number;
}) {
  return request({
    url: '/api/leakDetection/waterAnalysis/abnormalDetail',
    method: 'get',
    params
  })
} 