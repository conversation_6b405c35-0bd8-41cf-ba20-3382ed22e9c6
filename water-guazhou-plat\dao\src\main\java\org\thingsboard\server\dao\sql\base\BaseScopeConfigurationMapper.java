package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseScopeConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseScopeConfigurationPageRequest;

import java.util.List;

/**
 * 公共管理平台-范围设置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Mapper
public interface BaseScopeConfigurationMapper extends BaseMapper<BaseScopeConfiguration> {
    /**
     * 查询公共管理平台-范围设置
     *
     * @param id 公共管理平台-范围设置主键
     * @return 公共管理平台-范围设置
     */
    public BaseScopeConfiguration selectBaseScopeConfigurationById(String id);

    /**
     * 查询公共管理平台-范围设置列表
     *
     * @param baseScopeConfiguration 公共管理平台-范围设置
     * @return 公共管理平台-范围设置集合
     */
    public IPage<BaseScopeConfiguration> selectBaseScopeConfigurationList(BaseScopeConfigurationPageRequest baseScopeConfiguration);

    /**
     * 新增公共管理平台-范围设置
     *
     * @param baseScopeConfiguration 公共管理平台-范围设置
     * @return 结果
     */
    public int insertBaseScopeConfiguration(BaseScopeConfiguration baseScopeConfiguration);

    /**
     * 修改公共管理平台-范围设置
     *
     * @param baseScopeConfiguration 公共管理平台-范围设置
     * @return 结果
     */
    public int updateBaseScopeConfiguration(BaseScopeConfiguration baseScopeConfiguration);

    /**
     * 删除公共管理平台-范围设置
     *
     * @param id 公共管理平台-范围设置主键
     * @return 结果
     */
    public int deleteBaseScopeConfigurationById(String id);

    /**
     * 批量删除公共管理平台-范围设置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseScopeConfigurationByIds(@Param("array") List<String> ids);
}
