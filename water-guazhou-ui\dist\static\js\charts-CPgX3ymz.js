import{a9 as l}from"./index-r0dFAfgr.js";function n(e,o){return{tooltip:{trigger:"item"},legend:{top:"center",orient:"vertical",left:"right"},series:[{name:o,type:"pie",center:["30%","50%"],radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:5,borderWidth:1},label:{show:!1,position:"center"},emphasis:{label:{show:!1}},labelLine:{show:!1},data:l(e,"children",{name:"level",value:"count"})}]}}function c(e,o){const t=[],r=[];return e.forEach(a=>{t.push(a.month),r.push(a.count)}),{tooltip:{trigger:"axis"},calculable:!0,xAxis:[{type:"category",boundaryGap:!1,data:t}],yAxis:{type:"value"},series:[{name:o,type:"line",data:r,lineStyle:{normal:{width:4,color:{type:"linear",colorStops:[{offset:0,color:"#57BD9F"},{offset:1,color:"#19BC8C"}],globalCoord:!1},shadowColor:"rgba(72,216,191, 0.3)",shadowBlur:6,shadowOffsetY:10}},itemStyle:{normal:{color:"#57BD9F",borderWidth:6,borderColor:"#57BD9F"}}}]}}export{c as 折,n as 饼};
