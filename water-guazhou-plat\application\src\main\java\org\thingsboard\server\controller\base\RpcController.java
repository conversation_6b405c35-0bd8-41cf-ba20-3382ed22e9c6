/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.BaseResult;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.dao.model.sql.OperateLogEntity;
import org.thingsboard.server.service.rpc.DeviceRpcService;
import org.thingsboard.server.service.security.AccessValidator;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by ashvayka on 22.03.18.
 */
@RestController
@RequestMapping(TbUrlConstants.RPC_URL_PREFIX)
@Slf4j
public class RpcController extends BaseController {

    public static final int DEFAULT_TIMEOUT = 10000;
    protected final ObjectMapper jsonMapper = new ObjectMapper();

    @Autowired
    private DeviceRpcService deviceRpcService;

    @Autowired
    private AccessValidator accessValidator;

    private ExecutorService executor;

    @PostConstruct
    public void initExecutor() {
        executor = Executors.newSingleThreadExecutor();
    }

    @PreDestroy
    public void shutdownExecutor() {
        if (executor != null) {
            executor.shutdownNow();
        }
    }

    @RequestMapping(value = "/oneway/{deviceId}", method = RequestMethod.POST)
    @ResponseBody
    public DeferredResult<ResponseEntity> handleOneWayDeviceRPCRequest(@PathVariable("deviceId") String deviceIdStr, @RequestBody String requestBody) throws ThingsboardException {
        return deviceRpcService.handleDeviceRPCRequest(true, new DeviceId(UUID.fromString(deviceIdStr)), requestBody, new DeviceId(UUID.fromString(deviceIdStr)), null);
    }

    @RequestMapping(value = "/twoway/{deviceId}", method = RequestMethod.POST)
    @ResponseBody
    public DeferredResult<ResponseEntity> handleTwoWayDeviceRPCRequest(@PathVariable("deviceId") String deviceIdStr, @RequestBody String requestBody) throws ThingsboardException {
        return deviceRpcService.handleDeviceRPCRequest(false, new DeviceId(UUID.fromString(deviceIdStr)), requestBody, new DeviceId(UUID.fromString(deviceIdStr)), null);
    }

    @RequestMapping(value = "/twoway/gateway/{deviceId}", method = RequestMethod.POST)
    @ResponseBody
    public DeferredResult<ResponseEntity> handleTwoWayGateWayRPCRequest(@PathVariable("deviceId") String deviceIdStr, @RequestBody String requestBody) throws ThingsboardException {
        //根据传入的设备ID来转换为网关ID
        Device device = deviceService.findDeviceById(new DeviceId(UUID.fromString(deviceIdStr)));
        if (device.getGateWayId() == null) {
            throw new ThingsboardException("回调失败，请检查提交的参数信息", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        DeviceId gatewayId = device.getGateWayId();
        return deviceRpcService.handleDeviceRPCRequest(false, device.getId(), requestBody, gatewayId, null);
    }

    @RequestMapping(value = "/oneway/gateway/{deviceId}", method = RequestMethod.POST)
    @ResponseBody
    public DeferredResult<ResponseEntity> handleOneWayGateWayRPCRequest(@PathVariable("deviceId") String deviceIdStr, @RequestBody String requestBody) throws ThingsboardException {
        //根据传入的设备ID来转换为网关ID
        Device device = deviceService.findDeviceById(new DeviceId(UUID.fromString(deviceIdStr)));
        if (device.getGateWayId() == null) {
            throw new ThingsboardException("回调失败，请检查提交的参数信息", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        DeviceId gatewayId = device.getGateWayId();
        return deviceRpcService.handleDeviceRPCRequest(true, device.getId(), requestBody, gatewayId, null);
    }


    @RequestMapping(value = "/callback", method = RequestMethod.POST)
    @ResponseBody
    public void rpcCallback(@RequestBody String requestBody) throws ThingsboardException {
        JSONObject params = JSON.parseObject(requestBody);
        if (!params.containsKey("requestId")) {
            throw new ThingsboardException("回调失败，请检查提交的参数信息", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        log.info("收到回复：" + System.currentTimeMillis());
        if (DataConstants.RPC_RESULT_MAP.containsKey(params.getString("requestId"))) {
            DataConstants.RPC_RESULT_MAP.put(params.getString("requestId"), Optional.of(params));
        } else {
            log.warn("该请求已经超时或该请求不存在, requestId = [{}]", params.getString("requestId"));
        }
    }

    /**
     * 下发到繁易盒子
     *
     * @param deviceIdStr
     * @param requestBody
     * @return
     * @throws ThingsboardException
     */
    @RequestMapping(value = "/fy/{deviceId}", method = RequestMethod.POST)
    @ResponseBody
    public boolean handleFanyiRPCRequest(@PathVariable("deviceId") String deviceIdStr, @RequestBody String requestBody) throws ThingsboardException {
        //根据传入的设备ID来转换为网关ID
        Device device = deviceService.findDeviceById(new DeviceId(UUIDConverter.fromString(deviceIdStr)));
        return deviceRpcService.sendRpcToDevice(device, requestBody).isResult();
    }

    /**
     * 下发到繁易盒子
     *
     * @param deviceIdStr
     * @param requestBody
     * @return
     * @throws ThingsboardException
     */
    @RequestMapping(value = "/fy/{deviceId}/{phone}/{captcha}", method = RequestMethod.POST)
    @ResponseBody
    public Object handleFanyiRpcRequestWithCaptcha(@PathVariable("deviceId") String deviceIdStr, @PathVariable("phone") String phone, @PathVariable("captcha") String captcha, @RequestBody String requestBody) throws ThingsboardException {
        //根据传入的设备ID来转换为网关ID
        OperateLogEntity operateLogEntity = OperateLogEntity.builder()
                .captcha(captcha)
                .createTime(System.currentTimeMillis())
                .phone(phone)
                .tenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()))
                .invalid(DataConstants.IS_DELETE_YES)
                .body(requestBody)
                .type(DataConstants.ORERATE_TYPE_OPERATE)
                .userId(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId())).build();
        //check是否有未关闭的操作模式记录
        OperateLogEntity oldLog = operateLogService.findByPhoneAndInvalidAndCaptcha(phone, DataConstants.ROOT_PROJECT_PARENT_ID, captcha);
        if (oldLog == null || (System.currentTimeMillis() - oldLog.getCreateTime() > 1000 * 60 * 30)) {
            operateLogEntity.setResult(DataConstants.SEND_MSG_FAILED);
            operateLogEntity.setResultMsg("验证码不存在或验证码已失效！");
            operateLogService.saveLog(operateLogEntity);
            return BaseResult.builder().msg(operateLogEntity.getResultMsg()).isResult(false).build();
        }
        Device device = deviceService.findDeviceById(new DeviceId(UUIDConverter.fromString(deviceIdStr)));
        if(device.getHardwareId()!=null&&!"".equalsIgnoreCase(device.getHardwareId())){
            BaseResult baseResult = deviceRpcService.sendRpcToDevice(device, requestBody);
            operateLogEntity.setResult(baseResult.isResult() ? DataConstants.SEND_MSG_SUCCESS : DataConstants.SEND_MSG_FAILED);
            operateLogEntity.setResultMsg(baseResult.getMsg());
            operateLogService.saveLog(operateLogEntity);
            return baseResult;
        }else {
            DeviceId gatewayId = device.getGateWayId();
            return deviceRpcService.sendRPCToModbus( device.getId(), convertRpcBody(requestBody), gatewayId, getCurrentUser());
        }


    }


    private String convertRpcBody(String body){
        JSONObject jsonObject = JSONObject.parseObject(body);
        JSONObject data = jsonObject.getJSONObject("Data");
        JSONObject result =new JSONObject();
        result.put("method","writeToModbus");
        JSONObject params = new JSONObject();
        params.put("tag",data.getString("name"));
        params.put("value",data.getString("value"));
        result.put("params",params);
        result.put("timeout", 20000);
        return result.toJSONString();
    }

}
