import{m as a}from"./index-r0dFAfgr.js";function i(t){return a({url:"/api/stationCircuitScheme/list",method:"get",params:t})}function r(t){return a({url:"/api/stationCircuitScheme",method:"post",data:t})}function n(t){return a({url:"/api/stationCircuitScheme/remove",method:"delete",data:t})}function s(t){return a({url:"/api/stationCircuitTask/list",method:"get",params:t})}function o(t){return a({url:"/api/stationCircuitTask/remove",method:"delete",data:t||[]})}function u(t){return a({url:"/api/stationCircuitTask",method:"post",data:t})}function c(t){return a({url:"/api/stationCircuitTask/complete",method:"post",data:t})}function d(t){return a({url:"/api/stationCircuitTask/audit",method:"post",data:t})}function l(t){return a({url:"/api/stationCircuitTask/receive",method:"post",data:t})}function m(t){return a({url:"/api/stationCircuitPlan/list",method:"get",params:t})}function p(t){return a({url:"/api/stationCircuitPlan/remove",method:"delete",data:t||[]})}function h(t){return a({url:"/api/stationCircuitPlan",method:"post",data:t})}export{r as a,i as b,n as c,p as d,c as e,u as f,m as g,d as h,s as i,o as j,l as r,h as s};
