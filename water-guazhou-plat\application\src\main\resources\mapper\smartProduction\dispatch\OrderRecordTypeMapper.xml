<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.dispatch.OrderRecordTypeMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           dept_id_list,
                           department_resolve_multi_id(dept_id_list) dept_name_list,
                           tenant_id<!--@sql from sp_order_record_type -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordType">
        <result column="id" property="id"/>
        <result column="dept_id_list" property="deptIdList"/>
        <result column="dept_name_list" property="deptNameList"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <update id="update">
        update sp_order_record_type
        <set>
            <if test="deptIdList != null">
                dept_id_list = #{deptIdList},
            </if>
            <if test="name != null and name != ''">
                name = #{name}
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_order_record_type
        <where>
            <if test="name != null and name != ''">
                name like '%'|| #{name} ||'%'
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <select id="getNameById" resultType="java.lang.String">
        select name from sp_order_record_type where id = #{id}
    </select>
</mapper>