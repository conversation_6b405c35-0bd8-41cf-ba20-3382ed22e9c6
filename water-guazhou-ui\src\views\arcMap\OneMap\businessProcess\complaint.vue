<!-- gis投诉热线 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards
      v-model="cardsvalue"
      :span="8"
    ></Cards>
    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components'
import { IFormIns } from '@/components/type'
import { ring } from '../../components/components/chart'

const emit = defineEmits(['highlightMark', 'addMarks'])
const props = defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const refForm = ref<IFormIns>()

const cardsvalue = ref([
  { label: '0 件', value: '月度受理量' },
  { label: '0 件', value: '今日已办量' },
  { label: '0 件', value: '今日待办量' }
])
const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    hide: true
  },
  columns: [
    {
      label: '热线编号',
      prop: 'key1',
      width: 90
    },
    {
      label: '来电号码',
      prop: 'key2',
      width: 90
    },
    {
      label: '电话来源',
      prop: 'key3',
      width: 90
    },
    {
      label: '工单类型',
      prop: 'key4',
      width: 90
    }
  ],
  handleRowClick: row => {
    TableConfig.currentRow = row
    emit('highlightMark', props.menu, row?.id)
  }
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        type: 'underline',
        desc: '热线类型占比'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          }
        }
      ]
    },
    {
      fields: [
        {
          type: 'input',
          field: 'layer',
          append: '刷新'
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const refreshData = () => {
  //
}
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  height: calc(100% - 335px);
}
</style>
