package org.thingsboard.server.dao.sql.video;

import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@SqlDao
public interface VideoRepository extends CrudRepository<VideoEntity, String>, JpaSpecificationExecutor<VideoEntity> {

    List<VideoEntity> findByProjectIdAndNameLikeAndGroupIdLikeOrderByUpdateTimeDesc(@Param("projectId") String projectId, @Param("name") String name, @Param("groupId") String groupId);

    List<VideoEntity> findByProjectIdAndNameLikeAndGroupIdIsNullOrGroupIdIsOrderByUpdateTimeDesc(@Param("projectId") String projectId, @Param("name") String name, @Param("groupId") String groupId);


    List<VideoEntity> findByProjectIdAndVideoTypeOrderByUpdateTimeDesc(String projectId, String type);

    List<VideoEntity> findByVideoTypeOrderByUpdateTimeDesc(String type);

    List<VideoEntity> findByVideoTypeAndTenantIdOrderByUpdateTimeDesc(String type, String tenantId);

    @Query("select v from VideoEntity v where v. projectId = ?1 and (v.toOracle is null or v.toOracle <> '1')")
    List<VideoEntity> findAllByProjectId(String id);

    VideoEntity findById(String id);

    List<VideoEntity> findAllByNameLikeAndTenantIdOrderByUpdateTimeDesc(String name, String tenantId);

    List<VideoEntity> findBySerialNumber(String cameraIndexCode);

    List<VideoEntity> findAllByGroupIdIsNotNullOrGroupIdEquals(String groupId);



}
