<template>
  <div class="team_table">
    <FormTable :config="TableConfig"></FormTable>
    <DialogForm
      ref="refForm"
      class="dialogForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
    <SLDrawer ref="refDetail" :config="detailConfig">
      <detail :config="data.selected" :show="8"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import {
  postConstructionExpense,
  getConstructionContractOptions
} from '@/api/engineeringManagement/manage';
import { formatDate } from '@/utils/DateFormatter';
import detail from '../../../components/detail.vue';
import { StatusType, TypesFee, PaymentMethod } from '../../../data';
import { traverse } from '@/utils/GlobalHelper';

const refForm = ref<IDialogFormIns>();
const refDetail = ref<ISLDrawerIns>();

const props = defineProps<{
  config: {
    items: any;
  };
}>();

const emit = defineEmits(['extendedReturn']);

const TableConfig = reactive<ITable>({
  loading: false,
  indexVisible: true,
  dataList: computed(() => props.config.items) as any,
  columns: [
    // { prop: 'code', label: '资金编号' },
    { prop: 'contractName', label: '所属合同' },
    { prop: 'contractTypeName', label: '合同类别' },
    { prop: 'payeeOrganization', label: '收款单位' },
    { prop: 'contractCost', label: '合同金额(万元)' },
    { prop: 'cost', label: '支付金额(万元)' },
    { prop: 'type', label: '费用类型' },
    { prop: 'remark', label: '说明' },
    { prop: 'creatorName', label: '创建人' },
    {
      prop: 'createTime',
      label: '创建时间',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      prop: 'status',
      label: '工作状态',
      tag: true,
      tagColor: (): string =>
        StatusType.find((item) => item.value === 'COMPLETED')?.color || '',
      formatter: () =>
        StatusType.find((item) => item.value === 'COMPLETED')?.label
    }
  ],
  operationWidth: '200px',
  operations: [
    {
      isTextBtn: false,
      type: 'success',
      text: '编辑',
      perm: true,
      click: (row) => clickEdit(row)
    },
    {
      isTextBtn: false,
      text: '详情',
      perm: true,
      click: (row) => {
        data.selected = row;
        refDetail.value?.openDrawer();
      }
    }
  ],
  pagination: {
    hide: true
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '编辑签证',
  appendToBody: true,
  labelWidth: '150px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    params.pipLengthDesign = JSON.stringify(params.pipLengthDesign);
    postConstructionExpense(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
        } else {
          ElMessage.warning(text + '失败');
        }
        emit('extendedReturn', {});
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '资金编号',
          field: 'code',
          disabled: true
        },
        {
          xs: 12,
          type: 'select',
          label: '费用类型',
          field: 'type',
          rules: [{ required: true, message: '请选择费用类型' }],
          options: TypesFee
        },
        {
          xs: 12,
          type: 'select',
          label: '所属合同名称',
          field: 'contractCode',
          options: computed(() => data.contractList) as any
        },
        {
          xs: 12,
          type: 'number',
          label: '合同金额(万元)',
          field: 'contractTotalCost',
          readonly: true
        },
        {
          xs: 12,
          type: 'select',
          label: '支付方式',
          field: 'paymentType',
          options: PaymentMethod
        },
        {
          xs: 12,
          type: 'number',
          label: '金额(万元)',
          field: 'cost',
          rules: [{ required: true, message: '请输入金额' }],
          min: 0
        },
        {
          xs: 12,
          type: 'date',
          label: '报批时间',
          field: 'approvalTime',
          format: 'x',
          rules: [{ required: true, message: '请输入报批时间' }]
        },
        {
          xs: 12,
          type: 'date',
          label: '提交财务处理时间',
          field: 'submitFinanceTime',
          format: 'x',
          rules: [{ required: true, message: '请输入提交财务处理时间' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '一审审核金额(万元)',
          field: 'firstVerifyCost'
        },
        {
          xs: 12,
          type: 'input',
          label: '一审结果单位',
          field: 'firstVerifyOrganization'
        },
        {
          xs: 12,
          type: 'input',
          label: '二审审核金额(万元)',
          field: 'secondVerifyCost'
        },
        {
          xs: 12,
          type: 'input',
          label: '二审结果单位',
          field: 'secondVerifyOrganization'
        },
        {
          xs: 12,
          type: 'input',
          label: '代收款信息',
          field: 'payeeInfo'
        },
        {
          xs: 12,
          type: 'input',
          label: '收款单位',
          field: 'payeeOrganization'
        },
        {
          type: 'textarea',
          label: '说明',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  group: [],
  width: '80%',
  modalClass: 'lightColor',
  appendToBody: true,
  cancel: false
});

const data = reactive({
  selected: {},
  // 合同列表
  contractList: [],
  getConstructionContract: (val: string) => {
    getConstructionContractOptions(val).then((res) => {
      data.contractList = traverse(res.data.data.data || [], 'children', {
        label: 'name',
        value: 'code'
      });
    });
  }
});

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑费用明细';
  addOrUpdateConfig.defaultValue = {
    ...(row || {}),
    contractTotalCost: row.contractCost
  };
  data.getConstructionContract(row.constructionCode);
  refForm.value?.openDialog();
};
</script>

<style lang="scss" scoped>
.team_table {
  width: 100%;
  padding: 5px 15px;
}
</style>
