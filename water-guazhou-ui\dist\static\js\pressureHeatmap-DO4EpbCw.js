import{C as Me,c as ye,r as N,l as n,a8 as g,o as ve,bA as De,g as D,n as b,q as f,F as v,p as i,i as T,b$ as J,G as V,c0 as be,c1 as xe,bh as h,aB as q,aJ as K,av as Q,an as we,a0 as _e,c2 as Ye,J as He,aK as Pe,aL as Te,E as ke,c3 as Fe,bB as W}from"./index-r0dFAfgr.js";/* empty css                  */import{w as k}from"./Point-WxyopZva.js";import{g as x,y as Se,m as Le,cU as E,dy as Ie,H as Ce}from"./MapView-DaoQedLH.js";import Re from"./RightDrawerMap-D5PhmGFO.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as Ve}from"./ViewHelper-BGCZjxXH.js";import{G as Ee}from"./zhandian-YaGuQZe6.js";import{u as ze}from"./useStation-DJgnSZIA.js";import"./widget-BcWKanF2.js";import"./ArcView-DpMnCY82.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./DateFormatter-Bm9a68Ax.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const $e={class:"pressure-heatmap-wrapper"},je={class:"heatmap-content"},Be={class:"control-panel"},Oe={class:"time-range-selector"},Ue={class:"time-controls"},Ae={class:"display-controls"},Ge={class:"legend-panel"},Ne={class:"legend-items"},Je={class:"legend-text"},qe={class:"stats-panel"},Ke={class:"current-time"},Qe={class:"stats-grid"},We={class:"stat-item"},Xe={class:"stat-value"},Ze={class:"stat-item"},et={class:"stat-value high"},tt={class:"stat-item"},at={class:"stat-value low"},st={class:"stat-item"},rt={class:"stat-value warning"},ot={key:0,class:"time-slider-container"},it={class:"slider-wrapper"},nt={class:"time-label"},lt={class:"time-label"},mt={class:"time-markers"},ct={__name:"pressureHeatmap",setup(dt){const F=ye();let m=null,u=null;ze(),N({defaultParams:{stationIds:[],alarmStatus:"",time:n().format("YYYY-MM-DD"),reportType:"day",timeRange:[n().subtract(7,"days").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")]}});const t=N({stationList:[],selectedStations:[],isPlaying:!1,currentTimeIndex:0,playbackSpeed:1e3,playbackTimer:null,timeSliderData:[],currentDisplayTime:n().format("YYYY-MM-DD HH:mm:ss"),timeRange:{start:n().subtract(1,"days").format("MM-DD HH:mm"),end:n().format("MM-DD HH:mm")},currentStats:{avgPressure:"0.25",maxPressure:"0.35",minPressure:"0.15",abnormalCount:3},pressureLegend:[{value:.1,color:"#0066CC",label:"< 0.1"},{value:.2,color:"#00CCFF",label:"0.1-0.2"},{value:.3,color:"#00FF99",label:"0.2-0.3"},{value:.4,color:"#FFFF00",label:"0.3-0.4"},{value:.5,color:"#FF6600",label:"0.4-0.5"},{value:.6,color:"#FF0000",label:"> 0.5"}],timeMarkers:[],pressurePoints:[],windows:[],mapGraphics:[],showHeatmap:!0,showPoints:!0,fixedMonitoringPoints:[],selectedTimeRange:[n().subtract(7,"days").format("YYYY-MM-DD HH:mm:ss"),n().format("YYYY-MM-DD HH:mm:ss")],timeRangeShortcuts:[{text:"最近一周",value:[n().subtract(7,"days"),n()]},{text:"最近一个月",value:[n().subtract(30,"days"),n()]},{text:"最近三个月",value:[n().subtract(90,"days"),n()]},{text:"最近一年",value:[n().subtract(365,"days"),n()]}]}),S=g(()=>t.isPlaying),z=g({get:()=>t.currentTimeIndex,set:a=>{t.timeSliderData.length>0&&(t.currentTimeIndex=Math.max(0,Math.min(a,t.timeSliderData.length-1)))}}),$=g(()=>t.timeSliderData),X=g(()=>t.currentDisplayTime),j=g(()=>t.timeRange),w=g(()=>t.currentStats),Z=g(()=>t.pressureLegend),ee=g(()=>t.timeMarkers),B=g({get:()=>t.playbackSpeed,set:a=>t.playbackSpeed=a}),O=g({get:()=>t.selectedTimeRange,set:a=>t.selectedTimeRange=a}),te=g(()=>[{text:"最近一周",value:[n().subtract(7,"days").format("YYYY-MM-DD HH:mm:ss"),n().format("YYYY-MM-DD HH:mm:ss")]},{text:"最近一个月",value:[n().subtract(30,"days").format("YYYY-MM-DD HH:mm:ss"),n().format("YYYY-MM-DD HH:mm:ss")]},{text:"最近三个月",value:[n().subtract(90,"days").format("YYYY-MM-DD HH:mm:ss"),n().format("YYYY-MM-DD HH:mm:ss")]},{text:"最近一年",value:[n().subtract(365,"days").format("YYYY-MM-DD HH:mm:ss"),n().format("YYYY-MM-DD HH:mm:ss")]}]),_=async()=>{if(!m||!t.timeSliderData.length)return;const a=t.timeSliderData[t.currentTimeIndex];t.currentDisplayTime=a.time;const e=L(),o=e.map(s=>parseFloat(s.pressure));t.currentStats.avgPressure=(o.reduce((s,c)=>s+c,0)/o.length).toFixed(2),t.currentStats.maxPressure=Math.max(...o).toFixed(2),t.currentStats.minPressure=Math.min(...o).toFixed(2),t.currentStats.abnormalCount=o.filter(s=>s>.4||s<.15).length,await U(e),A(e)},Y=()=>{try{if(!m||!m.spatialReference){console.warn("地图视图未准备好，延迟创建热力图图层");return}const a=[new E({name:"ObjectID",alias:"ObjectID",type:"oid"}),new E({name:"pressure",alias:"压力值",type:"double"}),new E({name:"name",alias:"监测点名称",type:"string"})],e=new Ie({field:"pressure",colorStops:[{color:[0,102,204,0],ratio:0},{color:[0,204,255,.8],ratio:.2},{color:[0,255,153,.8],ratio:.4},{color:[255,255,0,.8],ratio:.6},{color:[255,102,0,.8],ratio:.8},{color:[255,0,0,1],ratio:1}],radius:25,maxDensity:.008,minDensity:0,blurRadius:15});u=new Ce({source:[],fields:a,objectIdField:"ObjectID",geometryType:"point",spatialReference:m.spatialReference,renderer:e,popupEnabled:!1,title:"管网压力热力图"}),m.map.add(u),console.log("热力图图层创建成功")}catch(a){console.error("创建热力图图层失败:",a)}},U=async a=>{if(!t.showHeatmap)return;if(!u&&(console.warn("热力图图层不存在，重新创建"),Y(),await new Promise(r=>setTimeout(r,500)),!u)){console.error("热力图图层创建失败，跳过更新");return}const e=[];let o=1;a.forEach(r=>{const p=r.lng,l=r.lat,M=Math.max(15,Math.min(35,Math.floor(r.pressure*50)));for(let y=0;y<M;y++){const P=Math.max(.008,.02-r.pressure*.015),C=(Math.random()-.5)*P,R=(Math.random()-.5)*P,fe=t.currentTimeIndex,ge=Math.sin((fe+r.id*2)/12)*.05,he=Math.max(.05,Math.min(.8,r.pressure+ge));e.push(new x({geometry:new k({longitude:p+C,latitude:l+R,spatialReference:m.spatialReference}),attributes:{ObjectID:o++,pressure:he,name:`${r.name}_热力点_${y+1}`}}))}if(r.pressure>.35)for(let y=0;y<10;y++){const P=(Math.random()-.5)*.005,C=(Math.random()-.5)*.005,R=r.pressure+Math.random()*.1;e.push(new x({geometry:new k({longitude:p+P,latitude:l+C,spatialReference:m.spatialReference}),attributes:{ObjectID:o++,pressure:Math.min(.8,R),name:`${r.name}_高压热力_${y+1}`}}))}});const s=(95.739+95.83)/2,c=(40.491+40.539)/2,d=t.currentTimeIndex;for(let r=0;r<20;r++){const p=(Math.random()-.5)*.08,l=(Math.random()-.5)*.04,M=.18+Math.sin((d+r)/15)*.08;e.push(new x({geometry:new k({longitude:s+p,latitude:c+l,spatialReference:m.spatialReference}),attributes:{ObjectID:o++,pressure:Math.max(.05,M),name:`背景热力_${r+1}`}}))}try{await u.applyEdits({deleteFeatures:u.source.items.toArray(),addFeatures:e})}catch(r){console.warn("热力图数据更新失败:",r),m.map.findLayerById(u.id)&&m.map.remove(u),Y(),setTimeout(async()=>{try{await u.applyEdits({addFeatures:e})}catch(p){console.warn("重试热力图数据添加失败:",p)}},100)}},A=a=>{m&&(m.graphics.removeAll(),t.showPoints&&a.forEach((e,o)=>{const s=new k({longitude:e.lng,latitude:e.lat,spatialReference:m.spatialReference}),c=ie(e.pressure),d=Math.max(e.pressure*40,12),r=new x({geometry:s,symbol:new Se({style:"circle",color:c,size:d,outline:{color:[255,255,255],width:2}}),attributes:{name:e.name,pressure:e.pressure,status:e.pressure>.4?"偏高":e.pressure<.15?"偏低":"正常"}}),p=new x({geometry:s,symbol:new Le({text:`${e.name}
${e.pressure}MPa`,color:"#333",font:{size:8,family:"sans-serif"},yoffset:-20,haloColor:"white",haloSize:1})});m.graphics.addMany([r,p])}))},ae=async a=>{var o;if(!a)return;const e=[{label:"监测点名称",value:a.name},{label:"压力值",value:`${a.pressure}MPa`},{label:"状态",value:a.status},{label:"更新时间",value:t.currentDisplayTime}];t.windows.length=0,t.windows.push({visible:!1,x:0,y:0,offsetY:-30,title:a.name,attributes:{values:e,id:a.name}}),await W(),(o=F.value)==null||o.openPop(a.name)},se=async a=>{if(u&&(u.visible=a,a&&t.timeSliderData.length>0)){const e=L();await U(e)}},re=a=>{if(m)if(a){const e=L();A(e)}else m.graphics.removeAll()},oe=()=>{m&&(G(),m.ready?Y():m.when(()=>{Y()}).catch(a=>{console.error("地图初始化失败:",a)}),I(),Ve(m,a=>{var o,s;const e=(o=a.results)==null?void 0:o[0];if(e&&e.type==="graphic"){const c=(s=e.graphic)==null?void 0:s.attributes;ae(c)}}))},G=()=>{if(t.fixedMonitoringPoints.length>0)return;const a={minLng:95.739,maxLng:95.83,minLat:40.491,maxLat:40.539};for(let e=0;e<30;e++){const o=a.minLng+Math.random()*(a.maxLng-a.minLng),s=a.minLat+Math.random()*(a.maxLat-a.minLat);t.fixedMonitoringPoints.push({id:e+1,name:`监测点${e+1}`,lng:parseFloat(o.toFixed(6)),lat:parseFloat(s.toFixed(6))})}},L=()=>{const a=[];return t.fixedMonitoringPoints.length===0&&G(),t.fixedMonitoringPoints.forEach((e,o)=>{const s=t.currentTimeIndex,c=.25+Math.sin((s+o*3)/10)*.15,d=(Math.random()-.5)*.1,r=Math.max(.05,Math.min(.6,c+d));a.push({id:e.id,name:e.name,lng:e.lng,lat:e.lat,pressure:parseFloat(r.toFixed(2))})}),a},ie=a=>a<.1?"#0066CC":a<.2?"#00CCFF":a<.3?"#00FF99":a<.4?"#FFFF00":a<.5?"#FF6600":"#FF0000",I=()=>{try{const a=[],e=[],o=n(t.selectedTimeRange[0]),s=n(t.selectedTimeRange[1]),c=s.diff(o,"hours");let d=1;c>168?d=6:c>72?d=3:c>24&&(d=2);const r=Math.min(Math.ceil(c/d),48);for(let p=0;p<r;p++){const l=o.clone().add(p*d,"hours");if(l.isAfter(s))break;a.push({time:l.format("YYYY-MM-DD HH:mm:ss"),timeLabel:l.format("MM-DD HH:mm")});const M=Math.max(1,Math.floor(r/6));p%M===0&&e.push({position:p/(r-1)*100,label:l.format("MM-DD HH:mm")})}a.length>0&&!n(a[a.length-1].time).isSame(s,"hour")&&(a.push({time:s.format("YYYY-MM-DD HH:mm:ss"),timeLabel:s.format("MM-DD HH:mm")}),e.push({position:100,label:s.format("MM-DD HH:mm")})),t.timeRange={start:o.format("MM-DD HH:mm"),end:s.format("MM-DD HH:mm")},a.length>0&&(t.timeSliderData=a,t.timeMarkers=e,t.currentTimeIndex=a.length-1,H(),W(async()=>{await _()}))}catch(a){console.error("初始化时间轴失败:",a),t.timeSliderData=[],t.timeMarkers=[],t.currentTimeIndex=0}},ne=()=>{t.isPlaying=!t.isPlaying,t.isPlaying?le():H()},le=()=>{t.playbackTimer&&clearInterval(t.playbackTimer),t.playbackTimer=setInterval(()=>{t.currentTimeIndex<t.timeSliderData.length-1?(t.currentTimeIndex++,_()):H()},t.playbackSpeed)},H=()=>{t.isPlaying=!1,t.playbackTimer&&(clearInterval(t.playbackTimer),t.playbackTimer=null)},me=async()=>{H(),t.currentTimeIndex=0,await _()},ce=async a=>{t.timeSliderData.length>0&&(t.currentTimeIndex=Math.max(0,Math.min(a,t.timeSliderData.length-1)),await _())},de=async a=>{var e,o;m=a,(e=F.value)==null||e.toggleCustomDetail(!1);try{const c=(await Ee({page:1,size:999,type:"压力监测站",projectId:(o=_e().selectedProject)==null?void 0:o.value})).data.data||[];t.stationList=c.map(d=>({label:d.name,value:d.id}))}catch(s){console.error("获取监测站列表失败:",s)}setTimeout(()=>{oe()},1e3)};ve(()=>{window.addEventListener("resize",()=>{})}),De(()=>{var a;t.playbackTimer&&clearInterval(t.playbackTimer),u&&m&&((a=m.map)==null||a.remove(u),u=null),window.removeEventListener("resize",()=>{})});const pe=a=>{!a||a.length!==2||(t.selectedTimeRange=a,I())},ue=async()=>{I()};return(a,e)=>{const o=Ye,s=He,c=Pe,d=Te,r=ke,p=Fe;return D(),b("div",$e,[f(Re,{ref_key:"refMap",ref:F,title:"管网压力分布热力图",windows:t.windows,"hide-detail-close":!0,"hide-layer-list":!0,"right-drawer-width":450,enableCluster:!1,onMapLoaded:de},{"detail-header":v(()=>e[12]||(e[12]=[])),"detail-default":v(()=>e[13]||(e[13]=[])),default:v(()=>[i("div",je,[i("div",Be,[i("div",Oe,[f(o,{modelValue:O.value,"onUpdate:modelValue":e[0]||(e[0]=l=>O.value=l),type:"datetimerange",shortcuts:te.value,"range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm:ss",onChange:pe,size:"small",style:{width:"300px"}},null,8,["modelValue","shortcuts"]),f(s,{icon:T(J),onClick:ue,size:"small",type:"primary"},{default:v(()=>e[5]||(e[5]=[V(" 刷新数据 ")])),_:1},8,["icon"])]),i("div",Ue,[f(s,{icon:S.value?T(be):T(xe),type:S.value?"danger":"primary",onClick:ne,size:"small"},{default:v(()=>[V(h(S.value?"暂停":"播放"),1)]),_:1},8,["icon","type"]),f(s,{icon:T(J),onClick:me,size:"small"},{default:v(()=>e[6]||(e[6]=[V(" 重置 ")])),_:1},8,["icon"]),f(d,{modelValue:B.value,"onUpdate:modelValue":e[1]||(e[1]=l=>B.value=l),size:"small",style:{width:"100px"}},{default:v(()=>[f(c,{label:"0.5x",value:2e3}),f(c,{label:"1x",value:1e3}),f(c,{label:"2x",value:500}),f(c,{label:"4x",value:250})]),_:1},8,["modelValue"])]),i("div",Ae,[f(r,{modelValue:t.showHeatmap,"onUpdate:modelValue":e[2]||(e[2]=l=>t.showHeatmap=l),onChange:se,size:"small","active-text":"热力图","inactive-text":"热力图"},null,8,["modelValue"]),f(r,{modelValue:t.showPoints,"onUpdate:modelValue":e[3]||(e[3]=l=>t.showPoints=l),onChange:re,size:"small","active-text":"监测点","inactive-text":"监测点"},null,8,["modelValue"])]),i("div",Ge,[e[7]||(e[7]=i("span",{class:"legend-title"},"压力值(MPa)：",-1)),i("div",Ne,[(D(!0),b(q,null,K(Z.value,l=>(D(),b("div",{key:l.value,class:"legend-item"},[i("div",{class:"legend-color",style:Q({backgroundColor:l.color})},null,4),i("span",Je,h(l.label),1)]))),128))])])]),i("div",qe,[i("div",Ke,h(X.value),1),i("div",Qe,[i("div",We,[e[8]||(e[8]=i("div",{class:"stat-label"},"平均压力",-1)),i("div",Xe,h(w.value.avgPressure)+"MPa",1)]),i("div",Ze,[e[9]||(e[9]=i("div",{class:"stat-label"},"最高压力",-1)),i("div",et,h(w.value.maxPressure)+"MPa",1)]),i("div",tt,[e[10]||(e[10]=i("div",{class:"stat-label"},"最低压力",-1)),i("div",at,h(w.value.minPressure)+"MPa",1)]),i("div",st,[e[11]||(e[11]=i("div",{class:"stat-label"},"异常点位",-1)),i("div",rt,h(w.value.abnormalCount)+"个",1)])])]),$.value.length>0?(D(),b("div",ot,[i("div",it,[i("div",nt,h(j.value.start),1),f(p,{modelValue:z.value,"onUpdate:modelValue":e[4]||(e[4]=l=>z.value=l),min:0,max:Math.max(0,$.value.length-1),step:1,"show-tooltip":!1,onChange:ce,class:"time-slider"},null,8,["modelValue","max"]),i("div",lt,h(j.value.end),1)]),i("div",mt,[(D(!0),b(q,null,K(ee.value,(l,M)=>(D(),b("div",{key:M,class:"time-marker",style:Q({left:l.position+"%"})},h(l.label),5))),128))])])):we("",!0)])]),_:1},8,["windows"])])}}},gs=Me(ct,[["__scopeId","data-v-5db13cf8"]]);export{gs as default};
