package org.thingsboard.server.controller.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.store.StoreInRecord;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.store.StoreInRecordService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/StoreInRecord")
public class StoreInRecordController extends BaseController {
    @Autowired
    private StoreInRecordService service;


    @GetMapping
    public IPage<StoreInRecord> findAllConditional(StoreInRecordPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public StoreInRecord save(@RequestBody StoreInRecordSaveRequest req) throws ThingsboardException {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody StoreInRecordSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public IstarResponse delete(@PathVariable String id) {
        return IstarResponse.ok(service.delete(id));
    }
}