// 工程管理-工程流程
import request from '@/plugins/axios';

// 查询流程类型
export function processTypeList(params: {
  name?: string;
  size: number;
  page: number;
}) {
  return request({
    url: '/api/process/type',
    method: 'get',
    params
  });
}

// 增加修改流程类型
export function editProcessType(params: {
  name: string;
  type: string;
  status: boolean;
  remark?: string;
  contractTemplateList?: any[];
}) {
  return request({
    url: '/api/process/type',
    method: 'post',
    data: params
  });
}

// 删除流程类型
export function delProcessType(ids: string[]) {
  return request({
    url: '/api/process/type',
    method: 'delete',
    data: ids || []
  });
}

// 查询步骤
export function processStepList(params: {
  name?: string;
  size: number;
  page: number;
}) {
  return request({
    url: '/api/process/step',
    method: 'get',
    params
  });
}

// 增加步骤
export function editProcessStep(params: {
  mainId: string;
  name: string;
  type: string;
  orderNum: number;
  formId: string;
  title: string;
  executionDay: string;
  users?: string;
  remark?: string;
}) {
  return request({
    url: '/api/process/step',
    method: 'post',
    data: params
  });
}

// 删除步骤
export function delProcessStep(ids: string[]) {
  return request({
    url: '/api/process/step',
    method: 'delete',
    data: ids || []
  });
}
// 查询步骤附件
export function stepAttachmentList(params: {
  name?: string;
  size: number;
  page: number;
}) {
  return request({
    url: '/api/process/step/attachment',
    method: 'get',
    params
  });
}

// 增加步骤附件
export function editStepAttachment(params: {
  mainId: string;
  name: string;
  type: string;
  orderNum: number;
  formId: string;
  title: string;
  executionDay: string;
  users?: string;
  remark?: string;
}) {
  return request({
    url: '/api/process/step/attachment',
    method: 'post',
    data: params
  });
}

// 删除步骤附件
export function delStepAttachment(ids: string[]) {
  return request({
    url: '/api/process/step/attachment',
    method: 'delete',
    data: ids || []
  });
}

// 获取部门用户
export function getAllUserByPidStr(params: { pid: string }) {
  return request({
    url: '/api/user/getAllByPidStr',
    method: 'get',
    params
  });
}

// 获取表单列表
export function formList(params: {
  name?: string;
  size: number;
  page: number;
}) {
  return request({
    url: '/api/form',
    method: 'get',
    params
  });
}

// 删除表单
export function delForm(ids?: string[]) {
  return request({
    url: '/api/form',
    method: 'delete',
    data: ids
  });
}

// 新增/修改表单
export function ediForm(params: {
  name: string;
  type: string;
  status: boolean;
  remark?: string;
}) {
  return request({
    url: '/api/form',
    method: 'post',
    data: params
  });
}

// 根据id获取表单内容
export function getFormById(id: string) {
  return request({
    url: `/api/form/${id}`,
    method: 'get'
  });
}

// 获取步骤
export function getStepByMainId(mainId: string) {
  return request({
    url: `/api/install/project/c/${mainId}`,
    method: 'get'
  });
}

// 获取步骤
export function getFileByTypeId(id: string) {
  return request({
    url: `/api/process/type/${id}`,
    method: 'get'
  });
}

// 获取附件上传列表
export function getAttachmentByStepId(mainId: string, childId: string) {
  return request({
    url: `/api/install/project/attachment/${mainId}`,
    method: 'get',
    params: { childId }
  });
}
