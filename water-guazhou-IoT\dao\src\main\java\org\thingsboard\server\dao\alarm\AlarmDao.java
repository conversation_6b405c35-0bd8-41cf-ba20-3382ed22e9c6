/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.alarm;

import com.google.common.util.concurrent.ListenableFuture;
import org.thingsboard.server.common.data.alarm.Alarm;
import org.thingsboard.server.common.data.alarm.AlarmInfo;
import org.thingsboard.server.common.data.alarm.AlarmJsonId;
import org.thingsboard.server.common.data.alarm.AlarmQuery;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.Dao;

import java.util.List;
import java.util.UUID;

/**
 * Created by ash<PERSON><PERSON><PERSON> on 11.05.17.
 */
public interface AlarmDao extends Dao<Alarm> {

    Boolean deleteAlarm(TenantId tenantId, Alarm alarm);

    ListenableFuture<Alarm> findLatestByOriginatorAndType(TenantId tenantId, EntityId originator, String type);

    ListenableFuture<Alarm> findByIdAsync(UUID key);

    Alarm save(TenantId tenantId, Alarm alarm);

    ListenableFuture<List<AlarmInfo>> findAlarms(TenantId tenantId, AlarmQuery query);

    ListenableFuture<List<Alarm>> findUnClearByJsonId(AlarmJsonId id);

    List<Alarm> findClearAlarmByTenantAndLevel(TenantId tenantId, List<DeviceId> deviceId, String type, String level, String status, long start, long end);

    ListenableFuture<List<Alarm>> findHistoryAlarm( TenantId tenantId,long start, long end);

    ListenableFuture<List<Alarm>> findRealTimeAlarm(TenantId tenantId,long start, long end);

    ListenableFuture<List<Alarm>> findHistoryByDeviceId(DeviceId deviceId,long start, long end);

    ListenableFuture<List<Alarm>> findOnlineByTypeAndDevice(DeviceId deviceId,String type);

    ListenableFuture<List<Alarm>> findOnlineByLevelAndDevice(DeviceId deviceId,String level);

    ListenableFuture<List<Alarm>> findHistoryAlarmByDevice(DeviceId deviceId,String alarmType);

    void deleteAlarmByDevice(DeviceId deviceId);

    ListenableFuture<List<Alarm>> findOnlineAlarmByJsonId(AlarmJsonId id);

    ListenableFuture<List<Alarm>> findAlarmByJsonId(AlarmJsonId id);

    ListenableFuture<List<Alarm>> findNotOfflineAlarmByDevice(DeviceId deviceId);

    List<String> findAlarmDeviceIdList(TenantId tenantId);

}
