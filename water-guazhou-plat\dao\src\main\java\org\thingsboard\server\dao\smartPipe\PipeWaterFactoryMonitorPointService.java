package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeWaterFactoryMonitorPoint;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-19
 */
public interface PipeWaterFactoryMonitorPointService {

    String check(PipeWaterFactoryMonitorPoint pipeWaterFactoryMonitorPoint);

    PipeWaterFactoryMonitorPoint save(PipeWaterFactoryMonitorPoint pipeWaterFactoryMonitorPoint);

    List<PipeWaterFactoryMonitorPoint> getList(String name, String tenantId);

    String changeDirection(String id);

    String changeOrderNum(String id, Integer orderNum);

    void delete(List<String> idList);

    List<JSONObject> getReport(String type, String date, String start, String end, String tenantId);

    List<JSONObject> getReportHeader(String tenantId);
}
