<!-- 工程管理-工程管理-签证单 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import {
  getProjectType,
  getConstructionVisaList,
  postConstructionVisa,
  getConstructionVisaGlobalExport,
  postConstructionVisaComplete,
  getConstructionVisaExport
} from '@/api/engineeringManagement/manage';
import { getProjectList } from '@/api/engineeringManagement/projectManagement';
import detail from './components/detail.vue';
import { StatusType } from '../../data';
import { traverse } from '@/utils/GlobalHelper';

const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '工程编号', field: 'constructionCode', type: 'input' },
    { label: '工程名称', field: 'constructionName', type: 'input' },
    { label: '业主单位', field: 'firstPartOrganization', type: 'input' },
    { label: '监理单位', field: 'supervisorOrganization', type: 'input' },
    {
      label: '工程类别',
      field: 'constructionTypeId',
      type: 'select',
      options: computed(() => data.projectType) as any
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'default',
          perm: true,
          text: '导出',
          icon: ICONS.DOWNLOAD,
          click: () => {
            getConstructionVisaGlobalExport().then((res) => {
              const url = window.URL.createObjectURL(res.data);
              const link = document.createElement('a');
              link.style.display = 'none';
              link.href = url;
              link.setAttribute('download', `签证单.xlsx`);
              document.body.appendChild(link);
              link.click();
            });
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: false,
  indexVisible: true,
  expandable: true,
  expandComponent: shallowRef(detail),
  extendedReturn: () => {
    refreshData();
  },
  rowKey: 'constructionCode',
  columns: [
    { label: '工程编号', prop: 'constructionCode' },
    { label: '工程名称', prop: 'constructionName' },
    { label: '工程类别', prop: 'constructionTypeName' },
    { label: '业主单位', prop: 'firstpartOrganization' },
    { label: '施工单位', prop: 'constructionOrganization' },
    { label: '设计单位', prop: 'secondpartOrganization' },
    { label: '监理单位', prop: 'supervisorOrganization' },
    { label: '合同总金额(万元)', prop: 'contractTotalCost' },
    {
      label: '工作状态',
      prop: 'status',
      tag: true,
      tagColor: (row): string =>
        StatusType.find((item) => item.value === row.status)?.color || '',
      formatter: (row) =>
        StatusType.find((item) => item.value === row.status)?.label
    }
  ],
  operationWidth: '360px',
  operations: [
    {
      disabled: (val) => !(val.status !== 'COMPLETED'),
      isTextBtn: false,
      type: 'primary',
      text: '添加工程签证',
      perm: true,
      click: (row) => {
        clickAdd(row);
      }
    },
    {
      disabled: (val) => val.status === 'COMPLETED' || val.items.length === 0,
      isTextBtn: false,
      type: 'success',
      text: '完成',
      perm: true,
      click: (row) => {
        postConstructionVisaComplete(row.constructionCode).then((res) => {
          if (res.data.code === 200) {
            ElMessage.success('已完成');
          } else {
            ElMessage.warning('完成失败');
          }
          refreshData();
        });
      }
    },
    {
      isTextBtn: false,
      text: '导出签证情况',
      perm: true,
      click: (row) => {
        getConstructionVisaExport(row.constructionCode).then((res) => {
          const url = window.URL.createObjectURL(res.data);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', `${row.constructionName}工程签证.xlsx`);
          document.body.appendChild(link);
          link.click();
        });
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '编辑签证',
  appendToBody: true,
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    params.pipLengthDesign = JSON.stringify(params.pipLengthDesign);
    postConstructionVisa(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
        } else {
          ElMessage.warning(text + '失败');
        }
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '签证编号',
          field: 'code',
          rules: [{ required: true, message: '请输入签证编号' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '工程编号',
          field: 'constructionCode',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '工程名称',
          field: 'constructionName',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '施工单位',
          field: 'constructOrganization',
          rules: [{ required: true, message: '请输入施工单位' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '施工地点',
          field: 'address',
          rules: [{ required: true, message: '请输入施工地点' }]
        },
        {
          xs: 12,
          type: 'date',
          label: '施工时间',
          field: 'constructTime',
          format: 'x'
        },
        {
          xs: 12,
          type: 'input',
          label: '建设单位',
          field: 'buildOrganization',
          rules: [{ required: true, message: '请输入建设单位' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '监理单位',
          field: 'supervisorOrganization',
          rules: [{ required: true, message: '请输入监理单位' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '审计单位',
          field: 'auditOrganization',
          rules: [{ required: true, message: '请输入审计单位' }]
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

const clickAdd = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '添加签证';
  data.DesignTubeLength = [];
  addOrUpdateConfig.defaultValue = {
    constructionCode: row.constructionCode,
    constructionName: row.constructionName,
    constructTime: new Date()
  };
  refForm.value?.openDialog();
};

const data = reactive({
  // 项目
  projectList: [],
  // 项目类别
  projectType: [],
  // 预算管长
  DesignTubeLength: [] as any[],
  getOptions: () => {
    getProjectList({ page: 1, size: -1 }).then((res) => {
      data.projectList = traverse(res.data.data.data || [], 'children', {
        label: 'name',
        value: 'code'
      });
    });
    getProjectType({ page: 1, size: -1 }).then((res) => {
      data.projectType = traverse(res.data.data.data || [], 'children');
    });
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  };
  getConstructionVisaList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  data.getOptions();
});
</script>

<style>
.cs {
  margin-top: 10px;
  padding-top: 20px;
}
</style>
