package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseVectorConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseVectorConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseVectorConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 公共管理平台-矢量数据配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Api(tags = "平台管理-矢量数据配置")
@RestController
@RequestMapping("api/base/vector/configuration")
public class BaseVectorConfigurationController extends BaseController {

    @Autowired
    private IBaseVectorConfigurationService baseVectorConfigurationService;

    /**
     * 查询公共管理平台-矢量数据配置列表
     */
    @MonitorPerformance(description = "平台管理-查询矢量数据配置列表")
    @ApiOperation(value = "查询矢量数据配置列表")
    @GetMapping("/list")
    public IstarResponse list(BaseVectorConfigurationPageRequest baseVectorConfiguration) {
        return IstarResponse.ok(baseVectorConfigurationService.selectBaseVectorConfigurationList(baseVectorConfiguration));
    }

    /**
     * 获取公共管理平台-矢量数据配置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询矢量数据配置")
    @ApiOperation(value = "查询矢量数据配置")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseVectorConfigurationService.selectBaseVectorConfigurationById(id));
    }

    /**
     * 新增公共管理平台-矢量数据配置
     */
    @MonitorPerformance(description = "平台管理-新增矢量数据配置")
    @ApiOperation(value = "新增矢量数据配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseVectorConfiguration baseVectorConfiguration) {
        return IstarResponse.ok(baseVectorConfigurationService.insertBaseVectorConfiguration(baseVectorConfiguration));
    }

    /**
     * 修改公共管理平台-矢量数据配置
     */
    @MonitorPerformance(description = "平台管理-修改矢量数据配置")
    @ApiOperation(value = "修改矢量数据配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseVectorConfiguration baseVectorConfiguration) {
        return IstarResponse.ok(baseVectorConfigurationService.updateBaseVectorConfiguration(baseVectorConfiguration));
    }

    /**
     * 删除公共管理平台-矢量数据配置
     */
    @MonitorPerformance(description = "平台管理-删除矢量数据配置")
    @ApiOperation(value = "删除矢量数据配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseVectorConfigurationService.deleteBaseVectorConfigurationByIds(ids));
    }

    /**
     * 获取所有的矢量数据配置数据
     * @return
     */
    @MonitorPerformance(description = "平台管理-获取所有的矢量数据配置数据")
    @ApiOperation(value = "获取所有的矢量数据配置数据")
    @GetMapping("/getAllBaseVectorConfiguration")
    public List<BaseVectorConfiguration> selectAllBaseVectorConfiguration() {
        return baseVectorConfigurationService.selectAllBaseVectorConfiguration();
    }
}
