package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrangePartner;
import org.thingsboard.server.dao.util.imodel.query.AwareCurrentUserUUID;
import org.thingsboard.server.dao.util.imodel.query.AwareTenantUUID;
import org.thingsboard.server.dao.util.imodel.query.Requestible;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class GuardArrangeSwitchRequest implements Requestible, AwareCurrentUserUUID, AwareTenantUUID {
    // 排班成员条目id
    @NotNullOrEmpty
    private String id;

    // 替换用户id
    @NotNullOrEmpty
    private List<String> userIdList;

    // 调班原因
    @NotNullOrEmpty
    private String remark;


    @Setter(AccessLevel.NONE)
    private List<GuardArrangePartner> guardArrangePartners;

    @Setter(AccessLevel.NONE)
    private String arrangeId;

    @Setter(AccessLevel.NONE)
    private String currentUserId;

    @Setter(AccessLevel.NONE)
    private String tenantId;


    public List<GuardArrangePartner> buildArrangeList(String arrangeId) {
        if (guardArrangePartners != null) {
            return guardArrangePartners;
        }

        this.arrangeId = arrangeId;
        if (userIdList.size() == 0) {
            return Collections.emptyList();
        }

        guardArrangePartners = userIdList.stream()
                .map(userId -> new GuardArrangePartner(arrangeId, userId))
                .collect(Collectors.toList());
        return guardArrangePartners;
    }

    @Override
    public void currentUserId(String uuid) {
        currentUserId = uuid;
    }

    @Override
    public void tenantId(String uuid) {
        tenantId = uuid;
    }

    public String getRandomId() {
        return IdWorker.get32UUID();
    }

}
