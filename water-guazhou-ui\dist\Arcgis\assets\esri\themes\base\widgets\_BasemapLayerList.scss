@mixin basemapLayerList() {
  $message-warning-border-color: $Calcite_Yellow_a150;
  $indicator-size: 6px;
  .esri-basemap-layer-list {
    color: $font-color;
    background-color: $background-color--offset;
    padding: calc(var(--esri-widget-padding-y) * 0.5) calc(var(--esri-widget-padding-x) * 0.5);
    overflow-y: auto;
    display: flex;
    flex-flow: column;
  }
  .esri-basemap-layer-list__title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $cap-spacing--quarter 0;
  }

  .esri-widget__heading.esri-basemap-layer-list__main-heading {
    flex: 1 1;
    margin: 0;
    padding: $cap-spacing--quarter $side-spacing--quarter $cap-spacing--quarter 0;
    font-size: $font-size;
  }

  .esri-basemap-layer-list__editing-card {
    display: flex;
    flex-flow: column;
    padding: $cap-spacing $side-spacing;
    background-color: $background-color;
    @include defaultBoxShadow();
    border-radius: $border-radius;
    width: 100%;
    animation: esri-fade-in-down 250ms ease-in-out;
  }

  .esri-basemap-layer-list__editing-input {
    display: flex;
    flex-flow: column;
  }

  .esri-basemap-layer-list__editing-actions {
    display: flex;
    justify-content: flex-end;
    margin: $cap-spacing--half 0 0 0;
    .esri-button {
      font-size: $font-size--small;
      min-height: $button-height--half;
      width: 33%;
    }
  }

  .esri-basemap-layer-list__edit-button {
    background-color: transparent;
    border: none;
    color: $interactive-font-color;
    width: $button-width;
    height: $button-height;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 0 0 $button-width;
    border-radius: $border-radius;
    transition: background-color 125ms ease-in-out;
    cursor: pointer;
    &:hover,
    &:focus {
      background-color: $border-color;
      color: $interactive-font-color--hover;
    }
  }

  .esri-widget__heading.esri-basemap-layer-list__list-heading {
    font-size: $font-size--small;
    padding: 0;
    font-weight: $font-weight;
  }
  .esri-basemap-layer-list__list {
    list-style: none;
    margin: 0 0 0 $side-spacing;
    padding: 0;
    @include sortableChosen("esri-basemap-layer-list--chosen");
  }
  .esri-basemap-layer-list__list,
  .esri-basemap-layer-list__item {
    &.esri-basemap-layer-list--chosen .esri-basemap-layer-list__item {
      background-color: transparent;
    }
  }
  .esri-basemap-layer-list__item--has-children {
    padding-bottom: $cap-spacing--half;
  }
  .esri-basemap-layer-list__item--has-children .esri-basemap-layer-list__list:not([hidden]) {
    animation: esri-fade-in 375ms ease-in-out;
  }
  .esri-basemap-layer-list__list[hidden] {
    display: none;
  }
  .esri-basemap-layer-list__list--root {
    margin: 0;
  }
  .esri-basemap-layer-list__item--selectable .esri-basemap-layer-list__item-container {
    cursor: pointer;
    &:hover {
      border-left-color: $border-color;
    }
  }
  .esri-basemap-layer-list__item[aria-selected="true"] > .esri-basemap-layer-list__item-container {
    border-left-color: $border-color--active;
    &:hover {
      border-left-color: $border-color--active;
    }
  }
  .esri-basemap-layer-list__item-container ~ .esri-basemap-layer-list__list .esri-basemap-layer-list__item {
    border-bottom-width: 0;
  }
  .esri-basemap-layer-list__item {
    background-color: $background-color;
    border-bottom: 1px solid $border-color;
    position: relative;
    overflow: hidden;
    list-style: none;
    margin: $cap-spacing--quarter 0;
    padding: 0;
    @include sortableChosen("esri-basemap-layer-list--chosen");
  }
  .esri-basemap-layer-list__item-container {
    border-left: $border-size--active solid transparent;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    padding: $cap-spacing $side-spacing--half $cap-spacing ($side-spacing + 5);
    transition: border-color 250ms ease-in-out;
  }
  .esri-basemap-layer-list__item--invisible-at-scale .esri-basemap-layer-list__item-title {
    color: $interactive-font-color--disabled;
  }
  .esri-basemap-layer-list__item--has-children > .esri-basemap-layer-list__item-container {
    padding-left: 5px;
  }
  .esri-basemap-layer-list__item--has-children > .esri-basemap-layer-list__list {
    font-size: $font-size--small;
  }
  .esri-basemap-layer-list__child-toggle {
    color: $interactive-font-color;
    width: $side-spacing; // Matches side padding on items that don't have this toggle.
    align-self: center;
    display: flex;
    cursor: pointer;
  }
  .esri-basemap-layer-list__child-toggle {
    @include icomoonIconSelector() {
      line-height: 1.2em;
    }
  }
  .esri-basemap-layer-list__child-toggle .esri-basemap-layer-list__child-toggle-icon--opened,
  .esri-basemap-layer-list__child-toggle .esri-basemap-layer-list__child-toggle-icon--closed-rtl,
  .esri-basemap-layer-list__child-toggle--open .esri-basemap-layer-list__child-toggle-icon--closed {
    display: none;
  }
  .esri-basemap-layer-list__child-toggle--open .esri-basemap-layer-list__child-toggle-icon--opened {
    display: block;
  }
  .esri-basemap-layer-list__item-label {
    display: flex;
    flex-flow: row;
    justify-content: flex-start;
    align-items: center;
    flex: 1;
    user-select: none;
  }
  .esri-basemap-layer-list__item-label[role="switch"],
  .esri-basemap-layer-list__item-label[role="checkbox"],
  .esri-basemap-layer-list__item-label[role="radio"] {
    cursor: pointer;
  }
  .esri-basemap-layer-list--new-ui {
    .esri-basemap-layer-list__item-toggle-icon {
      visibility: hidden;
    }
    .esri-basemap-layer-list__item--invisible .esri-basemap-layer-list__item-toggle-icon {
      color: inherit;
    }
    // show icon on focus of toggle/label
    .esri-basemap-layer-list__item-toggle:focus .esri-basemap-layer-list__item-toggle-icon,
    .esri-basemap-layer-list__item-label:focus .esri-basemap-layer-list__item-toggle-icon,
    // how icon on hover of item container
    .esri-basemap-layer-list__item-container:hover .esri-basemap-layer-list__item-toggle-icon,
    // always show icon when item is not visible
    .esri-basemap-layer-list__item--invisible > .esri-basemap-layer-list__item-container .esri-basemap-layer-list__item-toggle-icon {
      visibility: visible;
    }
  }
  .esri-basemap-layer-list__item-title {
    flex: 1;
    padding-left: $side-spacing--third;
    padding-right: $side-spacing--third;
    line-height: $line-height;
    word-break: break-word;
    overflow-wrap: break-word;
    transition: color 125ms ease-in-out;
  }

  .esri-basemap-layer-list__status-indicator {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-block: 0;
    height: $indicator-size;
    width: $indicator-size;
    margin-inline: $cap-spacing;
  }

  .esri-basemap-layer-list__publishing {
    border: 1px solid $interactive-font-color;
    animation: publishing 2s normal infinite;
  }

  .esri-basemap-layer-list__updating {
    background-color: $updating;
    border-radius: 50%;
    animation: updating 2s normal infinite;
  }

  .esri-basemap-layer-list__connection-status {
    height: $button-width--half;
    width: $button-width--half;
    margin-inline: $side-spacing--half;
    color: $connection-disconnected;
  }

  .esri-basemap-layer-list__connection-status--connected {
    color: $connection-connected;
  }

  .esri-basemap-layer-list__item-message {
    display: flex;
    align-items: center;
    visibility: hidden;
    height: 0;
    margin-top: -1px;
    padding: $cap-spacing--half $side-spacing--half;
    overflow: hidden;
    font-size: $font-size--small;
    transition: transform 250ms ease-in-out;
    transform: scale(1, 0);
    animation: esri-fade-in-down 250ms ease-in-out;
    transform-origin: center top;
    background-color: $background-color--offset-subtle;
    margin-inline-start: 3rem;
    border-inline-start: $border-size--active solid $message-warning-border-color;
    margin-block-end: 0.25rem;
    margin-inline-end: 0.25rem;
  }
  .esri-basemap-layer-list__item-message {
    @include icomoonIconSelector() {
      margin-right: 0.3rem;
    }
  }
  .esri-basemap-layer-list__item--has-message {
    .esri-basemap-layer-list__item-message {
      visibility: visible;
      height: auto;
      transform: scale(1, 1);
    }
  }
  .esri-basemap-layer-list__item-toggle {
    padding: 0 $side-spacing--quarter;
    cursor: pointer;
    color: $interactive-font-color;
    display: flex;
    align-items: center;
  }

  .esri-basemap-layer-list__item-actions-menu {
    align-self: center;
    display: flex;
  }
  .esri-basemap-layer-list__item-actions-menu-item {
    display: flex;
    flex: 1 0 auto;
    justify-content: center;
    align-items: center;
    color: $interactive-font-color;
    cursor: pointer;
    padding: 0 $side-spacing--half;
    transition: border-color 250ms ease-in-out;
  }
  .esri-basemap-layer-list__item-actions-menu-item .esri-disabled-element {
    pointer-events: none;
    opacity: $opacity--disabled;
  }
  .esri-basemap-layer-list__item-actions-menu-item:first-of-type {
    margin: 0 2px;
  }
  .esri-basemap-layer-list__item-actions-menu-item:hover {
    background-color: $background-color--hover;
  }
  .esri-basemap-layer-list__item-actions-menu-item--active,
  .esri-basemap-layer-list__item-actions-menu-item--active:hover {
    background-color: $background-color--active;
  }
  .esri-basemap-layer-list__item-actions {
    position: relative;
    background-color: $background-color--offset;
    color: $interactive-font-color;
    margin: -1px $side-spacing--half $cap-spacing--half;
    height: auto;
  }
  .esri-basemap-layer-list__item-actions[aria-expanded="true"] {
    animation: esri-fade-in 250ms ease-in-out;
  }
  .esri-basemap-layer-list__item-actions-section {
    animation: esri-fade-in 375ms ease-in-out;
  }
  .esri-basemap-layer-list__item-actions[hidden] {
    display: none;
  }
  .esri-basemap-layer-list__item-actions-close {
    color: $interactive-font-color;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    padding: 5px;
    z-index: 1;
  }
  .esri-basemap-layer-list__item-actions-list {
    display: flex;
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: $cap-spacing--half 0;
    list-style: none;
    border-top: 2px solid $background-color;
  }
  .esri-basemap-layer-list__item-actions-list:first-of-type {
    border-top: 0;
  }
  .esri-basemap-layer-list__item-action,
  .esri-basemap-layer-list__action-toggle {
    border: 1px solid transparent;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    cursor: pointer;
    font-size: $font-size--small;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
    padding: $cap-spacing--half $side-spacing;
    opacity: 1;
    transition: opacity 250ms ease-in-out 250ms, background-color 250ms ease-in-out;
  }

  .esri-basemap-layer-list__item-action {
    justify-content: flex-start;
    flex-flow: row;
  }
  .esri-basemap-layer-list__action-toggle {
    flex-flow: row-reverse;
    justify-content: space-between;
    .esri-basemap-layer-list__item-action-title {
      margin-left: 0;
    }
    .esri-basemap-layer-list__item-action-icon {
      background-color: $background-color--inverse;
      border-radius: $toggle-height;
      box-shadow: 0 0 0 1px $interactive-font-color--inverse;
      flex: 0 0 $toggle-width;
      height: $toggle-height;
      overflow: hidden;
      padding: 0;
      position: relative;
      transition: background-color 125ms ease-in-out;
      width: $icon-size;
      &:before {
        // Toggle handle. Overrides any icon class
        background-color: $interactive-font-color--inverse;
        border-radius: 100%;
        content: "";
        display: block;
        height: $toggle-handle-size;
        left: 0;
        margin: 2px;
        position: absolute;
        top: 0;
        transition: background-color 125ms ease-in-out, left 125ms ease-in-out;
        width: $toggle-handle-size;
      }
    }
  }
  .esri-basemap-layer-list__action-toggle--on .esri-basemap-layer-list__item-action-icon {
    // Toggle on
    background-color: $interactive-font-color--inverse;
    &:before {
      background-color: $background-color--inverse;
      box-shadow: 0 0 0 1px $background-color--inverse;
      left: $toggle-handle-size;
    }
  }
  .esri-basemap-layer-list__item-action:hover,
  .esri-basemap-layer-list__action-toggle:hover {
    background-color: $background-color--hover;
  }
  .esri-basemap-layer-list__item-actions[hidden] .esri-basemap-layer-list__item-action {
    opacity: 0;
  }
  .esri-basemap-layer-list__item-action-icon {
    flex: 0 0 $icon-size;
    font-size: $icon-size;
    display: inline-block;
    width: $icon-size;
    height: $icon-size;
    margin-top: 0.1em;
  }
  .esri-basemap-layer-list__item-action-image {
    flex: 0 0 $icon-size;
    width: $icon-size;
    height: $icon-size;
    font-size: $font-size;
    text-align: center;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: 50% 50%;
  }
  .esri-basemap-layer-list__item-action-title {
    margin-left: 5px;
  }
  .esri-basemap-layer-list-panel {
    margin: $cap-spacing $side-spacing;
  }
  .esri-basemap-layer-list__hr {
    border: none;
    height: 1px;
    width: 100%;
    background-color: $border-color;
  }
  .esri-basemap-layer-list__no-items {
    color: $interactive-font-color;
    text-align: center;
    padding: $cap-spacing--double $side-spacing;
  }

  // Legend as content
  .esri-basemap-layer-list-panel__content--legend .esri-legend__service {
    padding: 0 0 $cap-spacing 0;
  }

  [dir="rtl"] .esri-basemap-layer-list {
    .esri-basemap-layer-list__item--has-children > .esri-basemap-layer-list__item-container {
      padding-left: $side-spacing + 5;
      padding-right: 5px;
    }
    .esri-basemap-layer-list__list {
      margin: 0 $side-spacing 0 0;
    }
    .esri-basemap-layer-list__list--root {
      margin: 0;
    }
    .esri-basemap-layer-list__child-toggle .esri-basemap-layer-list__child-toggle-icon--closed {
      display: none;
    }
    .esri-basemap-layer-list__child-toggle .esri-basemap-layer-list__child-toggle-icon--closed-rtl {
      display: block;
    }
    .esri-basemap-layer-list__child-toggle--open .esri-basemap-layer-list__child-toggle-icon--closed-rtl {
      display: none;
    }
    .esri-basemap-layer-list__item-action-title {
      margin-left: 0;
      margin-right: 5px;
    }
    .esri-basemap-layer-list__action-toggle .esri-basemap-layer-list__action-toggle {
      margin-right: 0;
    }
    .esri-basemap-layer-list__item:after {
      animation: looping-progresss-bar-ani $looping-progress-bar-params reverse;
    }
    .esri-basemap-layer-list__item-message {
      @include icomoonIconSelector() {
        margin-right: 0;
        margin-left: 0.3rem;
      }
    }
    .esri-basemap-layer-list__item--selectable .esri-basemap-layer-list__item-container {
      border-left: none;
      border-right: $border-size--active solid transparent;
      &:hover {
        border-right-color: $border-color;
      }
    }
    .esri-basemap-layer-list__item[aria-selected="true"] > .esri-basemap-layer-list__item-container {
      border-right-color: $border-color--active;
      &:hover {
        border-right-color: $border-color--active;
      }
    }
  }

  @keyframes updating {
    0%,
    40% {
      background-color: transparent;
    }
    50%,
    80% {
      background-color: var(--calcite-ui-brand);
    }
    100% {
      background-color: transparent;
    }
  }

  @keyframes publishing {
    0%,
    20% {
      transform: rotate(45deg);
    }
    80%,
    100% {
      transform: rotate(135deg);
    }
  }
}

@if $include_BasemapLayerList==true {
  @include basemapLayerList();
}
