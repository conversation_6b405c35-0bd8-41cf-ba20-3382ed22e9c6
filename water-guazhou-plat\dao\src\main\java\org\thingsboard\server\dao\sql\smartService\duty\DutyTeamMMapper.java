package org.thingsboard.server.dao.sql.smartService.duty;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.duty.DutyTeamM;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-11-14
 */
@Mapper
public interface DutyTeamMMapper extends BaseMapper<DutyTeamM> {

    List<DutyTeamM> getList(@Param("keywords") String keywords, @Param("tenantId") String tenantId);
}
