import{d as M,c as k,r as x,b as C,Q as T,g as z,h as G,F as w,p as O,q as B,i as c,_ as P,X as W}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as Y,a as Q}from"./LayerHelper-Cn-iiqxI.js";import{g as V}from"./QueryHelper-ILO3qZqg.js";import{E as $}from"./StatisticsHelper-D-s_6AyQ.js";import{u as j}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import J from"./RightDrawerMap-D5PhmGFO.js";import K from"./StatisticsCharts-CyK-dNnC.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./useDetector-BRcb7GRN.js";import"./useHighLight-DPevRAc5.js";import"./ToolHelper-BiiInOzB.js";import"./geoserverUtils-wjOSMa7E.js";import"./echart-BoVIcYbV.js";import"./config-DqqM5K5L.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const mr=M({__name:"RoadStatistics",setup(U){const y=k(),d=k(),f=k(),r=x({tabs:[],layerInfos:[],layerIds:[],loading:!1}),o={},I=x({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>r.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>g("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>r.loading,iconifyIcon:"ep:crop",click:()=>g("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",disabled:()=>r.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>g("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>r.loading,iconifyIcon:"ep:delete",click:()=>_()}]}]},{id:"layerid",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value"},{type:"btn-group",btns:[{perm:!0,text:"统计",styles:{width:"100%"},loading:()=>r.loading,click:()=>S()}]}]}],labelPosition:"top",gutter:12}),D=(t,e)=>{const a=t.label;u(a,e,!0)},F=(t,e)=>{const a=t==null?void 0:t.label;u(a,e)},L=(t,e)=>{const a=t==null?void 0:t.name;u(a,e)},u=async(t,e,a)=>{var l,m;if(t===void 0)return;let p="";if(t==="合计")p="1=1";else if(t==="--"){p="LANEWAY is null";return}else p="LANEWAY='"+t+"'";await S(p,e);const s=(l=f.value)==null?void 0:l.getCurLayer();(m=f.value)==null||m.refreshDetail(s,a??!0)},g=t=>{var e;o.view&&(_(),(e=o.sketch)==null||e.create(t))},_=()=>{var t;(t=o.graphicsLayer)==null||t.removeAll(),o.graphics=void 0},R=async()=>{var s,l,m;r.layerIds=Q(o.view);const t=await W(r.layerIds);r.layerInfos=((l=(s=t.data)==null?void 0:s.result)==null?void 0:l.rows)||[];const e=(m=I.group.find(i=>i.id==="layerid"))==null?void 0:m.fields[0],a=r.layerInfos.filter(i=>i.geometrytype==="esriGeometryPoint").map(i=>({label:i.layername,value:i.layerid,data:i})),p=r.layerInfos.filter(i=>i.geometrytype==="esriGeometryPolyline").map(i=>({label:i.layername,value:i.layerid,data:i}));e&&(e.options=[{label:"管点类",value:-1,children:a,disabled:!0},{label:"管线类",value:-2,children:p,disabled:!0}]),d.value&&(d.value.dataForm.layerid=[...r.layerIds])},S=async(t,e)=>{var a,p,s,l,m;C.info("正在统计，请稍候...");try{r.loading=!0;const i=e===void 0?((a=d.value)==null?void 0:a.dataForm.layerid)||[]:r.layerInfos.filter(n=>n.layername===e).map(n=>n.layerid);if(!i.length){C.warning("请选择要统计的图层"),r.loading=!1;return}const h=await V(i,r.layerInfos,{where:t||"1=1",geometry:(p=o.graphics)==null?void 0:p.geometry});if(e!==void 0)if(r.tabs.length){const n=r.tabs.find(b=>b.name===e),v=h.find(b=>b.name===e);n&&(n.data=v==null?void 0:v.data),(s=f.value)==null||s.refreshTable(e,i==null?void 0:i[0])}else r.tabs=h;else r.tabs=h;((l=y.value)==null?void 0:l.isCustomOpened())||(m=y.value)==null||m.toggleCustomDetail(!0)}catch(i){console.log(i),r.loading=!1,C.error("统计失败")}},{initSketch:E,destroySketch:N}=j(),A=t=>{o.graphics=t.graphics[0]},q=t=>{o.view=t,o.graphicsLayer=Y(o.view,{id:"search-pipe-road",title:"按道路统计管长"}),o.sketch=E(o.view,o.graphicsLayer,{createCallBack:A,updateCallBack:A}),R()};return T(()=>{var t,e;N(),(t=o.graphicsLayer)==null||t.removeAll(),(e=o.graphicsLayer)==null||e.destroy()}),(t,e)=>{const a=P;return z(),G(J,{ref_key:"refMap",ref:y,title:"按道路统计","full-content":!0,onMapLoaded:q},{"detail-header":w(()=>e[1]||(e[1]=[O("span",null,"统计结果",-1)])),"detail-default":w(()=>{var p;return[B(K,{ref_key:"refStatisticsCharts",ref:f,view:o.view,"layer-ids":c(r).layerIds,"query-params":{where:"1=1",geometry:(p=o.graphics)==null?void 0:p.geometry},"statistics-params":{group_fields:["LANEWAY"],statistic_field:c($).OBJECTID,statistic_type:"1"},tabs:c(r).tabs,unit:"个",onDetailRefreshed:e[0]||(e[0]=s=>c(r).loading=!1),onAttrRowClick:F,onRingClick:L,onBarClick:L,onTotalRowClick:D},null,8,["view","layer-ids","query-params","statistics-params","tabs"])]}),default:w(()=>[B(a,{ref_key:"refForm",ref:d,config:c(I)},null,8,["config"])]),_:1},512)}}});export{mr as default};
