package org.thingsboard.server.dao.model.sql.smartProduction.dispatch;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecordStatus.*;

public class MessageRecordStatusTypeHandler extends BaseTypeHandler<MessageRecordStatus> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, MessageRecordStatus parameter, JdbcType jdbcType) throws SQLException {
        if (jdbcType == null) {
            ps.setString(i, parameter.name());
        } else {
            ps.setObject(i, parameter.name(), jdbcType.TYPE_CODE);
        }
    }

    @Override
    public MessageRecordStatus getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String s = rs.getString(columnName);
        if (s == null) {
            return null;
        }

        try {
            MessageRecordStatus.valueOf(s);
        } catch (IllegalArgumentException e) {
            return FAILURE;
        }

        return MessageRecordStatus.valueOf(s);
    }

    @Override
    public MessageRecordStatus getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String s = rs.getString(columnIndex);
        if (s == null) {
            return null;
        }

        try {
            MessageRecordStatus.valueOf(s);
        } catch (IllegalArgumentException e) {
            return FAILURE;
        }

        return MessageRecordStatus.valueOf(s);
    }

    @Override
    public MessageRecordStatus getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String s = cs.getString(columnIndex);
        if (s == null) {
            return null;
        }

        try {
            MessageRecordStatus.valueOf(s);
        } catch (IllegalArgumentException e) {
            return FAILURE;
        }

        return MessageRecordStatus.valueOf(s);
    }
}
