package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeWaterFactoryMonitorPoint;
import org.thingsboard.server.dao.sql.smartPipe.PipeWaterFactoryMonitorPointMapper;
import org.thingsboard.server.dao.util.InfluxUtil;
import org.thingsboard.server.dao.util.TimeUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class PipeWaterFactoryMonitorPointServiceImpl implements PipeWaterFactoryMonitorPointService {

    @Autowired
    private PipeWaterFactoryMonitorPointMapper pipeWaterFactoryMonitorPointMapper;

    @Autowired
    private InfluxUtil influxUtil;

    @Override
    public PipeWaterFactoryMonitorPoint save(PipeWaterFactoryMonitorPoint pipeWaterFactoryMonitorPoint) {
        pipeWaterFactoryMonitorPoint.setCreateTime(new Date());

        pipeWaterFactoryMonitorPointMapper.insert(pipeWaterFactoryMonitorPoint);

        return pipeWaterFactoryMonitorPoint;
    }

    @Override
    public List<PipeWaterFactoryMonitorPoint> getList(String name, String tenantId) {
        return pipeWaterFactoryMonitorPointMapper.getList(name, "", tenantId);
    }

    @Override
    public String changeDirection(String id) {
        if (StringUtils.isBlank(id)) {
            return "请选择水厂";
        }
        PipeWaterFactoryMonitorPoint pipeWaterFactoryMonitorPoint = pipeWaterFactoryMonitorPointMapper.selectById(id);
        if (pipeWaterFactoryMonitorPoint == null) {
            return "该水厂或流量计不存在";
        }
        switch (pipeWaterFactoryMonitorPoint.getDirection()) {
            case "3":
                pipeWaterFactoryMonitorPoint.setDirection("4");
                break;
            case "4":
                pipeWaterFactoryMonitorPoint.setDirection("3");
                break;
        }
        pipeWaterFactoryMonitorPointMapper.updateById(pipeWaterFactoryMonitorPoint);

        return "";
    }

    @Override
    public String changeOrderNum(String id, Integer orderNum) {
        if (StringUtils.isBlank(id)) {
            return "请选择水厂";
        }
        PipeWaterFactoryMonitorPoint pipeWaterFactoryMonitorPoint = pipeWaterFactoryMonitorPointMapper.selectById(id);
        if (pipeWaterFactoryMonitorPoint == null) {
            return "该水厂或流量计不存在";
        }

        pipeWaterFactoryMonitorPoint.setOrderNum(orderNum);

        pipeWaterFactoryMonitorPointMapper.updateById(pipeWaterFactoryMonitorPoint);

        return "";
    }

    @Override
    public void delete(List<String> idList) {
        pipeWaterFactoryMonitorPointMapper.deleteBatchIds(idList);
    }

    @Override
    public List getReport(String type, String date, String start, String end, String tenantId) {
        List<PipeWaterFactoryMonitorPoint> list = pipeWaterFactoryMonitorPointMapper.getList("", "", tenantId);
        if (list.size() == 0) {
            return new ArrayList();
        }
        String typeTemp = type;
        if ("month".equals(typeTemp)) {
            typeTemp = "monthOne";
        }

        Map<String, LocalDate> localDateByType = TimeUtils.getLocalDateByType(typeTemp, date, start, end);
        LocalDate startDate = localDateByType.get("start");
        LocalDate endDate = localDateByType.get("end");

        Map<String, String> deviceMap = new HashMap<>();
        Map<String, String> factoryMap = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();
        List<String> deviceList = new ArrayList<>();
        for (PipeWaterFactoryMonitorPoint pipeWaterFactoryMonitorPoint : list) {
            deviceList.add(pipeWaterFactoryMonitorPoint.getDeviceId() + ".total_flow");
            deviceMap.put(pipeWaterFactoryMonitorPoint.getDeviceId() + ".total_flow", pipeWaterFactoryMonitorPoint.getDeviceName());
            factoryMap.put(pipeWaterFactoryMonitorPoint.getDeviceId() + ".total_flow", pipeWaterFactoryMonitorPoint.getFactoryName());
            typeMap.put(pipeWaterFactoryMonitorPoint.getDeviceId() + ".total_flow", pipeWaterFactoryMonitorPoint.getDirection());
        }
        Map<String, JSONObject> resultMap = new LinkedHashMap<>();
        JSONObject dataMap;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String influxType = "1d";
        switch (type) {
            case "month":
            case "day":
                formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                break;
            case "year":
                formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                influxType = "1nc";
        }
        List<JSONObject> reportHeader = this.getReportHeader(tenantId);
        JSONArray titleList;

        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        while (startDate.isBefore(endDate)) {
            dataMap = new JSONObject();
            for (JSONObject object : reportHeader) {
                dataMap.put("date", formatter.format(startDate));
                dataMap.put("in" + object.getString("title"), BigDecimal.ZERO);
                dataMap.put("out" + object.getString("title"), BigDecimal.ZERO);
                dataMap.put("difference" + object.getString("title"), BigDecimal.ZERO);
                dataMap.put("sum", BigDecimal.ZERO);
                titleList = object.getJSONArray("titleList");
                for (Object subTitle : titleList) {
                    dataMap.put(((JSONObject) subTitle).getString("value"), BigDecimal.ZERO);
                }
            }

            resultMap.put(formatter.format(startDate), dataMap);

            if ("year".equals(type)) {
                startDate = startDate.plusMonths(1L);
            } else {
                startDate = startDate.plusDays(1L);
            }
        }

        JSONObject data = influxUtil.getData(deviceList, startTime, endTime, influxType);

        List<String> timeList = data.keySet().stream().collect(Collectors.toList());
        BigDecimal value;
        for (String time : timeList) {
            if (data.get(time) == null) {
                continue;
            }
            dataMap = resultMap.get(time);
            for (String deviceId : deviceList) {
                value = data.getJSONObject(time).getBigDecimal(deviceId);
                if (value == null) {
                    continue;
                }
                if (DataConstants.PARTITION_MOUNT_DIRECTION.OUT.getValue().equals(typeMap.get(deviceId))) {
                    dataMap.put(deviceMap.get(deviceId), dataMap.getBigDecimal(deviceMap.get(deviceId)).add(value));
                    dataMap.put("out" + factoryMap.get(deviceId), dataMap.getBigDecimal("out" + factoryMap.get(deviceId)).add(value));
                    dataMap.put("sum", dataMap.getBigDecimal("sum").add(value));
                } else {
                    dataMap.put("in" + factoryMap.get(deviceId), dataMap.getBigDecimal("in" + factoryMap.get(deviceId)).add(value));
                }
                dataMap.put("difference" + factoryMap.get(deviceId), dataMap.getBigDecimal("in" + factoryMap.get(deviceId)).subtract(dataMap.getBigDecimal("out" + factoryMap.get(deviceId))));

            }
        }


        return resultMap.values().stream().collect(Collectors.toList());
    }

    @Override
    public List<JSONObject> getReportHeader(String tenantId) {
        List<PipeWaterFactoryMonitorPoint> list = pipeWaterFactoryMonitorPointMapper.getList("", "", tenantId);
        Map<String, JSONObject> resultMap = new LinkedHashMap<>();
        JSONObject resultObject;
        JSONObject tempObject;
        for (PipeWaterFactoryMonitorPoint pipeWaterFactoryMonitorPoint : list) {
            resultObject = resultMap.get(pipeWaterFactoryMonitorPoint.getFactoryName());
            if (resultObject == null) {
                resultObject = new JSONObject();
                resultObject.put("title", pipeWaterFactoryMonitorPoint.getFactoryName());
                resultObject.put("titleList", new JSONArray());
                resultMap.put(pipeWaterFactoryMonitorPoint.getFactoryName(), resultObject);
            }
            if (DataConstants.PARTITION_MOUNT_DIRECTION.IN.getValue().equals(pipeWaterFactoryMonitorPoint.getDirection())) {
                continue;
            }
            tempObject = new JSONObject();
            tempObject.put("label", pipeWaterFactoryMonitorPoint.getDeviceName());
            tempObject.put("value", pipeWaterFactoryMonitorPoint.getDeviceName());
            resultObject.getJSONArray("titleList").add(tempObject);
        }
        return resultMap.values().stream().collect(Collectors.toList());
    }

    public String check(PipeWaterFactoryMonitorPoint pipeWaterFactoryMonitorPoint) {
        if (StringUtils.isBlank(pipeWaterFactoryMonitorPoint.getFactoryName())) {
            return "请输入水厂名称";
        }

        if (StringUtils.isBlank(pipeWaterFactoryMonitorPoint.getDeviceId())) {
            return "请选择监测点";
        }

        if (StringUtils.isBlank(pipeWaterFactoryMonitorPoint.getDirection())) {
            return "请选择供水类型";
        }
        if (DataConstants.PARTITION_MOUNT_DIRECTION.getByValue(pipeWaterFactoryMonitorPoint.getDirection()) == null) {
            return "请选择正确的供水类型";
        }
        // 是否重复
        Map queryMap = new HashMap();
        queryMap.put("factory_name", pipeWaterFactoryMonitorPoint.getFactoryName());
        queryMap.put("device_id", pipeWaterFactoryMonitorPoint.getDeviceId());
        queryMap.put("tenant_id", pipeWaterFactoryMonitorPoint.getTenantId());
        List list = pipeWaterFactoryMonitorPointMapper.selectByMap(queryMap);
        if (list.size() > 0) {
            return "重复添加";
        }
        return null;
    }
}