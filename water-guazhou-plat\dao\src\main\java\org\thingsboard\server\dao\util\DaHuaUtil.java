package org.thingsboard.server.dao.util;

import com.alibaba.fastjson.JSONObject;
import com.dahuatech.hutool.http.Method;
import com.dahuatech.icc.exception.ClientException;
import com.dahuatech.icc.oauth.http.IClient;
import com.dahuatech.icc.oauth.http.IccHttpHttpRequest;
import com.dahuatech.icc.oauth.http.IccTokenResponse;
import com.dahuatech.icc.oauth.model.v202010.GeneralRequest;
import com.dahuatech.icc.oauth.model.v202010.GeneralResponse;
import com.dahuatech.icc.oauth.model.v202010.OauthPublicKeyResponse;
import com.dahuatech.icc.util.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 大华摄像头控制
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-06-30
 */
@Slf4j
@Component
public class DaHuaUtil {
    @Value("${dahua.host}")
    private String host;

    @Value("${dahua.username}")
    private String username;

    @Value("${dahua.password}")
    private String password;

    @Value("${dahua.clientId}")
    private String clientId;

    @Value("${dahua.clientSecret}")
    private String clientSecret;

    public static JSONObject tokenMap = new JSONObject();

    @Autowired
    @Lazy
    private IClient iClient;

    public String getPublicKey() {
        String url = host + "/evo-apigw/evo-oauth/1.0.0/oauth/public-key";
        IccHttpHttpRequest pubRequest = null;
        try {
            pubRequest = new IccHttpHttpRequest(url, Method.GET);
        } catch (ClientException e) {
            e.printStackTrace();
            log.error(e.getMessage());
            return null;
        }
        String pubBody = pubRequest.execute();
        OauthPublicKeyResponse keyResp = BeanUtil.toBean(pubBody, OauthPublicKeyResponse.class);

        if (!keyResp.isSuccess()) {
            log.error(keyResp.toString());
            return null;
        }
        return keyResp.getData().getPublicKey();
    }

    public String getNoPrefixToken() {
        if (tokenMap.get("expireTime") != null && System.currentTimeMillis() < tokenMap.getLong("expireTime")) {
            return tokenMap.getString("noPrefixToken");
        }
        String token = this.getToken();
        if (StringUtils.isNotBlank(token)) {
            return this.getNoPrefixToken();
        }
        return "大华获取token失败";
    }

    public String getToken() {
        // 判断token是否过期
        if (tokenMap.get("expireTime") != null && System.currentTimeMillis() < tokenMap.getLong("expireTime")) {
            return tokenMap.getString("token");
        }
        IccTokenResponse.IccToken keyResp = iClient.getAccessToken();

        tokenMap.put("token", keyResp.getToken_type() + " " + keyResp.getAccess_token());
        tokenMap.put("noPrefixToken", keyResp.getAccess_token());
        tokenMap.put("expireTime", System.currentTimeMillis() + ((keyResp.getExpires_in() - 20) * 1000L)); // 过期时间
        return keyResp.getAccess_token();
    }

    public String getPreviewURL(String channelId) {
        Map<String, Object> map = new HashMap();
        map.put("channelId", channelId);
        map.put("streamType", "1");
        map.put("type", "hls");
        Map dataMap = new HashMap();
        dataMap.put("data", map);
        JSONObject jsonObject = this.sendPost("/evo-apigw/admin/API/video/stream/realtime", dataMap);

        if (jsonObject.getJSONObject("data") != null && StringUtils.isNotBlank(jsonObject.getJSONObject("data").getString("url"))) {
            return jsonObject.getJSONObject("data").getString("url") + "?token=" + this.getNoPrefixToken();
        }

        return jsonObject.toJSONString();
    }

    public JSONObject sendPost(String url, Map body) {
        JSONObject object = new JSONObject();
        try {
            GeneralRequest generalRequest = new GeneralRequest(url, Method.POST);
            generalRequest.body(JSONObject.toJSONString(body));

            GeneralResponse generalResponse = iClient.doAction(generalRequest, generalRequest.getResponseClass());
            if ("1000".equals(generalResponse.getCode()) || generalResponse.isSuccess()) {
                return JSONObject.parseObject(generalResponse.getResult());
            } else {
                object.put("error", generalResponse);

                return object;
            }
        } catch (Exception e) {
            object.put("error", e.getMessage());
        }
        return object;

    }

    public String getPlaybackURL(String channelId, String startTime, String endTime) {
        Map<String, Object> map = new HashMap();
        map.put("channelId", channelId);
        map.put("streamType", "1");
        map.put("type", "hls");
        map.put("recordType", "1");
        map.put("beginTime", startTime);
        map.put("endTime", endTime);
        map.put("recordSource", "3");
        Map dataMap = new HashMap();
        dataMap.put("data", map);
        JSONObject jsonObject = this.sendPost("/evo-apigw/admin/API/video/stream/record", dataMap);

        if (jsonObject.getJSONObject("data") != null && StringUtils.isNotBlank(jsonObject.getJSONObject("data").getString("url"))) {
            return jsonObject.getJSONObject("data").getString("url") + "?token=" + this.getNoPrefixToken();
        }
        return jsonObject.toJSONString();
    }

    /**
     * 云台控制
     *
     * @param channelId
     * @param params
     * @return
     */
    public String controlling(String channelId, JSONObject params) {
        int action = params.getInteger("action");
        action = action == 0 ? 1 : 0;
        Map<String, Object> map = new HashMap();
        map.put("channelId", channelId);
        map.put("command", action);
        map.put("extend", "");
        String url = "";
        switch (params.getString("command")) {
            case "LEFT": // 左转
                map.put("direct", "3");
                map.put("stepX", "1");
                map.put("stepY", "1");
                url = "/evo-apigw/admin/API/DMS/Ptz/OperateDirect";

                break;
            case "RIGHT": // 右转
                map.put("direct", "4");
                map.put("stepX", "1");
                map.put("stepY", "1");
                url = "/evo-apigw/admin/API/DMS/Ptz/OperateDirect";

                break;
            case "UP": // 上转
                map.put("direct", "1");
                map.put("stepX", "1");
                map.put("stepY", "1");
                url = "/evo-apigw/admin/API/DMS/Ptz/OperateDirect";

                break;
            case "DOWN": // 下转
                map.put("direct", "2");
                map.put("stepX", "1");
                map.put("stepY", "1");
                url = "/evo-apigw/admin/API/DMS/Ptz/OperateDirect";

                break;
            case "ZOOM_IN": // 焦距变大
                map.put("operateType", "1");
                map.put("direct", "1");
                map.put("step", "1");
                url = "/evo-apigw/admin/API/DMS/Ptz/OperateCamera";

                break;
            case "ZOOM_OUT": // 焦距变小
                map.put("operateType", "1");
                map.put("direct", "2");
                map.put("step", "1");
                url = "/evo-apigw/admin/API/DMS/Ptz/OperateCamera";

                break;
            case "LEFT_UP": // 左上

                break;
            case "LEFT_DOWN": // 左下

                break;
            case "RIGHT_UP": // 右上

                break;
            case "RIGHT_DOWN": // 右下

                break;
            case "FOCUS_NEAR": // 焦点前移
                map.put("operateType", "2");
                map.put("direct", "2");
                map.put("step", "1");
                url = "/evo-apigw/admin/API/DMS/Ptz/OperateCamera";

                break;
            case "FOCUS_FAR": // 焦点后移
                map.put("operateType", "2");
                map.put("direct", "1");
                map.put("step", "1");
                url = "/evo-apigw/admin/API/DMS/Ptz/OperateCamera";

                break;
            case "IRIS_ENLARGE ": // 光圈扩大
                map.put("operateType", "3");
                map.put("direct", "1");
                map.put("step", "1");
                url = "/evo-apigw/admin/API/DMS/Ptz/OperateCamera";

                break;
            case "IRIS_REDUCE": // 光圈缩小
                map.put("operateType", "3");
                map.put("direct", "2");
                map.put("step", "1");
                url = "/evo-apigw/admin/API/DMS/Ptz/OperateCamera";

                break;
            case "WIPER_SWITCH": // 接通雨刷开关

                break;
            case "START_RECORD_TRACK": // 开始记录轨迹

                break;
            case "STOP_RECORD_TRACK": // 停止记录轨迹

                break;
            case "START_TRACK": // 开始轨迹

                break;
            case "STOP_TRACK": // 停止轨迹

                break;
        }

        Map dataMap = new HashMap();
        dataMap.put("data", map);
        JSONObject jsonObject = this.sendPost(url, dataMap);

        return jsonObject.toJSONString();
    }

    public List getCamaraList(String name) {
        Map dataMap = new HashMap();
        dataMap.put("pageNum", 1);
        dataMap.put("pageSize", 999);
        JSONObject jsonObject = this.sendPost("/evo-apigw/evo-brm/1.2.0/device/channel/subsystem/page", dataMap);

        if (jsonObject.get("data") != null && jsonObject.getJSONObject("data").get("pageData") != null) {
            return JSONObject.parseObject(JSONObject.toJSONString(jsonObject.getJSONObject("data").get("pageData")), List.class);
        }
        return new ArrayList();
    }
}
