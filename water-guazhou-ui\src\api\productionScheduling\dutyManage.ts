import request from '@/plugins/axios';

/**
 * 值班日志查询
 * @param params
 * @returns
 */
export const getDutyLogs = (params: {
  page: number;
  size: number;
  placeId?: string;
}) =>
  request({
    method: 'get',
    url: `/api/sp/guardRecord`,
    params
  });

/**
 * 分页条件查询
 * @param params
 * @returns
 */
export const guardEventRecord = (params: {
  page: number;
  size: number;
  recordId: string;
}) =>
  request({
    method: 'get',
    url: `/api/sp/guardEventRecord`,
    params
  });

/**
 * 值班事件
 * @param params
 * @returns
 */
export const saveGuardEventRecord = (params: {
  recordId: string;
  content: string;
}) =>
  request({
    method: 'post',
    url: `/api/sp/guardEventRecord`,
    params
  });

// 值班表
/**
 * 分页条件查询
 * @param params
 * @returns
 */
export const getGuardArrange = (params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  placeId: string;
}) =>
  request({
    method: 'get',
    url: `/api/sp/guardArrange`,
    params
  });

/**
 * 值班表成员查询
 * @param params
 * @returns
 */
export const guardArrangePartner = (params: {
  beginTime: string;
  endTime: string;
  arrangeId: string;
}) =>
  request({
    method: 'get',
    url: `/api/sp/guardArrangePartner`,
    params
  });

/**
 * 当前班次
 * @param params
 * @returns
 */
export const getCurrentGuard = (params?: any) =>
  request({
    method: 'get',
    url: '/api/sp/guardArrange/currentGuard',
    data: params
  });
