package org.thingsboard.server.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.thingsboard.server.common.data.DataConstants;

@Configuration
public class RedisKeyConfiguration {

    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private Integer port;

    @Value("${spring.redis.database}")
    private Integer db;

    @Value("${spring.redis.password:}")
    private String password;

    @Bean
    public RedisTemplate redisTemplate() {
        return new RedisTemplate();
    }

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        JedisConnectionFactory factory = new JedisConnectionFactory();
        factory.setHostName(host);
        factory.setPort(port);
        factory.setDatabase(db);
        if (!StringUtils.isEmpty(password)) {
            factory.setPassword(password);
        }
        return factory;
    }

    @Bean
    public RedisTemplate<String, Boolean> redisTemplate(@Autowired RedisConnectionFactory cf) {
        RedisTemplate<String, Boolean> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(cf);
        return redisTemplate;
    }


    @Bean(name = "logicalFlowContinuousRunningNode")
    public BoundHashOperations<String, Object, Object> logicalFlowContinuousRunningNode(@Autowired RedisTemplate<String, Boolean> redisTemplate) {
        return redisTemplate.boundHashOps(DataConstants.LOGICAL_FLOW_CONTINUOUS_RUNNING_NODE_KEY);
    }

    @Bean(name = "logicalFlowRunningNode")
    public BoundHashOperations<String, Object, Object> logicalFlowRunningNode(@Autowired RedisTemplate<String, Boolean> redisTemplate) {
        return redisTemplate.boundHashOps(DataConstants.LOGICAL_FLOW_RUNNING_NODE_KEY);
    }

}
