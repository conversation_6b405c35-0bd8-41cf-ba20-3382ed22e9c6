<!-- 分区监控 -->
<template>
  <DrawerBox
    :right-drawer="true"
    :right-drawer-bar-position="'top'"
  >
    <template #right>
      <SLTree :tree-data="TreeData"></SLTree>
    </template>
    <div class="partition-monitoring">
      <div class="top">
        <div class="left">
          <BasicInfo
            v-if="state.curPartition?.type === '2'"
            :data-dma="state.monitoringData"
          ></BasicInfo>
          <PieCharts
            v-else
            :data="state.monitoringData"
          ></PieCharts>
        </div>

        <ArcLayout
          ref="refArcLayout"
          @map-loaded="onMapLoaded"
        >
        </ArcLayout>
      </div>
      <div class="bottom">
        <TableChart
          v-if="state.curPartition?.type === '1'"
          :data="state.monitoringData"
        ></TableChart>
        <LineBars
          v-else
          :data-dma="state.monitoringData"
        ></LineBars>
      </div>
    </div>
  </DrawerBox>
</template>
<script lang="ts" setup>
import { usePartition } from '@/hooks/arcgis'
import DrawerBox from '@/components/DrawerBox/DrawerBox.vue'
import BasicInfo from './components/BasicInfo.vue'
import LineBars from './components/LineBars.vue'
import PieCharts from './components/PieCharts.vue'
import TableChart from './components/TableChart.vue'
import { GetPartitionMonitoring } from '@/api/mapservice/dma'

const partition = usePartition()
const TreeData = reactive<SLTreeConfig>({
  title: '选择分区',
  data: [],
  isFilterTree: true,
  treeNodeHandleClick: params => {
    console.log(params)

    TreeData.currentProject = params
    refreshData()
    const par = partition.List.value.find(item => item.id === params.id)
    par.geom && partition.extentToPartition(staticState.view, JSON.parse(par.geom))
  }
})
const state = reactive<{
  monitoringData: any
  curPartition: any
}>({
  monitoringData: {},
  curPartition: {}
})
const staticState: {
  view?: __esri.MapView
  partitionLayer?: __esri.GraphicsLayer
  curPartitionLayer?: __esri.GraphicsLayer
} = {}
const refreshData = () => {
  if (!TreeData.currentProject) return
  const par = partition.List.value.find(item => item.id === TreeData.currentProject.value)
  state.curPartition = par
  GetPartitionMonitoring({ partitionId: TreeData.currentProject?.value })
    .then(res => {
      state.monitoringData = res.data?.data || {}
    })
    .catch(() => {
      //
    })
}
const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view
  await partition.getTree()
  TreeData.data = partition.Tree.value
  TreeData.currentProject = TreeData.data?.[0]
  await partition.refreshPartitions(staticState.view)
  refreshData()
}
</script>
<style lang="scss" scoped>
.partition-monitoring {
  width: 100%;
  height: 100%;
  background-color: var(--el-bg-color);
}
.top {
  height: calc(100% - 400px);
  width: 100%;
  display: flex;
  .left {
    width: 600px;
    height: 100%;
  }
  .right {
    width: calc(100% - 600px);
    min-width: 450px;
    height: 100%;
  }
}
.bottom {
  height: 400px;
  display: flex;
  .right {
    width: 40%;
    height: 100%;
  }
  .left {
    width: 60%;
    height: 100%;
  }
}
</style>
