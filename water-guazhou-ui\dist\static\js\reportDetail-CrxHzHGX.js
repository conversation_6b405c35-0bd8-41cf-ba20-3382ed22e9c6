import{d as F,c as u,r as p,l as s,bJ as I,bI as d,am as T,o as K,bo as B,i,g as G,h as V,F as D,q as m,aq as $,br as J,C as M}from"./index-r0dFAfgr.js";import{_ as U}from"./TreeBox-DDD2iwoR.js";import{_ as j}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as H}from"./CardTable-rdWOL4_6.js";import{_ as O}from"./Search-NSrhrIa_.js";import{_ as Y}from"./index-BJ-QPYom.js";import"./index-0NlGN6gS.js";import{p as N}from"./printUtils-C-AxhDcd.js";import{b as z,G as A}from"./proSale-DWRhGXcG.js";import"./index-C9hz-UZb.js";const E=F({__name:"reportDetail",props:{partitions:{},tree:{}},setup(C){const y=C,b=u(),g=u(),k=u(),W=u(),v=u(),l=p({data:y.tree||[],title:"区域划分",showCheckbox:!0,checkedNodes:[],checkedKeys:[],handleCheck:(e,t)=>{console.log(t.checkedNodes,t.checkedKeys),l.checkedKeys=t.checkedKeys||[],l.checkedNodes=t.checkedNodes||[],f()}}),h=(e,t,o)=>{o.hidden=e.type!==o.field},S=p({defaultParams:{type:"year",year:s().format(I),month:[s().format(d),s().format(d)]},filters:[{type:"radio-button",field:"type",options:[{label:"按年",value:"year"},{label:"按年月",value:"month"}],label:"选择方式"},{handleHidden:h,type:"year",label:"",field:"year",clearable:!1,disabledDate(e){return new Date<e}},{handleHidden:h,type:"monthrange",label:"",field:"month",clearable:!1,format:d,disabledDate(e){return new Date<e}},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>f()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var e;(e=b.value)==null||e.resetForm()}},{perm:!0,text:"打印",type:"default",plain:!0,iconifyIcon:"ep:printer",click:()=>{N({title:"差销差报表详情",data:n.dataList,titleList:n.columns})}},{perm:!0,text:"导出",type:"primary",iconifyIcon:"ep:download",click:()=>{var e;(e=v.value)==null||e.exportTable()}}]}]}),n=p({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"partitionName",label:"分区名称",minWidth:120},{prop:"correctNrwRate",label:"校准产销差",unit:"(%)",align:"center",minWidth:120},{prop:"supplyTotal",label:"供水总量",unit:"(m³)",align:"center",minWidth:120},{prop:"correctUseWater",label:"校准用水量",unit:"(m³)",align:"center",minWidth:120},{prop:"userNum",label:"挂接用户数",unit:"(户)",align:"center",minWidth:120},{prop:"inWater",label:"进水量",unit:"(m³)",align:"center",minWidth:120},{prop:"outWater",label:"出水量",unit:"(m³)",align:"center",minWidth:120},{prop:"noIncomeWater",label:"无收益水量",unit:"(m³)",align:"center",minWidth:120}],operations:[{perm:!0,type:"default",isTextBtn:!1,plain:!0,text:"详情",iconifyIcon:"ep:more-filled",click:e=>{var t;n.currentRow=e,(t=W.value)==null||t.openDialog(),w()}}],operationWidth:"150px",pagination:{hide:!0,refreshData:({page:e,size:t})=>{n.pagination.page=e,n.pagination.limit=t,f()}}}),L=p({dialogWidth:"70%",title:"详情",group:[]}),x=p({defaultParams:{type:"year",year:s().format(I),month:[s().format(d),s().format(d)]},filters:[{type:"radio-button",field:"type",options:[{label:"按年",value:"year"},{label:"按年月",value:"month"}],label:"选择方式"},{handleHidden:h,type:"year",label:"",field:"year",clearable:!1,disabledDate(e){return new Date<e}},{handleHidden:h,type:"monthrange",label:"",field:"month",clearable:!1,format:d,disabledDate(e){return new Date<e}},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>w()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var e;(e=g.value)==null||e.resetForm()}},{perm:!0,text:"打印",type:"default",plain:!0,iconifyIcon:"ep:printer",click:()=>{N({title:"差销差报表详情",data:c.dataList,titleList:c.columns})}},{perm:!0,text:"导出",type:"primary",iconifyIcon:"ep:download",click:()=>{var e;(e=k.value)==null||e.exportTable()}}]}]}),c=p({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"partitionId",label:"日期"},{prop:"partitionName",label:"区域名称",align:"center",minWidth:120},{prop:"correctNrwRate",label:"参考产销差",unit:"(%)",align:"center",minWidth:120},{prop:"supplyTotal",label:"供水总量",unit:"(m³)",align:"center",minWidth:120},{prop:"correctUseWater",label:"参考用户抄见量",unit:"(m³)",align:"center",minWidth:150},{prop:"userNum",label:"挂接用户数",unit:"(户)",align:"center",minWidth:140},{prop:"inWater",label:"进水量",unit:"(m³)",align:"center",minWidth:120},{prop:"outWater",label:"出水量",unit:"(m³)",align:"center",minWidth:120},{prop:"noIncomeWater",label:"无收益水量",unit:"(m³)",align:"center",minWidth:150}],pagination:{hide:!0}}),f=async()=>{var e,t,o,a;try{n.loading=!0;const r=((e=b.value)==null?void 0:e.queryParams)||{},_=await z({type:r.type,date:r.type==="year"?r.year:void 0,start:r.type==="month"?(t=r.month)==null?void 0:t[0]:void 0,end:r.type==="month"?(o=r.month)==null?void 0:o[1]:void 0,partitionIds:(a=l.checkedKeys)==null?void 0:a.join(",")});n.dataList=_.data.data||[]}catch{}n.loading=!1},w=async()=>{var e,t,o;if(n.currentRow){try{c.loading=!0;const a=((e=g.value)==null?void 0:e.queryParams)||x.defaultParams||{},r=await A({type:a.type,date:a.type==="year"?a.year:void 0,partitionId:n.currentRow.partitionId,start:a.type==="month"?(t=a.month)==null?void 0:t[0]:void 0,end:a.type==="month"?(o=a.month)==null?void 0:o[1]:void 0});c.dataList=r.data.data||[]}catch{}c.loading=!1}};return T(()=>y.tree,()=>{l.data=y.tree||[]}),T(()=>y.partitions,()=>{f()}),K(async()=>{f()}),(e,t)=>{const o=Y,a=O,r=H,_=$,q=j,P=U,R=J;return B((G(),V(P,null,{tree:D(()=>[m(o,{ref:"refTree","tree-data":i(l)},null,8,["tree-data"])]),default:D(()=>[m(a,{ref_key:"refSearch",ref:b,config:i(S),class:"search"},null,8,["config"]),m(r,{ref_key:"refTable",ref:v,class:"card-table",config:i(n)},null,8,["config"]),m(q,{ref_key:"refDialog",ref:W,config:i(L)},{default:D(()=>[m(a,{ref_key:"refSearch_Detail",ref:g,config:i(x),class:"search"},null,8,["config"]),m(_,{ref_key:"refTable_Detail",ref:k,config:i(c),class:"form-table"},null,8,["config"])]),_:1},8,["config"])]),_:1})),[[R,!!i(l).loading]])}}}),le=M(E,[["__scopeId","data-v-7bafb63c"]]);export{le as default};
