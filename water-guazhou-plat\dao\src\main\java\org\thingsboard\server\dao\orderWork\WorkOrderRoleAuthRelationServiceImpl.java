package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderRoleAuthRelation;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderRoleAuthRelationMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkOrderRoleAuthRelationServiceImpl implements WorkOrderRoleAuthRelationService {

    @Autowired
    private WorkOrderRoleAuthRelationMapper workOrderRoleAuthRelationMapper;

    @Override
    public List<String> findAuthDeptList(List<String> roleIds, List<String> authTypes) {
        QueryWrapper<WorkOrderRoleAuthRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", roleIds).in("auth_type", authTypes);

        List<WorkOrderRoleAuthRelation> relations = workOrderRoleAuthRelationMapper.selectList(queryWrapper);

        return relations.stream().map(WorkOrderRoleAuthRelation::getDeptId).collect(Collectors.toList());
    }

    @Override
    public void save(String roleId, String authType, List<String> deptList) {
        // 删除已有的授权部门
        QueryWrapper<WorkOrderRoleAuthRelation> deleteQueryWrapper = new QueryWrapper<>();
        deleteQueryWrapper.eq("role_id", roleId).eq("auth_type", authType);
        workOrderRoleAuthRelationMapper.delete(deleteQueryWrapper);

        // 保存
        List<WorkOrderRoleAuthRelation> insertList = new ArrayList<>();
        for (String deptId : deptList) {
            WorkOrderRoleAuthRelation relation = new WorkOrderRoleAuthRelation();
            relation.setRoleId(roleId);
            relation.setAuthType(authType);
            relation.setDeptId(deptId);
            insertList.add(relation);
        }

        workOrderRoleAuthRelationMapper.batchInsert(insertList);
    }
}
