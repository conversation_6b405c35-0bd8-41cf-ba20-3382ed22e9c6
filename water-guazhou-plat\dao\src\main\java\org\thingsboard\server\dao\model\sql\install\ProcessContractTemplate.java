package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 合同模板
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_install_process_contract_template")
@Data
public class ProcessContractTemplate {
    @TableId
    private String id;

    private String mainId;

    private String name;

    private String file;

    private String remark;

    private String tenantId;

}
