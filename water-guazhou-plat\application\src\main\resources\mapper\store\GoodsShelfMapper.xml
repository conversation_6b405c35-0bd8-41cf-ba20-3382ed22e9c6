<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.GoodsShelfMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           parent_id,
                           code,
                           "name",
                           order_num,
                           remark,
                           create_time,
                           tenant_id<!--@sql from goods_shelf -->
    </sql>

    <sql id="Parent_Column_List">
        <!--@sql select -->id,
                           parent_id,
                           code,
                           "name",
                           order_num,
                           remark,
                           create_time,
                           tenant_id,
                           goods_shelf_get_parent_name(parent_id) as parent_name,
                           2                                      as layer
        <!--@sql from goods_shelf -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.store.GoodsShelf">
        <result column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="order_num" property="orderNum"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findChild" resultType="org.thingsboard.server.dao.model.sql.store.GoodsShelf">
        select
        <include refid="Parent_Column_List"/>
        from goods_shelf
        where parent_id = #{parentId}
        order by order_num
    </select>

    <update id="update">
        update goods_shelf
        <set>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getNameById" resultType="java.lang.String">
        select name
        from goods_shelf
        where id = #{id}
    </select>

    <select id="getCode" resultType="java.lang.String">
        select code
        from goods_shelf
        where id = #{id}
    </select>

    <select id="isCodeExists" resultType="boolean">
        select goods_shelf_is_code_exists(#{code}, #{id}, #{tenantId})
    </select>

    <select id="canBeDelete" resultType="boolean">
        <!--当前货架或子货架上没有设备-->
        select count(1) = 0
        from device_storage_journal storage
                 join (with recursive temp(id) as (select #{id}::varchar
                                                   union all
                                                   select shelf.id
                                                   from goods_shelf shelf,
                                                        temp
                                                   where shelf.parent_id = temp.id)
                       select temp.id
                       from temp) idList
                      on storage.shelves_id = idlist.id
    </select>

    <delete id="deleteWithChildrenRecursive">
        delete
        from goods_shelf
            using (with recursive temp(id) as (select #{id}::varchar
                                               union all
                                               select shelf.id
                                               from goods_shelf shelf,
                                                    temp
                                               where shelf.parent_id = temp.id)
                   select temp.id
                   from temp) idList
        where goods_shelf.id = idList.id
    </delete>
</mapper>