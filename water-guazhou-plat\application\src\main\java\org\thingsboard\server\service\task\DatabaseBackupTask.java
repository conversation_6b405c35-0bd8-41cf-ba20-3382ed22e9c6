package org.thingsboard.server.service.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.dataBackup.DataBackupService;
import org.thingsboard.server.dao.model.sql.DataBackup;

/**
 * OPENTSDB备份
 */
@Slf4j
@Component
public class DatabaseBackupTask {

    @Autowired
    private DataBackupService dataBackupService;

    @Scheduled(cron = "0 0 3 1/7 * ?")
    public void backup() {
        try {
            DataBackup dataBackup = getDataBackup();
            dataBackupService.backup(dataBackup);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private DataBackup getDataBackup() {
        DataBackup dataBackup = new DataBackup();
        dataBackup.setCreateTime(System.currentTimeMillis());
        dataBackup.setType(DataConstants.DATA_BACKUP_AUTO);
        dataBackup.setCreateBy("自动备份");
        dataBackup.setCreater("自动备份");
        return dataBackup;
    }

}
