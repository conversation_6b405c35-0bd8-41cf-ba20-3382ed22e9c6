import{d as D,c as d,r as l,a8 as f,x as c,a9 as v,g as k,n as L,q as s,i as n,F as O,aq as E,b6 as F,C as V}from"./index-r0dFAfgr.js";import{_ as q}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{C as B,D as M}from"./manage-BReaEVJk.js";import{f as w}from"./DateFormatter-Bm9a68Ax.js";import N from"./detail-DEo1RlcF.js";import{S as u,T as z,P}from"./data-Dv9-Tstw.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./projectManagement-CDcrrCQ1.js";import"./xmwcqk-Cxfq91Sa.js";import"./CardTable-rdWOL4_6.js";import"./index-C9hz-UZb.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const S={class:"team_table"},Y=D({__name:"detail",props:{config:{}},emits:["extendedReturn"],setup(g,{emit:b}){const p=d(),m=d(),y=g,x=b,_=l({loading:!1,indexVisible:!0,dataList:f(()=>y.config.items),columns:[{prop:"contractName",label:"所属合同"},{prop:"contractTypeName",label:"合同类别"},{prop:"payeeOrganization",label:"收款单位"},{prop:"contractCost",label:"合同金额(万元)"},{prop:"cost",label:"支付金额(万元)"},{prop:"type",label:"费用类型"},{prop:"remark",label:"说明"},{prop:"creatorName",label:"创建人"},{prop:"createTime",label:"创建时间",formatter:e=>w(e.createTime,"YYYY-MM-DD HH:mm:ss")},{prop:"status",label:"工作状态",tag:!0,tagColor:()=>{var e;return((e=u.find(t=>t.value==="COMPLETED"))==null?void 0:e.color)||""},formatter:()=>{var e;return(e=u.find(t=>t.value==="COMPLETED"))==null?void 0:e.label}}],operationWidth:"200px",operations:[{isTextBtn:!1,type:"success",text:"编辑",perm:!0,click:e=>T(e)},{isTextBtn:!1,text:"详情",perm:!0,click:e=>{var t;a.selected=e,(t=m.value)==null||t.openDrawer()}}],pagination:{hide:!0}}),o=l({title:"编辑签证",appendToBody:!0,labelWidth:"150px",dialogWidth:"1000px",submitting:!1,submit:e=>{o.submitting=!0;let t="新增";e.id&&(t="修改"),e.pipLengthDesign=JSON.stringify(e.pipLengthDesign),B(e).then(i=>{var r;o.submitting=!1,i.data.code===200?(c.success(t+"成功"),(r=p.value)==null||r.closeDialog()):c.warning(t+"失败"),x("extendedReturn",{})}).catch(i=>{o.submitting=!1,c.warning(i)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"资金编号",field:"code",disabled:!0},{xs:12,type:"select",label:"费用类型",field:"type",rules:[{required:!0,message:"请选择费用类型"}],options:z},{xs:12,type:"select",label:"所属合同名称",field:"contractCode",options:f(()=>a.contractList)},{xs:12,type:"number",label:"合同金额(万元)",field:"contractTotalCost",readonly:!0},{xs:12,type:"select",label:"支付方式",field:"paymentType",options:P},{xs:12,type:"number",label:"金额(万元)",field:"cost",rules:[{required:!0,message:"请输入金额"}],min:0},{xs:12,type:"date",label:"报批时间",field:"approvalTime",format:"x",rules:[{required:!0,message:"请输入报批时间"}]},{xs:12,type:"date",label:"提交财务处理时间",field:"submitFinanceTime",format:"x",rules:[{required:!0,message:"请输入提交财务处理时间"}]},{xs:12,type:"input",label:"一审审核金额(万元)",field:"firstVerifyCost"},{xs:12,type:"input",label:"一审结果单位",field:"firstVerifyOrganization"},{xs:12,type:"input",label:"二审审核金额(万元)",field:"secondVerifyCost"},{xs:12,type:"input",label:"二审结果单位",field:"secondVerifyOrganization"},{xs:12,type:"input",label:"代收款信息",field:"payeeInfo"},{xs:12,type:"input",label:"收款单位",field:"payeeOrganization"},{type:"textarea",label:"说明",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),C=l({title:"详情",group:[],width:"80%",modalClass:"lightColor",appendToBody:!0,cancel:!1}),a=l({selected:{},contractList:[],getConstructionContract:e=>{M(e).then(t=>{a.contractList=v(t.data.data.data||[],"children",{label:"name",value:"code"})})}}),T=e=>{var t;o.title="编辑费用明细",o.defaultValue={...e||{},contractTotalCost:e.contractCost},a.getConstructionContract(e.constructionCode),(t=p.value)==null||t.openDialog()};return(e,t)=>{const i=E,r=q,h=F;return k(),L("div",S,[s(i,{config:n(_)},null,8,["config"]),s(r,{ref_key:"refForm",ref:p,class:"dialogForm",config:n(o)},null,8,["config"]),s(h,{ref_key:"refDetail",ref:m,config:n(C)},{default:O(()=>[s(N,{config:n(a).selected,show:8},null,8,["config"])]),_:1},8,["config"])])}}}),_e=V(Y,[["__scopeId","data-v-2d26f4ac"]]);export{_e as default};
