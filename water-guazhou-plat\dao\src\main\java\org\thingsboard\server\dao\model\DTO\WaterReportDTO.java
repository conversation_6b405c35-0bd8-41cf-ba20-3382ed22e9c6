package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 水质报表DTO
 */
@Data
public class WaterReportDTO {

    private String stationId;

    private String stationName;

    private String timeStr;

    private BigDecimal codIn;

    private BigDecimal codInData;

    private BigDecimal bodIn;

    private BigDecimal bodInData;

    private BigDecimal nh3In;

    private BigDecimal nh3InData;

    private BigDecimal tpIn;

    private BigDecimal tpInData;

    private BigDecimal tnIn;

    private BigDecimal tnInData;

    private BigDecimal phIn;

    private BigDecimal phInData;

    private BigDecimal codOut;

    private BigDecimal codOutData;

    private BigDecimal bodOut;

    private BigDecimal bodOutData;

    private BigDecimal nh3Out;

    private BigDecimal nh3OutData;

    private BigDecimal tpOut;

    private BigDecimal tpOutData;

    private BigDecimal tnOut;

    private BigDecimal tnOutData;

    private BigDecimal phOut;

    private BigDecimal phOutData;

}
