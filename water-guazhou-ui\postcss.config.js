const path = require('path');

/**
 * 判断文件是否在大屏相关目录下
 * 只有在views/cockpit/目录下的文件才会被处理
 * @param {string} file - 文件路径
 * @returns {boolean} - 是否在大屏目录下
 */
function isBigScreenFile(file) {
  // 确保文件路径存在
  if (!file) return false;
  
  // 只处理大屏相关目录 - cockpit目录
  // 使用更严格的正则表达式匹配，确保只匹配views/cockpit/路径
  console.log(file,/[\\/]views[\\/]cockpit[\\/]/.test(file))
  return /[\\/]views[\\/]cockpit[\\/]/.test(file);
}

module.exports = {
  plugins: {
    'postcss-px-to-viewport': {
      viewportWidth: 1920, // 设计稿宽度
      unitPrecision: 3,
      viewportUnit: 'vw',
      selectorBlackList: [], // 不需要转换的选择器
      minPixelValue: 1, // 小于1px的不转换
      mediaQuery: false, // 是否允许媒体查询中转换px
      include: [/[\\/]bigScreen[\\/]/],
      exclude: [] // 不需要转换的文件
    }
  }
}; 