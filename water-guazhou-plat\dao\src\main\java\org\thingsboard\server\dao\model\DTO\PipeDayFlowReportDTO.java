package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 流量日报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-22
 */
@Data
public class PipeDayFlowReportDTO {

    private String partitionType; // 分区类型

    private String partitionName; // 分区名称

    private BigDecimal dayFlow = BigDecimal.ZERO; // 本日流量

    private BigDecimal dayFlowCompareToLastDayRate = BigDecimal.ZERO; // 环比昨日

    private BigDecimal dayChangeRate = BigDecimal.ZERO; // 日增减率

    private BigDecimal dayFlowCompareToLastYearRate = BigDecimal.ZERO; // 日流量同比去年

    private BigDecimal tenDaysFlow = BigDecimal.ZERO; // 本旬累计

    private BigDecimal tenDaysFlowCompareToLastTenDaysRate = BigDecimal.ZERO; // 环比上旬

    private BigDecimal tenDaysChangeRate = BigDecimal.ZERO; // 旬增减率

    private BigDecimal tenDaysFlowCompareToLastYearRate = BigDecimal.ZERO; // 同比去年

    private BigDecimal monthFlow = BigDecimal.ZERO; // 本月累计

    private BigDecimal monthFlowCompareToLastMonthRate = BigDecimal.ZERO; // 环比上月

    private BigDecimal monthChangeRate = BigDecimal.ZERO; // 月增减率

    private BigDecimal monthFlowCompareToLastYearRate = BigDecimal.ZERO; // 同比去年

    private BigDecimal yearFlow = BigDecimal.ZERO; // 本年累计

    private BigDecimal yearFlowCompareToLastYearRate = BigDecimal.ZERO; // 同比去年

    private BigDecimal dayFlowDivideParentRate = BigDecimal.ZERO; // 日流量占比上级流量

    private BigDecimal dayFlowDivideParentCompareToLastDayRate = BigDecimal.ZERO; // 日流量占比环比昨日

    private BigDecimal dayFlowDivideParentCompareToLastYearRate = BigDecimal.ZERO; // 日流量占比同比去年

    private BigDecimal tenDaysFlowDivideParentRate = BigDecimal.ZERO; // 旬流量占比上级流量

    private BigDecimal tenDaysFlowDivideParentCompareToLastTenDaysRate = BigDecimal.ZERO; // 旬流量占比环比昨日

    private BigDecimal tenDaysFlowDivideParentCompareToLastYearRate = BigDecimal.ZERO; // 旬流量占比同比去年

    private BigDecimal monthFlowDivideParentRate = BigDecimal.ZERO; // 月流量占比上级流量

    private BigDecimal monthFlowDivideParentCompareToLastMonthRate = BigDecimal.ZERO; // 月流量占比环比昨日

    private BigDecimal monthFlowDivideParentCompareToLastYearRate = BigDecimal.ZERO; // 月流量占比同比去年

    private BigDecimal yearFlowDivideParentRate = BigDecimal.ZERO; // 年流量占比上级流量

    private BigDecimal yearFlowDivideParentCompareToLastYearRate = BigDecimal.ZERO; // 年流量占比同比去年

}
