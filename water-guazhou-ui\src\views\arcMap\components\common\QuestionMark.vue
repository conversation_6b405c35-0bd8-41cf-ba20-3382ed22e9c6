<template>
  <div>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </div>
</template>
<script lang="ts" setup>
import { Delete } from '@element-plus/icons-vue'
import Graphic from '@arcgis/core/Graphic'
import Point from '@arcgis/core/geometry/Point'
import { createEllipse, createPoint, createPolygon, createPolyline, createRectGraphic, createTextDraw, extentTo, getGraphicLayer, initDrawer, setMapCursor, setSymbol } from '@/utils/MapHelper'

const props = defineProps<{
  view?: __esri.MapView
  telport?: string
}>()
const state = reactive<{
  curType: string
}>({
  curType: ''
})
const staticState:{
  graphicsLayer?:__esri.GraphicsLayer
  graphics?: __esri.Graphic
  drawer?: __esri.Draw
  drawAction?: __esri.DrawAction
  textArea?: HTMLTextAreaElement
  vertices?: number[][]
} = {

}
const TableConfig_QuetionMarks = reactive<ITable>({
  singleSelect: true,
  select: (row, isChecked) => {
    TableConfig_QuetionMarks.selectList = isChecked ? [row] : []
  },
  handleSelectChange: rows => {
    TableConfig_QuetionMarks.selectList = rows
  },
  handleRowDbClick: async row => {
    await extentTo(props.view, row.extent)
  },
  dataList: [],
  columns: [
    { label: '名称', prop: 'name' },
    { label: '创建日期', prop: 'createDate' },
    { label: '状态', prop: 'status' }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig_QuetionMarks.pagination.page = page
      TableConfig_QuetionMarks.pagination.limit = size
      refreshTable()
    },
    layout: 'total, sizes, jumper'
  }
})

const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '添加疑问标识'
      },
      fields: [
        {
          type: 'btn-group',
          label: '绘制工具：',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('ellipse')
            },
            {
              perm: false,
              text: '',
              type: 'default',
              size: 'large',
              iconifyIcon: 'mdi:arrow-top-right-bold-outline',
              click: () => {
                console.log('a')
              }
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              iconifyIcon: 'mdi:chart-timeline-variant',
              click: () => initDraw('polyline')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              iconifyIcon: 'material-symbols:text-fields',
              click: () => initDraw('text')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        },
        {
          type: 'input',
          label: '标识名称：',
          field: 'name',
          placeholder: '请输入当前标识名称'
        },
        {
          type: 'textarea',
          field: 'content',
          label: '标识内容：',
          placeholder: '请输入当前标识内容'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              type: 'success',
              text: '保存疑问标识',
              click: () => {
                //
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '我的疑问标识'
      },
      fields: [
        {
          type: 'table',
          style: {
            height: '400px'
          },
          config: TableConfig_QuetionMarks
        },
        {
          type: 'btn-group',
          style: {
            marginTop: '15px'
          },
          btns: [
            { perm: true,
              type: 'danger',
              styles: {
                width: '100%'
              },
              text: '删除选定的疑问标识',
              click: () => {
              //
              } }
          ]
        }
      ]
    }
  ]
})
const initDraw = (type:any) => {
  if (!props.view) return
  state.curType = type
  type = type === 'text' ? 'point' : type
  setMapCursor('crosshair')
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
  staticState.drawer = initDrawer(props.view)
  staticState.drawAction = staticState.drawer?.create(type, {
    mode: ['ellipse', 'polyline'].indexOf(type) !== -1 ? 'freehand' : 'click'
  })
  if (state.curType === 'text') {
    staticState.drawAction?.on('draw-complete', e => {
      staticState.vertices = e.vertices
      if (e.vertices.length) {
        const mappoint = props.view?.toScreen(new Point({
          x: e.vertices[0][0],
          y: e.vertices[0][1],
          spatialReference: props.view.spatialReference
        }))
        staticState.textArea = createTextDraw(mappoint, props.telport)
        staticState.textArea.addEventListener('blur', handleTextAreaBlur)
      }

      // props.view?.on('drag', e => {
      //   const mappoint = props.view?.toScreen(new Point({ x: e.x, y: e.y, spatialReference: props.view.spatialReference }))
      //   if (mappoint && textarea) {
      //     textarea.style.top = mappoint.y + 'px'
      //     textarea.style.left = mappoint.x + 'px'
      //   }
      // })
      setMapCursor('')
    })
  } else {
    staticState.drawAction?.on(['vertex-add', 'cursor-update'], updateVertices)
    staticState.drawAction?.on('draw-complete', e => {
      updateVertices(e)
      setMapCursor('')
    })
  }
}
const handleTextAreaBlur = () => {
  if (!staticState.vertices?.length) return
  const text = staticState.textArea?.value || ''
  if (staticState.textArea) {
    if (!props.telport) return
    const mapContainer = document.querySelector(props.telport)
    staticState.textArea.removeEventListener('blur', handleTextAreaBlur)
    mapContainer?.removeChild(staticState.textArea)
  }
  if (text) {
    const textGraphic = new Graphic({
      geometry: new Point({ x: staticState.vertices[0][0], y: staticState.vertices[0][1], spatialReference: props.view?.spatialReference }),
      symbol: setSymbol('text', {
        text,
        textColor: '#ff0000',
        yOffset: -8
      })
    })
    staticState.graphicsLayer?.add(textGraphic)
  }
}
const updateVertices = e => {
  const type = state.curType
  console.log(e)

  const graphic = type === 'ellipse' ? createEllipse(e.vertices, props.view?.spatialReference)
    : type === 'rectangle' ? createRectGraphic(e)
      : type === 'polyline'
        ? createPolyline(e.vertices, props.view?.spatialReference)
        : type === 'polygon'
          ? e.vertices.length < 3
            ? createPolyline(e.vertices, props.view?.spatialReference)
            : createPolygon(e.vertices, props.view?.spatialReference)
          : createPoint(e.vertices, props.view?.spatialReference)
  staticState.graphicsLayer?.removeAll()
  graphic && staticState.graphicsLayer?.add(graphic)
  staticState.graphics = graphic
}
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphics = undefined
}
const refreshTable = () => {
//
}
onMounted(() => {
  if (!props.view) return
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'question-mark-layer',
    title: '疑问标识'
  })
})
onBeforeUnmount(() => {
  staticState.graphicsLayer && props.view?.map.remove(staticState.graphicsLayer)
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
})
</script>
<style lang="scss" scoped></style>
