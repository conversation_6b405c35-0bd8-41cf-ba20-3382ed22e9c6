import{两 as _}from"./charts-CdlVhAbT.js";import{g as d}from"./smartDecisionData-BKd4shVG.js";import i from"./SupplyItem-Ba6JtLer.js";import{d as f,r as v,ay as g,g as t,n as o,p as e,aB as u,aJ as c,h as p,i as a,q as h,C as y}from"./index-r0dFAfgr.js";const b={class:"gsl-wrapper"},C={class:"supply-items left"},I={class:"supply-items right"},k={class:"chart"},B=f({__name:"GSL",setup(V){const r=v({leftItems:[{label:"今日供水量",unit:"万m³",status:"down",value:"1.44",scale:"11.17%"},{label:"昨日供水量",unit:"万m³",value:"2.64"},{label:"本月供水量",unit:"万m³",value:"813.92",status:"up",scale:"20.9%"}],rightItems:[{label:"礼辛水厂",unit:"万m³",status:"up",value:"9.95",scale:"1.9%"},{label:"二水厂",unit:"万m³",status:"up",value:"2.64",scale:"4.1%"},{label:"三水厂",unit:"万m³",value:"813.92",status:"down",scale:"20.9%"}]});return(w,s)=>{const m=g("VChart");return t(),o("div",b,[e("div",C,[(t(!0),o(u,null,c(a(r).leftItems,(l,n)=>(t(),p(i,{key:n,class:"supply-item",config:l},null,8,["config"]))),128))]),e("div",I,[(t(!0),o(u,null,c(a(r).rightItems,(l,n)=>(t(),p(i,{key:n,class:"supply-item",config:l},null,8,["config"]))),128))]),s[0]||(s[0]=e("div",{class:"center_item total"}," 总供水量 ",-1)),s[1]||(s[1]=e("div",{class:"center_item plant"}," 水厂供水量 ",-1)),e("div",k,[h(m,{ref:"refChart",option:a(_)(a(d))},null,8,["option"])])])}}}),N=y(B,[["__scopeId","data-v-9209e58a"]]);export{N as default};
