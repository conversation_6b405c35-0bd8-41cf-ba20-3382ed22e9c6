package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.area.AreaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.*;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class RestDeviceStorageJournal {
    // id
    private String id;

    // 设备标签
    private String deviceLabelCode;

    // 入库单编码
    private String storeInCode;

    // 是否已出库
    @Compute("isCheckout")
    private Boolean isCheckout;

    // 所在仓库
    private String storehouseId;

    // 所在仓库code
    private String storehouseCode;

    // 所在仓库名
    private String storehouseName;

    // 对应的出库到的出库单条目
    private String storeOutItemId;

    // 供应商
    private String supplierId;

    // 供应商名称
    private String supplierName;

    // 安装区域Id
    @InfoViaMapper(name = "treePath", mapper = AreaMapper.class)
    private String installAddressId;

    // 安装区域名称
    private String installAddressName;

    // 安装地址
    private String actualAddress;

    // 最后保养时间
    private Date lastMaintainanceTime;

    // 最后巡检时间
    private Date lastInspectionTime;

    // 租户ID
    private String tenantId;

    @Flatten
    @TableField(exist = false)
    private DeviceInfoResponse deviceInfoResponse;

    @SuppressWarnings("unused")
    private Boolean isCheckout() {
        return storeOutItemId != null;
    }
}
