/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server;


import akka.actor.FSM;
import com.alibaba.fastjson.JSONObject;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.jasypt.encryption.StringEncryptor;
import org.jboss.netty.handler.codec.base64.Base64Decoder;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.thingsboard.rule.engine.api.MailService;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.common.data.alarm.AlarmReport;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest
public class MyTest {

    @Autowired
    MailService mailService ;


    @Test
    public void name() {
        String token = "eyJ1c2VybmFtZSI6Imppbmd5dWFuQGFkbWluLmNvbSIsInRiSldUIjoiZXlKaGJHY2lPaUpJVXpVeE1pSjkuZXlKemRXSWlPaUpxYVc1bmVYVmhia0JoWkcxcGJpNWpiMjBpTENKelkyOXdaWE1pT2xzaVZFVk9RVTVVWDBGRVRVbE9JbDBzSW5WelpYSkpaQ0k2SW1Nek9EZzJNell3TFdabU5ESXRNVEZsT0MxaVlXWTNMVE5pTjJRM01UZ3paR0UxTXlJc0ltWnBjbk4wVG1GdFpTSTZJdWFadHVpTGtlV2J2ZW1aaFY5QlJFMUpUaUlzSW14aGMzUk9ZVzFsSWpvaTVwbTI2SXVSNVp1OTZabUZYMEZFVFVsT0lpd2laVzVoWW14bFpDSTZkSEoxWlN3aWFYTlFkV0pzYVdNaU9tWmhiSE5sTENKd2FHOXVaU0k2SWlJc0luUmxibUZ1ZEVsa0lqb2lNV0ZrWVRKaFpqQXRabVkwTWkweE1XVTRMV0poWmpjdE0ySTNaRGN4T0ROa1lUVXpJaXdpWTNWemRHOXRaWEpKWkNJNklqRXpPREUwTURBd0xURmtaREl0TVRGaU1pMDRNRGd3TFRnd09EQTRNRGd3T0RBNE1DSXNJbWx6Y3lJNkluUm9hVzVuYzJKdllYSmtMbWx2SWl3aWFXRjBJam94TlRnNE1EUTRNREU0TENKbGVIQWlPakUyTnpnd05EZ3dNVGg5LkZJT2NkSUl1U2d3TktSeHF0NDBXN2xzV1ZlVWtXV0ZMTjBBbi1fdHdmNG1VS1F0THJWLUhpb3l2WTg2TXppQjY0bUdTd0tmazBKUktFZU5sTVZ0NkdRIiwidGJVc2VyIjp7InN1YiI6Imppbmd5dWFuQGFkbWluLmNvbSIsInNjb3BlcyI6WyJURU5BTlRfQURNSU4iXSwidXNlcklkIjoiYzM4ODYzNjAtZmY0Mi0xMWU4LWJhZjctM2I3ZDcxODNkYTUzIiwiZmlyc3ROYW1lIjoi5pm26IuR5Zu96ZmFX0FETUlOIiwibGFzdE5hbWUiOiLmmbboi5Hlm73pmYVfQURNSU4iLCJlbmFibGVkIjp0cnVlLCJpc1B1YmxpYyI6ZmFsc2UsInBob25lIjoiIiwidGVuYW50SWQiOiIxYWRhMmFmMC1mZjQyLTExZTgtYmFmNy0zYjdkNzE4M2RhNTMiLCJjdXN0b21lcklkIjoiMTM4MTQwMDAtMWRkMi0xMWIyLTgwODAtODA4MDgwODA4MDgwIn0sIl9pZCI6IlJQNG43MmdkTzdLZVFxNk8iLCJpYXQiOjE1ODgwNDgwMTgsImV4cCI6MTU4ODEzNDQxOCwiYXVkIjoiaHR0cHM6Ly95b3VyZG9tYWluLmNvbSIsImlzcyI6ImZlYXRoZXJzIiwic3ViIjoiYW5vbnltb3VzIiwianRpIjoiMDY0YjE4NTYtNmFiZS00MThmLTk2NGItNzA4MzczNGYzNzRhIn0";

        String polayload =new String(Base64.getDecoder().decode(token));
        System.out.println(polayload);
    }

    public static void main(String[] args) {
        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
        System.out.println(bCryptPasswordEncoder.encode("admin.2021"));

    }
}
