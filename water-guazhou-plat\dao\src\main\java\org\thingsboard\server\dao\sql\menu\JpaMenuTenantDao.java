/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.menu;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.common.data.menu.MenuTenant;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.menu.MenuTenantDao;
import org.thingsboard.server.dao.model.sql.MenuTenantEntity;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;
import java.util.UUID;


@Component
@SqlDao
@Slf4j
public class JpaMenuTenantDao extends JpaAbstractDao<MenuTenantEntity, MenuTenant> implements MenuTenantDao {

    @Autowired
    private MenuTenantRepository menuTenantRepository;
    @Autowired
    private MenuPoolRepository menuPoolRepository;

    @Override
    protected Class<MenuTenantEntity> getEntityClass() {
        return MenuTenantEntity.class;
    }

    @Override
    protected CrudRepository<MenuTenantEntity, String> getCrudRepository() {
        return menuTenantRepository;
    }

    @Override
    public List<MenuPool> findMenuByTenantId(TenantId tenantId, MenuPoolId parentId) {
        return DaoUtil.convertDataList(
                menuPoolRepository.findMenuByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()), UUIDConverter.fromTimeUUID(parentId.getId()))
        );
    }

    @Override
    public List<MenuPool> findMenuByTenantId(TenantId tenantId) {
        return DaoUtil.convertDataList(
                menuPoolRepository.findMenuByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()))
        );
    }

    @Override
    public void deleteByTenantId(TenantId tenantId) {
        menuTenantRepository.deleteMenuTenantEntityByTenantIdAndIsExtensionMenu(UUIDConverter.fromTimeUUID(tenantId.getId()), DataConstants.IS_EXTENSION_MENU_FALSE);
    }

    public MenuPool findById(MenuTenantId id) {
        return menuPoolRepository.findMenuById(UUIDConverter.fromTimeUUID(id.getId())).toData();
    }

    @Override
    public List<String> getTreeByTenantId(TenantId tenantId) {
        return menuTenantRepository.findMenuPoolIdByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public List<MenuTenant> findByTenantId(TenantId tenantId) {
        return DaoUtil.convertDataList(menuTenantRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId())));
    }

    @Override
    public String getIdByType(Integer type, TenantId tenantId) {
        List<String> ids = menuTenantRepository.getIdByType(type, UUIDConverter.fromTimeUUID(tenantId.getId()));
        return ids.size() > 0 ? ids.get(0) : null;
    }

    @Override
    public String getAdditionalInfoByType(Integer type) {
        List<String> dataList = menuTenantRepository.getAdditionalInfoByType(type);
        return dataList.size() > 0 ? dataList.get(0) : "";
    }

    @Override
    public List<MenuTenant> findByExtensionMenu(TenantId tenantId) {
        return DaoUtil.convertDataList(
                menuTenantRepository.findByTenantIdAndIsExtensionMenu(UUIDConverter.fromTimeUUID(tenantId.getId()), "1"));
    }

    @Override
    public MenuTenant findByTenantIdAndMenuPoolId(TenantId tenantId, MenuPoolId id) {
        return DaoUtil.getData(
                menuTenantRepository.findByTenantIdAndMenuPoolId(UUIDConverter.fromTimeUUID(tenantId.getId()), UUIDConverter.fromTimeUUID(id.getId())));
    }

    @Override
    public List<MenuTenant> findByTenantIdAndMenuPoolIdIn(TenantId tenantId, List<String> menuIdList) {
        return DaoUtil.convertDataList(
                menuTenantRepository.findByTenantIdAndMenuPoolIdIn(UUIDConverter.fromTimeUUID(tenantId.getId()), menuIdList));
    }
}
