package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisa;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisaContainer;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionVisaMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionVisaPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionVisaSaveRequest;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal.SO_CONSTRUCTION_VISA_JOURNAL;
import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope.SO_CONSTRUCTION_CONTRACT;

@Service
public class SoConstructionVisaServiceImpl extends BasicSoConstructionTaskDriveService<SoConstructionVisa> implements SoConstructionVisaService {
    @Autowired
    private SoConstructionVisaMapper mapper;

    @Override
    public IPage<SoConstructionVisaContainer> findAllConditional(SoConstructionVisaPageRequest request) {
        IPage<SoConstructionVisaContainer> result = QueryUtil.pagify(request, (i, j) -> mapper.findByPage(request), () -> mapper.countByPage(request));
        for (SoConstructionVisaContainer record : result.getRecords()) {
            List<SoConstructionVisa> items = record.getItems();
            if (items.size() == 1 && items.get(0).getId() == null) {
                items.clear();
            }
        }
        return result;
    }

    @Override
    @Transactional
    public SoConstructionVisa save(SoConstructionVisaSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> commonSave(entity, e), mapper::updateFully);
    }

    @Override
    public boolean isCodeExists(String code, String tenantId, String id) {
        return mapper.isCodeExists(code, tenantId, id);
    }

    @Override
    public boolean update(SoConstructionVisa entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean complete(String constructionCode, String userId, String tenantId) {
        boolean success = taskInfoService.markAsComplete(constructionCode, tenantId, getCurrentScope());
        if (success) {
            recordService.recordComplete(tenantId, userId, constructionCode, getCurrentJournalType());
        }
        return success;
    }

    @Override
    public boolean isComplete(String id) {
        return taskInfoService.isComplete(id, getCurrentScope());
    }

    @Override
    public boolean isComplete(String constructionCode, String tenantId) {
        return taskInfoService.isComplete(constructionCode, tenantId, getCurrentScope());
    }

    @Override
    public SoGeneralSystemScope getCurrentScope() {
        return SO_CONSTRUCTION_CONTRACT;
    }

    @Override
    public SoGeneralSystemJournal getCurrentJournalType() {
        return SO_CONSTRUCTION_VISA_JOURNAL;
    }

    @Override
    public BaseMapper<SoConstructionVisa> getDirectMapper() {
        return mapper;
    }
}
