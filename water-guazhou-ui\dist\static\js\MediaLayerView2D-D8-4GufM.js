import{ar as Z,i as F,s as ee,R as te,j as re,N as se,e as b,y as D,a as ie}from"./Point-WxyopZva.js";import{aA as oe,bO as H,dv as x,e0 as ae,bq as ne,dG as le,dH as he,dI as me,bu as de,dJ as ce,dK as pe,dL as ue,dM as fe,eH as ye,bk as _e,bC as ve,bD as ge,eI as we,w as Re,A as Ee,eJ as Me}from"./MapView-DaoQedLH.js";import{R as T,cl as be,T as _}from"./index-r0dFAfgr.js";import{l as W,k as P,f as xe,a as z}from"./widget-BcWKanF2.js";import{j as Te,u as B}from"./perspectiveUtils-DtFlPAyL.js";import"./pe-B8dP0-Ut.js";import"./Rasterizer-CuAuGNQK.js";import{r as Ce}from"./Container-BwXq1a-x.js";import"./BufferPool-BAwXXd5w.js";import{T as Se}from"./enums-L38xj_2E.js";import{a as $e,w as Ve}from"./WGLContainer-Dyx9110G.js";import"./vec4f32-CjrfB-0a.js";import"./color-DAS1c3my.js";import{P as Ae,G as Ge,L as qe,D as De,F as Q}from"./enums-BDQrMlcz.js";import{E as Pe}from"./Texture-BYqObwfn.js";import"./ProgramTemplate-tdUBoAol.js";import"./MaterialKey-BYd7cMLJ.js";import"./utils-DPUVnAXL.js";import{E as k,f as Ie}from"./FramebufferObject-8j9PRuxE.js";import"./number-CoJp78Rz.js";import"./StyleDefinition-Bnnz5uyC.js";import"./enums-BRzLM11V.js";import"./MagnifierPrograms-txlcEObf.js";import"./OrderIndependentTransparency-C5Ap76ew.js";import"./floatRGBA-PQQNbO39.js";import"./webgl-debug-BJuvLAW9.js";import"./GraphicsView2D-DDTEO9AX.js";import"./AttributeStoreView-B0-phoCE.js";import"./earcut-BJup91r2.js";import{r as Le}from"./vec3f32-nZdmKIgz.js";import{e as Oe}from"./mat3f64-BVJGbF0t.js";import{f as Ue,u as je}from"./LayerView-BSt9B8Gh.js";import"./normalizeUtilsSync-NMksarRY.js";import"./_commonjsHelpers-DCkdB7M8.js";import"./cimAnalyzer-CMgqZsaO.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./enums-B5k73o5q.js";import"./alignmentUtils-CkNI7z7C.js";import"./definitions-826PWLuy.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./rasterizingUtils-BGZonnNf.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./config-MDUrh2eL.js";import"./GeometryUtils-BRRfazic.js";import"./imageutils-KgbVacIV.js";import"./Matcher-v9ErZwmD.js";import"./visualVariablesUtils-0WgcmuMn.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./tileUtils-B7X19rIS.js";import"./libtess-lH4Jrtkh.js";import"./TurboLine-CDscS66C.js";import"./ExpandedCIM-C1laM-_7.js";import"./CircularArray-CFz2ft5h.js";import"./ComputedAttributeStorage-CF7WDnl8.js";import"./arcadeTimeUtils-CyWQANWo.js";import"./executionError-BOo4jP8A.js";import"./centroid-UTistape.js";import"./basicInterfaces-Dc_Mm1a-.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./schemaUtils-DLXXqxNF.js";import"./util-DPgA-H2V.js";import"./TiledDisplayObject-C5kAiJtw.js";const v=Oe();class He extends Ce{constructor(e){super(),this.elementView=e,this.isWrapAround=!1,this.perspectiveTransform=oe(),this._vertices=new Float32Array(20),this._handles=[],this._handles.push(W(()=>this.elementView.element.opacity,t=>this.opacity=t,P),W(()=>[this.elementView.coords],()=>{this.requestRender()},P),xe(()=>this.elementView.element.loaded,()=>{const t=this.elementView.element;this.ready(),t.type==="video"&&T(t.content)&&this._handles.push(Z(t.content,"play",()=>this.requestRender()))},P)),e.element.load().catch(t=>{F.getLogger("esri.views.2d.layers.MediaLayerView2D").error(new ee("element-load-error","Element cannot be displayed",{element:e,error:t}))})}destroy(){this._handles.forEach(e=>e.remove()),this.texture=be(this.texture)}get dvsMat3(){return this.parent.dvsMat3}beforeRender(e){const{context:t}=e,s=this.elementView.element.content;if(T(s)){const i=s instanceof HTMLImageElement,o=s instanceof HTMLVideoElement,n=i?s.naturalWidth:o?s.videoWidth:s.width,a=i?s.naturalHeight:o?s.videoHeight:s.height;this._updatePerspectiveTransform(n,a),this.texture?o&&!s.paused&&(this.texture.setData(s),this.requestRender(),(t.type===H.WEBGL2||x(n)&&x(a))&&this.texture.generateMipmap()):(this.texture=new Pe(t,{pixelFormat:Ae.RGBA,dataType:Ge.UNSIGNED_BYTE,samplingMode:qe.LINEAR,wrapMode:De.CLAMP_TO_EDGE,width:n,height:a,preMultiplyAlpha:!0},s),(t.type===H.WEBGL2||x(n)&&x(a))&&this.texture.generateMipmap(),o&&!s.paused&&this.requestRender())}super.beforeRender(e)}_createTransforms(){return null}updateDrawCoords(e,t){const s=this.elementView.coords;if(_(s))return;const[i,o,n,a]=s.rings[0],c=this._vertices,{x:l,y:h}=e,p=t!==0;p?c.set([o[0]-l,o[1]-h,i[0]-l,i[1]-h,n[0]-l,n[1]-h,a[0]-l,a[1]-h,a[0]-l,a[1]-h,o[0]+t-l,o[1]-h,o[0]+t-l,o[1]-h,i[0]+t-l,i[1]-h,n[0]+t-l,n[1]-h,a[0]+t-l,a[1]-h]):c.set([o[0]-l,o[1]-h,i[0]-l,i[1]-h,n[0]-l,n[1]-h,a[0]-l,a[1]-h]),this.isWrapAround=p}getVAO(e,t,s){if(_(this.elementView.coords))return null;const i=this._vertices;if(this._vao)this._geometryVbo.setData(i);else{this._geometryVbo=k.createVertex(e,Q.DYNAMIC_DRAW,i);const o=k.createVertex(e,Q.STATIC_DRAW,new Uint16Array([0,0,0,1,1,0,1,1,1,1,0,0,0,0,0,1,1,0,1,1]));this._vao=new Ie(e,s,t,{geometry:this._geometryVbo,tex:o})}return this._vao}_updatePerspectiveTransform(e,t){const s=this._vertices;Te(v,[0,0,e,0,0,t,e,t],[s[0],s[1],s[4],s[5],s[2],s[3],s[6],s[7]]),ae(this.perspectiveTransform,v[6]/v[8]*e,v[7]/v[8]*t)}}class We extends $e{constructor(){super(...arguments),this._localOrigin=ne(0,0),this._viewStateId=-1,this._dvsMat3=le(),this.requiresDedicatedFBO=!1}get dvsMat3(){return this._dvsMat3}beforeRender(e){this._updateMatrices(e),this._updateOverlays(e,this.children);for(const t of this.children)t.beforeRender(e)}prepareRenderPasses(e){const t=e.registerRenderPass({name:"overlay",brushes:[Ve.overlay],target:()=>this.children,drawPhase:Se.MAP});return[...super.prepareRenderPasses(e),t]}_updateMatrices(e){const{state:t}=e,{id:s,size:i,pixelRatio:o,resolution:n,rotation:a,viewpoint:c,displayMat3:l}=t;if(this._viewStateId===s)return;const h=Math.PI/180*a,p=o*i[0],f=o*i[1],{x:C,y:w}=c.targetGeometry,S=he(C,t.spatialReference);this._localOrigin.x=S,this._localOrigin.y=w;const $=n*p,R=n*f,m=me(this._dvsMat3);de(m,m,l),ce(m,m,pe(p/2,f/2)),ue(m,m,Le(p/$,-f/R,1)),fe(m,m,-h),this._viewStateId=s}_updateOverlays(e,t){const{state:s}=e,{rotation:i,spatialReference:o,worldScreenWidth:n,size:a,viewpoint:c}=s,l=this._localOrigin;let h=0;const p=te(o);if(p&&o.isWrappable){const f=a[0],C=a[1],w=180/Math.PI*i,S=Math.abs(Math.cos(w)),$=Math.abs(Math.sin(w)),R=Math.round(f*S+C*$),[m,V]=p.valid,u=ye(o),{x:I,y:J}=c.targetGeometry,Y=[I,J],A=[0,0];s.toScreen(A,Y);const E=[0,0];let G;G=R>n?.5*n:.5*R;const L=Math.floor((I+.5*u)/u),K=m+L*u,X=V+L*u,q=[A[0]+G,0];s.toMap(E,q),E[0]>X&&(h=u),q[0]=A[0]-G,s.toMap(E,q),E[0]<K&&(h=-u);for(const M of t){const O=M.elementView.bounds;if(_(O))continue;const[U,,j]=O;U<m&&j>m?M.updateDrawCoords(l,u):j>V&&U<V?M.updateDrawCoords(l,-u):M.updateDrawCoords(l,h)}}else for(const f of t)f.updateDrawCoords(l,h)}}let y=class extends Ue(je){constructor(){super(...arguments),this._overlayContainer=null,this._fetchQueue=null,this._tileStrategy=null,this._elementReferences=new Map,this._debugGraphicsView=null,this.layer=null,this.elements=new _e}attach(){this.addAttachHandles([z(()=>this.layer.effectiveSource,"refresh",()=>{for(const r of this._tileStrategy.tiles)this._updateTile(r);this.requestUpdate()}),z(()=>this.layer.effectiveSource,"change",({element:r})=>this._elementUpdateHandler(r))]),this._overlayContainer=new We,this.container.addChild(this._overlayContainer),this._fetchQueue=new ve({tileInfoView:this.view.featuresTilingScheme,concurrency:10,process:(r,e)=>this._queryElements(r,e)}),this._tileStrategy=new ge({cachePolicy:"purge",resampling:!0,acquireTile:r=>this._acquireTile(r),releaseTile:r=>this._releaseTile(r),tileInfoView:this.view.featuresTilingScheme}),this.requestUpdate()}detach(){var r;this.elements.removeAll(),this._tileStrategy.destroy(),this._fetchQueue.destroy(),this._overlayContainer.removeAllChildren(),this.container.removeAllChildren(),this._elementReferences.clear(),(r=this._debugGraphicsView)==null||r.destroy()}supportsSpatialReference(r){return!0}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this.requestUpdate()}update(r){var e;this._tileStrategy.update(r),(e=this._debugGraphicsView)==null||e.update(r)}async hitTest(r,e){const t=[],s=r.normalize(),i=[s.x,s.y];for(const{projectedElement:{normalizedCoords:o,element:n}}of this._elementReferences.values())T(o)&&we(o.rings,i)&&t.push({type:"media",element:n,layer:this.layer,mapPoint:r});return t.reverse()}canResume(){return this.layer.source!=null&&super.canResume()}async doRefresh(){this._fetchQueue.reset(),this._tileStrategy.tiles.forEach(r=>this._updateTile(r))}_acquireTile(r){const e=new ze(r.clone());return this._updateTile(e),e}_updateTile(r){this.updatingHandles.addPromise(this._fetchQueue.push(r.key).then(e=>{const[t,s]=r.setElements(e);this._referenceElements(r,t),this._dereferenceElements(r,s),this.requestUpdate()},e=>{re(e)||F.getLogger(this.declaredClass).error(e)}))}_releaseTile(r){this._fetchQueue.abort(r.key.id),r.elements&&this._dereferenceElements(r,r.elements),this.requestUpdate()}async _queryElements(r,e){const t=this.layer.effectiveSource;if(_(t))return[];this.view.featuresTilingScheme.getTileBounds(d,r,!0);const s=new Re({xmin:d[0],ymin:d[1],xmax:d[2],ymax:d[3],spatialReference:this.view.spatialReference});return t.queryElements(s,e)}_referenceElements(r,e){const t=this.layer.source;if(!_(t))for(const s of e)this._referenceElement(r,s)}_referenceElement(r,e){se(this._elementReferences,e.uid,()=>{const t=new B({element:e,spatialReference:this.view.spatialReference}),s=new He(t);return this._overlayContainer.addChild(s),this.elements.add(e),{tiles:new Set,projectedElement:t,overlay:s,debugGraphic:null}}).tiles.add(r)}_dereferenceElements(r,e){for(const t of e)this._dereferenceElement(r,t)}_dereferenceElement(r,e){var s;const t=this._elementReferences.get(e.uid);t.tiles.delete(r),t.tiles.size||(this._overlayContainer.removeChild(t.overlay),t.overlay.destroy(),t.projectedElement.destroy(),this._elementReferences.delete(e.uid),this.elements.remove(e),(s=this._debugGraphicsView)==null||s.graphics.remove(t.debugGraphic))}_elementUpdateHandler(r){var s;let e=this._elementReferences.get(r.uid);if(e){const i=e.projectedElement.normalizedCoords;if(_(i))return this._overlayContainer.removeChild(e.overlay),e.overlay.destroy(),e.projectedElement.destroy(),this._elementReferences.delete(r.uid),this.elements.remove(r),void((s=this._debugGraphicsView)==null?void 0:s.graphics.remove(e.debugGraphic));const o=[],n=[];for(const a of this._tileStrategy.tiles){const c=N(this.view.featuresTilingScheme,a,i);e.tiles.has(a)?c||n.push(a):c&&o.push(a)}for(const a of o)this._referenceElement(a,r);for(const a of n)this._dereferenceElement(a,r);return e=this._elementReferences.get(r.uid),void((e==null?void 0:e.debugGraphic)&&(e.debugGraphic.geometry=e.projectedElement.normalizedCoords,this._debugGraphicsView.graphicUpdateHandler({graphic:e.debugGraphic,property:"geometry"})))}const t=new B({element:r,spatialReference:this.view.spatialReference}).normalizedCoords;if(T(t))for(const i of this._tileStrategy.tiles)N(this.view.featuresTilingScheme,i,t)&&this._referenceElement(i,r)}};b([D()],y.prototype,"_fetchQueue",void 0),b([D()],y.prototype,"layer",void 0),b([D({readOnly:!0})],y.prototype,"elements",void 0),y=b([ie("esri.views.2d.layers.MediaLayerView2D")],y);const d=Ee(),g={xmin:0,ymin:0,xmax:0,ymax:0};function N(r,e,t){return r.getTileBounds(d,e.key,!0),g.xmin=d[0],g.ymin=d[1],g.xmax=d[2],g.ymax=d[3],Me(g,t)}class ze{constructor(e){this.key=e,this.elements=null,this.isReady=!1,this.visible=!0}setElements(e){const t=[],s=new Set(this.elements);this.elements=e;for(const i of e)s.has(i)?s.delete(i):t.push(i);return this.isReady=!0,[t,Array.from(s)]}destroy(){}}const sr=y;export{sr as default};
