import{_ as m}from"./index-C9hz-UZb.js";import{d as b,c as g,r as h,a8 as x,g as C,h as v,F as e,p as l,bh as V,q as t,i as s,d_ as k,aw as n,cE as y,J as B,aq as E,C as T}from"./index-r0dFAfgr.js";const w={class:"card_title"},I={name:"CardTableV"},q=b({...I,props:{title:{},value:{},total:{}},setup(c,{expose:r}){const o=g(),i=c,_=h({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"规则名称",prop:"title"},{label:"报警信息",prop:"alarmInfo"}],operations:[],dataList:x(()=>i.value),pagination:{hide:!0}});return r({reftable:o}),(a,F)=>{const d=y,p=B,u=E,f=m;return C(),v(f,{class:n({card:!0,maxheight:a.total<=3}),title:" "},{title:e(()=>[l("div",w,[l("div",null,V(a.title),1),t(p,{key:"",text:""},{default:e(()=>[t(d,null,{default:e(()=>[t(s(k))]),_:1})]),_:1})])]),default:e(()=>[t(u,{ref_key:"reftable",ref:o,class:n({"card-table":!0,"max-table-height":a.total<=3}),title:"asdas",config:s(_)},null,8,["class","config"])]),_:1},8,["class"])}}}),S=T(q,[["__scopeId","data-v-fcb030b4"]]);export{S as default};
