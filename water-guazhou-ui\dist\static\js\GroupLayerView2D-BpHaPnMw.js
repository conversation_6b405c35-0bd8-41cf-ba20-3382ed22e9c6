import{e as a,y as l,a as p}from"./Point-WxyopZva.js";import{T as n}from"./index-r0dFAfgr.js";import{r as d}from"./GroupContainer-DSGvkJlW.js";import{u as V,f as m}from"./LayerView-BSt9B8Gh.js";import{bW as w,bk as v,bV as u}from"./MapView-DaoQedLH.js";import{l as o,U as y}from"./widget-BcWKanF2.js";import"./WGLContainer-Dyx9110G.js";import"./definitions-826PWLuy.js";import"./FramebufferObject-8j9PRuxE.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./vec4f32-CjrfB-0a.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./enums-L38xj_2E.js";import"./number-CoJp78Rz.js";import"./ProgramTemplate-tdUBoAol.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./utils-DPUVnAXL.js";import"./StyleDefinition-Bnnz5uyC.js";import"./config-MDUrh2eL.js";import"./GeometryUtils-BRRfazic.js";import"./Container-BwXq1a-x.js";import"./earcut-BJup91r2.js";import"./pe-B8dP0-Ut.js";let s=class extends V{constructor(i){super(i),this.type="group",this.layerViews=new v}_allLayerViewVisibility(i){this.layerViews.forEach(e=>{e.visible=i})}initialize(){this.handles.add([this.layerViews.on("change",i=>this._layerViewsChangeHandler(i)),o(()=>this.layer.visibilityMode,()=>this._applyVisibility(()=>this._allLayerViewVisibility(this.visible),()=>this._applyExclusiveVisibility(null)),y),o(()=>this.visible,i=>{this._applyVisibility(()=>this._allLayerViewVisibility(i),()=>{})},y)],"grouplayerview"),this._layerViewsChangeHandler({target:null,added:this.layerViews.toArray(),removed:[],moved:[]})}set layerViews(i){this._set("layerViews",u(i,this._get("layerViews")))}get updatingProgress(){return this.layerViews.length===0?1:this.layerViews.reduce((i,e)=>i+e.updatingProgress,0)/this.layerViews.length}isUpdating(){return this.layerViews.some(i=>i.updating)}_hasLayerViewVisibleOverrides(){return this.layerViews.some(i=>i._isOverridden("visible"))}_findLayerViewForLayer(i){return i&&this.layerViews.find(e=>e.layer===i)}_firstVisibleOnLayerOrder(){const i=this.layer.layers.find(e=>{var t;return!!((t=this._findLayerViewForLayer(e))!=null&&t.visible)});return i&&this._findLayerViewForLayer(i)}_applyExclusiveVisibility(i){n(i)&&(i=this._firstVisibleOnLayerOrder(),n(i)&&this.layerViews.length>0&&(i=this._findLayerViewForLayer(this.layer.layers.getItemAt(0)))),this.layerViews.forEach(e=>{e.visible=e===i})}_layerViewsChangeHandler(i){this.handles.remove("grouplayerview:visible"),this.handles.add(this.layerViews.map(t=>o(()=>t.visible,r=>this._applyVisibility(()=>{r!==this.visible&&(t.visible=this.visible)},()=>this._applyExclusiveVisibility(r?t:null)),y)).toArray(),"grouplayerview:visible");const e=i.added[i.added.length-1];this._applyVisibility(()=>this._allLayerViewVisibility(this.visible),()=>this._applyExclusiveVisibility(e!=null&&e.visible?e:null))}_applyVisibility(i,e){var t,r;this._hasLayerViewVisibleOverrides()&&(((t=this.layer)==null?void 0:t.visibilityMode)==="inherited"?i():((r=this.layer)==null?void 0:r.visibilityMode)==="exclusive"&&e())}};a([l({cast:w})],s.prototype,"layerViews",null),a([l({readOnly:!0})],s.prototype,"updatingProgress",null),a([l()],s.prototype,"view",void 0),s=a([p("esri.views.layers.GroupLayerView")],s);const b=s;let h=class extends m(b){constructor(){super(...arguments),this.container=new d}attach(){this._updateStageChildren(),this.addAttachHandles(this.layerViews.on("after-changes",()=>this._updateStageChildren()))}detach(){this.container.removeAllChildren()}update(i){}moveStart(){}viewChange(){}moveEnd(){}_updateStageChildren(){this.container.removeAllChildren(),this.layerViews.forEach((i,e)=>this.container.addChildAt(i.container,e))}};h=a([p("esri.views.2d.layers.GroupLayerView2D")],h);const J=h;export{J as default};
