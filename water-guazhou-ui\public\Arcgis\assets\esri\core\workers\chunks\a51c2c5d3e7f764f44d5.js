"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[3055],{3920:(e,t,i)=>{i.d(t,{p:()=>h,r:()=>c});var l=i(43697),s=i(15923),n=i(61247),r=i(5600),a=i(52011),o=i(72762);const h=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new n.Z}get updatingHandles(){return this._get("updatingHandles")||new o.t}};return(0,l._)([(0,r.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,l._)([(0,r.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,l._)([(0,a.j)("esri.core.HandleOwner")],t),t};let c=class extends(h(s.Z)){};c=(0,l._)([(0,a.j)("esri.core.HandleOwner")],c)},43090:(e,t,i)=>{function l(e){return 32+e.length}function s(e){if(!e)return 0;let t=o;for(const i in e)if(e.hasOwnProperty(i)){const s=e[i];switch(typeof s){case"string":t+=l(s);break;case"number":t+=16;break;case"boolean":t+=4}}return t}function n(e){if(!e)return 0;if(Array.isArray(e))return function(e){const t=e.length;if(0===t||"number"==typeof e[0])return 32+8*t;let i=h;for(let l=0;l<t;l++)i+=r(e[l]);return i}(e);let t=o;for(const i in e)e.hasOwnProperty(i)&&(t+=r(e[i]));return t}function r(e){switch(typeof e){case"object":return n(e);case"string":return l(e);case"number":return 16;case"boolean":return 4;default:return 8}}function a(e,t){return h+e.length*t}i.d(t,{Ul:()=>n,Y8:()=>c,do:()=>a,f2:()=>s});const o=32,h=32;var c;!function(e){e[e.KILOBYTES=1024]="KILOBYTES",e[e.MEGABYTES=1048576]="MEGABYTES",e[e.GIGABYTES=1073741824]="GIGABYTES"}(c||(c={}))},72762:(e,t,i)=>{i.d(t,{t:()=>p});var l=i(43697),s=i(15923),n=i(61247),r=i(70586),a=i(17445),o=i(1654),h=i(5600),c=i(52011);let p=class extends s.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new n.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,i={}){return this._installWatch(e,t,i,a.YP)}addWhen(e,t,i={}){return this._installWatch(e,t,i,a.gx)}addOnCollectionChange(e,t,{initial:i=!1,final:l=!1}={}){const s=++this._handleId;return this._handles.add([(0,a.on)(e,"after-changes",this._createSyncUpdatingCallback(),a.Z_),(0,a.on)(e,"change",t,{onListenerAdd:i?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:l?e=>t({added:[],removed:e.toArray()}):void 0})],s),{remove:()=>this._handles.remove(s)}}addPromise(e){if((0,r.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(u)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const i=()=>this._handles.remove(t);return e.then(i,i),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,i={},l){const s=++this._handleId;i.sync||this._installSyncUpdatingWatch(e,s);const n=l(e,t,i);return this._handles.add(n,s),{remove:()=>this._handles.remove(s)}}_installSyncUpdatingWatch(e,t){const i=this._createSyncUpdatingCallback(),l=(0,a.YP)(e,i,{sync:!0,equals:()=>!1});return this._handles.add(l,t),l}_createSyncUpdatingCallback(){return()=>{this._handles.remove(u),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,o.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(u))})),u)}}};(0,l._)([(0,h.Cb)({readOnly:!0})],p.prototype,"updating",void 0),p=(0,l._)([(0,c.j)("esri.core.support.WatchUpdatingTracking")],p);const u=-42},17287:(e,t,i)=>{i.d(t,{Y:()=>h});var l=i(43697),s=i(92604),n=i(70586),r=i(5600),a=(i(75215),i(67676),i(52011)),o=i(66677);const h=e=>{let t=class extends e{get title(){if(this._get("title")&&"defaults"!==this.originOf("title"))return this._get("title");if(this.url){const e=(0,o.Qc)(this.url);if((0,n.pC)(e)&&e.title)return e.title}return this._get("title")||""}set title(e){this._set("title",e)}set url(e){this._set("url",(0,o.Nm)(e,s.Z.getLogger(this.declaredClass)))}};return(0,l._)([(0,r.Cb)()],t.prototype,"title",null),(0,l._)([(0,r.Cb)({type:String})],t.prototype,"url",null),t=(0,l._)([(0,a.j)("esri.layers.mixins.ArcGISService")],t),t}},39450:(e,t,i)=>{i.d(t,{Z:()=>c});var l,s=i(43697),n=i(96674),r=i(5600),a=i(75215),o=(i(67676),i(52011));let h=l=class extends n.wq{constructor(e){super(e),this.cols=null,this.level=0,this.levelValue=null,this.origin=null,this.resolution=0,this.rows=null,this.scale=0}clone(){return new l({cols:this.cols,level:this.level,levelValue:this.levelValue,resolution:this.resolution,rows:this.rows,scale:this.scale})}};(0,s._)([(0,r.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],h.prototype,"cols",void 0),(0,s._)([(0,r.Cb)({type:a.z8,json:{write:!0}})],h.prototype,"level",void 0),(0,s._)([(0,r.Cb)({type:String,json:{write:!0}})],h.prototype,"levelValue",void 0),(0,s._)([(0,r.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],h.prototype,"origin",void 0),(0,s._)([(0,r.Cb)({type:Number,json:{write:!0}})],h.prototype,"resolution",void 0),(0,s._)([(0,r.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],h.prototype,"rows",void 0),(0,s._)([(0,r.Cb)({type:Number,json:{write:!0}})],h.prototype,"scale",void 0),h=l=(0,s._)([(0,o.j)("esri.layers.support.LOD")],h);const c=h},11145:(e,t,i)=>{i.d(t,{Z:()=>z});var l,s=i(43697),n=i(35454),r=i(96674),a=i(70586),o=i(67900),h=i(5600),c=i(75215),p=(i(67676),i(71715)),u=i(52011),d=i(30556),f=i(94139),v=i(82971),m=i(24470),_=i(8744),g=i(40488),y=i(39450),w=i(43077);const b=new n.X({PNG:"png",PNG8:"png8",PNG24:"png24",PNG32:"png32",JPEG:"jpg",JPG:"jpg",DIB:"dib",TIFF:"tiff",EMF:"emf",PS:"ps",PDF:"pdf",GIF:"gif",SVG:"svg",SVGZ:"svgz",Mixed:"mixed",MIXED:"mixed",LERC:"lerc",LERC2D:"lerc2d",RAW:"raw",pbf:"pbf"});let S=l=class extends r.wq{static create(e={}){const{resolutionFactor:t=1,scales:i,size:s=256,spatialReference:n=v.Z.WebMercator,numLODs:r=24}=e;if(!(0,_.JY)(n)){const e=[];if(i)for(let t=0;t<i.length;t++){const l=i[t];e.push(new y.Z({level:t,scale:l,resolution:l}))}else{let t=5e-4;for(let i=r-1;i>=0;i--)e.unshift(new y.Z({level:i,scale:t,resolution:t})),t*=2}return new l({dpi:96,lods:e,origin:new f.Z(0,0,n),size:[s,s],spatialReference:n})}const a=(0,_.C5)(n),h=e.origin?new f.Z({x:e.origin.x,y:e.origin.y,spatialReference:n}):new f.Z(a?{x:a.origin[0],y:a.origin[1],spatialReference:n}:{x:0,y:0,spatialReference:n}),c=1/(39.37*(0,o.c9)(n)*96),p=[];if(i)for(let e=0;e<i.length;e++){const t=i[e],l=t*c;p.push(new y.Z({level:e,scale:t,resolution:l}))}else{let e=(0,_.sT)(n)?512/s*591657527.5917094:256/s*591657527.591555;const i=Math.ceil(r/t);p.push(new y.Z({level:0,scale:e,resolution:e*c}));for(let l=1;l<i;l++){const i=e/2**t,s=i*c;p.push(new y.Z({level:l,scale:i,resolution:s})),e=i}}return new l({dpi:96,lods:p,origin:h,size:[s,s],spatialReference:n})}constructor(e){super(e),this.dpi=96,this.format=null,this.origin=null,this.minScale=0,this.maxScale=0,this.size=null,this.spatialReference=null}get isWrappable(){const{spatialReference:e,origin:t}=this;if(e&&t){const i=(0,_.C5)(e);return e.isWrappable&&!!i&&Math.abs(i.origin[0]-t.x)<=i.dx}return!1}readOrigin(e,t){return f.Z.fromJSON({spatialReference:t.spatialReference,...e})}set lods(e){let t=0,i=0;const l=[],s=this._levelToLOD={};e&&(t=-1/0,i=1/0,e.forEach((e=>{l.push(e.scale),t=e.scale>t?e.scale:t,i=e.scale<i?e.scale:i,s[e.level]=e}))),this._set("scales",l),this._set("minScale",t),this._set("maxScale",i),this._set("lods",e),this._initializeUpsampleLevels()}readSize(e,t){return[t.cols,t.rows]}writeSize(e,t){t.cols=e[0],t.rows=e[1]}zoomToScale(e){const t=this.scales;if(e<=0)return t[0];if(e>=t.length-1)return t[t.length-1];const i=Math.floor(e),l=i+1;return t[i]/(t[i]/t[l])**(e-i)}scaleToZoom(e){const t=this.scales,i=t.length-1;let l=0;for(;l<i;l++){const i=t[l],s=t[l+1];if(i<=e)return l;if(s===e)return l+1;if(i>e&&s<e)return l+Math.log(i/e)/Math.log(i/s)}return l}snapScale(e,t=.95){const i=this.scaleToZoom(e);return i%Math.floor(i)>=t?this.zoomToScale(Math.ceil(i)):this.zoomToScale(Math.floor(i))}tileAt(e,t,i,l){const s=this.lodAt(e);if(!s)return null;let n,r;if("number"==typeof t)n=t,r=i;else if((0,_.fS)(t.spatialReference,this.spatialReference))n=t.x,r=t.y,l=i;else{const e=(0,g.iV)(t,this.spatialReference);if((0,a.Wi)(e))return null;n=e.x,r=e.y,l=i}const o=s.resolution*this.size[0],h=s.resolution*this.size[1];return l||(l=new w.f(null,0,0,0,(0,m.Ue)())),l.level=e,l.row=Math.floor((this.origin.y-r)/h+.001),l.col=Math.floor((n-this.origin.x)/o+.001),this.updateTileInfo(l),l}updateTileInfo(e,t=l.ExtrapolateOptions.NONE){let i=this.lodAt(e.level);if(!i&&t===l.ExtrapolateOptions.POWER_OF_TWO){const t=this.lods[this.lods.length-1];t.level<e.level&&(i=t)}if(!i)return;const s=e.level-i.level,n=i.resolution*this.size[0]/2**s,r=i.resolution*this.size[1]/2**s;e.id=`${e.level}/${e.row}/${e.col}`,e.extent||(e.extent=(0,m.Ue)()),e.extent[0]=this.origin.x+e.col*n,e.extent[1]=this.origin.y-(e.row+1)*r,e.extent[2]=e.extent[0]+n,e.extent[3]=e.extent[1]+r}upsampleTile(e){const t=this._upsampleLevels[e.level];return!(!t||-1===t.parentLevel||(e.level=t.parentLevel,e.row=Math.floor(e.row/t.factor+.001),e.col=Math.floor(e.col/t.factor+.001),this.updateTileInfo(e),0))}getTileBounds(e,t){const i=this.lodAt(t.level);if(null==i)return null;const{resolution:l}=i,s=l*this.size[0],n=l*this.size[1];return e[0]=this.origin.x+t.col*s,e[1]=this.origin.y-(t.row+1)*n,e[2]=e[0]+s,e[3]=e[1]+n,e}lodAt(e){return this._levelToLOD?.[e]??null}clone(){return l.fromJSON(this.write({}))}getOrCreateCompatible(e,t){if(256===this.size[0]&&256===this.size[1])return 256===e?this:null;const i=[],s=this.lods.length;for(let e=0;e<s;e++){const l=this.lods[e],s=l.resolution*t;i.push(new y.Z({level:l.level,scale:l.scale,resolution:s}))}return new l({size:[e,e],dpi:this.dpi,format:this.format,compressionQuality:this.compressionQuality,origin:this.origin,spatialReference:this.spatialReference,lods:i})}_initializeUpsampleLevels(){const e=this.lods;this._upsampleLevels=[];let t=null;for(let i=0;i<e.length;i++){const l=e[i];this._upsampleLevels[l.level]={parentLevel:t?t.level:-1,factor:t?t.resolution/l.resolution:0},t=l}}};var O,C;(0,s._)([(0,h.Cb)({type:Number,json:{write:!0}})],S.prototype,"compressionQuality",void 0),(0,s._)([(0,h.Cb)({type:Number,json:{write:!0}})],S.prototype,"dpi",void 0),(0,s._)([(0,h.Cb)({type:String,json:{read:b.read,write:b.write,origins:{"web-scene":{read:!1,write:!1}}}})],S.prototype,"format",void 0),(0,s._)([(0,h.Cb)({readOnly:!0})],S.prototype,"isWrappable",null),(0,s._)([(0,h.Cb)({type:f.Z,json:{write:!0}})],S.prototype,"origin",void 0),(0,s._)([(0,p.r)("origin")],S.prototype,"readOrigin",null),(0,s._)([(0,h.Cb)({type:[y.Z],value:null,json:{write:!0}})],S.prototype,"lods",null),(0,s._)([(0,h.Cb)({readOnly:!0})],S.prototype,"minScale",void 0),(0,s._)([(0,h.Cb)({readOnly:!0})],S.prototype,"maxScale",void 0),(0,s._)([(0,h.Cb)({readOnly:!0})],S.prototype,"scales",void 0),(0,s._)([(0,h.Cb)({cast:e=>Array.isArray(e)?e:"number"==typeof e?[e,e]:[256,256]})],S.prototype,"size",void 0),(0,s._)([(0,p.r)("size",["rows","cols"])],S.prototype,"readSize",null),(0,s._)([(0,d.c)("size",{cols:{type:c.z8},rows:{type:c.z8}})],S.prototype,"writeSize",null),(0,s._)([(0,h.Cb)({type:v.Z,json:{write:!0}})],S.prototype,"spatialReference",void 0),S=l=(0,s._)([(0,u.j)("esri.layers.support.TileInfo")],S),(C=(O=S||(S={})).ExtrapolateOptions||(O.ExtrapolateOptions={}))[C.NONE=0]="NONE",C[C.POWER_OF_TWO=1]="POWER_OF_TWO";const z=S},43077:(e,t,i)=>{i.d(t,{f:()=>l});class l{constructor(e,t,i,l,s){this.id=e,this.level=t,this.row=i,this.col=l,this.extent=s}}},56608:(e,t,i)=>{i.d(t,{y:()=>z});var l,s=i(43697),n=i(3172),r=i(15923),a=i(43090),o=i(20102),h=i(3920),c=i(92604),p=i(13867),u=i(44553),d=i(95330),f=i(17445),v=i(1654),m=i(17452),_=i(5600),g=i(90578),y=i(67676),w=i(52011),b=i(43077),S=i(22974);class O{constructor(){this.location={left:0,top:0,width:0,height:0},this._allAvailability="unknown",this.byteSize=40}getAvailability(e,t){if("unknown"!==this._allAvailability)return this._allAvailability;const i=(e-this.location.top)*this.location.width+(t-this.location.left),l=i%8,s=i>>3,n=this._tileAvailabilityBitSet;return s<0||s>n.length?"unknown":n[s]&1<<l?"available":"unavailable"}_updateFromData(e){const t=this.location.width,i=this.location.height;let l=!0,s=!0;const n=Math.ceil(t*i/8),r=new Uint8Array(n);let a=0;for(let t=0;t<e.length;t++){const i=t%8;e[t]?(s=!1,r[a]|=1<<i):l=!1,7===i&&++a}s?this._allAvailability="unavailable":l?this._allAvailability="available":(this._allAvailability="unknown",this._tileAvailabilityBitSet=r,this.byteSize+=r.length)}static fromDefinition(e,t){const i=e.service.request||n.default,{row:l,col:s,width:r,height:a}=e,h={query:{f:"json"}};return t=t?{...h,...t}:h,i(function(e){let t;if("vector-tile"===e.service.type)t=`${e.service.url}/tilemap/${e.level}/${e.row}/${e.col}/${e.width}/${e.height}`;else{const i=e.service.tileServers;t=`${i&&i.length?i[e.row%i.length]:e.service.url}/tilemap/${e.level}/${e.row}/${e.col}/${e.width}/${e.height}`}const i=e.service.query;return i&&(t=`${t}?${i}`),t}(e),t).then((e=>e.data)).catch((e=>{if(e&&e.details&&422===e.details.httpStatus)return{location:{top:l,left:s,width:r,height:a},valid:!0,data:(0,y.a9)(r*a,0)};throw e})).then((e=>{if(e.location&&(e.location.top!==l||e.location.left!==s||e.location.width!==r||e.location.height!==a))throw new o.Z("tilemap:location-mismatch","Tilemap response for different location than requested",{response:e,definition:{top:l,left:s,width:r,height:a}});return O.fromJSON(e)}))}static fromJSON(e){O._validateJSON(e);const t=new O;return t.location=Object.freeze((0,S.d9)(e.location)),t._updateFromData(e.data),Object.freeze(t)}static _validateJSON(e){if(!e||!e.location)throw new o.Z("tilemap:missing-location","Location missing from tilemap response");if(!1===e.valid)throw new o.Z("tilemap:invalid","Tilemap response was marked as invalid");if(!e.data)throw new o.Z("tilemap:missing-data","Data missing from tilemap response");if(!Array.isArray(e.data))throw new o.Z("tilemap:data-mismatch","Data must be an array of numbers");if(e.data.length!==e.location.width*e.location.height)throw new o.Z("tilemap:data-mismatch","Number of data items does not match width/height of tilemap")}}function C(e){return`${e.level}/${e.row}/${e.col}/${e.width}/${e.height}`}let z=l=class extends((0,h.p)(r.Z)){constructor(e){super(e),this._pendingTilemapRequests={},this._availableLevels={},this.levels=5,this.cacheByteSize=2*a.Y8.MEGABYTES,this.request=n.default,this._prefetchingEnabled=!0}initialize(){this._tilemapCache=new p.Z(this.cacheByteSize),this.addHandles([(0,f.YP)((()=>{const{layer:e}=this;return[e?.parsedUrl,e?.tileServers,e?.apiKey,e?.customParameters]}),(()=>this._initializeTilemapDefinition())),(0,f.YP)((()=>this.layer?.tileInfo?.lods),(e=>this._initializeAvailableLevels(e)),f.tX)]),this._initializeTilemapDefinition()}castLevels(e){return e<=2?(c.Z.getLogger(this.declaredClass).error("Minimum levels for Tilemap is 3, but got ",e),3):e}get size(){return 1<<this.levels}fetchTilemap(e,t,i,l){if(!this._availableLevels[e])return Promise.reject(new o.Z("tilemap-cache:level-unavailable",`Level ${e} is unavailable in the service`));const s=this._tmpTilemapDefinition,n=this._tilemapFromCache(e,t,i,s);if(n)return Promise.resolve(n);const r=l&&l.signal;return l={...l,signal:null},new Promise(((e,t)=>{(0,d.fu)(r,(()=>t((0,d.zE)())));const i=C(s);let n=this._pendingTilemapRequests[i];if(!n){n=O.fromDefinition(s,l).then((e=>(this._tilemapCache.put(i,e,e.byteSize),e)));const e=()=>delete this._pendingTilemapRequests[i];this._pendingTilemapRequests[i]=n,n.then(e,e)}n.then(e,t)}))}getAvailability(e,t,i){if(!this._availableLevels[e])return"unavailable";const l=this._tilemapFromCache(e,t,i,this._tmpTilemapDefinition);return l?l.getAvailability(t,i):"unknown"}fetchAvailability(e,t,i,l){return this._availableLevels[e]?this.fetchTilemap(e,t,i,l).catch((e=>e)).then((l=>{if(l instanceof O){const s=l.getAvailability(t,i);if("unavailable"===s)throw new o.Z("tile-map:tile-unavailable","Tile is not available",{level:e,row:t,col:i});return s}if((0,d.D_)(l))throw l;return"unknown"})):Promise.reject(new o.Z("tilemap-cache:level-unavailable",`Level ${e} is unavailable in the service`))}fetchAvailabilityUpsample(e,t,i,l,s){l.level=e,l.row=t,l.col=i;const n=this.layer.tileInfo;n.updateTileInfo(l);const r=this.fetchAvailability(e,t,i,s).catch((e=>{if((0,d.D_)(e))throw e;if(n.upsampleTile(l))return this.fetchAvailabilityUpsample(l.level,l.row,l.col,l);throw e}));return this._fetchAvailabilityUpsamplePrefetch(l.id,e,t,i,s,r),r}async _fetchAvailabilityUpsamplePrefetch(e,t,i,s,n,r){if(!this._prefetchingEnabled||null==e)return;const a=`prefetch-${e}`;if(this.handles.has(a))return;const o=new AbortController;r.then((()=>o.abort()),(()=>o.abort()));let h=!1;const c={remove(){h||(h=!0,o.abort())}};if(this.handles.add(c,a),await(0,v.MU)(10,o.signal).catch((()=>{})),h||(h=!0,this.handles.remove(a)),(0,d.Hc)(o))return;const p=new b.f(e,t,i,s),u={...n,signal:o.signal},f=this.layer.tileInfo;for(let e=0;l._prefetches.length<l._maxPrefetch&&f.upsampleTile(p);++e){const e=this.fetchAvailability(p.level,p.row,p.col,u);l._prefetches.push(e);const t=()=>{l._prefetches.removeUnordered(e)};e.then(t,t)}}_initializeTilemapDefinition(){if(!this.layer.parsedUrl)return;const{parsedUrl:e,apiKey:t,customParameters:i}=this.layer;this._tilemapCache.clear(),this._tmpTilemapDefinition={service:{url:e.path,query:(0,m.B7)({...e.query,...i,token:t??e.query?.token}),tileServers:this.layer.tileServers,request:this.request,type:this.layer.type},width:this.size,height:this.size,level:0,row:0,col:0}}_tilemapFromCache(e,t,i,l){l.level=e,l.row=t-t%this.size,l.col=i-i%this.size;const s=C(l);return this._tilemapCache.get(s)}_initializeAvailableLevels(e){this._availableLevels={},e&&e.forEach((e=>this._availableLevels[e.level]=!0))}get test(){const e=this;return{get prefetchingEnabled(){return e._prefetchingEnabled},set prefetchingEnabled(t){e._prefetchingEnabled=t},hasTilemap:(t,i,l)=>!!e._tilemapFromCache(t,i,l,e._tmpTilemapDefinition)}}};z._maxPrefetch=4,z._prefetches=new u.Z({initialSize:l._maxPrefetch}),(0,s._)([(0,_.Cb)({constructOnly:!0,type:Number})],z.prototype,"levels",void 0),(0,s._)([(0,g.p)("levels")],z.prototype,"castLevels",null),(0,s._)([(0,_.Cb)({readOnly:!0,type:Number})],z.prototype,"size",null),(0,s._)([(0,_.Cb)({constructOnly:!0,type:Number})],z.prototype,"cacheByteSize",void 0),(0,s._)([(0,_.Cb)({constructOnly:!0})],z.prototype,"layer",void 0),(0,s._)([(0,_.Cb)({constructOnly:!0})],z.prototype,"request",void 0),z=l=(0,s._)([(0,w.j)("esri.layers.support.TilemapCache")],z)},45322:(e,t,i)=>{i.d(t,{d:()=>n,h:()=>s});var l=i(11145);const s={type:l.Z,json:{origins:{service:{read:{source:["tileInfo","minScale","maxScale","minLOD","maxLOD"],reader:n}}}}};function n(e,t,i,s){if(!e)return null;const{minScale:n,maxScale:r,minLOD:a,maxLOD:o}=t;if(null!=a&&null!=o)return s&&s.ignoreMinMaxLOD?l.Z.fromJSON(e):l.Z.fromJSON({...e,lods:e.lods.filter((({level:e})=>null!=e&&e>=a&&e<=o))});if(0!==n&&0!==r){const t=e=>Math.round(1e4*e)/1e4,i=n?t(n):1/0,s=r?t(r):-1/0;return l.Z.fromJSON({...e,lods:e.lods.filter((e=>{const l=t(e.scale);return l<=i&&l>=s}))})}return l.Z.fromJSON(e)}}}]);