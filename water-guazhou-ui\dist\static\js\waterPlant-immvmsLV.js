const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/ScadaPop-B0xTH3SS.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/zhandian-YaGuQZe6.js","static/js/DateFormatter-Bm9a68Ax.js","static/css/ScadaPop-CZ2Y6ymc.css"])))=>i.map(i=>d[i]);
import{d as M,c as N,r as m,ab as R,s as k,o as U,am as V,j as A,g as B,n as E,q as I,i as P,p as j,aa as q,_ as $,aq as z,a3 as G,C as O}from"./index-r0dFAfgr.js";import{w as Y}from"./Point-WxyopZva.js";import{c as H}from"./onemap-CEunQziB.js";import{P as J}from"./index-CcDafpIP.js";import{r as S}from"./chart-wy3NEK2T.js";import{g as K}from"./URLHelper-B9aplt5w.js";const Q={class:"onemap-panel-wrapper"},X={class:"table-box"},Z=M({__name:"waterPlant",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(F,{emit:W}){const D=q(()=>G(()=>import("./ScadaPop-B0xTH3SS.js"),__vite__mapDeps([0,1,2,3,4,5]))),u=W,c=F,f=N(),g=m({group:[{id:"chart",fieldset:{desc:"今日供水量各水源占比图",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:S(),style:{height:"150px"}}]},{fields:[{type:"input",field:"name",appendBtns:[{text:"刷新",perm:!0,click:()=>s()}],onChange:()=>s()}]}],labelPosition:"top",gutter:12}),o=m({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:t,size:e})=>{o.pagination.page=t,o.pagination.limit=e},layout:"total,sizes, jumper"},handleRowClick:t=>T(t),columns:[{width:120,label:"名称",prop:"name",sortable:!0},{width:130,label:"今日供水量(m³)",prop:"todayWaterSupply"},{width:160,label:"更新时间",prop:"lastTime",sortable:!0}]}),_=m({dataList:[],columns:[],pagination:{refreshData:({page:t,size:e})=>{_.pagination.page=t,_.pagination.limit=e,s()}}}),s=async()=>{var t,e,r,l,h,y;o.loading=!0;try{const i=await H({name:(t=f.value)==null?void 0:t.dataForm.name});o.dataList=((e=i.data)==null?void 0:e.data)||[];const b=g.group[0].fields[0],v=(l=(r=i.data)==null?void 0:r.data)==null?void 0:l.reduce((a,p,n,d)=>a+Number(p.todayWaterSupply??"0"),0),w=[],L=((y=(h=i.data)==null?void 0:h.data)==null?void 0:y.map(a=>{var d,C;const p=R(a.todayWaterSupply||0),n=(d=a.location)==null?void 0:d.split(",");if((n==null?void 0:n.length)===2){const x=new Y({longitude:n[0],latitude:n[1],spatialReference:(C=c.view)==null?void 0:C.spatialReference});w.push({id:a.stationId,visible:!1,x:x.x,y:x.y,offsetY:-40,title:a.name,customComponent:k(D),customConfig:{info:{type:"attrs",imageUrl:a.imgs,stationId:a.stationId,scadaUrl:a.scadaUrl}},attributes:{path:c.menu.path,id:a.stationId,row:a},symbolConfig:{url:K("水厂.png")}})}return{name:a.name,value:a.todayWaterSupply??0,valueAlias:p.value.toFixed(2)+p.unit,scale:(v?Number(a.todayWaterSupply??0)/v*100:0)+"%"}}))||[];b&&(b.option=S(L,"m³")),u("addMarks",{windows:w,customWinComp:k(J)})}catch(i){console.dir(i)}o.loading=!1},T=async t=>{u("highlightMark",c.menu,t==null?void 0:t.stationId)};return U(()=>{s()}),V(()=>A().isDark,()=>s()),(t,e)=>{const r=$,l=z;return B(),E("div",Q,[I(r,{ref_key:"refForm",ref:f,config:P(g)},null,8,["config"]),j("div",X,[I(l,{config:P(o)},null,8,["config"])])])}}}),ia=O(Z,[["__scopeId","data-v-ff955057"]]);export{ia as default};
