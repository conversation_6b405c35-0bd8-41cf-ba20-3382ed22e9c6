package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;

import java.util.List;

public interface SoDeviceItemService {
    /**
     * 分页条件查询设备项，
     * 工程设备项代表着 项目/工程/工程合同/工程实施 下所分配的设备类型及其数量
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoDeviceItem> findAllConditional(SoDeviceItemPageRequest request);

    /**
     * 保存设备项
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoDeviceItem save(SoDeviceItemSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoDeviceItem entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量保存设备项
     *
     * @param request 保存请求
     * @return 保存好的数据
     */
    List<SoDeviceItem> saveAll(List<SoDeviceItemSaveRequest> request, SoGeneralSystemScope scope);

    /**
     * 是否可以进行删除
     *
     * @param id 设备项id
     * @return 是否可被删除
     */
    boolean canBeDelete(String id);

}
