package org.thingsboard.server.dao.assay;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.AssayReportDataDTO;
import org.thingsboard.server.dao.model.request.AssayReportRequest;
import org.thingsboard.server.dao.model.sql.assay.*;
import org.thingsboard.server.dao.sql.assay.AssayReportDataAddressMapper;
import org.thingsboard.server.dao.sql.assay.AssayReportDataItemMapper;
import org.thingsboard.server.dao.sql.assay.AssayReportDataMapper;
import org.thingsboard.server.dao.sql.assay.AssayReportTypeMapper;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AssayReportDataServiceImpl implements AssayReportDataService {

    @Autowired
    private AssayReportDataMapper assayReportDataMapper;

    @Autowired
    private AssayReportDataItemMapper assayReportDataItemMapper;

    @Autowired
    private AssayReportDataAddressMapper assayReportDataAddressMapper;

    @Autowired
    private AssayReportTypeMapper assayReportTypeMapper;

    @Autowired
    private AssayReportTypeService assayReportTypeService;

    @Override
    public JSONObject getDataListByTypeId(String typeId) {

        // header
        List<JSONObject> headerList = new ArrayList<>();

        JSONObject jsonObject;

        List<AssayReportAddress> addressList = assayReportTypeService.getAddressList(typeId);
        for (AssayReportAddress assayReportAddress : addressList) {
            jsonObject = new JSONObject();
            jsonObject.put("lable", assayReportAddress.getAddressName());
            jsonObject.put("value", assayReportAddress.getAddressName());

            headerList.add(jsonObject);
        }

        // 值
        List<JSONObject> valueList = new ArrayList<>();
        List<AssayReportItem> itemList = assayReportTypeService.getItemList(typeId);
        for (AssayReportItem assayReportItem : itemList) {
            jsonObject = new JSONObject();
            jsonObject.put("title", assayReportItem.getTitle());
            jsonObject.put("target", assayReportItem.getTarget());
            jsonObject.put("unit", assayReportItem.getUnit());

            jsonObject.put("checkPerson", "");

            for (AssayReportAddress assayReportAddress : addressList) {
                jsonObject.put(assayReportAddress.getAddressName() + ".value", null);
                jsonObject.put(assayReportAddress.getAddressName() + ".status", null);
            }
            valueList.add(jsonObject);
        }

        JSONObject result = new JSONObject();
        result.put("header", headerList);
        result.put("value", valueList);
        return result;
    }

    @Override
    @Transactional
    public void save(AssayReportDataDTO entity, String tenantId) {
        AssayReportData assayReportData = new AssayReportData();
        BeanUtils.copyProperties(entity, assayReportData);

        if (StringUtils.isBlank(assayReportData.getId())) {
            assayReportData.setCreateTime(new Date());
            assayReportData.setTenantId(tenantId);
            assayReportDataMapper.insert(assayReportData);
            // 删除化验内容
            Map deleteMap = new HashMap();
            deleteMap.put("pid", assayReportData.getId());
            assayReportDataItemMapper.deleteByMap(deleteMap);
            assayReportDataAddressMapper.deleteByMap(deleteMap);
        } else {
            assayReportDataMapper.updateById(entity);
        }

        // 化验内容
        String isQualified = "1";
        String isComplete = "1";
        List<JSONObject> dataList = entity.getDataList();
        if (dataList != null && dataList.size() > 0) {
            List<AssayReportDataItem> assayReportDataItemList = new ArrayList<>();
            List<AssayReportDataAddress> assayReportDataAddressList = new ArrayList<>();
            AssayReportDataItem assayReportDataItem;
            AssayReportDataAddress assayReportDataAddress;
            String next;
            Map deleteMap = new HashMap();
            deleteMap.put("pid", assayReportData.getId());
            assayReportDataItemMapper.deleteByMap(deleteMap);
            for (JSONObject jsonObject : dataList) {
                assayReportDataItem = new AssayReportDataItem();
                assayReportDataItem.setTenantId(tenantId);
                assayReportDataItem.setCreateTime(new Date());
                assayReportDataItem.setPid(assayReportData.getId());
                assayReportDataItem.setCheckPerson(jsonObject.getString("checkPerson"));
                assayReportDataItem.setTitle(jsonObject.getString("title"));
                assayReportDataItem.setUnit(jsonObject.getString("unit"));
                assayReportDataItem.setTarget(jsonObject.getString("target"));
                assayReportDataItem.setId(UUID.randomUUID().toString().replace("-", ""));

                assayReportDataItemList.add(assayReportDataItem);

                Iterator<String> iterator = jsonObject.keySet().iterator();
                while (iterator.hasNext()) {
                    next = iterator.next();
                    if (next.contains("status")) {
                        next = next.substring(0, next.indexOf("."));
                        assayReportDataAddress = new AssayReportDataAddress();
                        assayReportDataAddress.setPid(assayReportDataItem.getId());
                        assayReportDataAddress.setCreateTime(new Date());
                        assayReportDataAddress.setAddress(next);
                        assayReportDataAddress.setTenantId(tenantId);
                        assayReportDataAddress.setStatus(jsonObject.getString(next + ".status"));
                        assayReportDataAddress.setValue(jsonObject.getString(next + ".value"));

                        // 是否合格
                        if (!"1".equals(assayReportDataAddress.getStatus()) && !"2".equals(assayReportDataAddress.getStatus())) {
                            isQualified = "0";
                        }
                        // 是否完成
                        if (StringUtils.isBlank(assayReportDataAddress.getStatus())) {
                            isComplete = "0";
                        }

                        assayReportDataAddressList.add(assayReportDataAddress);
                    }
                }
            }

            assayReportDataItemMapper.batchInsert(assayReportDataItemList);
            if (assayReportDataAddressList.size() > 0) {
                List<String> ids = assayReportDataAddressList.stream().map(a -> a.getPid()).collect(Collectors.toList());
                QueryWrapper<AssayReportDataAddress> queryWrapper = new QueryWrapper();
                queryWrapper.in("pid", ids);
                assayReportDataAddressMapper.delete(queryWrapper);
                assayReportDataAddressMapper.batchInsert(assayReportDataAddressList);
            }
        }
        // 更新状态
        assayReportData.setIsComplete(isComplete);
        assayReportData.setIsQualified(isQualified);
        assayReportDataMapper.updateById(assayReportData);
    }

    @Override
    public PageData<AssayReportData> findList(AssayReportRequest request) {
        Page<AssayReportData> pageRequest = new Page<>(request.getPage(), request.getSize());

        IPage<AssayReportData> pageResult = assayReportDataMapper.findList(pageRequest, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public JSONObject getDataListByPid(String pid) {
        // header
        List<JSONObject> headerList = new ArrayList<>();
        JSONObject jsonObject;

        // 值
        List<JSONObject> valueList = new ArrayList<>();
        List<AssayReportDataItem> itemList = assayReportDataItemMapper.getListByPid(pid);
        List<String> pidList = itemList.stream().map(a -> a.getId()).collect(Collectors.toList());
        List<AssayReportDataAddress> assayReportDataAddresses = new ArrayList<>();
        if (pidList.size() > 0) {
            assayReportDataAddresses = assayReportDataAddressMapper.getListByPidIn(pidList);
        }
        Map<String, List<AssayReportDataAddress>> addressMap = new HashMap<>();
        Set<String> headerSet = new LinkedHashSet<>();
        for (AssayReportDataAddress assayReportDataAddress : assayReportDataAddresses) {
            if (addressMap.get(assayReportDataAddress.getPid()) == null) {
                addressMap.put(assayReportDataAddress.getPid(), new ArrayList<>());
            }
            headerSet.add(assayReportDataAddress.getAddress());
            addressMap.get(assayReportDataAddress.getPid()).add(assayReportDataAddress);
        }

        Iterator<String> iterator = headerSet.iterator();
        while (iterator.hasNext()) {
            String next = iterator.next();
            jsonObject = new JSONObject();
            jsonObject.put("lable", next);
            jsonObject.put("value", next);

            headerList.add(jsonObject);
        }

        for (AssayReportDataItem assayReportItem : itemList) {
            jsonObject = new JSONObject();
            jsonObject.put("title", assayReportItem.getTitle());
            jsonObject.put("target", assayReportItem.getTarget());
            jsonObject.put("unit", assayReportItem.getUnit());

            jsonObject.put("checkPerson", assayReportItem.getCheckPerson());
            List<AssayReportDataAddress> addressList = addressMap.get(assayReportItem.getId());
            for (AssayReportDataAddress assayReportAddress : addressList) {
                jsonObject.put(assayReportAddress.getAddress() + ".value", assayReportAddress.getValue());
                jsonObject.put(assayReportAddress.getAddress() + ".status", assayReportAddress.getStatus());
            }
            valueList.add(jsonObject);
        }

        JSONObject result = new JSONObject();
        result.put("header", headerList);
        result.put("value", valueList);
        return result;
    }

    @Override
    public void remove(List<String> ids) {
        assayReportTypeMapper.deleteBatchIds(ids);
    }
}
