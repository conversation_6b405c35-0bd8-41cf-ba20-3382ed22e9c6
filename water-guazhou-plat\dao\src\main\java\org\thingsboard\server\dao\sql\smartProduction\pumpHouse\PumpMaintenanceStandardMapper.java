package org.thingsboard.server.dao.sql.smartProduction.pumpHouse;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpMaintenanceStandard;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpMaintenanceStandardPageRequest;

@Mapper
public interface PumpMaintenanceStandardMapper extends BaseMapper<PumpMaintenanceStandard> {
    IPage<PumpMaintenanceStandard> findByPage(PumpMaintenanceStandardPageRequest request);

    boolean update(PumpMaintenanceStandard entity);



}
