<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData"></SLTree>
    </template>
    <SLCard class="card-search">
      <div class="card-wrapper">
        <InlineForm ref="refSearch" :config="FormConfig"></InlineForm>
      </div>
    </SLCard>
    <SLCard class="card-chart" title="实时数据">
      <VChart
        ref="refChart"
        :theme="useAppStore().isDark ? 'darkblue' : 'light'"
        :option="chartOption"
      ></VChart>
    </SLCard>
    <CardTable
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    ></CardTable>
  </TreeBox>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import TreeBox from '../layout/treeOrDetailFrame/TreeBox.vue';
import {
  useChart,
  useDevice,
  useProject,
  useSearch,
  useTable
} from './composible/useHooks';
import { IECharts } from '@/plugins/echart';
import { TrueExcel } from '@/utils/exportExcel';
import { useAppStore } from '@/store';

const refSearch = ref<IInlineFormIns>();
const refChart = ref<IECharts>();
const refTable = ref<ICardTableIns>();
const { chartOption, refreshChart } = useChart(refChart);
const { TableConfig, refreshTable } = useTable();
const { FormConfig, initFirstLineOfFilters } = useSearch({
  refSearch,
  refTable,
  export: true,
  withInterval: false,
  searchTime: true,
  exportCall: () => handleExport(),
  refreshCall: (data) => {
    refreshChart(data);
    refreshTable(data);
  }
});
const handleExport = () => {
  const excel = new TrueExcel();
  const table = refTable.value?.getTable();
  const img = getChartImg(refChart);
  excel.addImage(img);
  excel.addElTable(table);
  excel.export();
};
const getChartImg = (chartIns?: any) => {
  if (!chartIns?.value) return;
  const width = chartIns.value.getWidth();
  const height = chartIns.value.getHeight();
  const img = {
    src: chartIns.value.getDataURL({
      pixelRatio: window.devicePixelRatio || 1
    }),
    width,
    height
  };
  return img;
};
const { deviceList, getDeviceData } = useDevice();
const { TreeData, refreshProject } = useProject(async (data) => {
  TreeData.currentProject = data;
  await getDeviceData(data?.id);
  initFirstLineOfFilters(deviceList.value || []);
});
onMounted(async () => {
  await refreshProject();
  refreshChart();
});
</script>
<style lang="scss" scoped>
.card-wrapper {
  padding: 20px 12px 0;
}
.card-table,
.card-chart {
  height: 400px;
}
.card-search,
.card-chart {
  margin-bottom: 15px;
}
</style>
