/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.constants;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.constantsAttribute.ConstantsAttribute;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.constantsAttribute.ConstantsAttributeDao;
import org.thingsboard.server.dao.entity.AbstractEntityService;
import org.thingsboard.server.dao.model.sql.base.ConstantsAttributeList;
import org.thingsboard.server.dao.sql.base.ConstantsMapper;
import org.thingsboard.server.dao.util.imodel.query.base.ConstantsAttributePageRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;


@Service
@Slf4j
public class ConstantsAttributeServiceImpl extends AbstractEntityService implements ConstantsService {

    @Autowired
    private ConstantsAttributeDao constantsAttributeDao;

    @Autowired
    private ConstantsMapper constantsMapper;


    @Override
    public List<ConstantsAttribute> saveConstantsAttribute(TenantId tenantId,List<ConstantsAttribute> constantsAttributes) {
        List<ConstantsAttribute> result = new ArrayList<>();
        for(ConstantsAttribute constantsAttribute:constantsAttributes){
            List<ConstantsAttribute> list = null;
            try {
                list = getConstantsByTypeAndKey(constantsAttribute.getType(), constantsAttribute.getKey()).get();
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (ExecutionException e) {
                e.printStackTrace();
            }
            if (list!=null&&!list.isEmpty())
                constantsAttribute.setId(list.get(0).getId());
            result.add(constantsAttributeDao.save(tenantId,constantsAttribute));
        }
        return result;
    }

    @Override
    public ListenableFuture<List<ConstantsAttribute>> getConstantsByTypeAndKey(String type, String key) {
        if (type == null && key == null) {
            return constantsAttributeDao.getAll();
        } else if (type != null && key != null)
            return constantsAttributeDao.getConstantsAttributeByTypeAndKey(type, key);
        else if (type != null && key == null)
            return constantsAttributeDao.getConstantsAttributeByType(type);
        else
            return constantsAttributeDao.getConstantsAttributeByKey(key);
    }

//    @Override
//    public ListenableFuture<List<ConstantsAttribute>> getConstantsByType(String type) {
//        return constantsAttributeDao.getConstantsAttributeByType(type);
//    }

    @Override
    public ListenableFuture<List<ConstantsAttribute>> getAll() {
        return constantsAttributeDao.getAll();
    }

    @Override
    public List<ConstantsAttribute> findConstantsByType(String type) {
        try {
            return constantsAttributeDao.getConstantsAttributeByType(type).get();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public IPage<ConstantsAttributeList> selectConstantsList(ConstantsAttributePageRequest constantsAttribute) {
        return constantsMapper.selectConstantsList(constantsAttribute);
    }
}
