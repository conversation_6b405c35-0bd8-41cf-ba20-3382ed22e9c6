import{两 as f,渐 as l}from"./charts-m9UAsyIq.js";import{l as p,L as d}from"./smartDecisionData-BKd4shVG.js";import{u as m}from"./useDetector-BRcb7GRN.js";import{d as u,c as n,o as h,ay as C,g as v,n as k,p as c,q as _,i as e,C as x}from"./index-r0dFAfgr.js";const L={class:"bottom-box",title:"漏损构成"},V={class:"chart"},g={class:"chart"},y=u({__name:"LSGC",setup(B){const a=n(),s=n(),i=m();return h(()=>{i.listenToMush(document.documentElement,()=>{var t,o;(t=a.value)==null||t.resize(),(o=s.value)==null||o.resize()})}),(t,o)=>{const r=C("VChart");return v(),k("div",L,[c("div",V,[_(r,{ref_key:"refChart1",ref:a,option:e(f)(e(p))},null,8,["option"])]),c("div",g,[_(r,{ref_key:"refChart2",ref:s,option:e(l)(e(d))},null,8,["option"])])])}}}),G=x(y,[["__scopeId","data-v-fe7da53f"]]);export{G as default};
