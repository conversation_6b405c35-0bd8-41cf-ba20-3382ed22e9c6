package org.thingsboard.server.dao.model.sql.workOrder;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("work_order_web_setting")
public class WorkOrderWebSetting {

    private String id;

    private String label;

    private String apiKey;

    private String code;

    private String status;

    private String callBackUrl;

    private String remark;

    private String workResource;

    private String tenantId;

    private Date createTime;


}
