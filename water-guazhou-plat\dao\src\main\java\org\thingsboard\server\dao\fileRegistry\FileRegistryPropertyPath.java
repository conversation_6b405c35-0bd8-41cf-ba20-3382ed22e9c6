package org.thingsboard.server.dao.fileRegistry;

import lombok.Getter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
class FileRegistryPropertyPath implements InitializingBean {
    @Value("${file-registry.path}")
    private String basePath;

    @Value("${file-registry.base-url}")
    private String baseURL;


    @Override
    public void afterPropertiesSet() {
        if (!baseURL.endsWith("/")) {
            baseURL = baseURL + "/";
        }
        if (!basePath.endsWith("/")) {
            basePath = basePath + "/";
        }
    }

    public String resolveDirectoryPath(String host, String label) {
        return combineDirectoryPath(basePath, host, label);
    }

    public String resolveRelativeDirectoryPath(String host, String label) {
        return combineDirectoryPath("", host, label);
    }

    private String combineDirectoryPath(String basePath, String host, String label) {
        String path = basePath;
        if (host != null) {
            path = path + host + "/";
        }

        if (label != null) {
            path = path + label + "/";
        }

        return path;
    }

}
