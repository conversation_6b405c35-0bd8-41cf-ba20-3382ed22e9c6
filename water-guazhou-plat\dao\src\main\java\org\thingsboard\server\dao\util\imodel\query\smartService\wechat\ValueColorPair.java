package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ValueColorPair {
    private String value;

    @JSONField(serialize = false)
    private String color;

    public ValueColorPair(Object value, String color) {
        String result = value + "";
        if (value == null) {
            result = "";
        }

        this.value = result;
        this.color = color;
    }

    public ValueColorPair() {

    }

    public ValueColorPair(Object value) {
        String result = value + "";
        if (value == null) {
            result = "";
        }

        this.value = result;
    }
}
