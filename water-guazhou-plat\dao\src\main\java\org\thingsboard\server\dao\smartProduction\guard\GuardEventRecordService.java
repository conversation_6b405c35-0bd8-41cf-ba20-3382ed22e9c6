package org.thingsboard.server.dao.smartProduction.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardEventRecord;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardEventRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardEventRecordSaveRequest;

public interface GuardEventRecordService {
    IPage<GuardEventRecord> findAllConditional(GuardEventRecordPageRequest request);

    GuardEventRecord save(GuardEventRecordSaveRequest entity);

    boolean delete(String id);

    /**
     * 删除值班日志下的所有值班事件
     *
     * @param recordId 日志id
     */
    boolean deleteByRecord(String recordId);

}
