package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.plan.PlanDetail;
import org.thingsboard.server.dao.sql.plan.PlanDetailMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanDetailSaveRequest;

import java.util.List;

@Service
public class PlanDetailServiceImpl implements PlanDetailService {
    @Autowired
    private PlanDetailMapper mapper;

    @Override
    public IPage<PlanDetail> findAllConditional(PlanDetailPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public List<PlanDetail> saveAll(List<PlanDetailSaveRequest> details) {
        return QueryUtil.saveOrUpdateBatchByRequest(details, mapper::saveAll, mapper::saveAll);
    }

    @Override
    public boolean update(PlanDetail entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        return QueryUtil.deleteBatch(idList, mapper::deleteBatchIds);
    }

    @Override
    public boolean deleteAllByMainId(String id, List<String> idList) {
        if (id == null) {
            return true;
        }
        return mapper.deleteAllByMainId(id, idList) > 0;
    }

    @Override
    public boolean removeAllByMainOnIdNotIn(String id, List<String> idList) {
        return deleteAllByMainId(id, idList);
    }

}
