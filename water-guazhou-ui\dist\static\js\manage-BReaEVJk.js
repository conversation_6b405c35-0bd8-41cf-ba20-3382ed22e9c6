import{m as o}from"./index-r0dFAfgr.js";function r(t){return o({url:"/api/so/project/type",method:"get",params:t})}function s(t){return o({url:"/api/so/project/type",method:"post",data:t})}function c(t){return o({url:"/api/so/construction",method:"get",params:t})}function i(t){return o({url:"/api/so/construction",method:"post",data:t})}function u(t,n){return o({url:`/api/so/construction/${t}/device`,method:"get",params:n})}function p(t,n){return o({url:`/api/so/construction/${t}/device`,method:"post",data:n})}function a(t){return o({url:"/api/so/constructionDesign",method:"get",params:t})}function l(){return o({url:"/api/so/construction/export/excel",method:"get",responseType:"blob"})}function m(t){return o({url:"/api/so/constructionDesign",method:"post",data:t})}function d(t){return o({url:`/api/so/constructionDesign/${t}/complete`,method:"post"})}function C(){return o({url:"/api/so/constructionDesign/export/excel",method:"get",responseType:"blob"})}function g(t){return o({url:"/api/so/constructionEstimate",method:"get",params:t})}function h(t){return o({url:"/api/so/constructionEstimate",method:"post",data:t})}function f(t){return o({url:`/api/so/constructionEstimate/${t}/complete`,method:"post"})}function x(){return o({url:"/api/so/constructionEstimate/export/excel",method:"get",responseType:"blob"})}function A(t){return o({url:"/api/so/constructionAccept",method:"get",params:t})}function y(t){return o({url:"/api/so/constructionAccept",method:"post",data:t})}function b(t){return o({url:`/api/so/constructionAccept/${t}/complete`,method:"post"})}function E(){return o({url:"/api/so/constructionAccept/export/excel",method:"get",responseType:"blob"})}function T(t){return o({url:"/api/so/constructionSettlement",method:"get",params:t})}function D(t){return o({url:"/api/so/constructionSettlement",method:"post",data:t})}function $(t){return o({url:`/api/so/constructionSettlement/${t}/complete`,method:"post"})}function L(){return o({url:"/api/so/constructionSettlement/export/excel",method:"get",responseType:"blob"})}function v(t){return o({url:"/api/so/constructionArchive",method:"get",params:t})}function V(t){return o({url:"/api/so/constructionArchive",method:"post",data:t})}function S(){return o({url:"/api/so/constructionArchive/export/excel",method:"get",responseType:"blob"})}function w(t){return o({url:"/api/so/constructionVisa",method:"get",params:t})}function F(t){return o({url:"/api/so/constructionVisa",method:"post",data:t})}function j(t){return o({url:`/api/so/constructionVisa/${t}/complete`,method:"post"})}function G(){return o({url:"/api/so/constructionVisa/export/global/excel",method:"get",responseType:"blob"})}function P(t){return o({url:"/api/so/constructionVisa/export/excel",method:"get",params:{constructionCode:t},responseType:"blob"})}function I(t){return o({url:"/api/so/constructionContract",method:"get",params:t})}function O(t){return o({url:"/api/so/constructionContract",method:"post",data:t})}function _(t){return o({url:"/api/so/constructionContract/type",method:"get",params:t})}function k(t){return o({url:"/api/so/constructionContract/type",method:"post",data:t})}function q(t){return o({url:"/api/so/constructionContract/export/global/excel",method:"get",params:{constructionCode:t},responseType:"blob"})}function z(){return o({url:"/api/so/constructionContract/export/global/excel",method:"get",responseType:"blob"})}function B(t){return o({url:`/api/so/constructionContract/${t}/complete`,method:"post"})}function H(t){return o({url:"/api/so/constructionExpense",method:"get",params:t})}function J(t){return o({url:"/api/so/constructionExpense",method:"post",data:t})}function K(t){return o({url:"/api/so/constructionExpense/export/excel",method:"get",params:{constructionCode:t},responseType:"blob"})}function M(){return o({url:"/api/so/constructionExpense/export/global/excel",method:"get",responseType:"blob"})}function N(t){return o({url:`/api/so/constructionExpense/${t}/complete`,method:"post"})}function Q(t){return o({url:"/api/so/constructionApply",method:"get",params:t})}function R(t){return o({url:`/api/so/constructionApply/${t}/complete`,method:"post"})}function U(t){return o({url:"/api/so/constructionApply",method:"post",data:t})}function W(t){return o({url:`/api/so/constructionApply/${t}`,method:"delete"})}function X(){return o({url:"/api/so/constructionApply/export/global/excel",method:"get",responseType:"blob"})}function Y(t){return o({url:"/api/so/constructionApply/export/excel",method:"get",params:{constructionCode:t},responseType:"blob"})}function Z(t){return o({url:`/api/so/construction/completionInfo/${t}`,method:"get"})}function tt(t){return o({url:"/api/so/constructionDesignAmend",method:"post",data:t})}function ot(t){return o({url:"/api/so/constructionDesignAmend",method:"get",params:t})}function nt(t){return o({url:`/api/so/constructionDesignAmend/${t}`,method:"delete"})}function et(t){return o({url:"/api/so/constructionContractAmend",method:"get",params:t})}function rt(t){return o({url:"/api/so/constructionContractAmend",method:"post",data:t})}function st(t){return o({url:`/api/so/constructionContractAmend/${t}`,method:"delete"})}function ct(t){return o({url:"/api/so/constructionContract/simple",method:"get",params:{constructionCode:t}})}function it(t){return o({url:"/api/so/constructionApplyFlow",method:"post",data:t})}function ut(t){return o({url:"/api/so/constructionApplyFlow",method:"get",params:t})}function pt(t){return o({url:`/api/so/constructionApplyFlow/${t}`,method:"delete"})}export{i as $,q as A,I as B,J as C,ct as D,M as E,N as F,K as G,H,C as I,d as J,m as K,a as L,R as M,U as N,W as O,X as P,Y as Q,Q as R,x as S,f as T,h as U,g as V,L as W,$ as X,D as Y,T as Z,l as _,k as a,p as a0,F as a1,G as a2,j as a3,P as a4,w as a5,_ as b,c,Z as d,tt as e,nt as f,r as g,ot as h,rt as i,st as j,et as k,it as l,pt as m,ut as n,E as o,s as p,b as q,y as r,A as s,S as t,V as u,v,O as w,u as x,z as y,B as z};
