package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 报装流程类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_install_project_attachment")
@Data
public class ProjectAttachment {
    @TableId
    private String id;

    private String mainId;

    private String childId;

    private String name;

    private Integer num;

    private String file;

    private String remark;

    private String tenantId;

    private String code;

}
