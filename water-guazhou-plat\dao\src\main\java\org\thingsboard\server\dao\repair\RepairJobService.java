package org.thingsboard.server.dao.repair;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.RepairJobCEntity;
import org.thingsboard.server.dao.model.sql.RepairJobEntity;
import org.thingsboard.server.dao.model.sql.RepairJobTriggerEntity;

import java.util.List;

public interface RepairJobService {
    RepairJobEntity detail(String id, TenantId tenantId);

    PageData<RepairJobEntity> findList(int page, int size, String name, String deviceId, User currentUser);

    void remove(List<String> ids);

    RepairJobEntity faultReport(RepairJobEntity entity, User currentUser);

    RepairJobEntity save(RepairJobEntity job);

    void save(List<RepairJobCEntity> childList);

    List<RepairJobEntity> findJobByType(String type);

    RepairJobTriggerEntity findTrigger(String mainId);

    void save(RepairJobTriggerEntity jobC);

    RepairJobEntity findById(String contentId);

    Object groupByPriority(String deviceId);
}
