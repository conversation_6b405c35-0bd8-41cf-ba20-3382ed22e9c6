package org.thingsboard.server.dao.util.imodel.query.annotations;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Documented
@Deprecated
public @interface QueryLike {
    /**
     * 是否使用or查询 默认为false
     * @return 是否使用or查询
     */
    @AliasFor("or")
    boolean value() default false;

    @AliasFor("value")
    boolean or() default false;

    String prefix() default "%";

    String suffix() default "%";
}
