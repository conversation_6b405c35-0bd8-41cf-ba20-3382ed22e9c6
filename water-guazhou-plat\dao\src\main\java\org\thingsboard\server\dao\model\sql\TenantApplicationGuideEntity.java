package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName("tenant_application_guide")
@NoArgsConstructor
@AllArgsConstructor
public class TenantApplicationGuideEntity {

    private String id;

    private String applicationId;

    @TableField(exist = false)
    private String applicationName;

    private String leftImg;

    private String leftContent;

    private String rightImg;

    private String rightContent;

    private String creator;

    private Date createTime;

    private String tenantId;

}
