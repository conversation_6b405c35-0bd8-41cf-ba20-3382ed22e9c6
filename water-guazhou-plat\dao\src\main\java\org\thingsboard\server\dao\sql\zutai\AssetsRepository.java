package org.thingsboard.server.dao.sql.zutai;

import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.zutai.AssetsEntity;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
public interface AssetsRepository extends CrudRepository<AssetsEntity, String> {
    List findAllByIdLikeAndProjectIdLikeAndTenantIdOrderByCreateTimeDesc(String id, String projectId, String tenantId);
}
