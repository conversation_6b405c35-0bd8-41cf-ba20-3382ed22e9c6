<!-- 营收用户管理 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refForm"
      :config="FormConfig"
    />
    <DialogForm
      ref="refUpload"
      :config="UploadConfig"
    >
    </DialogForm>
  </div>
</template>
<script lang="ts" setup>
import {
  DeleteDMAUsers,
  DownloadDMAUserTemplate,
  ExportDMAUsers,
  GetDMAUsers,
  ImportDMAUsers,
  PostDMAUser
} from '@/api/mapservice/dma'
import { getSelectOptions } from '@/api/operatingCharges'
import ImportButton from '@/components/Form/ImportButton.vue'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'
import { usePartition } from '@/hooks/arcgis'
import { formatterDateTime } from '@/utils/GlobalHelper'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { saveAs } from '@/utils/printUtils'

const partition = usePartition()
const refForm = ref<IDialogFormIns>()
const refUpload = ref<IDialogFormIns>()
const refSearch = ref<ICardSearchIns>()

const SearchConfig = reactive<ISearch>({
  defaultParams: {
    itemType: '',
    type: 'month',
    date: moment().format()
  },
  filters: [
    {
      type: 'input',
      label: '用户名称',
      field: 'custName',
      placeholder: '请输入用户姓名'
    },
    {
      type: 'input',
      label: '营收户号',
      field: 'custCode',
      placeholder: '请输入营收户号'
    },
    {
      type: 'select',
      field: 'waterCategory',
      width: 90,
      options: [],
      label: '用水类型',
      placeholder: '请选择',
      autoFillOptions: async config => {
        const res = await getSelectOptions('WaterCategoryType')
        config.options = res || []
      }
    },
    {
      type: 'input',
      label: '抄表员',
      field: 'copyMeterUser',
      placeholder: '请输入抄表员'
    },
    {
      type: 'input',
      label: '营收所',
      field: 'businessHall',
      placeholder: '请输入营收所'
    },
    {
      type: 'select-tree',
      label: '所属分区',
      field: 'partitionId',
      placeholder: '请选择',
      options: computed(() => partition.Tree.value) as any
    },
    {
      type: 'select',
      label: '是否挂接',
      field: 'isMount',
      options: [
        { label: '全部', value: '' },
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      placeholder: '请选择'
    },
    {
      type: 'input',
      label: '地址',
      field: 'address',
      placeholder: '请输入地址'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '添加',
          iconifyIcon: 'ep:circle-plus',
          click: () => handleAou()
        },
        {
          perm: true,
          text: '导入',
          type: 'warning',
          iconifyIcon: 'ep:upload',
          click: () => {
            refUpload.value?.openDialog()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'primary',
          iconifyIcon: 'ep:download',
          click: () => refreshData(true)
        }
      ]
    }
  ]
})
// 数据列表配置
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  dataList: [],
  columns: [
    { label: '用户名称', minWidth: 120, prop: 'custName' },
    { label: '营收户号', minWidth: 120, prop: 'custCode' },
    { label: '联系方式', minWidth: 120, prop: 'phone' },
    { label: '用水类型', minWidth: 120, prop: 'waterCategory' },
    { label: '册本编号', minWidth: 160, prop: 'meterBookCode' },
    { label: '册本名称', minWidth: 160, prop: 'meterBookName' },
    { label: '抄表人', minWidth: 120, prop: 'copyMeterUser' },
    { label: '地址', minWidth: 120, prop: 'address' },
    { label: '营业所', minWidth: 120, prop: 'businessHall' },
    { label: '挂接分区', minWidth: 140, prop: 'partitionName' },
    {
      label: '添加日期',
      minWidth: 160,
      prop: 'createTime',
      formatter: (row: any, value: any) => {
        return value ? moment(value).format(formatterDateTime) : ''
      }
    }
  ],
  operations: [
    {
      perm: true,
      text: '修改',
      iconifyIcon: 'ep:edit',
      click: row => handleAou(row)
    },
    {
      perm: true,
      text: '删除',
      type: 'danger',
      iconifyIcon: 'ep:delete',
      click: row => handleDelete(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
// 数据表单弹框配置
const FormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  title: '营收用户信息',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '营收户号',
          field: 'custCode',
          rules: [{ required: true, message: '请填写营收户号' }]
        },
        {
          type: 'select-tree',
          label: '所属分区',
          field: 'partitionId',
          options: computed(() => partition.Tree.value) as any
        },
        {
          type: 'input',
          label: '抄表员',
          field: 'copyMeterUser'
        },
        {
          type: 'input',
          label: '营收所',
          field: 'businessHall'
        }
      ]
    }
  ],
  submit: async params => {
    try {
      FormConfig.submitting = true
      const submitParams = {
        ...params
      }
      const res = await PostDMAUser(submitParams)
      if (res.data.code === 200) {
        SLMessage.success('操作成功')
        refreshData()
        refForm.value?.closeDialog()
      } else {
        SLMessage.error(res.data.message)
      }
    } catch (error) {
      SLMessage.error('操作失败')
    }
    FormConfig.submitting = false
  }
})
const UploadConfig = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  title: '导入营收用户信息',
  group: [
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '下载导入模板',
              type: 'default',
              click: () => DownloadDMAUserTemplate()
            },
            {
              perm: true,
              component: shallowRef(ImportButton),
              iconifyIcon: 'ep:upload',
              type: 'primary',
              click: formData => {
                SLConfirm('将上传数据并进行解析，确定上传？', '提示信息').then(
                  async () => {
                    try {
                      const res = await ImportDMAUsers(formData)
                      if (res.data.code === 200) {
                        SLMessage.success('导入成功')
                        refreshData()
                        refUpload.value?.closeDialog()
                      } else {
                        SLMessage.error(res.data.message)
                      }
                    } catch (error) {
                      SLMessage.error('上传失败')
                    }
                  }
                )
              }
            }
          ]
        }
      ]
    }
  ],
  cancel: false
})
// 添加修改
const handleAou = (row?: any) => {
  FormConfig.defaultValue = {
    ...(row || {})
  }
  refForm.value?.openDialog()
}

// 删除配置
const handleDelete = (row?: any) => {
  const ids = row
    ? [row.id]
    : TableConfig.selectList?.map(item => item.id) || []
  if (!ids.length) {
    SLMessage.warning('请先选择要删除的数据')
    return
  }
  SLConfirm('确定删除?', '提示信息')
    .then(async () => {
      try {
        await DeleteDMAUsers(ids)
        SLMessage.success('删除成功')
        refreshData()
      } catch (error) {
        SLMessage.error('删除失败')
      }
    })
    .catch(() => {
      //
    })
}
// 获取配置数据列表
const refreshData = async (isExport?: boolean) => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const params = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query
    }

    const res = await GetDMAUsers(params)
    const data = res.data?.data
    TableConfig.dataList = data?.data || []
    TableConfig.pagination.total = data?.total || 0
    if (isExport) {
      SLMessage.warning('导出中...')
      const res = await ExportDMAUsers({
        ...params,
        page: 1,
        size: TableConfig.pagination.total || 0
      })
      saveAs(res.data, '营收用户列表')
    }
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
onMounted(async () => {
  refreshData()
  partition.getTree()
})
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 100px);
  width: 100%;
}
</style>
