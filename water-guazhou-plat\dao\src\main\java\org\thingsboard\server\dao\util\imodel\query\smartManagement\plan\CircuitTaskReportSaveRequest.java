package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitTaskReport;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class CircuitTaskReportSaveRequest extends SaveRequest<CircuitTaskReport> {
    // 类型：关键点、设备、专项设备
    private String type;

    // 任务编号
    private String task_code;

    // 关键点/设备/专项设备的名称
    private String name;

    // [已废弃]设备类型，仅设备和专项设备该字段有值
    // 设备Id/关键点Id
    private String deviceType;

    // 是否到位、专项设备
    private Boolean isSettle = false;

    // 是否反馈
    private Boolean isFallback = false;

    @Override
    protected CircuitTaskReport build() {
        CircuitTaskReport entity = new CircuitTaskReport();
        commonSet(entity);
        return entity;
    }

    @Override
    protected CircuitTaskReport update(String id) {
        CircuitTaskReport entity = new CircuitTaskReport();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(CircuitTaskReport entity) {
        entity.setType(type);
        entity.setTaskCode(task_code);
        entity.setName(name);
        entity.setDeviceType(deviceType);
        entity.setIsSettle(isSettle);
        entity.setIsFallback(isFallback);
        entity.setTenantId(tenantId());
    }
}