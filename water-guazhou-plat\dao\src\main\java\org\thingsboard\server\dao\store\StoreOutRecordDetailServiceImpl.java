package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetail;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetailResponse;
import org.thingsboard.server.dao.sql.department.StoreOutRecordDetailMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordDetailSaveRequest;

import java.util.List;

@Service
public class StoreOutRecordDetailServiceImpl implements StoreOutRecordDetailService {
    @Autowired
    private StoreOutRecordDetailMapper mapper;

    @Autowired
    private DeviceStorageJournalService storageJournalService;


    @Override
    public IPage<StoreOutRecordDetailResponse> findAllConditional(StoreOutRecordDetailPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public List<StoreOutRecordDetail> saveAll(List<StoreOutRecordDetailSaveRequest> items) {
        return QueryUtil.saveOrUpdateBatchByRequest(items, mapper::saveAll, mapper::saveAll);
    }

    @Override
    public boolean update(StoreOutRecordDetail entity) {
        return mapper.update(entity);
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        storageJournalService.checkinViaStoreOut(id);
        return mapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public boolean deleteAll(List<String> idList) {
        storageJournalService.checkinAllViaStoreOut(idList);
        return QueryUtil.deleteBatch(idList, mapper::deleteBatchIds);
    }

    @Override
    public boolean removeAllByMainOnIdNotIn(String id, List<String> idList) {
        return mapper.removeAllByMainOnIdNotIn(id, idList) > 0;
    }

    @Override
    public List<String> differentIdListTo(String mainId, List<String> excludeList) {
        return mapper.differentIdListTo(mainId, excludeList);
    }

    @Override
    public boolean checkInStorages(String id, List<String> itemIdList) {
        List<String> strings = differentIdListTo(id, itemIdList);
        return storageJournalService.checkinAllViaStoreOut(strings);
    }

}
