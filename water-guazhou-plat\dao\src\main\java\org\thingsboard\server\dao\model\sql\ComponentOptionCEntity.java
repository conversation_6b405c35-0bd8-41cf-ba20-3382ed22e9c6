package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.COMPONENT_STORAGE_OPTION_C_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class ComponentOptionCEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.COMPONENT_STORAGE_OPTION_C_MAIN_ID)
    private String mainId;

    @Column(name = ModelConstants.COMPONENT_STORAGE_OPTION_C_COMPONENT_ID)
    private String componentId;

    @Column(name = ModelConstants.COMPONENT_STORAGE_OPTION_C_ORDER_NUMBER)
    private Integer orderNumber;

    @Column(name = ModelConstants.COMPONENT_STORAGE_OPTION_C_NUMBER)
    private Long number;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private ComponentStorageEntity component;


}
