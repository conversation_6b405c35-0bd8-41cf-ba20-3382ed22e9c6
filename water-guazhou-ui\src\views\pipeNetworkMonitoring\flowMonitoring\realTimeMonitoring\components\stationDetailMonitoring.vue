<template>
  <SLCard class="wrapper-content" title=" " overlay>
    <template #title>
      <Tabs v-model="state.activeName" :config="tabsConfig"></Tabs>
    </template>
    <div v-if="state.activeName === 'status'" class="content">
      <div class="top">
        <SLCard title="" overlay class="card-table">
          <!-- <template #title>
              <Tabs v-model="state.groupTab" :config="groupTabsConfig"></Tabs>
            </template> -->
          <div ref="echartsDiv" class="chart-box">
            <VChart
              ref="refGaugeChart"
              :theme="appStore.isDark ? 'dark' : 'light'"
              :option="state.gaugeCarOption"
            >
            </VChart>
          </div>
          <!-- <FormTable :config="baseTableConfig"></FormTable> -->
        </SLCard>
        <SLCard title="数据详情" overlay>
          <div class="table-box">
            <AttrTable
              :data="state.detailData"
              :columns="state.detailColumns"
            ></AttrTable>
          </div>
        </SLCard>
      </div>
      <div class="bottom">
        <SLCard title="图表分析" overlay>
          <div ref="echartsDiv" class="chart-box">
            <VChart
              ref="refChart"
              :theme="appStore.isDark ? 'dark' : 'light'"
              :option="state.chartOption"
            ></VChart>
          </div>
        </SLCard>
        <!-- <SLCard class="chart-box" title="现场实景" overlay>
          </SLCard>
          <SLCard class="chart-box" title="水源信息" overlay>
          </SLCard>
          <SLCard class="chart-box" title="报警信息" overlay>
            <FormTable :config="alarmTableConfig"></FormTable>
          </SLCard> -->
      </div>
    </div>

    <SLCard
      v-if="state.activeName === 'search'"
      class="wrapper-content content1"
      title=" "
      overlay
    >
      <template #title>
        <Search ref="cardSearch" :config="cardSearchConfig" />
      </template>
      <el-tabs v-model="state.searchActiveName">
        <el-tab-pane label="列表模式" name="list">
          <!-- 列表模式 -->
          <FormTable
            ref="refTable"
            :config="dataTableConfig"
            class="card-table"
          ></FormTable>
        </el-tab-pane>
        <el-tab-pane label="图表模式" name="echarts">
          <div ref="echartsDiv" class="chart-box">
            <!-- 图表模式 -->
            <VChart
              ref="refChart1"
              :theme="appStore.isDark ? 'dark' : 'light'"
              :option="state.chartOption1"
            ></VChart>
          </div>
        </el-tab-pane>
      </el-tabs>
    </SLCard>
    <SLCard
      v-if="state.activeName === 'alarm'"
      class="wrapper-content content1"
      title=" "
      overlay
    >
      <template #title>
        <Search ref="alarmSearch" :config="alarmSearchConfig" />
      </template>
      <FormTable
        ref="alarmTable"
        :config="alarmTableConfig"
        class="card-table"
      ></FormTable>
    </SLCard>
    <SLCard
      v-if="state.activeName === 'archives'"
      class="wrapper-content content1"
      title=""
      overlay
    >
      <AttrTable
        :data="state.detailData"
        :columns="state.detailColumns"
      ></AttrTable>
    </SLCard>
  </SLCard>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { reactive } from 'vue';
import elementResizeDetectorMaker from 'element-resize-detector';
import { Download, Search as SearchIcon } from '@element-plus/icons-vue';
import { lineOption, gaugeCarOption } from '../../data/echart';
import { IECharts } from '@/plugins/echart';
import { filterTime, detailAttrColumns, archivesAttrColumns } from './data';
import useStation from '@/hooks/station/useStation';
import {
  GetStationRealTimeDetail,
  GetStationAlarmList,
  GetStationDetail
} from '@/api/shuiwureports/zhandian';
import {
  getThreeDaysData,
  stationDayDataQuery
} from '@/api/headwatersManage/headwaterMonitoring';
import { removeSlash } from '@/utils/removeIdSlash';
import { useAppStore } from '@/store';
import useGlobal from '@/hooks/global/useGlobal';

const appStore = useAppStore();
const { $messageWarning } = useGlobal();
const { getStationAttrGroups } = useStation();
const emit = defineEmits(['hiddenLoading', 'update:model-value']);

const erd = elementResizeDetectorMaker();
const props = defineProps<{
  stationId: string;
  stationDetail: any;
}>();

const today = dayjs().date();
const state = reactive<{
  activeName: string;
  chartOption: any;
  chartOption1: any;
  gaugeCarOption: any;
  searchActiveName: string;
  groupTab: string;
  currentGroupTabs: any;
  rows: IAttrTableRow[][];
  detailData: any;
  groupStation: any;
  attributeOptions: any;
  detailColumns: IAttrTableRow[][];
  archivesTableColumns: IAttrTableRow[][];
}>({
  activeName: 'status',
  chartOption: null,
  chartOption1: null,
  gaugeCarOption: null,
  searchActiveName: 'list',
  groupTab: '',
  currentGroupTabs: [],
  attributeOptions: [],
  groupStation: [],
  rows: [],
  detailData: {},
  detailColumns: detailAttrColumns,
  archivesTableColumns: archivesAttrColumns
});

const refTable = ref<ICardTableIns>();
const alarmSearch = ref<ISearchIns>();
const alarmTable = ref<ICardTableIns>();
const echartsDiv = ref<any>();
const refChart = ref<IECharts>();
const refChart1 = ref<IECharts>();
const refGaugeChart = ref<IECharts>();
const cardSearch = ref<ISearchIns>();
let alarmTableData = reactive<any>([]);
let stationDayData = reactive<any>([]);

// 出水列表
const baseTableConfig = reactive<ITable>({
  loading: true,
  currentRow: [],
  currentRowKey: 'property',
  highlightCurrentRow: true,
  dataList: [],
  columns: [
    { prop: 'propertyName', label: '检测项名称' },
    { prop: 'value', label: '检测项数据' },
    {
      prop: 'collectionTime',
      label: '采集时间',
      formatter: (value: any) => {
        console.log('dddddd', value.collectionTime);
        return value.collectionTime > 0
          ? dayjs(value.collectionTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      }
    }
  ],
  operations: [],
  pagination: {
    hide: true
  },
  handleRowClick: (row: any) => {
    baseTableConfig.currentRow = row;
    refuseChart();
  }
});
// 报警列表
const alarmTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'name1', label: '报警描述' },
    { prop: 'name2', label: '报警时间' }
  ],
  operations: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    layout: 'total, prev, pager, next, sizes, jumper',
    handleSize: (val) => {
      alarmTableConfig.pagination.limit = val;
      // cardTableConfig.dataList = tableData.slice((currentPage-1)*pagesize,currentPage*pagesize)
    },
    refreshData: ({ page, size }) => {
      alarmTableConfig.pagination.page = page;
      alarmTableConfig.pagination.limit = size;
      alarmTableConfig.dataList = alarmTableData.slice(
        (page - 1) * size,
        page * size
      );
    }
  }
});
// 查询数据列表
const dataTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'name1', label: '报警描述' },
    { prop: 'name2', label: '报警时间' },
    { prop: 'name2', label: '处理状态' }
  ],
  operations: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    layout: 'total, prev, pager, next, jumper',
    handleSize: (val) => {
      dataTableConfig.pagination.limit = val;
    },
    refreshData: ({ page, size }) => {
      dataTableConfig.pagination.page = page;
      dataTableConfig.pagination.limit = size;
      dataTableConfig.dataList = stationDayData?.tableDataList?.slice(
        (page - 1) * size,
        page * size
      );
    }
  }
});

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    date: [
      dayjs()
        .date(today - 2)
        .format('YYYY-MM-DD'),
      dayjs().date(today).format('YYYY-MM-DD')
    ],
    filterStart: [0, 23],
    group: '',
    attributeId: ''
  },
  filters: [
    { type: 'daterange', label: '选中日期', field: 'date', clearable: false },
    {
      label: '时间',
      type: 'range',
      rangeType: 'select',
      field: 'filterStart',
      options: JSON.parse(JSON.stringify(filterTime)),
      startPlaceHolder: '0时',
      endPlaceHolder: '23时',
      startOptionDisabled: (option, end) => {
        return end && Number(end) < option.value;
      },
      endOptionDisabled: (option, start) => {
        return start && option.value <= Number(start);
      }
    },
    {
      label: '监测组',
      field: 'group',
      type: 'select',
      clearable: false,
      options: [],
      autoFillOptions: async (config) => {
        const groupStation = (await getStationAttrGroups(
          props.stationId
        )) as any;
        config.options = groupStation;
        state.groupStation = groupStation;
      },
      onChange: (val) => {
        console.log('groupStation', val);
        // const filter = cardSearchConfig.filters?.find(filter => filter?.field === 'attributeId') as IFormSelect
        let options: any[] = state.groupStation.find(
          (d) => d.value === val
        ).children;
        console.log('options', options);
        options = options?.map((option: any) => {
          return {
            label: option.name,
            value: option.id,
            data: removeSlash(option.deviceId) + '.' + option.attr,
            unit: option.unit ? '(' + option.unit + ')' : ''
          };
        });
        state.attributeOptions = options;
        cardSearchConfig.defaultParams = {
          ...cardSearchConfig.defaultParams,
          attributeId: options[0].id,
          group: val
        };
        cardSearch.value?.resetForm();
      }
    },
    {
      label: '曲线类型',
      field: 'attributeId',
      type: 'select',
      clearable: false,
      options: computed(() => state.attributeOptions) as any
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        // {
        //   perm: true,
        //   isBlockBtn: true,
        //   text: '',
        //   svgIcon: shallowRef(Filter),
        //   click: () => {
        //     cardSearch.value?.toggleMore()
        //   }
        // },
        {
          perm: true,
          text: '查询',
          click: () => {
            dataTableConfig.pagination.page = 1;
            getStationDayDataQuery();
          },
          svgIcon: shallowRef(SearchIcon)
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          hide: () => {
            return state.searchActiveName !== 'list';
          },
          svgIcon: shallowRef(Download),
          click: () => {
            refTable.value?.exportTable();
          }
        }
      ]
    }
  ]
});

// 报警搜索栏配置
const alarmSearchConfig = reactive<ISearch>({
  defaultParams: {
    date: [
      dayjs()
        .date(today - 2)
        .format('YYYY-MM-DD'),
      dayjs().date(today).format('YYYY-MM-DD')
    ]
  },
  filters: [
    { type: 'daterange', label: '选择时间', field: 'date', clearable: false },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => getAlarmList(),
          svgIcon: shallowRef(SearchIcon)
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          svgIcon: shallowRef(Download),
          click: () => {
            alarmTable.value?.exportTable();
          }
        }
      ]
    }
  ]
});

// 顶部分类tabs
const tabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  width: '100%',
  tabs: [
    { label: '当前状态', value: 'status' },
    { label: '数据查询', value: 'search' },
    { label: '报警信息', value: 'alarm' },
    { label: '仪表档案', value: 'archives' }
  ],
  handleTabClick: (tab: any) => {
    console.log(tab.props.name);
    state.activeName = tab.props.name;
    if (state.activeName === 'search') {
      console.log(state.currentGroupTabs);
      nextTick(() => {
        getStationDayDataQuery();
      });
    } else if (state.activeName === 'alarm') {
      nextTick(() => {
        getAlarmList('range');
      });
    }
  }
});
// 选中属性组下的所有属性
// const changeGroup = (value: any) => {
//   const group = state.currentGroupTabs.find(t => t.value === value)
//   console.log(group.children[0].id)
//   const filter = cardSearchConfig.filters?.find(filter => filter?.field === 'attributeId') as IFormSelect
//   filter.options = group.children.map((option: any) => { return { label: option.name, value: option.id, data: removeSlash(option.deviceId) + '.' + option.attr } })
//   cardSearchConfig.defaultParams = {
//     ...cardSearchConfig.defaultParams,
//     attributeId: group.children[0].id
//   }
//   cardSearch.value?.resetForm()
//   console.log(value)
// }
// 站点历史数据查询
const getStationDayDataQuery = async () => {
  const queryParams = cardSearch.value?.queryParams || {};
  const [start, end] = queryParams.date || [];
  const [filterStart, filterEnd] = queryParams.filterStart || [];
  const params = {
    filterStart: filterStart || 0,
    filterEnd: filterEnd || 23,
    queryType: '10m',
    stationId: props.stationId,
    group: queryParams?.group,
    attributeId: queryParams?.attributeId,
    start: start ? dayjs(start).startOf('day').valueOf() : '',
    end: end ? dayjs(end).endOf('day').valueOf() : ''
  };
  stationDayDataQuery(params)
    .then((res) => {
      if (res.data?.code === 200) {
        stationDayData = res.data?.data;
        const columns = stationDayData?.tableInfo.map((item: any) => {
          return {
            prop: item.columnValue,
            label: item.columnName,
            unit: item.unit ? '(' + item.unit + ')' : ''
          };
        });
        console.log(columns);
        dataTableConfig.columns = columns;
        dataTableConfig.dataList = stationDayData?.tableDataList?.slice(
          (1 - 1) * 20,
          20
        );
        // dataTableConfig.dataList = stationDayData?.tableDataList
        dataTableConfig.pagination.total =
          stationDayData?.tableDataList?.length;
        dataTableConfig.loading = false;
        refreshDataEchart(queryParams?.attributeId);
      } else {
        dataTableConfig.columns = [];
        dataTableConfig.dataList = [];
        $messageWarning(res.data?.message);
        dataTableConfig.loading = false;
      }
    })
    .catch((error) => {
      console.log(error);
    });
};
// 数据查询某属性值图表
const refreshDataEchart = (attributeId: string) => {
  // refuseChart()
  const dataEchart = lineOption();
  const attributeList = cardSearchConfig.filters?.find(
    (filter) => filter.field === 'attributeId'
  ) as IFormSelect;
  const attribute = attributeList.options?.find(
    (data) => data.value === attributeId
  ) as any;
  // dataEchart.yAxis[0].name = attribute.label as string
  dataEchart.yAxis[0].name = (attribute.label +
    (attribute.unit ? attribute.unit : '')) as string;
  dataEchart.xAxis.data = stationDayData?.tableDataList.map((item) => item.ts);
  console.log(
    attributeId + '.' + attribute.data,
    stationDayData?.tableDataList
  );
  const serie = {
    name: attribute.label,
    smooth: true,
    data: stationDayData?.tableDataList.map((item) => item[attribute.data]),
    type: 'line',
    markPoint: {
      data: [
        { type: 'max', name: '最大值' },
        { type: 'min', name: '最小值' }
      ]
    }
  };
  refChart1.value?.clear();
  dataEchart.series = [serie];
  nextTick(() => {
    erd.listenTo(echartsDiv.value, () => {
      state.chartOption1 = dataEchart;
      refChart1.value?.resize();
    });
  });
};

// 站点属性组
const groupTabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  width: '100%',
  stretch: false,
  tabs: [],
  handleTabClick: (tab: any) => {
    getAttributes(tab.props.name);
  }
});

watch(
  () => props.stationId,
  (value, oldValue) => {
    if (value) {
      refreshData();
    }
  }
);
const refreshData = () => {
  state.activeName = 'status';
  console.log('refreshData');
  nextTick(async () => {
    await getStationGroup(props.stationId);
  });
};

// 获取属性组
const getStationGroup = async (stationId: string) => {
  //
  state.groupStation = (await getStationAttrGroups(stationId)) as any;
  const options: any[] = state.groupStation[0].children;
  const attributeOptions = options?.map((option: any) => {
    return {
      label: option.name,
      value: option.id,
      data: removeSlash(option.deviceId) + '.' + option.attr
    };
  });
  state.attributeOptions = attributeOptions;
  console.log('getStationGroup', options[0].id);
  cardSearchConfig.defaultParams = {
    ...cardSearchConfig.defaultParams,
    attributeId: options[0].id,
    group: state.groupStation[0].value
  };
  cardSearch.value?.resetForm();
  state.currentGroupTabs = options;
  groupTabsConfig.tabs = options.map((tab: any) => {
    return { label: tab.label, value: tab.id, data: tab };
  });
  state.groupTab = groupTabsConfig.tabs[0]?.label as string;
  await getAttributes(state.groupTab);
  await getAlarmList();
};
// 获取报警列表
const getAlarmList = async (dateType?: string) => {
  //
  alarmTableConfig.loading = true;
  let start = dayjs().startOf('month').valueOf();
  let end = dayjs().endOf('month').valueOf();
  if (dateType === 'range') {
    const queryParams = alarmSearch.value?.queryParams;
    start = dayjs(queryParams?.date[0]).startOf('day').valueOf();
    end = dayjs(queryParams?.date[1]).endOf('day').valueOf();
  }
  GetStationAlarmList(props.stationId, start, end).then((res) => {
    console.log('res', res);
    alarmTableData = res.data;
    alarmTableConfig.dataList = alarmTableData?.slice((1 - 1) * 20, 20);
    alarmTableConfig.pagination.total = alarmTableData.length;
    // alarmTableConfig.currentRow = alarmTableData[0]
    alarmTableConfig.loading = false;
  });
};

// 获取组属性数据
const getAttributes = async (name: any) => {
  baseTableConfig.loading = true;
  const valveData = await GetStationRealTimeDetail(props.stationId, name);
  baseTableConfig.dataList = valveData.data as any;

  const defaultRow = valveData?.data.find((d) => d.property === 'total_flow');
  baseTableConfig.currentRow = defaultRow;
  await refuseChart();
  baseTableConfig.loading = false;
};

// 加载图表
const refuseChart = async () => {
  const data = baseTableConfig.currentRow;
  const res = await GetStationDetail(props.stationId);
  state.detailData = res.data;
  const threeDaysData = await getThreeDaysData({
    deviceId: removeSlash(data.deviceId),
    attr: data.property
  });
  const tData = threeDaysData.data?.data;
  const options = lineOption();
  const instantaneous = baseTableConfig.dataList.find(
    (d) => d.property === 'Instantaneous_flow'
  );
  const total = baseTableConfig.dataList.find(
    (d) => d.property === 'total_flow'
  );
  const pressure = baseTableConfig.dataList.find(
    (d) => d.property === 'pressure'
  );
  state.detailData = {
    ...state.detailData,
    Instantaneous_flow: instantaneous.value + '(m³/h)',
    total_flow: total.value + '(m³)',
    pressure: pressure ? pressure.value + '(' + pressure.unit + ')' : ''
  };
  console.log('state.detailData', state.detailData);
  const gaugeEchart = gaugeCarOption(
    instantaneous.value,
    total.value,
    pressure?.value
  );
  const dataMap = [
    { name: '前天', key: 'beforeYesterdayDataList' },
    { name: '昨天', key: 'yesterdayDataList' },
    { name: '今天', key: 'todayDataList' }
  ];
  options.xAxis.data = tData.todayDataList.map((item) => item.ts);
  options.yAxis[0].name = '瞬时流量（m³/h）'; // data.propertyName.concat(data.unit ? '(' + data.unit + ')' : '')
  const series = dataMap.map((item) => {
    const data = tData[item.key].map((item) => item.value);
    return {
      name: item.name,
      smooth: true,
      data,
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    };
  });
  options.series = series;
  refChart.value?.clear();
  refGaugeChart.value?.clear();
  nextTick(() => {
    erd.listenTo(echartsDiv.value, () => {
      state.chartOption = options;
      state.gaugeCarOption = gaugeEchart;
      refChart.value?.resize();
      refGaugeChart.value?.resize();
    });
  });
  emit('hiddenLoading');
};

// onMounted(async () => {
//   refreshData()
// })
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
  width: 100%;
}

.content {
  height: 100%;
  width: 100%;

  .top {
    height: 40%;
    display: flex;
    justify-content: space-between;

    .card-table {
      width: 80%;
      height: 100%;
    }

    .chart-box {
      width: 95%;
      height: 100%;
    }
  }

  .bottom {
    height: calc(60% - 10px);
    display: flex;
    margin-top: 10px;

    .chart-box {
      width: 100%;
      height: 100%;
    }
  }
}

.content1 {
  //height: 86vh;
  width: 100%;

  .search-card {
    height: 100%;
    padding: 80px 0px;
  }

  .card-table {
    height: 100%;
    width: 100%;
  }

  .chart-box {
    width: 100%;
    height: 60vh;
  }

  :deep(.el-tabs) {
    height: 100%;

    .el-tabs__content {
      height: calc(100% - 55px);

      .el-tab-pane {
        height: 100%;
      }
    }
  }
}

.content2 {
  height: 100%;
  width: 100%;

  .card-table {
    height: 100%;
    width: 100%;
  }
}

:deep(.sl-card.hastitle .sl-card-title) {
  height: auto !important;
}
</style>
