package org.thingsboard.server.dao.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRecord;
import org.thingsboard.server.dao.smartProduction.guard.GuardEventRecordService;
import org.thingsboard.server.dao.sql.smartProduction.guard.GuardRecordMapper;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardRecordSaveRequest;

@Service
public class GuardRecordServiceImpl implements GuardRecordService {
    @Autowired
    private GuardRecordMapper mapper;

    @Autowired
    private GuardEventRecordService guardEventRecordService;


    @Override
    public GuardRecord findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<GuardRecord> findAllConditional(GuardRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public GuardRecord save(GuardRecordSaveRequest entity) {
        // TODO: [LFT] 值班日志记录
        return null;
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        guardEventRecordService.deleteByRecord(id);
        return mapper.deleteById(id) > 0;
    }

}
