import{d as U,r as x,bF as m,c as G,a8 as I,o as S,ay as j,g as h,n as v,q as a,F as l,p as e,i as n,G as k,bh as i,d_ as F,aw as q,aB as B,aJ as E,h as V,c2 as W,J as H,bz as K,bU as Q,cE as Z,bW as $,bm as aa,dD as ta,bl as ea,C as la}from"./index-r0dFAfgr.js";import{s as b,a as sa}from"./chart-BKY3iX5C.js";import{h as na,i as oa}from"./zhandian-YaGuQZe6.js";import da from"./analyzecardTable-CGdCyl5r.js";import"./index-C9hz-UZb.js";const ra={class:"wrapper"},ia={class:"title_view"},ca={class:"card_2"},_a={class:"card_left"},ua={class:"card_letf_1"},pa={class:"card_letf_2"},fa={class:"card_left_3 flex"},ma={class:"card_right"},ha={class:"card_right_1"},va={style:{height:"230px",width:"100%"}},ya={class:"card_title"},ga={style:{height:"230px",width:"100%"}},ba={style:{"line-height":"30px",width:"250px"}},wa={style:{"margin-right":"10px"}},Da={style:{width:"360px",height:"30px"}},xa=U({__name:"analyze",setup(ka){const _=x({date:m().format("YYYY-MM")}),L=G({}),s=x({LXFB:b(),DJFB:b(),BJPX:sa(),showDate:I(()=>m(_.date).month()+1),total:0,emergencyAlarm:0,tableList:[],tabsList:[]}),y=x({activeName:"水厂",keys:["水厂","泵站","流量监测站","压力监测站","水质监测站"],data:[[],[],[]]}),z=d=>{w(d)};function J(d){const t={name:["管网报警","泵站报警","水厂报警"],data:[0,0,0]};for(const o in d.alarmData)switch(d.alarmData[o].title){case"管网":t.data[0]+=d.alarmData[o].list.length||0;break;case"泵站":t.data[1]+=d.alarmData[o].list.length||0;break;case"水厂":t.data[2]+=d.alarmData[o].list.length||0;break}s.LXFB=b(t);const u={name:["提醒报警","重要报警","紧急报警"],data:[0,0,0]};let c=[];d.alarmData.forEach(o=>{c=[...c,...o.list]}),c.forEach(o=>{u.data[o.alarmLevel-1]+=1}),s.emergencyAlarm=u.data[2],s.DJFB=b(u)}function w(d){const t={startTime:m(_.date).valueOf(),endTime:m(_.date).add(1,"M").valueOf(),stationType:d};na(t).then(u=>{let c=0;(u.data.data||[]).forEach(o=>{c+=o.count}),s.tabsList=(u.data.data||[]).map(o=>({label:o.key,value:(o.count/c*100).toFixed(2),scale:o.count}))})}function M(){switch(s.tableList.length){case 0:return 24;case 1:return 24;case 2:return 12;case 3:return 8;default:return 8}}const Y=(d,t)=>{console.log(d,t),d&&(L.value[t]=d)};function N(){s.tableList.forEach((d,t)=>{L.value[t].reftable.exportTable()})}function A(){_.date=m().format("YYYY-MM"),D()}function D(){const d={startTime:m(_.date).valueOf(),endTime:m(_.date).add(1,"M").valueOf()};oa(d).then(t=>{t.data.code===200&&(s.total=t.data.data.total||0,s.tableList=t.data.data.alarmData||[],J(t.data.data),w(y.activeName))})}return S(()=>{D(),w("水厂")}),(d,t)=>{const u=W,c=H,o=K,p=Q,C=Z,T=j("VChart"),g=$,X=ea,P=aa,O=ta;return h(),v("div",ra,[a(g,{gutter:20},{default:l(()=>[a(p,{span:24},{default:l(()=>[a(o,{shadow:"always",class:"analyze_card"},{default:l(()=>[e("div",ia,[t[4]||(t[4]=e("span",{style:{"font-weight":"900",color:"var(--el-text-color-secondary)","font-size":"18px"}},"报警分析",-1)),e("div",null,[a(u,{modelValue:n(_).date,"onUpdate:modelValue":t[0]||(t[0]=r=>n(_).date=r),type:"month",placeholder:"请选择日期",style:{"margin-right":"10px"},onChange:D},null,8,["modelValue"]),a(c,{onClick:A},{default:l(()=>t[2]||(t[2]=[k(" 本月 ")])),_:1}),a(c,{type:"primary",onClick:N},{default:l(()=>t[3]||(t[3]=[k(" 导出 ")])),_:1})])])]),_:1})]),_:1}),a(p,{span:18},{default:l(()=>[a(g,{gutter:20},{default:l(()=>[a(p,{span:16},{default:l(()=>[a(o,{shadow:"always",class:"analyze_card"},{default:l(()=>[e("div",ca,[e("div",_a,[e("div",ua,i(n(s).showDate)+"月报警情况",1),e("div",pa,i(n(s).showDate)+"月报警情况:"+i(n(s).showDate)+"月共计报警"+i(n(s).total)+"次 ",1),e("div",fa,[e("div",null,[e("span",null,i(n(s).showDate)+"月共计报警(次)",1),e("div",null,i(n(s).total),1)]),e("div",null,[e("span",null,i(n(s).showDate)+"月紧急报警(次)",1),e("div",null,i(n(s).emergencyAlarm),1)])])]),e("div",ma,[e("div",ha,[e("div",null,i(n(s).showDate)+"月报警类型分布",1),a(c,{key:"",text:""},{default:l(()=>[a(C,null,{default:l(()=>[a(n(F))]),_:1})]),_:1})]),e("div",va,[a(T,{option:n(s).LXFB},null,8,["option"])])])])]),_:1})]),_:1}),a(p,{span:8},{default:l(()=>[a(o,{shadow:"always",class:"analyze_card"},{default:l(()=>[e("div",null,[e("div",ya,[e("div",null,i(n(s).showDate)+"月报警等级分布",1),a(c,{key:"",text:""},{default:l(()=>[a(C,null,{default:l(()=>[a(n(F))]),_:1})]),_:1})]),e("div",ga,[a(T,{option:n(s).DJFB},null,8,["option"])])])]),_:1})]),_:1}),a(p,{span:24},{default:l(()=>[a(g,{gutter:20,class:q({greater_than_three:!(n(s).tableList.length>3)})},{default:l(()=>[(h(!0),v(B,null,E(n(s).tableList,(r,f)=>(h(),V(p,{key:f,span:M()},{default:l(()=>[a(da,{ref_for:!0,ref:R=>Y(R,f),title:r.title,value:r.list,total:n(s).tableList.length},null,8,["title","value","total"])]),_:2},1032,["span"]))),128))]),_:1},8,["class"])]),_:1})]),_:1})]),_:1}),a(p,{span:6},{default:l(()=>[a(g,{gutter:20},{default:l(()=>[a(p,{span:24},{default:l(()=>[a(o,{shadow:"always",class:"analyze_card",style:{height:"735px"}},{default:l(()=>[t[5]||(t[5]=e("div",{class:"card_title"},[e("div",null,"紧急报警排序")],-1)),a(P,{modelValue:n(y).activeName,"onUpdate:modelValue":t[1]||(t[1]=r=>n(y).activeName=r),class:"demo-tabs",onTabChange:z},{default:l(()=>[(h(!0),v(B,null,E(n(y).keys,(r,f)=>(h(),V(X,{key:f,label:r,name:r},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),e("div",null,[(h(!0),v(B,null,E(n(s).tabsList,(r,f)=>(h(),v("div",{key:f,class:"sort"},[e("div",ba,[e("span",wa,i(f+1),1),e("span",null,i(r.label),1)]),e("div",Da,[a(O,{percentage:r.scale},{default:l(()=>[a(c,{text:"",style:{width:"50px"}},{default:l(()=>[k(i(r.scale)+"次 ",1)]),_:2},1024)]),_:2},1032,["percentage"])])]))),128))])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])}}}),Fa=la(xa,[["__scopeId","data-v-1faa3833"]]);export{Fa as default};
