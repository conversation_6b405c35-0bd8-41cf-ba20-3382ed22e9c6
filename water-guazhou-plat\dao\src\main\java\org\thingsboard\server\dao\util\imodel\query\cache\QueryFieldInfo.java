package org.thingsboard.server.dao.util.imodel.query.cache;

import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.query.CriteriaBuilderWrapper;
import org.thingsboard.server.dao.util.imodel.query.annotations.QueryAcquired;
import org.thingsboard.server.dao.util.reflection.ReflectionUtils;

import java.lang.reflect.Field;

public abstract class QueryFieldInfo<T> {
    protected final String name;
    protected final boolean or;
    private final Field field;
    private final boolean ignoreEmpty;
    private static final String UNDEFINED_SIGN = "===UNDEFINED===";

    public QueryFieldInfo(boolean or, Field field) {
        this.field = field;
        this.name = field.getName();
        this.or = or;
        this.ignoreEmpty = !field.isAnnotationPresent(QueryAcquired.class);
    }

    public void process(Object entity, CriteriaBuilderWrapper wrapper) {
        T value = getValue(entity);

        // 空值不做匹配
        if (ignoreEmpty) {
            if (value == null)
                return;
            else if (value instanceof String && StringUtils.isNullOrEmpty((String) value))
                return;
        } else if (UNDEFINED_SIGN.equals(value)) {
            return;
        }

        process(entity, wrapper, value);
    }

    protected abstract void process(Object entity, CriteriaBuilderWrapper wrapper, T value);

    private <U> U getValue(Object entity) {
        return ReflectionUtils.getValue(field, entity);
    }
}
