/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.constantsAttribute.ConstantsAttribute;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.util.imodel.query.base.ConstantsAttributePageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.constants.ConstantsService;

import java.util.List;

@RestController
@RequestMapping("/api")
public class ConstantsController extends BaseController {

    @Autowired
    private ConstantsService constantsService;

    //@PreAuthorize("hasAnyAuthority( 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'CUSTOMER_USER')")
    @RequestMapping(value = "/constants/find", method = RequestMethod.GET)
    @ResponseBody
    @SysLog(detail = "查询数据字典列表")
    public List<ConstantsAttribute> getConstantsAttributeById(@Param("type") String type, @Param("key") String key) throws ThingsboardException {
        try {
            return constantsService.getConstantsByTypeAndKey(type, key).get();
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @GetMapping("/constants/list")
    @SysLog(detail = "分页查询数据字典列表")
    public IstarResponse list(ConstantsAttributePageRequest constantsAttribute){
        return IstarResponse.ok(constantsService.selectConstantsList(constantsAttribute));
    }

    //@PreAuthorize("hasAnyAuthority('CUSTOMER_USER')")
    @RequestMapping(value = "/constants/save", method = RequestMethod.POST)
    @ResponseBody
    @SysLog(detail = "新增数据字典项")
    public List<ConstantsAttribute> saveConstantsAttribute(@RequestBody List<ConstantsAttribute> constantsAttribute) throws ThingsboardException {
        checkNotNull(constantsAttribute);
        return constantsService.saveConstantsAttribute(getTenantId(),constantsAttribute);
    }

    @RequestMapping(value = "/constants/save/node", method = RequestMethod.POST)
    @ResponseBody
    @SysLog(detail = "新增数据字典项")
    public String saveConstantsAttribute(@RequestBody JsonNode constantsAttribute) throws ThingsboardException {
        checkNotNull(constantsAttribute);
        //System.out.print(constantsAttribute.asText());
        return constantsAttribute.asText();
    }

}
