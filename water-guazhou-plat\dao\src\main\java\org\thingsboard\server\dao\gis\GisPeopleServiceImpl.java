package org.thingsboard.server.dao.gis;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.gis.GisPeopleSettingEntity;
import org.thingsboard.server.dao.role.RoleService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class GisPeopleServiceImpl implements GisPeopleService {

    @Autowired
    private GisPeopleSettingService gisPeopleSettingService;

    @Autowired
    private RoleService roleService;

    @Override
    public List<User> findList(String type, TenantId tenantId) throws ThingsboardException {
        List<GisPeopleSettingEntity> settingList = gisPeopleSettingService.findByType(type, tenantId);
        if (settingList == null || settingList.size() != 1) {
            throw new ThingsboardException("GIS一张图人员配置异常!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询人员列表
        GisPeopleSettingEntity setting = settingList.get(0);
        List<User> userList = new ArrayList<>();
        String roles = setting.getRoles();
        if (StringUtils.isBlank(roles)) {
            throw new ThingsboardException("GIS一张图人员配置异常!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        String[] rolesArray = roles.split(",");
        for (String roleId : rolesArray) {
            List<User> list = roleService.getUserListByRole(roleId);
            if (list != null && list.size() > 0) {
                userList.addAll(list);
            }
        }

        return userList;
    }
}
