package org.thingsboard.server.dao.gis;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SmCircuitTaskCoordinate;
import org.thingsboard.server.dao.sql.smartManagement.plan.SmCircuitTaskCoordinateMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SmCircuitTaskCoordinatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SmCircuitTaskCoordinateSaveRequest;

@Service
public class SmCircuitTaskCoordinateServiceImpl implements SmCircuitTaskCoordinateService {
    @Autowired
    private SmCircuitTaskCoordinateMapper mapper;

    @Override
    public IPage<SmCircuitTaskCoordinate> findAllConditional(SmCircuitTaskCoordinatePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SmCircuitTaskCoordinate save(SmCircuitTaskCoordinateSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SmCircuitTaskCoordinate entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
