package org.thingsboard.server.dao.model.sql;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 流量计等设备型号管理
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2024-10-14
 */
@Data
public class DeviceModelInfoEntity {
    private String id;

    private String name;

    private String brand;

    private Integer caliber;

    private BigDecimal maxNum;

    private BigDecimal minNum;

    private BigDecimal minFlow;

    private BigDecimal divideFlow;

    private BigDecimal normalFlow;

    private BigDecimal overloadFlow;

    private BigDecimal startFlow;

    private BigDecimal pressureLoss;

    private Date createTime;

    private String tenantId;

}
