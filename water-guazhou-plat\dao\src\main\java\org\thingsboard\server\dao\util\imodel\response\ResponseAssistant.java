package org.thingsboard.server.dao.util.imodel.response;

import org.joda.time.DateTime;

import java.util.Date;

/**
 * 编写以下方法来扩展返回的ResponseMap,参数可选
 * private void customizeMap(ResponseMap map, ReturnHelper helper, JdbcHelper jdbc) {
 * <p>
 * }
 */
@Deprecated
public class ResponseAssistant implements Responsible {
    public final String formatDateTime(Date date) {
        if (date == null)
            return null;
        return new DateTime(date).toString("yyyy-MM-dd HH:mm:ss");
    }
}
