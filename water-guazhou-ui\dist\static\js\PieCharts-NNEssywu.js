import{d as f,g as n,n as c,p as t,bh as o,G as l,q as a,F as r,i as _,f7 as p,cE as u,C as g}from"./index-r0dFAfgr.js";const m="/static/png/zhibiao-CqdABYTk.png",x={class:"item flex"},b={class:"text flex fl_column ds_between"},h={key:0,class:"red"},C={key:1,class:"blue"},k=f({__name:"PieCharts",props:{config:{}},setup(d){const s=d;return(v,e)=>{const i=u;return n(),c("div",x,[e[1]||(e[1]=t("img",{src:m,alt:""},null,-1)),t("div",b,[t("span",null,o(s.config.label),1),t("span",null,o(s.config.value),1),t("span",null,[e[0]||(e[0]=l("同比 ")),s.config.status?(n(),c("span",h,[a(i,{color:"red"},{default:r(()=>[a(_(p))]),_:1}),l(" "+o(s.config.status),1)])):(n(),c("span",C,[a(i,{color:"blue"},{default:r(()=>[a(_(p))]),_:1}),l(" "+o(s.config.status),1)]))])])])}}}),y=g(k,[["__scopeId","data-v-5b62201c"]]);export{y as default};
