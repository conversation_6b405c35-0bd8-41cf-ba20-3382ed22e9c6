package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MAINTENANCE_PLAN_C_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class MaintenancePlanCEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.MAINTENANCE_PLAN_C_MAIN_ID)
    private String mainId;

    @Column(name = ModelConstants.PROJECT_RELATION_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.MAINTENANCE_STANDARD_DEVICE_TYPE)
    private String deviceType;

    @Column(name = ModelConstants.DEVICE_ID_PROPERTY)
    private String deviceId;

    @Column(name = ModelConstants.MAINTENANCE_PLAN_C_STANDARD_ID)
    private String standardId;

    @Column(name = ModelConstants.MAINTENANCE_STANDARD_REMARK)
    private String remark;

    @Column(name = ModelConstants.MAINTENANCE_PLAN_C_ORDER_NUMBER)
    private Integer orderNumber;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private String projectName;

    @Transient
    private String deviceName;

    @Transient
    private String standardName;

}
