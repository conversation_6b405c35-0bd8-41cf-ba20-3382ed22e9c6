package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 智慧管网-指标总览供水量
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-12-19
 */
@Data
public class WaterOverviewHPDTO {


    private BigDecimal lastDaySupply = BigDecimal.ZERO;

    private BigDecimal lastDaySale = BigDecimal.ZERO;

    private BigDecimal lastDayLeak = BigDecimal.ZERO;

    private String lastDayNRW = "0%";

    private BigDecimal yearSupply = BigDecimal.ZERO;

    private BigDecimal yearSale = BigDecimal.ZERO;

    private BigDecimal yearLeak = BigDecimal.ZERO;

    private String yearNRW = "0%";

    private BigDecimal monthSupply = BigDecimal.ZERO;

    private BigDecimal monthSale = BigDecimal.ZERO;

    private BigDecimal monthLeak = BigDecimal.ZERO;

    private String monthNRW = "0%";

}
