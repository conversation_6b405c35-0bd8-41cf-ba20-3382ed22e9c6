<!-- 合同管理 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <SLDrawer
      ref="refForm"
      :config="addOrUpdateConfig"
    ></SLDrawer>
    <SLDrawer
      ref="PurchaseOrder"
      :config="addPurchaseOrder"
    ></SLDrawer>
    <!-- 设备选中 -->
    <SLDrawer
      ref="refFormEquipment"
      :config="addEquipment"
    ></SLDrawer>
    <el-upload
      v-show="false"
      ref="upload"
      action="action"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      :show-file-list="false"
      :on-change="clickUpload"
    >
      <el-button
        ref="fileRef"
        size="small"
        type="primary"
      >
        点击上传
      </el-button>
    </el-upload>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import {
  getDeviceapiContract,
  getDevicePurchaseSearch,
  getDevicePurchaseItem,
  postContract,
  getDeviceapiContractDetail,
  exporttemplateFile
} from '@/api/equipment_assets/equipmentPurchase'
import { readExcelToJson, contractCorrespondence } from '../../equipmentAssetsData'
import { formatDate } from '@/utils/DateFormatter'
import { getDeviceListSearch } from '@/api/equipment_assets/equipmentManage'
import { traverse } from '@/utils/GlobalHelper'
import { downloadUrl } from '@/utils/fileHelper'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const refForm = ref<ISLDrawerIns>()

const fileRef = ref()

const refFormEquipment = ref<ISLDrawerIns>()

const PurchaseOrder = ref<ISLDrawerIns>()

const chosen = ref([])

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '合同编号', field: 'code', type: 'input' },
    { label: '合同名称', field: 'title', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          type: 'success',
          perm: $btnPerms('RoleManageAdd'),
          text: '新增',
          icon: ICONS.ADD,
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '合同标题', prop: 'title' },
    { label: '合同编号', prop: 'code' },
    { label: '所属采购单', prop: 'purchaseName' },
    { label: '创建人', prop: 'creatorName' },
    { label: '创建时间', prop: 'createTime' },
    {
      label: '文件下载',
      prop: 'file',
      download: true,
      cellStyle: { fontStyle: 'italic', whiteSpace: 'pre-wrap', width: '100px', cursor: 'pointer' },
      handleClick: row => {
        downloadUrl(row.file)
      }
    }],
  operations: [
    {
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.EDIT,
      click: row => clickEdit(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  labelWidth: '130px',
  submit: (params: any, status?: boolean) => {
    if (status) {
      data.getDevice()
      refFormEquipment.value?.openDrawer()
      return
    }
    if (TableConfig.dataList.findIndex(item => item.code === params.code) !== -1 && !params.id) {
      ElMessage.warning('合同编号重复')
      return
    }
    let val = '新增成功'
    if (params.id) { val = '修改成功' }
    postContract(params).then(() => {
      refreshData()
      ElMessage.success(val)
      refForm.value?.closeDrawer()
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 12,
          disabled: true,
          type: 'input',
          label: '所属采购单',
          field: 'purchaseName',
          rules: [{ required: true, message: '请点击采购单里的查询按钮选择采购单' }],
          btn: {
            icon: ICONS.QUERY,
            type: 'primary',
            perm: true,
            click: () => {
              PurchaseOrder.value?.openDrawer()
            }
          }
        },
        {
          xl: 12,
          type: 'input',
          label: '合同编号',
          field: 'code',
          rules: [{ required: true, message: '请输入合同编号' }]
        },
        {
          xl: 12,
          type: 'input',
          label: '合同标题',
          field: 'title',
          rules: [{ required: true, message: '请输入合同标题' }]
        },
        {
          xl: 24,
          type: 'file',
          label: '上传文件',
          field: 'file'
        },
        {
          xl: 12,
          type: 'btn-group',
          label: '设备内容',
          btns: [
            {
              text: '下载模板',
              type: 'primary',
              perm: $btnPerms('RoleManageDelete'),
              click: () => {
                exporttemplateFile().then(res => {
                  const url = window.URL.createObjectURL(res.data)
                  console.log(url)
                  const link = document.createElement('a')
                  link.style.display = 'none'
                  link.href = url
                  link.setAttribute('download', `设备模板.xlsx`)
                  document.body.appendChild(link)
                  link.click()
                }).catch(error => {
                  ElMessage.warning(error)
                })
              }
            },
            {
              text: '点击上传',
              type: 'primary',
              perm: $btnPerms('RoleManageDelete'),
              click: () => {
                fileRef.value.$el.click()
              }
            }
          ]
        },
        {
          type: 'table',
          field: 'items',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.detail) as any,
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '添加',
                        perm: true,
                        click: () => {
                          refForm.value?.Submit(true)
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '设备编号',
                prop: 'serialId',
                formItemConfig: {
                  disabled: true,
                  type: 'input'
                }
              },
              {
                label: '货品名称',
                prop: 'name',
                formItemConfig: {
                  disabled: true,
                  type: 'input'
                }
              },
              {
                label: '规格型号',
                prop: 'model',
                formItemConfig: {
                  disabled: true,
                  type: 'input'
                }
              },
              {
                label: '单位',
                prop: 'unit',
                formItemConfig: {
                  disabled: true,
                  type: 'input'
                }
              },
              {
                label: '数量',
                prop: 'num',
                tableDataName: 'items',
                formItemConfig: {
                  type: 'input',
                  field: 'num',
                  rules: [{ required: true, message: '请输入数量' }]
                }
              },
              {
                label: '单价',
                prop: 'price',
                tableDataName: 'items',
                formItemConfig: {
                  type: 'input',
                  field: 'price',
                  rules: [{ required: true, message: '请输入单价' }]
                }
              },
              {
                label: '税率',
                prop: 'taxRate',
                tableDataName: 'items',
                formItemConfig: {
                  type: 'input',
                  field: 'taxRate',
                  rules: [{ required: true, message: '请输入税率' }]
                }
              },
              {
                label: '备注',
                prop: 'remark',
                formItemConfig: {
                  type: 'input'
                }
              }
            ],
            operations: [
              {
                text: '移除',
                type: 'danger',
                icon: ICONS.DELETE,
                perm: $btnPerms('RoleManageDelete'),
                click: row => {
                  data.detail = data.detail.filter(item => item.number !== row.number)
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 采购单选择
const addPurchaseOrder = reactive<IDrawerConfig>({
  title: '采购单列表',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'table',
          field: 'device',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.purchaseList) as any,
            handleRowClick: row => {
              refForm.value?.closeDrawer()
              addOrUpdateConfig.defaultValue = { ...addOrUpdateConfig.defaultValue, purchaseName: row.title, purchaseId: row.id }
              data.getDevicePurchaseItemValue(row.id)
              PurchaseOrder.value?.closeDrawer()
              setTimeout(() => {
                refForm.value?.openDrawer()
              }, 500)
            },
            columns: [
              {
                label: '采购单标题',
                prop: 'title'
              },
              {
                label: '采购单编码',
                prop: 'code'
              },
              {
                label: '采购部门名称',
                prop: 'tenantName'
              },
              {
                label: '请购人',
                prop: 'userName'
              },
              {
                label: '用途',
                prop: 'useWay'
              },
              {
                label: '申请时间',
                prop: 'preTime',
                formatter: row => formatDate(row.preTime, 'YYYY-MM-DD')
              },
              {
                label: '采购单创建人',
                prop: 'creatorName'
              },
              {
                label: '创建时间',
                prop: 'createTime',
                formatter: row => formatDate(row.createTime, 'YYYY-MM-DD')
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 设备选择
const addEquipment = reactive<IDrawerConfig>({
  title: '设备选择',
  submit: (params: any, status?: boolean) => {
    // 搜索处理
    if (status) {
      data.getDevice(params)
    } else {
      const list = traverse(chosen.value, 'children', { number: 'id' })
      const some: any[] = []
      list.forEach(el => {
        if (!some.some((e: any) => e.number === el.number)) {
          some.push(el)
        }
      })
      data.detail = [...data.detail, ...some]
      refFormEquipment.value?.closeDrawer()
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '设备编码',
          field: 'serialId'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备名称',
          field: 'name'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备型号',
          field: 'model'
        },
        {
          type: 'table',
          field: 'device',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceValue) as any,
            selectList: [],
            handleSelectChange: val => {
              chosen.value = val
            },
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '搜索',
                        perm: true,
                        click: () => {
                          refFormEquipment.value?.Submit(true)
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '设备编码',
                prop: 'serialId'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '设备款式',
                prop: 'model'
              },
              {
                label: '可用年限',
                prop: 'useYear'
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增'
  data.detail = []
  chosen.value = []
  addOrUpdateConfig.defaultValue = {}
  refForm.value?.openDrawer()
}

const clickEdit = (row: { [x: string]: any }) => {
  chosen.value = []
  addOrUpdateConfig.title = '编辑'
  data.getdetail(row)
  addOrUpdateConfig.defaultValue = { ...(row) || {} }
  refForm.value?.openDrawer()
}

// 上传文件
async function clickUpload(file: any) {
  console.log(file)
  readExcelToJson(file).then((res: any) => {
    let i = 1
    res && res.forEach(el => {
      const val = { number: i++ }
      for (const i in el) {
        val[contractCorrespondence[i]] = el[i]
      }
      data.detail.push(val)
    })
    const strings = data.detail.map(item => JSON.stringify(item))
    const removeDupList = [...new Set(strings)] // 也可以使用Array.from(new Set(strings))
    data.detail = removeDupList.map((item: any) => JSON.parse(item))
  })
}

const data = reactive({
  // 设备列表
  deviceValue: [],
  // 文件
  detail: [] as any,
  // 采购列表
  purchaseList: [],

  getDevice: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      ...param
    }
    getDeviceListSearch(params).then(res => {
      data.deviceValue = res.data.data.data || []
    })
  },
  getdetail: row => {
    const params = { page: 1, size: 99999, mainId: row.id }
    getDeviceapiContractDetail(params).then(res => {
      data.detail = traverse(res.data.data.data || [], 'children', { number: 'id' })
    })
  },
  getDevicePurchaseItemValue: (id: string) => {
    const params = {
      mainId: id,
      page: 1,
      size: 20
    }
    getDevicePurchaseItem(params).then(res => {
      data.detail = traverse(res.data.data.data || [], 'children', { number: 'id' })
    })
  },

  init: () => {
    const params = {
      size: 99999,
      page: 1
    }
    getDevicePurchaseSearch(params).then(res => {
      data.purchaseList = res.data.data.data || []
    }).catch(() => { ElMessage.error('获取数据失败') })
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  }
  getDeviceapiContract(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(() => {
  refreshData()
  data.init()
})
</script>
