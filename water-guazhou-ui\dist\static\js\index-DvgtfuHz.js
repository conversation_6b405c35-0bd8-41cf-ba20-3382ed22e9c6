import{_ as b}from"./CardSearch-CB_HNR-Q.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{d as x,c as m,r as v,eX as L,o as D,g as I,n as q,q as u,i as d,x as f,bO as k,C as w}from"./index-r0dFAfgr.js";import{I as E}from"./common-CvK_P_ao.js";import{c as _,f as g}from"./data-XGHpLV70.js";import{a as P,e as T}from"./schedulingInstructions-DdZ9MUvq.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const R={class:"wrapper"},A=x({__name:"index",setup(N){const o=m(),y=m({filters:[{label:"确认完成",field:"isComplete",type:"switch"},{label:"确认备注",field:"replyContent",type:"input",inputStyle:{width:"600px"}},{type:"btn-group",btns:[{perm:!0,text:"确认",icon:E.ADD,click:()=>C()},{perm:!0,text:"跳转查询",type:"success",click:()=>h()}]}]}),a=v({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",handleSelectChange:e=>{a.selectList=e},columns:[{label:"回复状态",prop:"commandStatus",tag:!0,tagColor:e=>{var t;return((t=_[e.commandStatus])==null?void 0:t.color)||""},formatter:e=>{var t;return((t=_[e.commandStatus])==null?void 0:t.value)||""}},{label:"执行时间",prop:"executionTime"},{label:"指令内容",prop:"sendContent"},{label:"回复内容",prop:"replyContent"},{label:"回复时间",prop:"replyTime"},{label:"完成状态",prop:"completeStatus",tag:!0,tagColor:e=>{var t;return((t=g[e.completeStatus])==null?void 0:t.color)||""},formatter:e=>{var t;return((t=g[e.completeStatus])==null?void 0:t.value)||""}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,r()}}});function C(){var n,s,l,p,c;const e=[];(n=a.selectList)==null||n.forEach(i=>{e.push(i.id)});const t={idList:e,isComplete:((s=o.value)==null?void 0:s.queryParams)&&((l=o.value)==null?void 0:l.queryParams.isComplete)||!1,replyContent:((p=o.value)==null?void 0:p.queryParams)&&((c=o.value)==null?void 0:c.queryParams.replyContent)||!1};T(t).then(()=>{f.success("回复成功"),a.selectList=[],r()}).catch(i=>{f.warning(i)})}function h(e){k.push({name:"SCDD010204",query:e})}const r=async()=>{const e={size:a.pagination.limit,page:a.pagination.page,receiveDeptId:L.get("departmentId")||"",commandStatus:"WAITING_REPLY"};P(e).then(t=>{a.dataList=t.data.data.data||[],a.pagination.total=t.data.data.total||0})};return D(async()=>{r()}),(e,t)=>{const n=S,s=b;return I(),q("div",R,[u(n,{class:"card-table",config:d(a)},null,8,["config"]),u(s,{ref_key:"refForm",ref:o,class:"mag_top_10",config:d(y)},null,8,["config"])])}}}),K=w(A,[["__scopeId","data-v-e75a2575"]]);export{K as default};
