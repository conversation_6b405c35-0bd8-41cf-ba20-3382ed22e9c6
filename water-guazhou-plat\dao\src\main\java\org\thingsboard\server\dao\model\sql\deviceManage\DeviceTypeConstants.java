package org.thingsboard.server.dao.model.sql.deviceManage;

import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceType;
import org.thingsboard.server.dao.util.TimeUtils;

import java.util.Date;

public interface DeviceTypeConstants {
    String TOP_SERIAL_ID = "00000000000000";
    String TOP_NAME = "设备类别";
    String TOP_LEVEL = "0";
    int TOP_ORDER_NUM = 0;
    Date TOP_CREATE_TIME = TimeUtils.evaluteDate("2022-11-9");

    DeviceType TOP_ROOT = new DeviceType(TOP_SERIAL_ID, TOP_NAME, TOP_ORDER_NUM, TOP_LEVEL, TOP_CREATE_TIME);

    SoDeviceType SO_TOP_ROOT = new SoDeviceType(TOP_SERIAL_ID, TOP_NAME, TOP_ORDER_NUM, TOP_LEVEL, TOP_CREATE_TIME);

}
