package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseMessageConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseMessageConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseMessageConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-消息配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Api(tags = "平台管理-消息配置")
@RestController
@RequestMapping("api/base/message/configuration")
public class BaseMessageConfigurationController extends BaseController {

    @Autowired
    private IBaseMessageConfigurationService baseMessageConfigurationService;

    /**
     * 查询平台管理-消息配置列表
     */
    @MonitorPerformance(description = "平台管理-查询消息配置列表")
    @ApiOperation(value = "查询消息配置列表")
    @GetMapping("/list")
    public IstarResponse list(BaseMessageConfigurationPageRequest baseMessageConfiguration) {
        return IstarResponse.ok(baseMessageConfigurationService.selectBaseMessageConfigurationList(baseMessageConfiguration));
    }

    /**
     * 获取平台管理-消息配置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询消息配置详情")
    @ApiOperation(value = "查询消息配置详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseMessageConfigurationService.selectBaseMessageConfigurationById(id));
    }

    /**
     * 新增平台管理-消息配置
     */
    @MonitorPerformance(description = "平台管理-新增消息配置")
    @ApiOperation(value = "新增消息配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseMessageConfiguration baseMessageConfiguration) {
        return IstarResponse.ok(baseMessageConfigurationService.insertBaseMessageConfiguration(baseMessageConfiguration));
    }

    /**
     * 修改平台管理-消息配置
     */
    @MonitorPerformance(description = "平台管理-修改消息配置")
    @ApiOperation(value = "修改消息配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseMessageConfiguration baseMessageConfiguration) {
        return IstarResponse.ok(baseMessageConfigurationService.updateBaseMessageConfiguration(baseMessageConfiguration));
    }

    /**
     * 删除平台管理-消息配置
     */
    @MonitorPerformance(description = "平台管理-删除消息配置")
    @ApiOperation(value = "删除消息配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseMessageConfigurationService.deleteBaseMessageConfigurationByIds(ids));
    }
}
