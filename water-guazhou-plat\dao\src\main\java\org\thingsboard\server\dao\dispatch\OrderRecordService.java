package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecord;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.*;

import java.util.List;

public interface OrderRecordService {
    /**
     * 分页条件查询指令记录
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<OrderRecord> findAllConditional(OrderRecordPageRequest request);

    /**
     * 保存指令记录
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    List<OrderRecord> save(List<OrderRecordSaveRequest> entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(OrderRecord entity);

    /**
     * 批量删除
     *
     * @param idList 唯一标识列表
     * @return 是否删除成功
     */
    boolean delete(List<String> idList);

    /**
     * 批量接收指令
     *
     * @param req 详细信息
     * @return 是否成功
     */
    boolean receive(OrderRecordReceiveRequest req);

    /**
     * 批量拒绝指令
     *
     * @param req 详细信息
     * @return 是否成功
     */
    boolean reject(OrderRecordRejectRequest req);

    /**
     * 批量回复指令
     *
     * @param req 详细信息
     * @return 是否成功
     */
    boolean reply(OrderRecordReplyRequest req);

    /**
     * 执行指令
     *
     * @param req 详细信息
     * @return 是否成功
     */
    boolean execute(OrderRecordExecuteRequest req);

    /**
     * 批量发送指令，发送后的指令才能被接收
     *
     * @param idList 指令id列表
     * @return 是否成功
     */
    boolean send(List<String> idList);

    /**
     * 命令是否为指定状态
     *
     * @param id     命令id
     * @param status 命令状态
     * @return 是否成功
     */
    boolean isStatus(String id, OrderRecordStatus status);

}
