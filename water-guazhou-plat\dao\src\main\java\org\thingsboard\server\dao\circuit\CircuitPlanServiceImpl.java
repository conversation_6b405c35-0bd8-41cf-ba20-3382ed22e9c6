package org.thingsboard.server.dao.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlan;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlanResponse;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask;
import org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitPlanMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitPlanPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitPlanSaveRequest;

import java.util.List;

@Service
public class CircuitPlanServiceImpl implements CircuitPlanService {
    @Autowired
    private CircuitPlanMapper mapper;

    @Autowired
    private CircuitTaskService service;

    @Override
    public IPage<CircuitPlanResponse> findAllConditional(CircuitPlanPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public CircuitPlan save(CircuitPlanSaveRequest entity) {
        CircuitPlan plan = QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
        List<CircuitTask> tasks = entity.generate();
        service.saveAllWithGenerateTaskItem(tasks);
        return plan;
    }

    @Override
    public boolean update(CircuitPlan entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        return mapper.deleteBatchIds(idList) > 0;
    }

}
