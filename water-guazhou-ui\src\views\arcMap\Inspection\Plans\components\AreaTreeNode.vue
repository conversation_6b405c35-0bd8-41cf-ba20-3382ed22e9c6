<template>
  <div class="custom-tree-node">
    <el-icon>
      <FolderOpened
        v-if="data.data.layer === 1"
        class="area"
      />
      <i
        v-else-if="data.data.layer === 2"
        class="iconfont icon-xunjian district"
      ></i>
    </el-icon>
    <span class="custom-tree-node__label">{{ data?.label }}</span>
    <div class="btn-wrapper">
      <template
        v-for="(btn, i) in customProps?.iconBtns"
        :key="i"
      >
        <Button
          :size="'small'"
          :row="data"
          :config="btn"
        ></Button>
      </template>

      <div
        v-if="iconCounts && textCounts"
        class="divider"
      ></div>
      <template
        v-for="(btn, i) in customProps?.textBtns"
        :key="i"
      >
        <Button
          :size="'small'"
          :row="data"
          :config="btn"
        ></Button>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { FolderOpened } from '@element-plus/icons-vue'

const props = defineProps<{
  node: any
  data: NormalOption
  customProps?: {
    textBtns: IButton[]
    iconBtns: IButton[]
  }
}>()
const iconCounts = computed(
  () => props.customProps?.iconBtns?.filter(item => (typeof item.perm === 'function'
    ? item.perm(props.data) === true
    : item.perm === true)).length
)
const textCounts = computed(
  () => props.customProps?.textBtns.filter(item => (typeof item.perm === 'function'
    ? item.perm(props.data) === true
    : item.perm === true)).length
)
</script>
<style lang="scss" scoped>
.custom-tree-node {
  display: flex;
  flex: 1;
  align-items: center;
  padding-right: 8px;
  .custom-tree-node__label{
    line-height: 1em;
  }
  .btn-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: nowrap;
    margin-left: auto;
    .el-button + .el-button {
      margin-left: 0;
    }
    .el-button {
      padding-left: 5px;
      padding-right: 5px;
    }
  }
  .divider {
    width: 1px;
    height: 1em;
    border: none;
    background-color: var(--el-border-color);
  }
  .area {
    color: orange;
  }
  .district {
    color: lightblue;
  }
}
</style>
