import{d as K,c as G,r as E,Q,g as F,h as x,F as b,q as I,i as d,G as X,an as Y,b as _,_ as Z,aq as ee,J as te,X as re}from"./index-r0dFAfgr.js";import{w as ie,g as oe,v as N,b as M,u as ae}from"./MapView-DaoQedLH.js";import{w as J}from"./Point-WxyopZva.js";import{g as C,s as V,p as ne}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import{a as se,i as pe,e as me}from"./IdentifyHelper-RJWmLn49.js";import{a as le,g as ce}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as ue}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{u as de}from"./useScheme-DcjSAE44.js";import"./geometryEngineBase-BhsKaODW.js";import ye from"./RightDrawerMap-D5PhmGFO.js";import{_ as fe}from"./SchemeManage.vue_vue_type_script_setup_true_lang-fv9Irhyi.js";import{_ as we}from"./SaveScheme.vue_vue_type_script_setup_true_lang-Bt-6iBz5.js";import ge from"./SchemeHeader-BLYQTCg3.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";const kr=K({__name:"PickSearch",setup(he){const u=de("pick"),v=G(),c=G();let S={};const m=E({layerInfos:[],layerIds:[],windows:[],gisSaveScheme:window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme}),e={view:void 0,identifyResults:[]},R=E({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!1,showCheckbox:!0,field:"layerid",nodeKey:"value"}]},{fieldset:{desc:"点击地图进行查询"},fields:[]}]}),l=E({height:"none",columns:[{label:"类型",prop:"layerName"},{label:"编号",prop:GIS_SERVER_SWITCH?"OBJECTID":"新编号"}],pagination:{hide:!0},handleRowClick:async t=>{var i;l.currentRow=t;let r;GIS_SERVER_SWITCH?(r=e.identifyResults.find(a=>a.properties.OBJECTID===t.OBJECTID),await C(e.view,S[t.OBJECTID])):(r=e.identifyResults.find(a=>a.feature.attributes.OBJECTID===t.OBJECTID),await C(e.view,r==null?void 0:r.feature)),(i=v.value)==null||i.openPop(t.OBJECTID)},dataList:[]}),A=()=>{var t;if(GIS_SERVER_SWITCH){const r=R.group[0].fields[0];let a=((t=e.view)==null?void 0:t.layerViews.items[0].layer.sublayers).items.map(o=>({label:o.name,value:o.name}));r.options=a,c.value&&(c.value.dataForm.layerid=m.layerIds)}else m.layerIds=le(e.view),re(m.layerIds).then(r=>{var f,w;m.layerInfos=((w=(f=r.data)==null?void 0:f.result)==null?void 0:w.rows)||[];const i=R.group[0].fields[0],a=m.layerInfos.filter(s=>s.geometrytype==="esriGeometryPoint").map(s=>({label:s.layername,value:s.layerid,data:s})),o=m.layerInfos.filter(s=>s.geometrytype==="esriGeometryPolyline").map(s=>({label:s.layername,value:s.layerid,data:s}));i&&(i.options=[{label:"管点类",value:-1,children:a},{label:"管线类",value:-2,children:o}]),c.value&&(c.value.dataForm.layerid=m.layerIds)})},H=()=>{var t;e.view&&(ue("crosshair"),e.graphicsLayer=ce(e.view,{id:"search-pick",title:"查询结果"}),e.mapClick=(t=e.view)==null?void 0:t.on("click",async r=>{await q(r)}))},T=async(t,r)=>{var f,w,s,k,O,D,B,L;const i=t.mapPoint;if(!e.view)return;e.mapExtent=r??e.view.extent,e.mapPoint=i;const a=((f=c.value)==null?void 0:f.dataForm.layerid)||[];if(!a.length){_.warning("请先选择要查询的图层");return}(w=v.value)==null||w.closeAllPop();let o;if(GIS_SERVER_SWITCH){if(o=await se(e.view,"/geoserver/guazhou/wms",a.join(","),t),!o){_.warning("没有相关数据");return}o=o.data,(s=e.graphicsLayer)==null||s.removeAll(),e.identifyResults=o.results||o.features||[],e.identifyResults.length>50&&(e.identifyResults.length=50);const g=[],y=[];m.windows=[],S={},e.identifyResults.map(n=>{const p=j(n.geometry);let h=new oe({geometry:p,symbol:V(p.type)});y.push(h),S[n.properties.OBJECTID]=h,m.windows.push({visible:!0,longitude:n.geometry.type==="MultiLineString"?n.geometry.coordinates[0][0][0]:n.geometry.coordinates[0],latitude:n.geometry.type==="MultiLineString"?n.geometry.coordinates[0][0][1]:n.geometry.coordinates[1],title:n.id,attributes:{row:n.properties,id:n.properties.OBJECTID}}),g.push({layerName:n.id,OBJECTID:n.properties.OBJECTID})}),(k=e.graphicsLayer)==null||k.addMany(y),l.dataList=g,l.currentRow=g[0]}else{const g=pe({layerIds:((O=c.value)==null?void 0:O.dataForm.layerid)||[],geometry:i,mapExtent:r||e.view.extent});o=await me(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,g),(D=e.graphicsLayer)==null||D.removeAll(),e.identifyResults=o.results||o.features||[],e.identifyResults.length>50&&(e.identifyResults.length=50);const y=[],n=[];m.windows=[],e.identifyResults.map(p=>{y.push({layerName:p.layerName,layerId:p.layerId,...p.feature.attributes}),p.feature.symbol=V(p.feature.geometry.type),n.push(p.geometry);const h=ne(p.feature.geometry)||[];m.windows.push({visible:!1,x:h[0],y:h[1],title:p.layerName+"("+p.feature.attributes.新编号+")",attributes:{row:p.feature.attributes,id:p.feature.attributes.OBJECTID}})}),(B=e.graphicsLayer)==null||B.addMany(n),l.dataList=y,l.currentRow=y[0],(L=o.results)!=null&&L.length||_.warning("没有查询到设备")}},P=async t=>{var r,i,a;t=t??((r=l.currentRow)==null?void 0:r.OBJECTID),GIS_SERVER_SWITCH?(l.currentRow=l.dataList.find(o=>o.OBJECTID===t),await C(e.view,S[t])):(await C(e.view,(i=e.identifyResults[0])==null?void 0:i.feature),l.currentRow=l.dataList.find(o=>o.OBJECTID===t)),(a=v.value)==null||a.openPop(t)},q=async t=>{var r;if(e.view)try{await T(t),await P(),e.view.refresh()}catch{(r=e.view)==null||r.graphics.removeAll()}},W=async t=>{var i;const r=u.parseScheme(t);if((i=c.value)!=null&&i.dataForm&&(c.value.dataForm.layerid=r.layerid||[]),r.mapPoint){const a=r.mapExtent?new ie({...r.mapExtent}):void 0,o=new J({...r.mapPoint});o&&(await T({mapPoint:o},a),await P(r.OBJECTID))}},$=()=>{if(!e.mapPoint||!e.identifyResults.length){_.warning("请先在地图上进行点选操作");return}u.openSaveDialog()},U=t=>{var r,i;u.submitScheme({...t,type:u.schemeType.value,detail:JSON.stringify({layerid:((r=c.value)==null?void 0:r.dataForm.layerid)||[],mapPoint:e.mapPoint,OBJECTID:(i=l.currentRow)==null?void 0:i.OBJECTID,mapExtent:e==null?void 0:e.mapExtent})})},j=t=>{switch(t.type){case"Point":return new J({x:t.coordinates[0],y:t.coordinates[1],spatialReference:{wkid:4326}});case"MultiPoint":return new ae({points:t.coordinates,spatialReference:{wkid:4326}});case"LineString":return new M({paths:[t.coordinates],spatialReference:{wkid:4326}});case"MultiLineString":return new M({paths:t.coordinates,spatialReference:{wkid:4326}});case"Polygon":return new N({rings:t.coordinates,spatialReference:{wkid:4326}});case"MultiPolygon":return new N({rings:t.coordinates.reduce((r,i)=>r.concat(i),[]),spatialReference:{wkid:4326}});default:return console.error("Unsupported GeoJSON type:",t.type),null}},z=t=>{e.view=t,setTimeout(()=>{A(),H()},1e3)};return Q(()=>{var t,r,i;(t=e.graphicsLayer)==null||t.removeAll(),(r=e.graphicsLayer)==null||r.destroy(),(i=e.mapClick)==null||i.remove()}),(t,r)=>{const i=Z,a=ee,o=te;return F(),x(ye,{ref_key:"refMap",ref:v,title:"点选查询1",windows:d(m).windows,onMapLoaded:z},{"right-title":b(()=>[I(ge,{title:"点选查询",onSchemeClick:d(u).openManagerDialog},null,8,["onSchemeClick"])]),default:b(()=>[I(i,{ref_key:"refForm",ref:c,config:d(R)},null,8,["config"]),I(a,{config:d(l)},null,8,["config"]),d(m).gisSaveScheme?(F(),x(o,{key:0,style:{width:"100%","margin-top":"20px"},type:"primary",onClick:$},{default:b(()=>r[0]||(r[0]=[X(" 保存方案 ")])),_:1})):Y("",!0),I(fe,{ref:d(u).getSchemeManageRef,type:d(u).schemeType.value,onRowClick:W},null,8,["type"]),I(we,{ref:d(u).getSaveSchemeRef,onSubmit:U},null,512)]),_:1},8,["windows"])}}});export{kr as default};
