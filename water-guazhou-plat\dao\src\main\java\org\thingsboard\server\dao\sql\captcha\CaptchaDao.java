package org.thingsboard.server.dao.sql.captcha;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.CaptchaEntity;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/28 14:33
 */
@Service
@Transactional
public class CaptchaDao {

    @Autowired
    private CaptchaRepository captchaRepository;


    public List<CaptchaEntity> getCaptchaByTenant(String tenantId) {
        return captchaRepository.findByTenantId(tenantId);
    }


    public List<CaptchaEntity> getCaptchaByPhone(String phone) {
        return captchaRepository.findByPhone(phone);
    }


    public CaptchaEntity saveCaptcha(CaptchaEntity captchaEntity) {
        return captchaRepository.save(captchaEntity);
    }

    public CaptchaEntity getCaptchaByPhoneAndInvalid(String phone, String invalid) {
        return captchaRepository.findByPhoneAndAndInvalid(phone, invalid);
    }

    public CaptchaEntity getCaptchaByCaptchaAndValid(String captcha, String invalid) {
        return captchaRepository.findByCaptchaAndAndInvalid(captcha,invalid);
    }

    public CaptchaEntity getCaptchaByCaptchaAndValidAndPhone(String captcha, String invalid,String pnone) {
        return captchaRepository.findByCaptchaAndAndInvalidAndPhone(captcha,invalid,pnone);
    }
}
