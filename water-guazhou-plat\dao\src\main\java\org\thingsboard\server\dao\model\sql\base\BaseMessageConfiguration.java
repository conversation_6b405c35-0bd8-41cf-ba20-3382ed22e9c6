package org.thingsboard.server.dao.model.sql.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-消息配置对象 base_message_configuration
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@ApiModel(value = "消息配置", description = "平台管理-消息配置实体类")
@Data
public class BaseMessageConfiguration {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 配置名称
     */
    @ApiModelProperty(value = "配置名称")
    private String configName;

    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型")
    private String messageType;

    /**
     * 目标平台
     */
    @ApiModelProperty(value = "目标平台")
    private String platform;

    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    private String status;

    /**
     * 消息标题模板
     */
    @ApiModelProperty(value = "消息标题模板")
    private String titleTemplate;

    /**
     * 消息内容模板
     */
    @ApiModelProperty(value = "消息内容模板")
    private String contentTemplate;

    /**
     * 内容编码格式
     */
    @ApiModelProperty(value = "内容编码格式")
    private String encoding;

    /**
     * 推送失败重试次数
     */
    @ApiModelProperty(value = "推送失败重试次数")
    private String retryCount;

    /**
     * 重试间隔
     */
    @ApiModelProperty(value = "重试间隔")
    private String retryInterval;

    /**
     * 每秒推送速率限制
     */
    @ApiModelProperty(value = "每秒推送速率限制")
    private String rateLimit;

    /**
     * 是否异步推送
     */
    @ApiModelProperty(value = "是否异步推送")
    private String isAsync;

}
