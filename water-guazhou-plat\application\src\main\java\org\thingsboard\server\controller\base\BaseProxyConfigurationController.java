package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseProxyConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseProxyConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseProxyConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-代理配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("api/base/proxy/configuration")
public class BaseProxyConfigurationController extends BaseController {

    @Autowired
    private IBaseProxyConfigurationService baseProxyConfigurationService;

    /**
     * 查询平台管理-代理配置列表
     */
    @MonitorPerformance(description = "平台管理-查询代理配置列表")
    @ApiOperation(value = "查询代理配置列表")
    @GetMapping("/list")
    public IstarResponse list(BaseProxyConfigurationPageRequest baseProxyConfiguration) {
        return IstarResponse.ok(baseProxyConfigurationService.selectBaseProxyConfigurationList(baseProxyConfiguration));
    }

    /**
     * 获取平台管理-代理配置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询代理配置详情")
    @ApiOperation(value = "查询代理配置详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseProxyConfigurationService.selectBaseProxyConfigurationById(id));
    }

    /**
     * 新增平台管理-代理配置
     */
    @MonitorPerformance(description = "平台管理-新增代理配置")
    @ApiOperation(value = "新增代理配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseProxyConfiguration baseProxyConfiguration) {
        return IstarResponse.ok(baseProxyConfigurationService.insertBaseProxyConfiguration(baseProxyConfiguration));
    }

    /**
     * 修改平台管理-代理配置
     */
    @MonitorPerformance(description = "平台管理-修改代理配置")
    @ApiOperation(value = "修改代理配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseProxyConfiguration baseProxyConfiguration) {
        return IstarResponse.ok(baseProxyConfigurationService.updateBaseProxyConfiguration(baseProxyConfiguration));
    }

    /**
     * 删除平台管理-代理配置
     */
    @MonitorPerformance(description = "平台管理-删除代理配置")
    @ApiOperation(value = "删除代理配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseProxyConfigurationService.deleteBaseProxyConfigurationByIds(ids));
    }
}
