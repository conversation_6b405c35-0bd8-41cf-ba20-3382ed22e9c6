import { request } from '@/plugins/axios'

/**
 * 新增/编辑分区信息
 * @param params
 * @returns
 */
export const PostPartition = (params: any) => {
  return request({
    url: '/api/spp/dma/partition',
    method: 'post',
    data: params
  })
}
/**
 * 查询列表
 * @param params
 * @returns
 */
export const GetPartitionList = (params?: { name?: string }) => {
  return request({
    url: '/api/spp/dma/partition/all',
    method: 'get',
    params
  })
}
/**
 * 查询列表
 * @param params
 * @returns
 */
export const GetPartitionTree = (params?: any) => {
  return request({
    url: '/api/spp/dma/partition/list',
    method: 'get',
    params
  })
}
/**
 * 查询分区信息
 * @param id
 * @returns
 */
export const GetPartitionDetail = (id: string) => {
  return request({
    url: '/api/spp/dma/partition/' + id,
    method: 'get'
  })
}
/**
 * 删除分区
 * @param ids
 * @returns
 */
export const DeletePartition = (ids: string[]) => {
  return request({
    url: '/api/spp/dma/partition',
    method: 'delete',
    data: ids
  })
}
/**
 * 查询分区总览
 * @param params
 * @returns
 */
export const GetPartitionOverview = (params: { partitionId: string }) => {
  return request({
    url: `/api/spp/dma/partition/overview`,
    method: 'get',
    params
  })
}
