import{D as k}from"./DrawerBox-CLde5xC8.js";import{_ as x}from"./Search-NSrhrIa_.js";import{d as D,c as p,r as S,s as B,o as V,g as E,h as I,F as m,q as o,i as a,p as d,bl as P,bm as L,al as M,C as T}from"./index-r0dFAfgr.js";import{_ as q}from"./ArcLayout-CHnHL9Pv.js";import N from"./CollectScrollList-5revMnZo.js";import{u as O}from"./usePipeCollect-DNAtT5mx.js";import{_ as A}from"./CollectPipes.vue_vue_type_script_setup_true_lang-D6VAdK4k.js";import"./SideDrawer-CBntChyn.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";const F={class:"collection-list"},R={class:"collections"},U=D({__name:"CollectOverview",setup($){const u=p(),_=p(),l=p(),s=O("dealing"),f=p(),h=S({filters:[{type:"input",style:{width:"320px"},prefixIcon:B(M),field:"title",appendBtns:[{perm:!0,iconifyIcon:"ep:filter",click:()=>n()}]}],defaultParams:{}}),v=async t=>{console.log(t),f.value=t},g=()=>{l.value,console.log("scrollend"),n(!0)},b=t=>{},n=t=>{var r,e;(e=l.value)==null||e.refreshData({status:s.status.value,title:(r=u.value)==null?void 0:r.queryParams.title},t)};return V(()=>{var t;(t=_.value)==null||t.toggleDrawer("rtl",!0)}),(t,r)=>{const e=q,c=P,w=L,C=x,y=k;return E(),I(y,{ref_key:"refDrawer",ref:_,"right-drawer":!0,"right-drawer-width":350},{right:m(()=>[o(w,{modelValue:a(s).status.value,"onUpdate:modelValue":r[0]||(r[0]=i=>a(s).status.value=i),class:"collect-tabs",stretch:!0,onTabClick:r[1]||(r[1]=()=>n())},{default:m(()=>[o(c,{label:"待办",name:"dealing"}),o(c,{label:"已办",name:"complete"}),o(c,{label:"总览",name:"total"})]),_:1},8,["modelValue"]),d("div",F,[o(C,{ref_key:"refSearch",ref:u,style:{"margin-bottom":"12px"},config:a(h)},null,8,["config"]),d("div",R,[o(N,{ref_key:"refCollect",ref:l,status:"complete",onClick:v,onScrollEnd:g},null,512)])])]),default:m(()=>[o(e,{onMapLoaded:b},{default:m(()=>{var i;return[o(A,{code:(i=a(f))==null?void 0:i.code},null,8,["code"])]}),_:1})]),_:1},512)}}}),Io=T(U,[["__scopeId","data-v-d3ba8abf"]]);export{Io as default};
