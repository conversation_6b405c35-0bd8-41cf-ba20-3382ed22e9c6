import { useAppStore } from '@/store'
import { transNumberUnit } from '@/utils/GlobalHelper'

export const initCirCleOption = () => {
  const option = {
    // tooltip: {
    //   trigger: 'item'
    // },
    legend: {
      right: 70,
      top: 'center',
      orient: 'vertical',
      textStyle: {
      }
    },
    color: ['#ff0000', '#2e9f43', '#ff9c00'],
    series: [
      {
        name: '监测点统计',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['25%', '50%'],
        colorBy: 'data',
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        // emphasis: {
        //   label: {
        //     show: true,
        //     fontSize: '40',
        //     fontWeight: 'bold'
        //   }
        // },
        labelLine: {
          show: false
        },
        data: [
          { value: 0, name: '报警', color: '#ff0000' },
          { value: 83, name: '正常', color: '#2e9f43' },
          { value: 9, name: '异常', color: '#ff9c00' }
        ]
      }
    ]
  }
  return option
}
export const initWaterLineOption = (datas: NormalOption[], type?: string) => {
  const xData: string[] = []
  type = type || '属性'
  const Datas: Record<string, number[]> = {}
  const items = datas?.filter(item => item.data?.type === type) || []
  // const basic: number = attrList.find(item => item.label === type)?.value as number || 0
  for (let i = 0; i < 24; i += 2) {
    const x = i < 10 ? `0${i}` : i
    xData.push(`${x}:00`)
    items.map(item => {
      const basic = item.data?.basicVal
      const type = item.value as string
      if (!Datas[type]) {
        Datas[type] = []
      }
      Datas[type].push(Number((Math.random() * basic * 0.1 + basic).toFixed(3)))
    })
  }
  const series = items.map(item => ({
    name: item.value,
    type: 'line',
    smooth: true,
    // symbol:'diamond',
    data: Datas[item.value as string] || [],
    markPoint: {
      data: [
        { type: 'max', name: 'Max' },
        { type: 'min', name: 'Min' }
      ]
    }
    // markLine: {
    //   data: [{ type: 'average', name: 'Avg' }]
    // }
  }))
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top: 'top',
      left: 'center',
      textStyle: {
      }
    },
    grid: {
      left: 30,
      top: 50,
      right: 30,
      bottom: 30
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData
    },
    yAxis: {
      type: 'value'
      // axisLabel: {
      //   formatter: '{value} °C'
      // },

    },
    series
  }
  return option
}

export function lineOption(dateX?: any, left?:number, right?:number) {
  return {
    // color: ['#318DFF', '#A431FF', '#FC2B2B', '#FFB800'],
    // backgroundColor: useAppStore().isDark ? '#131624' : '#F4F7FA',
    name: '',
    grid: {
      left: left || 80,
      right: right || 80,
      top: 80,
      bottom: 50
    },
    legend: {
      type: 'scroll',
      width: 600,
      top: 10,
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dateX
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }, {
      position: 'right',
      type: 'value',
      name: '',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          // color: '#656b84' // 更改坐标轴文字颜色
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [{}]
  }
}
// 饼图
export const pieOption = (title?: string, data?: any) => {
  const option = {
    color: ['#ff8516', '#1d8932', '#de2d43'],
    legend: {
      type: 'scroll',
      orient: 'vertical',
      top: 'center',
      right: 'right',
      width: 200,
      formatter: '{name}{value}',
      textStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    title: {
      text: title,
      bottom: 0,
      left: 'center',
      textStyle: {
        color: 'rgb(137, 168, 195)',
        fontSize: 13
      }
    },
    graphic: {
      elements: [
        {
          type: 'image',
          style: {
            image: 'https://yp.a-hh.cn/zhjk/img.jpg',
            width: 25,
            height: 30
          },
          left: 'center',
          top: 'center'
        }]
    },
    series: [
      {
        startAngle: -60,
        name: 'Access From',
        type: 'pie',
        radius: ['62%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
          color: '#ffffff',
          formatter() {
            return (data || 0) + '\n' + 'm³'
          }
        },
        // emphasis: {
        //   label: {
        //     show: true,
        //     fontSize: '12',
        //     fontWeight: 'bold'
        //   }
        // },
        data: [
          { value: 0, name: '报警' },
          { value: 4, name: '正常' },
          { value: 0, name: '异常' }
        ]
      }
    ]
  }
  return option
}
// 树状图
export const barOption = (title: string, color: string, dataX: any[], data: any[]) => {
  const option = {
    backgroundColor: useAppStore().isDark ? '#131624' : '#F4F7FA',
    color: [color],
    xAxis: {
      type: 'category',
      data: dataX
    },
    grid: {
      left: 50,
      right: 0
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '供水量(m³)',
      axisLine: {
        show: true,
        lineStyle: {
          // color: '#ffffff' // '#333'
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: '#656b84' // 更改坐标轴文字颜色
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          color: useAppStore().isDark ? '#303958' : '#ccc',
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [
      {
        data,
        type: 'bar'
      }
    ]
  }
  return option
}

export const gaugeOption = (color?: any[], data?: any[]) => {
  return {
    // tooltip: {
    //   formatter: '{a} <br/>{b} : {c}%'
    // },
    backgroundColor: useAppStore().isDark ? '#131624' : '#F4F7FA',
    color,
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        axisLine: {
          lineStyle: {
            width: 30
            // color: [
            //   [0.2, '#7ce781'],
            //   [0.7, '#ff8d18'],
            //   [1, '#ff020a']
            // ]
          }
        },
        pointer: {
          show: false,
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          distance: -30,
          length: 8,
          lineStyle: {
            color: '#fff',
            width: 2
          }
        },
        splitLine: {
          distance: -30,
          length: 30,
          lineStyle: {
            color: '#fff',
            width: 4
          }
        },
        axisLabel: {
          color: 'auto',
          distance: 40,
          fontSize: 20
        },
        detail: {
          valueAnimation: true,
          formatter: '{value} Mpa',
          color: 'auto'
        },
        data
      }
    ]
  }
}

export const gaugeCarOption = (data1:number, data2:number, data3:number) => {
  return {
    toolbox: {
      feature: {
        restore: {},
        saveAsImage: {}
      }
    },
    series: [
      {
        name: 'gauge 0',
        type: 'gauge',
        min: 0,
        max: 7,
        splitNumber: 7,
        startAngle: 240,
        endAngle: -320,
        radius: '66%',
        center: ['22%', '46%'],
        axisLine: {
          lineStyle: {
            width: 6,
            color: [
              [0.3, '#67e0e3'],
              [0.6, '#37a2da'],
              [1, '#fd666d']
            ]
          }
        },
        pointer: {
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          distance: 1,
          length: 8,
          lineStyle: {
            color: 'auto',
            width: 1
          }
        },
        splitLine: {
          distance: 1,
          length: 12,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        axisLabel: {
          color: 'auto',
          distance: 20
        },
        title: {
          show: true,
          offsetCenter: [0, '-30%'],
          color: useAppStore().isDark ? '#FFFFFF' : '#000000'
        },
        detail: {
          fontSize: 20,
          valueAnimation: true,
          formatter: '{value}',
          color: 'auto'
        },
        data: [
          {
            value: data1,
            name: '瞬时流量'
          }
        ]
      },
      // middle
      {
        type: 'gauge',
        z: 10,
        startAngle: 210,
        endAngle: -30,
        radius: '90%',
        center: ['50%', '60%'],
        axisLine: {
          lineStyle: {
            width: 8,
            color: [
              [0.3, '#67e0e3'],
              [0.6, '#37a2da'],
              [1, '#fd666d']
            ]
          }
        },
        pointer: {
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          distance: 1,
          length: 10,
          lineStyle: {
            color: 'auto',
            width: 1
          }
        },
        splitLine: {
          distance: 1,
          length: 16,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        axisLabel: {
          color: 'auto',
          distance: 20,
          fontSize: 13
        },
        title: {
          show: true,
          offsetCenter: [0, '-30%'],
          textStyle: {
            fontWeight: 'bolder',
            fontSize: '14',
            color: useAppStore().isDark ? '#FFFFFF' : '#000000'
          }
        },
        detail: {
          fontSize: 20,
          valueAnimation: true,
          formatter: '{value}',
          color: 'auto'
        },
        data: [
          {
            value: data2,
            name: '累计流量'
          }
        ]
      },
      // right
      {
        name: 'gauge 4',
        type: 'gauge',
        min: 0,
        max: 7,
        splitNumber: 7,
        startAngle: 150,
        endAngle: -60,
        radius: '66%',
        center: ['80%', '46%'],
        axisLine: {
          lineStyle: {
            width: 6,
            color: [
              [0.3, '#67e0e3'],
              [0.6, '#37a2da'],
              [1, '#fd666d']
            ]
          }
        },
        pointer: {
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          distance: 1,
          length: 8,
          lineStyle: {
            color: 'auto',
            width: 1
          }
        },
        splitLine: {
          distance: 1,
          length: 12,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        axisLabel: {
          color: 'auto',
          distance: 20,
          fontSize: 12
        },
        title: {
          show: true,
          offsetCenter: [0, '-30%'],
          color: useAppStore().isDark ? '#FFFFFF' : '#000000'
        },
        detail: {
          fontSize: 20,
          valueAnimation: true,
          formatter: '{value}',
          color: 'auto'
        },
        data: [
          {
            value: data3 || 0,
            name: '压力'
          }
        ]
      }
    ]
  }
}

// 饼图
export const ring = (
  data: {
    name: string
    nameAlias?: string
    value: string
    valueAlias?: string
    scale: string
  }[] = [],
  unit?: string,
  prefix?: string,
  percision = 2
) => {
  const title = '总数'
  const formatNumber = function (num) {
    const reg = /(?=(\B)(\d{3})+$)/g
    return num.toString().replace(reg, ',')
  }
  const total = data.reduce((a, b: any) => {
    return a + (parseFloat(b.value) || 0) * 1
  }, 0)
  const transedTotal = transNumberUnit(total)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return (
          (prefix || '')
          + params.name
          + ': '
          + Number(params.value).toFixed(percision)
          + ' '
          + unit
        )
      }
    },
    legend: {
      // selectedMode: false, // 取消图例上的点击事件
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      left: 'right',
      top: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      symbolKeepAspect: true,
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 80,
            fontSize: 12
          },
          value: {
            align: 'left',
            width: 70,
            fontSize: 12
          },
          count: {
            align: 'left',
            width: 70,
            fontSize: 12
          },
          upRate: {
            align: 'left',
            fontSize: 12
          },
          downRate: {
            align: 'left',
            fontSize: 12
          }
        }
      },
      data: data.map(item => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            if (name === data[i].name) {
              return (
                '{name| '
                + (data[i].nameAlias || name)
                + '}'
                + '{value| '
                + (data[i].valueAlias || data[i].value)
                + ' '
                + (unit || '')
                + '}'
                + '{value| '
                + (data[i].scale || '')
                + '}'
              )
            }
          }
        }
      }
    },
    title: [
      {
        text:
          '{name|'
          + title
          + ((unit && '(' + transedTotal.unit + unit + ')')
            || '(' + transedTotal.unit + ')')
          + '}\n{val|'
          + formatNumber(transedTotal.value.toFixed(percision))
          + '}',
        top: 'center',
        left: '19%',
        textAlign: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 10,
              fontWeight: 'normal',
              padding: [8, 0],
              align: 'center',
              color: '#fff'
            },
            val: {
              fontSize: 16,
              fontWeight: 'bold',
              color: '#fff'
            }
          }
        }
      }
    ],
    series: [
      {
        type: 'pie',
        radius: ['45%', '60%'],
        center: ['20%', '50%'],
        data,
        hoverAnimation: false,
        label: {
          show: false,
          formatter: params => {
            return (
              '{icon|●}{name|'
              + params.name
              + '}{value|'
              + formatNumber(Number(params.value || '0').toFixed(percision))
              + '}'
            )
          },
          padding: [0, -100, 25, -100],
          rich: {
            icon: {
              fontSize: 16
            },
            name: {
              fontSize: 14,
              padding: [0, 10, 0, 4]
            },
            value: {
              fontSize: 18,
              fontWeight: 'bold'
            }
          }
        }
      }
    ]
  }
  return option
}
