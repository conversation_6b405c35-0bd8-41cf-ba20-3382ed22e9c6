package org.thingsboard.server.dao.model.sql.smartProduction.guard;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("guard_event_record")
public class GuardEventRecord {
    // 事件id
    private String id;

    // 所属日志id
    private String recordId;

    // 事件内容
    private String content;

    // 记录人id
    @ParseUsername
    private String userId;

    // 创建时间
    private Date createTime;

    // 租户id
    private String tenantId;

}
