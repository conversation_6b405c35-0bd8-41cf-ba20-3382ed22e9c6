/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.role;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.*;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.role.MenuRole;
import org.thingsboard.server.common.data.role.Role;
import org.thingsboard.server.common.data.role.UserRole;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.menu.MenuCustomerDao;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.RoleApplication;
import org.thingsboard.server.dao.model.sql.RoleApplicationMenu;
import org.thingsboard.server.dao.sql.role.MenuApplicationRoleRepository;
import org.thingsboard.server.dao.sql.role.RoleApplicationRepository;
import org.thingsboard.server.dao.tenant.TenantApplicationService;
import org.thingsboard.server.dao.user.UserDao;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleDao roleDao;

    @Autowired
    private MenuRoleDao menuRoleDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private UserRoleDao userRoleDao;

    @Autowired
    private MenuCustomerDao menuCustomerDao;

    @Autowired
    private TenantApplicationService tenantApplicationService;

    @Autowired
    private MenuApplicationRoleRepository menuApplicationRoleRepository;

    @Autowired
    private RoleApplicationRepository roleApplicationRepository;

    @Override
    public Role saveRole(Role role) {
        log.trace("Executing saveRole [{}]", role);

        // 补全数据
        role.setFlagDelete(ModelConstants.CUSTOMER_ROLE_FLAG_DELETE_FALSE);
        role.setStatus(ModelConstants.CUSTOMER_ROLE_STATUS_DISPLAY);

        return roleDao.save(role);
    }

    @Override
    public boolean deleteRole(TenantId tenantId, RoleId roleId) {
        log.trace("Executing deleteRole, tenantId [{}] roleId [{}]", tenantId, roleId);
        // 校验
        Role _role = roleDao.findById(roleId.getId());
        if (_role == null) {
            throw new RuntimeException("no data has been found!");
        }
        if (!_role.getTenantId().getId().equals(tenantId.getId())) {
            // 该角色的拥有者不是当前登陆租户
            throw new RuntimeException("You don't have permission to perform this operation!");
        }
        // 如果有用于引用该角色，无法删除
        List<UserRole> userRoles = userRoleDao.findByRole(roleId);
        if (userRoles != null && userRoles.size() > 0) {
            throw new RuntimeException("A user has referenced this role!");
        }

        return roleDao.removeById(roleId.getId());
    }

    @Override
    public List<Role> findByTenantId(TenantId tenantId) {
        log.trace("Executing findByTenantId [{}]", tenantId);
        return roleDao.findByTenantId(tenantId);
    }

    @Override
    @Transactional
    public void assignMenuToRole(List<String> menuIds, RoleId roleId) {
        log.trace("Executing assignMenuToRole, menuIds [{}] roleId [{}]", menuIds, roleId);
        // 删除该角色原有的菜单
        menuRoleDao.deleteByRoleId(roleId);

        // 保存
        menuIds.forEach(menuId -> {
            MenuRole menuRole = new MenuRole();
            menuRole.setId(UUID.randomUUID().toString().replace("-",""));
            menuRole.setRoleId(roleId);
            menuRole.setMenuId(new MenuCustomerId(UUID.fromString(menuId)));

            menuRoleDao.save(menuRole);
        });
    }

    @Override
    @Transactional
    public void assignRoleToUser(UserId userId, RoleId roleId) throws ThingsboardException {
        log.trace("Executing assignRoleToUser, userId [{}] roleId [{}]", userId, roleId);
        // 校验
        User user = userDao.findById(userId.getId());
        if (user == null || user.getAuthority() != Authority.CUSTOMER_USER) {
            throw new ThingsboardException("invalid arguments",
                    ThingsboardErrorCode.INVALID_ARGUMENTS);
        }
        // 删除该角色原有角色名
        userRoleDao.deleteByUserId(userId);
        if (roleId != null) {
            // 保存
            UserRole userRole = new UserRole();
            userRole.setId(UUID.randomUUID().toString().replace("-",""));
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);

            userRoleDao.save(userRole);
        }
    }

    @Override
    public List<String> getTreeByRoleId(RoleId roleId) {
        log.trace("Executing getTreeByRoleId, roleId [{}]", roleId);
        List<String> result = new ArrayList<>();
        List<String> menuIds = menuRoleDao.findByRoleId(roleId);
        menuIds.forEach(id -> result.add(new MenuPoolId(UUIDConverter.fromString(id)).getId().toString()));

        return result;
    }

    @Override
    public String getRoleIdByUserId(UserId userId) {
        log.trace("Executing getRoleIdByUserId, userId [{}]", userId);

        return userRoleDao.getRoleIdByUserId(userId).get(0);
    }

    @Override
    public Role findById(RoleId roleId) {
        log.trace("Executing findById, roleId [{}]", roleId);
        return roleDao.findById(roleId.getId());
    }

    @Override
    public void deleteUserRoleByUserId(CustomerId customerId) {
        userRoleDao.deleteByUserId(customerId);
    }

    @Override
    public PageData<Role> findList(Integer page, Integer size, String name, String tenantId) {
        PageRequest pageRequest = new PageRequest(page - 1, size);
        return roleDao.findList(name, tenantId, pageRequest);
    }

    @Override
    public List<User> getUserListByRole(String roleId) {
        List<UserRole> userRoleList = userRoleDao.findByRole(new RoleId(UUIDConverter.fromString(roleId)));
        if (userRoleList != null && userRoleList.size() > 0) {
            List<String> userIdList = userRoleList.stream()
                    .map(userRole -> UUIDConverter.fromTimeUUID(userRole.getUserId().getId()))
                    .collect(Collectors.toList());
            return userDao.findByIdIn(userIdList);
        }

        return new ArrayList<>();
    }

    @Override
    @Transactional
    public void assignMenuToRole(List<String> menuIds, RoleId roleId, String tenantApplicationId) {
        log.trace("Executing assignMenuToRole, menuIds [{}] roleId [{}]", menuIds, roleId);
        // 删除该角色原有的菜单
        menuRoleDao.deleteByRoleId(roleId);

        // 保存
        menuIds.forEach(menuId -> {
            MenuRole menuRole = new MenuRole();
            menuRole.setId(UUID.randomUUID().toString().replace("-",""));
            menuRole.setRoleId(roleId);
            menuRole.setMenuId(new MenuCustomerId(UUID.fromString(menuId)));

            menuRoleDao.save(menuRole);
        });

        // 删除该角色原有的应用菜单权限
        String roleIdString = UUIDConverter.fromTimeUUID(roleId.getId());
        menuApplicationRoleRepository.deleteByRoleIdAndTenantApplicationId(roleIdString, tenantApplicationId);

        // 保存
        menuIds.forEach(menuId -> {
            RoleApplicationMenu menuApplicationRole = new RoleApplicationMenu();
            menuApplicationRole.setRoleId(roleIdString);
            menuApplicationRole.setMenuId(UUIDConverter.fromTimeUUID(new MenuCustomerId(UUID.fromString(menuId)).getId()));
            menuApplicationRole.setTenantApplicationId(tenantApplicationId);

            menuApplicationRoleRepository.save(menuApplicationRole);
        });

    }

    @Override
    public List<String> getTreeByRoleId(RoleId roleId, String tenantApplicationId) {
        log.trace("Executing getTreeByRoleId, roleId [{}]", roleId);
        List<String> result = new ArrayList<>();
        List<RoleApplicationMenu> dataList = menuApplicationRoleRepository
                .findByRoleIdAndTenantApplicationId(UUIDConverter.fromTimeUUID(roleId.getId()), tenantApplicationId);

        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> menuIds = dataList.stream()
                .map(data -> new MenuPoolId(UUIDConverter.fromString(data.getMenuId())).getId().toString())
                .collect(Collectors.toList());
//        menuIds.forEach(id -> result.add(new MenuPoolId(UUIDConverter.fromString(id)).getId().toString()));

        return menuIds;
    }

    @Override
    public List<String> getRoleTenantApplicationList(String strRoleId) {
        List<RoleApplication> list = roleApplicationRepository.findByRoleId(strRoleId);
        return list.stream().map(RoleApplication::getTenantApplicationId).distinct().collect(Collectors.toList());
    }

    @Override
    public void assignTenantApplicationToRole(String roleId, String tenantApplicationId) {
        if (StringUtils.isBlank(roleId) || StringUtils.isBlank(tenantApplicationId)) {
            return;
        }

        RoleApplication entity = roleApplicationRepository.findByRoleIdAndTenantApplicationId(roleId, tenantApplicationId);
        if (entity == null) {
            entity = new RoleApplication();
            entity.setRoleId(roleId);
            entity.setTenantApplicationId(tenantApplicationId);

            roleApplicationRepository.save(entity);
        } else {
            roleApplicationRepository.delete(entity);
        }

    }

    @Override
    public List<String> getRoleIdsByUserId(UserId userId) {
        return userRoleDao.getRoleIdByUserId(userId);
    }
}
