import{r as x,L as fe,M as V,$ as H,U as q,v as $,j as ve,A as Te,k as tt,K as rt,l as st,F as Ue,d as it,n as nt}from"./pe-B8dP0-Ut.js";import{e as g,y as _,a as Z,Q as ot,W as Le,a9 as ee,ak as Y,s as O,am as at,ar as ge}from"./Point-WxyopZva.js";import{fK as qe,aS as _e,fB as te}from"./index-r0dFAfgr.js";import{M as je,W as Be,N as P,O as lt,Q as xe,l as we,R as ct,c as Ve}from"./widget-BcWKanF2.js";const re="esri-identity-form",N={base:re,group:`${re}__group`,label:`${re}__label`,footer:`${re}__footer`,esriInput:"esri-input",esriButton:"esri-button",esriButtonSecondary:"esri-button--secondary"},ut="ArcGIS Online";let j=class extends Be{constructor(e,t){super(e,t),this._usernameInputNode=null,this._passwordInputNode=null,this.signingIn=!1,this.server=null,this.resource=null,this.error=null,this.oAuthPrompt=!1}render(){const{error:e,server:t,resource:r,signingIn:i,oAuthPrompt:s,messages:n}=this,l=P("div",{class:N.group},lt(s?n.oAuthInfo:n.info,{server:t&&/\.arcgis\.com/i.test(t)?ut:t,resource:`(${r||n.lblItem})`})),u=s?null:P("div",{class:N.group,key:"username"},P("label",{class:N.label},n.lblUser,P("input",{value:"",required:!0,autocomplete:"off",spellcheck:!1,type:"text",bind:this,afterCreate:xe,"data-node-ref":"_usernameInputNode",class:N.esriInput}))),a=s?null:P("div",{class:N.group,key:"password"},P("label",{class:N.label},n.lblPwd,P("input",{value:"",required:!0,type:"password",bind:this,afterCreate:xe,"data-node-ref":"_passwordInputNode",class:N.esriInput}))),h=P("div",{class:this.classes(N.group,N.footer)},P("input",{type:"submit",disabled:!!i,value:i?n.lblSigning:n.lblOk,class:N.esriButton}),P("input",{type:"button",value:n.lblCancel,bind:this,onclick:this._cancel,class:this.classes(N.esriButton,N.esriButtonSecondary)})),p=e?P("div",null,e.details&&e.details.httpStatus?n.invalidUser:n.noAuthService):null;return P("form",{class:N.base,bind:this,onsubmit:this._submit},l,p,u,a,h)}_cancel(){this._set("signingIn",!1),this._usernameInputNode&&(this._usernameInputNode.value=""),this._passwordInputNode&&(this._passwordInputNode.value=""),this.emit("cancel")}_submit(e){e.preventDefault(),this._set("signingIn",!0);const t=this.oAuthPrompt?{}:{username:this._usernameInputNode&&this._usernameInputNode.value,password:this._passwordInputNode&&this._passwordInputNode.value};this.emit("submit",t)}};g([_(),je("esri/identity/t9n/identity")],j.prototype,"messages",void 0),g([_()],j.prototype,"signingIn",void 0),g([_()],j.prototype,"server",void 0),g([_()],j.prototype,"resource",void 0),g([_()],j.prototype,"error",void 0),g([_()],j.prototype,"oAuthPrompt",void 0),j=g([Z("esri.identity.IdentityForm")],j);const ht=j;/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var Me=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],le=Me.join(","),ze=typeof Element>"u",G=ze?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,ce=!ze&&Element.prototype.getRootNode?function(o){var e;return o==null||(e=o.getRootNode)===null||e===void 0?void 0:e.call(o)}:function(o){return o==null?void 0:o.ownerDocument},ue=function o(e,t){var r;t===void 0&&(t=!0);var i=e==null||(r=e.getAttribute)===null||r===void 0?void 0:r.call(e,"inert"),s=i===""||i==="true",n=s||t&&e&&o(e.parentNode);return n},dt=function(e){var t,r=e==null||(t=e.getAttribute)===null||t===void 0?void 0:t.call(e,"contenteditable");return r===""||r==="true"},He=function(e,t,r){if(ue(e))return[];var i=Array.prototype.slice.apply(e.querySelectorAll(le));return t&&G.call(e,le)&&i.unshift(e),i=i.filter(r),i},Ge=function o(e,t,r){for(var i=[],s=Array.from(e);s.length;){var n=s.shift();if(!ue(n,!1))if(n.tagName==="SLOT"){var l=n.assignedElements(),u=l.length?l:n.children,a=o(u,!0,r);r.flatten?i.push.apply(i,a):i.push({scopeParent:n,candidates:a})}else{var h=G.call(n,le);h&&r.filter(n)&&(t||!e.includes(n))&&i.push(n);var p=n.shadowRoot||typeof r.getShadowRoot=="function"&&r.getShadowRoot(n),d=!ue(p,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(n));if(p&&d){var f=o(p===!0?n.children:p.children,!0,r);r.flatten?i.push.apply(i,f):i.push({scopeParent:n,candidates:f})}else s.unshift.apply(s,n.children)}}return i},Ke=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},Je=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||dt(e))&&!Ke(e)?0:e.tabIndex},pt=function(e,t){var r=Je(e);return r<0&&t&&!Ke(e)?0:r},ft=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},We=function(e){return e.tagName==="INPUT"},vt=function(e){return We(e)&&e.type==="hidden"},gt=function(e){var t=e.tagName==="DETAILS"&&Array.prototype.slice.apply(e.children).some(function(r){return r.tagName==="SUMMARY"});return t},_t=function(e,t){for(var r=0;r<e.length;r++)if(e[r].checked&&e[r].form===t)return e[r]},mt=function(e){if(!e.name)return!0;var t=e.form||ce(e),r=function(l){return t.querySelectorAll('input[type="radio"][name="'+l+'"]')},i;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")i=r(window.CSS.escape(e.name));else try{i=r(e.name)}catch(n){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",n.message),!1}var s=_t(i,e.form);return!s||s===e},bt=function(e){return We(e)&&e.type==="radio"},yt=function(e){return bt(e)&&!mt(e)},wt=function(e){var t,r=e&&ce(e),i=(t=r)===null||t===void 0?void 0:t.host,s=!1;if(r&&r!==e){var n,l,u;for(s=!!((n=i)!==null&&n!==void 0&&(l=n.ownerDocument)!==null&&l!==void 0&&l.contains(i)||e!=null&&(u=e.ownerDocument)!==null&&u!==void 0&&u.contains(e));!s&&i;){var a,h,p;r=ce(i),i=(a=r)===null||a===void 0?void 0:a.host,s=!!((h=i)!==null&&h!==void 0&&(p=h.ownerDocument)!==null&&p!==void 0&&p.contains(i))}}return s},Oe=function(e){var t=e.getBoundingClientRect(),r=t.width,i=t.height;return r===0&&i===0},St=function(e,t){var r=t.displayCheck,i=t.getShadowRoot;if(getComputedStyle(e).visibility==="hidden")return!0;var s=G.call(e,"details>summary:first-of-type"),n=s?e.parentElement:e;if(G.call(n,"details:not([open]) *"))return!0;if(!r||r==="full"||r==="legacy-full"){if(typeof i=="function"){for(var l=e;e;){var u=e.parentElement,a=ce(e);if(u&&!u.shadowRoot&&i(u)===!0)return Oe(e);e.assignedSlot?e=e.assignedSlot:!u&&a!==e.ownerDocument?e=a.host:e=u}e=l}if(wt(e))return!e.getClientRects().length;if(r!=="legacy-full")return!0}else if(r==="non-zero-area")return Oe(e);return!1},It=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if(t.tagName==="FIELDSET"&&t.disabled){for(var r=0;r<t.children.length;r++){var i=t.children.item(r);if(i.tagName==="LEGEND")return G.call(t,"fieldset[disabled] *")?!0:!i.contains(e)}return!0}t=t.parentElement}return!1},he=function(e,t){return!(t.disabled||ue(t)||vt(t)||St(t,e)||gt(t)||It(t))},Se=function(e,t){return!(yt(t)||Je(t)<0||!he(e,t))},kt=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},At=function o(e){var t=[],r=[];return e.forEach(function(i,s){var n=!!i.scopeParent,l=n?i.scopeParent:i,u=pt(l,n),a=n?o(i.candidates):l;u===0?n?t.push.apply(t,a):t.push(l):r.push({documentOrder:s,tabIndex:u,item:i,isScope:n,content:a})}),r.sort(ft).reduce(function(i,s){return s.isScope?i.push.apply(i,s.content):i.push(s.content),i},[]).concat(t)},Tt=function(e,t){t=t||{};var r;return t.getShadowRoot?r=Ge([e],t.includeContainer,{filter:Se.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:kt}):r=He(e,t.includeContainer,Se.bind(null,t)),At(r)},Ut=function(e,t){t=t||{};var r;return t.getShadowRoot?r=Ge([e],t.includeContainer,{filter:he.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):r=He(e,t.includeContainer,he.bind(null,t)),r},se=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return G.call(e,le)===!1?!1:Se(t,e)},xt=Me.concat("iframe").join(","),me=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return G.call(e,xt)===!1?!1:he(t,e)};/*!
* focus-trap 7.2.0
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/function Pe(o,e){var t=Object.keys(o);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(o);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(o,i).enumerable})),t.push.apply(t,r)}return t}function Ce(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Pe(Object(t),!0).forEach(function(r){Ot(o,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(t)):Pe(Object(t)).forEach(function(r){Object.defineProperty(o,r,Object.getOwnPropertyDescriptor(t,r))})}return o}function Ot(o,e,t){return e=Ct(e),e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}function Pt(o,e){if(typeof o!="object"||o===null)return o;var t=o[Symbol.toPrimitive];if(t!==void 0){var r=t.call(o,e||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(o)}function Ct(o){var e=Pt(o,"string");return typeof e=="symbol"?e:String(e)}var Re={activateTrap:function(e,t){if(e.length>0){var r=e[e.length-1];r!==t&&r.pause()}var i=e.indexOf(t);i===-1||e.splice(i,1),e.push(t)},deactivateTrap:function(e,t){var r=e.indexOf(t);r!==-1&&e.splice(r,1),e.length>0&&e[e.length-1].unpause()}},Rt=function(e){return e.tagName&&e.tagName.toLowerCase()==="input"&&typeof e.select=="function"},Dt=function(e){return e.key==="Escape"||e.key==="Esc"||e.keyCode===27},X=function(e){return e.key==="Tab"||e.keyCode===9},Nt=function(e){return X(e)&&!e.shiftKey},Et=function(e){return X(e)&&e.shiftKey},De=function(e){return setTimeout(e,0)},Ne=function(e,t){var r=-1;return e.every(function(i,s){return t(i)?(r=s,!1):!0}),r},Q=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return typeof e=="function"?e.apply(void 0,r):e},ie=function(e){return e.target.shadowRoot&&typeof e.composedPath=="function"?e.composedPath()[0]:e.target},Ft=[],Lt=function(e,t){var r=(t==null?void 0:t.document)||document,i=(t==null?void 0:t.trapStack)||Ft,s=Ce({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:Nt,isKeyBackward:Et},t),n={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0},l,u=function(c,v,y){return c&&c[v]!==void 0?c[v]:s[y||v]},a=function(c){return n.containerGroups.findIndex(function(v){var y=v.container,T=v.tabbableNodes;return y.contains(c)||T.find(function(U){return U===c})})},h=function(c){var v=s[c];if(typeof v=="function"){for(var y=arguments.length,T=new Array(y>1?y-1:0),U=1;U<y;U++)T[U-1]=arguments[U];v=v.apply(void 0,T)}if(v===!0&&(v=void 0),!v){if(v===void 0||v===!1)return v;throw new Error("`".concat(c,"` was specified but was not a node, or did not return a node"))}var R=v;if(typeof v=="string"&&(R=r.querySelector(v),!R))throw new Error("`".concat(c,"` as selector refers to no known node"));return R},p=function(){var c=h("initialFocus");if(c===!1)return!1;if(c===void 0)if(a(r.activeElement)>=0)c=r.activeElement;else{var v=n.tabbableGroups[0],y=v&&v.firstTabbableNode;c=y||h("fallbackFocus")}if(!c)throw new Error("Your focus-trap needs to have at least one focusable element");return c},d=function(){if(n.containerGroups=n.containers.map(function(c){var v=Tt(c,s.tabbableOptions),y=Ut(c,s.tabbableOptions);return{container:c,tabbableNodes:v,focusableNodes:y,firstTabbableNode:v.length>0?v[0]:null,lastTabbableNode:v.length>0?v[v.length-1]:null,nextTabbableNode:function(U){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,D=y.findIndex(function(B){return B===U});if(!(D<0))return R?y.slice(D+1).find(function(B){return se(B,s.tabbableOptions)}):y.slice(0,D).reverse().find(function(B){return se(B,s.tabbableOptions)})}}}),n.tabbableGroups=n.containerGroups.filter(function(c){return c.tabbableNodes.length>0}),n.tabbableGroups.length<=0&&!h("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times")},f=function m(c){if(c!==!1&&c!==r.activeElement){if(!c||!c.focus){m(p());return}c.focus({preventScroll:!!s.preventScroll}),n.mostRecentlyFocusedNode=c,Rt(c)&&c.select()}},w=function(c){var v=h("setReturnFocus",c);return v||(v===!1?!1:c)},b=function(c){var v=ie(c);if(!(a(v)>=0)){if(Q(s.clickOutsideDeactivates,c)){l.deactivate({returnFocus:s.returnFocusOnDeactivate&&!me(v,s.tabbableOptions)});return}Q(s.allowOutsideClick,c)||c.preventDefault()}},I=function(c){var v=ie(c),y=a(v)>=0;y||v instanceof Document?y&&(n.mostRecentlyFocusedNode=v):(c.stopImmediatePropagation(),f(n.mostRecentlyFocusedNode||p()))},A=function(c){var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,y=ie(c);d();var T=null;if(n.tabbableGroups.length>0){var U=a(y),R=U>=0?n.containerGroups[U]:void 0;if(U<0)v?T=n.tabbableGroups[n.tabbableGroups.length-1].lastTabbableNode:T=n.tabbableGroups[0].firstTabbableNode;else if(v){var D=Ne(n.tabbableGroups,function(de){var pe=de.firstTabbableNode;return y===pe});if(D<0&&(R.container===y||me(y,s.tabbableOptions)&&!se(y,s.tabbableOptions)&&!R.nextTabbableNode(y,!1))&&(D=U),D>=0){var B=D===0?n.tabbableGroups.length-1:D-1,Ze=n.tabbableGroups[B];T=Ze.lastTabbableNode}else X(c)||(T=R.nextTabbableNode(y,!1))}else{var W=Ne(n.tabbableGroups,function(de){var pe=de.lastTabbableNode;return y===pe});if(W<0&&(R.container===y||me(y,s.tabbableOptions)&&!se(y,s.tabbableOptions)&&!R.nextTabbableNode(y))&&(W=U),W>=0){var $e=W===n.tabbableGroups.length-1?0:W+1,et=n.tabbableGroups[$e];T=et.firstTabbableNode}else X(c)||(T=R.nextTabbableNode(y))}}else T=h("fallbackFocus");T&&(X(c)&&c.preventDefault(),f(T))},L=function(c){if(Dt(c)&&Q(s.escapeDeactivates,c)!==!1){c.preventDefault(),l.deactivate();return}(s.isKeyForward(c)||s.isKeyBackward(c))&&A(c,s.isKeyBackward(c))},K=function(c){var v=ie(c);a(v)>=0||Q(s.clickOutsideDeactivates,c)||Q(s.allowOutsideClick,c)||(c.preventDefault(),c.stopImmediatePropagation())},J=function(){if(n.active)return Re.activateTrap(i,l),n.delayInitialFocusTimer=s.delayInitialFocus?De(function(){f(p())}):f(p()),r.addEventListener("focusin",I,!0),r.addEventListener("mousedown",b,{capture:!0,passive:!1}),r.addEventListener("touchstart",b,{capture:!0,passive:!1}),r.addEventListener("click",K,{capture:!0,passive:!1}),r.addEventListener("keydown",L,{capture:!0,passive:!1}),l},S=function(){if(n.active)return r.removeEventListener("focusin",I,!0),r.removeEventListener("mousedown",b,!0),r.removeEventListener("touchstart",b,!0),r.removeEventListener("click",K,!0),r.removeEventListener("keydown",L,!0),l};return l={get active(){return n.active},get paused(){return n.paused},activate:function(c){if(n.active)return this;var v=u(c,"onActivate"),y=u(c,"onPostActivate"),T=u(c,"checkCanFocusTrap");T||d(),n.active=!0,n.paused=!1,n.nodeFocusedBeforeActivation=r.activeElement,v&&v();var U=function(){T&&d(),J(),y&&y()};return T?(T(n.containers.concat()).then(U,U),this):(U(),this)},deactivate:function(c){if(!n.active)return this;var v=Ce({onDeactivate:s.onDeactivate,onPostDeactivate:s.onPostDeactivate,checkCanReturnFocus:s.checkCanReturnFocus},c);clearTimeout(n.delayInitialFocusTimer),n.delayInitialFocusTimer=void 0,S(),n.active=!1,n.paused=!1,Re.deactivateTrap(i,l);var y=u(v,"onDeactivate"),T=u(v,"onPostDeactivate"),U=u(v,"checkCanReturnFocus"),R=u(v,"returnFocus","returnFocusOnDeactivate");y&&y();var D=function(){De(function(){R&&f(w(n.nodeFocusedBeforeActivation)),T&&T()})};return R&&U?(U(w(n.nodeFocusedBeforeActivation)).then(D,D),this):(D(),this)},pause:function(){return n.paused||!n.active?this:(n.paused=!0,S(),this)},unpause:function(){return!n.paused||!n.active?this:(n.paused=!1,d(),J(),this)},updateContainerElements:function(c){var v=[].concat(c).filter(Boolean);return n.containers=v.map(function(y){return typeof y=="string"?r.querySelector(y):y}),n.active&&d(),this}},l.updateContainerElements(e),l};const z="esri-identity-modal",F={base:z,open:`${z}--open`,closed:`${z}--closed`,title:`${z}__title`,dialog:`${z}__dialog`,content:`${z}__content`,closeButton:`${z}__close-button`,iconClose:"esri-icon-close"};let M=class extends Be{constructor(o,e){super(o,e),this.container=document.createElement("div"),this.content=null,this.open=!1,this._focusTrap=null,this._close=()=>{this.open=!1},document.body.appendChild(this.container),this.addHandles(we(()=>this.open,()=>this._toggleFocusTrap()))}destroy(){this._destroyFocusTrap()}get title(){var o;return(o=this.messages)==null?void 0:o.auth.signIn}render(){const o=this.id,{open:e,content:t,title:r,messages:i}=this,s=e&&!!t,n={[F.open]:s,[F.closed]:!s},l=P("button",{class:F.closeButton,"aria-label":i.close,title:i.close,bind:this,onclick:this._close,type:"button"},P("span",{"aria-hidden":"true",class:F.iconClose})),u=`${o}_title`,a=`${o}_content`,h=r?P("h1",{id:u,class:F.title},r):null,p=s?P("div",{bind:this,class:F.dialog,role:"dialog","aria-labelledby":u,"aria-describedby":a,afterCreate:this._createFocusTrap},l,h,this._renderContent(a)):null;return P("div",{tabIndex:-1,class:this.classes(F.base,n)},p)}_destroyFocusTrap(){var o;(o=this._focusTrap)==null||o.deactivate({onDeactivate:()=>{}}),this._focusTrap=null}_toggleFocusTrap(){const{_focusTrap:o,open:e}=this;o&&(e?o.activate():o.deactivate())}_createFocusTrap(o){this._destroyFocusTrap();const e=requestAnimationFrame(()=>{this._focusTrap=Lt(o,{initialFocus:"input",onDeactivate:this._close}),this._toggleFocusTrap()});this.addHandles(ot(()=>cancelAnimationFrame(e)))}_renderContent(o){const e=this.content;return typeof e=="string"?P("div",{class:F.content,id:o,innerHTML:e}):ct(e)?P("div",{class:F.content,id:o},e.render()):e instanceof HTMLElement?P("div",{class:F.content,id:o,bind:e,afterCreate:this._attachToNode}):null}_attachToNode(o){const e=this;o.appendChild(e)}};g([_({readOnly:!0})],M.prototype,"container",void 0),g([_()],M.prototype,"content",void 0),g([_()],M.prototype,"open",void 0),g([_(),je("esri/t9n/common")],M.prototype,"messages",void 0),g([_()],M.prototype,"title",null),M=g([Z("esri.identity.IdentityModal")],M);const Ee=M,be="esriJSAPIOAuth";class Ie{constructor(e,t){this.oAuthInfo=null,this.storage=null,this.appId=null,this.codeVerifier=null,this.expires=null,this.refreshToken=null,this.ssl=null,this.stateUID=null,this.token=null,this.userId=null,this.oAuthInfo=e,this.storage=t,this._init()}isValid(){let e=!1;if(this.oAuthInfo&&this.userId&&(this.refreshToken||this.token)){if(this.expires==null&&this.refreshToken)e=!0;else if(this.expires){const t=Date.now();this.expires>t&&(this.expires-t)/1e3>60*this.oAuthInfo.minTimeUntilExpiration&&(e=!0)}}return e}save(){if(!this.storage)return!1;const e=this._load(),t=this.oAuthInfo;if(t&&t.authNamespace&&t.portalUrl){let r=e[t.authNamespace];r||(r=e[t.authNamespace]={}),this.appId||(this.appId=t.appId),r[t.portalUrl]={appId:this.appId,codeVerifier:this.codeVerifier,expires:this.expires,refreshToken:this.refreshToken,ssl:this.ssl,stateUID:this.stateUID,token:this.token,userId:this.userId};try{this.storage.setItem(be,JSON.stringify(e))}catch(i){return console.warn(i),!1}return!0}return!1}destroy(){const e=this._load(),t=this.oAuthInfo;if(t&&t.appId&&t.portalUrl&&(this.expires==null||this.expires>Date.now())&&(this.refreshToken||this.token)){const r=t.portalUrl.replace(/^http:/i,"https:")+"/sharing/rest/oauth2/revokeToken",i=new FormData;if(i.append("f","json"),i.append("auth_token",this.refreshToken||this.token),i.append("client_id",t.appId),i.append("token_type_hint",this.refreshToken?"refresh_token":"access_token"),typeof navigator.sendBeacon=="function")navigator.sendBeacon(r,i);else{const s=new XMLHttpRequest;s.open("POST",r),s.send(i)}}if(t&&t.authNamespace&&t.portalUrl&&this.storage){const r=e[t.authNamespace];if(r){delete r[t.portalUrl];try{this.storage.setItem(be,JSON.stringify(e))}catch(i){console.log(i)}}}t&&(t._oAuthCred=null,this.oAuthInfo=null)}_init(){const e=this._load(),t=this.oAuthInfo;if(t&&t.authNamespace&&t.portalUrl){let r=e[t.authNamespace];r&&(r=r[t.portalUrl],r&&(this.appId=r.appId,this.codeVerifier=r.codeVerifier,this.expires=r.expires,this.refreshToken=r.refreshToken,this.ssl=r.ssl,this.stateUID=r.stateUID,this.token=r.token,this.userId=r.userId))}}_load(){let e={};if(this.storage){const t=this.storage.getItem(be);if(t)try{e=JSON.parse(t)}catch(r){console.warn(r)}}return e}}Ie.prototype.declaredClass="esri.identity.OAuthCredential";var ke;let C=ke=class extends Le{constructor(o){super(o),this._oAuthCred=null,this.appId=null,this.authNamespace="/",this.expiration=20160,this.flowType="auto",this.forceLogin=!1,this.forceUserId=!1,this.locale=null,this.minTimeUntilExpiration=30,this.popup=!1,this.popupCallbackUrl="oauth-callback.html",this.popupWindowFeatures="height=490,width=800,resizable,scrollbars,status",this.portalUrl="https://www.arcgis.com",this.preserveUrlHash=!1,this.userId=null}clone(){return ke.fromJSON(this.toJSON())}};g([_({json:{write:!0}})],C.prototype,"appId",void 0),g([_({json:{write:!0}})],C.prototype,"authNamespace",void 0),g([_({json:{write:!0}})],C.prototype,"expiration",void 0),g([_({json:{write:!0}})],C.prototype,"flowType",void 0),g([_({json:{write:!0}})],C.prototype,"forceLogin",void 0),g([_({json:{write:!0}})],C.prototype,"forceUserId",void 0),g([_({json:{write:!0}})],C.prototype,"locale",void 0),g([_({json:{write:!0}})],C.prototype,"minTimeUntilExpiration",void 0),g([_({json:{write:!0}})],C.prototype,"popup",void 0),g([_({json:{write:!0}})],C.prototype,"popupCallbackUrl",void 0),g([_({json:{write:!0}})],C.prototype,"popupWindowFeatures",void 0),g([_({json:{write:!0}})],C.prototype,"portalUrl",void 0),g([_({json:{write:!0}})],C.prototype,"preserveUrlHash",void 0),g([_({json:{write:!0}})],C.prototype,"userId",void 0),C=ke=g([Z("esri.identity.OAuthInfo")],C);const Fe=C;let E=class extends Le{constructor(e){super(e),this.adminTokenServiceUrl=null,this.currentVersion=null,this.hasPortal=null,this.hasServer=null,this.owningSystemUrl=null,this.owningTenant=null,this.server=null,this.shortLivedTokenValidity=null,this.tokenServiceUrl=null,this.webTierAuth=null}};g([_({json:{write:!0}})],E.prototype,"adminTokenServiceUrl",void 0),g([_({json:{write:!0}})],E.prototype,"currentVersion",void 0),g([_({json:{write:!0}})],E.prototype,"hasPortal",void 0),g([_({json:{write:!0}})],E.prototype,"hasServer",void 0),g([_({json:{write:!0}})],E.prototype,"owningSystemUrl",void 0),g([_({json:{write:!0}})],E.prototype,"owningTenant",void 0),g([_({json:{write:!0}})],E.prototype,"server",void 0),g([_({json:{write:!0}})],E.prototype,"shortLivedTokenValidity",void 0),g([_({json:{write:!0}})],E.prototype,"tokenServiceUrl",void 0),g([_({json:{write:!0}})],E.prototype,"webTierAuth",void 0),E=g([Z("esri.identity.ServerInfo")],E);const ye=E,ne={},Ye=o=>{const e=new H(o.owningSystemUrl).host,t=new H(o.server).host,r=/.+\.arcgis\.com$/i;return r.test(e)&&r.test(t)},Ae=(o,e)=>!!(Ye(o)&&e&&e.some(t=>t.test(o.server)));let oe=null,ae=null;try{oe=window.localStorage,ae=window.sessionStorage}catch{}class Qe extends Ve{constructor(){super(),this._portalConfig=globalThis.esriGeowConfig,this.serverInfos=[],this.oAuthInfos=[],this.credentials=[],this._soReqs=[],this._xoReqs=[],this._portals=[],this._defaultOAuthInfo=null,this._defaultTokenValidity=60,this.dialog=null,this.formConstructor=ht,this.tokenValidity=null,this.normalizeWebTierAuth=!1,this._appOrigin=window.origin!=="null"?window.origin:window.location.origin,this._appUrlObj=fe(window.location.href),this._busy=null,this._rejectOnPersistedPageShow=!1,this._oAuthLocationParams=null,this._gwTokenUrl="/sharing/rest/generateToken",this._agsRest="/rest/services",this._agsPortal=/\/sharing(\/|$)/i,this._agsAdmin=/(https?:\/\/[^\/]+\/[^\/]+)\/admin\/?(\/.*)?$/i,this._adminSvcs=/\/rest\/admin\/services(\/|$)/i,this._gwDomains=[{regex:/^https?:\/\/www\.arcgis\.com/i,customBaseUrl:"maps.arcgis.com",tokenServiceUrl:"https://www.arcgis.com/sharing/rest/generateToken"},{regex:/^https?:\/\/(?:dev|[a-z\d-]+\.mapsdev)\.arcgis\.com/i,customBaseUrl:"mapsdev.arcgis.com",tokenServiceUrl:"https://dev.arcgis.com/sharing/rest/generateToken"},{regex:/^https?:\/\/(?:devext|[a-z\d-]+\.mapsdevext)\.arcgis\.com/i,customBaseUrl:"mapsdevext.arcgis.com",tokenServiceUrl:"https://devext.arcgis.com/sharing/rest/generateToken"},{regex:/^https?:\/\/(?:qaext|[a-z\d-]+\.mapsqa)\.arcgis\.com/i,customBaseUrl:"mapsqa.arcgis.com",tokenServiceUrl:"https://qaext.arcgis.com/sharing/rest/generateToken"},{regex:/^https?:\/\/[a-z\d-]+\.maps\.arcgis\.com/i,customBaseUrl:"maps.arcgis.com",tokenServiceUrl:"https://www.arcgis.com/sharing/rest/generateToken"}],this._legacyFed=[],this._regexSDirUrl=/http.+\/rest\/services\/?/gi,this._regexServerType=/(\/(FeatureServer|GPServer|GeoDataServer|GeocodeServer|GeoenrichmentServer|GeometryServer|GlobeServer|ImageServer|KnowledgeGraphServer|MapServer|MissionServer|MobileServer|NAServer|NetworkDiagramServer|OGCFeatureServer|ParcelFabricServer|RelationalCatalogServer|SceneServer|StreamServer|UtilityNetworkServer|ValidationServer|VectorTileServer|VersionManagementServer|VideoServer)).*/gi,this._gwUser=/http.+\/users\/([^\/]+)\/?.*/i,this._gwItem=/http.+\/items\/([^\/]+)\/?.*/i,this._gwGroup=/http.+\/groups\/([^\/]+)\/?.*/i,this._rePortalTokenSvc=/\/sharing(\/rest)?\/generatetoken/i,this._createDefaultOAuthInfo=!0,this._hasTestedIfAppIsOnPortal=!1,this._getOAuthLocationParams(),window.addEventListener("pageshow",e=>{this._pageShowHandler(e)})}registerServers(e){const t=this.serverInfos;t?(e=e.filter(r=>!this.findServerInfo(r.server)),this.serverInfos=t.concat(e)):this.serverInfos=e,e.forEach(r=>{r.owningSystemUrl&&this._portals.push(r.owningSystemUrl),r.hasPortal&&this._portals.push(r.server)})}registerOAuthInfos(e){const t=this.oAuthInfos;if(t){for(const r of e){const i=this.findOAuthInfo(r.portalUrl);i&&t.splice(t.indexOf(i),1)}this.oAuthInfos=t.concat(e)}else this.oAuthInfos=e}registerToken(e){e={...e};const t=this._sanitizeUrl(e.server),r=this._isServerRsrc(t);let i,s=this.findServerInfo(t),n=!0;s||(s=new ye,s.server=this._getServerInstanceRoot(t),r?s.hasServer=!0:(s.tokenServiceUrl=this._getTokenSvcUrl(t),s.hasPortal=!0),this.registerServers([s])),i=this._findCredential(t),i?(delete e.server,Object.assign(i,e),n=!1):(i=new k({userId:e.userId,server:s.server,token:e.token,expires:e.expires,ssl:e.ssl,scope:r?"server":"portal"}),i.resources=[t],this.credentials.push(i)),i.emitTokenChange(!1),n||i.refreshServerTokens()}toJSON(){return qe({serverInfos:this.serverInfos.map(e=>e.toJSON()),oAuthInfos:this.oAuthInfos.map(e=>e.toJSON()),credentials:this.credentials.map(e=>e.toJSON())})}initialize(e){if(!e)return;typeof e=="string"&&(e=JSON.parse(e));const t=e.serverInfos,r=e.oAuthInfos,i=e.credentials;if(t){const s=[];t.forEach(n=>{n.server&&n.tokenServiceUrl&&s.push(n.declaredClass?n:new ye(n))}),s.length&&this.registerServers(s)}if(r){const s=[];r.forEach(n=>{n.appId&&s.push(n.declaredClass?n:new Fe(n))}),s.length&&this.registerOAuthInfos(s)}i&&i.forEach(s=>{s.server&&s.token&&s.expires&&s.expires>Date.now()&&((s=s.declaredClass?s:new k(s)).emitTokenChange(),this.credentials.push(s))})}findServerInfo(e){let t;e=this._sanitizeUrl(e);for(const r of this.serverInfos)if(this._hasSameServerInstance(r.server,e)){t=r;break}return t}findOAuthInfo(e){let t;e=this._sanitizeUrl(e);for(const r of this.oAuthInfos)if(this._hasSameServerInstance(r.portalUrl,e)){t=r;break}return t}findCredential(e,t){if(!e)return;let r;e=this._sanitizeUrl(e);const i=this._isServerRsrc(e)?"server":"portal";if(t){for(const s of this.credentials)if(this._hasSameServerInstance(s.server,e)&&t===s.userId&&s.scope===i){r=s;break}}else for(const s of this.credentials)if(this._hasSameServerInstance(s.server,e)&&this._getIdenticalSvcIdx(e,s)!==-1&&s.scope===i){r=s;break}return r}getCredential(e,t){let r,i,s=!0;t&&(r=!!t.token,i=t.error,s=t.prompt!==!1),t={...t},e=this._sanitizeUrl(e);const n=new AbortController,l=ee();if(t.signal&&Y(t.signal,()=>{n.abort()}),Y(n,()=>{l.reject(new O("identity-manager:user-aborted","ABORTED"))}),at(n))return l.promise;t.signal=n.signal;const u=this._isAdminResource(e),a=r?this.findCredential(e):null;let h;if(a&&i&&i.details&&i.details.httpStatus===498)a.destroy();else if(a)return h=new O("identity-manager:not-authorized","You are currently signed in as: '"+a.userId+"'. You do not have access to this resource: "+e,{error:i}),l.reject(h),l.promise;const p=this._findCredential(e,t);if(p)return l.resolve(p),l.promise;let d=this.findServerInfo(e);if(d)!d.hasServer&&this._isServerRsrc(e)&&(d._restInfoPms=this._getTokenSvcUrl(e),d.hasServer=!0);else{const f=this._getTokenSvcUrl(e);if(!f)return h=new O("identity-manager:unknown-resource","Unknown resource - could not find token service endpoint."),l.reject(h),l.promise;d=new ye,d.server=this._getServerInstanceRoot(e),typeof f=="string"?(d.tokenServiceUrl=f,d.hasPortal=!0):(d._restInfoPms=f,d.hasServer=!0),this.registerServers([d])}return d.hasPortal&&d._selfReq===void 0&&(s||V(d.tokenServiceUrl,this._appOrigin)||this._gwDomains.some(f=>f.tokenServiceUrl===d.tokenServiceUrl))&&(d._selfReq={owningTenant:t&&t.owningTenant,selfDfd:this._getPortalSelf(d.tokenServiceUrl.replace(this._rePortalTokenSvc,"/sharing/rest/portals/self"),e)}),this._enqueue(e,d,t,l,u)}getResourceName(e){return this._isRESTService(e)?e.replace(this._regexSDirUrl,"").replace(this._regexServerType,"")||"":this._gwUser.test(e)&&e.replace(this._gwUser,"$1")||this._gwItem.test(e)&&e.replace(this._gwItem,"$1")||this._gwGroup.test(e)&&e.replace(this._gwGroup,"$1")||""}generateToken(e,t,r){const i=this._rePortalTokenSvc.test(e.tokenServiceUrl),s=new H(this._appOrigin),n=e.shortLivedTokenValidity;let l,u,a,h,p,d,f,w;t&&(w=this.tokenValidity||n||this._defaultTokenValidity,w>n&&n>0&&(w=n)),r&&(l=r.isAdmin,u=r.serverUrl,a=r.token,d=r.signal,f=r.ssl,e.customParameters=r.customParameters),l?h=e.adminTokenServiceUrl:(h=e.tokenServiceUrl,p=new H(h.toLowerCase()),e.webTierAuth&&(r!=null&&r.serverUrl)&&!f&&s.scheme==="http"&&(V(s.uri,h,!0)||p.scheme==="https"&&s.host===p.host&&s.port==="7080"&&p.port==="7443")&&(h=h.replace(/^https:/i,"http:").replace(/:7443/i,":7080")));const b={query:{request:"getToken",username:t==null?void 0:t.username,password:t==null?void 0:t.password,serverUrl:u,token:a,expiration:w,referer:l||i?this._appOrigin:null,client:l?"referer":null,f:"json",...e.customParameters},method:"post",authMode:"anonymous",useProxy:this._useProxy(e,r),signal:d,...r==null?void 0:r.ioArgs};return i||(b.withCredentials=!1),q(h,b).then(I=>{const A=I.data;if(!A||!A.token)return new O("identity-manager:authentication-failed","Unable to generate token");const L=e.server;return ne[L]||(ne[L]={}),t&&(ne[L][t.username]=t.password),A.validity=w,A})}isBusy(){return!!this._busy}checkSignInStatus(e){return this.checkAppAccess(e,"").then(t=>t.credential)}checkAppAccess(e,t,r){let i=!1;return this.getCredential(e,{prompt:!1}).then(s=>{let n;const l={f:"json"};if(s.scope==="portal")if(t&&(this._doPortalSignIn(e)||r&&r.force))n=s.server+"/sharing/rest/oauth2/validateAppAccess",l.client_id=t;else{if(!s.token)return{credential:s};n=s.server+"/sharing/rest"}else{if(!s.token)return{credential:s};n=s.server+"/rest/services"}return s.token&&(l.token=s.token),q(n,{query:l,authMode:"anonymous"}).then(u=>{if(u.data.valid===!1)throw new O("identity-manager:not-authorized",`You are currently signed in as: '${s.userId}'.`,u.data);return i=!!u.data.viewOnlyUserTypeApp,{credential:s}}).catch(u=>{if(u.name==="identity-manager:not-authorized")throw u;const a=u.details&&u.details.httpStatus;if(a===498)throw s.destroy(),new O("identity-manager:not-authenticated","User is not signed in.");if(a===400)throw new O("identity-manager:invalid-request");return{credential:s}})}).then(s=>({credential:s.credential,viewOnly:i}))}setOAuthResponseHash(e){e&&(e.charAt(0)==="#"&&(e=e.substring(1)),this._processOAuthPopupParams($(e)))}setOAuthRedirectionHandler(e){this._oAuthRedirectFunc=e}setProtocolErrorHandler(e){this._protocolFunc=e}signIn(e,t,r={}){const i=ee(),s=()=>{var p;u==null||u.remove(),a==null||a.remove(),h==null||h.remove(),l==null||l.destroy(),(p=this.dialog)==null||p.destroy(),this.dialog=l=u=a=h=null},n=()=>{s(),this._oAuthDfd=null,i.reject(new O("identity-manager:user-aborted","ABORTED"))};r.signal&&Y(r.signal,()=>{n()});let l=new this.formConstructor;l.resource=this.getResourceName(e),l.server=t.server,this.dialog=new Ee,this.dialog.content=l,this.dialog.open=!0,this.emit("dialog-create");let u=l.on("cancel",n),a=we(()=>this.dialog.open,n),h=l.on("submit",p=>{this.generateToken(t,p,{isAdmin:r.isAdmin,signal:r.signal}).then(d=>{s();const f=new k({userId:p.username,server:t.server,token:d.token,expires:d.expires!=null?Number(d.expires):null,ssl:!!d.ssl,isAdmin:r.isAdmin,validity:d.validity});i.resolve(f)}).catch(d=>{l.error=d,l.signingIn=!1})});return i.promise}oAuthSignIn(e,t,r,i){this._oAuthDfd=ee();const s=this._oAuthDfd;let n;i!=null&&i.signal&&Y(i.signal,()=>{const w=this._oAuthDfd&&this._oAuthDfd.oAuthWin_;w&&!w.closed?w.close():this.dialog&&d()}),s.resUrl_=e,s.sinfo_=t,s.oinfo_=r;const l=r._oAuthCred;if(l.storage&&(r.flowType==="authorization-code"||r.flowType==="auto"&&!r.popup&&t.currentVersion>=8.4)){let w=crypto.getRandomValues(new Uint8Array(32));n=ve(w),l.codeVerifier=n,w=crypto.getRandomValues(new Uint8Array(32)),l.stateUID=ve(w),l.save()||(l.codeVerifier=n=null)}else l.codeVerifier=null;let u,a,h,p;this._getCodeChallenge(n).then(w=>{const b=!i||i.oAuthPopupConfirmation!==!1;r.popup&&b?(u=new this.formConstructor,u.oAuthPrompt=!0,u.server=t.server,this.dialog=new Ee,this.dialog.content=u,this.dialog.open=!0,this.emit("dialog-create"),a=u.on("cancel",d),h=we(()=>this.dialog.open,d),p=u.on("submit",()=>{f(),this._doOAuthSignIn(e,t,r,w)})):this._doOAuthSignIn(e,t,r,w)});const d=()=>{f(),this._oAuthDfd=null,s.reject(new O("identity-manager:user-aborted","ABORTED"))},f=()=>{var w;a==null||a.remove(),h==null||h.remove(),p==null||p.remove(),u==null||u.destroy(),(w=this.dialog)==null||w.destroy(),this.dialog=null};return s.promise}destroyCredentials(){this.credentials&&this.credentials.slice().forEach(e=>{e.destroy()}),this.emit("credentials-destroy")}enablePostMessageAuth(e="https://www.arcgis.com/sharing/rest"){this._postMessageAuthHandle&&this._postMessageAuthHandle.remove(),this._postMessageAuthHandle=ge(window,"message",t=>{var r;if((t.origin===this._appOrigin||t.origin.endsWith(".arcgis.com"))&&((r=t.data)==null?void 0:r.type)==="arcgis:auth:requestCredential"){const i=t.source;this.getCredential(e).then(s=>{i.postMessage({type:"arcgis:auth:credential",credential:{expires:s.expires,server:s.server,ssl:s.ssl,token:s.token,userId:s.userId}},t.origin)}).catch(s=>{i.postMessage({type:"arcgis:auth:error",error:{name:s.name,message:s.message}},t.origin)})}})}disablePostMessageAuth(){this._postMessageAuthHandle&&(this._postMessageAuthHandle.remove(),this._postMessageAuthHandle=null)}_getOAuthLocationParams(){var r,i;let e=window.location.hash;if(e){e.charAt(0)==="#"&&(e=e.substring(1));const s=$(e);let n=!1;if(s.access_token&&s.expires_in&&s.state&&s.hasOwnProperty("username"))try{s.state=JSON.parse(s.state),s.state.portalUrl&&(this._oAuthLocationParams=s,n=!0)}catch{}else if(s.error&&s.error_description&&(console.log("IdentityManager OAuth Error: ",s.error," - ",s.error_description),s.error==="access_denied"&&(n=!0,s.state)))try{s.state=JSON.parse(s.state)}catch{}n&&(window.location.hash=((r=s.state)==null?void 0:r.hash)||"")}let t=window.location.search;if(t){t.charAt(0)==="?"&&(t=t.substring(1));const s=$(t);let n=!1;if(s.code&&s.state)try{s.state=JSON.parse(s.state),s.state.portalUrl&&s.state.uid&&(this._oAuthLocationParams=s,n=!0)}catch{}else if(s.error&&s.error_description&&(console.log("IdentityManager OAuth Error: ",s.error," - ",s.error_description),s.error==="access_denied"&&(n=!0,s.state)))try{s.state=JSON.parse(s.state)}catch{}if(n){const l={...s};["code","error","error_description","message_code","persist","state"].forEach(h=>{delete l[h]});const u=Te(l),a=window.location.pathname+(u?`?${u}`:"")+(((i=s.state)==null?void 0:i.hash)||"");window.history.replaceState(window.history.state,"",a)}}}_getOAuthToken(e,t,r,i,s){return e=e.replace(/^http:/i,"https:"),q(`${e}/sharing/rest/oauth2/token`,{authMode:"anonymous",method:"post",query:i&&s?{grant_type:"authorization_code",code:t,redirect_uri:i,client_id:r,code_verifier:s}:{grant_type:"refresh_token",refresh_token:t,client_id:r}}).then(n=>n.data)}_getCodeChallenge(e){if(e&&globalThis.isSecureContext){const t=new TextEncoder().encode(e);return crypto.subtle.digest("SHA-256",t).then(r=>ve(new Uint8Array(r)))}return Promise.resolve(null)}_pageShowHandler(e){if(e.persisted&&this.isBusy()&&this._rejectOnPersistedPageShow){const t=new O("identity-manager:user-aborted","ABORTED");this._errbackFunc(t)}}_findCredential(e,t){let r,i,s,n,l=-1;const u=t&&t.token,a=t&&t.resource,h=this._isServerRsrc(e)?"server":"portal",p=this.credentials.filter(d=>this._hasSameServerInstance(d.server,e)&&d.scope===h);if(e=a||e,p.length)if(p.length===1){if(r=p[0],s=this.findServerInfo(r.server),i=s&&s.owningSystemUrl,n=i?this.findCredential(i,r.userId):void 0,l=this._getIdenticalSvcIdx(e,r),!u)return l===-1&&r.resources.push(e),this._addResource(e,n),r;l!==-1&&(r.resources.splice(l,1),this._removeResource(e,n))}else{let d,f;if(p.some(w=>(f=this._getIdenticalSvcIdx(e,w),f!==-1&&(d=w,s=this.findServerInfo(d.server),i=s&&s.owningSystemUrl,n=i?this.findCredential(i,d.userId):void 0,l=f,!0))),u)d&&(d.resources.splice(l,1),this._removeResource(e,n));else if(d)return this._addResource(e,n),d}}_findOAuthInfo(e){let t=this.findOAuthInfo(e);if(!t){for(const r of this.oAuthInfos)if(this._isIdProvider(r.portalUrl,e)){t=r;break}}return t}_addResource(e,t){t&&this._getIdenticalSvcIdx(e,t)===-1&&t.resources.push(e)}_removeResource(e,t){let r=-1;t&&(r=this._getIdenticalSvcIdx(e,t),r>-1&&t.resources.splice(r,1))}_useProxy(e,t){return t&&t.isAdmin&&!V(e.adminTokenServiceUrl,this._appOrigin)||!this._isPortalDomain(e.tokenServiceUrl)&&String(e.currentVersion)==="10.1"&&!V(e.tokenServiceUrl,this._appOrigin)}_getOrigin(e){const t=new H(e);return t.scheme+"://"+t.host+(t.port!=null?":"+t.port:"")}_getServerInstanceRoot(e){const t=e.toLowerCase();let r=t.indexOf(this._agsRest);return r===-1&&this._isAdminResource(e)&&(r=this._agsAdmin.test(e)?e.replace(this._agsAdmin,"$1").length:e.search(this._adminSvcs)),r!==-1||tt(t)||(r=t.indexOf("/sharing")),r===-1&&t.substr(-1)==="/"&&(r=t.length-1),r>-1?e.substring(0,r):e}_hasSameServerInstance(e,t){return e.substr(-1)==="/"&&(e=e.slice(0,-1)),e=e.toLowerCase(),t=this._getServerInstanceRoot(t).toLowerCase(),e=this._normalizeAGOLorgDomain(e),t=this._normalizeAGOLorgDomain(t),(e=e.substr(e.indexOf(":")))===(t=t.substr(t.indexOf(":")))}_normalizeAGOLorgDomain(e){const t=/^https?:\/\/(?:cdn|[a-z\d-]+\.maps)\.arcgis\.com/i,r=/^https?:\/\/(?:cdndev|[a-z\d-]+\.mapsdevext)\.arcgis\.com/i,i=/^https?:\/\/(?:cdnqa|[a-z\d-]+\.mapsqa)\.arcgis\.com/i;return t.test(e)?e=e.replace(t,"https://www.arcgis.com"):r.test(e)?e=e.replace(r,"https://devext.arcgis.com"):i.test(e)&&(e=e.replace(i,"https://qaext.arcgis.com")),e}_sanitizeUrl(e){const t=(_e.request.proxyUrl||"").toLowerCase(),r=t?e.toLowerCase().indexOf(t+"?"):-1;return r!==-1&&(e=e.substring(r+t.length+1)),e=rt(e),fe(e).path}_isRESTService(e){return e.includes(this._agsRest)}_isAdminResource(e){return this._agsAdmin.test(e)||this._adminSvcs.test(e)}_isServerRsrc(e){return this._isRESTService(e)||this._isAdminResource(e)}_isIdenticalService(e,t){let r=!1;if(this._isRESTService(e)&&this._isRESTService(t)){const i=this._getSuffix(e).toLowerCase(),s=this._getSuffix(t).toLowerCase();if(r=i===s,!r){const n=/(.*)\/(MapServer|FeatureServer|UtilityNetworkServer).*/gi;r=i.replace(n,"$1")===s.replace(n,"$1")}}else this._isAdminResource(e)&&this._isAdminResource(t)?r=!0:this._isServerRsrc(e)||this._isServerRsrc(t)||!this._isPortalDomain(e)||(r=!0);return r}_isPortalDomain(e){const t=new H(e.toLowerCase()),r=this._portalConfig;let i=this._gwDomains.some(s=>s.regex.test(t.uri));return!i&&r&&(i=this._hasSameServerInstance(this._getServerInstanceRoot(r.restBaseUrl),t.uri)),i||_e.portalUrl&&(i=V(t,_e.portalUrl,!0)),i||(i=this._portals.some(s=>this._hasSameServerInstance(s,t.uri))),i=i||this._agsPortal.test(t.path),i}_isIdProvider(e,t){let r=-1,i=-1;this._gwDomains.forEach((n,l)=>{r===-1&&n.regex.test(e)&&(r=l),i===-1&&n.regex.test(t)&&(i=l)});let s=!1;if(r>-1&&i>-1&&(r===0||r===4?i!==0&&i!==4||(s=!0):r===1?i!==1&&i!==2||(s=!0):r===2?i===2&&(s=!0):r===3&&i===3&&(s=!0)),!s){const n=this.findServerInfo(t),l=n&&n.owningSystemUrl;l&&Ye(n)&&this._isPortalDomain(l)&&this._isIdProvider(e,l)&&(s=!0)}return s}_getIdenticalSvcIdx(e,t){let r=-1;for(let i=0;i<t.resources.length;i++){const s=t.resources[i];if(this._isIdenticalService(e,s)){r=i;break}}return r}_getSuffix(e){return e.replace(this._regexSDirUrl,"").replace(this._regexServerType,"$1")}_getTokenSvcUrl(e){let t,r,i;if(this._isRESTService(e)||this._isAdminResource(e)){const s=this._getServerInstanceRoot(e);return t=s+"/admin/generateToken",r=q(e=s+"/rest/info",{query:{f:"json"}}).then(n=>n.data),{adminUrl:t,promise:r}}if(this._isPortalDomain(e)){let s="";if(this._gwDomains.some(n=>(n.regex.test(e)&&(s=n.tokenServiceUrl),!!s)),s||this._portals.some(n=>(this._hasSameServerInstance(n,e)&&(s=n+this._gwTokenUrl),!!s)),s||(i=e.toLowerCase().indexOf("/sharing"),i!==-1&&(s=e.substring(0,i)+this._gwTokenUrl)),s||(s=this._getOrigin(e)+this._gwTokenUrl),s){const n=new H(e).port;/^http:\/\//i.test(e)&&n==="7080"&&(s=s.replace(/:7080/i,":7443")),s=s.replace(/http:/i,"https:")}return s}if(e.toLowerCase().includes("premium.arcgisonline.com"))return"https://premium.arcgisonline.com/server/tokens"}_processOAuthResponseParams(e,t,r){const i=t._oAuthCred;if(e.code){const n=i.codeVerifier;return i.codeVerifier=null,i.stateUID=null,i.save(),this._getOAuthToken(r.server,e.code,t.appId,this._getRedirectURI(t,!0),n).then(l=>{const u=new k({userId:l.username,server:r.server,token:l.access_token,expires:Date.now()+1e3*l.expires_in,ssl:l.ssl,oAuthState:e.state,_oAuthCred:i});return t.userId=u.userId,i.storage=l.persist?oe:ae,i.refreshToken=l.refresh_token,i.token=null,i.expires=l.refresh_token_expires_in?Date.now()+1e3*l.refresh_token_expires_in:null,i.userId=u.userId,i.ssl=u.ssl,i.save(),u})}const s=new k({userId:e.username,server:r.server,token:e.access_token,expires:Date.now()+1e3*Number(e.expires_in),ssl:e.ssl==="true",oAuthState:e.state,_oAuthCred:i});return t.userId=s.userId,i.storage=e.persist?oe:ae,i.refreshToken=null,i.token=s.token,i.expires=s.expires,i.userId=s.userId,i.ssl=s.ssl,i.save(),Promise.resolve(s)}_processOAuthPopupParams(e){var r;const t=this._oAuthDfd;if(this._oAuthDfd=null,t)if(clearInterval(this._oAuthIntervalId),(r=this._oAuthOnPopupHandle)==null||r.remove(),e.error){const i=e.error==="access_denied",s=new O(i?"identity-manager:user-aborted":"identity-manager:authentication-failed",i?"ABORTED":"OAuth: "+e.error+" - "+e.error_description);t.reject(s)}else this._processOAuthResponseParams(e,t.oinfo_,t.sinfo_).then(i=>{t.resolve(i)}).catch(i=>{t.reject(i)})}_setOAuthResponseQueryString(e){e&&(e.charAt(0)==="?"&&(e=e.substring(1)),this._processOAuthPopupParams($(e)))}_exchangeToken(e,t,r){return q(`${e}/sharing/rest/oauth2/exchangeToken`,{authMode:"anonymous",method:"post",query:{f:"json",client_id:t,token:r}}).then(i=>i.data.token)}_getPlatformSelf(e,t){return e=e.replace(/^http:/i,"https:"),q(`${e}/sharing/rest/oauth2/platformSelf`,{authMode:"anonymous",headers:{"X-Esri-Auth-Client-Id":t,"X-Esri-Auth-Redirect-Uri":window.location.href.replace(/#.*$/,"")},method:"post",query:{f:"json",expiration:30},withCredentials:!0}).then(r=>r.data)}_getPortalSelf(e,t){let r;return this._gwDomains.some(i=>(i.regex.test(e)&&(r=i.customBaseUrl),!!r)),r?Promise.resolve({allSSL:!0,currentVersion:"8.4",customBaseUrl:r,portalMode:"multitenant",supportsOAuth:!0}):(this._appOrigin.startsWith("https:")?e=e.replace(/^http:/i,"https:").replace(/:7080/i,":7443"):/^http:/i.test(t)&&(e=e.replace(/^https:/i,"http:").replace(/:7443/i,":7080")),q(e,{query:{f:"json"},authMode:"anonymous",withCredentials:!0}).then(i=>i.data))}_doPortalSignIn(e){const t=this._portalConfig,r=window.location.href,i=this.findServerInfo(e);return!(!t&&!this._isPortalDomain(r)||!(i?i.hasPortal||i.owningSystemUrl&&this._isPortalDomain(i.owningSystemUrl):this._isPortalDomain(e))||!(this._isIdProvider(r,e)||t&&(this._hasSameServerInstance(this._getServerInstanceRoot(t.restBaseUrl),e)||this._isIdProvider(t.restBaseUrl,e))||V(r,e,!0)))}_checkProtocol(e,t,r,i){let s=!0;const n=i?t.adminTokenServiceUrl:t.tokenServiceUrl;return n.trim().toLowerCase().startsWith("https:")&&!this._appOrigin.startsWith("https:")&&st(n)&&(s=!!this._protocolFunc&&!!this._protocolFunc({resourceUrl:e,serverInfo:t}),!s)&&r(new O("identity-manager:aborted","Aborted the Sign-In process to avoid sending password over insecure connection.")),s}_enqueue(e,t,r,i,s,n){return i||(i=ee()),i.resUrl_=e,i.sinfo_=t,i.options_=r,i.admin_=s,i.refresh_=n,this._busy?this._hasSameServerInstance(this._getServerInstanceRoot(e),this._busy.resUrl_)?(this._oAuthDfd&&this._oAuthDfd.oAuthWin_&&this._oAuthDfd.oAuthWin_.focus(),this._soReqs.push(i)):this._xoReqs.push(i):this._doSignIn(i),i.promise}_doSignIn(e){this._busy=e,this._rejectOnPersistedPageShow=!1;const t=a=>{const h=e.options_&&e.options_.resource,p=e.resUrl_,d=e.refresh_;let f=!1;this.credentials.includes(a)||(d&&this.credentials.includes(d)?(d.userId=a.userId,d.token=a.token,d.expires=a.expires,d.validity=a.validity,d.ssl=a.ssl,d.creationTime=a.creationTime,f=!0,a=d):this.credentials.push(a)),a.resources||(a.resources=[]),a.resources.includes(h||p)||a.resources.push(h||p),a.scope=this._isServerRsrc(p)?"server":"portal",a.emitTokenChange();const w=this._soReqs,b={};this._soReqs=[],w.forEach(I=>{if(!this._isIdenticalService(p,I.resUrl_)){const A=this._getSuffix(I.resUrl_);b[A]||(b[A]=!0,a.resources.push(I.resUrl_))}}),e.resolve(a),w.forEach(I=>{this._hasSameServerInstance(this._getServerInstanceRoot(p),I.resUrl_)?I.resolve(a):this._soReqs.push(I)}),this._busy=e.resUrl_=e.sinfo_=e.refresh_=null,f||this.emit("credential-create",{credential:a}),this._soReqs.length?this._doSignIn(this._soReqs.shift()):this._xoReqs.length&&this._doSignIn(this._xoReqs.shift())},r=a=>{e.reject(a),this._busy=e.resUrl_=e.sinfo_=e.refresh_=null,this._soReqs.length?this._doSignIn(this._soReqs.shift()):this._xoReqs.length&&this._doSignIn(this._xoReqs.shift())},i=(a,h,p,d)=>{var L,K,J;const f=e.sinfo_,w=!e.options_||e.options_.prompt!==!1,b=f.hasPortal&&this._findOAuthInfo(e.resUrl_);let I,A;if(a)t(new k({userId:a,server:f.server,token:p||null,expires:d!=null?Number(d):null,ssl:!!h}));else if(window!==window.parent&&((L=this._appUrlObj.query)!=null&&L["arcgis-auth-origin"])&&((K=this._appUrlObj.query)!=null&&K["arcgis-auth-portal"])&&this._hasSameServerInstance(this._getServerInstanceRoot(this._appUrlObj.query["arcgis-auth-portal"]),e.resUrl_)){window.parent.postMessage({type:"arcgis:auth:requestCredential"},this._appUrlObj.query["arcgis-auth-origin"]);const S=ge(window,"message",m=>{m.source===window.parent&&m.data&&(m.data.type==="arcgis:auth:credential"?(S.remove(),m.data.credential.expires<Date.now()?r(new O("identity-manager:credential-request-failed","Parent application's token has expired.")):t(new k(m.data.credential))):m.data.type==="arcgis:auth:error"&&(S.remove(),m.data.error.name==="tokenExpiredError"?r(new O("identity-manager:credential-request-failed","Parent application's token has expired.")):r(O.fromJSON(m.data.error))))});Y((J=e.options_)==null?void 0:J.signal,()=>{S.remove()})}else if(b){let S=b._oAuthCred;if(!S){const m=new Ie(b,oe),c=new Ie(b,ae);m.isValid()&&c.isValid()?m.expires>c.expires?(S=m,c.destroy()):(S=c,m.destroy()):S=m.isValid()?m:c,b._oAuthCred=S}if(S.isValid()){I=new k({userId:S.userId,server:f.server,token:S.token,expires:S.expires,ssl:S.ssl,_oAuthCred:S});const m=b.appId!==S.appId&&this._doPortalSignIn(e.resUrl_);m||S.refreshToken?(e._pendingDfd=S.refreshToken?this._getOAuthToken(f.server,S.refreshToken,S.appId).then(c=>(I.expires=Date.now()+1e3*c.expires_in,I.token=c.access_token,I)):Promise.resolve(I),e._pendingDfd.then(c=>m?this._exchangeToken(c.server,b.appId,c.token).then(v=>(c.token=v,c)).catch(()=>c):c).then(c=>{t(c)}).catch(()=>{S==null||S.destroy(),i()})):t(I)}else if(this._oAuthLocationParams&&this._hasSameServerInstance(b.portalUrl,this._oAuthLocationParams.state.portalUrl)&&(this._oAuthLocationParams.access_token||this._oAuthLocationParams.code&&this._oAuthLocationParams.state.uid===S.stateUID&&S.codeVerifier)){const m=this._oAuthLocationParams;this._oAuthLocationParams=null,e._pendingDfd=this._processOAuthResponseParams(m,b,f).then(c=>{t(c)}).catch(r)}else{const m=()=>{w?e._pendingDfd=this.oAuthSignIn(e.resUrl_,f,b,e.options_).then(t,r):(A=new O("identity-manager:not-authenticated","User is not signed in."),r(A))};this._doPortalSignIn(e.resUrl_)?e._pendingDfd=this._getPlatformSelf(f.server,b.appId).then(c=>{V(c.portalUrl,this._appOrigin,!0)?(I=new k({userId:c.username,server:f.server,expires:Date.now()+1e3*c.expires_in,token:c.token}),t(I)):m()}).catch(m):m()}}else if(w){if(this._checkProtocol(e.resUrl_,f,r,e.admin_)){let S=e.options_;e.admin_&&(S=S||{},S.isAdmin=!0),e._pendingDfd=this.signIn(e.resUrl_,f,S).then(t,r)}}else A=new O("identity-manager:not-authenticated","User is not signed in."),r(A)},s=()=>{const a=e.sinfo_,h=a.owningSystemUrl,p=e.options_;let d,f,w,b;if(p&&(d=p.token,f=p.error,w=p.prompt),b=this._findCredential(h,{token:d,resource:e.resUrl_}),!b){for(const I of this.credentials)if(this._isIdProvider(h,I.server)){b=I;break}}if(b){const I=this.findCredential(e.resUrl_,b.userId);if(I)t(I);else if(Ae(a,this._legacyFed)){const A=b.toJSON();A.server=a.server,A.resources=null,t(new k(A))}else(e._pendingDfd=this.generateToken(this.findServerInfo(b.server),null,{serverUrl:e.resUrl_,token:b.token,signal:e.options_.signal,ssl:b.ssl})).then(A=>{t(new k({userId:b==null?void 0:b.userId,server:a.server,token:A.token,expires:A.expires!=null?Number(A.expires):null,ssl:!!A.ssl,isAdmin:e.admin_,validity:A.validity}))},r)}else this._busy=null,d&&(e.options_.token=null),(e._pendingDfd=this.getCredential(h.replace(/\/?$/,"/sharing"),{resource:e.resUrl_,owningTenant:a.owningTenant,signal:e.options_.signal,token:d,error:f,prompt:w})).then(()=>{this._enqueue(e.resUrl_,e.sinfo_,e.options_,e,e.admin_)},I=>{e.resUrl_=e.sinfo_=e.refresh_=null,e.reject(I)})};this._errbackFunc=r;const n=e.sinfo_.owningSystemUrl,l=this._isServerRsrc(e.resUrl_),u=e.sinfo_._restInfoPms;u?u.promise.then(a=>{const h=e.sinfo_;if(h._restInfoPms){h.adminTokenServiceUrl=h._restInfoPms.adminUrl,h._restInfoPms=null,h.tokenServiceUrl=(te("authInfo.tokenServicesUrl",a)||te("authInfo.tokenServiceUrl",a)||te("tokenServiceUrl",a))??null,h.shortLivedTokenValidity=te("authInfo.shortLivedTokenValidity",a)??null,h.currentVersion=a.currentVersion,h.owningTenant=a.owningTenant;const p=h.owningSystemUrl=a.owningSystemUrl;p&&this._portals.push(p)}l&&h.owningSystemUrl?s():i()},()=>{e.sinfo_._restInfoPms=null;const a=new O("identity-manager:server-identification-failed","Unknown resource - could not find token service endpoint.");r(a)}):l&&n?s():e.sinfo_._selfReq?e.sinfo_._selfReq.selfDfd.then(a=>{const h={};let p,d,f,w;return a&&(p=a.user&&a.user.username,h.username=p,h.allSSL=a.allSSL,d=a.supportsOAuth,w=parseFloat(a.currentVersion),a.portalMode==="multitenant"&&(f=a.customBaseUrl),e.sinfo_.currentVersion=w),e.sinfo_.webTierAuth=!!p,p&&this.normalizeWebTierAuth?this.generateToken(e.sinfo_,null,{ssl:h.allSSL}).catch(()=>null).then(b=>(h.portalToken=b&&b.token,h.tokenExpiration=b&&b.expires,h)):!p&&d&&w>=4.4&&!this._findOAuthInfo(e.resUrl_)?this._generateOAuthInfo({portalUrl:e.sinfo_.server,customBaseUrl:f,owningTenant:e.sinfo_._selfReq.owningTenant}).catch(()=>null).then(()=>h):h}).catch(()=>null).then(a=>{e.sinfo_._selfReq=null,a?i(a.username,a.allSSL,a.portalToken,a.tokenExpiration):i()}):i()}_generateOAuthInfo(e){let t,r=null,i=e.portalUrl;const s=e.customBaseUrl,n=e.owningTenant,l=!this._defaultOAuthInfo&&this._createDefaultOAuthInfo&&!this._hasTestedIfAppIsOnPortal;if(l){r=window.location.href;let u=r.indexOf("?");u>-1&&(r=r.slice(0,u)),u=r.search(/\/(apps|home)\//),r=u>-1?r.slice(0,u):null}return l&&r?(this._hasTestedIfAppIsOnPortal=!0,t=q(r+"/sharing/rest",{query:{f:"json"}}).then(()=>{this._defaultOAuthInfo=new Fe({appId:"arcgisonline",popupCallbackUrl:r+"/home/<USER>"})})):t=Promise.resolve(),t.then(()=>{if(this._defaultOAuthInfo)return i=i.replace(/^http:/i,"https:"),q(i+"/sharing/rest/oauth2/validateRedirectUri",{query:{accountId:n,client_id:this._defaultOAuthInfo.appId,redirect_uri:Ue(this._defaultOAuthInfo.popupCallbackUrl),f:"json"}}).then(u=>{if(u.data.valid){const a=this._defaultOAuthInfo.clone();u.data.urlKey&&s?a.portalUrl="https://"+u.data.urlKey.toLowerCase()+"."+s:a.portalUrl=i,a.popup=window!==window.top||!(V(i,this._appOrigin)||this._gwDomains.some(h=>h.regex.test(i)&&h.regex.test(this._appOrigin))),this.oAuthInfos.push(a)}})})}_doOAuthSignIn(e,t,r,i){const s=r._oAuthCred,n={portalUrl:r.portalUrl};!r.popup&&r.preserveUrlHash&&window.location.hash&&(n.hash=window.location.hash),s.stateUID&&(n.uid=s.stateUID);const l={client_id:r.appId,response_type:s.codeVerifier?"code":"token",state:JSON.stringify(n),expiration:r.expiration,locale:r.locale,redirect_uri:this._getRedirectURI(r,!!s.codeVerifier)};r.forceLogin&&(l.force_login=!0),r.forceUserId&&r.userId&&(l.prepopulatedusername=r.userId),!r.popup&&this._doPortalSignIn(e)&&(l.redirectToUserOrgUrl=!0),s.codeVerifier&&(l.code_challenge=i||s.codeVerifier,l.code_challenge_method=i?"S256":"plain");const u=r.portalUrl.replace(/^http:/i,"https:")+"/sharing/oauth2/authorize",a=u+"?"+Te(l);if(r.popup){const h=window.open(a,"esriJSAPIOAuth",r.popupWindowFeatures);if(h)h.focus(),this._oAuthDfd.oAuthWin_=h,this._oAuthIntervalId=setInterval(()=>{if(h.closed){clearInterval(this._oAuthIntervalId),this._oAuthOnPopupHandle.remove();const p=this._oAuthDfd;if(p){const d=new O("identity-manager:user-aborted","ABORTED");p.reject(d)}}},500),this._oAuthOnPopupHandle=ge(window,["arcgis:auth:hash","arcgis:auth:location:search"],p=>{p.type==="arcgis:auth:hash"?this.setOAuthResponseHash(p.detail):this._setOAuthResponseQueryString(p.detail)});else{const p=new O("identity-manager:popup-blocked","ABORTED");this._oAuthDfd.reject(p)}}else this._rejectOnPersistedPageShow=!0,this._oAuthRedirectFunc?this._oAuthRedirectFunc({authorizeParams:l,authorizeUrl:u,resourceUrl:e,serverInfo:t,oAuthInfo:r}):window.location.href=a}_getRedirectURI(e,t){const r=window.location.href.replace(/#.*$/,"");if(e.popup)return Ue(e.popupCallbackUrl);if(t){const i=fe(r);return i.query&&["code","error","error_description","message_code","persist","state"].forEach(s=>{delete i.query[s]}),it(i.path,i.query)}return r}}Qe.prototype.declaredClass="esri.identity.IdentityManagerBase";let k=class extends Ve.EventedAccessor{constructor(o){super(o),this._oAuthCred=null,this.tokenRefreshBuffer=2,o&&o._oAuthCred&&(this._oAuthCred=o._oAuthCred)}initialize(){this.resources=this.resources||[],this.creationTime==null&&(this.creationTime=Date.now())}refreshToken(){const o=x.findServerInfo(this.server),e=o&&o.owningSystemUrl,t=!!e&&this.scope==="server",r=t&&Ae(o,x._legacyFed),i=o.webTierAuth,s=i&&x.normalizeWebTierAuth,n=ne[this.server],l=n&&n[this.userId];let u,a=this.resources&&this.resources[0],h=t?x.findServerInfo(e):null,p={username:this.userId,password:l};if(i&&!s)return;t&&!h&&x.serverInfos.some(f=>(x._isIdProvider(e,f.server)&&(h=f),!!h));const d=h?x.findCredential(h.server,this.userId):null;if(!t||d){if(!r){if(t)u={serverUrl:a,token:d&&d.token,ssl:d&&d.ssl};else if(s)p=null,u={ssl:this.ssl};else{if(!l){let f;return a&&(a=x._sanitizeUrl(a),this._enqueued=1,f=x._enqueue(a,o,null,null,this.isAdmin,this),f.then(()=>{this._enqueued=0,this.refreshServerTokens()}).catch(()=>{this._enqueued=0})),f}this.isAdmin&&(u={isAdmin:!0})}return x.generateToken(t?h:o,t?null:p,u).then(f=>{this.token=f.token,this.expires=f.expires!=null?Number(f.expires):null,this.creationTime=Date.now(),this.validity=f.validity,this.emitTokenChange(),this.refreshServerTokens()}).catch(()=>{})}d==null||d.refreshToken()}}refreshServerTokens(){this.scope==="portal"&&x.credentials.forEach(o=>{const e=x.findServerInfo(o.server),t=e&&e.owningSystemUrl;o!==this&&o.userId===this.userId&&t&&o.scope==="server"&&(x._hasSameServerInstance(this.server,t)||x._isIdProvider(t,this.server))&&(Ae(e,x._legacyFed)?(o.token=this.token,o.expires=this.expires,o.creationTime=this.creationTime,o.validity=this.validity,o.emitTokenChange()):o.refreshToken())})}emitTokenChange(o){clearTimeout(this._refreshTimer);const e=this.server&&x.findServerInfo(this.server),t=e&&e.owningSystemUrl,r=t&&x.findServerInfo(t);o===!1||t&&this.scope!=="portal"&&(!r||!r.webTierAuth||x.normalizeWebTierAuth)||this.expires==null&&this.validity==null||this._startRefreshTimer(),this.emit("token-change")}destroy(){this.userId=this.server=this.token=this.expires=this.validity=this.resources=this.creationTime=null,this._oAuthCred&&(this._oAuthCred.destroy(),this._oAuthCred=null);const o=x.credentials.indexOf(this);o>-1&&x.credentials.splice(o,1),this.emitTokenChange(),this.emit("destroy")}toJSON(){const o=qe({userId:this.userId,server:this.server,token:this.token,expires:this.expires,validity:this.validity,ssl:this.ssl,isAdmin:this.isAdmin,creationTime:this.creationTime,scope:this.scope}),e=this.resources;return e&&e.length>0&&(o.resources=e.slice()),o}_startRefreshTimer(){clearTimeout(this._refreshTimer);const o=6e4*this.tokenRefreshBuffer,e=2**31-1;let t=(this.validity?this.creationTime+6e4*this.validity:this.expires)-Date.now();t<0?t=0:t>e&&(t=e),this._refreshTimer=setTimeout(this.refreshToken.bind(this),t>o?t-o:t)}};g([_()],k.prototype,"creationTime",void 0),g([_()],k.prototype,"expires",void 0),g([_()],k.prototype,"isAdmin",void 0),g([_()],k.prototype,"oAuthState",void 0),g([_()],k.prototype,"resources",void 0),g([_()],k.prototype,"scope",void 0),g([_()],k.prototype,"server",void 0),g([_()],k.prototype,"ssl",void 0),g([_()],k.prototype,"token",void 0),g([_()],k.prototype,"tokenRefreshBuffer",void 0),g([_()],k.prototype,"userId",void 0),g([_()],k.prototype,"validity",void 0),k=g([Z("esri.identity.Credential")],k);class Xe extends Qe{}Xe.prototype.declaredClass="esri.identity.IdentityManager";const qt=new Xe;nt(qt);export{qt as default};
