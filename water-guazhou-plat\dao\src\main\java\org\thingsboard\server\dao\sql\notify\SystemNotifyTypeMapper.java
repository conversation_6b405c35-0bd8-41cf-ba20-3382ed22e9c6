package org.thingsboard.server.dao.sql.notify;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.SystemNotifyTypeListRequest;
import org.thingsboard.server.dao.model.sql.notify.SystemNotifyType;

@Mapper
public interface SystemNotifyTypeMapper extends BaseMapper<SystemNotifyType> {

    IPage<SystemNotifyType> findList(@Param("param") SystemNotifyTypeListRequest request);
}
