import{d as v,c as a,r,o as C,ay as w,g as k,n as x,q as o,i as e,t as V,p as i,F,_ as z,aq as B,C as D}from"./index-r0dFAfgr.js";import{C as R}from"./index-CcDafpIP.js";import{d as W,r as q}from"./chart-wy3NEK2T.js";import A from"./openDialog-zxp1lZ_x.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";const E={class:"onemap-panel-wrapper"},L={class:"table-box"},N={style:{width:"100%",height:"100%",display:"flex"}},T={style:{width:"100%",height:"100%"}},I=v({__name:"residentialArea",props:{view:{}},setup(M){const d=a(),c=a(),s=a([{label:"91个",value:"居民区总数"},{label:"0户",value:"用户总数"},{label:"98%",value:"抄表率"}]),l=a(),f=W(),m=r({tabs:{filters:[{type:"tabs",field:"type",tabs:[{label:"cs",value:"cs"}],onChange:()=>{}}]}}),p=r({dataList:[],pagination:{hide:!0},columns:[{minWidth:120,label:"小区名称",prop:"key1",sortable:!0},{minWidth:120,label:"用户数",prop:"key3"},{minWidth:120,label:"抄表类型",prop:"key3"}],handleRowClick:t=>{p.currentRow=t}}),u=r({group:[{fieldset:{type:"underline",desc:"小区规模占比图"},fields:[{type:"vchart",option:q(),style:{width:"100%",height:"150px"},itemContainerStyle:{marginBottom:0}}]},{fields:[{type:"input",field:"layer",append:"刷新"}]}],labelPosition:"top",gutter:12});C(()=>{window.addEventListener("resize",_)});function _(){var t;(t=l.value)==null||t.resize()}return(t,n)=>{const h=z,g=B,y=w("VChart");return k(),x("div",E,[o(e(R),{modelValue:e(s),"onUpdate:modelValue":n[0]||(n[0]=b=>V(s)?s.value=b:null)},null,8,["modelValue"]),o(h,{ref_key:"refForm",ref:d,config:e(u)},null,8,["config"]),i("div",L,[o(g,{config:e(p)},null,8,["config"])]),o(A,{ref_key:"refDetail",ref:c,config:e(m),onClose:()=>{}},{right:F(()=>[i("div",N,[n[1]||(n[1]=i("div",{style:{width:"50%",height:"100%"}},null,-1)),i("div",T,[o(y,{ref_key:"refChart2",ref:l,autoresize:"",theme:"dark",option:e(f)},null,8,["option"])])])]),_:1},8,["config"])])}}}),H=D(I,[["__scopeId","data-v-d46b2bfe"]]);export{H as default};
