package org.thingsboard.server.dao.model.sql.smartService.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("wx_news_category")
public class WxNewsCategory {
    // id
    private String id;

    // 栏目名称
    private String name;

    // 编号
    private Integer serialNo;

    // 客户id
    private String tenantId;

}
