<template>
  <div style="position: relative">
    <div v-if="compName === 'newpage'" class="preview-box">
      <component :is="components.get(compName)"></component>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{
  source?: string;
  type?: 'menu' | 'page' | 'iframe';
}>();
// 默认加载的组件名
const compName = ref('default');
// 需要加载的组件集合
const components = ref(new Map<string, any>());
components.value.set(
  'default',
  defineAsyncComponent(() => import('@/views/404.vue'))
);
const modules = import.meta.glob('@/views/**/*.vue');
watch(
  () => props.source,
  (val) => {
    if (props.type === 'menu') {
      compName.value = 'default';
      return;
    }
    const routerLink =
      modules[/* @vite-ignore */ `/src/views/${val}.vue`];
    if (routerLink) {
      components.value.set('newpage', defineAsyncComponent(routerLink as any));
      compName.value = 'newpage';
    } else {
      compName.value = 'default';
    }
  }
);
</script>
<style lang="scss" scoped>
.back-box {
  width: 1920px;
  height: 1080px;
  position: absolute;
  left: 0;
  top: 0;
  zoom: 0.5;
  z-index: 2000;
}
.preview-box {
  width: 1920px;
  height: 1080px;
  margin: 0;
  zoom: 0.5;
  z-index: 1000;
}
</style>
