<!-- gis事件热点图 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards
      v-model="cardsvalue"
      :span="24"
    ></Cards>
    <Form :config="FormConfig"></Form>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components'
import { ring } from '../../components/components/chart'

defineEmits(['highlightMark', 'addMarks'])
defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const cardsvalue = ref([{ label: '0 个', value: '隐藏点总数' }])

const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        type: 'underline',
        desc: '隐患事件类型占比'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          }
        }
      ]
    }
  ]
})
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
</style>
