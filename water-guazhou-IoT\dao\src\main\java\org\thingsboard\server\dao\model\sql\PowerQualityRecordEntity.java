package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = "power_quality_record")
public class PowerQualityRecordEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    private String title;

    private Long time;

    private String deviceId;

    private String deviceName;

    private String projectId;

    private String projectName;

    private String powerQualityDetail;

    private String gatewayId;

    private String name;

    public PowerQualityRecordEntity() {
    }

    public PowerQualityRecordEntity(String id, String title, Long time, String deviceId, String deviceName, String projectId, String projectName, String powerQualityDetail, String gatewayId, String name) {
        this.id = id;
        this.title = title;
        this.time = time;
        this.deviceId = deviceId;
        this.deviceName = deviceName;
        this.projectId = projectId;
        this.projectName = projectName;
        this.powerQualityDetail = powerQualityDetail;
        this.gatewayId = gatewayId;
        this.name = name;
    }

    public PowerQualityRecordEntity(String id, String title, Long time, String deviceId,
                                    String deviceName, String projectId, String projectName,
                                    String gatewayId, String name) {
        this.id = id;
        this.title = title;
        this.time = time;
        this.deviceId = deviceId;
        this.deviceName = deviceName;
        this.projectId = projectId;
        this.projectName = projectName;
        this.gatewayId = gatewayId;
        this.name = name;
    }
}
