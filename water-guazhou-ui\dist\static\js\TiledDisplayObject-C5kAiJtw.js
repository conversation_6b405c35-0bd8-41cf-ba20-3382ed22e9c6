import{bs as a,bt as u,bu as l}from"./MapView-DaoQedLH.js";import{r as g}from"./Container-BwXq1a-x.js";let m=class extends g{constructor(t,s,e,h,i,r,o=i,n=r){super(),this.triangleCountReportedInDebug=0,this.triangleCount=0,this.texture=null,this.key=new a(t),this.resolution=s,this.x=e,this.y=h,this.width=i,this.height=r,this.rangeX=o,this.rangeY=n}destroy(){this.texture&&(this.texture.dispose(),this.texture=null)}setTransform(t){const s=this.resolution/(t.resolution*t.pixelRatio),e=this.transforms.tileMat3,[h,i]=t.toScreenNoRotation([0,0],[this.x,this.y]),r=this.width/this.rangeX*s,o=this.height/this.rangeY*s;u(e,r,0,0,0,o,0,h,i,1),l(this.transforms.dvs,t.displayViewMat3,e)}};export{m as r};
