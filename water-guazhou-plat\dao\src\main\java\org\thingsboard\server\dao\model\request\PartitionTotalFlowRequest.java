package org.thingsboard.server.dao.model.request;

import lombok.Data;

import java.util.Set;

@Data
public class PartitionTotalFlowRequest {

    private int page;

    private int size;

    private String partitionId; // 分区id

    private Set<String> deviceIdList;

    private String type;  // 补录类型 1时 2日 3月

    private String interval; // 间隔 1min  5min 15min 30min 1h

    private Long start;

    private Long end;

    private String month;

    private String year;

    private String meterName; // 水表名称

    private String tenantId;

    private String queryType; // 查询类型

}
