package org.thingsboard.server.dao.waterSource.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.fileupload.ISysFileService;
import org.thingsboard.server.dao.model.sql.assay.Assay;
import org.thingsboard.server.dao.sql.waterSource.AssayMapper;
import org.thingsboard.server.dao.waterSource.AssayService;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AssayServiceImpl implements AssayService {

    @Autowired
    private AssayMapper assayMapper;

    @Autowired
    private ISysFileService fileService;

    @Override
    public PageData<Assay> getList(Map<String, Object> params, String tenantId) {
        try {
            Page<Assay> page = new Page<>(
                    Integer.parseInt(params.getOrDefault("page", "1").toString()),
                    Integer.parseInt(params.getOrDefault("pageSize", "10").toString())
            );

            params.put("tenantId", tenantId);
            params.put("testDate", params.getOrDefault("testDate", null));
            params.put("sortField", params.getOrDefault("sortProperty", "createTime"));
            params.put("sortOrder", params.getOrDefault("sortOrder", "DESC"));

            IPage<Assay> pageResult = assayMapper.getList(page, params);
            return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
        } catch (Exception e) {
            log.error("Failed to get assay list", e);
            throw new RuntimeException("获取化验记录列表失败", e);
        }
    }

    @Override
    @Transactional
    public Assay save(Assay entity) {
        try {
            Date now = new Date();
            if (StringUtils.isBlank(entity.getId())) {
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                assayMapper.insert(entity);
            } else {
                Assay existing = assayMapper.selectById(entity.getId());
                if (existing == null) {
                    throw new RuntimeException("化验记录不存在");
                }
                entity.setUpdateTime(now);
                entity.setCreateTime(existing.getCreateTime());
                entity.setCreator(existing.getCreator());
                assayMapper.updateById(entity);
            }
            return entity;
        } catch (Exception e) {
            log.error("Failed to save assay", e);
            throw new RuntimeException("保存化验记录失败", e);
        }
    }

    @Override
    @Transactional
    public void delete(List<String> idList) {
        try {
            // First get all assays to check for report files
            List<Assay> assays = assayMapper.selectBatchIds(idList);
            for (Assay assay : assays) {
                if (StringUtils.isNotBlank(assay.getReportName())) {
                    fileService.deleteFile(assay.getReportName());
                }
            }
            // Then delete all records in batch
            assayMapper.deleteBatchIds(idList);
        } catch (Exception e) {
            log.error("删除化验记录失败", e);
        }
    }

    @Override
    public Assay getById(String id) {
        try {
            return assayMapper.selectById(id);
        } catch (Exception e) {
            log.error("获取化验记录失败", e);
            throw new RuntimeException("获取化验记录失败", e);
        }
    }

    @Override
    @Transactional
    public Assay uploadReport(Assay assay) {
        try {
            Assay result = assayMapper.selectById(assay.getId());
            if (result == null) {
                throw new RuntimeException("化验记录不存在");
            }

            if (StringUtils.isNotBlank(result.getReportFile())) {
                int i = result.getReportFile().lastIndexOf("/");
                String fileName = result.getReportFile().substring(i + 1, result.getReportFile().length());
                fileService.deleteFile(fileName);
            }
            assay.setUpdateTime(new Date());
            assayMapper.updateById(assay);
            return assay;
        } catch (Exception e) {
            log.error("Failed to upload report", e);
            throw new RuntimeException("上传报告失败", e);
        }
    }

}