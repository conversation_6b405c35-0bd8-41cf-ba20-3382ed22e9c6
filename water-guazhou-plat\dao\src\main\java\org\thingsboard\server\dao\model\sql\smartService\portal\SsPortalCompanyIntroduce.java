package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_company_introduce")
public class SsPortalCompanyIntroduce {
    // id
    private String id;

    // 简介
    private String introduce;

    // 描述
    private String detail;

    // 客户id
    private String tenantId;

}
