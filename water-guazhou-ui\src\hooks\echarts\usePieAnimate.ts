export const usePieAnimate = () => {
  let _thisChart: any
  let _dataLength = 0
  let _charPie3currentIndex = 0
  let _timer = -1
  let _interval = 5000
  let isSet = true
  const _bindMouveEvent = (seriesIndex = 0) => {
    _thisChart?.on('mouseover', param => {
      isSet = false
      clearInterval(_timer)
      // 取消之前高亮的图形
      _thisChart?.dispatchAction({
        type: 'downplay',
        seriesIndex,
        dataIndex: _charPie3currentIndex
      })
      // 高亮当前图形
      _thisChart?.dispatchAction({
        type: 'highlight',
        seriesIndex,
        dataIndex: param.dataIndex
      })
      // 显示 tooltip
      _thisChart?.dispatchAction({
        type: 'showTip',
        seriesIndex,
        dataIndex: param.dataIndex
      })
    })
    _thisChart?.on('mouseout', param => {
      _thisChart?.dispatchAction({
        type: 'downplay',
        seriesIndex,
        dataIndex: param.dataIndex
      })
      if (!isSet) {
        _timer = setInterval(() => _chartHover(seriesIndex), _interval)
        isSet = true
      }
    })
  }
  const _chartHover = (seriesIndex = 0) => {
    _thisChart?.dispatchAction({
      type: 'downplay',
      seriesIndex,
      dataIndex: _charPie3currentIndex
    })
    _charPie3currentIndex = (_charPie3currentIndex + 1) % _dataLength
    // 高亮当前图形
    _thisChart?.dispatchAction({
      type: 'highlight',
      seriesIndex,
      dataIndex: _charPie3currentIndex
    })
    // 显示 tooltip
    _thisChart?.dispatchAction({
      type: 'showTip',
      seriesIndex,
      dataIndex: _charPie3currentIndex
    })
  }
  const _start = (seriesIndex?: number) => {
    _timer = setInterval(() => _chartHover(seriesIndex), _interval)
    _bindMouveEvent(seriesIndex)
  }
  const end = () => {
    clearInterval(_timer)
  }
  const init = (refChartIns: any, dataLength = 0, interval = 5000, seriesIndex?: number) => {
    _dataLength = dataLength
    _thisChart = refChartIns
    _interval = interval
    _start(seriesIndex)
  }
  onBeforeUnmount(() => {
    end()
  })
  return {
    init
  }
}
