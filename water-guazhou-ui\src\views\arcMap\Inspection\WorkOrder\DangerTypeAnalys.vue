<template>
  <div class="wrapper">
    <SLCard
      class="card"
      title=" "
      overlay
    >
      <template #title>
        <Search
          ref="refSearch"
          :config="SearchConfig"
        ></Search>
      </template>
      <div class="content-wrapper">
        <div class="left">
          <FormTable :config="TableConfig"></FormTable>
        </div>
        <div class="right">
          <div class="right-top">
            <VChart
              ref="refChart"
              :option="state.barOpion"
            ></VChart>
          </div>
          <div class="right-bottom">
            <VChart
              ref="refChart1"
              :option="state.pieOption"
            ></VChart>
          </div>
        </div>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, shallowRef, onMounted, onBeforeUnmount } from 'vue'
import { Search as SearchIcon } from '@element-plus/icons-vue'
import moment from 'moment'
import { reduce } from 'lodash-es'
import { ISearchIns } from '@/components/type'
import { IECharts } from '@/plugins/echart'
// import { GetWorkOrderCountStatistic } from '@/api/workorder'
import { BarOption, PieChartOption } from '@/views/workorder/statistics/echart'
import { WorkOrderTrend } from '@/api/patrol'

const refSearch = ref<ISearchIns>()
const refChart = ref<IECharts>()
const refChart1 = ref<IECharts>()
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      label: '时间范围',
      options: [
        { label: '日', value: 'date' },
        { label: '月', value: 'month' },
        { label: '年', value: 'year' }
      ],
      field: 'type',
      onChange: () => refreshData()
    },
    {
      clearable: false,
      handleHidden: (params, query, config) => {
        config.hidden = params.type !== 'date'
      },
      type: 'date',
      label: '',
      field: 'date'
    },
    {
      clearable: false,
      handleHidden: (params, query, config) => {
        config.hidden = params.type !== 'month'
      },
      type: 'month',
      label: '',
      field: 'month'
    },
    {
      clearable: false,
      handleHidden: (params, query, config) => {
        config.hidden = params.type !== 'year'
      },
      type: 'year',
      label: '',
      field: 'year'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        }
      ]
    }
  ],
  defaultParams: {
    type: 'year',
    month: moment().format('YYYY-MM'),
    year: moment().format('YYYY'),
    date: moment().format('YYYY-MM-DD')
  }
})
const TableConfig = reactive<ITable>({
  indexVisible: true,
  columns: [
    { label: '事件类型', prop: 'key' },
    { label: '事件数量', prop: 'value' },
    { label: '比例（%）', prop: 'percentage' }
  ],
  dataList: [],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
const state = reactive<{
  barOpion: any
  pieOption: any
}>({
  barOpion: null,
  pieOption: null
})
const refreshData = async () => {
  const query = refSearch.value?.queryParams || {}

  const type = query?.type || 'date'
  let date: moment.Moment
  switch (type) {
    case 'month':
      date = moment(query[type], 'YYYY-MM')
      break
    case 'year':
      date = moment(query[type], 'YYYY')
      break
    default:
      date = moment(query[type], 'YYYY-MM-DD')
      break
  }
  const res = await WorkOrderTrend({
    fromTime: date
      .startOf(type === 'year' ? 'y' : type === 'month' ? 'M' : 'D')
      .valueOf(),
    toTime: date
      .endOf(type === 'year' ? 'y' : type === 'month' ? 'M' : 'D')
      .valueOf()
  })
  const data = res.data?.data || {}
  state.barOpion = BarOption(data.data || [])
  state.pieOption = PieChartOption(data.data || [], '事件类型分析')
  const total = reduce(
    data.data || [],
    (prev, cur) => {
      return prev + cur.value
    },
    0
  )
  TableConfig.dataList = data.data?.map(item => {
    item.percentage = (((item.value || 0) / (total || 1)) * 100).toFixed(2)
    return item
  }) || []
}
const resizeChart = () => {
  refChart.value?.resize()
  refChart1.value?.resize()
}
onMounted(() => {
  refreshData()
  window.addEventListener('resize', resizeChart)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeChart)
})
</script>
<style lang="scss" scoped>
.card {
  height: 100%;
}

.content-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
}

.left {
  width: 50%;
  height: 100%;
}

.right {
  width: 50%;
  height: 100%;

  .right-top,
  .right-bottom {
    height: 50%;
  }
}
</style>
