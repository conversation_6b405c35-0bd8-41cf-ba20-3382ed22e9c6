package org.thingsboard.server.controller.smartPipe;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeWaterFactoryMonitorPoint;
import org.thingsboard.server.dao.smartPipe.PipeWaterFactoryMonitorPointService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 智慧管网-水厂监测点
 */
@RestController
@RequestMapping("api/spp/waterFactory/monitorPoint")
public class PipeWaterFactoryMonitorPointController extends BaseController {

    @Autowired
    private PipeWaterFactoryMonitorPointService pipeWaterFactoryMonitorPointService;

    @PostMapping
    public IstarResponse save(@RequestBody PipeWaterFactoryMonitorPoint pipeWaterFactoryMonitorPoint) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        pipeWaterFactoryMonitorPoint.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        pipeWaterFactoryMonitorPoint.setTenantId(tenantId);

        String check = pipeWaterFactoryMonitorPointService.check(pipeWaterFactoryMonitorPoint);
        if (StringUtils.isNotBlank(check)) {
            return IstarResponse.error(check);
        }

        return IstarResponse.ok(pipeWaterFactoryMonitorPointService.save(pipeWaterFactoryMonitorPoint));
    }

    @PostMapping("changeDirection/{id}")
    public IstarResponse changeDirection(@PathVariable String id) {
        String result = pipeWaterFactoryMonitorPointService.changeDirection(id);
        if (StringUtils.isNotBlank(result)) {
            return IstarResponse.error(result);
        }
        return IstarResponse.ok("变更成功");
    }

    @PostMapping("changeOrderNum")
    public IstarResponse changeOrderNum(@RequestBody JSONObject param) {
        String id = param.getString("id");
        Integer orderNum = param.getInteger("orderNum");
        String result = pipeWaterFactoryMonitorPointService.changeOrderNum(id, orderNum);
        if (StringUtils.isNotBlank(result)) {
            return IstarResponse.error(result);
        }
        return IstarResponse.ok("操作成功");
    }

    @GetMapping("list")
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeWaterFactoryMonitorPointService.getList(name, tenantId));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> idList) {
        pipeWaterFactoryMonitorPointService.delete(idList);

        return IstarResponse.ok("移除成功");
    }

    @GetMapping("reportHeader")
    public IstarResponse getReportHeader() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeWaterFactoryMonitorPointService.getReportHeader(tenantId));
    }

    @GetMapping("report")
    public IstarResponse getReport(String type, String date, String start, String end) throws ThingsboardException {
        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("请选择类型");
        }
        if (StringUtils.isBlank(date) && StringUtils.isBlank(start)) {
            return IstarResponse.error("请选择时间");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeWaterFactoryMonitorPointService.getReport(type, date, start, end, tenantId));
    }

    @GetMapping("reportExport")
    public IstarResponse getReportExport(String type, String date, String start, String end, HttpServletResponse response) throws ThingsboardException {
        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("请选择类型");
        }
        if (StringUtils.isBlank(date) && StringUtils.isBlank(start)) {
            return IstarResponse.error("请选择时间");
        }

        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        List<JSONObject> dataList = pipeWaterFactoryMonitorPointService.getReport(type, date, start, end, tenantId);

        List<JSONObject> headerList = (List<JSONObject>) this.getReportHeader().getData();
        ExcelUtil.getReportExport(headerList, dataList, response, type);

        return IstarResponse.ok();

    }

}
