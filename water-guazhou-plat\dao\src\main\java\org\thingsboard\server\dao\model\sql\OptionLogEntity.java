/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.id.OptionLogId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.optionLog.OptionLog;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.OPTION_LOG_NAME)
public class OptionLogEntity extends BaseSqlEntity<OptionLog> {

    @Column(name = ModelConstants.OPTION_LOG_USER_ID)
    private String userId;

    @Column(name = ModelConstants.OPTION_LOG_TENANT_ID)
    private String tenantId;

    @Column(name = ModelConstants.OPTION_LOG_FIRST_NAME)
    private String firstName;

    @Column(name = ModelConstants.OPTION_LOG_AUTHORITY)
    private String authority;

    @Column(name = ModelConstants.OPTION_LOG_OPTIONS)
    private String options;

    @Column(name = ModelConstants.OPTION_LOG_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.OPTION_LOG_ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.OPTION_LOG_TYPE)
    private String type;

    @Column(name = ModelConstants.OPTION_LOG_INFO)
    private String info;

    @Column(name = ModelConstants.OPTION_LOG_STATUS)
    private String status;

    @Column(name = ModelConstants.OPTION_LOG_EXAMINE_ID)
    private String examineId;

    @Column(name = ModelConstants.OPTION_LOG_EXAMINE_NAME)
    private String examineName;

    @Column(name = ModelConstants.OPTION_LOG_EXAMINE_TENANT_ID)
    private String examineTenantId;

    @Column(name = ModelConstants.OPTION_LOG_EXAMINE_TIME)
    private Long examineTime;
    public OptionLogEntity() {
        super();
    }

    public OptionLogEntity(OptionLog log) {
        if (log.getId() != null) {
            this.setId(log.getId().getId());
        }
        if (log.getUserId() != null) {
            this.userId = toString(log.getUserId().getId());
        }
        if (log.getTenantId() != null) {
            this.tenantId = toString(log.getTenantId().getId());
        }
        this.firstName = log.getFirstName();
        this.authority = log.getAuthority();
        this.options = log.getOptions();
        this.createTime = log.getCreateTime();
        if (log.getAdditionalInfo() != null) {
            this.additionalInfo = JacksonUtil.toString(log.getAdditionalInfo());
        }
        this.type = log.getType();
        this.info = log.getInfo();
        this.status = log.getStatus();
        this.examineId = log.getExamineId();
        this.examineName = log.getExamineName();
        this.examineTenantId = log.getExamineTenantId();
        this.examineTime = log.getExamineTime();

    }

    @Override
    public OptionLog toData() {
        OptionLog optionLog = new OptionLog(new OptionLogId(getId()));
        optionLog.setUserId(new UserId(toUUID(userId)));
        optionLog.setTenantId(new TenantId(toUUID(tenantId)));
        optionLog.setFirstName(firstName);
        optionLog.setAuthority(authority);
        optionLog.setOptions(options);
        optionLog.setCreateTime(createTime);
        optionLog.setAdditionalInfo(JacksonUtil.toJsonNode(additionalInfo));
        optionLog.setInfo(info);
        optionLog.setType(type);
        optionLog.setStatus(status);
        optionLog.setExamineId(examineId);
        optionLog.setExamineName(examineName);
        optionLog.setExamineTenantId(tenantId);
        optionLog.setExamineTime(examineTime);
        try {
            optionLog.setTypeDescription(DataConstants.OPERATING_TYPE.valueOf(options).getDescription());
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
        return optionLog;
    }
}
