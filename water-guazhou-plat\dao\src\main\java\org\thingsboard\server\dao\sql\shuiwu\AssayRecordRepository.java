package org.thingsboard.server.dao.sql.shuiwu;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.AssayRecord;

import java.util.Date;
import java.util.List;

public interface AssayRecordRepository extends JpaRepository<AssayRecord, String> {
    List<AssayRecord> findByStationIdOrderByTimeDesc(String stationId);

    List<AssayRecord> findByTenantIdOrderByTimeDesc(String tenantId);

    List<AssayRecord> findByStationIdAndTimeBetween(String stationId, Date startTime, Date endTime);
}
