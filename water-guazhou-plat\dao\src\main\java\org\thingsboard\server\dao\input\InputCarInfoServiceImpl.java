package org.thingsboard.server.dao.input;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.InputCarInfoListRequest;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;
import org.thingsboard.server.dao.model.sql.input.InputCarInfo;
import org.thingsboard.server.dao.sql.input.InputCarInfoMapper;
import org.thingsboard.server.dao.sql.input.InputCarInfoRepository;
import org.thingsboard.server.dao.user.UserService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InputCarInfoServiceImpl implements InputCarInfoService {

    @Autowired
    private InputCarInfoRepository inputCarInfoRepository;

    @Autowired
    private InputCarInfoMapper inputCarInfoMapper;

    @Autowired
    private UserService userService;

    @Override
    public void save(InputCarInfo inputCarInfo) {
        inputCarInfoRepository.save(inputCarInfo);
    }

    @Override
    public PageData<InputCarInfo> findList(InputCarInfoListRequest request) {
        Page<InputCarInfo> pageRequest = new Page<>(request.getPage(), request.getSize());
        IPage<InputCarInfo> pageResult = inputCarInfoMapper.findList(pageRequest, request);

        List<User> userList = userService.findUserByTenant(new TenantId(UUIDConverter.fromString(request.getTenantId())));
        Map<String, User> userMap = userList.stream().collect(Collectors.toMap(user -> UUIDConverter.fromTimeUUID(user.getUuidId()), user -> user));
        List<InputCarInfo> records = pageRequest.getRecords();
        for (InputCarInfo record : records) {
            User user = userMap.get(record.getCreateUser());
            if (user != null) {
                record.setCreateUserName(user.getFirstName());
            }
        }

        return new PageData<>(pageResult.getTotal(), records);
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            inputCarInfoRepository.delete(id);
        }
    }
}
