package org.thingsboard.server.dao.district;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartManagement.district.CircuitDistrictPoint;
import org.thingsboard.server.dao.sql.smartManagement.district.CircuitDistrictPointMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictPointPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictPointSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportSaveRequest;

import java.util.List;

@Service
public class CircuitDistrictPointServiceImpl implements CircuitDistrictPointService {
    @Autowired
    private CircuitDistrictPointMapper mapper;

    @Override
    public IPage<CircuitDistrictPoint> findAllConditional(CircuitDistrictPointPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public CircuitDistrictPoint save(CircuitDistrictPointSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    public boolean update(CircuitDistrictPoint entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<CircuitTaskReportSaveRequest> selectReportTemplate(List<String> idList) {
        return mapper.selectReportTemplate(idList);
    }

    @Override
    public CircuitDistrictPoint findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public boolean hasPoint(String districtAreaId) {
        return mapper.hasPoint(districtAreaId);
    }

}
