<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.project.SoGeneralTypeMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           order_num,
                           tenant_id<!--@sql from so_general_type -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="order_num" property="orderNum"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <update id="update">
        update so_general_type
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_general_type
        <set>
            name      = #{name},
            order_num = #{orderNum},
        </set>
        where id = #{id}
    </update>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_general_type
        <where>
            <if test="name != null">
                and name like '%' || #{name} || '%'
            </if>
            <if test="scope != null">
                and scope like '%' || #{scope} || '%'
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by order_num
    </select>
</mapper>