package org.thingsboard.server.dao.sql.smartService.wechat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxPaymentConfig;
import org.thingsboard.server.dao.util.imodel.query.smartService.wechat.WxPaymentConfigPageRequest;

import java.util.List;

@Mapper
public interface WxPaymentConfigMapper extends BaseMapper<WxPaymentConfig> {
    IPage<WxPaymentConfig> findByPage(WxPaymentConfigPageRequest request);

    boolean updateFully(WxPaymentConfig entity);

    String getIdByTenantId(@Param("tenantId") String tenantId);

    WxPaymentConfig selectByTenantId(String tenantId);


}
