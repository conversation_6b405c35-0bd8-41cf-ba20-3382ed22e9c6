import { request } from '@/plugins/axios'

/**
 * 查询DMA小流指标配置列表
 * @param params
 * @returns
 */
export const GetDMAMinFlowConfig = (partitionId: any) => {
  return request({
    url: `/api/spp/minFlowConfig/listByPartitionId/${partitionId}`,
    method: 'get'
  })
}
/**
 * 保存DMA小流指标配置
 * @param params
 * @returns
 */
export const PostDMAMinFlowConfig = (params: {
  partitionId: string
  nightFlowMin: number
  nightFlowMax: number
  nightValueMin: number
  nightValueMax: number
  unitPipeNightFlowMin: number
  unitPipeNightFlowMax: number
  mnfDivDayAvgHourFlowMin: number
  mnfDivDayAvgHourFlowMax: number
  collectRate: number
  stockType: number
  incrTime: number
  incrBase: number
  incrWarn: number
  incrError: number
  incrType: number
}) => {
  return request({
    url: '/api/spp/minFlowConfig',
    method: 'post',
    data: params
  })
}
