package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 *
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_PARTITION_VALVE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PartitionValve {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_PARTITION_VALVE_PARTITION_ID)
    private String partitionId;

    @TableField(ModelConstants.PIPE_PARTITION_VALVE_TYPE)
    private String type;

    @TableField(exist = false)
    private String typeName;

    @TableField(ModelConstants.PIPE_PARTITION_VALVE_CODE)
    private String code;

    @TableField(ModelConstants.PIPE_PARTITION_VALVE_CALIBER)
    private String caliber;

    @TableField(ModelConstants.PIPE_PARTITION_VALVE_VALVE_TYPE)
    private String valveType;

    @TableField(ModelConstants.PIPE_PARTITION_VALVE_ADDRESS)
    private String address;

    @TableField(ModelConstants.PIPE_PARTITION_VALVE_RUN_STATUS)
    private String runStatus;

    @TableField(ModelConstants.PIPE_PARTITION_VALVE_REMARK)
    private String remark;

    @TableField(ModelConstants.PIPE_PARTITION_VALVE_IMG)
    private String img;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
