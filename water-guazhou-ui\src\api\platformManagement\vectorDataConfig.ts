import { request } from '@/plugins/axios'

// 获取矢量数据配置列表
export function getVectorDataConfigList(params: any) {
  return request({
    url: '/api/base/vector/configuration/list',
    method: 'get',
    params
  })
}

// 获取矢量数据配置详情
export function getVectorDataConfigDetail(id: string) {
  return request({
    url: '/api/base/vector/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增矢量数据配置
export function addVectorDataConfig(data: any) {
  return request({
    url: '/api/base/vector/configuration/add',
    method: 'post',
    data
  })
}

// 修改矢量数据配置
export function editVectorDataConfig(data: any) {
  return request({
    url: '/api/base/vector/configuration/edit',
    method: 'post',
    data
  })
}

// 删除矢量数据配置
export function deleteVectorDataConfig(ids: string[]) {
  return request({
    url: '/api/base/vector/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
} 