package org.thingsboard.server.dao.model.sql.smartService.call;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 通话记录
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-24
 */
@TableName("tb_service_call_log")
@Data
public class CallLog {

    @TableId
    private String id;

    private String phone;

    private String areaName;

    @TableField(exist = false)
    private String serialNo;

    private String direction;

    private String status;

    private String extension;

    private Date callTime;

    private Date listenTime;

    private Date endTime;

    @TableField(exist = false)
    private String sourceName;

    @TableField(exist = false)
    private String area;

    @TableField(exist = false)
    private String workOrderType;

    @TableField(exist = false)
    private String topicName;

    @TableField(exist = false)
    private String seatsName;

    @TableField(exist = false)
    private Integer callDuration;

    private String evaluate;

    private String fileUrl;

    private String fileName;

    private String userId;

}
