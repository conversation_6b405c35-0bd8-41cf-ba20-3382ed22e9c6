<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.pumpHouse.PumpMaintenanceStandardMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           type,
                           name,
                           input_user_name,
                           remark,
                           file,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_pump_maintenance_standard -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpMaintenanceStandard">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="input_user_name" property="inputUserName"/>
        <result column="remark" property="remark"/>
        <result column="file" property="file"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_pump_maintenance_standard
        <where>
            <if test="type != null and type != ''">
                type like '%' || #{type} || '%'
            </if>
            <if test="name != null and name != ''">
                and name like '%' || #{name} || '%'
            </if>
            <if test="inputUserName != null and inputUserName != ''">
                and input_user_name like '%' || #{inputUserName} || '%'
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sp_pump_maintenance_standard
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="inputUserName != null">
                input_user_name = #{inputUserName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="file != null">
                file = #{file},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>