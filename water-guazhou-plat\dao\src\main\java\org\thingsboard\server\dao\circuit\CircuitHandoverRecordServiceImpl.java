package org.thingsboard.server.dao.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitHandoverRecord;
import org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitHandoverRecordMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitHandoverRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitHandoverRecordSaveRequest;

@Service
public class CircuitHandoverRecordServiceImpl implements CircuitHandoverRecordService {
    @Autowired
    private CircuitHandoverRecordMapper mapper;

    @Override
    public IPage<CircuitHandoverRecord> findAllConditional(CircuitHandoverRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public CircuitHandoverRecord save(CircuitHandoverRecordSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(CircuitHandoverRecord entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
