package org.thingsboard.server.dao.smartService.duty;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartService.duty.DutyShift;
import org.thingsboard.server.dao.sql.smartService.duty.DutyShiftMapper;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class DutyShiftServiceImpl implements DutyShiftService {
    @Autowired
    private DutyShiftMapper dutyShiftMapper;

    @Override
    public List<DutyShift> getList(String tenantId) {

        QueryWrapper<DutyShift> query = new QueryWrapper<>();
        query.orderByDesc("create_time");

        List<DutyShift> dutyShifts = dutyShiftMapper.selectList(query);

        return dutyShifts;
    }

    @Override
    public DutyShift save(DutyShift dutyShift) {
        dutyShift.setUpdateTime(new Date());
        if (StringUtils.isBlank(dutyShift.getId())) {
            dutyShift.setCreateTime(new Date());
            dutyShiftMapper.insert(dutyShift);
        } else {
            dutyShiftMapper.updateById(dutyShift);
        }

        return dutyShift;
    }

    @Override
    public int delete(List<String> ids) {
        return dutyShiftMapper.deleteBatchIds(ids);
    }
}
