package org.thingsboard.server.dao.report;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.report.ReportType;
import org.thingsboard.server.dao.sql.report.ReportTypeMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class ReportTypeServiceImpl implements ReportTypeService {

    @Autowired
    private ReportTypeMapper reportTypeMapper;

    @Override
    public ReportType save(ReportType reportType) {
        if (StringUtils.isBlank(reportType.getId())) {
            reportType.setCreateTime(new Date());
            reportTypeMapper.insert(reportType);
        } else {
            reportTypeMapper.updateById(reportType);
        }
        return reportType;
    }


    @Override
    public List<ReportType> getList(String tenantId) {
        QueryWrapper<ReportType> reportTypeQueryWrapper = new QueryWrapper<>();
        reportTypeQueryWrapper.eq("tenant_id", tenantId);
        reportTypeQueryWrapper.orderByAsc("order_num").orderByDesc("create_time");

        List<ReportType> reportTypes = reportTypeMapper.selectList(reportTypeQueryWrapper);
        List<ReportType> result = reportTypes.stream().filter(a -> StringUtils.isBlank(a.getPid())).collect(Collectors.toList());
        for (ReportType reportType : result) {
            this.buildTree(reportType, reportTypes);
        }

        return result;
    }

    private void buildTree(ReportType reportType, List<ReportType> reportTypes) {
        for (ReportType subReportType : reportTypes) {
            if (reportType.getId().equals(subReportType.getPid())) {
                this.buildTree(subReportType, reportTypes);
                subReportType.setParentName(reportType.getName());
                reportType.getChildren().add(subReportType);
            }
        }
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        // 是否有下级节点
        QueryWrapper<ReportType> reportTypeQueryWrapper = new QueryWrapper<>();
        reportTypeQueryWrapper.in("pid", ids);
        List<ReportType> reportTypes = reportTypeMapper.selectList(reportTypeQueryWrapper);
        if (reportTypes.size() > 0) {
            return IstarResponse.error("有子类型未删除");
        }
        reportTypeMapper.deleteBatchIds(ids);

        return IstarResponse.ok("删除成功");
    }

}
