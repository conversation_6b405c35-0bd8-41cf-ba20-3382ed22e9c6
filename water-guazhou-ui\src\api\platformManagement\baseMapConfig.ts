import { request } from '@/plugins/axios'

// 获取底图配置列表
export function getBaseMapConfigList(params: any) {
  return request({
    url: '/api/base/map/configuration/list',
    method: 'get',
    params
  })
}

// 获取底图配置详情
export function getBaseMapConfigDetail(id: string) {
  return request({
    url: '/api/base/map/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增底图配置
export function addBaseMapConfig(data: any) {
  return request({
    url: '/api/base/map/configuration/add',
    method: 'post',
    data
  })
}

// 修改底图配置
export function editBaseMapConfig(data: any) {
  return request({
    url: '/api/base/map/configuration/edit',
    method: 'post',
    data
  })
}

// 删除底图配置
export function deleteBaseMapConfig(ids: string[]) {
  return request({
    url: '/api/base/map/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
} 