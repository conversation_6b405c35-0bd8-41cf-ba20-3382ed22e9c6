package org.thingsboard.server.dao.model.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("sp_circuit_handover_record")
public class CircuitHandoverRecord {
    // id
    @TableId
    private String id;

    // 值班地点
    private String dutyLocation;

    // 运维情况
    private String omRemark;

    // 泵房状态。状态值：正常、异常、危险
    private String pumpRoomStatus;

    // 日常记录
    private String record;

    // 记录时间
    private Date recordTime;

    // 交接班时间
    private Date shiftTime;

    // 交班人
    private String handoverPerson;

    // 接班人
    private String takeoverPerson;

    // 交接班说明
    private String handoverRemark;

    // 租户ID
    private String tenantId;

}
