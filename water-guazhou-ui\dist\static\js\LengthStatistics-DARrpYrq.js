import{_ as Z}from"./Panel-DyoxrWMd.js";import{d as $,c as O,r as q,W as B,b as v,bB as ee,o as te,Q as re,ay as ae,g as ie,n as se,q as C,i as w,F as oe,p as le,X as ne,a1 as pe,_ as ce,C as de}from"./index-r0dFAfgr.js";import{GetFieldConfig as V,GetFieldUniqueValue as me}from"./fieldconfig-Bk3o1wi7.js";import{P as ue}from"./pipe-nogVzCHG.js";import{_ as ye}from"./Search-NSrhrIa_.js";import{a as fe,m as ge,n as he}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as we,a as ve}from"./LayerHelper-Cn-iiqxI.js";import{e as xe,i as be}from"./QueryHelper-ILO3qZqg.js";import{E as Fe}from"./StatisticsHelper-D-s_6AyQ.js";import{s as T,i as Oe}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import Ce from"./PipeDetail-CTBPYFJW.js";import"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./DateFormatter-Bm9a68Ax.js";import"./config-fy91bijz.js";const _e={class:"chart-box"},Le=$({__name:"LengthStatistics",props:{view:{},telport:{}},setup(M){const m=M,n=O(),k=O(),I=O(),_=O(),L=O(),r=q({curNode:void 0,curOperate:"",pipeLayerOption:[],tabs:[],staticFields:["SUBTYPE ","DISTRICT","MATERIAL","DIAMETER","JUNCTION","OCCUPYSURF","GASCHAR","ANTIMATERIAL","DATATYPE","VENDER","OWNERDEPT","MANAGEDEPT"],layerFields:{},chartOption:null,alllayersbardata:[],chartFields:[]}),o={chartTabs:[],staticField:Fe.ShapeLen},x=q({group:[{fieldset:{desc:"统计字段"},fields:[{type:"tree",options:[],style:{width:"50%",height:"200px",padding:"8px"},field:"layer",nodeKey:"label",showCheckbox:!0,className:"sql-list-wrapper",handleCheckChange:async(e,a)=>{r.curLayer=e,await D(e.label,a)},extraFormItem:[{type:"tree",style:{width:"50%",height:"200px",padding:"8px"},showCheckbox:!0,multiple:!0,className:"sql-list-wrapper",field:"staticFields",options:[],handleCheckChange:async(e,a)=>{if(!r.curLayer)return;const i=r.chartFields.find(t=>{var s;return t.layerName===((s=r.curLayer)==null?void 0:s.label)});if(i){const t=i.fields.findIndex(s=>s.value===e.value);t!==-1&&i.fields.splice(t,1),a&&i.fields.push(e)}}}]}]},{id:"field-construct",fieldset:{desc:"过滤条件（可选）"},fields:[{type:"select",label:"图层名称",field:"layer1",options:[]},{type:"list",label:"图层字段",data:[],className:"sql-list-wrapper",setData:async(e,a)=>{var t,s;if(!a.layer1)return;const i=await V(a.layer1);e.data=(s=(t=i.data)==null?void 0:t.result)==null?void 0:s.rows},setDataBy:"layer1",displayField:"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:e=>{r.curNode=e,f(e.name)}},{type:"btn-group",size:"small",style:{width:"40%",height:"144px",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{f("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{f("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{f(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{f("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{f("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{f("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{f("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{f("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:e=>{f("'"+e+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>r.curOperate==="uniqueing"?"正在获取唯一值":"获取唯一值",loading:()=>r.curOperate==="uniqueing",disabled:()=>r.curOperate==="detailing",styles:{width:"100%",borderRadius:"0"},click:()=>z()}]}]}]},{type:"textarea",label:"属性条件",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",btns:[{perm:!0,text:"清除",size:"small",type:"danger",click:()=>R()}]},{type:"btn-group",label:"几何条件：",size:"small",btns:[{perm:!0,text:"绘制范围",type:"success",click:()=>Q()},{perm:!0,text:"清空范围",type:"danger",click:()=>K()}]}]},{fields:[{type:"btn-group",itemContainerStyle:{marginBottom:"5px"},btns:[{perm:!0,text:()=>r.curOperate==="statistising"?"正在统计":"统计",loading:()=>r.curOperate==="statistising",disabled:()=>["statistising","detailing","drawing"].indexOf(r.curOperate)!==-1,type:"warning",click:()=>Y(),styles:{width:"100%"}}]}]},{fields:[{type:"btn-group",btns:[{perm:!0,text:()=>r.curOperate==="detailing"?"正在查询":"查看详情",loading:()=>r.curOperate==="detailing",disabled:()=>["statistising","detailing","drawing"].indexOf(r.curOperate)!==-1,click:()=>{var e;r.curOperate="detailing",(e=L.value)==null||e.openDialog()},styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12}),A=q({filters:[{type:"tabs",field:"type",itemContainerStyle:{width:"100%"},tabs:[],onChange:()=>P()}]}),U=async()=>{var l,p,u;if(!m.view)return;const e=ve(m.view),i=((p=(l=(await ne(e)).data)==null?void 0:l.result)==null?void 0:p.rows)||[];r.pipeLayerOption=[],i.map(d=>{var g;(d.geometrytype==="esriGeometryPolyline"||d.layername.indexOf("立管")>-1)&&((g=r.pipeLayerOption)==null||g.push({label:d.layername,value:d.layername,id:d.layerid,data:d}))});const t=x.group[0].fields[0];t&&(t.options=r.pipeLayerOption),r.curLayer=t.options[0];const s=x.group[1].fields[0];s&&(s.options=r.pipeLayerOption),(u=n.value)!=null&&u.dataForm&&(n.value.dataForm.layer=r.pipeLayerOption.map(d=>d.value)),await D(r.pipeLayerOption[0].label,!0)},J=async e=>{var t,s;if(r.layerFields[e])return r.layerFields[e];const a=await V(e),i=pe(((s=(t=a.data)==null?void 0:t.result)==null?void 0:s.rows)||[],{id:"name",value:"name",label:"alias"});return r.layerFields[e]=i,i},D=async(e,a)=>{var l;const i=x.group[0].fields[0].extraFormItem,t=i&&i[0],s=a?await J(e):[];t&&(t.options=s.filter(p=>r.staticFields.indexOf(p.value)!==-1)),r.chartFields=[],a?r.chartFields.unshift({layerName:e,fields:[...(t==null?void 0:t.options)||[]]}):r.chartFields=r.chartFields.filter(p=>p.layerName!==e),n.value&&(n.value.dataForm.staticFields=(l=t==null?void 0:t.options)==null?void 0:l.map(p=>p.value))},z=async()=>{var e,a;if(r.curNode){r.curOperate="uniqueing";try{const i=(e=r.pipeLayerOption.find(p=>{var u;return p.label===((u=n.value)==null?void 0:u.dataForm.layer1)}))==null?void 0:e.id,t=await me({usertoken:B().gToken,layerid:i,f:"pjson",field_name:r.curNode.name}),s=(a=x.group.find(p=>p.id==="field-construct"))==null?void 0:a.fields[2].extraFormItem,l=s&&s[0];l&&(l.data=t.data.result.rows)}catch{v.error("获取唯一值失败")}r.curOperate=""}},f=e=>{var i;if(!n.value)return;(i=n.value)!=null&&i.dataForm||(n.value.dataForm={});const a=n.value.dataForm.sql||" ";n.value.dataForm.sql=a+e+" "},Q=()=>{var e,a;m.view&&(R(),(e=L.value)==null||e.closeDialog(),(a=I.value)==null||a.Close(),r.curOperate="drawing",o.graphicsLayer=we(m.view,{id:"length-statistics",title:"长度统计绘制图层"}),o.queryGeometry=void 0,T("crosshair"),o.drawer=Oe(m.view),o.drawAction=o.drawer.create("polygon"),o.drawAction.on(["vertex-add","cursor-update"],E),o.drawAction.on("draw-complete",i=>{var s;E(i),T("");const t=fe("polygon",i.vertices,(s=m.view)==null?void 0:s.spatialReference);o.queryGeometry=t,r.curOperate=""}))},E=e=>{var i,t,s,l;const a=e.vertices.length<3?ge(e.vertices,(i=m.view)==null?void 0:i.spatialReference):he(e.vertices,(t=m.view)==null?void 0:t.spatialReference);(s=o.graphicsLayer)==null||s.removeAll(),a&&((l=o.graphicsLayer)==null||l.add(a))},W=async()=>{var e;m.view&&((e=L.value)==null||e.extentTo(m.view))},Y=async()=>{var i,t,s,l,p,u,d,g,c,y;const e=(i=n.value)==null?void 0:i.dataForm.layer;if(!(e!=null&&e.length)){v.warning("请选择要统计的图层");return}const a=(s=(t=n.value)==null?void 0:t.dataForm)==null?void 0:s.staticFields;if(!(a!=null&&a.length)){v.warning("请选择要统计的属性");return}r.curOperate="statistising";try{const b=((l=n.value)==null?void 0:l.dataForm.layer)||[],h=r.pipeLayerOption.filter(N=>b.indexOf(N.value)!==-1),F=(u=(p=n.value)==null?void 0:p.dataForm)==null?void 0:u.sql,X=h.map(N=>N.id),S=await ue({usertoken:B().gToken,layerids:JSON.stringify(X),group_fields:JSON.stringify(((d=n.value)==null?void 0:d.dataForm.staticFields)||[]),statistic_field:o.staticField,statistic_type:"2",where:F,geometry:o.queryGeometry,f:"pjson"});if(console.log(S),S.data.code===1e4)o.chartTabs=((c=(g=S.data)==null?void 0:g.result)==null?void 0:c.rows)||[],j(),(y=I.value)==null||y.Open(),ee(()=>{P()});else{v.error("统计失败"),r.curOperate="";return}r.tabs=[],await G(h,0,F),r.tabs.length||v.info("查询结果为空")}catch(b){v.error(b.message||"统计失败")}r.curOperate=""},j=()=>{var i,t,s;const e=(i=A.filters)==null?void 0:i.find(l=>l.field==="type"),a=o.chartTabs.map(l=>({label:l.layername,name:l.layerid}));e&&(e.tabs=a.map(l=>({...l,value:l.name}))),(t=_.value)!=null&&t.queryParams&&(_.value.queryParams.type=(s=a[0])==null?void 0:s.name)},P=()=>{var p,u,d,g;const e=[],a=[],i=(p=_.value)==null?void 0:p.queryParams.type,t=o.chartTabs.find(c=>c.layerid===i),s=t==null?void 0:t.layername,l=((u=r.chartFields.find(c=>c.layerName===s))==null?void 0:u.fields)||[];t==null||t.rows.map(c=>{let y="";l.map(b=>{const h=c[b.value],F=h===null||h===""||h===" "?"<空>":h;y===""?y=F:y+=" "+F}),y&&a.push(c[o.staticField]),y&&e.push(y==null?void 0:y.toString())}),(d=k.value)==null||d.resize(),(g=k.value)==null||g.clear(),r.chartOption={title:{},tooltip:{trigger:"axis"},calculable:!0,xAxis:[{type:"category",data:e.map(c=>c!=null&&c.indexOf&&c.indexOf(" ")>0?c.replace(/\s+/g,`
`):c),axisLabel:{interval:0,rotate:0}}],grid:{left:60,right:72,top:40,bottom:96},yAxis:[{type:"value",name:"单位(m)",splitLine:{lineStyle:{color:"#fff",opacity:.2}}}],dataZoom:[{show:!0,start:0,end:100},{type:"inside",start:0,end:100},{show:!0,yAxisIndex:0,filterMode:"empty",width:30,height:"80%",showDataShadow:!1,left:"93%"}],series:[{name:t==null?void 0:t.layername,type:"bar",data:a||[],barWidth:30,label:{show:!0,position:"top"},itemStyle:{normal:{color:"#337AB7"}}}]}},G=async(e,a,i)=>{try{const t=e[a],s=await xe(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+t.id,be({returnGeometry:!1,where:i||"",geometry:o.queryGeometry,orderByFields:["OBJECTID asc"]}));s!==null&&r.tabs.push({label:t.label,name:t.label,data:s}),a<e.length-1&&await G(e,++a,i)}catch{throw console.log("发生错误，获取详情ids失败"),new Error("发生错误，获取详情ids失败")}},R=()=>{var e;(e=n.value)!=null&&e.dataForm&&(n.value.dataForm.sql="")},H=()=>{var e,a,i;r.curOperate="",T(""),(e=o.drawAction)==null||e.destroy(),(a=o.drawer)==null||a.destroy(),o.drawAction=void 0,o.drawer=void 0,o.graphicsLayer&&((i=m.view)==null||i.map.remove(o.graphicsLayer))},K=()=>{var e,a,i;(e=o.graphicsLayer)==null||e.removeAll(),(a=o.drawAction)==null||a.destroy(),(i=o.drawer)==null||i.destroy()};return te(()=>{U()}),re(()=>{H()}),(e,a)=>{const i=ce,t=ae("VChart"),s=Z;return ie(),se("div",null,[C(i,{ref_key:"refForm",ref:n,config:w(x)},null,8,["config"]),C(s,{ref_key:"refChartPanel",ref:I,telport:e.telport,"custom-class":"gis-length-statistics-panel",title:"长度统计结果"},{default:oe(()=>[C(ye,{ref_key:"refChartTab",ref:_,style:{padding:"0"},config:w(A)},null,8,["config"]),le("div",_e,[C(t,{ref_key:"refChart",ref:k,option:w(r).chartOption},null,8,["option"])])]),_:1},8,["telport"]),C(Ce,{ref_key:"refDetail",ref:L,tabs:w(r).tabs,telport:m.telport,onClose:a[0]||(a[0]=()=>w(r).curOperate=""),onRefreshed:a[1]||(a[1]=()=>w(r).curOperate=""),onRefreshing:a[2]||(a[2]=()=>w(r).curOperate="detailing"),onRowdblclick:W},null,8,["tabs","telport"])])}}}),Xt=de(Le,[["__scopeId","data-v-b991228a"]]);export{Xt as default};
