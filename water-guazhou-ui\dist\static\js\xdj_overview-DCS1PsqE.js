import{d as r,a0 as d,c as a,o as c,Q as _,g as v,n as p,p as s,i,C as u}from"./index-r0dFAfgr.js";import{r as l}from"./水泵_在线-2IzRX-2K.js";const f={class:"main"},m={class:"card zutai-card"},x={class:"card-content",style:{bottom:"55%",left:"31%",width:"120px"}},h={class:"row"},g={class:"status"},w=["src"],y={class:"card-content",style:{bottom:"55%",right:"27%",width:"120px"}},b={class:"row"},B={class:"status"},j=["src"],I=r({__name:"xdj_overview",setup(k){const e=d();a({});const o=a(),n=()=>{console.log(e.projectList),e.projectList[0].id};return c(()=>{n(),o.value=setInterval(()=>{n()},3e4)}),_(()=>{clearInterval(o.value)}),(C,t)=>(v(),p("div",f,[s("div",m,[s("div",x,[t[1]||(t[1]=s("div",{class:"card-title"},[s("span",{style:{color:"#d8feff","text-align":"center"}},"消毒设备1#")],-1)),s("div",h,[t[0]||(t[0]=s("div",{class:"label"},"状态：",-1)),s("div",g,[s("img",{src:i(l),style:{width:"15px",height:"15px"}},null,8,w)])])]),s("div",y,[t[3]||(t[3]=s("div",{class:"card-title"},[s("span",{style:{color:"#d8feff","text-align":"center"}},"消毒设备2#")],-1)),s("div",b,[t[2]||(t[2]=s("div",{class:"label"},"状态：",-1)),s("div",B,[s("img",{src:i(l),style:{width:"15px",height:"15px"}},null,8,j)])])])])]))}}),z=u(I,[["__scopeId","data-v-85a1f947"]]);export{z as default};
