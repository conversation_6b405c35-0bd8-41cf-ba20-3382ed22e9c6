package org.thingsboard.server.dao.sql.maintainCircuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.maintainCircuit.MaintainCircuitTeamM;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface MaintainCircuitTeamMMapper extends BaseMapper<MaintainCircuitTeamM> {

    List<MaintainCircuitTeamM> getList(@Param("name") String name, @Param("type") String type, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("name") String name, @Param("type") String type, @Param("tenantId") String tenantId);
}
