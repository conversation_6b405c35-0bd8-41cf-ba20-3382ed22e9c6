import{两 as l,渐 as p}from"./charts-m9UAsyIq.js";import{s as d,W as f}from"./smartDecisionData-BKd4shVG.js";import{u as m}from"./useDetector-BRcb7GRN.js";import{d as u,c as n,o as h,ay as C,g as v,n as k,p as c,q as _,i as e,C as x}from"./index-r0dFAfgr.js";const V={class:"bottom-box",title:"服务类型"},W={class:"chart"},b={class:"chart"},y=u({__name:"FWLX",setup(B){const s=n(),a=n(),i=m();return h(()=>{i.listenToMush(document.documentElement,()=>{var t,o;(t=s.value)==null||t.resize(),(o=a.value)==null||o.resize()})}),(t,o)=>{const r=C("VChart");return v(),k("div",V,[c("div",W,[_(r,{ref_key:"refChart1",ref:s,option:e(l)(e(d))},null,8,["option"])]),c("div",b,[_(r,{ref_key:"refChart2",ref:a,option:e(p)(e(f))},null,8,["option"])])])}}}),L=x(y,[["__scopeId","data-v-b97dc964"]]);export{L as default};
