package org.thingsboard.server.dao.smartService.kpi;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormManualConfig;
import org.thingsboard.server.dao.sql.smartService.kpi.KpiNormManualConfigMapper;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class KpiNormManualConfigServiceImpl implements KpiNormManualConfigService {
    @Autowired
    private KpiNormManualConfigMapper kpiNormManualConfigMapper;

    @Override
    public PageData getList(Boolean enabled, String month, String seatsId, Integer score, int page, int size, String tenantId) {

        List<KpiNormManualConfig> blacklists = kpiNormManualConfigMapper.getList(enabled, month, seatsId, score, page, size, tenantId);

        int total = kpiNormManualConfigMapper.getListCount(enabled, month, seatsId, score, tenantId);

        return new PageData(total, blacklists);

    }

    @Override
    public KpiNormManualConfig save(KpiNormManualConfig kpiNormManualConfig) {
        kpiNormManualConfig.setUpdateTime(new Date());
        if (StringUtils.isBlank(kpiNormManualConfig.getId())) {
            kpiNormManualConfig.setCreateTime(new Date());
            kpiNormManualConfigMapper.insert(kpiNormManualConfig);
        } else {
            kpiNormManualConfigMapper.updateById(kpiNormManualConfig);
        }

        return kpiNormManualConfig;
    }

    @Override
    public int delete(List<String> ids) {
        return kpiNormManualConfigMapper.deleteBatchIds(ids);
    }

    @Override
    public boolean check(KpiNormManualConfig kpiNormManualConfig) {
        QueryWrapper<KpiNormManualConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("month", kpiNormManualConfig.getMonth());
        queryWrapper.eq("norm_id", kpiNormManualConfig.getNormId());
        List<KpiNormManualConfig> list = kpiNormManualConfigMapper.selectList(queryWrapper);
        for (KpiNormManualConfig kpiNormManualConfig1 : list) {
            if (!kpiNormManualConfig1.getId().equals(kpiNormManualConfig.getId())) {
                return false;
            }
        }
        return true;
    }
}
