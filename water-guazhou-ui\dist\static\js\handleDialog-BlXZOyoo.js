import{C as _,M as g,D as h,g as k,n as v,q as l,F as r,G as u,H as I,I as b,J as x,K as C,L as E}from"./index-r0dFAfgr.js";import{c as V}from"./index-Bj5d3Vsu.js";const{$messageSuccess:R,$messageError:w}=g(),y={name:"HandleDialog",props:["dialogInfo"],emits:["refresh"],data(){return{form:{removeRemark:""},rules:{removeRemark:[{required:!0,message:"请输入告警解除备注",trigger:"blur"},{validator:(e,o,s)=>{o.trim()===""?(console.log(o),s(new Error("输入不可为空，请输入"))):s()},trigger:"blur"}]}}},computed:{visible(){return this.dialogInfo.visible}},methods:{save(){this.$refs.form.validate(a=>{if(a){const e=this.dialogInfo.row,o={alarmId:[],remark:this.form.removeRemark},s=h(e.id.id);o.alarmId.push(s),console.log(o,this.$route.path,"-----单独"),V(o).then(()=>{R("解除操作成功"),this.dialogInfo.close(),this.$emit("refresh")})}else return w("请填写备注"),!1})}}},B={class:"handle-dialog"};function D(a,e,o,s,t,n){const f=I,i=b,d=x,c=C,p=E;return k(),v("div",B,[l(p,{modelValue:n.visible,"onUpdate:modelValue":e[1]||(e[1]=m=>n.visible=m),title:"解除告警",width:"35%","append-to-body":"",class:"alarm-design","close-on-click-modal":!1,onClose:o.dialogInfo.close},{default:r(()=>[l(c,{ref:"form",model:t.form,"label-width":"90px",rules:t.rules},{default:r(()=>[l(i,{label:"解除备注",prop:"removeRemark"},{default:r(()=>[l(f,{modelValue:t.form.removeRemark,"onUpdate:modelValue":e[0]||(e[0]=m=>t.form.removeRemark=m),placeholder:"请输入解除备注"},null,8,["modelValue"])]),_:1}),l(i,null,{default:r(()=>[l(d,{type:"primary",onClick:n.save},{default:r(()=>e[2]||(e[2]=[u(" 保 存 ")])),_:1},8,["onClick"]),l(d,{onClick:o.dialogInfo.close},{default:r(()=>e[3]||(e[3]=[u(" 取 消 ")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onClose"])])}const q=_(y,[["render",D]]);export{q as default};
