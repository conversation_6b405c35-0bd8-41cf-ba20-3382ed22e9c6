import{m as u}from"./index-r0dFAfgr.js";const o=t=>u({url:"/api/ss/seats/user",method:"get",params:t}),c=t=>u({url:"/api/ss/workOrder/type/tree",method:"get",params:t}),n=t=>u({url:"/api/ss/dict",method:"get",params:t}),d=()=>({getOrderTypeOption:async()=>{var e;return((e=(await c({isDel:0})).data)==null?void 0:e.data).map(a=>({label:a.name,value:a.id,data:a.children}))},getOrderStatus:l=>{const s=[{label:"待接单",value:"PENDING"},{label:"分派",value:"ASSIGN"},{label:"接收",value:"RESOLVING"},{label:"到场",value:"ARRIVING"},{label:"处理",value:"PROCESSING"},{label:"完工",value:"APPROVED"},{label:"回访",value:"REVISIT"},{label:"办结",value:"CONCLUDE"},{label:"终止",value:"TERMINATED"},{label:"审核退回",value:"CHARGEBACK"},{label:"退单驳回",value:"REJECTED"}];return l&&s.unshift({label:"全部",value:""}),s},sourceList:async()=>{var e;return((e=(await n({pid:"c434676651adcd8e11d1e563eb687e06"})).data)==null?void 0:e.data).map(a=>({label:a.name,value:a.id}))},areaList:async()=>{var e;return((e=(await n({pid:"2d28676d2f3d1541c23c306bc895e276"})).data)==null?void 0:e.data).map(a=>({label:a.name,value:a.id}))},directOptions:()=>[{label:"全部",value:""},{label:"呼入",value:"0"},{label:"呼出",value:"1"},{label:"内线",value:"2"}],statusOption:()=>[{label:"全部",value:""},{label:"未接通",value:"0"},{label:"接通",value:"1"},{label:"忙",value:"2"},{label:"语音留言",value:"3"}],seatUserList:async()=>{var e;return((e=(await o({page:1,size:999})).data)==null?void 0:e.data.data).map(a=>({label:a.userName,value:a.id}))}}),I=d;export{I as u,c as w};
