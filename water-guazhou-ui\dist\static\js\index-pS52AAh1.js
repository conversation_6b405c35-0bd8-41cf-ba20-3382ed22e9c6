import{d as u,c as f,g as a,n,aB as v,aJ as k,p as o,aw as i,i as l,bh as B,C}from"./index-r0dFAfgr.js";const b={class:"footer"},h=["onClick"],x=u({__name:"index",props:{barItems:{}},emits:["to"],setup(m,{emit:p}){const d=p,t=f(m.barItems[0]),_=s=>{t.value=s,d("to",s)};return(s,g)=>(a(),n("div",b,[(a(!0),n(v,null,k(s.barItems,e=>{var r,c;return a(),n("div",{key:e.name,class:"footer__item",onClick:y=>_(e)},[o("div",{class:i(["icon",[((r=l(t))==null?void 0:r.name)===e.name?"is-active":""]])},null,2),o("div",{class:i(["title",[((c=l(t))==null?void 0:c.name)===e.name?"is-active":""]])},[o("span",null,B(e.text),1)],2)],8,h)}),128))]))}}),w=C(x,[["__scopeId","data-v-81bdf204"]]);export{w as default};
