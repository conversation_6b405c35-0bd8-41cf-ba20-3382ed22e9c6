package org.thingsboard.server.dao.assessmenttable;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.assessmenttable.AssessmentTableEntity;

import java.util.List;
import java.util.UUID;

/**
 * 考核表Mapper接口
 */
@Mapper
public interface AssessmentTableMapper extends BaseMapper<AssessmentTableEntity> {

    /**
     * 分页查询考核表列表
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param searchText 搜索文本
     * @param assessmentType 考核类型
     * @param status 考核状态
     * @param period 考核周期
     * @return 分页结果
     */
    IPage<AssessmentTableEntity> selectAssessmentTables(
            Page<AssessmentTableEntity> page,
            @Param("tenantId") String tenantId,
            @Param("searchText") String searchText,
            @Param("assessmentType") String assessmentType,
            @Param("status") String status,
            @Param("period") String period
    );
    
    /**
     * 根据ID查询考核表
     *
     * @param id 考核表ID
     * @return 考核表实体
     */
    AssessmentTableEntity selectById(@Param("id") String id);
    
    /**
     * 批量删除考核表
     *
     * @param ids 考核表ID列表
     * @return 删除数量
     */
    int batchDeleteByIds(@Param("ids") List<String> ids);
} 