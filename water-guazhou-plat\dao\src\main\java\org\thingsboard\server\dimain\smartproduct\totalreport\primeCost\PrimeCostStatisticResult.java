package org.thingsboard.server.dimain.smartproduct.totalreport.primeCost;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Getter
@Setter
public class PrimeCostStatisticResult {
    // 水价信息列表
    private Map<String, PrimeCostStatisticResultOfWaterPrice> waterPriceInfoMap;

    // 药剂信息列表
    private Map<String, List<PrimeCostStatisticResultOfMedical>> medicalPriceInfoMap;

    // 第一个药品信息
    private List<PrimeCostStatisticResultOfMedical> firstMedicalPriceInfoList;

    // 总药剂吨水成本（吨/元）
    private Map<String, JSONObject> medicalTotalPerWaterPriceInfo = new HashMap<>();

    // 电费信息
    private Map<String, PrimeCostStatisticResultOfPower> powerPriceInfoMap;

    // 总药剂费用
    private BigDecimal totalMedicalPrice;

    // 总费用
    private BigDecimal totalPrice;

    // 去年同期总费用
    private BigDecimal totalPriceLastYear;

    // 费用小计同比增长
    private BigDecimal totalPriceCurrentRangeIncreaseCompareToLastYearTheRange;

    // 格式化好的费用小计同比增长
    private String totalPriceCurrentRangeIncreaseCompareToLastYearTheRangeName;

    // 总水量
    private BigDecimal totalWaterAmount;


    public void setMedicalPriceInfoMap(Map<String, List<PrimeCostStatisticResultOfMedical>> medicalPriceInfoMap) {
        this.medicalPriceInfoMap = medicalPriceInfoMap;
        for (String key : medicalPriceInfoMap.keySet()) {
            BigDecimal total = medicalPriceInfoMap.get(key).stream()
                    .map(PrimeCostStatisticResultOfMedical::getPricePerWater)
                    .reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
            JSONObject medicalTotalPerWaterPriceInfoData = new JSONObject();
            medicalTotalPerWaterPriceInfoData.put("medicalTotalPerWaterPrice", total);
            medicalTotalPerWaterPriceInfo.put(key, medicalTotalPerWaterPriceInfoData);
        }
        Optional<String> firstMedicalPriceInfo = medicalPriceInfoMap.keySet().stream().findFirst();
        String key = firstMedicalPriceInfo.orElse(null);
        firstMedicalPriceInfoList = medicalPriceInfoMap.get(key);
    }

    public void setWaterPriceInfoMap(List<PrimeCostStatisticResultOfWaterPrice> list) {
        Map<String, PrimeCostStatisticResultOfWaterPrice> mapping = new HashMap<>();
        for (PrimeCostStatisticResultOfWaterPrice data : list) {
            mapping.put(data.getFactory(), data);
        }

        waterPriceInfoMap = mapping;
    }

    public void setPowerPriceInfoMap(List<PrimeCostStatisticResultOfPower> powerPriceInfoList) {
        Map<String, PrimeCostStatisticResultOfPower> mapping = new HashMap<>();
        for (PrimeCostStatisticResultOfPower data : powerPriceInfoList) {
            mapping.put(data.getFactory(), data);
        }
        powerPriceInfoMap = mapping;
    }

    public void calculate() {
        totalPrice = BigDecimal.ZERO;
        totalPriceLastYear = BigDecimal.ZERO;
        totalPriceCurrentRangeIncreaseCompareToLastYearTheRange = BigDecimal.ZERO;
        for (String factory : waterPriceInfoMap.keySet()) {
            PrimeCostStatisticResultOfWaterPrice priceInfo = waterPriceInfoMap.get(factory);
            PrimeCostStatisticResultOfPower powerInfo = powerPriceInfoMap.get(factory);
            // 计算费用小计
            BigDecimal factoryTotal = BigDecimal.ZERO;
            factoryTotal = factoryTotal.add(priceInfo.getTotalWaterPrice());
            List<PrimeCostStatisticResultOfMedical> medicalPriceInfoList = medicalPriceInfoMap.get(factory);
            if (medicalPriceInfoList != null)
                for (PrimeCostStatisticResultOfMedical primeCostStatisticResultOfMedical : medicalPriceInfoList) {
                    factoryTotal = factoryTotal.add(primeCostStatisticResultOfMedical.getTotalMedicalPrice());
                }
            factoryTotal = factoryTotal.add(powerInfo.getTotalPrice());
            priceInfo.setTotalPrice(factoryTotal);
            totalPrice = totalPrice.add(factoryTotal);

            // 计算去年费用小计
            BigDecimal factoryTotalLastYear = BigDecimal.ZERO;
            boolean lastYearContainsData = false;
            if (priceInfo.getTotalWaterPriceLastYear() != null) {
                factoryTotalLastYear = factoryTotalLastYear.add(priceInfo.getTotalWaterPriceLastYear());
                lastYearContainsData = true;
            }
            if (medicalPriceInfoList != null)
                for (PrimeCostStatisticResultOfMedical primeCostStatisticResultOfMedical : medicalPriceInfoList) {
                    if (primeCostStatisticResultOfMedical.getTotalMedicalPriceLastYear() != null) {
                        factoryTotalLastYear = factoryTotalLastYear.add(primeCostStatisticResultOfMedical.getTotalMedicalPriceLastYear());
                        lastYearContainsData = true;
                    }
                }
            if (powerInfo.getTotalPriceLastYear() != null) {
                factoryTotalLastYear = factoryTotalLastYear.add(powerInfo.getTotalPriceLastYear());
                lastYearContainsData = true;
            }
            if (lastYearContainsData) {
                totalPriceLastYear = totalPriceLastYear.add(factoryTotalLastYear);
                priceInfo.setTotalPriceLastYear(factoryTotalLastYear);
            }

            // 费用小计同比增长
            if (priceInfo.getTotalPriceLastYear() != null && !Objects.equals(priceInfo.getTotalPriceLastYear(), BigDecimal.ZERO)) {
                priceInfo.setTotalPriceCurrentRangeIncreaseCompareToLastYearTheRange(
                        priceInfo.getTotalPrice()
                                .subtract(priceInfo.getTotalPriceLastYear())
                                .divide(priceInfo.getTotalPriceLastYear(), 4, RoundingMode.UP)
                                .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.UP)
                );
            }

            // 吨水成本小计
            if (priceInfo.getTotalWaterAmount().equals(BigDecimal.ZERO)) {
                priceInfo.setTotalPrimeCostPerWater(BigDecimal.ZERO);
            } else {
                priceInfo.setTotalPrimeCostPerWater(factoryTotalLastYear.divide(priceInfo.getTotalWaterAmount(), RoundingMode.UP));
            }
        }
        if (!totalPriceLastYear.equals(BigDecimal.ZERO)) {
            totalPriceCurrentRangeIncreaseCompareToLastYearTheRange = totalPrice.subtract(totalPriceLastYear)
                    .divide(totalPriceLastYear, 4, RoundingMode.UP);
            totalPriceCurrentRangeIncreaseCompareToLastYearTheRangeName = totalPriceCurrentRangeIncreaseCompareToLastYearTheRange
                                                                                  .multiply(BigDecimal.valueOf(100))
                                                                                  .setScale(2, RoundingMode.UP) + "%";
        }

        totalMedicalPrice = BigDecimal.ZERO;
        for (List<PrimeCostStatisticResultOfMedical> prices : medicalPriceInfoMap.values()) {
            for (PrimeCostStatisticResultOfMedical price : prices) {
                totalMedicalPrice = totalMedicalPrice.add(price.getTotalMedicalPrice());
            }
        }

        totalWaterAmount = BigDecimal.ZERO;
        for (PrimeCostStatisticResultOfWaterPrice price : waterPriceInfoMap.values()) {
            totalWaterAmount = totalWaterAmount.add(price.getTotalWaterAmount());
        }
    }

}
