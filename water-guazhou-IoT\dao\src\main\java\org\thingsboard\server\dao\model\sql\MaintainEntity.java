/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.MaintainId;
import org.thingsboard.server.common.data.id.RepairId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.maintain.Maintain;
import org.thingsboard.server.common.data.repair.Repair;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MAINTAIN_NAME)
public class MaintainEntity extends BaseSqlEntity<Maintain> {

    @Column(name = ModelConstants.MAINTAIN_JSON_NAME)
    private String name;

    @Type(type = "json")
    @Column(name = ModelConstants.ASSET_ADDITIONAL_INFO_PROPERTY)
    private JsonNode additionalInfo;

    @Column(name = ModelConstants.MAINTAIN_JSON)
    private String maintainJson;

    @Column(name = ModelConstants.REPAIR_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.REPAIR_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.REPAIR_TENANT_ID)
    private String tenantId;

    @Column(name = ModelConstants.MAINTAIN_PARAMS)
    private String params;

    @Column(name = ModelConstants.MAINTAIN_TYPE)
    private String type;

    @Column(name = ModelConstants.MAINTAIN_PERIOD)
    private String period;

    public MaintainEntity() {
    }

    public MaintainEntity(Maintain maintain) {
        if (maintain.getId() != null) {
            this.id = toString(maintain.getId().getId());
        }
        if (maintain.getDeviceId() != null) {
            this.deviceId = toString(maintain.getDeviceId().getId());
        }
        if (maintain.getTenantId() != null) {
            this.tenantId = toString(maintain.getTenantId().getId());
        }
        this.createTime = maintain.getCreateTime();
        this.name = maintain.getName();
        this.maintainJson = maintain.getMaintainJson();
        this.additionalInfo = maintain.getAdditionalInfo();
        this.params=maintain.getParams();
        this.type=maintain.getType();
        this.period=maintain.getPeriod();
    }

    @Override
    public Maintain toData() {
        Maintain maintain = new Maintain(new MaintainId(getId()));
        maintain.setDeviceId(new DeviceId(toUUID(deviceId)));
        maintain.setCreateTime(createTime);
        maintain.setTenantId(new TenantId(toUUID(tenantId)));
        maintain.setName(name);
        maintain.setAdditionalInfo(additionalInfo);
        maintain.setMaintainJson(maintainJson);
        maintain.setParams(params);
        maintain.setType(type);
        maintain.setPeriod(period);
        return maintain;
    }
}
