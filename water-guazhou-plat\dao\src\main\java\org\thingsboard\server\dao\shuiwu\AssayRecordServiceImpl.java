package org.thingsboard.server.dao.shuiwu;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.AssayRecord;
import org.thingsboard.server.dao.sql.shuiwu.AssayRecordRepository;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class AssayRecordServiceImpl implements AssayRecordService {

    @Autowired
    private AssayRecordRepository assayRecordRepository;

    @Override
    public AssayRecord save(AssayRecord assayRecord) {
        return assayRecordRepository.save(assayRecord);
    }

    @Override
    public List<AssayRecord> findListByStationId(String stationId) {
        return assayRecordRepository.findByStationIdOrderByTimeDesc(stationId);
    }

    @Override
    public List<AssayRecord> findListByTenantId(TenantId tenantId) {
        return  assayRecordRepository.findByTenantIdOrderByTimeDesc(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public Object waterQualityAnalysis(String stationId, Long start, Long end) {
        List<AssayRecord> dataList = assayRecordRepository.findByStationIdAndTimeBetween(stationId, new Date(start), new Date(end));

        // 统计
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, String> standardMap = new HashMap<>();
        standardMap.put("dissolvedOxygen", "6,7");
        standardMap.put("conductivity", "2,3");
        standardMap.put("temperature", "2,3");
        standardMap.put("chlorophyl", "3.33,4");
        standardMap.put("ammoniaNitrogen", "2,3");
        standardMap.put("nitrate", "2,3");
        standardMap.put("dissolvedOrganicCarbon", "2,3");
        standardMap.put("organicCarbon", "2,3");
        standardMap.put("turbidity", "2,3");
        standardMap.put("ph", "6,8");
        standardMap.put("chromaticity", "2,3");
        standardMap.put("uv254", "2,3");

        Map<String, String> propertyNameMap = new HashMap<>();
        propertyNameMap.put("dissolvedOxygen", "溶解氧(mg/L)");
        propertyNameMap.put("conductivity", "电导率(us/cm)");
        propertyNameMap.put("temperature", "温度(℃)");
        propertyNameMap.put("chlorophyl", "叶绿素(mg/L)");
        propertyNameMap.put("ammoniaNitrogen", "氨氮(mg/L)");
        propertyNameMap.put("nitrate", "硝酸盐(mg/L)");
        propertyNameMap.put("dissolvedOrganicCarbon", "溶解性有机碳(mg/L)");
        propertyNameMap.put("organicCarbon", "总有机碳(mg/L)");
        propertyNameMap.put("turbidity", "浊度(NTU)");
        propertyNameMap.put("ph", "pH");
        propertyNameMap.put("chromaticity", "色度(Hazen)");
        propertyNameMap.put("uv254", "UV254(Abs/m)");

        Map<String, List<BigDecimal>> dataMap = new LinkedHashMap<>();
        for (AssayRecord assayRecord : dataList) {
            BigDecimal dissolvedOxygen = assayRecord.getDissolvedOxygen();
            List<BigDecimal> dissolvedOxygenDataList = new ArrayList<>();
            if (dataMap.containsKey("dissolvedOxygen")) {
                dissolvedOxygenDataList = dataMap.get("dissolvedOxygen");
            }
            dissolvedOxygenDataList.add(dissolvedOxygen);
            dataMap.put("dissolvedOxygen", dissolvedOxygenDataList);

            BigDecimal conductivity = assayRecord.getConductivity();
            List<BigDecimal> conductivityDataList = new ArrayList<>();
            if (dataMap.containsKey("conductivity")) {
                conductivityDataList = dataMap.get("conductivity");
            }
            conductivityDataList.add(conductivity);
            dataMap.put("conductivity", conductivityDataList);

            BigDecimal temperature = assayRecord.getTemperature();
            List<BigDecimal> temperatureDataList = new ArrayList<>();
            if (dataMap.containsKey("temperature")) {
                temperatureDataList = dataMap.get("temperature");
            }
            temperatureDataList.add(temperature);
            dataMap.put("temperature", temperatureDataList);

            BigDecimal chlorophyl = assayRecord.getChlorophyl();
            List<BigDecimal> chlorophylDataList = new ArrayList<>();
            if (dataMap.containsKey("chlorophyl")) {
                chlorophylDataList = dataMap.get("chlorophyl");
            }
            chlorophylDataList.add(chlorophyl);
            dataMap.put("chlorophyl", chlorophylDataList);

            BigDecimal ammoniaNitrogen = assayRecord.getAmmoniaNitrogen();
            List<BigDecimal> ammoniaNitrogenDataList = new ArrayList<>();
            if (dataMap.containsKey("ammoniaNitrogen")) {
                ammoniaNitrogenDataList = dataMap.get("ammoniaNitrogen");
            }
            ammoniaNitrogenDataList.add(ammoniaNitrogen);
            dataMap.put("ammoniaNitrogen", ammoniaNitrogenDataList);

            BigDecimal nitrate = assayRecord.getNitrate();
            List<BigDecimal> nitrateDataList = new ArrayList<>();
            if (dataMap.containsKey("nitrate")) {
                nitrateDataList = dataMap.get("nitrate");
            }
            nitrateDataList.add(nitrate);
            dataMap.put("nitrate", nitrateDataList);

            BigDecimal dissolvedOrganicCarbon = assayRecord.getDissolvedOrganicCarbon();
            List<BigDecimal> dissolvedOrganicCarbonDataList = new ArrayList<>();
            if (dataMap.containsKey("dissolvedOrganicCarbon")) {
                dissolvedOrganicCarbonDataList = dataMap.get("dissolvedOrganicCarbon");
            }
            dissolvedOrganicCarbonDataList.add(dissolvedOrganicCarbon);
            dataMap.put("dissolvedOrganicCarbon", dissolvedOrganicCarbonDataList);

            BigDecimal organicCarbon = assayRecord.getOrganicCarbon();
            List<BigDecimal> organicCarbonDataList = new ArrayList<>();
            if (dataMap.containsKey("organicCarbon")) {
                organicCarbonDataList = dataMap.get("organicCarbon");
            }
            organicCarbonDataList.add(organicCarbon);
            dataMap.put("organicCarbon", organicCarbonDataList);

            BigDecimal turbidity = assayRecord.getTurbidity();
            List<BigDecimal> turbidityDataList = new ArrayList<>();
            if (dataMap.containsKey("turbidity")) {
                turbidityDataList = dataMap.get("turbidity");
            }
            turbidityDataList.add(turbidity);
            dataMap.put("turbidity", turbidityDataList);

            BigDecimal ph = assayRecord.getPh();
            List<BigDecimal> phDataList = new ArrayList<>();
            if (dataMap.containsKey("ph")) {
                phDataList = dataMap.get("ph");
            }
            phDataList.add(ph);
            dataMap.put("ph", phDataList);

            BigDecimal chromaticity = assayRecord.getChromaticity();
            List<BigDecimal> chromaticityDataList = new ArrayList<>();
            if (dataMap.containsKey("chromaticity")) {
                chromaticityDataList = dataMap.get("chromaticity");
            }
            chromaticityDataList.add(chromaticity);
            dataMap.put("chromaticity", chromaticityDataList);

            BigDecimal uv254 = assayRecord.getUv254();
            List<BigDecimal> uv254DataList = new ArrayList<>();
            if (dataMap.containsKey("uv254")) {
                uv254DataList = dataMap.get("uv254");
            }
            uv254DataList.add(uv254);
            dataMap.put("uv254", uv254DataList);

        }

        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<BigDecimal>> entry : dataMap.entrySet()) {
            String key = entry.getKey();

            String standardString = standardMap.get(key);
            String[] standardArray = standardString.split(",");

            List<BigDecimal> value = entry.getValue();
            BigDecimal max = null;
            BigDecimal min = null;
            BigDecimal total = new BigDecimal("0");
            int ok = 0;
            int time = 0;
            JSONObject object = new JSONObject();
            if (value != null && value.size() > 0) {
                for (BigDecimal data : value) {
                    if (data == null) {
                        continue;
                    }
                    if (max == null || max.doubleValue() < data.doubleValue()) {
                        max = data;
                    }
                    if (min == null || min.doubleValue() > data.doubleValue()) {
                        min = data;
                    }
                    total = total.add(data);

                    if (standardArray.length == 2) {
                        if (data.doubleValue() > Double.parseDouble(standardArray[0]) && data.doubleValue() < Double.parseDouble(standardArray[1])) {
                            ok++;
                        }
                    }
                    time++;
                }
                object.put("standardOk", ok);
                object.put("standardBad", value.size() - ok);
                object.put("complianceRate", BigDecimal.valueOf(ok)
                        .divide(BigDecimal.valueOf(value.size()), 2, BigDecimal.ROUND_DOWN)
                        .multiply(new BigDecimal("100")));
            }

            String name = propertyNameMap.get(key);
            object.put("name", StringUtils.isBlank(name) ? key : name);
            object.put("max", max);
            object.put("min", min);
            object.put("avg", total.divide(BigDecimal.valueOf(value == null ? 0 : value.size()), 2, BigDecimal.ROUND_DOWN));
            object.put("time", time);

            resultList.add(object);
        }

        return resultList;
    }
}
