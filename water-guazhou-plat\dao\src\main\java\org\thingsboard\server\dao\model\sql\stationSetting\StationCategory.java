package org.thingsboard.server.dao.model.sql.stationSetting;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 站点类别配置
 */
@Data
@TableName(value = "tb_station_category")
public class StationCategory {

    private String id;

    private String type;

    private String name;

    private String code;

    private Integer orderNumber;

    private String isSystem;

    private String status;

    private Date createTime;

    private String tenantId;


}
