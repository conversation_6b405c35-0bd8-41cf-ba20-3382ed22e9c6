package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_comment")
public class SsPortalComment {
    // id
    private String id;

    // 姓名
    private String name;

    // 手机号
    private String phone;

    // 邮箱
    private String email;

    // 内容
    private String content;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
