import{d as T,c as L,r as k,b as u,Q as q,g as z,h as E,F,q as g,i as d,_ as H,X as V}from"./index-r0dFAfgr.js";import{g as C}from"./MapView-DaoQedLH.js";import{g as $,c as J,s as Q}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as G,a as U}from"./LayerHelper-Cn-iiqxI.js";import{g as W,q as K,c as X}from"./QueryHelper-ILO3qZqg.js";import{u as j}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{u as Y}from"./useScheme-DcjSAE44.js";import"./geometryEngineBase-BhsKaODW.js";import Z from"./RightDrawerMap-D5PhmGFO.js";import ee from"./SchemeHeader-BLYQTCg3.js";import{_ as re}from"./SchemeManage.vue_vue_type_script_setup_true_lang-fv9Irhyi.js";import{_ as te}from"./SaveScheme.vue_vue_type_script_setup_true_lang-Bt-6iBz5.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";const ct=T({__name:"BufferSearch",setup(ie){const b=L(),p=L(),s=k({tabs:[],loading:!1,layerInfos:[],layerIds:[]}),e={},h=k({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"点要素",iconifyIcon:"mdi:circle-slice-8",click:()=>v("point")},{perm:!0,text:"",type:"default",size:"large",title:"线要素",iconifyIcon:"mdi:chart-timeline-variant",click:()=>v("polyline")},{perm:!0,text:"",type:"default",size:"large",title:"面要素",iconifyIcon:"mdi:shape-polygon-plus",click:()=>v("polygon")},{perm:!0,text:"",type:"default",size:"large",iconifyIcon:"ep:delete",click:()=>w()}]}]},{fieldset:{desc:"缓冲半径"},fields:[{type:"input-number",append:"米",field:"distance"}]},{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!1,showCheckbox:!0,field:"layerid",nodeKey:"value"},{type:"btn-group",btns:[{perm:!0,text:"查询",styles:{width:"100%"},click:()=>S()},{perm:window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,text:"保存方案",styles:{width:"100%"},click:()=>D()}]}]}],labelPosition:"top",gutter:12,defaultValue:{distance:200}}),v=r=>{var t,i,o;e.view&&((t=e.graphicsLayer)==null||t.removeAll(),(i=e.bufferLayer)==null||i.removeAll(),(o=M.value)==null||o.create(r))},S=async()=>{var t,i,o,l,m,c;if(!e.graphics){u.warning("请先绘制图形");return}u.info("正在查询中，请稍候...");const r=((i=(t=p.value)==null?void 0:t.dataForm)==null?void 0:i.distance)||"0";s.tabs.length=0;try{Number(r)?e.bufferGeometry=await R():e.bufferGeometry=(o=e.graphics)==null?void 0:o.geometry;const f=((m=(l=p.value)==null?void 0:l.dataForm.layerid)==null?void 0:m.filter(y=>y>=0))||[];s.tabs=await W(f,s.layerInfos,{where:"1=1",geometry:e.bufferGeometry}),(c=b.value)==null||c.refreshDetail(s.tabs),e.bufferGeometry&&$(e.view,new C({geometry:e.bufferGeometry}),{avoidHighlight:!0})}catch{u.error("系统错误")}s.loading=!1},R=async()=>{var l,m,c,f,y,a,I;const r=(l=e.graphics)==null?void 0:l.geometry;if((m=e.bufferLayer)==null||m.removeAll(),!r)return;const t=((f=(c=p.value)==null?void 0:c.dataForm)==null?void 0:f.distance)||0,i=await K(X({bufferSpatialReference:(y=e.view)==null?void 0:y.spatialReference,distances:[t],geometries:[r],outSpatialReference:(a=e.view)==null?void 0:a.spatialReference,geodesic:!0,unit:"meters",unionResults:!1})),o=J({geometry:i[0],symbol:Q("polygon",{color:[0,182,153,.2],outlineColor:"#00B699",outlineWidth:1})});return(I=e.bufferLayer)==null||I.add(o),i[0]},w=()=>{var r,t;(r=e.graphicsLayer)==null||r.removeAll(),(t=e.bufferLayer)==null||t.removeAll(),e.graphics=void 0},x=async()=>{var r,t;if(window.GIS_SERVER_SWITCH){const i=(r=h.group.find(m=>m.id==="layer"))==null?void 0:r.fields[0];let l=((t=e.view)==null?void 0:t.layerViews.items[0].layer.sublayers).items.map(m=>({label:m.name,value:m.name}));i.options=l,p.value&&(p.value.dataForm.layerid=l.map(m=>m.value))}else s.layerIds=U(e.view),V(s.layerIds).then(i=>{var c,f,y;s.layerInfos=((f=(c=i.data)==null?void 0:c.result)==null?void 0:f.rows)||[];const o=(y=h.group.find(a=>a.id==="layer"))==null?void 0:y.fields[0],l=s.layerInfos.filter(a=>a.geometrytype==="esriGeometryPoint").map(a=>({label:a.layername,value:a.layerid,data:a})),m=s.layerInfos.filter(a=>a.geometrytype==="esriGeometryPolyline").map(a=>({label:a.layername,value:a.layerid,data:a}));o&&(o.options=[{label:"管点类",value:-1,children:l},{label:"管线类",value:-2,children:m}]),p.value&&(p.value.dataForm.layerid=s.layerIds)})},{initSketch:B,destroySketch:A,sketch:M}=j(),_=r=>{r.state==="complete"&&(e.graphics=r.graphics[0],console.log(JSON.stringify(e.graphics)))},N=async r=>{e.view=r,e.graphicsLayer=G(e.view,{id:"search-buffer-temp",title:"缓冲区-绘制"}),e.bufferLayer=G(e.view,{id:"search-buffer",title:"缓冲区"}),B(e.view,e.graphicsLayer,{updateCallBack:_,createCallBack:_}),setTimeout(()=>{x()},1e3)},n=Y("buffer"),D=()=>{if(!e.graphics){u.warning("请先绘制图形");return}n.openSaveDialog()},O=async r=>{var i,o;w();const t=n.parseScheme(r);(i=p.value)!=null&&i.dataForm&&(p.value.dataForm.layerid=t.layerid||[],p.value.dataForm.distance=t.distance),t.graphic&&(e.graphics=C.fromJSON(t.graphic),(o=e.graphicsLayer)==null||o.add(e.graphics)),S()},P=r=>{var t,i;n.submitScheme({...r,type:n.schemeType.value,detail:JSON.stringify({layerid:((t=p.value)==null?void 0:t.dataForm.layerid)||[],distance:(i=p.value)==null?void 0:i.dataForm.distance,graphic:e.graphics})})};return q(()=>{var r,t,i,o;(r=e.graphicsLayer)==null||r.removeAll(),(t=e.graphicsLayer)==null||t.destroy(),(i=e.bufferLayer)==null||i.removeAll(),(o=e.bufferLayer)==null||o.destroy(),A()}),(r,t)=>{const i=H;return z(),E(Z,{ref_key:"refMap",ref:b,title:"缓冲区查询",onMapLoaded:N},{"right-title":F(()=>[g(ee,{title:"缓冲区查询",onSchemeClick:d(n).openManagerDialog},null,8,["onSchemeClick"])]),default:F(()=>[g(i,{ref_key:"refForm",ref:p,config:d(h)},null,8,["config"]),g(re,{ref:d(n).getSchemeManageRef,type:d(n).schemeType.value,onRowClick:O},null,8,["type"]),g(te,{ref:d(n).getSaveSchemeRef,onSubmit:P},null,512)]),_:1},512)}}});export{ct as default};
