import{_ as O}from"./TreeBox-DDD2iwoR.js";import{_ as P}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as W}from"./index-C9hz-UZb.js";import{z as k,d as N,r as s,c as x,bF as o,a8 as h,s as b,S,b as C,o as F,g as I,h as j,F as v,q as n,aq as w,al as H,b7 as M,ak as Y,C as q}from"./index-r0dFAfgr.js";import{_ as z}from"./CardSearch-CB_HNR-Q.js";import{_ as B}from"./index-BJ-QPYom.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as V}from"./usePartition-DkcY9fQ2.js";import{f as $}from"./DateFormatter-Bm9a68Ax.js";import{g as R}from"./dict-C6KyvrXS.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./index-0NlGN6gS.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const A=m=>k({url:"/api/spp/dma/cost/list",method:"get",params:m}),E=m=>k({url:"/api/spp/dma/cost",method:"post",data:m}),G=N({__name:"index",setup(m){const d=s({dialogType:"add",stationTree:[],dictList:[],stationId:""}),c=x(),_=x(),r=s({data:[],loading:!1,currentProject:null,title:"选择分区",expandOnClickNode:!1,treeNodeHandleClick:async e=>{r.currentProject!==e&&(r.currentProject=e,await l())}}),L=s({defaultParams:{stype:"year",year:o().format(),month:o().format(),day:[o().format(),o().format()]},filters:[{type:"select",label:"选择方式",clearable:!1,field:"stype",options:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按时间段",value:"day"}]},{type:"year",label:"",clearable:!1,field:"year",handleHidden:(e,t,a)=>{a.hidden=e.stype==="day"||e.stype==="month"}},{type:"month",label:"",clearable:!1,field:"month",handleHidden:(e,t,a)=>{a.hidden=e.stype==="day"||e.stype==="year"}},{type:"daterange",label:"",clearable:!1,field:"day",handleHidden:(e,t,a)=>{a.hidden=e.stype==="month"||e.stype==="year"}},{type:"select",label:"成本类型",field:"type",options:h(()=>d.dictList)},{type:"input",label:"记录人名称",labelWidth:120,field:"creatorName"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:b(H),click:()=>l()},{type:"default",perm:!0,text:"重置",svgIcon:b(M),click:()=>{var e;(e=_.value)==null||e.resetForm(),l()}},{perm:!0,type:"success",text:"新增",svgIcon:b(Y),click:()=>{var e,t,a;d.dialogType="add",g.title="新建成本记录",g.defaultValue={startDate:o().format(),endDate:o().format(),syncMonth:o().format()},(t=(e=c.value)==null?void 0:e.refForm)==null||t.resetForm(),(a=c.value)==null||a.openDialog()}}]}]}),g=s({dialogWidth:500,title:"新建成本记录",labelPosition:"right",submit:e=>{console.log(e),S("确定提交？","提示信息").then(async()=>{var t;try{E(e).then(a=>{a.data.code===200&&C.success("提交成功")}),await l(),(t=c.value)==null||t.closeDialog()}catch{C.error("提交失败")}})},group:[{fields:[{type:"select-tree",field:"partitionId",rules:[{required:!0,message:"请填选择分区"}],placeholder:"请填选择分区",label:"分区",options:h(()=>r.data)},{type:"input",field:"code",rules:[{required:!0,message:"请填写工程编码"}],placeholder:"请填写工程编码",label:"工程编码"},{type:"select",field:"type",rules:[{required:!0,message:"请选择成本类型"}],placeholder:"请选择成本类型",label:"成本类型",options:h(()=>d.dictList)},{type:"input",field:"base",rules:[{required:!0,message:"请填写成本值"}],placeholder:"请填写成本值",label:"成本值"},{type:"input",field:"value",rules:[{required:!0,message:"请填写成本含量"}],placeholder:"请填写成本含量",label:"成本含量"},{type:"textarea",field:"remark",placeholder:"请填写备注",label:"备注"}]}]}),l=async()=>{var f;const e=((f=_.value)==null?void 0:f.queryParams)||{};let t={};if((e==null?void 0:e.stype)==="day")t={start:o(e.day[0]).startOf("day").valueOf(),end:o(e.day[1]).endOf("day").valueOf()};else if((e==null?void 0:e.stype)==="month"||(e==null?void 0:e.stype)==="year"){const p=e.stype==="month"?e.month:e.year;t={start:o(p).startOf(e==null?void 0:e.stype).valueOf(),end:o(p).endOf(e==null?void 0:e.stype).valueOf()}}const a={type:e.type,creatorName:e.creatorName,page:i.pagination.page||1,size:i.pagination.limit||20,...t,partitionId:r.currentProject.id};A(a).then(p=>{var u,y;i.dataList=(u=p.data.data)==null?void 0:u.data,i.pagination.total=(y=p.data.data)==null?void 0:y.total})},i=s({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"code",label:"工程编码",minWidth:120},{prop:"typeName",label:"成本类型",minWidth:120},{prop:"base",label:"成本值",minWidth:140},{prop:"value",label:"成本含量",minWidth:120},{prop:"creatorName",label:"记录人名称",minWidth:120},{prop:"createTime",label:"记录时间",minWidth:120,formatter:e=>$(e.createTime,"YYYY-MM-DD HH:mm:ss")},{prop:"remark",label:"备注",minWidth:120}],pagination:{refreshData:({page:e,size:t})=>{i.pagination.page=e,i.pagination.limit=t,l()}}}),D=V();return F(async()=>{await D.getTree(),r.data=D.Tree.value,r.currentProject=r.data[0],R({type:"1",page:1,size:9999}).then(e=>{var t,a;d.dictList=(t=e.data.data)==null?void 0:t.data,console.log((a=e.data.data)==null?void 0:a.data)})}),(e,t)=>{const a=B,f=z,p=w,u=W,y=P,T=O;return I(),j(T,null,{tree:v(()=>[n(a,{ref:"refTree","tree-data":r},null,8,["tree-data"])]),default:v(()=>[n(u,{class:"wrapper-content",title:"",overlay:""},{default:v(()=>[n(f,{ref_key:"refSearch",ref:_,config:L},null,8,["config"]),n(p,{class:"card-table",config:i},null,8,["config"])]),_:1}),n(y,{ref_key:"refDialog",ref:c,config:g},null,8,["config"])]),_:1})}}}),ht=q(G,[["__scopeId","data-v-bcc2df68"]]);export{ht as default};
