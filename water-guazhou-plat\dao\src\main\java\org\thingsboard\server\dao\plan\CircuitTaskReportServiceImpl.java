package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitTaskReport;
import org.thingsboard.server.dao.sql.smartManagement.plan.CircuitTaskReportMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportSaveRequest;

import java.util.List;

@Service
public class CircuitTaskReportServiceImpl implements CircuitTaskReportService {
    @Autowired
    private CircuitTaskReportMapper mapper;

    @Override
    public IPage<CircuitTaskReport> findAllConditional(CircuitTaskReportPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public CircuitTaskReport save(CircuitTaskReportSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    public boolean update(CircuitTaskReport entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<CircuitTaskReport> saveAll(List<CircuitTaskReportSaveRequest> entities) {
        return QueryUtil.saveBatchByRequest(entities, mapper::saveAll);
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public boolean present(CircuitTaskReportCompleteRequest req) {
        return mapper.present(req);
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public boolean fallback(CircuitTaskReportCompleteRequest req) {
        return mapper.fallback(req);
    }

    @Override
    public CircuitTaskReport findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public CircuitTaskReport findByPoint(String taskCode, String pointId, String tenantId) {
        return mapper.findByPoint(taskCode, pointId, tenantId);
    }

    @Override
    public String arrivalRate() {
        double rate = mapper.arrivalRate();
        String result = rate * 100 + "%";
        return result;
    }

    @Override
    public String feedbackRate() {
        double rate = mapper.feedbackRate();
        String result = rate * 100 + "%";
        return result;
    }

}
