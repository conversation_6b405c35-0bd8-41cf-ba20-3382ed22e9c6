import{_ as W}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as q}from"./CardTable-rdWOL4_6.js";import{_ as P}from"./CardSearch-CB_HNR-Q.js";import{d as D,M as V,c as p,r as f,s as o,a8 as B,S as g,o as F,g as $,n as E,q as h,i as b,ak as C,bq as y,al as M,bM as N}from"./index-r0dFAfgr.js";import{h as R,i as z,p as A}from"./process-DWVjEFpZ.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const G={class:"wrapper"},Y=D({__name:"index",setup(K){const{$messageError:_,$messageSuccess:v,$messageWarning:x}=V(),L=p(),I=p(),d=p(),u=p([]),S=f({filters:[{type:"input",label:"类型名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"新增",svgIcon:o(C),click:()=>{l.title="新增",k()}},{perm:!0,text:"删除",type:"danger",svgIcon:o(y),click:()=>{var e;if(console.log(a.selectList),(a.selectList||[]).length>0){const t=(e=a.selectList)==null?void 0:e.map(s=>s.id);T(t)}else x("请选择删除数据")}},{perm:!0,text:"查询",svgIcon:o(M),click:()=>{a.pagination.page=1,r()}}]}]}),a=f({loading:!0,dataList:[],selectList:[],indexVisible:!0,rowKey:"id",columns:[{prop:"code",label:"编号",minWidth:120},{prop:"name",label:"类型名称",minWidth:120},{prop:"type",label:"流程类型",formatter:(e,t)=>e.type==="app"?"移动端":"PC端"},{prop:"orderNum",label:"排序",minWidth:120},{prop:"remark",label:"备注",minWidth:120},{prop:"status",label:"生效",minWidth:120,iconStyle:{color:"#19a39e"},cellStyle:e=>({color:e.status?"#36a624":"#f56c6c"}),formatter:e=>e.status?"是":"否"}],operations:[{text:"编辑",isTextBtn:!1,perm:!0,svgIcon:o(N),click:e=>{l.title="编辑",k(e)}},{text:"删除",isTextBtn:!1,perm:!0,type:"danger",svgIcon:o(y),click:e=>T([e.id])}],operationFixed:"right",operationWidth:200,pagination:{refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,r()}},handleSelectChange:e=>{a.selectList=e}}),l=f({dialogWidth:1200,labelWidth:"140px",title:"新建",defaultValue:{status:!1},submit:void 0,group:[{fields:[{type:"input",label:"流程类型名称",field:"name",rules:[{required:!0,message:"请填写标题"}],placeholder:"请填写标题"},{type:"select",label:"流程类型",field:"type",rules:[{required:!0,message:"请选择流程类型"}],options:[{label:"移动端",value:"app"},{label:"PC端",value:"pc"}],placeholder:""},{type:"switch",label:"是否启用",field:"status",rules:[{required:!0,message:"请选择"}]},{type:"number",label:"排序",field:"orderNum"},{type:"textarea",label:"备注",field:"remark",placeholder:"请填写备注"},{type:"divider",text:"合同模板"},{type:"table",label:"",field:"contractTemplateList",config:{indexVisible:!0,height:200,dataList:B(()=>u.value),titleRight:[{items:[{type:"btn-group",btns:[{type:"primary",perm:!0,text:"",size:"small",svgIcon:o(C),styles:{padding:"5px"},iconStyles:{marginRight:0},click:()=>{u.value.push({name:"",file:"",remark:""})}}]}]}],columns:[{label:"模板名称",prop:"name",formItemConfig:{type:"input"}},{label:"合同",prop:"file",formItemConfig:{type:"file",returnType:"comma",limit:1}},{label:"备注",prop:"remark",formItemConfig:{type:"input"}}],pagination:{hide:!0},operations:[{perm:!0,text:"删除",type:"danger",svgIcon:o(y),click:e=>{console.log(e),g("确定删除？","提示信息").then(()=>{var i,n,c;const t=(i=l.group[0].fields)==null?void 0:i.find(m=>m.field==="configList");console.log((n=t.config)==null?void 0:n.dataList);const s=(c=t.config)==null?void 0:c.dataList;t.config.dataList=s.filter(m=>m.id!==e.id)})}}]}}]}]}),k=e=>{var t;l.defaultValue={...e||{status:!1}},u.value=e&&e.contractTemplateList?e.contractTemplateList:[],l.submit=s=>{g("确定提交？","提示信息").then(()=>{l.submitting=!0,s={...s,id:e?e.id:null},R(s).then(()=>{var i;(i=d.value)==null||i.closeDialog(),l.submitting=!1,v("保存成功"),r()}).catch(i=>{_(i),l.submitting=!1})})},(t=d.value)==null||t.openDialog()},T=e=>{g("确定删除？","提示信息").then(()=>{z(e).then(()=>{v("删除成功"),r()}).catch(t=>{x(t)})})},r=async()=>{var s;a.loading=!0;const t={...((s=L.value)==null?void 0:s.queryParams)||{},page:a.pagination.page||1,size:a.pagination.limit||20};A(t).then(async i=>{var c;const n=(c=i.data)==null?void 0:c.data;a.dataList=n.data,a.pagination.total=n.total}).catch(i=>{_(i)}),a.loading=!1};return F(()=>{r()}),(e,t)=>{const s=P,i=q,n=W;return $(),E("div",G,[h(s,{ref_key:"refSearch",ref:L,config:b(S)},null,8,["config"]),h(i,{ref_key:"refTable",ref:I,class:"card-table",config:b(a)},null,8,["config"]),h(n,{ref_key:"refForm",ref:d,config:b(l)},null,8,["config"])])}}});export{Y as default};
