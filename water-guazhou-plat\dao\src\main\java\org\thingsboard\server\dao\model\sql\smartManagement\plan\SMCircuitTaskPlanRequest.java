package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskSaveRequest;

import java.util.Date;
import java.util.List;

public class SMCircuitTaskPlanRequest extends SaveRequest<List<SMCircuitTaskSaveRequest>> {
    // 计划Id
    @Getter
    @Setter
    @NotNullOrEmpty
    private String planId;

    // 任务名称
    @Getter
    @Setter
    @NotNullOrEmpty
    private String name;

    // 接收人员Id
    @Getter
    @Setter
    @NotNullOrEmpty
    private String receiveUserId;

    // 开始时间
    @Getter
    @Setter
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    // 结束时间
    @Getter
    @Setter
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    // 共同完成人Id，多个用逗号隔开
    @Getter
    @Setter
    private String collaborateUserId;

    // 到位距离
    @Getter
    @Setter
    private String presentDistance;

    // 描述
    @Getter
    @Setter
    private String remark;

    private SMCircuitPlan plan;

    @Override
    protected boolean allowUpdate() {
        return false;
    }

    @Override
    protected List<SMCircuitTaskSaveRequest> build() {
        return null;
    }

    @Override
    protected List<SMCircuitTaskSaveRequest> update(String id) {
        return null;
    }

    public List<SMCircuitTaskSaveRequest> build(SMCircuitPlan plan) {
        this.plan = plan;
        return this.plan.planCircle().generate(beginTime, endTime, this::createTask);
    }

    private SMCircuitTaskSaveRequest createTask(Date from, Date to) {
        SMCircuitTaskSaveRequest entity = this.assimilation(new SMCircuitTaskSaveRequest());
        entity.setPlanId(plan.getId());
        entity.setDistrictAreaId(plan.getDistrictAreaId());
        entity.setIsNormalPlan(plan.getIsNormalPlan());
        entity.setIsNeedFeedback(plan.getIsNeedFeedback());
        entity.setMoveType(plan.getMoveType());
        // TODO: [LFT] 接入外部接口写入任务报告
        entity.setDevices(plan.getDevices());
        // entity.setSpecialDevices(plan.getDevices());
        entity.setName(name);
        entity.setReceiveUserId(receiveUserId);
        entity.setCollaborateUserId(collaborateUserId);
        entity.setPresentDistance(presentDistance);
        entity.setRemark(remark);
        entity.setCreator(currentUserUUID());
        entity.setBeginTime(from);
        entity.setEndTime(to);
        return entity;
    }

}
