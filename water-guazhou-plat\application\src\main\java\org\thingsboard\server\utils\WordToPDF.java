//package org.thingsboard.server.utils;
//
//import com.aspose.words.Document;
//import com.aspose.words.FontSettings;
//import com.aspose.words.License;
//import com.aspose.words.SaveFormat;
//
//import java.io.File;
//import java.io.FileOutputStream;
//import java.io.InputStream;
//
///**
// * word转pdf
// *
// * @<NAME_EMAIL>
// * @since v1.0.0 2021-02-07
// */
//public class WordToPDF {
//    /**
//     * 将doc格式文件转成html
//     *
//     * @param sourcerFile  doc文件路径
//     * @param targetFile doc文件中图片存储目录
//     * @return html
//     */
//    public static void WordToPdf(String sourcerFile,String targetFile) {
//        if (!getLicense()) {// 验证License 若不验证则转化出的pdf文档会有水印产生
//            return;
//        }
//        try {
//            long old = System.currentTimeMillis();
//            FontSettings.setFontsFolder("/usr/share/fonts/windows", false);
//            File file = new File(targetFile);  //新建一个空白pdf文档
//            FileOutputStream os = new FileOutputStream(file);
//            Document doc = new Document(sourcerFile);                    //sourcerFile是将要被转化的word文档
//            doc.save(os, SaveFormat.PDF);//全面支持DOC, DOCX, OOXML, RTF HTML, OpenDocument, PDF, EPUB, XPS, SWF 相互转换
//            os.close();
//            long now = System.currentTimeMillis();
//            System.out.println("共耗时：" + ((now - old) / 1000.0) + "秒");  //转化用时
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//    /**
//     * 判断是否有授权文件 如果没有则会认为是试用版，转换的文件会有水印
//     *@return
//     */
//    public static boolean getLicense() {
//        boolean result = false;
//        try {
//            InputStream is = WordToPDF.class.getClassLoader().getResourceAsStream("license.xml");
//            License aposeLic = new License();
//            aposeLic.setLicense(is);
//            result = true;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return result;
//    }
//}
