package org.thingsboard.server.dao.model.DTO;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.dao.model.sql.assay.AssayReportData;

import java.util.List;

/**
 * 呼叫来源报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-01-03
 */
@Data
@NoArgsConstructor
public class AssayReportDataDTO extends AssayReportData {

    private List<JSONObject> dataList;
}
