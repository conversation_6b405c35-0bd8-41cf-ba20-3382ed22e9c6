<!-- 管网采集 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'管网采集'"
    :hide-detail-close="true"
    :hide-right-drawer="true"
    :detail-max-min="true"
    @map-loaded="onMaploaded"
  >
    <template #detail-header>
      <span>采集任务管理</span>
    </template>
    <template #detail-default>
      <div v-show="state.curpage === 'detail'" class="page-wrapper">
        <div class="detail-header">
          <Icon :icon="'ep:back'" @click="handleBack"></Icon>
          <div class="detail-header-divider"></div>
          <span>任务详情</span>
        </div>
        <div class="detail-main">
          <el-tabs
            v-model="state.detailActionName"
            type="border-card"
            :class="useAppStore().isDark ? 'darkblue' : 'light'"
          >
            <el-tab-pane label="基础信息" name="basic">
              <div class="content-wrapper">
                <div class="left overlay-y">
                  <el-card class="card-t-0 darkblue">
                    <template #header>
                      <Icon :icon="'mdi:book-open-blank-variant'"></Icon>
                      <span> 关于任务 </span>
                    </template>
                    <ul class="list">
                      <li>
                        <span>编号</span>
                        <span>{{ state.detail.serialNo }}</span>
                      </li>
                      <li>
                        <span>名称：</span>
                        <span>{{ state.detail.title }}</span>
                      </li>
                      <li>
                        <span>描述：</span>
                        <span>{{ state.detail.source }}</span>
                      </li>
                      <li>
                        <span>是否超期：</span>
                        <span class="tag-blue">
                          {{ state.detail.overtime ? '是' : '否' }}
                        </span>
                      </li>
                      <li>
                        <span>当前状态：</span>
                        <span>{{
                          formatWorkOrderStatus(state.detail.status)
                        }}</span>
                      </li>
                    </ul>
                  </el-card>
                  <el-card class="darkblue">
                    <template #header>
                      <span
                        ><i class="iconfont icon-baogao"></i> 采集设备数量</span
                      >
                    </template>
                    <ul class="list">
                      <li>
                        <span>处理人：</span>
                        <Icon
                          v-if="state.detail.processUserId"
                          :icon="'ep:user'"
                        ></Icon>
                        <span>
                          {{ state.detail.processUserId }}
                        </span>
                      </li>
                      <li>
                        <span>处理级别：</span
                        ><span>{{
                          state.detail.processLevel &&
                          state.detail.processLevel + '级别'
                        }}</span>
                      </li>
                      <li>
                        <span>预计完成时间：</span
                        ><span>{{ state.detail.estimatedFinishTime }}</span>
                      </li>
                      <li>
                        <span>完成时间：</span
                        ><span>{{ state.detail.completeTime }}</span>
                      </li>
                    </ul>
                  </el-card>
                </div>

                <div class="right overlay-y">
                  <el-timeline>
                    <el-timeline-item
                      v-for="(item, i) in state.timeline"
                      :key="i"
                      :hollow="true"
                      type="success"
                      :hide-timestamp="true"
                      placement="top"
                    >
                      <div class="timeline-item-wrapper">
                        <div class="timeline-item-title">
                          <span class="title">{{ item.data.typeName }}</span>
                          <span class="time">{{ item.data.processTime }}</span>
                        </div>
                        <SLCard class="timeline-item-content">
                          <template v-if="item.type === 'text'">
                            <p v-if="item.data.type === 'ASSIGN'" class="text">
                              任务派发给了
                              <span class="text-name">{{
                                item.data?.nextProcessUserId
                              }}</span
                              >， 操作人：
                              <span class="text-name">{{
                                item.data?.processUserId
                              }}</span>
                            </p>
                            <p
                              v-if="item.data.type === 'RESOLVING'"
                              class="text"
                            >
                              {{ item.data?.nextProcessUserId }} 接收了工单
                            </p>
                          </template>

                          <AttrTable
                            v-if="item?.type === 'attr-table'"
                            :data="item.data"
                            :columns="item.columns"
                          ></AttrTable>
                        </SLCard>
                      </div>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="采集信息" name="collect">
              <div class="table-box">
                <FormTable :config="TableConfigCollect"></FormTable>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div v-show="state.curpage === 'table'" class="page-wrapper">
        <div class="tab-wrapper">
          <FormItem
            v-model="state.activeName"
            :config="state.tabConfig"
          ></FormItem>
        </div>
        <InlineForm ref="refForm" :config="SearchConfig"></InlineForm>
        <div class="table-box">
          <FormTable
            v-show="state.activeName === 'rtk'"
            :config="TableConfigRtk"
          ></FormTable>
          <FormTable
            v-show="state.activeName === 'dataImport'"
            :config="TableConfigDataImport"
          ></FormTable>
        </div>
      </div>
    </template>
  </RightDrawerMap>
  <DialogForm ref="refDialogFormTrans" :config="DialogConfigTrans"></DialogForm>
</template>
<script lang="ts" setup>
import { useAppStore } from '@/store';
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';
import { formatWorkOrderStatus } from '@/views/workorder/config';
import { Icon } from '@iconify/vue';

const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const state = reactive<{
  activeName: string;
  tabConfig: IFormItem;
  detailActionName: string;
  curpage: 'detail' | 'table';
  detail: Record<string, any>;
  timeline: {
    type: 'attr-table' | 'text';
    data: any;
    columns?: any[];
  }[];
}>({
  curpage: 'table',
  activeName: 'rtk',
  tabConfig: {
    type: 'tabs',
    tabs: [
      { label: 'RTK任务', value: 'rtk' },
      { label: '外业数据导入', value: 'dataImport' }
    ]
  },
  detailActionName: 'basic',
  detail: {},
  timeline: []
});
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
} = {};
const SearchConfig = reactive<IFormConfig>({
  labelWidth: 40,
  size: 'small',
  group: [
    {
      fields: [
        {
          type: 'radio-button',
          field: 'status',
          label: '状态',
          options: [
            { label: '全部', value: '' },
            { label: '待分派', value: 'PENDING' },
            { label: '待接单', value: 'ASSIGN' },
            { label: '处理中', value: 'RESOLVING' },
            { label: '待审核', value: 'SUBMIT' },
            { label: '审核通过', value: 'APPROVED' }
          ]
        },
        {
          labelWidth: 70,
          type: 'input',
          field: 'serialNo',
          label: '任务编号'
        },
        {
          type: 'input',
          field: 'title',
          label: '标题'
        },
        {
          labelWidth: 60,
          type: 'select',
          field: 'creator',
          label: '创建人',
          options: []
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              iconifyIcon: 'ep:search',
              click: () => refreshData()
            },
            {
              perm: true,
              text: '重置',
              type: 'default',
              iconifyIcon: 'ep:refresh',
              click: () => resetForm()
            },
            {
              perm: true,
              text: '新增',
              type: 'success',
              iconifyIcon: 'ep:plus',
              click: () => handleAddRtk()
            }
          ]
        }
      ]
    }
  ]
});
const resetForm = () => {
  //
};
const handleAddRtk = () => {
  //
};
const TableConfigRtk = reactive<ITable>({
  dataList: [{ code: 'aaa', isOverTime: true, status: 'ASSIGN' }],
  columns: [
    { minWidth: 120, label: '任务编号', prop: 'code' },
    { minWidth: 120, label: '任务名称', prop: 'title' },
    { minWidth: 120, label: '任务来源', prop: 'source' },
    { minWidth: 120, label: '探测类型', prop: 'type' },
    { minWidth: 120, label: '任务描述', prop: 'remark' },
    { minWidth: 120, label: '创建人', prop: 'organizerId' },
    { minWidth: 120, label: '创建时间', prop: 'createTime' },
    { minWidth: 120, label: '接收人', prop: 'processUserId' },
    {
      minWidth: 120,
      label: '是否超期',
      prop: 'isOverTime',
      tag: true,
      tagColor: (row, val) => (val === true ? '#ff0000' : '#00ff00'),
      formatter: (row, val) => (val === true ? '是' : '否')
    },
    {
      minWidth: 120,
      label: '状态',
      prop: 'status',
      tag: true,
      tagColor: '#409eff',
      formatter: (row, val) => formatWorkOrderStatus(val)
    }
  ],
  operations: [
    {
      perm: true,
      text: '详情',
      iconifyIcon: 'ep:info-filled',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      text: '转发',
      type: 'warning',
      iconifyIcon: 'ep:promotion',
      click: (row) => handleTrans(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfigRtk.pagination.page = page;
      TableConfigRtk.pagination.limit = size;
      refreshData();
    }
  }
});
const handleTrans = (row) => {
  console.log(row);

  refDialogFormTrans.value?.openDialog();
};

const TableConfigDataImport = reactive<ITable>({
  dataList: [],
  columns: [
    { minWidth: 120, label: '任务编号', prop: 'code' },
    { minWidth: 120, label: '任务名称', prop: 'title' },
    { minWidth: 120, label: '任务描述', prop: 'remark' },
    { minWidth: 120, label: '创建人', prop: 'organizerId' },
    { minWidth: 120, label: '创建时间', prop: 'createTime' },
    { minWidth: 120, label: '状态', prop: 'status' }
  ],
  operations: [
    {
      perm: true,
      text: '详情',
      iconifyIcon: 'ep:info-filled',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      text: '删除',
      type: 'danger',
      iconifyIcon: 'ep:delete',
      click: (row) => handleTrans(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfigRtk.pagination.page = page;
      TableConfigRtk.pagination.limit = size;
      refreshData();
    }
  }
});

const handleDetail = (row) => {
  state.curpage = 'detail';
  console.log(row);
};
const handleBack = () => {
  state.curpage = 'table';
};
const refreshData = () => {
  //
};
const TableConfigCollect = reactive<ITable>({
  dataList: [],
  columns: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfigCollect.pagination.page = page;
      TableConfigCollect.pagination.limit = size;
      refreshCollectData();
    }
  }
});
const refreshCollectData = () => {
  //
};

const refDialogFormTrans = ref<IDialogFormIns>();
const DialogConfigTrans = reactive<IDialogFormConfig>({
  dialogWidth: 450,
  title: '转发',
  labelPosition: 'left',
  group: [
    {
      fields: [
        { type: 'select', field: 'receiver', label: '接收人', options: [] },
        { type: 'textarea', field: 'remark', label: '备注' }
      ]
    }
  ],
  submit: (params: any) => {
    console.log(params);
  }
});
const onMaploaded = () => {
  refMap.value?.toggleCustomDetail();
  refMap.value?.toggleCustomDetailMaxmin('max');
};
onBeforeUnmount(() => {
  staticState.graphicsLayer?.removeAll();
});
</script>
<style lang="scss" scoped>
.page-wrapper {
  width: 100%;
  height: 100%;
}
.tab-wrapper {
  padding-bottom: 12px;
}
.table-box {
  height: calc(100% - 105px);
}
.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .el-icon {
    cursor: pointer;
  }
  .detail-header-divider {
    width: 1px;
    height: 1em;
    border: none;
    background-color: var(--el-border-color);
    margin: 0 20px;
  }
}
.detail-main {
  width: 100%;
  height: calc(100% - 32px);
  :deep(.el-tabs) {
    height: 100%;
    .el-tabs__content {
      height: calc(100% - 40px);
      .el-tab-pane {
        height: 100%;
      }
    }
  }
  .table-box {
    height: 100%;
  }
}
.content-wrapper {
  width: 100%;
  height: 100%;
  display: flex;

  .left {
    width: 400px;
    padding: 8px;

    .el-card {
      margin-top: 20px;

      &.card-t-0 {
        margin-top: 0;
      }
    }
  }

  .right {
    width: calc(100% - 400px);
    padding: 8px;
  }
}

.list {
  list-style: none;

  li {
    font-size: 12px;
    padding: 8px 5px;
    line-height: 1.5em;
  }
}

.timeline-item-wrapper {
  padding-bottom: 8px;

  .timeline-item-title {
    margin-bottom: 12px;

    .title {
      color: #00aa00;
      font-weight: bold;
      font-size: 14px;
      margin-right: 8px;
    }
  }

  .timeline-item-content {
    padding: 20px;
  }
}

.voicer,
.videor {
  margin: 5px 0;
  margin-right: 8px;

  &:last-child {
    margin-right: 0;
  }
}

.voice-url {
  text-decoration: underline;
  color: cadetblue;
  cursor: pointer;
  text-overflow: ellipsis;
  overflow: hidden;
}

.label {
  color: cadetblue;
  font-size: 12px;
}

.text {
  color: #333;
  font-size: 12px;

  .text-name {
    color: #333;
  }
}
</style>
