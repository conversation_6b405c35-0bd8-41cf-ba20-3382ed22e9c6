/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import com.alibaba.druid.util.Base64;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.rule.engine.api.MailService;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.security.UserCredentials;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.security.auth.jwt.RefreshTokenRepository;
import org.thingsboard.server.service.security.model.SecurityUser;
import org.thingsboard.server.service.security.model.UserPrincipal;
import org.thingsboard.server.service.security.model.token.JwtToken;
import org.thingsboard.server.service.security.model.token.JwtTokenFactory;
import org.thingsboard.server.service.utils.Aes;
import org.thingsboard.server.service.utils.VerifyUtil;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api")
@Slf4j
public class AuthController extends BaseController {


    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Autowired
    private JwtTokenFactory tokenFactory;

    @Autowired
    private RefreshTokenRepository refreshTokenRepository;

    @Autowired
    private MailService mailService;

    @Value("${hostname.api}")
    private String apiUrl;

    @PreAuthorize("isAuthenticated()")
    @RequestMapping(value = "/auth/user", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_AUTH_USER_GET)
    public @ResponseBody
    User getUser() throws ThingsboardException {
        try {
            SecurityUser securityUser = getCurrentUser();
            return userService.findUserById(getTenantId(), securityUser.getId());
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("isAuthenticated()")
    @RequestMapping(value = "/auth/changePassword", method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.OK)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_CHANGE_PASSWORD)
    public void changePassword(
            @RequestBody JsonNode changePasswordRequest) throws ThingsboardException {
        try {
            String currentPassword = changePasswordRequest.get("currentPassword").asText();
            String newPassword = changePasswordRequest.get("newPassword").asText();
            SecurityUser securityUser = getCurrentUser();
            UserCredentials userCredentials = userService.findUserCredentialsByUserId(getTenantId(), securityUser.getId());
            if (!passwordEncoder.matches(currentPassword, userCredentials.getPassword())) {
                throw new ThingsboardException("输入的旧密码不正确!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            /*boolean b = UserController.checkPassword(newPassword);
            if (!b) {
                throw new ThingsboardException("密码强度过低, 请输入8到16位大小写字母加特殊字符的组合!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }*/
            userCredentials.setPassword(passwordEncoder.encode(newPassword));
            userService.saveUserCredentials(getTenantId(), userCredentials);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @RequestMapping(value = "/auth/resetUserPassword/{userId}", method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.OK)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_CHANGE_PASSWORD)
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE')")
    public void resetUserPassword(@PathVariable String userId) throws ThingsboardException {
        try {
            String newPassword = "123456";
            SecurityUser securityUser = getCurrentUser();
            UserCredentials userCredentials = userService.findUserCredentialsByUserId(getTenantId(), new UserId(UUIDConverter.fromString(userId)));
            /*boolean b = UserController.checkPassword(newPassword);
            if (!b) {
                throw new ThingsboardException("密码强度过低, 请输入8到16位大小写字母加特殊字符的组合!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }*/
            userCredentials.setPassword(passwordEncoder.encode(newPassword));
            userService.saveUserCredentials(getTenantId(), userCredentials);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 生成验证码的接口
     *
     * @param response Response对象
     * @param request  Request对象
     * @throws Exception
     */
    @PostMapping("/noauth/verify")
    public Object getCode(HttpServletResponse response, HttpServletRequest request) throws Exception {
        // 获取到session
        HttpSession session = request.getSession();
        // 取到sessionid
        String id = session.getId();

        // 利用图片工具生成图片
        // 返回的数组第一个参数是生成的验证码，第二个参数是生成的图片
        Object[] objs = VerifyUtil.newBuilder()
                .setWidth(135)   //设置图片的宽度
                .setHeight(50)   //设置图片的高度
                .setSize(5)      //设置字符的个数
                .setLines(10)    //设置干扰线的条数
                .setFontSize(25) //设置字体的大小
                .setTilt(true)   //设置是否需要倾斜
                .setBackgroundColor(Color.LIGHT_GRAY) //设置验证码的背景颜色
                .build()         //构建VerifyUtil项目
                .createImage();  //生成图片
        // 打印验证码
        System.out.println(objs[0]);

        // 将图片输出给浏览器
        BufferedImage image = (BufferedImage) objs[1];
        response.setContentType("image/png");
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(image, "png", os);

        JSONObject result = new JSONObject();
        String requestId = UUID.randomUUID().toString();
        result.put("request", requestId);
        result.put("data", Base64.byteArrayToBase64(os.toByteArray()));

        DataConstants.VERIFY_CODE_MAP.put(requestId, (String) objs[0]);

        return result;
    }

    @GetMapping("/noauth/getLoginKey")
    public Object getLoginKey() {
        JSONObject result = new JSONObject();
        result.put("key", Aes.getKEY());

        return result;
    }

    @RequestMapping(value = "/noauth/activate", params = {"activateToken"}, method = RequestMethod.GET)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_CHECK_ACTIVE_CODE)
    public ResponseEntity<String> checkActivateToken(
            @RequestParam(value = "activateToken") String activateToken, @RequestParam(value = "istarCreatePasswordUrl", required = false) String istarCreatePasswordUrl) throws ThingsboardException {
        HttpHeaders headers = new HttpHeaders();
        HttpStatus responseStatus;
        UserCredentials userCredentials = userService.findUserCredentialsByActivateToken(null, activateToken);
        if (userCredentials != null) {
            String createURI = "/login/createPassword";
            try {
                URI location = new URI(createURI + "?activateToken=" + activateToken);

                //qing添加,如果传上来的url,就按传上来的url执行,如果没有按照thingsboard执行
                if (!StringUtils.isEmpty(istarCreatePasswordUrl) && istarCreatePasswordUrl.contains("http"))
                    headers.setLocation(new URI(URLDecoder.decode(istarCreatePasswordUrl) + "?activateToken=" + activateToken));
                else
                    headers.setLocation(location);

                responseStatus = HttpStatus.SEE_OTHER;
            } catch (URISyntaxException e) {
                log.error("Unable to create URI with address [{}]", createURI);
                responseStatus = HttpStatus.BAD_REQUEST;
            }
        } else {
            responseStatus = HttpStatus.CONFLICT;
        }
        return new ResponseEntity<>(headers, responseStatus);
    }

    @RequestMapping(value = "/noauth/resetPasswordByEmail", method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.OK)
    public void requestResetPasswordByEmail(
            @RequestBody JsonNode resetPasswordByEmailRequest,
            HttpServletRequest request, @RequestParam(value = "istarResetPasswordUrl", required = false) String istarResetPasswordUrl) throws ThingsboardException {
        try {
            String email = resetPasswordByEmailRequest.get("email").asText();
            UserCredentials userCredentials = userService.requestPasswordReset(null, email);
            String baseUrl = constructBaseUrl(request);

            String resetUrl = null;
            //qing添加,如果传上来的url,就按传上来的url执行,如果没有按照thingsboard执行
            if (!StringUtils.isEmpty(istarResetPasswordUrl) && istarResetPasswordUrl.contains("http"))
                resetUrl = apiUrl + "/api/noauth/resetPassword?resetToken=" + userCredentials.getResetToken() + "&istarResetPasswordUrl=" + URLEncoder.encode(istarResetPasswordUrl);
            else
                resetUrl = String.format("%s/api/noauth/resetPassword?resetToken=%s", baseUrl, userCredentials.getResetToken());

            mailService.sendResetPasswordEmail(resetUrl, email);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @RequestMapping(value = "/noauth/resetPassword", params = {"resetToken"}, method = RequestMethod.GET)
    public ResponseEntity<String> checkResetToken(
            @RequestParam(value = "resetToken") String resetToken, @RequestParam(value = "istarResetPasswordUrl", required = false) String istarResetPasswordUrl) throws ThingsboardException {
        HttpHeaders headers = new HttpHeaders();
        HttpStatus responseStatus;
        String resetURI = "/login/resetPassword";
        UserCredentials userCredentials = userService.findUserCredentialsByResetToken(null, resetToken);
        if (userCredentials != null) {
            try {
                URI location = new URI(resetURI + "?resetToken=" + resetToken);
                //qing添加,如果传上来的url,就按传上来的url执行,如果没有按照thingsboard执行
                if (!StringUtils.isEmpty(istarResetPasswordUrl) && istarResetPasswordUrl.contains("http"))
                    headers.setLocation(new URI(URLDecoder.decode(istarResetPasswordUrl) + "?resetToken=" + resetToken));
                else
                    headers.setLocation(location);
                responseStatus = HttpStatus.SEE_OTHER;
            } catch (URISyntaxException e) {
                log.error("Unable to create URI with address [{}]", resetURI);
                responseStatus = HttpStatus.BAD_REQUEST;
            }
        } else {
            responseStatus = HttpStatus.CONFLICT;
        }
        return new ResponseEntity<>(headers, responseStatus);
    }

    @RequestMapping(value = "/noauth/activate", method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_ACTIVE)
    public JsonNode activateUser(
            @RequestBody JsonNode activateRequest,
            HttpServletRequest request, @RequestParam(value = "istarLoginUrl", required = false) String istarLoginUrl) throws ThingsboardException {
        try {
            String activateToken = activateRequest.get("activateToken").asText();
            String password = activateRequest.get("password").asText();
            String encodedPassword = passwordEncoder.encode(password);
            UserCredentials credentials = userService.activateUserCredentials(null, activateToken, encodedPassword);
            User user = userService.findUserById(null, credentials.getUserId());
            UserPrincipal principal = new UserPrincipal(UserPrincipal.Type.USER_NAME, user.getEmail());
            SecurityUser securityUser = new SecurityUser(user, credentials.isEnabled(), principal);
            String baseUrl = constructBaseUrl(request);

            String loginUrl = null;


            //qing添加,如果传上来的url,就按传上来的url执行,如果没有按照thingsboard执行
            if (!StringUtils.isEmpty(istarLoginUrl) && istarLoginUrl.contains("http"))
                loginUrl = URLDecoder.decode(istarLoginUrl);
            else
                loginUrl = String.format("%s/login", baseUrl);

            String email = user.getEmail();

            try {
                mailService.sendAccountActivatedEmail(loginUrl, email);
            } catch (Exception e) {
                log.info("Unable to send account activation email [{}]", e.getMessage());
            }

            JwtToken accessToken = tokenFactory.createAccessJwtToken(securityUser);
            JwtToken refreshToken = refreshTokenRepository.requestRefreshToken(securityUser);

            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode tokenObject = objectMapper.createObjectNode();
            tokenObject.put("token", accessToken.getToken());
            tokenObject.put("refreshToken", refreshToken.getToken());
            return tokenObject;
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @RequestMapping(value = "/noauth/resetPassword", method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public JsonNode resetPassword(
            @RequestBody JsonNode resetPasswordRequest,
            HttpServletRequest request, @RequestParam(value = "istarLoginUrl", required = false) String istarLoginUrl) throws ThingsboardException {
        try {
            String resetToken = resetPasswordRequest.get("resetToken").asText();
            String password = resetPasswordRequest.get("password").asText();
            UserCredentials userCredentials = userService.findUserCredentialsByResetToken(null, resetToken);
            if (userCredentials != null) {
                String encodedPassword = passwordEncoder.encode(password);
                userCredentials.setPassword(encodedPassword);
                userCredentials.setResetToken(null);
                userCredentials = userService.saveUserCredentials(null, userCredentials);
                User user = userService.findUserById(null, userCredentials.getUserId());
                UserPrincipal principal = new UserPrincipal(UserPrincipal.Type.USER_NAME, user.getEmail());
                SecurityUser securityUser = new SecurityUser(user, userCredentials.isEnabled(), principal);
                String baseUrl = constructBaseUrl(request);
                String loginUrl = String.format("%s/login", baseUrl);
                String email = user.getEmail();

                //qing添加,如果传上来的url,就按传上来的url执行,如果没有按照thingsboard执行
                if (!StringUtils.isEmpty(istarLoginUrl) && istarLoginUrl.contains("http"))
                    mailService.sendPasswordWasResetEmail(URLDecoder.decode(istarLoginUrl), email);

                else
                    mailService.sendPasswordWasResetEmail(loginUrl, email);

                JwtToken accessToken = tokenFactory.createAccessJwtToken(securityUser);
                JwtToken refreshToken = refreshTokenRepository.requestRefreshToken(securityUser);

                ObjectMapper objectMapper = new ObjectMapper();
                ObjectNode tokenObject = objectMapper.createObjectNode();
                tokenObject.put("token", accessToken.getToken());
                tokenObject.put("refreshToken", refreshToken.getToken());
                return tokenObject;
            } else {
                throw new ThingsboardException("Invalid reset token!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @GetMapping(value = "/auth/root/token")
    @ResponseStatus(value = HttpStatus.OK)
    public String requestResetPasswordByEmail(@Param("tenantId") String tenantId) throws ThingsboardException {
        if (getUser().isSystemAdmin()) {
            TenantId tenant = new TenantId(UUIDConverter.fromString(tenantId));
            User user = getCurrentUser();
            user.setTenantId(tenant);
            UserPrincipal principal = new UserPrincipal(UserPrincipal.Type.USER_NAME, user.getEmail());
            SecurityUser securityUser = new SecurityUser(user, true, principal);
            JwtToken accessToken = tokenFactory.createAccessJwtToken(securityUser);
            return accessToken.getToken();
        }else {
            return ("权限异常！");
        }
    }


//
//    @GetMapping(value = "/auth/sendSms")
//    @ResponseStatus(value = HttpStatus.OK)
//    public String sendsms(@Param("tenantId") String tenantId) throws ThingsboardException {
//        AlarmReport alarmReport = new AlarmReport();
//        alarmReport.setTime(DateUtils.date2Str(System.currentTimeMillis(), DateUtils.DATE_FORMATE_DEFAULT_2));
//        alarmReport.setAlarmValue("25");
//        alarmReport.setSetValue("22");
//        alarmReport.setProp("A相电压");
//        alarmReport.setDeviceName("1#变压器");
//        mailService.sendSMS(getCurrentUser(),alarmReport);
//        return "true";
//    }

}
