<template>
  <div ref="refContainer" :style="computedStyle">
    <VChart ref="refChart" :option="option"></VChart>
  </div>
</template>
<script lang="ts" setup>
import { useDetector } from '@/hooks/echarts';
import { IECharts } from '@/plugins/echart';
import { useAppStore } from '@/store';
import { transNumberUnit } from '@/utils/GlobalHelper';

const appStore = useAppStore();
const refContainer = ref<HTMLDivElement>();
const refChart = ref<IECharts>();
const props = defineProps<{
  width?: number;
  height?: number;
  data: { name: string; value: any }[];
  unit?: string;
  prefix?: string;
  percision?: number;
}>();
const option = ref<any>();
const computedStyle = computed(() => {
  return {
    width: props.width ? props.width + 'px' : '100%',
    height: props.height ? props.height + 'px' : '100%'
  };
});
const detector = useDetector();
watch(
  () => props.data,
  () => {
    refreshChart();
  },
  { deep: true }
);
const formatNumber = function (num) {
  const reg = /(?=(\B)(\d{3})+$)/g;
  return num.toString().replace(reg, ',');
};
const refreshChart = () => {
  const title = '合计';
  const data = props.data || [];
  const total = data.reduce((a, b: any) => {
    return a + (parseFloat(b.value) || 0) * 1;
  }, 0);
  const transedTotal = transNumberUnit(total);
  option.value = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return (
          (props.prefix || '') +
          params.name +
          ': ' +
          Number(params.value).toFixed(props.percision ?? 0) +
          ' ' +
          props.unit
        );
      }
    },
    legend: {
      // selectedMode: false, // 取消图例上的点击事件
      // type: 'scroll',
      icon: 'circle',
      orient: 'horizontal',
      bottom: 10,
      left: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      symbolKeepAspect: true,
      textStyle: {
        color: '#B8D2FF'
      },
      data: data.map((item) => item.name)
    },
    title: [
      {
        text:
          '{name|' +
          title +
          (props.unit
            ? '(' + transedTotal.unit + props.unit + ')'
            : transedTotal.unit
              ? '(' + transedTotal.unit + ')'
              : '') +
          '}\n{val|' +
          formatNumber(transedTotal.value.toFixed(props.percision ?? 0)) +
          '}',
        top: '33%',
        left: '50%',
        textAlign: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 10,
              fontWeight: 'normal',
              padding: [8, 0],
              align: 'center',
              color: appStore.isDark ? '#fff' : '#2A2A2A'
            },
            val: {
              fontSize: 16,
              fontWeight: 'bold',
              color: appStore.isDark ? '#fff' : '#2A2A2A'
            }
          }
        }
      }
    ],
    series: [
      {
        type: 'pie',
        radius: ['35%', '50%'],
        center: ['50%', '40%'],
        data,
        hoverAnimation: true,
        label: {
          // show: false,
          // formatter: params => {
          //   return (
          //     '{icon|●}{name|'
          //     + params.name
          //     + '}{value|'
          //     + formatNumber(Number(params.value || '0').toFixed(props.percision ?? 0))
          //     + '}'
          //   )
          // },
          // padding: [0, -100, 25, -100],
          // rich: {
          //   icon: {
          //     fontSize: 16
          //   },
          //   name: {
          //     fontSize: 14,
          //     padding: [0, 10, 0, 4]
          //   },
          //   value: {
          //     fontSize: 18,
          //     fontWeight: 'bold'
          //   }
          // }
        }
      }
    ]
  };
};

onMounted(() => {
  refreshChart();
  detector.listenTo(refContainer.value, refChart);
});
</script>
<style lang="scss" scoped></style>
