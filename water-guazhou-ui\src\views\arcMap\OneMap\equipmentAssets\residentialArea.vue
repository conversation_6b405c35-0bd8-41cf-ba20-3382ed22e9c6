<!-- gis居民区 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards v-model="cardsvalue"></Cards>

    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
    <!-- 底部弹窗 -->
    <OpenDialog
      ref="refDetail"
      :config="config"
      @close="() => {}"
    >
      <template #right>
        <div style="width: 100%; height: 100%; display: flex">
          <div style="width: 50%; height: 100%"></div>
          <div style="width: 100%; height: 100%">
            <VChart
              ref="refChart2"
              autoresize
              theme="dark"
              :option="option2"
            ></VChart>
          </div>
        </div>
      </template>
    </OpenDialog>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components'
import { cylinder, ring } from '../../components/components/chart'
import { IFormIns } from '@/components/type'
import { IECharts } from '@/plugins/echart'
import OpenDialog from '../../components/components/openDialog.vue'

defineProps<{
  view?: __esri.MapView
}>()
const refForm = ref<IFormIns>()

const refDetail = ref<InstanceType<typeof OpenDialog>>()

const cardsvalue = ref([
  { label: '91个', value: '居民区总数' },
  { label: '0户', value: '用户总数' },
  { label: '98%', value: '抄表率' }
])

const refChart2 = ref<IECharts>()
const option2 = cylinder()

const config = reactive({
  tabs: {
    filters: [
      {
        type: 'tabs',
        field: 'type',
        tabs: [{ label: 'cs', value: 'cs' }],
        onChange: () => {
          //
        }
      }
    ]
  }
})
const TableConfig = reactive<ITable>({
  dataList: [],
  pagination: {
    hide: true
  },
  columns: [
    {
      minWidth: 120,
      label: '小区名称',
      prop: 'key1',
      sortable: true
    },
    {
      minWidth: 120,
      label: '用户数',
      prop: 'key3'
    },
    {
      minWidth: 120,
      label: '抄表类型',
      prop: 'key3'
    }
  ],
  handleRowClick: row => {
    TableConfig.currentRow = row
    // refDetail.value?.openDialog()
  }
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        type: 'underline',
        desc: '小区规模占比图'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          },
          itemContainerStyle: {
            marginBottom: 0
          }
        }
      ]
    },
    {
      fields: [
        {
          type: 'input',
          field: 'layer',
          append: '刷新'
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const refreshData = () => {
  //
}

onMounted(() => {
  refreshData()
  window.addEventListener('resize', resizeChart)
})

function resizeChart() {
  refChart2.value?.resize()
}
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  height: calc(100% - 315px);
}
</style>
