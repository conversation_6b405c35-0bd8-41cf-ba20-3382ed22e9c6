<!-- 多媒体上传 -->
<template>
  <DrawerBox
    ref="refDrawerBox"
    :right-drawer="true"
    :right-drawer-title="'多媒体上传'"
  >
    <template #right>
      <Form
        ref="refForm"
        :config="FormConfig"
      ></Form>
      <ArcSqlGenerator
        ref="refSql"
        :layer-name="state.panelTitle"
        @submit="handleSqlGenerated"
      ></ArcSqlGenerator>
    </template>
    <ArcLayout
      ref="refArcLayout"
      :panel-telport="''"
      :panel-title="'多媒体上传查询结果' + (state.panelTitle ? '-' + state.panelTitle : '')"
      @map-loaded="onMapLoaded"
    >
      <ArcDraw
        ref="refArcDraw"
        :layerid="'query-area'"
        :layername="'查询范围'"
      ></ArcDraw>
      <template #detail-default>
        <div class="detail-wrapper">
          <DetailTable
            ref="refDetailTable"
            class="pipe-list"
            :operations="[{ perm: true, text: '多媒体上传', click: row => handleUpload(row) }]"
            @refresh-data="startSearch"
            @row-click="handleRowClick"
          ></DetailTable>
          <PipeMedias
            ref="refPipeMedias"
            class="media-tabs"
            :objectid="state.currentRow?.OBJECTID"
            :layerid="refForm?.dataForm.layerid?.[0]"
            :layername="state.panelTitle"
          ></PipeMedias>
        </div>
      </template>
    </ArcLayout>
    <UploadForm
      ref="refUploadForm"
      :objectid="state.currentRow?.OBJECTID"
      :layerid="refForm?.dataForm.layerid?.[0]"
      :layername="state.panelTitle"
      :geo="getFeatureGeometry(state.currentRow)"
      @uploaded="handleUploaded"
    ></UploadForm>
  </DrawerBox>
</template>
<script lang="ts" setup>
import { SLMessage } from '@/utils/Message'
import { OCalcOperator, OGisConditionFields, OGisConditionLogic } from '@/utils/arcgis/utils/SqlHelper'
import DetailTable from '@/views/arcMap/components/common/DetailTable.vue'
import PipeMedias from './PipeMedias.vue'
import UploadForm from './UploadForm.vue'
import { QueryByPolygon } from '@/utils/geoserver/wfsUtils'

const refPipeMedias = ref<InstanceType<typeof PipeMedias>>()
const refUploadForm = ref<InstanceType<typeof UploadForm>>()
const refDetailTable = ref<InstanceType<typeof DetailTable>>()

const refDrawerBox = ref<IDrawerBoxIns>()
const refArcDraw = ref<IArcDrawIns>()
const refSql = ref<IArcSqlGeneratorIns>()
const refForm = ref<IFormIns>()
const refArcLayout = ref<IArcLayoutIns>()

// 定义静态状态对象存储MapView实例
const staticState: {
  view?: __esri.MapView
} = {}

// 提供PipeMedias组件引用给其他组件使用
provide('pipeMediasRef', refPipeMedias);

const state = reactive<{
  tabs: any[]
  curOperate: string
  curFieldNode?: any
  panelTitle: string
  currentRow?: any
  geoServerLayers?: any[]
}>({
  tabs: [],
  curOperate: '',
  panelTitle: '',
  currentRow: undefined,
  geoServerLayers: []
})

const TableConfig = reactive<ITable>({
  dataList: [],
  height: 170,
  columns: [
    {
      minWidth: 60,
      label: '逻辑',
      prop: 'logic',
      formatter(row, value) {
        return OGisConditionLogic[value]
      }
    },
    {
      minWidth: 100,
      label: '字段',
      prop: 'field',
      formatter(row, value) {
        return OGisConditionFields[value]
      }
    },
    {
      minWidth: 70,
      label: '运行符',
      prop: 'calc',
      formatter(row, value) {
        return OCalcOperator[value]
      }
    },
    {
      minWidth: 80,
      label: '值',
      prop: 'value'
    }
  ],
  pagination: {
    hide: true
  }
})

const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => refArcDraw.value?.initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              iconifyIcon: 'ep:crop',
              click: () => refArcDraw.value?.initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => refArcDraw.value?.initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              iconifyIcon: 'ep:delete',
              click: () => refArcDraw.value?.clear()
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: computed(() => {
            // 如果静态状态中已有图层信息，则使用它
            if (state.geoServerLayers && state.geoServerLayers.length > 0) {
              return state.geoServerLayers;
            }
            return [];
          }) as unknown as any,
          checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value',
          handleCheckChange: (data, isChecked) => {
            if (isChecked) {
              state.panelTitle = data.label
              refForm.value && (refForm.value.dataForm.layerid = [data.value])
            }
          }
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '属性过滤'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: { width: '100%' },
              text: '添加条件',
              iconifyIcon: 'ep:plus',
              click: () => {
                refSql.value?.openDialog()
              }
            },
            {
              perm: true,
              text: '清除条件',
              type: 'danger',
              iconifyIcon: 'ep:delete',
              disabled: () => state.curOperate === 'detailing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            }
          ]
        },
        {
          type: 'table',
          config: TableConfig
        }
      ]
    },
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: () => (state.curOperate === 'detailing' ? '正在查询' : '查询'),
              disabled: () => state.curOperate === 'detailing',
              loading: () => state.curOperate === 'detailing',
              click: () => startSearch(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})

const handleUpload = row => {
  debugger
  state.currentRow = row
  refUploadForm.value?.openDialog()
}

const handleUploaded = () => {
  refPipeMedias.value?.refreshData()
}

const handleRowClick = (row) => {
  state.currentRow = row
  refPipeMedias.value?.refreshData()
}

const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '')
  TableConfig.dataList = []
  refSql.value?.clear()
}

const handleSqlGenerated = params => {
  console.log(params)
  TableConfig.dataList = params.list
  refForm.value?.dataForm && (refForm.value.dataForm.sql = params.sql)
}

const startSearch = async () => {
  try {
    state.tabs.length = 0
    state.curOperate = 'detailing'
    const layerIds = refForm.value?.dataForm.layerid
    if (!layerIds?.length) {
      SLMessage.warning('请选择图层')
    } else {
      refArcLayout.value?.refPanel?.Toggle(true)
      try {
        // 获取SQL条件
        const where = refForm.value?.dataForm?.sql || null;
        
        // 获取绘制的多边形坐标
        const graphics = refArcDraw.value?.getGraphics();
        
        // 检查是否有绘制的图形
        let polygonCoordinates = null;
        if (graphics && graphics.length > 0) {
          const graphic = graphics[0];
          
          // 处理不同类型的几何图形
          if (graphic.geometry.type === 'polygon') {
            // 获取多边形的环坐标
            polygonCoordinates = (graphic.geometry as any).rings[0];
          } else if (graphic.geometry.hasOwnProperty('extent')) {
            // 如果是矩形(extent)，转换为多边形坐标
            const extent = (graphic.geometry as any).extent;
            const { xmin, ymin, xmax, ymax } = extent;
            polygonCoordinates = [
              [xmin, ymin],
              [xmax, ymin],
              [xmax, ymax],
              [xmin, ymax],
              [xmin, ymin] // 闭合多边形
            ];
          }
        }
        
        // 获取图层名称 - 确保不为空
        const layerName = state.panelTitle; // 使用选中的图层名称
        if (!layerName) {
          SLMessage.error('图层名称不能为空');
          state.curOperate = '';
          return;
        }
        // 使用QueryByPolygon进行查询
        const response = await QueryByPolygon(layerName, polygonCoordinates, where);
        
        // 处理查询结果
        if (response && response.data && response.data.features) {
          const features = response.data.features;
          
          if (features.length === 0) {
            SLMessage.warning('未查询到数据');
            state.curOperate = '';
            return;
          }
          
          await nextTick();
          // 更新DetailTable
          refDetailTable.value?.refreshDetail(
            staticState.view,
            {
              layerid: layerIds[0],
              layername: layerName,
              oids: features
            },
          );
          state.tabs = features;
        } else {
          SLMessage.warning('查询结果为空');
        }
      } catch (error: any) {
        console.error('GeoServer查询失败:', error);
        SLMessage.error('查询失败: ' + (error.message || '未知错误'));
      }

    }
  } catch (error: any) {
    SLMessage.error(error.message)
  }
  state.curOperate = ''
}

const getLayerInfo = () => {
  try {
    // 使用类型断言解决类型错误
    const layerInfo = (staticState.view?.layerViews as any)?.items?.[1]?.layer?.sublayers;

    if (layerInfo && layerInfo.items) {
      let layers = layerInfo.items.map((item: any) => {
        return {
          label: item.name,
          value: item.name,
          // data: item
        }
      });
      
      // 保存GeoServer图层信息到state中以便在选项计算属性中使用
      state.geoServerLayers = layers;
      
    }
  } catch (error) {
    console.error('获取GeoServer图层信息失败:', error);
  }
}

const onMapLoaded = async (view: __esri.MapView) => {
  // 保存MapView到staticState
  staticState.view = view
  
  // 获取图层信息
  setTimeout(() => {
    getLayerInfo()
  }, 1000)
}

const getFeatureGeometry = (feature: any) => {
  if (!feature) return null;
  else  {
    let row = state.tabs.filter(m => m.properties.id === feature.id)[0];
    return row?.geometry
  }
}

onMounted(() => {
  refDrawerBox.value?.toggleDrawer('rtl', true)
})
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;
  .el-table__empty-text {
    line-height: 40px;
  }
}
.detail-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  .pipe-list {
    width: 60%;
  }
  .media-tabs {
    width: 40%;
  }
}
</style>
<style>
.sql-btns-wrapper,
.sql-list-wrapper {
  box-shadow: 0 0 0 1px var(--el-border-color);
}
</style>
