package org.thingsboard.server.dao.util.imodel.query.plan;

import org.thingsboard.server.dao.model.sql.plan.PlanTaskDetail;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PlanTaskDetailPageRequest extends AdvancedPageableQueryEntity<PlanTaskDetail, PlanTaskDetailPageRequest> {
    // 主表ID
    private String mainId;

    // 设备编码
    private String serialId;

    // 货架ID
    private String shelvesId;
}
