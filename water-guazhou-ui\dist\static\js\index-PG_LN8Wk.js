import{d as j,a6 as J,r as T,c as m,bF as O,s as E,j as g,bB as W,o as H,ay as U,g as Y,n as k,q as c,i,p as l,F as f,bh as X,bo as L,br as $,b7 as G,C as K}from"./index-r0dFAfgr.js";import{_ as Q}from"./index-C9hz-UZb.js";import{_ as Z}from"./CardTable-rdWOL4_6.js";import{_ as ee}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{_ as te}from"./CardSearch-CB_HNR-Q.js";import{l as M}from"./echart-DkOzaNjN.js";import{u as ae}from"./useStation-DJgnSZIA.js";import{g as oe,e as se}from"./waterIndicators-BJSzKLY_.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const ne={class:"wrapper"},re={class:"main"},ie={class:"left"},le={class:"table1"},ce={class:"table2"},pe={class:"right"},ue={class:"card-title"},de={class:"title"},fe=j({__name:"index",setup(me){const{getAllStationOption:R}=ae(),I=J(),n=T({queryType:"day",compareType:2,chartOption1:null,chartOption2:null,chartOption3:null,storedStationName:"",storedStationList:[]}),y=m(!1),h=m(),S=m(),C=m(),q=m(),b=m(),v=T({defaultParams:{queryType:"day",time:O()},filters:[{type:"select",label:"水厂",field:"stationId",options:[],clearable:!1,onChange:e=>{n.storedStationName=n.storedStationList.find(t=>t.id===e).label}},{type:"select",field:"queryType",width:"80px",clearable:!1,options:[{label:"日",value:"day"},{label:"月",value:"month"},{label:"年",value:"year"}],label:"分析类型"},{type:"date",label:"日期",field:"time",format:"YYYY-MM-DD",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.queryType==="month"||e.queryType==="year"}},{type:"month",label:"月份",field:"time",format:"YYYY-MM",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.queryType==="day"||e.queryType==="year"}},{type:"year",label:"年份",field:"time",format:"YYYY",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.queryType==="month"||e.queryType==="day"}},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>D(),icon:"iconfont icon-chaxun"},{perm:!0,type:"default",text:"重置",svgIcon:E(G),click:()=>{var e;(e=h.value)==null||e.resetForm()}},{text:"导出",perm:!0,type:"warning",icon:"iconfont icon-xiazai",click:()=>P()}]}]}),z=m({defaultValue:{compareType:2},group:[{fields:[{type:"radio-button",field:"compareType",options:[{label:"环比",value:2},{label:"同比",value:1},{label:"定基比",value:3}],label:"",onChange:e=>{n.compareType=e,D()}}]}]}),_=T({loading:!0,dataList:[],highlightCurrentRow:!0,columns:[],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),x=T({loading:!0,dataList:[],highlightCurrentRow:!0,columns:[{prop:"ts",label:"时间"},{prop:"max",label:"最大值"},{prop:"maxTs",label:"最大值发生时间"},{prop:"min",label:"最小值"},{prop:"minTs",label:"最小值发生时间"},{prop:"avg",label:"平均值"}],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),D=async()=>{var r;y.value=!0,_.loading=!0,x.loading=!0;const e=((r=h.value)==null?void 0:r.queryParams)||{},t={stationId:e.stationId,queryType:e.queryType,time:O(e.time).format(n.queryType==="month"?"YYYY-MM":n.queryType==="year"?"YYYY":"YYYY-MM-DD"),compareType:n.compareType},o=(await oe(t)).data.data;console.log("dddddd",o),_.columns=o==null?void 0:o.baseTable.tableInfo.map(s=>({prop:s.columnValue,label:s.columnName,unit:s.unit?"("+s.unit+")":""})),_.dataList=o==null?void 0:o.baseTable.tableDataList,_.loading=!1,x.dataList=o==null?void 0:o.countTable,x.loading=!1,B(o==null?void 0:o.baseTable,t)},B=(e,t)=>{F();const a={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:g().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:g().isDark?"#ffffff":"#000000"}}]}},o=e==null?void 0:e.tableDataList.map(u=>u.ts),r=M();r.series=[],r.yAxis[0].name="流量(m³/h)",r.xAxis.data=o,e==null||e.tableInfo.filter(u=>{if(!["differenceRate","ts","changeRate"].includes(u.columnValue)){const w=JSON.parse(JSON.stringify(a));w.name=u.columnName,w.data=e==null?void 0:e.tableDataList.map(V=>V[u.columnValue]),r.series.push(w)}});const s=M();s.series=[],s.yAxis[0].name="流量(m³/h)",s.xAxis.data=o;const d=JSON.parse(JSON.stringify(a));d.data=e==null?void 0:e.tableDataList.map(u=>u[t.time]),s.series.push(d);const p=M();p.series=[],p.yAxis[0].name="系数(%)",p.xAxis.data=o;const N=JSON.parse(JSON.stringify(a));N.data=e==null?void 0:e.tableDataList.map(u=>u.changeRate),p.series.push(N),W(()=>{b.value&&I.listenTo(b.value,()=>{n.chartOption1=r,n.chartOption2=s,n.chartOption3=p,A()})}),y.value=!1},F=()=>{var e,t,a;(e=S.value)==null||e.clear(),(t=C.value)==null||t.clear(),(a=q.value)==null||a.clear()},A=()=>{var e,t,a;(e=S.value)==null||e.resize(),(t=C.value)==null||t.resize(),(a=q.value)==null||a.resize()},P=()=>{var a;const e=((a=h.value)==null?void 0:a.queryParams)||{},t={stationId:e.stationId,queryType:e.queryType,time:O(e.time).format(n.queryType==="month"?"YYYY-MM":n.queryType==="year"?"YYYY":"YYYY-MM-DD"),compareType:n.compareType};se(t).then(o=>{const r=window.URL.createObjectURL(o.data),s=document.createElement("a");s.style.display="none",s.href=r,s.setAttribute("download","出水流量报表.xlsx"),document.body.appendChild(s),s.click()})};return H(async()=>{var a,o,r,s;const e=await R("水厂");n.storedStationList=e,console.log("state.storedStations",e);const t=(a=v.filters)==null?void 0:a.find(d=>d.field==="stationId");t.options=e,v.defaultParams={...v.defaultParams,stationId:(o=e[0])==null?void 0:o.id},n.storedStationName=(r=e[0])==null?void 0:r.label,(s=h.value)==null||s.resetForm(),D()}),(e,t)=>{const a=te,o=ee,r=Z,s=Q,d=U("VChart"),p=$;return Y(),k("div",ne,[c(a,{ref_key:"cardSearch",ref:h,config:i(v)},null,8,["config"]),l("div",re,[l("div",ie,[c(s,{class:"left-card",title:" "},{right:f(()=>[c(o,{ref:"refForm",class:"left",config:i(z)},null,8,["config"])]),default:f(()=>[l("div",le,[c(r,{class:"card-table",config:i(_)},null,8,["config"])]),l("div",ce,[c(r,{class:"card-table",config:i(x)},null,8,["config"])])]),_:1})]),l("div",pe,[c(s,{title:" ",class:"card-chart"},{title:f(()=>[l("div",ue,[t[0]||(t[0]=l("span",{class:"icon iconfont icon-shujufenxi"},null,-1)),l("span",de,X(i(n).compareType===1?" 同比":i(n).compareType===2?" 环比":" 定基比")+"分析",1)])]),default:f(()=>[L((Y(),k("div",{ref_key:"zxDiv",ref:b,class:"chart-box"},[c(d,{ref_key:"refChart1",ref:S,theme:i(g)().isDark?"blackBackground":"whiteBackground",option:i(n).chartOption1},null,8,["theme","option"])])),[[p,i(y)]])]),_:1}),c(s,{title:" ",class:"card-chart"},{title:f(()=>t[1]||(t[1]=[l("div",{class:"card-title"},[l("span",{class:"icon iconfont icon-shujufenxi"}),l("span",{class:"title"}," 阈值分析")],-1)])),default:f(()=>[L((Y(),k("div",{ref_key:"zxDiv",ref:b,class:"chart-box"},[c(d,{ref_key:"refChart2",ref:C,theme:i(g)().isDark?"blackBackground":"whiteBackground",option:i(n).chartOption2},null,8,["theme","option"])])),[[p,i(y)]])]),_:1}),c(s,{title:" ",class:"card-chart"},{title:f(()=>t[2]||(t[2]=[l("div",{class:"card-title"},[l("span",{class:"icon iconfont icon-shujufenxi"}),l("span",{class:"title"}," 系数分析")],-1)])),default:f(()=>[L((Y(),k("div",{ref_key:"zxDiv",ref:b,class:"chart-box"},[c(d,{ref_key:"refChart3",ref:q,theme:i(g)().isDark?"blackBackground":"whiteBackground",option:i(n).chartOption3},null,8,["theme","option"])])),[[p,i(y)]])]),_:1})])])])}}}),Se=K(fe,[["__scopeId","data-v-b1911294"]]);export{Se as default};
