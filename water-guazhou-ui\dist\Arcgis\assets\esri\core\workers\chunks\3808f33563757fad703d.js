"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[3847],{36030:(e,n,t)=>{t.d(n,{J:()=>r});var i=t(35454),l=t(5600);function r(e,n={}){const t=e instanceof i.X?e:new i.X(e,n),r={type:n?.ignoreUnknown??1?t.apiValues:String,json:{type:t.jsonValues,read:!n?.readOnly&&{reader:t.read},write:{writer:t.write}}};return void 0!==n?.readOnly&&(r.readOnly=!!n.readOnly),void 0!==n?.default&&(r.json.default=n.default),void 0!==n?.name&&(r.json.name=n.name),void 0!==n?.nonNullable&&(r.nonNullable=n.nonNullable),(0,l.Cb)(r)}},66577:(e,n,t)=>{t.d(n,{qM:()=>c});var i=t(75215),l=t(6570),r=t(9361),a=t(65091),o=t(94139),s=t(38913),u=t(58901);t(82971),t(86973),t(33955);const c={base:r.Z,key:"type",typeMap:{extent:l.Z,multipoint:a.Z,point:o.Z,polyline:u.Z,polygon:s.Z}};(0,i.N7)(c)},33955:(e,n,t)=>{t.d(n,{Ji:()=>h,YX:()=>c,aW:()=>f,im:()=>y,l9:()=>m,oU:()=>p,q9:()=>v,wp:()=>d});var i=t(70586),l=t(6570),r=t(9361),a=t(65091),o=t(94139),s=t(38913),u=t(58901);function c(e){return void 0!==e.xmin&&void 0!==e.ymin&&void 0!==e.xmax&&void 0!==e.ymax}function f(e){return void 0!==e.points}function d(e){return void 0!==e.x&&void 0!==e.y}function m(e){return void 0!==e.paths}function p(e){return void 0!==e.rings}function y(e){return(0,i.Wi)(e)?null:e instanceof r.Z?e:d(e)?o.Z.fromJSON(e):m(e)?u.Z.fromJSON(e):p(e)?s.Z.fromJSON(e):f(e)?a.Z.fromJSON(e):c(e)?l.Z.fromJSON(e):null}function h(e){return e?d(e)?"esriGeometryPoint":m(e)?"esriGeometryPolyline":p(e)?"esriGeometryPolygon":c(e)?"esriGeometryEnvelope":f(e)?"esriGeometryMultipoint":null:null}const g={esriGeometryPoint:o.Z,esriGeometryPolyline:u.Z,esriGeometryPolygon:s.Z,esriGeometryEnvelope:l.Z,esriGeometryMultipoint:a.Z};function v(e){return e&&g[e]||null}},82397:(e,n,t)=>{t.d(n,{G6:()=>h,Ie:()=>y,J9:()=>m,RF:()=>d,U1:()=>p,vY:()=>o});var i=t(70586);t(33955);const l=(e,n,t)=>[n,t],r=(e,n,t)=>[n,t,e[2]],a=(e,n,t)=>[n,t,e[2],e[3]];function o(e){return e?{originPosition:"upper-left"===e.originPosition?"upperLeft":"lower-left"===e.originPosition?"lowerLeft":e.originPosition,scale:e.tolerance?[e.tolerance,e.tolerance]:[1,1],translate:(0,i.pC)(e.extent)?[e.extent.xmin,e.extent.ymax]:[0,0]}:null}function s({scale:e,translate:n},t){return t*e[0]+n[0]}function u({scale:e,translate:n},t){return n[1]-t*e[1]}function c(e,n,t){const i=new Array(t.length);if(!t.length)return i;const[l,r]=e.scale;let a=s(e,t[0][0]),o=u(e,t[0][1]);i[0]=n(t[0],a,o);for(let e=1;e<t.length;e++){const s=t[e];a+=s[0]*l,o-=s[1]*r,i[e]=n(s,a,o)}return i}function f(e,n,t){const i=new Array(t.length);for(let l=0;l<t.length;l++)i[l]=c(e,n,t[l]);return i}function d(e,n,t,i,l){return n.x=function({scale:e,translate:n},t){return Math.round((t-n[0])/e[0])}(e,t.x),n.y=function({scale:e,translate:n},t){return Math.round((n[1]-t)/e[1])}(e,t.y),n!==t&&(i&&(n.z=t.z),l&&(n.m=t.m)),n}function m(e,n,t,o,s){return(0,i.pC)(t)&&(n.points=function(e,n,t,i){return c(e,t?i?a:r:i?r:l,n)}(e,t.points,o,s)),n}function p(e,n,t,l,r){return(0,i.Wi)(t)||(n.x=s(e,t.x),n.y=u(e,t.y),n!==t&&(l&&(n.z=t.z),r&&(n.m=t.m))),n}function y(e,n,t,o,s){return(0,i.pC)(t)&&(n.rings=function(e,n,t,i){return f(e,t?i?a:r:i?r:l,n)}(e,t.rings,o,s)),n}function h(e,n,t,o,s){return(0,i.pC)(t)&&(n.paths=function(e,n,t,i){return f(e,t?i?a:r:i?r:l,n)}(e,t.paths,o,s)),n}},86973:(e,n,t)=>{t.d(n,{M:()=>l,P:()=>r});var i=t(35454);const l=(0,i.w)()({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon"}),r=(0,i.w)()({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh"})},35671:(e,n,t)=>{t.d(n,{io:()=>I,AB:()=>x,gd:()=>V,Q0:()=>F,YN:()=>h,UF:()=>v,O5:()=>N,CH:()=>w,os:()=>_,H7:()=>O,Ec:()=>X,qN:()=>L,q6:()=>m,Pz:()=>q,Lk:()=>T,Qc:()=>Z,vP:()=>W}),t(20102);var i,l,r=t(70586),a=t(78286);(l=i||(i={})).VALUE_OUT_OF_RANGE="domain-validation-error::value-out-of-range",l.INVALID_CODED_VALUE="domain-validation-error::invalid-coded-value";var o=t(59266);const s=/^([0-9])/,u=/[^a-z0-9_\u0080-\uffff]/gi,c=/_{2,}/g,f=/^_/,d=/_$/;function m(e){return null==e?null:e.trim().replace(u,"_").replace(c,"_").replace(f,"").replace(d,"").replace(s,"F$1")||null}const p=["field","field2","field3","normalizationField","rotationInfo.field","proportionalSymbolInfo.field","proportionalSymbolInfo.normalizationField","colorInfo.field","colorInfo.normalizationField"],y=["field","normalizationField"];function h(e,n){if(null!=e&&null!=n)for(const t of Array.isArray(e)?e:[e])if(g(p,t,n),"visualVariables"in t&&t.visualVariables)for(const e of t.visualVariables)g(y,e,n)}function g(e,n,t){if(e)for(const i of e){const e=(0,a.hS)(i,n),l=e&&"function"!=typeof e&&t.get(e);l&&(0,a.RB)(i,l.name,n)}}function v(e,n){if(null!=e&&n?.fields?.length)if("startField"in e){const t=n.get(e.startField),i=n.get(e.endField);e.startField=t?.name??null,e.endField=i?.name??null}else{const t=n.get(e.startTimeField),i=n.get(e.endTimeField);e.startTimeField=t?.name??null,e.endTimeField=i?.name??null}}const b=new Set;function F(e,n){return e&&n?(b.clear(),V(b,e,n),Array.from(b).sort()):[]}function V(e,n,t){if(t)if(n?.fields?.length)if(t.includes("*"))for(const{name:t}of n.fields)e.add(t);else for(const i of t)x(e,n,i);else{if(t.includes("*"))return e.clear(),void e.add("*");for(const n of t)null!=n&&e.add(n)}}function x(e,n,t){if("string"==typeof t)if(n){const i=n.get(t);i&&e.add(i.name)}else e.add(t)}function T(e,n){return(0,r.Wi)(n)||(0,r.Wi)(e)?[]:n.includes("*")?(e.fields??[]).map((e=>e.name)):n}async function I(e,n,t){if(!t)return;const{arcadeUtils:i}=await(0,o.LC)(),l=i.extractFieldNames(t,n?.fields?.map((e=>e.name)));for(const t of l)x(e,n,t)}function N({displayField:e,fields:n}){return e||(n&&n.length?S(n,"name-or-title")||S(n,"unique-identifier")||S(n,"type-or-category")||function(e){for(const n of e){if(!n||!n.name)continue;const e=n.name.toLowerCase();if(e.includes("name")||e.includes("title"))return n.name}return null}(n):null)}function S(e,n){for(const t of e)if(t&&t.valueType&&t.valueType===n)return t.name;return null}function w(e){if(!e)return[];const n="editFieldsInfo"in e&&e.editFieldsInfo;return n?F(e.fieldsIndex,[n&&n.creatorField,n&&n.creationDateField,n&&n.editorField,n&&n.editDateField]):[]}function _(e){const n=e.defaultValue;return void 0!==n&&A(e,n)?n:e.nullable?null:void 0}function D(e){return"number"==typeof e&&!isNaN(e)&&isFinite(e)}function M(e){return null===e||D(e)}const C="isInteger"in Number?Number.isInteger:e=>"number"==typeof e&&isFinite(e)&&Math.floor(e)===e;function z(e){return null===e||C(e)}function k(e){return null!=e&&"string"==typeof e}function G(e){return null===e||k(e)}function $(){return!0}function A(e,n){let t;switch(e.type){case"date":case"integer":case"long":case"small-integer":case"esriFieldTypeDate":case"esriFieldTypeInteger":case"esriFieldTypeLong":case"esriFieldTypeSmallInteger":t=e.nullable?z:C;break;case"double":case"single":case"esriFieldTypeSingle":case"esriFieldTypeDouble":t=e.nullable?M:D;break;case"string":case"esriFieldTypeString":t=e.nullable?G:k;break;default:t=$}return 1===arguments.length?t:t(n)}const E=new Set(["integer","small-integer","single","double","esriFieldTypeInteger","esriFieldTypeSmallInteger","esriFieldTypeSingle","esriFieldTypeDouble"]);function O(e){return null!=e&&E.has(e.type)}function L(e){return null!=e&&("string"===e.type||"esriFieldTypeString"===e.type)}var P,U;function q(e){return null==e||"number"==typeof e&&isNaN(e)?null:e}function Z(e,n){return null==e||e.nullable&&null===n?null:O(e)&&!function(e,n){const t="string"==typeof e?R(e):e;if(!t)return!1;const i=t.min,l=t.max;return t.isInteger?C(n)&&n>=i&&n<=l:n>=i&&n<=l}(e.type,Number(n))?P.OUT_OF_RANGE:A(e,n)?e.domain?function(e,n){switch(e.type){case"range":{const t="range"in e?e.range[0]:e.minValue,l="range"in e?e.range[1]:e.maxValue;if(null!=t&&+n<t||null!=l&&+n>l)return i.VALUE_OUT_OF_RANGE;break}case"coded-value":case"codedValue":if(null==e.codedValues||e.codedValues.every((e=>null==e||e.code!==n)))return i.INVALID_CODED_VALUE}return null}(e.domain,n):null:U.INVALID_TYPE}function R(e){switch(e){case"esriFieldTypeSmallInteger":case"small-integer":return B;case"esriFieldTypeInteger":case"integer":return j;case"esriFieldTypeSingle":case"single":return J;case"esriFieldTypeDouble":case"double":return Y}}!function(e){e.OUT_OF_RANGE="numeric-range-validation-error::out-of-range"}(P||(P={})),function(e){e.INVALID_TYPE="type-validation-error::invalid-type"}(U||(U={}));const B={min:-32768,max:32767,isInteger:!0},j={min:-2147483648,max:2147483647,isInteger:!0},J={min:-34e37,max:12e37,isInteger:!1},Y={min:-Number.MAX_VALUE,max:Number.MAX_VALUE,isInteger:!1};function W(e,n,t){switch(e){case i.INVALID_CODED_VALUE:return`Value ${t} is not in the coded domain - field: ${n.name}, domain: ${JSON.stringify(n.domain)}`;case i.VALUE_OUT_OF_RANGE:return`Value ${t} is out of the range of valid values - field: ${n.name}, domain: ${JSON.stringify(n.domain)}`;case U.INVALID_TYPE:return`Value ${t} is not a valid value for the field type - field: ${n.name}, type: ${n.type}, nullable: ${n.nullable}`;case P.OUT_OF_RANGE:{const{min:e,max:i}=R(n.type);return`Value ${t} is out of range for the number type - field: ${n.name}, type: ${n.type}, value range is ${e} to ${i}`}}}function X(e){return!!e&&["raster.itempixelvalue","raster.servicepixelvalue"].some((n=>e.toLowerCase().startsWith(n)))}},10278:(e,n,t)=>{t.d(n,{Z:()=>d});var i=t(43697),l=t(35454),r=t(96674),a=t(5600),o=(t(75215),t(67676),t(36030)),s=t(52011);const u=new l.X({esriClassifyEqualInterval:"equal-interval",esriClassifyManual:"manual",esriClassifyNaturalBreaks:"natural-breaks",esriClassifyQuantile:"quantile",esriClassifyStandardDeviation:"standard-deviation",esriClassifyDefinedInterval:"defined-interval"}),c=new l.X({esriNormalizeByLog:"log",esriNormalizeByPercentOfTotal:"percent-of-total",esriNormalizeByField:"field"});let f=class extends r.wq{constructor(e){super(e),this.type="class-breaks-definition",this.breakCount=null,this.classificationField=null,this.classificationMethod=null,this.normalizationField=null,this.normalizationType=null}set standardDeviationInterval(e){"standard-deviation"===this.classificationMethod&&this._set("standardDeviationInterval",e)}set definedInterval(e){"defined-interval"===this.classificationMethod&&this._set("definedInterval",e)}};(0,i._)([(0,o.J)({classBreaksDef:"class-breaks-definition"})],f.prototype,"type",void 0),(0,i._)([(0,a.Cb)({json:{write:!0}})],f.prototype,"breakCount",void 0),(0,i._)([(0,a.Cb)({json:{write:!0}})],f.prototype,"classificationField",void 0),(0,i._)([(0,a.Cb)({type:String,json:{read:u.read,write:u.write}})],f.prototype,"classificationMethod",void 0),(0,i._)([(0,a.Cb)({json:{write:!0}})],f.prototype,"normalizationField",void 0),(0,i._)([(0,a.Cb)({json:{read:c.read,write:c.write}})],f.prototype,"normalizationType",void 0),(0,i._)([(0,a.Cb)({value:null,json:{write:!0}})],f.prototype,"standardDeviationInterval",null),(0,i._)([(0,a.Cb)({value:null,json:{write:!0}})],f.prototype,"definedInterval",null),f=(0,i._)([(0,s.j)("esri.rest.support.ClassBreaksDefinition")],f);const d=f},622:(e,n,t)=>{t.d(n,{k:()=>r});const i=t(92604).Z.getLogger("esri.rest.support.generateRendererUtils");function l(e,n){return Number(e.toFixed(n))}function r(e){const{normalizationTotal:n}=e;return{classBreaks:a(e),normalizationTotal:n}}function a(e){const n=e.definition,{classificationMethod:t,normalizationType:i,definedInterval:r}=n,a=n.breakCount??1,u=[];let c=e.values;if(0===c.length)return[];c=c.sort(((e,n)=>e-n));const f=c[0],d=c[c.length-1];if("equal-interval"===t)if(c.length>=a){const e=(d-f)/a;let n=f;for(let t=1;t<a;t++){const r=l(f+t*e,6);u.push({minValue:n,maxValue:r,label:o(n,r,i)}),n=r}u.push({minValue:n,maxValue:d,label:o(n,d,i)})}else c.forEach((e=>{u.push({minValue:e,maxValue:e,label:o(e,e,i)})}));else if("natural-breaks"===t){const n=function(e){const n=[],t=[];let i=Number.MIN_VALUE,l=1,r=-1;for(let a=0;a<e.length;a++){const o=e[a];o===i?(l++,t[r]=l):null!==o&&(n.push(o),i=o,l=1,t.push(l),r++)}return{uniqueValues:n,valueFrequency:t}}(c),t=e.valueFrequency||n.valueFrequency,r=s(n.uniqueValues,t,a);let m=f;for(let e=1;e<a;e++)if(n.uniqueValues.length>e){const t=l(n.uniqueValues[r[e]],6);u.push({minValue:m,maxValue:t,label:o(m,t,i)}),m=t}u.push({minValue:m,maxValue:d,label:o(m,d,i)})}else if("quantile"===t)if(c.length>=a&&f!==d){let e=f,n=Math.ceil(c.length/a),t=0;for(let l=1;l<a;l++){let r=n+t-1;r>c.length&&(r=c.length-1),r<0&&(r=0),u.push({minValue:e,maxValue:c[r],label:o(e,c[r],i)}),e=c[r],t+=n,n=Math.ceil((c.length-t)/(a-l))}u.push({minValue:e,maxValue:d,label:o(e,d,i)})}else{let e=-1;for(let n=0;n<c.length;n++){const t=c[n];t!==e&&(e=t,u.push({minValue:e,maxValue:t,label:o(e,t,i)}),e=t)}}else if("standard-deviation"===t){const e=function(e){let n=0;for(let t=0;t<e.length;t++)n+=e[t];return n/=e.length,n}(c),n=function(e,n){let t=0;for(let i=0;i<e.length;i++){const l=e[i];t+=(l-n)*(l-n)}return t/=e.length,Math.sqrt(t)}(c,e);if(0===n)u.push({minValue:c[0],maxValue:c[0],label:o(c[0],c[0],i)});else{const t=function(e,n,t,i,l){let r=Math.max(i-e,n-i)/l/t;return r=r>=1?1:r>=.5?.5:.25,r}(f,d,a,e,n)*n;let r=0,s=f;for(let n=a;n>=1;n--){const a=l(e-(n-.5)*t,6);u.push({minValue:s,maxValue:a,label:o(s,a,i)}),s=a,r++}let c=l(e+.5*t,6);u.push({minValue:s,maxValue:c,label:o(s,c,i)}),s=c,r++;for(let n=1;n<=a;n++)c=r===2*a?d:l(e+(n+.5)*t,6),u.push({minValue:s,maxValue:c,label:o(s,c,i)}),s=c,r++}}else if("defined-interval"===t){if(!r)return u;const e=c[0],n=c[c.length-1],t=Math.ceil((n-e)/r);let a=e;for(let n=1;n<t;n++){const t=l(e+n*r,6);u.push({minValue:a,maxValue:t,label:o(a,t,i)}),a=t}u.push({minValue:a,maxValue:n,label:o(a,n,i)})}return u}function o(e,n,t){let i=null;return i=e===n?t&&"percent-of-total"===t?e+"%":e.toString():t&&"percent-of-total"===t?e+"% - "+n+"%":e+" - "+n,i}function s(e,n,t){const i=e.length,l=[];t>i&&(t=i);for(let e=0;e<t;e++)l.push(Math.round(e*i/t-1));l.push(i-1);let r=u(l,e,n,t);return function(e,n,t,i,l,r){let a=0,o=0,s=0,u=0,f=!0;for(let d=0;d<2&&f;d++){0===d&&(f=!1);for(let d=0;d<r-1;d++)for(;t[d+1]+1!==t[d+2];){t[d+1]=t[d+1]+1;const r=c(d,t,i,l);s=r.sbMean,a=r.sbSdcm;const m=c(d+1,t,i,l);if(u=m.sbMean,o=m.sbSdcm,!(a+o<n[d]+n[d+1])){t[d+1]=t[d+1]-1;break}n[d]=a,n[d+1]=o,e[d]=s,e[d+1]=u,f=!0}for(let d=r-1;d>0;d--)for(;t[d]!==t[d-1]+1;){t[d]=t[d]-1;const r=c(d-1,t,i,l);s=r.sbMean,a=r.sbSdcm;const m=c(d,t,i,l);if(u=m.sbMean,o=m.sbSdcm,!(a+o<n[d-1]+n[d])){t[d]=t[d]+1;break}n[d-1]=a,n[d]=o,e[d-1]=s,e[d]=u,f=!0}}return f}(r.mean,r.sdcm,l,e,n,t)&&(r=u(l,e,n,t)),l}function u(e,n,t,i){let l=[],r=[],a=[],o=0;const s=[],u=[];for(let l=0;l<i;l++){const i=c(l,e,n,t);s.push(i.sbMean),u.push(i.sbSdcm),o+=u[l]}let f,d=o,m=!0;for(;m||o<d;){m=!1,l=[];for(let n=0;n<i;n++)l.push(e[n]);for(let t=0;t<i;t++)for(let l=e[t]+1;l<=e[t+1];l++)if(f=n[l],t>0&&l!==e[t+1]&&Math.abs(f-s[t])>Math.abs(f-s[t-1]))e[t]=l;else if(t<i-1&&e[t]!==l-1&&Math.abs(f-s[t])>Math.abs(f-s[t+1])){e[t+1]=l-1;break}d=o,o=0,r=[],a=[];for(let l=0;l<i;l++){r.push(s[l]),a.push(u[l]);const i=c(l,e,n,t);s[l]=i.sbMean,u[l]=i.sbSdcm,o+=u[l]}}if(o>d){for(let n=0;n<i;n++)e[n]=l[n],s[n]=r[n],u[n]=a[n];o=d}return{mean:s,sdcm:u}}function c(e,n,t,l){let r=0,a=0;for(let i=n[e]+1;i<=n[e+1];i++){const e=l[i];r+=t[i]*e,a+=e}a<=0&&i.warn("Exception in Natural Breaks calculation");const o=r/a;let s=0;for(let i=n[e]+1;i<=n[e+1];i++)s+=l[i]*(t[i]-o)**2;return{sbMean:o,sbSdcm:s}}},24452:(e,n,t)=>{t.d(n,{DL:()=>T,F_:()=>g,G2:()=>x,H0:()=>c,Lq:()=>m,Qm:()=>F,S5:()=>u,XL:()=>d,eT:()=>v,fk:()=>V,i5:()=>f,oF:()=>I,wk:()=>s});var i=t(10278),l=t(622);const r=/\s*(\+|-)?((\d+(\.\d+)?)|(\.\d+))\s*/gi,a=new Set(["esriFieldTypeInteger","esriFieldTypeSmallInteger","esriFieldTypeSingle","esriFieldTypeDouble"]),o=["min","max","avg","stddev","count","sum","variance","nullcount","median"];function s(e){return null==e||"string"==typeof e&&!e?"<Null>":e}function u(e){const n=null!=e.normalizationField||null!=e.normalizationType,t=null!=e.minValue||null!=e.maxValue,i=!!e.sqlExpression&&e.supportsSQLExpression;return!n&&!t&&!i}function c(e){const n=e.returnDistinct?[...new Set(e.values)]:e.values,t=n.filter((e=>null!=e)).length,i={count:t};return e.supportsNullCount&&(i.nullcount=n.length-t),e.percentileParams&&(i.median=d(n,e.percentileParams)),i}function f(e){const{values:n,useSampleStdDev:t,supportsNullCount:i}=e;let l=Number.POSITIVE_INFINITY,r=Number.NEGATIVE_INFINITY,a=null,o=null,s=null,u=null,c=0;const f=null==e.minValue?-1/0:e.minValue,m=null==e.maxValue?1/0:e.maxValue;for(const e of n)Number.isFinite(e)?e>=f&&e<=m&&(a=null===a?e:a+e,l=Math.min(l,e),r=Math.max(r,e),c++):"string"==typeof e&&c++;if(c&&null!=a){o=a/c;let e=0;for(const t of n)Number.isFinite(t)&&t>=f&&t<=m&&(e+=(t-o)**2);u=t?c>1?e/(c-1):0:c>0?e/c:0,s=Math.sqrt(u)}else l=null,r=null;const p={avg:o,count:c,max:r,min:l,stddev:s,sum:a,variance:u};return i&&(p.nullcount=n.length-c),e.percentileParams&&(p.median=d(n,e.percentileParams)),p}function d(e,n){const{fieldType:t,value:i,orderBy:l,isDiscrete:r}=n,a=m(t,"desc"===l);if(0===(e=[...e].filter((e=>null!=e)).sort(((e,n)=>a(e,n)))).length)return null;if(i<=0)return e[0];if(i>=1)return e[e.length-1];const o=(e.length-1)*i,s=Math.floor(o),u=s+1,c=o%1,f=e[s],d=e[u];return u>=e.length||r||"string"==typeof f||"string"==typeof d?f:f*(1-c)+d*c}function m(e,n){const t=n?1:-1,i=function(e){return e?(e,n)=>{const t=p(e,n,!0);return null!=t?t:n-e}:(e,n)=>{const t=p(e,n,!1);return null!=t?t:e-n}}(n),l=y(n);if(!e||!["esriFieldTypeDate","esriFieldTypeString","esriFieldTypeGUID","esriFieldTypeGlobalID",...a].includes(e))return(e,n)=>"number"==typeof e&&"number"==typeof n?i(e,n):"string"==typeof e&&"string"==typeof n?l(e,n):t;if("esriFieldTypeDate"===e)return(e,n)=>{const l=new Date(e).getTime(),r=new Date(n).getTime();return isNaN(l)||isNaN(r)?t:i(l,r)};if(a.has(e))return(e,n)=>i(e,n);if("esriFieldTypeString"===e)return(e,n)=>l(e,n);if("esriFieldTypeGUID"===e||"esriFieldTypeGlobalID"===e){const e=y(n);return(n,t)=>e(h(n),h(t))}return n?(e,n)=>1:(e,n)=>-1}function p(e,n,t){if(t){if(null==e)return null==n?0:1;if(null==n)return-1}else{if(null==e)return null==n?0:-1;if(null==n)return 1}return null}function y(e){return e?(e,n)=>{const t=p(e,n,!0);return null!=t?t:(e=e.toUpperCase())>(n=n.toUpperCase())?-1:e<n?1:0}:(e,n)=>{const t=p(e,n,!1);return null!=t?t:(e=e.toUpperCase())<(n=n.toUpperCase())?-1:e>n?1:0}}function h(e){return e.substr(24,12)+e.substr(19,4)+e.substr(16,2)+e.substr(14,2)+e.substr(11,2)+e.substr(9,2)+e.substr(6,2)+e.substr(4,2)+e.substr(2,2)+e.substr(0,2)}function g(e,n){let t;for(t in e)o.includes(t)&&(Number.isFinite(e[t])||(e[t]=null));return n?(["avg","stddev","variance"].forEach((n=>{null!=e[n]&&(e[n]=Math.ceil(e[n]))})),e):e}function v(e){const n={};for(let t of e)(null==t||"string"==typeof t&&""===t.trim())&&(t=null),null==n[t]?n[t]={count:1,data:t}:n[t].count++;return{count:n}}function b(e){return"coded-value"!==e?.type?[]:e.codedValues.map((e=>e.code))}function F(e,n,t,i){const l=e.count,r=[];if(t&&n){const e=[],t=b(n[0]);for(const l of t)if(n[1]){const t=b(n[1]);for(const r of t)if(n[2]){const t=b(n[2]);for(const n of t)e.push(`${s(l)}${i}${s(r)}${i}${s(n)}`)}else e.push(`${s(l)}${i}${s(r)}`)}else e.push(l);for(const n of e)l.hasOwnProperty(n)||(l[n]={data:n,count:0})}for(const e in l){const n=l[e];r.push({value:n.data,count:n.count,label:n.label})}return{uniqueValueInfos:r}}function V(e,n,t,i){let l=null;switch(n){case"log":0!==e&&(l=Math.log(e)*Math.LOG10E);break;case"percent-of-total":Number.isFinite(i)&&0!==i&&(l=e/i*100);break;case"field":Number.isFinite(t)&&0!==t&&(l=e/t);break;case"natural-log":e>0&&(l=Math.log(e));break;case"square-root":e>0&&(l=e**.5)}return l}function x(e,n){const t=function(e){const{breakCount:n,field:t,normalizationField:l,normalizationType:r}=e,a=e.classificationMethod||"equal-interval",o="standard-deviation"===a?e.standardDeviationInterval||1:void 0;return new i.Z({breakCount:n,classificationField:t,classificationMethod:a,normalizationField:"field"===r?l:void 0,normalizationType:r,standardDeviationInterval:o})}({field:n.field,normalizationType:n.normalizationType,normalizationField:n.normalizationField,classificationMethod:n.classificationMethod,standardDeviationInterval:n.standardDeviationInterval,breakCount:n.numClasses||5});return e=function(e,n,t){const i=n??-1/0,l=t??1/0;return e.filter((e=>Number.isFinite(e)&&e>=i&&e<=l))}(e,n.minValue,n.maxValue),(0,l.k)({definition:t,values:e,normalizationTotal:n.normalizationTotal})}function T(e,n){let t=e.classBreaks;const i=t.length,l=t[0].minValue,a=t[i-1].maxValue,o="standard-deviation"===n,s=r;return t=t.map((e=>{const n=e.label,t={minValue:e.minValue,maxValue:e.maxValue,label:n};if(o&&n){const e=n.match(s)?.map((e=>+e.trim()))??[];2===e.length?(t.minStdDev=e[0],t.maxStdDev=e[1],e[0]<0&&e[1]>0&&(t.hasAvg=!0)):1===e.length&&(n.includes("<")?(t.minStdDev=null,t.maxStdDev=e[0]):n.includes(">")&&(t.minStdDev=e[0],t.maxStdDev=null))}return t})),{minValue:l,maxValue:a,classBreakInfos:t,normalizationTotal:e.normalizationTotal}}function I(e,n){const t=function(e,n){const{field:t,classificationMethod:i,standardDeviationInterval:l,normalizationType:r,normalizationField:a,normalizationTotal:o,minValue:s,maxValue:c}=n,d=n.numBins||10;let m=null,p=null,y=null;if(i&&"equal-interval"!==i||r){const{classBreaks:n}=x(e,{field:t,normalizationType:r,normalizationField:a,normalizationTotal:o,classificationMethod:i,standardDeviationInterval:l,minValue:s,maxValue:c,numClasses:d});m=n[0].minValue,p=n[n.length-1].maxValue,y=n.map((e=>[e.minValue,e.maxValue]))}else{if(null!=s&&null!=c)m=s,p=c;else{const n=f({values:e,minValue:s,maxValue:c,useSampleStdDev:!r,supportsNullCount:u({normalizationType:r,normalizationField:a,minValue:s,maxValue:c})});m=n.min??null,p=n.max??null}y=function(e,n,t){const i=(n-e)/t,l=[];let r,a=e;for(let e=1;e<=t;e++)r=a+i,r=Number(r.toFixed(16)),l.push([a,e===t?n:r]),a=r;return l}(m??0,p??0,d)}return{min:m,max:p,intervals:y}}(e,n),i=t.intervals,l=t.min??0,r=t.max??0,a=i.map(((e,n)=>({minValue:i[n][0],maxValue:i[n][1],count:0})));for(const n of e)if(null!=n&&n>=l&&n<=r){const e=N(i,n);e>-1&&a[e].count++}return{bins:a,minValue:l,maxValue:r,normalizationTotal:n.normalizationTotal}}function N(e,n){let t=-1;for(let i=e.length-1;i>=0;i--)if(n>=e[i][0]){t=i;break}return t}},59266:(e,n,t)=>{t.d(n,{LC:()=>u,WW:()=>c,Yi:()=>f,mz:()=>m,pp:()=>d}),t(66577);var i=t(20102),l=t(92604),r=t(70586),a=t(82971);const o=l.Z.getLogger("esri.support.arcadeOnDemand");let s;function u(){return s||(s=(async()=>{const e=await Promise.all([t.e(7126),t.e(8732),t.e(5587),t.e(1482),t.e(5638)]).then(t.bind(t,20837));return{arcade:e.arcade,arcadeUtils:e,Dictionary:e.Dictionary,Feature:e.arcadeFeature}})()),s}const c=(e,n,t)=>m.create(e,n,t,null,["$feature"]),f=(e,n,t)=>m.create(e,n,t,null,["$feature","$view"]),d=(e,n,t,i)=>m.create(e,n,t,i,["$feature","$view"]);class m{constructor(e,n,t,i,l,r,a){this.script=e,this.evaluate=i;const o=Array.isArray(r)?r:r.fields;this.fields=o,this._syntaxTree=t,this._arcade=n,this._arcadeFeature=l,this._spatialReference=a,this._referencesGeometry=n.scriptTouchesGeometry(this._syntaxTree),this._referencesScale=this._arcade.referencesMember(this._syntaxTree,"scale")}static async create(e,n,t,l,s,c){const{arcade:f,Feature:d,Dictionary:p}=await u(),y=a.Z.fromJSON(n);let h;try{h=f.parseScript(e,c)}catch(n){return o.error(new i.Z("arcade-bad-expression","Failed to parse arcade script",{script:e,error:n})),null}const g=s.reduce(((e,n)=>({...e,[n]:null})),{});let v=null;(0,r.pC)(l)&&(v=new p(l),v.immutable=!0,g.$config=null);const b=f.scriptUsesGeometryEngine(h),F=b&&f.enableGeometrySupport(),V=f.scriptUsesFeatureSet(h)&&f.enableFeatureSetSupport(),x=f.scriptIsAsync(h),T=x&&f.enableAsyncSupport(),I={vars:g,spatialReference:y,useAsync:!!T};await Promise.all([F,V,T]);const N=new Set;await f.loadDependentModules(N,h,null,x,b);const S=new p;S.immutable=!1,S.setField("scale",0);const w=f.compileScript(h,I);return new m(e,f,h,(e=>("$view"in e&&e.$view&&(S.setField("scale","object"==typeof e.$view?e.$view.scale:void 0),e.$view=S),v&&(e.$config=v),w({vars:e,spatialReference:y}))),new d,t,y)}repurposeFeature(e){return e.geometry&&!e.geometry.spatialReference&&(e.geometry.spatialReference=this._spatialReference),this._arcadeFeature.repurposeFromGraphicLikeObject(e.geometry,e.attributes,{fields:this.fields}),this._arcadeFeature}referencesGeometry(){return this._referencesGeometry}referencesScale(){return this._referencesScale}}}}]);