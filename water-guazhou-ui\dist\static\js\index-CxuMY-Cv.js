import{_ as l}from"./CardTable-rdWOL4_6.js";import{_ as m}from"./CardSearch-CB_HNR-Q.js";import{d as _,r as a,g as f,n as u,q as o,i as r}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const g={class:"wrapper"},S=_({__name:"index",setup(d){const p=a({filters:[{type:"input",label:"关键词",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询"},{perm:!0,type:"primary",text:"新增"}]}],defaultParams:{}}),e=a({columns:[{label:"方案名称",prop:"name"},{label:"创建时间",prop:"time"}],dataList:[],operations:[{perm:!0,text:"删除",type:"danger"}],pagination:{refreshData:({page:t,size:n})=>{e.pagination.page=t||1,e.pagination.limit=n||20,i()}}}),i=()=>{};return(t,n)=>{const s=m,c=l;return f(),u("div",g,[o(s,{ref:"refSearch",config:r(p)},null,8,["config"]),o(c,{class:"card-table",config:r(e)},null,8,["config"])])}}});export{S as default};
