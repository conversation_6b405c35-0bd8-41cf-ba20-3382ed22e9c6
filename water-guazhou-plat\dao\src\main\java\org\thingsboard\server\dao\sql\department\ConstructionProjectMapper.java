package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.store.ConstructionProject;
import org.thingsboard.server.dao.util.imodel.query.store.ConstructionProjectPageRequest;

@Mapper
public interface ConstructionProjectMapper extends BaseMapper<ConstructionProject> {
    IPage<ConstructionProject> findByPage(ConstructionProjectPageRequest request);

    boolean update(ConstructionProject proj);

    String getNameById(String id);
}
