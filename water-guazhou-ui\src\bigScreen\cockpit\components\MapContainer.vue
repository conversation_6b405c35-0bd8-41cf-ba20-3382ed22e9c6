<template>
  <div class="map-container" ref="mapContainer">
    <div v-if="!mapLoaded" class="map-loading">
      <div class="loading-text">天地图影像加载中...</div>
    </div>
    
    <!-- 图层控制按钮 -->
    <div class="layer-control" v-if="mapLoaded">
      <button class="control-btn" :class="{ active: showPipeLayer }" @click="togglePipeLayer">
        <span>管网图层</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, reactive } from 'vue'
import { Scene,RasterLayer, PointLayer } from '@antv/l7'
import { Map } from '@antv/l7-maps'

// 天地图token
const TIANDITU_TOKEN = window.SITE_CONFIG['gisTdtToken']; // 请替换为有效的token

const props = defineProps({
  // 地图中心点
  center: {
    type: Array,
    default: () => [95.782306, 40.520545] // 甘肃瓜州县精确中心点坐标
  },
  // 地图缩放级别
  zoom: {
    type: Number,
    default: 9 // 适合查看瓜州县整体区域的缩放级别
  },
  // 地图点位数据
  points: {
    type: Array,
    default: () => []
  },
  // 地图高度
  height: {
    type: String,
    default: '100%'
  },
  // 地图宽度
  width: {
    type: String,
    default: '100%'
  },
  // 是否允许交互
  interactive: {
    type: Boolean,
    default: true
  },
  // GeoServer WMS服务地址
  geoserverUrl: {
    type: String,
    default: 'http://localhost:8080/geoserver/wms'
  },
  // 管网图层名称
  pipeLayerName: {
    type: String,
    default: 'guazhou:pipe_network'
  },
  // 管网图层范围
  // pipeLayerExtent: {
  //   type: Array,
  //   default: () => [95.355895, 40.285644, 96.147305, 40.573433] // 瓜州县的大致范围
  // }
})

// 本地数据
const localPoints = reactive([
  {
    name: '瓜州县水厂',
    status: 'normal',
    longitude: 95.782306,
    latitude: 40.520545
  },
  {
    name: '渊泉镇',
    status: 'normal',
    longitude: 95.777863,
    latitude: 40.518753
  },
  {
    name: '柳园镇',
    status: 'warning',
    longitude: 95.355895,
    latitude: 40.432522
  },
  {
    name: '锁阳城镇',
    status: 'normal',
    longitude: 96.147305,
    latitude: 40.513103
  },
  {
    name: '南岔镇',
    status: 'critical',
    longitude: 95.573433,
    latitude: 40.285644
  }
]);

// 地图加载状态
const mapLoaded = ref(false);

// 地图容器引用
const mapContainer = ref(null)
// 场景实例
let scene = null
// 点图层
let pointLayer = null

// 图层控制状态
const showPipeLayer = ref(true);
let pipeLayerInstance = null;

// 切换管网图层显示状态
const togglePipeLayer = () => {
  showPipeLayer.value = !showPipeLayer.value;
  if (pipeLayerInstance) {
    if (showPipeLayer.value) {
      pipeLayerInstance.show();
    } else {
      pipeLayerInstance.hide();
    }
  }
};

// 初始化地图
const initMap = () => {
  if (!mapContainer.value) {
    console.error('地图容器未找到')
    return
  }
  
  console.log('初始化地图', mapContainer.value)
  
  try {
    // 创建地图场景 - 使用高德地图
    scene = new Scene({
      id: mapContainer.value,
      map: new Map({
        style: 'dark', // 使用内置的暗色主题
        center: props.center,
        zoom: props.zoom,
        minZoom: 7,
        maxZoom: 18,
        pitch: 0,
        interactive: props.interactive
      })
    })

    console.log('地图场景创建成功')

    // 等待场景加载完成
    scene.on('loaded', () => {
      mapLoaded.value = true;
        // 底图服务
      const baseLayer = new RasterLayer({ zIndex: 1,forceRender:true})
      .source(
        'https://t1.tianditu.gov.cn/DataServer?T=img_w&X={x}&Y={y}&L={z}&tk=' + TIANDITU_TOKEN,
        {
          parser: {
            type: 'rasterTile',
            tileSize: 256,
          }
        }
      );
      // 注记服务
      const annotionLayer = new RasterLayer({ zIndex: 2 ,forceRender:true})
      .source(
        'https://t1.tianditu.gov.cn/DataServer?T=cia_w&X={x}&Y={y}&L={z}&tk=' + TIANDITU_TOKEN,
        {
          parser: {
            type: 'rasterTile',
            tileSize: 256,
          }
        }
      );
      
      // 添加GeoServer WMS管网图层
      const pipeLayer = new RasterLayer({ zIndex: 3, forceRender: true })
      .source(
        // 使用props中的配置构建WMS URL
        `http://***********:18080/geoserver/guazhou/wms?SERVICE=WMS&VERSION=1.1.1&REQUEST=GetMap&FORMAT=image/png&TRANSPARENT=true&LAYERS=guazhou:管线&CRS=EPSG:4326&STYLES=&WIDTH=256&HEIGHT=256&BBOX={bbox}`,
        {
          parser: {
            type: 'rasterTile',
            tileSize: 256,
            // extent: props.pipeLayerExtent,
          }
        }
      );
      // 添加GeoServer WMS管网图层
      const pointLayer = new RasterLayer({ zIndex: 4, forceRender: true })
      .source(
        // 使用props中的配置构建WMS URL
        `http://***********:18080/geoserver/guazhou/wms?SERVICE=WMS&VERSION=1.1.1&REQUEST=GetMap&FORMAT=image/png&TRANSPARENT=true&LAYERS=guazhou:测点&CRS=EPSG:4326&STYLES=&WIDTH=256&HEIGHT=256&BBOX={bbox}`,
        {
          parser: {
            type: 'rasterTile',
            tileSize: 256,
            // extent: props.pipeLayerExtent,
          }
        }
      );
      
      // 保存图层实例以便控制
      pipeLayerInstance = pipeLayer;
      
      scene.addLayer(baseLayer);
      scene.addLayer(annotionLayer);
      scene.addLayer(pipeLayer);
      scene.addLayer(pointLayer);
      
      console.log('地图图层加载完成，包括管网图层');
      // addPointLayer()
    })

    // 添加错误处理
    scene.on('error', (error) => {
      console.error('地图加载错误:', error)
    })
  } catch (error) {
    console.error('初始化地图出错:', error)
  }
}

// 添加点图层
const addPointLayer = () => {
  if (!scene) {
    console.error('场景未初始化，无法添加点图层')
    return
  }
  
  // 使用本地点位数据或props中的数据
  const pointsData = localPoints.length > 0 ? localPoints : props.points;
  
  if (!pointsData.length) {
    console.warn('没有点位数据，使用默认测试数据')
    // 如果没有点位数据，使用瓜州县的默认测试数据
    const testPoints = [
      { name: '水厂1', status: 'normal', lng: 95.78, lat: 40.52 },
      { name: '水厂2', status: 'warning', lng: 95.85, lat: 40.55 },
      { name: '泵站1', status: 'critical', lng: 95.72, lat: 40.48 }
    ];
    
    try {
      console.log('使用测试点位数据:', testPoints);
      
      // 创建点图层
      pointLayer = new PointLayer({})
        .source(testPoints, {
          parser: {
            type: 'json',
            x: 'lng',
            y: 'lat'
          }
        })
        .shape('circle')
        .size(12)
        .color('status', (status) => {
          // 根据状态返回不同的颜色
          switch(status) {
            case 'warning':
              return '#FFC61A';
            case 'critical':
              return '#FF5252';
            case 'normal':
            default:
              return '#1AA7FF';
          }
        })
        .style({
          opacity: 0.8,
          strokeWidth: 1,
          stroke: '#fff'
        })
        // 添加标签
        .label({
          field: 'name',
          style: {
            fill: '#fff',
            fontSize: 12,
            textAnchor: 'center',
            textOffset: [0, -20],
            stroke: '#001328',
            strokeWidth: 2
          }
        })
        // 添加鼠标悬停效果
        .active({
          color: '#ffffff',
          size: 15
        });

      // 添加图层到场景
      scene.addLayer(pointLayer)
      console.log('点图层添加成功')

      // 添加点击事件
      pointLayer.on('click', (e) => {
        if (e && e.feature) {
          // 触发点击事件
          emit('point-click', e.feature.properties)
        }
      })
    } catch (error) {
      console.error('添加点图层出错:', error)
    }
    return;
  }

  try {
    // 处理数据格式，确保有正确的经纬度字段
    const formattedPoints = pointsData.map(point => {
      return {
        ...point,
        lng: point.longitude || point.lng || 0,
        lat: point.latitude || point.lat || 0
      };
    });

    console.log('格式化后的点位数据:', formattedPoints);

    // 创建点图层
    pointLayer = new PointLayer({})
      .source(formattedPoints, {
        parser: {
          type: 'json',
          x: 'lng',
          y: 'lat'
        }
      })
      .shape('circle')
      .size(12)
      .color('status', (status) => {
        // 根据状态返回不同的颜色
        switch(status) {
          case 'warning':
            return '#FFC61A';
          case 'critical':
            return '#FF5252';
          case 'normal':
          default:
            return '#1AA7FF';
        }
      })
      .style({
        opacity: 0.8,
        strokeWidth: 1,
        stroke: '#fff'
      })
      // 添加标签
      .label({
        field: 'name',
        style: {
          fill: '#fff',
          fontSize: 12,
          textAnchor: 'center',
          textOffset: [0, -20],
          stroke: '#001328',
          strokeWidth: 2
        }
      })
      // 添加鼠标悬停效果
      .active({
        color: '#ffffff',
        size: 15
      });

    // 添加图层到场景
    scene.addLayer(pointLayer)
    console.log('点图层添加成功')

    // 添加点击事件
    pointLayer.on('click', (e) => {
      if (e && e.feature) {
        // 触发点击事件
        emit('point-click', e.feature.properties)
      }
    })
  } catch (error) {
    console.error('添加点图层出错:', error)
  }
}

// 更新点位数据
const updatePoints = () => {
  if (!pointLayer) return;
  
  // 使用本地点位数据或props中的数据
  const pointsData = localPoints.length > 0 ? localPoints : props.points;
  
  if (!pointsData.length) return;

  try {
    // 处理数据格式
    const formattedPoints = pointsData.map(point => {
      return {
        ...point,
        lng: point.longitude || point.lng || 0,
        lat: point.latitude || point.lat || 0
      };
    });

    pointLayer.setData(formattedPoints);
  } catch (error) {
    console.error('更新点图层出错:', error);
  }
}

// 监听点位数据变化
watch(() => props.points, () => {
  if (pointLayer) {
    updatePoints()
  } else if (scene) {
    addPointLayer()
  }
}, { deep: true })

// 监听中心点变化
watch(() => props.center, (newCenter) => {
  if (scene && scene.map) {
    scene.map.setCenter(newCenter)
  }
})

// 监听缩放级别变化
watch(() => props.zoom, (newZoom) => {
  if (scene && scene.map) {
    scene.map.setZoom(newZoom)
  }
})

// 定义事件
const emit = defineEmits(['point-click'])

// 组件挂载后初始化地图
onMounted(() => {
  console.log('MapContainer组件已挂载')
  
  // 增加延迟时间确保DOM完全渲染
  setTimeout(() => {
    initMap()
  }, 300)
})

// 组件卸载前销毁地图
onUnmounted(() => {
  console.log('MapContainer组件已卸载')
  if (scene) {
    scene.destroy()
    scene = null
    pointLayer = null
    pipeLayerInstance = null
  }
})

// 对外暴露方法
defineExpose({
  togglePipeLayer,
  showPipeLayer: () => {
    showPipeLayer.value = true;
    pipeLayerInstance?.show();
  },
  hidePipeLayer: () => {
    showPipeLayer.value = false;
    pipeLayerInstance?.hide();
  }
});
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  width: v-bind('width');
  height: v-bind('height');
  background-color: #000; /* 更改为黑色背景，更适合影像地图 */
  z-index: 2; /* 确保地图在适当的层级显示 */
  border: 1px solid transparent; /* 添加边框以确保容器有尺寸 */
  pointer-events: auto; /* 确保地图可以接收鼠标事件 */
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7); /* 更深的黑色背景 */
  color: white;
  font-size: 16px;
  z-index: 3;
}

.loading-text {
  padding: 10px 20px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
}

.layer-control {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 4;
}

.control-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  margin-left: 8px;
  cursor: pointer;
  transition: background-color 0.3s;

  &.active {
    background-color: rgba(255, 255, 255, 0.8);
  }

  &:hover {
    background-color: rgba(255, 255, 255, 0.4);
  }

  span {
    font-size: 14px;
    font-weight: bold;
    color: white;
  }
}
</style> 