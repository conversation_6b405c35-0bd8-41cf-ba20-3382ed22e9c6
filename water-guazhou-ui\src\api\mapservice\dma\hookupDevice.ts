import { request } from '@/plugins/axios'

/**
 * 查询DMA可挂接的所有设备列表
 * @param params
 * @returns
 */
export const GetDMADeviceList = (params: {
  page: number
  size: number
  name?: string
  type?: string
  partitionId?: string
}) => {
  return request({
    url: '/api/device/getPartitionDevice',
    method: 'get',
    params
  })
}

/**
 * 挂接DMA设备
 * @param params
 * @returns
 */
export const HookupDMADevice = (
  params: {
    partitionId?: string
    deviceId?: string
    type?: string
    isAccount?: string
  }[]
) => {
  return request({
    url: '/api/spp/dma/partition/mount/batchSave',
    method: 'post',
    data: params
  })
}
/**
 * 查询DMA已挂接的设备列表
 * @param params
 * @returns
 */
export const GetDMAHookedDevices = (
  params: IQueryPagerParams & {
    partitionId?: string
    name?: string
    type?: string
  }
) => {
  return request({
    url: '/api/spp/dma/partition/mount/list',
    method: 'get',
    params
  })
}
/**
 * 移出DMA挂接设备
 * @param ids
 * @returns
 */
export const DeleteDMADevices = (ids: string[]) => {
  return request({
    url: '/api/spp/dma/partition/mount',
    method: 'delete',
    data: ids
  })
}
/**
 * 变更DMA设备的方向
 * @param id
 * @returns
 */
export const PostDMADeviceDirection = (id: string) => {
  return request({
    url: `/api/spp/dma/partition/mount/changeDirection/${id}`,
    method: 'post'
  })
}
/**
 * 查询分区设备树
 * @param params
 * @returns
 */
export const GetHookedDeviceTree = (params: { type: string }) => {
  return request({
    url: `/api/spp/dma/partition/partitionDeviceTree`,
    method: 'get',
    params
  })
}
