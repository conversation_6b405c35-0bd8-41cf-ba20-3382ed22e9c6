/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.rule.engine.api;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.RuleNodeId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.rule.RuleNode;
import org.thingsboard.server.common.msg.TbMsg;
import org.thingsboard.server.common.msg.TbMsgMetaData;
import org.thingsboard.server.dao.alarm.AlarmCountService;
import org.thingsboard.server.dao.alarm.AlarmJsonService;
import org.thingsboard.server.dao.alarm.AlarmService;
import org.thingsboard.server.dao.alarmV2.AlarmCenterService;
import org.thingsboard.server.dao.alarmV2.AlarmRuleService;
import org.thingsboard.server.dao.alarmV2.AlarmRuleSmartService;
import org.thingsboard.server.dao.attributes.AttributesService;
import org.thingsboard.server.dao.customer.CustomerService;
import org.thingsboard.server.dao.dashboard.DashboardService;
import org.thingsboard.server.dao.dataMonitor.DataMonitorService;
import org.thingsboard.server.dao.device.DeviceAuthService;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.entityview.EntityViewService;
import org.thingsboard.server.dao.influx.InfluxService;
import org.thingsboard.server.dao.msg.JinzhouMsgSendService;
import org.thingsboard.server.dao.msgLog.DeviceLogService;
import org.thingsboard.server.dao.msgLog.MsgLogService;
import org.thingsboard.server.dao.notify.SystemNotifyService;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.relation.RelationService;
import org.thingsboard.server.dao.rule.RuleChainService;
import org.thingsboard.server.dao.station.StationAttrService;
import org.thingsboard.server.dao.station.StationService;
import org.thingsboard.server.dao.tenant.TenantService;
import org.thingsboard.server.dao.timeseries.TimeseriesService;
import org.thingsboard.server.dao.user.UserCredentialsDao;
import org.thingsboard.server.dao.user.UserService;

import java.util.Set;

/**
 * Created by ashvayka on 13.01.18.
 */
public interface TbContext {

    void tellNext(TbMsg msg, String relationType);

    void tellNext(TbMsg msg, String relationType, Throwable th);

    void tellNext(TbMsg msg, Set<String> relationTypes);

    void tellSelf(TbMsg msg, long delayMs);

    void tellFailure(TbMsg msg, Throwable th);

    void updateSelf(RuleNode self);

    TbMsg newMsg(String type, EntityId originator, TbMsgMetaData metaData, String data);

    TbMsg transformMsg(TbMsg origMsg, String type, EntityId originator, TbMsgMetaData metaData, String data);

    RuleNodeId getSelfId();

    TenantId getTenantId();

    AttributesService getAttributesService();

    CustomerService getCustomerService();

    TenantService getTenantService();

    UserService getUserService();

    DeviceService getDeviceService();

    DashboardService getDashboardService();

    AlarmService getAlarmService();

    RuleChainService getRuleChainService();

    RuleEngineRpcService getRpcService();

    RuleEngineTelemetryService getTelemetryService();

    TimeseriesService getTimeseriesService();

    BaseObtainDataService getBaseObtainDataService();

    DataMonitorService getDataMonitorService();

    RelationService getRelationService();

    EntityViewService getEntityViewService();

    ListeningExecutor getJsExecutor();

    ListeningExecutor getMailExecutor();

    ListeningExecutor getDbCallbackExecutor();

    ListeningExecutor getExternalCallExecutor();

    MailService getMailService();

    ScriptEngine createJsScriptEngine(String script, String... argNames);

    String getNodeId();

    RuleChainTransactionService getRuleChainTransactionService();

    AlarmJsonService getAlarmJsonService();

    UserCredentialsDao userCredentialsDao();

    DeviceAuthService getDeviceAuthService();

    MsgLogService getMsgLogService();

    DeviceLogService getDeviceLogService();

    InfluxService getInfluxService();

    StringRedisTemplate getStringRedisTemplate();

    AlarmRuleService getAlarmRuleService();

    AlarmRuleSmartService getAlarmRuleSmartService();

    AlarmCenterService getAlarmCenterService();

    StationAttrService getStationAttrService();

    StationService getStationService();

    AlarmCountService getAlarmCountService();

    SystemNotifyService getSystemNotifyService();

    JinzhouMsgSendService getJinzhouMsgSendService();

    String getMsgVersion();
}
