<template>
    <div class="topbar">
        <div class="mask" v-if="$route.path == '/' && showMask"></div>
        <div class="bg-wrap">
            <div class="nav-wrap">
                <div class="nav-block marginRight">
                    <div 
                        class="item-nav" 
                        :class="[activeMenu === 'production' ? 'active' : '']"
                        @click="switchView('production')"
                    >
                        生产一张图
                    </div>
                    <div 
                        class="item-nav" 
                        :class="[activeMenu === 'operations' ? 'active' : '']"
                    >
                    </div>
                    <div 
                        class="item-nav" 
                        :class="[activeMenu === 'quality' ? 'active' : '']"
                    >
                    </div>
                </div>
                <div class="title">
                    <span></span>
                </div>
                <div class="nav-block marginLeft">
                    <div 
                        class="item-nav" 
                        :class="[activeMenu === 'revenue' ? 'active' : '']"
                        @click="switchView('revenue')"
                    >
                        营收一张图
                    </div>
                    <div 
                        class="item-nav" 
                        :class="[activeMenu === 'assets' ? 'active' : '']"
                    >
                    </div>
                    <div 
                        class="item-nav" 
                        :class="[activeMenu === 'emergency' ? 'active' : '']"

                    >
                    </div>
                </div>

                <!-- <router-link to='/roadMaintenance' class="item-nav" :class="[active === 7 ? 'active' : '']">
                  道路养护
                  </router-link> -->
            </div>
            <div class="weather-warp">
                <img class="weatherImg" :src="this.img" onerror="this.src='../../assets/img/weather/102.png'" alt=""
                    v-if="weatherIcon" @click="weathertest">
                <div class="weatherSpan">
                    <span>{{ temperature }}</span>
                    <span>{{ weather }}</span>
                </div>
            </div>
            <div class="right-warp">
                <div class="date-wrap">
                    <div class="date1">
                        {{ date.time }}
                    </div>
                    <div class="date2">
                        <span>{{ date.week }}</span>
                        <!-- <br/> -->
                        <span>{{ date.day }}</span>
                    </div>
                </div>
                <div class="videoCall" @click="showVideoCall">
                    <div class="videoUserImg"></div>
                </div>
                <div class="set">
                     
                    <div class="img" @click="Unicon"></div>
                </div>

            </div>
        </div>
    </div>

</template>
  
<script>

export default {
    data() {
        return {
            date: {
                day: '',
                week: '',
                time: ''
            },
            w: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
            timer: null,
            weather: '',
            temperature: '',
            weatherIcon: '',
            i: 0,
            path: null,
            showMask: false,
            title: null,
            isShowVideoCallPage:false,
        }
    },
    components: {},
    props: {
        active: {
            type: Number,
            default: 1
        },
        activeMenu: {
            type: String,
            default: 'production'
        }
    },
    emits: ['change-view'],
    watch: {
        "$store.state.topBar": {
            immediate: true,
            handler(now) {
                this.title = now
            }
        }
    },
    computed: {
        img() {
            let weatherImg
            try {
                weatherImg = require('../../assets/img/weather/' + this.weatherIcon + '.png')
            } catch (err) {
                console.log(err)
                weatherImg = require('../../assets/img/weather/102.png')
            }
            return weatherImg
        }
    },
    created() {

    },
    mounted() {
        this.timer = setInterval(() => {
            var today = new Date();
            var month = today.getMonth() + 1;
            this.date.day = today.getFullYear() + '.' + month + '.' + today.getDate();
            this.date.week = this.w[today.getDay()];
            var minutes = today.getMinutes() < 10 ? '0' + today.getMinutes() : today.getMinutes();
            this.date.time = today.getHours() + ':' + minutes;
            //window.now = today.getFullYear()+'-'+ month + '-' +  today.getDate() + ' ' + today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
        }, 1000)
        //   getWeather({
        //       city: '高阳',
        //       extensions: 'base',
        //       key: '63b2abf7010d6d97f40d16d91f96bb97'
        //   }).then(res => {
        //       let todayWeather = res.lives[0];
        //       console.log(todayWeather);
        //       this.weatherIcon = todayWeather.weather;
        //       this.weather = todayWeather.weather;
        //       this.temperature = `${todayWeather.temperature}°C`;
        //   })

    },
    methods: {
        showVideoCall(){
            this.isShowVideoCallPage=true;
            setTimeout(() => {
                this.setLabelTitle();
            }, 50);
        },
        setLabelTitle(){
            let checkBoxLabelList=document.getElementsByClassName("el-checkbox__label");
            for(let i=0;i<checkBoxLabelList.length;i++){
                let tmpInerText = checkBoxLabelList[i].childNodes[1].innerText;
                if(checkBoxLabelList[i].childNodes[1].tagName=="span"||checkBoxLabelList[i].childNodes[1].tagName=="SPAN"){
                    if(tmpInerText.length>10){
                        checkBoxLabelList[i].childNodes[1].setAttribute('title',tmpInerText);
                    }
                }
            }
        },
        loginout() {
            this.$confirm('确定要退出该系统吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$router.push({
                    path: '/'
                })
                localStorage.clear()
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });
            })
        },
        skip() {
            window.open(emergencySystem);
        },
        showTheMask(res) {
            this.showMask = res
        },
        getTitle(title) {
            this.title = title
        },
        Unicon() {
            window.location.href = process.env.VUE_APP_YWTG_URL
        },
        switchView(view) {
            this.$emit('change-view', view);
        }
    },
    beforeUnmounted() {
        clearInterval(this.timer)
        this.timer = null
    }
}
</script>
  
<style lang="scss" scoped>
.mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100vw;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: -1;
}

.topbar {
    width: 100%;
    height: 72px;
    position: absolute;
    z-index: 99;
    top: 0;

    .bg-wrap {
        width: 100%;
        height: 100%;
        display: inline-block;
        background-image: url('../../assets/img/topbarBg.svg');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        backdrop-filter: blur(3px);
        color: #fff;
        font-size: 16px;

        .right-warp {
            right: 20px;
            top: 22px;
            height: 44px;
            position: absolute;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;

            .date-wrap {
                display: flex;
                flex-direction: row;

                .date1 {
                    width: 86px;
                    height: 44px;
                    font-size: 36px;
                    font-family: 'DINPro';
                    font-weight: 400;
                    color: #FFFFFF;
                    line-height: 44px;
                    margin-right: 20px;
                }

                .date2 {
                    font-size: 10px;
                    font-family: 'DINPro';
                    font-weight: 400;
                    color: #FFFFFF;
                    line-height: 16px;
                    display: flex;
                    /*弹性容器*/
                    flex-direction: column;
                    flex-wrap: wrap;
                    /*自动换行*/
                    align-content: center;
                    /*纵向对齐方式居中*/
                    margin: auto;
                    // &::after {
                    //     content: '';
                    //     width: 1px;
                    //     height: 28px;
                    //     // background: #f00;
                    //     background: white;
                    //     position:absolute;
                    //     top: 8px;
                    //     left: 180px;
                    // }
                }

                margin-right: 5px;
            }

            .set {
                width: 40px;
                height: 40px;
                background: rgba(0, 149, 255, 0.3);
                box-shadow: inset 0px 0px 4px 1px #0095FF;
                border-radius: 4px 4px 4px 4px;
                opacity: 1;
                border: 1px solid #0095FF;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-left: 15px;
                position: relative;
                z-index: 0;

                .img {
                    //padding:5px;
                    height: 35px;
                    width: 35px;
                    background: url('../../assets/img/Union.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    cursor: pointer;
                }

            }
            .videoCall{
                width: 40px;
                height: 40px;
                // background-color: #fff;
                cursor: pointer;
                .videoUserImg{
                    //padding:5px;
                    height: 43px;
                    width: 43px;
                    background: url('../../assets/img/users.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    cursor: pointer;
                }
            }

            .s {
                content: '';
                width: 1px;
                height: 28px;
                background: white;
                margin: auto;
            }
        }

        .weather-warp {
            margin-left: 24px;
            margin-top: 24px;
            width: 90px;
            display: flex;
            flex-direction: row;

            .weatherImg {
                margin: auto;
                width: 34px;
                height: 34px;
                cursor: pointer;
                background-repeat: no-repeat;
                background-size: cover;
            }

            .weatherSpan {
                //margin-left: 10px;
                font-size: 10px;
                font-family: Noto Sans SC-Regular, Noto Sans SC;
                font-weight: 400;
                color: #FFFFFF;
                line-height: 16px;
                display: flex;
                /*弹性容器*/
                flex-wrap: wrap;
                /*自动换行*/
                align-content: center;
                /*纵向对齐方式居中*/
                flex-direction: column;
                margin: auto;
                ;
            }
        }

        .account-wrap {
            margin-right: 15px;
            margin-top: 10px;
            display: flex;

            //   .bb {
            //       width: 24px;
            //       height: 24px;
            //       display: block;
            //       background-image: url('../../assets/img/close-icon.png');
            //       background-repeat: no-repeat;
            //       background-size: 100% 100%;
            //       margin-left: 10px;
            //       cursor: pointer;
            //       // margin-right: 15px;
            //   }

            //   .aa {
            //       width: 24px;
            //       height: 24px;
            //       display: block;
            //       background-image: url('../../assets/img/equipment-icon.png');
            //       background-repeat: no-repeat;
            //       background-size: 100% 100%;
            //       margin-left: 10px;
            //       cursor: pointer;
            //       // margin-right: 15px;
            //   }
        }

        .nav-wrap {
            width: 1410px;
            // height: 48px;
            top: 17px;
            left: 50%;
            transform: translateX(-50%);
            position: absolute;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-end;

            //background: transparent;//linear-gradient(rgba(0,0,0,1)0%,rgba(0,0,0,0.5)66%, rgba(0,0,0,0)99%);
            .title {
                display: flex;
                flex-direction: column;
                align-items: center;

                span {
                    font-family: 'MStiffHei PRC';
                    font-style: normal;
                    font-weight: 400;
                    font-size: 44px;
                    line-height: 48px;
                    height: 56px;
                    color: #FFFFFF;
                    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
                }
            }

            .nav-block {
                display: flex;

                .item-nav {
                    display: block;
                    height: 43px;
                    margin: 0 16.5px;
                    line-height: 28px;
                    font-size: 24px;
                    text-align: center;
                    font-family: 'MStiffHeiPRC-Bold', sans-serif;
                    // font-weight: 400;
                    color: rgba(255, 255, 255, 0.8);
                    cursor: pointer;

                    &:hover {
                        color: #01FFFF;
                    }
                }

                .active {
                    background-image: url('../../assets/img/Group 1000002320.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    color: #01FFFF;
                }
            }
        }

        // &::before{
        //   content: '';
        //   display: block;
        //   height: 100%;
        //   width: 100%;
        //   backdrop-filter: blur(3px);
        //   z-index: -1000000;
        // }
    }

    .decoration {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-49.80%);
        width: 47.2%;
        height: 94px;
        z-index: -99999;
    }

}
</style>
  