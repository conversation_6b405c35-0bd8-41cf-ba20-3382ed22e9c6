import * as geoprocessor from '@arcgis/core/rest/geoprocessor';
import * as print from '@arcgis/core/rest/print';
import LinearUnit from '@arcgis/core/rest/support/LinearUnit';
import PrintParameters from '@arcgis/core/rest/support/PrintParameters';
import { useGisStore } from '@/store';

/**
 * 向焊管GP服务发送任务
 * @param layerdbname 图层字段名
 * @param oid 分析的OBJECTID
 * @returns Promise<Jobinfo>
 */
export const submitBurstAnalysGPJob = (layerdbname, oid) => {
  return geoprocessor.submitJob(
    window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisBurstGPService,
    {
      pipefeatureclassname: layerdbname,
      pipeobjectid: oid,
      bysource: true,
      usertoken: useGisStore().gToken
    }
  );
};
/**
 * 向关阀服务发送任务
 * @param oids 分析的OBJECTID数组
 * @returns Promise<Jobinfo>
 */
export const submitShutValvesGPJob = (params: any) => {
  return (geoprocessor as __esri.geoprocessor).submitJob(
    window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisShutValveAnalysGPService,
    {
      ...(params || {})
    }
  );
};
/**
 * 向拓展关阀服务发送任务
 * @param layerdbname 图层字段名
 * @param oids 分析的OBJECTID数组
 * @returns Promise<Jobinfo>
 */
export const submitExtendShutValvesGPJob = (params: any) => {
  return geoprocessor.submitJob(
    window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisExtendShutValveAnalysGPService,
    {
      ...(params || {})
    }
  );
};
export const submitConnectAnalysGPJob = (
  layerdbname: string,
  oid: any,
  applystop?: boolean
) => {
  return geoprocessor.submitJob(
    window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisConnectGPService,
    {
      pipefeatureclassname: layerdbname,
      pipeobjectid: oid,
      applystop,
      usertoken: useGisStore().gToken,
      versioned: false
    }
  );
};
export const submitGPJob = (url: string, params: any) => {
  return geoprocessor.submitJob(url, params);
};
export const excuteGPJob = (url: string, featureSet: any[], options: any) => {
  const params = {
    Input_Observation_Point: featureSet,
    Viewshed_Distance: new LinearUnit({
      distance: 5,
      units: 'meters'
    })
  };
  return geoprocessor.execute(url, params, options);
};

/**
 * 执行打印任务
 * @param view
 * @param template 打印模板
 * @returns
 */
export const excutePrintTask = (
  view?: __esri.MapView,
  template?: __esri.PrintTemplate
) => {
  if (!view || !template) {
    console.log('未传递参数');
  }
  const url =
    window.SITE_CONFIG.GIS_CONFIG.gisUtilitiesService +
    window.SITE_CONFIG.GIS_CONFIG.gisPrintingToolsGPService +
    '/Export%20Web%20Map%20Task';
  const params = new PrintParameters({
    view,
    template,
    outSpatialReference: view?.spatialReference
  });
  return print.execute(decodeURI(url), params);
};
