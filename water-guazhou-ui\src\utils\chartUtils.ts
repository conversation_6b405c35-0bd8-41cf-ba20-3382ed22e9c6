export const hexToRgba = (hex, opacity) => {
  let rgbaColor = '';
  const reg = /^#[\da-f]{6}$/i;
  if (reg.test(hex)) {
    rgbaColor = `rgba(${parseInt(`0x${hex.slice(1, 3)}`)},${parseInt(
      `0x${hex.slice(3, 5)}`
    )},${parseInt(`0x${hex.slice(5, 7)}`)},${opacity})`;
  }
  return rgbaColor;
};
export const rgbToHex = (r: number, g: number, b: number) => {
  const hex = ((r << 16) | (g << 8) | b).toString(16);
  return '#' + new Array(Math.abs(hex.length - 7)).join('0') + hex;
};
export const hexToRgb = (hex: string) => {
  const rgb: number[] = [];
  for (let i = 1; i < 7; i += 2) {
    rgb.push(parseInt('0x' + hex.slice(i, i + 2)));
  }
  return rgb;
};
export const isRgb = (rgb?: string) => {
  return !!rgb?.startsWith('rgb');
};
export const isHex = (hex?: string) => {
  return !!hex?.startsWith('#');
};
export const gradient = (
  startColor: string,
  endColor: string,
  count: number
) => {
  // 将 hex 转换为rgb
  const sColor = hexToRgb(startColor);
  const eColor = hexToRgb(endColor);

  // 计算R\G\B每一步的差值
  const rStep = (eColor[0] - sColor[0]) / count;
  const gStep = (eColor[1] - sColor[1]) / count;
  const bStep = (eColor[2] - sColor[2]) / count;

  const gradientColorArr: string[] = [];
  for (let i = 0; i < count; i++) {
    // 计算每一步的hex值
    gradientColorArr.push(
      rgbToHex(
        parseInt((rStep * i + sColor[0]).toString()),
        parseInt((gStep * i + sColor[1]).toString()),
        parseInt((bStep * i + sColor[2]).toString())
      )
    );
  }
  return gradientColorArr;
};
