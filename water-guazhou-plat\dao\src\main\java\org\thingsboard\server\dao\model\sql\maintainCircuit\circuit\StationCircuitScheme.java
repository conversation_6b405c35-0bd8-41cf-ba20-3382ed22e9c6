package org.thingsboard.server.dao.model.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_STATION_CIRCUIT_SCHEME_TABLE)
@TableName(ModelConstants.TB_STATION_CIRCUIT_SCHEME_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class StationCircuitScheme {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_SCHEME_NAME)
    private String name;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_SCHEME_CONTENT)
    private String content;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_SCHEME_CREATE_USER)
    private String createUser;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_SCHEME_STATION_TYPE)
    private String stationType;

    @Transient
    @TableField(exist = false)
    private String createUserName;

}
