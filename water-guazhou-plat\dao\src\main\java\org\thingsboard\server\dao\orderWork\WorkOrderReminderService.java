package org.thingsboard.server.dao.orderWork;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.WorkOrderReminderRequest;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderReminder;

public interface WorkOrderReminderService {

    WorkOrderReminder save(WorkOrderReminder workOrderReminder);

    PageData<WorkOrderReminder> getList(WorkOrderReminderRequest workOrderReminderRequest);

    void softDelete(String id);

    void delete(String id);
}
