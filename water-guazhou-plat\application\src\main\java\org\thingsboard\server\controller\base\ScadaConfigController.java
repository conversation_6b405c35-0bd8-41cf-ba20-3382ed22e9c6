package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.dao.largeScreen.ScadaConfigService;
import org.thingsboard.server.dao.model.sql.ScadaConfig;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

@RestController
@RequestMapping("/api/scadaConfig")
public class ScadaConfigController extends BaseController {

    @Autowired
    private ScadaConfigService scadaConfigService;

    @GetMapping("list")
    public IstarResponse findList() {
        return IstarResponse.ok(scadaConfigService.findList());
    }

    @GetMapping("{id}")
    public IstarResponse findByCode(@PathVariable String id) {
        return IstarResponse.ok(scadaConfigService.findByType(id));
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody ScadaConfig entity) {
        scadaConfigService.save(entity);
        return IstarResponse.ok();
    }

}
