package org.thingsboard.server.dao.sql.report;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.WaterReport;

import java.util.Date;
import java.util.List;

/**
 * 水质报表填报
 */
public interface WaterReportRepository extends JpaRepository<WaterReport, String> {
    List<WaterReport> findByTimeBetweenOrderByTimeAsc(Date monthStart, Date monthEnd);
}
