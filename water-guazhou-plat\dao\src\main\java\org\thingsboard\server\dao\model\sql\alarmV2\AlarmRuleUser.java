package org.thingsboard.server.dao.model.sql.alarmV2;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 报警Version2 报警规则
 */
@Data
@TableName("tb_alarm_rule_user")
@NoArgsConstructor
@AllArgsConstructor
public class AlarmRuleUser implements Serializable {

    @TableId
    private String id;

    private String mainId;

    private String userId;

    private String type;

    private Date createTime;

}
