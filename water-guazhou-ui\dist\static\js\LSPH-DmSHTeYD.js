import{d as f,r as u,o as b,ay as d,g as h,n as y,q as _,i as x,a7 as g,C as v}from"./index-r0dFAfgr.js";import"./index-0NlGN6gS.js";import{c as S}from"./statistics-CeyexT_5.js";const w={class:"chart"},L=f({__name:"LSPH",setup(z){const n=(o=[],e=[])=>{const s=Math.max(...o)||100,c=e.map((a,t)=>({name:a,value:o[t],num:o[t]})),p=e.map((a,t)=>({name:a,value:s,label:{show:!0,position:"right",fontSize:14,color:"#fff",offset:[16,0],formatter(){return o[t]}}})),m=e.map((a,t)=>({name:a,value:o[t],label:o[t]}));return{tooltip:{axisPointer:{type:"shadow"}},grid:{top:20,left:20,right:60,bottom:20,containLabel:!0},xAxis:{type:"value",splitLine:{show:!1},axisLine:{show:!1},axisLabel:{show:!1},axisTick:{show:!1},position:"top"},yAxis:{type:"category",data:e,inverse:!0,axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"rgba(255,255,255,0.85)",fontSize:14,fontFamily:"TencentSans"}}},dataZoom:[{type:"slider",show:!1,startValue:0,endValue:5,yAxisIndex:[0]},{type:"inside",yAxisIndex:[0],zoomOnMouseWheel:!1,moveOnMouseMove:!0,moveOnMouseWheel:!0}],series:[{type:"bar",barGap:"-100%",barWidth:14,z:1,itemStyle:{color:new g(0,0,1,0,[{offset:0,color:"rgba(0,255,255,1)"},{offset:1,color:"rgba(255,0,0,1)"}],!1)},data:c},{type:"bar",barWidth:14,z:0,itemStyle:{color:"rgba(26, 49, 99, 1)"},tooltip:{show:!1},data:p},{type:"pictorialBar",symbolRepeat:"fixed",symbolMargin:6,symbol:"rect",z:2,symbolClip:!0,symbolSize:[1,14],symbolPosition:"start",itemStyle:{color:"rgba(0,0,0,1)"},data:m}]}},r=u({option:n()}),l=()=>{S().then(o=>{const{x:e,y:s}=o.data.data||{};r.option=n(s||[],e||[])}).catch(()=>{r.option=n()})};return b(()=>{l()}),(o,e)=>{const s=d("VChart");return h(),y("div",w,[_(s,{option:x(r).option},null,8,["option"])])}}}),P=v(L,[["__scopeId","data-v-673a83ca"]]);export{P as default};
