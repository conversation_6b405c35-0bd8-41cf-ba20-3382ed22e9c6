import{_ as x}from"./index-DyRO7tCT.js";import{_ as y}from"./CardTable-rdWOL4_6.js";import{_ as k}from"./CardSearch-CB_HNR-Q.js";import B from"./menuAssign-BOMLsYx0.js";import{d as C,M as S,c as l,r as T,bu as w,n as A,q as p,h as u,an as d,c9 as O,cp as U,g as s}from"./index-r0dFAfgr.js";import"./index-qoWsDjz-.js";import"./index-B69llYYW.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";import"./URLHelper-B9aplt5w.js";import"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CaaU9niG.js";const V={class:"wrapper"},H=C({__name:"index",setup(D){const{$messageError:m,$messageSuccess:f,$confirm:g}=S(),r=l(null),_=l({filters:[{label:"搜索",field:"keyWord",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>o()},{perm:!0,text:"添加",click:()=>{t.value.defaultValue={},t.value.visible=!0}}]}],defaultParams:{}}),a=T({loading:!1,dataList:[],columns:[{prop:"appName",label:"名称"},{prop:"additionalInfo",label:"备注"}],operations:[{text:"菜单赋予",isTextBtn:!0,perm:!0,icon:"iconfont icon-guanli",click:e=>{i.value.currentId=e.id,i.value.visible=!0}},{text:"编辑",isTextBtn:!0,perm:!0,icon:"iconfont icon-baogao",click:e=>{t.value.defaultValue=e,t.value.visible=!0}},{text:"删除",isTextBtn:!0,perm:!0,icon:"iconfont icon-shanchu",click:e=>v(e)}],operationWidth:"260px",pagination:{page:1,limit:20,total:0,layout:"total, prev, pager, next, sizes, jumper",handleSize:e=>{a.pagination.limit=e,o()},handlePage:e=>{a.pagination.page=e,o()}}}),t=l({visible:!1,title:"应用操作",close:()=>t.value.visible=!1,addUrl:"api/app/type",editUrl:"api/app/type/edit",defaultValue:{},externalParams:{},columns:[{type:"input",label:"应用名称",key:"appName",rules:[{required:!0,message:"请填写应用名称"}]},{type:"textarea",label:"备注",key:"additionalInfo",rows:3}]}),o=async e=>{a.loading=!0;const c={page:a.pagination.page,size:a.pagination.limit};!e&&r.value&&Object.assign(c,r.value.queryParams);try{const n=await O();console.log(n,"getAppList"),a.loading=!1,n.status===200?(a.dataList=n.data,a.pagination.total=n.data.length):m("获取失败")}catch{a.loading=!1}},v=e=>{g("确定删除指定企业?","删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{U(e.id).then(()=>{f("操作成功"),o()})})},i=l({currentId:"",visible:!1,close:()=>i.value.visible=!1});return w(()=>{o(!0)}),(e,c)=>{const n=k,b=y,h=x;return s(),A("div",V,[p(n,{ref_key:"cardSearch",ref:r,class:"cardSearch",config:_.value},null,8,["config"]),p(b,{class:"card-table",config:a},null,8,["config"]),t.value.visible?(s(),u(h,{key:0,ref:"addOrUpdate",config:t.value,"dialog-width":"560px",onRefreshData:o},null,8,["config"])):d("",!0),i.value.visible?(s(),u(B,{key:1,config:i.value},null,8,["config"])):d("",!0)])}}});export{H as default};
