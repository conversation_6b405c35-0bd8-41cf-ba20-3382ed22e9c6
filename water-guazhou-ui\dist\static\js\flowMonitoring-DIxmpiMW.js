import{d as O,c as P,r as F,s as S,o as $,am as z,j as E,g as Y,n as H,q as I,i as C,t as J,p as K,_ as Q,aq as X,C as Z}from"./index-r0dFAfgr.js";import{w as aa}from"./Point-WxyopZva.js";import{G as ta}from"./onemap-CEunQziB.js";import{P as U,C as ea}from"./index-CcDafpIP.js";import{r as j}from"./chart-wy3NEK2T.js";import{g as na}from"./URLHelper-B9aplt5w.js";const sa={class:"onemap-panel-wrapper"},la={class:"table-box"},oa=O({__name:"flowMonitoring",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(q,{emit:A}){const D=A,w=q,m=P([{label:"0 个",value:"流量计总数"},{label:"0 %",value:"报警率"}]),x=P(),g=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],k=F({group:[{id:"chart",fieldset:{desc:"监测状态统计",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:j(),style:{height:"150px"}}]},{id:"tab",fields:[{type:"input",field:"name",appendBtns:[{perm:!0,text:"刷新",click:()=>i(!0)}],onChange:()=>i()},{type:"tabs",field:"type",tabs:[{label:"全部",value:"all"},...g.map(t=>({...t,value:t.name}))],tabType:"simple",onChange:()=>i()}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),o=F({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:t,size:s})=>{o.pagination.page=t,o.pagination.limit=s},layout:"total,sizes, jumper"},handleRowClick:t=>G(t),columns:[{minWidth:200,label:"名称",prop:"name",sortable:!0},{minWidth:80,label:"状态",prop:"status",formatter:t=>{var s;return((s=g.find(r=>r.name===t.status))==null?void 0:s.label)||t.status}},{minWidth:160,label:"更新时间",prop:"lastTime",sortable:!0}]}),M=F({dataList:[],columns:[],pagination:{refreshData:({page:t,size:s})=>{M.pagination.page=t,M.pagination.limit=s,i()}}}),i=async t=>{var s,r,b,h,T,V,L,R,W;o.loading=!0;try{const v=(s=x.value)==null?void 0:s.dataForm.type,_=await ta({name:(r=x.value)==null?void 0:r.dataForm.name,status:v==="all"?"":v});o.dataList=((b=_.data)==null?void 0:b.data)||[];const B=k.group[0].fields[0],y=((T=(h=_.data)==null?void 0:h.data)==null?void 0:T.length)||0,N=[],p=[];if((L=(V=_.data)==null?void 0:V.data)!=null&&L.map(a=>{var u,f,d;const e=(u=a.location)==null?void 0:u.split(",");if((e==null?void 0:e.length)===2){const c=new aa({longitude:e[0],latitude:e[1],spatialReference:(f=w.view)==null?void 0:f.spatialReference});N.push({id:a.stationId,visible:!1,x:c.x,y:c.y,offsetY:-40,title:a.name,customComponent:S(U),customConfig:{info:{type:"attrs",imageUrl:a.imgs,stationId:a.stationId}},attributes:{path:w.menu.path,id:a.stationId,row:a},symbolConfig:{url:na(((d=a.name)==null?void 0:d.indexOf("热"))!==-1?"测流压站.png":"测流点.png")}})}let n=p.find(c=>c.name===a.status);const{label:l}=g.find(c=>c.name===a.status)||{};n?n.value++:(n={name:a.status,nameAlias:l,value:1,scale:"0%"},p.push(n))}),p.map(a=>{var e,n,l;return a.scale=y===0?"0%":(Number(a.value)/y*100).toFixed(2)+"%",a.value=((l=(n=(e=_.data)==null?void 0:e.data)==null?void 0:n.filter(u=>u.status===a.name))==null?void 0:l.length)||0,a}),t){const a=(R=k.group.find(e=>e.id==="tab"))==null?void 0:R.fields[1];if(a){a.tabs=a.tabs.map(n=>{var f;const l=p.find(d=>d.name===n.value),u=((f=g.find(d=>d.name===n.value))==null?void 0:f.label)||"";return n.label=u+"("+((l==null?void 0:l.value)||0)+")",n});const e=a.tabs.find(n=>n.value==="all");e&&(e.label="全部("+y+")"),B&&(B.option=j(p,"个","",0))}m.value[0].label=y+"个",m.value[1].label=((W=p.find(e=>e.name==="alarm"))==null?void 0:W.scale)||"0 %"}D("addMarks",{windows:N,customWinComp:S(U)})}catch(v){console.dir(v)}o.loading=!1},G=async t=>{D("highlightMark",w.menu,t==null?void 0:t.stationId)};return $(()=>{i(!0)}),z(()=>E().isDark,()=>i(!0)),(t,s)=>{const r=Q,b=X;return Y(),H("div",sa,[I(C(ea),{modelValue:C(m),"onUpdate:modelValue":s[0]||(s[0]=h=>J(m)?m.value=h:null),span:12},null,8,["modelValue"]),I(r,{ref_key:"refForm",ref:x,config:C(k)},null,8,["config"]),K("div",la,[I(b,{config:C(o)},null,8,["config"])])])}}}),ma=Z(oa,[["__scopeId","data-v-24778764"]]);export{ma as default};
