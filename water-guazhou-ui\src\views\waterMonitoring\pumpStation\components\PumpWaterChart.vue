<template>
  <div class="chart-container">
    <div class="chart-title">泵水量与其他量</div>
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Array,
    default: () => []
  }
})

const chartRef = ref(null)
let chart = null

const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chart) return
  
  const data = props.chartData
  const stationNames = data.map(item => item.stationName)
  const pumpWaterData = data.map(item => item.pumpWater)
  const otherWaterData = data.map(item => item.otherWater)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['中水量', '其他量'],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: stationNames
    },
    yAxis: {
      type: 'value',
      name: '水量(m³)'
    },
    series: [
      {
        name: '中水量',
        type: 'bar',
        data: pumpWaterData,
        itemStyle: {
          color: '#5470c6'
        },
        label: {
          show: true,
          position: 'top'
        }
      },
      {
        name: '其他量',
        type: 'bar',
        data: otherWaterData,
        itemStyle: {
          color: '#91cc75'
        },
        label: {
          show: true,
          position: 'top'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

watch(() => props.chartData, () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initChart()
  
  window.addEventListener('resize', () => {
    chart && chart.resize()
  })
})
</script>

<style scoped lang="scss">
.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .chart-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .chart {
    flex: 1;
  }
}
</style>
