package org.thingsboard.server.dao.model.sql.smartProduction.dispatch;

import org.thingsboard.server.dao.util.imodel.response.NameDisplayableEnum;

public enum MessageRecordStatus implements NameDisplayableEnum {
    PENDING("待发送"),
    SENDING("正在发送"),
    FAILURE("失败"),
    SUCCESS("成功");

    private final String displayName;

    MessageRecordStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

}
