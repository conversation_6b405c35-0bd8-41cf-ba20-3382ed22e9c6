import axios from 'axios'
export const queryXZQH = (params: {
  searchWord?: string
  searchType?: string
  needSubInfo?: number
  needAll?: number
  needPolygon?: number
  needPre?: number
}) => {
  const paramsJson = JSON.stringify({
    searchWord: '',
    searchType: '1',
    needSubInfo: '',
    needAll: '',
    needPolygon: '',
    needPre: '',
    ...params
  })
  const url = `http://api.tianditu.gov.cn/administrative?postStr=${paramsJson}&tk=${window.SITE_CONFIG.GIS_CONFIG.gisTdtToken}`
  return axios.get(url)
}
export const queryGeocoder = (params: { keyWord?: string }) => {
  const paramsJson = JSON.stringify({
    ...params
  })
  const url = `http://api.tianditu.gov.cn/geocoder?ds=${paramsJson}&tk=${window.SITE_CONFIG.GIS_CONFIG.gisTdtToken}`
  return axios.get(url)
}

export const queryTdt = (params: ITDTSearchParams) => {
  const paramsJson = JSON.stringify({
    mapBound: '-180,-90,180,90',
    queryType: 1,
    level: 12,
    // specify: '156340000',
    count: 100,
    start: 0,
    ...params
  })
  const url = `http://api.tianditu.gov.cn/v2/search?postStr=${paramsJson}&type=query&tk=${window.SITE_CONFIG.GIS_CONFIG.gisTdtToken}`
  return axios.get(url)
}

export const InverseGeocoding = (val:{
  lon:string
  lat:string
}) => {
  return axios.get('http://api.tianditu.gov.cn/geocoder', {
    params: {
      postStr: {
        ...val,
        ver: '1'
      },
      tk: window.SITE_CONFIG.GIS_CONFIG.gisTdtToken
    }
  })
}
