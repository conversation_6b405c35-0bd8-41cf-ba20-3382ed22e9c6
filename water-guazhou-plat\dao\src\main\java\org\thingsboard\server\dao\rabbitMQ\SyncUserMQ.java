package org.thingsboard.server.dao.rabbitMQ;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class SyncUserMQ {

    @Autowired
    private RabbitTemplate rabbitTemplate;
    private String EXCHANGE_SYNC_USER = "exchangeSyncUser";
    private String ROUTING_KEY_ADD_USER = "add_user";
    private String ROUTING_KEY_UPDATE_USER = "update_user";
    private String ROUTING_KEY_DELETE_USER = "delete_user";

    public void sendAddUser(String message) {
        rabbitTemplate.convertAndSend(EXCHANGE_SYNC_USER, ROUTING_KEY_ADD_USER, message);
    }

    public void sendUpdateUser(String message) {
        rabbitTemplate.convertAndSend(EXCHANGE_SYNC_USER, ROUTING_KEY_UPDATE_USER, message);
    }

    public void sendDeleteUser(String message) {
        rabbitTemplate.convertAndSend(EXCHANGE_SYNC_USER, ROUTING_KEY_DELETE_USER, message);
    }
}
