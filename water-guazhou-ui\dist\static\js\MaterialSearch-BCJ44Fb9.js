import{d as C,c as g,r as O,b as c,bB as L,o as _,g as S,n as h,q as b,i as n,_ as P,C as k}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import{e as x,i as A}from"./QueryHelper-ILO3qZqg.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import B from"./PipeDetail-CTBPYFJW.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./DateFormatter-Bm9a68Ax.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./config-fy91bijz.js";const E=C({__name:"MaterialSearch",props:{view:{},telport:{}},setup(v){const s=v,a=g(),u=g(),t=O({pipeLayerOption:[],curOperate:"",tabs:[],materails:["PVC","PE","钢","钢筋混凝土","PP","UPVC","ABS","GPR","PPR"]}),d=O({group:[{fieldset:{desc:"图层名称"},fields:[{type:"select",field:"layer",options:[]}]},{fieldset:{desc:"材质"},fields:[{type:"tree",options:t.materails.map(e=>({label:e,value:e,id:e})),multiple:!0,showCheckbox:!0,field:"MATERIAL",onChange:e=>{console.log(e)}},{type:"btn-group",itemContainerStyle:{marginBottom:"5px"},btns:[{perm:!0,loading:()=>t.curOperate==="detailing",text:"全选",click:()=>f("all"),styles:{width:"100%"}},{perm:!0,loading:()=>t.curOperate==="detailing",text:"全不选",click:()=>f("none"),styles:{width:"100%"}}]},{type:"btn-group",btns:[{perm:!0,loading:()=>t.curOperate==="detailing",text:()=>t.curOperate==="detailing"?"正在查询":"查询",click:()=>F(),type:"success",styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12}),f=e=>{var r;(r=a.value)!=null&&r.dataForm&&(a.value.dataForm.MATERIAL=e==="all"?t.materails:[])},w=()=>{var p,m,l,i;if(!s.view)return;const e=(p=s.view)==null?void 0:p.map.findLayerById("pipelayer");t.pipeLayerOption=[],(m=e==null?void 0:e.sublayers)==null||m.map(o=>{var y;(y=t.pipeLayerOption)==null||y.push({label:o.title,value:o.title,id:o.id})});const r=d.group[0].fields[0];r&&(r.options=t.pipeLayerOption),(l=a.value)!=null&&l.dataForm&&(a.value.dataForm.layer=t.pipeLayerOption&&((i=t.pipeLayerOption[0])==null?void 0:i.value))},F=async()=>{var m,l;const e=t.pipeLayerOption.find(i=>{var o;return i.label===((o=a.value)==null?void 0:o.dataForm.layer)});if(!e){c.warning("请选择图层"),t.curOperate="";return}const r=(l=(m=a.value)==null?void 0:m.dataForm)==null?void 0:l.MATERIAL;if(!(r!=null&&r.length)){c.warning("请选择材质"),t.curOperate="";return}let p="";r.map((i,o)=>{o>0&&(p+=" or "),p+="MATERIAL='"+i+"'"});try{t.curOperate="detailing";const i=await x(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+e.id,A({returnGeometry:!1,where:p,orderByFields:["OBJECTID asc"]}));i!=null&&i.length?(t.tabs=[{label:e.label,name:e.label,id:e.id,data:i}],L(()=>{var o;(o=u.value)==null||o.openDialog()})):(c.info("查询结果为空"),t.curOperate="",t.tabs=[])}catch{c.error("查询失败，请检查查询条件是否正确"),t.curOperate=""}},I=async()=>{var e;s.view&&((e=u.value)==null||e.extentTo(s.view))};return _(()=>{w()}),(e,r)=>{const p=P;return S(),h("div",null,[b(p,{ref_key:"refForm",ref:a,config:n(d)},null,8,["config"]),b(B,{ref_key:"refDetail",ref:u,tabs:n(t).tabs,telport:e.telport,onRefreshed:r[0]||(r[0]=()=>n(t).curOperate="viewingDetail"),onRefreshing:r[1]||(r[1]=()=>n(t).curOperate="detailing"),onClose:r[2]||(r[2]=m=>n(t).curOperate=""),onRowdblclick:I},null,8,["tabs","telport"])])}}}),re=k(E,[["__scopeId","data-v-33762395"]]);export{re as default};
