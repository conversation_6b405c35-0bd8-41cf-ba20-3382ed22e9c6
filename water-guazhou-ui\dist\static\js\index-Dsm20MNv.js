import{_ as w}from"./TreeBox-DDD2iwoR.js";import{_ as I}from"./CardTable-rdWOL4_6.js";import{_ as k}from"./CardSearch-CB_HNR-Q.js";import{_ as D}from"./index-BJ-QPYom.js";import{d as N,c as b,r as h,l as _,bI as S,bH as x,b as y,o as P,g as M,h as j,F as L,q as c,i as d,C as q}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as R}from"./usePartition-DkcY9fQ2.js";import"./index-0NlGN6gS.js";import{_ as B}from"./NewOrder.vue_vue_type_script_setup_true_lang-DAnrmOVe.js";import{E}from"./index-Bo22WWST.js";import{e as H,f as A}from"./lossControl-DNefZk8I.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./FormMap-BGaXSqQF.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./utils-D5nxoMq3.js";import"./useUser-Blb5V02j.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const F=N({__name:"index",setup(V){const v=b(),u=b(),a=h({data:[],loading:!0,title:"选择分区",expandOnClickNode:!1,treeNodeHandleClick:t=>{a.currentProject!==t&&(a.currentProject=t,p())}}),g=(t,r,i)=>{i.hidden=t.type!==i.field},T=h({defaultParams:{month:_().format(S),day:[_().subtract(1,"M").format(x),_().format(x)],type:"month"},filters:[{type:"radio-button",label:"选择方式",field:"type",options:[{label:"按日期",value:"day"},{label:"按月",value:"month"}]},{type:"month",label:"",field:"month",handleHidden:g},{type:"daterange",label:"",field:"day",handleHidden:g},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",disabled:()=>!!e.loading,click:()=>p()},{type:"default",perm:!0,text:"重置",iconifyIcon:"ep:refresh",click:()=>{var t;(t=u.value)==null||t.resetForm()}},{perm:!0,text:"工单",type:"success",iconifyIcon:"ep:plus",click:()=>{var t,r;(t=e.selectList)!=null&&t.length?e.selectList.length>1?y.warning("只能选择一条数据"):(r=W.value)==null||r.openDialog():y.warning("请选择一条数据")}},{perm:!0,type:"warning",text:"导出",iconifyIcon:"ep:download",disabled:()=>!!e.loading,click:()=>{p(!0)}}]}]}),e=h({dataList:[],indexVisible:!0,rowKey:"partitionId",columns:[{prop:"partitionName",label:"分区名称",minWidth:180},{prop:"name2",label:"抄表情况",minWidth:120,align:"center",subColumns:[{prop:"needCopyNum",label:"应抄户",minWidth:120,sortable:!0},{prop:"realCopyNum",label:"实抄户",minWidth:120,sortable:!0},{prop:"notCopyNum",label:"未抄户",minWidth:120,sortable:!0},{prop:"copyRate",label:"抄表率",unit:"(%)",minWidth:120,sortable:!0}]},{prop:"name3",label:"实抄水量分析",minWidth:120,align:"center",subColumns:[{prop:"supplyTotal",label:"供水量(m³)",minWidth:160,sortable:!0},{prop:"realCopyWater",label:"实抄水量(m³)",minWidth:180,sortable:!0},{prop:"nrwWater",label:"产销差水量(m³)",minWidth:180,sortable:!0},{prop:"nrwRate",label:"产销差",unit:"(%)",minWidth:140,sortable:!0}]},{prop:"name4",label:"校准水量分析",minWidth:120,align:"center",subColumns:[{prop:"correctSupplyTotal",label:"校准供水量(m³)",minWidth:180,sortable:!0},{prop:"correctCopyWater",label:"校准用水量(m³)",minWidth:180,sortable:!0},{prop:"correctNrwWater",label:"校准产销差水量(m³)",minWidth:180,sortable:!0},{prop:"correctNrwRate",label:"校准产销差",unit:"(%)",minWidth:160,sortable:!0}]},{prop:"name5",label:"表观漏失分析",align:"center",subColumns:[{prop:"referenceLossWater",label:"参考漏失水量(m³)",minWidth:200,sortable:!0},{prop:"faceLossWater",label:"表观漏失水量(m³)",minWidth:200,sortable:!0}]}],singleSelect:!0,handleSelectChange(t){e.selectList=t},select(t){var r;(r=e.selectList)!=null&&r.length&&e.selectList.findIndex(i=>i.partitionId===t.partitionId)!==-1?e.selectList=[]:e.selectList=[t]},pagination:{hide:!0,refreshData({page:t,size:r}){e.pagination.page=t||1,e.pagination.limit=r||20,p()}}}),W=b(),p=async t=>{var r,i,m,l,s;if(!a.currentProject){y.warning("请选择分区");return}e.loading=!0;try{const o=((r=u.value)==null?void 0:r.queryParams)||{},n={date:o.type==="month"?o.month:void 0,partitionId:(i=a.currentProject)==null?void 0:i.value,type:o.type,start:o.type==="day"?(m=o.day)==null?void 0:m[0]:void 0,end:o.type==="day"?(l=o.day)==null?void 0:l[1]:void 0};if(t){const f=await H(n);E(f.data,"总分表差评估")}else{const f=await A(n);e.dataList=((s=f.data)==null?void 0:s.data)||[]}}catch(o){console.log(o)}e.loading=!1},C=R();return P(async()=>{await C.getTree(),a.data=C.Tree.value,a.currentProject=a.data[0],p()}),(t,r)=>{const i=D,m=k,l=I,s=w;return M(),j(s,null,{tree:L(()=>[c(i,{ref:"refTree","tree-data":d(a)},null,8,["tree-data"])]),default:L(()=>{var o,n;return[c(m,{ref_key:"refSearch",ref:u,config:d(T)},null,8,["config"]),c(l,{ref_key:"refTable",ref:v,class:"card-table",config:d(e)},null,8,["config"]),c(B,{ref_key:"refDialog",ref:W,"default-values":{partitionId:(n=(o=d(e).selectList)==null?void 0:o[0])==null?void 0:n.partitionId}},null,8,["default-values"])]}),_:1})}}}),Ie=q(F,[["__scopeId","data-v-412220aa"]]);export{Ie as default};
