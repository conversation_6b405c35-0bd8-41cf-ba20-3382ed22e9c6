import s from"./PatrolDetail-3FfvS_c4.js";import{d as c,r as f,bu as l,i as r,g as u,h as d,an as w,bB as _}from"./index-r0dFAfgr.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./Videor.vue_vue_type_script_setup_true_lang-EsHlP83o.js";import"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import"./plan-BLf3nu6_.js";import"./circuitTaskFormRecord-CjbtiPXk.js";import"./config-C9CMv0E7.js";import"./FeatureHelper-Da16o0mu.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./QueryHelper-ILO3qZqg.js";import"./useWaterPoint-Bv0z6ym6.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./useDistrict-B4Fis32p.js";import"./area-Bpl-8n1R.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";import"./useCTI-CrDoUkpT.js";const Ut=c({__name:"InspectionMaintenanceDetail",emits:["refresh"],setup(h,{expose:p,emit:e}){const a=e,t=f({mounted:!1}),i={};return p({refreshDetail:async(o,m,n)=>{a("refresh",{title:o.name}),t.mounted=!1,i.view=m,t.row=o,t.layerInfo=n,await _(),t.mounted=!0}}),l(()=>{t.mounted=!1}),(o,m)=>r(t).mounted?(u(),d(s,{key:0,"layer-info":r(t).layerInfo,view:i.view,row:r(t).row},null,8,["layer-info","view","row"])):w("",!0)}});export{Ut as default};
