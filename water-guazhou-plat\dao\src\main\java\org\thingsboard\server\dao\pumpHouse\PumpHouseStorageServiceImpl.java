package org.thingsboard.server.dao.pumpHouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpHouseStorage;
import org.thingsboard.server.dao.sql.smartProduction.pumpHouse.PumpHouseStorageMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpHouseStoragePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpHouseStorageSaveRequest;

import java.util.List;

@Service
public class PumpHouseStorageServiceImpl implements PumpHouseStorageService {
    @Autowired
    private PumpHouseStorageMapper mapper;

    @Override
    public IPage<PumpHouseStorage> findAllConditional(PumpHouseStoragePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public PumpHouseStorage save(PumpHouseStorageSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(PumpHouseStorage entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<PumpHouseStorage> saveAll(List<PumpHouseStorageSaveRequest> entities) {
        return QueryUtil.saveOrUpdateBatchByRequest(entities, mapper::saveAll, mapper::updateAll);
    }

}
