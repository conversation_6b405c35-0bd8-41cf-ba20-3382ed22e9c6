import{z as o}from"./index-r0dFAfgr.js";const p=t=>o({url:"/api/spp/partitionTotalFlow/list",method:"get",params:t}),e=t=>o({url:"/api/spp/partitionTotalFlow/listExport",method:"get",data:t,responseType:"blob"}),l=t=>o({url:"/api/spp/partitionTotalFlow/correctRecords",method:"get",params:t}),a=t=>o({url:"/api/spp/partitionTotalFlow/correctRecordsExport",method:"get",params:t,responseType:"blob"}),s=t=>o({url:"/api/spp/partitionTotalFlow/correct",method:"post",data:t});export{s as C,a as E,l as G,e as a,p as b};
