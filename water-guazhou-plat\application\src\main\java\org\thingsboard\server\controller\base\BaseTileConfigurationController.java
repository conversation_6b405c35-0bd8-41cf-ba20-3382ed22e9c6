package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseTileConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration;
import org.thingsboard.server.dao.model.sql.base.BaseVectorConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTileConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 公共管理平台-瓦片数据配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Api(tags = "平台管理-瓦片数据配置")
@RestController
@RequestMapping("api/base/tile/configuration")
public class BaseTileConfigurationController extends BaseController {

    @Autowired
    private IBaseTileConfigurationService baseTileConfigurationService;

    /**
     * 查询公共管理平台-瓦片数据配置列表
     */
    @MonitorPerformance(description = "平台管理-查询瓦片数据配置列表")
    @ApiOperation(value = "查询瓦片数据配置列表")
    @GetMapping("/list")
    public IstarResponse list(BaseTileConfigurationPageRequest baseTileConfiguration) {
        return IstarResponse.ok(baseTileConfigurationService.selectBaseTileConfigurationList(baseTileConfiguration));
    }

    /**
     * 获取公共管理平台-瓦片数据配置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询瓦片数据配置详情")
    @ApiOperation(value = "查询瓦片数据配置详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseTileConfigurationService.selectBaseTileConfigurationById(id));
    }

    /**
     * 新增公共管理平台-瓦片数据配置
     */
    @MonitorPerformance(description = "平台管理-新增瓦片数据配置")
    @ApiOperation(value = "新增瓦片数据配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseTileConfiguration baseTileConfiguration) {
        return IstarResponse.ok(baseTileConfigurationService.insertBaseTileConfiguration(baseTileConfiguration));
    }

    /**
     * 修改公共管理平台-瓦片数据配置
     */
    @MonitorPerformance(description = "平台管理-修改瓦片数据配置")
    @ApiOperation(value = "修改瓦片数据配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseTileConfiguration baseTileConfiguration) {
        return IstarResponse.ok(baseTileConfigurationService.updateBaseTileConfiguration(baseTileConfiguration));
    }

    /**
     * 删除公共管理平台-瓦片数据配置
     */
    @MonitorPerformance(description = "平台管理-删除瓦片数据配置")
    @ApiOperation(value = "删除瓦片数据配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseTileConfigurationService.deleteBaseTileConfigurationByIds(ids));
    }

    /**
     * 获取所有的瓦片数据配置数据
     * @return
     */
    @MonitorPerformance(description = "平台管理-获取所有的瓦片数据配置数据")
    @ApiOperation(value = "获取所有的瓦片数据配置数据")
    @GetMapping("/getAllBaseTileConfiguration")
    public List<BaseTileConfiguration> selectAllBaseTileConfiguration() {
        return baseTileConfigurationService.selectAllBaseTileConfiguration();
    }
}
