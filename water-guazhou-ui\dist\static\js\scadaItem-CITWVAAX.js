import{C as l,M as r,j as p,u as i,l as f,g as m,n as h,p as s,bh as n}from"./index-r0dFAfgr.js";const u="/static/png/scada-pc-BhHLzOmo.png",{$confirm:k}=r(),g={props:["scada"],emits:["set","copy","del"],data(){return{scadaImg:u,scadaUrl:p().scadaUrl}},methods:{set(){this.$emit("set",this.scada)},copy(){this.$emit("copy",this.scada.id)},del(){k("确定删除该组态, 是否继续?","删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.$emit("del",this.scada.id)}).catch(()=>{})},preview(){const e=this.scadaUrl+"viewer/"+this.scada.id+"?token="+i().token;window.open(e)},editScada(){const e=this.scadaUrl+"app/editor/"+this.scada.id+"?token="+i().token;console.log(i().token,this.scada,e),window.open(e),console.log("编辑 绕登陆才行",this.scada,e)},getTime(e){return f(e).format("YYYY-MM-DD")}}},v={class:"scadaBox"},x=["src"],y={class:"title"},_={class:"scadaInfo"},w={class:"scadaInfo"},B={class:"footer"},S={class:"editScada"};function I(e,a,c,b,d,t){return m(),h("div",v,[s("img",{src:d.scadaImg,class:"imgBox"},null,8,x),s("h1",y,n(c.scada.name),1),s("p",_,"创建时间："+n(t.getTime(c.scada.createTime)),1),s("p",w,"备注："+n(c.scada.detail),1),s("div",B,[s("div",{class:"opreation",onClick:a[0]||(a[0]=(...o)=>t.set&&t.set(...o))},a[4]||(a[4]=[s("span",null,[s("i",{style:{color:"#1f7ad5"},class:"iconfont icon-shezhi1"}),s("span",{class:"icon-text"},"设置"),s("i",{class:"break"},"|")],-1)])),s("div",{class:"opreation",onClick:a[1]||(a[1]=(...o)=>t.preview&&t.preview(...o))},a[5]||(a[5]=[s("span",null,[s("i",{style:{color:"#11a57c"},class:"iconfont icon-yulan1"}),s("span",{class:"icon-text"},"预览"),s("i",{class:"break"},"|")],-1)])),s("div",{class:"opreation",onClick:a[2]||(a[2]=(...o)=>t.del&&t.del(...o))},a[6]||(a[6]=[s("span",null,[s("i",{style:{color:"#ff5722"},class:"iconfont icon-shanchu"}),s("span",{class:"icon-text"},"删除")],-1)]))]),s("div",S,[s("i",{style:{color:"#fff"},class:"iconfont icon-bianji",onClick:a[3]||(a[3]=(...o)=>t.editScada&&t.editScada(...o))})])])}const T=l(g,[["render",I],["__scopeId","data-v-8ac4e2d2"]]);export{T as default};
