/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.menu;

import org.codehaus.jackson.map.ObjectMapper;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.MenuCustomerId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.menu.*;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;

import java.io.IOException;
import java.util.*;

public interface MenuCustomerService {

    default MenuCustomer menuPoolToMenuCustomer(MenuPool menuPool) {
        MenuCustomer menuCustomer = new MenuCustomer();
        menuCustomer.setName(menuPool.getDefaultName());
        menuCustomer.setDefaultName(menuPool.getDefaultName());
        menuCustomer.setOrderNum(menuPool.getOrderNum());
        menuCustomer.setStatus(menuPool.getStatus());
        menuCustomer.setFlagDelete(menuPool.getFlagDelete());
        menuCustomer.setAdditionalInfo(menuPool.getAdditionalInfo());

        return menuCustomer;
    }

    default List<Menu> menuCustomerListToMenuList(List<MenuCustomer> menuCustomerList) {
        List<Menu> result = new ArrayList<>();
        menuCustomerList.forEach(menuCustomer -> {
            try {
                result.add(menuCustomerToMenu(menuCustomer));
            } catch (IOException e) {
                e.printStackTrace();
            }
        });

        return result;
    }

    default Menu menuCustomerToMenu(MenuCustomer menuCustomer) throws IOException {
        Menu menu = new Menu();
        menu.setId(menuCustomer.getId().getId().toString());
        menu.setParentId(menuCustomer.getParentId().getId().toString());
        menu.setMeta(buildMeta(menuCustomer));
        menu.setOrderNum(menuCustomer.getOrderNum());
        String additionalInfo = menuCustomer.getAdditionalInfo();
        Map map = new ObjectMapper().readValue(additionalInfo, Map.class);
        menu.setPath((String) map.get("path"));
        menu.setComponent((String) map.get("component"));
        menu.setName((String) map.get("name"));

        return menu;
    }

    default MenuMeta buildMeta(MenuCustomer menuCustomer){
        MenuMeta meta = new MenuMeta();
        meta.setTitle(menuCustomer.getDefaultName());
        meta.setRoles(new String[]{"TENANT_ADMIN","TENANT_SYS","CUSTOMER_USER"});

        return meta;
    }

    /**
     * menu转换为menupool
     *
     * @param menu
     * @return
     */
    default MenuCustomer menuToMenuCustomer(Menu menu) {
        MenuCustomer menuCustomer = new MenuCustomer();
        // 设置扩展信息
        Map<String, String> map = new HashMap<>();
        map.put("path", menu.getPath());
        map.put("name", menu.getName());
        map.put("component", menu.getComponent());
        menuCustomer.setAdditionalInfo(JacksonUtil.toString(map));
        // 设置基本信息
        if (menu.getId() != null && menu.getId().trim().length() > 0) {
            menuCustomer.setId(new MenuCustomerId(UUID.fromString(menu.getId())));
        }
        if (menu.getParentId() != null && menu.getParentId().trim().length() > 0) {
            menuCustomer.setParentId(new MenuCustomerId(UUID.fromString(menu.getParentId())));
        } else {
            menuCustomer.setParentId(new MenuCustomerId(ModelConstants.MENU_CUSTOMER_ROOT));
        }
        menuCustomer.setDefaultName(menu.getMeta().getTitle());
        menuCustomer.setName(menu.getMeta().getTitle());
        menuCustomer.setOrderNum(menu.getOrderNum());

        return menuCustomer;
    }

    List<MenuCustomer> saveMenuCustomer(List<MenuTenant> menuTenantList, TenantId tenantId);

    MenuCustomer saveMenuCustomer(Menu menu, TenantId tenantId);

    List<Menu> findByTenantId(TenantId tenantId);


    /**
     * 获取该租户的菜单树
     * @param tenantId
     * @return
     */
    List<MenuPoolVO> getTree(TenantId tenantId);


    /**
     * 校验当前租户是否拥有该类型菜单的新建权
     * @param type
     * @param tenantId
     * @return
     */
    boolean checkType(Integer type, TenantId tenantId);

    /**
     * 获取指定租户拥有的菜单类型
     * @param tenantId
     * @return
     */
    List<MenuTypeVO> getTypes(TenantId tenantId);

    List<Menu> findCustomerMenuByTenantId(UserId userId, TenantId tenantId);

    /**
     * 删除菜单
     * @param id
     * @param tenantId
     */
    void deleteMenu(MenuCustomerId id, TenantId tenantId) throws ThingsboardException;
}
