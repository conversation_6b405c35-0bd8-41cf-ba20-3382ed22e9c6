<template>
  <div
    ref="refDiv"
    class="chartview"
  >
    <VChart
      ref="refChart"
      :option="props.option"
    ></VChart>
  </div>
</template>

<script lang="ts" setup>
import detector from 'element-resize-detector'

const props = defineProps<{ option: any }>()
const refDiv = ref<HTMLDivElement>()
const refChart = ref<any>()
const resizer = detector()
onMounted(() => {
  refDiv.value && resizer.listenTo(refDiv.value, resize)
})
onBeforeUnmount(() => {
  refDiv.value && resizer.removeAllListeners(refDiv.value)
})
function resize() {
  refChart.value?.resize()
}
defineExpose({
  resize
})
</script>

<style lang="scss" scoped>
.chartview {
  height: 100%;
  width: 100%;
}
</style>
