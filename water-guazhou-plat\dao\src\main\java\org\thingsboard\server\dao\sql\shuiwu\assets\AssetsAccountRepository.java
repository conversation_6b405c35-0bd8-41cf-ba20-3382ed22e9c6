package org.thingsboard.server.dao.sql.shuiwu.assets;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;

import java.util.List;

/**
 * 设备台账
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
public interface AssetsAccountRepository extends CrudRepository<AssetsAccountEntity, String> {
    Page<AssetsAccountEntity> findAllByDeviceNameLikeAndDeviceStatusLikeAndDeviceNoLikeAndDeviceGradeLikeAndDeviceTypeLikeAndSpecificationModelLikeAndBrandLikeAndSupplierLikeAndDeviceSourceLikeAndProjectIdLikeAndTenantIdAndPurchaseTimeBetweenAndWarrantyTimeBetweenAndEnableTimeBetweenAndExpectScrapTimeBetween(String name, String deviceStatus, String deviceNo, String deviceGrade, String deviceType, String specificationModel, String brand, String supplier, String deviceSource, String projectId, String tenantId, Long purchaseStart, Long purchaseEnd, Long warrantyStart, Long warrantyEnd, Long enableStart, Long enableEnd, Long expectScrapStart, Long expectScrapEnd, Pageable pageable);

    List<AssetsAccountEntity> findByDeviceId(String deviceId);

    @Query("SELECT a.deviceType FROM AssetsAccountEntity a " +
            "WHERE a.tenantId = ?1 AND a.projectId LIKE ?2 " +
            "GROUP BY a.deviceType " +
            "ORDER BY a.deviceType ")
    List<String> getDeviceTypeList(String tenantId, String projectId);

    @Query("SELECT a FROM AssetsAccountEntity a " +
            "WHERE a.tenantId = ?2 AND a.deviceType = ?1 AND a.projectId = ?3 " +
            "ORDER BY a.createTime DESC")
    List<AssetsAccountEntity> getListByDeviceType(String deviceType, String tenantId, String projectId);

    List<AssetsAccountEntity> findByTenantId(String tenantId);

    List<AssetsAccountEntity> findAllByParentDeviceIdsLike(String parentId);


    List<AssetsAccountEntity> findAllBySubDeviceIdsLike(String parentId);

    Page<AssetsAccountEntity> findAllByDeviceNameLikeAndDeviceTypeLikeAndProjectIdLikeAndTenantIdLikeAndIdNotIn(String name, String deviceType, String projectId, String tenantId, List<String> notInIdList, Pageable pageable);

    List<AssetsAccountEntity> findByProjectId(String projectId);
}
