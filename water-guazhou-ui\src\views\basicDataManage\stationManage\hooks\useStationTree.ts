import { Search } from '@element-plus/icons-vue';
import { GetStationList } from '@/api/shuiwureports/zhandian';
import { formatTree } from '@/utils/GlobalHelper';
import { colors, initStationType } from '../data';

export const useStationTree = () => {
  const optType = ref<'edit' | 'add'>('add');
  const refreshStationTree = async (params?: {
    type?: string;
    projectId?: string;
  }) => {
    const res = await GetStationList({
      page: 1,
      size: 999,
      ...(params || {})
    });
    TreeData.value.data = formatTree(res.data?.data || [], {
      label: 'name',
      id: 'id',
      value: 'id',
      children: 'children'
    });
  };
  const TreeData = ref<SLTreeConfig>({
    data: []
  });
  const init = (options: {
    nodeClick: (data?: NormalOption) => void;
    refreshTree: () => void;
  }) => {
    TreeData.value = {
      data: [],
      title: '站点列表',
      titleRight: [
        {
          type: 'btn-group',
          label: '',
          field: '',
          btns: [
            {
              type: 'primary',
              perm: true,
              text: '',
              // disabled: () => !TreeData.currentProject,
              size: 'small',
              icon: 'iconfont icon-jia',
              styles: {
                padding: '5px'
              },
              iconStyles: {
                marginRight: 0
              },
              click: () => options.nodeClick()
            }
          ]
        }
      ],
      isFilterTree: true,
      filterSize: 'small',
      filterClassName: 'simple-input',
      filterIcon: shallowRef(Search),
      filterPlaceHolder: '输入关键字搜索',
      selectFilter: {
        key: 'type',
        label: '',
        options: initStationType('all'),
        search: true,
        handleChange: () => {
          options.refreshTree();
        }
      },
      queryParams: {
        type: ''
      },
      tags: [
        {
          field: 'type',
          effect: 'light',
          color: (val: any) => {
            return colors[val] || '#333';
          },
          hit: false
        }
      ],
      treeNodeHandleClick: options.nodeClick
    };
  };
  return {
    init,
    TreeData,
    optType,
    refreshStationTree
  };
};
