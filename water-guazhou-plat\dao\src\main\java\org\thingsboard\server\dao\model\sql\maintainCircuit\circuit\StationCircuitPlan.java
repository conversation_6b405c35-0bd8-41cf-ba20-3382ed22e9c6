package org.thingsboard.server.dao.model.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_TABLE)
@TableName(ModelConstants.TB_STATION_CIRCUIT_PLAN_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class StationCircuitPlan {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_NAME)
    private String name;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_TYPE)
    private String type;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_DAYS)
    private Integer days;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_START_TIME)
    private Date startTime;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_END_TIME)
    private Date endTime;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_AUDIT_DEP)
    private String auditDep;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_AUDIT_USER_ID)
    private String auditUserId;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_OPTION_DEP)
    private String optionDep;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_OPTION_USER_ID)
    private String optionUserId;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_STATION_IDS)
    private String stationIds;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_REMARK)
    private String remark;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_STATION_TYPE)
    private String stationType;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_SCHEME_ID)
    private String schemeId;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    @TableField(exist = false)
    private String auditDepName;

    @Transient
    @TableField(exist = false)
    private String auditUserName;

    @Transient
    @TableField(exist = false)
    private String optionDepName;

    @Transient
    @TableField(exist = false)
    private String optionUserName;


}
