"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[4475],{84475:(e,t,s)=>{s.r(t),s.d(t,{DictionaryLoader:()=>f});var i=s(22303),o=s(3172),r=s(20102),n=s(92604),l=s(13867),a=s(70586),c=s(95330),y=s(19153),m=s(59266),u=s(17386);const h="esri.renderers.support.DictionaryLoader",p={type:"CIMSimpleLineCallout",lineSymbol:{type:"CIMLineSymbol",symbolLayers:[{type:"CIMSolidStroke",width:.5,color:[0,0,0,255]}]}};class f{constructor(e,t,s){this.config=null,this.fieldMap=null,this.url=null,this._ongoingRequests=new Map,this._symbolCache=new l.Z(100),this._dictionaryPromise=null,this.url=e,this.config=t,this.fieldMap=s}getSymbolFields(){return this._symbolFields}async getSymbolAsync(e,t){let s;this._dictionaryPromise||(this._dictionaryPromise=this.fetchResources(t));try{s=await this._dictionaryPromise}catch(e){if((0,c.D_)(e))return this._dictionaryPromise=null,null}const o={};if(this.fieldMap)for(const t of this._symbolFields){const s=this.fieldMap[t];if(s&&null!=e.attributes[s]){const i=""+e.attributes[s];o[t]=i}else o[t]=""}const r=s?.(o,t);if(!r||"string"!=typeof r)return null;const n=(0,y.hP)(r).toString(),l=this._symbolCache.get(n);if(l)return l.catch((()=>{this._symbolCache.pop(n)})),l;const m=r.split(";"),u=[],h=[];for(const e of m)if(e)if(e.includes("po:")){const t=e.substr(3).split("|");if(3===t.length){const e=t[0],s=t[1];let o=t[2];if("DashTemplate"===s)o=o.split(" ").map((e=>Number(e)));else if("Color"===s){const e=new i.Z(o).toRgba();o=[e[0],e[1],e[2],255*e[3]]}else o=Number(o);h.push({primitiveName:e,propertyName:s,value:o})}}else if(e.includes("|")){for(const t of e.split("|"))if(this._itemNames.has(t)){u.push(t);break}}else this._itemNames.has(e)&&u.push(e);const p=!(0,a.pC)(e.geometry)||!e.geometry.hasZ&&"point"===e.geometry.type,f=this._cimPartsToCIMSymbol(u,h,p,t);return this._symbolCache.put(n,f,1),f}async fetchResources(e){if(this._dictionaryPromise)return this._dictionaryPromise;if(!this.url)return void n.Z.getLogger(h).error("no valid URL!");const t=(0,o.default)(this.url+"/resources/styles/dictionary-info.json",{responseType:"json",query:{f:"json"},signal:(0,a.pC)(e)?e.signal:null}),[{data:s}]=await Promise.all([t,(0,m.LC)()]);if(!s)throw this._dictionaryPromise=null,new r.Z("esri.renderers.DictionaryRenderer","Bad dictionary data!");const i=s.expression,l=s.authoringInfo;this._refSymbolUrlTemplate=this.url+"/"+s.cimRefTemplateUrl,this._itemNames=new Set(s.itemsNames),this._symbolFields=l.symbol;const c={};if(this.config){const e=this.config;for(const t in e)c[t]=e[t]}if(l.configuration)for(const e of l.configuration)c.hasOwnProperty(e.name)||(c[e.name]=e.value);const y=[];if((0,a.pC)(e)&&e.fields&&this.fieldMap)for(const t of this._symbolFields){const s=this.fieldMap[t],i=e.fields.filter((e=>e.name===s));i.length>0&&y.push({...i[0],name:t})}const u=(0,m.pp)(i,(0,a.pC)(e)?e.spatialReference:null,y,c).then((e=>{const t={scale:0};return(s,i)=>{if((0,a.Wi)(e))return null;const o=e.repurposeFeature({geometry:null,attributes:s});return t.scale=(0,a.pC)(i)?i.scale??void 0:void 0,e.evaluate({$feature:o,$view:t})}})).catch((e=>(n.Z.getLogger(h).error("Creating dictinoary expression failed:",e),null)));return this._dictionaryPromise=u,u}async _cimPartsToCIMSymbol(e,t,s,i){const o=new Array(e.length);for(let t=0;t<e.length;t++)o[t]=this._getSymbolPart(e[t],i);const r=await Promise.all(o),n=this.fieldMap;if(n)for(const e of r)d(e,n);return new u.Z({data:this._combineSymbolParts(r,t,s)})}async _getSymbolPart(e,t){if(this._ongoingRequests.has(e))return this._ongoingRequests.get(e).then((e=>e.data));const s=this._refSymbolUrlTemplate.replace(/\{itemName\}/gi,e),i=(0,o.default)(s,{responseType:"json",query:{f:"json"},...t});this._ongoingRequests.set(e,i);try{return(await i).data}catch(t){throw this._ongoingRequests.delete(e),t}}_combineSymbolParts(e,t,s){if(!e||0===e.length)return null;const i={...e[0]};if(e.length>1){i.symbolLayers=[];for(const t of e){const e=t;i.symbolLayers.unshift(...e.symbolLayers)}}return s&&(i.callout=p),{type:"CIMSymbolReference",symbol:i,primitiveOverrides:t}}}function d(e,t){if(!e)return;const s=e.symbolLayers;if(!s)return;let i=s.length;for(;i--;){const e=s[i];e&&!1!==e.enable&&"CIMVectorMarker"===e.type&&b(e,t)}}function b(e,t){const s=e.markerGraphics;if(s)for(const e of s){if(!e)continue;const s=e.symbol;if(s)switch(s.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":d(s,t);break;case"CIMTextSymbol":s.fieldMap=t}}}},17386:(e,t,s)=>{s.d(t,{Z:()=>f});var i,o=s(43697),r=s(22974),n=s(19153),l=s(5600),a=(s(75215),s(36030)),c=s(71715),y=s(52011),m=s(30556),u=s(35671),h=s(89164);let p=i=class extends h.Z{constructor(e){super(e),this.data=null,this.type="cim"}readData(e,t){return t}writeData(e,t){if(e)for(const s in e)t[s]=e[s]}async collectRequiredFields(e,t){if("CIMSymbolReference"===this.data?.type){const s=this.data.primitiveOverrides;if(s){const i=s.map((s=>{const i=s.valueExpressionInfo;return(0,u.io)(e,t,i.expression)}));await Promise.all(i)}}}clone(){return new i({data:(0,r.d9)(this.data)})}hash(){return(0,n.hP)(JSON.stringify(this.data)).toString()}};(0,o._)([(0,l.Cb)({json:{write:!1}})],p.prototype,"color",void 0),(0,o._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"data",void 0),(0,o._)([(0,c.r)("data",["symbol"])],p.prototype,"readData",null),(0,o._)([(0,m.c)("data",{})],p.prototype,"writeData",null),(0,o._)([(0,a.J)({CIMSymbolReference:"cim"},{readOnly:!0})],p.prototype,"type",void 0),p=i=(0,o._)([(0,y.j)("esri.symbols.CIMSymbol")],p);const f=p},89164:(e,t,s)=>{s.d(t,{Z:()=>h});var i=s(43697),o=s(22303),r=s(35454),n=s(96674),l=s(5600),a=(s(75215),s(67676),s(71715)),c=s(52011);const y=new r.X({esriSMS:"simple-marker",esriPMS:"picture-marker",esriSLS:"simple-line",esriSFS:"simple-fill",esriPFS:"picture-fill",esriTS:"text",esriSHD:"shield-label-symbol",PointSymbol3D:"point-3d",LineSymbol3D:"line-3d",PolygonSymbol3D:"polygon-3d",WebStyleSymbol:"web-style",MeshSymbol3D:"mesh-3d",LabelSymbol3D:"label-3d",CIMSymbolReference:"cim"});let m=0,u=class extends n.wq{constructor(e){super(e),this.id="sym"+m++,this.type=null,this.color=new o.Z([0,0,0,1])}readColor(e){return e&&null!=e[0]?[e[0],e[1],e[2],e[3]/255]:e}async collectRequiredFields(e,t){}hash(){return JSON.stringify(this.toJSON())}clone(){}};(0,i._)([(0,l.Cb)({type:y.apiValues,readOnly:!0,json:{read:!1,write:{ignoreOrigin:!0,writer:y.write}}})],u.prototype,"type",void 0),(0,i._)([(0,l.Cb)({type:o.Z,json:{write:{allowNull:!0}}})],u.prototype,"color",void 0),(0,i._)([(0,a.r)("color")],u.prototype,"readColor",null),u=(0,i._)([(0,c.j)("esri.symbols.Symbol")],u);const h=u}}]);