<template>
  <el-dialog
    v-model="dialogVisible"
    title="查看泵组方案"
    width="800px"
    :before-close="handleClose"
  >
    <div class="scheme-dialog">
      <el-form :model="form" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="方案名称:">
              <el-input v-model="form.schemeName" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方案编码:">
              <el-input v-model="form.schemeCode" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属泵房:">
              <el-input v-model="form.pumpRoomName" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方案状态:">
              <el-tag :type="form.status === 1 ? 'success' : 'info'">
                {{ form.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="pump-config-section">
        <div class="section-title">泵组配置</div>
        <el-table :data="pumpConfigs" border style="width: 100%;">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="pumpName" label="泵机名称" min-width="150" />
          <el-table-column prop="fixedPower" label="额定功率" width="120" />
          <el-table-column prop="fixedFlow" label="额定流量" width="120" />
        </el-table>
      </div>

      <div class="scheme-description">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="description-item">
              <label>方案描述:</label>
              <el-input
                v-model="form.schemeDescription"
                type="textarea"
                :rows="3"
                readonly
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="description-item">
              <label>方案备注:</label>
              <el-input
                v-model="form.schemeRemark"
                type="textarea"
                :rows="3"
                readonly
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="scheme-info">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>创建时间:</label>
              <span>{{ formatDate(form.createTime) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>创建人:</label>
              <span>{{ form.creator || '系统' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  schemeData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'edit'])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = reactive({
  schemeName: '',
  schemeCode: '',
  pumpRoomName: '',
  schemeDescription: '',
  schemeRemark: '',
  status: 1,
  createTime: '',
  creator: ''
})

// 泵组配置数据
const pumpConfigs = ref<Array<{
  pumpName: string,
  fixedPower: string,
  fixedFlow: string
}>>([])

// 格式化日期
const formatDate = (dateStr: string | number) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN')
}

// 加载方案数据
const loadSchemeData = () => {
  if (props.schemeData && Object.keys(props.schemeData).length > 0) {
    // 填充基本信息
    form.schemeName = props.schemeData.schemeName || ''
    form.schemeCode = props.schemeData.schemeCode || ''
    form.pumpRoomName = props.schemeData.pumpRoomName || ''
    form.schemeDescription = props.schemeData.schemeDescription || ''
    form.schemeRemark = props.schemeData.schemeRemark || ''
    form.status = props.schemeData.status || 1
    form.createTime = props.schemeData.createTime || ''
    form.creator = props.schemeData.creator || ''

    // 解析泵组配置
    try {
      const configStr = props.schemeData.pumpGroupConfig || '[]'
      const configs = JSON.parse(configStr)
      pumpConfigs.value = configs.map((config: any) => ({
        pumpName: config.pumpDisplayName || config.pumpName || '未知泵机',
        fixedPower: config.fixedPower || '-',
        fixedFlow: config.fixedFlow || '-'
      }))
    } catch (error) {
      console.error('解析泵组配置失败:', error)
      pumpConfigs.value = []
    }
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 编辑方案
const handleEdit = () => {
  emit('edit', props.schemeData)
  handleClose()
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadSchemeData()
  }
})

// 监听方案数据变化
watch(() => props.schemeData, () => {
  if (props.visible) {
    loadSchemeData()
  }
}, { deep: true })
</script>

<style scoped lang="scss">
.scheme-dialog {
  .pump-config-section {
    margin: 20px 0;
    
    .section-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 10px;
    }
  }
  
  .scheme-description {
    margin-top: 20px;
    
    .description-item {
      label {
        display: block;
        margin-bottom: 5px;
        font-size: 14px;
        font-weight: bold;
      }
    }
  }

  .scheme-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    .info-item {
      margin-bottom: 10px;
      
      label {
        display: inline-block;
        width: 80px;
        font-weight: bold;
        color: #606266;
      }
      
      span {
        color: #303133;
      }
    }
  }
}

.dialog-footer {
  text-align: center;
}

:deep(.el-input__inner) {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
}

:deep(.el-textarea__inner) {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
}
</style>
