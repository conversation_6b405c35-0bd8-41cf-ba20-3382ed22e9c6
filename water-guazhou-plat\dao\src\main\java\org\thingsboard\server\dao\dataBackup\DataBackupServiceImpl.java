package org.thingsboard.server.dao.dataBackup;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.DataBackup;
import org.thingsboard.server.dao.sql.dataBackup.DataBackupRepository;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@Transactional
public class DataBackupServiceImpl implements DataBackupService {

    @Value("${database-backup.opentsdb-backup-script-path}")
    private String OPENTSDB_BACKUP_SCRIPT_PATH;

    @Value("${database-backup.postgresql-backup-script-path}")
    private String POSTGRESQL_BACKUP_SCRIPT_PATH;

    @Value("${database-backup.influxdb-backup-script-path}")
    private String INFLUXDB_BACKUP_SCRIPT_PATH;

    @Autowired
    private DataBackupRepository dataBackupRepository;

    @Override
    public void save(DataBackup dataBackup) {
        dataBackupRepository.save(dataBackup);
    }

    @Override
    public void backup(DataBackup dataBackup) {
        if (StringUtils.isNotBlank(OPENTSDB_BACKUP_SCRIPT_PATH)) {
            new Thread(this::opentsdbBackup).start();
        }

        if (StringUtils.isNotBlank(POSTGRESQL_BACKUP_SCRIPT_PATH)) {
            new Thread(this::postgresqlBackup).start();
        }

        if (StringUtils.isNotBlank(INFLUXDB_BACKUP_SCRIPT_PATH)) {
            new Thread(this::influxdbBackup).start();
        }

        dataBackup.setEndTime(System.currentTimeMillis());

        save(dataBackup);
    }

    @Override
    public List<DataBackup> findList(long start, long end) {
        return dataBackupRepository.findByStartAndEnd(start, end);
    }

    public void opentsdbBackup() {
        try {
            log.info("准备开始备份opentsdb：[{}], opebtsdb备份脚本的位置：[{}]", new SimpleDateFormat("").format(new Date()), OPENTSDB_BACKUP_SCRIPT_PATH);
            Process exec = Runtime.getRuntime().exec("sh " + OPENTSDB_BACKUP_SCRIPT_PATH);
//        int i = exec.waitFor();
//            log.info("是否正确执行opentsdb备份. [{}]", i);
            BufferedReader input = new BufferedReader(new InputStreamReader(exec.getInputStream()));
            String line = "";
            while ((line = input.readLine()) != null) {
                log.info(line);
            }
            input.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    public void postgresqlBackup() {
        try {
            log.info("准备开始备份postgresql：[{}], postgresql备份脚本的位置：[{}]", new SimpleDateFormat("").format(new Date()), POSTGRESQL_BACKUP_SCRIPT_PATH);
            Process exec = Runtime.getRuntime().exec("sh " + POSTGRESQL_BACKUP_SCRIPT_PATH);
//        int i = exec.waitFor();
//            log.info("是否正确执行postgresql备份. [{}]", i);
            BufferedReader input = new BufferedReader(new InputStreamReader(exec.getInputStream()));
            String line = "";
            while ((line = input.readLine()) != null) {
                log.info(line);
            }
            input.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void influxdbBackup() {
        try {
            log.info("准备开始备份influxdb：[{}], influxdb备份脚本的位置：[{}]", new SimpleDateFormat("").format(new Date()), INFLUXDB_BACKUP_SCRIPT_PATH);
            Process exec = Runtime.getRuntime().exec("sh " + INFLUXDB_BACKUP_SCRIPT_PATH);
//        int i = exec.waitFor();
//            log.info("是否正确执行postgresql备份. [{}]", i);
            BufferedReader input = new BufferedReader(new InputStreamReader(exec.getInputStream()));
            String line = "";
            while ((line = input.readLine()) != null) {
                log.info(line);
            }
            input.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
