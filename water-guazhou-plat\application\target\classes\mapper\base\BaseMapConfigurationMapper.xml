<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseMapConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseMapConfiguration" id="BaseMapConfigurationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="url"    column="url"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectBaseMapConfigurationVo">
        select id, name, type, url, status, remark from base_map_configuration
    </sql>

    <select id="selectBaseMapConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseMapConfiguration" resultMap="BaseMapConfigurationResult">
        <include refid="selectBaseMapConfigurationVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectBaseMapConfigurationById" parameterType="String" resultMap="BaseMapConfigurationResult">
        <include refid="selectBaseMapConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseMapConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseMapConfiguration">
        insert into base_map_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="url != null">url,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="url != null">#{url},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateBaseMapConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseMapConfiguration">
        update base_map_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="url != null">url = #{url},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseMapConfigurationById" parameterType="String">
        delete from base_map_configuration where id = #{id}
    </delete>

    <delete id="deleteBaseMapConfigurationByIds" parameterType="String">
        delete from base_map_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>