/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.repair;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.repair.Repair;
import org.thingsboard.server.common.data.repair.RepairType;

import java.util.List;
import java.util.UUID;

public interface RepairService {

    /**
     * 新增维修记录
     *
     * @param repair 维修记录
     * @return
     */
    Repair addRepair(Repair repair);

    List<Repair> findByStatus(RepairType waiting);

    void updateStatus(List<String> needUpdateRepairIdList, String code);

    List<Repair> findByTenantId(TenantId tenantId);

    Repair findById(UUID id);

    Repair update(Repair repair);
}
