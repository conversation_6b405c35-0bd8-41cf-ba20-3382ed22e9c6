package org.thingsboard.server.dao.groundwater;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.groundwater.GroundwaterLevel;
import org.thingsboard.server.dao.model.sql.groundwater.GroundwaterRecharge;
import org.thingsboard.server.dao.model.request.GroundwaterLevelRequest;
import org.thingsboard.server.dao.model.request.GroundwaterRechargeRequest;
import org.thingsboard.server.dao.sql.groundwater.GroundwaterLevelMapper;
import org.thingsboard.server.dao.sql.groundwater.GroundwaterRechargeMapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 地下水涵养水位服务实现
 */
@Slf4j
@Service
public class GroundwaterServiceImpl implements GroundwaterService {

    @Autowired
    private GroundwaterLevelMapper groundwaterLevelMapper;

    @Autowired
    private GroundwaterRechargeMapper groundwaterRechargeMapper;

    // 多孔介质渗透系数 (m/d)，这是一个经验值，实际应根据当地地质条件调整
    private static final BigDecimal PERMEABILITY_COEFFICIENT = new BigDecimal("0.05");

    // 有效孔隙度，这是一个经验值，实际应根据当地地质条件调整
    private static final BigDecimal EFFECTIVE_POROSITY = new BigDecimal("0.25");

    // 区域面积 (平方米)，实际应根据具体区域计算
    private static final BigDecimal DEFAULT_AREA = new BigDecimal("10000");

    @Override
    @Transactional
    public GroundwaterLevel saveWaterLevel(GroundwaterLevel entity) {
        try {
            Date now = new Date();
            if (entity.getId() == null || entity.getId().isEmpty()) {
                // 新增
                entity.setId(UUID.randomUUID().toString());
                entity.setCreateTime(now);
                entity.setUpdateTime(now);

                // 计算水位变化量，需要查询前一个记录
                if (entity.getLevelChange() == null) {
                    List<GroundwaterLevel> latestLevels = groundwaterLevelMapper.findByStationIdAndTimeBetween(
                            entity.getStationId(),
                            new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000L),  // 30天前
                            now);

                    if (!latestLevels.isEmpty()) {
                        // 按时间排序
                        latestLevels.sort(Comparator.comparing(GroundwaterLevel::getRecordTime).reversed());
                        GroundwaterLevel previousLevel = latestLevels.get(0);

                        // 计算变化量 (当前水位 - 上一次水位)
                        if (previousLevel.getWaterLevel() != null && entity.getWaterLevel() != null) {
                            entity.setLevelChange(entity.getWaterLevel().subtract(previousLevel.getWaterLevel()));
                        }
                    } else {
                        entity.setLevelChange(BigDecimal.ZERO);
                    }
                }

                // 设置水位状态 (1-正常, 2-偏低, 3-偏高)
                if (entity.getStatus() == null) {
                    if (entity.getLevelChange() != null) {
                        if (entity.getLevelChange().compareTo(new BigDecimal("-0.5")) < 0) {
                            entity.setStatus(2); // 偏低
                        } else if (entity.getLevelChange().compareTo(new BigDecimal("0.5")) > 0) {
                            entity.setStatus(3); // 偏高
                        } else {
                            entity.setStatus(1); // 正常
                        }
                    } else {
                        entity.setStatus(1); // 默认正常
                    }
                }

                groundwaterLevelMapper.insert(entity);
            } else {
                // 更新
                GroundwaterLevel existingEntity = groundwaterLevelMapper.selectById(entity.getId());
                if (existingEntity == null) {
                    throw new RuntimeException("地下水水位数据不存在");
                }

                entity.setUpdateTime(now);
                entity.setCreateTime(existingEntity.getCreateTime());

                groundwaterLevelMapper.updateById(entity);
            }
            return entity;
        } catch (Exception e) {
            log.error("保存地下水水位数据失败", e);
            throw new RuntimeException("保存地下水水位数据失败: " + e.getMessage());
        }
    }

    @Override
    public PageData<GroundwaterLevel> findWaterLevelList(GroundwaterLevelRequest request, TenantId tenantId) {
        try {
            Page<GroundwaterLevel> page = new Page<>(request.getPage(), request.getSize());
            String tenantIdStr = UUIDConverter.fromTimeUUID(tenantId.getId());

            List<GroundwaterLevel> list = groundwaterLevelMapper.findList(
                    page,
                    request.getStationId(),
                    request.getStationName(),
                    tenantIdStr,
                    request.getStatus(),
                    request.getStartTime(),
                    request.getEndTime()
            );

            Integer total = groundwaterLevelMapper.findListCount(
                    request.getStationId(),
                    request.getStationName(),
                    tenantIdStr,
                    request.getStatus(),
                    request.getStartTime(),
                    request.getEndTime()
            );

            return new PageData<>(total, list);
        } catch (Exception e) {
            log.error("查询地下水水位数据失败", e);
            throw new RuntimeException("查询地下水水位数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void removeWaterLevel(List<String> ids) {
        try {
            groundwaterLevelMapper.deleteBatchIds(ids);
        } catch (Exception e) {
            log.error("删除地下水水位数据失败", e);
            throw new RuntimeException("删除地下水水位数据失败: " + e.getMessage());
        }
    }

    @Override
    public GroundwaterLevel getWaterLevelById(String id) {
        try {
            // 使用连表查询获取水位详情和测点名称
            GroundwaterLevel entity = groundwaterLevelMapper.findDetailById(id);
            if (entity == null) {
                throw new RuntimeException("地下水水位数据不存在");
            }
            return entity;
        } catch (Exception e) {
            log.error("获取地下水水位详情失败", e);
            throw new RuntimeException("获取地下水水位详情失败: " + e.getMessage());
        }
    }

    @Override
    public PageData<GroundwaterLevel> findWaterLevelList(Map<String, Object> params) {
        try {
            Page<GroundwaterLevel> page = new Page<>(
                    Integer.parseInt(params.getOrDefault("page", "1").toString()),
                    Integer.parseInt(params.getOrDefault("pageSize", "10").toString())
            );

            // 处理时间参数
//            if (params.get("startTime") != null && params.get("startTime").toString().length() > 0) {
//                params.put("startTime", new Date(Long.parseLong(params.get("startTime").toString())));
//            }
//            if (params.get("endTime") != null && params.get("endTime").toString().length() > 0) {
//                params.put("endTime", new Date(Long.parseLong(params.get("endTime").toString())));
//            }
            if (params.get("recordTime") != null && params.get("recordTime").toString().length() > 0) {
                params.put("recordTime", new Date(Long.parseLong(params.get("recordTime").toString())));
            }

            // 处理状态参数
            if (params.get("status") != null && params.get("status").toString().length() > 0) {
                params.put("status", Integer.parseInt(params.get("status").toString()));
            }

            // 设置排序参数
            params.put("sortField", params.getOrDefault("sortProperty", "recordTime"));
            params.put("sortOrder", params.getOrDefault("sortOrder", "DESC"));

            IPage<GroundwaterLevel> pageResult = groundwaterLevelMapper.getList(page, params);
            return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
        } catch (Exception e) {
            log.error("查询地下水水位数据失败", e);
            throw new RuntimeException("查询地下水水位数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public GroundwaterLevel updateWaterLevel(GroundwaterLevel entity) {
        try {
            if (entity.getId() == null || entity.getId().isEmpty()) {
                throw new RuntimeException("更新数据时ID不能为空");
            }

            GroundwaterLevel existingEntity = groundwaterLevelMapper.selectById(entity.getId());
            if (existingEntity == null) {
                throw new RuntimeException("地下水水位数据不存在");
            }

            // 保留创建时间
            entity.setCreateTime(existingEntity.getCreateTime());
            // 更新时间
            entity.setUpdateTime(new Date());

            // 计算水位变化量，如果没有提供
            if (entity.getLevelChange() == null && entity.getWaterLevel() != null && existingEntity.getWaterLevel() != null) {
                entity.setLevelChange(entity.getWaterLevel().subtract(existingEntity.getWaterLevel()));
            }

            // 设置水位状态
            if (entity.getStatus() == null && entity.getLevelChange() != null) {
                if (entity.getLevelChange().compareTo(new BigDecimal("-0.5")) < 0) {
                    entity.setStatus(2); // 偏低
                } else if (entity.getLevelChange().compareTo(new BigDecimal("0.5")) > 0) {
                    entity.setStatus(3); // 偏高
                } else {
                    entity.setStatus(1); // 正常
                }
            }

            groundwaterLevelMapper.updateById(entity);
            return entity;
        } catch (Exception e) {
            log.error("更新地下水水位数据失败", e);
            throw new RuntimeException("更新地下水水位数据失败: " + e.getMessage());
        }
    }

    @Override
    public List<GroundwaterLevel> getWaterLevelChangeData(String stationId, Date startTime, Date endTime) {
        try {
            return groundwaterLevelMapper.findByStationIdAndTimeBetween(stationId, startTime, endTime);
        } catch (Exception e) {
            log.error("获取地下水水位变化数据失败", e);
            throw new RuntimeException("获取地下水水位变化数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public GroundwaterRecharge saveRechargeAnalysis(GroundwaterRecharge entity) {
        try {
            Date now = new Date();
            if (entity.getId() == null || entity.getId().isEmpty()) {
                // 新增
                entity.setId(UUID.randomUUID().toString());
                entity.setCreateTime(now);
                entity.setUpdateTime(now);

                groundwaterRechargeMapper.insert(entity);
            } else {
                // 更新
                GroundwaterRecharge existingEntity = groundwaterRechargeMapper.selectById(entity.getId());
                if (existingEntity == null) {
                    throw new RuntimeException("地下水涵养水位数据不存在");
                }

                entity.setUpdateTime(now);
                entity.setCreateTime(existingEntity.getCreateTime());

                groundwaterRechargeMapper.updateById(entity);
            }
            return entity;
        } catch (Exception e) {
            log.error("保存地下水涵养水位数据失败", e);
            throw new RuntimeException("保存地下水涵养水位数据失败: " + e.getMessage());
        }
    }

    @Override
    public PageData<GroundwaterRecharge> findRechargeList(GroundwaterRechargeRequest request, String tenantId) {
        try {
            Page<GroundwaterRecharge> page = new Page<>(request.getPage(), request.getSize());

            List<GroundwaterRecharge> list = groundwaterRechargeMapper.findList(
                    page,
                    request.getAreaId(),
                    tenantId,
                    request.getStatus(),
                    request.getStartTime(),
                    request.getEndTime()
            );

            Integer total = groundwaterRechargeMapper.findListCount(
                    request.getAreaId(),
                    tenantId,
                    request.getStatus(),
                    request.getStartTime(),
                    request.getEndTime()
            );

            return new PageData<>(total, list);
        } catch (Exception e) {
            log.error("查询地下水涵养水位数据失败", e);
            throw new RuntimeException("查询地下水涵养水位数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void removeRechargeAnalysis(List<String> ids) {
        try {
            groundwaterRechargeMapper.deleteBatchIds(ids);
        } catch (Exception e) {
            log.error("删除地下水涵养水位数据失败", e);
            throw new RuntimeException("删除地下水涵养水位数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public GroundwaterRecharge analyzeGroundwaterRecharge(String areaId, Date startTime, Date endTime) {
        try {
            // 1. 获取区域的所有测点数据
            // 实际应用中应查询区域关联的测点，这里简化处理
            List<GroundwaterLevel> levelData = groundwaterLevelMapper.findByStationIdAndTimeBetween(
                    areaId, // 这里简化处理，直接使用 areaId 代替 stationId
                    startTime,
                    endTime
            );

            if (levelData.isEmpty()) {
                throw new RuntimeException("区域内没有足够的地下水水位数据进行分析");
            }

            // 2. 按时间排序
            levelData.sort(Comparator.comparing(GroundwaterLevel::getRecordTime));

            // 3. 确定期初和期末水位
            GroundwaterLevel initialLevel = levelData.get(0);
            GroundwaterLevel finalLevel = levelData.get(levelData.size() - 1);

            // 4. 计算水位变化量
            BigDecimal levelChange = finalLevel.getWaterLevel().subtract(initialLevel.getWaterLevel());

            // 5. 汇总降雨量
            BigDecimal totalRainfall = levelData.stream()
                    .filter(level -> level.getRainfallAmount() != null)
                    .map(GroundwaterLevel::getRainfallAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 6. 计算地下水补给量
            // 使用达西定律计算：Q = K * A * (h2 - h1) / L
            // 其中：Q是流量，K是渗透系数，A是面积，h2-h1是水位差，L是流程长度

            // 简化模型：Q = K * A * h * n
            // 其中：h是水位变化，n是有效孔隙度
            BigDecimal rechargeAmount = PERMEABILITY_COEFFICIENT
                    .multiply(DEFAULT_AREA)
                    .multiply(levelChange.abs())
                    .multiply(EFFECTIVE_POROSITY);

            // 7. 计算地下水消耗量
            // 如果水位下降，则消耗量 = |补给量| + 额外消耗量
            // 如果水位上升，则消耗量 = |补给量| - 水位上升对应的水量
            BigDecimal consumptionAmount;
            if (levelChange.compareTo(BigDecimal.ZERO) < 0) {
                // 水位下降
                consumptionAmount = rechargeAmount.add(
                        levelChange.abs()
                                .multiply(DEFAULT_AREA)
                                .multiply(EFFECTIVE_POROSITY)
                );
            } else {
                // 水位上升或不变
                consumptionAmount = rechargeAmount.subtract(
                        levelChange
                                .multiply(DEFAULT_AREA)
                                .multiply(EFFECTIVE_POROSITY)
                );
                // 确保消耗量不为负
                if (consumptionAmount.compareTo(BigDecimal.ZERO) < 0) {
                    consumptionAmount = BigDecimal.ZERO;
                }
            }

            // 8. 计算建议补给量
            BigDecimal suggestedRechargeAmount;
            String rechargeSuggestion;
            Integer status;

            if (levelChange.compareTo(new BigDecimal("-0.5")) < 0) {
                // 水位下降过多，需要增加补给
                suggestedRechargeAmount = levelChange.abs()
                        .multiply(DEFAULT_AREA)
                        .multiply(EFFECTIVE_POROSITY)
                        .multiply(new BigDecimal("1.2")); // 增加20%的安全系数

                rechargeSuggestion = "地下水位降低较多，建议增加补给水量，合理调整取水策略。";
                status = 3; // 不足
            } else if (levelChange.compareTo(new BigDecimal("0.5")) > 0) {
                // 水位上升过多，可以减少补给
                suggestedRechargeAmount = BigDecimal.ZERO;
                rechargeSuggestion = "地下水位上升较多，地下水涵养状况良好，可以适当增加取水量。";
                status = 1; // 良好
            } else {
                // 水位变化在合理范围内
                suggestedRechargeAmount = levelChange.abs()
                        .multiply(DEFAULT_AREA)
                        .multiply(EFFECTIVE_POROSITY)
                        .multiply(new BigDecimal("0.8")); // 80%的补充量

                rechargeSuggestion = "地下水位变化在合理范围内，建议保持当前的取水和补给策略。";
                status = 2; // 一般
            }

            // 9. 生成分析结果
            String analysisResult = String.format(
                    "分析周期：%s 至 %s，水位从 %.2f 米变化到 %.2f 米，变化量为 %.2f 米。" +
                            "期间降雨量为 %.2f 毫米，地下水补给量约为 %.2f 立方米，消耗量约为 %.2f 立方米。",
                    startTime, endTime,
                    initialLevel.getWaterLevel().doubleValue(),
                    finalLevel.getWaterLevel().doubleValue(),
                    levelChange.doubleValue(),
                    totalRainfall.doubleValue(),
                    rechargeAmount.doubleValue(),
                    consumptionAmount.doubleValue()
            );

            // 10. 创建并保存涵养水位分析结果
            GroundwaterRecharge recharge = new GroundwaterRecharge();
            recharge.setAreaId(areaId);
            recharge.setTenantId(initialLevel.getTenantId());
            recharge.setStartTime(startTime);
            recharge.setEndTime(endTime);
            recharge.setInitialLevel(initialLevel.getWaterLevel());
            recharge.setFinalLevel(finalLevel.getWaterLevel());
            recharge.setLevelChange(levelChange);
            recharge.setRechargeAmount(rechargeAmount.setScale(2, RoundingMode.HALF_UP));
            recharge.setRainfallAmount(totalRainfall);
            recharge.setConsumptionAmount(consumptionAmount.setScale(2, RoundingMode.HALF_UP));
            recharge.setSuggestedRechargeAmount(suggestedRechargeAmount.setScale(2, RoundingMode.HALF_UP));
            recharge.setRechargeSuggestion(rechargeSuggestion);
            recharge.setAnalysisResult(analysisResult);
            recharge.setStatus(status);
            recharge.setCreateTime(new Date());
            recharge.setUpdateTime(new Date());

            return saveRechargeAnalysis(recharge);

        } catch (Exception e) {
            log.error("分析地下水涵养水位失败", e);
            throw new RuntimeException("分析地下水涵养水位失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getWaterLevelStatistics() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 获取最近30天的数据
            Date endTime = new Date();
            Date startTime = new Date(endTime.getTime() - 30 * 24 * 60 * 60 * 1000L);

            // 1. 查询所有测点的水位数据，不考虑租户
            // 使用自定义SQL查询所有水位数据
            List<GroundwaterLevel> recentData = groundwaterLevelMapper.findList(
                    null,  // 不分页
                    null,  // 不限制测点
                    null,  // 不限制测点名称
                    null,  // 不限制租户
                    null,  // 不限制状态
                    startTime,
                    endTime
            );

            // 2. 计算各项统计指标

            // 平均水位变化量
            BigDecimal avgLevelChange = BigDecimal.ZERO;
            if (!recentData.isEmpty()) {
                BigDecimal sum = recentData.stream()
                        .filter(level -> level.getLevelChange() != null)
                        .map(GroundwaterLevel::getLevelChange)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                long count = recentData.stream()
                        .filter(level -> level.getLevelChange() != null)
                        .count();

                if (count > 0) {
                    avgLevelChange = sum.divide(new BigDecimal(count), 2, RoundingMode.HALF_UP);
                }
            }

            // 水位上升测点数量
            long risingPoints = recentData.stream()
                    .filter(level -> level.getLevelChange() != null && level.getLevelChange().compareTo(BigDecimal.ZERO) > 0)
                    .count();

            // 水位下降测点数量
            long fallingPoints = recentData.stream()
                    .filter(level -> level.getLevelChange() != null && level.getLevelChange().compareTo(BigDecimal.ZERO) < 0)
                    .count();

            // 统计不同状态的测点数量
            long normalPoints = recentData.stream()
                    .filter(level -> level.getStatus() != null && level.getStatus() == 1)
                    .count();

            long lowPoints = recentData.stream()
                    .filter(level -> level.getStatus() != null && level.getStatus() == 2)
                    .count();

            long highPoints = recentData.stream()
                    .filter(level -> level.getStatus() != null && level.getStatus() == 3)
                    .count();

            // 3. 组装结果
            result.put("avgLevelChange", avgLevelChange);
            result.put("risingPoints", risingPoints);
            result.put("fallingPoints", fallingPoints);
            result.put("normalPoints", normalPoints);
            result.put("lowPoints", lowPoints);
            result.put("highPoints", highPoints);
            result.put("totalPoints", recentData.size());

            return result;
        } catch (Exception e) {
            log.error("获取地下水水位统计数据失败", e);
            throw new RuntimeException("获取地下水水位统计数据失败: " + e.getMessage());
        }
    }
}