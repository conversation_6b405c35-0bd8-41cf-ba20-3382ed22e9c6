import{e as s,y as o,a as f,v as D,j as P,i as R,x as z,s as $,w as S}from"./Point-WxyopZva.js";import{g as b,w as k,c_ as A,j as C,db as M,i as O,bk as T}from"./MapView-DaoQedLH.js";import{l as x,w as E,U as q}from"./widget-BcWKanF2.js";import{R as _,T as V,aO as j}from"./index-r0dFAfgr.js";import{m as N,y as J,h as L}from"./RasterVFDisplayObject-C4NlpKNN.js";import{f as G,u as H}from"./LayerView-BSt9B8Gh.js";import{a as W}from"./GraphicsView2D-DDTEO9AX.js";import{n as K}from"./HighlightGraphicContainer-B4wkFrY6.js";import{T as Q,x as X,d as Y}from"./dataUtils-DovfQoP5.js";import{a as Z}from"./BitmapContainer-ziwQ7v9F.js";import{h as ee}from"./Container-BwXq1a-x.js";import{l as te}from"./Bitmap-CraE42_6.js";import{v as ie}from"./ExportStrategy-BadISnDs.js";import{U as re}from"./pe-B8dP0-Ut.js";import{J as ae}from"./rasterProjectionHelper-BvgFmUDx.js";import{T as B}from"./enums-L38xj_2E.js";import{a as se}from"./WGLContainer-Dyx9110G.js";import{s as oe}from"./popupUtils-BjdidZV3.js";import{i as ne}from"./RefreshableLayerView-DUeNHzrW.js";import"./FramebufferObject-8j9PRuxE.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./number-CoJp78Rz.js";import"./cimAnalyzer-CMgqZsaO.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./alignmentUtils-CkNI7z7C.js";import"./definitions-826PWLuy.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./normalizeUtilsSync-NMksarRY.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./AttributeStoreView-B0-phoCE.js";import"./TiledDisplayObject-C5kAiJtw.js";import"./visualVariablesUtils-0WgcmuMn.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./MaterialKey-BYd7cMLJ.js";import"./Matcher-v9ErZwmD.js";import"./tileUtils-B7X19rIS.js";import"./libtess-lH4Jrtkh.js";import"./GeometryUtils-BRRfazic.js";import"./earcut-BJup91r2.js";import"./TurboLine-CDscS66C.js";import"./ExpandedCIM-C1laM-_7.js";import"./schemaUtils-DLXXqxNF.js";import"./utils-DPUVnAXL.js";import"./util-DPgA-H2V.js";import"./ComputedAttributeStorage-CF7WDnl8.js";import"./arcadeTimeUtils-CyWQANWo.js";import"./executionError-BOo4jP8A.js";import"./centroid-UTistape.js";import"./BaseGraphicContainer-Cqw9Xlck.js";import"./FeatureContainer-B5oUlI2-.js";import"./TileContainer-CC8_A7ZF.js";import"./vec3f32-nZdmKIgz.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./vec4f32-CjrfB-0a.js";import"./ProgramTemplate-tdUBoAol.js";import"./StyleDefinition-Bnnz5uyC.js";import"./config-MDUrh2eL.js";let h=class extends D{constructor(){super(...arguments),this.attached=!1,this.container=new ee,this.updateRequested=!1,this.type="imagery",this._bitmapView=new Z}destroy(){this.attached&&(this.detach(),this.attached=!1),this.updateRequested=!1}get updating(){return!this.attached||this.isUpdating()}update(e){this.strategy.update(e).catch(t=>{P(t)||R.getLogger(this.declaredClass).error(t)})}hitTest(e){return new b({attributes:{},geometry:e.clone(),layer:this.layer})}attach(){this.container.addChild(this._bitmapView);const e=this.layer.version>=10,t=this.layer.version>=10.1?this.layer.imageMaxHeight:2048,i=this.layer.version>=10.1?this.layer.imageMaxWidth:2048;this.strategy=new ie({container:this._bitmapView,imageNormalizationSupported:e,imageMaxHeight:t,imageMaxWidth:i,fetchSource:this._fetchImage.bind(this),requestUpdate:()=>this.requestUpdate()})}detach(){this.strategy.destroy(),this._bitmapView.removeAllChildren(),this.container.removeAllChildren(),this.updateRequested=!1}redraw(){this.strategy.updateExports(async e=>{const{source:t}=e;if(!t||t instanceof ImageBitmap)return;const i=await this.layer.applyRenderer({extent:t.extent,pixelBlock:t.originalPixelBlock??t.pixelBlock});t.filter=r=>this.layer.pixelFilter?this.layer.applyFilter(r):{...i,extent:t.extent}}).catch(e=>{P(e)||R.getLogger(this.declaredClass).error(e)})}requestUpdate(){this.updateRequested||(this.updateRequested=!0,this.view.requestUpdate())}isUpdating(){return this.strategy.updating||this.updateRequested}getPixelData(){if(this.updating)return null;const e=this.strategy.bitmaps;if(e.length===1&&e[0].source)return{extent:e[0].source.extent,pixelBlock:e[0].source.originalPixelBlock};if(e.length>1){const t=this.view.extent,i=e.map(a=>a.source).filter(a=>a.extent&&a.extent.intersects(t)).map(a=>({extent:a.extent,pixelBlock:a.originalPixelBlock})),r=Q(i,t);return _(r)?{extent:r.extent,pixelBlock:r.pixelBlock}:null}return null}async _fetchImage(e,t,i,r){var c;(r=r||{}).timeExtent=this.timeExtent,r.requestAsImageElement=!0,r.returnImageBitmap=!0;const a=await this.layer.fetchImage(e,t,i,r);if(a.imageBitmap)return a.imageBitmap;const n=await this.layer.applyRenderer(a.pixelData,{signal:r.signal}),l=new te(n.pixelBlock,(c=n.extent)==null?void 0:c.clone(),a.pixelData.pixelBlock);return l.filter=p=>this.layer.applyFilter(p),l}};s([o()],h.prototype,"attached",void 0),s([o()],h.prototype,"container",void 0),s([o()],h.prototype,"layer",void 0),s([o()],h.prototype,"strategy",void 0),s([o()],h.prototype,"timeExtent",void 0),s([o()],h.prototype,"view",void 0),s([o()],h.prototype,"updateRequested",void 0),s([o()],h.prototype,"updating",null),s([o()],h.prototype,"type",void 0),h=s([f("esri.views.2d.layers.imagery.ImageryView2D")],h);const le=h;class he extends se{constructor(){super(...arguments),this.symbolTypes=["triangle"]}get requiresDedicatedFBO(){return!1}prepareRenderPasses(t){const i=t.registerRenderPass({name:"imagery (vf)",brushes:[N],target:()=>this.children,drawPhase:B.MAP});return[...super.prepareRenderPasses(t),i]}doRender(t){this.visible&&t.drawPhase===B.MAP&&this.symbolTypes.forEach(i=>{t.renderPass=i,super.doRender(t)})}}let y=class extends D{constructor(e){super(e),this._loading=null,this.update=z((t,i)=>this._update(t,i).catch(r=>{P(r)||R.getLogger(this.declaredClass).error(r)}))}get updating(){return!!this._loading}redraw(e){if(!this.container.children.length)return;const t=this.container.children[0];t.symbolizerParameters=e,t.invalidateVAO(),this.container.symbolTypes=e.style==="wind_speed"?["scalar","triangle"]:e.style==="simple_scalar"?["scalar"]:["triangle"],this.container.requestRender()}async _update(e,t,i){if(!e.stationary)return;const{extent:r,spatialReference:a}=e.state,n=new k({xmin:r.xmin,ymin:r.ymin,xmax:r.xmax,ymax:r.ymax,spatialReference:a}),[l,c]=e.state.size;this._loading=this.fetchPixels(n,l,c,i);const p=await this._loading;this._addToDisplay(p,t,e.state),this._loading=null}_addToDisplay(e,t,i){if(V(e.pixelBlock))return this.container.children.forEach(l=>l.destroy()),void this.container.removeAllChildren();const{extent:r,pixelBlock:a}=e,n=new J(a);n.offset=[0,0],n.symbolizerParameters=t,n.rawPixelData=e,n.invalidateVAO(),n.x=r.xmin,n.y=r.ymax,n.pixelRatio=i.pixelRatio,n.rotation=i.rotation,n.resolution=i.resolution,n.width=a.width*t.symbolTileSize,n.height=a.height*t.symbolTileSize,this.container.children.forEach(l=>l.destroy()),this.container.removeAllChildren(),this.container.symbolTypes=t.style==="wind_speed"?["scalar","triangle"]:t.style==="simple_scalar"?["scalar"]:["triangle"],this.container.addChild(n)}};s([o()],y.prototype,"fetchPixels",void 0),s([o()],y.prototype,"container",void 0),s([o()],y.prototype,"_loading",void 0),s([o()],y.prototype,"updating",null),y=s([f("esri.views.2d.layers.imagery.ImageryVFStrategy")],y);const pe=y;let m=class extends A{constructor(){super(...arguments),this.attached=!1,this.container=new he,this.type="imageryVF",this._dataParameters={exportParametersVersion:0,bbox:"",symbolTileSize:0,time:""},this._fetchpixels=async(e,t,i,r)=>{const a=await this._projectFullExtentPromise,{symbolTileSize:n}=this.layer.renderer,{extent:l,width:c,height:p}=X(e,t,i,n,a);if(_(a)&&!a.intersects(e))return{extent:l,pixelBlock:null};const d={bbox:`${l.xmin}, ${l.ymin}, ${l.xmax}, ${l.ymax}`,exportParametersVersion:this.layer.exportImageServiceParameters.version,symbolTileSize:n,time:JSON.stringify(this.timeExtent||"")};if(this._canReuseVectorFieldData(d)){const u=this.getPixelData();if(_(u)&&`${u.extent.xmin}, ${u.extent.ymin}, ${u.extent.xmax}, ${u.extent.ymax}`===d.bbox)return u}const{pixelData:g}=await this.layer.fetchImage(l,c,p,{timeExtent:this.timeExtent,requestAsImageElement:!1,signal:r});this._dataParameters=d;const w=g==null?void 0:g.pixelBlock;return V(w)?{extent:l,pixelBlock:null}:{extent:l,pixelBlock:this.layer.rasterInfo.dataType==="vector-uv"?j(Y(w,"vector-uv")):w}}}get updating(){return!this.attached||this._strategy.updating}attach(){this._projectFullExtentPromise=this._getProjectedFullExtent(this.view.spatialReference),this._strategy=new pe({container:this.container,fetchPixels:this._fetchpixels}),this.handles.add(x(()=>this.layer.renderer,e=>this._updateSymbolizerParams(e),E),"attach")}detach(){this._strategy.destroy(),this.container.children.forEach(e=>e.destroy()),this.container.removeAllChildren(),this.handles.remove("attach"),this._strategy=this.container=this._projectFullExtentPromise=null}getPixelData(){var r;const e=(r=this.container.children[0])==null?void 0:r.rawPixelData;if(this.updating||!e)return null;const{extent:t,pixelBlock:i}=e;return{extent:t,pixelBlock:i}}hitTest(e){return new b({attributes:{},geometry:e.clone(),layer:this.layer})}update(e){this._strategy.update(e,this._symbolizerParams)}redraw(){const{renderer:e}=this.layer;e&&(this._updateSymbolizerParams(e),this._strategy.redraw(this._symbolizerParams))}_canReuseVectorFieldData(e){const t=this._dataParameters.exportParametersVersion===e.exportParametersVersion,i=this._dataParameters.time===e.time,r=this._dataParameters.symbolTileSize===e.symbolTileSize,a=this._dataParameters.bbox===e.bbox;return t&&i&&r&&a}async _getProjectedFullExtent(e){try{return await ae(this.layer.fullExtent,e)}catch{try{const i=(await re(this.layer.url,{query:{option:"footprints",outSR:e.wkid||JSON.stringify(e.toJSON()),f:"json"}})).data.featureCollection.layers[0].layerDefinition.extent;return i?k.fromJSON(i):null}catch{return null}}}_updateSymbolizerParams(e){e.type==="vector-field"&&(this._symbolizerParams=this.layer.symbolizer.generateWebGLParameters({pixelBlock:null}))}};s([o()],m.prototype,"attached",void 0),s([o()],m.prototype,"container",void 0),s([o()],m.prototype,"layer",void 0),s([o()],m.prototype,"timeExtent",void 0),s([o()],m.prototype,"type",void 0),s([o()],m.prototype,"view",void 0),s([o()],m.prototype,"updating",null),m=s([f("esri.views.2d.layers.imagery.VectorFieldView2D")],m);const me=m,ce=e=>{let t=class extends e{constructor(){super(...arguments),this.view=null}async fetchPopupFeatures(i,r){const{layer:a}=this;if(!i)throw new $("imagerylayerview:fetchPopupFeatures","Nothing to fetch without area",{layer:a});const{popupEnabled:n}=a,l=oe(a,r);if(!n||V(l))throw new $("imagerylayerview:fetchPopupFeatures","Missing required popupTemplate or popupEnabled",{popupEnabled:n,popupTemplate:l});const c=await l.getRequiredFields(),p=new M;p.timeExtent=this.timeExtent,p.geometry=i,p.outFields=c,p.outSpatialReference=i.spatialReference;const{resolution:d,spatialReference:g}=this.view,w=this.view.type==="2d"?new S(d,d,g):new S(.5*d,.5*d,g),{returnTopmostRaster:u,showNoDataRecords:F}=l.layerOptions||{returnTopmostRaster:!0,showNoDataRecords:!1},I={returnDomainValues:!0,returnTopmostRaster:u,pixelSize:w,showNoDataRecords:F,signal:_(r)?r.signal:null};return a.queryVisibleRasters(p,I).then(U=>U)}canResume(){var i;return!!super.canResume()&&!((i=this.timeExtent)!=null&&i.isEmpty)}};return s([o()],t.prototype,"layer",void 0),s([o()],t.prototype,"suspended",void 0),s([o(C)],t.prototype,"timeExtent",void 0),s([o()],t.prototype,"view",void 0),t=s([f("esri.views.layers.ImageryLayerView")],t),t};let v=class extends ce(ne(G(H))){constructor(){super(...arguments),this._exportImageVersion=-1,this._highlightGraphics=new O,this._highlightView=void 0,this.layer=null,this.subview=null}get pixelData(){const{subview:e}=this;return this.updating||!e?null:"getPixelData"in e?e.getPixelData():null}async hitTest(e,t){return this.subview?[{type:"graphic",graphic:this.subview.hitTest(e),layer:this.layer,mapPoint:e}]:null}update(e){var t;(t=this.subview)==null||t.update(e)}attach(){this.layer.increaseRasterJobHandlerUsage(),this._setSubView(),this.view&&(this._highlightView=new W({view:this.view,graphics:this._highlightGraphics,requestUpdateCallback:()=>this.requestUpdate(),container:new K(this.view.featuresTilingScheme)}),this.container.addChild(this._highlightView.container)),this.addAttachHandles([x(()=>this.layer.blendMode??"normal",e=>this.subview&&(this.subview.container.blendMode=e),E),x(()=>this.layer.effect??null,e=>this.subview&&(this.subview.container.effect=e),E),x(()=>this.layer.exportImageServiceParameters.version,e=>{e&&this._exportImageVersion!==e&&(this._exportImageVersion=e,this.requestUpdate())},q),x(()=>this.timeExtent,e=>{const{subview:t}=this;t&&(t.timeExtent=e,"redraw"in t?this.requestUpdate():t.redrawOrRefetch())},q),this.layer.on("redraw",()=>{const{subview:e}=this;e&&("redraw"in e?e.redraw():e.redrawOrRefetch())}),x(()=>this.layer.renderer,()=>this._setSubView())])}detach(){var e,t;this.layer.decreaseRasterJobHandlerUsage(),this.container.removeAllChildren(),this._detachSubview(this.subview),(e=this.subview)==null||e.destroy(),this.subview=null,(t=this._highlightView)==null||t.destroy(),this._exportImageVersion=-1}moveStart(){}viewChange(){}moveEnd(){this.requestUpdate()}highlight(e,t){if(!((Array.isArray(e)?e[0]:T.isCollection(e)?e.getItemAt(0):e)instanceof b))return{remove:()=>{}};let i=[];return Array.isArray(e)||T.isCollection(e)?i=e.map(r=>r.clone()):e instanceof b&&(i=[e.clone()]),this._highlightGraphics.addMany(i),{remove:()=>{this._highlightGraphics.removeMany(i)}}}async doRefresh(){this.requestUpdate()}isUpdating(){return!this.subview||this.subview.updating}_setSubView(){var i,r;if(!this.view)return;const e=(i=this.layer.renderer)==null?void 0:i.type;let t="imagery";if(e==="vector-field"?t="imageryVF":e==="flow"&&(t="flow"),this.subview){const{type:a}=this.subview;if(a===t)return this._attachSubview(this.subview),void(a==="flow"?this.subview.redrawOrRefetch():a==="imagery"&&this.layer.format==="lerc"?this.subview.redraw():this.requestUpdate());this._detachSubview(this.subview),(r=this.subview)==null||r.destroy()}this.subview=t==="imagery"?new le({layer:this.layer,view:this.view,timeExtent:this.timeExtent}):t==="imageryVF"?new me({layer:this.layer,view:this.view,timeExtent:this.timeExtent}):new L({layer:this.layer,layerView:this}),this._attachSubview(this.subview),this.requestUpdate()}_attachSubview(e){e&&!e.attached&&(e.attach(),e.attached=!0,this.container.addChildAt(e.container,0),e.container.blendMode=this.layer.blendMode,e.container.effect=this.layer.effect)}_detachSubview(e){e!=null&&e.attached&&(this.container.removeChild(e.container),e.detach(),e.attached=!1)}};s([o()],v.prototype,"pixelData",null),s([o()],v.prototype,"subview",void 0),v=s([f("esri.views.2d.layers.ImageryLayerView2D")],v);const Vt=v;export{Vt as default};
