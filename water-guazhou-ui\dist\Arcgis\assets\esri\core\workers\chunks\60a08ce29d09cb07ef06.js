"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5935],{75935:(t,e,o)=>{o.r(e),o.d(e,{default:()=>y});var i,r=o(43697),s=(o(66577),o(96674)),n=o(22974),d=o(5600),p=o(75215),l=o(52011),u=o(30556),a=o(10158),h=o(82971);let c=i=class extends s.wq{constructor(t){super(t),this.cacheHint=void 0,this.dynamicDataSource=void 0,this.gdbVersion=null,this.geometryPrecision=void 0,this.historicMoment=null,this.maxAllowableOffset=void 0,this.objectIds=null,this.orderByFields=null,this.outFields=null,this.outSpatialReference=null,this.relationshipId=void 0,this.start=void 0,this.num=void 0,this.returnGeometry=!1,this.returnM=void 0,this.returnZ=void 0,this.where=null}_writeHistoricMoment(t,e){e.historicMoment=t&&t.getTime()}writeStart(t,e){e.resultOffset=this.start,e.resultRecordCount=this.num||10,this.start>0&&null==this.where&&(e.definitionExpression="1=1")}clone(){return new i((0,n.d9)({cacheHint:this.cacheHint,dynamicDataSource:this.dynamicDataSource,gdbVersion:this.gdbVersion,geometryPrecision:this.geometryPrecision,historicMoment:this.historicMoment&&new Date(this.historicMoment.getTime()),maxAllowableOffset:this.maxAllowableOffset,objectIds:this.objectIds,orderByFields:this.orderByFields,outFields:this.outFields,outSpatialReference:this.outSpatialReference,relationshipId:this.relationshipId,start:this.start,num:this.num,returnGeometry:this.returnGeometry,where:this.where,returnZ:this.returnZ,returnM:this.returnM}))}};(0,r._)([(0,d.Cb)({type:Boolean,json:{write:!0}})],c.prototype,"cacheHint",void 0),(0,r._)([(0,d.Cb)({type:a.n,json:{write:!0}})],c.prototype,"dynamicDataSource",void 0),(0,r._)([(0,d.Cb)({type:String,json:{write:!0}})],c.prototype,"gdbVersion",void 0),(0,r._)([(0,d.Cb)({type:Number,json:{write:!0}})],c.prototype,"geometryPrecision",void 0),(0,r._)([(0,d.Cb)({type:Date})],c.prototype,"historicMoment",void 0),(0,r._)([(0,u.c)("historicMoment")],c.prototype,"_writeHistoricMoment",null),(0,r._)([(0,d.Cb)({type:Number,json:{write:!0}})],c.prototype,"maxAllowableOffset",void 0),(0,r._)([(0,d.Cb)({type:[Number],json:{write:!0}})],c.prototype,"objectIds",void 0),(0,r._)([(0,d.Cb)({type:[String],json:{write:!0}})],c.prototype,"orderByFields",void 0),(0,r._)([(0,d.Cb)({type:[String],json:{write:!0}})],c.prototype,"outFields",void 0),(0,r._)([(0,d.Cb)({type:h.Z,json:{read:{source:"outSR"},write:{target:"outSR"}}})],c.prototype,"outSpatialReference",void 0),(0,r._)([(0,d.Cb)({json:{write:!0}})],c.prototype,"relationshipId",void 0),(0,r._)([(0,d.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],c.prototype,"start",void 0),(0,r._)([(0,u.c)("start"),(0,u.c)("num")],c.prototype,"writeStart",null),(0,r._)([(0,d.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],c.prototype,"num",void 0),(0,r._)([(0,d.Cb)({json:{write:!0}})],c.prototype,"returnGeometry",void 0),(0,r._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:t=>({enabled:t})}}})],c.prototype,"returnM",void 0),(0,r._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:t=>({enabled:t})}}})],c.prototype,"returnZ",void 0),(0,r._)([(0,d.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],c.prototype,"where",void 0),c=i=(0,r._)([(0,l.j)("esri.rest.support.RelationshipQuery")],c),c.from=(0,p.se)(c);const y=c}}]);