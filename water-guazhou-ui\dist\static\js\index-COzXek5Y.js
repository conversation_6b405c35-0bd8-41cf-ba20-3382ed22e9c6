import{_ as V}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as W}from"./CardTable-rdWOL4_6.js";import{_ as z}from"./CardSearch-CB_HNR-Q.js";import{d as I,M as N,r as _,c as x,s as C,bB as q,S as M,o as A,g as G,n as R,q as S,i as v,al as H,b7 as J,ak as K}from"./index-r0dFAfgr.js";import{p as Q}from"./data-DPRqSSi2.js";import{c as U,b as X,e as Z,f as O}from"./waterInspection-DqEu1Oyl.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const w={class:"wrapper"},ce=I({__name:"index",setup(ee){const{$messageError:P,$messageWarning:Y,$messageSuccess:$}=N(),b=_({settings:""}),y=x([]),g=x(),T=x(),B=_({defaultParams:{type:null},filters:[{type:"department-user",label:"创建人员",field:"creator"},{type:"daterange",label:"创建时间",field:"fromTime",format:"YYYY-MM-DD"},{type:"input",label:"模板名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:C(H),click:()=>m()},{perm:!0,text:"重置",type:"default",svgIcon:C(J),click:()=>{var e;(e=T.value)==null||e.resetForm()}},{perm:!0,text:"添加",svgIcon:C(K),click:()=>{var t,a;o.title="新增",b.settings="";const e=(a=(t=o.group[0])==null?void 0:t.fields)==null?void 0:a.find(n=>n.field==="project");e.config.handleSelectChange=n=>L(n),k()}}]}]}),p=_({indexVisible:!0,columns:[{label:"模板名称",prop:"name",align:"center"},{label:"创建人",prop:"creatorName",align:"center"},{label:"备注",prop:"remark",align:"center"},{label:"创建时间",prop:"createTime",align:"center"}],operationWidth:"180px",operations:[{perm:!0,text:"查看",click:e=>{o.title="查看",k(e,!0)}},{perm:!0,text:"修改",click:e=>{o.title="修改",k(e)}},{perm:!0,text:"删除",type:"danger",click:e=>E(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{p.pagination.page=e,p.pagination.limit=t,m()}}}),o=_({dialogWidth:1e3,title:"新增",group:[{fields:[{type:"input",label:"模板名称",field:"name",rules:[{required:!0,message:"请输入模板名称"}],placeholder:"请输入模板名称"},{type:"select",label:"项目分类",field:"itemTypes",returnType:"str",multiple:!0,clearable:!1,options:Q,onChange:e=>D(e,b.settings)},{type:"table",field:"project",config:{height:"300px",dataList:[],selectList:[],border:!0,handleSelectChange:e=>L(e),columns:[{label:"项目分类",prop:"itemType"},{label:"项目名称",prop:"name"},{label:"巡检方法",prop:"method"},{label:"巡检要求",prop:"require"}],pagination:{hide:!0}}},{type:"textarea",label:"备注信息",field:"remark",placeholder:"请输入备注信息",colStyles:{marginTop:"20px"}}]}]}),L=e=>{y.value=e.map(t=>t.id),console.log(y.value)},D=async(e,t,a)=>{const n=e?e.join(","):null,i=o.group[0].fields.find(r=>r.field==="project");n?U({page:1,size:999,itemType:n,type:"水源"}).then(l=>{var d;const c=(d=l.data.data)==null?void 0:d.data;console.log("res",e);const s=c.filter(u=>t.indexOf(u.id)!==-1);a?q(()=>{i.config.dataList=s,i.config.selectList=s}):(i.config.dataList=c,q(()=>{i.config.selectList=s}))}):i.config.dataList=[]},k=(e,t)=>{var n,i,r;const a=(i=(n=o.group[0])==null?void 0:n.fields)==null?void 0:i.find(l=>l.field==="project");a.config.dataList=[],a.config.selectList=[],console.log(a.config.dataList),t?a.config.handleSelectChange=void 0:a.config.handleSelectChange=l=>L(l),o.group.map(l=>{l.fields.map(c=>{c.readonly=!!t})}),o.defaultValue={...e||{}},e&&(b.settings=e.settings,D(e.itemTypes.split(","),e.settings,t)),t?(console.log(e.itemTypes),o.submit=void 0):o.submit=l=>{var u,j;const s=((j=(u=a.config)==null?void 0:u.dataList)==null?void 0:j.filter(f=>y.value.includes(f.id))).map(f=>f.id),d=s==null?void 0:s.join(",");if(d&&d.length>0){const f={type:"水源",settings:d,remark:l.remark,name:l.name,id:e?e.id:null};M("确定提交？","提示信息").then(()=>{X(f).then(()=>{var h,F;(h=g.value)==null||h.resetForm(),(F=g.value)==null||F.closeDialog(),m(),$("提交成功")}).catch(h=>{P(h)})}).catch(()=>{})}else Y("请选择至少一个项目")},(r=g.value)==null||r.openDialog()},E=e=>{M("确定删除?","提示信息").then(()=>{Z(e.id).then(()=>{m()})}).catch(()=>{})},m=async()=>{var r,l,c,s;const e=((r=T.value)==null?void 0:r.queryParams)||{},[t,a]=e.fromTime||[],n={...e,page:p.pagination.page||1,size:p.pagination.limit||20,fromTime:t||null,toTime:a||null,type:"水源"},i=await O(n);console.log((l=i.data)==null?void 0:l.data.total),p.pagination.total=(c=i.data)==null?void 0:c.data.total,p.dataList=(s=i.data)==null?void 0:s.data.data};return A(async()=>{m()}),(e,t)=>{const a=z,n=W,i=V;return G(),R("div",w,[S(a,{ref_key:"refSearch",ref:T,config:v(B)},null,8,["config"]),S(n,{config:v(p),class:"card-table"},null,8,["config"]),S(i,{ref_key:"refForm",ref:g,config:v(o)},null,8,["config"])])}}});export{ce as default};
