<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.StoreInRecordDetailMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           main_id,
                           serial_id,
                           shelves_id,
                           device_rest_storage_count_by_serial_id_and_shelves_id(serial_id, shelves_id, tenant_id) as count,
                           num,
                           price,
                           tax_rete,
                           tenant_id<!--@sql from store_in_record_detail -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.store.StoreInRecordDetail">
        <result column="id" property="id"/>
        <result column="main_id" property="mainId"/>
        <result column="serial_id" property="serialId"/>
        <result column="shelves_id" property="shelvesId"/>
        <result column="num" property="num"/>
        <result column="count" property="count"/>
        <result column="price" property="price"/>
        <result column="tax_rete" property="taxRete"/>
        <result column="tenant_id" property="tenantId"/>
        <association property="deviceInfoResponse"
                     javaType="org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse"
                     column="{serialId=serial_id,tenantId=tenant_id}"
                     select="org.thingsboard.server.dao.sql.deviceType.DeviceMapper.getInfoBySerialId"/>
    </resultMap>

    <insert id="saveAll">
        INSERT INTO store_in_record_detail(id,
                                           main_id,
                                           serial_id,
                                           shelves_id,
                                           num,
                                           price,
                                           tax_rete,
                                           tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.mainId},
             #{element.serialId},
             #{element.shelvesId},
             #{element.num},
             #{element.price},
             #{element.taxRete},
             #{element.tenantId})
        </foreach>
    </insert>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from store_in_record_detail
        <where>
            <if test="mainId != null and mainId != ''">
                and main_id = #{mainId}
            </if>
            <if test="serialId != null and serialId != ''">
                and serial_id = #{serialId}
            </if>
            <if test="shelvesId != null and shelvesId != ''">
                and shelves_id = #{shelvesId}
            </if>
            <!--            <if test="num != null">-->
            <!--                and num=#{num}-->
            <!--            </if>-->
            <!--            <if test="price != null">-->
            <!--                and price=#{price}-->
            <!--            </if>-->
            <!--            <if test="taxRete != null">-->
            <!--                and tax_rete=#{taxRete}-->
            <!--            </if>-->
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update store_in_record_detail
        <set>
            <if test="serialId != null">
                serial_id = #{serialId},
            </if>
            <if test="shelvesId != null">
                shelves_id = #{shelvesId},
            </if>
            <if test="num != null">
                num = #{num},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="taxRete != null">
                tax_rete = #{taxRete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateAll">
        update store_in_record_detail
        <set>
            serial_id  = valueTable.serialId,
            shelves_id = valueTable.shelvesId,
            num        = valueTable.num,
            price      = valueTable.price,
            tax_rete   = valueTable.taxRete,
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.serialId},
             #{element.shelvesId},
             #{element.num},
             #{element.price},
             #{element.taxRete})
        </foreach>
        ) as valueTable(id, serialId, shelvesId, num, price, taxRete)
        where id = valueTable.id
    </update>

    <delete id="removeAllByMainId">
        delete
        from store_in_record_detail
        where main_id = #{id}
    </delete>
</mapper>