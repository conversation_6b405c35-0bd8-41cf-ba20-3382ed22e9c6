import{m as i}from"./index-r0dFAfgr.js";function u(t){return i({url:`/api/sp/circuitTemplate/${t}`,method:"delete"})}function a(t){return i({url:"/api/sp/circuitTemplate",method:"get",params:t})}function c(t){return i({url:"/api/sp/circuitTemplate",method:"post",data:t})}function n(t){return i({url:"/api/sp/circuitConfig",method:"get",params:t})}function s(t){return i({url:"/api/sp/circuitConfig",method:"post",data:t})}function o(t){return i({url:`/api/sp/circuitConfig/${t}`,method:"delete"})}function p(t){return i({url:"/api/sp/circuitPlan",method:"get",params:t})}function d(t){return i({url:"/api/sp/circuitPlan",method:"post",data:t})}function l(t){return i({url:`/api/sp/circuitPlan/${t}`,method:"delete"})}function f(t){return i({url:"/api/sp/circuitPlan",method:"delete",data:t})}function m(t){return i({url:"/api/sp/circuitTask",method:"get",params:t})}function T(t){return i({url:"/api/sp/circuitTask",method:"post",data:t})}function h(t){return i({url:`/api/sp/circuitTask/${t}`,method:"delete"})}function C(t){return i({url:`/api/sp/circuitTask/${t}/receive`,method:"post"})}function k(t,r){return i({url:`/api/sp/circuitTask/${t}/sendVerify/${r}`,method:"post"})}function g(t,r){return i({url:`/api/sp/circuitTask/${t}/verify/${r}`,method:"post"})}function $(t){return i({url:"/api/circuitTaskItem",method:"get",params:t})}export{s as a,c as b,n as c,o as d,u as e,a as f,f as g,d as h,l as i,p as j,T as k,h as l,$ as m,m as n,C as r,k as s,g as v};
