/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.VirtualId;
import org.thingsboard.server.common.data.virtual.Virtual;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.SearchTextEntity;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.VIRTUAL_COLUMN_FAMILY_NAME)
public class VirtualEntity extends BaseSqlEntity<Virtual> implements SearchTextEntity<Virtual> {
    @Column(name = ModelConstants.VIRTUAL_TENANT_ID)
    private String tenantId;
    @Column(name = ModelConstants.VIRTUAL_NAME)
    private String name;
    //公式
    @Column(name = ModelConstants.VIRTUAL_FORULA)
    private String formula;
    //单位
    @Column(name = ModelConstants.VIRTUAL_TYPE)
    private String type;
    //group
    @Column(name = ModelConstants.VIRTUAL_GROUP)
    private String virtualGroup;

    @Column(name = ModelConstants.VIRTUAL_UNIT)
    private String unit;

    //序列号
    @Column(name = ModelConstants.VIRTUAL_SERIAL_NUMBER)
    private String serialNumber;

    public VirtualEntity() {
    }

    public VirtualEntity(Virtual virtual) {
        if (virtual.getId() != null)
            this.setId(virtual.getUuidId());
        if (virtual.getTenantId() != null)
            this.tenantId = UUIDConverter.fromTimeUUID(virtual.getTenantId().getId());
        this.name = virtual.getName();
        this.formula = virtual.getFormula();
        this.type = virtual.getType();
        this.virtualGroup=virtual.getVirtualGroup();
        this.serialNumber=virtual.getSerialNumber();
        this.unit=virtual.getUnit();
    }

    @Override
    public Virtual toData() {
        Virtual virtual=new Virtual(new VirtualId(getId()));
        virtual.setTenantId(new TenantId(toUUID(tenantId)));
        virtual.setFormula(formula);
        virtual.setVirtualGroup(virtualGroup);
        virtual.setName(name);
        virtual.setType(type);
        virtual.setSerialNumber(serialNumber);
        virtual.setUnit(getUnit());
        return virtual;

    }

    @Override
    public String getSearchTextSource() {
        return name;
    }

    @Override
    public void setSearchText(String searchText) {
        this.name=searchText;
    }
}
