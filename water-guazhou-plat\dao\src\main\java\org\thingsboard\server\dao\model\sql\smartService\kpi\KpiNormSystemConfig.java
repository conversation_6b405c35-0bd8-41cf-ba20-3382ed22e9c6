package org.thingsboard.server.dao.model.sql.smartService.kpi;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * kpi指标配置
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-15
 */
@TableName("tb_service_kpi_norm_system_config")
@Data
public class KpiNormSystemConfig {

    @TableId
    private String id;

    private String mainId;

    private String code;

    private String type;

    private String name;

    private Integer startValue;

    private Integer endValue;

    private Double score;

    private String enable;

    private Date updateTime;

    private String tenantId;

}
