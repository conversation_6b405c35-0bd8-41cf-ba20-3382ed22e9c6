package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageTemplate;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class WxMessageTemplateSaveRequest extends SaveRequest<WxMessageTemplate> {
    // 模板名称
    private String name;

    // 模板Id
    private String templateId;

    // 页面模板JSON
    private String template;

    @Override
    protected WxMessageTemplate build() {
        WxMessageTemplate entity = new WxMessageTemplate();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected WxMessageTemplate update(String id) {
        WxMessageTemplate entity = new WxMessageTemplate();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(WxMessageTemplate entity) {
        entity.setName(name);
        entity.setTemplateId(templateId);
        entity.setTemplate(template);
    }
}