package org.thingsboard.server.dao.smartProduction.sludge;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.SludgeRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.sludge.Sludge;
import org.thingsboard.server.dao.sql.smartProduction.sludge.SludgeMapper;

import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Service
public class SludgeServiceImpl implements SludgeService {

    @Autowired
    private SludgeMapper sludgeMapper;

    @Override
    public Sludge save(Sludge sludge) {
        if (StringUtils.isBlank(sludge.getId())) {
            sludge.setCreateTime(new Date());
            sludgeMapper.insert(sludge);
        } else {
            sludgeMapper.updateById(sludge);
        }
        return sludge;
    }


    @Override
    public PageData<Sludge> getList(SludgeRequest request) {
        IPage<Sludge> page = new Page<>(request.getPage(), request.getSize());
        IPage<Sludge> result = sludgeMapper.getList(page, request);
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public JSONObject analysis(String year, String tenantId) {
        // 时间处理
        List<JSONObject> coutObject = sludgeMapper.countByYear(year, tenantId);
        Map<Integer, JSONObject> map = new HashMap<>();
        String month;
        for (JSONObject jsonObject : coutObject) {
            month = jsonObject.getString("timeMonth");
            map.put(Integer.valueOf(month.substring(0, month.length() - 1)), jsonObject);
        }

        // 当前月
        JSONObject result = new JSONObject();
        result.put("month", new JSONArray());
        result.put("gross", new JSONArray());
        result.put("tare", new JSONArray());
        result.put("net", new JSONArray());
        int monthNow = LocalDate.now().getMonthValue();
        if (!year.equals(LocalDate.now().getYear() + "")) {
            monthNow = 12;
        }
        for (int i = 0; i < monthNow; i++) {
            result.getJSONArray("month").add((i + 1) + "月");
            if (map.get(i + 1) == null) {
                result.getJSONArray("gross").add(0);
                result.getJSONArray("tare").add(0);
                result.getJSONArray("net").add(0);
            } else {

                result.getJSONArray("gross").add(map.get(i + 1).get("gross"));
                result.getJSONArray("tare").add(map.get(i + 1).get("tare"));
                result.getJSONArray("net").add(map.get(i + 1).get("net"));
            }

        }

        return result;
    }

    @Override
    public void delete(List<String> ids) {
        sludgeMapper.deleteBatchIds(ids);
    }

}
