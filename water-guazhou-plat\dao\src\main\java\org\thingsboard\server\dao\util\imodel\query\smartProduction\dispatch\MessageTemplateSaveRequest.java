package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageTemplate;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class MessageTemplateSaveRequest extends SaveRequest<MessageTemplate> {
    // 消息模板名称
    @NotNullOrEmpty
    private String name;

    // 短信模板编号
    @NotNullOrEmpty
    private String code;

    // 短信模板签名
    @NotNullOrEmpty
    private String signKey;

    // 消息内容
    @NotNullOrEmpty
    private String content;

    public MessageTemplate build() {
        MessageTemplate entity = new MessageTemplate();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    public MessageTemplate update(String id) {
        MessageTemplate entity = new MessageTemplate();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(MessageTemplate entity) {
        entity.setName(name);
        entity.setContent(content);
    }
}