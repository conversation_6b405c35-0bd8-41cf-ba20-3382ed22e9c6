package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_COMMAND_LOG_TABLE_NAME)
@TableName(ModelConstants.TB_COMMAND_LOG_TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
public class CommandLog {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_COMMAND_LOG_OPTION_USER_ID)
    private String optionUserId;

    @Column(name = ModelConstants.TB_COMMAND_LOG_TIME)
    private Date time;

    @Column(name = ModelConstants.TB_COMMAND_LOG_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.TB_COMMAND_LOG_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.TB_COMMAND_LOG_ATTR)
    private String attr;

    @Column(name = ModelConstants.TB_COMMAND_LOG_VALUE)
    private String value;

    @Column(name = ModelConstants.TB_COMMAND_LOG_RESULT)
    private String result;

    @Column(name = ModelConstants.TB_COMMAND_LOG_PARAM)
    private String param;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
