package org.thingsboard.server.dao.util.imodel.query.store;

import org.thingsboard.server.dao.model.sql.store.StoreInRecordDetail;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class StoreInRecordDetailPageRequest extends AdvancedPageableQueryEntity<StoreInRecordDetail, StoreInRecordDetailPageRequest> {
    // 入库单主表ID
    private String mainId;

    // 设备编码
    private String serialId;

    // 货架ID
    private String shelvesId;
}
