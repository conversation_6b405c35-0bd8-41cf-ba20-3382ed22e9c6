import request from '@/plugins/axios';

export * from './gis/area';
export * from './gis/plan';
export * from './gis/workorder';
export * from './gis/maintenance';
export * from './gis/locas';
// ----------------巡检 ---------------

/**
 * 获取指定任务的历史轨迹数据
 * @param taskId 任务id
 * @returns
 */
export const getTrack = (taskId: string) =>
  request({
    method: 'get',
    url: `/api/taskTrackHistory/getTrack?contentId=${taskId}`
  });
