import{d as u,j as c,k as d,u as I,c as g,r as p,S as _,a as w,b as n,o as G,g as C,h as m,i as F,_ as S}from"./index-r0dFAfgr.js";const N=u({__name:"ThemeInfo",setup(T){const i=c(),r=d(),f=I(),t=g(),o=p({group:[{fieldset:{type:"simple",desc:"通知相关"},fields:[{type:"switch",label:"消息通知:",field:"MessageNotification",activeText:"接收",inActiveText:"关闭",inlinePrompt:!1,inActiveColor:"#66b1ff",aInfo:!0,onChange:e=>{i.SET_MessageNotification(e)}},{labelWidth:"200px",type:"switch",label:"语音播报(需开启消息通知):",field:"VoiceBroadcast",activeText:"开启",inActiveText:"关闭",inlinePrompt:!1,inActiveColor:"#66b1ff",aInfo:!0,onChange:e=>{i.SET_VoiceBroadcast(e)}}]},{fieldset:{type:"simple",desc:"主题相关"},fields:[{type:"radio",label:"菜单风格:",field:"menuType",aInfo:!0,options:[{label:"下拉面板",value:"top"},{label:"侧边面板",value:"left"}],onChange:e=>{i.TOGGLE_menuType(e)}},{type:"switch",label:"标签导航:",field:"showTags",activeText:"显示",inActiveText:"隐藏",aInfo:!0,width:60,onChange:e=>{r.TOGGLE_showTags(e)}}]},{fieldset:{type:"simple",desc:"地图相关"},fields:[{type:"radio",label:"地图底图:",field:"gisDefaultBaseMap",aInfo:!0,options:[{label:"矢量图",value:"vec_w"},{label:"卫星图",value:"img_w"}],onChange:e=>{var a;window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap=e,window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi&&(window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi=e==="img_w"?"cia_w":"cva_w"),(a=t.value)!=null&&a.dataForm&&(t.value.dataForm.gisDefaultPoi=window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi)}},{handleHidden:(e,a,s)=>{s.hidden=e.gisDefaultBaseMap==="img_w"},type:"radio",label:"地图主题:",field:"gisDefaultBaseMapFilterColor",aInfo:!0,options:[{label:"象牙白",value:""},{label:"典雅黑",value:"rgba(255, 255, 255, 0.0)"}],onChange:e=>{window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMapFilterColor=e}},{type:"switch",label:"地名标注:",field:"showPoi",activeText:"显示",inActiveText:"隐藏",aInfo:!0,width:60,onChange:e=>{var a;e===!1?window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi="":window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi=e==="img_w"?"cia_w":"cva_w",(a=t.value)!=null&&a.dataForm&&(t.value.dataForm.gisDefaultPoi=window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi)}},{type:"input",field:"gisDefaultPoi",aInfo:!0,hidden:!0},{type:"btn-group",btns:[{perm:!0,text:"保存",loading:()=>!!o.submitting,click:()=>{var e;return(e=t.value)==null?void 0:e.Submit()}},{perm:!0,text:"同步账号配置",type:"default",click:()=>l()}]}]}],defaultValue:{},submit:e=>{_("确定保存?","提示信息").then(async()=>{try{o.submitting=!0;const a=typeof e.additionalInfo=="string"?JSON.parse(e.additionalInfo):e.additionalInfo,s={...e,additionalInfo:{...a||{}}};await w(s),l(),n.success("保存成功")}catch{n.error("系统错误")}o.submitting=!1}).catch(()=>{})}}),l=async()=>{if(t.value)try{await f.GetInfo(),o.defaultValue={...f.user||{},isDark:!!i.isDark,menuType:i.menuType,showTags:r.showTags,gisDefaultBaseMap:window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap,showPoi:!!window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi,gisDefaultBaseMapFilterColor:window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMapFilterColor,gisDefaultPoi:window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi,MessageNotification:!1,VoiceBroadcast:!1},t.value.resetForm()}catch{n.error("同步失败")}};return G(()=>{l()}),(e,a)=>{const s=S;return C(),m(s,{ref_key:"refForm",ref:t,config:F(o)},null,8,["config"])}}});export{N as _};
