package org.thingsboard.server.dao.maintainCircuit.maintain;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskC;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskM;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainTaskCMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainTaskMMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class MaintainTaskMServiceImpl implements MaintainTaskMService {

    @Autowired
    private MaintainTaskMMapper maintainTaskMMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private MaintainTaskCMapper maintainTaskCMapper;

    @Override
    public PageData getList(String code, String name, String planName, String teamName, String userName, String userId, String status, String auditStatus, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId) {

        List<MaintainTaskM> maintainTaskMList = maintainTaskMMapper.getList(code, name, planName, teamName, userName, userId, status, auditStatus, startStartTime, startEndTime, endStartTime, endEndTime, page, size, tenantId);

        int total = maintainTaskMMapper.getListCount(code, name, planName, teamName, userName, userId, status, auditStatus, startStartTime, startEndTime, endStartTime, endEndTime, tenantId);

        return new PageData(total, maintainTaskMList);
    }


    @Override
    public MaintainTaskM getDetail(String mainId) {
        MaintainTaskM maintainTaskM = maintainTaskMMapper.getById(mainId);
        List<MaintainTaskC> maintainTaskCList = maintainTaskCMapper.getList(maintainTaskM.getId());
        // 所属分类链表
        for (MaintainTaskC maintainTaskC : maintainTaskCList) {
            this.setType(maintainTaskC);
        }

        maintainTaskM.setMaintainTaskCList(maintainTaskCList);
        return maintainTaskM;
    }

    @Override
    public boolean checkAuditor(String id, String userId) {
        MaintainTaskM maintainTaskM = maintainTaskMMapper.selectById(id);
        if (maintainTaskM.getAuditor() != null && !userId.equals(maintainTaskM.getAuditor())) {
            return false;
        }
        return true;
    }

    @Override
    public IstarResponse receive(String id, String userId) {
        MaintainTaskM maintainTaskM = maintainTaskMMapper.selectById(id);
        if (maintainTaskM == null) {
            return IstarResponse.error("该任务不存在");
        }
        if (!userId.equals(maintainTaskM.getUserId())) {
            return IstarResponse.error("您没有接收权限");
        }
        if (!"0".equals(maintainTaskM.getStatus())) {
            return IstarResponse.error("该任务已接收");
        }

        maintainTaskM.setStatus("1");
        maintainTaskM.setRealStartTime(new Date());

        maintainTaskMMapper.updateById(maintainTaskM);

        return IstarResponse.ok("接收成功");
    }

    @Override
    public Integer getNotCompleteNum(String userId, String tenantId) {
        return maintainTaskMMapper.getNotCompleteNum(userId, tenantId);
    }

    @Override
    public MaintainTaskM save(MaintainTaskM maintainTaskM) {

        if (StringUtils.isBlank(maintainTaskM.getId())) {
            maintainTaskM.setCode("BY" + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));;
            maintainTaskM.setAuditStatus("0");;
            maintainTaskM.setStatus("0");
            maintainTaskM.setCreateTime(new Date());

            maintainTaskMMapper.insert(maintainTaskM);
        } else {
            maintainTaskMMapper.updateById(maintainTaskM);
        }

        Map deleteMap = new HashMap<>();
        deleteMap.put("main_id", maintainTaskM.getId());
        maintainTaskCMapper.deleteByMap(deleteMap);

        if (maintainTaskM.getMaintainTaskCList() != null) {
            for (MaintainTaskC maintainTaskC : maintainTaskM.getMaintainTaskCList()) {
                maintainTaskC.setMainId(maintainTaskM.getId());
                maintainTaskC.setTenantId(maintainTaskM.getTenantId());
                maintainTaskCMapper.insert(maintainTaskC);
            }
        }

        return maintainTaskM;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        maintainTaskMMapper.deleteBatchIds(ids);

        // 删除子表
        QueryWrapper<MaintainTaskC> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("main_id", ids);
        maintainTaskCMapper.delete(queryWrapper);

        return IstarResponse.ok("删除成功");
    }

    @Override
    public void reviewer(MaintainTaskM maintainTaskM) {
        MaintainTaskM maintainTaskM1 = new MaintainTaskM();
        maintainTaskM1.setId(maintainTaskM.getId());
        maintainTaskM1.setAuditor(maintainTaskM.getAuditor());
        maintainTaskM1.setAuditRemark(maintainTaskM.getAuditRemark());
        maintainTaskM1.setAuditStatus(maintainTaskM.getAuditStatus());
        maintainTaskM1.setAuditTime(new Date());

        maintainTaskMMapper.updateById(maintainTaskM1);
    }

    @Override
    public void changeStatus(MaintainTaskM maintainTaskM) {
        MaintainTaskM maintainTaskM1 = new MaintainTaskM();
        maintainTaskM1.setId(maintainTaskM.getId());
        maintainTaskM1.setStatus(maintainTaskM.getStatus());

        maintainTaskMMapper.updateById(maintainTaskM1);
    }

    @Override
    public List<MaintainTaskM> findAll() {
        return maintainTaskMMapper.selectByMap(new HashMap<>());
    }

    @Override
    public List statistics(Long startTime, Long endTime, String tenantId) {
        return maintainTaskMMapper.statistics(startTime == null ? null : new Date(startTime), endTime == null ? null : new Date(endTime), tenantId);
    }


    private void setType(MaintainTaskC maintainTaskC) {
        if (StringUtils.isBlank(maintainTaskC.getTypeId())) {
            maintainTaskC.setLinkedType("-");
            return;
        }
        String linkedType = "-";
        String topType = "-";
        DeviceType deviceType = null;
        String parentId = maintainTaskC.getTypeId();
        for (int i = 0; i < 5; i++) {
            deviceType = deviceTypeMapper.selectById(parentId);
            if (deviceType == null) {
                break;
            }
            linkedType = deviceType.getName() + ">" + linkedType;
            if (StringUtils.isBlank(deviceType.getParentId())) {
                topType = deviceType.getName();
                break;
            }
            parentId = deviceType.getParentId();
        }
        if (linkedType.length() >= 2) {
            linkedType = linkedType.substring(0, linkedType.length() - 2);
        }

        maintainTaskC.setLinkedType(linkedType);
        maintainTaskC.setTopType(topType);
        maintainTaskC.setType(linkedType);
        try {
            if (linkedType.contains(">")) {
                maintainTaskC.setType(linkedType.substring(linkedType.lastIndexOf(">") + 1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 上次操作时间
        MaintainTaskC maintainPlanC1 = maintainTaskCMapper.selectFirstByDeviceLabelCode(maintainTaskC.getDeviceLabelCode(), maintainTaskC.getCreateTime());
        if (maintainPlanC1 != null) {
            maintainTaskC.setLastModifyTime(maintainPlanC1.getCreateTime());
        }
    }


    private boolean checkTimeIsToday(Date nextExecuteTime) throws ParseException {
        String string = DateUtils.date2Str(new Date(), DateUtils.DATE_FORMATE_DAY);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_DEFAULT);
//        Date todayStart = dateFormat.parse(string + " 00:00:00");
        Date todayEnd = dateFormat.parse(string + " 23:59:59");

        if (/*nextExecuteTime.getTime() > todayStart.getTime() && */nextExecuteTime.getTime() < todayEnd.getTime()) {
            return true;
        }

        return false;
    }

}
