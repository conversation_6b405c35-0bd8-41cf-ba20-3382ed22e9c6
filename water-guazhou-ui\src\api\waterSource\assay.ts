import request from '@/plugins/axios'

// 获取化验记录列表
export function getWaterQualityTestList(params: any) {
  return request({
    url: '/api/assay/list',
    method: 'get',
    params
  })
}

// 保存化验记录
export function saveWaterQualityTest(data: {
  id?: string
  reportName: string
  samplingLocation: string
  testingUnit: string
  testResults: string
  testDate: string
  reportFile?: string
  remark?: string
}) {
  return request({
    url: '/api/assay',
    method: 'post',
    data
  })
}

// 修改化验记录
export function updateWaterQualityTest(data: {
  id: string
  reportName: string
  samplingLocation: string
  testingUnit: string
  testResults: string
  testDate: string
  reportFile?: string
  remark?: string
}) {
  return request({
    url: '/api/assay/update',
    method: 'post',
    data
  })
}

// 删除化验记录
export function deleteWaterQualityTest(idList: string[]) {
  return request({
    url: '/api/assay',
    method: 'delete',
    data: idList
  })
}

// 获取单个化验记录
export function getWaterQualityTestById(id: string) {
  return request({
    url: `/api/assay/${id}`,
    method: 'get'
  })
}
