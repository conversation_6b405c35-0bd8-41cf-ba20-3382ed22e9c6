package org.thingsboard.server.dao.sql.workOrder;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.OrderFollowEntity;

import java.util.List;

@Deprecated
public interface OrderFollowRepository extends JpaRepository<OrderFollowEntity, String> {
    List<OrderFollowEntity> findByUserId(String userId);

    OrderFollowEntity findByUserIdAndOrderId(String userId, String orderId);
}
