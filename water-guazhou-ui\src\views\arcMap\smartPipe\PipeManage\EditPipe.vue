<!-- 管网编辑 -->
<template>
  <RightDrawerMap ref="refMap" title="管网编辑" @map-loaded="onMaploaded">
    <Form ref="refForm" :config="layerSelector.FormConfig"></Form>
    <template #map-bars>
      <div class="top-toolbar">
        <el-popover
          ref="refEditorPop"
          trigger="click"
          :popper-class="useAppStore().isDark ? 'darkblue' : ''"
        >
          <template #reference>
            <el-button :text="true">
              <span> 编辑器 </span><Icon :icon="'ep:arrow-down'"></Icon>
            </el-button>
          </template>
          <dl class="edit-toogle">
            <dd :class="[state.editing ? 'disabled' : '']" @click="startEdit">
              <Icon :icon="'ep:edit'"></Icon><span>开始编辑</span>
            </dd>
            <dd
              class=""
              :class="[state.editing ? '' : 'disabled']"
              @click="endEdit"
            >
              <Icon :icon="'ep:edit-pen'"></Icon>
              <span>结束编辑</span>
            </dd>
            <dt>选择可编辑数据</dt>
            <dd
              :class="{
                disabled: !state.editing,
                active: editArea.curType.value === 'polygon'
              }"
              @click="(e) => handleDrawEditableArea('polygon')"
            >
              <Icon :icon="'gis:polygon-hole-pt'"></Icon>
              <span>按多边形选择</span>
            </dd>
            <dd
              :class="{
                disabled: !state.editing,
                active: editArea.curType.value === 'rectangle'
              }"
              @click="(e) => handleDrawEditableArea('rectangle')"
            >
              <Icon :icon="'gis:rectangle-pt'"></Icon>
              <span>按矩形选择</span>
            </dd>
            <dd
              :class="{
                disabled: !state.editing,
                active: editArea.curType.value === 'circle'
              }"
              @click="(e) => handleDrawEditableArea('circle')"
            >
              <Icon :icon="'gis:circle'"></Icon>
              <span>按圆形选择</span>
            </dd>
            <dt>编辑操作</dt>
            <dd
              :class="{
                disabled: !state.editing
              }"
              @click="handleAddFeature"
            >
              <Icon :icon="'mdi:plus-circle-outline'"></Icon>
              <span>添加要素</span>
            </dd>
            <dt>配置</dt>
            <dd id="useCapture" :class="[state.useCapture ? 'active' : '']">
              <el-checkbox
                v-model="state.useCapture"
                @change="handleSnapping"
              ></el-checkbox>
              <label for="useCapture">使用捕捉</label>
            </dd>
            <!-- <dd
              id="toolTipBeforeComfirm"
              :class="[state.toolTipBeforeComfirm ? 'active' : '']"
            >
              <el-checkbox v-model="state.toolTipBeforeComfirm"></el-checkbox>
              <label for="toolTipBeforeComfirm">编辑确认提示</label>
            </dd> -->
            <!-- <dd :class="[state.addPointDeviceAtCrossPoint ? 'active' : '']">
              <el-checkbox
                id="addPointDeviceAtCrossPoint"
                v-model="state.addPointDeviceAtCrossPoint"
              ></el-checkbox>
              <label for="addPointDeviceAtCrossPoint">交点添加点设备</label>
            </dd> -->
          </dl>
        </el-popover>
        <Button
          v-for="(btn, i) in topBarBtns"
          :key="i"
          :size="'default'"
          :config="btn"
        ></Button>
      </div>

      <Panel
        v-if="state.isMounted"
        ref="refPanel"
        :title="state.isAddingNewFeature ? '添加要素' : '编辑属性'"
        :full-content="true"
        :telport="'#arcmap-wrapper'"
        :show-close="true"
        :max-min="false"
        class="pipe-attr-panel-editable"
      >
        <!-- 移除了显示"暂无数据"的区域 -->
        <div class="edit-panel-content overlay-y">
          <Form ref="refFormEdit" :config="editForm.FormConfig"></Form>
        </div>
        <div class="edit-panel-footer">
          <el-button
            type="info"
            icon="ep:copy-document"
            @click="handleCopyAttributes"
            style="margin-right: 10px;"
          >
            复制属性
          </el-button>
          <!-- 根据当前操作类型显示不同的按钮 -->
          <el-button
            v-if="state.isAddingNewFeature"
            type="primary"
            :icon="Promotion"
            :loading="editForm.FormConfig.submitting"
            @click="handleAddFeatureSubmit"
          >
            添加要素
          </el-button>
          <el-button
            v-else
            type="primary"
            :icon="Promotion"
            :loading="editForm.FormConfig.submitting"
            @click="handleEditAttributesSubmit"
          >
            保存属性
          </el-button>
        </div>
      </Panel>
      <Panel
        v-if="state.isMounted"
        ref="refPanelMerge"
        :modal="false"
        :title="'合并'"
        :full-content="true"
        :telport="'#arcmap-wrapper'"
        :show-close="true"
        :max-min="false"
        class="pipe-attr-panel-editable"
      >
        <div class="edit-panel-content overlay-y">
          <FormTree :config="mergePipe.TreeData.value"></FormTree>
        </div>

        <div class="edit-panel-footer">
          {{
            mergePipe.curNode.value
              ? '合并到：' + mergePipe.curNode.value.label
              : '请选择保留的设备'
          }}
          <el-button
            type="primary"
            :icon="Promotion"
            @click="handleMergeSubmit"
          >
            确定
          </el-button>
        </div>
      </Panel>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import { PostGisOperateLog } from '@/api/system/gisSetting';
import { useSketch } from '@/hooks/arcgis';
import { useAppStore } from '@/store';
import { useUserStore } from '@/store/modules/user';
import moment from 'moment';
import {
  Equals,
  createGraphic,
  // generate4548Graphic, // 未使用
  getGraphicLayer,
  intersects,
  refreshPipeLayer,
  setMapCursor,
  excuteIdentifyByGeoserver
} from '@/utils/MapHelper';
import { SLMessage, SLConfirm } from '@/utils/Message';
import * as coordinateFormatter from '@arcgis/core/geometry/coordinateFormatter';
import { Promotion } from '@element-plus/icons-vue';
import { ElPopover } from 'element-plus';
// 导入 GeoServer WFS-T 工具函数
import { addFeature, updateFeatureAttributes } from '@/utils/geoserver/wfsTransactionUtils';
// @ts-ignore
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';
import { EGigLogFunc, EGisLogApp, EGisLogOperateType } from '../../config';
import {
  useEditArea,
  useEditForm,
  useLayerSelector,
  useMergePipe,
  useRectSelect,
  useSplit
} from './hooks/editPipe';

// 日期格式化常量
const formatterDate = 'YYYY-MM-DD';
const formatterDateTime = 'YYYY-MM-DD HH:mm:ss';

const refForm = ref<IFormIns>();
const refEditorPop = ref<InstanceType<typeof ElPopover>>();
// 使用 any 类型绕过类型检查
const refPanelMerge = ref<any>();
const refPanel = ref<any>();
const refFormEdit = ref<IFormIns>();
// 不再需要 disableUpdate 变量，更新按钮始终可点击
/** ** 图层选择 ***** */
const layerSelector = reactive(
  useLayerSelector(refForm, (data) => {
    refreshEditAbleFeatures();
    state.pointEdiable = false;
    rectSelect.destroy();
  })
);
/** 可编辑区域 */
const editArea = useEditArea(() => refreshEditAbleFeatures());
/** 属性表单 */
const editForm = reactive(
  useEditForm(refFormEdit, (data) => {
    staticState.graphic = staticState.graphics.length
      ? staticState.graphics.find(
          (item) => item.attributes.OBJECTID === data.value
        )
      : undefined;
    staticState.graphic &&
      editForm
        .resetForm(refForm.value?.dataForm.layerid?.[0], staticState.graphic)
        .then(() => {
          // 移除 disableUpdate 设置
          // disableUpdate.value = false;
        })
        .catch((e: any) => {
          console.log(e);

          // 移除 disableUpdate 设置
          // disableUpdate.value = true;
        });
  })
);
const mergePipe = useMergePipe();
const splitPipe = useSplit((res) => {
  const layername = layerSelector.curLayerInfo?.layername;
  PostGisOperateLog({
    optionName: EGigLogFunc.GWBIANJI,
    type: EGisLogApp.BASICGIS,
    content: `拆分OBJECTID为${res[0]?.deleteFeatureResults[0]?.objectId}的${layername},拆分后OBJECTID:[${res[1]?.addFeatureResults[0]?.objectId},${res[2]?.addFeatureResults[0]?.objectId}]`,
    optionType: EGisLogOperateType.UPDATE
  }).catch(() => {
    console.log('生成gis操作日志失败');
  });
  refreshPipeLayer(staticState.view);
  refreshEditAbleFeatures();
});
const rectSelect = reactive(
  useRectSelect((graphic) => {
    if (!staticState.sketch) {
      initEditSketch();
    }
    updateSelectedFeatures(graphic);
    refreshMergeForm();
  })
);

const updateSelectedFeatures = (graphic: __esri.Graphic) => {
  debugger
  staticState.clonedGraphics = [];
  staticState.graphics = [];
  staticState.graphicsLayer?.graphics
    .filter((item) => {
      const hasAttri = !!item.attributes;
      const isIntersected = intersects(item.geometry, graphic.geometry);
      return isIntersected && hasAttri;
    })
    .map((item) => {
      staticState.clonedGraphics.push(
        createGraphic({
          geometry: item.geometry,
          symbol: item.symbol,
          attributes: item.attributes
        })
      );
      staticState.graphics.push(item);
    });
  // 激活选择的要素
  staticState.sketch?.update(staticState.graphics);
  refreshEditForm();
};
let state = reactive<{
  isMounted: boolean;
  curPage: 'index' | 'add';
  timer: any;
  /* 处于可编辑状态 */
  editing: boolean;
  /** 使用捕捉 */
  useCapture: boolean;
  /** 提交操作前确认 */
  toolTipBeforeComfirm: boolean;
  /** 交点添加点设备 */
  addPointDeviceAtCrossPoint: boolean;
  /** 当前的绘制工具 */
  activeDrawType: 'polygon' | 'rectangle' | 'circle' | '';
  hasSelectedFeature: boolean;
  isPipeLine: boolean;
  mergeable: boolean;
  pointEdiable: boolean;
  /** 已复制的属性 */
  copiedAttributes: any | null;
  /** 复制属性的来源图层 */
  copiedAttributesLayerId: string | number | null;
  /** 是否处于复制属性模式 */
  copyAttributesMode: boolean;
  /** 是否正在添加新要素 */
  isAddingNewFeature: boolean;
  /** 当前正在添加的要素信息 */
  currentAddingFeatureInfo?: {
    layerId: string | number;
    layerName: string;
  };
}>({
  isMounted: false,
  curPage: 'index',
  timer: null,
  editing: false,
  useCapture: true,
  toolTipBeforeComfirm: true,
  addPointDeviceAtCrossPoint: false,
  activeDrawType: '',
  hasSelectedFeature: false,
  isPipeLine: false,
  mergeable: false,
  pointEdiable: false,
  copiedAttributes: null,
  copiedAttributesLayerId: null,
  copyAttributesMode: false,
  isAddingNewFeature: false,
  currentAddingFeatureInfo: undefined
});

const staticState: {
  view?: __esri.MapView;
  sketch?: __esri.SketchViewModel;
  graphic?: __esri.Graphic;
  submitGraphic?: __esri.Graphic;
  /** 正在更新的多个要素 */
  graphics: __esri.Graphic[];
  clonedGraphics: __esri.Graphic[];
  clonedGraphic?: __esri.Graphic;
  graphicsLayer?: __esri.GraphicsLayer;
  /** 复制属性模式的点击事件处理器 */
  copyAttributesClickHandler?: any;
  /** 属性查询模式的点击事件处理器 */
  attributeQueryClickHandler?: any;
  /** 当前正在添加的要素的图形对象 */
  currentAddingFeatureGraphic?: __esri.Graphic;
} = {
  graphics: [],
  clonedGraphics: [],
  copyAttributesClickHandler: null,
  attributeQueryClickHandler: null,
  currentAddingFeatureGraphic: undefined
};

const topBarBtns: IButton[] = [
  {
    perm: true,
    text: '框选',
    isTextBtn: true,
    disabled: () => !state.editing,
    type: 'default',
    click: () => rectSelect.start(staticState.view),
    iconifyIcon: 'gis:rectangle-pt'
  },
  {
    perm: true,
    text: '编辑节点',
    isTextBtn: true,
    disabled: (): boolean => !state.pointEdiable,
    type: 'default',
    click: () => handleEditLinePoint(),
    iconifyIcon: 'gis:difference'
  },
  {
    perm: true,
    text: '合并',
    isTextBtn: true,
    disabled: (): boolean => !state.mergeable,
    type: 'default',
    click: () => handleMerge(),
    iconifyIcon: 'pajamas:merge-request'
  },
  {
    perm: true,
    text: '拆分',
    isTextBtn: true,
    disabled: (): boolean => !(state.hasSelectedFeature && state.isPipeLine),
    type: 'default',
    click: () => handleSplit(),
    iconifyIcon: 'pajamas:merge-request-close'
  },
  {
    perm: true,
    text: '属性',
    isTextBtn: true,
    // disabled: (): boolean => !state.hasSelectedFeature,
    type: 'default',
    click: () => handleViewAttrs(),
    iconifyIcon: 'material-symbols:info'
  },
  {
    perm: false,
    text: '废弃',
    type: 'warning',
    isTextBtn: true,
    disabled: (): boolean => !state.hasSelectedFeature,
    click: () => handleDisuse(),
    iconifyIcon: 'quill:discard'
  },
  {
    perm: true,
    text: '删除',
    type: 'danger',
    isTextBtn: true,
    disabled: (): boolean => !state.hasSelectedFeature,
    click: () => deleteFeature(),
    iconifyIcon: 'ep:delete'
  }
];
const startEdit = () => {
  state.editing = true;
  state.hasSelectedFeature = false;
  state.isPipeLine = false;
  state.mergeable = false;
  // 保留复制的属性，不重置
  // state.copiedAttributes = null;
  // state.copiedAttributesLayerId = null;
  staticState.graphic = undefined;
  staticState.graphics = [];
  staticState.clonedGraphic = undefined;
  staticState.clonedGraphics = [];
  staticState.graphicsLayer?.removeAll();
  handleDrawEditableArea();
};
const endEdit = () => {
  state.editing = false;
  staticState.graphicsLayer?.removeAll();
  staticState.graphic = undefined;
  staticState.graphics = [];
  editArea.clear();
  state.hasSelectedFeature = false;
  state.isPipeLine = false;
  state.mergeable = false;
  splitPipe.destroy();
  editArea.destroy();
  rectSelect.destroy();
  refEditorPop.value?.hide();
};
const handleDrawEditableArea = (type?: any) => {
  if (!state.editing) return;
  SLMessage.info('请绘制编辑区域');
  editArea.startDraw(staticState.view, type);
  // 关闭编辑器下拉框
  refEditorPop.value?.hide();
};
const handleSplit = () => {
  if (!(state.hasSelectedFeature && state.isPipeLine)) return;
  const pipeLayerId = refForm.value?.dataForm.layerid?.[0];
  const fittingLayerInfo = layerSelector.layerInfos.find(
    (item) => item.layername === '节点'
  );
  splitPipe.init(staticState.view, pipeLayerId, fittingLayerInfo, (point) => {
    splitPipe.setSplitPoint(staticState.graphic, point);
  });
};
/**
 * 处理属性按钮点击事件
 * 显示要素属性查询面板，允许用户查询和编辑要素属性
 */
const handleViewAttrs = async () => {
  // 获取当前图层ID
  const layerId = refForm.value?.dataForm.layerid?.[0];
  if (!layerId) {
    SLMessage.warning('请先选择图层');
    return;
  }

  // 获取当前图层名称
  const layerName = layerSelector.curLayerInfo?.layername || '要素';
  console.log('打开属性查询面板:', layerName);

  try {
    // 关闭其他面板
    refPanelMerge.value?.Close();

    // 设置面板标题
    if (refPanel.value) {
      refPanel.value.title = `${layerName}属性查询`;
    }

    // 如果已经有选中的要素，则显示其属性
    if (staticState.graphic) {
      console.log('显示选中要素属性:', staticState.graphic.attributes?.OBJECTID);
      // 刷新编辑表单
      await refreshEditForm();
    } else {
      // 如果没有选中的要素，则显示空表单，等待用户点击地图选择要素
      console.log('没有选中的要素，显示空表单');
      // 清空表单
      if (refFormEdit.value) {
        refFormEdit.value.dataForm = {};
      }

      // 设置提示信息
      if (editForm.FormConfig && editForm.FormConfig.group && editForm.FormConfig.group.length > 0) {
        editForm.FormConfig.group[0].fields = [{
          type: 'input',
          label: '提示',
          field: 'hint',
          placeholder: '请在地图上点击要素以查看和编辑其属性',
          disabled: true
        }];
      }
    }

    // 打开属性面板
    refPanel.value?.Open();

    // 更新面板底部按钮
    // 这里我们需要确保面板底部有"保存"按钮，而不是"添加要素"按钮
    // 由于面板组件是复用的，我们需要根据当前操作类型动态调整按钮
    state.isAddingNewFeature = false;

    // 如果没有选中的要素，则启用点击地图选择要素的功能
    if (!staticState.graphic) {
      // 启用点击地图选择要素的功能
      enableSelectFeatureByClick();
    }
  } catch (error) {
    console.error('显示属性面板时出错:', error);
    SLMessage.error('无法显示属性面板');
  }
};

/**
 * 启用点击地图选择要素的功能
 */
const enableSelectFeatureByClick = () => {
  // 如果视图未初始化，则返回
  if (!staticState.view) {
    console.error('视图未初始化，无法启用点击选择功能');
    return;
  }

  console.log('启用点击地图选择要素的功能');

  // 设置鼠标指针为十字形
  staticState.view.container.style.cursor = 'crosshair';

  // 添加点击事件监听器
  const clickHandler = staticState.view.on('click', async (event) => {
    console.log('地图点击事件:', event);

    // 获取当前图层ID
    const layerId = refForm.value?.dataForm.layerid?.[0];
    if (!layerId) {
      SLMessage.warning('请先选择图层');
      return;
    }

    // 获取当前图层名称
    const layerName = layerSelector.curLayerInfo?.layername || '要素';

    try {
      // 执行空间查询，获取点击位置的要素
      let feature: __esri.Graphic | null = null;

      // 检查是否使用 GeoServer 模式
      if ((window as any).GIS_SERVER_SWITCH) {
        // GeoServer 模式下，使用 WFS 查询
        console.log('使用 GeoServer 模式查询要素');

        // 使用GeoServer的WMS服务查询
        // 确保图层名称格式正确
        const layerNameStr = typeof layerName === 'string' ? layerName : `${layerName}`;
        console.log('使用图层名称进行查询:', layerNameStr);

        // 执行点击查询
        const response = await excuteIdentifyByGeoserver(
          staticState.view,
          '/geoserver/guazhou/wms', // WMS服务URL
          layerNameStr, // 图层名称
          event // 点击事件，包含屏幕坐标
        );

        console.log('GeoServer 查询响应:', response);

        // 检查是否有结果
        if (response && response.data && response.data.features && response.data.features.length > 0) {
          // 获取第一个要素
          const feature = response.data.features[0];
          console.log('找到要素:', feature);

          // 详细检查几何信息
          console.log('要素几何信息:', feature.geometry);

          // 检查几何信息是否存在
          if (!feature.geometry) {
            console.error('要素几何信息为空');
            SLMessage.warning('要素几何信息为空，无法编辑');
            return;
          }

          // 将GeoJSON几何信息转换为ArcGIS几何对象
          let arcgisGeometry: any;
          try {
            // 根据几何类型创建不同的几何对象
            if (feature.geometry.type === 'Point') {
              // 创建点几何
              arcgisGeometry = {
                type: 'point',
                x: feature.geometry.coordinates[0],
                y: feature.geometry.coordinates[1],
                spatialReference: staticState.view?.spatialReference
              };
            } else if (feature.geometry.type === 'LineString') {
              // 创建线几何
              arcgisGeometry = {
                type: 'polyline',
                paths: [feature.geometry.coordinates],
                spatialReference: staticState.view?.spatialReference
              };
            } else if (feature.geometry.type === 'MultiLineString') {
              // 创建多线几何
              arcgisGeometry = {
                type: 'polyline',
                paths: feature.geometry.coordinates,
                spatialReference: staticState.view?.spatialReference
              };
            } else if (feature.geometry.type === 'Polygon') {
              // 创建面几何
              arcgisGeometry = {
                type: 'polygon',
                rings: feature.geometry.coordinates,
                spatialReference: staticState.view?.spatialReference
              };
            } else {
              console.error('不支持的几何类型:', feature.geometry.type);
              SLMessage.warning(`不支持的几何类型: ${feature.geometry.type}`);
              return;
            }

            console.log('转换后的ArcGIS几何对象:', arcgisGeometry);
          } catch (error) {
            console.error('转换几何信息时出错:', error);
            SLMessage.error('转换几何信息失败');
            return;
          }

          // 获取属性信息
          const featureProperties = feature.properties || {};

          // 创建图形对象
          const graphic = createGraphic({
            geometry: arcgisGeometry,
            attributes: featureProperties
          });

          // 检查创建的图形对象
          console.log('创建的图形对象:', graphic);
          if (!graphic.geometry) {
            console.error('创建的图形对象几何信息为空');
            SLMessage.error('创建图形对象失败');
            return;
          }

          // 设置当前选中的要素
          staticState.graphic = graphic;
          staticState.graphics = [graphic];

          // 刷新编辑表单
          await refreshEditForm();
          debugger
          // 高亮显示选中的要素
          highlightSelectedFeature(graphic);
        } else {
          console.log('未找到要素');
          SLMessage.warning('未找到要素，请重新点击');
        }
      }
    } catch (error) {
      console.error('查询要素时出错:', error);
      SLMessage.error('查询要素失败，请重试');
    }
  });

  // 将点击事件处理器存储在 staticState 中，以便后续移除
  staticState.attributeQueryClickHandler = clickHandler;

  // 当面板关闭时，移除点击事件监听器
  if (refPanel.value) {
    refPanel.value.onClose = () => {
      disableSelectFeatureByClick();
    };
  }
};

/**
 * 禁用点击地图选择要素的功能
 */
const disableSelectFeatureByClick = () => {
  console.log('禁用点击地图选择要素的功能');

  // 恢复鼠标指针
  if (staticState.view) {
    staticState.view.container.style.cursor = 'default';
  }

  // 移除点击事件监听器
  if (staticState.attributeQueryClickHandler) {
    staticState.attributeQueryClickHandler.remove();
    staticState.attributeQueryClickHandler = null;
  }
};

/**
 * 高亮显示选中的要素
 */
const highlightSelectedFeature = (graphic: __esri.Graphic) => {
  // 确保图形图层已初始化
  if (!staticState.graphicsLayer && staticState.view) {
    staticState.graphicsLayer = getGraphicLayer(staticState.view, {
      id: 'pipe-editing',
      title: '编辑'
    });
  }

  // 如果图形图层未初始化，则返回
  if (!staticState.graphicsLayer) {
    console.error('图形图层未初始化，无法高亮显示要素');
    return;
  }

  // 清空图形图层
  staticState.graphicsLayer.removeAll();

  // 创建高亮样式
  // 使用 any 类型绕过类型检查
  let highlightSymbol: any = null;

  // 检查几何对象是否存在
  if (!graphic.geometry) {
    console.error('高亮显示失败：几何对象为空');
    return;
  }

  console.log('高亮显示几何类型:', graphic.geometry.type);

  // 根据几何类型创建不同的高亮样式
  if (graphic.geometry.type === 'point') {
    highlightSymbol = {
      type: 'simple-marker',
      color: [255, 0, 0, 0.5],
      size: 12,
      outline: {
        color: [255, 0, 0, 1],
        width: 2
      }
    };
  } else if (graphic.geometry.type === 'polyline') {
    highlightSymbol = {
      type: 'simple-line',
      color: [255, 0, 0, 1],
      width: 4
    };
  } else if (graphic.geometry.type === 'polygon') {
    highlightSymbol = {
      type: 'simple-fill',
      color: [255, 0, 0, 0.3],
      outline: {
        color: [255, 0, 0, 1],
        width: 2
      }
    };
  } else {
    console.error('不支持的几何类型:', graphic.geometry.type);
    return;
  }

  // 创建高亮图形
  const highlightGraphic = createGraphic({
    geometry: graphic.geometry,
    symbol: highlightSymbol
  });

  // 添加高亮图形到图形图层
  staticState.graphicsLayer.add(highlightGraphic);
};
const refreshEditForm = async () => {
  editForm.resetTreeData(
    layerSelector.curLayerInfo?.layername,
    staticState.graphics
  );
  await nextTick();
  editForm
    .resetForm(refForm.value?.dataForm.layerid?.[0], staticState.graphic)
    .then(() => {
      // 移除 disableUpdate 设置
      // disableUpdate.value = false;
    })
    .catch((e: any) => {
      console.log(e);

      // 移除 disableUpdate 设置
      // disableUpdate.value = true;
    });
};

/**
 * 处理属性编辑提交
 * 保存要素的属性修改
 */
const handleEditAttributesSubmit = async () => {
  // 检查是否有选中的要素
  if (!staticState.graphic) {
    SLMessage.warning('没有选中要素');
    return;
  }

  // 获取当前图层ID
  const layerId = refForm.value?.dataForm.layerid?.[0];
  if (!layerId) {
    SLMessage.warning('请先选择图层');
    return;
  }

  // 获取当前图层名称
  const layerName = layerSelector.curLayerInfo?.layername || '要素';
  console.log('保存要素属性:', layerName, staticState.graphic.attributes?.OBJECTID);

  // 从表单中获取属性值
  let attributes = {};
  if (refFormEdit.value) {
    try {
      // 直接使用表单数据作为属性
      attributes = JSON.parse(JSON.stringify(refFormEdit.value.dataForm || {}));
    } catch (error) {
      console.error('处理表单数据时出错:', error);
      // 如果出错，使用空对象
      attributes = {};
    }
  }

  // 过滤掉未填写的字段，只保留有值的字段
  const filteredAttributes = {};
  Object.keys(attributes).forEach(key => {
    const value = attributes[key];
    // 只保留非空值（不为 undefined、null 或空字符串）
    if (value !== undefined && value !== null && value !== '') {
      filteredAttributes[key] = value;
    }
  });

  // 显示确认对话框
  SLConfirm('此操作将更新要素的属性，是否继续？', '提示信息')
    .then(async () => {
      try {
        // 更新要素属性
        // 创建一个新的图形对象，包含原始几何信息和更新后的属性
        // 使用类型断言确保TypeScript不会报错
        const updatedGraphic = createGraphic({
          geometry: (staticState.graphic as __esri.Graphic).geometry,
          attributes: {
            ...(staticState.graphic as __esri.Graphic).attributes,
            ...filteredAttributes
          }
        });

        // 检查是否使用 GeoServer 模式
        if ((window as any).GIS_SERVER_SWITCH) {
          console.log('使用 GeoServer WFS-T 模式更新属性');

          // 获取图层名称
          const layerName = typeof layerId === 'string' ? layerId : `${layerId}`;
          console.log('图层名称:', layerName);

          // 执行 WFS-T 更新操作
          const response = await updateFeatureAttributes(
            layerName,
            [updatedGraphic],
            filteredAttributes,
            false // 不更新几何信息，只更新属性
          );

          console.log('GeoServer更新响应:', response);

          // 解析XML响应以检查是否成功
          const parser = new DOMParser();
          const xmlDoc = parser.parseFromString(response.data, "text/xml");

          // 获取更新的要素数量
          const totalUpdatedElements = xmlDoc.getElementsByTagName('wfs:totalUpdated');
          const totalUpdated = totalUpdatedElements.length > 0 ?
            parseInt(totalUpdatedElements[0].textContent || '0') : 0;

          console.log('更新的要素数量:', totalUpdated);

          // 判断是否成功
          if (totalUpdated > 0) {
            // 更新本地图形对象
            if (staticState.graphic) {
              staticState.graphic.attributes = {
                ...staticState.graphic.attributes,
                ...filteredAttributes
              };

              // 记录操作日志
              PostGisOperateLog({
                optionName: EGigLogFunc.GWBIANJI,
                type: EGisLogApp.BASICGIS,
                content: `${EGisLogOperateType.UPDATE}OBJECTID为${staticState.graphic.attributes.OBJECTID}的${layerName}的属性信息`,
                optionType: EGisLogOperateType.UPDATE
              }).catch(() => {
                console.log('生成gis操作日志失败');
              });
            }

            // 刷新图层和编辑区域
            refreshPipeLayer(staticState.view);
            refreshEditAbleFeatures();

            // 提示用户更新成功
            SLMessage.success(`更新${layerName}属性成功`);

            // 关闭属性面板
            refPanel.value?.Close();
          } else {
            console.error('WFS-T 更新要素失败:', response);
            SLMessage.error('更新属性失败');
          }
        } else {
          // ArcGIS 模式
          console.log('使用 ArcGIS 模式更新属性');

          // 确保 layerId 是数字
          const numericLayerId = typeof layerId === 'string' ? parseInt(layerId) || 0 : layerId;

          // 使用 submitEdit 函数
          editForm.submitEdit(
            numericLayerId,
            { updateFeatures: [updatedGraphic] },
            (result) => {
              // 更新本地图形对象
              console.log('ArcGIS更新响应:', result);
              if (staticState.graphic) {
                staticState.graphic.attributes = {
                  ...staticState.graphic.attributes,
                  ...filteredAttributes
                };

                // 记录操作日志
                PostGisOperateLog({
                  optionName: EGigLogFunc.GWBIANJI,
                  type: EGisLogApp.BASICGIS,
                  content: `${EGisLogOperateType.UPDATE}OBJECTID为${staticState.graphic.attributes.OBJECTID}的${layerName}的属性信息`,
                  optionType: EGisLogOperateType.UPDATE
                }).catch(() => {
                  console.log('生成gis操作日志失败');
                });
              }

              // 刷新图层和编辑区域
              refreshPipeLayer(staticState.view);
              refreshEditAbleFeatures();

              // 提示用户更新成功
              SLMessage.success(`更新${layerName}属性成功`);

              // 关闭属性面板
              refPanel.value?.Close();
            },
            () => {
              // 失败回调
              SLMessage.error('更新属性失败');
            }
          );
        }
      } catch (error) {
        console.error('更新属性时出错:', error);
        SLMessage.error('更新属性失败，请重试');
      }
    })
    .catch(() => {
      // 用户取消操作
      console.log('用户取消了更新属性操作');
    });
};
const refreshEditAbleFeatures = () => {
  editArea.queryFeatures(
    refForm.value?.dataForm.layerid?.[0],
    staticState.graphicsLayer
  );
};
/**
 * 删除选中的要素
 * 支持 ArcGIS 和 GeoServer 两种模式
 */
const deleteFeature = () => {
  // 获取当前图层名称
  const curLayerName = layerSelector.curLayerInfo?.layername;
  // 获取当前图层ID
  const layerId = refForm.value?.dataForm?.layerid?.[0];

  // 获取要删除的要素
  const featuresToDelete = staticState.sketch?.updateGraphics;

  if (!featuresToDelete || featuresToDelete.length === 0) {
    SLMessage.warning('没有选中要删除的要素');
    return;
  }

  // 显示确认对话框
  SLConfirm('此操作将删除选中的要素，是否继续？', '提示信息')
    .then(async () => {
      try {
        // 检查是否使用 GeoServer 模式
        if ((window as any).GIS_SERVER_SWITCH) {
          console.log('使用 GeoServer WFS-T 模式删除要素');

          // 获取图层名称
          const layerName = typeof layerId === 'string' ? layerId : `${layerId}`;
          console.log('图层名称:', layerName);

          // 导入 WFS-T 工具函数
          const { deleteFeature } = await import('@/utils/geoserver/wfsTransactionUtils');
          // 收集要删除的要素的 ID
          const featureIds: string[] = [];

          // 检查 featuresToDelete 的结构
          const features = Array.isArray(featuresToDelete) ? featuresToDelete :
                          (featuresToDelete as any).items ||
                          (featuresToDelete as any).toArray?.() ||
                          [featuresToDelete];

          if (!features || features.length === 0) {
            SLMessage.error('没有要删除的要素');
            return;
          }

          // 遍历要素，尝试多种方式获取要素 ID
          features.forEach((feature: any) => {
            const featureId = typeof feature.id === 'string' ? feature.id : `${feature.id}`;
            featureIds.push(featureId);
          });
          console.log('要删除的要素 ID:', featureIds);

          // 执行 WFS-T 删除操作
          const response = await deleteFeature(layerName, featureIds);
          console.log('GeoServer 删除响应:', response);

          // 解析 XML 响应以检查是否成功
          const parser = new DOMParser();
          const xmlDoc = parser.parseFromString(response.data, "text/xml");

          // 获取删除的要素数量
          const totalDeletedElements = xmlDoc.getElementsByTagName('wfs:totalDeleted');
          const totalDeleted = totalDeletedElements.length > 0 ?
            parseInt(totalDeletedElements[0].textContent || '0') : 0;

          console.log('删除的要素数量:', totalDeleted);

          // 判断是否成功
          if (totalDeleted > 0) {
            // 记录操作日志
            PostGisOperateLog({
              optionName: EGigLogFunc.GWBIANJI,
              type: EGisLogApp.BASICGIS,
              content: `${EGisLogOperateType.DELETE}OBJECTID为${featuresToDelete
                .map(item => item.attributes.OBJECTID)
                .join('、')}的${curLayerName}`,
              optionType: EGisLogOperateType.DELETE
            }).catch(() => {
              console.log('生成gis操作日志失败');
            });

            // 刷新图层和编辑区域
            refreshPipeLayer(staticState.view);
            refreshEditAbleFeatures();

            // 重置状态
            staticState.graphic = undefined;
            staticState.graphics = [];
            editForm.resetForm();

            // 提示用户删除成功
            SLMessage.success(`删除${curLayerName}成功`);
          } else {
            console.error('WFS-T 删除要素失败:', response);
            SLMessage.error('删除要素失败');
          }
        } else {
          // ArcGIS 模式
          console.log('使用 ArcGIS 模式删除要素');

          editForm.submitEdit(
            layerId,
            { deleteFeatures: featuresToDelete },
            (res) => {
              // 记录操作日志
              PostGisOperateLog({
                optionName: EGigLogFunc.GWBIANJI,
                type: EGisLogApp.BASICGIS,
                content: `${EGisLogOperateType.DELETE}OBJECTID为${res.deleteFeatureResults
                  .map((item) => item.objectId)
                  .join('、')}的${curLayerName}`,
                optionType: EGisLogOperateType.DELETE
              }).catch(() => {
                console.log('生成gis操作日志失败');
              });

              // 刷新图层和编辑区域
              refreshPipeLayer(staticState.view);
              refreshEditAbleFeatures();

              // 重置状态
              staticState.graphic = undefined;
              staticState.graphics = [];
              editForm.resetForm();

              // 提示用户删除成功
              SLMessage.success(`删除${curLayerName}成功`);
            }
          );
        }
      } catch (error) {
        console.error('删除要素时出错:', error);
        SLMessage.error('删除要素失败，请重试');
      }
    })
    .catch(() => {
      // 用户取消操作
      console.log('用户取消了删除操作');
    });
};

const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (!result.graphics?.length) {
    console.warn('没有图形数据');
    return;
  }

  const graphic: __esri.Graphic = result.graphics[0];
  console.log('图形类型:', graphic.geometry.type, '属性:', graphic.attributes);
  state.pointEdiable = false;
  staticState.graphic = graphic;
  staticState.graphics = result.graphics;
  state.hasSelectedFeature = false;
  state.isPipeLine = graphic.geometry.type === 'polyline';

  // 处理新创建的要素
  if (result.state === 'complete' && !graphic.attributes) {
    // 确保已选择图层
    const layerId = refForm.value?.dataForm.layerid?.[0];
    if (!layerId) {
      SLMessage.error('请先选择图层');
      return;
    }

    // 获取当前图层名称
    const layerName = typeof layerId === 'string' ? layerId : '新要素';

    // 获取图层字段并打开属性编辑对话框
    getLayerFieldsAndOpenAttributeEditor(layerId, layerName, graphic);

    return;
  }

  /**
   * 获取图层字段并打开属性编辑对话框
   * @param layerId 图层ID
   * @param layerName 图层名称
   * @param graphic 绘制的图形
   */
  async function getLayerFieldsAndOpenAttributeEditor(layerId: string | number, layerName: string, graphic: __esri.Graphic) {
    try {
      console.log('获取图层字段信息:', layerId);

      // 创建默认属性对象
      let defaultAttributes: any = {};

      // 根据图层获取字段定义
      let fields: any[] = [];

      if ((window as any).GIS_SERVER_SWITCH) {
        // GeoServer模式
        try {
          // 导入GeoServer相关工具函数
          const { GetFieldConfig: GetFieldConfigByGeoserver } = await import('@/utils/geoserver/wfsUtils');

          // 获取图层字段信息
          const response = await GetFieldConfigByGeoserver(layerId);
          console.log('GeoServer字段信息:', response);

          if (response && response.data && response.data.featureTypes &&
              response.data.featureTypes[0]) {

            // 获取图层类型信息
            if (response.data.featureTypes[0].typeName) {
              // 存储图层名称
              const typeName = response.data.featureTypes[0].typeName;
              console.log('图层类型名称:', typeName);

              // 如果有几何类型信息，也存储下来
              if (response.data.featureTypes[0].defaultGeometry) {
                const geometryType = response.data.featureTypes[0].defaultGeometry;
                console.log('图层几何类型:', geometryType);

                // 查找图层信息并更新
                const layerInfoIndex = layerSelector.layerInfos.findIndex((layer: any) =>
                  layer.layerid === layerId || layer.layername === layerName
                );

                if (layerInfoIndex >= 0) {
                  // 更新现有图层信息
                  layerSelector.layerInfos[layerInfoIndex].geometrytype = geometryType;
                  console.log('更新图层信息的几何类型:', geometryType);
                } else {
                  // 添加新的图层信息
                  console.log('未找到图层信息，无法更新几何类型');
                }
              }
            }

            // 获取字段信息
            if (response.data.featureTypes[0].properties) {
              fields = response.data.featureTypes[0].properties;

              // 过滤掉几何字段
              fields = fields.filter(field => field.name !== 'geom' && field.name !== 'geometry');

              console.log('过滤后的字段:', fields);
            }
          }
        } catch (error) {
          console.error('获取GeoServer字段信息失败:', error);
        }
      } else {
        // ArcGIS模式
        try {
          // 导入ArcGIS相关工具函数
          const { GetFieldConfig } = await import('@/api/mapservice/fieldconfig');

          // 获取图层字段信息
          const response = await GetFieldConfig(layerName);
          console.log('ArcGIS字段信息:', response);

          if (response && response.data && response.data.result) {
            // 获取图层类型信息
            if (response.data.result.geometryType) {
              const geometryType = response.data.result.geometryType;
              console.log('ArcGIS图层几何类型:', geometryType);

              // 查找图层信息并更新
              const layerInfoIndex = layerSelector.layerInfos.findIndex((layer: any) =>
                layer.layerid === layerId || layer.layername === layerName
              );

              if (layerInfoIndex >= 0) {
                // 更新现有图层信息
                layerSelector.layerInfos[layerInfoIndex].geometrytype = geometryType;
                console.log('更新图层信息的几何类型:', geometryType);
              } else {
                // 添加新的图层信息
                console.log('未找到图层信息，无法更新几何类型');
              }
            }

            // 获取字段信息
            if (response.data.result.rows) {
              fields = response.data.result.rows;

              // 过滤掉几何字段和系统字段
              fields = fields.filter(field =>
                field.name !== 'SHAPE' &&
                field.name !== 'SHAPE.STLength()' &&
                field.name !== 'SHAPE.STArea()' &&
                field.name !== 'OBJECTID'
              );

              console.log('过滤后的字段:', fields);
            }
          }
        } catch (error) {
          console.error('获取ArcGIS字段信息失败:', error);
        }
      }

      // 如果没有获取到字段信息，使用默认字段
      if (!fields || fields.length === 0) {
        console.warn('未获取到字段信息，使用默认字段');
      }

      // 如果有复制的属性，应用到默认属性中
      if (state.copiedAttributes && state.copiedAttributesLayerId === layerId) {
        defaultAttributes = {
          ...defaultAttributes,
          ...state.copiedAttributes
        };
      }

      // 为图形设置默认属性
      graphic.attributes = defaultAttributes;

      // 设置状态为正在添加新要素
      state.isAddingNewFeature = true;
      state.currentAddingFeatureInfo = {
        layerId,
        layerName
      };
      // 将图形对象存储在非响应式对象中
      staticState.currentAddingFeatureGraphic = graphic;

      // 打开属性编辑面板
      openAttributeEditor(layerId, layerName, graphic, fields);

    } catch (error) {
      console.error('获取字段定义失败:', error);

      // 创建空的默认属性对象，不包含 OBJECTID 字段
      const defaultAttributes: any = {};

      graphic.attributes = defaultAttributes;

      // 设置状态为正在添加新要素
      state.isAddingNewFeature = true;
      state.currentAddingFeatureInfo = {
        layerId,
        layerName
      };
      // 将图形对象存储在非响应式对象中
      staticState.currentAddingFeatureGraphic = graphic;

      // 打开属性编辑面板，使用空字段列表
      openAttributeEditor(layerId, layerName, graphic, []);
    }
  }

  /**
   * 打开属性编辑面板
   * @param graphic 绘制的图形
   * @param fields 字段信息
   */
  function openAttributeEditor(_: string | number, __: string, graphic: __esri.Graphic, fields: any[]) {
    console.log('打开属性编辑面板');

    // 设置当前图形
    staticState.graphic = graphic as any; // 使用类型断言解决类型错误

    // 重置编辑表单
    if (refFormEdit.value) {
      // 清空表单数据
      refFormEdit.value.dataForm = { ...graphic.attributes };

      // 更新表单字段
      if (editForm.FormConfig && editForm.FormConfig.group && editForm.FormConfig.group.length > 0) {
        // 创建表单字段
        const formFields = fields
          // 过滤掉系统字段，如 OBJECTID 和 SID
          .filter(field =>
            field.name !== 'OBJECTID' &&
            field.name !== 'SID' &&
            field.name !== 'objectid' &&
            field.name !== 'sid'
          )
          .map(field => {
            // 根据字段类型创建不同的表单控件
            let fieldType: 'input' | 'date' | 'number' | 'switch' = 'input';
            if (field.type === 'date' || field.type === 'datetime') {
              fieldType = 'date';
            } else if (field.type === 'number' || field.type === 'integer' || field.type === 'double') {
              fieldType = 'number';
            } else if (field.type === 'boolean') {
              fieldType = 'switch';
            }

            return {
              type: fieldType,
              label: field.alias || field.name,
              field: field.name,
              placeholder: `请输入${field.alias || field.name}`
            };
          }) as any;

        // 更新表单配置
        editForm.FormConfig.group[0].fields = formFields;
      }
    }

    // 打开属性面板
    refPanel.value?.Open();
  }



  if (result.state === 'start') {
    staticState.clonedGraphic = createGraphic({
      geometry: graphic.geometry,
      symbol: graphic.symbol,
      attributes: graphic.attributes
    });
    state.hasSelectedFeature = true;
    if (refPanel.value?.visible === true) {
      editForm.resetTreeData(
        layerSelector.curLayerInfo?.layername,
        staticState.graphics
      );
      editForm.resetForm(refForm.value?.dataForm.layerid?.[0], graphic);
    }
  }
  state.mergeable = false;
  if (result.graphics.length > 1) {
    // 合并需要满足的条件：
    // 1、 不是管线
    // 2、 有多个
    state.mergeable = !state.isPipeLine;
    state.pointEdiable = false;
    let isEqual = true;
    result.graphics.map((item) => {
      const oldItem = staticState.clonedGraphics.find(
        (item) => item.attributes.OBJECTID === item.attributes.OBJECT
      );

      isEqual = !!oldItem && Equals(item.geometry, oldItem.geometry);
    });
    if (isEqual) return;
  } else {
    state.pointEdiable = state.isPipeLine;
  }
  const isEqual =
    staticState.clonedGraphic?.geometry &&
    Equals(staticState.clonedGraphic?.geometry, graphic.geometry);
  if (isEqual) return;
  if (result.state !== 'complete' || result.aborted) return;

  const curLayerName = layerSelector.curLayerInfo?.layername;
  const layerId = refForm.value?.dataForm?.layerid?.[0];

  if (!curLayerName || !layerId) {
    console.error('无法确定图层名称或ID');
    return;
  }

  const oringinalGeo: any = staticState.clonedGraphic?.geometry;
  const newGeo: any = result.graphics?.[0].geometry;

  // 显示确认对话框
  SLConfirm('此操作将更新要素的空间位置，是否继续？', '提示信息')
    .then(async () => {
      try {
        // 检查是否使用 GeoServer 模式
        if ((window as any).GIS_SERVER_SWITCH) {
          console.log('使用 GeoServer 模式更新要素位置');

          // 创建更新属性对象 - 只更新几何信息，不更新属性
          const updateAttributes = {};

          // 使用统一的updateFeatureAttributes函数，设置updateGeometry=true表示同时更新几何信息
          // 直接使用staticState.graphics，不再使用result.graphics
          const response = await updateFeatureAttributes(
            curLayerName,
            staticState.graphics,
            updateAttributes,
            true // 更新几何信息
          );

          console.log('更新要素位置响应:', response);

          // 解析XML响应以检查是否成功
          const parser = new DOMParser();
          const xmlDoc = parser.parseFromString(response.data, "text/xml");

          // 获取更新的要素数量
          const totalUpdatedElements = xmlDoc.getElementsByTagName('wfs:totalUpdated');
          const totalUpdated = totalUpdatedElements.length > 0 ?
            parseInt(totalUpdatedElements[0].textContent || '0') : 0;

          console.log('更新的要素数量:', totalUpdated);

          if (totalUpdated > 0) {
            // 记录操作日志
            PostGisOperateLog({
              optionName: EGigLogFunc.GWBIANJI,
              type: EGisLogApp.BASICGIS,
              content: `${EGisLogOperateType.UPDATE}OBJECTID为${
                result.graphics?.[0].attributes?.OBJECTID || '未知'
              }的${curLayerName}的空间位置，更新前位置: ${
                oringinalGeo?.type === 'point'
                  ? JSON.stringify([oringinalGeo?.x, oringinalGeo?.y])
                  : oringinalGeo.type === 'polyline'
                    ? JSON.stringify(oringinalGeo?.paths || [])
                    : ''
              },更新后的位置：${
                newGeo.type === 'point'
                  ? JSON.stringify([newGeo?.x, newGeo?.y])
                  : newGeo.type === 'polyline'
                    ? JSON.stringify(newGeo?.paths || [])
                    : ''
              }`,
              optionType: EGisLogOperateType.UPDATE
            }).catch(() => {
              console.log('生成gis操作日志失败');
            });

            // 刷新图层和编辑区域
            refreshPipeLayer(staticState.view);
            refreshEditAbleFeatures();

            // 显示成功消息
            SLMessage.success(`更新${curLayerName}位置成功`);
          } else {
            console.error('更新要素位置失败:', response);
            SLMessage.error('更新要素位置失败，请重试');
            refreshEditAbleFeatures();
          }
        } else {
          // ArcGIS模式
          console.log('使用 ArcGIS 模式更新要素位置');

          let submitGraphics: __esri.Graphic[] = [];
          // 不需要特定站点的坐标转换
          submitGraphics = result.graphics;

          editForm.submitEdit(
            layerId,
            { updateFeatures: submitGraphics },
            () => {
              PostGisOperateLog({
                optionName: EGigLogFunc.GWBIANJI,
                type: EGisLogApp.BASICGIS,
                content: `${EGisLogOperateType.UPDATE}OBJECTID为${
                  result.graphics?.[0].attributes?.OBJECTID
                }的${curLayerName}的空间位置，更新前位置: ${
                  oringinalGeo?.type === 'point'
                    ? JSON.stringify([oringinalGeo?.x, oringinalGeo?.y])
                    : oringinalGeo.type === 'polyline'
                      ? JSON.stringify(oringinalGeo?.paths || [])
                      : ''
                },更新后的位置：${
                  newGeo.type === 'point'
                    ? JSON.stringify([newGeo?.x, newGeo?.y])
                    : newGeo.type === 'polyline'
                      ? JSON.stringify(newGeo?.paths || [])
                      : ''
                }`,
                optionType: EGisLogOperateType.UPDATE
              }).catch(() => {
                console.log('生成gis操作日志失败');
              });
              refreshPipeLayer(staticState.view);
              refreshEditAbleFeatures();

              // 显示成功消息
              SLMessage.success(`更新${curLayerName}位置成功`);
            },
            () => {
              // 失败回调
              console.error('更新要素位置失败');
              SLMessage.error('更新要素位置失败，请重试');
              refreshEditAbleFeatures();
            }
          );
        }
      } catch (error) {
        console.error('更新要素位置时出错:', error);
        SLMessage.error('更新要素位置失败，请重试');
        refreshEditAbleFeatures();
      }
    })
    .catch(() => {
      // 用户取消操作
      console.log('用户取消了更新要素位置操作');
      refreshEditAbleFeatures();
    });
};

const { initSketch, destroySketch } = useSketch();
const initEditSketch = () => {
  console.log('初始化编辑 sketch...');

  // 确保先销毁旧的 sketch
  destroySketch();

  // 确保 graphicsLayer 已初始化
  if (!staticState.graphicsLayer && staticState.view) {
    console.log('初始化 graphicsLayer...');
    staticState.graphicsLayer = getGraphicLayer(staticState.view, {
      id: 'pipe-editing',
      title: '编辑'
    });
  }

  if (!staticState.view) {
    console.error('视图未初始化，无法创建 sketch');
    return;
  }

  if (!staticState.graphicsLayer) {
    console.error('图形图层未初始化，无法创建 sketch');
    return;
  }

  console.log('创建 sketch...');
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    createCallBack: resolveDrawEnd,
    updateCallBack: resolveDrawEnd,
    delCallBack: () => {
      console.log('删除回调触发');
      refreshEditAbleFeatures();
    },
    snappingOptions: {
      enabled: state.useCapture,
      featureSources: [{ layer: staticState.graphicsLayer }]
    }
  });

  console.log('sketch 初始化完成:', !!staticState.sketch);
};
const onMaploaded = async (view: __esri.MapView) => {
  staticState.view = view;
  staticState.view?.watch('extent', editArea.refresh);

  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-editing',
    title: '编辑'
  });
  initEditSketch();
  setTimeout(() => {
    layerSelector.getLayerInfo(staticState.view);
  }, 1000);
};


const handleMergeSubmit = () => {
  const layerid = refForm.value?.dataForm.layerid?.[0];
  const layername = layerSelector.curLayerInfo?.layername;
  const graphic = staticState.graphic;
  if (!mergePipe.curNode.value) {
    SLMessage.error('请选中要保留的设备');
    return;
  }
  if (layerid === undefined || !graphic) return;
  const deletedFeatures: __esri.Graphic[] =
    staticState.graphics.filter(
      (item) => item.attributes.OBJECTID !== mergePipe.curNode.value?.value
    ) || [];
  let submitFeatures: __esri.Graphic[] = [];
  // 不需要特定站点的坐标转换
  submitFeatures = deletedFeatures;
  editForm.submitEdit(
    refForm.value?.dataForm?.layerid?.[0],
    {
      deleteFeatures: submitFeatures
    },
    () => {
      PostGisOperateLog({
        optionName: EGigLogFunc.GWBIANJI,
        type: EGisLogApp.BASICGIS,
        content: `合并OBJECTID为${staticState.graphics
          .map((item) => item.attributes.OBJECTID)
          .join(
            '、'
          )}的${layername}，合并后OBJECTID为${mergePipe.curNode.value?.value}`,
        optionType: EGisLogOperateType.UPDATE
      }).catch(() => {
        console.log('生成gis操作日志失败');
      });
      refreshPipeLayer(staticState.view);
      refreshEditAbleFeatures();
      refPanelMerge.value?.Close();
      mergePipe.curNode.value = undefined;
      const pipelayer = staticState.view?.map.findLayerById(
        'pipelayer'
      ) as __esri.MapImageLayer;
      pipelayer && pipelayer.refresh();
    }
  );
};
const handleSnapping = () => {
  staticState.sketch &&
    (staticState.sketch.snappingOptions.enabled = !!state.useCapture);
};
const handleEditLinePoint = () => {
  staticState.graphic &&
    staticState.sketch?.update(staticState.graphic, {
      tool: 'reshape'
    });
};

const handleMerge = () => {
  refPanel.value?.Close();
  refreshMergeForm();
};
const refreshMergeForm = async () => {
  if (!state.mergeable) return;
  refPanelMerge.value?.Open();
  await nextTick();
  mergePipe.resetForm(staticState.graphics);
};
const handleDisuse = () => {
  //
};

/**
 * 处理添加要素提交
 * 当用户点击属性面板中的"添加要素"按钮时调用
 */
const handleAddFeatureSubmit = async () => {
  // 检查是否有当前添加的要素信息
  if (!state.currentAddingFeatureInfo || !staticState.currentAddingFeatureGraphic) {
    SLMessage.error('没有要添加的要素信息');
    return;
  }
  // 使用解构并创建副本，避免直接修改响应式对象
  const { layerId, layerName } = state.currentAddingFeatureInfo;
  // 从表单中获取属性值
  let attributes = {};
  if (refFormEdit.value) {
    try {
      // 直接使用表单数据作为属性
      attributes = JSON.parse(JSON.stringify(refFormEdit.value.dataForm || {}));
    } catch (error) {
      console.error('处理表单数据时出错:', error);
      // 如果出错，使用空对象
      attributes = {};
    }
  }

  // 创建一个全新的对象，避免直接访问代理对象的属性
  // 使用一个更简单的方法，直接创建一个新的对象

  // 创建提交数据对象
  const submitData: any = {
    // 不直接引用geometry对象，而是创建一个新的对象
    geometry: null,
    attributes: attributes,
    symbol: null
  };

  // 在提交前，我们将使用原始的图形对象
  // 这样可以避免在这里触发代理对象错误

  console.log('提交新要素:', submitData);

  // 显示确认对话框
  SLConfirm('此操作将添加新要素到空间数据库，是否继续？', '提示信息')
    .then(async () => {
      try {
        // 检查是否使用 GeoServer 模式
        if ((window as any).GIS_SERVER_SWITCH) {
          // GeoServer 模式下，使用 WFS-T 添加要素
          console.log('使用 GeoServer WFS-T 模式添加要素');

          const newGraphic = staticState.currentAddingFeatureGraphic;

          if (!newGraphic) {
            throw new Error('找不到要添加的图形对象');
          }

          // 创建一个全新的属性对象，合并表单数据和系统字段
          // 过滤掉未填写的字段，只保留有值的字段
          const filteredAttributes = {};
          Object.keys(attributes).forEach(key => {
            const value = attributes[key];
            // 只保留非空值（不为 undefined、null 或空字符串）
            if (value !== undefined && value !== null && value !== '') {
              filteredAttributes[key] = value;
            }
          });

          // 使用过滤后的属性创建新的属性对象
          newGraphic.attributes = {
            // 只添加过滤后的表单属性，不添加可能不存在的字段
            ...filteredAttributes
          };

          newGraphic['type'] = layerSelector.curLayerInfo?.type;

          const response = await addFeature(layerName, newGraphic);
          console.log('WFS-T 添加要素响应:', response);

          // 解析XML响应以检查是否成功
          const parser = new DOMParser();
          const xmlDoc = parser.parseFromString(response.data, "text/xml");

          // 获取插入的要素数量
          const totalInsertedElements = xmlDoc.getElementsByTagName('wfs:totalInserted');
          const totalInserted = totalInsertedElements.length > 0 ?
            parseInt(totalInsertedElements[0].textContent || '0') : 0;

          // 获取要素ID
          const featureIdElements = xmlDoc.getElementsByTagName('ogc:FeatureId');
          const featureIdAttr = featureIdElements.length > 0 ?
            featureIdElements[0].getAttribute('fid') : 'none';

          console.log('插入的要素数量:', totalInserted);
          console.log('要素ID:', featureIdAttr);

          // 检查响应是否成功
          if (totalInserted > 0 && featureIdAttr !== 'none') {
            // 提取新添加要素的 ID
            const featureId = featureIdAttr;
            console.log('新添加要素的 ID:', featureId);

            // 从 featureId 中提取 OBJECTID
            let objectId: string | null = null;
            if (featureId) {
              const idParts = featureId.split('.');
              objectId = idParts.length > 1 ? idParts[1] : featureId;
            }

            // 更新要素的 OBJECTID
            if (objectId) {
              newGraphic.attributes.OBJECTID = objectId;
              // 同时添加 fid 属性，以便后续删除操作使用
              newGraphic.attributes.fid = featureId;
            }

            // 记录操作日志
            PostGisOperateLog({
              optionName: EGigLogFunc.GWBIANJI,
              type: EGisLogApp.BASICGIS,
              content: `添加新${layerName}，OBJECTID为${objectId || '未知'}`,
              optionType: EGisLogOperateType.ADD
            }).catch(() => {
              console.log('生成gis操作日志失败');
            });

            // 刷新图层和编辑区域
            refreshPipeLayer(staticState.view);
            refreshEditAbleFeatures();

            // 更新当前图形
            staticState.graphic = newGraphic as any; // 使用类型断言解决类型错误

            // 显示成功消息
            SLMessage.success(`添加${layerName}成功`);

            // 重置添加要素状态
            state.isAddingNewFeature = false;
            state.currentAddingFeatureInfo = undefined;
            staticState.currentAddingFeatureGraphic = undefined;

            // 关闭属性面板
            refPanel.value?.Close();
          } else {
            console.error('WFS-T 添加要素失败:', response);
            SLMessage.error('添加要素失败，请重试');
          }
        } else {
          // 完全避免直接访问代理对象的属性
          // 创建一个空的对象，稍后再填充
          const newGraphic: any = {};

          try {
            // 获取原始图形对象的引用，但不直接访问其属性
            const originalGraphic = staticState.currentAddingFeatureGraphic;

            // 从原始图形对象中获取几何信息
            if (originalGraphic && originalGraphic.geometry) {
              // 创建一个新的几何对象，而不是直接引用原始对象
              if (originalGraphic.geometry.type === 'point') {
                // 使用类型断言来解决 TypeScript 类型错误
                const pointGeometry = originalGraphic.geometry as any;
                newGraphic.geometry = {
                  type: 'point',
                  x: pointGeometry.x,
                  y: pointGeometry.y
                };
              } else if (originalGraphic.geometry.type === 'polyline') {
                // 使用类型断言来解决 TypeScript 类型错误
                const polylineGeometry = originalGraphic.geometry as any;
                newGraphic.geometry = {
                  type: 'polyline',
                  paths: [...polylineGeometry.paths]
                };
              } else {
                // 如果是其他类型的几何，创建一个空的点几何
                newGraphic.geometry = { type: "point", x: 0, y: 0 };
              }
            } else {
              // 如果无法获取几何信息，创建一个空的点几何
              newGraphic.geometry = { type: "point", x: 0, y: 0 };
            }

            // 如果能获取到符号信息，也添加进去
            if (originalGraphic && originalGraphic.symbol) {
              newGraphic.symbol = { ...originalGraphic.symbol };
            }
          } catch (error) {
            console.error('处理几何信息时出错:', error);
            // 如果出错，创建一个空的点几何
            newGraphic.geometry = { type: "point", x: 0, y: 0 };
          }

          // 创建一个全新的属性对象，合并表单数据和系统字段
          // 过滤掉未填写的字段，只保留有值的字段
          const filteredAttributes = {};
          Object.keys(attributes).forEach(key => {
            const value = attributes[key];
            // 只保留非空值（不为 undefined、null 或空字符串）
            if (value !== undefined && value !== null && value !== '') {
              filteredAttributes[key] = value;
            }
          });

          // 使用过滤后的属性创建新的属性对象
          newGraphic.attributes = {
            // 首先添加过滤后的表单属性
            ...filteredAttributes,
            // 然后添加创建日期和创建人
            CREATEDDATE: moment().format(formatterDate),
            CREATEDBY: useUserStore().user?.name || ''
            // 不包含 OBJECTID，让服务器自动生成
          };

          console.log('提交的属性数据:', newGraphic.attributes);

          console.log('使用 ArcGIS 模式添加要素');

          // 使用 ArcGIS 方式提交新创建的要素
          editForm.submitEdit(
            typeof layerId === 'string' ? parseInt(layerId) || 0 : layerId, // 确保 layerId 是数字类型
            { addFeatures: [newGraphic as any] }, // 使用新创建的图形对象，使用类型断言解决类型错误
            (res) => {
              SLMessage.success(`添加${layerName}成功`);
              PostGisOperateLog({
                optionName: EGigLogFunc.GWBIANJI,
                type: EGisLogApp.BASICGIS,
                content: `添加新${layerName}，OBJECTID为${res.addFeatureResults[0]?.objectId}`,
                optionType: EGisLogOperateType.ADD
              }).catch(() => {
                console.log('生成gis操作日志失败');
              });
              refreshPipeLayer(staticState.view);
              refreshEditAbleFeatures();

              // 更新OBJECTID
              if (res.addFeatureResults && res.addFeatureResults[0]) {
                newGraphic.attributes.OBJECTID = res.addFeatureResults[0].objectId;

                // 如果服务器返回了完整的要素，可能包含SID
                if (res.addFeatureResults[0].globalId) {
                  newGraphic.attributes.SID = res.addFeatureResults[0].globalId;
                }
              }
              staticState.graphic = newGraphic as any; // 使用类型断言解决类型错误

              // 重置添加要素状态
              state.isAddingNewFeature = false;
              state.currentAddingFeatureInfo = undefined;
              staticState.currentAddingFeatureGraphic = undefined;

              // 关闭属性面板
              refPanel.value?.Close();
            },
            () => {
              // 失败回调
              console.error('添加要素失败');
              SLMessage.error('添加要素失败，请重试');
            }
          );
        }
      } catch (error) {
        console.error('添加要素时出错:', error);
        SLMessage.error('添加要素失败，请重试');
      }
    })
    .catch(() => {
      // 用户取消操作
      console.log('用户取消了添加要素操作');
      // 关闭属性面板
      refPanel.value?.Close();
    });
};

/**
 * 复制要素的属性
 * 点击按钮后，进入复制属性模式，用户可以点击地图上的要素来复制其属性
 */
const handleCopyAttributes = () => {
  // 获取当前图层ID
  const layerId = refForm.value?.dataForm.layerid?.[0];
  if (!layerId) {
    SLMessage.warning('无法确定当前图层');
    return;
  }

  // 如果已经处于复制属性模式，则退出该模式
  if (state.copyAttributesMode) {
    exitCopyAttributesMode();
    return;
  }

  // 进入复制属性模式
  state.copyAttributesMode = true;

  // 更改鼠标样式为复制样式
  setMapCursor('copy');

  // 显示提示信息
  SLMessage.info('请点击地图上的要素以复制其属性');

  // 添加点击事件处理器
  staticState.copyAttributesClickHandler = staticState.view?.on('click', handleMapClickForCopy);
};

/**
 * 退出复制属性模式
 */
const exitCopyAttributesMode = () => {
  // 恢复默认鼠标样式
  setMapCursor('default');

  // 移除点击事件处理器
  if (staticState.copyAttributesClickHandler) {
    staticState.copyAttributesClickHandler.remove();
    staticState.copyAttributesClickHandler = null;
  }

  // 退出复制属性模式
  state.copyAttributesMode = false;

  SLMessage.info('已退出复制属性模式');
};

/**
 * 处理地图点击事件，用于复制属性
 * @param event 点击事件
 */
const handleMapClickForCopy = async (event: any) => {
  // 获取当前图层ID
  const layerId = refForm.value?.dataForm.layerid?.[0];
  if (!layerId) {
    SLMessage.warning('无法确定当前图层');
    return;
  }

  // 获取点击位置的屏幕坐标
  const screenPoint = {
    x: event.x,
    y: event.y
  };

  console.log('点击位置屏幕坐标:', screenPoint.x, screenPoint.y);

  // 使用GeoServer的WMS服务查询
  // 确保图层名称格式正确
  const layerName = typeof layerId === 'string' ? layerId : `${layerId}`;
  console.log('使用图层名称进行查询:', layerName);

  // 执行点击查询
  const response = await excuteIdentifyByGeoserver(staticState.view, '/geoserver/guazhou/wms', layerName, screenPoint);
  console.log('GeoServer查询结果:', response);
  // 检查是否有查询结果
  if (response && response.data && response.data.features && response.data.features.length > 0) {
    // 获取第一个要素
    const feature = response.data.features[0];

    if (feature && feature.properties) {
      // 获取要素的属性
      const attributes = { ...feature.properties };

      // 排除系统字段
      delete attributes.OBJECTID;
      delete attributes.SID;
      delete attributes.objectid;
      delete attributes.sid;
      delete attributes.SHAPE;
      delete attributes.shape;
      delete attributes.geometry;
      delete attributes.geom;

      // 保存复制的属性和来源图层
      state.copiedAttributes = attributes;
      state.copiedAttributesLayerId = layerId;

      // 如果当前正在编辑表单，则应用复制的属性到表单
      if (refFormEdit.value && staticState.graphic) {
        // 合并当前属性和复制的属性
        const mergedAttributes = {
          ...refFormEdit.value.dataForm,
          ...attributes
        };

        // 保留原始的系统字段
        if (refFormEdit.value.dataForm.OBJECTID) {
          mergedAttributes.OBJECTID = refFormEdit.value.dataForm.OBJECTID;
        }
        if (refFormEdit.value.dataForm.SID) {
          mergedAttributes.SID = refFormEdit.value.dataForm.SID;
        }

        // 更新表单数据
        refFormEdit.value.dataForm = mergedAttributes;

        // 更新图形属性
        staticState.graphic.attributes = {
          ...staticState.graphic.attributes,
          ...attributes
        };

        SLMessage.success('已复制属性并应用到当前表单');
      } else {
        SLMessage.success('已复制属性');
      }

      console.log('已复制属性:', attributes);

      // 退出复制属性模式
      exitCopyAttributesMode();
    } else {
      SLMessage.warning('要素没有属性');
    }
  } else {
    SLMessage.warning('未找到要素，请重新点击');
  }
};

/**
 * 处理添加要素
 * 根据当前选择的图层类型，添加相应的要素（点、线、面）
 */
const handleAddFeature = () => {
  if (!state.editing) {
    SLMessage.error('请先开始编辑');
    return;
  }

  // 检查表单数据
  console.log('表单数据:', refForm.value?.dataForm);

  // 检查是否已选择图层
  const layerId = refForm.value?.dataForm.layerid?.[0];
  if (!layerId) {
    SLMessage.warning('请先选择图层');
    // 关闭编辑器下拉框
    refEditorPop.value?.hide();
    return;
  }

  // 获取当前图层名称
  const layerName = typeof layerId === 'string' ? layerId : '图层';
  console.log('当前选择的图层:', layerName);

  // 尝试从图层信息中获取几何类型
  const layerInfo = layerSelector.curLayerInfo;
  // 根据图层类型确定要创建的几何类型
  let geometryType = layerInfo?.type || 'point'; // 默认为点

  // 确保 sketch 已初始化
  if (!staticState.sketch) {
    console.log('初始化 sketch...');
    initEditSketch();
  }

  // 确保 graphicsLayer 已初始化
  if (!staticState.graphicsLayer) {
    console.log('初始化 graphicsLayer...');
    staticState.graphicsLayer = getGraphicLayer(staticState.view, {
      id: 'pipe-editing',
      title: '编辑'
    });
  }

  // 使用 sketch 添加要素
  try {
    // 根据几何类型提供不同的操作提示
    if (geometryType.toLowerCase().includes('point')) {
      SLMessage.info('请在地图上点击添加点要素');
      geometryType = 'point'
    } else if (geometryType.toLowerCase().includes('line')) {
      SLMessage.info('请在地图上点击添加线的顶点，双击结束绘制');
      geometryType = 'polyline'
    } else {
      SLMessage.info('请在地图上点击添加面的顶点，双击结束绘制');
    }

    // 对所有几何类型都使用点击模式，而不是拖动模式
    staticState.sketch?.create(geometryType as any, {
      mode: 'click'  // 使用点击模式，不再使用freehand模式
    });

    console.log('创建要素命令已发送，使用点击模式');
  } catch (error) {
    console.error('创建要素失败:', error);
    SLMessage.error('创建要素失败，请重试');
  }

  // 关闭编辑器下拉框
  refEditorPop.value?.hide();
};
onMounted(() => {
  state.isMounted = true;
  coordinateFormatter.load();
});
onBeforeUnmount(() => {
  destroySketch();
  splitPipe.destroy();
});
</script>
<style lang="scss" scoped>
.el-button {
  --el-fill-color: transparent;
  --el-fill-color-light: transparent;
}
// .darkblue {
//   .top-toolbar {
//     background-color: rgba(21, 45, 68, 0.9);
//   }
// }
.top-toolbar {
  position: absolute;
  top: 20px;
  left: 30px;
  border-radius: 4px;
  display: flex;
  flex-wrap: nowrap;
  background: var(--el-bg-color);
  box-shadow: 0 4px 8px rgba(26, 42, 83, 0.25);
  border-radius: 12px;
  .el-button + .el-button {
    margin: 0;
  }
}
.edit-toogle {
  dd {
    margin-left: 0;
    font-size: 14px;
    padding: 0 8px;
    line-height: 25px;
    display: flex;
    align-items: center;
    word-break: keep-all;
    cursor: pointer;
    & > .el-icon,
    & > .el-checkbox {
      margin-right: 8px;
    }
    &.disabled {
      color: var(--el-disabled-text-color);
      cursor: not-allowed;
    }
    &.active {
      color: var(--el-color-primary);
      &.disabled {
        color: var(--el-color-primary-light-5);
      }
    }
  }
  dt {
    padding: 12px 0;
    font-size: 12px;
  }
}
:deep(.pipe-attr-panel-editable) {
  left: 0;
  top: 0;
  width: 350px;
}
.edit-panel-header {
  padding: 8px 12px;
  height: 150px;
}
.edit-panel-content {
  height: 400px;
  padding: 8px 12px;
}
.edit-panel-footer {
  text-align: right;
  padding: 8px 12px;
}
</style>
