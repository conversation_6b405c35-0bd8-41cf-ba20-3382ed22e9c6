/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.DatavVO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * datav 报警统计列表对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlarmListVO implements Comparable<AlarmListVO>{
    // 设备名称
    private String deviceName;
    // 报警总数
    private int alarmTotalValue;
    // 已解除报警数
    private int alarmClearedAckValue;
    // 未解除报警数
    private int alarmActiveAckValue;

    @Override
    public int compareTo(AlarmListVO alarmListVO) {
        Integer thisCount = this.getAlarmActiveAckValue();
        return thisCount.compareTo(alarmListVO.getAlarmActiveAckValue());
    }
}
