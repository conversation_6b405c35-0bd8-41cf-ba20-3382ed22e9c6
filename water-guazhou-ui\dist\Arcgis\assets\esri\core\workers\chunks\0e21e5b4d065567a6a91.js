"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5329],{25329:(e,t,a)=>{a.r(t),a.d(t,{createSymbolSchema:()=>o});var r=a(66039),n=a(63523);function l(e){return"line-marker"===e.type?{type:"line-marker",color:e.color?.toJSON(),placement:e.placement,style:e.style}:e.constructor.fromJSON(e.toJSON()).toJSON()}function s(e){return(0,n.hF)(e)}function o(e,t,a=!1){if(!e)return null;switch(e.type){case"simple-fill":case"picture-fill":return function(e,t,a){const o=(0,n.jj)(r.LW.FILL,t),c=a?s(o):o,i=e.clone(),h=i.outline,m=(0,n.jy)(t.symbologyType);m||(i.outline=null);const u={materialKey:c,hash:i.hash(),...l(i)};if(m)return u;const y=[];if(y.push(u),h){const e=(0,n.jj)(r.LW.LINE,{...t,isOutline:!0}),o={materialKey:a?s(e):e,hash:h.hash(),...l(h)};y.push(o)}return{type:"composite-symbol",layers:y,hash:y.reduce(((e,t)=>t.hash+e),"")}}(e,t,a);case"simple-marker":case"picture-marker":return function(e,t,a){const o=(0,n.jj)(r.LW.MARKER,t),c=a?s(o):o,i=l(e);return{materialKey:c,hash:e.hash(),...i,angle:e.angle,maxVVSize:t.maxVVSize}}(e,t,a);case"simple-line":return function(e,t,a){const o=(0,n.jy)(t.symbologyType)?r.mD.DEFAULT:t.symbologyType,c=(0,n.jj)(r.LW.LINE,{...t,symbologyType:o}),i=a?s(c):c,h=e.clone(),m=h.marker;h.marker=null;const u=[];if(u.push({materialKey:i,hash:h.hash(),...l(h)}),m){const e=(0,n.jj)(r.LW.MARKER,t),o=a?s(e):e;m.color=m.color??h.color,u.push({materialKey:o,hash:m.hash(),lineWidth:h.width,...l(m)})}return{type:"composite-symbol",layers:u,hash:u.reduce(((e,t)=>t.hash+e),"")}}(e,t,a);case"text":return function(e,t,a){const o=(0,n.jj)(r.LW.TEXT,t),c=a?s(o):o,i=l(e);return{materialKey:c,hash:e.hash(),...i,angle:e.angle,maxVVSize:t.maxVVSize}}(e,t,a);case"label":return function(e,t,a){const l=e.toJSON(),o=(0,n.jj)(r.LW.LABEL,{...t,placement:l.labelPlacement});return{materialKey:a?s(o):o,hash:e.hash(),...l,labelPlacement:l.labelPlacement}}(e,t,a);case"cim":return{type:"cim",rendererKey:t.vvFlags,data:e.data,maxVVSize:t.maxVVSize};case"CIMSymbolReference":return{type:"cim",rendererKey:t.vvFlags,data:e,maxVVSize:t.maxVVSize};case"web-style":return{...l(e),type:"web-style",hash:e.hash(),rendererKey:t.vvFlags,maxVVSize:t.maxVVSize};default:throw new Error(`symbol not supported ${e.type}`)}}}}]);