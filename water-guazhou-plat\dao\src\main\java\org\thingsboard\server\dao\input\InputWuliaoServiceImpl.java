package org.thingsboard.server.dao.input;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.WuniInput;
import org.thingsboard.server.dao.model.sql.input.InputWuliao;
import org.thingsboard.server.dao.sql.input.InputWuliaoRepository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class InputWuliaoServiceImpl implements InputWuliaoService {

    @Autowired
    private InputWuliaoRepository inputWuliaoRepository;

    @Override
    public void save(InputWuliao inputWuliao) {
        inputWuliaoRepository.save(inputWuliao);
    }

    @Override
    public List<InputWuliao> findMonthReport(String month) {
        Date monthStart = DateUtils.str2Date(month, "yyyy-MM");
        Calendar instance = Calendar.getInstance();
        instance.setTime(monthStart);
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));

        Date monthEnd = instance.getTime();

        List<InputWuliao> wuliaoList = inputWuliaoRepository.findByTimeBetweenOrderByTimeAsc(monthStart, monthEnd);
        // 统计数据
        InputWuliao avgData = new InputWuliao();
        avgData.setTimeStr("平均值");
        InputWuliao minData = new InputWuliao();
        minData.setTimeStr("最低");
        InputWuliao maxData = new InputWuliao();
        maxData.setTimeStr("最高");
        InputWuliao totalData = new InputWuliao();
        totalData.setTimeStr("合计");
        if (wuliaoList != null && wuliaoList.size() > 0) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            for (InputWuliao wuliao : wuliaoList) {
                wuliao.setTimeStr(dateFormat.format(wuliao.getTime()));
                // 处理水量
                if (wuliao.getProcessWater() != null && (minData.getProcessWater() == null || minData.getProcessWater().doubleValue() < wuliao.getProcessWater().doubleValue())) {
                    minData.setProcessWater(wuliao.getProcessWater());
                }
                if (wuliao.getProcessWater() != null && (maxData.getProcessWater() == null || maxData.getProcessWater().doubleValue() > wuliao.getProcessWater().doubleValue())) {
                    maxData.setProcessWater(wuliao.getProcessWater());
                }
                if (totalData.getProcessWater() == null) {
                    totalData.setProcessWater(wuliao.getProcessWater());
                } else {
                    if (wuliao.getProcessWater() == null) {
                        wuliao.setProcessWater(BigDecimal.ZERO);
                    }
                    totalData.setProcessWater(totalData.getProcessWater().add(wuliao.getProcessWater()));
                }

                // 絮混凝剂数量
                if (wuliao.getXfljNum() != null && (minData.getXfljNum() == null || minData.getXfljNum().doubleValue() < wuliao.getXfljNum().doubleValue())) {
                    minData.setXfljNum(wuliao.getXfljNum());
                }
                if (wuliao.getXfljNum() != null && (maxData.getXfljNum() == null || maxData.getXfljNum().doubleValue() > wuliao.getXfljNum().doubleValue())) {
                    maxData.setXfljNum(wuliao.getXfljNum());
                }
                if (totalData.getXfljNum() == null) {
                    totalData.setXfljNum(wuliao.getXfljNum());
                } else {
                    if (wuliao.getXfljNum() == null) {
                        wuliao.setXfljNum(BigDecimal.ZERO);
                    }
                    totalData.setXfljNum(totalData.getXfljNum().add(wuliao.getXfljNum()));
                }

                // 絮混凝剂单耗
                if (wuliao.getXfljUnit() != null && (minData.getXfljUnit() == null || minData.getXfljUnit().doubleValue() < wuliao.getXfljUnit().doubleValue())) {
                    minData.setXfljUnit(wuliao.getXfljUnit());
                }
                if (wuliao.getXfljUnit() != null && (maxData.getXfljUnit() == null || maxData.getXfljUnit().doubleValue() > wuliao.getXfljUnit().doubleValue())) {
                    maxData.setXfljUnit(wuliao.getXfljUnit());
                }
                if (totalData.getXfljUnit() == null) {
                    totalData.setXfljUnit(wuliao.getXfljUnit());
                } else {
                    if (wuliao.getXfljUnit() == null) {
                        wuliao.setXfljUnit(BigDecimal.ZERO);
                    }
                    totalData.setXfljUnit(totalData.getXfljUnit().add(wuliao.getXfljUnit()));
                }

                // 消毒剂数量
                if (wuliao.getXdjNum() != null && (minData.getXdjNum() == null || minData.getXdjNum().doubleValue() < wuliao.getXdjNum().doubleValue())) {
                    minData.setXdjNum(wuliao.getXdjNum());
                }
                if (wuliao.getXdjNum() != null && (maxData.getXdjNum() == null || maxData.getXdjNum().doubleValue() > wuliao.getXdjNum().doubleValue())) {
                    maxData.setXdjNum(wuliao.getXdjNum());
                }
                if (totalData.getXdjNum() == null) {
                    totalData.setXdjNum(wuliao.getXdjNum());
                } else {
                    if (wuliao.getXdjNum() == null) {
                        wuliao.setXdjNum(BigDecimal.ZERO);
                    }
                    totalData.setXdjNum(totalData.getXdjNum().add(wuliao.getXdjNum()));
                }

                // 消毒剂单耗
                if (wuliao.getXdjUnit() != null && (minData.getXdjUnit() == null || minData.getXdjUnit().doubleValue() < wuliao.getXdjUnit().doubleValue())) {
                    minData.setXdjUnit(wuliao.getXdjUnit());
                }
                if (wuliao.getXdjUnit() != null && (maxData.getXdjUnit() == null || maxData.getXdjUnit().doubleValue() > wuliao.getXdjUnit().doubleValue())) {
                    maxData.setXdjUnit(wuliao.getXdjUnit());
                }
                if (totalData.getXdjUnit() == null) {
                    totalData.setXdjUnit(wuliao.getXdjUnit());
                } else {
                    if (wuliao.getXdjUnit() == null) {
                        wuliao.setXdjUnit(BigDecimal.ZERO);
                    }
                    totalData.setXdjUnit(totalData.getXdjUnit().add(wuliao.getXdjUnit()));
                }

                // 商业碳源数量
                if (wuliao.getTyNum() != null && (minData.getTyNum() == null || minData.getTyNum().doubleValue() < wuliao.getTyNum().doubleValue())) {
                    minData.setTyNum(wuliao.getTyNum());
                }
                if (wuliao.getTyNum() != null && (maxData.getTyNum() == null || maxData.getTyNum().doubleValue() > wuliao.getTyNum().doubleValue())) {
                    maxData.setTyNum(wuliao.getTyNum());
                }
                if (totalData.getTyNum() == null) {
                    totalData.setTyNum(wuliao.getTyNum());
                } else {
                    if (wuliao.getTyNum() == null) {
                        wuliao.setTyNum(BigDecimal.ZERO);
                    }
                    totalData.setTyNum(totalData.getTyNum().add(wuliao.getTyNum()));
                }

                // 商业碳源单耗
                if (wuliao.getTyUnit() != null && (minData.getTyUnit() == null || minData.getTyUnit().doubleValue() < wuliao.getTyUnit().doubleValue())) {
                    minData.setTyUnit(wuliao.getTyUnit());
                }
                if (wuliao.getTyUnit() != null && (maxData.getTyUnit() == null || maxData.getTyUnit().doubleValue() > wuliao.getTyUnit().doubleValue())) {
                    maxData.setTyUnit(wuliao.getTyUnit());
                }
                if (totalData.getTyUnit() == null) {
                    totalData.setTyUnit(wuliao.getTyUnit());
                } else {
                    if (wuliao.getTyUnit() == null) {
                        wuliao.setTyUnit(BigDecimal.ZERO);
                    }
                    totalData.setTyUnit(totalData.getTyUnit().add(wuliao.getTyUnit()));
                }

                // 除磷药剂数量
                if (wuliao.getClyjNum() != null && (minData.getClyjNum() == null || minData.getClyjNum().doubleValue() < wuliao.getClyjNum().doubleValue())) {
                    minData.setClyjNum(wuliao.getClyjNum());
                }
                if (wuliao.getClyjNum() != null && (maxData.getClyjNum() == null || maxData.getClyjNum().doubleValue() > wuliao.getClyjNum().doubleValue())) {
                    maxData.setClyjNum(wuliao.getClyjNum());
                }
                if (totalData.getClyjNum() == null) {
                    totalData.setClyjNum(wuliao.getClyjNum());
                } else {
                    if (wuliao.getClyjNum() == null) {
                        wuliao.setClyjNum(BigDecimal.ZERO);
                    }
                    totalData.setClyjNum(totalData.getClyjNum().add(wuliao.getClyjNum()));
                }

                // 除磷药剂单耗
                if (wuliao.getClyjUnit() != null && (minData.getClyjUnit() == null || minData.getClyjUnit().doubleValue() < wuliao.getClyjUnit().doubleValue())) {
                    minData.setClyjUnit(wuliao.getClyjUnit());
                }
                if (wuliao.getClyjUnit() != null && (maxData.getClyjUnit() == null || maxData.getClyjUnit().doubleValue() > wuliao.getClyjUnit().doubleValue())) {
                    maxData.setClyjUnit(wuliao.getClyjUnit());
                }
                if (totalData.getClyjUnit() == null) {
                    totalData.setClyjUnit(wuliao.getClyjUnit());
                } else {
                    if (wuliao.getClyjUnit() == null) {
                        wuliao.setClyjUnit(BigDecimal.ZERO);
                    }
                    totalData.setClyjUnit(totalData.getClyjUnit().add(wuliao.getClyjUnit()));
                }

                // 污泥脱水剂数量
                if (wuliao.getWntsjNum() != null && (minData.getWntsjNum() == null || minData.getWntsjNum().doubleValue() < wuliao.getWntsjNum().doubleValue())) {
                    minData.setWntsjNum(wuliao.getWntsjNum());
                }
                if (wuliao.getWntsjNum() != null && (maxData.getWntsjNum() == null || maxData.getWntsjNum().doubleValue() > wuliao.getWntsjNum().doubleValue())) {
                    maxData.setWntsjNum(wuliao.getWntsjNum());
                }
                if (totalData.getWntsjNum() == null) {
                    totalData.setWntsjNum(wuliao.getWntsjNum());
                } else {
                    if (wuliao.getWntsjNum() == null) {
                        wuliao.setWntsjNum(BigDecimal.ZERO);
                    }
                    totalData.setWntsjNum(totalData.getWntsjNum().add(wuliao.getWntsjNum()));
                }

                // 污泥脱水剂单耗
                if (wuliao.getWntsjUnit() != null && (minData.getWntsjUnit() == null || minData.getWntsjUnit().doubleValue() < wuliao.getWntsjUnit().doubleValue())) {
                    minData.setWntsjUnit(wuliao.getWntsjUnit());
                }
                if (wuliao.getWntsjUnit() != null && (maxData.getWntsjUnit() == null || maxData.getWntsjUnit().doubleValue() > wuliao.getWntsjUnit().doubleValue())) {
                    maxData.setWntsjUnit(wuliao.getWntsjUnit());
                }
                if (totalData.getWntsjUnit() == null) {
                    totalData.setWntsjUnit(wuliao.getWntsjUnit());
                } else {
                    if (wuliao.getWntsjUnit() == null) {
                        wuliao.setWntsjUnit(BigDecimal.ZERO);
                    }
                    totalData.setWntsjUnit(totalData.getWntsjUnit().add(wuliao.getWntsjUnit()));
                }

                // 耗电
                if (wuliao.getEnergyNum() != null && (minData.getEnergyNum() == null || minData.getEnergyNum().doubleValue() < wuliao.getEnergyNum().doubleValue())) {
                    minData.setEnergyNum(wuliao.getEnergyNum());
                }
                if (wuliao.getEnergyNum() != null && (maxData.getEnergyNum() == null || maxData.getEnergyNum().doubleValue() > wuliao.getEnergyNum().doubleValue())) {
                    maxData.setEnergyNum(wuliao.getEnergyNum());
                }
                if (totalData.getEnergyNum() == null) {
                    totalData.setEnergyNum(wuliao.getEnergyNum());
                } else {
                    if (wuliao.getEnergyNum() == null) {
                        wuliao.setEnergyNum(BigDecimal.ZERO);
                    }
                    totalData.setEnergyNum(totalData.getEnergyNum().add(wuliao.getEnergyNum()));
                }

                // 耗电单耗
                if (wuliao.getEnergyUnit() != null && (minData.getEnergyUnit() == null || minData.getEnergyUnit().doubleValue() < wuliao.getEnergyUnit().doubleValue())) {
                    minData.setEnergyUnit(wuliao.getEnergyUnit());
                }
                if (wuliao.getEnergyUnit() != null && (maxData.getEnergyUnit() == null || maxData.getEnergyUnit().doubleValue() > wuliao.getEnergyUnit().doubleValue())) {
                    maxData.setEnergyUnit(wuliao.getEnergyUnit());
                }
                if (totalData.getEnergyUnit() == null) {
                    totalData.setEnergyUnit(wuliao.getEnergyUnit());
                } else {
                    if (wuliao.getEnergyUnit() == null) {
                        wuliao.setEnergyUnit(BigDecimal.ZERO);
                    }
                    totalData.setEnergyUnit(totalData.getEnergyUnit().add(wuliao.getEnergyUnit()));
                }

            }
            // 计算平均值
            int size = wuliaoList.size();

            BigDecimal processWater = totalData.getProcessWater();
            BigDecimal xfljNum = totalData.getXfljNum();
            BigDecimal xfljUnit = totalData.getXfljUnit();
            BigDecimal xdjNum = totalData.getXdjNum();
            BigDecimal xdjUnit = totalData.getXdjUnit();
            BigDecimal tyNum = totalData.getTyNum();
            BigDecimal tyUnit = totalData.getTyUnit();
            BigDecimal clyjNum = totalData.getClyjNum();
            BigDecimal clyjUnit = totalData.getClyjUnit();
            BigDecimal wntsjNum = totalData.getWntsjNum();
            BigDecimal wntsjUnit = totalData.getWntsjUnit();
            BigDecimal energyNum = totalData.getEnergyNum();
            BigDecimal energyUnit = totalData.getEnergyUnit();

            avgData.setProcessWater(processWater.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setXfljNum(xfljNum.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setXfljUnit(xfljUnit.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setXdjNum(xdjNum.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setXdjUnit(xdjUnit.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setTyNum(tyNum.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setTyUnit(tyUnit.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setClyjNum(clyjNum.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setClyjUnit(clyjUnit.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setWntsjNum(wntsjNum.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setWntsjUnit(wntsjUnit.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setEnergyNum(energyNum.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));
            avgData.setEnergyUnit(energyUnit.divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_EVEN));

            wuliaoList.add(minData);
            wuliaoList.add(maxData);
            wuliaoList.add(avgData);
            wuliaoList.add(totalData);
        }

        return wuliaoList;
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            inputWuliaoRepository.delete(id);
        }
    }

    @Override
    public List<InputWuliao> findList(String month) {
        Date monthStart = DateUtils.str2Date(month, "yyyy-MM");
        Calendar instance = Calendar.getInstance();
        instance.setTime(monthStart);
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));

        Date monthEnd = instance.getTime();

        List<InputWuliao> wuliaoList = inputWuliaoRepository.findByTimeBetweenOrderByTimeAsc(monthStart, monthEnd);
        return wuliaoList;
    }

}
