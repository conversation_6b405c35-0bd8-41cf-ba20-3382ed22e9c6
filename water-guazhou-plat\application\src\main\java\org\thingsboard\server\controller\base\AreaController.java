package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.area.AreaService;
import org.thingsboard.server.dao.model.sql.AreaEntity;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 区域
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@RestController
@RequestMapping("api/area")
public class AreaController extends BaseController {

    @Autowired
    private AreaService areaService;

    @GetMapping("tree/page")
    public IstarResponse getTree(@RequestParam(required = false, defaultValue = "") String name, @RequestParam(required = false, defaultValue = "") String shortName, int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(areaService.getTree(name, shortName, page, size, tenantId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody AreaEntity areaEntity) throws ThingsboardException {
        areaEntity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        areaEntity.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getId().getId()));
        return IstarResponse.ok(areaService.save(areaEntity));

    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return IstarResponse.ok(areaService.delete(ids));
    }

    @PostMapping("bindImg")
    public IstarResponse bindImg(@RequestBody AreaEntity areaEntity) {
        return areaService.bindImg(areaEntity);
    }
}
