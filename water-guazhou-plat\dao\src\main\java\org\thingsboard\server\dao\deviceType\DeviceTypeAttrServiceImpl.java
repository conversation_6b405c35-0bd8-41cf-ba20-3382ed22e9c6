package org.thingsboard.server.dao.deviceType;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceTypeAttr;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeAttrMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypeAttrPageRequest;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypeAttrSaveRequest;

import java.util.List;

@Service
public class DeviceTypeAttrServiceImpl implements DeviceTypeAttrService {
    @Autowired
    private DeviceTypeAttrMapper attrMapper;

    @Override
    public IPage<DeviceTypeAttr> findAllConditional(DeviceTypeAttrPageRequest request) {
        return attrMapper.findByPage(request);
    }

    @Override
    public DeviceTypeAttr save(DeviceTypeAttrSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, attrMapper);
    }

    @Override
    public boolean update(DeviceTypeAttr entity) {
        return attrMapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return attrMapper.deleteById(id) > 0;
    }

    @Override
    public List<DeviceTypeAttr> getListBySerialId(String serialId, String tenantId, String type) {
        QueryWrapper<DeviceTypeAttr> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("serial_id", serialId);
        queryWrapper.eq("tenant_id", tenantId);
        // 移除对不存在的 type 列的引用
        // queryWrapper.eq("type", type);
        return attrMapper.selectList(queryWrapper);
    }

}
