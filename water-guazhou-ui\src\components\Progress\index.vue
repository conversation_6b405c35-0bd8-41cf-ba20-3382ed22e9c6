<template>
  <div
    class="progress"
    :style="computedStyle"
    :data-rate="rate.toFixed(2) + '%'"
  >
    <div ref="refContainer" class="progress-inner-wrapper">
      <div
        v-for="(item, i) in 10"
        :key="i"
        class="pregress-item"
        :class="
          i < Math.floor(rate / 10)
            ? 'active'
            : i === Math.floor(rate / 10)
              ? 'half-active'
              : ''
        "
      ></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const refContainer = ref<HTMLDivElement>();
const props = defineProps<{
  rate: number;
  startColor?: string;
  endColor?: string;
}>();
const computedStyle = computed(() => {
  const nonNullRate = props.rate || 0;
  const offsetLeft = ((nonNullRate % 10) / 10) * 56;
  const marginRight = 10;
  const itemWidth = refContainer.value
    ? (refContainer.value.clientWidth + marginRight) / 10 - marginRight
    : 50;
  const preCount = Math.floor(nonNullRate / 10);
  return {
    '--item-width': itemWidth + 'px',
    '--item-margin-right': marginRight + 'px',
    '--bar-offset-left': offsetLeft + 'px',
    '--bar-left':
      preCount * itemWidth + preCount * marginRight + offsetLeft - 2 + 'px'
  };
});
</script>
<style lang="scss" scoped>
.progress {
  height: 60px;
  position: relative;
  &::before {
    content: attr(data-rate);
    position: absolute;
    width: 56px;
    bottom: 30px;
    left: var(--bar-left);
    transform: translateX(-50%);
    border: 1px solid rgba(14, 241, 241, 1);
    height: 22px;
    font-size: 12px;
    line-height: 22px;
    border-radius: 4px;
    text-align: center;
  }
  &::after {
    content: '';
    position: absolute;
    width: 1px;
    bottom: 22px;
    left: var(--bar-left);
    height: 8px;
    background-color: rgba(14, 241, 241, 1);
  }
  .progress-inner-wrapper {
    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  .pregress-item {
    background: rgba(49, 174, 244, 0.2);
    width: var(--item-width, 50px);
    height: 20px;
    border-radius: 4px;
    margin-right: var(--item-margin-right);
    &.active {
      background: linear-gradient(to right, #31aef4 0%, #0df1f1 100%);
    }
    &.half-active {
      background: linear-gradient(
        to right,
        rgb(49, 174, 244) 0%,
        rgb(13, 241, 241) var(--bar-offset-left, var(--item-width) / 2),
        rgba(49, 174, 244, 0.2) var(--bar-offset-left, var(--item-width) / 2)
      );
    }
  }
}
</style>
