package org.thingsboard.server.dao.deviceType;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.util.imodel.query.GeneralDeviceTypeService;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypeSaveRequest;

import java.util.List;

public interface DeviceTypeService extends GeneralDeviceTypeService {
    /**
     * 分页条件查询设备类型
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DeviceType> findAllConditional(DeviceTypePageRequest request);

    /**
     * 查询完整的类型树
     *
     * @param tenantId 客户id
     * @return 类型树
     */
    List<DeviceType> findAllStructure(String tenantId);

    /**
     * 保存设备类型
     *
     * @param deviceType 实体信息
     * @return 保存好的实体
     */
    DeviceType save(DeviceTypeSaveRequest deviceType);

    /**
     * 增量修改
     *
     * @param deviceType 实体信息
     * @return 是否修改成功
     */
    boolean update(DeviceType deviceType);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 是否允许删除
     *
     * @param id 唯一标识
     * @return 是否允许删除
     */
    boolean canBeDelete(String id, String tenantId);

    /**
     * 序列号是否已存在
     *
     * @param serialId 序列号
     * @param tenantId 客户id
     * @return 是否已存在
     */
    boolean existsBySerialId(String serialId, String tenantId);

    /**
     * 获取父级id
     *
     * @param id 唯一标识
     * @return 父级id
     */
    String getParentId(String id);

    /**
     * 通过序列号获取当前深度
     *
     * @param serialId 序列号啊
     * @param tenantId 客户id
     * @return 深度
     */
    int getDepthBySerialId(String serialId, String tenantId);

}
