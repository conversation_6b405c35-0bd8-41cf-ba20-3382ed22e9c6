import{_ as v}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{d as D,c as u,M as w,r as i,a8 as I,s as L,bu as C,o as q,g as B,n as F,q as l,i as c,F as T,b6 as M,bL as V}from"./index-r0dFAfgr.js";import{_ as $}from"./CardTable-rdWOL4_6.js";import{_ as E}from"./CardSearch-CB_HNR-Q.js";import N from"./detail-UHbHAYbG.js";import{I as W}from"./common-CvK_P_ao.js";import{p as z}from"./process-DWVjEFpZ.js";import{b as R,p as j}from"./applyInstall-D-IustB3.js";import{f as G}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./printUtils-C-AxhDcd.js";const H={class:"wrapper"},oe=D({__name:"index",setup(O){const f=u(),{$messageSuccess:k,$messageError:x}=w(),o=i({typeList:[],taskInfo:{},currentId:""}),m=u(),g=u(),S=i({filters:[{label:"工程编号",field:"code",type:"input"},{label:"工程类型",field:"type",type:"select",options:I(()=>o.typeList)},{label:"步骤名称",field:"currentStep",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:W.QUERY,click:()=>p()}]}]}),_=i({title:"处理",labelWidth:"100px",dialogWidth:"800px",submit:e=>{R({id:o.currentId,status:e.delStatus,remark:e.remark||""}).then(t=>{var a;t.data.code===200?(k("提交成功"),(a=f.value)==null||a.closeDialog(),p()):x(t.data.message)})},defaultValue:{delStatus:1},group:[{fields:[{type:"select",label:"审核",field:"delStatus",options:[{label:"通过",value:1},{label:"驳回",value:2}],rules:[{required:!0,message:"请选择处理结果"}]},{type:"textarea",label:"备注",field:"remark",handleHidden:(e,t,a)=>{a.hidden=e.delStatus===1}}]}]}),b=i({title:"",cancel:!1,width:"80%",group:[]}),s=i({indexVisible:!0,columns:[{label:"工程编号",prop:"code"},{label:"任务类型",prop:"typeName"},{label:"当前步骤",prop:"currentStep"},{label:"任务地址",prop:"address"},{label:"创建时间",prop:"createTime",formatter:(e,t)=>G(t)}],dataList:[],operationFixed:"right",operationWidth:150,operations:[{perm:!0,text:"接收",isTextBtn:!1,click:e=>{var t;o.currentId=e.id,_.defaultValue={...e,delStatus:1},(t=f.value)==null||t.openDialog()}},{perm:!0,text:"查看",isTextBtn:!1,type:"success",svgIcon:L(V),click:e=>{var t;b.title=e.code,(t=m.value)==null||t.openDrawer(),o.taskInfo=e}}],pagination:{refreshData:({page:e,size:t})=>{s.pagination.page=e,s.pagination.limit=t,p()}}});C(async()=>{var a,r;const e=await z({page:1,size:9999});o.typeList=(a=e.data)==null?void 0:a.data.data;const t=(r=e.data)==null?void 0:r.data.data;o.typeList=t.map(n=>({label:n.name,value:n.id}))});const p=async()=>{var r,n,d,y,h;const t={...((r=g.value)==null?void 0:r.queryParams)||{},status:"未接收",page:s.pagination.page||1,size:s.pagination.limit||20},a=await j(t);s.pagination.total=(d=(n=a.data)==null?void 0:n.data)==null?void 0:d.total,s.dataList=(h=(y=a.data)==null?void 0:y.data)==null?void 0:h.data};return q(async()=>{await p()}),(e,t)=>{const a=E,r=$,n=M,d=v;return B(),F("div",H,[l(a,{ref_key:"refSearch",ref:g,config:c(S)},null,8,["config"]),l(r,{class:"card-table",config:c(s)},null,8,["config"]),l(n,{ref_key:"refDetail",ref:m,config:c(b)},{default:T(()=>[l(N,{"task-info":c(o).taskInfo,"task-id":"1"},null,8,["task-info"])]),_:1},8,["config"]),l(d,{ref_key:"refForm",ref:f,config:c(_)},null,8,["config"])])}}});export{oe as default};
