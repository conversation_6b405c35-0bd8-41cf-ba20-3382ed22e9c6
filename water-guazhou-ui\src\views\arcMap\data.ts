export const geodata = (): { name: string; value: number; pnt: number[] }[] => {
  const length = 1000
  return Array.from({ length }).map((item, i) => {
    return {
      name: 'heatmark-' + i,
      value: Number((Math.random() * 2).toFixed(4)),
      pnt: [106.5468 + Math.random() * 0.1, 29.5661 + Math.random() * 0.1]
    }
  })
  //   [
  //   { name: '重庆', value: Number((Math.random()*10).toFixed(4)), pnt: [106.5468, 29.5661] },
  //   { name: '酉阳县', value: Number((Math.random()*10).toFixed(4)), pnt: [108.8196, 28.8666] },
  //   { name: '奉节县', value: Number((Math.random()*10).toFixed(4)), pnt: [109.3909, 30.9265] },
  //   { name: '巫溪县', value: Number((Math.random()*10).toFixed(4)), pnt: [109.3359, 31.4813] },
  //   { name: '开县', value: Number((Math.random()*10).toFixed(4)), pnt: [108.4131, 31.2561] },
  //   { name: '彭水县', value: Number((Math.random()*10).toFixed(4)), pnt: [108.2043, 29.3994] },
  //   { name: '云阳县', value: Number((Math.random()*10).toFixed(4)), pnt: [108.8306, 31.0089] },
  //   { name: '万州区', value: Number((Math.random()*10).toFixed(4)), pnt: [108.3911, 30.6958] },
  //   { name: '城口县', value: Number((Math.random()*10).toFixed(4)), pnt: [108.7756, 31.9098] },
  //   { name: '江津区', value: Number((Math.random()*10).toFixed(4)), pnt: [106.2158, 28.9874] },
  //   { name: '石柱县', value: Number((Math.random()*10).toFixed(4)), pnt: [108.2813, 30.1025] },
  //   { name: '巫山县', value: Number((Math.random()*10).toFixed(4)), pnt: [109.8853, 31.1188] },
  //   { name: '涪陵区', value: Number((Math.random()*10).toFixed(4)), pnt: [107.3364, 29.6796] },
  //   { name: '丰都县', value: Number((Math.random()*10).toFixed(4)), pnt: [107.8418, 29.9048] },
  //   { name: '武隆县', value: Number((Math.random()*10).toFixed(4)), pnt: [107.655, 29.35] },
  //   { name: '南川区', value: Number((Math.random()*10).toFixed(4)), pnt: [107.1716, 29.1302] },
  //   { name: '秀山县', value: Number((Math.random()*10).toFixed(4)), pnt: [109.0173, 28.5205] },
  //   { name: '黔江区', value: Number((Math.random()*10).toFixed(4)), pnt: [108.7207, 29.4708] },
  //   { name: '合川区', value: Number((Math.random()*10).toFixed(4)), pnt: [106.3257, 30.108] },
  //   { name: '綦江县', value: Number((Math.random()*10).toFixed(4)), pnt: [106.6553, 28.8171] },
  //   { name: '忠县', value: Number((Math.random()*10).toFixed(4)), pnt: [107.8967, 30.3223] },
  //   { name: '梁平县', value: Number((Math.random()*10).toFixed(4)), pnt: [107.7429, 30.6519] },
  //   { name: '巴南区', value: Number((Math.random()*10).toFixed(4)), pnt: [106.7322, 29.4214] },
  //   { name: '潼南县', value: Number((Math.random()*10).toFixed(4)), pnt: [105.7764, 30.1135] },
  //   { name: '永川区', value: Number((Math.random()*10).toFixed(4)), pnt: [105.8643, 29.2566] },
  //   { name: '垫江县', value: Number((Math.random()*10).toFixed(4)), pnt: [107.4573, 30.2454] },
  //   { name: '渝北区', value: Number((Math.random()*10).toFixed(4)), pnt: [106.7212, 29.8499] },
  //   { name: '长寿区', value: Number((Math.random()*10).toFixed(4)), pnt: [107.1606, 29.9762] },
  //   { name: '大足县', value: Number((Math.random()*10).toFixed(4)), pnt: [105.7544, 29.6136] },
  //   { name: '铜梁县', value: Number((Math.random()*10).toFixed(4)), pnt: [106.0291, 29.8059] },
  //   { name: '荣昌县', value: Number((Math.random()*10).toFixed(4)), pnt: [105.5127, 29.4708] },
  //   { name: '璧山县', value: Number((Math.random()*10).toFixed(4)), pnt: [106.2048, 29.5807] },
  //   { name: '北碚区', value: Number((Math.random()*10).toFixed(4)), pnt: [106.5674, 29.8883] },
  //   { name: '万盛区', value: Number((Math.random()*10).toFixed(4)), pnt: [106.908, 28.9325] },
  //   { name: '南岸区', value: Number((Math.random()*10).toFixed(4)), pnt: [106.6663, 29.5367] },
  //   { name: '江北区', value: Number((Math.random()*10).toFixed(4)), pnt: [106.8311, 29.6191] },
  //   { name: '双桥区', value: Number((Math.random()*10).toFixed(4)), pnt: [105.7874, 29.4928] },
  //   { name: '渝中区', value: Number((Math.random()*10).toFixed(4)), pnt: [106.5344, 29.5477] }
  // ]
}
