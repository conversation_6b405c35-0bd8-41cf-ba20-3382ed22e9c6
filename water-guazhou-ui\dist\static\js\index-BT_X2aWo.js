import{_ as B}from"./TreeBox-DDD2iwoR.js";import{_ as z}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as U}from"./index-C9hz-UZb.js";import{d as c,c as y,r as p,l as M,bI as q,bJ as I,b as V,g as P,h as X,F as f,q as i,i as e,p as r,bh as s,G as o,n as G,an as $,aq as H,C as O}from"./index-r0dFAfgr.js";import{_ as E}from"./CardSearch-CB_HNR-Q.js";import{_ as J}from"./index-BJ-QPYom.js";import{G as Y}from"./waterBalance-BluOprpN.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as A}from"./usePartition-DkcY9fQ2.js";import{_ as K}from"./WaterBalanceDetail.vue_vue_type_script_setup_true_lang-BGf9YqXQ.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./index-0NlGN6gS.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const Q={class:"table"},Z={colspan:"9",class:"table-title"},h=["rowspan"],tt={rowspan:"12",style:{background:"rgb(1 87 155)"}},rt={rowspan:"4",style:{background:"rgb(21 101 192)"}},ot={rowspan:"2",style:{background:"rgb(25 118 210)"}},et={style:{background:"rgb(25 118 210)"}},nt={rowspan:"2",style:{background:"rgb(0 200 83)"}},st={rowspan:"1",style:{background:"rgb(25 118 210)"}},lt={rowspan:"2",style:{background:"rgb(33 150 243)"}},at={rowspan:"1",style:{background:"rgb(0 200 83)"}},it={rowspan:"10",style:{background:"rgb(81 98 120)"}},dt={rowspan:"1",style:{background:"rgb(0 200 83)"}},pt={key:0,rowspan:"4",style:{background:"rgb(140, 197, 64)"}},mt={rowspan:"8",style:{background:"rgb(103 128 159)"}},ut={rowspan:"4",style:{background:"rgb(103 128 159)"}},bt={rowspan:"1",style:{background:"rgb(103 128 159)"}},gt={rowspan:"4",style:{background:"rgb(255 230 153)","border-top":"2px solid #222536","border-right":"2px solid #222536"}},yt={rowspan:"8",style:{background:"rgb(146 208 80)","border-left":"1px solid rgb(146 208 80)","border-top":"2px solid #222536"}},ft={rowspan:"8",style:{background:"rgb(0 176 240)","border-top":"1px solid rgb(0 176 240)"}},kt={rowspan:"1",style:{background:"rgb(103 128 159)"}},wt={rowspan:"1",style:{background:"rgb(103 128 159)"}},Ft={rowspan:"1",style:{background:"rgb(103 128 159)"}},Wt={rowspan:"4",style:{background:"rgb(237, 125, 49)"}},Rt={rowspan:"2",style:{background:"rgb(116 144 178)"}},xt={rowspan:"1",style:{background:"rgb(116 144 178)"}},Lt={rowspan:"1",style:{background:"rgb(116 144 178)"}},vt={rowspan:"2",style:{background:"rgb(103 128 159)"}},Tt={rowspan:"1",style:{background:"rgb(103 128 159)"}},Dt={rowspan:"1",style:{background:"rgb(103 128 159)"}},Mt=c({__name:"index",setup(Pt){const F=y(),W=y(),C=y(),R=A(),n=p({isRoot:!0,dataForm:{}}),a=p({data:[],title:"选择分区",isFilterTree:!0,expandOnClickNode:!1,treeNodeHandleClick:l=>{a.currentProject=l,k()},autoFillOptions:()=>{R.getTree().then(()=>{a.data=R.Tree.value||[],a.currentProject=a.data[0],k()})}}),x=(l,t,d)=>{d.hidden=l.type!==d.field},N=p({defaultParams:{type:"month",month:M().format(q),year:M().format(I)},filters:[{type:"select",field:"type",clearable:!1,options:[{label:"按月",value:"month"},{label:"按年",value:"year"}],label:"选择方式"},{handleHidden:x,type:"month",label:"选择月份",field:"month",clearable:!1},{handleHidden:x,type:"year",label:"选择年份",field:"year",clearable:!1}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>k(),iconifyIcon:"ep:search"},{perm:!0,text:"术语字典",type:"default",plain:!0,click:()=>{var l;return(l=L.value)==null?void 0:l.openDialog()},iconifyIcon:"ep:info-filled"},{perm:!0,text:"详情",type:"default",plain:!0,click:()=>{var l;if(!a.currentProject){V.warning("请先选择一个分区");return}(l=W.value)==null||l.openDialog()},iconifyIcon:"ep:more-filled"}]}]}),S=p({height:"none",columns:[{label:"漏损指标修正值",align:"center",prop:"field1",subColumns:[{minWidth:200,label:"类别",prop:"type"},{minWidth:400,label:"计算公式",prop:"method"},{minWidth:100,label:"计算值",prop:"val"}]}],spanMethod({rowIndex:l,columnIndex:t}){if(t===0||t===2){if(l===5)return{rowspan:3,colspan:1};if(l===6)return{rowspan:0,colspan:1};if(l===7)return{rowspan:0,colspan:1}}return{rowspan:1,colspan:1}},dataList:[{type:"综合漏损率RwL",method:"（供水总量-注册用户用水量）/ 供水总量 X 100%"},{type:"漏损率RBL（修正后，评定值）",method:"RBL = RwL - Rn"},{type:"修正类别",method:"计算公式",val:"修正值"},{type:"居民抄表到户水量的修正值（R1）",method:"0.08X居民抄表到户水量占总供水量比例（%）"},{type:"单位供水量管长的修正值（R2）",method:"0.99X（DN75以上管道长度/供水总量-0.0693）X 100%"},{type:"年平均压力修正值（R3）",method:"年平均出厂压力大于0.35MPa小于等于0.55MPa时，修正值R3应为0.5%"},{method:"年平均出厂压力大于0.55MPa小于等于0.75MPa时，修正值R3应为1%"},{method:"年平均出厂压力大于0.75MPa时，修正值R3应为2%"},{type:"最大冻土深度修正值（R4）",method:"最大冻土深度大于1.4m时，修正值应为1%"},{type:"总修正值（Rn）",method:"Rn = R1+R2+R3+R4"}],pagination:{hide:!0}}),L=y(),_=p({title:"术语字典",group:[{fields:[{type:"table",config:{height:500,columns:[{minWidth:200,label:"术语",prop:"say"},{minWidth:260,label:"英文名",prop:"en"},{minWidth:400,label:"术语解释",prop:"desc"}],dataList:[{say:"供水管网",en:"water distribution system",desc:"连接水厂和用户水表（含）之间的管道及其附属设施的总称。"},{say:"自产供水量",en:"self produced water supply",desc:"供水单位自有水厂的供水量，可根据流量计设备的水量数据进行统计计算。"},{say:"外购供水量",en:"purchased water supply",desc:"供水单位向其他单位购买并输入到管网的供水量，通常情况下从邻近水务公司购买的水。"},{say:"趸售水量",en:"wholesale water supply",desc:"供水企业对暂未实现抄表到户的转供单位或物业管理小区的供水。"},{say:"供水管网",en:"water distribution system",desc:"连接水厂和用户水表（含）之间的管道及其附属设施的总称。"},{say:"供水总量",en:"system input quantity",desc:"进入供水管网中的全部水量之和，包括自产供水量和外购供水量。"},{say:"注册用户用水量",en:"authorized consumption",desc:"在供水单位登记注册的用户的计费用水量和免费用水量。"},{say:"计费用水量",en:"billed authorized consumption",desc:"在供水单位注册的计费用户的用水量。"},{say:"免费用水量",en:"unbilled authorized consumption",desc:"按规定减免收费的注册用户的用水量和用于管网维护和冲洗等的水量。"},{say:"漏损水量",en:"water losses",desc:"供水总量和注册用户用水量之间的差值、有漏失水量、计量损失水量和其他随时水量组成。"},{say:"漏失水量",en:"reallosses",desc:"各种类型的管线漏点、管网中水箱及水池等渗漏和溢流造成实际漏掉的水量"},{say:"明漏水量",en:"reported leakage",desc:"水溢出地面或可见的管网漏点的漏失水量"},{say:"暗漏水量",en:"unreported leakage",desc:"指在地面下检测到的管网漏点的漏失水量。"},{say:"背景漏失水量",en:"background leakage",desc:"现有技术手段和措施未能检测到的管网漏点的漏失水量。"},{say:"计量损失水量",en:"metering losses",desc:"计量表具性能限值或计量方式改变导致计量误差的损失水量。"},{say:"其他损失水量",en:"other losses",desc:"未注册用户用水和用户据查等管理因素导致的损失水量。"},{say:"区域管理",en:"zone management",desc:"将供水管网划分为若干供水区域，对每个供水区域的水量，水压进行监测控制，实现漏损量化管理的方式。"},{say:"独立计量区",en:"district metered area",desc:"将供水管网分割成单独十量的供水区域，规模一般小于区域管理的范围。"},{say:"夜间最小流量",en:"minimum night flow",desc:"独立计量区每日夜间用户用水量最小时的进水流量。"},{say:"计量仪表误差",en:"measuring instrument error",desc:"由于水厂出厂流量计的误差造成的水量误差。该误差也包括部分出水管道缺少流量计造成的流量减少情况。"},{say:"水箱、水池渗漏溢流水量",en:"leakage and overflow of water tank",desc:"由于管网中各类水箱、水池的结构产生裂缝、以及水箱进行水控制阀门失效造成水箱溢流的水量损失。"},{say:"居民用户表具误差",en:"meter error of residential users",desc:"由于楼门总表和对应户表的计量水量总会存在一定误差，由于居民用户总表计量水量和分表计量总水量之间的差值产生的水量损失。"},{say:"非居民用户表具误差",en:"not meter error of residential users",desc:"非居民用户表具本身存在误差造成的用水量不准或估值，由于表具计量数据的误差导致的非居民用户用水量的损失。"},{say:"未注册用户用水和管理因素导致的损失",en:"not register water and message water",desc:"由于非法用水、偷盗水、用户拒查、水表漏立方等造成的损失水量。"},{say:"收益水量",en:"revenue water volume",desc:"向用户收取水费使自来水公司获得收益的水量。"},{say:"无收益水量",en:"not revenue water volume",desc:"没有为水司带来收益的水量。"},{say:"零压测试",en:"zero-pressure test",desc:"为判断独立计量区是否封闭，关闭边界阀门后放水，监测区域内压力是否下降至零。"},{say:"漏损率",en:"water loss rate",desc:"管网漏损水量与供水总量之比，通常用百分比表示。"},{say:"漏失率",en:"real loss rate",desc:"管网漏失水量与供水量总量之比，通常用百分比表示。"},{say:"产销差率",en:"production and sales difference rate",desc:"供水产销差水量与供水总量之比。"},{say:"基本漏损率",en:"benchmark water loss rate",desc:"漏损率评定标准修正前的基准控制值。"},{say:"单位供水量管长",en:"pipe length per unit water supply",desc:"管径大于等于75mm的管道总长与供水总量之比。"},{say:"水表量程比",en:"tumdown rate",desc:"水表常用流量和最小流量的比值。"},{say:"最大冻土深度",en:"maximum frozen soil depth",desc:"历年冻土深度最大值中的最大值。"}],pagination:{hide:!0}}}]}]}),j=p({title:"详情",group:[],dialogWidth:"80%"}),k=async()=>{var b,m,g,v,T,D;const{type:l,month:t,year:d}=((b=F.value)==null?void 0:b.queryParams)||{},u=l==="month"?t:l==="year"?d:void 0;if(!a.currentProject||!u)return;n.isRoot=((g=(m=a.currentProject)==null?void 0:m.path)==null?void 0:g.length)===1;const w=await Y({partitionId:(v=a.currentProject)==null?void 0:v.value,ym:u});n.dataForm=((T=w.data)==null?void 0:T.data)||{},n.dataForm.partitionName=(D=a.currentProject)==null?void 0:D.label};return(l,t)=>{const d=J,u=E,w=H,b=U,m=z,g=B;return P(),X(g,null,{tree:f(()=>[i(d,{"tree-data":e(a)},null,8,["tree-data"])]),default:f(()=>[i(u,{ref_key:"refSearch",ref:F,config:e(N)},null,8,["config"]),i(b,{overlay:"",class:"card-table"},{default:f(()=>[r("table",Q,[r("tr",null,[r("td",Z,s(e(n).dataForm.partitionName)+" "+s(e(n).dataForm.ym)+" 水平衡表（立方米） ",1)]),r("tr",null,[r("td",{rowspan:e(n).isRoot?4:8,style:{background:"rgb(9 179 205)"}},[o(s(e(n).isRoot?"自产供水量":"进水量")+" ",1),t[0]||(t[0]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.ownSupplyWater??"-"),1)],8,h),r("td",tt,[t[1]||(t[1]=o(" 供水总量 ")),t[2]||(t[2]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.supplyTotalWater??"-"),1)]),r("td",rt,[t[3]||(t[3]=o(" 注册用水量 ")),t[4]||(t[4]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.useTotalWater??"-"),1)]),r("td",ot,[t[5]||(t[5]=o(" 计费用水量 ")),t[6]||(t[6]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.incomeWater??"-"),1)]),r("td",et,[t[7]||(t[7]=o(" 计费计量用水 ")),t[8]||(t[8]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.feeMeteringUseWater??"-"),1)]),r("td",nt,[t[9]||(t[9]=o(" 收益水量 ")),t[10]||(t[10]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.incomeWater??"-"),1)])]),r("tr",null,[r("td",st,[t[11]||(t[11]=o(" 计费未计量用水 ")),t[12]||(t[12]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.feeNoMeteringUseWater??"-"),1)])]),r("tr",null,[r("td",lt,[t[13]||(t[13]=o(" 免费用水量 ")),t[14]||(t[14]=r("br",null,null,-1)),o(" "+s(Number(e(n).dataForm.freeMeteringUseWater??0)+Number(e(n).dataForm.freeNoMeteringUseWater??0)||"-"),1)]),r("td",at,[t[15]||(t[15]=o(" 免费计量用水 ")),t[16]||(t[16]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.freeMeteringUseWater??"-"),1)]),r("td",it,[t[17]||(t[17]=o(" 未收益水量 ")),t[18]||(t[18]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.noIncomeWater??"-"),1)]),t[19]||(t[19]=r("td",{rowspan:"2",colspan:"3",style:{background:"rgb(0 176 240)","border-bottom":"1px solid rgb(0 176 240)"}},null,-1))]),r("tr",null,[r("td",dt,[t[20]||(t[20]=o(" 免费未计量用水 ")),t[21]||(t[21]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.freeNoMeteringUseWater??"-"),1)])]),r("tr",null,[e(n).isRoot?(P(),G("td",pt,[t[22]||(t[22]=o(" 外购供水量 ")),t[23]||(t[23]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.buySupplyWater??"-"),1)])):$("",!0),r("td",mt,[t[24]||(t[24]=o(" 漏损水量 ")),t[25]||(t[25]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.lossTotalWater??"-"),1)]),r("td",ut,[t[26]||(t[26]=o(" 漏失水量 ")),t[27]||(t[27]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.leakTotalWater??"-"),1)]),r("td",bt,[t[28]||(t[28]=o(" 明漏水量 ")),t[29]||(t[29]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.frontLeakTotalWater??"-"),1)]),r("td",gt,[t[30]||(t[30]=o(" 漏失率 ")),t[31]||(t[31]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.leakRate??"-")+" % ",1)]),r("td",yt,[t[32]||(t[32]=o(" 综合漏损率 ")),t[33]||(t[33]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.lossRate??"-")+" % ",1)]),r("td",ft,[t[34]||(t[34]=o(" 产销差率 ")),t[35]||(t[35]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.nrwRate??"-")+" % ",1)])]),r("tr",null,[r("td",kt,[t[36]||(t[36]=o(" 暗漏水量 ")),t[37]||(t[37]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.backendLeakTotalWater??"-"),1)])]),r("tr",null,[r("td",wt,[t[38]||(t[38]=o(" 背景水量 ")),t[39]||(t[39]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.backgroundLeakWater??"-"),1)])]),r("tr",null,[r("td",Ft,[t[40]||(t[40]=o(" 水箱水池渗漏溢流 ")),t[41]||(t[41]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.shuixiangLeakWater??"-"),1)])]),r("tr",null,[r("td",Wt,[o(s(e(n).isRoot?"趸售水量":"出水量")+" ",1),t[42]||(t[42]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.batchSaleWater??"-"),1)]),r("td",Rt,[t[43]||(t[43]=o(" 计量损失水量 ")),t[44]||(t[44]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.mistakeLossTotalWater??"-"),1)]),r("td",xt,[t[45]||(t[45]=o(" 居民用户表具误差 ")),t[46]||(t[46]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.custMistakeLossWater??"-"),1)]),t[47]||(t[47]=r("td",{rowspan:"4",style:{background:"rgb(146 208 80)","border-right":"1px solid rgb(146 208 80)"}},null,-1))]),r("tr",null,[r("td",Lt,[t[48]||(t[48]=o(" 非居民用户表具误差 ")),t[49]||(t[49]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.nonCustMistakeLossWater??"-"),1)])]),r("tr",null,[r("td",vt,[t[50]||(t[50]=o(" 其他损失水量 ")),t[51]||(t[51]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.otherLossWater??"-"),1)]),r("td",Tt,[t[52]||(t[52]=o(" 未注册用水量 ")),t[53]||(t[53]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.noRegisterLossWater??"-"),1)])]),r("tr",null,[r("td",Dt,[t[54]||(t[54]=o(" 管理因素导致的损失 ")),t[55]||(t[55]=r("br",null,null,-1)),o(" "+s(e(n).dataForm.pipeLossWater??"-"),1)])])]),i(w,{style:{"margin-top":"20px",padding:"10px"},config:e(S)},null,8,["config"])]),_:1}),i(m,{ref_key:"refDialog",ref:L,config:e(_)},null,8,["config"]),i(m,{ref_key:"refDetail",ref:W,config:e(j)},{default:f(()=>[i(K,{ref_key:"refDetailTable",ref:C,partition:e(a).currentProject},null,8,["partition"])]),_:1},8,["config"])]),_:1})}}}),hr=O(Mt,[["__scopeId","data-v-3e480e0c"]]);export{hr as default};
