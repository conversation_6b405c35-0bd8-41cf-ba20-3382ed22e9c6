<template>
  <div class="wrapper"></div>
</template>
<script lang="ts" setup>
import { useHomeBar } from '@/hooks/arcgis'

const view: __esri.MapView | undefined = inject('view')
const home = useHomeBar()
let homeBar: __esri.Home

const overrideGoto = (extent?: any) => {
  homeBar.goToOverride = () => {
    extent && view?.goTo(extent)
  }
}
defineExpose({
  overrideGoto
})
onMounted(() => {
  view?.when(() => {
    homeBar = home.init(view)
  })
})
</script>
<style lang="scss" scoped></style>
