import{g as c}from"./index-BggOjNGp.js";import{a1 as N}from"./index-r0dFAfgr.js";const f="请添加分组";var l=(r=>(r.SHUICHANGE="水厂",r.CHELIUYAZHAN="测流压站",r.<PERSON>EN<PERSON>Z<PERSON>="泵站",r.YALIJIANCEZHAN="压力监测站",r.LIULIANGJIANCEZHAN="流量监测站",r.SHUIZHIJIANCEZHAN="水质监测站",r.SHUIYUANDI="水源地",r.DAYONGHU="大用户",r.WUSHUICHULICHANGE="污水处理厂",r.RESHUIJIN="热水井",r.DIBIAO="地标",r.WENQIANJIUDIAN="温泉酒店",r.LVGUAN="旅馆",r.YIYUAN="医院",r.<PERSON>IC<PERSON>="派出所",r))(l||{});const a={水厂:"#3c8da5",测流压站:"#9b4b62",泵站:"#8f5c3e",压力监测站:"#909c36",流量监测站:"#327c53",水质监测站:"#5f4894",水源地:"#43548f",大用户:"#489785",污水处理厂:"#9e561d",水池监测站:"#f03595",阀门:"#8f5c3e",热水井:"#f56c6c",地标:"#1ECE70",温泉酒店:"#CE1EBC",医院:"#FF0B0B",派出所:"#0B54FF",消防栓:"#F5A623"},s=(r,I)=>{const e=[];for(const A in a)e.push({label:A.toString(),value:A.toString()});return r==="all"&&e.unshift({label:"全部",value:""}),e},o=async r=>{var A;const I=await c(r);return N(((A=I.data)==null?void 0:A.全部)||[],{label:"value",value:"label",id:"label",children:"children"})};export{f as A,l as S,a as c,o as g,s as i};
