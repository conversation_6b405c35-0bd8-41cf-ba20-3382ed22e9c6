<template>
  <div></div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point'

const view = inject('view') as __esri.MapView
const convert = (coords: [number, number]) => {
  if (!coords) return coords
  const point = new Point({ longitude: coords[0], latitude: coords[1], spatialReference: view.spatialReference })
  return [point.x, point.y]
}
defineExpose({
  convert
})
</script>
<style lang="scss" scoped></style>
