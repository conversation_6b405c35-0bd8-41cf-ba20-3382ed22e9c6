<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseDatabaseConnectionMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseDatabaseConnection" id="BaseDatabaseConnectionResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="initScriptDb"    column="init_script_db"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="dbType"    column="db_type"    />
        <result property="dbHost"    column="db_host"    />
        <result property="dbPort"    column="db_port"    />
        <result property="dbName"    column="db_name"    />
        <result property="dbUser"    column="db_user"    />
        <result property="dbPwd"    column="db_pwd"    />
    </resultMap>

    <sql id="selectBaseDatabaseConnectionVo">
        select id, name, description, init_script_db, created_at, updated_at, db_type, db_host, db_port, db_name, db_user, db_pwd from base_database_connection
    </sql>

    <select id="selectBaseDatabaseConnectionList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseConnection" resultMap="BaseDatabaseConnectionResult">
        <include refid="selectBaseDatabaseConnectionVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="initScriptDb != null  and initScriptDb != ''"> and init_script_db = #{initScriptDb}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
            <if test="dbType != null  and dbType != ''"> and db_type = #{dbType}</if>
            <if test="dbHost != null  and dbHost != ''"> and db_host = #{dbHost}</if>
            <if test="dbPort != null  and dbPort != ''"> and db_port = #{dbPort}</if>
            <if test="dbName != null  and dbName != ''"> and db_name like concat('%', #{dbName}, '%')</if>
            <if test="dbUser != null  and dbUser != ''"> and db_user = #{dbUser}</if>
            <if test="dbPwd != null  and dbPwd != ''"> and db_pwd = #{dbPwd}</if>
        </where>
        order by created_at desc
    </select>
    
    <select id="selectBaseDatabaseConnectionById" parameterType="String" resultMap="BaseDatabaseConnectionResult">
        <include refid="selectBaseDatabaseConnectionVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseDatabaseConnection" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseConnection">
        insert into base_database_connection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="initScriptDb != null">init_script_db,</if>
            <if test="dbType != null">db_type,</if>
            <if test="dbHost != null">db_host,</if>
            <if test="dbPort != null">db_port,</if>
            <if test="dbName != null">db_name,</if>
            <if test="dbUser != null">db_user,</if>
            <if test="dbPwd != null">db_pwd,</if>
            created_at,
            updated_at
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="initScriptDb != null">#{initScriptDb},</if>
            <if test="dbType != null">#{dbType},</if>
            <if test="dbHost != null">#{dbHost},</if>
            <if test="dbPort != null">#{dbPort},</if>
            <if test="dbName != null">#{dbName},</if>
            <if test="dbUser != null">#{dbUser},</if>
            <if test="dbPwd != null">#{dbPwd},</if>
            NOW(),
            NOW()
         </trim>
    </insert>

    <update id="updateBaseDatabaseConnection" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseConnection">
        update base_database_connection
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="initScriptDb != null">init_script_db = #{initScriptDb},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            updated_at = NOW(),
            <if test="dbType != null">db_type = #{dbType},</if>
            <if test="dbHost != null">db_host = #{dbHost},</if>
            <if test="dbPort != null">db_port = #{dbPort},</if>
            <if test="dbName != null">db_name = #{dbName},</if>
            <if test="dbUser != null">db_user = #{dbUser},</if>
            <if test="dbPwd != null">db_pwd = #{dbPwd},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseDatabaseConnectionById" parameterType="String">
        delete from base_database_connection where id = #{id}
    </delete>

    <delete id="deleteBaseDatabaseConnectionByIds" parameterType="String">
        delete from base_database_connection where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>