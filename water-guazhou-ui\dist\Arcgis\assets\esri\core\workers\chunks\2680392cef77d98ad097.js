"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[7845],{66643:(e,t,n)=>{n.d(t,{Ed:()=>u,UI:()=>c,mt:()=>h,q6:()=>y,vr:()=>f});var s=n(43697),r=n(15923),i=n(70586),o=n(95330),a=n(5600),l=(n(75215),n(67676),n(52011));function u(e,t,n){return(0,o.as)(e.map(((e,s)=>t.apply(n,[e,s]))))}async function c(e,t,n){return(await(0,o.as)(e.map(((e,s)=>t.apply(n,[e,s]))))).map((e=>e.value))}function p(e){return{ok:!0,value:e}}function d(e){return{ok:!1,error:e}}async function y(e){if((0,i.Wi)(e))return{ok:!1,error:new Error("no promise provided")};try{return p(await e)}catch(e){return d(e)}}async function h(e){try{return p(await e)}catch(e){return(0,o.r9)(e),d(e)}}function f(e,t){return new m(e,t)}let m=class extends r.Z{get value(){return e=this._result,(0,i.pC)(e)&&!0===e.ok?e.value:null;var e}get error(){return e=this._result,(0,i.pC)(e)&&!1===e.ok?e.error:null;var e}get finished(){return(0,i.pC)(this._result)}constructor(e,t){super({}),this._result=null,this._abortHandle=null,this.abort=()=>{this._abortController=(0,i.IM)(this._abortController)},this.remove=this.abort,this._abortController=new AbortController;const{signal:n}=this._abortController;this.promise=e(n),this.promise.then((e=>{this._result=p(e),this._cleanup()}),(e=>{this._result=d(e),this._cleanup()})),this._abortHandle=(0,o.fu)(t,this.abort)}normalizeCtorArgs(){return{}}destroy(){this.abort()}_cleanup(){this._abortHandle=(0,i.hw)(this._abortHandle),this._abortController=null}};(0,s._)([(0,a.Cb)()],m.prototype,"value",null),(0,s._)([(0,a.Cb)()],m.prototype,"error",null),(0,s._)([(0,a.Cb)()],m.prototype,"finished",null),(0,s._)([(0,a.Cb)()],m.prototype,"promise",void 0),(0,s._)([(0,a.Cb)()],m.prototype,"_result",void 0),m=(0,s._)([(0,l.j)("esri.core.asyncUtils.ReactiveTask")],m)},97845:(e,t,n)=>{n.r(t),n.d(t,{default:()=>F});var s=n(3172),r=n(66643),i=n(20102),o=n(92604),a=n(70586),l=n(95330),u=n(33955),c=n(8744),p=n(98732),d=n(57191),y=n(37427),h=n(50245),f=n(92722),m=n(25278),g=n(23095),_=n(99514),b=n(86719),S=n(35671);const w={hasAttachments:!1,capabilities:"query, editing, create, delete, update",useStandardizedQueries:!0,supportsCoordinatesQuantization:!0,supportsReturningQueryGeometry:!0,advancedQueryCapabilities:{supportsQueryAttachments:!1,supportsStatistics:!0,supportsPercentileStatistics:!0,supportsReturningGeometryCentroid:!0,supportsQueryWithDistance:!0,supportsDistinct:!0,supportsReturningQueryExtent:!0,supportsReturningGeometryProperties:!1,supportsHavingClause:!0,supportsOrderBy:!0,supportsPagination:!0,supportsQueryWithResultType:!1,supportsSqlExpression:!0,supportsDisjointSpatialRel:!0}};class F{constructor(){this._queryEngine=null,this._snapshotFeatures=async e=>{const t=await this._fetch(e);return this._createFeatures(t)}}destroy(){this._queryEngine?.destroy(),this._queryEngine=this._fieldsIndex=this._createDefaultAttributes=null}async load(e,t={}){this._loadOptions={url:e.url,customParameters:e.customParameters};const n=[];await this._checkProjection(e.spatialReference);let s=null;e.url&&(s=await this._fetch(t?.signal));const r=(0,f.my)(s,{geometryType:e.geometryType}),o=e.fields||r.fields||[],a=null!=e.hasZ?e.hasZ:r.hasZ,l=r.geometryType;let u=e.objectIdField||r.objectIdFieldName||"__OBJECTID";const p=e.spatialReference||c.Zn;let y=e.timeInfo;o===r.fields&&r.unknownFields.length>0&&n.push({name:"geojson-layer:unknown-field-types",message:"Some fields types couldn't be inferred from the features and were dropped",details:{unknownFields:r.unknownFields}});let g=new _.Z(o).get(u);g?("esriFieldTypeString"!==g.type&&(g.type="esriFieldTypeOID"),g.editable=!1,g.nullable=!1,u=g.name):(g={alias:u,name:u,type:"string"===r.objectIdFieldType?"esriFieldTypeString":"esriFieldTypeOID",editable:!1,nullable:!1},o.unshift(g));const F={};for(const e of o){if(null==e.name&&(e.name=e.alias),null==e.alias&&(e.alias=e.name),!e.name)throw new i.Z("geojson-layer:invalid-field-name","field name is missing",{field:e});if(!b.v.jsonValues.includes(e.type))throw new i.Z("geojson-layer:invalid-field-type",`invalid type for field "${e.name}"`,{field:e});if(e.name!==g.name){const t=(0,S.os)(e);void 0!==t&&(F[e.name]=t)}}this._fieldsIndex=new _.Z(o);const I=this._fieldsIndex.requiredFields.indexOf(g);if(I>-1&&this._fieldsIndex.requiredFields.splice(I,1),y){if(y.startTimeField){const e=this._fieldsIndex.get(y.startTimeField);e?(y.startTimeField=e.name,e.type="esriFieldTypeDate"):y.startTimeField=null}if(y.endTimeField){const e=this._fieldsIndex.get(y.endTimeField);e?(y.endTimeField=e.name,e.type="esriFieldTypeDate"):y.endTimeField=null}if(y.trackIdField){const e=this._fieldsIndex.get(y.trackIdField);e?y.trackIdField=e.name:(y.trackIdField=null,n.push({name:"geojson-layer:invalid-timeInfo-trackIdField",message:"trackIdField is missing",details:{timeInfo:y}}))}y.startTimeField||y.endTimeField||(n.push({name:"geojson-layer:invalid-timeInfo",message:"startTimeField and endTimeField are missing",details:{timeInfo:y}}),y=null)}const E=l?(0,m.bU)(l):void 0,T={warnings:n,featureErrors:[],layerDefinition:{...w,drawingInfo:E??void 0,templates:(0,m.Hq)(F),extent:void 0,geometryType:l,objectIdField:u,fields:o,hasZ:!!a,timeInfo:y}};this._queryEngine=new h.q({fields:o,geometryType:l,hasM:!1,hasZ:a,objectIdField:u,spatialReference:p,timeInfo:y,featureStore:new d.Z({geometryType:l,hasM:!1,hasZ:a}),cacheSpatialQueries:!0}),this._createDefaultAttributes=(0,m.Dm)(F,u);const j=await this._createFeatures(s);this._objectIdGenerator=this._createObjectIdGenerator(this._queryEngine,j);const q=this._normalizeFeatures(j,T.warnings,T.featureErrors);this._queryEngine.featureStore.addMany(q);const{fullExtent:C,timeExtent:x}=await this._queryEngine.fetchRecomputedExtents();if(T.layerDefinition.extent=C,x){const{start:e,end:t}=x;T.layerDefinition.timeInfo.timeExtent=[e,t]}return T}async applyEdits(e){const{spatialReference:t,geometryType:n}=this._queryEngine;return await Promise.all([(0,g.b)(t,n),(0,y._W)(e.adds,t),(0,y._W)(e.updates,t)]),await this._waitSnapshotComplete(),this._applyEdits(e)}async queryFeatures(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQuery(e,t.signal)}async queryFeatureCount(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForCount(e,t.signal)}async queryObjectIds(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForIds(e,t.signal)}async queryExtent(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForExtent(e,t.signal)}async querySnapping(e,t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForSnapping(e,t.signal)}async refresh(e){this._loadOptions.customParameters=e,this._snapshotTask?.abort(),this._snapshotTask=(0,r.vr)(this._snapshotFeatures),this._snapshotTask.promise.then((e=>{this._queryEngine.featureStore.clear(),this._objectIdGenerator=this._createObjectIdGenerator(this._queryEngine,e);const t=this._normalizeFeatures(e);t&&this._queryEngine.featureStore.addMany(t)}),(e=>{this._queryEngine.featureStore.clear(),(0,l.D_)(e)||o.Z.getLogger("esri.layers.GeoJSONLayer").error(new i.Z("geojson-layer:refresh","An error occurred during refresh",{error:e}))})),await this._waitSnapshotComplete();const{fullExtent:t,timeExtent:n}=await this._queryEngine.fetchRecomputedExtents();return{extent:t,timeExtent:n}}async _createFeatures(e){if(null==e)return[];const{geometryType:t,hasZ:n,objectIdField:s}=this._queryEngine,r=(0,f.lG)(e,{geometryType:t,hasZ:n,objectIdField:s});if(!(0,c.fS)(this._queryEngine.spatialReference,c.Zn))for(const e of r)(0,a.pC)(e.geometry)&&(e.geometry=(0,p.GH)((0,y.iV)((0,p.di)(e.geometry,this._queryEngine.geometryType,this._queryEngine.hasZ,!1),c.Zn,this._queryEngine.spatialReference)));return r}async _waitSnapshotComplete(){if(this._snapshotTask&&!this._snapshotTask.finished){try{await this._snapshotTask.promise}catch{}return this._waitSnapshotComplete()}}async _fetch(e){const{url:t,customParameters:n}=this._loadOptions,r=(await(0,s.default)(t,{responseType:"json",query:{...n},signal:e})).data;return await(0,f.O3)(r),r}_normalizeFeatures(e,t,n){const{objectIdField:s}=this._queryEngine,r=[];for(const i of e){const e=this._createDefaultAttributes(),o=(0,g.O0)(this._fieldsIndex,e,i.attributes,!0,t);o?n?.push(o):(this._assignObjectId(e,i.attributes,!0),i.attributes=e,i.objectId=e[s],r.push(i))}return r}async _applyEdits(e){const{adds:t,updates:n,deletes:s}=e,r={addResults:[],deleteResults:[],updateResults:[],uidToObjectId:{}};if(t&&t.length&&this._applyAddEdits(r,t),n&&n.length&&this._applyUpdateEdits(r,n),s&&s.length){for(const e of s)r.deleteResults.push((0,g.d1)(e));this._queryEngine.featureStore.removeManyById(s)}const{fullExtent:i,timeExtent:o}=await this._queryEngine.fetchRecomputedExtents();return{extent:i,timeExtent:o,featureEditResults:r}}_applyAddEdits(e,t){const{addResults:n}=e,{geometryType:s,hasM:r,hasZ:i,objectIdField:o,spatialReference:l,featureStore:c}=this._queryEngine,d=[];for(const r of t){if(r.geometry&&s!==(0,u.Ji)(r.geometry)){n.push((0,g.av)("Incorrect geometry type."));continue}const t=this._createDefaultAttributes(),i=(0,g.O0)(this._fieldsIndex,t,r.attributes);if(i)n.push(i);else{if(this._assignObjectId(t,r.attributes),r.attributes=t,null!=r.uid){const t=r.attributes[o];e.uidToObjectId[r.uid]=t}if((0,a.pC)(r.geometry)){const e=r.geometry.spatialReference??l;r.geometry=(0,y.iV)((0,g.og)(r.geometry,e),e,l)}d.push(r),n.push((0,g.d1)(r.attributes[o]))}}c.addMany((0,p.Yn)([],d,s,i,r,o))}_applyUpdateEdits({updateResults:e},t){const{geometryType:n,hasM:s,hasZ:r,objectIdField:i,spatialReference:o,featureStore:l}=this._queryEngine;for(const c of t){const{attributes:t,geometry:d}=c,h=t&&t[i];if(null==h){e.push((0,g.av)(`Identifier field ${i} missing`));continue}if(!l.has(h)){e.push((0,g.av)(`Feature with object id ${h} missing`));continue}const f=(0,p.EI)(l.getFeature(h),n,r,s);if((0,a.pC)(d)){if(n!==(0,u.Ji)(d)){e.push((0,g.av)("Incorrect geometry type."));continue}const t=d.spatialReference??o;f.geometry=(0,y.iV)((0,g.og)(d,t),t,o)}if(t){const n=(0,g.O0)(this._fieldsIndex,f.attributes,t);if(n){e.push(n);continue}}l.add((0,p.XA)(f,n,r,s,i)),e.push((0,g.d1)(h))}}_createObjectIdGenerator(e,t){const n=e.fieldsIndex.get(e.objectIdField);if("esriFieldTypeString"===n.type)return()=>n.name+"-"+Date.now().toString(16);let s=Number.NEGATIVE_INFINITY;for(const e of t)e.objectId&&(s=Math.max(s,e.objectId));return s=Math.max(0,s)+1,()=>s++}_assignObjectId(e,t,n=!1){const s=this._queryEngine.objectIdField;e[s]=n&&s in t?t[s]:this._objectIdGenerator()}async _checkProjection(e){try{await(0,y._W)(c.Zn,e)}catch{throw new i.Z("geojson-layer","Projection not supported")}}}},92722:(e,t,n)=>{n.d(t,{O3:()=>w,lG:()=>I,my:()=>F,q9:()=>l});var s=n(20102),r=n(70272),i=n(5428),o=n(35671);const a={LineString:"esriGeometryPolyline",MultiLineString:"esriGeometryPolyline",MultiPoint:"esriGeometryMultipoint",Point:"esriGeometryPoint",Polygon:"esriGeometryPolygon",MultiPolygon:"esriGeometryPolygon"};function l(e){return a[e]}function*u(e){switch(e.type){case"Feature":yield e;break;case"FeatureCollection":for(const t of e.features)t&&(yield t)}}function*c(e){if(e)switch(e.type){case"Point":yield e.coordinates;break;case"LineString":case"MultiPoint":yield*e.coordinates;break;case"MultiLineString":case"Polygon":for(const t of e.coordinates)yield*t;break;case"MultiPolygon":for(const t of e.coordinates)for(const e of t)yield*e}}function p(e){for(const t of e)if(t.length>2)return!0;return!1}function d(e){let t=0;for(let n=0;n<e.length;n++){const s=e[n],r=e[(n+1)%e.length];t+=s[0]*r[1]-r[0]*s[1]}return t<=0}function y(e){const t=e[0],n=e[e.length-1];return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]||e.push(t),e}function h(e,t,n){switch(t.type){case"LineString":case"MultiPoint":return function(e,t,n){return g(e,t.coordinates,n),e}(e,t,n);case"MultiLineString":return function(e,t,n){for(const s of t.coordinates)g(e,s,n);return e}(e,t,n);case"MultiPolygon":return function(e,t,n){for(const s of t.coordinates){f(e,s[0],n);for(let t=1;t<s.length;t++)m(e,s[t],n)}return e}(e,t,n);case"Point":return function(e,t,n){return b(e,t.coordinates,n),e}(e,t,n);case"Polygon":return function(e,t,n){const s=t.coordinates;f(e,s[0],n);for(let t=1;t<s.length;t++)m(e,s[t],n);return e}(e,t,n)}}function f(e,t,n){const s=y(t);!function(e){return!d(e)}(s)?g(e,s,n):_(e,s,n)}function m(e,t,n){const s=y(t);!function(e){return d(e)}(s)?g(e,s,n):_(e,s,n)}function g(e,t,n){for(const s of t)b(e,s,n);e.lengths.push(t.length)}function _(e,t,n){for(let s=t.length-1;s>=0;s--)b(e,t[s],n);e.lengths.push(t.length)}function b(e,t,n){const[s,r,i]=t;e.coords.push(s,r),n.hasZ&&e.coords.push(i||0)}function S(e){switch(typeof e){case"string":return"esriFieldTypeString";case"number":return"esriFieldTypeDouble";default:return"unknown"}}function w(e){if(!e)throw new s.Z("geojson-layer:empty","GeoJSON data is empty");if("Feature"!==e.type&&"FeatureCollection"!==e.type)throw new s.Z("geojson-layer:unsupported-geojson-object","missing or not supported GeoJSON object type",{data:e});const{crs:t}=e;if(!t)return;const n="string"==typeof t?t:"name"===t.type?t.properties.name:"EPSG"===t.type?t.properties.code:null,r=new RegExp(".*(CRS84H?|4326)$","i");if(!n||!r.test(n))throw new s.Z("geojson-layer:unsupported-crs","unsupported GeoJSON 'crs' member",{crs:t})}function F(e,t={}){const n=[],s=new Set,r=new Set;let i,a=!1,d=null,y=!1,{geometryType:h=null}=t,f=!1;for(const t of u(e)){const{geometry:e,properties:u,id:m}=t;if((!e||(h||(h=l(e.type)),l(e.type)===h))&&(a||(a=p(c(e))),y||(y=null!=m,y&&(i=typeof m,u&&(d=Object.keys(u).filter((e=>u[e]===m))))),u&&d&&y&&null!=m&&(d.length>1?d=d.filter((e=>u[e]===m)):1===d.length&&(d=u[d[0]]===m?d:[])),!f&&u)){let e=!0;for(const t in u){if(s.has(t))continue;const i=u[t];if(null==i){e=!1,r.add(t);continue}const a=S(i);if("unknown"===a){r.add(t);continue}r.delete(t),s.add(t);const l=(0,o.q6)(t);l&&n.push({name:l,alias:t,type:a})}f=e}}const m=(0,o.q6)(1===d?.length&&d[0]||null)??void 0;if(m)for(const e of n)if(e.name===m&&(0,o.H7)(e)){e.type="esriFieldTypeOID";break}return{fields:n,geometryType:h,hasZ:a,objectIdFieldName:m,objectIdFieldType:i,unknownFields:Array.from(r)}}function I(e,t){return Array.from(function*(e,t={}){const{geometryType:n,objectIdField:s}=t;for(const o of e){const{geometry:e,properties:a,id:u}=o;if(e&&l(e.type)!==n)continue;const c=a||{};let p;s&&(p=c[s],null==u||p||(c[s]=p=u));const d=new r.u_(e?h(new i.Z,e,t):null,c,null,p??void 0);yield d}}(u(e),t))}},25278:(e,t,n)=>{n.d(t,{Dm:()=>c,Hq:()=>p,MS:()=>d,bU:()=>a});var s=n(80442),r=n(22974),i=n(61159),o=n(58333);function a(e){return{renderer:{type:"simple",symbol:"esriGeometryPoint"===e||"esriGeometryMultipoint"===e?o.I4:"esriGeometryPolyline"===e?o.ET:o.lF}}}const l=/^[_$a-zA-Z][_$a-zA-Z0-9]*$/;let u=1;function c(e,t){if((0,s.Z)("esri-csp-restrictions"))return()=>({[t]:null,...e});try{let n=`this.${t} = null;`;for(const t in e)n+=`this${l.test(t)?`.${t}`:`["${t}"]`} = ${JSON.stringify(e[t])};`;const s=new Function(`\n      return class AttributesClass$${u++} {\n        constructor() {\n          ${n};\n        }\n      }\n    `)();return()=>new s}catch(n){return()=>({[t]:null,...e})}}function p(e={}){return[{name:"New Feature",description:"",prototype:{attributes:(0,r.d9)(e)}}]}function d(e,t){return{analytics:{supportsCacheHint:!1},attachment:null,data:{isVersioned:!1,supportsAttachment:!1,supportsM:!1,supportsZ:e},metadata:{supportsAdvancedFieldProperties:!1},operations:{supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsAdd:t,supportsDelete:t,supportsEditing:t,supportsChangeTracking:!1,supportsQuery:!0,supportsQueryAnalytics:!1,supportsQueryAttachments:!1,supportsQueryTopFeatures:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsUpdate:t,supportsExceedsLimitStatistics:!0},query:i.g,queryRelated:{supportsCount:!0,supportsOrderBy:!0,supportsPagination:!0,supportsCacheHint:!1},queryTopFeatures:{supportsCacheHint:!1},editing:{supportsGeometryUpdate:t,supportsGlobalId:!1,supportsReturnServiceEditsInSourceSpatialReference:!1,supportsRollbackOnFailure:!1,supportsUpdateWithoutM:!1,supportsUploadWithItemId:!1,supportsDeleteByAnonymous:!1,supportsDeleteByOthers:!1,supportsUpdateByAnonymous:!1,supportsUpdateByOthers:!1}}}},58333:(e,t,n)=>{n.d(t,{ET:()=>i,I4:()=>r,eG:()=>l,lF:()=>o,lj:()=>c,qP:()=>a,wW:()=>u});const s=[252,146,31,255],r={type:"esriSMS",style:"esriSMSCircle",size:6,color:s,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},i={type:"esriSLS",style:"esriSLSSolid",width:.75,color:s},o={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},a={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},l={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},u={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},c={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);