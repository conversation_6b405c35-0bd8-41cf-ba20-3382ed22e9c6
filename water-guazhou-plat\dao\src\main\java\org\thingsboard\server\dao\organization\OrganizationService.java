package org.thingsboard.server.dao.organization;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.util.imodel.query.organization.OrganizationPageRequest;
import org.thingsboard.server.dao.util.imodel.query.organization.OrganizationSaveRequest;

import java.util.List;

public interface OrganizationService {
    /**
     * 分页条件查询单位
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    List<Organization> findAllConditional(OrganizationPageRequest request);

    List<Organization> findAllStructure(Integer depth, String tenantId);

    /**
     * 保存单位
     *
     * @param request 实体信息
     * @return 保存好的实体
     */
    Organization save(OrganizationSaveRequest request);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);


    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(Organization entity);

    /**
     * 是否允许删除
     *
     * @param id 唯一标识
     * @return 是否允许删除
     */
    boolean canBeDelete(String id);

    /**
     * 是否允许添加
     *
     * @param parentId 父级唯一标识
     * @return 是否允许删除
     */
    boolean canBeAdd(String parentId);

    /**
     * 初始化根单位，在客户被创建时调用
     *
     * @param name     单位名称
     * @param type     单位类型
     * @param phone    单位联系方式
     * @param tenantId 单位id
     * @return 保存好的单位
     */
    Organization initRootOrganization(String name, String type, String phone, String tenantId);

    /**
     * 所有和单位相关联的数据，在单位被移除时移除
     *
     * @param tenantId 客户id
     * @return 是否成功
     */
    boolean removeRootOrganization(String tenantId);

    /**
     * 查询指定ID下的所有子节点ID
     *
     * @param pid 父节点ID
     * @return 数据
     */
    List<String> findAllChildrenByParentId(String pid, TenantId tenantId);
}
