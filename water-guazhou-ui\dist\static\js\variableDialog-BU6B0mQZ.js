import{_ as w}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{C as O,M as L,c as _,r as I,S as M,b as S,ak as P,e3 as B,d6 as E,bM as F,ay as R,g,n as k,q as i,F as s,p as m,G as d,aB as T,aJ as V,h as v,an as A,J as K,N as J,H as Z,e4 as j,aK as G,aL as Q,O as H,L as W}from"./index-r0dFAfgr.js";import{s as X}from"./index-BggOjNGp.js";import{c as Y}from"./formValidate-U0WTqY4Y.js";import $ from"./KeyValueTable-yppKjcF3.js";const ee={statType:{1:"累计值",0:"瞬时值"},dataType:{char:"8位有符号数",byte:"8位无符号数",short:"16位有符号数",word:"16位无符号数",DINT:"32位有符号数",Dword:"32位无符号数",float:"单精度浮点数",REAL:"双精度浮点数",String:"String字符串",Long:"Long",Boolean:"Boolean",hlong:"有符号Long",ulong:"无符号Long",part:"24位特殊读取",bit:"Bit"},propertyType:{1:"只读量",2:"只写量",3:"读写量",4:"开关量"},registerType:{1:"位寄存器",0:"普通寄存器"},registerSignFlag:{1:"有符号",0:"无符号"},order:{big:"大端在前",little:"小端在前"}},{$message:b}=L(),te={name:"VariableDialog",components:{KeyValueTable:$},props:["varDialogInfo"],emits:["getTemlate"],setup(){const t=_(),e=_(),n=_(),o=_(-1),a=_(["display"]),l=I({title:"配置字典",dialogWidth:600,group:[],submit:()=>{var C;((C=e.value)==null?void 0:C.map(f=>{var D;return(D=f.save)==null?void 0:D.call(f)})).some(f=>f===!0)?M("当前配置存在重项，确定继续吗？","提示信息").then(()=>{t.value.closeDialog(),S.success("配置成功，保存后生效")}).catch(()=>{}):(t.value.closeDialog(),S.success("配置成功，保存后生效"))}});return{Plus:P,CopyDocument:B,Close:E,Edit:F,refKeyValueDialog:t,refKeyValueTable:e,DialogFormConfig:l,configFields:a,currentRow:n,handleOperate:y=>{console.log(y),n.value=y.row,o.value=y.$index,t.value.openDialog()}}},data(){return{NBMQTT:["name","propertyCategory","statType","propertyType","unit","sampleDeviation","dataOffset","samplingMax","samplingMin","sampleCoef","unitCoef","range","formulaProperty","group"],MQTT:["name","propertyCategory","statType","propertyType","unit","controlOptions","sampleDeviation","dataOffset","samplingMax","samplingMin","sampleCoef","unitCoef","range","formulaProperty","group"],MODBUS:["name","propertyCategory","statType","dataType","propertyType","unit","registerType","functionCode","registerAddress","byteCount","bitPosition","registerSignFlag","sampleDeviation","order","byteOrder","dataOffset","samplingMax","samplingMin","sampleCoef","unitCoef","range","formulaProperty","group"],DTU:["name","propertyCategory","statType","dataType","propertyType","unit","controlOptions","registerType","functionCode","registerAddress","byteCount","bitPosition","registerSignFlag","sampleDeviation","order","byteOrder","dataOffset","samplingMax","samplingMin","sampleCoef","unitCoef","range","formulaProperty","group"],NBDTU:["name","propertyCategory","statType","dataType","propertyType","unit","registerType","functionCode","registerAddress","byteCount","bitPosition","registerSignFlag","sampleDeviation","order","byteOrder","dataOffset","samplingMax","samplingMin","sampleCoef","unitCoef","range","formulaProperty","group"],protocolType:[],columnData:[],protocolData:[],saveDis:!1,formData:{number:{type:"number",value:0,i18n:{en_US:"number",zh_CN:"排列序号"},precision:0,min:-1,hasValue:!1},name:{type:"string",value:"",maxLength:-1,i18n:{en_US:"name",zh_CN:"变量名称"},required:!0,hasValue:!1,rule:[{required:!0,message:"请输入 变量名称",trigger:"blur"},{max:20,message:"名称长度不超过20",trigger:"blur"},{validator:(n,o,a)=>{/^[a-zA-Z0-9_一-龥]+$/g.test(o)?a():a(new Error("可输入中文、英文、数字、下划线字符"))},trigger:"blur"}]},propertyCategory:{type:"string",value:"",maxLength:-1,i18n:{en_US:"property category",zh_CN:"实际变量名称(非中文)"},required:!0,unique:!0,hasValue:!1,rule:[{required:!0,message:"请输入 实际变量名称(非中文)",trigger:"blur"},{max:20,message:"字符长度不超过20",trigger:"blur"},{validator:(n,o,a)=>{/^[5A-Za-z0-9_]+$/.test(o)?a():a(new Error("只能输入英文,数字,下划线字符"))},trigger:"blur"}]},statType:{type:"select",value:"",i18n:{en_US:"stat type",zh_CN:"统计类型"},required:!0,options:[{key:1,value:"累计值"},{key:0,value:"瞬时值"}],hasValue:!1,rule:[{required:!0,message:"请选择 统计类型",trigger:"change"}]},dataType:{type:"select",value:"",i18n:{en_US:"data type",zh_CN:"数据类型"},required:!0,options:[{key:"char",value:"8位有符号数"},{key:"byte",value:"8位无符号数"},{key:"short",value:"16位有符号数"},{key:"word",value:"16位无符号数"},{key:"DINT",value:"32位有符号数"},{key:"Dword",value:"32位无符号数"},{key:"float",value:"单精度浮点数"},{key:"REAL",value:"双精度浮点数"},{key:"String",value:"String字符串"},{key:"Long",value:"Long"},{key:"Boolean",value:"Boolean"},{key:"hlong",value:"有符号Long"},{key:"ulong",value:"无符号Long"},{key:"part",value:"24位特殊读取"},{key:"bit",value:"Bit"}],hasValue:!1,rule:[{required:!0,message:"请选择 数据类型",trigger:"change"}]},propertyType:{type:"select",value:"",i18n:{en_US:"property type",zh_CN:"变量类型"},required:!0,options:this.formatPropertyTypeOption(ee.propertyType),hasValue:!1,rule:[{required:!0,message:"请选择 变量类型",trigger:"change"}]},unit:{type:"string",value:"",maxLength:10,i18n:{en_US:"unit",zh_CN:"单位"},required:!0,hasValue:!1,rule:[{required:!0,message:"请输入 单位",trigger:"blur"},{max:10,message:"字符长度不超过10",trigger:"blur"}]},controlOptions:{type:"string",value:"",i18n:{en_US:"controlOptions",zh_CN:"控制项"},disabled:!1,hasValue:!1},registerType:{type:"select",value:"",i18n:{en_US:"register type",zh_CN:"寄存器类型"},required:!0,options:[{key:0,value:"普通寄存器"},{key:1,value:"位寄存器"}],hasValue:!1,rule:[{required:!0,message:"请选择 寄存器类型",trigger:"change"}],change:()=>{console.log(this.formData.bitPosition,"bitPosition")}},functionCode:{type:"number",value:"",i18n:{en_US:"function code",zh_CN:"功能码"},required:!0,precision:0,min:1,hasValue:!1,rule:[{required:!0,message:"请输入 功能码",trigger:"blur"}]},registerAddress:{type:"number",value:"",i18n:{en_US:"register address",zh_CN:"寄存器地址"},required:!0,precision:0,min:0,hasValue:!1,rule:[{required:!0,message:"请输入 寄存器地址",trigger:"blur"}]},byteCount:{type:"number",value:"",i18n:{en_US:"register count",zh_CN:"读取寄存器个数"},required:!0,precision:0,min:1,hasValue:!1,rule:[{required:!0,message:"请输入 读取寄存器个数",trigger:"blur"}]},bitPosition:{type:"number",value:"",i18n:{en_US:"bit position",zh_CN:"位寄存器读取地址"},needed:{key:"registerType",value:"1"},hasValue:!1},registerSignFlag:{type:"select",value:"",i18n:{en_US:"register sign flag",zh_CN:"数据有无符号"},required:!0,options:[{key:0,value:"无符号"},{key:1,value:"有符号"}],hasValue:!1,rule:[{required:!0,message:"请选择 数据有无符号",trigger:"change"}]},sampleDeviation:{type:"number",value:"",i18n:{en_US:"sample deviation",zh_CN:"每小时数据偏差值"},required:!0,precision:0,hasValue:!1,rule:[{required:!0,message:"请输入 每小时数据偏差值",trigger:"blur"}]},order:{type:"select",value:"",i18n:{en_US:"order",zh_CN:"大小端"},required:!0,options:[{key:"big",value:"大端在前"},{key:"little",value:"小端在前"}],hasValue:!1,rule:[{required:!0,message:"请选择 大小端",trigger:"change"}]},byteOrder:{type:"string",value:"",maxLength:-1,i18n:{en_US:"byte order",zh_CN:"解析顺序"},required:!0,hasValue:!1,rule:[{required:!0,message:"请输入 解析顺序",trigger:"blur"}]},dataOffset:{type:"number",value:"",i18n:{en_US:"data offset",zh_CN:"数据偏移量"},required:!0,precision:0,hasValue:!1,rule:[{required:!0,message:"请输入 数据偏移量",trigger:"blur"}]},samplingMax:{type:"number",value:"",i18n:{en_US:"sampling max",zh_CN:"采样最大值"},required:!0,precision:0,hasValue:!1,rule:[{required:!0,message:"请输入 采样最大值",trigger:"blur"}]},samplingMin:{type:"number",value:"",i18n:{en_US:"sampling min",zh_CN:"采样最小值"},required:!0,precision:0,hasValue:!1,rule:[{required:!0,message:"请输入 采样最小值",trigger:"blur"}]},sampleCoef:{type:"number",value:1,i18n:{en_US:"sample coef",zh_CN:"采样系数"},required:!0,precision:0,hasValue:!1,min:0,rule:[{required:!0,message:"请输入 采样系数",trigger:"blur"}]},unitCoef:{type:"number",value:-1,i18n:{en_US:"unit coef",zh_CN:"数据小数位"},required:!0,precision:0,min:-1,hasValue:!1,rule:[{required:!0,message:"请输入 数据小数位",trigger:"blur"}]},range:{type:"number",value:"",i18n:{en_US:"range",zh_CN:"量程"},required:!1,precision:0},formulaProperty:{type:"string",value:"",maxLength:-1,i18n:{en_US:"formula property",zh_CN:"公式"},required:!1},group:{type:"string",value:"",maxLength:-1,i18n:{en_US:"group",zh_CN:"设备"},required:!1}},currencyRules:Y,protocolRules:{}}},computed:{visible(){return this.varDialogInfo.visible}},created(){this.protocolType=this[this.varDialogInfo.template.type],this.protocolType||(this.protocolType=[]);for(const t of this.protocolType){const e=this.formData[t];e.key=t,this.protocolRules[t]=e.rule,this.columnData.push(e)}if(this.protocolData=[],this.varDialogInfo.protocolList&&this.varDialogInfo.protocolList.length>0)for(const t of this.varDialogInfo.protocolList)this.protocolData.push(t);else this.addVariable();console.log(this.varDialogInfo),console.log(this.columnData,this.protocolRules,"columnData  this.protocolRules")},methods:{handleSave(t,e){console.log(t,e),this.currentRow[t]=e,console.log(this.protocolData)},handleDisable(t,e){switch(t){case"bitPosition":return e.registerType===0||e.registerType==="0"}},formatPropertyTypeOption(t){const e=[];if(!t)return e;for(const n in t){const o={key:Number(n),value:t[n]};e.push(o)}return e},addVariable(){const t={};for(const e of this.protocolType)t[e]=null;t.unitCoef=-1,t.sampleCoef=1,this.protocolData.push(t)},handleCopy(t){console.log("复制",t);const e=JSON.parse(JSON.stringify(t.row));this.protocolData.push(e),b.success("成功：已复制添加到最后一行")},haneleDelete(t){this.protocolData.splice(t.$index,1),b.success("删除当前行成功")},save(){const t=this.varDialogInfo.template;this.saveDis=!0;const e={},n={},o=this.protocolData;for(const a of o){const l=[];for(const u of this.columnData)l.push(u.key);if(a.name!==""&&a.propertyCategory!==""){if(console.log(a.propertyCategory,"value.propertyCategory"),!/^[5A-Za-z0-9_#]+$/.test(a.propertyCategory)){b.warning("实际变量名称（非中文）只能输入英文,数字,下划线,#字符"),this.saveDis=!1;return}if(!e[a.name]&&!n[a.propertyCategory])e[a.name]=a.name,n[a.propertyCategory]=a.propertyCategory;else{b.warning("变量名称、实际变量名称 不可重复"),this.saveDis=!1;return}}else{b.warning("变量名称、实际变量名称 不可为空"),this.saveDis=!1;return}}if(this.varDialogInfo.protocolList.length===0&&this.varDialogInfo.filtered.length===0)t.protocolList=this.protocolData;else{const a=this.varDialogInfo.filtered.concat(this.protocolData),l=this.varDialogInfo.prototypeData1.concat(a);t.protocolList=l.concat(this.varDialogInfo.prototypeData2)}X(t).then(()=>{b.success("保存成功"),this.saveDis=!1,this.varDialogInfo.close(),this.$emit("getTemlate")})}}},re={class:"operation-btn-box"},ae={class:"btn-box"},oe={class:"operate-btn",style:{height:"'56px'"}},le={class:"tail-btn"};function ne(t,e,n,o,a,l){const u=K,y=J,C=Z,f=j,D=G,q=Q,N=H,x=W,U=R("KeyValueTable"),z=w;return g(),k(T,null,[i(x,{modelValue:l.visible,"onUpdate:modelValue":e[0]||(e[0]=r=>l.visible=r),width:"85%",class:"variable-dialog",title:n.varDialogInfo.currentTitle,"close-on-click-modal":!1,onClose:n.varDialogInfo.close},{default:s(()=>[m("div",re,[m("div",ae,[i(u,{type:"primary",icon:o.Plus,onClick:l.addVariable},{default:s(()=>e[1]||(e[1]=[d(" 添加变量 ")])),_:1},8,["icon","onClick"])]),e[2]||(e[2]=m("p",null,[d(" 请填写编辑变量信息后保存 "),m("span",{class:"msg-green"},[m("span",{class:"msg-bold"},"变量名称 变量实际名称"),d(" 不可重复")]),d(" 变量实际名称 "),m("span",{class:"msg-green"},"只能输入英文,数字,下划线,#字符"),d(" ，公式中必须 包含变量本身且不能有其他变量。 ")],-1))]),i(N,{data:a.protocolData,"highlight-current-row":!1},{default:s(()=>[i(y,{sortable:"",type:"index",width:"50"}),(g(!0),k(T,null,V(a.columnData,(r,h)=>(g(),v(y,{key:h,label:r.i18n.zh_CN,prop:r.key,width:"200"},{default:s(p=>[r.type==="string"?(g(),v(C,{key:0,modelValue:p.row[r.key],"onUpdate:modelValue":c=>p.row[r.key]=c,size:"small",placeholder:"请输入",class:"item-input-style",disabled:l.handleDisable(r.key,p.row)},null,8,["modelValue","onUpdate:modelValue","disabled"])):r.type==="number"?(g(),v(f,{key:1,modelValue:p.row[r.key],"onUpdate:modelValue":c=>p.row[r.key]=c,size:"small","controls-position":"right",min:r.min,disabled:l.handleDisable(r.key,p.row),max:r.max,class:"item-input-number",placeholder:"请输入"},null,8,["modelValue","onUpdate:modelValue","min","disabled","max"])):r.type==="select"?(g(),v(q,{key:2,modelValue:p.row[r.key],"onUpdate:modelValue":c=>p.row[r.key]=c,size:"small",placeholder:"请选择",class:"item-input-style",disabled:l.handleDisable(r.key,p.row),onChange:()=>r.change(p.row,r)},{default:s(()=>[(g(!0),k(T,null,V(r.options,c=>(g(),v(D,{key:c.key,label:c.value,value:c.key},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])):A("",!0)]),_:2},1032,["label","prop"]))),128)),i(y,{label:"操作",fixed:"right",align:"center",width:"260"},{default:s(r=>[m("div",oe,[i(u,{size:"small",type:"default",text:!0,icon:o.Edit,onClick:h=>o.handleOperate(r)},{default:s(()=>e[3]||(e[3]=[d(" 配置字典 ")])),_:2},1032,["icon","onClick"]),i(u,{size:"small",icon:o.CopyDocument,type:"default",text:!0,onClick:h=>l.handleCopy(r)},{default:s(()=>e[4]||(e[4]=[d(" 复制 ")])),_:2},1032,["icon","onClick"]),i(u,{size:"small",icon:o.Close,type:"danger",text:!0,onClick:h=>l.haneleDelete(r)},{default:s(()=>e[5]||(e[5]=[d(" 删除 ")])),_:2},1032,["icon","onClick"])])]),_:1})]),_:1},8,["data"]),m("div",le,[i(u,{type:"primary",disabled:a.saveDis,onClick:l.save},{default:s(()=>e[6]||(e[6]=[d(" 保存 ")])),_:1},8,["disabled","onClick"]),i(u,{onClick:n.varDialogInfo.close},{default:s(()=>e[7]||(e[7]=[d(" 取消 ")])),_:1},8,["onClick"])])]),_:1},8,["modelValue","title","onClose"]),i(z,{ref:"refKeyValueDialog",config:o.DialogFormConfig},{default:s(()=>[(g(!0),k(T,null,V(o.configFields,r=>(g(),v(U,{key:r,ref_for:!0,ref:"refKeyValueTable",field:r,"key-values":o.currentRow[r],onOnSave:h=>l.handleSave(r,h)},null,8,["field","key-values","onOnSave"]))),128))]),_:1},8,["config"])],64)}const se=O(te,[["render",ne],["__scopeId","data-v-d96266c0"]]),ce=Object.freeze(Object.defineProperty({__proto__:null,default:se},Symbol.toStringTag,{value:"Module"}));export{ee as a,ce as b,se as v};
