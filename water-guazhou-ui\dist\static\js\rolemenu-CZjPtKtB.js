import{d as q,u as O,c as v,r as P,o as V,a0 as I,dR as H,g as i,h as u,F as l,p as Q,q as p,G as g,n as A,aJ as W,bo as X,i as h,aB as Y,a as Z,x as B,bb as $,bV as j,dm as ee,bl as ae,bm as te,J as se,L as ne,br as le,C as oe}from"./index-r0dFAfgr.js";const re={class:"content-box"},ie={class:"dialog-footer"},pe=q({__name:"rolemenu",props:{tableConfig:{}},emits:["refresh"],setup(w,{emit:N}){var b;const f=O(),M=N,k=w,c=v([]),e=P({userdata:[],data:[],selectNodes:[],enterpriseoptions:[],enterprise:"",appoptions:[],app:"",apps:[],selectedApps:[],selectedAppItems:[],visible:!0,loading:!1,userInfo:{...f.user||{},additionalInfo:{...((b=f.user)==null?void 0:b.additionalInfo)||{}}}}),r=v({}),d=v({});V(()=>{e.appoptions=[],e.app=I().curNavs[0].id,e.apps=[],e.selectedApps=[],e.selectedAppItems=[],C(e.app)});const S=async(s,a)=>{r.value[e.app]=c.value[e.app].getCheckedKeys(),e.app=s.props.name||"",C(e.app)},z=async()=>{r.value[e.app]=c.value[e.app].getCheckedKeys(),d.value[e.app]=[...r.value[e.app]],d.value[e.app].forEach((s,a)=>{E(e.data,s,a)}),e.userInfo.additionalInfo.menu=d.value;try{await Z(e.userInfo),B.success("保存成功"),M("refresh")}catch{B.warning("保存失败")}},C=async s=>{s&&(e.app=s),e.loading=!0,H(e.app).then(a=>{e.data=y(a.data,"children",{label:"meta.title"}),U()}).finally(()=>{e.loading=!1})},U=()=>{},K=()=>{var s;(s=c.value[e.app])==null||s.setCheckedKeys([])};function y(s,a="children",n={label:"name"}){return s.map(t=>{if(t){for(const m in n)t[m]=t.meta.title;t.value={name:t.meta.title,value:t.id},t[a]&&t[a].length&&y(t[a],a,n)}return t}),s}function E(s,a,n){return s.forEach(t=>(t&&(a===t.id&&(d.value[e.app][n]={label:t.meta.title,value:t.name,component:t.component,id:t.id,children:!!t.children}),t.children&&t.children.length&&E(t.children,a,n)),t)),s}return V(()=>{var a;const s=((a=f.user.additionalInfo)==null?void 0:a.menu)||[];for(const n in s)r.value[n]=[],s[n].forEach(t=>{r.value[n].push(t.id)})}),(s,a)=>{var x;const n=$,t=j,m=ee,L=ae,R=te,_=se,D=ne,F=le;return i(),u(D,{modelValue:e.visible,"onUpdate:modelValue":a[1]||(a[1]=o=>e.visible=o),title:"常用功能",width:"60%","close-on-click-modal":!1,onClose:(x=k.tableConfig)==null?void 0:x.close},{footer:l(()=>[Q("span",ie,[p(_,{size:"small",type:"primary",onClick:z},{default:l(()=>a[2]||(a[2]=[g("保存")])),_:1}),p(_,{size:"small",onClick:K},{default:l(()=>a[3]||(a[3]=[g("清空")])),_:1}),p(_,{size:"small",onClick:k.tableConfig.close},{default:l(()=>a[4]||(a[4]=[g("取 消")])),_:1},8,["onClick"])])]),default:l(()=>[p(R,{modelValue:e.app,"onUpdate:modelValue":a[0]||(a[0]=o=>e.app=o),"tab-position":"left",style:{height:"500px","margin-top":"20px"},onTabClick:S},{default:l(()=>[(i(!0),A(Y,null,W(h(I)().curNavs,(o,G)=>(i(),u(L,{key:G,label:o.name,name:o.id},{default:l(()=>{var T;return[X((i(),A("div",re,[((T=e.data)==null?void 0:T.length)>0?(i(),u(t,{key:0,height:"500px"},{default:l(()=>[p(n,{ref_for:!0,ref:J=>{h(c)[o.id]=J},"default-checked-keys":h(r)[e.app],data:e.data,class:"menu-tree","show-checkbox":"","node-key":"id","default-expand-all":!0},null,8,["default-checked-keys","data"])]),_:2},1024)):(i(),u(m,{key:1,"image-size":150,description:"暂无"}))])),[[F,e.loading]])]}),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue","onClose"])}}}),de=oe(pe,[["__scopeId","data-v-2e8ffcad"]]);export{de as default};
