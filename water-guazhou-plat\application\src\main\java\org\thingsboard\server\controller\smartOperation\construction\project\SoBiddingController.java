package org.thingsboard.server.controller.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBidding;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.construction.project.SoBiddingService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/bidding")
public class SoBiddingController extends BaseController {
    @Autowired
    private SoBiddingService service;

    @GetMapping
    public IPage<SoBidding> findAllConditional(SoBiddingPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoBiddingPageRequest request) {
        return ExcelFileInfo.of("项目招投标流程列表", findAllConditional(request).getRecords())
                .nextTitle("projectCode", "项目编号")
                .nextTitle("projectName", "项目名称")
                .nextTitle("projectTypeName", "项目类别")
                .nextTitle("projectEstimate", "项目概算(万元)")
                .nextTitle("projectStartTimeName", "启动时间")
                .nextTitle("projectExpectEndTimeName", "预计结束时间")
                .nextTitle("projectPrincipal", "项目负责人");
    }

    @PostMapping
    public SoBidding save(@RequestBody SoBiddingSaveRequest req) {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoBiddingSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}