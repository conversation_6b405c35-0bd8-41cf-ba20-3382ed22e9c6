import{d as P,c as Q,r as T,s as F,o as X,am as z,j as Z,g as D,n as M,q as k,i as B,t as E,p as x,_ as H,aq as L,C as _}from"./index-r0dFAfgr.js";import{w as q}from"./Point-WxyopZva.js";import{r as R}from"./chart-wy3NEK2T.js";import{p as $}from"./onemap-CEunQziB.js";import{P as U,C as aa}from"./index-CcDafpIP.js";const ea="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAhCAYAAAAoNdCeAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAG6ADAAQAAAABAAAAIQAAAAAdnvTeAAAF7ElEQVRIDZ1XW2wUZRT+5z6zuzO7220pYOllgbZ0kRBAojEaKyZGEoiXAHIzYgBD1MSgaOBB8cGkDwaJGoIPJkaNivhAgEiUxpIYFBIot9ZgS5dSu21Z2LZ7n52dnfGcgS077W5ZOcn2v8w55/vPOd9/KUWmkY6Op1iX5m3m+OrHTcpcrWdiD+vZpMc0NJZhxAzDuyIcJ3eapnE0lxk+s+TZJUGK2muUckkV+2CaJnXmSGud6G7cnUkNv6imhn16NlVUl6KIyXKKKboeColS1bfR8Nn9T669cKuY3ykOzJ/WMF2zZm1Ixgc+URODVaVAJjujAJXjFUOS64KSVP1mYGSonVp7OFeoZwM72/6CT+Bm7R0Pd+7QtRhTqFhuH0BNwVGtyhUt+9OJvrZHV56I5W3pfOd6x6uiwM78MBbpemAg9IUlyKTDYnK8b6dTadre+8tzQh7DAkOFOCO/HL19cYeWvv1AEeUdWoCGQaUTA3wqduM91dX0DPrHeevP+Y5N87LJ2F+J6LXKQiPs04xEaJoFTVvG7WqmSQxDJ0YubZtnWYch+xZeZChj9eIV34RYJMQlIr0TT1312TRhoPgWkbqWrVB4meT0NKQoB5g0tIatZVgHAGVI8MoBEotcnnCTM1RKS4ebnYp/M0y2sZ0yG9CSI2uKsc5duYiwnBNWnSXXuw5ChBzJZsYJJ1YQXYtC1CL8BFIf2EZoViKoXwhmQjozqbDIiZXrejt3f0XToudpNTlUMbEcW4cmejYOAGPEBMCapo0klRggNY3rYT5JvNXLics9j2hqBMCRdBN8m/CSy6WpbGbUT1EzGmlg6ipI0TQFoQiFEQHoYM8PVnShnh8hrUkSGfqDRCNXrOjuln8CJN9BcuhaQmJYaRkLqwrkPxRrTSi85KohC5bvBQDVAsO0Imms2kHL8W4C2Slmbs0ZehoYbjSzgKqU0kIyZNI3ybWL+/B0sBiJAIBi9fE79rOQwjlNmyzSFPNlmDoFFlWskVOB1yUE6G7kNFh1yPqhFjKvfuHrpL/rS4g0NWFoGFrp7QEshlWJNDBKn7Ao1pm0v2oXbCH1sB2wtct0ZYcsmiRNs7wStRuVHsEhS+Y0brAUsMVxOQJbxgQWhWle8HbjiV2OyN5mwgkeSxVbHJcjcPfloGg9NEWzx4CWUL8SgmS4K5Hh0yT870mipm5aLY7vyT29e3NQRpo2WUFJ56jsOTar3zruUPw746N/11pMK9DEMS/6gPq1YAQ8gnHw8hfWHG5kQYSjVJoB0/rdudEC6ztduFgNuHI6KXWwiyXuawN88pHvWd69C04K24kPtwDxVC4mjUvftzmx9hfS3iYUQf1Cgb1oClJVgmWkQ/6lbTGrWJfbX/NrZvb4ePj8gkJl7CPV8QRBOpUWeBvARi/cCqjLCd4cnJcnSc7cunjFwZAFhkdK95871yXG+w6mYv0KXBflMaY0OixSNOWKQFB0zn4j8Fjbb3iDW7nAzi3tws8OpeFzh1yXwqJO4+e+n2iGMx1yw7jonPlpYDDYjv7RyBYBXuGaZ9FL8dGrnyXjQZ9p2N4r9wVBBYzI6Z4bcsn+XZJ280hD69dq3tAGhpPd3Wt4EvVvgVv742S0z4fHVbnCcg7T5WkcdMi1u7ixS0fmrzyRKbSdTCkSCBzW9PjQdy6l/iNYYaTcDc+wAkQ0f1Bwzt5TDAhBp0SWX0nvmY2KRtWuj4avtKmJAQ++MUrJndTNG4br/21HZuRYYeoKbUqCodKlXzc7GWfVtmQ0uCcZu16F99hkwdQ5lLk3JLnmA9ozcAgzM1knP54WDJXwPRmnpFXp1MiB+Ng/ttcXw/Cmy9vSz3DOLVGq93Rr66nS4YOvKTXLryLfYkqkVP9R2dO03eVtjNAMb33CiGRvyw3F3fBKOUBodN/I8qC9vW8J6gizNp0I7UvGgj54XveLQsWOwBPek9P955K3/19gqHzu3HaHpCnPa+rYbk70vKvyp35ftuz81EIWIjxoH481+PHqeI8f2tLPiRIA/wEK8pFj4ntYNQAAAABJRU5ErkJggg==",ta={class:"onemap-panel-wrapper"},sa={class:"table-box"},na=P({__name:"bigUser",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(G,{emit:O}){const w=O,C=G,d=Q([{label:"0 户",value:"大用户总数"},{label:"0 %",value:"报警率"}]),y=Q(),m=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],Y=T({group:[{id:"chart",fieldset:{desc:"监测状态统计",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:R(),style:{height:"150px"}}]},{id:"tab",fields:[{type:"input",field:"name",appendBtns:[{perm:!0,text:"刷新",click:()=>p(!0)}],onChange:()=>p()},{type:"tabs",field:"type",tabs:[{label:"全部",value:"all"},...m.map(e=>({...e,value:e.name}))],tabType:"simple",onChange:()=>p()}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),i=T({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:e,size:n})=>{i.pagination.page=e,i.pagination.limit=n},layout:"total,sizes, jumper"},handleRowClick:e=>j(e),columns:[{width:120,label:"名称",prop:"name",sortable:!0},{width:130,label:"状态",prop:"status",formatter:e=>{var n;return((n=m.find(r=>r.name===e.status))==null?void 0:n.label)||e.status}},{width:160,label:"更新时间",prop:"lastTime",sortable:!0}]}),p=async e=>{var n,r,f,g,W,N,V,S,J;i.loading=!0;try{const b=(n=y.value)==null?void 0:n.dataForm.type,h=await $({name:(r=y.value)==null?void 0:r.dataForm.name,status:b==="all"?"":b});i.dataList=((f=h.data)==null?void 0:f.data)||[];const K=Y.group[0].fields[0],v=((W=(g=h.data)==null?void 0:g.data)==null?void 0:W.length)||0,I=[],A=[];if((V=(N=h.data)==null?void 0:N.data)!=null&&V.map(a=>{var u,c;const t=(u=a.location)==null?void 0:u.split(",");if((t==null?void 0:t.length)===2){const l=new q({longitude:t[0],latitude:t[1],spatialReference:(c=C.view)==null?void 0:c.spatialReference});I.push({visible:!1,x:l.x,y:l.y,offsetY:-40,id:a.stationId,title:a.name,customComponent:F(U),customConfig:{info:{type:"attrs",imageUrl:a.imgs,stationId:a.stationId}},attributes:{path:C.menu.path,id:a.stationId,row:a},symbolConfig:{url:ea}})}let s=A.find(l=>l.status===a.status);const{label:o}=m.find(l=>l.name===a.status)||{};s?s.value++:(s={name:a.status,nameAlias:o,value:1,scale:"0%"},A.push(s))}),A.map(a=>{var t,s,o;return a.scale=v===0?"0%":Number(a.value)/v*100+"%",a.value=((o=(s=(t=h.data)==null?void 0:t.data)==null?void 0:s.filter(u=>u.status===a.name))==null?void 0:o.length)||0,a}),e){const a=(S=Y.group.find(t=>t.id==="tab"))==null?void 0:S.fields[1];if(a){a.tabs=a.tabs.map(s=>{var c;const o=A.find(l=>l.name===s.value),u=((c=m.find(l=>l.name===s.value))==null?void 0:c.label)||"";return s.label=u+"("+((o==null?void 0:o.value)||0)+")",s});const t=a.tabs.find(s=>s.value==="all");t&&(t.label="全部("+v+")"),K&&(K.option=R(A,"户"))}d.value[0].label=v+"户",d.value[1].label=((J=A.find(t=>t.name==="alarm"))==null?void 0:J.scale)||"0 %"}w("addMarks",{windows:I,customWinComp:F(U)})}catch(b){console.dir(b)}i.loading=!1},j=async e=>{w("highlightMark",C.menu,e==null?void 0:e.stationId)};return X(()=>{p(!0)}),z(()=>Z().isDark,()=>p(!0)),(e,n)=>{const r=H,f=L;return D(),M("div",ta,[k(B(aa),{modelValue:B(d),"onUpdate:modelValue":n[0]||(n[0]=g=>E(d)?d.value=g:null),span:12},null,8,["modelValue"]),k(r,{ref_key:"refForm",ref:y,config:B(Y)},null,8,["config"]),x("div",sa,[k(f,{config:B(i)},null,8,["config"])])])}}}),ua=_(na,[["__scopeId","data-v-bcb65c4d"]]);export{ua as default};
