package org.thingsboard.server.dao.model.sql.smartManagement.maintaince;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.util.Date;

@Getter
@Setter
@Entity
@TableName("sm_maintain_task_item")
public class SMMaintainTaskItem {
    @Id
    // id
    private String id;

    // 任务id
    private String taskId;

    // 设备在gis服务中的标识
    private String objectId;

    // 图片
    private String img;

    // 音频
    private String audio;

    // 视频
    private String video;

    // 文件
    private String file;

    // 完成时间
    private Date completeTime;

    // 备注
    private String remark;

    // 租户Id
    private String tenantId;

}
