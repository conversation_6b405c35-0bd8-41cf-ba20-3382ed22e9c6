import{w as T,Q as nt,e as c,y as _,a as it,I as Ut,M as Et,R as zt,E as It,$ as vt,v as Bt}from"./Point-WxyopZva.js";import{gS as Gt,y as q,g as A,u as Ft,l as J,bM as tt,gT as ft,bm as dt,_ as Rt,gU as Mt,b3 as Nt,b4 as jt,af as f,aO as U,aA as kt,ea as Y,aW as L,e0 as Wt,gV as At,aT as B,ab as wt,fY as Yt,aP as lt,v as Zt,ae as Kt,aQ as $,ez as Z,S as Xt,eC as Qt,gW as qt}from"./MapView-DaoQedLH.js";import{$ as ht,aO as p,R as P,aW as z,aS as Jt,T as K}from"./index-r0dFAfgr.js";import{a2 as te,a3 as ee,u as ie,a4 as _t,a5 as ut,a6 as R,Q as xt,V as Ct,a7 as Ot,F as oe,a8 as mt,a9 as ae,aa as se}from"./AnimatedLinesLayer-B2VbV4jv.js";import{H as re,B as ne,S as $t,A as le}from"./editPlaneUtils-BTKj0FMd.js";import{u as he}from"./colorUtils-BY7h749P.js";import{l as I}from"./widget-BcWKanF2.js";import{h as Lt}from"./GraphicsLayer-DTrBRwJQ.js";import{t as ce,j as gt}from"./GraphicManipulator-HH4WUKex.js";import{W as et,Z as Q}from"./boundedPlane-DeyjpfhM.js";import"./pe-B8dP0-Ut.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./automaticLengthMeasurementUtils-DljoUgEz.js";import"./spatialReferenceEllipsoidUtils-j_kxMN-4.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./automaticAreaMeasurementUtils-DfgMw58X.js";import"./triangle-lwOWqU0w.js";import"./lineSegment-DQ0q5UHF.js";import"./drapedUtils-DJwxIB1g.js";const pe=new Gt({data:{type:"CIMSymbolReference",symbol:{type:"CIMLineSymbol",symbolLayers:[{type:"CIMSolidStroke",effects:[{type:"CIMGeometricEffectDashes",dashTemplate:[3.75,3.75],lineDashEnding:"HalfPattern",controlPointEnding:"NoConstraint"}],enable:!0,capStyle:"Butt",joinStyle:"Round",miterLimit:10,width:1.6,color:[255,255,255,255]},{type:"CIMSolidStroke",enable:!0,capStyle:"Butt",joinStyle:"Round",miterLimit:10,width:2,color:[0,0,0,255]}]}}}),de=new q({style:"circle",size:6,color:[127,127,127,1],outline:{color:[50,50,50],width:1}}),_e=new q({style:"circle",size:6,color:[255,127,0,1],outline:{color:[50,50,50],width:1}});let V=class extends re{constructor(t){super(t),this._visualElementGraphics={outline:null,regularVertices:null,activeVertex:null},this.activeFillSymbol=null,this.type="draw-2d",this._visualElementSymbols={outline:ht(t.activeLineSymbol,pe),regularVertices:ht(t.regularVerticesSymbol,de),activeVertex:ht(t.activeVertexSymbol,_e),fill:p(t.activeFillSymbol)}}normalizeCtorArgs(t){const e={...t};return delete e.activeFillSymbol,delete e.activeVertexSymbol,delete e.regularVerticesSymbol,delete e.activeLineSymbol,e}initializeGraphic(t){return P(this._visualElementSymbols.fill)&&(t.symbol=this._visualElementSymbols.fill),null}makeDrawOperation(){const{defaultZ:t,hasZ:e,view:o}=this;return new te({view:o,manipulators:this.manipulators,geometryType:ne(this.geometryType),drawingMode:this.mode,hasZ:e,defaultZ:t,snapToSceneEnabled:this.snapToScene,drawSurface:new ee(o,e,t),hasM:!1,snappingManager:this.snappingManager,snappingVisualizer:new ie(this.internalGraphicsLayer),tooltipOptions:this.tooltipOptions})}onActiveVertexChanged(t){if(this.geometryType==="point")return null;const[e,o]=t,a=new T({x:e,y:o,spatialReference:this.view.spatialReference});return P(this._visualElementGraphics.activeVertex)?(this._visualElementGraphics.activeVertex.geometry=a,null):(this._visualElementGraphics.activeVertex=new A({geometry:a,symbol:this._visualElementSymbols.activeVertex,attributes:{displayOrder:2}}),this.internalGraphicsLayer.add(this._visualElementGraphics.activeVertex),this.internalGraphicsLayer.graphics.sort(ct),nt(()=>{P(this._visualElementGraphics.activeVertex)&&(this.internalGraphicsLayer.remove(this._visualElementGraphics.activeVertex),this._visualElementGraphics.activeVertex=z(this._visualElementGraphics.activeVertex))}))}onOutlineChanged(t){const e=t.clone();if(e.type==="polyline"){const o=e.paths[e.paths.length-1];o.splice(0,o.length-2)}return P(this._visualElementGraphics.outline)?(this._visualElementGraphics.outline.geometry=e,null):(this._visualElementGraphics.outline=new A({geometry:e,symbol:this._visualElementSymbols.outline,attributes:{displayOrder:0}}),this.internalGraphicsLayer.add(this._visualElementGraphics.outline),this.internalGraphicsLayer.graphics.sort(ct),nt(()=>{P(this._visualElementGraphics.outline)&&(this.internalGraphicsLayer.remove(this._visualElementGraphics.outline),this._visualElementGraphics.outline=z(this._visualElementGraphics.outline))}))}onRegularVerticesChanged(t){const e=new Ft({points:t,spatialReference:this.view.spatialReference});return P(this._visualElementGraphics.regularVertices)?(this._visualElementGraphics.regularVertices.geometry=e,null):(this._visualElementGraphics.regularVertices=new A({geometry:e,symbol:this._visualElementSymbols.regularVertices,attributes:{displayOrder:1}}),this.internalGraphicsLayer.add(this._visualElementGraphics.regularVertices),this.internalGraphicsLayer.graphics.sort(ct),nt(()=>{P(this._visualElementGraphics.regularVertices)&&(this.internalGraphicsLayer.remove(this._visualElementGraphics.regularVertices),this._visualElementGraphics.regularVertices=z(this._visualElementGraphics.regularVertices))}))}};function ct(i,t){var e,o;return(((e=i.attributes)==null?void 0:e.displayOrder)??-1/0)-(((o=t.attributes)==null?void 0:o.displayOrder)??-1/0)}c([_()],V.prototype,"activeFillSymbol",void 0),c([_({readOnly:!0})],V.prototype,"type",void 0),c([_({constructOnly:!0,nonNullable:!0})],V.prototype,"view",void 0),V=c([it("esri.views.2d.interactive.draw.DrawGraphicTool2D")],V);function ue(i,t){const e=i.a*t;return he(i)>225?new J([0,0,0,e]):new J([255,255,255,e])}function me(i,t){const e=new J(i);return e.a*=t,e}function D(i=1){return me(Jt.analysisTheme.accentColor,i)}function St(i=1){return ue(D(),i)}let yt=class{get hovering(){return this.someManipulator(t=>t.hovering)}get grabbing(){return this.someManipulator(t=>t.grabbing)}get dragging(){return this.someManipulator(t=>t.dragging)}hasManipulator(t){return this.someManipulator(e=>e===t)}someManipulator(t){let e=!1;return this.forEachManipulator(o=>{!e&&t(o)&&(e=!0)}),e}};var F;(function(i){i[i.TRANSLATE_XY=0]="TRANSLATE_XY",i[i.SCALE=1]="SCALE",i[i.ROTATE=2]="ROTATE"})(F||(F={}));class Dt extends yt{constructor(t){super(),this._view=t.view,this._tool=t.tool,this._graphic=t.graphic,this._manipulator=this._createManipulator(),this.forEachManipulator(e=>this._tool.manipulators.add(e))}destroy(){this.forEachManipulator(t=>{this._tool.manipulators.remove(t),t.destroy()}),this._tool=null,this._view=null,this._manipulator=null,this._graphic=null}forEachManipulator(t){t(this._manipulator,F.TRANSLATE_XY)}createDragPipeline(t,e){let o=null,a=null,r=0,n=0,s=0;const{offsetX:l,offsetY:d,size:m}=ce(p(this._graphic.symbol));return _t(this._manipulator,(g,E)=>{E.next(y=>{if(y.action==="start"){const h=t();o=h.editGeometryOperations,a=h.constraints}return y}).next(ut(this._view)).next(y=>{const{x:h,y:u,z:v}=y.mapEnd;if(a&&(h+l<a.xmin||u+d-m<a.ymin||h+l>a.xmax||u+d-m>a.ymax))return y;y.action==="start"&&(r=y.mapStart.x,n=y.mapStart.y,s=y.mapStart.z);const w=h-r,H=u-n,x=v-s;r=h,n=u,s=v;const O=[];for(const at of o.data.components)O.push(...at.vertices);const N=y.action==="start"?R.NEW_STEP:R.ACCUMULATE_STEPS,ot=o.moveVertices(O,w,H,x,N);return e(y,ot),y})})}_createManipulator(){const t=this._view,e=this._graphic;return new gt({view:t,graphic:e,selectable:!0,cursor:"move"})}}const G={up:"ArrowUp",down:"ArrowDown",left:"ArrowLeft",right:"ArrowRight",toggleOpacity:"t",shift:"Shift",primaryKey:Mt},ge=1,ye=10,bt=new J("#009AF2");let k=class extends xt{constructor(t){super(t),this._isOpacityToggled=!1,this._isModifierActive=!1,this._factor=1,this._initialControlPoints=null,this._graphicsLayer=new Lt({internal:!0,listMode:"hide",visible:!1,effect:"drop-shadow(0px, 0px, 3px)"}),this._undoStack=[],this._redoStack=[],this._sharedUndoStack=[],this._sharedRedoStack=[],this._highlightHandle=null,this.activeHandle=0}initialize(){this._initialize()}destroy(){const{map:t}=this.view;this._controlPointManipulations.forEach(e=>e.destroy()),this._controlPointEditGeometryOperations.forEach(e=>e.destroy()),t.removeMany([this._graphicsLayer]),this._graphicsLayer.removeAll(),this._graphicsLayer=z(this._graphicsLayer),this._georeference=null,this._controlPointGraphics=null,this._controlPointManipulations=null,this._graphicsLayer=null,this._controlPointEditGeometryOperations=null,this._undoStack=null,this._redoStack=null,this._initialControlPoints=null,this._sharedUndoStack=null,this._sharedRedoStack=null}get _hasValidSpatialReference(){return Ut(this.view.spatialReference)}onActivate(){this.visible=!0}onDeactivate(){this.visible=!1}onShow(){this._graphicsLayer.visible=!0}onHide(){this._graphicsLayer.visible=!1}canUndo(){const t=this._undoStack[this._undoStack.length-1];return t!=null&&this._controlPointEditGeometryOperations[t].canUndo}canRedo(){const t=this._redoStack[this._redoStack.length-1];return t!=null&&this._controlPointEditGeometryOperations[t].canRedo}undo(){if(this._undoStack.length>0){const t=this._undoStack.pop();this._controlPointEditGeometryOperations[t].undo(),this.updateGraphics(),this._redoStack.push(t)}}redo(){if(this._redoStack.length>0){const t=this._redoStack.pop();this._controlPointEditGeometryOperations[t].redo(),this.updateGraphics(),this._undoStack.push(t)}}refresh(){const{mediaElement:t}=this;if(K(t.georeference))return;const e=t.georeference;e.type!=="control-points"||K(e.coords)||(this._georeference=e,p(this._georeference.controlPoints).forEach(({mapPoint:o},a)=>{const r=this._controlPointEditGeometryOperations[a],n=r.data.components[0].vertices[0];r.setVertexPosition(n,r.data.coordinateHelper.pointToVector(o))}),this.updateGraphics())}reset(){this._georeference.controlPoints=this._initialControlPoints,this.refresh(),this._sharedUndoStack.length=0,this._sharedRedoStack.length=0}updateGraphics(){const t=this._georeference,e=p(t.controlPoints),o=p(e[0].mapPoint).spatialReference,a=this._hasValidSpatialReference;this._georeference.controlPoints=this._controlPointEditGeometryOperations.map((r,n)=>{const s=r.data.geometry;return this._controlPointGraphics[n].geometry=s,{mapPoint:tt(s,o),sourcePoint:a?p(e[n]).sourcePoint:t.toSource(s)}})}updateActiveHandle(t){if(this.activeHandle===t)return;const e=p(this._controlPointGraphics[this.activeHandle].symbol).clone();ft(e,D()),this._controlPointGraphics[this.activeHandle].symbol=e;const o=p(this._controlPointGraphics[t].symbol).clone();ft(o,bt),this._controlPointGraphics[t].symbol=o,this.activeHandle=t,this.view.surface===document.activeElement&&this.highlightActiveHandle()}async highlightActiveHandle(){this.removeHighlightActiveHandle();const t=await this.view.whenLayerView(this._graphicsLayer);this._highlightHandle=t.highlight(this._controlPointGraphics[this.activeHandle])}removeHighlightActiveHandle(){this._highlightHandle&&this._highlightHandle.remove()}setSharedUndoStack(t){this._sharedUndoStack=t}setSharedRedoStack(t){this._sharedRedoStack=t}async _initialize(){const{view:t,mediaElement:e}=this;if(K(e.georeference))return;const o=e.georeference;o.type!=="control-points"||K(o.coords)||(this._georeference=o,this._initialControlPoints=p(this._georeference.controlPoints),t.map.addMany([this._graphicsLayer]),t.focus(),this.visible=!1,this.finishToolCreation(),await this._loadProjectionEngine(),this._controlPointEditGeometryOperations=p(this._georeference.controlPoints).map(({mapPoint:a})=>Ct.fromGeometry(tt(a,t.spatialReference),dt.Local)),this._controlPointGraphics=this._controlPointEditGeometryOperations.map((a,r)=>new A({symbol:new Gt({data:{type:"CIMSymbolReference",symbol:{type:"CIMPointSymbol",symbolLayers:[{type:"CIMVectorMarker",enable:!0,colorLocked:!0,anchorPoint:{x:0,y:-15.75},anchorPointUnits:"Absolute",dominantSizeAxis3D:"Y",size:9,billboardMode3D:"FaceNearPlane",frame:{xmin:0,ymin:0,xmax:84.3,ymax:84.3},markerGraphics:[{type:"CIMMarkerGraphic",geometry:{rings:[[[83.2,32.5],[84.3,40.7],[83.8,48.9],[81.7,56.9],[78.1,64.3],[73,70.9],[66.9,76.4],[59.7,80.5],[51.9,83.2],[43.7,84.3],[35.4,83.8],[27.4,81.7],[20,78],[13.4,73],[7.9,66.8],[3.8,59.7],[1.1,51.9],[0,43.7],[.5,35.4],[2.6,27.4],[6.3,20],[11.3,13.4],[17.5,7.9],[24.7,3.8],[32.5,1.1],[39.8,.1],[47.1,.3],[54.3,1.8],[61.1,4.5],[67.4,8.4],[72.9,13.3],[77.4,19.1],[80.9,25.5],[83.2,32.5]]]},symbol:{type:"CIMPolygonSymbol",symbolLayers:[{type:"CIMSolidFill",enable:!0,color:[255,255,255,255]}]}}],scaleSymbolsProportionally:!0,respectFrame:!0,clippingPath:{type:"CIMClippingPath",clippingType:"Intersect",path:{rings:[[[0,0],[84.3,0],[84.3,84.3],[0,84.3],[0,0]]]}},rotation:0},{type:"CIMVectorMarker",enable:!0,anchorPoint:{x:0,y:-11.25},anchorPointUnits:"Absolute",dominantSizeAxis3D:"Y",size:22.5,billboardMode3D:"FaceNearPlane",frame:{xmin:0,ymin:0,xmax:197.7,ymax:294.7},markerGraphics:[{type:"CIMMarkerGraphic",geometry:{rings:[[[98.9,0],[119.4,23.2],[139.4,49.3],[156.8,75.2],[171.2,100.8],[182.4,125.3],[190.6,148.8],[195.7,171.4],[197.7,192.9],[197.7,195.8],[197.7,200.3],[197.6,202.5],[197.5,204.8],[197.3,207.1],[197,209.4],[196.7,211.7],[196.4,214.1],[196,216.4],[195.5,218.7],[195,221.1],[194.4,223.4],[193.7,225.8],[193,228.1],[192.2,230.5],[191.4,232.8],[190.5,235.1],[189.5,237.5],[188.5,239.7],[187.4,242],[186.2,244.3],[185,246.5],[183.7,248.7],[182.4,250.9],[181,253.1],[179.5,255.2],[178,257.3],[176.4,259.4],[174.7,261.4],[173.1,263.3],[171.3,265.3],[169.5,267.2],[167.7,269],[165.8,270.8],[163.9,272.5],[161.9,274.2],[159.9,275.8],[157.8,277.4],[155.7,278.9],[153.6,280.4],[151.4,281.7],[149.2,283.1],[147,284.4],[144.8,285.6],[142.5,286.7],[140.3,287.8],[138,288.8],[135.7,289.8],[133.4,290.7],[131,291.5],[128.7,292.3],[126.4,293],[124,293.6],[121.7,294.2],[119.4,294.7],[117,295.2],[114.7,295.6],[112.4,296],[110.1,296.3],[107.8,296.5],[105.5,296.7],[103.3,296.8],[101.1,296.9],[98.8,296.9],[83.1,295.7],[67.8,292],[53.3,285.9],[39.9,277.5],[28.1,267.2],[18,255.1],[10,241.5],[4.2,226.9],[.9,211.5],[0,195.8],[.1,192.9],[2.1,171.4],[7.2,148.8],[15.4,125.3],[26.6,100.8],[41,75.2],[58.4,49.3],[78.4,23.2],[98.9,0]]]},symbol:{type:"CIMPolygonSymbol",symbolLayers:[{type:"CIMSolidFill",enable:!0,color:r===this.activeHandle?bt.toArray():D().toArray()}]}}],scaleSymbolsProportionally:!0,respectFrame:!0,clippingPath:{type:"CIMClippingPath",clippingType:"Intersect",path:{rings:[[[0,0],[197.7,0],[197.7,294.7],[0,294.7],[0,0]]]}},rotation:0}],haloSize:1,scaleX:1,angleAlignment:"Display",angle:0}}}),geometry:a.data.geometry})),this._graphicsLayer.graphics.addMany([...this._controlPointGraphics]),this._controlPointManipulations=this._controlPointGraphics.map(a=>new Dt({tool:this,view:t,graphic:a})),this.addHandles([...this._controlPointManipulations.map((a,r)=>a.createDragPipeline(this._getInfo.bind(this,r),(n,s)=>{n.action==="start"&&(this._undoStack.push(r),this._redoStack=[],this._sharedUndoStack.push({tool:this,operation:s}),this._sharedRedoStack.length=0),this.updateGraphics()})),I(()=>this.view.scale,()=>this.active?this.updateGraphics():null)]),this._controlPointManipulations.forEach((a,r)=>{const n=s=>{this.addHandles([s.events.on(["click","grab-changed"],l=>this.updateActiveHandle(r))])};a.forEachManipulator(n)}),this.addHandles([t.on("key-down",a=>{t.activeTool===this&&(a.key!==G.shift||a.repeat||(this._isModifierActive=!0,a.stopPropagation()),a.key!==G.toggleOpacity||a.repeat||(e.opacity*=this._isOpacityToggled?2:.5,this._isOpacityToggled=!this._isOpacityToggled,a.stopPropagation()),a.key!==G.primaryKey||a.repeat||(this._factor=ye,a.stopPropagation()),this._isModifierActive&&(a.key===G.up&&(this._move(0,this._factor),a.stopPropagation()),a.key===G.down&&(this._move(0,-this._factor),a.stopPropagation()),a.key===G.left&&(this._move(-this._factor,0),a.stopPropagation()),a.key===G.right&&(this._move(this._factor,0),a.stopPropagation())))}),t.on("key-up",a=>{t.activeTool===this&&(a.key===G.shift&&(this._isModifierActive=!1,a.stopPropagation()),a.key===G.primaryKey&&(this._factor=ge,a.stopPropagation()))})]))}async _loadProjectionEngine(){const t=p(p(this._georeference.controlPoints)[0].mapPoint);return Rt(t.spatialReference,this.view.spatialReference)}_getInfo(t){return{editGeometryOperations:this._controlPointEditGeometryOperations[t],constraints:this._hasValidSpatialReference?null:{xmin:0,ymin:0,xmax:this._georeference.width,ymax:this._georeference.height}}}_move(t,e){const o=this._controlPointEditGeometryOperations[this.activeHandle],a=[];for(const n of o.data.components)a.push(...n.vertices);const r=o.moveVertices(a,t*this.view.resolution,e*this.view.resolution,0,R.NEW_STEP);this._sharedUndoStack.push({tool:this,operation:r}),this._sharedRedoStack.length=0,this.updateGraphics()}};c([_()],k.prototype,"_hasValidSpatialReference",null),c([_()],k.prototype,"activeHandle",void 0),c([_({constructOnly:!0,nonNullable:!0})],k.prototype,"mediaElement",void 0),c([_({constructOnly:!0})],k.prototype,"view",void 0),k=c([it("esri.views.2d.interactive.editingTools.ControlPointsTransformTool")],k);function Ht(i,t){i.action==="start"?t.cursor="grabbing":t.cursor="grab"}class ve{constructor(){this._lastDragEvent=null,this.next=new Ot,this._enabled=!1}get enabled(){return this._enabled}set enabled(t){if(this._enabled!==t&&P(this._lastDragEvent)){const e={...this._lastDragEvent,action:"update"};t&&this._adjustScaleFactors(e),this.next.execute(e)}this._enabled=t}createDragEventPipelineStep(){return this._lastDragEvent=null,t=>(this._lastDragEvent=t.action!=="end"?{...t}:null,this._enabled&&this._adjustScaleFactors(t),t)}_adjustScaleFactors(t){const e=t.direction[0]!==0&&t.direction[1]!==0?Math.max(Math.abs(t.factor1),Math.abs(t.factor2)):t.direction[0]===0?Math.abs(t.factor2):Math.abs(t.factor1);t.factor1=t.factor1<0?-e:e,t.factor2=t.factor2<0?-e:e}}class fe{constructor(){this._lastDragEvent=null,this.next=new Ot,this._enabled=!1}get enabled(){return this._enabled}set enabled(t){if(this._enabled!==t&&P(this._lastDragEvent)){const e={...this._lastDragEvent,action:"update"};t&&this._adjustRotateAngle(e),this.next.execute(e)}this._enabled=t}createDragEventPipelineStep(){return this._lastDragEvent=null,t=>(this._lastDragEvent=t.action!=="end"?{...t}:null,this._enabled&&this._adjustRotateAngle(t),t)}_adjustRotateAngle(t){const e=Nt(t.rotateAngle);t.rotateAngle=jt(5*Math.round(e/5))}}class we extends yt{constructor(t){super(),this._handles=new Et,this._originCache=f(),this._view=t.view,this._tool=t.tool,this._graphic=t.graphic,this._snapRotation=t.snapRotation,this._manipulator=this._createManipulator(),this._handles.add([this._manipulator.events.on("grab-changed",e=>Ht(e,this._manipulator))]),this.forEachManipulator(e=>this._tool.manipulators.add(e))}destroy(){this._handles.destroy(),this.forEachManipulator(t=>{this._tool.manipulators.remove(t),t.destroy()}),this._tool=null,this._view=null,this._manipulator=null,this._snapRotation=null,this._graphic=null,this._handles=null,this._originCache=null}forEachManipulator(t){t(this._manipulator,F.ROTATE)}createDragPipeline(t,e){let o=null,a=null;return _t(this._manipulator,(r,n)=>{n.next(s=>{if(s.action==="start"){r.cursor="grabbing";const l=t();o=l.plane,a=l.editGeometryOperations}return s}).next(ut(this._view)).next(s=>({...s,rotateAngle:oe(s.mapStart,s.mapEnd,{x:o.origin[0],y:o.origin[1]},!0)})).next(this._snapRotation.createDragEventPipelineStep(),this._snapRotation.next).next(s=>{const l=U(this._originCache,o.origin),d=[];for(const E of a.data.components)d.push(...E.vertices);const m=s.action==="start"?R.NEW_STEP:R.ACCUMULATE_STEPS,g=a.rotateVertices(d,l,s.rotateAngle,m,mt.REPLACE);return $t(g,o),e(s,g),s}).next(s=>(s.action==="end"&&(r.cursor="grab"),s))})}_createManipulator(){const t=this._view,e=this._graphic;return new gt({view:t,graphic:e,selectable:!0,cursor:"grab"})}}const pt=10,Pt=1e-6,Se=.3;function Tt(i){const t=B(i.basis1),e=B(i.basis2);return Se*Math.min(t,e)}class be extends yt{constructor(t){super(),this._handles=new Et,this._planeStart=et(),this._displayPlaneStart=et(),this._originCache=f(),this._axisCache=kt(),this._renderStartCache=f(),this._renderEndCache=f(),this._resizeOriginCache=f(),this._view=t.view,this._tool=t.tool,this._graphic=t.graphic,this._direction=t.direction,this._preserveAspectRatio=t.preserveAspectRatio,this._manipulator=this._createManipulator(),this._handles.add([this._manipulator.events.on("grab-changed",e=>Ht(e,this._manipulator))]),this.forEachManipulator(e=>this._tool.manipulators.add(e))}destroy(){this._handles.destroy(),this.forEachManipulator(t=>{this._tool.manipulators.remove(t),t.destroy()}),this._tool=null,this._view=null,this._graphic=null,this._manipulator=null,this._direction=null,this._handles=null,this._planeStart=null,this._displayPlaneStart=null,this._originCache=null,this._axisCache=null,this._renderStartCache=null,this._renderEndCache=null,this._resizeOriginCache=null,this._preserveAspectRatio=null}forEachManipulator(t){t(this._manipulator,F.SCALE)}createDragPipeline(t,e){let o=null,a=null,r=null,n=0,s=null,l=null;const d=this._planeStart,m=this._displayPlaneStart,g=this._direction;return _t(this._manipulator,(E,y)=>{y.next(h=>{if(h.action==="start"){E.cursor="grabbing";const u=t();o=u.plane,a=u.displayPlane,r=u.editGeometryOperations,n=pt*this._view.resolution,Q(o,d),Q(a,m);const v=zt(r.data.spatialReference);s=v?v.valid[1]-v.valid[0]-3*pt*this._view.resolution:null}return h}).next(ut(this._view)).next(h=>{const u=U(this._renderStartCache,[h.mapStart.x,h.mapStart.y,0]),v=U(this._renderEndCache,[h.mapEnd.x,h.mapEnd.y,0]),w=U(this._resizeOriginCache,m.origin);Y(w,w,m.basis1,-g[0]),Y(w,w,m.basis2,-g[1]),L(v,v,w),L(u,u,w);const H=g[0]!==0&&g[1]!==0,x=Tt(m),O=Tt(a)/x,N=(st,rt)=>{if(st===0)return 1;let C=B(rt),j=.5*st*wt(rt,v)/C;const W=j<0?-1:1;H&&(j+=(C-.5*st*wt(rt,u)/C)*W*O);const Vt=C<1.5*n?1:Pt;return C=Math.max(C-n,Pt),W>0&&(j-=pt*this._view.resolution),W*Math.max(W*(j/C),Vt)},ot=N(g[0],m.basis1),at=N(g[1],m.basis2);return{...h,direction:g,factor1:ot,factor2:at}}).next(this._preserveAspectRatio.createDragEventPipelineStep(),this._preserveAspectRatio.next).next(h=>{const u=U(this._originCache,d.origin);Y(u,u,d.basis1,-g[0]),Y(u,u,d.basis2,-g[1]);const v=Wt(this._axisCache,d.basis1[0],d.basis1[1]);At(v,v);const w=[];for(const O of r.data.components)w.push(...O.vertices);const H=h.action==="start"?R.NEW_STEP:R.ACCUMULATE_STEPS,x=r.scaleVertices(w,u,v,h.factor1,h.factor2,H,mt.REPLACE);return s&&s<r.data.geometry.extent.width&&l?r.updateVertices(w,l):(Q(d,o),$t(x,o),l=x.operation,e(h,x)),h}).next(h=>(h.action==="end"&&(E.cursor="grab"),h))})}_createManipulator(){return new gt({view:this._view,graphic:this._graphic,selectable:!0,cursor:"grab"})}}const S={up:"ArrowUp",down:"ArrowDown",left:"ArrowLeft",right:"ArrowRight",plus:"+",minus:"-",toggleOpacity:"t",shift:"Shift",primaryKey:Mt},Pe=80,Te=10,Ee=30,Ge=[[1,1],[1,-1],[-1,-1],[-1,1],[1,0],[0,-1],[-1,0],[0,1]],Re=1,Me=10;let b=class extends xt{constructor(i){super(i),this._initialControlPoints=null,this._initialGeometry=null,this._graphic=null,this._planeCache=et(),this._displayPlaneCache=et(),this._mainAxisCache=kt(),this._rotationHandleCache=f(),this._cornerA=f(),this._cornerB=f(),this._cornerC=f(),this._cornerD=f(),this._avgAB=f(),this._avgBC=f(),this._avgCD=f(),this._avgDA=f(),this._preserveAspectRatio=new ve,this._snapRotation=new fe,this._graphicsLayer=new Lt({internal:!0,listMode:"hide",visible:!1}),this._sharedUndoStack=[],this._sharedRedoStack=[],this._isOpacityToggled=!1,this._isModifierActive=!1,this._factor=1,this.preserveAspectRatio=null,this.snapRotation=null}initialize(){this._initialize()}destroy(){const{map:i}=this.view;this._dragManipulation.destroy(),this._rotateManipulation.destroy(),this._scaleManipulations.forEach(t=>t.destroy()),this._editGeometryOperations.destroy(),i.removeMany([this._graphicsLayer]),this._graphicsLayer.removeAll(),this._graphicsLayer=z(this._graphicsLayer),this._initialControlPoints=null,this._initialGeometry=null,this._graphic=null,this._preserveAspectRatio=null,this._snapRotation=null,this._planeCache=null,this._displayPlaneCache=null,this._rotationHandleCache=null,this._mainAxisCache=null,this._cornerA=null,this._cornerB=null,this._cornerC=null,this._cornerD=null,this._avgAB=null,this._avgBC=null,this._avgCD=null,this._avgDA=null,this._sharedUndoStack=null,this._sharedRedoStack=null}get _plane(){const i=this._graphic.geometry;if(!P(i))return null;const t=this._editGeometryOperations.data,e=t.components[0].edges[0],o=Yt(this._mainAxisCache,e.leftVertex.pos,e.rightVertex.pos);At(o,o);let a=Pe*this.view.resolution;const r=this.view.spatialReference;return It(r,i.spatialReference)&&(a*=vt(r)/vt(i.spatialReference)),le(o,t,a,this._planeCache)}get _displayPlane(){const i=this._plane;if(!i)return null;const t=this._displayPlaneCache;Q(i,t);const e=Te*this.view.resolution;return lt(t.basis1,t.basis1,1+e/B(t.basis1)),lt(t.basis2,t.basis2,1+e/B(t.basis2)),t}get _backgroundGraphicGeometry(){const i=this._displayPlane;if(!i)return null;const t=this.view.spatialReference;return this._updateDisplayPlaneConrers(i),new Zt({spatialReference:t,rings:[[this._cornerA,this._cornerB,this._cornerC,this._cornerD,this._cornerA]]})}get _rotateGraphicGeometry(){const i=this._plane;if(!i)return null;const t=this._rotationHandleCache;return Kt(t,i.basis1),lt(t,t,Ee*this.view.resolution),$(t,t,i.origin),$(t,t,i.basis1),new T({x:t[0],y:t[1],spatialReference:this.view.spatialReference})}get _scaleGraphicGeometries(){const i=this._displayPlane;if(!i)return[];const t=this.view.spatialReference;this._updateDisplayPlaneConrers(i);const{_cornerA:e,_cornerB:o,_cornerC:a,_cornerD:r}=this,n=Z(this._avgAB,e,o,.5),s=Z(this._avgBC,o,a,.5),l=Z(this._avgCD,a,r,.5),d=Z(this._avgDA,r,e,.5);return[new T({x:e[0],y:e[1],spatialReference:t}),new T({x:o[0],y:o[1],spatialReference:t}),new T({x:a[0],y:a[1],spatialReference:t}),new T({x:r[0],y:r[1],spatialReference:t}),new T({x:n[0],y:n[1],spatialReference:t}),new T({x:s[0],y:s[1],spatialReference:t}),new T({x:l[0],y:l[1],spatialReference:t}),new T({x:d[0],y:d[1],spatialReference:t})]}onActivate(){this.visible=!0}onDeactivate(){this.visible=!1}onShow(){this._graphicsLayer.visible=!0}onHide(){this._graphicsLayer.visible=!1}canUndo(){return this._editGeometryOperations.canUndo}canRedo(){return this._editGeometryOperations.canRedo}undo(){this._editGeometryOperations.undo(),this.updateGraphics()}redo(){this._editGeometryOperations.redo(),this.updateGraphics()}refresh(){const{view:i,target:t}=this,e="georeference"in t?p(p(t.georeference).coords):t.geometry,o=this._editGeometryOperations,a=o.data.components[0].vertices,r=ae.fromGeometry(tt(e,i.spatialReference),dt.Local).components[0].vertices;a.forEach((n,s)=>{o.setVertexPosition(n,r[s].pos)}),this.updateGraphics()}reset(){const{target:i}=this;if("georeference"in i){const t=p(i.georeference);t.type==="control-points"&&(t.controlPoints=this._initialControlPoints)}else i.geometry=this._initialGeometry;this.refresh(),this._sharedUndoStack.length=0,this._sharedRedoStack.length=0}updateGraphics(){const i=this._editGeometryOperations.data.geometry;"georeference"in this.target&&(p(this.target.georeference).coords=i),this._graphic.geometry=i,this._backgroundGraphic.geometry=this._backgroundGraphicGeometry,this._rotateGraphic.geometry=this._rotateGraphicGeometry,this._scaleGraphicGeometries.forEach((t,e)=>{this._scaleGraphics[e].geometry=t})}setSharedUndoStack(i){this._sharedUndoStack=i}setSharedRedoStack(i){this._sharedRedoStack=i}async _initialize(){const{view:i,target:t}=this;if("georeference"in t){const o=p(t.georeference);this._graphic=new A({geometry:p(o.coords)}),this._initialControlPoints=o.type==="control-points"?o.controlPoints:null}else this._graphic=t,this._initialGeometry=p(t.geometry);i.map.addMany([this._graphicsLayer]),i.focus(),this.visible=!1,this.finishToolCreation(),await this._loadProjectionEngine(),this._editGeometryOperations=Ct.fromGeometry(tt(this._graphic.geometry,i.spatialReference),dt.Local),this._backgroundGraphic=new A({symbol:new Xt({color:"transparent",outline:{type:"simple-line",color:D(),width:2}}),geometry:this._backgroundGraphicGeometry}),this._rotateGraphic=new A({symbol:new q({color:St(),outline:{type:"simple-line",color:D(),width:1}}),geometry:this._rotateGraphicGeometry}),this._scaleGraphics=this._scaleGraphicGeometries.map(o=>new A({symbol:new q({size:6,style:"square",color:St(),outline:{type:"simple-line",color:D(),width:1}}),geometry:o})),this._graphicsLayer.graphics.addMany([this._backgroundGraphic,this._rotateGraphic,...this._scaleGraphics]),this._dragManipulation=new Dt({tool:this,view:i,graphic:this._graphic}),this._rotateManipulation=new we({tool:this,view:i,graphic:this._rotateGraphic,snapRotation:this._snapRotation}),this._scaleManipulations=this._scaleGraphics.map((o,a)=>new be({tool:this,view:i,graphic:o,direction:Ge[a],preserveAspectRatio:this._preserveAspectRatio})),this.addHandles([this._dragManipulation.createDragPipeline(this._getInfo.bind(this),this._updateGraphics.bind(this)),this._rotateManipulation.createDragPipeline(this._getInfo.bind(this),this._updateGraphics.bind(this)),...this._scaleManipulations.map(o=>o.createDragPipeline(this._getInfo.bind(this),this._updateGraphics.bind(this))),I(()=>this.view.scale,()=>this.active?this.updateGraphics():null),i.on("click",async o=>{if(i.activeTool!=null&&i.activeTool!==this)return;const a=Qt(o),r=[];i.map.allLayers.forEach(l=>{l.type!=="vector-tile"&&l.type!=="imagery"||r.push(l)});const n=await this.view.hitTest(a,{exclude:r}),s=n.results;if(s.length===0)i.activeTool=null;else{const l=se(n.results),d="georeference"in t,m=s.map(E=>E.type==="media"?E.element:null).filter(Boolean),g=[...this._graphicsLayer.graphics,d?null:t].filter(Boolean);d&&m.includes(t)||P(l)&&g.includes(l.graphic)?i.activeTool==null&&(i.activeTool=this):i.activeTool=null}})]);const e=o=>{this.addHandles(o.events.on("grab-changed",a=>{"georeference"in t&&(a.action==="start"?t.opacity*=.5:a.action==="end"&&(t.opacity*=2))}))};this._dragManipulation.forEachManipulator(e),this._rotateManipulation.forEachManipulator(e),this._scaleManipulations.forEach(o=>o.forEachManipulator(e)),this.addHandles([i.on("key-down",o=>{i.activeTool===this&&(o.key!==S.shift||o.repeat||(this.preserveAspectRatio==null&&(this._preserveAspectRatio.enabled=!this._preserveAspectRatio.enabled),this.snapRotation==null&&(this._snapRotation.enabled=!this._snapRotation.enabled),this._isModifierActive=!0,o.stopPropagation()),o.key!==S.toggleOpacity||o.repeat||("georeference"in t&&(t.opacity*=this._isOpacityToggled?2:.5,this._isOpacityToggled=!this._isOpacityToggled),o.stopPropagation()),o.key!==S.primaryKey||o.repeat||(this._factor=Me,o.stopPropagation()),this._isModifierActive&&(o.key===S.plus&&(this._scale(this._factor),o.stopPropagation()),o.key===S.minus&&(this._scale(-this._factor),o.stopPropagation()),o.key===S.up&&(this._move(0,this._factor),o.stopPropagation()),o.key===S.down&&(this._move(0,-this._factor),o.stopPropagation()),o.key===S.left&&(this._move(-this._factor,0),o.stopPropagation()),o.key===S.right&&(this._move(this._factor,0),o.stopPropagation())))}),i.on("key-up",o=>{i.activeTool===this&&(o.key===S.shift&&(this.preserveAspectRatio==null&&(this._preserveAspectRatio.enabled=!this._preserveAspectRatio.enabled),this.snapRotation==null&&(this._snapRotation.enabled=!this._snapRotation.enabled),this._isModifierActive=!1,o.stopPropagation()),o.key===S.primaryKey&&(this._factor=Re,o.stopPropagation()))})])}async _loadProjectionEngine(){const i=p(this._graphic.geometry);return Rt(i.spatialReference,this.view.spatialReference)}_updateDisplayPlaneConrers(i){const{basis1:t,basis2:e,origin:o}=i,a=this._cornerA;$(a,o,t),$(a,a,e);const r=this._cornerB;$(r,o,t),L(r,r,e);const n=this._cornerC;L(n,o,t),L(n,n,e);const s=this._cornerD;L(s,o,t),$(s,s,e)}_getInfo(){return{editGeometryOperations:this._editGeometryOperations,plane:this._plane,displayPlane:this._displayPlane}}_updateGraphics(i,t){i.action==="start"&&(this._sharedUndoStack.push({tool:this,operation:t}),this._sharedRedoStack.length=0),this.updateGraphics()}_scale(i){var n;const t=this._editGeometryOperations,e=[];for(const s of t.data.components)e.push(...s.vertices);const o=(n=t.data.geometry.extent)==null?void 0:n.width,a=(o+i*this.view.resolution)/o,r=t.scaleVertices(e,this._plane.origin,qt,a,a,R.NEW_STEP,mt.REPLACE);this._sharedUndoStack.push({tool:this,operation:r}),this._sharedRedoStack.length=0,this.updateGraphics()}_move(i,t){const e=this._editGeometryOperations,o=[];for(const r of e.data.components)o.push(...r.vertices);const a=e.moveVertices(o,i*this.view.resolution,t*this.view.resolution,0,R.NEW_STEP);this._sharedUndoStack.push({tool:this,operation:a}),this._sharedRedoStack.length=0,this.updateGraphics()}};c([_()],b.prototype,"_plane",null),c([_()],b.prototype,"_backgroundGraphicGeometry",null),c([_()],b.prototype,"_rotateGraphicGeometry",null),c([_()],b.prototype,"_scaleGraphicGeometries",null),c([_()],b.prototype,"preserveAspectRatio",void 0),c([_()],b.prototype,"snapRotation",void 0),c([_({constructOnly:!0,nonNullable:!0})],b.prototype,"target",void 0),c([_({constructOnly:!0})],b.prototype,"view",void 0),b=c([it("esri.views.2d.interactive.editingTools.TransformTool")],b);const X={redo:"r",undo:"z"};let M=class extends Bt{constructor(i){super(i),this._transformTool=null,this._controlPointsTransformTool=null,this._advancedModeTransformTool=null,this._activeTool=null,this._sharedUndoStack=[],this._sharedRedoStack=[],this._originalOpacity=null,this.activeHandle=0}initialize(){const{view:i,mediaElement:t,preserveAspectRatio:e,snapRotation:o,advancedMode:a}=this;this._originalOpacity=t.opacity,this._transformTool=new b({target:t,view:i,preserveAspectRatio:e,snapRotation:o}),this._controlPointsTransformTool=new k({mediaElement:t,view:i}),this._advancedModeTransformTool=new k({mediaElement:a.mediaElement,view:a.view}),this._transformTool.setSharedUndoStack(this._sharedUndoStack),this._transformTool.setSharedRedoStack(this._sharedRedoStack),this._controlPointsTransformTool.setSharedUndoStack(this._sharedUndoStack),this._controlPointsTransformTool.setSharedRedoStack(this._sharedRedoStack),this._advancedModeTransformTool.setSharedUndoStack(this._sharedUndoStack),this._advancedModeTransformTool.setSharedRedoStack(this._sharedRedoStack);const r=p(t.georeference),n=p(a.mediaElement.georeference);a.view.tools.addMany([this._advancedModeTransformTool]),"controlPoints"in n&&"controlPoints"in r&&this.addHandles([a.view.on("key-down",s=>{s.key===X.undo&&this.canUndo()&&(this.undo(),s.stopPropagation()),s.key===X.redo&&this.canRedo()&&(this.redo(),s.stopPropagation())}),a.view.on("focus",async s=>{this._controlPointsTransformTool.removeHighlightActiveHandle(),this._advancedModeTransformTool.highlightActiveHandle()}),I(()=>n.controlPoints,s=>{var l;r.controlPoints=p(s).map(({sourcePoint:d},m)=>({sourcePoint:d,mapPoint:p(r.controlPoints)[m].mapPoint})),(l=this._activeTool)==null||l.refresh()}),I(()=>this._controlPointsTransformTool.activeHandle,s=>{this._advancedModeTransformTool.updateActiveHandle(s),this.activeHandle=s}),I(()=>this._advancedModeTransformTool.activeHandle,s=>{this._controlPointsTransformTool.updateActiveHandle(s),this.activeHandle=s})]),this.addHandles([i.on("key-down",s=>{s.key===X.undo&&this.canUndo()&&(this.undo(),s.stopPropagation()),s.key===X.redo&&this.canRedo()&&(this.redo(),s.stopPropagation())}),i.on("focus",async s=>{this._advancedModeTransformTool.removeHighlightActiveHandle(),this._controlPointsTransformTool.highlightActiveHandle()})]),i.tools.addMany([this._transformTool,this._controlPointsTransformTool]),i.activeTool=this._transformTool,this._activeTool=this._transformTool,i.focus()}destroy(){var i,t;(i=this._transformTool)==null||i.destroy(),(t=this._controlPointsTransformTool)==null||t.destroy(),this._transformTool=null,this._controlPointsTransformTool=null,this._advancedModeTransformTool=null,this._activeTool=null,this._sharedUndoStack=null,this._sharedRedoStack=null}canUndo(){return this._sharedUndoStack.length>0}canRedo(){return this._sharedRedoStack.length>0}undo(){var i;if(this._sharedUndoStack.length>0){const{tool:t,operation:e}=this._sharedUndoStack.pop();t!==this._activeTool&&t.refresh(),e.undo(),t.updateGraphics(),this._sharedRedoStack.push({tool:t,operation:e}),this._activeTool!==t&&((i=this._activeTool)==null||i.refresh())}}redo(){var i;if(this._sharedRedoStack.length>0){const{tool:t,operation:e}=this._sharedRedoStack.pop();t!==this._activeTool&&t.refresh(),e.apply(),t.updateGraphics(),this._sharedUndoStack.push({tool:t,operation:e}),this._activeTool!==t&&((i=this._activeTool)==null||i.refresh())}}refresh(){this._activeTool.refresh()}reset(){this._activeTool.reset(),this._advancedModeTransformTool.reset()}async enableAdvancedMode(){this.view.activeTool=this._controlPointsTransformTool,this._activeTool=this._controlPointsTransformTool,this._activeTool.refresh(),await this.advancedMode.view.when(),this.advancedMode.view.activeTool=this._advancedModeTransformTool,this._originalOpacity=this._controlPointsTransformTool.mediaElement.opacity,this._controlPointsTransformTool.mediaElement.opacity=.25*this._originalOpacity}disableAdvancedMode(){this.view.activeTool=this._transformTool,this._activeTool=this._transformTool,this._activeTool.refresh(),this.advancedMode.view.activeTool=null,this._controlPointsTransformTool.mediaElement.opacity=this._originalOpacity}};c([_()],M.prototype,"activeHandle",void 0),c([_({constructOnly:!0})],M.prototype,"advancedMode",void 0),c([_()],M.prototype,"preserveAspectRatio",void 0),c([_()],M.prototype,"snapRotation",void 0),c([_({constructOnly:!0,nonNullable:!0})],M.prototype,"mediaElement",void 0),c([_({constructOnly:!0})],M.prototype,"view",void 0),M=c([it("esri.views.2d.interactive.editingTools.MediaTransformToolsWrapper")],M);export{k as ControlPointsTransformTool,V as DrawGraphicTool2D,M as MediaTransformToolsWrapper,b as TransformTool};
