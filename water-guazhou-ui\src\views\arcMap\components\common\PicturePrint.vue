<template>
  <div>
    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
  </div>
</template>
<script lang="ts" setup>
import { Printer } from '@element-plus/icons-vue'
import { v4 as uuidv4 } from 'uuid'
import PrintTemplate from '@arcgis/core/rest/support/PrintTemplate.js'
import { max } from 'lodash-es'
import { IFormIns } from '@/components/type'
import {
  createGraphic,
  createRect,
  excutePrintTask,
  extentTo,
  getGraphicLayer,
  getScaleOnExtent,
  initDrawer,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper'
import { SLMessage } from '@/utils/Message'
import { getPrintFormat, getPrintTemplate } from '../../config'
import { downloadFile } from '@/utils/fileHelper'

const props = defineProps<{
  view?: __esri.MapView
}>()
const staticState: {
  drawer?: __esri.Draw
  drawAction?: __esri.DrawAction
  graphicsLayer?: __esri.GraphicsLayer
  graphic?: __esri.Graphic
} = {}
const refForm = ref<IFormIns>()
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '标题'
      },
      fields: [
        {
          type: 'input',
          field: 'title',
          rules: [{ required: true, message: '请输入标题' }]
        }
      ]
    },
    {
      fieldset: {
        desc: '格式'
      },
      fields: [{ type: 'select', field: 'type', options: getPrintFormat() }]
    },
    {
      fieldset: {
        desc: '高级设置'
      },
      fields: [
        {
          type: 'radio',
          field: 'range',
          label: '出图范围',
          options: [
            { label: '当前范围', value: 'current' },
            { label: '绘制范围', value: 'draw' }
          ],
          onChange: val => rangeChange(val)
        },
        { type: 'input', field: 'username', label: '出图人员' },
        { type: 'input', field: 'depart', label: '出图单位' },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              svgIcon: shallowRef(Printer),
              click: () => startPrint(),
              text: '打印'
            }
          ]
        }
      ]
    },
    {
      id: 'print-result',
      fieldset: {
        desc: '打印结果'
      },
      fields: [
        {
          type: 'list',
          data: [],
          style: { height: '100px' },
          itemStyle: row => {
            return {
              textDecoration: row.url ? 'underline' : 'none',
              color: '#00ff00'
            }
          },
          formatter: (val, row) => {
            if (!val) return val
            return (
              val
              + (row.status === 'success'
                ? '（打印完成,点击下载！）'
                : row.status === 'printing'
                  ? '（正在打印...）'
                  : row.status === 'failed'
                    ? '打印失败!'
                    : '')
            )
          },
          displayField: 'title',
          nodeClick: item => {
            item.url && downloadFile(item.url, item.title)
          }
        }
      ]
    }
  ],
  defaultValue: {
    range: 'current',
    type: 'pdf'
  }
})
const initDraw = () => {
  if (!props.view) return
  setMapCursor('crosshair')
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'proint-layer',
    title: '打印绘制图层'
  })
  staticState.drawer = initDrawer(props.view)
  staticState.drawAction = staticState.drawer?.create('rectangle')
  staticState.drawAction?.on('vertex-add', drawRect)
  staticState.drawAction?.on('cursor-update', drawRect)
  staticState.drawAction?.on('draw-complete', () => {
    setMapCursor('')
  })
}

const drawRect = e => {
  props.view?.graphics.removeAll()
  const vertices = e?.vertices || []
  const rect = createRect(vertices, props.view?.spatialReference)
  const rectGraphic = rect
    && createGraphic({
      geometry: rect,
      symbol: setSymbol('polygon') as any
    })
  staticState.graphic = rectGraphic
  rectGraphic && props.view?.graphics.add(rectGraphic)
}

const startPrint = async () => {
  if (!props.view) return
  const res = await refForm.value?.Submit()
  if (res === false) return
  if (refForm.value?.dataForm.range === 'draw' && !staticState.graphic) {
    SLMessage.warning('请先绘制打印范围')
    return
  }
  const result = {
    id: uuidv4(),
    url: '',
    status: 'printing',
    title: refForm.value?.dataForm.title
  }
  const printResultList = FormConfig.group.find(
    item => item.id === 'print-result'
  )?.fields[0] as IFormList
  printResultList.data.unshift(result)
  try {
    // await refForm.value?.Submit()
    // const mapjson = toJson(props.view, '0')
    // const dataForm = refForm.value?.dataForm || {}
    // mapjson.layoutOptions.authorText = dataForm.username
    // mapjson.layoutOptions.copyrightText = dataForm.depart

    // mapjson.layoutOptions.titleText = dataForm.title
    // if (dataForm.type === 'draw') {
    //   mapjson.mapOptions.scale = getScaleOnExtent(staticState.graphic?.geometry?.extent)
    //   mapjson.mapOptions.extent = staticState.graphic?.geometry.extent.toJSON()
    // }
    // mapjson.layoutOptions.customTextElements.push({ company: dataForm.depart })
    // const url = window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPrintGPService
    // const jobinfo = await submitGPJob(url, {
    //   Web_Map_as_JSON: JSON.stringify(mapjson),
    //   Format: refForm.value?.dataForm.type, // "PDF"
    //   Layout_Template: 'Default', // that.domObj.find(".layout").val(),//"DefaultA0L"
    //   Georef_info: 'False',
    //   Template_Folder: window.SITE_CONFIG.GIS_CONFIG.gisPrintTemplatePath + '/0'
    // })
    // await jobinfo.waitForJobCompletion()
    // if (jobinfo.jobStatus === 'job-succeeded') {
    //   // const res = await jobinfo.fetchResultData('summary')
    //   result.title = (refForm.value?.dataForm.title || '') + '（打印完成）'
    // } else if (jobinfo.jobStatus === 'job-cancelled') {
    //   result.title = (refForm.value?.dataForm.title || '') + '（取消打印）'
    // } else if (jobinfo.jobStatus === 'job-cancelling') {
    //   result.title = (refForm.value?.dataForm.title || '') + '（正在取消）'
    // } else if (jobinfo.jobStatus === 'job-failed') {
    //   result.title = (refForm.value?.dataForm.title || '') + '（打印失败）'
    // }
    const dataForm = refForm.value?.dataForm || {}
    if (dataForm.range === 'draw' && staticState.graphic) {
      await extentTo(props.view, staticState.graphic.geometry.extent, true)
    }
    const template = getPrintTemplate().find(
      item => item.value === 'a3-landscape'
    )
    const length: number = max(template?.data.pageSize) || 42.0
    const scale = (getScaleOnExtent(staticState.graphic?.geometry.extent) || 0)
      / (length / 100)
    const res = await excutePrintTask(
      props.view,
      new PrintTemplate({
        format: dataForm.type,
        exportOptions: {
          dpi: 96
          // width: staticState.curTemplate?.width,
          // height: staticState.curTemplate?.height
        },
        outScale: dataForm.range === 'draw' ? scale : props.view.scale,
        layout: 'a3-landscape',
        layoutOptions: {
          titleText: dataForm.title,
          authorText: dataForm.username,
          copyrightText: dataForm.depart,
          scalebarUnit: 'Meters',
          elementOverrides: {
            'North Arrow': {
              visible: true
            }
          }
        },
        showLabels: true
      })
    )
    result.status = 'success'
    result.url = res.url
  } catch (error) {
    result.status = 'failed'
  }
  const index = printResultList.data.findIndex(item => item.id === result.id)
  index !== -1 && printResultList.data.splice(index, 1)
  printResultList.data.unshift(result)
}
const destroy = () => {
  staticState.graphicsLayer && props.view?.map.remove(staticState.graphicsLayer)
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
  setMapCursor('')
}
const rangeChange = val => {
  props.view?.graphics.removeAll()
  staticState.graphic = undefined
  if (val === 'draw') initDraw()
  else destroy()
}
onBeforeUnmount(() => {
  destroy()
})
</script>
<style lang="scss" scoped></style>
