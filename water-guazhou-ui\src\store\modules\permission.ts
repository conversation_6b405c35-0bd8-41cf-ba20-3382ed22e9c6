import { getConstantsAttributeById } from '@/api/constants';
import { GetApplicationAsyncRoutes } from '@/api/menu/source';
import { getCurUserBtnPerms } from '@/api/permission';
import {
  asyncRouterMap,
  asyncRouterTop,
  constantRouterMap
} from '@/router/modules';
import { filterAsyncRouter, Set_navApps } from '@/utils/RouterHelper';
import { bigScreenVisibleSet } from '@/utils/StoreHelper';
import { useAppStore } from './app';
import { useBusinessStore } from './business';
import { useUserStore } from './user';

export const usePermissionStore = defineStore('permission', () => {
  const initialState: Store_Permission_State = {
    // 所有路由
    routers: [],
    // 动态路由（异步加载）
    addRouters: [],
    // 按钮权限
    btnPerms: [],
    usebtnperms: localStorage.getItem('usebtnperms') !== 'false',
    // 企业路由
    tenantRouters: [],
    // 未处理的动态路由，（拿到的后台路由数据）
    untreatedTRouter: sessionStorage.getItem('untreatedTRouter')
      ? sessionStorage.getItem('untreatedTRouter')
      : [],
    // sys-admin路由 用于返回sysAdmin
    sysRouter: [],
    // 自定义ifream大屏
    bigScreenR: [],
    // 自定义ifream大屏  sys-admin
    sysBigScreenR: [],
    defaultRoutePath: '/'
  };
  const state = reactive<Store_Permission_State>(initialState);
  const actions = {
    /**
     * 设置异步路由
     * @param routers 路由
     */
    SET_ASYNC_ROUTERS: (routers: any[]) => {
      const topRouters = asyncRouterTop;
      // const mapRouters = asyncRouterMap;
      const asyncMainRouterMap = [
        ...(topRouters || []),
        ...routers,
        // ...(mapRouters || []),
        ...constantRouterMap
      ];

      state.addRouters = routers || [];
      state.routers = asyncMainRouterMap;
    },
    ADD_T_ROUTERS: (tenantRouters) => {
      state.sysRouter = state.routers;
      state.tenantRouters = tenantRouters;
      state.routers = tenantRouters.concat(constantRouterMap);
    },
    SYS_ROUTER: () => {
      state.routers = state.sysRouter;
      bigScreenVisibleSet(state.sysBigScreenR, state.routers);
      state.bigScreenR = state.sysBigScreenR;
      state.tenantRouters = [];
      state.untreatedTRouter = [];
      sessionStorage.removeItem('untreatedTRouter');
    },
    SET_SYS_BIG_S: (routerInfo) => {
      bigScreenVisibleSet(routerInfo, state.routers);
      state.bigScreenR = routerInfo;
    },
    SET_SYS_T_BIG: (routerInfo) => {
      state.sysBigScreenR = state.bigScreenR;
      bigScreenVisibleSet(routerInfo, state.routers);
      state.bigScreenR = routerInfo;
    },
    SET_CUR_USER_BTN_PERMS: (payload) => {
      state.btnPerms = payload;
    },
    UNTREATED_T_ROUTER: (untreatedTRouter) => {
      // 未处理的project 的路由菜单
      state.untreatedTRouter = untreatedTRouter;
      sessionStorage.setItem(
        'untreatedTRouter',
        JSON.stringify(untreatedTRouter)
      );
    },
    SET_defaultRoutePath: (path) => {
      state.defaultRoutePath = path;
    },
    async GenerateRoutes() {
      const businessStore = useBusinessStore();
      const appStore = useAppStore();
      const userStore = useUserStore();
      const roles = userStore.user?.authority?.split(',') || [];
      let getRouter: any[] = [];
      if (roles.indexOf('SYS_ADMIN') === -1) {
        const { appid } = appStore;
        const { curNavs } = businessStore;
        if (!appid || !curNavs.length) {
          await Set_navApps();
        }
        if (appid) {
          const routeres = await GetApplicationAsyncRoutes(appid);
          // 应对网络慢时频繁点击菜单导致的菜单于侧边菜单不对应的bug
          const flag = appid === appStore.appid;

          flag && (getRouter = routeres.data || []);
        }
      }
      actions.SET_ASYNC_ROUTERS(filterAsyncRouter(getRouter) || []);
      if (roles.indexOf('CUSTOMER_USER') !== -1) {
        //
      } else {
        // 拿取扩展大屏
        const params = {
          type: 'sysBigScreen',
          key: 'sys_admin'
        };
        if (roles.indexOf('TENANT_ADMIN') !== -1) {
          params.type = 'tenantBigScreen';
          params.key = userStore.tenantId;
        }
        const res = await getConstantsAttributeById(params);
        if (res.data[0]) {
          const rData = JSON.parse(res.data[0].value);
          const newRouter: any[] = [];
          for (const item in rData) {
            const info = rData[item];
            if (info.state) {
              const router = {
                path: info.url,
                name: item,
                component: () => import('@/views/extendPage/sysPage.vue'),
                meta: { title: info.name, icon: 'device' }
              };
              newRouter.push(router);
            }
          }
          actions.SET_SYS_BIG_S(newRouter);
        }
      }
    },
    untreatedTRouter(untreatedTRouter) {
      actions.UNTREATED_T_ROUTER(untreatedTRouter);
    },
    sysRouter() {
      actions.SYS_ROUTER();
    },
    setBigScreen(routerInfo) {
      // console.log(routerInfo, '---routerInfo')
      if (routerInfo.sysChange) {
        actions.SET_SYS_T_BIG(routerInfo.router);
      } else {
        actions.SET_SYS_BIG_S(routerInfo.router);
      }
    },
    async GetCurUserBtnPerms() {
      const res = await getCurUserBtnPerms();
      // console.log(res, 'GetCurUserBtnPermsGetCurUserBtnPerms')
      if (res.status === 200) actions.SET_CUR_USER_BTN_PERMS(res.data);
    },
    SetCurUserBtnPerms(userBtnPerms) {
      actions.SET_CUR_USER_BTN_PERMS(userBtnPerms);
    }
  };
  return { ...toRefs(state), ...actions };
});
