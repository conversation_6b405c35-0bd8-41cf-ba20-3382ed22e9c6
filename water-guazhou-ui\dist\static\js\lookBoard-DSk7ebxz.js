import{_ as H}from"./index-C9hz-UZb.js";import{_ as Q}from"./Search-NSrhrIa_.js";import{ac as N,d as X,c as L,a0 as Z,j as tt,cN as et,r as A,bB as F,o as ot,ay as at,g,h as O,F as b,p as m,q as f,n as w,aJ as B,i as M,cs as it,bh as I,aw as P,aB as j,an as $,bl as rt,bm as st,C as nt}from"./index-r0dFAfgr.js";import{p as lt}from"./padStart-BKfyZZDO.js";import{G as pt,a as ct,b as mt}from"./bengzhan-Dc7fbek7.js";import{G as ut}from"./shuichangzonglan-HwbtusbI.js";import dt from"./RightDrawerMap-D5PhmGFO.js";import{c as ht,n as yt}from"./onemap-CEunQziB.js";import{w as ft}from"./Point-WxyopZva.js";import{g as vt,n as gt}from"./MapView-DaoQedLH.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as _t}from"./ViewHelper-BGCZjxXH.js";import{g as bt}from"./URLHelper-B9aplt5w.js";import"./ArcView-DpMnCY82.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./widget-BcWKanF2.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const G=(V,d)=>{const u=["#459f19","#82a12f","#3a89ac"],S=Array.from({length:24}).map((v,y)=>lt(y.toString(),2,"0")),T=[],_=[],k=[],h=(d==null?void 0:d.beforeYesterday)||{},l=(d==null?void 0:d.today)||{},D=(d==null?void 0:d.yesterday)||{};for(const v in h||{}){const y=h[v];for(const c in y)T.push(y[c])}for(const v in D||{}){const y=D[v];for(const c in y)_.push(y[c])}for(const v in l||{}){const y=l[v];for(const c in y)k.push(y[c])}return{tooltip:{trigger:"axis"},legend:{show:!0,right:"center",top:10,textStyle:{color:"#aaa"}},xAxis:{type:"category",boundaryGap:!1,data:S||[],axisLine:{lineStyle:{color:"#aaa"}},axisTick:{lineStyle:{color:"#aaa"}},axisLabel:{textStyle:{color:"#aaa"}}},yAxis:{type:"value",name:V,nameLoacation:"top",splitLine:{lineStyle:{color:"#aaa",opacity:.2}},splitArea:{show:!1},axisLine:{lineStyle:{color:"#aaa"}},axisTick:{lineStyle:{color:"#aaa"}},axisLabel:{textStyle:{color:"#aaa"}},boundaryGap:[0,"100%"]},grid:{top:40,left:50,right:20,bottom:30},series:[{name:"前日",type:"line",symbol:"none",sampling:"lttb",itemStyle:{color:u[0]},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:N(u[0],.3)},{offset:1,color:N(u[0],.1)}],global:!1}},data:T||[]},{name:"昨日",type:"line",symbol:"none",sampling:"lttb",itemStyle:{color:u[1]},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:N(u[1],.3)},{offset:1,color:N(u[1],.1)}],global:!1}},data:_||[]},{name:"今日",type:"line",symbol:"none",sampling:"lttb",itemStyle:{color:u[2]},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:N(u[2],.3)},{offset:1,color:N(u[2],.1)}],global:!1}},data:k||[]}]}},St={class:"detail-wrapper"},xt={class:"wrapper"},wt={class:"statistics"},kt={class:"item-inner"},Ct={class:"item-text title"},Nt={class:"item-text count"},Mt={class:"item-text unit"},It={class:"title_card"},Tt={key:0,class:"card-box"},Dt={class:"card-content"},Lt={class:"monitor-box"},Ft={class:"total"},Pt={class:"label"},Wt={key:0,class:"unit"},Bt={class:"inner-card"},jt=X({__name:"lookBoard",setup(V){var z;const d=L(0),u=Z(),S=tt(),T=L(),_=L(),{proxy:k}=et(),h={},l=A({Statistics:[{className:"text-blue",title:"上月总供水量",count:0,unit:"m³"},{className:"text-green",title:"本月总供水量",count:0,unit:"m³"},{className:"text-orange",title:"年累计供水量",count:0,unit:"m³"},{className:"text-purple",title:"报警数",count:0,unit:"个"}],shuichangname:[],ShuiChangDatas:[],windows:[]}),D=A({filters:[{type:"select-tree",label:"查看范围",field:"projectId",checkStrictly:!0,options:u.projectList,nodeClick:t=>{d.value=0,u.SET_selectedProject(t),W(t)}}],defaultParams:{projectId:(z=u.selectedProject)==null?void 0:z.value}}),W=async t=>{var p;ut({stationType:"水厂",projectId:(t==null?void 0:t.value)||((p=u.selectedProject)==null?void 0:p.value)}).then(i=>{var r,s,o,n;l.Statistics[0].count=(((r=i.data)==null?void 0:r.lastMonthTotal)||0).toFixed(2),l.Statistics[1].count=(((s=i.data)==null?void 0:s.monthTotal)||0).toFixed(2),l.Statistics[2].count=(((o=i.data)==null?void 0:o.total)||0).toFixed(2),l.Statistics[3].count=(((n=i.data)==null?void 0:n.alarm)||0).toFixed(0)}).catch(()=>{l.Statistics[0].count=0,l.Statistics[1].count=0,l.Statistics[2].count=0,l.Statistics[3].count=0}),pt({projectId:t==null?void 0:t.value,stationType:"水厂"}).then(i=>{var r,s;l.shuichangname=[],(r=i.data)==null||r.map(o=>{l.shuichangname.push({title:o.name,stationData:o})}),v((s=l.shuichangname[0])==null?void 0:s.stationData)})};async function v(t){let p={},i={},r={};try{p=await ct({stationId:(t==null?void 0:t.stationId)??""}),i=await mt({stationId:(t==null?void 0:t.stationId)??""}),console.log("使用模拟清水池液位数据"),r={data:R()}}catch{}l.ShuiChangDatas=[],l.ShuiChangDatas.push({id:t==null?void 0:t.stationId,title:t==null?void 0:t.name,curMonitorData1:{value:4011,unit:"m³",propertyName:"出水流量(m³)",lineChartOption:G("出水流量(m³)",i.data)},curMonitorData2:{value:.229,unit:"Mpa",propertyName:"出水压力(Mpa)",lineChartOption:G("出水压力(Mpa)",p.data)},curMonitorData3:{value:3.5,unit:"m",propertyName:"清水池液位(m)",lineChartOption:G("清水池液位(m)",r.data)},monitorData:(t==null?void 0:t.dataList)||[]}),c.value=l.ShuiChangDatas[0],F(()=>{if(!t)return;const s=k.$refs["refChart"+t.stationId+"-1"];s!=null&&s.resize&&s.resize();const o=k.$refs["refChart"+t.stationId+"-2"];o!=null&&o.resize&&o.resize();const n=k.$refs["refChart"+t.stationId+"-3"];n!=null&&n.resize&&n.resize()})}ot(()=>{W()});const y=t=>{c.value=l.ShuiChangDatas[t],v(l.shuichangname[t].stationData)},c=L({}),E=async t=>{var p,i;h.view=t,(p=_.value)==null||p.toggleCustomDetail(!0),await F(),(i=_.value)==null||i.toggleCustomDetailMaxmin("max"),await new Promise(r=>setTimeout(r,1e3)),_t(h.view,r=>{var o,n,e;const s=(o=r.results)==null?void 0:o[0];if(s&&s.type==="graphic"){const a=(e=(n=s.graphic)==null?void 0:n.attributes)==null?void 0:e.row;a!=null&&a.fromLookBoard&&Y(a)}}),await K()},R=()=>{const t={},p={},i={};for(let r=0;r<24;r++){const s=r.toString().padStart(2,"0"),o=3.5,n=Math.sin(r*.3)*.3+Math.random()*.1-.05;t[s]={[s]:Math.max(0,Number((o+n).toFixed(2)))};const e=Math.sin(r*.25)*.4+Math.random()*.15-.075;p[s]={[s]:Math.max(0,Number((o+e).toFixed(2)))};const a=Math.sin(r*.35)*.2+Math.random()*.1-.05;i[s]={[s]:Math.max(0,Number((o+a).toFixed(2)))}}return{beforeYesterday:t,yesterday:p,today:i}},Y=async t=>{var r,s,o;if(!t||!h.view)return;l.windows.length=0;const p=h.view.graphics.find(n=>{var e,a,C,x;return((a=(e=n.attributes)==null?void 0:e.row)==null?void 0:a.id)===t.id||((x=(C=n.attributes)==null?void 0:C.row)==null?void 0:x.stationId)===t.stationId});if(!p)return;const i=p.geometry;try{const e=((r=(await yt({stationId:t.stationId||t.id})).data)==null?void 0:r.data)||{},a=[{label:"水厂名称",value:t.name||t.stationName||"水厂"}];e.todayWaterSupply!==void 0&&e.todayWaterSupply!==null&&a.push({label:"今日供水量",value:Number(e.todayWaterSupply).toFixed(1),unit:"m³"}),e.yesterdayWaterSupply!==void 0&&e.yesterdayWaterSupply!==null&&a.push({label:"昨日供水量",value:Number(e.yesterdayWaterSupply).toFixed(1),unit:"m³"}),e.monthWaterSupply!==void 0&&e.monthWaterSupply!==null&&a.push({label:"本月供水量",value:Number(e.monthWaterSupply).toFixed(1),unit:"m³"}),e.todayWaterTake!==void 0&&e.todayWaterTake!==null&&a.push({label:"今日取水量",value:Number(e.todayWaterTake).toFixed(1),unit:"m³"}),e.status!==void 0&&a.push({label:"运行状态",value:e.status===1?"正常":"异常"});const C=h.view.toScreen(i),x={visible:!1,x:i.x,y:i.y,offsetY:-30,title:t.name||t.stationName||"水厂",attributes:{values:a,id:t.id||t.stationId}};l.windows.push(x),await F(),(s=_.value)==null||s.openPop(t.id||t.stationId)}catch{const e={visible:!1,x:i.x,y:i.y,offsetY:-30,title:t.name||t.stationName||"水厂",attributes:{values:[{label:"水厂名称",value:t.name||t.stationName||"水厂"},{label:"数据获取",value:"暂时无法获取详细数据"}],id:t.id||t.stationId}};l.windows.push(e),await F(),(o=_.value)==null||o.openPop(t.id||t.stationId)}},K=async()=>{var t,p;try{if(!h.view)return;h.view.graphics.removeAll();try{const i=await ht({projectId:(t=u.selectedProject)==null?void 0:t.value}),r=((p=i.data)==null?void 0:p.data)||i.data||[];Array.isArray(r)&&r.length>0&&r.filter(o=>{const n=o.name||o.stationName||"";return n.includes("水厂")&&!n.includes("污水厂")}).forEach((o,n)=>{var a;const e=(a=o.location)==null?void 0:a.split(",");if((e==null?void 0:e.length)===2&&e[0]&&e[1]){const C=parseFloat(e[0]),x=parseFloat(e[1]);if(!isNaN(C)&&!isNaN(x)){const U=new ft({longitude:C,latitude:x,spatialReference:h.view.spatialReference}),q=new vt({geometry:U,symbol:new gt({width:40,height:40,yoffset:20,url:bt("水厂.png")}),attributes:{row:{...o,id:o.stationId||o.id||`water-plant-${n}`,stationId:o.stationId||o.id,name:o.name||o.stationName||`水厂${n+1}`,fromLookBoard:!0}}});h.view.graphics.add(q)}}})}catch{}}catch{}},J=(t,p)=>{};return(t,p)=>{const i=rt,r=st,s=Q,o=H,n=at("VChart");return g(),O(dt,{ref_key:"refMap",ref:_,title:"监测总览","detail-max-min":!0,"hide-right-drawer":!0,"hide-detail-close":!0,windows:l.windows,onMapLoaded:E,onPopToggle:J},{"detail-header":b(()=>p[1]||(p[1]=[m("span",null,"监测总览",-1)])),"detail-default":b(()=>[m("div",St,[m("div",xt,[f(o,{class:"wrapper-content hei",title:"",overlay:""},{default:b(()=>[m("div",wt,[(g(!0),w(j,null,B(l.Statistics,(e,a)=>(g(),w("div",{key:a,class:"statistics-item"},[m("div",kt,[f(M(it),{icon:a===0?"material-symbols:water-dropdown-outline":a===1?"ep:calendar":a===2?"ep:takeaway-box":a===3?"ep:bell":"",size:30},null,8,["icon"]),m("span",Ct,I(e.title),1),m("div",{class:P(["total",e.className])},[m("span",Nt,I(e.count),1),m("span",Mt,I(e.unit),1)],2)])]))),128))]),m("div",It,[f(r,{modelValue:d.value,"onUpdate:modelValue":p[0]||(p[0]=e=>d.value=e),class:"demo-tabs",onTabChange:y},{default:b(()=>[(g(!0),w(j,null,B(l.shuichangname,(e,a)=>(g(),O(i,{key:a,label:e.title,name:a},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),f(s,{ref_key:"refSearch",ref:T,class:"serch",config:D},null,8,["config"])]),JSON.stringify(c.value)!=="{}"?(g(),w("div",Tt,[m("div",{class:P(["card-item",{isDark:M(S).isDark}])},[m("div",Dt,[f(o,{title:"监测数据",class:"inner-card left",overlay:""},{default:b(()=>[m("div",Lt,[(g(!0),w(j,null,B(c.value.monitorData,(e,a)=>(g(),w("div",{key:a,class:P(["monitor-item",{isDark:M(S).isDark}])},[m("span",{class:P(["count",e.status===!0?"text-green":e.status===!1?"text-red":"text-blue"])},I((e.value*1||0).toFixed(2)),3),m("span",Ft,[m("span",Pt,I(e.propertyName),1),e.unit?(g(),w("span",Wt,I(e.unit),1)):$("",!0)])],2))),128))])]),_:1}),m("div",Bt,[f(o,{class:"chart-item",title:c.value.curMonitorData1.propertyName},{default:b(()=>[f(n,{ref:"refChart"+c.value.id+"-1",theme:M(S).isDark?"dark":"light",option:c.value.curMonitorData1.lineChartOption},null,8,["theme","option"])]),_:1},8,["title"]),f(o,{class:"chart-item",title:c.value.curMonitorData2.propertyName},{default:b(()=>[f(n,{ref:"refChart"+c.value.id+"-2",theme:M(S).isDark?"dark":"light",option:c.value.curMonitorData2.lineChartOption},null,8,["theme","option"])]),_:1},8,["title"]),f(o,{class:"chart-item",title:c.value.curMonitorData3.propertyName},{default:b(()=>[f(n,{ref:"refChart"+c.value.id+"-3",theme:M(S).isDark?"dark":"light",option:c.value.curMonitorData3.lineChartOption},null,8,["theme","option"])]),_:1},8,["title"])])])],2)])):$("",!0)]),_:1})])])]),_:1},8,["windows"])}}}),Yo=nt(jt,[["__scopeId","data-v-f61c2d3e"]]);export{Yo as default};
