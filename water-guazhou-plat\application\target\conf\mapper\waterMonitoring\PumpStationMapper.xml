<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.thingsboard.server.dao.sql.waterMonitoring.PumpStationMapper">

    <!-- 获取泵站列表 -->
    <select id="getStationList" resultType="java.util.HashMap">
        SELECT
            p.id,
            p.name,
            '泵房' as type,
            p.address,
            '' as location,
            'normal' as status
        FROM sp_pump_house_storage p
        WHERE p.tenant_id = #{tenantId}
        ORDER BY p.name
    </select>

    <!-- 获取泵站详情 -->
    <select id="getStationDetail" resultType="java.util.HashMap">
        SELECT
            p.id as stationId,
            p.name as stationName,
            p.id as pumpId,
            p.name as pumpName,
            p.code as pumpCode,
            p.supply_method as pumpType,
            p.water_box_num as pumpNum,
            p.company_name as companyName,
            p.remark as performanceParameters
        FROM sp_pump_house_storage p
        WHERE p.tenant_id = #{tenantId}
        <if test="stationIds != null and stationIds.size() > 0">
            AND p.id IN
            <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                #{stationId}
            </foreach>
        </if>
        ORDER BY p.name
    </select>

    <!-- 应用泵站方案 -->
    <update id="applyScheme">
        UPDATE sp_pump_house_storage
        SET remark = CONCAT(COALESCE(remark, ''), ' 方案ID: ', #{schemeId})
        WHERE tenant_id = #{tenantId}
        <if test="stationIds != null and stationIds.size() > 0">
            AND id IN
            <foreach collection="stationIds" item="stationId" open="(" separator="," close=")">
                #{stationId}
            </foreach>
        </if>
    </update>

</mapper>
