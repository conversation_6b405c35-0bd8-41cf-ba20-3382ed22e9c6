package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Data;
import org.thingsboard.server.dao.model.sql.statistic.StatisticTimeUnit;
import org.thingsboard.server.dao.util.imodel.query.TimeableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.QueryIgnore;

@Data
public class WorkOrderCountRequest extends TimeableQueryEntity {
    @QueryIgnore
    private String statisticOrganizer;

    @QueryIgnore
    private String statisticType;

    // DAY、Month、YEAR
    @QueryIgnore
    private String timeUnit;

    public StatisticTimeUnit timeUnit() {
        try {
            return StatisticTimeUnit.valueOf(timeUnit);
        } catch (Exception ignore) {

        }
        return null;
    }

    public boolean isCountOrganizer() {
        return Boolean.parseBoolean(statisticOrganizer);
    }

    public boolean isCountType() {
        return Boolean.parseBoolean(statisticType);
    }

    public boolean byTime() {
        return timeUnit != null;
    }
}
