package org.thingsboard.server.dao.sql.dataSource;

import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.RestApiEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@SqlDao
public interface RestApiRepository extends CrudRepository<RestApiEntity, String> {
    List<RestApiEntity> findByTenantIdOrderByUpdateTime(@Param("tenantId") String tenantId);

    List<RestApiEntity> findByProjectIdOrderByUpdateTime(@Param("projectId") String projectId);

    @Override
    List<RestApiEntity> findAll();


    int countByTenantIdAndName(@Param("tenantId") String tenantId,
                               @Param("name") String name);


    List<RestApiEntity> findByProjectIdOrderByCreateTime(String projectId);
}
