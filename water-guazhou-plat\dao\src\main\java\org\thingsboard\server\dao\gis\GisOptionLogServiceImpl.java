package org.thingsboard.server.dao.gis;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisOptionLogListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisOptionLog;
import org.thingsboard.server.dao.sql.gis.GisOptionLogMapper;
import org.thingsboard.server.dao.sql.gis.GisOptionLogRepository;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class GisOptionLogServiceImpl implements GisOptionLogService {

    @Autowired
    private GisOptionLogRepository gisOptionLogRepository;

    @Autowired
    private GisOptionLogMapper gisOptionLogMapper;

    @Override
    public PageData<GisOptionLog> findList(GisOptionLogListRequest request, TenantId tenantId) {
        Page<GisOptionLog> pageRequest = new Page<>(request.getPage(), request.getSize());
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        IPage<GisOptionLog> pageResult = gisOptionLogMapper.findList(pageRequest, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void save(GisOptionLog entity, User currentUser) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));
            entity.setOptionTime(new Date());
            entity.setOptionUser(UUIDConverter.fromTimeUUID(currentUser.getUuidId()));
            entity.setOptionUserName(currentUser.getFirstName());
        }
        gisOptionLogRepository.save(entity);
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            gisOptionLogRepository.delete(id);
        }
    }
}
