import{_ as B}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as W}from"./CardTable-rdWOL4_6.js";import{_ as j}from"./CardSearch-CB_HNR-Q.js";import{m as h,d as E,c as w,r as _,s as T,b as d,S as k,c4 as H,o as O,g as Z,n as J,q as A,F as K,p as v,bh as Q,al as U,ak as X,bq as q,bL as ee,C as ae}from"./index-r0dFAfgr.js";import{f as V}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function te(u){return h({url:"/api/groundwater/level/list",method:"get",params:u})}function re(u){return h({url:"/api/groundwater/level",method:"post",data:u})}function oe(u){return h({url:"/api/groundwater/level/update",method:"post",data:u})}function F(u){return h({url:"/api/groundwater/level",method:"delete",data:u})}function se(u){return h({url:"/api/groundwater/level/change",method:"get",params:u})}function le(u){return h({url:"/api/groundwater/recharge/analyze",method:"get",params:u})}function ie(u){return h({url:"/api/station/list",method:"get",params:u})}const ne={class:"wrapper"},de={class:"analysis-container"},ce={class:"chart-container"},ue={class:"suggestion-container"},ge={class:"suggestion-content"},me=E({__name:"index",setup(u){const S=w(),p=w(),C=w(),L=w();let y=null;const i=_({selectedIds:[],suggestion:"",dialogVisible:!1,analysisData:{dates:[],values:[]}}),M=_({labelWidth:"100px",filters:[{type:"input",label:"测点名称",field:"stationName",onChange:()=>m()},{type:"date",label:"记录时间",field:"recordTime",onChange:()=>m()},{type:"select",label:"水位状态",field:"status",options:[{label:"正常",value:1},{label:"偏低",value:2},{label:"偏高",value:3}],onChange:()=>m()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",svgIcon:T(U),click:()=>m()},{perm:!0,type:"primary",text:"新增",svgIcon:T(X),click:()=>G()},{perm:!0,type:"danger",text:"批量删除",svgIcon:T(q),click:()=>$()}]}],defaultParams:{}}),g=_({title:"地下水水位数据列表",selectList:[],indexVisible:!0,columns:[{label:"测点名称",prop:"stationName",formatter:e=>e.stationName||e.wellName||"-"},{label:"测点位置",prop:"stationLocation",formatter:e=>e.stationLocation||e.wellLocation||"-"},{label:"水位高度(m)",prop:"waterLevel"},{label:"水位变化量(m)",prop:"levelChange"},{label:"地下水补给量(m³)",prop:"rechargeAmount"},{label:"降雨量(mm)",prop:"rainfallAmount"},{label:"记录时间",prop:"recordTime",formatter:e=>{const a=e.recordTime;return a?V(a,"YYYY-MM-DD HH:mm:ss"):""}},{label:"水位状态",prop:"status",formatter:e=>({1:"正常",2:"偏低",3:"偏高"})[e.status]||"-"},{label:"数据来源",prop:"dataSource",formatter:e=>({1:"手动录入",2:"设备采集"})[e.dataSource]||"-"},{label:"备注",prop:"remark"}],dataList:[],pagination:{page:1,limit:10,total:0,refreshData:({page:e,size:a})=>{g.pagination.page=e,g.pagination.limit=a,m()}},operations:[{perm:!0,text:"分析",svgIcon:T(ee),click:e=>Y(e)},{perm:!0,text:"删除",svgIcon:T(q),click:e=>P(e)}],handleSelectChange:e=>{i.selectedIds=e.map(a=>a.id),g.selectList=e}}),f=_({title:"地下水水位数据",dialogWidth:"500px",group:[{fields:[{type:"input",field:"id",hidden:!0},{type:"select",field:"stationId",label:"测点站点",required:!0,options:[],rules:[{required:!0,message:"请选择测点站点",trigger:"change"}],onChange:e=>R(e)},{type:"input",field:"stationName",label:"测点名称",hidden:!0},{type:"input",field:"stationLocation",label:"测点位置",hidden:!0},{type:"input-number",field:"waterLevel",label:"水位高度(m)",required:!0,rules:[{required:!0,message:"请输入水位高度",trigger:"blur"}]},{type:"input-number",field:"levelChange",label:"水位变化量(m)",required:!0},{type:"input-number",field:"rechargeAmount",label:"地下水补给量(m³)",required:!0},{type:"input-number",field:"rainfallAmount",label:"降雨量(mm)",required:!0},{type:"datetime",field:"recordTime",label:"记录时间",required:!0,valueFormat:"x",rules:[{required:!0,message:"请选择记录时间",trigger:"blur"}]},{type:"select",field:"status",label:"水位状态",options:[{label:"正常",value:1},{label:"偏低",value:2},{label:"偏高",value:3}],required:!0},{type:"select",field:"dataSource",label:"数据来源",options:[{label:"手动录入",value:1},{label:"设备采集",value:2}],required:!0},{type:"textarea",field:"remark",label:"备注"}]}],defaultValue:{},submit:async e=>{var a,t,n,r,l,c;try{f.submitting=!0;const o={...e,wellName:e.wellName||e.stationName,wellLocation:e.wellLocation||e.stationLocation};if(e.recordTime)try{if(typeof e.recordTime=="string"){const s=new Date(e.recordTime);isNaN(s.getTime())?(o.recordTime=Date.now(),console.warn("日期转换失败，使用当前时间")):o.recordTime=s.getTime()}else typeof e.recordTime=="number"?o.recordTime=e.recordTime:(o.recordTime=Date.now(),console.warn("未知的日期格式，使用当前时间"))}catch(s){o.recordTime=Date.now(),console.error("日期处理出错，使用当前时间:",s)}else o.recordTime=Date.now(),console.warn("没有记录时间，使用当前时间");if(delete o.measureDate,e.stationId&&(o.stationId=e.stationId),console.log("提交数据:",o),e.id){const s=await oe(o);console.log("更新响应:",s),s.data&&s.data.code===200?(d.success(s.data.message||"更新成功"),(a=p.value)==null||a.closeDialog(),i.dialogVisible=!1,m()):d.error(((t=s.data)==null?void 0:t.message)||((n=s.data)==null?void 0:n.msg)||"更新失败")}else{const s=await re(o);s.data&&s.data.code===200?(d.success(s.data.message||"添加成功"),(r=p.value)==null||r.closeDialog(),i.dialogVisible=!1,m()):d.error(((l=s.data)==null?void 0:l.message)||((c=s.data)==null?void 0:c.msg)||"添加失败")}}catch(o){console.error("保存失败",o),d.error("保存失败")}finally{f.submitting=!1}}}),I=_({title:"地下水涵养水位分析",dialogWidth:"800px",fullscreen:!1,group:[],defaultValue:{},cancel:!0}),m=async()=>{var e,a,t;try{g.loading=!0;const r={...((e=S.value)==null?void 0:e.queryParams)||{},page:g.pagination.page,size:g.pagination.limit};if(r.recordTime)try{const c=new Date(r.recordTime);isNaN(c.getTime())?(delete r.recordTime,console.warn("日期转换失败，已移除记录时间参数")):(r.recordTime=c.getTime(),console.log("记录时间转换为时间戳:",r.recordTime))}catch(c){delete r.recordTime,console.error("日期处理出错:",c)}const l=await te(r);if(l.data&&l.data.code===200){let c=[],o=0;l.data.data&&Array.isArray(l.data.data.data)?(c=l.data.data.data,o=l.data.data.total||0):l.data.data&&Array.isArray(l.data.data.records)?(c=l.data.data.records,o=l.data.data.total||0):Array.isArray(l.data.data)?(c=l.data.data,o=l.data.data.length):console.warn("未识别的数据格式:",l.data);const s=c.map(b=>({...b,stationName:b.stationName||b.wellName,stationLocation:b.stationLocation||b.wellLocation,recordTime:b.recordTime}));console.log("处理后的表格数据:",s),g.dataList=s,g.pagination.total=o}else g.dataList=[],g.pagination.total=0,d.error(((a=l.data)==null?void 0:a.message)||((t=l.data)==null?void 0:t.msg)||"获取数据失败")}catch(n){console.error("获取数据失败",n),d.error("获取数据失败"),g.dataList=[],g.pagination.total=0}finally{g.loading=!1}},D=w([]),N=async()=>{try{const e=await ie({page:1,size:1e3});let a=[];if(e&&e.data?e.data.data&&Array.isArray(e.data.data)?a=e.data.data:e.data.code===200?e.data.data&&Array.isArray(e.data.data.data)?a=e.data.data.data:e.data.data&&Array.isArray(e.data.data.records)?a=e.data.data.records:Array.isArray(e.data.data)&&(a=e.data.data):Array.isArray(e.data)&&(a=e.data):Array.isArray(e)&&(a=e),a&&a.length>0){D.value=a.map(t=>{let n=t.address||"";if(!n&&t.location)if(typeof t.location=="string"&&t.location.includes(",")){const[r,l]=t.location.split(",");n=`经纬度: ${r},${l}`}else n=String(t.location);return{label:t.name||"未命名站点",value:t.id,location:n,address:t.address||"",originalLocation:t.location||"",originalData:t}});try{const t=f.group[0].fields.find(n=>n.field==="stationId");t&&typeof t=="object"&&Object.assign(t,{options:D.value})}catch(t){console.error("设置站点选项失败",t)}return!0}else return console.warn("未找到站点数据"),!1}catch(e){return console.error("获取站点列表失败",e),!1}},R=e=>{try{const a=D.value.find(t=>t.value===e);a&&(p.value&&p.value.formRef?(p.value.formRef.setFieldValue("stationName",a.label),p.value.formRef.setFieldValue("stationLocation",a.address||a.location||"")):f.defaultValue={...f.defaultValue,stationId:a.value,stationName:a.label,stationLocation:a.address||a.location||""})}catch(a){console.error("处理站点选择变化失败",a)}},G=async()=>{var e;f.title="新增地下水涵养水位";try{if(!await N()){d.warning("站点列表加载失败，请重试");return}}catch(a){console.error("加载站点列表失败",a),d.warning("站点列表加载失败，请重试");return}f.defaultValue={recordTime:Date.now()},i.dialogVisible=!0,(e=p.value)==null||e.openDialog()},z=()=>{i.dialogVisible=!1},P=e=>{k("确定要删除该记录吗？","删除提示").then(async()=>{try{await F([e.id]),d.success("删除成功"),m()}catch{d.error("删除失败")}}).catch(()=>{})},$=()=>{if(i.selectedIds.length===0){d.warning("请选择要删除的记录");return}k(`确定要删除选中的 ${i.selectedIds.length} 条记录吗？`,"删除提示").then(async()=>{try{await F(i.selectedIds),d.success("删除成功"),m()}catch{d.error("删除失败")}}).catch(()=>{})},Y=async e=>{var a,t,n;try{I.title=`${e.stationName} 水位分析`,(a=C.value)==null||a.openDialog();const r=await se({stationId:e.stationId,startTime:new Date(new Date().getTime()-30*24*60*60*1e3).getTime(),endTime:new Date().getTime()});if(console.log("分析数据返回:",r),r.data&&r.data.code===200){let l=[];Array.isArray(r.data.data)?l=r.data.data:r.data.data&&Array.isArray(r.data.data.data)?l=r.data.data.data:r.data.data&&Array.isArray(r.data.data.records)?l=r.data.data.records:(l=[],console.warn("未识别的分析数据格式:",r.data));const c=l.map(s=>({...s,recordTime:s.recordTime,waterLevel:s.waterLevel||0}));i.analysisData={dates:c.map(s=>String(V(s.recordTime,"MM-DD")||"")),values:c.map(s=>s.waterLevel)},console.log("处理后的分析数据:",i.analysisData),setTimeout(()=>{x(i.analysisData.dates,i.analysisData.values)},100);const o=await le({areaId:e.areaId||e.id,startTime:new Date(new Date().getTime()-30*24*60*60*1e3).getTime(),endTime:new Date().getTime()});console.log("涵养建议返回:",o),o.data&&o.data.code===200?o.data.data&&o.data.data.rechargeSuggestion?i.suggestion=o.data.data.rechargeSuggestion:o.data.data&&typeof o.data.data=="string"?i.suggestion=o.data.data:(i.suggestion="暂无建议",console.warn("未识别的建议数据格式:",o.data)):i.suggestion="暂无建议"}else i.analysisData={dates:[],values:[]},i.suggestion="暂无建议",x([],[]),d.error(((t=r.data)==null?void 0:t.message)||((n=r.data)==null?void 0:n.msg)||"获取分析数据失败")}catch(r){console.error("获取分析数据失败",r),d.error("获取分析数据失败"),i.analysisData={dates:[],values:[]},i.suggestion="暂无建议",L.value&&x([],[])}},x=(e,a)=>{if(!L.value)return;y&&y.dispose(),y=H(L.value);const t={title:{text:"地下水位变化趋势",left:"center"},tooltip:{trigger:"axis",formatter:"{b}<br />{a}: {c} m"},xAxis:{type:"category",data:e,axisLabel:{rotate:45}},yAxis:{type:"value",name:"水位(m)"},series:[{name:"水位",type:"line",data:a,markPoint:{data:[{type:"max",name:"最高值"},{type:"min",name:"最低值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}],grid:{left:"3%",right:"4%",bottom:"15%",containLabel:!0},dataZoom:[{type:"inside",start:0,end:100},{start:0,end:100}]};y.setOption(t),window.addEventListener("resize",()=>{y==null||y.resize()})};return O(async()=>{try{N().catch(e=>{console.error("加载站点列表失败",e)})}catch(e){console.error("加载站点列表失败",e)}try{await m()}catch{d.error("加载数据列表失败")}}),(e,a)=>{const t=j,n=W,r=B;return Z(),J("div",ne,[A(t,{ref_key:"refSearch",ref:S,config:M},null,8,["config"]),A(n,{config:g,class:"card-table"},null,8,["config"]),A(r,{ref_key:"refDialogForm",ref:p,config:f,onClose:z},null,8,["config"]),A(r,{ref_key:"refAnalysisForm",ref:C,config:I},{default:K(()=>[v("div",de,[v("div",ce,[v("div",{ref_key:"chartRef",ref:L,class:"chart"},null,512)]),v("div",ue,[a[0]||(a[0]=v("h3",null,"涵养建议",-1)),v("div",ge,Q(i.suggestion),1)])])]),_:1},8,["config"])])}}}),_e=ae(me,[["__scopeId","data-v-0a8329f4"]]);export{_e as default};
