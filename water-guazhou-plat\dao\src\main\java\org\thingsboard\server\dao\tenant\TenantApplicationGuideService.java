package org.thingsboard.server.dao.tenant;

import org.thingsboard.server.dao.model.sql.TenantApplicationEntity;
import org.thingsboard.server.dao.model.sql.TenantApplicationGuideEntity;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

public interface TenantApplicationGuideService {
    IstarResponse save(TenantApplicationGuideEntity tenantApplicationGuideEntity);

    List<TenantApplicationGuideEntity> getList(String tenantId);

    TenantApplicationGuideEntity getByApplicationId(String applicationId);

    List<TenantApplicationEntity> getNotSetList(String tenantId);
}
