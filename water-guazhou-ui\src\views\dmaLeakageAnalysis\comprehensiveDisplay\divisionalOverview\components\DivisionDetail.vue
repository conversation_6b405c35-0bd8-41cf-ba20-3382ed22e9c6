<template>
  <div class="division-detail">
    <Tabs v-model="state.curTab" :config="TabConfig" class="tabs">
      <template #content>
        <div class="tab-content">
          <div
            class="left"
            :class="partitionsType === '1' ? 'root' : 'division'"
          >
            <div class="list">
              <div
                v-for="(item, i) in state.basicInfo"
                :key="i"
                class="list-item"
              >
                <label class="label" for="">{{ item.label + ':' }}</label
                ><span class="value">{{ item.value }}</span>
              </div>
            </div>
          </div>
          <div
            ref="refDiv"
            class="right"
            :class="partitionsType === '1' ? 'root' : 'division'"
          >
            <div class="title">
              {{ partitionsType === '1' ? 'DMA分区状态' : '挂接用户信息' }}
            </div>
            <div v-if="partitionsType === '1'" class="charts">
              <VChart ref="refChart1" :option="state.option_root"></VChart>
            </div>
            <div v-else class="charts">
              <div class="chart">
                <VChart
                  ref="refChart2"
                  :option="state.option_user_inhabitant"
                ></VChart>
              </div>
              <div class="chart">
                <VChart
                  ref="refChart3"
                  :option="state.option_user_big_user"
                ></VChart>
              </div>
            </div>
          </div>
        </div>
      </template>
    </Tabs>
  </div>
</template>
<script lang="ts" setup>
import { GetPartitionOverview } from '@/api/mapservice/dma';
import { useDetector } from '@/hooks/echarts';
import { useAppStore } from '@/store';
import { transNumberUnit } from '@/utils/GlobalHelper';

const appStore = useAppStore();
const initOption = (data: { value: number; name: string; scale: string }[]) => {
  // const data = [
  //   { value: 0, name: '规划中', scale: '0.00%' },
  //   { value: 0, name: '建制中', scale: '0.00%' },
  //   { value: 8, name: '评估中', scale: '72.73%' },
  //   { value: 0, name: '营运中', scale: '0.00%' },
  //   { value: 3, name: '检修漏', scale: '27.27%' }
  // ]
  const total = data.reduce((prev, cur) => {
    return (cur.value ?? 0) + prev;
  }, 0);
  return {
    title: {
      text: '{name|合计(个)}\n{val|' + total + '}',
      top: 'center',
      left: '29%',
      textAlign: 'center',
      textStyle: {
        rich: {
          name: {
            fontSize: 16,
            fontWeight: 'normal',
            padding: [8, 0],
            align: 'center',
            color: appStore.isDark ? '#fff' : '#2A2A2A'
          },
          val: {
            fontSize: 25,
            fontWeight: 'bold',
            color: appStore.isDark ? '#fff' : '#2A2A2A'
          }
        }
      }
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      top: 'center',
      right: 'left',
      textStyle: {
        color: '#318DFF',
        rich: {
          name: {
            align: 'left',
            width: 60,
            fontSize: 12,
            color: appStore.isDark ? '#fff' : '#2A2A2A'
          },
          value: {
            align: 'left',
            width: 50,
            fontSize: 12,
            color: '#00ff00'
          },
          count: {
            align: 'left',
            width: 40,
            fontSize: 12
          },
          upRate: {
            align: 'left',
            fontSize: 12
          },
          downRate: {
            align: 'left',
            fontSize: 12,
            color: '#409EFF'
          }
        }
      },
      data: data.map((item) => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            if (name === data[i].name) {
              return (
                '{name| ' +
                name +
                '}' +
                '{value| ' +
                data[i].value +
                ' ' +
                '个' +
                '}' +
                '{downRate| ' +
                (data[i].scale || '') +
                '}'
              );
            }
          }
        }
      }
    },
    series: [
      {
        name: '分区状态',
        type: 'pie',
        radius: ['50%', '70%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 0,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
          // position: 'center',
          // formatter: params => {
          //   return '合计'
          // }
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 12,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data
      }
    ]
  };
};
const ring = (
  data: {
    name: string;
    nameAlias?: string;
    value: string;
    valueAlias?: string;
    scale: string;
  }[] = [],
  unit?: string,
  prefix?: string,
  percision = 2
) => {
  const formatNumber = function (num) {
    const reg = /(?=(\B)(\d{3})+$)/g;
    return num.toString().replace(reg, ',');
  };
  const total = data.reduce((a, b: any) => {
    return a + (parseFloat(b.value) || 0) * 1;
  }, 0);
  const transedTotal = transNumberUnit(total);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return (
          (prefix || '') +
          params.name +
          ': ' +
          Number(params.value).toFixed(0) +
          ' ' +
          (unit || '户')
        );
      }
    },
    legend: {
      // selectedMode: false, // 取消图例上的点击事件
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      right: 'left',
      top: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      symbolKeepAspect: true,
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 90,
            fontSize: 12,
            color: appStore.isDark ? '#fff' : '#2A2A2A'
          },
          value: {
            align: 'left',
            width: 50,
            fontSize: 12,
            color: '#00ff00'
          },
          downRate: {
            align: 'left',
            fontSize: 12,
            color: '#409EFF'
          }
        }
      },
      data: data.map((item) => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            if (name === data[i].name) {
              return (
                '{name| ' +
                (data[i].nameAlias || name) +
                '}' +
                '{value| ' +
                (data[i].valueAlias || data[i].value) +
                ' ' +
                (unit || '') +
                '}' +
                '{downRate| ' +
                (data[i].scale || '') +
                '}'
              );
            }
          }
        }
      }
    },
    title: [
      {
        text:
          '{name|总数(户)}\n{val|' +
          formatNumber(transedTotal.value.toFixed(0)) +
          '}',
        top: 'center',
        left: '19%',
        textAlign: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 10,
              fontWeight: 'normal',
              padding: [8, 0],
              align: 'center',
              color: appStore.isDark ? '#fff' : '#2A2A2A'
            },
            val: {
              fontSize: 16,
              fontWeight: 'bold',
              color: appStore.isDark ? '#fff' : '#2A2A2A'
            }
          }
        }
      }
    ],
    series: [
      {
        type: 'pie',
        radius: ['35%', '50%'],
        center: ['20%', '50%'],
        data,
        hoverAnimation: true,
        label: {
          show: false,
          formatter: (params) => {
            return (
              '{icon|●}{name|' +
              params.name +
              '}{value|' +
              formatNumber(Number(params.value || '0').toFixed(percision)) +
              '}'
            );
          },
          padding: [0, -100, 25, -100],
          rich: {
            icon: {
              fontSize: 16
            },
            name: {
              fontSize: 14,
              padding: [0, 10, 0, 4]
            },
            value: {
              fontSize: 18,
              fontWeight: 'bold'
            }
          }
        }
      }
    ]
  };
  return option;
};
const props = defineProps<{
  data?: {
    partition?: any;
    data?: any;
  };
  partitions?: any[];
}>();
const TabConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'border-card',
  tabs: [{ label: '分区信息', value: '1' }]
});
const state = reactive<{
  curTab: string;
  option_root: any;
  option_user_inhabitant: any;
  option_user_big_user: any;
  basicInfo: { label: string; value: string | number }[];
}>({
  curTab: '1',
  option_root: null,
  option_user_big_user: null,
  option_user_inhabitant: null,
  basicInfo: []
});
const level = computed(() => {
  return props.data?.data?.path?.length ?? 0;
});
const partitionsType = computed(() => {
  return props.data?.partition?.type?.toString();
});
const getLevel2Count = () => {
  let count = 0;
  props.data?.data?.children?.map((item) => {
    if (level.value === 1) {
      count += item.children?.length || 0;
    } else {
      count++;
    }
  });
  return count;
};
const initData = async () => {
  const id = props.data?.data?.value;
  if (!id) return;
  try {
    const res = await GetPartitionOverview({
      partitionId: props.data?.data?.value
    });
    const partition = res.data?.data || {};
    state.basicInfo = [
      { label: '分区名称', value: partition?.partitionName ?? '--' },
      // { label: '分区编号', value: partition. || '' },
      { label: '分区类型', value: partition?.type ?? '--' },
      ...(level.value <= 2
        ? [
            ...[
              level.value === 1
                ? {
                    label: '一级分区',
                    value: (props.data?.data?.children?.length || 0) + '个'
                  }
                : {
                    label: '上级分区',
                    value: partition?.parentName ?? '--'
                  }
            ],
            {
              label: '二级分区',
              value: getLevel2Count() + ' 个'
            },
            {
              label: 'DMA分区个数',
              value:
                (props.partitions?.filter(
                  (item) => !!item.geom && item.pid === props.data?.partition.id
                ).length ?? '--') + '个'
            }
          ]
        : []),
      {
        label: '供水面积',
        value: (partition?.supplyWaterArea ?? '--') + 'km²'
      },
      {
        label: '管线长度',
        value: (partition?.mainLineLength ?? '--') + 'km'
      },
      {
        label: '用户类型',
        value: partition?.userType ?? '--'
      },
      {
        label: '挂接用户数',
        value: (partition?.custNum ?? '--') + '户'
      },
      {
        label: '远传大用户数',
        value: (partition?.bigUserNum ?? '--') + '户'
      },
      { label: '抄表员', value: partition?.copyMeterUser ?? '--' },
      { label: '流量监测点', value: `${partition?.flowNum ?? '--'} 个` },
      { label: '压力监测点', value: `${partition?.pressureNum ?? '--'} 个` },
      { label: '负责人', value: partition?.director ?? '--' },
      ...(level.value <= 2
        ? [{ label: '分区范围', value: partition?.range ?? '--' }]
        : [
            {
              label: '上级分区',
              value: partition?.parentName ?? '--'
            },
            { label: '入水口', value: `${partition?.inWaterNum ?? '--'} 个` },
            {
              label: '分区状态',
              value: partition?.status ?? '--'
            },
            { label: '分区地址', value: partition?.range ?? '--' },
            {
              label: '物业公司',
              value: partition?.propertyName ?? '--'
            }
          ])
    ];
    if (partitionsType.value === '1') {
      const partitions: { name: string; num: number }[] =
        res.data?.data?.dmaPartitionStatus ?? [];
      const total = partitions.reduce((prev, cur) => {
        return (cur.num ?? 0) + prev;
      }, 0);
      state.option_root = initOption(
        res.data?.data?.dmaPartitionStatus?.map((item) => {
          return {
            value: item.num,
            name: item.name,
            scale: total === 0 ? '0%' : (item.num / total).toFixed(2) + '%'
          };
        }) || []
      );
    } else {
      const userNum = partition?.custNum || 0;
      const bigUserNum = partition?.bigUserNum || 0;
      const data = partition?.custUseWaterType?.map((item) => {
        return {
          name: item.name,
          nameAlias: item.name,
          value: item.num,
          valueAlias: item.num + ' 户',
          scale: `${userNum === 0 ? '0.00' : ((item.num / userNum) * 100).toFixed(2)} %`
        };
      }) || [
        {
          name: '居民用水',
          nameAlias: '居民用水',
          value: userNum,
          valueAlias: userNum + ' 户',
          scale: '100.00 %'
        }
      ];

      const data1 = [
        {
          name: '大用户数',
          nameAlias: '大用户数',
          value: bigUserNum,
          valueAlias: bigUserNum + ' 户',
          scale: bigUserNum === 0 ? '0.00 %' : '100.00%'
        }
      ];
      state.option_user_big_user = ring(data1);
      state.option_user_inhabitant = ring(data);
    }
  } catch (error) {
    //
  }
};
const detector = useDetector();
const refDiv = ref();
const refChart1 = ref();
const refChart2 = ref();
const refChart3 = ref();
watch(
  () => props.data,
  () => {
    initData();
  }
);
onMounted(() => {
  detector.listenToMush(refDiv.value, () => {
    refChart1.value?.resize();
    refChart2.value?.resize();
    refChart3.value?.resize();
  });
});
onBeforeMount(() => {
  initData();
});
</script>
<style lang="scss" scoped>
.division-detail {
  height: 100%;
  .tabs {
    height: 100%;
    :deep(.el-tabs) {
      height: 100%;
      .el-tab-pane {
        height: 100%;
      }
      .el-tabs__content {
        height: calc(100% - 40px);
      }
    }
  }
  .tab-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    .left,
    .right {
      width: 50%;
      height: 100%;
    }
    .right {
      border-left: 1px solid var(--el-border-color);
      padding: 0 20px;
      &.root {
        width: 40%;
      }
      &.division {
        width: 30%;
      }
      .title {
        height: 24px;
      }
      .charts {
        height: 214px;
      }
    }
    .root {
      &.left {
        width: 60%;
      }
      &.right {
        width: 40%;
      }
    }
    .division {
      &.left {
        width: 50%;
      }
      &.right {
        width: 50%;
      }
    }
  }
  .list {
    display: flex;

    flex-direction: row;
    flex-wrap: wrap;
    .list-item {
      width: 33%;
    }
    .list-item {
      padding: 8px 20px;

      .label {
        color: var(--el-text-color-placeholder);
        font-size: 14px;
        padding-right: 12px;
      }
      .value {
        font-size: 14px;
        color: #00bcd4;
        font-weight: bold;
      }
    }
  }
  .charts {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    height: 100%;
    width: 100%;
    .chart {
      width: 50%;
      height: 100%;
    }
  }
}
</style>
