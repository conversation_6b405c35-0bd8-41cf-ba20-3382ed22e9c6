<template>
  <div class="video-selector">
    <el-button type="primary" @click="openDialog">
      选择视频 ({{ selectedVideos.length }})
    </el-button>

    <el-dialog
      v-model="dialogVisible"
      title="选择关联视频"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="video-dialog-content">
        <div class="left-panel">
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索视频名称"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="tree-container">
            <div v-if="!videoTreeData.length" class="empty-tree">
              暂无视频数据
            </div>
            <el-tree
              v-else
              ref="videoTree"
              :data="videoTreeData"
              :props="treeProps"
              node-key="id"
              :expand-on-click-node="false"
              default-expand-all
              @node-click="handleNodeClick"
            >
              <template #default="{ node, data }">
                <div class="tree-node">
                  <el-icon v-if="data.type === 'project'">
                    <Folder />
                  </el-icon>
                  <el-icon v-else-if="data.type === 'group'">
                    <FolderOpened />
                  </el-icon>
                  <el-icon v-else>
                    <VideoCamera />
                  </el-icon>
                  <span class="node-label">{{ node.label || data.name || data.id }}</span>
                </div>
              </template>
            </el-tree>
          </div>
        </div>

        <div class="right-panel">
          <div class="video-list-header">
            <span>视频列表</span>
            <el-button
              type="primary"
              size="small"
              :disabled="!currentVideoList.length"
              @click="selectAllVideos"
            >
              全选当前
            </el-button>
          </div>

          <div class="video-list">
            <div
              v-for="video in filteredVideoList"
              :key="video.id"
              class="video-item"
              :class="{ selected: isVideoSelected(video.id) }"
              @click="toggleVideoSelection(video)"
            >
              <div class="video-info">
                <div class="video-name">{{ video.name }}</div>
                <div class="video-details">
                  <span>设备号: {{ video.serialNumber || '无' }}</span>
                  <span>通道: {{ video.channelId || '无' }}</span>
                </div>
              </div>
              <div class="video-status">
                <el-tag
                  :type="video.online ? 'success' : 'danger'"
                  size="small"
                >
                  {{ video.online ? '在线' : '离线' }}
                </el-tag>
              </div>
              <div class="selection-indicator">
                <el-icon v-if="isVideoSelected(video.id)" color="#409EFF">
                  <Check />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="selected-videos-panel">
        <div class="panel-header">
          <span>已选择的视频 ({{ selectedVideos.length }})</span>
          <el-button
            type="danger"
            size="small"
            :disabled="!selectedVideos.length"
            @click="clearAllSelection"
          >
            清空选择
          </el-button>
        </div>
        <div class="selected-list">
          <el-tag
            v-for="video in selectedVideos"
            :key="video.id"
            closable
            @close="removeVideoSelection(video.id)"
          >
            {{ video.name }}
          </el-tag>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelection">
            确定 ({{ selectedVideos.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import {
  Search,
  Folder,
  FolderOpened,
  VideoCamera,
  Check
} from '@element-plus/icons-vue';
import { getCameraTree, getGroupVideoList } from '@/api/video';
import { ElMessage } from 'element-plus';

interface VideoItem {
  id: string;
  name: string;
  serialNumber?: string;
  channelId?: string;
  online?: boolean;
  [key: string]: any;
}

interface TreeNode {
  id: string;
  label: string;
  type: 'project' | 'group' | 'video';
  children?: TreeNode[];
  projectId?: string;
  groupId?: string;
  nodeDetail?: any;
}

const props = defineProps<{
  modelValue?: VideoItem[];
}>();

const emit = defineEmits<{
  'update:modelValue': [videos: VideoItem[]];
  'change': [videos: VideoItem[]];
}>();

const dialogVisible = ref(false);
const searchKeyword = ref('');
const videoTreeData = ref<any[]>([]);
const currentVideoList = ref<VideoItem[]>([]);
const selectedVideos = ref<VideoItem[]>([]);
const currentNode = ref<any>(null);

const treeProps = {
  children: 'children',
  label: 'label'
};

// 过滤后的视频列表
const filteredVideoList = computed(() => {
  if (!searchKeyword.value) {
    return currentVideoList.value;
  }
  return currentVideoList.value.filter(video =>
    video.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 初始化选中的视频
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    selectedVideos.value = [...newValue];
  }
}, { immediate: true });

// 打开对话框
const openDialog = () => {
  dialogVisible.value = true;
  loadVideoTree();
};

// 加载视频树
const loadVideoTree = async () => {
  try {
    const response = await getCameraTree();

    // 提取实际的数据数组
    let actualData = [];
    if (response.data) {
      if (Array.isArray(response.data)) {
        actualData = response.data;
      } else if (response.data.code === 200 && response.data.data) {
        actualData = response.data.data;
      } else if (response.data.data) {
        actualData = response.data.data;
      } else {
        actualData = response.data;
      }
    }

    // 处理数据，添加label字段
    const processedData = addLabelToTreeData(actualData);
    videoTreeData.value = processedData;
  } catch (error) {
    ElMessage.error('加载视频树失败');
    console.error('Load video tree error:', error);
  }
};

// 为树数据添加label字段（Element Tree组件需要）
const addLabelToTreeData = (data: any[]): any[] => {
  if (!Array.isArray(data)) {
    return [];
  }

  return data.map(item => ({
    ...item,
    label: item.name || item.label || '未命名',
    children: item.children && Array.isArray(item.children) ? addLabelToTreeData(item.children) : undefined
  }));
};

// 处理树节点点击
const handleNodeClick = async (data: any) => {
  currentNode.value = data;

  if (data.type === 'group') {
    try {
      const projectId = findProjectIdForNode(data);

      if (projectId) {
        const response = await getGroupVideoList({
          projectId: projectId,
          groupId: data.id
        });
        currentVideoList.value = response.data || [];
      } else {
        currentVideoList.value = [];
      }
    } catch (error) {
      ElMessage.error('加载视频列表失败');
      currentVideoList.value = [];
    }
  } else if (data.type === 'video') {
    // 如果点击的是视频节点，可以直接选择
    const videoItem: VideoItem = {
      id: data.id,
      name: data.name || data.label,
      serialNumber: data.nodeDetail?.serialNumber,
      channelId: data.nodeDetail?.channelId,
      online: data.nodeDetail?.status === '1',
      ...data.nodeDetail
    };
    toggleVideoSelection(videoItem);
  } else {
    currentVideoList.value = [];
  }
};

// 查找节点的项目ID
const findProjectIdForNode = (node: any): string | undefined => {
  // 简化逻辑：在整个树中查找父节点
  const findParent = (treeData: any[], childId: string): any => {
    for (const item of treeData) {
      if (item.children) {
        for (const child of item.children) {
          if (child.id === childId) {
            return item;
          }
        }
        const found = findParent(item.children, childId);
        if (found) return found;
      }
    }
    return null;
  };

  // 递归向上查找项目节点
  const findProject = (currentNode: any): string | undefined => {
    if (currentNode.type === 'project') {
      return currentNode.id;
    }

    const parent = findParent(videoTreeData.value, currentNode.id);
    if (parent) {
      return findProject(parent);
    }

    return undefined;
  };

  return findProject(node);
};

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

// 检查视频是否已选中
const isVideoSelected = (videoId: string): boolean => {
  return selectedVideos.value.some(video => video.id === videoId);
};

// 切换视频选择状态
const toggleVideoSelection = (video: VideoItem) => {
  const index = selectedVideos.value.findIndex(v => v.id === video.id);
  if (index > -1) {
    selectedVideos.value.splice(index, 1);
  } else {
    selectedVideos.value.push(video);
  }
};

// 全选当前视频列表
const selectAllVideos = () => {
  filteredVideoList.value.forEach(video => {
    if (!isVideoSelected(video.id)) {
      selectedVideos.value.push(video);
    }
  });
};

// 移除视频选择
const removeVideoSelection = (videoId: string) => {
  const index = selectedVideos.value.findIndex(v => v.id === videoId);
  if (index > -1) {
    selectedVideos.value.splice(index, 1);
  }
};

// 清空所有选择
const clearAllSelection = () => {
  selectedVideos.value = [];
};

// 确认选择
const confirmSelection = () => {
  emit('update:modelValue', [...selectedVideos.value]);
  emit('change', [...selectedVideos.value]);
  dialogVisible.value = false;
};

onMounted(() => {
  if (props.modelValue) {
    selectedVideos.value = [...props.modelValue];
  }
});
</script>

<style lang="scss" scoped>
.video-selector {
  width: 100%;
}

.video-dialog-content {
  display: flex;
  height: 500px;
  gap: 16px;
}

.left-panel {
  width: 300px;
  border-right: 1px solid #e4e7ed;
  padding-right: 16px;

  .search-box {
    margin-bottom: 16px;
  }

  .tree-container {
    height: calc(100% - 50px);
    overflow-y: auto;

    .empty-tree {
      text-align: center;
      color: #999;
      padding: 20px;
    }
  }
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;

  .node-label {
    font-size: 14px;
  }
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;

  .video-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-weight: 500;
  }

  .video-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }
}

.video-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }

  &.selected {
    background-color: #e6f7ff;
    border-color: #409EFF;
  }

  &:last-child {
    border-bottom: none;
  }

  .video-info {
    flex: 1;

    .video-name {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .video-details {
      font-size: 12px;
      color: #666;

      span {
        margin-right: 16px;
      }
    }
  }

  .video-status {
    margin-right: 12px;
  }

  .selection-indicator {
    width: 20px;
    text-align: center;
  }
}

.selected-videos-panel {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-weight: 500;
  }

  .selected-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    max-height: 100px;
    overflow-y: auto;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
