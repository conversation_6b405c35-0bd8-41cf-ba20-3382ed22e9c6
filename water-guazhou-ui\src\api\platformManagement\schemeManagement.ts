import { request } from '@/plugins/axios'

// 获取方案管理列表
export function getSchemeManagementList(params: any) {
  return request({
    url: '/api/base/scheme/management/list',
    method: 'get',
    params
  })
}

// 获取方案管理详情
export function getSchemeManagementDetail(id: string) {
  return request({
    url: '/api/base/scheme/management/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增方案管理
export function addSchemeManagement(data: any) {
  return request({
    url: '/api/base/scheme/management/add',
    method: 'post',
    data
  })
}

// 修改方案管理
export function editSchemeManagement(data: any) {
  return request({
    url: '/api/base/scheme/management/edit',
    method: 'post',
    data
  })
}

// 删除方案管理
export function deleteSchemeManagement(ids: string[]) {
  return request({
    url: '/api/base/scheme/management/deleteIds',
    method: 'delete',
    data: ids
  })
} 