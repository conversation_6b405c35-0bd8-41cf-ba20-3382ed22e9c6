<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.groundwater.GroundwaterRechargeMapper">

    <sql id="Base_Column_List">
        id, tenant_id, area_id, start_time, end_time, initial_level, final_level, 
        level_change, recharge_amount, rainfall_amount, consumption_amount, 
        suggested_recharge_amount, recharge_suggestion, analysis_result, 
        create_time, update_time, status, creator
    </sql>

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.groundwater.GroundwaterRecharge">
        SELECT
            gr.*,
            ra.name AS area_name
        FROM
            tb_groundwater_recharge gr
        LEFT JOIN
            tb_remote_area ra ON gr.area_id = ra.id
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND gr.tenant_id = #{tenantId}
            </if>
            <if test="areaId != null and areaId != ''">
                AND gr.area_id = #{areaId}
            </if>
            <if test="status != null">
                AND gr.status = #{status}
            </if>
            <if test="startTime != null">
                AND gr.start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND gr.end_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY gr.create_time DESC
    </select>

    <select id="findListCount" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            tb_groundwater_recharge gr
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND gr.tenant_id = #{tenantId}
            </if>
            <if test="areaId != null and areaId != ''">
                AND gr.area_id = #{areaId}
            </if>
            <if test="status != null">
                AND gr.status = #{status}
            </if>
            <if test="startTime != null">
                AND gr.start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND gr.end_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="findLatestByAreaId" resultType="org.thingsboard.server.dao.model.sql.groundwater.GroundwaterRecharge">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            tb_groundwater_recharge
        WHERE
            area_id = #{areaId}
        ORDER BY
            create_time DESC
        LIMIT 1
    </select>

</mapper> 