package org.thingsboard.server.common.data.VO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 润滑油加注管理列表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RhyjzglVO {

    private String id;

    private String realDeviceId;

    private String realDeviceName;

    private Date thisTime;

    private Date lastTime;

    private String timeInterval;

    private String remark;

}
