package org.thingsboard.server.dao.sql.deviceType;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceTypeAttr;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypeAttrPageRequest;

@Mapper
public interface DeviceTypeAttrMapper extends BaseMapper<DeviceTypeAttr> {
    IPage<DeviceTypeAttr> findByPage(DeviceTypeAttrPageRequest request);

    boolean update(DeviceTypeAttr attr);
}
