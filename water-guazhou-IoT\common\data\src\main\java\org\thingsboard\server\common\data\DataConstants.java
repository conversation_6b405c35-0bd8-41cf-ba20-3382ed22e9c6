/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 */
public class DataConstants {


    public static final String SYSTEM = "SYSTEM";
    public static final String TENANT = "TENANT";
    public static final String CUSTOMER = "CUSTOMER";
    public static final String DEVICE = "DEVICE";
    public static final String DEVICE_ID = "deviceId";
    public static final String DEVICE_NAME = "deviceName";
    public static final String PROPERTY = "property";
    public static final String RESOURCE_NUMBER = "resourceNumber";
    //排序类型
    public static final String ORDER = "order";
    /**
     * 排序-时间降序
     */
    public static final String ORDER_TIME_DESC = "timeDescending";
    /**
     * 排序-时间升序序
     */
    public static final String ORDER_TIME_ASC = "timeAscending";
    /**
     * 排序-编号降序
     */
    public static final String ORDER_NUMBER_DESC = "numberDescending";
    /**
     * 排序-编号升序
     */
    public static final String ORDER_NUMBER_ASC = "numberAscending";
    /**
     * 排序-数据降序
     */
    public static final String ORDER_VALUE_DESC = "temperatureDescending";
    /**
     * 排序-数据升序
     */
    public static final String ORDER_VALUE_ASC = "temperatureAscending";
    /**
     * 是否进行分组
     */
    public static final String IS_GROUP = "isGroup";
    /**
     * 参数名称
     */
    public static final String PROP_NAME = "propertyName";
    /**
     * 参数单位
     */
    public static final String PROP_UNIT = "propertyUnit";
    /**
     * 步长
     */
    public static final String TIME_LIMIT = "timeLimit";

    /**
     * 报警级别：预警
     */
    public static final String ALARM_LEVEL_WARNING = "预警";
    public static final String ALARM_LEVEL_ALARM = "报警";

    public static final String STATUS_DROP = "dropped";

    public static final String STATUS_NORMAL = "normal";

    public static final String STATUS_WARN = "warning";

    public static final String STATUS_ALARM = "alarm";

    public static final String CLIENT_SCOPE = "CLIENT_SCOPE";
    public static final String SERVER_SCOPE = "SERVER_SCOPE";
    public static final String SHARED_SCOPE = "SHARED_SCOPE";
    public static final String RPC_TO_MQTT = "writeToMqtt";
    public static final String RPC_TO_MODBUS = "writeToModbus";
    public static final String RPC_TO_DTU = "writeToDtu";

    public static final String[] allScopes() {
        return new String[]{CLIENT_SCOPE, SHARED_SCOPE, SERVER_SCOPE};
    }

    public static final String ALARM = "ALARM";
    public static final String ALARM_TIME = "alarmTime";
    public static final String ERROR = "ERROR";
    public static final String LC_EVENT = "LC_EVENT";
    public static final String STATS = "STATS";
    public static final String DEBUG_RULE_NODE = "DEBUG_RULE_NODE";
    public static final String RESTORE = "restore";

    public static final String ONEWAY = "ONEWAY";
    public static final String TWOWAY = "TWOWAY";
    public static final String INFLUX_DEVICE_DATA="device";
    public static final String IN = "IN";
    public static final String OUT = "OUT";
    public static final String TRUE = "true";

    public static final String INACTIVITY_EVENT = "INACTIVITY_EVENT";
    public static final String CONNECT_EVENT = "CONNECT_EVENT";
    public static final String DISCONNECT_EVENT = "DISCONNECT_EVENT";
    public static final String ACTIVITY_EVENT = "ACTIVITY_EVENT";
    public static final String ATTRIBUTE_KEY_RESOURCE_GROUP = "resourceGroup";

    public static final String ENTITY_CREATED = "ENTITY_CREATED";
    public static final String ENTITY_UPDATED = "ENTITY_UPDATED";
    public static final String ENTITY_DELETED = "ENTITY_DELETED";
    public static final String ENTITY_ASSIGNED = "ENTITY_ASSIGNED";
    public static final String ENTITY_UNASSIGNED = "ENTITY_UNASSIGNED";
    public static final String ATTRIBUTES_UPDATED = "ATTRIBUTES_UPDATED";
    public static final String ATTRIBUTES_DELETED = "ATTRIBUTES_DELETED";

    public static final String RPC_CALL_FROM_SERVER_TO_DEVICE = "RPC_CALL_FROM_SERVER_TO_DEVICE";
    public static final String ATTRIBUTE_CHANGE_METER = "changeMeter";
    public static final String ATTRIBUTE_PROP = "prop";
    public static final String INPUT_KV = "inputKv";
    public static final String ATTRIBUTE_COST = "成本";
    public static final String VALUE = "value";
    public static final String CREATE_DEVICE_LIMIT = "createDeviceLimit";
    public static final int RESPONSE_CODE_SUCCESS = 200;
    public static final int RESPONSE_CODE_FAILURE = 500;
    public static final int RESPONSE_CODE_PARAMERROR = 405;
    public static final int RESPONSE_NOTHING_DATA = 406;
    public static final String RESPONSE_INFO_LOCAL_SUCCESS = "OK";
    public static final String RESPONSE_INFO_DATABASE_ERROR = "数据库操作失败！";
    public static final String RESPONSE_SECRET_FAILED = "验证秘钥失败!";
    public static final String RESPONSE_PARAMS_ERROR = "参数错误！";
    public static final String RESPONSE_ERROR_UN_KNOW = "未定义的异常";
    public static final String RESPONSE_ERROR_GET_COST = "解析能源费用出现异常，请检查能源费用字段!";
    public static final String RESPONSE_ERROR_COST_ERROR = "获取成本费用失败！";
    public static final String RESPONSE_ERROR_GET_DEVICE_INFO = "获取设备属性失败！";
    public static final String RESPONSE_ERROR_GET_DATA_FALIED = "从OPENTSDB获取数据失败";
    public static final String REQUEST_PARAM_START = "start";
    public static final String REQUEST_PARAM_END = "end";
    public static final String REQUEST_PARAM_ATTRIBUTES = "attributes";
    public static final String REQUEST_PARAM_ATTRIBUTE = "attribute";
    public static final String REQUEST_PARAM_TIME_SHARING_NAME = "timeSharingName";
    public static final String REQUEST_PARAM_TYPE = "type";
    public static final String REQUEST_PARAM_FORMULA = "formula";
    public static final String REQUEST_PARAM_FORMULA_LIST = "formulaList";
    public static final String REQUEST_PARAM_NAME = "name";
    public static final String REQUEST_PARAM_DATA = "data";
    public static final String REQUEST_PARAM_KEY = "key";
    public static final String REQUEST_PARAM_VALUE = "value";
    public static final String REQUEST_PARAM_START_TIME = "startTime";
    public static final String REQUEST_PARAM_PRICE = "price";
    public static final String REQUEST_PARAM_INTERVAL = "interval";
    public static final String REQUEST_PARAM_ASSETS = "assets";
    public static final String REQUEST_PARAM_ENERGY = "energy";
    public static final String REQUEST_PARAM_SECRET = "secret";
    public static final String REQUEST_PARAM_TENANT_ID = "tenantId";
    public static final String REQUEST_PARAM_ENERGY_ALL = "energyAll";
    public static final String REQUEST_PARAM_ASSET_ID = "assetId";
    public static final String REQUEST_PARAM_ASSET_IDS = "assetIds";
    public static final String REQUEST_PARAM_ENERGY_CATAGORY = "energyCatagory";
    public static final String REQUEST_PARAM_ENERGY_DEFINITION = "energyDefinition";
    public static final String REQUEST_PARAM_COAL_RATIO = "coalRatio";
    public static final String REQUEST_PARAM_CARBON_RATIO = "carbonRatio";
    public static final String REQUEST_PARAM_CHILDREN = "children";
    public static final String RESPONSE_ENERGY_DAY_DATA = "本日累计用能";
    public static final String RESPONSE_ENERGY_MONTH_DATA = "本月累计用能";
    public static final String RESPONSE_ENERGY_YEAR_DATA = "本年累计用能";
    public static final String RESPONSE_ENERGY_MONTH_COST_DATA = "本月累计成本";
    public static final String RESPONSE_ENERGY_OPEN_TS_DB_NONE = "none";
    public static final String RESPONSE_ENERGY_PRICE = "energyPrice";
    public static final String PARAMS_SHOW_DATA = "showData";
    public static final String PARAMS_ATTRIBUTE_NAME = "propertyCategory";
    public static final String PARAMS_UNIT = "unit";

    public static final String DEFAULT_GATEWAY = "默认网关";
    public static final String GATEWAY_NAME = "gateWay";
    public static final String DEFAULT_CUSTOMER_USER_ID = "1b21dd2138140008080808080808080";

    public static final String DEFAULT_LOGO = "https://image.istarscloud.com/istars.png";


    public static final String REQUEST_PARAM_RANGE = "range";
    public static final String REQUEST_PARAM_FROM = "from";
    public static final String REQUEST_PARAM_TO = "to";
    public static final String REQUEST_PARAM_TARGETS = "targets";
    public static final String REQUEST_PARAM_TARGET = "target";
    public static final String REQUEST_PARAM_PROP = "prop";
    public static final String REQUEST_PARAM_REF_ID = "refId";
    public static final String REQUEST_PARAM_ENERGY_TYPE_ASSET = "asset";
    public static final String REQUEST_PARAM_ENERGY_TYPE_DEVICE = "device";
    public static final String REQUEST_PARAM_ENERGY_TYPE_VIRTUAL = "virtual";
    public static final String REQUEST_ALARM_USE_LAST_DATA = "useLastData";
    public static final String REQUEST_ALARM_LAST_DATA = "lastData";
    public static final String REQUEST_DATA_STEPSIZE = "stepSize";
    public static final String REQUEST_DATA_STEPSIZE_DEFAULT = "15m";
    public static final String REQUEST_DATA_STEPSIZE_SECOND = "30s";
    public static final String REQUEST_DATA_STEPSIZE_MINUTE = "1m";
    public static final String REQUEST_DATA_STEPSIZE_FIVE_MINUTE = "5m";
    public static final String REQUEST_DATA_STEPSIZE_TEN_MINUTE = "10m";

    //操作类型
    public static final String OPERATING_TYPE_ASSET_DATA = "查询ASSET数据！";
    public static final String OPERATING_TYPE_ASSET_DATA_AND_PRICE = "查询ASSET数据和成本！";
    public static final String OPERATING_TYPE_ASSET_DATA_TIME_SHARING = "查询ASSET的分时数据！";
    public static final String OPERATING_TYPE_ASSET_DATA_ENERGY_TIME_SHARING = "查询ASSET分时总能耗！";
    public static final String OPERATING_TYPE_ASSET_TYPE = "获取ASSET的类型！";
    public static final String OPERATING_TYPE_ASSET_SAVE = "保存ASSET！";
    public static final String OPERATING_TYPE_ASSET_QUERY = "查询ASSET列表！";
    public static final String OPERATING_TYPE_ASSET_TO_CUSTOMER = "分配ASSET给用户！";
    public static final String OPERATING_TYPE_ASSET_TO_CUSTOMER_RESET = "重置ASSET到用户！";
    public static final String OPERATING_TYPE_ASSET_DEL = "删除ASSET！";
    public static final String OPERATING_TYPE_VIRTUAL_GET = "获取VIRTUAL列表";
    public static final String OPERATING_TYPE_VIRTUAL_DATA = "获取VIRTUAL数据";
    public static final String OPERATING_TYPE_VIRTUAL_ADD = "新增VIRTUAL";
    public static final String OPERATING_TYPE_VIRTUAL_DEL = "删除VIRTUAL";
    public static final String OPERATING_TYPE_FORMULA_DATA = "根据公式获取数据";
    public static final String OPERATING_TYPE_INPUT_KV_ADD = "添加输入值";
    public static final String OPERATING_TYPE_INPUT_KV_GET = "获取输入值";
    public static final String OPERATING_TYPE_INPUT_KV_DEL = "删除输入值";
    public static final String OPERATING_TYPE_DEVICE_REAL_TIME = "获取设备实时数据";
    public static final String OPERATING_TYPE_DEVICE_INFO = "获取设备信息·";
    public static final String OPERATING_TYPE_DEVICE_LIST = "获取设备列表";
    public static final String OPERATING_TYPE_GATEWAY_LIST = "获取网关列表";
    public static final String OPERATING_TYPE_MOUNT_DEVICE = "挂载设备到网关";
    public static final String OPERATING_TYPE_GET_NO_GATEWAY_DEVICE = "获取尚未挂载的设备";
    public static final String OPERATING_TYPE_GET_GATEWAY_DEVICE = "获取网关下挂载的设备";
    public static final String OPERATING_TYPE_DEVICE_ONLIEE = "获取设备在线状态";
    public static final String OPERATING_TYPE_DEVICE_CREDENTIALS_ADD = "创建设备秘钥";
    public static final String OPERATING_TYPE_DEVICE_CREDENTIALS_INFO = "获取设备秘钥";
    public static final String OPERATING_TYPE_DEVICE_TYPE = "获取设备类型";
    public static final String OPERATING_TYPE_DEVICE_DATA = "获取设备数据";
    public static final String OPERATING_TYPE_DEVICE_TO_COUSTMER = "绑定设备到用户";
    public static final String OPERATING_TYPE_DEVICE_ADD = "添加设备";
    public static final String OPERATING_TYPE_DEVICE_DEL = "删除设备";
    public static final String OPERATING_TYPE_TENANT_GET = "获取TENANT";
    public static final String OPERATING_TYPE_TENANT_ADD = "新增TENANT";
    public static final String OPERATING_TYPE_TENANT_DEL = "删除TENANT";
    public static final String OPERATING_TYPE_TENANT_ATTRIBUTE = "TENANT添加属性";
    public static final String OPERATING_TYPE_USER_INFO = "获取USER信息";
    public static final String OPERATING_TYPE_USER_LIST = "获取USER列表";
    public static final String OPERATING_TYPE_USER_ADD = "添加USER";
    public static final String OPERATING_TYPE_USER_DEL = "删除USER";
    public static final String OPERATING_TYPE_USER_SEND_EMAIL = "发送USER验证邮件";
    public static final String OPERATING_TYPE_USER_ACTIVE = "激活USER";
    public static final String OPERATING_TYPE_ALARM_INFO = "获取报警列表";
    public static final String OPERATING_TYPE_ALARM_CLEAR = "解除报警";
    public static final String OPERATING_TYPE_LOG_INFO = "查看登陆日志";
    public static final String OPERATING_TYPE_TELEMETRY_DEL = "删除属性";
    public static final String OPERATING_TYPE_TELEMETRY_ADD = "添加属性";
    public static final String OPERATING_TYPE_ROLE_ADD = "添加角色";
    public static final String OPERATING_TYPE_ROLE_DEL = "删除角色";
    public static final String OPERATING_TYPE_ROLE_MENU_ADD = "为角色分配菜单权限";
    public static final String OPERATING_TYPE_ROLE_USER_ADD = "为用户分配角色权限";
    public static final String OPERATING_TYPE_MENU_ADD = "添加菜单";
    public static final String OPERATING_TYPE_MENU_DEL = "删除菜单";
    public static final String OPERATING_TYPE_MENU_POOL_ADD = "添加菜单池";
    public static final String OPERATING_TYPE_MENU_POOL_DEL = "删除菜单池";
    public static final String OPERATING_TYPE_ENERGY_ADD = "添加能源";
    public static final String OPERATING_TYPE_ENERGY_GET = "获取能源";
    public static final String OPERATING_TYPE_ENERGY_DEL = "删除能源";
    public static final String OPERATING_TYPE_SCADA_SAVE_URL = "保存/修改组态URL";
    public static final String OPERATING_TYPE_SCADA_QUERY_URL = "查询组态URL";
    public static final String OPERATING_TYPE_SCADA_DELETE_URL = "删除组态URL";
    public static final String MAINTAIN_STATUS_WAIT = "wait";
    public static final String MAINTAIN_STATUS_START = "start";
    public static final String MAINTAIN_STATUS_FINISH = "finish";
    public static final String MAINTAIN_TYPE_ONCE = "once";
    public static final String MAINTAIN_TYPE_CYCLE = "cycle";
    /**
     * new alarm
     */
    public static final String ALARM_PARAMS = "params";
    public static final String ALARM_CHECK_SCRIPTS = "alarmScript";
    public static final String ALARM_RESTORE_SCRIPT = "restoreScript";
    public static final String ALARM_TYPE = "alarmType";
    public static final String ALARM_SEVERITY = "severity";
    public static final String ALARM_PROPAGATE = "propagate";
    public static final String ALARM_RECORDING = "record";
    /**
     * 报警类型-周期
     */
    public static final String ALARM_TYPE_CYCLE = "cycle";

    public static final String DEVICE_TYPE_PORT = "MODBUS";
    public static final String DEVICE_TYPE_DTU = "DTU";


    public static final String PROTOCOL_TYPE_MODBUS_RTU = "rtu";
    public static final String PROTOCOL_TYPE_MODBUS_TCP = "tcp";
    public static final String PROTOCOL_TYPE_MODBUS = "MODBUS";

    // 设备列表
    public static final String DATAV_CHARTS_TYPE_DEVICE_LIST = "deviceList";
    // 告警统计表格
    public static final String DATAV_CHARTS_TYPE_ALARM_TABLE = "alarmTable";
    // 告警列表
    public static final String DATAV_CHARTS_TYPE_ALARM_LIST = "alarmList";
    // 饼图
    public static final String DATAV_CHARTS_TYPE_PIE = "pie";
    // 柱状/折线图
    public static final String DATAV_CHARTS_TYPE_LINE_OR_BAR = "lineOrBar";
    // 资产 柱状/折线图
    public static final String DATAV_CHARTS_TYPE_ASSET_LINE_OR_BAR = "assetLineOrBar";

    // 设备数据
    public static final String DATAV_DATA_TYPE_DEVICE = "deviceData";
    // 资产数据
    public static final String DATAV_DATA_TYPE_ASSET = "ASSET";

    public static final String START = "start";
    public static final String END = "end";
    public static final String CONSUMPTION = "consumption";
    //指数运算符
    public static final String INDEX = "\\^";
    public static final String FORMULA_SELF = "A";
    public static final String ALARM_TIMESTAMP = "ts";
    public static final String ALARM_INFO = "info";
    public static final String ALARM_STATUS = "status";

    public static final String IS_DELETE_YES = "1";
    public static final String IS_DELETE_NO = "0";

    public static final String LOGO_URL = "logoUrl";

    // grafana数据类型 资产列表
    public static final String GRAFANA_RESOURCE_ASSET_LIST = "assetList";
    // grafana数据类型 设备列表
    public static final String GRAFANA_RESOURCE_DEVICE_LIST = "deviceList";
    // grafana数据类型 虚拟表列表
    public static final String GRAFANA_RESOURCE_VIRTUAL_LIST = "virtualList";

    // grafana数据属性类型 资产列表
    public static final String GRAFANA_ATTRIBUTES_ASSET_LIST = "assetAttributes";
    // grafana数据属性类型 设备列表
    public static final String GRAFANA_ATTRIBUTES_DEVICE_LIST = "deviceAttributes";
    // grafana数据属性类型 虚拟表列表
    public static final String GRAFANA_ATTRIBUTES_VIRTUAL_LIST = "virtualAttributes";

    public static final String CONSTANTS_DEVICE_TEMPLATE = "deviceTemplate";

    /**
     * 扩展菜单标识
     */
    public static final String IS_EXTENSION_MENU_TRUE = "1";
    public static final String IS_EXTENSION_MENU_FALSE = "0";
    public static final int MENU_POOL_STATUS_IS_EXTENSION_MENU = 5;

    // 企业默认设备数
    public static final int DEFAULT_DEVICE_SIZE = 15;
    /**
     * 默认设备报警详情数
     */
    public static final int DEFAULT_ALARM_CONF = 50;
    //数据上报IP
    public static final String HITSDB_IP = "http://localhost";
    public static final String HITSDB_PORT = "4242"; //实例端口
    public static final String PUT_URL = HITSDB_IP + ":" + HITSDB_PORT +
            "/api/put?details=true";

    // public static final String INFLUX_PORT = "8086";
    // public static final String INFLUX_URL = HITSDB_IP + ":" + INFLUX_PORT;


    public static final String JERRY_EMAIL = "<EMAIL>";
    public static final String JERRY_PHONE = "18408226899";

    // rpc调用结果map
    public static ConcurrentMap<String, Optional> RPC_RESULT_MAP = new ConcurrentHashMap<>();


    //lot 模块相关
    public static final String TENCENT_LOT_CARD_IP = "ic.tencentcloudapi.com";
    public static final String TENCENT_LOT_CARD_ACTION = "DescribeCard";
    public static final String TENCENT_LOT_CARD_VERSION = "2019-03-07";

    /**
     * 资源类型
     */
    public enum ProjectRelationEntityType {

        DEVICE("设备"),
        MENU("菜单"),
        SCADA("组态"),
        IPC("工控机"),
        VIRTUAL("虚拟表"),
        ENERGY("能源"),
        ASSET("区域"),
        INPUT_KV("产量录入"),
        USER("用户"),
        ALARM_SETTING("告警设置"),
        ALARM("告警记录"),
        DATA_SOURCE("数据源"),
        ;


        private final String description;

        ProjectRelationEntityType(final String description) {
            this.description = description;
        }

        public String toString() {
            return description;
        }
    }

    /**
     * 站点类型
     */
    public enum StationType {

        WATER_SOURCE("水源地"),
        WATER_POOL("水池监测站"),
        WATER_PLANT("水厂"),
        PUMP_STATION("泵站"),
        FLOW_MONITORING("流量监测站"),
        XIAO_FANG_SHUAN("消防栓"),
        PRESSURE_MONITORING("压力监测站"),
        VALVE_MONITORING("阀门"),
        FLOW_FLOW_MONITORING("测流压站"),
        WATER_QUALITY_MONITORING("水质监测站"),
        BIG_USER("大用户"),
        WATER_POOL_STATION("水池监测站"),
        SEWAGE_PROCESS_STATION("污水处理厂"),
        ;


        private final String value;

        StationType(final String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
    /**
     * 站点类型
     */
    public enum DeviceAttrGroupType {

        WATER_OUTLET("出水口"),
        WATER_INLET("进水口"),
        ;


        private final String value;

        DeviceAttrGroupType(final String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }


    /**
     * 常见属性类型
     */
    public enum DeviceAttrType {

        TOTAL_FLOW("total_flow"),// 累计流量
        INSTANTANEOUS_FLOW("Instantaneous_flow"),// 瞬时流量
        PRESSURE("pressure"), // 压力
        ENERGY_IN("ENERGY_IN"),// 有功电能
        RUNTIME("runtime"),// 运行时长
        TURBIDITY("turbidity"),// 浊度
        REMAINDER("remainder"),// 余氯
        PH("ph"),// ph
        TEMPERATURE("temperature"),// 温度
        TP("tp"),// 总磷
        TN("tn"),// 总氮
        COD("cod"),// cod
        BOD("bod"),// bod
        NH3("nh3"),// 氨氮
        PRESSURE_FRONT("pressure_front"),// 氨氮
        PRESSURE_BACKEND("pressure_backend"),// 氨氮
        VALVE_OPENING("valve_opening"),// 氨氮
        ;


        private final String value;

        DeviceAttrType(final String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

}
