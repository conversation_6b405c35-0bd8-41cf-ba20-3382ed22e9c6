package org.thingsboard.server.dao.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordType;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordTypePageRequest;

@Mapper
public interface OrderRecordTypeMapper extends BaseMapper<OrderRecordType> {
    IPage<OrderRecordType> findByPage(OrderRecordTypePageRequest request);

    String getNameById(String id);

    boolean update(OrderRecordType entity);

}
