// 曲线运行图表配置
import * as echarts from 'echarts'

// 生成模拟时间数据 - 格式为"00时", "01时", ..., "23时"
const generateTimeData = (hours = 24) => {
  const times = []
  for (let i = 0; i < hours; i++) {
    times.push(`${i.toString().padStart(2, '0')}时`)
  }
  return times
}

// 生成模拟流量数据
const generateFlowData = (length = 24) => {
  const data = []
  const baseFlow = 2500
  for (let i = 0; i < length; i++) {
    // 模拟一天的流量变化，早晚高峰
    const hour = i
    let multiplier = 1
    if (hour >= 6 && hour <= 9) multiplier = 1.3 // 早高峰
    else if (hour >= 17 && hour <= 21) multiplier = 1.2 // 晚高峰
    else if (hour >= 22 || hour <= 5) multiplier = 0.7 // 夜间低峰
    
    const value = baseFlow * multiplier + Math.random() * 200 - 100
    data.push(Math.round(value))
  }
  return data
}

// 生成预测流量数据
const generatePredictedFlowData = (length = 24) => {
  const data = []
  const baseFlow = 2600
  for (let i = 0; i < length; i++) {
    const hour = i
    let multiplier = 1
    if (hour >= 6 && hour <= 9) multiplier = 1.35 // 预测早高峰更高
    else if (hour >= 17 && hour <= 21) multiplier = 1.25 // 预测晚高峰
    else if (hour >= 22 || hour <= 5) multiplier = 0.65 // 预测夜间更低
    
    const value = baseFlow * multiplier + Math.random() * 150 - 75
    data.push(Math.round(value))
  }
  return data
}

// 生成压力数据
const generatePressureData = (length = 24, type = 'outlet') => {
  const data = []
  let basePressure = 0.45

  if (type === 'outlet') {
    basePressure = 0.45
  } else if (type === 'predicted') {
    basePressure = 0.48 // 预测压力稍高一些
  } else {
    basePressure = 0.28 // 其他类型
  }

  for (let i = 0; i < length; i++) {
    let value = basePressure

    if (type === 'predicted') {
      // 预测压力有不同的变化模式
      value += Math.sin(i * 0.3) * 0.05 + (Math.random() - 0.5) * 0.08
    } else {
      value += (Math.random() - 0.5) * 0.1
    }

    data.push(Number(value.toFixed(2)))
  }
  return data
}

// 生成浓度数据
const generateConcentrationData = (length = 24) => {
  const data = []
  for (let i = 0; i < length; i++) {
    // 模拟药物浓度变化，呈现波动趋势
    const base = 1.2
    const variation = Math.sin(i * Math.PI / 12) * 0.3 + Math.random() * 0.2 - 0.1
    data.push(Number((base + variation).toFixed(2)))
  }
  return data
}

// 出厂水流量曲线配置
export const flowChartOption = (data?: any) => {
  // 只使用传入的真实数据，不使用模拟数据
  const timeData = data?.timeData || generateTimeData()
  const actualData = data?.actualData || []
  const predictedData = data?.predictedData || []



  return {
    color: ['#5470c6', '#91cc75'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value} m³/h<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['实际流量', '预测流量'],
      top: 10
    },
    grid: {
      left: 60,
      right: 60,
      top: 50,
      bottom: 40
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '流量(m³/h)',
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '实际流量',
        type: 'line',
        data: actualData,
        smooth: true,
        connectNulls: false, // 不连接null值
        lineStyle: {
          width: 2,
          color: '#ff7f0e' // 橙色
        },
        itemStyle: {
          color: '#ff7f0e' // 橙色
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      },
      {
        name: '预测流量',
        type: 'line',
        data: predictedData,
        smooth: true,
        connectNulls: true, // 预测数据连接所有点
        lineStyle: {
          width: 2,
          type: 'dashed',
          color: '#1f77b4' // 蓝色
        },
        itemStyle: {
          color: '#1f77b4', // 蓝色
          opacity: 0.8
        }
      }
    ]
  }
}

// 压力变化趋势配置
export const pressureChartOption = (data?: any) => {
  // 只使用传入的真实数据，不使用模拟数据
  const timeData = data?.timeData || generateTimeData()
  const outletPressure = data?.outletPressure || []
  const predictedPressure = data?.predictedPressure || []



  return {
    color: ['#ee6666', '#73c0de'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value} MPa<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['出厂压力', '预测压力'],
      top: 10
    },
    grid: {
      left: 60,
      right: 60,
      top: 50,
      bottom: 40
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '压力(MPa)',
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '出厂压力',
        type: 'line',
        data: outletPressure,
        smooth: true,
        connectNulls: false, // 不连接null值
        lineStyle: {
          width: 2,
          color: '#ff7f0e' // 橙色
        },
        itemStyle: {
          color: '#ff7f0e' // 橙色
        }
      },
      {
        name: '预测压力',
        type: 'line',
        data: predictedPressure,
        smooth: true,
        connectNulls: true, // 预测数据连接所有点
        lineStyle: {
          width: 2,
          type: 'dashed', // 预测压力使用虚线
          color: '#1f77b4' // 蓝色
        },
        itemStyle: {
          color: '#1f77b4' // 蓝色
        }
      }
    ]
  }
}

// 水体药物浓度预测配置
export const concentrationChartOption = (data?: any) => {
  // 使用传入的数据或生成模拟数据
  const timeData = data?.timeData || generateTimeData()
  const concentrationData = data?.concentrationData || generateConcentrationData()

  return {
    color: ['#409eff'],
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `时间: ${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value} mg/L<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['药物浓度预测'],
      top: 10
    },
    grid: {
      left: 60,
      right: 60,
      top: 50,
      bottom: 40
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '浓度(mg/L)',
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '药物浓度预测',
        type: 'line',
        data: concentrationData,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#409eff'
        },
        itemStyle: {
          color: '#409eff'
        },
        areaStyle: {
          opacity: 0.3,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(64, 158, 255, 0.8)'
            }, {
              offset: 1, color: 'rgba(64, 158, 255, 0.1)'
            }]
          }
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      }
    ]
  }
}


