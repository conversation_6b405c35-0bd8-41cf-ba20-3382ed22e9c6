<template>
  <el-tag
    :hit="props.hit || false"
    :round="props.round"
    :style="styles"
    :size="props.size"
    :closable="props.closeable"
    :disable-transitions="props.disableTransitions"
    :effect="props.effect"
    :type="props.tagType"
  >
    <slot></slot>
  </el-tag>
</template>
<script lang="ts" setup>
import { hexToRgba } from '@/utils/GlobalHelper'

const props = defineProps<{
  color?: string |((row?: any, val?: any) => string)
  round?: boolean
  row?: any
  value?: any
  closeable?: boolean
  disableTransitions?: boolean
  hit?: boolean
  size?: ISize
  effect?: 'dark' | 'light' | 'plain'
  tagType?: 'success' | 'info' | 'warning' | 'danger'
}>()
// const state = reactive<{
//   styles: Record<string, any>
// }>({
//   styles: {}
// })
const styles = computed(() => {
  const tColor = typeof props.color === 'function'
    ? props.color(props.row, props.value)
    : props.color
  return {
    '--el-tag-bg-color': hexToRgba(tColor, 0.2),
    '--el-tag-border-color': hexToRgba(tColor, 0.2),
    '--el-tag-hover-color': tColor,
    '--el-tag-text-color': tColor
  }
})
</script>
<style lang="scss" scoped></style>
