<template>
  <el-dialog
    v-model="visible"
    :title="getDialogTitle()"
    width="800px"
    :before-close="handleClose"
  >
    <!-- 数据加载中的骨架屏 -->
    <div v-if="dataLoading" class="loading-skeleton">
      <el-skeleton :rows="4" animated />
    </div>

    <!-- 表单内容 -->
    <el-form
      v-else
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="配置名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入配置名称" :readonly="readonly" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="配置编码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入配置编码" :readonly="readonly" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="配置类型" prop="type">
              <el-select
                v-model="formData.type"
                placeholder="请选择"
                style="width: 100%"
                :disabled="readonly"
                @change="handleTypeChange"
              >
                <el-option label="管网" value="0" />
                <el-option label="泵站" value="1" />
                <el-option label="其他" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择"
                style="width: 100%"
                :disabled="readonly"
              >
                <el-option label="启用" value="0" />
                <el-option label="停用" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表单配置 -->
      <div class="form-section">
        <h4 class="section-title">表单配置</h4>
        <div class="table-container">
          <el-table :data="formData.formConfig" border style="width: 100%">
            <el-table-column prop="location" label="巡检地点" width="150">
              <template #default="{ row, $index }">
                <el-input
                  v-model="row.location"
                  placeholder="请输入巡检地点"
                  :readonly="readonly"
                />
              </template>
            </el-table-column>

            <el-table-column prop="position" label="巡检位置" width="120">
              <template #default="{ row, $index }">
                <el-input
                  v-model="row.position"
                  placeholder="请输入巡检位置"
                  :readonly="readonly"
                />
              </template>
            </el-table-column>
            
            <el-table-column prop="content" label="检验内容" min-width="200">
              <template #default="{ row, $index }">
                <el-input 
                  v-model="row.content" 
                  placeholder="请输入检验内容"
                  :readonly="readonly"
                />
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="80" v-if="!readonly">
              <template #default="{ row, $index }">
                <el-button 
                  type="danger" 
                  size="small" 
                  circle
                  @click="removeFormItem($index)"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 新增行 -->
          <div class="add-row" v-if="!readonly">
            <el-input
              v-model="newRow.location"
              placeholder="请输入巡检地点"
              style="width: 150px; margin-right: 10px"
            />

            <el-input
              v-model="newRow.position"
              placeholder="请输入巡检位置"
              style="width: 120px; margin-right: 10px"
            />

            <el-input
              v-model="newRow.content"
              placeholder="请输入检验内容"
              style="width: 200px; margin-right: 10px"
            />

            <el-button type="primary" @click="addFormItem">
              <el-icon><Plus /></el-icon>
              增加一行
            </el-button>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ readonly ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!readonly" type="primary" @click="handleSubmit" :loading="submitLoading">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Close, Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { getCircuitSettingsById, addCircuitSettings, updateCircuitSettings } from '@/api/CircuitSettings/circuitSettings'

interface FormConfigItem {
  location: string
  position: string
  content: string
}

interface Props {
  modelValue: boolean
  editId?: string
  readonly?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  editId: '',
  readonly: false
})

const emit = defineEmits<Emits>()

const visible = ref(false)
const dataLoading = ref(false)
const submitLoading = ref(false)
const formRef = ref<FormInstance>()

const formData = reactive({
  name: '',
  code: '',
  type: '',
  status: '0',
  formConfig: [] as FormConfigItem[]
})

const newRow = reactive({
  location: '',
  position: '',
  content: ''
})



const rules: FormRules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入配置编码', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择配置类型', trigger: 'change' }
  ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, async (val) => {
  visible.value = val
  if (val) {
    if (props.editId) {
      await loadEditData()
    } else {
      resetForm()
    }
  }
})

// 监听弹窗关闭
watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 获取弹窗标题
const getDialogTitle = () => {
  if (props.readonly) {
    return '查看巡检配置'
  }
  return props.editId ? '编辑巡检配置' : '新建巡检配置'
}

// 重置表单
const resetForm = () => {
  formData.name = ''
  formData.code = ''
  formData.type = ''
  formData.status = '0'
  formData.formConfig = []
  resetNewRow()
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 重置新增行
const resetNewRow = () => {
  newRow.location = ''
  newRow.position = ''
  newRow.content = ''
}

// 巡检类型改变
const handleTypeChange = (value: string) => {
  // 可以根据不同类型预设不同的表单配置
  if (!props.editId) {
    formData.formConfig = []
  }
}

// 添加表单项
const addFormItem = () => {
  if (!newRow.location || !newRow.position || !newRow.content) {
    ElMessage.warning('请填写完整信息')
    return
  }
  
  formData.formConfig.push({
    location: newRow.location,
    position: newRow.position,
    content: newRow.content
  })
  
  resetNewRow()
}

// 删除表单项
const removeFormItem = (index: number) => {
  formData.formConfig.splice(index, 1)
}

// 加载编辑数据
const loadEditData = async () => {
  if (!props.editId) return

  try {
    dataLoading.value = true
    const res = await getCircuitSettingsById(props.editId)
    if (res?.data) {
      const responseData = res.data.data || res.data
      formData.name = responseData.name || ''
      formData.code = responseData.code || ''
      formData.type = responseData.type || ''
      formData.status = responseData.status || '0'

      // 解析表单配置JSON
      if (responseData.formConfig) {
        try {
          formData.formConfig = JSON.parse(responseData.formConfig)
        } catch (e) {
          console.error('解析表单配置失败:', e)
          formData.formConfig = []
        }
      } else {
        formData.formConfig = []
      }
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    dataLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (formData.formConfig.length === 0) {
      ElMessage.warning('请至少添加一项表单配置')
      return
    }

    submitLoading.value = true

    const submitData = {
      name: formData.name,
      code: formData.code,
      type: formData.type,
      status: formData.status,
      formConfig: JSON.stringify(formData.formConfig)
    }

    if (props.editId) {
      await updateCircuitSettings(props.editId, submitData)
      ElMessage.success('更新成功')
    } else {
      await addCircuitSettings(submitData)
      ElMessage.success('创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error(error)
    ElMessage.error(props.editId ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  dataLoading.value = false
  submitLoading.value = false
  resetForm()
}
</script>

<style lang="scss" scoped>
.form-section {
  margin-bottom: 24px;
  
  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }
}

.table-container {
  .add-row {
    margin-top: 16px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.loading-skeleton {
  padding: 20px 0;
}
</style>
