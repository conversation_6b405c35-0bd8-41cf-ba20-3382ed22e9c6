import{r as It}from"./deduplicate-Clsym5GM.js";import{T as x}from"./InterleavedLayout-EYSqXknm.js";import{O as u}from"./VertexAttribute-BAIQI41G.js";import{o as j}from"./glUtil-D4FNL8tc.js";import{fu as dt,fv as At}from"./index-r0dFAfgr.js";import{aQ as ht,ae as lt,af as $,b4 as et,aS as Ot,aR as B,aO as nt,ab as X,aV as St,aU as wt,ac as ft,aW as ot}from"./MapView-DaoQedLH.js";function rt(t,e,o){const r=e/3,s=new Uint32Array(o+1),c=new Uint32Array(o+1),p=(n,i)=>{n<i?s[n+1]++:c[i+1]++};for(let n=0;n<r;n++){const i=t[3*n],g=t[3*n+1],m=t[3*n+2];p(i,g),p(g,m),p(m,i)}let a=0,N=0;for(let n=0;n<o;n++){const i=s[n+1],g=c[n+1];s[n+1]=a,c[n+1]=N,a+=i,N+=g}const l=new Uint32Array(6*r),f=s[o],d=(n,i,g)=>{if(n<i){const m=s[n+1]++;l[2*m]=i,l[2*m+1]=g}else{const m=c[i+1]++;l[2*f+2*m]=n,l[2*f+2*m+1]=g}};for(let n=0;n<r;n++){const i=t[3*n],g=t[3*n+1],m=t[3*n+2];d(i,g,n),d(g,m,n),d(m,i,n)}const A=(n,i)=>{const g=2*n,m=i-n;for(let O=1;O<m;O++){const v=l[g+2*O],P=l[g+2*O+1];let S=O-1;for(;S>=0&&l[g+2*S]>v;S--)l[g+2*S+2]=l[g+2*S],l[g+2*S+3]=l[g+2*S+1];l[g+2*S+2]=v,l[g+2*S+3]=P}};for(let n=0;n<o;n++)A(s[n],s[n+1]),A(f+c[n],f+c[n+1]);const I=new Int32Array(3*r),E=(n,i)=>n===t[3*i]?0:n===t[3*i+1]?1:n===t[3*i+2]?2:-1,h=(n,i)=>{const g=E(n,i);I[3*i+g]=-1},T=(n,i,g,m)=>{const O=E(n,i);I[3*i+O]=m;const v=E(g,m);I[3*m+v]=i};for(let n=0;n<o;n++){let i=s[n];const g=s[n+1];let m=c[n];const O=c[n+1];for(;i<g&&m<O;){const v=l[2*i],P=l[2*f+2*m];v===P?(T(n,l[2*i+1],P,l[2*f+2*m+1]),i++,m++):v<P?(h(n,l[2*i+1]),i++):(h(P,l[2*f+2*m+1]),m++)}for(;i<g;)h(n,l[2*i+1]),i++;for(;m<O;)h(l[2*f+2*m],l[2*f+2*m+1]),m++}return I}const vt=x().vec3f(u.POSITION).u16(u.COMPONENTINDEX).u16(u.U16PADDING),$t=x().vec2u8(u.SIDENESS);j($t);const ut=x().vec3f(u.POSITION0).vec3f(u.POSITION1).u16(u.COMPONENTINDEX).u8(u.VARIANTOFFSET,{glNormalized:!0}).u8(u.VARIANTSTROKE).u8(u.VARIANTEXTENSION,{glNormalized:!0}).u8(u.U8PADDING,{glPadding:!0}).u16(u.U16PADDING,{glPadding:!0}),G=ut.clone().vec3f(u.NORMAL),K=ut.clone().vec3f(u.NORMALA).vec3f(u.NORMALB);u.POSITION0,u.POSITION1,u.COMPONENTINDEX,u.VARIANTOFFSET,u.VARIANTSTROKE,u.VARIANTEXTENSION,u.NORMAL,u.NORMALA,u.NORMALB,u.SIDENESS;let gt=class{updateSettings(e){this.settings=e,this._edgeHashFunction=e.reducedPrecision?Tt:Et}write(e,o,r){const s=this._edgeHashFunction(r);b.seed=s;const c=b.getIntRange(0,255),p=b.getIntRange(0,this.settings.variants-1),a=.7,N=b.getFloat(),l=255*(.5*Pt(-(1-Math.min(N/a,1))+Math.max(0,N-a)/(1-a),1.2)+.5);e.position0.setVec(o,r.position0),e.position1.setVec(o,r.position1),e.componentIndex.set(o,r.componentIndex),e.variantOffset.set(o,c),e.variantStroke.set(o,p),e.variantExtension.set(o,l)}trim(e,o){return e.slice(0,o)}};const Q=new Float32Array(6),W=new Uint32Array(Q.buffer),y=new Uint32Array(1);function Et(t){const e=Q;e[0]=t.position0[0],e[1]=t.position0[1],e[2]=t.position0[2],e[3]=t.position1[0],e[4]=t.position1[1],e[5]=t.position1[2],y[0]=5381;for(let o=0;o<W.length;o++)y[0]=31*y[0]+W[o];return y[0]}function Tt(t){const e=Q;e[0]=M(t.position0[0]),e[1]=M(t.position0[1]),e[2]=M(t.position0[2]),e[3]=M(t.position1[0]),e[4]=M(t.position1[1]),e[5]=M(t.position1[2]),y[0]=5381;for(let o=0;o<W.length;o++)y[0]=31*y[0]+W[o];return y[0]}const st=1e4;function M(t){return Math.round(t*st)/st}function Pt(t,e){const o=t<0?-1:1;return Math.abs(t)**e*o}let z=class{constructor(){this._commonWriter=new gt}updateSettings(e){this._commonWriter.updateSettings(e)}allocate(e){return G.createBuffer(e)}write(e,o,r){this._commonWriter.write(e,o,r),ht(_,r.faceNormal0,r.faceNormal1),lt(_,_),e.normal.setVec(o,_)}trim(e,o){return this._commonWriter.trim(e,o)}};z.Layout=G,z.glLayout=j(G,1);let k=class{constructor(){this._commonWriter=new gt}updateSettings(e){this._commonWriter.updateSettings(e)}allocate(e){return K.createBuffer(e)}write(e,o,r){this._commonWriter.write(e,o,r),e.normalA.setVec(o,r.faceNormal0),e.normalB.setVec(o,r.faceNormal1)}trim(e,o){return this._commonWriter.trim(e,o)}};k.Layout=K,k.glLayout=j(K,1);const _=$(),b=new dt,V=-1;var it;function yt(t,e,o,r=xt){const s=t.vertices.position,c=t.vertices.componentIndex,p=et(r.anglePlanar),a=et(r.angleSignificantEdge),N=Math.cos(a),l=Math.cos(p),f=q.edge,d=f.position0,A=f.position1,I=f.faceNormal0,E=f.faceNormal1,h=Rt(t),T=Mt(t),n=T.length/4,i=e.allocate(n);let g=0;const m=n,O=o.allocate(m);let v=0,P=0,S=0;const J=At(0,n),F=new Float32Array(n);F.forEach((D,w,R)=>{s.getVec(T[4*w+0],d),s.getVec(T[4*w+1],A),R[w]=Ot(d,A)}),J.sort((D,w)=>F[w]-F[D]);const Y=new Array,Z=new Array;for(let D=0;D<n;D++){const w=J[D],R=F[w],C=T[4*w+0],Nt=T[4*w+1],L=T[4*w+2],U=T[4*w+3],tt=U===V;if(s.getVec(C,d),s.getVec(Nt,A),tt)B(I,h[3*L+0],h[3*L+1],h[3*L+2]),nt(E,I),f.componentIndex=c.get(C),f.cosAngle=X(I,E);else{if(B(I,h[3*L+0],h[3*L+1],h[3*L+2]),B(E,h[3*U+0],h[3*U+1],h[3*U+2]),f.componentIndex=c.get(C),f.cosAngle=X(I,E),Dt(f,l))continue;f.cosAngle<-.9999&&nt(E,I)}P+=R,S++,tt||Vt(f,N)?(e.write(i,g++,f),Y.push(R)):Lt(f,p)&&(o.write(O,v++,f),Z.push(R))}const mt=new Float32Array(Y.reverse()),pt=new Float32Array(Z.reverse());return{regular:{instancesData:e.trim(i,g),lodInfo:{lengths:mt}},silhouette:{instancesData:o.trim(O,v),lodInfo:{lengths:pt}},averageEdgeLength:P/S}}function Vt(t,e){return t.cosAngle<e}function Dt(t,e){return t.cosAngle>e}function Lt(t,e){const o=St(t.cosAngle),r=q.fwd,s=q.ortho;return wt(r,t.position1,t.position0),o*(X(ft(s,t.faceNormal0,t.faceNormal1),r)>0?-1:1)>e}function Mt(t){const e=t.faces.length/3,o=t.faces,r=t.neighbors;let s=0;for(let a=0;a<e;a++){const N=r[3*a+0],l=r[3*a+1],f=r[3*a+2],d=o[3*a+0],A=o[3*a+1],I=o[3*a+2];s+=N===V||d<A?1:0,s+=l===V||A<I?1:0,s+=f===V||I<d?1:0}const c=new Int32Array(4*s);let p=0;for(let a=0;a<e;a++){const N=r[3*a+0],l=r[3*a+1],f=r[3*a+2],d=o[3*a+0],A=o[3*a+1],I=o[3*a+2];(N===V||d<A)&&(c[p++]=d,c[p++]=A,c[p++]=a,c[p++]=N),(l===V||A<I)&&(c[p++]=A,c[p++]=I,c[p++]=a,c[p++]=l),(f===V||I<d)&&(c[p++]=I,c[p++]=d,c[p++]=a,c[p++]=f)}return c}function Rt(t){const e=t.faces.length/3,o=t.vertices.position,r=t.faces,s=H.v0,c=H.v1,p=H.v2,a=new Float32Array(3*e);for(let N=0;N<e;N++){const l=r[3*N+0],f=r[3*N+1],d=r[3*N+2];o.getVec(l,s),o.getVec(f,c),o.getVec(d,p),ot(c,c,s),ot(p,p,s),ft(s,c,p),lt(s,s),a[3*N+0]=s[0],a[3*N+1]=s[1],a[3*N+2]=s[2]}return a}(function(t){t[t.SOLID=0]="SOLID",t[t.SKETCH=1]="SKETCH"})(it||(it={}));const q={edge:{position0:$(),position1:$(),faceNormal0:$(),faceNormal1:$(),componentIndex:0,cosAngle:0},ortho:$(),fwd:$()},H={v0:$(),v1:$(),v2:$()},xt={anglePlanar:4,angleSignificantEdge:35};function zt(t){const e=Ft(t.data,t.skipDeduplicate,t.indices,t.indicesLength);return ct.updateSettings(t.writerSettings),at.updateSettings(t.writerSettings),yt(e,ct,at)}function Ft(t,e,o,r){if(e){const p=rt(o,r,t.count);return new Ut(o,r,p,t)}const s=It(t.buffer,t.stride/4,{originalIndices:o,originalIndicesLength:r}),c=rt(s.indices,r,s.uniqueCount);return{faces:s.indices,facesLength:s.indices.length,neighbors:c,vertices:vt.createView(s.buffer)}}class Ut{constructor(e,o,r,s){this.faces=e,this.facesLength=o,this.neighbors=r,this.vertices=s}}const ct=new z,at=new k,kt=x().vec3f(u.POSITION0).vec3f(u.POSITION1),qt=x().vec3f(u.POSITION0).vec3f(u.POSITION1).u16(u.COMPONENTINDEX).u16(u.U16PADDING,{glPadding:!0});export{vt as A,kt as a,zt as f,qt as m,yt as p,Ft as u};
