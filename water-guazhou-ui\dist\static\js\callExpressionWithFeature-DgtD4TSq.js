import{i as u,s as m}from"./Point-WxyopZva.js";import{T as l}from"./index-r0dFAfgr.js";import{q as p,C as f,B as y,v as d}from"./quantizationUtils-DtI9CsYu.js";function M(a,r,e,n,t){if(l(a))return null;const o=a.referencesGeometry()&&t?g(r,n,t):r,s=a.repurposeFeature(o);try{return a.evaluate({...e,$feature:s})}catch(c){return u.getLogger("esri.views.2d.support.arcadeOnDemand").warn("Feature arcade evaluation failed:",c),null}}const i=new Map;function g(a,r,e){const{transform:n,hasZ:t,hasM:o}=e;i.has(r)||i.set(r,w(r));const s=i.get(r)(a.geometry,n,t,o);return{...a,geometry:s}}function w(a){const r={};switch(a){case"esriGeometryPoint":return(e,n,t,o)=>d(n,r,e,t,o);case"esriGeometryPolygon":return(e,n,t,o)=>y(n,r,e,t,o);case"esriGeometryPolyline":return(e,n,t,o)=>f(n,r,e,t,o);case"esriGeometryMultipoint":return(e,n,t,o)=>p(n,r,e,t,o);default:return u.getLogger("esri.views.2d.support.arcadeOnDemand").error(new m("mapview-arcade",`Unable to handle geometryType: ${a}`)),e=>e}}export{M as i};
