package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordType;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.OrderRecordTypeMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordTypeSaveRequest;

@Service
public class OrderRecordTypeServiceImpl implements OrderRecordTypeService {
    @Autowired
    private OrderRecordTypeMapper mapper;

    @Override
    public IPage<OrderRecordType> findAllConditional(OrderRecordTypePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public OrderRecordType save(OrderRecordTypeSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    public boolean update(OrderRecordType entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public OrderRecordType findById(String id) {
        return mapper.selectById(id);
    }
}
