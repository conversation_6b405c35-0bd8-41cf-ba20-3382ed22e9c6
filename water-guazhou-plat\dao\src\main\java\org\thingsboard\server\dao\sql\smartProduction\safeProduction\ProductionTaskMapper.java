package org.thingsboard.server.dao.sql.smartProduction.safeProduction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.SafeProductionRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionTask;

@Mapper
public interface ProductionTaskMapper extends BaseMapper<ProductionTask> {

    IPage<ProductionTask> findList(IPage<ProductionTask> ipage, @Param("param") SafeProductionRequest request);
}
