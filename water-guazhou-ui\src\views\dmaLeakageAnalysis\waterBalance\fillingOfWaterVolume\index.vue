<!-- 水量填报 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData"></SLTree>
    </template>
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    />
    <SLCard
      class="card-table"
      :content-style="{ padding: '12px' }"
    >
      <div class="form-wrapper overlay-y">
        <Form
          ref="refForm"
          :config="FormConfig"
        ></Form>
      </div>
    </SLCard>
    <DialogForm
      ref="refDialog"
      :config="DialogConfig"
    ></DialogForm>
    <DialogForm
      ref="refDetail"
      :config="DetailConfig"
    >
      <WaterBalanceDetail
        ref="refDetailTable"
        :partition="TreeData.currentProject"
      ></WaterBalanceDetail>
    </DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { DMAReportWater, GetWaterBalanceDetail, GetWaterBalanceTotalWater } from '@/api/mapservice/dma/waterBalance'
import { ICardSearchIns, IDialogFormIns, IFormIns } from '@/components/type'
import { usePartition } from '@/hooks/arcgis'
import { formatterMonth } from '@/utils/GlobalHelper'
import { SLConfirm, SLMessage } from '@/utils/Message'
import WaterBalanceDetail from './components/WaterBalanceDetail.vue'

const refDetailTable = ref<InstanceType<typeof WaterBalanceDetail>>()
const refForm = ref<IFormIns>()
const refDialog = ref<IDialogFormIns>()
const refSearch = ref<ICardSearchIns>()
const refDetail = ref<IDialogFormIns>()
const state = reactive<{
  // 当前弹窗数据提交时要计算的字段
  curFilling: string
  // 当前弹窗提交时要汇总的字段
  sumField: string
}>({
  curFilling: '',
  sumField: ''
})
const partition = usePartition()
const TreeData = reactive<SLTreeConfig>({
  data: [],
  title: '选择分区',
  isFilterTree: true,
  expandOnClickNode: false,
  treeNodeHandleClick: params => {
    TreeData.currentProject = params
    refreshData()
  },
  autoFillOptions: () => {
    partition.getTree().then(() => {
      TreeData.data = partition.Tree.value || []
      TreeData.currentProject = TreeData.data[0]
      refreshData()
    })
  }
})

// 搜索栏初始化配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'month',
    month: moment().format(formatterMonth)
  },
  filters: [
    {
      type: 'select',
      field: 'type',
      clearable: false,
      width: '100px',
      options: [
        { label: '按月', value: 'month' }
        // { label: '按年', value: 'year' },
      ],
      label: '选择方式',
      extraFormItem: [
        {
          width: '160px',
          type: 'month',
          field: 'month',
          disabledDate(date) {
            return date > new Date()
          }
        }
      ]
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => {
            refreshData()
          },
          iconifyIcon: 'ep:search'
        },
        {
          perm: true,
          text: '填写帮助',
          type: 'default',
          plain: true,
          click: () => {
            refDetail.value?.openDialog()
          },
          iconifyIcon: 'ep:info-filled'
        },
        {
          perm: true,
          text: '保存',
          type: 'primary',
          click: () => {
            refForm.value?.Submit()
          },
          iconifyIcon: 'ep:circle-check'
        }
      ]
    }
  ]
})

const FormConfig = reactive<IFormConfig>({
  labelPosition: 'right',
  labelWidth: 200,
  group: [
    {
      fieldset: {
        desc: '基础信息',
        icon: 'material-symbols:sunny-outline',
        type: 'underline'
      },
      fields: [
        {
          md: 12,
          xl: 8,
          type: 'input',
          label: '填报单位',
          field: 'uploadName'
        },
        {
          md: 12,
          xl: 8,
          type: 'input',
          label: '填表年份',
          field: 'ym',
          disabled: true
        }
      ]
    },
    {
      handleHidden(row, query, group) {
        group.hidden = TreeData.currentProject?.path?.length !== 1
      },
      fieldset: {
        desc: '供水总量',
        icon: 'material-symbols:sunny-outline',
        type: 'underline'
      },
      fields: [
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '自产供水量',
          field: 'ownSupplyWater',
          suffix: 'm³',
          onChange: () => calcSum('supplyTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '外购供水量',
          field: 'buySupplyWater',
          suffix: 'm³',
          onChange: () => calcSum('supplyTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '趸售水量',
          field: 'batchSaleWater',
          suffix: 'm³',
          onChange: () => calcSum('supplyTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '供水总量',
          field: 'supplyTotalWater',
          suffix: 'm³',
          readonly: true
        }
      ]
    },
    {
      handleHidden(row, query, group) {
        group.hidden = TreeData.currentProject?.path?.length !== 2
      },
      fieldset: {
        desc: '供水总量',
        icon: 'material-symbols:sunny-outline',
        type: 'underline'
      },
      fields: [
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '进水量',
          field: 'ownSupplyWater',
          suffix: 'm³',
          onChange: () => calcSum('supplyTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '出水量',
          field: 'batchSaleWater',
          suffix: 'm³',
          onChange: () => calcSum('supplyTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '供水总量',
          field: 'supplyTotalWater',
          suffix: 'm³',
          readonly: true
        }
      ]
    },
    {
      fieldset: {
        desc: '注册用户用水量',
        icon: 'material-symbols:sunny-outline',
        type: 'underline'
      },
      fields: [
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '计费计量用水量',
          field: 'feeMeteringUseWater',
          suffix: 'm³',
          onChange: () => calcSum('useTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '计费未计量用水量',
          field: 'feeNoMeteringUseWater',
          suffix: 'm³',
          readonly: true,
          appendBtns: [
            {
              styles: { width: '55px' },
              perm: true,
              text: '填写',
              type: 'primary',
              click: () => openDialog('计费未计量用水量', 'feeNoMeteringUseWater', 'useTotalWater')
            }
          ]
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '免费计量用水量',
          field: 'freeMeteringUseWater',
          suffix: 'm³',
          readonly: true,
          appendBtns: [
            {
              styles: { width: '55px' },
              perm: true,
              text: '填写',
              type: 'primary',
              click: () => openDialog('免费计量用水量', 'freeMeteringUseWater', 'useTotalWater')
            }
          ]
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '免费未计量用水量',
          field: 'freeNoMeteringUseWater',
          suffix: 'm³',
          readonly: true,
          appendBtns: [
            {
              styles: { width: '55px' },
              perm: true,
              text: '填写',
              type: 'primary',
              click: () => openDialog('免费未计量用水量', 'freeNoMeteringUseWater', 'useTotalWater')
            }
          ]
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '注册用户用水量',
          field: 'useTotalWater',
          suffix: 'm³',
          readonly: true
        }
      ]
    },
    {
      fieldset: {
        desc: '漏失水量',
        icon: 'material-symbols:sunny-outline',
        type: 'underline'
      },
      fields: [
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '明漏水量',
          field: 'frontLeakTotalWater',
          suffix: 'm³',
          readonly: true,
          appendBtns: [
            {
              styles: { width: '55px' },
              perm: true,
              text: '填写',
              type: 'primary',
              click: () => openDialog('明漏水量', 'frontLeakTotalWater', 'leakTotalWater')
            }
          ]
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '暗漏水量',
          field: 'backendLeakTotalWater',
          suffix: 'm³',
          readonly: true,
          appendBtns: [
            {
              styles: { width: '55px' },
              perm: true,
              text: '填写',
              type: 'primary',
              click: () => openDialog('暗漏水量', 'backendLeakTotalWater', 'leakTotalWater')
            }
          ]
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '背景漏失水量',
          field: 'backgroundLeakWater',
          suffix: 'm³',
          onChange: () => calcSum('leakTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '水箱、水池的售楼和溢流水量',
          field: 'shuixiangLeakWater',
          suffix: 'm³',
          onChange: () => calcSum('leakTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '漏失水量',
          field: 'leakTotalWater',
          suffix: 'm³',
          readonly: true
        }
      ]
    },
    {
      fields: [
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '居民用户表具误差损失水量',
          field: 'custMistakeLossWater',
          suffix: 'm³',
          onChange: () => calcSum('mistakeLossTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '非居民用户表具误差损失水量',
          field: 'nonCustMistakeLossWater',
          suffix: 'm³',
          onChange: () => calcSum('mistakeLossTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '计量损失水量',
          field: 'mistakeLossTotalWater',
          readonly: true,
          suffix: 'm³'
        }
      ]
    },
    {
      fields: [
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '未注册用户用水和用户拒查等',
          field: 'noRegisterLossWater',
          suffix: 'm³',
          readonly: true,
          appendBtns: [
            {
              styles: { width: '55px' },
              perm: true,
              text: '填写',
              type: 'primary',
              click: () => openDialog('未注册用户用水和用户拒查等', 'noRegisterLossWater', 'otherLossTotalWater')
            }
          ]
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '管理因素导致的损失水量',
          field: 'pipeLossWater',
          suffix: 'm³',
          onChange: () => calcSum('otherLossTotalWater')
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '其他漏失水量',
          field: 'otherLossTotalWater',
          readonly: true,
          suffix: 'm³'
        }
      ]
    },
    {
      fields: [
        {
          md: 12,
          xl: 12,
          type: 'input-number',
          label: '漏损水量',
          field: 'lossTotalWater',
          readonly: true,
          suffix: 'm³'
        }
      ]
    },
    {
      fieldset: {
        desc: '系统数据',
        icon: 'material-symbols:sunny-outline',
        type: 'underline'
      },
      fields: [
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: 'DN75（含）以上管道长度',
          field: 'dn75PipeLength',
          suffix: 'km'
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '单位供水量管长',
          field: 'unitSupplyPipeLength',
          suffix: 'km/m³'
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '年平均出厂压力',
          field: 'yearAvgPressure',
          suffix: 'Mpa'
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '居民抄表到户水量',
          field: 'custCopiedWater',
          suffix: 'm³'
        },
        {
          md: 12,
          xl: 8,
          type: 'input-number',
          label: '最大冻土深度',
          field: 'maxFrozenSoilDepth',
          suffix: 'm'
        }
      ]
    }
  ],
  submit(params) {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        try {
          const res = await DMAReportWater(params)
          if (res.data.code === 200) {
            SLMessage.success('提交成功')
          } else {
            SLMessage.error(res.data.message)
          }
        } catch (error) {
          SLMessage.error('操作失败')
        }
      })
      .catch(() => {
        //
      })
  }
})

const openDialog = (title: string, field: string, sumField: string) => {
  DialogConfig.title = title
  state.curFilling = field
  state.sumField = sumField
  DialogConfig.defaultValue = {
    ...(refForm.value?.dataForm || {})
  }
  refDialog.value?.openDialog()
}
const handleHidden = (row, query, group) => {
  group.hidden = state.curFilling !== group.id
}
const DialogConfig = reactive<IDialogFormConfig>({
  dialogWidth: 600,
  labelPosition: 'right',
  labelWidth: 180,
  title: '',
  group: [
    {
      handleHidden,
      id: 'feeNoMeteringUseWater',
      fields: [
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'huanweiUseWater',
          label: '环卫用水'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'lvhuaUseWater',
          label: '绿化用水'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'pipeUseWater',
          label: '新建管道冲洗'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'sunhuaiUseWater',
          label: '第三方资产损坏'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'dingliangUseWater',
          label: '定量户用水'
        }
      ]
    },
    {
      handleHidden,
      id: 'freeMeteringUseWater',
      fields: [
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'bangongUseWater',
          label: '自来水企业生产/办公用水'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'xiaofangUseWater',
          label: '消防用水'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'jianmianUseWater',
          label: '政策性减免'
        }
      ]
    },
    {
      handleHidden,
      id: 'freeNoMeteringUseWater',
      fields: [
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'freeBangongUseWater',
          label: '自来水企业生产/办公用水'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'freeXiaofangUseWater',
          label: '消防用水'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'freeWeihuUseWater',
          label: '管网维护和冲洗水量'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'freeChongxiUseWater',
          label: '二次供水泵房冲洗'
        }
      ]
    },
    {
      id: 'frontLeakTotalWater',
      handleHidden,
      fields: [
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'frontPointLeakWater',
          label: '明漏点漏失水量'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'frontPipeLeakWater',
          label: '爆管漏失水量'
        }
      ]
    },
    {
      id: 'backendLeakTotalWater',
      handleHidden,
      fields: [
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'backendCheckedLeakWater',
          label: '已检出暗漏点水量'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'backendNoCheckedWater',
          label: '未检出暗漏点水量'
        }
      ]
    },
    {
      id: 'noRegisterLossWater',
      handleHidden,
      fields: [
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'toudaoLossWater',
          label: '偷盗用水量'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'copyMeterLossWater',
          label: '抄表误差水量'
        },
        {
          type: 'input-number',
          suffix: 'm³',
          field: 'otherLossWater',
          label: '其他损失水量'
        }
      ]
    }
  ],
  submit(params) {
    if (!refForm.value || !state.curFilling || !state.sumField) return
    let total = 0
    DialogConfig.group
      .find(item => item.id === state.curFilling)
      ?.fields.map(item => {
        if (refForm.value && item.field) {
          refForm.value.dataForm[item.field] = params[item.field]
          total += Number(params[item.field] || 0)
        }
      })
    refForm.value.dataForm[state.curFilling] = total
    calcSum(state.sumField)
    refDialog.value?.closeDialog()
  }
})
const calcSum = (field: string) => {
  if (!refForm.value) return
  const dataForm = refForm.value.dataForm || {}
  switch (field) {
    // 供水总量
    case 'supplyTotalWater':
      refForm.value.dataForm.supplyTotalWater = Number(dataForm.ownSupplyWater || 0)
        + Number(dataForm.buySupplyWater || 0)
        - Number(dataForm.batchSaleWater || 0)
      break
    // 注册用户用水量
    case 'useTotalWater':
      refForm.value.dataForm.useTotalWater = Number(dataForm.feeMeteringUseWater || 0)
        + Number(dataForm.feeNoMeteringUseWater || 0)
        + Number(dataForm.freeMeteringUseWater || 0)
        + Number(dataForm.freeNoMeteringUseWater || 0)
      break
    // 漏失水量
    case 'leakTotalWater':
      refForm.value.dataForm.leakTotalWater = Number(dataForm.frontLeakTotalWater || 0)
        + Number(dataForm.backendLeakTotalWater || 0)
        + Number(dataForm.backgroundLeakWater || 0)
        + Number(dataForm.shuixiangLeakWater || 0)
      calcSum('lossTotalWater')
      break
    // 计量损失水量
    case 'mistakeLossTotalWater':
      refForm.value.dataForm.mistakeLossTotalWater = Number(dataForm.custMistakeLossWater || 0) + Number(dataForm.nonCustMistakeLossWater || 0)
      calcSum('lossTotalWater')
      break
    // 其他漏失水量
    case 'otherLossTotalWater':
      refForm.value.dataForm.otherLossTotalWater = Number(dataForm.noRegisterLossWater || 0) + Number(dataForm.pipeLossWater || 0)
      calcSum('lossTotalWater')
      break
    // 漏损水量
    case 'lossTotalWater':
      refForm.value.dataForm.lossTotalWater = Number(dataForm.leakTotalWater || 0)
        + Number(dataForm.mistakeLossTotalWater || 0)
        + Number(dataForm.otherLossTotalWater || 0)
      break
    default:
      break
  }
}
const DetailConfig = reactive<IDialogFormConfig>({
  title: '填写帮助',
  group: [],
  dialogWidth: '80%'
  // submitText: '导出',
  // submit: () => {
  //   refDetailTable.value?.refTable?.exportTable()
  // }
})

const refreshData = async () => {
  if (!TreeData.currentProject) return
  const res = await GetWaterBalanceDetail({
    partitionId: TreeData.currentProject.value,
    ym: refSearch.value?.queryParams?.month
  })

  if (!refForm.value) return
  refForm.value.dataForm = {
    ...(res.data.data || {
      partitionId: TreeData.currentProject?.value,
      ym: refSearch.value?.queryParams?.month
    })
  }
  if (!res.data.data) {
    const supplyInfo = await GetWaterBalanceTotalWater({
      partitionId: TreeData.currentProject.value,
      month: refSearch.value?.queryParams?.month,
      direction: 'in'
    })
    const useInfo = await GetWaterBalanceTotalWater({
      partitionId: TreeData.currentProject.value,
      month: refSearch.value?.queryParams?.month,
      direction: 'out'
    })
    refForm.value.dataForm.ownSupplyWater = supplyInfo.data?.data ?? 0
    refForm.value.dataForm.batchSaleWater = useInfo.data?.data ?? 0
    calcSum('supplyTotalWater')
  }
  if (!refDialog.value?.refForm) return
  DialogConfig.defaultValue = {
    ...(res.data.data || {})
  }
}
</script>

<style lang="scss" scoped>
.card {
  height: 100%;
}
.form-wrapper {
  height: 100%;
}
</style>
