package org.thingsboard.server.controller.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.dispatch.EmergencyVehicleService;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyVehicle;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyVehiclePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyVehicleSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping({"/api/sp/mergencyVehicle"})
public class EmergencyVehicleController extends BaseController {
    @Autowired
    private EmergencyVehicleService service;

    public EmergencyVehicleController() {
    }

    @GetMapping
    public IPage<EmergencyVehicle> findAllConditional(EmergencyVehiclePageRequest request) {
        return this.service.findAllConditional(request);
    }

    @PostMapping
    public EmergencyVehicle save(@RequestBody EmergencyVehicleSaveRequest req) {
        return this.service.save(req);
    }

    @PatchMapping({"/{id}"})
    public boolean edit(@RequestBody EmergencyVehicleSaveRequest req, @PathVariable String id) {
        return this.service.update(req.update(id));
    }

    @DeleteMapping({"/{id}"})
    public boolean delete(@PathVariable String id) {
        return this.service.delete(id);
    }
}
