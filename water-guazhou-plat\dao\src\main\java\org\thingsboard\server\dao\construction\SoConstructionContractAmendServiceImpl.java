package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractAmend;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionContractAmendMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractAmendPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractAmendSaveRequest;

@Service
public class SoConstructionContractAmendServiceImpl implements SoConstructionContractAmendService {
    @Autowired
    private SoConstructionContractAmendMapper mapper;

    @Override
    public IPage<SoConstructionContractAmend> findAllConditional(SoConstructionContractAmendPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoConstructionContractAmend save(SoConstructionContractAmendSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SoConstructionContractAmend entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
