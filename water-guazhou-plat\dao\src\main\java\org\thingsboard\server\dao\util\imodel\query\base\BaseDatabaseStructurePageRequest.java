package org.thingsboard.server.dao.util.imodel.query.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseStructure;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

import java.util.Date;

/**
 * 公共管理-数据库结构修复对象 base_database_structure
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@ApiModel(value = "数据库结构修复", description = "平台管理-数据库结构修复实体类")
@Data
public class BaseDatabaseStructurePageRequest extends PageableQueryEntity<BaseDatabaseStructure> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String version;

    /**
     * 变更类型（字段/表/视图/定时任务/主键）
     */
    @ApiModelProperty(value = "变更类型")
    private String changeType;

    /**
     * 操作对象名（如表名user）
     */
    @ApiModelProperty(value = "操作对象名")
    private String objectName;

    /**
     * 对象类型
     */
    @ApiModelProperty(value = "对象类型")
    private String objectType;

    /**
     * 变更描述
     */
    @ApiModelProperty(value = "变更描述")
    private String changeDescription;

    /**
     * 实际执行的SQL
     */
    @ApiModelProperty(value = "实际执行的SQL")
    private String sqlStatement;

    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态")
    private String status;

    /**
     * 执行者
     */
    @ApiModelProperty(value = "执行者")
    private String executedBy;

    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executedAt;

    /**
     * 变更动作
     */
    @ApiModelProperty(value = "变更动作")
    private String manageType;
}
