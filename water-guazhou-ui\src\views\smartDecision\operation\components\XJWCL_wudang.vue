<template>
  <div class="inspection-comp-ratio">
    <div
      v-for="(item, i) in state.data"
      :key="i"
      class="item"
    >
      <div class="chart-wrapper">
        <VChart :option="item.option"></VChart>
      </div>
      <div class="blocks">
        <div
          v-for="(obj, j) in item.items"
          :key="j"
          class="block-item"
        >
          <div class="text">
            {{ obj.label }}
          </div>
          <div class="count">
            {{ obj.count }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { GetCircuitTaskStatusCount } from '@/api/mapservice/onemap'
import { GetCompleteRatio } from '@/api/workorder'

const generateOption = (title: string, ratio: number) => {
  return {
    series: [
      {
        type: 'gauge',
        min: 0,
        max: 100,
        startAngle: 200,
        endAngle: -20,
        splitNumber: 12,
        itemStyle: {
          color: 'rgb(60, 148, 221)'
        },
        radius: '100%',
        center: ['50%', '65%'],
        progress: {
          show: true,
          width: 12
        },
        pointer: {
          show: false
        },
        axisLine: {
          // roundCap: true,
          lineStyle: {
            width: 12
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: false
        },
        detail: {
          offsetCenter: [0, 0],
          valueAnimation: true,
          formatter(value) {
            return '{value|' + value.toFixed(0) + '}{unit|%}\n{name|' + title + '}'
          },
          rich: {
            name: {
              fontSize: 12,
              color: 'rgb(146, 209, 253)',
              fontWight: 'bolder'
            },
            value: {
              fontSize: 20,
              fontWeight: 'bolder',
              color: 'rgb(60, 148, 221)'
            },
            unit: {
              fontSize: 12,
              color: '#999'
              // padding: [0, 0, -20, 10]
            }
          }
        },
        data: [
          {
            value: ratio
          }
        ]
      }
    ]
  }
}
const state = reactive<{
  data: {
    ratio: number
    option: any
    items: { label: string; count: number }[]
  }[]
}>({
  data: [
    // {
    //   option: generateOption('巡检完成率', 100),
    //   ratio: 100,
    //   items: [
    //     { label: '巡检次数', count: 90 },
    //     { label: '完成次数', count: 90 }
    //   ]
    // },
    {
      ratio: 35.18,
      option: generateOption('工单及时率', 35.18),
      items: [
        { label: '完成工单总数', count: 4096 },
        { label: '及时完成数', count: 1441 }
      ]
    }
  ]
})
const refreshData = () => {
  // GetCircuitTaskStatusCount().then(res => {
  //   const data = res.data?.data ?? {}
  //   const complete = data.complete ?? 0
  //   const pending = data.pending ?? 0
  //   const received = data.received ?? 0
  //   const total = complete + pending + received
  //   const compRate = total === 0 ? 0 : Number((complete / total).toFixed(2))
  //   state.data[0].items = [
  //     { label: '巡检次数', count: total },
  //     { label: '完成次数', count: complete }
  //   ]
  //   state.data[0].option = generateOption('巡检完成率', compRate)
  // })
  GetCompleteRatio().then(res => {
    const data = res.data?.data || {}
    state.data[0].items = [
      { label: '完成工单总数', count: data.totalYearly },
      { label: '及时完成数', count: (data.totalYearly ?? 0) * (data.percentYearly ?? 0) }
    ]
    state.data[0].option = generateOption('工单及时率', Number(((data.percentYearly ?? 0) * 100)?.toFixed(2)))
  })
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.inspection-comp-ratio {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  .item {
    width: 50%;
    height: 100%;
    .chart-wrapper {
      width: 100%;
      height: 130px;
    }
    .blocks {
      height: 50px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .block-item {
        position: relative;
        border: 1px solid rgb(27, 93, 155);
        font-size: 12px;
        width: 45%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        &:first-child {
          border-right: none;
          border-radius: 8px 0 0 8px;
          &::after {
            content: ' ';
            width: 1px;
            height: 60%;
            background-color: rgb(27, 93, 155);
            position: absolute;
            right: 0;
            top: 20%;
          }
        }
        &:last-child {
          border-left: none;
          border-radius: 0 8px 8px 0;
        }
        .text {
          line-height: 25px;
          color: rgb(221, 170, 6);
        }
        .count {
          line-height: 25px;
          color: rgb(11, 238, 122);
        }
      }
    }
  }
}
</style>
