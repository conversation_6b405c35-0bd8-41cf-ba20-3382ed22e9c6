import{_ as h}from"./TreeBox-DDD2iwoR.js";import{_}from"./CardTable-rdWOL4_6.js";import{_ as b}from"./index-BJ-QPYom.js";import{G as w,e as x}from"./editDialogForm-C5uiUcxU.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as T,c as W,r as l,bB as y,o as C,g as D,h as F,F as c,q as p,i as a,C as M}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as P}from"./usePartition-DkcY9fQ2.js";import{e as v}from"./index-0NlGN6gS.js";import"./index-C9hz-UZb.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const k=T({__name:"index",setup(N){const m=W(),o=P(),i=l({data:[],loading:!1,isFilterTree:!0,currentProject:null,title:"选择分区",expandOnClickNode:!1,treeNodeHandleClick:async t=>{i.currentProject!==t&&(i.currentProject=t,await n())}}),s=()=>{o.getTree().then(()=>{var t;i.data=o.Tree.value,i.currentProject=(t=o.Tree.value)==null?void 0:t[0],n()})},e=l({loading:!1,indexVisible:!0,dataList:[],columns:[{prop:"partitionName",label:"分区名称",align:"center",fixed:"left",minWidth:160},{prop:"copyMeterType",label:"抄表类型",align:"center",minWidth:120,formatter:(t,r)=>v[r]},{prop:"nightFlow",label:"夜间最小流",align:"center",subColumns:[{prop:"nightFlowMin",label:"最小值",unit:"(m³/h)",align:"center",minWidth:120},{prop:"nightFlowMax",label:"最大值",unit:"(m³/h)",align:"center",minWidth:120}]},{prop:"nightValue",label:"夜间最小值",align:"center",subColumns:[{prop:"nightValueMin",label:"最小值",unit:"(m³/h)",align:"center",minWidth:120},{prop:"nightValueMax",label:"最大值",unit:"(m³/h)",align:"center",minWidth:120}]},{prop:"unitPipeNightFlow",label:"单位管长夜间流量",align:"center",subColumns:[{prop:"unitPipeNightFlowMin",label:"最小值",unit:"(m³/h)",align:"center",minWidth:120},{prop:"unitPipeNightFlowMax",label:"最大值",unit:"(m³/h)",align:"center",minWidth:120}]},{prop:"mnfDivDayAvgHourFlow",label:"MNF/日均小时流量",align:"center",subColumns:[{prop:"mnfDivDayAvgHourFlowMin",label:"最小值",unit:"(m³/h)",align:"center",minWidth:120},{prop:"mnfDivDayAvgHourFlowMax",label:"最大值",unit:"(m³/h)",align:"center",minWidth:120}]},{prop:"incrBase",label:"基准值",unit:"(m³/h)",align:"center",minWidth:120},{prop:"incrWarn",label:"黄色预警值",align:"center",minWidth:120},{prop:"incrError",label:"红色预警值",align:"center",minWidth:120},{prop:"isDefault",label:"是否默认",align:"center",subColumns:[{prop:"stockType",label:"存量",align:"center",minWidth:120,formatter:(t,r)=>r==="1"?"手动":r==="2"?"自动":r},{prop:"incrType",label:"增量",align:"center",minWidth:120,formatter:(t,r)=>r==="1"?"手动":r==="2"?"自动":r}]}],operationWidth:120,operations:[{perm:!0,text:"修改",isTextBtn:!1,iconifyIcon:"ep:edit",click:t=>u(t)}],pagination:{hide:!0}}),u=async t=>{var r;e.currentRow=t,await y(),(r=m.value)==null||r.openDialog()},n=async()=>{if(i.currentProject){e.loading=!0;try{const t=await w(i.currentProject.id);e.dataList=t.data.data||[]}catch{e.dataList=[]}e.loading=!1}};return C(()=>{s()}),(t,r)=>{const d=b,g=_,f=h;return D(),F(f,null,{tree:c(()=>[p(d,{ref:"refTree","tree-data":a(i)},null,8,["tree-data"])]),default:c(()=>[p(g,{class:"card-table",config:a(e)},null,8,["config"]),p(x,{ref_key:"refEditDialog",ref:m,"default-value":a(e).currentRow,onSuccess:n},null,8,["default-value"])]),_:1})}}}),tr=M(k,[["__scopeId","data-v-8a52f0a0"]]);export{tr as default};
