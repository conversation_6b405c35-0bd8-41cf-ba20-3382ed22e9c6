import{G as u}from"./index-CpGhZCTT.js";import{d as y,r as h,o as d,ay as b,g as x,h as O,i as _,l as c}from"./index-r0dFAfgr.js";const L=y({__name:"QXGZL",setup(g){const l=h({repaireOption:null}),f=o=>{const s=Array.from({length:12}).map((e,i)=>i+1+"月"),a=o,t=20;return{tooltip:{trigger:"item"},grid:{left:20,right:20,bottom:0,top:40,containLabel:!0},xAxis:[{boundaryGap:t,type:"category",data:s,axisLine:{show:!1},splitLine:{show:!1},axisLabel:{color:"#fff",rotate:45,margin:12},axisTick:{show:!1}}],yAxis:[{type:"value",splitLine:{show:!1},axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1}}],series:[{name:"抢修工作量",color:"#0099FF",type:"bar",data:a,hoverAnimation:!1,barWidth:t,label:{show:!0,position:"top",distance:10,color:"#fff"},itemStyle:{opacity:.8}},{name1:"bar0-1",color:"#0099FF",type:"pictorialBar",silent:!0,symbolSize:[t,t/2],symbolOffset:[0,t/4],data:a},{name1:"bar0-0",color:"#0099FF",silent:!0,type:"pictorialBar",symbolSize:[t,t/2],symbolOffset:[0,-t/4],data:a.map(e=>({value:e,symbolPosition:"end"}))}]}},m=()=>{u({fromTime:c().startOf("y").valueOf(),toTime:c().valueOf(),statisticType:!0,timeUnit:"MONTH"}).then(o=>{var t,r;const s=((r=(t=o.data)==null?void 0:t.data)==null?void 0:r.types)||[],a=[];s.map(e=>{var i;(i=e.data)!=null&&i.length&&e.data.map((p,n)=>{a[n]===void 0?a[n]=p.value??0:a[n]+=p.value??0})}),l.repaireOption=f(a)})};return d(async()=>{m()}),(o,s)=>{const a=b("VChart");return x(),O(a,{option:_(l).repaireOption},null,8,["option"])}}});export{L as _};
