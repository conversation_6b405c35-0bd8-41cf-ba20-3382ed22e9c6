/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.msg;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.WorkOrderVisitMsgDTO;
import org.thingsboard.server.dao.model.sql.MsgConfigEntity;
import org.thingsboard.server.dao.model.sql.MsgSendLogEntity;
import org.thingsboard.server.dao.model.sql.MsgTemplateEntity;
import org.thingsboard.server.dao.model.sql.smartService.call.MsgVisitBack;
import org.thingsboard.server.dao.orderWork.NewlyWorkOrderService;
import org.thingsboard.server.dao.sql.msg.MsgConfigMapper;
import org.thingsboard.server.dao.sql.msg.MsgSendLogMapper;
import org.thingsboard.server.dao.sql.msg.MsgTemplateMapper;
import org.thingsboard.server.dao.sql.smartService.call.MsgVisitBackMapper;
import org.thingsboard.server.dao.util.alicloud.AlibabaShortMessageSender;
import org.thingsboard.server.dao.util.imodel.query.smartService.SendVisitMsgRequest;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MsgTemplateServiceImpl implements MsgTemplateService {


    @Autowired
    private MsgTemplateMapper msgTemplateMapper;

    @Autowired
    private MsgConfigMapper msgConfigMapper;

    @Autowired
    private MsgSendLogMapper msgSendLogMapper;

    @Autowired
    private MsgVisitBackMapper msgVisitBackMapper;

    @Autowired
    private NewlyWorkOrderService newlyWorkOrderService;

    @Override
    public MsgTemplateEntity save(MsgTemplateEntity msgConfigEntity) {
        if (StringUtils.isBlank(msgConfigEntity.getId())) {
            msgConfigEntity.setCreateTime(new Date());
            msgTemplateMapper.insert(msgConfigEntity);

            return msgConfigEntity;
        }

        msgTemplateMapper.updateById(msgConfigEntity);
        return msgConfigEntity;
    }

    @Override
    public PageData<MsgTemplateEntity> getList(int page, int size, String configId, String name, String tenantId) {
        IPage<MsgTemplateEntity> iPage = new Page<>(page, size);

        Page<MsgTemplateEntity> list = msgTemplateMapper.getList(iPage, configId, name, tenantId);
        return new PageData<>(list.getTotal(), list.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        msgTemplateMapper.deleteBatchIds(ids);
    }

/*    public SendSmsResponseBody send(String phone) throws ExecutionException, InterruptedException {
        AlibabaShortMessageSender alibabaShortMessageSender = new AlibabaShortMessageSender("LTAI5tJ2RJStud9jzy4HZ5Yq", "******************************");

        SendSmsResponseBody send = alibabaShortMessageSender.send("SMS_463895307", phone, "眉山环天水务", templateParam);
        return send;
    }*/

    @Override
    public void send(JSONObject params) {
        String templateId = params.getString("templateId");
        MsgTemplateEntity msgTemplateEntity = msgTemplateMapper.selectById(templateId);
        MsgConfigEntity msgConfigEntity = msgConfigMapper.selectById(msgTemplateEntity.getConfigId());
        List<String> phoneList = params.getObject("phoneList", List.class);
        Map param = params.getObject("params", Map.class);
        String creator = params.getString("creator");
        String id = params.getString("id");
        String sendType = params.getString("sendType");
        if (sendType != null) {
            String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            param.put("date", date);
        }
        AlibabaShortMessageSender alibabaShortMessageSender = new AlibabaShortMessageSender(msgConfigEntity.getAccessKeyId(), msgConfigEntity.getAccessKeySecret());

        new Thread(() -> {
            MsgSendLogEntity msgSendLogEntity;
            MsgVisitBack msgVisitBack = new MsgVisitBack();
            Map<String, MsgVisitBack> sendListByWorkOrderIdList = params.getObject("sendListByWorkOrderIdList", Map.class);

            for (String phone : phoneList) {
                msgSendLogEntity = new MsgSendLogEntity();
                msgSendLogEntity.setTemplateId(templateId);
                msgSendLogEntity.setCreateTime(new Date());
                msgSendLogEntity.setPhone(phone);
                msgSendLogEntity.setParam(JSONObject.toJSONString(param));
                msgSendLogEntity.setCreator(creator);
                msgSendLogEntity.setTenantId(msgConfigEntity.getTenantId());
                if (StringUtils.isNotBlank(sendType)) {
                    String workOrderId = params.getString("workOrderId");
                    String sendUser = params.getString("sendUser");
                    msgVisitBack = new MsgVisitBack();
                    msgVisitBack.setPhone(phone);
                    msgVisitBack.setSendTime(new Date());
                    msgVisitBack.setCreateTime(new Date());
                    msgVisitBack.setVisitTime(null);
                    msgVisitBack.setWorkOrderId(workOrderId);
                    msgVisitBack.setSendUser(sendUser);
                }

                try {
                    SendSmsResponseBody send = alibabaShortMessageSender.send(msgTemplateEntity.getCode(), phone, msgConfigEntity.getSignName(), param);
                    msgSendLogEntity.setStatus("1");
                    msgSendLogEntity.setRemark("发送成功");
                    if (!"OK".equals(send.getCode())) {
                        msgVisitBack.setSendStatus("2");
                        msgVisitBack.setRemark(send.getMessage());
                        msgSendLogEntity.setStatus("2");
                        msgSendLogEntity.setRemark(send.getMessage());
                        if (send.getMessage().contains("Specified access key is not found")) {
                            msgVisitBack.setRemark("SpecifiedKey不存在");
                            msgSendLogEntity.setRemark("SpecifiedKey不存在");
                        } else if (send.getMessage().contains("Specified access key is disabled")) {
                            msgSendLogEntity.setRemark("SpecifiedKey被禁用");
                            msgVisitBack.setRemark("SpecifiedKey被禁用");
                        }
                    }
                } catch (Exception e) {
                    msgSendLogEntity.setStatus("2");
                    msgVisitBack.setSendStatus("2");
                    if (e.getMessage().contains("Specified access key is not found")) {
                        msgVisitBack.setRemark("SpecifiedKey不存在");
                        msgSendLogEntity.setRemark("SpecifiedKey不存在");
                    } else if (e.getMessage().contains("Specified access key is disabled")) {
                        msgSendLogEntity.setRemark("SpecifiedKey被禁用");
                        msgVisitBack.setRemark("SpecifiedKey被禁用");
                    } else {
                        msgVisitBack.setRemark(e.getMessage());
                        msgSendLogEntity.setRemark(e.getMessage());
                    }

                    e.printStackTrace();
                }

                try {
                    Set set = param.keySet();
                    Iterator iterator = set.iterator();
                    String content = msgTemplateEntity.getContent();
                    while (iterator.hasNext()) {
                        String next = (String) iterator.next();
                        content.replace("${" + next + "}", String.valueOf(param.get(next)));
                    }
                    msgSendLogEntity.setContent(content);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                // 保存回访短信
                if (StringUtils.isNotBlank(sendType)) {
                    if (sendListByWorkOrderIdList.containsKey(msgVisitBack.getWorkOrderId())) {
                        MsgVisitBack msgVisitBack1 = sendListByWorkOrderIdList.get(msgVisitBack.getWorkOrderId());
                        msgVisitBack1.setWorkOrderId(msgVisitBack1.getWorkOrderId() + "-" + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));
                        msgVisitBackMapper.updateById(msgVisitBack1);
                    }
                    msgVisitBackMapper.insert(msgVisitBack);
                }

                // 短信记录
                if (StringUtils.isNotBlank(id)) {
                    msgSendLogEntity.setId(id);
                    msgSendLogMapper.updateById(msgSendLogEntity);
                } else {
                    msgSendLogMapper.insert(msgSendLogEntity);
                }
            }
        }).start();
    }


    public void sendOne(String phone, Map<String, String> param, String templateId, String creator, String custCode, String tenantId) {
        MsgTemplateEntity msgTemplateEntity = msgTemplateMapper.selectById(templateId);
        MsgConfigEntity msgConfigEntity = msgConfigMapper.selectById(msgTemplateEntity.getConfigId());

        AlibabaShortMessageSender alibabaShortMessageSender = new AlibabaShortMessageSender(msgConfigEntity.getAccessKeyId(), msgConfigEntity.getAccessKeySecret());

        MsgSendLogEntity msgSendLogEntity;
        msgSendLogEntity = new MsgSendLogEntity();
        msgSendLogEntity.setTenantId(tenantId);
        msgSendLogEntity.setTemplateId(templateId);
        msgSendLogEntity.setCreateTime(new Date());
        msgSendLogEntity.setPhone(phone);
        msgSendLogEntity.setParam(JSONObject.toJSONString(param));
        msgSendLogEntity.setCreator(creator);
        msgSendLogEntity.setTenantId(msgConfigEntity.getTenantId());
        msgSendLogEntity.setCustCode(custCode);

        try {
            alibabaShortMessageSender.send(msgTemplateEntity.getCode(), phone, msgConfigEntity.getSignName(), param);
            msgSendLogEntity.setStatus("1");
            msgSendLogEntity.setRemark("发送成功");
        } catch (Exception e) {
            msgSendLogEntity.setStatus("2");
            msgSendLogEntity.setRemark(e.getMessage());

            if (e.getMessage().contains("Specified access key is not found")) {
                msgSendLogEntity.setRemark("SpecifiedKey不存在");
            } else if (e.getMessage().contains("Specified access key is disabled")) {
                msgSendLogEntity.setRemark("SpecifiedKey被禁用");
            } else {
                msgSendLogEntity.setRemark(e.getMessage());
            }

            e.printStackTrace();
        }

        try {
            Set set = param.keySet();
            Iterator iterator = set.iterator();
            String content = msgTemplateEntity.getContent();
            while (iterator.hasNext()) {
                String next = (String) iterator.next();
                content = content.replace("${" + next + "}", String.valueOf(param.get(next)));
            }
            msgSendLogEntity.setContent(content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        msgSendLogMapper.insert(msgSendLogEntity);

    }

    @Override
    public MsgTemplateEntity getById(String id) {

        MsgTemplateEntity msgTemplateEntity = msgTemplateMapper.selectById(id);
        if (msgTemplateEntity != null && StringUtils.isNotBlank(msgTemplateEntity.getConfigId())) {
            MsgConfigEntity msgConfigEntity = msgConfigMapper.selectById(msgTemplateEntity.getConfigId());
            if (msgConfigEntity != null) {
                msgTemplateEntity.setSignName(msgConfigEntity.getSignName());
            }
        }

        return msgTemplateEntity;
    }

    @Override
    public JSONObject visitBack(JSONArray jsonArray) {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (Object o : jsonArray) {
            LinkedHashMap jsonObject = ((LinkedHashMap) o);
            String sendTime = (String) jsonObject.get("send_time");
            String sequenceId = String.valueOf(jsonObject.get("sequence_id"));
            String phoneNumber = (String) jsonObject.get("phone_number");
            String content = (String) jsonObject.get("content");
            if ("1".equals(content) || "2".equals(content) || "3".equals(content)) {
                MsgVisitBack sendSuccessNotReplyList = this.getSendSuccessNotReplyList(phoneNumber);
                if (sendSuccessNotReplyList != null) {
                    sendSuccessNotReplyList.setContent(content);
                    try {
                        sendSuccessNotReplyList.setVisitTime(format.parse(sendTime));
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    msgVisitBackMapper.updateById(sendSuccessNotReplyList);
                } else {
                    insertMsg(format, sendTime, phoneNumber, content);
                }
            } else {
                insertMsg(format, sendTime, phoneNumber, content);
            }

            log.info("序号:{}, 回复时间:{}, 电话号码:{}, 回复内容: {} ", sequenceId, sendTime, phoneNumber, content);
        }

        JSONObject result = new JSONObject();
        result.put("code", 0);
        result.put("msg", "接收成功");
        return result;
    }

    @Override
    public JSONObject sendVisitMsg(SendVisitMsgRequest sendVisitMsgRequest) {
        List<WorkOrderVisitMsgDTO> workOrderMsgList = newlyWorkOrderService.getWorkOrderMsgList(sendVisitMsgRequest);

        JSONObject result = new JSONObject();
        result.put("result", "发送成功，共发送0条数据");
        result.put("code", "0");
        if (workOrderMsgList.size() == 0) {
            return result;
        }

        new Thread(() -> {
            this.sendVisitBack(workOrderMsgList, sendVisitMsgRequest.getSendUser(), sendVisitMsgRequest.getTemplateId());
        }).start();

        result.put("result", "发送成功");
        return result;
    }

    public void sendVisitBack(List<WorkOrderVisitMsgDTO> workOrderMsgList, String sendUser, String templateId) {
        List<String> workOrderIdList = workOrderMsgList.stream().map(a -> a.getId()).collect(Collectors.toList());
        Map<String, MsgVisitBack> sendListByWorkOrderIdList = this.getSendListByWorkOrderIdList(workOrderIdList);
        Date now = new Date();
        JSONObject jsonObject;
        for (WorkOrderVisitMsgDTO workOrderMsg : workOrderMsgList) {
            jsonObject = new JSONObject();
            jsonObject.put("sendUser", sendUser);
            jsonObject.put("templateId", templateId);
            jsonObject.put("sendListByWorkOrderIdList", sendListByWorkOrderIdList);
            jsonObject.put("sendType", "1");
            this.send(jsonObject);
        }
    }

    public Map<String, MsgVisitBack> getSendListByWorkOrderIdList(List<String> workOrderIds) {
        QueryWrapper<MsgVisitBack> msgVisitBackQueryWrapper = new QueryWrapper<>();
        msgVisitBackQueryWrapper.in("work_order_id", workOrderIds);
        List<MsgVisitBack> msgVisitBacks = msgVisitBackMapper.selectList(msgVisitBackQueryWrapper);

        Map<String, MsgVisitBack> result = new HashMap();
        if (msgVisitBacks.size() == 0) {
            return result;
        }

        result = msgVisitBacks.stream().collect(Collectors.toMap(a -> a.getWorkOrderId(), a -> a));

        return result;
    }

    private MsgVisitBack getSendSuccessNotReplyList(String phoneNumber) {
        QueryWrapper<MsgVisitBack> msgVisitBackQueryWrapper = new QueryWrapper<>();
        msgVisitBackQueryWrapper.eq("phone", phoneNumber);
        msgVisitBackQueryWrapper.eq("send_status", "1");
        msgVisitBackQueryWrapper.isNull("content");
        msgVisitBackQueryWrapper.isNotNull("work_order_id");

        List<MsgVisitBack> msgVisitBacks = msgVisitBackMapper.selectList(msgVisitBackQueryWrapper);
        if (msgVisitBacks.size() > 0) {
            return msgVisitBacks.get(0);
        }

        return null;
    }

    private void insertMsg(SimpleDateFormat format, String sendTime, String phoneNumber, String content) {
        MsgVisitBack msgVisitBack;
        msgVisitBack = new MsgVisitBack();
        msgVisitBack.setPhone(phoneNumber);
        msgVisitBack.setContent(content);
        msgVisitBack.setCreateTime(new Date());
        try {
            msgVisitBack.setVisitTime(format.parse(sendTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        msgVisitBackMapper.insert(msgVisitBack);
    }
}
