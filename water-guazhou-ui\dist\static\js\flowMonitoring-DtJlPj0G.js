import{m as i}from"./index-r0dFAfgr.js";function o(t){return i({url:"/istar/api/station/data/detailList/view",method:"get",params:t})}function r(t){return i({url:"/istar/api/flowMonitoringStation/flowData",method:"get",params:t})}function a(t){return i({url:"/istar/api/station/data/detailList/view",method:"get",params:t})}function n(t){return i({url:"/istar/api/flowMonitoringStation/getMeterConfigChart",method:"get",params:t})}function s(t){return i({url:"/istar/api/flowMonitoringStation/getPeak",method:"get",params:t})}function u(t){return i({url:"/istar/api/flowMonitoringStation/getRatio",method:"get",params:t})}function g(t){return i({url:"/istar/api/flowMonitoringStation/getPeriod",method:"get",params:t})}function l(t){return i({url:"/istar/api/pressureMonitoringStation/getPeriod",method:"get",params:t})}function f(t){return i({url:"/istar/api/pressureMonitoringStation/pressureData",method:"get",params:t})}export{n as a,s as b,g as c,r as d,a as e,l as f,u as g,f as p,o as s};
