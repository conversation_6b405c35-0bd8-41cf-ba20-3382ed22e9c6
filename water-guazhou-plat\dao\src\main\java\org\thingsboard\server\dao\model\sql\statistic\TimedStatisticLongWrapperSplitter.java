package org.thingsboard.server.dao.model.sql.statistic;

import java.util.*;

public class TimedStatisticLongWrapperSplitter implements StatisticItem {
    private final Map<String, List<TimedStatisticLongWrapper>> routes = new HashMap<>();
    private final List<TimedStatisticLongWrapper> emptyDatas = new ArrayList<>();

    public Map<String, List<TimedStatisticLongWrapper>> getRoutes() {
        return routes;
    }

    public void split(List<StatisticLong> statisticLongs, Date from, Date to) {
        Set<String> keys = new HashSet<>(routes.keySet());
        for (StatisticLong statisticLong : statisticLongs) {
            // 创建与填充
            String currentKey = statisticLong.getKey();
            List<TimedStatisticLongWrapper> wrapper = routes.computeIfAbsent(currentKey, key -> new ArrayList<>(emptyDatas));

            // 分流统计
            wrapper.add(new TimedStatisticLongWrapper(statisticLong, from, to));
            keys.remove(currentKey);
        }


        // region 补充丢失的
        TimedStatisticLongWrapper emptyData = new EmptyTimedStatisticLongWrapper(from, to);
        for (String key : keys) {
            routes.get(key).add(emptyData);
        }
        // endregion
        emptyDatas.add(emptyData);
    }

    @Override
    public boolean isEmpty() {
        return routes.isEmpty();
    }
}
