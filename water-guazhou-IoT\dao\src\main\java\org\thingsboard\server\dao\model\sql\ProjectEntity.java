package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.PROJECT_TABLE)
public class ProjectEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.PROJECT_PARENT_ID)
    private String parentId;

    @Column(name = ModelConstants.PROJECT_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.PROJECT_TENANT_ID)
    private String tenantId;

    @Column(name = ModelConstants.PROJECT_NAME)
    private String name;

    @Column(name = ModelConstants.PROJECT_ADDITIONAL_INFO)
    private String additionalInfo;

}
