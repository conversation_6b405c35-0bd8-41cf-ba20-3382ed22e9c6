import{a1 as i,e as o,y as n,a as p,s as y}from"./Point-WxyopZva.js";import{R as l,V as u,e as d}from"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";let e=class extends l(u(d)){constructor(r){super(r),this.resourceInfo=null,this.type="unknown"}initialize(){this.addResolvingPromise(new Promise((r,s)=>{i(()=>{const t=this.resourceInfo&&(this.resourceInfo.layerType||this.resourceInfo.type);let a="Unknown layer type";t&&(a+=" "+t),s(new y("layer:unknown-layer-type",a,{layerType:t}))})}))}read(r,s){super.read({resourceInfo:r},s)}write(){return null}};o([n({readOnly:!0})],e.prototype,"resourceInfo",void 0),o([n({type:["show","hide"]})],e.prototype,"listMode",void 0),o([n({json:{read:!1},readOnly:!0,value:"unknown"})],e.prototype,"type",void 0),e=o([p("esri.layers.UnknownLayer")],e);const v=e;export{v as default};
