<template>
  <el-breadcrumb
    class="app-breadcrumb"
    separator="/"
  >
    <transition-group name="breadcrumb">
      <template
        v-for="(item, index) in state.levelList"
        :key="item.path"
      >
        <el-breadcrumb-item v-if="item.meta.title">
          <span
            v-if="
              item.redirect === 'noredirect' ||
                index === state.levelList.length - 1
            "
            class="no-redirect"
          >{{ item.meta.title }}</span>
          <!-- <router-link v-else :to="item.redirect||item.path">{{item.meta.title}}</router-link> -->
          <span v-else>{{ item.meta.title }}</span>
        </el-breadcrumb-item>
      </template>
    </transition-group>
  </el-breadcrumb>
</template>

<script lang="ts" setup>
import { getTenantInfo } from '@/api/tenant'
import { useAppStore, useUserStore } from '@/store'

const appStore = useAppStore()
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const state = reactive<{
  levelList: any
}>({
  levelList: null
})
const getBreadcrumb = () => {
  let matched: any[] = route.matched.filter(item => item.name)
  const first = matched[0]
  if (userStore.roles[0] !== 'SYS_ADMIN' && !appStore.sysSign) {
    getTenantInfo(userStore.tenantId).then(rel => {
      const companyName = rel.data.name
      if (first && first.name !== 'dashboard') {
        matched = [{ path: '/dashboard', meta: { title: companyName } }].concat(
          matched
        )
      }
      state.levelList = matched
    })
  } else {
    if (first && first.name !== 'dashboard') {
      matched = [{ path: '/dashboard', meta: { title: '' } }].concat(matched)
    }
    state.levelList = matched
  }
}
watch(
  () => router.currentRoute,
  () => {
    getBreadcrumb()
  }
)
onMounted(() => {
  getBreadcrumb()
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 60px;
  margin-left: 10px;
}
</style>
