<!-- 巡检任务统计 -->
<template>
  <div class="patrol-statistics-container">
    <div class="patrol-statistics-header">
      <h2>巡检任务统计</h2>
    </div>
    <div class="patrol-statistics-content">
      <div
        v-if="state.curPage === 'table'"
        class="page-wrapper"
      >
          <!-- 统计卡片区域 -->
          <div class="statistics-cards">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-card shadow="hover" class="statistics-card">
                  <div class="statistics-card-content">
                    <div class="statistics-card-value">{{ statistics.totalTasks }}</div>
                    <div class="statistics-card-label">总任务数</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="statistics-card">
                  <div class="statistics-card-content">
                    <div class="statistics-card-value">{{ statistics.completedTasks }}</div>
                    <div class="statistics-card-label">已完成任务</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="statistics-card">
                  <div class="statistics-card-content">
                    <div class="statistics-card-value">{{ statistics.presentRate.toFixed(2) }}%</div>
                    <div class="statistics-card-label">到位率</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card shadow="hover" class="statistics-card">
                  <div class="statistics-card-content">
                    <div class="statistics-card-value">{{ statistics.feedbackRate.toFixed(2) }}%</div>
                    <div class="statistics-card-label">反馈率</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <!-- 搜索区域 -->
          <Search
            ref="refSearch"
            :config="SearchConfig"
            style="margin: 16px 0"
          ></Search>

          <!-- 图表区域已移除 -->

          <!-- 表格区域 -->
          <div class="table-box">
            <el-card shadow="hover" class="table-card">
              <template #header>
                <div class="table-header">
                  <span>任务列表</span>
                  <el-button type="primary" size="small" @click="exportData">
                    <el-icon><Download /></el-icon> 导出数据
                  </el-button>
                </div>
              </template>
              <FormTable :config="TableConfig"></FormTable>
            </el-card>
          </div>
      </div>
      <div
        v-show="state.curPage === 'detail'"
        class="page-wrapper"
      >
        <div class="detail-header">
          <el-icon @click="handleBack">
            <Back />
          </el-icon>
          <div class="detail-header-divider"></div>
          <span>巡检任务详情</span>
        </div>
        <div class="detail-main overlay-y">
          <PatrolDetail
            :row="TableConfig.currentRow"
            @row-click="handleDetailRowClick"
          ></PatrolDetail>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, shallowRef } from 'vue'
import { Back, Download, InfoFilled, Refresh, Search as SearchIcon } from '@element-plus/icons-vue'
import PatrolDetail from './components/PatrolDetail.vue'
import Search from '@/components/Form/Search.vue'
import FormTable from '@/components/Form/FormTable.vue'
import {
  GetPatrolTaskList,
  GetPatrolCompleteTotal,
  GetPatrolArrivalRate,
  GetPatrolFeedbackRate
} from '@/api/patrol'
import { PatrolTaskStatusConfig } from '../config'
import { SLMessage } from '@/utils/Message'
// 图表相关导入已移除

const refSearch = ref<any>()

const state = reactive<{
  tabs: any[]
  loading: boolean
  curPage: 'table' | 'detail'
  orderTypes: any[]
}>({
  tabs: [],
  loading: false,
  curPage: 'table',
  orderTypes: []
})

// 统计数据
const statistics = reactive({
  totalTasks: 0,
  completedTasks: 0,
  presentRate: 0,
  feedbackRate: 0
  // 图表相关数据已移除
})

const SearchConfig = reactive<any>({
  scrollBarGradientColor: '#fafafa',
  filters: [
    {
      type: 'radio-button',
      options: [
        { label: '全部', value: '' },
        { label: '常规', value: 'true' },
        { label: '临时', value: 'false' }
      ],
      field: 'isNormalPlan'
    },
    {
      type: 'datetimerange',
      field: 'fromTime',
      label: '开始时间'
    },
    {
      type: 'user-select',
      labelWidth: 60,
      field: 'receiveUserId',
      label: '巡检员'
    },
    {
      type: 'user-select',
      field: 'creator',
      label: '创建人'
    },
    {
      type: 'input',
      label: '快速查找',
      field: 'keyword'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive<any>({
  dataList: [],
  columns: [
    {
      minWidth: 120,
      label: '是否常规计划',
      prop: 'isNormalPlan',
      tag: true,
      tagColor: (row: any): string => (row.isNormalPlan ? '#409eff' : '#0cbb4a'),
      formatter: (row: any) => (row.isNormalPlan ? '常规' : '临时')
    },
    { minWidth: 120, label: '任务编号', prop: 'code' },
    { minWidth: 120, label: '任务名称', prop: 'name' },
    { minWidth: 120, label: '巡检周期', prop: 'planCircleName' },
    { minWidth: 160, label: '开始时间', prop: 'beginTime' },
    { minWidth: 160, label: '结束时间', prop: 'endTime' },
    { minWidth: 120, label: '巡检员', prop: 'receiveUserName' },
    { minWidth: 120, label: '到位状况', prop: 'presentState' },
    { minWidth: 120, label: '反馈状况', prop: 'fallbackState' },
    {
      minWidth: 100,
      align: 'center',
      label: '任务状态',
      prop: 'status',
      tag: true,
      tagColor: (row: any): string => PatrolTaskStatusConfig[row.status]?.color,
      formatter: (row: any) => {
        return PatrolTaskStatusConfig[row.status]?.text || row.status
      }
    }
  ],
  operationWidth: 100,
  operations: [
    {
      perm: true,
      text: '详情',
      svgIcon: shallowRef(InfoFilled),
      type: 'info',
      click: (row: any) => {
        state.curPage = 'detail'
        TableConfig.currentRow = row
        refreshDetail()
      }
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  },
  handleSelectChange: (rows: any) => {
    TableConfig.selectList = rows
  }
})

const handleBack = () => {
  state.curPage = 'table'
}

const refreshDetail = () => {
  // 详情页面刷新逻辑
}

// 图表相关函数已移除



// 刷新数据
const refreshData = () => {
  const query = refSearch.value?.queryParams || {}

  // 获取任务列表（用于表格显示）
  GetPatrolTaskList({
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    ...query,
    fromTime: query.fromTime && query.fromTime[0],
    toTime: query.fromTime && query.fromTime[1],
    receiveUserId: query.receiveUserId?.join(','),
    creator: query.creator?.join(',')
  }).then(res => {
    if (res.data?.code === 200) {
      TableConfig.dataList = res.data?.data?.data || []
      const total = res.data?.data?.total || 0
      TableConfig.pagination.total = total

      // 更新总任务数
      statistics.totalTasks = total

      // 不再需要调用 GetPatrolCompleteCount 接口获取图表数据

      // 使用新的API获取已完成任务总数
      GetPatrolCompleteTotal().then(totalRes => {
        if (totalRes.data?.code === 200) {
          statistics.completedTasks = totalRes.data?.data || 0
        }
      }).catch(error => {
        console.error('获取已完成任务总数失败:', error)
        SLMessage.error('获取已完成任务总数失败')
      })

      // 使用新的API获取到位率
      GetPatrolArrivalRate().then(arrivalRes => {
        if (arrivalRes.data?.code === 200) {
          // 后端返回的是字符串形式的百分比，如 "85.5%"，需要转换为数字
          const rateStr = arrivalRes.data?.data || "0%"
          statistics.presentRate = parseFloat(rateStr.replace('%', ''))
        }
      }).catch(error => {
        console.error('获取到位率失败:', error)
        SLMessage.error('获取到位率失败')
      })

      // 使用新的API获取反馈率
      GetPatrolFeedbackRate().then(feedbackRes => {
        if (feedbackRes.data?.code === 200) {
          // 后端返回的是字符串形式的百分比，如 "85.5%"，需要转换为数字
          const rateStr = feedbackRes.data?.data || "0%"
          statistics.feedbackRate = parseFloat(rateStr.replace('%', ''))
        }
      }).catch(error => {
        console.error('获取反馈率失败:', error)
        SLMessage.error('获取反馈率失败')
      })
    } else {
      SLMessage.error(res.data?.message || '获取任务列表失败')
    }
  }).catch(error => {
    console.error('获取任务列表失败:', error)
    SLMessage.error('系统错误')
  })
}

// 导出数据
const exportData = () => {
  SLMessage.info('导出功能开发中...')
}

const handleDetailRowClick = () => {
  // 处理详情行点击事件
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.patrol-statistics-container {
  height: 100%;
  width: 100%;
  padding: 20px;
  background-color: #f5f7fa;
}

.patrol-statistics-header {
  margin-bottom: 20px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
}

.patrol-statistics-content {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.page-wrapper {
  height: 100%;
}

.statistics-cards {
  margin-bottom: 16px;

  .statistics-card {
    height: 120px;

    .statistics-card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      .statistics-card-value {
        font-size: 28px;
        font-weight: bold;
        color: #409eff;
      }

      .statistics-card-label {
        margin-top: 8px;
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.statistics-charts {
  margin-bottom: 16px;

  .chart-card {
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart-container {
      height: 300px;
    }
  }
}

.table-box {
  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .el-icon {
    cursor: pointer;
  }
  .detail-header-divider {
    width: 1px;
    height: 1em;
    border: none;
    background-color: var(--el-border-color);
    margin: 0 20px;
  }
}

.detail-main {
  padding-right: 8px;
  height: calc(100% - 36px);
}
</style>
