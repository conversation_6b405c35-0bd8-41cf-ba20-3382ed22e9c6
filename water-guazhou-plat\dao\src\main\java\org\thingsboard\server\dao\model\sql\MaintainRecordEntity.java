/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.*;
import org.thingsboard.server.common.data.maintain.Maintain;
import org.thingsboard.server.common.data.maintain.MaintainRecord;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MAINTAIN_RECORD_NAME)
public class MaintainRecordEntity extends BaseSqlEntity<MaintainRecord> {

    @Column(name = ModelConstants.MAINTAIN_JSON_NAME)
    private String name;

    @Type(type = "json")
    @Column(name = ModelConstants.ASSET_ADDITIONAL_INFO_PROPERTY)
    private JsonNode additionalInfo;


    @Column(name = ModelConstants.REPAIR_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.REPAIR_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.REPAIR_TENANT_ID)
    private String tenantId;

    @Column(name = ModelConstants.MAINTAIN_RECORD_START)
    private Long startTime;

    @Column(name = ModelConstants.MAINTAIN_RECORD_END)
    private Long endTime;

    @Column(name = ModelConstants.MAINTAIN_ID)
    private String maintainId;

    @Column(name = ModelConstants.MAINTAIN_USER)
    private String maintainUser;

    @Column(name = ModelConstants.REPAIR_STATUS)
    private String status;

    @Column(name = ModelConstants.PROJECT_RELATION_PROJECT_ID)
    private String projectId;

    public MaintainRecordEntity() {
    }

    public MaintainRecordEntity(MaintainRecord maintainRecord) {
        if (maintainRecord.getId() != null) {
            this.id = toString(maintainRecord.getId().getId());
        }
        if (maintainRecord.getDeviceId() != null) {
            this.deviceId = toString(maintainRecord.getDeviceId().getId());
        }
        if (maintainRecord.getTenantId() != null) {
            this.tenantId = toString(maintainRecord.getTenantId().getId());
        }
        if (maintainRecord.getMaintainUser() != null) {
            this.maintainUser = toString(maintainRecord.getMaintainUser().getId());
        }
        if (maintainRecord.getMaintainId() != null) {
            this.maintainId = toString(maintainRecord.getMaintainId().getId());
        }
        this.createTime = maintainRecord.getCreateTime();
        this.name = maintainRecord.getName();
        this.additionalInfo = maintainRecord.getAdditionalInfo();
        this.status = maintainRecord.getStatus();
        this.startTime = maintainRecord.getStartTime();
        this.endTime = maintainRecord.getEndTime();
        this.projectId=maintainRecord.getProjectId();

    }

    @Override
    public MaintainRecord toData() {
        MaintainRecord maintain = new MaintainRecord(new MaintainRecordId(getId()));
        maintain.setDeviceId(new DeviceId(toUUID(deviceId)));
        maintain.setCreateTime(createTime);
        maintain.setTenantId(new TenantId(toUUID(tenantId)));
        maintain.setName(name);
        maintain.setAdditionalInfo(additionalInfo);
        maintain.setMaintainId(new MaintainId(toUUID(maintainId)));
        maintain.setStartTime(startTime);
        maintain.setEndTime(endTime);
        maintain.setStatus(status);
        maintain.setProjectId(projectId);
        if (maintainUser != null)
            maintain.setMaintainUser(new UserId(toUUID(maintainUser)));
        return maintain;
    }
}
