<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.alarmV2.AlarmRuleUserMapper">


    <insert id="batchInsert">
        INSERT INTO tb_alarm_rule_user (id, main_id, type, user_id, create_time)
        VALUES
        <foreach collection="param" item="item" separator=",">
            (#{item.id}, #{item.mainId}, #{item.type}, #{item.userId}, #{item.createTime})
        </foreach>
    </insert>
</mapper>