package org.thingsboard.server.dao.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitConfig;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitConfigPageRequest;

import java.util.List;

@Mapper
public interface CircuitConfigMapper extends BaseMapper<CircuitConfig> {
    IPage<CircuitConfig> findByPage(CircuitConfigPageRequest request);

    boolean update(CircuitConfig entity);

    List<String> getTypes(String tenantId);

    String getItemTypes(String multiId);
}
