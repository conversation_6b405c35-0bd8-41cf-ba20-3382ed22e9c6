package org.thingsboard.server.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.thingsboard.server.config.xss.XssFilter;

import javax.servlet.Filter;

@Configuration
public class XSSFilterConfig {

/*    @Bean
    public FilterRegistrationBean filterRegistrationBean() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(xssFilter());
        registration.addUrlPatterns("/*");
        registration.addInitParameter("paramName", "paramValue");
        registration.setName("xssFilter");
        return registration;
    }

    *//**
     * 创建一个bean
     * @return
     *//*
    @Bean(name = "xssFilter")
    public Filter xssFilter() {
        return new XssFilter();
    }*/
}