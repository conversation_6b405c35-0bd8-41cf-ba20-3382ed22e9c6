import{d as j,c as M,r as H,o as J,bv as Q,x as m,y as K,C as Y,g as V,n as L,q as l,F as u,G as p,p as f,bo as G,h as N,aB as X,aJ as Z,aK as _,aL as x,I as ee,H as ae,c2 as le,J as re,K as oe,N as te,O as ue,P as ne,e4 as se,L as ie,bK as de,br as me}from"./index-r0dFAfgr.js";import{x as b}from"./xlsx-rVJkW9yq.js";import{g as pe,a as fe,d as ge,s as ye,e as Fe,i as ce}from"./pressure-DUrGd62l.js";const be=j({name:"WaterPlantPressure",setup(){const a=M(null),e=M(null),r=H({queryParams:{page:1,size:10,waterPlantType:"",waterPlantName:"",startTime:"",endTime:"",dataSource:"",deviceSerial:"",fromTime:"",toTime:"",tenantId:""},dateRange:[],loading:!1,pressureList:[],total:0,dialogTitle:"",dialogVisible:!1,form:{id:"",waterPlantId:"",waterPlantName:"",model:"",pressure:null,recordTime:"",remark:"",deviceSerial:"",dataSource:"手动填报"},rules:{waterPlantId:[{required:!0,message:"请选择水厂",trigger:"change"}],pressure:[{required:!0,message:"请输入出厂水压",trigger:"blur"}],recordTime:[{required:!0,message:"请选择记录时间",trigger:"change"}]},waterPlantOptions:[],importDialogVisible:!1,fileList:[],importData:[]}),g=async()=>{r.loading=!0;try{const o=await pe(r.queryParams);r.pressureList=o.data.records||[],r.total=o.data.total||0}catch(o){console.error("获取水厂压力列表失败",o),m.error("获取水厂压力列表失败")}finally{r.loading=!1}},S=async()=>{try{const o=await fe({});r.waterPlantOptions=o.data.records||[]}catch(o){console.error("获取水厂列表失败",o),m.error("获取水厂列表失败")}},k=()=>{r.queryParams.page=1,g()},y=()=>{var o;(o=a.value)==null||o.resetFields(),r.dateRange=[],r.queryParams.startTime="",r.queryParams.endTime="",r.queryParams.fromTime="",r.queryParams.toTime="",r.queryParams.deviceSerial="",k()},P=o=>{o?(r.queryParams.startTime=o[0],r.queryParams.endTime=o[1],r.queryParams.fromTime=o[0],r.queryParams.toTime=o[1]):(r.queryParams.startTime="",r.queryParams.endTime="",r.queryParams.fromTime="",r.queryParams.toTime="")},i=o=>{r.queryParams.size=o,g()},c=o=>{r.queryParams.page=o,g()},E=()=>{r.dialogTitle="新增水厂压力信息",r.form={id:"",waterPlantId:"",waterPlantName:"",model:"",pressure:null,recordTime:new Date().toISOString().slice(0,19).replace("T"," "),remark:"",deviceSerial:"",dataSource:"手动填报"},r.dialogVisible=!0},d=o=>{r.dialogTitle="编辑水厂压力信息",r.form=JSON.parse(JSON.stringify(o)),r.dialogVisible=!0},B=o=>{K.confirm("确认删除该水厂压力信息吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await ge(o.id),m.success("删除成功"),g()}catch(s){console.error("删除失败",s),m.error("删除失败")}}).catch(()=>{})},F=o=>{const s=r.waterPlantOptions.find(n=>n.id===o);s&&(r.form.waterPlantName=s.name)},v=()=>{var o;(o=e.value)==null||o.validate(async s=>{if(s)try{await ye(r.form),m.success("保存成功"),r.dialogVisible=!1,g()}catch(n){console.error("保存失败",n),m.error("保存失败")}})},T=()=>{r.importDialogVisible=!0,r.fileList=[],r.importData=[]},q=async()=>{try{const o=await Fe(r.queryParams),s=new Blob([o.data],{type:"application/vnd.ms-excel"}),n=document.createElement("a");n.href=URL.createObjectURL(s),n.download="水厂压力信息.xlsx",n.click(),URL.revokeObjectURL(n.href)}catch(o){console.error("导出失败",o),m.error("导出失败")}},w=o=>{const s=o.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||o.type==="application/vnd.ms-excel",n=o.size/1024/1024<10;return s?n?!0:(m.error("上传文件大小不能超过10MB!"),!1):(m.error("上传文件只能是Excel格式!"),!1)},A=o=>{const s=o.file,n=new FileReader;n.onload=z=>{const R=z.target.result,U=b.read(R,{type:"array"}),O=U.SheetNames[0],W=U.Sheets[O],$=b.utils.sheet_to_json(W);r.importData=$.map(C=>({waterPlantName:C.水厂名称,model:C.型号,pressure:C["出厂水压(MPa)"],recordTime:C.记录时间,remark:C.备注}))},n.readAsArrayBuffer(s)},D=()=>{m.warning("最多只能上传1个文件")},t=()=>{r.importData=[]},h=()=>{const o=[{水厂名称:"示例水厂",型号:"YYY-MM-dd","出厂水压(MPa)":1.02,记录时间:"2023-01-01 08:00:00",备注:"示例备注"}],s=b.utils.json_to_sheet(o),n=b.utils.book_new();b.utils.book_append_sheet(n,s,"水厂压力信息"),b.writeFile(n,"水厂压力信息导入模板.xlsx")},I=async()=>{if(r.importData.length===0){m.warning("请先上传文件");return}try{await ce(r.importData),m.success("导入成功"),r.importDialogVisible=!1,g()}catch(o){console.error("导入失败",o),m.error("导入失败")}};return J(()=>{g(),S()}),{queryForm:a,form:e,...Q(r),handleQuery:k,resetQuery:y,handleDateRangeChange:P,handleSizeChange:i,handleCurrentChange:c,handleAdd:E,handleEdit:d,handleDelete:B,handleWaterPlantChange:F,submitForm:v,handleImport:T,handleExport:q,beforeUpload:w,handleUpload:A,handleExceed:D,handleRemove:t,downloadTemplate:h,confirmImport:I}}}),Pe={class:"app-container"},Ce={class:"table-toolbar"},Ee={class:"pagination-container"},Be={class:"dialog-footer"},we={class:"import-template"},Ve={class:"dialog-footer"};function ke(a,e,r,g,S,k){const y=_,P=x,i=ee,c=ae,E=le,d=re,B=oe,F=te,v=ue,T=ne,q=se,w=ie,A=de,D=me;return V(),L("div",Pe,[l(B,{model:a.queryParams,ref:"queryForm",inline:!0,class:"search-form"},{default:u(()=>[l(i,{label:"水厂类别："},{default:u(()=>[l(P,{modelValue:a.queryParams.waterPlantType,"onUpdate:modelValue":e[0]||(e[0]=t=>a.queryParams.waterPlantType=t),placeholder:"请选择",clearable:""},{default:u(()=>[l(y,{label:"全部",value:""}),l(y,{label:"自动填报",value:"自动填报"}),l(y,{label:"手动填报",value:"手动填报"})]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"名称筛选："},{default:u(()=>[l(c,{modelValue:a.queryParams.waterPlantName,"onUpdate:modelValue":e[1]||(e[1]=t=>a.queryParams.waterPlantName=t),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),l(i,{label:"日期范围："},{default:u(()=>[l(E,{modelValue:a.dateRange,"onUpdate:modelValue":e[2]||(e[2]=t=>a.dateRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",onChange:a.handleDateRangeChange},null,8,["modelValue","onChange"])]),_:1}),l(i,{label:"数据来源："},{default:u(()=>[l(P,{modelValue:a.queryParams.dataSource,"onUpdate:modelValue":e[3]||(e[3]=t=>a.queryParams.dataSource=t),placeholder:"请选择",clearable:""},{default:u(()=>[l(y,{label:"自动采集",value:"自动采集"}),l(y,{label:"手动填报",value:"手动填报"})]),_:1},8,["modelValue"])]),_:1}),l(i,null,{default:u(()=>[l(d,{type:"primary",onClick:a.handleQuery},{default:u(()=>e[15]||(e[15]=[p("查询")])),_:1},8,["onClick"]),l(d,{onClick:a.resetQuery},{default:u(()=>e[16]||(e[16]=[p("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),f("div",Ce,[l(d,{type:"primary",onClick:a.handleAdd},{default:u(()=>e[17]||(e[17]=[p("新增")])),_:1},8,["onClick"]),l(d,{type:"success",onClick:a.handleImport},{default:u(()=>e[18]||(e[18]=[p("导入")])),_:1},8,["onClick"]),l(d,{type:"warning",onClick:a.handleExport},{default:u(()=>e[19]||(e[19]=[p("导出")])),_:1},8,["onClick"])]),G((V(),N(v,{data:a.pressureList,border:""},{default:u(()=>[l(F,{label:"水厂名称",prop:"waterPlantName",align:"center"}),l(F,{label:"型号",prop:"model",align:"center"}),l(F,{label:"出厂水压/MPa",prop:"pressure",align:"center"}),l(F,{label:"数据来源",prop:"dataSource",align:"center"}),l(F,{label:"操作",align:"center",width:"200"},{default:u(t=>[l(d,{type:"primary",size:"mini",onClick:h=>a.handleEdit(t.row)},{default:u(()=>e[20]||(e[20]=[p("编辑")])),_:2},1032,["onClick"]),l(d,{type:"danger",size:"mini",onClick:h=>a.handleDelete(t.row)},{default:u(()=>e[21]||(e[21]=[p("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[D,a.loading]]),f("div",Ee,[l(T,{onSizeChange:a.handleSizeChange,onCurrentChange:a.handleCurrentChange,"current-page":a.queryParams.page,"page-sizes":[10,20,50,100],"page-size":a.queryParams.size,layout:"total, sizes, prev, pager, next, jumper",total:a.total},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"])]),l(w,{title:a.dialogTitle,modelValue:a.dialogVisible,"onUpdate:modelValue":e[11]||(e[11]=t=>a.dialogVisible=t),width:"500px","append-to-body":""},{footer:u(()=>[f("div",Be,[l(d,{onClick:e[10]||(e[10]=t=>a.dialogVisible=!1)},{default:u(()=>e[23]||(e[23]=[p("取 消")])),_:1}),l(d,{type:"primary",onClick:a.submitForm},{default:u(()=>e[24]||(e[24]=[p("确 定")])),_:1},8,["onClick"])])]),default:u(()=>[l(B,{ref:"form",model:a.form,rules:a.rules,"label-width":"100px"},{default:u(()=>[l(i,{label:"水厂名称",prop:"waterPlantName"},{default:u(()=>[l(P,{modelValue:a.form.waterPlantId,"onUpdate:modelValue":e[4]||(e[4]=t=>a.form.waterPlantId=t),placeholder:"请选择水厂",filterable:"",onChange:a.handleWaterPlantChange},{default:u(()=>[(V(!0),L(X,null,Z(a.waterPlantOptions,t=>(V(),N(y,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),l(i,{label:"型号",prop:"model"},{default:u(()=>[l(c,{modelValue:a.form.model,"onUpdate:modelValue":e[5]||(e[5]=t=>a.form.model=t),placeholder:"请输入型号"},null,8,["modelValue"])]),_:1}),l(i,{label:"设备编号",prop:"deviceSerial"},{default:u(()=>[l(c,{modelValue:a.form.deviceSerial,"onUpdate:modelValue":e[6]||(e[6]=t=>a.form.deviceSerial=t),placeholder:"请输入设备编号"},null,8,["modelValue"])]),_:1}),l(i,{label:"出厂水压",prop:"pressure"},{default:u(()=>[l(q,{modelValue:a.form.pressure,"onUpdate:modelValue":e[7]||(e[7]=t=>a.form.pressure=t),precision:2,step:.1,min:0,placeholder:"请输入出厂水压(MPa)"},null,8,["modelValue"]),e[22]||(e[22]=f("span",{class:"unit"},"MPa",-1))]),_:1}),l(i,{label:"记录时间",prop:"recordTime"},{default:u(()=>[l(E,{modelValue:a.form.recordTime,"onUpdate:modelValue":e[8]||(e[8]=t=>a.form.recordTime=t),type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(i,{label:"备注",prop:"remark"},{default:u(()=>[l(c,{modelValue:a.form.remark,"onUpdate:modelValue":e[9]||(e[9]=t=>a.form.remark=t),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(w,{title:"导入水厂压力信息",modelValue:a.importDialogVisible,"onUpdate:modelValue":e[14]||(e[14]=t=>a.importDialogVisible=t),width:"500px","append-to-body":""},{footer:u(()=>[f("div",Ve,[l(d,{onClick:e[13]||(e[13]=t=>a.importDialogVisible=!1)},{default:u(()=>e[28]||(e[28]=[p("取 消")])),_:1}),l(d,{type:"primary",onClick:a.confirmImport},{default:u(()=>e[29]||(e[29]=[p("确 定")])),_:1},8,["onClick"])])]),default:u(()=>[l(A,{class:"upload-demo",drag:"",action:"#","http-request":a.handleUpload,"before-upload":a.beforeUpload,"file-list":a.fileList,limit:1,"on-exceed":a.handleExceed,"on-remove":a.handleRemove,accept:".xlsx, .xls"},{tip:u(()=>e[25]||(e[25]=[f("div",{class:"el-upload__tip"},"只能上传xlsx/xls文件，且不超过10MB",-1)])),default:u(()=>[e[26]||(e[26]=f("i",{class:"el-icon-upload"},null,-1)),e[27]||(e[27]=f("div",{class:"el-upload__text"},[p("将文件拖到此处，或"),f("em",null,"点击上传")],-1))]),_:1},8,["http-request","before-upload","file-list","on-exceed","on-remove"]),f("div",we,[f("a",{onClick:e[12]||(e[12]=(...t)=>a.downloadTemplate&&a.downloadTemplate(...t))},"下载导入模板")])]),_:1},8,["modelValue"])])}const Ae=Y(be,[["render",ke],["__scopeId","data-v-7668257b"]]);export{Ae as default};
