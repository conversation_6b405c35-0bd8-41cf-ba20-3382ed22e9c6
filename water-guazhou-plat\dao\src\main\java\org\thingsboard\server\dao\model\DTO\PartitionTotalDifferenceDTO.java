package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 总分差表评估
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-15
 */
@Data
public class PartitionTotalDifferenceDTO {

    private String partitionId;

    private String partitionName;

    private Integer needCopyNum = 0;

    private Integer realCopyNum = 0;

    private Integer notCopyNum = 0;

    private BigDecimal copyRate = BigDecimal.ZERO;

    private BigDecimal supplyTotal = BigDecimal.ZERO;

    private BigDecimal realCopyWater = BigDecimal.ZERO;

    private BigDecimal nrwWater = BigDecimal.ZERO;

    private BigDecimal nrwRate = BigDecimal.ZERO;

    private BigDecimal correctSupplyTotal = BigDecimal.ZERO;

    private BigDecimal correctCopyWater = BigDecimal.ZERO;

    private BigDecimal correctNrwWater = BigDecimal.ZERO;

    private BigDecimal correctNrwRate = BigDecimal.ZERO;

    private BigDecimal referenceLossWater = BigDecimal.ZERO;

    private BigDecimal faceLossWater = BigDecimal.ZERO;

}
