package org.thingsboard.server.controller.maintainCircuit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintainCircuit.MaintainCircuitTeamMService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.MaintainCircuitTeamM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-07
 */
@RestController
@RequestMapping("api/maintainCircuit/team/m")
public class MaintainCircuitTeamMController extends BaseController {
    @Autowired
    private MaintainCircuitTeamMService maintainCircuitTeamMService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam(required = false, defaultValue = "") String type,
                                 int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(maintainCircuitTeamMService.getList(name, type, page, size, tenantId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody MaintainCircuitTeamM maintainCircuitTeamM) throws ThingsboardException {
        maintainCircuitTeamM.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        maintainCircuitTeamM.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(maintainCircuitTeamMService.save(maintainCircuitTeamM));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return maintainCircuitTeamMService.delete(ids);
    }
}
