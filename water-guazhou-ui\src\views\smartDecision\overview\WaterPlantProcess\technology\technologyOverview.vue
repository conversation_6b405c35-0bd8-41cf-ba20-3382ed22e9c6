<template>
  <div class="main">
    <div style="height: 85%; width: 100%">
      <gyzl_overview v-if="activeName === 'gyzl'"></gyzl_overview>
      <ysc_overview1 v-if="activeName === 'ysc1'"></ysc_overview1>
      <ysc_overview2 v-if="activeName === 'ysc2'"></ysc_overview2>
      <cdc_overview v-if="activeName === 'cdc'"></cdc_overview>
      <psj_overview v-if="activeName === 'psj'"> </psj_overview>
      <jyj_overview v-if="activeName === 'jyj'"> </jyj_overview>
      <xdj_overview v-if="activeName === 'xdj'"> </xdj_overview>
      <wflc_overview v-if="activeName === 'wflc'"> </wflc_overview>
      <ystsb_overview v-if="activeName === 'ystsb'"> </ystsb_overview>
      <qsc_overview1 v-if="activeName === 'qsc1'"> </qsc_overview1>
      <qsc_overview2 v-if="activeName === 'qsc2'"> </qsc_overview2>
    </div>
    <div class="app">
      <div class="swiper-button" @click="prevSlide">
          <el-icon color="#fff"><ArrowLeftBold /></el-icon>
      </div>
      <!-- 左 -->
      <div
        style="
          width: 80%;
          overflow-x: auto;
          display: flex;
          justify-content: space-around;
        "
        ref="slider"
      >
        <div v-for="(item, index) in buttonList" :key="index" :gutter="10">
          <div class="box" @click="activeName = item.key">
            <div class="img-c" style="">
              <img
                :src="item.img"
                class="sidebar-logo"
                style="width: 70px; height: 70px"
              />
            </div>
            <div
              class="app-name"
              :style="{ color: activeName === item.key ? '#00f8ff' : '#fff' }"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="swiper-button" @click="nextSlide">
        <el-icon color="#fff"><ArrowRightBold /></el-icon>
      </div>
      <!-- 右 -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ArrowRightBold, ArrowLeftBold } from '@element-plus/icons-vue';
import gyzl_overview from './components/gyzl_overview.vue';
import ysc_overview2 from './components/ysc_overview2.vue';
import ysc_overview1 from './components/ysc_overview1.vue';
import cdc_overview from './components/cdc_overview.vue';
import psj_overview from './components/psj_overview.vue';
import jyj_overview from './components/jyj_overview.vue';
import xdj_overview from './components/xdj_overview.vue';
import wflc_overview from './components/wflc_overview.vue';
import ystsb_overview from './components/ystsb_overview.vue';
import qsc_overview1 from './components/qsc_overview1.vue';
import qsc_overview2 from './components/qsc_overview2.vue';
import gyzl_img from '../../../imgs/tongchuan/总工艺图.png';
import ysc_img1 from '../../../imgs/tongchuan/原水池1.png';
import ysc_img2 from '../../../imgs/tongchuan/原水池2.png';
import cdc_img from '../../../imgs/tongchuan/沉淀池.png';
import jyj_img from '../../../imgs/tongchuan/加药间.png';
import psj_img from '../../../imgs/tongchuan/配水井.png';
import wflc_img from '../../../imgs/tongchuan/无阀滤池.png';
import ystsb_img from '../../../imgs/tongchuan/原水提升泵.png';
import qsc_img1 from '../../../imgs/tongchuan/清水池2.png';
import qsc_img2 from '../../../imgs/tongchuan/清水池1.png';
import xdj_img from '../../../imgs/tongchuan/消毒间.png';
const activeName = ref<string>('gyzl');
const slider = ref<any>();
const slideWidth = ref<any>(960);
const buttonList = ref<any>([
  {
    key: 'gyzl',
    name: '工艺总览',
    img: gyzl_img
  },
  {
    key: 'ysc1',
    name: '原水池1',
    img: ysc_img1
  },
  {
    key: 'ysc2',
    name: '原水池2',
    img: ysc_img2
  },
  {
    key: 'ystsb',
    name: '原水提升泵',
    img: ystsb_img
  },
  {
    key: 'jyj',
    name: '加药间',
    img: jyj_img
  },
  {
    key: 'psj',
    name: '配水井',
    img: psj_img
  },
  {
    key: 'cdc',
    name: '沉淀池',
    img: cdc_img
  },
  {
    key: 'wflc',
    name: '无阀滤池',
    img: wflc_img
  },
  {
    key: 'xdj',
    name: '消毒间',
    img: xdj_img
  },
  {
    key: 'qsc1',
    name: '清水池1',
    img: qsc_img1
  },
  {
    key: 'qsc2',
    name: '清水池2',
    img: qsc_img2
  }
]);
const prevSlide = () => {
  slider.value.scrollBy({ left: -slideWidth.value, behavior: 'smooth' });
};
const nextSlide = () => {
  slider.value.scrollBy({ left: slideWidth.value, behavior: 'smooth' });
};
</script>
<style lang="scss" scoped>

.main {
  width: 100%;
  height: 100%;
  position: relative;
  background: rgb(3, 17, 45);
}

.content {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 20px;
  padding-top: 81px;
  color: rgba(184, 210, 255, 1);
}

.card {
  position: absolute;
}

.zutai-card {
  width: 100%;
  height: 100%;
  border: 1px solid rgba(0, 191, 252, 0.4);
  background: url(../../../imgs/tongchuan/总工艺图.png) 0 0 /100% 100% no-repeat;
}

.cqjj-card {
  width: 745px;
  height: 315px;
  top: 746px;
  left: 26px;
}

.szjc-card {
  width: 994px;
  height: 315px;
  top: 746px;
  left: 783px;
}

.ssqx-card {
  width: 738px;
  height: 315px;
  top: 105px;
  left: 1790px;
}

.nhjc-card {
  width: 738px;
  height: 315px;
  top: 436px;
  left: 1790px;
}

.rgsl-card {
  width: 738px;
  height: 294px;
  top: 767px;
  left: 1790px;
}

.card-content {
  width: 223px;
  border-radius: 4px;
  padding: 0 10px 20px 10px;
  box-shadow: 0px 0px 8px 0px #00d9ff inset;
  background: linear-gradient(
    rgba(30, 105, 158, 0.1) 0%,
    rgba(30, 105, 158, 0.6) 100%
  );

  .card-title {
    // transform: translateX(-50%);
    font-size: 14px;
    line-height: 18px;
    width: 93px;
    height: 18px;
    margin: 0 auto;
    text-align: center;
    border-radius: 0 0 10px 10px;
    background: linear-gradient(
      180deg,
      rgba(116, 229, 255, 0.2) 0%,
      rgba(16, 229, 255, 0.6) 100%
    );
  }

  .row {
    display: flex;
    font-size: 14px;
    color: #d8feff;
    padding: 10px 10px 0px 10px;

    .value {
      padding-left: 4px;
    }
  }
}
.left-box {
  position: absolute;
  top: 0%;
  height: 100%;
  left: 1%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  text-align: right;
  padding: 16% 0;
  text-align: center;
  .box {
    width: 120px;
    display: flex;
    align-items: center;
    border: 2px solid hsl(202, 57%, 56%);
    background: #072546e5;
    color: #8edbf7;
    padding: 0 10px;
    height: 40px;
    gap: 0px;
    font-size: 14px;
    line-height: 40px;
    border-radius: 3px;
    transform: skewX(-20deg);
  }
  .button {
    text-align: left;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .box > div {
    transform: skewX(20deg);
  }
  .check-box {
    color: #fff;
    background: #42cbeae5;
    border: 2px solid #6cddf6;
  }
}

.app {
  width: 50%;
  margin: 10px auto;
  display: flex;
  align-items: center;
  position: relative;
  justify-content: space-between;
  bottom: 30px;
  .box {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    padding: 20%;
    text-align: center;
    margin-bottom: 10px;
    width: 150px;
    .img-c {
      border-radius: 50%;
      width: 70px;
      height: 70px;
    }
    .app-name {
      color: #fff;
      font-weight: 600;
    }
  }
}

.swiper-button {
  background: #9b9393;
  padding: 0 4px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: 100px;
  line-height: 100px;
  text-align: center;
  cursor: pointer;
}
</style>
