import{d as m,a8 as p,ab as _,g as l,n as o,aB as d,aJ as v,aw as b,q as h,p as n,bh as c,i as f,cU as D,C as x}from"./index-r0dFAfgr.js";import{i as w,a as B,b as k,c as C,d as L,e as N}from"./6-4nR55Xef.js";const S={class:"wheeling-items"},y={class:"info"},E={class:"text"},F={class:"value"},I=m({__name:"DeviceStatic_wudang",props:{pipeData:{}},setup(r){const t=r,u=p(()=>{var i,a,e;const s=_(((i=t.pipeData)==null?void 0:i.pipeLength)??0);return{data:[{label:"供水管长",value:s.value.toFixed(2)+s.unit,unit:"米",color:"lightblue",img:w},{label:"阀门",value:((a=t.pipeData)==null?void 0:a.valve)||0,unit:"个",color:"lightblue",img:B},{label:"水表",value:((e=t.pipeData)==null?void 0:e.meter)||0,unit:"个",color:"orange",img:k},{label:"排气阀",value:13,unit:"个",color:"orange",img:C},{label:"泵房",value:2,unit:"个",color:"seablue",img:L},{label:"三通",value:12,unit:"个",color:"seablue",img:N}]}});return(s,i)=>{const a=D;return l(),o("div",S,[(l(!0),o(d,null,v(f(u).data,(e,g)=>(l(),o("div",{key:g,class:b(["wheeling-item",e.color])},[h(a,{class:"img",fit:"contain",src:e.img},null,8,["src"]),n("div",y,[n("div",E,c(e.label),1),n("div",F,c(e.value)+" "+c(e.unit),1)])],2))),128))])}}}),q=x(I,[["__scopeId","data-v-6ce647c7"]]);export{q as default};
