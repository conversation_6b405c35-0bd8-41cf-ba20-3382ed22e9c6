package org.thingsboard.server.dao.util;

import lombok.extern.slf4j.Slf4j;
import org.thingsboard.server.dao.model.DTO.TreeNodeDTO;
import org.thingsboard.server.dao.model.DTO.TreeNodeNRWDTO;

import java.util.List;
import java.util.stream.Collectors;
 
@Slf4j
public class TreeUtil {
 
    /**
     * @param list         全部节点列表
     * @param rootParentId 根节点ID
     * @return 树结构数据
     */
    public static  List<TreeNodeDTO> listToTree(List<TreeNodeDTO> list, String rootParentId) {
        return list.stream()
                .filter(treeNodeDTO -> treeNodeDTO.getParentId() != null)
                .filter(treeNodeDTO -> treeNodeDTO.getParentId().equals(rootParentId))
                .peek(treeNodeDTO -> treeNodeDTO.setChildren(listToTree(list, treeNodeDTO.getId())))
                .collect(Collectors.toList());
    }

    /**
     * @param list         全部节点列表
     * @param rootParentId 根节点ID
     * @return 树结构数据
     */
    public static  List<TreeNodeNRWDTO> listToTreeNRW(List<TreeNodeNRWDTO> list, String rootParentId) {
        return list.stream()
                .filter(treeNodeDTO -> treeNodeDTO.getParentId() != null)
                .filter(treeNodeDTO -> treeNodeDTO.getParentId().equals(rootParentId))
                .peek(treeNodeDTO -> treeNodeDTO.setChildren(listToTreeNRW(list, treeNodeDTO.getId())))
                .collect(Collectors.toList());
    }
}
 