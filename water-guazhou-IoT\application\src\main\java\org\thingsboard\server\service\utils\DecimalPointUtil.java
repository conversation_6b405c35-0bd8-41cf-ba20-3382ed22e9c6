/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

import static java.lang.Double.POSITIVE_INFINITY;
import static java.lang.Float.NEGATIVE_INFINITY;

/**
 * double类型控制小数点位数
 * 返回值为String
 */

public class DecimalPointUtil {


    //转换double后面的小数点
    public static String convertDoubleDigits(double number, Integer digits) {
        if(digits==null||digits.equals(""))
            return String.valueOf(number);
//        if (digits == 0)
//            return String.valueOf(number);
        String num = String.valueOf(number);
        if (number == POSITIVE_INFINITY || number == NEGATIVE_INFINITY || num.equals("NaN"))
            return "";
        String format = null;
        for (int i = 0; i < digits + 1; i++) {
            if (i == 0)
                format = "0";
            else if (i == 1)
                format = format + ".0";
            else
                format = format + "0";
        }
        DecimalFormat df = new DecimalFormat(format);
        //System.out.println(df.format(number));//设置double类型小数点后位数格式
        return (df.format(number));
    }

    //转换double后面的小数点
    public static String convertDecelmalDigits(BigDecimal number, Integer digits) {
//        if (digits == 0)
//            return String.valueOf(number);
        String format = null;
        for (int i = 0; i < digits + 1; i++) {
            if (i == 0)
                format = "0";
            else if (i == 1)
                format = format + ".0";
            else
                format = format + "0";
        }
        DecimalFormat df = new DecimalFormat(format);
        //System.out.println(df.format(number));//设置double类型小数点后位数格式
        return (df.format(number));
    }


    //转换浮点数
    public static Double convertFloat(String number) {
        Float value = Float.intBitsToFloat((int) Long.parseLong(number.trim(), 16));
        BigDecimal bd = new BigDecimal(String.valueOf(value));
        Double d = bd.doubleValue();
        return d;
    }


    public static Double getDouble(String s) {
        if (s == null || s.equals(""))
            return 0.0;
        else return Double.parseDouble(s);
    }


}
