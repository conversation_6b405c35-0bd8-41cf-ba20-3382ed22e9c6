import{_ as h}from"./CardTable-rdWOL4_6.js";import{_ as p}from"./CardSearch-CB_HNR-Q.js";import{C as d,u as g,M as m,l as i,ae as u,af as T,g as _,n as b,q as c}from"./index-r0dFAfgr.js";import{g as y}from"./index-BiPwaSSe.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const s=g(),{$format:C,$messageInfo:S}=m(),N={name:"OperationLog",data(){return{cardSearchConfig:{filters:[{label:"搜索",field:"keyWord",type:"input"},{label:"查询时间",field:"timerange",type:"daterange"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>this.refreshData()}]}],defaultParams:{timerange:[i().subtract(1,"month"),i()],tenantId:u()}},cardTableConfig:{loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"createTime",label:"时间",icon:"iconfont icon-shijian",formatter:a=>C(a.createTime),iconStyle:{color:"#69E850",display:"inline-block","font-size":"16px"}},{prop:"firstName",label:"用户名"},{prop:"typeDescription",label:"操作内容"}],pagination:{page:1,limit:20,total:0,handleSize:a=>{this.cardTableConfig.pagination.limit=a,this.refreshData()},handlePage:a=>{this.cardTableConfig.pagination.page=a,this.refreshData()}}}}},created(){if(this.hasTenantFilter=s.roles[0]==="TENANT_SUPPORT"||s.roles[0]==="TENANT_PROMOTE",this.hasTenantFilter){this.cardSearchConfig.filters.splice(1,0,{label:"企业",field:"tenantId",type:"select",onChange:a=>{T(a)},options:[]}),this.getTenants();return}this.refreshData(!0)},methods:{refreshData(a){this.cardTableConfig.loading=!0;const e={timerange:[i().subtract(1,"month"),i()]};a||Object.assign(e,this.$refs.cardSearch.queryParams);const l={startTime:i(e.timerange[0]).valueOf(),endTime:i(e.timerange[1]).add(1,"days").valueOf(),page:this.cardTableConfig.pagination.page||1,size:this.cardTableConfig.pagination.limit||20,keyWord:e.keyWord};try{y(l).then(n=>{let r=[];r=n.data.data.map(o=>{const t=o;return t.name=o.firstName,t.time=i(o.createTime).format("YYYY-MM-DD  HH:mm:ss"),t.authority==="TENANT_ADMIN"?t.authority="企业管理人员":t.authority==="TENANT_SYS"?t.authority="企业配置人员":t.authority==="CUSTOMER_USER"?t.authority="企业用户":t.authority==="SYS_ADMIN"?t.authority="超级管理员":t.authority==="TENANT_SUPPORT"?t.authority="技术支持":t.authority==="TENANT_PROMOTE"&&(t.authority="市场推广"),t.info==="Login"&&(t.info="登录"),t}),this.total=n.data.total,this.cardTableConfig.dataList=r,this.cardTableConfig.pagination.total=this.total,this.cardTableConfig.loading=!1})}catch{this.cardTableConfig.loading=!1}},getTenants(){const a=s.tenantList;if(!a.length){S("该账户下没有企业信息");return}this.cardSearchConfig.filters[1].options=a.map(e=>({label:e.title,value:e.id})),this.refreshData(!0)}}},O={class:"wrapper"};function D(a,e,l,n,r,o){const t=p,f=h;return _(),b("div",O,[c(t,{ref:"cardSearch",class:"cardSearch",config:r.cardSearchConfig},null,8,["config"]),c(f,{config:r.cardTableConfig,class:"card-table"},null,8,["config"])])}const x=d(N,[["render",D]]);export{x as default};
