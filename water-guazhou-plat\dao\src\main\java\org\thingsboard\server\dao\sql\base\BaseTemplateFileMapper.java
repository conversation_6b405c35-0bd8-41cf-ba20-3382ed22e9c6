package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseTemplateFile;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTemplateFilePageRequest;

import java.util.List;

/**
 * 平台管理-模型文件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Mapper
public interface BaseTemplateFileMapper {
    /**
     * 查询平台管理-模型文件
     *
     * @param id 平台管理-模型文件主键
     * @return 平台管理-模型文件
     */
    public BaseTemplateFile selectBaseTemplateFileById(String id);

    /**
     * 查询平台管理-模型文件列表
     *
     * @param baseTemplateFile 平台管理-模型文件
     * @return 平台管理-模型文件集合
     */
    public IPage<BaseTemplateFile> selectBaseTemplateFileList(BaseTemplateFilePageRequest baseTemplateFile);

    /**
     * 新增平台管理-模型文件
     *
     * @param baseTemplateFile 平台管理-模型文件
     * @return 结果
     */
    public int insertBaseTemplateFile(BaseTemplateFile baseTemplateFile);

    /**
     * 修改平台管理-模型文件
     *
     * @param baseTemplateFile 平台管理-模型文件
     * @return 结果
     */
    public int updateBaseTemplateFile(BaseTemplateFile baseTemplateFile);

    /**
     * 删除平台管理-模型文件
     *
     * @param id 平台管理-模型文件主键
     * @return 结果
     */
    public int deleteBaseTemplateFileById(String id);

    /**
     * 批量删除平台管理-模型文件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseTemplateFileByIds(@Param("array") List<String> ids);
}
