package org.thingsboard.server.dao.sql.smartService.call;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.call.CallLog;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface CallLogMapper extends BaseMapper<CallLog> {

    List<CallLog> getList(@Param("phone") String phone, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("source") String source, @Param("area") String area, @Param("type") String type, @Param("topic") String topic, @Param("seatsId") String seatsId, @Param("status") String status, @Param("direction") String direction, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("phone") String phone, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("source") String source, @Param("area") String area, @Param("type") String type, @Param("topic") String topic, @Param("seatsId") String seatsId, @Param("status") String status, @Param("direction") String direction, @Param("tenantId") String tenantId);

    List<CallLog> getQueueMonitor(@Param("day") String day);
}
