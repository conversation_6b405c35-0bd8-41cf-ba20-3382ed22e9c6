import{d as s,r as i,a8 as n,g as p,h as d,F as f,q as m,i as x,bz as u,C as _}from"./index-r0dFAfgr.js";import{d as b}from"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const g=s({__name:"gcsjjcxx",props:{config:{}},setup(l){const c=l,r=i({defaultValue:n(()=>c.config),border:!0,direction:"horizontal",column:2,title:"工程设计基础信息",fields:[{type:"text",label:"设计编号:",field:"code"},{type:"text",label:"设计分类:",field:"type"},{type:"text",label:"设计费用:",field:"cost"},{type:"text",label:"设计管长:",field:"pipLengthDesign",formatter:a=>{const o=a&&JSON.parse(a||"")||[];let e="";return o.forEach(t=>{e+=`DN${t.dn}设计管长${t.length},`}),e}},{type:"text",label:"设计备注:",field:"remark"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName"},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]});return(a,o)=>{const e=b,t=u;return p(),d(t,{shadow:"hover",class:"card"},{default:f(()=>[m(e,{config:x(r)},null,8,["config"])]),_:1})}}}),v=_(g,[["__scopeId","data-v-63bccdf3"]]);export{v as default};
