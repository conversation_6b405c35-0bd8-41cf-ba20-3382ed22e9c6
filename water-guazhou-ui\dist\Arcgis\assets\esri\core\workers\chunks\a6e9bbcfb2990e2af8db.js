"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[4599],{45091:(e,t,s)=>{s.d(t,{Z:()=>l});var r,n,i=s(80442),a=s(71143);(n=r||(r={}))[n.varint=0]="varint",n[n.fixed64=1]="fixed64",n[n.delimited=2]="delimited",n[n.fixed32=5]="fixed32",n[n.unknown=99]="unknown";const o=4294967296,u=new TextDecoder("utf-8"),c=(0,i.Z)("safari")||(0,i.Z)("ios")?6:(0,i.Z)("ff")?12:32;class l{constructor(e,t,s=0,n=(e?e.byteLength:0)){this._tag=0,this._dataType=r.unknown,this._init(e,t,s,n)}_init(e,t,s,r){this._data=e,this._dataView=t,this._pos=s,this._end=r}asUnsafe(){return this}clone(){return new l(this._data,this._dataView,this._pos,this._end)}pos(){return this._pos}move(e){this._pos=e}nextTag(e){for(;;){if(this._pos===this._end)return!1;const t=this._decodeVarint();if(this._tag=t>>3,this._dataType=7&t,!e||e===this._tag)break;this.skip()}return!0}next(){if(this._pos===this._end)return!1;const e=this._decodeVarint();return this._tag=e>>3,this._dataType=7&e,!0}empty(){return this._pos>=this._end}tag(){return this._tag}getInt32(){return this._decodeVarint()}getInt64(){return this._decodeVarint()}getUInt32(){let e=4294967295;return e=(127&this._data[this._pos])>>>0,this._data[this._pos++]<128?e:(e=(e|(127&this._data[this._pos])<<7)>>>0,this._data[this._pos++]<128?e:(e=(e|(127&this._data[this._pos])<<14)>>>0,this._data[this._pos++]<128?e:(e=(e|(127&this._data[this._pos])<<21)>>>0,this._data[this._pos++]<128?e:(e=(e|(15&this._data[this._pos])<<28)>>>0,this._data[this._pos++]<128?e:void 0))))}getUInt64(){return this._decodeVarint()}getSInt32(){const e=this.getUInt32();if(void 0!==e)return e>>>1^-(1&e)|0}getSInt64(){return this._decodeSVarint()}getBool(){const e=0!==this._data[this._pos];return this._skip(1),e}getEnum(){return this._decodeVarint()}getFixed64(){const e=this._dataView,t=this._pos,s=e.getUint32(t,!0)+e.getUint32(t+4,!0)*o;return this._skip(8),s}getSFixed64(){const e=this._dataView,t=this._pos,s=e.getUint32(t,!0)+e.getInt32(t+4,!0)*o;return this._skip(8),s}getDouble(){const e=this._dataView.getFloat64(this._pos,!0);return this._skip(8),e}getFixed32(){const e=this._dataView.getUint32(this._pos,!0);return this._skip(4),e}getSFixed32(){const e=this._dataView.getInt32(this._pos,!0);return this._skip(4),e}getFloat(){const e=this._dataView.getFloat32(this._pos,!0);return this._skip(4),e}getString(){const e=this._getLength(),t=this._pos,s=this._toString(this._data,t,t+e);return this._skip(e),s}getBytes(){const e=this._getLength(),t=this._pos,s=this._toBytes(this._data,t,t+e);return this._skip(e),s}getLength(){return this._getLengthUnsafe()}processMessageWithArgs(e,t,s,r){const n=this.getMessage(),i=e(n,t,s,r);return n.release(),i}processMessage(e){const t=this.getMessage(),s=e(t);return t.release(),s}getMessage(){const e=this._getLength(),t=l.pool.acquire();return t._init(this._data,this._dataView,this._pos,this._pos+e),this._skip(e),t}release(){l.pool.release(this)}dataType(){return this._dataType}skip(){switch(this._dataType){case r.varint:this._decodeVarint();break;case r.fixed64:this._skip(8);break;case r.delimited:this._skip(this._getLength());break;case r.fixed32:this._skip(4);break;default:throw new Error("Invalid data type!")}}skipLen(e){this._skip(e)}_skip(e){if(this._pos+e>this._end)throw new Error("Attempt to skip past the end of buffer!");this._pos+=e}_decodeVarint(){const e=this._data;let t=this._pos,s=0,r=0;if(this._end-t>=10)do{if(r=e[t++],s|=127&r,0==(128&r))break;if(r=e[t++],s|=(127&r)<<7,0==(128&r))break;if(r=e[t++],s|=(127&r)<<14,0==(128&r))break;if(r=e[t++],s|=(127&r)<<21,0==(128&r))break;if(r=e[t++],s+=268435456*(127&r),0==(128&r))break;if(r=e[t++],s+=34359738368*(127&r),0==(128&r))break;if(r=e[t++],s+=4398046511104*(127&r),0==(128&r))break;if(r=e[t++],s+=562949953421312*(127&r),0==(128&r))break;if(r=e[t++],s+=72057594037927940*(127&r),0==(128&r))break;if(r=e[t++],s+=0x8000000000000000*(127&r),0==(128&r))break;throw new Error("Varint too long!")}while(0);else{let n=1;for(;t!==this._end&&(r=e[t],0!=(128&r));)++t,s+=(127&r)*n,n*=128;if(t===this._end)throw new Error("Varint overrun!");++t,s+=r*n}return this._pos=t,s}_decodeSVarint(){const e=this._data;let t=this._pos,s=0,r=0;const n=1&e[t];if(this._end-t>=10)do{if(r=e[t++],s|=127&r,0==(128&r))break;if(r=e[t++],s|=(127&r)<<7,0==(128&r))break;if(r=e[t++],s|=(127&r)<<14,0==(128&r))break;if(r=e[t++],s|=(127&r)<<21,0==(128&r))break;if(r=e[t++],s+=268435456*(127&r),0==(128&r))break;if(r=e[t++],s+=34359738368*(127&r),0==(128&r))break;if(r=e[t++],s+=4398046511104*(127&r),0==(128&r))break;if(r=e[t++],s+=562949953421312*(127&r),0==(128&r))break;if(r=e[t++],s+=72057594037927940*(127&r),0==(128&r))break;if(r=e[t++],s+=0x8000000000000000*(127&r),0==(128&r))break;throw new Error("Varint too long!")}while(0);else{let n=1;for(;t!==this._end&&(r=e[t],0!=(128&r));)++t,s+=(127&r)*n,n*=128;if(t===this._end)throw new Error("Varint overrun!");++t,s+=r*n}return this._pos=t,n?-(s+1)/2:s/2}_getLength(){if(this._dataType!==r.delimited)throw new Error("Not a delimited data type!");return this._decodeVarint()}_getLengthUnsafe(){return this.getUInt32()}_toString(e,t,s){if((s=Math.min(this._end,s))-t>c){const r=e.subarray(t,s);return u.decode(r)}let r="",n="";for(let i=t;i<s;++i){const t=e[i];128&t?n+="%"+t.toString(16):(r+=decodeURIComponent(n)+String.fromCharCode(t),n="")}return n.length&&(r+=decodeURIComponent(n)),r}_toBytes(e,t,s){return s=Math.min(this._end,s),new Uint8Array(e.buffer,t,s-t)}}l.pool=new a.Z(l,void 0,(e=>{e._data=null,e._dataView=null}))},16306:(e,t,s)=>{s.d(t,{aX:()=>w});var r=s(68773),n=s(20102),i=s(92604),a=s(70586),o=s(38913),u=s(58901),c=s(73913),l=s(8744),p=s(40488),f=(s(66577),s(3172)),d=s(33955),h=s(11282),g=s(17452);async function y(e,t,s){const r="string"==typeof e?(0,g.mN)(e):e,n=t[0].spatialReference,i=(0,d.Ji)(t[0]),a={...s,query:{...r.query,f:"json",sr:n.wkid?n.wkid:JSON.stringify(n),geometries:JSON.stringify((u=t,{geometryType:(0,d.Ji)(u[0]),geometries:u.map((e=>e.toJSON()))}))}},{data:o}=await(0,f.default)(r.path+"/simplify",a);var u;return function(e,t,s){const r=(0,d.q9)(t);return e.map((e=>{const t=r.fromJSON(e);return t.spatialReference=s,t}))}(o.geometries,i,n)}const m=i.Z.getLogger("esri.geometry.support.normalizeUtils");function _(e){return"polygon"===e[0].type}function k(e){return"polyline"===e[0].type}function b(e,t,s){if(t){const t=function(e,t){if(!(e instanceof u.Z||e instanceof o.Z)){const e="straightLineDensify: the input geometry is neither polyline nor polygon";throw m.error(e),new n.Z(e)}const s=(0,c.x3)(e),r=[];for(const e of s){const s=[];r.push(s),s.push([e[0][0],e[0][1]]);for(let r=0;r<e.length-1;r++){const n=e[r][0],i=e[r][1],a=e[r+1][0],o=e[r+1][1],u=Math.sqrt((a-n)*(a-n)+(o-i)*(o-i)),c=(o-i)/u,l=(a-n)/u,p=u/t;if(p>1){for(let e=1;e<=p-1;e++){const r=e*t,a=l*r+n,o=c*r+i;s.push([a,o])}const e=(u+Math.floor(p-1)*t)/2,r=l*e+n,a=c*e+i;s.push([r,a])}s.push([a,o])}}return function(e){return"polygon"===e.type}(e)?new o.Z({rings:r,spatialReference:e.spatialReference}):new u.Z({paths:r,spatialReference:e.spatialReference})}(e,1e6);e=(0,p.Sx)(t,!0)}return s&&(e=(0,c.Sy)(e,s)),e}function S(e,t,s){if(Array.isArray(e)){const r=e[0];if(r>t){const s=(0,c.XZ)(r,t);e[0]=r+s*(-2*t)}else if(r<s){const t=(0,c.XZ)(r,s);e[0]=r+t*(-2*s)}}else{const r=e.x;if(r>t){const s=(0,c.XZ)(r,t);e=e.clone().offset(s*(-2*t),0)}else if(r<s){const t=(0,c.XZ)(r,s);e=e.clone().offset(t*(-2*s),0)}}return e}function x(e,t){let s=-1;for(let r=0;r<t.cutIndexes.length;r++){const n=t.cutIndexes[r],i=t.geometries[r],a=(0,c.x3)(i);for(let e=0;e<a.length;e++){const t=a[e];t.some((s=>{if(s[0]<180)return!0;{let s=0;for(let e=0;e<t.length;e++){const r=t[e][0];s=r>s?r:s}s=Number(s.toFixed(9));const r=-360*(0,c.XZ)(s,180);for(let s=0;s<t.length;s++){const t=i.getPoint(e,s);i.setPoint(e,s,t.clone().offset(r,0))}return!0}}))}if(n===s){if(_(e))for(const t of(0,c.x3)(i))e[n]=e[n].addRing(t);else if(k(e))for(const t of(0,c.x3)(i))e[n]=e[n].addPath(t)}else s=n,e[n]=i}return e}async function w(e,t,s){if(!Array.isArray(e))return w([e],t);t&&"string"!=typeof t&&m.warn("normalizeCentralMeridian()","The url object is deprecated, use the url string instead");const n="string"==typeof t?t:t?.url??r.Z.geometryServiceUrl;let i,g,_,k,T,q,R,F,I=0;const U=[],Z=[];for(const t of e)if((0,a.Wi)(t))Z.push(t);else if(i||(i=t.spatialReference,g=(0,l.C5)(i),_=i.isWebMercator,q=_?102100:4326,k=c.UZ[q].maxX,T=c.UZ[q].minX,R=c.UZ[q].plus180Line,F=c.UZ[q].minus180Line),g)if("mesh"===t.type)Z.push(t);else if("point"===t.type)Z.push(S(t.clone(),k,T));else if("multipoint"===t.type){const e=t.clone();e.points=e.points.map((e=>S(e,k,T))),Z.push(e)}else if("extent"===t.type){const e=t.clone()._normalize(!1,!1,g);Z.push(e.rings?new o.Z(e):e)}else if(t.extent){const e=t.extent,s=(0,c.XZ)(e.xmin,T)*(2*k);let r=0===s?t.clone():(0,c.Sy)(t.clone(),s);e.offset(s,0),e.intersects(R)&&e.xmax!==k?(I=e.xmax>I?e.xmax:I,r=b(r,_),U.push(r),Z.push("cut")):e.intersects(F)&&e.xmin!==T?(I=e.xmax*(2*k)>I?e.xmax*(2*k):I,r=b(r,_,360),U.push(r),Z.push("cut")):Z.push(r)}else Z.push(t.clone());else Z.push(t);let E=(0,c.XZ)(I,k),V=-90;const C=E,N=new u.Z;for(;E>0;){const e=360*E-180;N.addPath([[e,V],[e,-1*V]]),V*=-1,E--}if(U.length>0&&C>0){const t=x(U,await async function(e,t,s,r){const n=(0,h.en)(e),i=t[0].spatialReference,a={...r,query:{...n.query,f:"json",sr:JSON.stringify(i),target:JSON.stringify({geometryType:(0,d.Ji)(t[0]),geometries:t}),cutter:JSON.stringify(s)}},o=await(0,f.default)(n.path+"/cut",a),{cutIndexes:u,geometries:c=[]}=o.data;return{cutIndexes:u,geometries:c.map((e=>{const t=(0,d.im)(e);return t.spatialReference=i,t}))}}(n,U,N,s)),r=[],i=[];for(let s=0;s<Z.length;s++){const n=Z[s];if("cut"!==n)i.push(n);else{const n=t.shift(),o=e[s];(0,a.pC)(o)&&"polygon"===o.type&&o.rings&&o.rings.length>1&&n.rings.length>=o.rings.length?(r.push(n),i.push("simplify")):i.push(_?(0,p.$)(n):n)}}if(!r.length)return i;const o=await y(n,r,s),u=[];for(let e=0;e<i.length;e++){const t=i[e];"simplify"!==t?u.push(t):u.push(_?(0,p.$)(o.shift()):o.shift())}return u}const O=[];for(let e=0;e<Z.length;e++){const t=Z[e];if("cut"!==t)O.push(t);else{const e=U.shift();O.push(!0===_?(0,p.$)(e):e)}}return O}},73913:(e,t,s)=>{s.d(t,{Sy:()=>u,UZ:()=>a,XZ:()=>o,x3:()=>c});var r=s(58901),n=s(82971),i=s(33955);const a={102100:{maxX:20037508.342788905,minX:-20037508.342788905,plus180Line:new r.Z({paths:[[[20037508.342788905,-20037508.342788905],[20037508.342788905,20037508.342788905]]],spatialReference:n.Z.WebMercator}),minus180Line:new r.Z({paths:[[[-20037508.342788905,-20037508.342788905],[-20037508.342788905,20037508.342788905]]],spatialReference:n.Z.WebMercator})},4326:{maxX:180,minX:-180,plus180Line:new r.Z({paths:[[[180,-180],[180,180]]],spatialReference:n.Z.WGS84}),minus180Line:new r.Z({paths:[[[-180,-180],[-180,180]]],spatialReference:n.Z.WGS84})}};function o(e,t){return Math.ceil((e-t)/(2*t))}function u(e,t){const s=c(e);for(const e of s)for(const s of e)s[0]+=t;return e}function c(e){return(0,i.oU)(e)?e.rings:e.paths}},76497:(e,t,s)=>{function r(e){const t={};for(const s in e){if("declaredClass"===s)continue;const n=e[s];if(null!=n&&"function"!=typeof n)if(Array.isArray(n)){t[s]=[];for(let e=0;e<n.length;e++)t[s][e]=r(n[e])}else"object"==typeof n?n.toJSON&&(t[s]=JSON.stringify(n)):t[s]=n}return t}s.d(t,{A:()=>r})},77863:(e,t,s)=>{s.d(t,{G$:()=>T,K9:()=>U,O7:()=>p});var r=s(20102),n=s(70586),i=s(45091),a=s(5428),o=s(78760);const u=["esriFieldTypeSmallInteger","esriFieldTypeInteger","esriFieldTypeSingle","esriFieldTypeDouble","esriFieldTypeString","esriFieldTypeDate","esriFieldTypeOID","esriFieldTypeGeometry","esriFieldTypeBlob","esriFieldTypeRaster","esriFieldTypeGUID","esriFieldTypeGlobalID","esriFieldTypeXML"],c=["sqlTypeBigInt","sqlTypeBinary","sqlTypeBit","sqlTypeChar","sqlTypeDate","sqlTypeDecimal","sqlTypeDouble","sqlTypeFloat","sqlTypeGeometry","sqlTypeGUID","sqlTypeInteger","sqlTypeLongNVarchar","sqlTypeLongVarbinary","sqlTypeLongVarchar","sqlTypeNChar","sqlTypeNVarchar","sqlTypeOther","sqlTypeReal","sqlTypeSmallInt","sqlTypeSqlXml","sqlTypeTime","sqlTypeTimestamp","sqlTypeTimestamp2","sqlTypeTinyInt","sqlTypeVarbinary","sqlTypeVarchar"],l=["upperLeft","lowerLeft"];function p(e){return e>=u.length?null:u[e]}function f(e){return e>=c.length?null:c[e]}function d(e){return e>=l.length?null:l[e]}function h(e,t){return t>=e.geometryTypes.length?null:e.geometryTypes[t]}function g(e,t,s){const r=e.asUnsafe(),n=t.createPointGeometry(s);for(;r.next();)switch(r.tag()){case 3:{const e=r.getUInt32(),s=r.pos()+e;let i=0;for(;r.pos()<s;)t.addCoordinatePoint(n,r.getSInt64(),i++);break}default:r.skip()}return n}function y(e,t,s){const r=e.asUnsafe(),n=t.createGeometry(s),i=2+(s.hasZ?1:0)+(s.hasM?1:0);for(;r.next();)switch(r.tag()){case 2:{const e=r.getUInt32(),s=r.pos()+e;let i=0;for(;r.pos()<s;)t.addLength(n,r.getUInt32(),i++);break}case 3:{const e=r.getUInt32(),s=r.pos()+e;let a=0;for(t.allocateCoordinates(n);r.pos()<s;)t.addCoordinate(n,r.getSInt64(),a),a++,a===i&&(a=0);break}default:r.skip()}return n}function m(e){const t=e.asUnsafe(),s=new a.Z;let r="esriGeometryPoint";for(;t.next();)switch(t.tag()){case 2:{const e=t.getUInt32(),r=t.pos()+e;for(;t.pos()<r;)s.lengths.push(t.getUInt32());break}case 3:{const e=t.getUInt32(),r=t.pos()+e;for(;t.pos()<r;)s.coords.push(t.getSInt64());break}case 1:r=o.A[t.getEnum()];break;default:t.skip()}return{queryGeometry:s,queryGeometryType:r}}function _(e){const t=e.asUnsafe();for(;t.next();)switch(t.tag()){case 1:return t.getString();case 2:return t.getFloat();case 3:return t.getDouble();case 4:return t.getSInt32();case 5:return t.getUInt32();case 6:return t.getInt64();case 7:return t.getUInt64();case 8:return t.getSInt64();case 9:return t.getBool();default:return t.skip(),null}return null}function k(e){const t=e.asUnsafe(),s={type:p(0)};for(;t.next();)switch(t.tag()){case 1:s.name=t.getString();break;case 2:s.type=p(t.getEnum());break;case 3:s.alias=t.getString();break;case 4:s.sqlType=f(t.getEnum());break;case 5:default:t.skip();break;case 6:s.defaultValue=t.getString()}return s}function b(e){const t={},s=e.asUnsafe();for(;s.next();)switch(s.tag()){case 1:t.name=s.getString();break;case 2:t.isSystemMaintained=s.getBool();break;default:s.skip()}return t}function S(e,t,s,r){const n=t.createFeature(s);let i=0;for(;e.next();)switch(e.tag()){case 1:{const t=r[i++].name;n.attributes[t]=e.processMessage(_);break}case 2:n.geometry=e.processMessageWithArgs(y,t,s);break;case 4:n.centroid=e.processMessageWithArgs(g,t,s);break;default:e.skip()}return n}function x(e){const t=[1,1,1,1],s=e.asUnsafe();for(;s.next();)switch(s.tag()){case 1:t[0]=s.getDouble();break;case 2:t[1]=s.getDouble();break;case 4:t[2]=s.getDouble();break;case 3:t[3]=s.getDouble();break;default:s.skip()}return t}function w(e){const t=[0,0,0,0],s=e.asUnsafe();for(;s.next();)switch(s.tag()){case 1:t[0]=s.getDouble();break;case 2:t[1]=s.getDouble();break;case 4:t[2]=s.getDouble();break;case 3:t[3]=s.getDouble();break;default:s.skip()}return t}function T(e){const t={originPosition:d(0)},s=e.asUnsafe();for(;s.next();)switch(s.tag()){case 1:t.originPosition=d(s.getEnum());break;case 2:t.scale=s.processMessage(x);break;case 3:t.translate=s.processMessage(w);break;default:s.skip()}return t}function q(e){const t={},s=e.asUnsafe();for(;s.next();)switch(s.tag()){case 1:t.shapeAreaFieldName=s.getString();break;case 2:t.shapeLengthFieldName=s.getString();break;case 3:t.units=s.getString();break;default:s.skip()}return t}function R(e,t){const s=t.createSpatialReference();for(;e.next();)switch(e.tag()){case 1:s.wkid=e.getUInt32();break;case 5:s.wkt=e.getString();break;case 2:s.latestWkid=e.getUInt32();break;case 3:s.vcsWkid=e.getUInt32();break;case 4:s.latestVcsWkid=e.getUInt32();break;default:e.skip()}return s}function F(e,t){const s=t.createFeatureResult(),r=e.asUnsafe();s.geometryType=h(t,0);let n=!1;for(;r.next();)switch(r.tag()){case 1:s.objectIdFieldName=r.getString();break;case 3:s.globalIdFieldName=r.getString();break;case 4:s.geohashFieldName=r.getString();break;case 5:s.geometryProperties=r.processMessage(q);break;case 7:s.geometryType=h(t,r.getEnum());break;case 8:s.spatialReference=r.processMessageWithArgs(R,t);break;case 10:s.hasZ=r.getBool();break;case 11:s.hasM=r.getBool();break;case 12:s.transform=r.processMessage(T);break;case 9:{const e=r.getBool();s.exceededTransferLimit=e;break}case 13:t.addField(s,r.processMessage(k));break;case 15:n||(t.prepareFeatures(s),n=!0),t.addFeature(s,r.processMessageWithArgs(S,t,s,s.fields));break;case 2:s.uniqueIdField=r.processMessage(b);break;default:r.skip()}return t.finishFeatureResult(s),s}function I(e,t){const s={};let r=null;for(;e.next();)switch(e.tag()){case 4:r=e.processMessageWithArgs(m);break;case 1:s.featureResult=e.processMessageWithArgs(F,t);break;default:e.skip()}return(0,n.pC)(r)&&s.featureResult&&t.addQueryGeometry(s,r),s}function U(e,t){try{const s=2,r=new i.Z(new Uint8Array(e),new DataView(e)),n={};for(;r.next();)r.tag()===s?n.queryResult=r.processMessageWithArgs(I,t):r.skip();return n}catch(e){throw new r.Z("query:parsing-pbf","Error while parsing FeatureSet PBF payload",{error:e})}}},78760:(e,t,s)=>{s.d(t,{A:()=>u,J:()=>c});var r=s(67900),n=s(8744),i=s(70272),a=s(44876),o=s(5428);const u=["esriGeometryPoint","esriGeometryMultipoint","esriGeometryPolyline","esriGeometryPolygon"];class c{constructor(e){this._options=e,this.geometryTypes=u,this._coordinatePtr=0,this._vertexDimension=0}createFeatureResult(){return new a.Z}prepareFeatures(e){this._vertexDimension=2,e.hasZ&&this._vertexDimension++,e.hasM&&this._vertexDimension++}finishFeatureResult(e){if(!e||!e.features||!e.hasZ||!this._options.sourceSpatialReference||!e.spatialReference||(0,n.fS)(e.spatialReference,this._options.sourceSpatialReference)||e.spatialReference.vcsWkid)return;const t=(0,r._R)(this._options.sourceSpatialReference)/(0,r._R)(e.spatialReference);if(1!==t)for(const s of e.features){if(!(0,i.S6)(s))continue;const e=s.geometry.coords;for(let s=2;s<e.length;s+=3)e[s]*=t}}addFeature(e,t){e.features.push(t)}createFeature(){return new i.u_}createSpatialReference(){return{wkid:0}}createGeometry(){return new o.Z}addField(e,t){e.fields.push(t)}allocateCoordinates(e){e.coords.length=e.lengths.reduce(((e,t)=>e+t),0)*this._vertexDimension,this._coordinatePtr=0}addCoordinate(e,t){e.coords[this._coordinatePtr++]=t}addCoordinatePoint(e,t){e.coords.push(t)}addLength(e,t){e.lengths.push(t)}addQueryGeometry(e,t){e.queryGeometry=t.queryGeometry,e.queryGeometryType=t.queryGeometryType}createPointGeometry(){return new o.Z}}},45178:(e,t,s)=>{s.d(t,{C:()=>n});var r=s(77863);function n(e,t){const s=(0,r.K9)(e,t),n=s.queryResult.featureResult,i=s.queryResult.queryGeometry,a=s.queryResult.queryGeometryType;if(n&&n.features&&n.features.length&&n.objectIdFieldName){const e=n.objectIdFieldName;for(const t of n.features)t.attributes&&(t.objectId=t.attributes[e])}return n&&(n.queryGeometry=i,n.queryGeometryType=a),n}},34599:(e,t,s)=>{s.d(t,{Ev:()=>y,JT:()=>d,Vr:()=>_,hH:()=>m,n7:()=>g,qp:()=>h});var r=s(3172),n=s(70586),i=s(17452),a=s(33955),o=s(16306),u=s(76497),c=s(45178),l=s(28694);const p="Layer does not support extent calculation.";function f(e,t){const s=e.geometry,r=e.toJSON();delete r.compactGeometryEnabled,delete r.defaultSpatialReferenceEnabled;const i=r;let o,u,c;if((0,n.pC)(s)&&(u=s.spatialReference,c=s.spatialReference.wkid||JSON.stringify(s.spatialReference),i.geometryType=(0,a.Ji)(s),i.geometry=function(e,t){if(t&&"extent"===e.type)return`${e.xmin},${e.ymin},${e.xmax},${e.ymax}`;if(t&&"point"===e.type)return`${e.x},${e.y}`;const s=e.toJSON();return delete s.spatialReference,JSON.stringify(s)}(s,e.compactGeometryEnabled),i.inSR=c),r.groupByFieldsForStatistics&&(i.groupByFieldsForStatistics=r.groupByFieldsForStatistics.join(",")),r.objectIds&&(i.objectIds=r.objectIds.join(",")),r.orderByFields&&(i.orderByFields=r.orderByFields.join(",")),!r.outFields||!r.returnDistinctValues&&(t?.returnCountOnly||t?.returnExtentOnly||t?.returnIdsOnly)?delete i.outFields:r.outFields.includes("*")?i.outFields="*":i.outFields=r.outFields.join(","),r.outSR?(i.outSR=r.outSR.wkid||JSON.stringify(r.outSR),o=e.outSpatialReference):s&&(r.returnGeometry||r.returnCentroid)&&(i.outSR=i.inSR,o=u),r.returnGeometry&&delete r.returnGeometry,r.outStatistics&&(i.outStatistics=JSON.stringify(r.outStatistics)),r.fullText&&(i.fullText=JSON.stringify(r.fullText)),r.pixelSize&&(i.pixelSize=JSON.stringify(r.pixelSize)),r.quantizationParameters&&(e.defaultSpatialReferenceEnabled&&(0,n.pC)(u)&&(0,n.pC)(e.quantizationParameters)&&(0,n.pC)(e.quantizationParameters.extent)&&u.equals(e.quantizationParameters.extent.spatialReference)&&delete r.quantizationParameters.extent.spatialReference,i.quantizationParameters=JSON.stringify(r.quantizationParameters)),r.parameterValues&&(i.parameterValues=JSON.stringify(r.parameterValues)),r.rangeValues&&(i.rangeValues=JSON.stringify(r.rangeValues)),r.dynamicDataSource&&(i.layer=JSON.stringify({source:r.dynamicDataSource}),delete r.dynamicDataSource),r.timeExtent){const e=r.timeExtent,{start:t,end:s}=e;null==t&&null==s||(i.time=t===s?t:`${t??"null"},${s??"null"}`),delete r.timeExtent}return e.defaultSpatialReferenceEnabled&&(0,n.pC)(u)&&(0,n.pC)(o)&&u.equals(o)&&(i.defaultSR=i.inSR,delete i.inSR,delete i.outSR),i}async function d(e,t,s,r){const i=(0,n.pC)(t.timeExtent)&&t.timeExtent.isEmpty?{data:{features:[]}}:await k(e,t,"json",r);return(0,l.p)(t,s,i.data),i}async function h(e,t,s,r){if((0,n.pC)(t.timeExtent)&&t.timeExtent.isEmpty)return{data:s.createFeatureResult()};const i=await g(e,t,r),a=i;return a.data=(0,c.C)(i.data,s),a}function g(e,t,s){return k(e,t,"pbf",s)}function y(e,t,s){return(0,n.pC)(t.timeExtent)&&t.timeExtent.isEmpty?Promise.resolve({data:{objectIds:[]}}):k(e,t,"json",s,{returnIdsOnly:!0})}function m(e,t,s){return(0,n.pC)(t.timeExtent)&&t.timeExtent.isEmpty?Promise.resolve({data:{count:0}}):k(e,t,"json",s,{returnIdsOnly:!0,returnCountOnly:!0})}function _(e,t,s){return(0,n.pC)(t.timeExtent)&&t.timeExtent.isEmpty?Promise.resolve({data:{count:0,extent:null}}):k(e,t,"json",s,{returnExtentOnly:!0,returnCountOnly:!0}).then((e=>{const t=e.data;if(t.hasOwnProperty("extent"))return e;if(t.features)throw new Error(p);if(t.hasOwnProperty("count"))throw new Error(p);return e}))}function k(e,t,s,a={},c={}){const l="string"==typeof e?(0,i.mN)(e):e,p=t.geometry?[t.geometry]:[];return a.responseType="pbf"===s?"array-buffer":"json",(0,o.aX)(p,null,a).then((e=>{const o=e&&e[0];(0,n.pC)(o)&&((t=t.clone()).geometry=o);const p=(0,u.A)({...l.query,f:s,...c,...f(t,c)});return(0,r.default)((0,i.v_)(l.path,"query"),{...a,query:{...p,...a.query}})}))}},11282:(e,t,s)=>{s.d(t,{cv:()=>o,en:()=>a,lA:()=>i}),s(68773),s(40330);var r=s(22974),n=s(17452);function i(e,t){return t?{...t,query:{...e??{},...t.query}}:{query:e}}function a(e){return"string"==typeof e?(0,n.mN)(e):(0,r.d9)(e)}function o(e,t,s){const r={};for(const n in e){if("declaredClass"===n)continue;const i=e[n];if(null!=i&&"function"!=typeof i)if(Array.isArray(i)){r[n]=[];for(let e=0;e<i.length;e++)r[n][e]=o(i[e])}else if("object"==typeof i)if(i.toJSON){const e=i.toJSON(s&&s[n]);r[n]=t?e:JSON.stringify(e)}else r[n]=t?i:JSON.stringify(i);else r[n]=i}return r}s(71058)}}]);