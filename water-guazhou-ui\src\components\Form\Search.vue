<template>
  <div class="search-root">
    <div class="search-box" :style="state.operationStyle">
      <div ref="refWrapper" :class="{ 'filter-wrapper': true }">
        <div
          ref="refScrollable"
          class="scrollable-items"
          :class="[state.showScrollBar ? 'show-scroll-bar' : '']"
        >
          <template v-for="(filter, i) in config.filters" :key="i">
            <template v-if="!filter.hidden">
              <div
                v-if="filter.type === 'divider'"
                class="form-item app-header-divider"
              >
                <div class="app-header-divider left"></div>
                <div class="app-header-divider right"></div>
              </div>
              <div v-else class="form-item" :style="filter.itemContainerStyle">
                <label
                  v-if="filter.label"
                  :style="{
                    width: filter.labelWidth,
                    ...((filter.colStyles || {}) as any)
                  }"
                  class="form-item-label"
                  >{{ filter.label }}</label
                >
                <FormItem
                  v-if="filter.field"
                  v-model="state.queryParams[filter.field]"
                  :config="filter"
                  :popper-class="config.popperClass"
                  :size="config.size"
                  @change="(val: any) => handleFormItemChange(val, filter)"
                  @blur="(val: any) => handleFormItemBlur(val, filter)"
                  @focus="(val: any) => handleFormItemFocus(val, filter)"
                />
                <FormItem
                  v-else
                  :config="filter"
                  :popper-class="config.popperClass"
                  :size="config.size"
                />
                <template
                  v-for="(extraFilter, j) in filter.extraFormItem"
                  :key="j"
                >
                  <FormItem
                    v-if="extraFilter?.field"
                    v-model="state.queryParams[extraFilter.field]"
                    :config="extraFilter"
                    :popper-class="config.popperClass"
                    :size="config.size"
                    @change="
                      (val: any) => handleFormItemChange(val, extraFilter)
                    "
                    @blur="(val: any) => handleFormItemBlur(val, extraFilter)"
                    @focus="(val: any) => handleFormItemFocus(val, extraFilter)"
                  />
                  <FormItem
                    v-else
                    :config="extraFilter"
                    :popper-class="config.popperClass"
                    :size="config.size"
                  />
                </template>
              </div>
            </template>
          </template>
          <template v-for="(filter, i) in config.moreFilters" :key="i">
            <template v-if="!filter.hidden">
              <div
                v-if="filter.type === 'divider'"
                class="form-item app-header-divider"
              >
                <div class="app-header-divider left"></div>
                <div class="app-header-divider right"></div>
              </div>
              <div v-else class="form-item">
                <label v-if="filter.label" class="form-item-label">{{
                  filter.label
                }}</label>
                <FormItem
                  v-if="filter.field"
                  v-model="state.queryParams[filter.field]"
                  :config="filter"
                  :popper-class="config.popperClass"
                  :size="config.size"
                  @change="(val: any) => handleFormItemChange(val, filter)"
                  @blur="(val: any) => handleFormItemBlur(val, filter)"
                  @focus="(val: any) => handleFormItemFocus(val, filter)"
                />
                <FormItem
                  v-else
                  :config="filter"
                  :popper-class="config.popperClass"
                  :size="config.size"
                />
              </div>
            </template>
          </template>
        </div>
        <div
          v-if="state.showScrollBar && state.scrollLeft > 0"
          class="scroll-bar left"
        >
          <div class="bar-wrapper">
            <Icon
              class="scroll-bar-icon"
              icon="ep:d-arrow-left"
              @click="(e) => scrollbarClick('left')"
            ></Icon>
          </div>
        </div>
        <div
          v-if="state.showScrollBar && !state.isBottom"
          class="scroll-bar right"
        >
          <div class="bar-wrapper">
            <Icon
              class="scroll-bar-icon"
              icon="ep:d-arrow-right"
              @click="(e) => scrollbarClick('right')"
            ></Icon>
          </div>
        </div>
      </div>
      <div
        v-if="config.operations?.length || state.showScrollBar"
        ref="refOperations"
        class="operation-items"
      >
        <template v-for="(filter, i) in config.operations" :key="i">
          <div
            v-if="!filter.hidden"
            class="operation-item"
            @click="closeDetail"
          >
            <FormItem
              v-if="filter.field"
              v-model="state.queryParams[filter.field]"
              :config="filter"
              :popper-class="config.popperClass"
              :size="config.size"
              @change="(val: any) => handleFormItemChange(val, filter)"
              @blur="(val: any) => handleFormItemBlur(val, filter)"
              @focus="(val: any) => handleFormItemFocus(val, filter)"
            />
            <FormItem
              v-else
              :config="filter"
              :popper-class="config.popperClass"
              :size="config.size"
            />
          </div>
        </template>
        <div class="operation-item">
          <el-button
            v-if="state.showScrollBar"
            class="stateSwitch"
            :type="'text'"
            :title="state.showDetail ? '点击收起' : '点击展开'"
            link
            @click="openDetail"
          >
            <Icon
              :icon="
                state.showDetail
                  ? 'tabler:arrows-minimize'
                  : 'tabler:arrows-maximize'
              "
            ></Icon>
          </el-button>
        </div>
      </div>
    </div>
    <el-collapse-transition>
      <el-card v-show="state.showDetail" class="detail">
        <el-row :gutter="10" class="show_detail">
          <template v-for="(filter, i) in config.filters" :key="i">
            <el-col
              :xs="filter.xs ?? 24"
              :sm="filter.sm ?? 12"
              :md="filter.md ?? 6"
              :lg="filter.lg ?? 5"
              :xl="filter.xl ?? 4"
            >
              <div
                v-if="filter.type === 'divider'"
                class="form-item app-header-divider"
              >
                <div class="app-header-divider left"></div>
                <div class="app-header-divider right"></div>
              </div>
              <div v-else class="form-item" :style="filter.itemContainerStyle">
                <label
                  v-if="filter.label"
                  :style="{
                    width: filter.labelWidth,
                    ...((filter.colStyles || {}) as any)
                  }"
                  class="form-item-label"
                  >{{ filter.label }}</label
                >
                <FormItem
                  v-if="filter.field"
                  v-model="state.queryParams[filter.field]"
                  :config="filter"
                  :popper-class="config.popperClass"
                  :size="config.size"
                  @change="(val: any) => handleFormItemChange(val, filter)"
                  @blur="(val: any) => handleFormItemBlur(val, filter)"
                  @focus="(val: any) => handleFormItemFocus(val, filter)"
                />
                <FormItem
                  v-else
                  :config="filter"
                  :popper-class="config.popperClass"
                  :size="config.size"
                />
                <template
                  v-for="(extraFilter, j) in filter.extraFormItem"
                  :key="j"
                >
                  <FormItem
                    v-if="extraFilter?.field"
                    v-model="state.queryParams[extraFilter.field]"
                    :config="extraFilter"
                    :popper-class="config.popperClass"
                    :size="config.size"
                    @change="
                      (val: any) => handleFormItemChange(val, extraFilter)
                    "
                    @blur="(val: any) => handleFormItemBlur(val, extraFilter)"
                    @focus="(val: any) => handleFormItemFocus(val, extraFilter)"
                  />
                  <FormItem
                    v-else
                    :config="extraFilter"
                    :popper-class="config.popperClass"
                    :size="config.size"
                  />
                </template>
              </div>
            </el-col>
          </template>
          <template v-for="(filter, i) in config.moreFilters" :key="i">
            <el-col
              :xs="filter.xs ?? 24"
              :sm="filter.sm ?? 12"
              :md="filter.md ?? 6"
              :lg="filter.lg ?? 5"
              :xl="filter.xl ?? 4"
            >
              <div
                v-if="filter.type === 'divider'"
                class="form-item app-header-divider"
              >
                <div class="app-header-divider left"></div>
                <div class="app-header-divider right"></div>
              </div>
              <div v-else class="form-item" :style="filter.itemContainerStyle">
                <label
                  v-if="filter.label"
                  :style="{
                    width: filter.labelWidth,
                    ...((filter.colStyles || {}) as any)
                  }"
                  class="form-item-label"
                  >{{ filter.label }}</label
                >
                <FormItem
                  v-if="filter.field"
                  v-model="state.queryParams[filter.field]"
                  :config="filter"
                  :popper-class="config.popperClass"
                  :size="config.size"
                  @change="(val: any) => handleFormItemChange(val, filter)"
                  @blur="(val: any) => handleFormItemBlur(val, filter)"
                  @focus="(val: any) => handleFormItemFocus(val, filter)"
                />
                <FormItem
                  v-else
                  :config="filter"
                  :popper-class="config.popperClass"
                  :size="config.size"
                />
                <template
                  v-for="(extraFilter, j) in filter.extraFormItem"
                  :key="j"
                >
                  <FormItem
                    v-if="extraFilter?.field"
                    v-model="state.queryParams[extraFilter.field]"
                    :config="extraFilter"
                    :popper-class="config.popperClass"
                    :size="config.size"
                    @change="
                      (val: any) => handleFormItemChange(val, extraFilter)
                    "
                    @blur="(val: any) => handleFormItemBlur(val, extraFilter)"
                    @focus="(val: any) => handleFormItemFocus(val, extraFilter)"
                  />
                  <FormItem
                    v-else
                    :config="extraFilter"
                    :popper-class="config.popperClass"
                    :size="config.size"
                  />
                </template>
              </div>
            </el-col>
          </template>
        </el-row>
      </el-card>
    </el-collapse-transition>
  </div>
</template>

<script lang="ts" setup>
import { reactive, toRefs, onMounted, watch, ref, onBeforeUnmount, nextTick } from 'vue';
import { Icon } from '@iconify/vue';
// import { IElForm } from '@/common/types/element-plus'
// import { ISearchMoreIns } from '../type'

// const refForm = ref<IElForm>()
// const refSearchMore = ref<ISearchMoreIns>()
const props = defineProps<{
  config: ISearch;
}>()

// 初始化参数
const state = reactive<{
  scrollLeft: number;
  /** 是否滚动至最右 */
  isBottom: boolean;
  showScrollBar: boolean;
  operationStyle: any;
  moreFilterVisiable: boolean;
  moreFilterConditions: {
    label?: string;
    value?: any;
    filter?: IFormItem;
    formatter?: (val: any, row?: any, filter?: IFormItem) => any;
  }[];
  queryParams: Record<string, any>;
  //* * 详情 */
  showDetail: boolean;
}>({
  showScrollBar: false,
  scrollLeft: 0,
  isBottom: false,
  operationStyle: {
    '--operationWidth': '0',
    '--gradientColor': props.config.scrollBarGradientColor
  },
  moreFilterVisiable: false,
  moreFilterConditions: [],
  queryParams: {
    ...(props.config.defaultParams || {})
  },
  showDetail: false
});

// 接收AddtionalFilters附加参数
// const addtionalSubmit = (params: Record<string, any>) => {
//   Object.assign(state.queryParams, params)
//   formateQueryParamsToString(params)
//   props.config.handleSearch && props.config.handleSearch(state.queryParams)
// }
const handleFormItemChange = (val: any, item: IFormItem) => {
  item.onChange && item.onChange(val, item);
};

const handleFormItemBlur = (val: any, item: IFormItem) => {
  item.onBlur && item.onBlur(val, item);
};

const handleFormItemFocus = (val: any, item: IFormItem) => {
  item.onFocus && item.onFocus(val, item);
};

/** 是否展开详情 */
const openDetail = () => {
  state.showDetail = !state.showDetail;
};

/** 收起详情 */
const closeDetail = () => {
  state.showDetail = false;
};

/**
 * 处理更多选项的提示信息
 * @param params
 */
// const formateQueryParamsToString = (params?: Record<string, any>) => {
//   state.moreFilterConditions = []
//   props.config.moreFilters?.map(item => {
//     if (!item.field || item.type === 'text') return
//     let value = (params && params[item.field]) || '-'
//     if (item.type.indexOf('range') !== -1) {
//       value = value.join && value.join('~')
//     }

//     state.moreFilterConditions.push({
//       label: item.label,
//       filter: item,
//       value,
//       formatter: item.formatter
//     })
//   })
// }
const toggleMore = () => {
  state.moreFilterVisiable = !state.moreFilterVisiable;
};
const resetForm = () => {
  state.queryParams = { ...(props.config.defaultParams || {}) };
  // refForm.value?.resetFields()
  // refSearchMore.value?.resetForm()
};
/** 设置滚动条的宽度，适配带多余按钮项的情况 */
const calcScrollBarWidth = () => {
  let operationWidth = 0;
  if (refOperations.value) {
    const operationChildren = refOperations.value?.children;
    if (operationChildren?.length) {
      for (let index = 0; index < operationChildren.length; index++) {
        operationWidth += operationChildren[index].scrollWidth;
      }
    }
    state.operationStyle['--operationWidth'] = operationWidth + 'px';
    // if (props.config.showDetail) {
    //   state.operationStyle['--operationWidth'] = operationWidth + 30 + 'px'
    // } else {
    // }
  }
  if (refScrollable.value && refWrapper.value) {
    const children = refScrollable.value.children;
    let childWidth = 0;
    if (children?.length) {
      for (let index = 0; index < children.length; index++) {
        childWidth += children[index].scrollWidth;
      }
    }
    const parentWidth = refScrollable.value.clientWidth;
    // console.log('childWidth', childWidth, parentWidth)
    state.showScrollBar = parentWidth < childWidth;
    refreshBarState();
  }
};

watch(
  () => state.showScrollBar,
  () => {
    nextTick().then(() => {
      calcScrollBarWidth();
    });
  }
);

const refreshBarState = () => {
  if (!refScrollable.value) return;
  if (state.showScrollBar) {
    state.scrollLeft =
      refScrollable.value.scrollLeft <= 0 ? 0 : refScrollable.value.scrollLeft;
    state.isBottom =
      refScrollable.value.scrollLeft + refScrollable.value.clientWidth >
      refScrollable.value.scrollWidth - 1;
  } else {
    state.scrollLeft = 0;
    state.isBottom = true;
    refScrollable.value.scrollLeft = 0;
  }
};
const scrollbarClick = (type: 'left' | 'right') => {
  if (!refScrollable.value) return;
  if (type === 'left') refScrollable.value.scrollLeft -= 120;
  if (type === 'right') refScrollable.value.scrollLeft += 120;
  refreshBarState();
};
watch(
  () => state.queryParams,
  () => {
    if (props.config.static) return;
    props.config.filters?.map((item) => {
      item.handleHidden &&
        item.handleHidden(state.queryParams, undefined, item);
    });
    props.config.moreFilters?.map((item) => {
      item.handleHidden &&
        item.handleHidden(state.queryParams, undefined, item);
    });
    props.config.operations?.map((item) => {
      item.handleHidden &&
        item.handleHidden(state.queryParams, undefined, item);
    });
  },
  {
    deep: true,
    immediate: true
  }
);
// 监听参数，更新更多筛选提示文本的内容
// watch(
//   () => state.queryParams,
//   () => {
//     state.moreFilterConditions = props.config.moreFilters?.map(item => {
//       return {
//         label: item.label,
//         value: item.field ? state.queryParams[item.field] || '-' : '-'
//       }
//     }) || []
//   }
// )
const refScrollable = ref<HTMLDivElement>();
const refOperations = ref<HTMLDivElement>();
const refWrapper = ref<HTMLDivElement>();

watch(
  () => [props.config.filters, props.config.moreFilters],
  () => calcScrollBarWidth()
);

// watch(
//   () => props.config.moreFilters,
//   () => calcScrollBarWidth()
// )

onMounted(() => {
  calcScrollBarWidth();
  // setTimeout处理弹出框内的搜索栏缩放按钮
  setTimeout(() => {
    calcScrollBarWidth();
  }, 500);
  refScrollable.value?.addEventListener(
    'wheel',
    (e) => {
      e.preventDefault();
      if (!refScrollable.value) return;
      refScrollable.value.scrollLeft += e.deltaY;
      state.scrollLeft = refScrollable.value.scrollLeft;
      state.isBottom =
        refScrollable.value.scrollLeft + refScrollable.value.clientWidth >
        refScrollable.value.scrollWidth - 1;
    },
    { passive: false }
  );
  window.addEventListener('resize', calcScrollBarWidth);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', calcScrollBarWidth);
});

defineExpose({
  ...toRefs(state),
  toggleMore,
  resetForm
});
</script>
<style lang="scss" scoped>
.search-root {
  width: 100%;
  position: relative;
}

.dark {
  .app-header-divider {
    .right {
      background-color: #434a63;
    }

    .left {
      background-color: #10131e;
    }
  }

  .scroll-bar {
    &.left {
      background: linear-gradient(
        270deg,
        rgba(0, 0, 0, 0) 0%,
        var(--el-bg-color) 50%
      );
    }

    &.right {
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        var(--el-bg-color) 50%
      );
    }
  }
}

.app-header-divider {
  width: 42px;
  min-width: 42px;

  .right,
  .left {
    height: var(--el-component-size);
    width: 1px;
    min-width: 1px;
  }

  .right {
    margin-right: 10px;
    background-color: #f7f8fb;
  }

  .left {
    margin-left: 10px;
    background-color: #e1e3e9;
  }
}

.search-box {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  padding: 0 20px;
}

.filter-wrapper {
  height: var(--el-component-size);
  display: flex;
  padding: 0;
  position: relative;
  max-width: calc(100% - var(--operationWidth));

  .scrollable-items {
    overflow-x: auto;
    -ms-overflow-style: none;
    width: 100%;
    overflow: -moz-scrollbars-none;
    white-space: nowrap;
    height: 100%;
    &::-webkit-scrollbar {
      width: 0 !important;
      height: 0 !important;
    }

    &.show-scroll-bar {
      padding: 0;
      margin-right: 20px;
    }
  }
}

.operation-items {
  padding: 0 0 0 20px;
  display: flex;
  align-items: flex-start;

  .operation-item {
    padding-right: 12px;
  }
}

.scroll-bar {
  width: 30px;
  height: 32px;
  position: absolute;
  cursor: pointer;
  z-index: 999;

  &.left {
    left: 0;
    background: linear-gradient(
      270deg,
      rgba(255, 255, 255, 0) 0%,
      var(--gradientColor, #ffffff) 50%
    );

    &.gradient-hide {
      background: none;
    }

    .bar-wrapper {
      justify-content: flex-start;
    }
  }

  &.right {
    right: 0;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      var(--gradientColor, #ffffff) 50%
    );

    &.gradient-hide {
      background: none;
    }

    .bar-wrapper {
      justify-content: flex-end;
    }
  }

  .bar-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }
}

.scroll-bar-icon {
  cursor: pointer;
}

.form-item,
.app-header-divider {
  display: inline-flex;
  vertical-align: bottom;
  align-items: center;
}

.form-item {
  padding-left: 20px;

  &:first-child {
    padding-left: 0;
  }
}

.form-item-label {
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 21px;
  word-break: keep-all;
  /* identical to box height */

  color: #7c8295;
  margin-right: 8px;
}

.show_detail {
  width: 100%;

  .form-item {
    margin-top: 2px;
    margin-bottom: 10px;
    margin-right: 20px;
    padding: 0;
    width: 100%;
  }
}

.stateSwitch {
  height: 32px;
}

.paddingRight {
  padding-right: 0;
}

.detail {
  position: absolute;
  z-index: 1000;
  top: calc(100% + 20px);
  width: 100%;
}

.displayNone {
  display: none;
}
</style>

<style lang="scss">
.detail {
  .el-card__body {
    padding: var(--el-card-padding);
    grid-template-columns: auto !important;
  }
}
</style>
