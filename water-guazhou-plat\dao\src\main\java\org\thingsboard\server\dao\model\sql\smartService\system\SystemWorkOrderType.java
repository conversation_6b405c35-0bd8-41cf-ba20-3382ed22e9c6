package org.thingsboard.server.dao.model.sql.smartService.system;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 班组主表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-26
 */
@TableName("tb_service_system_work_order_type")
@Data
public class SystemWorkOrderType {
    
    @TableId
    private String id;

    private String pid;

    private String name;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

    private String isDel;

    private transient List<SystemWorkOrderType> children = new ArrayList();
}
