import{C as n,g as r,n as a,p as t,aw as c,bh as l}from"./index-r0dFAfgr.js";const d={name:"TestFlow",data(){return{loading:!0,error:null,connectionStatus:"connecting",authMethod:"Direct Access + Auth Injection",authUrl:"",baseUrl:"http://10.6.5.224:5666/workflow/processDefinition",clientId:"e5cd7e4891bf95d1d19206ce24a7b32e",loadingTimeout:null}},computed:{iframeSrc(){return this.baseUrl}},mounted(){},methods:{onloadFrame(){const e="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI4eTQ4UXdxRDJqcWpESlQySVF1aHB5RHpEVHFUQlFTOCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuawtOWKoeWxgCIsImRlcHRDYXRlZ29yeSI6IiJ9.xhUc_NU6l8K0kSbAJwVr6_MFApV2_E9Q0pvfcpYeNv8";alert("尝试向iframe发送消息:"),this.$refs.workflowFrame.contentWindow.postMessage(e,"*")},async initializeWorkflow(){try{this.loading=!0,this.error=null,this.connectionStatus="connecting",this.loadingTimeout=setTimeout(()=>{this.onIframeLoad()},1e3)}catch(e){console.error("初始化工作流失败:",e),this.error="无法连接到工作流系统，请检查网络连接",this.connectionStatus="error",this.loading=!1}},onIframeLoad(){console.log("iframe 加载完成"),console.log("工作流系统已成功加载:",this.baseUrl),this.loadingTimeout&&(clearTimeout(this.loadingTimeout),this.loadingTimeout=null),this.loading=!1,this.connectionStatus="connected",console.log("工作流系统连接成功"),this.$nextTick(()=>{console.log("DOM已更新，准备注入认证信息..."),this.injectAuthToIframe()})},onIframeError(e){alert(11),console.error("iframe 加载失败",e),this.loadingTimeout&&(clearTimeout(this.loadingTimeout),this.loadingTimeout=null),this.error="iframe 加载失败，可能是跨域问题或目标服务器不可访问",this.connectionStatus="error",this.loading=!1},injectAuthToIframe(){try{console.log("尝试注入认证信息到iframe..."),console.log("$refs对象:",this.$refs);const e=this.$refs.workflowFrame;if(console.log("iframe元素:",e),console.log("iframe类型:",typeof e),!e){console.error("iframe元素未找到！检查ref是否正确设置"),console.log("可用的refs:",Object.keys(this.$refs));return}console.log("iframe src:",e.src),console.log("iframe contentWindow:",e.contentWindow),e.contentWindow?(console.log("iframe contentWindow可访问，尝试注入脚本..."),setTimeout(()=>{try{const o="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzdXYxdjJ2NWN1cmlQTUhiaHJTSnJTbEFWSG5adEFLOCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuawtOWKoeWxgCIsImRlcHRDYXRlZ29yeSI6IiJ9.cFKY3lk1yIhjYuKV2ssT6096vieNBzHNSZMCeXdVJzg";alert("尝试向iframe发送消息:"),e.contentWindow.postMessage(o,"*")}catch(o){console.warn("脚本注入失败 (跨域限制):",o)}},1e3)):console.warn("无法访问iframe contentWindow，可能是跨域限制")}catch(e){console.warn("无法注入认证信息到iframe:",e),console.log("这通常是由于跨域安全限制导致的")}},retryConnection(){this.initializeWorkflow()},async testConnection(){try{console.log("测试连接到工作流系统...");const e=await fetch(this.baseUrl,{method:"HEAD",mode:"no-cors",headers:{authorization:`Bearer ${this.authToken}`,clientid:this.clientId}});console.log("连接测试完成，状态:",e.status),this.$message.success("连接测试完成，请查看控制台日志")}catch(e){console.error("连接测试失败:",e),this.$message.error("连接测试失败: "+e.message)}},getStatusText(){switch(this.connectionStatus){case"connecting":return"正在连接...";case"connected":return"已连接";case"error":return"连接失败";default:return"未知状态"}}}},m={class:"workflow-container"},h={class:"header-info"},f={class:"connection-status"},u={class:"status-text"},I=["src","onload"];function g(e,o,w,J,i,s){return r(),a("div",m,[t("div",h,[o[0]||(o[0]=t("h2",null,"工作流系统",-1)),t("div",f,[t("span",{class:c(["status-indicator",i.connectionStatus])},null,2),t("span",u,l(s.getStatusText()),1)])]),t("iframe",{ref:"workflowFrame",src:s.iframeSrc,onload:s.onloadFrame,class:"embedded-frame",allow:"fullscreen",frameborder:"0"},null,8,I)])}const b=n(d,[["render",g],["__scopeId","data-v-bdb55b75"]]);export{b as default};
