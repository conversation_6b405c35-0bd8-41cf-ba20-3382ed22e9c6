package org.thingsboard.server.dao.sql.waterSource;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.sampling.Sampling;

import java.util.Map;

/**
 * 采样记录Mapper接口
 */
@Mapper
public interface SamplingMapper extends BaseMapper<Sampling> {

    /**
     * 获取采样记录列表
     */
    IPage<Sampling> getList(IPage<Sampling> page, @Param("params") Map<String, Object> params);
}
