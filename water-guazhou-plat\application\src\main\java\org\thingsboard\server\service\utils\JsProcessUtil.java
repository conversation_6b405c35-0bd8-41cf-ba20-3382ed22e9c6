package org.thingsboard.server.service.utils;


import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.logicalFlow.BO.VariableData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JS变量处理Util
 */
public final class JsProcessUtil {

    /**
     * 获取替换原始脚本中的变量Map（中间变量、设备变量等）
     * key: 完整的变量格式串
     * value: 仅为变量ID
     *
     * @param originScript 原始脚本
     * @return 处理后的Map, 格式：{"#{1e99d7b4114a1008c664121185feafc}":"1e99d7b4114a1008c664121185feafc"}
     */
    public static Map<String, Map<String, String>> processOriginScript(String originScript) {
        Map<String, String> betwixtVariableMap = new HashMap<>();
        Map<String, String> deviceVariableMap = new HashMap<>();
        String temp = new String(originScript);
        // 提取中间变量
        getFirstVariable(temp, DataConstants.dataSourceVariableType.BETWIXT, betwixtVariableMap);
        // 提取设备变量
        getFirstVariable(temp, DataConstants.dataSourceVariableType.DEVICE, deviceVariableMap);
        // 返回
        Map<String, Map<String, String>> result = new HashMap<>();
        result.put(DataConstants.dataSourceVariableType.BETWIXT.name(), betwixtVariableMap);
        result.put(DataConstants.dataSourceVariableType.DEVICE.name(), deviceVariableMap);

        return result;
    }

    /**
     * 递归获取变量
     *
     * @param temp          含变量的脚本字符串
     * @param variableType  变量类型
     * @param variableMap   存放变量的map
     */
    public static void getFirstVariable(String temp, DataConstants.dataSourceVariableType variableType, Map<String, String> variableMap) {
        String variable = "";
        String prefix = variableType.getPrefix() + "{";
        if (!temp.contains(prefix)) {
            return;
        }
        // 获取变量的位置
        int prefixIndex = temp.indexOf(prefix);
        temp = temp.substring(prefixIndex + 2);
        int suffixIndex = temp.indexOf("}");
        variable = temp.substring(0, suffixIndex);
        variableMap.put(prefix + variable + "}", variable);

        getFirstVariable(temp, variableType, variableMap);
    }

    /**
     * 根据变量数据集合替换数据到JS脚本
     *
     * @param script            JS脚本
     * @param variableDataList  数据集合
     * @return  替换成正式数据后的JS脚本
     */
    public static String replaceData2Script(String script, List<VariableData> variableDataList) {
        String result = new String(script);
        for (VariableData variableData : variableDataList) {
            if (variableData.getValue() == null) {
                result = result.replace(variableData.getVariable(), "'no data'");
            } else {
                result = result.replace(variableData.getVariable(), variableData.getValue());
            }
        }

        return result;
    }

}
