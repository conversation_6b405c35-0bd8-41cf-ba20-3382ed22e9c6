package org.thingsboard.server.dao.util.imodel.query.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.plan.Plan;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class PlanPageRequest extends AdvancedPageableQueryEntity<Plan, PlanPageRequest> {
    // 关键字
    private String keyWord;

    // 计划名称
    private String name;

    // 执行人ID
    private String executionUserId;

    // 执行人ID
    private String executionDepartmentId;

    // 盘点目标仓库ID
    private String storehouseId;

    // 开始时间起点
    private String startTimeFrom;

    // 开始时间终点
    private String startTimeTo;

    // 结束时间起点
    private String endTimeFrom;

    // 结束时间终点
    private String endTimeTo;

    public Date getStartTimeFrom() {
        return toDate(startTimeFrom);
    }

    public Date getStartTimeTo() {
        return toDate(startTimeTo);
    }

    public Date getEndTimeFrom() {
        return toDate(endTimeFrom);
    }

    public Date getEndTimeTo() {
        return toDate(endTimeTo);
    }
}
