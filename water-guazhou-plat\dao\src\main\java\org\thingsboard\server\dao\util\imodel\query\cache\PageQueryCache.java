package org.thingsboard.server.dao.util.imodel.query.cache;

import org.thingsboard.server.dao.util.imodel.query.CriteriaBuilderWrapper;

import java.util.Collection;

public class PageQueryCache {
    private final QueryFieldInfo<?>[] infos;

    public PageQueryCache(Collection<? extends QueryFieldInfo<?>> infoList) {
        this.infos = infoList.toArray(new QueryFieldInfo[0]);
    }

    public void process(Object entity, CriteriaBuilderWrapper wrapper) {
        for (QueryFieldInfo<?> info : infos) {
            info.process(entity, wrapper);
        }
    }
}
