package org.thingsboard.server.controller.fault;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.controller.workOrder.NewlyWorkOrderController;
import org.thingsboard.server.dao.fault.FaultReportService;
import org.thingsboard.server.dao.model.sql.fault.FaultReport;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.util.imodel.response.model.Model;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-07
 */
@IStarController
@RequestMapping("api/fault/report")
public class FaultReportController extends BaseController {

    @Autowired
    private FaultReportService faultReportService;

    @Autowired
    private NewlyWorkOrderController newlyWorkOrderController;

    @PostMapping
    public IstarResponse save(@RequestBody FaultReport faultReport) throws ThingsboardException {
        faultReport.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        faultReport.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        faultReport.getWorkOrder().setUploadPhone(getCurrentUser().getPhone());
        return IstarResponse.ok(faultReportService.save(faultReport));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return faultReportService.delete(ids);
    }

    @GetMapping("device")
    public IstarResponse getDeviceList(String workOrderId) {
        return IstarResponse.ok(faultReportService.getDeviceList(workOrderId));
    }

    @RequestMapping("workOrder")
    public IstarResponse getByPage(WorkOrderPageRequest request) {
        IPage<WorkOrder> workOrderByPage = newlyWorkOrderController.getByPage(new Model(), request);
        List<WorkOrder> records = workOrderByPage.getRecords();
        // 获取故障项目
        List<JSONObject> jsonObjects = JSONObject.parseArray(JSONObject.toJSONString(records), JSONObject.class);
        for (JSONObject jsonObject : jsonObjects) {
            String id = jsonObject.getString("id");
            FaultReport faultReport = faultReportService.getByWorkOrderId(id);
            if (faultReport != null) {
                jsonObject.put("faultProject", faultReport.getFaultProject());
            }
        }

        return IstarResponse.ok(new PageData<>(workOrderByPage.getTotal(), jsonObjects));
    }


    /**
     * 维修信息
     *
     * @param deviceLabelCode
     * @return
     */
    @GetMapping("statistics/{deviceLabelCode}")
    public IstarResponse statistics(@PathVariable String deviceLabelCode) {
        return IstarResponse.ok(faultReportService.statistics(deviceLabelCode));
    }

    /**
     * 维修记录
     */
    @GetMapping("list/{deviceLabelCode}")
    public IstarResponse getList(@PathVariable String deviceLabelCode, int page, int size) {
        return IstarResponse.ok(faultReportService.getRepairList(deviceLabelCode, page, size));
    }

}
