package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-01
 */
@TableName("tb_supplier_goods")
@Data
public class SupplierGoodsEntity {

    @TableId
    private String id;

    private String mainId;

    private String serialId;

    private transient String name;

    private transient String model;

    private transient String unit;

    private Double num;

    private Double price;

    private Double taxRate;

    private Date createTime;

    private String tenantId;
}
