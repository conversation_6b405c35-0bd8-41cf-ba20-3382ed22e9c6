/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.msg;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponseBody;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.MsgTemplateEntity;
import org.thingsboard.server.dao.util.imodel.query.smartService.SendVisitMsgRequest;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

public interface MsgTemplateService {

    MsgTemplateEntity save(MsgTemplateEntity msgTemplateEntity);

    PageData<MsgTemplateEntity> getList(int page, int size, String configId, String name, String tenantId);

    void delete(List<String> ids);

    void send(JSONObject params) throws ExecutionException, InterruptedException;

    /**
     *
     * @param phone
     * @param param
     * @param templateId
     * @param creator
     * @param custCode
     * @param tenantId
     */
    void sendOne(String phone, Map<String, String> param, String templateId, String creator, String custCode, String tenantId);

    MsgTemplateEntity getById(String id);

    JSONObject visitBack(JSONArray jsonObject);

    JSONObject sendVisitMsg(SendVisitMsgRequest sendVisitMsgRequest);

}
