import{A as u}from"./index-BI1vGJja.js";const L=async(d,l)=>{const o=await u.load({key:"312ea3a03da6d14c60ea71789d1848ae",version:"1.4.15",plugins:["AMap.Autocomplete","AMap.PlaceSearch","AMap.Scale","AMap.MapType","AMap.CircleEditor"],AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}}),s=new o.Map(d,{zoom:11,mapStyle:l.mapStyle,center:l.center||[116.397428,39.90923]});if(l.events)for(const n in l.events)s.on(n,()=>{l.events[n](s)});return l.search,{map:s,setMarker:(n,e)=>{const t=new o.Marker({position:new o.LngLat(...n),...e||{}});return s.add(t),t},setCenter:n=>{n.length===0&&s.setCenter(n)},getCenter:()=>s.getCenter(),AMap:o,setMarkers:(n,e)=>{const t=[];n.forEach((r,p)=>{e&&e.contentHandle&&(e.content=e.contentHandle(r,p),console.log(e.content),delete e.contentHandle),t.push(new o.Marker({position:new o.LngLat(...r),...e||{}}))}),console.log(t);const a=new o.OverlayGroup(t);return s.add(a),a},setPolyline:(n,e)=>{const t=n.map(r=>new o.LngLat(...r)),a=new o.Polyline({path:t,...e||{}});return s.add(a),a},setPolylines:(n,e)=>{const t=[];n.forEach(r=>{const p=r.map(c=>new o.LngLat(...c));t.push(new o.Polyline({path:p,...e||{}}))});const a=new o.OverlayGroup(t);return s.add(a),a},setInfoWindow:(n,e)=>{const t=new o.InfoWindow({anchor:"top",...e||{}});return t.open(s,n),t},setInfoWindows:(n,e)=>{const t=[];return n.forEach((r,p)=>{e&&e.contentHandle&&(e.content=e.contentHandle(r,p),console.log(e.content),delete e.contentHandle);const c=new o.InfoWindow({isCustom:!0,anchor:"top",...e||{}});c.open(s,r),t.push(c)}),new o.OverlayGroup(t)}}};export{L as u};
