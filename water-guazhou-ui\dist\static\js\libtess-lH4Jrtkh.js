import{a4 as r,a3 as e}from"./index-r0dFAfgr.js";import{a as s}from"./pe-B8dP0-Ut.js";const l=128e3;let a=null,n=null;async function m(){return a||(a=c()),a}async function c(){n=await(r("esri-csp-restrictions")?await e(()=>import("./libtess-asm-CdE0p00f.js"),[]).then(t=>t.l):await e(()=>import("./libtess-BLDTbnLO.js"),[]).then(t=>t.l)).load({locateFile:t=>s(`esri/core/libs/libtess/${t}`)})}function p(i,t){const o=Math.max(i.length,l);return n.triangulate(i,t,o)}export{m as i,p as r};
