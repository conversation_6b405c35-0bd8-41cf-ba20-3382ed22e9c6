import{d as M,j as E,u as h,b3 as T,r as V,cF as v,a8 as b,ch as P,bY as z,bu as Y,o as H,cG as J,ay as j,g as r,n as _,p as o,i as s,bh as p,an as c,h as S,a0 as F,G as L,F as t,q as a,cH as G,aw as R,cI as q,cJ as K,cK as O,J as U,cL as $,cM as Q,C as W}from"./index-r0dFAfgr.js";import"./alarmScrollView-DjIanuT8.js";import X from"./index-nTd2CWfi.js";import{_ as Z}from"./ChangeTheme.vue_vue_type_script_setup_true_lang-D3tU7NZH.js";const ss={class:"navbar",mode:"horizontal"},os={class:"left-box"},es={class:"menu-logo unified-theme-bg-color"},ts=["src"],as={key:0,class:"title-box"},ns={class:"title"},ls={id:"fill",class:"fill"},rs={key:0},cs={class:"login-info"},is={class:"login-info margin-l"},us={class:"right-box"},ds={class:"avatar-wrapper"},_s=["src"],ps=M({__name:"Navbar",setup(ms){const i=E(),u=h(),g=T(),n=V({userLog:v,userName:h().name,alarm:0,customColor:!0,currentTime:"",lastLoginDate:"",lastLoginIp:"",bulletinScroll:null,bulletinLast:{title:"",body:""},audioMute:!0}),k=b(()=>u.roles.includes("SYS_ADMIN")&&u.roles.includes("TENANT_SYS")?!1:P().addRouters.some(l=>l.label==="告警管理")),w=()=>{g.push({path:"/HVACPage/realTimeAlarm/index/ah"})},y=()=>{g.push("/app")},I=async()=>{u.LogOut()},x=async()=>{try{const e=await q();n.lastLoginDate=e.data.lastLoginDate,n.lastLoginIp=e.data.lastLoginIp}catch(e){console.log(e.message)}},d=b(()=>!z(["SYS_ADMIN"]));return Y(()=>{n.bulletinScroll&&clearInterval(n.bulletinScroll)}),H(async()=>{try{const e=await J();i.ToggleLogo(e.data)}catch(e){console.log(e.message)}i.SetAppVersion(),!d.value&&x()}),(e,l)=>{const A=K,m=O,C=j("router-link"),D=U,N=$,B=Q;return r(),_("div",{class:R(["navbar-container",{dark:s(i).isDark}])},[o("div",ss,[o("div",os,[o("div",es,[o("img",{src:s(i).logo,alt:""},null,8,ts)]),s(d)?(r(),_("div",as,[o("p",ns,p(s(i).appname),1)])):c("",!0)]),o("div",ls,[s(n).bulletinLast.title===""&&!s(d)?(r(),_("div",rs,[o("span",cs,"上次登录时间："+p(s(n).lastLoginDate),1),o("span",is,"上次登录IP："+p(s(n).lastLoginIp),1)])):c("",!0),c("",!0),s(d)?(r(),S(X,{key:2})):c("",!0)]),o("div",us,[s(F)().usePortal?(r(),_("p",{key:0,class:"return-app",onClick:y},l[0]||(l[0]=[o("i",{class:"el-icon-s-home"},null,-1),L(" 返回门户 ")]))):c("",!0),s(k)?(r(),S(A,{key:1,class:"alarm",value:s(n).alarm},{default:t(()=>[a(s(G),{onClick:w})]),_:1},8,["value"])):c("",!0),a(B,{class:"avatar-container",trigger:"click"},{dropdown:t(()=>[a(N,{class:"user-dropdown"},{default:t(()=>[a(C,{class:"inlineBlock",to:"/accountManage/index"},{default:t(()=>[a(m,null,{default:t(()=>l[1]||(l[1]=[o("span",null,"账户管理",-1)])),_:1}),a(m,null,{default:t(()=>[a(Z)]),_:1})]),_:1}),a(m,{divided:""},{default:t(()=>[a(D,{class:"long-btn",size:"small",text:!0,onClick:I},{default:t(()=>l[2]||(l[2]=[L(" 登出 ")])),_:1})]),_:1})]),_:1})]),default:t(()=>{var f;return[o("div",ds,[o("span",null,p((f=s(u).user)==null?void 0:f.name),1),o("img",{class:"user-avatar",src:s(v)},null,8,_s)])]}),_:1})])])],2)}}}),bs=W(ps,[["__scopeId","data-v-6cdd6d69"]]);export{bs as default};
