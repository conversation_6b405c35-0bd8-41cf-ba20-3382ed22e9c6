import S from"./Timer-DD6yFqCB.js";import g from"./ExitFullScreen-BAhtug-v.js";import x from"./Weather-C85tAM-i.js";import{d as F,c as i,r as I,g as C,n as E,p as e,bh as M,i as d,q as o,C as N}from"./index-r0dFAfgr.js";import"./padStart-BKfyZZDO.js";const O={class:"header__title"},b={class:"text"},w=F({__name:"index",props:{title:{},logo:{}},emits:["fullscreen"],setup(u,{emit:_}){var r,n;const l=u,p=_,s=i(!0),m=I({title:l.title||((n=(r=window.SITE_CONFIG)==null?void 0:r.SMART_DECISION_CONFIG)==null?void 0:n.title)||"智慧水务可视化管控平台"}),f=()=>{s.value=!1},v=()=>{s.value=!0},a=i(!1),h=t=>{p("fullscreen",t),t!==void 0?a.value=!0:a.value=!!document.fullscreenElement};return(t,c)=>(C(),E("div",{class:"header",onMouseover:f,onMouseleave:v},[e("div",O,[e("span",b,M(d(m).title),1)]),c[0]||(c[0]=e("div",{class:"header__footer"},[e("div",{class:"left"}),e("div",{class:"right"})],-1)),o(x,{class:"header__weather",logo:l.logo},null,8,["logo"]),o(S,{class:"header__timer"}),o(g,{collapsed:d(s),onFullscreen:h},null,8,["collapsed"])],32))}}),V=N(w,[["__scopeId","data-v-be77b75e"]]);export{V as default};
