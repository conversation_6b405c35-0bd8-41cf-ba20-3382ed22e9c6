<template>
  <div>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <PipeDetail
      ref="refDetail"
      :tabs="state.tabs"
      :telport="telport"
      @refreshed="state.currentOperate = 'viewingdetail'"
      @refreshing="state.currentOperate = 'detailing'"
      @close="state.currentOperate = 'analysed'"
      @rowdblclick="handleLocate"
    ></PipeDetail>
  </div>
</template>
<script lang="ts" setup>
import Extent from '@arcgis/core/geometry/Extent.js'
import { IFormIns } from '@/components/type'
import {
  setMapCursor,
  getGraphicLayer,
  getSubLayerIds,
  initIdentifyParams,
  excuteIdentify,
  setSymbol,
  submitConnectAnalysGPJob,
  getPipeMapLayerMinIndex,
  initQueryParams,
  excuteQueryForIds
} from '@/utils/MapHelper'
import { SLMessage } from '@/utils/Message'
import { queryLayerClassName } from '@/api/mapservice'
import PipeDetail from '../common/PipeDetail.vue'

const props = defineProps<{
  view?: __esri.MapView
  telport?: string
}>()
const state = reactive<{
  currentOperate:
    | 'picking'
    | 'picked'
    | 'analysing'
    | 'analysed'
    | 'detailing'
    | 'viewingdetail'
    | ''
  tabs: IPipeDetailTab[]
}>({
  currentOperate: '',
  tabs: []
})
const staticState: {
  resultLayer?: __esri.MapImageLayer
  graphicLayer?: __esri.GraphicsLayer
  mapClick?: any
  identifyResult?: any
  jobid?: any
  resultSummary?: any
} = {}
const refDetail = ref<InstanceType<typeof PipeDetail>>()
const refForm = ref<IFormIns>()
const TableConfig_Pipe = reactive<ITable>({
  columns: [
    { label: '管线类型', prop: 'layerName' },
    {
      label: '管线编号',
      prop: 'value',
      formatter: row => row.attributes?.OBJECTID
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
})
const TableConfig_AnalysResult = reactive<IAttrTable>({
  data: [],
  columns: []
})

const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  gutter: 12,
  group: [
    {
      fieldset: {
        desc: '选取管线'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'warning',
              styles: {
                width: '100%'
              },
              text: () => (state.currentOperate === 'picking'
                ? '正在选取管线'
                : '点击选择管线'),
              loading: () => state.currentOperate === 'picking',
              disabled: () => state.currentOperate === 'picking'
                || state.currentOperate === 'analysing'
                || state.currentOperate === 'viewingdetail',
              click: () => startPick()
            }
          ]
        },
        {
          type: 'table',
          label: '所选管线数据概览',
          style: {
            height: '80px'
          },
          config: TableConfig_Pipe
        },
        {
          type: 'checkbox',
          field: 'usePreventLayer',
          options: [{ label: '使用障碍图层', value: 'usePreventLayer' }]
        }
      ]
    },
    {
      fieldset: {
        desc: '执行分析'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              text: () => (state.currentOperate === 'analysing' ? '正在分析' : '开始分析'),
              loading: () => state.currentOperate === 'analysing',
              disabled: () => state.currentOperate === 'analysing'
                || state.currentOperate === 'detailing'
                || state.currentOperate === 'picking'
                || !TableConfig_Pipe.dataList.length,
              click: () => startAnalys()
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '分析结果'
      },
      fields: [
        {
          type: 'checkbox',
          label: '分析结果概览',
          field: 'viewInMap',
          options: [{ label: '地图显示', value: 'viewInMap' }],
          onChange: val => {
            staticState.resultLayer
              && (staticState.resultLayer.visible = !!val.length)
          }
        },
        {
          type: 'attr-table',
          config: TableConfig_AnalysResult
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginTop: '20px',
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              text: () => (state.currentOperate === 'detailing'
                ? '正在查询'
                : '查看详细结果'),
              click: () => viewDetail(),
              loading: () => state.currentOperate === 'detailing',
              disabled: () => state.currentOperate !== 'analysed'
                && state.currentOperate !== 'viewingdetail',
              styles: {
                width: '100%'
              }
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'danger',
              text: '清除所有',
              disabled: () => state.currentOperate === 'analysing'
                || state.currentOperate === 'detailing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {
    viewInMap: ['viewInMap'],
    usePreventLayer: ['usePreventLayer']
  }
})

const startPick = () => {
  if (props.view) {
    setMapCursor('crosshair')
    state.currentOperate = 'picking'
    staticState.graphicLayer = getGraphicLayer(props.view, {
      id: 'connect-analys',
      title: '连通性分析标注'
    })
    staticState.mapClick = props.view?.on('click', async e => {
      staticState.graphicLayer?.removeAll()
      setMapCursor('')
      try {
        await doIdentify(e)
      } catch (error) {
        SLMessage.error('拾取失败')
      }

      staticState.mapClick?.remove && staticState.mapClick.remove()
      staticState.mapClick = undefined
      state.currentOperate = 'picked'
    })
  }
}
const doIdentify = async (e: any) => {
  if (!props.view) return
  const res = await excuteIdentify(
    window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
    initIdentifyParams({
      tolerance: 3,
      geometry: e.mapPoint,
      mapExtent: props.view.extent,
      width: props.view.width,
      layerIds: getSubLayerIds(props.view)
    })
  )
  const result = res.results?.filter(
    item => item.geometryType !== 'esriGeometryPoint'
  )[0]
  if (!result) {
    SLMessage.warning('没有查询到管线')
    return (state.currentOperate = 'picked')
  }
  const data = (result && [result]) || []
  TableConfig_Pipe.dataList = data.map(item => {
    return {
      attributes: item.feature.attributes,
      layerId: item.layerId,
      layerName: item.layerName
    }
  })
  staticState.identifyResult = result
  if (result) {
    const graphic = result.feature
    graphic && (graphic.symbol = setSymbol('polyline'))
    staticState.graphicLayer?.add(graphic)
  }
  state.currentOperate = 'picked'
}
const startAnalys = async () => {
  state.currentOperate = 'analysing'
  staticState.resultLayer && props.view?.map.remove(staticState.resultLayer)
  try {
    const res = await queryLayerClassName(staticState.identifyResult.layerId)
    const datas = res.data?.result?.rows
    const layerdbName = datas?.length && datas[0].layerdbname
    const pipeoid = staticState.identifyResult?.feature.attributes['OBJECTID']
    const jobinfo = await submitConnectAnalysGPJob(
      layerdbName,
      pipeoid,
      !!refForm.value?.dataForm['usePreventLayer']?.length
    )
    await jobinfo.waitForJobCompletion({
      statusCallback: res => {
        staticState.jobid = res.jobId
      }
    })
    if (jobinfo.jobStatus === 'job-succeeded') {
      staticState.jobid = jobinfo.jobId
      const layer = await jobinfo.fetchResultMapImageLayer(jobinfo.jobId)
      staticState.resultLayer = layer
      staticState.resultLayer.title = '连通性分析结果'
      const pipeMapIndex = getPipeMapLayerMinIndex(props.view)
      props.view?.map.add(staticState.resultLayer, pipeMapIndex)

      const res = await jobinfo.fetchResultData('summary')

      const value: any = res.value
      if (value?.code !== 10000) {
        SLMessage.error(value.error)
      } else {
        staticState.resultSummary = value?.result?.summary
        showAnalyzsSummary(value?.result?.summary)
      }
      const tabs = staticState.resultSummary.layersummary.map(item => {
        return {
          label: item.layername,
          name: item.layername
        }
      })
      await setTabOids(tabs, 0)
      state.tabs = tabs
    } else if (jobinfo.jobStatus === 'job-cancelled') {
      SLMessage.info('已取消分析')
    } else if (jobinfo.jobStatus === 'job-cancelling') {
      SLMessage.info('任务正在取消')
    } else if (jobinfo.jobStatus === 'job-failed') {
      SLMessage.info('分析失败，请联系管理员')
    }
  } catch (error) {
    SLMessage.error('系统错误')
    state.currentOperate = 'picked'
  }
  state.currentOperate = 'analysed'
}

const showAnalyzsSummary = (summary: any) => {
  if (!summary?.layersummary?.length) return
  if (summary?.layersummary?.length) {
    const summaryTData: any = {}
    const columns: IAttrTableRow[][] = []
    summary.layersummary.forEach(value => {
      if (value.geometrytype === 'esriGeometryPoint') {
        summaryTData[value.layerdbname] = value.count + '个'
        columns.push([{ label: value.layername, prop: value.layerdbname }])
      } else {
        summaryTData[value.layerdbname] = (value.length?.toFixed(2) || 0) + '米'
        columns.push([{ label: value.layername, prop: value.layerdbname }])
      }
    })
    TableConfig_AnalysResult.data = summaryTData
    TableConfig_AnalysResult.columns = columns
  }
  let xmin: number = summary.xmin || props.view?.extent?.xmin
  let xmax: number = summary.xmax || props.view?.extent?.xmax
  let ymin: number = summary.ymin || props.view?.extent?.ymin
  let ymax: number = summary.ymax || props.view?.extent?.ymax
  const width = xmax - xmin
  const height = ymax - ymin
  xmin -= width / 2
  xmax += width / 2
  ymin -= height / 2
  ymax += height / 2
  props.view?.goTo(
    new Extent({
      xmin,
      ymin,
      xmax,
      ymax,
      spatialReference: props.view?.spatialReference
    })
  )
}
const viewDetail = async () => {
  try {
    if (!state.tabs.length) {
      SLMessage.warning('暂无详细信息')
      return
    }
    refDetail.value?.openDialog()
  } catch (error) {
    SLMessage.error('查询失败')
    console.log(error)
  }
}
const setTabOids = async (tabs, index) => {
  if (index < tabs.length) {
    const tab = tabs[index]
    tab.data = await getTempOids(tab.name, 0)
    index < tabs.length - 1 && (await setTabOids(tabs, ++index))
  }
}
const getTempOids = async (tab: string, layerIndex: number) => {
  try {
    let alloids = await excuteQueryForIds(
      (staticState.resultLayer?.url || '') + '/' + layerIndex,
      initQueryParams({
        where: "layername='" + tab + "'",
        orderByFields: ['OBJECTID asc'],
        returnGeometry: false
      })
    )
    if (alloids === null) {
      alloids = await getTempOids(tab, ++layerIndex)
    }
    return alloids
  } catch (error) {
    return []
  }
}
const handleLocate = async () => {
  props.view && refDetail.value?.extentTo(props.view)
}
const clear = () => {
  staticState.graphicLayer?.removeAll()
  staticState.graphicLayer && props.view?.map.remove(staticState.graphicLayer)
  staticState.resultLayer && props.view?.map.remove(staticState.resultLayer)
  staticState.mapClick?.remove && staticState.mapClick.remove()
  staticState.mapClick = undefined
  state.currentOperate = ''
  TableConfig_Pipe.dataList = []
  TableConfig_AnalysResult.data = []
  TableConfig_AnalysResult.columns = []
}
onBeforeUnmount(() => {
  clear()
})
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;
  .el-table__empty-text {
    line-height: 40px;
  }
}
</style>
