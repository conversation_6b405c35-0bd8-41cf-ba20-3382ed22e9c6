package org.thingsboard.server.dao.sql.groundwater;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.groundwater.GroundwaterRecharge;

import java.util.Date;
import java.util.List;

/**
 * 地下水涵养水位 Mapper
 */
@Mapper
public interface GroundwaterRechargeMapper extends BaseMapper<GroundwaterRecharge> {
    
    /**
     * 分页查询涵养水位数据
     * @param page 分页参数
     * @param areaId 区域ID
     * @param tenantId 租户ID
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页数据
     */
    List<GroundwaterRecharge> findList(Page<GroundwaterRecharge> page,
                                     @Param("areaId") String areaId,
                                     @Param("tenantId") String tenantId,
                                     @Param("status") Integer status,
                                     @Param("startTime") Date startTime,
                                     @Param("endTime") Date endTime);
    
    /**
     * 查询涵养水位数据总数
     * @param areaId 区域ID
     * @param tenantId 租户ID
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 总数
     */
    Integer findListCount(@Param("areaId") String areaId,
                         @Param("tenantId") String tenantId,
                         @Param("status") Integer status,
                         @Param("startTime") Date startTime,
                         @Param("endTime") Date endTime);
    
    /**
     * 获取指定区域最新的涵养水位数据
     * @param areaId 区域ID
     * @return 涵养水位数据
     */
    GroundwaterRecharge findLatestByAreaId(@Param("areaId") String areaId);
} 