<template>
  <RightDrawerMap
    ref="refMap"
    :title="'水源监测总览'"
    :windows="state.windows"
    :hide-detail-close="true"
    :hide-layer-list="true"
    :right-drawer-width="600"
    @map-loaded="onMapLoaded"
  >
    <div class="content">
      <FieldSet type="simple" title="水源监测"></FieldSet>
      <div class="right-box">
        <FormTable
          ref="refCard"
          class="table-box"
          :config="cardTableConfig"
        ></FormTable>
      </div>
      <el-divider></el-divider>
      <FieldSet
        type="simple"
        :title="(state.curRow?.name || '') + '24小时运行曲线'"
      ></FieldSet>
      <div v-loading="loading" class="right-box bottom">
        <VChart
          ref="refChart1"
          :option="state.lineOption"
          :theme="useAppStore().isDark ? 'blackBackground' : 'whiteBackground'"
        />
      </div>
    </div>
    <template #detail-header>
      <span class="title">水源总览</span>
    </template>
    <template #detail-default>
      <div v-loading="loading" class="bottom-content">
        <div class="bottom-box">
          <div class="water-supply-card">
            <div class="card-header">
              <i class="water-icon el-icon-water-cup"></i>
              <span class="title">今日供水量</span>
            </div>
            <div class="card-value">
              <span class="number">{{ state.pieOption1 }}</span>
              <span class="unit">m³</span>
            </div>
            <VChart
              ref="refChart2"
              class="bottom-chart-box"
              :theme="appStore.isDark ? 'blackBackground' : 'whiteBackground'"
              :option="state.barOption1"
              autoresize
              :style="{ backgroundColor: 'transparent' }"
            />
          </div>
        </div>
        <div class="bottom-box">
          <div class="water-supply-card">
            <div class="card-header">
              <i class="water-icon el-icon-data-analysis"></i>
              <span class="title">昨日供水量</span>
            </div>
            <div class="card-value">
              <span class="number">{{ state.pieOption2 }}</span>
              <span class="unit">m³</span>
            </div>
            <VChart
              ref="refChart3"
              class="bottom-chart-box"
              :theme="appStore.isDark ? 'blackBackground' : 'whiteBackground'"
              :option="state.barOption2"
              autoresize
              :style="{ backgroundColor: 'transparent' }"
            />
          </div>
        </div>
        <div ref="refBottom" class="bottom-box">
          <div class="water-supply-card">
            <div class="card-header">
              <i class="water-icon el-icon-data-line"></i>
              <span class="title">本月供水量</span>
            </div>
            <div class="card-value">
              <span class="number">{{ state.pieOption3 }}</span>
              <span class="unit">m³</span>
            </div>
            <VChart
              ref="refChart4"
              class="bottom-chart-box"
              :theme="appStore.isDark ? 'blackBackground' : 'whiteBackground'"
              :option="state.barOption3"
              autoresize
              :style="{ backgroundColor: 'transparent' }"
            />
          </div>
        </div>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic.js';
import Point from '@arcgis/core/geometry/Point.js';
import TextSymbol from '@arcgis/core/symbols/TextSymbol.js';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol.js';
import elementResizeDetectorMaker from 'element-resize-detector';
import { lineOption, barOption } from '../../echartsData/echart';
import useStation from '@/hooks/station/useStation';
import { GetStationRealTimeDetail } from '@/api/shuiwureports/zhandian';
import { IECharts } from '@/plugins/echart';
import {
  getWaterSupplyInfo,
  getWaterSupplyDetail
} from '@/api/headwatersManage/headwaterMonitoring';
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue';
import { bindViewClick, gotoAndHighLight } from '@/utils/MapHelper';
import { useAppStore } from '@/store';
import { getStationImageUrl } from '@/utils/URLHelper';

const appStore = useAppStore();
const erd = elementResizeDetectorMaker();
const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const { getAllStationOption } = useStation();

const refBottom = ref<any>();
const refChart2 = ref<IECharts>();
const refChart1 = ref<IECharts>();
const refChart3 = ref<IECharts>();
const refChart4 = ref<IECharts>();
const loading = ref<boolean>(false);

let tableData = reactive<any[]>([]);
const state = reactive<{
  colors: string[];
  barOption1: any;
  pieOption1: any;
  barOption2: any;
  pieOption2: any;
  barOption3: any;
  pieOption3: any;
  lineOption: any;
  curRow?: any;
  data: any;
  stationLocation: any[];
  windows: IArcMarkerProps[];
}>({
  colors: ['#43B53099', '#318DFF99', '#FFB800D4'],
  pieOption1: null,
  barOption1: null,
  pieOption2: null,
  barOption2: null,
  pieOption3: null,
  barOption3: null,
  lineOption: null,
  curRow: {},
  data: null,
  stationLocation: [],
  windows: []
});

const staticState: {
  view?: __esri.MapView;
} = {};
const handleMarkClick = async (stationId?: string) => {
  const tableRow = cardTableConfig.dataList.find(
    (item) => item.stationId === stationId
  );
  state.curRow = tableRow;
  resetPanel(tableRow);
  let graphic: __esri.Graphic | undefined;
  if (!stationId) {
    graphic = staticState.view?.graphics?.getItemAt(0);
  } else {
    graphic = staticState.view?.graphics.find(
      (item) => item.attributes?.row?.id === stationId
    );
    if (graphic) {
      await gotoAndHighLight(staticState.view, graphic, {
        zoom: 15,
        avoidHighlight: true
      });
    }
  }
  if (!graphic) return;

  const attributes = graphic.attributes?.row || {};
  const res = await GetStationRealTimeDetail(attributes.id);
  const values =
    res.data?.map((item) => {
      item.label = item.propertyName;
      item.value;

      return item;
    }) || [];
  const point = graphic?.geometry as __esri.Point;
  state.windows.length = 0;
  state.windows.push({
    visible: false,
    x: point.x,
    y: point.y,
    offsetY: -30,
    title: attributes.name,
    attributes: {
      values,
      id: attributes.id
    }
  });
  await nextTick();
  refMap.value?.openPop(attributes.id);
};

const cardTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  columns: [
    { prop: 'name', label: '名称', minWidth: 80 },
    { prop: 'todayWaterSupply', label: '今日供水量', minWidth: 95 },
    { prop: 'yesterdayWaterSupply', label: '昨日供水量', minWidth: 95 },
    { prop: 'monthWaterSupply', label: '本月供水量', minWidth: 95 }
  ],
  highlightCurrentRow: true,
  currentRowKey: 'stationId',
  handleRowClick: async (row: any) => {
    state.curRow = row;
    const g = staticState.view?.graphics.find(
      (item) => item.attributes?.row?.id === row.stationId
    );
    g &&
      (await gotoAndHighLight(staticState.view, g, {
        zoom: 15,
        avoidHighlight: true
      }));
    handleMarkClick(row.stationId);
  },
  pagination: {
    layout: 'total,  prev, pager, next, jumper',
    refreshData: ({ page, size }) => {
      cardTableConfig.pagination.page = page;
      cardTableConfig.pagination.limit = size;
      cardTableConfig.dataList = tableData.slice(
        (page - 1) * size,
        page * size
      );
    }
  }
});

// refreshData
// 刷新列表 模拟数据
const refreshData = () => {
  getWaterSupplyInfo().then((res) => {
    tableData = res.data?.data;
    cardTableConfig.dataList = tableData?.slice(0, 20);
    cardTableConfig.pagination.total = tableData.length;
    cardTableConfig.currentRow = tableData[0];
    cardTableConfig.loading = false;
    handleMarkClick(tableData[0]?.stationId);
  });
  addMarks();
};
//  获取监控数据已经加载显示图表
const resetPanel = async (row?: any) => {
  if (!row) return;
  // loading.value = true
  try {
    echartClear();
    erd.listenTo(refBottom.value, async () => {
      cardTableConfig.currentRow = row;
      const res = await getWaterSupplyDetail(row.stationId || row.id);
      const result = res.data.data;
      nextTick(() => {
        const datax1 = result.todayTotalFlowDataList?.map((item) => {
          return item.ts.substring(8, 20);
        });
        const seriesData1 = result.todayTotalFlowDataList?.map(
          (item) => item.value
        );
        state.barOption1 = barOption(
          '',
          state.colors[0],
          datax1,
          seriesData1,
          50,
          10
        );
        // 设置背景透明
        if (state.barOption1) {
          state.barOption1.backgroundColor = 'transparent';
        }
        
        state.pieOption1 = row.todayWaterSupply;

        const datax2 = result.yesterdayTotalFlowDataList?.map((item) => {
          return item.ts.substring(8, 20);
        });
        const seriesData2 = result.yesterdayTotalFlowDataList?.map(
          (item) => item.value
        );
        state.pieOption2 = row.yesterdayWaterSupply;
        state.barOption2 = barOption(
          '',
          state.colors[1],
          datax2,
          seriesData2,
          50,
          10
        );
        // 设置背景透明
        if (state.barOption2) {
          state.barOption2.backgroundColor = 'transparent';
        }

        const datax3 = result.monthTotalFlowDataList?.map((item) => {
          return item.ts.substring(8, 20);
        });
        const seriesData3 = result.monthTotalFlowDataList?.map(
          (item) => item.value
        );
        state.pieOption3 = row.monthWaterSupply;
        state.barOption3 = barOption(
          '',
          state.colors[2],
          datax3,
          seriesData3,
          50,
          10
        );
        // 设置背景透明
        if (state.barOption3) {
          state.barOption3.backgroundColor = 'transparent';
        }

        const lineDatax = result.todayTotalFlowDataList.map((item) => item.ts);
        const pressureData = result.pressure?.map((item) => item.value);
        const InstantaneousDta = result.Instantaneous_flow?.map(
          (item) => item.value
        );
        state.lineOption = lineOption(
          lineDatax,
          pressureData,
          InstantaneousDta,
          50,
          50
        );
        // 设置背景透明
        if (state.lineOption) {
          state.lineOption.backgroundColor = 'transparent';
        }
        state.lineOption.yAxis[0].name = '压力(MPa)';
        state.lineOption.yAxis[1].name = '瞬时流量(m³/h)';
        echartResize();
      });
    });
  } catch (error) {
    //
  }

  loading.value = false;
};

const echartClear = () => {
  refChart1.value?.clear();
  refChart2.value?.clear();
  refChart3.value?.clear();
  refChart4.value?.clear();
};

const echartResize = () => {
  refChart1.value?.resize();
  refChart2.value?.resize();
  refChart3.value?.resize();
  refChart4.value?.resize();
};

// 添加地图图标
const addMarks = async () => {
  const res = await getAllStationOption('水源地');
  staticState.view?.graphics?.removeAll();
  res.map((data) => {
    const item = data.data;
    const location = item?.location?.split(',');
    const point = new Point({
      longitude: location?.[0],
      latitude: location?.[1],
      spatialReference: staticState.view?.spatialReference
    });
    const markG = new Graphic({
      geometry: point,
      symbol: new PictureMarkerSymbol({
        width: 25,
        height: 30,
        yoffset: 15,
        url: getStationImageUrl('水源地.png')
      }),
      attributes: {
        row: item
      }
    });
    const waterData = (res?.find((water) => water.id === item.id) as any) || {};
    const markText = new Graphic({
      geometry: point,
      symbol: new TextSymbol({
        yoffset: -15,
        color: '#00ff33',
        text: waterData ? waterData.label : '-'
      })
    });

    staticState.view?.graphics?.addMany([markG, markText]);
  });
  handleMarkClick();
};
const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view;
  refMap.value?.toggleCustomDetail(true);
  refreshData();
  bindViewClick(staticState.view, (res) => {
    const result = res.results?.[0];
    if (!result) return;
    if (result.type === 'graphic') {
      const row = result.graphic?.attributes?.row;
      handleMarkClick(row?.id);
    }
  });
};
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
}

.bottom-content {
  height: 100%;
  display: flex;
  justify-content: space-around;
  padding: 0px;
  gap: 12px;
  overflow: hidden;

  .bottom-box {
    width: 33.33%;
    height: 100%;
    padding-bottom: 10px;
    
    .water-supply-card {
      height: 100%;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
      // padding: 15px;
      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;
      border: 1px solid rgba(0, 128, 255, 0.1);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, #0080FF, #00BFFF);
      }
      
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        border-color: rgba(0, 128, 255, 0.3);
      }
      
      .card-header {
        display: flex;
        align-items: center;
        margin-top: 15px;
        margin-left: 10px;
        margin-bottom: 4px;
        .water-icon {
          font-size: 20px;
          color: #0080FF;
          margin-right: 8px;
        }
        
        .title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }
      
      .card-value {
        display: flex;
        align-items: baseline;
        // margin-bottom: 15px;
        margin-left: 15px;
        .number {
          font-size: 32px;
          font-weight: 600;
          color: #0080FF;
          font-family: "DIN Alternate", "Arial", sans-serif;
        }
        
        .unit {
          font-size: 14px;
          color: #666;
          margin-left: 5px;
        }
      }
      
      .bottom-chart-box {
        flex: 1;
        width: 100%;
        min-height: 180px;
        max-height: calc(100% - 80px);
        background-color: transparent !important;
        margin-bottom: -10px;
        
        :deep(.echarts) {
          width: 100% !important;
          height: 100% !important;
          background-color: transparent !important;
          
          canvas {
            background-color: transparent !important;
          }
        }
      }
    }
  }
}

// 暗色模式样式
:deep(.dark) {
  .water-supply-card {
    background: rgba(30, 38, 55, 0.6);
    border-color: rgba(0, 128, 255, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
      border-color: rgba(0, 128, 255, 0.4);
    }
    
    .card-header {
      .title {
        color: #e0e0e0;
      }
    }
    
    .card-value {
      .number {
        color: #00BFFF;
      }
      
      .unit {
        color: #a0a0a0;
      }
    }
  }
}

.right-box {
  width: 100%;
  min-height: 30%;
  max-height: 40%;
  .table-box {
    height: 90%;
  }
}

.bottom {
  height: calc(42% - 20px);
}

.today-lightning-total,
.today-lightning-perton {
  display: flex;

  .count {
    font-size: 18px;
    color: #ffad51;
    font-weight: 500;
  }

  .unit {
    font-size: 12px;
  }

  .yesterday {
    margin-left: auto;
    font-size: 12px;
    padding-top: 10px;
  }
}

.today-lightning-perton {
  .count {
    color: #ff51ab;
  }
}

.header-slot {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    word-break: keep-all;
  }
}
</style>
<style lang="scss">
.panel {
  &.map-right {
    right: 0;
  }

  &.map-right {
    top: 0;
    width: 400px;
    height: 100%;

    .content {
      width: 100%;
      height: calc(100% - 15px);
    }
  }

  &.map-bottom {
    left: 0;
  }

  &.map-bottom {
    bottom: 0;
    width: calc(100% - 400px);
    height: 300px;

    .content {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-around;
    }
  }
}

// 全局样式，确保所有echarts实例背景透明
.echarts-for-vue, .echarts {
  background-color: transparent !important;
  
  canvas {
    background-color: transparent !important;
  }
}

// 修改图表主题，确保背景透明
.blackBackground, .whiteBackground {
  background-color: transparent !important;
}
</style>
