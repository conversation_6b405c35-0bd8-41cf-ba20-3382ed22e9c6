"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[2710],{16453:(e,r,t)=>{t.d(r,{R:()=>v,w:()=>w});var i=t(43697),n=t(15923),s=t(70586),a=t(41103),o=t(22974),l=t(31263);class c{constructor(){this._propertyOriginMap=new Map,this._originStores=new Array(l.kk),this._values=new Map,this.multipleOriginsSupported=!0}clone(e){const r=new c,t=this._originStores[l.s3.DEFAULTS];t&&t.forEach(((e,t)=>{r.set(t,(0,o.d9)(e),l.s3.DEFAULTS)}));for(let t=l.s3.SERVICE;t<l.kk;t++){const i=this._originStores[t];i&&i.forEach(((i,n)=>{e&&e.has(n)||r.set(n,(0,o.d9)(i),t)}))}return r}get(e,r){const t=void 0===r?this._values:this._originStores[r];return t?t.get(e):void 0}keys(e){const r=null==e?this._values:this._originStores[e];return r?[...r.keys()]:[]}set(e,r,t=l.s3.USER){let i=this._originStores[t];if(i||(i=new Map,this._originStores[t]=i),i.set(e,r),!this._values.has(e)||(0,s.j0)(this._propertyOriginMap.get(e))<=t){const i=this._values.get(e);return this._values.set(e,r),this._propertyOriginMap.set(e,t),i!==r}return!1}delete(e,r=l.s3.USER){const t=this._originStores[r];if(!t)return;const i=t.get(e);if(t.delete(e),this._values.has(e)&&this._propertyOriginMap.get(e)===r){this._values.delete(e);for(let t=r-1;t>=0;t--){const r=this._originStores[t];if(r&&r.has(e)){this._values.set(e,r.get(e)),this._propertyOriginMap.set(e,t);break}}}return i}has(e,r){const t=void 0===r?this._values:this._originStores[r];return!!t&&t.has(e)}revert(e,r){for(;r>0&&!this.has(e,r);)--r;const t=this._originStores[r],i=t&&t.get(e),n=this._values.get(e);return this._values.set(e,i),this._propertyOriginMap.set(e,r),n!==i}originOf(e){return this._propertyOriginMap.get(e)||l.s3.DEFAULTS}forEach(e){this._values.forEach(e)}}var p=t(50549),u=t(1153),y=t(52011);const d=e=>{let r=class extends e{constructor(...e){super(...e);const r=(0,s.j0)((0,u.vw)(this)),t=r.store,i=new c;r.store=i,(0,a.M)(r,t,i)}read(e,r){(0,p.i)(this,e,r)}getAtOrigin(e,r){const t=m(this),i=(0,l.M9)(r);if("string"==typeof e)return t.get(e,i);const n={};return e.forEach((e=>{n[e]=t.get(e,i)})),n}originOf(e){return(0,l.x3)(this.originIdOf(e))}originIdOf(e){return m(this).originOf(e)}revert(e,r){const t=m(this),i=(0,l.M9)(r),n=(0,u.vw)(this);let s;s="string"==typeof e?"*"===e?t.keys(i):[e]:e,s.forEach((e=>{n.invalidate(e),t.revert(e,i),n.commit(e)}))}};return r=(0,i._)([(0,y.j)("esri.core.ReadOnlyMultiOriginJSONSupport")],r),r};function m(e){return(0,u.vw)(e).store}let f=class extends(d(n.Z)){};f=(0,i._)([(0,y.j)("esri.core.ReadOnlyMultiOriginJSONSupport")],f);var g=t(76169);const S=e=>{let r=class extends e{constructor(...e){super(...e)}clear(e,r="user"){return h(this).delete(e,(0,l.M9)(r))}write(e={},r){return(0,g.c)(this,e=e||{},r),e}setAtOrigin(e,r,t){(0,u.vw)(this).setAtOrigin(e,r,(0,l.M9)(t))}removeOrigin(e){const r=h(this),t=(0,l.M9)(e),i=r.keys(t);for(const e of i)r.originOf(e)===t&&r.set(e,r.get(e,t),l.s3.USER)}updateOrigin(e,r){const t=h(this),i=(0,l.M9)(r),n=this.get(e);for(let r=i+1;r<l.kk;++r)t.delete(e,r);t.set(e,n,i)}toJSON(e){return this.write({},e)}};return r=(0,i._)([(0,y.j)("esri.core.WriteableMultiOriginJSONSupport")],r),r.prototype.toJSON.isDefaultToJSON=!0,r};function h(e){return(0,u.vw)(e).store}const v=e=>{let r=class extends(S(d(e))){constructor(...e){super(...e)}};return r=(0,i._)([(0,y.j)("esri.core.MultiOriginJSONSupport")],r),r};let w=class extends(v(n.Z)){};w=(0,i._)([(0,y.j)("esri.core.MultiOriginJSONSupport")],w)},38009:(e,r,t)=>{t.d(r,{q:()=>d});var i=t(43697),n=t(20102),s=t(17452),a=t(5600),o=(t(75215),t(67676),t(52011)),l=t(30556),c=t(50549),p=t(76169);const u={"web-scene/operational-layers":{ArcGISDimensionLayer:!0,ArcGISFeatureLayer:!0,ArcGISImageServiceLayer:!0,ArcGISMapServiceLayer:!0,ArcGISSceneServiceLayer:!0,ArcGISTiledElevationServiceLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,BuildingSceneLayer:!0,GroupLayer:!0,IntegratedMeshLayer:!0,OGCFeatureLayer:!0,PointCloudLayer:!0,WebTiledLayer:!0,CSV:!0,GeoJSON:!0,VectorTileLayer:!0,WFS:!0,WMS:!0,KML:!0,RasterDataLayer:!0,Voxel:!0,LineOfSightLayer:!0},"web-scene/basemap":{ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,WebTiledLayer:!0,OpenStreetMap:!0,VectorTileLayer:!0,ArcGISImageServiceLayer:!0,WMS:!0,ArcGISMapServiceLayer:!0,ArcGISSceneServiceLayer:!0},"web-scene/ground":{ArcGISTiledElevationServiceLayer:!0,RasterDataElevationLayer:!0},"web-map/operational-layers":{ArcGISAnnotationLayer:!0,ArcGISDimensionLayer:!0,ArcGISFeatureLayer:!0,ArcGISImageServiceLayer:!0,ArcGISImageServiceVectorLayer:!0,ArcGISMapServiceLayer:!0,ArcGISStreamLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,BingMapsAerial:!0,BingMapsHybrid:!0,BingMapsRoad:!0,CSV:!0,GeoRSS:!0,GeoJSON:!0,GroupLayer:!0,KML:!0,MediaLayer:!0,OGCFeatureLayer:!0,OrientedImageryLayer:!0,SubtypeGroupLayer:!0,VectorTileLayer:!0,WFS:!0,WMS:!0,WebTiledLayer:!0},"web-map/basemap":{ArcGISImageServiceLayer:!0,ArcGISImageServiceVectorLayer:!0,ArcGISMapServiceLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,OpenStreetMap:!0,VectorTileLayer:!0,WMS:!0,WebTiledLayer:!0,BingMapsAerial:!0,BingMapsRoad:!0,BingMapsHybrid:!0},"web-map/tables":{ArcGISFeatureLayer:!0},"portal-item/operational-layers":{ArcGISFeatureLayer:!0,ArcGISSceneServiceLayer:!0,PointCloudLayer:!0,BuildingSceneLayer:!0,IntegratedMeshLayer:!0,OrientedImageryLayer:!0}};var y=t(21506);const d=e=>{let r=class extends e{constructor(){super(...arguments),this.title=null}writeListMode(e,r,t,i){(i&&"ground"===i.layerContainerType||e&&(0,p.d)(this,t,{},i))&&(r[t]=e)}writeOperationalLayerType(e,r,t,i){!e||i&&"tables"===i.layerContainerType||(r.layerType=e)}writeTitle(e,r){r.title=e??"Layer"}read(e,r){r&&(r.layer=this),(0,c.$)(this,e,(r=>super.read(e,r)),r)}write(e,r){if(r?.origin){const e=`${r.origin}/${r.layerContainerType||"operational-layers"}`,t=u[e];let i=t&&t[this.operationalLayerType];if("ArcGISTiledElevationServiceLayer"===this.operationalLayerType&&"web-scene/operational-layers"===e&&(i=!1),"ArcGISDimensionLayer"===this.operationalLayerType&&"web-map/operational-layers"===e&&(i=!1),!i)return r.messages?.push(new n.Z("layer:unsupported",`Layers (${this.title}, ${this.id}) of type '${this.declaredClass}' are not supported in the context of '${e}'`,{layer:this})),null}const t=super.write(e,{...r,layer:this}),i=!!r&&!!r.messages&&!!r.messages.filter((e=>e instanceof n.Z&&"web-document-write:property-required"===e.name)).length;return(0,s.jc)(t?.url)?(r?.messages?.push(new n.Z("layer:invalid-url",`Layer (${this.title}, ${this.id}) of type '${this.declaredClass}' using a Blob URL cannot be written to web scenes and web maps`,{layer:this})),null):!this.url&&i?null:t}beforeSave(){}};return(0,i._)([(0,a.Cb)({type:String,json:{write:{ignoreOrigin:!0},origins:{"web-scene":{write:{isRequired:!0,ignoreOrigin:!0}},"portal-item":{write:!1}}}})],r.prototype,"id",void 0),(0,i._)([(0,a.Cb)(y.rT)],r.prototype,"listMode",void 0),(0,i._)([(0,l.c)("listMode")],r.prototype,"writeListMode",null),(0,i._)([(0,a.Cb)({type:String,readOnly:!0,json:{read:!1,write:{target:"layerType",ignoreOrigin:!0},origins:{"portal-item":{write:!1}}}})],r.prototype,"operationalLayerType",void 0),(0,i._)([(0,l.c)("operationalLayerType")],r.prototype,"writeOperationalLayerType",null),(0,i._)([(0,a.Cb)(y.Oh)],r.prototype,"opacity",void 0),(0,i._)([(0,a.Cb)({type:String,json:{write:{ignoreOrigin:!0,writerEnsuresNonNull:!0},origins:{"web-scene":{write:{isRequired:!0,ignoreOrigin:!0,writerEnsuresNonNull:!0}},"portal-item":{write:!1}}},value:"Layer"})],r.prototype,"title",void 0),(0,i._)([(0,l.c)("title"),(0,l.c)(["web-scene"],"title")],r.prototype,"writeTitle",null),(0,i._)([(0,a.Cb)({type:Boolean,json:{name:"visibility"}})],r.prototype,"visible",void 0),r=(0,i._)([(0,o.j)("esri.layers.mixins.OperationalLayer")],r),r}},72965:(e,r,t)=>{t.d(r,{M:()=>a});var i=t(43697),n=t(5600),s=(t(75215),t(67676),t(52011));const a=e=>{let r=class extends e{constructor(){super(...arguments),this.minScale=0,this.maxScale=0}get effectiveScaleRange(){const e={minScale:this.minScale,maxScale:this.maxScale},r=this.parent;r&&"effectiveScaleRange"in r&&function(e,r){e.minScale=e.minScale>0?r.minScale>0?Math.min(e.minScale,r.minScale):e.minScale:r.minScale,e.maxScale=e.maxScale>0?r.maxScale>0?Math.max(e.maxScale,r.maxScale):e.maxScale:r.maxScale}(e,r.effectiveScaleRange);const t=this._get("effectiveScaleRange");return t&&t.minScale===e.minScale&&t.maxScale===e.maxScale?t:e}};return(0,i._)([(0,n.Cb)({type:Number,nonNullable:!0,json:{write:!0}})],r.prototype,"minScale",void 0),(0,i._)([(0,n.Cb)({type:Number,nonNullable:!0,json:{write:!0}})],r.prototype,"maxScale",void 0),(0,i._)([(0,n.Cb)({readOnly:!0})],r.prototype,"effectiveScaleRange",null),r=(0,i._)([(0,s.j)("esri.layers.mixins.ScaleRangeLayer")],r),r}},21506:(e,r,t)=>{t.d(r,{qG:()=>L,PV:()=>g,id:()=>I,iR:()=>d,rn:()=>f,rT:()=>x,u1:()=>_,rO:()=>O,Oh:()=>v,bT:()=>w,C_:()=>y,Lx:()=>S,vg:()=>b,YI:()=>u,HQ:()=>m});var i=t(92835),n=t(6570),s=t(82971),a=t(25929),o=t(70586),l=(t(95330),t(35463)),c=t(86787),p=t(65242);const u={type:Boolean,value:!0,json:{origins:{service:{read:!1,write:!1},"web-map":{read:!1,write:!1}},name:"screenSizePerspective",write:!0}},y={type:Boolean,value:!0,json:{name:"disablePopup",read:{reader:(e,r)=>!r.disablePopup},write:{enabled:!0,writer(e,r,t){r[t]=!e}}}},d={type:Boolean,value:!0,nonNullable:!0,json:{name:"showLabels",write:!0}},m={type:String,json:{origins:{"portal-item":{write:!1}},write:{isRequired:!0,ignoreOrigin:!0,writer:a.w}}},f={type:Boolean,value:!0,nonNullable:!0,json:{origins:{service:{read:{enabled:!1}}},name:"showLegend",write:!0}},g={value:null,type:c.Z,json:{origins:{service:{name:"elevationInfo",write:!0}},name:"layerDefinition.elevationInfo",write:!0}};function S(e){return{type:e,readOnly:!0,json:{origins:{service:{read:!0}},read:!1}}}const h={write:!0,read:!0},v={type:Number,json:{origins:{"web-document":h,"portal-item":{write:!0}}}},w={...v,json:{...v.json,origins:{"web-document":{...h,write:{enabled:!0,target:{opacity:{type:Number},"layerDefinition.drawingInfo.transparency":{type:Number}}}}},read:{source:["layerDefinition.drawingInfo.transparency","drawingInfo.transparency"],reader:(e,r,t)=>t&&"service"!==t.origin||!r.drawingInfo||void 0===r.drawingInfo.transparency?r.layerDefinition&&r.layerDefinition.drawingInfo&&void 0!==r.layerDefinition.drawingInfo.transparency?(0,p.b)(r.layerDefinition.drawingInfo.transparency):void 0:(0,p.b)(r.drawingInfo.transparency)}}},L={type:i.Z,readOnly:!0,get(){if(!this.layer?.timeInfo)return null;const{datesInUnknownTimezone:e,timeOffset:r,useViewTime:t}=this.layer,n=this.view?.timeExtent;let s=this.layer.timeExtent;e&&(s=function(e){if(!e)return e;const{start:r,end:t}=e;return new i.Z({start:(0,o.pC)(r)?(0,l.Nm)(r,r.getTimezoneOffset(),"minutes"):r,end:(0,o.pC)(t)?(0,l.Nm)(t,t.getTimezoneOffset(),"minutes"):t})}(s));let a=t?n&&s?n.intersection(s):n||s:s;if(!a||a.isEmpty||a.isAllTime)return a;r&&(a=a.offset(-r.value,r.unit)),e&&(a=function(e){if(!e)return e;const{start:r,end:t}=e;return new i.Z({start:(0,o.pC)(r)?(0,l.Nm)(r,-r.getTimezoneOffset(),"minutes"):r,end:(0,o.pC)(t)?(0,l.Nm)(t,-t.getTimezoneOffset(),"minutes"):t})}(a));const c=this._get("timeExtent");return a.equals(c)?c:a}},b={type:n.Z,readOnly:!0,json:{origins:{service:{read:{source:["fullExtent","spatialReference"],reader:(e,r)=>{const t=n.Z.fromJSON(e);return null!=r.spatialReference&&"object"==typeof r.spatialReference&&(t.spatialReference=s.Z.fromJSON(r.spatialReference)),t}}}},read:!1}},I={type:String,json:{origins:{service:{read:!1},"portal-item":{read:!1}}}},O={type:Number,json:{origins:{service:{write:{enabled:!1}}},read:{source:"layerDefinition.minScale"},write:{target:"layerDefinition.minScale"}}},_={type:Number,json:{origins:{service:{write:{enabled:!1}}},read:{source:"layerDefinition.maxScale"},write:{target:"layerDefinition.maxScale"}}},x={json:{write:{ignoreOrigin:!0},origins:{"web-map":{read:!1,write:!1}}}}},99282:(e,r,t)=>{t.d(r,{a:()=>s});var i=t(67900),n=t(68441);const s={inches:(0,i.En)(1,"meters","inches"),feet:(0,i.En)(1,"meters","feet"),"us-feet":(0,i.En)(1,"meters","us-feet"),yards:(0,i.En)(1,"meters","yards"),miles:(0,i.En)(1,"meters","miles"),"nautical-miles":(0,i.En)(1,"meters","nautical-miles"),millimeters:(0,i.En)(1,"meters","millimeters"),centimeters:(0,i.En)(1,"meters","centimeters"),decimeters:(0,i.En)(1,"meters","decimeters"),meters:(0,i.En)(1,"meters","meters"),kilometers:(0,i.En)(1,"meters","kilometers"),"decimal-degrees":1/(0,i.ty)(1,"meters",n.sv.radius)}},86787:(e,r,t)=>{t.d(r,{Z:()=>w});var i,n=t(43697),s=t(35454),a=t(96674),o=t(70586),l=t(5600),c=(t(75215),t(67676),t(71715)),p=t(52011),u=t(30556),y=t(35671);let d=i=class extends a.wq{constructor(e){super(e)}async collectRequiredFields(e,r){return(0,y.io)(e,r,this.expression)}clone(){return new i({expression:this.expression,title:this.title})}equals(e){return this.expression===e.expression&&this.title===e.title}};(0,n._)([(0,l.Cb)({type:String,json:{write:!0}})],d.prototype,"expression",void 0),(0,n._)([(0,l.Cb)({type:String,json:{write:!0}})],d.prototype,"title",void 0),d=i=(0,n._)([(0,p.j)("esri.layers.support.FeatureExpressionInfo")],d);const m=d;var f,g=t(12541);const S=(0,s.w)()({onTheGround:"on-the-ground",relativeToGround:"relative-to-ground",relativeToScene:"relative-to-scene",absoluteHeight:"absolute-height"}),h=new s.X({foot:"feet",kilometer:"kilometers",meter:"meters",mile:"miles","us-foot":"us-feet",yard:"yards"});let v=f=class extends a.wq{constructor(e){super(e),this.offset=null}readFeatureExpressionInfo(e,r){return null!=e?e:r.featureExpression&&0===r.featureExpression.value?{expression:"0"}:void 0}writeFeatureExpressionInfo(e,r,t,i){r[t]=e.write({},i),"0"===e.expression&&(r.featureExpression={value:0})}get mode(){const{offset:e,featureExpressionInfo:r}=this;return this._isOverridden("mode")?this._get("mode"):(0,o.pC)(e)||r?"relative-to-ground":"on-the-ground"}set mode(e){this._override("mode",e)}set unit(e){this._set("unit",e)}write(e,r){return this.offset||this.mode||this.featureExpressionInfo||this.unit?super.write(e,r):null}clone(){return new f({mode:this.mode,offset:this.offset,featureExpressionInfo:this.featureExpressionInfo?this.featureExpressionInfo.clone():void 0,unit:this.unit})}equals(e){return this.mode===e.mode&&this.offset===e.offset&&this.unit===e.unit&&(0,o._W)(this.featureExpressionInfo,e.featureExpressionInfo)}};(0,n._)([(0,l.Cb)({type:m,json:{write:!0}})],v.prototype,"featureExpressionInfo",void 0),(0,n._)([(0,c.r)("featureExpressionInfo",["featureExpressionInfo","featureExpression"])],v.prototype,"readFeatureExpressionInfo",null),(0,n._)([(0,u.c)("featureExpressionInfo",{featureExpressionInfo:{type:m},"featureExpression.value":{type:[0]}})],v.prototype,"writeFeatureExpressionInfo",null),(0,n._)([(0,l.Cb)({type:S.apiValues,nonNullable:!0,json:{type:S.jsonValues,read:S.read,write:{writer:S.write,isRequired:!0}}})],v.prototype,"mode",null),(0,n._)([(0,l.Cb)({type:Number,json:{write:!0}})],v.prototype,"offset",void 0),(0,n._)([(0,l.Cb)({type:g.f9,json:{type:String,read:h.read,write:h.write}})],v.prototype,"unit",null),v=f=(0,n._)([(0,p.j)("esri.layers.support.ElevationInfo")],v);const w=v},12541:(e,r,t)=>{t.d(r,{Z7:()=>n,f9:()=>s});var i=t(99282);function n(e){return 1/(i.a[e]||1)}const s=function(){const e=Object.keys(i.a);return e.sort(),e}()},65242:(e,r,t)=>{t.d(r,{a:()=>n,b:()=>s});var i=t(75215);function n(e){const r=(0,i.vU)(100*(1-e));return Math.max(0,Math.min(r,100))}function s(e){const r=1-e/100;return Math.max(0,Math.min(r,1))}}}]);