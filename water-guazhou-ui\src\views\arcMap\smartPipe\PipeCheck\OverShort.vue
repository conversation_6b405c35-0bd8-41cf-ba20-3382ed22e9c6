<!-- 超短线检查 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'超短线检查'"
    @map-loaded="onMapLoaded"
    @detail-refreshed="state.loading = false"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import {
  extentTo,
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds,
  initSketch
} from '@/utils/MapHelper'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'

const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerInfos: any[]
  layerIds: any[]
  curType: 'ellipse' | 'rectangle' | 'polygon' | ''
}>({
  tabs: [],
  curType: '',
  layerInfos: [],
  layerIds: [],
  loading: false
})
const staticState: {
  view?: __esri.MapView
  graphics?: __esri.Graphic
  graphicsLayer?: __esri.GraphicsLayer
  sketch?: __esri.SketchViewModel
  sketchCreateHandler?: any
  sketchUpdateHandler?: any
} = {}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '超短线标准'
      },
      fields: [
        {
          type: 'input-number',
          prepend: '管长≤',
          append: 'm',
          placeholder: '请输入标准值',
          field: 'length'
        }
      ]
    },
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              type: 'default',
              size: 'large',
              title: '绘制椭圆',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    },
    {
      id: 'layer',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: true,
          label: '选择图层',
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: () => (state.loading ? '正在检查，过程稍长，请耐心等待！' : '检查'),
              styles: {
                width: '100%'
              },
              loading: () => state.loading,
              click: () => startQuery()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    length: 1
  }
})

const initDraw = (type: any) => {
  if (!staticState.view) return
  clearGraphicsLayer()
  staticState.sketch?.create(type)
}
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphics = undefined
}

const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
  const field = FormConfig.group.find(item => item.id === 'layer')
    ?.fields[0] as IFormTree
  // const points = state.layerInfos
  //   .filter(item => item.geometrytype === 'esriGeometryPoint')
  //   .map(item => {
  //     return {
  //       label: item.layername,
  //       value: item.layerid,
  //       data: item
  //     }
  //   })
  const lines = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPolyline')
    .map(item => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      }
    })
  field
    && (field.options = [
      // { label: '管点类', value: -1, children: points },
      { label: '管线类', value: -2, disabled: true, children: lines }
    ])
  if (lines.length === 1) {
    refForm.value
      && (refForm.value.dataForm.layerid = lines.map(item => item.value))
  }
}
const startQuery = async () => {
  SLMessage.info('正在检查，请稍候...')
  try {
    state.loading = true
    state.tabs.length = 0
    const layerIds = refForm.value?.dataForm.layerid?.filter(item => item >= 0)
    const tabs = await getLayerOids(layerIds, state.layerInfos, {
      where: ' PIPELENGTH<=' + (refForm.value?.dataForm.length || 0) + ' ',
      geometry: staticState.graphics?.geometry
    })
    await refMap.value?.refreshDetail(tabs)
    extentTo(
      staticState.view,
      staticState.graphics?.geometry?.extent || staticState.view?.extent,
      true
    )
  } catch (error) {
    console.log(error)
    state.loading = false
    SLMessage.error('检查失败')
  }
}
const onMapLoaded = view => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-overshort',
    title: '超短线检查'
  })
  const { sketch, updateHandler, createHandler } = initSketch(
    staticState.view,
    staticState.graphicsLayer,
    graphic => {
      staticState.graphics = graphic
    }
  )
  staticState.sketch = sketch
  staticState.sketchCreateHandler = createHandler
  staticState.sketchUpdateHandler = updateHandler
  getLayerInfo()
}
onBeforeUnmount(() => {
  staticState.sketchCreateHandler?.remove()
  staticState.sketchUpdateHandler?.remove()
  staticState.graphicsLayer?.removeAll()
  staticState.graphicsLayer?.destroy()
  staticState.sketch?.destroy()
})
</script>
<style lang="scss" scoped></style>
