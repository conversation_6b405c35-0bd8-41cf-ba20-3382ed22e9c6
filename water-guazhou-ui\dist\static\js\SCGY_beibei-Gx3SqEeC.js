import{d as p,r as d,o as u,g as e,n as a,p as t,i as o,q as h,F as i,aB as b,aJ as f,h as C,dB as B,dC as x,C as v}from"./index-r0dFAfgr.js";/* empty css                         */const l="/static/png/shuichang-rBzwd2vV.png",k="/static/png/sc1-niPNdCxN.png",I="/static/png/sc2-DJbsHa96.png",N="/static/png/sc3-DkznntPb.png",D="/static/png/sc4-BV3_OmE1.png",E="/static/png/sc5-Dge6YHBf.png",V="/static/png/sc6-Drl8Nmi3.png",w={class:"img-box"},y={class:"img-top"},Y=["src"],z=["src"],F=p({__name:"SCGY_beibei",setup(G){const s=d({currentImage:l,imgs:[{label:"水厂",img:l},{label:"沉淀池",img:k},{label:"反应池",img:I},{label:"加药加氯间",img:N},{label:"空压机鼓风机",img:D},{label:"浓缩池",img:E},{label:"排污泵房",img:V}]}),c=n=>{s.currentImage=s.imgs[n].img};return u(()=>{c(0)}),(n,H)=>{const g=B,r=x;return e(),a("div",w,[t("div",y,[t("img",{src:o(s).currentImage,alt:""},null,8,Y)]),t("div",null,[h(r,{interval:4e3,type:"card",height:"120px",class:"mg_top_10",onChange:c},{default:i(()=>[(e(!0),a(b,null,f(o(s).imgs,(m,_)=>(e(),C(g,{key:_},{default:i(()=>[t("img",{src:m.img,style:{width:"100%",height:"160px"}},null,8,z)]),_:2},1024))),128))]),_:1})])])}}}),S=v(F,[["__scopeId","data-v-3458f7f2"]]);export{S as default};
