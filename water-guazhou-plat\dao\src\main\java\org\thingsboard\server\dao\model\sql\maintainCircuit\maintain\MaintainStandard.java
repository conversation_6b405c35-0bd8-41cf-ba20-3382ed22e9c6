package org.thingsboard.server.dao.model.sql.maintainCircuit.maintain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 保养规范
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@TableName("tb_device_maintain_standard")
@Data
public class MaintainStandard {
    @TableId
    private String id;

    private String serialId;

    private transient String deviceTypeName;

    private String method;

    private String remark;

    private String creator;

    private transient String creatorName;

    private Date createTime;

    private String tenantId;

}
