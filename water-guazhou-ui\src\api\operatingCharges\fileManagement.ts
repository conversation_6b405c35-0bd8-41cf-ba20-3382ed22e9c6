// 营收-档案管理
import request from '@/plugins/axios';

/**
 * 用户信息列表查询
 * @param typeId
 * @param params
 * @returns
 */
export function getCustInfoList(params: {
  address?: string;
  billType?: string;
  brand?: string;
  caliber?: string;
  cancelUserTimeEnd?: string;
  cancelUserTimeStart?: string;
  changeMeterTimeEnd?: string;
  changeMeterTimeStart?: string;
  code?: string;
  copyMeterUser?: string;
  createTimeEnd?: string;
  createTimeStart?: string;
  industryCategory?: string;
  isMoreMeter?: string;
  meterBookIds?: string;
  meterWellCode?: string;
  mixUseWater?: string;
  name?: string;
  orgId?: string;
  page?: number | undefined;
  paymentMethod?: string;
  payType?: string;
  phone?: string;
  priceCode?: string;
  remark?: string;
  size?: number | undefined;
  status?: string;
  steelSealNumber?: string;
  type?: string;
  userType?: string;
  waterCategory?: string;
  waterStopFlag?: string;
}) {
  return request({
    url: '/api/revenue/custInfo/list',
    method: 'GET',
    params
  });
}

export function importSave(
  key: string,
  params: {
    isExpanding: number;
  }
) {
  return request({
    url: `/api/revenue/custInfo/importSave/${key}`,
    method: 'POST',
    data: params
  });
}

// 查询用户的抄表数据列表
export function getReadMeterDataList(params: {
  page: string | number;
  size: string | number;
  userCode: string;
  beginTime?: string;
  endTime?: string;
}) {
  return request({
    url: `/api/revenue/readMeterData/listByUser`,
    method: 'get',
    params
  });
}

/**
 * 获取区域树
 * @returns {Promise<any>} 区域树数据
 */
export function getAreaTree() {
  return request({
    url: `/api/revenue/area/tree`,
    method: 'get'
  });
}

/**
 * 删除区域
 * @param data 区域数据
 * @returns 请求结果
 */
export function deleteArea(data: string[]) {
  return request({
    url: `/api/revenue/area`,
    method: 'delete',
    data
  });
}

/**
 * 获取仪表盘列表
 * @param params 请求参数
 * @param params.page 页码
 * @param params.size 每页数量
 * @param params.type 类型
 * @param params.areaId 区域ID
 * @param params.orgId 组织ID
 * @param params.name 名称
 * @returns Promise对象，包含仪表盘列表数据
 */
export function getMeterBookList(params: {
  page: string | number;
  size: string | number;
  type?: string;
  areaId?: string;
  orgId?: string;
  name?: string;
}) {
  return request({
    url: `/api/revenue/areaBook/list`,
    method: 'get',
    params
  });
}
