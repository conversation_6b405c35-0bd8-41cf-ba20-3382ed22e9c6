import{i as R,s as E}from"./Point-WxyopZva.js";import{e as I,n as S}from"./enums-B5k73o5q.js";import{E as i,S as T,I as g}from"./enums-L38xj_2E.js";import{F as _,G as d,C as m}from"./enums-BDQrMlcz.js";import{t as $}from"./VertexElementDescriptor-BOD-G50G.js";import{T as B}from"./index-r0dFAfgr.js";import{x as M}from"./number-CoJp78Rz.js";const p=<PERSON>.getLogger("esri.views.2d.engine.webgl.Utils"),c="geometry",O=[{name:c,strideInBytes:12}],F=[{name:c,strideInBytes:36}],b=[{name:c,strideInBytes:24}],x=[{name:c,strideInBytes:12}],P=[{name:c,strideInBytes:40}],z=[{name:c,strideInBytes:36}],G=[{name:c,strideInBytes:36}];function l(t){const n={};for(const e of t)n[e.name]=e.strideInBytes;return n}const k=l([{name:c,strideInBytes:36}]),V=l(O),Y=l(F),H=l(b),q=l(x),K=l(P),W=l(z),X=l(G);function It(t,n){switch(t){case i.MARKER:return n===T.HEATMAP?V:k;case i.FILL:switch(n){case T.DOT_DENSITY:return q;case T.SIMPLE:case T.OUTLINE_FILL_SIMPLE:return H;default:return Y}case i.LINE:return K;case i.TEXT:return W;case i.LABEL:return X}}const J=[c],Q=[c],Z=[c],j=[c],tt=[c];function nt(t){switch(t){case i.MARKER:return J;case i.FILL:return Q;case i.LINE:return Z;case i.TEXT:return j;case i.LABEL:return tt}}function et(t){switch(t%4){case 0:case 2:return 4;case 1:case 3:return 1}}function St(t,n){switch(n%4){case 0:case 2:return new Uint32Array(Math.floor(t*n/4));case 1:case 3:return new Uint8Array(t*n)}}function Tt(t,n){switch(n%4){case 0:case 2:return new Uint32Array(t);case 1:case 3:return new Uint8Array(t)}}function gt(t){return t!=null}function Et(t){return typeof t=="number"}function ht(t){switch(t){case"butt":return I.BUTT;case"round":return I.ROUND;case"square":return I.SQUARE;default:return p.error(new E("mapview-invalid-type",`Cap type ${t} is not a valid option. Defaulting to round`)),I.ROUND}}function wt(t){switch(t){case"miter":return S.MITER;case"bevel":return S.BEVEL;case"round":return S.ROUND;default:return p.error(new E("mapview-invalid-type",`Join type ${t} is not a valid option. Defaulting to round`)),S.ROUND}}function Lt(t){switch(t){case"opacity":return g.OPACITY;case"color":return g.COLOR;case"rotation":return g.ROTATION;case"size":return g.SIZE;default:return p.error(`Cannot interpret unknown vv: ${t}`),null}}function Nt(t,n,e,r,a,o,s){for(const f in o){const u=o[f].stride,h=et(u),L=o[f].data,N=e[f].data;if(L==null||N==null)continue;const U=u*a.vertexCount/h,v=u*t/h,C=u*a.vertexFrom/h;for(let y=0;y<U;++y)N[y+v]=L[y+C]}if(s!=null&&r!=null){const f=a.indexCount;for(let u=0;u<f;++u)r[u+n]=s[u+a.indexFrom]-a.vertexFrom+t}}const Dt={[c]:_.STATIC_DRAW};function At(t,n){const e=[];for(let r=0;r<5;++r){const a=nt(r),o={};for(const s of a)o[s]={data:n(r,s)};e.push({data:t(r),buffers:o})}return e}function rt(t){switch(t){case m.BYTE:case m.UNSIGNED_BYTE:return 1;case m.SHORT:case m.UNSIGNED_SHORT:return 2;case m.FLOAT:case m.INT:case m.UNSIGNED_INT:return 4}}function Mt(t){switch(t){case d.UNSIGNED_BYTE:return 1;case d.UNSIGNED_SHORT_4_4_4_4:return 2;case d.FLOAT:return 4;default:return void p.error(new E("webgl-utils",`Unable to handle type ${t}`))}}function Ut(t){switch(t){case d.UNSIGNED_BYTE:return Uint8Array;case d.UNSIGNED_SHORT_4_4_4_4:return Uint16Array;case d.FLOAT:return Float32Array;default:return void p.error(new E("webgl-utils",`Unable to handle type ${t}`))}}function at(t){var e;const n={};for(const r in t){const a=t[r];let o=0;n[r]=a.map(s=>{const f=new $(s.name,s.count,s.type,o,0,s.normalized||!1);return o+=s.count*rt(s.type),f}),(e=n[r])==null||e.forEach(s=>s.stride=o)}return n}const st=t=>{const n=new Map;for(const e in t)for(const r of t[e])n.set(r.name,r.location);return n},it=t=>{const n={};for(const e in t){const r=t[e];n[e]=r!=null&&r.length?r[0].stride:0}return n},w=new Map,vt=(t,n)=>{if(!w.has(t)){const e=at(n),r={strides:it(e),bufferLayouts:e,attributes:st(n)};w.set(t,r)}return w.get(t)};function Ct(t){t(i.FILL),t(i.LINE),t(i.MARKER),t(i.TEXT),t(i.LABEL)}const Rt=t=>"path"in t&&ct(t.path),_t=t=>"url"in t&&t.url||"imageData"in t&&t.imageData,$t=t=>"imageData"in t&&t.imageData&&"contentType"in t&&t.contentType?`data:${t.contentType};base64,${t.imageData}`:"url"in t?t.url:null,D=t=>t!=null&&t.startsWith("data:image/gif"),Bt=t=>"url"in t&&t.url&&(t.url.includes(".gif")||D(t.url))||"contentType"in t&&t.contentType==="image/gif"||"imageData"in t&&D(t.imageData),A=t=>t!=null&&t.startsWith("data:image/png"),Ot=t=>"url"in t&&t.url&&(t.url.includes(".png")||A(t.url))||"contentType"in t&&t.contentType==="image/png"||"imageData"in t&&A(t.imageData),Ft=t=>t.type&&t.type.toLowerCase().includes("3d");function bt(t){switch(t.type){case"line":{const n=t;return n.cim.type==="CIMSolidStroke"&&!n.dashTemplate}case"fill":return t.cim.type==="CIMSolidFill";case"esriSFS":return t.style==="esriSFSSolid"||t.style==="esriSFSNull";case"esriSLS":return t.style==="esriSLSSolid"||t.style==="esriSLSNull";default:return!1}}const xt=t=>t.includes("data:image/svg+xml");function Pt(t){switch("cim"in t?t.cim.type:t.type){case"esriSMS":case"esriPMS":case"CIMPointSymbol":return!1;case"CIMVectorMarker":case"CIMCharacterMarker":case"CIMPictureMarker":return ot(t);default:return!0}}function zt(t){const n="maxVVSize"in t&&t.maxVVSize,e="width"in t&&t.width||"size"in t&&t.size||0;return n||e}function Gt(t){const n=[];for(let e=0;e<t.length;e++)n.push(t.charCodeAt(e));return n}const ct=t=>!!t&&(t=t.trim(),!!(/^[mzlhvcsqta]\s*[-+.0-9][^mlhvzcsqta]+/i.test(t)&&/[\dz]$/i.test(t)&&t.length>4)),ot=t=>{var n,e;return t.type==="fill"&&((e=(n=t==null?void 0:t.cim)==null?void 0:n.markerPlacement)==null?void 0:e.type)==="CIMMarkerPlacementInsidePolygon"};function kt(t,n=0,e=!1){const r=t[n+3];return t[n+0]*=r,t[n+1]*=r,t[n+2]*=r,e||(t[n+3]*=255),t}function Vt(t){if(!t)return 0;const{r:n,g:e,b:r,a}=t;return M(n*a,e*a,r*a,255*a)}function Yt(t){if(!t)return 0;const[n,e,r,a]=t;return M(n*(a/255),e*(a/255),r*(a/255),a)}function Ht(t,n,e=0){if(B(n))return t[e+0]=0,t[e+1]=0,t[e+2]=0,void(t[e+3]=0);const{r,g:a,b:o,a:s}=n;t[e+0]=r*s/255,t[e+1]=a*s/255,t[e+2]=o*s/255,t[e+3]=s}export{Et as $,et as F,wt as G,At as H,ot as I,Mt as K,Tt as P,It as R,Lt as V,Ut as W,Dt as Y,Ht as a,Ct as b,Yt as c,zt as d,vt as e,Pt as f,Vt as g,Bt as i,Nt as j,ht as k,bt as l,xt as m,Rt as n,Ot as o,Gt as p,_t as r,$t as s,kt as t,Ft as u,St as x,gt as z};
