package org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_pump_manage")
// 泵机管理
public class PumpManage {
    // id
    @TableId
    private String id;

    // 所属泵房ID
    private String pumpRoomId;

    // 所属泵房编码
    private String pumpRoomCode;

    // 所属泵房名称
    private String pumpRoomName;

    // 设备编码
    private String code;

    // 设备名称
    private String name;

    // 设备简称
    private String nickname;

    // 泵个数
    private Integer pumpNum;

    // 厂家名称
    private String companyName;

    // 型号
    private String model;

    // 安装人名称
    private String installUserName;

    // 安装时间
    private Date installDate;

    // 性能参数
    private String performanceParameters;

    // 备注
    private String remark;

    // 创建人
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

}
