import { request } from '@/plugins/axios'

/**
 * 查询管网列表
 * @param params
 * @returns
 */
export const GetDmaPartitionPartitionPipe = (
  params: IQueryPagerParams & {
    partitionId?: string
  }
) => {
  return request({
    url: '/api/spp/dma/partition/partitionPipe/list',
    method: 'get',
    params
  })
}
/**
 * 添加管网
 * @param params
 * @returns
 */
export const AddDmaPartitionPartitionPipe = (params: {
  partitionId: string
  mainPipe: string
  pipe: string
  length: string
  year: string
  remark: string
  file: string
}) => {
  return request({
    url: '/api/spp/dma/partition/partitionPipe',
    method: 'post',
    data: params
  })
}
/**
 * 删除管网
 * @param ids
 * @returns
 */
export const DeleteDmaPartitionPartitionPipe = (ids: string[]) => {
  return request({
    url: '/api/spp/dma/partition/partitionPipe',
    method: 'delete',
    data: ids
  })
}
