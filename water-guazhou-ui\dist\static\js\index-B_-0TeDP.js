import{d as X,r as x,c,o as Z,g as C,n as L,q as t,F as l,p as v,G as d,bo as ee,h as te,bh as ae,i as le,an as oe,y as S,x as u,J as ne,H as re,I as se,c2 as ie,K as de,N as ue,O as pe,P as me,bz as ge,bK as fe,L as ce,br as ye,C as ve}from"./index-r0dFAfgr.js";import{f as be}from"./DateFormatter-Bm9a68Ax.js";import{g as _e,d as h,u as Ve,s as De}from"./assay-DCQODP3D.js";import{u as Ue}from"./fileUpload-CoTDqaci.js";const Ce={class:"water-quality-test"},Ne={class:"card-header"},we={class:"header-buttons"},ke={class:"pagination"},Fe={class:"el-upload__tip"},xe={key:0,style:{color:"#67C23A"}},Le={class:"dialog-footer"},Se=X({__name:"index",setup(he){const n=x({pageNum:1,pageSize:10,samplingLocation:"",reportName:"",testingUnit:"",testDate:null,type:2}),_=c([]),V=c(0),D=c(!1),p=c([]),z=o=>{p.value=o.map(e=>e.id)},r=x({id:"",samplingLocation:"",reportName:"",testingUnit:"",testResults:"",testDate:null,reportFile:"",remark:"",type:2}),E={samplingLocation:[{required:!0,message:"请输入采样地点",trigger:"blur"}],reportName:[{required:!0,message:"请输入报告名称",trigger:"blur"}],testingUnit:[{required:!0,message:"请输入检测单位",trigger:"blur"}],testResults:[{required:!0,message:"请输入检测结果",trigger:"blur"}],testDate:[{required:!0,message:"请选择检测月份",trigger:"change"}]},y=c(!1),U=c(""),b=c(),f=async()=>{D.value=!0;try{const o={pageNum:n.pageNum,pageSize:n.pageSize,samplingLocation:n.samplingLocation,reportName:n.reportName,testingUnit:n.testingUnit,type:2};n.testDate&&(o.testDate=n.testDate);const e=await _e(o);e.data&&e.data.data&&e.data.data.data?(_.value=Array.isArray(e.data.data.data)?e.data.data.data:[],V.value=e.data.data.total||0):(console.warn("返回数据格式不正确:",e),_.value=[],V.value=0)}catch(o){console.error("获取数据失败:",o),_.value=[],V.value=0}finally{D.value=!1}},N=()=>{n.pageNum=1,p.value=[],f()},q=()=>{n.samplingLocation="",n.reportName="",n.testingUnit="",n.testDate=null,p.value=[],N()},R=()=>{U.value="新增化验记录",y.value=!0},B=o=>{U.value="编辑化验记录",Object.assign(r,{...o,testDate:o.testDate?Number(o.testDate):null}),y.value=!0},T=o=>{S.confirm("确认删除该记录吗？","提示",{type:"warning"}).then(async()=>{try{await h([o.id]),u.success("删除成功"),f()}catch(e){console.error(e),u.error("删除失败")}})},M=()=>{if(p.value.length===0){u.warning("请选择要删除的记录");return}S.confirm(`确认删除选中的 ${p.value.length} 条记录吗？`,"提示",{type:"warning"}).then(async()=>{try{await h(p.value),u.success("批量删除成功"),p.value=[],f()}catch(o){console.error(o),u.error("批量删除失败")}})},Q=o=>{o.reportFile&&window.open(o.reportFile)},I=o=>o.size/1024/1024<10?!0:(u.warning("文件大小不能超过10MB"),!1),P=async o=>{const{file:e}=o;try{const s=await Ue(e,"file");r.reportFile=s,u.success("文件上传成功")}catch(s){console.error("文件上传失败:",s),u.error("文件上传失败")}},$=async()=>{b.value&&await b.value.validate(async o=>{if(o)try{const e={...r,testDate:r.testDate?String(r.testDate):String(Date.now()),reportFile:typeof r.reportFile=="string"?r.reportFile:"",type:2};if(!e.reportFile&&!r.id){u.warning("请先上传化验报告文件");return}r.id?(await Ve(e),u.success("修改成功")):(await De(e),u.success("新增成功")),y.value=!1,f()}catch(e){console.error("保存失败:",e),u.error("保存失败")}})},A=()=>{b.value&&b.value.resetFields(),Object.assign(r,{id:"",samplingLocation:"",reportName:"",testingUnit:"",testResults:"",testDate:null,reportFile:"",remark:"",type:2});const o=document.querySelector(".upload-demo .el-upload__input");o&&(o.value="")},W=o=>{n.pageSize=o,p.value=[],f()},Y=o=>{n.pageNum=o,p.value=[],f()};return Z(()=>{f()}),(o,e)=>{const s=ne,m=re,i=se,w=ie,k=de,g=ue,j=pe,O=me,K=ge,G=fe,H=ce,J=ye;return C(),L("div",Ce,[t(K,{class:"box-card"},{header:l(()=>[v("div",Ne,[e[16]||(e[16]=v("span",null,"化验记录",-1)),v("div",we,[t(s,{type:"primary",onClick:R},{default:l(()=>e[14]||(e[14]=[d("新增记录")])),_:1}),t(s,{type:"danger",disabled:p.value.length===0,onClick:M},{default:l(()=>e[15]||(e[15]=[d("批量删除")])),_:1},8,["disabled"])])])]),default:l(()=>[t(k,{model:n,ref:"queryForm",inline:!0,class:"search-form"},{default:l(()=>[t(i,{label:"采样地点",prop:"samplingLocation"},{default:l(()=>[t(m,{modelValue:n.samplingLocation,"onUpdate:modelValue":e[0]||(e[0]=a=>n.samplingLocation=a),placeholder:"请输入采样地点",clearable:""},null,8,["modelValue"])]),_:1}),t(i,{label:"报告名称",prop:"reportName"},{default:l(()=>[t(m,{modelValue:n.reportName,"onUpdate:modelValue":e[1]||(e[1]=a=>n.reportName=a),placeholder:"请输入报告名称",clearable:""},null,8,["modelValue"])]),_:1}),t(i,{label:"检测单位",prop:"testingUnit"},{default:l(()=>[t(m,{modelValue:n.testingUnit,"onUpdate:modelValue":e[2]||(e[2]=a=>n.testingUnit=a),placeholder:"请输入检测单位",clearable:""},null,8,["modelValue"])]),_:1}),t(i,{label:"检测月份",prop:"testDate"},{default:l(()=>[t(w,{modelValue:n.testDate,"onUpdate:modelValue":e[3]||(e[3]=a=>n.testDate=a),type:"month",placeholder:"选择月份","value-format":"x"},null,8,["modelValue"])]),_:1}),t(i,null,{default:l(()=>[t(s,{type:"primary",onClick:N},{default:l(()=>e[17]||(e[17]=[d("搜索")])),_:1}),t(s,{onClick:q},{default:l(()=>e[18]||(e[18]=[d("重置")])),_:1})]),_:1})]),_:1},8,["model"]),ee((C(),te(j,{data:_.value,style:{width:"100%"},onSelectionChange:z},{default:l(()=>[t(g,{type:"selection",width:"55"}),t(g,{prop:"samplingLocation",label:"采样地点","min-width":"120"}),t(g,{prop:"reportName",label:"报告名称","min-width":"120"}),t(g,{prop:"testingUnit",label:"检测单位","min-width":"120"}),t(g,{prop:"testResults",label:"检测结果","min-width":"120"}),t(g,{prop:"testDate",label:"检测月份","min-width":"120"},{default:l(({row:a})=>[d(ae(a.testDate?le(be)(Number(a.testDate),"YYYY-MM"):""),1)]),_:1}),t(g,{prop:"reportFile",label:"报告附件","min-width":"120"},{default:l(({row:a})=>[t(s,{type:"text",onClick:F=>Q(a)},{default:l(()=>e[19]||(e[19]=[d("下载")])),_:2},1032,["onClick"])]),_:1}),t(g,{label:"操作",width:"150"},{default:l(({row:a})=>[t(s,{type:"text",onClick:F=>B(a)},{default:l(()=>e[20]||(e[20]=[d("编辑")])),_:2},1032,["onClick"]),t(s,{type:"text",onClick:F=>T(a)},{default:l(()=>e[21]||(e[21]=[d("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[J,D.value]]),v("div",ke,[t(O,{"current-page":n.pageNum,"onUpdate:currentPage":e[4]||(e[4]=a=>n.pageNum=a),"page-size":n.pageSize,"onUpdate:pageSize":e[5]||(e[5]=a=>n.pageSize=a),total:V.value,onSizeChange:W,onCurrentChange:Y,layout:"total, sizes, prev, pager, next, jumper"},null,8,["current-page","page-size","total"])])]),_:1}),t(H,{title:U.value,modelValue:y.value,"onUpdate:modelValue":e[13]||(e[13]=a=>y.value=a),width:"500px",onClose:A},{footer:l(()=>[v("span",Le,[t(s,{onClick:e[12]||(e[12]=a=>y.value=!1)},{default:l(()=>e[24]||(e[24]=[d("取消")])),_:1}),t(s,{type:"primary",onClick:$},{default:l(()=>e[25]||(e[25]=[d("确定")])),_:1})])]),default:l(()=>[t(k,{ref_key:"formRef",ref:b,model:r,rules:E,"label-width":"100px"},{default:l(()=>[t(i,{label:"采样地点",prop:"samplingLocation"},{default:l(()=>[t(m,{modelValue:r.samplingLocation,"onUpdate:modelValue":e[6]||(e[6]=a=>r.samplingLocation=a),placeholder:"请输入采样地点"},null,8,["modelValue"])]),_:1}),t(i,{label:"报告名称",prop:"reportName"},{default:l(()=>[t(m,{modelValue:r.reportName,"onUpdate:modelValue":e[7]||(e[7]=a=>r.reportName=a),placeholder:"请输入报告名称"},null,8,["modelValue"])]),_:1}),t(i,{label:"检测单位",prop:"testingUnit"},{default:l(()=>[t(m,{modelValue:r.testingUnit,"onUpdate:modelValue":e[8]||(e[8]=a=>r.testingUnit=a),placeholder:"请输入检测单位"},null,8,["modelValue"])]),_:1}),t(i,{label:"检测结果",prop:"testResults"},{default:l(()=>[t(m,{modelValue:r.testResults,"onUpdate:modelValue":e[9]||(e[9]=a=>r.testResults=a),placeholder:"请输入检测结果"},null,8,["modelValue"])]),_:1}),t(i,{label:"检测月份",prop:"testDate"},{default:l(()=>[t(w,{modelValue:r.testDate,"onUpdate:modelValue":e[10]||(e[10]=a=>r.testDate=a),type:"month",placeholder:"选择月份","value-format":"x"},null,8,["modelValue"])]),_:1}),t(i,{label:"化验报告",prop:"reportFile"},{default:l(()=>[t(G,{class:"upload-demo","http-request":P,"before-upload":I,limit:1},{tip:l(()=>[v("div",Fe,[e[23]||(e[23]=d(" 请上传化验报告文件 ")),r.reportFile?(C(),L("span",xe," (已上传) ")):oe("",!0)])]),default:l(()=>[t(s,{type:"primary"},{default:l(()=>e[22]||(e[22]=[d("点击上传")])),_:1})]),_:1})]),_:1}),t(i,{label:"备注",prop:"remark"},{default:l(()=>[t(m,{modelValue:r.remark,"onUpdate:modelValue":e[11]||(e[11]=a=>r.remark=a),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}}),Be=ve(Se,[["__scopeId","data-v-04b2a57f"]]);export{Be as default};
