import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import{s as y}from"./ToolHelper-BiiInOzB.js";import{Q as v}from"./index-r0dFAfgr.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const E=()=>{let e,h;const g=[];let n,s;const l=(t,o,r,f)=>{if(!t||!o)return;const m=o.layer;t.whenLayerView(m).then(i=>{p();const u=i.highlight(o);g.push(u),s&&clearTimeout(s),s=setTimeout(()=>{r&&r(o)},f??500)})},H=(t,o,r,f)=>{!t||!o||(e==null||e.remove(),e=t.on("pointer-move",m=>{t.hitTest(m.native,{include:o}).then(i=>{var u,d;i.results.length?(y("pointer"),n=((u=i.results[0])==null?void 0:u.type)==="graphic"?i.results[0].graphic:void 0,n&&!((d=n==null?void 0:n.attributes)!=null&&d.notHighlight)&&l(t,n,r,f)):(r==null||r(),y(""),s&&clearTimeout(s))})}),h=t.on("click",m=>{t.hitTest(m.native).then(i=>{i.results.length||p()})}))},p=()=>{g.map(t=>t.remove&&t.remove()),g.length=0},T=()=>{p(),e==null||e.remove(),h==null||h.remove()};return v(()=>{T()}),{bindHoverHighLight:H,removeHoverHighLight:p,destroy:T,highlight:l}};export{E as u};
