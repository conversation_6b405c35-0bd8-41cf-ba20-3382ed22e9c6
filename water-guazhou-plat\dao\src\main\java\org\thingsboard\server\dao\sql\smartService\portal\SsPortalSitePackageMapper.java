package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSitePackage;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalSitePackagePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalSitePackageSaveRequest;

import java.util.List;

@Mapper
public interface SsPortalSitePackageMapper extends BaseMapper<SsPortalSitePackage> {
    IPage<SsPortalSitePackage> findByPage(SsPortalSitePackagePageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalSitePackage entity);

    boolean updateFully(SsPortalSitePackage entity);

    List<SsPortalSitePackage> findRoots(SsPortalSitePackagePageRequest request);

    List<SsPortalSitePackage> findChildren(@Param("id") String id, @Param("jumpToUrl") Boolean jumpToUrl, @Param("tenantId") String tenantId);

    boolean canBeDelete(String id);

    boolean canSave(SsPortalSitePackageSaveRequest request);

}
