<template>
  <div class="wrapper">
    <SLCard class="wrapper-content" title=" ">
      <template #title>
        <Tabs v-model="state.activeName" :config="tabsConfig"></Tabs>
      </template>
      <div v-if="state.activeName === '监测模式'" class="monitoring-tab">
        <div class="top-box">
          <SLCard class="card" title="监测状态统计">
            <Form ref="refForm" :config="FormConfig"> </Form>
          </SLCard>
          <SLCard class="table" title="">
            <FormTable class="" :config="alarmTableConfig"></FormTable>
          </SLCard>
        </div>
        <div class="bottom-box">
          <div class="card-item" :class="{ isDark: useAppStore().isDark }">
            <div
              v-for="(monitor, x) in state.JianCeDatas"
              :key="x"
              class="card-content"
            >
              <SLCard title=" " class="inner-card left">
                <template #title>
                  <div class="monitor-title">
                    <div class="title">
                      <el-image
                        :src="state.icon"
                        style="width: 35px; height: 36px"
                      />
                      <span>{{ monitor.title }}</span>
                    </div>
                    <div @click="showDetail(monitor)">
                      <Icon
                        icon="ph:warning-circle-bold"
                        style="color: #4f7db8; font-size: 18px"
                      ></Icon>
                    </div>
                  </div>
                </template>
                <div class="monitor-table">
                  <div class="monitor">
                    <div
                      v-for="(item, t) in monitor.monitorData"
                      :key="t"
                      class="box-1"
                    >
                      <div>
                        {{ item.propertyName }}
                        {{ item.unit ? '(' + item.unit + ')' : '' }}
                      </div>
                      <div>{{ item.value || '无' }}</div>
                    </div>
                  </div>
                </div>
              </SLCard>
            </div>
          </div>
        </div>
      </div>
      <FormTable
        v-if="state.activeName === '列表模式'"
        class="table"
        :config="tableConfig"
      ></FormTable>
    </SLCard>
    <SLDrawer ref="refDrawer" :config="drawerConfig">
      <template #title>
        <el-image :src="state.icon" style="width: 35px; height: 36px" />
        <span style="padding-left: 10px">{{ state.drawerTitle }}</span>
      </template>

      <station-detail-monitoring
        :station-id="state.stationId"
        :monitor="state.stationInfo"
        @hiddenLoading="hiddenLoading"
      ></station-detail-monitoring>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import {
  Refresh,
  Search as SearchIcon,
  Download,
  Plus,
  Filter
} from '@element-plus/icons-vue';
import { Icon } from '@iconify/vue';
import { useAppStore, useBusinessStore } from '@/store';
import stationDetailMonitoring from './components/stationDetailMonitoring.vue';
import { IECharts } from '@/plugins/echart';
import { pieOption } from '../echartsData/echart';
import useStation from '@/hooks/station/useStation';
import {
  GetStationList,
  GetStationRecentDataByType,
  getAlarmCenter
} from '@/api/shuiwureports/zhandian';
import { getInfoDetail } from '@/api/waterQualityManage/waterQualityMonitoring';
import { formatDate } from '@/utils/DateFormatter';
import { getStationView } from '@/api/pipeNetworkMonitoring/flowMonitoring';
import { IStationDatas } from '@/views/pipeNetworkMonitoring/flowMonitoring/data/type';
// import { warningList } from '@/api/waterFactoryManage/warningAndIncident'
import { ring } from '@/views/arcMap/components/components/chart';
import { GetWaterQualityStaionList } from '@/api/mapservice/onemap';
import Group1 from '@/assets/images/Group1.png';
import { alarmStatus, processStatus, alarmType } from '@/common/constans/alarm';

const { getStationTree } = useStation();
const statusOptions = [
  { name: 'offline', label: '离线' },
  { name: 'alarm', label: '报警' },
  { name: 'online', label: '正常' }
];

const state = reactive<{
  activeName: string;
  drawerTitle: string;
  tabsList: any[];
  stationTree: any;
  treeDataType: string;
  stationId: string;
  pieOption: any;
  stationInfo: any;
  JianCeDatas: any;
  icon: any;
}>({
  activeName: '监测模式',
  drawerTitle: '',
  tabsList: [],
  stationTree: [],
  treeDataType: 'Project',
  stationId: '',
  pieOption: null,
  stationInfo: {},
  JianCeDatas: [],
  icon: Group1
});

const refDrawer = ref<ISLDrawerIns>();
const refChart = ref<IECharts>();
const trefSearch = ref<ISearchIns>();
const allStation = ref<IStationDatas[]>([]);
const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: true,
  title: '区域划分',
  expandOnClickNode: false,
  treeNodeHandleClick: async (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      TreeData.loading = true;
      TreeData.currentProject = data;
      state.treeDataType = data.data.type as string;
      if (state.treeDataType === 'Project') {
        useBusinessStore().SET_selectedProject(data);
        await refreshData();
        state.stationId = '';
      } else {
        nextTick(() => {
          state.stationId = data.id as string;
          state.stationInfo = data;
        });
      }
    }
  }
});
const drawerConfig = reactive<IDrawerConfig>({
  title: '',
  labelWidth: '130px',
  width: '80%',
  group: [],
  cancel: false,
  onClosed: () => {
    state.treeDataType === 'Project';
    state.stationId = '';
  }
});
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'chart',
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            height: '150px'
          }
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    type: 'all'
  }
});

const hiddenLoading = () => {
  TreeData.loading = false;
};
// 告警饼图统计
const alarmNum = async () => {
  // const res = await GetStationList({
  //   page: 1,
  //   size: 999,
  //   projectId: useBusinessStore().selectedProject?.value,
  //   type: '水质监测站'
  // })
  const res = await GetWaterQualityStaionList({
    projectId: useBusinessStore().selectedProject?.value,
    status: ''
  });
  const field = FormConfig.group[0].fields[0] as IFormVChart;
  const total: number = res.data?.data?.length || 0;
  const statusDatas: any[] = [];
  const data = res.data?.data;
  data?.map((item) => {
    let statusData = statusDatas.find((o) => o.status === item.status);
    const { label } = statusOptions.find((o) => o.name === item.status) || {};
    if (!statusData) {
      statusData = {
        name:
          statusOptions.find((o) => o.name === item.status)?.label ??
          item.status,
        status: item.status,
        nameAlias: label,
        value: 1,
        // valueAlias: '1',
        scale: '0%'
      };
      statusDatas.push(statusData);
    } else {
      statusData.value++;
    }
  });
  statusDatas.map((item) => {
    item.scale = total === 0 ? '0%' : (Number(item.value) / total) * 100 + '%';
    return item;
  });
  field && (field.option = ring(statusDatas, '个'));
};

// 监控模式搜索配置
const msearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: 'RTU编号',
      field: 'name'
    },
    {
      type: 'select',
      label: '排序方式',
      field: 'name',
      options: []
    },
    {
      type: 'select',
      label: '排序顺序',
      field: 'name',
      options: []
    },
    {
      type: 'input',
      label: '筛选条件',
      field: 'name'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '添加条件',
          type: 'success',
          svgIcon: shallowRef(Plus),
          click: () => {
            // refTable.value?.exportTable()
          }
        },
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        }
      ]
    }
  ],
  defaultParams: {
    projectId: useBusinessStore().selectedProject?.value
  }
});

// 列表模式搜索配置
const tsearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: '监测点名称',
      labelWidth: '120px',
      field: 'name1'
    },
    {
      type: 'input',
      label: 'RTU编号',
      field: 'name1'
    },
    {
      type: 'input-number',
      label: '超过不通讯时长',
      labelWidth: '120px',
      field: 'name1'
    },

    {
      type: 'btn-group',
      btns: [
        {
          type: 'default',
          perm: true,
          svgIcon: shallowRef(Filter),
          text: '',
          isBlockBtn: true,
          click: () => {
            trefSearch.value?.toggleMore();
          }
        },
        {
          perm: true,
          text: '添加条件',
          type: 'success',
          svgIcon: shallowRef(Plus),
          click: () => {
            // refTable.value?.exportTable()
          }
        },
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            trefSearch.value?.resetForm();
          }
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          svgIcon: shallowRef(Download),
          click: () => {
            //
          }
        }
      ]
    }
  ],
  moreFilters: [
    {
      type: 'select',
      label: '排序顺序',
      field: 'name1',
      options: []
    },
    {
      type: 'select',
      label: '筛选条件',
      field: 'name1',
      options: [{ label: '正常', value: '正常' }]
    }
  ],
  defaultParams: {
    projectId: useBusinessStore().selectedProject?.value
  }
});

// tab配置
const tabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  width: '100%',
  tabs: [
    { label: '监测模式', value: '监测模式' },
    { label: '列表模式', value: '列表模式' }
  ],
  handleTabClick: (tab: any) => {
    console.log('动都不动不得不对不对', tab.props.name);
  }
});

// 数据获取
const refreshData = async () => {
  await alarmList();
  // TreeData.loading = true
  TreeData.loading = false;
  const res = await GetStationList({
    page: tableConfig.pagination.page || 1,
    size: tableConfig.pagination.limit || 20,
    type: '水质监测站',
    projectId: useBusinessStore().selectedProject?.value
  });
  const dataList: any = [];
  const data = res.data?.data;
  data.map(async (d) => {
    const attribute = await GetStationRecentDataByType({
      stationType: '水质监测站',
      projectId: useBusinessStore().selectedProject?.value
    });
    console.log('d', d);
    const attr = attribute.data.find((attr) => attr.name === d.name);
    const data = {
      ...d,
      ...attr
    };
    dataList.push(data);
  });
  console.log('dataList', dataList);
  tableConfig.dataList = dataList;
  tableConfig.pagination.total = res.data?.total;
  const stationView = await getStationView({
    stationType: '水质监测站',
    projectId: data?.value || useBusinessStore().selectedProject?.value
  });

  const stationDatas = stationView.data;
  allStation.value = [];
  stationDatas?.map((stationData) => {
    allStation.value.push({
      id: stationData.stationId,
      title: stationData.name,
      monitorData: stationData.dataList || []
    });
    state.JianCeDatas = allStation.value;
  });
  TreeData.loading = false;
  await refreshChart();
  await alarmNum();
};
// 图表刷新
const refreshChart = async () => {
  refChart.value?.clear();
  nextTick(() => {
    // const datax1 = result.todayTotalFlowDataList?.map(item => {
    //   return item.ts.substring(8, 20)
    // })
    // const pressureData = result.pressure?.map(item => item.value)
    // const InstantaneousDta = result.Instantaneous_flow?.map(item => item.value)
    state.pieOption = pieOption();
    refChart.value?.resize();
  });
};

// 报警列表配置
const alarmTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'alarmInfo', label: '报警描述' },
    {
      prop: 'time',
      label: '报警时间',
      formatter: (row: any) => dayjs(row.time).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      prop: 'alarmType',
      label: '报警类型',
      formatter: (row) =>
        alarmType.find((item) => item.value === row.alarmType)?.label
    },
    {
      prop: 'alarmStatus',
      label: '报警状态',
      formatter: (row) =>
        alarmStatus.find((item) => item.value === row.alarmStatus)?.label
    },
    {
      prop: 'processStatus',
      label: '处理状态',
      formatter: (row) =>
        processStatus.find((item) => item.value === row.alarmStatus)?.label
    }
    // {
    //   prop: 'status',
    //   label: '处理状态',
    //   formatter: val => {
    //     return '未恢复 | 未确认'
    //   },
    //   cellStyle: (row, val) => {
    //     return {
    //       color: 'red'
    //     }
    //   }
    // }
  ],
  operations: [],
  pagination: {
    refreshData: ({ page, size }) => {
      alarmTableConfig.pagination.page = page;
      alarmTableConfig.pagination.limit = size;
      alarmList();
      // alarmTableConfig.dataList = tableConfig.slice((page - 1) * size, page * size)
    }
  }
});
const alarmList = async () => {
  const params = {
    alarmStatus: '1',
    stationType: '水质监测站',
    projectId: useBusinessStore().selectedProject?.value,
    size: alarmTableConfig.pagination.limit || 20,
    page: alarmTableConfig.pagination.page || 1
  };
  const res = await getAlarmCenter(params);
  const result = res.data?.data;
  console.log(result);
  alarmTableConfig.dataList = result?.data || [];
  alarmTableConfig.pagination.total = result?.total || 0;
};
// 列表
const tableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'name', label: '监测点名称', align: 'center', sortable: true },
    { prop: 'time', label: '读取时间', align: 'center', sortable: true },
    { prop: 'remainder', label: '余氯', align: 'center', sortable: true },
    { prop: 'turbidity', label: '浊度', align: 'center', sortable: true },
    { prop: 'ph', label: 'PH', align: 'center', sortable: true },
    { prop: 'oxygen', label: '溶氧', align: 'center', sortable: true },
    { prop: 'conductance', label: '电导率', align: 'center', sortable: true },
    {
      prop: 'createTime',
      label: '安装时间',
      align: 'center',
      sortable: true,
      formatter: (row: any, val: any) => {
        return formatDate(val);
      }
    },
    { prop: 'address', label: '安装位置', align: 'center', sortable: true }
  ],
  operations: [],
  // handleRowClick: (row) => {
  //   showDetail({
  //     title: row.name,
  //     ...row
  //   });
  // },
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    layout: 'total, prev, pager, next, sizes, jumper',
    handleSize: (val) => {
      tableConfig.pagination.limit = val;
      // cardTableConfig.dataList = tableData.slice((currentPage-1)*pagesize,currentPage*pagesize)
    },
    refreshData: ({ page, size }) => {
      tableConfig.pagination.page = page;
      tableConfig.pagination.limit = size;
      // tableConfig.dataList = tableConfig.slice((page - 1) * size, page * size)
    }
  }
});
// 显示单个监测站信息
const showDetail = (data) => {
  refDrawer.value?.openDrawer();
  nextTick(() => {
    state.drawerTitle = data.title;
    state.stationInfo = data;
    state.stationId = data.id;
    state.treeDataType = 'Station';
  });
  console.log(data);
};
onMounted(async () => {
  const treeData = await getStationTree('水质监测站');
  const project = useBusinessStore().selectedProject as any;
  const treeData1 = await getInfoDetail({ projectId: project.id });
  console.log(treeData1);
  TreeData.data = treeData;
  // TreeData.currentProject = getFormatTreeNode(
  //   TreeData.data
  // )
  TreeData.currentProject = treeData[0];
  refreshData();
});
// 切换tabs
// const handleClick = val => {
//   console.log('value', val)
// }
</script>
<style lang="scss" scoped>
.dark {
  .iconClass {
    color: #ffffff;
  }

  .monitor-table {
    background-image: linear-gradient(#222536 50%, #313748 50%) !important;
    background-size: 100% 144px !important;

    .monitor-name {
      background: #383e53 !important;
    }

    .monitor {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      background-image: linear-gradient(#222536 50%, #313748 50%) !important;
      background-size: 100% 144px !important;
    }
  }

  .box-1 {
    div:nth-child(1) {
      color: #e8e9ec !important;
    }
  }
}

.wrapper-content {
  height: 100%;
}

.tab-content {
  height: 82vh;
}

:deep(.el-card__body) {
  padding: 12px;
  display: grid;
  align-content: center;
  justify-content: space-between;
  flex-wrap: wrap;
  // grid-template-rows: 425px 425px 425px;
  grid-template-columns: 32% 32% 32%;
}

.top-box {
  height: 40%;
  display: flex;
  justify-content: space-between;

  .card {
    width: calc(40% - 15px);
    height: 100%;
  }

  .table {
    width: calc(60%);
  }
}

.table-tab {
  .table {
    height: 80vh;
  }
}

.bottom-box {
  display: flex;
  height: 60%;
  width: 100%;
  margin-bottom: 15px;

  .card-item {
    //height: 500px;
    width: 100%;
    overflow-y: auto;
    margin-bottom: 16px;
    padding: 12px 0;
    display: grid;
    align-content: space-between;
    justify-content: space-between;
    flex-wrap: wrap;
    grid-template-columns: 24% 24% 24% 24%;

    &:last-child {
      margin-bottom: 0;
    }

    &.isDark {
      :deep(.el-card__header) {
        background-color: #415c88;
      }
    }

    .card-title {
      display: flex;
      align-items: center;
    }

    .card-content {
      margin-bottom: 30px;

      // display: flex;
      // justify-content: space-between;
      // width: calc(50% - 8px);
      .time-box {
        color: #f54141;
        text-align: center;
        // background-color: #222536;
        border-bottom: 1px solid #f54141;
        padding: 5px 0;
      }
    }

    .inner-card {
      // width: calc(50% - 8px);
      // width: 30%;
      padding-left: 0;
      padding-right: 0;
      height: 430px;

      &.left {
        height: 415px;
      }
    }

    //   .chart-item {
    //     height: 200px;
    //     margin-bottom: 15px;
    //     &:last-child {
    //       margin-bottom: 0;
    //     }
    //   }
    .monitor-title {
      display: flex;
      justify-content: space-between;
      padding: 0 17px;
      width: 100%;
      align-items: center;

      .title {
        display: flex;
        align-items: center;

        span {
          padding-left: 10px;
          font-weight: 600;
          font-size: 16px;
        }
      }
    }

    .monitor-table {
      width: 100%;
      height: 100%;
      overflow: auto;
      background-image: linear-gradient(#ffffff 50%, #f9fafe 50%);
      background-size: 100% 144px !important;

      .monitor-name {
        height: 72px;
        line-height: 72px;
        background: #f9fafe;
      }

      .monitor {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        background-image: linear-gradient(#ffffff 50%, #f9fafe 50%);
        background-size: 100% 144px !important;
      }

      .box-1 {
        text-align: center;
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 72px;
        line-height: 24px;
        overflow-y: auto;
        width: 33.33%;

        div:nth-child(1) {
          color: #7c8295;
          font-size: 12px;
          font-weight: 400;
        }

        div:nth-child(2) {
          color: #4f7db8;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }

  .monitor-box {
    display: flex;
    flex-wrap: wrap;

    .monitor-item {
      flex-basis: 25%;
      min-height: 80px;
      min-width: 110px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      border: 1px solid #e8e8ed;
      background-color: #fff;

      &.isDark {
        border: 1px solid #222536;
        background-color: #1a1d2d;
      }

      .count {
        color: #42b8fe;
        font-size: 18px;
        line-height: 30px;
      }

      .total {
        display: flex;
        align-items: baseline;
      }

      .label {
        color: #949ab8;
        font-size: 14px;
      }

      .unit {
        font-size: 12px;
        margin-left: 8px;
      }
    }
  }
}

.text-blue {
  color: #399bff;
}

.text-green {
  color: #42c902;
}

.text-red {
  color: #af1313;
}

.monitoring-tab {
  height: 100%;
}
</style>
