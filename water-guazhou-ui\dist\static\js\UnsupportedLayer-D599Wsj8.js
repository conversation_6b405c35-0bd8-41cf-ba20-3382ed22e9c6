import{a1 as n,e as o,y as i,a,s as l}from"./Point-WxyopZva.js";import{R as u,V as d,e as y}from"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";let r=class extends u(d(y)){constructor(e){super(e),this.resourceInfo=null,this.type="unsupported"}initialize(){this.addResolvingPromise(new Promise((e,s)=>{n(()=>{const t=this.resourceInfo&&(this.resourceInfo.layerType||this.resourceInfo.type);let p="Unsupported layer type";t&&(p+=" "+t),s(new l("layer:unsupported-layer-type",p,{layerType:t}))})}))}read(e,s){const t={resourceInfo:e};e.id!=null&&(t.id=e.id),e.title!=null&&(t.title=e.title),super.read(t,s)}write(e){return Object.assign(e||{},this.resourceInfo,{id:this.id})}};o([i({readOnly:!0})],r.prototype,"resourceInfo",void 0),o([i({type:["show","hide"]})],r.prototype,"listMode",void 0),o([i({json:{read:!1},readOnly:!0,value:"unsupported"})],r.prototype,"type",void 0),r=o([a("esri.layers.UnsupportedLayer")],r);const v=r;export{v as default};
