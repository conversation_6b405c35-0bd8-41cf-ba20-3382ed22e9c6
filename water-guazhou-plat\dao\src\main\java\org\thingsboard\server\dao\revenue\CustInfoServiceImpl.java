package org.thingsboard.server.dao.revenue;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.revenue.CustInfo;
import org.thingsboard.server.dao.sql.revenue.CustInfoMapper;

import java.util.List;

@Slf4j
@Service(value = "CustInfoServiceParent")
public class CustInfoServiceImpl implements CustInfoService {

    @Autowired
    private CustInfoMapper custInfoMapper;

    @Override
    public CustInfo findOneByCodeAndTenantId(String code, String tenantId) {

        return custInfoMapper.findOneByCodeAndTenantId(code, tenantId);
    }

    @Override
    public List<CustInfo> findByUserCodeList(List<String> userCodeList, TenantId tenantId) {
        QueryWrapper<CustInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("code", userCodeList).eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId()));
        return custInfoMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> findOpenIdListByMeterBookId(String meterBookIds, String tenantId) {
        return custInfoMapper.findOpenIdListByMeterBookId(meterBookIds, tenantId);
    }

    @Override
    public List<String> findOpenIdListByDmaId(String dmaIds, String tenantId) {
        return custInfoMapper.findOpenIdListByDmaId(dmaIds, tenantId);
    }
}
