package org.thingsboard.server.controller.smartPipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataCorrectRecords;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataReadMeterData;
import org.thingsboard.server.dao.smartPipe.PipeCopyDataReadMeterDataService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 智慧管网-用水量修正
 */
@RestController
@RequestMapping("api/spp/readMeterData")
public class PipeCopyDataReadMeterDataController extends BaseController {

    @Autowired
    private PipeCopyDataReadMeterDataService pipeCopyDataReadMeterDataService;

    @GetMapping("list")
    public IstarResponse getList(PartitionCustRequest partitionCustRequest) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionCustRequest.setTenantId(tenantId);
        return IstarResponse.ok(pipeCopyDataReadMeterDataService.getList(partitionCustRequest));
    }

    @GetMapping("listExport")
    public void getListExport(PartitionCustRequest partitionCustRequest, HttpServletResponse response) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionCustRequest.setTenantId(tenantId);
        List<PipeCopyDataReadMeterData> data = pipeCopyDataReadMeterDataService.getList(partitionCustRequest).getData();

        Map headMap = new LinkedHashMap();
        headMap.put("partitionName", "分区名称");
        headMap.put("custName", "用户名称");
        headMap.put("custCode", "营收户号");
        headMap.put("phone", "联系方式");
        headMap.put("ym", "抄表年月");
        headMap.put("correctWater", "修正水量");
        headMap.put("totalWater", "抄表水量");
        headMap.put("thisReadDate", "本次抄表日期");
        headMap.put("lastReadDate", "上次抄表时间");
        JSONArray array = JSONObject.parseArray(JSONObject.toJSONString(data));

        ExcelUtil.exportExcelX("用水量修正", headMap, array, "yyyy-MM-dd HH:mm:ss", 20, false, response);

    }

    @PostMapping("correct")
    public IstarResponse save(@RequestBody PipeCopyDataReadMeterData pipeCopyDataReadMeterData) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        pipeCopyDataReadMeterData.setTenantId(tenantId);

        pipeCopyDataReadMeterDataService.correct(pipeCopyDataReadMeterData, userId);

        return IstarResponse.ok();
    }

    @GetMapping("correct/records")
    public IstarResponse getCorrectRecords(PartitionCustRequest partitionCustRequest) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionCustRequest.setTenantId(tenantId);
        return IstarResponse.ok(pipeCopyDataReadMeterDataService.getCorrectRecords(partitionCustRequest));
    }

    @GetMapping("correct/recordsExport")
    public void getCorrectRecordsExport(PartitionCustRequest partitionCustRequest, HttpServletResponse response) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionCustRequest.setTenantId(tenantId);
        List<PipeCopyDataCorrectRecords> data = pipeCopyDataReadMeterDataService.getCorrectRecords(partitionCustRequest).getData();

        Map headMap = new LinkedHashMap();
        headMap.put("custName", "用户名称");
        headMap.put("phone", "联系方式");
        headMap.put("ym", "抄表年月");
        headMap.put("correctWater", "修正水量");
        JSONArray array = JSONObject.parseArray(JSONObject.toJSONString(data));

        ExcelUtil.exportExcelX("用水量修正记录", headMap, array, "yyyy-MM-dd HH:mm:ss", 20, false, response);

    }

}
