package com.istar;

import com.istar.upload.config.UploadConfigProperty;
import com.istar.upload.util.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.context.annotation.Bean;

import javax.servlet.MultipartConfigElement;

@SpringBootApplication
@EnableEurekaClient
@EnableConfigurationProperties(UploadConfigProperty.class)
public class UploadApplication {
    public static void main(String[] args) {
        SpringApplication.run(UploadApplication.class, args);
    }

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        factory.setMaxFileSize(1073741824L);
        factory.setMaxRequestSize(1073741824L);
        return factory.createMultipartConfig();
    }

    @Bean
    public MinioClient minIOUtils(@Value("${minio.endpoint}") String endpoint,
                                  @Value("${minio.accessKey}") String accessKey,
                                  @Value("${minio.secretKey}") String secretKey,
                                  @Value("${minio.bucketName}") String bucketName) {
        return new MinioClient(endpoint, bucketName, accessKey, secretKey);
    }
}
