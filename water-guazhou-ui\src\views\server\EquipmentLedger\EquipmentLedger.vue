<!-- 电信-设备台账 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable
      ref="refTable"
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm ref="refForm" :config="FormConfig"> </DialogForm>
    <DialogForm ref="refUploadDialog" :config="uploadConfig">
      <el-form ref="ruleFormRef" :rules="rules" :model="ruleForm">
        <el-row>
          <el-col :span="12">
            <el-form-item>
              <div class="buttons">
                <el-button
                  type="primary"
                  :icon="Download"
                  :plain="true"
                  @click="downloadTemplate"
                >
                  下载模板
                </el-button>
                <el-upload
                  ref="upload"
                  action="action"
                  accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                  :show-file-list="true"
                  :auto-upload="false"
                  :on-remove="handleRemove"
                  :limit="1"
                  :on-change="clickUpload"
                >
                  <el-button type="primary" :icon="Upload">
                    添加文件
                  </el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      只能导入xlsx文件, 请确保导入的文件单元格格式为文本!
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="泵房" required prop="pumpRoomId">
              <el-select v-model="ruleForm.pumpRoomId" placeholder="请选择泵房">
                <el-option
                  v-for="(item, index) in state.pumpRooms"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <FormTable :config="uploadTableConfig"></FormTable>
    </DialogForm>
    <DialogForm ref="refFileUpload" :config="uploadFormConfig">
      <Search ref="uploadSearch" :config="uploadSearchConfig" />
      <div class="btns">
        <el-upload
          ref="uploadRef"
          :action="state.fileActionUrl"
          :show-file-list="true"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          :auto-upload="false"
          multiple
          :headers="{
            'X-Authorization': 'Bearer ' + useUserStore().token
          }"
          :on-success="(res, file) => clickUploadFile(res, file)"
          class="upload-demo"
        >
          <template #trigger>
            <el-button type="primary"> 读取文件 </el-button>
          </template>
        </el-upload>

        <el-button type="success" @click="() => uploadRef?.submit()">
          上传
        </el-button>
      </div>
      <FormTable :config="fileTableConfig" class="form-table" />
    </DialogForm>
  </div>
</template>
<script lang="ts" setup>
import {
  Delete,
  Edit,
  Download,
  Plus,
  Refresh,
  Upload,
  Search as SearchIcon
} from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { useAppStore, useUserStore } from '@/store';
import { FormInstance, FormRules, ElMessage } from 'element-plus';
import { SLConfirm } from '@/utils/Message.js';
import {
  moreFilters,
  machineRoomInfoColumn,
  formDataFields,
  formFields,
  pumpUpload,
  readExcelToJson
} from './data';
import type { UploadInstance } from 'element-plus';
import {
  pumpHouseStorageList,
  saveRegistry,
  delRegistry,
  fileRegistryList
} from '@/api/secondSupplyManage/pumpRoomInfo';
import {
  deviceManageList,
  adddeviceManage,
  editdeviceManage,
  deldeviceManage,
  batchAdddeviceManage,
  deviceManageExport,
  deviceManageTemplate
} from '@/api/server/index';
import { downloadFile } from '@/utils/fileHelper';
import useGlobal from '@/hooks/global/useGlobal';

const { $messageSuccess, $messageError } = useGlobal();

const ruleFormRef = ref<FormInstance>();
const refForm = ref<IDialogFormIns>();
const refTable = ref<ICardTableIns>();
const refUploadDialog = ref<IDialogFormIns>();
const refSearch = ref<ICardSearchIns>();
const uploadRef = ref<UploadInstance>();
const uploadSearch = ref<ISearchIns>();
const refFileUpload = ref<IDialogFormIns>();

const state = reactive<{
  pumpRooms: any;
  fileType: string;
  pumpRoomId: string;
  fileActionUrl: string;
  dataList: any;
  rowId: string;
}>({
  pumpRooms: [],
  fileType: '',
  pumpRoomId: '',
  fileActionUrl: useAppStore().actionUrl + '/file/api/upload/file',
  dataList: [],
  rowId: ''
});
const ruleForm = reactive({
  pumpRoomId: ''
});

const rules = reactive<FormRules>({
  pumpRoomId: [
    {
      required: true,
      message: '请选择泵房',
      trigger: 'blur'
    }
  ]
});
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: '设备编码',
      field: 'code',
      placeholder: '请输入设备编码'
    },
    ...moreFilters
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
          }
        },
        {
          perm: true,
          text: '添加',
          type: 'success',
          svgIcon: shallowRef(Plus),
          click: () => {
            FormConfig.title = '新增';
            handleEdit();
          }
        },
        {
          perm: true,
          text: '导入',
          type: 'warning',
          svgIcon: shallowRef(Upload),
          click: () => {
            handleRemove();
            refUploadDialog.value?.openDialog();
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'primary',
          svgIcon: shallowRef(Download),
          click: () => exportTable()
        }
      ]
    }
  ]
});

// 导入弹框
const uploadConfig = reactive<IDialogFormConfig>({
  title: '导入',
  dialogWidth: 1200,
  group: [],
  cancel: true,
  btns: [
    {
      perm: true,
      text: '确定导入',
      click: async () => {
        await ruleFormRef.value?.validate(async (valid) => {
          if (valid) {
            if (state.dataList.length > 0) {
              console.log(state.dataList);
              state.dataList = state.dataList.map((data) => {
                return (data = {
                  ...data,
                  pumpRoomId: ruleForm.pumpRoomId
                });
              });
              batchAdddeviceManage(state.dataList)
                .then((res) => {
                  if (res.data?.code === 200) {
                    handleRemove();
                    refUploadDialog.value?.closeDialog();
                    ElMessage.success('提交成功');
                    refreshData();
                  } else {
                    ElMessage.error('提交失败');
                  }
                })
                .catch((error) => {
                  console.log(error);
                  ElMessage.error('提交失败');
                });
            } else {
              ElMessage.warning('请导入正确的xlsx文件！');
            }
          }
        });
      }
    }
  ]
});

// 解析导入文件数据
const clickUpload = (file: any) => {
  state.dataList = [];
  readExcelToJson(file).then((res: any) => {
    res &&
      res.forEach((el) => {
        const val = {};
        for (const i in el) {
          if (typeof pumpUpload[i] === 'undefined') {
            ElMessage.info(
              '设备编码/设备名称/设备简称/泵个数/厂家名称/设备型号/安装人/安装日期/性能参数/备注; 且每行均有对应数据!'
            );
            return;
          }
          val[pumpUpload[i]] = el[i];
        }
        state.dataList.push(val);
      });
    const strings = state.dataList.map((item) => JSON.stringify(item));
    const removeDupList = [...new Set(strings)]; // 也可以使用Array.from(new Set(strings))
    uploadTableConfig.dataList = removeDupList.map((item: any) =>
      JSON.parse(item)
    );
  });
};
// 导出列表
const exportTable = async () => {
  const query = refSearch.value?.queryParams || {};
  const [installDateFrom, installDateTo] = query.installDateFrom;
  const [fromTime, toTime] = query.fromTime;
  const params = {
    ...query,
    page: TableConfig.pagination.page || 1,
    size: -1,
    installDateFrom: installDateFrom
      ? dayjs(installDateFrom).startOf('day').valueOf()
      : null,
    installDateTo: installDateTo
      ? dayjs(installDateTo).startOf('day').valueOf()
      : null,
    fromTime,
    toTime
  };
  const res = await deviceManageExport(params);
  const url = window.URL.createObjectURL(res.data);
  console.log(url);
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', `机房信息列表.xlsx`);
  document.body.appendChild(link);
  link.click();
};
// 下载模板
const downloadTemplate = async () => {
  const res = await deviceManageTemplate();
  const url = window.URL.createObjectURL(res.data);
  console.log(url);
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', `机房信息模板.xlsx`);
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(link.href);
};
// 上传数据列表
const uploadTableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: formFields.slice(1),
  dataList: [],
  pagination: {
    hide: true
  }
});

const clickUploadFile = async (res, file) => {
  const param = {
    fileName: file.name,
    fileAddress: file.response,
    label: state.fileType,
    host: state.rowId
  };
  saveRegistry(param).then(() => {
    refreshFileData(state.fileType, state.rowId);
  });
};

//  附件查询配置
const uploadSearchConfig = reactive<ISearch>({
  defaultParams: {
    time: []
  },
  filters: [
    {
      type: 'input',
      label: '文件名',
      field: 'fileName',
      placeholder: '请输入文件名'
    },
    {
      type: 'daterange',
      label: '上传日期',
      field: 'fromTime',
      format: 'YYYY-MM-DD'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshFileData(state.fileType, state.rowId)
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            uploadSearch.value?.resetForm();
          }
        }
      ]
    }
  ]
});

// 数据列表配置
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: machineRoomInfoColumn.concat([
    {
      minWidth: 120,
      label: '施工图纸',
      prop: 'constructionFile',
      formItemConfig: {
        type: 'btn-group',
        btns: [
          {
            perm: true,
            text: '查看',
            type: 'default',
            svgIcon: shallowRef(Edit),
            click: (row) => {
              console.log(row.id);
              state.fileType = 'constructionFile';
              refFileUpload.value?.openDialog();
              refreshFileData(state.fileType, row.id);
            }
          }
        ]
      }
    },
    {
      minWidth: 120,
      label: '其他文件',
      prop: 'otherFile',
      formItemConfig: {
        type: 'btn-group',
        btns: [
          {
            perm: true,
            text: '查看',
            type: 'default',
            svgIcon: shallowRef(Edit),
            click: (row) => {
              state.fileType = 'otherFile';
              refFileUpload.value?.openDialog();
              refreshFileData(state.fileType, row.id);
            }
          }
        ]
      }
    }
  ]),
  operations: [
    {
      perm: true,
      text: '修改',
      svgIcon: shallowRef(Edit),
      click: (row) => {
        FormConfig.title = '修改';
        handleEdit(row);
      }
    },
    {
      perm: true,
      text: '删除',
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

// 附件上传
const uploadFormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 1050,
  title: '上传文件',
  labelWidth: 120,
  group: [{ fields: formDataFields() }]
});

// 表单配置
const FormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 600,
  title: '新增',
  labelWidth: 120,
  group: [{ fields: formFields }],
  submit: (params: any) => {
    params.pumpRoomId = '';
    SLConfirm('确定提交？', '提示信息')
      .then(() => {
        if (FormConfig.title === '新增') {
          adddeviceManage(params).then(() => {
            refForm.value?.refForm?.resetForm();
            refForm.value?.closeDialog();
            ElMessage.success('提交成功');
            refreshData();
          });
        } else {
          editdeviceManage(params).then(() => {
            refForm.value?.refForm?.resetForm();
            refForm.value?.closeDialog();
            ElMessage.success('提交成功');
            refreshData();
          });
        }
      })
      .catch(() => {
        //
      });
  }
});
// 编辑弹框
const handleEdit = (row?: any) => {
  FormConfig.defaultValue = row
    ? {
        ...row,
        installDate: dayjs(row.installDate).format()
      }
    : {};
  refForm.value?.openDialog();
};
// 删除数据
const handleDelete = (row?: any) => {
  SLConfirm('确定删除?', '提示信息')
    .then(() => {
      deldeviceManage(row.id).then(() => {
        refreshData();
      });
    })
    .catch(() => {
      //
    });
};

// 附件上传列表
const fileTableConfig = reactive<ITable>({
  indexVisible: true,
  columns: [
    { label: '文件', prop: 'fileName', minWidth: 120 },
    {
      label: '上传时间',
      prop: 'uploadTime',
      minWidth: 120,
      formatter: (row: any, value: any) => {
        return value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '';
      }
    }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      text: '下载',
      type: 'primary',
      svgIcon: shallowRef(Download),
      click: (row) => {
        downloadFile(row.fileAddress, row.fileName);
      }
    },
    {
      perm: true,
      text: '删除',
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: (row) => {
        SLConfirm('确定删除该附件, 是否继续?', '删除提示').then(() => {
          delRegistry(row.id)
            .then((res) => {
              if (res.data?.code === 200) {
                $messageSuccess('删除成功');
              } else {
                $messageError('删除失败');
              }
              refreshFileData(state.fileType, state.rowId);
            })
            .catch((err) => {
              $messageError(err);
            });
        });
      }
    }
  ],
  pagination: {
    hide: true
  }
});

// 刷新数据
const refreshData = async () => {
  const query = refSearch.value?.queryParams || {};
  const [installDateFrom, installDateTo] = query.installDateFrom;
  const [fromTime, toTime] = query.fromTime;
  const params = {
    ...query,
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    installDateFrom: installDateFrom
      ? dayjs(installDateFrom).startOf('day').valueOf()
      : null,
    installDateTo: installDateTo
      ? dayjs(installDateTo).startOf('day').valueOf()
      : null,
    fromTime,
    toTime
  };
  const result = await deviceManageList(params);
  console.log(result.data.data.total);
  TableConfig.pagination.total = result.data?.data.total;
  TableConfig.dataList = result.data?.data.data;
};

const refreshFileData = async (label: string, id: string) => {
  const query = uploadSearch.value?.queryParams || {
    fromTime: []
  };
  const [start, end] = query.fromTime || [];
  const newParams = {
    ...query,
    host: id,
    label,
    fromTime: start,
    toTime: end,
    page: 1,
    size: 99999
  };
  state.rowId = id;
  const res = await fileRegistryList(newParams);
  console.log(res.data?.data?.data);
  fileTableConfig.dataList = res.data?.data?.data;
};
// 删除导入文件清空数据
const handleRemove = () => {
  state.dataList = [];
  uploadTableConfig.dataList = [];
};
//
onMounted(async () => {
  const result = await pumpHouseStorageList({ size: 999, page: 1 });
  const pumpRooms = result?.data?.data?.data;
  state.pumpRooms = pumpRooms;
  const pumpHouseOptions = pumpRooms.map((pumpHouse) => {
    return { label: pumpHouse.name, value: pumpHouse.id };
  });
  const pumpRoomField = FormConfig.group[0]?.fields?.find(
    (field) => field.field === 'pumpRoomId'
  ) as IFormSelect;
  pumpRoomField.options = pumpHouseOptions;
  refreshData();
});
</script>
<style lang="scss" scoped>
.buttons {
  display: flex;
  align-items: flex-start;

  button {
    margin-right: 10px;
  }
}
</style>
