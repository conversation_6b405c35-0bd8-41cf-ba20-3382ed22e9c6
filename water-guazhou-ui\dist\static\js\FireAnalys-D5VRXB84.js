import{d as F,c as v,r as w,b as n,Q as C,g as R,h as x,F as A,q as M,i as b,_ as G,X as z}from"./index-r0dFAfgr.js";import{c as N,s as h}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as L,a as Q}from"./LayerHelper-Cn-iiqxI.js";import{g as V,q as $,c as E}from"./QueryHelper-ILO3qZqg.js";import{u as O}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import U from"./RightDrawerMap-D5PhmGFO.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Hr=F({__name:"FireAnalys",setup(W){const p=v(),s=v(),i=w({tabs:[],loading:!1,mounted:!1,layerInfos:[],layerIds:[]}),t={queryParams:{geometry:void 0,where:void 0}},k=w({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制着火点",iconifyIcon:"mdi:circle-slice-8",click:()=>I()},{perm:!0,text:"",title:"清除绘制",type:"default",size:"large",iconifyIcon:"ep:delete",click:()=>{var r;l(),(r=p.value)==null||r.clearDetailData()}}]}]},{fieldset:{desc:"分析范围"},fields:[{type:"input-number",append:"米",field:"distance"},{type:"btn-group",btns:[{perm:!0,text:"查询",styles:{width:"100%"},click:()=>S()}]}]}],labelPosition:"top",gutter:12,defaultValue:{distance:200}}),{initSketch:_,destroySketch:P}=O(),I=()=>{var r,e;l(),(r=p.value)==null||r.clearDetailData(),(e=t.sketch)==null||e.create("point")},S=async()=>{var e,o,m;if(!t.firePoint){n.warning("请先绘制着火点");return}n.info("正在查询中，请稍候...");const r=((o=(e=s.value)==null?void 0:e.dataForm)==null?void 0:o.distance)||"0";i.tabs.length=0;try{const a=t.firePoint.geometry;r==="0"?t.queryParams.geometry=a:await q(a),i.tabs=await V(i.layerIds,i.layerInfos,t.queryParams),(m=p.value)==null||m.refreshDetail(i.tabs)}catch{n.error("系统错误"),i.loading=!1}},q=async r=>{var a,f,d,y,u,g;if((a=t.bufferLayer)==null||a.removeAll(),!r)return;const e=((d=(f=s.value)==null?void 0:f.dataForm)==null?void 0:d.distance)||0,o=await $(E({bufferSpatialReference:(y=t.view)==null?void 0:y.spatialReference,distances:[e],geometries:[r],outSpatialReference:(u=t.view)==null?void 0:u.spatialReference,geodesic:!0,unit:"meters",unionResults:!1}));t.queryParams.geometry=o[0];const m=N({geometry:o[0],symbol:h("polygon",{color:[0,182,153,.2],outlineColor:"#00B699",outlineWidth:1})});(g=t.bufferLayer)==null||g.add(m)},l=()=>{var r,e,o;(r=t.bufferLayer)==null||r.removeAll(),(e=t.drawLayer)==null||e.removeAll(),(o=t.firePoint)==null||o.destroy(),t.firePoint=void 0,t.queryParams.geometry=void 0},B=async()=>{var e,o;i.layerIds=Q(t.view,void 0,void 0,"消防栓");const r=await z(i.layerIds);i.layerInfos=((o=(e=r.data)==null?void 0:e.result)==null?void 0:o.rows)||[]},c=r=>{r.state==="complete"&&(t.firePoint=r.graphics[0])},D=r=>{t.view=r,t.bufferLayer=L(t.view,{id:"pipe-analys-buffer",title:"火灾分析结果"}),t.drawLayer=L(t.view,{id:"pipe-analys-firepoint",title:"火灾点"}),t.sketch=_(t.view,t.drawLayer,{updateCallBack:c,createCallBack:c}),t.sketch.pointSymbol=h("point",{color:"#ff0000"}),B()};return C(()=>{var r,e;(r=t.bufferLayer)==null||r.removeAll(),(e=t.drawLayer)==null||e.removeAll(),P()}),(r,e)=>{const o=G;return R(),x(U,{ref_key:"refMap",ref:p,title:"火灾分析","full-content":!0,onMapLoaded:D,onDetailRefreshed:e[0]||(e[0]=m=>b(i).loading=!1)},{default:A(()=>[M(o,{ref_key:"refForm",ref:s,config:b(k)},null,8,["config"])]),_:1},512)}}});export{Hr as default};
