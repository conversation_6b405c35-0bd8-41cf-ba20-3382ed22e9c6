package org.thingsboard.server.common.data.VO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业短信秘钥
 *
 * <AUTHOR>
 * @date 2020/5/22 17:39
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class TenantSmsKey {
    /**
     * 短信APPkey
     */
    private String smsAppKey;
    /**
     * 设备类key
     */
    private String smsDeviceKey;
    /**
     * 流程类key
     */
    private String smsModelKey;
    /**
     * 验证码key
     */
    private String captchaKey;


}
