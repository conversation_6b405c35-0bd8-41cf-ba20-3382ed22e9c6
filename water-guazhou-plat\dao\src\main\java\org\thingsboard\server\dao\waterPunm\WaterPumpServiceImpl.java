package org.thingsboard.server.dao.waterPunm;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.WaterPumpEntity;
import org.thingsboard.server.dao.sql.waterPump.WaterPumpRepository;

import java.util.List;

@Slf4j
@Service
public class WaterPumpServiceImpl implements WaterPumpService {

    @Autowired
    private WaterPumpRepository waterPumpRepository;

    @Override
    public List<WaterPumpEntity> findAll(TenantId tenantId) {
        return waterPumpRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }
}
