/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.RepairEntity;

import javax.transaction.Transactional;
import java.util.List;

public interface RepairRepository extends CrudRepository<RepairEntity, String> {

    List<RepairEntity> findByStatus(String status);

    @Modifying
    @Transactional
    @Query("update RepairEntity r set r.status = :status where r.id in (:needUpdateRepairIdList)")
    void updateStatus(@Param("needUpdateRepairIdList") List<String> needUpdateRepairIdList,
                      @Param("status") String status);

    List<RepairEntity> findByTenantId(String tenantId);
}
