<!-- 设备类别属性 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    />
    <DialogForm
      ref="refForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import useGlobal from '@/hooks/global/useGlobal'
import { IListVideoItem } from '@/common/types/video'
import { SLConfirm } from '@/utils/Message'
import { getDeviceTypeTree, getDeviceTypeAttrSearch, postDeviceTypeAttr, deleteDeviceTypeAttr, patchDeviceTypeAttr } from '@/api/equipment_assets/equipmentManage'
import { traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refForm = ref<IDialogFormIns>()

const refSearch = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '属性编码', field: 'code', type: 'input' },
    { label: '属性名称', field: 'name', type: 'input' },
    { label: '所属类别', field: 'typeName', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole('新建')
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '属性编码', prop: 'code' },
    { label: '属性名称', prop: 'name' },
    { label: '所属类别', prop: 'mainTypeName' },
    { label: '备注', prop: 'remark' },
    { label: '创建人', prop: 'creatorName' },
    { label: '创建时间', prop: 'createTime' }
  ],
  operationWidth: '200px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: row => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: row => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const TreeData = reactive<SLTreeConfig>({
  title: ' ',
  data: [],
  currentProject: {},
  expandOnClickNode: false,
  isFilterTree: true,
  treeNodeHandleClick: data => {
    // 设置当前选中项目信息
    TreeData.currentProject = data
    refreshData()
  }
})

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  dialogWidth: '500px',
  labelWidth: '100px',
  submit: (params: any) => {
    if (params.id) {
      patchDeviceTypeAttr(params.id, params).then(() => {
        ElMessage.success('修改成功')
        refForm.value?.closeDialog()
        refreshData()
      }).catch(error => {
        ElMessage.warning(error)
      })
    } else {
      postDeviceTypeAttr(params).then(() => {
        ElMessage.success('新增成功')
        refForm.value?.closeDialog()
        refreshData()
      }).catch(error => {
        ElMessage.warning(error)
      })
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          label: '所属类型',
          field: 'serialId',
          checkStrictly: true,
          options: computed(() => traverse(TreeData.data, 'children', { label: 'name', value: 'serialId' })) as any,
          readonly: true
        },
        {
          type: 'input-number',
          label: '属性编码',
          field: 'code',
          rules: [{ required: true, message: '请输入属性编码' }]
        },
        {
          type: 'input',
          label: '属性名称',
          field: 'name',
          rules: [{ required: true, message: '请输入属性名称' }]
        },
        {
          type: 'textarea',
          label: '备注/说明',
          field: 'remark'
        }

      ]
    }
  ]
})

const refreshData = async () => {
  const params = {
    serialId: TreeData.currentProject.serialId || '',
    ...refSearch.value?.queryParams
  }
  getDeviceTypeAttrSearch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.dataList.map(item => {
      item.category = TreeData.currentProject.name
      return item
    })
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

const clickCreatedRole = (title?: any) => {
  addOrUpdateConfig.title = title || '编辑'
  if (!TreeData.currentProject.serialId) {
    ElMessage.warning('请选中左侧类别树')
    return
  }
  if (TreeData.currentProject.level !== '3') {
    ElMessage.warning('请选择末级，分组无法添加')
    return
  }
  addOrUpdateConfig.defaultValue = { serialId: TreeData.currentProject.serialId }
  refForm.value?.openDialog()
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  addOrUpdateConfig.defaultValue = { ...(row) || {} }
  refForm.value?.openDialog()
}

const handleDelete = (row?: IListVideoItem) => {
  SLConfirm('确定删除指定类别属性?', '删除提示').then(() => {
    deleteDeviceTypeAttr(row?.id || '').then(() => {
      ElMessage.success('删除成功')
      refreshData()
    })
  })
}

function init() {
  getDeviceTypeTree().then(res => {
    TreeData.data = traverse(res?.data?.data || [])
    refreshData()
  })
}

onMounted(async () => {
  init()
})

</script>
