package org.thingsboard.server.dao.sql.version;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.AppVersionEntity;

import java.util.List;

public interface AppVersionRepository extends CrudRepository<AppVersionEntity, String>, JpaRepository<AppVersionEntity, String> {


    List<AppVersionEntity> findByAppKeyOrderByCreateTimeDesc(String appKey);

    List<AppVersionEntity> findByAppKeyAndTenantKeyOrderByCreateTimeDesc(String appKey, String tenantKey);
}
