<!-- 快速查询 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="快速查询"
    @map-loaded="onMaploaded"
  >
    <!-- <template #right-title>
      <SchemeHeader
        :title="'快速查询'"
        @scheme-click="scheme.openManagerDialog"
      ></SchemeHeader>
    </template> -->
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <SchemeManage
      :ref="scheme.getSchemeManageRef"
      :type="scheme.schemeType.value"
      @row-click="handleUseScheme"
    ></SchemeManage>
    <SaveScheme
      :ref="scheme.getSaveSchemeRef"
      @submit="handleSchemeSubmit"
    ></SaveScheme>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic'
import { getGraphicLayer, getLayerOids, getSubLayerIds } from '@/utils/MapHelper'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../common/RightDrawerMap.vue'
import { useScheme, useSketch } from '@/hooks/arcgis'
import SchemeManage from './Scheme/SchemeManage.vue'
import SaveScheme from './Scheme/SaveScheme.vue'
import SchemeHeader from './Scheme/SchemeHeader.vue'
import {QueryByPolygon} from '@/utils/geoserver/wfsUtils'

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()

const state = reactive<{
  tabs: any[]
  loading: boolean
  layerInfos: any[]
  layerIds: any[]
}>({
  tabs: [],
  layerInfos: [],
  layerIds: [],
  loading: false
})
const staticState: {
  view?: __esri.MapView
  graphics?: __esri.Graphic
  graphicsLayer?: __esri.GraphicsLayer
} = {}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    },
    {
      id: 'layer',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: false,
          label: '选择图层',
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              styles: {
                width: '100%'
              },
              loading: () => state.loading,
              click: () => startQuery()
            },
            // {
            //   perm: window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,
            //   text: '保存方案',
            //   styles: {
            //     width: '100%'
            //   },
            //   click: () => scheme.openSaveDialog()
            // }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})

const initDraw = (type: any) => {
  if (!staticState.view) return
  staticState.graphicsLayer?.removeAll()
  staticState.graphics = undefined
  sketch.value?.create(type)
}

const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphics = undefined
}

const getLayerInfo = () => {
  if(GIS_SERVER_SWITCH){
    const field = FormConfig.group.find(item => item.id === 'layer')?.fields[0] as IFormTree
    const layerInfo = staticState.view?.layerViews.items[0].layer.sublayers;
    let layers = layerInfo.items.map(item => {
      return {
        label: item.name,
        value: item.name,
        // data: item
      }
    });
    field.options = layers;// [{ label: '管线类', value: -2, children: layers }]
    refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
  } else {
    state.layerIds = getSubLayerIds(staticState.view)
    queryLayerClassName(state.layerIds).then(layerInfo => {      
      state.layerInfos = layerInfo.data?.result?.rows || []
      const field = FormConfig.group.find(item => item.id === 'layer')?.fields[0] as IFormTree
      const points = state.layerInfos
        .filter(item => item.geometrytype === 'esriGeometryPoint')
        .map(item => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          }
        })
      const lines = state.layerInfos
        .filter(item => item.geometrytype === 'esriGeometryPolyline')
        .map(item => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          }
        })
      field
        && (field.options = [
          { label: '管点类', value: -1, children: points },
          { label: '管线类', value: -2, children: lines }
        ])
      refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
    })
  }
}
const startQuery = async () => {
  SLMessage.info('正在查询，请稍候...')
  try {
    state.loading = true
    state.tabs.length = 0
    if(GIS_SERVER_SWITCH){
      const layerIds = refForm.value?.dataForm.layerid || []
      // const rings4326 = webMercatorUtils.webMercatorToGeographic(staticState.graphics?.geometry)
      debugger
      QueryByPolygon(layerIds.join(','),staticState.graphics?.geometry?.rings[0]).then(res => {
        let features = res.data.features;
        const uniqueValues = new Set(); // 使用Set来自动去重
        features.forEach(item => {
          if (item['id'].split('.')[0] !== undefined) {
            uniqueValues.add(item['id'].split('.')[0]);
          }
        });
        let tabs = Array.from(uniqueValues); // 将Set转换为数组
        tabs.forEach(tab => {
          let data = features.filter(item => item.id.split('.')[0] === tab)
          state.tabs.push({name:tab,label:`${tab}(${data.length})`,data:data})
        })
        refMap.value?.refreshDetail(state.tabs)
      })
    }else{
      const layerIds = refForm.value?.dataForm.layerid?.filter(item => item >= 0) || []
      state.tabs = await getLayerOids(layerIds, state.layerInfos, {
        where: '1=1',
        geometry: staticState.graphics?.geometry
      })
      refMap.value?.refreshDetail(state.tabs)
    }
  } catch (error) {
    console.log(error)
    SLMessage.error('查询失败')
  }
  state.loading = false
}
const { initSketch, destroySketch, sketch } = useSketch()
const resolveDrawEnd = (res: ISketchHandlerParameter) => {
  if (res.state === 'complete') {
    staticState.graphics = res.graphics[0]
    console.log(JSON.stringify(staticState.graphics))
  }
}
const onMaploaded = async view => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-quick',
    title: '快速查询'
  })
  initSketch(staticState.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  })
  setTimeout(() => {
    getLayerInfo()
  },1000)
}
const scheme = useScheme('quick')

const handleUseScheme = async (row: any) => {
  const detail = scheme.parseScheme(row)
  if (refForm.value?.dataForm) {
    refForm.value.dataForm.layerid = detail.layerid || []
  }
  if (detail.graphic) {
    staticState.graphics = Graphic.fromJSON(detail.graphic)
    sketch.value?.cancel()
    staticState.graphicsLayer?.removeAll()
    staticState.graphicsLayer?.add(staticState.graphics)
  }
  startQuery()
}
const handleSchemeSubmit = params => {
  scheme.submitScheme({
    ...params,
    type: scheme.schemeType.value,
    detail: JSON.stringify({
      layerid: refForm.value?.dataForm.layerid || [],
      graphic: staticState.graphics
    })
  })
}
onBeforeUnmount(() => {
  staticState.graphicsLayer && staticState.view?.map.remove(staticState.graphicsLayer)
  destroySketch()
})
</script>
<style lang="scss" scoped>
.right-title {
  width: 100%;

  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
