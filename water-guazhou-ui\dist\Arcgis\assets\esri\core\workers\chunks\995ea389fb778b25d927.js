"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9393,9880],{99880:(e,t,r)=>{r.d(t,{V:()=>l});var n=r(68773),s=(r(3172),r(20102)),i=r(92604),o=r(17452);const a=i.Z.getLogger("esri.assets");function l(e){if(!n.Z.assetsPath)throw a.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new s.Z("assets:path-not-set","config.assetsPath is not set");return(0,o.v_)(n.Z.assetsPath,e)}},25929:(e,t,r)=>{r.d(t,{M:()=>y,a:()=>v,e:()=>h,f:()=>i,i:()=>p,p:()=>d,r:()=>a,t:()=>o,w:()=>l});var n=r(70586),s=r(17452);function i(e,t){const r=t&&t.url&&t.url.path;if(e&&r&&(e=(0,s.hF)(e,r,{preserveProtocolRelative:!0}),t.portalItem&&t.readResourcePaths)){const r=(0,s.PF)(e,t.portalItem.itemUrl);null!=r&&c.test(r)&&t.readResourcePaths.push(t.portalItem.resourceFromPath(r).path)}return f(e,t&&t.portal)}function o(e,t,r=y.YES){if(null==e)return e;!(0,s.YP)(e)&&t&&t.blockedRelativeUrls&&t.blockedRelativeUrls.push(e);let n=(0,s.hF)(e);if(t){const r=t.verifyItemRelativeUrls&&t.verifyItemRelativeUrls.rootPath||t.url&&t.url.path;if(r){const i=f(r,t.portal),o=f(n,t.portal);n=(0,s.PF)(o,i,i),null!=n&&n!==o&&n!==e&&t.verifyItemRelativeUrls&&t.verifyItemRelativeUrls.writtenUrls.push(n)}}return n=h(n,t?.portal),(0,s.YP)(n)&&(n=(0,s.Fv)(n)),t?.resources&&t?.portalItem&&!(0,s.YP)(n)&&!(0,s.HK)(n)&&r===y.YES&&t.resources.toKeep.push({resource:t.portalItem.resourceFromPath(n),compress:!1}),n}function a(e,t,r){return i(e,r)}function l(e,t,r,n){const s=o(e,n);void 0!==s&&(t[r]=s)}const u=/\/items\/([^\/]+)\/resources\/(.*)/,c=/^\.\/resources\//;function p(e){return(e?.match(u)??null)?.[1]??null}function d(e){const t=e?.match(u)??null;if(null==t)return null;const r=t[2],i=r.lastIndexOf("/");if(-1===i){const{path:e,extension:t}=(0,s.fZ)(r);return{prefix:null,filename:e,extension:(0,n.Wg)(t)}}const{path:o,extension:a}=(0,s.fZ)(r.slice(i+1));return{prefix:r.slice(0,i),filename:o,extension:(0,n.Wg)(a)}}function h(e,t){return t&&!t.isPortal&&t.urlKey&&t.customBaseUrl?(0,s.Ie)(e,`${t.urlKey}.${t.customBaseUrl}`,t.portalHostname):e}function f(e,t){if(!t||t.isPortal||!t.urlKey||!t.customBaseUrl)return e;const r=`${t.urlKey}.${t.customBaseUrl}`,n=(0,s.TI)();return(0,s.D6)(n,`${n.scheme}://${r}`)?(0,s.Ie)(e,t.portalHostname,r):(0,s.Ie)(e,r,t.portalHostname)}var y,m;(m=y||(y={}))[m.YES=0]="YES",m[m.NO=1]="NO";const v=Object.freeze(Object.defineProperty({__proto__:null,get MarkKeep(){return y},ensureMainOnlineDomain:h,fromJSON:i,itemIdFromResourceUrl:p,prefixAndFilenameFromResourceUrl:d,read:a,toJSON:o,write:l},Symbol.toStringTag,{value:"Module"}))},32448:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(43697),s=r(15923),i=r(50758),o=r(52011);class a{constructor(){this._emitter=new a.EventEmitter(this)}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}}!function(e){class t{constructor(e=null){this._target=e,this._listenersMap=null}clear(){this._listenersMap&&this._listenersMap.clear(),this._listenersMap=null}emit(e,t){const r=this._listenersMap&&this._listenersMap.get(e);if(!r)return!1;const n=this._target||this;return[...r].forEach((e=>{e.call(n,t)})),r.length>0}on(e,t){if(Array.isArray(e)){const r=e.map((e=>this.on(e,t)));return(0,i.AL)(r)}if(e.includes(","))throw new TypeError("Evented.on() with a comma delimited string of event types is not supported");this._listenersMap||(this._listenersMap=new Map);const r=this._listenersMap.get(e)||[];return r.push(t),this._listenersMap.set(e,r),{remove:()=>{const r=this._listenersMap&&this._listenersMap.get(e)||[],n=r.indexOf(t);n>=0&&r.splice(n,1)}}}once(e,t){const r=this.on(e,(e=>{r.remove(),t.call(null,e)}));return r}hasEventListener(e){const t=this._listenersMap&&this._listenersMap.get(e);return null!=t&&t.length>0}}e.EventEmitter=t,e.EventedMixin=e=>{let r=class extends e{constructor(){super(...arguments),this._emitter=new t}destroy(){this._emitter.clear()}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}};return r=(0,n._)([(0,o.j)("esri.core.Evented")],r),r};let r=class extends s.Z{constructor(){super(...arguments),this._emitter=new a.EventEmitter(this)}destroy(){this._emitter.clear()}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}};r=(0,n._)([(0,o.j)("esri.core.Evented")],r),e.EventedAccessor=r}(a||(a={}));const l=a},10699:(e,t,r)=>{r.d(t,{IG:()=>o,iv:()=>a});var n=r(43697),s=r(52011);let i=0;const o=e=>{let t=class extends e{constructor(...e){super(...e),Object.defineProperty(this,"uid",{writable:!1,configurable:!1,value:Date.now().toString(16)+"-object-"+i++})}};return t=(0,n._)([(0,s.j)("esri.core.Identifiable")],t),t},a=e=>{let t=class extends e{constructor(...e){super(...e),Object.defineProperty(this,"uid",{writable:!1,configurable:!1,value:i++})}};return t=(0,n._)([(0,s.j)("esri.core.NumericIdentifiable")],t),t};let l=class extends(o(class{})){};l=(0,n._)([(0,s.j)("esri.core.Identifiable")],l)},10661:(e,t,r)=>{r.d(t,{s:()=>s});var n=r(42100);class s extends n.s{notify(){const e=this._observers;if(e&&e.length>0){const t=e.slice();for(const e of t)e.onInvalidated(),e.onCommitted()}}}},66643:(e,t,r)=>{r.d(t,{Ed:()=>u,UI:()=>c,mt:()=>f,q6:()=>h,vr:()=>y});var n=r(43697),s=r(15923),i=r(70586),o=r(95330),a=r(5600),l=(r(75215),r(67676),r(52011));function u(e,t,r){return(0,o.as)(e.map(((e,n)=>t.apply(r,[e,n]))))}async function c(e,t,r){return(await(0,o.as)(e.map(((e,n)=>t.apply(r,[e,n]))))).map((e=>e.value))}function p(e){return{ok:!0,value:e}}function d(e){return{ok:!1,error:e}}async function h(e){if((0,i.Wi)(e))return{ok:!1,error:new Error("no promise provided")};try{return p(await e)}catch(e){return d(e)}}async function f(e){try{return p(await e)}catch(e){return(0,o.r9)(e),d(e)}}function y(e,t){return new m(e,t)}let m=class extends s.Z{get value(){return e=this._result,(0,i.pC)(e)&&!0===e.ok?e.value:null;var e}get error(){return e=this._result,(0,i.pC)(e)&&!1===e.ok?e.error:null;var e}get finished(){return(0,i.pC)(this._result)}constructor(e,t){super({}),this._result=null,this._abortHandle=null,this.abort=()=>{this._abortController=(0,i.IM)(this._abortController)},this.remove=this.abort,this._abortController=new AbortController;const{signal:r}=this._abortController;this.promise=e(r),this.promise.then((e=>{this._result=p(e),this._cleanup()}),(e=>{this._result=d(e),this._cleanup()})),this._abortHandle=(0,o.fu)(t,this.abort)}normalizeCtorArgs(){return{}}destroy(){this.abort()}_cleanup(){this._abortHandle=(0,i.hw)(this._abortHandle),this._abortController=null}};(0,n._)([(0,a.Cb)()],m.prototype,"value",null),(0,n._)([(0,a.Cb)()],m.prototype,"error",null),(0,n._)([(0,a.Cb)()],m.prototype,"finished",null),(0,n._)([(0,a.Cb)()],m.prototype,"promise",void 0),(0,n._)([(0,a.Cb)()],m.prototype,"_result",void 0),m=(0,n._)([(0,l.j)("esri.core.asyncUtils.ReactiveTask")],m)},94443:(e,t,r)=>{r.d(t,{ME:()=>h,Su:()=>f,tz:()=>d});var n=r(20102),s=r(95330),i=r(70171);const o=/^([a-z]{2})(?:[-_]([A-Za-z]{2}))?$/,a={ar:!0,bg:!0,bs:!0,ca:!0,cs:!0,da:!0,de:!0,el:!0,en:!0,es:!0,et:!0,fi:!0,fr:!0,he:!0,hr:!0,hu:!0,id:!0,it:!0,ja:!0,ko:!0,lt:!0,lv:!0,nb:!0,nl:!0,pl:!0,"pt-BR":!0,"pt-PT":!0,ro:!0,ru:!0,sk:!0,sl:!0,sr:!0,sv:!0,th:!0,tr:!0,uk:!0,vi:!0,"zh-CN":!0,"zh-HK":!0,"zh-TW":!0};function l(e){return a[e]??!1}const u=[],c=new Map;function p(e){for(const t of c.keys())y(e.pattern,t)&&c.delete(t)}function d(e){return u.includes(e)||(p(e),u.unshift(e)),{remove(){const t=u.indexOf(e);t>-1&&(u.splice(t,1),p(e))}}}async function h(e){const t=(0,i.Kd)();c.has(e)||c.set(e,async function(e,t){const r=[];for(const n of u)if(y(n.pattern,e))try{return await n.fetchMessageBundle(e,t)}catch(e){r.push(e)}if(r.length)throw new n.Z("intl:message-bundle-error",`Errors occurred while loading "${e}"`,{errors:r});throw new n.Z("intl:no-message-bundle-loader",`No loader found for message bundle "${e}"`)}(e,t));const r=c.get(e);return r&&await m.add(r),r}function f(e){if(!o.test(e))return null;const t=o.exec(e);if(null===t)return null;const[,r,n]=t,s=r+(n?"-"+n.toUpperCase():"");return l(s)?s:l(r)?r:null}function y(e,t){return"string"==typeof e?t.startsWith(e):e.test(t)}(0,i.Ze)((()=>{c.clear()}));const m=new class{constructor(){this._numLoading=0,this._dfd=null}async waitForAll(){this._dfd&&await this._dfd.promise}add(e){return this._increase(),e.then((()=>this._decrease()),(()=>this._decrease())),this.waitForAll()}_increase(){this._numLoading++,this._dfd||(this._dfd=(0,s.dD)())}_decrease(){this._numLoading=Math.max(this._numLoading-1,0),this._dfd&&0===this._numLoading&&(this._dfd.resolve(),this._dfd=null)}}},87085:(e,t,r)=>{r.d(t,{Z:()=>_});var n=r(43697),s=(r(66577),r(3172)),i=r(20102),o=r(32448),a=r(10699),l=r(83379),u=r(92604),c=r(95330),p=r(17452),d=r(5600),h=(r(75215),r(67676),r(52011)),f=r(68773),y=r(6570),m=r(82971);let v=0,b=class extends(o.Z.EventedMixin((0,a.IG)(l.Z))){constructor(){super(...arguments),this.attributionDataUrl=null,this.fullExtent=new y.Z(-180,-90,180,90,m.Z.WGS84),this.id=Date.now().toString(16)+"-layer-"+v++,this.legendEnabled=!0,this.listMode="show",this.opacity=1,this.parent=null,this.popupEnabled=!0,this.attributionVisible=!0,this.spatialReference=m.Z.WGS84,this.title=null,this.type=null,this.url=null,this.visible=!0}static async fromArcGISServerUrl(e){const t="string"==typeof e?{url:e}:e;return(await r.e(3529).then(r.bind(r,63529))).fromUrl(t)}static fromPortalItem(e){return async function(e){const t="portalItem"in e?e:{portalItem:e},n=await r.e(8008).then(r.bind(r,28008));try{return await n.fromItem(t)}catch(e){const r=t&&t.portalItem,n=r&&r.id||"unset",s=r&&r.portal&&r.portal.url||f.Z.portalUrl;throw u.Z.getLogger("esri.layers.support.fromPortalItem").error("#fromPortalItem()","Failed to create layer from portal item (portal: '"+s+"', id: '"+n+"')",e),e}}(e)}initialize(){this.when().catch((e=>{(0,c.D_)(e)||u.Z.getLogger(this.declaredClass).error("#load()",`Failed to load layer (title: '${this.title??"no title"}', id: '${this.id??"no id"}')`,{error:e})}))}destroy(){if(this.parent){const e=this,t=this.parent;"layers"in t&&t.layers.includes(e)?t.layers.remove(e):"tables"in t&&t.tables.includes(e)?t.tables.remove(e):"baseLayers"in t&&t.baseLayers.includes(e)?t.baseLayers.remove(e):"baseLayers"in t&&t.referenceLayers.includes(e)&&t.referenceLayers.remove(e)}}get hasAttributionData(){return null!=this.attributionDataUrl}get parsedUrl(){return(0,p.mN)(this.url)}async fetchAttributionData(){const e=this.attributionDataUrl;if(this.hasAttributionData&&e)return(await(0,s.default)(e,{query:{f:"json"},responseType:"json"})).data;throw new i.Z("layer:no-attribution-data","Layer does not have attribution data")}};(0,n._)([(0,d.Cb)({type:String})],b.prototype,"attributionDataUrl",void 0),(0,n._)([(0,d.Cb)({type:y.Z})],b.prototype,"fullExtent",void 0),(0,n._)([(0,d.Cb)({readOnly:!0})],b.prototype,"hasAttributionData",null),(0,n._)([(0,d.Cb)({type:String,clonable:!1})],b.prototype,"id",void 0),(0,n._)([(0,d.Cb)({type:Boolean,nonNullable:!0})],b.prototype,"legendEnabled",void 0),(0,n._)([(0,d.Cb)({type:["show","hide","hide-children"]})],b.prototype,"listMode",void 0),(0,n._)([(0,d.Cb)({type:Number,range:{min:0,max:1},nonNullable:!0})],b.prototype,"opacity",void 0),(0,n._)([(0,d.Cb)({clonable:!1})],b.prototype,"parent",void 0),(0,n._)([(0,d.Cb)({readOnly:!0})],b.prototype,"parsedUrl",null),(0,n._)([(0,d.Cb)({type:Boolean})],b.prototype,"popupEnabled",void 0),(0,n._)([(0,d.Cb)({type:Boolean})],b.prototype,"attributionVisible",void 0),(0,n._)([(0,d.Cb)({type:m.Z})],b.prototype,"spatialReference",void 0),(0,n._)([(0,d.Cb)({type:String})],b.prototype,"title",void 0),(0,n._)([(0,d.Cb)({readOnly:!0,json:{read:!1}})],b.prototype,"type",void 0),(0,n._)([(0,d.Cb)()],b.prototype,"url",void 0),(0,n._)([(0,d.Cb)({type:Boolean,nonNullable:!0})],b.prototype,"visible",void 0),b=(0,n._)([(0,h.j)("esri.layers.Layer")],b);const _=b},66677:(e,t,r)=>{r.d(t,{B5:()=>p,DR:()=>h,G:()=>g,M8:()=>m,Nm:()=>v,Qc:()=>d,XG:()=>b,a7:()=>y,ld:()=>f,wH:()=>_});var n=r(70586),s=r(17452),i=r(25929);const o={mapserver:"MapServer",imageserver:"ImageServer",featureserver:"FeatureServer",sceneserver:"SceneServer",streamserver:"StreamServer",vectortileserver:"VectorTileServer"},a=Object.values(o),l=new RegExp(`^((?:https?:)?\\/\\/\\S+?\\/rest\\/services\\/(.+?)\\/(${a.join("|")}))(?:\\/(?:layers\\/)?(\\d+))?`,"i"),u=new RegExp(`^((?:https?:)?\\/\\/\\S+?\\/([^\\/\\n]+)\\/(${a.join("|")}))(?:\\/(?:layers\\/)?(\\d+))?`,"i"),c=/(.*?)\/(?:layers\/)?(\d+)\/?$/i;function p(e){return!!l.test(e)}function d(e){if((0,n.Wi)(e))return null;const t=(0,s.mN)(e),r=t.path.match(l)||t.path.match(u);if(!r)return null;const[,i,a,c,p]=r,d=a.indexOf("/");return{title:f(-1!==d?a.slice(d+1):a),serverType:o[c.toLowerCase()],sublayer:null!=p&&""!==p?parseInt(p,10):null,url:{path:i}}}function h(e){const t=(0,s.mN)(e).path.match(c);return t?{serviceUrl:t[1],sublayerId:Number(t[2])}:null}function f(e){return(e=e.replace(/\s*[/_]+\s*/g," "))[0].toUpperCase()+e.slice(1)}function y(e,t){const r=[];if(e){const t=d(e);(0,n.pC)(t)&&t.title&&r.push(t.title)}if(t){const e=f(t);r.push(e)}if(2===r.length){if(r[0].toLowerCase().includes(r[1].toLowerCase()))return r[0];if(r[1].toLowerCase().includes(r[0].toLowerCase()))return r[1]}return r.join(" - ")}function m(e){if(!e)return!1;const t=(e=e.toLowerCase()).includes(".arcgis.com/"),r=e.includes("//services")||e.includes("//tiles")||e.includes("//features");return t&&r}function v(e,t){return e?(0,s.Qj)((0,s.Hu)(e,t)):e}function b(e){let{url:t}=e;if(!t)return{url:t};t=(0,s.Hu)(t,e.logger);const r=(0,s.mN)(t),i=d(r.path);let o;if((0,n.pC)(i))null!=i.sublayer&&null==e.layer.layerId&&(o=i.sublayer),t=i.url.path;else if(e.nonStandardUrlAllowed){const e=h(r.path);(0,n.pC)(e)&&(t=e.serviceUrl,o=e.sublayerId)}return{url:(0,s.Qj)(t),layerId:o}}function _(e,t,r,n,o){(0,i.w)(t,n,"url",o),n.url&&null!=e.layerId&&(n.url=(0,s.v_)(n.url,r,e.layerId.toString()))}function g(e){if(!e)return!1;const t=e.toLowerCase(),r=t.includes("/services/"),n=t.includes("/mapserver/wmsserver"),s=t.includes("/imageserver/wmsserver"),i=t.includes("/wmsserver");return r&&(n||s||i)}},84230:(e,t,r)=>{r.d(t,{A2:()=>a,S1:()=>p,fb:()=>o,ln:()=>d,oP:()=>c,rQ:()=>l,y2:()=>u});var n=r(40330),s=r(3172),i=r(70586);const o={Point:"SceneLayer","3DObject":"SceneLayer",IntegratedMesh:"IntegratedMeshLayer",PointCloud:"PointCloudLayer",Building:"BuildingSceneLayer"};function a(e){const t=e?.type;return"building-scene"===t||"integrated-mesh"===t||"point-cloud"===t||"scene"===t}function l(e){return"feature"===e?.type&&!e.url&&"memory"===e.source?.type}function u(e){return"feature"===e?.type&&"feature-layer"===e.source?.type}async function c(e,t){const r=n.id?.findServerInfo(e);if(null!=r?.currentVersion)return r.owningSystemUrl||null;const o=e.toLowerCase().indexOf("/rest/services");if(-1===o)return null;const a=`${e.substring(0,o)}/rest/info`,l=(0,i.pC)(t)?t.signal:null,{data:u}=await(0,s.default)(a,{query:{f:"json"},responseType:"json",signal:l});return u?.owningSystemUrl||null}function p(e){return function(e){if(!("capabilities"in e))return!1;switch(e.type){case"csv":case"feature":case"geojson":case"imagery":case"knowledge-graph-sublayer":case"ogc-feature":case"oriented-imagery":case"scene":case"subtype-group":case"subtype-sublayer":case"wfs":return!0;default:return!1}}(e)?"effectiveCapabilities"in e?e.effectiveCapabilities:e.capabilities:null}function d(e){return!!function(e){if(!("editingEnabled"in e))return!1;switch(e.type){case"csv":case"feature":case"geojson":case"oriented-imagery":case"scene":case"subtype-group":case"subtype-sublayer":return!0;default:return!1}}(e)&&("effectiveEditingEnabled"in e?e.effectiveEditingEnabled:e.editingEnabled)}},14661:(e,t,r)=>{r.d(t,{$o:()=>u,Kz:()=>c,Ss:()=>p,_$:()=>a,ck:()=>l,qj:()=>o});var n=r(44547),s=r(82971),i=r(40488);function o(e,t){if(!a(e,t)){const r=e.typeKeywords;r?r.push(t):e.typeKeywords=[t]}}function a(e,t){return!!e.typeKeywords?.includes(t)}function l(e,t){const r=e.typeKeywords;if(r){const e=r.indexOf(t);e>-1&&r.splice(e,1)}}async function u(e){const t=e.clone().normalize();let r;if(t.length>1)for(const e of t)r?e.width>r.width&&(r=e):r=e;else r=t[0];return async function(e){const t=e.spatialReference;if(t.isWGS84)return e.clone();if(t.isWebMercator)return(0,i.Sx)(e);const r=s.Z.WGS84;return await(0,n.iQ)(t,r),(0,n.iV)(e,r)}(r)}const c={DEVELOPER_BASEMAP:"DeveloperBasemap",JSAPI:"ArcGIS API for JavaScript",METADATA:"Metadata",MULTI_LAYER:"Multilayer",SINGLE_LAYER:"Singlelayer",TABLE:"Table"};function p(e){const{portal:t,isOrgItem:r,itemControl:n}=e,s=t.user?.privileges;let i=!s||s.includes("features:user:edit"),o=!!r&&!!s?.includes("features:user:fullEdit");const a="update"===n||"admin"===n;return a?o=i=!0:o&&(i=!0),{features:{edit:i,fullEdit:o},content:{updateItem:a}}}}}]);