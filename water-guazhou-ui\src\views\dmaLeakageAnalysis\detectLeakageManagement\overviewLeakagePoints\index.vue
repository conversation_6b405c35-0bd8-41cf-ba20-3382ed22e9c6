<template>
  <DrawerBox
    ref="refDrawerBox"
    :right-drawer="true"
    :bottom-drawer="true"
    :bottom-drawer-title="currentPartitionName + ' - 详细信息'"
    bottom-drawer-bar-position="left"
  >
    <!-- Right Drawer for Charts and Ranking -->
    <template #right>
      <div class="right-drawer-container">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>漏点原因分析</span>
            </div>
          </template>
          <div ref="pieChartContainer" class="chart-container"></div>
        </el-card>
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>每月漏点趋势</span>
            </div>
          </template>
          <div ref="barChartContainer" class="chart-container"></div>
        </el-card>
        <el-card class="box-card rank-card">
          <template #header>
            <div class="card-header">
              <span>分区漏点数量排行</span>
            </div>
          </template>
          <el-table :data="leakRankData" style="width: 100%" height="250">
            <el-table-column type="index" label="排行" width="60" />
            <el-table-column prop="partitionName" label="分区" />
            <el-table-column prop="leakCount" label="漏点数" sortable />
            <el-table-column label="操作" align="center" width="80">
              <template #default="scope">
                <el-button link type="primary" @click="handleViewDetails(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </template>
    
    <!-- Bottom Drawer for Details -->
    <template #bottom>
      <div>这里是 {{ currentPartitionName }} 的详细漏损数据和分析报告...</div>
    </template>

    <!-- Main Content: Map -->
    <ArcLayout ref="refArcLayout" @map-loaded="onMapLoaded"></ArcLayout>
  </DrawerBox>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import DrawerBox from '@/components/DrawerBox/DrawerBox.vue'
import ArcLayout from '@/components/arcMap/widgets/ArcLayout.vue'
import Graphic from '@arcgis/core/Graphic'
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol'
import Point from '@arcgis/core/geometry/Point'

// --- Refs for Components and DOM elements ---
const refDrawerBox = ref<InstanceType<typeof DrawerBox>>()
const refArcLayout = ref<InstanceType<typeof ArcLayout>>()
const pieChartContainer = ref<HTMLElement>()
const barChartContainer = ref<HTMLElement>()

// --- Reactive State ---
const staticsState: { view?: __esri.MapView } = {}
const currentPartitionName = ref('')

// --- Mock Data ---
const leakPoints = [
  { lng: 120.38, lat: 31.83, name: 'A区漏点01', reason: '管道老化' },
  { lng: 120.40, lat: 31.84, name: 'A区漏点02', reason: '外力破坏' },
  { lng: 120.35, lat: 31.80, name: 'B区漏点01', reason: '接口腐蚀' },
  { lng: 120.42, lat: 31.81, name: 'C区漏点01', reason: '管道老化' },
  { lng: 120.39, lat: 31.79, name: 'C区漏点02', reason: '管道老化' },
]

const leakRankData = [
  { partitionName: 'C区', leakCount: 2, pipeMaterial: '铸铁' },
  { partitionName: 'A区', leakCount: 2, pipeMaterial: 'PE' },
  { partitionName: 'B区', leakCount: 1, pipeMaterial: '球墨铸铁' },
  { partitionName: 'D区', leakCount: 0, pipeMaterial: 'PE' },
]

const pieChartData = {
  title: { text: '漏点原因', x: 'center' },
  tooltip: { trigger: 'item' },
  legend: { orient: 'vertical', left: 'left', data: ['管道老化', '外力破坏', '接口腐蚀'] },
  series: [{
    name: '原因分析',
    type: 'pie',
    radius: '50%',
    data: [
      { value: 3, name: '管道老化' },
      { value: 1, name: '外力破坏' },
      { value: 1, name: '接口腐蚀' },
    ],
  }],
};

const barChartData = {
  tooltip: { trigger: 'axis' },
  xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月'] },
  yAxis: { type: 'value' },
  series: [{ name: '漏点数', data: [2, 1, 3, 5, 4], type: 'bar' }],
};


// --- Methods ---
const initCharts = () => {
  if (pieChartContainer.value && barChartContainer.value) {
    const pieChart = echarts.init(pieChartContainer.value)
    const barChart = echarts.init(barChartContainer.value)
    pieChart.setOption({ series: [{ name: '原因分析', type: 'pie', radius: '60%', data: [ { value: 3, name: '管道老化' }, { value: 1, name: '外力破坏' }, { value: 1, name: '接口腐蚀' } ] }], title: { text: '漏点原因', x: 'center' }, tooltip: { trigger: 'item' }, legend: { orient: 'vertical', left: 'left', data: ['管道老化', '外力破坏', '接口腐蚀'] }})
    barChart.setOption({ series: [{ name: '漏点数', data: [2, 1, 3, 5, 4], type: 'bar' }], tooltip: { trigger: 'axis' }, xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月'] }, yAxis: { type: 'value' }})
  }
}

const addLeakPointsToMap = () => {
  const view = staticsState.view
  if (!view) return

  const markerSymbol = new SimpleMarkerSymbol({
    color: [226, 119, 40],
    outline: {
      color: [255, 255, 255],
      width: 2,
    },
  })

  leakPoints.forEach(p => {
    const point = new Point({ longitude: p.lng, latitude: p.lat });
    const graphic = new Graphic({
      geometry: point,
      symbol: markerSymbol,
      attributes: p
    });
    view.graphics.add(graphic);
  })
}

const handleViewDetails = (row) => {
  currentPartitionName.value = row.partitionName
  refDrawerBox.value?.toggleDrawer('btt', true)
  ElMessage.info(`拉起抽屉，展示 ${row.partitionName} 的详细信息`)
}

const onMapLoaded = async (view: __esri.MapView) => {
  staticsState.view = view
  addLeakPointsToMap()
}

onMounted(() => {
  refDrawerBox.value?.toggleDrawer('rtl', true)
  initCharts()
})
</script>

<style scoped>
.right-drawer-container {
  padding: 10px;
  height: 100%;
  overflow-y: auto;
}
.box-card {
  margin-bottom: 15px;
}
.card-header {
  font-weight: bold;
}
.chart-container {
  width: 100%;
  height: 220px;
}
</style> 