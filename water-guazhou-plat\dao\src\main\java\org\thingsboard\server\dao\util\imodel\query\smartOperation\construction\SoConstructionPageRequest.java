package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstruction;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SoConstructionPageRequest extends AdvancedPageableQueryEntity<SoConstruction, SoConstructionPageRequest> {
    // 所属项目编号
    private String projectCode;

    // 编号
    private String code;

    // 工程名称
    private String name;

    // 工程地址
    private String address;

}
