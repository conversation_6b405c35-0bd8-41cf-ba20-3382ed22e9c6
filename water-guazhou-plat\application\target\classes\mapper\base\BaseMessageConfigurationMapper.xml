<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseMessageConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseMessageConfiguration" id="BaseMessageConfigurationResult">
        <result property="id"    column="id"    />
        <result property="configName"    column="config_name"    />
        <result property="messageType"    column="message_type"    />
        <result property="platform"    column="platform"    />
        <result property="status"    column="status"    />
        <result property="titleTemplate"    column="title_template"    />
        <result property="contentTemplate"    column="content_template"    />
        <result property="encoding"    column="encoding"    />
        <result property="retryCount"    column="retry_count"    />
        <result property="retryInterval"    column="retry_interval"    />
        <result property="rateLimit"    column="rate_limit"    />
        <result property="isAsync"    column="is_async"    />
    </resultMap>

    <sql id="selectBaseMessageConfigurationVo">
        select id, config_name, message_type, platform, status, title_template, content_template, encoding, retry_count, retry_interval, rate_limit, is_async from base_message_configuration
    </sql>

    <select id="selectBaseMessageConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseMessageConfiguration" resultMap="BaseMessageConfigurationResult">
        <include refid="selectBaseMessageConfigurationVo"/>
        <where>  
            <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
            <if test="messageType != null  and messageType != ''"> and message_type = #{messageType}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="titleTemplate != null  and titleTemplate != ''"> and title_template = #{titleTemplate}</if>
            <if test="contentTemplate != null  and contentTemplate != ''"> and content_template = #{contentTemplate}</if>
            <if test="encoding != null  and encoding != ''"> and encoding = #{encoding}</if>
            <if test="retryCount != null  and retryCount != ''"> and retry_count = #{retryCount}</if>
            <if test="retryInterval != null  and retryInterval != ''"> and retry_interval = #{retryInterval}</if>
            <if test="rateLimit != null  and rateLimit != ''"> and rate_limit = #{rateLimit}</if>
            <if test="isAsync != null  and isAsync != ''"> and is_async = #{isAsync}</if>
        </where>
    </select>
    
    <select id="selectBaseMessageConfigurationById" parameterType="String" resultMap="BaseMessageConfigurationResult">
        <include refid="selectBaseMessageConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseMessageConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseMessageConfiguration">
        insert into base_message_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="configName != null">config_name,</if>
            <if test="messageType != null">message_type,</if>
            <if test="platform != null">platform,</if>
            <if test="status != null">status,</if>
            <if test="titleTemplate != null">title_template,</if>
            <if test="contentTemplate != null">content_template,</if>
            <if test="encoding != null">encoding,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="retryInterval != null">retry_interval,</if>
            <if test="rateLimit != null">rate_limit,</if>
            <if test="isAsync != null">is_async,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="configName != null">#{configName},</if>
            <if test="messageType != null">#{messageType},</if>
            <if test="platform != null">#{platform},</if>
            <if test="status != null">#{status},</if>
            <if test="titleTemplate != null">#{titleTemplate},</if>
            <if test="contentTemplate != null">#{contentTemplate},</if>
            <if test="encoding != null">#{encoding},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="retryInterval != null">#{retryInterval},</if>
            <if test="rateLimit != null">#{rateLimit},</if>
            <if test="isAsync != null">#{isAsync},</if>
         </trim>
    </insert>

    <update id="updateBaseMessageConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseMessageConfiguration">
        update base_message_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="configName != null">config_name = #{configName},</if>
            <if test="messageType != null">message_type = #{messageType},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="status != null">status = #{status},</if>
            <if test="titleTemplate != null">title_template = #{titleTemplate},</if>
            <if test="contentTemplate != null">content_template = #{contentTemplate},</if>
            <if test="encoding != null">encoding = #{encoding},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="retryInterval != null">retry_interval = #{retryInterval},</if>
            <if test="rateLimit != null">rate_limit = #{rateLimit},</if>
            <if test="isAsync != null">is_async = #{isAsync},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseMessageConfigurationById" parameterType="String">
        delete from base_message_configuration where id = #{id}
    </delete>

    <delete id="deleteBaseMessageConfigurationByIds" parameterType="String">
        delete from base_message_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>