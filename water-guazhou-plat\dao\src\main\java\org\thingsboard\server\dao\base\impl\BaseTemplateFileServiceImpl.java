package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseTemplateFileService;
import org.thingsboard.server.dao.model.sql.base.BaseTemplateFile;
import org.thingsboard.server.dao.sql.base.BaseTemplateFileMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTemplateFilePageRequest;

/**
 * 平台管理-模型文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class BaseTemplateFileServiceImpl implements IBaseTemplateFileService {

    @Autowired
    private BaseTemplateFileMapper baseTemplateFileMapper;

    /**
     * 查询平台管理-模型文件
     *
     * @param id 平台管理-模型文件主键
     * @return 平台管理-模型文件
     */
    @Override
    public BaseTemplateFile selectBaseTemplateFileById(String id) {
        return baseTemplateFileMapper.selectBaseTemplateFileById(id);
    }

    /**
     * 查询平台管理-模型文件列表
     *
     * @param baseTemplateFile 平台管理-模型文件
     * @return 平台管理-模型文件
     */
    @Override
    public IPage<BaseTemplateFile> selectBaseTemplateFileList(BaseTemplateFilePageRequest baseTemplateFile) {
        return baseTemplateFileMapper.selectBaseTemplateFileList(baseTemplateFile);
    }

    /**
     * 新增平台管理-模型文件
     *
     * @param baseTemplateFile 平台管理-模型文件
     * @return 结果
     */
    @Override
    public int insertBaseTemplateFile(BaseTemplateFile baseTemplateFile) {
        baseTemplateFile.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseTemplateFileMapper.insertBaseTemplateFile(baseTemplateFile);
    }

    /**
     * 修改平台管理-模型文件
     *
     * @param baseTemplateFile 平台管理-模型文件
     * @return 结果
     */
    @Override
    public int updateBaseTemplateFile(BaseTemplateFile baseTemplateFile) {
        return baseTemplateFileMapper.updateBaseTemplateFile(baseTemplateFile);
    }

    /**
     * 批量删除平台管理-模型文件
     *
     * @param ids 需要删除的平台管理-模型文件主键
     * @return 结果
     */
    @Override
    public int deleteBaseTemplateFileByIds(List<String> ids) {
        return baseTemplateFileMapper.deleteBaseTemplateFileByIds(ids);
    }

    /**
     * 删除平台管理-模型文件信息
     *
     * @param id 平台管理-模型文件主键
     * @return 结果
     */
    @Override
    public int deleteBaseTemplateFileById(String id) {
        return baseTemplateFileMapper.deleteBaseTemplateFileById(id);
    }
}
