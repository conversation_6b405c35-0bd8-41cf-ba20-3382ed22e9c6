package org.thingsboard.server.dao.orderWork;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.DTO.TreeNodeDTO;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderType;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderTypeMapper;
import org.thingsboard.server.dao.util.TreeUtil;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkOrderTypeServiceImpl implements WorkOrderTypeService {

    @Autowired
    private WorkOrderTypeMapper workOrderTypeMapper;

    @Override
    public List<TreeNodeDTO> findList(String status, TenantId tenantId) {
        List<WorkOrderType> list = workOrderTypeMapper.findList(status, UUIDConverter.fromTimeUUID(tenantId.getId()));
        List<TreeNodeDTO> treeNodeList = list.stream()
                .map(type -> TreeNodeDTO.builder().id(type.getId()).parentId(type.getPid()).name(type.getName()).build())
                .collect(Collectors.toList());

        return TreeUtil.listToTree(treeNodeList, "0");
    }

    @Override
    public void save(WorkOrderType entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            entity.setStatus("1");

            workOrderTypeMapper.insert(entity);
        } else {
            workOrderTypeMapper.updateById(entity);
        }
    }

    @Override
    public void changeStatus(String status, String id) {
        WorkOrderType resource = workOrderTypeMapper.selectById(id);
        if (resource != null) {
            resource.setStatus(status);
            workOrderTypeMapper.updateById(resource);
        }
    }

    @Override
    public List<TreeNodeDTO> findByResourceId(String resourceId) {
        List<WorkOrderType> list = workOrderTypeMapper.findByResourceId(resourceId);
        List<TreeNodeDTO> treeNodeList = list.stream()
                .map(type -> TreeNodeDTO.builder().id(type.getId()).parentId(type.getPid()).name(type.getName()).build())
                .collect(Collectors.toList());

        return TreeUtil.listToTree(treeNodeList, "0");
    }

    @Override
    public List<String> findTypeIdByResourceId(String resourceId) {
        List<WorkOrderType> list = workOrderTypeMapper.findByResourceId(resourceId);
        return list.stream().map(WorkOrderType::getId).collect(Collectors.toList());
    }

}
