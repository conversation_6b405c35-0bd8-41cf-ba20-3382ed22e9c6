import{d as T,c as O,r as R,b as f,Q as B,g as M,h as A,F as C,q as S,i as g,_ as J,X as q}from"./index-r0dFAfgr.js";import{g as k}from"./MapView-DaoQedLH.js";import{s as G}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import{i as V,e as $}from"./IdentifyHelper-RJWmLn49.js";import{g as j,a as H}from"./LayerHelper-Cn-iiqxI.js";import"./Point-WxyopZva.js";import"./project-DUuzYgGl.js";import{G as W}from"./pipe-nogVzCHG.js";import{s as h}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{u as Q}from"./useScheme-DcjSAE44.js";import"./geometryEngineBase-BhsKaODW.js";import{e as U,c as z,g as K}from"./geoserverUtils-wjOSMa7E.js";import X from"./RightDrawerMap-D5PhmGFO.js";import Y from"./SchemeHeader-BLYQTCg3.js";import{_ as Z}from"./SchemeManage.vue_vue_type_script_setup_true_lang-fv9Irhyi.js";import{_ as ee}from"./SaveScheme.vue_vue_type_script_setup_true_lang-Bt-6iBz5.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";const ur=T({__name:"LineSearch",setup(te){const w=O(),u=O(),a=R({tabs:[],curOperate:"",layerInfos:[],layerIds:[]}),e={queryParams:{geometry:void 0,where:void 0},identifyResults:[]},I=R({group:[{fieldset:{desc:"依次点选两个节点后进行查询"},fields:[{type:"tree",options:[],checkStrictly:!1,label:"选择图层",showCheckbox:!0,field:"layerid",nodeKey:"value"},{type:"btn-group",itemContainerStyle:{marginBottom:"8px"},btns:[{perm:!0,type:"warning",text:()=>a.curOperate==="picking"?"拾取节点中...":"拾取节点",styles:{width:"100%"},loading:()=>a.curOperate==="picking",click:()=>F()}]},{type:"btn-group",itemContainerStyle:{marginBottom:"8px"},btns:[{perm:!0,text:()=>a.curOperate==="searching"?"查询中，请稍候...":"查询",styles:{width:"50%"},loading:()=>a.curOperate==="searching",disabled:()=>a.curOperate==="picking",click:()=>b()},{perm:window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,text:"保存方案",styles:{width:"100%"},click:()=>D()},{perm:!0,text:"清除",styles:{width:"50%"},type:"danger",disabled:()=>a.curOperate==="searching",click:()=>_()}]}]}],labelPosition:"top",gutter:12}),F=()=>{var t,r;e.view&&(_(),a.curOperate="picking",h("crosshair"),(t=e.graphicsLayer)==null||t.removeAll(),e.identifyResults=[],e.mapClick=(r=e.view)==null?void 0:r.on("click",async s=>{await L(s)}))},L=async t=>{var r,s,c,p;if(e.view)try{let o;if(window.GIS_SERVER_SWITCH){const i=await U(e.view,"/geoserver/guazhou/wms","节点",t);if(!i||!i.data||!i.data.features||!i.data.features.length){f.warning("没有查询到节点");return}const n=i.data.features[0],m=z(n.geometry);o=new k({geometry:m,attributes:n.properties||{},symbol:G(m.type,{color:[255,0,0,1]})})}else{const i=V({layerIds:[1],geometry:t.mapPoint,mapExtent:e.view.extent}),n=await $(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,i);if(!((r=n.results)!=null&&r.length)){f.warning("没有查询到节点");return}o=n.results[0].feature,o.symbol=G(o.geometry.type,{color:[255,0,0,1]})}e.identifyResults.push(o),(s=e.graphicsLayer)==null||s.add(o),e.identifyResults.length>=2&&((c=e.mapClick)==null||c.remove(),a.curOperate="",h(""))}catch(o){console.error("节点查询失败:",o),(p=e.view)==null||p.graphics.removeAll()}},b=async()=>{var r,s,c;if(e.identifyResults.length!==2){f.warning("请依次选取两个节点后再试");return}const t=((r=u.value)==null?void 0:r.dataForm.layerid)||[];if(!(t!=null&&t.length)){f.warning("请选择要查询的图层");return}a.curOperate="searching",h("progress");try{const p=a.layerInfos.filter(i=>t.indexOf(i.layerid)!==-1)||[];let o;if(window.GIS_SERVER_SWITCH){const i=e.identifyResults.map(l=>l.geometry.toJSON()),n=e.identifyResults.map(l=>l.attributes.id||l.attributes.OBJECTID||l.id||"").join(","),m=(s=p.find(l=>l.geometrytype==="esriGeometryPoint"||l.layername.includes("点")))==null?void 0:s.layername;console.log("节点几何信息:",i),console.log("节点ID:",n),console.log("节点图层名称:",m),o=await K({nodeGeometries:i,flagOIDs:n,nodeLayerName:m,outputLayers:t.join(",")})}else o=await W({flagOIDs:e.identifyResults.map(i=>i.attributes.OBJECTID).join(","),outputLayers:p.map(i=>i.layername).join(",")});if(!o.data.result)o.data.code===1e4?f.warning(o.data.message):f.error(o.data.message||"查询失败");else{const i=p.map(n=>{var l;const m=o.data.result.find(v=>v.layeralias===n.layername);return{label:n.layername+"("+(((l=m==null?void 0:m.queryresult)==null?void 0:l.length)||0)+")",name:n.layername,data:(m==null?void 0:m.queryresult)||[]}});(c=w.value)==null||c.refreshDetail(i)}}catch(p){f.error("查询失败"),console.error("沿线查询失败:",p)}h(""),a.curOperate=""},x=()=>{var t,r,s,c,p;if(window.GIS_SERVER_SWITCH){const o=I.group[0].fields[0],i=(p=(c=(s=(r=(t=e.view)==null?void 0:t.layerViews)==null?void 0:r.items)==null?void 0:s[0])==null?void 0:c.layer)==null?void 0:p.sublayers;if(i&&i.items){let n=i.items.map(m=>({label:m.name,value:m.name}));o.options=n,u.value&&(u.value.dataForm.layerid=a.layerIds)}}else a.layerIds=H(e.view),q(a.layerIds).then(o=>{var l,v;a.layerInfos=((v=(l=o.data)==null?void 0:l.result)==null?void 0:v.rows)||[];const i=I.group[0].fields[0],n=a.layerInfos.filter(y=>y.geometrytype==="esriGeometryPoint").map(y=>({label:y.layername,value:y.layerid,data:y})),m=a.layerInfos.filter(y=>y.geometrytype==="esriGeometryPolyline").map(y=>({label:y.layername,value:y.layerid,data:y}));i&&(i.options=[{label:"管点类",value:-1,children:n},{label:"管线类",value:-2,children:m}]),u.value&&(u.value.dataForm.layerid=a.layerIds)})},_=()=>{var t,r,s;(t=e.mapClick)==null||t.remove(),e.identifyResults=[],(r=e.graphicsLayer)==null||r.removeAll(),(s=w.value)==null||s.clearDetailData(),a.curOperate="",h("")},d=Q("line"),D=()=>{if(e.identifyResults.length<2){f.warning("请先在地图上选择两个节点");return}d.openSaveDialog()},N=async t=>{var s,c,p;_();const r=d.parseScheme(t);(s=u.value)!=null&&s.dataForm&&(u.value.dataForm.layerid=r.layerid||[]),r.identifyResults.length&&(e.identifyResults=r.identifyResults.map(o=>k.fromJSON(o)),(c=e.graphicsLayer)==null||c.removeAll(),(p=e.graphicsLayer)==null||p.addMany(e.identifyResults)),b()},E=t=>{var r;d.submitScheme({...t,type:d.schemeType.value,detail:JSON.stringify({layerid:((r=u.value)==null?void 0:r.dataForm.layerid)||[],identifyResults:e.identifyResults})})},P=t=>{e.view=t,e.graphicsLayer=j(e.view,{id:"search-line",title:"沿线查询"}),setTimeout(()=>{x()},1e3)};return B(()=>{var t,r;(t=e.mapClick)==null||t.remove(),(r=e.graphicsLayer)==null||r.removeAll()}),(t,r)=>{const s=J;return M(),A(X,{ref_key:"refMap",ref:w,title:"沿线查询",onMapLoaded:P,onDetailRefreshed:r[0]||(r[0]=c=>g(a).curOperate="")},{"right-title":C(()=>[S(Y,{title:"沿线查询",onSchemeClick:g(d).openManagerDialog},null,8,["onSchemeClick"])]),default:C(()=>[S(s,{ref_key:"refForm",ref:u,config:g(I)},null,8,["config"]),S(Z,{ref:g(d).getSchemeManageRef,type:g(d).schemeType.value,onRowClick:N},null,8,["type"]),S(ee,{ref:g(d).getSaveSchemeRef,onSubmit:E},null,512)]),_:1},512)}}});export{ur as default};
