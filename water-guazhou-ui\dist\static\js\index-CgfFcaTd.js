import{_ as x}from"./TreeBox-DDD2iwoR.js";import{_ as D}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as C}from"./CardTable-rdWOL4_6.js";import{_ as T}from"./CardSearch-CB_HNR-Q.js";import{_ as k}from"./index-BJ-QPYom.js";import{d as S,c as q,r as s,s as u,S as f,b as d,o as F,g as L,h as B,F as m,q as c,i as p,ak as M,bM as V,bq as j,C as I}from"./index-r0dFAfgr.js";import{f as N}from"./DateFormatter-Bm9a68Ax.js";import{s as P,g as Y,d as w}from"./dict-C6KyvrXS.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const H=S({__name:"index",setup(W){const n=q(),_=s({filters:[{type:"btn-group",btns:[{text:"新增",perm:!0,svgIcon:u(M),click:()=>{var e;r.defaultValue={type:l.currentProject.value},r.title="新增",(e=n.value)==null||e.openDialog()}}]}]}),a=s({loading:!1,dataList:[],indexVisible:!0,columns:[{label:"代码类型编号",prop:"code"},{label:"代码类型",prop:"type",formatter:e=>e.type==="1"?"成本类型":""},{label:"字典名称",prop:"label"},{label:"字典值",prop:"value"},{label:"排序",prop:"orderNum"},{label:"创建时间",prop:"createTime",formatter:e=>N(e.createTime,"YYYY-MM-DD HH:mm:ss")}],operations:[{text:"编辑",isTextBtn:!0,perm:!0,svgIcon:u(V),click:e=>{var t;r.title="编辑",r.defaultValue={...e},(t=n.value)==null||t.openDialog()}},{perm:!0,text:"删除",isTextBtn:!0,type:"danger",svgIcon:u(j),click:e=>g(e)}],operationWidth:"200px",pagination:{refreshData:({page:e,size:t})=>{a.pagination.limit=t,a.pagination.page=e,i()}}}),r=s({dialogWidth:500,title:"新增字典",defaultValue:{},submit:e=>{console.log(e),f("确定提交？","提示信息").then(async()=>{var t;r.submitting=!0;try{P(e).then(o=>{o.data.code===200&&d.success("提交成功")}),await i(),(t=n.value)==null||t.closeDialog()}catch(o){d.error("提交失败"),console.dir(o)}r.submitting=!1})},group:[{fields:[{type:"input",label:"代码分类",field:"code",rules:[{required:!0,message:"请填写代码分类"}]},{type:"input",label:"字典名称",field:"label",rules:[{required:!0,message:"请填写字典名称"}]},{type:"input",label:"字典值",field:"value",rules:[{required:!0,message:"请填写字典值"}]},{type:"number",label:"排序",field:"orderNum"}]}]}),l=s({title:"字典管理",currentProject:{},data:[{value:1,label:"成本类型"}],isFilterTree:!0,treeNodeHandleClick:e=>{l.currentProject=e,a.pagination.page=1,i()}}),i=async()=>{const e={page:a.pagination.page||1,size:a.pagination.limit||20,type:l.currentProject.value};Y(e).then(t=>{if(t.data.code===200){console.log(t.data.data);const{data:o}=t.data;a.dataList=o.data,a.pagination.total=o.total}})},g=e=>{f("确定删除?","删除提示").then(()=>{w([e.id]).then(t=>{t.data.code===200&&(d.success("删除成功"),i())})})};return F(async()=>{}),(e,t)=>{const o=k,b=T,h=C,y=D,v=x;return L(),B(v,null,{tree:m(()=>[c(o,{"tree-data":p(l)},null,8,["tree-data"])]),default:m(()=>[c(b,{ref:"cardSearch",config:p(_)},null,8,["config"]),c(h,{config:p(a),class:"card-table"},null,8,["config"]),c(y,{ref_key:"refForm",ref:n,config:p(r)},null,8,["config"])]),_:1})}}}),U=I(H,[["__scopeId","data-v-8b16fbf8"]]);export{U as default};
