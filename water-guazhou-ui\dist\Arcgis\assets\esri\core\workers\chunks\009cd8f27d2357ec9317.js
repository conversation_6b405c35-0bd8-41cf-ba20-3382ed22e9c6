"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[3974],{3920:(e,t,r)=>{r.d(t,{p:()=>u,r:()=>p});var n=r(43697),s=r(15923),o=r(61247),i=r(5600),a=r(52011),l=r(72762);const u=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new o.Z}get updatingHandles(){return this._get("updatingHandles")||new l.t}};return(0,n._)([(0,i.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,n._)([(0,i.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,n._)([(0,a.j)("esri.core.HandleOwner")],t),t};let p=class extends(u(s.Z)){};p=(0,n._)([(0,a.j)("esri.core.HandleOwner")],p)},64830:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(70586);class s{constructor(e=(e=>e.values().next().value)){this._peeker=e,this._items=new Set}get length(){return this._items.size}clear(){this._items.clear()}last(){if(0===this._items.size)return;let e;for(e of this._items);return e}peek(){if(0!==this._items.size)return this._peeker(this._items)}push(e){this.contains(e)||this._items.add(e)}contains(e){return this._items.has(e)}pop(){if(0===this.length)return;const e=this.peek();return this._items.delete((0,n.j0)(e)),e}popLast(){if(0===this.length)return;const e=this.last();return this._items.delete((0,n.j0)(e)),e}remove(e){this._items.delete(e)}filter(e){return this._items.forEach((t=>{e(t)||this._items.delete(t)})),this}}},72762:(e,t,r)=>{r.d(t,{t:()=>c});var n=r(43697),s=r(15923),o=r(61247),i=r(70586),a=r(17445),l=r(1654),u=r(5600),p=r(52011);let c=class extends s.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new o.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,r={}){return this._installWatch(e,t,r,a.YP)}addWhen(e,t,r={}){return this._installWatch(e,t,r,a.gx)}addOnCollectionChange(e,t,{initial:r=!1,final:n=!1}={}){const s=++this._handleId;return this._handles.add([(0,a.on)(e,"after-changes",this._createSyncUpdatingCallback(),a.Z_),(0,a.on)(e,"change",t,{onListenerAdd:r?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:n?e=>t({added:[],removed:e.toArray()}):void 0})],s),{remove:()=>this._handles.remove(s)}}addPromise(e){if((0,i.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(d)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const r=()=>this._handles.remove(t);return e.then(r,r),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,r={},n){const s=++this._handleId;r.sync||this._installSyncUpdatingWatch(e,s);const o=n(e,t,r);return this._handles.add(o,s),{remove:()=>this._handles.remove(s)}}_installSyncUpdatingWatch(e,t){const r=this._createSyncUpdatingCallback(),n=(0,a.YP)(e,r,{sync:!0,equals:()=>!1});return this._handles.add(n,t),n}_createSyncUpdatingCallback(){return()=>{this._handles.remove(d),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,l.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(d))})),d)}}};(0,n._)([(0,u.Cb)({readOnly:!0})],c.prototype,"updating",void 0),c=(0,n._)([(0,p.j)("esri.core.support.WatchUpdatingTracking")],c);const d=-42},80903:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(50758),s=r(92604),o=r(95330),i=r(64830),a=r(25045);class l{constructor(){this._inUseClients=new Array,this._clients=new Array,this._clientPromises=new Array,this._ongoingJobsQueue=new i.Z}destroy(){this.close()}get closed(){return!this._clients||!this._clients.length}open(e,t){return new Promise(((r,n)=>{let s=!0;const i=e=>{(0,o.k_)(t.signal),s&&(s=!1,e())};this._clients.length=e.length,this._clientPromises.length=e.length,this._inUseClients.length=e.length;for(let s=0;s<e.length;++s){const l=e[s];(0,o.y8)(l)?this._clientPromises[s]=l.then((e=>(this._clients[s]=new a.default(e,t,(()=>this._ongoingJobsQueue.pop()??null)),i(r),this._clients[s])),(()=>(i(n),null))):(this._clients[s]=new a.default(l,t,(()=>this._ongoingJobsQueue.pop()??null)),this._clientPromises[s]=Promise.resolve(this._clients[s]),i(r))}}))}broadcast(e,t,r){const n=new Array(this._clientPromises.length);for(let s=0;s<this._clientPromises.length;++s){const o=this._clientPromises[s];n[s]=o.then((n=>n?.invoke(e,t,r)))}return n}close(){let e;for(;e=this._ongoingJobsQueue.pop();)e.deferred.reject((0,o.zE)(`Worker closing, aborting job calling '${e.methodName}'`));for(const e of this._clientPromises)e.then((e=>e?.close()));this._clients.length=0,this._clientPromises.length=0}invoke(e,t,r){let n;Array.isArray(r)?(s.Z.getLogger("esri.core.workers.Connection").warn("invoke()","The transferList parameter is deprecated, use the options object instead"),n={transferList:r}):n=r;const i=(0,o.dD)();this._ongoingJobsQueue.push({methodName:e,data:t,invokeOptions:n,deferred:i});for(let e=0;e<this._clientPromises.length;e++){const t=this._clients[e];t?t.jobAdded():this._clientPromises[e].then((e=>e?.jobAdded()))}return i.promise}on(e,t){return Promise.all(this._clientPromises).then((()=>(0,n.AL)(this._clients.map((r=>r.on(e,t))))))}openPorts(){return new Promise((e=>{const t=new Array(this._clientPromises.length);let r=t.length;for(let n=0;n<this._clientPromises.length;++n)this._clientPromises[n].then((s=>{s&&(t[n]=s.openPort()),0==--r&&e(t)}))}))}get test(){return{numClients:this._clients.length}}}},78346:(e,t,r)=>{r.d(t,{bA:()=>D});var n=r(20102),s=r(80442),o=r(95330),i=r(80903),a=r(25045),l=r(40330),u=r(92604),p=r(70586),c=r(94362),d=r(99880),y=r(68773),h=(r(2587),r(17452));const f={};function m(e){const t={async:e.async,isDebug:e.isDebug,locale:e.locale,baseUrl:e.baseUrl,has:{...e.has},map:{...e.map},packages:e.packages&&e.packages.concat()||[],paths:{...e.paths}};return e.hasOwnProperty("async")||(t.async=!0),e.hasOwnProperty("isDebug")||(t.isDebug=!1),e.baseUrl||(t.baseUrl=f.baseUrl),t}var g=r(41213);class w{constructor(){const e=document.createDocumentFragment();["addEventListener","dispatchEvent","removeEventListener"].forEach((t=>{this[t]=(...r)=>e[t](...r)}))}}class b{constructor(){this._dispatcher=new w,this._workerPostMessage({type:c.Cs.HANDSHAKE})}terminate(){}get onmessage(){return this._onmessageHandler}set onmessage(e){this._onmessageHandler&&this.removeEventListener("message",this._onmessageHandler),this._onmessageHandler=e,e&&this.addEventListener("message",e)}get onmessageerror(){return this._onmessageerrorHandler}set onmessageerror(e){this._onmessageerrorHandler&&this.removeEventListener("messageerror",this._onmessageerrorHandler),this._onmessageerrorHandler=e,e&&this.addEventListener("messageerror",e)}get onerror(){return this._onerrorHandler}set onerror(e){this._onerrorHandler&&this.removeEventListener("error",this._onerrorHandler),this._onerrorHandler=e,e&&this.addEventListener("error",e)}postMessage(e){(0,g.Y)((()=>{this._workerMessageHandler(new MessageEvent("message",{data:e}))}))}dispatchEvent(e){return this._dispatcher.dispatchEvent(e)}addEventListener(e,t,r){this._dispatcher.addEventListener(e,t,r)}removeEventListener(e,t,r){this._dispatcher.removeEventListener(e,t,r)}_workerPostMessage(e){(0,g.Y)((()=>{this.dispatchEvent(new MessageEvent("message",{data:e}))}))}async _workerMessageHandler(e){const t=(0,c.QM)(e);if(t&&t.type===c.Cs.OPEN){const{modulePath:e,jobId:r}=t;let n=await a.default.loadWorker(e);n||(n=await import(e));const s=a.default.connect(n);this._workerPostMessage({type:c.Cs.OPENED,jobId:r,data:s})}}}var v=r(70171),_=r(17202);const S=u.Z.getLogger("esri.core.workers.workerFactory"),{HANDSHAKE:C}=c.Cs;let F,P;const k="Failed to create Worker. Fallback to execute module in main thread";async function x(e){return new Promise((t=>{function r(s){const o=(0,c.QM)(s);o&&o.type===C&&(e.removeEventListener("message",r),e.removeEventListener("error",n),t(e))}function n(t){t.preventDefault(),e.removeEventListener("message",r),e.removeEventListener("error",n),S.warn("Failed to create Worker. Fallback to execute module in main thread",t),(e=new b).addEventListener("message",r),e.addEventListener("error",n)}e.addEventListener("message",r),e.addEventListener("error",n)}))}function O(){let e;if(null!=y.Z.default){const t={...y.Z};delete t.default,e=JSON.parse(JSON.stringify(t))}else e=JSON.parse(JSON.stringify(y.Z));e.assetsPath=(0,h.hF)(e.assetsPath),e.defaultAssetsPath=e.defaultAssetsPath?(0,h.hF)(e.defaultAssetsPath):void 0,e.request.interceptors=[],e.log.interceptors=[],e.locale=(0,v.Kd)(),e.has={"esri-csp-restrictions":(0,s.Z)("esri-csp-restrictions"),"esri-2d-debug":!1,"esri-2d-update-debug":(0,s.Z)("esri-2d-update-debug"),"featurelayer-pbf":(0,s.Z)("featurelayer-pbf"),"featurelayer-simplify-thresholds":(0,s.Z)("featurelayer-simplify-thresholds"),"featurelayer-simplify-payload-size-factors":(0,s.Z)("featurelayer-simplify-payload-size-factors"),"featurelayer-simplify-mobile-factor":(0,s.Z)("featurelayer-simplify-mobile-factor"),"esri-atomics":(0,s.Z)("esri-atomics"),"esri-shared-array-buffer":(0,s.Z)("esri-shared-array-buffer"),"esri-tiles-debug":(0,s.Z)("esri-tiles-debug"),"esri-workers-arraybuffer-transfer":(0,s.Z)("esri-workers-arraybuffer-transfer"),"feature-polyline-generalization-factor":(0,s.Z)("feature-polyline-generalization-factor"),"host-webworker":1,"polylabel-placement-enabled":(0,s.Z)("polylabel-placement-enabled")},e.workers.loaderUrl&&(e.workers.loaderUrl=(0,h.hF)(e.workers.loaderUrl)),e.workers.workerPath?e.workers.workerPath=(0,h.hF)(e.workers.workerPath):e.workers.workerPath=(0,h.hF)((0,d.V)("esri/core/workers/RemoteClient.js")),e.workers.useDynamicImport=!1;const t=y.Z.workers.loaderConfig,r=m({baseUrl:t?.baseUrl,locale:(0,v.Kd)(),has:{"csp-restrictions":1,"dojo-test-sniff":0,"host-webworker":1,...t?.has},map:{...t?.map},paths:{...t?.paths},packages:t?.packages||[]}),n={version:l.i8,buildDate:_.r,revision:_.$};return JSON.stringify({esriConfig:e,loaderConfig:r,kernelInfo:n})}let E=0;const{ABORT:T,INVOKE:R,OPEN:N,OPENED:j,RESPONSE:I}=c.Cs;class Z{static async create(e){const t=await async function(){if(!(0,s.Z)("esri-workers")||((0,s.Z)("mozilla"),0))return x(new b);if(!F&&!P)try{const e='let globalId=0;const outgoing=new Map,configuration=JSON.parse("{CONFIGURATION}");self.esriConfig=configuration.esriConfig;const workerPath=self.esriConfig.workers.workerPath,HANDSHAKE=0,OPEN=1,OPENED=2,RESPONSE=3,INVOKE=4,ABORT=5;function createAbortError(){const e=new Error("Aborted");return e.name="AbortError",e}function receiveMessage(e){return e&&e.data?"string"==typeof e.data?JSON.parse(e.data):e.data:null}function invokeStaticMessage(e,o,r){const t=r&&r.signal,n=globalId++;return new Promise(((r,i)=>{if(t){if(t.aborted)return i(createAbortError());t.addEventListener("abort",(()=>{outgoing.get(n)&&(outgoing.delete(n),self.postMessage({type:5,jobId:n}),i(createAbortError()))}))}outgoing.set(n,{resolve:r,reject:i}),self.postMessage({type:4,jobId:n,methodName:e,abortable:null!=t,data:o})}))}let workerRevisionChecked=!1;function checkWorkerRevision(e){if(!workerRevisionChecked&&e.kernelInfo){workerRevisionChecked=!0;const{revision:o,version:r}=configuration.kernelInfo,{revision:t,version:n}=e.kernelInfo;esriConfig.assetsPath!==esriConfig.defaultAssetsPath&&o!==t&&console.warn(`Version mismatch detected between ArcGIS API for JavaScript modules and assets. For more information visit https://bit.ly/3QnsuSo.\\nModules version: ${r}\\nAssets version: ${n}`)}}function messageHandler(e){const o=receiveMessage(e);if(!o)return;const r=o.jobId;switch(o.type){case 1:let n;function t(e){const o=n.connect(e);self.postMessage({type:2,jobId:r,data:o},[o])}"function"==typeof define&&define.amd?require([workerPath],(e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||new Promise((e=>{require([o.modulePath],e)})))).then(t)})):"System"in self&&"function"==typeof System.import?System.import(workerPath).then((e=>(n=e.default,checkWorkerRevision(n),n.loadWorker(o.modulePath)))).then((e=>e||System.import(o.modulePath))).then(t):esriConfig.workers.useDynamicImport?import(workerPath).then((e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||import(o.modulePath))).then(t)})):(self.RemoteClient||importScripts(workerPath),n=self.RemoteClient.default||self.RemoteClient,checkWorkerRevision(n),n.loadWorker(o.modulePath).then(t));break;case 3:if(outgoing.has(r)){const i=outgoing.get(r);outgoing.delete(r),o.error?i.reject(JSON.parse(o.error)):i.resolve(o.data)}}}self.dojoConfig=configuration.loaderConfig,esriConfig.workers.loaderUrl&&(self.importScripts(esriConfig.workers.loaderUrl),"function"==typeof require&&"function"==typeof require.config&&require.config(configuration.loaderConfig)),self.addEventListener("message",messageHandler),self.postMessage({type:0});'.split('"{CONFIGURATION}"').join(`'${O()}'`);F=URL.createObjectURL(new Blob([e],{type:"text/javascript"}))}catch(e){P=e||{}}let e;if(F)try{e=new Worker(F,{name:"esri-worker-"+E++})}catch(t){S.warn(k,P),e=new b}else S.warn(k,P),e=new b;return x(e)}();return new Z(t,e)}constructor(e,t){this._outJobs=new Map,this._inJobs=new Map,this.worker=e,this.id=t,e.addEventListener("message",this._onMessage.bind(this)),e.addEventListener("error",(e=>{e.preventDefault(),u.Z.getLogger("esri.core.workers.WorkerOwner").error(e)}))}terminate(){this.worker.terminate()}async open(e,t={}){const{signal:r}=t,n=(0,c.jt)();return new Promise(((t,s)=>{const i={resolve:t,reject:s,abortHandle:(0,o.$F)(r,(()=>{this._outJobs.delete(n),this._post({type:T,jobId:n})}))};this._outJobs.set(n,i),this._post({type:N,jobId:n,modulePath:e})}))}_onMessage(e){const t=(0,c.QM)(e);if(t)switch(t.type){case j:this._onOpenedMessage(t);break;case I:this._onResponseMessage(t);break;case T:this._onAbortMessage(t);break;case R:this._onInvokeMessage(t)}}_onAbortMessage(e){const t=this._inJobs,r=e.jobId,n=t.get(r);n&&(n.controller&&n.controller.abort(),t.delete(r))}_onInvokeMessage(e){const{methodName:t,jobId:r,data:n,abortable:s}=e,i=s?new AbortController:null,a=this._inJobs,u=l.Nv[t];let p;try{if("function"!=typeof u)throw new TypeError(`${t} is not a function`);p=u.call(null,n,{signal:i?i.signal:null})}catch(e){return void this._post({type:I,jobId:r,error:(0,c.AB)(e)})}(0,o.y8)(p)?(a.set(r,{controller:i,promise:p}),p.then((e=>{a.has(r)&&(a.delete(r),this._post({type:I,jobId:r},e))}),(e=>{a.has(r)&&(a.delete(r),e||(e={message:"Error encountered at method"+t}),(0,o.D_)(e)||this._post({type:I,jobId:r,error:(0,c.AB)(e||{message:`Error encountered at method ${t}`})}))}))):this._post({type:I,jobId:r},p)}_onOpenedMessage(e){const{jobId:t,data:r}=e,n=this._outJobs.get(t);n&&(this._outJobs.delete(t),(0,p.hw)(n.abortHandle),n.resolve(r))}_onResponseMessage(e){const{jobId:t,error:r,data:s}=e,o=this._outJobs.get(t);o&&(this._outJobs.delete(t),(0,p.hw)(o.abortHandle),r?o.reject(n.Z.fromJSON(JSON.parse(r))):o.resolve(s))}_post(e,t,r){return(0,c.oi)(this.worker,e,t,r)}}let A=(0,s.Z)("esri-workers-debug")?1:(0,s.Z)("esri-mobile")?Math.min(navigator.hardwareConcurrency-1,3):(0,s.Z)("host-browser")?navigator.hardwareConcurrency-1:0;A||(A=(0,s.Z)("safari")&&(0,s.Z)("mac")?7:2);let M=0;const U=[];async function L(e,t){const r=new i.Z;return await r.open(e,t),r}async function D(e,t={}){if("string"!=typeof e)throw new n.Z("workers:undefined-module","modulePath is missing");let r=t.strategy||"distributed";if((0,s.Z)("host-webworker")&&!(0,s.Z)("esri-workers")&&(r="local"),"local"===r){let r=await a.default.loadWorker(e);r||(r=await import(e)),(0,o.k_)(t.signal);const n=t.client||r;return L([a.default.connect(r)],{...t,client:n})}if(await async function(){if(G)return G;q=new AbortController;const e=[];for(let t=0;t<A;t++){const r=Z.create(t).then((e=>(U[t]=e,e)));e.push(r)}return G=Promise.all(e),G}(),(0,o.k_)(t.signal),"dedicated"===r){const r=M++%A;return L([await U[r].open(e,t)],t)}if(t.maxNumWorkers&&t.maxNumWorkers>0){const r=Math.min(t.maxNumWorkers,A);if(r<A){const n=new Array(r);for(let s=0;s<r;++s){const r=M++%A;n[s]=U[r].open(e,t)}return L(n,t)}}return L(U.map((r=>r.open(e,t))),t)}let q,G=null},2587:(e,t,r)=>{r(90344),r(18848),r(940),r(70171);var n=r(94443),s=r(3172),o=r(20102),i=r(70586);async function a(e){if((0,i.pC)(u.fetchBundleAsset))return u.fetchBundleAsset(e);const t=await(0,s.default)(e,{responseType:"text"});return JSON.parse(t.data)}class l{constructor({base:e="",pattern:t,location:r=new URL(window.location.href)}){let n;n="string"==typeof r?e=>new URL(e,new URL(r,window.location.href)).href:r instanceof URL?e=>new URL(e,r).href:r,this.pattern="string"==typeof t?new RegExp(`^${t}`):t,this.getAssetUrl=n,e=e?e.endsWith("/")?e:e+"/":"",this.matcher=new RegExp(`^${e}(?:(.*)/)?(.*)$`)}fetchMessageBundle(e,t){return async function(e,t,r,s){const i=t.exec(r);if(!i)throw new o.Z("esri-intl:invalid-bundle",`Bundle id "${r}" is not compatible with the pattern "${t}"`);const l=i[1]?`${i[1]}/`:"",u=i[2],p=(0,n.Su)(s),c=`${l}${u}.json`,d=p?`${l}${u}_${p}.json`:c;let y;try{y=await a(e(d))}catch(t){if(d===c)throw new o.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:t});try{y=await a(e(c))}catch(e){throw new o.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:e})}}return y}(this.getAssetUrl,this.matcher,e,t)}}const u={};var p,c=r(99880);(0,n.tz)((p={pattern:"esri/",location:c.V},new l(p)))},940:(e,t,r)=>{r.d(t,{n:()=>u});var n=r(92604),s=r(78286),o=r(19153),i=r(90344),a=r(18848);const l=n.Z.getLogger("esri.intl.substitute");function u(e,t,r={}){const{format:n={}}=r;return(0,o.gx)(e,(e=>function(e,t,r){let n,o;const i=e.indexOf(":");if(-1===i?n=e.trim():(n=e.slice(0,i).trim(),o=e.slice(i+1).trim()),!n)return"";const a=(0,s.hS)(n,t);if(null==a)return"";const l=(o?r?.[o]:null)??r?.[n];return l?p(a,l):o?c(a,o):d(a)}(e,t,n)))}function p(e,t){switch(t.type){case"date":return(0,i.p6)(e,t.intlOptions);case"number":return(0,a.uf)(e,t.intlOptions);default:return l.warn("missing format descriptor for key {key}"),d(e)}}function c(e,t){switch(t.toLowerCase()){case"dateformat":return(0,i.p6)(e);case"numberformat":return(0,a.uf)(e);default:return l.warn(`inline format is unsupported since 4.12: ${t}`),/^(dateformat|datestring)/i.test(t)?(0,i.p6)(e):/^numberformat/i.test(t)?(0,a.uf)(e):d(e)}}function d(e){switch(typeof e){case"string":return e;case"number":return(0,a.uf)(e);case"boolean":return""+e;default:return e instanceof Date?(0,i.p6)(e):""}}},12653:(e,t,r)=>{r.r(t),r.d(t,{default:()=>B});var n=r(43697),s=(r(66577),r(51773)),o=(r(16050),r(12501),r(28756),r(92271),r(72529),r(5499),r(84382),r(81571),r(91423),r(32400)),i=r(70586),a=r(16453),l=r(78286),u=r(17452),p=r(5600),c=r(75215),d=(r(67676),r(52011)),y=r(30556),h=r(86973),f=r(87085),m=r(20102),g=r(3920),w=r(80442),b=r(83379),v=r(95330),_=r(78346),S=r(25278),C=r(51432),F=r(74889),P=r(6570);let k=class extends((0,g.p)(b.Z)){constructor(){super(...arguments),this._connection=null,this.capabilities=(0,S.MS)(!1,!1),this.type="wfs",this.refresh=(0,v.Ds)((async e=>{await this.load();const{extent:t}=await this._connection.invoke("refresh",e);return t&&(this.sourceJSON.extent=t),{dataChanged:!0,updates:{extent:this.sourceJSON.extent}}}))}load(e){const t=(0,i.pC)(e)?e.signal:null;return this.addResolvingPromise(this._startWorker({signal:t})),Promise.resolve(this)}destroy(){this._connection?.close(),this._connection=null}async openPorts(){return await this.load(),this._connection.openPorts()}async queryFeatures(e,t={}){await this.load(t);const r=await this._connection.invoke("queryFeatures",e?e.toJSON():null,t);return F.Z.fromJSON(r)}async queryFeaturesJSON(e,t={}){return await this.load(t),this._connection.invoke("queryFeatures",e?e.toJSON():null,t)}async queryFeatureCount(e,t={}){return await this.load(t),this._connection.invoke("queryFeatureCount",e?e.toJSON():null,t)}async queryObjectIds(e,t={}){return await this.load(t),this._connection.invoke("queryObjectIds",e?e.toJSON():null,t)}async queryExtent(e,t={}){await this.load(t);const r=await this._connection.invoke("queryExtent",e?e.toJSON():null,t);return{count:r.count,extent:P.Z.fromJSON(r.extent)}}async querySnapping(e,t={}){return await this.load(t),this._connection.invoke("querySnapping",e,t)}async _createLoadOptions(e){const{url:t,customParameters:r,name:n,namespaceUri:s,spatialReference:o,fields:a,geometryType:l,swapXY:u}=this.layer;if(!t)throw new m.Z("wfs-layer:missing-url","WFSLayer must be created with a url");this.wfsCapabilities||(this.wfsCapabilities=await(0,C.FU)(t,{customParameters:r,...e}));const p=["fields","geometryType","name","namespaceUri","spatialReference","swapXY"].some((e=>null==this.layer[e])),c=p?await(0,C.be)(this.wfsCapabilities,n,s,{spatialReference:o,customParameters:r,signal:e?.signal}):{...(0,C.eB)(a??[]),geometryType:l,name:n,namespaceUri:s,spatialReference:o,swapXY:u},d=(0,i.Wg)((0,C.ft)(this.wfsCapabilities.readFeatureTypes(),c.name,c.namespaceUri)),y=h.M.toJSON(c.geometryType);return{customParameters:r,featureType:d,fields:c.fields?.map((e=>e.toJSON()))??[],geometryField:c.geometryField,geometryType:y,getFeatureUrl:this.wfsCapabilities.operations.GetFeature.url,getFeatureOutputFormat:this.wfsCapabilities.operations.GetFeature.outputFormat,objectIdField:c.objectIdField,spatialReference:c.spatialReference.toJSON(),swapXY:!!c.swapXY}}async _startWorker(e){const[t,r]=await(0,v.as)([this._createLoadOptions(e),(0,_.bA)("WFSSourceWorker",{...e,strategy:(0,w.Z)("feature-layers-workers")?"dedicated":"local"})]),n=t.error||r.error||null,s=r.value||null;if(n)throw s&&s.close(),n;const o=t.value;this._connection=r.value;const i=(await this._connection.invoke("load",o,e)).extent;this.sourceJSON={extent:i,fields:o.fields,geometryType:o.geometryType,objectIdField:o.objectIdField,geometryField:o.geometryField,drawingInfo:(0,S.bU)(o.geometryType),name:o.featureType.title,wfsInfo:{name:o.featureType.name,featureUrl:o.getFeatureUrl,maxFeatures:3e3,swapXY:o.swapXY,supportedSpatialReferences:o.featureType.supportedSpatialReferences,version:"2.0.0",wfsNamespace:o.featureType.namespaceUri}}}};(0,n._)([(0,p.Cb)()],k.prototype,"capabilities",void 0),(0,n._)([(0,p.Cb)({constructOnly:!0})],k.prototype,"layer",void 0),(0,n._)([(0,p.Cb)()],k.prototype,"sourceJSON",void 0),(0,n._)([(0,p.Cb)()],k.prototype,"type",void 0),(0,n._)([(0,p.Cb)()],k.prototype,"wfsCapabilities",void 0),k=(0,n._)([(0,d.j)("esri.layers.graphics.sources.WFSSource")],k);var x,O=r(71612),E=r(17017),T=r(69637),R=r(6404),N=r(38009),j=r(68825),I=r(16859),Z=r(34760),A=r(72965),M=r(28294),U=r(21506),L=r(1231),D=r(53518),q=r(35671),G=r(54306),J=r(30707),W=r(14165),H=r(32163),$=r(82971);const z=(0,D.v)();let Q=x=class extends((0,j.c)((0,E.N)((0,R.M)((0,T.b)((0,O.h)((0,M.n)((0,Z.Q)((0,A.M)((0,N.q)((0,I.I)((0,a.R)(f.Z)))))))))))){static fromWFSLayerInfo(e){const{customParameters:t,fields:r,geometryField:n,geometryType:s,name:o,namespaceUri:i,objectIdField:a,spatialReference:l,swapXY:u,url:p,wfsCapabilities:c}=e;return new x({customParameters:t,fields:r,geometryField:n,geometryType:s,name:o,namespaceUri:i,objectIdField:a,spatialReference:l,swapXY:u,url:p,wfsCapabilities:c})}constructor(e){super(e),this.copyright=null,this.customParameters=null,this.definitionExpression=null,this.displayField=null,this.elevationInfo=null,this.featureUrl=void 0,this.fields=null,this.fieldsIndex=null,this.fullExtent=null,this.geometryType=null,this.labelsVisible=!0,this.labelingInfo=null,this.legendEnabled=!0,this.objectIdField=null,this.operationalLayerType="WFS",this.maxFeatures=3e3,this.mode=0,this.name=null,this.namespaceUri=null,this.outFields=null,this.popupEnabled=!0,this.popupTemplate=null,this.screenSizePerspectiveEnabled=!0,this.source=new k({layer:this}),this.spatialReference=$.Z.WGS84,this.spatialReferences=[4326],this.swapXY=void 0,this.title="WFS",this.type="wfs",this.url=null,this.version=void 0}destroy(){this.source?.destroy()}load(e){return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["WFS"]},e).then((()=>this.source.load(e))).then((()=>{this.read(this.source.sourceJSON,{origin:"service",url:this.parsedUrl}),this.revert(["objectIdField","fields","timeInfo","spatialReference","name","namespaceUri"],"service"),(0,q.YN)(this.renderer,this.fieldsIndex),(0,q.UF)(this.timeInfo,this.fieldsIndex)}))),Promise.resolve(this)}get capabilities(){return this.source?.capabilities}get createQueryVersion(){return this.commitProperty("definitionExpression"),this.commitProperty("timeExtent"),this.commitProperty("timeOffset"),this.commitProperty("geometryType"),this.commitProperty("capabilities"),(this._get("createQueryVersion")||0)+1}get defaultPopupTemplate(){return this.createPopupTemplate()}writeFields(e,t,r){const n=e.filter((e=>e.name!==C.M8));this.geometryField&&n.unshift(new L.Z({name:this.geometryField,alias:this.geometryField,type:"geometry"})),(0,l.RB)(r,n.map((e=>e.toJSON())),t)}get parsedUrl(){return(0,u.mN)(this.url)}set renderer(e){(0,q.YN)(e,this.fieldsIndex),this._set("renderer",e)}get wfsCapabilities(){return this.source?.wfsCapabilities}set wfsCapabilities(e){this.source&&(this.source.wfsCapabilities=e)}createPopupTemplate(e){return(0,H.eZ)(this,e)}createQuery(){const e=new W.Z;e.returnGeometry=!0,e.outFields=["*"],e.where=this.definitionExpression||"1=1";const{timeOffset:t,timeExtent:r}=this;return e.timeExtent=null!=t&&null!=r?r.offset(-t.value,t.unit):r||null,e}getFieldDomain(e,t){return this.getField(e)?.domain}getField(e){return this.fieldsIndex?.get(e)}queryFeatures(e,t){return this.load().then((()=>this.source.queryFeatures(W.Z.from(e)||this.createQuery(),t))).then((e=>{if(e?.features)for(const t of e.features)t.layer=t.sourceLayer=this;return e}))}queryObjectIds(e,t){return this.load().then((()=>this.source.queryObjectIds(W.Z.from(e)||this.createQuery(),t)))}queryFeatureCount(e,t){return this.load().then((()=>this.source.queryFeatureCount(W.Z.from(e)||this.createQuery(),t)))}queryExtent(e,t){return this.load().then((()=>this.source.queryExtent(W.Z.from(e)||this.createQuery(),t)))}async hasDataChanged(){try{const{dataChanged:e,updates:t}=await this.source.refresh(this.customParameters);return(0,i.pC)(t)&&this.read(t,{origin:"service",url:this.parsedUrl,ignoreDefaults:!0}),e}catch{}return!1}};(0,n._)([(0,p.Cb)({readOnly:!0})],Q.prototype,"capabilities",null),(0,n._)([(0,p.Cb)({type:String})],Q.prototype,"copyright",void 0),(0,n._)([(0,p.Cb)({readOnly:!0})],Q.prototype,"createQueryVersion",null),(0,n._)([(0,p.Cb)({json:{name:"wfsInfo.customParameters",write:{overridePolicy:e=>({enabled:!!(e&&Object.keys(e).length>0),ignoreOrigin:!0})}}})],Q.prototype,"customParameters",void 0),(0,n._)([(0,p.Cb)({readOnly:!0})],Q.prototype,"defaultPopupTemplate",null),(0,n._)([(0,p.Cb)({type:String,json:{name:"layerDefinition.definitionExpression",write:{enabled:!0,allowNull:!0}}})],Q.prototype,"definitionExpression",void 0),(0,n._)([(0,p.Cb)({type:String})],Q.prototype,"displayField",void 0),(0,n._)([(0,p.Cb)(U.PV)],Q.prototype,"elevationInfo",void 0),(0,n._)([(0,p.Cb)({type:String,readOnly:!0,json:{name:"wfsInfo.featureUrl",write:{ignoreOrigin:!0,isRequired:!0}}})],Q.prototype,"featureUrl",void 0),(0,n._)([(0,p.Cb)({type:[L.Z],json:{name:"layerDefinition.fields",write:{ignoreOrigin:!0,isRequired:!0},origins:{service:{name:"fields"}}}})],Q.prototype,"fields",void 0),(0,n._)([(0,y.c)("fields")],Q.prototype,"writeFields",null),(0,n._)([(0,p.Cb)(z.fieldsIndex)],Q.prototype,"fieldsIndex",void 0),(0,n._)([(0,p.Cb)({type:P.Z,json:{name:"extent"}})],Q.prototype,"fullExtent",void 0),(0,n._)([(0,p.Cb)()],Q.prototype,"geometryField",void 0),(0,n._)([(0,p.Cb)({type:String,json:{read:{source:"layerDefinition.geometryType",reader:h.M.read},write:{target:"layerDefinition.geometryType",writer:h.M.write,ignoreOrigin:!0},origins:{service:{read:h.M.read}}}})],Q.prototype,"geometryType",void 0),(0,n._)([(0,p.Cb)({type:String})],Q.prototype,"id",void 0),(0,n._)([(0,p.Cb)(U.iR)],Q.prototype,"labelsVisible",void 0),(0,n._)([(0,p.Cb)({type:[G.Z],json:{name:"layerDefinition.drawingInfo.labelingInfo",read:{reader:J.r},write:!0}})],Q.prototype,"labelingInfo",void 0),(0,n._)([(0,p.Cb)(U.rn)],Q.prototype,"legendEnabled",void 0),(0,n._)([(0,p.Cb)({type:["show","hide"]})],Q.prototype,"listMode",void 0),(0,n._)([(0,p.Cb)({type:String})],Q.prototype,"objectIdField",void 0),(0,n._)([(0,p.Cb)({type:["WFS"]})],Q.prototype,"operationalLayerType",void 0),(0,n._)([(0,p.Cb)({type:c.z8,json:{name:"wfsInfo.maxFeatures",write:{ignoreOrigin:!0,isRequired:!0}}})],Q.prototype,"maxFeatures",void 0),(0,n._)([(0,p.Cb)({type:[0],readOnly:!0,json:{origins:{"web-map":{write:{ignoreOrigin:!0,isRequired:!0}}}}})],Q.prototype,"mode",void 0),(0,n._)([(0,p.Cb)({type:String,json:{name:"wfsInfo.name",write:{ignoreOrigin:!0,isRequired:!0}}})],Q.prototype,"name",void 0),(0,n._)([(0,p.Cb)({type:String,json:{name:"wfsInfo.wfsNamespace",write:{ignoreOrigin:!0,isRequired:!0}}})],Q.prototype,"namespaceUri",void 0),(0,n._)([(0,p.Cb)(U.bT)],Q.prototype,"opacity",void 0),(0,n._)([(0,p.Cb)(z.outFields)],Q.prototype,"outFields",void 0),(0,n._)([(0,p.Cb)({readOnly:!0})],Q.prototype,"parsedUrl",null),(0,n._)([(0,p.Cb)(U.C_)],Q.prototype,"popupEnabled",void 0),(0,n._)([(0,p.Cb)({type:s.Z,json:{name:"popupInfo",write:!0}})],Q.prototype,"popupTemplate",void 0),(0,n._)([(0,p.Cb)({types:o.A,json:{origins:{service:{name:"drawingInfo.renderer"},"web-scene":{types:o.o,name:"layerDefinition.drawingInfo.renderer",write:!0}},name:"layerDefinition.drawingInfo.renderer",write:{ignoreOrigin:!0}}})],Q.prototype,"renderer",null),(0,n._)([(0,p.Cb)(U.YI)],Q.prototype,"screenSizePerspectiveEnabled",void 0),(0,n._)([(0,p.Cb)({readOnly:!0})],Q.prototype,"source",void 0),(0,n._)([(0,p.Cb)({type:$.Z,json:{name:"layerDefinition.spatialReference",write:{ignoreOrigin:!0,isRequired:!0},origins:{service:{name:"extent.spatialReference"}}}})],Q.prototype,"spatialReference",void 0),(0,n._)([(0,p.Cb)({readOnly:!0,type:[c.z8],json:{name:"wfsInfo.supportedSpatialReferences",write:{ignoreOrigin:!0,isRequired:!0}}})],Q.prototype,"spatialReferences",void 0),(0,n._)([(0,p.Cb)({type:Boolean,value:!1,json:{name:"wfsInfo.swapXY",write:{ignoreOrigin:!0,isRequired:!0}}})],Q.prototype,"swapXY",void 0),(0,n._)([(0,p.Cb)({json:{write:{ignoreOrigin:!0,isRequired:!0},origins:{service:{name:"name"}}}})],Q.prototype,"title",void 0),(0,n._)([(0,p.Cb)({json:{read:!1},readOnly:!0})],Q.prototype,"type",void 0),(0,n._)([(0,p.Cb)(U.HQ)],Q.prototype,"url",void 0),(0,n._)([(0,p.Cb)({type:String,readOnly:!0,json:{name:"wfsInfo.version",write:{ignoreOrigin:!0,isRequired:!0}}})],Q.prototype,"version",void 0),(0,n._)([(0,p.Cb)()],Q.prototype,"wfsCapabilities",null),Q=x=(0,n._)([(0,d.j)("esri.layers.WFSLayer")],Q);const B=Q},61159:(e,t,r)=>{r.d(t,{g:()=>n});const n={supportsStatistics:!0,supportsPercentileStatistics:!0,supportsSpatialAggregationStatistics:!1,supportedSpatialAggregationStatistics:{envelope:!1,centroid:!1,convexHull:!1},supportsCentroid:!0,supportsCacheHint:!1,supportsDistance:!0,supportsDistinct:!0,supportsExtent:!0,supportsGeometryProperties:!1,supportsHavingClause:!0,supportsOrderBy:!0,supportsPagination:!0,supportsQuantization:!0,supportsQuantizationEditMode:!1,supportsQueryGeometry:!0,supportsResultType:!1,supportsSqlExpression:!0,supportsMaxRecordCountFactor:!1,supportsStandardizedQueriesOnly:!0,supportsTopFeaturesQuery:!1,supportsQueryByOthers:!0,supportsHistoricMoment:!1,supportsFormatPBF:!1,supportsDisjointSpatialRelationship:!0,supportsDefaultSpatialReference:!1,supportsFullTextSearch:!1,supportsCompactGeometry:!1,maxRecordCountFactor:void 0,maxRecordCount:void 0,standardMaxRecordCount:void 0,tileMaxRecordCount:void 0}},92722:(e,t,r)=>{r.d(t,{O3:()=>_,lG:()=>C,my:()=>S,q9:()=>l});var n=r(20102),s=r(70272),o=r(5428),i=r(35671);const a={LineString:"esriGeometryPolyline",MultiLineString:"esriGeometryPolyline",MultiPoint:"esriGeometryMultipoint",Point:"esriGeometryPoint",Polygon:"esriGeometryPolygon",MultiPolygon:"esriGeometryPolygon"};function l(e){return a[e]}function*u(e){switch(e.type){case"Feature":yield e;break;case"FeatureCollection":for(const t of e.features)t&&(yield t)}}function*p(e){if(e)switch(e.type){case"Point":yield e.coordinates;break;case"LineString":case"MultiPoint":yield*e.coordinates;break;case"MultiLineString":case"Polygon":for(const t of e.coordinates)yield*t;break;case"MultiPolygon":for(const t of e.coordinates)for(const e of t)yield*e}}function c(e){for(const t of e)if(t.length>2)return!0;return!1}function d(e){let t=0;for(let r=0;r<e.length;r++){const n=e[r],s=e[(r+1)%e.length];t+=n[0]*s[1]-s[0]*n[1]}return t<=0}function y(e){const t=e[0],r=e[e.length-1];return t[0]===r[0]&&t[1]===r[1]&&t[2]===r[2]||e.push(t),e}function h(e,t,r){switch(t.type){case"LineString":case"MultiPoint":return function(e,t,r){return g(e,t.coordinates,r),e}(e,t,r);case"MultiLineString":return function(e,t,r){for(const n of t.coordinates)g(e,n,r);return e}(e,t,r);case"MultiPolygon":return function(e,t,r){for(const n of t.coordinates){f(e,n[0],r);for(let t=1;t<n.length;t++)m(e,n[t],r)}return e}(e,t,r);case"Point":return function(e,t,r){return b(e,t.coordinates,r),e}(e,t,r);case"Polygon":return function(e,t,r){const n=t.coordinates;f(e,n[0],r);for(let t=1;t<n.length;t++)m(e,n[t],r);return e}(e,t,r)}}function f(e,t,r){const n=y(t);!function(e){return!d(e)}(n)?g(e,n,r):w(e,n,r)}function m(e,t,r){const n=y(t);!function(e){return d(e)}(n)?g(e,n,r):w(e,n,r)}function g(e,t,r){for(const n of t)b(e,n,r);e.lengths.push(t.length)}function w(e,t,r){for(let n=t.length-1;n>=0;n--)b(e,t[n],r);e.lengths.push(t.length)}function b(e,t,r){const[n,s,o]=t;e.coords.push(n,s),r.hasZ&&e.coords.push(o||0)}function v(e){switch(typeof e){case"string":return"esriFieldTypeString";case"number":return"esriFieldTypeDouble";default:return"unknown"}}function _(e){if(!e)throw new n.Z("geojson-layer:empty","GeoJSON data is empty");if("Feature"!==e.type&&"FeatureCollection"!==e.type)throw new n.Z("geojson-layer:unsupported-geojson-object","missing or not supported GeoJSON object type",{data:e});const{crs:t}=e;if(!t)return;const r="string"==typeof t?t:"name"===t.type?t.properties.name:"EPSG"===t.type?t.properties.code:null,s=new RegExp(".*(CRS84H?|4326)$","i");if(!r||!s.test(r))throw new n.Z("geojson-layer:unsupported-crs","unsupported GeoJSON 'crs' member",{crs:t})}function S(e,t={}){const r=[],n=new Set,s=new Set;let o,a=!1,d=null,y=!1,{geometryType:h=null}=t,f=!1;for(const t of u(e)){const{geometry:e,properties:u,id:m}=t;if((!e||(h||(h=l(e.type)),l(e.type)===h))&&(a||(a=c(p(e))),y||(y=null!=m,y&&(o=typeof m,u&&(d=Object.keys(u).filter((e=>u[e]===m))))),u&&d&&y&&null!=m&&(d.length>1?d=d.filter((e=>u[e]===m)):1===d.length&&(d=u[d[0]]===m?d:[])),!f&&u)){let e=!0;for(const t in u){if(n.has(t))continue;const o=u[t];if(null==o){e=!1,s.add(t);continue}const a=v(o);if("unknown"===a){s.add(t);continue}s.delete(t),n.add(t);const l=(0,i.q6)(t);l&&r.push({name:l,alias:t,type:a})}f=e}}const m=(0,i.q6)(1===d?.length&&d[0]||null)??void 0;if(m)for(const e of r)if(e.name===m&&(0,i.H7)(e)){e.type="esriFieldTypeOID";break}return{fields:r,geometryType:h,hasZ:a,objectIdFieldName:m,objectIdFieldType:o,unknownFields:Array.from(s)}}function C(e,t){return Array.from(function*(e,t={}){const{geometryType:r,objectIdField:n}=t;for(const i of e){const{geometry:e,properties:a,id:u}=i;if(e&&l(e.type)!==r)continue;const p=a||{};let c;n&&(c=p[n],null==u||c||(p[n]=c=u));const d=new s.u_(e?h(new o.Z,e,t):null,p,null,c??void 0);yield d}}(u(e),t))}},25278:(e,t,r)=>{r.d(t,{Dm:()=>p,Hq:()=>c,MS:()=>d,bU:()=>a});var n=r(80442),s=r(22974),o=r(61159),i=r(58333);function a(e){return{renderer:{type:"simple",symbol:"esriGeometryPoint"===e||"esriGeometryMultipoint"===e?i.I4:"esriGeometryPolyline"===e?i.ET:i.lF}}}const l=/^[_$a-zA-Z][_$a-zA-Z0-9]*$/;let u=1;function p(e,t){if((0,n.Z)("esri-csp-restrictions"))return()=>({[t]:null,...e});try{let r=`this.${t} = null;`;for(const t in e)r+=`this${l.test(t)?`.${t}`:`["${t}"]`} = ${JSON.stringify(e[t])};`;const n=new Function(`\n      return class AttributesClass$${u++} {\n        constructor() {\n          ${r};\n        }\n      }\n    `)();return()=>new n}catch(r){return()=>({[t]:null,...e})}}function c(e={}){return[{name:"New Feature",description:"",prototype:{attributes:(0,s.d9)(e)}}]}function d(e,t){return{analytics:{supportsCacheHint:!1},attachment:null,data:{isVersioned:!1,supportsAttachment:!1,supportsM:!1,supportsZ:e},metadata:{supportsAdvancedFieldProperties:!1},operations:{supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsAdd:t,supportsDelete:t,supportsEditing:t,supportsChangeTracking:!1,supportsQuery:!0,supportsQueryAnalytics:!1,supportsQueryAttachments:!1,supportsQueryTopFeatures:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsUpdate:t,supportsExceedsLimitStatistics:!0},query:o.g,queryRelated:{supportsCount:!0,supportsOrderBy:!0,supportsPagination:!0,supportsCacheHint:!1},queryTopFeatures:{supportsCacheHint:!1},editing:{supportsGeometryUpdate:t,supportsGlobalId:!1,supportsReturnServiceEditsInSourceSpatialReference:!1,supportsRollbackOnFailure:!1,supportsUpdateWithoutM:!1,supportsUploadWithItemId:!1,supportsDeleteByAnonymous:!1,supportsDeleteByOthers:!1,supportsUpdateByAnonymous:!1,supportsUpdateByOthers:!1}}}},68825:(e,t,r)=>{r.d(t,{c:()=>f});var n,s=r(43697),o=r(78286),i=r(5600),a=(r(75215),r(67676),r(52011)),l=r(35454),u=r(96674);const p=new l.X({asc:"ascending",desc:"descending"});let c=n=class extends u.wq{constructor(e){super(e),this.field=null,this.valueExpression=null,this.order="ascending"}clone(){return new n({field:this.field,valueExpression:this.valueExpression,order:this.order})}};(0,s._)([(0,i.Cb)({type:String,json:{write:!0}})],c.prototype,"field",void 0),(0,s._)([(0,i.Cb)({type:String,json:{write:!0}})],c.prototype,"valueExpression",void 0),(0,s._)([(0,i.Cb)({type:p.apiValues,json:{read:p.read,write:p.write}})],c.prototype,"order",void 0),c=n=(0,s._)([(0,a.j)("esri.layers.support.OrderByInfo")],c);const d=c;function y(e,t,r){if(!e)return null;const n=e.find((e=>!!e.field));if(!n)return null;const s=new d;return s.read(n,r),[s]}function h(e,t,r,n){const s=e.find((e=>!!e.field));s&&(0,o.RB)(r,[s.toJSON()],t)}const f=e=>{let t=class extends e{constructor(){super(...arguments),this.orderBy=null}};return(0,s._)([(0,i.Cb)({type:[d],json:{origins:{"web-scene":{write:!1,read:!1}},read:{source:"layerDefinition.orderBy",reader:y},write:{target:"layerDefinition.orderBy",writer:h}}})],t.prototype,"orderBy",void 0),t=(0,s._)([(0,a.j)("esri.layers.mixins.OrderedLayer")],t),t}},51432:(e,t,r)=>{r.d(t,{M8:()=>_,ft:()=>x,FU:()=>F,Bm:()=>j,be:()=>O,eB:()=>E}),r(66577);var n=r(3172),s=r(20102),o=r(66374),i=r(70586),a=r(95330),l=r(17452),u=r(44547),p=r(8744),c=r(86973),d=r(92722);function y(e){return function(e){const t=h.exec(e);if(!t?.groups)return null;const r=t.groups,n=+r.year,s=+r.month-1,o=+r.day,i=+(r.hours??"0"),a=+(r.minutes??"0"),l=+(r.seconds??"0");if(i>23)return null;if(a>59)return null;if(l>59)return null;const u=r.ms??"0",p=u?+u.padEnd(3,"0").substring(0,3):0;let c;if(r.isUTC)c=Date.UTC(n,s,o,i,a,l,p);else if(r.offsetSign){const e=+r.offsetHours,t=+r.offsetMinutes;c=6e4*("+"===r.offsetSign?-1:1)*(60*e+t)+Date.UTC(n,s,o,i,a,l,p)}else c=new Date(n,s,o,i,a,l,p).getTime();return Number.isNaN(c)?null:c}(e)??function(e){const t=new Date(e).getTime();return Number.isNaN(t)?null:t}(e)}const h=/^(?:(?<year>-?\d{4,})-(?<month>\d{2})-(?<day>\d{2}))(?:T(?<hours>\d{2}):(?<minutes>\d{2}):(?<seconds>\d{2})(?:\.(?<ms>\d+))?)?(?:(?<isUTC>Z)|(?:(?<offsetSign>\+|-)(?<offsetHours>\d{2}):(?<offsetMinutes>\d{2})))?$/;var f=r(88724),m=r(1231),g=r(82971),w=r(6570);const b="xlink:href",v="2.0.0",_="__esri_wfs_id__",S="wfs-layer:feature-type-not-found",C="wfs-layer:unknown-geometry-type";async function F(e,t){const r=function(e){const t=Z(e);(function(e){const t=e.firstElementChild?.getAttribute("version");if(t&&t!==v)throw new s.Z("wfs-layer:unsupported-wfs-version",`Unsupported WFS version ${t}. Supported version: ${v}`)})(t),M(t);const r=t.firstElementChild,n=(0,o.Fs)(function(e){return(0,f.H)(e,{FeatureTypeList:{FeatureType:e=>{const t={typeName:"undefined:undefined",name:"",title:"",description:"",extent:null,namespacePrefix:"",namespaceUri:"",supportedSpatialReferences:[]},r=new Set([4326]),n=e=>{const t=parseInt(e.textContent?.match(/(?<wkid>\d+$)/i)?.groups?.wkid??"",10);Number.isNaN(t)||r.add(t)};return(0,f.h)(e,{Name:e=>{const{name:r,prefix:n}=A(e.textContent);t.typeName=`${n}:${r}`,t.name=r,t.namespacePrefix=n,t.namespaceUri=e.lookupNamespaceURI(n)},Abstract:e=>{t.description=e.textContent},Title:e=>{t.title=e.textContent},WGS84BoundingBox:e=>{t.extent=function(e){let t,r,n,s;for(const o of e.children)switch(o.localName){case"LowerCorner":[t,r]=o.textContent.split(" ").map((e=>Number.parseFloat(e)));break;case"UpperCorner":[n,s]=o.textContent.split(" ").map((e=>Number.parseFloat(e)))}return{xmin:t,ymin:r,xmax:n,ymax:s,spatialReference:p.Zn}}(e)},DefaultSRS:n,DefaultCRS:n,OtherSRS:n,OtherCRS:n}),t.title||(t.title=t.name),t.supportedSpatialReferences.push(...r),t}}})}(r));return{operations:k(r),get featureTypes(){return Array.from(n())},readFeatureTypes:n}}((await(0,n.default)(e,{responseType:"text",query:{SERVICE:"WFS",REQUEST:"GetCapabilities",VERSION:v,...t?.customParameters},signal:t?.signal})).data);return function(e,t){(0,l.$U)(e)&&((0,l.D6)(e,t.operations.DescribeFeatureType.url,!0)&&(t.operations.DescribeFeatureType.url=(0,l.hO)(t.operations.DescribeFeatureType.url)),(0,l.D6)(e,t.operations.GetFeature.url,!0)&&(t.operations.GetFeature.url=(0,l.hO)(t.operations.GetFeature.url)))}(e,r),r}const P=new Set(["json","application/json","geojson","application/json; subtype=geojson"]);function k(e){let t=!1;const r={GetCapabilities:{url:""},DescribeFeatureType:{url:""},GetFeature:{url:"",outputFormat:null,supportsPagination:!1}};if((0,f.h)(e,{OperationsMetadata:{Operation:e=>{switch(e.getAttribute("name")){case"GetCapabilities":return{DCP:{HTTP:{Get:e=>{r.GetCapabilities.url=e.getAttribute(b)}}}};case"DescribeFeatureType":return{DCP:{HTTP:{Get:e=>{r.DescribeFeatureType.url=e.getAttribute(b)}}}};case"GetFeature":return{DCP:{HTTP:{Get:e=>{r.GetFeature.url=e.getAttribute(b)}}},Parameter:e=>{if("outputFormat"===e.getAttribute("name"))return{AllowedValues:{Value:e=>{const t=e.textContent;t&&P.has(t.toLowerCase())&&(r.GetFeature.outputFormat=t)}}}}}}},Constraint:e=>{switch(e.getAttribute("name")){case"KVPEncoding":return{DefaultValue:e=>{t="true"===e.textContent.toLowerCase()}};case"ImplementsResultPaging":return{DefaultValue:e=>{r.GetFeature.supportsPagination="true"===e.textContent.toLowerCase()}}}}}}),!t)throw new s.Z("wfs-layer:kvp-encoding-not-supported","WFS service doesn't support key/value pair (KVP) encoding");if((0,i.Wi)(r.GetFeature.outputFormat))throw new s.Z("wfs-layer:geojson-not-supported","WFS service doesn't support GeoJSON output format");return r}function x(e,t,r){return(0,o.sE)(e,(e=>r?e.name===t&&e.namespaceUri===r:e.typeName===t||e.name===t))}async function O(e,t,r,n={}){const{featureType:o,extent:l}=await async function(e,t,r,n={}){const{spatialReference:o=g.Z.WGS84}=n,a=e.readFeatureTypes(),l=t?x(a,t,r):a.next().value;if((0,i.Wi)(l))throw t?new s.Z(S,`The type '${t}' could not be found in the service`):new s.Z("wfs-layer:empty-service","The service is empty");let c=new w.Z({...l.extent,spatialReference:o});if(!(0,p.fS)(o,p.Zn))try{await(0,u.iQ)(p.Zn,o,void 0,n),c=(0,u.iV)(c,p.Zn)}catch{throw new s.Z("wfs-layer:unsupported-spatial-reference","Projection not supported")}return{extent:c,spatialReference:o,featureType:l}}(e,t,r,n),{fields:c,geometryType:d,swapXY:y,objectIdField:h,geometryField:f}=await async function(e,t,r={}){const[n,o]=await(0,a.as)([R(e.operations.DescribeFeatureType.url,t,r),T(e,t,r)]);if(n.error||o.error)throw new s.Z("wfs-layer:getWFSLayerTypeInfo-error",`An error occurred while getting info about the feature type '${t}'`,{error:n.error||o.error});const{fields:l,errors:u}=n.value??{},p=n.value?.geometryType||o.value?.geometryType,c=o.value?.swapXY??!1;if((0,i.Wi)(p))throw new s.Z(C,`The geometry type could not be determined for type '${t}`,{typeName:t,geometryType:p,fields:l,errors:u});return{...E(l??[]),geometryType:p,swapXY:c}}(e,o.typeName,n);return{url:e.operations.GetCapabilities.url,name:o.name,namespaceUri:o.namespaceUri,fields:c,geometryField:f,geometryType:d,objectIdField:h,spatialReference:n.spatialReference??g.Z.WGS84,extent:l,swapXY:y,wfsCapabilities:e,customParameters:n.customParameters}}function E(e){const t=e.find((e=>"geometry"===e.type));let r=e.find((e=>"oid"===e.type));return e=e.filter((e=>"geometry"!==e.type)),r||(r=new m.Z({name:_,type:"oid",alias:_}),e.unshift(r)),{geometryField:t?.name??null,objectIdField:r.name,fields:e}}async function T(e,t,r={}){let s,o=!1;const[i,a]=await Promise.all([j(e.operations.GetFeature.url,t,e.operations.GetFeature.outputFormat,{...r,count:1}),(0,n.default)(e.operations.GetFeature.url,{responseType:"text",query:I(t,void 0,{...r,count:1}),signal:r?.signal})]),l="FeatureCollection"===i.type&&i.features[0]?.geometry;if(l){let e;switch(s=c.M.fromJSON((0,d.q9)(l.type)),l.type){case"Point":e=l.coordinates;break;case"LineString":case"MultiPoint":e=l.coordinates[0];break;case"MultiLineString":case"Polygon":e=l.coordinates[0][0];break;case"MultiPolygon":e=l.coordinates[0][0][0]}const t=/<[^>]*pos[^>]*> *(-?\d+(?:\.\d+)?) (-?\d+(?:\.\d+)?)/.exec(a.data);if(t){const r=e[0].toFixed(3),n=e[1].toFixed(3),s=parseFloat(t[1]).toFixed(3);r===parseFloat(t[2]).toFixed(3)&&n===s&&(o=!0)}}return{geometryType:s,swapXY:o}}async function R(e,t,r){return function(e,t){const{name:r}=A(e),n=Z(t);M(n);const a=(0,o.sE)((0,f.H)(n.firstElementChild,{element:e=>({name:e.getAttribute("name"),typeName:A(e.getAttribute("type")).name})}),(({name:e})=>e===r));if((0,i.pC)(a)){const e=(0,o.sE)((0,f.H)(n.firstElementChild,{complexType:e=>e}),(e=>e.getAttribute("name")===a.typeName));if((0,i.pC)(e))return function(e){const t=[],r=[];let n;const o=(0,f.H)(e,{complexContent:{extension:{sequence:{element:e=>e}}}});for(const i of o){const o=i.getAttribute("name");if(!o)continue;let a,l;if(i.hasAttribute("type")?a=A(i.getAttribute("type")).name:(0,f.h)(i,{simpleType:{restriction:e=>(a=A(e.getAttribute("base")).name,{maxLength:e=>{l=+e.getAttribute("value")}})}}),!a)continue;const u="true"===i.getAttribute("nillable");let p=!1;switch(a.toLowerCase()){case"integer":case"nonpositiveinteger":case"negativeinteger":case"long":case"int":case"short":case"byte":case"nonnegativeinteger":case"unsignedlong":case"unsignedint":case"unsignedshort":case"unsignedbyte":case"positiveinteger":r.push(new m.Z({name:o,alias:o,type:"integer",nullable:u}));break;case"float":case"double":case"decimal":r.push(new m.Z({name:o,alias:o,type:"double",nullable:u}));break;case"boolean":case"string":case"gyearmonth":case"gyear":case"gmonthday":case"gday":case"gmonth":case"anyuri":case"qname":case"notation":case"normalizedstring":case"token":case"language":case"idrefs":case"entities":case"nmtoken":case"nmtokens":case"name":case"ncname":case"id":case"idref":case"entity":case"duration":case"time":r.push(new m.Z({name:o,alias:o,type:"string",nullable:u,length:l??255}));break;case"datetime":case"date":r.push(new m.Z({name:o,alias:o,type:"date",nullable:u,length:l??36}));break;case"pointpropertytype":n="point",p=!0;break;case"multipointpropertytype":n="multipoint",p=!0;break;case"curvepropertytype":case"multicurvepropertytype":case"multilinestringpropertytype":n="polyline",p=!0;break;case"surfacepropertytype":case"multisurfacepropertytype":case"multipolygonpropertytype":n="polygon",p=!0;break;case"geometrypropertytype":case"multigeometrypropertytype":p=!0,t.push(new s.Z(C,`geometry type '${a}' is not supported`,{type:(new XMLSerializer).serializeToString(e)}));break;default:t.push(new s.Z("wfs-layer:unknown-field-type",`Unknown field type '${a}'`,{type:(new XMLSerializer).serializeToString(e)}))}p&&r.push(new m.Z({name:o,alias:o,type:"geometry",nullable:u}))}for(const e of r)if("integer"===e.type&&!e.nullable&&N.has(e.name.toLowerCase())){e.type="oid";break}return{geometryType:n,fields:r,errors:t}}(e)}throw new s.Z(S,`Type '${e}' not found in document`,{document:(new XMLSerializer).serializeToString(n)})}(t,(await(0,n.default)(e,{responseType:"text",query:{SERVICE:"WFS",REQUEST:"DescribeFeatureType",VERSION:v,TYPENAME:t,...r?.customParameters},signal:r?.signal})).data)}const N=new Set(["objectid","fid"]);async function j(e,t,r,o){let{data:i}=await(0,n.default)(e,{responseType:"text",query:I(t,r,o),signal:o?.signal});i=i.replace(/": +(-?\d+),(\d+)(,)?/g,'": $1.$2$3');try{if(o?.dateFields?.length){const e=new Set(o.dateFields);return JSON.parse(i,((t,r)=>e.has(t)?y(r):r))}return JSON.parse(i)}catch(e){throw new s.Z("wfs-layer:malformed-json","Error while parsing the response",{response:i,error:e})}}function I(e,t,r){return{SERVICE:"WFS",REQUEST:"GetFeature",VERSION:v,TYPENAMES:e,OUTPUTFORMAT:t,SRSNAME:"EPSG:4326",STARTINDEX:r?.startIndex,COUNT:r?.count,...r?.customParameters}}function Z(e){return(new DOMParser).parseFromString(e.trim(),"text/xml")}function A(e){const[t,r]=e.split(":");return{prefix:r?t:"",name:r??t}}function M(e){let t="",r="";if((0,f.h)(e.firstElementChild,{Exception:e=>(t=e.getAttribute("exceptionCode"),{ExceptionText:e=>{r=e.textContent}})}),t)throw new s.Z(`wfs-layer:${t}`,r)}},88724:(e,t,r)=>{function n(e,t){if(e&&t)for(const r of e.children)if(r.localName in t){const e=t[r.localName];if("function"==typeof e){const t=e(r);t&&n(r,t)}else n(r,e)}}function*s(e,t){for(const r of e.children)if(r.localName in t){const e=t[r.localName];"function"==typeof e?yield e(r):yield*s(r,e)}}r.d(t,{H:()=>s,h:()=>n})},51706:(e,t,r)=>{var n,s;function o(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function i(e){return null!=e&&!isNaN(e)&&isFinite(e)}function a(e){return e.valueExpression?n.Expression:e.field&&"string"==typeof e.field?n.Field:n.Unknown}function l(e,t){const r=t||a(e),o=e.valueUnit||"unknown";return r===n.Unknown?s.Constant:e.stops?s.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?s.ClampedLinear:"unknown"===o?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?s.Proportional:s.Additive:s.Identity:s.RealWorldSize}r.d(t,{PS:()=>a,QW:()=>l,RY:()=>n,hL:()=>s,iY:()=>o,qh:()=>i}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(n||(n={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(s||(s={}))},74889:(e,t,r)=>{r.d(t,{Z:()=>v});var n,s=r(43697),o=r(66577),i=r(38171),a=r(35454),l=r(96674),u=r(22974),p=r(70586),c=r(5600),d=(r(75215),r(71715)),y=r(52011),h=r(30556),f=r(82971),m=r(33955),g=r(1231);const w=new a.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let b=n=class extends l.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const r=f.Z.fromJSON(t.spatialReference),n=[];for(let t=0;t<e.length;t++){const s=e[t],o=i.Z.fromJSON(s),a=s.geometry&&s.geometry.spatialReference;(0,p.pC)(o.geometry)&&!a&&(o.geometry.spatialReference=r);const l=s.aggregateGeometries,u=o.aggregateGeometries;if(l&&(0,p.pC)(u))for(const e in u){const t=u[e],n=l[e]?.spatialReference;(0,p.pC)(t)&&!n&&(t.spatialReference=r)}n.push(o)}return n}writeGeometryType(e,t,r,n){if(e)return void w.write(e,t,r,n);const{features:s}=this;if(s)for(const e of s)if(e&&(0,p.pC)(e.geometry))return void w.write(e.geometry.type,t,r,n)}readQueryGeometry(e,t){if(!e)return null;const r=!!e.spatialReference,n=(0,m.im)(e);return n&&!r&&t.spatialReference&&(n.spatialReference=f.Z.fromJSON(t.spatialReference)),n}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:r}=this;if(r)for(const e of r)if(e&&(0,p.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new n(this.cloneProperties())}cloneProperties(){return(0,u.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let r=0;r<t.features.length;r++){const n=t.features[r];if(n.geometry){const t=e&&e[r];n.geometry=t&&t.toJSON()||n.geometry}}return t}quantize(e){const{scale:[t,r],translate:[n,s]}=e,o=this.features,i=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-n)/t)),(e=>Math.round((s-e)/r)));for(let e=0,t=o.length;e<t;e++)i?.((0,p.Wg)(o[e].geometry))||(o.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:r}=this;if(!r)return this;const{translate:[n,s],scale:[o,i]}=r,a=this._getHydrationFunction(e,(e=>e*o+n),(e=>s-e*i));for(const{geometry:e}of t)(0,p.pC)(e)&&a&&a(e);return this.transform=null,this}_quantizePoints(e,t,r){let n,s;const o=[];for(let i=0,a=e.length;i<a;i++){const a=e[i];if(i>0){const e=t(a[0]),i=r(a[1]);e===n&&i===s||(o.push([e-n,i-s]),n=e,s=i)}else n=t(a[0]),s=r(a[1]),o.push([n,s])}return o.length>0?o:null}_getQuantizationFunction(e,t,r){return"point"===e?e=>(e.x=t(e.x),e.y=r(e.y),e):"polyline"===e||"polygon"===e?e=>{const n=(0,m.oU)(e)?e.rings:e.paths,s=[];for(let e=0,o=n.length;e<o;e++){const o=n[e],i=this._quantizePoints(o,t,r);i&&s.push(i)}return s.length>0?((0,m.oU)(e)?e.rings=s:e.paths=s,e):null}:"multipoint"===e?e=>{const n=this._quantizePoints(e.points,t,r);return n&&n.length>0?(e.points=n,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,r){return"point"===e?e=>{e.x=t(e.x),e.y=r(e.y)}:"polyline"===e||"polygon"===e?e=>{const n=(0,m.oU)(e)?e.rings:e.paths;let s,o;for(let e=0,i=n.length;e<i;e++){const i=n[e];for(let e=0,n=i.length;e<n;e++){const n=i[e];e>0?(s+=n[0],o+=n[1]):(s=n[0],o=n[1]),n[0]=t(s),n[1]=r(o)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=r(e.ymin),e.xmax=t(e.xmax),e.ymax=r(e.ymax)}:"multipoint"===e?e=>{const n=e.points;let s,o;for(let e=0,i=n.length;e<i;e++){const i=n[e];e>0?(s+=i[0],o+=i[1]):(s=i[0],o=i[1]),i[0]=t(s),i[1]=r(o)}}:null}};(0,s._)([(0,c.Cb)({type:String,json:{write:!0}})],b.prototype,"displayFieldName",void 0),(0,s._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"exceededTransferLimit",void 0),(0,s._)([(0,c.Cb)({type:[i.Z],json:{write:!0}})],b.prototype,"features",void 0),(0,s._)([(0,d.r)("features")],b.prototype,"readFeatures",null),(0,s._)([(0,c.Cb)({type:[g.Z],json:{write:!0}})],b.prototype,"fields",void 0),(0,s._)([(0,c.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:w.read}}})],b.prototype,"geometryType",void 0),(0,s._)([(0,h.c)("geometryType")],b.prototype,"writeGeometryType",null),(0,s._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"hasM",void 0),(0,s._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"hasZ",void 0),(0,s._)([(0,c.Cb)({types:o.qM,json:{write:!0}})],b.prototype,"queryGeometry",void 0),(0,s._)([(0,d.r)("queryGeometry")],b.prototype,"readQueryGeometry",null),(0,s._)([(0,c.Cb)({type:f.Z,json:{write:!0}})],b.prototype,"spatialReference",void 0),(0,s._)([(0,h.c)("spatialReference")],b.prototype,"writeSpatialReference",null),(0,s._)([(0,c.Cb)({json:{write:!0}})],b.prototype,"transform",void 0),b=n=(0,s._)([(0,y.j)("esri.rest.support.FeatureSet")],b),b.prototype.toJSON.isDefaultToJSON=!0;const v=b},58333:(e,t,r)=>{r.d(t,{ET:()=>o,I4:()=>s,eG:()=>l,lF:()=>i,lj:()=>p,qP:()=>a,wW:()=>u});const n=[252,146,31,255],s={type:"esriSMS",style:"esriSMSCircle",size:6,color:n,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},o={type:"esriSLS",style:"esriSLSSolid",width:.75,color:n},i={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},a={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},l={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},u={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},p={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);