package org.thingsboard.server.controller.fault;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.fault.FaultTaskMService;
import org.thingsboard.server.dao.model.sql.fault.FaultTaskM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@RestController
@RequestMapping("api/fault/task/m")
public class FaultTaskMController extends BaseController {

    @Autowired
    private FaultTaskMService faultTaskMService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String code,
                                 @RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam(required = false, defaultValue = "") String planName,
                                 @RequestParam(required = false, defaultValue = "") String teamName,
                                 @RequestParam(required = false, defaultValue = "") String userName,
                                 @RequestParam(required = false, defaultValue = "") String status,
                                 @RequestParam(required = false, defaultValue = "") String auditStatus,
                                 Long startStartTime,
                                 Long startEndTime,
                                 Long endStartTime,
                                 Long endEndTime,
                                 int page, int size) throws ThingsboardException {

        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(faultTaskMService.getList(code, name, planName, teamName, userName, status, auditStatus, startStartTime == null ? null : new Date(startStartTime), startEndTime == null ? null : new Date(startEndTime), endStartTime == null ? null : new Date(endStartTime), endEndTime == null ? null : new Date(endEndTime), page, size, tenantId));
    }


    @GetMapping("detail/{mainId}")
    public IstarResponse getDetail(@PathVariable String mainId) {
        return IstarResponse.ok(faultTaskMService.getDetail(mainId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody FaultTaskM faultTaskM) throws ThingsboardException {
        faultTaskM.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        faultTaskM.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(faultTaskMService.save(faultTaskM));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return faultTaskMService.delete(ids);
    }


    @PostMapping("changeStatus")
    public IstarResponse changeStatus(@RequestBody FaultTaskM faultTaskM) throws ThingsboardException {
        faultTaskMService.reviewer(faultTaskM);

        return IstarResponse.ok("操作成功");
    }

    @PostMapping("reviewer")
    public IstarResponse reviewer(@RequestBody FaultTaskM faultTaskM) throws ThingsboardException {
        boolean checkAuditor = this.checkAuditor(faultTaskM.getId(), UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        if (!checkAuditor) {
            return IstarResponse.error("您没有审核权限");
        }
        faultTaskM.setAuditor(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        faultTaskMService.reviewer(faultTaskM);

        return IstarResponse.ok("审核成功");
    }

    private boolean checkAuditor(String id, String userId) {
        return faultTaskMService.checkAuditor(id, userId);
    }

    /**
     * 统计
     *
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("statistics")
    public IstarResponse statistics(Long startTime, Long endTime) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(faultTaskMService.statistics(startTime, endTime, tenantId));
    }
}
