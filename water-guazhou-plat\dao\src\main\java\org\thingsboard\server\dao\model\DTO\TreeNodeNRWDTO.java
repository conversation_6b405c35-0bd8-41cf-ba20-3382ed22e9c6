package org.thingsboard.server.dao.model.DTO;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class TreeNodeNRWDTO {

    private String id;

    private String parentId;

    // 节点名称
    private String name;

    private Integer level;

    private BigDecimal supplyWater;

    private BigDecimal saleWater;

    private BigDecimal diffWater;

    private List<TreeNodeNRWDTO> children;

}
