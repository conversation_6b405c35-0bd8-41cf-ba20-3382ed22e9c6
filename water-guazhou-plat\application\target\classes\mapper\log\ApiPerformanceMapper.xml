<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.optionLog.ApiPerformanceMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.ApiPerformance" id="ApiPerformanceResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="methodName"    column="method_name"    />
        <result property="className"    column="class_name"    />
        <result property="executionTime"    column="execution_time"    />
        <result property="callTime"    column="call_time"    />
        <result property="success"    column="success"    />
        <result property="description"    column="description"    />
    </resultMap>

    <sql id="selectApiPerformanceVo">
        select id, user_id, method_name, class_name, execution_time, call_time, success, description from api_performance
    </sql>

    <select id="selectApiPerformanceList" parameterType="org.thingsboard.server.dao.model.sql.base.ApiPerformance" resultMap="ApiPerformanceResult">
        <include refid="selectApiPerformanceVo"/>
        <where>  
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="methodName != null  and methodName != ''"> and method_name like concat('%', #{methodName}, '%')</if>
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="executionTime != null "> and execution_time = #{executionTime}</if>
            <if test="callTime != null "> and call_time = #{callTime}</if>
            <if test="success != null  and success != ''"> and success = #{success}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
        </where>
        order by call_time desc
    </select>
    
    <select id="selectApiPerformanceById" parameterType="String" resultMap="ApiPerformanceResult">
        <include refid="selectApiPerformanceVo"/>
        where id = #{id}
    </select>

    <insert id="insertApiPerformance" parameterType="org.thingsboard.server.dao.model.sql.base.ApiPerformance">
        insert into api_performance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="methodName != null">method_name,</if>
            <if test="className != null">class_name,</if>
            <if test="executionTime != null">execution_time,</if>
            <if test="callTime != null">call_time,</if>
            <if test="success != null">success,</if>
            <if test="description != null">description,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="methodName != null">#{methodName},</if>
            <if test="className != null">#{className},</if>
            <if test="executionTime != null">#{executionTime},</if>
            NOW(),
            <if test="success != null">#{success},</if>
            <if test="description != null">#{description},</if>
         </trim>
    </insert>

    <update id="updateApiPerformance" parameterType="org.thingsboard.server.dao.model.sql.base.ApiPerformance">
        update api_performance
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="methodName != null">method_name = #{methodName},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="executionTime != null">execution_time = #{executionTime},</if>
            <if test="callTime != null">call_time = #{callTime},</if>
            <if test="success != null">success = #{success},</if>
            <if test="description != null">description = #{description},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApiPerformanceById" parameterType="String">
        delete from api_performance where id = #{id}
    </delete>

    <delete id="deleteApiPerformanceByIds" parameterType="String">
        delete from api_performance where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>