package org.thingsboard.server.utils.imodel.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.apache.ibatis.session.SqlSession;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor;
import org.thingsboard.server.dao.util.imodel.Environment;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;
import org.thingsboard.server.dao.util.reflection.ReflectionUtils;
import org.thingsboard.server.utils.ExcelUtil;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.lang.reflect.Type;

public final class IStarMessageConverterHandler implements HandlerMethodReturnValueHandler, HandlerMethodArgumentResolver {
    private final RequestResponseBodyMethodProcessor handler;
    private final JdbcTemplate jdbcTemplate;
    private final SqlSession sqlSession;

    private final Method readWithMessageConvertersMethod = ReflectionUtils.getMethod("readWithMessageConverters",
            new Class<?>[]{NativeWebRequest.class, MethodParameter.class, Type.class}, RequestResponseBodyMethodProcessor.class);
    private final EntityValidator validator;

    public IStarMessageConverterHandler(RequestResponseBodyMethodProcessor handler, JdbcTemplate jdbcTemplate, SqlSession sessionFactory, EntityValidator validator) {
        this.handler = handler;
        this.jdbcTemplate = jdbcTemplate;
        this.sqlSession = sessionFactory;
        this.validator = validator;
    }

    @Override
    public boolean supportsReturnType(MethodParameter methodParameter) {
        return handler.supportsReturnType(methodParameter);
    }

    @Override
    public void handleReturnValue(Object o, MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest) throws Exception {
        if (!AnnotatedElementUtils.hasAnnotation(methodParameter.getDeclaringClass(), IStarController.class)) {
            handler.handleReturnValue(o, methodParameter, modelAndViewContainer, nativeWebRequest);
            return;
        }

        if (o == null) {
            this.handler.handleReturnValue(IstarResponse.ok(), methodParameter, modelAndViewContainer, nativeWebRequest);
            return;
        }

        int version = (int) AnnotatedElementUtils.getMergedAnnotationAttributes(methodParameter.getDeclaringClass(), IStarController.class).get("version");

        try (ReturnHelper helper = new ReturnHelper(jdbcTemplate, sqlSession, Environment.getEnvironment(), null, version)) {
            if (o.getClass().equals(ExcelFileInfo.class)) {
                HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
                ExcelFileInfo info = (ExcelFileInfo) helper.process(o, null);
                ExcelUtil.downloadExcelFile(info.getTitle(), info.getHeadMap(), JSONArray.parseArray(JSON.toJSONString(info.getItems())), response, info.isWithoutTitle());
                return;
            }

            if (o.getClass().equals(IstarResponse.class)) {
                this.handler.handleReturnValue(helper.process(o, null), methodParameter, modelAndViewContainer, nativeWebRequest);
                return;
            }
            if (o instanceof Boolean && !(Boolean) o) {
                this.handler.handleReturnValue(IstarResponse.falseError(), methodParameter, modelAndViewContainer, nativeWebRequest);
                return;
            }
            this.handler.handleReturnValue(helper.process(IstarResponse.ok(o), null), methodParameter, modelAndViewContainer, nativeWebRequest);
        }
    }

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return handler.supportsParameter(parameter);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        return handler.resolveArgument(parameter, mavContainer, webRequest, binderFactory);
    }
}
