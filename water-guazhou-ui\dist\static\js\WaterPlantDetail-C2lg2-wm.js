import{d as O,cN as U,r as E,a8 as M,u as $,c as q,o as J,ay as j,g as c,n as m,bo as w,i as o,q as h,p as i,F as I,aB as k,aJ as z,h as H,G as K,bh as g,ab as Q,bt as X,dz as Y,dA as Z,br as tt,C as et}from"./index-r0dFAfgr.js";import{h as at}from"./chart-wy3NEK2T.js";import{c as ot,k as st}from"./onemap-CEunQziB.js";import{f as it,d as rt}from"./zhandian-YaGuQZe6.js";import{g as f}from"./echarts-Bhn8T7lM.js";import{u as nt}from"./useDetector-BRcb7GRN.js";const lt={class:"one-map-detail"},dt={class:"row1"},ct={class:"pie-charts"},ut={class:"pie-chart"},pt={class:"pie-chart"},mt={class:"row2"},ht={class:"detail-attrgrou-radio"},ft={class:"detail-right"},_t={class:"list-items overlay-y"},vt={class:"item-label"},Ct={class:"item-content"},yt={class:"chart-box"},gt={class:"row3"},xt=["src"],Rt=O({__name:"WaterPlantDetail",emits:["refresh","mounted"],setup(wt,{expose:V,emit:F}){const D=F,{proxy:x}=U(),t=E({curRadio:"",radioGroup:[],pieChart1:f(0,{max:100,title:"今日供水量(万m³)"}),pieChart2:f(0,{max:100,title:"昨日供水量(万m³)"}),pieChart3:f(0,{max:3e3,title:"本月供水量(万m³)"}),lineChartOption:null,stationRealTimeData:[],detailLoading:!1}),N=M(()=>{var a;let e=(a=t.curRow)==null?void 0:a.scadaUrl;if(!e)return"";const s=e.indexOf("?")!==-1?"&":"?";return e+=s+"token="+$().token,e}),P=async e=>{var s,a,u,C;D("refresh",{title:e.name}),t.detailLoading=!0;try{if(e.fromAllStation){const n=await ot({});t.curRow=(s=n.data)==null?void 0:s.data.find(d=>d.stationId===e.stationId)}else t.curRow=e;const p=n=>{const d=Q(n??0);return{value:+d.value.toFixed(2),unit:d.unit}},l=p((a=t.curRow)==null?void 0:a.todayWaterSupply),r=p((u=t.curRow)==null?void 0:u.yesterdayWaterSupply),_=p((C=t.curRow)==null?void 0:C.monthWaterSupply);t.pieChart1=f(l.value,{max:100,title:"今日供水量("+(l.unit||"")+"m³)"}),t.pieChart2=f(r.value,{max:100,title:"昨日供水量("+(l.unit||"")+"m³)"}),t.pieChart3=f(_.value,{max:1e3,title:"本月供水量("+(l.unit||"")+"m³)"}),Array.from({length:3}).map((n,d)=>{var y;(y=x.$refs["refChart"+(d+1)])==null||y.resize()});const A=st({stationId:e.stationId}).then(n=>{var b,S;const d=(b=n.data.data.pressure)==null?void 0:b.map(R=>{var v;return(v=R.value)==null?void 0:v.toFixed(2)}),y=n.data.data.todayTotalFlowDataList.map(R=>{var v;return(v=R.value)==null?void 0:v.toFixed(2)});t.lineChartOption=at({line1:{data:y,unit:"m³",name:"供水量"},line2:{data:d,unit:"MPa",name:"压力"}}),(S=x.$refs.refChart4)==null||S.resize()}),B=it({stationId:e.stationId}).then(n=>{t.radioGroup=n.data||[],t.curRadio=t.radioGroup[0],G(t.radioGroup[0])});Promise.all([A,B]).finally(()=>{t.detailLoading=!1})}catch(p){console.log(p),t.detailLoading=!1}},G=async e=>{var a;const s=await rt((a=t.curRow)==null?void 0:a.stationId,e);t.stationRealTimeData=s.data||[]};V({refreshDetail:P});const T=()=>{Array.from({length:3}).map((e,s)=>{var a;(a=x.$refs["refChart"+(s+1)])==null||a.resize()})},W=nt(),L=q();return J(()=>{D("mounted"),W.listenToMush(L.value,T)}),(e,s)=>{const a=X,u=j("VChart"),C=Y,p=Z,l=tt;return c(),m("div",lt,[w((c(),m("div",dt,[h(a,{size:"default",title:"水厂监测",type:"simple",class:"row-title"}),i("div",ct,[i("div",{ref_key:"refChartDiv",ref:L,class:"pie-chart"},[h(u,{ref:"refChart1",option:o(t).pieChart1},null,8,["option"])],512),i("div",ut,[h(u,{ref:"refChart2",option:o(t).pieChart2},null,8,["option"])]),i("div",pt,[h(u,{ref:"refChart3",option:o(t).pieChart3},null,8,["option"])])])])),[[l,o(t).detailLoading]]),i("div",mt,[i("div",ht,[h(p,{modelValue:o(t).curRadio,"onUpdate:modelValue":s[0]||(s[0]=r=>o(t).curRadio=r),onChange:G},{default:I(()=>[(c(!0),m(k,null,z(o(t).radioGroup,(r,_)=>(c(),H(C,{key:_,label:r},{default:I(()=>[K(g(r),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),i("div",ft,[w((c(),m("div",_t,[(c(!0),m(k,null,z(o(t).stationRealTimeData,(r,_)=>(c(),m("div",{key:_,class:"list-item"},[i("div",vt,g(r.propertyName),1),i("div",Ct,g(r.value||"--")+" "+g(r.unit),1)]))),128))])),[[l,o(t).detailLoading]]),w((c(),m("div",yt,[h(u,{ref:"refChart4",option:o(t).lineChartOption},null,8,["option"])])),[[l,o(t).detailLoading]])])]),h(a,{size:"default",title:"组态信息",type:"simple",class:"row-title"}),i("div",gt,[i("iframe",{ref:"refIframe",frameborder:"0",scrolling:"auto",src:o(N),width:"100%",height:"100%",allowfullscreen:"true"},null,8,xt)])])}}}),kt=et(Rt,[["__scopeId","data-v-8dcd3c40"]]);export{kt as default};
