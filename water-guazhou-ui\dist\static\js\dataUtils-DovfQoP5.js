import{e as O,y as z,ai as Rt,a as $t,W as Lt,s as jt,i as Z,h as Et,S as Nt,R as Gt}from"./Point-WxyopZva.js";import{R as $,T as H,a5 as qt,aO as ft,a4 as nt,fu as It}from"./index-r0dFAfgr.js";import{s as gt}from"./pixelRangeUtils-Dr0gmLDH.js";import{w as Ot,b4 as Wt}from"./MapView-DaoQedLH.js";let yt=class{constructor(e=null,n=null,r=null){this.minValue=e,this.maxValue=n,this.noDataValue=r}};var tt;let G=tt=class extends Lt{static createEmptyBand(t,e){return new(tt.getPixelArrayConstructor(t))(e)}static getPixelArrayConstructor(t){let e;switch(t){case"u1":case"u2":case"u4":case"u8":e=Uint8Array;break;case"u16":e=Uint16Array;break;case"u32":e=Uint32Array;break;case"s8":e=Int8Array;break;case"s16":e=Int16Array;break;case"s32":e=Int32Array;break;case"f32":case"c64":case"c128":case"unknown":e=Float32Array;break;case"f64":e=Float64Array}return e}constructor(t){super(t),this.width=null,this.height=null,this.pixelType="f32",this.validPixelCount=null,this.mask=null,this.maskIsAlpha=!1,this.premultiplyAlpha=!1,this.statistics=null,this.depthCount=1}castPixelType(t){if(!t)return"f32";let e=t.toLowerCase();return["u1","u2","u4"].includes(e)?e="u8":["unknown","u8","s8","u16","s16","u32","s32","f32","f64"].includes(e)||(e="f32"),e}getPlaneCount(){var t;return(t=this.pixels)==null?void 0:t.length}addData(t){if(!t.pixels||t.pixels.length!==this.width*this.height)throw new jt("pixelblock:invalid-or-missing-pixels","add data requires valid pixels array that has same length defined by pixel block width * height");this.pixels||(this.pixels=[]),this.statistics||(this.statistics=[]),this.pixels.push(t.pixels),this.statistics.push(t.statistics??new yt)}getAsRGBA(){const t=new ArrayBuffer(this.width*this.height*4);switch(this.pixelType){case"s8":case"s16":case"u16":case"s32":case"u32":case"f32":case"f64":this._fillFromNon8Bit(t);break;default:this._fillFrom8Bit(t)}return new Uint8ClampedArray(t)}getAsRGBAFloat(){const t=new Float32Array(this.width*this.height*4);return this._fillFrom32Bit(t),t}updateStatistics(){if(!this.pixels)return;this.statistics=this.pixels.map(n=>this._calculateBandStatistics(n,this.mask));const t=this.mask;let e=0;if($(t))for(let n=0;n<t.length;n++)t[n]&&e++;else e=this.width*this.height;this.validPixelCount=e}clamp(t){if(!t||t==="f64"||t==="f32"||!this.pixels)return;const[e,n]=gt(t),r=this.pixels,a=this.width*this.height,s=r.length;let l,c,o;const h=[];for(let i=0;i<s;i++){o=tt.createEmptyBand(t,a),l=r[i];for(let u=0;u<a;u++)c=l[u],o[u]=c>n?n:c<e?e:c;h.push(o)}this.pixels=h,this.pixelType=t}extractBands(t){const{pixels:e,statistics:n}=this;if(H(t)||t.length===0||!e||e.length===0)return this;const r=e.length,a=t.some(l=>l>=e.length),s=r===t.length&&!t.some((l,c)=>l!==c);return a||s?this:new tt({pixelType:this.pixelType,width:this.width,height:this.height,mask:this.mask,validPixelCount:this.validPixelCount,maskIsAlpha:this.maskIsAlpha,pixels:t.map(l=>e[l]),statistics:n&&t.map(l=>n[l])})}clone(){const t=new tt({width:this.width,height:this.height,pixelType:this.pixelType,maskIsAlpha:this.maskIsAlpha,validPixelCount:this.validPixelCount});let e;$(this.mask)&&(this.mask instanceof Uint8Array?t.mask=new Uint8Array(this.mask):t.mask=this.mask.slice(0));const n=tt.getPixelArrayConstructor(this.pixelType);if(this.pixels&&this.pixels.length>0){t.pixels=[];const r=!!this.pixels[0].slice;for(e=0;e<this.pixels.length;e++)t.pixels[e]=r?this.pixels[e].slice(0,this.pixels[e].length):new n(this.pixels[e])}if(this.statistics)for(t.statistics=[],e=0;e<this.statistics.length;e++)t.statistics[e]=qt(this.statistics[e]);return t.premultiplyAlpha=this.premultiplyAlpha,t}_fillFrom8Bit(t){const{mask:e,maskIsAlpha:n,premultiplyAlpha:r,pixels:a}=this;if(!t||!a||!a.length)return void Z.getLogger(this.declaredClass).error("getAsRGBA()","Unable to convert to RGBA. The input pixel block is empty.");let s,l,c,o;s=l=c=a[0],a.length>=3?(l=a[1],c=a[2]):a.length===2&&(l=a[1]);const h=new Uint32Array(t),i=this.width*this.height;if(s.length===i)if($(e)&&e.length===i)if(n)for(o=0;o<i;o++){const u=e[o];if(u){const p=u/255;h[o]=r?u<<24|c[o]*p<<16|l[o]*p<<8|s[o]*p:u<<24|c[o]<<16|l[o]<<8|s[o]}}else for(o=0;o<i;o++)e[o]&&(h[o]=255<<24|c[o]<<16|l[o]<<8|s[o]);else for(o=0;o<i;o++)h[o]=255<<24|c[o]<<16|l[o]<<8|s[o];else Z.getLogger(this.declaredClass).error("getAsRGBA()","Unable to convert to RGBA. The pixelblock is invalid.")}_fillFromNon8Bit(t){const{pixels:e,mask:n,statistics:r}=this;if(!t||!e||!e.length)return void Z.getLogger(this.declaredClass).error("getAsRGBA()","Unable to convert to RGBA. The input pixel block is empty.");const a=this.pixelType;let s=1,l=0,c=1;if(r&&r.length>0){for(const m of r)if(m.minValue!=null&&(l=Math.min(l,m.minValue)),m.maxValue!=null&&m.minValue!=null){const g=m.maxValue-m.minValue;c=Math.max(c,g)}s=255/c}else{let m=255;a==="s8"?(l=-128,m=127):a==="u16"?m=65535:a==="s16"?(l=-32768,m=32767):a==="u32"?m=4294967295:a==="s32"?(l=-2147483648,m=2147483647):a==="f32"?(l=-34e38,m=34e38):a==="f64"&&(l=-Number.MAX_VALUE,m=Number.MAX_VALUE),s=255/(m-l)}const o=new Uint32Array(t),h=this.width*this.height;let i,u,p,f,d;if(i=u=p=e[0],i.length!==h)return Z.getLogger(this.declaredClass).error("getAsRGBA()","Unable to convert to RGBA. The pixelblock is invalid.");if(e.length>=2)if(u=e[1],e.length>=3&&(p=e[2]),$(n)&&n.length===h)for(f=0;f<h;f++)n[f]&&(o[f]=255<<24|(p[f]-l)*s<<16|(u[f]-l)*s<<8|(i[f]-l)*s);else for(f=0;f<h;f++)o[f]=255<<24|(p[f]-l)*s<<16|(u[f]-l)*s<<8|(i[f]-l)*s;else if($(n)&&n.length===h)for(f=0;f<h;f++)d=(i[f]-l)*s,n[f]&&(o[f]=255<<24|d<<16|d<<8|d);else for(f=0;f<h;f++)d=(i[f]-l)*s,o[f]=255<<24|d<<16|d<<8|d}_fillFrom32Bit(t){const{pixels:e,mask:n}=this;if(!t||!e||!e.length)return Z.getLogger(this.declaredClass).error("getAsRGBAFloat()","Unable to convert to RGBA. The input pixel block is empty.");let r,a,s,l;r=a=s=e[0],e.length>=3?(a=e[1],s=e[2]):e.length===2&&(a=e[1]);const c=this.width*this.height;if(r.length!==c)return Z.getLogger(this.declaredClass).error("getAsRGBAFloat()","Unable to convert to RGBA. The pixelblock is invalid.");let o=0;if($(n)&&n.length===c)for(l=0;l<c;l++)t[o++]=r[l],t[o++]=a[l],t[o++]=s[l],t[o++]=1&n[l];else for(l=0;l<c;l++)t[o++]=r[l],t[o++]=a[l],t[o++]=s[l],t[o++]=1}_calculateBandStatistics(t,e){let n=1/0,r=-1/0;const a=t.length;let s,l=0;if($(e))for(s=0;s<a;s++)e[s]&&(l=t[s],n=l<n?l:n,r=l>r?l:r);else for(s=0;s<a;s++)l=t[s],n=l<n?l:n,r=l>r?l:r;return new yt(n,r)}};O([z({json:{write:!0}})],G.prototype,"width",void 0),O([z({json:{write:!0}})],G.prototype,"height",void 0),O([z({json:{write:!0}})],G.prototype,"pixelType",void 0),O([Rt("pixelType")],G.prototype,"castPixelType",null),O([z({json:{write:!0}})],G.prototype,"validPixelCount",void 0),O([z({json:{write:!0}})],G.prototype,"mask",void 0),O([z({json:{write:!0}})],G.prototype,"maskIsAlpha",void 0),O([z({json:{write:!0}})],G.prototype,"pixels",void 0),O([z()],G.prototype,"premultiplyAlpha",void 0),O([z({json:{write:!0}})],G.prototype,"statistics",void 0),O([z({json:{write:!0}})],G.prototype,"depthCount",void 0),O([z({json:{write:!0}})],G.prototype,"noDataValues",void 0),O([z({json:{write:!0}})],G.prototype,"bandMasks",void 0),G=tt=O([$t("esri.layers.support.PixelBlock")],G);const V=G;var kt,Mt;(function(t){t[t.matchAny=0]="matchAny",t[t.matchAll=1]="matchAll"})(kt||(kt={})),function(t){t[t.bestMatch=0]="bestMatch",t[t.fail=1]="fail"}(Mt||(Mt={}));const ke=6;function L(t){return $(t)&&t.declaredClass==="esri.layers.support.PixelBlock"&&t.pixels&&t.pixels.length>0}function Me(t,e){if(!(e!=null&&e.length)||!L(t))return t;const n=t.pixels.length;return e&&e.some(r=>r>=n)||n===1&&e.length===1&&e[0]===0?t:n!==e.length||e.some((r,a)=>r!==a)?new V({pixelType:t.pixelType,width:t.width,height:t.height,mask:t.mask,validPixelCount:t.validPixelCount,maskIsAlpha:t.maskIsAlpha,pixels:e.map(r=>t.pixels[r]),statistics:t.statistics&&e.map(r=>t.statistics[r])}):t}function Ae(t){if(!(t!=null&&t.length)||t.some(i=>!L(i)))return null;if(t.length===1)return $(t[0])?t[0].clone():null;const e=t,{width:n,height:r,pixelType:a}=e[0];if(e.some(i=>i.width!==n||i.height!==r))return null;const s=e.map(({mask:i})=>i).filter(i=>i!=null);let l=null;s.length&&(l=new Uint8Array(n*r),l.set(s[0]),s.length>1&&St(s.slice(1),l));const c=[];e.forEach(({pixels:i})=>c.push(...i));const o=e.map(({statistics:i})=>i).filter(i=>i==null?void 0:i.length),h=[];return o.forEach(i=>h.push(...i)),new V({pixelType:a,width:n,height:r,mask:l,pixels:c,statistics:h.length?h:null})}function be(t){if(!t)return;const e=t.colormap;if(!e||e.length===0)return;const n=e.sort((u,p)=>u[0]-p[0]);let r=0;n[0][0]<0&&(r=n[0][0]);const a=Math.max(256,n[n.length-1][0]-r+1),s=new Uint8Array(4*a),l=[];let c,o=0,h=0;const i=n[0].length===5;if(a>65536)return n.forEach(u=>{l[u[0]-r]=i?u.slice(1):u.slice(1).concat([255])}),{indexed2DColormap:l,offset:r,alphaSpecified:i};if(t.fillUnspecified)for(c=n[h],o=c[0]-r;o<a;o++)s[4*o]=c[1],s[4*o+1]=c[2],s[4*o+2]=c[3],s[4*o+3]=i?c[4]:255,o===c[0]-r&&(c=h===n.length-1?c:n[++h]);else for(o=0;o<n.length;o++)c=n[o],h=4*(c[0]-r),s[h]=c[1],s[h+1]=c[2],s[h+2]=c[3],s[h+3]=i?c[4]:255;return{indexedColormap:s,offset:r,alphaSpecified:i}}function ve(t,e){if(!L(t)||!e||!e.indexedColormap&&!e.indexed2DColormap)return t;const n=t.clone(),r=n.pixels;let a=n.mask;const s=n.width*n.height;if(r.length!==1)return t;const{indexedColormap:l,indexed2DColormap:c,offset:o,alphaSpecified:h}=e;let i=0;const u=r[0],p=new Uint8Array(u.length),f=new Uint8Array(u.length),d=new Uint8Array(u.length);let m,g=0;if(l){const M=l.length-1;if($(a))for(i=0;i<s;i++)a[i]&&(g=4*(u[i]-o),g<o||g>M?a[i]=0:(p[i]=l[g],f[i]=l[g+1],d[i]=l[g+2],a[i]=l[g+3]));else{for(a=new Uint8Array(s),i=0;i<s;i++)g=4*(u[i]-o),g<o||g>M?a[i]=0:(p[i]=l[g],f[i]=l[g+1],d[i]=l[g+2],a[i]=l[g+3]);n.mask=a}}else if(c)if($(a))for(i=0;i<s;i++)a[i]&&(m=c[u[i]],p[i]=m[0],f[i]=m[1],d[i]=m[2],a[i]=m[3]);else{for(a=new Uint8Array(s),i=0;i<s;i++)m=c[u[i]],p[i]=m[0],f[i]=m[1],d[i]=m[2],a[i]=m[3];n.mask=a}return n.pixels=[p,f,d],n.statistics=null,n.pixelType="u8",n.maskIsAlpha=h,n}function Pe(t,e){if(!L(t))return null;const{pixels:n,mask:r}=t,a=n.length;let s=e.lut;const{offset:l}=e;s&&s[0].length===1&&(s=n.map(()=>s));const c=[],o=e.outputPixelType||"u8";for(let i=0;i<a;i++){const u=Bt(n[i],r,s[i],l||0,o);c.push(u)}const h=new V({width:t.width,height:t.height,pixels:c,mask:r,pixelType:o});return h.updateStatistics(),h}function Bt(t,e,n,r,a){const s=t.length,l=V.createEmptyBand(a,s);if(e)for(let c=0;c<s;c++)e[c]&&(l[c]=n[t[c]-r]);else for(let c=0;c<s;c++)l[c]=n[t[c]-r];return l}function Ue(t,e){if(!L(t))return null;const n=t.clone(),{pixels:r}=n,a=n.width*n.height,s=e.length,l=Math.floor(s/2),c=e[Math.floor(l)],o=r[0];let h,i,u,p,f,d,m=!1;const g=new Uint8Array(a),M=new Uint8Array(a),A=new Uint8Array(a);let y=n.mask;const w=e[0].mappedColor.length===4;for(y||(y=new Uint8Array(a),y.fill(w?255:1),n.mask=y),f=0;f<a;f++)if(y[f]){for(h=o[f],m=!1,d=l,i=c,u=0,p=s-1;p-u>1;){if(h===i.value){m=!0;break}h>i.value?u=d:p=d,d=Math.floor((u+p)/2),i=e[Math.floor(d)]}m||(h===e[u].value?(i=e[u],m=!0):h===e[p].value?(i=e[p],m=!0):h<e[u].value?(m=!1,i=null):h>e[u].value&&(h<e[p].value?(i=e[u],m=!0):p===s-1?(m=!1,i=null):(i=e[p],m=!0))),m?(g[f]=i.mappedColor[0],M[f]=i.mappedColor[1],A[f]=i.mappedColor[2],y[f]=i.mappedColor[3]):g[f]=M[f]=A[f]=y[f]=0}return n.pixels=[g,M,A],n.mask=y,n.pixelType="u8",n.maskIsAlpha=w,n}function Te(t,e){if(!L(t))return null;const{width:n,height:r}=t,{inputRanges:a,outputValues:s,outputPixelType:l,noDataRanges:c,allowUnmatched:o,isLastInputRangeInclusive:h}=e,i=t.pixels[0],u=V.createEmptyBand(l,i.length),p=t.mask,f=new Uint8Array(n*r);p?f.set(p):f.fill(255);const d=t.pixelType.startsWith("f")?1e-6:0,m=a.map(y=>y-d);m[0]=a[0],m[m.length-1]=a[a.length-1]+(h?1e-6:0);const g=a.length/2,[M,A]=gt(l);for(let y=0;y<r;y++)for(let w=0;w<n;w++){const x=y*n+w;if(f[x]){const k=i[x];let v=!1;for(let b=g-1;b>=0;b--)if(k===m[2*b]||k>m[2*b]&&k<m[2*b+1]){u[x]=s[b],v=!0;break}v||(o?u[x]=k>A?A:k<M?M:k:f[x]=0)}}if(c!=null&&c.length)for(let y=0;y<r;y++)for(let w=0;w<n;w++){const x=y*n+w;if(!p||p[x]){const k=i[x];for(let v=0;v<g;v+=2)if(k>=c[v]&&k<=c[v+1]){u[x]=0,f[x]=0;break}}}return new V({width:n,height:r,pixelType:l,pixels:[u],mask:f})}function At(t,e,n,r){const a=n!=null&&n.length>=2?new Set(n):null,s=(n==null?void 0:n.length)===1?n[0]:null,l=!!(e!=null&&e.length);for(let c=0;c<t.length;c++)if(r[c]){const o=t[c];if(l){let h=!1;for(let i=0;i<e.length;i+=2)if(o>=e[i]&&o<=e[i+1]){h=!0;break}h||(r[c]=0)}r[c]&&(o===s||a!=null&&a.has(o))&&(r[c]=0)}}function bt(t,e){const n=t[0].length;for(let r=0;r<n;r++)if(e[r]){let a=!1;for(let s=0;s<t.length;s++)if(t[s][r]){a=!0;break}a||(e[r]=0)}}function St(t,e){const n=t[0].length;for(let r=0;r<n;r++)if(e[r]){let a=!1;for(let s=0;s<t.length;s++)if(t[s][r]===0){a=!0;break}a&&(e[r]=0)}}function Ie(t,e){if(!L(t))return null;const{width:n,height:r,pixels:a}=t,s=n*r,l=new Uint8Array(s);t.mask?l.set(t.mask):l.fill(255);const c=a.length,{includedRanges:o,noDataValues:h,outputPixelType:i,matchAll:u,lookups:p}=e;if(p){const f=[];for(let d=0;d<c;d++){const m=p[d],g=Bt(a[d],l,m.lut,m.offset||0,"u8");f.push(g)}f.length===1?l.set(f[0]):u?bt(f,l):St(f,l)}else if(u){const f=[];for(let d=0;d<c;d++){const m=new Uint8Array(s);m.set(l),At(a[d],o==null?void 0:o.slice(2*d,2*d+2),h==null?void 0:h[d],m),f.push(m)}f.length===1?l.set(f[0]):bt(f,l)}else for(let f=0;f<c;f++)At(a[f],o==null?void 0:o.slice(2*f,2*f+2),h==null?void 0:h[f],l);return new V({width:n,height:r,pixelType:i,pixels:a,mask:l})}function Be(t){const{srcPixelType:e,inputRanges:n,outputValues:r,allowUnmatched:a,noDataRanges:s,isLastInputRangeInclusive:l,outputPixelType:c}=t;if(e!=="u8"&&e!=="s8"&&e!=="u16"&&e!=="s16")return null;const o=e.includes("16")?65536:256,h=e.includes("s")?-o/2:0,i=V.createEmptyBand(c,o),u=new Uint8Array(o);a&&u.fill(255);const[p,f]=gt(c);if(n!=null&&n.length&&(r!=null&&r.length)){const m=n.map(g=>g-1e-6);m[0]=n[0],l&&(m[m.length-1]=n[n.length-1]);for(let g=0;g<m.length;g++){const M=r[g]>f?f:r[g]<p?p:r[g],A=Math.ceil(m[2*g]-h),y=Math.floor(m[2*g+1]-h);for(let w=A;w<=y;w++)i[w]=M,u[w]=255}}if(s!=null&&s.length)for(let d=0;d<s.length;d++){const m=Math.ceil(s[2*d]-h),g=Math.floor(s[2*d+1]-h);for(let M=m;M<=g;M++)u[M]=0}return{lut:i,offset:h,mask:u}}function Se(t,e,n){if(t!=="u8"&&t!=="s8"&&t!=="u16"&&t!=="s16")return null;const r=t.includes("16")?65536:256,a=t.includes("s")?-r/2:0,s=new Uint8Array(r);if(e)for(let l=0;l<e.length;l++){const c=Math.ceil(e[2*l]-a),o=Math.floor(e[2*l+1]-a);for(let h=c;h<=o;h++)s[h]=255}else s.fill(255);if(n)for(let l=0;l<n.length;l++)s[n[l]-a]=0;return{lut:s,offset:a}}function zt(t,e,n,r,a,s,l,c){return{xmin:a<=n*t?0:a<n*t+t?a-n*t:t,ymin:s<=r*e?0:s<r*e+e?s-r*e:e,xmax:a+l<=n*t?0:a+l<n*t+t?a+l-n*t:t,ymax:s+c<=r*e?0:s+c<r*e+e?s+c-r*e:e}}function Ce(t,e){if(!t||t.length===0)return null;const n=t.find(d=>d.pixelBlock);if(!n||H(n.pixelBlock))return null;const r=(n.extent.xmax-n.extent.xmin)/n.pixelBlock.width,a=(n.extent.ymax-n.extent.ymin)/n.pixelBlock.height,s=.01*Math.min(r,a),l=t.sort((d,m)=>Math.abs(d.extent.ymax-m.extent.ymax)>s?m.extent.ymax-d.extent.ymax:Math.abs(d.extent.xmin-m.extent.xmin)>s?d.extent.xmin-m.extent.xmin:0),c=Math.min.apply(null,l.map(d=>d.extent.xmin)),o=Math.min.apply(null,l.map(d=>d.extent.ymin)),h=Math.max.apply(null,l.map(d=>d.extent.xmax)),i=Math.max.apply(null,l.map(d=>d.extent.ymax)),u={x:Math.round((e.xmin-c)/r),y:Math.round((i-e.ymax)/a)},p={width:Math.round((h-c)/r),height:Math.round((i-o)/a)},f={width:Math.round((e.xmax-e.xmin)/r),height:Math.round((e.ymax-e.ymin)/a)};return Math.round(p.width/n.pixelBlock.width)*Math.round(p.height/n.pixelBlock.height)!==l.length||u.x<0||u.y<0||p.width<f.width||p.height<f.height?null:{extent:e,pixelBlock:Jt(l.map(d=>d.pixelBlock),p,{clipOffset:u,clipSize:f})}}function pt(t,e,n,r,a,s){const{width:l,height:c}=n.block,{x:o,y:h}=n.offset,{width:i,height:u}=n.mosaic,p=zt(l,c,r,a,o,h,i,u);let f=0,d=0;if(s){const m=s.hasGCSSShiftTransform?360:s.halfWorldWidth??0,g=l*s.resolutionX,M=s.startX+r*g;M<m&&M+g>m?d=s.rightPadding:M>=m&&(f=s.leftMargin-s.rightPadding,d=0)}if(p.xmax-=d,typeof e!="number")for(let m=p.ymin;m<p.ymax;m++){const g=(a*c+m-h)*i+(r*l-o)+f,M=m*l;for(let A=p.xmin;A<p.xmax;A++)t[g+A]=e[M+A]}else for(let m=p.ymin;m<p.ymax;m++){const g=(a*c+m-h)*i+(r*l-o)+f;for(let M=p.xmin;M<p.xmax;M++)t[g+M]=e}}function Jt(t,e,n={}){const{clipOffset:r,clipSize:a,alignmentInfo:s,blockWidths:l}=n;if(l)return Xt(t,e,{blockWidths:l});const c=t.find(v=>L(v));if(H(c))return null;const o=a?a.width:e.width,h=a?a.height:e.height,i=c.width,u=c.height,p=e.width/i,f=e.height/u,d={offset:r||{x:0,y:0},mosaic:a||e,block:{width:i,height:u}},m=c.pixelType,g=V.getPixelArrayConstructor(m),M=c.pixels.length,A=[];let y,w;for(let v=0;v<M;v++){w=new g(o*h);for(let b=0;b<f;b++)for(let P=0;P<p;P++){const U=t[b*p+P];L(U)&&(y=U.pixels[v],pt(w,y,d,P,b,s))}A.push(w)}let x;if(t.some(v=>H(v)||$(v.mask)&&v.mask.length>0)){x=new Uint8Array(o*h);for(let v=0;v<f;v++)for(let b=0;b<p;b++){const P=t[v*p+b],U=$(P)?P.mask:null;$(U)?pt(x,U,d,b,v,s):pt(x,P?1:0,d,b,v,s)}}const k=new V({width:o,height:h,pixels:A,pixelType:m,mask:x});return k.updateStatistics(),k}function Xt(t,e,n){const r=t.find(f=>$(f));if(H(r))return null;const a=t.some(f=>!$(f)||!!f.mask),{width:s,height:l}=e,c=a?new Uint8Array(s*l):null,{blockWidths:o}=n,h=[],i=r.getPlaneCount(),u=V.getPixelArrayConstructor(r.pixelType);if(a)for(let f=0,d=0;f<t.length;d+=o[f],f++){const m=t[f];if(!L(m))continue;const g=ft(m.mask);for(let M=0;M<l;M++)for(let A=0;A<o[f];A++)c[M*s+A+d]=g==null?255:g[M*m.width+A]}for(let f=0;f<i;f++){const d=new u(s*l);for(let m=0,g=0;m<t.length;g+=o[m],m++){const M=t[m];if(!L(M))continue;const A=M.pixels[f];if(A!=null)for(let y=0;y<l;y++)for(let w=0;w<o[m];w++)d[y*s+w+g]=A[y*M.width+w]}h.push(d)}const p=new V({width:s,height:l,mask:c,pixels:h,pixelType:r.pixelType});return p.updateStatistics(),p}function Fe(t,e,n){if(!L(t))return null;const{width:r,height:a}=t,s=e.x,l=e.y,c=n.width+s,o=n.height+l;if(s<0||l<0||c>r||o>a||s===0&&l===0&&c===r&&o===a)return t;t.mask||(t.mask=new Uint8Array(r*a));const h=t.mask;for(let i=0;i<a;i++){const u=i*r;for(let p=0;p<r;p++)h[u+p]=i<l||i>=o||p<s||p>=c?0:1}return t.updateStatistics(),t}function Ht(t){if(!L(t))return null;const e=t.clone(),{width:n,height:r,pixels:a}=t,s=a[0],l=e.pixels[0],c=ft(t.mask);for(let o=2;o<r-1;o++){const h=new Map;for(let u=o-2;u<o+2;u++)for(let p=0;p<4;p++){const f=u*n+p;rt(h,s[f],c?c[f]:1)}l[o*n]=vt(h),l[o*n+1]=l[o*n+2]=l[o*n];let i=3;for(;i<n-1;i++){let u=(o-2)*n+i+1;rt(h,s[u],c?c[u]:1),u=(o-1)*n+i+1,rt(h,s[u],c?c[u]:1),u=o*n+i+1,rt(h,s[u],c?c[u]:1),u=(o+1)*n+i+1,rt(h,s[u],c?c[u]:1),u=(o-2)*n+i-3,at(h,s[u],c?c[u]:1),u=(o-1)*n+i-3,at(h,s[u],c?c[u]:1),u=o*n+i-3,at(h,s[u],c?c[u]:1),u=(o+1)*n+i-3,at(h,s[u],c?c[u]:1),l[o*n+i]=vt(h)}l[o*n+i+1]=l[o*n+i]}for(let o=0;o<n;o++)l[o]=l[n+o]=l[2*n+o],l[(r-1)*n+o]=l[(r-2)*n+o];return e.updateStatistics(),e}function vt(t){if(t.size===0)return 0;let e=0,n=-1,r=0;const a=t.keys();let s=a.next();for(;!s.done;)r=t.get(s.value),r>e&&(n=s.value,e=r),s=a.next();return n}function at(t,e,n){if(n===0)return;const r=t.get(e);r===1?t.delete(e):t.set(e,r-1)}function rt(t,e,n){n!==0&&t.set(e,t.has(e)?t.get(e)+1:1)}function Kt(t,e,n){let{x:r,y:a}=e;const{width:s,height:l}=n;if(r===0&&a===0&&l===t.height&&s===t.width)return t;const{width:c,height:o}=t,h=Math.max(0,a),i=Math.max(0,r),u=Math.min(r+s,c),p=Math.min(a+l,o);if(u<0||p<0||!L(t))return null;r=Math.max(0,-r),a=Math.max(0,-a);const{pixels:f}=t,d=s*l,m=f.length,g=[];for(let w=0;w<m;w++){const x=f[w],k=V.createEmptyBand(t.pixelType,d);for(let v=h;v<p;v++){const b=v*c;let P=(v+a-h)*s+r;for(let U=i;U<u;U++)k[P++]=x[b+U]}g.push(k)}const M=new Uint8Array(d),A=ft(t.mask);for(let w=h;w<p;w++){const x=w*c;let k=(w+a-h)*s+r;for(let v=i;v<u;v++)M[k++]=A?A[x+v]:1}const y=new V({width:n.width,height:n.height,pixelType:t.pixelType,pixels:g,mask:M});return y.updateStatistics(),y}function Qt(t,e=!0){if(!L(t))return null;const{pixels:n,width:r,height:a,mask:s,pixelType:l}=t,c=[],o=Math.round(r/2),h=Math.round(a/2),i=a-1,u=r-1;for(let f=0;f<n.length;f++){const d=n[f],m=V.createEmptyBand(l,o*h);let g=0;for(let M=0;M<a;M+=2)for(let A=0;A<r;A+=2){const y=d[M*r+A];if(e){const w=A===u?y:d[M*r+A+1],x=M===i?y:d[M*r+A+r],k=A===u?x:M===i?w:d[M*r+A+r+1];m[g++]=(y+w+x+k)/4}else m[g++]=y}c.push(m)}let p=null;if($(s)){p=new Uint8Array(o*h);let f=0;for(let d=0;d<a;d+=2)for(let m=0;m<r;m+=2){const g=s[d*r+m];if(e){const M=m===u?g:s[d*r+m+1],A=d===i?g:s[d*r+m+r],y=m===u?A:d===i?M:s[d*r+m+r+1];p[f++]=g*M*A*y?1:0}else p[f++]=g}}return new V({width:o,height:h,pixelType:l,pixels:c,mask:p})}function _e(t,e,n){if(!L(t))return null;const{width:r,height:a}=e;let{width:s,height:l}=t;const c=new Map,o={x:0,y:0},h=n==null?1:1+n;let i=t;for(let u=0;u<h;u++){const p=Math.ceil(s/r),f=Math.ceil(l/a);for(let d=0;d<f;d++){o.y=d*a;for(let m=0;m<p;m++){o.x=m*r;const g=Kt(i,o,e);c.set(`${u}/${d}/${m}`,g)}}u<h-1&&(i=Qt(i)),s=Math.round(s/2),l=Math.round(l/2)}return c}function Ct(t,e,n,r,a=0){const{width:s,height:l}=t,{width:c,height:o}=e,h=r.cols,i=r.rows,u=Math.ceil(c/h-.1/h),p=Math.ceil(o/i-.1/i);let f,d,m,g,M,A,y;const w=u*h,x=w*p*i,k=new Float32Array(x),v=new Float32Array(x),b=new Uint32Array(x),P=new Uint32Array(x);let U,S,B=0;for(let C=0;C<p;C++)for(let D=0;D<u;D++){f=12*(C*u+D),d=n[f],m=n[f+1],g=n[f+2],M=n[f+3],A=n[f+4],y=n[f+5];for(let I=0;I<i;I++){B=(C*i+I)*w+D*h,S=(I+.5)/i;for(let T=0;T<I;T++)U=(T+.5)/h,k[B+T]=(d*U+m*S+g)*s+a,v[B+T]=(M*U+A*S+y)*l+a,b[B+T]=Math.floor(k[B+T]),P[B+T]=Math.floor(v[B+T])}f+=6,d=n[f],m=n[f+1],g=n[f+2],M=n[f+3],A=n[f+4],y=n[f+5];for(let I=0;I<i;I++){B=(C*i+I)*w+D*h,S=(I+.5)/i;for(let T=I;T<h;T++)U=(T+.5)/h,k[B+T]=(d*U+m*S+g)*s+a,v[B+T]=(M*U+A*S+y)*l+a,b[B+T]=Math.floor(k[B+T]),P[B+T]=Math.floor(v[B+T])}}return{offsets_x:k,offsets_y:v,offsets_xi:b,offsets_yi:P,gridWidth:w}}function De(t,e){const{coefficients:n,spacing:r}=e,{offsets_x:a,offsets_y:s,gridWidth:l}=Ct(t,t,n,{rows:r[0],cols:r[1]}),{width:c,height:o}=t,h=new Float32Array(c*o),i=180/Math.PI;for(let u=0;u<o;u++)for(let p=0;p<c;p++){const f=u*l+p,d=u===0?f:f-l,m=u===o-1?f:f+l,g=a[d]-a[m],M=s[m]-s[d];if(isNaN(g)||isNaN(M))h[u*c+p]=90;else{let A=Math.atan2(M,g)*i;A=(360+A)%360,h[u*c+p]=A}}return h}function Ve(t,e,n,r,a="nearest"){if(!L(t))return null;a==="majority"&&(t=Ht(t));const{pixels:s,mask:l,pixelType:c}=t,o=t.width,h=t.height,i=V.getPixelArrayConstructor(c),u=s.length,{width:p,height:f}=e;let d=!1;for(let P=0;P<n.length;P+=3)n[P]===-1&&n[P+1]===-1&&n[P+2]===-1&&(d=!0);const{offsets_x:m,offsets_y:g,offsets_xi:M,offsets_yi:A,gridWidth:y}=Ct({width:o,height:h},e,n,r,a==="majority"?.5:0);let w;const x=(P,U,S)=>{const B=P instanceof Float32Array||P instanceof Float64Array?0:.5;for(let C=0;C<f;C++){w=C*y;for(let D=0;D<p;D++){if(m[w]<0||g[w]<0)P[C*p+D]=0;else if(S)P[C*p+D]=U[M[w]+A[w]*o];else{const I=Math.floor(m[w]),T=Math.floor(g[w]),_=Math.ceil(m[w]),F=Math.ceil(g[w]),N=m[w]-I,J=g[w]-T;if(!l||l[I+T*o]&&l[I+T*o]&&l[I+F*o]&&l[_+F*o]){const q=(1-N)*U[I+T*o]+N*U[_+T*o],W=(1-N)*U[I+F*o]+N*U[_+F*o];P[C*p+D]=(1-J)*q+J*W+B}else P[C*p+D]=U[M[w]+A[w]*o]}w++}}},k=[];let v;for(let P=0;P<u;P++)v=new i(p*f),x(v,s[P],a==="nearest"||a==="majority"),k.push(v);const b=new V({width:p,height:f,pixelType:c,pixels:k});if($(l))b.mask=new Uint8Array(p*f),x(b.mask,l,!0);else if(d){b.mask=new Uint8Array(p*f);for(let P=0;P<p*f;P++)b.mask[P]=m[P]<0||g[P]<0?0:1}return b.updateStatistics(),b}const et=new Map;et.set("meter-per-second",1),et.set("kilometer-per-hour",.277778),et.set("knots",.514444),et.set("feet-per-second",.3048),et.set("mile-per-hour",.44704);const dt=180/Math.PI,xt=5,ct=new Et({esriMetersPerSecond:"meter-per-second",esriKilometersPerHour:"kilometer-per-hour",esriKnots:"knots",esriFeetPerSecond:"feet-per-second",esriMilesPerHour:"mile-per-hour"});function wt(t,e){return et.get(t)/et.get(e)||1}function Ft(t){return(450-t)%360}function _t(t,e="geographic"){const[n,r]=t,a=Math.sqrt(n*n+r*r);let s=Math.atan2(r,n)*dt;return s=(360+s)%360,e==="geographic"&&(s=Ft(s)),[a,s]}function Yt(t,e="geographic"){let n=t[1];e==="geographic"&&(n=Ft(n)),n%=360;const r=t[0];return[r*Math.cos(n/dt),r*Math.sin(n/dt)]}function Re(t,e,n,r="geographic"){if(!L(t)||H(n))return t;const a=e==="vector-magdir"?t.clone():ft(Pt(t,e)),s=a.pixels[1];for(let l=0;l<s.length;l++)s[l]=r==="geographic"?(s[l]+n[l]+270)%360:(s[l]+360-n[l])%360;return e==="vector-magdir"?a:Pt(a,"vector-magdir")}function Pt(t,e,n="geographic",r=1){if(!L(t))return t;const{pixels:a,width:s,height:l}=t,c=s*l,o=a[0],h=a[1],i=t.pixelType.startsWith("f")?t.pixelType:"f32",u=V.createEmptyBand(i,c),p=V.createEmptyBand(i,c);let f=0;for(let m=0;m<l;m++)for(let g=0;g<s;g++)e==="vector-uv"?([u[f],p[f]]=_t([o[f],h[f]],n),u[f]*=r):([u[f],p[f]]=Yt([o[f],h[f]],n),u[f]*=r,p[f]*=r),f++;const d=new V({pixelType:i,width:t.width,height:t.height,mask:t.mask,validPixelCount:t.validPixelCount,maskIsAlpha:t.maskIsAlpha,pixels:[u,p]});return d.updateStatistics(),d}function $e(t,e,n=1){if(n===1||!L(t))return t;const r=t.clone(),{pixels:a,width:s,height:l}=r,c=a[0];a[1];let o=0;for(let h=0;h<l;h++)for(let i=0;i<s;i++)c[o]*=n,o++;return r.updateStatistics(),r}function Le(t,e,n,r,a){if(H(a)||!a.spatialReference.equals(t.spatialReference))return{extent:t,width:Math.round(e/r),height:Math.round(n/r),resolution:t.width/e};const s=a.xmin,l=a.ymax,c=(t.xmax-t.xmin)/e*r,o=(t.ymax-t.ymin)/n*r,h=(c+o)/2;return t.xmin=s+Math.floor((t.xmin-s)/c)*c,t.xmax=s+Math.ceil((t.xmax-s)/c)*c,t.ymin=l+Math.floor((t.ymin-l)/o)*o,t.ymax=l+Math.ceil((t.ymax-l)/o)*o,{extent:t,width:Math.round(t.width/c),height:Math.round(t.height/o),resolution:h}}const Zt=Dt(0,0,0);function Dt(t=0,e=0,n=Math.PI,r=!0){r&&(n=(2*Math.PI-n)%(2*Math.PI));const a=r?-1:1,s=13*a,l=-7*a,c=-2*a,o=-16*a,h=21.75,[i,u]=E(0,e+s,n,h),[p,f]=E(t-5.5,e+l,n,h),[d,m]=E(t+5.5,e+l,n,h),[g,M]=E(t-1.5,e+c,n,h),[A,y]=E(t+1.5,e+c,n,h),[w,x]=E(t-1.5,e+o,n,h),[k,v]=E(t+1.5,e+o,n,h);return[i,u,p,f,g,M,A,y,d,m,w,x,k,v]}function te(t=0,e=Math.PI,n=!0){n&&(e=(2*Math.PI-e)%(2*Math.PI));const r=10,a=n?-1:1,s=5*a,l=20*a,c=25*a,o=45,h=0,i=0,u=2,p=0,f=u*a,d=n?1:-1,m=r/2*d;let[g,M]=[h+m,i-l],[A,y]=[g+u*d,M],[w,x]=[A-p*d,y+f],[k,v]=[h-m,i-c],[b,P]=[k+p*d,v-f],U=Math.ceil(t/xt),S=Math.floor(U/10);U-=8*S;const B=[],C=[];for(let K=0;K<U/2;K++,S--){S<=0&&U%2==1&&K===(U-1)/2&&(k=h,b=k+p*d,v=(v+M)/2,P=v-f);const[Q,ot]=E(k,v,e,o);if(S>0){const[it,st]=E(A,v,e,o),[lt,R]=E(g,M,e,o);B.push(it),B.push(st),B.push(Q),B.push(ot),B.push(lt),B.push(R)}else{const[it,st]=E(A,y,e,o),[lt,R]=E(w,x,e,o),[j,ut]=E(b,P,e,o);C.push(Q),C.push(ot),C.push(j),C.push(ut),C.push(lt),C.push(R),C.push(it),C.push(st)}v+=s,M+=s,y+=s,x+=s,P+=s}const[D,I]=E(h+m,i+l,e,o),T=(r/2+u)*d,[_,F]=E(h+T,i+l,e,o),[N,J]=E(h+m,i-c,e,o),[q,W]=E(h+T,i-c,e,o);return{pennants:B,barbs:C,shaft:[D,I,_,F,N,J,q,W]}}function E(t,e,n,r=1){const a=Math.sqrt(t*t+e*e)/r,s=(2*Math.PI+Math.atan2(e,t))%(2*Math.PI);return[a,(2*Math.PI+s-n)%(2*Math.PI)]}const ht=[0,1,3,6,10,16,21,27,33,40,47,55,63],ee=[0,.5,1,1.5,2],ne=[0,.25,.5,1,1.5,2,2.5,3,3.5,4];function Y(t,e,n,r){const a=wt(r||"knots",n);let s;for(s=1;s<e.length;s++)if(s===e.length-1){if(t<e[s]*a)break}else if(t<=e[s]*a)break;return Math.min(s-1,e.length-2)}function ie(t,e,n,r,a){let s=0;switch(e){case"beaufort_kn":s=Y(t,ht,"knots",n);break;case"beaufort_km":s=Y(t,ht,"kilometer-per-hour",n);break;case"beaufort_ft":s=Y(t,ht,"feet-per-second",n);break;case"beaufort_m":s=Y(t,ht,"meter-per-second",n);break;case"classified_arrow":s=Y(t,a??[],r,n);break;case"ocean_current_m":s=Y(t,ee,"meter-per-second",n);break;case"ocean_current_kn":s=Y(t,ne,"knots",n)}return s}function se(t,e){const{style:n,inputUnit:r,outputUnit:a,breakValues:s}=e,l=ct.fromJSON(r),c=ct.fromJSON(a),o=7*6,h=15;let i=0,u=0;const{width:p,height:f,mask:d}=t,m=t.pixels[0],g=t.pixels[1],M=$(d)?d.filter(x=>x>0).length:p*f,A=new Float32Array(M*o),y=new Uint32Array(h*M),w=e.invertDirection?Dt(0,0,0,!1):Zt;for(let x=0;x<f;x++)for(let k=0;k<p;k++){const v=x*p+k;if(!d||d[x*p+k]){const b=(g[v]+360)%360/180*Math.PI,P=ie(m[v],n,l,c,s);for(let S=0;S<w.length;S+=2)A[i++]=(k+.5)/p,A[i++]=(x+.5)/f,A[i++]=w[S],A[i++]=w[S+1]+b,A[i++]=P,A[i++]=m[v];const U=7*(i/o-1);y[u++]=U,y[u++]=U+1,y[u++]=U+2,y[u++]=U+0,y[u++]=U+4,y[u++]=U+3,y[u++]=U+0,y[u++]=U+2,y[u++]=U+3,y[u++]=U+2,y[u++]=U+5,y[u++]=U+3,y[u++]=U+5,y[u++]=U+6,y[u++]=U+3}}return{vertexData:A,indexData:y}}const mt=[];function le(t,e){if(mt.length===0)for(let f=0;f<30;f++)mt.push(te(5*f,0,!e.invertDirection));const n=wt(ct.fromJSON(e.inputUnit),"knots"),{width:r,height:a,mask:s}=t,l=t.pixels[0],c=t.pixels[1],o=6,h=[],i=[];let u=0,p=0;for(let f=0;f<a;f++)for(let d=0;d<r;d++){const m=f*r+d,g=l[m]*n;if((!s||s[f*r+d])&&g>=xt){const M=(c[m]+360)%360/180*Math.PI,{pennants:A,barbs:y,shaft:w}=mt[Math.min(Math.floor(g/5),29)];if(A.length+y.length===0)continue;let x=h.length/o;const k=(d+.5)/r,v=(f+.5)/a;for(let b=0;b<A.length;b+=2)h[u++]=k,h[u++]=v,h[u++]=A[b],h[u++]=A[b+1]+M,h[u++]=0,h[u++]=g;for(let b=0;b<y.length;b+=2)h[u++]=k,h[u++]=v,h[u++]=y[b],h[u++]=y[b+1]+M,h[u++]=0,h[u++]=g;for(let b=0;b<w.length;b+=2)h[u++]=k,h[u++]=v,h[u++]=w[b],h[u++]=w[b+1]+M,h[u++]=0,h[u++]=g;for(let b=0;b<A.length/6;b++)i[p++]=x,i[p++]=x+1,i[p++]=x+2,x+=3;for(let b=0;b<y.length/8;b++)i[p++]=x,i[p++]=x+1,i[p++]=x+2,i[p++]=x+1,i[p++]=x+2,i[p++]=x+3,x+=4;i[p++]=x+0,i[p++]=x+1,i[p++]=x+2,i[p++]=x+1,i[p++]=x+3,i[p++]=x+2,x+=4}}return{vertexData:new Float32Array(h),indexData:new Uint32Array(i)}}function re(t,e){let r=0,a=0;const{width:s,height:l,mask:c}=t,o=t.pixels[0],h=[],i=[],u=wt(ct.fromJSON(e.inputUnit),"knots"),p=e.style==="wind_speed"?xt:Number.MAX_VALUE;for(let f=0;f<l;f++)for(let d=0;d<s;d++){const m=o[f*s+d]*u;if((!c||c[f*s+d])&&m<p){for(let M=0;M<4;M++)h[r++]=(d+.5)/s,h[r++]=(f+.5)/l,h[r++]=M<2?-.5:.5,h[r++]=M%2==0?-.5:.5,h[r++]=0,h[r++]=m;const g=4*(r/24-1);i[a++]=g,i[a++]=g+1,i[a++]=g+2,i[a++]=g+1,i[a++]=g+2,i[a++]=g+3}}return{vertexData:new Float32Array(h),indexData:new Uint32Array(i)}}function je(t,e){return e.style==="simple_scalar"?re(t,e):e.style==="wind_speed"?le(t,e):se(t,e)}function Ee(t,e,n,r=[0,0],a=.5){const{width:s,height:l,mask:c}=t,[o,h]=t.pixels,[i,u]=r,p=Math.round((s-i)/n),f=Math.round((l-u)/n),d=p*f,m=new Float32Array(d),g=new Float32Array(d),M=new Uint8Array(d);for(let y=0;y<f;y++)for(let w=0;w<p;w++){let x=0;const k=y*p+w,v=Math.max(0,y*n+u),b=Math.max(0,w*n+i),P=Math.min(l,v+n),U=Math.min(s,b+n);for(let S=v;S<P;S++)for(let B=b;B<U;B++){const C=S*s+B;if(!c||c[C]){x++;const D=[o[C],h[C]],[I,T]=D;m[k]+=I,g[k]+=T}}if(x>=(P-v)*(U-b)*(1-a)){M[k]=1;const[S,B]=_t([m[k]/x,g[k]/x]);m[k]=S,g[k]=B}else M[k]=0,m[k]=0,g[k]=0}const A=new V({width:p,height:f,pixels:[m,g],mask:M});return A.updateStatistics(),A}const X=Z.getLogger("esri.views.2d.engine.flow.dataUtils"),oe=10;async function Ne(t,e,n,r){const a=performance.now(),s=ae(e,n),l=performance.now(),c=ce(e,s,n.width,n.height),o=performance.now(),h=ue(c),i=performance.now(),u=t==="Streamlines"?pe(h,oe):me(h),p=performance.now();return nt("esri-2d-profiler")&&(X.info("I.1","_createFlowFieldFromData (ms)",Math.round(l-a)),X.info("I.2","_getStreamlines (ms)",Math.round(o-l)),X.info("I.3","createAnimatedLinesData (ms)",Math.round(i-o)),X.info("I.4","create{Streamlines|Particles}Mesh (ms)",Math.round(p-i)),X.info("I.5","createFlowMesh (ms)",Math.round(p-a)),X.info("I.6","Mesh size (bytes)",u.vertexData.buffer.byteLength+u.indexData.buffer.byteLength)),await Promise.resolve(),Nt(r),u}function ae(t,e){const n=fe(e.data,e.width,e.height,t.smoothing);return t.interpolate?(r,a)=>{const s=Math.floor(r),l=Math.floor(a);if(s<0||s>=e.width)return[0,0];if(l<0||l>=e.height)return[0,0];const c=r-s,o=a-l,h=s,i=l,u=s<e.width-1?s+1:s,p=l<e.height-1?l+1:l,f=n[2*(i*e.width+h)],d=n[2*(i*e.width+u)],m=n[2*(p*e.width+h)],g=n[2*(p*e.width+u)],M=n[2*(i*e.width+h)+1],A=n[2*(i*e.width+u)+1];return[(f*(1-o)+m*o)*(1-c)+(d*(1-o)+g*o)*c,(M*(1-o)+n[2*(p*e.width+h)+1]*o)*(1-c)+(A*(1-o)+n[2*(p*e.width+u)+1]*o)*c]}:(r,a)=>{const s=Math.round(r),l=Math.round(a);return s<0||s>=e.width||l<0||l>=e.height?[0,0]:[n[2*(l*e.width+s)+0],n[2*(l*e.width+s)+1]]}}function he(t,e,n,r,a,s,l,c,o){const h=[];let i=n,u=r,p=0,[f,d]=e(i,u);f*=t.velocityScale,d*=t.velocityScale;const m=Math.sqrt(f*f+d*d);let g,M;h.push({x:i,y:u,t:p,speed:m});for(let A=0;A<t.verticesPerLine;A++){let[y,w]=e(i,u);y*=t.velocityScale,w*=t.velocityScale;const x=Math.sqrt(y*y+w*w);if(x<t.minSpeedThreshold)return h;const k=y/x,v=w/x;if(i+=k*t.segmentLength,u+=v*t.segmentLength,p+=t.segmentLength/x,Math.acos(k*g+v*M)>t.maxTurnAngle)return h;if(t.collisions){const b=Math.round(i*o),P=Math.round(u*o);if(b<0||b>l-1||P<0||P>c-1)return h;const U=s[P*l+b];if(U!==-1&&U!==a)return h;s[P*l+b]=a}h.push({x:i,y:u,t:p,speed:x}),g=k,M=v}return h}function ce(t,e,n,r){const a=[],s=new It,l=1/Math.max(t.lineCollisionWidth,1),c=Math.round(n*l),o=Math.round(r*l),h=new Int32Array(c*o);for(let u=0;u<h.length;u++)h[u]=-1;const i=[];for(let u=0;u<r;u+=t.lineSpacing)for(let p=0;p<n;p+=t.lineSpacing)i.push({x:p,y:u,sort:s.getFloat()});i.sort((u,p)=>u.sort-p.sort);for(const{x:u,y:p}of i)if(s.getFloat()<t.density){const f=he(t,e,u,p,a.length,h,c,o,l);if(f.length<2)continue;a.push(f)}return a}function fe(t,e,n,r){if(r===0)return t;const a=Math.round(3*r),s=new Array(2*a+1);let l=0;for(let h=-a;h<=a;h++){const i=Math.exp(-h*h/(r*r));s[h+a]=i,l+=i}for(let h=-a;h<=a;h++)s[h+a]/=l;const c=new Float32Array(t.length);for(let h=0;h<n;h++)for(let i=0;i<e;i++){let u=0,p=0;for(let f=-a;f<=a;f++){if(i+f<0||i+f>=e)continue;const d=s[f+a];u+=d*t[2*(h*e+(i+f))+0],p+=d*t[2*(h*e+(i+f))+1]}c[2*(h*e+i)+0]=u,c[2*(h*e+i)+1]=p}const o=new Float32Array(t.length);for(let h=0;h<e;h++)for(let i=0;i<n;i++){let u=0,p=0;for(let f=-a;f<=a;f++){if(i+f<0||i+f>=n)continue;const d=s[f+a];u+=d*c[2*((i+f)*e+h)+0],p+=d*c[2*((i+f)*e+h)+1]}o[2*(i*e+h)+0]=u,o[2*(i*e+h)+1]=p}return o}function ue(t,e){const n=new It,r=t.reduce((o,h)=>o+h.length,0),a=new Float32Array(4*r),s=new Array(t.length);let l=0,c=0;for(const o of t){const h=l;for(const i of o)a[4*l+0]=i.x,a[4*l+1]=i.y,a[4*l+2]=i.t,a[4*l+3]=i.speed,l++;s[c++]={startVertex:h,numberOfVertices:o.length,totalTime:o[o.length-1].t,timeSeed:n.getFloat()}}return{lineVertices:a,lineDescriptors:s}}function pe(t,e){const{lineVertices:r,lineDescriptors:a}=t;let s=0,l=0;for(const f of a)s+=2*f.numberOfVertices,l+=6*(f.numberOfVertices-1);const c=new Float32Array(s*9),o=new Uint32Array(l);let h=0,i=0;function u(){o[i++]=h-2,o[i++]=h,o[i++]=h-1,o[i++]=h,o[i++]=h+1,o[i++]=h-1}function p(f,d,m,g,M,A,y,w){const x=h*9;let k=0;c[x+k++]=f,c[x+k++]=d,c[x+k++]=1,c[x+k++]=m,c[x+k++]=A,c[x+k++]=y,c[x+k++]=g/2,c[x+k++]=M/2,c[x+k++]=w,h++,c[x+k++]=f,c[x+k++]=d,c[x+k++]=-1,c[x+k++]=m,c[x+k++]=A,c[x+k++]=y,c[x+k++]=-g/2,c[x+k++]=-M/2,c[x+k++]=w,h++}for(const f of a){const{totalTime:d,timeSeed:m}=f;let g=null,M=null,A=null,y=null,w=null,x=null;for(let k=0;k<f.numberOfVertices;k++){const v=r[4*(f.startVertex+k)+0],b=r[4*(f.startVertex+k)+1],P=r[4*(f.startVertex+k)+2],U=r[4*(f.startVertex+k)+3];let S=null,B=null,C=null,D=null;if(k>0){S=v-g,B=b-M;const I=Math.sqrt(S*S+B*B);if(S/=I,B/=I,k>1){let T=S+w,_=B+x;const F=Math.sqrt(T*T+_*_);T/=F,_/=F;const N=Math.min(1/(T*S+_*B),e);T*=N,_*=N,C=-_,D=T}else C=-B,D=S;C!==null&&D!==null&&(p(g,M,A,C,D,d,m,U),u())}g=v,M=b,A=P,w=S,x=B,y=U}p(g,M,A,-x,w,d,m,y)}return{vertexData:c,indexData:o}}function me(t){const{lineVertices:a,lineDescriptors:s}=t;let l=0,c=0;for(const I of s){const T=I.numberOfVertices-1;l+=4*T*2,c+=6*T*2}const o=new Float32Array(l*16),h=new Uint32Array(c);let i,u,p,f,d,m,g,M,A,y,w,x,k,v,b=0,P=0;function U(){h[P++]=b-8,h[P++]=b-7,h[P++]=b-6,h[P++]=b-7,h[P++]=b-5,h[P++]=b-6,h[P++]=b-4,h[P++]=b-3,h[P++]=b-2,h[P++]=b-3,h[P++]=b-1,h[P++]=b-2}function S(I,T,_,F,N,J,q,W,K,Q,ot,it,st,lt){const R=b*16;let j=0;for(const ut of[1,2])for(const Vt of[1,2,3,4])o[R+j++]=I,o[R+j++]=T,o[R+j++]=_,o[R+j++]=F,o[R+j++]=q,o[R+j++]=W,o[R+j++]=K,o[R+j++]=Q,o[R+j++]=ut,o[R+j++]=Vt,o[R+j++]=st,o[R+j++]=lt,o[R+j++]=N/2,o[R+j++]=J/2,o[R+j++]=ot/2,o[R+j++]=it/2,b++}function B(I,T){let _=A+w,F=y+x;const N=Math.sqrt(_*_+F*F);_/=N,F/=N;const J=A*_+y*F;_/=J,F/=J;let q=w+k,W=x+v;const K=Math.sqrt(q*q+W*W);q/=K,W/=K;const Q=w*q+x*W;q/=Q,W/=Q,S(i,u,p,f,-F,_,d,m,g,M,-W,q,I,T),U()}function C(I,T,_,F,N,J){if(A=w,y=x,w=k,x=v,A==null&&y==null&&(A=w,y=x),d!=null&&m!=null){k=I-d,v=T-m;const q=Math.sqrt(k*k+v*v);k/=q,v/=q}A!=null&&y!=null&&B(N,J),i=d,u=m,p=g,f=M,d=I,m=T,g=_,M=F}function D(I,T){A=w,y=x,w=k,x=v,A==null&&y==null&&(A=w,y=x),A!=null&&y!=null&&B(I,T)}for(const I of s){i=null,u=null,p=null,f=null,d=null,m=null,g=null,M=null,A=null,y=null,w=null,x=null,k=null,v=null;const{totalTime:T,timeSeed:_}=I;for(let F=0;F<I.numberOfVertices;F++)C(a[4*(I.startVertex+F)+0],a[4*(I.startVertex+F)+1],a[4*(I.startVertex+F)+2],a[4*(I.startVertex+F)+3],T,_);D(T,_)}return{vertexData:o,indexData:h}}function Ut(t,e){const n=e.pixels,{width:r,height:a}=e,s=new Float32Array(r*a*2),l=e.mask||new Uint8Array(r*a*2);if(e.mask||l.fill(255),t==="vector-uv")for(let c=0;c<r*a;c++)s[2*c+0]=n[0][c],s[2*c+1]=-n[1][c];else if(t==="vector-magdir")for(let c=0;c<r*a;c++){const o=n[0][c],h=Wt(n[1][c]),i=Math.cos(h-Math.PI/2),u=Math.sin(h-Math.PI/2);s[2*c+0]=i*o,s[2*c+1]=u*o}return{data:s,mask:l,width:r,height:a}}async function Ge(t,e,n,r,a,s){const l=performance.now(),c=Gt(e.spatialReference);if(!c){const w=await Tt(t,e,n,r,a,s);return nt("esri-2d-profiler")&&X.info("I.7","loadImagery, early exit (ms)",Math.round(performance.now()-l)),nt("esri-2d-profiler")&&X.info("I.9","Number of parts",1),w}const[o,h]=c.valid,i=h-o,u=Math.ceil(e.width/i),p=e.width/u,f=Math.round(n/u);let d=e.xmin;const m=[],g=performance.now();for(let w=0;w<u;w++){const x=new Ot({xmin:d,xmax:d+p,ymin:e.ymin,ymax:e.ymax,spatialReference:e.spatialReference});m.push(Tt(t,x,f,r,a,s)),d+=p}const M=await Promise.all(m);nt("esri-2d-profiler")&&X.info("I.8","All calls to _fetchPart (ms)",Math.round(performance.now()-g)),nt("esri-2d-profiler")&&X.info("I.9","Number of parts",M.length);const A={data:new Float32Array(n*r*2),mask:new Uint8Array(n*r),width:n,height:r};let y=0;for(const w of M){for(let x=0;x<w.height;x++)for(let k=0;k<w.width;k++)y+k>=n||(A.data[2*(x*n+y+k)+0]=w.data[2*(x*w.width+k)+0],A.data[2*(x*n+y+k)+1]=w.data[2*(x*w.width+k)+1],A.mask[x*n+y+k]=w.mask[x*w.width+k]);y+=w.width}return nt("esri-2d-profiler")&&X.info("I.10","loadImagery, general exit (ms)",Math.round(performance.now()-l)),A}async function Tt(t,e,n,r,a,s){const l={requestProjectedLocalDirections:!0,signal:s};if($(a)&&(l.timeExtent=a),t.type==="imagery"){await t.load({signal:s});const h=t.rasterInfo.dataType,i=await t.fetchImage(e,n,r,l);return!i||H(i.pixelData)||H(i.pixelData.pixelBlock)?{data:new Float32Array(n*r*2),mask:new Uint8Array(n*r),width:n,height:r}:Ut(h,i.pixelData.pixelBlock)}await t.load({signal:s});const c=t.rasterInfo.dataType,o=await t.fetchPixels(e,n,r,l);return!o||H(o.pixelBlock)?{data:new Float32Array(n*r*2),mask:new Uint8Array(n*r),width:n,height:r}:Ut(c,o.pixelBlock)}export{Se as A,Te as B,De as D,Ve as F,Be as M,_e as R,Fe as S,Ce as T,Re as a,Jt as b,ct as c,Pt as d,Ge as e,Ne as f,$e as g,je as h,re as i,_t as j,L as k,wt as l,V as m,Me as n,Ue as o,Pe as p,be as q,Ee as r,Ae as s,Mt as t,ve as u,kt as v,ke as w,Le as x,Ie as y,Bt as z};
