package org.thingsboard.server.dao.util.imodel.query.store;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.ConstructionProject;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class ConstructionProjectSaveRequest extends SaveRequest<ConstructionProject> {
    // 项目编码
    @NotNullOrEmpty
    private String code;

    // 项目名称
    @NotNullOrEmpty
    private String name;

    // 项目地址
    @NotNullOrEmpty
    private String address;

    // 施工部门ID
    private String orgId;

    // 施工方
    @NotNullOrEmpty
    private String constructionSide;

    // // 项目添加时间
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    // private Date insertTime;

    // 施工开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    // 施工结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Override
    protected ConstructionProject build() {
        ConstructionProject entity = new ConstructionProject();
        Date now = new Date();
        entity.setInsertTime(new Date());
        entity.setCreateTime(now);
        entity.setCreator(currentUserUUID());
        entity.setTenantId(tenantId());
        commonSet(entity, now);
        return entity;
    }

    @Override
    protected ConstructionProject update(String id) {
        ConstructionProject entity = new ConstructionProject();
        entity.setId(id);
        commonSet(entity, new Date());
        return entity;
    }

    private void commonSet(ConstructionProject entity, Date now) {
        entity.setCode(code);
        entity.setName(name);
        entity.setAddress(address);
        entity.setOrgId(orgId);
        entity.setConstructionSide(constructionSide);
        entity.setStartTime(startTime);
        entity.setEndTime(endTime);
        entity.setUpdateTime(now);
    }
}