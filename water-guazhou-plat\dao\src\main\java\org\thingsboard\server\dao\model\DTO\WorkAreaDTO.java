package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工单区域统计报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-12-20
 */
@Data
public class WorkAreaDTO {
    private String type;

    private List<WorkAreaTopicDTO> workAreaTopicList = new ArrayList<>();

    private Map<String, WorkAreaTopicDTO> workAreaTopicMap = new HashMap<>();
}
