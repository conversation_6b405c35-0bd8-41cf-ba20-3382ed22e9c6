<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.deviceDump.DeviceDumpDetailMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           main_id,
                           device_label_code,
                           device_storage_is_dumped_by_device_label_code(device_label_code, tenant_id) as is_dumped,
                           tenant_id<!--@sql from device_dump_detail -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.deviceDump.DeviceDumpDetail">
        <result column="id" property="id"/>
        <result column="main_id" property="mainId"/>
        <result column="device_label_code" property="deviceLabelCode"/>
        <result column="tenant_id" property="tenantId"/>
        <association property="deviceInfoResponse"
                     javaType="org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse"
                     column="{labelCode=device_label_code,tenantId=tenant_id}"
                     select="org.thingsboard.server.dao.sql.deviceType.DeviceMapper.getInfoByDeviceLabelCode"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_dump_detail
        <where>
            <if test="mainId != null and mainId != null and mainId != ''">
                and main_id = #{mainId}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update device_dump_detail
        <set>
            <if test="deviceLabelCode != null">
                device_label_code = #{deviceLabelCode},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO device_dump_detail(id,
                                       main_id,
                                       device_label_code,
                                       tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.mainId},
             #{element.deviceLabelCode},
             #{element.tenantId})
        </foreach>
        on conflict(id) do nothing
    </insert>

    <update id="updateAll">
        update device_dump_detail
        <set>
            device_label_code = valueTable.deviceLabelCode
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.deviceLabelCode})
        </foreach>
        ) as valueTable(id, deviceLabelCode)
        where id = valueTable.id
    </update>

    <delete id="removeAllByMainId">
        delete
        from device_dump_detail
        where main_id = #{id}
    </delete>
</mapper>