import{d as L,a0 as V,M as B,c as F,r as g,S as M,D as q,b as C,o as E,bo as R,i as s,g as b,h,F as k,q as y,an as w,br as z,C as O}from"./index-r0dFAfgr.js";import{_ as $}from"./TreeBox-DDD2iwoR.js";import{_ as G}from"./CardTable-rdWOL4_6.js";import{_ as H}from"./CardSearch-CB_HNR-Q.js";import{_ as J}from"./index-BJ-QPYom.js";import{j as K,k as Q,s as U}from"./index-Bj5d3Vsu.js";import{a as X}from"./index-BggOjNGp.js";import Y from"./settingsDialog-CKfDVqGv.js";import Z from"./connectDialog-DBnsFYNJ.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./lodash.default-B3JdLn1L.js";import"./reduce-BbPixnH6.js";import"./padStart-BKfyZZDO.js";import"./_baseExtremum-UssVWohW.js";import"./_baseLt-svgXHEqw.js";import"./sortBy-DDhdj0i5.js";import"./max-CCqK09y5.js";import"./minBy-DBQvPu-j.js";import"./_baseSum-Cz9yialR.js";import"./min-ks0CS-3r.js";import"./sumBy-Dpy7mNiE.js";const I=L({__name:"alarmSettings",setup(ee){const _=V(),{$btnPerms:v}=B(),S=F(),n=g({devices:[],ents:new Map,severityColor:{提示:"rgb(85,204,244)",次要:"rgb(255,216,0)",重要:"#f58717",紧急:"rgb(245,75,23)",严重:"#FF0000"}}),c=g({title:"区域划分",data:_.projectList,loading:!1,isFilterTree:!0,currentProject:_.selectedProject,treeNodeHandleClick:t=>{c.currentProject=t,_.SET_selectedProject(t),m()}}),x=g({defaultParams:{keyword:""},filters:[{label:"搜索",field:"keyword",type:"input"},{type:"btn-group",btns:[{perm:!0,type:"primary",icon:"iconfont icon-chaxun",text:"查询",click:()=>m()},{perm:v("AlarmSettingAdd"),type:"primary",icon:"iconfont icon-jia",text:"添加告警",click:()=>P()}]}]}),i=g({loading:!1,dataList:[],columns:[{minWidth:200,prop:"name",label:"告警名称"},{minWidth:120,prop:"dName",label:"告警设备",width:180},{minWidth:120,prop:"attributeName",label:"监测数据",width:150},{minWidth:120,prop:"alarmTypeName",label:"告警类型"},{minWidth:120,prop:"cycleName",label:"周期",width:120},{minWidth:120,prop:"alarmValue",label:"告警触发值",width:120},{minWidth:120,prop:"recoverSet",label:"恢复类型",width:120},{minWidth:120,prop:"recoverValue",label:"恢复触发值",width:120},{minWidth:120,prop:"severity",label:"告警级别",cellStyle:t=>({color:t.severityColor})},{minWidth:120,prop:"period",label:"有效时间段",icon:"el-icon-time",iconStyle:{color:"#69e850"},width:200},{minWidth:120,prop:"alarmRemarks",label:"告警描述"}],operations:[{text:"关联联系人",isTextBtn:!0,perm:v("AlarmSettingConnectPerson"),icon:"iconfont icon-xiangqing",click:t=>T(t)},{text:"编辑",isTextBtn:!0,perm:v("AlarmSettingEdit"),icon:"iconfont icon-bianji",click:t=>A(t)},{text:"删除",isTextBtn:!0,type:"danger",perm:v("AlarmSettingDelete"),icon:"iconfont icon-shanchu",click:t=>N(t)}],operationWidth:"280px",pagination:{refreshData:({page:t,size:o})=>{i.pagination.limit=o,i.pagination.page=t,m()}}}),f=g({visible:!1,tableData:[],close:()=>f.visible=!1}),r=g({visible:!1,temp:{},project:{},deviceList:[],close:()=>r.visible=!1}),T=t=>{f.visible=!0,f.tableData=t},m=async t=>{var o,u;i.loading=!0;try{const l={page:i.pagination.page||1,size:i.pagination.limit||20};t||Object.assign(l,((o=S.value)==null?void 0:o.queryParams)||{});const p=await K((u=c.currentProject)==null?void 0:u.value,l),e=await D(p.data);console.log(e,"listlistlistlist"),i.dataList=e,i.pagination.total=p.data.total}catch{}i.loading=!1},D=async t=>{var p;n.devices=[];let o=t.data;i.pagination.total=(p=t.data)==null?void 0:p.length,(n.devices.length===0||n.ents.size===0)&&(await X(c.currentProject.id)).data.forEach(a=>{const d={label:a.name,value:a.id.id};n.ents.set(a.id.id,a.name),n.devices.push(d)});const u={day:"日",month:"月",year:"年"};o=o.map(e=>{const a={};e.alarmValue=e.details.alarmSetValue,e.recoverValue=e.details.recoverSetValue,e.alarmTypeName=e.details.setAlarmType,e.recoverSet=e.details.rType,e.alarmRemarks=e.details.alarmRemarks,e.attributeName=e.details.attributeName,e.severityColor=n.severityColor[e.severity];for(const d in e)a[d]=e[d];return a.isCycle=e.isCycle||!1,e.cycle?(a.cycleName=u[e.cycle],a.recoverSet="-",a.recoverValue="-"):(a.cycleName="",a.cycle=null),a.dName=n.ents.get(e.deviceId.id),a.name=e.name,a});const l=function(e,a){const d=e.createdTime,j=a.createdTime;return d<j?1:d>j?-1:0};return o=o.sort(l),o},N=async t=>{M("确定删除该告警, 是否继续?","删除提示").then(async()=>{(await Q(q(t.id.id))).status===200&&(C.success("删除成功"),m())})},P=()=>{r.project=c.currentProject,r.temp={},r.deviceList=n.devices,r.visible=!0},A=t=>{r.project=c.currentProject,r.temp=t,r.deviceList=n.devices,r.visible=!0},W=t=>{t.projectId=c.currentProject.id,U(t).then(()=>{C.success("保存成功"),i.pagination.page=1,m()})};return E(()=>{m()}),(t,o)=>{const u=J,l=H,p=G,e=$,a=z;return R((b(),h(e,null,{tree:k(()=>[y(u,{"tree-data":s(c)},null,8,["tree-data"])]),default:k(()=>[y(l,{ref_key:"refSearch",ref:S,config:s(x)},null,8,["config"]),y(p,{config:s(i),class:"card-table"},null,8,["config"]),s(r).visible?(b(),h(Y,{key:0,"dialog-config":s(r),onSave:W},null,8,["dialog-config"])):w("",!0),s(f).visible?(b(),h(Z,{key:1,"connect-dialog":s(f)},null,8,["connect-dialog"])):w("",!0)]),_:1})),[[a,!!s(c).loading]])}}}),Ce=O(I,[["__scopeId","data-v-6a4eaef0"]]);export{Ce as default};
