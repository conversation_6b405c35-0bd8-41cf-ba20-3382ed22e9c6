/** 实时监控-全部站点 */
export const AllStations = defineAsyncComponent(
  () => import('./RealTimeData/AllStations.vue')
);
export const AllStationsDetail = defineAsyncComponent(
  () => import('./details/AllStationsDetail.vue')
);
/** 实时监控-水源 */
export const WaterSource = defineAsyncComponent(
  () => import('./RealTimeData/waterSource.vue')
);
export const WaterSourceDetail = defineAsyncComponent(
  () => import('./details/WaterSourceDetail.vue')
);
/** 实时监控-水厂 */
export const WaterPlant = defineAsyncComponent(
  () => import('./RealTimeData/waterPlant.vue')
);
export const WaterPlantDetail = defineAsyncComponent(
  () => import('./details/WaterPlantDetail.vue')
);
/** 实时监控-污水处理厂 */
export const WaterPlantWS = defineAsyncComponent(
  () => import('./RealTimeData/waterPlantWS.vue')
);
export const WaterPlantWSDetail = defineAsyncComponent(
  () => import('./details/WaterPlantWSDetail.vue')
);

/** 智能水表 */
export const SmartMeter = defineAsyncComponent(
  () => import('./RealTimeData/smartMeter.vue')
);
export const SmartMeterDetail = defineAsyncComponent(
  () => import('./details/SmartMeterDetail.vue')
);
/** 实时监控-流量 */
export const FlowMonitoring = defineAsyncComponent(
  () => import('./RealTimeData/flowMonitoring.vue')
);
export const FlowMonitoringDetail = defineAsyncComponent(
  () => import('./details/FlowMonitoringDetail.vue')
);

/** 实时监控-压力 */

export const PressureMonitoring = defineAsyncComponent(
  () => import('./RealTimeData/pressureMonitoring.vue')
);
export const PressureMonitoringDetail = defineAsyncComponent(
  () => import('./details/PressureMonitoringDetail.vue')
);
/** 实时监控-水质 */

export const WaterQualityMonitoring = defineAsyncComponent(
  () => import('./RealTimeData/waterQualityMonitoring.vue')
);
export const WaterQualityMonitoringDetail = defineAsyncComponent(
  () => import('./details/WaterQualityMonitoringDetail.vue')
);
/** 实时监控-大用户 */
export const BigUser = defineAsyncComponent(
  () => import('./RealTimeData/bigUser.vue')
);
export const BigUserDetail = defineAsyncComponent(
  () => import('./details/BigUserDetail.vue')
);
/** 实时监控-泵站 */
export const Secondary = defineAsyncComponent(
  () => import('./RealTimeData/secondary.vue')
);
export const SecondaryDetail = defineAsyncComponent(
  () => import('./details/SecondaryDetail.vue')
);
/** 实时监控-消防栓 */
export const FireHydrant = defineAsyncComponent(
  () => import('./RealTimeData/fireHydrant.vue')
);

/**
 * 水池
 */
export const WaterPool = defineAsyncComponent(
  () => import('./RealTimeData/waterPoolMonitoring.vue')
);
export const WaterPoolDetail = defineAsyncComponent(
  () => import('./details/WaterPoolMonitoringDetail.vue')
);
/**
 * 实时监测-阀门
 */
export const RealTimeValve = defineAsyncComponent(
  () => import('./RealTimeData/valve.vue')
);
export const RealTimeValveDetail = defineAsyncComponent(
  () => import('./details/RealtimeValveDetail.vue')
);
/** 设备资产-详情 */
export const PipeDetail = defineAsyncComponent(
  () => import('./details/PipeDetail.vue')
);
/** 设备资产-管线 */
export const PipeLine = defineAsyncComponent(
  () => import('./equipmentAssets/pipeline.vue')
);

/** 设备资产-阀门 */
export const Valve = defineAsyncComponent(
  () => import('./equipmentAssets/valve.vue')
);

/** 设备资产-消防栓 */
export const FireHydrantDetail = defineAsyncComponent(
  () => import('./details/fireHydrantDetail.vue')
);

/** 设备资产-传感器 */
export const Sensor = defineAsyncComponent(
  () => import('./equipmentAssets/sensor.vue')
);

/** 设备资产-水表 */
export const WaterMeter = defineAsyncComponent(
  () => import('./equipmentAssets/waterMeter.vue')
);

/** 人员车辆-巡检人员 */
export const Inspection = defineAsyncComponent(
  () => import('./personnelVehicle/inspection.vue')
);
export const InspectionDetail = defineAsyncComponent(
  () => import('./details/InspectionDetail.vue')
);
/** 人员车辆-抄表人员 */
export const MeterReading = defineAsyncComponent(
  () => import('./personnelVehicle/meterReading.vue')
);
export const MeterReadingDetail = defineAsyncComponent(
  () => import('./details/MeterReadingDetail.vue')
);
/** 人员车辆-维修人员 */
export const Repair = defineAsyncComponent(
  () => import('./personnelVehicle/repair.vue')
);
export const RepairDetail = defineAsyncComponent(
  () => import('./details/RepairDetail.vue')
);
/** 人员车辆-车辆监控 */
export const VehicleMonitoring = defineAsyncComponent(
  () => import('./personnelVehicle/vehicleMonitoring.vue')
);
export const VehicleMonitoringDetail = defineAsyncComponent(
  () => import('./details/VehicleMonitoringDetail.vue')
);
/** 业务流程-工单流程 */
export const WorkOrder = defineAsyncComponent(
  () => import('./businessProcess/workOrder.vue')
);
export const WorkOrderDetail = defineAsyncComponent(
  () => import('./details/WorkOrderDetail.vue')
);
/** 业务流程-投诉热线 */
export const Complaint = defineAsyncComponent(
  () => import('./businessProcess/complaint.vue')
);
export const ComplaintDetail = defineAsyncComponent(
  () => import('./details/ComplaintDetail.vue')
);
/** 业务流程-用户报装 */
export const Install = defineAsyncComponent(
  () => import('./businessProcess/install.vue')
);
export const InstallDetail = defineAsyncComponent(
  () => import('./details/InstallDetail.vue')
);
/** 业务流程-巡检养护 */
export const InspectionMaintenance = defineAsyncComponent(
  () => import('./businessProcess/inspectionMaintenance.vue')
);
export const InspectionMaintenanceDetail = defineAsyncComponent(
  () => import('./details/InspectionMaintenanceDetail.vue')
);
/** 数据场景-水量热点图 */
export const WaterVolume = defineAsyncComponent(
  () => import('./dataScene/waterVolume.vue')
);
export const WaterVolumeDetail = defineAsyncComponent(
  () => import('./details/WaterVolumeDetail.vue')
);
/** 数据场景-事件热点图 */
export const EventHeatMap = defineAsyncComponent(
  () => import('./dataScene/event.vue')
);
export const EventHeatMapDetail = defineAsyncComponent(
  () => import('./details/EventDetail.vue')
);
/** 数据场景-DMA分区 */
export const Dma = defineAsyncComponent(() => import('./dataScene/dma.vue'));
export const DmaDetail = defineAsyncComponent(
  () => import('./details/DmaDetail.vue')
);
/** 数据场景-调度指挥 */
export const DispatchCommand = defineAsyncComponent(
  () => import('./emergencyDispatch/dispatchCommand.vue')
);
// export const DispatchCommandDetail = defineAsyncComponent(
//   () => import('./details/DispatchCommandDetail.vue')
// )
/** 数据场景-调度指挥 */
export const VideoSurveillance = defineAsyncComponent(
  () => import('./emergencyDispatch/videoSurveillance.vue')
);
export const VideoSurveillanceDetail = defineAsyncComponent(
  () => import('./details/VideoSurveillanceDetail.vue')
);
