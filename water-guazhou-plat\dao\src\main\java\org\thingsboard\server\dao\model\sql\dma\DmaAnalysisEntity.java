package org.thingsboard.server.dao.model.sql.dma;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * DMA分析
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DMA_ANALYSIS_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class DmaAnalysisEntity {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.DMA_ANALYSIS_PARTITION_ID)
    private String partitionId;

    @Column(name = ModelConstants.DMA_ANALYSIS_SUPPLY_TOTAL)
    private Double supplyTotal;

    @Column(name = ModelConstants.DMA_ANALYSIS_REAL_TOTAL)
    private Double realTotal;

    @Column(name = ModelConstants.DMA_ANALYSIS_FREE_USE)
    private Double freeUse;

    @Column(name = ModelConstants.DMA_ANALYSIS_LOSS_TOTAL)
    private Double lossTotal;

    @Column(name = ModelConstants.DMA_ANALYSIS_CREATE_TIME)
    private Date createTime;

}
