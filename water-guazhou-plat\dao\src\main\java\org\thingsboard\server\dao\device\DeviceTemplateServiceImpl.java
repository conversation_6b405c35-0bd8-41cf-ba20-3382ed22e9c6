package org.thingsboard.server.dao.device;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.device.DeviceTemplateAndProtocol;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.dao.attributes.AttributesService;
import org.thingsboard.server.dao.model.sql.DeviceTemplate;
import org.thingsboard.server.dao.model.sql.Protocol;
import org.thingsboard.server.dao.sql.device.DeviceTemplateRepository;
import org.thingsboard.server.dao.sql.device.ProtocolRepository;

import javax.transaction.Transactional;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;

@Slf4j
@Service
@Transactional
public class DeviceTemplateServiceImpl implements DeviceTemplateService {

    @Autowired
    private DeviceTemplateRepository deviceTemplateRepository;

    @Autowired
    private ProtocolRepository protocolRepository;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    protected AttributesService attributesService;


    @Override
    public DeviceTemplate add(DeviceTemplate deviceTemplate) {
        if (deviceTemplate.getCreateTime() == null) {
            deviceTemplate.setCreateTime(System.currentTimeMillis());
        }
        return deviceTemplateRepository.save(deviceTemplate);
    }

    @Override
    public DeviceTemplate edit(DeviceTemplate deviceTemplate) {
        DeviceTemplate _deviceTemplate = deviceTemplateRepository.findOne(deviceTemplate.getId());
        if (_deviceTemplate != null) {
            _deviceTemplate.setName(deviceTemplate.getName());
            _deviceTemplate.setRemark(deviceTemplate.getRemark());
            _deviceTemplate.setAdditionalInfo(deviceTemplate.getAdditionalInfo());

            deviceTemplateRepository.save(_deviceTemplate);
            return _deviceTemplate;
        }
        return null;
    }

    @Override
    public DeviceTemplate findById(String id) {
        return deviceTemplateRepository.findOne(id);
    }

    @Override
    public List<DeviceTemplate> findDeviceTemplateByTenant(TenantId tenantId) {
        Sort orders = new Sort(new Sort.Order(Sort.Direction.DESC, "createTime"));
        List<DeviceTemplate> deviceTemplateList = deviceTemplateRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()), orders);
        if (deviceTemplateList != null && deviceTemplateList.size() > 0) {
            return deviceTemplateList;
        }
        return new ArrayList<>();
    }

    @Override
    public DeviceTemplate deleteById(String id) throws ThingsboardException {
        DeviceTemplate _deviceTemplate = deviceTemplateRepository.findOne(id);
        // 查看是否有设备关联该模板
        List<Device> deviceList = deviceService.findByTemplateId(id);
        if (deviceList != null && deviceList.size() > 0) {
            throw new ThingsboardException("有存在的设备引用该模板, 不允许删除！", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        if (_deviceTemplate != null) {
            deviceTemplateRepository.delete(id);
            // 删除协议详情
//            protocolRepository.deleteByTemplateId(id);
            return _deviceTemplate;
        }
        return null;
    }


    @Override
    public void saveDeviceTemplateAndProtocol(DeviceTemplateAndProtocol deviceTemplateAndProtocol) throws ThingsboardException {
        // 查询设备模板是否存在
        DeviceTemplate deviceTemplate = deviceTemplateRepository.findOne(deviceTemplateAndProtocol.getId());
        if (deviceTemplate == null) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 先更新设备模板
        deviceTemplate.setRemark(deviceTemplateAndProtocol.getRemark());
        deviceTemplate.setAdditionalInfo(deviceTemplateAndProtocol.getAdditionalInfo());
        deviceTemplate.setName(deviceTemplateAndProtocol.getName());

        deviceTemplate.setProtocol(JSON.toJSONString(deviceTemplateAndProtocol.getProtocolList()));

        deviceTemplateRepository.save(deviceTemplate);
    }

    @Override
    public DeviceTemplateAndProtocol findDeviceTemplateAndProtocolById(String id) throws ThingsboardException {
        // 查询设备模板信息
        DeviceTemplate deviceTemplate = deviceTemplateRepository.findOne(id);
        if (deviceTemplate == null) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        DeviceTemplateAndProtocol deviceTemplateAndProtocol = new DeviceTemplateAndProtocol();
        BeanUtils.copyProperties(deviceTemplate, deviceTemplateAndProtocol);

        deviceTemplateAndProtocol.setProtocolList(JSON.parseArray(deviceTemplate.getProtocol()));

        return deviceTemplateAndProtocol;
    }

    @Override
    public void copyDeviceTemplate(String deviceTemplateId) throws ThingsboardException {
        // 查询设备模板信息
        DeviceTemplate deviceTemplate = deviceTemplateRepository.findOne(deviceTemplateId);
        if (deviceTemplate == null) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 修改名称
        DeviceTemplate copyDeviceTemplate = new DeviceTemplate();
        BeanUtils.copyProperties(deviceTemplate, copyDeviceTemplate);
        copyDeviceTemplate.setName(deviceTemplate.getName() + " 副本 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date()));
        copyDeviceTemplate.setId(null);
        copyDeviceTemplate.setCreateTime(System.currentTimeMillis());
        deviceTemplateRepository.save(copyDeviceTemplate);
    }

    @Override
    public List<DeviceTemplate> findDeviceTemplateByTenantAndType(TenantId tenantId, String type) {
        Sort orders = new Sort(new Sort.Order(Sort.Direction.ASC, "createTime"));
        List<DeviceTemplate> deviceTemplateList = deviceTemplateRepository.findByTenantIdAndType(UUIDConverter.fromTimeUUID(tenantId.getId()), type, orders);
        if (deviceTemplateList != null && deviceTemplateList.size() > 0) {
            return deviceTemplateList;
        }
        return new ArrayList<>();
    }

    @Override
    public List<DeviceTemplate> findByIds(List<String> ids) {
        return deviceTemplateRepository.findByIdIn(ids);
    }

    @Override
    public void saveAll(List<DeviceTemplate> data) {
        deviceTemplateRepository.save(data);
    }

    @Override
    public Map<String, List<JSONObject>> findDeviceTemplateAndProtocolGroup(String id, TenantId tenantId) throws ThingsboardException, ExecutionException, InterruptedException {
        Map<String, List<JSONObject>> result = new LinkedHashMap<>();
        result.put("全部", new ArrayList<>());

        ListenableFuture<Optional<AttributeKvEntry>> prop = attributesService.find(tenantId, new DeviceId(UUIDConverter.fromString(id)), DataConstants.SHARED_SCOPE, "prop");

        List<Protocol> array = JSON.parseArray(prop.get().get().getStrValue().get(), Protocol.class);
        List<JSONObject> all = new ArrayList<>();
        for (Protocol protocol : array) {
            JSONObject p = new JSONObject();
            p.put("label", protocol.getPropertyCategory());
            p.put("value", protocol.getName());
            p.put("unit", protocol.getUnit());

            String group = protocol.getGroup();

            if (StringUtils.isNotBlank(group)) {
                List<JSONObject> list = new ArrayList<>();
                if (result.containsKey(group)) {
                    list = result.get(group);
                }

                list.add(p);
                result.put(group, list);
            }

            all.add(p);
        }

        result.put("全部", all);

        return result;
    }

    @Override
    public List<JSONObject> findProtocolById(String id, TenantId tenantId) throws Exception {
        ListenableFuture<Optional<AttributeKvEntry>> prop = attributesService.find(tenantId, new DeviceId(UUIDConverter.fromString(id)), DataConstants.SHARED_SCOPE, "prop");

        List<Protocol> array = JSON.parseArray(prop.get().get().getStrValue().get(), Protocol.class);
        List<JSONObject> all = new ArrayList<>();
        for (Protocol protocol : array) {
            JSONObject p = new JSONObject();
            p.put("label", protocol.getName());
            p.put("value", protocol.getPropertyCategory());
            p.put("unit", protocol.getUnit());

            all.add(p);
        }

        return all;
    }

    @Override
    public void save(DeviceTemplate template) {
        deviceTemplateRepository.save(template);
    }


}
