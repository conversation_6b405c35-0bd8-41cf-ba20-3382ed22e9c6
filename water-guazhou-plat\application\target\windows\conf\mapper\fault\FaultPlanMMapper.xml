<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.fault.FaultPlanMMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.fault.FaultPlanM">
        select a.*, b.first_name as creatorName, b1.first_name as reviewerName, td.name reviewerDepartment, b2.first_name as userName, tctm.name as departmentName
        from tb_device_fault_plan_m a
        left join tb_user b on a.creator = b.id
        left join tb_user b1 on a.reviewer = b1.id
        left join tb_department td on b1.department_id =td.id
        left join tb_user b2 on a.user_id = b2.id
        left join tb_department tctm on b2.department_id = tctm.id
        where 1=1
         <if test="planName != ''">
             and a.name like '%' || #{planName} || '%'
         </if>
        <if test="userName != ''">
            and b2.first_name like '%' || #{userName} || '%'
        </if>
        <if test="teamName != ''">
            and tctm.name like '%' || #{teamName} || '%'
        </if>
        and a.tenant_id = #{tenantId}

        <if test="startStartTime != null" >
            and a.start_time &gt;= #{startStartTime}
        </if>

        <if test="startEndTime != null" >
            and a.start_time &lt;= #{startEndTime}
        </if>

        <if test="endStartTime != null" >
            and a.end_time &gt;= #{endStartTime}
        </if>

        <if test="endEndTime != null" >
            and a.end_time &lt;= #{endEndTime}
        </if>

        order by a.create_time desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_device_fault_plan_m a
        left join tb_user b on a.creator = b.id
        left join tb_user b1 on a.reviewer = b1.id
        left join tb_department td on b1.department_id =td.id
        left join tb_user b2 on a.user_id = b2.id
        left join tb_department tctm on b.department_id = tctm.id
        where 1=1
        <if test="planName != ''">
            and a.name like '%' || #{planName} || '%'
        </if>
        <if test="userName != ''">
            and b2.first_name like '%' || #{userName} || '%'
        </if>
        <if test="teamName != ''">
            and tctm.name like '%' || #{teamName} || '%'
        </if>
        and a.tenant_id = #{tenantId}

        <if test="startStartTime != null" >
            and a.start_time &gt;= #{startStartTime}
        </if>

        <if test="startEndTime != null" >
            and a.start_time &lt;= #{startEndTime}
        </if>

        <if test="endStartTime != null" >
            and a.end_time &gt;= #{endStartTime}
        </if>

        <if test="endEndTime != null" >
            and a.end_time &lt;= #{endEndTime}
        </if>
    </select>

    <select id="getById" resultType="org.thingsboard.server.dao.model.sql.fault.FaultPlanM">
        select a.*, b.first_name as creatorName, b1.first_name as reviewerName, td.name reviewerDepartment, b2.first_name as userName, tctm.name as departmentName
        from tb_device_fault_plan_m a
                 left join tb_user b on a.creator = b.id
                 left join tb_user b1 on a.reviewer = b1.id
                 left join tb_department td on b1.department_id =td.id
                 left join tb_user b2 on a.user_id = b2.id
                 left join tb_department tctm on b.department_id = tctm.id
        where a.id = #{mainId}
    </select>

</mapper>