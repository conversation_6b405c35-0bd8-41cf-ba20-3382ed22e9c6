package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.COMPONENT_STORAGE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class ComponentStorageEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.COMPONENT_STORAGE_CODE)
    private String code;

    @Column(name = ModelConstants.NAME)
    private String name;

    @Column(name = ModelConstants.COMPONENT_STORAGE_TYPE)
    private String type;

    @Column(name = ModelConstants.COMPONENT_STORAGE_SPECIFICATION)
    private String specification;

    @Column(name = ModelConstants.COMPONENT_STORAGE_UNIT)
    private String unit;

    @Column(name = ModelConstants.COMPONENT_STORAGE_NUMBER)
    private Long number;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.CREATOR_PROPERTY)
    private String creator;

    @Column(name = ModelConstants.COMPONENT_STORAGE_IS_DEL)
    private String isDel;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
