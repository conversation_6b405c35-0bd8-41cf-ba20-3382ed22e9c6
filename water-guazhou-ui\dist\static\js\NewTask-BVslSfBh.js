import{d as N,c as v,r as k,b as c,Q as S,g as D,h as T,F as B,q as M,i as U,_ as z,X as V}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as G,a as A}from"./LayerHelper-Cn-iiqxI.js";import{g as R}from"./QueryHelper-ILO3qZqg.js";import{GetFieldConfig as j,GetFieldUniqueValue as E}from"./fieldconfig-Bk3o1wi7.js";import{u as O}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import{A as P}from"./maintenance-zUn_QdHH.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import Q from"./RightDrawerMap-D5PhmGFO.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Ht=N({__name:"NewTask",setup(J){const o=v(),x=v(),p={},t=k({tabs:[],loading:!1,detailing:!1,uniquing:!1,layerIds:[],layerInfos:[]}),g=k({group:[{fields:[{type:"input",label:"任务名称",field:"name",rules:[{required:!0,message:"请输入任务名称"}]},{type:"select",field:"device",options:[],label:"选择设备类型",rules:[{required:!0,message:"请选择设备类型"}],onChange:()=>h()},{type:"btn-group",label:"绘制工具",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>t.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>y("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>t.loading,iconifyIcon:"ep:crop",click:()=>y("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制椭圆",disabled:()=>t.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>y("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>t.loading,iconifyIcon:"ep:delete",click:()=>f()}]},{type:"list",label:"设备属性",data:[],className:"sql-list-wrapper",setData:async(e,i)=>{var a,d,u;if(i.device===void 0)return;const r=i.device,l=(a=t.layerInfos.find(m=>m.layerid===r))==null?void 0:a.layername;if(!l)return;const s=await j(l);e.data=(u=(d=s.data)==null?void 0:d.result)==null?void 0:u.rows},setDataBy:"device",displayField:"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:e=>{t.curFieldNode=e,n(e.name)}},{id:"uniquevalue",type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{n("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{n("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{n(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{n("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{n("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{n("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{n("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{n("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:e=>{n("'"+e+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>t.uniquing?"正在获取唯一值":"获取唯一值",loading:()=>t.uniquing,disabled:()=>t.loading,styles:{width:"100%",borderRadius:"0"},click:()=>F()}]}]}]},{type:"textarea",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",btns:[{perm:!0,text:"清除组合条件",type:"danger",disabled:()=>t.uniquing,click:()=>q(),styles:{width:"100%"}},{perm:!0,text:"查看设备详情",type:"success",disabled:()=>t.uniquing,loading:()=>t.detailing,click:()=>h(),styles:{width:"100%"}}]},{type:"datetimerange",label:"起止时间",field:"beginTime",rules:[{required:!0,message:"请输入选择起止时间"}]},{type:"user-select",label:"养护人员",placement:"top",field:"maintainUser"},{type:"textarea",label:"备注",field:"remark"},{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},text:"确定",loading:()=>t.loading,click:()=>{var e;return(e=o.value)==null?void 0:e.Submit()}},{perm:!0,styles:{width:"100%"},text:"重置",type:"default",disabled:t.loading,click:()=>{var e;return(e=o.value)==null?void 0:e.resetForm()}}]}]}],labelPosition:"top",gutter:12,defaultValue:{maintainUser:[]},submit:e=>{var r,l,s,a,d,u;const i={...e,deviceName:(r=t.layerInfos.find(m=>m.layerid===e.device))==null?void 0:r.layername,maintainUser:(l=e.maintainUser)==null?void 0:l.join(","),beginTime:(s=e.beginTime)==null?void 0:s[0],endTime:(a=e.beginTime)==null?void 0:a[1],items:((u=(d=t.tabs[0])==null?void 0:d.data)==null?void 0:u.map(m=>({objectId:m})))||[]};P(i).then(m=>{var w;m.data.code===200?(c.success(m.data.message),(w=o.value)==null||w.resetForm()):c.error(m.data.message)}).catch(m=>{console.log(m),c.error("系统错误")})}}),q=()=>{var e;(e=o.value)!=null&&e.dataForm&&(o.value.dataForm.sql="")},F=async()=>{var i,r;if(!t.curFieldNode)return;const e=(i=o.value)==null?void 0:i.dataForm.device;if(e===void 0){c.warning("请先选择设备");return}t.uniquing=!0;try{const l=await E({layerid:e,field_name:t.curFieldNode.name}),s=(r=g.group[0].fields.find(d=>d.id==="uniquevalue"))==null?void 0:r.extraFormItem,a=s&&s[0];a&&(a.data=l.data.result.rows)}catch{c.error("获取唯一值失败")}t.uniquing=!1},n=e=>{var r;if(!o.value)return;(r=o.value)!=null&&r.dataForm||(o.value.dataForm={});const i=o.value.dataForm.sql||" ";o.value.dataForm.sql=i+e+" "},y=e=>{var i;f(),(i=p.sketch)==null||i.create(e)},f=()=>{var e;(e=p.graphicsLayer)==null||e.removeAll(),p.graphic=void 0},h=async()=>{var s,a,d;const e=((s=o.value)==null?void 0:s.dataForm)||{},i=e.device;if(i===void 0){c.warning("请先选择设备");return}if(!p.graphic){c.warning("请绘制范围");return}const r=e.sql,l=(a=p.graphic)==null?void 0:a.geometry;t.tabs=await R([i],t.layerInfos,{geometry:l,where:r}),(d=x.value)==null||d.refreshDetail(t.tabs)},I=async()=>{var l,s;t.layerIds=A(p.view);const e=await V(t.layerIds);t.layerInfos=((s=(l=e.data)==null?void 0:l.result)==null?void 0:s.rows)||[];const i=g.group[0].fields[1],r=t.layerInfos.map(a=>({label:a.layername,value:a.layerid,data:a}));i&&(i.options=r)},{initSketch:_,destroySketch:C}=O(),b=e=>{e.state==="complete"&&(p.graphic=e.graphics[0],h())},L=async e=>{p.view=e,p.graphicsLayer=G(p.view,{id:"inspect-range",title:"养护范围"}),p.sketch=_(p.view,p.graphicsLayer,{updateCallBack:b,createCallBack:b}),I()};return S(()=>{C(),f()}),(e,i)=>{const r=z;return D(),T(Q,{ref_key:"refMap",ref:x,title:"新建任务",onMapLoaded:L},{default:B(()=>[M(r,{ref_key:"refForm",ref:o,config:U(g)},null,8,["config"])]),_:1},512)}}});export{Ht as default};
