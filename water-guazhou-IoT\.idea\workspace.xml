<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="441eff54-dbd9-4582-b881-c281efea59aa" name="Changes" comment="研发云pom中修改gradle配置">
      <change beforePath="$PROJECT_DIR$/application/gradle.properties" beforeDir="false" afterPath="$PROJECT_DIR$/application/gradle.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/application/gradle/wrapper/gradle-wrapper.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/gradle/wrapper/gradle-wrapper.properties" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\apache-maven-3.6.3\.m2" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.3\conf\settings.xml" />
        <option name="workOffline" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zf6lkcJYjnk6c4oz7EtoNSCJpV" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.ThingsboardServerApplication.executor": "Run",
    "Maven.thingsboard [clean].executor": "Run",
    "Maven.thingsboard [install].executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Code-Yanfayun/water/guazhou/water-guazhou-IoT",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "editor.preferences.import"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="ThingsboardServerApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.thingsboard.server.ThingsboardServerApplication" />
      <module name="application" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.thingsboard.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="water-guazhou-IoT" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="water-guazhou-IoT" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ThingsboardServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="441eff54-dbd9-4582-b881-c281efea59aa" name="Changes" comment="" />
      <created>1752109933995</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752109933995</updated>
    </task>
    <task id="LOCAL-00001" summary="修改gradle配置">
      <option name="closed" value="true" />
      <created>1752128005754</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752128005754</updated>
    </task>
    <task id="LOCAL-00002" summary="修改gradle配置，研发云">
      <option name="closed" value="true" />
      <created>1752128919587</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752128919587</updated>
    </task>
    <task id="LOCAL-00003" summary="研发云修改gradle配置">
      <option name="closed" value="true" />
      <created>1752129339796</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752129339796</updated>
    </task>
    <task id="LOCAL-00004" summary="研发云修改gradle配置">
      <option name="closed" value="true" />
      <created>1752130233613</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752130233613</updated>
    </task>
    <task id="LOCAL-00005" summary="研发云重新增加gradle配置">
      <option name="closed" value="true" />
      <created>1752133179500</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752133179500</updated>
    </task>
    <task id="LOCAL-00006" summary="研发云重新增加gradle配置">
      <option name="closed" value="true" />
      <created>1752133496544</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752133496544</updated>
    </task>
    <task id="LOCAL-00007" summary="研发云pom中修改gradle配置">
      <option name="closed" value="true" />
      <created>1752141408592</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752141408592</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="修改gradle配置" />
    <MESSAGE value="修改gradle配置，研发云" />
    <MESSAGE value="研发云修改gradle配置" />
    <MESSAGE value="研发云重新增加gradle配置" />
    <MESSAGE value="研发云pom中修改gradle配置" />
    <option name="LAST_COMMIT_MESSAGE" value="研发云pom中修改gradle配置" />
  </component>
</project>