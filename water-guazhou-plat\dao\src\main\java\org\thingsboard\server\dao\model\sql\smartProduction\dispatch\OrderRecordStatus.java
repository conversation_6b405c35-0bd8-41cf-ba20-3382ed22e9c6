package org.thingsboard.server.dao.model.sql.smartProduction.dispatch;

import org.thingsboard.server.dao.util.imodel.response.NameDisplayableEnum;

public enum OrderRecordStatus implements NameDisplayableEnum {
    PENDING("待发送"),
    WAITING_RECEIVE("待接收"),
    WAITING_REPLY("待回复"),
    REPLIED("已回复"),
    DECLINED("已拒绝");

    private final String displayName;

    OrderRecordStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
