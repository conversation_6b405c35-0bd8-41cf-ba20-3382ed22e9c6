import{d as h,r as i,c as y,a8 as k,s as w,bu as L,o as x,g as C,n as D,q as p,i as c,F as v,b6 as S,bL as I}from"./index-r0dFAfgr.js";import{_ as q}from"./CardTable-rdWOL4_6.js";import{_ as B}from"./CardSearch-CB_HNR-Q.js";import T from"./detail-UHbHAYbG.js";import{I as M}from"./common-CvK_P_ao.js";import{p as N}from"./process-DWVjEFpZ.js";import{p as z}from"./applyInstall-D-IustB3.js";import{f as E}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./printUtils-C-AxhDcd.js";const R={class:"wrapper"},J=h({__name:"index",setup(V){const r=i({typeList:[],taskInfo:{}}),f=y(),m=y(),b=i({filters:[{label:"工程编号",field:"code",type:"input"},{label:"工程类型",field:"type",type:"select",options:k(()=>r.typeList)},{label:"步骤名称",field:"currentStep",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:M.QUERY,click:()=>l()}]}]}),d=i({title:"",cancel:!1,width:"80%",group:[]}),e=i({indexVisible:!0,columns:[{label:"工程编号",prop:"code"},{label:"任务类型",prop:"typeName"},{label:"当前步骤",prop:"currentStep"},{label:"任务地址",prop:"address"},{label:"创建时间",prop:"createTime",formatter:(a,t)=>E(t)}],dataList:[],operationWidth:100,operations:[{perm:!0,text:"查看",isTextBtn:!1,type:"success",svgIcon:w(I),click:a=>{var t;d.title=a.code,(t=f.value)==null||t.openDrawer(),r.taskInfo=a}}],pagination:{refreshData:({page:a,size:t})=>{e.pagination.page=a,e.pagination.limit=t,l()}}});L(async()=>{var o,n;const a=await N({page:1,size:9999});r.typeList=(o=a.data)==null?void 0:o.data.data;const t=(n=a.data)==null?void 0:n.data.data;r.typeList=t.map(s=>({label:s.name,value:s.id}))});const l=async()=>{var n,s,u,_,g;const t={...((n=m.value)==null?void 0:n.queryParams)||{},status:"已完成",page:e.pagination.page||1,size:e.pagination.limit||20},o=await z(t);e.pagination.total=(u=(s=o.data)==null?void 0:s.data)==null?void 0:u.total,e.dataList=(g=(_=o.data)==null?void 0:_.data)==null?void 0:g.data};return x(async()=>{await l()}),(a,t)=>{const o=B,n=q,s=S;return C(),D("div",R,[p(o,{ref_key:"refSearch",ref:m,config:c(b)},null,8,["config"]),p(n,{class:"card-table",config:c(e)},null,8,["config"]),p(s,{ref_key:"refDetail",ref:f,config:c(d)},{default:v(()=>[p(T,{"task-info":c(r).taskInfo,"task-id":"1"},null,8,["task-info"])]),_:1},8,["config"])])}}});export{J as default};
