package org.thingsboard.server.dao.util.imodel.query.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceTypeAttr;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class DeviceTypeAttrSaveRequest extends SaveRequest<DeviceTypeAttr> {
    // 设备类型编号
    @NotNullOrEmpty
    private String serialId;

    // 属性编码
    @NotNullOrEmpty
    private String code;

    // 类别名称
    @NotNullOrEmpty
    private String name;

    // 备注
    private String remark;

    // 创建人
    private String creator;

    @Override
    protected DeviceTypeAttr build() {
        DeviceTypeAttr attr = new DeviceTypeAttr();
        attr.setCreator(currentUserUUID());
        attr.setCreateTime(createTime());
        attr.setTenantId(tenantId());
        commonSet(attr);

        return attr;
    }

    @Override
    protected DeviceTypeAttr update(String id) {
        DeviceTypeAttr attr = new DeviceTypeAttr();
        attr.setId(id);
        commonSet(attr);
        return attr;
    }

    private void commonSet(DeviceTypeAttr entity) {
        entity.setSerialId(serialId);
        entity.setCode(code);
        entity.setName(name);
        entity.setRemark(remark);
        entity.setCreator(creator);
        entity.setUpdateTime(createTime());
    }
}
