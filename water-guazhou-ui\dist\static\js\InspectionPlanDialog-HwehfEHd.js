import{m as y,d as j,c as m,r as z,am as x,bB as A,x as k,o as J,g as f,h as V,F as o,p as K,q as e,G as B,bh as D,an as G,n as P,aJ as H,aB as W,dr as Q,H as X,I as Y,bU as Z,aK as ee,aL as le,bW as ae,K as te,J as oe,L as ne,C as se}from"./index-r0dFAfgr.js";/* empty css                         */function be(r){return y({url:"/api/inspection/plan",method:"get",params:r})}function re(r){return y({url:`/api/inspection/plan/${r}`,method:"get"})}function ie(r){return y({url:"/api/inspection/plan",method:"post",data:r})}function ue(r,T){return y({url:`/api/inspection/plan/${r}`,method:"post",data:T})}function ge(r){return y({url:`/api/inspection/plan/${r}`,method:"delete"})}function de(){return y({url:"/api/inspection/checklist-template",method:"get"})}const pe={key:0,class:"loading-skeleton"},ce={class:"dialog-footer"},me=j({__name:"InspectionPlanDialog",props:{modelValue:{type:Boolean,default:!1},editId:{default:""},readonly:{type:Boolean,default:!1}},emits:["update:modelValue","success"],setup(r,{emit:T}){const u=r,C=T,_=m(!1),g=m(!1),v=m(!1),h=m(),d=m(!1),E=m([{label:"设备运行状态检查表",value:"equipment_status"},{label:"管网压力检查表",value:"pipeline_pressure"},{label:"泵站运行检查表",value:"pump_station"},{label:"水质检测检查表",value:"water_quality"},{label:"安全隐患检查表",value:"safety_hazard"}]),a=z({planName:"",inspectionType:"",inspectionCycle:"",executionRole:"",checklistTemplate:"",status:"1",remark:""}),U={planName:[{required:!0,message:"请输入巡检计划名称",trigger:"blur"}],inspectionType:[{required:!0,message:"请选择巡检类型",trigger:"change"}],inspectionCycle:[{required:!0,message:"请选择巡检周期",trigger:"change"}],executionRole:[{required:!0,message:"请选择执行角色",trigger:"change"}],checklistTemplate:[{required:!0,message:"请输入检查表模板",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]};x(()=>u.modelValue,async l=>{_.value=l,l&&(d.value=!!u.editId,d.value?await S():q())}),x(_,l=>{C("update:modelValue",l)});const L=()=>u.readonly?"查看巡检计划":d.value?"编辑巡检计划":"新建巡检计划",q=()=>{a.planName="",a.inspectionType="",a.inspectionCycle="",a.executionRole="",a.checklistTemplate="",a.status="1",a.remark="",A(()=>{var l;(l=h.value)==null||l.clearValidate()})},S=async()=>{if(u.editId)try{g.value=!0;const l=await re(u.editId);if(l!=null&&l.data){const t=l.data.data||l.data;a.planName=t.planName||"",a.inspectionType=t.inspectionType||"",a.inspectionCycle=t.inspectionCycle||"",a.executionRole=t.executionRole||"",a.checklistTemplate=t.checklistTemplate||"",a.status=t.status||"1",a.remark=t.remark||""}}catch(l){console.error(l),k.error("获取数据失败")}finally{g.value=!1}},F=async()=>{if(h.value)try{await h.value.validate(),v.value=!0,d.value?(await ue(u.editId,a),k.success("更新成功")):(await ie(a),k.success("创建成功")),C("success"),w()}catch(l){console.error(l),k.error(d.value?"更新失败":"创建失败")}finally{v.value=!1}},$=async()=>{try{const l=await de();if(l!=null&&l.data){const t=l.data.data||l.data;Array.isArray(t)&&(E.value=t.map(p=>({label:p.name||p.label,value:p.id||p.value})))}}catch(l){console.error("加载检查表模板失败:",l)}},w=()=>{_.value=!1,g.value=!1,v.value=!1,q()};return J(()=>{$()}),(l,t)=>{const p=Q,N=X,i=Y,c=Z,s=ee,b=le,I=ae,M=te,R=oe,O=ne;return f(),V(O,{modelValue:_.value,"onUpdate:modelValue":t[7]||(t[7]=n=>_.value=n),title:L(),width:"800px","before-close":w},{footer:o(()=>[K("span",ce,[e(R,{onClick:w},{default:o(()=>[B(D(l.readonly?"关闭":"取消"),1)]),_:1}),l.readonly?G("",!0):(f(),V(R,{key:0,type:"primary",onClick:F,loading:v.value},{default:o(()=>[B(D(d.value?"更新":"创建"),1)]),_:1},8,["loading"]))])]),default:o(()=>[g.value?(f(),P("div",pe,[e(p,{rows:6,animated:""})])):(f(),V(M,{key:1,ref_key:"formRef",ref:h,model:a,rules:U,"label-width":"120px"},{default:o(()=>[e(I,{gutter:20},{default:o(()=>[e(c,{span:12},{default:o(()=>[e(i,{label:"巡检计划名称",prop:"planName"},{default:o(()=>[e(N,{modelValue:a.planName,"onUpdate:modelValue":t[0]||(t[0]=n=>a.planName=n),placeholder:"请输入巡检计划名称",readonly:l.readonly},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(c,{span:12},{default:o(()=>[e(i,{label:"巡检类型",prop:"inspectionType"},{default:o(()=>[e(b,{modelValue:a.inspectionType,"onUpdate:modelValue":t[1]||(t[1]=n=>a.inspectionType=n),placeholder:"请选择巡检类型",style:{width:"100%"},disabled:l.readonly},{default:o(()=>[e(s,{label:"管网巡检",value:"pipeline"}),e(s,{label:"泵站巡检",value:"pumpStation"}),e(s,{label:"其他巡检",value:"other"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(I,{gutter:20},{default:o(()=>[e(c,{span:12},{default:o(()=>[e(i,{label:"巡检周期",prop:"inspectionCycle"},{default:o(()=>[e(b,{modelValue:a.inspectionCycle,"onUpdate:modelValue":t[2]||(t[2]=n=>a.inspectionCycle=n),placeholder:"请选择巡检周期",style:{width:"100%"},disabled:l.readonly},{default:o(()=>[e(s,{label:"每周一、三、五日执行",value:"weekly_135"}),e(s,{label:"每月5、15、25日执行",value:"monthly_51525"}),e(s,{label:"每季度第一个月10日执行",value:"quarterly_10"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(c,{span:12},{default:o(()=>[e(i,{label:"执行角色",prop:"executionRole"},{default:o(()=>[e(b,{modelValue:a.executionRole,"onUpdate:modelValue":t[3]||(t[3]=n=>a.executionRole=n),placeholder:"请选择执行角色",style:{width:"100%"},disabled:l.readonly},{default:o(()=>[e(s,{label:"运维组长、设备专员",value:"maintenance_equipment"}),e(s,{label:"巡检员、设备专员",value:"inspector_equipment"}),e(s,{label:"其他",value:"other"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(I,{gutter:20},{default:o(()=>[e(c,{span:12},{default:o(()=>[e(i,{label:"检查表模板",prop:"checklistTemplate"},{default:o(()=>[e(b,{modelValue:a.checklistTemplate,"onUpdate:modelValue":t[4]||(t[4]=n=>a.checklistTemplate=n),placeholder:"请选择检查表模板",style:{width:"100%"},disabled:l.readonly,filterable:""},{default:o(()=>[(f(!0),P(W,null,H(E.value,n=>(f(),V(s,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(c,{span:12},{default:o(()=>[e(i,{label:"状态",prop:"status"},{default:o(()=>[e(b,{modelValue:a.status,"onUpdate:modelValue":t[5]||(t[5]=n=>a.status=n),placeholder:"请选择状态",style:{width:"100%"},disabled:l.readonly},{default:o(()=>[e(s,{label:"启用",value:"1"}),e(s,{label:"停用",value:"0"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(i,{label:"备注",prop:"remark"},{default:o(()=>[e(N,{modelValue:a.remark,"onUpdate:modelValue":t[6]||(t[6]=n=>a.remark=n),type:"textarea",rows:3,placeholder:"请输入备注信息",readonly:l.readonly},null,8,["modelValue","readonly"])]),_:1})]),_:1},8,["model"]))]),_:1},8,["modelValue","title"])}}}),fe=se(me,[["__scopeId","data-v-f8e57c45"]]),ve=Object.freeze(Object.defineProperty({__proto__:null,default:fe},Symbol.toStringTag,{value:"Module"}));export{fe as I,ve as a,ge as d,be as g};
