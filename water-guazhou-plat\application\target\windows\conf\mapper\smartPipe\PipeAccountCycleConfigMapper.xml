<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipeAccountCycleConfigMapper">

    <select id="getListByPartitionIdIn" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeAccountCycleConfig">
        select a.*, b.id as partitionId, b.name as partitionName
        from tb_pipe_account_cycle_config a
        left join tb_pipe_partition b on b.id = a.partition_id
        where b.id in
        <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        <if test="name != null and name != ''">
            and b.name like '%' || #{name} ||'%'
        </if>
        <if test="month != null and month != ''">
            and a.month like '%' || #{month} || '%'
        </if>
        order by b.create_time desc
    </select>

</mapper>