<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.wechat.WxMessageRecordMapper">
    <sql id="Base_Column_List">
        id,
        title,
        template_id,
        template_name,
        sync_water_off,
        url,
        reason,
        remark,
        template,
        variables,
        send_user_id,
        status,
        create_time,
        tenant_id
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecord">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="template_id" property="templateId"/>
        <result column="template_name" property="templateName"/>
        <result column="sync_water_off" property="syncWaterOff"/>
        <result column="url" property="url"/>
        <result column="reason" property="reason"/>
        <result column="remark" property="remark"/>
        <result column="template" property="template"/>
        <result column="variables" property="variables"/>
        <result column="send_user_id" property="sendUserId"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <insert id="save">
        INSERT INTO wx_message_record(id,
                                      title,
                                      sync_water_off,
                                      url,
                                      reason,
                                      remark,
                                      template_name,
                                      template,
                                      variables,
                                      send_user_id,
                                      status,
                                      template_id,
                                      create_time,
                                      tenant_id)
        VALUES (#{id},
                #{title},
                #{syncWaterOff},
                #{url},
                #{reason},
                #{remark},
                (select i.name from wx_message_template i where id = #{template}),
                (select i.template from wx_message_template i where id = #{template}),
                #{variables},
                #{sendUserId},
                0,
                #{template},
                now(),
                #{tenantId})
    </insert>

    <update id="markAsComplete">
        update wx_message_record
        set status = 1
        where id = #{recordId}
    </update>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_message_record
        <where>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="templateName != null and templateName != ''">
                <!--@formatter:off-->
                and (select count(1) > 0 from wx_message_template where id = wx_message_record.template and name like '%'|| #{templateName} ||'%')
                <!--@formatter:on-->
            </if>
            <if test="status != null">
                and "status" = #{status}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="getLatestByTemplateId" resultType="org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecord">
        select * from wx_message_record where template_id = #{templateId} order by create_time desc offset 0 limit 1
    </select>
</mapper>