package org.thingsboard.server.dao.shuiwu.assets;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.UserEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsTransferEntity;
import org.thingsboard.server.dao.sql.project.ProjectRepository;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsAccountRepository;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsTransferRepository;
import org.thingsboard.server.dao.sql.user.UserRepository;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
@Service
@Slf4j
public class AssetsTransferServiceImpl implements AssetsTransferService {
    @Autowired
    private AssetsTransferRepository assetsTransferRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private AssetsAccountRepository assetsAccountRepository;
    @Autowired
    private ProjectRepository projectRepository;

    @Override
    public PageData getPage(JSONObject params) {
        int page = 0;
        int limit = 10;
        String transferNo = ""; // 转移编号
        String projectId = ""; //调往部门
        String position = ""; // 调往地址
        String director = ""; // 新负责人
        String applicantId = ""; // 申请人id
        String deviceId = "";
        Long start = 0l; // 开始时间
        Long end = System.currentTimeMillis(); // 结束时间
        String reviewerId = ""; // 审核人id
        String status = ""; // 审核结果

        if (params.getInteger("page") != null) {
            page = params.getInteger("page") - 1;
        }

        if (params.getInteger("limit") != null) {
            limit = params.getInteger("limit");
        }

        if (StringUtils.isNotBlank(params.getString("transferNo"))) {
            transferNo = params.getString("transferNo");
        }
        transferNo = "%" + transferNo + "%";


        if (StringUtils.isNotBlank(params.getString("transferNo"))) {
            transferNo = params.getString("transferNo");
        }
        transferNo = "%" + transferNo + "%";

        if (StringUtils.isNotBlank(params.getString("projectId"))) {
            projectId = params.getString("projectId");
        }
        projectId = "%" + projectId + "%";

        if (StringUtils.isNotBlank(params.getString("position"))) {
            position = params.getString("position");
        }
        position = "%" + position + "%";

        if (StringUtils.isNotBlank(params.getString("director"))) {
            director = params.getString("director");
        }
        director = "%" + director + "%";

        if (StringUtils.isNotBlank(params.getString("applicantId"))) {
            applicantId = params.getString("applicantId");
        }
        applicantId = "%" + applicantId + "%";

        if (StringUtils.isNotBlank(params.getString("reviewerId"))) {
            reviewerId = params.getString("reviewerId");
        }
        reviewerId = "%" + reviewerId + "%";

        if (StringUtils.isNotBlank(params.getString("status"))) {
            status = params.getString("status");
        }
        status = "%" + status + "%";

        if (StringUtils.isNotBlank(params.getString("deviceId"))) {
            deviceId = params.getString("deviceId");
        }
        deviceId = "%" + deviceId + "%";

        if (params.getLong("start") != null) {
            start = params.getLong("start");
        }

        if (params.getLong("end") != null) {
            end = params.getLong("end");
        }
        String tenantId = params.getString("tenantId");
        PageRequest pageRequest = new PageRequest(page, limit, new Sort(Sort.Direction.DESC, "createTime"));

        Page<AssetsTransferEntity> assetsTransferEntities = assetsTransferRepository.findAllByTransferNoLikeAndProjectIdLikeAndPositionLikeAndDirectorLikeAndApplicantIdLikeAndReviewerIdLikeAndStatusLikeAndTenantIdLikeAndDeviceIdsLikeAndCreateTimeBetween(transferNo, projectId, position, director, applicantId, reviewerId, status, tenantId, deviceId, start, end, pageRequest);

        PageData<AssetsTransferEntity> pageData = new PageData<>(assetsTransferEntities.getTotalElements(), assetsTransferEntities.getContent());

        for (AssetsTransferEntity assetsTransferEntity : pageData.getData()) {
            // 申请人
            if (StringUtils.isNotBlank(assetsTransferEntity.getApplicantId())) {
                UserEntity userEntity = userRepository.findOne(assetsTransferEntity.getApplicantId());
                if (userEntity != null) {
                    assetsTransferEntity.setApplicantName(userEntity.getFirstName());
                }
            }
            // 审核人
            if (StringUtils.isNotBlank(assetsTransferEntity.getReviewerId())) {
                UserEntity userEntity = userRepository.findOne(assetsTransferEntity.getReviewerId());
                if (userEntity != null) {
                    assetsTransferEntity.setReviewerName(userEntity.getFirstName());
                }
            }
            // 调拨设备列表
            assetsTransferEntity.setAssetsAccountList(new ArrayList<>());
            if (StringUtils.isNotBlank(assetsTransferEntity.getDeviceIds())) {
                String[] deviceIdArr = assetsTransferEntity.getDeviceIds().split(",");
                for (String deviceId1 : deviceIdArr) {
                    AssetsAccountEntity assetsAccountEntity = assetsAccountRepository.findOne(deviceId1);
                    if (assetsAccountEntity != null) {
                        assetsTransferEntity.getAssetsAccountList().add(assetsAccountEntity);
                    }
                }
            }

            // 项目中文
            assetsTransferEntity.setProjectName("");
            if (StringUtils.isBlank(assetsTransferEntity.getProjectId())) continue;

            String projectName = "";
            ProjectEntity projectEntity = projectRepository.findOne(assetsTransferEntity.getProjectId());
            while (projectEntity != null) {
                projectName = projectEntity.getName() + "/" + projectName;
                projectEntity = projectRepository.findOne(projectEntity.getParentId());
            }
            if (projectName.length() > 0) {
                projectName = projectName.substring(0, projectName.length() - 1);
            }
            assetsTransferEntity.setProjectName(projectName);
        }
        return pageData;
    }

    @Override
    public AssetsTransferEntity save(AssetsTransferEntity assetsTransferEntity) {
        if (StringUtils.isBlank(assetsTransferEntity.getId())) {
            assetsTransferEntity.setCreateTime(System.currentTimeMillis());
        }
        // 转移编号 自动生成
        if (StringUtils.isBlank(assetsTransferEntity.getTransferNo())) {
            SimpleDateFormat format = new SimpleDateFormat("yyMMddHHmmssSSS");
            String deviceNo = "ZY" + format.format(new Date());
            assetsTransferEntity.setTransferNo(deviceNo);
        }

        if (StringUtils.isBlank(assetsTransferEntity.getReviewerId())) {
            assetsTransferEntity.setReviewerId("");
        }
        if (StringUtils.isBlank(assetsTransferEntity.getStatus())) {
            assetsTransferEntity.setStatus("2");
        }
        if (StringUtils.isBlank(assetsTransferEntity.getProjectId())) {
            assetsTransferEntity.setProjectId("");
        }

        assetsTransferEntity.setUpdateTime(System.currentTimeMillis());

        assetsTransferRepository.save(assetsTransferEntity);

        return assetsTransferEntity;
    }

    @Override
    public Map reviewer(String id, String reviewerId, String status) {
        Map result = new HashMap();
        // 根据id查找
        AssetsTransferEntity transferEntity = assetsTransferRepository.findOne(id);
        if (transferEntity == null) {
            result.put("code", 500);
            result.put("msg", "该id不存在");

            return result;
        }

        transferEntity.setReviewerId(reviewerId);
        transferEntity.setStatus(status);
        transferEntity.setUpdateTime(System.currentTimeMillis());

        assetsTransferRepository.save(transferEntity);

        // 转移部门
        if ("1".equals(status) && StringUtils.isNotBlank(transferEntity.getDeviceIds())) {
            String deviceIds = transferEntity.getDeviceIds();
            String[] deviceIdArr = deviceIds.split(",");
            for (String deviceId : deviceIdArr) {
                AssetsAccountEntity assetsAccountEntity = assetsAccountRepository.findOne(deviceId);
                if (assetsAccountEntity != null) {
                    assetsAccountEntity.setProjectId(transferEntity.getProjectId());
                    assetsAccountRepository.save(assetsAccountEntity);
                }
            }
        }

        result.put("code", 200);
        result.put("msg", "操作成功");

        return result;
    }

    @Override
    public void delete(List<String> idList) {
        for (String id : idList) {
            assetsTransferRepository.delete(id);
        }
    }
}
