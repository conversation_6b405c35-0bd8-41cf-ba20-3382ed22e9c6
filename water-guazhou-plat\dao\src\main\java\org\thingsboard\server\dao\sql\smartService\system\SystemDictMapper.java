package org.thingsboard.server.dao.sql.smartService.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.system.SystemDict;

import java.util.List;
import java.util.Map;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface SystemDictMapper extends BaseMapper<SystemDict> {

    List<Map<String, String>> selectAreaList(@Param("tenantId") String tenantId);
}
