import{d as B,c as b,r as L,e8 as c,am as z,ay as A,g as _,n as h,p as a,bh as u,i as p,q as x,aw as N,ab as S,bt as U,C as V}from"./index-r0dFAfgr.js";import{a as O,e as E}from"./monitoringOverview-DvKhtmcR.js";import{w as P}from"./wing_light-VdA2SB0B.js";import{d as q,e as G}from"./headwaterMonitoring-BgK7jThW.js";import{p as Z}from"./padStart-BKfyZZDO.js";const j={class:"row-top"},H={class:"total-info"},J={class:"detail"},K={class:"count"},M={class:"value"},Q={class:"unit"},R={class:"footer"},X={class:"yesterday number"},Y={class:"yesterday"},$=["src"],tt=["title"],at={key:1,class:"empty"},et={class:"chart-box"},st=B({__name:"SCDD_zilianda",props:{size:{},waterSupply:{}},emits:["showWaterSupply"],setup(W,{emit:D}){const v=D,m=W,s=b(),g=(r,t,o)=>({tooltip:{trigger:"axis"},grid:{left:30,right:20,bottom:0,top:30,containLabel:!0},legend:{left:"right",top:"top",textStyle:{color:"#fff"}},dataZoom:[{show:!0,type:"inside",start:0,end:100,textStyle:{color:"#fff"}},{show:!1,start:0,end:100}],xAxis:{axisLabel:{color:"#B8D2FF"},axisTick:{show:!1},boundaryGap:!1,type:"category",data:r??Array.from({length:24}).map((n,l)=>Z((l+1).toString(),2,"0")+"时")},yAxis:[{name:"单位(m³)",nameTextStyle:{color:"#B8D2FF"},axisLabel:{color:"#B8D2FF"},axisLine:{show:!1},splitLine:{lineStyle:{type:"dashed",color:"#1B4E90"}},type:"value"}],series:[{type:"line",name:"昨日供水量",smooth:!0,symbol:"none",itemStyle:{color:"rgba(243, 232, 129, 1)"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(255, 195, 41, 0.3)"},{offset:1,color:"rgba(243, 232, 129, 0)"}],global:!1}},data:o??[]},{type:"line",name:"今日供水量",smooth:!0,symbol:"none",itemStyle:{color:"rgba(41, 255, 255, 1)"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(41, 255, 255, 0.6)"},{offset:1,color:"rgba(129, 197, 243, 0)"}],global:!1}},data:t??[]}]}),d=L({todayTotal:c(0),yesterdayTotal:c(0),delta:0,deltaAbs:"up",curPumpIndex:0,pumps:[],chartOption:g()}),C=r=>{d.curPumpIndex=r},k=async()=>{var r,t;try{const o=await O();d.pumps=((t=(r=o.data)==null?void 0:r.data)==null?void 0:t.map(e=>{const n=S(e.todayWaterSupply),l=S(e.yesterdayWaterSupply);return{...e,todayWaterSupply:c(n.value),yesterdayWaterSupply:c(l.value),unit:n.unit+"m³",yesterdayUnit:l.unit+"m³"}}))||[],s.value=d.pumps.find(e=>e.name===m.waterSupply.name),v("showWaterSupply",s.value),w()}catch{}},F=async()=>{const t=(await q()).data.data,o=S(t.todayWaterSupply),e=S(t.yesterdayWaterSupply);s.value={...t,todayWaterSupply:c(o.value),yesterdayWaterSupply:c(e.value),unit:o.unit+"m³",yesterdayUnit:e.unit+"m³"},v("showWaterSupply",s.value),w()},w=async()=>{var r,t,o,e,n,l;try{let i=[];if(s.value.name==="梓莲达")i=await G();else{const y=s.value.stationId;if(!y)return;i=await E(y)}const f=((o=(t=(r=i.data)==null?void 0:r.data)==null?void 0:t.yesterdayTotalFlowDataList)==null?void 0:o.map(y=>y.value))||[],I=((l=(n=(e=i.data)==null?void 0:e.data)==null?void 0:n.todayTotalFlowDataList)==null?void 0:l.map(y=>y.value))||[];d.chartOption=g(void 0,I,f)}catch{}};z(()=>m.waterSupply,()=>{m.waterSupply.name==="梓莲达"?F():k()});const T=b();return(r,t)=>{var n,l,i,f;const o=U,e=A("VChart");return _(),h("div",{class:N(["wrapper",m.size])},[a("div",j,[a("div",H,[t[3]||(t[3]=a("div",{class:"water-light"},null,-1)),t[4]||(t[4]=a("div",{class:"total-panel"},null,-1)),a("div",J,[a("div",K,[a("span",M,u(((n=p(s))==null?void 0:n.todayWaterSupply)??"--"),1),a("span",Q,u(((l=p(s))==null?void 0:l.unit)??"m³"),1)]),t[2]||(t[2]=a("div",{class:"text"},[a("span",null,"今日供水量")],-1)),a("div",R,[t[1]||(t[1]=a("span",{class:"yesterday"},"昨天",-1)),a("span",X,u(((i=p(s))==null?void 0:i.yesterdayWaterSupply)??"--"),1),a("span",Y,u(((f=p(s))==null?void 0:f.yesterdayUnit)??"m³"),1)])])]),a("div",{ref_key:"refWrapper",ref:T,class:"wings overlay-y"},[p(s)?(_(),h("div",{key:0,class:"wing-block",onClick:t[0]||(t[0]=()=>C())},[a("img",{src:p(P),alt:"泵站",class:"rotate"},null,8,$),a("div",{class:"text active",title:p(s).name},[a("span",null,u(p(s).name),1)],8,tt)])):(_(),h("div",at,"暂无泵站信息"))],512)]),x(o,{title:"供水详情",type:"simple",style:{margin:"0"}}),a("div",et,[x(e,{option:p(d).chartOption},null,8,["option"])])],2)}}}),it=V(st,[["__scopeId","data-v-bf59420c"]]);export{it as default};
