<template>
  <div class="chart">
    <VChart
      ref="refChart"
      :option="state.cxcOption"
    ></VChart>
    <div class="area area-top">
      <div class="circle">
        {{ state.level.count }}
      </div>
      <div class="info">
        <div class="title">
          一级分区
        </div>
        <div class="flow">
          流量计数<span>{{ state.level.flow }}</span>
        </div>
      </div>
    </div>
    <div class="area area-bottom">
      <div class="circle">
        {{ state.dma.count }}
      </div>
      <div class="info">
        <div class="title">
          DMA分区
        </div>
        <div class="flow">
          流量计数<span>{{ state.dma.flow }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useDetector } from '@/hooks/echarts'
import { usePartition } from '@/hooks/arcgis'

const state = reactive<{
  cxcOption: any
  level: {
    count: number
    flow: number
  }
  dma: {
    count: number
    flow: number
  }
  curType: 'dma' | 'level'
}>({
  curType: 'level',
  level: {
    count: 0,
    flow: 0
  },
  dma: {
    count: 0,
    flow: 0
  },
  cxcOption: {}
})
const partition = usePartition()
const filterTreeData = (
  tree: any,
  treeData: any[],
  treeLinks: { source: string; target: string; value: number }[]
) => {
  if (tree.path.length <= 2) {
    treeData.push({ name: tree.label })

    if (tree.path.length === 2) {
      state.level.count++
    } else {
      tree.children?.map(item => {
        const value = item.children?.length || 0
        treeLinks.push({
          source: tree.label,
          target: item.label,
          value
        })
        filterTreeData(item, treeData, treeLinks)
      })
    }
  }
}
const refChart = ref()
const detector = useDetector()
const refreshChart = () => {
  const treeData: { name: string }[] = []
  const treeLinks: { source: string; target: string; value: number }[] = []
  partition.Tree.value.map(item => filterTreeData(item, treeData, treeLinks))
  state.cxcOption = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'sankey',
        data: treeData,
        links: treeLinks,
        left: 50.0,
        top: 60.0,
        right: 100.0,
        bottom: 60.0,
        lineStyle: {
          color: 'source',
          curveness: 0.5
        },
        itemStyle: {
          color: '#1f77b4',
          borderColor: '#1f77b4'
        },
        label: {
          color: '#fff',
          fontFamily: 'Arial',
          fontSize: 10
        }
      }
    ]
  }
}
onMounted(async () => {
  const p1 = partition.getTree()
  const p2 = partition.getDevices({ page: 1, size: 9999, type: '1' })
  await Promise.all([p1, p2])
  refreshChart()
  refChart.value?.resize()

  detector.listenToMush(document.documentElement, () => {
    refChart.value?.resize()
  })
})
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
  position: relative;
  .area {
    cursor: pointer;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width: 100px;
  }
  .area-top {
    position: absolute;
    left: 20px;
    top: 20px;
  }
  .area-bottom {
    position: absolute;
    left: 20px;
    bottom: 20px;
  }
  .circle {
    width: 32px;
    height: 32px;
    background: linear-gradient(
      180deg,
      rgba(56, 234, 255, 0.67) 0%,
      rgba(56, 234, 255, 0.15) 100%
    );
    box-shadow: inset 0 4px 4px rgba(175, 255, 245, 0.46);
    border-radius: 33px;
    display: grid;
    place-items: center;
  }
  .info {
    width: calc(100% - 32px);
    padding: 0 12px;
    font-size: 12px;
    color: #fff;
    word-break: keep-all;
    line-height: 16px;
    .title {
      color: #38eaff;
      font-size: 12px;
    }
    .flow {
      span {
        color: #38eaff;
        margin-left: 12px;
      }
    }
  }
}
</style>
