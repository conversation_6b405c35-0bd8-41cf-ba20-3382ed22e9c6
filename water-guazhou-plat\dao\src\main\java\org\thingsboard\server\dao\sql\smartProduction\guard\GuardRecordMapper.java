package org.thingsboard.server.dao.sql.smartProduction.guard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRecord;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardRecordPageRequest;

@Mapper
public interface GuardRecordMapper extends BaseMapper<GuardRecord> {
    IPage<GuardRecord> findByPage(GuardRecordPageRequest request);

}
