package org.thingsboard.server.dao.repair;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.MaintenancePlanEntity;
import org.thingsboard.server.dao.model.sql.RepairPlanEntity;

import java.util.List;

public interface MaintenancePlanService {
    MaintenancePlanEntity detail(String id, User currentUser);

    PageData<MaintenancePlanEntity> findList(int page, int size, String name, User currentUser);

    MaintenancePlanEntity savePlan(MaintenancePlanEntity entity, User currentUser);

    void executePlan(MaintenancePlanEntity entity, boolean create);

    void remove(List<String> ids);

    void changeStatus(String id);

    List<MaintenancePlanEntity> findPlanByType(String type);

    void buildTriggerJob(MaintenancePlanEntity plan);
}
