import{d as N,c as v,r as h,b as y,Q as T,g as F,h as B,F as b,p,bh as L,i as c,q as w,_ as M,aq as S,X as q,C as V}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as O}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import P from"./RightDrawerMap-D5PhmGFO.js";import{b as A}from"./pipeCheck-BaGB4XFi.js";import Q from"./DetailTable-Dc-xAY7v.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const $={class:"detail-wrapper"},E={class:"left"},G={class:"table-box"},J={class:"right"},K={class:"title"},U={class:"table-box"},X=N({__name:"CrossOver",setup(j){const d=v(),g=v(),e=h({tabs:[],curType:"",layerInfos:[],layerIds:[],loading:!1,curLayerName:""}),n={queryParams:{geometry:void 0,where:"1=1"}},a=h({dataList:[],indexVisible:!0,columns:[{label:"相交个数",prop:"intersectCount",formatter:r=>{var t;return((t=r.intersectlines)==null?void 0:t.length)||0}}],pagination:{hide:!0},handleRowClick:r=>{a.currentRow=r,_()}}),_=async()=>{var r,t,i;a.currentRow&&(await((t=u.value)==null?void 0:t.refreshDetail(n.view,{layername:e.curLayerName,layerid:(r=e.layerInfos.find(m=>m.layername===e.curLayerName))==null?void 0:r.layerid,oids:a.currentRow.intersectlines||[]},{page:1})),(i=g.value)==null||i.toggleCustomDetail(!0))},C=h({group:[{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value",handleCheckChange:(r,t)=>{t&&d.value&&(d.value.dataForm.layerid=[r.value])}},{type:"btn-group",btns:[{perm:!0,text:()=>e.loading?"正在检查，过程稍长，请耐心等待！":"检查",styles:{width:"100%"},loading:()=>e.loading,click:()=>R()}]}]}],labelPosition:"top",gutter:12,defaultValue:{length:1}}),I=async()=>{var m,s,l;e.layerIds=O(n.view);const r=await q(e.layerIds);e.layerInfos=((s=(m=r.data)==null?void 0:m.result)==null?void 0:s.rows)||[];const t=(l=C.group.find(o=>o.id==="layer"))==null?void 0:l.fields[0],i=e.layerInfos.filter(o=>o.geometrytype==="esriGeometryPolyline").map(o=>({label:o.layername,value:o.layerid,data:o}));t&&(t.options=[{label:"管线类",value:-2,disabled:!0,children:i}]),d.value&&(d.value.dataForm.layerid=[i.map(o=>o.value)[0]])},u=v(),k=r=>{var t;(t=u.value)==null||t.extentTo(n.view,r.OBJECTID)},R=async()=>{var r,t,i,m,s,l;e.loading=!0,y.info("正在检查，请稍候..."),(r=g.value)==null||r.toggleCustomDetail(!0),(t=u.value)==null||t.clearTable(),a.dataList=[],a.currentRow=void 0;try{e.tabs.length=0;const o=((m=(i=d.value)==null?void 0:i.dataForm.layerid)==null?void 0:m.filter(f=>f>=0))||[];if(!o.length)y.warning("请选择一个图层");else{e.curLayerName=((s=e.layerInfos.find(x=>x.layerid===o[0]))==null?void 0:s.layername)||"";const f=await A({layer:e.curLayerName});(l=f.data.result)!=null&&l.length||y.success("没有相关内容"),a.dataList=f.data.result||[],a.currentRow=a.dataList[0],_()}}catch(o){console.log(o),y.error(o.message)}e.loading=!1},D=r=>{n.view=r,I()};return T(()=>{var r,t,i;(r=n.graphicsLayer)==null||r.removeAll(),(t=n.drawAction)==null||t.destroy(),(i=n.drawer)==null||i.destroy()}),(r,t)=>{const i=M,m=S;return F(),B(P,{ref_key:"refMap",ref:g,title:"相交检查","detail-max-min":!0,"full-content":!0,onMapLoaded:D,onDetailRefreshed:t[0]||(t[0]=s=>c(e).loading=!1)},{"detail-header":b(()=>[p("span",null,"相交检查结果"+L(c(e).curLayerName&&" - "+c(e).curLayerName),1)]),"detail-default":b(()=>{var s,l;return[p("div",$,[p("div",E,[t[1]||(t[1]=p("div",{class:"title"}," 相交列表 ",-1)),p("div",G,[w(m,{config:c(a)},null,8,["config"])])]),p("div",J,[p("div",K," 相交要素详情 "+L((s=c(a).currentRow)!=null&&s.id?" - 序号"+((l=c(a).currentRow)==null?void 0:l.id):""),1),p("div",U,[w(Q,{ref_key:"refDetailTable",ref:u,onRowClick:k,onRefreshData:_},null,512)])])])]}),default:b(()=>[w(i,{ref_key:"refForm",ref:d,config:c(C)},null,8,["config"])]),_:1},512)}}}),Zr=V(X,[["__scopeId","data-v-f76cbf10"]]);export{Zr as default};
