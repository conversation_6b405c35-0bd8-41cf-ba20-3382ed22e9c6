<!-- 漏损工单 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    />
    <NewOrder
      ref="refDialog"
      :need-partition="true"
      :default-values="{ isDirectDispatch: true }"
      @success="refreshData"
    ></NewOrder>
    <SLDrawer
      ref="refdetail"
      :config="detailConfig"
    >
      <detail :id="selectedId"></detail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { ICardSearchIns, ICardTableIns, ISLDrawerIns } from '@/components/type'
// import router from '@/router'
import OrderStepTagsVue from '@/views/workorder/components/OrderStepTags.vue'
import detail from '@/views/workorder/components/detail.vue'
import {
  EWorkOrderStatus,
  formatEmergencyLevelColor,
  // formatWorkOrderDealLevel,
  formatWorkOrderLevel,
  getEmergencyLevelOpetions,
  getFromOptions,
  WorkOrderStatusRecord
} from '@/views/workorder/config'
import { formatterDate, formatterDateTime, formatterMonth, formatterYear } from '@/utils/GlobalHelper'
import { GetLossWorkOrderList } from '@/api/mapservice/dma'
import NewOrder from '../components/NewOrder.vue'
import { formatDate } from '@/utils/DateFormatter'

const refDialog = ref<InstanceType<typeof NewOrder>>()
const refSearch = ref<ICardSearchIns>()
const refTable = ref<ICardTableIns>()
const refdetail = ref<ISLDrawerIns>()

// 明细弹框
const detailConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  cancel: false,
  className: 'lightColor',
  group: []
})
const selectedId = ref<string>('')
const handleHidden = (params, query, formItem) => (formItem.hidden = params.type !== formItem.field)
const SearchConfig = reactive<ISearch>({
  filters: [
    // {
    //   field: 'title',
    //   label: '标题',
    //   type: 'input',
    //   onChange: () => refreshData()
    // },
    {
      field: 'status',
      label: '工单状态',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '待处理', value: EWorkOrderStatus.待接单 },
        { label: '处理中', value: 'PIPE_PROCESSING' },
        { label: '已处理', value: 'PIPE_COMPLETE' },
        { label: '已终止', value: EWorkOrderStatus.已终止 }
      ],
      clearable: false
    },
    {
      type: 'select',
      label: '选择方式',
      field: 'type',
      options: [
        { label: '按年', value: 'year' },
        { label: '按月', value: 'month' },
        { label: '按时间段', value: 'day' }
      ],
      clearable: false
    },
    {
      field: 'day',
      label: '发起时间',
      type: 'daterange',
      handleHidden,
      clearable: false
    },
    {
      field: 'month',
      label: '发起时间',
      type: 'month',
      handleHidden,
      clearable: false
    },
    {
      field: 'year',
      label: '发起时间',
      type: 'year',
      handleHidden,
      clearable: false
    },
    {
      field: 'organizerName',
      label: '发起人',
      type: 'input',
      clearable: false
    },
    {
      field: 'partitionName',
      label: '分区名称',
      type: 'input',
      clearable: false
    },
    {
      field: 'processUserName',
      label: '接收人',
      type: 'input',
      clearable: false
    },
    {
      field: 'receiveDepartmentName',
      label: '部门名称',
      type: 'input',
      clearable: false
    },
    {
      field: 'source',
      label: '来源',
      type: 'select',
      options: getFromOptions()
    },
    {
      field: 'level',
      label: '紧急程度',
      type: 'select',
      options: getEmergencyLevelOpetions()
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          iconifyIcon: 'ep:refresh',
          click: () => resetForm()
        },
        {
          perm: true,
          type: 'success',
          text: '新增工单',
          iconifyIcon: 'ep:download',
          click: () => {
            refDialog.value?.openDialog()
          }
        }
      ]
    }
  ],
  handleSearch: () => refreshData(),
  defaultParams: {
    status: '',
    type: 'month',
    day: [moment().subtract(1, 'M').format(formatterDate), moment().format(formatterDate)],
    month: moment().format(formatterMonth),
    year: moment().format(formatterYear)
  }
})
const TableConfig = reactive<ICardTable>({
  expandable: true,
  expandComponent: shallowRef(OrderStepTagsVue),
  columns: [
    { minWidth: 160, label: '标题', prop: 'title' },
    { minWidth: 160, label: '分区名称', prop: 'partitionName' },
    { minWidth: 160, label: '事件描述', prop: 'remark' },
    { minWidth: 160, label: '添加人', prop: 'organizerName' },
    { minWidth: 160, label: '接收人', prop: 'processUserName' },
    { minWidth: 160, label: '部门', prop: 'receiveDepartmentName' },
    {
      minWidth: 160,
      label: '处理级别',
      prop: 'processLevelLabel'
      // formatter(row, value) {
      //   return formatWorkOrderDealLevel(value)
      // }
    },
    {
      minWidth: 160,
      label: '紧急程度',
      prop: 'level',
      iconifyIcon: 'ep:clock',
      formatter: (row, val) => formatWorkOrderLevel(val),
      cellStyle: row => {
        return {
          color: formatEmergencyLevelColor(row.level)
        }
      }
    },
    {
      minWidth: 160,
      label: '添加日期',
      prop: 'createTime',
      formatter(row, value) {
        return formatDate(value, formatterDateTime)
      }
    },
    {
      minWidth: 160,
      label: '预计完成日期',
      prop: 'estimatedFinishTime',
      formatter(row, value) {
        return formatDate(value, formatterDateTime)
      }
    },
    {
      minWidth: 160,
      label: '处理进度',
      prop: 'status',
      formatter(row, val) {
        return WorkOrderStatusRecord[val] || val
      }
    }
  ],
  defaultExpandAll: true,
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      text: '详情',
      isTextBtn: true,
      click: row => handleDetail(row)
    }
  ]
})
const handleDetail = (row: any) => {
  selectedId.value = row.id || ''
  detailConfig.title = row.serialNo
  refdetail.value?.openDrawer()
}
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const params: any = {
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit || 20,
      ...query,
      start: query.day?.[0],
      end: query.day?.[1]
    }
    delete params.day
    const res = await GetLossWorkOrderList(params)
    const data = res.data?.data
    TableConfig.dataList = data.data
    TableConfig.pagination.total = data.total
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
const resetForm = () => {
  refSearch.value?.resetForm()
  refreshData()
}

onMounted(() => {
  refreshData()
})
// const
</script>
<style lang="scss" scoped></style>
