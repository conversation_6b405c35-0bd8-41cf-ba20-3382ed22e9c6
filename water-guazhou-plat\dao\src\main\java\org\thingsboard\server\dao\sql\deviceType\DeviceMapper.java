package org.thingsboard.server.dao.sql.deviceType;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.deviceManage.Device;
import org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse;

import java.util.List;

@Mapper
public interface DeviceMapper extends BaseMapper<Device> {

    List<Device> getList(@Param("typeIdList") List<String> typeIdList, @Param("serialId") String serialId, @Param("name") String name, @Param("model") String model, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("typeIdList") List<String> typeIdList, @Param("serialId") String serialId, @Param("name") String name, @Param("model") String model, @Param("tenantId") String tenantId);

    DeviceInfoResponse getInfoBySerialId(@Param("serialId") String serialId, @Param("tenantId") String tenantId);

    DeviceInfoResponse getInfoByDeviceLabelCode(@Param("labelCode") String labelCode, @Param("tenantId") String tenantId);

    @Select("select * from m_device where serial_id = #{serialId} and tenant_id = #{tenantId}")
    Device selectBySerialId(@Param("serialId") String serialId, @Param("tenantId") String tenantId);

    List<JSONObject> getFlowList();
}
