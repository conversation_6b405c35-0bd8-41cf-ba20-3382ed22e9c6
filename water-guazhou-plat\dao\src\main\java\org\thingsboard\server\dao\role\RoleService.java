/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.role;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.RoleId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.role.Role;

import java.util.List;

public interface RoleService {
    /**
     * 新增/更新 角色
     * @param role
     * @return
     */
    Role saveRole(Role role);

    boolean deleteRole(TenantId tenantId, RoleId roleId);

    /**
     * 获取当前用户的角色集合
     * @param tenantId
     * @return
     */
    List<Role> findByTenantId(TenantId tenantId);

    /**
     * 分配菜单给角色
     * @param menuIds
     * @param roleId
     */
    void assignMenuToRole(List<String> menuIds, RoleId roleId);

    /**
     * 分配角色给用户
     * @param userId
     * @param roleId
     */
    void assignRoleToUser(UserId userId, RoleId roleId) throws ThingsboardException;

    /**
     * 获取指定角色已选择的菜单id
     * @param roleId
     * @return
     */
    List<String> getTreeByRoleId(RoleId roleId);

    String getRoleIdByUserId(UserId userId);

    Role findById(RoleId roleId);

    void deleteUserRoleByUserId(CustomerId customerId);

    PageData<Role> findList(Integer page, Integer size, String name, String tenantId);

    List<User> getUserListByRole(String roleId);

    void assignMenuToRole(List<String> menuIds, RoleId roleId, String tenantApplicationId);

    List<String> getTreeByRoleId(RoleId roleId, String tenantApplicationId);

    List<String> getRoleTenantApplicationList(String strRoleId);

    void assignTenantApplicationToRole(String roleId, String tenantApplicationId);

    List<String> getRoleIdsByUserId(UserId userId);
}
