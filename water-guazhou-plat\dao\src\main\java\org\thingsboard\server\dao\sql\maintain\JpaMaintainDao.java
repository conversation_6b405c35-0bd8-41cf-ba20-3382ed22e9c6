/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.maintain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.maintain.Maintain;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.maintain.MaintainDao;
import org.thingsboard.server.dao.model.sql.MaintainEntity;
import org.thingsboard.server.dao.repair.RepairDao;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@Component
@SqlDao
@Slf4j
public class JpaMaintainDao extends JpaAbstractDao<MaintainEntity, Maintain> implements MaintainDao {

    @Autowired
    private MaintainRepository maintainRepository;

    @Override
    protected Class<MaintainEntity> getEntityClass() {
        return MaintainEntity.class;
    }

    @Override
    protected CrudRepository<MaintainEntity, String> getCrudRepository() {
        return maintainRepository;
    }


    @Override
    public List<Maintain> findByTenantId(TenantId tenantId) {
        return DaoUtil.convertDataList(maintainRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId())));
    }

    @Override
    public List<Maintain> findByProjectId(String projectId) {
        return DaoUtil.convertDataList(maintainRepository.findByProjectId(projectId));
    }
}
