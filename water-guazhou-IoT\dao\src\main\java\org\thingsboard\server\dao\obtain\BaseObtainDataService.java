/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.obtain;

import org.thingsboard.server.common.data.constantsAttribute.LoocalMap;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;
import org.thingsboard.server.common.data.virtual.Virtual;

import java.math.BigDecimal;
import java.util.*;

public interface BaseObtainDataService {
    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getAssetData(List<String> assetId, long start, long end, String type, String name, String readmeterTime, TenantId tenantId) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String,LinkedHashMap<String,BigDecimal>>> getTimeSharing(String assetId, long start, long end, String type, String name, String readmeterTime, String timeSharingName, TenantId tenantId) throws ThingsboardException;

    List<ResponseTs> getDeviceDataFromOpenTSDB(long start, long end, List<String> formula, TenantId tenantId, String timeLimit) throws ThingsboardException;

    List<ResponseTs> getLastDataFromOpenTSDB(List<String> formula, TenantId tenantId) throws ThingsboardException;

    Object convertFormula(long start, long end, List<String> formula, TenantId tenantId) throws ThingsboardException;

    PropAttribute getProp(TenantId tenantId,String deviceId, String propName) throws ThingsboardException;

    LinkedHashMap<String, BigDecimal> groupDataByType(ArrayList<ArrayList<LoocalMap>> data, String statType, String readmeterTime, String type, long start, long end) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertResponseTsByVirtuals(TenantId tenantId,List<ResponseTs> responseTs, List<String> virtuals, String type, String readmeterTime, List<Virtual> list, long start, long end) throws ThingsboardException;

    LinkedHashMap<String, HashMap<String, BigDecimal>> getAssetDataAndCost(String assetIds, long start, long end, String type, String name, String readmeterTime, TenantId tenantId) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getDeviceData(List<String> attributes, long start, long end, String type, String readmeterTime, TenantId tenantId) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String, String>> getDeviceLastAllData(List<String> attributes, TenantId tenantId) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertResponseTsByFormula(TenantId tenantId,List<ResponseTs> responseTs, List<String> formula, String type, long start, long end,boolean useLastData) throws ThingsboardException;

    Object getEnergyData(List<String> assetIds, long start, long end, String type, TenantId tenantId) throws ThingsboardException;

    Optional<Object> getBaseEnergyData(String assetId, String type, TenantId tenantId) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> getAllEnergyByTimeSharing(String assetId, long start, long end, String type, String readmeterTime, String timeSharingName, TenantId tenantId) throws ThingsboardException;

    Object getAssetAllEnergy(List<String> assetId, long start, long end, String type, String name, String readmeterTime, TenantId tenantId) throws ThingsboardException;

    Object getAssetAllEnergyAndCost(List<String> assetId, long start, long end, String type, String name, String readmeterTime, TenantId tenantId) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertResponseTsByFormula(TenantId tenantId, List<String> formula,long start) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertByFormula
            (TenantId tenantId, List<ResponseTs> responseTs, String propType, String readmeterTime, String type, long start, long end) throws
            ThingsboardException;


    List<ResponseTs> getFirstDataFromOpenTSDB(long start, long end, String formula, TenantId
            tenantId, String timeLimit) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getDeviceOriginalData(List<String> sourceIds, long startTimeTime, long endTimeTime, String readmeterTime, TenantId tenantId);

    /**
     * 查询数据且累计值不进行差值求和
     */
    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getDeviceDataByOriginal(List<String> attributes, long start, long end, String type, String readmeterTime, TenantId tenantId) throws ThingsboardException;
}
