package org.thingsboard.server.dao.smartService.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartService.system.SystemDict;
import org.thingsboard.server.dao.sql.smartService.system.SystemDictMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class SystemDictServiceImpl implements SystemDictService {
    @Autowired
    private SystemDictMapper systemDictMapper;

    @Override
    public List<SystemDict> getList(String pid, String tenantId) {
        QueryWrapper<SystemDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        if (StringUtils.isBlank(pid)) {
            queryWrapper.isNull("pid");
        } else {
            queryWrapper.eq("pid", pid);
        }
        queryWrapper.orderByAsc("order_num");
        List<SystemDict> supplierEntities = systemDictMapper.selectList(queryWrapper);

        return supplierEntities;

    }

    @Override
    @Transactional
    public SystemDict save(SystemDict systemDict) {
        systemDict.setUpdateTime(new Date());
        if (StringUtils.isBlank(systemDict.getId())) {
            systemDict.setCreateTime(new Date());
            systemDictMapper.insert(systemDict);
        } else {
            systemDictMapper.updateById(systemDict);
        }

        return systemDict;
    }

    @Override
    @Transactional
    public IstarResponse delete(List<String> ids) {
        systemDictMapper.deleteBatchIds(ids);
        QueryWrapper<SystemDict> wrapper = new QueryWrapper<>();
        wrapper.in("pid", ids);
        return IstarResponse.ok("删除成功");
    }

    @Override
    public List getTree(String tenantId) {
        QueryWrapper<SystemDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("order_num");
        List<SystemDict> supplierEntities = systemDictMapper.selectList(queryWrapper);

        // 获取根节点
        List<SystemDict> root = supplierEntities.stream().filter(a -> StringUtils.isBlank(a.getPid())).collect(Collectors.toList());

        for (SystemDict systemDict : root) {
            for (SystemDict s : supplierEntities) {
                if (systemDict.getId().equals(s.getPid()) && tenantId.equals(s.getTenantId())) {
                    systemDict.getChildren().add(s);
                }
            }
        }
        return root;
    }

    @Override
    public boolean checkCode(SystemDict systemDict, String tenantId) {
        QueryWrapper<SystemDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", systemDict.getCode());
        queryWrapper.eq("tenant_id", tenantId);
        List<SystemDict> systemDicts = systemDictMapper.selectList(queryWrapper);
        for (SystemDict systemDict1 : systemDicts) {
            if (!systemDict1.getId().equals(systemDict.getId())) {
                return false;
            }
        }
        return true;
    }
}
