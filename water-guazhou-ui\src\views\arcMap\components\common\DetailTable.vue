<template>
  <div class="detail-table">
    <FormTable :config="TableConfig_Detail"></FormTable>
  </div>
</template>
<script lang="ts" setup>
import Graphic from "@arcgis/core/Graphic.js";
import Point from "@arcgis/core/geometry/Point";
import Multipoint from "@arcgis/core/geometry/Multipoint";
import Polyline from "@arcgis/core/geometry/Polyline";
import Polygon from "@arcgis/core/geometry/Polygon";
import { formatDate } from '@/utils/DateFormatter'
import { formatterDate } from '@/utils/GlobalHelper'
import {
  getGraphicLayer,
  gotoAndHighLight,
  setSymbol
} from '@/utils/MapHelper'
import { SLMessage } from '@/utils/Message'

const view = inject('view') as __esri.MapView | undefined
const emit = defineEmits(['row-click', 'refreshData'])
const props = defineProps<{
  operations?: IButton[]
}>()
const staticState: {
  hilightLayer?: __esri.GraphicsLayer
  tabFeatures: __esri.Graphic[]
} = {
  tabFeatures: []
}
const TableConfig_Detail = reactive<ITable>({
  dataList: [],
  columns: [{ label: '序号', prop: 'index' }],
  handleRowClick: async row => {
    emit('row-click', row)
  },
  operations: props.operations,
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig_Detail.pagination.page = page
      TableConfig_Detail.pagination.limit = size
      emit('refreshData')
    }
  }
})
const refreshDetail = async (
  viewIns?: __esri.MapView,
  layer?: {
    layername: string
    layerid: number
    where?: string
    oids: any[]
    isGeoServer?: boolean // 添加GeoServer模式标记
  },
  options?: {
    queryParams?: {
      where?: string
      geometry?: any
    }
    /** 重置table页 */
    page?: number
    allAttributes?: boolean
  }
) => {
  try {
    staticState.hilightLayer = getGraphicLayer(viewIns || view, {
      id: 'pipe-detail',
      title: '详情展示'
    })
    if (options?.page !== undefined) {
      TableConfig_Detail.pagination.page = options.page || 1
    }
    TableConfig_Detail.loading = true
    const tab = layer?.layername

    if (!tab) {
      TableConfig_Detail.dataList = []
      TableConfig_Detail.pagination.total = 0
      TableConfig_Detail.loading = false
      staticState.hilightLayer?.removeAll()
      return
    }

    // 获取所有的要素
    const allFeatures = layer?.oids || [];
    TableConfig_Detail.pagination.total = allFeatures.length || 0;

    // 分页处理
    const currentPage = TableConfig_Detail.pagination.page || 1;
    const pageSize = TableConfig_Detail.pagination.limit || 20;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, allFeatures.length);
    const pageFeatures = allFeatures.slice(startIndex, endIndex);

    const data: any[] = [];
    const columns: IFormTableColumn[] = [];
    debugger
    if (pageFeatures.length > 0) {
      // 处理列信息
      // 获取第一个要素的属性作为列定义
      const firstFeature = pageFeatures[0];
      if (firstFeature && firstFeature.properties) {
        // 从属性中提取列
        Object.keys(firstFeature.properties).forEach(key => {
          const conf: IFormTableColumn = {
            label: key, // 使用属性名作为标签
            prop: key,
            minWidth: 160
          };

          // 判断属性类型并设置格式化器
          // const value = firstFeature.properties[key];
          // if (value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))) {
          //   conf.formatter = row => {
          //     return formatDate(row[key], formatterDate);
          //   };
          // } else if (value === 0 || value === 1) {
          //   conf.formatter = row => {
          //     return row[key] === 1 ? '是' : '否';
          //   };
          // }

          columns.push(conf);
        });
      }

      // 处理数据
      pageFeatures.forEach(feature => {
        if (feature.properties) {
          // 将GeoJSON的properties作为表格数据
          data.push(feature.properties);
        }
      });

      // 设置列
      TableConfig_Detail.columns = columns;
    }

    // 设置数据
    TableConfig_Detail.dataList = data;

    // 处理高亮显示
    staticState.hilightLayer?.removeAll();
    staticState.tabFeatures = allFeatures;

    // 将GeoJSON要素转换为ArcGIS要素并添加到高亮图层
    try {
      const arcgisGraphics = [];

      for (const feature of allFeatures) {
        if (feature.geometry) {
          try {
            // 使用geoJsonToArcGIS函数转换几何对象
            const arcgisGeometry = geoJsonToArcGIS(feature.geometry);

            if (arcgisGeometry) {
              // 创建图形对象
              try {
                // 首先确保我们有正确的symbol
                const symbolType = arcgisGeometry.type;
                const symbol = setSymbol(symbolType);

                if (!symbol) {
                  console.warn('无法为类型创建符号:', symbolType);
                  continue;
                }

                // 创建图形对象
                const graphic = new Graphic({
                  geometry: arcgisGeometry,
                  attributes: feature.properties,
                  symbol: symbol
                });

                // 将symbol存储在feature上，以便在extentTo方法中使用
                feature.symbol = symbol;

                arcgisGraphics.push(graphic);
              } catch (error) {
                console.error('创建图形对象时出错:', error);
              }
            }
          } catch (error) {
            console.error('处理要素几何时出错:', error);
          }
        }
      }

      if (arcgisGraphics.length > 0) {
        console.log('添加高亮图层要素:', arcgisGraphics.length);
        staticState.hilightLayer?.addMany(arcgisGraphics);
      } else {
        console.warn('没有可用的高亮图层要素');
      }
    } catch (error) {
      console.error('GeoServer模式下添加高亮图层时出错:', error);
    }

  } catch (error) {
    console.dir(error)
    SLMessage.error('查询失败')
  }
  TableConfig_Detail.loading = false
}
const extentTo = async (viewIns = view, oid?: string) => {
  if (!viewIns) return

  let feature: any;
  feature = staticState.tabFeatures.find(item => {
    // 如果是GeoJSON要素，属性在properties中
    return (item as any).properties && (item as any).properties.OBJECTID === oid;
  });

  if (feature) {
    try {
      // 转换几何对象
      let arcgisGeometry = geoJsonToArcGIS(feature.geometry);
      if (!arcgisGeometry) {
        console.error('无法创建ArcGIS几何对象:', feature.geometry);
        return;
      }
      debugger
      // 创建一个新的Graphic对象
      const newFeature = new Graphic({
        geometry: arcgisGeometry,
        // attributes: (feature as any).properties,
        symbol: setSymbol(arcgisGeometry.type)
      });

      // 使用gotoAndHighLight函数高亮显示要素
      await gotoAndHighLight(viewIns, newFeature);

    } catch (error) {
      console.error('高亮显示要素时出错:', error);
    }
  }
}
const geoJsonToArcGIS = (geoJson) => {
  switch (geoJson.type) {
    case 'Point':
      return new Point({
        x: geoJson.coordinates[0],
        y: geoJson.coordinates[1],
        spatialReference: { wkid: 3857 } // 你可以设置适当的 spatialReference
      });
    case 'MultiPoint':
      return new Multipoint({
        points: geoJson.coordinates,
        spatialReference: { wkid: 3857 }
      });
    case 'LineString':
      return new Polyline({
        paths: [geoJson.coordinates],
        spatialReference: { wkid: 3857 }
      });
    case 'MultiLineString':
      return new Polyline({
        paths: geoJson.coordinates,
        spatialReference: { wkid: 3857 }
      });
    case 'Polygon':
      return new Polygon({
        rings: geoJson.coordinates,
        spatialReference: { wkid: 3857 }
      });
    case 'MultiPolygon':
      return new Polygon({
        // 将 MultiPolygon 转换为单个 Polygon 对象的 rings 数组
        rings: geoJson.coordinates.reduce((rings, polygon) => rings.concat(polygon), []),
        spatialReference: { wkid: 3857 }
      });
    default:
      console.error('Unsupported GeoJSON type:', geoJson.type);
      return null;
  }
}
const clearTable = () => {
  TableConfig_Detail.dataList = []
  TableConfig_Detail.pagination.total = 0
}
defineExpose({
  extentTo,
  refreshDetail,
  clearTable,
  staticState,
  TableConfig_Detail
})
</script>
<style lang="scss" scoped>
.detail-table {
  height: 100%;
}
</style>
<style lang="scss"></style>
