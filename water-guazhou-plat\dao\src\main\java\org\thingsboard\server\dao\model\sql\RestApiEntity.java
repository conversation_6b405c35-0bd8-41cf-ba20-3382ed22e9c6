package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2020/2/26 10:51
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.RESTAPI_TABLE)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class RestApiEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;
    /**
     * 数据源名称
     */
    @Column(name = ModelConstants.DATASOURCE_NAME)
    private String name;

    /**
     * 数据源排序（默认-1）
     */
    @Column(name = ModelConstants.DATASOURCE_ORDER)
    private long order = -1;
    /**
     * 备注
     */
    @Column(name = ModelConstants.DATASOURCE_FORMAT)
    private String format;

    /**
     * 最后一次更新时间
     */
    @Column(name = ModelConstants.DATASOURCE_UPDATE_TIME)
    private Long updateTime;

    @Column(name = ModelConstants.CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.ADDITIONAL_INFO_PROPERTY)
    private String additionalInfo;


    /* ------------------------  API数据源 ----------------------------*/

    /**
     * API数据源-URL
     */
    @Column(name = ModelConstants.DATASOURCE_API_URL)
    private String URL;
    /**
     * API数据源-方法
     */
    @Column(name = ModelConstants.DATASOURCE_API_METHOD)
    private String method;
    /**
     * API数据源-传递参数列表
     */
    @Column(name = ModelConstants.DATASOURCE_API_PARAMS)
    private String params;

    /**
     * API数据源-传递参数列表
     */
    @Column(name = ModelConstants.DATASOURCE_API_HEADERS)
    private String headers;
    /**
     * API数据源-解析数据名
     */
    @Column(name = ModelConstants.DATASOURCE_PARSING_ATTRIBUTE)
    private String parsingAttribute;


    @Column(name = ModelConstants.DATASOURCE_STATISTICS_FREQUENCY)
    private String frequency;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.PROJECT_RELATION_PROJECT_ID)
    private String projectId;

    @Transient
    private String nodeType = "DataSource";

}
