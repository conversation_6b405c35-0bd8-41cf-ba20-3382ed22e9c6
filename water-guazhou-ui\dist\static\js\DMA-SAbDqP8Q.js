import{z as v,d as K,cN as X,c as Y,r as A,s as B,j as S,o as Z,bB as tt,Q as et,g as h,n as k,h as G,i as C,aB as $,aJ as ot,an as I,d3 as it,q as rt,_ as at}from"./index-r0dFAfgr.js";import{v as nt,g as N,m as st}from"./MapView-DaoQedLH.js";import{w as O}from"./Point-WxyopZva.js";import{s as pt}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as z}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{b as lt,a as ft,e as x}from"./ViewHelper-BGCZjxXH.js";import ct from"./CountCard-DjoeFppY.js";import mt from"./CardGroup-BC7j8bYt.js";import dt from"./ListWindow-WS05QqV0.js";import{g as b}from"./URLHelper-B9aplt5w.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./fieldconfig-Bk3o1wi7.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./PopLayout-BP55MvL7.js";const ut=p=>{const u=[],d=[],a=[],i=(p==null?void 0:p.map(y=>(u.push(y.supply),d.push(y.loss),a.push(y.lossRate),y.date)))||[];return{backgroundColor:"transparent",tooltip:{trigger:"axis"},legend:{show:!0,textStyle:{color:"#fff"},icon:"rect",top:0,left:"center",itemWidth:15,itemHeight:3},xAxis:{type:"category",data:i||[],show:!0,color:"#fff",axisLabel:{textStyle:{color:"#ffffff"}},splitLine:{show:!1},splitArea:{show:!1}},yAxis:[{name:"m³",type:"value",color:"#ffffff",axisLabel:{textStyle:{color:"#ffffff"}},splitArea:{show:!1},splitLine:{lineStyle:{color:"#ffffff",opacity:.2,type:"dashed"}}},{name:"%",type:"value",color:"#ffffff",axisLabel:{textStyle:{color:"#ffffff"}},splitArea:{show:!1},splitLine:{lineStyle:{color:"#ffffff",opacity:.2,type:"dashed"}}}],grid:{left:70,right:30,top:50,bottom:30},series:[{type:"bar",barMaxWidth:5,stack:"bar",name:"供水量",itemStyle:{color:"#54a8ff"},yAxisIndex:0,data:u||[]},{type:"bar",barMaxWidth:5,stack:"bar",name:"漏水量",itemStyle:{color:"#80e539"},yAxisIndex:0,data:d||[]},{type:"line",symbol:"none",name:"漏失率",yAxisIndex:1,itemStyle:{color:"#be9b41"},data:a||[]}]}},yt=p=>{const u=[],d=[],a=(p==null?void 0:p.map(l=>(u.push(l.lossRate),d.push(l.differenceRate),l.date)))||[];return{backgroundColor:"transparent",tooltip:{trigger:"axis"},legend:{show:!0,textStyle:{color:"#fff"},icon:"rect",top:0,left:"center",itemWidth:15,itemHeight:3},yAxis:{name:"%",type:"value",show:!0,color:"#fff",axisLabel:{textStyle:{color:"#ffffff"}},splitLine:{lineStyle:{color:"#ffffff",opacity:.2,type:"dashed"}},splitArea:{show:!1}},xAxis:{data:a||[],type:"category",boundaryGap:!1,scale:!0,color:"#ffffff",axisLabel:{textStyle:{color:"#ffffff"}},splitLine:{show:!1},splitArea:{show:!1}},grid:{left:70,right:30,top:50,bottom:30},series:[{type:"line",barWidth:10,symbol:"none",smooth:!1,name:"漏损率",data:u||[],itemStyle:{color:"#be9b41"}},{type:"line",barWidth:10,symbol:"none",smooth:!1,name:"差销差",data:d||[],itemStyle:{color:"#cc3f40"}}]}},ht=p=>{const u=[],d=(p==null?void 0:p.map(i=>(u.push([i.start,i.end,i.min,i.max]),i.date)))||[];return{backgroundColor:"transparent",color:["#36E5E4","#E33737","#80E539","#E2E033","#EA54FF"],xAxis:{type:"category",data:d,show:!0,color:"#fff",axisLabel:{textStyle:{color:"#ffffff"}},splitLine:{show:!1},splitArea:{show:!1}},yAxis:{name:"MPa",type:"value",color:"#ffffff",axisLabel:{textStyle:{color:"#ffffff"}},splitArea:{show:!1},splitLine:{lineStyle:{color:"#ffffff",opacity:.2,type:"dashed"}}},grid:{left:50,right:30,top:30,bottom:30},series:{type:"candlestick",data:u||[],itemStyle:{color:"#54a8ff",borderColor:"#54a8ff",borderColor0:"#54a8ff",color0:"##54a8ff"}}}},gt=()=>v({url:"/api/dma/partition/list",method:"get"}),wt=p=>v({url:`/api/dma/partition/parent/${p}`,method:"get"}),xt=p=>v({url:`/api/dma/partition/child/${p}`,method:"get"}),bt=p=>v({url:"/api/dma/analysis/dashboard?partitionId="+p,method:"get"}),vt={key:0,class:"infowindow-container"},Oe=K({__name:"DMA",props:{view:{},telport:{}},setup(p){const{proxy:u}=X(),d=Y(),a=p,i=A({supply:[{prop:"supplyTotal",title:"供水量",count:0,unit:"m³",img:b("供水量.png")},{prop:"saleTotal",title:"售水量",count:0,unit:"m³",img:b("供水量 2.png")},{prop:"difference",title:"产销差",count:0,unit:"m³",img:b("供水量 3.png")},{prop:"differenceRate",title:"产销差率",count:0,unit:"%",img:b("供水量 4.png")}],areaScopes:[],marks:[],setToViewingDetail:!1,areaTree:[],parentExtentTo:!1}),l={},y=A({dataList:[],indexVisible:!0,columns:[{label:"区域名称",prop:"partitionName"},{label:"流量(m³/h)",prop:"nightMinFlow"},{label:"上报时间",prop:"createTime"}],pagination:{hide:!0}}),g=A({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"总览"},fields:[{type:"component",style:{width:"100%"},field:"overview",component:B(ct)}]},{fieldset:{desc:"供水情况"},fields:[{type:"component",field:"supply",style:{width:"100%"},component:B(mt)}]},{id:"lossRate",fieldset:{desc:"漏失率统计"},fields:[{type:"vchart",style:()=>({width:"100%",height:"200px",backgroundColor:S().isDark?"#1A293C":"#c7c7c7"}),option:null}]},{id:"lossRateAndProSale",fieldset:{desc:"漏损率和产销差趋势"},fields:[{type:"vchart",option:null,style:()=>({width:"100%",height:"200px",backgroundColor:S().isDark?"#1A293C":"#c7c7c7"})}]},{fieldset:{desc:"夜间最小流量"},fields:[{type:"table",config:y}]},{id:"wholeDayLiuliangPercent",fieldset:{desc:"全天流量占比"},fields:[{type:"vchart",style:()=>({width:"100%",height:"200px",backgroundColor:S().isDark?"#1A293C":"#c7c7c7"}),option:null}]}],defaultValue:{supply:i.supply}}),w=async t=>{const e=await bt(t);D(e.data)},D=t=>{const e=(t==null?void 0:t.supply)||{};i.supply.map(o=>e[o.prop]),d.value&&(d.value.dataForm={supply:i.supply,overview:(t==null?void 0:t.overview)||{}}),y.dataList=(t==null?void 0:t.nightMinFlow)||[],Q(t)},H=async()=>{var t,e;a.view&&(l.graphicsLayer=z(a.view,{id:"dma-partition",title:"DMA分区"}),l.textLayer=z(a.view,{id:"dma-partition-poi",title:"DMA分区注记"}),(t=l.graphicsLayer)==null||t.removeAll(),(e=l.textLayer)==null||e.removeAll(),i.marks=[],j(),lt(a.view,async o=>{var s,r,m,f;if(!o.results.length)return;const n=o.results[0];if(n.type==="graphic"){const c=(s=n.graphic)==null?void 0:s.attributes;c.type==="dma_text"?(w((r=c.result)==null?void 0:r.id),await x(a.view,n.graphic.geometry.extent,!0),J(c.result)):c.type==="dma_area"&&(w((m=c.result)==null?void 0:m.id),await L((f=c.result)==null?void 0:f.id),await x(a.view,n.graphic.geometry.extent,!0))}}),ft(a.view,o=>{o.results.length&&o.results.map(n=>{var s;if(n.type==="graphic"){const r=(s=n.graphic)==null?void 0:s.attributes;(r.type==="dma_text"||r.type==="dma_area")&&R(r==null?void 0:r.result)}})}))},q=t=>{L(t==null?void 0:t.id,!0)},J=t=>{var o;const e=u.$refs["refPop"+t.id];e!=null&&e.length&&((o=e[0])==null||o.open())},R=async t=>{var o,n,s,r;if(!(t!=null&&t.id))return;const e=await wt(t.id);(o=e.data)!=null&&o.length&&(t.pid&&w(t.pid),i.marks=[],i.parentExtentTo=!0,(n=l.graphicsLayer)==null||n.removeAll(),(s=l.textLayer)==null||s.removeAll(),(r=e.data)==null||r.map(m=>{const f=M(m);f&&i.parentExtentTo&&x(a.view,f.geometry.extent,!0)}))},L=async(t,e)=>{var n,s,r,m;if(!t)return;const o=await xt(t);return(n=o.data)!=null&&n.length&&(i.marks=[],(s=l.graphicsLayer)==null||s.removeAll(),(r=l.textLayer)==null||r.removeAll(),(m=o.data)==null||m.map(f=>{const c=M(f);e&&c&&x(a.view,c.geometry.extent,!0)})),o.data||[]},U=t=>{t.highLight=!0},M=t=>{var T,_,E;if(!t)return;const e=t.position instanceof Array?t.position:typeof t.position=="string"?t.position&&JSON.parse(t.position)||[]:[];if(!e||e.length<3)return;e.push(e[0]);const o=[Math.random()*255,Math.random()*255,Math.random()*255,.2],n=e.map(F=>{var W;const V=new O({longitude:F[0],latitude:F[1],spatialReference:(W=a.view)==null?void 0:W.spatialReference});return[V.x,V.y]}),s=new nt({rings:[n],spatialReference:(T=a.view)==null?void 0:T.spatialReference}),r=pt("polygon",{color:o,outlineColor:"#E3E709",outlineWidth:1}),m=new N({geometry:s,symbol:r,attributes:{type:"dma_area",result:t}});(_=l.graphicsLayer)==null||_.add(m);const f=new st({text:t.name,color:"#333333",haloColor:"#ffffff",haloSize:4,font:{size:"7px",weight:"bold"}}),c=new N({geometry:s,symbol:f,attributes:{type:"dma_text",result:t}});(E=l.textLayer)==null||E.add(c);const P=new O(c==null?void 0:c.geometry.extent.center);return i.marks.push({showMore:!0,showBack:!0,title:t.name,visible:!0,x:P.longitude||0,y:P.latitude||0,bgColor:"#1e2f44",attributes:{id:t.id,values:[{label:"昨日总供水量",value:t.supplyTotal||0,unit:"m³"},{label:"昨日流量占比",value:t.supplyRate||0,unit:"%"},{label:"总用户数",value:t.households||0,unit:"户"},{label:"夜间最小流量",value:t.nightMinFlow||0,unit:"m³/h"},{label:"产销差率",value:t.differenceRate||0,unit:"%"},{label:"漏失率",value:t.lossRate||0,unit:"%"}]}}),m},j=async()=>{var e;const t=await gt();i.areaTree=t.data||[],i.areaTree.map(o=>{L(o.id,!0)}),w((e=i.areaTree[0])==null?void 0:e.id)},Q=t=>{var s,r,m;const e=(s=g.group.find(f=>f.id==="lossRate"))==null?void 0:s.fields[0];e.option=ut((t==null?void 0:t.lossRate)||[]);const o=(r=g.group.find(f=>f.id==="lossRateAndProSale"))==null?void 0:r.fields[0];o.option=yt((t==null?void 0:t.lossRate)||[]);const n=(m=g.group.find(f=>f.id==="wholeDayLiuliangPercent"))==null?void 0:m.fields[0];n.option=ht((t==null?void 0:t.pressure)||[])};return Z(()=>{tt(()=>{D()}),H()}),et(()=>{var t,e;l.graphicsLayer&&((t=a.view)==null||t.map.remove(l.graphicsLayer)),l.textLayer&&((e=a.view)==null||e.map.remove(l.textLayer))}),(t,e)=>{var n;const o=at;return h(),k($,null,[t.telport?(h(),G(it,{key:0,to:t.telport},[(n=C(i).marks)!=null&&n.length?(h(),k("div",vt,[(h(!0),k($,null,ot(C(i).marks,(s,r)=>(h(),G(dt,{key:r,ref_for:!0,ref:"refPop"+s.attributes.id,view:a.view,config:s,onHighlight:U,onMore:q,onBack:R},null,8,["view","config"]))),128))])):I("",!0)],8,["to"])):I("",!0),rt(o,{ref_key:"refForm",ref:d,config:C(g)},null,8,["config"])],64)}}});export{Oe as default};
