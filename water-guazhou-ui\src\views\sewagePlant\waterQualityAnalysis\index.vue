<!-- 水质分析 -->
<template>
  <div class="page-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline>
          <el-form-item label="站点选择">
            <el-select v-model="searchForm.stationId" placeholder="请选择站点" style="width: 200px">
              <el-option label="污水处理厂" value="station1" />
            </el-select>
          </el-form-item>
          <el-form-item label="报告类型">
            <el-radio-group v-model="searchForm.type">
              <el-radio-button label="day">日报</el-radio-button>
              <el-radio-button label="month">月报</el-radio-button>
              <el-radio-button label="year">年报</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 320px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button type="warning" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button type="success" @click="handlePrint">
              <el-icon><Printer /></el-icon>
              打印
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 内容区域 -->
    <div class="content-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>{{ currentMode === 'list' ? '水质列表' : '水质图表' }}</span>
            <div class="mode-switch">
              <el-radio-group v-model="currentMode">
                <el-radio-button label="chart">
                  <el-icon><TrendCharts /></el-icon>
                </el-radio-button>
                <el-radio-button label="list">
                  <el-icon><Grid /></el-icon>
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <!-- 图表模式 -->
        <div v-if="currentMode === 'chart'" class="chart-container">
          <div v-if="chartData.option" class="chart-wrapper">
            <VChart
              :option="chartData.option"
              :theme="isDark ? 'dark' : 'light'"
              style="width: 100%; height: 500px;"
            />
          </div>
          <div v-else class="chart-loading">
            <el-icon size="64" color="#ccc"><TrendCharts /></el-icon>
            <p style="color: #999; margin-top: 16px;">正在加载图表...</p>
          </div>
        </div>

        <!-- 列表模式 -->
        <div v-if="currentMode === 'list'" class="table-container">
          <el-table
            :data="tableData.list"
            :loading="tableData.loading"
            border
            stripe
            style="width: 100%"
          >
            <el-table-column prop="sampleTime" label="采样时间" min-width="150" />
            <el-table-column prop="sampleLocation" label="采样地点" min-width="120" />
            <el-table-column prop="weather" label="天气" min-width="80" />
            <el-table-column prop="outletCod" label="出水COD" min-width="120">
              <template #default="{ row }">
                {{ row.outletCod }} mg/L
              </template>
            </el-table-column>
            <el-table-column prop="outletBod" label="BOD5" min-width="120">
              <template #default="{ row }">
                {{ row.outletBod }} mg/L
              </template>
            </el-table-column>
            <el-table-column prop="suspendedSolids" label="悬浮物" min-width="120">
              <template #default="{ row }">
                {{ row.suspendedSolids }} mg/L
              </template>
            </el-table-column>
            <el-table-column prop="ammoniaNitrogen" label="氨氮" min-width="120">
              <template #default="{ row }">
                {{ row.ammoniaNitrogen }} mg/L
              </template>
            </el-table-column>
            <el-table-column prop="totalNitrogen" label="总氮" min-width="120">
              <template #default="{ row }">
                {{ row.totalNitrogen }} mg/L
              </template>
            </el-table-column>
            <el-table-column prop="totalPhosphorus" label="总磷" min-width="120">
              <template #default="{ row }">
                {{ row.totalPhosphorus }} mg/L
              </template>
            </el-table-column>
            <el-table-column prop="outletPh" label="出水PH值" min-width="120" />
            <el-table-column prop="fecalColiform" label="粪大肠菌群数" min-width="150">
              <template #default="{ row }">
                {{ row.fecalColiform }} 个/L
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download, Printer, TrendCharts, Grid } from '@element-plus/icons-vue'
import { useAppStore } from '@/store'

// 使用 store
const appStore = useAppStore()
const isDark = computed(() => appStore.isDark)

// 搜索表单
const searchForm = reactive({
  stationId: 'station1',
  type: 'day',
  dateRange: [new Date(), new Date()]
})

// 当前模式
const currentMode = ref('list')

// 表格数据
const tableData = reactive({
  loading: false,
  list: [] as any[]
})

// 图表数据
const chartData = reactive({
  option: null
})

// 模拟数据
const getMockData = () => {
  return [
    {
      sampleTime: '2025-04-10 09:00',
      sampleLocation: '出水口',
      weather: '晴',
      outletCod: '15.2',
      outletBod: '8.5',
      suspendedSolids: '12.3',
      ammoniaNitrogen: '2.1',
      totalNitrogen: '8.7',
      totalPhosphorus: '0.8',
      outletPh: '7.2',
      fecalColiform: '1000'
    },
    {
      sampleTime: '2025-04-15 09:00',
      sampleLocation: '出水口',
      weather: '阴',
      outletCod: '18.7',
      outletBod: '9.2',
      suspendedSolids: '14.1',
      ammoniaNitrogen: '2.5',
      totalNitrogen: '9.3',
      totalPhosphorus: '0.9',
      outletPh: '7.1',
      fecalColiform: '1200'
    },
    {
      sampleTime: '2025-04-20 09:00',
      sampleLocation: '出水口',
      weather: '雨',
      outletCod: '22.1',
      outletBod: '11.8',
      suspendedSolids: '16.7',
      ammoniaNitrogen: '3.2',
      totalNitrogen: '10.5',
      totalPhosphorus: '1.1',
      outletPh: '6.9',
      fecalColiform: '1500'
    },
    {
      sampleTime: '2025-04-25 09:00',
      sampleLocation: '出水口',
      weather: '晴',
      outletCod: '16.8',
      outletBod: '9.7',
      suspendedSolids: '13.5',
      ammoniaNitrogen: '2.3',
      totalNitrogen: '8.9',
      totalPhosphorus: '0.7',
      outletPh: '7.3',
      fecalColiform: '900'
    }
  ]
}

// 生成图表配置
const generateChart = () => {
  const data = getMockData()

  chartData.option = {
    title: {
      text: '水质指标趋势图',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['出水COD', 'BOD5', '氨氮', '总氮', '总磷'],
      top: 30,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map(item => item.sampleTime.split(' ')[0])
    },
    yAxis: {
      type: 'value',
      name: '浓度 (mg/L)',
      min: 0
    },
    series: [
      {
        name: '出水COD',
        type: 'line',
        data: data.map(item => parseFloat(item.outletCod)),
        smooth: true,
        itemStyle: { color: '#5470C6' },
        lineStyle: { width: 2 }
      },
      {
        name: 'BOD5',
        type: 'line',
        data: data.map(item => parseFloat(item.outletBod)),
        smooth: true,
        itemStyle: { color: '#91CC75' },
        lineStyle: { width: 2 }
      },
      {
        name: '氨氮',
        type: 'line',
        data: data.map(item => parseFloat(item.ammoniaNitrogen)),
        smooth: true,
        itemStyle: { color: '#FAC858' },
        lineStyle: { width: 2 }
      },
      {
        name: '总氮',
        type: 'line',
        data: data.map(item => parseFloat(item.totalNitrogen)),
        smooth: true,
        itemStyle: { color: '#EE6666' },
        lineStyle: { width: 2 }
      },
      {
        name: '总磷',
        type: 'line',
        data: data.map(item => parseFloat(item.totalPhosphorus)),
        smooth: true,
        itemStyle: { color: '#73C0DE' },
        lineStyle: { width: 2 }
      }
    ]
  }
}

// 监听模式切换
watch(currentMode, (newMode) => {
  if (newMode === 'chart') {
    setTimeout(() => {
      generateChart()
    }, 100)
  }
})

// 加载数据
const loadData = () => {
  tableData.loading = true
  setTimeout(() => {
    tableData.list = getMockData()
    tableData.loading = false
  }, 500)
}

// 事件处理
const handleSearch = () => {
  loadData()
  if (currentMode.value === 'chart') {
    generateChart()
  }
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handlePrint = () => {
  ElMessage.info('打印功能开发中...')
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.search-section {
  .el-card {
    border-radius: 8px;
  }
}

.content-section {
  flex: 1;

  .el-card {
    height: 100%;
    border-radius: 8px;

    :deep(.el-card__body) {
      height: calc(100% - 60px);
      display: flex;
      flex-direction: column;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .mode-switch {
    .el-radio-group {
      .el-radio-button {
        :deep(.el-radio-button__inner) {
          padding: 8px 12px;
        }
      }
    }
  }
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;

  .chart-wrapper {
    flex: 1;
  }

  .chart-loading {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
}

.table-container {
  flex: 1;

  .el-table {
    height: 100%;
  }
}
</style>

