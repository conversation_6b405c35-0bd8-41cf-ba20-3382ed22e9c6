package org.thingsboard.server.dao.repair;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.RepairStandardEntity;

import java.util.List;

public interface RepairStandardService {
    RepairStandardEntity findById(String id);

    PageData<RepairStandardEntity> findList(int page, int size, String name, String deviceType, User currentUser);

    RepairStandardEntity save(RepairStandardEntity entity);

    void remove(List<String> ids);

    List<RepairStandardEntity> findAll(String name, TenantId tenantId);

    List<RepairStandardEntity> findAll(String name, String tenantId);
}
