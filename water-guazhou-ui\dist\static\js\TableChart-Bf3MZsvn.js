import{d as S,r as v,j as k,c as C,am as T,o as z,ay as N,g as d,n as A,p as h,q as O,i as l,h as f,an as V,aw as j,aq as P,a7 as I,bt as B,C as F}from"./index-r0dFAfgr.js";import{u as G}from"./useDetector-BRcb7GRN.js";import"./index-0NlGN6gS.js";import{h as q}from"./statistics-CeyexT_5.js";const E={class:"left"},K={class:"right"},U=S({__name:"TableChart",props:{data:{}},setup(W){const y=W,e=v({dataList:[],defaultExpandAll:!0,indexVisible:!0,rowKey:"partitionId",treeProps:{children:"subPartitionNRWDTOList"},columns:[{fixed:"left",minWidth:160,label:"分区名称",prop:"partitionName"},{minWidth:120,label:"校准产销差(%)",prop:"nrw"},{minWidth:120,label:"日期",prop:"date"},{minWidth:120,label:"用户数(户)",prop:"userNum"},{minWidth:120,label:"m³",align:"center",prop:"key",subColumns:[{minWidth:120,label:"供水总量",prop:"supplyTotal"},{minWidth:120,label:"校准用水量",prop:"correctUseWater"},{minWidth:120,label:"进水量",prop:"inWater"},{minWidth:120,label:"出水量",prop:"outWater"},{minWidth:120,label:"漏失总量",prop:"lossTotal"}]}],highlightCurrentRow:!0,pagination:{hide:!0},handleRowClick:async t=>{e.currentRow=t,p.rowOption=await L()}}),L=async()=>{var a,c,m,u;if(!e.currentRow)return;const t=await q({partitionId:e.currentRow.partitionId}),r=((c=(a=t.data)==null?void 0:a.data)==null?void 0:c.x)||[],o=((u=(m=t.data)==null?void 0:m.data)==null?void 0:u.y)||[],s=e.currentRow||{};return{title:{text:(s.partitionName||"")+"上月流量",top:20},tooltip:{trigger:"axis"},grid:{containLabel:!0,left:20,right:40,top:80,bottom:30},xAxis:{type:"category",boundaryGap:!1,data:r},yAxis:{name:"m³",type:"value",splitLine:{lineStyle:{type:"dashed",color:k().isDark?"rgba(255,255,255,0.25)":"rgba(0,0,0,0.25)"}}},series:[{name:s.partitionName,type:"line",data:o,smooth:!0,markPoint:{data:[{type:"max",name:"Max"},{type:"min",name:"Min"}]},markLine:{data:[{type:"average",name:"Avg"}]}}]}},R=()=>{var w,x;const t=((x=(w=e.dataList)==null?void 0:w[0])==null?void 0:x.subPartitionNRWDTOList)||[],r=[],o=t.map(n=>(r.push(n.partitionName),n.nrw)),s=Math.max(...o)||100,a=k().isDark,c=r.map((n,i)=>({name:n,value:o[i],num:o[i]})),m=r.map((n,i)=>({name:n,value:s,label:{show:!0,position:"right",fontSize:14,color:a?"rgba(255,255,255,0.8)":"rgba(0,0,0,0.8)",offset:[16,0],formatter(){return o[i]}}})),u=r.map((n,i)=>({name:n,value:o[i],label:o[i]}));return{tooltip:{axisPointer:{type:"shadow"}},grid:{top:0,left:10,right:80,bottom:0,containLabel:!0},xAxis:{type:"value",splitLine:{show:!1},axisLine:{show:!1},axisLabel:{show:!1},axisTick:{show:!1},position:"top"},yAxis:{type:"category",data:r,inverse:!0,axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:a?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)",fontSize:14,fontFamily:"TencentSans"}}},dataZoom:[{type:"slider",show:!1,backgroundColor:"rgba(0,0,0,0)",fillerColor:a?"rgba(255,255,255,0.3)":"rgba(0,0,0,0.6)",borderColor:"rgb(0,0,0,0.25)",showDetail:!1,startValue:0,endValue:5,yAxisIndex:[0],filterMode:"empty",width:8,right:3,handleSize:0,zoomLoxk:!0,top:10,height:"90%"},{type:"inside",yAxisIndex:[0,1],zoomOnMouseWheel:!1,moveOnMouseMove:!0,moveOnMouseWheel:!0}],series:[{type:"bar",barGap:"-100%",barWidth:14,z:1,itemStyle:{color:new I(0,0,1,0,[{offset:0,color:"rgba(0,255,255,1)"},{offset:1,color:"rgba(255,0,0,1)"}],!1)},data:c},{type:"bar",barWidth:14,z:0,itemStyle:{color:a?"rgba(26, 49, 99, 1)":"rgba(26, 49, 99, 0.1)"},tooltip:{show:!1},data:m},{type:"pictorialBar",symbolRepeat:"fixed",symbolMargin:6,symbol:"rect",z:2,symbolClip:!0,symbolSize:[1,14],symbolPosition:"start",itemStyle:{color:a?"rgba(255,255,255,0.6)":"rgba(0,0,0,0.6)"},data:u}]}},p=v({option:null,rowOption:null}),D=()=>{var t;e.dataList=[(t=y.data)==null?void 0:t.partitionNRW],e.currentRow=void 0,p.option=R()},g=C(),b=C(),M=G();return T(()=>y.data,()=>{D()}),z(()=>{M.listenToMush(g.value,()=>{var t;(t=b.value)==null||t.resize()})}),(t,r)=>{const o=P,s=B,a=N("VChart");return d(),A("div",{ref_key:"refDiv",ref:g,class:"table-cahrt"},[h("div",E,[O(o,{config:l(e)},null,8,["config"])]),h("div",K,[l(e).currentRow?V("",!0):(d(),f(s,{key:0,type:"simple",title:"DMA分区产销差率排行榜"})),h("div",{class:j(["chart",l(e).currentRow?"full":""])},[l(e).currentRow?(d(),f(a,{key:0,ref_key:"refChart",ref:b,option:l(p).rowOption},null,8,["option"])):(d(),f(a,{key:1,ref_key:"refChart",ref:b,option:l(p).option},null,8,["option"]))],2)])],512)}}}),X=F(U,[["__scopeId","data-v-08c3b80a"]]);export{X as default};
