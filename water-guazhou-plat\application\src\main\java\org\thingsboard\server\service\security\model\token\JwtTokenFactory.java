/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.security.model.token;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.AMQP;
import io.jsonwebtoken.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.config.JwtSettings;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.security.model.SecurityUser;
import org.thingsboard.server.service.security.model.UserPrincipal;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class JwtTokenFactory {

    private static final String SCOPES = "scopes";
    private static final String USER_ID = "userId";
    private static final String FIRST_NAME = "firstName";
    private static final String LAST_NAME = "lastName";
    private static final String ENABLED = "enabled";
    private static final String IS_PUBLIC = "isPublic";
    private static final String TENANT_ID = "tenantId";
    private static final String CUSTOMER_ID = "customerId";
    private static final String PHONE = "phone";

    private final JwtSettings settings;

    @Autowired
    public JwtTokenFactory(JwtSettings settings) {
        this.settings = settings;
    }

    /**
     * Factory method for issuing new JWT Tokens.
     */
    public AccessJwtToken createAccessJwtToken(SecurityUser securityUser) {
        if (StringUtils.isBlank(securityUser.getEmail()))
            throw new IllegalArgumentException("Cannot create JWT Token without username/email");

        if (securityUser.getAuthority() == null)
            throw new IllegalArgumentException("User doesn't have any privileges");

        UserPrincipal principal = securityUser.getUserPrincipal();
        String subject = principal.getValue();
        Claims claims = Jwts.claims().setSubject(subject);
        claims.put(SCOPES, securityUser.getAuthorities().stream().map(s -> s.getAuthority()).collect(Collectors.toList()));
        claims.put(USER_ID, securityUser.getId().getId().toString());
        claims.put(FIRST_NAME, securityUser.getFirstName());
        claims.put(LAST_NAME, securityUser.getLastName());
        claims.put(ENABLED, securityUser.isEnabled());
        claims.put(IS_PUBLIC, principal.getType() == UserPrincipal.Type.PUBLIC_ID);
        try {
            claims.put(PHONE, JacksonUtil.fromString(securityUser.getAdditionalInfo().asText(), Map.class).get("phone"));
        } catch (Exception e) {
            claims.put(PHONE, "");
        }
        if (securityUser.getTenantId() != null) {
            claims.put(TENANT_ID, securityUser.getTenantId().getId().toString());
        }
        if (securityUser.getCustomerId() != null) {
            claims.put(CUSTOMER_ID, securityUser.getCustomerId().getId().toString());
        }
        if (securityUser.getTenantList() != null && securityUser.getTenantList().size() > 0) {
            claims.put("tenantList", securityUser.getTenantList());
        }

        ZonedDateTime currentTime = ZonedDateTime.now();

        String token = Jwts.builder()
                .setClaims(claims)
                .setIssuer(settings.getTokenIssuer())
                .setIssuedAt(Date.from(currentTime.toInstant()))
                .setExpiration(Date.from(currentTime.plusSeconds(settings.getTokenExpirationTime()).toInstant()))
                .signWith(SignatureAlgorithm.HS512, settings.getTokenSigningKey())
                .compact();

        return new AccessJwtToken(token, claims);
    }

    public SecurityUser parseAccessJwtToken(RawAccessJwtToken rawAccessToken) {
        Jws<Claims> jwsClaims = rawAccessToken.parseClaims(settings.getTokenSigningKey());
        Claims claims = jwsClaims.getBody();
        String subject = claims.getSubject();
        List<String> scopes = claims.get(SCOPES, List.class);
        if (scopes == null || scopes.isEmpty()) {
            throw new IllegalArgumentException("JWT Token doesn't have any scopes");
        }

        SecurityUser securityUser = new SecurityUser(new UserId(UUID.fromString(claims.get(USER_ID, String.class))));
        securityUser.setEmail(subject);
        securityUser.setAuthority(Authority.parse(scopes.get(0)));
        securityUser.setFirstName(claims.get(FIRST_NAME, String.class));
        securityUser.setLastName(claims.get(LAST_NAME, String.class));
        securityUser.setEnabled(claims.get(ENABLED, Boolean.class));
        boolean isPublic = claims.get(IS_PUBLIC, Boolean.class);
        UserPrincipal principal = new UserPrincipal(isPublic ? UserPrincipal.Type.PUBLIC_ID : UserPrincipal.Type.USER_NAME, subject);
        securityUser.setUserPrincipal(principal);
        String tenantId = claims.get(TENANT_ID, String.class);
        if (tenantId != null) {
            securityUser.setTenantId(new TenantId(UUID.fromString(tenantId)));
        }
        String customerId = claims.get(CUSTOMER_ID, String.class);
        if (customerId != null) {
            securityUser.setCustomerId(new CustomerId(UUID.fromString(customerId)));
        }
        List tenantList = claims.get("tenantList", List.class);
        if (tenantList != null) {
            securityUser.setTenantList(JSON.parseArray(JSON.toJSONString(tenantList), Tenant.class));
        }

        return securityUser;
    }

    public JwtToken createRefreshToken(SecurityUser securityUser) {
        if (StringUtils.isBlank(securityUser.getEmail())) {
            throw new IllegalArgumentException("Cannot create JWT Token without username/email");
        }

        ZonedDateTime currentTime = ZonedDateTime.now();

        UserPrincipal principal = securityUser.getUserPrincipal();
        Claims claims = Jwts.claims().setSubject(principal.getValue());
        claims.put(SCOPES, Collections.singletonList(Authority.REFRESH_TOKEN.name()));
        claims.put(USER_ID, securityUser.getId().getId().toString());
        claims.put(IS_PUBLIC, principal.getType() == UserPrincipal.Type.PUBLIC_ID);

        String token = Jwts.builder()
                .setClaims(claims)
                .setIssuer(settings.getTokenIssuer())
                .setId(UUID.randomUUID().toString())
                .setIssuedAt(Date.from(currentTime.toInstant()))
                .setExpiration(Date.from(currentTime.plusSeconds(settings.getRefreshTokenExpTime()).toInstant()))
                .signWith(SignatureAlgorithm.HS512, settings.getTokenSigningKey())
                .compact();

        return new AccessJwtToken(token, claims);
    }

    public SecurityUser parseRefreshToken(RawAccessJwtToken rawAccessToken) {
        Jws<Claims> jwsClaims = rawAccessToken.parseClaims(settings.getTokenSigningKey());
        Claims claims = jwsClaims.getBody();
        String subject = claims.getSubject();
        List<String> scopes = claims.get(SCOPES, List.class);
        if (scopes == null || scopes.isEmpty()) {
            throw new IllegalArgumentException("Refresh Token doesn't have any scopes");
        }
        if (!scopes.get(0).equals(Authority.REFRESH_TOKEN.name())) {
            throw new IllegalArgumentException("Invalid Refresh Token scope");
        }
        boolean isPublic = claims.get(IS_PUBLIC, Boolean.class);
        UserPrincipal principal = new UserPrincipal(isPublic ? UserPrincipal.Type.PUBLIC_ID : UserPrincipal.Type.USER_NAME, subject);
        SecurityUser securityUser = new SecurityUser(new UserId(UUID.fromString(claims.get(USER_ID, String.class))));
        securityUser.setUserPrincipal(principal);
        return securityUser;
    }


    public  String verify(String token) {
        Jws<Claims> jwts = (Jws<Claims>) Jwts.parser().parse(token);
        JSONObject jsonObject = (JSONObject) jwts.getBody();
        return jsonObject.getString("_id");
    }

    public static void main(String[] args) {
        String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyJ9.eyJ1c2VybmFtZSI6Imppbmd5dWFuQGFkbWluLmNvbSIsInRiSldUIjoiZXlKaGJHY2lPaUpJVXpVeE1pSjkuZXlKemRXSWlPaUpxYVc1bmVYVmhia0JoWkcxcGJpNWpiMjBpTENKelkyOXdaWE1pT2xzaVZFVk9RVTVVWDBGRVRVbE9JbDBzSW5WelpYSkpaQ0k2SW1Nek9EZzJNell3TFdabU5ESXRNVEZsT0MxaVlXWTNMVE5pTjJRM01UZ3paR0UxTXlJc0ltWnBjbk4wVG1GdFpTSTZJdWFadHVpTGtlV2J2ZW1aaFY5QlJFMUpUaUlzSW14aGMzUk9ZVzFsSWpvaTVwbTI2SXVSNVp1OTZabUZYMEZFVFVsT0lpd2laVzVoWW14bFpDSTZkSEoxWlN3aWFYTlFkV0pzYVdNaU9tWmhiSE5sTENKd2FHOXVaU0k2SWlJc0luUmxibUZ1ZEVsa0lqb2lNV0ZrWVRKaFpqQXRabVkwTWkweE1XVTRMV0poWmpjdE0ySTNaRGN4T0ROa1lUVXpJaXdpWTNWemRHOXRaWEpKWkNJNklqRXpPREUwTURBd0xURmtaREl0TVRGaU1pMDRNRGd3TFRnd09EQTRNRGd3T0RBNE1DSXNJbWx6Y3lJNkluUm9hVzVuYzJKdllYSmtMbWx2SWl3aWFXRjBJam94TlRnNE1EUTRNREU0TENKbGVIQWlPakUyTnpnd05EZ3dNVGg5LkZJT2NkSUl1U2d3TktSeHF0NDBXN2xzV1ZlVWtXV0ZMTjBBbi1fdHdmNG1VS1F0THJWLUhpb3l2WTg2TXppQjY0bUdTd0tmazBKUktFZU5sTVZ0NkdRIiwidGJVc2VyIjp7InN1YiI6Imppbmd5dWFuQGFkbWluLmNvbSIsInNjb3BlcyI6WyJURU5BTlRfQURNSU4iXSwidXNlcklkIjoiYzM4ODYzNjAtZmY0Mi0xMWU4LWJhZjctM2I3ZDcxODNkYTUzIiwiZmlyc3ROYW1lIjoi5pm26IuR5Zu96ZmFX0FETUlOIiwibGFzdE5hbWUiOiLmmbboi5Hlm73pmYVfQURNSU4iLCJlbmFibGVkIjp0cnVlLCJpc1B1YmxpYyI6ZmFsc2UsInBob25lIjoiIiwidGVuYW50SWQiOiIxYWRhMmFmMC1mZjQyLTExZTgtYmFmNy0zYjdkNzE4M2RhNTMiLCJjdXN0b21lcklkIjoiMTM4MTQwMDAtMWRkMi0xMWIyLTgwODAtODA4MDgwODA4MDgwIn0sIl9pZCI6IlJQNG43MmdkTzdLZVFxNk8iLCJpYXQiOjE1ODgwNDgwMTgsImV4cCI6MTU4ODEzNDQxOCwiYXVkIjoiaHR0cHM6Ly95b3VyZG9tYWluLmNvbSIsImlzcyI6ImZlYXRoZXJzIiwic3ViIjoiYW5vbnltb3VzIiwianRpIjoiMDY0YjE4NTYtNmFiZS00MThmLTk2NGItNzA4MzczNGYzNzRhIn0.sZ_e6aOYDkPDsafBAihSWd8Yg2nqhUxhhoPk0iFMnls";
        Jws<Claims> jwts = (Jws<Claims>) Jwts.parser().parse(token);
        JSONObject jsonObject = (JSONObject) jwts.getBody();
    }

}
