<!-- 缓冲区查询 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="缓冲区查询"
    @map-loaded="onMaploaded"
  >
    <template #right-title>
      <SchemeHeader
        :title="'缓冲区查询'"
        @scheme-click="scheme.openManagerDialog"
      ></SchemeHeader>
    </template>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <SchemeManage
      :ref="scheme.getSchemeManageRef"
      :type="scheme.schemeType.value"
      @row-click="handleUseScheme"
    ></SchemeManage>
    <SaveScheme
      :ref="scheme.getSaveSchemeRef"
      @submit="handleSchemeSubmit"
    ></SaveScheme>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic'
import {
  createGraphic,
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds,
  gotoAndHighLight,
  initBufferParams,
  queryBufferPolygon,
  setSymbol
} from '@/utils/MapHelper'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../common/RightDrawerMap.vue'
import SchemeHeader from './Scheme/SchemeHeader.vue'
import SchemeManage from './Scheme/SchemeManage.vue'
import SaveScheme from './Scheme/SaveScheme.vue'
import { useScheme } from '@/hooks/arcgis/useScheme'
import { useSketch } from '@/hooks/arcgis'

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()

const state = reactive<{
  tabs: any[]
  loading: boolean
  layerInfos: any[]
  layerIds: any[]
}>({
  tabs: [],
  loading: false,
  layerInfos: [],
  layerIds: []
})
const staticState: {
  view?: __esri.MapView
  bufferGeometry?: __esri.Geometry
  bufferLayer?: __esri.GraphicsLayer
  graphics?: __esri.Graphic
  graphicsLayer?: __esri.GraphicsLayer
} = {}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '点要素',
              iconifyIcon: 'mdi:circle-slice-8',
              click: () => initDraw('point')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '线要素',
              iconifyIcon: 'mdi:chart-timeline-variant',
              click: () => initDraw('polyline')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '面要素',
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '缓冲半径'
      },
      fields: [
        {
          type: 'input-number',
          append: '米',
          field: 'distance'
        }
      ]
    },
    {
      id: 'layer',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: false,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              styles: {
                width: '100%'
              },
              click: () => startQuery()
            },
            {
              perm: window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,
              text: '保存方案',
              styles: {
                width: '100%'
              },
              click: () => handleSaveScheme()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    distance: 200
  }
})

const initDraw = (type: any) => {
  if (!staticState.view) return
  staticState.graphicsLayer?.removeAll()
  staticState.bufferLayer?.removeAll()
  sketch.value?.create(type)
}
const startQuery = async () => {
  if (!staticState.graphics) {
    SLMessage.warning('请先绘制图形')
    return
  }
  SLMessage.info('正在查询中，请稍候...')
  const distance = refForm.value?.dataForm?.distance || '0'
  state.tabs.length = 0
  try {
    if (Number(distance)) {
      staticState.bufferGeometry = await doBuffer()
    } else {
      staticState.bufferGeometry = staticState.graphics?.geometry
    }

    const layers = refForm.value?.dataForm.layerid?.filter(item => item >= 0) || []
    state.tabs = await getLayerOids(layers, state.layerInfos, {
      where: '1=1',
      geometry: staticState.bufferGeometry
    })
    refMap.value?.refreshDetail(state.tabs)
    staticState.bufferGeometry
      && gotoAndHighLight(staticState.view, new Graphic({ geometry: staticState.bufferGeometry }), {
        avoidHighlight: true
      })
  } catch (error) {
    SLMessage.error('系统错误')
  }
  state.loading = false
}
const doBuffer = async () => {
  const geometry = staticState.graphics?.geometry
  staticState.bufferLayer?.removeAll()
  if (!geometry) return
  const distance = refForm.value?.dataForm?.distance || 0
  const polygons = await queryBufferPolygon(
    initBufferParams({
      bufferSpatialReference: staticState.view?.spatialReference,
      distances: [distance],
      geometries: [geometry],
      outSpatialReference: staticState.view?.spatialReference,
      geodesic: true,
      unit: 'meters',
      unionResults: false
    })
  )
  const graphic = createGraphic({
    geometry: polygons[0],
    symbol: setSymbol('polygon', {
      color: [0, 182, 153, 0.2],
      outlineColor: '#00B699',
      outlineWidth: 1
    })
  })
  staticState.bufferLayer?.add(graphic)
  return polygons[0]
}
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.bufferLayer?.removeAll()
  staticState.graphics = undefined
}

const getLayerInfo = async () => {
  if(window.GIS_SERVER_SWITCH){
    const field = FormConfig.group.find(item => item.id === 'layer')?.fields[0] as IFormTree
    const layerInfo = staticState.view?.layerViews.items[0].layer.sublayers;
    let layers = layerInfo.items.map(item => {
      return {
        label: item.name,
        value: item.name,
        // data: item
      }
    });
    field.options = layers;
    refForm.value && (refForm.value.dataForm.layerid = layers.map(item => item.value))
  }else{
    state.layerIds = getSubLayerIds(staticState.view)
    queryLayerClassName(state.layerIds).then(layerInfo => {
      state.layerInfos = layerInfo.data?.result?.rows || []
      const field = FormConfig.group.find(item => item.id === 'layer')?.fields[0] as IFormTree
      const points = state.layerInfos
        .filter(item => item.geometrytype === 'esriGeometryPoint')
        .map(item => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          }
        })
      const lines = state.layerInfos
        .filter(item => item.geometrytype === 'esriGeometryPolyline')
        .map(item => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          }
        })
      field
        && (field.options = [
          { label: '管点类', value: -1, children: points },
          { label: '管线类', value: -2, children: lines }
        ])
      refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
    })
  }
}
const { initSketch, destroySketch, sketch } = useSketch()
const resolveDrawEnd = (res: ISketchHandlerParameter) => {
  if (res.state === 'complete') {
    staticState.graphics = res.graphics[0]
    console.log(JSON.stringify(staticState.graphics))
  }
}
const onMaploaded = async view => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-buffer-temp',
    title: '缓冲区-绘制'
  })
  staticState.bufferLayer = getGraphicLayer(staticState.view, {
    id: 'search-buffer',
    title: '缓冲区'
  })
  initSketch(staticState.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  })
  setTimeout(() => {
    getLayerInfo()
  }, 1000);
}

const scheme = useScheme('buffer')
const handleSaveScheme = () => {
  if (!staticState.graphics) {
    SLMessage.warning('请先绘制图形')
    return
  }
  scheme.openSaveDialog()
}
const handleUseScheme = async (row: any) => {
  clearGraphicsLayer()
  const detail = scheme.parseScheme(row)
  if (refForm.value?.dataForm) {
    refForm.value.dataForm.layerid = detail.layerid || []
    refForm.value.dataForm.distance = detail.distance
  }
  if (detail.graphic) {
    staticState.graphics = Graphic.fromJSON(detail.graphic)
    staticState.graphicsLayer?.add(staticState.graphics)
  }
  startQuery()
}
const handleSchemeSubmit = params => {
  scheme.submitScheme({
    ...params,
    type: scheme.schemeType.value,
    detail: JSON.stringify({
      layerid: refForm.value?.dataForm.layerid || [],
      distance: refForm.value?.dataForm.distance,
      graphic: staticState.graphics
    })
  })
}
onBeforeUnmount(() => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphicsLayer?.destroy()
  staticState.bufferLayer?.removeAll()
  staticState.bufferLayer?.destroy()
  destroySketch()
})
</script>
<style lang="scss" scoped></style>
