<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionContractMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        contract.id,
        contract.code,
        contract.name,
        contract.type,
        (select name from so_general_type where id = contract.type)        as type_name,
        construction.code                                                  as construction_code,
        construction.name                                                  as construction_name,
        construction.type_id                                               as construction_type_id,
        (select name from so_general_type where id = construction.type_id) as construction_type_name,
        contract.firstpart_organization,
        contract.firstpart_representative,
        contract.firstpart_phone,
        contract.secondpart_organization,
        contract.secondpart_representative,
        contract.secondpart_phone,
        combied_firstpart_organization                                     as firstpart_organization,
        combied_secondpart_organization                                    as secondpart_organization,
        combied_supervisor_organization                                    as combied_supervisor_organization,
        combied_construct_organization                                     as combied_construct_organization,
        contract_total_cost                                                as contract_total_cost,
        contract.cost,
        contract.work_time_begin,
        contract.work_time_end,
        contract.sign_time,
        contract.remark,
        contract.attachments,
        info.status                                                        as status,
        contract.creator,
        contract.create_time,
        contract.update_time,
        contract.update_user,
        construction.tenant_id
        <!--@sql from so_construction_contract contract, so_construction construction, so_construction_task_info info, so_construction_details details -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContract">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="type_name" property="typeName"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="firstpart_organization" property="firstpartOrganization"/>
        <result column="firstpart_representative" property="firstpartRepresentative"/>
        <result column="firstpart_phone" property="firstpartPhone"/>
        <result column="secondpart_organization" property="secondpartOrganization"/>
        <result column="secondpart_representative" property="secondpartRepresentative"/>
        <result column="secondpart_phone" property="secondpartPhone"/>
        <result column="cost" property="cost"/>
        <result column="work_time_begin" property="workTimeBegin"/>
        <result column="work_time_end" property="workTimeEnd"/>
        <result column="sign_time" property="signTime"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <resultMap id="SoConstructionContractContainerResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractContainer">
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="construction_type_id" property="constructionTypeId"/>
        <result column="construction_type_name" property="constructionTypeName"/>

        <result column="firstpart_organization" property="firstpartOrganization"/>
        <result column="secondpart_organization" property="secondpartOrganization"/>
        <result column="combied_supervisor_organization" property="supervisorOrganization"/>
        <result column="combied_construct_organization" property="constructionOrganization"/>
        <result column="contract_total_cost" property="contractTotalCost"/>

        <result column="status" property="status"/>
        <collection property="items" resultMap="BaseResultMap"/>
    </resultMap>

    <resultMap id="SoSimpleConstructionContractResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoSimpleConstructionContract">
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="cost" property="cost"/>
    </resultMap>
    <select id="findSimple"
            resultMap="SoSimpleConstructionContractResultMap">
        select code, name, cost
        from so_construction_contract contract
        <where>
            <if test="constructionCode != null and constructionCode != ''">
                and construction_code like '%' || #{constructionCode} || '%'
            </if>
            <!--            <if test="constructionName != null and constructionName != ''">-->
            <!--                and construction.name like '%' || #{constructionName} || '%'-->
            <!--            </if>-->
            <!--            <if test="constructionTypeId != null and constructionTypeId != ''">-->
            <!--                and construction.type_id = #{constructionTypeId}-->
            <!--            </if>-->
            <!--            <if test="firstpartOrganization != null and firstpartOrganization != ''">-->
            <!--                and firstpart_organization like '%' || #{firstpartOrganization} || '%'-->
            <!--            </if>-->
            <!--            <if test="secondpartOrganization != null and secondpartOrganization != ''">-->
            <!--                and secondpart_organization like '%' || #{secondpartOrganization} || '%'-->
            <!--            </if>-->
            <!--            <if test="superVisitorOrganization != null and superVisitorOrganization != ''">-->
            <!--                and combied_supervisor_organization like '%' || #{superVisitorOrganization} || '%'-->
            <!--            </if>-->
            <!--            <if test="fromTime != null">-->
            <!--                and contract.create_time >= #{fromTime}-->
            <!--            </if>-->
            <!--            <if test="toTime != null">-->
            <!--                and contract.create_time &lt;= #{toTime}-->
            <!--            </if>-->
            and contract.tenant_id = #{tenantId}
        </where>
    </select>

    <select id="findByPage" resultMap="SoConstructionContractContainerResultMap">
        <bind name="scope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_CONTRACT"/>
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_construction_contract contract
                           on contract.construction_code = construction.code and
                              contract.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = construction.code and
                              info.tenant_id = construction.tenant_id
                               and info.scope = #{scope}
                 left join so_construction_details details
                           on construction.code = details.current_construction_code and
                              construction.tenant_id = details.current_tenant_id
        where construction.code in (
        <include refid="pageInfoSubQuery"/>
        <if test="size > 0">
            offset #{offset} limit #{size}
        </if>
        )
          and construction.tenant_id = #{tenantId}
        order by construction.create_time desc
    </select>

    <select id="countByPage" resultType="long">
        select count(1) from (<include refid="pageInfoSubQuery"/>) a
    </select>

    <sql id="pageInfoSubQuery">
        select distinct construction.code
        from so_construction construction
                 left join so_construction_contract contract
                           on contract.construction_code = construction.code and
                              contract.tenant_id = construction.tenant_id
                 left join so_construction_details details
                           on construction.code = details.current_construction_code and
                              construction.tenant_id = details.current_tenant_id
        <where>
            <if test="constructionCode != null and constructionCode != ''">
                and construction_code like '%' || #{constructionCode} || '%'
            </if>
            <if test="constructionName != null and constructionName != ''">
                and construction.name like '%' || #{constructionName} || '%'
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            <if test="firstpartOrganization != null and firstpartOrganization != ''">
                and firstpart_organization like '%' || #{firstpartOrganization} || '%'
            </if>
            <if test="secondpartOrganization != null and secondpartOrganization != ''">
                and secondpart_organization like '%' || #{secondpartOrganization} || '%'
            </if>
            <if test="superVisitorOrganization != null and superVisitorOrganization != ''">
                and combied_supervisor_organization like '%' || #{superVisitorOrganization} || '%'
            </if>
            <if test="fromTime != null">
                and contract.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and contract.create_time &lt;= #{toTime}
            </if>
            and construction.tenant_id = #{tenantId}
        </where>
    </sql>

    <update id="update">
        update so_construction_contract
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="firstpartOrganization != null">
                firstpart_organization = #{firstpartOrganization},
            </if>
            <if test="firstpartRepresentative != null">
                firstpart_representative = #{firstpartRepresentative},
            </if>
            <if test="firstpartPhone != null">
                firstpart_phone = #{firstpartPhone},
            </if>
            <if test="secondpartOrganization != null">
                secondpart_organization = #{secondpartOrganization},
            </if>
            <if test="secondpartRepresentative != null">
                secondpart_representative = #{secondpartRepresentative},
            </if>
            <if test="secondpartPhone != null">
                secondpart_phone = #{secondpartPhone},
            </if>
            <if test="cost != null">
                cost = #{cost},
            </if>
            <if test="workTimeBegin != null">
                work_time_begin = #{workTimeBegin},
            </if>
            <if test="workTimeEnd != null">
                work_time_end = #{workTimeEnd},
            </if>
            <if test="signTime != null">
                sign_time = #{signTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
            update_time = #{updateTime},
            update_user = #{updateUser}
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction_contract
        set code                      = #{code},
            name                      = #{name},
            type                      = #{type},
            firstpart_organization    = #{firstpartOrganization},
            firstpart_representative  = #{firstpartRepresentative},
            firstpart_phone           = #{firstpartPhone},
            secondpart_organization   = #{secondpartOrganization},
            secondpart_representative = #{secondpartRepresentative},
            secondpart_phone          = #{secondpartPhone},
            cost                      = #{cost},
            work_time_begin           = #{workTimeBegin},
            work_time_end             = #{workTimeEnd},
            sign_time                 = #{signTime},
            remark                    = #{remark},
            attachments               = #{attachments},
            update_time               = #{updateTime},
            update_user               = #{updateUser}
        where id = #{id}
    </update>

    <select id="isCodeExists" resultType="boolean">
        select count(1)
        from so_construction_contract outside
        where outside.code = #{code}
          and tenant_id = #{tenantId}
        <if test="id != null and id != ''">
            <!--需要存在，下方不存在是会返回null，null意味着false(不重复)，会出现假是自己(假不重复)-->
            and (select count(1) > 0 from so_construction_contract where id = #{id})
            <!--两个code相等则意味着是自己，不是自己则重复，是自己则放行(false-是自己所以返回未重复，true-不是自己所以返回重复)-->
            and (select not outside.code = code from so_construction_contract where id = #{id})
        </if>
    </select>

    <select id="getConstructionCodeById" resultType="java.lang.String">
        select construction_code
        from so_construction_contract
        where id = #{id}
    </select>

    <select id="isCompleteByContractCode" resultType="boolean">
        <bind name="status" value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@COMPLETED"/>
        <bind name="contractScope" value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_CONTRACT"/>
        select status != #{status}
        from so_construction_task_info
        where construction_code = (select construction_code from so_construction_contract where code = #{code} and tenant_id = #{tenantId})
        and scope = #{contractScope}
          and tenant_id = #{tenantId}
    </select>
</mapper>