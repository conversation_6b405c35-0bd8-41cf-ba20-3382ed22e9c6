package org.thingsboard.server.dao.util.imodel.response;

import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.joda.time.DateTime;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.UUIDBased;
import org.thingsboard.server.dao.model.sql.statistic.StatisticItem;
import org.thingsboard.server.dao.util.imodel.response.annotations.*;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;
import org.thingsboard.server.dao.util.reflection.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

public class ResponseMap extends LinkedHashMap<String, Object> {
    public static final String NAME_SUFFIX = "Name";

    public static final String SOURCE_SUFFIX = "Source";

    public static final String DEPARTMENT_ID_SUFFIX = "DepartmentId";

    public static final String DEPARTMENT_NAME_SUFFIX = "DepartmentName";

    public static final String ORGANIZER_ID_SUFFIX = "OrganizationId";

    public static final String ORGANIZER_NAME_SUFFIX = "OrganizationName";

    public static final String SERIAL_VERSION_UID_FIELD_NAME = "serialVersionUID";

    public ResponseMap() {

    }

    public ResponseMap(Object origin, ReturnHelper returnHelper) {
        this.addAll(origin, returnHelper);
    }

    public void addAll(Object origin, ReturnHelper returnHelper) {
        addAll(origin, returnHelper, true);
    }

    public Object addAll(Object origin, ReturnHelper returnHelper, boolean isOverride) {
        if (origin == null) {
            return origin;
        } else if (origin instanceof Collection || (origin instanceof StatisticItem && !((StatisticItem) origin).flattenSplittable())) {
            Object result = returnHelper.process(origin, null);
            if (result.getClass().equals(ResponseMap.class)) {
                putAll((ResponseMap) result);
                return null;
            }
            return result;
        }
        List<Field> fields = ReflectionUtils.getAllField(origin.getClass());
        for (Field field : fields) {
            if (field.getName().equals(SERIAL_VERSION_UID_FIELD_NAME) || field.getType().getName().startsWith("org.slf4j")) {
                continue;
            }
            Object originalValue = ReflectionUtils.getValue(field, origin);
            if (originalValue instanceof NameDisplayableEnum) {
                String displayName = ((NameDisplayableEnum) originalValue).getDisplayName();
                this.put(field.getName() + NAME_SUFFIX, displayName, isOverride);
            }
            if (originalValue instanceof Date) {
                String formattedDate = formatDateTime((Date) originalValue);
                if (returnHelper.getVersion() > 1) {
                    this.put(field.getName() + NAME_SUFFIX, formattedDate);
                } else {
                    originalValue = formattedDate;
                }
            }
            if (originalValue instanceof UUIDBased) {
                UUIDBased uuid = (UUIDBased) originalValue;
                String uuidStr = UUIDConverter.fromTimeUUID(uuid.getId());
                this.put(field.getName(), uuidStr);
                this.put(field.getName() + SOURCE_SUFFIX, originalValue);
                continue;
            }
            if (field.isAnnotationPresent(Compute.class)) {
                Compute computeAnno = field.getAnnotation(Compute.class);
                String method = computeAnno.value();
                Object computedValue = ReflectionUtils.callMethod(method, origin);
                if (computedValue != null)
                    ReflectionUtils.conditionalSetValue(field, computedValue, origin, true);
                originalValue = ReflectionUtils.getValue(field, origin);
            }
            if (field.isAnnotationPresent(Flatten.class)) {
                Object processedValue = addAll(originalValue, returnHelper, false);
                if (processedValue != null) {
                    this.put(field.getName(), processedValue, isOverride);
                }
                return null;
            } else {
                Object processedValue = returnHelper.process(originalValue, null);
                this.put(field.getName(), processedValue, isOverride);
            }
            resolveAnnotateField(origin, field, returnHelper, isOverride);
        }

        return null;
    }

    public Object put(String key, Object value, boolean isOverride) {
        if (isOverride)
            return this.put(key, value);
        else
            return this.putIfAbsent(key, value);
    }

    private void resolveAnnotateField(Object origin, Field field, ReturnHelper returnHelper, boolean isOverride) {
        if (field.isAnnotationPresent(ParseUsername.class)) {
            ParseUsername userIdAnno = field.getAnnotation(ParseUsername.class);
            String prefix = userIdAnno.value();
            String id = ReflectionUtils.getValue(field, origin);
            // DepartmentMapper mapper = returnHelper.jdbcHelper().getSqlSession().getMapper(DepartmentMapper.class);
            put(handleNamePrefix(field, prefix, null), returnHelper.jdbcHelper().resolveUsername(id), isOverride);
            // String depId = null;
            // if (id != null && userIdAnno.withDepartment()) {
            //     depId = mapper.getDepartmentId(id);
            //     put(handleNamePrefix(field, prefix, DEPARTMENT_ID_SUFFIX), depId, isOverride);
            //     put(handleNamePrefix(field, prefix, DEPARTMENT_NAME_SUFFIX), mapper.getDepartmentName(depId), isOverride);
            // }
            // if (id != null && userIdAnno.withOrganization()) {
            //     if (!userIdAnno.withDepartment())
            //         depId = mapper.getDepartmentId(id);
            //     if (depId != null) {
            //         String orgId = mapper.getDirectOrgByDepartmentId(depId);
            //         put(handleNamePrefix(field, prefix, ORGANIZER_ID_SUFFIX), orgId, isOverride);
            //         put(handleNamePrefix(field, prefix, ORGANIZER_NAME_SUFFIX), mapper.getDepartmentName(orgId), isOverride);
            //     }
            // }
            if (id != null && userIdAnno.withDepartment()) {
                DepartmentInfo departmentInfo = returnHelper.jdbcHelper().resolveDepartmentInfoByUserId(id);
                if (departmentInfo.isDepartment()) {
                    put(handleNamePrefix(field, prefix, DEPARTMENT_ID_SUFFIX), departmentInfo.getId(), isOverride);
                    put(handleNamePrefix(field, prefix, DEPARTMENT_NAME_SUFFIX), departmentInfo.getName(), isOverride);
                }
            }
            if (id != null && userIdAnno.withOrganization()) {
                DepartmentInfo departmentInfo = returnHelper.jdbcHelper().resolveDepartmentInfoByUserId(id);
                if (departmentInfo.isDepartment()) {
                    departmentInfo = returnHelper.jdbcHelper().resolveDepartmentInfo(departmentInfo.getDirectOrgId());
                }
                put(handleNamePrefix(field, prefix, ORGANIZER_ID_SUFFIX), departmentInfo.getId(), isOverride);
                put(handleNamePrefix(field, prefix, ORGANIZER_NAME_SUFFIX), departmentInfo.getName(), isOverride);
            }

        } else if (field.isAnnotationPresent(ParseTenantName.class)) {
            ParseTenantName tenantIdAnno = field.getAnnotation(ParseTenantName.class);
            String prefix = tenantIdAnno.value();
            prefix = handleNamePrefix(field, prefix, null);
            String id = ReflectionUtils.getValue(field, origin);
            put(prefix, returnHelper.jdbcHelper().resolveTenantName(id), isOverride);
        } else if (field.isAnnotationPresent(ParseViaMapper.class)) {
            ParseViaMapper viaMapperAnno = field.getAnnotation(ParseViaMapper.class);
            Class<?> mapperType = viaMapperAnno.value();
            Method mapperMethod = ReflectionUtils.getMethod(viaMapperAnno.method(), new Class[]{field.getType()}, mapperType);
            String prefix = viaMapperAnno.prefix();
            prefix = handleNamePrefix(field, prefix, viaMapperAnno.withoutSuffix() ? Strings.EMPTY : null);
            String id = ReflectionUtils.getValue(field, origin);
            SqlSession sqlSession = returnHelper.jdbcHelper().getSqlSession();
            Object mapperEntity = sqlSession.getMapper(mapperType);
            Object name = ReflectionUtils.callMethod(mapperMethod, new Object[]{id}, mapperEntity);
            put(prefix, name, isOverride);
        }
        if (field.isAnnotationPresent(InfoViaMapper.class)) {
            InfoViaMapper viaMapperAnno = field.getAnnotation(InfoViaMapper.class);
            Class<?> mapperType = viaMapperAnno.mapper();
            String id = ReflectionUtils.getValue(field, origin);
            String prefix = viaMapperAnno.prefix();
            String name = viaMapperAnno.name();
            String methodName = "get" + StringUtils.capitalize(name);
            Method mapperMethod = ReflectionUtils.getMethod(methodName, new Class[]{field.getType()}, mapperType);
            SqlSession sqlSession = returnHelper.jdbcHelper().getSqlSession();
            Object mapperEntity = sqlSession.getMapper(mapperType);
            Object value = ReflectionUtils.callMethod(mapperMethod, new Object[]{id}, mapperEntity);
            if (StringUtils.isNotEmpty(prefix)) {
                name = prefix + StringUtils.capitalize(name);
            }
            put(name, value, isOverride);

            String nextName = viaMapperAnno.nextName();
            if (value != null && StringUtils.isNotEmpty(nextName)) {
                String nextMethodName = "get" + StringUtils.capitalize(nextName);
                mapperMethod = ReflectionUtils.getMethod(nextMethodName, new Class[]{value.getClass()}, mapperType);
                value = ReflectionUtils.callMethod(mapperMethod, new Object[]{value}, mapperEntity);
                if (StringUtils.isNotEmpty(prefix)) {
                    nextName = prefix + StringUtils.capitalize(nextName);
                }
                put(nextName, value, isOverride);
            }
        }

        if (field.isAnnotationPresent(Info.class)) {
            Info infoAnno = field.getAnnotation(Info.class);
            String methodName = infoAnno.name();
            put(methodName, ReflectionUtils.callMethod(methodName, origin), isOverride);
        }
    }

    private String handleNamePrefix(Field field, String prefix, String customSuffix) {
        if (prefix.length() == 0) {
            prefix = field.getName();
            if (prefix.endsWith("Id")) {
                prefix = prefix.substring(0, prefix.length() - 2);
            }
        }
        return customSuffix != null ? prefix + customSuffix : prefix + NAME_SUFFIX;
    }

    private String formatDateTime(Date date) {
        if (date == null)
            return null;
        return new DateTime(date).toString("yyyy-MM-dd HH:mm:ss");
    }

    @Override
    public Object put(String key, Object value) {
        // TODO: [LFT] 发布版本解除封锁
        // if (value == null) {
        //     return null;
        // }
        return super.put(key, value);
    }

    @Override
    public Object putIfAbsent(String key, Object value) {
        // TODO: [LFT] 发布版本删除
        if (get(key) == null) {
            remove(key);
        }

        // TODO: [LFT] 发布版本解除封锁
        // if (value == null) {
        //     return null;
        // }
        return super.putIfAbsent(key, value);
    }

}
