import{B as i,e as d,U as p}from"./pe-B8dP0-Ut.js";import{bZ as f,b_ as l}from"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";function y(n){const t=n.toJSON();return t.attachmentTypes&&(t.attachmentTypes=t.attachmentTypes.join(",")),t.keywords&&(t.keywords=t.keywords.join(",")),t.globalIds&&(t.globalIds=t.globalIds.join(",")),t.objectIds&&(t.objectIds=t.objectIds.join(",")),t.size&&(t.size=t.size.join(",")),t}function A(n,t){const e={};for(const o of t){const{parentObjectId:a,parentGlobalId:s,attachmentInfos:c}=o;for(const r of c){const{id:u}=r,h=i(d(`${n.path}/${a}/attachments/${u}`)),m=f.fromJSON(r);m.set({url:h,parentObjectId:a,parentGlobalId:s}),e[a]?e[a].push(m):e[a]=[m]}}return e}function $(n,t,e){let o={query:l({...n.query,f:"json",...y(t)})};return e&&(o={...e,...o,query:{...e.query,...o.query}}),p(n.path+"/queryAttachments",o).then(a=>a.data.attachmentGroups)}async function g(n,t,e){const{objectIds:o}=t,a=[];for(const s of o)a.push(p(n.path+"/"+s+"/attachments",e));return Promise.all(a).then(s=>o.map((c,r)=>({parentObjectId:c,attachmentInfos:s[r].data.attachmentInfos})))}export{$ as executeAttachmentQuery,g as fetchAttachments,A as processAttachmentQueryResult};
