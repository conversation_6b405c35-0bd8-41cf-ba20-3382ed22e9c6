<!--流量分析 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree ref="refTree" :tree-data="TreeData"></SLTree>
    </template>
    <CardSearch ref="refSearch" :config="SearchConfig" />
    <SLCard class="card-table" title=" ">
      <template #right>
        <el-radio-group
          v-model="state.activeName"
          @change="() => refreshData()"
        >
          <el-radio-button label="echarts">
            <Icon icon="clarity:line-chart-line" />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon icon="material-symbols:table" />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div ref="refDiv" class="content">
        <div
          v-if="state.activeName === 'echarts'"
          ref="refDiv"
          class="chart-box"
        >
          <!-- 图表模式 -->
          <VChart
            ref="refChart"
            :theme="useAppStore().isDark ? 'dark' : 'light'"
            :option="state.chartOption"
          ></VChart>
        </div>
        <!-- 列表模式 -->
        <FormTable
          v-if="state.activeName === 'list'"
          ref="refCardTable"
          class="card-table"
          :config="TableConfig"
        />
      </div>
    </SLCard>
  </TreeBox>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import { useAppStore } from '@/store';
import { IECharts } from '@/plugins/echart';
import { useDetector } from '@/hooks/echarts';
import { usePartition } from '@/hooks/arcgis';
import { SLMessage } from '@/utils/Message';
import {
  GetPartitionDeviceFlowOrPressure,
  ExportPartitionDeviceFlowOrPressure,
  IQueryPartitionDeviceFlowOrPressureProperties
} from '@/api/mapservice/dma';
import { formatterMonth, formatterYear } from '@/utils/GlobalHelper';
import { ExportReport } from '@/views/yinshou/baobiao';

const state = reactive<{
  chartOption: any;
  activeName: string;
}>({
  chartOption: null,
  activeName: 'list'
});

const refChart = ref<IECharts>();
const refSearch = ref<ICardSearchIns>();
const refCardTable = ref<ICardTableIns>();
const tableData = reactive<any[]>([]);
const TreeData = reactive<SLTreeConfig>({
  data: [],
  title: '选择设备',
  showCheckbox: true,
  handleCheck(ids, data) {
    TreeData.checkedKeys = data.checkedKeys || [];
    TreeData.checkedNodes = data.checkedNodes || [];
    resetTableColumn();
  }
  // treeNodeHandleClick: (data: NormalOption) => {
  //   if (TreeData.currentProject !== data) {
  //     TreeData.currentProject = data
  //     refreshData()
  //   }
  // }
});
// 搜索栏初始化配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'daterange',
    queryType: '15m',
    daterange: [
      moment().subtract(1, 'd').format('YYYY-MM-DD'),
      moment().format('YYYY-MM-DD')
    ],
    month: moment().format(formatterMonth),
    year: moment().format(formatterYear)
  },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '按年', value: 'year' },
        { label: '按月', value: 'month' },
        { label: '按时间段', value: 'daterange' }
      ],
      label: '选择方式',
      onChange: (value) => {
        if (!refSearch.value?.queryParams) return;
        refSearch.value.queryParams.queryType =
          value === 'year' ? '1nc' : value === 'month' ? '1d' : '15min';
      }
    },
    {
      hidden: true,
      handleHidden: (params, query, config) =>
        (config.hidden = params.type !== 'year'),
      type: 'year',
      label: '选择年份',
      field: 'year',
      clearable: false,
      format: formatterYear,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      handleHidden(params, query, formItem) {
        formItem.hidden = params.type !== 'year';
      },
      type: 'select',
      label: '时间间隔:',
      field: 'queryType',
      clearable: false,
      options: [{ label: '1月', value: '1nc' }],
      itemContainerStyle: {
        width: '180px'
      }
    },
    {
      hidden: true,
      handleHidden: (params, query, config) =>
        (config.hidden = params.type !== 'month'),
      type: 'month',
      label: '选择月份',
      field: 'month',
      clearable: false,
      format: formatterMonth,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      handleHidden(params, query, formItem) {
        formItem.hidden = params.type !== 'month';
      },
      type: 'select',
      label: '时间间隔:',
      field: 'queryType',
      clearable: false,
      options: [{ label: '1天', value: '1d' }],
      itemContainerStyle: {
        width: '180px'
      }
    },
    {
      hidden: true,
      handleHidden: (params, query, config) =>
        (config.hidden = params.type !== 'daterange'),
      type: 'daterange',
      label: '选择日期',
      field: 'daterange',
      clearable: false,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      handleHidden(params, query, formItem) {
        formItem.hidden = params.type !== 'daterange';
      },
      type: 'select',
      label: '时间间隔:',
      field: 'queryType',
      clearable: false,
      options: [
        { label: '1分钟', value: '1m' },
        { label: '5分钟', value: '5m' },
        { label: '15分钟', value: '15m' },
        { label: '30分钟', value: '30m' },
        { label: '1小时', value: '1h' }
      ],
      itemContainerStyle: {
        width: '180px'
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => {
            // console.log(SearchConfig.defaultParams)
            refreshData();
          },
          iconifyIcon: 'ep:search'
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm();
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          hide: () => {
            return state.activeName !== 'list';
          },
          click: () => {
            refreshData(true);
          },
          iconifyIcon: 'ep:download'
        }
      ]
    }
  ]
});

// 初始化列表配置数据
const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  pagination: {
    page: 1,
    limit: 200,
    pageSize: [100, 200, 300, 500],
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      TableConfig.dataList = tableData?.slice((page - 1) * size, page * size);
    }
  }
});

const getCheckedDevices = () => {
  const devices =
    TreeData.checkedNodes?.filter((item) => item.data.type === '3') || [];
  return devices;
};
const getSubColumns = () => {
  const devices = getCheckedDevices();
  const subColumns: IFormTableColumn[] =
    devices.map((item) => {
      return {
        minWidth: 120,
        label: item.label,
        prop: item.data?.deviceId,
        unit: '(m³/h)'
      } as IFormTableColumn;
    }) || [];
  return subColumns;
};
const resetTableColumn = () => {
  const subColumns = getSubColumns();
  TableConfig.columns = [
    { minWidth: 160, label: '时间', prop: 'time' },
    ...subColumns,
    { label: '最小站点', minWidth: 120, prop: 'minPoint' },
    { label: '最小值(m³/h)', minWidth: 120, prop: 'minValue' },
    { label: '最大站点', minWidth: 120, prop: 'maxPoint' },
    { label: '最大值(m³/h)', minWidth: 120, prop: 'maxValue' },
    { label: '平均值(m³/h)', minWidth: 120, prop: 'avgValue' },
    { label: '合计(m³/h)', minWidth: 120, prop: 'sumValue' }
  ];
};
// 刷新列表
const refreshData = async (isExport?: boolean) => {
  if (!TreeData.checkedNodes?.length) {
    SLMessage.warning('请先选择设备');
    return;
  }
  const query = refSearch.value?.queryParams || {};
  const params: IQueryPartitionDeviceFlowOrPressureProperties = {
    page: TableConfig.pagination.page,
    size: TableConfig.pagination.limit,
    deviceIdList: getCheckedDevices().map((item) => item.data?.deviceId),
    type: query.type === 'daterange' ? 'time' : query.type,
    interval: query.queryType,
    start:
      query.daterange?.[0] &&
      moment(query.daterange?.[0]).startOf('D').valueOf(),
    end:
      query.daterange?.[1] && moment(query.daterange?.[1]).endOf('D').valueOf(),
    month: query.month,
    year: query.year,
    queryType: 'flow'
  };
  if (isExport) {
    const res = await ExportPartitionDeviceFlowOrPressure(params);
    ExportReport(res.data, '流量分析报表');
  } else {
    const res = await GetPartitionDeviceFlowOrPressure(params);
    const data = res.data?.data;
    TableConfig.dataList =
      data?.data?.map((item) => {
        const obj = {
          ...item
        };
        item.data?.map((o) => {
          obj[o.id] = o.value;
        });
        return obj;
      }) || [];
    TableConfig.pagination.total = data?.total || 0;
    refreshChart(getSubColumns());
  }
};

// 配置加载图表数据
const refreshChart = (subColumns: any[]) => {
  if (state.activeName !== 'echarts') return;
  const chartOption: any = {
    // backgroundColor: useAppStore().isDark ? '#222536' : 'transparent',
    grid: {
      left: 50,
      right: 50,
      top: 50,
      bottom: 80
    },
    legend: {
      type: 'scroll',
      textStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['--'],
      splitLine: {
        show: false
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100
      }
    ],
    yAxis: [
      // {
      //   position: 'left',
      //   type: 'value',
      //   name: '出口压力(Mpa)',
      //   axisLine: {
      //     show: true,
      //     lineStyle: {
      //       // color: '#ffffff' // '#333'
      //       types: 'solid'
      //     }
      //   },
      //   axisLabel: {
      //     show: true,
      //     textStyle: {
      //       color: '#656b84' // 更改坐标轴文字颜色
      //       // fontSize: 14 //更改坐标轴文字大小
      //     }
      //   },
      //   splitLine: {
      //     lineStyle: {
      //       color: useAppStore().isDark ? '#303958' : '#ccc',
      //       type: [5, 10],
      //       dashOffset: 5
      //     }
      //   }
      // },
      {
        // position: 'right',
        type: 'value',
        name: '瞬时流量(m³/h)',
        axisLine: {
          show: true,
          lineStyle: {
            // color: '#ffffff' // '#333'
            types: 'solid'
          }
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#656b84' // 更改坐标轴文字颜色
            // fontSize: 14 //更改坐标轴文字大小
          }
        },
        splitLine: {
          lineStyle: {
            color: useAppStore().isDark ? '#303958' : '#ccc',
            type: [5, 10],
            dashOffset: 5
          }
        }
      }
    ],
    series: []
  };
  const list = TableConfig.dataList.filter((item) => !!item.time);
  const xData = list.map((row) => row.time);
  chartOption.xAxis.data = xData;
  const datas: Record<string, number[]> = {};
  list.map((item) => {
    item.data?.map((o) => {
      if (datas[o.id]) datas[o.id].push(o.value);
      else {
        datas[o.id] = [o.value];
      }
    });
  });
  chartOption.series = subColumns.map((info) => {
    return {
      name: info.label,
      smooth: true,
      data: datas[info.prop] || [],
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    };
  });
  refChart.value?.clear();
  state.chartOption = chartOption;
};

const detector = useDetector();
const partition = usePartition();
const refDiv = ref();
onMounted(async () => {
  await partition.getDeviceTree({ type: 'flow' });
  TreeData.data = partition.DeviceTree.value;
  detector.listenToMush(refDiv.value, () => {
    refChart.value?.resize();
  });
});
</script>

<style lang="scss" scoped>
.chart-box,
.content {
  width: 100%;
  height: 100%;
}
</style>
