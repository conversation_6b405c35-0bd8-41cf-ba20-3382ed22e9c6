package org.thingsboard.server.controller.leakDetectionManagement;

import lombok.Data;

/**
 * 表计异常详情数据传输对象
 */
@Data
public class MeterAbnormalDetailDto {
    /**
     * 水表编号
     */
    private String meterId;
    
    /**
     * 用户名称
     */
    private String userName;
    
    /**
     * 用户地址
     */
    private String address;
    
    /**
     * 异常类型
     */
    private String abnormalType;
    
    /**
     * 电池电量
     */
    private String batteryLevel;
    
    /**
     * 信号强度
     */
    private String signalStrength;
    
    /**
     * 异常程度：0-4
     * 0: 轻微
     * 1: 一般
     * 2: 中度
     * 3: 严重
     * 4: 极严重
     */
    private int abnormalLevel;
} 