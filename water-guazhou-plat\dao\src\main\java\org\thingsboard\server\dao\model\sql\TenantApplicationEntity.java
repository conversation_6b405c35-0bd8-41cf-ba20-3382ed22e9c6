package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TENANT_APPLICATION_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class TenantApplicationEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TENANT_APPLICATION_NAME)
    private String name;

    @Column(name = ModelConstants.TENANT_APPLICATION_TYPE)
    private String type;

    @Column(name = ModelConstants.TENANT_APPLICATION_RESOURCE_TYPE)
    private String resourceType;

    @Column(name = ModelConstants.TENANT_APPLICATION_APPLICATION)
    private String applicationUrl;

    @Column(name = ModelConstants.TENANT_APPLICATION_ORDER_NUM)
    private Integer orderNum;

    @Column(name = ModelConstants.TENANT_APPLICATION_REMARK)
    private String remark;

    @Column(name = ModelConstants.TENANT_APPLICATION_IMG)
    private String img;

    @Column(name = ModelConstants.TENANT_APPLICATION_ICON)
    private String icon;

    @Column(name = ModelConstants.TENANT_APPLICATION_CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_APPLICATION_TENANT_ID)
    private String tenantId;

    @Transient
    private List<String> menuIdList;

}
