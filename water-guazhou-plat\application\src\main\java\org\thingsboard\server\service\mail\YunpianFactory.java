package org.thingsboard.server.service.mail;

import com.yunpian.sdk.YunpianClient;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2020/2/24 9:53
 */
public class YunpianFactory {


    private static YunpianClient yunpianClient;


    public static YunpianClient getYunpianClient(String key) {
        return new YunpianClient(key, createProperties()).init();
    }


    /**
     * 创建新的配置文件以覆盖云片网默认配置文件，此处为将配置文件中的请求方式由https转换为http
     *
     * @return
     */
    private static Properties createProperties() {
        Properties prop = new Properties();
        prop.setProperty("yp.user.host", "http://sms.yunpian.com");
        prop.setProperty("yp.sign.host", "http://sms.yunpian.com");
        prop.setProperty("yp.tpl.host", "http://sms.yunpian.com");
        prop.setProperty("yp.sms.host", "http://sms.yunpian.com");
        prop.setProperty("yp.short_url.host", "http://sms.yunpian.com");
        return prop;
    }

}
