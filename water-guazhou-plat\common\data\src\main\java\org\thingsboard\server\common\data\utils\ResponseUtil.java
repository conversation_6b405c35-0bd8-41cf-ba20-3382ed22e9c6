/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.utils;

import com.influxdb.query.FluxTable;
import org.thingsboard.server.common.data.constantsAttribute.LoocalMap;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;
import org.thingsboard.server.common.data.utils.TimeDiff;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Map;


public class ResponseUtil {
    /**
     * 根据换表时间筛选数据，返回一个时间-数据的集合
     *
     * @param fluxTable
     * @param changemeterMap
     * @return
     */
    public static ArrayList<ArrayList<LoocalMap>> handleResponseTs(FluxTable fluxTable, Map<Long, Long> changemeterMap) {
        ArrayList<ArrayList<LoocalMap>> result = new ArrayList<>();
        if (changemeterMap==null||changemeterMap.isEmpty()) {
            ArrayList<LoocalMap> loocalMaps = new ArrayList<>();
            fluxTable.getRecords().forEach(entry -> loocalMaps.add(new LoocalMap((entry.getTime().toEpochMilli()), new BigDecimal(String.valueOf(entry.getValue())))));
            result.add(loocalMaps);
        } else {
            ArrayList<LoocalMap> loocalMaps = new ArrayList<>();
            final int[] count = {0};
            fluxTable.getRecords().forEach(entry -> {
                boolean flag = false;
                count[0]++;
                for (Long j : changemeterMap.keySet()) {
                    if (TimeDiff.betweenTwoTimes(entry.getTime().toEpochMilli(), j, changemeterMap.get(j))) {
                        flag = true;
                    }
                }
                if (!flag) {
                    loocalMaps.add(new LoocalMap(entry.getTime().toEpochMilli(), new BigDecimal(String.valueOf(entry.getValue()))));
                }
                //此处添加当循环到最后一条的时候同样把map添加到返回结果集中
                if ((flag && !loocalMaps.isEmpty()) || (count[0] == fluxTable.getRecords().size() && !loocalMaps.isEmpty())) {
                    ArrayList<LoocalMap> loocalMapArrayList = new ArrayList<>();
                    loocalMapArrayList.addAll(loocalMaps);
                    result.add(loocalMapArrayList);
                    loocalMaps.clear();
                }
            });
        }
        return result;
    }
}
