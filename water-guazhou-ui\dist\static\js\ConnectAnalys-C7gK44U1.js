import{d as U,r as S,c as M,b as y,X,Q as H,g as K,n as W,q as N,i as O,_ as Y,C as Z}from"./index-r0dFAfgr.js";import{w as ee}from"./MapView-DaoQedLH.js";import{s as te}from"./FeatureHelper-Da16o0mu.js";import{b as re}from"./GPHelper-fLrvVD-A.js";import{e as ae,i as ie}from"./IdentifyHelper-RJWmLn49.js";import{g as oe,a as ne,e as se}from"./LayerHelper-Cn-iiqxI.js";import{e as pe,i as le}from"./QueryHelper-ILO3qZqg.js";import{s as A}from"./ToolHelper-BiiInOzB.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import"./Point-WxyopZva.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import me from"./PipeDetail-CTBPYFJW.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./scaleUtils-DgkF6NQH.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./project-DUuzYgGl.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ExportImageParameters-BiedgHNY.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./DateFormatter-Bm9a68Ax.js";import"./config-fy91bijz.js";const ce=U({__name:"ConnectAnalys",props:{view:{},telport:{}},setup(B){const n=B,r=S({currentOperate:"",tabs:[]}),a={},P=M(),T=M(),L=S({columns:[{label:"管线类型",prop:"layerName"},{label:"管线编号",prop:"value",formatter:e=>{var t;return(t=e.attributes)==null?void 0:t.OBJECTID}}],dataList:[],pagination:{hide:!0}}),b=S({data:[],columns:[]}),E=S({labelPosition:"top",gutter:12,group:[{fieldset:{desc:"选取管线"},fields:[{type:"btn-group",btns:[{perm:!0,type:"warning",styles:{width:"100%"},text:()=>r.currentOperate==="picking"?"正在选取管线":"点击选择管线",loading:()=>r.currentOperate==="picking",disabled:()=>r.currentOperate==="picking"||r.currentOperate==="analysing"||r.currentOperate==="viewingdetail",click:()=>J()}]},{type:"table",label:"所选管线数据概览",style:{height:"80px"},config:L},{type:"checkbox",field:"usePreventLayer",options:[{label:"使用障碍图层",value:"usePreventLayer"}]}]},{fieldset:{desc:"执行分析"},fields:[{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},text:()=>r.currentOperate==="analysing"?"正在分析":"开始分析",loading:()=>r.currentOperate==="analysing",disabled:()=>r.currentOperate==="analysing"||r.currentOperate==="detailing"||r.currentOperate==="picking"||!L.dataList.length,click:()=>Q()}]}]},{fieldset:{desc:"分析结果"},fields:[{type:"checkbox",label:"分析结果概览",field:"viewInMap",options:[{label:"地图显示",value:"viewInMap"}],onChange:e=>{a.resultLayer&&(a.resultLayer.visible=!!e.length)}},{type:"attr-table",config:b},{type:"btn-group",itemContainerStyle:{marginTop:"20px",marginBottom:"8px"},btns:[{perm:!0,text:()=>r.currentOperate==="detailing"?"正在查询":"查看详细结果",click:()=>V(),loading:()=>r.currentOperate==="detailing",disabled:()=>r.currentOperate!=="analysed"&&r.currentOperate!=="viewingdetail",styles:{width:"100%"}}]},{type:"btn-group",btns:[{perm:!0,type:"danger",text:"清除所有",disabled:()=>r.currentOperate==="analysing"||r.currentOperate==="detailing",click:()=>G(),styles:{width:"100%"}}]}]}],defaultValue:{viewInMap:["viewInMap"],usePreventLayer:["usePreventLayer"]}}),J=()=>{var e;n.view&&(A("crosshair"),r.currentOperate="picking",a.graphicLayer=oe(n.view,{id:"connect-analys",title:"连通性分析标注"}),a.mapClick=(e=n.view)==null?void 0:e.on("click",async t=>{var i,o;(i=a.graphicLayer)==null||i.removeAll(),A("");try{await $(t)}catch{y.error("拾取失败")}(o=a.mapClick)!=null&&o.remove&&a.mapClick.remove(),a.mapClick=void 0,r.currentOperate="picked"}))},$=async e=>{var m,d;if(!n.view)return;const i=(m=(await ae(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,ie({tolerance:3,geometry:e.mapPoint,mapExtent:n.view.extent,width:n.view.width,layerIds:ne(n.view)}))).results)==null?void 0:m.filter(s=>s.geometryType!=="esriGeometryPoint")[0];if(!i)return y.warning("没有查询到管线"),r.currentOperate="picked";const o=i&&[i]||[];if(L.dataList=o.map(s=>({attributes:s.feature.attributes,layerId:s.layerId,layerName:s.layerName})),a.identifyResult=i,i){const s=i.feature;s&&(s.symbol=te("polyline")),(d=a.graphicLayer)==null||d.add(s)}r.currentOperate="picked"},Q=async()=>{var e,t,i,o,m,d,s,w,v;r.currentOperate="analysing",a.resultLayer&&((e=n.view)==null||e.map.remove(a.resultLayer));try{const u=(i=(t=(await X(a.identifyResult.layerId)).data)==null?void 0:t.result)==null?void 0:i.rows,k=(u==null?void 0:u.length)&&u[0].layerdbname,I=(o=a.identifyResult)==null?void 0:o.feature.attributes.OBJECTID,l=await re(k,I,!!((d=(m=T.value)==null?void 0:m.dataForm.usePreventLayer)!=null&&d.length));if(await l.waitForJobCompletion({statusCallback:f=>{a.jobid=f.jobId}}),l.jobStatus==="job-succeeded"){a.jobid=l.jobId;const f=await l.fetchResultMapImageLayer(l.jobId);a.resultLayer=f,a.resultLayer.title="连通性分析结果";const C=se(n.view);(s=n.view)==null||s.map.add(a.resultLayer,C);const p=(await l.fetchResultData("summary")).value;(p==null?void 0:p.code)!==1e4?y.error(p.error):(a.resultSummary=(w=p==null?void 0:p.result)==null?void 0:w.summary,q((v=p==null?void 0:p.result)==null?void 0:v.summary));const h=a.resultSummary.layersummary.map(g=>({label:g.layername,name:g.layername}));await F(h,0),r.tabs=h}else l.jobStatus==="job-cancelled"?y.info("已取消分析"):l.jobStatus==="job-cancelling"?y.info("任务正在取消"):l.jobStatus==="job-failed"&&y.info("分析失败，请联系管理员")}catch{y.error("系统错误"),r.currentOperate="picked"}r.currentOperate="analysed"},q=e=>{var w,v,x,u,k,I,l,f,C,_,p,h;if(!((w=e==null?void 0:e.layersummary)!=null&&w.length))return;if((v=e==null?void 0:e.layersummary)!=null&&v.length){const g={},j=[];e.layersummary.forEach(c=>{var R;c.geometrytype==="esriGeometryPoint"?(g[c.layerdbname]=c.count+"个",j.push([{label:c.layername,prop:c.layerdbname}])):(g[c.layerdbname]=(((R=c.length)==null?void 0:R.toFixed(2))||0)+"米",j.push([{label:c.layername,prop:c.layerdbname}]))}),b.data=g,b.columns=j}let t=e.xmin||((u=(x=n.view)==null?void 0:x.extent)==null?void 0:u.xmin),i=e.xmax||((I=(k=n.view)==null?void 0:k.extent)==null?void 0:I.xmax),o=e.ymin||((f=(l=n.view)==null?void 0:l.extent)==null?void 0:f.ymin),m=e.ymax||((_=(C=n.view)==null?void 0:C.extent)==null?void 0:_.ymax);const d=i-t,s=m-o;t-=d/2,i+=d/2,o-=s/2,m+=s/2,(h=n.view)==null||h.goTo(new ee({xmin:t,ymin:o,xmax:i,ymax:m,spatialReference:(p=n.view)==null?void 0:p.spatialReference}))},V=async()=>{var e;try{if(!r.tabs.length){y.warning("暂无详细信息");return}(e=P.value)==null||e.openDialog()}catch(t){y.error("查询失败"),console.log(t)}},F=async(e,t)=>{if(t<e.length){const i=e[t];i.data=await D(i.name,0),t<e.length-1&&await F(e,++t)}},D=async(e,t)=>{var i;try{let o=await pe((((i=a.resultLayer)==null?void 0:i.url)||"")+"/"+t,le({where:"layername='"+e+"'",orderByFields:["OBJECTID asc"],returnGeometry:!1}));return o===null&&(o=await D(e,++t)),o}catch{return[]}},z=async()=>{var e;n.view&&((e=P.value)==null||e.extentTo(n.view))},G=()=>{var e,t,i,o;(e=a.graphicLayer)==null||e.removeAll(),a.graphicLayer&&((t=n.view)==null||t.map.remove(a.graphicLayer)),a.resultLayer&&((i=n.view)==null||i.map.remove(a.resultLayer)),(o=a.mapClick)!=null&&o.remove&&a.mapClick.remove(),a.mapClick=void 0,r.currentOperate="",L.dataList=[],b.data=[],b.columns=[]};return H(()=>{G()}),(e,t)=>{const i=Y;return K(),W("div",null,[N(i,{ref_key:"refForm",ref:T,config:O(E)},null,8,["config"]),N(me,{ref_key:"refDetail",ref:P,tabs:O(r).tabs,telport:e.telport,onRefreshed:t[0]||(t[0]=o=>O(r).currentOperate="viewingdetail"),onRefreshing:t[1]||(t[1]=o=>O(r).currentOperate="detailing"),onClose:t[2]||(t[2]=o=>O(r).currentOperate="analysed"),onRowdblclick:z},null,8,["tabs","telport"])])}}}),At=Z(ce,[["__scopeId","data-v-0abaa109"]]);export{At as default};
