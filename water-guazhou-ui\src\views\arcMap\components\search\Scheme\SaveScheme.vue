<template>
  <DialogForm ref="refDialog" :config="DialogFormConfig"></DialogForm>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store';
import { removeSlash } from '@/utils/removeIdSlash';

const emit = defineEmits(['submit']);
const refDialog = ref<IDialogFormIns>();
const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '保存方案',
  dialogWidth: 500,
  draggable: true,
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '方案名称',
          field: 'name',
          rules: [{ required: true, message: '请输入方案名称' }]
        },
        {
          type: 'radio',
          label: '方案权限',
          field: 'userId',
          options: [
            {
              label: '仅自己可见',
              value: removeSlash(useUserStore().user?.id?.id)
            },
            { label: '所有人可见', value: 'all' }
          ],
          rules: [{ required: true, message: '请选择权限' }]
        },
        { type: 'textarea', label: '备注', field: 'remark' }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: { userId: removeSlash(useUserStore().user?.id?.id) },
  submit: (params) => {
    emit('submit', params);
  }
});
const openDialog = () => {
  refDialog.value?.openDialog();
};
const closeDialog = () => {
  refDialog.value?.closeDialog();
};
defineExpose({
  openDialog,
  closeDialog
});
</script>
<style lang="scss" scoped></style>
