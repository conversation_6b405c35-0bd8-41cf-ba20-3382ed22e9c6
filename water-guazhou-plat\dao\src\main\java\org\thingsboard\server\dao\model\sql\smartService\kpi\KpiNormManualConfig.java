package org.thingsboard.server.dao.model.sql.smartService.kpi;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * kpi指标配置
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-15
 */
@TableName("tb_service_kpi_norm_manual_config")
@Data
public class KpiNormManualConfig {

    @TableId
    private String id;

    private String month;

    private String seatsId;

    private transient String seatsName;

    private String normId;

    private transient String normName;

    private transient Double weight;

    private Integer score;

    private String remark;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

}
