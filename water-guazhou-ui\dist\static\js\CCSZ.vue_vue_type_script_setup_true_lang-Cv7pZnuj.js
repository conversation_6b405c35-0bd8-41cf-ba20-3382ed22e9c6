import{e as c}from"./zhandian-YaGuQZe6.js";import{d as i,r as m,o as p,g as d,h as f,i as u,a0 as _,l}from"./index-r0dFAfgr.js";import S from"./scroll-Yl2_tWHV.js";const D=i({__name:"CCSZ",setup(g){const e=m({data:[]}),r=async()=>{var a,o,n;const s=await c({stationType:"水质监测站",projectId:(o=(a=_().navSelectedRange)==null?void 0:a.data)==null?void 0:o.id});e.data=((n=s.data)==null?void 0:n.map(t=>({...t,time:t.time&&l(t.time,"YYYY-MM-DD HH:mm:ss").format("HH:mm:ss")})))||[]};return p(()=>{r()}),(s,a)=>(d(),f(S,{config:u(e).data},null,8,["config"]))}});export{D as _};
