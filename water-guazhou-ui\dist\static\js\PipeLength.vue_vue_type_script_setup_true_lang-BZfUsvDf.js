import{d as F,r as g,c as S,o as x,Q as q,g as G,h as C,i as B,X as M,b as _,_ as P,W as I}from"./index-r0dFAfgr.js";import{e as N}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as A,a as E}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{u as O}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{P as D}from"./pipe-nogVzCHG.js";import{E as k}from"./StatisticsHelper-D-s_6AyQ.js";const te=F({__name:"AreaMeasure",props:{view:{},telport:{}},setup(h){const o=h,n=g({measuring:!1,disabled:!0}),a={},s=S(),m=g({labelWidth:60,group:[{fields:[{type:"text",field:"area",label:"面积",style:{marginRight:"12px"},extraFormItem:[{type:"select",field:"unit",width:"100px",options:[{label:"公顷",value:"hectares"},{label:"平方米",value:"square-meters"},{label:"平方公里",value:"square-kilometers"}],onChange:()=>f()}]},{type:"btn-group",btns:[{perm:!0,text:"新测量",type:"default",loading:()=>n.measuring,disabled:()=>n.disabled,click:()=>b()}]}]}],gutter:12,defaultValue:{unit:"square-meters"}}),{initSketch:v,destroySketch:w}=O(),f=async()=>{var e;if(!(!a.queryGeometry||!s.value)){n.measuring=!0;try{const r=N(a.queryGeometry.rings[0],s.value.dataForm.unit||"square-meters",(e=o.view)==null?void 0:e.spatialReference);s.value.dataForm.area=r.toFixed(2)}catch(r){console.dir(r)}n.measuring=!1}},d=e=>{var r;e.state==="complete"&&(a.queryGeometry=(r=e.graphics[0])==null?void 0:r.geometry,f())},b=()=>{var e,r;(e=a.graphicsLayer)==null||e.removeAll(),(r=a.sketch)==null||r.create("polygon")},L=async()=>{var c,p,y;if(!o.view)return;const e=E(o.view),l=((p=(c=(await M(e)).data)==null?void 0:c.result)==null?void 0:p.rows)||[],t=[];if(l.map(i=>{(i.geometrytype==="esriGeometryPolyline"||i.layername.indexOf("立管")>-1)&&(t==null||t.push({label:i.layername,value:i.layerid,id:i.layerid,data:i}))}),!t.length){_.error("未加载管线服务,功能不可用");return}n.disabled=!1;const u=m.group[0].fields[0];u&&(u.options=t),s.value&&(s.value.dataForm.pipeLayer=(y=t[0])==null?void 0:y.value)};return x(async()=>{o.view&&(L(),a.graphicsLayer=A(o.view,{id:"pipe-length",title:"管线面积测量"}),a.sketch=v(o.view,a.graphicsLayer,{updateCallBack:d,createCallBack:d}))}),q(()=>{var e;w(),a.graphicsLayer&&((e=o.view)==null||e.map.remove(a.graphicsLayer))}),(e,r)=>{const l=P;return G(),C(l,{ref_key:"refForm",ref:s,config:B(m)},null,8,["config"])}}}),se=F({__name:"PipeLength",props:{view:{},telport:{}},setup(h){const o=h,n=g({measuring:!1,disabled:!0}),a={},s=S(),m=g({group:[{fields:[{type:"select",clearable:!1,options:[],label:"管线图层",field:"pipeLayer"},{type:"text",field:"pipeLength",label:"管线长度",unit:"米"},{type:"btn-group",btns:[{perm:!0,text:"新测量",type:"default",loading:()=>n.measuring,disabled:()=>n.disabled,click:()=>b()}]}]}],gutter:12}),{initSketch:v,destroySketch:w}=O(),f=async()=>{var r,l,t,u;if(!a.queryGeometry)return;n.measuring=!0;const e=(r=s.value)==null?void 0:r.dataForm.pipeLayer;try{const c=await D({usertoken:I().gToken,layerids:JSON.stringify(e!==void 0?[e]:[]),group_fields:JSON.stringify([]),statistic_field:k.ShapeLen,statistic_type:"2",where:"1=1",geometry:a.queryGeometry,f:"pjson"});if(c.data.code===1e4){const p=(t=(l=c.data)==null?void 0:l.result)==null?void 0:t.rows,y=p.length&&((u=p[0].rows)==null?void 0:u.length)&&p[0].rows[0];s.value&&(s.value.dataForm.pipeLength=y&&y[k.ShapeLen])}else _.error("统计失败"),s.value&&(s.value.dataForm.pipeLength=void 0)}catch(c){console.dir(c)}n.measuring=!1},d=e=>{var r;e.state==="complete"&&(a.queryGeometry=(r=e.graphics[0])==null?void 0:r.geometry,f())},b=()=>{var e,r;(e=a.graphicsLayer)==null||e.removeAll(),(r=a.sketch)==null||r.create("polygon")},L=async()=>{var c,p,y;if(!o.view)return;const e=E(o.view),l=((p=(c=(await M(e)).data)==null?void 0:c.result)==null?void 0:p.rows)||[],t=[];if(l.map(i=>{(i.geometrytype==="esriGeometryPolyline"||i.layername.indexOf("立管")>-1)&&(t==null||t.push({label:i.layername,value:i.layerid,id:i.layerid,data:i}))}),!t.length){_.error("未加载管线服务,功能不可用");return}n.disabled=!1;const u=m.group[0].fields[0];u&&(u.options=t),s.value&&(s.value.dataForm.pipeLayer=(y=t[0])==null?void 0:y.value)};return x(async()=>{o.view&&(L(),a.graphicsLayer=A(o.view,{id:"pipe-length",title:"管线长度测量"}),a.sketch=v(o.view,a.graphicsLayer,{updateCallBack:d,createCallBack:d}))}),q(()=>{var e;w(),a.graphicsLayer&&((e=o.view)==null||e.map.remove(a.graphicsLayer))}),(e,r)=>{const l=P;return G(),C(l,{ref_key:"refForm",ref:s,config:B(m)},null,8,["config"])}}});export{se as _,te as a};
