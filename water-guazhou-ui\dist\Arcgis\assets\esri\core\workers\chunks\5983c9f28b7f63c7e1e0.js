"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[739],{57435:(e,t,r)=>{r.d(t,{Z:()=>p});var s=r(43697),o=r(46791),i=r(70586),n=(r(80442),r(20102),r(92604),r(26258),r(87538)),l=r(5600),a=(r(75215),r(67676),r(52011));let u=class extends o.Z{constructor(e){super(e),this.getCollections=null}initialize(){this.own((0,n.EH)((()=>this._refresh())))}destroy(){this.getCollections=null}_refresh(){const e=(0,i.pC)(this.getCollections)?this.getCollections():null;if((0,i.Wi)(e))return void this.removeAll();let t=0;for(const r of e)(0,i.pC)(r)&&(t=this._processCollection(t,r));this.splice(t,this.length)}_createNewInstance(e){return new o.Z(e)}_processCollection(e,t){if(!t)return e;const r=this.itemFilterFunction?this.itemFilterFunction:e=>!!e;for(const s of t)if(s){if(r(s)){const t=this.indexOf(s,e);t>=0?t!==e&&this.reorder(s,e):this.add(s,e),++e}if(this.getChildrenFunction){const t=this.getChildrenFunction(s);if(Array.isArray(t))for(const r of t)e=this._processCollection(e,r);else e=this._processCollection(e,t)}}return e}};(0,s._)([(0,l.Cb)()],u.prototype,"getCollections",void 0),(0,s._)([(0,l.Cb)()],u.prototype,"getChildrenFunction",void 0),(0,s._)([(0,l.Cb)()],u.prototype,"itemFilterFunction",void 0),u=(0,s._)([(0,a.j)("esri.core.CollectionFlattener")],u);const p=u},42756:(e,t,r)=>{r.r(t),r.d(t,{default:()=>H});var s=r(43697),o=(r(66577),r(46791)),i=r(57435),n=r(70586),l=r(16453),a=r(95330),u=r(17445),p=r(17452),y=r(5600),h=r(75215),f=(r(67676),r(71715)),c=r(52011),d=r(30556),m=r(82971),b=r(87085),g=r(71612),v=r(38009),C=r(16859),_=r(34760),w=r(72965),S=r(21506),x=r(32448),F=r(96674),k=r(83379),P=r(19153),G=r(68773),R=r(40330),Z=(r(51773),r(3172)),E=r(22974),I=r(60437),T=r(20322);r(91423),r(74889);const O={esriGeometryPoint:"points",esriGeometryPolyline:"polylines",esriGeometryPolygon:"polygons"};function L(e){const t=e.folders||[],r=t.slice(),s=new Map,o=new Map,i=new Map,n=new Map,l=new Map,a={esriGeometryPoint:o,esriGeometryPolyline:i,esriGeometryPolygon:n};(e.featureCollection&&e.featureCollection.layers||[]).forEach((e=>{const t=(0,E.d9)(e);t.featureSet.features=[];const r=e.featureSet.geometryType;s.set(r,t);const l=e.layerDefinition.objectIdField;"esriGeometryPoint"===r?M(o,l,e.featureSet.features):"esriGeometryPolyline"===r?M(i,l,e.featureSet.features):"esriGeometryPolygon"===r&&M(n,l,e.featureSet.features)})),e.groundOverlays&&e.groundOverlays.forEach((e=>{l.set(e.id,e)})),t.forEach((t=>{t.networkLinkIds.forEach((s=>{const o=function(e,t,r){const s=function(e,t){let r;return t.some((t=>t.id===e&&(r=t,!0))),r}(e,r);return s&&(s.parentFolderId=t,s.networkLink=s),s}(s,t.id,e.networkLinks);o&&r.push(o)}))})),r.forEach((e=>{if(e.featureInfos){e.points=(0,E.d9)(s.get("esriGeometryPoint")),e.polylines=(0,E.d9)(s.get("esriGeometryPolyline")),e.polygons=(0,E.d9)(s.get("esriGeometryPolygon")),e.mapImages=[];for(const t of e.featureInfos)switch(t.type){case"esriGeometryPoint":case"esriGeometryPolyline":case"esriGeometryPolygon":{const r=a[t.type].get(t.id);r&&e[O[t.type]].featureSet.features.push(r);break}case"GroundOverlay":{const r=l.get(t.id);r&&e.mapImages.push(r);break}}e.fullExtent=z([e])}}));const u=z(r);return{folders:t,sublayers:r,extent:u}}function N(e,t,r,s){const o=R.id&&R.id.findCredential(e);e=(0,p.fl)(e,{token:o&&o.token});const i=G.Z.kmlServiceUrl;return(0,Z.default)(i,{query:{url:e,model:"simple",folders:"",refresh:0!==r||void 0,outSR:JSON.stringify(t)},responseType:"json",signal:s})}function j(e,t,r=null,s=[]){const o=[],i={},n=t.sublayers,l=t.folders.map((e=>e.id));return n.forEach((t=>{const n=new e;if(r?n.read(t,r):n.read(t),s.length&&l.includes(n.id)&&(n.visible=s.includes(n.id)),i[t.id]=n,null!=t.parentFolderId&&-1!==t.parentFolderId){const e=i[t.parentFolderId];e.sublayers||(e.sublayers=[]),e.sublayers?.unshift(n)}else o.unshift(n)})),o}function M(e,t,r){r.forEach((r=>{e.set(r.attributes[t],r)}))}function z(e){const t=(0,I.Ue)(I.Gv),r=(0,I.Ue)(I.Gv);for(const s of e){if(s.polygons&&s.polygons.featureSet&&s.polygons.featureSet.features)for(const e of s.polygons.featureSet.features)(0,T.Yg)(t,e.geometry),(0,I.TC)(r,t);if(s.polylines&&s.polylines.featureSet&&s.polylines.featureSet.features)for(const e of s.polylines.featureSet.features)(0,T.Yg)(t,e.geometry),(0,I.TC)(r,t);if(s.points&&s.points.featureSet&&s.points.featureSet.features)for(const e of s.points.featureSet.features)(0,T.Yg)(t,e.geometry),(0,I.TC)(r,t);if(s.mapImages)for(const e of s.mapImages)(0,T.Yg)(t,e.extent),(0,I.TC)(r,t)}return(0,I.fS)(r,I.Gv)?void 0:{xmin:r[0],ymin:r[1],zmin:r[2],xmax:r[3],ymax:r[4],zmax:r[5],spatialReference:m.Z.WGS84}}var q,D=r(6570);let J=q=class extends(x.Z.EventedMixin((0,F.eC)(k.Z))){constructor(...e){super(...e),this.description=null,this.id=null,this.networkLink=null,this.sublayers=null,this.title=null,this.sourceJSON=null,this.fullExtent=null,this.addHandles([(0,u.on)((()=>this.sublayers),"after-add",(({item:e})=>{e.parent=this,e.layer=this.layer}),u.Z_),(0,u.on)((()=>this.sublayers),"after-remove",(({item:e})=>{e.layer=e.parent=null}),u.Z_),(0,u.YP)((()=>this.sublayers),((e,t)=>{if(t)for(const e of t)e.layer=e.parent=null;if(e)for(const t of e)t.parent=this,t.layer=this.layer}),u.Z_)])}initialize(){(0,u.N1)((()=>this.networkLink)).then((()=>(0,u.N1)((()=>!0===this.visible)))).then((()=>this.load()))}load(e){if(!this.networkLink)return;if(this.networkLink.viewFormat)return;const t=(0,n.pC)(e)?e.signal:null,r=this._fetchService(this._get("networkLink")?.href??"",t).then((e=>{const t=z(e.sublayers);this.fullExtent=D.Z.fromJSON(t),this.sourceJSON=e;const r=(0,h.se)(o.Z.ofType(q),j(q,e));this.sublayers?this.sublayers.addMany(r):this.sublayers=r,this.layer?.emit("sublayer-update"),this.layer&&this.layer.notifyChange("visibleSublayers")}));return this.addResolvingPromise(r),Promise.resolve(this)}get visible(){return this._get("visible")}set visible(e){this._get("visible")!==e&&(this._set("visible",e),this.layer&&this.layer.notifyChange("visibleSublayers"))}readVisible(e,t){return!!t.visibility}set layer(e){this._set("layer",e),this.sublayers&&this.sublayers.forEach((t=>t.layer=e))}_fetchService(e,t){return N(e,this.layer.outSpatialReference,this.layer.refreshInterval,t).then((e=>L(e.data)))}};(0,s._)([(0,y.Cb)()],J.prototype,"description",void 0),(0,s._)([(0,y.Cb)()],J.prototype,"id",void 0),(0,s._)([(0,y.Cb)({readOnly:!0,value:null})],J.prototype,"networkLink",void 0),(0,s._)([(0,y.Cb)({json:{write:{allowNull:!0}}})],J.prototype,"parent",void 0),(0,s._)([(0,y.Cb)({type:o.Z.ofType(q),json:{write:{allowNull:!0}}})],J.prototype,"sublayers",void 0),(0,s._)([(0,y.Cb)({value:null,json:{read:{source:"name",reader:e=>(0,P.Cb)(e)}}})],J.prototype,"title",void 0),(0,s._)([(0,y.Cb)({value:!0})],J.prototype,"visible",null),(0,s._)([(0,f.r)("visible",["visibility"])],J.prototype,"readVisible",null),(0,s._)([(0,y.Cb)()],J.prototype,"sourceJSON",void 0),(0,s._)([(0,y.Cb)({value:null})],J.prototype,"layer",null),(0,s._)([(0,y.Cb)({type:D.Z})],J.prototype,"fullExtent",void 0),J=q=(0,s._)([(0,c.j)("esri.layers.support.KMLSublayer")],J);const U=J,A=["kml","xml"];let W=class extends((0,g.h)((0,_.Q)((0,w.M)((0,v.q)((0,C.I)((0,l.R)(b.Z))))))){constructor(...e){super(...e),this._visibleFolders=[],this.allSublayers=new i.Z({getCollections:()=>[this.sublayers],getChildrenFunction:e=>e.sublayers}),this.outSpatialReference=m.Z.WGS84,this.path=null,this.legendEnabled=!1,this.operationalLayerType="KML",this.sublayers=null,this.type="kml",this.url=null}initialize(){this.addHandles([(0,u.YP)((()=>this.sublayers),((e,t)=>{t&&t.forEach((e=>{e.parent=null,e.layer=null})),e&&e.forEach((e=>{e.parent=this,e.layer=this}))}),u.Z_),this.on("sublayer-update",(()=>this.notifyChange("fullExtent")))])}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}readSublayersFromItemOrWebMap(e,t){this._visibleFolders=t.visibleFolders}readSublayers(e,t,r){return j(U,t,r,this._visibleFolders)}writeSublayers(e,t){const r=[],s=e.toArray();for(;s.length;){const e=s[0];e.networkLink||(e.visible&&r.push(e.id),e.sublayers&&s.push(...e.sublayers.toArray())),s.shift()}t.visibleFolders=r}get title(){const e=this._get("title");return e&&"defaults"!==this.originOf("title")?e:this.url?(0,p.vt)(this.url,A)||"KML":e||""}set title(e){this._set("title",e)}get visibleSublayers(){const e=this.sublayers,t=[],r=e=>{e.visible&&(t.push(e),e.sublayers&&e.sublayers.forEach(r))};return e&&e.forEach(r),t}get fullExtent(){return this._recomputeFullExtent()}load(e){const t=(0,n.pC)(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["KML"],supportsData:!1},e).catch(a.r9).then((()=>this._fetchService(t)))),Promise.resolve(this)}destroy(){super.destroy(),this.allSublayers.destroy()}async _fetchService(e){const t=L((await Promise.resolve().then((()=>this.resourceInfo?{ssl:!1,data:this.resourceInfo}:N(this.url??"",this.outSpatialReference,this.refreshInterval,e)))).data);t&&this.read(t,{origin:"service"})}_recomputeFullExtent(){let e=null;(0,n.pC)(this.extent)&&(e=this.extent.clone());const t=r=>{if(r.sublayers)for(const s of r.sublayers.items)t(s),s.visible&&s.fullExtent&&((0,n.pC)(e)?e.union(s.fullExtent):e=s.fullExtent.clone())};return t(this),e}};(0,s._)([(0,y.Cb)({readOnly:!0})],W.prototype,"allSublayers",void 0),(0,s._)([(0,y.Cb)({type:m.Z})],W.prototype,"outSpatialReference",void 0),(0,s._)([(0,y.Cb)({type:String,json:{origins:{"web-scene":{read:!0,write:!0}},read:!1}})],W.prototype,"path",void 0),(0,s._)([(0,y.Cb)({readOnly:!0,json:{read:!1,write:!1}})],W.prototype,"legendEnabled",void 0),(0,s._)([(0,y.Cb)({type:["show","hide","hide-children"]})],W.prototype,"listMode",void 0),(0,s._)([(0,y.Cb)({type:["KML"]})],W.prototype,"operationalLayerType",void 0),(0,s._)([(0,y.Cb)({})],W.prototype,"resourceInfo",void 0),(0,s._)([(0,y.Cb)({type:o.Z.ofType(U),json:{write:{ignoreOrigin:!0}}})],W.prototype,"sublayers",void 0),(0,s._)([(0,f.r)(["web-map","portal-item"],"sublayers",["visibleFolders"])],W.prototype,"readSublayersFromItemOrWebMap",null),(0,s._)([(0,f.r)("service","sublayers",["sublayers"])],W.prototype,"readSublayers",null),(0,s._)([(0,d.c)("sublayers")],W.prototype,"writeSublayers",null),(0,s._)([(0,y.Cb)({readOnly:!0,json:{read:!1}})],W.prototype,"type",void 0),(0,s._)([(0,y.Cb)({json:{origins:{"web-map":{read:{source:"title"}}},write:{ignoreOrigin:!0}}})],W.prototype,"title",null),(0,s._)([(0,y.Cb)(S.HQ)],W.prototype,"url",void 0),(0,s._)([(0,y.Cb)({readOnly:!0})],W.prototype,"visibleSublayers",null),(0,s._)([(0,y.Cb)({type:D.Z})],W.prototype,"extent",void 0),(0,s._)([(0,y.Cb)()],W.prototype,"fullExtent",null),W=(0,s._)([(0,c.j)("esri.layers.KMLLayer")],W);const H=W},34760:(e,t,r)=>{r.d(t,{Q:()=>b});var s=r(43697),o=r(92604),i=r(95330),n=r(5600),l=(r(75215),r(67676),r(52011)),a=r(46791),u=(r(80442),r(20102),r(26258),r(87538));const p=new a.Z,y=new WeakMap;function h(e){return null!=e&&"object"==typeof e&&"refreshInterval"in e&&"refresh"in e}function f(e,t){return Number.isFinite(e)&&Number.isFinite(t)?t<=0?e:f(t,e%t):0}let c=0,d=0;function m(){const e=Date.now();for(const t of p)t.refreshInterval&&e-(y.get(t)??0)+5>=6e4*t.refreshInterval&&(y.set(t,e),t.refresh(e))}(0,u.EH)((()=>{const e=Date.now();let t=0;for(const r of p)t=f(Math.round(6e4*r.refreshInterval),t),r.refreshInterval?y.get(r)||y.set(r,e):y.delete(r);if(t!==d){if(d=t,clearInterval(c),0===d)return void(c=0);c=setInterval(m,d)}}));const b=e=>{let t=class extends e{constructor(...e){super(...e),this.refreshInterval=0,this.refreshTimestamp=0,this._debounceHasDataChanged=(0,i.Ds)((()=>this.hasDataChanged())),this.when().then((()=>{!function(e){h(e)&&p.push(e)}(this)}),(()=>{}))}destroy(){h(this)&&p.includes(this)&&p.remove(this)}get refreshParameters(){return{_ts:this.refreshTimestamp||null}}refresh(e=Date.now()){(0,i.R8)(this._debounceHasDataChanged()).then((t=>{t&&this._set("refreshTimestamp",e),this.emit("refresh",{dataChanged:t})}),(e=>{o.Z.getLogger(this.declaredClass).error(e),this.emit("refresh",{dataChanged:!1,error:e})}))}async hasDataChanged(){return!0}};return(0,s._)([(0,n.Cb)({type:Number,cast:e=>e>=.1?e:e<=0?0:.1,json:{write:!0}})],t.prototype,"refreshInterval",void 0),(0,s._)([(0,n.Cb)({readOnly:!0})],t.prototype,"refreshTimestamp",void 0),(0,s._)([(0,n.Cb)()],t.prototype,"refreshParameters",null),t=(0,s._)([(0,l.j)("esri.layers.mixins.RefreshableLayer")],t),t}},51706:(e,t,r)=>{var s,o;function i(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function n(e){return null!=e&&!isNaN(e)&&isFinite(e)}function l(e){return e.valueExpression?s.Expression:e.field&&"string"==typeof e.field?s.Field:s.Unknown}function a(e,t){const r=t||l(e),i=e.valueUnit||"unknown";return r===s.Unknown?o.Constant:e.stops?o.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?o.ClampedLinear:"unknown"===i?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?o.Proportional:o.Additive:o.Identity:o.RealWorldSize}r.d(t,{PS:()=>l,QW:()=>a,RY:()=>s,hL:()=>o,iY:()=>i,qh:()=>n}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(s||(s={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(o||(o={}))},74889:(e,t,r)=>{r.d(t,{Z:()=>C});var s,o=r(43697),i=r(66577),n=r(38171),l=r(35454),a=r(96674),u=r(22974),p=r(70586),y=r(5600),h=(r(75215),r(71715)),f=r(52011),c=r(30556),d=r(82971),m=r(33955),b=r(1231);const g=new l.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let v=s=class extends a.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const r=d.Z.fromJSON(t.spatialReference),s=[];for(let t=0;t<e.length;t++){const o=e[t],i=n.Z.fromJSON(o),l=o.geometry&&o.geometry.spatialReference;(0,p.pC)(i.geometry)&&!l&&(i.geometry.spatialReference=r);const a=o.aggregateGeometries,u=i.aggregateGeometries;if(a&&(0,p.pC)(u))for(const e in u){const t=u[e],s=a[e]?.spatialReference;(0,p.pC)(t)&&!s&&(t.spatialReference=r)}s.push(i)}return s}writeGeometryType(e,t,r,s){if(e)return void g.write(e,t,r,s);const{features:o}=this;if(o)for(const e of o)if(e&&(0,p.pC)(e.geometry))return void g.write(e.geometry.type,t,r,s)}readQueryGeometry(e,t){if(!e)return null;const r=!!e.spatialReference,s=(0,m.im)(e);return s&&!r&&t.spatialReference&&(s.spatialReference=d.Z.fromJSON(t.spatialReference)),s}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:r}=this;if(r)for(const e of r)if(e&&(0,p.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new s(this.cloneProperties())}cloneProperties(){return(0,u.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let r=0;r<t.features.length;r++){const s=t.features[r];if(s.geometry){const t=e&&e[r];s.geometry=t&&t.toJSON()||s.geometry}}return t}quantize(e){const{scale:[t,r],translate:[s,o]}=e,i=this.features,n=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-s)/t)),(e=>Math.round((o-e)/r)));for(let e=0,t=i.length;e<t;e++)n?.((0,p.Wg)(i[e].geometry))||(i.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:r}=this;if(!r)return this;const{translate:[s,o],scale:[i,n]}=r,l=this._getHydrationFunction(e,(e=>e*i+s),(e=>o-e*n));for(const{geometry:e}of t)(0,p.pC)(e)&&l&&l(e);return this.transform=null,this}_quantizePoints(e,t,r){let s,o;const i=[];for(let n=0,l=e.length;n<l;n++){const l=e[n];if(n>0){const e=t(l[0]),n=r(l[1]);e===s&&n===o||(i.push([e-s,n-o]),s=e,o=n)}else s=t(l[0]),o=r(l[1]),i.push([s,o])}return i.length>0?i:null}_getQuantizationFunction(e,t,r){return"point"===e?e=>(e.x=t(e.x),e.y=r(e.y),e):"polyline"===e||"polygon"===e?e=>{const s=(0,m.oU)(e)?e.rings:e.paths,o=[];for(let e=0,i=s.length;e<i;e++){const i=s[e],n=this._quantizePoints(i,t,r);n&&o.push(n)}return o.length>0?((0,m.oU)(e)?e.rings=o:e.paths=o,e):null}:"multipoint"===e?e=>{const s=this._quantizePoints(e.points,t,r);return s&&s.length>0?(e.points=s,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,r){return"point"===e?e=>{e.x=t(e.x),e.y=r(e.y)}:"polyline"===e||"polygon"===e?e=>{const s=(0,m.oU)(e)?e.rings:e.paths;let o,i;for(let e=0,n=s.length;e<n;e++){const n=s[e];for(let e=0,s=n.length;e<s;e++){const s=n[e];e>0?(o+=s[0],i+=s[1]):(o=s[0],i=s[1]),s[0]=t(o),s[1]=r(i)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=r(e.ymin),e.xmax=t(e.xmax),e.ymax=r(e.ymax)}:"multipoint"===e?e=>{const s=e.points;let o,i;for(let e=0,n=s.length;e<n;e++){const n=s[e];e>0?(o+=n[0],i+=n[1]):(o=n[0],i=n[1]),n[0]=t(o),n[1]=r(i)}}:null}};(0,o._)([(0,y.Cb)({type:String,json:{write:!0}})],v.prototype,"displayFieldName",void 0),(0,o._)([(0,y.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],v.prototype,"exceededTransferLimit",void 0),(0,o._)([(0,y.Cb)({type:[n.Z],json:{write:!0}})],v.prototype,"features",void 0),(0,o._)([(0,h.r)("features")],v.prototype,"readFeatures",null),(0,o._)([(0,y.Cb)({type:[b.Z],json:{write:!0}})],v.prototype,"fields",void 0),(0,o._)([(0,y.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:g.read}}})],v.prototype,"geometryType",void 0),(0,o._)([(0,c.c)("geometryType")],v.prototype,"writeGeometryType",null),(0,o._)([(0,y.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],v.prototype,"hasM",void 0),(0,o._)([(0,y.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],v.prototype,"hasZ",void 0),(0,o._)([(0,y.Cb)({types:i.qM,json:{write:!0}})],v.prototype,"queryGeometry",void 0),(0,o._)([(0,h.r)("queryGeometry")],v.prototype,"readQueryGeometry",null),(0,o._)([(0,y.Cb)({type:d.Z,json:{write:!0}})],v.prototype,"spatialReference",void 0),(0,o._)([(0,c.c)("spatialReference")],v.prototype,"writeSpatialReference",null),(0,o._)([(0,y.Cb)({json:{write:!0}})],v.prototype,"transform",void 0),v=s=(0,o._)([(0,f.j)("esri.rest.support.FeatureSet")],v),v.prototype.toJSON.isDefaultToJSON=!0;const C=v}}]);