<!-- gis巡检养护 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards
      v-model="cardsvalue"
      :span="12"
      style="margin-bottom: 10px"
    ></Cards>
    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components'
import { IFormIns } from '@/components/type'
import { GetPatrolCompleteCount, GetPatrolTaskList } from '@/api/patrol'
import { PatrolTaskStatusConfig } from '../../Inspection/config'
import { useDistrict } from '../../Inspection/hooks/useDistrict'

const emit = defineEmits(['highlightMark', 'addMarks'])
const props = defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const refForm = ref<IFormIns>()

const cardsvalue = ref([
  { label: '0', value: '任务总数' },
  { label: '0 %', value: '完成率' }
])
const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    pagerCount: 5,
    layout: 'total,sizes,pager',
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  columns: [
    {
      label: '任务编号',
      prop: 'code',
      width: 90
    },
    {
      label: '巡检人',
      prop: 'receiveUserName'
    },
    {
      label: '当前状态',
      prop: 'status',
      width: 90,
      formatter: row => {
        return row.status && PatrolTaskStatusConfig[row.status]?.text
      }
    }
  ],
  handleRowClick: row => {
    emit('highlightMark', props.menu, row)
    TableConfig.currentRow = row
    viewTaskLocus()
  }
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          field: 'layer',
          appendBtns: [
            {
              perm: true,
              text: '刷新',
              click: () => refreshData()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const district = useDistrict('viewDiv')
const viewTaskLocus = async () => {
  if (!TableConfig.currentRow) return
  district.add(props.view, TableConfig.currentRow.districtAreaId, {
    goto: true,
    ratio: 1,
    showKeyPoint: true
  })
}
const refreshData = async () => {
  GetPatrolTaskList({
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20
  }).then(res => {
    TableConfig.dataList = res.data?.data?.data || []
    TableConfig.pagination.total = res.data?.data?.total || 0
    cardsvalue.value[0].label = (res?.data?.data?.total || 0) + ''
  })
}
const refreshCount = () => {
  GetPatrolCompleteCount().then(res => {
    cardsvalue.value[1].label = (res.data?.data?.percent || '0.0') + ' %'
  })
}
onMounted(() => {
  refreshData()
  refreshCount()
})
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}

.table-box {
  height: calc(100% - 120px);
}
</style>
