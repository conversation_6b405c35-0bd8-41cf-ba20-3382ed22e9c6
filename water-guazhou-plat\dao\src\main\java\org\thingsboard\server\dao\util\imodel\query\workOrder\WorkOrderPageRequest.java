package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStage;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.AwareCurrentUserUUID;
import org.thingsboard.server.dao.util.imodel.query.AwareTenantUUID;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;

import java.util.EnumSet;
import java.util.Set;

@Getter
@Setter
public class WorkOrderPageRequest extends AdvancedPageableQueryEntity<WorkOrder, WorkOrderPageRequest> implements AwareTenantUUID, AwareCurrentUserUUID {
    // 关键字 编号/标题/摘要/地址/电话
    private String keyword;

    // "当前状态"
    private String status;

    // "来源"
    private String source;

    // "紧急程度"
    private String level;

    // "工单类型"
    private String type;

    // region 额外
    // 上报人
    private String uploadUserId;

    // 上报人电话
    private String uploadPhone;

    // 上报人户号
    private String uploadNo;

    // 上报人地址
    private String uploadAddress;

    // 发起人
    private String organizerId;

    // 处理人
    private String processUserId;

    // 当前步骤处理人
    private String stepProcessUserId;
    // endregion

    // region 模糊内容查询
    // "工单单号唯一。两部分构成：1,日期; 2,递增号码。如：************"
    private String serialNo;

    // "标题"
    private String title;

    // "描述/备注"
    private String remark;

    // "地址"
    private String address;
    // endregion

    private String ownerId;

    // 在哪些状态闭区间之间，传入数组 最多两个 如RESOLVING,PROCESSING
    private String stageBetween;

    // 工单阶段
    private String stage;

    // 抄送人，多个用逗号隔开
    private String ccUserId;

    // 设备类别Id
    private String deviceTypeId;

    // 设备名称
    private String deviceName;

    // 设备序列号
    private String deviceSerial;

    // 是否强制将stepProcessUserId设置为当前用户
    private boolean self;

    private String tenantId;

    private String currentUserId;

    private String[] stageBetween() {
        if (stageBetween == null) {
            return null;
        }
        if (stageBetween.contains(",")) {
            return stageBetween.split(",");
        }

        return new String[]{stageBetween, stageBetween};
    }

    public Set<WorkOrderStatus> stageBetweenSet() {
        if (stage != null) {
            WorkOrderStage workOrderStage = WorkOrderStage.valueOf(stage);
            return workOrderStage.getStatusBetween();
        }

        String[] between = stageBetween();
        if (between != null)
            return EnumSet.range(
                    WorkOrderStatus.valueOf(between[0]),
                    WorkOrderStatus.valueOf(between[1])
            );
        return null;
    }

    @Override
    public String valid(IStarHttpRequest request) {
        if (stageBetween != null) {
            String[] between = stageBetween();
            if (between == null) {
                return null;
            }
            if (between.length > 2) {
                return "stageBetween超过两个";
            } else if (between.length == 1) {
                try {
                    WorkOrderStatus.valueOf(between[0]);
                } catch (IllegalArgumentException e) {
                    return "错误的状态" + between[0];
                }
            } else if (between.length == 2) {
                try {
                    int start = WorkOrderStatus.valueOf(between[0]).ordinal();
                    int end = WorkOrderStatus.valueOf(between[1]).ordinal();
                    if (start > end) {
                        return "错误的状态顺序" + between[0] + " to " + between[1];
                    }
                } catch (IllegalArgumentException e) {
                    return "错误的状态" + between[0];
                }
            }
        }

        if (self) {
            stepProcessUserId = request.getUserId();
        }

        return null;
    }

    @Override
    public void tenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String tenantId() {
        return tenantId;
    }

    @Override
    public void currentUserId(String currentUserId) {
        this.currentUserId = currentUserId;
    }

    public String currentUserUUID() {
        return currentUserId;
    }

}
