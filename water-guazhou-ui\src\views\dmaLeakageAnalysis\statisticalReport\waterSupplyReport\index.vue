<!-- 水厂供水报表 -->
<template>
  <div class="wrapper">
    <Search
      ref="refSearch"
      class="search"
      :config="SearchConfig"
    ></Search>
    <CardTable
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialog"
      :config="DialogConfig"
    >
      <AOUMonitoring></AOUMonitoring>
    </DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { GetDmaPlantSupplyReport, GetDmaPlantSupplyReportHeader } from '@/api/mapservice/dma'
import { ICardTableIns, IDialogFormIns, ISearchIns } from '@/components/type'
import { usePartition } from '@/hooks/arcgis'
import { formatterDate, formatterMonth, formatterYear } from '@/utils/GlobalHelper'
import AOUMonitoring from './components/AOUMonitoring.vue'

const refTable = ref<ICardTableIns>()
const refSearch = ref<ISearchIns>()
const handleHidden = (params, query, config) => (config.hidden = params.type !== config.field)
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      label: '选择方式',
      field: 'type',
      options: [
        { label: '按年', value: 'year' },
        { label: '按月', value: 'month' },
        { label: '按时间段', value: 'day' }
      ]
    },
    {
      handleHidden,
      type: 'year',
      label: '',
      field: 'year',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden,
      type: 'month',
      label: '',
      field: 'month',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden,
      type: 'daterange',
      label: '',
      field: 'day',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '添加监测点',
          type: 'success',
          iconifyIcon: 'ep:circle-plus',
          click: () => {
            refDialog.value?.openDialog()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'success',
          iconifyIcon: 'ep:download',
          click: () => refTable.value?.exportTable()
        }
      ]
    }
  ],
  defaultParams: {
    type: 'year',
    year: moment().format(formatterYear),
    month: moment().format(formatterMonth),
    day: [moment().format(formatterDate), moment().format(formatterDate)]
  }
})
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { label: '日期', prop: 'date' },
    {
      label: '总供水',
      prop: 'total'
    }
  ],
  pagination: {
    hide: true
  }
})
const refDialog = ref<IDialogFormIns>()
const DialogConfig = reactive<IDialogFormConfig>({
  title: '添加监测点',
  dialogWidth: '80%',
  group: [],
  cancel: false
})

const refreshData = async () => {
  try {
    TableConfig.loading = true
    const query = refSearch.value?.queryParams || {}
    const p1 = GetDmaPlantSupplyReportHeader()
    const p2 = GetDmaPlantSupplyReport({
      type: query.type,
      date: query.type === 'month' ? query.month : query.type === 'year' ? query.year : undefined,
      start: query.type === 'day' ? query.day?.[0] : undefined,
      end: query.type === 'day' ? query.day?.[1] : undefined
    })
    const res = await Promise.all([p1, p2])
    const subColumns: IFormTableColumn[] = res[0].data.data?.map(item => {
      const column: IFormTableColumn = {
        label: item.title,
        prop: item.title,
        subColumns: [
          ...(item.titleList?.map(cItem => {
            const cColumn: IFormTableColumn = {
              label: cItem.label,
              prop: cItem.value,
              minWidth: 160
            }
            return cColumn
          }) || []),
          { minWidth: 140, label: '水厂总进水', prop: `in${item.title}` },
          { minWidth: 140, label: '水厂总出水', prop: `out${item.title}` },
          { minWidth: 140, label: '差值', prop: `difference${item.title}` }
        ]
      }
      return column
    }) || []
    TableConfig.columns = [
      { minWidth: 160, label: '日期', prop: 'date' },
      ...subColumns,
      {
        minWidth: 160,
        label: '总供水',
        prop: 'sum'
      }
    ]
    const data: any[] = res[1]?.data?.data || []
    const length = data.length
    const total: any = { date: '合计' }
    const ave: any = { date: '平均' }
    subColumns[0]?.subColumns?.map(item => {
      total[item.prop] = length
        ? data.reduce((prev, cur) => {
          return prev + cur[item.prop]
        }, 0)
        : 0
      total[item.prop] = total[item.prop].toFixed(2)
      ave[item.prop] = (length ? total[item.prop] / length : 0).toFixed(2)
    })
    data.push(total)
    data.push(ave)
    TableConfig.dataList = data
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
const partition = usePartition()
onMounted(async () => {
  await partition.getTree()
  refreshData()
})
</script>

<style scoped>
.search {
  margin-bottom: 10px;
}
.card-table {
  height: calc(100% - 50px);
}
</style>
