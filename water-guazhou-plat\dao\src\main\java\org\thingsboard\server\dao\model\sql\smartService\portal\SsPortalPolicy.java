package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_policy")
public class SsPortalPolicy {
    // id
    private String id;

    // 名称
    private String name;

    // 图标
    private String icon;

    // 效果图标
    private String effectIcon;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
