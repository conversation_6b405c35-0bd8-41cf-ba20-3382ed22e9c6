<!--
  节点压力
 -->
<template>
  <RightDrawerMap :title="'节点压力'">
    <SliderBar></SliderBar>
    <el-divider></el-divider>
    <HydraulicPanel
      :header="['压力等级(MPa)', '图层控制', '定位']"
      :legends="[
        { label: '>6Mpa', value: 2, checked: true },
        { label: '5~6Mpa', value: 3342, checked: true },
        { label: '4~5Mpa', value: 154, checked: true },
        { label: '3~4Mpa', value: 211, checked: true },
        { label: '2~3Mpa', value: 184, checked: true },
        { label: '1~2Mpa', value: 184, checked: true },
        { label: '0~1Mpa', value: 184, checked: true }
      ]"
      :unit="'个'"
    ></HydraulicPanel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import HydraulicPanel from '../components/HydraulicPanel.vue'
import SliderBar from '../components/SliderBar.vue'
</script>
<style lang="scss" scoped></style>
