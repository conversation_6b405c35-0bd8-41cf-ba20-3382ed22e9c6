package org.thingsboard.server.dao.pumpHouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpMaintenanceStandard;
import org.thingsboard.server.dao.sql.smartProduction.pumpHouse.PumpMaintenanceStandardMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpMaintenanceStandardPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpMaintenanceStandardSaveRequest;

@Service
public class PumpMaintenanceStandardServiceImpl implements PumpMaintenanceStandardService {
    @Autowired
    private PumpMaintenanceStandardMapper mapper;

    @Override
    public IPage<PumpMaintenanceStandard> findAllConditional(PumpMaintenanceStandardPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public PumpMaintenanceStandard save(PumpMaintenanceStandardSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(PumpMaintenanceStandard entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
