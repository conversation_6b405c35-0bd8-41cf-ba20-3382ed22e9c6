package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseProductAuthorizationService;
import org.thingsboard.server.dao.model.sql.base.BaseProductAuthorization;
import org.thingsboard.server.dao.util.imodel.query.base.BaseProductAuthorizationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 平台管理-产品授权Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Api(tags = "平台管理-产品授权")
@RestController
@RequestMapping("api/base/product/authorization")
public class BaseProductAuthorizationController extends BaseController {

    @Autowired
    private IBaseProductAuthorizationService baseProductAuthorizationService;

    /**
     * 查询平台管理-产品授权列表
     */
    @MonitorPerformance(description = "平台管理-查询产品授权列表")
    @ApiOperation(value = "查询产品授权列表")
    @GetMapping("/list")
    public IstarResponse list(BaseProductAuthorizationPageRequest baseProductAuthorization) {
        return IstarResponse.ok(baseProductAuthorizationService.selectBaseProductAuthorizationList(baseProductAuthorization));
    }

    /**
     * 获取平台管理-产品授权详细信息
     */
    @MonitorPerformance(description = "平台管理-获取产品授权详情")
    @ApiOperation(value = "获取产品授权详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseProductAuthorizationService.selectBaseProductAuthorizationById(id));
    }

    /**
     * 新增平台管理-产品授权
     */
    @MonitorPerformance(description = "平台管理-新增产品授权")
    @ApiOperation(value = "新增产品授权")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseProductAuthorization baseProductAuthorization) {
        return IstarResponse.ok(baseProductAuthorizationService.insertBaseProductAuthorization(baseProductAuthorization));
    }

    /**
     * 修改平台管理-产品授权
     */
    @MonitorPerformance(description = "平台管理-修改产品授权")
    @ApiOperation(value = "修改产品授权")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseProductAuthorization baseProductAuthorization) {
        return IstarResponse.ok(baseProductAuthorizationService.updateBaseProductAuthorization(baseProductAuthorization));
    }

    /**
     * 删除平台管理-产品授权
     */
    @MonitorPerformance(description = "平台管理-删除产品授权")
    @ApiOperation(value = "删除产品授权")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseProductAuthorizationService.deleteBaseProductAuthorizationByIds(ids));
    }
}
