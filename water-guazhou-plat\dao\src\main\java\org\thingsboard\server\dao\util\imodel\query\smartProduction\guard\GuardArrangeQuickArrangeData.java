package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class GuardArrangeQuickArrangeData {
    // 地点id
    private String placeId;

    // 班组id
    private String groupId;

    // 班次id
    private String classId;

    // 值班日期
    private Date dayTime;

    public String getGeneratedId() {
        return IdWorker.get32UUID();
    }


}
