import { request } from '@/plugins/axios';

// 分页条件查询仓库信息
export const getstoreSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
  code?: string;
  name?: string;
  managerId?: string;
}) =>
  request({
    url: `/api/store`,
    method: 'get',
    params
  });

// 添加仓库
export const postStore = (params?: {
  code: string;
  name: string;
  address: string;
  orderNum?: string;
  managerId: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}) =>
  request({
    url: `/api/store`,
    method: 'post',
    data: params
  });

// 修改仓库
export const patchStore = (
  id: string,
  params?: {
    code: string;
    name: string;
    address: string;
    orderNum?: string;
    managerId: string;
    remark?: string;
    createTime?: string;
    updateTime?: string;
  }
) =>
  request({
    url: `/api/store/${id}`,
    method: 'patch',
    data: params
  });

// 删除仓库
export const deleteStore = (id: string) =>
  request({
    url: `/api/store/${id}`,
    method: 'delete'
  });

// 分页条件查询货架信息
export const getGoodsShelfSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
  code?: string;
  name?: string;
}) =>
  request({
    url: `/api/goodsShelf`,
    method: 'get',
    params
  });

// 添加货架
export const postGoodsShelf = (params?: {
  code: string;
  name: string;
  address: string;
  orderNum?: string;
  managerId: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}) =>
  request({
    url: `/api/goodsShelf`,
    method: 'post',
    data: params
  });

// 修改货架
export const patchGoodsShelf = (
  id: string,
  params?: {
    code: string;
    name: string;
    address: string;
    orderNum?: string;
    managerId: string;
    remark?: string;
    createTime?: string;
    updateTime?: string;
  }
) =>
  request({
    url: `/api/goodsShelf/${id}`,
    method: 'patch',
    data: params
  });

// 删除货架
export const deleteGoodsShelf = (id: string) =>
  request({
    url: `/api/goodsShelf/${id}`,
    method: 'delete'
  });

// 施工项目
// 分页条件查询货架信息
export const getConstructionProjectSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
  code?: string;
  name?: string;
  address?: string;
  orgId?: string;
}) =>
  request({
    url: `/api/constructionProject`,
    method: 'get',
    params
  });

// 入库单
// 分页条件查询入库单信息
export const getStoreInRecordSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
  code?: string;
  title?: string;
  storehouseId?: string;
}) =>
  request({
    url: `/api/StoreInRecord`,
    method: 'get',
    params
  });

// 添加入库单
export const postStoreInRecord = (params?: {
  code: string;
  name: string;
  address: string;
  orgId?: string;
  constructionSide: string;
  insertTime?: string;
  startTime?: string;
  endTime?: string;
}) =>
  request({
    url: `/api/StoreInRecord`,
    method: 'post',
    data: params
  });

// 分页条件查询入库单条目
export const getStoreInRecordDetailSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
  mainId?: string;
  serialId?: string;
  shelvesId?: string;
}) =>
  request({
    url: `/api/StoreInRecordDetail`,
    method: 'get',
    params
  });

// 出库单
// 分页条件查询出库单信息
export const getStoreOutRecordSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
  code?: string;
  title?: string;
  storehouseId?: string;
}) =>
  request({
    url: `/api/StoreOutRecord`,
    method: 'get',
    params
  });

// 添加出库单
export const postStoreOutRecord = (params?: {
  code: string;
  name: string;
  address: string;
  orgId?: string;
  constructionSide: string;
  insertTime?: string;
  startTime?: string;
  endTime?: string;
}) =>
  request({
    url: `/api/StoreOutRecord`,
    method: 'post',
    data: params
  });

// 分页条件查询出库单设备信息
export const getStoreOutRecordDetailSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
  mainId: string;
}) =>
  request({
    url: `/api/StoreOutRecordDetail`,
    method: 'get',
    params
  });

// 设备出库
export const postCheckout = (params?: {
  storeOutId?: string;
  storeOutItemId?: string;
  checkouts: string[];
}) =>
  request({
    url: `/api/deviceStorageJournal/checkout`,
    method: 'post',
    data: [params]
  });
