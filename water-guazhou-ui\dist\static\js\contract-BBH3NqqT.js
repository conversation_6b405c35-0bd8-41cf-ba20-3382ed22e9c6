import{_ as k}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as O}from"./CardTable-rdWOL4_6.js";import{_ as v}from"./CardSearch-CB_HNR-Q.js";import{d as w,c as d,a8 as g,s as b,r as u,x as l,a9 as y,o as E,g as z,n as L,q as m,i as f,b7 as N}from"./index-r0dFAfgr.js";import{I as x}from"./common-CvK_P_ao.js";import{y as D,z as R,A as q,w as B,g as S,b as j,B as A}from"./manage-BReaEVJk.js";import P from"./detail-BLJaI4-F.js";import{S as C}from"./data-Dv9-Tstw.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./device-DWHb0XjG.js";import"./projectManagement-CDcrrCQ1.js";import"./detail-DEo1RlcF.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";import"./DateFormatter-Bm9a68Ax.js";const U={class:"wrapper"},vt=w({__name:"contract",setup(V){const p=d(),c=d(),_=d({filters:[{label:"工程编号",field:"constructionCode",type:"input"},{label:"工程名称",field:"constructionName",type:"input"},{label:"业主单位",field:"firstpartOrganization",type:"input"},{label:"监理单位",field:"superVisitorOrganization",type:"input"},{label:"工程类别",field:"constructionTypeId",type:"select",options:g(()=>s.projectType)}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:x.DOWNLOAD,click:()=>{D().then(t=>{const e=window.URL.createObjectURL(t.data),o=document.createElement("a");o.style.display="none",o.href=e,o.setAttribute("download","合同管理.xlsx"),document.body.appendChild(o),o.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:b(N),click:()=>{var t;(t=p.value)==null||t.resetForm(),r()}},{perm:!0,text:"查询",icon:x.QUERY,click:()=>r()}]}]}),i=u({defaultExpandAll:!1,indexVisible:!0,expandable:!0,expandComponent:b(P),extendedReturn:()=>{r()},rowKey:"constructionCode",columns:[{label:"工程编号",prop:"constructionCode"},{label:"工程名称",prop:"constructionName"},{label:"工程类别",prop:"constructionTypeName"},{label:"业主单位",prop:"firstpartOrganization"},{label:"施工单位",prop:"constructionOrganization"},{label:"设计单位",prop:"secondpartOrganization"},{label:"监理单位",prop:"supervisorOrganization"},{label:"合同总金额(万元)",prop:"contractTotalCost"},{label:"工作状态",prop:"status",tag:!0,tagColor:t=>{var e;return((e=C.find(o=>o.value===t.status))==null?void 0:e.color)||""},formatter:t=>{var e;return(e=C.find(o=>o.value===t.status))==null?void 0:e.label}}],operationWidth:"360px",operations:[{disabled:t=>t.status==="COMPLETED",isTextBtn:!1,type:"primary",text:"添加工程合同",perm:!0,click:t=>{h(t)}},{disabled:t=>t.status==="COMPLETED",isTextBtn:!1,type:"success",text:"完成",perm:!0,click:t=>{R(t.constructionCode).then(e=>{e.data.code===200?l.success("已完成"):l.warning("完成失败"),r()})}},{isTextBtn:!1,text:"导出合同情况",perm:!0,click:t=>{q(t.constructionCode).then(e=>{const o=window.URL.createObjectURL(e.data),a=document.createElement("a");a.style.display="none",a.href=o,a.setAttribute("download",`${t.constructionName}合同管理.xlsx`),document.body.appendChild(a),a.click()})}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{i.pagination.page=t,i.pagination.limit=e,r()}}}),n=u({title:"添加工程合同",appendToBody:!0,labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:t=>{n.submitting=!0;let e="新增";t.id&&(e="修改"),t.time&&(t.workTimeBegin=t.time[0],t.workTimeEnd=t.time[1],delete t.time),B(t).then(o=>{var a;n.submitting=!1,o.data.code===200?(l.success(e+"成功"),(a=c.value)==null||a.closeDialog()):l.warning(e+"失败")}).catch(o=>{n.submitting=!1,l.warning(o)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"合同编号",field:"code",rules:[{required:!0,message:"请输入合同编号"}]},{xs:12,type:"select",label:"合同分类",field:"type",options:g(()=>s.ConstructionContractType)},{xs:12,type:"input",label:"合同名称",field:"name",rules:[{required:!0,message:"请输入合同名称"}]},{xs:12,type:"input",label:"工程名称",field:"constructionName",disabled:!0},{xs:12,type:"input",label:"工程编号",field:"constructionCode",disabled:!0},{xs:12,type:"input",label:"甲方单位",field:"firstpartOrganization",rules:[{required:!0,message:"请输入甲方单位"}]},{xs:12,type:"input",label:"乙方单位",field:"secondpartOrganization",rules:[{required:!0,message:"请输入乙方单位"}]},{xs:12,type:"input",label:"甲方代表",field:"firstpartRepresentative",rules:[{required:!0,message:"请输入甲方代表"}]},{xs:12,type:"input",label:"乙方联系人",field:"secondpartRepresentative"},{xs:12,type:"input",label:"甲方代表联系电话",field:"firstpartPhone"},{xs:12,type:"input",label:"乙方联系电话",field:"secondpartPhone"},{xs:12,type:"number",label:"合同金额",field:"cost",rules:[{required:!0,message:"请输入合同金额"}]},{xs:12,type:"daterange",label:"合同工期",field:"time"},{xs:12,type:"date",label:"签署时间",field:"signTime"},{type:"textarea",label:"详细说明",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),h=t=>{var e;n.title="添加工程合同",n.defaultValue={constructionCode:t.constructionCode,constructionName:t.constructionName},(e=c.value)==null||e.openDialog()},s=u({projectType:[],ConstructionContractType:[],getOptions:()=>{S({page:1,size:-1}).then(t=>{s.projectType=y(t.data.data.data||[],"children")}),j({page:1,size:-1}).then(t=>{s.ConstructionContractType=y(t.data.data.data||[],"children")})}}),r=async()=>{var e;const t={size:i.pagination.limit||20,page:i.pagination.page||1,...((e=p.value)==null?void 0:e.queryParams)||{}};A(t).then(o=>{i.dataList=o.data.data.data||[],i.pagination.total=o.data.data.total||0})};return E(()=>{r(),s.getOptions()}),(t,e)=>{const o=v,a=O,T=k;return z(),L("div",U,[m(o,{ref_key:"refSearch",ref:p,config:f(_)},null,8,["config"]),m(a,{config:f(i),class:"card-table"},null,8,["config"]),m(T,{ref_key:"refForm",ref:c,config:f(n)},null,8,["config"])])}}});export{vt as default};
