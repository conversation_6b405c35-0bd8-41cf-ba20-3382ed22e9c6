package org.thingsboard.server.dao.sql.menu2;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.MenuResource;

public interface MenuResourceRepository extends JpaRepository<MenuResource, String> {

    @Query("SELECT a FROM MenuResource a " +
            "WHERE a.name LIKE %?1% AND a.url LIKE %?2%")
    Page<MenuResource> findList(String name, String url, Pageable pageable);
}
