import{d as ze,u as Ge,j as Ae,M as Ie,c as p,bF as _,r as b,x as W,a8 as Ve,bu as Be,g as u,h as I,F as n,p as e,q as o,bh as r,i as a,G as g,dS as Le,aw as le,av as x,ak as Oe,dT as Ue,n as f,aJ as w,an as j,aB as C,dU as We,dV as je,dk as R,Z as ie,a0 as Re,l as ne,bO as $,D as $e,dW as He,cR as qe,dX as Je,cE as Ze,c6 as Xe,aK as Ke,aL as Pe,J as Qe,b6 as et,dn as tt,_ as st,bV as at,C as ot}from"./index-r0dFAfgr.js";import{_ as lt}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";/* empty css                    */import{_ as it}from"./index-C9hz-UZb.js";/* empty css                  */import{s as nt,p as rt,_ as dt,a as ct,b as ut,c as ft,d as pt,g as mt,e as _t,k as vt,f as gt}from"./dutyManage-C2OfTDsM.js";import{i as ht}from"./index-Bj5d3Vsu.js";import{f as yt}from"./DateFormatter-Bm9a68Ax.js";import kt from"./rolemenu-CZjPtKtB.js";const bt={class:"main"},xt={class:"wrapper"},wt={class:"left"},Ct={class:"date-bg"},Dt={style:{display:"flex"}},Yt={class:"userInfo"},Mt={class:"userName"},Ft={class:"weather"},Nt={style:{display:"flex","justify-content":"space-between",flex:"1","align-items":"center"}},Tt={style:{"font-weight":"600","font-size":"16px"}},Et={class:"date-weather"},St={key:0,class:"today"};const zt=["onClick"];const Gt={class:"list-con"},At={key:0},It={class:"schedule-title"},Vt={class:"name"},Bt={class:"desc"},Lt={class:"schedule-num"},Ot={key:1,style:{"text-align":"center","margin-top":"7%"}},Ut={style:{width:"100%",color:"#000","padding-left":"10px"}},Wt={class:"duty-info",style:{}},jt={class:"d-bg",style:{padding:"20px",display:"flex","flex-direction":"column","justify-content":"space-between"}},Rt={class:"info",style:{display:"flex","justify-content":"space-between"}},$t={class:"inf0-1",style:{width:"45%","line-height":"24px"}},Ht={style:{color:"#ffffff","font-size":"14px"}},qt={style:{display:"flex","justify-content":"space-between",width:"55%"}},Jt={class:"inf0-2",style:{"line-height":"24px"}},Zt={style:{color:"#ffffff","font-size":"14px"}},Xt={class:"inf01"},Kt={class:"info2"},Pt={class:"right"},Qt={class:"right-bottom-title",style:{width:"100%",display:"flex","justify-content":"space-between"}},es={class:"app-content",style:{"overflow-y":"auto"}},ts=["onClick"],ss={class:"images"},as={class:"m-title",style:{"font-size":"14px","margin-top":"5px"}},os={class:"right-bottom"},ls={class:"right-bottom-title",style:{width:"100%",display:"flex","justify-content":"space-between"}},is={class:"content"},ns={key:0,style:{padding:"0",height:"140px"}},rs={class:"color-grey"},ds={key:1,style:{"text-align":"center","margin-top":"10%"}},cs={class:"right-bottom-title",style:{width:"100%",display:"flex","justify-content":"space-between"}},us={class:"content"},fs={style:{padding:"0",overflow:"auto",height:"140px"}},ps={class:"color-grey"},ms={class:"right-content"},_s={class:"right-bottom-title",style:{"padding-left":"10px"}},vs={class:"content"},gs={class:"search",style:{}},hs={style:{"padding-top":"30px",display:"flex",width:"100%"}},ys=ze({__name:"index-bak copy",setup(ks){const F=Ge(),h=Ae(),{$messageWarning:H,$messageSuccess:re,$messageError:q}=Ie(),de=["周日","周一","周二","周三","周四","周五","周六"],N=p(new Date),J=p({}),V=p(),Z=p(),X=p(),T=p([]),B=p([]),L=p([]),ce=p(),E=p([]),K=p([]),D=p({}),Y=p(""),ue=p(_().format("YYYY-MM-DD")),P=p(_().format("YYYY-MM-DD")),Q=p(),O=()=>{T.value=[];for(let s=1;s<=7;s++)T.value.push({week:_().startOf("week").add(s-l.weekNum,"day").day(),day:_().startOf("week").add(s-l.weekNum,"day").format("DD"),time:_().startOf("week").add(s-l.weekNum,"day").format("YYYY-MM-DD")});l.singleContract=_(T.value[6].time).format("YYYY-MM")},l=b({apps:[],menus:[],drawerTitle:"添加",todayTime:"添加",hour:"",alarmData:[],loading:!1,weekNum:0,shortcutMenu:[],singleContract:_(new Date).format("YYYY年MM月"),CurrentGuard:{},partnerNames:[]}),M=b({visible:!1,roleId:"",close:()=>M.visible=!1}),ee=b({title:"事件记录",dialogWidth:"900px",labelWidth:"100px",group:[{fields:[{label:"事件内容",field:"content",type:"textarea",rules:[{required:!0,message:"请输入事件内容",trigger:"blur"}]}]}],submit:s=>{nt({...s,recordId:l.CurrentGuard.id}).then(t=>{t.data.code===200?re("添加成功"):q("添加失败")}).catch(t=>{q(t)})}}),fe=b({title:"新增日程安排",dialogWidth:"900px",labelWidth:"100px",group:[],submit:()=>{var s;(s=Q.value)==null||s.Submit()}}),te=b({title:"通讯录详情",group:[{fields:[{xs:12,field:"departmentName",type:"text",readonly:!0,labelWidth:"120px",label:"所属部门名称："},{xs:12,field:"firstName",type:"text",readonly:!0,label:"姓名："},{xs:12,field:"phone",type:"text",readonly:!0,label:"联系方式："},{xs:12,field:"email",type:"text",readonly:!0,label:"邮箱地址："}]}]}),pe=b({labelPosition:"left",group:[{fields:[{field:"title",type:"input",label:"标题",rules:[{required:!0,message:"请输入标题"}]},{field:"content",type:"textarea",label:"行程内容",rules:[{required:!0,message:"请输入行程内容"}]}]}],submit(s){s.time=N.value,rt(s).then(t=>{var d;t.data.code===200?(W.success("保存成功"),(d=V.value)==null||d.closeDialog()):W.warning("保存失败")}).catch(t=>{W.warning(t)})}}),me=()=>{var s;(s=V.value)==null||s.openDialog()},_e=()=>{var s;ee.defaultValue={},(s=Z.value)==null||s.openDialog()},se=s=>{P.value=s,mt({beginTime:s,endTime:s}).then(t=>{B.value=t.data.data||[]})},ve=s=>s?_(s).format("YYYY-MM-DD HH:mm:ss"):"",ge=()=>{l.weekNum+=7,O()},he=()=>{l.weekNum-=7,O()},ye=()=>{ie.get("https://restapi.amap.com/v3/ip",{params:{key:"2ef4bde4346f3aef3799e28c465711e4"}}).then(s=>{ie.get("https://restapi.amap.com/v3/weather/weatherInfo?parameters",{params:{key:"2ef4bde4346f3aef3799e28c465711e4",city:s.data.adcode}}).then(t=>{D.value=t.data.lives[0]??{}})})},ke=async s=>{var t,d;if(s){l.loading=!0;const c=await _t({name:s});E.value=(t=c.data)==null?void 0:t.data,K.value=(d=c.data)==null?void 0:d.data,console.log(E.value),l.loading=!1}else E.value=[]},be=async()=>{var t,d,c,v;const s=await ht({start:ne().subtract(1,"year").startOf("day").valueOf(),end:ne().valueOf(),page:1,size:20},(d=(t=Re().navSelectedRange)==null?void 0:t.data)==null?void 0:d.id);l.alarmData=(v=(c=s.data)==null?void 0:c.data)==null?void 0:v.map(y=>{var z,G;const S=y.details.record||[];let k="";switch(y.type){case"scope":k="【范围告警】";break;case"change":k="【变动告警】";break;case"maintain":k="【维持告警】";break;case"offline":k="【掉线报警】";break}return{createdTime:y.createdTime,type:(z=S[0])==null?void 0:z.info,status:y.type,statusType:k,remark:(y.deviceName||"")+" "+(((G=S[0])==null?void 0:G.info)||""),time:yt(y.startTs)}})},xe=s=>{console.log(s)},we=()=>{var s,t;if(console.log(Y.value),Y.value){const d=(s=K.value)==null?void 0:s.find(c=>Y.value===c.id.id);console.log(d),d?(te.defaultValue=d,(t=X.value)==null||t.openDialog()):H("无此用户")}else H("输入用户姓名")},Ce=()=>{$.push({name:"defaultRoute_BJZX"})},De=async()=>{var c,v;const s=(v=(c=F.user)==null?void 0:c.tenantId)==null?void 0:v.id;if(!s)return;const t=$e(s),d=await He(t,"ALL");l.apps=d.data||[]},ae=()=>{$.push({name:"defaultRoute_TZGG"})},Ye=b({title:"功能选择",width:"40%",group:[{fields:[{type:"tree",label:"",field:"menuIdList",showCheckbox:!0,options:Ve(()=>l.menus)}]}],onClose:()=>{},submit:s=>{console.log("params",s)}}),Me=async()=>{var d;const s={type:"公告板",startTime:_().startOf("week").valueOf(),endTime:_().endOf("week").valueOf(),size:20,page:1},t=await vt(s);L.value=(d=t.data.data)==null?void 0:d.data},Fe=async()=>{const s=await gt();l.CurrentGuard=s.data.data||{};const t=l.CurrentGuard.partnerNames;l.partnerNames=(t==null?void 0:t.split(","))||[]};Be(()=>{De(),O(),J.value=F.user;const s=parseInt(_(N.value).format("HH"));l.hour=s<13?"上午好":s<19?"下午好":"晚上好",console.log("时间",l.hour),ye(),be(),Me(),se(_().format("YYYY-MM-DD")),oe(),Fe()});async function oe(){var t;await F.GetInfo();const s=((t=F.user.additionalInfo)==null?void 0:t.menu)||[];for(const d in s)l.shortcutMenu=[],s[d].forEach(c=>{c.children===!1&&l.shortcutMenu.push({...c,id:d})})}function Ne(s){h.SET_appid(s.id),qe().then(()=>{$.push({name:s.value,path:s.component})})}return(s,t)=>{const d=Je,c=Ze,v=it,y=Xe,S=Ke,k=Pe,z=Qe,G=et,Te=tt,Ee=st,U=lt,Se=at;return u(),I(Se,null,{default:n(()=>[e("div",bt,[e("div",xt,[e("div",wt,[o(v,{title:"",style:{height:"130px","border-radius":"12px"},class:le({isDark:a(h).isDark})},{default:n(()=>[e("div",Ct,[e("div",Dt,[o(d,{size:80,src:"@/assets/images/user-log.png",icon:"Avatar"}),e("div",Yt,[e("div",Mt,r(J.value.lastName)+"，"+r(a(l).hour),1),e("div",Ft,[g(r(a(_)(new Date).format("YYYY年MM月DD日"))+" "+r(D.value.week)+" ",1),o(c,{size:14,style:{"margin-left":"22px"}},{default:n(()=>[o(a(Le))]),_:1}),g(" "+r(D.value.city)+" "+r(D.value.weather)+"，"+r(D.value.temperature)+"℃ ",1)])])])])]),_:1},8,["class"]),o(v,{title:" ",style:{height:"386px","margin-top":"15px","border-radius":"12px"}},{title:n(()=>[e("div",Nt,[e("div",{style:x([{color:a(h).isDark?"#fff":""},{"padding-left":"10px"}])}," 日程安排 ",4),e("div",Tt,r(a(l).singleContract),1),e("div",null,[o(c,{size:20,style:{cursor:"pointer"},onClick:me},{default:n(()=>[o(a(Oe))]),_:1})])])]),default:n(()=>[e("div",Et,[o(c,{style:{cursor:"pointer"},onClick:ge},{default:n(()=>[o(a(Ue))]),_:1}),(u(!0),f(C,null,w(T.value,(i,m)=>(u(),f("div",{key:m,class:"week-style"},[e("div",null,[e("span",null,r(de[i.week]),1)]),ue.value===i.time?(u(),f("div",St,[t[3]||(t[3]=g(" 今 ")),j("",!0)])):(u(),f("div",{key:1,class:le(P.value===i.time?"today":""),onClick:A=>se(i.time)},[g(r(i.day)+" ",1),j("",!0)],10,zt))]))),128)),o(c,{style:{cursor:"pointer"},onClick:he},{default:n(()=>[o(a(We))]),_:1})]),e("div",Gt,[B.value.length>0?(u(),f("div",At,[(u(!0),f(C,null,w(B.value,(i,m)=>(u(),f("div",{key:m,class:"list-card",style:x(a(h).isDark?{background:i%2===0?"#222536":"#313748"}:{background:i%2===0?"#F8F9FF":"#F3F9FF"})},[e("div",It,[e("div",Vt,r(i.title),1),e("div",Bt,r(i.content),1)]),e("div",Lt,"日程"+r(m+1),1)],4))),128))])):(u(),f("div",Ot,t[4]||(t[4]=[e("img",{src:dt,style:{width:"191px",height:"138px"}},null,-1)])))])]),_:1}),o(v,{title:" ",class:"duty"},{title:n(()=>[e("div",Ut,[e("div",{style:x({color:a(h).isDark?"#fff":""})}," 值班中心 ",4)])]),default:n(()=>[e("div",Wt,[e("div",jt,[e("div",Rt,[e("div",$t,[t[5]||(t[5]=e("div",{class:"dut-t"},"值班地点",-1)),e("div",Ht,r(a(l).CurrentGuard.placeName),1)]),e("div",qt,[e("div",Jt,[t[6]||(t[6]=e("div",{class:"dut-t"},"值班班次",-1)),e("div",Zt,r(a(l).CurrentGuard.className)+"（"+r(a(l).CurrentGuard.beginTime)+" - "+r(a(l).CurrentGuard.endTime)+" ） ",1)]),t[7]||(t[7]=e("div",{class:"but",style:{cursor:"pointer"}},"值班签到",-1))])]),e("div",Xt,[t[8]||(t[8]=e("span",{class:"dut-t"}," 值班班长：",-1)),o(y,{effect:"plain",class:"ml-2",color:"#549ce1",style:{color:"#ffffff"},hit:""},{default:n(()=>[g(r(a(l).CurrentGuard.headName),1)]),_:1})]),e("div",Kt,[t[9]||(t[9]=e("span",{class:"dut-t"},"值班地点：",-1)),(u(!0),f(C,null,w(a(l).partnerNames,(i,m)=>(u(),I(y,{key:m,effect:"plain",class:"ml-2",color:"#549ce1",style:{color:"#ffffff","margin-right":"8px"},hit:""},{default:n(()=>[g(r(i),1)]),_:2},1024))),128))])])]),e("div",{class:"duty-event"},[e("div",{class:"duty-event-title",onClick:_e},t[10]||(t[10]=[e("img",{src:ct,style:{width:"40px",height:"40px"}},null,-1),e("div",{class:"text"},"事件记录",-1)])),t[11]||(t[11]=e("div",{class:"duty-event-title"},[e("img",{src:ut,style:{width:"40px",height:"40px"}}),e("div",{class:"text"},"交接班登记")],-1))])]),_:1})]),e("div",Pt,[o(v,{title:" ",style:{height:"287px","border-radius":"12px","padding-left":"20px","padding-right":"20px"}},{title:n(()=>[e("div",Qt,[e("div",{style:x({color:a(h).isDark?"#fff":""})}," 常用功能 ",4),e("div",{class:"more-arrow",onClick:t[0]||(t[0]=()=>{a(M).visible=!a(M).visible})},[o(c,{size:20,style:{cursor:"pointer"}},{default:n(()=>[o(a(je))]),_:1})])])]),default:n(()=>[e("div",es,[(u(!0),f(C,null,w(a(l).shortcutMenu,(i,m)=>(u(),f("div",{key:m,class:"lattice",onClick:A=>Ne(i)},[e("div",ss,[t[12]||(t[12]=e("div",{class:"image"},[e("img",{src:ft,style:{width:"36px",height:"36px"}})],-1)),e("div",null,[e("span",as,r(i.label),1)])])],8,ts))),128))])]),_:1}),e("div",os,[o(v,{title:" ",class:"notice-card",style:{"border-radius":"12px"}},{title:n(()=>[e("div",ls,[e("div",{style:x({color:a(h).isDark?"#fff":""})}," 通知公告 ",4),e("div",{class:"more-arrow",onClick:ae},[t[13]||(t[13]=g(" 查看全部")),o(c,{size:16},{default:n(()=>[o(a(R))]),_:1})])])]),default:n(()=>[e("div",is,[L.value.lenght>0?(u(),f("ul",ns,[(u(!0),f(C,null,w(L.value,(i,m)=>(u(),f("li",{key:m,class:"list"},[e("div",null,r(i.title),1),e("div",rs,r(a(_)(i.createTime).format("YYYY-MM-DD HH:mm:ss")),1)]))),128))])):(u(),f("div",ds,t[14]||(t[14]=[e("img",{src:pt,style:{width:"150px",height:"108px"}},null,-1)])))])]),_:1}),o(v,{title:" ",class:"alarm-card",style:{"border-radius":"12px"}},{title:n(()=>[e("div",cs,[e("div",{style:x({color:a(h).isDark?"#fff":""})}," 报警列表 ",4),e("div",{class:"more-arrow",onClick:Ce},[t[15]||(t[15]=g(" 查看全部")),o(c,{size:16,color:"#A5AECD"},{default:n(()=>[o(a(R))]),_:1})])])]),default:n(()=>[e("div",us,[e("ul",fs,[(u(!0),f(C,null,w(a(l).alarmData,(i,m)=>(u(),f("li",{key:m,class:"list"},[e("div",null,r(i.statusType)+" "+r(i.remark),1),e("div",ps,r(ve(i.createdTime)),1)]))),128))])])]),_:1})]),e("div",ms,[o(v,{title:" ",class:"book-card",style:{"border-radius":"12px",height:"184px"}},{title:n(()=>[e("div",_s,[e("div",{style:x({color:a(h).isDark?"#fff":""})}," 单位通讯录 ",4),e("div",{class:"more-arrow",onClick:ae},[t[16]||(t[16]=g(" 查看全部")),o(c,{size:16},{default:n(()=>[o(a(R))]),_:1})])])]),default:n(()=>[e("div",vs,[e("div",gs,[e("div",hs,[o(k,{modelValue:Y.value,"onUpdate:modelValue":t[1]||(t[1]=i=>Y.value=i),filterable:"",remote:"",size:"large","reserve-keyword":"","value-key":"id",placeholder:"输入用户姓名","remote-method":ke,loading:a(l).loading,style:{width:"90%",height:"56px"},onChange:xe},{default:n(()=>[(u(!0),f(C,null,w(E.value,(i,m)=>{var A;return u(),I(S,{key:m,label:i.firstName,value:(A=i.id)==null?void 0:A.id},null,8,["label","value"])}),128))]),_:1},8,["modelValue","loading"]),e("div",null,[o(z,{type:"primary",size:"large",class:"but",onClick:we},{default:n(()=>t[17]||(t[17]=[g(" 查询 ")])),_:1})])])])])]),_:1})])])]),o(G,{ref_key:"refForm_Add",ref:ce,config:a(Ye)},null,8,["config"]),o(U,{ref_key:"refForm",ref:V,config:a(fe)},{default:n(()=>[o(Te,{modelValue:N.value,"onUpdate:modelValue":t[2]||(t[2]=i=>N.value=i)},null,8,["modelValue"]),o(Ee,{ref_key:"refFormConfig",ref:Q,config:a(pe)},null,8,["config"])]),_:1},8,["config"]),o(U,{ref_key:"refUserForm",ref:X,config:a(te)},null,8,["config"]),a(M).visible?(u(),I(kt,{key:0,"table-config":a(M),onRefresh:oe},null,8,["table-config"])):j("",!0),o(U,{ref_key:"refEventForm",ref:Z,config:a(ee)},null,8,["config"])])]),_:1})}}}),Ts=ot(ys,[["__scopeId","data-v-388aa068"]]);export{Ts as default};
