package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecord;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageQueueMessageRecordSendRequestInfo;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageRecordSendRequest;

import java.util.List;

public interface MessageRecordService {
    /**
     * 分页条件查询短信发起记录
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<MessageRecord> findAllConditional(MessageRecordPageRequest request);

    /**
     * 保存短信发起记录
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    MessageRecord save(MessageRecord entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(MessageRecord entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 发起短信
     *
     * @param req 消息发送请求
     * @return 是否发送成功
     */
    boolean send(MessageRecordSendRequest req);

    /**
     * 获取短信发起信息
     *
     * @param markAsSending 是否标记为正在发送状态
     * @return 短信发起信息
     */
    List<MessageQueueMessageRecordSendRequestInfo> getReadyMessages(boolean markAsSending);

    /**
     * 标记为发送成功状态
     *
     * @param id 短信发起信息id
     * @return 是否标记成功
     */
    boolean markAsSuccess(String id);

    /**
     * 标记为发送失败状态
     *
     * @param id 短信发起信息id
     * @return 是否标记成功
     */
    boolean markAsFailure(String id, String code);

}
