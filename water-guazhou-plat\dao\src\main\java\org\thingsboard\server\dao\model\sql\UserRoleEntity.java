/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;


import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.RoleId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.role.UserRole;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.CUSTOMER_USER_ROLE)
public class UserRoleEntity extends BaseSqlEntity<UserRole> {

    @Column(name = ModelConstants.CUSTOMER_USER_ROLE_MENU_ID)
    private String userId;

    @Column(name = ModelConstants.CUSTOMER_USER_ROLE_ROLE_ID)
    private String roleId;


    public UserRoleEntity() {
    }

    public UserRoleEntity(UserRole userRole) {
        this.id = userRole.getId();
        this.userId = toString(userRole.getUserId().getId());
        this.roleId = toString(userRole.getRoleId().getId());
    }

    @Override
    public UserRole toData() {
        UserRole userRole = new UserRole();
        userRole.setId(id);
        userRole.setUserId(new UserId(toUUID(userId)));
        userRole.setRoleId(new RoleId(toUUID(roleId)));

        return userRole;
    }
}
