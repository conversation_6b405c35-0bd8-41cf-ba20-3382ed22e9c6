package org.thingsboard.server.dao.model.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.station.StationMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.Info;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.PENDING;

@Getter
@Setter
@ResponseEntity
@TableName("sp_circuit_task")
public class CircuitTask {
    // id
    @TableId
    private String id;

    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    private String type;

    // 任务编号
    private String code;

    // 任务名称
    private String name;

    // 任务类型。常规任务、临时任务。常规任务为巡检计划生成的任务；临时任务为任务页面新增的任务
    private String taskType;

    // 执行巡检人员
    @ParseUsername(withDepartment = true)
    private String executionUserId;

    // 巡检成果审核人员
    @ParseUsername(withDepartment = true)
    private String auditUserId;

    // 预计开始时间
    private Date startTime;

    // 预计结束时间
    private Date endTime;

    // 实际开始时间
    private Date realStartTime;

    // 实际结束时间
    private Date realEndTime;

    // 要巡检的站点
    @ParseViaMapper(StationMapper.class)
    private String stationId;

    // 巡检模板
    private String templateId;

    // 任务状态
    @Info(name = "statusName")
    private String status;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

    private String statusName() {
        if (status.equals(PENDING.name())) {
            return "待接收";
        } else if (realEndTime == null) {
            return "处理中";
        }

        return realEndTime.getTime() > endTime.getTime() ? "超时完成" : "按时完成";
    }

}
