package org.thingsboard.server.dao.sql.workOrder;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.WorkOrderDetailDTO;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderDetail;

import java.util.List;

@Mapper
public interface WorkOrderDetailMapper extends BaseMapper<WorkOrderDetail> {
    List<WorkOrderDetail> findAllDetails(String id);


    List<WorkOrderDetail> getStagesByWorkOrder(String id);

    List<WorkOrderDetailDTO> getDetails(@Param("workOrderId") String workOrderId);
}
