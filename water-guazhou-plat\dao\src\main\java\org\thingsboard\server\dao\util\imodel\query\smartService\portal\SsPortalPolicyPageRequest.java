package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicy;

@Getter
@Setter
public class SsPortalPolicyPageRequest extends AdvancedPageableQueryEntity<SsPortalPolicy, SsPortalPolicyPageRequest> {
    // 名称
    private String name;
}
