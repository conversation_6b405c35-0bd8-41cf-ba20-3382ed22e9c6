<template>
  <div class="item">
    <div
      v-if="config.rows?.indexOf(1) !== -1"
      class="row"
    >
      <span class="title">{{ props.config.label }}</span>
    </div>
    <div
      v-if="config.rows?.indexOf(2) !== -1"
      class="row"
    >
      <span class="value blue">{{ props.config.value }} </span>
      <span class="unit">{{ props.config.unit }}</span>
      <Icon
        v-if="config.status"
        :icon="
          config.status === 'up'
            ? 'material-symbols:arrow-drop-up'
            : 'material-symbols:arrow-drop-down'
        "
        :class="config.status"
      ></Icon>
      <span
        class="value blue"
        :class="config.status"
      >
        {{ props.config.scale }}
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue'

const props = defineProps<{ config: ITargetItem }>()
</script>

<style lang="scss" scoped>
.item {
  width: 180px;
  height: 65px;
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 24px;
  padding: 8px;
  background: url(../imgs/smartproduce_center_item_item.png) 0 0 /100% 100% no-repeat;
  .row {
    display: flex;
    align-items: center;
  }
  .title {
    font-size: 14px;
  }
  .text {
    font-size: 12px;
    margin-right: 8px;
  }
  .value {
    font-size: 18px;
  }
  .unit {
    font-size: 12px;
    margin-left: 4px;
  }
}
.blue {
  color: #2b70e6;
}
.up {
  color: red;
}

.down {
  color: green;
}

.white {
  color: white;
}
</style>
