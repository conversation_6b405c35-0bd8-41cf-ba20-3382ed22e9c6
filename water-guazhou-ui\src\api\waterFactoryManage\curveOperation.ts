// 曲线运行 API
import request from '@/plugins/axios'

// 获取曲线运行实时数据
export function getCurveOperationData(params?: {
  stationId?: string
}) {
  return request({
    url: '/istar/api/production/waterPlant/getCurveOperationData',
    method: 'get',
    params
  })
}

// 获取曲线运行预测数据
export function getCurveOperationPrediction(params: {
  type?: string
  pressureType?: string
  stationId?: string
  timeRange?: number
}) {
  return request({
    url: '/istar/api/production/waterPlant/getCurveOperationPrediction',
    method: 'get',
    params
  })
}

// 获取出水流量历史数据
export function getOutletFlowHistory(params: {
  stationId?: string
  startTime: number
  endTime: number
  granularity?: string // hour, day, month
}) {
  return request({
    url: '/istar/api/production/waterPlant/getOutletFlowHistory',
    method: 'get',
    params
  })
}

// 获取压力历史数据
export function getPressureHistory(params: {
  stationId?: string
  startTime: number
  endTime: number
  pressureType: 'inlet' | 'outlet' | 'both'
  granularity?: string
}) {
  return request({
    url: '/istar/api/production/waterPlant/getPressureHistory',
    method: 'get',
    params
  })
}

// 获取水体药物浓度数据
export function getWaterConcentrationData(params: {
  stationId?: string
  startTime: number
  endTime: number
  drugType?: string
}) {
  return request({
    url: '/istar/api/production/waterPlant/getWaterConcentrationData',
    method: 'get',
    params
  })
}

// 应用运行参数
export function applyCurveOperationParams(params: {
  stationId?: string
  timeRangeEnabled: boolean
  supplyFlow: number
  minFlow: number
  maxFlow: number
  howParameter?: string
}) {
  return request({
    url: '/istar/api/production/waterPlant/applyCurveOperationParams',
    method: 'post',
    data: params
  })
}
