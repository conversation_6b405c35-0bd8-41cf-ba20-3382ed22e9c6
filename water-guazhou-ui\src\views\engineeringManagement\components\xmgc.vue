<!-- 工程管理-详情-项目工程 -->
<template>
  <CardTable
    title="项目工程"
    :config="TableConfig"
    class="card-table"
  ></CardTable>
</template>

<script lang="ts" setup>
import { getSingleProjectList } from '@/api/engineeringManagement/manage';

const props = defineProps<{ id: string }>();

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '工程编号', prop: 'name' },
    { label: '工程名称', prop: 'name' },
    { label: '工程地址', prop: 'address' },
    { label: '工程类别', prop: 'typeName' },
    { label: '工程预算(万元)', prop: 'estimate' },
    { label: '申请单位', prop: 'fitstpartName' }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const refreshData = async () => {
  getSingleProjectList({ page: 1, size: -1, projectCode: props.id }).then(
    (res) => {
      TableConfig.dataList = res.data.data.data || [];
    }
  );
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.card-table {
  height: 300px;
  margin-bottom: 20px;
}
</style>
