<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
  </div>
</template>
<script lang="ts" setup>
const SearchConfig = reactive<ISearch>({
  filters: [{ type: 'input', label: '关键词', field: 'name' }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询' },
        { perm: true, type: 'primary', text: '新增' }
      ]
    }
  ],
  defaultParams: {}
})
const TableConfig = reactive<ITable>({
  columns: [
    { label: '方案名称', prop: 'name' },
    { label: '创建时间', prop: 'time' }
  ],
  dataList: [],
  operations: [{ perm: true, text: '删除', type: 'danger' }],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const refreshData = () => {
  //
}
</script>
<style lang="scss" scoped></style>
