package org.thingsboard.server.dao.model.sql.shuiwu;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * 水务-巡检管理主表
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SHUIWU_XJRWGL_C_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class SwXjrwglCEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_C_MAIN_ID)
    private String mainId;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_C_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_C_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.SHUIWU_CRITERION_DEVICE_TYPE)
    private String deviceType;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_C_CRITERION_ID)
    private String criterionId;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_C_ORDER_NUMBER)
    private Integer orderNum;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.REMARK)
    private String remark;

    @Transient
    private String deviceName;

    @Transient
    private String criterionName;

    @Transient
    private String projectName;
}
