import request from '@/plugins/axios'

export const getAdminSettings = (key:any) => {
  return request({
    url: `/api/admin/settings/${key}`,
    method: 'get'
  })
}

export const saveAdminSettings = (params:any) => {
  return request({
    url: '/api/admin/settings',
    method: 'post',
    data: params
  })
}

export const sendTestMail = (params:any) => {
  return request({
    url: '/api/admin/settings/testMail',
    method: 'post',
    data: params
  })
}

export const setMesKey = (params:any) => {
  return request({
    url: '/api/setSmsKey',
    method: 'post',
    data: params
  })
}

export const sendMes = (key:any) => {
  return request({
    url: `/api/testSms?phone=${key}`,
    method: 'get'
  })
}

export const getSMKey = () => {
  return request({
    url: '/api/getSmsKey',
    method: 'get'
  })
}

/**
 * 查询消息通知数量统计
 * @param params {to:用户ID  status:0未读 1已读}
 * @returns
 */
export const getNotifyCount = (params:{
  to:string
  status:number
}) => {
  return request({
    url: '/api/systemNotify/notifyCount',
    method: 'get',
    params
  })
}

/**
 * 查询系统通知消息列表
 * @param params
 * @returns
 */
export const getSystemNotify = (params:{
  page:string|number
  size:string|number
  to?:string
  type?:number,
  topic?:string
  fromName?:string
  beginTime?:string
  endTime?:string
}) => {
  return request({
    url: '/api/systemNotify/list',
    method: 'get',
    params
  })
}

/**
 * 一键已读
 * @param params
 * @returns
 */
export const postReadAll = (params:{
  to:string
  type:number
}) => {
  return request({
    url: '/api/systemNotify/readAll',
    method: 'post',
    data: params
  })
}

/**
 * 消息已读
 * @param params
 * @returns
 */
export const postRead = (params:{
  id:string
}) => {
  return request({
    url: '/api/systemNotify/read',
    method: 'post',
    data: params
  })
}
