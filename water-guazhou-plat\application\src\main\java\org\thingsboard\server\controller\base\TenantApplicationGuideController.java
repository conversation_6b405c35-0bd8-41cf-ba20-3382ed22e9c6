package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.TenantApplicationGuideEntity;
import org.thingsboard.server.dao.tenant.TenantApplicationGuideService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

@RestController
@RequestMapping("api/tenantApplicationGuide")
public class TenantApplicationGuideController extends BaseController {

    @Autowired
    private TenantApplicationGuideService tenantApplicationGuideService;

    @PostMapping
    public IstarResponse save(@RequestBody TenantApplicationGuideEntity tenantApplicationGuideEntity) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        tenantApplicationGuideEntity.setCreator(userId);
        tenantApplicationGuideEntity.setTenantId(tenantId);

        return tenantApplicationGuideService.save(tenantApplicationGuideEntity);
    }

    @GetMapping("list")
    public IstarResponse getList() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(tenantApplicationGuideService.getList(tenantId));
    }

    @GetMapping("notSetList")
    public IstarResponse getNotSetList() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(tenantApplicationGuideService.getNotSetList(tenantId));
    }

    @GetMapping("getByApplicationId/{applicationId}")
    public IstarResponse getByApplicationId(@PathVariable String applicationId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(tenantApplicationGuideService.getByApplicationId(applicationId));
    }
}
