package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.model.sql.base.BasePipeLineConfiguration;
import org.thingsboard.server.dao.base.IBasePipeLineConfigurationService;
import org.thingsboard.server.dao.util.imodel.query.base.BasePipeLineConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 平台管理-管网配置Controller
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Api(tags = "平台管理-管网配置")
@RestController
@RequestMapping("api/base/pipe/configuration")
public class BasePipeLineConfigurationController extends BaseController {

    @Autowired
    private IBasePipeLineConfigurationService basePipeLineConfigurationService;

    /**
     * 查询平台管理-管网配置列表
     */
    @MonitorPerformance(description = "平台管理-查询管网配置列表接口")
    @ApiOperation(value = "查询管网配置列表")
    @GetMapping("/list")
    public IstarResponse list(BasePipeLineConfigurationPageRequest basePipeLineConfiguration) {
        return IstarResponse.ok(basePipeLineConfigurationService.selectBasePipeLineConfigurationList(basePipeLineConfiguration));
    }

    /**
     * 获取平台管理-管网配置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询管网配置详情接口")
    @ApiOperation(value = "获取管网配置详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(basePipeLineConfigurationService.selectBasePipeLineConfigurationById(id));
    }

    /**
     * 新增平台管理-管网配置
     */
    @MonitorPerformance(description = "平台管理-新增管网配置接口")
    @ApiOperation(value = "新增管网配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BasePipeLineConfiguration basePipeLineConfiguration) {
        return IstarResponse.ok(basePipeLineConfigurationService.insertBasePipeLineConfiguration(basePipeLineConfiguration));
    }

    /**
     * 修改平台管理-管网配置
     */
    @MonitorPerformance(description = "平台管理-修改管网配置接口")
    @ApiOperation(value = "修改管网配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BasePipeLineConfiguration basePipeLineConfiguration) {
        return IstarResponse.ok(basePipeLineConfigurationService.updateBasePipeLineConfiguration(basePipeLineConfiguration));
    }

    /**
     * 删除平台管理-管网配置
     */
    @MonitorPerformance(description = "平台管理-删除管网配置接口")
    @ApiOperation(value = "删除管网配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(basePipeLineConfigurationService.deleteBasePipeLineConfigurationByIds(ids));
    }
}
