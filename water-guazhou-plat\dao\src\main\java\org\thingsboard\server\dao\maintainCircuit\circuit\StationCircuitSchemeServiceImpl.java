package org.thingsboard.server.dao.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.maintainCircuit.StationCircuitSchemeService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitScheme;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.StationCircuitSchemeMapper;
import org.thingsboard.server.dao.user.UserService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StationCircuitSchemeServiceImpl implements StationCircuitSchemeService {

    @Autowired
    private StationCircuitSchemeMapper stationCircuitSchemeMapper;

    @Autowired
    private UserService userService;

    @Override
    public void save(StationCircuitScheme entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            stationCircuitSchemeMapper.insert(entity);
        } else {
            stationCircuitSchemeMapper.updateById(entity);
        }
    }

    @Override
    public PageData<StationCircuitScheme> findList(int page, int size, String name, String stationType, TenantId tenantId) {
        Page<StationCircuitScheme> pageRequest = new Page<>(page, size);
        IPage<StationCircuitScheme> pageResult = stationCircuitSchemeMapper.findList(pageRequest, name, stationType, UUIDConverter.fromTimeUUID(tenantId.getId()));
        // 查询用户
        List<User> userList = userService.findUserByTenant(tenantId);
        Map<String, User> userMap = userList.stream()
                .collect(Collectors.toMap(user -> UUIDConverter.fromTimeUUID(user.getUuidId()), user -> user));
        List<StationCircuitScheme> records = pageResult.getRecords();
        for (StationCircuitScheme record : records) {
            // 设置人员名称
            User createUser = userMap.get(record.getCreateUser());
            if (createUser != null) {
                record.setCreateUserName(createUser.getFirstName());
            }
        }

        return new PageData<>(pageResult.getTotal(), records);
    }

    @Override
    public void remove(List<String> ids) {
        stationCircuitSchemeMapper.deleteBatchIds(ids);
    }

    @Override
    public StationCircuitScheme findById(String id) {
        return stationCircuitSchemeMapper.selectById(id);
    }
}
