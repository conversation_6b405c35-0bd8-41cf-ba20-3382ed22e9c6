import{d as l,r as c,a8 as r,g as i,h as p,F as n,q as d,i as f,bz as m,C as x}from"./index-r0dFAfgr.js";import{d as _}from"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const b=l({__name:"ssgcjsjcxx",props:{config:{}},setup(e){const t=e,a=c({defaultValue:r(()=>t.config),border:!0,direction:"horizontal",column:2,title:"所属工程结算基础信息",fields:[{type:"text",label:"结算人:",field:"processUser"},{type:"text",label:"结算金额:",field:"cost"},{type:"text",label:"结算备注:",field:"remark"},{type:"text",label:"合同总金额:",field:"contractTotalCost"},{type:"text",label:"费用总金额:",field:"expenseTotalCost"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName"},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]});return(u,y)=>{const o=_,s=m;return i(),p(s,{class:"card"},{default:n(()=>[d(o,{config:f(a)},null,8,["config"])]),_:1})}}}),T=x(b,[["__scopeId","data-v-266a1bb1"]]);export{T as default};
