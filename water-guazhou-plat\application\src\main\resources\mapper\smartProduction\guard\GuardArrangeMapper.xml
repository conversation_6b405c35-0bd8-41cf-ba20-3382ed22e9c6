<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.guard.GuardArrangeMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        place_id,
        (select address from guard_place where id = #{placeId}) place_name,
        class_id,
        class_name,
        day_time,
        begin_time,
        end_time,
        group_id,
        group_name,
        department_id,
        head,
        update_user_id,
        update_time,
        creator,
        create_time,
        tenant_id,
        (select string_agg(first_name, '、')
         from guard_arrange_partner
                  join tb_user on guard_arrange_partner.user_id = tb_user.id
         where arrange_id = guard_arrange.id)                   partner_names
        <!--@sql from guard_arrange -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrange">
        <result column="id" property="id"/>
        <result column="place_id" property="placeId"/>
        <result column="place_name" property="placeName"/>
        <result column="class_id" property="classId"/>
        <result column="class_name" property="className"/>
        <result column="day_time" property="dayTime"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="department_id" property="departmentId"/>
        <result column="head" property="head"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="partner_names" property="partnerNames"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from guard_arrange
        <where>
            <if test="placeId != null and placeId != ''">
                and place_id = #{placeId}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by begin_time::time
    </select>

    <update id="updateFully">
        update guard_arrange
        set place_id       = #{placeId},
            class_name     = #{className},
            day_time       = #{dayTime},
            begin_time     = #{beginTime},
            end_time       = #{endTime},
            department_id  = #{departmentId},
            head           = #{head},
            update_time    = #{updateTime},
            update_user_id = #{updateUserId}
        where id = #{id}
    </update>

    <insert id="quickArrange">
        INSERT INTO guard_arrange(id,
                                  place_id,
                                  class_id,
                                  class_name,
                                  day_time,
                                  begin_time,
                                  end_time,
                                  group_id,
                                  group_name,
                                  department_id,
                                  head,
                                  update_user_id,
                                  update_time,
                                  creator,
                                  create_time,
                                  tenant_id)VALUES
        <foreach collection="arrangeDataList" item="item" index="index" separator=",">
            (#{item.generatedId},
             #{item.placeId},
             #{item.classId},
             (select name from guard_class where id = #{item.classId}),
             #{item.dayTime},
             (select begin_time from guard_class where id = #{item.classId}),
             (select end_time from guard_class where id = #{item.classId}),
             #{item.groupId},
             (select name from guard_group where id = #{item.groupId}),
             (select department_id from guard_group where id = #{item.groupId}),
             (select head from guard_group where id = #{item.groupId}),
             #{userId},
             now(),
             #{userId},
             now(),
             #{tenantId})
        </foreach>
    </insert>

    <select id="detectArrangeOverride" resultType="boolean">
        <bind name="tempGuardClassName"
              value="@org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrange@TEMP_GUARD"/>
        select count(1) > 0
        from guard_arrange
        where class_name != #{tempGuardClassName}
          and day_time in
        <foreach item="item" index="index" collection="hitDate" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="removeOnQuickArrangeAtDate" resultType="java.lang.String">
        <bind name="tempGuardClassName"
              value="@org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrange@TEMP_GUARD"/>
        delete
        from guard_arrange
        where class_name != #{tempGuardClassName}
          and day_time in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        returning id
    </select>
    <select id="getCurrentGuard" resultType="org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrange">
        SELECT a.*, b.address as placeName, c.first_name as headName,  (select string_agg(first_name, ',')
                                                                        from guard_arrange_partner
                                                                                 join tb_user on guard_arrange_partner.user_id = tb_user.id
                                                                        where arrange_id = a.id)                   partnerNames
        FROM guard_arrange a
        left join guard_place b on a.place_id = b.id
        left join tb_user c on a.head = c.id
        left join guard_arrange_partner d on a.id = d.arrange_id
        left join tb_user f on d.user_id = f.id
        where to_timestamp(#{now} / 1000) &gt;= to_timestamp(to_char(day_time, 'YYYY-MM-DD ') || begin_time, 'YYYY-MM-DD hh24:mi')
        and to_timestamp(#{now} / 1000) &lt; to_timestamp(to_char(day_time, 'YYYY-MM-DD ') || end_time, 'YYYY-MM-DD hh24:mi')
        order by begin_time desc offset 0 limit 1
    </select>
</mapper>