import{_ as b}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_}from"./CardTable-rdWOL4_6.js";import{e as C,f as h,h as x}from"./manage-BReaEVJk.js";import{d as y,c as D,r as d,dP as k,x as s,S as L,o as T,g as v,n as B,q as p,i as f,aB as N,C as V}from"./index-r0dFAfgr.js";import{C as w}from"./data-DDQ4eWNr.js";import"./index-C9hz-UZb.js";const E=y({__name:"sjbg",props:{id:{},config:{}},setup(u){const l=D(),i=u,r=d({defaultExpandAll:!0,indexVisible:!0,title:"设计变更",titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加",perm:!0,click:()=>{var e;o.defaultValue={code:`${i.config.code||""}-${k()}`,designCode:i.config.code||"",constructionCode:i.config.constructionCode,type:"提高合理化建议"},(e=l.value)==null||e.openDialog()}}]}]}],columns:[{label:"变更编号",prop:"code"},{label:"变更类型",prop:"type"},{label:"添加人",prop:"creatorName"},{label:"添加时间",prop:"createTimeName"},{label:"说明",prop:"remark"},{label:"附件",prop:"attachments",download:!0}],operationWidth:"160px",operations:[{isTextBtn:!1,type:"success",text:"编辑设计",perm:!0,click:e=>g(e)},{isTextBtn:!1,type:"danger",text:"删除",perm:!0,click:e=>m(e)}],dataList:[],pagination:{hide:!0}}),o=d({title:"添加设计变更",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{o.submitting=!0;let t="新增";e.id&&(t="修改"),e.pipLengthDesign=JSON.stringify(e.pipLengthDesign),C(e).then(n=>{var a;o.submitting=!1,n.data.code===200?(s.success(t+"成功"),(a=l.value)==null||a.closeDialog(),c()):s.warning(t+"失败")}).catch(n=>{o.submitting=!1,s.warning(n)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"变更编号",field:"code",disabled:!0},{xs:12,type:"input",label:"设计编号",field:"designCode",disabled:!0},{xs:12,type:"select",label:"变更类型",field:"type",options:w},{type:"textarea",label:"说明",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),g=e=>{var t;o.title="编辑设计变更",o.defaultValue={...e||{},constructionCode:i.config.constructionCode},(t=l.value)==null||t.openDialog()},m=e=>{L("确定删除？","提示信息").then(()=>{h(e.id).then(t=>{var n,a;((n=t.data)==null?void 0:n.code)===200?(s.success("删除成功"),c()):s.warning((a=t.data)==null?void 0:a.message)})}).catch(()=>{})},c=async()=>{x({page:1,size:-1,designCode:i.config.code,constructionCode:i.config.constructionCode}).then(e=>{r.dataList=e.data.data.data||[]})};return T(()=>{c()}),(e,t)=>{const n=_,a=b;return v(),B(N,null,[p(n,{config:f(r),class:"card-table"},null,8,["config"]),p(a,{ref_key:"refForm",ref:l,config:f(o)},null,8,["config"])],64)}}}),I=V(E,[["__scopeId","data-v-7934bf25"]]);export{I as default};
