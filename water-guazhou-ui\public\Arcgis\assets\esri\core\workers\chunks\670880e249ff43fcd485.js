"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[2664],{65845:(e,t,a)=>{a.d(t,{D:()=>n});var r=a(81153);function n(e){e&&e.writtenProperties&&e.writtenProperties.forEach((({target:e,propName:t,newOrigin:a})=>{(0,r.l)(e)&&a&&e.originOf(t)!==a&&e.updateOrigin(t,a)}))}},81153:(e,t,a)=>{function r(e){return e&&"getAtOrigin"in e&&"originOf"in e}a.d(t,{l:()=>r})},32664:(e,t,a)=>{a.r(t),a.d(t,{save:()=>J,saveAll:()=>A,saveAs:()=>$});var r=a(67676),n=a(20102),o=a(92604),l=a(70586),i=a(95330),s=a(65845),u=a(19238),c=a(66677),d=a(52104),y=a(84230),p=a(65587),f=a(15235),m=a(33516),w=a(14661);const v=o.Z.getLogger("esri.layers.FeatureLayer"),h="Feature Service";function b(e,t){return`Layer (title: ${e.title}, id: ${e.id}) of type '${e.declaredClass}' ${t}`}function g(e,t){if(t.type!==h)throw new n.Z("feature-layer:portal-item-wrong-type",b(e,`should have portal item of type "${h}"`))}async function I(e){if(await e.load(),(0,y.rQ)(e))throw new n.Z("feature-layer:save",b(e,"using an in-memory source cannot be saved to a portal item"))}async function S(e,t,a){"beforeSave"in e&&"function"==typeof e.beforeSave&&await e.beforeSave();const r=e.write({},t);return function(e,t){let a=(e.messages??[]).filter((({type:e})=>"error"===e)).map((({name:e,message:t,details:a})=>new n.Z(e,t,a)));if(t?.ignoreUnsupported&&(a=a.filter((({name:e})=>"layer:unsupported"!==e&&"symbol:unsupported"!==e&&"symbol-layer:unsupported"!==e&&"property:unsupported"!==e&&"url:unsupported"!==e))),a.length>0)throw new n.Z("feature-layer:save","Failed to save feature layer due to unsupported or invalid content. See 'details.errors' for more detailed information",{errors:a})}(t,a),r}function O(e){const{layer:t,layerJSON:a}=e;return t.isTable?{layers:[],tables:[a]}:{layers:[a],tables:[]}}function N(e){(0,w.qj)(e,w.Kz.JSAPI),e.typeKeywords&&(e.typeKeywords=e.typeKeywords.filter(((e,t,a)=>a.indexOf(e)===t)))}async function P(e,t){return/\/\d+\/?$/.test(e.url??"")?O(t[0]):async function(e,t){const{layer:{url:a,customParameters:r,apiKey:n}}=t[0];let o=await e.fetchData("json");o&&null!=o.layers&&null!=o.tables||(o=await async function(e,t,a){var r,n;e||(e={}),(r=e).layers||(r.layers=[]),(n=e).tables||(n.tables=[]);const{url:o,customParameters:s,apiKey:c}=t,{serviceJSON:y,layersJSON:p}=await(0,d.V)(o,{customParameters:s,apiKey:c}),f=Z(e.layers,y.layers,a),m=Z(e.tables,y.tables,a);e.layers=f.itemResources,e.tables=m.itemResources;const w=[...f.added,...m.added],v=p?[...p.layers,...p.tables]:[];return await async function(e,t,a,r){const n=t.map((({id:e})=>new u.default({url:a,layerId:e,sourceJSON:r.find((({id:t})=>t===e))})));await(0,i.as)(n.map((e=>e.load()))),n.forEach((t=>{const{layerId:a,loaded:r,defaultPopupTemplate:n}=t;r&&!(0,l.Wi)(n)&&D(t,{id:a,popupInfo:n.toJSON()},e)}))}(e,w,o,v),e}(o,{url:a??"",customParameters:r,apiKey:n},t.map((e=>e.layer.layerId))));for(const e of t)D(e.layer,e.layerJSON,o);return o}(e,t)}function Z(e,t,a){const n=(0,r.e5)(e,t,((e,t)=>e.id===t.id));e=e.filter((e=>!n.removed.some((t=>t.id===e.id))));const o=n.added.map((({id:e})=>({id:e})));return o.forEach((({id:t})=>{e.push({id:t})})),{itemResources:e,added:o.filter((({id:e})=>!a.includes(e)))}}function D(e,t,a){e.isTable?K(a.tables,t):K(a.layers,t)}function K(e,t){if(!e)return;const a=e.findIndex((({id:e})=>e===t.id));-1===a?e.push(t):e[a]=t}function T(e){const{portalItem:t}=e;return(0,y.y2)(e)&&!e.dynamicDataSource&&!!t?.loaded&&t.type===h}const J=(0,i.Ds)((async function(e,t){await I(e),function(e){const t=e.portalItem;if(!t)throw v.error("save: requires the portalItem property to be set"),new n.Z("feature-layer:portal-item-not-set",b(e,"requires the portalItem property to be set"));if(!t.loaded)throw new n.Z("feature-layer:portal-item-not-loaded",b(e,"cannot be saved to a portal item that does not exist or is inaccessible"));g(e,t)}(e);const a=e.portalItem,r=(0,m.Y)(a),o=await S(e,r,t),l=await P(a,[{layer:e,layerJSON:o}]);return N(a),await a.update({data:l}),(0,s.D)(r),a})),A=(0,i.Ds)((async(e,t)=>{await async function(e){if(!e?.length)throw new n.Z("feature-layer-utils-saveall:missing-parameters","'layers' array should contain at least one feature layer");await Promise.all(e.map((e=>e.load())));for(const t of e)if(!T(t))throw new n.Z("feature-layer-utils-saveall:invalid-parameters",`'layers' array should only contain layers or tables in a feature service loaded from 'Feature Service' item. ${b(t,"does not conform")}`,{layer:t});const t=e.map((e=>e.portalItem.id));if(new Set(t).size>1)throw new n.Z("feature-layer-utils-saveall:invalid-parameters","All layers in the 'layers' array should be loaded from the same portal item");const a=e.map((e=>e.layerId));if(new Set(a).size!==a.length)throw new n.Z("feature-layer-utils-saveall:invalid-parameters","'layers' array should contain only one instance each of layer or table in a feature service")}(e);const a=e[0].portalItem,r=(0,m.Y)(a),o=await Promise.all(e.map((e=>S(e,r,t)))),l=await P(a,e.map(((e,t)=>({layer:e,layerJSON:o[t]}))));return N(a),await a.update({data:l}),await Promise.all(e.slice(1).map((e=>e.portalItem.reload()))),(0,s.D)(r),a.clone()})),$=(0,i.Ds)((async function(e,t,a){await I(e);const r=function(e,t){var a,r;let n=f.default.from(t);return n.id&&(n=n.clone(),n.id=null),(a=n).type??(a.type=h),(r=n).portal??(r.portal=p.Z.getDefault()),g(e,n),n}(e,t),n=(0,m.Y)(r),o=O({layer:e,layerJSON:await S(e,n,a)});return await async function(e,t){const{url:a,layerId:r,title:n,fullExtent:o,isTable:i}=e,s=(0,c.Qc)(a),u=(0,l.pC)(s)&&"FeatureServer"===s.serverType;t.url=u?a:`${a}/${r}`,t.title||(t.title=n),t.extent=null,!i&&(0,l.pC)(o)&&(t.extent=await(0,w.$o)(o)),(0,w.ck)(t,w.Kz.METADATA),(0,w.ck)(t,w.Kz.MULTI_LAYER),(0,w.qj)(t,w.Kz.SINGLE_LAYER),i&&(0,w.qj)(t,w.Kz.TABLE),N(t)}(e,r),await async function(e,t,a){const r=e.portal;await(r?.signIn()),await(r?.user?.addItem({item:e,data:t,folder:a?.folder}))}(r,o,a),e.portalItem=r,(0,s.D)(n),r}))},52104:(e,t,a)=>{a.d(t,{V:()=>n});var r=a(96187);async function n(e,t){const a=await(0,r.T)(e,t);a.layers=a.layers.filter(o);const n={serviceJSON:a};if((a.currentVersion??0)<10.5)return n;const l=await(0,r.T)(e+"/layers",t);return n.layersJSON={layers:l.layers.filter(o),tables:l.tables},n}function o(e){return!e.type||"Feature Layer"===e.type}},33516:(e,t,a)=>{a.d(t,{Y:()=>l,h:()=>o});var r=a(17452),n=a(65587);function o(e){return{origin:"portal-item",url:(0,r.mN)(e.itemUrl),portal:e.portal||n.Z.getDefault(),portalItem:e,readResourcePaths:[]}}function l(e){return{origin:"portal-item",messages:[],writtenProperties:[],url:e.itemUrl?(0,r.mN)(e.itemUrl):null,portal:e.portal||n.Z.getDefault(),portalItem:e}}},96187:(e,t,a)=>{a.d(t,{T:()=>n});var r=a(3172);async function n(e,t){const{data:a}=await(0,r.default)(e,{responseType:"json",query:{f:"json",...t?.customParameters,token:t?.apiKey}});return a}}}]);