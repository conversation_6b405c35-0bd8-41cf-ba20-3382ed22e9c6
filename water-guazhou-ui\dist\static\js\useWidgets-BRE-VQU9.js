import{b as c,M as s,m as d,a as S,h as u,c as p}from"./AnimatedLinesLayer-B2VbV4jv.js";import{h as g}from"./GraphicsLayer-DTrBRwJQ.js";import{D as o}from"./TileLayer-B5vQ99gG.js";const y=()=>({initSearch:(t,i)=>{var e;const n=new c({view:t});(e=t.ui)==null||e.add(n,i||"top-right")},initSketch:(t,i)=>{var r;const n=new g;t.map.add(n);const e=new s({view:t,layer:n,creationMode:"update"});(r=t.ui)==null||r.add(e,i||"top-right")},initPrint:(t,i,n)=>{var a;const e=new S({view:t,printServiceUrl:i}),r=new u({view:t,content:e,expandTooltip:"打印"});(a=t.ui)==null||a.add(r,n||"top-right")},initSwipe:(t,i)=>{var e;const n=new p({...i||{},view:t,leadingLayers:i.leadingLayers||[new o({url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService})],trailingLayers:i.tailingLayers||[new o({url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService})],direction:i.direction||"horizontal"});return(e=t.ui)==null||e.add(n),n},addCustomWidget:(t,i,n)=>{i&&(t==null||t.ui.add(i,n||"top-right"))},removeCustomWidget:(t,i)=>{i&&(t==null||t.ui.remove(i))},initScaleBar:(t,i)=>{var e;const n=new d({view:t,unit:"metric",style:"ruler"});(e=t.ui)==null||e.add(n,i||"bottom-left")}});export{y as u};
