<template>
  <div class="water-quality-test">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>化验记录</span>
          <div class="header-buttons">
            <el-button type="primary" @click="handleAdd">新增记录</el-button>
            <el-button type="danger" :disabled="selectedIds.length === 0" @click="handleBatchDelete">批量删除</el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
        <el-form-item label="采样地点" prop="samplingLocation">
          <el-input v-model="queryParams.samplingLocation" placeholder="请输入采样地点" clearable />
        </el-form-item>
        <el-form-item label="报告名称" prop="reportName">
          <el-input v-model="queryParams.reportName" placeholder="请输入报告名称" clearable />
        </el-form-item>
        <el-form-item label="检测单位" prop="testingUnit">
          <el-input v-model="queryParams.testingUnit" placeholder="请输入检测单位" clearable />
        </el-form-item>
        <el-form-item label="检测月份" prop="testDate">
          <el-date-picker
            v-model="queryParams.testDate"
            type="month"
            placeholder="选择月份"
            value-format="x"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="samplingLocation" label="采样地点" min-width="120" />
        <el-table-column prop="reportName" label="报告名称" min-width="120" />
        <el-table-column prop="testingUnit" label="检测单位" min-width="120" />
        <el-table-column prop="testResults" label="检测结果" min-width="120" />
        <el-table-column prop="testDate" label="检测月份" min-width="120">
          <template #default="{ row }">
            {{ row.testDate ? formatDate(Number(row.testDate), 'YYYY-MM') : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="reportFile" label="报告附件" min-width="120">
          <template #default="{ row }">
            <el-button type="text" @click="handleDownload(row)">下载</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="采样地点" prop="samplingLocation">
          <el-input v-model="form.samplingLocation" placeholder="请输入采样地点" />
        </el-form-item>
        <el-form-item label="报告名称" prop="reportName">
          <el-input v-model="form.reportName" placeholder="请输入报告名称" />
        </el-form-item>
        <el-form-item label="检测单位" prop="testingUnit">
          <el-input v-model="form.testingUnit" placeholder="请输入检测单位" />
        </el-form-item>
        <el-form-item label="检测结果" prop="testResults">
          <el-input v-model="form.testResults" placeholder="请输入检测结果" />
        </el-form-item>
        <el-form-item label="检测月份" prop="testDate">
          <el-date-picker
            v-model="form.testDate"
            type="month"
            placeholder="选择月份"
            value-format="x"
          />
        </el-form-item>
        <el-form-item label="化验报告" prop="reportFile">
          <el-upload
            class="upload-demo"
            :http-request="handleUpload"
            :before-upload="beforeUpload"
            :limit="1"
          >
            <el-button type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                请上传化验报告文件
                <span v-if="form.reportFile" style="color: #67C23A;">
                  (已上传)
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { formatDate } from '@/utils/DateFormatter'
import { getWaterQualityTestList, saveWaterQualityTest, updateWaterQualityTest, deleteWaterQualityTest } from '@/api/waterSource/assay'
import { uploadFile } from '@/utils/fileUpload'
import { ElMessage, ElMessageBox } from 'element-plus'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  samplingLocation: '',
  reportName: '',
  testingUnit: '',
  testDate: null,
  type: 2 // 添加 type=2 表示污水厂的化验记录
})

// 表格数据
const tableData = ref([])
const total = ref(0)
const loading = ref(false)

// 选中项管理
const selectedIds = ref<string[]>([])
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 表单数据
const form = reactive({
  id: '',
  samplingLocation: '',
  reportName: '',
  testingUnit: '',
  testResults: '',
  testDate: null,
  reportFile: '',
  remark: '',
  type: 2 // 添加 type=2 表示污水厂的化验记录
})

// 表单校验规则
const rules = {
  samplingLocation: [{ required: true, message: '请输入采样地点', trigger: 'blur' }],
  reportName: [{ required: true, message: '请输入报告名称', trigger: 'blur' }],
  testingUnit: [{ required: true, message: '请输入检测单位', trigger: 'blur' }],
  testResults: [{ required: true, message: '请输入检测结果', trigger: 'blur' }],
  testDate: [{ required: true, message: '请选择检测月份', trigger: 'change' }]
}

// 对话框控制
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    // 处理查询参数，确保参数名称与后端期望的一致
    const params: any = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      samplingLocation: queryParams.samplingLocation,
      reportName: queryParams.reportName,
      testingUnit: queryParams.testingUnit,
      type: 2 // 添加 type=2 表示污水厂的化验记录
    }

    // 处理检测月份参数
    if (queryParams.testDate) {
      params.testDate = queryParams.testDate // 检测月份
    }

    const res = await getWaterQualityTestList(params)
    // 确保数据是数组
    if (res.data && res.data.data && res.data.data.data) {
      tableData.value = Array.isArray(res.data.data.data) ? res.data.data.data : []
      total.value = res.data.data.total || 0
    } else {
      console.warn('返回数据格式不正确:', res)
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  selectedIds.value = [] // 清除选中状态
  getList()
}

// 重置搜索
const resetQuery = () => {
  queryParams.samplingLocation = ''
  queryParams.reportName = ''
  queryParams.testingUnit = ''
  queryParams.testDate = null
  selectedIds.value = [] // 清除选中状态
  handleQuery()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增化验记录'
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogTitle.value = '编辑化验记录'
  Object.assign(form, {
    ...row,
    // 确保 testDate 是数字类型的时间戳
    testDate: row.testDate ? Number(row.testDate) : null
  })
  dialogVisible.value = true
}

// 删除单条记录
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该记录吗？', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      // 将单个ID转换为数组传递给API
      // 注意：由于 API 设计，我们无法直接在删除时传递 type 参数
      // 但后端应该会根据记录 ID 识别记录类型（type=2 表示污水厂的化验记录）
      await deleteWaterQualityTest([row.id])
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  ElMessageBox.confirm(`确认删除选中的 ${selectedIds.value.length} 条记录吗？`, '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      // 注意：由于 API 设计，我们无法直接在删除时传递 type 参数
      // 但后端应该会根据记录 ID 识别记录类型（type=2 表示污水厂的化验记录）
      await deleteWaterQualityTest(selectedIds.value)
      ElMessage.success('批量删除成功')
      selectedIds.value = []
      getList()
    } catch (error) {
      console.error(error)
      ElMessage.error('批量删除失败')
    }
  })
}

// 下载报告
const handleDownload = (row) => {
  if (row.reportFile) {
    window.open(row.reportFile)
  }
}

// 上传前校验
const beforeUpload = (file: File) => {
  // 这里可以添加文件类型和大小的校验
  const isValidSize = file.size / 1024 / 1024 < 10 // 限制文件大小为10MB
  if (!isValidSize) {
    ElMessage.warning('文件大小不能超过10MB')
    return false
  }
  return true
}

// 自定义上传方法
const handleUpload = async (options: any) => {
  const { file } = options

  try {
    // 直接使用通用的文件上传工具函数
    const fileUrl = await uploadFile(file, 'file')
    // 将文件URL保存到表单中
    form.reportFile = fileUrl
    ElMessage.success('文件上传成功')
  } catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败')
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 处理日期格式
        const submitData = {
          ...form,
          // 已经使用 value-format="x" 确保是时间戳格式，这里转换为字符串
          // 确保 testDate 不为 undefined，如果没有值则使用当前时间
          testDate: form.testDate ? String(form.testDate) : String(Date.now()),
          // 确保 reportFile 是字符串类型
          reportFile: typeof form.reportFile === 'string' ? form.reportFile : '',
          // 添加 type=2 表示污水厂的化验记录
          type: 2
        }

        // 检查是否上传了文件
        if (!submitData.reportFile && !form.id) {
          ElMessage.warning('请先上传化验报告文件')
          return
        }

        if (form.id) {
          await updateWaterQualityTest(submitData)
          ElMessage.success('修改成功')
        } else {
          await saveWaterQualityTest(submitData)
          ElMessage.success('新增成功')
        }
        dialogVisible.value = false
        getList()
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  // 清除所有表单数据，包括文件上传状态
  Object.assign(form, {
    id: '',
    samplingLocation: '',
    reportName: '',
    testingUnit: '',
    testResults: '',
    testDate: null,
    reportFile: '',
    remark: '',
    type: 2 // 确保 type=2 表示污水厂的化验记录
  })

  // 重置文件上传组件
  const uploadComponent = document.querySelector('.upload-demo .el-upload__input');
  if (uploadComponent) {
    (uploadComponent as HTMLInputElement).value = '';
  }
}

// 分页
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  selectedIds.value = [] // 清除选中状态
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  selectedIds.value = [] // 清除选中状态
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.water-quality-test {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .search-form {
    margin-bottom: 20px;
  }

  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>