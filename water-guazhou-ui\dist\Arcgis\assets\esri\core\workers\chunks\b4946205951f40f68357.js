"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9238],{64830:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(70586);class s{constructor(e=(e=>e.values().next().value)){this._peeker=e,this._items=new Set}get length(){return this._items.size}clear(){this._items.clear()}last(){if(0===this._items.size)return;let e;for(e of this._items);return e}peek(){if(0!==this._items.size)return this._peeker(this._items)}push(e){this.contains(e)||this._items.add(e)}contains(e){return this._items.has(e)}pop(){if(0===this.length)return;const e=this.peek();return this._items.delete((0,i.j0)(e)),e}popLast(){if(0===this.length)return;const e=this.last();return this._items.delete((0,i.j0)(e)),e}remove(e){this._items.delete(e)}filter(e){return this._items.forEach((t=>{e(t)||this._items.delete(t)})),this}}},80903:(e,t,r)=>{r.d(t,{Z:()=>l});var i=r(50758),s=r(92604),o=r(95330),n=r(64830),a=r(25045);class l{constructor(){this._inUseClients=new Array,this._clients=new Array,this._clientPromises=new Array,this._ongoingJobsQueue=new n.Z}destroy(){this.close()}get closed(){return!this._clients||!this._clients.length}open(e,t){return new Promise(((r,i)=>{let s=!0;const n=e=>{(0,o.k_)(t.signal),s&&(s=!1,e())};this._clients.length=e.length,this._clientPromises.length=e.length,this._inUseClients.length=e.length;for(let s=0;s<e.length;++s){const l=e[s];(0,o.y8)(l)?this._clientPromises[s]=l.then((e=>(this._clients[s]=new a.default(e,t,(()=>this._ongoingJobsQueue.pop()??null)),n(r),this._clients[s])),(()=>(n(i),null))):(this._clients[s]=new a.default(l,t,(()=>this._ongoingJobsQueue.pop()??null)),this._clientPromises[s]=Promise.resolve(this._clients[s]),n(r))}}))}broadcast(e,t,r){const i=new Array(this._clientPromises.length);for(let s=0;s<this._clientPromises.length;++s){const o=this._clientPromises[s];i[s]=o.then((i=>i?.invoke(e,t,r)))}return i}close(){let e;for(;e=this._ongoingJobsQueue.pop();)e.deferred.reject((0,o.zE)(`Worker closing, aborting job calling '${e.methodName}'`));for(const e of this._clientPromises)e.then((e=>e?.close()));this._clients.length=0,this._clientPromises.length=0}invoke(e,t,r){let i;Array.isArray(r)?(s.Z.getLogger("esri.core.workers.Connection").warn("invoke()","The transferList parameter is deprecated, use the options object instead"),i={transferList:r}):i=r;const n=(0,o.dD)();this._ongoingJobsQueue.push({methodName:e,data:t,invokeOptions:i,deferred:n});for(let e=0;e<this._clientPromises.length;e++){const t=this._clients[e];t?t.jobAdded():this._clientPromises[e].then((e=>e?.jobAdded()))}return n.promise}on(e,t){return Promise.all(this._clientPromises).then((()=>(0,i.AL)(this._clients.map((r=>r.on(e,t))))))}openPorts(){return new Promise((e=>{const t=new Array(this._clientPromises.length);let r=t.length;for(let i=0;i<this._clientPromises.length;++i)this._clientPromises[i].then((s=>{s&&(t[i]=s.openPort()),0==--r&&e(t)}))}))}get test(){return{numClients:this._clients.length}}}},78346:(e,t,r)=>{r.d(t,{bA:()=>q});var i=r(20102),s=r(80442),o=r(95330),n=r(80903),a=r(25045),l=r(40330),u=r(92604),d=r(70586),c=r(94362),p=r(99880),h=r(68773),y=(r(2587),r(17452));const f={};function m(e){const t={async:e.async,isDebug:e.isDebug,locale:e.locale,baseUrl:e.baseUrl,has:{...e.has},map:{...e.map},packages:e.packages&&e.packages.concat()||[],paths:{...e.paths}};return e.hasOwnProperty("async")||(t.async=!0),e.hasOwnProperty("isDebug")||(t.isDebug=!1),e.baseUrl||(t.baseUrl=f.baseUrl),t}var g=r(41213);class b{constructor(){const e=document.createDocumentFragment();["addEventListener","dispatchEvent","removeEventListener"].forEach((t=>{this[t]=(...r)=>e[t](...r)}))}}class w{constructor(){this._dispatcher=new b,this._workerPostMessage({type:c.Cs.HANDSHAKE})}terminate(){}get onmessage(){return this._onmessageHandler}set onmessage(e){this._onmessageHandler&&this.removeEventListener("message",this._onmessageHandler),this._onmessageHandler=e,e&&this.addEventListener("message",e)}get onmessageerror(){return this._onmessageerrorHandler}set onmessageerror(e){this._onmessageerrorHandler&&this.removeEventListener("messageerror",this._onmessageerrorHandler),this._onmessageerrorHandler=e,e&&this.addEventListener("messageerror",e)}get onerror(){return this._onerrorHandler}set onerror(e){this._onerrorHandler&&this.removeEventListener("error",this._onerrorHandler),this._onerrorHandler=e,e&&this.addEventListener("error",e)}postMessage(e){(0,g.Y)((()=>{this._workerMessageHandler(new MessageEvent("message",{data:e}))}))}dispatchEvent(e){return this._dispatcher.dispatchEvent(e)}addEventListener(e,t,r){this._dispatcher.addEventListener(e,t,r)}removeEventListener(e,t,r){this._dispatcher.removeEventListener(e,t,r)}_workerPostMessage(e){(0,g.Y)((()=>{this.dispatchEvent(new MessageEvent("message",{data:e}))}))}async _workerMessageHandler(e){const t=(0,c.QM)(e);if(t&&t.type===c.Cs.OPEN){const{modulePath:e,jobId:r}=t;let i=await a.default.loadWorker(e);i||(i=await import(e));const s=a.default.connect(i);this._workerPostMessage({type:c.Cs.OPENED,jobId:r,data:s})}}}var _=r(70171),v=r(17202);const C=u.Z.getLogger("esri.core.workers.workerFactory"),{HANDSHAKE:S}=c.Cs;let F,I;const E="Failed to create Worker. Fallback to execute module in main thread";async function k(e){return new Promise((t=>{function r(s){const o=(0,c.QM)(s);o&&o.type===S&&(e.removeEventListener("message",r),e.removeEventListener("error",i),t(e))}function i(t){t.preventDefault(),e.removeEventListener("message",r),e.removeEventListener("error",i),C.warn("Failed to create Worker. Fallback to execute module in main thread",t),(e=new w).addEventListener("message",r),e.addEventListener("error",i)}e.addEventListener("message",r),e.addEventListener("error",i)}))}function j(){let e;if(null!=h.Z.default){const t={...h.Z};delete t.default,e=JSON.parse(JSON.stringify(t))}else e=JSON.parse(JSON.stringify(h.Z));e.assetsPath=(0,y.hF)(e.assetsPath),e.defaultAssetsPath=e.defaultAssetsPath?(0,y.hF)(e.defaultAssetsPath):void 0,e.request.interceptors=[],e.log.interceptors=[],e.locale=(0,_.Kd)(),e.has={"esri-csp-restrictions":(0,s.Z)("esri-csp-restrictions"),"esri-2d-debug":!1,"esri-2d-update-debug":(0,s.Z)("esri-2d-update-debug"),"featurelayer-pbf":(0,s.Z)("featurelayer-pbf"),"featurelayer-simplify-thresholds":(0,s.Z)("featurelayer-simplify-thresholds"),"featurelayer-simplify-payload-size-factors":(0,s.Z)("featurelayer-simplify-payload-size-factors"),"featurelayer-simplify-mobile-factor":(0,s.Z)("featurelayer-simplify-mobile-factor"),"esri-atomics":(0,s.Z)("esri-atomics"),"esri-shared-array-buffer":(0,s.Z)("esri-shared-array-buffer"),"esri-tiles-debug":(0,s.Z)("esri-tiles-debug"),"esri-workers-arraybuffer-transfer":(0,s.Z)("esri-workers-arraybuffer-transfer"),"feature-polyline-generalization-factor":(0,s.Z)("feature-polyline-generalization-factor"),"host-webworker":1,"polylabel-placement-enabled":(0,s.Z)("polylabel-placement-enabled")},e.workers.loaderUrl&&(e.workers.loaderUrl=(0,y.hF)(e.workers.loaderUrl)),e.workers.workerPath?e.workers.workerPath=(0,y.hF)(e.workers.workerPath):e.workers.workerPath=(0,y.hF)((0,p.V)("esri/core/workers/RemoteClient.js")),e.workers.useDynamicImport=!1;const t=h.Z.workers.loaderConfig,r=m({baseUrl:t?.baseUrl,locale:(0,_.Kd)(),has:{"csp-restrictions":1,"dojo-test-sniff":0,"host-webworker":1,...t?.has},map:{...t?.map},paths:{...t?.paths},packages:t?.packages||[]}),i={version:l.i8,buildDate:v.r,revision:v.$};return JSON.stringify({esriConfig:e,loaderConfig:r,kernelInfo:i})}let T=0;const{ABORT:P,INVOKE:R,OPEN:O,OPENED:M,RESPONSE:Z}=c.Cs;class D{static async create(e){const t=await async function(){if(!(0,s.Z)("esri-workers")||((0,s.Z)("mozilla"),0))return k(new w);if(!F&&!I)try{const e='let globalId=0;const outgoing=new Map,configuration=JSON.parse("{CONFIGURATION}");self.esriConfig=configuration.esriConfig;const workerPath=self.esriConfig.workers.workerPath,HANDSHAKE=0,OPEN=1,OPENED=2,RESPONSE=3,INVOKE=4,ABORT=5;function createAbortError(){const e=new Error("Aborted");return e.name="AbortError",e}function receiveMessage(e){return e&&e.data?"string"==typeof e.data?JSON.parse(e.data):e.data:null}function invokeStaticMessage(e,o,r){const t=r&&r.signal,n=globalId++;return new Promise(((r,i)=>{if(t){if(t.aborted)return i(createAbortError());t.addEventListener("abort",(()=>{outgoing.get(n)&&(outgoing.delete(n),self.postMessage({type:5,jobId:n}),i(createAbortError()))}))}outgoing.set(n,{resolve:r,reject:i}),self.postMessage({type:4,jobId:n,methodName:e,abortable:null!=t,data:o})}))}let workerRevisionChecked=!1;function checkWorkerRevision(e){if(!workerRevisionChecked&&e.kernelInfo){workerRevisionChecked=!0;const{revision:o,version:r}=configuration.kernelInfo,{revision:t,version:n}=e.kernelInfo;esriConfig.assetsPath!==esriConfig.defaultAssetsPath&&o!==t&&console.warn(`Version mismatch detected between ArcGIS API for JavaScript modules and assets. For more information visit https://bit.ly/3QnsuSo.\\nModules version: ${r}\\nAssets version: ${n}`)}}function messageHandler(e){const o=receiveMessage(e);if(!o)return;const r=o.jobId;switch(o.type){case 1:let n;function t(e){const o=n.connect(e);self.postMessage({type:2,jobId:r,data:o},[o])}"function"==typeof define&&define.amd?require([workerPath],(e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||new Promise((e=>{require([o.modulePath],e)})))).then(t)})):"System"in self&&"function"==typeof System.import?System.import(workerPath).then((e=>(n=e.default,checkWorkerRevision(n),n.loadWorker(o.modulePath)))).then((e=>e||System.import(o.modulePath))).then(t):esriConfig.workers.useDynamicImport?import(workerPath).then((e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||import(o.modulePath))).then(t)})):(self.RemoteClient||importScripts(workerPath),n=self.RemoteClient.default||self.RemoteClient,checkWorkerRevision(n),n.loadWorker(o.modulePath).then(t));break;case 3:if(outgoing.has(r)){const i=outgoing.get(r);outgoing.delete(r),o.error?i.reject(JSON.parse(o.error)):i.resolve(o.data)}}}self.dojoConfig=configuration.loaderConfig,esriConfig.workers.loaderUrl&&(self.importScripts(esriConfig.workers.loaderUrl),"function"==typeof require&&"function"==typeof require.config&&require.config(configuration.loaderConfig)),self.addEventListener("message",messageHandler),self.postMessage({type:0});'.split('"{CONFIGURATION}"').join(`'${j()}'`);F=URL.createObjectURL(new Blob([e],{type:"text/javascript"}))}catch(e){I=e||{}}let e;if(F)try{e=new Worker(F,{name:"esri-worker-"+T++})}catch(t){C.warn(E,I),e=new w}else C.warn(E,I),e=new w;return k(e)}();return new D(t,e)}constructor(e,t){this._outJobs=new Map,this._inJobs=new Map,this.worker=e,this.id=t,e.addEventListener("message",this._onMessage.bind(this)),e.addEventListener("error",(e=>{e.preventDefault(),u.Z.getLogger("esri.core.workers.WorkerOwner").error(e)}))}terminate(){this.worker.terminate()}async open(e,t={}){const{signal:r}=t,i=(0,c.jt)();return new Promise(((t,s)=>{const n={resolve:t,reject:s,abortHandle:(0,o.$F)(r,(()=>{this._outJobs.delete(i),this._post({type:P,jobId:i})}))};this._outJobs.set(i,n),this._post({type:O,jobId:i,modulePath:e})}))}_onMessage(e){const t=(0,c.QM)(e);if(t)switch(t.type){case M:this._onOpenedMessage(t);break;case Z:this._onResponseMessage(t);break;case P:this._onAbortMessage(t);break;case R:this._onInvokeMessage(t)}}_onAbortMessage(e){const t=this._inJobs,r=e.jobId,i=t.get(r);i&&(i.controller&&i.controller.abort(),t.delete(r))}_onInvokeMessage(e){const{methodName:t,jobId:r,data:i,abortable:s}=e,n=s?new AbortController:null,a=this._inJobs,u=l.Nv[t];let d;try{if("function"!=typeof u)throw new TypeError(`${t} is not a function`);d=u.call(null,i,{signal:n?n.signal:null})}catch(e){return void this._post({type:Z,jobId:r,error:(0,c.AB)(e)})}(0,o.y8)(d)?(a.set(r,{controller:n,promise:d}),d.then((e=>{a.has(r)&&(a.delete(r),this._post({type:Z,jobId:r},e))}),(e=>{a.has(r)&&(a.delete(r),e||(e={message:"Error encountered at method"+t}),(0,o.D_)(e)||this._post({type:Z,jobId:r,error:(0,c.AB)(e||{message:`Error encountered at method ${t}`})}))}))):this._post({type:Z,jobId:r},d)}_onOpenedMessage(e){const{jobId:t,data:r}=e,i=this._outJobs.get(t);i&&(this._outJobs.delete(t),(0,d.hw)(i.abortHandle),i.resolve(r))}_onResponseMessage(e){const{jobId:t,error:r,data:s}=e,o=this._outJobs.get(t);o&&(this._outJobs.delete(t),(0,d.hw)(o.abortHandle),r?o.reject(i.Z.fromJSON(JSON.parse(r))):o.resolve(s))}_post(e,t,r){return(0,c.oi)(this.worker,e,t,r)}}let L=(0,s.Z)("esri-workers-debug")?1:(0,s.Z)("esri-mobile")?Math.min(navigator.hardwareConcurrency-1,3):(0,s.Z)("host-browser")?navigator.hardwareConcurrency-1:0;L||(L=(0,s.Z)("safari")&&(0,s.Z)("mac")?7:2);let x=0;const N=[];async function A(e,t){const r=new n.Z;return await r.open(e,t),r}async function q(e,t={}){if("string"!=typeof e)throw new i.Z("workers:undefined-module","modulePath is missing");let r=t.strategy||"distributed";if((0,s.Z)("host-webworker")&&!(0,s.Z)("esri-workers")&&(r="local"),"local"===r){let r=await a.default.loadWorker(e);r||(r=await import(e)),(0,o.k_)(t.signal);const i=t.client||r;return A([a.default.connect(r)],{...t,client:i})}if(await async function(){if(G)return G;J=new AbortController;const e=[];for(let t=0;t<L;t++){const r=D.create(t).then((e=>(N[t]=e,e)));e.push(r)}return G=Promise.all(e),G}(),(0,o.k_)(t.signal),"dedicated"===r){const r=x++%L;return A([await N[r].open(e,t)],t)}if(t.maxNumWorkers&&t.maxNumWorkers>0){const r=Math.min(t.maxNumWorkers,L);if(r<L){const i=new Array(r);for(let s=0;s<r;++s){const r=x++%L;i[s]=N[r].open(e,t)}return A(i,t)}}return A(N.map((r=>r.open(e,t))),t)}let J,G=null},2587:(e,t,r)=>{r(90344),r(18848),r(940),r(70171);var i=r(94443),s=r(3172),o=r(20102),n=r(70586);async function a(e){if((0,n.pC)(l.fetchBundleAsset))return l.fetchBundleAsset(e);const t=await(0,s.default)(e,{responseType:"text"});return JSON.parse(t.data)}const l={};var u,d=r(99880);(0,i.tz)((u={pattern:"esri/",location:d.V},new class{constructor({base:e="",pattern:t,location:r=new URL(window.location.href)}){let i;i="string"==typeof r?e=>new URL(e,new URL(r,window.location.href)).href:r instanceof URL?e=>new URL(e,r).href:r,this.pattern="string"==typeof t?new RegExp(`^${t}`):t,this.getAssetUrl=i,e=e?e.endsWith("/")?e:e+"/":"",this.matcher=new RegExp(`^${e}(?:(.*)/)?(.*)$`)}fetchMessageBundle(e,t){return async function(e,t,r,s){const n=t.exec(r);if(!n)throw new o.Z("esri-intl:invalid-bundle",`Bundle id "${r}" is not compatible with the pattern "${t}"`);const l=n[1]?`${n[1]}/`:"",u=n[2],d=(0,i.Su)(s),c=`${l}${u}.json`,p=d?`${l}${u}_${d}.json`:c;let h;try{h=await a(e(p))}catch(t){if(p===c)throw new o.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:t});try{h=await a(e(c))}catch(e){throw new o.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:e})}}return h}(this.getAssetUrl,this.matcher,e,t)}}(u)))},940:(e,t,r)=>{r.d(t,{n:()=>u});var i=r(92604),s=r(78286),o=r(19153),n=r(90344),a=r(18848);const l=i.Z.getLogger("esri.intl.substitute");function u(e,t,r={}){const{format:i={}}=r;return(0,o.gx)(e,(e=>function(e,t,r){let i,o;const n=e.indexOf(":");if(-1===n?i=e.trim():(i=e.slice(0,n).trim(),o=e.slice(n+1).trim()),!i)return"";const a=(0,s.hS)(i,t);if(null==a)return"";const l=(o?r?.[o]:null)??r?.[i];return l?d(a,l):o?c(a,o):p(a)}(e,t,i)))}function d(e,t){switch(t.type){case"date":return(0,n.p6)(e,t.intlOptions);case"number":return(0,a.uf)(e,t.intlOptions);default:return l.warn("missing format descriptor for key {key}"),p(e)}}function c(e,t){switch(t.toLowerCase()){case"dateformat":return(0,n.p6)(e);case"numberformat":return(0,a.uf)(e);default:return l.warn(`inline format is unsupported since 4.12: ${t}`),/^(dateformat|datestring)/i.test(t)?(0,n.p6)(e):/^numberformat/i.test(t)?(0,a.uf)(e):p(e)}}function p(e){switch(typeof e){case"string":return e;case"number":return(0,a.uf)(e);case"boolean":return""+e;default:return e instanceof Date?(0,n.p6)(e):""}}},19238:(e,t,r)=>{r.r(t),r.d(t,{default:()=>Be});var i=r(43697),s=r(51773),o=(r(16050),r(12501),r(28756),r(92271),r(72529),r(5499),r(84382)),n=r(81571),a=r(91423),l=r(32400),u=r(3172),d=r(9790),c=r(2368),p=r(46791),h=r(20102),y=r(61247),f=r(22974),m=r(92604),g=r(70586),b=r(16453),w=r(78286),_=r(95330),v=r(42033),C=r(17452),S=r(5600),F=r(90578),I=r(71715),E=r(52011),k=r(30556),j=r(63213),T=r(55343),P=r(82971),R=r(87085),O=(r(66577),r(38171)),M=r(3920),Z=r(80442),D=r(83379),L=r(609),x=r(78346),N=r(75215),A=(r(67676),r(52421)),q=r(59431),J=r(28694),G=r(74889),B=r(6570),U=r(38913),W=r(86973);let $=0;const H="esri.layers.graphics.sources.MemorySource",Q=m.Z.getLogger(H);let V=class extends(D.Z.LoadableMixin((0,L.v)((0,M.p)(p.Z)))){constructor(e){super(e),this._idToClientGraphic=null,this.type="memory"}load(e){const t=(0,g.pC)(e)?e.signal:null;return this.addResolvingPromise(this._startWorker(t)),Promise.resolve(this)}destroy(){this._connection?.close(),this._connection=null}get _workerGeometryType(){const e=this.layer?.geometryType;return e?this._geometryTypeRequiresClientGraphicMapping(e)?"polygon":e:null}applyEdits(e){return this.load().then((()=>this._applyEdits(e)))}openPorts(){return this.load().then((()=>this._connection.openPorts()))}async queryFeatures(e,t={}){await this.load(t);const r=await this._connection.invoke("queryFeatures",e?e.toJSON():null,t);(0,J.p)(e,this.layer.spatialReference,r);const i=G.Z.fromJSON(r);if(!this._requiresClientGraphicMapping())return i;const s=this.layer.objectIdField;for(const e of i.features){const t=e.attributes[s],r=this._idToClientGraphic.get(t);r&&(e.geometry=r.geometry)}return i.geometryType=this.layer.geometryType,i}async queryFeaturesJSON(e,t={}){if(this._requiresClientGraphicMapping())throw new h.Z("query-features-json:unsupported","Cannot query in JSON format for client only geometry types (mesh and extent)");await this.load(t);const r=await this._connection.invoke("queryFeatures",e?e.toJSON():null,t);return(0,J.p)(e,this.layer.spatialReference,r),r}queryFeatureCount(e,t={}){return this.load(t).then((()=>this._connection.invoke("queryFeatureCount",e?e.toJSON():null,t)))}queryObjectIds(e,t={}){return this.load(t).then((()=>this._connection.invoke("queryObjectIds",e?e.toJSON():null,t)))}queryExtent(e,t={}){return this.load(t).then((()=>this._connection.invoke("queryExtent",e?e.toJSON():null,t))).then((e=>({count:e.count,extent:B.Z.fromJSON(e.extent)})))}querySnapping(e,t={}){return this.load(t).then((()=>this._connection.invoke("querySnapping",e,t)))}async _applyEdits(e){if(!this._connection)throw new h.Z("feature-layer-source:edit-failure","Memory source not loaded");const t=this.layer.objectIdField;let r=null;const i=[],s=[];await Promise.all([this._prepareClientMapping(e.addFeatures,null),this._prepareClientMapping(e.updateFeatures,null)]);const o=e=>"objectId"in e&&null!=e.objectId?e.objectId:"attributes"in e&&null!=e.attributes[t]?e.attributes[t]:null;if(e.addFeatures&&(r=this._prepareAddFeatures(e.addFeatures)),e.deleteFeatures)for(const t of e.deleteFeatures){const e=o(t);null!=e&&i.push(e)}const n=e.updateFeatures&&this._idToClientGraphic?new Map:null;if(e.updateFeatures)for(const t of e.updateFeatures)if(s.push(this._serializeFeature(t)),n){const e=o(t);null!=e&&n.set(e,t)}(0,q.P)(r?r.features:null,s,this.layer.spatialReference);const{fullExtent:a,featureEditResults:l}=await this._connection.invoke("applyEdits",{adds:r?r.features:[],updates:s,deletes:i});return this.fullExtent=a,r&&r.finish(l.uidToObjectId),this._updateClientGraphicIds(n,l),this._createEditsResult(l)}async _prepareClientMapping(e,t){if("mesh"!==this._layerOrSourceGeometryType||(0,g.Wi)(e))return;const r=[];for(const{geometry:i}of e)!(0,g.pC)(i)||"mesh"!==i.type||i.hasExtent||i.loaded||r.push(i.load({signal:t}));r.length&&await Promise.all(r)}_updateClientGraphicIds(e,t){if(this._idToClientGraphic){if(e)for(const r of t.updateResults){if(!r.success)continue;const t=e.get(r.objectId);null!=t&&this._addIdToClientGraphic(t)}for(const e of t.deleteResults)e.success&&this._idToClientGraphic.delete(e.objectId)}}_createEditsResult(e){return{addFeatureResults:e.addResults?e.addResults.map(this._createFeatureEditResult,this):[],updateFeatureResults:e.updateResults?e.updateResults.map(this._createFeatureEditResult,this):[],deleteFeatureResults:e.deleteResults?e.deleteResults.map(this._createFeatureEditResult,this):[],addAttachmentResults:[],updateAttachmentResults:[],deleteAttachmentResults:[]}}_createFeatureEditResult(e){const t=!0===e.success?null:e.error||{code:void 0,description:void 0};return{objectId:e.objectId,globalId:e.globalId,error:t?new h.Z("feature-layer-source:edit-failure",t.description,{code:t.code}):null}}_prepareAddFeatures(e){const t=new Map,r=new Array(e.length);let i=null;for(let s=0;s<e.length;s++){const o=e[s],n=this._serializeFeature(o);!i&&(0,g.pC)(o.geometry)&&(i=o.geometry.type),r[s]=n,t.set(`${n.uid}`,o)}const s=this;return{features:r,inferredGeometryType:i,finish(e){const r=s.sourceJSON.objectIdField;for(const i in e){const o=e[i],n=t.get(i);n&&(n.attributes||(n.attributes={}),-1===o?delete n.attributes[r]:n.attributes[r]=o,s._addIdToClientGraphic(n))}}}}_addIdToClientGraphic(e){if(!this._idToClientGraphic)return;const t=this.sourceJSON.objectIdField,r=e.attributes&&e.attributes[t];null!=r&&this._idToClientGraphic.set(r,e)}get _layerOrSourceGeometryType(){return this.layer?.geometryType??this.sourceJSON?.geometryType}_requiresClientGraphicMapping(){return this._geometryTypeRequiresClientGraphicMapping(this._layerOrSourceGeometryType)}_geometryRequiresClientGraphicMapping(e){return this._geometryTypeRequiresClientGraphicMapping(e.type)}_geometryTypeRequiresClientGraphicMapping(e){return"mesh"===e||"multipatch"===e||"extent"===e}_serializeFeature(e){const{attributes:t}=e,r=this._geometryForSerialization(e),i=($++).toString();return r?{uid:i,geometry:r.toJSON(),attributes:t}:{uid:i,attributes:t}}_geometryForSerialization(e){const{geometry:t}=e;return(0,g.Wi)(t)?null:this._geometryRequiresClientGraphicMapping(t)?t.extent?U.Z.fromExtent(t.extent):null:t}async _startWorker(e){this._connection=await(0,x.bA)("MemorySourceWorker",{strategy:(0,Z.Z)("feature-layers-workers")?"dedicated":"local",signal:e});const{fields:t,spatialReference:r,objectIdField:i,hasM:s,hasZ:o,timeInfo:n}=this.layer,a="defaults"===this.layer.originOf("spatialReference");await this._prepareClientMapping(this.items,e);const l=this._prepareAddFeatures(this.items);this.handles.add(this.on("before-changes",(e=>{Q.error("Source modifications will not propagate after layer has been loaded. Please use .applyEdits() instead"),e.preventDefault()})));const u={features:l.features,fields:t&&t.map((e=>e.toJSON())),geometryType:W.P.toJSON(this._workerGeometryType),hasM:"mesh"!==this._layerOrSourceGeometryType&&s,hasZ:"mesh"===this._layerOrSourceGeometryType||o,objectIdField:i,spatialReference:a?null:r&&r.toJSON(),timeInfo:n?n.toJSON():null},d=await this._connection.invoke("load",u,{signal:e});for(const e of d.warnings)Q.warn(e.message,{layer:this.layer,warning:e});d.featureErrors.length&&Q.warn(`Encountered ${d.featureErrors.length} validation errors while loading features`,d.featureErrors);const c=d.layerDefinition;this._geometryTypeRequiresClientGraphicMapping(l.inferredGeometryType)&&(c.geometryType=W.P.toJSON(l.inferredGeometryType)),this.sourceJSON=c,this._requiresClientGraphicMapping()&&(this._idToClientGraphic=new Map),l.finish(d.assignedObjectIds)}};(0,i._)([(0,A.c)({Type:O.Z,ensureType:(0,N.se)(O.Z)})],V.prototype,"itemType",void 0),(0,i._)([(0,S.Cb)()],V.prototype,"type",void 0),(0,i._)([(0,S.Cb)({constructOnly:!0})],V.prototype,"layer",void 0),(0,i._)([(0,S.Cb)({readOnly:!0})],V.prototype,"_workerGeometryType",null),(0,i._)([(0,S.Cb)()],V.prototype,"sourceJSON",void 0),V=(0,i._)([(0,E.j)(H)],V);var z=r(54295),K=r(17287),Y=r(71612),X=r(17017),ee=r(66361),te=r(69637),re=r(53713),ie=r(6404),se=r(38009),oe=r(68825),ne=r(16859),ae=r(15923);let le=class extends ae.Z{constructor(){super(...arguments),this.updating=!1,this.status="unknown"}};(0,i._)([(0,S.Cb)()],le.prototype,"updating",void 0),(0,i._)([(0,S.Cb)()],le.prototype,"status",void 0),le=(0,i._)([(0,E.j)("esri.layers.support.PublishingInfo")],le);const ue=le,de="esri.layers.mixins.PublishableLayer",ce=Symbol(de),pe=e=>{var t;let r=class extends e{constructor(){super(...arguments),this[t]=!0}get publishingInfo(){if(this.destroyed)return null;const e=this._get("publishingInfo");if(e)return e;const t=new ue;return this._checkPublishingStatus(t),t}_checkPublishingStatus(e){let t=0;const r=async s=>{let o;e.updating=!0;try{o=await this.fetchPublishingStatus()}catch(e){o="unavailable"}"published"!==o&&"unavailable"!==o||("publishing"===e.status&&this.refresh(),i.remove()),e.status=o,e.updating=!1,i.removed||(t=setTimeout(r,s,s+125))},i={removed:!1,remove(){this.removed=!0,clearTimeout(t)}};this.when().catch((()=>i.remove())),r(250),this.own(i)}};return t=ce,(0,i._)([(0,S.Cb)({readOnly:!0,clonable:!1})],r.prototype,"publishingInfo",null),r=(0,i._)([(0,E.j)(de)],r),r};var he=r(34760),ye=r(72965),fe=r(28294),me=r(66677),ge=r(21506),be=r(50957),we=r(70082),_e=r(16451),ve=r(53518),Ce=r(35671),Se=r(54306),Fe=r(30707),Ie=r(72064),Ee=r(76259),ke=r(60199),je=r(10158),Te=r(40555),Pe=r(14165),Re=r(28141),Oe=r(32163),Me=r(65242);const Ze="FeatureLayer",De="esri.layers.FeatureLayer",Le=m.Z.getLogger(De);function xe(e,t){return new h.Z("layer:unsupported",`Layer (${e.title}, ${e.id}) of type '${e.declaredClass}' ${t}`,{layer:e})}function Ne(e){return e&&e instanceof p.Z}const Ae=(0,ve.v)();function qe(e,t,r){const i=!!r?.writeLayerSchema;return{enabled:i,ignoreOrigin:i}}let Je=class extends((0,re.B)((0,ie.M)((0,te.b)(pe((0,ee.o1)((0,Y.h)((0,oe.c)((0,fe.n)((0,ye.M)((0,he.Q)((0,K.Y)((0,se.q)((0,ne.I)((0,b.R)((0,X.N)((0,z.V)((0,c.J)(R.Z)))))))))))))))))){constructor(...e){super(...e),this._handles=new y.Z,this.charts=null,this.copyright=null,this.displayField=null,this.dynamicDataSource=null,this.fields=null,this.fieldsIndex=null,this.formTemplate=null,this.fullExtent=null,this.geometryType=null,this.hasM=void 0,this.hasZ=void 0,this.infoFor3D=null,this.isTable=!1,this.labelsVisible=!0,this.labelingInfo=null,this.legendEnabled=!0,this.objectIdField=null,this.outFields=null,this.path=null,this.popupEnabled=!0,this.popupTemplate=null,this.screenSizePerspectiveEnabled=!0,this.spatialReference=P.Z.WGS84,this.subtypeCode=null,this.templates=null,this.timeInfo=null,this.title=null,this.sublayerTitleMode="item-title",this.type="feature",this.typeIdField=null,this.types=null,this.visible=!0}destroy(){this.source?.destroy(),this._handles=(0,g.SC)(this._handles)}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}load(e){const t=(0,g.pC)(e)?e.signal:null;if(this.portalItem?.loaded&&this.source)return this.addResolvingPromise(this.createGraphicsSource(t).then((e=>this.initLayerProperties(e)))),Promise.resolve(this);const r=this.loadFromPortal({supportedTypes:["Feature Service","Feature Collection"]},e).catch(_.r9).then((async()=>{if(this.url&&null==this.layerId&&/FeatureServer|MapServer\/*$/i.test(this.url)){const e=await this._fetchFirstLayerId(t);null!=e&&(this.layerId=e)}if(!this.url&&!this._hasMemorySource())throw new h.Z("feature-layer:missing-url-or-source","Feature layer must be created with either a url or a source");return this.initLayerProperties(await this.createGraphicsSource(t))})).then((()=>this._setUserPrivileges(this.serviceItemId,e))).then((()=>(0,be.nU)(this,e)));return this.addResolvingPromise(r),Promise.resolve(this)}readCapabilities(e,t){return t=t.layerDefinition||t,(0,Ie.h)(t,this.url)}get createQueryVersion(){return this.commitProperty("definitionExpression"),this.commitProperty("dynamicDataSource"),this.commitProperty("timeExtent"),this.commitProperty("timeOffset"),this.commitProperty("geometryType"),this.commitProperty("gdbVersion"),this.commitProperty("historicMoment"),this.commitProperty("returnZ"),this.commitProperty("capabilities"),this.commitProperty("returnM"),(this._get("createQueryVersion")??0)+1}get editingEnabled(){return!(this.loaded&&!this.capabilities?.operations.supportsEditing)&&(this._isOverridden("editingEnabled")?this._get("editingEnabled"):this._hasMemorySource()||this.userHasEditingPrivileges)}set editingEnabled(e){this._overrideIfSome("editingEnabled",e)}readEditingEnabled(e,t){return this._readEditingEnabled(t,!1)}readEditingEnabledFromWebMap(e,t,r){return this._readEditingEnabled(t,!0,r)}writeEditingEnabled(e,t){this._writeEditingEnabled(e,t,!1)}writeEditingEnabledToWebMap(e,t,r,i){this._writeEditingEnabled(e,t,!0,i)}get effectiveEditingEnabled(){return(0,be.sX)(this)}readIsTable(e,t){return"Table"===(t=t?.layerDefinition??t).type||!t.geometryType}writeIsTable(e,t,r,i){i?.writeLayerSchema&&(0,w.RB)(r,e?"Table":"Feature Layer",t)}readGlobalIdField(e,t){return(0,be.rk)(t.layerDefinition||t)}readObjectIdField(e,t){return(0,be.kZ)(t.layerDefinition||t)}get parsedUrl(){const e=(0,C.mN)(this.url);return null!=e&&(null!=this.dynamicDataSource?e.path=(0,C.v_)(e.path,"dynamicLayer"):null!=this.layerId&&(e.path=(0,C.v_)(e.path,this.layerId.toString()))),e}get defaultPopupTemplate(){return this.createPopupTemplate()}set renderer(e){(0,Ce.YN)(e,this.fieldsIndex),this._set("renderer",e)}readRenderer(e,t,r){const i=(t=t.layerDefinition||t).drawingInfo?.renderer;if(i){const e=(0,a.a)(i,t,r)??void 0;return e||Le.error("Failed to create renderer",{rendererDefinition:t.drawingInfo.renderer,layer:this,context:r}),e}if(t.defaultSymbol)return t.types&&t.types.length?new n.Z({defaultSymbol:Ge(t.defaultSymbol,t,r),field:t.typeIdField,uniqueValueInfos:t.types.map((e=>({id:e.id,symbol:Ge(e.symbol,e,r)})))}):new o.Z({symbol:Ge(t.defaultSymbol,t,r)})}set source(e){const t=this._get("source");t!==e&&(Ne(t)&&this._resetMemorySource(t),Ne(e)&&this._initMemorySource(e),this._set("source",e))}castSource(e){return e?Array.isArray(e)||e instanceof p.Z?new V({layer:this,items:e}):e:null}readSource(e,t){const r=G.Z.fromJSON(t.featureSet);return new V({layer:this,items:r?.features??[]})}readTemplates(e,t){const r=t.editFieldsInfo,i=r&&r.creatorField,s=r&&r.editorField;return e=e&&e.map((e=>we.Z.fromJSON(e))),this._fixTemplates(e,i),this._fixTemplates(e,s),e}readTitle(e,t){const r=t.layerDefinition?.name??t.name,i=t.title||t.layerDefinition&&t.layerDefinition.title;if(r){const e=this.portalItem&&this.portalItem.title;if("item-title"===this.sublayerTitleMode)return this.url?(0,me.a7)(this.url,r):r;let t=r;if(!t&&this.url){const e=(0,me.Qc)(this.url);(0,g.pC)(e)&&(t=e.title)}if(!t)return;return"item-title-and-service-name"===this.sublayerTitleMode&&e&&e!==t&&(t=e+" - "+t),(0,me.ld)(t)}if("item-title"===this.sublayerTitleMode&&i)return i}readTitleFromWebMap(e,t){return t.title||t.layerDefinition&&t.layerDefinition.name}readTypeIdField(e,t){let r=(t=t.layerDefinition||t).typeIdField;if(r&&t.fields){r=r.toLowerCase();const e=t.fields.find((e=>e.name.toLowerCase()===r));e&&(r=e.name)}return r}readTypes(e,t){e=(t=t.layerDefinition||t).types;const r=t.editFieldsInfo,i=r&&r.creatorField,s=r&&r.editorField;return e&&e.map((e=>(e=_e.Z.fromJSON(e),this._fixTemplates(e.templates,i),this._fixTemplates(e.templates,s),e)))}readVisible(e,t){return t.layerDefinition&&null!=t.layerDefinition.defaultVisibility?!!t.layerDefinition.defaultVisibility:null!=t.visibility?!!t.visibility:void 0}async addAttachment(e,t){return(0,be.JD)(this,e,t,Ze)}async updateAttachment(e,t,r){return(0,be.Y5)(this,e,t,r,Ze)}async applyEdits(e,t){return(0,be.Jj)(this,e,t)}on(e,t){return super.on(e,t)}createPopupTemplate(e){return(0,Oe.eZ)(this,e)}async createGraphicsSource(e){if(this._hasMemorySource()&&this.source)return this.source.load({signal:e});const{default:t}=await(0,_.Hl)(Promise.all([r.e(4599),r.e(6502)]).then(r.bind(r,6502)),e);return new t({layer:this}).load({signal:e})}createQuery(){const e=(0,be.rP)(this);e.dynamicDataSource=this.dynamicDataSource;const t=(0,g.pC)(this.subtypeCode)?`${this.subtypeField} = ${this.subtypeCode}`:null,r=(0,v._)(this.definitionExpression,t);return e.where=r||"1=1",e}async deleteAttachments(e,t){return(0,be.FV)(this,e,t,Ze)}async fetchRecomputedExtents(e){return(0,be.Ci)(this,e,Ze)}getFeatureType(e){const{typeIdField:t,types:r}=this;if(!t||!e)return null;const i=e.attributes?e.attributes[t]:void 0;if(null==i)return null;let s=null;return r?.some((e=>{const{id:t}=e;return null!=t&&(t.toString()===i.toString()&&(s=e),!!s)})),s}getFieldDomain(e,t){const r=t&&t.feature,i=this.getFeatureType(r);if(i){const t=i.domains&&i.domains[e];if(t&&"inherited"!==t.type)return t}return this._getLayerDomain(e)}getField(e){return this.fieldsIndex.get(e)}async queryAttachments(e,t){return(0,be.SU)(this,e,t,Ze)}async queryFeatures(e,t){const r=await this.load(),i=await r.source.queryFeatures(Pe.Z.from(e)??r.createQuery(),t);if(i?.features)for(const e of i.features)e.layer=e.sourceLayer=r;return i}async queryObjectIds(e,t){return(0,be.tD)(this,e,t,Ze)}async queryFeatureCount(e,t){return(0,be.VG)(this,e,t,Ze)}async queryExtent(e,t){return(0,be.KE)(this,e,t,Ze)}async queryRelatedFeatures(e,t){return(0,be.kp)(this,e,t,Ze)}async queryRelatedFeaturesCount(e,t){return(0,be.C9)(this,e,t,Ze)}async queryTopFeatures(e,t){const{source:r,capabilities:i}=await this.load();if(!r.queryTopFeatures||!i?.query?.supportsTopFeaturesQuery)throw new h.Z(Ze,"Layer source does not support queryTopFeatures capability");const s=await r.queryTopFeatures(Re.Z.from(e),t);if(s?.features)for(const e of s.features)e.layer=e.sourceLayer=this;return s}async queryTopObjectIds(e,t){const{source:r,capabilities:i}=await this.load();if(!r.queryTopObjectIds||!i?.query.supportsTopFeaturesQuery)throw new h.Z(Ze,"Layer source does not support queryTopObjectIds capability");return r.queryTopObjectIds(Re.Z.from(e),t)}async queryTopFeaturesExtent(e,t){const{source:r,capabilities:i}=await this.load();if(!r.queryTopExtents||!i?.query?.supportsTopFeaturesQuery)throw new h.Z(Ze,"Layer source does not support queryTopExtents capability");return r.queryTopExtents(Re.Z.from(e),t)}async queryTopFeatureCount(e,t){const{source:r,capabilities:i}=await this.load();if(!r.queryTopCount||!i?.query?.supportsTopFeaturesQuery)throw new h.Z(Ze,"Layer source does not support queryFeatureCount capability");return r.queryTopCount(Re.Z.from(e),t)}read(e,t){const r=e.featureCollection;if(r){const e=r.layers;e&&1===e.length&&(super.read(e[0],t),null!=r.showLegend&&super.read({showLegend:r.showLegend},t))}super.read(e,t),t&&"service"===t.origin&&(this.revert(["objectIdField","fields","timeInfo"],"service"),this.spatialReference||this.revert(["spatialReference"],"service"))}write(e,t){t={...t,origin:t?.origin??void 0,writeLayerSchema:t?.writeLayerSchema??this._hasMemorySource()};const{origin:r,layerContainerType:i,messages:s}=t;if(this.dynamicDataSource)return s?.push(xe(this,"using a dynamic data source cannot be written to web scenes, web maps and feature service items")),null;if(this.isTable){if("web-scene"===r||"web-map"===r&&"tables"!==i)return s?.push(xe(this,"using a table source cannot be written to web scenes and web maps")),null;if(this._hasMemorySource())return s?.push(xe(this,"using an in-memory table source cannot be written to web scenes and web maps")),null}else if(this.loaded&&"web-map"===r&&"tables"===i)return s?.push(xe(this,"using a non-table source cannot be written to tables in web maps")),null;return super.write(e,t)}clone(){if(this._hasMemorySource())throw new h.Z(Ze,`FeatureLayer (title: ${this.title}, id: ${this.id}) created using in-memory source cannot be cloned`);return super.clone()}serviceSupportsSpatialReference(e){return!!this.loaded&&("memory"===this.source?.type||(0,ke.D)(this,e))}async save(e){return(await r.e(2664).then(r.bind(r,32664))).save(this,e)}async saveAs(e,t){return(await r.e(2664).then(r.bind(r,32664))).saveAs(this,e,t)}_readEditingEnabled(e,t,r){let i=e.layerDefinition?.capabilities;return i?this._hasEditingCapability(i):(i=e.capabilities,t&&"web-map"===r?.origin&&!this._hasMemorySource()&&i?this._hasEditingCapability(i):void 0)}_hasEditingCapability(e){return e.toLowerCase().split(",").map((e=>e.trim())).includes("editing")}_writeEditingEnabled(e,t,r,i){if(!e){const e=this.capabilities?.operations?.supportsSync?"Query,Sync":"Query";(0,w.RB)("layerDefinition.capabilities",e,t),r&&!i?.writeLayerSchema&&(t.capabilities=e)}}_getLayerDomain(e){const t=this.fieldsIndex.get(e);return t?t.domain:null}_fetchFirstLayerId(e){return(0,u.default)(this.url,{query:{f:"json",...this.customParameters,token:this.apiKey},responseType:"json",signal:e}).then((e=>{const t=e.data;if(t)return Array.isArray(t.layers)&&t.layers.length>0?t.layers[0].id:Array.isArray(t.tables)&&t.tables.length>0?t.tables[0].id:void 0}))}async initLayerProperties(e){return this._set("source",e),e.sourceJSON&&(this.sourceJSON=e.sourceJSON,this.read(e.sourceJSON,{origin:"service",portalItem:this.portalItem,portal:this.portalItem?.portal,url:this.parsedUrl})),this._verifySource(),this._verifyFields(),(0,Ce.YN)(this.renderer,this.fieldsIndex),(0,Ce.UF)(this.timeInfo,this.fieldsIndex),(0,Te.y)(this,{origin:"service"})}async hasDataChanged(){return(0,be.gG)(this)}async fetchPublishingStatus(){const e=this.source;return e?.fetchPublishingStatus?e.fetchPublishingStatus():"unavailable"}_verifyFields(){const e=this.parsedUrl?.path??"undefined";this.objectIdField||console.log("FeatureLayer: 'objectIdField' property is not defined (url: "+e+")"),this.isTable||this._hasMemorySource()||-1!==e.search(/\/FeatureServer\//i)||this.fields?.some((e=>"geometry"===e.type))||console.log("FeatureLayer: unable to find field of type 'geometry' in the layer 'fields' list. If you are using a map service layer, features will not have geometry (url: "+e+")")}_fixTemplates(e,t){e&&e.forEach((e=>{const r=e.prototype&&e.prototype.attributes;r&&t&&delete r[t]}))}_verifySource(){if(this._hasMemorySource()){if(this.url)throw new h.Z("feature-layer:mixed-source-and-url","FeatureLayer cannot be created with both an in-memory source and a url")}else if(!this.url)throw new h.Z("feature-layer:source-or-url-required","FeatureLayer requires either a url, a valid portal item or a source")}_initMemorySource(e){e.forEach((e=>{e.layer=this,e.sourceLayer=this})),this._handles.add([e.on("after-add",(e=>{e.item.layer=this,e.item.sourceLayer=this})),e.on("after-remove",(e=>{e.item.layer=null,e.item.sourceLayer=null}))],"fl-source")}_resetMemorySource(e){e.forEach((e=>{e.layer=null,e.sourceLayer=null})),this._handles.remove("fl-source")}_hasMemorySource(){return!(this.url||!this.source)}};(0,i._)([(0,I.r)("service","capabilities")],Je.prototype,"readCapabilities",null),(0,i._)([(0,S.Cb)({json:{origins:{"portal-item":{write:!0},"web-map":{write:!0}}}})],Je.prototype,"charts",void 0),(0,i._)([(0,S.Cb)({readOnly:!0})],Je.prototype,"createQueryVersion",null),(0,i._)([(0,S.Cb)({json:{read:{source:"layerDefinition.copyrightText"}}})],Je.prototype,"copyright",void 0),(0,i._)([(0,S.Cb)({json:{read:{source:"layerDefinition.displayField"}}})],Je.prototype,"displayField",void 0),(0,i._)([(0,S.Cb)({types:d.LB,readOnly:!0})],Je.prototype,"defaultSymbol",void 0),(0,i._)([(0,S.Cb)({type:je.n})],Je.prototype,"dynamicDataSource",void 0),(0,i._)([(0,S.Cb)({type:Boolean})],Je.prototype,"editingEnabled",null),(0,i._)([(0,I.r)(["portal-item","web-scene"],"editingEnabled",["layerDefinition.capabilities"])],Je.prototype,"readEditingEnabled",null),(0,i._)([(0,I.r)("web-map","editingEnabled",["capabilities","layerDefinition.capabilities"])],Je.prototype,"readEditingEnabledFromWebMap",null),(0,i._)([(0,k.c)(["portal-item","web-scene"],"editingEnabled",{"layerDefinition.capabilities":{type:String}})],Je.prototype,"writeEditingEnabled",null),(0,i._)([(0,k.c)("web-map","editingEnabled",{capabilities:{type:String},"layerDefinition.capabilities":{type:String}})],Je.prototype,"writeEditingEnabledToWebMap",null),(0,i._)([(0,S.Cb)({readOnly:!0})],Je.prototype,"effectiveEditingEnabled",null),(0,i._)([(0,S.Cb)({...Ae.fields,json:{read:{source:"layerDefinition.fields"},origins:{service:{name:"fields"},"web-map":{write:{target:"layerDefinition.fields",overridePolicy:qe}}}}})],Je.prototype,"fields",void 0),(0,i._)([(0,S.Cb)(Ae.fieldsIndex)],Je.prototype,"fieldsIndex",void 0),(0,i._)([(0,S.Cb)({type:T.Z,json:{name:"formInfo",write:!0,origins:{"web-scene":{read:!1,write:!1}}}})],Je.prototype,"formTemplate",void 0),(0,i._)([(0,S.Cb)({json:{read:{source:"layerDefinition.extent"}}})],Je.prototype,"fullExtent",void 0),(0,i._)([(0,S.Cb)({json:{origins:{"web-map":{write:{target:"layerDefinition.geometryType",overridePolicy:qe,writer(e,t,r){const i=e?be.Fr.toJSON(e):null;i&&(0,w.RB)(r,i,t)}}}},read:{source:"layerDefinition.geometryType",reader:be.Fr.read}}})],Je.prototype,"geometryType",void 0),(0,i._)([(0,S.Cb)({json:{read:{source:"layerDefinition.hasM"}}})],Je.prototype,"hasM",void 0),(0,i._)([(0,S.Cb)({json:{read:{source:"layerDefinition.hasZ"}}})],Je.prototype,"hasZ",void 0),(0,i._)([(0,S.Cb)(ge.id)],Je.prototype,"id",void 0),(0,i._)([(0,S.Cb)({readOnly:!0,json:{origins:{service:{read:!0}},read:!1}})],Je.prototype,"infoFor3D",void 0),(0,i._)([(0,S.Cb)({json:{origins:{"web-map":{write:{target:"layerDefinition.type"}}}}})],Je.prototype,"isTable",void 0),(0,i._)([(0,I.r)("service","isTable",["type","geometryType"]),(0,I.r)("isTable",["layerDefinition.type","layerDefinition.geometryType"])],Je.prototype,"readIsTable",null),(0,i._)([(0,k.c)("web-map","isTable")],Je.prototype,"writeIsTable",null),(0,i._)([(0,S.Cb)(ge.iR)],Je.prototype,"labelsVisible",void 0),(0,i._)([(0,S.Cb)({type:[Se.Z],json:{origins:{service:{read:{source:"drawingInfo.labelingInfo",reader:Fe.r},write:{target:"drawingInfo.labelingInfo",enabled:!1}}},read:{source:"layerDefinition.drawingInfo.labelingInfo",reader:Fe.r},write:{target:"layerDefinition.drawingInfo.labelingInfo"}}})],Je.prototype,"labelingInfo",void 0),(0,i._)([(0,S.Cb)((()=>{const e=(0,f.d9)(ge.bT);return e.json.origins["portal-item"]={write:{target:"layerDefinition.drawingInfo.transparency",writer(e,t,r){(0,w.RB)(r,(0,Me.a)(e),t)}}},e})())],Je.prototype,"opacity",void 0),(0,i._)([(0,S.Cb)(ge.rn)],Je.prototype,"legendEnabled",void 0),(0,i._)([(0,S.Cb)({type:["show","hide"],json:(()=>{const e=(0,f.d9)(ge.rT.json);return e.origins["portal-item"]={read:!1,write:!1},e})()})],Je.prototype,"listMode",void 0),(0,i._)([(0,I.r)("globalIdField",["layerDefinition.globalIdField","layerDefinition.fields"])],Je.prototype,"readGlobalIdField",null),(0,i._)([(0,S.Cb)({json:{origins:{"web-map":{write:{target:"layerDefinition.objectIdField",overridePolicy:qe}}}}})],Je.prototype,"objectIdField",void 0),(0,i._)([(0,I.r)("objectIdField",["layerDefinition.objectIdField","layerDefinition.fields"])],Je.prototype,"readObjectIdField",null),(0,i._)([(0,S.Cb)({value:"ArcGISFeatureLayer",type:["ArcGISFeatureLayer"]})],Je.prototype,"operationalLayerType",void 0),(0,i._)([(0,S.Cb)(Ae.outFields)],Je.prototype,"outFields",void 0),(0,i._)([(0,S.Cb)({readOnly:!0})],Je.prototype,"parsedUrl",null),(0,i._)([(0,S.Cb)({type:String,json:{origins:{"web-scene":{read:!0,write:!0}},read:!1}})],Je.prototype,"path",void 0),(0,i._)([(0,S.Cb)(ge.C_)],Je.prototype,"popupEnabled",void 0),(0,i._)([(0,S.Cb)({type:s.Z,json:{name:"popupInfo",write:!0}})],Je.prototype,"popupTemplate",void 0),(0,i._)([(0,S.Cb)({readOnly:!0})],Je.prototype,"defaultPopupTemplate",null),(0,i._)([(0,S.Cb)({types:l.A,json:{origins:{service:{write:{target:"drawingInfo.renderer",enabled:!1}},"web-scene":{types:l.o,name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy:(e,t,r)=>({ignoreOrigin:r?.writeLayerSchema})}}},write:{target:"layerDefinition.drawingInfo.renderer",overridePolicy:(e,t,r)=>({ignoreOrigin:r?.writeLayerSchema})}}})],Je.prototype,"renderer",null),(0,i._)([(0,I.r)("service","renderer",["drawingInfo.renderer","defaultSymbol"]),(0,I.r)("renderer",["layerDefinition.drawingInfo.renderer","layerDefinition.defaultSymbol"])],Je.prototype,"readRenderer",null),(0,i._)([(0,S.Cb)((()=>{const e=(0,f.d9)(ge.YI);return e.json.origins["portal-item"]={read:!1,write:!1},e})())],Je.prototype,"screenSizePerspectiveEnabled",void 0),(0,i._)([(0,S.Cb)({clonable:!1})],Je.prototype,"source",null),(0,i._)([(0,F.p)("source")],Je.prototype,"castSource",null),(0,i._)([(0,I.r)("portal-item","source",["featureSet"]),(0,I.r)("web-map","source",["featureSet"])],Je.prototype,"readSource",null),(0,i._)([(0,S.Cb)({json:{read:{source:"layerDefinition.extent.spatialReference"}}})],Je.prototype,"spatialReference",void 0),(0,i._)([(0,S.Cb)({type:Number})],Je.prototype,"subtypeCode",void 0),(0,i._)([(0,S.Cb)({type:[we.Z]})],Je.prototype,"templates",void 0),(0,i._)([(0,I.r)("templates",["editFieldsInfo","creatorField","editorField","templates"])],Je.prototype,"readTemplates",null),(0,i._)([(0,S.Cb)({type:Ee.Z})],Je.prototype,"timeInfo",void 0),(0,i._)([(0,S.Cb)()],Je.prototype,"title",void 0),(0,i._)([(0,I.r)("service","title",["name"]),(0,I.r)("portal-item","title",["layerDefinition.title","layerDefinition.name","title"])],Je.prototype,"readTitle",null),(0,i._)([(0,I.r)("web-map","title",["layerDefinition.name","title"])],Je.prototype,"readTitleFromWebMap",null),(0,i._)([(0,S.Cb)({type:String})],Je.prototype,"sublayerTitleMode",void 0),(0,i._)([(0,S.Cb)({json:{read:!1}})],Je.prototype,"type",void 0),(0,i._)([(0,S.Cb)({type:String})],Je.prototype,"typeIdField",void 0),(0,i._)([(0,I.r)("service","typeIdField"),(0,I.r)("typeIdField",["layerDefinition.typeIdField"])],Je.prototype,"readTypeIdField",null),(0,i._)([(0,S.Cb)({type:[_e.Z]})],Je.prototype,"types",void 0),(0,i._)([(0,I.r)("service","types",["types"]),(0,I.r)("types",["layerDefinition.types"])],Je.prototype,"readTypes",null),(0,i._)([(0,S.Cb)({type:Boolean,json:{origins:{"portal-item":{write:{target:"layerDefinition.defaultVisibility"}}}}})],Je.prototype,"visible",void 0),(0,i._)([(0,I.r)("portal-item","visible",["visibility","layerDefinition.defaultVisibility"])],Je.prototype,"readVisible",null),Je=(0,i._)([(0,E.j)(De)],Je);const Ge=(0,j.d)({types:d.QT}),Be=Je},68825:(e,t,r)=>{r.d(t,{c:()=>f});var i,s=r(43697),o=r(78286),n=r(5600),a=(r(75215),r(67676),r(52011)),l=r(35454),u=r(96674);const d=new l.X({asc:"ascending",desc:"descending"});let c=i=class extends u.wq{constructor(e){super(e),this.field=null,this.valueExpression=null,this.order="ascending"}clone(){return new i({field:this.field,valueExpression:this.valueExpression,order:this.order})}};(0,s._)([(0,n.Cb)({type:String,json:{write:!0}})],c.prototype,"field",void 0),(0,s._)([(0,n.Cb)({type:String,json:{write:!0}})],c.prototype,"valueExpression",void 0),(0,s._)([(0,n.Cb)({type:d.apiValues,json:{read:d.read,write:d.write}})],c.prototype,"order",void 0),c=i=(0,s._)([(0,a.j)("esri.layers.support.OrderByInfo")],c);const p=c;function h(e,t,r){if(!e)return null;const i=e.find((e=>!!e.field));if(!i)return null;const s=new p;return s.read(i,r),[s]}function y(e,t,r,i){const s=e.find((e=>!!e.field));s&&(0,o.RB)(r,[s.toJSON()],t)}const f=e=>{let t=class extends e{constructor(){super(...arguments),this.orderBy=null}};return(0,s._)([(0,n.Cb)({type:[p],json:{origins:{"web-scene":{write:!1,read:!1}},read:{source:"layerDefinition.orderBy",reader:h},write:{target:"layerDefinition.orderBy",writer:y}}})],t.prototype,"orderBy",void 0),t=(0,s._)([(0,a.j)("esri.layers.mixins.OrderedLayer")],t),t}},40555:(e,t,r)=>{r.d(t,{y:()=>n});var i=r(66643),s=r(95330),o=r(20941);async function n(e,t,r){const n=e&&e.getAtOrigin&&e.getAtOrigin("renderer",t.origin);if(n&&"unique-value"===n.type&&n.styleOrigin){const a=await(0,i.q6)(n.populateFromStyle());if((0,s.k_)(r),!1===a.ok){const r=a.error;t&&t.messages&&t.messages.push(new o.Z("renderer:style-reference",`Failed to create unique value renderer from style reference: ${r.message}`,{error:r,context:t})),e.clear("renderer",t?.origin)}}}},59431:(e,t,r)=>{r.d(t,{P:()=>a});var i=r(70586),s=r(67900),o=r(8744);function n(e,t,r){if(null==e.hasM||e.hasZ)for(const e of t)for(const t of e)t.length>2&&(t[2]*=r)}function a(e,t,r){if(!e&&!t||!r)return;const i=(0,s._R)(r);l(e,r,i),l(t,r,i)}function l(e,t,r){if(e)for(const i of e)u(i.geometry,t,r)}function u(e,t,r){if((0,i.Wi)(e)||!e.spatialReference||(0,o.fS)(e.spatialReference,t))return;const a=(0,s._R)(e.spatialReference)/r;if(1!==a)if("x"in e)null!=e.z&&(e.z*=a);else if("rings"in e)n(e,e.rings,a);else if("paths"in e)n(e,e.paths,a);else if("points"in e&&(null==e.hasM||e.hasZ))for(const t of e.points)t.length>2&&(t[2]*=a)}},28141:(e,t,r)=>{r.d(t,{Z:()=>S});var i,s=r(43697),o=r(66577),n=r(92835),a=r(35454),l=r(96674),u=r(22974),d=r(70586),c=r(5600),p=r(75215),h=r(52011),y=r(30556),f=r(33955);r(67676);let m=i=class extends l.wq{constructor(e){super(e),this.groupByFields=void 0,this.topCount=void 0,this.orderByFields=void 0}clone(){return new i({groupByFields:this.groupByFields,topCount:this.topCount,orderByFields:this.orderByFields})}};(0,s._)([(0,c.Cb)({type:[String],json:{write:!0}})],m.prototype,"groupByFields",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{write:!0}})],m.prototype,"topCount",void 0),(0,s._)([(0,c.Cb)({type:[String],json:{write:!0}})],m.prototype,"orderByFields",void 0),m=i=(0,s._)([(0,h.j)("esri.rest.support.TopFilter")],m);const g=m;var b,w=r(82971);const _=new a.X({esriSpatialRelIntersects:"intersects",esriSpatialRelContains:"contains",esriSpatialRelCrosses:"crosses",esriSpatialRelDisjoint:"disjoint",esriSpatialRelEnvelopeIntersects:"envelope-intersects",esriSpatialRelIndexIntersects:"index-intersects",esriSpatialRelOverlaps:"overlaps",esriSpatialRelTouches:"touches",esriSpatialRelWithin:"within",esriSpatialRelRelation:"relation"}),v=new a.X({esriSRUnit_Meter:"meters",esriSRUnit_Kilometer:"kilometers",esriSRUnit_Foot:"feet",esriSRUnit_StatuteMile:"miles",esriSRUnit_NauticalMile:"nautical-miles",esriSRUnit_USNauticalMile:"us-nautical-miles"});let C=b=class extends l.wq{constructor(e){super(e),this.cacheHint=void 0,this.distance=void 0,this.geometry=null,this.geometryPrecision=void 0,this.maxAllowableOffset=void 0,this.num=void 0,this.objectIds=null,this.orderByFields=null,this.outFields=null,this.outSpatialReference=null,this.resultType=null,this.returnGeometry=!1,this.returnM=void 0,this.returnZ=void 0,this.start=void 0,this.spatialRelationship="intersects",this.timeExtent=null,this.topFilter=void 0,this.units=null,this.where="1=1"}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10}clone(){return new b((0,u.d9)({cacheHint:this.cacheHint,distance:this.distance,geometry:this.geometry,geometryPrecision:this.geometryPrecision,maxAllowableOffset:this.maxAllowableOffset,num:this.num,objectIds:this.objectIds,orderByFields:this.orderByFields,outFields:this.outFields,outSpatialReference:this.outSpatialReference,resultType:this.resultType,returnGeometry:this.returnGeometry,returnZ:this.returnZ,returnM:this.returnM,start:this.start,spatialRelationship:this.spatialRelationship,timeExtent:this.timeExtent,topFilter:this.topFilter,units:this.units,where:this.where}))}};(0,s._)([(0,c.Cb)({type:Boolean,json:{write:!0}})],C.prototype,"cacheHint",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{write:{overridePolicy:e=>({enabled:e>0})}}})],C.prototype,"distance",void 0),(0,s._)([(0,c.Cb)({types:o.qM,json:{read:f.im,write:!0}})],C.prototype,"geometry",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{write:!0}})],C.prototype,"geometryPrecision",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{write:!0}})],C.prototype,"maxAllowableOffset",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],C.prototype,"num",void 0),(0,s._)([(0,c.Cb)({json:{write:!0}})],C.prototype,"objectIds",void 0),(0,s._)([(0,c.Cb)({type:[String],json:{write:!0}})],C.prototype,"orderByFields",void 0),(0,s._)([(0,c.Cb)({type:[String],json:{write:!0}})],C.prototype,"outFields",void 0),(0,s._)([(0,c.Cb)({type:w.Z,json:{read:{source:"outSR"},write:{target:"outSR"}}})],C.prototype,"outSpatialReference",void 0),(0,s._)([(0,c.Cb)({type:String,json:{write:!0}})],C.prototype,"resultType",void 0),(0,s._)([(0,c.Cb)({json:{write:!0}})],C.prototype,"returnGeometry",void 0),(0,s._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],C.prototype,"returnM",void 0),(0,s._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],C.prototype,"returnZ",void 0),(0,s._)([(0,c.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],C.prototype,"start",void 0),(0,s._)([(0,y.c)("start"),(0,y.c)("num")],C.prototype,"writeStart",null),(0,s._)([(0,c.Cb)({type:String,json:{read:{source:"spatialRel",reader:_.read},write:{target:"spatialRel",writer:_.write}}})],C.prototype,"spatialRelationship",void 0),(0,s._)([(0,c.Cb)({type:n.Z,json:{write:!0}})],C.prototype,"timeExtent",void 0),(0,s._)([(0,c.Cb)({type:g,json:{write:!0}})],C.prototype,"topFilter",void 0),(0,s._)([(0,c.Cb)({type:String,json:{read:v.read,write:{writer:v.write,overridePolicy(e){return{enabled:(0,d.pC)(e)&&(0,d.pC)(this.distance)&&this.distance>0}}}}})],C.prototype,"units",void 0),(0,s._)([(0,c.Cb)({type:String,json:{write:!0}})],C.prototype,"where",void 0),C=b=(0,s._)([(0,h.j)("esri.rest.support.TopFeaturesQuery")],C),C.from=(0,p.se)(C);const S=C}}]);