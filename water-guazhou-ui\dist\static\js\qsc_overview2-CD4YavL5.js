import{d as c,a0 as r,c as n,o as i,Q as l,g as d,n as p,p as e,C as v}from"./index-r0dFAfgr.js";const _={class:"main"},u=c({__name:"qsc_overview2",setup(f){const s=r();n({});const t=n(),a=()=>{console.log(s.projectList),s.projectList[0].id};return i(()=>{a(),t.value=setInterval(()=>{a()},3e4)}),l(()=>{clearInterval(t.value)}),(m,o)=>(d(),p("div",_,o[0]||(o[0]=[e("div",{class:"card zutai-card"},[e("div",{class:"card-content",style:{top:"20%",left:"47%",width:"140px"}},[e("div",{class:"card-title"},[e("span",{style:{color:"#d8feff","text-align":"center"}},"清水池2")])])],-1)])))}}),B=v(u,[["__scopeId","data-v-45996947"]]);export{B as default};
