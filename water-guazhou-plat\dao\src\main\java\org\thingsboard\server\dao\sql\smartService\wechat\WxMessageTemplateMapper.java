package org.thingsboard.server.dao.sql.smartService.wechat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageTemplate;
import org.thingsboard.server.dao.util.imodel.query.smartService.wechat.WxMessageTemplatePageRequest;

@Mapper
public interface WxMessageTemplateMapper extends BaseMapper<WxMessageTemplate> {
    IPage<WxMessageTemplate> findByPage(WxMessageTemplatePageRequest request);

    boolean update(WxMessageTemplate entity);

    String getWechatTemplateId(String id);
}
