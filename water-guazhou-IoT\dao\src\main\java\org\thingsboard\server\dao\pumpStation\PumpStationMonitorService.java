package org.thingsboard.server.dao.pumpStation;


import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;

import java.util.List;
import java.util.Map;

/**
 * 泵站监控服务接口
 */
public interface PumpStationMonitorService {

    /**
     * 获取泵站监控数据
     *
     * @param stationIds      站点ID列表
     * @param timeGranularity 时间粒度
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @param pumpType        泵类型
     * @return 监控数据
     */
    Map<String, Object> getMonitorData(List<String> stationIds, String timeGranularity,
                                       Long startTime, Long endTime, String pumpType, TenantId tenantId) throws ThingsboardException;

    /**
     * 获取泵站详情
     *
     * @param stationIds 站点ID列表
     * @return 泵站详情列表
     */
    List<Map<String, Object>> getStationDetail(List<String> stationIds, TenantId tenantId) throws ThingsboardException;

    /**
     * 应用泵站方案
     *
     * @param schemeId   方案ID
     * @param stationIds 站点ID列表
     */
    void applyScheme(String schemeId, List<String> stationIds) throws ThingsboardException;
}
