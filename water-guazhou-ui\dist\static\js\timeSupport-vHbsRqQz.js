const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/geometryEngineJSON-DX0oHAsv.js","static/js/geometryEngineBase-BhsKaODW.js","static/js/json-Wa8cmqdu.js"])))=>i.map(i=>d[i]);
import{a3 as N}from"./index-r0dFAfgr.js";import{s as R,I}from"./Point-WxyopZva.js";import{fu as w,fv as E,J as d,fw as v,I as m,fx as T,fy as _,ao as G,c2 as q}from"./MapView-DaoQedLH.js";import{f as C}from"./projectionSupport-BDUl30tr.js";import{v as c}from"./utils-dKbgHYZY.js";function M(e){return e==="mesh"?w:E(e)}function P(e,t){return e?t?4:3:t?3:2}function $(e,t,i,r){return b(e,t,i,r.coords[0],r.coords[1])}function x(e,t,i,r,s,n){const l=P(s,n),{coords:a,lengths:u}=r;if(!u)return!1;for(let o=0,f=0;o<u.length;o++,f+=l)if(!b(e,t,i,a[f],a[f+1]))return!1;return!0}function b(e,t,i,r,s){if(!e)return!1;const n=P(t,i),{coords:l,lengths:a}=e;let u=!1,o=0;for(const f of a)u=F(u,l,n,o,f,r,s),o+=f*n;return u}function F(e,t,i,r,s,n,l){let a=e,u=r;for(let o=r,f=r+s*i;o<f;o+=i){u=o+i,u===f&&(u=r);const h=t[o],p=t[o+1],A=t[u],y=t[u+1];(p<l&&y>=l||y<l&&p>=l)&&h+(l-p)/(y-p)*(A-h)<n&&(a=!a)}return a}const S="feature-store:unsupported-query",j={esriSpatialRelIntersects:"intersects",esriSpatialRelContains:"contains",esriSpatialRelCrosses:"crosses",esriSpatialRelDisjoint:"disjoint",esriSpatialRelEnvelopeIntersects:"intersects",esriSpatialRelIndexIntersects:null,esriSpatialRelOverlaps:"overlaps",esriSpatialRelTouches:"touches",esriSpatialRelWithin:"within",esriSpatialRelRelation:null},g={spatialRelationship:{esriSpatialRelIntersects:!0,esriSpatialRelContains:!0,esriSpatialRelWithin:!0,esriSpatialRelCrosses:!0,esriSpatialRelDisjoint:!0,esriSpatialRelTouches:!0,esriSpatialRelOverlaps:!0,esriSpatialRelEnvelopeIntersects:!0,esriSpatialRelIndexIntersects:!1,esriSpatialRelRelation:!1},queryGeometry:{esriGeometryPoint:!0,esriGeometryMultipoint:!0,esriGeometryPolyline:!0,esriGeometryPolygon:!0,esriGeometryEnvelope:!0},layerGeometry:{esriGeometryPoint:!0,esriGeometryMultipoint:!0,esriGeometryPolyline:!0,esriGeometryPolygon:!0,esriGeometryEnvelope:!1}};function O(e){return e!=null&&g.spatialRelationship[e]===!0}function D(e){return e!=null&&g.queryGeometry[q(e)]===!0}function U(e){return e!=null&&g.layerGeometry[e]===!0}function V(){return N(()=>import("./geometryEngineJSON-DX0oHAsv.js").then(e=>e.g),__vite__mapDeps([0,1,2]))}function H(e,t,i,r,s){if(d(t)&&i==="esriGeometryPoint"&&(e==="esriSpatialRelIntersects"||e==="esriSpatialRelContains")){const n=v(new G,t,!1,!1);return Promise.resolve(l=>$(n,!1,!1,l))}if(d(t)&&i==="esriGeometryMultipoint"){const n=v(new G,t,!1,!1);if(e==="esriSpatialRelContains")return Promise.resolve(l=>x(n,!1,!1,l,r,s))}if(m(t)&&i==="esriGeometryPoint"&&(e==="esriSpatialRelIntersects"||e==="esriSpatialRelContains"))return Promise.resolve(n=>T(t,c(i,r,s,n)));if(m(t)&&i==="esriGeometryMultipoint"&&e==="esriSpatialRelContains")return Promise.resolve(n=>_(t,c(i,r,s,n)));if(m(t)&&e==="esriSpatialRelIntersects"){const n=M(i);return Promise.resolve(l=>n(t,c(i,r,s,l)))}return V().then(n=>{const l=n[j[e]].bind(null,t.spatialReference,t);return a=>l(c(i,r,s,a))})}async function K(e,t,i){const{spatialRel:r,geometry:s}=e;if(s){if(!O(r))throw new R(S,"Unsupported query spatial relationship",{query:e});if(I(s.spatialReference)&&I(i)){if(!D(s))throw new R(S,"Unsupported query geometry type",{query:e});if(!U(t))throw new R(S,"Unsupported layer geometry type",{query:e});if(e.outSR)return C(e.geometry&&e.geometry.spatialReference,e.outSR)}}}function Q(e){if(m(e))return!0;if(d(e)){for(const t of e.rings)if(t.length!==5||t[0][0]!==t[1][0]||t[0][0]!==t[4][0]||t[2][0]!==t[3][0]||t[0][1]!==t[3][1]||t[0][1]!==t[4][1]||t[1][1]!==t[2][1])return!1;return!0}return!1}async function X(e,t){if(!e)return null;const i=t.featureAdapter,{startTimeField:r,endTimeField:s}=e;let n=Number.POSITIVE_INFINITY,l=Number.NEGATIVE_INFINITY;if(r&&s)await t.forEach(a=>{const u=i.getAttribute(a,r),o=i.getAttribute(a,s);u==null||isNaN(u)||(n=Math.min(n,u)),o==null||isNaN(o)||(l=Math.max(l,o))});else{const a=r||s;await t.forEach(u=>{const o=i.getAttribute(u,a);o==null||isNaN(o)||(n=Math.min(n,o),l=Math.max(l,o))})}return{start:n,end:l}}function ee(e,t,i){if(!t||!e)return null;const{startTimeField:r,endTimeField:s}=e;if(!r&&!s)return null;const{start:n,end:l}=t;return n===null&&l===null?null:n===void 0&&l===void 0?J():r&&s?W(i,r,s,n,l):Y(i,r||s,n,l)}function W(e,t,i,r,s){return r!=null&&s!=null?n=>{const l=e.getAttribute(n,t),a=e.getAttribute(n,i);return(l==null||l<=s)&&(a==null||a>=r)}:r!=null?n=>{const l=e.getAttribute(n,i);return l==null||l>=r}:s!=null?n=>{const l=e.getAttribute(n,t);return l==null||l<=s}:void 0}function Y(e,t,i,r){return i!=null&&r!=null&&i===r?s=>e.getAttribute(s,t)===i:i!=null&&r!=null?s=>{const n=e.getAttribute(s,t);return n>=i&&n<=r}:i!=null?s=>e.getAttribute(s,t)>=i:r!=null?s=>e.getAttribute(s,t)<=r:void 0}function J(){return()=>!1}export{Q as I,K as P,ee as n,X as t,H as v};
