<template>
  <div class="tree-list-container">
    <div :class="{ headerBoxBorder: props.treeData.title }">
      <div v-if="props.treeData.title" class="tree-list-title">
        <slot name="header">
          <span>{{ props.treeData.title }} </span>
          <div
            v-if="props.treeData.titleRight?.length"
            class="tree-list-title-right-wrapper"
          >
            <template v-for="(item, i) in props.treeData.titleRight" :key="i">
              <FormItem
                v-if="item.field"
                v-model="state.queryParams[item.field]"
                :config="item"
                @change="item.onChange"
              ></FormItem>
              <FormItem v-else :config="item"></FormItem>
            </template>
          </div>
        </slot>
      </div>
    </div>
    <template v-for="(item, i) in props.treeData.extraFilters" :key="i">
      <div class="tree-form-item">
        <FormItem
          v-if="item.field"
          v-model="state.queryParams[item.field]"
          :config="item"
          :row="state.queryParams"
          @change="item.onChange"
        ></FormItem>
        <FormItem v-else :config="item" :row="state.queryParams"></FormItem>
      </div>
    </template>
    <el-select
      v-if="props.treeData.selectFilter"
      v-model="state.queryParams[props.treeData.selectFilter.key]"
      collapse-tags
      size="default"
      class="tree-filter-box"
      :multiple="props.treeData.selectFilter.multiple"
      :placeholder="'请选择' + props.treeData.selectFilter.label"
      :style="props.treeData.selectFilter.style"
      style="width: 100%"
      :filterable="props.treeData.selectFilter.search"
      @change="props.treeData.selectFilter?.handleChange"
    >
      <el-option
        v-for="option of props.treeData.selectFilter.options"
        :key="option.value as string"
        :value="option.value"
        :label="option.label"
        style="box-sizing: border-box"
      ></el-option>
    </el-select>
    <div v-if="props.treeData.switch" class="switch-box">
      <span
        v-for="item in props.treeData.switch.options"
        :key="item.value as string"
        :class="{ active: props.treeData?.switch?.curVal === item.value }"
        @click="
          props.treeData.switch?.handle && props.treeData.switch.handle(item)
        "
      >
        {{ item.label }}
      </span>
    </div>
    <el-input
      v-if="props.treeData.isFilterTree"
      v-model="state.filterText"
      :placeholder="props.treeData.filterPlaceHolder || '搜索关键字'"
      class="tree-filter-box"
      :clearable="true"
      :class="props.treeData.filterClassName"
      :prefix-icon="props.treeData.filterIcon"
    >
    </el-input>
    <div v-if="props.treeData.addRoot?.perm" class="operatebtn-box">
      <Button
        class="btn-lang edit-primary-blue"
        :config="props.treeData.addRoot"
      ></Button>
    </div>

    <div
      v-if="
        props.treeData.add?.perm ||
        props.treeData.delete?.perm ||
        props.treeData.edit?.perm
      "
      class="operatebtn-box"
      :class="{ 'active-operation': props.treeData.currentProject }"
    >
      <Button
        v-if="props.treeData.add?.perm"
        class="node-o-btn query-yellow"
        :config="props.treeData.add"
        type="warning"
      ></Button>

      <Button
        v-if="props.treeData.edit?.perm"
        class="node-o-btn add-child-blue"
        :config="props.treeData.edit"
      ></Button>

      <Button
        v-if="props.treeData.delete?.perm"
        class="node-o-btn delete-red"
        :config="props.treeData.delete"
        type="danger"
      ></Button>
    </div>
    <div
      class="tree-list-box"
      :class="{
        [className]: true
      }"
    >
      <el-scrollbar>
        <el-tree
          ref="refTree"
          :accordion="props.treeData.accordion !== false"
          highlight-current
          node-key="id"
          :default-expand-all="props.treeData.defaultExpandAll !== false"
          :expand-on-click-node="props.treeData.expandOnClickNode !== false"
          :current-node-key="props.treeData.currentProject?.id"
          :loading="props.treeData.loading"
          :data="props.treeData.data"
          :props="props.treeData.defaultProps || state.defaultProps"
          :filter-node-method="filterNode"
          :default-checked-keys="props.treeData.checkedKeys"
          :default-expanded-keys="props.treeData.expandNodeId"
          :show-checkbox="props.treeData.showCheckbox"
          :lazy="!props.treeData.isLazy === false"
          :load="props.treeData.loadFun"
          @check="props.treeData.handleCheck"
          @check-change="props.treeData.handleCheckChange"
          @node-expand="props.treeData.nodeExpand"
          @node-click="handnodeclick"
        >
          <template #default="{ node, data }">
            <div
              class="custom-tree-node"
              :class="{
                'active-tree-node':
                  props.treeData.currentProject &&
                  props.treeData.currentProject.id === data.id,
                'disabled-node': data.disabled
              }"
            >
              <p class="c-t-label">
                <Icon
                  v-if="data.iconifyIcon || treeData.iconifyIcon"
                  style="margin-right: 8px"
                  :style="data.iconStyle || treeData.iconStyle"
                  :icon="data.iconifyIcon || treeData.iconifyIcon"
                ></Icon>
                <el-icon
                  v-if="
                    data.icon ||
                    data.svgIcon ||
                    treeData.icon ||
                    treeData.svgIcon
                  "
                  style="margin-right: 8px"
                  :style="data.iconStyle || treeData.iconStyle"
                >
                  <i
                    v-if="data.icon || treeData.icon"
                    :class="data.icon || treeData.icon"
                  ></i>
                  <component
                    :is="data.svgIcon || treeData.svgIcon"
                    v-else
                  ></component>
                </el-icon>
                <!-- <i
                  v-if="data.icon || props.treeData.icon"
                  :class="data.icon || props.treeData.icon"
                ></i>
                <i
                  v-else
                  class="iconfont project-icon"
                  :class="{
                    'icon-xiangmu1': !node.isHost,
                    'icon-wangguan': node.isHost
                  }"
                ></i> -->
                <span
                  class="c-t-name"
                  :title="data.label || data[state.defaultProps.label]"
                  >{{ data.label || data[state.defaultProps.label] }}</span
                >
              </p>
              <div v-if="props.treeData.tags">
                <Tag
                  v-for="(item, i) in props.treeData.tags"
                  :key="i"
                  :round="item.round"
                  :color="
                    typeof item.color === 'string'
                      ? item.color
                      : item.color &&
                        item.color(item.field && data.data[item.field])
                  "
                >
                  {{ data.data[item.field] }}
                </Tag>
              </div>
              <div
                v-if="
                  props.treeData.add?.perm ||
                  props.treeData.delete?.perm ||
                  props.treeData.edit?.perm ||
                  props.treeData.nodeOperations?.length
                "
                class="hover-button"
              >
                <template
                  v-for="(btn, i) in props.treeData.nodeOperations"
                  :key="i"
                >
                  <template v-if="btn.iconifyIcon">
                    <Icon
                      v-if="
                        typeof btn.perm === 'function'
                          ? btn.perm(data)
                          : btn.perm
                      "
                      class="svgicon"
                      :class="[
                        btn.type || 'primary',
                        typeof btn.disabled === 'function'
                          ? btn.disabled(data)
                            ? 'disabled'
                            : ''
                          : btn.disabled
                            ? 'disabled'
                            : ''
                      ]"
                      :icon="
                        typeof btn.iconifyIcon === 'function'
                          ? btn.iconifyIcon(data)
                          : btn.iconifyIcon
                      "
                      :color="
                        typeof btn.color === 'function'
                          ? btn.color(data)
                          : btn.color
                      "
                      :title="
                        typeof btn.text === 'function'
                          ? btn.text(data)
                          : btn.text
                      "
                      @click.stop="
                        () =>
                          !(typeof btn?.disabled === 'function'
                            ? btn.disabled(data)
                            : btn?.disabled) && btn.click?.(data, $parent, node)
                      "
                    ></Icon>
                  </template>
                  <template v-else-if="btn.icon || btn.svgIcon">
                    <el-icon
                      v-if="
                        typeof btn.perm === 'function'
                          ? btn.perm(data)
                          : btn.perm
                      "
                      class="svgicon"
                      :class="[
                        btn.type || 'primary',
                        typeof btn.disabled === 'function'
                          ? btn.disabled(data)
                            ? 'disabled'
                            : ''
                          : btn.disabled
                            ? 'disabled'
                            : ''
                      ]"
                      :title="
                        typeof btn.text === 'function'
                          ? btn.text(data)
                          : btn.text
                      "
                      @click.stop="
                        () =>
                          !(typeof btn?.disabled === 'function'
                            ? btn.disabled(data)
                            : btn?.disabled) && btn.click?.(data, $parent, node)
                      "
                    >
                      <i
                        v-if="btn.icon"
                        :class="
                          typeof btn.icon === 'function'
                            ? btn.icon(data)
                            : btn.icon
                        "
                        :style="{
                          color:
                            typeof btn.color === 'function'
                              ? btn.color(data)
                              : btn.color
                        }"
                      ></i>
                      <component
                        :is="
                          typeof btn.svgIcon === 'function'
                            ? btn.svgIcon(data)
                            : btn.svgIcon
                        "
                        v-else
                      ></component>
                    </el-icon>
                  </template>
                </template>
                <Plus
                  v-if="props.treeData.add?.perm"
                  class="svgicon success"
                  :class="[
                    typeof props.treeData.add.disabled === 'function'
                      ? props.treeData.add.disabled(data)
                        ? 'disabled'
                        : ''
                      : props.treeData.add.disabled
                        ? 'disabled'
                        : ''
                  ]"
                  :size="12"
                  @click.stop="
                    () =>
                      !(typeof props.treeData.add?.disabled === 'function'
                        ? props.treeData.add.disabled(data)
                        : props.treeData.add?.disabled) &&
                      props.treeData.add?.click?.(data, $parent, node)
                  "
                />

                <Edit
                  v-if="props.treeData.edit?.perm"
                  class="svgicon primary"
                  :class="[
                    typeof props.treeData.edit.disabled === 'function'
                      ? props.treeData.edit.disabled(data)
                        ? 'disabled'
                        : ''
                      : props.treeData.edit.disabled
                        ? 'disabled'
                        : ''
                  ]"
                  :size="12"
                  @click.stop="
                    () =>
                      !(typeof props.treeData.edit?.disabled === 'function'
                        ? props.treeData.edit.disabled(data)
                        : props.treeData.edit?.disabled) &&
                      props.treeData.edit?.click?.(data, $parent, node)
                  "
                />

                <Delete
                  v-if="props.treeData.delete?.perm"
                  class="svgicon danger"
                  :size="12"
                  :class="[
                    typeof props.treeData.delete.disabled === 'function'
                      ? props.treeData.delete.disabled(data)
                        ? 'disabled'
                        : ''
                      : props.treeData.delete.disabled
                        ? 'disabled'
                        : ''
                  ]"
                  @click.stop="
                    () =>
                      !(typeof props.treeData.delete?.disabled === 'function'
                        ? props.treeData.delete.disabled(data)
                        : props.treeData.delete?.disabled) &&
                      props.treeData.delete?.click?.(data, $parent, node)
                  "
                />
              </div>
            </div>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Plus, Delete, Edit } from '@element-plus/icons-vue';
import { reactive, nextTick, ref, watch, toRefs, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { IElTree } from '@/common/types/element-plus';
import Tag from '../Form/Tag.vue';
import FormItem from '../Form/FormItem.vue';
import Button from '../Form/Button.vue';

const props = defineProps<{
  treeData: SLTreeConfig;
}>();
const state = reactive<{
  filterText: string;
  customer_disable: boolean;
  queryParams: any;
  defaultProps: any;
}>({
  filterText: '',
  customer_disable: false,
  queryParams: { ...(props.treeData.queryParams || {}) },
  defaultProps: props.treeData.defaultProps || {
    children: 'children',
    label: 'name',
    isLeaf: 'isLeaf'
  }
});
const className = computed(() => {
  let count = 0;
  const titlePatern = props.treeData.title ? 'title-' : '';
  if (
    props.treeData.add?.perm ||
    props.treeData.edit?.perm ||
    props.treeData.delete?.perm
  ) {
    count++;
  }
  if (props.treeData.addRoot?.perm) count++;
  if (props.treeData.isFilterTree) count++;
  if (props.treeData.switch?.options.length) count++;
  if (props.treeData.selectFilter) count++;
  props.treeData.extraFilters?.map(() => {
    count++;
  });
  const operatePatern = 'operate' + count;
  return 'tree-height-' + titlePatern + operatePatern;
});
const refTree = ref<IElTree>();
const filterNode = (value, data) => {
  const val: string | undefined = value?.trim()?.toLocaleLowerCase();
  if (!val) return true;
  // 有可能使用的name
  const label = data.label
    ? data?.label?.trim()?.toLocaleLowerCase()
    : data?.name?.trim()?.toLocaleLowerCase();
  if (!label) return false;
  return label.indexOf(val) !== -1;
};
watch(
  () => state.filterText,
  (value) => {
    refTree.value && refTree.value.filter(value);
  }
);
const setCurrentByKey = (key?: string) => {
  refTree.value?.setCurrentKey(key || props.treeData.currentProject?.id, true);
};
const setCheckedKeys = (keys?: string[]) => {
  refTree.value?.setCheckedKeys(keys || []);
};
watch(
  () => props.treeData.currentProject,
  (newVal: any) => {
    nextTick(() => {
      refTree.value && refTree.value.setCurrentKey(newVal?.id);
    });
  },
  {
    immediate: true
  }
);

const handnodeclick = (params: any, node?: any, e?: any) => {
  if (!params.disabled) {
    props.treeData.treeNodeHandleClick &&
      props.treeData.treeNodeHandleClick(params, node, e);
  }
};
defineExpose({
  ...toRefs(state),
  refTree,
  setCurrentByKey,
  setCheckedKeys
});
onMounted(() => {
  props.treeData.autoFillOptions?.();
});
</script>

<style lang="scss" scoped>
.dark {
  .tree-list-container {
    background-color: var(--el-bg-color);
  }
}
.tree-list-container {
  // .el-select {
  //   --el-component-size: 48px;
  // }
  background-color: #fff;
  height: 100%;
  width: 100%;
  .switch-box {
    padding: 16px 16px 0;
    display: flex;
    height: 48px;
    span {
      border: 1px solid #404354;
      display: block;
      flex: 1;
      color: #fff;
      text-align: center;
      line-height: 30px;
      &.active {
        background-color: #3e8ef7;
        border-color: #3e8ef7;
      }
      &:hover {
        cursor: pointer;
      }
      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
    }
  }
  .tree-list-title {
    height: 56px;
    line-height: 56px;
    margin: -1px -1px 0 0;
    padding: 0 16px;
    //  ;
    font-size: 18px;
    color: #9097c0;
    font-weight: 400;
    display: flex;
    justify-content: space-between;
    // border-bottom: 1px solid #4e5166;
    &-right-wrapper {
      display: flex;
      align-items: center;
    }
  }
  .tree-form-item {
    padding: 16px 16px 0;
  }
  .tree-filter-box {
    padding: 16px 16px 0;
    .el-input-group__append {
      background: #10bc1e;
      color: #fff;
    }
  }
  .operatebtn-box {
    padding: 16px 16px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn-lang {
      width: 100%;
    }
    .node-o-btn {
      width: 33%;
    }
  }
  .add-child-blue {
    border-color: #40b5e6;
    background-color: #40b5e6;

    &:hover,
    &:active,
    &:focus {
      background-color: #32d1db;
    }
  }
  .all-project {
    height: 40px;
    line-height: 40px;
    padding-left: 15px;
    width: calc(100% - 20px);
    margin: 10px 10px 0 10px;
    border-bottom: 1px solid #23729f;
    cursor: pointer;
    .icon-xiangmu1 {
      color: #25a1cf;
      margin-right: 10px;
    }
    &:hover {
      background: rgba(9, 17, 40, 0.5);
      box-shadow:
        0 2px 10px rgba(0, 0, 0, 0.2),
        inset 0 0 20px 2px rgba(0, 149, 255, 0.6);
    }
  }
  .active-all-project {
    color: #02feff;
    font-weight: 600;
    background: rgba(9, 17, 40, 0.5);
    border: 1px solid #23729f;
    box-shadow:
      0 2px 10px rgba(0, 0, 0, 0.2),
      inset 0 0 20px 2px rgba(0, 149, 255, 0.6);
    .icon-xiangmu1 {
      color: #526dff;
    }
  }
  .tree-list-box {
    padding-top: 10px;
  }
  .tree-height-operate {
    &0 {
      height: 100%;
    }
    &1 {
      height: calc(100% - 48px);
    }
    &2 {
      height: calc(100% - 48px * 2);
    }
    &3 {
      height: calc(100% - 48px * 3);
    }
    &4 {
      height: calc(100% - 48px * 4);
    }
    &5 {
      height: calc(100% - 48px * 5);
    }
  }
  .tree-height-title-operate {
    &0 {
      height: calc(100% - 55px);
    }
    &1 {
      height: calc(100% - 55px - 48px);
    }
    &2 {
      height: calc(100% - 55px - 48px * 2);
    }
    &3 {
      height: calc(100% - 55px - 48px * 3);
    }
    &4 {
      height: calc(100% - 55px - 48px * 4);
    }
    &5 {
      height: calc(100% - 55px - 48px * 5);
    }
  }

  .color {
    color: #000000;
    color: #a72d2d;
  }
}
.headerBoxBorder {
  border-bottom: 1px solid var(--el-border-color);
}

:deep(.el-tree) {
  height: 100%;
  padding: 0 16px;
  transition: height ease 1s;
  .el-tree-node {
    height: auto;

    &.is-current > .el-tree-node__content,
    .el-tree-node__content:hover {
      background-color: transparent;
      background-image: linear-gradient(270deg, #2980b9 0%, #6dd5fa 100%);
    }

    .el-tree-node__content {
      height: auto;
      border-radius: 5px;
    }
  }
}
.custom-tree-node {
  overflow: hidden;
  flex: 1;
  height: 40px;
  display: flex;
  font-size: 14px;
  padding-right: 8px;
  align-items: center;
  position: relative;
  justify-content: space-between;
  &:hover {
    .hover-button {
      display: flex;
    }
  }
  .hover-button {
    height: 18px;
    display: none;
    cursor: pointer;
  }
  .svgicon {
    margin-right: 8px;
  }
}
.disabled-node {
  cursor: not-allowed;
}
.c-t-label {
  display: flex;
  align-items: center;
  overflow: hidden;
}
.c-t-name {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
</style>
