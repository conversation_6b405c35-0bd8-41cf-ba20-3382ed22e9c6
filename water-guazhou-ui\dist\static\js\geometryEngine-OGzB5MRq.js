import{G as t}from"./geometryEngineBase-BhsKaODW.js";import{hydratedAdapter as r}from"./hydrated-DLkO5ZPr.js";import"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";function i(e){return Array.isArray(e)?e[0].spatialReference:e&&e.spatialReference}function R(e){return t.extendedSpatialReferenceInfo(e)}function x(e,n){return t.clip(r,i(e),e,n)}function y(e,n){return t.cut(r,i(e),e,n)}function S(e,n){return t.contains(r,i(e),e,n)}function A(e,n){return t.crosses(r,i(e),e,n)}function D(e,n,a){return t.distance(r,i(e),e,n,a)}function L(e,n){return t.equals(r,i(e),e,n)}function T(e,n){return t.intersects(r,i(e),e,n)}function V(e,n){return t.touches(r,i(e),e,n)}function v(e,n){return t.within(r,i(e),e,n)}function z(e,n){return t.disjoint(r,i(e),e,n)}function H(e,n){return t.overlaps(r,i(e),e,n)}function I(e,n,a){return t.relate(r,i(e),e,n,a)}function J(e){return t.isSimple(r,i(e),e)}function N(e){return t.simplify(r,i(e),e)}function O(e,n=!1){return t.convexHull(r,i(e),e,n)}function b(e,n){return t.difference(r,i(e),e,n)}function j(e,n){return t.symmetricDifference(r,i(e),e,n)}function q(e,n){return t.intersect(r,i(e),e,n)}function B(e,n=null){return t.union(r,i(e),e,n)}function C(e,n,a,s,c,u){return t.offset(r,i(e),e,n,a,s,c,u)}function E(e,n,a,s=!1){return t.buffer(r,i(e),e,n,a,s)}function P(e,n,a,s,c,u){return t.geodesicBuffer(r,i(e),e,n,a,s,c,u)}function G(e,n,a=!0){return t.nearestCoordinate(r,i(e),e,n,a)}function $(e,n){return t.nearestVertex(r,i(e),e,n)}function k(e,n,a,s){return t.nearestVertices(r,i(e),e,n,a,s)}function f(e){var n;return"xmin"in e?"center"in e?e.center:null:"x"in e?e:"extent"in e?((n=e.extent)==null?void 0:n.center)??null:null}function F(e,n,a){if(e==null)throw new o;const s=e.spatialReference;if((a=a??f(e))==null)throw new o;const c=e.constructor.fromJSON(t.rotate(e,n,a));return c.spatialReference=s,c}function K(e,n){if(e==null)throw new o;const a=e.spatialReference;if((n=n??f(e))==null)throw new o;const s=e.constructor.fromJSON(t.flipHorizontal(e,n));return s.spatialReference=a,s}function M(e,n){if(e==null)throw new o;const a=e.spatialReference;if((n=n??f(e))==null)throw new o;const s=e.constructor.fromJSON(t.flipVertical(e,n));return s.spatialReference=a,s}function Q(e,n,a,s){return t.generalize(r,i(e),e,n,a,s)}function U(e,n,a){return t.densify(r,i(e),e,n,a)}function W(e,n,a,s=0){return t.geodesicDensify(r,i(e),e,n,a,s)}function X(e,n){return t.planarArea(r,i(e),e,n)}function Y(e,n){return t.planarLength(r,i(e),e,n)}function Z(e,n,a){return t.geodesicArea(r,i(e),e,n,a)}function _(e,n,a){return t.geodesicLength(r,i(e),e,n,a)}function ee(e,n){return t.intersectLinesToPoints(r,i(e),e,n)}function ne(e,n){t.changeDefaultSpatialReferenceTolerance(e,n)}function te(e){t.clearDefaultSpatialReferenceTolerance(e)}class o extends Error{constructor(){super("Illegal Argument Exception")}}export{E as buffer,ne as changeDefaultSpatialReferenceTolerance,te as clearDefaultSpatialReferenceTolerance,x as clip,S as contains,O as convexHull,A as crosses,y as cut,U as densify,b as difference,z as disjoint,D as distance,L as equals,R as extendedSpatialReferenceInfo,K as flipHorizontal,M as flipVertical,Q as generalize,Z as geodesicArea,P as geodesicBuffer,W as geodesicDensify,_ as geodesicLength,q as intersect,ee as intersectLinesToPoints,T as intersects,J as isSimple,G as nearestCoordinate,$ as nearestVertex,k as nearestVertices,C as offset,H as overlaps,X as planarArea,Y as planarLength,I as relate,F as rotate,N as simplify,j as symmetricDifference,V as touches,B as union,v as within};
