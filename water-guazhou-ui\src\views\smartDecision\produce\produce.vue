<!-- 智慧生产 -->
<template>
  <div class="content">
    <!-- left -->
    <div class="content_left">
      <TitleCard :title="'水厂工艺'" class="scgy">
        <SCGY></SCGY>
      </TitleCard>
      <TitleCard :title="'合格率'" class="hgl">
        <HGL></HGL>
      </TitleCard>
    </div>
    <!-- center -->
    <div class="content_center">
      <GSL></GSL>
      <bigItemVue />
    </div>
    <!-- right -->
    <div class="content_left content_right">
      <TitleCard :title="'出厂流量'" class="ccll mg-12">
        <CCLL></CCLL>
      </TitleCard>
      <TitleCard :title="'出厂压力'" class="ccll mg-12">
        <CCYL></CCYL>
      </TitleCard>
      <!-- <div class="cards item_bg mg-12">
        <titleVue :config="'出厂压力'"></titleVue>
        <div class="chart">
          <VChart
            ref="refChart"
            :option="折线渐变面积(ccllData)"
          ></VChart>
        </div>
      </div> -->
      <TitleCard :title="'出厂水质'" class="ccsz">
        <CCSZ></CCSZ>
      </TitleCard>
    </div>
  </div>
</template>

<script lang="ts" setup>
// import scrollVue from './components/scroll.vue'
import bigItemVue from './components/bigItem.vue';

import TitleCard from '../components/TitleCard.vue';
import SCGY from './components/SCGY.vue';
import HGL from './components/HGL.vue';
import GSL from './components/GSL.vue';
import CCLL from './components/CCLL.vue';
import CCYL from './components/CCYL.vue';
import CCSZ from './components/CCSZ.vue';
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  width: 100%;
  padding: 100px 50px 30px;
  display: flex;
  justify-content: space-between;
  // background-color: rgb(0, 10, 20);
  color: #fff;
  padding-top: 92px;

  .content_left {
    width: 479px;
    height: 100%;
    z-index: 2;
    padding: 10px;
    .scgy {
      width: 100%;
      margin-bottom: 12px;
      // height: 66%;
    }
    .hgl {
      width: 100%;
      height: 180px;
    }
    .cards {
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px;

      .card {
        flex: 1;
      }
    }
  }
  .content_center {
    flex: 1;
    padding: 10px;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    .cards {
      width: 100%;
    }
  }
  .content_right {
    .ccll {
      width: 100%;
      height: 270px;
    }
    .ccsz {
      width: 100%;
      height: 375px;
    }
  }
}

.chart {
  width: 100%;
  height: 270px;
}

.mg_top_10 {
  margin-top: 10px;
}

.item_center_big_bg {
  background-image: url('../imgs/shuichang.png');
  background-size: 100% 100%;
  background-position: top;
  background-repeat: no-repeat;
}
.flex {
  display: flex;
}
.fl_ju_sb {
  justify-content: space-between;
}

.fl_dc_cl {
  flex-direction: column;
}
.center_item {
  position: absolute;
  width: 100px;
  height: 40px;
  background-color: #2e2f78;
  line-height: 40px;
  border-radius: 5px;
  text-align: center;
}
.mg-12 {
  margin-bottom: 12px;
}
</style>
