package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DEVICE_COMMAND_TABLE_NAME)
@TableName(ModelConstants.DEVICE_COMMAND_TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
public class DeviceCommand {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.DEVICE_COMMAND_DEVICE_KEY)
    private String deviceKey;

    @Column(name = ModelConstants.DEVICE_COMMAND_COMMAND_TYPE)
    private String commandType;

    @Column(name = ModelConstants.DEVICE_COMMAND_COMMAND)
    private String command;

    @Column(name = ModelConstants.DEVICE_COMMAND_OPTION_TIME)
    private Date optionTime;

    @Column(name = ModelConstants.DEVICE_COMMAND_OPTION_USER)
    private String optionUser;

    @Column(name = ModelConstants.DEVICE_COMMAND_OPTION_IP)
    private String optionIp;

    /**
     * 操作结果.
     * 1：已缓存到平台,等待执行;
     * 2：已作废（有同类型新指令）;
     * 3：已作废（手动作废）;
     * 4：已下发到设备
     */
    @Column(name = ModelConstants.DEVICE_COMMAND_OPTION_RESULT)
    private String optionResult;

    @Column(name = ModelConstants.DEVICE_COMMAND_EXECUTE_TIME)
    private Date executeTime;

    @Column(name = ModelConstants.DEVICE_COMMAND_EXECUTE_RESULT)
    private String executeResult;

    @Column(name = ModelConstants.DEVICE_COMMAND_EXECUTE_RESPONSE)
    private String executeResponse;

    @Column(name = ModelConstants.TB_OPERATING_INCOME_INPUT_UPDATE_USER)
    private String updateUser;

    @Column(name = ModelConstants.DEVICE_COMMAND_AEP_COMMAND_ID)
    private String aepCommandId;

}
