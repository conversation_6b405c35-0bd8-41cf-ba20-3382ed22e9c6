import{d as r,r as s,o as i,ay as c,g as p,n as l,q as d,i as f,C as _}from"./index-r0dFAfgr.js";const y={class:"chart-wrapper"},m=r({__name:"SBLXSLTJ",setup(u){const e=s({deviceTypeOption:null}),a=(t,o)=>({tooltip:{trigger:"axis"},grid:{containLabel:!0,left:20,top:40,right:20,bottom:20},xAxis:{type:"category",data:t,axisLabel:{color:"#fff",rotate:45}},yAxis:{name:"个",type:"value",splitLine:{lineStyle:{color:"#666",type:"dashed"}},axisLabel:{color:"#00ffff"}},series:[{type:"bar",data:o,barWidth:10,itemStyle:{color:"#00ffff"}}]});return i(()=>{e.deviceTypeOption=a(["排气阀","消防栓","流量计","压力计","阀门","水表","节点","四通","三通","弯头","管线"],[12,32,46,46,215,541,221,75,124,165,789])}),(t,o)=>{const n=c("VChart");return p(),l("div",y,[d(n,{option:f(e).deviceTypeOption},null,8,["option"])])}}}),x=_(m,[["__scopeId","data-v-0868d1c9"]]);export{x as default};
