import{_ as le}from"./Panel-DyoxrWMd.js";import{d as ne,c as C,r as A,b as v,W as J,bB as pe,o as ce,Q as de,ay as me,g as ue,n as ye,q as _,i as b,F as fe,p as ge,X as he,a1 as we,_ as ve,C as be}from"./index-r0dFAfgr.js";import{GetFieldConfig as Q,GetFieldUniqueValue as xe}from"./fieldconfig-Bk3o1wi7.js";import{P as Fe}from"./pipe-nogVzCHG.js";import Oe from"./PipeDetail-CTBPYFJW.js";import{a as Ce,m as _e,n as ke}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as qe,a as Ne}from"./LayerHelper-Cn-iiqxI.js";import{e as Le,i as Ie}from"./QueryHelper-ILO3qZqg.js";import{s as D,i as Se}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{_ as Te}from"./Search-NSrhrIa_.js";import"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./DateFormatter-Bm9a68Ax.js";import"./config-fy91bijz.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";const Ae={class:"chart-box"},De=ne({__name:"CountStatistics",props:{view:{},telport:{}},setup(z){const y=z,p=C(),S=C(),T=C(),k=C(),q=C(),a=A({curNode:void 0,curOperate:"",pipeLayerOption:[],tabs:[],staticFields:["SUBTYPE ","DISTRICT","MATERIAL","DIAMETER","JUNCTION","OCCUPYSURF","GASCHAR","ANTIMATERIAL","DATATYPE","VENDER","OWNERDEPT","MANAGEDEPT"],layerFields:{},chartOption:null,alllayersbardata:[],chartFields:[]}),o={chartTabs:[],staticField:"OBJECTID"},x=A({group:[{fieldset:{desc:"统计字段"},fields:[{type:"tree",options:[],style:{width:"50%",height:"200px",padding:"8px"},field:"layer",nodeKey:"label",showCheckbox:!0,className:"sql-list-wrapper",handleCheckChange:async(e,r)=>{a.curLayer=e,await j(e.label,r)},extraFormItem:[{type:"tree",style:{width:"50%",height:"200px",padding:"8px"},showCheckbox:!0,multiple:!0,className:"sql-list-wrapper",field:"staticFields",options:[],handleCheckChange:async(e,r)=>{const t=a.chartFields.find(i=>{var s;return i.layerName===((s=a.curLayer)==null?void 0:s.label)});if(t){const i=t.fields.findIndex(s=>s.value===e.value);i!==-1&&t.fields.splice(i,1),r&&t.fields.push(e)}}}]}]},{id:"field-construct",fieldset:{desc:"过滤条件（可选）"},fields:[{type:"select",label:"图层名称",field:"layer1",options:[],onChange:e=>{const r=a.chartFields.find(t=>t.layerName===e);r&&p.value&&(p.value.dataForm.sql=r.sql||"")}},{type:"list",label:"图层字段",data:[],className:"sql-list-wrapper",setData:async(e,r)=>{var t,i;if(!r.layer1)e.data=[];else{const s=await Q(r.layer1);e.data=((i=(t=s.data)==null?void 0:t.result)==null?void 0:i.rows)||[]}},setDataBy:"layer1",displayField:"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:e=>{a.curNode=e,g(e.name)}},{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{g("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{g("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{g(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{g("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{g("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{g("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{g("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{g("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:e=>{g("'"+e+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>a.curOperate==="uniqueing"?"正在获取唯一值":"获取唯一值",loading:()=>a.curOperate==="uniqueing",disabled:()=>a.curOperate==="detailing",styles:{width:"100%",borderRadius:"0"},click:()=>H()}]}]}]},{type:"textarea",label:"属性条件",field:"sql",placeholder:"OBJECTID > 0",onChange:()=>E()},{type:"btn-group",btns:[{perm:!0,text:"清除",disabled:()=>a.curOperate==="detailing"||a.curOperate==="statistising",size:"small",type:"danger",click:()=>te()}]},{type:"btn-group",label:"几何条件：",size:"small",btns:[{perm:!0,text:()=>a.curOperate==="drawing"?"正在绘制":"绘制范围",loading:()=>a.curOperate==="drawing",type:"success",click:()=>K()},{perm:!0,text:"清空范围",type:"danger",disabled:()=>a.curOperate==="drawing",click:()=>ae()}]}]},{fields:[{type:"btn-group",itemContainerStyle:{marginBottom:"5px"},btns:[{perm:!0,text:()=>a.curOperate==="statistising"?"正在统计":"统计",loading:()=>a.curOperate==="statistising",disabled:()=>["statistising","detailing","drawing"].indexOf(a.curOperate)!==-1,type:"warning",click:()=>Z(),styles:{width:"100%"}}]}]},{fields:[{type:"btn-group",btns:[{perm:!0,text:()=>a.curOperate==="detailing"?"正在查询":"查看详情",loading:()=>a.curOperate==="detailing",disabled:()=>["statistising","detailing","drawing"].indexOf(a.curOperate)!==-1,click:()=>{var e;a.curOperate="detailing",(e=q.value)==null||e.openDialog()},styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12}),G=A({filters:[{type:"tabs",field:"type",itemContainerStyle:{width:"100%"},tabs:[],onChange:()=>R()}]}),W=async()=>{var s,n;if(!y.view)return;const e=Ne(y.view),t=((n=(s=(await he(e)).data)==null?void 0:s.result)==null?void 0:n.rows)||[];a.pipeLayerOption=[],t.map(l=>{var f;(f=a.pipeLayerOption)==null||f.push({label:l.layername,value:l.layername,id:l.layerid,data:l})});const i=x.group[0].fields[0];i&&(i.options=a.pipeLayerOption)},Y=async e=>{var i,s;if(a.layerFields[e])return a.layerFields[e];const r=await Q(e),t=we(((s=(i=r.data)==null?void 0:i.result)==null?void 0:s.rows)||[],{id:"name",value:"name",label:"alias"});return a.layerFields[e]=t,t},j=async(e,r)=>{var f,h,F,O,u,N;const t=x.group[0].fields[0].extraFormItem,i=t&&t[0],s=r?await Y(e):[];i&&(i.options=s.filter(m=>a.staticFields.indexOf(m.value)!==-1));const n=x.group[1].fields[0];let l=a.chartFields.find(m=>m.layerName===e);if(l||(l={layerName:e,fields:[]},a.chartFields.unshift(l)),r){const m=a.pipeLayerOption.find(L=>L.label===e);m&&((f=n.options)==null||f.push(m))}else n&&(n.options=(h=n.options)==null?void 0:h.filter(m=>m.label!==e)),((O=(F=p.value)==null?void 0:F.dataForm)==null?void 0:O.layer1)===e&&p.value&&(p.value.dataForm.layer1=n.options&&((u=n.options[0])==null?void 0:u.value)||"");p.value&&(p.value.dataForm.staticFields=((N=a.chartFields.find(m=>m.layerName===e))==null?void 0:N.fields.map(m=>m.value))||[])},H=async()=>{var r,t,i;if(!a.curNode)return;a.curOperate="uniqueing";const e=(r=p.value)==null?void 0:r.dataForm.layer1;if(!e){v.warning("请先选择要过滤的图层");return}try{const s=(t=a.pipeLayerOption.find(h=>h.label===e))==null?void 0:t.id,n=await xe({usertoken:J().gToken,layerid:s,f:"pjson",field_name:a.curNode.name}),l=(i=x.group.find(h=>h.id==="field-construct"))==null?void 0:i.fields[2].extraFormItem,f=l&&l[0];f&&(f.data=n.data.result.rows)}catch{v.error("获取唯一值失败")}a.curOperate=""},g=e=>{var t;if(!p.value)return;(t=p.value)!=null&&t.dataForm||(p.value.dataForm={});const r=p.value.dataForm.sql||" ";p.value.dataForm.sql=r+e+" ",E()},E=()=>{var t,i;const e=(t=p.value)==null?void 0:t.dataForm.layer1;if(!e)return;const r=a.chartFields.find(s=>s.layerName===e);r&&(r.sql=((i=p.value)==null?void 0:i.dataForm.sql)||"")},K=()=>{var e,r;y.view&&((e=q.value)==null||e.closeDialog(),(r=T.value)==null||r.Close(),a.curOperate="drawing",o.graphicsLayer=qe(y.view,{id:"length-statistics",title:"长度统计绘制图形"}),o.queryGeometry=void 0,D("crosshair"),o.drawer=Se(y.view),o.drawAction=o.drawer.create("polygon"),o.drawAction.on(["vertex-add","cursor-update"],P),o.drawAction.on("draw-complete",t=>{var s;P(t),D("");const i=Ce("polygon",t.vertices,(s=y.view)==null?void 0:s.spatialReference);o.queryGeometry=i,a.curOperate=""}))},P=e=>{var t,i,s,n;const r=e.vertices.length<3?_e(e.vertices,(t=y.view)==null?void 0:t.spatialReference):ke(e.vertices,(i=y.view)==null?void 0:i.spatialReference);(s=o.graphicsLayer)==null||s.removeAll(),r&&((n=o.graphicsLayer)==null||n.add(r))},X=async()=>{var e;y.view&&((e=q.value)==null||e.extentTo(y.view))},Z=async()=>{var t,i,s,n;const e=(t=p.value)==null?void 0:t.dataForm.layer;if(!(e!=null&&e.length)){v.warning("请选择要统计的图层");return}const r=(s=(i=p.value)==null?void 0:i.dataForm)==null?void 0:s.staticFields;if(!(r!=null&&r.length)){v.warning("请选择要统计的属性");return}a.curOperate="statistising";try{const l=$();ee(l),(n=T.value)==null||n.Open(),await pe(),R(),a.tabs=[],await B(l,0),a.tabs.length||v.info("查询结果为空")}catch(l){v.error(l.message||"统计失败")}a.curOperate=""},$=()=>{var t;const e=((t=p.value)==null?void 0:t.dataForm.layer)||[];return a.pipeLayerOption.filter(i=>e.indexOf(i.value)!==-1)},ee=e=>{var i,s,n;const r=(i=G.filters)==null?void 0:i.find(l=>l.field==="type"),t=(e==null?void 0:e.map(l=>({label:l.label,name:l.id||"",data:[]})))||[];r&&(r.tabs=t.map(l=>({...l,value:l.name}))),(s=k.value)!=null&&s.queryParams&&(k.value.queryParams.type=(n=t[0])==null?void 0:n.name),o.chartTabs=t},R=async()=>{var t,i,s,n,l,f,h,F,O;const e=(t=k.value)==null?void 0:t.queryParams.type,r=a.pipeLayerOption.find(u=>u.id===e);if(e)try{const u=a.chartFields.find(c=>c.layerName===(r==null?void 0:r.label)),N=(u==null?void 0:u.fields.map(c=>c.value))||[],m=u==null?void 0:u.sql,L=await Fe({usertoken:J().gToken,layerids:JSON.stringify([e]),group_fields:JSON.stringify(N),statistic_field:o.staticField,statistic_type:"1",where:o.queryGeometry?"":m,geometry:o.queryGeometry,f:"pjson"});if(L.data.code===1e4){const c=o.chartTabs.find(d=>d.name===e);c&&(c.data=((l=(n=(s=(i=L.data)==null?void 0:i.result)==null?void 0:s.rows)==null?void 0:n.find(d=>d.layerid===e))==null?void 0:l.rows)||[]);const V=[],M=[],ie=c==null?void 0:c.label,se=((f=a.chartFields.find(d=>d.layerName===ie))==null?void 0:f.fields)||[];(h=c==null?void 0:c.data)==null||h.map(d=>{let w="";se.map(oe=>{const I=d[oe.value],U=I===null||I===""||I===" "?"<空>":I;w===""?w=U:w+=" "+U}),w&&M.push(d[o.staticField]),w&&V.push(w==null?void 0:w.toString())}),(F=S.value)==null||F.resize(),(O=S.value)==null||O.clear(),a.chartOption={title:{},tooltip:{trigger:"axis"},calculable:!0,xAxis:[{type:"category",data:V.map(d=>d!=null&&d.indexOf&&d.indexOf(" ")>0?d.replace(/\s+/g,`
`):d),axisLabel:{interval:0,rotate:0}}],grid:{left:60,right:72,top:40,bottom:96},yAxis:[{type:"value",name:"单位(个)",splitLine:{lineStyle:{color:"#fff",opacity:.2}}}],dataZoom:[{show:!0,start:0,end:100},{type:"inside",start:0,end:100},{show:!0,yAxisIndex:0,filterMode:"empty",width:30,height:"80%",showDataShadow:!1,left:"93%"}],series:[{name:c==null?void 0:c.label,type:"bar",data:M||[],barWidth:30,label:{show:!0,position:"top"},itemStyle:{normal:{color:"#337AB7"}}}]}}else{v.error("统计失败"),a.curOperate="";return}}catch(u){console.dir(u),v.error("统计失败")}},B=async(e,r)=>{try{const t=e[r],i=a.chartFields.find(n=>n.layerName===(t==null?void 0:t.label)),s=await Le(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+t.id,Ie({returnGeometry:!1,where:o.queryGeometry?"":(i==null?void 0:i.sql)||"1=1",geometry:o.queryGeometry,orderByFields:["OBJECTID asc"]}));s!==null&&a.tabs.push({label:t.label,name:t.label,data:s}),r<e.length-1&&await B(e,++r)}catch{throw console.log("发生错误，获取详情ids失败"),new Error("发生错误，获取详情ids失败")}},te=()=>{var e;(e=p.value)!=null&&e.dataForm&&(p.value.dataForm.sql="")},re=()=>{var e,r,t;a.curOperate="",D(""),(e=o.drawAction)==null||e.destroy(),(r=o.drawer)==null||r.destroy(),o.drawAction=void 0,o.drawer=void 0,o.graphicsLayer&&((t=y.view)==null||t.map.remove(o.graphicsLayer))},ae=()=>{var e,r,t;(e=o.graphicsLayer)==null||e.removeAll(),(r=o.drawAction)==null||r.destroy(),(t=o.drawer)==null||t.destroy(),o.queryGeometry=void 0};return ce(()=>{W()}),de(()=>{re()}),(e,r)=>{const t=ve,i=me("VChart"),s=le;return ue(),ye("div",null,[_(t,{ref_key:"refForm",ref:p,config:b(x)},null,8,["config"]),_(s,{ref_key:"refChartPanel",ref:T,"custom-class":"gis-length-statistics-panel",title:"数量统计结果"},{default:fe(()=>[_(Te,{ref_key:"refChartTab",ref:k,style:{padding:"0"},config:b(G)},null,8,["config"]),ge("div",Ae,[_(i,{ref_key:"refChart",ref:S,option:b(a).chartOption},null,8,["option"])])]),_:1},512),_(Oe,{ref_key:"refDetail",ref:q,tabs:b(a).tabs,telport:e.telport,onClose:r[0]||(r[0]=()=>b(a).curOperate=""),onRefreshed:r[1]||(r[1]=()=>b(a).curOperate=""),onRefreshing:r[2]||(r[2]=()=>b(a).curOperate="detailing"),onRowdblclick:X},null,8,["tabs","telport"])])}}}),ir=be(De,[["__scopeId","data-v-b6076855"]]);export{ir as default};
