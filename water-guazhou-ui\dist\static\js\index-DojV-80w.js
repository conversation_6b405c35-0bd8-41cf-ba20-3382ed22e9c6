import{_ as C}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as I}from"./CardSearch-CB_HNR-Q.js";import{_ as y}from"./CardTable-rdWOL4_6.js";import{d as k,c as u,D as h,eX as f,a8 as S,s as U,r as g,x as c,bT as R,o as D,g as E,n as T,q as _,i as b,ak as V,C as j}from"./index-r0dFAfgr.js";import{c as v}from"./data-XGHpLV70.js";import{b as w,c as N,a as q}from"./schedulingInstructions-DdZ9MUvq.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const F={class:"wrapper"},P=k({__name:"index",setup(W){const l=u(),p=u(),L=u({defaultParams:{receiveUserId:h(f.get("userId")||"")},filters:[{label:"",field:"receiveUserId",type:"select",options:S(()=>d.UserList),readonly:!0},{type:"btn-group",btns:[{perm:!0,text:"接收",svgIcon:U(V),click:()=>x()},{perm:!0,text:"拒绝",type:"danger",click:()=>{var e;(e=p.value)==null||e.openDialog()}}]}]}),a=g({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",handleSelectChange:e=>{a.selectList=e},selectList:[],columns:[{label:"发送人",prop:"sendUserName"},{label:"发送时间",prop:"sendTime"},{label:"接收站点",prop:"receiveDeptName"},{label:"指令内容",prop:"sendContent"},{label:"执行时间",prop:"executionTime"},{label:"备注",prop:"remark"},{label:"指令状态",prop:"commandStatus",tag:!0,tagColor:e=>{var t;return((t=v[e.commandStatus])==null?void 0:t.color)||""},formatter:e=>{var t;return((t=v[e.commandStatus])==null?void 0:t.value)||""}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,i()}}}),n=g({title:"拒绝",dialogWidth:"500px",labelWidth:"100px",submitting:!1,submit:e=>{var r;n.submitting=!0;const t=[];(r=a.selectList)==null||r.forEach(s=>{t.push(s.id)});const o={idList:t,rejectRemark:e.rejectRemark||""};w(o).then(()=>{var s;c.success("拒绝成功"),a.selectList=[],(s=p.value)==null||s.closeDialog(),n.submitting=!1,i()}).catch(s=>{c.warning(s),n.submitting=!1})},defaultValue:{},group:[{fields:[{label:"拒绝原因",field:"rejectRemark",type:"input",rules:[{required:!0,message:"请输入拒绝原因"}]}]}]});function x(){var o,r,s;const e=[];(o=a.selectList)==null||o.forEach(m=>{e.push(m.id)});const t={idList:e,receiveUserId:((r=l.value)==null?void 0:r.queryParams)&&((s=l.value)==null?void 0:s.queryParams.receiveUserId)||""};N(t).then(()=>{c.success("接收成功"),a.selectList=[],i()}).catch(m=>{c.warning(m)})}const d=g({UserList:[],getUserListValue:e=>{R({pid:e}).then(t=>{const o=t.data.data.data||[];d.UserList=o.map(r=>({label:r.firstName,value:h(r.id.id)}))})}}),i=async()=>{const e={size:a.pagination.limit,page:a.pagination.page,receiveDeptId:f.get("departmentId")||"",commandStatus:"WAITING_RECEIVE"};q(e).then(t=>{a.dataList=t.data.data.data||[],a.pagination.total=t.data.data.total||0})};return D(async()=>{i(),d.getUserListValue(f.get("departmentId")||"")}),(e,t)=>{const o=y,r=I,s=C;return E(),T("div",F,[_(o,{class:"card-table",config:b(a)},null,8,["config"]),_(r,{ref_key:"refSearch",ref:l,class:"mag_top_10",config:b(L)},null,8,["config"]),_(s,{ref_key:"refForm",ref:p,config:b(n)},null,8,["config"])])}}}),$=j(P,[["__scopeId","data-v-6d02ad13"]]);export{$ as default};
