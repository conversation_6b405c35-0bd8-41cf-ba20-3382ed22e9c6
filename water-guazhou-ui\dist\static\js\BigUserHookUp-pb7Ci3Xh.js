import{d as T,c as y,r as p,s as g,a8 as b,b as n,S as D,y as P,o as I,g as M,n as B,p as v,q as u,i as f,aq as H,al as w,dO as j,bq as q,dG as G,C as U}from"./index-r0dFAfgr.js";import{_ as V}from"./Search-NSrhrIa_.js";import{_ as F}from"./index-BJ-QPYom.js";import{D as z}from"./index-0NlGN6gS.js";import{f as E}from"./DateFormatter-Bm9a68Ax.js";import{b as R,P as O,G as $,D as J,H as K}from"./hookupDevice-Bcbk7s68.js";const Q={class:"device-hookup"},W={class:"left"},X={class:"right overlay-y"},Y=T({__name:"BigUserHookUp",props:{tree:{},currentTreeNode:{}},setup(k){const m=y(),A=y(),_=k,l=p({data:_.tree,currentProject:_.currentTreeNode,treeNodeHandleClick:e=>{l.currentProject=e,i(),s()}}),x=p({filters:[{type:"input",label:"名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:g(w),click:()=>i()},{perm:!0,text:"添加",svgIcon:g(j),click:()=>C(),loading:b(()=>r.loading)}]}],defaultParams:{type:"1"}}),r=p({indexVisible:!0,dataList:[],pagination:{refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,i()}},columns:[{label:"设备",prop:"name"},{label:"类型",prop:"deviceTypeName"},{label:"创建时间",prop:"createTime",formatter:(e,t)=>E(t)}],handleSelectChange:e=>{r.selectList=e||[]}}),i=async()=>{var e;try{r.loading=!0;const o=(await R({page:r.pagination.page||1,size:r.pagination.limit||20,...((e=m.value)==null?void 0:e.queryParams)||{},type:"3"})).data.data||{};r.dataList=o.data,r.pagination.total=o.total||0}catch{n.error("查询失败")}r.loading=!1},L=p({filters:[{type:"input",label:"名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:g(w),click:()=>s()},{perm:!0,text:"移出",svgIcon:g(q),type:"danger",loading:b(()=>a.loading),click:()=>N()}]}]}),a=p({indexVisible:!0,dataList:[],pagination:{refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,s()}},columns:[{label:"名称",prop:"name"},{label:"类型",prop:"type",formatter:(e,t)=>t==="3"?"大用户":t},{label:"水流方向",prop:"direction",tag:!0,tagColor:(e,t)=>t==="1"?"#318DFF":"#f56c6c",formatter:(e,t)=>z[t]},{label:"是否核算",prop:"isAccount",formatter:(e,t)=>t==="1"?"是":"否"}],handleSelectChange:e=>{a.selectList=e||[]},operations:[{perm:!0,text:"变更方向",svgIcon:g(G),click:e=>S(e.id)}]}),S=e=>{D("确定变更方向？","提示信息").then(async()=>{try{const t=await O(e);t.data.code===200?(n.success("操作成功"),s()):n.error(t.data.message)}catch{n.error("操作失败")}})},s=async()=>{var e,t;if(!l.currentProject){a.dataList=[],a.pagination.total=0;return}try{a.loading=!0;const c=(await $({page:a.pagination.page||1,size:a.pagination.limit||20,partitionId:(e=l.currentProject)==null?void 0:e.id,...((t=m.value)==null?void 0:t.queryParams)||{},type:"3"})).data.data||{};a.dataList=c.data,a.pagination.total=c.total||0}catch{n.error("查询失败")}a.loading=!1},h=async e=>{const t=r.selectList||[];if(!t.length){n.warning("请选择要添加的设备");return}try{r.loading=!0,a.loading=!0;const o=await K(t.map(c=>{var d;return{partitionId:(d=l.currentProject)==null?void 0:d.id,deviceId:c.id,type:"3",isAccount:e}}));o.data.code===200?(i(),s()):n.error(o.data.message)}catch{n.error("添加失败")}r.loading=!1,a.loading=!1},C=async()=>{P.confirm("是否核算？","提示信息",{distinguishCancelAndClose:!0,confirmButtonText:"是",cancelButtonText:"否"}).then(()=>{console.log("confirm"),h("1")}).catch(e=>{e==="cancel"&&h("0")})},N=async()=>{const e=a.selectList||[];if(!e.length){n.warning("请选择要移出的设备");return}D("确定移出？","提示信息").then(async()=>{try{r.loading=!0,a.loading=!0;const t=await J(e.map(o=>o.id));t.data.code===200?(i(),s()):n.error(t.data.message)}catch{n.error("移出失败")}r.loading=!1,a.loading=!1}).catch(()=>{})};return I(()=>{i(),s()}),(e,t)=>{const o=F,c=V,d=H;return M(),B("div",Q,[v("div",W,[u(o,{"tree-data":f(l)},null,8,["tree-data"])]),v("div",X,[u(c,{ref_key:"refSearch_All",ref:m,class:"right-search",config:f(x)},null,8,["config"]),u(d,{class:"right-table",config:f(r)},null,8,["config"]),u(c,{ref_key:"refSearch_New",ref:A,class:"right-search",config:f(L)},null,8,["config"]),u(d,{class:"right-table",config:f(a)},null,8,["config"])])])}}}),ne=U(Y,[["__scopeId","data-v-1713b6c4"]]);export{ne as default};
