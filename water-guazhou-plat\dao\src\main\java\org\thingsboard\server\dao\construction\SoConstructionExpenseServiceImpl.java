package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionExpense;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionExpenseContainer;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionExpenseMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionExpensePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionExpenseSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionTaskInfoSaveRequest;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal.SO_CONSTRUCTION_EXPENSE_JOURNAL;
import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope.SO_CONSTRUCTION_EXPENSE;

@Service
public class SoConstructionExpenseServiceImpl extends BasicSoConstructionTaskDriveService<SoConstructionExpense> implements SoConstructionExpenseService {
    @Autowired
    private SoConstructionExpenseMapper mapper;

    @Override
    public IPage<SoConstructionExpenseContainer> findAllConditional(SoConstructionExpensePageRequest request) {
        IPage<SoConstructionExpenseContainer> result = QueryUtil.pagify(request, (i, j) -> mapper.findByPage(request), () -> mapper.countByPage(request));
        for (SoConstructionExpenseContainer record : result.getRecords()) {
            List<SoConstructionExpense> items = record.getItems();
            if (items.size() == 1 && items.get(0).getId() == null) {
                items.clear();
            }
        }
        return result;
    }

    @Override
    @Transactional
    public SoConstructionExpense save(SoConstructionExpenseSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> {
            int count = mapper.insert(e);
            if (count > 0 &&
                taskInfoService.save(SoConstructionTaskInfoSaveRequest.of(entity, SO_CONSTRUCTION_EXPENSE, e.getConstructionCode())) != null) {
                recordService.recordCreate(entity, entity.getConstructionCode(), SO_CONSTRUCTION_EXPENSE_JOURNAL);
            }
            return count;
        }, mapper::updateFully);
    }

    @Override
    public boolean isCodeExists(String code, String tenantId, String id) {
        return mapper.isCodeExists(code, tenantId, id);
    }

    @Override
    public boolean complete(String constructionCode, String userId, String tenantId) {
        boolean success = taskInfoService.markAsComplete(constructionCode, tenantId, getCurrentScope());
        if (success) {
            recordService.recordComplete(tenantId, userId, constructionCode, getCurrentJournalType());
        }
        return success;
    }

    @Override
    public boolean update(SoConstructionExpense entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean isComplete(String id) {
        return taskInfoService.isComplete(id, getCurrentScope());
    }

    @Override
    public boolean isComplete(String constructionCode, String tenantId) {
        return taskInfoService.isComplete(constructionCode, tenantId, getCurrentScope());
    }

    @Override
    public SoGeneralSystemScope getCurrentScope() {
        return SO_CONSTRUCTION_EXPENSE;
    }

    @Override
    public SoGeneralSystemJournal getCurrentJournalType() {
        return SO_CONSTRUCTION_EXPENSE_JOURNAL;
    }

    @Override
    public BaseMapper<SoConstructionExpense> getDirectMapper() {
        return mapper;
    }

}
