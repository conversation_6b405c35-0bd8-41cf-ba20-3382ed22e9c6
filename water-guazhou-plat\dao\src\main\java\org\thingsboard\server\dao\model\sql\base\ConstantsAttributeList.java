package org.thingsboard.server.dao.model.sql.base;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ConstantsAttributeList {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 键
     */
    @ApiModelProperty(value = "键")
    private String key;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private String value;

}
