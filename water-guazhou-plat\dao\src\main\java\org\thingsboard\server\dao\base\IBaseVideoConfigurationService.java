package org.thingsboard.server.dao.base;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BaseVideoConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseVideoConfigurationPageRequest;

/**
 * 平台管理-视频管理Service接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IBaseVideoConfigurationService {
    /**
     * 查询平台管理-视频管理
     *
     * @param id 平台管理-视频管理主键
     * @return 平台管理-视频管理
     */
    public BaseVideoConfiguration selectBaseVideoConfigurationById(String id);

    /**
     * 查询平台管理-视频管理列表
     *
     * @param baseVideoConfiguration 平台管理-视频管理
     * @return 平台管理-视频管理集合
     */
    public IPage<BaseVideoConfiguration> selectBaseVideoConfigurationList(BaseVideoConfigurationPageRequest baseVideoConfiguration);

    /**
     * 新增平台管理-视频管理
     *
     * @param baseVideoConfiguration 平台管理-视频管理
     * @return 结果
     */
    public int insertBaseVideoConfiguration(BaseVideoConfiguration baseVideoConfiguration);

    /**
     * 修改平台管理-视频管理
     *
     * @param baseVideoConfiguration 平台管理-视频管理
     * @return 结果
     */
    public int updateBaseVideoConfiguration(BaseVideoConfiguration baseVideoConfiguration);

    /**
     * 批量删除平台管理-视频管理
     *
     * @param ids 需要删除的平台管理-视频管理主键集合
     * @return 结果
     */
    public int deleteBaseVideoConfigurationByIds(List<String> ids);

    /**
     * 删除平台管理-视频管理信息
     *
     * @param id 平台管理-视频管理主键
     * @return 结果
     */
    public int deleteBaseVideoConfigurationById(String id);
}
