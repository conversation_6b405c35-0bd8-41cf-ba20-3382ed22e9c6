import { sortBy } from 'lodash-es'
import { getAlarmCenter, GetAlarmCountOfLevel, GetAlarmRank, GetAlarmYearReport } from '@/api/shuiwureports/zhandian'
import { useStations } from './station/useStation'
/**
 * 旧版本的报警
 */
export const useAlarms = () => {
  const curAlarmGroup = ref<{ station?: Record<string, any>; alarms: any[] }>()
  const alarms = reactive<{ total: number; data: any[] }>({
    total: 0,
    data: []
  })
  const alarmGroups = ref<Record<string, { station?: any; alarms: any[] }>>({})
  const groupAlarmsByStationId = async () => {
    try {
      const stations = useStations()
      await stations.getStations()
      alarms.data.map(item => {
        if (alarmGroups.value[item.stationId]) {
          alarmGroups.value[item.stationId].alarms.push(item)
        } else {
          const station = stations.stations.value.find(o => o.id === item.stationId)
          if (!station?.location) return
          alarmGroups.value[item.stationId] = {
            station,
            alarms: [item]
          }
        }
      })
    } catch (error) {
      //
    }
  }
  const getalarms = async (params: {
    page: number | string
    size: number | string
    alarmType?: string
    alarmLevel?: string
    processStatus?: string
    alarmStatus?: string
    stationId?: string
  }) => {
    try {
      const res = await getAlarmCenter(params)
      const data: { total: number; data: any[] } = res.data?.data || {}
      alarms.total = data.total ?? 0
      alarms.data = data.data || []
      return data
    } catch (error) {
      //
    }
  }
  const setCurAlarmGroup = (id?: string) => {
    if (!id) return undefined
    curAlarmGroup.value = alarmGroups.value[id]
  }
  return {
    alarms,
    getalarms,
    setCurAlarmGroup,
    curAlarmGroup,
    groupAlarmsByStationId,
    alarmGroups
  }
}
export const useAlarmRank = () => {
  const rank = ref<{ sort: number; label: string; count: number; unit: string; percentage: number }[]>([])
  const getRank = async (params?: { startTime: number; endTime: number }) => {
    try {
      const res = await GetAlarmRank({
        startTime: params?.startTime ?? moment().subtract(1, 'y').valueOf(),
        endTime: params?.endTime ?? moment().valueOf()
      })
      rank.value = sortBy(
        res.data?.data?.map(item => {
          return {
            sort: 0,
            count: item.count,
            unit: '个',
            label: item.key
          }
        }) ?? [],
        o => {
          return o.count
        }
      )?.reverse()
      const total = rank.value.reduce((prev, cur) => {
        return cur.count + prev
      }, 0)
      rank.value.map((item, i) => {
        item.sort = i + 1
        item.percentage = (total === 0 ? 0 : item.count / total) * 100
      })
    } catch (error) {
      console.log(error)
    }
  }
  return {
    rank,
    getRank
  }
}
export const useAlarmLevel = () => {
  const level = ref<{ level1: number; level2: number; level3: number }>({
    level1: 0,
    level2: 0,
    level3: 0
  })
  const getLevel = async (params?: { startTime: number; endTime: number }) => {
    try {
      const res = await GetAlarmCountOfLevel({
        startTime: params?.startTime ?? moment().subtract(1, 'y').valueOf(),
        endTime: params?.endTime ?? moment().valueOf()
      })
      level.value = {
        ...level.value,
        ...(res.data?.data ?? {})
      }
    } catch (error) {
      console.log(error)
    }
  }
  return {
    level,
    getLevel
  }
}
export const useAlarmMonthlyCount = () => {
  const counts = ref<{ month: string; total: number; end: number; unProcess: number; processRate: number }[]>([])
  const getCounts = async () => {
    try {
      const res = await GetAlarmYearReport()
      counts.value = res.data?.data ?? []
    } catch (error) {
      console.log(error)
    }
  }
  return {
    counts,
    getCounts
  }
}

export const alarmLevel = [
  { label: '提醒报警', value: '1' },
  { label: '重要报警', value: '2' },
  { label: '紧急报警', value: '3' }
]
export const alarmLevelObj = {
  1: '提醒报警',
  2: '重要报警',
  3: '紧急报警'
}
export const formatAlarmLevel = (level: string) => {
  return alarmLevelObj[level]
}
export const alarmType = [
  { label: '液位异常', value: '1' },
  { label: '水质异常', value: '2' },
  { label: '设备故障', value: '3' },
  { label: '通讯异常', value: '4' },
  { label: '流量异常', value: '5' },
  { label: '控制异常', value: '6' },
  { label: '设备健康', value: '7' },
  { label: '其他', value: '8' }
]
export const alarmTypeObj = {
  1: '液位异常',
  2: '水质异常',
  3: '设备故障',
  4: '通讯异常',
  5: '流量异常',
  6: '控制异常',
  7: '设备健康',
  8: '其他'
}
export const formatAlarmType = (type: string) => {
  return alarmTypeObj[type]
}

export const alarmStatus = [
  { label: '报警中', value: '1' },
  { label: '已恢复', value: '2' },
  { label: '已解除', value: '3' }
]
export const alarmStatusObj = {
  1: '报警中',
  2: '已恢复',
  3: '已解除'
}
export const formatAlarmStatus = (status: string) => {
  return alarmStatusObj[status]
}
export const processStatus = [
  { label: '未处理', value: '1' },
  { label: '处理中', value: '2' },
  { label: '已处理', value: '3' }
]
export const processStatusObj = {
  1: '未处理',
  2: '处理中',
  3: '已处理'
}
export const formatProcessStatus = status => {
  return processStatusObj[status]
}
