import{_ as J}from"./TreeBox-DDD2iwoR.js";import{d as O,c as k,r as v,a0 as Q,x as b,a9 as X,o as Y,ay as I,g as N,h as T,F as s,p as m,q as r,i,G as Z,cA as ee,cy as te,an as oe,bV as re,J as ae,H as le,bU as ne,bW as se,cE as ie,b6 as de,C as ce}from"./index-r0dFAfgr.js";import{_ as ue}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as pe}from"./CardTable-rdWOL4_6.js";import{_ as fe}from"./index-BJ-QPYom.js";import{a as B,C as E}from"./GeneralProcessing-CQ8i9ijT.js";import me from"./sort-CFejUh8Q.js";import{j as ge,i as _e,k as he,s as M}from"./index-D9ERhRP6.js";import"./index-C9hz-UZb.js";const ve={class:"tree-box"},be={style:{height:"100%",position:"relative"}},ye={class:"buttons"},xe=O({__name:"VideoAccess",setup(ke){const A=[{label:"自建",value:"1"},{label:"天网",value:"2"},{label:"利旧",value:"3"}];k("1");const q=k(!1),w=k(),p=k(),h=k(),n=v({projectId:"",videoLeft:[],videoRight:[],leftName:"",rightName:"",search:"1"}),g=v({title:"分组管理",isFilterTree:!0,data:[],extraFilters:[{type:"select-tree",field:"projectId",clearable:!1,checkStrictly:!0,options:Q().projectList,onChange:e=>{n.projectId=e,C(),L()}}],treeNodeHandleClick:e=>{g.currentProject=e,j()}}),_=v({title:" ",defaultExpandAll:!0,indexVisible:!0,rowKey:"id",handleSelectChange:e=>{n.videoRight=e},selectList:[],columns:[{label:"名称",prop:"name",minWidth:"400px"}],dataList:[],pagination:{hide:!0}}),y=v({title:" ",defaultExpandAll:!0,indexVisible:!0,handleSelectChange:e=>{n.videoLeft=e},selectList:[],rowKey:"id",columns:[{label:"名称",prop:"name",minWidth:"250px"}],operationWidth:70,operations:[{text:"编辑",perm:!0,click:e=>{var o,a;const t=((o=e.coordinate)==null?void 0:o.split(","))||["",""];V.defaultValue={...e,lng:t[1]||"",lat:t[0]||""},(a=p.value)==null||a.openDialog(),setTimeout(()=>{e.coordinate&&F(e.coordinate)},0)}}],dataList:[],pagination:{hide:!0}}),V=v({title:"编辑",dialogWidth:"1000px",labelWidth:"110px",submitting:!1,submit:e=>{e.lng&&e.lat&&(e.coordinate=`${e.lat},${e.lng}`)},defaultValue:{},group:[{fields:[{xs:24,type:"input",label:"摄像头名称",field:"name",rules:[{required:!0,message:"请输入摄像头名称"}]},{xs:24,type:"input",label:"编号",field:"serialNumber",rules:[{required:!0,message:"请输入编号"}]},{xs:24,type:"select",label:"类型",field:"videoType",options:A,rules:[{required:!0,message:"请输入类型"}]},{type:"input",label:"经度",field:"lng",onBlur:()=>{var t,o,a,l,d;const e=(o=(t=p.value)==null?void 0:t.refForm)==null?void 0:o.dataForm;if(e.lat&&e.lng){if((l=(a=p.value)==null?void 0:a.refForm)!=null&&l.dataForm){const u=[parseFloat(e.lat),parseFloat(e.lng)];p.value.refForm.dataForm.coordinate=u;const f=u.join(",");F(f),e&&(e.coordinate=f,(d=h.value)==null||d.getMarkerAddress().then(x=>{e.address=x}))}}else b.warning("请先输入经纬度")},rules:[{required:!0,message:"请输入纬度"}]},{type:"input",label:"纬度",field:"lat",onBlur:()=>{var t,o,a,l,d;const e=(o=(t=p.value)==null?void 0:t.refForm)==null?void 0:o.dataForm;if(e.lat&&e.lng){if((l=(a=p.value)==null?void 0:a.refForm)!=null&&l.dataForm){const u=[parseFloat(e.lat),parseFloat(e.lng)];p.value.refForm.dataForm.coordinate=u;const f=u.join(",");F(f),e&&(e.coordinate=f,(d=h.value)==null||d.getMarkerAddress().then(x=>{e.address=x}))}}else b.warning("请先输入经纬度")},rules:[{required:!0,message:"请输入纬度"}]},{type:"form-map",showInput:!0,field:"coordinate"}]}]}),F=e=>{var o,a;const t=e.split(",").map(l=>parseFloat(l));(o=h.value)==null||o.setMarker(t),(a=h.value)==null||a.zoomTo()},D=e=>{var a,l,d;const t=[e.latlng.lat,e.latlng.lng].join(","),o=(l=(a=p.value)==null?void 0:a.refForm)==null?void 0:l.dataForm;o&&(o.coordinate=t,o.lat=e.latlng.lat,o.lng=e.latlng.lng,(d=h.value)==null||d.getMarkerAddress().then(u=>{o.address=u}))};function P(e){if(q.value=!1,e){if(!g.currentProject){b.warning("请选择分组");return}const t=[...y.dataList,...n.videoRight],o={projectId:n.projectId,groupId:g.currentProject.id,videoList:t.map((a,l)=>({...a,orderNum:l}))};if(o.videoList.length===0){b.warning("请选择要添加的摄像头");return}E(o,M,{},"添加成功").then(()=>{C(),j(),_.selectList=[],y.selectList=[],L()})}else{const t=n.videoLeft,o={projectId:n.projectId,groupId:"",videoList:t.map((a,l)=>({...a,orderNum:l}))};if(o.videoList.length===0){b.warning("请选择要移除的摄像头");return}E(o,M,{},"移除成功").then(()=>{C(),j(),_.selectList=[],y.selectList=[],L()})}}const C=()=>{_.loading=!0,ge(n.projectId,{isBind:0}).then(e=>{_.dataList=e.data||[],_.loading=!1}).catch(e=>{_.loading=!1,b.warning(e)})},L=()=>{const e=n.projectId;B({projectId:e},_e).then(t=>{g.data=X(t.data,"children")})},W=v({title:"排序",group:[],modalClass:"lightColor",width:"80%"}),$=()=>{var e;(e=w.value)==null||e.openDrawer()},j=async()=>{n.videoLeft=[];const e={groupId:g.currentProject.id,projectId:n.projectId||"",name:n.leftName};B(e,he,{table:y})};return Y(()=>{}),(e,t)=>{const o=fe,a=re,l=ae,d=le,u=pe,f=ne,x=se,S=ie,R=I("SuperPointPicker"),G=I("SuperMapContainer"),U=I("SuperMapProvider"),H=ue,K=de,z=J;return N(),T(z,null,{tree:s(()=>[m("div",ve,[r(a,null,{default:s(()=>[r(o,{style:{width:"calc(100% - 5px)"},ref:"refTree","tree-data":i(g)},null,8,["tree-data"])]),_:1})])]),default:s(()=>[m("div",be,[r(x,{gutter:60,style:{height:"100%"}},{default:s(()=>[r(f,{span:12,style:{height:"100%"}},{default:s(()=>[r(u,{style:{height:"100%"},config:i(y),title:"已接入摄像头"},{title:s(()=>[t[5]||(t[5]=m("div",{class:"card-table-title"},[m("div",null,"分组下摄像头")],-1)),r(l,{type:"success",style:{"margin-right":"10px"},onClick:$},{default:s(()=>t[4]||(t[4]=[Z("重新排序")])),_:1}),r(d,{modelValue:i(n).leftName,"onUpdate:modelValue":t[0]||(t[0]=c=>i(n).leftName=c),placeholder:"请输入关键字",style:{width:"300px","margin-right":"10px"},onBlur:j},null,8,["modelValue"])]),_:1},8,["config"])]),_:1}),r(f,{span:12,style:{height:"100%"}},{default:s(()=>[r(u,{style:{height:"100%"},config:i(_)},{title:s(()=>[t[6]||(t[6]=m("div",{class:"card-table-title"},[m("div",null,"待接入摄像头")],-1)),r(d,{modelValue:i(n).rightName,"onUpdate:modelValue":t[1]||(t[1]=c=>i(n).rightName=c),placeholder:"请输入关键字",style:{width:"300px","margin-right":"10px"},onBlur:C},null,8,["modelValue"])]),_:1},8,["config"])]),_:1})]),_:1}),m("div",ye,[r(l,{onClick:t[2]||(t[2]=c=>P(!1))},{default:s(()=>[r(S,null,{default:s(()=>[r(i(ee))]),_:1})]),_:1}),t[7]||(t[7]=m("br",null,null,-1)),r(l,{onClick:t[3]||(t[3]=c=>P(!0))},{default:s(()=>[r(S,null,{default:s(()=>[r(i(te))]),_:1})]),_:1})])]),r(H,{ref_key:"refForm",ref:p,config:i(V)},{fieldSlot:s(({config:c,row:Ce})=>[c.field==="coordinate"?(N(),T(U,{key:0,style:{width:"100%",height:"300px"}},{default:s(()=>[r(G,{"default-mouse-status":"crosshair","hide-control":!0,"hide-legend":!0},{default:s(()=>[r(R,{disabled:!!c.readonly,ref_key:"refPointPicker",ref:h,onPick:D},null,8,["disabled"])]),_:2},1024)]),_:2},1024)):oe("",!0)]),_:1},8,["config"]),r(K,{ref_key:"refSort",ref:w,config:i(W)},{default:s(()=>{var c;return[r(me,{groupId:((c=i(g).currentProject)==null?void 0:c.id)||"",projectId:i(n).projectId||""},null,8,["groupId","projectId"])]}),_:1},8,["config"])]),_:1})}}}),Te=ce(xe,[["__scopeId","data-v-f20e1ef7"]]);export{Te as default};
