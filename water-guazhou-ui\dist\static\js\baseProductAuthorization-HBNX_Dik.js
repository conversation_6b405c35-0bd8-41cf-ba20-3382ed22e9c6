import{_ as w}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as P}from"./CardSearch-CB_HNR-Q.js";import{z as u,C as k,c as b,r as p,b as l,S as v,o as B,g as T,n as A,q as f}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function L(i){return u({url:"/api/base/product/authorization/list",method:"get",params:i})}function q(i){return u({url:"/api/base/product/authorization/getDetail",method:"get",params:{id:i}})}function _(i){return u({url:"/api/base/product/authorization/add",method:"post",data:i})}function y(i){return u({url:"/api/base/product/authorization/edit",method:"post",data:i})}function V(i){return u({url:"/api/base/product/authorization/deleteIds",method:"delete",data:i})}const F={class:"wrapper"},E={__name:"baseProductAuthorization",setup(i){const g=b(),c=b(),x=p({labelWidth:"100px",filters:[{type:"input",label:"产品名称",field:"name",placeholder:"请输入产品名称",onChange:()=>s()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>s()},{perm:!0,type:"primary",text:"新增",click:()=>m()},{perm:!0,type:"danger",text:"批量删除",click:()=>h()}]}],defaultParams:{}}),n=p({columns:[{label:"产品名称",prop:"name"},{label:"产品类型",prop:"type"},{label:"产品描述",prop:"description"},{label:"是否开启",prop:"enabled",render:e=>e.enabled==="true"?"是":"否"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>z(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>m(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>h(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{n.pagination.page=e,s()},handleSize:e=>{n.pagination.limit=e,s()}},handleSelectChange:e=>{n.selectList=e||[]}}),a=p({title:"新增产品授权",group:[{fields:[{type:"input",label:"产品名称",field:"name",rules:[{required:!0,message:"请输入产品名称"}]},{type:"input",label:"产品类型",field:"type",rules:[{required:!0,message:"请输入产品类型"}]},{type:"textarea",label:"产品描述",field:"description",placeholder:"请输入产品描述"},{type:"textarea",label:"路由配置",field:"routeConfig",placeholder:"请输入路由配置"},{type:"textarea",label:"配置信息",field:"config",placeholder:"请输入配置信息"},{type:"select",label:"是否开启",field:"enabled",options:[{label:"是",value:"true"},{label:"否",value:"false"}],rules:[{required:!0,message:"请选择是否开启"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var t;try{e.id?(await y(e),l.success("修改成功")):(await _(e),l.success("新增成功")),(t=c.value)==null||t.closeDialog(),s()}catch{l.error("操作失败")}}}),D=()=>{a.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),a.showSubmit=!0,a.showCancel=!0,a.cancelText="取消",a.submitText="确定",a.submit=async e=>{var t;try{e.id?(await y(e),l.success("修改成功")):(await _(e),l.success("新增成功")),(t=c.value)==null||t.closeDialog(),s()}catch{l.error("操作失败")}}},s=async()=>{var e,t;try{const r=await L({page:n.pagination.page,size:n.pagination.limit,...((e=g.value)==null?void 0:e.queryParams)||{}}),o=((t=r.data)==null?void 0:t.data)||r;n.dataList=o.records||o,n.pagination.total=o.total||o.length||0}catch{l.error("数据加载失败")}},m=e=>{var t;e?(a.title="编辑产品授权",a.defaultValue={...e}):(a.title="新增产品授权",a.defaultValue={}),(t=c.value)==null||t.openDialog()},z=async e=>{var t,r;try{const o=await q(e.id),d=((t=o.data)==null?void 0:t.data)||o;D(),a.title="产品授权详情",a.defaultValue={...d},a.group[0].fields.forEach(C=>{C.disabled=!0}),a.showSubmit=!1,a.cancelText="关闭",(r=c.value)==null||r.openDialog()}catch{l.error("获取详情失败")}},h=e=>{v("确定删除？","删除提示").then(async()=>{var r;const t=e?[e.id]:((r=n.selectList)==null?void 0:r.map(o=>o.id))||[];if(!t.length){l.warning("请选择要删除的数据");return}await V(t),l.success("删除成功"),s()}).catch(()=>{})};return B(()=>{s()}),(e,t)=>{const r=P,o=S,d=w;return T(),A("div",F,[f(r,{ref_key:"refSearch",ref:g,config:x},null,8,["config"]),f(o,{class:"card-table",config:n},null,8,["config"]),f(d,{ref_key:"refDialogForm",ref:c,config:a},null,8,["config"])])}}},G=k(E,[["__scopeId","data-v-a2854fe6"]]);export{G as default};
