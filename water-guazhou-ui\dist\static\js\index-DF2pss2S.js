import{_ as ne}from"./index-C9hz-UZb.js";import{d as se,r as O,bF as r,c as M,am as R,bB as Y,a8 as re,bX as ie,s as W,b as N,o as le,ah as ce,bA as de,ay as ue,g as k,n as $,q as S,i as p,F as C,cs as H,bo as J,bR as G,p as h,aB as U,aJ as X,h as K,G as Q,bh as Z,j as pe,dF as me,dA as fe,J as ye,al as he,aj as ve,bD as _e,C as be}from"./index-r0dFAfgr.js";import{_ as ge}from"./CardTable-rdWOL4_6.js";import{_ as De}from"./CardSearch-CB_HNR-Q.js";import{b as ee}from"./statisticalAnalysis-D5JxC4wJ.js";import{u as Se}from"./useStation-DJgnSZIA.js";import{p as xe}from"./printUtils-C-AxhDcd.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const Ce={class:"wrapper"},Le={class:"content-container"},Ye={class:"list-layout"},we={class:"table-container"},ke={class:"date-selector"},Te={class:"date-buttons"},Pe={class:"content-container"},Oe={class:"chart-layout"},Ne={class:"chart-container"},ze={class:"date-selector"},Ie={class:"date-buttons"},Me=se({__name:"index",setup(We){const{getStationTree:te}=Se(),a=O({type:"date",treeDataType:"Station",stationId:"",sumsRow:{},title:"",activeName:"list",chartOption:null,availableDates:[],selectedDate:"",chartData:null}),q=r().date(),B=M(),L=M(),T=M();R(()=>a.activeName,()=>{a.activeName==="echarts"&&Y(()=>{setTimeout(()=>{I()},100)})}),R(()=>{var e,t;return(t=(e=L.value)==null?void 0:e.queryParams)==null?void 0:t.type},e=>{var t;e&&((t=f.currentProject)!=null&&t.id)&&Y(()=>{P()})});const f=O({data:[],currentProject:{}}),z=O({defaultParams:{type:"day",year:[r().format(),r().format()],month:[r().format(),r().format()],day:[r().date(q-6).format("YYYY-MM-DD"),r().date(q).format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:re(()=>f.data),label:"站点选择",onChange:e=>{var o;const t=ie(f.data,"children","id",e);t&&t.id&&(f.currentProject=t,a.treeDataType=((o=t.data)==null?void 0:o.type)||"Station",a.stationId=t.id,Y(()=>{P()}))}},{type:"radio-button",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型"},{hidden:!0,type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(e,t,o)=>{o.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(e,t,o)=>{o.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(e,t,o)=>{o.hidden=e.type==="month"||e.type==="day"}},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>P(),svgIcon:W(he)},{text:"导出",perm:!0,type:"warning",svgIcon:W(ve),click:()=>ae()},{perm:!0,text:"打印",type:"success",svgIcon:W(_e),click:()=>oe()}]}]}),v=O({loading:!1,dataList:[],columns:[],operations:[],operationWidth:"150px",pagination:{hide:!0}}),P=async()=>{var t;if(!a.stationId){N.warning("请先选择站点");return}v.loading=!0;const e=(t=L.value)==null?void 0:t.queryParams;if(!e){v.loading=!1;return}try{const o=e[e.type],m={stationId:a.stationId,queryType:e.type,start:r(o[0]).startOf(e.type==="day"?"date":e.type).valueOf(),end:r(o[1]).endOf(e.type==="day"?"date":e.type).valueOf()};ee(m).then(u=>{const b=u.data.data,g=b.flowList||[],x=b.energyList||[],y=b.unitConsumption||[],i=[];for(let n=0;n<g.length;n++)e.type==="day"?i.push(n+"时"):e.type==="month"?i.push(n+1+"日"):i.push(n+1+"月");const D=[{prop:"time",label:"时间",minWidth:120,align:"center"},{prop:"unitConsumption",label:"吨水电耗",unit:"(kWh/m³)",minWidth:180,align:"center"},{prop:"totalFlow",label:"取水量",unit:"(m³)",minWidth:180,align:"center"},{prop:"energy",label:"用电量",unit:"(kW)",minWidth:180,align:"center"}];v.columns=D;const l=i.map((n,c)=>{var d,s,w;return{time:n,unitConsumption:((d=y[c])==null?void 0:d.value)||0,totalFlow:((s=g[c])==null?void 0:s.value)||0,energy:((w=x[c])==null?void 0:w.value)||0}});v.dataList=l,v.loading=!1,a.chartData=b;const _=e[e.type];if(e.type==="day"){const n=r(_[0]),c=r(_[1]),d=[];let s=n;for(;s.isBefore(c)||s.isSame(c);)d.push(s.format("YYYY-MM-DD")),s=s.add(1,"day");a.availableDates=d}else if(e.type==="month"){const n=r(_[0]),c=r(_[1]),d=[];let s=n;for(;s.isBefore(c)||s.isSame(c);)d.push(s.format("YYYY-MM")),s=s.add(1,"month");a.availableDates=d}else{const n=r(_[0]),c=r(_[1]),d=[];let s=n;for(;s.isBefore(c)||s.isSame(c);)d.push(s.format("YYYY")),s=s.add(1,"year");a.availableDates=d}a.selectedDate=a.availableDates[0]||"",a.activeName==="echarts"&&Y(()=>{setTimeout(()=>{a.availableDates.length>1?j(a.selectedDate):I()},200)})}).catch(u=>{console.error("获取数据失败:",u),v.loading=!1,N.error("获取数据失败，请稍后重试")})}catch(o){console.error("获取数据失败:",o),v.loading=!1,N.error("获取数据失败")}},j=e=>{var g,x;const t=(g=f.currentProject)==null?void 0:g.id,o=((x=L.value)==null?void 0:x.queryParams)||{};if(!t||!e)return;let m,u;o.type==="day"?(m=r(e).startOf("day").valueOf(),u=r(e).endOf("day").valueOf()):o.type==="month"?(m=r(e).startOf("month").valueOf(),u=r(e).endOf("month").valueOf()):(m=r(e).startOf("year").valueOf(),u=r(e).endOf("year").valueOf());const b={stationId:t,queryType:o.type,start:m,end:u};ee(b).then(y=>{const i=y.data.data;a.chartData=i;const D=i.flowList||[],l=i.energyList||[],_=i.unitConsumption||[],n=[];for(let d=0;d<D.length;d++)o.type==="day"?n.push(d+"时"):o.type==="month"?n.push(d+1+"日"):n.push(d+1+"月");const c=n.map((d,s)=>{var w,F,V;return{time:d,unitConsumption:((w=_[s])==null?void 0:w.value)||0,totalFlow:((F=D[s])==null?void 0:F.value)||0,energy:((V=l[s])==null?void 0:V.value)||0}});v.dataList=c,a.activeName==="echarts"&&I()}).catch(y=>{console.error("获取选中日期数据失败:",y),N.error("获取数据失败，请稍后重试")})},A=e=>{a.selectedDate=e,j(e)},E=e=>{const t=e.split("-");return t.length===3?`${t[1]}-${t[2]}`:e},I=()=>{var D;if(!a.chartData)return;const e=a.chartData,o=(((D=L.value)==null?void 0:D.queryParams)||{}).type||"day",m=e.flowList||[],u=[];for(let l=0;l<m.length;l++)o==="day"?u.push(l+"时"):o==="month"?u.push(l+1+"日"):u.push(l+1+"月");const b=(e.unitConsumption||[]).map(l=>l.value||0),g=(e.flowList||[]).map(l=>l.value||0),x=(e.energyList||[]).map(l=>l.value||0);let y="";o==="day"?y=`${r(a.selectedDate).format("M月D日")} ${f.currentProject.label||""}生产图`:o==="month"?y=`${r(a.selectedDate).format("YYYY年M月")} ${f.currentProject.label||""}生产图`:y=`${r(a.selectedDate).format("YYYY年")} ${f.currentProject.label||""}生产图`;const i={title:{text:y,left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},formatter:function(l){let _=`${l[0].axisValue}<br/>`;return l.forEach(n=>{let c="";n.seriesName.includes("吨水电耗")?c="kWh/m³":n.seriesName.includes("取水量")?c="m³":n.seriesName.includes("用电量")&&(c="kW"),_+=`${n.marker} ${n.seriesName}: ${n.value} ${c}<br/>`}),_}},legend:{top:30,data:["吨水电耗","取水量","用电量"],textStyle:{fontSize:12}},grid:{left:"8%",right:"8%",bottom:"10%",top:"20%",containLabel:!0},xAxis:{type:"category",data:u,name:o==="day"?"时间":o==="month"?"日期":"月份",nameTextStyle:{fontSize:12},axisLabel:{fontSize:11,rotate:0}},yAxis:[{type:"value",name:"取水量(m³)/用电量(kW)",position:"left",nameTextStyle:{fontSize:12},axisLabel:{fontSize:11,formatter:"{value}"}},{type:"value",name:"吨水电耗(kWh/m³)",position:"right",nameTextStyle:{fontSize:12},axisLabel:{fontSize:11,formatter:"{value}"}}],series:[{name:"取水量",type:"bar",yAxisIndex:0,data:g,itemStyle:{color:"#91CC75"},barWidth:"20%"},{name:"用电量",type:"bar",yAxisIndex:0,data:x,itemStyle:{color:"#C0C0C0"},barWidth:"20%"},{name:"吨水电耗",type:"line",yAxisIndex:1,data:b,smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:3,color:"#5CB85C"},itemStyle:{color:"#5CB85C"}}]};a.chartOption=i,Y(()=>{setTimeout(()=>{var l;(l=T.value)==null||l.resize()},200)})},ae=()=>{var e;(e=B.value)==null||e.exportTable()},oe=()=>{xe({title:a.title,data:v.dataList,titleList:v.columns})};return le(async()=>{var o,m;const e=await te("水厂");f.data=e;const t=ce(f.data);t&&t.id&&(f.currentProject=t,a.treeDataType=((o=t.data)==null?void 0:o.type)||"Station",a.stationId=t.id),z.defaultParams={...z.defaultParams,treeData:f.currentProject},(m=L.value)==null||m.resetForm(),Y(()=>{setTimeout(()=>{P()},100)}),window.addEventListener("resize",()=>{setTimeout(()=>{var u;(u=T.value)==null||u.resize()},100)})}),de(()=>{window.removeEventListener("resize",()=>{var e;(e=T.value)==null||e.resize()})}),(e,t)=>{const o=De,m=me,u=fe,b=ge,g=ye,x=ue("VChart"),y=ne;return k(),$("div",Ce,[S(o,{ref_key:"cardSearch",ref:L,config:p(z)},null,8,["config"]),S(y,{class:"card",title:p(a).activeName==="list"?"吨水电耗分析":"吨水电耗图表"},{query:C(()=>[S(u,{modelValue:p(a).activeName,"onUpdate:modelValue":t[0]||(t[0]=i=>p(a).activeName=i)},{default:C(()=>[S(m,{label:"echarts"},{default:C(()=>[S(p(H),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),S(m,{label:"list"},{default:C(()=>[S(p(H),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:C(()=>[J(h("div",Le,[h("div",Ye,[h("div",we,[S(b,{id:"print",ref_key:"refTable",ref:B,class:"card-table",config:p(v)},null,8,["config"])]),h("div",ke,[t[1]||(t[1]=h("div",{class:"selector-title"},"选择日期",-1)),h("div",Te,[(k(!0),$(U,null,X(p(a).availableDates,i=>(k(),K(g,{key:i,type:p(a).selectedDate===i?"primary":"default",size:"small",onClick:D=>A(i),class:"date-btn"},{default:C(()=>[Q(Z(E(i)),1)]),_:2},1032,["type","onClick"]))),128))])])])],512),[[G,p(a).activeName==="list"]]),J(h("div",Pe,[h("div",Oe,[h("div",Ne,[S(x,{ref_key:"refChart",ref:T,theme:p(pe)().isDark?"dark":"light",option:p(a).chartOption,class:"line-chart"},null,8,["theme","option"])]),h("div",ze,[t[2]||(t[2]=h("div",{class:"selector-title"},"选择日期",-1)),h("div",Ie,[(k(!0),$(U,null,X(p(a).availableDates,i=>(k(),K(g,{key:i,type:p(a).selectedDate===i?"primary":"default",size:"small",onClick:D=>A(i),class:"date-btn"},{default:C(()=>[Q(Z(E(i)),1)]),_:2},1032,["type","onClick"]))),128))])])])],512),[[G,p(a).activeName==="echarts"]])]),_:1},8,["title"])])}}}),He=be(Me,[["__scopeId","data-v-8170f61e"]]);export{He as default};
