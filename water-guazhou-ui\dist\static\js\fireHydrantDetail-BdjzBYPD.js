import{d as P,cN as S,r as U,c as M,o as $,ay as j,g as s,n as c,p as h,q as G,F as I,aB as w,aJ as N,h as q,G as J,bh as f,i as l,bo as O,ab as K,bF as T,dz as Q,dA as W,br as X,C as Y}from"./index-r0dFAfgr.js";import{h as Z}from"./chart-wy3NEK2T.js";import{f as aa,d as ta}from"./zhandian-YaGuQZe6.js";import{b as ea}from"./monitoringOverview-DvKhtmcR.js";import{g as u}from"./echarts-Bhn8T7lM.js";import{u as oa}from"./useDetector-BRcb7GRN.js";const ia={class:"one-map-detail"},ra={class:"row2"},na={class:"detail-attrgrou-radio"},sa={class:"detail-right"},la={class:"list-items overlay-y"},da={class:"item-label"},ca={class:"item-content"},ua={class:"chart-box"},ma=P({__name:"fireHydrantDetail",emits:["refresh","mounted"],setup(pa,{expose:V,emit:F}){const v=F,{proxy:g}=S(),a=U({curRadio:"",radioGroup:[],pieChart1:u(0,{max:100,title:"累计流量(m³)"}),pieChart2:u(0,{max:100,title:"瞬时流量(m³/h)"}),pieChart3:u(0,{max:1e3,title:"压力(bar)"}),lineChartOption:null,stationRealTimeData:[],detailLoading:!1}),k=async r=>{v("refresh",{title:r.name}),a.curRow=r,Array.from({length:4}).map((t,o)=>{var d;(d=g.$refs["refChart"+(o+1)])==null||d.resize()}),a.detailLoading=!0;try{const t=e=>{const n=K(e);return{value:+n.value.toFixed(2),unit:n.unit}},o=ea({stationId:r.stationId,start:T().startOf("day").valueOf(),end:T().endOf("day").valueOf()}).then(e=>{var C,x,b,R,D,L;const n=((C=e.data.data.total_flow)==null?void 0:C.map(p=>p.value))||[],i=((x=e.data.data.Instantaneous_flow)==null?void 0:x.map(p=>p.value))||[],m=((b=e.data.data.pressure)==null?void 0:b.map(p=>p.value))||[];a.lineChartOption=Z({line1:{data:n,unit:"m³",name:"累计流量"},line2:{data:i,unit:"m³/h",name:"瞬时流量"},line3:{data:m,unit:"bar",name:"压力"}});const _=t(((R=e.data.data)==null?void 0:R.currentTurbidity)||0),H=t(((D=e.data.data)==null?void 0:D.currentRemainder)||0),E=t(((L=e.data.data)==null?void 0:L.currentPH)||0);a.pieChart1=u(_.value,{max:100,title:"浊度("+(_.unit||"")+"NTU)"}),a.pieChart2=u(H.value,{max:100,title:"余氯("+(_.unit||"")+"mg/L)"}),a.pieChart3=u(E.value,{max:1e3,title:"PH("+(_.unit||"")+"pH)"})}),d=aa({stationId:r.stationId}).then(e=>{a.radioGroup=e.data||[],a.curRadio=a.radioGroup[0],y(a.radioGroup[0])});Promise.all([o,d]).finally(()=>{a.detailLoading=!1})}catch{a.detailLoading=!1}},y=async r=>{var o;const t=await ta((o=a.curRow)==null?void 0:o.stationId,r);a.stationRealTimeData=t.data||[]};V({refreshDetail:k});const z=()=>{Array.from({length:3}).map((r,t)=>{var o;(o=g.$refs["refChart"+(t+1)])==null||o.resize()})},A=oa(),B=M();return $(()=>{v("mounted"),A.listenToMush(B.value,z)}),(r,t)=>{const o=Q,d=W,e=j("VChart"),n=X;return s(),c("div",ia,[h("div",ra,[h("div",na,[G(d,{modelValue:l(a).curRadio,"onUpdate:modelValue":t[0]||(t[0]=i=>l(a).curRadio=i),onChange:y},{default:I(()=>[(s(!0),c(w,null,N(l(a).radioGroup,(i,m)=>(s(),q(o,{key:m,label:i},{default:I(()=>[J(f(i),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),h("div",sa,[O((s(),c("div",la,[(s(!0),c(w,null,N(l(a).stationRealTimeData,(i,m)=>(s(),c("div",{key:m,class:"list-item"},[h("div",da,f(i.propertyName),1),h("div",ca,f(i.value||"--")+" "+f(i.unit),1)]))),128))])),[[n,l(a).detailLoading]]),O((s(),c("div",ua,[G(e,{ref:"refChart4",option:l(a).lineChartOption},null,8,["option"])])),[[n,l(a).detailLoading]])])])])}}}),Ca=Y(ma,[["__scopeId","data-v-997de85c"]]);export{Ca as default};
