/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.role;

import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.RoleId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.role.UserRole;
import org.thingsboard.server.dao.Dao;

import java.util.List;

public interface UserRoleDao extends Dao<UserRole> {
    void deleteByUserId(UserId userId);

    List<UserRole> findByRole(RoleId roleId);

    /**
     * 查询指定用户的角色
     * @param userId
     * @return
     */
    List<String> getRoleIdByUserId(UserId userId);

    void deleteByUserId(CustomerId customerId);
}
