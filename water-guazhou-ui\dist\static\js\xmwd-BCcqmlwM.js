import{_ as i}from"./CardTable-rdWOL4_6.js";import{g as r}from"./projectManagement-CDcrrCQ1.js";import{d as l,r as p,o as s,g as c,h as d,i as m}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";const h=l({__name:"xmwd",setup(_){const a=p({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"文件名称",prop:"code"},{label:"文件类型",prop:"code"},{label:"文件大小",prop:"code"},{label:"上传时间",prop:"code"},{label:"上传人",prop:"code"}],dataList:[],pagination:{hide:!0}}),o=async()=>{const t={size:a.pagination.limit||20,page:a.pagination.page||1};t!=null&&t.time&&(t.startTimeFrom=t.time[0],t.startTimeTo=t.time[1],delete t.time),r(t).then(e=>{a.dataList=e.data.data.data||[],a.pagination.total=e.data.data.total||0})};return s(()=>{o()}),(t,e)=>{const n=i;return c(),d(n,{config:m(a),class:"card-table"},null,8,["config"])}}});export{h as default};
