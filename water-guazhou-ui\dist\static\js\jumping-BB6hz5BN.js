import{d,b3 as l,r as p,o as u,bo as m,i as g,g as _,n as f,dY as y,u as t,a0 as S,dZ as w,br as b,C as h}from"./index-r0dFAfgr.js";import{A as n}from"./AES-CssdJXyB.js";const k={class:"jumping-modal"},v=d({__name:"jumping",setup(A){const r=l(),s=p({loading:!0}),c=async()=>{s.loading=!1;const e=await y(),a={username:n.encrypt("<EMAIL>",e.data.key),password:n.encrypt("Aa123456",e.data.key)};await t().Login(a);const o=t().roles;r.push({path:S().usePortal?o[0]==="SYS_ADMIN"?"/":"/app":"/"}).finally(()=>{s.loading=!1}),w({strategy:"local",username:a.username,password:a.password}).then(i=>{t().ToggleScadaToken(i.data.accessToken)}),localStorage.setItem("ysinfo",JSON.stringify({u:a.username,p:a.password}))};return u(()=>{c()}),(e,a)=>{const o=b;return m((_(),f("div",k,null,512)),[[o,g(s).loading]])}}}),B=h(v,[["__scopeId","data-v-cb795e40"]]);export{B as default};
