package org.thingsboard.server.controller.base;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.datavVO.PieVO;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.project.ProjectTreeVO;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.security.model.SecurityUser;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("api/project")
public class ProjectController extends BaseController {

    @Autowired
    private ProjectRelationService projectRelationService;

    @GetMapping("{id}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_PROJECT_GET)
    public ProjectEntity findById(@PathVariable String id) {
        return projectService.findById(id);
    }

    @PostMapping
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_PROJECT_ADD)
    public ProjectEntity save(@RequestBody ProjectEntity entity) throws ThingsboardException {
        TenantId tenantId = getTenantId();
        if (entity != null) {
            entity.setTenantId(UUIDConverter.fromTimeUUID(UUID.fromString(tenantId.getId().toString())));
        }
        return projectService.save(entity);
    }

    @PostMapping("edit")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_PROJECT_EDIT)
    public ProjectEntity update(@RequestBody ProjectEntity entity) throws ThingsboardException {
        if (StringUtils.isBlank(entity.getId())) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        return projectService.update(entity);
    }

    @DeleteMapping("{id}")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_PROJECT_DELETE)
    public ProjectEntity delete(@PathVariable String id) {
        return projectService.deleteById(id);
    }

    @GetMapping("children/{id}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_PROJECT_GET)
    public List<ProjectTreeVO> findChildrenById(@PathVariable String id) {
        return projectService.findChildrenById(id);
    }

    @GetMapping("root")
    @SysLog(detail = DataConstants.OPERATING_TYPE_PROJECT_GET)
    public List<ProjectTreeVO> findByRootProject(@RequestParam(value = "devices", required = false, defaultValue = "false") Boolean devices) throws ThingsboardException {
        TenantId tenantId = getTenantId();
        SecurityUser currentUser = getCurrentUser();
        /*if (Authority.CUSTOMER_USER.equals(currentUser.getAuthority())) {
            // 获取当前角色的项目
            return projectRelationService.findProjectTreeVOByEntityTypeAndEntityId(
                    "USER", currentUser.getUuidId().toString());
        } else {*/
        Authority authority = currentUser.getAuthority();
        return projectService.findRootProject(UUIDConverter.fromTimeUUID(UUID.fromString(tenantId.getId().toString())), devices, currentUser.getId(), authority);
//        }
    }

    @GetMapping("tree/dataSourceAndDevice")
    @SysLog(detail = DataConstants.OPERATING_TYPE_PROJECT_GET)
    public List<ProjectTreeVO> findProjectTree(@RequestParam(required = false) String tenantIdString) throws ThingsboardException {
        if (StringUtils.isBlank(tenantIdString)) {
            TenantId tenantId = getTenantId();
            tenantIdString = UUIDConverter.fromTimeUUID(UUID.fromString(tenantId.getId().toString()));
        }
        return projectService.findDataSourceAndDeviceProjectTree(tenantIdString);
    }

    @GetMapping("tree/projectAndGateway")
    @SysLog(detail = DataConstants.OPERATING_TYPE_PROJECT_GET)
    public List<ProjectTreeVO> findProjectAndGatewayTree(@RequestParam(required = false) String tenantIdString) throws ThingsboardException {
        if (StringUtils.isBlank(tenantIdString)) {
            TenantId tenantId = getTenantId();
            tenantIdString = UUIDConverter.fromTimeUUID(UUID.fromString(tenantId.getId().toString()));
        }
        return projectService.findProjectAndGatewayTree(tenantIdString);
    }

    @GetMapping("business/statistics")
    @SysLog(detail = DataConstants.OPERATING_TYPE_PROJECT_GET)
    public List<PieVO> projectBusinessStatistics() throws ThingsboardException {
        return projectService.projectBusinessStatistics(getTenantId());
    }

}
