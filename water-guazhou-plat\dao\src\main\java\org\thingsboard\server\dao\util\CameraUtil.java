package org.thingsboard.server.dao.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.VideoConfigEntity;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.sql.video.VideoRepository;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.video.VideoConfigService;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 摄像头一体机
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-19
 */
@Component
@Slf4j
public class CameraUtil {

    @Value("${camera.host}")
    private String cameraHost;

    @Value("${camera.appKey}")
    private String cameraAppKey;

    @Value("${camera.appSecret}")
    private String cameraAppSecret;

    @Autowired
    private VideoRepository videoRepository;

    @Autowired
    private DaHuaUtil daHuaUtil;

    @Autowired
    private YingShiYunUtil yingShiYunUtil;

    @Autowired
    private WvpUtil wvpUtil;

    @Autowired
    private VideoConfigService videoConfigService;

    /**
     * 获取摄像头列表
     */
    public List getCameraList(String name, String tenantId) throws ThingsboardException {
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("pageNo", 1);
        jsonBody.put("name", name);
        jsonBody.put("pageSize", 512);
        jsonBody.put("resourceType", "camera");
        String result = getResource("/api/irds/v2/resource/resourcesByParams", jsonBody, "application/json", tenantId);
        Map map = JSONObject.parseObject(result, Map.class);
        if (map == null || !"0".equals(map.get("code"))) {
            log.error("获取摄像头列表失败：{}", map == null ? "返回结果：" + result : map.get("msg"));
            throw new ThingsboardException("获取摄像头列表失败", ThingsboardErrorCode.GENERAL);
        }
        // 获取总记录数
        Map dataMap = (Map) map.get("data");
        Integer total = Integer.valueOf(String.valueOf(dataMap.get("total")));
        List camaraList = new ArrayList();
        camaraList.addAll((List) dataMap.get("list"));
        if (total > 512) {
            int i = total / 512;
            if (total % 512 > 0) {
                i++;
            }
            for (int j = 0; j < i; j++) {

                jsonBody.put("pageNo", j + 2);
                result = getResource("/api/irds/v2/resource/resourcesByParams", jsonBody, "application/json", tenantId);
                map = JSONObject.parseObject(result, Map.class);
                if (!map.get("code").equals("0")) {
                    log.error("获取摄像头列表失败：{}", map.get("msg"));
                    continue;
                }
                dataMap = (Map) map.get("data");
                camaraList.addAll((List) dataMap.get("list"));
            }
        }

        return camaraList;
    }

    private String getResource(String url, JSONObject jsonObject, String contentType, String tenantId) {
        /**
         * STEP1：设置平台参数，根据实际情况,设置host appkey appsecret 三个参数.
         */
        VideoConfigEntity videoConfig = videoConfigService.getByTenantId(tenantId);
        if (videoConfig != null) {
            cameraHost = videoConfig.getHost(); // 平台的ip端口
            cameraAppKey = videoConfig.getHkAppKey();  // 密钥appkey
            cameraAppSecret = videoConfig.getHkAppSecret();// 密钥appSecret
        }
        ArtemisConfig.host = cameraHost; // 平台的ip端口
        ArtemisConfig.appKey = cameraAppKey;  // 密钥appkey
        ArtemisConfig.appSecret = cameraAppSecret;// 密钥appSecret


        /**
         * STEP2：设置OpenAPI接口的上下文
         */
        final String ARTEMIS_PATH = "/artemis";

        /**
         * STEP3：设置接口的URI地址
         */
        final String previewURLsApi = ARTEMIS_PATH + url;
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", previewURLsApi);//根据现场环境部署确认是http还是https
            }
        };
        log.info("IP端口：{}, 秘钥appKey：{}，秘钥appSecret：{}", cameraHost, cameraAppKey, cameraAppSecret);

        /**
         * STEP5：组装请求参数
         */
        String body = jsonObject.toJSONString();
        /**
         * STEP6：调用接口
         */
        String result = ArtemisHttpUtil.doPostStringArtemis(path, body, null, null, contentType, null);// post请求application/json类型参数
        if (StringUtils.isBlank(result)) {
            return new JSONObject().toJSONString();
        }
        return result;
    }

    public String getPreviewURL(String cameraIndexCode, String tenantId) throws ThingsboardException {
        if (StringUtils.isBlank(cameraIndexCode)) {
            throw new ThingsboardException("indexCode不能为空", ThingsboardErrorCode.GENERAL);
        }

        JSONObject jsonBody = new JSONObject();
        jsonBody.put("cameraIndexCode", cameraIndexCode);
        jsonBody.put("streamType", 0);
        jsonBody.put("protocol", "hls");
        jsonBody.put("transmode", 1);
        String result = getResource("/api/video/v1/cameras/previewURLs", jsonBody, "application/json", tenantId);

        Map map = JSONObject.parseObject(result, Map.class);
        if (!"0".equals(map.get("code"))) {
            throw new ThingsboardException((String) map.get("msg"), ThingsboardErrorCode.GENERAL);
        }
        Map data = (Map) map.get("data");
        String previewUrl = String.valueOf(data.get("url"));
        // 获取旧IP
        // String oldIp = previewUrl.substring(previewUrl.indexOf("://") + 3, previewUrl.indexOf(":83"));
        // previewUrl = previewUrl.replace(oldIp, "*************");
        return previewUrl;

    }

    public IstarResponse controlling(JSONObject params, String tenantId) throws ThingsboardException {
        String id = params.getString("id");
        VideoEntity videoEntity = videoRepository.findById(id);
        if (videoEntity == null) {
            return IstarResponse.error("摄像头不存在");
        }

        String result = "";
        String previewURL = "";
        switch (videoEntity.getVideoType()) {
            case "1": // 海康
                result = getResource("/api/video/v1/ptzs/controlling", params, "application/json", tenantId);

                Map map = JSONObject.parseObject(result, Map.class);
                if (!map.get("code").equals("0")) {
                    String msg = String.valueOf(map.get("msg"));
                    if ("0x01900053".equals(msg)) {
                        return IstarResponse.error("设备不支持该操作");
                    }
                    return IstarResponse.error(result);
                }
                break; // 大华
            case "2":
                result = daHuaUtil.controlling(videoEntity.getChannelId(), params);
                break;
        }


        return IstarResponse.ok(result);
    }

    public IstarResponse getPlaybackURL(JSONObject jsonObject, String tenantId) throws ThingsboardException {
        String id = jsonObject.getString("id");
        VideoEntity videoEntity = videoRepository.findById(id);
        if (videoEntity == null) {
            return IstarResponse.error("摄像头不存在");
        }
        Long start = jsonObject.getLong("start");
        Long end = jsonObject.getLong("end");
        if (start == null) {
            return IstarResponse.error("请输入开始查询时间");
        }
        if (end == null) {
            return IstarResponse.error("请输入结束查询时间");
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String previewURL = "";
        String startTime;
        String endTime;
        switch (videoEntity.getVideoType()) {
            case "1": // 海康
                try {        // 开始结束时间处理
                    format = new SimpleDateFormat("yyyy-MM-dd’T’HH:mm:ss.SSSzzz");
                    jsonObject.put("beginTime", format.format(new Date(start)));
                    jsonObject.put("endTime", format.format(new Date(end)));
                    jsonObject.put("recordLocation", "0");
                    jsonObject.put("protocol", "hls");
                    jsonObject.put("transmode", "0");
                    jsonObject.put("lockType", "0");

                    String result = getResource("/api/video/v2/cameras/playbackURLs", jsonObject, "application/json", tenantId);
                    Map map = JSONObject.parseObject(result, Map.class);
                    if (!map.get("code").equals("0")) {
                        throw new ThingsboardException((String) map.get("msg"), ThingsboardErrorCode.GENERAL);
                    }
                    Map data = (Map) map.get("data");

                    previewURL = String.valueOf(data.get("url"));
                } catch (ThingsboardException e) {
                    e.printStackTrace();
                    return IstarResponse.error("获取海康播放链接错误：" + e.getMessage());
                }
                break; // 大华
            case "2":
                startTime = format.format(new Date(start));
                endTime = format.format(new Date(end));
                previewURL = daHuaUtil.getPlaybackURL(videoEntity.getChannelId(), startTime, endTime);
                break;
        }

        return IstarResponse.ok(previewURL);
    }

    public String getPreviewURLById(String id, String tenantId) throws IOException {
        VideoEntity videoEntity = videoRepository.findById(id);
        if (videoEntity == null) {
            return "摄像头不存在";
        }
        String previewURL = "";
        switch (videoEntity.getVideoType()) {
            case "1": // 海康
                try {
                    previewURL = this.getPreviewURL(videoEntity.getSerialNumber(), tenantId);
                } catch (ThingsboardException e) {
                    previewURL = "获取海康播放链接错误：" + e.getMessage();
                    e.printStackTrace();
                }
                break; // 大华
            case "2":
                previewURL = daHuaUtil.getPreviewURL(videoEntity.getChannelId());
                break;
            case "3": // 萤石云
                previewURL = yingShiYunUtil.getPreviewURL(videoEntity.getSerialNumber());
                break;
            case "4": // rtsp播放
                previewURL = videoEntity.getUrl();
                break;
            case "5": // wvp播放
                previewURL = wvpUtil.gerUrl(videoEntity.getChannelId());
                break;
        }
        return previewURL;
    }

    public IstarResponse recordStart(JSONObject params, String tenantId) {
        String id = params.getString("id");
        if (StringUtils.isBlank(id)) {
            return IstarResponse.error("id不能为空");
        }
        VideoEntity videoEntity = videoRepository.findById(id);
        if (videoEntity == null) {
            return IstarResponse.error("摄像头不存在");
        }
        String previewURL = "";
        switch (videoEntity.getVideoType()) {
            case "1": // 海康
                    String indexCode = videoEntity.getSerialNumber();
                    params.put("cameraIndexCode", indexCode);
                String resource = getResource("/api/video/v1/manualRecord/start", params, "application/json", tenantId);
                Map map = JSONObject.parseObject(resource, Map.class);
                if (!map.get("code").equals("0")) {
                    return IstarResponse.error("获取链接失败：" + resource);
                }
                Map data = (Map) map.get("data");

                return IstarResponse.ok(data);
                // 大华
            case "2":
                break;
        }
        return null;
    }

    public IstarResponse recordStop(JSONObject params, String tenantId) {
        String id = params.getString("id");
        if (StringUtils.isBlank(id)) {
            return IstarResponse.error("id不能为空");
        }
        VideoEntity videoEntity = videoRepository.findById(id);
        if (videoEntity == null) {
            return IstarResponse.error("摄像头不存在");
        }
        String previewURL = "";
        switch (videoEntity.getVideoType()) {
            case "1": // 海康
                String indexCode = videoEntity.getSerialNumber();
                params.put("cameraIndexCode", indexCode);
                String resource = getResource("/api/video/v1/manualRecord/stop", params, "application/json", tenantId);
                Map map = JSONObject.parseObject(resource, Map.class);
                if (!map.get("code").equals("0")) {
                    return IstarResponse.error("获取链接失败：" + resource);
                }
                Map data = (Map) map.get("data");

                return IstarResponse.ok(data);
            // 大华
            case "2":
                break;
        }
        return null;
    }

    public List getDahuaCamaraList(String name) {
        return daHuaUtil.getCamaraList(name);
    }

    public String getPreviewUrlByCode(String cameraIndexCode, String tenantId) throws Exception {
        List<VideoEntity> videoList = videoRepository.findBySerialNumber(cameraIndexCode);
        if (videoList == null || videoList.isEmpty()) {
            throw new ThingsboardException("播放的摄像头编码不存在, CODE = "+cameraIndexCode+"!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        VideoEntity videoEntity = videoList.get(0);
        if (videoEntity == null) {
            return "摄像头不存在";
        }
        String previewURL = "";
        switch (videoEntity.getVideoType()) {
            case "1": // 海康
                try {
                    previewURL = this.getPreviewURL(videoEntity.getSerialNumber(), tenantId);
                } catch (ThingsboardException e) {
                    previewURL = "获取海康播放链接错误：" + e.getMessage();
                    e.printStackTrace();
                }
                break; // 大华
            case "2":
                previewURL = daHuaUtil.getPreviewURL(videoEntity.getChannelId());
                break;
            case "3": // 萤石云
                previewURL = yingShiYunUtil.getPreviewURL(videoEntity.getSerialNumber());
                break;
            case "4": // rtsp播放
                previewURL = videoEntity.getUrl();
                break;
            case "5": // wvp播放
                previewURL = wvpUtil.gerUrl(videoEntity.getChannelId());
                break;
        }
        return previewURL;
    }

    public void setStatus(List<VideoEntity> records) {
        if (records == null || records.size() == 0) {
            return;
        }
        // 海康
        List<String> haikangList = records.stream().filter(videoVo -> videoVo.getVideoType().equals("1")).map(VideoEntity::getSerialNumber).collect(Collectors.toList());
        int size = haikangList.size() / 500;
        if (haikangList.size() % 500 > 0) {
            size = size + 1;
        }
        Map<String, String> statusMap = new HashMap<>();

        try {
            for (int i = 0; i < size; i++) {
                List<String> subList = haikangList.stream().skip(i * 500).limit(500).collect(Collectors.toList());
                JSONObject params = new JSONObject();
                params.put("indexCodes", subList);
                params.put("pageSize", 500);

                String resource = getResource("/api/nms/v1/online/camera/get", params, "application/json", records.get(0).getTenantId());
                // log.info("海康摄像头状态：{}", resource);
                JSONObject map = JSONObject.parseObject(resource, JSONObject.class);
                if (!map.get("code").equals("0")) {
                    log.error("获取海康摄像头状态失败：" + resource);
                    continue;
                }

                JSONArray jsonArray = map.getJSONObject("data").getJSONArray("list");
                for (int j = 0; j < jsonArray.size(); j++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(j);
                    String indexCode = jsonObject.getString("indexCode");
                    String status = jsonObject.getString("online");
                    statusMap.put(indexCode, status);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 天网
        records.forEach(videoVo -> {
            videoVo.setStatus(statusMap.get(videoVo.getSerialNumber()));
        });
    }
}
