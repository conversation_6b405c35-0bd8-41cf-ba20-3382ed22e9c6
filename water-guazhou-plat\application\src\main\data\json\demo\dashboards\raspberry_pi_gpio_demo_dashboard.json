{"title": "Raspberry PI GPIO Demo Dashboard", "configuration": {"description": "Demo dashboard for Raspberry PI GPIO Demo", "widgets": {"602177f6-267b-cb87-4e8f-e23d7fb2f61c": {"isSystemType": true, "bundleAlias": "gpio_widgets", "typeAlias": "raspberry_pi_gpio_control", "type": "rpc", "title": "New widget", "sizeX": 6, "sizeY": 10, "config": {"targetDeviceAliases": [], "showTitle": true, "backgroundColor": "#fff", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"parseGpioStatusFunction": "return body[pin] === true;", "gpioStatusChangeRequest": {"method": "setGpioStatus", "paramsBody": "{\n   \"pin\": \"{$pin}\",\n   \"enabled\": \"{$enabled}\"\n}"}, "requestTimeout": 500, "switchPanelBackgroundColor": "#008a00", "gpioStatusRequest": {"method": "getGpioStatus", "paramsBody": "{}"}, "gpioList": [{"pin": 7, "label": "GPIO 4 (GPCLK0)", "row": 3, "col": 0, "_uniqueKey": 0}, {"pin": 11, "label": "GPIO 17", "row": 5, "col": 0, "_uniqueKey": 1}, {"pin": 12, "label": "GPIO 18", "row": 5, "col": 1, "_uniqueKey": 2}, {"_uniqueKey": 3, "pin": 13, "label": "GPIO 27", "row": 6, "col": 0}, {"_uniqueKey": 4, "pin": 15, "label": "GPIO 22", "row": 7, "col": 0}, {"_uniqueKey": 5, "pin": 16, "label": "GPIO 23", "row": 7, "col": 1}, {"_uniqueKey": 6, "pin": 18, "label": "GPIO 24", "row": 8, "col": 1}, {"_uniqueKey": 7, "pin": 22, "label": "GPIO 25", "row": 10, "col": 1}, {"_uniqueKey": 8, "pin": 29, "label": "GPIO 5", "row": 14, "col": 0}, {"_uniqueKey": 9, "pin": 31, "label": "GPIO 6", "row": 15, "col": 0}, {"_uniqueKey": 10, "pin": 32, "label": "GPIO 12", "row": 15, "col": 1}, {"_uniqueKey": 11, "pin": 33, "label": "GPIO 13", "row": 16, "col": 0}, {"_uniqueKey": 12, "pin": 35, "label": "GPIO 19", "row": 17, "col": 0}, {"_uniqueKey": 13, "pin": 36, "label": "GPIO 16", "row": 17, "col": 1}, {"_uniqueKey": 14, "pin": 37, "label": "GPIO 26", "row": 18, "col": 0}, {"_uniqueKey": 15, "pin": 38, "label": "GPIO 20", "row": 18, "col": 1}, {"_uniqueKey": 16, "pin": 40, "label": "GPIO 21", "row": 19, "col": 1}]}, "title": "Raspberry Pi GPIO Control Panel", "datasources": [], "targetDeviceAliasIds": ["f26b12b6-6938-e1a0-85ec-d88a1f23e382"]}, "row": 0, "col": 0, "id": "602177f6-267b-cb87-4e8f-e23d7fb2f61c"}, "3cca52a5-e874-eb43-b444-8efa01e663c8": {"isSystemType": true, "bundleAlias": "gpio_widgets", "typeAlias": "raspberry_pi_gpio_panel", "type": "latest", "title": "New widget", "sizeX": 7, "sizeY": 10, "config": {"showTitle": true, "backgroundColor": "#fff", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"gpioList": [{"pin": 1, "label": "3.3V", "row": 0, "col": 0, "color": "#fc9700", "_uniqueKey": 0}, {"pin": 2, "label": "5V", "row": 0, "col": 1, "color": "#fb0000", "_uniqueKey": 1}, {"pin": 3, "label": "GPIO 2 (I2C1_SDA)", "row": 1, "col": 0, "color": "#02fefb", "_uniqueKey": 2}, {"color": "#fb0000", "pin": 4, "label": "5V", "row": 1, "col": 1}, {"color": "#02fefb", "pin": 5, "label": "GPIO 3 (I2C1_SCL)", "row": 2, "col": 0}, {"color": "#000000", "pin": 6, "label": "GND", "row": 2, "col": 1}, {"color": "#00fd00", "pin": 7, "label": "GPIO 4 (GPCLK0)", "row": 3, "col": 0}, {"color": "#fdfb00", "pin": 8, "label": "GPIO 14 (UART_TXD)", "row": 3, "col": 1}, {"color": "#000000", "pin": 9, "label": "GND", "row": 4, "col": 0}, {"color": "#fdfb00", "pin": 10, "label": "GPIO 15 (UART_RXD)", "row": 4, "col": 1}, {"color": "#00fd00", "pin": 11, "label": "GPIO 17", "row": 5, "col": 0}, {"color": "#00fd00", "pin": 12, "label": "GPIO 18", "row": 5, "col": 1}, {"color": "#00fd00", "pin": 13, "label": "GPIO 27", "row": 6, "col": 0}, {"color": "#000000", "pin": 14, "label": "GND", "row": 6, "col": 1}, {"color": "#00fd00", "pin": 15, "label": "GPIO 22", "row": 7, "col": 0}, {"color": "#00fd00", "pin": 16, "label": "GPIO 23", "row": 7, "col": 1}, {"color": "#fc9700", "pin": 17, "label": "3.3V", "row": 8, "col": 0}, {"color": "#00fd00", "pin": 18, "label": "GPIO 24", "row": 8, "col": 1}, {"color": "#fd01fd", "pin": 19, "label": "GPIO 10 (SPI_MOSI)", "row": 9, "col": 0}, {"color": "#000000", "pin": 20, "label": "GND", "row": 9, "col": 1}, {"color": "#fd01fd", "pin": 21, "label": "GPIO 9 (SPI_MISO)", "row": 10, "col": 0}, {"color": "#00fd00", "pin": 22, "label": "GPIO 25", "row": 10, "col": 1}, {"color": "#fd01fd", "pin": 23, "label": "GPIO 11 (SPI_SCLK)", "row": 11, "col": 0}, {"color": "#fd01fd", "pin": 24, "label": "GPIO 8 (SPI_CE0)", "row": 11, "col": 1}, {"color": "#000000", "pin": 25, "label": "GND", "row": 12, "col": 0}, {"color": "#fd01fd", "pin": 26, "label": "GPIO 7 (SPI_CE1)", "row": 12, "col": 1}, {"color": "#ffffff", "pin": 27, "label": "ID_SD", "row": 13, "col": 0}, {"color": "#ffffff", "pin": 28, "label": "ID_SC", "row": 13, "col": 1}, {"color": "#00fd00", "pin": 29, "label": "GPIO 5", "row": 14, "col": 0}, {"color": "#000000", "pin": 30, "label": "GND", "row": 14, "col": 1}, {"color": "#00fd00", "pin": 31, "label": "GPIO 6", "row": 15, "col": 0}, {"color": "#00fd00", "pin": 32, "label": "GPIO 12", "row": 15, "col": 1}, {"color": "#00fd00", "pin": 33, "label": "GPIO 13", "row": 16, "col": 0}, {"color": "#000000", "pin": 34, "label": "GND", "row": 16, "col": 1}, {"color": "#00fd00", "pin": 35, "label": "GPIO 19", "row": 17, "col": 0}, {"color": "#00fd00", "pin": 36, "label": "GPIO 16", "row": 17, "col": 1}, {"color": "#00fd00", "pin": 37, "label": "GPIO 26", "row": 18, "col": 0}, {"color": "#00fd00", "pin": 38, "label": "GPIO 20", "row": 18, "col": 1}, {"color": "#000000", "pin": 39, "label": "GND", "row": 19, "col": 0}, {"color": "#00fd00", "pin": 40, "label": "GPIO 21", "row": 19, "col": 1}], "ledPanelBackgroundColor": "#008a00"}, "title": "Raspberry Pi GPIO Status Panel", "datasources": [{"type": "entity", "dataKeys": [{"name": "7", "type": "attribute", "label": "7", "color": "#2196f3", "settings": {}, "_hash": 0.20925966435886978}, {"name": "11", "type": "attribute", "label": "11", "color": "#4caf50", "settings": {}, "_hash": 0.330267349594344}, {"name": "12", "type": "attribute", "label": "12", "color": "#f44336", "settings": {}, "_hash": 0.5040578704481748}, {"name": "13", "type": "attribute", "label": "13", "color": "#ffc107", "settings": {}, "_hash": 0.588956328191639}, {"name": "15", "type": "attribute", "label": "15", "color": "#607d8b", "settings": {}, "_hash": 0.9229040530336119}, {"name": "16", "type": "attribute", "label": "16", "color": "#9c27b0", "settings": {}, "_hash": 0.8692315253041654}, {"name": "18", "type": "attribute", "label": "18", "color": "#8bc34a", "settings": {}, "_hash": 0.41465562857521543}, {"name": "22", "type": "attribute", "label": "22", "color": "#3f51b5", "settings": {}, "_hash": 0.36135260043112827}, {"name": "29", "type": "attribute", "label": "29", "color": "#e91e63", "settings": {}, "_hash": 0.9904592276182183}, {"name": "31", "type": "attribute", "label": "31", "color": "#ffeb3b", "settings": {}, "_hash": 0.038330985429919195}, {"name": "32", "type": "attribute", "label": "32", "color": "#03a9f4", "settings": {}, "_hash": 0.4334683890135089}, {"name": "33", "type": "attribute", "label": "33", "color": "#ff9800", "settings": {}, "_hash": 0.6487255992492305}, {"name": "35", "type": "attribute", "label": "35", "color": "#673ab7", "settings": {}, "_hash": 0.971555321150732}, {"name": "36", "type": "attribute", "label": "36", "color": "#cddc39", "settings": {}, "_hash": 0.7826129728424382}, {"name": "37", "type": "attribute", "label": "37", "color": "#009688", "settings": {}, "_hash": 0.44925676517537627}, {"name": "38", "type": "attribute", "label": "38", "color": "#795548", "settings": {}, "_hash": 0.051518155759787465}, {"name": "40", "type": "attribute", "label": "40", "color": "#00bcd4", "settings": {}, "_hash": 0.8733296686871144}], "name": "RPi", "entityAliasId": "f26b12b6-6938-e1a0-85ec-d88a1f23e382"}], "timewindow": {"realtime": {"timewindowMs": 60000}}}, "row": 0, "col": 6, "id": "3cca52a5-e874-eb43-b444-8efa01e663c8"}}, "states": {"default": {"name": "<PERSON><PERSON><PERSON>", "root": true, "layouts": {"main": {"widgets": {"602177f6-267b-cb87-4e8f-e23d7fb2f61c": {"sizeX": 6, "sizeY": 10, "row": 0, "col": 0}, "3cca52a5-e874-eb43-b444-8efa01e663c8": {"sizeX": 7, "sizeY": 10, "row": 0, "col": 6}}, "gridSettings": {"backgroundColor": "#eeeeee", "color": "rgba(0,0,0,0.870588)", "columns": 24, "margins": [10, 10], "backgroundSizeMode": "100%"}}}}}, "entityAliases": {"f26b12b6-6938-e1a0-85ec-d88a1f23e382": {"id": "f26b12b6-6938-e1a0-85ec-d88a1f23e382", "alias": "RPi", "filter": {"type": "entityName", "resolveMultiple": false, "entityType": "DEVICE", "entityNameFilter": "Raspberry Pi Demo Device"}}}, "timewindow": {"displayValue": "", "selectedTab": 0, "realtime": {"interval": 1000, "timewindowMs": 60000}, "history": {"historyType": 0, "interval": 1000, "timewindowMs": 60000, "fixedTimewindow": {"startTimeMs": 1498653734150, "endTimeMs": 1498740134150}}, "aggregation": {"type": "AVG", "limit": 200}}, "settings": {"stateControllerId": "default", "showTitle": true, "showDashboardsSelect": true, "showEntitiesSelect": true, "showDashboardTimewindow": true, "showDashboardExport": true, "toolbarAlwaysOpen": false}}, "name": "Raspberry PI GPIO Demo Dashboard"}