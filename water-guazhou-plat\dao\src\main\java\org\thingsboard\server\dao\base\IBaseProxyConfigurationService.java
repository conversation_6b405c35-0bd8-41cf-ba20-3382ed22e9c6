package org.thingsboard.server.dao.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BaseProxyConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseProxyConfigurationPageRequest;

import java.util.List;

/**
 * 平台管理-代理配置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface IBaseProxyConfigurationService {
    /**
     * 查询平台管理-代理配置
     *
     * @param id 平台管理-代理配置主键
     * @return 平台管理-代理配置
     */
    public BaseProxyConfiguration selectBaseProxyConfigurationById(String id);

    /**
     * 查询平台管理-代理配置列表
     *
     * @param baseProxyConfiguration 平台管理-代理配置
     * @return 平台管理-代理配置集合
     */
    public IPage<BaseProxyConfiguration> selectBaseProxyConfigurationList(BaseProxyConfigurationPageRequest baseProxyConfiguration);

    /**
     * 新增平台管理-代理配置
     *
     * @param baseProxyConfiguration 平台管理-代理配置
     * @return 结果
     */
    public int insertBaseProxyConfiguration(BaseProxyConfiguration baseProxyConfiguration);

    /**
     * 修改平台管理-代理配置
     *
     * @param baseProxyConfiguration 平台管理-代理配置
     * @return 结果
     */
    public int updateBaseProxyConfiguration(BaseProxyConfiguration baseProxyConfiguration);

    /**
     * 批量删除平台管理-代理配置
     *
     * @param ids 需要删除的平台管理-代理配置主键集合
     * @return 结果
     */
    public int deleteBaseProxyConfigurationByIds(List<String> ids);

    /**
     * 删除平台管理-代理配置信息
     *
     * @param id 平台管理-代理配置主键
     * @return 结果
     */
    public int deleteBaseProxyConfigurationById(String id);
}
