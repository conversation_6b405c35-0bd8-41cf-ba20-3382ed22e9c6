package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseVideoConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseVideoConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseVideoConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-视频管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Api(tags = "平台管理-视频管理")
@RestController
@RequestMapping("api/base/video/configuration")
public class BaseVideoConfigurationController extends BaseController {

    @Autowired
    private IBaseVideoConfigurationService baseVideoConfigurationService;

    /**
     * 查询平台管理-视频管理列表
     */
    @MonitorPerformance(description = "平台管理-查询视频管理列表")
    @ApiOperation(value = "查询视频管理列表")
    @GetMapping("/list")
    public IstarResponse list(BaseVideoConfigurationPageRequest baseVideoConfiguration) {
        return IstarResponse.ok(baseVideoConfigurationService.selectBaseVideoConfigurationList(baseVideoConfiguration));
    }

    /**
     * 获取平台管理-视频管理详细信息
     */
    @MonitorPerformance(description = "平台管理-查询视频管理详情")
    @ApiOperation(value = "查询视频管理详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseVideoConfigurationService.selectBaseVideoConfigurationById(id));
    }

    /**
     * 新增平台管理-视频管理
     */
    @MonitorPerformance(description = "平台管理-新增视频管理")
    @ApiOperation(value = "新增视频管理")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseVideoConfiguration baseVideoConfiguration) {
        return IstarResponse.ok(baseVideoConfigurationService.insertBaseVideoConfiguration(baseVideoConfiguration));
    }

    /**
     * 修改平台管理-视频管理
     */
    @MonitorPerformance(description = "平台管理-修改视频管理")
    @ApiOperation(value = "修改视频管理")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseVideoConfiguration baseVideoConfiguration) {
        return IstarResponse.ok(baseVideoConfigurationService.updateBaseVideoConfiguration(baseVideoConfiguration));
    }

    /**
     * 删除平台管理-视频管理
     */
    @MonitorPerformance(description = "平台管理-删除视频管理")
    @ApiOperation(value = "删除视频管理")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseVideoConfigurationService.deleteBaseVideoConfigurationByIds(ids));
    }
}
