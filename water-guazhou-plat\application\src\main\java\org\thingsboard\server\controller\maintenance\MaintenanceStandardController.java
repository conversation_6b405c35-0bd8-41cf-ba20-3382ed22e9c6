package org.thingsboard.server.controller.maintenance;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.MaintenanceStandardEntity;
import org.thingsboard.server.dao.repair.MaintenanceStandardService;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("api/maintenance/standard")
public class MaintenanceStandardController extends BaseController {

    @Autowired
    private MaintenanceStandardService maintenanceStandardService;

    @GetMapping("{id}")
    public MaintenanceStandardEntity get(@PathVariable String id) {
        return maintenanceStandardService.findById(id);
    }

    @GetMapping("list")
    public PageData<MaintenanceStandardEntity> findList(@RequestParam int page, @RequestParam int size,
                                                        @RequestParam(required = false, defaultValue = "") String name,
                                                        @RequestParam(required = false, defaultValue = "") String deviceType) throws ThingsboardException {
        User currentUser = getCurrentUser();
        return maintenanceStandardService.findList(page, size, name, deviceType, currentUser);
    }

    @GetMapping("all")
    public List<MaintenanceStandardEntity> findList(@RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        User currentUser = getCurrentUser();
        return maintenanceStandardService.findAll(name, currentUser.getTenantId());
    }

    @PostMapping("save")
    public MaintenanceStandardEntity save(@RequestBody MaintenanceStandardEntity entity) throws ThingsboardException {
        User currentUser = getCurrentUser();
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreator(currentUser.getFirstName());
            entity.setCreateTime(new Date());
            entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));
        }

        return maintenanceStandardService.save(entity);
    }

    @DeleteMapping
    public void remove(@RequestBody List<String> ids) {
        maintenanceStandardService.remove(ids);
    }

}
