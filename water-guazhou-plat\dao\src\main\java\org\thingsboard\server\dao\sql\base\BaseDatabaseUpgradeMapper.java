package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseUpgrade;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseUpgradePageRequest;

import java.util.List;

/**
 * 平台管理-数据库升级记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Mapper
public interface BaseDatabaseUpgradeMapper {
    /**
     * 查询平台管理-数据库升级记录
     *
     * @param id 平台管理-数据库升级记录主键
     * @return 平台管理-数据库升级记录
     */
    public BaseDatabaseUpgrade selectBaseDatabaseUpgradeById(String id);

    /**
     * 查询平台管理-数据库升级记录列表
     *
     * @param baseDatabaseUpgrade 平台管理-数据库升级记录
     * @return 平台管理-数据库升级记录集合
     */
    public IPage<BaseDatabaseUpgrade> selectBaseDatabaseUpgradeList(BaseDatabaseUpgradePageRequest baseDatabaseUpgrade);

    /**
     * 新增平台管理-数据库升级记录
     *
     * @param baseDatabaseUpgrade 平台管理-数据库升级记录
     * @return 结果
     */
    public int insertBaseDatabaseUpgrade(BaseDatabaseUpgrade baseDatabaseUpgrade);

    /**
     * 修改平台管理-数据库升级记录
     *
     * @param baseDatabaseUpgrade 平台管理-数据库升级记录
     * @return 结果
     */
    public int updateBaseDatabaseUpgrade(BaseDatabaseUpgrade baseDatabaseUpgrade);

    /**
     * 删除平台管理-数据库升级记录
     *
     * @param id 平台管理-数据库升级记录主键
     * @return 结果
     */
    public int deleteBaseDatabaseUpgradeById(String id);

    /**
     * 批量删除平台管理-数据库升级记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseDatabaseUpgradeByIds(@Param("array") List<String> ids);
}
