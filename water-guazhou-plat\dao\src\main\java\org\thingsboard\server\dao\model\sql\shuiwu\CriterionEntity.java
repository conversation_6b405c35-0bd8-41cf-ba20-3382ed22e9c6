package org.thingsboard.server.dao.model.sql.shuiwu;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.shuiwu.DTO.CriterionDetailDTO;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SHUIWU_CRITERION_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class CriterionEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SHUIWU_CRITERION_NAME)
    private String name;

    @Column(name = ModelConstants.SHUIWU_CRITERION_DEVICE_TYPE)
    private String deviceType;

    @Column(name = ModelConstants.SHUIWU_CRITERION_REMARK)
    private String remark;

    @Column(name = ModelConstants.SHUIWU_CRITERION_DETAIL)
    private String detail;

    @Column(name = ModelConstants.SHUIWU_CRITERION_CREATOR)
    private String creator;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.IS_DEL)
    private String isDel;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private List<CriterionDetailDTO> dataList;
}
