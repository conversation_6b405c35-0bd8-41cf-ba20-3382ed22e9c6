import { ISLOperation } from '../SLCardSearch/type'
import { ISLFormItem } from '../SLFormItem/type'
import { ISLPaginationConfig } from '../SLPagination/type'

export interface ISLTableColumn {
  prop: string
  label: string
  formatter?: (row: any, val: any, field?: string) => string
  border?: string
  align?: 'center' | 'right'
  fixed?: string | boolean
  minWidth?: string | number
  width?: string | number
  icon?: string
  iconStyle?: Record<string, any>
  svgIcon?: any
  image?: boolean
  className?: string
  /**
   * 子列，用于多级表头
   */
  subColumns?: ISLTableColumn[]
  cellStyle?: Record<string, string> | ((row: any) => Record<string, any>)
  handleClick?: (row: any) => void
  /**
   * 仅当为表单元素时生效
   *
   * row:当前行内容
   *
   * val:选中的值
   */
  handleChange?: (row: any, val: any, field?: string) => void
  /**
   * 为table配置表单元素
   *
   * 当存在这个属性时，则table对应列的单元格将为此配置设置的表单元素
   */
  slFormItemConfig?: ISLFormItem
  summary?: boolean
  /** 写到后面的单位 */
  unit?: string
  /** 写到前面的单位 */
  preUnit?: string
}
export interface ISLTableIndex {
  label?: string
  width?: string
  align?: string
  fixed?: 'right'
  showRank?: boolean
}
export interface ISLTableConfig {
  loading?: any
  stripe?: boolean
  selectList?: any[]
  currentRow?: any
  indexVisible?: ISLTableIndex | boolean
  dataList: any[]
  columns: ISLTableColumn[]
  operations?: ISLOperation[]
  operationFixed?: boolean | string
  operationWidth?: string | number
  operationSize?: 'small' | 'default' | 'large'
  height?: string | number

  maxHeight?: string | number
  sort?: {
    // topColors?: string[] // 暂时无用
    showBackground?: boolean
    order?: 'ascending' | 'descending'
    prop?: string
  }
  pagination: ISLPaginationConfig
  showSummary?: boolean
  /** 合计行的描述信息，默认是合计 */
  summaryDesc?: string
  highlightCurrentRow?: boolean
  spanMethod?: ({
    row, column, rowIndex, columnIndex
  }) => { rowspan: number; colspan: number }
  /**
   * @param columns 表格列配置
   * @param 表格数据
   * @return 返回合计行数据，比如：['合计',100,659,...]
   */
  summaryMethod?: (columns: ISLTableColumn[], data: any) => any
  handleSelectChange?: (val: any) => any
  handleRowClick?: (row: any) => any
  handleRowDbClick?: (row: any) => any
}
