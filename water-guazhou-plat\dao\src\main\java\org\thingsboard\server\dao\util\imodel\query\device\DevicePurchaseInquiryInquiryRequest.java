package org.thingsboard.server.dao.util.imodel.query.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseInquiry;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class DevicePurchaseInquiryInquiryRequest extends SaveRequest<DevicePurchaseInquiry> {
    // 供应商ID
    @NotNullOrEmpty
    private String supplierId;

    // 联系人
    @NotNullOrEmpty
    private String contact;

    // 联系方式
    @NotNullOrEmpty
    private String contactPhone;

    // 单价
    @NotNullOrEmpty
    private Double price;

    // 意向供应商
    @NotNullOrEmpty
    private Boolean intentionSupplier;

    // 附件
    @NotNullOrEmpty
    private String file;

    @Override
    protected DevicePurchaseInquiry build() {
        DevicePurchaseInquiry entity = new DevicePurchaseInquiry();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected DevicePurchaseInquiry update(String id) {
        DevicePurchaseInquiry entity = new DevicePurchaseInquiry();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(DevicePurchaseInquiry entity) {
        entity.setSupplierId(supplierId);
        entity.setContact(contact);
        entity.setContactPhone(contactPhone);
        entity.setPrice(price);
        entity.setInquiryTime(new Date());
        entity.setIntentionSupplier(intentionSupplier);
        entity.setFile(file);
    }
}