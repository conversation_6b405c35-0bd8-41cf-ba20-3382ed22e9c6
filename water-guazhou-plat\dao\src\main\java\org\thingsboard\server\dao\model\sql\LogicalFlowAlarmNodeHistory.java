package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@EqualsAndHashCode
@Table(name = ModelConstants.TABLE_LOGICAL_FLOW_ALARM_NODE_HISTORY)
public class LogicalFlowAlarmNodeHistory {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.LOGICAL_FLOW_ID)
    private String logicalFlowId;

    @Column(name = ModelConstants.LOGICAL_FLOW_NODE_ID)
    private String logicalFlowNodeId;

    @Column(name = ModelConstants.LOGICAL_FLOW_ALARM_NODE_HISTORY_RECIPIENT)
    private String recipient;

    @Column(name = ModelConstants.CREATED_TIME)
    private Long createdTime;

    @Column(name = ModelConstants.LOGICAL_FLOW_ALARM_NODE_HISTORY_SEND_FLAG)
    private String sendFlag;

    @Transient
    private String logicalFlowName;

    @Transient
    private String logicalFlowNodeName;
}
