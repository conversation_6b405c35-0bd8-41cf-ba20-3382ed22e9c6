<template>
  <el-tabs
    v-model="name"
    type="border-card"
    class="information"
    :stretch="true"
    @tab-change="handleClick"
  >
    <el-tab-pane name="待办">
      <template #label>
        <span>待办<el-badge :value="state['0']"></el-badge></span>
      </template>
      <informationList
        v-if="name === '待办'"
        :type="0"
        @refreshData="refreshData"
      ></informationList>
    </el-tab-pane>
    <el-tab-pane name="报警">
      <template #label>
        <span>报警<el-badge :value="state['1']"></el-badge></span>
      </template>
      <informationList
        v-if="name === '报警'"
        :type="1"
        @refreshData="refreshData"
      ></informationList>
    </el-tab-pane>
    <el-tab-pane name="通知">
      <template #label>
        <span>通知<el-badge :value="state['2']"></el-badge></span>
      </template>
      <informationList
        v-if="name === '通知'"
        :type="2"
        @refreshData="refreshData"
      ></informationList>
    </el-tab-pane>
  </el-tabs>
</template>
<script lang="ts" setup>
import informationList from './informationList.vue'

const props = defineProps<{ config: any }>()
const emit = defineEmits(['refreshData'])

const refreshData = () => {
  emit('refreshData')
}

const state = ref({})

const name = ref('待办')

function handleClick(params: any) {
  name.value = params
}

watch(props, () => {
  state.value = props.config
})
</script>

<style lang="scss" scoped>
.information {
  margin: -20px;
}
</style>
