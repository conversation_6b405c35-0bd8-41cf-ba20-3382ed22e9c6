package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import javax.persistence.Id;
import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sm_circuit_task_report")
public class CircuitTaskReport {
    // id
    @Id
    private String id;

    // 类型：关键点、设备、专项设备
    private String type;

    // 任务编号
    private String taskCode;

    // 关键点/设备/专项设备的名称
    private String name;

    // 设备类型，仅设备和专项设备该字段有值
    private String deviceType;

    // 是否到位、专项设备
    private Boolean isSettle;

    // 到位时间
    private Date settleTime;

    // 是否反馈
    private Boolean isFallback;

    // 反馈时间
    private Date fallbackTime;

    // 图片
    private String image;

    // 视频
    private String video;

    // 音频
    private String audio;

    // 附件
    private String file;

    // 客户Id
    private String tenantId;

    // 是否已完成
    private Boolean isComplete;

    // 地理位置，目前规则为经纬度使用逗号隔开
    private String coordinate;

}
