import{d as ee,c as f,r as u,l as b,bH as I,s as te,a8 as re,S as g,b as l,D as y,u as D,o as oe,g as V,n as ie,q as m,h as ae,F as se,a9 as le,b6 as ne}from"./index-r0dFAfgr.js";import{_ as ce}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as de}from"./CardTable-rdWOL4_6.js";import{_ as pe}from"./CardSearch-CB_HNR-Q.js";import fe from"./detail-CU6-qhMl.js";import ue from"./OrderStepTags-CClNfq4j.js";import{b as me,g as ge,a as q}from"./config-DqqM5K5L.js";import{R as ye,d as be,H as he,e as _e,A as C,f as Ie,c as ve,a as ke}from"./index-CpGhZCTT.js";import{u as xe}from"./useUser-Blb5V02j.js";import{f as v}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         */import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./OutsideWorkOrder-C6s8joBt.js";const Re={class:"wrapper"},$e=ee({__name:"MyDeals",setup(Ue){const{getUserOptions:J}=xe(),h=f(),F=f(),k=f(),x=f(),R=f(),U=f(),S=f(),w=f(),N=f(),T=u({WorkOrderEmergencyLevelList:[]});function H(){ke("1").then(e=>{T.WorkOrderEmergencyLevelList=le(e.data.data||[],"children",{label:"name",value:"id"})})}const P=f("0"),E=f(),A=u({title:"流程明细",cancel:!1,className:"lightColor",group:[]}),z=u({filters:[{field:"stageBetween",label:"工单状态",labelWidth:40,type:"radio-button",options:[{label:"待接收",value:"ASSIGN"},{label:"待完成",value:"RESOLVING"},{label:"已完成",value:"SUBMIT"}],onChange:e=>{var a;const t=e==="ASSIGN"?["详情","接收"]:e==="RESOLVING"?["详情","到场","处理","完成","退单"]:["详情","复审"],r=e==="ASSIGN"?100:e==="RESOLVING"?320:100;s.operationWidth=r,(a=s.operations)==null||a.map(o=>{const i=o.text;o.perm=t.indexOf(i)!==-1}),d()}},{field:"title",label:"标题",type:"input",onChange:()=>d()},{field:"date",label:"发起时间",type:"daterange"},{field:"source",label:"来源",type:"select",options:me()},{field:"level",label:"紧急程度",type:"select",options:ge()},{field:"type",label:"类型",type:"select-tree",options:q()}],operations:[{type:"btn-group",btns:[{perm:!0,type:"primary",text:"查询",iconifyIcon:"ep:search",click:()=>d()},{perm:!0,type:"default",text:"重置",iconifyIcon:"ep:refresh",click:()=>X()},{perm:!0,type:"warning",text:"导出",iconifyIcon:"ep:download",click:()=>Y()}]}],defaultParams:{stageBetween:"ASSIGN",date:[b().subtract(1,"M").format(I),b().format(I)]},handleSearch:()=>d()}),s=u({expandable:!0,expandComponent:te(ue),columns:[{minWidth:180,prop:"serialNo",label:"工单编号"},{minWidth:100,prop:"title",label:"标题"},{minWidth:120,prop:"source",label:"来源"},{minWidth:120,prop:"type",label:"类型"},{minWidth:120,prop:"level",label:"紧急程度",tag:!0,tagColor:e=>{var t;return((t=T.WorkOrderEmergencyLevelList.find(r=>r.value===e.level))==null?void 0:t.color)||""},formatter:e=>{var t;return(t=T.WorkOrderEmergencyLevelList.find(r=>r.value===e.level))==null?void 0:t.label}},{minWidth:100,prop:"organizerName",label:"发起人"},{minWidth:160,prop:"createTime",label:"发起时间",formatter:e=>v(e.createTime)},{minWidth:120,prop:"processUserName",label:"处理人"},{minWidth:120,prop:"processLevelLabel",label:"处理级别"},{minWidth:160,prop:"estimatedFinishTime",label:"预计完成时间",formatter:e=>v(e.estimatedFinishTime)},{minWidth:160,prop:"completeTime",label:"完成时间",formatter:e=>v(e.completeTime)},{minWidth:160,prop:"updateTime",label:"最后更新时间",formatter:e=>v(e.updateTime)}],defaultExpandAll:!0,dataList:[],pagination:{refreshData:({page:e,size:t})=>{s.pagination.page=e,s.pagination.limit=t,d()}},operationWidth:100,operations:[{perm:!0,text:"详情",isTextBtn:!0,click:e=>Q(e)},{perm:!0,text:"接收",isTextBtn:!0,click:e=>{s.currentRow=e,$()}},{perm:!1,text:"转发",isTextBtn:!0,click:e=>{var t;s.currentRow=e,(t=h.value)==null||t.openDialog()}},{perm:!1,text:"协作",click:e=>{var t;s.currentRow=e,O.defaultValue={type:e.source},(t=F.value)==null||t.openDialog()}},{perm:!1,text:"到场",click:e=>{var t;s.currentRow=e,(t=k.value)==null||t.openDialog()}},{perm:!1,text:"处理",click:e=>{var t;s.currentRow=e,(t=R.value)==null||t.openDialog()}},{perm:!1,text:"完成",click:e=>{var t;s.currentRow=e,(t=U.value)==null||t.openDialog()}},{hide:re(()=>window.SITE_CONFIG.SITENAME==="xiaoqiu"),perm:!1,text:"退单",click:e=>{var t;s.currentRow=e,(t=S.value)==null||t.openDialog()}},{perm:!1,text:"复审",isTextBtn:!0,disabled:e=>e.status!=="SUBMIT"&&e.status!=="REVIEW",click:e=>{var t;s.currentRow=e,(t=x.value)==null||t.openDialog()}}]}),$=()=>{g("确定接收？","提示信息").then(async()=>{var e,t;try{const r=await ye((e=s.currentRow)==null?void 0:e.id);r.data.code===200?(l.success("接收成功！"),d()):l.error(((t=r.data)==null?void 0:t.err)||"接收失败")}catch{l.error("系统错误")}}).catch(()=>{})},_=u({dialogWidth:500,title:"复审",labelPosition:"top",group:[{fields:[{type:"select",label:"审核人：",field:"processUserId",options:[],rules:[{required:!0,message:"请选择审核人"}]},{type:"textarea",label:"备注：",field:"processRemark"}]}],defaultValue:{},submit:e=>{g("确定提交？","提示信息").then(async()=>{var t,r,a,o;_.submitting=!0;try{const i=await be((t=s.currentRow)==null?void 0:t.id,{...e,processAdditionalInfo:JSON.stringify({}),stage:"REVIEW"});((r=i.data)==null?void 0:r.code)===200?(l.success("操作成功"),(a=x.value)==null||a.closeDialog(),d()):l.error(((o=i.data)==null?void 0:o.err)||"操作失败")}catch{l.error("系统错误")}_.submitting=!1}).catch(()=>{})}}),n=u({title:"转发工单",labelPosition:"top",dialogWidth:500,group:[{fields:[{type:"select",label:"转发至：",field:"expectUserId",options:[],rules:[{required:!0,message:"请选择接收人"}]},{type:"select",label:"审核人：",field:"nextProcessUserId",options:[],rules:[{required:!0,message:"请选择审核人"}]},{type:"textarea",label:"备注：",field:"processRemark",placeholder:" "}]}],submit:e=>{g("确定转发？","提示信息").then(async()=>{var t,r,a,o,i;n.submitting=!0;try{const p=(r=(t=n.group[0].fields[0].options)==null?void 0:t.find(W=>W.value===e.expectUserId))==null?void 0:r.label;e={...e,expectUserId:y(e.expectUserId),nextProcessUserId:y(e.nextProcessUserId)};const c=await he((a=s.currentRow)==null?void 0:a.id,{processAdditionalInfo:JSON.stringify({expectUserId:p}),...e});c.data.code===200?(l.success("转发成功！"),d(),(o=h.value)==null||o.closeDialog()):l.error(((i=c.data)==null?void 0:i.err)||"转发失败")}catch{l.error("系统错误")}n.submitting=!1}).catch(()=>{})}}),O=u({title:"申请协作",labelPosition:"top",dialogWidth:500,group:[{fields:[{type:"textarea",label:"申请理由：",field:"processRemark",placeholder:" ",rules:[{required:!0,message:"请输入申请理由"}]},{type:"select",label:"审核人：",field:"nextProcessUserId",options:[],rules:[{required:!0,message:"请选择审核人"}]},{type:"select-tree",label:"工单类型",field:"type",options:q(),rules:[{required:!0,message:"请选择工单类型"}]},{type:"textarea",field:"remark",label:"备注"},{type:"textarea",field:"collaborationRemark",label:"协作备注"}]}],submit:e=>{g("确定提交？","提示信息").then(async()=>{var t;n.submitting=!0;try{e={...e,isDirectDispatch:!1},e.nextProcessUserId&&(e.nextProcessUserId=y(e.nextProcessUserId)),_e((t=s.currentRow)==null?void 0:t.id,e).then(r=>{var a,o;r.data.code===200?(l.success("协作提交成功！"),d(),(a=h.value)==null||a.closeDialog()):l.error(((o=r.data)==null?void 0:o.err)||"协作提交失败")})}catch{l.error("系统错误")}n.submitting=!1}).catch(()=>{})}}),j=u({title:"到场",dialogWidth:500,labelPosition:"top",group:[{fields:[{field:"imgUrl",label:"现场图片：",type:"image",returnType:"comma",limit:2,multiple:!0},{field:"videoUrl",label:"现场视频：",type:"file",limit:2,returnType:"comma",tips:"只能上传视频文件,最多上传2个，大小不能超过100M"},{field:"audioUrl",label:"现场录音：",type:"file",limit:2,returnType:"comma",tips:"只能上传音频文件,最多上传2个，大小不能超过4M"},{field:"otherFileUrl",label:"其它附件：",type:"file",limit:2,returnType:"comma",tips:"只能上传文件,最多上传2个，大小不能超过4M"},{field:"processRemark",label:"备注：",type:"textarea"}]}],submit:e=>{g("确定提交？","提示信息").then(async()=>{var t,r,a,o,i,p;n.submitting=!0;try{const c=await C((t=s.currentRow)==null?void 0:t.id,{processRemark:"到场",processAdditionalInfo:JSON.stringify(e),stage:"ARRIVING",nextProcessUserId:y(((a=(r=D().user)==null?void 0:r.id)==null?void 0:a.id)||"")});((o=c.data)==null?void 0:o.code)===200?(l.success("操作成功"),(i=k.value)==null||i.closeDialog(),d()):l.error(((p=c.data)==null?void 0:p.err)||"操作失败")}catch{l.error("系统错误")}n.submitting=!1}).catch(()=>{})}}),K=u({title:"处理",labelPosition:"top",dialogWidth:500,group:[{fields:[{field:"imgUrl",label:"现场图片：",type:"image",returnType:"comma",limit:2,multiple:!0},{field:"videoUrl",label:"现场视频：",type:"file",limit:2,returnType:"comma",tips:"只能上传视频文件,最多上传2个，大小不能超过100M"},{field:"audioUrl",label:"现场录音：",type:"file",limit:2,returnType:"comma",tips:"只能上传音频文件,最多上传2个，大小不能超过4M"},{field:"otherFileUrl",label:"其它附件：",type:"file",limit:2,returnType:"comma",tips:"只能上传文件,最多上传2个，大小不能超过4M"},{field:"processRemark",label:"备注：",type:"textarea"}]}],submit:e=>{g("确定提交？","提示信息").then(async()=>{var t,r,a,o,i,p;n.submitting=!0;try{const c=await C((t=s.currentRow)==null?void 0:t.id,{processRemark:"处理",processAdditionalInfo:JSON.stringify(e),stage:"PROCESSING",nextProcessUserId:y(((a=(r=D().user)==null?void 0:r.id)==null?void 0:a.id)||"")});((o=c.data)==null?void 0:o.code)===200?(l.success("操作成功"),(i=R.value)==null||i.closeDialog(),d()):l.error(((p=c.data)==null?void 0:p.err)||"操作失败")}catch{l.error("系统错误")}n.submitting=!1}).catch(()=>{})}}),B=u({title:"完成",dialogWidth:500,labelPosition:"top",group:[{fields:[{field:"imgUrl",label:"图片：",type:"image",returnType:"comma",limit:2,multiple:!0},{field:"audioUrl",label:"录音：",type:"file",limit:2,returnType:"comma",tips:"只能上传音频文件,最多上传2个，大小不能超过4M"},{field:"videoUrl",label:"视频：",type:"file",limit:2,returnType:"comma",tips:"只能上传视频文件,最多上传2个，大小不能超过100M"},{field:"otherFileUrl",label:"附件：",type:"file",limit:2,returnType:"comma",tips:"只能上传文件,最多上传2个，大小不能超过4M"},{field:"auditUserId",label:"审核人：",type:"select",options:[],rules:[{required:!0,message:"请选择审核人"}]},{field:"processRemark",label:"备注：",type:"textarea"}]}],submit:e=>{g("确定提交？","提示信息").then(async()=>{var t,r,a,o;n.submitting=!0;try{const i=await C((t=s.currentRow)==null?void 0:t.id,{processRemark:"完成",processAdditionalInfo:JSON.stringify(e),stage:"SUBMIT",nextProcessUserId:y(e.auditUserId)});((r=i.data)==null?void 0:r.code)===200?(l.success("操作成功"),(a=U.value)==null||a.closeDialog(),d()):l.error(((o=i.data)==null?void 0:o.err)||"操作失败")}catch{l.error("系统错误")}n.submitting=!1}).catch(()=>{})}}),L=u({title:"退单",labelPosition:"top",dialogWidth:500,group:[{fields:[{type:"textarea",label:"退单原因：",field:"processRemark",rules:[{required:!0,message:"请输入退单原因"}]},{type:"select",label:"审核人：",field:"nextProcessUserId",options:[],rules:[{required:!0,message:"请选择审核人"}]}]}],defaultValue:{processAdditionalInfo:JSON.stringify({})},submit:e=>{g("确定提交？","提示信息").then(async()=>{var t,r,a,o;n.submitting=!0;try{const i=await Ie((t=s.currentRow)==null?void 0:t.id,{processRemark:"退单申请",processAdditionalInfo:JSON.stringify(e),nextProcessUserId:y(e.nextProcessUserId)});((r=i.data)==null?void 0:r.code)===200?(l.success("操作成功"),(a=S.value)==null||a.closeDialog(),d()):l.error(((o=i.data)==null?void 0:o.err)||"操作失败")}catch{l.error("系统错误")}n.submitting=!1}).catch(()=>{})}}),Q=e=>{var t;s.currentRow=e,A.title=e.serialNo,(t=E.value)==null||t.openDrawer()},X=()=>{var e;(e=w.value)==null||e.resetForm(),d()},Y=()=>{var e;(e=N.value)==null||e.exportTable()},Z=async()=>{const e=await J(!1,{authority:"CUSTOMER_USER"}),t=n.group[0].fields[0],r=n.group[0].fields[1],a=O.group[0].fields[1],o=B.group[0].fields.find(c=>c.field==="auditUserId"),i=L.group[0].fields.find(c=>c.field==="nextProcessUserId"),p=_.group[0].fields[0];p.options=e,t.options=e,r.options=e,a.options=e,o.options=e,i.options=e},d=async()=>{var e,t,r,a,o;s.loading=!0;try{const i=((e=w.value)==null?void 0:e.queryParams)||{},[p,c]=((t=i.date)==null?void 0:t.length)===2?[b(i.date[0],I).valueOf(),b(i.date[1],I).endOf("D").valueOf()]:[b().subtract(1,"M").startOf("D").valueOf(),b().endOf("D").valueOf()],W=i.stageBetween==="ASSIGN"?["ASSIGN"]:i.stageBetween==="RESOLVING"?["RESOLVING","REJECTED"]:i.stageBetween==="SUBMIT"?["SUBMIT","APPROVED"]:["ASSIGN"],M={page:s.pagination.page,size:s.pagination.limit||20,...i,fromTime:p,toTime:c,stepProcessUserId:y(((a=(r=D().user)==null?void 0:r.id)==null?void 0:a.id)||""),stageBetween:W.join(",")};delete M.date;const G=(o=(await ve(M)).data)==null?void 0:o.data;P.value=(Math.random()*1e3).toFixed(0),s.dataList=G.data,s.pagination.total=G.total}catch{}s.loading=!1};return oe(()=>{H(),d(),Z()}),(e,t)=>{const r=pe,a=de,o=ce,i=ne;return V(),ie("div",Re,[m(r,{ref_key:"refSearch",ref:w,config:z},null,8,["config"]),(V(),ae(a,{key:P.value,ref_key:"refTable",ref:N,class:"card-table",config:s},null,8,["config"])),m(o,{ref_key:"refForm_Trans",ref:h,config:n},null,8,["config"]),m(o,{ref_key:"refForm_Colab",ref:F,config:O},null,8,["config"]),m(o,{ref_key:"refForm_Arrive",ref:k,config:j},null,8,["config"]),m(o,{ref_key:"refForm_Deal",ref:R,config:K},null,8,["config"]),m(o,{ref_key:"refForm_Comp",ref:U,config:B},null,8,["config"]),m(o,{ref_key:"refForm_ChargeBack",ref:S,config:L},null,8,["config"]),m(o,{ref_key:"refFormReview",ref:x,config:_},null,8,["config"]),m(i,{ref_key:"refdetail",ref:E,config:A},{default:se(()=>{var p;return[m(fe,{id:(p=s.currentRow)==null?void 0:p.id},null,8,["id"])]}),_:1},8,["config"])])}}});export{$e as default};
