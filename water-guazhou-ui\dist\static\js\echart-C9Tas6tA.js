import"./index-r0dFAfgr.js";const s=(e=[],t=[],a=[],i=[])=>({legend:{right:20,top:"top",type:"scroll",textStyle:{color:"rgba(255, 255, 255, 0.6)",fontSize:12}},tooltip:{trigger:"axis"},grid:{left:10,right:30,top:50,bottom:20,containLabel:!0},xAxis:{type:"category",data:e,axisLabel:{show:!0,textStyle:{color:"rgba(255, 255, 255, 0.6)"}},axisTick:{show:!1},splitLine:{show:!1}},yAxis:[{position:"left",type:"value",name:"m³",axisLine:{show:!1},axisLabel:{show:!0,textStyle:{color:"rgba(255, 255, 255, 0.6)"}},axisTick:{show:!1},splitLine:{lineStyle:{type:"dashed",color:"rgba(255,255,255,0.6)"}}},{type:"value",name:"产销差（%）",axisLabel:{show:!0,textStyle:{color:"rgba(255, 255, 255, 0.6)"}},axisTick:{show:!1},splitLine:{show:!1}}],series:[{name:"供水量",type:"bar",barWidth:10,data:t||[]},{name:"用水量",type:"bar",barWidth:10,data:a||[]},{name:"产销差",type:"line",yAxisIndex:1,data:i||[]}]});export{s as i};
