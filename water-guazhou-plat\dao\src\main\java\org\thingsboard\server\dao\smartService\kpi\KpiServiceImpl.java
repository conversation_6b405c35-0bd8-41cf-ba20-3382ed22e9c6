package org.thingsboard.server.dao.smartService.kpi;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.sql.smartService.call.CallWorkOrderMapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class KpiServiceImpl implements KpiService {
    @Autowired
    private CallWorkOrderMapper callWorkOrderMapper;

    @Override
    public List<JSONObject> getList(String startTime, String endTime, String tenantId) {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Long start = null;
        Long end = null;
        try {
            start = format.parse(startTime).getTime();
            end = format.parse(endTime).getTime() + (24 * 3600 * 1000L) - 1;
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<JSONObject> kpiList = callWorkOrderMapper.getKpiList(start, end, tenantId);
        for (JSONObject jsonObject : kpiList) {
            jsonObject.put("score", 0);
            if (jsonObject.getBigDecimal("allOrder") != null && jsonObject.getBigDecimal("completeOrder") != null && !jsonObject.getBigDecimal("allOrder").equals(BigDecimal.ZERO)) {
                jsonObject.put("score", jsonObject.getBigDecimal("completeOrder").multiply(BigDecimal.valueOf(100)).divide(jsonObject.getBigDecimal("allOrder"), RoundingMode.HALF_UP));
            }
            if (jsonObject.getBigDecimal("allEvaluate") != null && jsonObject.getBigDecimal("goodEvaluate") != null && !jsonObject.getBigDecimal("allEvaluate").equals(BigDecimal.ZERO)) {
                jsonObject.put("score", jsonObject.getBigDecimal("score").add(jsonObject.getBigDecimal("goodEvaluate").multiply(BigDecimal.valueOf(100)).divide(jsonObject.getBigDecimal("allEvaluate"), RoundingMode.HALF_UP)));
            }
        }
        kpiList.sort(Comparator.comparing(a -> a.getBigDecimal("score")));
        Collections.reverse(kpiList);

        return kpiList;
    }
}
