package org.thingsboard.server.dao.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlan;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlanResponse;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitPlanPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitPlanSaveRequest;

import java.util.List;

public interface CircuitPlanService {
    /**
     * 分页条件查询巡检计划
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<CircuitPlanResponse> findAllConditional(CircuitPlanPageRequest request);

    /**
     * 保存巡检计划
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    CircuitPlan save(CircuitPlanSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(CircuitPlan entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量删除
     *
     * @param idList 唯一标识列表
     * @return 是否删除成功
     */
    boolean deleteAll(List<String> idList);

}
