import { request } from '@/plugins/axios';

/**
 * 查询单个水源地的详细供水数据（今日、昨日、本月）、出水压力、瞬时流量曲线数据
 * @param params
 * @returns
 */
export const GetWaterSourceSupplyDetail = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/water/source/getWaterSupplyDetail',
    method: 'get',
    params
  });
};

/**
 * 查询水厂列表以及各个水厂的今日、昨日、本月供水量
 * @param params
 * @returns
 */
export const GetWaterPlantSupply = (params: {
  /** 不传则查全部 */
  projectId?: string;
  /** 站点名称 模糊 */
  name?: string;
}) => {
  return request({
    url: '/istar/api/production/waterPlant/getWaterSupplyInfo',
    method: 'get',
    params
  });
};

/**
 * 查询单个水厂的详细供水数据（今日、昨日、本月）、出水压力、瞬时流量曲线数据
 * @param params
 * @returns
 */
export const GetWaterPlantSupplyDetail = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/production/waterPlant/gis/getWaterSupplyDetail',
    method: 'get',
    params
  });
};
/**
 * 查询单个水厂的详细供水数据（今日取水、今日供水、昨日供水、本月供水）
 * @param params
 * @returns
 */
export const GetWaterPlanySupplyInfo = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/production/waterPlant/gis/getWaterInfo',
    method: 'get',
    params
  });
};

/**
 * 查询流量监测站点列表
 * @param params
 * @returns
 */
export const GetWaterFlowStationList = (params: {
  projectId?: string;
  name?: string;
  status?: string;
}) => {
  return request({
    url: '/istar/api/flowMonitoringStation/getList',
    method: 'get',
    params
  });
};
/**
 * 查询流量监测站的数据详情
 * @param params
 * @returns
 */
export const GetWaterFlowStationDetail = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/flowMonitoringStation/gis/getDataDetail',
    method: 'get',
    params
  });
};
/**
 * 查询压力监测站点列表
 * @param params
 * @returns
 */
export const GetPressureStationList = (params: {
  projectId?: string;
  name?: string;
  status?: string;
}) => {
  return request({
    url: '/istar/api/pressureMonitoringStation/getList',
    method: 'get',
    params
  });
};
/**
 * 查询阀门列表
 * @param params
 * @returns
 */
export const GetValveStationList = (params: {
  projectId?: string;
  name?: string;
  status?: string;
}) => {
  return request({
    url: '/istar/api/valveMonitoringStation/getList',
    method: 'get',
    params
  });
};
/**
 * 查询水池监测站点列表
 * @param params
 * @returns
 */
export const GetPoolMonitoringList = (params: {
  projectId?: string;
  name?: string;
  status?: string;
}) => {
  return request({
    url: '/istar/api/water/source/waterPool/getList',
    method: 'get',
    params
  });
};

/**
 * 查询压力监测站点详情
 * @param params
 * @returns
 */
export const GetPressureStationDetail = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/pressureMonitoringStation/gis/getDataDetail',
    method: 'get',
    params
  });
};
/**
 * 查询阀门详情
 * @param params
 * @returns
 */
export const GetValveStationDetail = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/valveMonitoringStation/gis/getDataDetail',
    method: 'get',
    params
  });
};
/**
 * 查询水质监测站点列表
 * @param params
 * @returns
 */
export const GetWaterQualityStaionList = (params: {
  projectId?: string;
  name?: string;
  status?: string;
}) => {
  return request({
    url: '/istar/api/waterQualityStation/getList',
    method: 'get',
    params
  });
};
/**
 * 查询水质监测站点详情
 * @param params
 * @returns
 */
export const GetWaterQualityStaionDetail = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/waterQualityStation/gis/getDataDetail',
    method: 'get',
    params
  });
};

/**
 * 查询单个二供泵房的详细供水数据（今日、昨日、本月）、出水压力、瞬时流量曲线数据
 * @param params
 * @returns
 */
export const GetBoosterPumpWaterSupplyLineData = (params: {
  stationId: string;
}) => {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyDetail',
    method: 'get',
    params
  });
};
/**
 * 查询单个二供泵房的详细供水数据（今日取水、今日供水、昨日供水、本月供水）
 * @param params
 * @returns
 */
export const GetBoosterPumpSupplyDetail = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/boosterPumpStation/gis/getWaterInfo',
    method: 'get',
    params
  });
};
/**
 * 泵站状态统计信息
 * @param params
 * @returns
 */
export const GetBoosterPumpStatusStatistic = (params: { status: string }) => {
  return request({
    url: '/istar/api/boosterPumpStation/stationStatusCount',
    method: 'get',
    params
  });
};
/**
 * 泵站曲线信息
 * @param params
 * @returns
 */
export const GetBoosterPumpDataByAttr = (params: {
  /**
   * 站点id，用逗号分隔
   */
  stationIds: string;
  /**
   * 要查询的属性
   */
  attr: string;
}) => {
  return request({
    url: '/istar/api/boosterPumpStation/getStationDataByAttr',
    method: 'get',
    params
  });
};

/**
 * 查询大用户列表
 * @param params
 * @returns
 */
export const GetBigUserList = (params: {
  projectId?: string;
  name?: string;
  status?: string;
}) => {
  return request({
    url: '/istar/api/bigUser/getList',
    method: 'get',
    params
  });
};
/**
 * 查询大用户表今日与本月的数据详情
 * @param params
 * @returns
 */
export const GetBigUserDetail = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/bigUser/gis/getDataDetail',
    method: 'get',
    params
  });
};
/**
 * 查询巡检任务状态数量
 * @param params
 * @returns
 */
export const GetCircuitTaskStatusCount = (params?: any) => {
  return request({
    url: '/api/sp/circuitTask/statusCount',
    method: 'get',
    params
  });
};
/**
 * 查询巡检任务状态数量
 * @param params
 * @returns
 */
export const GetMaintainStatusCount = (params?: any) => {
  return request({
    url: '/api/sm/maintainTask/statusCount',
    method: 'get',
    params
  });
};
