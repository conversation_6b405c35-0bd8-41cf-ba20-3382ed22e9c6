import{bF as D,d as ae,c as F,r as _,s as k,x as v,S as E,o as te,g as U,n as M,q as r,i as m,F as d,p as O,aj as W,G as S,ca as Y,aB as oe,aJ as le,h as ne,J as re,bK as se,I as pe,bU as ie,aK as me,aL as de,bW as ce,K as ue,aq as fe,al as ge,b7 as _e,ak as be,bM as he,bq as ye,C as ke}from"./index-r0dFAfgr.js";import{_ as ve}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as De}from"./CardTable-rdWOL4_6.js";import{_ as Re}from"./CardSearch-CB_HNR-Q.js";import{X as P}from"./xlsx-rVJkW9yq.js";import{j as xe,k as we,l as Fe,m as Le,n as Ne,p as Te,h as Ie,o as qe}from"./pumpRoomInfo-DV75B9MO.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const Ue=[{label:"泵房编码",prop:"pumpRoomCode",minWidth:120},{label:"泵房名称",prop:"pumpRoomName",minWidth:120},{label:"设备编码",prop:"code",minWidth:120},{label:"设备名称",prop:"name",minWidth:120},{label:"设备简称",prop:"nickname",minWidth:120},{label:"泵个数",prop:"pumpNum",minWidth:120},{label:"厂家名称",prop:"companyName",minWidth:120},{label:"设备型号",prop:"model",minWidth:120},{label:"安装人",prop:"installUserName",minWidth:120},{label:"安装日期",prop:"installDate",minWidth:120,formatter:(L,f)=>D(f).format("YYYY-MM-DD")},{label:"录入日期",prop:"createTime",minWidth:120,formatter:(L,f)=>D(f).format("YYYY-MM-DD")},{label:"性能参数",prop:"performanceParameters"},{label:"备注",prop:"remark"}],Ce=[{label:"设备名称",field:"name",type:"input",placeholder:"请输入设备名称"},{label:"设备简称",field:"nickname",type:"input",placeholder:"请输入设备简称"},{label:"泵个数",field:"pumpNum",type:"input-number",placeholder:"请输入泵个数"},{label:"厂家名称",field:"companyName",type:"input",placeholder:"请输入厂家名称"},{label:"设备型号",field:"model",type:"input",placeholder:"请输入设备型号"},{label:"安装人",field:"installUserName",type:"input",placeholder:"请输入安装人"},{label:"安装日期",field:"installDateFrom",type:"daterange"},{label:"录入日期",field:"fromTime",type:"daterange",format:"YYYY-MM-DD"}],V=[{label:"泵房",field:"pumpRoomId",prop:"pumpRoomId",type:"select",rules:[{required:!0,message:"请选择泵房"}],placeholder:"请选择泵房"},{label:"设备编码",field:"code",prop:"code",type:"input",rules:[{required:!0,message:"请填写设备编码"}],placeholder:"请填写设备编码"},{label:"设备名称",field:"name",prop:"name",type:"input",rules:[{required:!0,message:"请填写设备名称"}],placeholder:"请填写设备名称"},{label:"设备简称",field:"nickname",prop:"nickname",type:"input",rules:[{required:!0,message:"请填写设备简称"}],placeholder:"请填写设备简称"},{label:"泵个数",field:"pumpNum",prop:"pumpNum",type:"input-number",rules:[{required:!0,message:"请填写泵个数"}],placeholder:"请填写泵个数"},{label:"厂家名称",field:"companyName",prop:"companyName",type:"input",rules:[{required:!0,message:"请填写厂家名称"}],placeholder:"请填写厂家名称"},{label:"设备型号",field:"model",prop:"model",type:"input",rules:[{required:!0,message:"请填写设备型号"}],placeholder:"请填写设备型号"},{label:"安装人",field:"installUserName",prop:"installUserName",type:"input",rules:[{required:!0,message:"请填写安装人"}],placeholder:"请填写安装人"},{label:"安装日期",field:"installDate",prop:"installDate",type:"date",rules:[{required:!0,message:"请选择安装日期"}]},{label:"性能参数 ",field:"performanceParameters",prop:"performanceParameters",type:"textarea",rules:[{required:!0,message:"请填写性能参数"}],placeholder:"请填写性能参数"},{label:"备注",field:"remark",prop:"remark",type:"textarea",placeholder:"请填写备注"}],j={设备编码:"code",设备名称:"name",设备简称:"nickname",泵个数:"pumpNum",厂家名称:"companyName",设备型号:"model",安装人:"installUserName",安装日期:"installDate",性能参数:"performanceParameters",备注:"remark"},Ee=L=>new Promise(f=>{const u=new FileReader;u.onload=T=>{const w=new Uint8Array(T.target.result),b=P.read(w,{type:"array"}),p=b.Sheets[b.SheetNames[0]],R=P.utils.sheet_to_json(p);f(R)},u.readAsArrayBuffer(L.raw)}),Me={class:"wrapper"},Oe={class:"buttons"},We=ae({__name:"index",setup(L){const f=F(),u=F(),T=F(),w=F(),b=F(),p=_({pumpRooms:[],pumpRoomId:"",dataList:[]}),R=_({pumpRoomId:""}),B=_({pumpRoomId:[{required:!0,message:"请选择泵房",trigger:"blur"}]}),A=_({filters:[{type:"input",label:"泵房编码",field:"pumpRoomCode",placeholder:"请输入泵房编码"},{type:"input",label:"泵房名称",field:"pumpRoomName",placeholder:"请输入泵房名称"},{type:"input",label:"设备编码",field:"code",placeholder:"请输入设备编码"},...Ce],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:k(ge),click:()=>h()},{perm:!0,text:"重置",type:"default",svgIcon:k(_e),click:()=>{var e;(e=b.value)==null||e.resetForm()}},{perm:!0,text:"添加",type:"success",svgIcon:k(be),click:()=>{x.title="新增",C()}},{perm:!0,text:"导入",type:"warning",svgIcon:k(Y),click:()=>{var e;q(),(e=w.value)==null||e.openDialog()}},{perm:!0,text:"导出",type:"primary",svgIcon:k(W),click:()=>K()}]}]}),J=_({title:"导入",dialogWidth:1200,group:[],cancel:!0,btns:[{perm:!0,text:"确定导入",click:async()=>{var e;await((e=f.value)==null?void 0:e.validate(async a=>{a&&(p.dataList.length>0?(console.log(p.dataList),p.dataList=p.dataList.map(t=>t={...t,pumpRoomId:R.pumpRoomId}),xe(p.dataList).then(t=>{var l,o;((l=t.data)==null?void 0:l.code)===200?(q(),(o=w.value)==null||o.closeDialog(),v.success("提交成功"),h()):v.error("提交失败")}).catch(t=>{console.log(t),v.error("提交失败")})):v.warning("请导入正确的xlsx文件！"))}))}}]}),z=e=>{p.dataList=[],Ee(e).then(a=>{a&&a.forEach(o=>{const i={};for(const s in o){if(typeof j[s]>"u"){v.info("设备编码/设备名称/设备简称/泵个数/厂家名称/设备型号/安装人/安装日期/性能参数/备注; 且每行均有对应数据!");return}i[j[s]]=o[s]}p.dataList.push(i)});const t=p.dataList.map(o=>JSON.stringify(o)),l=[...new Set(t)];I.dataList=l.map(o=>JSON.parse(o))})},K=async()=>{var y;const e=((y=b.value)==null?void 0:y.queryParams)||{},[a,t]=e.installDateFrom,[l,o]=e.fromTime,i={...e,page:g.pagination.page||1,size:-1,installDateFrom:a?D(a).startOf("day").valueOf():null,installDateTo:t?D(t).startOf("day").valueOf():null,fromTime:l,toTime:o},s=await we(i),c=window.URL.createObjectURL(s.data);console.log(c);const n=document.createElement("a");n.style.display="none",n.href=c,n.setAttribute("download","机房信息列表.xlsx"),document.body.appendChild(n),n.click()},X=async()=>{const e=await qe(),a=window.URL.createObjectURL(e.data);console.log(a);const t=document.createElement("a");t.style.display="none",t.href=a,t.setAttribute("download","机房信息模板.xlsx"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(t.href)},I=_({indexVisible:!0,columns:V.slice(1),dataList:[],pagination:{hide:!0}}),g=_({indexVisible:!0,columns:Ue,operations:[{perm:!0,text:"修改",svgIcon:k(he),click:e=>{x.title="修改",C(e)}},{perm:!0,text:"删除",type:"danger",svgIcon:k(ye),click:e=>$(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:a})=>{g.pagination.page=e,g.pagination.limit=a,h()}}}),x=_({dialogWidth:600,title:"新增",labelWidth:120,group:[{fields:V}],submit:e=>{E("确定提交？","提示信息").then(()=>{x.title==="新增"?Fe(e).then(()=>{var a,t,l;(t=(a=u.value)==null?void 0:a.refForm)==null||t.resetForm(),(l=u.value)==null||l.closeDialog(),v.success("提交成功"),h()}):Le(e).then(()=>{var a,t,l;(t=(a=u.value)==null?void 0:a.refForm)==null||t.resetForm(),(l=u.value)==null||l.closeDialog(),v.success("提交成功"),h()})}).catch(()=>{})}}),C=e=>{var a;x.defaultValue=e?{...e,installDate:D(e.installDate).format()}:{},(a=u.value)==null||a.openDialog()},$=e=>{E("确定删除?","提示信息").then(()=>{Ne(e.id).then(()=>{h()})}).catch(()=>{})},h=async()=>{var c,n,y;const e=((c=b.value)==null?void 0:c.queryParams)||{},[a,t]=e.installDateFrom,[l,o]=e.fromTime,i={...e,page:g.pagination.page||1,size:g.pagination.limit||20,installDateFrom:a?D(a).startOf("day").valueOf():null,installDateTo:t?D(t).startOf("day").valueOf():null,fromTime:l,toTime:o},s=await Te(i);console.log(s.data.data.total),g.pagination.total=(n=s.data)==null?void 0:n.data.total,g.dataList=(y=s.data)==null?void 0:y.data.data},q=()=>{p.dataList=[],I.dataList=[]};return te(async()=>{var o,i,s,c;const e=await Ie({size:999,page:1}),a=(i=(o=e==null?void 0:e.data)==null?void 0:o.data)==null?void 0:i.data;p.pumpRooms=a;const t=a.map(n=>({label:n.name,value:n.id})),l=(c=(s=x.group[0])==null?void 0:s.fields)==null?void 0:c.find(n=>n.field==="pumpRoomId");l.options=t,h()}),(e,a)=>{const t=Re,l=De,o=ve,i=re,s=se,c=pe,n=ie,y=me,G=de,H=ce,Q=ue,Z=fe;return U(),M("div",Me,[r(t,{ref_key:"refSearch",ref:b,config:m(A)},null,8,["config"]),r(l,{ref_key:"refTable",ref:T,config:m(g),class:"card-table"},null,8,["config"]),r(o,{ref_key:"refForm",ref:u,config:m(x)},null,8,["config"]),r(o,{ref_key:"refUploadDialog",ref:w,config:m(J)},{default:d(()=>[r(Q,{ref_key:"ruleFormRef",ref:f,rules:m(B),model:m(R)},{default:d(()=>[r(H,null,{default:d(()=>[r(n,{span:12},{default:d(()=>[r(c,null,{default:d(()=>[O("div",Oe,[r(i,{type:"primary",icon:m(W),plain:!0,onClick:X},{default:d(()=>a[1]||(a[1]=[S(" 下载模板 ")])),_:1},8,["icon"]),r(s,{ref:"upload",action:"action",accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","show-file-list":!0,"auto-upload":!1,"on-remove":q,limit:1,"on-change":z},{tip:d(()=>a[3]||(a[3]=[O("div",{class:"el-upload__tip"}," 只能导入xlsx文件, 请确保导入的文件单元格格式为文本! ",-1)])),default:d(()=>[r(i,{type:"primary",icon:m(Y)},{default:d(()=>a[2]||(a[2]=[S(" 添加文件 ")])),_:1},8,["icon"])]),_:1},512)])]),_:1})]),_:1}),r(n,{span:12},{default:d(()=>[r(c,{label:"泵房",required:"",prop:"pumpRoomId"},{default:d(()=>[r(G,{modelValue:m(R).pumpRoomId,"onUpdate:modelValue":a[0]||(a[0]=N=>m(R).pumpRoomId=N),placeholder:"请选择泵房"},{default:d(()=>[(U(!0),M(oe,null,le(m(p).pumpRooms,(N,ee)=>(U(),ne(y,{key:ee,label:N.name,value:N.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["rules","model"]),r(Z,{config:m(I)},null,8,["config"])]),_:1},8,["config"])])}}}),ze=ke(We,[["__scopeId","data-v-12b5b912"]]);export{ze as default};
