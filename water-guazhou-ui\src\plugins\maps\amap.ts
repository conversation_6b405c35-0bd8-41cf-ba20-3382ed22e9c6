import AMapLoader from '@amap/amap-jsapi-loader'

AMapLoader.load({
  // key: 'b9ead67a4044d978a72125506b8742db', // 申请好的Web端开发者Key，首次调用 load 时必填
  key: '312ea3a03da6d14c60ea71789d1848ae',
  version: '1.4.15', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
  plugins: [
    'AMap.Autocomplete',
    'AMap.PlaceSearch',
    'AMap.Scale',
    'AMap.MapType',
    'AMap.CircleEditor'
  ] // 需要使用的的插件列表，如比例尺'AMap.Scale'等
})

export default app => {
  app.use()
  // app.use(AmapVue)
}
