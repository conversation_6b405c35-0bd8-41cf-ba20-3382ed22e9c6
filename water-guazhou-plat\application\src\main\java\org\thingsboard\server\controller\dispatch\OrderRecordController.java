
package org.thingsboard.server.controller.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.dispatch.OrderRecordService;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecord;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordExecuteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordReceiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordRejectRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordReplyRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordSaveRequest;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping({"/api/sp/orderRecord"})
public class OrderRecordController extends BaseController {
    @Autowired
    private OrderRecordService service;

    public OrderRecordController() {
    }

    @GetMapping
    public IPage<OrderRecord> findAllConditional(OrderRecordPageRequest request) {
        return this.service.findAllConditional(request);
    }

    @PostMapping
    public List<OrderRecord> save(@RequestBody List<OrderRecordSaveRequest> req) {
        return this.service.save(req);
    }

    @PostMapping({"/send"})
    public boolean send(@RequestBody List<String> idList) {
        if (idList == null || idList.size() == 0) {
            ExceptionUtils.silentThrow("未指定发送的指令", new Object[0]);
        }

        return this.service.send(idList);
    }

    @PostMapping({"/receive"})
    public boolean receive(@RequestBody OrderRecordReceiveRequest req) {
        return this.service.receive(req);
    }

    @PostMapping({"/reject"})
    public boolean reject(@RequestBody OrderRecordRejectRequest req) {
        return this.service.reject(req);
    }

    @PostMapping({"/reply"})
    public boolean reply(@RequestBody OrderRecordReplyRequest req) {
        return this.service.reply(req);
    }

    @PostMapping({"/{id}/execute"})
    public boolean execute(@RequestBody OrderRecordExecuteRequest req, @PathVariable String id) {
        req.setOrderRecordId(id);
        return this.service.execute(req);
    }

    public boolean edit(@RequestBody OrderRecordSaveRequest req, @PathVariable String id) {
        return this.service.update((OrderRecord) req.unwrap(id));
    }

    @DeleteMapping
    public boolean delete(@RequestBody List<String> idList) {
        for (String id : idList) {
            if (!this.service.isStatus(id, OrderRecordStatus.PENDING)) {
                ExceptionUtils.silentThrow("仅允许撤销未发送的指令", new Object[0]);
            }
        }

        return this.service.delete(idList);
    }
}
