import { padStart } from 'lodash-es'
import { hexToRgba } from '@/utils/GlobalHelper'
import type { ChartOption, WaterQualityIndicator } from './types'

/**
 * 初始化水质指标趋势图配置
 * @param title 图表标题
 * @param data 数据
 * @param indicatorName 指标名称
 * @param unit 单位
 * @returns 
 */
export const initWaterQualityTrendOption = (
  title: string,
  data?: { beforeYesterday: Record<string, any>; today: Record<string, any>; yesterday: Record<string, any> },
  indicatorName?: string,
  unit?: string
): ChartOption => {
  const colors = ['#459f19', '#82a12f', '#3a89ac']
  const xData: string[] = Array.from({ length: 24 }).map((item, i) => padStart(i.toString(), 2, '0'))
  const Data1: number[] = []
  const Data2: number[] = []
  const Data3: number[] = []
  const beforeYesterday = data?.beforeYesterday || {}
  const today = data?.today || {}
  const yesterday = data?.yesterday || {}
  
  for (const key in beforeYesterday || {}) {
    const cData = beforeYesterday[key]
    for (const cKey in cData) {
      Data1.push(cData[cKey])
    }
  }
  for (const key in yesterday || {}) {
    const cData = yesterday[key]
    for (const cKey in cData) {
      Data2.push(cData[cKey])
    }
  }
  for (const key in today || {}) {
    const cData = today[key]
    for (const cKey in cData) {
      Data3.push(cData[cKey])
    }
  }

  const option: ChartOption = {
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}:00<br/>`
        params.forEach((item: any) => {
          result += `${item.marker}${item.seriesName}: ${item.value}${unit || ''}<br/>`
        })
        return result
      }
    },
    legend: {
      show: true,
      right: 'center',
      top: 30,
      textStyle: {
        color: '#aaa'
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData || [],
      axisLine: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#aaa'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: `${indicatorName || ''}${unit ? `(${unit})` : ''}`,
      nameLocation: 'top',
      splitLine: {
        lineStyle: {
          color: '#aaa',
          opacity: 0.2
        }
      },
      splitArea: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#aaa'
        }
      },
      boundaryGap: [0, '100%']
    },
    grid: {
      top: 60,
      left: 50,
      right: 20,
      bottom: 30
    },
    series: [
      {
        name: '前日',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: colors[0]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgba(colors[0], 0.3)
              },
              {
                offset: 1,
                color: hexToRgba(colors[0], 0.1)
              }
            ],
            global: false
          }
        },
        data: Data1 || []
      },
      {
        name: '昨日',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: colors[1]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgba(colors[1], 0.3)
              },
              {
                offset: 1,
                color: hexToRgba(colors[1], 0.1)
              }
            ],
            global: false
          }
        },
        data: Data2 || []
      },
      {
        name: '今日',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: colors[2]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgba(colors[2], 0.3)
              },
              {
                offset: 1,
                color: hexToRgba(colors[2], 0.1)
              }
            ],
            global: false
          }
        },
        data: Data3 || []
      }
    ]
  }
  return option
}

/**
 * 初始化水质指标对比柱状图配置
 * @param title 图表标题
 * @param indicators 指标数据
 * @returns 
 */
export const initWaterQualityCompareOption = (
  title: string,
  indicators: WaterQualityIndicator[]
): ChartOption => {
  const xData = indicators.map(item => item.propertyName)
  const yData = indicators.map(item => item.value)
  const colors = indicators.map(item => {
    if (item.status === true) return '#52c41a' // 绿色-达标
    if (item.status === false) return '#ff4d4f' // 红色-超标
    return '#1890ff' // 蓝色-未知
  })

  const option: ChartOption = {
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const data = params[0]
        const indicator = indicators[data.dataIndex]
        const status = indicator.status === true ? '达标' : indicator.status === false ? '超标' : '未知'
        return `${data.axisValue}<br/>
                ${data.marker}数值: ${data.value}${indicator.unit}<br/>
                状态: <span style="color: ${data.color}">${status}</span>`
      }
    },
    xAxis: {
      type: 'category',
      data: xData,
      axisLabel: {
        rotate: 45,
        textStyle: {
          color: '#666'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '浓度值',
      axisLabel: {
        textStyle: {
          color: '#666'
        }
      }
    },
    grid: {
      top: 60,
      left: 50,
      right: 20,
      bottom: 80
    },
    series: [
      {
        name: '水质指标',
        type: 'bar',
        data: yData.map((value, index) => ({
          value,
          itemStyle: {
            color: colors[index]
          }
        })),
        barWidth: '60%'
      }
    ]
  }
  return option
}

/**
 * 生成模拟水质趋势数据
 * @param indicatorCode 指标代码
 * @param baseValue 基础值
 * @returns 
 */
export const generateMockWaterQualityTrendData = (indicatorCode: string, baseValue: number) => {
  const beforeYesterday = {}
  const yesterday = {}
  const today = {}

  // 生成24小时的模拟数据
  for (let hour = 0; hour < 24; hour++) {
    const hourKey = hour.toString().padStart(2, '0')
    
    // 前日数据
    const variation1 = Math.sin(hour * 0.3) * (baseValue * 0.1) + Math.random() * (baseValue * 0.05) - (baseValue * 0.025)
    beforeYesterday[hourKey] = {
      [hourKey]: Math.max(0, Number((baseValue + variation1).toFixed(2)))
    }

    // 昨日数据
    const variation2 = Math.sin(hour * 0.25) * (baseValue * 0.12) + Math.random() * (baseValue * 0.06) - (baseValue * 0.03)
    yesterday[hourKey] = {
      [hourKey]: Math.max(0, Number((baseValue + variation2).toFixed(2)))
    }

    // 今日数据
    const variation3 = Math.sin(hour * 0.35) * (baseValue * 0.08) + Math.random() * (baseValue * 0.04) - (baseValue * 0.02)
    today[hourKey] = {
      [hourKey]: Math.max(0, Number((baseValue + variation3).toFixed(2)))
    }
  }

  return {
    beforeYesterday,
    yesterday,
    today
  }
}

/**
 * 获取水质指标状态颜色
 * @param status 状态
 * @returns 
 */
export const getWaterQualityStatusColor = (status: boolean | null | undefined): string => {
  if (status === true) return '#52c41a' // 绿色-达标
  if (status === false) return '#ff4d4f' // 红色-超标
  return '#1890ff' // 蓝色-未知
}

/**
 * 获取水质指标状态文本
 * @param status 状态
 * @returns 
 */
export const getWaterQualityStatusText = (status: boolean | null | undefined): string => {
  if (status === true) return '达标'
  if (status === false) return '超标'
  return '未知'
}
