import{_ as x}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as v}from"./CardTable-rdWOL4_6.js";import{_ as L}from"./CardSearch-CB_HNR-Q.js";import{z as c,C as P,c as S,r as b,b as n,S as q,o as C,g as T,n as B,q as g}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function k(o){return c({url:"/api/base/database/source/list",method:"get",params:o})}function E(o){return c({url:"/api/base/database/source/getDetail",method:"get",params:{id:o}})}function M(o){return c({url:"/api/base/database/source/add",method:"post",data:o})}function N(o){return c({url:"/api/base/database/source/edit",method:"post",data:o})}function Q(o){return c({url:"/api/base/database/source/deleteIds",method:"delete",data:o})}const z={class:"wrapper"},I={__name:"databaseSource",setup(o){const f=S(),d=S(),p=(e,a,t)=>{if(a===""||a===null){t(new Error("不能为空"));return}/^\d+$/.test(a)?Number(a)<=0?t(new Error("必须大于0")):t():t(new Error("必须为正整数"))},w=b({labelWidth:"100px",filters:[{type:"input",label:"数据源名称",field:"scName",placeholder:"请输入数据源名称",onChange:()=>i()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>i()},{perm:!0,type:"primary",text:"新增",click:()=>y()},{perm:!0,type:"danger",text:"批量删除",click:()=>_()}]}],defaultParams:{}}),l=b({columns:[{label:"主键ID",prop:"id"},{label:"数据源名称",prop:"scName"},{label:"数据库类型",prop:"dbType"},{label:"服务器地址",prop:"dbHost"},{label:"端口号",prop:"dbPort"},{label:"最大连接数",prop:"dbMaxPoolSize"},{label:"最小空闲连接数",prop:"minIdle"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>D(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>y(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>_(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{l.pagination.page=e,i()},handleSize:e=>{l.pagination.limit=e,i()}},handleSelectChange:e=>{l.selectList=e||[]}}),r=b({title:"新增数据源",group:[{fields:[{type:"input",label:"数据源名称",field:"scName",rules:[{required:!0,message:"请输入数据源名称"}]},{type:"select",label:"数据库类型",field:"dbType",options:[{label:"MySQL",value:"MySQL"},{label:"Oracle",value:"Oracle"},{label:"PostgreSQL",value:"PostgreSQL"},{label:"SQL Server",value:"SQL Server"}],rules:[{required:!0,message:"请选择数据库类型"}]},{type:"input",label:"服务器地址",field:"dbHost",rules:[{required:!0,message:"请输入服务器地址"}]},{type:"input",label:"端口号",field:"dbPort",rules:[{required:!0,message:"请输入端口号"},{validator:p,trigger:"blur"}]},{type:"input",label:"数据库名称",field:"dbName",rules:[{required:!0,message:"请输入数据库名称"}]},{type:"input",label:"数据库账号",field:"dbUsername",rules:[{required:!0,message:"请输入数据库账号"}]},{type:"password",label:"数据库密码",field:"password",rules:[{required:!0,message:"请输入密码"}],showPassword:!0},{type:"input-number",label:"最大连接数",field:"dbMaxPoolSize",min:1,step:1,rules:[{required:!0,message:"请输入最大连接数"},{validator:p,trigger:"blur"}]},{type:"input-number",label:"最小空闲连接数",field:"minIdle",min:1,step:1,rules:[{required:!0,message:"请输入最小空闲连接数"},{validator:p,trigger:"blur"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var a;try{const t={...e,password:btoa(e.password)};e.id?(await N(t),n.success("修改成功")):(await M(t),n.success("新增成功")),(a=d.value)==null||a.closeDialog(),i()}catch{n.error("操作失败")}}}),h=()=>{r.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),r.showSubmit=!0,r.showCancel=!0,r.cancelText="取消",r.submitText="确定"},y=e=>{var a;h(),r.title=e?"编辑数据源":"新增数据源",r.defaultValue={...e||{}},(a=d.value)==null||a.openDialog()},D=async e=>{var a,t;try{const s=await E(e.id);let u=((a=s.data)==null?void 0:a.data)||s;u.password&&(u.password=atob(u.password)),h(),r.title="数据源详情",r.defaultValue={...u},r.group[0].fields.forEach(m=>{m.disabled=!0,m.type==="password"&&(m.showPassword=!1)}),r.showSubmit=!1,r.cancelText="关闭",(t=d.value)==null||t.openDialog()}catch{n.error("获取详情失败")}},_=e=>{q("确定删除？","删除提示").then(async()=>{var t;const a=e?[e.id]:((t=l.selectList)==null?void 0:t.map(s=>s.id))||[];if(!a.length){n.warning("请选择要删除的数据");return}await Q(a),n.success("删除成功"),i()}).catch(()=>{})},i=async()=>{var e,a;try{const t=await k({page:l.pagination.page,size:l.pagination.limit,...((e=f.value)==null?void 0:e.queryParams)||{}}),s=((a=t.data)==null?void 0:a.data)||t;l.dataList=s.records||s,l.pagination.total=s.total||s.length||0}catch{n.error("数据加载失败")}};return C(()=>{i()}),(e,a)=>{const t=L,s=v,u=x;return T(),B("div",z,[g(t,{ref_key:"refSearch",ref:f,config:w},null,8,["config"]),g(s,{class:"card-table",config:l},null,8,["config"]),g(u,{ref_key:"refDialogForm",ref:d,config:r},null,8,["config"])])}}},A=P(I,[["__scopeId","data-v-e534c333"]]);export{A as default};
