// 智慧管网-设备统计
export const sbtjData = [
  [{ label: '供水管长', value: '130.90', unit: '千米' },
    { label: '阀门', value: '785', unit: '个' },
    { label: '水表', value: '10', unit: '个' },
    { label: '流量计', value: '94', unit: '个' }],
  [{ label: '阀门井', value: '865', unit: '个' },
    { label: '压力计', value: '86', unit: '个' },
    { label: '消防栓', value: '129', unit: '个' },
    { label: '水表井', value: '65', unit: '座' }]
]

// 智慧管网-供水管网
export const gsgwData = {
  data: [1030, 103, 2030, 1630],
  xdata: ['2020-01', '2020-02', '2020-03', '2020-04'],
  xname: '',
  yname: '单位(个数)'
}

// 智慧管网-中间数据
export const zhongjianData = [
  { label: '管线长度', value: '130.90', unit: '(千米)' },
  { label: '水质监测点', value: '4', unit: '(米)' },
  { label: '测压点', value: '0', unit: '(米)' },
  { label: '测流点', value: '0', unit: '(米)' },
  { label: '大用户远传', value: '0', unit: '(米)' },
  { label: '泵站', value: '0', unit: '(米)' }
]

// 智慧管网-供水管网
export const cxcData = {
  data: [[1030, 103, 2030, 1630], [1030, 103, 2030, 1630], [1030, 103, 2030, 1630]],
  xdata: ['2020-01', '2020-02', '2020-03', '2020-04'],
  xname: '',
  yname1: '吨',
  yname2: '%'
}

// 智慧管网-分区统计
export const fqtjData = {
  name: '新天科技',
  children: [
    {
      name: '东区 20',
      children: [
        {
          name: '东一区 20'
        },
        {
          name: '东二区 30'
        },
        {
          name: '东三区 50'
        },
        {
          name: '东四区 3'
        }
      ]
    },
    {
      name: '西区 30',
      children: [
        {
          name: '西一区 20'
        },
        {
          name: '西二区 30'
        }
      ]
    }
  ]
}

// 智慧管网-漏损排行
export const lsphData = {
  data: [12.3, 11.8, 11.3, 10.2, 9.3, 8.6, 4.2],
  ydataname: ['张沟监测点', '口腔监测点', '顿河监测点', '杨树林监测点', '沙湖监测点', '西流和监测点', '北街监测点']
}

// 智慧决策-顶部概览
export const dbglData = [
  { label: '实收水费 (万元)', value: '254.13', status: '17.14%' },
  { label: '售水量 (万m3)', value: '156.15', status: '44235.4%' },
  { label: '供水量 (万m3)', value: '1.95', status: '' },
  // { label: '制水量 (万m3)', value: '5945.15', status: '3.89%' },
  // { label: '电耗量 (万度)', value: '782.21', status: '3.89%' },
  { label: '总户表量 (万只)', value: '8.15', status: '27.76%' },
  { label: '管网长度 (公里)', value: '329.08', status: '' }
]

// 智慧决策-漏损构成
export const lsgcData = [
  { name: '物理漏损量', value: 0 },
  { name: '其它', value: 0 },
  { name: '表现漏损量(管理)', value: 0 }
]

// 智慧决策-售水量
export const sslData = [
  { name: '居民用水量', value: 816266 },
  { name: '非居民用水量', value: 593221 },
  { name: '其它', value: 152449 },
  { name: '大客户水量', value: 0 }
]

// 智慧决策-服务类型
export const fwlxData = [
  { name: '爆管', value: 170 },
  { name: '施工恢复', value: 3 },
  { name: '困难用水', value: 250 },
  { name: '水表问题', value: 44 },
  { name: '漏水问题', value: 196 },
  { name: '客服问题', value: 359 }
]

// 智慧决策-漏损构成柱状图
export const lsgccolumnData = {
  xdata: ['1月', '2月'],
  data: [[116, 54], [0, 3], [214, 36], [28, 16], [136, 60], [231, 128]],
  legend: ['爆管', '施工恢复', '困难用水', '水表问题', '漏水问题', '客服问题']
}

// 智慧决策-售水量柱状图
export const WaterSalesData = {
  xdata: ['1月', '2月'],
  data: [[816266, 0], [593221, 0], [152449, 0], [0, 0]],
  legend: ['居民用水量', '非居民用水量', '其它', '大客户水量']
}

// 智慧决策-漏损柱状图
export const LeakageData = {
  xdata: ['1月', '2月'],
  data: [[0, 0], [0, 0], [0, 0]],
  legend: ['物理漏损量', '其它', '表现漏损量(管理)']
}

// 智慧生产-供水占比
export const gszbData = [
  { name: '一水厂', value: 2 },
  { name: '二水厂', value: 1 },
  { name: '三水厂', value: 4 }
]

// 智慧生产-出厂流量
export const ccllData = {
  data: [[12, 232, 32, 43, 4, 34, 5, 4, 5, 45], [121, 22, 30, 23, 32, 12, 23, 34, 22, 27], [43, 123, 43, 45, 142, 35, 65, 53, 23, 22]],
  xdata: ['1:00', '2:00', '3:00', '4:00', '5:00', '6:00', '7:00', '8:00', '9:00', '10:00'],
  legend: ['一水厂', '二水厂', '三水厂']
}

// 智慧生产-供水占比
export const gszbData_wushui = [
  { name: '一污水厂', value: 2 },
  { name: '二污水厂', value: 1 },
  { name: '三污水厂', value: 4 }
]

// 智慧生产-出厂流量
export const ccllData_wushui = {
  data: [[12, 232, 32, 43, 4, 34, 5, 4, 5, 45], [121, 22, 30, 23, 32, 12, 23, 34, 22, 27], [43, 123, 43, 45, 142, 35, 65, 53, 23, 22]],
  xdata: ['1:00', '2:00', '3:00', '4:00', '5:00', '6:00', '7:00', '8:00', '9:00', '10:00'],
  legend: ['一污水厂', '二污水厂', '三污水厂']
}

// 智慧运营-管网材质
export const gwczData = [
  { name: 'PV', value: 23 },
  { name: 'PE', value: 12 },
  { name: '聚乙烯', value: 34 },
  { name: '钢', value: 12 },
  { name: '其它', value: 2 }
]

// 智慧运营-管网口径
export const gwkjData = [
  { name: 'DN100', value: 23 },
  { name: 'DN200', value: 22 },
  { name: 'DN500', value: 14 },
  { name: 'DN800', value: 22 },
  { name: 'Dn1000', value: 6 }
]

// 智慧运营-设备类型数量
export const sblxData = {
  data: [67, 98, 67, 105, 77],
  xdata: ['阀门', '消防栓', '水表井', '流量计', '压力计']
}

// 智慧运营-设备类型数量
export const zcsbData = [
  { name: '远传表', value: 11240 },
  { name: '运行中', value: 15233 },
  { name: '大用户', value: 18562 }
]

// 智慧运营-人力构成
export const rlgcData = [
  [{ name: '高管', value: 12 },
    { name: '财务管理部', value: 7 },
    { name: '工程管理部', value: 12 }],
  [{ name: '企业发展部', value: 23 },
    { name: '客户服务中心', value: 33 },
    { name: '生产运营中心', value: 43 },
    { name: '党建办公室', value: 5 }]
]

// 智慧运营-工单类型
export const gdlxData = [
  { name: '当场答复', value: 2 },
  { name: '其它', value: 4 },
  { name: '水质问题', value: 6 },
  { name: '水压问题', value: 5 },
  { name: '设备故障', value: 5 }
]
