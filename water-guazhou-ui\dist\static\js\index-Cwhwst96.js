import{_ as Y}from"./TreeBox-DDD2iwoR.js";import{_ as R}from"./index-DyRO7tCT.js";import{_ as J}from"./index-C9hz-UZb.js";import{M as E,C as L,ak as z,bq as W,bM as G,j as K,u as H,ay as p,bo as Q,g as l,n as c,p as u,G as b,bh as _,an as o,h as d,F as h,aB as I,aJ as O,av as C,aw as g,q as y,bw as T,aK as X,aL as Z,H as $,J as ee,bb as te,br as ae,m as q,al as V,s as N,D as re,l as U,ag as le,P as ie}from"./index-r0dFAfgr.js";import{_ as ne}from"./CardSearch-CB_HNR-Q.js";import se from"./scadaItem-CITWVAAX.js";import oe from"./TreeBox-mfOmxwZJ.js";import{v as A}from"./v4-SoommWqA.js";import"./index-qoWsDjz-.js";import"./index-B69llYYW.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";import"./URLHelper-B9aplt5w.js";import"./DateFormatter-Bm9a68Ax.js";import"./Search-NSrhrIa_.js";const{$messageInfo:B,$confirm:de}=E(),ce={name:"TreeList",components:{Plus:z,Delete:W,Edit:G},props:{treeData:{type:Object,default(){return{title:"区域划分",data:Array,isFilterTree:Boolean,showAll:Boolean,allowCreate:Boolean,btnPerms:Object,allowNew:Boolean,treeNodeHandleClick:Function,expandNodeId:Array,defaultProps:Object,clickAddOrEdit:Function,allowAdd:Boolean,allowEdit:Boolean,allowDelete:Boolean,projectDelete:Function,currentProject:Object,selectFilter:Object}}}},data(){return{filterText:"",customer_disable:!1,queryParams:{}}},computed:{textColor(){return K().theme==="252C47"?"#BCC3DF":"#606266"},operationVsible(){if(!this.treeData.btnPerms)return!1;for(const t in this.treeData.btnPerms)if(this.treeData.btnPerms[t])return this.treeData.allowCreate;return!1}},watch:{filterText(t){this.$refs.tree.filter(t)}},created(){H().roles[0]==="CUSTOMER_USER"?this.treeData.allowNew&&this.treeData.allowCreate&&(this.customer_disable=!0):this.customer_disable=!1},mounted(){this.$nextTick(()=>{this.$nextTick(()=>{this.treeData.treeRef=this.$refs.tree})}),this.treeData.selectFilter&&(this.queryParams=this.treeData.selectFilter.defaultVal||{})},methods:{filterNode(t,a){return t?a[this.treeData.defaultProps.label].indexOf(t)!==-1:!0},handleCreate(){this.treeData.clickAddOrEdit(null,"created")},add(t){this.customer_disable?B("暂无权限"):t&&this.treeData.clickAddOrEdit(t,"add")},edit(t){this.customer_disable?B("暂无权限"):t&&this.treeData.clickAddOrEdit(t,"edit")},deleteHandle(t){if(this.customer_disable)B("暂无权限");else{const a=t.name?"确定要删除"+t.name+"吗?":"确定要删除吗?";de(a,"删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{t&&this.treeData.projectDelete(t.id,this)}).catch(e=>{console.log(e)})}},treeNodeHandleClick(t,a,e){this.treeData.treeNodeHandleClick(t,a,e,this.treeData)}}},ue={class:"tree-list-container"},he={key:0,class:"tree-list-title"},fe={key:1},ge={key:0,class:"switchBox"},me=["onClick"],De={class:"c-t-label"},be={class:"c-t-name"},_e={key:0,class:"hover-button"};function ye(t,a,e,i,s,n){const P=X,v=Z,x=$,m=ee,w=p("Plus"),S=p("Delete"),j=p("Edit"),f=te,M=ae;return Q((l(),c("div",ue,[u("div",{class:g({headerBoxBorder:!e.treeData.noTitle})},[e.treeData.noTitle?o("",!0):(l(),c("p",he,[a[7]||(a[7]=u("i",{class:"iconfont"},null,-1)),b(" "+_(e.treeData.title),1)])),e.treeData.selectFilter?(l(),c("div",fe,[e.treeData.selectFilter?(l(),d(v,{key:0,modelValue:s.queryParams[e.treeData.selectFilter.key],"onUpdate:modelValue":a[0]||(a[0]=r=>s.queryParams[e.treeData.selectFilter.key]=r),multiple:e.treeData.selectFilter.multiple,"collapse-tags":"",placeholder:"请选择"+e.treeData.selectFilter.label,class:"tree-filter-box",style:C([e.treeData.selectFilter.style,{width:"100%",margin:"0px 0 16px"}]),filterable:e.treeData.selectFilter.search,onChange:e.treeData.selectFilter.handleChange},{default:h(()=>[(l(!0),c(I,null,O(e.treeData.selectFilter.options,r=>(l(),d(P,{key:r.value,value:r.value,label:r.label,style:{"box-sizing":"border-box"}},null,8,["value","label"]))),128))]),_:1},8,["modelValue","multiple","placeholder","style","filterable","onChange"])):o("",!0)])):o("",!0)],2),e.treeData.switch?(l(),c("div",ge,[(l(!0),c(I,null,O(e.treeData.switch.options,r=>(l(),c("span",{key:r.name,class:g({active:e.treeData.switch.curVal===r.val}),onClick:D=>e.treeData.switch.handle(r)},_(r.name),11,me))),128))])):o("",!0),e.treeData.isFilterTree?(l(),d(x,{key:1,modelValue:s.filterText,"onUpdate:modelValue":a[1]||(a[1]=r=>s.filterText=r),placeholder:"搜索关键字",class:"tree-filter-box"},null,8,["modelValue"])):o("",!0),e.treeData.allowNew&&e.treeData.btnPerms.addBtn?(l(),d(m,{key:2,class:"tree-o-btn",type:"primary",size:"large",disabled:s.customer_disable,onClick:a[2]||(a[2]=r=>n.handleCreate(e.treeData))},{default:h(()=>[b(_(e.treeData.operationText&&e.treeData.operationText[0]||"新建项目"),1)]),_:1},8,["disabled"])):o("",!0),n.operationVsible?(l(),c("div",{key:3,class:g(["operation-btns",{"active-operation":e.treeData.currentProject}])},[e.treeData.btnPerms.addBtn&&!e.treeData.btnPerms.noChild?(l(),d(m,{key:0,disabled:s.customer_disable,style:C({width:e.treeData.nodeBtnWidth||""}),type:"warning",size:"large",onClick:a[3]||(a[3]=r=>n.add(e.treeData.currentProject))},{default:h(()=>[b(_(e.treeData.operationText&&e.treeData.operationText[1]||"子项"),1)]),_:1},8,["disabled","style"])):o("",!0),e.treeData.btnPerms.editBtn?(l(),d(m,{key:1,disabled:s.customer_disable,style:C({width:e.treeData.nodeBtnWidth||""}),class:"add-child-blue",size:"large",onClick:a[4]||(a[4]=r=>n.edit(e.treeData.currentProject))},{default:h(()=>a[8]||(a[8]=[b(" 编辑 ")])),_:1},8,["disabled","style"])):o("",!0),e.treeData.btnPerms.delBtn?(l(),d(m,{key:2,disabled:s.customer_disable,style:C({width:e.treeData.nodeBtnWidth||""}),size:"large",type:"danger",onClick:a[5]||(a[5]=r=>n.deleteHandle(e.treeData.currentProject))},{default:h(()=>a[9]||(a[9]=[b(" 删除 ")])),_:1},8,["disabled","style"])):o("",!0)],2)):o("",!0),e.treeData.showAll?(l(),c("p",{key:4,class:g(["all-project",{"active-all-project":e.treeData.activeAll}]),onClick:a[6]||(a[6]=(...r)=>e.treeData.showAllProject&&e.treeData.showAllProject(...r))},a[10]||(a[10]=[u("i",{class:"iconfont icon-xiangmu1"},null,-1),b(" 所有项目 ")]),2)):o("",!0),u("div",{class:g(["tree-list-box",{"tree-btn-list-box":n.operationVsible,"tree-f-list-box":!n.operationVsible&&!e.treeData.noTitle&&e.treeData.isFilterTree,"tree-all-list":e.treeData.showAll}])},[y(f,{ref:"tree",class:"filter-tree",accordion:"","highlight-current":"","node-key":"id","default-expand-all":"",data:e.treeData.data,props:e.treeData.defaultProps,"filter-node-method":n.filterNode,"default-checked-keys":[1],"default-expanded-keys":e.treeData.expandNodeId,onNodeClick:n.treeNodeHandleClick},{default:h(({node:r,data:D})=>[u("div",{class:g(["custom-tree-node",{"active-tree-node":e.treeData.currentProject&&e.treeData.currentProject.id===D.id,"disabled-node":D.disabled}])},[u("p",De,[u("i",{class:g(["iconfont project-icon",{"icon-xiangmu1":!r.isHost,"icon-wangguan":r.isHost}])},null,2),u("span",be,_(r.label),1)]),n.operationVsible&&!e.treeData.btnPerms.noChild?(l(),c("span",_e,[e.treeData.btnPerms.addBtn?(l(),d(w,{key:0,style:{color:"#32d1db"},size:14,onClick:T(F=>n.add(D,t.$parent,r),["stop"])},null,8,["onClick"])):o("",!0),e.treeData.btnPerms.delBtn?(l(),d(S,{key:1,style:{color:"#f56c6c"},size:14,onClick:T(F=>n.deleteHandle(D,t.$parent,r),["stop"])},null,8,["onClick"])):o("",!0),e.treeData.btnPerms.editBtn?(l(),d(j,{key:2,style:{color:"#1f9fff"},size:14,onClick:T(F=>n.edit(D,t.$parent,r),["stop"])},null,8,["onClick"])):o("",!0)])):o("",!0)],2)]),_:1},8,["data","props","filter-node-method","default-expanded-keys","onNodeClick"])],2)])),[[M,e.treeData.data&&e.treeData.loading,void 0,{body:!0}]])}const Ce=L(ce,[["render",ye],["__scopeId","data-v-e319fe63"]]);function ke(t){return q({url:"/api/zutai/dashboard-list",method:"get",params:t})}function pe(t){return q({url:`/api/zutai/dashboard-list?id=${t}`,method:"delete"})}const{$message:k}=E(),Pe={name:"Scada",components:{ScadaItem:se,TreeBox:oe},setup(){return{Search:V,Plus:z}},data(){return{totalLoading:!1,scadaList:[],detailContainer:!1,tenantId:"",cardSearchConfig:{filters:[{label:"搜索",field:"name",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:N(V),click:()=>this.refreshData()},{text:"添加组态",perm:!0,svgIcon:N(z),click:()=>{this.addOrUpdateConfig.defaultValue={originatorId:this.treeData.currentProject.id,protect:0,width:800,height:600,protectPwd:"",tenantId:this.tenantId,chartType:"cloud"},this.addOrUpdateConfig.title="添加组态",this.addOrUpdateConfig.visible=!0}}]}]},addOrUpdateConfig:{visible:!1,title:"添加设备",close:()=>{this.addOrUpdateConfig.visible=!1},addUrl:"/api/zutai/dashboard-list",editUrl:"/api/zutai/dashboard-list",defaultValue:{},externalParams:{},setSubmitParams:t=>{console.log(t),t.dashboardId||(t.dashboardId=A()),t.dashboardId||(t.dashboardId=A()),t.dashboardId=t.dashboardId.replace(/-/g,"");const a='{"pageConfig":{"width":'+t.width+',"height":'+t.height+',"backgroundColor":"#ffffff"},"assets":[],"panels":[]}';return t._id?{...t,data:a,_id:t.id}:{...t,protect:!1,protectPwd:"",checked:!1,data:a,projectId:this.treeData.currentProject.id}},height:"300px",columns:[{type:"input",label:"组态名称",key:"name",rules:[{required:!0,message:"请填写组态名称"}]},{type:"textarea",label:"备注",key:"detail"}]},treeData:{that:this,title:"区域划分",data:[],loading:!1,isFilterTree:!0,currentId:"",currentProject:{},expandNodeId:[],defaultProps:{children:"children",label:"name"},treeNodeHandleClick:t=>{this.treeData.currentProject=t,this.refreshData()}},pagination:{currentPage:1,pageSize:10,total:0}}},created(){this.tenantId=re(H().tenantId),this.refreshTree(!0)},methods:{async refreshData(t){this.detailContainer=!0;const a={page:this.pagination.currentPage,size:this.pagination.pageSize,projectId:this.treeData.currentProject.id};t||Object.assign(a,this.$refs.cardSearch.queryParams);try{const e=await ke(a);this.detailContainer=!1,e.status===200?(this.scadaList=[],this.pagination.total=e.data.total||0,e.data.data&&Array.isArray(e.data.data)?this.scadaList=e.data.data.filter(i=>i&&i.id).map(i=>(i.keyId=i.id,i.time=U(i.createTime).format("YYYY/MM/DD HH:mm:ss"),i)):e.data&&Array.isArray(e.data)?this.scadaList=e.data.filter(i=>i&&i.id).map(i=>(i.keyId=i.id,i.time=U(i.createTime).format("YYYY/MM/DD HH:mm:ss"),i)):(console.warn("组态数据格式异常:",e.data),this.scadaList=[])):k.error("获取失败")}catch(e){console.log(e),this.detailContainer=!1}},refreshTree(t){le().then(a=>{if(a.data){this.treeData.data=a.data;const e=this.treeData.data.filter(i=>!i.disabled);this.treeData.currentProject=e[0],this.totalLoading=!1,this.refreshData(t)}else k("暂无项目 不可操作，请创建项目"),this.totalLoading=!1}).catch(a=>{console.log(a),k("暂无项目 不可操作，请创建项目"),this.totalLoading=!1})},setScada(t){let a={};if(t.data)try{a=JSON.parse(t.data)}catch(i){console.log(i)}const e={...t,width:a.pageConfig?a.pageConfig.width:800,height:a.pageConfig?a.pageConfig.height:600};this.addOrUpdateConfig.defaultValue=e,this.addOrUpdateConfig.visible=!0},copyScada(t){console.log(t)},delScada(t){pe(t).then(()=>{k({type:"success",message:"删除成功"}),this.refreshData()})},handleSizeChange(t){this.pagination.pageSize=t,this.refreshData()},handleCurrentChange(t){this.pagination.currentPage=t,this.refreshData()}}},ve={class:"scadaContainer"};function xe(t,a,e,i,s,n){const P=Ce,v=ne,x=p("ScadaItem"),m=ie,w=J,S=R,j=Y;return l(),d(j,null,{tree:h(()=>[y(P,{"tree-data":s.treeData},null,8,["tree-data"])]),default:h(()=>[y(v,{ref:"cardSearch",config:s.cardSearchConfig},null,8,["config"]),y(w,{title:"组态列表",class:"card-table",overlay:""},{default:h(()=>[u("div",ve,[(l(!0),c(I,null,O(s.scadaList,f=>(l(),d(x,{key:f.id,scada:f,onSet:n.setScada,onCopy:n.copyScada,onDel:n.delScada},null,8,["scada","onSet","onCopy","onDel"]))),128))]),y(m,{"current-page":s.pagination.currentPage,"onUpdate:currentPage":a[0]||(a[0]=f=>s.pagination.currentPage=f),"page-size":s.pagination.pageSize,"onUpdate:pageSize":a[1]||(a[1]=f=>s.pagination.pageSize=f),"page-sizes":[5,10,20,50],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:s.pagination.total,onSizeChange:n.handleSizeChange,onCurrentChange:n.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])]),_:1}),s.addOrUpdateConfig.visible?(l(),d(S,{key:0,ref:"addOrUpdate",config:s.addOrUpdateConfig,onRefreshData:n.refreshData},null,8,["config","onRefreshData"])):o("",!0)]),_:1})}const He=L(Pe,[["render",xe],["__scopeId","data-v-a1198869"]]);export{He as default};
