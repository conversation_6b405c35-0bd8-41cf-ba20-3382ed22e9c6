package org.thingsboard.server.dao.sql.maintainCircuit.maintain;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainPlanM;

import java.util.Date;
import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface MaintainPlanMMapper extends BaseMapper<MaintainPlanM> {

    List<MaintainPlanM> getList(@Param("planName") String planName, @Param("teamName") String teamName, @Param("userName") String userName, @Param("startStartTime") Date startStartTime, @Param("startEndTime") Date startEndTime, @Param("endStartTime") Date endStartTime, @Param("endEndTime") Date endEndTime, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("planName") String planName, @Param("teamName") String teamName, @Param("userName") String userName, @Param("startStartTime") Date startStartTime, @Param("startEndTime") Date startEndTime, @Param("endStartTime") Date endStartTime, @Param("endEndTime") Date endEndTime, @Param("tenantId") String tenantId);

    MaintainPlanM getById(@Param("mainId") String mainId);
}
