import{_ as b}from"./DPlayer-Be2AurQX.js";import{C as k,M as z,ag as y,Z as N,g as u,n as h,p as s,q as I,F as x,aw as a,an as S,bh as m,aB as T,aJ as F,h as j,bb as O}from"./index-r0dFAfgr.js";import{f as P,d as D,c as g,M as f,a as p,b as B}from"./index-D9ERhRP6.js";import{g as E,a as R}from"./data-CfQCw447.js";import"./DPlayer.min-_HMH7IVX.js";const{$message:c,$messageError:v,$messageSuccess:q}=z(),M={name:"RealTimeVideo",components:{DPlayer:b},data(){return{videoSources:[],treeData:[],showVideo:[],dataSourceData:[],currentProject:{},currentSize:4,currentSelect:0,videoInfoMap:{},panelOpen:!0,fullScreen:!1,hiddenText:"隐藏",defaultProps:{label:"name"},command:"",currentData:{}}},created(){for(let t=0;t<this.currentSize;t++)this.showVideo.push({id:t,projectId:"",msg:"无信号"});y().then(t=>{if(t.data){this.treeData=t.data;const e=this.treeData.filter(i=>!i.disabled),l=this.$route.query.projectId,n=l?E(e,l):R(e[0]);this.$nextTick(()=>{this.$refs.dcTree?(this.$refs.dcTree.setChecked(n.id),this.$refs.dcTree.setCurrentKey(n.id),this.getProjectData(n)):this.getProjectData(e[0])}),this.currentProject=n,this.currentProId=n.id}else c("暂无项目 不可操作，请创建项目")}).catch(t=>{console.log(t),c("暂无项目 不可操作，请创建项目")})},methods:{async getProjectData(t){if(this.currentData=t,t.videoType==="3")if(window.SITE_CONFIG.SITENAME==="jingzhou"&&(this.closeVideo(this.currentSelect,"获取中"),this.getVideo(t,this.currentSelect)),t.url){const e=await N.get("http://58.42.239.112:8799/api/rtsp/hlv",{params:{rtspUrl:t.url}});console.log(e.data),t.videoUrl={m3u8uri:e.data,live:!0},this.videoInfoMap[t.serialNumber]=t,this.showVideo[this.currentSelect]=t,this.selcetVideoBox(this.currentSelect+1)}else this.showVideo[this.currentSelect].msg="视频获取中...";else t.videoType==="4"?t.url?(t.videoUrl={m3u8uri:t.url,live:!0},this.videoInfoMap[t.serialNumber]=t,this.showVideo[this.currentSelect]=t,this.selcetVideoBox(this.currentSelect+1)):this.showVideo[this.currentSelect].msg="视频获取中...":t.videoType==="5"?this.showVideo[this.currentSelect].videoUrl?(this.closeVideo(this.currentSelect,"获取中"),this.getVideo(t,this.currentSelect)):(this.showVideo[this.currentSelect].msg="视频获取中...",this.getVideo(t,this.currentSelect)):t.serialNumber?this.showVideo[this.currentSelect].videoUrl?(this.closeVideo(this.currentSelect,"获取中"),this.getVideo(t,this.currentSelect)):(this.showVideo[this.currentSelect].msg="视频获取中...",this.getVideo(t,this.currentSelect)):(this.currentProject=t,P(this.currentProject.id).then(e=>{if(e.data.length===0){c("该项目下无视频");return}const l=e.data.map(n=>{const i=this.videoSources.find(r=>r.deviceid===n.serialNumber);return n.status=i&&i.status,n.online=i&&i.online,n});t.children?t.children=t.children.filter(n=>n.nodeType==="Project").concat(l):this.$set(t,"children",l)}))},getVideo(t,e){if(this.videoInfoMap[t.serialNumber]){this.showVideo[e]=this.videoInfoMap[t.serialNumber],this.selcetVideoBox(e+1);return}D(t.id).then(l=>{const n=l.data;n?(t.videoUrl={m3u8uri:n,live:!0},this.videoInfoMap[t.serialNumber]=t,this.showVideo[e]=t,this.selcetVideoBox(this.currentSelect+1)):c("获取播放地址失败")}).catch(l=>{c.error(l.message)})},changeShowSize(t){this.currentSize=t,this.showVideo=[],this.currentSelect=0;for(let e=0;e<this.currentSize;e++)this.showVideo.push({id:e,projectId:"",msg:"无信号"})},selcetVideoBox(t){this.currentSelect=t},closeVideo(t,e){this.showVideo[t]={id:t,projectId:"",msg:e==="获取中"?"视频获取中...":"无信号"},this.hiddenText=e+" 当前："+t},async changeControl(t){if(this.command=t.action?this.command:t,!this.showVideo[this.currentSelect].id){c("请选择播放成功的窗口，进行控制");return}const e={id:this.showVideo[this.currentSelect].id,...this.command,action:t.action||0};g(e).then(l=>{l.data.code!==200?changeControl({...e,action:1}).then(n=>{n.data.code!==200?q(n.data.message):v(n.data.message)}):v(l.data.message)})},async chengeControl(t){if(!this.showVideo[this.currentSelect].serialNumber){c("请选择播放成功的窗口，进行控制");return}const e={memberkey:f,deviceid:this.showVideo[this.currentSelect].serialNumber,operator:t,speed:10},l=await p({param:JSON.stringify(e)}),n={parmdata:e,sign:l.data.sign};B(n).then(i=>{i.data.code==="0"&&i.data.msg==="设备不在线"&&c("设备不在线 不可控制")})},async changeZoom(t){if(console.log(t),!this.showVideo[this.currentSelect].serialNumber){c("请选择播放成功的窗口，进行控制");return}const e={memberkey:f,deviceid:this.showVideo[this.currentSelect].serialNumber,operator:t,speed:t===0?0:10},l=await p({param:JSON.stringify(e)}),n={parmdata:e,sign:l.data.sign};g(n).then(i=>{console.log(i)})},async changeAperture(t){if(console.log(t),!this.showVideo[this.currentSelect].serialNumber){c("请选择播放成功的窗口，进行控制");return}const e={memberkey:f,deviceid:this.showVideo[this.currentSelect].serialNumber,operator:t,speed:t===0?0:10},l=await p({param:JSON.stringify(e)}),n={parmdata:e,sign:l.data.sign};g(n).then(i=>{console.log(i)})},panelOpenChange(){this.panelOpen=!this.panelOpen},changeFullScreen(){const t=document.getElementById("full-box");document.fullScreen||document.mozFullScreen||document.webkitIsFullScreen?(document.exitFullscreen&&document.exitFullscreen(),this.fullScreen=!1):(t.requestFullscreen&&t.requestFullscreen()||t.mozRequestFullScreen&&t.mozRequestFullScreen()||t.webkitRequestFullscreen&&t.webkitRequestFullscreen()||t.msRequestFullscreen&&t.msRequestFullscreen(),this.fullScreen=!0)}}},U={class:"left-video-list"},_={class:"list-box tree-list-box"},Z={class:"custom-tree-node"},A={class:"c-t-label"},G={class:"c-t-name"},J={class:"control-box"},L={class:"direction-box"},K={class:"zoom-aperture"},H={class:"zoom-box"},W={class:"aperture-box"},Y={class:"hidden-text"},Q={class:"right-video-show"},X={class:"top-btn-control"},$={id:"full-box",class:"video-container"},ee=["onClick"],te=["onClick"],se={key:1,class:"video-msg"};function ie(t,e,l,n,i,r){const w=O,C=b;return u(),h("div",{class:a(["real-time-video-container",{"real-time-video-full-screen":i.fullScreen}])},[s("div",U,[e[24]||(e[24]=s("p",{class:"list-title"},"设备列表",-1)),s("div",_,[I(w,{ref:"dcTree",class:"data-source-s-tree",data:i.treeData,"node-key":"id","highlight-current":"","default-expand-all":"","default-checked-keys":[1],props:i.defaultProps,"expand-on-click-node":!1,onNodeClick:r.getProjectData},{default:x(({node:o,data:d})=>[s("div",Z,[s("p",A,[d.serialNumber||d.videoType==="3"?(u(),h("i",{key:0,class:a(["iconfont icon-shexiangtou",{"shexiangtou-active":d.online}])},null,2)):S("",!0),s("span",G,m(o.label),1)])])]),_:1},8,["data","props","onNodeClick"])]),i.currentData.videoType!=="3"?(u(),h("div",{key:0,class:a(["video-control",{"hidden-panel":!i.panelOpen}])},[e[23]||(e[23]=s("p",{class:"v-c-title"},"云台控制",-1)),s("div",J,[s("div",L,[s("p",{class:"c-d-btn d-btn-center",onClick:e[0]||(e[0]=o=>r.changeControl({action:"1"}))},e[16]||(e[16]=[s("i",{class:"iconfont icon-zantingtingzhi"},null,-1)])),s("p",{class:"c-d-btn",onClick:e[1]||(e[1]=o=>r.changeControl({command:"UP"}))},e[17]||(e[17]=[s("i",{class:"iconfont icon-arrowright cd-btn-l"},null,-1)])),s("p",{class:"c-d-btn",onClick:e[2]||(e[2]=o=>r.changeControl({command:"RIGHT"}))},e[18]||(e[18]=[s("i",{class:"iconfont icon-arrowright cd-btn-t"},null,-1)])),s("p",{class:"c-d-btn",onClick:e[3]||(e[3]=o=>r.changeControl({command:"LEFT"}))},e[19]||(e[19]=[s("i",{class:"iconfont icon-arrowright cd-btn-b"},null,-1)])),s("p",{class:"c-d-btn",onClick:e[4]||(e[4]=o=>r.changeControl({command:"DOWN"}))},e[20]||(e[20]=[s("i",{class:"iconfont icon-arrowright cd-btn-r"},null,-1)]))]),s("div",K,[s("div",H,[e[21]||(e[21]=s("p",{class:"z-a-title"},"变焦：",-1)),s("i",{class:"el-icon-minus c-btn c-btn-lh",style:{"font-size":"20px","font-weight":"800"},onClick:e[5]||(e[5]=o=>r.changeControl({command:"ZOOM_IN"}))},"—"),s("i",{class:"iconfont icon-zantingtingzhi c-btn",onClick:e[6]||(e[6]=o=>r.changeControl({action:"1"}))}),s("i",{class:"iconfont icon-jia c-btn c-btn-lh",onClick:e[7]||(e[7]=o=>r.changeControl({command:"ZOOM_OUT"}))})]),s("div",W,[e[22]||(e[22]=s("p",{class:"z-a-title"},"光圈：",-1)),s("i",{class:"el-icon-minus c-btn c-btn-lh",style:{"font-size":"20px","font-weight":"800"},onClick:e[8]||(e[8]=o=>r.changeControl({command:"IRIS_REDUCE"}))},"—"),s("i",{class:"iconfont icon-zantingtingzhi c-btn",onClick:e[9]||(e[9]=o=>r.changeControl({action:"1"}))}),s("i",{class:"iconfont icon-jia c-btn c-btn-lh",onClick:e[10]||(e[10]=o=>r.changeControl({command:"IRIS_ENLARGE"}))})])]),s("p",{class:"panel-control",onClick:e[11]||(e[11]=o=>r.panelOpenChange())},[s("i",{class:a(["iconfont",{"icon-arrowleft":i.panelOpen,"icon-arrowright":!i.panelOpen}])},null,2)]),s("span",Y,m(i.hiddenText),1)])],2)):S("",!0)]),s("div",Q,[s("div",X,[s("div",null,[e[25]||(e[25]=s("span",{class:"t-b-label"},"分屏：",-1)),s("i",{class:a(["iconfont icon-yiping size-btn",{"current-size":i.currentSize===1}]),onClick:e[12]||(e[12]=o=>r.changeShowSize(1))},null,2),s("i",{class:a(["iconfont icon-siping size-btn",{"current-size":i.currentSize===4}]),onClick:e[13]||(e[13]=o=>r.changeShowSize(4))},null,2),s("i",{class:a(["iconfont icon-iconjiuping size-btn",{"current-size":i.currentSize===9}]),onClick:e[14]||(e[14]=o=>r.changeShowSize(9))},null,2)]),s("p",{class:"full-screen",onClick:e[15]||(e[15]=(...o)=>r.changeFullScreen&&r.changeFullScreen(...o))},e[26]||(e[26]=[s("i",{class:"iconfont icon-quanping"},null,-1)]))]),s("div",$,[(u(!0),h(T,null,F(i.showVideo,(o,d)=>(u(),h("div",{key:d,class:a(["video-box",{"item-1-size":i.currentSize===1,"item-4-size":i.currentSize===4,"item-9-size":i.currentSize===9,"current-select":i.currentSelect===d}]),onClick:V=>r.selcetVideoBox(d)},[s("i",{class:a(["el-icon-close",{"show-close":o.videoUrl}]),onClick:V=>r.closeVideo(d,"关闭")},null,10,te),o.videoUrl?(u(),j(C,{key:0,"video-info":o},null,8,["video-info"])):(u(),h("p",se,m(o.msg),1))],10,ee))),128))])])],2)}const ae=k(M,[["render",ie],["__scopeId","data-v-316194d4"]]);export{ae as default};
