/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.menu;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class MenuPoolVO implements Serializable {
    private String id;
    private String label;
    private Integer type;
    private String path;
    private List<MenuPoolVO> children;

    public static MenuPoolVO toMenuPoolVO(MenuPool menuPool) {
        MenuPoolVO vo = new MenuPoolVO();
        vo.setId(menuPool.getId().getId().toString());
        vo.setLabel(menuPool.getDefaultName());
        vo.setType(menuPool.getType());

        return vo;
    }

    public static MenuPoolVO toMenuPoolVO(MenuCustomer menuCustomer) {
        try {
            MenuPoolVO vo = new MenuPoolVO();
            vo.setId(menuCustomer.getId().getId().toString());
            vo.setLabel(menuCustomer.getName());
            String info = menuCustomer.getAdditionalInfo();
            Map map = new ObjectMapper().readValue(info, Map.class);
            vo.setPath((String) map.get("path"));

            return vo;
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }
}
