/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.video;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.VideoConfigEntity;
import org.thingsboard.server.dao.sql.video.VideoConfigMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class VideoConfigServiceImpl implements VideoConfigService {


    @Autowired
    private VideoConfigMapper videoConfigMapper;

    @Override
    public IstarResponse save(VideoConfigEntity videoConfigEntity) {
        if (StringUtils.isBlank(videoConfigEntity.getId())) {
            // 查找配置是否存在
            VideoConfigEntity oldConfig = videoConfigMapper.selectByTenantId(videoConfigEntity.getTenantId());
            if (oldConfig == null) {
                videoConfigEntity.setCreateTime(new Date());
                videoConfigMapper.insert(videoConfigEntity);

                return IstarResponse.ok(videoConfigEntity);
            }
            videoConfigEntity.setId(oldConfig.getId());

        }

        videoConfigMapper.updateById(videoConfigEntity);
        return IstarResponse.ok(videoConfigEntity);
    }

    @Override
    public void delete(String id) {
        videoConfigMapper.deleteById(id);
    }

    @Override
    public void deleteAll(List<String> ids) {
        videoConfigMapper.deleteBatchIds(ids);
    }

    @Override
    public VideoConfigEntity getByTenantId(String tenantId) {
        VideoConfigEntity videoConfigEntity = videoConfigMapper.selectByTenantId(tenantId);
        if (videoConfigEntity == null) {
            videoConfigEntity = new VideoConfigEntity();
        }
        return videoConfigEntity;
    }

}
