package org.thingsboard.server.dao.util.imodel.query.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.thingsboard.server.dao.model.sql.plan.Plan;
import org.thingsboard.server.dao.model.sql.plan.PlanTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class PlanSaveRequest extends ComplexSaveRequest<Plan, PlanDetailSaveRequest> {

    // 计划名称
    @NotNullOrEmpty
    private String name;

    // 执行人ID
    @NotNullOrEmpty(refTable = "tb_user")
    private String executionUserId;

    // 开始时间
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    // 执行天数
    @NotNullOrEmpty
    private Integer executionDays;

    // 间隔天数
    @NotNullOrEmpty
    private Integer intervalDays;

    // 执行次数
    @NotNullOrEmpty
    private Integer executionNum;

    // 盘点类型 1-仓内 2-仓外
    @NotNullOrEmpty
    private String executionType;

    // 盘点目标仓库ID
    private String storehouseId;

    // 备注
    private String remark;

    @Override
    public String valid(IStarHttpRequest request) {
        if (executionNum <= 0) {
            return "执行次数应大于0";
        }

        int executionTypeNum = Integer.parseInt(executionType);
        if (executionTypeNum < 1 || executionTypeNum > 2) {
            return "盘点类型非法";
        } else if (executionTypeNum == 1 && storehouseId == null) {
            return "仓内盘点需要指定仓库";
        } else if (executionTypeNum == 2 && storehouseId != null) {
            storehouseId = null;
        }

        return checkItemExistence("至少应该存在一条计划项目");
    }

    @Override
    protected Plan build() {
        Plan entity = new Plan();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected Plan update(String id) {
        disallowUpdate();
        Plan entity = new Plan();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(Plan entity) {
        entity.setName(name);
        entity.setExecutionUserId(executionUserId);
        entity.setStartTime(startTime);
        entity.setExecutionDays(executionDays);
        entity.setIntervalDays(intervalDays);
        entity.setExecutionNum(executionNum);
        entity.setExecutionType(executionType);
        entity.setStorehouseId(storehouseId);
        int n = executionNum - 1;
        entity.setEndTime(new DateTime(startTime)
                .plusDays(executionDays + n * (intervalDays + executionDays)).toDate());
        entity.setRemark(remark);
    }

    @Override
    protected StringSetter<PlanDetailSaveRequest> parentSetter() {
        return PlanDetailSaveRequest::setMainId;
    }

    public List<PlanTask> generatePlans() {
        return QueryUtil.generateSequence(startTime, executionDays, intervalDays, executionNum, this::createPlanTask);
    }

    private PlanTask createPlanTask(Date beginTime, Date endTime) {
        PlanTask planTask = new PlanTask();
        planTask.setName(name);
        planTask.setExecutionType(executionType);
        planTask.setType("计划任务");
        planTask.setStatus(GeneralTaskStatus.PENDING);
        planTask.setStorehouseId(storehouseId);
        planTask.setExecutionUserId(executionUserId);
        planTask.setStartTime(beginTime);
        planTask.setEndTime(endTime);
        planTask.setCreator(currentUserUUID());
        planTask.setCreateTime(createTime());
        planTask.setTenantId(tenantId());
        return planTask;

    }

    @JsonIgnore
    private transient List<PlanTaskDetailSaveRequest> generatedPlanTaskItems;

    public List<PlanTaskDetailSaveRequest> generatePlanItems(String parentId) {
        if (generatedPlanTaskItems != null) {
            for (PlanTaskDetailSaveRequest item : generatedPlanTaskItems) {
                item.setId(null);
                item.setMainId(parentId);
            }
            return generatedPlanTaskItems;
        }
        generatedPlanTaskItems = getItems(parentId).stream().map(x -> {
            PlanTaskDetailSaveRequest item = new PlanTaskDetailSaveRequest();
            item.setMainId(parentId);
            item.setSerialId(x.getSerialId());
            item.setShelvesId(x.getShelvesId());
            item.tenantId(tenantId());
            item.currentUserId(currentUserUUID());
            return item;
        }).collect(Collectors.toList());
        return generatedPlanTaskItems;
    }

}