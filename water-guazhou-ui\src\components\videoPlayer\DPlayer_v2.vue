<template>
  <div
    class="video_box"
    @dblclick="wholeFullScreen"
  >
    <div
      ref="DPlayerIns"
      style="width: 100%; height: 100%"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import Hls from 'hls.js'
import Flv from 'flv.js'
import DPlayer from 'dplayer'

const props = defineProps<{
  videoInfo: DPlayerIns
}>()
const hls = new Hls()
const DPlayerIns = ref<HTMLDivElement>()
const dp = ref()

function init() {
  dp.value = new DPlayer({
    element: DPlayerIns.value,
    loop: props.videoInfo?.loop ?? false,
    autoplay: props.videoInfo?.autoplay ?? false, // 自动播放
    // theme: '#0093ff', //颜色
    // loop: true, //循环播放
    // lang: 'zh-cn', //语言
    // screenshot: false, //开启截图
    hotkey: props.videoInfo?.hotkey ?? false, //控制按钮
    preload: props.videoInfo?.preload ?? 'auto', //视频预加载
    // volume: 0.7, //默认音量
    preventClickToggle: true,
    live: props.videoInfo.live ?? true, // 直播模式
    mutex: false, // 多个视频播放(false允许/true不允许)
    video: {
      url: props.videoInfo.video.url,
      type: props.videoInfo.video.type || 'auto',
      customType: {
        customHls(video, player) {
          hls.loadSource(video.src)
          hls.attachMedia(video)
        },
        customFlv: function (video, player) {
          const flvPlayer = Flv.createPlayer({
            type: 'flv',
            url: video.src,
          })
          flvPlayer.attachMediaElement(video)
          flvPlayer.load()
          player.events.on('destroy', () => {
            flvPlayer.unload()
            flvPlayer.detachMediaElement()
            flvPlayer.destroy()
          })
        },
      },
    },
  })
}

// 最大化
const wholeFullScreen = () => {
  dp.value.fullScreen.request('web')
}

onMounted(() => {
  init()
})

onUnmounted(() => {
  hls.stopLoad()
  hls.detachMedia()
  dp.value = null
})
</script>

<style lang="scss" scoped>
.video_box {
  width: 100%;
  height: 100%;
  z-index: 100000;
}
</style>
