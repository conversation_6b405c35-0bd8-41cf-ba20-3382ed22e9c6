<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.DmaDictMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.DmaDict">
        select a.*
        from tb_pipe_dma_dict a
        <where>
            <if test="param.type != null and param.type != ''">
                and a.type like '%' || #{param.type} || '%'
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>