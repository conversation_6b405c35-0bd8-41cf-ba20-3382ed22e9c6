package org.thingsboard.server.dao.model.sql.smartService.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("wx_account_config")
public class WxAccountConfig {
    // id
    private String id;

    // URL
    private String url;

    // 用户Token，后台自动获取
    private String token;

    // token过期时间
    private Date expireTime;

    // 公众号名称
    private String name;

    // 公众号ID
    private String wxId;

    // 第三方用户唯一凭证
    private String appId;

    // 商户号ID
    private String mchId;

    // API密钥
    private String key;

    // 支付成功回调链接
    private String notifyUrl;

    // 第三方用户唯一凭证密钥
    private String appSecret;

    // 公众号账号
    private String account;

    // 客户id
    private String tenantId;

}
