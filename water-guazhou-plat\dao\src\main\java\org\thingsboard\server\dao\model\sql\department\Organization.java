package org.thingsboard.server.dao.model.sql.department;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("tb_organization")
public class Organization implements Identifiable {

    @TableId(type = IdType.ASSIGN_UUID)
    // ID
    private String id;

    // 供水单位名称
    private String name;

    // 单位类型
    private String type;

    // 联系电话
    private String phone;

    // 父级ID
    private String parentId;

    @TableField(exist = false)
    private String parentName;

    // 排序，升序
    private Integer orderNum;

    // 创建时间
    private Date createTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;

    private String location;

    private String styleInformation;

    //水厂类型（0水源地，1供水厂，2污水厂，3其他）
    private String orgType;

    @TableField(exist = false)
    private Integer layer;
}