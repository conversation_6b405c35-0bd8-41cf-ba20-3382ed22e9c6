/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.lot;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import org.thingsboard.server.common.data.BaseData;
import org.thingsboard.server.common.data.LotCardType;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.LotCardId;
import org.thingsboard.server.common.data.id.TenantId;

@Data
public class LotCard extends BaseData<LotCardId> {

    private DeviceId deviceId;
    private Long createTime;
    private String name;
    private JsonNode additionalInfo;
    private TenantId tenantId;
    private LotCardType type;
    //网卡ID
    private String ccid;
    //状态：使用中，已欠费，已停用
    private String status;
    //电话号码
    private String number;
    //运营商
    private String operator;
    //当前流量
    private String data_flow;

    public LotCard() {
        super();
    }

    public LotCard(LotCardId id) {
        super(id);
    }

    public LotCard(LotCard lotCard) {
        super();
        this.deviceId =lotCard.getDeviceId();
        this.createTime = lotCard.getCreateTime();
        this.additionalInfo= lotCard.getAdditionalInfo();
        this.tenantId = lotCard.getTenantId();
        this.ccid=lotCard.getCcid();
        this.name=lotCard.getName();
        this.status=lotCard.getStatus();
        this.number=lotCard.getNumber();
        this.operator=lotCard.getOperator();
        this.data_flow=lotCard.getData_flow();
        this.type=lotCard.getType();
    }

}
