<template>
  <div class="scale"></div>
</template>
<script lang="ts" setup>
// import { useScaleBar } from '@/hooks/arcgis';
import ScaleBar from '@arcgis/core/widgets/ScaleBar';

const props = defineProps<{ position?: string | __esri.UIAddPosition }>();
const view: __esri.MapView | undefined = inject('view');
const scaleBar = new ScaleBar({
  view,
  unit: 'metric',
  style: 'ruler'
});
view?.ui.add(scaleBar, props.position || 'bottom-left');
onBeforeUnmount(() => {
  scaleBar.destroy();
  view?.ui.remove(scaleBar);
});
</script>
<style lang="scss" scoped></style>
