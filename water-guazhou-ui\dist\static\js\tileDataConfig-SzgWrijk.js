import{_ as Z}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as P}from"./CardTable-rdWOL4_6.js";import{_ as q}from"./CardSearch-CB_HNR-Q.js";import{z as g,C as z,c as S,r as b,b as n,S as A,o as I,g as B,n as E,q as h}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function M(c){return g({url:"/api/base/tile/configuration/list",method:"get",params:c})}function F(c){return g({url:"/api/base/tile/configuration/getDetail",method:"get",params:{id:c}})}function _(c){return g({url:"/api/base/tile/configuration/add",method:"post",data:c})}function T(c){return g({url:"/api/base/tile/configuration/edit",method:"post",data:c})}function V(c){return g({url:"/api/base/tile/configuration/deleteIds",method:"delete",data:c})}const W={class:"wrapper"},k={__name:"tileDataConfig",setup(c){const y=S(),u=S(),m=(e,i,o)=>{if(!i){o();return}/^[0-9]+$/.test(i)?o():o(new Error("只能输入整数"))},L=(e,i,o)=>{if(!i){o();return}const a=parseFloat(i);isNaN(a)||a<0||a>1?o(new Error("透明度范围应为0-1")):o()},D=(e,i,o)=>{if(!i){o();return}const a=parseFloat(i);isNaN(a)||a<0||a>2?o(new Error("亮度范围应为0-2")):o()},C=b({labelWidth:"100px",filters:[{type:"input",label:"瓦片名称",field:"name",placeholder:"请输入瓦片名称",onChange:()=>p()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>p()},{perm:!0,type:"primary",text:"新增",click:()=>x()},{perm:!0,type:"danger",text:"批量删除",click:()=>v()}]}],defaultParams:{}}),l=b({columns:[{label:"瓦片名称",prop:"name"},{label:"瓦片类型",prop:"type"},{label:"瓦片地址",prop:"url",showOverflowTooltip:!0},{label:"格式",prop:"format"},{label:"最小缩放",prop:"minZoomLevel"},{label:"最大缩放",prop:"maxZoomLevel"},{label:"透明度",prop:"opacity"},{label:"亮度",prop:"brightness"},{label:"描述",prop:"description",showOverflowTooltip:!0}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>w(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>x(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>v(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{l.pagination.page=e,p()},handleSize:e=>{l.pagination.limit=e,p()}},handleSelectChange:e=>{l.selectList=e||[]}}),t=b({title:"新增瓦片数据配置",group:[{fields:[{type:"input",label:"瓦片名称",field:"name",rules:[{required:!0,message:"请输入瓦片名称"}]},{type:"select",label:"瓦片类型",field:"type",options:[{label:"WMTS",value:"WMTS"},{label:"TMS",value:"TMS"},{label:"XYZ",value:"XYZ"},{label:"WMS",value:"WMS"}],rules:[{required:!0,message:"请选择瓦片类型"}]},{type:"input",label:"瓦片地址",field:"url",rules:[{required:!0,message:"请输入瓦片地址"}]},{type:"select",label:"格式",field:"format",options:[{label:"PNG",value:"image/png"},{label:"JPEG",value:"image/jpeg"},{label:"WebP",value:"image/webp"},{label:"GIF",value:"image/gif"}],rules:[{required:!0,message:"请选择格式"}]},{type:"input",label:"最小缩放级别",field:"minZoomLevel",placeholder:"请输入整数",rules:[{required:!0,message:"请输入最小缩放级别"},{validator:m,trigger:"blur"}]},{type:"input",label:"最大缩放级别",field:"maxZoomLevel",placeholder:"请输入整数",rules:[{required:!0,message:"请输入最大缩放级别"},{validator:m,trigger:"blur"}]},{type:"input",label:"透明度",field:"opacity",placeholder:"请输入0-1之间的数值",rules:[{required:!0,message:"请输入透明度"},{validator:L,trigger:"blur"}]},{type:"input",label:"亮度",field:"brightness",placeholder:"请输入0-2之间的数值",rules:[{required:!0,message:"请输入亮度"},{validator:D,trigger:"blur"}]},{type:"input",label:"瓦片大小",field:"tileSize",placeholder:"例如：256",rules:[{required:!0,message:"请输入瓦片大小"},{validator:m,trigger:"blur"}]},{type:"input",label:"矩阵集",field:"matrixSet",placeholder:"请输入矩阵集标识"},{type:"textarea",label:"描述",field:"description",placeholder:"请输入描述信息"}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var i;try{if(parseInt(e.minZoomLevel)>parseInt(e.maxZoomLevel)){n.error("最小缩放级别应该小于等于最大缩放级别");return}e.id?(await T(e),n.success("修改成功")):(await _(e),n.success("新增成功")),(i=u.value)==null||i.closeDialog(),p()}catch{n.error("操作失败")}}}),f=()=>{t.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),t.showSubmit=!0,t.showCancel=!0,t.cancelText="取消",t.submitText="确定",t.submit=async e=>{var i;try{if(parseInt(e.minZoomLevel)>parseInt(e.maxZoomLevel)){n.error("最小缩放级别应该小于等于最大缩放级别");return}e.id?(await T(e),n.success("修改成功")):(await _(e),n.success("新增成功")),(i=u.value)==null||i.closeDialog(),p()}catch{n.error("操作失败")}},t.footerBtns=void 0},x=e=>{var i;f(),t.title=e?"编辑瓦片数据配置":"新增瓦片数据配置",t.defaultValue={...e||{}},(i=u.value)==null||i.openDialog()},w=async e=>{var o,a;const i={id:e.id||"1",name:e.name||"测试瓦片详情",type:e.type||"WMTS",url:e.url||"https://example.com/wmts/{z}/{x}/{y}.png",format:e.format||"image/png",minZoomLevel:e.minZoomLevel||"0",maxZoomLevel:e.maxZoomLevel||"18",opacity:e.opacity||"1.0",brightness:e.brightness||"1.0",tileSize:e.tileSize||"256",matrixSet:e.matrixSet||"EPSG:3857",description:e.description||"这是瓦片数据配置的详情数据"};try{console.log("获取详情，行数据:",e);const s=await F(e.id);console.log("详情API响应:",s);let r=null;s.data?s.data.data?r=s.data.data:r=s.data:s&&(r=s),console.log("解析后的详情数据:",r),r||(console.log("使用模拟详情数据"),r=i),f(),t.title="瓦片数据配置详情",t.defaultValue={...r},console.log("设置的详情数据:",t.defaultValue),t.group[0].fields.forEach(d=>{d.type==="select"&&(d.readonly=!0),d.disabled=!0}),t.showSubmit=!1,t.showCancel=!0,t.cancel=!0,t.cancelText="关闭",t.submitText=void 0,t.submit=void 0,t.submitting=!1,t.footerBtns=[{text:"关闭",type:"default",click:()=>{var d;(d=u.value)==null||d.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:t.showSubmit,showCancel:t.showCancel,cancel:t.cancel,cancelText:t.cancelText,submitText:t.submitText,submit:t.submit,footerBtns:t.footerBtns}),(o=u.value)==null||o.openDialog()}catch(s){console.error("获取详情失败:",s),console.log("API调用失败，使用模拟详情数据"),f(),t.title="瓦片数据配置详情",t.defaultValue={...i},t.group[0].fields.forEach(r=>{r.type==="select"&&(r.readonly=!0),r.disabled=!0}),t.showSubmit=!1,t.showCancel=!0,t.cancel=!0,t.cancelText="关闭",t.submitText=void 0,t.submit=void 0,t.submitting=!1,t.footerBtns=[{text:"关闭",type:"default",click:()=>{var r;(r=u.value)==null||r.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:t.showSubmit,showCancel:t.showCancel,cancel:t.cancel,cancelText:t.cancelText,submitText:t.submitText,submit:t.submit,footerBtns:t.footerBtns}),(a=u.value)==null||a.openDialog(),n.error("API调用失败，当前显示模拟数据")}},v=e=>{A("确定删除？","删除提示").then(async()=>{var i;try{const o=e?[e.id]:((i=l.selectList)==null?void 0:i.map(s=>s.id))||[];if(!o.length){n.warning("请选择要删除的数据");return}(await V(o)).data?(n.success("删除成功"),p()):n.error("删除失败")}catch{n.error("删除失败")}}).catch(()=>{})},p=async()=>{var i;const e=[{id:"1",name:"测试瓦片配置1",type:"WMTS",url:"https://example.com/wmts/{z}/{x}/{y}.png",format:"image/png",minZoomLevel:"0",maxZoomLevel:"18",opacity:"1.0",brightness:"1.0",tileSize:"256",matrixSet:"EPSG:3857",description:"这是一个测试瓦片配置"},{id:"2",name:"测试瓦片配置2",type:"XYZ",url:"https://example.com/xyz/{z}/{x}/{y}.jpg",format:"image/jpeg",minZoomLevel:"1",maxZoomLevel:"16",opacity:"0.8",brightness:"1.2",tileSize:"512",matrixSet:"EPSG:4326",description:"另一个测试瓦片配置"}];try{const o=(i=y.value)==null?void 0:i.queryParams;console.log("请求参数:",{page:l.pagination.page,size:l.pagination.limit,...o||{}});const a=await M({page:l.pagination.page,size:l.pagination.limit,...o||{}});console.log("API响应数据:",a),a.data?a.data.records?(l.dataList=a.data.records||[],l.pagination.total=a.data.total||0):a.data.data&&a.data.data.records?(l.dataList=a.data.data.records||[],l.pagination.total=a.data.data.total||0):Array.isArray(a.data)?(l.dataList=a.data,l.pagination.total=a.data.length):Array.isArray(a.data.data)?(l.dataList=a.data.data,l.pagination.total=a.data.data.length):(console.warn("未知的数据结构:",a.data),l.dataList=[],l.pagination.total=0):Array.isArray(a)?(l.dataList=a,l.pagination.total=a.length):(console.warn("无法解析的响应格式:",a),l.dataList=[],l.pagination.total=0),console.log("解析后的数据:",l.dataList),console.log("总数:",l.pagination.total),l.dataList.length===0&&(console.log("使用模拟数据进行测试"),l.dataList=e,l.pagination.total=e.length)}catch(o){console.error("获取数据失败:",o),console.log("API调用失败，使用模拟数据"),l.dataList=e,l.pagination.total=e.length,n.error("API调用失败，当前显示模拟数据")}};return I(()=>{p()}),(e,i)=>{const o=q,a=P,s=Z;return B(),E("div",W,[h(o,{ref_key:"refSearch",ref:y,config:C},null,8,["config"]),h(a,{class:"card-table",config:l},null,8,["config"]),h(s,{ref_key:"refDialogForm",ref:u,config:t},null,8,["config"])])}}},$=z(k,[["__scopeId","data-v-1cece77f"]]);export{$ as default};
