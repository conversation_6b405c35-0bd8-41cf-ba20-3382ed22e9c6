"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5004],{88387:(e,t,i)=>{i.r(t),i.d(t,{default:()=>ee});var r,n=i(43697),o=(i(66577),i(51773)),s=(i(16050),i(12501),i(28756),i(92271),i(72529),i(5499),i(84382)),l=i(81571),a=i(91423),d=i(32400),p=i(3172),c=i(9790),u=i(20102),y=i(50758),h=i(92604),m=i(70586),f=i(16453),b=i(95330),g=i(5600),S=i(75215),v=(i(67676),i(71715)),w=i(52011),C=i(63213),I=i(86973),_=i(87085),x=i(17287),R=i(71612),F=i(17017),k=i(69637),j=i(6404),T=i(38009),P=i(16859),L=i(34760),Z=i(72965),D=i(28294),O=i(21506),z=i(1231),N=i(53518),U=i(35671),A=i(54306),E=i(30707),M=i(96674);let q=r=class extends M.wq{constructor(){super(...arguments),this.age=null,this.ageReceived=null,this.displayCount=null,this.maxObservations=1}clone(){return new r({age:this.age,ageReceived:this.ageReceived,displayCount:this.displayCount,maxObservations:this.maxObservations})}};(0,n._)([(0,g.Cb)({type:Number,json:{write:!0}})],q.prototype,"age",void 0),(0,n._)([(0,g.Cb)({type:Number,json:{write:!0}})],q.prototype,"ageReceived",void 0),(0,n._)([(0,g.Cb)({type:Number,json:{write:!0}})],q.prototype,"displayCount",void 0),(0,n._)([(0,g.Cb)({type:Number,json:{write:!0}})],q.prototype,"maxObservations",void 0),q=r=(0,n._)([(0,w.j)("esri.layers.support.PurgeOptions")],q);const V=q;var G=i(40555),W=i(14165),Y=i(32163),J=i(86787),Q=i(82971),$=i(6570);const H=(0,N.v)();function B(e,t){return new u.Z("layer:unsupported",`Layer (${e.title}, ${e.id}) of type '${e.declaredClass}' ${t}`,{layer:e})}let K=class extends((0,j.M)((0,k.b)((0,R.h)((0,D.n)((0,Z.M)((0,L.Q)((0,x.Y)((0,T.q)((0,P.I)((0,f.R)((0,F.N)(_.Z)))))))))))){constructor(...e){super(...e),this.copyright=null,this.definitionExpression=null,this.displayField=null,this.elevationInfo=null,this.fields=null,this.fieldsIndex=null,this.geometryDefinition=null,this.geometryType=null,this.labelsVisible=!0,this.labelingInfo=null,this.legendEnabled=!0,this.maxReconnectionAttempts=0,this.maxReconnectionInterval=20,this.maxScale=0,this.minScale=0,this.objectIdField=null,this.operationalLayerType="ArcGISStreamLayer",this.popupEnabled=!0,this.popupTemplate=null,this.purgeOptions=new V,this.screenSizePerspectiveEnabled=!0,this.sourceJSON=null,this.spatialReference=Q.Z.WGS84,this.type="stream",this.url=null,this.updateInterval=300,this.webSocketUrl=null}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}load(e){if(!("WebSocket"in globalThis))return this.addResolvingPromise(Promise.reject(new u.Z("stream-layer:websocket-unsupported","WebSocket is not supported in this browser. StreamLayer will not have real-time connection with the stream service."))),Promise.resolve(this);const t=(0,m.pC)(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Stream Service","Feed"]},e).catch(b.r9).then((()=>this._fetchService(t)))),Promise.resolve(this)}get defaultPopupTemplate(){return this.createPopupTemplate()}set renderer(e){(0,U.YN)(e,this.fieldsIndex),this._set("renderer",e)}readRenderer(e,t,i){const r=(t=t.layerDefinition||t).drawingInfo&&t.drawingInfo.renderer||void 0;if(r){const e=(0,a.a)(r,t,i)||void 0;return e||h.Z.getLogger(this.declaredClass).error("Failed to create renderer",{rendererDefinition:t.drawingInfo.renderer,layer:this,context:i}),e}if(t.defaultSymbol)return t.types&&t.types.length?new l.Z({defaultSymbol:X(t.defaultSymbol,t,i),field:t.typeIdField,uniqueValueInfos:t.types.map((e=>({id:e.id,symbol:X(e.symbol,e,i)})))}):new s.Z({symbol:X(t.defaultSymbol,t,i)})}async connect(e){const[{createConnection:t}]=await Promise.all([Promise.all([i.e(4599),i.e(5660)]).then(i.bind(i,95047)),this.load()]),r=this.geometryType?I.M.toJSON(this.geometryType):null,{customParameters:n=null,definitionExpression:o=null,geometryDefinition:s=null,maxReconnectionAttempts:l=0,maxReconnectionInterval:a=20,spatialReference:d=this.spatialReference}=e||this.createConnectionParameters(),p=t(this.parsedUrl,this.spatialReference,d,r,{geometry:s,where:o},l,a,n??void 0),c=(0,y.AL)([this.on("send-message-to-socket",(e=>p.sendMessageToSocket(e))),this.on("send-message-to-client",(e=>p.sendMessageToClient(e)))]);return p.once("destroy",c.remove),p}createConnectionParameters(){return{spatialReference:this.spatialReference,customParameters:this.customParameters,definitionExpression:this.definitionExpression,geometryDefinition:this.geometryDefinition,maxReconnectionAttempts:this.maxReconnectionAttempts,maxReconnectionInterval:this.maxReconnectionInterval}}createPopupTemplate(e){return(0,Y.eZ)(this,e)}createQuery(){const e=new W.Z;return e.returnGeometry=!0,e.outFields=["*"],e.where=this.definitionExpression||"1=1",e}getFieldDomain(e,t){if(!this.fields)return null;let i=null;return this.fields.some((t=>(t.name===e&&(i=t.domain),!!i))),i}getField(e){return this.fieldsIndex.get(e)}serviceSupportsSpatialReference(e){return!0}sendMessageToSocket(e){this.emit("send-message-to-socket",e)}sendMessageToClient(e){this.emit("send-message-to-client",e)}write(e,t){const i=t?.messages;return this.webSocketUrl?(i?.push(B(this,"using a custom websocket connection cannot be written to web scenes and web maps")),null):this.parsedUrl?super.write(e,t):(i?.push(B(this,"using a client-side only connection without a url cannot be written to web scenes and web maps")),null)}async _fetchService(e){if(!this.webSocketUrl&&this.parsedUrl){if(!this.sourceJSON){const{data:t}=await(0,p.default)(this.parsedUrl.path,{query:{f:"json",...this.customParameters,...this.parsedUrl.query},responseType:"json",signal:e});this.sourceJSON=t}}else{if(!this.timeInfo?.trackIdField)throw new u.Z("stream-layer:missing-metadata","The stream layer trackIdField must be specified.");if(!this.objectIdField){const e=this.fields.find((e=>"oid"===e.type))?.name;if(!e)throw new u.Z("stream-layer:missing-metadata","The stream layer objectIdField must be specified.");this.objectIdField=e}if(!this.fields)throw new u.Z("stream-layer:missing-metadata","The stream layer fields must be specified.");if(this.fields.some((e=>e.name===this.objectIdField))||this.fields.push(new z.Z({name:this.objectIdField,alias:this.objectIdField,type:"oid"})),!this.geometryType)throw new u.Z("stream-layer:missing-metadata","The stream layer geometryType must be specified.");this.webSocketUrl&&(this.url=this.webSocketUrl)}return this.read(this.sourceJSON,{origin:"service",portalItem:this.portalItem,portal:this.portalItem?.portal,url:this.parsedUrl}),(0,U.YN)(this.renderer,this.fieldsIndex),(0,U.UF)(this.timeInfo,this.fieldsIndex),this.objectIdField||(this.objectIdField="__esri_stream_id__"),(0,G.y)(this,{origin:"service"})}};(0,n._)([(0,g.Cb)({type:String})],K.prototype,"copyright",void 0),(0,n._)([(0,g.Cb)({readOnly:!0})],K.prototype,"defaultPopupTemplate",null),(0,n._)([(0,g.Cb)({type:String,json:{name:"layerDefinition.definitionExpression",write:{enabled:!0,allowNull:!0}}})],K.prototype,"definitionExpression",void 0),(0,n._)([(0,g.Cb)({type:String})],K.prototype,"displayField",void 0),(0,n._)([(0,g.Cb)({type:J.Z})],K.prototype,"elevationInfo",void 0),(0,n._)([(0,g.Cb)(H.fields)],K.prototype,"fields",void 0),(0,n._)([(0,g.Cb)(H.fieldsIndex)],K.prototype,"fieldsIndex",void 0),(0,n._)([(0,g.Cb)({type:$.Z})],K.prototype,"geometryDefinition",void 0),(0,n._)([(0,g.Cb)({type:I.M.apiValues,json:{read:{reader:I.M.read}}})],K.prototype,"geometryType",void 0),(0,n._)([(0,g.Cb)(O.iR)],K.prototype,"labelsVisible",void 0),(0,n._)([(0,g.Cb)({type:[A.Z],json:{read:{source:"layerDefinition.drawingInfo.labelingInfo",reader:E.r},write:{target:"layerDefinition.drawingInfo.labelingInfo"}}})],K.prototype,"labelingInfo",void 0),(0,n._)([(0,g.Cb)(O.rn)],K.prototype,"legendEnabled",void 0),(0,n._)([(0,g.Cb)({type:["show","hide"]})],K.prototype,"listMode",void 0),(0,n._)([(0,g.Cb)({type:S.z8})],K.prototype,"maxReconnectionAttempts",void 0),(0,n._)([(0,g.Cb)({type:S.z8})],K.prototype,"maxReconnectionInterval",void 0),(0,n._)([(0,g.Cb)(O.u1)],K.prototype,"maxScale",void 0),(0,n._)([(0,g.Cb)(O.rO)],K.prototype,"minScale",void 0),(0,n._)([(0,g.Cb)({type:String})],K.prototype,"objectIdField",void 0),(0,n._)([(0,g.Cb)({value:"ArcGISStreamLayer",type:["ArcGISStreamLayer"]})],K.prototype,"operationalLayerType",void 0),(0,n._)([(0,g.Cb)(O.C_)],K.prototype,"popupEnabled",void 0),(0,n._)([(0,g.Cb)({type:o.Z,json:{read:{source:"popupInfo"},write:{target:"popupInfo"}}})],K.prototype,"popupTemplate",void 0),(0,n._)([(0,g.Cb)({type:V})],K.prototype,"purgeOptions",void 0),(0,n._)([(0,g.Cb)({types:d.A,json:{origins:{service:{write:{target:"drawingInfo.renderer",enabled:!1}},"web-scene":{name:"layerDefinition.drawingInfo.renderer",types:d.o,write:!0}},write:{target:"layerDefinition.drawingInfo.renderer"}}})],K.prototype,"renderer",null),(0,n._)([(0,v.r)("service","renderer",["drawingInfo.renderer","defaultSymbol"]),(0,v.r)("renderer",["layerDefinition.drawingInfo.renderer","layerDefinition.defaultSymbol"])],K.prototype,"readRenderer",null),(0,n._)([(0,g.Cb)(O.YI)],K.prototype,"screenSizePerspectiveEnabled",void 0),(0,n._)([(0,g.Cb)()],K.prototype,"sourceJSON",void 0),(0,n._)([(0,g.Cb)({type:Q.Z,json:{origins:{service:{read:{source:"spatialReference"}}}}})],K.prototype,"spatialReference",void 0),(0,n._)([(0,g.Cb)({json:{read:!1}})],K.prototype,"type",void 0),(0,n._)([(0,g.Cb)(O.HQ)],K.prototype,"url",void 0),(0,n._)([(0,g.Cb)({type:Number})],K.prototype,"updateInterval",void 0),(0,n._)([(0,g.Cb)({type:String})],K.prototype,"webSocketUrl",void 0),K=(0,n._)([(0,w.j)("esri.layers.StreamLayer")],K);const X=(0,C.d)({types:c.QT}),ee=K},17287:(e,t,i)=>{i.d(t,{Y:()=>d});var r=i(43697),n=i(92604),o=i(70586),s=i(5600),l=(i(75215),i(67676),i(52011)),a=i(66677);const d=e=>{let t=class extends e{get title(){if(this._get("title")&&"defaults"!==this.originOf("title"))return this._get("title");if(this.url){const e=(0,a.Qc)(this.url);if((0,o.pC)(e)&&e.title)return e.title}return this._get("title")||""}set title(e){this._set("title",e)}set url(e){this._set("url",(0,a.Nm)(e,n.Z.getLogger(this.declaredClass)))}};return(0,r._)([(0,s.Cb)()],t.prototype,"title",null),(0,r._)([(0,s.Cb)({type:String})],t.prototype,"url",null),t=(0,r._)([(0,l.j)("esri.layers.mixins.ArcGISService")],t),t}},40555:(e,t,i)=>{i.d(t,{y:()=>s});var r=i(66643),n=i(95330),o=i(20941);async function s(e,t,i){const s=e&&e.getAtOrigin&&e.getAtOrigin("renderer",t.origin);if(s&&"unique-value"===s.type&&s.styleOrigin){const l=await(0,r.q6)(s.populateFromStyle());if((0,n.k_)(i),!1===l.ok){const i=l.error;t&&t.messages&&t.messages.push(new o.Z("renderer:style-reference",`Failed to create unique value renderer from style reference: ${i.message}`,{error:i,context:t})),e.clear("renderer",t?.origin)}}}},51706:(e,t,i)=>{var r,n;function o(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function s(e){return null!=e&&!isNaN(e)&&isFinite(e)}function l(e){return e.valueExpression?r.Expression:e.field&&"string"==typeof e.field?r.Field:r.Unknown}function a(e,t){const i=t||l(e),o=e.valueUnit||"unknown";return i===r.Unknown?n.Constant:e.stops?n.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?n.ClampedLinear:"unknown"===o?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?n.Proportional:n.Additive:n.Identity:n.RealWorldSize}i.d(t,{PS:()=>l,QW:()=>a,RY:()=>r,hL:()=>n,iY:()=>o,qh:()=>s}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(r||(r={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(n||(n={}))},58333:(e,t,i)=>{i.d(t,{ET:()=>o,I4:()=>n,eG:()=>a,lF:()=>s,lj:()=>p,qP:()=>l,wW:()=>d});const r=[252,146,31,255],n={type:"esriSMS",style:"esriSMSCircle",size:6,color:r,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},o={type:"esriSLS",style:"esriSLSSolid",width:.75,color:r},s={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},l={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},a={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},d={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},p={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);