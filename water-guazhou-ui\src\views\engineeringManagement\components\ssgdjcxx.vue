<!-- 工程管理-详情-所属归档基础信息 -->
<template>
  <el-card class="card">
    <descriptions :config="basicConfig"></descriptions>
  </el-card>
</template>

<script lang="ts" setup>
const props = defineProps<{ config: any }>();

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => props.config) as any,
  border: true,
  direction: 'horizontal',
  column: 2,
  title: '所属归档基础信息',
  fields: [
    { type: 'text', label: '归档备注:', field: 'remark' },
    { type: 'text', label: '创建人:', field: 'creatorName' },
    { type: 'text', label: '创建时间:', field: 'createTimeName' },
    { type: 'text', label: '最后更新人:', field: 'updateUserName' },
    { type: 'text', label: '最后更新时间:', field: 'updateTimeName' }
  ]
});
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}
</style>
