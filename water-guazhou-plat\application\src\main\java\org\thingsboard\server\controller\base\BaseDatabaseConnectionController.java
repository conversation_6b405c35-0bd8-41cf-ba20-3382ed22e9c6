package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseDatabaseConnectionService;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseConnection;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseConnectionPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-数据库连接Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Api(tags = "平台管理-数据库连接")
@RestController
@RequestMapping("api/base/database/connection")
public class BaseDatabaseConnectionController extends BaseController {

    @Autowired
    private IBaseDatabaseConnectionService baseDatabaseConnectionService;

    /**
     * 查询平台管理-数据库连接列表
     */
    @MonitorPerformance(description = "平台管理-查询数据库连接列表")
    @ApiOperation(value = "查询数据库连接列表")
    @GetMapping("/list")
    public IstarResponse list(BaseDatabaseConnectionPageRequest baseDatabaseConnection) {
        return IstarResponse.ok(baseDatabaseConnectionService.selectBaseDatabaseConnectionList(baseDatabaseConnection));
    }

    /**
     * 获取平台管理-数据库连接详细信息
     */
    @MonitorPerformance(description = "平台管理-查询数据库连接详情")
    @ApiOperation(value = "查询数据库连接详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseDatabaseConnectionService.selectBaseDatabaseConnectionById(id));
    }

    /**
     * 新增平台管理-数据库连接
     */
    @MonitorPerformance(description = "平台管理-新增数据库连接")
    @ApiOperation(value = "新增数据库连接")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseDatabaseConnection baseDatabaseConnection) {
        return IstarResponse.ok(baseDatabaseConnectionService.insertBaseDatabaseConnection(baseDatabaseConnection));
    }

    /**
     * 修改平台管理-数据库连接
     */
    @MonitorPerformance(description = "平台管理-修改数据库连接")
    @ApiOperation(value = "修改数据库连接")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseDatabaseConnection baseDatabaseConnection) {
        return IstarResponse.ok(baseDatabaseConnectionService.updateBaseDatabaseConnection(baseDatabaseConnection));
    }

    /**
     * 删除平台管理-数据库连接
     */
    @MonitorPerformance(description = "平台管理-删除数据库连接")
    @ApiOperation(value = "删除数据库连接")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseDatabaseConnectionService.deleteBaseDatabaseConnectionByIds(ids));
    }
}
