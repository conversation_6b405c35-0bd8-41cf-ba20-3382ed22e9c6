{"widgetLabel": "剖切", "cancel": "取消", "hint": "通过单击并拖动场景中的表面开始剖切", "voxelHint": "通过单击并拖动场景中的表面开始剖切。 体素图层将切片视为无限平面。", "excludeHint": "在视图中选取一个图层以将其从剖切中排除", "verticalHint": "按住 Shift 键强制垂直剖切。", "verticalHint2": "按住 Shift 键后持续垂直剖切。", "unsupported": "MapView 不支持剖切。", "multipleDisabled": "一次只能有一个剖切处于活动状态", "newSlice": "新剖切", "excludedLayers": "排除的图层", "pickLayer": "从视图中选取一个图层", "excludeLayer": "排除图层", "includeLayer": "在剖切中包含图层", "ground": "地面"}