<!--压力分析 -->
<template>
  <DrawerBox
    :right-drawer="true"
    :right-drawer-bar-position="'top'"
  >
    <template #right>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>
    <div class="pressure-analysis">
      <div class="top">
        <div class="left">
          <CardSearch
            ref="refSearch"
            :config="SearchConfig"
          />
          <SLCard
            class="card-table"
            title=" "
          >
            <template #right>
              <el-radio-group
                v-model="state.activeName"
                @change="() => refreshData()"
              >
                <el-radio-button label="echarts">
                  <Icon icon="clarity:line-chart-line" />
                </el-radio-button>
                <el-radio-button label="list">
                  <Icon icon="material-symbols:table" />
                </el-radio-button>
              </el-radio-group>
            </template>
            <div
              ref="refDiv"
              class="content"
            >
              <div
                v-if="state.activeName === 'echarts'"
                ref="refDiv"
                class="chart-box"
              >
                <!-- 图表模式 -->
                <VChart
                  ref="refChart"
                  :theme="useAppStore().isDark ? 'dark' : 'light'"
                  :option="state.chartOption"
                ></VChart>
              </div>
              <!-- 列表模式 -->
              <FormTable
                v-if="state.activeName === 'list'"
                ref="refCardTable"
                class="card-table"
                :config="TableConfig"
              />
            </div>
          </SLCard>
        </div>
        <div class="right">
          <ArcLayout
            ref="refArcLayout"
            @loaded="onMapLoaded"
          >
          </ArcLayout>
        </div>
      </div>
    </div>
  </DrawerBox>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { useAppStore } from '@/store'
import { IECharts } from '@/plugins/echart'
import { useDetector } from '@/hooks/echarts'
import { usePartition, useWaterPoint } from '@/hooks/arcgis'
import { SLMessage } from '@/utils/Message'
import {
  ExportPartitionDeviceFlowOrPressure,
  GetPartitionDeviceFlowOrPressure,
  IQueryPartitionDeviceFlowOrPressureProperties
} from '@/api/mapservice/dma'
import { formatterMonth, formatterYear } from '@/utils/GlobalHelper'
import { ExportReport } from '@/views/yinshou/baobiao'
import ArcLayout from '@/components/arcMap/widgets/ArcLayout.vue'
import DrawerBox from '@/components/DrawerBox/DrawerBox.vue'

const state = reactive<{
  chartOption: any
  activeName: string
}>({
  chartOption: null,
  activeName: 'list'
})

const refChart = ref<IECharts>()
const refSearch = ref<ICardSearchIns>()
const refCardTable = ref<ICardTableIns>()
const refArcLayout = ref()
const tableData = reactive<any[]>([])
const staticState: {
  view?: __esri.MapView
  waterPointInstance?: any
} = {}

const TreeData = reactive<SLTreeConfig>({
  data: [],
  title: '选择设备',
  showCheckbox: true,
  handleCheck(ids, data) {
    TreeData.checkedKeys = data.checkedKeys || []
    TreeData.checkedNodes = data.checkedNodes || []
    resetTableColumn()
    showDevicesOnMap()
  }
})
// 搜索栏初始化配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'daterange',
    queryType: '15m',
    daterange: [moment().subtract(1, 'd').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
    month: moment().format(formatterMonth),
    year: moment().format(formatterYear)
  },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '按年', value: 'year' },
        { label: '按月', value: 'month' },
        { label: '按时间段', value: 'daterange' }
      ],
      label: '选择方式',
      onChange: value => {
        if (!refSearch.value?.queryParams) return
        refSearch.value.queryParams.queryType = value === 'year' ? '1nc' : value === 'month' ? '1d' : '15min'
      }
    },
    {
      hidden: true,
      handleHidden: (params, query, config) => (config.hidden = params.type !== 'year'),
      type: 'year',
      label: '选择年份',
      field: 'year',
      clearable: false,
      format: formatterYear,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden(params, query, formItem) {
        formItem.hidden = params.type !== 'year'
      },
      type: 'select',
      label: '时间间隔:',
      field: 'queryType',
      clearable: false,
      options: [{ label: '1月', value: '1nc' }],
      itemContainerStyle: {
        width: '180px'
      }
    },
    {
      hidden: true,
      handleHidden: (params, query, config) => (config.hidden = params.type !== 'month'),
      type: 'month',
      label: '选择月份',
      field: 'month',
      clearable: false,
      format: formatterMonth,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden(params, query, formItem) {
        formItem.hidden = params.type !== 'month'
      },
      type: 'select',
      label: '时间间隔:',
      field: 'queryType',
      clearable: false,
      options: [{ label: '1天', value: '1d' }],
      itemContainerStyle: {
        width: '180px'
      }
    },
    {
      hidden: true,
      handleHidden: (params, query, config) => (config.hidden = params.type !== 'daterange'),
      type: 'daterange',
      label: '选择日期',
      field: 'daterange',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden(params, query, formItem) {
        formItem.hidden = params.type !== 'daterange'
      },
      type: 'select',
      label: '时间间隔:',
      field: 'queryType',
      clearable: false,
      options: [
        { label: '1分钟', value: '1m' },
        { label: '5分钟', value: '5m' },
        { label: '15分钟', value: '15m' },
        { label: '30分钟', value: '30m' },
        { label: '1小时', value: '1h' }
      ],
      itemContainerStyle: {
        width: '180px'
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          disabled: () => !!TableConfig.loading,
          click: () => {
            // console.log(SearchConfig.defaultParams)
            refreshData()
          },
          iconifyIcon: 'ep:search'
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          disabled: () => !!TableConfig.loading,
          hide: () => {
            return state.activeName !== 'list'
          },
          click: () => {
            refreshData(true)
          },
          iconifyIcon: 'ep:download'
        }
      ]
    }
  ]
})
const getCheckedDevices = () => {
  const devices = TreeData.checkedNodes?.filter(item => item.data.type === '3') || []
  return devices
}
const getSubColumns = () => {
  const devices = getCheckedDevices()

  const subColumns: IFormTableColumn[] = devices.map(item => {
    return {
      minWidth: 120,
      label: item.label,
      prop: item.data?.deviceId,
      unit: '(MPa)'
    } as IFormTableColumn
  }) || []
  return subColumns
}
const resetTableColumn = () => {
  const subColumns = getSubColumns()
  TableConfig.columns = [
    { minWidth: 160, label: '时间', prop: 'time' },
    ...subColumns,
    { label: '最小站点', minWidth: 120, prop: 'minPoint' },
    { label: '最小值(MPa)', minWidth: 120, prop: 'minValue' },
    { label: '最大站点', minWidth: 120, prop: 'maxPoint' },
    { label: '最大值(MPa)', minWidth: 120, prop: 'maxValue' },
    { label: '平均值(MPa)', minWidth: 120, prop: 'avgValue' },
    { label: '合计(MPa)', minWidth: 120, prop: 'sumValue' }
  ]
}
// 初始化列表配置数据
const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  pagination: {
    page: 1,
    limit: 200,
    pageSize: [100, 200, 300, 500],
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      TableConfig.dataList = tableData?.slice((page - 1) * size, page * size)
    }
  }
})

// 刷新列表
const refreshData = async (isExport?: boolean) => {
  if (!TreeData.checkedNodes?.length) {
    SLMessage.warning('请先选择设备')
    return
  }
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const params: IQueryPartitionDeviceFlowOrPressureProperties = {
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      deviceIdList: getCheckedDevices().map(item => item.data?.deviceId),
      type: query.type === 'daterange' ? 'time' : query.type,
      interval: query.queryType,
      start: query.daterange?.[0] && moment(query.daterange?.[0]).startOf('D').valueOf(),
      end: query.daterange?.[1] && moment(query.daterange?.[1]).endOf('D').valueOf(),
      month: query.month,
      year: query.year,
      queryType: 'pressure'
    }
    if (isExport) {
      const res = await ExportPartitionDeviceFlowOrPressure(params)
      ExportReport(res.data, '压力分析报表')
    } else {
      const res = await GetPartitionDeviceFlowOrPressure(params)
      const data = res.data?.data
      TableConfig.dataList = data?.data?.map(item => {
        const obj = {
          ...item
        }
        item.data?.map(o => {
          obj[o.id] = o.value
        })
        return obj
      }) || []
      TableConfig.pagination.total = data?.total || 0
      refreshChart(getSubColumns())
      showDevicesOnMap()
    }
  } catch (error) {
    //
  }
  TableConfig.loading = false
}

// 配置加载图表数据
const refreshChart = (subColumns: any[]) => {
  if (state.activeName !== 'echarts') return
  const chartOption: any = {
    grid: {
      left: 50,
      right: 50,
      top: 50,
      bottom: 80
    },
    legend: {
      type: 'scroll',
      textStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['--']
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100
      }
    ],
    yAxis: [
      {
        position: 'left',
        type: 'value',
        name: '出口压力(Mpa)',
        axisLine: {
          show: true,
          lineStyle: {
            types: 'solid'
          }
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#656b84'
          }
        },
        splitLine: {
          lineStyle: {
            color: useAppStore().isDark ? '#303958' : '#ccc',
            type: [5, 10],
            dashOffset: 5
          }
        }
      }
    ],
    series: []
  }
  const list = TableConfig.dataList.filter(item => !!item.time)
  const xData = list.map(row => row.time)
  chartOption.xAxis.data = xData
  const datas: Record<string, number[]> = {}
  list.map(item => {
    item.data?.map(o => {
      if (datas[o.id]) datas[o.id].push(o.value)
      else {
        datas[o.id] = [o.value]
      }
    })
  })
  chartOption.series = subColumns.map(info => {
    return {
      name: info.label,
      smooth: true,
      data: datas[info.prop] || [],
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }
  })
  refChart.value?.clear()
  state.chartOption = chartOption
}

// 地图相关功能
const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view
  
  // 初始化分区
  const partitionInstance = usePartition()
  await partitionInstance.refreshPartitions(view)
  
  // 初始化水点实例
  staticState.waterPointInstance = useWaterPoint('arcmap-wrapper')
  
  // 加载设备树
  await partitionInstance.getDeviceTree({ type: 'pressure' })
  TreeData.data = partitionInstance.DeviceTree.value
  
  // 如果已经选择了设备，则在地图上显示
  if (TreeData.checkedNodes?.length) {
    showDevicesOnMap()
  }
}

// 在地图上显示设备点位
const showDevicesOnMap = async () => {
  if (!staticState.view || !staticState.waterPointInstance) return
  
  staticState.waterPointInstance.removeAll()
  
  const devices = getCheckedDevices()
  if (!devices.length) return
  
  // 获取设备数据
  const deviceData = TableConfig.dataList.length > 0 ? TableConfig.dataList[0] : null
  
  const pointData = devices.map(device => {
    // 获取设备位置信息
    const location = device.data?.location || device.data?.additionalInfo?.location
    if (!location) return null
    
    // 解析位置信息
    let x, y
    if (Array.isArray(location)) {
      [x, y] = location
    } else if (typeof location === 'string') {
      const coords = location.replace(/[\[\]]/g, '').split(',').map(Number)
      x = coords[0]
      y = coords[1]
    } else {
      return null
    }
    
    // 获取压力值
    let pressureValue = 0.3 // 默认值
    if (deviceData && deviceData[device.data?.deviceId]) {
      pressureValue = deviceData[device.data?.deviceId]
    }
    
    // 根据压力值确定颜色
    let color = '#52c41a' // 正常绿色
    if (pressureValue > 0.5) {
      color = '#ff4d4f' // 高压红色
    } else if (pressureValue < 0.2) {
      color = '#1890ff' // 欠压蓝色
    }
    
    // 创建点位
    const point = {
      type: "point",
      x: x,
      y: y,
      spatialReference: staticState.view?.spatialReference
    }
    
    return {
      id: device.data.deviceId,
      point: point,
      color: color
    }
  }).filter(Boolean)
  
  // 添加水点
  staticState.waterPointInstance.addMany(staticState.view, pointData, {
    width: 30,
    height: 30
  })
}

const detector = useDetector()
const refDiv = ref()
onMounted(() => {
  detector.listenToMush(refDiv.value, () => {
    refChart.value?.resize()
  })
})
</script>

<style lang="scss" scoped>
.pressure-analysis {
  width: 100%;
  height: 100%;
  background-color: var(--el-bg-color);
}
.top {
  height: 100%;
  width: 100%;
  display: flex;
  .left {
    width: 600px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .right {
    width: calc(100% - 600px);
    min-width: 450px;
    height: 100%;
  }
}
.chart-box,
.content {
  width: 100%;
  height: 100%;
  flex: 1;
}
.card-table {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
