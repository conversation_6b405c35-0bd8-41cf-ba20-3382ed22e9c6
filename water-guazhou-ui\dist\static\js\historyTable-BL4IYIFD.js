import{C as m,D as n,u as p,l as d,g,n as u,p as y,q as s,F as f,N as _,O as b,P as v}from"./index-r0dFAfgr.js";import{g as T}from"./index-Bj5d3Vsu.js";const C={name:"HistoryTable",props:["dialogInfo","deviceName"],data(){return{currentPage:1,titleType:"",historyList:[],getHistory:[]}},computed:{total(){return this.getHistory.length},visible(){return this.dialogInfo.visible}},created(){const t=this.dialogInfo.row;t.type==="scope"&&(this.titleType="范围告警"),t.type==="change"&&(this.titleType="变动告警"),t.type==="maintain"&&(this.titleType="维持告警"),t.type==="offline"&&(this.titleType="掉线报警");const i={deviceId:n(t.originator.id),type:t.type};if(this.$route.path==="/eventAlarm/realTimeAlarm"||this.$route.path==="/moverealTimeAlarm"){const a=n(p().id)+"."+new Date().valueOf();i.secret=btoa(a)}else{const a=this.$route.fullPath.split("=secret");i.secret=a[a.length-1]}console.log(this.$route.path,"---ls"),T(i).then(a=>{console.log(a.data,"历史");for(const e of a.data){console.log(this.deviceName,"deviceName");const o=this.deviceName.get(e.originator.id)?this.deviceName.get(e.originator.id):"设备已删除";e.createdTime=d(e.createdTime).format("YYYY-MM-DD HH:mm"),e.name=o,e.removeRemark=e.details.clearRemarks?e.details.clearRemarks:e.details.confirmRemarks,e.alarmSet=e.alarmJsonName?e.alarmJsonName:"掉线 - "+o,this.getHistory.push(e)}this.historyList=this.getHistory.slice(0,[10])})},methods:{handleCurrentChange(t){this.currentPage=t,this.historyList=this.getHistory.slice(t*10-10,[t*10])}}},N={class:"history-table-dialog"},H={class:"history-list-table"};function k(t,i,a,e,o,l){const r=_,c=b,h=v;return g(),u("div",N,[y("div",H,[s(c,{stripe:"",height:"470",data:o.historyList,class:"h-table","row-class-name":"h-table-style"},{default:f(()=>[s(r,{prop:"name",label:"告警设备",width:"180"}),s(r,{prop:"alarmSet",label:"告警名称"}),s(r,{prop:"createdTime",label:"告警时间"}),s(r,{prop:"removeRemark",label:"处理备注"})]),_:1},8,["data"]),s(h,{"page-size":10,"current-page":o.currentPage,total:l.total,layout:"total, prev, pager, next",onCurrentChange:l.handleCurrentChange},null,8,["current-page","total","onCurrentChange"])])])}const w=m(C,[["render",k]]);export{w as default};
