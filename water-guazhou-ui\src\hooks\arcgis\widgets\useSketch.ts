import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer.js'
import Sketch from '@arcgis/core/widgets/Sketch.js'

export const useSketch = () => {
  let sketch: __esri.Sketch | undefined
  const init = (view: __esri.MapView, widgetPosition?: string) => {
    const layer = new GraphicsLayer()
    view.map.add(layer)
    const sketch = new Sketch({
      view,
      layer,
      creationMode: 'update'
    }) as __esri.Sketch
    view.ui?.add(sketch, widgetPosition || 'top-right')
    return sketch
  }
  const destroy = () => {
    sketch?.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init
  }
}
export default useSketch
