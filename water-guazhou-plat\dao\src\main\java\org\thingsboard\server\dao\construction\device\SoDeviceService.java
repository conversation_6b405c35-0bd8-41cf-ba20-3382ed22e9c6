package org.thingsboard.server.dao.construction.device;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDevice;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDevicePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDeviceSaveRequest;

import java.util.List;

public interface SoDeviceService {
    /**
     * 分页条件查询工程设备
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoDevice> findAllConditional(SoDevicePageRequest request);

    /**
     * 保存工程设备
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoDevice save(SoDeviceSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoDevice entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 序列号是否已存在
     *
     * @param serialId 序列号
     * @param id       唯一标识
     * @param tenantId 客户id
     * @return 是否已存在
     */
    boolean isSerialIdExists(String serialId, String id, String tenantId);

    /**
     * 批量删除
     * @param idList id列表
     * @return 是否成功
     */
    boolean deleteBatch(List<String> idList);

}
