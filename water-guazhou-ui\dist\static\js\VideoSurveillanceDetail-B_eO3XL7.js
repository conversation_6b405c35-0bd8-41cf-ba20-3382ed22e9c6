import{d as i,c as n,o as c,g as v,n as l,dy as p,C as u}from"./index-r0dFAfgr.js";const m={class:"one-map-detail"},b=i({__name:"VideoSurveillanceDetail",emits:["refresh","mounted"],setup(f,{expose:s,emit:d}){const r=d,o=n();s({refreshDetail:async e=>{console.log(e)}}),c(()=>{r("mounted"),setTimeout(()=>{var e;window.addEventListener("resize",a),(e=o.value)==null||e.resize()},3e3)});function a(){var e;(e=o.value)==null||e.resize()}return(e,t)=>(v(),l("div",m,t[0]||(t[0]=[p('<div style="width:100%;height:100%;display:flex;justify-content:space-around;" data-v-b0751de6><video controls data-v-b0751de6><source src="" type="video/webm" data-v-b0751de6><source src="" type="video/mp4" data-v-b0751de6> Sorry, your browser doesn&#39;t support embedded videos. </video><video controls data-v-b0751de6><source src="" type="video/webm" data-v-b0751de6><source src="" type="video/mp4" data-v-b0751de6> Sorry, your browser doesn&#39;t support embedded videos. </video></div>',1)])))}}),h=u(b,[["__scopeId","data-v-b0751de6"]]);export{h as default};
