<!-- 拆迁分析 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'拆迁分析'"
    :full-content="true"
    @map-loaded="onMapLoaded"
    @detail-refreshed="state.loading = false"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import {
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds
} from '@/utils/MapHelper'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { useSketch } from '@/hooks/arcgis'

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()

const state = reactive<{
  tabs: any[]
  loading: boolean
  mounted: boolean
  layerInfos: any[]
  layerIds: any[]
  curType: 'ellipse' | 'rectangle' | 'polygon' | ''
}>({
  tabs: [],
  curType: '',
  mounted: false,
  layerInfos: [],
  layerIds: [],
  loading: false
})
const staticState: {
  view?: __esri.MapView
  sketch?: __esri.SketchViewModel
  graphicsLayer?: __esri.GraphicsLayer
  queryParams: {
    geometry?: __esri.Geometry
    where?: string
  }
} = {
  queryParams: {
    geometry: undefined,
    where: '1=1'
  }
}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制拆迁范围'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '分析',
              styles: {
                width: '100%'
              },
              loading: () => state.loading,
              click: () => startQuery()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})

const initDraw = (type: any) => {
  if (!staticState.view) return
  staticState.sketch?.create(type)
  clearGraphicsLayer()
  // state.curType = type
  // setMapCursor('crosshair')

  // staticState.drawer?.destroy()
  // staticState.drawer = initDrawer(staticState.view)
  // staticState.drawAction?.destroy()
  // staticState.drawAction = staticState.drawer?.create(type, {
  //   mode: ['ellipse', 'rectangle'].indexOf(type) !== -1 ? 'freehand' : 'click'
  // })

  // staticState.drawAction?.on(['vertex-add', 'cursor-update'], updateVertices)
  // staticState.drawAction?.on('draw-complete', async e => {
  //   updateVertices(e)
  //   setMapCursor('')
  // })
}
// const updateVertices = e => {
//   const type = state.curType
//   staticState.graphicsLayer?.removeAll()
//   if (!type) return
//   const graphic = type === 'ellipse'
//     ? createEllipse(
//       e.vertices,
//       staticState.view?.spatialReference,
//       setSymbol('polygon', {
//         color: [0, 255, 0, 0.2],
//         outlineColor: [0, 255, 0, 1],
//         outlineWidth: 1
//       })
//     )
//     : type === 'polygon'
//       ? e.vertices.length < 3
//         ? createPolyline(
//           e.vertices,
//           staticState.view?.spatialReference,
//           setSymbol('polyline', {
//             color: [0, 255, 0, 1],
//             width: 1
//           })
//         )
//         : createPolygon(
//           e.vertices,
//           staticState.view?.spatialReference,
//           setSymbol('polygon', {
//             color: [0, 255, 0, 0.2],
//             outlineColor: [0, 255, 0, 1],
//             outlineWidth: 1
//           })
//         )
//       : createRectGraphic(
//         e.vertices,
//         staticState.view?.spatialReference,
//         setSymbol('polygon', {
//           color: [0, 255, 0, 0.2],
//           outlineColor: [0, 255, 0, 1],
//           outlineWidth: 1
//         })
//       )
//   graphic && staticState.graphicsLayer?.add(graphic)
//   staticState.queryParams.geometry = graphic?.geometry
// }
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.queryParams.geometry = undefined
  refMap.value?.clearDetailData()
}

const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
}
const startQuery = async () => {
  SLMessage.info('正在分析，请稍候...')
  try {
    state.loading = true
    state.tabs.length = 0
    state.tabs = await getLayerOids(
      state.layerIds,
      state.layerInfos,
      staticState.queryParams
    )
    refMap.value?.refreshDetail(state.tabs)
  } catch (error) {
    console.log(error)
    state.loading = false
  }
}
const { initSketch, destroySketch } = useSketch()
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    staticState.queryParams.geometry = result.graphics[0]?.geometry
    // resolveMeasure()
  }
}
const onMapLoaded = view => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-analys-demolition',
    title: '拆迁分析'
  })
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  })
  getLayerInfo()
}
onBeforeUnmount(() => {
  staticState.graphicsLayer?.removeAll()
  destroySketch()
})
</script>
<style lang="scss" scoped></style>
