package org.thingsboard.server.dao.smartProduction.safeProduction;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.SafeProductionRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionTask;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

public interface ProductionTaskService {

    ProductionTask save(ProductionTask productionTask);

    PageData<ProductionTask> findList(SafeProductionRequest request);

    void delete(List<String> idList);

    ProductionTask review(ProductionTask productionTask);

    IstarResponse process(ProductionTask productionTask, String userId);

    IstarResponse receive(String id, String userId);
}
