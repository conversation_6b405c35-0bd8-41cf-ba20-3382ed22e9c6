package org.thingsboard.server.dao.optionLog;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gexin.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.request.DeviceCommandRequest;
import org.thingsboard.server.dao.model.sql.DeviceCommand;
import org.thingsboard.server.dao.sql.optionLog.DeviceCommandMapper;
import org.thingsboard.server.dao.sql.optionLog.DeviceCommandRepository;
import org.thingsboard.server.dao.util.HttpClientFactory;

import java.io.UnsupportedEncodingException;
import java.util.List;

@Slf4j
@Service
public class DeviceCommandServiceImpl implements DeviceCommandService {

    @Value("${device-command.url}")
    private String DEVICE_COMMAND_URL;

    @Value("${device-command.cancel-url}")
    private String CANCEL_DEVICE_COMMAND_URL;

    @Autowired
    private DeviceCommandRepository deviceCommandRepository;
    @Autowired
    private DeviceCommandMapper deviceCommandMapper;
    @Autowired
    private DeviceService deviceService;

    @Override
    public PageData<DeviceCommand> findList(DeviceCommandRequest request, TenantId tenantId) {
        Page<DeviceCommand> page = new Page<>(request.getPage(), request.getSize());
        IPage<DeviceCommand> pageResult = deviceCommandMapper.findList(page, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public DeviceCommand findById(String id) {
        return deviceCommandRepository.findOne(id);
    }

    @Override
    @Transactional
    public void cancelCommand(String id, String username) throws ThingsboardException {
        // 查询指令
        DeviceCommand command = this.findById(id);
        if (command != null && "1".equalsIgnoreCase(command.getOptionResult())) {
            command.setOptionResult("3");
            command.setUpdateUser(username);

            deviceCommandRepository.save(command);
            try {
                // 取消指令
                CloseableHttpClient httpClient = HttpClientFactory.getHttpClient();
                HttpPost httpPost = new HttpPost(CANCEL_DEVICE_COMMAND_URL);
                JSONObject param = new JSONObject();
                // 查询设备信息
                Device device = deviceService.findDeviceById(new DeviceId(UUIDConverter.fromString(command.getDeviceKey())));
                if (device == null) {
                    throw new ThingsboardException("要取消指令的设备不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
                }
                param.put("address", device.getAepDeviceId());
                param.put("commandId", command.getAepCommandId());
                httpPost.setEntity(new StringEntity(param.toJSONString()));

                CloseableHttpResponse execute = httpClient.execute(httpPost);
                int statusCode = execute.getStatusLine().getStatusCode();
                JSONObject commandResult = JSONObject.parseObject(EntityUtils.toString(execute.getEntity()));
                if (statusCode != 200) {
                    throw new ThingsboardException("取消指令失败, 原因: " + commandResult.getString("message"),
                            ThingsboardErrorCode.BAD_REQUEST_PARAMS);
                }
            } catch (Exception e) {
                throw new ThingsboardException("取消指令失败", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
        }
    }

    @Override
    public List<DeviceCommand> findByDeviceKeyAndOptionResult(String deviceKey, String optionResult) {
        return deviceCommandRepository.findByDeviceKeyAndOptionResult(deviceKey, optionResult);
    }

    @Override
    @Transactional
    public void postCommand(DeviceCommand command) throws ThingsboardException {
        // 查询是否已有同类指令
        List<DeviceCommand> commandList = this.findByDeviceKeyAndOptionResult(command.getDeviceKey(), "1");
        if (commandList != null && commandList.size() > 0) {
            throw new ThingsboardException("请先取消之前的指令后再发送新的指令", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        try {
            // 发送指令
            CloseableHttpClient httpClient = HttpClientFactory.getHttpClient();
            HttpPost httpPost = new HttpPost(DEVICE_COMMAND_URL);
            JSONObject param = new JSONObject();
            // 查询设备信息
            Device device = deviceService.findDeviceById(new DeviceId(UUIDConverter.fromString(command.getDeviceKey())));
            if (device == null) {
                throw new ThingsboardException("要下发指令的设备不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            param.put("address", device.getAepDeviceId());
            param.put("valve", command.getCommand());
            httpPost.setEntity(new StringEntity(param.toJSONString()));

            CloseableHttpResponse execute = httpClient.execute(httpPost);
            int statusCode = execute.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                throw new ThingsboardException("下发指令失败", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            JSONObject commandResult = JSONObject.parseObject(EntityUtils.toString(execute.getEntity()));
            command.setAepCommandId(commandResult.getString("commandId"));
        } catch (Exception e) {
            throw new ThingsboardException("下发指令失败", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 新增指令
        deviceCommandRepository.save(command);
    }
}
