/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.aspect.annotation;

import org.thingsboard.server.common.data.DataConstants;

import java.lang.annotation.*;

/**
 * 系统日志注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SysLog {

    // 日志说明
    String value() default "";

    // 日志种类 1-操作日志 2-登录日志
    int type() default 1;

    // 日志详情
    String detail() default "";

    DataConstants.OPERATING_TYPE options() default DataConstants.OPERATING_TYPE.OPERATING_TYPE_OTHER;
}
