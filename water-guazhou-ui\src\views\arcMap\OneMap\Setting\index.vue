<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refForm"
      :config="FormConfig"
    ></DialogForm>
  </div>
</template>
<script lang="ts" setup>
import { getRolesByPage } from '@/api/menu'
import { GetUserSettingList, SaveUserSetting } from '@/api/user'
import { IDialogFormIns, ISearchIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { removeSlash } from '@/utils/removeIdSlash'

const refSearch = ref<ISearchIns>()
const refForm = ref<IDialogFormIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    { type: 'input', label: '名称', field: 'name' },
    {
      type: 'btn-group',
      btns: [
        { text: '查询', click: () => refreshData(), perm: true },
        { text: '新增', click: () => handleAdd(), perm: true }
      ]
    }
  ]
})
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    {
      minWidth: 220,
      label: '名称',
      prop: 'name'
    },
    { minWidth: 220, label: '类型', prop: 'type' },
    { minWidth: 220, label: '角色', prop: 'roles' }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  },
  operationWidth: 80,
  operationHeaderAlign: 'center',
  operations: [{ perm: true, text: '编辑', click: row => handleAdd(row) }]
})
const FormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  title: '新增',
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '配置项名称',
          field: 'name',
          readonly: true
        },
        {
          type: 'input',
          label: '配置项类型',
          field: 'type',
          readonly: true
        },
        {
          type: 'select',
          label: '关联角色',
          multiple: true,
          field: 'roles',
          rules: [{ required: true, message: '请选择关联角色' }]
        }
      ]
    }
  ],
  submit: params => {
    SLConfirm('确定提交？', '提示信息')
      .then(() => {
        const submitParams = {
          ...params,
          roles: params.roles?.join(',')
        }
        FormConfig.submitting = true
        SaveUserSetting(submitParams)
          .then(res => {
            if (res.data.code === 200) {
              SLMessage.success(res.data.message)
              refreshData()
              refForm.value?.closeDialog()
            } else {
              SLMessage.error(res.data.messsage)
            }
          })
          .catch(() => {
            SLMessage.error('系统错误')
          })
          .finally(() => {
            FormConfig.submitting = false
          })
      })
      .catch(() => {
        //
      })
  }
})
const handleAdd = (row?: any) => {
  FormConfig.title = row ? '编辑' : '新增'
  FormConfig.defaultValue = {
    ...(row || {})
  }
  refForm.value?.openDialog()
}
const refreshData = async () => {
  const name = refSearch.value?.queryParams?.name
  const res = await GetUserSettingList({
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    name
  })
  TableConfig.dataList = res.data?.data.data
  TableConfig.pagination.total = res.data?.data.total
}
const initRoles = () => {
  getRolesByPage({
    page: 1,
    size: 99999
  }).then(res => {
    const tree = FormConfig.group[0].fields[2] as IFormSelect
    tree
      && (tree.options = res.data.data?.map(item => {
        const id = removeSlash(item.id.id)
        return {
          label: item.name,
          value: id,
          id
        }
      }))
  })
}
onMounted(() => {
  initRoles()
  refreshData()
})
</script>
<style lang="scss" scoped></style>
