import{_ as d}from"./index-C9hz-UZb.js";import{d as p,r as u,n as f,q as a,F as t,g as n,i as o,h as l,an as _,G as v,bh as b,bl as y,bm as N,C as x}from"./index-r0dFAfgr.js";import{_ as C}from"./AccountInfo.vue_vue_type_script_setup_true_lang-zU3ecsBN.js";import{_ as V}from"./ThemeInfo.vue_vue_type_script_setup_true_lang-D5tmC0mu.js";import"./changePassword-DW35Gs1E.js";const h={class:"account-manage-warpper"},k=p({__name:"index",setup(g){const e=u({version:window.SITE_CONFIG.version,activeName:"account"});return(w,c)=>{const s=y,m=N,r=d;return n(),f("div",h,[a(r,{title:"个人中心"},{default:t(()=>[a(m,{modelValue:o(e).activeName,"onUpdate:modelValue":c[0]||(c[0]=i=>o(e).activeName=i),class:"demo-tabs"},{default:t(()=>[a(s,{label:"账户信息",name:"account",class:"overlay-y"},{default:t(()=>[o(e).activeName==="account"?(n(),l(C,{key:0})):_("",!0)]),_:1}),a(s,{label:"个性化配置",name:"theme",class:"overlay-y"},{default:t(()=>[o(e).activeName==="theme"?(n(),l(V,{key:0})):_("",!0)]),_:1}),a(s,{label:"版本信息",name:"version",class:"overlay-y"},{default:t(()=>[v(" 版本信息:"+b(o(e).version),1)]),_:1})]),_:1},8,["modelValue"])]),_:1})])}}}),F=x(k,[["__scopeId","data-v-3073fe48"]]);export{F as default};
