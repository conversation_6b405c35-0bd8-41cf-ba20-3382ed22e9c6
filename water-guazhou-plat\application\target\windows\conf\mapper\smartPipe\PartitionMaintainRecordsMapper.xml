<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PartitionMaintainRecordsMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMaintainRecords">
        select a.*
        from tb_pipe_partition_maintain_records a
        <where>
            <if test="param.partitionId != null and param.partitionId != ''">
                and a.partition_id = #{param.partitionId}
            </if>
            and a.tenant_id = #{param.tenantId}
        </where>
        order by a.create_time desc

    </select>
</mapper>