import{_ as R}from"./index-C9hz-UZb.js";import{_ as W}from"./CardTable-rdWOL4_6.js";import{d as H,a6 as $,r as S,bF as i,c as N,am as G,bB as P,a8 as J,bX as Q,s as V,o as U,ah as X,ay as K,g as Z,n as ee,q as f,i as c,F as v,cs as I,bo as A,bR as O,p as j,j as te,dF as ae,dA as oe,al as ne,aj as re,bD as le,C as se}from"./index-r0dFAfgr.js";import{_ as ie}from"./CardSearch-CB_HNR-Q.js";import{h as ce}from"./statisticalAnalysis-D5JxC4wJ.js";import{u as de}from"./useStation-DJgnSZIA.js";import{p as ue}from"./printUtils-C-AxhDcd.js";import{r as pe}from"./data-D3PIONJl.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";function me(){return{title:{text:"",textStyle:{color:"#5470C6",fontSize:"14px"},top:10},grid:{left:90,right:90,top:70,bottom:80},legend:{top:20,type:"scroll",width:"500",textStyle:{fontSize:12}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(x){let Y=x[0].name+"<br/>";return x.forEach(e=>{Y+=e.marker+e.seriesName+": "+e.value+"<br/>"}),Y}},xAxis:{type:"category",data:[],axisLabel:{rotate:45,interval:0,fontSize:11,margin:8},axisTick:{alignWithLabel:!0}},yAxis:[{position:"left",type:"value",name:"数值",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[]}}const fe={class:"wrapper"},ye=H({__name:"index",setup(q){const{getStationTree:x}=de(),Y=$(),e=S({type:"date",treeDataType:"Station",stationId:"",sumsRow:{},title:"",activeName:"list",chartOption:null,dataList:{}});i().date();const L=N(),C=N(),D=N(),k=N();G(()=>e.activeName,()=>{e.activeName==="echarts"&&P(()=>{M()})});const m=S({data:[],currentProject:{}}),w=S({defaultParams:{type:"day",year:[i().format("YYYY"),i().format("YYYY")],month:[i().format("YYYY-MM"),i().format("YYYY-MM")],day:[i().format("YYYY-MM-DD"),i().format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:J(()=>m.data),label:"站点选择",onChange:t=>{const a=Q(m.data,"children","id",t);m.currentProject=a,e.treeDataType=a.data.type,e.treeDataType==="Station"&&(e.stationId=a.id,T())}},{type:"radio-button",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型"},{type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(t,a,o)=>{o.hidden=t.type==="month"||t.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(t,a,o)=>{o.hidden=t.type==="day"||t.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(t,a,o)=>{o.hidden=t.type==="month"||t.type==="day"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>T(),svgIcon:V(ne)},{text:"导出",perm:!0,type:"warning",svgIcon:V(re),click:()=>z()},{perm:!0,text:"打印",type:"success",svgIcon:V(le),click:()=>E()}]}]}),y=S({loading:!0,dataList:[],columns:[],operations:[],operationWidth:"150px",pagination:{hide:!0}}),T=()=>{var l;y.loading=!0;const t=m.currentProject.id,a=((l=C.value)==null?void 0:l.queryParams)||{},o=pe.find(h=>h.value===a.type),r=a[(o==null?void 0:o.value)||"day"];if(!r||!r[0]||!r[1]){y.loading=!1;return}e.title=m.currentProject.label+"水量报表("+(o==null?void 0:o.label)+i(r[0]).format((o==null?void 0:o.data)||"YYYY-MM-DD")+"至"+i(r[1]).format((o==null?void 0:o.data)||"YYYY-MM-DD")+")";const[d,g]=r,s={stationId:t,queryType:a.type,start:i(d).startOf(a.type==="day"?"day":a.type==="month"?"month":"year").valueOf(),end:i(g).endOf(a.type==="day"?"day":a.type==="month"?"month":"year").valueOf()};ce(s).then(h=>{var n,_;const u=(n=h.data)==null?void 0:n.data;e.dataList=u;const b=(_=u==null?void 0:u.tableInfo)==null?void 0:_.map(p=>({prop:p.columnValue,label:p.columnName,unit:p.unit?"("+p.unit+")":""}));console.log(b),y.columns=b,y.dataList=u==null?void 0:u.tableDataList,y.loading=!1,e.activeName==="echarts"&&M()})},z=()=>{var t;(t=L.value)==null||t.exportTable()},E=()=>{ue({title:e.title,data:y.dataList,titleList:y.columns})},B=()=>{var t;(t=D.value)==null||t.resize()},M=()=>{var t,a,o;!((t=e.dataList)!=null&&t.tableDataList)||!((a=e.dataList)!=null&&a.tableInfo)||((o=D.value)==null||o.clear(),P(()=>{var h,u,b;const r=me(),d=(h=e.dataList)==null?void 0:h.tableDataList,g=(u=e.dataList)==null?void 0:u.tableInfo;(b=C.value)!=null&&b.queryParams;const s=g.filter(n=>n.columnName!=="数据时间"&&n.columnValue!=="ts"&&n.columnValue!=="min"&&n.columnValue!=="max"&&n.columnValue!=="total"&&!["最小值","最大值","总计","合计","平均值"].includes(n.columnName)),l=(d==null?void 0:d.filter(n=>{const _=n.ts||n.time||n.date,p=String(_||"");return p.endsWith("时")&&!["最小值","最大值","平均值","合计","总计"].includes(p)}))||[];console.log("过滤前数据条数:",d==null?void 0:d.length),console.log("过滤后数据条数:",l==null?void 0:l.length),r.xAxis.data=l==null?void 0:l.map(n=>{const _=n.ts||n.time||n.date;return String(_||"")}),r.legend={top:20,type:"scroll",width:"500",textStyle:{fontSize:12}},s.length>0&&(r.yAxis[0].name=s[0].unit?`${s[0].columnName}(${s[0].unit})`:s[0].columnName),r.series=s.map((n,_)=>{const p=["#5470C6","#91CC75","#FAC858","#EE6666","#73C0DE","#3BA272","#FC8452","#9A60B4","#EA7CCC"];return{name:n.columnName,type:"bar",data:l.map(F=>F[n.columnValue]),itemStyle:{color:p[_%p.length]},label:{show:!1},barWidth:s.length===1?"60%":void 0}}),e.chartOption=r,k.value&&Y.listenTo(k.value,()=>{B()})}))};return U(async()=>{var a;const t=await x("水源地");m.data=t,m.currentProject=X(m.data),w.defaultParams={...w.defaultParams,treeData:m.currentProject},(a=C.value)==null||a.resetForm(),T()}),(t,a)=>{const o=ie,r=ae,d=oe,g=K("VChart"),s=W,l=R;return Z(),ee("div",fe,[f(o,{ref_key:"cardSearch",ref:C,config:c(w)},null,8,["config"]),f(l,{class:"card",title:c(e).activeName==="list"?"水质列表":"水质图表"},{query:v(()=>[f(d,{modelValue:c(e).activeName,"onUpdate:modelValue":a[0]||(a[0]=h=>c(e).activeName=h)},{default:v(()=>[f(r,{label:"echarts"},{default:v(()=>[f(c(I),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:bar-chart-line"})]),_:1}),f(r,{label:"list"},{default:v(()=>[f(c(I),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:v(()=>[A(j("div",{ref_key:"chartContainer",ref:k,class:"chart-box"},[f(g,{ref_key:"refChart",ref:D,theme:c(te)().isDark?"dark":"light",option:c(e).chartOption},null,8,["theme","option"])],512),[[O,c(e).activeName==="echarts"]]),A(j("div",null,[f(s,{id:"print",ref_key:"refTable",ref:L,class:"card-table",config:c(y)},null,8,["config"])],512),[[O,c(e).activeName==="list"]])]),_:1},8,["title"])])}}}),De=se(ye,[["__scopeId","data-v-0d1075d6"]]);export{De as default};
