<!-- 出库单 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <SLDrawer ref="refForm" :config="addOrUpdateConfig"></SLDrawer>
    <!-- 设备选中 -->
    <SLDrawer ref="refFormEquipment" :config="addEquipment"></SLDrawer>
    <!-- 详情 -->
    <SLDrawer ref="detailForm" :config="detailConfig"></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';

import useGlobal from '@/hooks/global/useGlobal';
import {
  getStoreOutRecordSerch,
  getstoreSerch,
  getGoodsShelfSerch,
  getConstructionProjectSerch,
  getStoreOutRecordDetailSerch,
  postStoreOutRecord
} from '@/api/equipment_assets/equipmentOutStock';
import { getDeviceStorageJournalEq } from '@/api/equipment_assets/ledgerManagement';
import { getUserList } from '@/api/user/index';
import { removeSlash } from '@/utils/removeIdSlash';
import {
  getDevicePurchaseSearch,
  getDeviceapiContractDetail
} from '@/api/equipment_assets/equipmentPurchase';
import {
  getSupplierSerch,
  getDeviceTypeTree
} from '@/api/equipment_assets/equipmentManage';
import { traverse, uniqueFunc } from '@/utils/GlobalHelper';
import { formatDate } from '@/utils/DateFormatter';
import { exportType, exportStatus } from '../../equipmentAssetsData';

const { $btnPerms } = useGlobal();

const refSearch = ref<ICardSearchIns>();

const refForm = ref<ISLDrawerIns>();

const detailForm = ref<ISLDrawerIns>();

const refFormEquipment = ref<ISLDrawerIns>();

const chosen = ref([]);

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '出库单编码', field: 'code', type: 'input', labelWidth: '90px' },
    { label: '出库单标题', field: 'title', type: 'input', labelWidth: '90px' },
    {
      label: '目标仓库',
      field: 'storehouseId',
      type: 'select',
      options: computed(() => data.storeList) as any
    },
    {
      label: '出库状态',
      field: 'isOut',
      type: 'select',
      options: exportStatus
    },
    {
      label: '出库类型',
      field: 'type',
      type: 'select',
      options: exportType
    },
    {
      type: 'select',
      label: '关联项目',
      field: 'constructionProjectId',
      options: computed(() => data.ConstructionProject) as any,
      onChange: () => refreshData(),
      formatter: (val, row, filter: any) => {
        return (
          filter?.options?.find((item) => item.value === val)?.label || val
        );
      }
    },
    {
      type: 'department-user',
      label: '领用人',
      field: 'receiveUserId',
      formatter: (val, row, filter: any) => {
        return (
          filter?.options?.find((item) => item.value === val)?.label || val
        );
      }
    },
    {
      type: 'department-user',
      label: '经办人',
      field: 'manager',
      formatter: (val, row, filter: any) => {
        return (
          filter?.options?.find((item) => item.value === val)?.label || val
        );
      }
    },
    {
      type: 'department-user',
      label: '出库人',
      field: 'creator',
      formatter: (val, row, filter: any) => {
        return (
          filter?.options?.find((item) => item.value === val)?.label || val
        );
      }
    },
    { label: '出库时间', field: 'outtime', type: 'daterange' },
    { label: '创建时间', field: 'createtime', type: 'daterange' },
    {
      type: 'switch',
      label: '是否报账',
      field: 'reimbursement'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          type: 'success',
          perm: true,
          text: '新增',
          icon: ICONS.ADD,
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '出库单编码', prop: 'code' },
    { label: '出库单标题', prop: 'title' },
    { label: '仓库名称', prop: 'storehouseName' },
    {
      label: '出库时间',
      prop: 'outTime',
      formatter: (row) => formatDate(row.outTime, 'YYYY-MM-DD') || ''
    },
    { label: '领用部门', prop: 'managerDepartmentName' },
    { label: '领用人', prop: 'receiveUserName' },
    { label: '经办人', prop: 'managerName' },
    { label: '创建人', prop: 'creatorName' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD')
    },
    { label: '项目名称', prop: 'constructionProjectName' },
    {
      label: '出库状态',
      prop: 'isOut',
      formatter: (row) => (row.isOut ? '已出库' : '未出库')
    },
    {
      label: '出库类型',
      prop: 'type',
      formatter: (row) =>
        exportType.find((item: any) => item.value === row.type)?.label
    },
    {
      label: '是否报账',
      prop: 'reimbursement',
      formItemConfig: {
        readonly: true,
        type: 'switch'
      }
    }
  ],
  operationWidth: '160px',
  operations: [
    {
      disabled: (row) => row.isOut,
      type: 'success',
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.EDIT,
      click: (row) => clickEdit(row)
    },
    {
      type: 'primary',
      color: '#4195f0',
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: (row) => openDetails(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '添加',
  labelWidth: '100px',
  width: '1100px',
  submit: (params: any, status?: boolean) => {
    if (status) {
      data.selectValue = params;
      data.getDevice();
      refFormEquipment.value?.openDrawer();
    } else {
      if (params.items) {
        for (const i in params.items) {
          const item = params.items[i];
          if (item.count < item.num) {
            ElMessage.warning('出库数量不应多余当前数量');
            return;
          }
          if (item.num === 0) {
            ElMessage.warning('出库数量不能为0');
            return;
          }
        }
      }
      let val = '添加成功';
      if (params.id) val = '修改成功';
      if (params.items) {
        params.items.map((item) => {
          item.deviceLabelId = item.id;
          return item;
        });
      }
      postStoreOutRecord(params).then(() => {
        refreshData();
        ElMessage.success(val);
        refForm.value?.closeDrawer();
      });
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          disabled: true,
          xl: 8,
          type: 'input',
          label: '出库单编码',
          field: 'code',
          rules: [{ required: true, message: '请输入出库单编码' }]
        },

        {
          xl: 8,
          type: 'input',
          label: '出库单标题',
          field: 'title',
          rules: [{ required: true, message: '请输入出库单标题' }]
        },
        {
          xl: 8,
          type: 'switch',
          label: '是否报账',
          field: 'reimbursement'
        },
        {
          xl: 8,
          type: 'select',
          label: '目标仓库',
          field: 'storehouseId',
          rules: [{ required: true, message: '请选择目标仓库' }],
          options: computed(() => data.storeList) as any,
          onChange: (val) => {
            data.getGoodsShelfValue(val);
          }
        },
        {
          xl: 8,
          type: 'department-user',
          label: '领用人',
          field: 'receiveUserId',
          rules: [{ required: true, message: '请选择领用人' }]
        },
        {
          xl: 8,
          type: 'department-user',
          label: '经办人',
          field: 'manager',
          rules: [{ required: true, message: '请选择经办人' }]
        },
        {
          xl: 8,
          type: 'select',
          label: '出库类型',
          field: 'type',
          rules: [{ required: true, message: '请选择出库类型' }],
          options: [
            { label: '领用', value: '0' },
            { label: '安装', value: '1' },
            { label: '售出', value: '2' }
          ]
        },
        {
          xl: 8,
          type: 'select',
          label: '关联项目',
          field: 'constructionProjectId',
          options: computed(() => data.ConstructionProject) as any
        },
        {
          xl: 8,
          type: 'switch',
          label: '是否补录',
          field: 'addRecord'
        },
        {
          xl: 18,
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'table',
          field: 'items',
          config: {
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end',
                  marginBottom: '10px'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '增加设备',
                        perm: true,
                        click: () => {
                          refForm.value?.Submit(true);
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '规格/型号',
                prop: 'model'
              },
              {
                label: '所属大类',
                prop: 'topType'
              },
              {
                label: '所属类别',
                prop: 'type'
              },
              {
                label: '仓库',
                prop: 'storehouseName'
              },
              {
                label: '货架',
                prop: 'shelvesName'
              },
              {
                label: '当前数量',
                prop: 'count'
              },
              {
                label: '出库数量',
                prop: 'num',
                width: '140px',
                formItemConfig: {
                  type: 'number',
                  min: 0
                }
              },
              {
                label: '计算单位',
                prop: 'unit'
              }
            ],
            operationWidth: 80,
            operations: [
              {
                text: '移除',
                type: 'danger',
                icon: ICONS.DELETE,
                perm: $btnPerms('RoleManageDelete'),
                click: (row) => {
                  data.selectList = data.selectList.filter(
                    (item) => item.location !== row.location
                  );
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

// 设备选择
const addEquipment = reactive<IDrawerConfig>({
  title: '设备选择',
  labelWidth: '130px',
  submit: (params: any, status?: boolean) => {
    // 搜索处理
    if (status) {
      delete params.device;
      data.getDevice(params);
    } else {
      data.selectList = [...data.selectList, ...chosen.value];
      data.selectList = uniqueFunc(data.selectList, ['shelvesId', 'serialId']);
      data.selectList.map((item, index) => {
        item.location = index;
        return item;
      });
      refFormEquipment.value?.closeDrawer();
    }
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '设备名称',
          field: 'name'
        },
        {
          xl: 8,
          type: 'input',
          label: '规格/型号',
          field: 'model'
        },
        {
          xl: 8,
          type: 'select-tree',
          label: '货架',
          field: 'shelvesId',
          checkStrictly: true,
          options: computed(() => traverse(data.GoodsShelf)) as any
        },
        {
          xl: 8,
          type: 'select-tree',
          label: '设备类别',
          field: 'deviceTypeId',
          checkStrictly: true,
          options: computed(() => traverse(data.DeviceType)) as any
        },
        {
          type: 'table',
          field: 'device',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceValue) as any,
            selectList: [],
            handleSelectChange: (val) => {
              chosen.value = val;
            },
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '查询',
                        perm: true,
                        click: () => {
                          refFormEquipment.value?.Submit(true);
                        }
                      },
                      {
                        text: '重置',
                        perm: true,
                        click: () => {
                          refFormEquipment.value?.resetForm();
                          refFormEquipment.value?.Submit(true);
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '规格/型号',
                prop: 'model'
              },
              {
                label: '所属大类',
                prop: 'topType'
              },
              {
                label: '所属类别',
                prop: 'type'
              },
              {
                label: '仓库',
                prop: 'storehouseName'
              },
              {
                label: '货架',
                prop: 'shelvesName'
              },
              {
                label: '当前数量',
                prop: 'count'
              },
              {
                label: '单位',
                prop: 'unit'
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  labelWidth: '100px',

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '出库单编码',
          field: 'code',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '出库单标题',
          field: 'title',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '仓库名称',
          field: 'storehouseName',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '出库时间',
          field: 'outTime',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '领用部门',
          field: 'receiveUserDepartmentName',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '领用人',
          field: 'receiveUserName',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '经办部门',
          field: 'managerDepartmentName',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '经办人',
          field: 'managerName',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '关联项目',
          field: 'constructionProjectName',
          disabled: true
        },
        {
          xl: 8,
          type: 'select',
          label: '出库类型',
          field: 'type',
          rules: [{ required: true, message: '请选择出库类型' }],
          options: [
            { label: '领用', value: '0' },
            { label: '安装', value: '1' },
            { label: '售出', value: '2' }
          ],
          readonly: true
        },
        {
          xl: 8,
          type: 'switch',
          label: '是否报账',
          field: 'reimbursement',
          readonly: true
        },
        {
          xl: 8,
          type: 'input',
          label: '创建人',
          field: 'creatorName',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '创建时间',
          field: 'createTime',
          disabled: true
        },
        {
          xl: 16,
          type: 'textarea',
          label: '备注',
          field: 'remark',
          disabled: true
        },
        {
          type: 'table',
          field: 'device',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '规格/型号',
                prop: 'model'
              },
              {
                label: '所属大类',
                prop: 'topType'
              },
              {
                label: '所属类别',
                prop: 'type'
              },
              {
                label: '出库数量',
                prop: 'num'
              },
              {
                label: '单位',
                prop: 'unit'
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新建出库单';
  data.selectList = [];
  chosen.value = [];
  addOrUpdateConfig.defaultValue = {
    code: 'CK' + formatDate(new Date(), 'YYYYMMDDHHmmss')
  };
  refForm.value?.openDrawer();
};

const openDetails = (row: { [x: string]: any }) => {
  const value = { ...row };
  for (const i in value) {
    if (value[i] === undefined || value[i] === null) value[i] = ' ';
  }
  addOrUpdateConfig.title = '出库单详情';
  detailConfig.defaultValue = { ...(value || {}) };
  detailForm.value?.openDrawer();
  const params = { page: 1, size: 99999, mainId: value.id };
  getStoreOutRecordDetailSerch(params).then((res) => {
    data.selectList = res.data.data.data || [];
  });
};

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑出库单';
  data.selectList = [];
  chosen.value = [];
  data.selectValue = row;
  data.getGoodsShelfValue(row.storehouseId);
  addOrUpdateConfig.defaultValue = { ...(row || {}) };
  refForm.value?.openDrawer();
  const params = { page: 1, size: 99999, mainId: row.id };
  getStoreOutRecordDetailSerch(params).then((res) => {
    const value = traverse(res.data.data.data || [], 'children', {
      storehouseName: 'storeName'
    });
    data.selectList = value.map((item, index) => {
      item.location = index;
      return item;
    });
  });
};

const data = reactive({
  // 用户列表
  UserList: [],
  // 采购单
  DevicePurchase: [],
  // 仓库
  storeList: [],
  // 选中的出库单
  selectValue: {} as any,
  // 设备列表
  deviceValue: [] as any[],
  // 设备数量
  total: 0,
  // 选中的设备
  selectList: [] as any[],
  // 供应商
  SupplierList: [],
  // 合同
  contract: [],
  // 货架
  GoodsShelf: [],
  // 施工项目
  ConstructionProject: [],
  // 设备类别
  DeviceType: [],

  // 获取设备
  getDevice: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      storeId: data.selectValue.storehouseId || '',
      ...param
    };
    getDeviceStorageJournalEq(params).then((res) => {
      data.deviceValue = res.data.data.data || [];
    });
  },

  // 获取用户
  getUserListValue: (pid: string) => {
    getUserList({ pid }).then((res) => {
      const value = res.data.data.data || [];
      data.UserList = value.map((item) => {
        return { label: item.firstName, value: removeSlash(item.id.id) };
      });
    });
  },
  // 获取采购单
  getDevicePurchaseValue: () => {
    const params = { page: 1, size: 99999 };
    getDevicePurchaseSearch(params).then((res) => {
      const value = res.data.data.data || [];
      data.DevicePurchase = value.map((item) => {
        return { label: item.title, value: item.id };
      });
    });
  },
  // 获取仓库
  getstoreSerchValue: () => {
    const params = { page: 1, size: 99999 };
    getstoreSerch(params).then((res) => {
      const value = res.data.data.data || [];
      data.storeList = value.map((item) => {
        return { label: item.name, value: item.id };
      });
    });
  },
  // 获取供应商
  getSupplierValue: () => {
    const params = { page: 1, size: 99999 };
    getSupplierSerch(params).then((res) => {
      const value = res.data.data.data || [];
      data.SupplierList = value.map((item) => {
        return { label: item.name, value: item.id };
      });
    });
  },
  // 获取货架
  getGoodsShelfValue: (row) => {
    const params = { page: 1, size: 99999, id: row };
    getGoodsShelfSerch(params).then((res) => {
      data.GoodsShelf = res.data.data.data[0].children || [];
    });
  },
  // 获取合同
  getDeviceapiContractDetailValue: () => {
    const params = { page: 1, size: 99999 };
    getDeviceapiContractDetail(params).then((res) => {
      const value = res.data.data.data || [];
      data.contract = value.map((item) => {
        return { label: item.title, value: item.id };
      });
    });
  },
  // 获取施工项目
  getConstructionProjectValue: () => {
    const params = { page: 1, size: 99999 };
    getConstructionProjectSerch(params).then((res) => {
      data.ConstructionProject = traverse(res.data.data.data || []);
    });
  },
  // 获取设备类别
  getDeviceTypeTreeValue: () => {
    getDeviceTypeTree().then((res) => {
      data.DeviceType = res.data.data || [];
    });
  },
  init: () => {
    data.getDevicePurchaseValue();
    data.getstoreSerchValue();
    data.getSupplierValue();
    data.getDeviceapiContractDetailValue();
    data.getConstructionProjectValue();
    data.getDeviceTypeTreeValue();
  }
});

const refreshData = async () => {
  const params: {
    size: number | undefined;
    page: number | undefined;
    fromTime?: string;
    toTime?: string;
    outTimeFrom?: string;
    outTimeTo?: string;
    createtime?: string;
    outtime?: string;
  } = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  };
  if (params.createtime && params.createtime?.length > 1) {
    params.fromTime = (refSearch.value?.queryParams as any).createtime[0] || '';
    params.toTime = (refSearch.value?.queryParams as any).createtime[1] || '';
  }
  if (params.outtime && params.outtime?.length > 1) {
    params.outTimeFrom = (refSearch.value?.queryParams as any).outtime[0] || '';
    params.outTimeTo = (refSearch.value?.queryParams as any).outtime[1] || '';
  }
  delete params.createtime;
  delete params.outtime;
  getStoreOutRecordSerch(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  data.init();
});
</script>
