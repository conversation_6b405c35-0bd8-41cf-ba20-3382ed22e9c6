import{d as U,c as h,r as m,o as H,g as D,n as N,p as i,q as n,i as c,F as v,cs as k,b as r,aq as S,J as B,C as R}from"./index-r0dFAfgr.js";import{_ as F}from"./Search-NSrhrIa_.js";import"./index-0NlGN6gS.js";import{b as w,M as T,c as q}from"./userManage-E__vPxsL.js";import"./printUtils-C-AxhDcd.js";const I={class:"manual-hookuo-revenue"},A={class:"left"},z={class:"center"},E={class:"btn"},P={class:"btn"},V={class:"right"},G=U({__name:"RevenueHookUp",props:{currentTreeNode:{}},setup(C){const f=C,g=h(),b=h(),_=h("businessHall"),y=h("businessHall"),W=m({filters:[{type:"input",label:"查询条件",field:"keyword",prepend:{type:"select",field:"type",style:{width:"100px"},options:[{label:"用户名称",value:"custName"},{label:"用户编号",value:"custCode"},{label:"抄表员",value:"copyMeterUser"},{label:"营业所",value:"businessHall"},{label:"地址",value:"address"}],onChange:e=>{_.value=e}},prependDefault:"businessHall"},{type:"btn-group",btns:[{perm:!0,text:"查询",loading:()=>!!a.loading,click:()=>p()}]}]}),x=m({filters:[{type:"input",label:"查询条件",field:"keyword",prepend:{type:"select",field:"type",style:{width:"100px"},options:[{label:"用户名称",value:"custName"},{label:"用户编号",value:"custCode"},{label:"抄表员",value:"copyMeterUser"},{label:"营业所",value:"businessHall"},{label:"地址",value:"address"}],onChange:e=>{y.value=e}},prependDefault:"businessHall"},{type:"btn-group",btns:[{perm:!0,text:"查询",loading:()=>!!s.loading,click:()=>d()}]}]}),a=m({height:400,pagination:{refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,p()}},dataList:[],columns:[{minWidth:120,label:"用户号",prop:"custCode",fixed:"left"},{minWidth:120,label:"姓名",prop:"custName",fixed:"left"},{minWidth:120,label:"地址",prop:"address"},{minWidth:120,label:"册本编号",prop:"meterBookCode"},{minWidth:120,label:"册本名称",prop:"meterBookName"},{minWidth:120,label:"抄表员",prop:"copyMeterUser"},{minWidth:120,label:"营业所",prop:"businessHall"},{minWidth:120,label:"用水类型",prop:"waterCategory"}],handleSelectChange:e=>{a.selectList=e||[]}}),s=m({height:400,pagination:{refreshData:({page:e,size:t})=>{s.pagination.page=e,s.pagination.limit=t,d()}},dataList:[],columns:[{minWidth:120,label:"用户号",prop:"custCode",fixed:"left"},{minWidth:120,label:"姓名",prop:"custName",fixed:"left"},{minWidth:120,label:"地址",prop:"address"},{minWidth:120,label:"册本编号",prop:"meterBookCode"},{minWidth:120,label:"册本名称",prop:"meterBookName"},{minWidth:120,label:"抄表员",prop:"copyMeterUser"},{minWidth:120,label:"营业所",prop:"businessHall"},{minWidth:120,label:"用水类型",prop:"waterCategory"}],handleSelectChange:e=>{s.selectList=e||[]}}),p=async()=>{var t;const e={page:a.pagination.page||1,size:a.pagination.limit||20,isMount:"0"};e[_.value]=(t=g.value)==null?void 0:t.queryParams.keyword,a.loading=!0;try{const o=(await w(e)).data.data||{};a.dataList=o.data||[],a.pagination.total=o.total||0}catch{}a.loading=!1},d=async()=>{var t,l;const e={page:s.pagination.page||1,size:s.pagination.limit||20,isMount:"1",partitionId:(t=f.currentTreeNode)==null?void 0:t.value};e[y.value]=(l=b.value)==null?void 0:l.queryParams.keyword,s.loading=!0;try{const u=(await w(e)).data.data||{};s.dataList=u.data||[],s.pagination.total=u.total||0}catch{}s.loading=!1},L=async()=>{const e=a.selectList||[];if(!e.length){r.warning("请选择要挂接的用户");return}try{const t=await T(e.map(l=>{var o;return{partitionId:(o=f.currentTreeNode)==null?void 0:o.value,id:l.id}}));t.data.code===200?(r.success("操作成功"),p(),d()):r.error(t.data.message)}catch{r.error("操作失败")}},M=async()=>{const e=s.selectList||[];if(!e.length){r.warning("请先选择要取消挂接的用户");return}try{await q(e.map(t=>t.id)),r.success("操作成功"),p(),d()}catch{r.error("操作失败")}};return H(()=>{p(),d()}),(e,t)=>{const l=F,o=S,u=B;return D(),N("div",I,[i("div",A,[t[0]||(t[0]=i("div",{class:"title"}," 未挂接数据 ",-1)),n(l,{ref_key:"refSearchLeft",ref:g,class:"search",config:c(W)},null,8,["config"]),n(o,{class:"table-box",config:c(a)},null,8,["config"])]),i("div",z,[i("div",E,[n(u,{type:"primary",onClick:L},{default:v(()=>[n(c(k),{icon:"ep:d-arrow-right"})]),_:1})]),i("div",P,[n(u,{type:"primary",onClick:M},{default:v(()=>[n(c(k),{icon:"ep:d-arrow-left"})]),_:1})])]),i("div",V,[t[1]||(t[1]=i("div",{class:"title"}," 已挂接数据 ",-1)),n(l,{ref_key:"refSearchRight",ref:b,class:"search",config:c(x)},null,8,["config"]),n(o,{class:"table-box",config:c(s)},null,8,["config"])])])}}}),X=R(G,[["__scopeId","data-v-3d84df8c"]]);export{X as default};
