import{d as x,r as N,am as b,g as s,n as c,q as i,F as n,i as t,aw as m,h as C,cn as h,an as v,p as f,bh as g,aB as D,aJ as F,cE as O,dg as q,dh as z,C as J}from"./index-r0dFAfgr.js";/* empty css                         */const L={class:"vertical-menu"},S={class:"current-title"},U=["onClick"],j=x({__name:"VerticalBar",props:{collapseOnClick:{type:Boolean},menus:{}},emits:["change"],setup(y,{emit:B}){const p=y,a=N({collapse:"",current:void 0}),V=B,I=o=>{a.current=o,p.collapseOnClick&&(a.collapse=""),V("change",o)};return b(()=>p.menus,()=>{a.current=p.menus[0]}),(o,u)=>{const d=O,E=q,w=z;return s(),c("div",L,[i(w,{modelValue:t(a).collapse,"onUpdate:modelValue":u[0]||(u[0]=e=>t(a).collapse=e),accordion:"",class:"collapse-wrapper"},{default:n(()=>[i(E,{name:"1"},{title:n(()=>{var e;return[i(d,null,{default:n(()=>{var r,l,_,k;return[(r=t(a).current)!=null&&r.meta.icon?(s(),c("i",{key:0,class:m((l=t(a).current)==null?void 0:l.meta.icon)},null,2)):(_=t(a).current)!=null&&_.meta.svgIcon?(s(),C(h((k=t(a).current)==null?void 0:k.meta.svgIcon),{key:1})):v("",!0)]}),_:1}),f("span",S,g((e=t(a).current)==null?void 0:e.meta.title),1)]}),default:n(()=>[(s(!0),c(D,null,F(o.menus,(e,r)=>{var l;return s(),c("div",{key:r,class:m(["menu-item",[((l=t(a).current)==null?void 0:l.path)===e.path?"active":""]]),onClick:()=>I(e)},[i(d,null,{default:n(()=>[e.meta.icon?(s(),c("i",{key:0,class:m(e.meta.icon)},null,2)):e.meta.svgIcon?(s(),C(h(e.meta.svgIcon),{key:1})):v("",!0)]),_:2},1024),f("span",null,g(e.meta.title),1)],10,U)}),128))]),_:1})]),_:1},8,["modelValue"])])}}}),H=J(j,[["__scopeId","data-v-1e75c49b"]]);export{H as default};
