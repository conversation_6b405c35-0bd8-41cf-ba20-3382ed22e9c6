package org.thingsboard.server.dao.sql.tenant;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.ApplicationMenuEntity;

import java.util.List;

public interface ApplicationMenuRelationRepository extends JpaRepository<ApplicationMenuEntity, String> {
    @Modifying
    @Transactional
    void deleteByTenantApplicationId(String tenantApplicationId);

    List<ApplicationMenuEntity> findByTenantApplicationId(String tenantApplicationId);

    ApplicationMenuEntity findByMenuId( String menuId);

    @Modifying
    @Transactional
    void deleteByMenuId(String id);
}
