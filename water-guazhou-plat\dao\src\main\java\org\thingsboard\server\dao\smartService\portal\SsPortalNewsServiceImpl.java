package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalNews;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalNewsMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalNewsPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalNewsSaveRequest;

@Service
public class SsPortalNewsServiceImpl implements SsPortalNewsService {
    @Autowired
    private SsPortalNewsMapper mapper;

    @Override
    public SsPortalNews findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<SsPortalNews> findAllConditional(SsPortalNewsPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SsPortalNews save(SsPortalNewsSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalNews entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean active(SsPortalActiveRequest request) {
        return mapper.active(request);
    }

    @Override
    public boolean setHot(SsPortalActiveRequest request) {
        return mapper.setHot(request);
    }

    @Override
    public boolean setRecommend(SsPortalActiveRequest request) {
        return mapper.setRecommend(request);
    }

    @Override
    public boolean canSave(SsPortalNewsSaveRequest request) {
        return mapper.canSave(request);
    }

}
