<!-- 工程管理-详情-工程实施详情 -->
<template>
  <el-card class="card">
    <descriptions :config="basicConfig"></descriptions>
  </el-card>
  <CardTable :config="TableConfig" class="card-table"> </CardTable>
  <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import {
  postConstructionApplyFlow,
  getConstructionApplyFlowList,
  deleteConstructionApplyFlow
} from '@/api/engineeringManagement/manage';
import { GenNonDuplicateID } from '@/utils/GlobalHelper';
import { SLConfirm } from '@/utils/Message';
import { StageType } from './data';

const refForm = ref<IDialogFormIns>();
const props = defineProps<{ id: string; config: any }>();

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => props.config) as any,
  border: true,
  direction: 'horizontal',
  column: 2,
  title: '工程实施基础信息',
  fields: [
    { type: 'text', label: '实施编号:', field: 'code' },
    { type: 'text', label: '工程负责人:', field: 'principal' },
    { type: 'text', label: '联系电话:', field: 'phone' },
    { type: 'text', label: '施工班组:', field: 'constructClass' },
    {
      type: 'text',
      label: '工期开始时间:',
      field: 'beginTime',
      formatter: (row) => dayjs(row).format('YYYY-MM-DD')
    },
    {
      type: 'text',
      label: '工期结束时间:',
      field: 'endTime',
      formatter: (row) => dayjs(row).format('YYYY-MM-DD')
    },
    { type: 'text', label: '备注:', field: 'remark' }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  title: '管理流程明细',
  titleRight: [
    {
      style: {
        justifyContent: 'flex-end'
      },
      items: [
        {
          type: 'btn-group',
          btns: [
            {
              text: '添加',
              perm: true,
              click: () => {
                addOrUpdateConfig.title = '添加管理流程';
                addOrUpdateConfig.defaultValue = {
                  code: `${props.config.code || ''}-${GenNonDuplicateID()}`,
                  designCode: props.config.code || '',
                  constructionApplyCode: props.config.constructionCode,
                  type: '提高合理化建议'
                };
                refForm.value?.openDialog();
              }
            }
          ]
        }
      ]
    }
  ],
  columns: [
    { label: '工作名称', prop: 'workName' },
    { label: '开始时间', prop: 'beginTimeName' },
    { label: '结束时间', prop: 'endTimeName' },
    { label: '工作负责人', prop: 'headUser' },
    { label: '负责人电话', prop: 'headUserPhone' },
    { label: '工作阶段', prop: 'workStage' },
    { label: '添加时间', prop: 'createTimeName' }
  ],
  operationWidth: '160px',
  operations: [
    {
      isTextBtn: false,
      type: 'success',
      text: '编辑流程',
      perm: true,
      click: (row) => clickEdit(row)
    },
    {
      isTextBtn: false,
      type: 'danger',
      text: '删除',
      perm: true,
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增管理流程',
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    if (
      params.beginTime &&
      params.endTime &&
      params.endTime < params.beginTime
    ) {
      ElMessage.warning('开始时间不能大于结束时间');
      return;
    }
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    params.pipLengthDesign = JSON.stringify(params.pipLengthDesign);
    postConstructionApplyFlow(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
          refreshData();
        } else {
          ElMessage.warning(text + '失败');
        }
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '工程编号',
          field: 'constructionApplyCode',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '工作编号',
          field: 'code'
        },
        {
          xs: 12,
          type: 'input',
          label: '工作名称',
          field: 'workName'
        },
        {
          xs: 12,
          type: 'select',
          label: '工作阶段',
          field: 'workStage',
          options: StageType
        },
        {
          xs: 12,
          type: 'datetime',
          label: '开始时间',
          field: 'beginTime',
          format: 'x'
        },
        {
          xs: 12,
          type: 'datetime',
          label: '结束时间',
          field: 'endTime',
          format: 'x'
        },
        {
          xs: 12,
          type: 'input',
          label: '工作负责人',
          field: 'headUser'
        },
        {
          xs: 12,
          type: 'input',
          label: '负责人电话',
          field: 'headUserPhone'
        },
        {
          type: 'textarea',
          label: '工作内容',
          field: 'workContent'
        },
        {
          type: 'textarea',
          label: '说明',
          field: 'remark'
        }
      ]
    }
  ]
});

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑管理流程';
  addOrUpdateConfig.defaultValue = {
    ...(row || {}),
    constructionCode: props.config.constructionCode
  };
  refForm.value?.openDialog();
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除？', '提示信息')
    .then(() => {
      deleteConstructionApplyFlow(row.id).then((res) => {
        if (res.data?.code === 200) {
          ElMessage.success('删除成功');
          refreshData();
        } else {
          ElMessage.warning(res.data?.message);
        }
      });
    })
    .catch(() => {
      //
    });
};

const refreshData = async () => {
  getConstructionApplyFlowList({
    page: 1,
    size: -1,
    constructionCode: props.config.constructionCode,
    constructionApplyCode: props.config.constructionCode
  }).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
  });
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}
.card-table {
  height: 300px;
  margin-bottom: 20px;
}
</style>
