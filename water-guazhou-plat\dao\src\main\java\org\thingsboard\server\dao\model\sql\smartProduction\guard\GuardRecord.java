package org.thingsboard.server.dao.model.sql.smartProduction.guard;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("guard_record")
public class GuardRecord {
    // 日志记录id
    private String id;

    // 地点id
    private String placeId;

    // 地点名称
    @TableField(exist = false)
    private String placeName;

    // 交班班组id
    private String deliverGroupId;

    // 交班班组名称
    private String deliverGroupName;

    // 交班班组成员id，多个用逗号隔开
    private String deliverGroupUser;

    // 交班班组成员名称，多个用、隔开
    private String deliverGroupUserName;

    // 接班班组id
    private String handleGroupId;

    // 接班班组名称
    private String handleGroupName;

    // 接班班组成员id，多个用逗号隔开
    private String handleGroupUser;

    // 接班班组成员名称，多个用、隔开
    private String handleGroupUserName;

    // 班次id
    private String classId;

    // 班次名称
    private String className;

    // 开始时间
    private String beginTime;

    // 结束时间
    private String endTime;

    // 交接班记录
    private String remark;

    // 交接处理人id
    @ParseUsername
    private String exchangeUserId;

    // 交接时间
    private Date exchangeTime;

    // 创建人id
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户id
    private String tenantId;

}
