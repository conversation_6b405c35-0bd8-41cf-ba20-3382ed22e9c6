package org.thingsboard.server.notice;

import javafx.application.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.thingsboard.server.ThingsboardServerApplication;
import org.thingsboard.server.dao.notice.NoticeService;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ThingsboardServerApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class NoticeTesst {
    @Resource
    private NoticeService noticeService;
    @Test
    public void test(){
        System.out.println(noticeService.getList(null,null,null,1,1));
        // System.out.println(noticeService.getNoticeById("2"));
    }

}
