package org.thingsboard.server.utils.imodel.aop;


import org.apache.ibatis.session.SqlSession;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpMethod;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;
import org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.util.imodel.Environment;
import org.thingsboard.server.dao.util.imodel.query.*;
import org.thingsboard.server.dao.util.imodel.response.exception.chain.ExceptionResolvingChain;
import org.thingsboard.server.dao.util.imodel.response.model.IModel;
import org.thingsboard.server.dao.util.imodel.response.model.Model;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;
import org.thingsboard.server.dao.util.reflection.BeanWrapper;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.service.security.model.SecurityUser;
import org.thingsboard.server.utils.imodel.annotations.ExposeRoute;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Aspect
@SuppressWarnings("deprecation")
public class ControllerIntercept {
    private final JdbcTemplate jdbcTemplate;

    private final ExceptionResolvingChain chain = new ExceptionResolvingChain();

    private final EntityValidator validator;

    private SqlSession sqlSession;

    public ControllerIntercept(JdbcTemplate jdbcTemplate, EntityValidator validator, RequestMappingHandlerAdapter adapter, SqlSession sqlSession) {
        this.jdbcTemplate = jdbcTemplate;
        this.validator = validator;
        this.adaptMMessageConverterHandler(adapter, sqlSession);
    }

    private void adaptMMessageConverterHandler(RequestMappingHandlerAdapter adapter, SqlSession sqlSession) {
        this.sqlSession = sqlSession;
        ArrayList<HandlerMethodReturnValueHandler> handlers = new ArrayList<>(adapter.getReturnValueHandlers());
        List<HandlerMethodArgumentResolver> resolvers = new ArrayList<>(adapter.getArgumentResolvers());
        IStarMessageConverterHandler proxy = null;
        for (int i = 0; i < handlers.size(); i++) {
            HandlerMethodReturnValueHandler handler = handlers.get(i);
            if (handler instanceof RequestResponseBodyMethodProcessor) {
                proxy = new IStarMessageConverterHandler((RequestResponseBodyMethodProcessor) handler, jdbcTemplate, sqlSession, validator);
                handlers.set(i, proxy);
                break;
            }
        }
        for (int i = 0; i < resolvers.size(); i++) {
            HandlerMethodArgumentResolver resolver = resolvers.get(i);
            if (resolver instanceof RequestResponseBodyMethodProcessor) {
                resolvers.set(i, proxy);
            }
        }
        adapter.setReturnValueHandlers(handlers);
        adapter.setArgumentResolvers(resolvers);
    }

    // region 新版AOP
    @Around("execution(public * org.thingsboard.server.controller..*.*(..))")
    public Object istar(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!isIStarControllerMethod(joinPoint.getSignature())) {
            return joinPoint.proceed();
        }
        IStarHttpRequest request = wrapRequest(
                ((MethodSignature) joinPoint.getSignature())
                        .getMethod()
                        .isAnnotationPresent(ExposeRoute.class)
        );
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            resolveParameter(arg, request, false);
        }
        try {
            return joinPoint.proceed(args);
        } catch (DuplicateKeyException exception) {
            throw ExceptionUtils.createSilentThrow("编码重复");
        } catch (DataIntegrityViolationException exception) {
            String method = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getMethod();
            if (method.equals(HttpMethod.DELETE.name())) {
                throw ExceptionUtils.createSilentThrow("检测到有相关关联数据，无法直接删除");
            }
            throw ExceptionUtils.createSilentThrow("检测到非法数据，请检查");
        }
    }

    @SuppressWarnings("unchecked")
    private void resolveParameter(Object arg, IStarHttpRequest request, boolean hasParent) {
        RequestAssistantType type = Requestible.getType(arg);
        if (type == RequestAssistantType.OBJECT) {
            Requestible assistant = (Requestible) arg;
            resolveRequestible(assistant, request, hasParent);
        } else if (type == RequestAssistantType.COLLECTION) {
            resolveRequestibleCollection((Collection<Requestible>) arg, request, hasParent);
        } else if (type == RequestAssistantType.ARRAY) {
            resolveRequestibleArray((Requestible[]) arg, request, hasParent);
        }
    }

    private void resolveRequestibleCollection(Collection<Requestible> entities, IStarHttpRequest request, boolean hasParent) {
        for (Requestible entity : entities) {
            resolveRequestible(entity, request, hasParent);
        }
    }

    private void resolveRequestibleArray(Requestible[] entities, IStarHttpRequest request, boolean hasParent) {
        for (Requestible entity : entities) {
            resolveRequestible(entity, request, hasParent);
        }
    }

    private void resolveRequestible(Requestible requestible, IStarHttpRequest request, boolean hasParent) {
        String info = requestible.valid(request);
        List<Object> requestAssistantFields = new ArrayList<>();
        if (info == null && !request.isPatch())
            info = validate(new BeanWrapper(requestible), requestAssistantFields, hasParent);

        if (info != null)
            throw ExceptionUtils.createSilentThrow(info);

        if (requestible instanceof AwareTenantUUID) {
            ((AwareTenantUUID) requestible).tenantId(request.getTenantId());
        }

        if (requestible instanceof AwareCurrentUserUUID) {
            ((AwareCurrentUserUUID) requestible).currentUserId(request.getUserId());
        }

        for (Object requestAssistantField : requestAssistantFields) {
            resolveParameter(requestAssistantField, request, true);
        }
    }
    // endregion


    @Pointcut("execution(public org.thingsboard.server.dao.util.imodel.response.model.IModel org.thingsboard.server.controller ..*.*(..))")
    private void getPointcut() {
    }

    @Around("getPointcut()")
    public Object logAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!isIModelFunction(joinPoint.getSignature())) {
            return joinPoint.proceed();
        }
        IStarHttpRequest request = wrapRequest(false);

        IModel model = null;
        try {
            String valid = null;
            Object[] args = joinPoint.getArgs();
            int modelIndex = -1;
            for (int i = 0; i < args.length; i++) {
                Object arg = args[i];
                if (valid == null && arg instanceof Requestible) {
                    Requestible entity = (Requestible) arg;
                    String validMessage = entity.valid(request);
                    if (validMessage != null)
                        valid = validMessage;
                    else
                        valid = validate(new BeanWrapper(entity), new ArrayList<>(), false);

                    if (arg instanceof AwareTenantUUID) {
                        ((AwareTenantUUID) arg).tenantId(request.getTenantId());
                    }

                    if (arg instanceof AwareCurrentUserUUID) {
                        ((AwareCurrentUserUUID) arg).currentUserId(request.getUserId());
                    }
                } else if (arg instanceof IModel) {
                    if (modelIndex != -1) {
                        throw new ThingsboardException("Expected one IModel but found multiple in function" +
                                                       joinPoint.getClass().getName() + "." + joinPoint.getSignature().getName(), ThingsboardErrorCode.BAD_REQUEST_PARAMS);
                    }
                    modelIndex = i;
                    model = new Model();
                    args[i] = model;
                }
            }

            if (modelIndex == -1) {
                throw new ThingsboardException("Expected IModel but not found in function" +
                                               joinPoint.getClass().getName() + "." + joinPoint.getSignature().getName(), ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }


            model.valid(valid);

            ReturnHelper wrap = new ReturnHelper(jdbcTemplate, sqlSession, Environment.getEnvironment(), model, 1);
            try {
                joinPoint.proceed(args);
            } catch (Exception e) {
                chain.resolve(wrap, e);
            }
            return wrap.getValue();
        } finally {
            if (model != null)
                model.reclaim();
        }
    }

    private String validate(BeanWrapper wrapper, List<Object> requestAssistantFields, boolean hasParent) {
        return validator.validate(wrapper, requestAssistantFields, hasParent);
    }

    private boolean isIModelFunction(Signature signature) {
        Class<?> returnType = getReturnType(signature);
        return IModel.class.isAssignableFrom(returnType);
    }

    private boolean isIStarControllerMethod(Signature signature) {
        if (signature instanceof MethodSignature) {
            Class<?> declaringType = signature.getDeclaringType();
            if (declaringType != null)
                return AnnotatedElementUtils.hasAnnotation(declaringType, IStarController.class);
        }
        return false;
    }

    private Class<?> getReturnType(Signature signature) {
        if (signature instanceof MethodSignature)
            return getReturnType((MethodSignature) signature);
        return null;
    }

    private Class<?> getReturnType(MethodSignature signature) {
        return signature.getMethod().getReturnType();
    }

    private SecurityUser currentUser() throws ThingsboardException {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof SecurityUser) {
            return (SecurityUser) authentication.getPrincipal();
        }
        throw new ThingsboardException("You aren't authorized to perform this operation!", ThingsboardErrorCode.AUTHENTICATION);
    }

    private IStarHttpRequest wrapRequest(boolean expose) throws ThingsboardException {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        return new IStarHttpRequestImpl(request, expose ? null : currentUser());
    }

}
