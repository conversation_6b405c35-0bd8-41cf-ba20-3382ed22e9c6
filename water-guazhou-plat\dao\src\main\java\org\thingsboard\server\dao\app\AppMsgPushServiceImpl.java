package org.thingsboard.server.dao.app;

import com.getui.push.v2.sdk.common.ApiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.AppUserCid;
import org.thingsboard.server.dao.sql.app.AppUserCidRepository;
import org.thingsboard.server.dao.util.AppMsgPushUtil;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AppMsgPushServiceImpl implements AppMsgPushService {

    @Autowired
    private AppUserCidRepository appUserCidRepository;

    @Autowired
    private AppMsgPushUtil appMsgPushUtil;


    @Override
    public void saveUserCid(String userId, String cid) {
        AppUserCid appUserCid = new AppUserCid();
        appUserCid.setUserId(userId);
        appUserCid.setCid(cid);
        appUserCidRepository.save(appUserCid);
    }

    @Override
    public boolean pushMsgToUser(String userId,String title, String msg) {
        // 查询用户的CID
        List<AppUserCid> userCidList = appUserCidRepository.findByUserId(userId);
        if (userCidList == null || userCidList.isEmpty()) {
            log.warn("该用户没有可推送的CID!");
            return true;
        }
        int size = userCidList.size();
        int temp = 0;
        for (AppUserCid appUserCid : userCidList) {
            ApiResult<Map<String, Map<String, String>>> result = appMsgPushUtil.pushMsg(msg, title, appUserCid.getCid());
            if (result.isSuccess()) {
                temp++;
            } else {
                log.warn("推送消息失败, 失败原因: {}-{}", result.getCode(), result.getMsg());
            }
        }
        if (temp == size) {
            // 全部成功推送
            return true;
        } else {
            // 未全部成功推送
            return false;
        }
    }
}
