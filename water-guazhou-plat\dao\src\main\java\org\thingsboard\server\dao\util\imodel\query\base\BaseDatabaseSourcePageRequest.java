package org.thingsboard.server.dao.util.imodel.query.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseSource;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 平台管理-多数据源对象 base_database_source
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@ApiModel(value = "多数据源对象", description = "平台管理-多数据源对象实体类")
@Data
public class BaseDatabaseSourcePageRequest extends PageableQueryEntity<BaseDatabaseSource> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称")
    private String scName;

    /**
     * 数据库类型分类
     */
    @ApiModelProperty(value = "数据库类型分类")
    private String dbType;

    /**
     * 数据库服务器地址
     */
    @ApiModelProperty(value = "数据库服务器地址")
    private String dbHost;

    /**
     * 端口号
     */
    @ApiModelProperty(value = "端口号")
    private String dbPort;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String dbName;

    /**
     * 数据库账号
     */
    @ApiModelProperty(value = "数据库账号")
    private String dbUsername;

    /**
     * 数据库密码
     */
    @ApiModelProperty(value = "数据库密码")
    private String password;

    /**
     * 最大连接数
     */
    @ApiModelProperty(value = "最大连接数")
    private Long dbMaxPoolSize;

    /**
     * 最小空闲连接数
     */
    @ApiModelProperty(value = "最小空闲连接数")
    private Long minIdle;

}
