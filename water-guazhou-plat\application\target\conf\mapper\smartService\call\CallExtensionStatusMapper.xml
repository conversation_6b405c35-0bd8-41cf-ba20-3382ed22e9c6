<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.call.CallExtensionStatusMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.DTO.ExtensionStatusDTO">
        select c.id as seatsId, c.first_name as seatsName, coalesce(e.times, 0) as inCount, coalesce(e.duration, 0) as inDuration, coalesce(f.times, 0) as outCount, coalesce(f.duration, 0) as outDuration, coalesce(g.times, 0) as busyCount
        from tb_service_seats_user a
                 left join tb_user c on a.user_id = c.id
                 left join (
            select user_id, count(*) as times, sum((EXTRACT(epoch from (coalesce( end_time, now()) - start_time)) / 60)::int4) as duration
            from tb_service_call_extension_status  where   status = '0'
                <if test="startTime != null">
                    and start_time &gt;= to_timestamp(#{startTime} / 1000)
                </if>
                <if test="endTime != null">
                    and end_time &lt;= to_timestamp(#{endTime} / 1000)
                </if>
            GROUP BY user_id
        ) e on a.user_id = e.user_id

                 left join (
            select user_id, count(*) as times, sum((EXTRACT(epoch from (coalesce( end_time, now()) - start_time)) / 60)::int4) as duration
            from tb_service_call_extension_status  where   status = '1'
                <if test="startTime != null">
                    and start_time &gt;= to_timestamp(#{startTime} / 1000)
                </if>
                <if test="endTime != null">
                    and end_time &lt;= to_timestamp(#{endTime} / 1000)
                </if>
             GROUP BY user_id
        ) f on a.user_id = f.user_id

                 left join (
            select user_id, count(*) as times, sum((EXTRACT(epoch from (coalesce( end_time, now()) - start_time)) / 60)::int4) as duration
            from tb_service_call_extension_status  where   status = '2'
                <if test="startTime != null">
                    and start_time &gt;= to_timestamp(#{startTime} / 1000)
                </if>
                <if test="endTime != null">
                    and end_time &lt;= to_timestamp(#{endTime} / 1000)
                </if>
             GROUP BY user_id
        ) g on a.user_id = g.user_id

        order by COALESCE(e.times, 0) desc

    </select>

    <select id="getDetail" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallExtensionStatus">
        select a.*, b.first_name as seatsName, (EXTRACT(epoch from (coalesce( end_time, now()) - start_time)) / 60)::int4 duration
        from tb_service_call_extension_status a left join tb_user b on a.user_id = b.id
        where a.user_id = #{seatsId} and (a.status = '0' ) and end_time is not null
        order by a.start_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>


    <select id="getDetailCount" resultType="int">
        select count(*)
        from tb_service_call_extension_status a left join tb_user b on a.user_id = b.id
        where a.user_id = #{seatsId} and (a.status = '0' ) and end_time is not null
    </select>
</mapper>