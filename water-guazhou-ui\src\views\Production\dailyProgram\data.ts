export function zhexian(params:{
  title?:string
  legend?:any
  xAxis?:any
}) {
  const option = {
    title: {
      text: params.title || '',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        label: {
          backgroundColor: '#ffffff',
          color: '#333'
        }
      }
    },
    legend: {
      data: params.legend || [],
      top: '20px'
    },
    grid: {
      left: '0%',
      right: '30px',
      bottom: '20px',
      containLabel: true
    },

    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: params.xAxis || []
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#666',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: 'Email',
        type: 'line',
        stack: 'Total',
        data: [120, 132, 101, 134, 90, 230, 210]
      }
    ]
  }
  return option
}

export const programType = [
  { label: '日常调度', value: '日常调度' },
  { label: '计划调度', value: '计划调度' },
  { label: '应急调度', value: '应急调度' }
]

export const dateLabel = [
  { label: '工作日', value: '工作日' },
  { label: '周末', value: '周末' },
  { label: '元旦', value: '元旦' },
  { label: '春节', value: '春节' },
  { label: '五一', value: '五一日' },
  { label: '端午', value: '端午末' },
  { label: '中秋', value: '中秋' },
  { label: '国庆', value: '国庆' },
  { label: '任意', value: '任意' }
]

export const weatherType = [
  { label: '晴天', value: '晴天' },
  { label: '阴天', value: '阴天' },
  { label: '小雨', value: '小雨' },
  { label: '中雨', value: '中雨' },
  { label: '大雨', value: '大雨日' },
  { label: '暴雨', value: '暴雨末' },
  { label: '雷阵雨', value: '雷阵雨' },
  { label: '雷暴', value: '雷暴' },
  { label: '雨夹雪', value: '雨夹雪' },
  { label: '小雪', value: '小雪' },
  { label: '中雪', value: '中雪' },
  { label: '大雪', value: '大雪' },
  { label: '暴雪', value: '暴雪' }
]

export const fields = [
  {
    label: '方案名称',
    field: 'name'
  },
  {
    label: '方案类型',
    field: 'type'
  },
  {
    label: '日期标签',
    field: 'dateLabel'
  },
  {
    label: '水厂',
    field: 'stationName'
  },
  {
    label: '方案日期',
    field: 'time',
    format: 'x'
  },

  {
    label: '天气类型',
    field: 'weatherType'
  },
  {
    label: '最高温度',
    field: 'maxTemperature',
    unit: '°C'
  },
  {
    label: '最低温度',
    field: 'minTemperature',
    unit: '°C'
  },
  {
    label: '降雨量',
    field: 'rainfall',
    unit: 'm³'
  },
  {
    label: '相对湿度',
    field: 'relativeHumidity',
    unit: '%'
  },
  {
    label: '供水量',
    field: 'waterSupply',
    unit: 'm³'
  },
  {
    label: '耗电量',
    field: 'powerConsumption',
    unit: 'Kwh'
  },
  {
    label: '清水池液位',
    field: 'waterLevel',
    unit: 'm'
  },
  {
    label: '方案描述',
    field: 'remark'
  }
]
