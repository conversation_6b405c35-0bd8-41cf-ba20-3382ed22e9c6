import{C as t}from"./enums-BDQrMlcz.js";import{t as c}from"./VertexElementDescriptor-BOD-G50G.js";function a(n,e=0){const i=n.stride;return n.fieldNames.filter(r=>{const o=n.fields.get(r).optional;return!(o&&o.glPadding)}).map(r=>{const o=n.fields.get(r),f=o.constructor.ElementCount,s=p(o.constructor.ElementType),u=o.offset,l=!(!o.optional||!o.optional.glNormalized);return new c(r,f,s,u,i,l,e)})}function p(n){const e=N[n];if(e)return e;throw new Error("BufferType not supported in WebGL")}const N={u8:t.UNSIGNED_BYTE,u16:t.UNSIGNED_SHORT,u32:t.UNSIGNED_INT,i8:t.BYTE,i16:t.SHORT,i32:t.INT,f32:t.FLOAT};export{a as o};
