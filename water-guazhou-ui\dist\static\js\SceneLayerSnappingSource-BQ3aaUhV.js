import{e as o,y as a,a as g,ax as f,am as L,v as b,S as C}from"./Point-WxyopZva.js";import{fb as E,a_ as H,aX as x,T as w,b2 as I,R as A}from"./index-r0dFAfgr.js";import{c_ as _,af as v}from"./MapView-DaoQedLH.js";import{h as S}from"./WorkerHandle-3vEm1fum.js";import{D as u}from"./workerHelper-wv3qZZD7.js";import{f as O}from"./edgeProcessing-OWtVBtJ5.js";import{E as $}from"./sphere-NgXH-gLx.js";import{q as R,t as D}from"./AnimatedLinesLayer-B2VbV4jv.js";import{r as V}from"./VertexSnappingCandidate-CgwNICNk.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./BufferView-BcX1hwIm.js";import"./InterleavedLayout-EYSqXknm.js";import"./types-Cezv0Yl1.js";import"./deduplicate-Clsym5GM.js";import"./Indices-iFKW8TWb.js";import"./VertexAttribute-BAIQI41G.js";import"./glUtil-D4FNL8tc.js";import"./enums-BDQrMlcz.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";class P extends S{constructor(e){super("EdgeProcessingWorker","extract",{extract:r=>[r.dataBuffer],extractComponentsEdgeLocations:r=>[r.dataBuffer],extractEdgeLocations:r=>[r.dataBuffer]},e)}async process(e,r,i){if(i)return O(e);const s=await this.invoke(new y(e),r);return this._unpackOutput(s)}async extractEdgeLocations(e,r){const i=await this.invokeMethod("extractEdgeLocations",new y(e),r);return u(i)}async extractComponentsEdgeLocations(e,r){const i=await this.invokeMethod("extractComponentsEdgeLocations",new y(e),r);return u(i)}_unpackOutput(e){return{regular:{instancesData:u(e.regular.instancesData),lodInfo:{lengths:new Float32Array(e.regular.lodInfo.lengths)}},silhouette:{instancesData:u(e.silhouette.instancesData),lodInfo:{lengths:new Float32Array(e.silhouette.lodInfo.lengths)}},averageEdgeLength:e.averageEdgeLength}}}class y{constructor(e){this.dataBuffer=e.data.buffer,this.writerSettings=e.writerSettings,this.skipDeduplicate=e.skipDeduplicate,this.indices=Array.isArray(e.indices)?e.indices:e.indices.buffer,this.indicesType=Array.isArray(e.indices)?"Array":E(e.indices)?"Uint32Array":"Uint16Array",this.indicesLength=e.indicesLength}}let d=class extends _{constructor(t){super(t),this.availability=0,this._ids=new Set}destroy(){this._workerHandle.destroy(),this._workerHandle=null}initialize(){this._workerHandle=new j(this.schedule,{fetchAllEdgeLocations:(t,e)=>this._fetchAllEdgeLocations(t,e)})}async fetchCandidates(t,e){const r=t.coordinateHelper,{point:i}=t,s=B;this.renderCoordsHelper.toRenderCoords(i,r.spatialReference,s);const l=t.distance,c=typeof l=="number"?l:l.distance,h=await this._workerHandle.invoke({bounds:$(s[0],s[1],s[2],c),types:t.types},e);return h.candidates.sort((m,k)=>m.distance-k.distance),h.candidates.map(m=>this._convertCandidate(r,m))}async add(t,e){this._ids.add(t.id),await this._workerHandle.invokeMethod("add",t,e)}async remove(t,e){this._ids.delete(t.id),await this._workerHandle.invokeMethod("remove",t,e)}_convertCandidate(t,e){switch(e.type){case"edge":return new R({objectId:e.objectId,targetPoint:this._convertRenderCoordinate(t,e.target),edgeStart:this._convertRenderCoordinate(t,e.start),edgeEnd:this._convertRenderCoordinate(t,e.end),isDraped:!1});case"vertex":return new V({objectId:e.objectId,targetPoint:this._convertRenderCoordinate(t,e.target),isDraped:!1})}}_convertRenderCoordinate({spatialReference:t},e){const r=v();return this.renderCoordsHelper.fromRenderCoords(e,r,t),D(r)}async _fetchAllEdgeLocations(t,e){const r=[],i=[];for(const{id:s,uid:l}of t.components)this._ids.has(s)&&r.push((async()=>{const c=await this.fetchEdgeLocations(s,e.signal),h=c.locations.buffer;return i.push(h),{id:s,uid:l,objectIds:c.objectIds,locations:h,origin:c.origin,type:c.type}})());return{result:{components:(await Promise.all(r)).filter(({id:s})=>this._ids.has(s))},transferList:i}}};o([a({constructOnly:!0})],d.prototype,"renderCoordsHelper",void 0),o([a({constructOnly:!0})],d.prototype,"fetchEdgeLocations",void 0),o([a({constructOnly:!0})],d.prototype,"schedule",void 0),o([a({readOnly:!0})],d.prototype,"availability",void 0),d=o([g("esri.views.interactive.snapping.featureSources.sceneLayerSource.SceneLayerSnappingSourceWorker")],d);class j extends S{constructor(e,r){super("SceneLayerSnappingSourceWorker","fetchCandidates",{},e,{strategy:"dedicated",client:r})}}const B=v();let n=class extends _{get updating(){return this.updatingHandles.updating}constructor(t){super(t),this.availability=1,this._abortController=new AbortController}destroy(){this._tracker=H(this._tracker),this._abortController=x(this._abortController)}initialize(){const{view:t}=this,e=t.resourceController;this._edgeWorker=new P(r=>e.immediate.schedule(r)),this._workerHandle=new d({renderCoordsHelper:this.view.renderCoordsHelper,schedule:r=>e.immediate.schedule(r),fetchEdgeLocations:async(r,i)=>{if(w(this._tracker))throw new Error("tracker-not-initialized");return this._tracker.fetchEdgeLocations(r,this._edgeWorker,i)}}),this.updatingHandles.addPromise(this._setupLayerView()),this.handles.add([f(this._workerHandle),f(this._edgeWorker)])}async fetchCandidates(t,e){return this._workerHandle.fetchCandidates(t,e)}refresh(){}async _setupLayerView(){if(this.destroyed)return;const t=I(this._abortController,r=>r.signal),e=await this.getLayerView();w(e)||L(t)||(this._tracker=e.trackSnappingSources({add:(r,i)=>{this.updatingHandles.addPromise(this._workerHandle.add({id:r,bounds:i},t))},remove:r=>{this.updatingHandles.addPromise(this._workerHandle.remove({id:r},t))}}))}};o([a({constructOnly:!0})],n.prototype,"getLayerView",void 0),o([a({constructOnly:!0})],n.prototype,"view",void 0),o([a({readOnly:!0})],n.prototype,"updating",null),o([a({readOnly:!0})],n.prototype,"availability",void 0),n=o([g("esri.views.interactive.snapping.featureSources.I3SSnappingSource")],n);let p=class extends b{get updating(){return this._i3sSources.some(t=>t.updating)}constructor(t){super(t),this.availability=1,this._i3sSources=[]}destroy(){this._i3sSources.forEach(t=>t.destroy()),this._i3sSources.length=0}initialize(){const{view:t}=this,e=this.layerSource.layer;this._i3sSources=e.type==="building-scene"?this._getBuildingSceneI3SSources(t,e):[this._getSceneLayerI3SSource(t,e)]}async fetchCandidates(t,e){const r=await Promise.all(this._i3sSources.map(i=>i.fetchCandidates(t,e)));return C(e),r.flat()}refresh(){this._i3sSources.forEach(t=>t.refresh())}_getBuildingSceneI3SSources(t,e){return e.allSublayers.toArray().map(r=>r.type==="building-component"?new n({getLayerView:async()=>(await t.whenLayerView(e)).whenSublayerView(r),view:t}):null).filter(A)}_getSceneLayerI3SSource(t,e){return new n({getLayerView:async()=>{const r=await t.whenLayerView(e);return r.type==="scene-layer-graphics-3d"?void 0:r},view:t})}};o([a({constructOnly:!0})],p.prototype,"layerSource",void 0),o([a({constructOnly:!0})],p.prototype,"view",void 0),o([a({readOnly:!0})],p.prototype,"updating",null),o([a({readOnly:!0})],p.prototype,"availability",void 0),p=o([g("esri.views.interactive.snapping.featureSources.SceneLayerSnappingSource")],p);export{p as SceneLayerSnappingSource};
