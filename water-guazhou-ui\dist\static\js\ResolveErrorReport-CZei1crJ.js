import{_ as J}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{D as V}from"./DrawerBox-CLde5xC8.js";import{d as q,c as _,r as w,bE as $,S as x,b as i,l as z,W as j,o as H,g as Q,n as U,q as d,F as b,i as g,p as E,bh as K,aB as X,aq as Z,C as tt}from"./index-r0dFAfgr.js";import{_ as et}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{_ as rt}from"./ArcLayout-CHnHL9Pv.js";import{g as ot}from"./MapView-DaoQedLH.js";import{s as R,g as it}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as at}from"./LayerHelper-Cn-iiqxI.js";import{a as st,i as nt}from"./QueryHelper-ILO3qZqg.js";import{GetAllFieldConfig as mt}from"./fieldconfig-Bk3o1wi7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{q as pt,c as lt}from"./geoserverUtils-wjOSMa7E.js";import{P as T,G as ct}from"./errorReport-D3BFqMSq.js";import{E as ft}from"./ErrorPopTable-Cv-lIqKT.js";import{f as dt}from"./DateFormatter-Bm9a68Ax.js";import"./SideDrawer-CBntChyn.js";import"./ArcView-DpMnCY82.js";import"./project-DUuzYgGl.js";import"./ViewHelper-BGCZjxXH.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./pipe-nogVzCHG.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./WMSLayer-mTaW758E.js";import"./widget-BcWKanF2.js";import"./scaleUtils-DgkF6NQH.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./imageBitmapUtils-Db1drMDc.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";const ut={class:"attr-table-box"},gt=q({__name:"ResolveErrorReport",setup(yt){const v=_(),D=_(),S=w({fieldConfig:[]}),C=_({hideFooter:!0,row:void 0,dataList:[],title:""}),p={identifyResults:[]},I=_(),N=w({labelWidth:70,group:[{fields:[{type:"daterange",itemContainerStyle:{width:"350px"},label:"上报时间",field:"reportTime"},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>u()},{perm:!0,text:"重置",iconifyIcon:"ep:refresh",type:"default",click:()=>M()}]}]}],defaultValue:{}}),a=w({dataList:[],columns:[{minWidth:100,label:"设备类别",prop:"layer"},{minWidth:100,label:"上报人",prop:"uploadUserName"},{minWidth:160,label:"上报时间",prop:"uploadTime",formatter(t,e){return dt(e,$)}},{minWidth:90,label:"上报内容",prop:"uploadContent",align:"center",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",isTextBtn:!0,size:"small",styles:{width:"100%"},click:t=>W(t)}]}}],operations:[{perm:!0,text:"同意",type:"success",disabled:t=>t.status==="同意"||t.status==="驳回",click:t=>B(t)},{perm:!0,text:"驳回",type:"danger",disabled:t=>t.status==="同意"||t.status==="驳回",click:t=>O(t)}],pagination:{refreshData:({page:t,size:e})=>{a.pagination.page=t,a.pagination.limit=e,u()}}}),B=t=>{x("确定同意？","提示信息").then(async()=>{try{(await T({id:t.id,status:"1"})).data.code===200?(i.success("已同意"),u()):i.error("操作失败")}catch{i.error("操作失败")}}).catch(()=>{})},O=t=>{var e;a.currentRow=t,(e=D.value)==null||e.openDialog()},u=()=>{var l;const{reportTime:t}=((l=I.value)==null?void 0:l.dataForm)||{},e=t==null?void 0:t[0];let s=t==null?void 0:t[1];s&&(s=z(s,"YYYY-MM-DD").add(1,"d").format("YYYY-MM-DD")),ct({page:a.pagination.page||1,size:a.pagination.limit||20,beginTime:e,endTime:s,status:"0"}).then(c=>{const n=c.data.data;a.dataList=(n==null?void 0:n.data)||[],a.pagination.total=(n==null?void 0:n.total)||0}).catch(()=>{a.dataList=[],a.pagination.total=0})},M=()=>{var t;(t=I.value)==null||t.resetForm()},W=async t=>{var e,s,l,c,n,y;if(!t.fid){i.warning("缺少设备ID信息");return}try{const h=t.uploadContent?JSON.parse(t.uploadContent):[];let m=null;if(window.GIS_SERVER_SWITCH)try{debugger;const o=await pt({typeName:t.layer,id:t.fid,workspace:"guazhou",tryObjectId:!0});if(o.success&&o.feature){const f=o.feature,r=lt(f.geometry);m=new ot({geometry:r,attributes:f.properties||{}}),m.symbol=R(r.type)}else{i.warning("未找到设备信息");return}}catch(o){console.error("GeoServer查询失败:",o),i.error("查询设备信息失败");return}else{const o=(e=j().gLayerInfos)==null?void 0:e.find(G=>G.layername===t.layer),f=o==null?void 0:o.layerid;if(!o){i.warning("未找到图层信息");return}const r=await st(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+f,nt({where:"OBJECTID = "+t.fid,outFields:["*"]}));if(r.features&&r.features.length>0)m=r.features[0],m.symbol=R(m.geometry.type);else{i.warning("未找到设备信息");return}}if(m){(s=p.graphicsLayer)==null||s.removeAll(),(l=p.graphicsLayer)==null||l.add(m),await it(p.view,m);const o=((c=S.fieldConfig.find(r=>r.layername===t.layer))==null?void 0:c.fields)||[],f=h.filter(r=>r.name!=="img").map(r=>({...o.find(A=>{var k;return A.name.toLocaleLowerCase()===((k=r.name)==null?void 0:k.toLocaleLowerCase())})||{},oldvalue:r.oldvalue,newvalue:r.newvalue}));p.identifyResults=[{feature:m,layerName:t.layer,layerId:t.fid}],C.value={hideFooter:!0,row:t,img:(n=h.find(r=>r.name==="img"))==null?void 0:n.newvalue,dataList:f,title:t.layer+"("+(m.attributes.SID||m.attributes.OBJECTID||t.fid)+")"},(y=v.value)==null||y.toggleDrawer("ltr",!0)}}catch(h){console.error("处理错误上报详情失败:",h),i.error("数据错误")}},F=w({group:[{fields:[{type:"input",label:"请输入驳回原因",field:"remark"}]}],labelPosition:"top",dialogWidth:500,title:"驳回",submit:t=>{x("确定驳回吗？","提示信息").then(async()=>{var e;F.submitting=!0;try{(await T({id:a.currentRow.id,status:"2",remark:t.remark})).data.code===200?(i.success("已驳回"),u(),(e=D.value)==null||e.closeDialog()):i.error("操作失败")}catch{i.error("操作失败")}F.submitting=!1}).catch(()=>{})}}),L=async()=>{const t=await mt();S.fieldConfig=t.data.result||[]},Y=async t=>{p.view=t,await L(),u()},P=()=>{p.graphicsLayer=at(p.view,{id:"error-report",title:"错误属性设备"})};return H(()=>{var t;L(),(t=v.value)==null||t.toggleDrawer("rtl",!0)}),(t,e)=>{const s=rt,l=et,c=Z,n=V,y=J;return Q(),U(X,null,[d(n,{ref_key:"refDrawer",ref:v,"left-drawer":!0,"right-drawer":!0,"right-drawer-title":"属性上报处理","right-drawer-absolute":!1,"left-drawer-absolute":!1,"left-drawer-bar-hide":!1,"right-drawer-width":600,"left-drawer-width":400,"left-drawer-title":" "},{right:b(()=>[d(l,{ref_key:"refForm",ref:I,config:g(N)},null,8,["config"]),E("div",ut,[d(c,{ref:"refTable",config:g(a)},null,8,["config"])])]),"left-title":b(()=>[E("span",null,K(g(C).title),1)]),left:b(()=>[d(ft,{config:g(C)},null,8,["config"])]),default:b(()=>[d(s,{onMapLoaded:Y,onPipeLoaded:P})]),_:1},512),d(y,{ref_key:"refDialogForm",ref:D,config:g(F)},null,8,["config"])],64)}}}),ar=tt(gt,[["__scopeId","data-v-83d616af"]]);export{ar as default};
