import printJS from 'print-js'
import html2canvas from 'html2canvas'

export const html2Canvas = (printContent, callback) => {
  // 获取dom 宽度 高度
  const width = printContent.clientWidth
  const height = printContent.clientHeight
  // 创建一个canvas节点
  const canvas = document.createElement('canvas') as any

  const scale = 4 // 定义任意放大倍数，支持小数；越大，图片清晰度越高，生成图片越慢。
  canvas.width = width * scale // 定义canvas 宽度 * 缩放
  canvas.height = height * scale // 定义canvas高度 *缩放
  canvas.style.width = width * scale + 'px'
  canvas.style.height = height * scale + 'px'
  canvas.getContext('2d').scale(scale, scale) // 获取context,设置scale

  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop // 获取滚动轴滚动的长度
  const scrollLeft = document.documentElement.scrollLeft || document.body.scrollLeft // 获取水平滚动轴的长度

  html2canvas(printContent, {
    canvas,
    backgroundColor: null,
    useCORS: true,
    windowHeight: document.body.scrollHeight,
    scrollX: -scrollLeft, // 解决水平偏移问题，防止打印的内容不全
    scrollY: -scrollTop
    // background: "#ffffff", // 一定要添加背景颜色，否则出来的图片，背景全部都是透明的
  })
    .then(canvas => {
      const url = canvas.toDataURL('image/png')
      // console.log("canvas url :" + url);
      callback({ url })
    })
    .catch(err => {
      console.error(err)
    })
}

export const printImg = (url, callback) => {
  printJS({
    printable: url,
    type: 'image',
    documentTitle: '', // 标题
    style: '@page{size:auto;margin: 1cm ;}' // 去除页眉页脚
  })
}

/**
 * html转图片打印
 * @param dom
 * @param callback
 */
export const html2CanvasPrint = (dom, callback) => {
  // 1、html转图片
  html2Canvas(dom, canvasRes => {
    // 2、打印图片
    printImg(canvasRes.url, callback)
  })
}

export const printHTML = (params: {
  printId?: string,
  title?: string,
  properties?: any,
  list?: any,
  type?: any,
  showSummary?: boolean
}) => {
  const style = '@page {margin:10mm 10mm;   } '
  + '@media print {.print_table{width: 100%;}}'
  printJS({
    printable: params.printId || params.list, // 要打印内容的id
    type: params.type || 'html',
    // headerStyle: 'text-align:center;font-size:24px;font-weight:bold;width:100%;margin-bottom:50px',
    header: params.title,
    ignoreElements: ['title'],
    properties: params.properties,
    style,
    maxWidth: 2560,
    repeatTableHeader: false,
    // css: 'https://unpkg.com/element-plus/dist/index.css',
    targetStyle: params.showSummary !== false ? ['text-align', 'border-collapse', 'width', 'text', 'padding-top', 'margin-bottom', 'margin'] : [],
    scanStyles: true
  })
}

// 传入url路径以及文件名即可
export const downLoadFile = (url, fileName) => {
  getBlob(url).then(blob => {
    saveAs(blob, fileName)
  })
}

export const getBlob = url => {
  return new Promise(resolve => {
    const xhr = new XMLHttpRequest()
    xhr.open('GET', url, true)
    xhr.responseType = 'blob'
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response)
      }
    }
    xhr.send()
  })
}

export const saveAs = (blob, filename) => {
  const link = document.createElement('a')
  const body = document.querySelector('body') as any

  link.href = window.URL.createObjectURL(blob)
  link.download = filename

  // fix Firefox
  link.style.display = 'none'
  body.appendChild(link)

  link.click()
  body.removeChild(link)

  window.URL.revokeObjectURL(link.href)
}

/**
 * 打印JSON/Data
 * @param params 设置项
 */
export const printJSON = (params: {
  /** 打印的title */
  title?: string,
  /** 打印的数据 */
  data: any[],
  /** 打印的列表名 */
  titleList: any[]
}) => {
  let data = JSON.parse(JSON.stringify(params.data))
  let titleList = JSON.parse(JSON.stringify(params.titleList))
  // 数据处理
  data = data.map(item => {
    titleList.forEach(key => {
      if (key.formatter) {
        item[key.prop] = key.formatter(item) ?? ''
        console.log(item.toString(), item[key.prop])
      } else if (item[key.prop] === 0) {
        //
      } else if (!item[key.prop]) {
        item[key.prop] = ' '
      }
    })
    return item
  })

  // 表头处理
  titleList = titleList.map(item => {
    return { field: item.prop, displayName: item.label }
  })
  const style = '@page {margin:10mm 10mm 0 10mm;page-size:8px;};'
  printJS({ printable: data, properties: titleList, type: 'json', style, targetStyles: ['*'], documentTitle: params.title })
}
