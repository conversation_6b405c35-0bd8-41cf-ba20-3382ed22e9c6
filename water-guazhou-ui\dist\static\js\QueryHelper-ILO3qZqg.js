import"./Point-WxyopZva.js";import{X as _}from"./index-r0dFAfgr.js";import{b$ as p,d9 as N,v as x,fn as E,dx as T,dw as P,db as v}from"./MapView-DaoQedLH.js";import{U as y}from"./pe-B8dP0-Ut.js";import{s as D,n as J}from"./executeForIds-BLdIsxvI.js";import{K as C,L,N as j}from"./AnimatedLinesLayer-B2VbV4jv.js";import"./project-DUuzYgGl.js";async function q(e,t,n){const o=p(e),s={...o.query,f:"json",...t.toJSON()},r=t.outSpatialReference||t.geometries[0].spatialReference,a=N(s,n);return y(o.path+"/buffer",a).then(c=>(c.data.geometries||[]).map(({rings:i})=>new x({spatialReference:r,rings:i})))}function B(e){const{contains:t,dynamicLayers:n,geometryPrecision:o,layerDefinitions:s,layerIds:r,maxAllowableOffset:a,outSR:c,returnGeometry:i,searchFields:g,searchText:w}=e.toJSON(),u={contains:t,returnGeometry:i,geometryPrecision:o,maxAllowableOffset:a,searchText:w};if(r&&(u.layers=r.join(",")),g&&(u.searchFields=g.join(",")),c&&(u.sr=c.wkid||JSON.stringify(c)),s){const l=[];for(let d=0;d<s.length;d++){const f=s[d];l[f.id]=f.definitionExpression}u.layerDefs=E(l)}if(n&&n.length){const l=[];for(let f=0;f<n.length;f++){const h=n[f],S=h.id;if(!h.subLayerIds&&r&&r.includes(S)){const O={id:S};O.source=h.source;let F=null;if(s&&s.length){const G=s.find($=>$.id===S);F=G&&G.definitionExpression}F&&(O.definitionExpression=F),l.push(O)}}let d=JSON.stringify(l);d==="[]"&&(d="[{}]"),u.dynamicLayers=d}return u}async function U(e,t,n){t=C.from(t);const o=B(t),s=p(e);s.path+="/find";const r=T({...s.query,f:"json",...o}),a=N(r,n);return y(s.path,a).then(I)}function I(e){const t=e.data;t.results=t.results||[];const n={results:[]};return n.results=t.results.map(o=>L.fromJSON(o)),n}const V=async(e,t)=>{const n=[];if(!(e!=null&&e.length))return n;const o=window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService;return(await Promise.allSettled(e==null?void 0:e.map(r=>b(`${o}/${r.layerid}`,m({orderByFields:["OBJECTID asc"],...t||{}}))))).map((r,a)=>{console.log(r);const c=e[a].layername,i=r.status==="fulfilled"?r.value:[];n[a]={label:c+"("+((i==null?void 0:i.length)||0)+")",name:c,data:i||[]}}),n},W=(e,t)=>(t=t||m(),P(e,t)),b=(e,t)=>(t=t||m(),D(e,t)),m=e=>new v({returnGeometry:!0,outFields:["*"],...e||{}}),Y=(e,t)=>(t=t||m(),J(e,t)),Z=async e=>await q(window.SITE_CONFIG.GIS_CONFIG.gisUtilitiesService+window.SITE_CONFIG.GIS_CONFIG.gisGeometryService,e),Q=e=>new C({...e}),z=(e,t)=>(t=t||Q(),U(e,t)),tt=e=>new j({...e}),et=(e,t,n)=>{const o=[],s=e.length;if(s===0)return[];const r=Math.ceil(s/n);if(t<=r){const a=(t-1)*n,c=t*n;for(let i=a;i<s&&i<c;i++)o.push(e[i])}return o},nt=async(e,t,n)=>{var r,a;if(!(e!=null&&e.length))return[];t!=null&&t.length||(t=((a=(r=(await _(e)).data)==null?void 0:r.result)==null?void 0:a.rows)||[]);const o=[],s=async c=>{if(!(c>=e.length))try{const i=e[c];if(i<0){await s(++c);return}const g=t==null?void 0:t.find(l=>l.layerid===i).layername,w=window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,u=await b(`${w}/${i}`,m({orderByFields:["OBJECTID asc"],...n||{}}));o.push({label:g+"("+((u==null?void 0:u.length)||0)+")",name:g,data:u||[]}),await s(++c)}catch{console.log("发生错误，停止递归")}};return await s(0),o};export{W as a,Y as b,tt as c,et as d,b as e,z as f,nt as g,Q as h,m as i,V as j,Z as q};
