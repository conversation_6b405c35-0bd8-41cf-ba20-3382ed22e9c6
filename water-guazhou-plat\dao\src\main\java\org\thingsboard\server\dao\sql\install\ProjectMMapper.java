package org.thingsboard.server.dao.sql.install;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.install.ProjectM;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-17
 */
@Mapper
public interface ProjectMMapper extends BaseMapper<ProjectM> {

    List<ProjectM> getList(@Param("address") String address, @Param("type") String type, @Param("status") String status, @Param("currentStep") String currentStep, @Param("tenantId") String tenantId, @Param("page") int page, @Param("size") int size);

    int getListCount(@Param("address") String address, @Param("type") String type, @Param("status") String status, @Param("currentStep") String currentStep, @Param("tenantId") String tenantId);

    List<ProjectM> getManageList(@Param("code") String code, @Param("address") String address, @Param("type") String type, @Param("status") String status, @Param("currentStep") String currentStep, @Param("start") Long start, @Param("end") Long end, @Param("userId") String userId, @Param("tenantId") String tenantId, @Param("page") int page, @Param("size") int size);

    int getManageListCount(@Param("code") String code, @Param("address") String address, @Param("type") String type, @Param("status") String status, @Param("currentStep") String currentStep, @Param("start") Long start, @Param("end") Long end, @Param("userId") String userId, @Param("tenantId") String tenantId);
}
