import GroupLayer from '@arcgis/core/layers/GroupLayer'
import FeatureLayer from '@arcgis/core/layers/FeatureLayer'
import GeoJSONLayer from '@arcgis/core/layers/GeoJSONLayer'
import WFSLayer from '@arcgis/core/layers/WFSLayer'

import SimpleRenderer from '@arcgis/core/renderers/SimpleRenderer'
import SimpleLineSymbol from '@arcgis/core/symbols/SimpleLineSymbol'
import Color from '@arcgis/core/Color'
import LabelClass from '@arcgis/core/layers/support/LabelClass'

export const usePipeLineGroup = (name: string) => {
  const group = new GroupLayer({
    title: name,
    visible: false,
    layers: []
  })
  const colors = [
    'rgb(125,0,255)',
    'rgb(125,125,0)',
    'rgb(125,125,255)',
    'rgb(125,255,255)',
    'rgb(255,0,0)',
    'rgb(255,0,125)',
    'rgb(255,0,255)',
    'rgb(255,125,0)',
    'rgb(255,125,125)',
    'rgb(255,125,255)',
    'rgb(125,0,0)',
    'rgb(125,0,125)',
    'rgb(125,125,0)',
    'rgb(125,255,0)',
    'rgb(125,255,125)'
  ]
  const genSql = (field: string, value: any) => {
    return value ? `${field} = '${value}'` : `${field} is null`
  }
  const genColor = index => {
    return colors[index % colors.length]
  }
  const addSubLayer = (view?: __esri.MapView, layerId?: number, title?: string, sql = '1=1', color = colors[0]) => {
    if (!view || layerId === undefined) return
    const subLayer1 = new FeatureLayer({
      url:
        window.SITE_CONFIG.GIS_CONFIG.gisService
        + window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer
        + '/'
        + layerId,
      title: title || '其它',
      renderer: new SimpleRenderer({
        symbol: new SimpleLineSymbol({
          width: 4,
          color: new Color(color)
        })
      }),
      labelingInfo: [new LabelClass({
        labelPlacement: 'center-along',
        labelExpression: '[MATERIAL] DN[DIAMETER]'
      })],
      definitionExpression: sql
    })
    group.add(subLayer1)
  }
  const addSubLayerByGeoServer = (title?: any, geojson?:any, color = colors[0],sql?:string) => {
    // // create a new blob from geojson featurecollection
    // const blob = new Blob([JSON.stringify(geojson)], {
    //   type: "application/json"
    // });

    // // URL reference to the blob
    // const url = URL.createObjectURL(blob);
    // // create new geojson layer using the blob url
    // const subLayer1 = new GeoJSONLayer({
    //   url,
    //   title
    // });
    const subLayer1 = new WFSLayer({
      url:'/geoserver/anqing/wfs',
      id: 'anqing:给水管线',
      name:'anqing:给水管线',
      title: title || '其它',
      renderer: new SimpleRenderer({
        symbol: new SimpleLineSymbol({
          width: 4,
          color: new Color(color)
        })
      }),
      labelingInfo: [new LabelClass({
        labelPlacement: 'center-along',
        labelExpression: '[MATERIAL] DN[DIAMETER]'
      })],
      definitionExpression: sql
    })
    group.add(subLayer1)
  }
  const init = (view?: __esri.MapView) => {
    view?.when(() => {
      view.map.add(group)
    })
  }
  const destroy = () => {
    group.layers.removeAll()
    group.destroy()
  }
  return {
    init,
    addSubLayer,
    addSubLayerByGeoServer,
    destroy,
    genSql,
    genColor
  }
}
