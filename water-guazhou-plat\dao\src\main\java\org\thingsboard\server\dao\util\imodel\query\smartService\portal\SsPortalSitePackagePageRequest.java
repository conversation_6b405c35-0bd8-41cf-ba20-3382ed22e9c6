package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSitePackage;

@Getter
@Setter
public class SsPortalSitePackagePageRequest extends AdvancedPageableQueryEntity<SsPortalSitePackage, SsPortalSitePackagePageRequest> {
    // 仅包含文件夹
    private Boolean folderOnly;

    // 是否为公告
    private Boolean isNotice;

    // 是否为链接
    private Boolean jumpToUrl;

    // 是否为激活状态（根目录在页面显示或底部都算激活状态，子目录在页面显示算激活状态）
    private Boolean active;

}
