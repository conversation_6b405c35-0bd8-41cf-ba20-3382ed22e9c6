package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ORDER_FOLLOW_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class OrderFollowEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ORDER_FOLLOW_TIME)
    private Date time;

    @Column(name = ModelConstants.ORDER_FOLLOW_USER_ID)
    private String userId;

    @Column(name = ModelConstants.ORDER_FOLLOW_ORDER_ID)
    private String orderId;
}
