import{d as Fe,c as H,r as M,bB as I,W as De,ab as Z,b as Q,bH as Ce,am as X,o as Se,ay as ke,g as k,n as O,p as u,q as F,i as v,an as ee,aB as te,aJ as ae,aw as xe,bh as w,bo as oe,bR as se,F as E,av as Oe,h as Ae,bp as Ee,j as W,_ as Ne,aq as ze,N as Pe,cE as Re,O as Be,C as Ie}from"./index-r0dFAfgr.js";import{_ as Le}from"./FormTableColumnFilter-BT7pLXIC.js";import{P as Ve}from"./pipe-nogVzCHG.js";import{u as $e}from"./useDetector-BRcb7GRN.js";import{f as Je}from"./DateFormatter-Bm9a68Ax.js";import{g as ie,s as He}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{g as Me}from"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{q as L,g as ne}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{u as We}from"./useHighLight-DPevRAc5.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{c as qe}from"./geoserverUtils-wjOSMa7E.js";import{z as le}from"./echart-BoVIcYbV.js";import{s as je}from"./config-fy91bijz.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ToolHelper-BiiInOzB.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./config-DqqM5K5L.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";import"./QueryHelper-ILO3qZqg.js";const Ge={class:"detail-wrapper"},Ue={key:0,class:"empty"},Ke={key:1,class:"statistics-tab-wrapper"},Ye={class:"tab-wrapper"},Ze={key:0,class:"filter-box"},Qe={class:"content"},Xe={class:"tabs overlay-y"},et=["onClick"],tt={class:"right"},at={class:"statistics-table"},ot={class:"statistics-chart",style:{height:"100%"}},st={class:"attr-table-container"},it={class:"attr-table-box"},nt={class:"dimension-detail"},lt={class:"detail-header"},rt={class:"detail-title"},ct={class:"detail-count"},dt={class:"detail-items"},ut=["onClick"],mt={class:"item-info"},ft={class:"item-name"},pt={class:"item-count"},gt={class:"item-progress"},bt={class:"progress-bar"},ht={class:"percentage"},_t={class:"dimension-name"},vt={class:"total-count"},yt={class:"type-count"},Tt={class:"table-summary"},wt={class:"summary-item"},Ft={class:"summary-value"},Dt={class:"charts-container"},Ct={class:"bar-container"},St={class:"bar"},kt={class:"ring-container"},xt={class:"ring"},Ot=Fe({__name:"StatisticsCharts",props:{view:{},layerIds:{},queryParams:{},statisticsParams:{},tabs:{},unit:{},allDevice:{type:Boolean},prefix:{},percision:{},theme:{}},emits:["rowClick","ring-click","bar-click","attr-row-click","detail-refreshing","detail-refreshed","total-row-click"],setup(re,{expose:ce,emit:de}){const q=(e=[],t,r,l=2)=>{const c="总数",o=function(a){const s=/(?=(\B)(\d{3})+$)/g;return a.toString().replace(s,",")},m=e.reduce((a,s)=>a+(parseFloat(s.value)||0)*1,0),d=Z(m);return{tooltip:{trigger:"item",formatter:a=>(r||"")+a.name+": "+Number(a.value).toFixed(l)+" "+t},legend:{type:"scroll",icon:"circle",orient:"vertical",left:"right",top:"center",align:"left",itemGap:10,itemWidth:10,itemHeight:10,symbolKeepAspect:!0,textStyle:{color:"#fff",rich:{name:{align:"left",width:60,fontSize:12,color:W().isDark?"#fff":"#2A2A2A"},value:{align:"left",width:60,fontSize:12,color:"#00ff00"},count:{align:"left",width:60,fontSize:12},upRate:{align:"left",fontSize:12},downRate:{align:"left",fontSize:12,color:"#409EFF"}}},data:e.map(a=>a.name),formatter(a){if(e&&e.length)for(let s=0;s<e.length;s++){const f=e[s].scale.substr(0,e[s].scale.length-1);if(a===e[s].name)return"{name| "+(e[s].nameAlias||a)+"}{value| "+(e[s].valueAlias||e[s].value)+" "+(t||"")+"}{downRate| "+(o(Number(f||"0").toFixed(l))+"%"||"")+"}"}}},title:[{text:"{name|"+c+(t&&"("+d.unit+t+")"||"("+d.unit+")")+`}
{val|`+o(d.value.toFixed(l))+"}",top:"center",left:"19%",textAlign:"center",textStyle:{rich:{name:{fontSize:10,fontWeight:"normal",padding:[8,0],align:"center",color:W().isDark?"#fff":"#2A2A2A"},val:{fontSize:16,fontWeight:"bold",color:W().isDark?"#fff":"#2A2A2A"}}}}],series:[{type:"pie",radius:["45%","60%"],center:["20%","50%"],data:e,hoverAnimation:!0,label:{show:!1,formatter:a=>"{icon|●}{name|"+a.name+"}{value|"+o(Number(a.value||"0").toFixed(l))+"}",padding:[0,-100,25,-100],rich:{icon:{fontSize:16},name:{fontSize:14,padding:[0,10,0,4]},value:{fontSize:18,fontWeight:"bold"}}}}]}},z=H(),P=H(),g=H(),R=de,n=re,i=M({attributes:[],ringOption:null,barOption:null,tabs:[{label:"统计",value:"0"},{label:"详情",value:"1"}],curTab:"0",fieldStats:new Map,selectedDimension:"",dimensionTableData:[],expandedRows:[],totalCount:0}),x={tabFeatures:[],currentTabData:[]},{highlight:At,removeHoverHighLight:ue}=We(),V=M({labelPosition:"top",gutter:16,group:[{fields:[{type:"tabs",field:"type",tabs:[],onChange:e=>{console.log("一级Tab切换到:",e),_=e,console.log("记录当前选择的Tab:",_),y&&(console.log("一级Tab切换，重置筛选状态"),y=!1),$(e,!0)}}]}]}),h=M({maxHeight:300,dataList:[],columns:[{label:"序号",prop:"index"}],pagination:{refreshData:async({page:e,size:t})=>{var c,o;h.pagination.page=e,h.pagination.limit=t;const r=(o=(c=g.value)==null?void 0:c.dataForm)==null?void 0:o.type,l=await L(r);r&&l!==-1&&N(r)}},handleRowClick:e=>{var t,r,l,c;console.log("点击表格行:",e),h.currentRow=e,e.OBJECTID&&pe(e.OBJECTID),n.view?ve(n.view,(r=(t=g.value)==null?void 0:t.dataForm)==null?void 0:r.type,e.OBJECTID):R("rowClick",e,(c=(l=g.value)==null?void 0:l.dataForm)==null?void 0:c.type)}}),$=async(e,t)=>{if(y){const o=e?[await L(e)]:[];if(!t)return;n.allDevice?G():U(o.filter(m=>m>=0));return}y=!1;const r=await L(e),l=t||i.curTab==="1";N(e,r,l);const c=r>=0?[r]:[];t&&(n.allDevice?G():U(c))},me=e=>{var t;(t=g.value)!=null&&t.dataForm&&(g.value.dataForm.type=e)},j=e=>{if(!e||!i.fieldStats.has(e)){i.barOption=null,i.ringOption=null;return}const t=i.fieldStats.get(e),r=Array.from(t.entries()).map(([o,m])=>({value:m,key:o,label:o})),l=Array.from(t.entries()).map(([o,m])=>({value:m.toString(),name:o,scale:"0%"})),c=Array.from(t.values()).reduce((o,m)=>o+m,0);l.forEach(o=>{const m=parseInt(o.value);o.scale=c===0?"0%":(m/c*100).toFixed(n.percision||2)+"%"}),i.barOption=le(r,n.unit),i.ringOption=q(l,n.unit,n.prefix,n.percision??2)},J=async(e,t)=>{var r;if(!n.view||!((r=x.currentTabData)!=null&&r.length)){console.log("无法高亮：缺少地图视图或数据");return}try{ue();let l=[],c={},o="statistics-highlight",m=!1;switch(e){case"all":l=x.currentTabData,c={color:[255,255,0,.8],width:4,outlineColor:[255,0,0,1],outlineWidth:2};break;case"filter":if(!(t!=null&&t.fieldName)||!(t!=null&&t.fieldValue)){console.error("筛选高亮需要提供fieldName和fieldValue");return}l=x.currentTabData.filter(s=>s[t.fieldName]===t.fieldValue),c={color:[0,255,255,.8],width:4,outlineColor:[255,0,0,1],outlineWidth:2},console.log(`筛选条件: ${t.fieldName} = ${t.fieldValue}，找到${l.length}个要素`);break;case"single":if(!(t!=null&&t.objectId))return;const a=x.currentTabData.find(s=>s.OBJECTID===t.objectId);l=a?[a]:[],c={color:[255,0,255,.8],width:5,outlineColor:[255,255,0,1],outlineWidth:3},o="temp-highlight-layer",m=!0,console.log(`高亮单个要素 OBJECTID: ${t.objectId}`);break}if(l.length===0){console.warn("没有找到要高亮的要素");return}const d=ne(n.view,{id:o,title:e==="single"?"临时高亮":"统计高亮"});if(!d){console.error("无法创建高亮图层");return}d.removeAll();const p=[];l.forEach(a=>{if(a.geometry){let s=a.geometry;if(a.geometry.type&&typeof a.geometry.coordinates<"u"&&(s=qe(a.geometry)),s){const f=new Me({geometry:s,symbol:He(s.type,c),attributes:a});p.push(f)}}}),p.length>0&&(d.addMany(p),console.log(`成功高亮显示${p.length}个要素`),m&&p.length===1&&await ie(n.view,p[0],{avoidHighlight:!1,zoom:18}))}catch(l){console.error("高亮要素时出错:",l)}},fe=()=>J("all"),pe=e=>J("single",{objectId:e}),ge=e=>{_e(e.fieldName);const t=i.expandedRows.includes(e.fieldName);e.fieldName===i.selectedDimension&&t?i.expandedRows=i.expandedRows.filter(l=>l!==e.fieldName):t||(i.expandedRows=[e.fieldName])},be=(e,t)=>{i.expandedRows=t.map(r=>r.fieldName)},he=async(e,t)=>{var r,l,c,o,m,d,p;console.log("=== 点击详情项 ==="),console.log("fieldName:",e,"item:",t),console.log("currentSelectedTab:",_),console.log("refTab.value?.dataForm?.type:",(l=(r=g.value)==null?void 0:r.dataForm)==null?void 0:l.type),console.log("props.tabs:",(c=n.tabs)==null?void 0:c.map(a=>a.name)),console.log("==================");try{y=!0,await J("filter",{fieldName:e,fieldValue:t.name});let a=(m=(o=g.value)==null?void 0:o.dataForm)==null?void 0:m.type;if(console.log("当前Tab值:",a,"refTab.value:",g.value),console.log("记录的用户选择Tab:",_),a||(_?(a=_,console.log("currentTab为空，使用用户选择的Tab:",a)):n.tabs&&n.tabs.length>0&&(a=n.tabs[0].name,console.log("currentTab为空，使用默认值:",a)),a&&((d=g.value)!=null&&d.dataForm)&&(g.value.dataForm.type=a,console.log("更新refTab.value.dataForm.type为:",a))),a){const s=((p=x.currentTabData)==null?void 0:p.filter(f=>f[e]===t.name))||[];i.curTab="1",await I(),await N(a,null,!0,!0,s),setTimeout(()=>{y=!1},100),R("attr-row-click",{fieldName:e,fieldValue:t.name,count:s.length},a)}else console.error("无法获取currentTab，筛选失败"),console.log("refTab.value:",g.value),console.log("props.tabs:",n.tabs)}catch(a){console.error("处理详情项点击时出错:",a),y=!1}},_e=e=>{if(console.log("切换到维度:",e),i.selectedDimension===e){console.log("已经是当前维度，无需切换");return}i.selectedDimension=e,j(e),I(()=>{A()})},G=async()=>{var d,p,a,s;console.log("开始刷新统计图表");let e=(p=(d=g.value)==null?void 0:d.dataForm)==null?void 0:p.type;console.log("当前选中的Tab:",e),console.log("记录的用户选择Tab:",_),e||(_?(e=_,console.log("currentTab为空，使用用户选择的Tab:",e)):n.tabs&&n.tabs.length>0&&(e=n.tabs[0].name,console.log("currentTab为空，使用默认值:",e)),e&&((a=g.value)!=null&&a.dataForm)&&(g.value.dataForm.type=e,console.log("更新refTab.value.dataForm.type为:",e)));const t=(s=n.tabs)==null?void 0:s.find(f=>f.name===e);if(!t||!t.data||t.data.length===0){i.attributes=[],i.barOption=null,i.ringOption=null;return}const r=new Map;t.data.forEach(f=>{const b=f.OBJECTID;r.has(b)||r.set(b,f)});const l=Array.from(r.values()),c=new Map;let o=[];if(e==="给水管线"||e==="管线")o=["管材","管径"];else if(e==="节点"||e==="测点")o=["备注"];else if(l.length>0){const f=l[0];console.log("数据样本:",f),o=["管材","材质","管径","备注","SUBTYPE","STATUS"].filter(C=>f.hasOwnProperty(C)),console.log("自动检测到的字段:",o)}o.forEach(f=>{const b=new Map;l.forEach(C=>{const D=C[f]||"未知";b.set(D,(b.get(D)||0)+1)}),b.size>0&&c.set(f,b)}),c.forEach((f,b)=>{}),i.fieldStats=c,i.totalCount=l.length,x.currentTabData=l,i.dimensionTableData=Array.from(c.entries()).map(([f,b])=>{const C=Array.from(b.values()).reduce((S,T)=>S+T,0),D=Array.from(b.entries()).sort((S,T)=>T[1]-S[1]).map(([S,T])=>({name:S,count:T,percentage:l.length>0?Number((T/l.length*100).toFixed(1)):0}));return{fieldName:f,total:C,types:b.size,details:D}});const m=Array.from(c.keys());(!i.selectedDimension||!m.includes(i.selectedDimension))&&(m.length>0?i.selectedDimension=m[0]:i.selectedDimension=""),i.selectedDimension?i.expandedRows=[i.selectedDimension]:i.expandedRows=[],i.selectedDimension?j(i.selectedDimension):(i.barOption=null,i.ringOption=null),fe(),await I(),A()},U=async e=>{var r,l,c;if(!(e!=null&&e.length))return;const t=await Ve({usertoken:De().gToken,layerids:JSON.stringify(e),group_fields:JSON.stringify(n.statisticsParams.group_fields||[]),statistic_field:n.statisticsParams.statistic_field,statistic_type:n.statisticsParams.statistic_type,where:n.queryParams.where||"1=1",geometry:n.queryParams.geometry,f:"pjson"});if(t.data.code===1e4){const o=((c=(l=(r=t.data)==null?void 0:r.result)==null?void 0:l.rows[0])==null?void 0:c.rows)||[],m=o.map(a=>{var s;return{value:a[n.statisticsParams.statistic_field],key:(n.prefix||"")+(((s=a[n.statisticsParams.group_fields[0]])==null?void 0:s.toString())||"--")}})||[];i.attributes=o.map(a=>{var s;return{value:(a[n.statisticsParams.statistic_field]||"--")+(n.unit||""),label:(n.prefix||"")+(((s=a[n.statisticsParams.group_fields[0]])==null?void 0:s.toString())||"--")}})||[],i.barOption=le(m,n.unit);const d=o.map(a=>{const s=Z(a[n.statisticsParams.statistic_field]||0);return{value:a[n.statisticsParams.statistic_field]||0,name:(n.prefix||"")+(a[n.statisticsParams.group_fields[0]]||"--"),valueAlias:s.value.toFixed(n.percision||2)+s.unit,scale:"0%"}})||[],p=d.reduce((a,s)=>s.value+a,0);d.map(a=>{a.scale=p===0?"0%":(a.value/p*100).toFixed(n.percision||2)+"%"}),i.attributes.push({label:"合计",notLink:!0,value:p.toFixed(n.statisticsParams.statistic_type==="1"?0:2)+(n.unit||"")}),i.ringOption=q(d,n.unit,n.prefix,n.percision||2),A()}else Q.error("统计失败")},N=async(e,t,r,l,c)=>{var o,m;R("detail-refreshing");try{if(h.loading=!0,!e){console.log("没有tab，清空表格数据"),h.dataList=[],h.loading=!1;return}const d=l?c:(m=(o=n.tabs)==null?void 0:o.find(S=>S.name===e))==null?void 0:m.data;if(!d||!Array.isArray(d)||d.length===0){h.dataList=[],h.pagination.total=0,h.loading=!1;return}h.pagination.total=d.length;const p=h.pagination.page||1,a=h.pagination.limit||20,s=(p-1)*a,f=s+a,b=d.slice(s,f);console.log(`分页信息: 总数${d.length}, 当前页${p}, 每页${a}, 当前页数据${b.length}条`);const C=[];if(d.length>0){const S=d[0];Object.keys(S).forEach(T=>{if(je.indexOf(T)===-1){const Y={label:T,prop:T,minWidth:160};(T.toLowerCase().includes("date")||T.toLowerCase().includes("time"))&&(Y.formatter=we=>{const B=we[T];return B&&typeof B=="number"?Je(B,Ce):B||"--"}),C.push(Y)}})}h.columns=C,h.dataList=b;const D=ne(n.view,{id:"pipe-detail",title:"详情"});d!=null&&d.length?r&&(console.log("统计模式下，地图显示功能暂时简化"),D&&D.removeAll(),x.tabFeatures=d):D==null||D.removeAll()}catch(d){console.dir(d),Q.error("查询失败")}h.loading=!1,R("detail-refreshed")},K=()=>{var l;if(console.log("initTab被调用，isFiltering:",y),y){console.log("正在筛选状态，跳过initTab");return}const e=V.group[0].fields[0];if(!e)return;const t=n.tabs||[];e.tabs=t.map(c=>({...c,value:c.name}))||[];let r=_;if(!r&&t.length>0&&(r=(l=t[0])==null?void 0:l.name),console.log("initTab选择的tab:",r,"用户选择的Tab:",_),r){g.value&&(g.value.dataForm.type=r);const c=V.group[0].fields[0];c&&c.tabs&&console.log("同步Form的Tab选择状态为:",r),_||(_=r,console.log("initTab首次记录选择的Tab:",_)),console.log("initTab调用refreshDetail，tab:",r),$(r,!0)}else console.log("initTab: 没有可用的tab，跳过refreshDetail")},ve=async(e,t,r)=>{var c,o;t=t||((o=(c=g.value)==null?void 0:c.dataForm)==null?void 0:o.type);const l=x.tabFeatures.find(m=>m.attributes.OBJECTID===r);l&&await ie(e,l)},ye=()=>{var e,t;return(t=(e=g.value)==null?void 0:e.dataForm)==null?void 0:t.type},A=()=>{z.value&&i.barOption?z.value.resize():console.log("柱状图刷新失败，refBar:",z.value,"barOption:",i.barOption),P.value&&i.ringOption?P.value.resize():console.log("环形图刷新失败，refRing:",P.value,"ringOption:",i.ringOption)};X(()=>n.tabs,()=>{console.log("props.tabs变化，触发initTab"),K()});let y=!1,_=null;X(()=>i.curTab,async(e,t)=>{var r,l,c;if(console.log("Tab切换监听器触发:",{newTab:e,oldTab:t,isFiltering:y}),await I(),e==="0")console.log("切换到统计Tab，刷新图表"),y&&(console.log("重置筛选状态"),y=!1),A();else if(e==="1"&&t==="0")if(console.log("从统计Tab切换到详情Tab，isFiltering:",y),y)console.log("筛选状态，跳过表格数据刷新，保持筛选结果");else{console.log("非筛选状态，执行正常的表格数据刷新");let o=(l=(r=g.value)==null?void 0:r.dataForm)==null?void 0:l.type;if(console.log("Tab切换监听器中的currentTab:",o),o||(_?(o=_,console.log("Tab切换监听器：currentTab为空，使用用户选择的Tab:",o)):n.tabs&&n.tabs.length>0&&(o=n.tabs[0].name,console.log("Tab切换监听器：currentTab为空，使用默认值:",o)),o&&((c=g.value)!=null&&c.dataForm)&&(g.value.dataForm.type=o,console.log("Tab切换监听器：更新refTab.value.dataForm.type为:",o))),o){const m=await L(o);console.log("调用refreshTable - 来源：Tab切换监听器"),N(o,m,!0)}else console.error("Tab切换监听器：无法获取currentTab")}});const Te=$e();return Se(()=>{K(),Te.listenToMush(document.documentElement,()=>{A()})}),ce({refreshChar:A,refreshDetail:$,setCurLayer:me,getCurLayer:ye,refreshTable:N}),(e,t)=>{var a;const r=Ne,l=Le,c=ze,o=Pe,m=Re,d=Be,p=ke("VChart");return k(),O("div",Ge,[(a=e.tabs)!=null&&a.length?(k(),O("div",Ke,[u("div",Ye,[F(r,{ref_key:"refTab",ref:g,config:v(V)},null,8,["config"]),v(i).curTab==="1"?(k(),O("div",Ze,[F(l,{columns:v(h).columns,"show-tooltip":!0},null,8,["columns"])])):ee("",!0)]),u("div",Qe,[u("div",Xe,[(k(!0),O(te,null,ae(v(i).tabs,(s,f)=>(k(),O("div",{key:f,class:xe(["tab-item",[v(i).curTab===s.value?"active":""]]),onClick:b=>v(i).curTab=s.value},w(s.label),11,et))),128))]),u("div",tt,[oe(u("div",at,[F(c,{config:v(h)},null,8,["config"])],512),[[se,v(i).curTab==="1"]]),oe(u("div",ot,[u("div",st,[u("div",it,[F(d,{data:v(i).dimensionTableData,style:{width:"100%"},size:"small",onRowClick:ge,"row-key":"fieldName","expand-row-keys":v(i).expandedRows,onExpandChange:be},{default:E(()=>[F(o,{type:"expand"},{default:E(({row:s})=>[u("div",nt,[u("div",lt,[u("span",rt,w(s.fieldName)+"分布详情",1),u("span",ct,"共"+w(s.details.length)+"类",1)]),u("div",dt,[(k(!0),O(te,null,ae(s.details,(f,b)=>(k(),O("div",{key:b,class:"detail-item",onClick:C=>he(s.fieldName,f)},[u("div",mt,[u("span",ft,w(f.name),1),u("span",pt,w(f.count)+w(e.unit),1)]),u("div",gt,[u("div",bt,[u("div",{class:"progress-fill",style:Oe({width:f.percentage+"%"})},null,4)]),u("span",ht,w(f.percentage)+"%",1)])],8,ut))),128))])])]),_:1}),F(o,{prop:"fieldName",label:"统计维度","min-width":"100"},{default:E(({row:s})=>[u("div",_t,[s.fieldName===v(i).selectedDimension?(k(),Ae(m,{key:0,class:"selected-icon"},{default:E(()=>[F(v(Ee))]),_:1})):ee("",!0),u("span",null,w(s.fieldName),1)])]),_:1}),F(o,{prop:"total",label:"总数",width:"80",align:"right"},{default:E(({row:s})=>[u("span",vt,w(s.total)+w(e.unit),1)]),_:1}),F(o,{prop:"types",label:"类型数",width:"80",align:"right"},{default:E(({row:s})=>[u("span",yt,w(s.types)+"类",1)]),_:1})]),_:1},8,["data","expand-row-keys"]),u("div",Tt,[u("div",wt,[t[0]||(t[0]=u("span",{class:"summary-label"},"数据总计:",-1)),u("span",Ft,w(v(i).totalCount)+w(e.unit),1)])])])]),u("div",Dt,[u("div",Ct,[u("div",St,[F(p,{ref_key:"refBar",ref:z,option:v(i).barOption,onClick:e.handleBarClick},null,8,["option","onClick"])])]),u("div",kt,[u("div",xt,[F(p,{ref_key:"refRing",ref:P,option:v(i).ringOption,onClick:e.handleRingClick},null,8,["option","onClick"])])])])],512),[[se,v(i).curTab==="0"]])])])])):(k(),O("div",Ue," 暂无结果 "))])}}}),ao=Ie(Ot,[["__scopeId","data-v-2cdc10d2"]]);export{ao as default};
