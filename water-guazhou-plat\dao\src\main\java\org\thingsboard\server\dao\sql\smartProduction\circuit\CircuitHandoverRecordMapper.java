package org.thingsboard.server.dao.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitHandoverRecord;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitHandoverRecordPageRequest;

@Mapper
public interface CircuitHandoverRecordMapper extends BaseMapper<CircuitHandoverRecord> {
    IPage<CircuitHandoverRecord> findByPage(CircuitHandoverRecordPageRequest request);

    boolean update(CircuitHandoverRecord entity);


}
