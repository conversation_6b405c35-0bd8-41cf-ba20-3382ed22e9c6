import{e as s,y as p,a as o}from"./Point-WxyopZva.js";import{g as a,bk as m}from"./MapView-DaoQedLH.js";import{aW as g,R as l}from"./index-r0dFAfgr.js";import{f as c,u as n}from"./LayerView-BSt9B8Gh.js";import{i as d}from"./GraphicContainer-C86a5RZy.js";import{a as u}from"./GraphicsView2D-DDTEO9AX.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./enums-L38xj_2E.js";import"./BaseGraphicContainer-Cqw9Xlck.js";import"./FeatureContainer-B5oUlI2-.js";import"./AttributeStoreView-B0-phoCE.js";import"./TiledDisplayObject-C5kAiJtw.js";import"./visualVariablesUtils-0WgcmuMn.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./number-CoJp78Rz.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./FramebufferObject-8j9PRuxE.js";import"./TileContainer-CC8_A7ZF.js";import"./WGLContainer-Dyx9110G.js";import"./vec4f32-CjrfB-0a.js";import"./ProgramTemplate-tdUBoAol.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./utils-DPUVnAXL.js";import"./StyleDefinition-Bnnz5uyC.js";import"./config-MDUrh2eL.js";import"./GeometryUtils-BRRfazic.js";import"./earcut-BJup91r2.js";import"./vec3f32-nZdmKIgz.js";import"./cimAnalyzer-CMgqZsaO.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./normalizeUtilsSync-NMksarRY.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./Matcher-v9ErZwmD.js";import"./tileUtils-B7X19rIS.js";import"./libtess-lH4Jrtkh.js";import"./TurboLine-CDscS66C.js";import"./ExpandedCIM-C1laM-_7.js";import"./schemaUtils-DLXXqxNF.js";import"./util-DPgA-H2V.js";import"./ComputedAttributeStorage-CF7WDnl8.js";import"./arcadeTimeUtils-CyWQANWo.js";import"./executionError-BOo4jP8A.js";import"./centroid-UTistape.js";const w={remove(){},pause(){},resume(){}};let e=class extends c(n){constructor(){super(...arguments),this._highlightIds=new Map}attach(){this.graphicsView=new u({requestUpdateCallback:()=>this.requestUpdate(),view:this.view,graphics:this.layer.graphics,container:new d(this.view.featuresTilingScheme)}),this._updateHighlight(),this.container.addChild(this.graphicsView.container),this.addAttachHandles(this.layer.on("graphic-update",this.graphicsView.graphicUpdateHandler))}detach(){this.container.removeAllChildren(),this.graphicsView=g(this.graphicsView)}async hitTest(i){return this.graphicsView?this.graphicsView.hitTest(i).map(t=>({type:"graphic",graphic:t,mapPoint:i,layer:this.layer})):null}async fetchPopupFeatures(i){return this.graphicsView?this.graphicsView.hitTest(i).filter(t=>!!t.popupTemplate):[]}queryGraphics(){return Promise.resolve(this.graphicsView.graphics)}update(i){this.graphicsView.processUpdate(i)}moveStart(){}viewChange(){this.graphicsView.viewChange()}moveEnd(){}isUpdating(){return!this.graphicsView||this.graphicsView.updating}highlight(i){let t;typeof i=="number"?t=[i]:i instanceof a?t=[i.uid]:Array.isArray(i)&&i.length>0?t=typeof i[0]=="number"?i:i.map(h=>h&&h.uid):m.isCollection(i)&&i.length>0&&(t=i.map(h=>h&&h.uid).toArray());const r=t==null?void 0:t.filter(l);return r!=null&&r.length?(this._addHighlight(r),{remove:()=>this._removeHighlight(r)}):w}_addHighlight(i){for(const t of i)if(this._highlightIds.has(t)){const r=this._highlightIds.get(t);this._highlightIds.set(t,r+1)}else this._highlightIds.set(t,1);this._updateHighlight()}_removeHighlight(i){for(const t of i)if(this._highlightIds.has(t)){const r=this._highlightIds.get(t)-1;r===0?this._highlightIds.delete(t):this._highlightIds.set(t,r)}this._updateHighlight()}_updateHighlight(){var i;(i=this.graphicsView)==null||i.setHighlight(Array.from(this._highlightIds.keys()))}};s([p()],e.prototype,"graphicsView",void 0),e=s([o("esri.views.2d.layers.GraphicsLayerView2D")],e);const vi=e;export{vi as default};
