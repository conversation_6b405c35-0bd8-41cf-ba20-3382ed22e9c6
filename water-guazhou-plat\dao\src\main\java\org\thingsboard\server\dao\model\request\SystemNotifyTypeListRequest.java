package org.thingsboard.server.dao.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.thingsboard.server.dao.model.sql.notify.SystemNotifyType;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@EqualsAndHashCode(callSuper = true)
@Data
public class SystemNotifyTypeListRequest extends AdvancedPageableQueryEntity<SystemNotifyType, SystemNotifyTypeListRequest> {

    private String type;

    private String menuName;

    private String sendType;

    private String status;

    private String tenantId;

}
