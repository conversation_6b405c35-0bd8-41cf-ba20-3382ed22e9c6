import{_ as R}from"./index-C9hz-UZb.js";import{d as F,M as H,a6 as G,bF as i,r as _,am as M,c as g,a8 as U,s as T,j as v,bB as J,bu as W,ay as K,g as C,n as O,q as f,i as p,F as b,cs as w,an as D,dF as Q,dA as $,aq as X,al as Y,b7 as Z,aj as ee,C as te}from"./index-r0dFAfgr.js";import{_ as ae}from"./CardSearch-CB_HNR-Q.js";import{l as re}from"./echart-0WahFbKA.js";import{e as ne,a as le}from"./queryStatistics-CQ9DBM08.js";import{u as oe}from"./useStation-DJgnSZIA.js";import{f as se}from"./formartColumn-D5r7JJ2G.js";import{b as ie}from"./zhandian-YaGuQZe6.js";import"./Search-NSrhrIa_.js";const ce={class:"wrapper"},de={key:1},ue=F({__name:"index",setup(me){const{$messageWarning:L}=H(),N=G(),{getStationTree:I,getStationTreeByDisabledType:A}=oe();i().date();const o=_({type:"date",chartOption:null,activeName:"echarts",data:null,checkedKeys:[],stationTree:[]});M(()=>o.activeName,()=>{o.activeName==="echarts"&&S()});const x=g(),k=g(),y=g(),P=g();let h=_([]);const V=_({defaultParams:{queryType:"15m",type:"day",year:[i().format(),i().format()],month:[i().format(),i().format()],day:[i().startOf("day").format(),i().format()]},filters:[{type:"select-tree",label:"监测点:",multiple:!0,field:"attributeId",clearable:!1,showCheckbox:!0,lazy:!0,options:U(()=>o.stationTree),lazyLoad:(e,t)=>{var a,l;if(e.level===0)return t([]);if(((a=e.data.children)==null?void 0:a.length)>0)return t(e.data.children);if(e.isLeaf)return t([]);if((l=e.data)!=null&&l.isLeaf)return t([]);ie({stationId:e.data.id}).then(c=>{var s;const n=(s=c.data)==null?void 0:s.map(m=>({label:m.type,value:"",id:"",children:m.attrList.map(r=>({label:r.name,value:r.id,id:r.id,isLeaf:!0}))}));return t(n)})}},{type:"radio-button",field:"type",options:[{label:"日",value:"day"},{label:"月",value:"month"},{label:"年",value:"year"}],label:"时间频率"},{type:"datetimerange",label:"选择日期",width:120,field:"day",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择日期",field:"month",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择日期",field:"year",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="day"}},{type:"select",label:"时间间隔:",field:"queryType",clearable:!1,options:[{label:"1 m",value:"1m"},{label:"5 m",value:"5m"},{label:"10 m",value:"10m"},{label:"15 m",value:"15m"},{label:"1小时",value:"hour"}],handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"},itemContainerStyle:{width:"180px"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var t;u.pagination.page=1,(((t=y.value)==null?void 0:t.queryParams)||{}).attributeId.length>0?z():L("选择监测点")},svgIcon:T(Y)},{type:"default",perm:!0,text:"重置",svgIcon:T(Z),click:()=>{var e;(e=y.value)==null||e.resetForm()}},{text:"导出",perm:!0,type:"warning",svgIcon:T(ee),hide:()=>o.activeName!=="list",click:()=>j()}]}]}),u=_({loading:!1,dataList:[],columns:[],operations:[],pagination:{page:1,limit:20,total:0,layout:"total, prev, pager, next, sizes, jumper",refreshData:({page:e,size:t})=>{u.pagination.page=e,u.pagination.limit=t,u.dataList=h==null?void 0:h.slice((e-1)*t,e*t)}}}),j=()=>{var e;if(u.dataList.length>0){const t=((e=y.value)==null?void 0:e.queryParams)||{};console.log(t);const[a,l]=t[t.type]||[];let c=0,n=0;t.type==="day"?(c=a?i(a).valueOf():"",n=l?i(l).valueOf():""):(c=a?i(a).startOf(t.type).valueOf():"",n=l?i(l).endOf(t.type).valueOf():"");const s={attributes:t.attributeId.join(","),queryType:t.type==="month"?"day":t.type==="year"?"month":t.queryType,start:c,end:n};ne(s).then(m=>{const r=window.URL.createObjectURL(m.data),d=document.createElement("a");d.style.display="none",d.href=r,d.setAttribute("download","数据对比表.xlsx"),document.body.appendChild(d),d.click()})}else L("无数据导出")},z=()=>{var s;u.loading=!0;const e=((s=y.value)==null?void 0:s.queryParams)||{},[t,a]=e[e.type]||[];let l=0,c=0;e.type==="day"?(l=t?i(t).valueOf():"",c=a?i(a).valueOf():""):(l=t?i(t).startOf(e.type).valueOf():"",c=a?i(a).endOf(e.type).valueOf():"");const n={attributes:e.attributeId.join(","),queryType:e.type==="month"?"day":e.type==="year"?"month":e.queryType,start:l,end:c};le(n).then(m=>{var d;const r=(d=m.data)==null?void 0:d.data;o.data=r,h=r==null?void 0:r.tableDataList,u.columns=se(r==null?void 0:r.tableInfo),u.dataList=h.slice(0*20,20),u.pagination.total=r==null?void 0:r.tableDataList.length,u.loading=!1,S()})},B=()=>{var e;(e=x.value)==null||e.resize()},S=()=>{var a,l,c;const e=re();e.series=[];const t={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:v().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:v().isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.xAxis.data=(a=o.data)==null?void 0:a.tableDataList.map(n=>n.ts),(l=o.data)==null||l.tableInfo.map((n,s)=>{var m;if(n.columnValue!=="ts"){const r=JSON.parse(JSON.stringify(t));r.name=n.columnName,r.data=(m=o.data)==null?void 0:m.tableDataList.map(q=>q[n.columnValue]);const d=n.columnName.split("--")[2]+(n.unit?"("+n.unit+")":"");s===1?e.yAxis[0].name=d:s>1&&(e.yAxis.find(E=>E.name===d)||(r.yAxisIndex=s-1,e.grid.right=50*(s-1),e.yAxis.push({position:"right",alignTicks:!0,type:"value",name:d,offset:50*(s-2),axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{color:"#656b84"}},splitLine:{lineStyle:{color:v().isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}}))),e.series.push(r)}}),(c=x.value)==null||c.clear(),J(()=>{k.value&&N.listenTo(k.value,()=>{o.chartOption=e,B()})})};return W(async()=>{const e=await I("泵站");await A(e,["Project","Station"],!1,"Station"),o.stationTree=e,console.log(" state.stationTree ",o.stationTree)}),(e,t)=>{const a=ae,l=Q,c=$,n=K("VChart"),s=X,m=R;return C(),O("div",ce,[f(a,{ref_key:"cardSearch",ref:y,config:p(V)},null,8,["config"]),f(m,{class:"card",title:" "},{right:b(()=>[f(c,{modelValue:p(o).activeName,"onUpdate:modelValue":t[0]||(t[0]=r=>p(o).activeName=r)},{default:b(()=>[f(l,{label:"echarts"},{default:b(()=>[f(p(w),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),f(l,{label:"list"},{default:b(()=>[f(p(w),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:b(()=>[p(o).activeName==="echarts"?(C(),O("div",{key:0,ref_key:"agriEcoDev",ref:k,class:"chart-box"},[f(n,{ref_key:"refChart",ref:x,theme:p(v)().isDark?"dark":"light",option:p(o).chartOption},null,8,["theme","option"])],512)):D("",!0),p(o).activeName==="list"?(C(),O("div",de,[f(s,{ref_key:"refCardTable",ref:P,class:"card-table",config:p(u)},null,8,["config"])])):D("",!0)]),_:1})])}}}),ke=te(ue,[["__scopeId","data-v-23c9ca42"]]);export{ke as default};
