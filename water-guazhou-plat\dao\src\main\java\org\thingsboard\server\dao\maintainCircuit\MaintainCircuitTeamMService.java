package org.thingsboard.server.dao.maintainCircuit;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.maintainCircuit.MaintainCircuitTeamM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface MaintainCircuitTeamMService {
    PageData getList(String name, String type, int page, int size, String tenantId);

    MaintainCircuitTeamM save(MaintainCircuitTeamM maintainCircuitTeamM);

    IstarResponse delete(List<String> ids);
}
