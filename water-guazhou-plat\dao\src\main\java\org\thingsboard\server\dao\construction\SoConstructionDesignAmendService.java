package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesignAmend;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionDesignAmendPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionDesignAmendSaveRequest;

public interface SoConstructionDesignAmendService {
    /**
     * 分页条件查询工程设计变更
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoConstructionDesignAmend> findAllConditional(SoConstructionDesignAmendPageRequest request);

    /**
     * 保存工程设计变更
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoConstructionDesignAmend save(SoConstructionDesignAmendSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoConstructionDesignAmend entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
