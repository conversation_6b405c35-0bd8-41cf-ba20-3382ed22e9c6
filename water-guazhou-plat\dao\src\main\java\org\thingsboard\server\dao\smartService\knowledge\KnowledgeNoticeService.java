package org.thingsboard.server.dao.smartService.knowledge;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeNotice;

import java.util.List;

/**
 * 知识库公告
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface KnowledgeNoticeService {
    PageData getList(String type, Long startTime, Long endTime, int page, int size, String tenantId);

    KnowledgeNotice save(KnowledgeNotice knowledgeNotice);

    int delete(List<String> ids);

    List<KnowledgeNotice> getAll(String tenantId);

    KnowledgeNotice getById(String id);
}
