import{b1 as a}from"./MapView-DaoQedLH.js";import{T as p}from"./index-r0dFAfgr.js";var e;function d(r){switch(r){case"multiply":default:return e.Multiply;case"ignore":return e.Ignore;case"replace":return e.Replace;case"tint":return e.Tint}}function g(r,o,n){if(p(r)||o===e.Ignore)return n[0]=255,n[1]=255,n[2]=255,void(n[3]=255);const u=a(Math.round(r[3]*i),0,i),c=u===0||o===e.Tint?0:o===e.Replace?s:f;n[0]=a(Math.round(r[0]*t),0,t),n[1]=a(Math.round(r[1]*t),0,t),n[2]=a(Math.round(r[2]*t),0,t),n[3]=u+c}(function(r){r[r.Multiply=1]="Multiply",r[r.Ignore=2]="Ignore",r[r.Replace=3]="Replace",r[r.Tint=4]="Tint"})(e||(e={}));const t=255,i=85,s=i,f=2*i;export{d as n,g as o,e as r};
