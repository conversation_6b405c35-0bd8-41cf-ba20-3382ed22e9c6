package org.thingsboard.server.dao.report;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.PressureReport;
import org.thingsboard.server.dao.sql.report.PressureReportRepository;

import java.util.Date;

@Slf4j
@Service
public class PressureReportServiceImpl implements PressureReportService {


    @Autowired
    private PressureReportRepository pressureReportRepository;

    @Override
    public Object getData(Date startTime, Date endTime, String stationId) {
        return pressureReportRepository.findByStationIdAndTimeBetween(stationId, startTime, endTime);
    }

    @Override
    public void save(PressureReport entity) {
        pressureReportRepository.save(entity);
    }
}
