/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.datavVO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * dataV 设备折线图（柱状图）类型图表 表现层对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceLineOrBarVO implements Comparable<DeviceLineOrBarVO>{

    private String xData;
    private BigDecimal yData;
    private String legend;


    @Override
    public int compareTo(DeviceLineOrBarVO o) {
        return o.getLegend().compareTo(legend);
    }
}
