-- 地下水水位监测表
CREATE TABLE IF NOT EXISTS tb_groundwater_level (
    id VARCHAR(36) PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    station_id VARCHAR(36) NOT NULL,
    water_level NUMERIC(10, 2),
    level_change NUMERIC(10, 2),
    recharge_amount NUMERIC(12, 2),
    rainfall_amount NUMERIC(10, 2),
    record_time TIMESTAMP,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    remark TEXT,
    status INTEGER,
    data_source INTEGER
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_groundwater_level_tenant ON tb_groundwater_level(tenant_id);
CREATE INDEX IF NOT EXISTS idx_groundwater_level_station ON tb_groundwater_level(station_id);
CREATE INDEX IF NOT EXISTS idx_groundwater_level_record_time ON tb_groundwater_level(record_time);
CREATE INDEX IF NOT EXISTS idx_groundwater_level_status ON tb_groundwater_level(status);

-- 注释
COMMENT ON TABLE tb_groundwater_level IS '地下水水位监测表';
COMMENT ON COLUMN tb_groundwater_level.id IS '主键ID';
COMMENT ON COLUMN tb_groundwater_level.tenant_id IS '租户ID';
COMMENT ON COLUMN tb_groundwater_level.station_id IS '测点ID';
COMMENT ON COLUMN tb_groundwater_level.water_level IS '水位高度(米)';
COMMENT ON COLUMN tb_groundwater_level.level_change IS '水位变化量(米)';
COMMENT ON COLUMN tb_groundwater_level.recharge_amount IS '地下水补给量(立方米)';
COMMENT ON COLUMN tb_groundwater_level.rainfall_amount IS '降雨量(毫米)';
COMMENT ON COLUMN tb_groundwater_level.record_time IS '记录时间';
COMMENT ON COLUMN tb_groundwater_level.create_time IS '创建时间';
COMMENT ON COLUMN tb_groundwater_level.update_time IS '更新时间';
COMMENT ON COLUMN tb_groundwater_level.remark IS '备注';
COMMENT ON COLUMN tb_groundwater_level.status IS '水位状态 (1-正常, 2-偏低, 3-偏高)';
COMMENT ON COLUMN tb_groundwater_level.data_source IS '数据来源 (1-手动录入, 2-设备采集)';

-- 地下水涵养水位分析表
CREATE TABLE IF NOT EXISTS tb_groundwater_recharge (
    id VARCHAR(36) PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    area_id VARCHAR(36) NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    initial_level NUMERIC(10, 2),
    final_level NUMERIC(10, 2),
    level_change NUMERIC(10, 2),
    recharge_amount NUMERIC(12, 2),
    rainfall_amount NUMERIC(10, 2),
    consumption_amount NUMERIC(12, 2),
    suggested_recharge_amount NUMERIC(12, 2),
    recharge_suggestion TEXT,
    analysis_result TEXT,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    status INTEGER,
    creator VARCHAR(36)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_groundwater_recharge_tenant ON tb_groundwater_recharge(tenant_id);
CREATE INDEX IF NOT EXISTS idx_groundwater_recharge_area ON tb_groundwater_recharge(area_id);
CREATE INDEX IF NOT EXISTS idx_groundwater_recharge_time ON tb_groundwater_recharge(start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_groundwater_recharge_status ON tb_groundwater_recharge(status);

-- 注释
COMMENT ON TABLE tb_groundwater_recharge IS '地下水涵养水位分析表';
COMMENT ON COLUMN tb_groundwater_recharge.id IS '主键ID';
COMMENT ON COLUMN tb_groundwater_recharge.tenant_id IS '租户ID';
COMMENT ON COLUMN tb_groundwater_recharge.area_id IS '区域ID';
COMMENT ON COLUMN tb_groundwater_recharge.start_time IS '分析周期开始时间';
COMMENT ON COLUMN tb_groundwater_recharge.end_time IS '分析周期结束时间';
COMMENT ON COLUMN tb_groundwater_recharge.initial_level IS '期初水位(米)';
COMMENT ON COLUMN tb_groundwater_recharge.final_level IS '期末水位(米)';
COMMENT ON COLUMN tb_groundwater_recharge.level_change IS '水位变化量(米)';
COMMENT ON COLUMN tb_groundwater_recharge.recharge_amount IS '补给量(立方米)';
COMMENT ON COLUMN tb_groundwater_recharge.rainfall_amount IS '降雨量(毫米)';
COMMENT ON COLUMN tb_groundwater_recharge.consumption_amount IS '地下水总消耗量(立方米)';
COMMENT ON COLUMN tb_groundwater_recharge.suggested_recharge_amount IS '建议补给量(立方米)';
COMMENT ON COLUMN tb_groundwater_recharge.recharge_suggestion IS '涵养水位建议';
COMMENT ON COLUMN tb_groundwater_recharge.analysis_result IS '分析结果';
COMMENT ON COLUMN tb_groundwater_recharge.create_time IS '创建时间';
COMMENT ON COLUMN tb_groundwater_recharge.update_time IS '更新时间';
COMMENT ON COLUMN tb_groundwater_recharge.status IS '分析状态(1-良好,2-一般,3-不足)';
COMMENT ON COLUMN tb_groundwater_recharge.creator IS '创建人';