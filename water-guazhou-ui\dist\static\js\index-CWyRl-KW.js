import{I as fe}from"./ImportButton-BXPDxx92.js";import{d as ge,c as h,r as b,a8 as q,S as z,b as u,W as ye,Q as he,g as x,n as ve,q as d,F as v,i as a,h as M,an as I,p as F,G as $,bh as ke,aB as be,J as _e,C as De}from"./index-r0dFAfgr.js";import{_ as we}from"./ArcLayout-CHnHL9Pv.js";import{_ as Ce}from"./index-BJ-QPYom.js";import{b as xe,c as Fe,C as Se,I as Le}from"./index-0NlGN6gS.js";import{P as Pe}from"./pipe-nogVzCHG.js";import{D as Te}from"./DrawerBox-CLde5xC8.js";import{_ as N}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{s as B,e as Me}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{c as Ie,g as Ne}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{E as Q}from"./StatisticsHelper-D-s_6AyQ.js";import{u as je}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as Ue}from"./usePartition-DkcY9fQ2.js";import"./geometryEngineBase-BhsKaODW.js";import Be from"./BigUserHookUp-pb7Ci3Xh.js";import He from"./DeviceHookUp-CV912_PJ.js";import Ae from"./MoreDetail-DtHcj6ih.js";import Re from"./PartitionDetail-D10Zp2JB.js";import Ee from"./RevenueHookUp-B3ytRhSE.js";import Oe from"./JZRevenueHookUp-KzTs2WN1.js";import{D as We,I as Ve}from"./userManage-E__vPxsL.js";import{P as Ge}from"./dma-SMxrzG7b.js";/* empty css                                                                     */import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./SideDrawer-CBntChyn.js";import"./hookupDevice-Bcbk7s68.js";import"./Search-NSrhrIa_.js";import"./MoreDetail_LLBXX-CQzT639E.js";import"./options-D6DdN2k5.js";import"./MoreDetail_HBXX-Br2C01p3.js";import"./MoreDetail_GWXX-CX2okXzV.js";import"./MoreDetail_LDJL-Bsom-Xka.js";import"./MoreDetail_WHJL-B8PB2Rg1.js";import"./MoreDetail_XHSXX-DAHg_s_1.js";import"./MoreDetail_FMXX-DC6qNS02.js";import"./MoreDetail_EGBF-Akh7gYtD.js";import"./printUtils-C-AxhDcd.js";const Je={class:"import-hookup-revenue"},qe={class:"btns"},ze={class:"text"},$e={class:"curpartition"},Qe=ge({__name:"index",setup(Ze){const _=h(),j=h(),H=h(),D=b({drawFrom:""}),s=Ue(),n=b({nodeOperations:[{icon:"ep:delete",color:"#ff0000",click:e=>{s.Delete([e.id]).then(()=>{n.data=s.Tree.value,s.refreshPartitions(r.view),n.currentProject=void 0})}}],extraFilters:[{type:"btn-group",btns:[{perm:!0,text:"新建分区",type:"success",click:()=>oe()},{perm:!0,text:"挂接",items:[{styles:{width:"120px"},perm:!0,text:"设备挂接",click:()=>L("device")},{styles:{width:"120px"},perm:!0,text:"大用户挂接",click:()=>L("user")},{hide:window.SITE_CONFIG.SITENAME==="jingzhou",styles:{width:"120px"},perm:!0,text:"营收挂接",click:()=>{L("revenue")}},{hide:window.SITE_CONFIG.SITENAME!=="jingzhou",styles:{width:"120px"},perm:!0,text:"营收挂接",click:()=>{L("jzrevenue")}}]},{perm:!0,text:"更多",items:[{perm:!0,text:"导出shapefile",click:()=>Z()}]}]}],data:[],isFilterTree:!0,expandOnClickNode:!1,title:"分区信息维护",treeNodeHandleClick:e=>{var o;n.currentProject=e;const t=s.List.value.find(i=>i.id===e.id);t.geom&&s.extentToPartition(r.view,JSON.parse(t.geom)),(o=j.value)==null||o.toggleDrawer("btt",!0)}}),w=b({title:"新建分区",dialogWidth:"70%",labelWidth:120,labelPosition:"right",desTroyOnClose:!1,defaultValue:{borderColor:"rgba(245,108,108,1)",rangeColor:"rgba(245,108,108,0.4)",type:"1",isMachineMeter:!1,copyMeterType:"1",status:"2"},group:[{fieldset:{desc:"基本信息"},fields:[{lg:8,md:12,type:"select-tree",label:"上级分区",checkStrictly:!0,field:"pid",options:q(()=>s.Tree.value)},{md:12,type:"input",label:"分区名称",field:"name",rules:[{required:!0,message:"请输入分区名称"}]},{lg:8,md:12,type:"select",label:"分区类型",field:"type",clearable:!1,options:xe},{lg:8,md:12,type:"input-number",label:"分区排序",field:"orderNum",rules:[{required:!0,message:"请输入分区排序"}]},{handleHidden:(e,t,o)=>{o.hidden=e.type==="1"},lg:8,md:12,type:"select",label:"分区状态",field:"status",clearable:!1,options:Fe},{lg:8,md:12,type:"select",label:"分区抄表类型",field:"copyMeterType",clearable:!1,options:Se},{lg:8,md:12,type:"select",label:"是否有机械表",field:"isMachineMeter",clearable:!1,options:Le},{lg:8,md:12,type:"select",label:"小区采集频率",field:"collectRate",options:[{label:"5",value:5},{label:"15",value:15},{label:"30",value:30},{label:"60",value:60}],rules:[{required:!0,message:"请选择采集频率"}]},{lg:8,md:12,type:"input",label:"负责人",field:"director"},{type:"input",label:"分区范围",field:"range"}]},{fieldset:{desc:"分区绘制"},fields:[{lg:8,md:12,label:"分区绘制",type:"btn-group",btns:[{perm:!0,text:"点击开始绘制",click:()=>ae()}]},{lg:8,md:12,type:"color-picker",label:"边框颜色",field:"borderColor",input:!0},{lg:8,md:12,type:"color-picker",label:"区域颜色",field:"rangeColor",input:!0},{lg:8,md:12,type:"input-number",label:"供水面积(k㎡)",field:"supplyWaterArea",appendBtns:[{perm:!0,text:"计算",click:()=>O()}]},{lg:8,md:12,type:"input-number",label:"主管线长度(km)",field:"mainLineLength",appendBtns:[{perm:!0,text:"计算",click:()=>W()}]},{type:"textarea",label:"备注",field:"remark"}]}],submit:e=>{z("确定提交？","提示信息").then(async()=>{var t,o;try{w.submitting=!0;const i={...e,geom:(t=r.graphic)!=null&&t.geometry?JSON.stringify(r.graphic.geometry.rings):void 0};(await Ge(i)).data&&(k(),u.success("操作成功"),U(),s.refreshPartitions(r.view),(o=p.value)==null||o.closeDialog())}catch(i){console.log(i),u.error("操作失败")}w.submitting=!1}).catch(()=>{})}}),p=h(),Z=()=>{u.info("功能开发中...")},y=h(""),S=h(),K=b({title:q(()=>y.value==="device"?"设备挂接":y.value==="revenue"?"营收手动挂接":y.value==="user"?"大用户挂接":""),dialogWidth:"80%",group:[],cancel:!1}),A=h(),X=b({title:"营收挂接",dialogWidth:450,group:[{fields:[{type:"btn-group",btns:[{perm:!0,text:"手动挂接",click:()=>Y()},{perm:!0,text:"批量导入",click:()=>ee()}]}]}],cancel:!1}),Y=()=>{var e;(e=S.value)==null||e.openDialog()},ee=()=>{var e;(e=R.value)==null||e.openDialog()},R=h(),te=b({title:"批量导入",dialogWidth:450,group:[],cancel:!1}),re=e=>{z("将上传数据并进行解析，确定上传？","提示信息").then(async()=>{try{const t=await Ve(e);t.data.code===200?u.success("导入成功"):u.error(t.data.message)}catch{u.error("上传失败")}})},L=e=>{var t,o,i;if(e!==""){if(!n.currentProject){u.warning("请先选择一个分区");return}y.value=e,e==="revenue"?(t=A.value)==null||t.openDialog():e==="jzrevenue"?(o=S.value)==null||o.openDialog():(i=S.value)==null||i.openDialog()}},oe=()=>{var e,t,o;D.drawFrom="add",w.defaultValue={...w.defaultValue||{},pid:(e=n.currentProject)==null?void 0:e.id},(t=p.value)==null||t.openDialog(),(o=p.value)==null||o.resetForm()},ie=()=>{var e;(e=H.value)==null||e.openDialog()},U=()=>{s.getTree().then(()=>{n.data=s.Tree.value})},r={},ae=()=>{var e,t,o;if(D.drawFrom="add",(e=p.value)==null||e.closeDialog(),k(),r.sketch){const i=(o=(t=p.value)==null?void 0:t.refForm)==null?void 0:o.dataForm;r.sketch.polygonSymbol=B("polygon",{color:i.rangeColor,outlineColor:i.borderColor}),r.sketch.create("polygon")}},ne=()=>{var e,t,o;if(D.drawFrom="detail",(e=p.value)==null||e.closeDialog(),k(),r.sketch){const i=((o=(t=_.value)==null?void 0:t.refForm)==null?void 0:o.dataForm)||{};r.sketch.polygonSymbol=B("polygon",{color:i.rangeColor,outlineColor:i.borderColor}),r.sketch.create("polygon")}},le=e=>{var t,o,i,c;if(D.drawFrom="detail",(t=p.value)==null||t.closeDialog(),k(),r.sketch){const l=(i=(o=_.value)==null?void 0:o.refForm)==null?void 0:i.dataForm;r.sketch.polygonSymbol=B("polygon",{color:l==null?void 0:l.rangeColor,outlineColor:l==null?void 0:l.borderColor});const m=s.getPartitionGraphic(e);if(!m){u.error("当前分区暂无区域信息");return}r.graphic=m.clone(),(c=r.graphicsLayer)==null||c.add(r.graphic),r.sketch.create("polygon"),r.sketch.update(r.graphic)}},k=()=>{var e;(e=r.graphicsLayer)==null||e.removeAll(),r.graphic=void 0},E=e=>{var t;e.state==="complete"&&(r.graphic=e.graphics[0],D.drawFrom==="add"&&((t=p.value)==null||t.openDialog()),O(),W())},O=()=>{var c,l,m,f,C,g;if(!r.graphic){u.warning("请先绘制图形");return}const e=r.graphic.geometry.rings,t=Me(e[0],"square-kilometers",(c=r.view)==null?void 0:c.spatialReference).toFixed(6),o=(m=(l=p.value)==null?void 0:l.refForm)==null?void 0:m.dataForm;o&&(o.supplyWaterArea=t);const i=(C=(f=_.value)==null?void 0:f.refForm)==null?void 0:C.dataForm;i&&(i.supplyWaterArea=t,i.geom=(g=r.graphic)!=null&&g.geometry?JSON.stringify(r.graphic.geometry.rings):void 0)},W=async()=>{var o,i,c,l,m,f,C;if(!r.graphic){u.warning("请先绘制图形");return}const e=await Ie(r.view),t=e==null?void 0:e.map(g=>g.id);try{const g=await Pe({usertoken:ye().gToken,layerids:JSON.stringify(t),group_fields:JSON.stringify(["DIAMETER"]),statistic_field:Q.ShapeLen,statistic_type:"2",where:"1=1",geometry:r.graphic.geometry,f:"pjson"}),P=(i=(o=p.value)==null?void 0:o.refForm)==null?void 0:i.dataForm,T=(l=(c=_.value)==null?void 0:c.refForm)==null?void 0:l.dataForm;if(g.data.code===1e4){const ue=((C=(f=(m=g.data)==null?void 0:m.result)==null?void 0:f.rows[0])==null?void 0:C.rows)||[];let V=0;ue.map(de=>{const J=Number(de[Q.ShapeLen]);isNaN(J)||(V+=J)});const G=(V/1e3).toFixed(5);P&&(P.mainLineLength=G),T&&(T.mainLineLength=G)}else P&&(P.mainLineLength=0),T&&(T.mainLineLength=0)}catch(g){console.log(g),u.error("计算失败")}},se=()=>{k(),U(),s.refreshPartitions(r.view)},{initSketch:pe,destroySketch:ce}=je(),me=async e=>{var t;r.view=e,U(),await s.refreshPartitions(r.view),r.graphicsLayer=Ne(r.view,{id:"partition-new-layer",title:"绘制新分区"}),r.sketch=pe(r.view,r.graphicsLayer,{updateCallBack:E,createCallBack:E}),(t=j.value)==null||t.toggleDrawer("rtl",!0)};return he(()=>{ce()}),(e,t)=>{var m;const o=Ce,i=we,c=_e,l=fe;return x(),ve(be,null,[d(Te,{ref_key:"refDrawerBox",ref:j,"right-drawer":!0,"bottom-drawer":!0,"bottom-drawer-bar-position":"left","bottom-drawer-title":((m=a(n).currentProject)==null?void 0:m.label)||"请选择分区"},{right:v(()=>[d(o,{"tree-data":a(n)},null,8,["tree-data"])]),bottom:v(()=>[d(Re,{ref_key:"refDetail",ref:_,"tree-data":a(n).data,"current-tree-node":a(n).currentProject,onEdit:le,onDraw:ne,onSuccess:se,onMore:ie},null,8,["tree-data","current-tree-node"])]),default:v(()=>[d(i,{ref:"refArcLayout",onMapLoaded:me},null,512)]),_:1},8,["bottom-drawer-title"]),d(N,{ref_key:"refDialogForm",ref:p,config:a(w),onClose:t[0]||(t[0]=f=>k())},null,8,["config"]),d(N,{ref_key:"refDialogConfig_HookUp",ref:S,config:a(K)},{default:v(()=>[a(y)==="device"?(x(),M(He,{key:0,tree:a(n).data,"current-tree-node":a(n).currentProject},null,8,["tree","current-tree-node"])):I("",!0),a(y)==="user"?(x(),M(Be,{key:1,tree:a(n).data,"current-tree-node":a(n).currentProject},null,8,["tree","current-tree-node"])):I("",!0),a(y)==="revenue"?(x(),M(Ee,{key:2,"current-tree-node":a(n).currentProject},null,8,["current-tree-node"])):I("",!0),a(y)==="jzrevenue"?(x(),M(Oe,{key:3,"current-tree-node":a(n).currentProject},null,8,["current-tree-node"])):I("",!0)]),_:1},8,["config"]),d(N,{ref_key:"refDialogConfig_RevenueType",ref:A,config:a(X)},null,8,["config"]),d(N,{ref_key:"refDialogConfig_ImportHookUpRevenue",ref:R,config:a(te)},{default:v(()=>{var f;return[F("div",Je,[F("div",qe,[d(c,{type:"success",onClick:a(We)},{default:v(()=>t[1]||(t[1]=[$(" 下载模板 ")])),_:1},8,["onClick"]),d(l,{config:{perm:!0,text:"追加挂接",click:re}},{default:v(()=>t[2]||(t[2]=[$(" 追加挂接 ")])),_:1},8,["config"])]),F("div",ze,[F("div",$e," 当前分区ID: "+ke((f=a(n).currentProject)==null?void 0:f.value),1),t[3]||(t[3]=F("div",{class:"warning"},"注： 选择导入文件时，请选择类型为xlsx",-1))])])]}),_:1},8,["config"]),d(Ae,{ref_key:"refMoreDetail",ref:H,partition:a(n).currentProject},null,8,["partition"])],64)}}}),io=De(Qe,[["__scopeId","data-v-869b11ec"]]);export{io as default};
