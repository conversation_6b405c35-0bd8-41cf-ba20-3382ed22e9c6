package org.thingsboard.server.dao.report;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.ReportTableDTO;
import org.thingsboard.server.dao.model.request.ReportRequest;
import org.thingsboard.server.dao.model.sql.report.Report;
import org.thingsboard.server.dao.model.sql.report.ReportQuery;
import org.thingsboard.server.dao.model.sql.report.ReportTable;
import org.thingsboard.server.dao.sql.report.*;

import java.util.*;

/**
 *
 */
@Service
public class ReportServiceImpl implements ReportService {

    @Autowired
    private ReportMapper reportMapper;


    @Autowired
    private ReportQueryMapper reportQueryMapper;

    @Autowired
    private ReportTableMapper reportTableMapper;

    @Override
    public Report save(Report report) {
        if (StringUtils.isBlank(report.getId())) {
            report.setCreateTime(new Date());
            report.setStatus("1");
            reportMapper.insert(report);

        } else {
            reportMapper.updateById(report);
        }
        return report;
    }


    @Override
    public PageData<Report> getList(ReportRequest request) {
        IPage<Report> page = new Page<>(request.getPage(), request.getSize());
        IPage<Report> result = reportMapper.getList(page, request);
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void softDelete(List<String> ids) {
        reportMapper.changeStatus(ids, "2");
    }


    @Override
    @Transactional
    public void delete(List<String> ids) {
        reportMapper.delete(ids);
    }

    @Override
    @Transactional
    public ReportTableDTO saveDetail(ReportTableDTO reportTableDTO) {

        JSONObject contentObj = reportTableDTO.getContentObj();
        ReportTable reportTable = new ReportTable();
        BeanUtils.copyProperties(reportTableDTO, reportTable);

        // 删除
        Map deleteMap = new HashMap();
        deleteMap.put("pid", reportTable.getId());
        reportTableMapper.deleteByMap(deleteMap);
        reportTable.setCreateTime(new Date());
        if (contentObj != null) {
            reportTable.setContent(contentObj.toJSONString());
        }
        if (StringUtils.isNotBlank(reportTable.getId())) {
            reportTableMapper.updateById(reportTable);
        } else {
            reportTableMapper.insert(reportTable);
        }

        // 删除旧查询条件
        deleteMap = new HashMap();
        deleteMap.put("report_id", reportTableDTO.getPid());
        reportQueryMapper.deleteByMap(deleteMap);

        // 查询条件
        JSONArray searchArr = contentObj.getJSONArray("search");
        List<ReportQuery> reportQueryCList = new ArrayList<>();
        if (searchArr != null && searchArr.size() > 0) {
            searchArr.forEach(a -> {
                JSONObject a1 = JSONObject.parseObject(JSONObject.toJSONString(a));
                // 数据库存起来
                if ("3".equals(a1.getString("dataType"))) {
                    ReportQuery reportQueryC = new ReportQuery();
                    reportQueryC.setPid(reportTable.getId());
                    reportQueryC.setReportId(reportTable.getPid());
                    reportQueryC.setDatabaseId(a1.getString("databaseId"));
                    reportQueryC.setContent(a1.getString("value"));
                    reportQueryC.setId(a1.getString("id"));
                    reportQueryC.setCreateTime(new Date());
                    reportQueryC.setTenantId(reportTable.getTenantId());

                    reportQueryCList.add(reportQueryC);
                }
            });
        }

        // 表格数据
        JSONObject table = contentObj.getJSONObject("table");
        // 数据库存起来
        if (table != null) {
            JSONObject tableSearch = table.getJSONObject("tableData");
            if (tableSearch != null && "3".equals(tableSearch.getString("dataType"))) {
                ReportQuery reportQueryC = new ReportQuery();
                reportQueryC.setPid(reportTable.getId());
                reportQueryC.setReportId(reportTable.getPid());
                reportQueryC.setDatabaseId(tableSearch.getString("databaseId"));
                reportQueryC.setContent(tableSearch.getString("value"));
                reportQueryC.setId(table.getString("id"));
                reportQueryC.setCreateTime(new Date());
                reportQueryC.setTenantId(reportTable.getTenantId());
                reportQueryCList.add(reportQueryC);
            }
        }
        if (reportQueryCList.size() > 0) {
            reportQueryMapper.batchInsert(reportQueryCList);
        }

        return reportTableDTO;
    }

    @Override
    public void recover(List<String> idList) {
        reportMapper.changeStatus(idList, "1");
    }

    @Override
    public ReportTableDTO getDetail(String pid) {
        ReportTable reportTable = reportTableMapper.selectByPid(pid);
        ReportTableDTO reportTableDTO = new ReportTableDTO();
        if (reportTable == null) {
            reportTableDTO.setPid(pid);
            return reportTableDTO;
        }
        BeanUtils.copyProperties(reportTable, reportTableDTO);

        if (StringUtils.isNotBlank(reportTableDTO.getContent())) {
            try {
                reportTableDTO.setContentObj(JSONObject.parseObject(reportTableDTO.getContent()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return reportTableDTO;
    }

}
