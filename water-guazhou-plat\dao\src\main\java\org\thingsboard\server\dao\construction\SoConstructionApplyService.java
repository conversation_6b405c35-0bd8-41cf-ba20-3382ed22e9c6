package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApply;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyContainer;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplySaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;

import java.util.List;

public interface SoConstructionApplyService {
    /**
     * 分页条件查询工程实施
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoConstructionApplyContainer> findAllConditional(SoConstructionApplyPageRequest request);

    /**
     * 保存工程实施
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoConstructionApply save(SoConstructionApplySaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoConstructionApply entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 编号是否已存在
     *
     * @param code     编号
     * @param tenantId 客户id
     * @param id       自身id（更新时不为null）
     * @return 是否已存在
     */
    boolean isCodeExists(String code, String tenantId, String id);

    /**
     * 完成
     *
     * @param id       唯一标识
     * @param userId   请求发起人id
     * @param tenantId 客户id
     * @return 是否成功
     */
    boolean complete(String id, String userId, String tenantId);

    /**
     * 分页条件查询设备项
     *
     * @param request 分页请求
     * @return 设备项
     */
    IPage<SoDeviceItem> getDevices(SoDeviceItemPageRequest request);

    /**
     * 保存设备项
     *
     * @param request 明细
     * @return 保存好的设备项
     */
    List<SoDeviceItem> saveDevice(List<SoDeviceItemSaveRequest> request);

    /**
     * 是否已完成
     *
     * @param id 唯一标识
     * @return 是否已完成
     */
    boolean isComplete(String id);

    /**
     * 是否已完成
     *
     * @param constructionCode 编号
     * @param tenantId         客户id
     * @return 是否已完成
     */
    boolean isComplete(String constructionCode, String tenantId);

}
