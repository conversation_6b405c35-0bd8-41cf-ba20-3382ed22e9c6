package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.MaintenanceJobEntity;
import org.thingsboard.server.dao.model.sql.RepairJobEntity;

import java.util.List;

public interface MaintenanceJobRepository extends JpaRepository<MaintenanceJobEntity, String> {

    @Query("SELECT DISTINCT mj FROM MaintenanceJobEntity mj , MaintenanceJobCEntity mjc " +
            "WHERE mj.id = mjc.mainId AND mj.tenantId = ?2 AND mjc.deviceId LIKE %?3% AND mj.name LIKE %?1%")
    Page<MaintenanceJobEntity> findList(String name, String tenantId, String deviceId, Pageable pageable);

    List<MaintenanceJobEntity> findByType(String type);

}
