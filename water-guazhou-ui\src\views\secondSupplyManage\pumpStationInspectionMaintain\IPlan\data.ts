import { dayjs } from 'element-plus'
import { getUserList } from '@/api/user'
// 周期任务表单
export const cycleFormFields: any = [{
  xs: 12,
  type: 'input',
  label: '计划名称',
  field: 'name',
  rules: [{ required: true, message: '请输入计划名称' }]
}, {
  xs: 12,
  type: 'select',
  label: '巡检模板',
  field: 'templateId',
  rules: [{ required: true, message: '请选择模板' }]
}, {
  xs: 12,
  type: 'date',
  label: '开始时间',
  width: '230px',
  field: 'startDate',
  rules: [{ required: true, message: '请输入计划开始时间' }]
}, {
  xs: 11,
  type: 'date',
  readonly: true,
  label: '结束时间',
  field: 'endDate',
  rules: [{ required: true, message: '请输入计划开始时间' }]
}, {
  xs: 8,
  type: 'cascader',
  label: '巡检部门',
  multiple: false,
  field: 'departmentId',
  rules: [{ required: true, message: '请选择巡检部门' }],
  onChange: async (val: any) => {
    const res = await getUserList({
      pid: val.pop(),
      status: 1,
      page: 1,
      size: 999
    })
    console.log(res)
    const executionUserList = fixedFormFields.find(filed => filed.filed === 'executionUserId')
    executionUserList.options = res.data?.data?.data
  }
}, {
  xs: 8,
  type: 'select',
  label: '巡检人员',
  field: 'executionUserId',
  rules: [{ required: true, message: '请选择巡检人员' }]
}, {
  xs: 8,
  type: 'select',
  label: '审核人员',
  field: 'auditUserId',
  rules: [{ required: true, message: '请选择审核人员' }]
}, {
  xs: 8,
  type: 'number',
  label: '消耗天数',
  field: 'executionDays',
  rules: [{ required: true, message: '请输入执行天数' }]
}, {
  xs: 8,
  type: 'number',
  label: '间隔天数',
  field: 'intervalDays',
  rules: [{ required: true, message: '请输入间隔时间' }]
}, {
  xs: 8,
  type: 'number',
  label: '执行次数',
  field: 'executionNum',
  rules: [{ required: true, message: '请输入执行次数' }]
},
{
  type: 'checkbox',
  field: '',
  label: '泵房列表',
  rules: [{ required: true }]
},
{
  type: 'checkbox',
  field: 'bengfang',
  label: '',
  rules: [{ required: true, message: '请选择泵房列表' }],
  noBorder: false,
  colStyles: {
    border: '1px solid rgb(94 97 111)',
    height: '200px',
    padding: '10px 10px'
  },
  options: [
    { label: '周期性任务', value: 'cycle' },
    { label: '固定日', value: 'fixed' },
    { label: '固定日期任务', value: 'fixed' },
    { label: '固定日', value: 'fixed' },
    { label: '固定日期任务', value: 'fixed' },
    { label: '固定日期任务', value: 'fixed' },
    { label: '固定日', value: 'fixed' },
    { label: '固定日期任务', value: 'fixed' },
    { label: '固定日期任务', value: 'fixed' }
  ]
},
{
  xs: 100,
  type: 'textarea',
  minRow: 5,
  label: 'remark',
  field: 'key12',
  colStyles: {
    marginTop: '20px'
  }
}]

// 固定任务表单
export const fixedFormFields: any = [
  {
    xs: 12,
    type: 'input',
    label: '计划名称',
    field: 'name',
    rules: [{ required: true, message: '请输入计划名称' }]
  }, {
    xs: 12,
    type: 'select',
    label: '巡检模板',
    field: 'templateId',
    rules: [{ required: true, message: '请选择模板' }]
  },
  {
    xs: 11,
    type: 'monthrange',
    label: '计划月份',
    field: 'key12',
    rules: [{ required: true, message: '请输入计划开始时间' }]
  },
  {
    xs: 11,
    type: 'date',
    label: '选择日期',
    field: 'fixedDate',
    rules: [{ required: true, message: '请选择任务日期' }]
  }, {
    xs: 8,
    type: 'cascader',
    multiple: false,
    label: '巡检部门',
    field: 'departmentId',
    rules: [{ required: true, message: '请选择巡检部门' }],
    onChange: async (val: any) => {
      const res = await getUserList({
        pid: val.pop(),
        status: 1,
        page: 1,
        size: 999
      })
      console.log(res)
      const executionUserList = fixedFormFields.find(filed => filed.filed === 'executionUserId')
      executionUserList.options = res.data?.data?.data
    }
  }, {
    xs: 8,
    type: 'select',
    label: '巡检人员',
    field: 'executionUserId',
    rules: [{ required: true, message: '请选择巡检人员' }]
  }, {
    xs: 8,
    type: 'select',
    label: '审核人员',
    field: 'auditUserId',
    rules: [{ required: true, message: '请选择审核人员' }]
  },
  {
    type: 'checkbox',
    field: '',
    label: '泵房列表',
    rules: [{ required: true }]
  },
  {
    type: 'checkbox',
    field: 'bengfang',
    label: '',
    rules: [{ required: true, message: '请选择泵房列表' }],
    noBorder: false,
    colStyles: {
      border: '1px solid rgb(94 97 111)',
      height: '200px',
      padding: '10px 10px'
    },
    options: [
      { label: '周期性任务', value: 'cycle' },
      { label: '固定日', value: 'fixed' },
      { label: '固定日期任务', value: 'fixed' },
      { label: '固定日', value: 'fixed' },
      { label: '固定日期任务', value: 'fixed' },
      { label: '固定日期任务', value: 'fixed' },
      { label: '固定日', value: 'fixed' },
      { label: '固定日期任务', value: 'fixed' },
      { label: '固定日期任务', value: 'fixed' }
    ]
  },
  {
    xs: 100,
    type: 'textarea',
    minRow: 5,
    label: '备注',
    field: 'remark',
    colStyles: {
      marginTop: '20px'
    }
  }]

export const getFormFields = (formType: string) => {
  const options: any = [{
    xs: 12,
    type: 'input',
    label: '计划名称',
    field: 'name',
    rules: [{ required: true, message: '请输入计划名称' }]
  }, {
    xs: 12,
    type: 'select',
    label: '巡检模板',
    field: 'templateId',
    rules: [{ required: true, message: '请选择模板' }]
  },
  {
    xs: 11,
    type: 'monthrange',
    label: '计划月份',
    field: 'key12',
    handleHidden: () => {
      formType === '周期性任务'
    },
    rules: [{ required: true, message: '请输入计划开始时间' }]
  },
  {
    xs: 11,
    type: 'date',
    label: '选择日期',
    field: 'fixedDate',
    handleHidden: () => {
      formType === '周期性任务'
    },
    rules: [{ required: true, message: '请选择任务日期' }]
  }, {
    xs: 12,
    type: 'date',
    label: '开始时间',
    width: '230px',
    field: 'startDate',
    textFormat: 'YYYY-MM-DD',
    formatter: (row: any, value: any) => { return dayjs(value).format('YYYY-MM-DD') },
    handleHidden: () => {
      return formType === '固定日期任务'
    },
    rules: [{ required: true, message: '请输入计划开始时间' }]
  }, {
    xs: 11,
    type: 'date',
    readonly: true,
    label: '结束时间',
    field: 'endDate',
    formatter: (row: any, value: any) => { return dayjs(value).format('YYYY-MM-DD') },
    handleHidden: () => {
      return formType === '固定日期任务'
    },
    rules: [{ required: true, message: '请输入计划开始时间' }]
  }, {
    xs: 8,
    type: 'cascader',
    label: '巡检部门',
    multiple: false,
    field: 'departmentId',
    rules: [{ required: true, message: '请选择巡检部门' }],
    onChange: async (val: any) => {
      const res = await getUserList({
        pid: val.pop(),
        status: 1,
        page: 1,
        size: 999
      })
      const executionUser = options.find(field => field.field === 'executionUserId')
      console.log(executionUser)
      executionUser.options = res.data?.data?.data
    }
  }, {
    xs: 8,
    type: 'select',
    label: '巡检人员',
    field: 'executionUserId',
    rules: [{ required: true, message: '请选择巡检人员' }]
  }, {
    xs: 8,
    type: 'select',
    label: '审核人员',
    field: 'auditUserId',
    rules: [{ required: true, message: '请选择审核人员' }]
  }, {
    xs: 8,
    type: 'number',
    label: '消耗天数',
    field: 'executionDays',
    handleHidden: () => {
      console.log(formType)
      return formType === '固定日期任务'
    },
    rules: [{ required: true, message: '请输入执行天数' }]
  }, {
    xs: 8,
    type: 'number',
    label: '间隔天数',
    field: 'intervalDays',
    handleHidden: () => {
      return formType === '固定日期任务'
    },
    rules: [{ required: true, message: '请输入间隔时间' }]
  }, {
    xs: 8,
    type: 'number',
    label: '执行次数',
    field: 'executionNum',
    handleHidden: () => {
      return formType === '固定日期任务'
    },
    rules: [{ required: true, message: '请输入执行次数' }]
  },
  {
    type: 'checkbox',
    field: '',
    label: '泵房列表',
    rules: [{ required: true }]
  },
  {
    type: 'checkbox',
    field: 'bengfang',
    label: '',
    rules: [{ required: true, message: '请选择泵房列表' }],
    noBorder: false,
    colStyles: {
      border: '1px solid rgb(94 97 111)',
      height: '200px',
      padding: '10px 10px'
    },
    options: [
      { label: '周期性任务', value: 'cycle' },
      { label: '固定日', value: 'fixed' },
      { label: '固定日期任务', value: 'fixed' },
      { label: '固定日', value: 'fixed' },
      { label: '固定日期任务', value: 'fixed' },
      { label: '固定日期任务', value: 'fixed' },
      { label: '固定日', value: 'fixed' },
      { label: '固定日期任务', value: 'fixed' },
      { label: '固定日期任务', value: 'fixed' }
    ]
  },
  {
    xs: 100,
    type: 'textarea',
    minRow: 5,
    label: 'remark',
    field: 'key12',
    colStyles: {
      marginTop: '20px'
    }
  }]
  return options
}
