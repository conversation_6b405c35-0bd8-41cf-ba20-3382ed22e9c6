package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 压力合格率报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-25
 */
@Data
@NoArgsConstructor
public class PressurePassRateDTO {

    private String id;

    private String name;

    private BigDecimal maxValue;

    private BigDecimal minValue;

    private Integer num = 0;

    private Integer passNum = 0;

    private BigDecimal passRate = BigDecimal.ZERO;

}
