package org.thingsboard.server.dao.model.sql.assay;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * 水质化验-化验项内容
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSAY_ITEM_TABLE)
@TableName(ModelConstants.ASSAY_ITEM_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class AssayItem {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ASSAY_ITEM_TITLE)
    private String title;

    @Column(name = ModelConstants.ASSAY_ITEM_SETTING_ID)
    private String settingId;

    @TableField(exist = false)
    private String settingName;

    @Column(name = ModelConstants.ASSAY_ITEM_ORDER_NUMBER)
    private Integer orderNumber;

    @Column(name = ModelConstants.ASSAY_ITEM_SERIAL_CODE)
    private String serialCode;

    @Column(name = ModelConstants.ASSAY_ITEM_TARGET)
    private String target;

    @Column(name = ModelConstants.ASSAY_ITEM_UNIT)
    private String unit;

    @Column(name = ModelConstants.ASSAY_ITEM_REMARK)
    private String remark;

    @Column(name = ModelConstants.ASSAY_ITEM_CREATE_USER)
    private String createUser;

    @TableField(exist = false)
    private String createUserName;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.UPDATE_TIME)
    private Date updateTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
