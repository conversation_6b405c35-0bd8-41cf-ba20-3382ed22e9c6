import { queryLayerClassName } from '@/api/mapservice';
import { GetFieldConfig } from '@/api/mapservice/fieldconfig';
import { PostGisOperateLog } from '@/api/system/gisSetting';
import { useSketch } from '@/hooks/arcgis';
import { useUserStore } from '@/store';
import { formatDate } from '@/utils/DateFormatter';
import { formatterDate, formatterDateTime } from '@/utils/GlobalHelper';
import {
  applyEdits,
  calcLength,
  createEllipse,
  createGeometry,
  createGraphic,
  createPolygon,
  createPolyline,
  createRectGraphic,
  excuteQuery,
  generate4548Graphic,
  getGraphicLayer,
  getNeerestPoint,
  getSubLayerIds,
  initDrawer,
  initQueryParams,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper';

// 导入GeoServer工具
import { QueryByPolygon } from '@/utils/geoserver/wfsUtils';
import { updateFeatureAttributes } from '@/utils/geoserver/wfsTransactionUtils';

// 导入ArcGIS类型
import Point from '@arcgis/core/geometry/Point';
import Polyline from '@arcgis/core/geometry/Polyline';
import Polygon from '@arcgis/core/geometry/Polygon';
import Graphic from '@arcgis/core/Graphic';

/**
 * 将GeoJSON格式的几何对象转换为ArcGIS几何类实例
 * @param geoJson GeoJSON格式的几何对象
 * @returns ArcGIS几何类实例
 */
const convertGeoJSONToArcGIS = (geoJson: any) => {
  if (!geoJson) return null;

  switch (geoJson.type) {
    case 'Point':
      return new Point({
        x: geoJson.coordinates[0],
        y: geoJson.coordinates[1],
        spatialReference: { wkid: 3857 }
      });
    case 'LineString':
      return new Polyline({
        paths: [geoJson.coordinates],
        spatialReference: { wkid: 3857 }
      });
    case 'MultiLineString':
      return new Polyline({
        paths: geoJson.coordinates,
        spatialReference: { wkid: 3857 }
      });
    case 'Polygon':
      return new Polygon({
        rings: geoJson.coordinates,
        spatialReference: { wkid: 3857 }
      });
    case 'MultiPolygon':
      return new Polygon({
        rings: geoJson.coordinates[0],
        spatialReference: { wkid: 3857 }
      });
    default:
      console.error('不支持的几何类型:', geoJson.type);
      return null;
  }
};
import { SLConfirm, SLMessage } from '@/utils/Message';
import {
  EGigLogFunc,
  EGisLogApp,
  EGisLogOperateType
} from '@/views/arcMap/config';
import FeatureLayer from '@arcgis/core/layers/FeatureLayer';
import { initPipeEditForm } from '../config';
import * as turf from '@turf/turf';

export const useLayerSelector = (
  formIns: any,
  checkChangeCallback: (data, isChecked) => any
) => {
  const refForm = formIns;
  const toggleLoading = (flag: boolean) =>
    ((FormConfig.value.group[0].fields[0] as IFormTree).loading = flag);
  const layerIds = ref<number[]>([]);
  const layerInfos = ref<ILayerInfo[]>([]);
  const curLayerInfo = ref<Record<string, any>>();
  const FormConfig = ref<IFormConfig>({
    labelPosition: 'top',
    group: [
      {
        id: 'layer',
        fieldset: {
          desc: '选择图层'
        },
        fields: [
          {
            loading: false,
            type: 'tree',
            checkStrictly: true,
            showCheckbox: true,
            field: 'layerid',
            nodeKey: 'value',
            options: [],
            handleCheckChange: (data, isChecked) => {
              if (isChecked) {
                refForm.value &&
                  (refForm.value.dataForm.layerid = [data.value]);

                curLayerInfo.value = data;
                checkChangeCallback?.(data, isChecked);
              }
            }
          }
        ]
      }
    ]
  });

  const getLayerInfo = (view?: __esri.MapView) => {
    if (!view) return;
    if((window as any).GIS_SERVER_SWITCH){
      const field = FormConfig.value.group[0].fields[0] as IFormTree;
      const layerInfo = view?.layerViews.items[0].layer.sublayers;
      let layers = layerInfo.items.map(item => {
        return {
          label: item.name,
          value: item.name,
          layername: item.name,
          type: item.type,
          spatialReferences: item.spatialReferences
        }
      });
      field.options = layers;// [{ label: '管线类', value: -2, children: layers }]
      // refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
    }else{
      layerIds.value = getSubLayerIds(view);
      queryLayerClassName(layerIds.value).then((layerInfo) => {
        layerInfos.value = layerInfo.data?.result?.rows || [];
        const field = FormConfig.value.group[0].fields[0] as IFormTree;
        const points = layerInfos.value
          .filter((item) => item.geometrytype === 'esriGeometryPoint')
          .map((item) => {
            return {
              label: item.layername,
              value: item.layerid,
              data: item
            };
          });
        const lines = layerInfos.value
          .filter((item) => item.geometrytype === 'esriGeometryPolyline')
          .map((item) => {
            return {
              label: item.layername,
              value: item.layerid,
              data: item
            };
          });
        field &&
          (field.options = [
            { label: '管线类', value: -2, children: lines, disabled: true },
            { label: '管点类', value: -1, children: points, disabled: true }
          ]);
        if (!refForm.value) return;
        refForm.value.dataForm.layerid = layerIds.value.slice(0, 1);
        curLayerInfo.value = layerInfos.value.find(
          (item) => item.layerid === layerIds.value[0]
        );
      });
    }
  };

  return {
    toggleLoading,
    getLayerInfo,
    FormConfig,
    layerIds,
    layerInfos,
    curLayerInfo
  };
};
/**
 * 编辑表单
 * @param formIns 表单实例
 * @param treeNodeClick 表单上面的树的点击事件
 */
export const useEditForm = (formIns: any, treeNodeClick: (data) => any) => {
  const resetTreeData = (layername: string, graphics: __esri.Graphic[]) => {
    TreeData.value.options = [
      {
        label: layername,
        value: layername,
        type: 'layer',
        children: graphics.map((item) => {
          return {
            label: item.attributes.OBJECTID,
            value: item.attributes.OBJECTID,
            type: 'graphic'
          };
        })
      }
    ];
  };
  const TreeData = ref<IFormTree>({
    type: 'tree',
    options: [],
    nodeClick: treeNodeClick
  });
  const refForm = formIns;
  const FormConfig = ref<IFormConfig>({
    labelWidth: 130,
    group: [
      {
        fields: []
      }
    ],
    labelPosition: 'right',
    submit: async (
      params: any,
      extraParams: {
        layerid: number;
        graphic: __esri.Graphic;
        layername: string;
        successCallback: () => void;
      }
    ) => {
      // 创建更新后的图形对象
      const graphic = createGraphic({
        geometry: extraParams.graphic.geometry,
        attributes: {
          ...(extraParams.graphic.attributes || {}),
          ...params,
          UPDATEDBY: useUserStore().user?.name,
          UPDATEDDATE: moment().format(formatterDateTime)
        }
      });

      // 直接执行更新操作，不再显示确认对话框（已在 handleEditSubmit 中添加）
      FormConfig.value.submitting = true;

      try {
        // 检查是否使用 GeoServer 模式
        if ((window as any).GIS_SERVER_SWITCH) {
          console.log('使用 GeoServer WFS-T 模式进行属性更新');

          // 获取图层名称
          let layerName = '';
          if (typeof extraParams.layerid === 'string') {
            layerName = extraParams.layerid;
          } else {
            // 如果是数字ID，直接使用ID作为图层名称
            layerName = `${extraParams.layerid}`;
          }

          console.log('图层名称:', layerName);

          // 执行 WFS-T 更新操作
          const response = await updateFeatureAttributes(
            layerName,
            [graphic],
            graphic.attributes,
            false // 不更新几何信息，只更新属性
          );

          console.log('GeoServer更新响应:', response);

          // 判断是否成功
          if (!(response.data && response.data.includes('<wfs:totalUpdated>'))) {
            console.error('WFS-T 更新要素失败:', response);
            throw new Error('更新要素失败');
          }
        } else {
          // ArcGIS 模式
          console.log('使用 ArcGIS 模式进行属性更新');

          // 确保 layerid 是数字
          const numericLayerId = typeof extraParams.layerid === 'string' ?
            parseInt(extraParams.layerid) || 0 : extraParams.layerid;

          // 使用原始的 applyEdits 函数
          await applyEdits(numericLayerId, {
            updateFeatures: [graphic]
          });
        }

        SLMessage.success('操作成功');

        // 记录操作日志
        PostGisOperateLog({
          optionName: EGigLogFunc.GWBIANJI,
          type: EGisLogApp.BASICGIS,
          content: `${EGisLogOperateType.UPDATE}OBJECTID为${graphic?.attributes.OBJECTID}的${extraParams.layername}的属性信息`,
          optionType: EGisLogOperateType.UPDATE
        }).catch(() => {
          console.log('生成gis操作日志失败');
        });

        // 执行成功回调
        extraParams.successCallback();
      } catch (error) {
        console.error('更新操作失败:', error);
        SLMessage.error('操作失败');
      } finally {
        FormConfig.value.submitting = false;
      }
    }
  });

  const submitEdit = async (
    layerid: number | string,
    params: __esri.FeatureLayerBaseApplyEditsEdits,
    successCallback?: (result: __esri.EditsResult) => void,
    cancelCallBack?: () => void
  ) => {
    if (layerid === undefined) {
      SLMessage.warning('请选择编辑图层');
      return;
    }

    SLConfirm('应用到空间数据库？', '提示信息')
      .then(async () => {
        try {
          // 检查是否使用 GeoServer 模式
          if ((window as any).GIS_SERVER_SWITCH) {
            console.log('使用 GeoServer WFS-T 模式进行编辑操作');

            // 获取图层名称
            let layerName = '';
            if (typeof layerid === 'string') {
              layerName = layerid;
            } else {
              // 如果是数字ID，直接使用ID作为图层名称
              // 在GeoServer模式下，我们通常使用图层名称而不是ID
              layerName = `${layerid}`;
            }

            console.log('图层名称:', layerName);

            // 根据操作类型执行不同的 WFS-T 操作
            if (params.addFeatures && params.addFeatures.length > 0) {
              // 添加要素
              console.log('添加要素:', params.addFeatures);

              // 导入 WFS-T 工具函数
              const { addFeature } = await import('@/utils/geoserver/wfsTransactionUtils');

              // 逐个添加要素
              // 使用 any 类型绕过类型检查
              const results: any = {
                addFeatureResults: [],
                updateFeatureResults: [],
                deleteFeatureResults: [],
                addAttachmentResults: [],
                updateAttachmentResults: [],
                deleteAttachmentResults: []
              };

              for (const feature of params.addFeatures) {
                try {
                  // 使用 WFS-T 添加要素
                  const response = await addFeature(layerName, feature);
                  console.log('WFS-T 添加要素响应:', response);

                  // 解析XML响应以检查是否成功
                  const parser = new DOMParser();
                  const xmlDoc = parser.parseFromString(response.data, "text/xml");

                  // 获取插入的要素数量
                  const totalInsertedElements = xmlDoc.getElementsByTagName('wfs:totalInserted');
                  const totalInserted = totalInsertedElements.length > 0 ?
                    parseInt(totalInsertedElements[0].textContent || '0') : 0;

                  // 获取要素ID
                  const featureIdElements = xmlDoc.getElementsByTagName('ogc:FeatureId');
                  const featureIdAttr = featureIdElements.length > 0 ?
                    featureIdElements[0].getAttribute('fid') : 'none';

                  console.log('插入的要素数量:', totalInserted);
                  console.log('要素ID:', featureIdAttr);

                  // 检查响应是否成功
                  if (totalInserted > 0 && featureIdAttr !== 'none') {
                    // 提取新添加要素的 ID
                    const featureId = featureIdAttr;
                    console.log('新添加要素的 ID:', featureId);

                    // 从 featureId 中提取 OBJECTID
                    let objectId: string | null = null;
                    if (featureId) {
                      const idParts = featureId.split('.');
                      objectId = idParts.length > 1 ? idParts[1] : featureId;
                    }

                    // 添加到结果中
                    results.addFeatureResults.push({
                      objectId: objectId,
                      success: true
                    });
                  } else {
                    console.error('WFS-T 添加要素失败:', response);
                    results.addFeatureResults.push({
                      success: false,
                      error: '添加要素失败'
                    });
                  }
                } catch (error) {
                  console.error('使用 WFS-T 添加要素时出错:', error);
                  results.addFeatureResults.push({
                    success: false,
                    error: (error as Error).message || '添加要素失败'
                  });
                }
              }

              SLMessage.success('操作成功');
              successCallback?.(results);
            } else if (params.updateFeatures && params.updateFeatures.length > 0) {
              // 更新要素
              console.log('更新要素:', params.updateFeatures);

              // 准备要更新的属性
              const features = params.updateFeatures;

              // 提取第一个要素的属性作为更新属性
              const attributes = { ...features[0].attributes };

              // 执行 WFS-T 更新操作
              // 将 features 转换为数组
              const featuresArray = Array.isArray(features) ? features : features.toArray();
              const response = await updateFeatureAttributes(
                layerName,
                featuresArray,
                attributes,
                true // 更新几何信息
              );
              console.log('GeoServer更新响应:', response);

              // 解析XML响应以检查是否成功
              const parser = new DOMParser();
              const xmlDoc = parser.parseFromString(response.data, "text/xml");

              // 获取更新的要素数量
              const totalUpdatedElements = xmlDoc.getElementsByTagName('wfs:totalUpdated');
              const totalUpdated = totalUpdatedElements.length > 0 ?
                parseInt(totalUpdatedElements[0].textContent || '0') : 0;

              console.log('更新的要素数量:', totalUpdated);

              // 判断是否成功
              if (totalUpdated > 0) {

                // 创建结果对象
                // 由于类型兼容性问题，我们使用 any 类型绕过类型检查
                const results: any = {
                  // 将 features 转换为数组并映射结果
                  updateFeatureResults: featuresArray.map((feature: any) => ({
                    objectId: feature.attributes.OBJECTID,
                    globalId: feature.attributes.SID || null,
                    success: true
                  })),
                  // 添加其他必需的属性
                  addFeatureResults: [],
                  deleteFeatureResults: [],
                  addAttachmentResults: [],
                  updateAttachmentResults: [],
                  deleteAttachmentResults: []
                };

                SLMessage.success('操作成功');
                successCallback?.(results);
              } else {
                console.error('WFS-T 更新要素失败:', response);
                SLMessage.error('操作失败');
                cancelCallBack?.();
              }
            } else if (params.deleteFeatures && params.deleteFeatures.length > 0) {
              // 删除要素
              console.log('删除要素:', params.deleteFeatures);

              // 导入 WFS-T 工具函数
              const { deleteFeature } = await import('@/utils/geoserver/wfsTransactionUtils');

              // 收集要删除的要素的 ID
              const featureIds: string[] = [];

              // 检查 params.deleteFeatures 的结构
              const features = Array.isArray(params.deleteFeatures) ? params.deleteFeatures :
                              (params.deleteFeatures as any).items ||
                              (params.deleteFeatures as any).toArray?.() ||
                              [params.deleteFeatures];

              if (!features || features.length === 0) {
                console.error('没有要删除的要素');
                SLMessage.error('删除失败：没有要删除的要素');
                cancelCallBack?.();
                return;
              }

              // 遍历要素，尝试多种方式获取要素 ID
              features.forEach((feature: any) => {
                // 尝试多种方式获取要素 ID
                if (feature.attributes) {
                  // 1. 首先尝试使用 OBJECTID
                  if (feature.attributes.OBJECTID) {
                    featureIds.push(`${layerName}.${feature.attributes.OBJECTID}`);
                  }
                  // 2. 尝试使用 fid 属性（GeoServer 常用）
                  else if (feature.attributes.fid) {
                    // 如果 fid 已经包含图层名称，则直接使用
                    const fid = feature.attributes.fid;
                    featureIds.push(fid.includes('.') ? fid : `${layerName}.${fid}`);
                  }
                  // 3. 尝试使用 id 属性
                  else if (feature.attributes.id) {
                    featureIds.push(`${layerName}.${feature.attributes.id}`);
                  }
                  // 4. 尝试使用 feature 本身的 id 属性
                  else if (feature.id) {
                    const featureId = typeof feature.id === 'string' ? feature.id : `${feature.id}`;
                    featureIds.push(featureId.includes('.') ? featureId : `${layerName}.${featureId}`);
                  }
                }
              });

              if (featureIds.length === 0) {
                console.log('无法获取要素 ID，尝试使用基于几何位置的删除方法');

                try {
                  // 导入基于几何位置的删除函数
                  const { deleteFeatureByGeometry } = await import('@/utils/geoserver/wfsTransactionUtils');

                  // 执行基于几何位置的删除操作
                  const response = await deleteFeatureByGeometry(layerName, features);
                  console.log('GeoServer 基于几何位置删除响应:', response);

                  // 解析 XML 响应以检查是否成功
                  const parser = new DOMParser();
                  const xmlDoc = parser.parseFromString(response.data, "text/xml");

                  // 获取删除的要素数量
                  const totalDeletedElements = xmlDoc.getElementsByTagName('wfs:totalDeleted');
                  const totalDeleted = totalDeletedElements.length > 0 ?
                    parseInt(totalDeletedElements[0].textContent || '0') : 0;

                  console.log('删除的要素数量:', totalDeleted);

                  // 判断是否成功
                  if (totalDeleted > 0) {
                    // 创建结果对象
                    const results: any = {
                      // 将删除的要素映射为结果
                      deleteFeatureResults: features.map((feature: any, index: number) => ({
                        objectId: index + 1, // 使用索引作为临时 ID
                        globalId: null,
                        success: true
                      })),
                      // 添加其他必需的属性
                      addFeatureResults: [],
                      updateFeatureResults: [],
                      addAttachmentResults: [],
                      updateAttachmentResults: [],
                      deleteAttachmentResults: []
                    };

                    SLMessage.success('删除成功');
                    successCallback?.(results);
                    return;
                  } else {
                    console.error('基于几何位置的删除失败:', response);
                    SLMessage.error('删除失败');
                    cancelCallBack?.();
                    return;
                  }
                } catch (error) {
                  console.error('基于几何位置的删除出错:', error);
                  SLMessage.error('删除失败：无法获取要素 ID，请确保要素有有效的 ID');
                  cancelCallBack?.();
                  return;
                }
              }

              console.log('要删除的要素 ID:', featureIds);

              try {
                // 执行 WFS-T 删除操作
                const response = await deleteFeature(layerName, featureIds);
                console.log('GeoServer 删除响应:', response);

                // 解析 XML 响应以检查是否成功
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(response.data, "text/xml");

                // 获取删除的要素数量
                const totalDeletedElements = xmlDoc.getElementsByTagName('wfs:totalDeleted');
                const totalDeleted = totalDeletedElements.length > 0 ?
                  parseInt(totalDeletedElements[0].textContent || '0') : 0;

                console.log('删除的要素数量:', totalDeleted);

                // 判断是否成功
                if (totalDeleted > 0) {
                  // 创建结果对象
                  const results: any = {
                    // 将删除的要素映射为结果
                    deleteFeatureResults: params.deleteFeatures.map((feature: any) => ({
                      objectId: feature.attributes.OBJECTID,
                      globalId: feature.attributes.SID || null,
                      success: true
                    })),
                    // 添加其他必需的属性
                    addFeatureResults: [],
                    updateFeatureResults: [],
                    addAttachmentResults: [],
                    updateAttachmentResults: [],
                    deleteAttachmentResults: []
                  };

                  SLMessage.success('删除成功');
                  successCallback?.(results);
                } else {
                  console.error('WFS-T 删除要素失败:', response);
                  SLMessage.error('删除失败');
                  cancelCallBack?.();
                }
              } catch (error) {
                console.error('WFS-T 删除操作失败:', error);
                SLMessage.error('删除失败');
                cancelCallBack?.();
              }
            } else {
              console.error('未知的编辑操作:', params);
              SLMessage.error('未知的编辑操作');
              cancelCallBack?.();
            }
          } else {
            // ArcGIS 模式
            console.log('使用 ArcGIS 模式进行编辑操作');

            // 确保 layerid 是数字
            const numericLayerId = typeof layerid === 'string' ? parseInt(layerid) || 0 : layerid;

            // 使用原始的 applyEdits 函数
            const res = await applyEdits(numericLayerId, params);
            SLMessage.success('操作成功');
            successCallback?.(res);
          }
        } catch (error) {
          console.error('编辑操作失败:', error);
          SLMessage.error('操作失败');
          cancelCallBack?.();
        }
      })
      .catch(() => {
        cancelCallBack?.();
      });
  };
  const resetForm = async (layerid?: number, graphic?: __esri.Graphic) => {
    const fields = await initPipeEditForm(layerid, graphic?.attributes);
    FormConfig.value.group[0].fields = [...fields];
    if (!refForm.value) return;
    refForm.value.dataForm = {
      ...(graphic?.attributes || {})
    };
    fields.map((item) => {
      if (item.type === 'date' && item.field) {
        refForm.value.dataForm[item.field] = formatDate(
          graphic?.attributes[item.field],
          formatterDate
        );
      }
    });
    if (graphic?.geometry.type === 'polyline') {
      const pipelength = calcLength(
        (graphic.geometry as __esri.Polyline).paths[0],
        'meters',
        graphic.geometry.spatialReference
      );
      refForm.value.dataForm.PIPELENGTH = pipelength;
    }
  };
  const submitForm = (extraParams: {
    layerid: any;
    graphic: any;
    layername: string;
    successCallback: () => void;
  }) => {
    refForm.value?.Submit(extraParams);
  };
  return {
    refForm,
    FormConfig,
    resetForm,
    submitForm,
    TreeData,
    resetTreeData,
    submitEdit
  };
};
export const useEditArea = (drawCallback?: () => void) => {
  let drawer: __esri.Draw | undefined;
  let drawAction: __esri.DrawAction | undefined;
  let editArea: __esri.Graphic | undefined;
  let editAreaLayer: __esri.GraphicsLayer | undefined;
  let drawAdd: { remove: () => void } | undefined;
  let drawComp: { remove: () => void } | undefined;
  const curType = ref<'polygon' | 'rectangle' | 'circle'>('rectangle');
  let view: __esri.MapView | undefined;
  const drawingEditRange = ref<boolean>(false);
  const startDraw = (mapView?: __esri.MapView, type?: any) => {
    if (!mapView) return;
    drawingEditRange.value = true;
    curType.value = type || 'rectangle';
    view = mapView;
    setMapCursor('crosshair');
    editAreaLayer = getGraphicLayer(view, {
      id: 'pipe-editable',
      title: '不可编辑区域'
    });
    drawer?.destroy();
    drawer = initDrawer(view);
    drawAction?.destroy();
    drawAction = drawer?.create(curType.value, {
      mode:
        ['circle', 'rectangle'].indexOf(curType.value) !== -1
          ? 'freehand'
          : 'click'
    });

    drawAdd = drawAction?.on(['vertex-add', 'cursor-update'], updateVertices);
    drawComp = drawAction?.on('draw-complete', async (e) => {
      updateVertices(e);
      setMapCursor('');
      drawingEditRange.value = false;
      refresh();
      drawCallback?.();
    });
  };
  const updateVertices = (e) => {
    const graphic =
      curType.value === 'circle'
        ? createEllipse(
            e.vertices,
            view?.spatialReference,
            setSymbol('polygon', {
              color: [0, 255, 0, 0.2],
              outlineColor: [0, 255, 0, 1],
              outlineWidth: 1
            })
          )
        : curType.value === 'polygon'
          ? e.vertices.length < 3
            ? createPolyline(
                e.vertices,
                view?.spatialReference,
                setSymbol('polyline', {
                  color: [0, 255, 0, 1],
                  width: 1
                })
              )
            : createPolygon(
                e.vertices,
                view?.spatialReference,
                setSymbol('polygon', {
                  color: [0, 255, 0, 0.2],
                  outlineColor: [0, 255, 0, 1],
                  outlineWidth: 1
                })
              )
          : createRectGraphic(
              e.vertices,
              view?.spatialReference,
              setSymbol('polygon', {
                color: [0, 255, 0, 0.2],
                outlineColor: [0, 255, 0, 1],
                outlineWidth: 1
              })
            );
    // if (!graphic) return

    editAreaLayer?.removeAll();
    graphic && editAreaLayer?.add(graphic);
    editArea = graphic;
  };
  const refresh = () => {
    if (!view) return;
    if (!editArea) return;
    if (editArea.geometry.type === 'polygon') {
      const fullExtent = view.extent;
      if (!fullExtent) return;
      const outline = [
        [fullExtent.xmin, fullExtent.ymin],
        [fullExtent.xmax, fullExtent.ymin],
        [fullExtent.xmax, fullExtent.ymax],
        [fullExtent.xmin, fullExtent.ymax],
        [fullExtent.xmin, fullExtent.ymin]
      ];
      // 确保绘制的几何环方向为逆时针（内环）
      let rings = (editArea.geometry as __esri.Polygon).rings[0];
      rings.push(rings[0]);
      console.log(rings);
      // 处理多边形绘制时，顺时针绘制导致可编辑范围被遮挡的问题
      // 如果是顺时针，则反转为逆时针
      if (
        !turf.booleanClockwise(turf.lineString(rings)) &&
        curType.value === 'polygon'
      ) {
        rings = turf.rewind(turf.polygon([rings]), { reverse: true }).geometry
          .coordinates[0];
      }

      const filledArrea = createGraphic({
        geometry: createGeometry(
          'polygon',
          [outline, rings],
          view?.spatialReference
        ),
        symbol: setSymbol('polygon', {
          color: [87, 90, 104, 0.5],
          outlineColor: [255, 255, 255, 1],
          outlineWidth: 1
        })
      });
      editAreaLayer?.removeAll();
      editAreaLayer?.add(filledArrea);
    }
  };
  /**
   *
   * @param layerid 查询要素的图层id
   * @param layer 要将查询到的要素添加到的图层
   * @returns 查询到的要素
   */
  const queryFeatures = async (
    layerid?: number | string,
    layer?: __esri.GraphicsLayer
  ): Promise<__esri.Graphic[]> => {
    if (layerid === undefined) return [];
    if (!editArea) return [];

    try {
      console.log('开始查询要素:', layerid, editArea);
      debugger
      let features: __esri.Graphic[] = [];

      // 根据环境选择不同的查询方式
      if ((window as any).GIS_SERVER_SWITCH === true) {
        // 使用GeoServer查询
        try {
          // 使用QuickSearch.vue中的方法，直接调用QueryByPolygon函数
          console.log('开始执行GeoServer查询:', layerid, editArea.geometry);

          // 确保我们有有效的几何对象
          if (!editArea.geometry) {
            console.error('没有有效的几何对象');
            return [];
          }

          // 将几何对象转换为any类型，以访问rings属性
          const geometry = editArea.geometry as any;
          if (!geometry.rings || !geometry.rings[0]) {
            console.error('几何对象没有rings属性或rings为空');
            return [];
          }

          // 确保多边形是闭合的（第一个点和最后一个点相同）
          const rings = geometry.rings[0];
          if (rings.length > 0) {
            const firstPoint = rings[0];
            const lastPoint = rings[rings.length - 1];

            // 检查第一个点和最后一个点是否相同
            if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
              console.log('多边形不闭合，添加闭合点');
              // 添加闭合点（复制第一个点到最后）
              rings.push([...firstPoint]);
            }
          }

          // 使用QueryByPolygon函数进行查询
          // 如果图层ID已经包含命名空间，直接使用，否则添加命名空间
          const layerName = typeof layerid === 'string' && layerid.includes(':') ? layerid : `guazhou:${layerid}`;
          console.log('使用图层名称进行查询:', layerName);
          const res = await QueryByPolygon(layerName, geometry.rings[0]);

          console.log('查询结果:', res);

          // 处理查询结果
          if (res && res.data && res.data.features && Array.isArray(res.data.features)) {
            // 将GeoJSON要素转换为ArcGIS Graphic
            console.log('查询到的要素数量:', res.data.features.length);

            features = [];
            debugger
            for (const feature of res.data.features) {
              try {
                // 使用文件顶部定义的转换函数

                // 直接获取ArcGIS几何类实例
                const esriGeometry = convertGeoJSONToArcGIS(feature.geometry);

                if (!esriGeometry) {
                  console.warn('转换几何对象失败:', feature.geometry);
                  continue;
                }
                const graphic = new Graphic({
                  id: feature.id,
                  type: feature.geometry.type,
                  geometry: esriGeometry,
                  attributes: feature.properties || {},
                  symbol: setSymbol(esriGeometry.type)
                });

                features.push(graphic);
              } catch (error) {
                console.error('处理GeoJSON要素失败:', error);
              }
            }
          }
        } catch (error) {
          console.error('GeoServer查询失败:', error);
        }
      } else {
        // 使用ArcGIS Server查询
        const res = await excuteQuery(
          window.SITE_CONFIG.GIS_CONFIG.gisService +
            window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
            '/' +
            layerid,
          initQueryParams({
            geometry: editArea?.geometry,
            where: '1=1',
            outFields: ['*']
          })
        );

        features = res.features.map((item) => {
          item.symbol = setSymbol(item.geometry.type);
          return item;
        });
      }

      // 将要素添加到图层
      if (layer && features.length > 0) {
        console.log('将要素添加到图层:', features.length);
        layer.removeAll();
        layer.addMany(features);
      } else {
        console.warn('没有要素添加到图层或图层不存在:', features.length, !!layer);
      }

      return features;
    } catch (error) {
      console.error('查询要素失败:', error);
      return [];
    }
  };
  const destroy = (view?: __esri.MapView) => {
    editAreaLayer && view?.map.remove(editAreaLayer);
    drawAdd?.remove();
    drawComp?.remove();
    drawAction?.destroy();
    drawer?.destroy();
    setMapCursor('');
  };
  const clear = () => {
    editAreaLayer?.removeAll();
    editArea = undefined;
  };
  return {
    drawingEditRange,
    curType,
    destroy,
    clear,
    startDraw,
    refresh,
    queryFeatures
  };
};
export const useSplit = (
  splitCallback?: (res: __esri.EditsResult[]) => any
) => {
  let splitLayer: __esri.GraphicsLayer | undefined;
  let splitPoint: __esri.Graphic | undefined;
  let layerId: number | undefined;
  let pipe: __esri.Graphic | undefined;
  let clickEvent: any;
  let moveEvent: any;
  let fittingLayerInfo: ILayerInfo | undefined;
  /**
   * 根据鼠标所在点来计算拆分点，并添加到图层中
   * @param graphic 管线要素
   * @param point 鼠标所在点
   * @returns
   */
  const setSplitPoint = (graphic?: __esri.Graphic, point?: __esri.Point) => {
    pipe = graphic;
    const pG = getNeerestPoint(pipe?.geometry, point);
    if (!pG || !splitLayer) return;
    splitPoint = createGraphic({
      geometry: pG,
      symbol: setSymbol('point', {
        color: [255, 255, 255],
        outlineColor: [255, 0, 255],
        outlineWidth: 2
      })
    });
    splitLayer.removeAll();
    splitPoint && splitLayer.add(splitPoint);
  };
  const init = (
    view?: __esri.MapView,
    layerid?: any,
    fittingLayerinfo?: ILayerInfo,
    /** 处理拆分点 */
    resolvePoint?: (point: __esri.Point) => any
  ) => {
    splitLayer?.destroy();
    if (!view) return;
    splitLayer = getGraphicLayer(view, {
      id: 'split-layer',
      title: '拆分点'
    });
    SLMessage.info('通过滑动鼠标并点击地图以确定拆分点');
    layerId = layerid;
    fittingLayerInfo = fittingLayerinfo;
    moveEvent = view?.on('pointer-move', (result) => {
      const point = view.toMap(result.native);
      resolvePoint?.(point);
    });
    clickEvent = view?.on('click', (result) => {
      moveEvent?.remove();
      resolvePoint?.(result.mapPoint);
      resolveSplite();
    });
  };
  const resolveSplite = () => {
    SLConfirm('将变更应用到空间数据？', '提示信息')
      .then(async () => {
        if (!pipe || !splitPoint || layerId === undefined) {
          SLMessage.error('操作失败');
          return;
        }
        const featureLayer = new FeatureLayer({
          url:
            window.SITE_CONFIG.GIS_CONFIG.gisService +
            window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer +
            '/' +
            layerId
        });
        const splitGeometry = splitPoint.geometry as __esri.Point;
        const splitPointSID = 'JD' + moment().valueOf();
        try {
          SLMessage.info('正在保存，请稍候...');
          const oldGeometry = pipe.geometry as __esri.Polyline;
          const attrs = { ...(pipe.attributes || {}) };
          delete attrs.OBJECTID;
          const addPipe1Geo = createGeometry(
            'polyline',
            [[oldGeometry.paths[0][0], [splitGeometry.x, splitGeometry.y]]],
            pipe.geometry.spatialReference
          ) as __esri.Polyline;
          let addPipe1: __esri.Graphic = createGraphic({
            geometry: addPipe1Geo,
            attributes: {
              ...(attrs || {}),
              PIPELENGTH: calcLength(
                addPipe1Geo.paths[0],
                'meters',
                pipe.geometry.spatialReference
              )
            }
          });
          const addPipe2Geo = createGeometry(
            'polyline',
            [[[splitGeometry.x, splitGeometry.y], oldGeometry.paths[0][1]]],
            pipe.geometry.spatialReference
          ) as __esri.Polyline;
          let addPipe2 = createGraphic({
            geometry: addPipe2Geo,
            attributes: {
              ...(attrs || {}),
              SID: 'GW' + (moment().valueOf() + 1),
              PIPELENGTH: calcLength(
                addPipe2Geo.paths[0],
                'meters',
                pipe.geometry.spatialReference
              )
            }
          });
          // pipe.geometry = addPipe1.geometry
          // 不需要特定站点的坐标转换
          const promiseDeletePipe = featureLayer.applyEdits({
            deleteFeatures: [pipe]
          });
          const promisePipe1 = featureLayer.applyEdits({
            // addFeatures: [addPipe2],
            addFeatures: [addPipe1]
          });
          const promisePipe2 = featureLayer.applyEdits({
            addFeatures: [addPipe2]
            // updateFeatures: [pipe]
          });
          // console.log(resPipe1.addFeatureResults, resPipe2.addFeatureResults, resPipe.deleteFeatureResults)
          const res = await Promise.all([
            promiseDeletePipe,
            promisePipe1,
            promisePipe2
          ]);
          featureLayer.destroy();
          SLMessage.success('保存成功,请及时校核拆分管线的属性');
          splitCallback?.(res);
        } catch (error) {
          featureLayer.destroy();
          console.log(error);
          SLMessage.error('保存失败');
          destroy();
          return;
        }

        if (fittingLayerInfo) {
          const fittingLayer = new FeatureLayer({
            url:
              window.SITE_CONFIG.GIS_CONFIG.gisService +
              window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer +
              '/' +
              fittingLayerInfo.layerid
          });
          try {
            // 不需要特定站点的坐标转换
            const fittingPoint = splitPoint;
            const fittingGeometry = fittingPoint.geometry as __esri.Point;

            const fields = await GetFieldConfig(fittingLayerInfo.layername);
            fields.data?.result?.rows?.map((item) => {
              fittingPoint.attributes[item.name] = null;
            });
            fittingPoint.attributes = {
              ...fittingPoint.attributes,
              SID: splitPointSID,
              SUBTYPE: '直线点',
              CREATEDDATE: moment().format(formatterDate),
              X: fittingGeometry.x,
              Y: fittingGeometry.y,
              DIAMETER: pipe?.attributes.DIAMETER,
              MATERIAL: pipe?.attributes.MATERIAL,
              DEPTH: pipe?.attributes.DEPTH,
              LANEWAY: pipe?.attributes.LANEWAY,
              BURYTYPE: pipe?.attributes.BURYTYPE
            };
            await fittingLayer.applyEdits({
              addFeatures: [fittingPoint]
            });
          } catch (error) {
            console.log(error);
          }
          fittingLayer.destroy;
        }
        destroy();
      })
      .catch(() => {
        destroy();
      });
  };
  const destroy = () => {
    clickEvent?.remove();
    moveEvent?.remove();
    splitLayer?.removeAll();
  };
  return {
    splitLayer,
    splitPoint,
    setSplitPoint,
    init,
    resolveSplite,
    destroy
  };
};
export const useRectSelect = (callBack: (graphic: __esri.Graphic) => void) => {
  const { initSketch, destroySketch } = useSketch();
  let sketch: __esri.SketchViewModel | undefined;
  let rectLayer: __esri.GraphicsLayer | undefined;
  const start = (view?: __esri.MapView) => {
    rectLayer = getGraphicLayer(view, {
      id: 'rect-layer',
      title: '框选'
    });
    destroySketch();
    sketch = initSketch(view, rectLayer, {
      updateCallBack: resolveDrawEnd,
      createCallBack: resolveDrawEnd
    });
    sketch?.create('rectangle');
  };

  const resolveDrawEnd = (result: ISketchHandlerParameter) => {
    if (result.state === 'complete') {
      const graphic = result.graphics[0];
      rectLayer?.removeAll();
      callBack?.(graphic);
      destroy();
    }
  };
  const destroy = () => {
    sketch?.destroy();
    rectLayer?.removeAll();
  };
  return {
    start,
    destroy
  };
};
export const useMergePipe = () => {
  const curNode = ref<NormalOption>();
  const TreeData = ref<IFormTree>({
    type: 'tree',
    options: [],
    label: '选择基准要素：',
    nodeClick: (data) => (curNode.value = data)
  });
  const resetForm = (graphics: __esri.Graphic[]) => {
    TreeData.value.options = graphics.map((item) => {
      return {
        label: item.attributes.SID,
        value: item.attributes.OBJECTID,
        data: item.attributes
      };
    });
    curNode.value = undefined;
  };
  return {
    TreeData,
    resetForm,
    curNode
  };
};
