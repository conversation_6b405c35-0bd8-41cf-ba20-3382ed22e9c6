/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.dataMonitor;

import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.thingsboard.server.common.data.dataMonitor.DataMonitor;
import org.thingsboard.server.common.data.energy.Energy;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EnergyId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.Dao;
import org.thingsboard.server.dao.model.sql.DataMonitorEntity;
import org.thingsboard.server.dao.model.sql.MenuRoleEntity;

import java.util.List;
import java.util.UUID;

public interface DataMonitorDao extends Dao<DataMonitor> {

    ListenableFuture<List<DataMonitor>> findDataMonitorByTenant(TenantId tenantId);

    ListenableFuture<List<DataMonitor>> findDataMonitorByTenantAndTime(TenantId tenantId,long start,long end);

    ListenableFuture<List<DataMonitor>> findDataMonitorByDeviceAndTime(DeviceId deviceId, long start, long end);


}
