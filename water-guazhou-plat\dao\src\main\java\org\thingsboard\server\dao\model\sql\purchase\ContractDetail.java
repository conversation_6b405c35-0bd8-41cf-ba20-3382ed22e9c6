package org.thingsboard.server.dao.model.sql.purchase;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse;
import org.thingsboard.server.dao.util.imodel.response.annotations.Compute;
import org.thingsboard.server.dao.util.imodel.response.annotations.Flatten;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
public class ContractDetail {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 合同主表ID
    private String mainId;

    // 设备编码
    private String serialId;

    // 单位
    private String unit;

    // 数量
    private String num;

    // 价格
    private String price;

    // 税率
    private String taxRate;

    // 备注
    private String remark;

    // 租户ID
    @ParseTenantName
    private String tenantId;

    @Flatten
    @TableField(exist = false)
    private DeviceInfoResponse deviceInfoResponse;

    @Compute("deviceName")
    @TableField(exist = false)
    private String deviceName;

    private String deviceName() {
        return deviceInfoResponse.getName();
    }

}
