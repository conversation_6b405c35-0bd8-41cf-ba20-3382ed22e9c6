package org.thingsboard.server.dao.dashChart;

import org.thingsboard.server.dao.model.sql.DashChartEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/3 14:01
 */
public interface DashChartService {


    DashChartEntity save(DashChartEntity dashChartEntity);

    List<DashChartEntity> findByTenant(String tenantId);

    List<DashChartEntity> findByOriginatorId(String originatorId);

    boolean delete(String id);

    boolean deleteByDashboardId(String dashboardId);

    DashChartEntity findByDashJsonId(String jsonId);

    List<DashChartEntity> findByOriginatorIdAndType(String originatorId,String type);

    List<DashChartEntity> findByOriginatorIdAndName(String originatorId, String name);
}
