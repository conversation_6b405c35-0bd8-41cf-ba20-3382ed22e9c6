import { request } from '@/plugins/axios'

/**
 * 查询DMA核算配置列表
 * @param params
 * @returns
 */
export const GetDMAAccountCycleConfig = (params: {
  month?: string
  name?: string
  partitionId: string
}) => {
  return request({
    url: '/api/spp/accountCycleConfig/list',
    method: 'get',
    params
  })
}
/**
 * 新增DMA核算配置
 * @param params
 * @returns
 */
export const PostDMAAccountCycleConfig = (params: {
  partitionId: string
  startTime: string
  endTime: string
  month: string
  autoGenerate: string
}) => {
  return request({
    url: '/api/spp/accountCycleConfig',
    method: 'post',
    data: params
  })
}

/**
 * 删除DMA核算配置
 * @param ids
 * @returns
 */
export const DeleteDMAAccountCycleConfig = (ids: string[]) => {
  return request({
    url: '/api/spp/accountCycleConfig',
    method: 'delete',
    data: ids
  })
}
