package org.thingsboard.server.dao.model.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("sp_order_record_type")
public class OrderRecordType {
    // id
    private String id;

    // 指令名称
    private String name;

    // 部门id，多个用逗号隔开
    private String deptIdList;

    // 部门id，多个用逗号隔开
    private String deptNameList;

    // 客户id
    private String tenantId;

}
