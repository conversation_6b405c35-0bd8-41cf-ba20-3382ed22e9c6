import{R as J}from"./index-r0dFAfgr.js";import{ap as Ot,J as $}from"./Point-WxyopZva.js";import{aQ as k,aP as q,ex as mt,aY as Rt,ey as At,aR as Et,d0 as M,af as p,b2 as et,aO as nt,ez as pt,ag as T}from"./MapView-DaoQedLH.js";import{s as lt,d as ot,a as Ft,e as Nt,r as bt,_ as C,p as st,T as U,k as z,V as it,R as L}from"./sphere-NgXH-gLx.js";import{p as A,A as Mt,j as x,H as gt}from"./plane-BhzlJB-C.js";import{i as rt}from"./Util-sSNWzwlq.js";function Z(o){return o?{ray:ot(o.ray),c0:o.c0,c1:o.c1}:{ray:ot(),c0:0,c1:Number.MAX_VALUE}}function kt(o,t=Z()){return Ft(o,t.ray),t.c0=0,t.c1=Number.MAX_VALUE,t}function qt(o,t){return ut(o,o.c0,t)}function Ct(o,t){return ut(o,o.c1,t)}function ut(o,t,e){return k(e,o.ray.origin,q(e,o.ray.direction,t))}new lt(()=>Z());function Ut(o){return o?[A(o[0]),A(o[1]),A(o[2]),A(o[3]),A(o[4]),A(o[5])]:[A(),A(),A(),A(),A(),A()]}function Bt(){return[p(),p(),p(),p(),p(),p(),p(),p()]}function Vt(o,t){for(let e=0;e<D.NUM;e++)Mt(o[e],t[e])}function Kt(o,t,e,n=xt){const s=mt(Nt.get(),t,o);Rt(s,s);for(let i=0;i<Q.NUM;++i){const h=At(bt.get(),It[i],s);Et(n[i],h[0]/h[3],h[1]/h[3],h[2]/h[3])}St(e,n)}function St(o,t){x(t[r.FAR_BOTTOM_LEFT],t[r.NEAR_BOTTOM_LEFT],t[r.NEAR_TOP_LEFT],o[g.LEFT]),x(t[r.NEAR_BOTTOM_RIGHT],t[r.FAR_BOTTOM_RIGHT],t[r.FAR_TOP_RIGHT],o[g.RIGHT]),x(t[r.FAR_BOTTOM_LEFT],t[r.FAR_BOTTOM_RIGHT],t[r.NEAR_BOTTOM_RIGHT],o[g.BOTTOM]),x(t[r.NEAR_TOP_LEFT],t[r.NEAR_TOP_RIGHT],t[r.FAR_TOP_RIGHT],o[g.TOP]),x(t[r.NEAR_BOTTOM_LEFT],t[r.NEAR_BOTTOM_RIGHT],t[r.NEAR_TOP_RIGHT],o[g.NEAR]),x(t[r.FAR_BOTTOM_RIGHT],t[r.FAR_BOTTOM_LEFT],t[r.FAR_TOP_LEFT],o[g.FAR])}function y(o,t){for(let e=0;e<D.NUM;e++){const n=o[e];if(n[0]*t[0]+n[1]*t[1]+n[2]*t[2]+n[3]>=t[3])return!1}return!0}function Wt(o,t){for(let e=0;e<D.NUM;e++){const n=o[e];if(!gt(n,t))return!1}return!0}var g,r;(function(o){o[o.LEFT=0]="LEFT",o[o.RIGHT=1]="RIGHT",o[o.BOTTOM=2]="BOTTOM",o[o.TOP=3]="TOP",o[o.NEAR=4]="NEAR",o[o.FAR=5]="FAR"})(g||(g={})),function(o){o[o.NEAR_BOTTOM_LEFT=0]="NEAR_BOTTOM_LEFT",o[o.NEAR_BOTTOM_RIGHT=1]="NEAR_BOTTOM_RIGHT",o[o.NEAR_TOP_RIGHT=2]="NEAR_TOP_RIGHT",o[o.NEAR_TOP_LEFT=3]="NEAR_TOP_LEFT",o[o.FAR_BOTTOM_LEFT=4]="FAR_BOTTOM_LEFT",o[o.FAR_BOTTOM_RIGHT=5]="FAR_BOTTOM_RIGHT",o[o.FAR_TOP_RIGHT=6]="FAR_TOP_RIGHT",o[o.FAR_TOP_LEFT=7]="FAR_TOP_LEFT"}(r||(r={}));r.FAR_BOTTOM_RIGHT,r.NEAR_BOTTOM_RIGHT,r.NEAR_BOTTOM_LEFT,r.FAR_BOTTOM_LEFT,r.NEAR_BOTTOM_LEFT,r.NEAR_BOTTOM_RIGHT,r.NEAR_TOP_RIGHT,r.NEAR_TOP_LEFT,r.FAR_BOTTOM_RIGHT,r.FAR_BOTTOM_LEFT,r.FAR_TOP_LEFT,r.FAR_TOP_RIGHT,r.NEAR_BOTTOM_RIGHT,r.FAR_BOTTOM_RIGHT,r.FAR_TOP_RIGHT,r.NEAR_TOP_RIGHT,r.FAR_BOTTOM_LEFT,r.NEAR_BOTTOM_LEFT,r.NEAR_TOP_LEFT,r.FAR_TOP_LEFT,r.FAR_TOP_LEFT,r.NEAR_TOP_LEFT,r.NEAR_TOP_RIGHT,r.FAR_TOP_RIGHT;var D,Q;(function(o){o[o.NUM=6]="NUM"})(D||(D={})),function(o){o[o.NUM=8]="NUM"}(Q||(Q={}));const It=[M(-1,-1,-1,1),M(1,-1,-1,1),M(1,1,-1,1),M(-1,1,-1,1),M(-1,-1,1,1),M(1,-1,1,1),M(1,1,1,1),M(-1,1,1,1)];new lt(Z);const xt=Bt();class H{get bounds(){return this._root.bounds}get halfSize(){return this._root.halfSize}get root(){return this._root.node}get maximumObjectsPerNode(){return this._maximumObjectsPerNode}get maximumDepth(){return this._maximumDepth}get objectCount(){return this._objectCount}constructor(t,e){this._objectToBoundingSphere=t,this._maximumObjectsPerNode=10,this._maximumDepth=20,this._degenerateObjects=new Set,this._root=new l,this._objectCount=0,e&&(e.maximumObjectsPerNode!==void 0&&(this._maximumObjectsPerNode=e.maximumObjectsPerNode),e.maximumDepth!==void 0&&(this._maximumDepth=e.maximumDepth))}destroy(){this._degenerateObjects.clear(),l.clearPool(),Y[0]=null,S.prune(),I.prune()}add(t,e=t.length){this._objectCount+=e,this._grow(t,e);const n=l.acquire();for(let s=0;s<e;s++){const i=t[s];this._isDegenerate(i)?this._degenerateObjects.add(i):(n.init(this._root),this._add(i,n))}l.release(n)}remove(t,e=null){this._objectCount-=t.length;const n=l.acquire();for(const s of t){const i=J(e)?e:C(this._objectToBoundingSphere(s),zt);G(i[3])?(n.init(this._root),this._remove(s,i,n)):this._degenerateObjects.delete(s)}l.release(n),this._shrink()}update(t,e){if(!G(e[3])&&this._isDegenerate(t))return;const n=jt(t);this.remove(n,e),this.add(n)}forEachAlongRay(t,e,n){const s=st(t,e);this._forEachNode(this._root,i=>{if(!this._intersectsNode(s,i))return!1;const h=i.node;return h.terminals.forAll(d=>{this._intersectsObject(s,d)&&n(d)}),h.residents!==null&&h.residents.forAll(d=>{this._intersectsObject(s,d)&&n(d)}),!0})}forEachAlongRayWithVerticalOffset(t,e,n,s){const i=st(t,e);this._forEachNode(this._root,h=>{if(!this._intersectsNodeWithOffset(i,h,s))return!1;const d=h.node;return d.terminals.forAll(a=>{this._intersectsObjectWithOffset(i,a,s)&&n(a)}),d.residents!==null&&d.residents.forAll(a=>{this._intersectsObjectWithOffset(i,a,s)&&n(a)}),!0})}forEach(t){this._forEachNode(this._root,e=>{const n=e.node;return n.terminals.forAll(t),n.residents!==null&&n.residents.forAll(t),!0}),this._degenerateObjects.forEach(t)}forEachDegenerateObject(t){this._degenerateObjects.forEach(t)}findClosest(t,e,n,s=()=>!0,i=1/0){let h=1/0,d=1/0,a=null;const c=V(t,e),f=u=>{if(--i,!s(u))return;const O=this._objectToBoundingSphere(u);if(!y(n,O))return;const F=B(t,e,z(O)),P=F-O[3],_=F+O[3];P<h&&(h=P,d=_,a=u)};return this._forEachNodeDepthOrdered(this._root,u=>{if(i<=0||!y(n,u.bounds)||(q(E,c,u.halfSize),k(E,E,u.bounds),B(t,e,E)>d))return!1;const O=u.node;return O.terminals.forAll(F=>f(F)),O.residents!==null&&O.residents.forAll(F=>f(F)),!0},t,e),a}forEachInDepthRange(t,e,n,s,i,h,d){let a=-1/0,c=1/0;const f={setRange:_=>{n===H.DepthOrder.FRONT_TO_BACK?(a=Math.max(a,_.near),c=Math.min(c,_.far)):(a=Math.max(a,-_.far),c=Math.min(c,-_.near))}};f.setRange(s);const u=B(e,n,t),O=V(e,n),F=V(e,-n),P=_=>{if(!d(_))return;const b=this._objectToBoundingSphere(_),j=z(b),tt=B(e,n,j)-u,ft=tt-b[3],Tt=tt+b[3];ft>c||Tt<a||!y(h,b)||i(_,f)};this._forEachNodeDepthOrdered(this._root,_=>{if(!y(h,_.bounds)||(q(E,O,_.halfSize),k(E,E,_.bounds),B(e,n,E)-u>c)||(q(E,F,_.halfSize),k(E,E,_.bounds),B(e,n,E)-u<a))return!1;const b=_.node;return b.terminals.forAll(j=>P(j)),b.residents!==null&&b.residents.forAll(j=>P(j)),!0},e,n)}forEachNode(t){this._forEachNode(this._root,e=>t(e.node,e.bounds,e.halfSize))}forEachNeighbor(t,e){const n=U(e),s=z(e),i=a=>{const c=this._objectToBoundingSphere(a),f=U(c),u=n+f;return!(et(z(c),s)-u*u<=0)||t(a)};let h=!0;const d=a=>{h&&(h=i(a))};this._forEachNode(this._root,a=>{const c=U(a.bounds),f=n+c;if(et(z(a.bounds),s)-f*f>0)return!1;const u=a.node;return u.terminals.forAll(d),h&&u.residents!==null&&u.residents.forAll(d),h}),h&&this.forEachDegenerateObject(d)}_intersectsNode(t,e){return v(e.bounds,2*-e.halfSize,m),v(e.bounds,2*e.halfSize,R),rt(t.origin,t.direction,m,R)}_intersectsNodeWithOffset(t,e,n){return v(e.bounds,2*-e.halfSize,m),v(e.bounds,2*e.halfSize,R),n.applyToMinMax(m,R),rt(t.origin,t.direction,m,R)}_intersectsObject(t,e){const n=this._objectToBoundingSphere(e);return!(n[3]>0)||it(n,t)}_intersectsObjectWithOffset(t,e,n){const s=this._objectToBoundingSphere(e);return!(s[3]>0)||it(n.applyToBoundingSphere(s),t)}_forEachNode(t,e){let n=l.acquire().init(t);const s=[n];for(;s.length!==0;){if(n=s.pop(),e(n)&&!n.isLeaf())for(let i=0;i<n.node.children.length;i++)n.node.children[i]&&s.push(l.acquire().init(n).advance(i));l.release(n)}}_forEachNodeDepthOrdered(t,e,n,s=H.DepthOrder.FRONT_TO_BACK){let i=l.acquire().init(t);const h=[i];for(Pt(n,s,dt);h.length!==0;){if(i=h.pop(),e(i)&&!i.isLeaf())for(let d=7;d>=0;--d){const a=dt[d];i.node.children[a]&&h.push(l.acquire().init(i).advance(a))}l.release(i)}}_remove(t,e,n){S.clear();const s=n.advanceTo(e,(i,h)=>{S.push(i.node),S.push(h)})?n.node.terminals:n.node.residents;if(s.removeUnordered(t),s.length===0)for(let i=S.length-2;i>=0;i-=2){const h=S.data[i],d=S.data[i+1];if(!this._purge(h,d))break}}_nodeIsEmpty(t){if(t.terminals.length!==0)return!1;if(t.residents!==null)return t.residents.length===0;for(let e=0;e<t.children.length;e++)if(t.children[e])return!1;return!0}_purge(t,e){return e>=0&&(t.children[e]=null),!!this._nodeIsEmpty(t)&&(t.residents===null&&(t.residents=new $({shrink:!0})),!0)}_add(t,e){e.advanceTo(this._objectToBoundingSphere(t))?e.node.terminals.push(t):(e.node.residents.push(t),e.node.residents.length>this._maximumObjectsPerNode&&e.depth<this._maximumDepth&&this._split(e))}_split(t){const e=t.node.residents;t.node.residents=null;for(let n=0;n<e.length;n++){const s=l.acquire().init(t);this._add(e.getItemAt(n),s),l.release(s)}}_grow(t,e){if(e!==0&&(at(t,e,n=>this._objectToBoundingSphere(n),N),G(N[3])&&!this._fitsInsideTree(N)))if(this._nodeIsEmpty(this._root.node))C(N,this._root.bounds),this._root.halfSize=1.25*this._root.bounds[3],this._root.updateBoundsRadiusFromHalfSize();else{const n=this._rootBoundsForRootAsSubNode(N);this._placingRootViolatesMaxDepth(n)?this._rebuildTree(N,n):this._growRootAsSubNode(n),l.release(n)}}_rebuildTree(t,e){nt(W,e.bounds),W[3]=e.halfSize,at([t,W],2,s=>s,X);const n=l.acquire().init(this._root);this._root.initFrom(null,X,X[3]),this._root.increaseHalfSize(1.25),this._forEachNode(n,s=>(this.add(s.node.terminals.data,s.node.terminals.length),s.node.residents!==null&&this.add(s.node.residents.data,s.node.residents.length),!0)),l.release(n)}_placingRootViolatesMaxDepth(t){const e=Math.log(t.halfSize/this._root.halfSize)*Math.LOG2E;let n=0;return this._forEachNode(this._root,s=>(n=Math.max(n,s.depth),n+e<=this._maximumDepth)),n+e>this._maximumDepth}_rootBoundsForRootAsSubNode(t){const e=t[3],n=t;let s=-1/0;const i=this._root.bounds,h=this._root.halfSize;for(let a=0;a<3;a++){const c=i[a]-h-(n[a]-e),f=n[a]+e-(i[a]+h),u=Math.max(0,Math.ceil(c/(2*h))),O=Math.max(0,Math.ceil(f/(2*h)))+1,F=2**Math.ceil(Math.log(u+O)*Math.LOG2E);s=Math.max(s,F),w[a].min=u,w[a].max=O}for(let a=0;a<3;a++){let c=w[a].min,f=w[a].max;const u=(s-(c+f))/2;c+=Math.ceil(u),f+=Math.floor(u);const O=i[a]-h-c*h*2;K[a]=O+(f+c)*h}const d=s*h;return K[3]=d*_t,l.acquire().initFrom(null,K,d,0)}_growRootAsSubNode(t){const e=this._root.node;nt(N,this._root.bounds),N[3]=this._root.halfSize,this._root.init(t),t.advanceTo(N,null,!0),t.node.children=e.children,t.node.residents=e.residents,t.node.terminals=e.terminals}_shrink(){for(;;){const t=this._findShrinkIndex();if(t===-1)break;this._root.advance(t),this._root.depth=0}}_findShrinkIndex(){if(this._root.node.terminals.length!==0||this._root.isLeaf())return-1;let t=null;const e=this._root.node.children;let n=0,s=0;for(;s<e.length&&t==null;)n=s++,t=e[n];for(;s<e.length;)if(e[s++])return-1;return n}_isDegenerate(t){return!G(this._objectToBoundingSphere(t)[3])}_fitsInsideTree(t){const e=this._root.bounds,n=this._root.halfSize;return t[3]<=n&&t[0]>=e[0]-n&&t[0]<=e[0]+n&&t[1]>=e[1]-n&&t[1]<=e[1]+n&&t[2]>=e[2]-n&&t[2]<=e[2]+n}}class l{constructor(){this.bounds=L(),this.halfSize=0,this.initFrom(null,null,0,0)}init(t){return this.initFrom(t.node,t.bounds,t.halfSize,t.depth)}initFrom(t,e,n,s=this.depth){return this.node=J(t)?t:l.createEmptyNode(),J(e)&&C(e,this.bounds),this.halfSize=n,this.depth=s,this}increaseHalfSize(t){this.halfSize*=t,this.updateBoundsRadiusFromHalfSize()}updateBoundsRadiusFromHalfSize(){this.bounds[3]=this.halfSize*_t}advance(t){let e=this.node.children[t];e||(e=l.createEmptyNode(),this.node.children[t]=e),this.node=e,this.halfSize/=2,this.depth++;const n=ct[t];return this.bounds[0]+=n[0]*this.halfSize,this.bounds[1]+=n[1]*this.halfSize,this.bounds[2]+=n[2]*this.halfSize,this.updateBoundsRadiusFromHalfSize(),this}advanceTo(t,e,n=!1){for(;;){if(this.isTerminalFor(t))return e&&e(this,-1),!0;if(this.isLeaf()){if(!n)return e&&e(this,-1),!1;this.node.residents=null}const s=this._childIndex(t);e&&e(this,s),this.advance(s)}}isLeaf(){return this.node.residents!=null}isTerminalFor(t){return t[3]>this.halfSize/2}_childIndex(t){const e=this.bounds;return(e[0]<t[0]?1:0)+(e[1]<t[1]?2:0)+(e[2]<t[2]?4:0)}static createEmptyNode(){return{children:[null,null,null,null,null,null,null,null],terminals:new $({shrink:!0}),residents:new $({shrink:!0})}}static acquire(){return l._pool.acquire()}static release(t){l._pool.release(t)}static clearPool(){l._pool.prune()}}function Ht(o,t){o[0]=Math.min(o[0],t[0]-t[3]),o[1]=Math.min(o[1],t[1]-t[3]),o[2]=Math.min(o[2],t[2]-t[3])}function Lt(o,t){o[0]=Math.max(o[0],t[0]+t[3]),o[1]=Math.max(o[1],t[1]+t[3]),o[2]=Math.max(o[2],t[2]+t[3])}function v(o,t,e){e[0]=o[0]+t,e[1]=o[1]+t,e[2]=o[2]+t}function at(o,t,e,n){if(t===1){const s=e(o[0]);C(s,n)}else{m[0]=1/0,m[1]=1/0,m[2]=1/0,R[0]=-1/0,R[1]=-1/0,R[2]=-1/0;for(let s=0;s<t;s++){const i=e(o[s]);G(i[3])&&(Ht(m,i),Lt(R,i))}pt(n,m,R,.5),n[3]=Math.max(R[0]-m[0],R[1]-m[1],R[2]-m[2])/2}}function Pt(o,t,e){if(!I.length)for(let n=0;n<8;++n)I.push({index:0,distance:0});for(let n=0;n<8;++n){const s=ct[n];I.data[n].index=n,I.data[n].distance=B(o,t,s)}I.sort((n,s)=>n.distance-s.distance);for(let n=0;n<8;++n)e[n]=I.data[n].index}function V(o,t){let e,n=1/0;for(let s=0;s<8;++s){const i=B(o,t,ht[s]);i<n&&(n=i,e=ht[s])}return e}function B(o,t,e){return t*(o[0]*e[0]+o[1]*e[1]+o[2]*e[2])}function G(o){return!isNaN(o)&&o!==-1/0&&o!==1/0&&o>0}l._pool=new Ot(l),function(o){var t;(t=o.DepthOrder||(o.DepthOrder={}))[t.FRONT_TO_BACK=1]="FRONT_TO_BACK",t[t.BACK_TO_FRONT=-1]="BACK_TO_FRONT"}(H||(H={}));const ct=[T(-1,-1,-1),T(1,-1,-1),T(-1,1,-1),T(1,1,-1),T(-1,-1,1),T(1,-1,1),T(-1,1,1),T(1,1,1)],ht=[T(-1,-1,-1),T(-1,-1,1),T(-1,1,-1),T(-1,1,1),T(1,-1,-1),T(1,-1,1),T(1,1,-1),T(1,1,1)],_t=Math.sqrt(3),Y=[null];function jt(o){return Y[0]=o,Y}const K=L(),E=p(),m=p(),R=p(),S=new $,zt=L(),N=L(),W=L(),X=L(),w=[{min:0,max:0},{min:0,max:0},{min:0,max:0}],I=new $,dt=[0,0,0,0,0,0,0,0],Xt=H;export{Ut as H,g as U,Xt as V,Z as a,Wt as b,Ct as g,qt as p,Kt as s,Vt as u,kt as y};
