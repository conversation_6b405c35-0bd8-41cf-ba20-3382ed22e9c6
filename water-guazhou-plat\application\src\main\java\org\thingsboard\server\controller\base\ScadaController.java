/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONObject;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.EntitySubtype;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.kv.BaseAttributeKvEntry;
import org.thingsboard.server.common.data.kv.StringDataEntry;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 组态API
 */
@RestController
@RequestMapping("api/scada")
public class ScadaController extends BaseController {

    /**
     * 保存组态链接
     */
    @PostMapping("/device")
    @PreAuthorize("hasAnyAuthority( 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SCADA_SAVE)
    public ResponseEntity<JSONObject> saveScadaDevice(@RequestBody JSONObject params) throws ThingsboardException {
        if (!params.containsKey("type") || !params.containsKey("url")) {
            throw new ThingsboardException("请求中必须包含 'type' 和 'url'!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        JSONObject result = new JSONObject();
        // 获取tenant
        TenantId tenantId = getTenantId();
        HttpStatus status;
        // 需要保存的attribute
        BaseAttributeKvEntry attributeKvEntry = new BaseAttributeKvEntry(
                new StringDataEntry(params.getString("type"), params.getString("url")), System.currentTimeMillis());
        // 查询数据库该type是否已有组态链接
        AttributeKvEntry _attributeKvEntry;
        try {
            _attributeKvEntry = attributesService.findNotFuture(tenantId, DataConstants.SERVER_SCOPE, params.getString("type"));
            if (_attributeKvEntry != null) { // 更新
                attributesService.save(tenantId, DataConstants.SERVER_SCOPE, Collections.singletonList(attributeKvEntry));
                result.put("msg", "保存成功");
                status = HttpStatus.OK;
            } else { // 新增
                attributesService.save(tenantId, DataConstants.SERVER_SCOPE, Collections.singletonList(attributeKvEntry));
                status = HttpStatus.CREATED;
                result.put("msg", "新增成功");
            }
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            result.put("err", "服务器内部错误!");
        }

        return ResponseEntity.status(status).body(result);
    }


    /**
     * 查询指定设备类型的组态链接
     */
    @GetMapping("/device/{type}")
    @PreAuthorize("hasAnyAuthority( 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @SysLog(detail = DataConstants.OPERATING_TYPE_SCADA_QUERY_URL)
    public ResponseEntity<JSONObject> queryScadaDevice(@PathVariable String type) throws ThingsboardException {
        if (type.isEmpty()) {
            throw new ThingsboardException("type参数错误!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        TenantId tenantId = getTenantId();
        // 查询
        JSONObject result = new JSONObject();
        HttpStatus status;
        try {
            AttributeKvEntry _attributeKvEntry = attributesService.findNotFuture(tenantId, DataConstants.SERVER_SCOPE, type);
            if (_attributeKvEntry != null) {
                Optional<String> strValue = _attributeKvEntry.getStrValue();
                result.put(type, strValue.get());
                status = HttpStatus.OK;

            } else {
                result.put("err", "该企业下未查询到指定设备类型的组态链接!");
                status = HttpStatus.NOT_FOUND;
            }
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            result.put("err", "服务器内部错误!");
        }

        return ResponseEntity.status(status).body(result);
    }

    /**
     * 删除指定设备类型的组态链接
     */
    @DeleteMapping("/device/{type}")
    @PreAuthorize("hasAnyAuthority( 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SCADA_DELETE)
    public ResponseEntity<JSONObject> deleteScadaDevice(@PathVariable String type) throws ThingsboardException {
        if (type == null) {
            throw new ThingsboardException("type参数错误!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        TenantId tenantId = getTenantId();
        // 查询
        JSONObject result = new JSONObject();
        HttpStatus status;
        try {
            AttributeKvEntry _attributeKvEntry = attributesService.findNotFuture(tenantId, DataConstants.SERVER_SCOPE, type);
            if (_attributeKvEntry != null) {
                attributesService.removeAll(tenantId, tenantId, DataConstants.SERVER_SCOPE, Collections.singletonList(type));
                result.put("msg", "删除成功");
                status = HttpStatus.OK;
            } else {
                result.put("err", "待删除的指定设备类型的组态链接不存在!");
                status = HttpStatus.NOT_FOUND;
            }
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            result.put("err", "服务器内部错误!");
        }

        return ResponseEntity.status(status).body(result);
    }


    /**
     * 查询当前企业可用的设备类型
     */
    @GetMapping("/types")
    @PreAuthorize("hasAnyAuthority( 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @SysLog(detail = DataConstants.OPERATING_TYPE_SCADA_DELETE_URL)
    public ResponseEntity<JSONObject> queryTypesByTenant() throws ThingsboardException {
        TenantId tenantId = getTenantId();
        JSONObject result = new JSONObject();
        HttpStatus status;
        try {
            List<String> types = deviceService.findDeviceTypesByTenantId(tenantId).get().stream()
                    .map(EntitySubtype::getType)
                    .filter(type -> !type.equalsIgnoreCase(DataConstants.DEVICE_TYPE_DTU) && !type.equalsIgnoreCase(DataConstants.GATEWAY_NAME) && !type.equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS))
                    .collect(Collectors.toList());
            if (types.isEmpty()) {
                status = HttpStatus.NOT_FOUND;
                result.put("err", "该企业暂无设备!");
            } else {
                result.put("types", types);
                status = HttpStatus.OK;
            }
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            result.put("err", "内部服务器错误");
        }
        return ResponseEntity.status(status).body(result);
    }
}
