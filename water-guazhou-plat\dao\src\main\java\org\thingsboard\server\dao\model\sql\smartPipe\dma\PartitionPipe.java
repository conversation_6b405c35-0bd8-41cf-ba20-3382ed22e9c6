package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 *
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_PARTITION_PIPE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PartitionPipe {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_PARTITION_PIPE_PARTITION_ID)
    private String partitionId;

    @TableField(ModelConstants.PIPE_PARTITION_PIPE_MAIN_PIPE)
    private String mainPipe;

    @TableField(ModelConstants.PIPE_PARTITION_PIPE_PIPE)
    private String pipe;

    @TableField(ModelConstants.PIPE_PARTITION_PIPE_LENGTH)
    private String length;

    @TableField(ModelConstants.PIPE_PARTITION_PIPE_YEAR)
    private String year;

    @TableField(ModelConstants.PIPE_PARTITION_PIPE_REMARK)
    private String remark;

    @TableField(ModelConstants.PIPE_PARTITION_PIPE_FILE)
    private String file;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
