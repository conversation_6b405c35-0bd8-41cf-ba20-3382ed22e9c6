package org.thingsboard.server.dao.smartService.call;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.call.Blacklist;
import org.thingsboard.server.dao.sql.smartService.call.BlacklistMapper;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class BlacklistServiceImpl implements BlacklistService {
    @Autowired
    private BlacklistMapper blacklistMapper;

    @Override
    public PageData getList(String keywords, int page, int size, String tenantId) {

        List<Blacklist> blacklists = blacklistMapper.getList(keywords, page, size);

        int total = blacklistMapper.getListCount(keywords);

        return new PageData(total, blacklists);

    }

    @Override
    public Blacklist save(Blacklist blacklist) {
        blacklist.setUpdateTime(new Date());
        if (StringUtils.isBlank(blacklist.getId())) {
            blacklist.setCreateTime(new Date());
            blacklistMapper.insert(blacklist);
        } else {
            blacklistMapper.updateById(blacklist);
        }

        return blacklist;
    }

    @Override
    public int delete(List<String> ids) {
        return blacklistMapper.deleteBatchIds(ids);
    }

    @Override
    public boolean checkPhone(Blacklist blacklist) {
        QueryWrapper<Blacklist> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("phone", blacklist.getPhone());
        List<Blacklist> list = blacklistMapper.selectList(queryWrapper);
        for (Blacklist blacklist1 : list) {
            if (!blacklist1.getId().equals(blacklist.getId())) {
                return false;
            }
        }
        return true;
    }
}
