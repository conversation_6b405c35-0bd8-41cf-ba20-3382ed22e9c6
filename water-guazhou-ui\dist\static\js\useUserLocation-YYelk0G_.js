import{w as y}from"./Point-WxyopZva.js";import{n as H}from"./MapView-DaoQedLH.js";import{Q as Y,s as E}from"./index-r0dFAfgr.js";import{G as x}from"./locas-Cxm3ID_S.js";import{c as L}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as O}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{u as T}from"./useHighLight-DPevRAc5.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import X from"./UserLocatePop-CScyuy8U.js";import{g}from"./URLHelper-B9aplt5w.js";import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";const Z=d=>{let o;const u=T(),a=async t=>{var r,p,n,m;const e=await x({page:1,size:999999,...t});return{data:((p=(r=e.data)==null?void 0:r.data)==null?void 0:p.data)||[],total:((m=(n=e.data)==null?void 0:n.data)==null?void 0:m.total)||0}},N=t=>a({...t,userTypeId:"XUNJIANRENYUAN"}),A=t=>a({...t,userTypeId:"CHAOBIAORENYUAN"}),I=t=>a({...t,userTypeId:"QIANGXIURENYUAN"}),U=(t,e,r,p)=>{const n=[];r&&(o=O(t,{id:"user-coords",title:"人员"}),u.removeHoverHighLight(),u.bindHoverHighLight(t,o,d));const m=(e==null?void 0:e.map(s=>{var f;const i=(f=s.coordinate)==null?void 0:f.split(","),C=parseFloat((i==null?void 0:i[0])||"0"),R=parseFloat((i==null?void 0:i[1])||"0"),l=new y({longitude:C,latitude:R,spatialReference:t==null?void 0:t.spatialReference}),G=L({geometry:l,symbol:new H({url:g("大用户表.png"),width:20,height:25,yoffset:12}),attributes:s}),b={id:s.userId,visible:!1,title:s.userName,x:l.x,y:l.y,offsetY:-40,attributes:{row:s,id:s.userId},customComponent:E(X),customConfig:{...s,extentTo:p},symbolConfig:{url:g("大用户表.png")}};return n.push(b),G}))||[];return r&&(o==null||o.removeAll(),o==null||o.addMany(m)),n},h=t=>o==null?void 0:o.graphics.find(r=>r.attributes.userId===t),c=()=>{o==null||o.removeAll()};return Y(()=>{c()}),{destroy:c,highlight:u,generatePops:U,getGraphic:h,_getLatestUserCoords:a,getLatestCHAOBIAORENYUANCoords:A,getLatestXunJianRenYuanCoords:N,getLatestQIANGXIURENYUANCoords:I}};export{Z as u};
