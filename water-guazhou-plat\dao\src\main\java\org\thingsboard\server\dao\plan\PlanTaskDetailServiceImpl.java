package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.plan.PlanTaskDetail;
import org.thingsboard.server.dao.sql.plan.PlanTaskDetailMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskDetailCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskDetailSaveRequest;

import java.util.List;

@Service
public class PlanTaskDetailServiceImpl implements PlanTaskDetailService {
    @Autowired
    private PlanTaskDetailMapper mapper;

    @Override
    public IPage<PlanTaskDetail> findAllConditional(PlanTaskDetailPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public List<PlanTaskDetail> saveAll(List<PlanTaskDetailSaveRequest> details) {
        return QueryUtil.saveOrUpdateBatchByRequest(details, mapper::saveAll, mapper::saveAll);
    }

    @Override
    public boolean complete(PlanTaskDetailCompleteRequest req) {
        return mapper.complete(req);
    }

    @Override
    public boolean deleteByMainId(String id) {
        return mapper.deleteByMainId(id) > 0;
    }

    @Override
    public boolean reset(String id) {
        return mapper.reset(id);
    }

    @Override
    public boolean removeAllByMainId(String id) {
        return deleteByMainId(id);
    }

    @Override
    public boolean update(PlanTaskDetail entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        return QueryUtil.deleteBatch(idList, mapper::deleteBatchIds);
    }

}
