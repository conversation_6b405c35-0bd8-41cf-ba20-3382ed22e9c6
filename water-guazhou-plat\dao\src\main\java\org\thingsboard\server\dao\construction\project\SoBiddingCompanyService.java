package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBiddingCompany;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingCompanyPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingCompanySaveRequest;

import java.util.List;

public interface SoBiddingCompanyService {
    /**
     * 分页条件查询项目招投标信息中的公司
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoBiddingCompany> findAllConditional(SoBiddingCompanyPageRequest request);

    /**
     * 保存公司到项目招投标信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoBiddingCompany save(SoBiddingCompanySaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoBiddingCompany entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    List<SoBiddingCompany> saveAll(List<SoBiddingCompanySaveRequest> entities);

    /**
     * 通过父级项目招投标id删除所有招投标公司
     *
     * @param id 唯一标识
     * @return 删除了多少个
     */
    int removeAllByBiddingId(String id);

}
