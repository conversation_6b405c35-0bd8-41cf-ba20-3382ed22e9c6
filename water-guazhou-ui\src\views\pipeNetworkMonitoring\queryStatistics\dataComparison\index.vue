<!--数据对比 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card-table"
      :title="state.activeName === 'list' ? '分析列表' : '分析曲线'"
    >
      <template #right>
        <el-radio-group v-model="state.activeName">
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px; font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px; font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div
        v-if="state.activeName === 'echarts'"
        ref="agriEcoDev"
        class="chart-box"
      >
        <!-- 图表模式 -->
        <VChart
          ref="refChart"
          :theme="appStore.isDark ? 'dark' : ''"
          :option="state.chartOption"
        ></VChart>
      </div>
      <!-- 列表模式 -->
      <div v-if="state.activeName === 'list'">
        <CardTable
          ref="refCardTable"
          class="cardTable"
          :config="cardTableConfig"
        />
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import elementResizeDetectorMaker from 'element-resize-detector'
import { Refresh, Search as SearchIcon, Download } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { lineOption } from '../echartsData/echart'
import { IECharts } from '@/plugins/echart'
import useStation from '@/hooks/station/useStation'
import { getDataCompare, exportDataCompare } from '@/api/pipeNetworkMonitoring/queryStatistics'
import useGlobal from '@/hooks/global/useGlobal'
import { formatColumn } from '@/utils/formartColumn'
import { useAppStore } from '@/store'
import { GetStationAttrs } from '@/api/shuiwureports/zhandian'

const appStore = useAppStore()
const { $messageWarning, $messageError } = useGlobal()
const erd = elementResizeDetectorMaker()
const { getStationTree, getStationTreeByDisabledType } = useStation()
const state = reactive<{
  type: 'date' | 'month' | 'year'
  chartOption: any
  activeName: string
  stationTree: any
  data: any
  checkedKeys: string[]
}>({
  type: 'date',
  chartOption: null,
  activeName: 'echarts',
  data: null,
  stationTree: [],
  checkedKeys: []
})

const refCardTable = ref<ICardTableIns>()
const refChart = ref<IECharts>()
const agriEcoDev = ref<any>()
const cardSearch = ref<ICardSearchIns>()
let tableData = reactive<any[]>([])

watch(
  () => state.activeName,
  () => {
    if (state.activeName === 'echarts') {
      refuseChart()
    }
  }
)

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    queryType: '15m',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().startOf('day').format(), dayjs().format()]
  },
  filters: [
    {
      type: 'select-tree',
      label: '监测点:',
      multiple: true,
      field: 'attributeId',
      clearable: false,
      showCheckbox: true,
      lazy: true,
      options: computed(() => state.stationTree) as any,
      lazyLoad: (node, resolve) => {
        if (node.level === 0) return resolve([])
        if (node.data.children?.length > 0) {
          return resolve(node.data.children)
        }
        if (node.isLeaf) return resolve([])
        if (node.data?.isLeaf) return resolve([])
        GetStationAttrs({ stationId: node.data.id }).then(res => {
          const newAttrs = res.data?.map(attr => {
            return {
              label: attr.type,
              value: '',
              id: '',
              children: attr.attrList.map(attr => {
                return {
                  label: attr.name,
                  value: attr.id,
                  id: attr.id,
                  isLeaf: true
                }
              })
            }
          })
          return resolve(newAttrs)
        })
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日', value: 'day' },
        { label: '月', value: 'month' },
        { label: '年', value: 'year' }
      ],
      label: '时间频率'
    },
    {
      type: 'datetimerange',
      label: '选择日期',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'year' || params.type === 'month'
      }
    },
    {
      type: 'monthrange',
      label: '选择日期',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        console.log(params.type)
        formItem.hidden = params.type === 'year' || params.type === 'day'
      }
    },
    {
      type: 'yearrange',
      label: '选择日期',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    },
    {
      type: 'select',
      label: '时间间隔:',
      field: 'queryType',
      clearable: false,
      options: [
        { label: '1 m', value: '1m' },
        { label: '5 m', value: '5m' },
        { label: '10 m', value: '10m' },
        { label: '15 m', value: '15m' },
        { label: '1小时', value: 'hour' }
      ],
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      },
      itemContainerStyle: {
        width: '180px'
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => {
            cardTableConfig.pagination.page = 1
            const queryParams = (cardSearch.value?.queryParams as any) || {}
            if (queryParams.attributeId && queryParams.attributeId.length > 0) {
              refreshData()
            } else $messageWarning('选择监测点')
          },
          svgIcon: shallowRef(SearchIcon)
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            cardSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          hide: () => {
            return state.activeName !== 'list'
          },
          svgIcon: shallowRef(Download),
          click: () => handleExport()
        }
      ]
    }
  ]
})
// 导出
const handleExport = () => {
  const queryParams = (cardSearch.value?.queryParams as any) || {}
  console.log(queryParams)
  const [start, end] = queryParams[queryParams.type] || []
  let startTime: any = 0
  let endTime: any = 0
  if (queryParams.type === 'day') {
    startTime = start ? dayjs(start).valueOf() : ''
    endTime = end ? dayjs(end).valueOf() : ''
  } else {
    startTime = start ? dayjs(start).startOf(queryParams.type).valueOf() : ''
    endTime = end ? dayjs(end).endOf(queryParams.type).valueOf() : ''
  }

  const params: any = {
    attributes: queryParams.attributeId.join(','),
    queryType: queryParams.type === 'month' ? 'day' : queryParams.type === 'year' ? 'month' : queryParams.queryType,
    start: startTime,
    end: endTime
  }
  exportDataCompare(params).then(res => {
    const url = window.URL.createObjectURL(res.data)
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', `数据对比表.xlsx`)
    document.body.appendChild(link)
    link.click()
  })
}
// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  pagination: {
    refreshData: ({ page, size }) => {
      cardTableConfig.pagination.page = page
      cardTableConfig.pagination.limit = size
      cardTableConfig.dataList = tableData?.slice((page - 1) * size, page * size)
    }
  }
})

// 刷新列表
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = (cardSearch.value?.queryParams as any) || {}
  console.log(queryParams)
  const [start, end] = queryParams[queryParams.type] || []
  let startTime: any = 0
  let endTime: any = 0
  if (queryParams.type === 'day') {
    startTime = start ? dayjs(start).valueOf() : ''
    endTime = end ? dayjs(end).valueOf() : ''
  } else {
    startTime = start ? dayjs(start).startOf(queryParams.type).valueOf() : ''
    endTime = end ? dayjs(end).endOf(queryParams.type).valueOf() : ''
  }

  const params: any = {
    attributes: queryParams.attributeId.join(','),
    queryType: queryParams.type === 'month' ? 'day' : queryParams.type === 'year' ? 'month' : queryParams.queryType,
    start: startTime,
    end: endTime
  }

  getDataCompare(params)
    .then(res => {
      const data = res.data?.data
      state.data = data
      const columns = formatColumn(data?.tableInfo)
      tableData = data?.tableDataList as any[]
      cardTableConfig.columns = columns
      cardTableConfig.dataList = tableData.slice((1 - 1) * 20, 20)
      cardTableConfig.pagination.total = data?.tableDataList.length
      cardTableConfig.loading = false
      refuseChart()
    })
    .catch(errors => {
      $messageError(errors)
    })
}

// 刷新图表
const resizeChart = () => {
  refChart.value?.resize()
}
// 配置加载图表数据
// 配置加载图表数据
const refuseChart = () => {
  const chartOption: any = lineOption()
  chartOption.series = []
  const serie = {
    name: '',
    smooth: true,
    data: [],
    type: 'line',
    markPoint: {
      data: [
        {
          type: 'max',
          name: '最大值',
          label: {
            fontSize: 12,
            color: appStore.isDark ? '#ffffff' : '#000000'
          }
        },
        {
          type: 'min',
          name: '最小值',
          label: {
            color: appStore.isDark ? '#ffffff' : '#000000'
          }
        }
      ]
    },
    markLine: {
      data: [{ type: 'average', name: '平均值' }]
    }
  }

  chartOption.xAxis.data = state.data?.tableDataList.map(table => table.ts)
  state.data?.tableInfo.map((info, index) => {
    if (info.columnValue !== 'ts') {
      const newSerie = JSON.parse(JSON.stringify(serie))
      newSerie.name = info.columnName
      newSerie.data = state.data?.tableDataList.map(table => table[info.columnValue])
      const name = info.columnName.split('--')[2] + (info.unit ? '(' + info.unit + ')' : '')
      if (index === 1) {
        chartOption.yAxis[0].name = name
        chartOption.yAxis[0].axisLabel.formatter = '{value} ' + info.unit ? info.unit : ''
      } else if (index > 1) {
        const yAxis = chartOption.yAxis.find(axis => axis.name === name)
        if (!yAxis) {
          console.log('ddd', index - 1)
          newSerie.yAxisIndex = index - 1
          chartOption.grid.right = 70 * (index - 1)
          chartOption.yAxis.push({
            position: 'right',
            alignTicks: true,
            type: 'value',
            name,
            offset: 70 * (index - 2),
            axisLine: {
              show: true,
              lineStyle: {
                types: 'solid'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#656b84' // 更改坐标轴文字颜色
              },
              formatter: '{value} ' + info.unit ? info.unit : ''
            },
            splitLine: {
              lineStyle: {
                color: appStore.isDark ? '#303958' : '#ccc',
                type: [5, 10],
                dashOffset: 5
              }
            }
          })
        }
      }
      chartOption.series.push(newSerie)
    }
  })
  refChart.value?.clear()
  nextTick(() => {
    if (agriEcoDev.value) {
      erd.listenTo(agriEcoDev.value, () => {
        state.chartOption = chartOption
        resizeChart()
      })
    }
  })
}
onBeforeMount(async () => {
  const type = ['流量监测站,测流压站'].join(',')
  const treeData = (await getStationTree(type)) as any[]
  await getStationTreeByDisabledType(treeData, ['Project', 'Station'], false, 'Station')
  // await getStationTreeByDisabledType(treeData, ['Project', 'Station'], true, 'Station')
  state.stationTree = treeData
  console.log(' state.stationTree ', state.stationTree)
})
</script>

<style lang="scss" scoped>
.card {
  height: calc(100% - 100px);
}

.cardTable {
  height: calc(100vh - 270px);
  width: 100%;
}

.chart-box {
  width: 100%;
  height: calc(100vh - 270px);
}
</style>
