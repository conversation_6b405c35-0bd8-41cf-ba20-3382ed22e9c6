/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.optionLog;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.optionLog.OptionLog;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
@Transactional
public class OptionLogServiceImpl implements OptionLogService {

    @Autowired
    private OptionLogDao optionLogDao;

    @Override
    public OptionLog save(OptionLog optionLog) {
        log.trace("Executing save [{}]", optionLog);
        return optionLogDao.save(optionLog);
    }

    @Override
    public List<OptionLog> getLogList(TenantId tenantId, String options, Long startTime, Long endTime) {
        log.trace("Executing getLogList tenantId [{}] options [{}]", tenantId, options);
        // 默认查询三天内的登录日志
        if (startTime == null && endTime == null) {
            startTime = new Date().getTime() - 1000 * 60 * 24 * 3;
            endTime = new Date().getTime();
        } else {
            if (startTime == null) {
                startTime = 0L;
            }
            if (endTime == null) {
                endTime = new Date().getTime();
            }
        }

        return optionLogDao.findByTenantIdAndOptions(tenantId, options, startTime, endTime);
    }
}
