package org.thingsboard.server.dao.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroupPartner;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupPartnerPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupPartnerSaveRequest;

import java.util.List;

public interface GuardGroupPartnerService {
    IPage<GuardGroupPartner> findAllConditional(GuardGroupPartnerPageRequest request);

    List<GuardGroupPartner> replaceAll(String groupId, List<GuardGroupPartnerSaveRequest> partners);

}
