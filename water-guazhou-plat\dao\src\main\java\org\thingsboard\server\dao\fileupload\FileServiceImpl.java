    package org.thingsboard.server.dao.fileupload;

    import org.springframework.beans.factory.annotation.Autowired;
    import org.springframework.beans.factory.annotation.Value;
    import org.springframework.stereotype.Service;
    import org.springframework.web.multipart.MultipartFile;
    import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
    import org.thingsboard.server.common.data.exception.ThingsboardException;
    import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsFileEntity;
    import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsFileRepository;

    import javax.servlet.http.HttpServletResponse;
    import java.io.File;
    import java.io.FileInputStream;
    import java.io.IOException;
    import java.io.OutputStream;
    import java.util.HashMap;
    import java.util.Map;
    import java.util.UUID;

    /**
     * @<NAME_EMAIL>
     * @since v1.0.0 2021-09-14
     */
    @Service
    public class FileServiceImpl implements FileService {

        @Autowired
        private AssetsFileRepository assetsFileRepository;


        @Value("${install.image_dir}")
        private String imageDir;

        @Value("${img.url}")
        private String imgUrl;

        @Override
        public Map uploadImage(MultipartFile file, String projectId) {

            Map result = new HashMap();
            // 文件名
            String originalFilename = file.getOriginalFilename();


            // 重新生成文件名
            String newFileName = UUID.randomUUID().toString().replace("-", "");
            String originFileName = file.getOriginalFilename();
            String suffix = originFileName.substring(originFileName.lastIndexOf("."));

            String distFileDirStr = imageDir + projectId + "/";
            File distFileDir = new File(distFileDirStr);
            if (!distFileDir.exists()) {
                distFileDir.mkdirs();
            }
            File distFile = new File(distFileDirStr + newFileName + suffix);
            try {
                file.transferTo(distFile);
            } catch (IOException e) {
                e.printStackTrace();
                result.put("code", 500);
                result.put("msg", e.getMessage());

                return result;
            }

            result.put("code", 0);
            result.put("fileName", imgUrl + "?fileName=" + newFileName + suffix + "&projectId=" + projectId);
            return result;
        }

        @Override
        public Map getImgByName(String fileName, String projectId, HttpServletResponse response) {
            Map result = new HashMap();

            FileInputStream is = null;
            OutputStream outputStream = null;
            try {
                File file = new File(imageDir + projectId + "/" + fileName);
                if (!file.exists()) {
                    result.put("code", 500);
                    result.put("msg", "该资源不存在");

                    return result;
                }
                is = new FileInputStream(file);

                outputStream = response.getOutputStream();

                int len = 0;
                byte[] b = new byte[1024];
                while ((len = is.read(b)) != -1) {
                    outputStream.write(b, 0, len);
                }
                outputStream.flush();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (is != null) {
                    try {
                        is.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

            result.put("code", 0);
            result.put("msg", "ok");
            return result;
        }

        /**
         * 根据文件名和项目id删除文件
         * @param name
         * @param projectId
         */
        @Override
        public void deleteImgByNameAndProjectId(String name, String projectId) {
            File file = new File(imageDir + projectId + "/" + name);
            if (file.exists()) {
                file.delete();
            }
        }

        @Override
        public AssetsFileEntity uploadFile(MultipartFile file, String projectId, String tenantId) throws ThingsboardException {
            Map result = new HashMap();
            // 文件名
            String originFileName = file.getOriginalFilename();
            AssetsFileEntity fileEntity = new AssetsFileEntity();
            fileEntity.setName(originFileName);
            fileEntity.setTenantId(tenantId);
            fileEntity.setProjectId(projectId);
            fileEntity.setCreateTime(System.currentTimeMillis());

            assetsFileRepository.save(fileEntity);
            // 重新生成文件名
            String newFileName = fileEntity.getId();
            String suffix = originFileName.substring(originFileName.lastIndexOf("."));

            String distFileDirStr = imageDir + projectId + "/";
            File distFileDir = new File(distFileDirStr);
            if (!distFileDir.exists()) {
                distFileDir.mkdirs();
            }
            File distFile = new File(distFileDirStr + newFileName + suffix);
            try {
                file.transferTo(distFile);
            } catch (IOException e) {
                e.printStackTrace();
                result.put("code", 500);
                result.put("msg", e.getMessage());

                throw new ThingsboardException("保存文件失败", ThingsboardErrorCode.GENERAL);
            }
            fileEntity.setUrl(imgUrl.replace("getImgByName", "viewFile") + "?fileName=" + fileEntity.getId() + fileEntity.getName().substring(fileEntity.getName().lastIndexOf(".")) + "&projectId=" + fileEntity.getProjectId());
            return fileEntity;
        }

        @Override
        public Map viewFile(File pdfFile, HttpServletResponse response) {
            Map result = new HashMap();

            FileInputStream is = null;
            OutputStream outputStream = null;
            try {
                is = new FileInputStream(pdfFile);

                outputStream = response.getOutputStream();

                int len = 0;
                byte[] b = new byte[1024];
                while ((len = is.read(b)) != -1) {
                    outputStream.write(b, 0, len);
                }
                outputStream.flush();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (is != null) {
                    try {
                        is.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

            result.put("code", 0);
            result.put("msg", "ok");
            return result;
        }

        @Override
        public void deleteByIds(String[] ids) {
            File file;
            for (String id : ids) {
                AssetsFileEntity one = assetsFileRepository.findOne(id);
                if (one != null) {
                    try {
                        file = new File(imageDir + one.getProjectId() + "/" + id + one.getName().substring(one.getName().lastIndexOf(".")));
                        file.delete();
                    } catch (Exception e) {
                        e.printStackTrace();
                        continue;
                    }
                }
            }
        }
    }
