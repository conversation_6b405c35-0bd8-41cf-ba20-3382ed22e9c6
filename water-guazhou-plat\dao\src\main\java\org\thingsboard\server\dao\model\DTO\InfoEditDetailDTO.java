package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 信息修改详情
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-03-01
 */
@Data
@NoArgsConstructor
public class InfoEditDetailDTO {

    private String name;

    private Object oldValue;

    private Object newValue;

    private String remark;

    private String createTime;

    public InfoEditDetailDTO(String name, Object oldValue, Object newValue, String remark, Date createTime) {
        this.name = name;
        this.oldValue = oldValue == null ? "" : oldValue;
        this.newValue = newValue == null ? "" : newValue;
        this.remark = remark;
        this.createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(createTime);
    }
}
