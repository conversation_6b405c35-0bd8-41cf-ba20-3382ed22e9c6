package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSystemInfo;

@Mapper
public interface SsPortalSystemInfoMapper extends BaseMapper<SsPortalSystemInfo> {
    SsPortalSystemInfo getByTenantId(String tenantId);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalSystemInfo entity);

    boolean updateFully(SsPortalSystemInfo entity);

    boolean save(SsPortalSystemInfo entity);

}
