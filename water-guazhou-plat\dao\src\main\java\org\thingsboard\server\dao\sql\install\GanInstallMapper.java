package org.thingsboard.server.dao.sql.install;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.GanInstallDTO;
import org.thingsboard.server.dao.model.sql.install.GanInstall;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-07-24
 */
@Mapper
public interface GanInstallMapper extends BaseMapper<GanInstall> {

    IPage<GanInstall> getList(IPage<GanInstall> page, @Param("param") GanInstallDTO userId);
}
