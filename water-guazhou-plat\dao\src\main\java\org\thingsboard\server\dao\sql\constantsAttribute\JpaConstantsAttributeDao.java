/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.constantsAttribute;

import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.constantsAttribute.ConstantsAttribute;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.constantsAttribute.ConstantsAttributeDao;
import org.thingsboard.server.dao.model.sql.ConstantsAttributeEntity;
import org.thingsboard.server.dao.sql.JpaAbstractSearchTextDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;


@Component
@SqlDao
public class JpaConstantsAttributeDao extends JpaAbstractSearchTextDao<ConstantsAttributeEntity, ConstantsAttribute> implements ConstantsAttributeDao {


    @Autowired
    private ConstantsAttributeRepository constantsAttributeRepository;

    @Override
    protected Class getEntityClass() {
        return ConstantsAttributeEntity.class;
    }

    @Override
    protected CrudRepository getCrudRepository() {
        return constantsAttributeRepository;
    }

    @Override
    public ListenableFuture<List<ConstantsAttribute>> getConstantsAttributeByType(String type) {
        return service.submit(() -> DaoUtil.convertDataList(constantsAttributeRepository.findByType(type)));
    }

    @Override
    public ListenableFuture<List<ConstantsAttribute>> getAll() {
        return service.submit(() -> find());
    }

    @Override
    public ListenableFuture<List<ConstantsAttribute>> getConstantsAttributeByKey(String key) {
        return service.submit(() -> DaoUtil.convertDataList(constantsAttributeRepository.findByKey(key)));
    }

    @Override
    public ListenableFuture<List<ConstantsAttribute>> getConstantsAttributeByTypeAndKey(String type, String key) {
        return service.submit(() -> DaoUtil.convertDataList(constantsAttributeRepository.findByTypeaAndKey(type, key)));
    }
}
