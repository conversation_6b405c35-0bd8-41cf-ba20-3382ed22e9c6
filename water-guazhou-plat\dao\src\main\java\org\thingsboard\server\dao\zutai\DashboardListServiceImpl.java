package org.thingsboard.server.dao.zutai;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.zutai.DashboardListEntity;
import org.thingsboard.server.dao.model.sql.zutai.ZutaiDashboardEntity;
import org.thingsboard.server.dao.sql.zutai.DashboardListRepository;
import org.thingsboard.server.dao.sql.zutai.ZutaiDashboardRepository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
@Service
public class DashboardListServiceImpl implements DashboardListService {
    @Autowired
    private DashboardListRepository dashboardListRepository;
    @Autowired
    private ZutaiDashboardRepository zutaiDashboardRepository;

    @Override
    public PageData getList(Map map) {
        String id = (String) map.get("id");
        String name = (String) map.get("name");
        String skip = (String) map.get("skip");
        String projectId = (String) map.get("projectId");
        String tenantId = (String) map.get("tenantId");
        String page = (String) map.get("page");
        String size = (String) map.get("size");

        if (StringUtils.isBlank(id)) {
            id = "";
        }
        if (StringUtils.isBlank(name)) {
            name = "";
        }
        if (StringUtils.isBlank(skip)) {
            skip = "0";
        }

        if (StringUtils.isBlank(size)) {
            size = "10";
        }

        if (StringUtils.isNotBlank(page)) {
            skip = ((Integer.valueOf(page) - 1) * Integer.valueOf(size)) + "";
        }

        if (StringUtils.isBlank(projectId)) {
            projectId = "";
        }

        id = "%" + id + "%";
        name = "%" + name + "%";
        projectId = "%" + projectId + "%";

        List<DashboardListEntity> dashboardListEntities = dashboardListRepository.findList(id, name, projectId, tenantId, Integer.valueOf(skip), Integer.valueOf(size));

        int total = dashboardListRepository.findListCount(id, name, projectId, tenantId);

        PageData pageData = new PageData(total, dashboardListEntities);
        return pageData;
    }

    @Override
    public DashboardListEntity save(DashboardListEntity dashboardListEntity) {

        // 是否新增
        boolean flag = false;
        if (StringUtils.isBlank(dashboardListEntity.getId())) {
            dashboardListEntity.setCreateTime(new Date());
            flag = true;
        }
        if (StringUtils.isBlank(dashboardListEntity.getProjectId())) {
            dashboardListEntity.setProjectId("");
        }


        DashboardListEntity save = dashboardListRepository.save(dashboardListEntity);
        if (flag) {
            ZutaiDashboardEntity entity = new ZutaiDashboardEntity();
            entity.setData("{\"pageConfig\":{\"width\":800,\"height\":600,\"backgroundColor\":\"#ffffff\"},\"assets\":[],\"panels\":[]}");
            entity.setId(save.getDashboardId());
            entity.setCreateTime(new Date());
            entity.setProjectId(dashboardListEntity.getProjectId());
            entity.setTenantId(dashboardListEntity.getTenantId());

            zutaiDashboardRepository.save(entity);
        }

        return save;
    }

    @Override
    public void delete(String id) {
        dashboardListRepository.delete(id);
    }
}
