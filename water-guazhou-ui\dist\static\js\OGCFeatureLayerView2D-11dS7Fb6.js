import{e as t,y as o,a as p}from"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import a from"./FeatureLayerView2D-B3SklxDn.js";import"./MapView-DaoQedLH.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./LayerView-BSt9B8Gh.js";import"./schemaUtils-DLXXqxNF.js";import"./enums-L38xj_2E.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./number-CoJp78Rz.js";import"./utils-DPUVnAXL.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./cimAnalyzer-CMgqZsaO.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./ExpandedCIM-C1laM-_7.js";import"./util-DPgA-H2V.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./popupUtils-BjdidZV3.js";import"./RefreshableLayerView-DUeNHzrW.js";const s=i=>{let r=class extends i{get availableFields(){return this.layer.fieldsIndex.fields.map(m=>m.name)}};return t([o()],r.prototype,"layer",void 0),t([o({readOnly:!0})],r.prototype,"availableFields",null),r=t([p("esri.views.layers.OGCFeatureLayerView")],r),r};let e=class extends s(a){supportsSpatialReference(i){return this.layer.serviceSupportsSpatialReference(i)}};e=t([p("esri.views.2d.layers.OGCFeatureLayerView2D")],e);const M=e;export{M as default};
