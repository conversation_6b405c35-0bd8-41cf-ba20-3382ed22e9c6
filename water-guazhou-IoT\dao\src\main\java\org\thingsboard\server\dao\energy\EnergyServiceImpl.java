/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.energy;

import com.google.common.util.concurrent.ListenableFuture;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.energy.Energy;
import org.thingsboard.server.common.data.id.EnergyId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.entity.AbstractEntityService;
import org.thingsboard.server.dao.feignService.ProjectRelationApi;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
public class EnergyServiceImpl extends AbstractEntityService implements EnergyService {

    @Autowired
    private EnergyDao energyDao;

    @Autowired
    private ProjectRelationApi projectRelationApi;

    @Override
    public Energy saveEnergy(Energy energy) {
        return energyDao.save(energy);
    }

    @Override
    public Object saveEnergyList(List<Energy> energyList) {
        List<Energy> result = new ArrayList<>();
        try {
            energyList.forEach(energy -> {
                result.add(saveEnergy(energy));
                // 更新资源项目关系
                if (StringUtils.isNotBlank(energy.getProjectId())) {
                    projectRelationApi.mountEntityToProject(DataConstants.ProjectRelationEntityType.ENERGY.name(),
                            energy.getProjectId(),
                            Collections.singletonList(UUIDConverter.fromTimeUUID(UUID.fromString(energy.getId().toString()))));
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 删除能源时删除掉关系中的所有关系表
     *
     * @param energyId
     * @return
     */
    @Override
    public boolean removeEnergy(TenantId tenantId,EnergyId energyId) {
        deleteEntityRelations(tenantId,energyId);
        // 更新资源项目关系
        projectRelationApi.mountEntityToProject(DataConstants.ProjectRelationEntityType.ENERGY.name(),
                "",
                Collections.singletonList(UUIDConverter.fromTimeUUID(UUID.fromString(energyId.toString()))));
        return energyDao.removeById(energyId.getId());
    }

    @Override
    public ListenableFuture<List<Energy>> findByTenantId(TenantId tenantId) {
        return energyDao.findByTenantId(tenantId);
    }

    @Override
    public ListenableFuture<Energy> findById(TenantId tenantId,EnergyId energyId) {
        return energyDao.findByIdAsync(tenantId,energyId.getId());
    }

    @Override
    public ListenableFuture<Energy> findById(EnergyId energyId) {
        return energyDao.findById(energyId);
    }
}
