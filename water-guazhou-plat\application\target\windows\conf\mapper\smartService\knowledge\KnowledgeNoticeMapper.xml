<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.knowledge.KnowledgeNoticeMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeNotice">
        select a.*, b.first_name as creatorName
        from tb_service_knowledge_notice a left join tb_user b on a.creator = b.id
        where a.tenant_id = #{tenantId}
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
        order by a.create_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_knowledge_notice a left join tb_user b on a.creator = b.id
        where a.tenant_id = #{tenantId}
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
    </select>

    <select id="getAll" resultType="org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeNotice">
        select a.id, type, start_time, end_time, recovery_time, title, area, create_time, update_time, tenant_id, creator
        from postgres.public.tb_service_knowledge_notice a
        where a.tenant_id = #{tenantId}
        order by a.create_time desc
    </select>
</mapper>