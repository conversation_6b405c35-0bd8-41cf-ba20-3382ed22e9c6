<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.district.CircuitDistrictAreaMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           type,
                           name,
<!--                           points,-->
                           buffer,
                           district_id,
                           tenant_id<!--@sql from sm_circuit_district_area -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrictArea">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
<!--        <result column="points" property="points"/>-->
        <result column="district_id" property="districtId"/>
        <result column="buffer" property="buffer"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findAllByDistrictId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_circuit_district_area
        where district_id = #{id}
    </select>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_circuit_district_area
        <where>
            <if test="districtId != null">
                and district_id = #{districtId}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update sm_circuit_district_area
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="points != null">
                points = #{points},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="districtId != null">
                district_id = #{districtId},
            </if>
            <if test="buffer != null">
                buffer = #{buffer},
            </if>
        </set>
        where id = #{id}
        <if test="districtId != null">
            and sm_circuit_district_can_add_area(#{districtId})
        </if>
    </update>

    <select id="isValidDistrictId" resultType="boolean">
        select sm_circuit_district_can_add_area(#{districtId})
    </select>

    <select id="getPoints" resultType="java.lang.String">
        select points
        from sm_circuit_district_area
        where id = #{id}
    </select>
</mapper>