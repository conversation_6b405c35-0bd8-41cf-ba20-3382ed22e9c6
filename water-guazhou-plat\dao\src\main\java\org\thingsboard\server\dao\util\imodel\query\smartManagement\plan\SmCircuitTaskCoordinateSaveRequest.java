package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SmCircuitTaskCoordinate;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SmCircuitTaskCoordinateSaveRequest extends SaveRequest<SmCircuitTaskCoordinate> {
    @NotNullOrEmpty
    private String taskCode;

    @NotNullOrEmpty
    private Float x;

    @NotNullOrEmpty
    private Float y;


    @Override
    protected SmCircuitTaskCoordinate build() {
        SmCircuitTaskCoordinate entity = new SmCircuitTaskCoordinate();
        entity.setCreator(currentUserUUID());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SmCircuitTaskCoordinate update(String id) {
        disallowUpdate();
        SmCircuitTaskCoordinate entity = new SmCircuitTaskCoordinate();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SmCircuitTaskCoordinate entity) {
        entity.setTaskCode(taskCode);
        entity.setX(x);
        entity.setY(y);
        entity.setCreateTime(createTime());
    }

}