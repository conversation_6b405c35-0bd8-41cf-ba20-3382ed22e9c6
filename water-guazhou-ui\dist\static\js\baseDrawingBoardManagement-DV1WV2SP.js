import{_ as C}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as k}from"./CardSearch-CB_HNR-Q.js";import{z as u,C as v,c as _,r as p,b as i,S as T,o as q,g as L,n as V,q as g}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function F(n){return u({url:"/api/base/drawing/management/list",method:"get",params:n})}function M(n){return u({url:"/api/base/drawing/management/getDetail",method:"get",params:{id:n}})}function y(n){return u({url:"/api/base/drawing/management/add",method:"post",data:n})}function w(n){return u({url:"/api/base/drawing/management/edit",method:"post",data:n})}function P(n){return u({url:"/api/base/drawing/management/deleteIds",method:"delete",data:n})}const z={class:"wrapper"},E={__name:"baseDrawingBoardManagement",setup(n){const m=_(),c=_(),D=p({labelWidth:"100px",filters:[{type:"input",label:"工艺图名称",field:"name",placeholder:"请输入工艺图名称",onChange:()=>l()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>l()},{perm:!0,type:"primary",text:"新增",click:()=>h()},{perm:!0,type:"danger",text:"批量删除",click:()=>b()}]}],defaultParams:{}}),o=p({columns:[{label:"工艺图名称",prop:"name"},{label:"描述",prop:"description"},{label:"版本",prop:"version"},{label:"状态",prop:"status"},{label:"工艺图文件路径",prop:"url"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>x(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>h(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>b(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{o.pagination.page=e,l()},handleSize:e=>{o.pagination.limit=e,l()}},handleSelectChange:e=>{o.selectList=e||[]}}),a=p({title:"新增工艺图",group:[{fields:[{type:"input",label:"工艺图名称",field:"name",rules:[{required:!0,message:"请输入工艺图名称"}]},{type:"input",label:"描述",field:"description",rules:[{required:!0,message:"请输入描述"}]},{type:"input",label:"版本",field:"version",rules:[{required:!0,message:"请输入版本"}]},{type:"input",label:"状态",field:"status",rules:[{required:!0,message:"请输入状态"}]},{type:"input",label:"工艺图文件路径",field:"url",rules:[{required:!0,message:"请输入工艺图文件路径"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var t;try{e.id?(await w(e),i.success("修改成功")):(await y(e),i.success("新增成功")),(t=c.value)==null||t.closeDialog(),l()}catch{i.error("操作失败")}}}),f=()=>{a.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),a.showSubmit=!0,a.showCancel=!0,a.cancelText="取消",a.submitText="确定",a.submit=async e=>{var t;try{e.id?(await w(e),i.success("修改成功")):(await y(e),i.success("新增成功")),(t=c.value)==null||t.closeDialog(),l()}catch{i.error("操作失败")}}},x=async e=>{var t,r;try{const s=await M(e.id),d=((t=s.data)==null?void 0:t.data)||s;f(),a.title="工艺图详情",a.defaultValue={...d},a.group[0].fields.forEach(B=>{B.disabled=!0}),a.showSubmit=!1,a.cancelText="关闭",(r=c.value)==null||r.openDialog()}catch{i.error("获取详情失败")}},h=e=>{var t;f(),e?(a.title="编辑工艺图",a.defaultValue={...e}):(a.title="新增工艺图",a.defaultValue={}),(t=c.value)==null||t.openDialog()},b=async e=>{try{const t=e?[e.id]:o.selectList.map(r=>r.id);if(!t.length){i.warning("请选择要删除的数据");return}await T("确定要删除选中的数据吗？"),await P(t),i.success("删除成功"),l()}catch(t){t!=="cancel"&&i.error("删除失败")}},l=async()=>{var e,t;try{const r=await F({page:o.pagination.page,size:o.pagination.limit,...((e=m.value)==null?void 0:e.queryParams)||{}}),s=((t=r.data)==null?void 0:t.data)||r;o.dataList=s.records||s,o.pagination.total=s.total||s.length||0}catch{i.error("数据加载失败")}};return q(()=>{l()}),(e,t)=>{const r=k,s=S,d=C;return L(),V("div",z,[g(r,{ref_key:"refSearch",ref:m,config:D},null,8,["config"]),g(s,{class:"card-table",config:o},null,8,["config"]),g(d,{ref_key:"refDialogForm",ref:c,config:a},null,8,["config"])])}}},G=v(E,[["__scopeId","data-v-761377de"]]);export{G as default};
