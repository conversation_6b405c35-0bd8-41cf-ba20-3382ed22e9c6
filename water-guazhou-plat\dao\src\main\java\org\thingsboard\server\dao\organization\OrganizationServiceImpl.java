package org.thingsboard.server.dao.organization;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.DTO.TreeNodeDTO;
import org.thingsboard.server.dao.model.sql.department.Department;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.sql.department.OrganizationMapper;
import org.thingsboard.server.dao.sql.user.UserMapper;
import org.thingsboard.server.dao.util.TreeUtil;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.organization.OrganizationPageRequest;
import org.thingsboard.server.dao.util.imodel.query.organization.OrganizationSaveRequest;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OrganizationServiceImpl implements OrganizationService {
    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public List<Organization> findAllConditional(OrganizationPageRequest request) {
        return organizationMapper.findAll(request);
    }

    @Override
    public List<Organization> findAllStructure(Integer depth, String tenantId) {
        if (depth > 2)
            return QueryUtil.buildMixTree(organizationMapper.findRoots(tenantId),
                    pid -> organizationMapper.findChildren(pid, tenantId),
                    pid -> departmentMapper.findChildren(pid, tenantId),
                    userMapper::findByDepartmentId);
        if (depth == 2)
            return QueryUtil.buildMixTree(organizationMapper.findRoots(tenantId),
                    pid -> organizationMapper.findChildren(pid, tenantId),
                    pid -> departmentMapper.findChildren(pid, tenantId));
        else if (depth > 0)
            return QueryUtil.buildTree(organizationMapper.findRoots(tenantId), pid -> organizationMapper.findChildren(pid, tenantId));

        return Collections.emptyList();
    }

    @Override
    public Organization save(OrganizationSaveRequest request) {
        return QueryUtil.saveOrUpdateOneByRequest(request, organizationMapper::insert, organizationMapper::update);
    }

    @Override
    public boolean delete(String id) {
        return organizationMapper.deleteById(id) > 0;
    }

    @Override
    public boolean update(Organization entity) {
        return organizationMapper.update(entity);
    }

    @Override
    public boolean canBeDelete(String id) {
        return organizationMapper.canBeDelete(id);
    }

    @Override
    public boolean canBeAdd(String parentId) {
        return organizationMapper.canBeAdd(parentId);
    }

    @Override
    public Organization initRootOrganization(String name, String type, String phone, String tenantId) {
        OrganizationSaveRequest req = new OrganizationSaveRequest();
        req.setName(name);
        req.setType(type);
        req.tenantId(tenantId);
        req.setOrderNum(1);
        req.setPhone(phone);
        return save(req);
    }

    @Override
    // 移除当前客户下的所有单位
    public boolean removeRootOrganization(String tenantId) {
        return departmentMapper.removeAllDepartment(tenantId) &&
                organizationMapper.removeAllOrganization(tenantId);
    }

    @Override
        public List<String> findAllChildrenByParentId(String pid, TenantId tenantId) {
        List<TreeNodeDTO> tree = this.findTree("0", tenantId);

        List<TreeNodeDTO> result = new ArrayList<>();
        for (TreeNodeDTO node : tree) {
            setChildren(node, result, pid);
        }

        return result.stream().map(TreeNodeDTO::getId).distinct().collect(Collectors.toList());
    }

    private void setChildren(TreeNodeDTO node, List<TreeNodeDTO> result, String pid) {
        List<TreeNodeDTO> children = node.getChildren();
        if (children != null && children.size() > 0) {
            if (node.getId().equals(pid)) {
                result.addAll(children);
                for (TreeNodeDTO child : children) {
                    setChildren(child, result, child.getId());
                }
            } else {
                for (TreeNodeDTO child : children) {
                    setChildren(child, result, pid);
                }
            }

        }

    }

    /**
     * 查询供水单位结构
     *
     * @param pid      父ID
     * @param tenantId 租户ID
     * @return 树
     */
    private List<TreeNodeDTO> findTree(String pid, TenantId tenantId) {
        List<TreeNodeDTO> treeNodeList = new ArrayList<>();
        // 查询供水列表
        QueryWrapper<Organization> organizationQueryWrapper = new QueryWrapper<>();
        organizationQueryWrapper.eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId()));
        List<Organization> organizations = organizationMapper.selectList(organizationQueryWrapper);
        for (Organization organization : organizations) {
            TreeNodeDTO node = TreeNodeDTO.builder()
                    .nodeDetail(organization)
                    .id(organization.getId())
                    .parentId(organization.getParentId() == null ? "0" : organization.getParentId())
                    .name(organization.getName())
                    .typeName("供水单位").build();
            treeNodeList.add(node);
        }

        // 查询部门列表
        /*QueryWrapper<Department> departmentQueryWrapper = new QueryWrapper<>();
        departmentQueryWrapper.eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId()));
        List<Department> departments = departmentMapper.selectList(departmentQueryWrapper);
        for (Department department : departments) {
            TreeNodeDTO node = TreeNodeDTO.builder()
                    .nodeDetail(department)
                    .id(department.getId())
                    .parentId(department.getParentId())
                    .name(department.getName())
                    .typeName("部门").build();
            treeNodeList.add(node);
        }*/

        // 转换为树

        return TreeUtil.listToTree(treeNodeList, pid);
    }


}
