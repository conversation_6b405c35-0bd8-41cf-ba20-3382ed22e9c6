import{d as V,c as h,r as _,am as W,l as b,bJ as F,bI as x,o as j,ay as N,bo as q,i as m,g as A,h as B,F as L,q as v,p as $,j as O,br as z,C as G}from"./index-r0dFAfgr.js";import{_ as M}from"./TreeBox-DDD2iwoR.js";import{_ as H}from"./Search-NSrhrIa_.js";import{_ as R}from"./index-BJ-QPYom.js";import{u as J}from"./useDetector-BRcb7GRN.js";import"./index-0NlGN6gS.js";import{G as U}from"./proSale-DWRhGXcG.js";const Y=V({__name:"chartView",props:{partitions:{}},setup(I){const g=h(),c=I,f=h(),D=h(),u=_({chartOption:null});W(()=>c.partitions,()=>{r.data=c.partitions||[]});const r=_({data:c.partitions||[],title:"区域划分",treeNodeHandleClick:e=>{r.currentProject=e,y()}}),S=(e,s,o)=>o.hidden=e.type!==o.field,P=_({defaultParams:{type:"year",year:b().format(F),month:[b().startOf("y").format(x),b().format(x)]},filters:[{type:"radio-button",field:"type",options:[{label:"按年",value:"year"},{label:"按年月",value:"month"}],label:"选择方式"},{handleHidden:S,type:"year",label:"",field:"year",clearable:!1,disabledDate(e){return new Date<e}},{handleHidden:S,type:"monthrange",label:"",field:"month",clearable:!1,format:x,disabledDate(e){return new Date<e}},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>y()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var e;(e=f.value)==null||e.resetForm()}}]}]});function w(e=[],s=[],o=[],i=[],a=[],p=[]){var l;return{title:{text:(((l=r.currentProject)==null?void 0:l.label)||"")+" 分析图",textStyle:{fontSize:16,color:"#318DFF"},left:40,top:10},grid:{left:50,right:50,top:80,bottom:80},legend:{type:"scroll",width:500,textStyle:{color:"#666",fontSize:12},top:10},tooltip:{trigger:"axis",formatter:t=>{var C;let k=(C=t[0])==null?void 0:C.axisValue;return t.map(d=>{k+=`
          <br />
          ${d.marker} ${d.seriesName} ${d.value+(d.componentSubType==="line"?" %":" m³")}
`}),k}},xAxis:{type:"category",boundaryGap:!0,data:e},dataZoom:[{type:"inside",start:0,end:100},{start:0,end:100}],yAxis:[{position:"left",type:"value",name:"m³",axisLine:{show:!0},axisLabel:{show:!0,textStyle:{color:"#656b84"}},splitLine:{lineStyle:{color:O().isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}},{position:"right",type:"value",name:"%",axisLine:{show:!0},axisLabel:{show:!0},splitLine:{lineStyle:{color:O().isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}}],series:[{name:"产销差",smooth:!0,data:s,type:"line",yAxisIndex:1,itemStyle:{color:"#318DFF"}},{name:"供水总量",data:o,type:"bar",barWidth:5},{name:"用户抄见量",data:i,type:"bar",barWidth:5},{name:"进水量",data:a,type:"bar",barWidth:5},{name:"出水量",data:p,type:"bar",barWidth:5}]}}const y=async()=>{var e,s,o,i;try{const a=((e=f.value)==null?void 0:e.queryParams)||{},n=(await U({type:a.type,date:a.type==="year"?a.year:void 0,partitionId:(s=r.currentProject)==null?void 0:s.value,start:a.type==="month"?(o=a.month)==null?void 0:o[0]:void 0,end:a.type==="month"?(i=a.month)==null?void 0:i[1]:void 0})).data.data,l=w(n.map(t=>t.partitionId),n.map(t=>t.correctNrwRate),n.map(t=>t.supplyTotal),n.map(t=>t.correctUseWater),n.map(t=>t.inWater),n.map(t=>t.outWater));u.chartOption=l}catch{u.chartOption=w()}},T=J();return j(async()=>{r.data=c.partitions||[],r.currentProject=r.data[0],y(),T.listenToMush(D.value,()=>{var e;(e=g.value)==null||e.resize()})}),(e,s)=>{const o=R,i=H,a=N("VChart"),p=M,n=z;return q((A(),B(p,{class:"bg-wrapper"},{tree:L(()=>[v(o,{ref:"refTree","tree-data":m(r)},null,8,["tree-data"])]),default:L(()=>[v(i,{ref_key:"refSearch",ref:f,config:m(P)},null,8,["config"]),$("div",{ref_key:"refDiv",ref:D,class:"card-ehcarts"},[v(a,{ref_key:"refChart",ref:g,option:m(u).chartOption},null,8,["option"])],512)]),_:1})),[[n,!!m(r).loading]])}}}),ae=G(Y,[["__scopeId","data-v-b2d2b974"]]);export{ae as default};
