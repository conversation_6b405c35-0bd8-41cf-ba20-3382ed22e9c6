package org.thingsboard.server.dao.maintainCircuit.maintain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskC;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskM;
import org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainTaskCMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainTaskMMapper;
import org.thingsboard.server.dao.util.imodel.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
public class MaintainTaskCServiceImpl implements MaintainTaskCService {

    @Autowired
    private MaintainTaskCMapper maintainTaskCMapper;

    @Autowired
    private MaintainTaskMMapper maintainTaskMMapper;

    @Override
    public void save(MaintainTaskC maintainTaskC) {
        maintainTaskC.setTime(new Date());
        maintainTaskCMapper.updateById(maintainTaskC);
        // 是否全部完成
        Map queryMap = new HashMap<>();
        queryMap.put("main_id", maintainTaskC.getMainId());
        List<MaintainTaskC> list = maintainTaskCMapper.selectByMap(queryMap);
        boolean isComplete = true;
        for (MaintainTaskC maintainTaskC1 : list) {
            if ("0".equals(maintainTaskC1.getStatus())) {
                isComplete = false;
            }
        }
        // 是否第一次，第一次则设置实际开始时间
        MaintainTaskM maintainTaskM = maintainTaskMMapper.selectById(maintainTaskC.getMainId());
        if (maintainTaskM != null) {
            if (maintainTaskM.getRealStartTime() == null) {
                maintainTaskM.setRealStartTime(new Date());
            }
            // 接收任务
            maintainTaskM.setStatus("1");

            // 全部完成  任务状态 0 待接收 1已接收 2按时完成 3超时完成 4未完成
            if (true == isComplete) {
                // 是否按时完成
                maintainTaskM.setRealEndTime(new Date());
                if (maintainTaskM.getEndTime() != null && maintainTaskM.getEndTime().getTime() < maintainTaskM.getRealEndTime().getTime()) {
                    maintainTaskM.setStatus("3");
                } else {
                    maintainTaskM.setStatus("2");
                }
            }
            maintainTaskMMapper.updateById(maintainTaskM);
        }
    }

    @Override
    public Map statistics(String deviceLabelCode) {

        Map result = new HashMap();
        // 保养次数
        int count = maintainTaskCMapper.countByLabelCode(deviceLabelCode);
        result.put("count", count);
        // 最近维修
        result.put("latestMaintainTime", "-");
        MaintainTaskC maintainTaskC = maintainTaskCMapper.selectLatestByDeviceLabelCode(deviceLabelCode);
        if (maintainTaskC != null) {
            result.put("latestMaintainTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(maintainTaskC.getCreateTime()));
        }

        // 今年保养情况
        Date nowYear = new Date();
        SimpleDateFormat yyyyFormat = new SimpleDateFormat("yyyy");
        try {
            nowYear = yyyyFormat.parse(yyyyFormat.format(new Date()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<Map> nowYearRepair = maintainTaskCMapper.getNowYearMaintainByDeviceLabelCode(deviceLabelCode, nowYear);
        result.put("nowYearMaintain", nowYearRepair);

        return result;
    }

    @Override
    public boolean checkUser(String mainId, String userId) {
        MaintainTaskM maintainTaskM = maintainTaskMMapper.selectById(mainId);
        if (maintainTaskM == null || StringUtils.isNullOrEmpty(maintainTaskM.getUserId()) || !maintainTaskM.getUserId().contains(userId)) {
            return false;
        }
        return true;
    }

}
