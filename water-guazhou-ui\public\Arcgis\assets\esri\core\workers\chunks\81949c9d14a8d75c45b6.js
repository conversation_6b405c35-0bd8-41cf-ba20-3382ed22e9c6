"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[7014,5660],{25929:(e,t,s)=>{s.d(t,{M:()=>f,a:()=>m,e:()=>g,f:()=>n,i:()=>c,p:()=>d,r:()=>a,t:()=>o,w:()=>h});var r=s(70586),i=s(17452);function n(e,t){const s=t&&t.url&&t.url.path;if(e&&s&&(e=(0,i.hF)(e,s,{preserveProtocolRelative:!0}),t.portalItem&&t.readResourcePaths)){const s=(0,i.PF)(e,t.portalItem.itemUrl);null!=s&&u.test(s)&&t.readResourcePaths.push(t.portalItem.resourceFromPath(s).path)}return p(e,t&&t.portal)}function o(e,t,s=f.YES){if(null==e)return e;!(0,i.YP)(e)&&t&&t.blockedRelativeUrls&&t.blockedRelativeUrls.push(e);let r=(0,i.hF)(e);if(t){const s=t.verifyItemRelativeUrls&&t.verifyItemRelativeUrls.rootPath||t.url&&t.url.path;if(s){const n=p(s,t.portal),o=p(r,t.portal);r=(0,i.PF)(o,n,n),null!=r&&r!==o&&r!==e&&t.verifyItemRelativeUrls&&t.verifyItemRelativeUrls.writtenUrls.push(r)}}return r=g(r,t?.portal),(0,i.YP)(r)&&(r=(0,i.Fv)(r)),t?.resources&&t?.portalItem&&!(0,i.YP)(r)&&!(0,i.HK)(r)&&s===f.YES&&t.resources.toKeep.push({resource:t.portalItem.resourceFromPath(r),compress:!1}),r}function a(e,t,s){return n(e,s)}function h(e,t,s,r){const i=o(e,r);void 0!==i&&(t[s]=i)}const l=/\/items\/([^\/]+)\/resources\/(.*)/,u=/^\.\/resources\//;function c(e){return(e?.match(l)??null)?.[1]??null}function d(e){const t=e?.match(l)??null;if(null==t)return null;const s=t[2],n=s.lastIndexOf("/");if(-1===n){const{path:e,extension:t}=(0,i.fZ)(s);return{prefix:null,filename:e,extension:(0,r.Wg)(t)}}const{path:o,extension:a}=(0,i.fZ)(s.slice(n+1));return{prefix:s.slice(0,n),filename:o,extension:(0,r.Wg)(a)}}function g(e,t){return t&&!t.isPortal&&t.urlKey&&t.customBaseUrl?(0,i.Ie)(e,`${t.urlKey}.${t.customBaseUrl}`,t.portalHostname):e}function p(e,t){if(!t||t.isPortal||!t.urlKey||!t.customBaseUrl)return e;const s=`${t.urlKey}.${t.customBaseUrl}`,r=(0,i.TI)();return(0,i.D6)(r,`${r.scheme}://${s}`)?(0,i.Ie)(e,t.portalHostname,s):(0,i.Ie)(e,s,t.portalHostname)}var f,_;(_=f||(f={}))[_.YES=0]="YES",_[_.NO=1]="NO";const m=Object.freeze(Object.defineProperty({__proto__:null,get MarkKeep(){return f},ensureMainOnlineDomain:g,fromJSON:n,itemIdFromResourceUrl:c,prefixAndFilenameFromResourceUrl:d,read:a,toJSON:o,write:h},Symbol.toStringTag,{value:"Module"}))},96794:(e,t,s)=>{s.d(t,{r:()=>i});var r=s(88764);function i(e,t){if(!(this instanceof i))return new i(e,t);this._maxEntries=Math.max(4,e||9),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),t&&("function"==typeof t?this.toBBox=t:this._initFormat(t)),this.clear()}function n(e,t,s){if(!s)return t.indexOf(e);for(var r=0;r<t.length;r++)if(s(e,t[r]))return r;return-1}function o(e,t){a(e,0,e.children.length,t,e)}function a(e,t,s,r,i){i||(i=m(null)),i.minX=1/0,i.minY=1/0,i.maxX=-1/0,i.maxY=-1/0;for(var n,o=t;o<s;o++)n=e.children[o],h(i,e.leaf?r(n):n);return i}function h(e,t){return e.minX=Math.min(e.minX,t.minX),e.minY=Math.min(e.minY,t.minY),e.maxX=Math.max(e.maxX,t.maxX),e.maxY=Math.max(e.maxY,t.maxY),e}function l(e,t){return e.minX-t.minX}function u(e,t){return e.minY-t.minY}function c(e){return(e.maxX-e.minX)*(e.maxY-e.minY)}function d(e){return e.maxX-e.minX+(e.maxY-e.minY)}function g(e,t){return(Math.max(t.maxX,e.maxX)-Math.min(t.minX,e.minX))*(Math.max(t.maxY,e.maxY)-Math.min(t.minY,e.minY))}function p(e,t){var s=Math.max(e.minX,t.minX),r=Math.max(e.minY,t.minY),i=Math.min(e.maxX,t.maxX),n=Math.min(e.maxY,t.maxY);return Math.max(0,i-s)*Math.max(0,n-r)}function f(e,t){return e.minX<=t.minX&&e.minY<=t.minY&&t.maxX<=e.maxX&&t.maxY<=e.maxY}function _(e,t){return t.minX<=e.maxX&&t.minY<=e.maxY&&t.maxX>=e.minX&&t.maxY>=e.minY}function m(e){return{children:e,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function y(e,t,s,i,n){for(var o,a=[t,s];a.length;)(s=a.pop())-(t=a.pop())<=i||(o=t+Math.ceil((s-t)/i/2)*i,(0,r.q)(e,o,t,s,n),a.push(t,o,o,s))}i.prototype={all:function(){return this._all(this.data,[])},search:function(e){var t=this.data,s=[],r=this.toBBox;if(!_(e,t))return s;for(var i,n,o,a,h=[];t;){for(i=0,n=t.children.length;i<n;i++)o=t.children[i],_(e,a=t.leaf?r(o):o)&&(t.leaf?s.push(o):f(e,a)?this._all(o,s):h.push(o));t=h.pop()}return s},collides:function(e){var t=this.data,s=this.toBBox;if(!_(e,t))return!1;for(var r,i,n,o,a=[];t;){for(r=0,i=t.children.length;r<i;r++)if(n=t.children[r],_(e,o=t.leaf?s(n):n)){if(t.leaf||f(e,o))return!0;a.push(n)}t=a.pop()}return!1},load:function(e){if(!e||!e.length)return this;if(e.length<this._minEntries){for(var t=0,s=e.length;t<s;t++)this.insert(e[t]);return this}var r=this._build(e.slice(),0,e.length-1,0);if(this.data.children.length)if(this.data.height===r.height)this._splitRoot(this.data,r);else{if(this.data.height<r.height){var i=this.data;this.data=r,r=i}this._insert(r,this.data.height-r.height-1,!0)}else this.data=r;return this},insert:function(e){return e&&this._insert(e,this.data.height-1),this},clear:function(){return this.data=m([]),this},remove:function(e,t){if(!e)return this;for(var s,r,i,o,a=this.data,h=this.toBBox(e),l=[],u=[];a||l.length;){if(a||(a=l.pop(),r=l[l.length-1],s=u.pop(),o=!0),a.leaf&&-1!==(i=n(e,a.children,t)))return a.children.splice(i,1),l.push(a),this._condense(l),this;o||a.leaf||!f(a,h)?r?(s++,a=r.children[s],o=!1):a=null:(l.push(a),u.push(s),s=0,r=a,a=a.children[0])}return this},toBBox:function(e){return e},compareMinX:l,compareMinY:u,toJSON:function(){return this.data},fromJSON:function(e){return this.data=e,this},_all:function(e,t){for(var s=[];e;)e.leaf?t.push.apply(t,e.children):s.push.apply(s,e.children),e=s.pop();return t},_build:function(e,t,s,r){var i,n=s-t+1,a=this._maxEntries;if(n<=a)return o(i=m(e.slice(t,s+1)),this.toBBox),i;r||(r=Math.ceil(Math.log(n)/Math.log(a)),a=Math.ceil(n/Math.pow(a,r-1))),(i=m([])).leaf=!1,i.height=r;var h,l,u,c,d=Math.ceil(n/a),g=d*Math.ceil(Math.sqrt(a));for(y(e,t,s,g,this.compareMinX),h=t;h<=s;h+=g)for(y(e,h,u=Math.min(h+g-1,s),d,this.compareMinY),l=h;l<=u;l+=d)c=Math.min(l+d-1,u),i.children.push(this._build(e,l,c,r-1));return o(i,this.toBBox),i},_chooseSubtree:function(e,t,s,r){for(var i,n,o,a,h,l,u,d;r.push(t),!t.leaf&&r.length-1!==s;){for(u=d=1/0,i=0,n=t.children.length;i<n;i++)h=c(o=t.children[i]),(l=g(e,o)-h)<d?(d=l,u=h<u?h:u,a=o):l===d&&h<u&&(u=h,a=o);t=a||t.children[0]}return t},_insert:function(e,t,s){var r=this.toBBox,i=s?e:r(e),n=[],o=this._chooseSubtree(i,this.data,t,n);for(o.children.push(e),h(o,i);t>=0&&n[t].children.length>this._maxEntries;)this._split(n,t),t--;this._adjustParentBBoxes(i,n,t)},_split:function(e,t){var s=e[t],r=s.children.length,i=this._minEntries;this._chooseSplitAxis(s,i,r);var n=this._chooseSplitIndex(s,i,r),a=m(s.children.splice(n,s.children.length-n));a.height=s.height,a.leaf=s.leaf,o(s,this.toBBox),o(a,this.toBBox),t?e[t-1].children.push(a):this._splitRoot(s,a)},_splitRoot:function(e,t){this.data=m([e,t]),this.data.height=e.height+1,this.data.leaf=!1,o(this.data,this.toBBox)},_chooseSplitIndex:function(e,t,s){var r,i,n,o,h,l,u,d;for(l=u=1/0,r=t;r<=s-t;r++)o=p(i=a(e,0,r,this.toBBox),n=a(e,r,s,this.toBBox)),h=c(i)+c(n),o<l?(l=o,d=r,u=h<u?h:u):o===l&&h<u&&(u=h,d=r);return d},_chooseSplitAxis:function(e,t,s){var r=e.leaf?this.compareMinX:l,i=e.leaf?this.compareMinY:u;this._allDistMargin(e,t,s,r)<this._allDistMargin(e,t,s,i)&&e.children.sort(r)},_allDistMargin:function(e,t,s,r){e.children.sort(r);var i,n,o=this.toBBox,l=a(e,0,t,o),u=a(e,s-t,s,o),c=d(l)+d(u);for(i=t;i<s-t;i++)n=e.children[i],h(l,e.leaf?o(n):n),c+=d(l);for(i=s-t-1;i>=t;i--)n=e.children[i],h(u,e.leaf?o(n):n),c+=d(u);return c},_adjustParentBBoxes:function(e,t,s){for(var r=s;r>=0;r--)h(t[r],e)},_condense:function(e){for(var t,s=e.length-1;s>=0;s--)0===e[s].children.length?s>0?(t=e[s-1].children).splice(t.indexOf(e[s]),1):this.clear():o(e[s],this.toBBox)},_initFormat:function(e){var t=["return a"," - b",";"];this.compareMinX=new Function("a","b",t.join(e[0])),this.compareMinY=new Function("a","b",t.join(e[1])),this.toBBox=new Function("a","return {minX: a"+e[0]+", minY: a"+e[1]+", maxX: a"+e[2]+", maxY: a"+e[3]+"};")}}},8557:(e,t,s)=>{s.d(t,{Z:()=>i});var r=s(70586);class i{constructor(e){this.size=0,this._start=0,this.maxSize=e,this._buffer=new Array(e)}get entries(){return this._buffer}enqueue(e){if(this.size===this.maxSize){const t=this._buffer[this._start];return this._buffer[this._start]=e,this._start=(this._start+1)%this.maxSize,t}return this._buffer[(this._start+this.size++)%this.maxSize]=e,null}dequeue(){if(0===this.size)return null;const e=this._buffer[this._start];return this._buffer[this._start]=null,this.size--,this._start=(this._start+1)%this.maxSize,e}peek(){return 0===this.size?null:this._buffer[this._start]}find(e){if(0===this.size)return null;for(const t of this._buffer)if((0,r.pC)(t)&&e(t))return t;return null}clear(e){let t=this.dequeue();for(;(0,r.pC)(t);)e&&e(t),t=this.dequeue()}}},3920:(e,t,s)=>{s.d(t,{p:()=>l,r:()=>u});var r=s(43697),i=s(15923),n=s(61247),o=s(5600),a=s(52011),h=s(72762);const l=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new n.Z}get updatingHandles(){return this._get("updatingHandles")||new h.t}};return(0,r._)([(0,o.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,r._)([(0,o.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,r._)([(0,a.j)("esri.core.HandleOwner")],t),t};let u=class extends(l(i.Z)){};u=(0,r._)([(0,a.j)("esri.core.HandleOwner")],u)},64830:(e,t,s)=>{s.d(t,{Z:()=>i});var r=s(70586);class i{constructor(e=(e=>e.values().next().value)){this._peeker=e,this._items=new Set}get length(){return this._items.size}clear(){this._items.clear()}last(){if(0===this._items.size)return;let e;for(e of this._items);return e}peek(){if(0!==this._items.size)return this._peeker(this._items)}push(e){this.contains(e)||this._items.add(e)}contains(e){return this._items.has(e)}pop(){if(0===this.length)return;const e=this.peek();return this._items.delete((0,r.j0)(e)),e}popLast(){if(0===this.length)return;const e=this.last();return this._items.delete((0,r.j0)(e)),e}remove(e){this._items.delete(e)}filter(e){return this._items.forEach((t=>{e(t)||this._items.delete(t)})),this}}},72762:(e,t,s)=>{s.d(t,{t:()=>c});var r=s(43697),i=s(15923),n=s(61247),o=s(70586),a=s(17445),h=s(1654),l=s(5600),u=s(52011);let c=class extends i.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new n.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,s={}){return this._installWatch(e,t,s,a.YP)}addWhen(e,t,s={}){return this._installWatch(e,t,s,a.gx)}addOnCollectionChange(e,t,{initial:s=!1,final:r=!1}={}){const i=++this._handleId;return this._handles.add([(0,a.on)(e,"after-changes",this._createSyncUpdatingCallback(),a.Z_),(0,a.on)(e,"change",t,{onListenerAdd:s?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:r?e=>t({added:[],removed:e.toArray()}):void 0})],i),{remove:()=>this._handles.remove(i)}}addPromise(e){if((0,o.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(d)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const s=()=>this._handles.remove(t);return e.then(s,s),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,s={},r){const i=++this._handleId;s.sync||this._installSyncUpdatingWatch(e,i);const n=r(e,t,s);return this._handles.add(n,i),{remove:()=>this._handles.remove(i)}}_installSyncUpdatingWatch(e,t){const s=this._createSyncUpdatingCallback(),r=(0,a.YP)(e,s,{sync:!0,equals:()=>!1});return this._handles.add(r,t),r}_createSyncUpdatingCallback(){return()=>{this._handles.remove(d),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,h.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(d))})),d)}}};(0,r._)([(0,l.Cb)({readOnly:!0})],c.prototype,"updating",void 0),c=(0,r._)([(0,u.j)("esri.core.support.WatchUpdatingTracking")],c);const d=-42},80903:(e,t,s)=>{s.d(t,{Z:()=>h});var r=s(50758),i=s(92604),n=s(95330),o=s(64830),a=s(25045);class h{constructor(){this._inUseClients=new Array,this._clients=new Array,this._clientPromises=new Array,this._ongoingJobsQueue=new o.Z}destroy(){this.close()}get closed(){return!this._clients||!this._clients.length}open(e,t){return new Promise(((s,r)=>{let i=!0;const o=e=>{(0,n.k_)(t.signal),i&&(i=!1,e())};this._clients.length=e.length,this._clientPromises.length=e.length,this._inUseClients.length=e.length;for(let i=0;i<e.length;++i){const h=e[i];(0,n.y8)(h)?this._clientPromises[i]=h.then((e=>(this._clients[i]=new a.default(e,t,(()=>this._ongoingJobsQueue.pop()??null)),o(s),this._clients[i])),(()=>(o(r),null))):(this._clients[i]=new a.default(h,t,(()=>this._ongoingJobsQueue.pop()??null)),this._clientPromises[i]=Promise.resolve(this._clients[i]),o(s))}}))}broadcast(e,t,s){const r=new Array(this._clientPromises.length);for(let i=0;i<this._clientPromises.length;++i){const n=this._clientPromises[i];r[i]=n.then((r=>r?.invoke(e,t,s)))}return r}close(){let e;for(;e=this._ongoingJobsQueue.pop();)e.deferred.reject((0,n.zE)(`Worker closing, aborting job calling '${e.methodName}'`));for(const e of this._clientPromises)e.then((e=>e?.close()));this._clients.length=0,this._clientPromises.length=0}invoke(e,t,s){let r;Array.isArray(s)?(i.Z.getLogger("esri.core.workers.Connection").warn("invoke()","The transferList parameter is deprecated, use the options object instead"),r={transferList:s}):r=s;const o=(0,n.dD)();this._ongoingJobsQueue.push({methodName:e,data:t,invokeOptions:r,deferred:o});for(let e=0;e<this._clientPromises.length;e++){const t=this._clients[e];t?t.jobAdded():this._clientPromises[e].then((e=>e?.jobAdded()))}return o.promise}on(e,t){return Promise.all(this._clientPromises).then((()=>(0,r.AL)(this._clients.map((s=>s.on(e,t))))))}openPorts(){return new Promise((e=>{const t=new Array(this._clientPromises.length);let s=t.length;for(let r=0;r<this._clientPromises.length;++r)this._clientPromises[r].then((i=>{i&&(t[r]=i.openPort()),0==--s&&e(t)}))}))}get test(){return{numClients:this._clients.length}}}},95047:(e,t,s)=>{s.r(t),s.d(t,{createConnection:()=>T});var r=s(43697),i=(s(66577),s(3172)),n=s(20102),o=s(92604),a=s(70586),h=s(95330),l=s(17452),u=(s(75215),s(67676),s(80442),s(52011)),c=s(5600),d=s(69285),g=s(32448);let p=class extends g.Z.EventedAccessor{destroy(){this.emit("destroy")}get connectionError(){return this.errorString?new n.Z("stream-connection",this.errorString):null}onFeature(e){this.emit("data-received",e)}onMessage(e){this.emit("message-received",e)}};(0,r._)([(0,c.Cb)({readOnly:!0})],p.prototype,"connectionError",null),p=(0,r._)([(0,u.j)("esri.layers.support.StreamConnection")],p);const f=p;var _,m;(m=_||(_={}))[m.CONNECTING=0]="CONNECTING",m[m.OPEN=1]="OPEN",m[m.CLOSING=2]="CLOSING",m[m.CLOSED=3]="CLOSED";let y=class extends f{constructor(e){super(),this._outstandingMessages=[],this.errorString=null;const{geometryType:t,spatialReference:s,sourceSpatialReference:r}=e;this._config=e,this._featureZScaler=(0,d.k)(t,r,s),this._open()}async _open(){await this._tryCreateWebSocket(),this.destroyed||await this._handshake()}destroy(){super.destroy(),(0,a.pC)(this._websocket)&&(this._websocket.onopen=null,this._websocket.onclose=null,this._websocket.onerror=null,this._websocket.onmessage=null,this._websocket.close()),this._websocket=null}get connectionStatus(){if((0,a.Wi)(this._websocket))return"disconnected";switch(this._websocket.readyState){case _.CONNECTING:case _.OPEN:return"connected";case _.CLOSING:case _.CLOSED:return"disconnected"}}sendMessageToSocket(e){(0,a.Wi)(this._websocket)?this._outstandingMessages.push(e):this._websocket.send(JSON.stringify(e))}sendMessageToClient(e){this._onMessage(e)}updateCustomParameters(e){this._config.customParameters=e,(0,a.pC)(this._websocket)&&this._websocket.close()}async _tryCreateWebSocket(e=this._config.source.path,t=1e3,s=0){try{if(this.destroyed)return;const t=(0,l.fl)(e,this._config.customParameters??{});this._websocket=await this._createWebSocket(t),this.notifyChange("connectionStatus")}catch(r){const i=t/1e3;return this._config.maxReconnectionAttempts&&s>=this._config.maxReconnectionAttempts?(o.Z.getLogger(this.declaredClass).error(new n.Z("websocket-connection","Exceeded maxReconnectionAttempts attempts. No further attempts will be made")),void this.destroy()):(o.Z.getLogger(this.declaredClass).error(new n.Z("websocket-connection",`Failed to connect. Attempting to reconnect in ${i}s`,r)),await(0,h.e4)(t),this._tryCreateWebSocket(e,Math.min(1.5*t,1e3*this._config.maxReconnectionInterval),s+1))}}_setWebSocketJSONParseHandler(e){e.onmessage=e=>{try{const t=JSON.parse(e.data);this._onMessage(t)}catch(e){return void o.Z.getLogger(this.declaredClass).error(new n.Z("websocket-connection","Failed to parse message, invalid JSON",{error:e}))}}}_createWebSocket(e){return new Promise(((t,s)=>{const r=new WebSocket(e);r.onopen=()=>{if(r.onopen=null,this.destroyed)return r.onclose=null,void r.close();r.onclose=e=>this._onClose(e),r.onerror=e=>this._onError(e),this._setWebSocketJSONParseHandler(r),t(r)},r.onclose=e=>{r.onopen=r.onclose=null,s(e)}}))}async _handshake(e=1e4){const t=this._websocket;if((0,a.Wi)(t))return;const s=(0,h.hh)(),r=t.onmessage,{filter:i,outFields:l,spatialReference:u}=this._config;return s.timeout(e),t.onmessage=e=>{let a=null;try{a=JSON.parse(e.data)}catch(e){}a&&"object"==typeof a||(o.Z.getLogger(this.declaredClass).error(new n.Z("websocket-connection","Protocol violation. Handshake failed - malformed message",e.data)),s.reject(),this.destroy()),a.spatialReference?.wkid!==u?.wkid&&(o.Z.getLogger(this.declaredClass).error(new n.Z("websocket-connection",`Protocol violation. Handshake failed - expected wkid of ${u.wkid}`,e.data)),s.reject(),this.destroy()),"json"!==a.format&&(o.Z.getLogger(this.declaredClass).error(new n.Z("websocket-connection","Protocol violation. Handshake failed - format is not set",e.data)),s.reject(),this.destroy()),i&&a.filter!==i&&o.Z.getLogger(this.declaredClass).error(new n.Z("websocket-connection","Tried to set filter, but server doesn't support it")),l&&a.outFields!==l&&o.Z.getLogger(this.declaredClass).error(new n.Z("websocket-connection","Tried to set outFields, but server doesn't support it")),t.onmessage=r;for(const e of this._outstandingMessages)t.send(JSON.stringify(e));this._outstandingMessages=[],s.resolve()},t.send(JSON.stringify({filter:i,outFields:l,format:"json",spatialReference:{wkid:u.wkid}})),s.promise}_onMessage(e){if(this.onMessage(e),"type"in e)switch(e.type){case"features":case"featureResult":for(const t of e.features)(0,a.pC)(this._featureZScaler)&&this._featureZScaler(t.geometry),this.onFeature(t)}}_onError(e){const t="Encountered an error over WebSocket connection";this._set("errorString",t),o.Z.getLogger(this.declaredClass).error("websocket-connection",t)}_onClose(e){this._websocket=null,this.notifyChange("connectionStatus"),1e3!==e.code&&o.Z.getLogger(this.declaredClass).error("websocket-connection",`WebSocket closed unexpectedly with error code ${e.code}`),this.destroyed||this._open()}};(0,r._)([(0,c.Cb)()],y.prototype,"connectionStatus",null),(0,r._)([(0,c.Cb)()],y.prototype,"errorString",void 0),y=(0,r._)([(0,u.j)("esri.layers.graphics.sources.connections.WebSocketConnection")],y);var I=s(34599),b=s(14165),v=s(33955),x=s(82971);const w={maxQueryDepth:5,maxRecordCountFactor:3};let S=class extends y{constructor(e){super({...w,...e}),this._buddyServicesQuery=null,this._relatedFeatures=null}async _open(){const e=await this._fetchServiceDefinition(this._config.source);e.timeInfo.trackIdField||o.Z.getLogger(this.declaredClass).warn("GeoEvent service was configured without a TrackIdField. This may result in certain functionality being disabled. The purgeOptions.maxObservations property will have no effect.");const t=this._fetchWebSocketUrl(e.streamUrls,this._config.spatialReference);this._buddyServicesQuery||(this._buddyServicesQuery=this._queryBuddyServices()),await this._buddyServicesQuery,await this._tryCreateWebSocket(t);const{filter:s,outFields:r}=this._config;this.destroyed||this._setFilter(s,r)}_onMessage(e){if("attributes"in e){let t;try{t=this._enrich(e),(0,a.pC)(this._featureZScaler)&&this._featureZScaler(t.geometry)}catch(e){return void o.Z.getLogger(this.declaredClass).error(new n.Z("geoevent-connection","Failed to parse message",e))}this.onFeature(t)}else this.onMessage(e)}async _fetchServiceDefinition(e){const t={f:"json",...this._config.customParameters},s=(0,i.default)(e.path,{query:t,responseType:"json"}),r=(await s).data;return this._serviceDefinition=r,r}_fetchWebSocketUrl(e,t){const s=e[0],{urls:r,token:i}=s,n=this._inferWebSocketBaseUrl(r);return(0,l.fl)(`${n}/subscribe`,{outSR:""+t.wkid,token:i})}_inferWebSocketBaseUrl(e){if(1===e.length)return e[0];for(const t of e)if(t.includes("wss"))return t;return o.Z.getLogger(this.declaredClass).error(new n.Z("geoevent-connection","Unable to infer WebSocket url",e)),null}async _setFilter(e,t){const s=this._websocket;if((0,a.Wi)(s)||(0,a.Wi)(e)&&(0,a.Wi)(t))return;const r=JSON.stringify({filter:this._serializeFilter(e,t)});let i=!1;const l=(0,h.hh)();return s.onmessage=e=>{const t=JSON.parse(e.data);t.filter&&(t.error&&(o.Z.getLogger(this.declaredClass).error(new n.Z("geoevent-connection","Failed to set service filter",t.error)),this._set("errorString",`Could not set service filter - ${t.error}`),l.reject(t.error)),this._setWebSocketJSONParseHandler(s),i=!0,l.resolve())},s.send(r),setTimeout((()=>{i||(this.destroyed||this._websocket!==s||o.Z.getLogger(this.declaredClass).error(new n.Z("geoevent-connection","Server timed out when setting filter")),l.reject())}),1e4),l.promise}_serializeFilter(e,t){const s={};if((0,a.Wi)(e)&&(0,a.Wi)(t))return s;if((0,a.pC)(e)&&e.geometry)try{const t=(0,v.im)(e.geometry);if("extent"!==t.type)throw new n.Z(`Expected extent but found type ${t.type}`);s.geometry=JSON.stringify(t.shiftCentralMeridian())}catch(e){o.Z.getLogger(this.declaredClass).error(new n.Z("geoevent-connection","Encountered an error when setting connection geometryDefinition",e))}return(0,a.pC)(e)&&e.where&&"1 = 1"!==e.where&&"1=1"!==e.where&&(s.where=e.where),(0,a.pC)(t)&&(s.outFields=t.join(",")),s}_enrich(e){if(!this._relatedFeatures)return e;const t=this._serviceDefinition.relatedFeatures.joinField,s=e.attributes[t],r=this._relatedFeatures.get(s);if(!r)return o.Z.getLogger(this.declaredClass).warn("geoevent-connection","Feature join failed. Is the join field configured correctly?",e),e;const{attributes:i,geometry:a}=r;for(const t in i)e.attributes[t]=i[t];return a&&(e.geometry=a),e.geometry||e.centroid||o.Z.getLogger(this.declaredClass).error(new n.Z("geoevent-connection","Found malformed feature - no geometry found",e)),e}async _queryBuddyServices(){try{const{relatedFeatures:e,keepLatestArchive:t}=this._serviceDefinition,s=this._queryRelatedFeatures(e),r=this._queryArchive(t);await s;const i=await r;if(!i)return;for(const e of i.features)this.onFeature(this._enrich(e))}catch(e){o.Z.getLogger(this.declaredClass).error(new n.Z("geoevent-connection","Encountered an error when querying buddy services",{error:e}))}}async _queryRelatedFeatures(e){if(!e)return;const t=await this._queryBuddy(e.featuresUrl);this._addRelatedFeatures(t)}async _queryArchive(e){if(e)return this._queryBuddy(e.featuresUrl)}async _queryBuddy(e){const t=new((await Promise.all([s.e(2710),s.e(1612),s.e(4729),s.e(9790),s.e(8244),s.e(1423),s.e(911),s.e(5546),s.e(9942),s.e(9238),s.e(5235),s.e(5587),s.e(2156)]).then(s.bind(s,19238))).default)({url:e}),{capabilities:r}=await t.load(),i=r.query.supportsMaxRecordCountFactor,n=r.query.supportsPagination,o=r.query.supportsCentroid,h=this._config.maxRecordCountFactor,l=t.capabilities.query.maxRecordCount,u=i?l*h:l,c=new b.Z;if(c.outFields=(0,a.Pt)(this._config.outFields,["*"]),c.where=(0,a.Pt)((0,a.U2)(this._config.filter,"where"),"1=1"),c.returnGeometry=!0,c.returnExceededLimitFeatures=!0,c.outSpatialReference=x.Z.fromJSON(this._config.spatialReference),o&&(c.returnCentroid=!0),i&&(c.maxRecordCountFactor=h),n)return c.num=u,t.destroy(),this._queryPages(e,c);const d=await(0,I.JT)(e,c,this._config.sourceSpatialReference);return t.destroy(),d.data}async _queryPages(e,t,s=[],r=0){t.start=(0,a.pC)(t.num)?r*t.num:null;const{data:i}=await(0,I.JT)(e,t,this._config.sourceSpatialReference);return i.exceededTransferLimit&&r<(this._config.maxQueryDepth??0)?(i.features.forEach((e=>s.push(e))),this._queryPages(e,t,s,r+1)):(s.forEach((e=>i.features.push(e))),i)}_addRelatedFeatures(e){const t=new Map,s=e.features,r=this._serviceDefinition.relatedFeatures.joinField;for(const e of s){const s=e.attributes[r];t.set(s,e)}this._relatedFeatures=t}};S=(0,r._)([(0,u.j)("esri.layers.graphics.sources.connections.GeoEventConnection")],S);const C=S;let F=class extends f{constructor(e){super(),this.connectionStatus="connected",this.errorString=null;const{geometryType:t,spatialReference:s,sourceSpatialReference:r}=e;this._featureZScaler=(0,d.k)(t,r,s)}updateCustomParameters(e){}sendMessageToSocket(e){}sendMessageToClient(e){if("type"in e)switch(e.type){case"features":case"featureResult":for(const t of e.features)(0,a.pC)(this._featureZScaler)&&this._featureZScaler(t.geometry),this.onFeature(t)}this.onMessage(e)}};function T(e,t,s,r,i,n,o,a){const h={source:e,sourceSpatialReference:t,spatialReference:s,geometryType:r,filter:i,maxReconnectionAttempts:n,maxReconnectionInterval:o,customParameters:a};return e?e.path.startsWith("wss://")||e.path.startsWith("ws://")?new y(h):new C(h):new F(h)}(0,r._)([(0,c.Cb)()],F.prototype,"connectionStatus",void 0),(0,r._)([(0,c.Cb)()],F.prototype,"errorString",void 0),F=(0,r._)([(0,u.j)("esri.layers.support.ClientSideConnection")],F)},92722:(e,t,s)=>{s.d(t,{O3:()=>v,lG:()=>w,my:()=>x,q9:()=>h});var r=s(20102),i=s(70272),n=s(5428),o=s(35671);const a={LineString:"esriGeometryPolyline",MultiLineString:"esriGeometryPolyline",MultiPoint:"esriGeometryMultipoint",Point:"esriGeometryPoint",Polygon:"esriGeometryPolygon",MultiPolygon:"esriGeometryPolygon"};function h(e){return a[e]}function*l(e){switch(e.type){case"Feature":yield e;break;case"FeatureCollection":for(const t of e.features)t&&(yield t)}}function*u(e){if(e)switch(e.type){case"Point":yield e.coordinates;break;case"LineString":case"MultiPoint":yield*e.coordinates;break;case"MultiLineString":case"Polygon":for(const t of e.coordinates)yield*t;break;case"MultiPolygon":for(const t of e.coordinates)for(const e of t)yield*e}}function c(e){for(const t of e)if(t.length>2)return!0;return!1}function d(e){let t=0;for(let s=0;s<e.length;s++){const r=e[s],i=e[(s+1)%e.length];t+=r[0]*i[1]-i[0]*r[1]}return t<=0}function g(e){const t=e[0],s=e[e.length-1];return t[0]===s[0]&&t[1]===s[1]&&t[2]===s[2]||e.push(t),e}function p(e,t,s){switch(t.type){case"LineString":case"MultiPoint":return function(e,t,s){return m(e,t.coordinates,s),e}(e,t,s);case"MultiLineString":return function(e,t,s){for(const r of t.coordinates)m(e,r,s);return e}(e,t,s);case"MultiPolygon":return function(e,t,s){for(const r of t.coordinates){f(e,r[0],s);for(let t=1;t<r.length;t++)_(e,r[t],s)}return e}(e,t,s);case"Point":return function(e,t,s){return I(e,t.coordinates,s),e}(e,t,s);case"Polygon":return function(e,t,s){const r=t.coordinates;f(e,r[0],s);for(let t=1;t<r.length;t++)_(e,r[t],s);return e}(e,t,s)}}function f(e,t,s){const r=g(t);!function(e){return!d(e)}(r)?m(e,r,s):y(e,r,s)}function _(e,t,s){const r=g(t);!function(e){return d(e)}(r)?m(e,r,s):y(e,r,s)}function m(e,t,s){for(const r of t)I(e,r,s);e.lengths.push(t.length)}function y(e,t,s){for(let r=t.length-1;r>=0;r--)I(e,t[r],s);e.lengths.push(t.length)}function I(e,t,s){const[r,i,n]=t;e.coords.push(r,i),s.hasZ&&e.coords.push(n||0)}function b(e){switch(typeof e){case"string":return"esriFieldTypeString";case"number":return"esriFieldTypeDouble";default:return"unknown"}}function v(e){if(!e)throw new r.Z("geojson-layer:empty","GeoJSON data is empty");if("Feature"!==e.type&&"FeatureCollection"!==e.type)throw new r.Z("geojson-layer:unsupported-geojson-object","missing or not supported GeoJSON object type",{data:e});const{crs:t}=e;if(!t)return;const s="string"==typeof t?t:"name"===t.type?t.properties.name:"EPSG"===t.type?t.properties.code:null,i=new RegExp(".*(CRS84H?|4326)$","i");if(!s||!i.test(s))throw new r.Z("geojson-layer:unsupported-crs","unsupported GeoJSON 'crs' member",{crs:t})}function x(e,t={}){const s=[],r=new Set,i=new Set;let n,a=!1,d=null,g=!1,{geometryType:p=null}=t,f=!1;for(const t of l(e)){const{geometry:e,properties:l,id:_}=t;if((!e||(p||(p=h(e.type)),h(e.type)===p))&&(a||(a=c(u(e))),g||(g=null!=_,g&&(n=typeof _,l&&(d=Object.keys(l).filter((e=>l[e]===_))))),l&&d&&g&&null!=_&&(d.length>1?d=d.filter((e=>l[e]===_)):1===d.length&&(d=l[d[0]]===_?d:[])),!f&&l)){let e=!0;for(const t in l){if(r.has(t))continue;const n=l[t];if(null==n){e=!1,i.add(t);continue}const a=b(n);if("unknown"===a){i.add(t);continue}i.delete(t),r.add(t);const h=(0,o.q6)(t);h&&s.push({name:h,alias:t,type:a})}f=e}}const _=(0,o.q6)(1===d?.length&&d[0]||null)??void 0;if(_)for(const e of s)if(e.name===_&&(0,o.H7)(e)){e.type="esriFieldTypeOID";break}return{fields:s,geometryType:p,hasZ:a,objectIdFieldName:_,objectIdFieldType:n,unknownFields:Array.from(i)}}function w(e,t){return Array.from(function*(e,t={}){const{geometryType:s,objectIdField:r}=t;for(const o of e){const{geometry:e,properties:a,id:l}=o;if(e&&h(e.type)!==s)continue;const u=a||{};let c;r&&(c=u[r],null==l||c||(u[r]=c=l));const d=new i.u_(e?p(new n.Z,e,t):null,u,null,c??void 0);yield d}}(l(e),t))}},25278:(e,t,s)=>{s.d(t,{Dm:()=>u,Hq:()=>c,MS:()=>d,bU:()=>a});var r=s(80442),i=s(22974),n=s(61159),o=s(58333);function a(e){return{renderer:{type:"simple",symbol:"esriGeometryPoint"===e||"esriGeometryMultipoint"===e?o.I4:"esriGeometryPolyline"===e?o.ET:o.lF}}}const h=/^[_$a-zA-Z][_$a-zA-Z0-9]*$/;let l=1;function u(e,t){if((0,r.Z)("esri-csp-restrictions"))return()=>({[t]:null,...e});try{let s=`this.${t} = null;`;for(const t in e)s+=`this${h.test(t)?`.${t}`:`["${t}"]`} = ${JSON.stringify(e[t])};`;const r=new Function(`\n      return class AttributesClass$${l++} {\n        constructor() {\n          ${s};\n        }\n      }\n    `)();return()=>new r}catch(s){return()=>({[t]:null,...e})}}function c(e={}){return[{name:"New Feature",description:"",prototype:{attributes:(0,i.d9)(e)}}]}function d(e,t){return{analytics:{supportsCacheHint:!1},attachment:null,data:{isVersioned:!1,supportsAttachment:!1,supportsM:!1,supportsZ:e},metadata:{supportsAdvancedFieldProperties:!1},operations:{supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsAdd:t,supportsDelete:t,supportsEditing:t,supportsChangeTracking:!1,supportsQuery:!0,supportsQueryAnalytics:!1,supportsQueryAttachments:!1,supportsQueryTopFeatures:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsUpdate:t,supportsExceedsLimitStatistics:!0},query:n.g,queryRelated:{supportsCount:!0,supportsOrderBy:!0,supportsPagination:!0,supportsCacheHint:!1},queryTopFeatures:{supportsCacheHint:!1},editing:{supportsGeometryUpdate:t,supportsGlobalId:!1,supportsReturnServiceEditsInSourceSpatialReference:!1,supportsRollbackOnFailure:!1,supportsUpdateWithoutM:!1,supportsUploadWithItemId:!1,supportsDeleteByAnonymous:!1,supportsDeleteByOthers:!1,supportsUpdateByAnonymous:!1,supportsUpdateByOthers:!1}}}},16467:(e,t,s)=>{s.d(t,{$9:()=>y,G4:()=>v,Lu:()=>m,WW:()=>F,d:()=>S,eS:()=>w,gp:()=>x,j:()=>b,w9:()=>I,yN:()=>C}),s(66577);var r=s(3172),i=s(20102),n=s(92604),o=s(70586),a=s(8744),h=s(40488),l=s(98732),u=s(44876),c=s(92722),d=s(25278),g=s(99514),p=s(86719),f=s(82971);const _=n.Z.getLogger("esri.layers.graphics.sources.ogcfeature"),m="http://www.opengis.net/def/crs/",y=`${m}OGC/1.3/CRS84`;async function I(e,t,s={},n=5){const{links:a}=e,h=E(a,"items","application/geo+json")||E(a,"http://www.opengis.net/def/rel/ogc/1.0/items","application/geo+json");if((0,o.Wi)(h))throw new i.Z("ogc-feature-layer:missing-items-page","Missing items url");const{data:l}=await(0,r.default)(h.href,{signal:s.signal,query:{limit:n,...s.customParameters,token:s.apiKey},headers:{accept:"application/geo+json"}});await(0,c.O3)(l);const u=(0,c.my)(l,{geometryType:t.geometryType}),f=t.fields||u.fields||[],m=null!=t.hasZ?t.hasZ:u.hasZ,y=u.geometryType,I=t.objectIdField||u.objectIdFieldName||"OBJECTID";let b=t.timeInfo;const v=f.find((({name:e})=>e===I));if(v)v.editable=!1,v.nullable=!1;else{if(!u.objectIdFieldType)throw new i.Z("ogc-feature-layer:missing-feature-id","Collection geojson require a feature id as a unique identifier");f.unshift({name:I,alias:I,type:"number"===u.objectIdFieldType?"esriFieldTypeOID":"esriFieldTypeString",editable:!1,nullable:!1})}if(I!==u.objectIdFieldName){const e=f.find((({name:e})=>e===u.objectIdFieldName));e&&(e.type="esriFieldTypeInteger")}f===u.fields&&u.unknownFields.length>0&&_.warn({name:"ogc-feature-layer:unknown-field-types",message:"Some fields types couldn't be inferred from the features and were dropped",details:{unknownFields:u.unknownFields}});for(const e of f){if(null==e.name&&(e.name=e.alias),null==e.alias&&(e.alias=e.name),"esriFieldTypeOID"!==e.type&&"esriFieldTypeGlobalID"!==e.type&&(e.editable=null==e.editable||!!e.editable,e.nullable=null==e.nullable||!!e.nullable),!e.name)throw new i.Z("ogc-feature-layer:invalid-field-name","field name is missing",{field:e});if(!p.v.jsonValues.includes(e.type))throw new i.Z("ogc-feature-layer:invalid-field-type",`invalid type for field "${e.name}"`,{field:e})}if(b){const e=new g.Z(f);if(b.startTimeField){const t=e.get(b.startTimeField);t?(b.startTimeField=t.name,t.type="esriFieldTypeDate"):b.startTimeField=null}if(b.endTimeField){const t=e.get(b.endTimeField);t?(b.endTimeField=t.name,t.type="esriFieldTypeDate"):b.endTimeField=null}if(b.trackIdField){const t=e.get(b.trackIdField);t?b.trackIdField=t.name:(b.trackIdField=null,_.warn({name:"ogc-feature-layer:invalid-timeInfo-trackIdField",message:"trackIdField is missing",details:{timeInfo:b}}))}b.startTimeField||b.endTimeField||(_.warn({name:"ogc-feature-layer:invalid-timeInfo",message:"startTimeField and endTimeField are missing",details:{timeInfo:b}}),b=null)}return{drawingInfo:y?(0,d.bU)(y):null,extent:k(e),geometryType:y,fields:f,hasZ:!!m,objectIdField:I,timeInfo:b}}async function b(e,t={}){const{links:s}=e,n=E(s,"data","application/json")||E(s,"http://www.opengis.net/def/rel/ogc/1.0/data","application/json");if((0,o.Wi)(n))throw new i.Z("ogc-feature-layer:missing-collections-page","Missing collections url");const{apiKey:a,customParameters:h,signal:l}=t,{data:u}=await(0,r.default)(n.href,{signal:l,headers:{accept:"application/json"},query:{...h,token:a}});return u}async function v(e,t={}){const{links:s}=e,n=E(s,"conformance","application/json")||E(s,"http://www.opengis.net/def/rel/ogc/1.0/conformance","application/json");if((0,o.Wi)(n))throw new i.Z("ogc-feature-layer:missing-conformance-page","Missing conformance url");const{apiKey:a,customParameters:h,signal:l}=t,{data:u}=await(0,r.default)(n.href,{signal:l,headers:{accept:"application/json"},query:{...h,token:a}});return u}async function x(e,t={}){const{apiKey:s,customParameters:i,signal:n}=t,{data:o}=await(0,r.default)(e,{signal:n,headers:{accept:"application/json"},query:{...i,token:s}});return o}async function w(e,t={}){const s="application/vnd.oai.openapi+json;version=3.0",i=E(e.links,"service-desc",s);if((0,o.Wi)(i))return _.warn("ogc-feature-layer:missing-openapi-page","The OGC API-Features server does not have an OpenAPI page."),null;const{apiKey:n,customParameters:a,signal:h}=t,{data:l}=await(0,r.default)(i.href,{signal:h,headers:{accept:s},query:{...a,token:n}});return l}function S(e){const t=/^http:\/\/www\.opengis.net\/def\/crs\/(?<authority>.*)\/(?<version>.*)\/(?<code>.*)$/i.exec(e)?.groups;if(!t)return null;const{authority:s,code:r}=t;switch(s.toLowerCase()){case"ogc":switch(r.toLowerCase()){case"crs27":return f.Z.GCS_NAD_1927.wkid;case"crs83":return 4269;case"crs84":case"crs84h":return f.Z.WGS84.wkid;default:return null}case"esri":case"epsg":{const e=Number.parseInt(r,10);return Number.isNaN(e)?null:e}default:return null}}async function C(e,t,s){const r=await F(e,t,s);return(0,l.cn)(r)}async function F(e,t,s){const{collection:n,layerDefinition:d,maxRecordCount:g,queryParameters:{apiKey:p,customParameters:_},spatialReference:m,supportedCrs:y}=e,{links:I}=n,b=E(I,"items","application/geo+json")||E(I,"http://www.opengis.net/def/rel/ogc/1.0/items","application/geo+json");if((0,o.Wi)(b))throw new i.Z("ogc-feature-layer:missing-items-page","Missing items url");const{geometry:v,num:x,start:w,timeExtent:S,where:C}=t;if(t.objectIds)throw new i.Z("ogc-feature-layer:query-by-objectids-not-supported","Queries with objectids are not supported");const F=f.Z.fromJSON(m),M=(0,o.Pt)(t.outSpatialReference,F),k=M.isWGS84?null:T(M,y),R=A(v,y),L=function(e){if((0,o.Wi)(e))return null;const{start:t,end:s}=e;return`${(0,o.pC)(t)?t.toISOString():".."}/${(0,o.pC)(s)?s.toISOString():".."}`}(S),O=(Z=C,(0,o.Wi)(Z)||!Z||"1=1"===Z?null:Z),P=x??(null!=w&&void 0!==w?10:g),{data:U}=await(0,r.default)(b.href,{...s,query:{..._,...R,crs:k,datetime:L,query:O,limit:P,startindex:w,token:p},headers:{accept:"application/geo+json"}});var Z;let B=!1;U.links&&(B=!!U.links.find((e=>"next"===e.rel))),!B&&Number.isInteger(U.numberMatched)&&Number.isInteger(U.numberReturned)&&(B=U.numberReturned<U.numberMatched);const{fields:N,geometryType:D,hasZ:z,objectIdField:q}=d,j=(0,c.lG)(U,{geometryType:D,hasZ:z,objectIdField:q});if(!k&&M.isWebMercator)for(const e of j)if((0,o.pC)(e.geometry)&&null!=D){const t=(0,l.di)(e.geometry,D,z,!1);t.spatialReference=f.Z.WGS84,e.geometry=(0,l.GH)((0,h.iV)(t,M))}for(const e of j)e.objectId=e.attributes[q];const G=k||!k&&M.isWebMercator?M.toJSON():a.Zn,W=new u.Z;return W.exceededTransferLimit=B,W.features=j,W.fields=N,W.geometryType=D,W.hasZ=z,W.objectIdFieldName=q,W.spatialReference=G,W}function T(e,t){const{isWebMercator:s,wkid:r}=e;if(!r)return null;const i=s?t[3857]??t[102100]??t[102113]??t[900913]:t[e.wkid];return i?`${m}${i}`:null}function M(e){if((0,o.Wi)(e))return"";const{xmin:t,ymin:s,xmax:r,ymax:i}=e;return`${t},${s},${r},${i}`}function A(e,t){if(!function(e){return(0,o.pC)(e)&&"extent"===e.type}(e))return null;const{spatialReference:s}=e;if(!s||s.isWGS84)return{bbox:M(e)};const r=T(s,t);return(0,o.pC)(r)?{bbox:M(e),"bbox-crs":r}:s.isWebMercator?{bbox:M((0,h.iV)(e,f.Z.WGS84))}:null}function k(e){const t=e.extent?.spatial;if(!t)return null;const s=t.bbox[0],r=4===s.length,i=s[0],n=s[1],o=r?void 0:s[2];return{xmin:i,ymin:n,xmax:r?s[2]:s[3],ymax:r?s[3]:s[4],zmin:o,zmax:r?void 0:s[5],spatialReference:f.Z.WGS84.toJSON()}}function E(e,t,s){return e.find((e=>e.rel===t&&e.type===s))||e.find((e=>e.rel===t&&!e.type))}},39450:(e,t,s)=>{s.d(t,{Z:()=>u});var r,i=s(43697),n=s(96674),o=s(5600),a=s(75215),h=(s(67676),s(52011));let l=r=class extends n.wq{constructor(e){super(e),this.cols=null,this.level=0,this.levelValue=null,this.origin=null,this.resolution=0,this.rows=null,this.scale=0}clone(){return new r({cols:this.cols,level:this.level,levelValue:this.levelValue,resolution:this.resolution,rows:this.rows,scale:this.scale})}};(0,i._)([(0,o.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],l.prototype,"cols",void 0),(0,i._)([(0,o.Cb)({type:a.z8,json:{write:!0}})],l.prototype,"level",void 0),(0,i._)([(0,o.Cb)({type:String,json:{write:!0}})],l.prototype,"levelValue",void 0),(0,i._)([(0,o.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],l.prototype,"origin",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],l.prototype,"resolution",void 0),(0,i._)([(0,o.Cb)({json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],l.prototype,"rows",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],l.prototype,"scale",void 0),l=r=(0,i._)([(0,h.j)("esri.layers.support.LOD")],l);const u=l},11145:(e,t,s)=>{s.d(t,{Z:()=>C});var r,i=s(43697),n=s(35454),o=s(96674),a=s(70586),h=s(67900),l=s(5600),u=s(75215),c=(s(67676),s(71715)),d=s(52011),g=s(30556),p=s(94139),f=s(82971),_=s(24470),m=s(8744),y=s(40488),I=s(39450),b=s(43077);const v=new n.X({PNG:"png",PNG8:"png8",PNG24:"png24",PNG32:"png32",JPEG:"jpg",JPG:"jpg",DIB:"dib",TIFF:"tiff",EMF:"emf",PS:"ps",PDF:"pdf",GIF:"gif",SVG:"svg",SVGZ:"svgz",Mixed:"mixed",MIXED:"mixed",LERC:"lerc",LERC2D:"lerc2d",RAW:"raw",pbf:"pbf"});let x=r=class extends o.wq{static create(e={}){const{resolutionFactor:t=1,scales:s,size:i=256,spatialReference:n=f.Z.WebMercator,numLODs:o=24}=e;if(!(0,m.JY)(n)){const e=[];if(s)for(let t=0;t<s.length;t++){const r=s[t];e.push(new I.Z({level:t,scale:r,resolution:r}))}else{let t=5e-4;for(let s=o-1;s>=0;s--)e.unshift(new I.Z({level:s,scale:t,resolution:t})),t*=2}return new r({dpi:96,lods:e,origin:new p.Z(0,0,n),size:[i,i],spatialReference:n})}const a=(0,m.C5)(n),l=e.origin?new p.Z({x:e.origin.x,y:e.origin.y,spatialReference:n}):new p.Z(a?{x:a.origin[0],y:a.origin[1],spatialReference:n}:{x:0,y:0,spatialReference:n}),u=1/(39.37*(0,h.c9)(n)*96),c=[];if(s)for(let e=0;e<s.length;e++){const t=s[e],r=t*u;c.push(new I.Z({level:e,scale:t,resolution:r}))}else{let e=(0,m.sT)(n)?512/i*591657527.5917094:256/i*591657527.591555;const s=Math.ceil(o/t);c.push(new I.Z({level:0,scale:e,resolution:e*u}));for(let r=1;r<s;r++){const s=e/2**t,i=s*u;c.push(new I.Z({level:r,scale:s,resolution:i})),e=s}}return new r({dpi:96,lods:c,origin:l,size:[i,i],spatialReference:n})}constructor(e){super(e),this.dpi=96,this.format=null,this.origin=null,this.minScale=0,this.maxScale=0,this.size=null,this.spatialReference=null}get isWrappable(){const{spatialReference:e,origin:t}=this;if(e&&t){const s=(0,m.C5)(e);return e.isWrappable&&!!s&&Math.abs(s.origin[0]-t.x)<=s.dx}return!1}readOrigin(e,t){return p.Z.fromJSON({spatialReference:t.spatialReference,...e})}set lods(e){let t=0,s=0;const r=[],i=this._levelToLOD={};e&&(t=-1/0,s=1/0,e.forEach((e=>{r.push(e.scale),t=e.scale>t?e.scale:t,s=e.scale<s?e.scale:s,i[e.level]=e}))),this._set("scales",r),this._set("minScale",t),this._set("maxScale",s),this._set("lods",e),this._initializeUpsampleLevels()}readSize(e,t){return[t.cols,t.rows]}writeSize(e,t){t.cols=e[0],t.rows=e[1]}zoomToScale(e){const t=this.scales;if(e<=0)return t[0];if(e>=t.length-1)return t[t.length-1];const s=Math.floor(e),r=s+1;return t[s]/(t[s]/t[r])**(e-s)}scaleToZoom(e){const t=this.scales,s=t.length-1;let r=0;for(;r<s;r++){const s=t[r],i=t[r+1];if(s<=e)return r;if(i===e)return r+1;if(s>e&&i<e)return r+Math.log(s/e)/Math.log(s/i)}return r}snapScale(e,t=.95){const s=this.scaleToZoom(e);return s%Math.floor(s)>=t?this.zoomToScale(Math.ceil(s)):this.zoomToScale(Math.floor(s))}tileAt(e,t,s,r){const i=this.lodAt(e);if(!i)return null;let n,o;if("number"==typeof t)n=t,o=s;else if((0,m.fS)(t.spatialReference,this.spatialReference))n=t.x,o=t.y,r=s;else{const e=(0,y.iV)(t,this.spatialReference);if((0,a.Wi)(e))return null;n=e.x,o=e.y,r=s}const h=i.resolution*this.size[0],l=i.resolution*this.size[1];return r||(r=new b.f(null,0,0,0,(0,_.Ue)())),r.level=e,r.row=Math.floor((this.origin.y-o)/l+.001),r.col=Math.floor((n-this.origin.x)/h+.001),this.updateTileInfo(r),r}updateTileInfo(e,t=r.ExtrapolateOptions.NONE){let s=this.lodAt(e.level);if(!s&&t===r.ExtrapolateOptions.POWER_OF_TWO){const t=this.lods[this.lods.length-1];t.level<e.level&&(s=t)}if(!s)return;const i=e.level-s.level,n=s.resolution*this.size[0]/2**i,o=s.resolution*this.size[1]/2**i;e.id=`${e.level}/${e.row}/${e.col}`,e.extent||(e.extent=(0,_.Ue)()),e.extent[0]=this.origin.x+e.col*n,e.extent[1]=this.origin.y-(e.row+1)*o,e.extent[2]=e.extent[0]+n,e.extent[3]=e.extent[1]+o}upsampleTile(e){const t=this._upsampleLevels[e.level];return!(!t||-1===t.parentLevel||(e.level=t.parentLevel,e.row=Math.floor(e.row/t.factor+.001),e.col=Math.floor(e.col/t.factor+.001),this.updateTileInfo(e),0))}getTileBounds(e,t){const s=this.lodAt(t.level);if(null==s)return null;const{resolution:r}=s,i=r*this.size[0],n=r*this.size[1];return e[0]=this.origin.x+t.col*i,e[1]=this.origin.y-(t.row+1)*n,e[2]=e[0]+i,e[3]=e[1]+n,e}lodAt(e){return this._levelToLOD?.[e]??null}clone(){return r.fromJSON(this.write({}))}getOrCreateCompatible(e,t){if(256===this.size[0]&&256===this.size[1])return 256===e?this:null;const s=[],i=this.lods.length;for(let e=0;e<i;e++){const r=this.lods[e],i=r.resolution*t;s.push(new I.Z({level:r.level,scale:r.scale,resolution:i}))}return new r({size:[e,e],dpi:this.dpi,format:this.format,compressionQuality:this.compressionQuality,origin:this.origin,spatialReference:this.spatialReference,lods:s})}_initializeUpsampleLevels(){const e=this.lods;this._upsampleLevels=[];let t=null;for(let s=0;s<e.length;s++){const r=e[s];this._upsampleLevels[r.level]={parentLevel:t?t.level:-1,factor:t?t.resolution/r.resolution:0},t=r}}};var w,S;(0,i._)([(0,l.Cb)({type:Number,json:{write:!0}})],x.prototype,"compressionQuality",void 0),(0,i._)([(0,l.Cb)({type:Number,json:{write:!0}})],x.prototype,"dpi",void 0),(0,i._)([(0,l.Cb)({type:String,json:{read:v.read,write:v.write,origins:{"web-scene":{read:!1,write:!1}}}})],x.prototype,"format",void 0),(0,i._)([(0,l.Cb)({readOnly:!0})],x.prototype,"isWrappable",null),(0,i._)([(0,l.Cb)({type:p.Z,json:{write:!0}})],x.prototype,"origin",void 0),(0,i._)([(0,c.r)("origin")],x.prototype,"readOrigin",null),(0,i._)([(0,l.Cb)({type:[I.Z],value:null,json:{write:!0}})],x.prototype,"lods",null),(0,i._)([(0,l.Cb)({readOnly:!0})],x.prototype,"minScale",void 0),(0,i._)([(0,l.Cb)({readOnly:!0})],x.prototype,"maxScale",void 0),(0,i._)([(0,l.Cb)({readOnly:!0})],x.prototype,"scales",void 0),(0,i._)([(0,l.Cb)({cast:e=>Array.isArray(e)?e:"number"==typeof e?[e,e]:[256,256]})],x.prototype,"size",void 0),(0,i._)([(0,c.r)("size",["rows","cols"])],x.prototype,"readSize",null),(0,i._)([(0,g.c)("size",{cols:{type:u.z8},rows:{type:u.z8}})],x.prototype,"writeSize",null),(0,i._)([(0,l.Cb)({type:f.Z,json:{write:!0}})],x.prototype,"spatialReference",void 0),x=r=(0,i._)([(0,d.j)("esri.layers.support.TileInfo")],x),w=x||(x={}),(S=w.ExtrapolateOptions||(w.ExtrapolateOptions={}))[S.NONE=0]="NONE",S[S.POWER_OF_TWO=1]="POWER_OF_TWO";const C=x},43077:(e,t,s)=>{s.d(t,{f:()=>r});class r{constructor(e,t,s,r,i){this.id=e,this.level=t,this.row=s,this.col=r,this.extent=i}}},66677:(e,t,s)=>{s.d(t,{B5:()=>c,DR:()=>g,G:()=>b,M8:()=>_,Nm:()=>m,Qc:()=>d,XG:()=>y,a7:()=>f,ld:()=>p,wH:()=>I});var r=s(70586),i=s(17452),n=s(25929);const o={mapserver:"MapServer",imageserver:"ImageServer",featureserver:"FeatureServer",sceneserver:"SceneServer",streamserver:"StreamServer",vectortileserver:"VectorTileServer"},a=Object.values(o),h=new RegExp(`^((?:https?:)?\\/\\/\\S+?\\/rest\\/services\\/(.+?)\\/(${a.join("|")}))(?:\\/(?:layers\\/)?(\\d+))?`,"i"),l=new RegExp(`^((?:https?:)?\\/\\/\\S+?\\/([^\\/\\n]+)\\/(${a.join("|")}))(?:\\/(?:layers\\/)?(\\d+))?`,"i"),u=/(.*?)\/(?:layers\/)?(\d+)\/?$/i;function c(e){return!!h.test(e)}function d(e){if((0,r.Wi)(e))return null;const t=(0,i.mN)(e),s=t.path.match(h)||t.path.match(l);if(!s)return null;const[,n,a,u,c]=s,d=a.indexOf("/");return{title:p(-1!==d?a.slice(d+1):a),serverType:o[u.toLowerCase()],sublayer:null!=c&&""!==c?parseInt(c,10):null,url:{path:n}}}function g(e){const t=(0,i.mN)(e).path.match(u);return t?{serviceUrl:t[1],sublayerId:Number(t[2])}:null}function p(e){return(e=e.replace(/\s*[/_]+\s*/g," "))[0].toUpperCase()+e.slice(1)}function f(e,t){const s=[];if(e){const t=d(e);(0,r.pC)(t)&&t.title&&s.push(t.title)}if(t){const e=p(t);s.push(e)}if(2===s.length){if(s[0].toLowerCase().includes(s[1].toLowerCase()))return s[0];if(s[1].toLowerCase().includes(s[0].toLowerCase()))return s[1]}return s.join(" - ")}function _(e){if(!e)return!1;const t=(e=e.toLowerCase()).includes(".arcgis.com/"),s=e.includes("//services")||e.includes("//tiles")||e.includes("//features");return t&&s}function m(e,t){return e?(0,i.Qj)((0,i.Hu)(e,t)):e}function y(e){let{url:t}=e;if(!t)return{url:t};t=(0,i.Hu)(t,e.logger);const s=(0,i.mN)(t),n=d(s.path);let o;if((0,r.pC)(n))null!=n.sublayer&&null==e.layer.layerId&&(o=n.sublayer),t=n.url.path;else if(e.nonStandardUrlAllowed){const e=g(s.path);(0,r.pC)(e)&&(t=e.serviceUrl,o=e.sublayerId)}return{url:(0,i.Qj)(t),layerId:o}}function I(e,t,s,r,o){(0,n.w)(t,r,"url",o),r.url&&null!=e.layerId&&(r.url=(0,i.v_)(r.url,s,e.layerId.toString()))}function b(e){if(!e)return!1;const t=e.toLowerCase(),s=t.includes("/services/"),r=t.includes("/mapserver/wmsserver"),i=t.includes("/imageserver/wmsserver"),n=t.includes("/wmsserver");return s&&(r||i||n)}},28694:(e,t,s)=>{s.d(t,{p:()=>n});var r=s(70586),i=s(69285);function n(e,t,s){if(!s||!s.features||!s.hasZ)return;const n=(0,i.k)(s.geometryType,t,e.outSpatialReference);if(!(0,r.Wi)(n))for(const e of s.features)n(e.geometry)}},58333:(e,t,s)=>{s.d(t,{ET:()=>n,I4:()=>i,eG:()=>h,lF:()=>o,lj:()=>u,qP:()=>a,wW:()=>l});const r=[252,146,31,255],i={type:"esriSMS",style:"esriSMSCircle",size:6,color:r,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},n={type:"esriSLS",style:"esriSLSSolid",width:.75,color:r},o={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},a={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},h={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},l={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},u={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}},61027:(e,t,s)=>{s.d(t,{KS:()=>l,PX:()=>n,QS:()=>c,_I:()=>r,jL:()=>h,nE:()=>u,vs:()=>a,xp:()=>o});const r=8388607,i=8388608,n=0,o=1,a=e=>(e&i)>>>23,h=e=>e&r,l=e=>a(e)===o?254:255;function u(e){return a(e)===o}function c(e,t){return((t?i:0)|e)>>>0}},26899:(e,t,s)=>{s.d(t,{$_:()=>F,UK:()=>A,ws:()=>T,xV:()=>M});var r=s(20102),i=s(92604),n=s(95648),o=s(66039),a=s(35371);const h=i.Z.getLogger("esri.views.2d.engine.webgl.Utils"),l="geometry",u=[{name:l,strideInBytes:12}],c=[{name:l,strideInBytes:36}],d=[{name:l,strideInBytes:24}],g=[{name:l,strideInBytes:12}],p=[{name:l,strideInBytes:40}],f=[{name:l,strideInBytes:36}],_=[{name:l,strideInBytes:36}];function m(e){const t={};for(const s of e)t[s.name]=s.strideInBytes;return t}const y=m([{name:l,strideInBytes:36}]),I=m(u),b=m(c),v=m(d),x=m(g),w=m(p),S=m(f),C=m(_);function F(e,t){switch(e){case o.LW.MARKER:return t===o.mD.HEATMAP?I:y;case o.LW.FILL:switch(t){case o.mD.DOT_DENSITY:return x;case o.mD.SIMPLE:case o.mD.OUTLINE_FILL_SIMPLE:return v;default:return b}case o.LW.LINE:return w;case o.LW.TEXT:return S;case o.LW.LABEL:return C}}function T(e){switch(e){case"butt":return n.RL.BUTT;case"round":return n.RL.ROUND;case"square":return n.RL.SQUARE;default:return h.error(new r.Z("mapview-invalid-type",`Cap type ${e} is not a valid option. Defaulting to round`)),n.RL.ROUND}}function M(e){switch(e){case"miter":return n.AH.MITER;case"bevel":return n.AH.BEVEL;case"round":return n.AH.ROUND;default:return h.error(new r.Z("mapview-invalid-type",`Join type ${e} is not a valid option. Defaulting to round`)),n.AH.ROUND}}function A(e){switch(e){case a.Br.UNSIGNED_BYTE:return Uint8Array;case a.Br.UNSIGNED_SHORT_4_4_4_4:return Uint16Array;case a.Br.FLOAT:return Float32Array;default:return void h.error(new r.Z("webgl-utils",`Unable to handle type ${e}`))}}a.l1.STATIC_DRAW,new Map},37720:(e,t,s)=>{s.d(t,{aH:()=>n,t2:()=>i});var r=s(12142);function i(e){if(!e)return 0;const{r:t,g:s,b:i,a:n}=e;return(0,r.Jz)(t*n,s*n,i*n,255*n)}function n(e){if(!e)return 0;const[t,s,i,n]=e;return(0,r.Jz)(t*(n/255),s*(n/255),i*(n/255),n)}},66039:(e,t,s)=>{var r,i,n,o,a,h,l;s.d(t,{LW:()=>r,X:()=>o,mD:()=>l,mf:()=>a}),function(e){e[e.FILL=0]="FILL",e[e.LINE=1]="LINE",e[e.MARKER=2]="MARKER",e[e.TEXT=3]="TEXT",e[e.LABEL=4]="LABEL"}(r||(r={})),function(e){e[e.NONE=0]="NONE",e[e.MAP=1]="MAP",e[e.LABEL=2]="LABEL",e[e.LABEL_ALPHA=4]="LABEL_ALPHA",e[e.HITTEST=8]="HITTEST",e[e.HIGHLIGHT=16]="HIGHLIGHT",e[e.CLIP=32]="CLIP",e[e.DEBUG=64]="DEBUG",e[e.NUM_DRAW_PHASES=9]="NUM_DRAW_PHASES"}(i||(i={})),function(e){e[e.SIZE=0]="SIZE",e[e.COLOR=1]="COLOR",e[e.OPACITY=2]="OPACITY",e[e.ROTATION=3]="ROTATION"}(n||(n={})),function(e){e[e.NONE=0]="NONE",e[e.OPACITY=1]="OPACITY",e[e.COLOR=2]="COLOR",e[e.ROTATION=4]="ROTATION",e[e.SIZE_MINMAX_VALUE=8]="SIZE_MINMAX_VALUE",e[e.SIZE_SCALE_STOPS=16]="SIZE_SCALE_STOPS",e[e.SIZE_FIELD_STOPS=32]="SIZE_FIELD_STOPS",e[e.SIZE_UNIT_VALUE=64]="SIZE_UNIT_VALUE"}(o||(o={})),function(e){e[e.MINMAX_TARGETS_OUTLINE=128]="MINMAX_TARGETS_OUTLINE",e[e.SCALE_TARGETS_OUTLINE=256]="SCALE_TARGETS_OUTLINE",e[e.FIELD_TARGETS_OUTLINE=512]="FIELD_TARGETS_OUTLINE",e[e.UNIT_TARGETS_OUTLINE=1024]="UNIT_TARGETS_OUTLINE"}(a||(a={})),function(e){e[e.SPRITE=0]="SPRITE",e[e.GLYPH=1]="GLYPH"}(h||(h={})),function(e){e[e.DEFAULT=0]="DEFAULT",e[e.SIMPLE=1]="SIMPLE",e[e.DOT_DENSITY=2]="DOT_DENSITY",e[e.OUTLINE_FILL=3]="OUTLINE_FILL",e[e.OUTLINE_FILL_SIMPLE=4]="OUTLINE_FILL_SIMPLE",e[e.HEATMAP=5]="HEATMAP",e[e.PIE_CHART=6]="PIE_CHART"}(l||(l={}))},12142:(e,t,s)=>{s.d(t,{Jz:()=>n,UJ:()=>i});const r=new Float32Array(1);function i(e,t){return 65535&e|t<<16}function n(e,t,s,r){return 255&e|(255&t)<<8|(255&s)<<16|r<<24}new Uint32Array(r.buffer)},74259:(e,t,s)=>{s.d(t,{k:()=>p,p:()=>f});var r=s(8557),i=s(32448),n=(s(80442),s(70586)),o=s(96794),a=s(60437),h=s(59999),l=s(61800);const u=(0,a.Ue)();function c(e,t){return e<<16|t}function d(e){return(4294901760&e)>>>16}function g(e){return 65535&e}const p={getObjectId:e=>e.getObjectId(),getAttributes:e=>e.readAttributes(),getAttribute:(e,t)=>e.readAttribute(t),cloneWithGeometry:(e,t)=>e,getGeometry:e=>e.readHydratedGeometry(),getCentroid:(e,t)=>e.readCentroid()};class f extends h.J{constructor(e,t,s){super(e,t),this.featureAdapter=p,this.events=new i.Z,this._featureSetsByInstance=new Map,this._objectIdToDisplayId=new Map,this._spatialIndexInvalid=!0,this._indexSearchCache=new r.Z(50),this._index=(0,o.r)(9,(e=>({minX:this._storage.getXMin(e),minY:this._storage.getYMin(e),maxX:this._storage.getXMax(e),maxY:this._storage.getYMax(e)}))),this.mode=s}get storeStatistics(){let e=0,t=0,s=0;return this.forEach((r=>{const i=r.readGeometry();i&&(t+=i.isPoint?1:i.lengths.reduce(((e,t)=>e+t),0),s+=i.isPoint?1:i.lengths.length,e+=1)})),{featureCount:e,vertexCount:t,ringCount:s}}hasInstance(e){return this._featureSetsByInstance.has(e)}onTileData(e,t){if((0,n.Wi)(t.addOrUpdate))return t;if(t.addOrUpdate.attachStorage(this._storage),"snapshot"===this.mode){const s=t.addOrUpdate.getCursor();for(;s.next();){const t=s.getDisplayId();this.setComputedAttributes(this._storage,s,t,e.scale)}return t}this._featureSetsByInstance.set(t.addOrUpdate.instance,t.addOrUpdate);const s=t.addOrUpdate.getCursor();for(;s.next();)this._insertFeature(s,e.scale);return this._spatialIndexInvalid=!0,this.events.emit("changed"),t}search(e){this._rebuildIndex();const t=e.id,s=this._indexSearchCache.find((e=>e.tileId===t));if((0,n.pC)(s))return s.readers;const r=new Map,i=this._searchIndex(e.bounds),o=[];for(const e of i){const t=this._storage.getInstanceId(e),s=d(t),i=g(t);r.has(s)||r.set(s,[]),r.get(s).push(i)}return r.forEach(((e,t)=>{const s=this._featureSetsByInstance.get(t);o.push(l.t.from(s,e))})),this._indexSearchCache.enqueue({tileId:t,readers:o}),o}insert(e){const t=e.getCursor(),s=this._storage;for(;t.next();){const e=c(t.instance,t.getIndex()),r=t.getObjectId(),i=this._objectIdToDisplayId.get(r)??this._storage.createDisplayId();t.setDisplayId(i),s.setInstanceId(i,e),this._objectIdToDisplayId.set(r,i)}this._featureSetsByInstance.set(e.instance,e),this._spatialIndexInvalid=!0}remove(e){const t=this._objectIdToDisplayId.get(e);if(!t)return;const s=this._storage.getInstanceId(t),r=g(s),i=d(s),n=this._featureSetsByInstance.get(i);this._objectIdToDisplayId.delete(e),this._storage.releaseDisplayId(t),n.removeAtIndex(r),n.isEmpty&&this._featureSetsByInstance.delete(i),this._spatialIndexInvalid=!0}forEach(e){this._objectIdToDisplayId.forEach((t=>{const s=this._storage.getInstanceId(t),r=this._lookupFeature(s);e(r)}))}forEachUnsafe(e){this._objectIdToDisplayId.forEach((t=>{const s=this._storage.getInstanceId(t),r=d(s),i=g(s),n=this._getFeatureSet(r);n.setIndex(i),e(n)}))}forEachInBounds(e,t){const s=this._searchIndex(e);for(const e of s){const s=this.lookupFeatureByDisplayId(e,this._storage);t((0,n.Wg)(s))}}forEachBounds(e,t){this._rebuildIndex();for(const s of e){if(!s.readGeometry())continue;const e=s.getDisplayId();(0,a.bZ)(u,this._storage.getXMin(e),this._storage.getYMin(e),this._storage.getXMax(e),this._storage.getYMax(e)),t(u)}}sweepFeatures(e,t,s){this._spatialIndexInvalid=!0,this._objectIdToDisplayId.forEach(((r,i)=>{e.has(r)||(t.releaseDisplayId(r),s&&s.unsetAttributeData(r),this._objectIdToDisplayId.delete(i))})),this.events.emit("changed")}sweepFeatureSets(e){this._spatialIndexInvalid=!0,this._featureSetsByInstance.forEach(((t,s)=>{e.has(s)||this._featureSetsByInstance.delete(s)}))}lookupObjectId(e,t){const s=this.lookupFeatureByDisplayId(e,t);return(0,n.Wi)(s)?null:s.getObjectId()}lookupDisplayId(e){return this._objectIdToDisplayId.get(e)}lookupFeatureByDisplayId(e,t){const s=t.getInstanceId(e);return this._lookupFeature(s)}lookupByDisplayIdUnsafe(e){const t=this._storage.getInstanceId(e),s=d(t),r=g(t),i=this._getFeatureSet(s);return i?(i.setIndex(r),i):null}_insertFeature(e,t){const s=this._storage,r=e.getObjectId(),i=c(e.instance,e.getIndex());s.getInstanceId(e.getDisplayId());let n=this._objectIdToDisplayId.get(r);n||(n=s.createDisplayId(),this._objectIdToDisplayId.set(r,n),this._spatialIndexInvalid=!0),e.setDisplayId(n),s.setInstanceId(n,i),this.setComputedAttributes(s,e,n,t)}_searchIndex(e){this._rebuildIndex();const t={minX:e[0],minY:e[1],maxX:e[2],maxY:e[3]};return this._index.search(t)}_rebuildIndex(){if(!this._spatialIndexInvalid)return;const e=[];"snapshot"===this.mode?this._featureSetsByInstance.forEach((t=>{const s=t.getCursor();for(;s.next();){const t=s.getDisplayId();this._storage.setBounds(t,s)&&e.push(t)}})):this._objectIdToDisplayId.forEach((t=>{const s=this._storage.getInstanceId(t);this._storage.setBounds(t,this._lookupFeature(s))&&e.push(t)})),this._index.clear(),this._index.load(e),this._indexSearchCache.clear(),this._spatialIndexInvalid=!1}_lookupFeature(e){const t=d(e),s=this._getFeatureSet(t);if(!s)return;const r=s.getCursor(),i=g(e);return r.setIndex(i),r}_getFeatureSet(e){return this._featureSetsByInstance.get(e)}}},92885:(e,t,s)=>{s.r(t),s.d(t,{default:()=>It});var r=s(43697),i=s(3920),n=s(80442),o=s(17445),a=s(5600),h=(s(75215),s(67676),s(52011)),l=s(8744),u=s(11145),c=s(15923),d=s(70586),g=s(95330),p=s(98732),f=s(50245),_=s(99514),m=s(74259),y=s(66677),I=s(3172),b=s(20102),v=s(92604),x=s(80903),w=s(82397),S=s(16467),C=s(34599),F=s(70272),T=s(87554);class M extends T.s{static fromFeatures(e,t){const{objectIdField:s,geometryType:r}=t,i=(0,p.Yn)([],e,r,!1,!1,s);for(let t=0;t<i.length;t++)i[t].displayId=e[t].displayId;return M.fromOptimizedFeatures(i,t)}static fromFeatureSet(e,t){const s=(0,p.h_)(e,t.objectIdField);return M.fromOptimizedFeatureSet(s,t)}static fromOptimizedFeatureSet(e,t){const{features:s}=e,r=M.fromOptimizedFeatures(s,t);r._exceededTransferLimit=e.exceededTransferLimit,r._transform=e.transform;for(const t of e.fields)"esriFieldTypeDate"===t.type&&r._dateFields.add(t.name);return r}static fromOptimizedFeatures(e,t,s){const r=T.s.createInstance(),i=new M(r,e,t);return i._transform=s,i}constructor(e,t,s){super(e,s),this._exceededTransferLimit=!1,this._featureIndex=-1,this._dateFields=new Set,this._geometryType=s?.geometryType,this._features=t}get _current(){return this._features[this._featureIndex]}get geometryType(){return this._geometryType}get hasFeatures(){return!!this._features.length}get hasNext(){return this._featureIndex+1<this._features.length}get exceededTransferLimit(){return this._exceededTransferLimit}get hasZ(){return!1}get hasM(){return!1}removeIds(e){const t=new Set(e);this._features=this._features.filter((e=>!(e.objectId&&t.has(e.objectId))))}append(e){for(const t of e)this._features.push(t)}getSize(){return this._features.length}getCursor(){return this.copy()}getQuantizationTransform(){return this._transform}getAttributeHash(){let e="";for(const t in this._current.attributes)e+=this._current.attributes[t];return e}getIndex(){return this._featureIndex}setIndex(e){this._featureIndex=e}getObjectId(){return this._current?.objectId}getDisplayId(){return this._current.displayId}setDisplayId(e){this._current.displayId=e}getGroupId(){return this._current.groupId}setGroupId(e){this._current.groupId=e}copy(){const e=new M(this.instance,this._features,this.fullSchema());return this.copyInto(e),e}next(){for(;++this._featureIndex<this._features.length&&!this._getExists(););return this._featureIndex<this._features.length}readLegacyFeature(){return(0,p.EI)(this._current,this.geometryType,this.hasZ,this.hasM)}readOptimizedFeature(){return this._current}readLegacyPointGeometry(){return this.readGeometry()?{x:this.getX(),y:this.getY()}:null}readLegacyGeometry(){const e=this.readGeometry();return(0,p.di)(e,this.geometryType,this.hasZ,this.hasM)}readLegacyCentroid(){const e=this.readCentroid();return(0,d.Wi)(e)?null:{x:e.coords[0]*this._sx+this._tx,y:e.coords[1]*this._sy+this._ty}}readGeometryArea(){return(0,F.S6)(this._current)?(0,p.lz)(this._current.geometry,2):0}readUnquantizedGeometry(){const e=this.readGeometry();if("esriGeometryPoint"===this.geometryType||!e)return e;const t=e.clone();return function({coords:e,lengths:t}){let s=0;for(const r of t){for(let t=1;t<r;t++)e[2*(s+t)]+=e[2*(s+t)-2],e[2*(s+t)+1]+=e[2*(s+t)-1];s+=r}}(t),t}readHydratedGeometry(){const e=this._current.geometry;if((0,d.Wi)(e))return null;const t=e.clone();return(0,d.pC)(this._transform)&&(0,p.$g)(t,t,this.hasZ,this.hasM,this._transform),t}getXHydrated(){if(!(0,F.S6)(this._current))return 0;const e=this._current.geometry.coords[0],t=this.getQuantizationTransform();return(0,d.Wi)(t)?e:e*t.scale[0]+t.translate[0]}getYHydrated(){if(!(0,F.S6)(this._current))return 0;const e=this._current.geometry.coords[1],t=this.getQuantizationTransform();return(0,d.Wi)(t)?e:t.translate[1]-e*t.scale[1]}getX(){return(0,F.S6)(this._current)?this._current.geometry.coords[0]*this._sx+this._tx:0}getY(){return(0,F.S6)(this._current)?this._current.geometry.coords[1]*this._sy+this._ty:0}readGeometry(){if(!(0,F.S6)(this._current)){if((0,d.pC)(this._current.centroid)){const[e,t]=this._current.centroid.coords;return this.createQuantizedExtrudedQuad(e,t)}return null}const e=this._current.geometry.clone();if(e.isPoint)return e.coords[0]=e.coords[0]*this._sx+this._tx,e.coords[1]=e.coords[1]*this._sy+this._ty,e;let t=0;for(const s of e.lengths)e.coords[2*t]=e.coords[2*t]*this._sx+this._tx,e.coords[2*t+1]=e.coords[2*t+1]*this._sy+this._ty,t+=s;return e}readCentroid(){return(0,F.S6)(this._current)?this._computeCentroid():this._current.centroid}hasField(e){return e in this._current.attributes||this.getFieldNames().map((e=>e.toLowerCase())).includes(e.toLowerCase())}getFieldNames(){return Object.keys(this._current.attributes)}_readAttribute(e,t){const s=this._current.attributes[e];if(void 0!==s)return null!=s&&t&&this._dateFields.has(e)?new Date(s):s;const r=this.readAttributes(),i=e?.toLocaleLowerCase().trim();for(const e in r)if(e.toLocaleLowerCase().trim()===i){const s=this._current.attributes[e];return null!=s&&t&&this._dateFields.has(e)?new Date(s):s}}copyInto(e){super.copyInto(e),e._featureIndex=this._featureIndex,e._transform=this._transform,e._dateFields=this._dateFields}_readAttributes(){return this._current.attributes}}var A=s(45091),k=s(5428),E=s(77863);const R=268435455;class L{constructor(){this.fieldMap=new Map,this.fields=[],this.hasFeatures=!1,this.exceededTransferLimit=!1,this.fieldCount=0,this.featureCount=0,this.objectIdFieldIndex=0,this.vertexCount=0,this.offsets={attributes:new Array,geometry:new Array},this.centroid=new Array}hasField(e){return this.fieldMap.has(e)}isDateField(e){return(null!=e&&this.fieldMap.get(e)?.isDate)??!1}getFieldIndex(e){return null!=e?this.fieldMap.get(e)?.index:void 0}}function O(e){const t=e.asUnsafe(),s=t.getLength(),r=t.pos()+s,i={name:"",isDate:!1};for(;t.pos()<r&&t.next();)switch(t.tag()){case 1:i.name=t.getString();break;case 2:"esriFieldTypeDate"===(0,E.O7)(t.getEnum())&&(i.isDate=!0);break;default:t.skip()}return i}function P(e){return e.toLowerCase().trim()}function U(e,t,s=!1){const r=e.asUnsafe(),i=r.pos(),n=new L;let o=0,a=0,h=null,l=null,u=null,c=!1;for(;r.next();)switch(r.tag()){case 1:h=r.getString();break;case 3:l=r.getString();break;case 12:u=r.processMessage(E.G$);break;case 9:if(n.exceededTransferLimit=r.getBool(),n.exceededTransferLimit){n.offsets.geometry=s?new Float64Array(8e3):new Int32Array(8e3),n.centroid=s?new Float64Array(16e3):new Int32Array(16e3);for(let e=0;e<n.centroid.length;e++)n.centroid[e]=R}break;case 13:{const t=O(e),s=t.name,r=P(t.name),i={fieldName:s,index:o++,isDate:t.isDate};n.fields.push(i),n.fieldMap.set(t.name,i),n.fieldMap.set(r,i);break}case 15:{const e=r.getLength(),i=r.pos()+e;if(!n.exceededTransferLimit){const e=n.offsets.geometry,t=n.centroid;e.push(0),t.push(R),t.push(R)}!c&&n.exceededTransferLimit&&(c=!0,n.offsets.attributes=s?new Float64Array(8e3*o):new Uint32Array(8e3*o));let h=a*o;for(;r.pos()<i&&r.next();)switch(r.tag()){case 1:{c?n.offsets.attributes[h++]=r.pos():n.offsets.attributes.push(r.pos());const e=r.getLength();r.skipLen(e);break}case 2:if(t){const e=r.getLength(),t=r.pos()+e;for(;r.pos()<t&&r.next();)switch(r.tag()){case 3:{r.getUInt32();const e=r.getSInt64(),t=r.getSInt64();n.centroid[2*a]=e,n.centroid[2*a+1]=t;break}default:r.skip()}}else{n.offsets.geometry[a]=r.pos();const e=r.getLength();n.vertexCount+=e,r.skipLen(e)}break;case 4:{const e=r.getLength(),t=r.pos()+e;for(;r.pos()<t&&r.next();)switch(r.tag()){case 3:{r.getUInt32();const e=r.getSInt64(),t=r.getSInt64();n.centroid[2*a]=e,n.centroid[2*a+1]=t;break}default:r.skip()}break}default:r.skip()}a++,n.hasFeatures=!0;break}default:r.skip()}const d=h||l;if(!d)throw new b.Z("FeatureSet has no objectId or globalId field name");return n.featureCount=a,n.fieldCount=o,n.objectIdFieldIndex=n.getFieldIndex(d),n.transform=u,n.displayIds=new Uint32Array(n.featureCount),n.groupIds=new Uint16Array(n.featureCount),r.move(i),n}const Z=268435455,B=128e3,N={small:{delta:new Int32Array(128),decoded:new Int32Array(128)},large:{delta:new Int32Array(B),decoded:new Int32Array(B)}};function D(e){return e<=N.small.delta.length?N.small:(e<=N.large.delta.length||(N.large.delta=new Int32Array(Math.round(1.25*e)),N.large.decoded=new Int32Array(Math.round(1.25*e))),N.large)}function z(e){return e.toLowerCase().trim()}function q(e){for(;e.next();){if(1===e.tag())return e.getMessage();e.skip()}return null}function j(e,t,s,r,i,n){return.5*Math.abs(e*r+s*n+i*t-e*n-s*t-i*r)}function G(e,t,s,r){return 0==e*r-s*t&&e*s+t*r>0}class W extends T.s{static fromBuffer(e,t,s=!1){const r=t.geometryType,i=function(e){try{const t=2,s=new A.Z(new Uint8Array(e),new DataView(e));for(;s.next();){if(s.tag()===t)return q(s.getMessage());s.skip()}}catch(e){const t=new b.Z("query:parsing-pbf","Error while parsing FeatureSet PBF payload",{error:e});v.Z.getLogger("esri.view.2d.layers.features.support.FeatureSetReaderPBF").error(t)}return null}(e),n=U(i,"esriGeometryPoint"===r,s),o=T.s.createInstance();return new W(o,i,n,t)}constructor(e,t,s,r){super(e,r),this._hasNext=!1,this._isPoints=!1,this._featureIndex=-1,this._featureOffset=0,this._cache={area:0,unquantGeometry:void 0,geometry:void 0,centroid:void 0,legacyFeature:void 0,optFeature:void 0},this._geometryType=r.geometryType,this._reader=t,this._header=s,this._hasNext=s.hasFeatures,this._isPoints="esriGeometryPoint"===r.geometryType}get geometryType(){return this._geometryType}get _size(){return this._header.featureCount}get hasZ(){return!1}get hasM(){return!1}get stride(){return 2+(this.hasZ?1:0)+(this.hasM?1:0)}get hasFeatures(){return this._header.hasFeatures}get hasNext(){return this._hasNext}get exceededTransferLimit(){return this._header.exceededTransferLimit}hasField(e){return this._header.hasField(e)||this._header.hasField(z(e))}getFieldNames(){return this._header.fields.map((e=>e.fieldName))}getSize(){return this._size}getQuantizationTransform(){return this._header.transform}getCursor(){return this.copy()}getIndex(){return this._featureIndex}setIndex(e){this._cache.area=0,this._cache.unquantGeometry=void 0,this._cache.geometry=void 0,this._cache.centroid=void 0,this._cache.legacyFeature=void 0,this._cache.optFeature=void 0,this._featureIndex=e}getAttributeHash(){let e="";return this._header.fields.forEach((({index:t})=>{e+=this._readAttributeAtIndex(t)+"."})),e}getObjectId(){return this._readAttributeAtIndex(this._header.objectIdFieldIndex)}getDisplayId(){return this._header.displayIds[this._featureIndex]}setDisplayId(e){this._header.displayIds[this._featureIndex]=e}getGroupId(){return this._header.groupIds[this._featureIndex]}setGroupId(e){this._header.groupIds[this._featureIndex]=e}readLegacyFeature(){if(void 0===this._cache.legacyFeature){const e=this.readCentroid(),t={attributes:this.readAttributes(),geometry:this._isPoints?this.readLegacyPointGeometry():this.readLegacyGeometry(),centroid:(e&&{x:e.coords[0],y:e.coords[1]})??null};return this._cache.legacyFeature=t,t}return this._cache.legacyFeature}readOptimizedFeature(){if(void 0===this._cache.optFeature){const e=new F.u_(this.readGeometry(),this.readAttributes(),this.readCentroid());return e.objectId=this.getObjectId(),e.displayId=this.getDisplayId(),this._cache.optFeature=e,e}return this._cache.optFeature}getXHydrated(){const e=this._header.centroid[2*this._featureIndex],t=this.getQuantizationTransform();return(0,d.Wi)(t)?e:e*t.scale[0]+t.translate[0]}getYHydrated(){const e=this._header.centroid[2*this._featureIndex+1],t=this.getQuantizationTransform();return(0,d.Wi)(t)?e:t.translate[1]-e*t.scale[1]}getX(){return this._header.centroid[2*this._featureIndex]*this._sx+this._tx}getY(){return this._header.centroid[2*this._featureIndex+1]*this._sy+this._ty}readLegacyPointGeometry(){return{x:this.getX(),y:this.getY()}}readLegacyGeometry(e){const t=this.readGeometry(e);return(0,p.di)(t,this.geometryType,!1,!1)}readLegacyCentroid(){const e=this.readCentroid();if(!e)return null;const[t,s]=e.coords;return{x:t,y:s}}readGeometryArea(){return this._cache.area||this.readGeometry(!0),this._cache.area}readUnquantizedGeometry(e=!1){if(void 0===this._cache.unquantGeometry){const t=this.readGeometry(e);if(!t)return this._cache.unquantGeometry=void 0,null;const s=D(t.coords.length).decoded,r=t.clone(s),i=r.coords;let n=0;for(const e of r.lengths){for(let t=1;t<e;t++){const e=2*(n+t),s=2*(n+t-1);i[e]+=i[s],i[e+1]+=i[s+1]}n+=e}return this._cache.unquantGeometry=r,r}return this._cache.unquantGeometry}readHydratedGeometry(){if(this._isPoints){if(this._header.centroid[2*this._featureIndex]===Z)return null;const e=this.getXHydrated(),t=this.getYHydrated();return new k.Z([],[e,t])}const e=this.readGeometry();if(!e)return null;const t=e.clone(),s=this.getQuantizationTransform();return(0,d.pC)(s)&&(0,p.$g)(t,t,this.hasZ,this.hasM,s),t}readGeometry(e=!1){if(void 0===this._cache.geometry){let t=null;if(this._isPoints){if(this._header.centroid[2*this._featureIndex]===Z)return null;const e=this.getX(),s=this.getY();t=new k.Z([],[e,s])}else{const s=this._header.offsets.geometry[this._featureIndex],r=this._reader;if(0===s){const e=this._readServerCentroid();if(!e)return null;const[t,s]=e.coords;return this.createQuantizedExtrudedQuad(t,s)}r.move(s);try{if(t=e?this._parseGeometryForDisplay(r):this._parseGeometry(r),null===t){const e=this._readServerCentroid();if(!e)return null;const[t,s]=e.coords;return this.createQuantizedExtrudedQuad(t,s)}}catch(e){return console.error("Failed to parse geometry!",e),null}}return this._cache.geometry=t,t}return this._cache.geometry}readCentroid(){if(void 0===this._cache.centroid){let e;return e=this._computeCentroid(),e||(e=this._readServerCentroid()),this._cache.centroid=e??void 0,e??null}return this._cache.centroid}copy(){const e=this._reader.clone(),t=new W(this.instance,e,this._header,this.fullSchema());return this.copyInto(t),t}next(){for(this._cache.area=0,this._cache.unquantGeometry=void 0,this._cache.geometry=void 0,this._cache.centroid=void 0,this._cache.legacyFeature=void 0,this._cache.optFeature=void 0;++this._featureIndex<this._size&&!this._getExists(););return this._featureIndex<this._size}_readAttribute(e,t){const s=this._header.hasField(e)?e:z(e),r=this._header.getFieldIndex(s);if(null==r)return;const i=this._readAttributeAtIndex(r);return t?null==i?i:this._header.isDateField(s)?new Date(i):i:i}_readAttributes(){const e={};return this._header.fields.forEach((({fieldName:t,index:s})=>{e[t]=this._readAttributeAtIndex(s)})),e}copyInto(e){super.copyInto(e),e._featureIndex=this._featureIndex,e._featureOffset=this._featureOffset,e._hasNext=this._hasNext}_readAttributeAtIndex(e){const t=this._header.offsets.attributes[this._featureIndex*this._header.fieldCount+e],s=this._reader;return s.move(t),function(e){const t=e.getLength(),s=e.pos()+t;for(;e.pos()<s&&e.next();)switch(e.tag()){case 1:return e.getString();case 2:return e.getFloat();case 3:return e.getDouble();case 4:return e.getSInt32();case 5:return e.getUInt32();case 6:return e.getInt64();case 7:return e.getUInt64();case 8:return e.getSInt64();case 9:return e.getBool();default:return e.skip(),null}return null}(s)}_readServerCentroid(){const e=this._header.centroid[2*this._featureIndex]+this._tx,t=this._header.centroid[2*this._featureIndex+1]+this._ty;return e===Z?null:new k.Z([],[e,t])}_parseGeometry(e){const t=e.asUnsafe(),s=t.getLength(),r=t.pos()+s,i=[],n=[];for(;t.pos()<r&&t.next();)switch(t.tag()){case 2:{const e=t.getUInt32(),s=t.pos()+e;for(;t.pos()<s;)n.push(t.getUInt32());break}case 3:{const e=t.getUInt32(),s=t.pos()+e;for(i.push(t.getSInt32()+this._tx),i.push(t.getSInt32()+this._ty),this.hasZ&&t.getSInt32(),this.hasM&&t.getSInt32();t.pos()<s;)i.push(t.getSInt32()),i.push(t.getSInt32()),this.hasZ&&t.getSInt32(),this.hasM&&t.getSInt32();break}default:t.skip()}return new k.Z(n,i)}_parseGeometryForDisplay(e){const t=e.asUnsafe(),s=t.getLength(),r=t.pos()+s,i=[],n=[];let o=0,a=0,h=null,l=0;const u="esriGeometryPolygon"===this.geometryType;for(;t.pos()<r&&t.next();)switch(t.tag()){case 2:{const e=t.getUInt32(),s=t.pos()+e;for(;t.pos()<s;){const e=t.getUInt32();i.push(e),o+=e}h=D(2*o).delta;break}case 3:{t.getUInt32();const e=2+(this.hasZ?1:0)+(this.hasM?1:0);(0,d.O3)(h);for(const s of i)if(a+e*s>h.length)for(let e=0;e<s;e++)t.getSInt32(),t.getSInt32(),this.hasZ&&t.getSInt32(),this.hasM&&t.getSInt32();else if(u){const e=this.getAreaSimplificationThreshold(s,this._header.vertexCount);let r=2,i=1;const o=!1;let u=t.getSInt32(),c=t.getSInt32();h[a++]=u,h[a++]=c,this.hasZ&&t.getSInt32(),this.hasM&&t.getSInt32();let d=t.getSInt32(),g=t.getSInt32();for(this.hasZ&&t.getSInt32(),this.hasM&&t.getSInt32();r<s;){let s=t.getSInt32(),n=t.getSInt32();this.hasZ&&t.getSInt32(),this.hasM&&t.getSInt32();const o=u+d,p=c+g;j(u,c,o,p,o+s,p+n)>=e?(l+=-.5*(o-u)*(p+c),i>1&&G(h[a-2],h[a-1],d,g)?(h[a-2]+=d,h[a-1]+=g):(h[a++]=d,h[a++]=g,i++),u=o,c=p):(s+=d,n+=g),d=s,g=n,r++}i<3||o?a-=2*i:(l+=-.5*(u+d-u)*(c+g+c),G(h[a-2],h[a-1],d,g)?(h[a-2]+=d,h[a-1]+=g,n.push(i)):(h[a++]=d,h[a++]=g,n.push(++i)))}else{let e=0,r=t.getSInt32(),i=t.getSInt32();this.hasZ&&t.getSInt32(),this.hasM&&t.getSInt32(),h[a++]=r,h[a++]=i,e+=1;for(let n=1;n<s;n++){const s=t.getSInt32(),o=t.getSInt32(),u=r+s,c=i+o;l+=-.5*(u-r)*(c+i),this.hasZ&&t.getSInt32(),this.hasM&&t.getSInt32(),n>2&&G(h[a-2],h[a-1],s,o)?(h[a-2]+=s,h[a-1]+=o):(h[a++]=s,h[a++]=o,e+=1),r=u,i=c}n.push(e)}break}default:t.skip()}if(this._cache.area=l,!n.length)return null;if(this._tx||this._ty){let e=0;(0,d.O3)(h);for(const t of n)h[2*e]+=this._tx,h[2*e+1]+=this._ty,e+=t}return new k.Z(n,h)}}class Y{constructor(e){this.service=e}destroy(){}}class Q extends Y{constructor(e){super(e),this._portsOpen=async function(e){const t=new x.Z;return await t.open(e,{}),t}(e.source).then((e=>this.client=e))}destroy(){this.client.close(),this.client=null}async executeQuery(e,t){await this._portsOpen;const s=await this.client.invoke("queryFeatures",e.toJSON(),t);return M.fromFeatureSet(s,this.service)}}class X extends Y{async executeQuery(e,t){const{data:s}=await(0,C.n7)(this.service.source,e,t),r=!e.quantizationParameters;return W.fromBuffer(s,this.service,r)}}class H extends Y{async executeQuery(e,t){const{source:s,capabilities:r,spatialReference:i,objectIdField:n,geometryType:o}=this.service;if((0,d.pC)(e.quantizationParameters)&&!r.query.supportsQuantization){const r=e.clone(),o=(0,w.vY)((0,d.Wg)(r.quantizationParameters));r.quantizationParameters=null;const{data:a}=await(0,C.JT)(s,r,i,t),h=(0,p.h_)(a,n);return(0,p.RZ)(o,h),M.fromOptimizedFeatureSet(h,this.service)}const{data:a}=await(0,C.JT)(s,e,this.service.spatialReference,t);return"esriGeometryPoint"===o&&(a.features=a.features?.filter((e=>{if((0,d.pC)(e.geometry)){const t=e.geometry;return Number.isFinite(t.x)&&Number.isFinite(t.y)}return!0}))),M.fromFeatureSet(a,this.service)}}class $ extends Y{async executeQuery(e,t){const{capabilities:s}=this.service;if(e.quantizationParameters&&!s.query.supportsQuantization){const s=e.clone(),r=(0,w.vY)((0,d.Wg)(s.quantizationParameters));s.quantizationParameters=null;const i=await(0,S.WW)(this.service.source,e,t);return(0,p.RZ)(r,i),M.fromOptimizedFeatureSet(i,this.service)}const r=await(0,S.WW)(this.service.source,e,t);return M.fromOptimizedFeatureSet(r,this.service)}}var V=s(92835),J=s(32448),K=s(22862),ee=s(14165),te=s(8557);class se{constructor(){this.version=0,this.source=!1,this.targets={feature:!1,aggregate:!1},this.storage={filters:!1,data:!1},this.mesh=!1,this.queryFilter=!1,this.why={mesh:[],source:[]}}static create(e){const t=new se;for(const s in e){const r=e[s];if("object"==typeof r)for(const e in r){const i=r[e];t[s][e]=i}t[s]=r}return t}static empty(){return se.create({})}static all(){return se.create({source:!0,targets:{feature:!0,aggregate:!0},storage:{filters:!0,data:!0},mesh:!0})}unset(e){this.version=e.version,e.source&&(this.source=!1),e.targets.feature&&(this.targets.feature=!1),e.targets.aggregate&&(this.targets.aggregate=!1),e.storage.filters&&(this.storage.filters=!1),e.storage.data&&(this.storage.data=!1),e.mesh&&(this.mesh=!1),e.queryFilter&&(this.queryFilter=!1)}any(){return this.source||this.mesh||this.storage.filters||this.storage.data||this.targets.feature||this.targets.aggregate||this.queryFilter}describe(){let e=0,t="";if(this.mesh){e+=20,t+="-> (20) Mesh needs update\n";for(const e of this.why.mesh)t+=`    + ${e}\n`}if(this.source){e+=10,t+="-> (10) The source needs update\n";for(const e of this.why.source)t+=`    + ${e}\n`}this.targets.feature&&(e+=5,t+="-> (5) Feature target parameters changed\n"),this.storage.filters&&(e+=5,t+="-> (5) Feature filter parameters changed\n"),this.targets.aggregate&&(e+=4,t+="-> (4) Aggregate target parameters changed\n"),this.storage.data&&(e+=1,t+="-> (1) Texture storage parameters changed");const s=e<5?"Fastest":e<10?"Fast":e<15?"Moderate":e<20?"Slow":"Very Slow";console.debug(`Applying ${s} update of cost ${e}/45 `),console.debug(t)}toJSON(){return{queryFilter:this.queryFilter,source:this.source,targets:this.targets,storage:this.storage,mesh:this.mesh}}}class re{constructor(e,t){this.requests={done:new Array,stream:new te.Z(10)},this._edits=null,this._abortController=new AbortController,this._version=0,this._done=!1,this.didSend=!1,this.tile=e,this._version=t}get signal(){return this._abortController.signal}get options(){return{signal:this._abortController.signal}}get empty(){return!this.requests.done.length&&(0,d.Wi)(this.edits)}get edits(){return this._edits}get done(){return this._done}end(){this._done=!0}clear(){this.requests.done=[]}applyUpdate(e){this.requests.done.forEach((t=>t.message.status.unset(e))),this._version=e.version,(0,d.pC)(this._edits)&&this._edits.status.unset(e)}add(e){e.message.status=e.message.status??se.empty(),e.message.status.version=this._version,(0,n.Z)("esri-2d-update-debug")&&console.debug(this.tile.id,"DataTileSubscription:add",this._version),e.message.end&&this.requests.done.forEach((e=>{(0,d.pC)(e.message)&&e.message.end&&(e.message.end=!1)})),this.requests.done.push(e)}edit(e,t){const s=e.getQuantizationTransform(),r=e.fullSchema(),i=Array.from(e.features()).filter(d.pC),n=[...t,...i.map((e=>e.objectId))];this.removeIds(n),this._invalidate(),(0,d.Wi)(this._edits)?this._edits={type:"append",addOrUpdate:M.fromOptimizedFeatures(i,r,(0,d.Wg)(s)),id:this.tile.id,status:se.empty(),end:!0}:(this.requests.done.forEach((e=>e.message.end=!1)),(0,d.Wg)(this._edits.addOrUpdate).append(e.features()))}*readers(){for(const{message:e}of this.requests.done)(0,d.pC)(e.addOrUpdate)&&(yield e.addOrUpdate);(0,d.pC)(this._edits)&&(0,d.pC)(this._edits.addOrUpdate)&&(yield this._edits.addOrUpdate)}_invalidate(){for(const e of this.requests.done)e.message.status=se.empty();(0,d.pC)(this._edits)&&(this._edits.status=se.empty())}removeIds(e){this._invalidate();for(const{message:t}of this.requests.done){const s=t.addOrUpdate;(0,d.pC)(s)&&(s.removeIds(e),s.isEmpty&&((0,n.Z)("esri-2d-update-debug")&&console.debug("Removing FeatureSetReader"),t.addOrUpdate=null))}(0,d.pC)(this._edits)&&(0,d.pC)(this._edits.addOrUpdate)&&this._edits.addOrUpdate.removeIds(e),this.requests.done=this.requests.done.filter((e=>e.message.addOrUpdate||e.message.end))}abort(){this._abortController.abort()}}class ie extends c.Z{constructor(e){super(),this.events=new J.Z,this._resolver=(0,g.hh)(),this._didEdit=!1,this._subscriptions=new Map,this._outSR=e.outSR,this._serviceInfo=e.serviceInfo,this._onTileUpdateMessage=e.onMessage}async _onMessage(e){const t=this._subscriptions.get(e.id);if(!t)return;const s={...e,remove:e.remove??[],status:e.status??se.empty()};return(0,g.R8)(this._onTileUpdateMessage(s,t.options))}update(e,t){const s=t.fields.length;t.outFields=function(e,t){const s=new Set;return e&&e.forEach((e=>s.add(e))),t&&t.forEach((e=>s.add(e))),s.has("*")?["*"]:Array.from(s)}(this._schema?.outFields,t.outFields),t.outFields=t.outFields.length>=.75*s?["*"]:t.outFields,t.outFields.sort();const r=(0,K.Hg)(this._schema,t);if(!r)return;(0,n.Z)("esri-2d-update-debug")&&console.debug("Applying Update - Source:",r);const i="orderByFields"in this._serviceInfo&&this._serviceInfo.orderByFields?this._serviceInfo.orderByFields:this._serviceInfo.objectIdField+" ASC",o={returnCentroid:"esriGeometryPolygon"===this._serviceInfo.geometryType,returnGeometry:!0,timeReferenceUnknownClient:"stream"!==this._serviceInfo.type&&this._serviceInfo.timeReferenceUnknownClient,outFields:t.outFields,outSpatialReference:this._outSR,orderByFields:[i],where:t.definitionExpression||"1=1",gdbVersion:t.gdbVersion,historicMoment:t.historicMoment,timeExtent:t.timeExtent?V.Z.fromJSON(t.timeExtent):null},a=this._schema&&(0,K.uD)(r,"outFields");this._schema&&(0,K.V7)(r,["timeExtent","definitionExpression","gdbVersion","historicMoment","customParameters"])&&(e.why.mesh.push("Layer filter and/or custom parameters changed"),e.why.source.push("Layer filter and/or custom parameters changed"),e.mesh=!0,e.source=!0,e.queryFilter=!0),a&&(e.why.source.push("Layer required fields changed"),e.source=!0),(0,K.Hg)(o,this._queryInfo)&&(this._queryInfo=o),this._schema=t,this._resolver.resolve()}whenInitialized(){return this._resolver.promise}async applyUpdate(e){if(e.queryFilter||e.source&&this._didEdit)return this.refresh(e.version),void(this._didEdit=!1);this._subscriptions.forEach((t=>t.applyUpdate(e))),await this.resend()}refresh(e,t){for(const t of this._tiles())this.unsubscribe(t),this.subscribe(t,e)}subscribe(e,t){const s=new re(e,t);this._subscriptions.set(e.id,s)}unsubscribe(e){const t=this.getSubscription(e.id);(0,d.pC)(t)&&t.abort(),this._subscriptions.delete(e.id)}createQuery(e={}){const t=this._queryInfo.historicMoment?new Date(this._queryInfo.historicMoment):null;return new ee.Z({...this._queryInfo,historicMoment:t,...e})}getSubscription(e){return this._subscriptions.has(e)?this._subscriptions.get(e):null}async queryLastEditDate(){throw new Error("Service does not support query type")}async query(e,t){throw new Error("Service does not support query")}*_tiles(){const e=Array.from(this._subscriptions.values());for(const t of e)yield t.tile}async edit(e,t){const s=Array.from(this._subscriptions.values()),r=s.map((({tile:e})=>e));for(const e of s)e.removeIds(t);if(e.length){const s=r.map((t=>{const s=this.createTileQuery(t);return s.objectIds=e,{tile:t,query:s}})).map((async({tile:e,query:t})=>({tile:e,result:await this.query(t,{query:{tile:(0,n.Z)("esri-tiles-debug")?e.id.replace(/\//g,"."):void 0}}),query:t}))),i=(await(0,g.WW)(s)).map((async({tile:s,result:r})=>{if(!r.hasFeatures&&!t.length&&!e.length)return;const i=this._subscriptions.get(s.key.id);i&&i.edit(r,e)}));await(0,g.as)(i)}this._didEdit=!0}}var ne=s(49733);class oe extends ie{constructor(e){super(e),this.type="feature",this.mode="on-demand",this._adapter=function(e){const{capabilities:t}=e;return function(e){return"ogc-source"===e?.type}(e.source)?new $(e):function(e){return Array.isArray(e.source)}(e)?new Q(e):t.query.supportsFormatPBF&&(0,n.Z)("featurelayer-pbf")?new X(e):new H(e)}(e.serviceInfo),this._queue=new ne.e({concurrency:8,process:async e=>{if((0,g.k_)(e),(0,d.pC)(e.tile)){const t=e.tile.key.id,{signal:s}=e,r=(0,n.Z)("esri-tiles-debug")?{tile:t.replace(/\//g,"."),depth:e.depth}:void 0,i=await this._adapter.executeQuery(e.query,{signal:s,query:{...r,...this._schema?.customParameters}});return i.level=e.tile.key.level,i}return this._adapter.executeQuery(e.query,{...e,query:this._schema?.customParameters})}}),this._patchQueue=new ne.e({concurrency:8,process:async e=>{if((0,g.k_)(e),(0,d.pC)(e.tile)){const t=e.tile.key.id,{signal:s}=e,r=(0,n.Z)("esri-tiles-debug")?{tile:t.replace(/\//g,"."),depth:e.depth}:void 0,i=await this._adapter.executeQuery(e.query,{signal:s,query:{...r,...this._schema?.customParameters}});return i.level=e.tile.key.level,i}return this._adapter.executeQuery(e.query,{...e,query:this._schema?.customParameters})}})}destroy(){super.destroy(),this._adapter.destroy(),this._queue.destroy(),this._patchQueue.destroy()}get updating(){return!!this._queue.length||Array.from(this._subscriptions.values()).some((e=>!e.done))}get maxRecordCountFactor(){const{query:e}=this._serviceInfo.capabilities;return e.supportsMaxRecordCountFactor?4:null}get maxPageSize(){const{query:e}=this._serviceInfo.capabilities;return(e.maxRecordCount??8e3)*(0,d.Pt)(this.maxRecordCountFactor,1)}get pageSize(){return Math.min(8e3,this.maxPageSize)}enableEvent(e,t){}subscribe(e,t){super.subscribe(e,t);const s=this._subscriptions.get(e.id);this._fetchDataTile(e).catch((t=>{(0,g.D_)(t)||v.Z.getLogger("esri.views.2d.layers.features.sources.BaseFeatureSource").error(new b.Z("mapview-query-error","Encountered error when fetching tile",{tile:e,error:t}))})).then((()=>s.end()))}unsubscribe(e){super.unsubscribe(e)}readers(e){return this._subscriptions.get(e).readers()}async query(e,t={}){const s=t.query??{};return this._adapter.executeQuery(e,{...t,query:{...s,...this._schema?.customParameters}})}async queryLastEditDate(){const e=this._serviceInfo.source,t={...e.query,f:"json"};return(await(0,I.default)(e.path,{query:t,responseType:"json"})).data.editingInfo.lastEditDate}createTileQuery(e,t={}){const s=this._serviceInfo.geometryType,r=this.createQuery(t);r.quantizationParameters=t.quantizationParameters??e.getQuantizationParameters(),r.resultType="tile",r.geometry=e.extent,this._serviceInfo.capabilities.query.supportsQuantization?"esriGeometryPolyline"===s&&(r.maxAllowableOffset=e.resolution*(0,n.Z)("feature-polyline-generalization-factor")):"esriGeometryPolyline"!==s&&"esriGeometryPolygon"!==s||(r.maxAllowableOffset=e.resolution,"esriGeometryPolyline"===s&&(r.maxAllowableOffset*=(0,n.Z)("feature-polyline-generalization-factor")));const i=this._serviceInfo.capabilities.query;return r.defaultSpatialReferenceEnabled=i.supportsDefaultSpatialReference,r.compactGeometryEnabled=i.supportsCompactGeometry,r}async _executePatchQuery(e,t,s,r){const i=t.clone();i.outFields=[this._serviceInfo.objectIdField,...s],i.returnCentroid=!1,i.returnGeometry=!1;const n=(0,d.pC)(i.start)?i.start/8e3:0,o=r.signal;return this._patchQueue.push({tile:e,query:i,signal:o,depth:n})}async _resend(e,t){const{query:s,message:r}=e,i=(0,d.pC)(s.outFields)?s.outFields:[],n=this._queryInfo.outFields,o=n.filter((e=>!i.includes(e)));if((0,d.Wi)(r.addOrUpdate))this._onMessage({...r,type:"append"});else if(o.length)try{const e=this._subscriptions.get(r.id).tile,i=await this._executePatchQuery(e,s,o,t);(0,g.k_)(t),s.outFields=n,r.addOrUpdate.joinAttributes(i),this._onMessage({...r,end:r.end,type:"append"})}catch(e){}else this._onMessage({...r,type:"append"})}async _resendSubscription(e){if((0,n.Z)("esri-2d-update-debug")&&console.debug(e.tile.id,"Resend Subscription"),e.empty)return this._onMessage({id:e.tile.id,addOrUpdate:null,end:!1,type:"append"});const t=e.signal;for(const s of e.requests.done)await this._resend(s,{signal:t});return(0,d.pC)(e.edits)?this._onMessage(e.edits):void 0}async resend(){const e=Array.from(this._subscriptions.values());await Promise.all(e.map((e=>this._resendSubscription(e))))}}const ae=(0,n.Z)("esri-mobile"),he={maxDrillLevel:ae?1:4,maxRecordCountFactor:ae?1:3};class le extends oe{constructor(e){super(e)}async _fetchDataTile(e){const t=this._serviceInfo.capabilities.query.supportsMaxRecordCountFactor,s=this._subscriptions.get(e.key.id),r=s.signal,i=e.getQuantizationParameters();let n=0;const o=async(a,h)=>{const l=this._queryInfo,u=this.createTileQuery(a,{maxRecordCountFactor:t?he.maxRecordCountFactor:void 0,returnExceededLimitFeatures:!1,quantizationParameters:i});n++;try{const t=await this._queue.push({tile:e,query:u,signal:r,depth:h});if(n--,(0,g.k_)(r),!t)return;if(l!==this._queryInfo)return void o(a,h);if(t.exceededTransferLimit&&h<he.maxDrillLevel){for(const e of a.createChildTiles())o(e,h+1);return}const i={id:e.id,addOrUpdate:t,end:0===n,type:"append"};s.add({query:u,message:i}),this._onMessage(i)}catch(t){(0,g.D_)(t)||this._onMessage({id:e.id,addOrUpdate:null,end:!0,type:"append"})}};o(e,0)}}class ue extends oe{constructor(e){super(e)}async _fetchDataTile(e){const t=this._subscriptions.get(e.key.id);let s=!1,r=0,i=0;const n=(r,n)=>{i--,(0,g.k_)(t);const o=e.id,a=r.reader,h=r.query;if(!a.exceededTransferLimit){if(s=!0,0!==n&&!a.hasFeatures){const e={id:o,addOrUpdate:a,end:0===i,type:"append"};return t.add({message:e,query:h}),void this._onMessage(e)}const e={id:o,addOrUpdate:a,end:0===i,type:"append"};return t.add({message:e,query:h}),void this._onMessage(e)}const l={id:o,addOrUpdate:a,end:s&&0===i,type:"append"};t.add({message:l,query:h}),this._onMessage(l)};let o=0,a=0;for(;!s&&a++<20;){let a;for(let h=0;h<o+1;h++){const o=r++;i++,a=this._fetchDataTilePage(e,o,t).then((e=>e&&n(e,o))).catch((t=>{s=!0,(0,g.D_)(t)||(v.Z.getLogger("esri.views.2d.layers.features.sources.PagedFeatureSource").error(new b.Z("mapview-query-error","Encountered error when fetching tile",{tile:e,error:t})),this._onMessage({id:e.id,addOrUpdate:null,end:s,type:"append"}))}))}await a,(0,g.k_)(t),o=Math.min(o+2,6)}}async _fetchDataTilePage(e,t,s){(0,g.k_)(s);const r=this._queryInfo,i={start:this.pageSize*t,num:this.pageSize,returnExceededLimitFeatures:!0,quantizationParameters:e.getQuantizationParameters()};(0,d.pC)(this.maxRecordCountFactor)&&(i.maxRecordCountFactor=this.maxRecordCountFactor);const n=this.createTileQuery(e,i);try{const i=s.signal,o=await this._queue.push({tile:e,query:n,signal:i,depth:t});return(0,g.k_)(s),o?r!==this._queryInfo?this._fetchDataTilePage(e,t,s):{reader:o,query:n}:null}catch(e){return(0,g.H9)(e),null}}}var ce=s(77734),de=s(61800);function ge(e,t,s){const r=e.getXHydrated(),i=e.getYHydrated(),n=t.getColumnForX(r),o=Math.floor(t.normalizeCol(n));return`${s}/${Math.floor(t.getRowForY(i))}/${o}`}function pe(e,t){if((0,d.Wi)(e))return null;const s=t.transform,r=e.getQuantizationTransform();if((0,d.Wi)(r)){const[t,r]=s.scale,[i,n]=s.translate,o=-i/t,a=1/t,h=n/r,l=1/-r;return e.transform(o,h,a,l)}const[i,n]=r.scale,[o,a]=r.translate,[h,l]=s.scale,[u,c]=s.translate,g=i/h,p=(o-u)/h,f=n/l,_=(-a+c)/l;return e.transform(p,_,g,f)}class fe extends oe{constructor(e){super(e),this.mode="snapshot",this._loading=!0,this._controller=new AbortController,this._downloadPromise=null,this._didSendEnd=!1,this._queries=new Array,this._invalidated=!1,this._hasAggregates=!1,this._random=new ce.Z(1e3),this._store=e.store,this._markedIdsBufId=this._store.storage.createBitset()}destroy(){super.destroy(),this._controller.abort()}get loading(){return this._loading}get _signal(){return this._controller.signal}update(e,t){super.update(e,t),null==this._featureCount&&(this._featureCount=t.initialFeatureCount),(0,d.pC)(t.changedFeatureCount)&&(this._featureCount=t.changedFeatureCount),this._hasAggregates=!!e.targets?.aggregate}async resend(e=!1){if(await this._downloadPromise,this._invalidated||e){const e=(0,d.s3)(this._featureCount,"Expected featureCount to be defined");return this._invalidated=!1,this._subscriptions.forEach((e=>e.clear())),this._downloadPromise=this._download(e),void await this._downloadPromise}const t=this._queries.map((({query:e,reader:t})=>this._sendPatchQuery(e,t)));await Promise.all(t),this._subscriptions.forEach((e=>{e.requests.done.forEach((e=>this._onMessage(e.message)))}))}async refresh(e,t){t&&(this._featureCount=t.featureCount),await this.resend(!0)}async _sendPatchQuery(e,t){const s=(0,d.pC)(e.outFields)?e.outFields:[],r=this._queryInfo.outFields,i=r.filter((e=>!s.includes(e)));if(!i.length)return;const n=e.clone(),o=this._signal;n.returnGeometry=!1,n.returnCentroid=!1,n.outFields=i,e.outFields=r;const a=await this._queue.push({query:n,depth:0,signal:o});(0,g.k_)({signal:o}),t.joinAttributes(a)}async _fetchDataTile(e){if(!this._downloadPromise){const e=(0,d.s3)(this._featureCount,"Expected featureCount to be defined");this._downloadPromise=this._download(e)}const t=this._store.search(e),s=this._subscriptions.get(e.key.id),r=t.length-1;for(let i=0;i<r;i++){const r=pe(t[i],e),n={type:"append",id:e.id,addOrUpdate:r,end:!1,status:se.empty()};s.add({query:null,message:n}),this._hasAggregates||await(0,g.e4)(1),this._onMessage(n)}const i=pe(r>=0?t[r]:null,e),n=this._didSendEnd,o={type:"append",id:e.id,addOrUpdate:i,end:n,status:se.empty()};s.add({query:null,message:o}),this._onMessage(o)}async _download(e){try{await this.whenInitialized();const t=this._store.storage.getBitset(this._markedIdsBufId),s=new Set;t.clear();const r=Math.ceil(e/this.pageSize),i=Array.from({length:r},((e,t)=>t)).sort(((e,t)=>this._random.getInt()-this._random.getInt())).map((e=>this._downloadPage(e,t,s)));await Promise.all(i),this._store.sweepFeatures(t,this._store.storage),this._store.sweepFeatureSets(s)}catch(e){v.Z.getLogger("esri.views.2d.layers.features.sources.SnapshotFeatureSource").error("mapview-snapshot-source","Encountered and error when downloading feature snapshot",e)}this._sendEnd(),this._loading=!1}async _downloadPage(e,t,s){const r=this.pageSize,i={start:e*r,num:r,cacheHint:!0};(0,d.pC)(this.maxRecordCountFactor)&&(i.maxRecordCountFactor=this.maxRecordCountFactor);const n=this.createQuery(i),o=this._signal,a=await this._queue.push({query:n,depth:e,signal:o});(0,g.k_)({signal:o}),this._queries.push({query:n,reader:a}),this._store.insert(a),s.add(a.instance);const h=a.getCursor();for(;h.next();)t.set(h.getDisplayId());this._send(a)}_send(e){if(!this._subscriptions.size)return;let t=null;const s=new Map,r=new Set,i=new Map;this._subscriptions.forEach((e=>{const n=e.tile;s.set(n.key.id,null),t=n.tileInfoView,r.add(n.level);const{row:o,col:a}=n.key,h=`${n.level}/${o}/${a}`,l=i.get(h)??[];l.push(e),i.set(h,l)}));for(const n of r){const r=t.getLODInfoAt(n),o=e.getCursor();for(;o.next();){const e=ge(o,r,n),t=o.getIndex();if(i.has(e))for(const r of i.get(e)){const e=r.tile.id;let i=s.get(e);(0,d.Wi)(i)&&(i=[],s.set(e,i)),i.push(t)}}}s.forEach(((t,s)=>{if((0,d.pC)(t)){const r=this._subscriptions.get(s),i={type:"append",id:s,addOrUpdate:pe(de.t.from(e,t),r.tile),end:!1,status:se.empty()};r.add({query:null,message:i}),this._onMessage(i)}}))}_sendEnd(){this._subscriptions.forEach((e=>{const t={type:"append",id:e.tile.id,addOrUpdate:null,end:!0,status:se.empty()};e.add({query:null,message:t}),this._onMessage(t)})),this._didSendEnd=!0}}var _e=s(96794),me=s(22021);const ye="__esri_timestamp__";class Ie{constructor(e,t,s,r,i=128){this._trackIdToObservations=new Map,this._idCounter=0,this._lastPurge=performance.now(),this._addOrUpdated=new Map,this._removed=[],this._maxAge=0,this._timeInfo=s,this._purgeOptions=r,this.store=e,this.objectIdField=t,this.purgeInterval=i,this._useGeneratedIds="__esri_stream_id__"===this.objectIdField}removeById(e){this._removed.push(e)}removeByTrackId(e){const t=this._trackIdToObservations.get(e);if(t)for(const e of t.entries)this._removed.push(e)}add(e){if(this._useGeneratedIds){const t=this._nextId();e.attributes[this.objectIdField]=t,e.objectId=t}else e.objectId=e.attributes[this.objectIdField];const t=e.objectId;if(this._addOrUpdated.set(t,e),this._maxAge=Math.max(this._maxAge,e.attributes[this._timeInfo.startTimeField]),!this._timeInfo.trackIdField)return(0,d.Wi)(this._trackIdLessObservations)&&(this._trackIdLessObservations=new te.Z(1e5)),void this._trackIdLessObservations.enqueue(t);const s=e.attributes[this._timeInfo.trackIdField];if(!this._trackIdToObservations.has(s)){const e=(0,d.pC)(this._purgeOptions)&&null!=this._purgeOptions.maxObservations?this._purgeOptions.maxObservations:1e3,t=(0,me.uZ)(e,0,1e3);this._trackIdToObservations.set(s,new te.Z(t))}const r=this._trackIdToObservations.get(s)?.enqueue(t);(0,d.pC)(r)&&(this._addOrUpdated.has(r)?this._addOrUpdated.delete(r):this._removed.push(r))}checkForUpdates(){const e=this._getToAdd(),t=this._getToRemove(),s=performance.now();s-this._lastPurge>=this.purgeInterval&&(this._purge(s),this._lastPurge=s);const r=[];if((0,d.pC)(t))for(const e of t){const t=this.store.removeById(e);(0,d.pC)(t)&&r.push(t)}const i=[];if((0,d.pC)(e)){const r=new Set((0,d.Pt)(t,[]));for(const t of e)r.has(t.objectId)||(t.attributes[ye]=s,this.store.add(t),i.push(t))}(i.length||r?.length)&&this.store.update(i,r)}_getToAdd(){if(!this._addOrUpdated.size)return null;const e=new Array(this._addOrUpdated.size);let t=0;return this._addOrUpdated.forEach((s=>e[t++]=s)),this._addOrUpdated.clear(),e}_getToRemove(){const e=this._removed;return this._removed.length?(this._removed=[],e):null}_nextId(){const e=this._idCounter;return this._idCounter=(this._idCounter+1)%4294967294+1,e}_purge(e){const t=this._purgeOptions;(0,d.pC)(t)&&(this._purgeSomeByDisplayCount(t),this._purgeByAge(t),this._purgeByAgeReceived(e,t),this._purgeTracks())}_purgeSomeByDisplayCount(e){if(!e.displayCount)return;let t=this.store.size;if(t>e.displayCount){if(this._timeInfo.trackIdField)for(const s of this._trackIdToObservations.values())if(t>e.displayCount&&s.size){const e=(0,d.Wg)(s.dequeue());this._removed.push(e),t--}if((0,d.pC)(this._trackIdLessObservations)){let s=t-e.displayCount;for(;s-- >0;){const e=this._trackIdLessObservations.dequeue();(0,d.pC)(e)&&this._removed.push(e)}}}}_purgeByAge(e){const t=this._timeInfo?.startTimeField;if(!e.age||!t)return;const s=60*e.age*1e3,r=this._maxAge-s;this.store.forEach((e=>{e.attributes[t]<r&&this._removed.push(e.objectId)}))}_purgeByAgeReceived(e,t){if(!t.ageReceived)return;const s=e-60*t.ageReceived*1e3;this.store.forEach((e=>{e.attributes[ye]<s&&this._removed.push(e.objectId)}))}_purgeTracks(){this._trackIdToObservations.forEach(((e,t)=>{0===e.size&&this._trackIdToObservations.delete(t)}))}}var be=s(95047);function ve(e,t){const s=e.weakClone();if((0,d.pC)(e.geometry)){const r=(0,p.Jd)(t,e.geometry.coords[0]),i=(0,p.IN)(t,e.geometry.coords[1]);s.geometry=new k.Z([],[r,i])}return s}class xe{constructor(e,t){this.onUpdate=e,this._geometryType=t,this._objectIdToFeature=new Map,this._index=null}get _features(){const e=[];return this._objectIdToFeature.forEach((t=>e.push(t))),e}add(e){this._objectIdToFeature.set(e.objectId,e),this._index=null}get(e){return this._objectIdToFeature.has(e)?this._objectIdToFeature.get(e):null}forEach(e){this._objectIdToFeature.forEach(e)}search(e){return this._index||(this._index=function(e,t){const s=(0,_e.r)(9,function(e){return"esriGeometryPoint"===e?e=>(0,d.pC)(e.geometry)?{minX:e.geometry.coords[0],minY:e.geometry.coords[1],maxX:e.geometry.coords[0],maxY:e.geometry.coords[1]}:{minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}:e=>{let t=1/0,s=1/0,r=-1/0,i=-1/0;return(0,d.pC)(e.geometry)&&e.geometry.forEachVertex(((e,n)=>{t=Math.min(t,e),s=Math.min(s,n),r=Math.max(r,e),i=Math.max(i,n)})),{minX:t,minY:s,maxX:r,maxY:i}}}(t));return s.load(e),s}(this._features,this._geometryType)),function(e,t){return e.search({minX:t.bounds[0],minY:t.bounds[1],maxX:t.bounds[2],maxY:t.bounds[3]})}(this._index,e)}clear(){this._index=null,this._objectIdToFeature.clear()}removeById(e){const t=this._objectIdToFeature.get(e);return t?(this._objectIdToFeature.delete(e),this._index=null,t):null}update(e,t){this.onUpdate(e,t)}get size(){return this._objectIdToFeature.size}}let we=class extends ie{constructor(e){super(e),this.type="stream",this._updateIntervalId=0,this._level=0,this._updateInfo={websocket:0,client:0},this._isPaused=!1,this._inUpdate=!1;const{outSR:t}=e,{geometryType:s,objectIdField:r,timeInfo:i,purgeOptions:n,source:o,spatialReference:a,serviceFilter:h,maxReconnectionAttempts:l,maxReconnectionInterval:u,updateInterval:c,customParameters:d,enabledEventTypes:g}=e.serviceInfo,f=new xe(this._onUpdate.bind(this),s),_=new Ie(f,r,i,n),m=(0,be.createConnection)(o,a,t,s,h,l,u,d??{});this._store=f,this._manager=_,this._connection=m,this._quantize=function(e){return"esriGeometryPoint"===e?ve:(t,s)=>{const r=t.weakClone(),i=new k.Z,n=(0,p.Nh)(i,t.geometry,!1,!1,e,s,!1,!1);return r.geometry=n,r}}(s),this._enabledEventTypes=new Set(g),this._handles=[this._connection.on("data-received",(e=>this._onFeature(e))),this._connection.on("message-received",(e=>this._onWebSocketMessage(e)))],this._initUpdateInterval=()=>{let t=performance.now();this._updateIntervalId=setInterval((()=>{const s=performance.now(),r=s-t;if(r>2500){t=s;const e=Math.round(this._updateInfo.client/(r/1e3)),i=Math.round(this._updateInfo.websocket/(r/1e3));this._updateInfo.client=0,this._updateInfo.websocket=0,this.events.emit("updateRate",{client:e,websocket:i})}e.canAcceptRequest()&&!this._inUpdate&&this._manager.checkForUpdates()}),c)},this._initUpdateInterval()}destroy(){super.destroy(),this._clearUpdateInterval(),this._handles.forEach((e=>e.remove())),this._connection.destroy()}_fetchDataTile(){}get connectionStatus(){return this._isPaused?"paused":this._connection?.connectionStatus}get errorString(){return this._connection?.errorString}updateCustomParameters(e){this._connection.updateCustomParameters(e)}pauseStream(){this._isPaused||(this._isPaused=!0,this._clearUpdateInterval())}resumeStream(){this._isPaused&&(this._isPaused=!1,this._initUpdateInterval())}sendMessageToSocket(e){this._connection.sendMessageToSocket(e)}sendMessageToClient(e){this._connection.sendMessageToClient(e)}enableEvent(e,t){t?this._enabledEventTypes.add(e):this._enabledEventTypes.delete(e)}get updating(){return!1}subscribe(e,t){super.subscribe(e,t);const s=this._subscriptions.get(e.id);this._level=e.level;const r=this._getTileFeatures(e);this._onMessage({type:"append",id:e.key.id,addOrUpdate:r,end:!0}),s.didSend=!0}unsubscribe(e){super.unsubscribe(e)}*readers(e){const t=this._subscriptions.get(e),{tile:s}=t;yield this._getTileFeatures(s)}createTileQuery(e){throw new Error("Service does not support tile  queries")}async resend(){this._subscriptions.forEach((e=>{const{tile:t}=e,s={type:"append",id:t.id,addOrUpdate:this._getTileFeatures(t),end:!0};this._onMessage(s)}))}_getTileFeatures(e){const t=this._store.search(e).map((t=>this._quantize(t,e.transform)));return M.fromOptimizedFeatures(t,this._serviceInfo,e.transform)}_onWebSocketMessage(e){if(this._enabledEventTypes.has("message-received")&&this.events.emit("message-received",e),"type"in e)switch(e.type){case"delete":if(e.objectIds)for(const t of e.objectIds)this._manager.removeById(t);if(e.trackIds)for(const t of e.trackIds)this._manager.removeByTrackId(t);break;case"clear":this._store.forEach((e=>this._manager.removeById(e.objectId)))}}_onFeature(e){this._updateInfo.websocket++;try{this._enabledEventTypes.has("data-received")&&this.events.emit("data-received",e);const t=(0,p.XA)(e,this._serviceInfo.geometryType,!1,!1,this._serviceInfo.objectIdField);this._manager.add(t)}catch(e){}}_clearUpdateInterval(){clearInterval(this._updateIntervalId),this._updateIntervalId=0}async _onUpdate(e,t){this._inUpdate=!0;try{(0,d.pC)(e)&&(this._updateInfo.client+=e.length),this._subscriptions.forEach(((e,t)=>{e.didSend&&e.tile.level===this._level&&this._onMessage({type:"append",id:t,addOrUpdate:null,clear:!0,end:!1})}));const t=[];this._subscriptions.forEach(((e,s)=>{if(!e.didSend||e.tile.level!==this._level)return;const r=e.tile,i={type:"append",id:s,addOrUpdate:this._getTileFeatures(r),remove:[],end:!1,status:se.empty()};e.requests.stream.enqueue(i),t.push(this._onMessage(i))})),await Promise.all(t),this._subscriptions.forEach(((e,t)=>{e.didSend&&e.tile.level===this._level&&this._onMessage({type:"append",id:t,addOrUpdate:null,end:!0})}))}catch{}this._inUpdate=!1}};(0,r._)([(0,a.Cb)()],we.prototype,"_isPaused",void 0),(0,r._)([(0,a.Cb)()],we.prototype,"connectionStatus",null),(0,r._)([(0,a.Cb)()],we.prototype,"errorString",null),we=(0,r._)([(0,h.j)("esri.views.2d.layers.features.sources")],we);var Se=s(16534),Ce=s(61027),Fe=s(26899);s(37720),s(66039);var Te=s(35371);const Me=v.Z.getLogger("esri.views.layers.2d.features.support.AttributeStore"),Ae=()=>null,ke={sharedArrayBuffer:(0,n.Z)("esri-shared-array-buffer"),atomics:(0,n.Z)("esri-atomics")};function Ee(e,t){return s=>t(e(s))}class Re{constructor(e,t,s,r){this.size=0,this.texelSize=4,this.dirtyStart=0,this.dirtyEnd=0;const{pixelType:i,layout:n,textureOnly:o}=r;this.textureOnly=o||!1,this.pixelType=i,this._ctype=t,this.layout=n,this._resetRange(),this._shared=e,this.size=s,o||(this.data=this._initData(i,s,e,t))}get buffer(){return(0,d.yw)(this.data,(e=>e.buffer))}unsetComponentAllTexels(e,t){const s=(0,d.Wg)(this.data);for(let r=0;r<this.size*this.size;r++)s[r*this.texelSize+e]&=~t;this.dirtyStart=0,this.dirtyEnd=this.size*this.size-1}setComponentAllTexels(e,t){const s=(0,d.Wg)(this.data);for(let r=0;r<this.size*this.size;r++)s[r*this.texelSize+e]|=255&t;this.dirtyStart=0,this.dirtyEnd=this.size*this.size-1}setComponent(e,t,s){const r=(0,d.Wg)(this.data);for(const i of s)r[i*this.texelSize+e]|=t,this.dirtyStart=Math.min(this.dirtyStart,i),this.dirtyEnd=Math.max(this.dirtyEnd,i)}setComponentTexel(e,t,s){(0,d.Wg)(this.data)[s*this.texelSize+e]|=t,this.dirtyStart=Math.min(this.dirtyStart,s),this.dirtyEnd=Math.max(this.dirtyEnd,s)}unsetComponentTexel(e,t,s){(0,d.Wg)(this.data)[s*this.texelSize+e]&=~t,this.dirtyStart=Math.min(this.dirtyStart,s),this.dirtyEnd=Math.max(this.dirtyEnd,s)}getData(e,t){const s=(0,Ce.jL)(e);return(0,d.Wg)(this.data)[s*this.texelSize+t]}setData(e,t,s){const r=(0,Ce.jL)(e),i=1<<t;0!=(this.layout&i)?(0,d.Wi)(this.data)||(this.data[r*this.texelSize+t]=s,this.dirtyStart=Math.min(this.dirtyStart,r),this.dirtyEnd=Math.max(this.dirtyEnd,r)):Me.error("mapview-attributes-store","Tried to set a value for a texel's readonly component")}lock(){this.pixelType===Te.Br.UNSIGNED_BYTE&&this._shared&&ke.atomics&&"local"!==this._ctype&&Atomics.store(this.data,0,1)}unlock(){this.pixelType===Te.Br.UNSIGNED_BYTE&&this._shared&&ke.atomics&&"local"!==this._ctype&&Atomics.store(this.data,0,0)}expand(e){if(this.size=e,!this.textureOnly){const t=this._initData(this.pixelType,e,this._shared,this._ctype),s=(0,d.Wg)(this.data);t.set(s),this.data=t}}toMessage(){const e=this.dirtyStart,t=this.dirtyEnd,s=this.texelSize;if(e>t)return null;this._resetRange();const r=!(this._shared||"local"===this._ctype),i=this.pixelType,n=this.layout,o=(0,d.Wg)(this.data);return{start:e,end:t,data:r&&o.slice(e*s,(t+1)*s)||null,pixelType:i,layout:n}}_initData(e,t,s,r){const i=s&&"local"!==r?SharedArrayBuffer:ArrayBuffer,n=(0,Fe.UK)(e),o=new n(new i(t*t*4*n.BYTES_PER_ELEMENT));for(let e=0;e<o.length;e+=4)o[e+1]=255;return o}_resetRange(){this.dirtyStart=2147483647,this.dirtyEnd=0}}class Le{constructor(e,t,s=(()=>{})){this._client=e,this.config=t,this._notifyChange=s,this._blocks=new Array,this._filters=new Array(Se.m4),this._attributeComputeInfo=null,this._targetType=0,this._abortController=new AbortController,this._hasScaleExpr=!1,this._size=32,this._nextUpdate=null,this._currUpdate=null,this._idsToHighlight=new Set;const r=t.supportsTextureFloat?Te.Br.FLOAT:Te.Br.UNSIGNED_BYTE;Ae(`Creating AttributeStore ${ke.sharedArrayBuffer?"with":"without"} shared memory`),this._blockDescriptors=[{pixelType:Te.Br.UNSIGNED_BYTE,layout:1},{pixelType:Te.Br.UNSIGNED_BYTE,layout:15,textureOnly:!0},{pixelType:Te.Br.UNSIGNED_BYTE,layout:15,textureOnly:!0},{pixelType:r,layout:15},{pixelType:r,layout:15},{pixelType:r,layout:15},{pixelType:r,layout:15}],this._blocks=this._blockDescriptors.map((()=>null))}destroy(){this._abortController.abort()}get hasScaleExpr(){return this._hasScaleExpr}get _signal(){return this._abortController.signal}get hasHighlight(){return this._idsToHighlight.size>0}isUpdating(){return!!this._currUpdate||!!this._nextUpdate}update(e,t){this.config=t;const s=t.schema.processors[0].storage,r=(0,K.Hg)(this._schema,s);if((e.targets.feature||e.targets.aggregate)&&(e.storage.data=!0),r&&((0,n.Z)("esri-2d-update-debug")&&console.debug("Applying Update - AttributeStore:",r),e.storage.data=!0,this._schema=s,this._attributeComputeInfo=null,!(0,d.Wi)(s))){switch(s.target){case"feature":this._targetType=Ce.PX;break;case"aggregate":this._targetType=Ce.xp}if("subtype"===s.type){this._attributeComputeInfo={isSubtype:!0,subtypeField:s.subtypeField,map:new Map};for(const e in s.mapping){const t=s.mapping[e];if((0,d.pC)(t)&&(0,d.pC)(t.vvMapping))for(const s of t.vvMapping)this._bindAttribute(s,parseInt(e,10))}}else{if(this._attributeComputeInfo={isSubtype:!1,map:new Map},(0,d.pC)(s.vvMapping))for(const e of s.vvMapping)this._bindAttribute(e);if((0,d.pC)(s.attributeMapping))for(const e of s.attributeMapping)this._bindAttribute(e)}}}onTileData(e,t){if((0,d.Wi)(t.addOrUpdate))return;const s=t.addOrUpdate.getCursor();for(;s.next();){const e=s.getDisplayId();this.setAttributeData(e,s)}}async setHighlight(e,t){const s=this._getBlock(0),r=t.map((e=>(0,Ce.jL)(e)));s.lock(),s.unsetComponentAllTexels(0,1),s.setComponent(0,1,r),s.unlock(),this._idsToHighlight.clear();for(const t of e)this._idsToHighlight.add(t);await this.sendUpdates()}async updateFilters(e,t,s){const{service:r,spatialReference:i}=s,{filters:o}=t,a=o.map(((e,t)=>this._updateFilter(e,t,r,i)));(await Promise.all(a)).some((e=>e))&&(e.storage.filters=!0,(0,n.Z)("esri-2d-update-debug")&&console.debug("Applying Update - AttributeStore:","Filters changed"))}setData(e,t,s,r){const i=(0,Ce.jL)(e);this._ensureSizeForTexel(i),this._getBlock(t).setData(e,s,r)}getData(e,t,s){return this._getBlock(t).getData(e,s)}getHighlightFlag(e){return this._idsToHighlight.has(e)?Se.uG:0}unsetAttributeData(e){const t=(0,Ce.jL)(e);this._getBlock(0).setData(t,0,0)}setAttributeData(e,t){const s=(0,Ce.jL)(e);if(this._ensureSizeForTexel(s),this._getBlock(0).setData(s,0,this.getFilterFlags(t)),this._targetType!==(0,Ce.vs)(e))return;const r=this._attributeComputeInfo,i=this.config.supportsTextureFloat?1:2;let n=null;r&&(n=r.isSubtype?r.map.get(t.readAttribute(r.subtypeField)):r.map,n&&n.size&&n.forEach(((e,r)=>{const n=r*i%4,o=Math.floor(r*i/4),a=this._getBlock(o+Se.aK),h=e(t);if(this.config.supportsTextureFloat)a.setData(s,n,h);else if(h===Se.AI)a.setData(s,n,255),a.setData(s,n+1,255);else{const e=(0,me.uZ)(Math.round(h),-32767,32766)+32768,t=255&e,r=(65280&e)>>8;a.setData(s,n,t),a.setData(s,n+1,r)}})))}sendUpdates(){if((0,n.Z)("esri-2d-update-debug")&&console.debug("AttributeStore::sendUpdate"),this._notifyChange(),this._nextUpdate)return this._nextUpdate.promise;if(this._currUpdate)return this._nextUpdate=(0,g.hh)(),this._nextUpdate.promise;const e={blocks:this._blocks.map((e=>(0,d.pC)(e)?e.toMessage():null))};return this._currUpdate=this._createResources().then((()=>{const t=()=>{if(this._currUpdate=null,this._nextUpdate){const e=this._nextUpdate;this._nextUpdate=null,this.sendUpdates().then((()=>e.resolve()))}else(0,n.Z)("esri-2d-update-debug")&&console.debug("AttributeStore::sendUpdate::No additional updates queued");this._notifyChange()};(0,n.Z)("esri-2d-update-debug")&&console.debug("AttributeStore::sendUpdate::client.update");const s=this._client.update(e,this._signal).then(t).catch(t);return this._client.render(this._signal),s})).catch((e=>{if((0,g.D_)(e))return this._createResourcesPromise=null,this._createResources();this._notifyChange(),Me.error(new b.Z("mapview-attribute-store","Encountered an error during client update",e))})),this._currUpdate}_ensureSizeForTexel(e){for(;e>=this._size*this._size;)if(this._expand())return}_bindAttribute(e,t){let s;if(null!=e.fieldIndex)e.normalizationField&&Me.warn("mapview-arcade","Ignoring normalizationField specified with an arcade expression which is not supported."),s=t=>t.getComputedNumericAtIndex(e.fieldIndex);else{if(!e.field)return;s=function(){const{normalizationField:t}=e;return t?s=>{const r=s.readAttribute(t);return r?s.readAttribute(e.field)/r:null}:t=>t.readAttribute(e.field)}()}const{valueRepresentation:r}=e;r&&(s=Ee(s,(e=>function(e,t){if(!e||!t)return e;switch(t){case"radius":case"distance":return 2*e;case"diameter":case"width":return e;case"area":return Math.sqrt(e)}return e}(e,r))));const i=e=>null===e||isNaN(e)||e===1/0||e===-1/0?Se.AI:e,n=this._attributeComputeInfo;if(n.isSubtype){const r=n.map.get(t)??new Map;r.set(e.binding,Ee(s,i)),n.map.set(t,r)}else n.map.set(e.binding,Ee(s,i))}_createResources(){if((0,d.pC)(this._createResourcesPromise))return this._createResourcesPromise;this._getBlock(Se.xl),this._getBlock(Se.pU),Ae("Initializing AttributeStore");const e={shared:ke.sharedArrayBuffer&&!("local"===this._client.type),size:this._size,blocks:(0,d.Fd)(this._blocks,(e=>({textureOnly:e.textureOnly,buffer:e.buffer,pixelType:e.pixelType})))},t=this._client.initialize(e,this._signal).catch((e=>{(0,g.D_)(e)?this._createResourcesPromise=null:Me.error(new b.Z("mapview-attribute-store","Encountered an error during client initialization",e))}));return this._createResourcesPromise=t,t.then((()=>(0,d.Wi)(this._createResourcesPromise)?this._createResources():void 0)),t}_getBlock(e){const t=this._blocks[e];if((0,d.pC)(t))return t;Ae(`Initializing AttributeBlock at index ${e}`);const s=ke.sharedArrayBuffer,r=this._client.type,i=new Re(s,r,this._size,this._blockDescriptors[e]);return this._blocks[e]=i,this._createResourcesPromise=null,i}_expand(){if(this._size<this.config.maxTextureSize){const e=this._size<<=1;return Ae("Expanding block size to",e,this._blocks),(0,d.JR)(this._blocks,(t=>t.expand(e))),this._createResourcesPromise=null,this._size=e,0}return Me.error(new b.Z("mapview-limitations","Maximum number of onscreen features exceeded.")),-1}async _updateFilter(e,t,s,r){const i=this._filters[t],n=(0,d.pC)(i)&&i.hash;if(!i&&!e)return!1;if(n===JSON.stringify(e))return!1;if((0,d.Wi)(e)){if(!i)return!1;const e=1<<t+1,s=this._getBlock(0);return this._filters[t]=null,s.setComponentAllTexels(0,e),this.sendUpdates(),!0}const o=await this._getFilter(t,s);return await o.update(e,r),!0}async _getFilter(e,t){const r=this._filters[e];if((0,d.pC)(r))return r;const{default:i}=await s.e(9169).then(s.bind(s,29169)),n=new i({geometryType:t.geometryType,hasM:!1,hasZ:!1,timeInfo:t.timeInfo,fieldsIndex:new _.Z(t.fields)});return this._filters[e]=n,n}isVisible(e){return!!(2&this._getBlock(0).getData(e,0))}getFilterFlags(e){let t=0;const s=(0,Ce.KS)(e.getDisplayId());for(let r=0;r<this._filters.length;r++){const i=!!(s&1<<r),n=this._filters[r];t|=(!i||(0,d.Wi)(n)||n.check(e)?1:0)<<r}let r=0;if(this._idsToHighlight.size){const t=e.getObjectId();r=this.getHighlightFlag(t)}return t<<1|r}}function Oe(e,t,s,r){r%2&&(r+=1);let i=0,n=0,o=-90,a=90,h=-180,l=180;for(let e=0;e<r/2;e++){for(let t=0;t<5;t++){const r=(h+l)/2,n=s>r?1:0;i|=n<<29-(t+5*e),h=(1-n)*h+n*r,l=(1-n)*r+n*l}for(let s=0;s<5;s++){const r=(o+a)/2,i=t>r?1:0;n|=i<<29-(s+5*e),o=(1-i)*o+i*r,a=(1-i)*r+i*a}}e.geohashX=i,e.geohashY=n}function Pe(e,t,s,r,i){i%2&&(i+=1);let n=0,o=0,a=-90,h=90,l=-180,u=180;for(let e=0;e<i/2;e++){for(let t=0;t<5;t++){const s=(l+u)/2,i=r>s?1:0;n|=i<<29-(t+5*e),l=(1-i)*l+i*s,u=(1-i)*s+i*u}for(let t=0;t<5;t++){const r=(a+h)/2,i=s>r?1:0;o|=i<<29-(t+5*e),a=(1-i)*a+i*r,h=(1-i)*r+i*h}}e[2*t]=n,e[2*t+1]=o}s(66577),new Float64Array(2),new Float64Array(2);var Ue=s(82971),Ze=s(37427);class Be{constructor(e=[],t,s=8096){this.onRelease=e=>{},this._nodes=0,this._root=new Ne(this,0,0,0),this._statisticFields=e,this._pool=s?new te.Z(8096):null,this._serviceInfo=t}destroy(){this.clear()}_acquire(e,t,s){this._nodes++;let r=null;return(0,d.pC)(this._pool)&&(r=this._pool.dequeue()),(0,d.pC)(r)?r.realloc(e,t,s):r=new Ne(this,e,t,s),r}_release(e){this.onRelease(e),this._nodes--,(0,d.pC)(this._pool)&&this._pool.enqueue(e)}get count(){return this._root.count}get size(){return this._nodes}get poolSize(){return(0,d.R2)(this._pool,0,(e=>e.size))}get depth(){let e=0;return this.forEach((t=>e=Math.max(e,t.depth))),e}dropLevels(e){this.forEach((t=>{if(t.depth>=e)for(let e=0;e<t.children.length;e++){const s=t.children[e];s&&this._release(s)}})),this.forEach((t=>{if(t.depth>=e)for(let e=0;e<t.children.length;e++)t.children[e]=null}))}clear(){this.forEach((e=>this._release(e))),this._root=new Ne(this,0,0,0)}insert(e,t,s=0){const r=M.fromOptimizedFeatures([e],this._serviceInfo).getCursor();r.next();const i=r.readGeometry();if(!i)return;const[n,o]=i.coords,a=e.geohashX,h=e.geohashY;this.insertCursor(r,e.displayId,n,o,a,h,t,s)}insertCursor(e,t,s,r,i,n,o,a=0){let h=this._root,l=0,u=0,c=0;for(;null!==h;){if(h.depth>=a&&(h.count+=1,h.xTotal+=s,h.yTotal+=r,h.xGeohashTotal+=i,h.yGeohashTotal+=n,h.referenceId=t,this._updateStatisticsCursor(e,h,1)),l>=o)return void h.add(t);const d=Math.ceil((l+1)/2),g=Math.floor((l+1)/2),p=1-l%2,f=30-(3*d+2*g),_=30-(2*d+3*g),m=(i&7*p+3*(1-p)<<f)>>f,y=(n&3*p+7*(1-p)<<_)>>_,I=m+y*(8*p+4*(1-p));u=u<<3*p+2*(1-p)|m,c=c<<2*p+3*(1-p)|y,null==h.children[I]&&(h.children[I]=this._acquire(u,c,l+1)),l+=1,h=h.children[I]}}remove(e,t){const s=M.fromOptimizedFeatures([e],this._serviceInfo).getCursor();s.next();const r=s.readGeometry();if(!r)return;const[i,n]=r.coords,o=e.geohashX,a=e.geohashY;this.removeCursor(s,i,n,o,a,t)}removeCursor(e,t,s,r,i,n){let o=this._root,a=0;for(;null!==o;){if(o.count-=1,o.xTotal-=t,o.yTotal-=s,o.xGeohashTotal-=r,o.yGeohashTotal-=i,this._updateStatisticsCursor(e,o,-1),a>=n)return void o.remove(e.getDisplayId());const h=Math.ceil((a+1)/2),l=Math.floor((a+1)/2),u=1-a%2,c=30-(3*h+2*l),d=30-(2*h+3*l),g=((r&7*u+3*(1-u)<<c)>>c)+((i&3*u+7*(1-u)<<d)>>d)*(8*u+4*(1-u)),p=o.children[g];1===p?.count&&(this._release(p),o.children[g]=null),a+=1,o=p}}forEach(e){let t=this._root;for(;null!==t;){const s=this._linkChildren(t)||t.next;e(t),t=s}}find(e,t,s){return this._root.find(e,t,s,0,0,0)}findIf(e){let t=null;return this.forEach((s=>{e(s)&&(t=s)})),t}findAllIf(e){const t=[];return this.forEach((s=>{e(s)&&t.push(s)})),t}findSingleOccupancyNode(e,t,s,r,i){let n=this._root;for(;null!==n;){const o=n.depth,a=n.xNode,h=n.yNode,l=1-o%2,u=n.xGeohashTotal/n.count,c=n.yGeohashTotal/n.count;if(1===n.count&&e<u&&u<=s&&t<c&&c<=r)return n;if(o>=i){n=n.next;continue}const d=Math.ceil((o+1)/2),g=Math.floor((o+1)/2),p=30-(3*d+2*g),f=30-(2*d+3*g),_=~((1<<p)-1),m=~((1<<f)-1),y=(e&_)>>p,I=(t&m)>>f,b=(s&_)>>p,v=(r&m)>>f,x=a<<3*l+2*(1-l),w=h<<2*l+3*(1-l),S=x+8*l+4*(1-l),C=w+4*l+8*(1-l),F=Math.max(x,y),T=Math.max(w,I),M=Math.min(S,b),A=Math.min(C,v);let k=null,E=null;for(let e=T;e<=A;e++)for(let t=F;t<=M;t++){const s=t-x+(e-w)*(8*l+4*(1-l)),r=n.children[s];r&&(k||(k=r,k.next=n.next),E&&(E.next=r),E=r,r.next=n.next)}n=k||n.next}return null}getRegionDisplayIds(e){let t=this._root;const{bounds:s,geohashBounds:r,level:i}=e,[n,o,a,h]=s,l=[];for(;null!==t;){const e=t.depth,s=t.xNode,u=t.yNode;if(e>=i){const e=t.xTotal/t.count,s=t.yTotal/t.count;e>=n&&e<=a&&s>=o&&s<=h&&t.displayIds.forEach((e=>l.push(e))),t=t.next;continue}const c=Math.ceil((e+1)/2),d=Math.floor((e+1)/2),g=1-e%2,p=30-(3*c+2*d),f=30-(2*c+3*d),_=~((1<<p)-1),m=~((1<<f)-1),y=(r.xLL&_)>>p,I=(r.yLL&m)>>f,b=(r.xTR&_)>>p,v=(r.yTR&m)>>f,x=s<<3*g+2*(1-g),w=u<<2*g+3*(1-g),S=x+8*g+4*(1-g),C=w+4*g+8*(1-g),F=Math.max(x,y),T=Math.max(w,I),M=Math.min(S,b),A=Math.min(C,v);let k=null,E=null;for(let e=T;e<=A;e++)for(let s=F;s<=M;s++){const r=s-x+(e-w)*(8*g+4*(1-g)),i=t.children[r];i&&(k||(k=i,k.next=t.next),E&&(E.next=i),E=i,i.next=t.next)}t=k||t.next}return l}getRegionStatistics(e){let t=this._root,s=0,r=0,i=0;const n={},{bounds:o,geohashBounds:a,level:h}=e,[l,u,c,d]=o;let g=0;for(;null!==t;){const e=t.depth,o=t.xNode,p=t.yNode;if(e>=h){const e=t.xTotal/t.count,o=t.yTotal/t.count;e>l&&e<=c&&o>u&&o<=d&&(s+=t.count,r+=t.xTotal,i+=t.yTotal,1===t.count&&(g=t.referenceId),this._aggregateStatistics(n,t.statistics)),t=t.next;continue}const f=Math.ceil((e+1)/2),_=Math.floor((e+1)/2),m=1-e%2,y=30-(3*f+2*_),I=30-(2*f+3*_),b=~((1<<y)-1),v=~((1<<I)-1),x=(a.xLL&b)>>y,w=(a.yLL&v)>>I,S=(a.xTR&b)>>y,C=(a.yTR&v)>>I,F=o<<3*m+2*(1-m),T=p<<2*m+3*(1-m),M=F+8*m+4*(1-m),A=T+4*m+8*(1-m),k=Math.max(F,x),E=Math.max(T,w),R=Math.min(M,S),L=Math.min(A,C);let O=null,P=null;for(let e=E;e<=L;e++)for(let o=k;o<=R;o++){const a=o-F+(e-T)*(8*m+4*(1-m)),h=t.children[a];if(h){if(e!==E&&e!==L&&o!==k&&o!==R){const e=h.xTotal/h.count,t=h.yTotal/h.count;e>l&&e<=c&&t>u&&t<=d&&(s+=h.count,r+=h.xTotal,i+=h.yTotal,1===h.count&&(g=h.referenceId),this._aggregateStatistics(n,h.statistics));continue}O||(O=h,O.next=t.next),P&&(P.next=h),P=h,h.next=t.next}}t=O||t.next}return{count:s,attributes:this.normalizeStatistics(n,s),xTotal:r,yTotal:i,referenceId:g}}getBins(e){const t=[],{geohashBounds:s,level:r}=e;let i=this._root;for(;null!==i;){const e=i.depth,n=i.xNode,o=i.yNode;if(e>=r){t.push(i),i=i.next;continue}const a=Math.ceil((e+1)/2),h=Math.floor((e+1)/2),l=1-e%2,u=30-(3*a+2*h),c=30-(2*a+3*h),d=~((1<<u)-1),g=~((1<<c)-1),p=(s.xLL&d)>>u,f=(s.yLL&g)>>c,_=(s.xTR&d)>>u,m=(s.yTR&g)>>c,y=n<<3*l+2*(1-l),I=o<<2*l+3*(1-l),b=y+8*l+4*(1-l),v=I+4*l+8*(1-l),x=Math.max(y,p),w=Math.max(I,f),S=Math.min(b,_),C=Math.min(v,m);let F=null,T=null;for(let e=w;e<=C;e++)for(let t=x;t<=S;t++){const s=t-y+(e-I)*(8*l+4*(1-l)),r=i.children[s];r&&(F||(F=r,F.next=i.next),T&&(T.next=r),T=r,r.next=i.next)}i=F||i.next}return t}_linkChildren(e){let t=null,s=null;for(let r=0;r<=e.children.length;r++){const i=e.children[r];i&&(t||(t=i,t.next=e.next),s&&(s.next=i),s=i,i.next=e.next)}return t}_updateStatisticsCursor(e,t,s){for(const r of this._statisticFields){const i=r.name,n=r.inField?e.readAttribute(r.inField):e.getComputedNumericAtIndex(r.inFieldIndex);switch(r.statisticType){case"min":{if(isNaN(n))break;if(!t.statistics[i]){t.statistics[i]={value:n};break}const e=t.statistics[i].value;t.statistics[i].value=Math.min(e,n);break}case"max":{if(isNaN(n))break;if(!t.statistics[i]){t.statistics[i]={value:n};break}const e=t.statistics[i].value;t.statistics[i].value=Math.max(e,n);break}case"count":break;case"sum":case"avg":{t.statistics[i]||(t.statistics[i]={value:0,nanCount:0});const e=t.statistics[i].value,r=t.statistics[i].nanCount??0;null==n||isNaN(n)?t.statistics[i].nanCount=r+s:t.statistics[i].value=e+s*n;break}case"avg_angle":{t.statistics[i]||(t.statistics[i]={x:0,y:0,nanCount:0});const e=t.statistics[i].x,r=t.statistics[i].y,o=t.statistics[i].nanCount??0,a=Math.PI/180;null==n||isNaN(n)?t.statistics[i].nanCount=o+s:(t.statistics[i].x=e+s*Math.cos(n*a),t.statistics[i].y=r+s*Math.sin(n*a));break}case"mode":{t.statistics[i]||(t.statistics[i]={});const e=t.statistics[i][n]||0;t.statistics[i][n]=e+s;break}}}}_aggregateStatistics(e,t){for(const s of this._statisticFields){const r=s.name;switch(s.statisticType){case"min":{if(!e[r]){e[r]={value:t[r].value};break}const s=e[r].value;e[r].value=Math.min(s,t[r].value);break}case"max":{if(!e[r]){e[r]={value:t[r].value};break}const s=e[r].value;e[r].value=Math.max(s,t[r].value);break}case"count":break;case"sum":case"avg":case"avg_angle":case"mode":e[r]||(e[r]={});for(const s in t[r]){const i=e[r][s]||0;e[r][s]=i+t[r][s]}}}}normalizeStatistics(e,t){const s={};for(const r of this._statisticFields){const i=r.name;switch(r.statisticType){case"min":case"max":{const r=e[i];if(!t||!r)break;s[i]=r.value;break}case"count":if(!t)break;s[i]=t;break;case"sum":{if(!t)break;const{value:r,nanCount:n}=e[i];if(!(t-n))break;s[i]=r;break}case"avg":{if(!t)break;const{value:r,nanCount:n}=e[i];if(!(t-n))break;s[i]=r/(t-n);break}case"avg_angle":{if(!t)break;const{x:r,y:n,nanCount:o}=e[i];if(!(t-o))break;const a=r/(t-o),h=n/(t-o),l=180/Math.PI,u=Math.atan2(h,a)*l;s[i]=u;break}case"mode":{const t=e[i];let r=0,n=0,o=null;for(const e in t){const s=t[e];s===r?n+=1:s>r&&(r=s,n=1,o=e)}s[i]="null"===o||n>1?null:o;break}}}return s}}class Ne{constructor(e,t,s,r){this.count=0,this.xTotal=0,this.yTotal=0,this.statistics={},this.displayId=0,this.referenceId=0,this.displayIds=new Set,this.next=null,this.depth=0,this.xNode=0,this.yNode=0,this.xGeohashTotal=0,this.yGeohashTotal=0,this._tree=e,this.children=new Array(32);for(let e=0;e<this.children.length;e++)this.children[e]=null;this.xNode=t,this.yNode=s,this.depth=r}realloc(e,t,s){for(let e=0;e<this.children.length;e++)this.children[e]=null;return this.xNode=e,this.yNode=t,this.depth=s,this.next=null,this.xGeohashTotal=0,this.yGeohashTotal=0,this.displayId=0,this.referenceId=0,this.xTotal=0,this.yTotal=0,this.count=0,this.statistics={},this.displayIds.clear(),this}get id(){return`${this.xNode}.${this.yNode}`}add(e){this.displayIds.add(e)}remove(e){this.displayIds.delete(e)}getAttributes(){const e=this._tree.normalizeStatistics(this.statistics,this.count);return e.referenceId=null,e.aggregateId=this.id,e.aggregateCount=this.count,e}getGeometry(e,t){const s=this.getLngLatBounds(),[r,i,n,o]=s,a=(0,Ze.iV)({rings:[[[r,i],[r,o],[n,o],[n,i],[r,i]]]},Ue.Z.WGS84,e),h=(0,p.Uy)(new k.Z,a,!1,!1);return(0,d.pC)(t)?(0,p.Nh)(new k.Z,h,!1,!1,"esriGeometryPolygon",t,!1,!1):h}getGeometryCentroid(e,t){const s=this.getLngLatBounds(),[r,i,n,o]=s,a=(0,Ze.iV)({x:(r+n)/2,y:(i+o)/2},Ue.Z.WGS84,e),h=(0,p.dd)(new k.Z,a);return(0,d.pC)(t)?(0,p.Nh)(new k.Z,h,!1,!1,"esriGeometryPoint",t,!1,!1):h}getLngLatBounds(){const e=this.depth,t=Math.ceil(e/2),s=Math.floor(e/2),r=30-(3*t+2*s),i=30-(2*t+3*s);return function(e,t){let s=-90,r=90,i=-180,n=180;for(let o=0;o<t;o++){const t=Math.ceil((o+1)/2),a=Math.floor((o+1)/2),h=1-o%2,l=30-(3*t+2*a),u=30-(2*t+3*a),c=2*h+3*(1-h),d=(7*h+3*(1-h)<<l&e.geohashX)>>l,g=(3*h+7*(1-h)<<u&e.geohashY)>>u;for(let e=3*h+2*(1-h)-1;e>=0;e--){const t=(i+n)/2,s=d&1<<e?1:0;i=(1-s)*i+s*t,n=(1-s)*t+s*n}for(let e=c-1;e>=0;e--){const t=(s+r)/2,i=g&1<<e?1:0;s=(1-i)*s+i*t,r=(1-i)*t+i*r}}return[i,s,n,r]}({geohashX:this.xNode<<r,geohashY:this.yNode<<i},this.depth)}find(e,t,s,r,i,n){if(r>=s)return this;const o=1-r%2,a=3*o+2*(1-o),h=2*o+3*(1-o),l=30-i-a,u=30-n-h,c=((e&7*o+3*(1-o)<<l)>>l)+((t&3*o+7*(1-o)<<u)>>u)*(8*o+4*(1-o)),d=this.children[c];return null==d?null:d.find(e,t,s,r+1,i+a,n+h)}}var De=s(60437),ze=s(68441),qe=s(59999),je=s(38913),Ge=s(6570);const We=v.Z.getLogger("esri.view.2d.layers.features.support.BinStore"),Ye=(0,De.Ue)();function Qe(e){return 57.29577951308232*e}class Xe extends qe.J{constructor(e,t,s,r){super(e,s),this.type="bin",this.events=new J.Z,this.objectIdField="aggregateId",this.featureAdapter=m.k,this._geohashLevel=5,this._geohashBuf=[],this._serviceInfo=r,this.geometryInfo=e.geometryInfo,this._spatialReference=t,this._projectionSupportCheck=(0,Ze._W)(t,Ue.Z.WGS84),this._bitsets.geohash=s.getBitset(s.createBitset()),this._bitsets.inserted=s.getBitset(s.createBitset())}destroy(){this._tree&&this._tree.destroy()}get featureSpatialReference(){return this._spatialReference}get fields(){return this._fields}async updateSchema(e,t){const s=this._schema;try{await super.updateSchema(e,t),await this._projectionSupportCheck}catch(e){}this._fields=this._schema.params.fields;const r=(0,K.Hg)(s,t);t&&(!(0,d.Wi)(r)||e.source||e.storage.filters)?(((0,K.uD)(r,"params.fields")||(0,K.uD)(r,"params")||!this._tree||e.source)&&(this._tree&&this._tree.destroy(),this._tree=new Be(this._statisticFields,this._serviceInfo),this._tree.onRelease=e=>e.displayId&&this._storage.releaseDisplayId(e.displayId),this._geohashLevel=this._schema.params.fixedBinLevel,this._rebuildTree(),(0,n.Z)("esri-2d-update-debug")&&We.info("Aggregate mesh needs update due to tree changing")),(0,n.Z)("esri-2d-update-debug")&&We.info("Aggregate mesh needs update due to tree changing"),e.targets[t.name]=!0,e.mesh=!1):s&&(e.mesh=!0)}clear(){this._rebuildTree()}sweepFeatures(e,t){this._bitsets.inserted.forEachSet((s=>{if(!e.has(s)){const e=t.lookupByDisplayIdUnsafe(s);this._remove(e)}}))}sweepAggregates(e,t,s){}onTileData(e,t,s,r,i=!0){if(!this._schema||(0,d.Wi)(t.addOrUpdate))return t;this.events.emit("changed");const n=this._getTransforms(e,this._spatialReference);{const e=t.addOrUpdate.getCursor();for(;e.next();)this._update(e,r)}if(t.status.mesh||!i)return t;const o=new Array;this._getBinsForTile(o,e,n,s),t.addOrUpdate=M.fromOptimizedFeatures(o,{...this._serviceInfo,geometryType:"esriGeometryPolygon"}),t.addOrUpdate.attachStorage(s),t.end=!0,t.isRepush||(t.clear=!0);{const r=t.addOrUpdate.getCursor();for(;r.next();){const t=r.getDisplayId();this._bitsets.computed.unset(t),this.setComputedAttributes(s,r,t,e.scale)}}return t}forEachBin(e){this._tree.forEach(e)}forEach(e){this._tree.forEach((t=>{if(t.depth!==this._geohashLevel)return;const s=this._toFeatureJSON(t),r=M.fromFeatures([s],{objectIdField:this.objectIdField,globalIdField:null,geometryType:this.geometryInfo.geometryType,fields:this.fields}).getCursor();r.next(),e(r)}))}forEachInBounds(e,t){}forEachBounds(e,t){const{hasM:s,hasZ:r}=this.geometryInfo;for(const i of e){const e=(0,p.$)(Ye,i.readGeometry(),r,s);(0,d.Wi)(e)||t(e)}}onTileUpdate(e){}getAggregate(e){const t=(0,Ce.QS)(e,!0),s=this._tree.findIf((e=>e.displayId===t));return(0,d.yw)(s,(e=>this._toFeatureJSON(e)))}getAggregates(){return this._tree.findAllIf((e=>e.depth===this._geohashLevel)).map(this._toFeatureJSON.bind(this))}getDisplayId(e){const t=this._tree.findIf((t=>t.id===e));return(0,d.yw)(t,(e=>e.displayId))}getFeatureDisplayIdsForAggregate(e){const t=this._tree.findIf((t=>t.id===e));return(0,d.R2)(t,[],(e=>Array.from(e.displayIds)))}getDisplayIdForReferenceId(e){const t=this._tree.findIf((t=>1===t.displayIds.size&&t.displayIds.has(e)));return(0,d.yw)(t,(e=>e.displayId))}_toFeatureJSON(e){const t=this._spatialReference;return{displayId:e.displayId,attributes:e.getAttributes(),geometry:(0,p.di)(e.getGeometry(t),"esriGeometryPolygon",!1,!1),centroid:null}}_rebuildTree(){this._bitsets.computed.clear(),this._bitsets.inserted.clear(),this._tree&&this._tree.clear()}_remove(e){const t=e.getDisplayId(),s=e.getXHydrated(),r=e.getYHydrated(),i=this._geohashBuf[2*t],n=this._geohashBuf[2*t+1];this._bitsets.inserted.has(t)&&(this._bitsets.inserted.unset(t),this._tree.removeCursor(e,s,r,i,n,this._geohashLevel))}_update(e,t){const s=e.getDisplayId(),r=this._bitsets.inserted,i=t.isVisible(s);if(i===r.has(s))return;if(!i)return void this._remove(e);const n=e.getXHydrated(),o=e.getYHydrated();if(!this._setGeohash(s,n,o))return;const a=this._geohashBuf[2*s],h=this._geohashBuf[2*s+1];this._tree.insertCursor(e,s,n,o,a,h,this._geohashLevel),r.set(s)}_setGeohash(e,t,s){if(this._bitsets.geohash.has(e))return!0;const r=this._geohashBuf;if(this._spatialReference.isWebMercator){const i=Qe(t/ze.sv.radius),n=i-360*Math.floor((i+180)/360);Pe(r,e,Qe(Math.PI/2-2*Math.atan(Math.exp(-s/ze.sv.radius))),n,12)}else{const i=(0,Ze.iV)({x:t,y:s},this._spatialReference,Ue.Z.WGS84);if(!i)return!1;Pe(r,e,i.y,i.x,12)}return this._bitsets.geohash.set(e),!0}_getBinsForTile(e,t,s,r){try{const i=this._getGeohashBounds(t),n=this._tree.getBins(i);for(const t of n){t.displayId||(t.displayId=r.createDisplayId(!0));let i=null;const n=t.getGeometry(this._spatialReference,s.tile);n||(i=t.getGeometryCentroid(this._spatialReference,s.tile));const o=new F.u_(n,t.getAttributes(),i);o.objectId=t.id,o.displayId=t.displayId,e.push(o)}}catch(e){return void We.error("Unable to get bins for tile",t.key.id)}}_getGeohash(e,t,s){const r={geohashX:0,geohashY:0};return Oe(r,t,e,s),r}_getGeohashBounds(e){const t=this._getGeohashLevel(e.key.level),s=[e.extent.xmin,e.extent.ymin,e.extent.xmax,e.extent.ymax],r=je.Z.fromExtent(Ge.Z.fromBounds(s,this._spatialReference)),i=(0,Ze.iV)(r,this._spatialReference,Ue.Z.WGS84,{densificationStep:64*e.resolution}),n=(0,p.Uy)(new k.Z,i,!1,!1),o=n.coords.filter(((e,t)=>!(t%2))),a=n.coords.filter(((e,t)=>t%2)),h=Math.min(...o),l=Math.min(...a),u=Math.max(...o),c=Math.max(...a),d=this._getGeohash(h,l,t),g=this._getGeohash(u,c,t);return{bounds:s,geohashBounds:{xLL:d.geohashX,yLL:d.geohashY,xTR:g.geohashX,yTR:g.geohashY},level:t}}_getGeohashLevel(e){return this._schema.params.fixedBinLevel}_getTransforms(e,t){const s={originPosition:"upperLeft",scale:[e.resolution,e.resolution],translate:[e.bounds[0],e.bounds[3]]},r=(0,l.C5)(t);if(!r)return{tile:s,left:null,right:null};const[i,n]=r.valid;return{tile:s,left:{...s,translate:[n,e.bounds[3]]},right:{...s,translate:[i-n+e.bounds[0],e.bounds[3]]}}}}const He=(0,De.Ue)();class $e extends F.nd{constructor(e,t,s,r,i){super(new k.Z([],[t,s]),r,null,e),this.geohashBoundsInfo=i}get count(){return this.attributes.cluster_count}static create(e,t,s,r,i,n,o,a){const h=new $e(t,s,r,n,o);return h.displayId=e.createDisplayId(!0),h.referenceId=a,h.tileLevel=i,h}update(e,t,s,r,i,n){return this.geometry.coords[0]=e,this.geometry.coords[1]=t,this.tileLevel=s,this.attributes=r,this.geohashBoundsInfo=i,this.referenceId=null,this.referenceId=n,this}toJSON(){return{attributes:{...this.attributes,aggregateId:this.objectId,referenceId:1===this.attributes.cluster_count?this.referenceId:null},geometry:{x:this.geometry.coords[0],y:this.geometry.coords[1]}}}}function Ve(e){return 57.29577951308232*e}class Je extends qe.J{constructor(e,t,s,r){super(e,s),this.type="cluster",this.events=new J.Z,this.objectIdField="aggregateId",this.featureAdapter=m.k,this._geohashLevel=0,this._tileLevel=0,this._aggregateValueRanges={},this._aggregateValueRangesChanged=!1,this._geohashBuf=[],this._clusters=new Map,this._tiles=new Map,this._serviceInfo=r,this.geometryInfo=e.geometryInfo,this._spatialReference=t,this._projectionSupportCheck=(0,Ze._W)(t,Ue.Z.WGS84),this._bitsets.geohash=s.getBitset(s.createBitset()),this._bitsets.inserted=s.getBitset(s.createBitset())}destroy(){this._tree.destroy()}get featureSpatialReference(){return this._spatialReference}get fields(){return this._fields}async updateSchema(e,t){const s=this._schema;try{await super.updateSchema(e,t),await this._projectionSupportCheck}catch(e){}this._fields=this._schema.params.fields;const r=(0,K.Hg)(s,t);t&&(!(0,d.Wi)(r)||e.source||e.storage.filters)?(((0,K.uD)(r,"params.fields")||!this._tree||e.source)&&(this._tree&&this._tree.destroy(),this._tree=new Be(this._statisticFields,this._serviceInfo),this._rebuildTree(),(0,n.Z)("esri-2d-update-debug")&&console.debug("Aggregate mesh needs update due to tree changing")),(0,n.Z)("esri-2d-update-debug")&&console.debug("Applying Update - ClusterStore:",r),e.targets[t.name]=!0,e.mesh=!1,this._aggregateValueRanges={}):s&&(e.mesh=!0)}clear(){this._rebuildTree()}sweepFeatures(e,t){this._bitsets.inserted.forEachSet((s=>{if(!e.has(s)){const e=t.lookupByDisplayIdUnsafe(s);this._remove(e)}}))}sweepAggregates(e,t,s){this._clusters.forEach(((r,i)=>{r&&r.tileLevel!==s&&(e.releaseDisplayId(r.displayId),t.unsetAttributeData(r.displayId),this._clusters.delete(i))}))}onTileData(e,t,s,r,i=!0){if(!this._schema||(0,d.Wi)(t.addOrUpdate))return t;this.events.emit("changed");const n=this._getTransforms(e,this._spatialReference);{const e=t.addOrUpdate.getCursor();for(;e.next();)this._update(e,r)}if(t.status.mesh||!i)return t;const o=new Array,a=this._schema.params.clusterRadius;this._getClustersForTile(o,e,a,s,n),t.addOrUpdate=M.fromOptimizedFeatures(o,this._serviceInfo),t.addOrUpdate.attachStorage(s),t.clear=!0,t.end=!0;{const r=t.addOrUpdate.getCursor();for(;r.next();){const t=r.getDisplayId();this._bitsets.computed.unset(t),this.setComputedAttributes(s,r,t,e.scale)}}return this._aggregateValueRangesChanged&&t.end&&(this.events.emit("valueRangesChanged",{valueRanges:this._aggregateValueRanges}),this._aggregateValueRangesChanged=!1),t}onTileUpdate({added:e,removed:t}){if(e.length){const t=e[0].level;this._tileLevel=t,this._setGeohashLevel(t)}if(!this._schema)return;const s=this._schema.params.clusterRadius;t.forEach((e=>{this._tiles.delete(e.key.id),this._markTileClustersForDeletion(e,s)}))}getAggregate(e){for(const t of this._clusters.values())if((t?.displayId&Ce._I)==(e&Ce._I))return t.toJSON();return null}getAggregates(){const e=[];for(const t of this._clusters.values())t?.tileLevel===this._tileLevel&&e.push(t.toJSON());return e}getDisplayId(e){const t=this._clusters.get(e);return t?t.displayId:null}getFeatureDisplayIdsForAggregate(e){const t=this._clusters.get(e);return t?this._tree.getRegionDisplayIds(t.geohashBoundsInfo):[]}getDisplayIdForReferenceId(e){for(const t of this._clusters.values())if(t?.referenceId===e)return t.displayId;return null}getAggregateValueRanges(){return this._aggregateValueRanges}forEach(e){this._clusters.forEach((t=>{if(!t)return;const s=t.toJSON(),r=M.fromFeatures([s],{objectIdField:this.objectIdField,globalIdField:null,geometryType:this.geometryInfo.geometryType,fields:this.fields}).getCursor();r.next(),e(r)}))}forEachInBounds(e,t){}forEachBounds(e,t){const{hasM:s,hasZ:r}=this.geometryInfo;for(const i of e){const e=(0,p.$)(He,i.readGeometry(),r,s);(0,d.Wi)(e)||t(e)}}size(){let e=0;return this.forEach((t=>e++)),e}_rebuildTree(){this._bitsets.computed.clear(),this._bitsets.inserted.clear(),this._tree&&this._tree.clear()}_remove(e){const t=e.getDisplayId(),s=e.getXHydrated(),r=e.getYHydrated(),i=this._geohashBuf[2*t],n=this._geohashBuf[2*t+1];this._bitsets.inserted.has(t)&&(this._bitsets.inserted.unset(t),this._tree.removeCursor(e,s,r,i,n,this._geohashLevel))}_update(e,t){const s=e.getDisplayId(),r=this._bitsets.inserted,i=t.isVisible(s);if(i===r.has(s))return;if(!i)return void this._remove(e);const n=e.getXHydrated(),o=e.getYHydrated();if(!this._setGeohash(s,n,o))return;const a=this._geohashBuf[2*s],h=this._geohashBuf[2*s+1];this._tree.insertCursor(e,s,n,o,a,h,this._geohashLevel),r.set(s)}_setGeohash(e,t,s){if(this._bitsets.geohash.has(e))return!0;const r=this._geohashBuf;if(this._spatialReference.isWebMercator){const i=Ve(t/ze.sv.radius),n=i-360*Math.floor((i+180)/360);Pe(r,e,Ve(Math.PI/2-2*Math.atan(Math.exp(-s/ze.sv.radius))),n,12)}else{const i=(0,Ze.iV)({x:t,y:s},this._spatialReference,Ue.Z.WGS84);if(!i)return!1;Pe(r,e,i.y,i.x,12)}return this._bitsets.geohash.set(e),!0}_getClustersForTile(e,t,s,r,i,n=!0){const o=this._schema.params.clusterPixelBuffer,a=2*s,h=Math.ceil(2**t.key.level*Se.I_/a)+1,l=Math.ceil(o/a)+0,u=Math.ceil(Se.I_/a),{row:c,col:g}=t.key,f=g*Se.I_,_=c*Se.I_,m=Math.floor(f/a)-l,y=Math.floor(_/a)-l,I=m+u+2*l,b=y+u+2*l,v=t.tileInfoView.getLODInfoAt(t.key.level);for(let s=m;s<=I;s++)for(let o=y;o<=b;o++){let a=s;v.wrap&&(a=s<0?s+h:s%h);const l=v.wrap&&s<0,u=v.wrap&&s%h!==s,c=this._lookupCluster(r,v,t.key.level,a,o,t);if((0,d.pC)(c)){const t=(0,d.yw)(i,(e=>l?e.left:u?e.right:e.tile));if(n&&(0,d.Wi)(t))continue;if(!c.count)continue;if((0,d.pC)(t)&&n){const s=c.geometry.clone();let r=c.attributes;s.coords[0]=(0,p.Jd)(t,s.coords[0]),s.coords[1]=(0,p.IN)(t,s.coords[1]),1===c.count&&(0,d.pC)(c.referenceId)&&(r={...c.attributes,referenceId:c.referenceId});const i=new F.u_(s,r);i.displayId=c.displayId,e.push(i)}}}}_getGeohashLevel(e){return Math.min(Math.ceil(e/2+2),12)}_setGeohashLevel(e){const t=this._getGeohashLevel(e),s=1*(Math.floor(t/1)+1)-1;if(this._geohashLevel!==s)return this._geohashLevel=s,this._rebuildTree(),void this._bitsets.geohash.clear()}_getTransforms(e,t){const s={originPosition:"upperLeft",scale:[e.resolution,e.resolution],translate:[e.bounds[0],e.bounds[3]]},r=(0,l.C5)(t);if(!r)return{tile:s,left:null,right:null};const[i,n]=r.valid;return{tile:s,left:{...s,translate:[n,e.bounds[3]]},right:{...s,translate:[i-n+e.bounds[0],e.bounds[3]]}}}_getClusterId(e,t,s){return(15&e)<<28|(16383&t)<<14|16383&s}_markForDeletion(e,t,s){const r=this._getClusterId(e,t,s);this._clusters.delete(r)}_getClusterBounds(e,t,s){const r=this._schema.params.clusterRadius,i=2*r;let n=s%2?t*i:t*i-r;const o=s*i;let a=n+i;const h=o-i,l=2**e.level*Se.I_;e.wrap&&n<0&&(n=0),e.wrap&&a>l&&(a=l);const u=n/Se.I_,c=o/Se.I_,d=a/Se.I_,g=h/Se.I_;return[e.getXForColumn(u),e.getYForRow(c),e.getXForColumn(d),e.getYForRow(g)]}_getGeohash(e,t,s){const r={geohashX:0,geohashY:0};return Oe(r,t,e,s),r}_getGeohashBounds(e,t){const s=this._getGeohashLevel(e.key.level);if(this._spatialReference.isWebMercator){const[e,r,i,n]=t,o={x:e,y:r},a={x:i,y:n};let h=0,l=0,u=0,c=0;{const e=Ve(o.x/ze.sv.radius);h=e-360*Math.floor((e+180)/360),l=Ve(Math.PI/2-2*Math.atan(Math.exp(-o.y/ze.sv.radius)))}{const e=Ve(a.x/ze.sv.radius);u=e-360*Math.floor((e+180)/360),c=Ve(Math.PI/2-2*Math.atan(Math.exp(-a.y/ze.sv.radius)))}const d={geohashX:0,geohashY:0},g={geohashX:0,geohashY:0};return Oe(d,l,h,s),Oe(g,c,u,s),{bounds:[e,r,i,n],geohashBounds:{xLL:d.geohashX,yLL:d.geohashY,xTR:g.geohashX,yTR:g.geohashY},level:s}}const r=je.Z.fromExtent(Ge.Z.fromBounds(t,this._spatialReference)),i=(0,Ze.iV)(r,this._spatialReference,Ue.Z.WGS84,{densificationStep:64*e.resolution});if(!i)return null;const n=(0,p.Uy)(new k.Z,i,!1,!1),o=n.coords.filter(((e,t)=>!(t%2))),a=n.coords.filter(((e,t)=>t%2)),h=Math.min(...o),l=Math.min(...a),u=Math.max(...o),c=Math.max(...a),d=this._getGeohash(h,l,s),g=this._getGeohash(u,c,s);return{bounds:t,geohashBounds:{xLL:d.geohashX,yLL:d.geohashY,xTR:g.geohashX,yTR:g.geohashY},level:s}}_lookupCluster(e,t,s,r,i,n){const o=this._getClusterId(s,r,i),a=this._clusters.get(o),h=this._getClusterBounds(t,r,i),l=this._getGeohashBounds(n,h);if((0,d.Wi)(l))return null;const u=this._tree.getRegionStatistics(l),{count:c,xTotal:g,yTotal:p,referenceId:f}=u,_=c?g/c:0,m=c?p/c:0;if(0===c)return this._clusters.set(o,null),null;const y={cluster_count:c,...u.attributes},I=(0,d.pC)(a)?a.update(_,m,s,y,l,f):$e.create(e,o,_,m,s,y,l,f);if(0===c){const[e,t,s,r]=h;I.geometry.coords[0]=(e+s)/2,I.geometry.coords[1]=(t+r)/2}return this._clusters.set(o,I),this._updateAggregateValueRangeForCluster(I,I.tileLevel),I}_updateAggregateValueRangeForCluster(e,t){const s=this._aggregateValueRanges[t]||{minValue:1/0,maxValue:0},r=s.minValue,i=s.maxValue;s.minValue=Math.min(r,e.count),s.maxValue=Math.max(i,e.count),this._aggregateValueRanges[t]=s,r===s.minValue&&i===s.maxValue||(this._aggregateValueRangesChanged=!0)}_markTileClustersForDeletion(e,t){const s=2*t,r=Math.ceil(Se.I_/s),{row:i,col:n}=e.key,o=n*Se.I_,a=i*Se.I_,h=Math.floor(o/s),l=Math.floor(a/s);for(let t=h;t<h+r;t++)for(let s=l;s<l+r;s++)this._markForDeletion(e.key.level,t,s)}}class Ke{constructor(){this._freeIds=[],this._idCounter=1}createId(e=!1){return(0,Ce.QS)(this._getFreeId(),e)}releaseId(e){this._freeIds.push(e)}_getFreeId(){return this._freeIds.length?this._freeIds.pop():this._idCounter++}}var et=s(93054);function tt(e,t,s){if(!(e.length>t))for(;e.length<=t;)e.push(s)}class st{constructor(){this._numerics=[],this._strings=[],this._idGenerator=new Ke,this._allocatedSize=256,this._bitsets=[],this._instanceIds=[],this._bounds=[]}createBitset(){const e=this._bitsets.length;return this._bitsets.push(et.p.create(this._allocatedSize,Ce._I)),e+1}getBitset(e){return this._bitsets[e-1]}_expand(){this._allocatedSize<<=1;for(const e of this._bitsets)e.resize(this._allocatedSize)}_ensureNumeric(e,t){this._numerics[e]||(this._numerics[e]=[]),tt(this._numerics[e],t,0)}_ensureInstanceId(e){tt(this._instanceIds,e,0)}_ensureString(e,t){this._strings[e]||(this._strings[e]=[]),tt(this._strings[e],t,null)}createDisplayId(e=!1){const t=this._idGenerator.createId();return t>this._allocatedSize&&this._expand(),(0,Ce.QS)(t,e)}releaseDisplayId(e){for(const t of this._bitsets)t.unset(e);return this._idGenerator.releaseId(e&Ce._I)}getComputedNumeric(e,t){return this.getComputedNumericAtIndex(e&Ce._I,0)}setComputedNumeric(e,t,s){return this.setComputedNumericAtIndex(e&Ce._I,s,0)}getComputedString(e,t){return this.getComputedStringAtIndex(e&Ce._I,0)}setComputedString(e,t,s){return this.setComputedStringAtIndex(e&Ce._I,0,s)}getComputedNumericAtIndex(e,t){const s=e&Ce._I;return this._ensureNumeric(t,s),this._numerics[t][s]}setComputedNumericAtIndex(e,t,s){const r=e&Ce._I;this._ensureNumeric(t,r),this._numerics[t][r]=s}getInstanceId(e){const t=e&Ce._I;return this._ensureInstanceId(t),this._instanceIds[t]}setInstanceId(e,t){const s=e&Ce._I;this._ensureInstanceId(s),this._instanceIds[s]=t}getComputedStringAtIndex(e,t){const s=e&Ce._I;return this._ensureString(t,s),this._strings[t][s]}setComputedStringAtIndex(e,t,s){const r=e&Ce._I;this._ensureString(t,r),this._strings[t][r]=s}getXMin(e){return this._bounds[4*(e&Ce._I)]}getYMin(e){return this._bounds[4*(e&Ce._I)+1]}getXMax(e){return this._bounds[4*(e&Ce._I)+2]}getYMax(e){return this._bounds[4*(e&Ce._I)+3]}setBounds(e,t){const s=t.readHydratedGeometry();if(!s||!s.coords.length)return!1;let r=1/0,i=1/0,n=-1/0,o=-1/0;s.forEachVertex(((e,t)=>{r=Math.min(r,e),i=Math.min(i,t),n=Math.max(n,e),o=Math.max(o,t)}));const a=e&Ce._I;return tt(this._bounds,4*a+4,0),this._bounds[4*a]=r,this._bounds[4*a+1]=i,this._bounds[4*a+2]=n,this._bounds[4*a+3]=o,!0}}function rt(e){if(!(0,g.D_)(e)&&!function(e){return"worker:port-closed"===e.name}(e))throw e}function it(e){return"feature"===e.type&&"snapshot"===e.mode}let nt=class extends c.Z{constructor(){super(...arguments),this._storage=new st,this._markedIdsBufId=this._storage.createBitset(),this._lastCleanup=performance.now(),this._cleanupNeeded=!1,this._invalidated=!1,this._tileToResolver=new Map,this._didEdit=!1,this._updateVersion=1,this.tileStore=null,this.config=null,this.processor=null,this.remoteClient=null,this.service=null}initialize(){this._initStores(),this._initSource(),this._updateQueue=new ne.e({concurrency:"stream"===this._source.type?1:4,process:(e,t)=>this._onTileMessage(e,{signal:t})}),this.addHandles([this.tileStore.on("update",this.onTileUpdate.bind(this)),(0,o.gx)((()=>!this.updating),(()=>this.onIdle()))]),this._checkUpdating=setInterval((()=>this.notifyChange("updating")),300)}_initSource(){const e=this.tileStore.tileScheme;this._source=function(e,t,s,r,i,n){const o=function(e,t,s,r,i,n){switch(e.type){case"snapshot":return{type:"feature",origin:"snapshot",featureCount:(0,d.Pt)(e.featureCount,0),serviceInfo:e,onMessage:r,outSR:t,tileInfoView:s,canAcceptRequest:i,store:n};case"stream":return{type:"stream",serviceInfo:e,onMessage:r,outSR:t,canAcceptRequest:i};case"memory":case"on-demand":return{type:"feature",serviceInfo:e,onMessage:r,outSR:t,origin:function(e){return Array.isArray(e)?"local":"path"in e&&(0,y.M8)(e.path)?"hosted":"unknown"}(e.source),tileInfoView:s,canAcceptRequest:i}}}(e,t,s,r,i,n);switch(o.type){case"feature":switch(o.origin){case"hosted":case"local":return new ue(o);case"snapshot":return new fe(o);default:return new le(o)}case"stream":return new we(o)}}(this.service,this.spatialReference,e,((e,t)=>(this._invalidated=!0,this._patchTile(e,t))),(()=>this._updateQueue&&this._updateQueue.length<50),this.featureStore),this._proxyEvents()}_proxyEvents(){if("stream"===this._source.type){const e=this._source.events,t=this._source;this.addHandles([(0,o.YP)((()=>t.connectionStatus),(e=>this.remoteClient.invoke("setProperty",{propertyName:"connectionStatus",value:e}).catch(rt)),{initial:!0}),(0,o.YP)((()=>t.errorString),(e=>this.remoteClient.invoke("setProperty",{propertyName:"errorString",value:e}).catch(rt)),{initial:!0}),e.on("data-received",(e=>this.remoteClient.invoke("emitEvent",{name:"data-received",event:{attributes:e.attributes,centroid:e.centroid,geometry:e.geometry}}).catch(rt))),e.on("message-received",(e=>this.remoteClient.invoke("emitEvent",{name:"message-received",event:e}).catch(rt))),e.on("updateRate",(e=>this.remoteClient.invoke("emitEvent",{name:"update-rate",event:{...e}}).catch(rt)))])}}_initAttributeStore(e){this.attributeStore||(this.attributeStore=new Le({type:"remote",initialize:(e,t)=>(0,g.R8)(this.remoteClient.invoke("tileRenderer.featuresView.attributeView.initialize",e,{signal:t}).catch(rt)),update:(e,t)=>(0,g.R8)(this.remoteClient.invoke("tileRenderer.featuresView.attributeView.requestUpdate",e,{signal:t}).catch(rt)),render:e=>(0,g.R8)(this.remoteClient.invoke("tileRenderer.featuresView.requestRender",void 0,{signal:e}).catch(rt))},e,(()=>this.notifyChange("updating"))))}_initStores(){const e="snapshot"===this.service.type?"snapshot":"on-demand",t={geometryInfo:{geometryType:this.service.geometryType,hasM:!1,hasZ:!1},spatialReference:this.spatialReference,fieldsIndex:this.fieldsIndex,fields:this.service.fields};this.featureStore=new m.p(t,this._storage,e)}_initQueryEngine(e){const t=this;this.featureQueryEngine?.destroy(),this.featureQueryEngine=new f.q({definitionExpression:e.schema.source.definitionExpression??void 0,fields:this.service.fields,geometryType:this.service.geometryType,objectIdField:this.service.objectIdField,hasM:!1,hasZ:!1,spatialReference:this.spatialReference.toJSON(),cacheSpatialQueries:!0,featureStore:this.featureStore,aggregateAdapter:{getFeatureObjectIds:e=>(0,d.Wi)(t.aggregateStore)?[]:t.aggregateStore.getFeatureDisplayIdsForAggregate(e).map((e=>t.getObjectId(e)))},timeInfo:this.service.timeInfo})}_initAggregateQueryEngine(e,t){if(this.aggregateQueryEngine?.destroy(),(0,d.Wi)(e))return;const s=t.targets.aggregate.params.fields.slice();this.aggregateQueryEngine=new f.q({definitionExpression:void 0,fields:s,geometryType:e.geometryInfo.geometryType,objectIdField:e.objectIdField,hasM:e.geometryInfo.hasM,hasZ:e.geometryInfo.hasZ,spatialReference:this.spatialReference.toJSON(),cacheSpatialQueries:!1,featureStore:e,aggregateAdapter:{getFeatureObjectIds:e=>[]}})}destroy(){this._updateQueue.destroy(),this._source.destroy(),this.featureQueryEngine?.destroy(),this.aggregateQueryEngine?.destroy(),this.attributeStore?.destroy();for(const e of this.tileStore.tiles)this._source.unsubscribe(e);clearInterval(this._checkUpdating)}get fieldsIndex(){return new _.Z(this.service.fields)}get spatialReference(){return this.tileStore.tileScheme.spatialReference}get updating(){return this.isUpdating()}isUpdating(){const e=this._source.updating,t=!!this._updateQueue.length,s=!this.attributeStore||this.attributeStore.isUpdating(),r=e||t||s;return(0,n.Z)("esri-2d-log-updating")&&console.log(`Updating FeatureController2D: ${r}\n  -> updatingSource ${e}\n  -> updateQueue ${t}\n  -> updatingAttributeStore ${s}\n`),r}updateCustomParameters(e){"stream"===this._source.type&&this._source.updateCustomParameters(e)}enableEvent(e){this._source.enableEvent(e.name,e.value)}pause(){this._updateQueue.pause(),this._updateQueue.clear()}resume(){this._updateQueue.resume()}pauseStream(){"stream"===this._source.type&&this._source.pauseStream()}resumeStream(){"stream"===this._source.type&&this._source.resumeStream()}sendMessageToSocket(e){"stream"===this._source.type&&this._source.sendMessageToSocket(e)}sendMessageToClient(e){"stream"===this._source.type&&this._source.sendMessageToClient(e)}_initAggregateStore(e){const t=e.schema.targets?.aggregate?.type,s=(0,d.yw)(this.config,(e=>e.schema.targets?.aggregate?.type));if(s!==t&&((0,d.pC)(this.aggregateStore)&&(this.removeHandles("valueRangesChanged"),this.aggregateStore.destroy(),this.aggregateStore=null),t)){switch(t){case"cluster":{const e={geometryInfo:{geometryType:"esriGeometryPoint",hasM:!1,hasZ:!1},spatialReference:this.spatialReference,fieldsIndex:this.fieldsIndex,fields:this.service.fields};this.aggregateStore=new Je(e,this.spatialReference,this._storage,this.service),this.addHandles(this.aggregateStore.events.on("valueRangesChanged",(e=>{this.remoteClient.invoke("emitEvent",{name:"valueRangesChanged",event:{valueRanges:e.valueRanges}}).catch(rt)})),"valueRangesChanged");break}case"bin":{const e={geometryInfo:{geometryType:"esriGeometryPolygon",hasM:!1,hasZ:!1},spatialReference:this.spatialReference,fieldsIndex:this.fieldsIndex,fields:this.service.fields};this.aggregateStore=new Xe(e,this.spatialReference,this._storage,this.service);break}}this.aggregateStore.onTileUpdate({added:this.tileStore.tiles,removed:[]})}}async update(e,t){this._updateVersion++,this._initQueryEngine(t),this._initAttributeStore(t),this.pause(),await Promise.all([this._source.update(e,t.schema.source),this.featureStore.updateSchema(e,t.schema.targets.feature),this.attributeStore.update(e,t),this.attributeStore.updateFilters(e,t,this)]),this._initAggregateStore(t),(0,d.pC)(this.aggregateStore)&&await this.aggregateStore.updateSchema(e,t.schema.targets.aggregate),this._initAggregateQueryEngine(this.aggregateStore,t.schema),(0,n.Z)("esri-2d-update-debug")&&e.describe(),this._set("config",t)}async applyUpdate(e){e.version=this._updateVersion,(0,n.Z)("esri-2d-update-debug")&&console.debug(`Applying update ${e.version}`),e.mesh&&this.clearTiles(),this._updateQueue.resume(),await this._source.applyUpdate(e),this.notifyChange("updating"),await(0,o.N1)((()=>!this.updating)),(0,d.pC)(this.aggregateStore)&&(await(0,g.e4)(10),await(0,o.N1)((()=>!this.updating)))}async onEdits({edits:e}){(0,n.Z)("esri-2d-update-debug")&&console.debug("Applying Edit:",e),this._didEdit=!0;try{const t=e.removed.map((e=>e.objectId&&-1!==e.objectId?e.objectId:this._lookupObjectIdByGlobalId(e.globalId))),s=e.addOrModified.map((({objectId:e})=>e));this.featureStore.invalidate(),await this._source.edit(s,t),this.clearTiles(),this.notifyChange("updating"),(0,d.pC)(this.aggregateStore)&&this.aggregateStore.clear(),await this._source.resend(),await(0,o.N1)((()=>!this.updating))}catch(e){}}async refresh(e){if(!e.dataChanged){const e=se.empty();return e.storage.filters=!0,this.applyUpdate(e)}this.featureStore.invalidate(),this.clearTiles(),this._source.refresh(this._updateVersion,e),this._cleanupNeeded=!0,this.notifyChange("updating"),await(0,o.N1)((()=>!this.updating))}clearTiles(){for(const e of this.tileStore.tiles)this.processor.onTileClear(e)}onTileUpdate(e){(0,d.pC)(this.aggregateStore)&&this.aggregateStore.onTileUpdate(e);for(const t of e.added)this._source.subscribe(t,this._updateVersion),this._level=t.level;for(const t of e.removed)this._source.unsubscribe(t),this._cleanupNeeded=!0,this._tileToResolver.has(t.id)&&(this._tileToResolver.get(t.id).resolve(),this._tileToResolver.delete(t.id));this.notifyChange("updating")}async onIdle(){this._invalidated&&(this._invalidated=!1,((0,d.pC)(this.aggregateStore)||"heatmap"===this.processor.type)&&await this._repushCurrentLevelTiles()),this._markAndSweep()}async querySummaryStatistics({query:e,params:t}){return this.featureQueryEngine.executeQueryForSummaryStatistics(e,t)}async queryAggregateSummaryStatistics({query:e,params:t}){return this.aggregateQueryEngine.executeQueryForSummaryStatistics(e,t)}async queryUniqueValues({query:e,params:t}){return this.featureQueryEngine.executeQueryForUniqueValues(e,t)}async queryAggregateUniqueValues({query:e,params:t}){return this.aggregateQueryEngine.executeQueryForUniqueValues(e,t)}async queryClassBreaks({query:e,params:t}){return this.featureQueryEngine.executeQueryForClassBreaks(e,t)}async queryAggregateClassBreaks({query:e,params:t}){return this.aggregateQueryEngine.executeQueryForClassBreaks(e,t)}async queryHistogram({query:e,params:t}){return this.featureQueryEngine.executeQueryForHistogram(e,t)}async queryAggregateHistogram({query:e,params:t}){return this.aggregateQueryEngine.executeQueryForHistogram(e,t)}queryExtent(e){return this.featureQueryEngine.executeQueryForExtent(e)}queryAggregates(e){return this.aggregateQueryEngine.executeQuery(e)}queryAggregateCount(e){return this.aggregateQueryEngine.executeQueryForCount(e)}queryAggregateIds(e){return this.aggregateQueryEngine.executeQueryForIds(e)}queryFeatures(e){return this.featureQueryEngine.executeQuery(e)}async queryVisibleFeatures(e){const t=await this.featureQueryEngine.executeQuery(e),s=t.objectIdFieldName;return t.features=t.features.filter((e=>{const t=e.attributes[s],r=this.getDisplayId(t);return(0,d.yw)(r,(e=>this.attributeStore.isVisible(e)))})),t}queryFeatureCount(e){return this.featureQueryEngine.executeQueryForCount(e)}queryLatestObservations(e){return this.featureQueryEngine.executeQueryForLatestObservations(e)}queryObjectIds(e){return this.featureQueryEngine.executeQueryForIds(e)}async queryStatistics(){return this.featureStore.storeStatistics}getObjectId(e){return this.featureStore.lookupObjectId(e,this._storage)}getDisplayId(e){if((0,d.pC)(this.aggregateStore)){const t=this.aggregateStore.getDisplayId(e);if((0,d.Wi)(t)){const t=this.featureStore.lookupDisplayId(e);return this.aggregateStore.getDisplayIdForReferenceId(t)}return t}return this.featureStore.lookupDisplayId(e)}getFeatures(e){const t=[],s=[];for(const r of e){const e=(0,d.pC)(this.aggregateStore)?this.getAggregate(r):null;if((0,d.pC)(e))if((0,d.pC)(e.attributes.referenceId)){const s=this.getFeature(e.attributes.referenceId);(0,d.pC)(s)&&t.push(s)}else s.push(e);else{const e=this.getFeature(r);(0,d.pC)(e)&&t.push(e)}}return{features:t,aggregates:s}}getFeature(e){const t=this.featureStore.lookupFeatureByDisplayId(e,this._storage);if((0,d.Wi)(t))return null;const s=t.readHydratedGeometry(),r=(0,p.di)(s,t.geometryType,t.hasZ,t.hasM);return{attributes:t.readAttributes(),geometry:r}}getAggregate(e){return(0,d.Wi)(this.aggregateStore)?null:this.aggregateStore.getAggregate(e)}getAggregates(){return(0,d.Wi)(this.aggregateStore)?[]:this.aggregateStore.getAggregates()}async setHighlight(e){const t=(0,d.lV)(e.map((e=>this.getDisplayId(e))));return this.attributeStore.setHighlight(e,t)}_lookupObjectIdByGlobalId(e){const t=this.service.globalIdField;if((0,d.Wi)(t))throw new Error("Expected globalIdField to be defined");let s=null;if(this.featureStore.forEach((r=>{e===r.readAttribute(t)&&(s=r.getObjectId())})),(0,d.Wi)(s))throw new Error(`Expected to find a feature with globalId ${e}`);return s}async _repushCurrentLevelTiles(){const e=this.tileStore.tiles.filter((e=>e.level===this._level));e.map((async e=>this._patchTile({type:"append",id:e.key.id,clear:!0,addOrUpdate:null,end:!1})));const t=e.map((async e=>this._patchTile({type:"append",id:e.key.id,addOrUpdate:M.fromOptimizedFeatures([],this.service),remove:[],end:!0,isRepush:!0,status:se.empty()})));await Promise.all(t)}_maybeForceCleanup(){performance.now()-this._lastCleanup>5e3&&this._markAndSweep()}_patchTile(e,t){const s=this._updateQueue.push(e,t).then((()=>{this.notifyChange("updating")})).catch((e=>{this.notifyChange("updating")}));return this.notifyChange("updating"),s}async _onTileMessage(e,t){if((0,g.k_)(t),(0,n.Z)("esri-2d-update-debug")){const t=(0,d.yw)(e.addOrUpdate,(e=>e.hasFeatures));console.debug(e.id,`FeatureController:onTileMessage: [clear:${e.clear}, end:${e.end}, features: ${t}]`)}const s=this.tileStore.get(e.id);if(!s)return;if(e.clear)return this.processor.onTileClear(s);const r=e.status;this._cleanupNeeded=!0;const i=[];for(const t of e.remove??[]){const e=this.featureStore.lookupDisplayId(t);e&&i.push(e)}e.remove=i;try{if((0,d.Wi)(e.addOrUpdate))return void this.processor.onTileMessage(s,{...e,addOrUpdate:null},(0,d.pC)(this.aggregateStore),t).catch(g.H9);if(e.addOrUpdate.setArcadeSpatialReference(this.spatialReference),this.featureStore.hasInstance(e.addOrUpdate.instance)&&r.targets.feature||(r.targets.feature=!0,this.featureStore.onTileData(s,e)),r.storage.data&&r.storage.filters||(r.storage.data=!0,r.storage.filters=!0,this.attributeStore.onTileData(s,e),"stream"===this._source.type||this._didEdit?(await this.attributeStore.sendUpdates(),(0,g.k_)(t)):this.attributeStore.sendUpdates()),(0,d.pC)(this.aggregateStore)&&!r.targets.aggregate){r.targets.aggregate=!0;const t=it(this._source)&&this._source.loading,i=!it(this._source)||t||e.end;if(this.aggregateStore.onTileData(s,e,this._storage,this.attributeStore,i),!i)return;r.mesh||(this.attributeStore.onTileData(s,e),await this.attributeStore.sendUpdates())}if(!r.mesh){r.mesh=!0;const i=(0,d.pC)(this.aggregateStore)&&"cluster"===this.aggregateStore.type;await this.processor.onTileMessage(s,e,i,t),(0,g.k_)(t)}this._maybeForceCleanup()}catch(e){(0,g.H9)(e)}}_mark(e,t,s){const r=(4294901760&this._storage.getInstanceId(e))>>>16;e&&(t.add(r),s.set(e))}_markAndSweep(){if(this._lastCleanup=performance.now(),"feature"===this._source.type&&"snapshot"===this._source.mode||"stream"!==this._source.type&&!this._cleanupNeeded)return;this._cleanupNeeded=!1;const e=this._storage.getBitset(this._markedIdsBufId),t=new Set;e.clear();for(const s of this.tileStore.tiles)for(const r of this._source.readers(s.id)){const s=r.getCursor();for(;s.next();){let r=s.getDisplayId();if(!r){const e=s.getObjectId();r=this.featureStore.lookupDisplayId(e)}this._mark(r,t,e)}}"symbol"===this.processor.type&&this.processor.forEachBufferId((s=>{this._mark(s,t,e)})),this._updateQueue.forEach((s=>{for(const r of s.remove??[]){const s=this.featureStore.lookupDisplayId(r);this._mark(s,t,e)}})),(0,d.pC)(this.aggregateStore)&&(this.aggregateStore.sweepFeatures(e,this.featureStore),"sweepAggregates"in this.aggregateStore&&this.aggregateStore.sweepAggregates(this._storage,this.attributeStore,this._level)),this.featureStore.sweepFeatures(e,this._storage,this.attributeStore),this.featureStore.sweepFeatureSets(t)}};(0,r._)([(0,a.Cb)({constructOnly:!0})],nt.prototype,"tileStore",void 0),(0,r._)([(0,a.Cb)()],nt.prototype,"config",void 0),(0,r._)([(0,a.Cb)({readOnly:!0})],nt.prototype,"fieldsIndex",null),(0,r._)([(0,a.Cb)()],nt.prototype,"processor",void 0),(0,r._)([(0,a.Cb)({constructOnly:!0})],nt.prototype,"remoteClient",void 0),(0,r._)([(0,a.Cb)({constructOnly:!0})],nt.prototype,"service",void 0),(0,r._)([(0,a.Cb)()],nt.prototype,"spatialReference",null),(0,r._)([(0,a.Cb)()],nt.prototype,"updating",null),nt=(0,r._)([(0,h.j)("esri.views.2d.layers.features.controllers.FeatureController2D")],nt);const ot=nt;var at=s(3894),ht=s(24470),lt=s(6388),ut=s(55415);class ct{constructor(e,t){this.key=new ut.Z(0,0,0,0),this.bounds=(0,ht.Ue)(),this.objectIds=new Set,this.key.set(t);const s=e.getLODInfoAt(this.key);this.tileInfoView=e,this.tileInfoView.getTileBounds(this.bounds,this.key,!0),this.resolution=s.resolution,this.scale=s.scale,this.level=s.level}get id(){return this.key.id}get extent(){return Ge.Z.fromBounds(this.bounds,this.tileInfoView.tileInfo.spatialReference)}get transform(){return{originPosition:"upperLeft",scale:[this.resolution,this.resolution],translate:[this.bounds[0],this.bounds[3]]}}createChildTiles(){const e=this.key.getChildKeys(),t=at.Z.acquire();for(let s=0;s<e.length;s++)t[s]=new ct(this.tileInfoView,e[s]);return t}getQuantizationParameters(){return lt.Z.fromJSON({mode:"view",originPosition:"upperLeft",tolerance:this.resolution,extent:{xmin:this.bounds[0],ymin:this.bounds[1],xmax:this.bounds[2],ymax:this.bounds[3],spatialReference:this.tileInfoView.tileInfo.spatialReference}})}}var dt=s(67524);const gt={added:[],removed:[]},pt=new Set,ft=new ut.Z(0,0,0,0);class _t extends J.Z{constructor(e){super(),this._tiles=new Map,this._index=(0,_e.r)(9,(0,n.Z)("esri-csp-restrictions")?e=>({minX:e.bounds[0],minY:e.bounds[1],maxX:e.bounds[2],maxY:e.bounds[3]}):[".bounds[0]",".bounds[1]",".bounds[2]",".bounds[3]"]),this.tiles=[],this.tileScheme=e}destroy(){this.clear()}clear(){this.tiles.length=0,this._tiles.clear(),this._index.clear()}has(e){return this._tiles.has(e)}get(e){return this._tiles.get(e)}boundsIntersections(e){return this._index.search({minX:e[0],minY:e[1],maxX:e[2],maxY:e[3]})}updateTiles(e){const t={added:[],removed:[]};for(const s of e.added)if(!this.has(s)){const e=new ct(this.tileScheme,s);this._tiles.set(s,e),this._index.insert(e),t.added.push(e)}for(const s of e.removed)if(this.has(s)){const e=this.get(s);this._tiles.delete(s),this._index.remove(e),t.removed.push(e)}this.tiles.length=0,this._tiles.forEach((e=>this.tiles.push(e))),(t.added.length||t.removed.length)&&this.emit("update",t)}setViewState(e){const t=this.tileScheme.getTileCoverage(e,0);if(!t)return;const{spans:s,lodInfo:r}=t,{level:i}=r;if(s.length>0)for(const{row:e,colFrom:t,colTo:n}of s)for(let s=t;s<=n;s++){const t=ft.set(i,e,r.normalizeCol(s),r.getWorldForColumn(s)).id;if(pt.add(t),!this.has(t)){const e=new ct(this.tileScheme,t);this._tiles.set(t,e),this._index.insert(e),this.tiles.push(e),gt.added.push(e)}}for(let e=this.tiles.length-1;e>=0;e--){const t=this.tiles[e];pt.has(t.id)||(this._tiles.delete(t.id),this.tiles.splice(e,1),this._index.remove(t),gt.removed.push(t))}(gt.added.length||gt.removed.length)&&this.emit("update",gt),dt.Z.pool.release(t),pt.clear(),gt.added.length=0,gt.removed.length=0}}var mt=s(83068);let yt=class extends i.r{constructor(){super(...arguments),this.controller=null,this.processor=null,this.remoteClient=null,this.tileStore=null,this.service=null,this.viewState=null,this._paused=!1,this._pendingTileUpdates=[]}initialize(){this.handles.add((0,o.YP)((()=>this.updating),(e=>{this.remoteClient.invoke("setUpdating",e).catch((e=>{}))})))}destroy(){this.stop(),this.controller?.destroy(),this.processor?.destroy(),this.controller=this.processor=this.tileStore=this.remoteClient=null}get updating(){return!this.controller||this.controller.updating}stop(){this._paused=!0,Array.isArray(this.service?.source)&&(this.service.source.forEach((e=>e.close())),this.service.source.length=0),this.tileStore?.updateTiles({added:[],removed:this.tileStore.tiles.map((e=>e.id))}),this.tileStore?.destroy(),this.tileStore=null,this._pendingTileUpdates.length=0}async startup({service:e,config:t,tileInfo:s,tiles:r}){if(this._paused=!0,Array.isArray(this.service?.source)&&(this.service.source.forEach((e=>e.close())),this.service.source.length=0),this.service=e,!this.tileStore||!(0,l.fS)(this.tileStore.tileScheme.spatialReference,s.spatialReference)){const e=new mt.Z(u.Z.fromJSON(s));r.added.length=r.removed.length=0,this.tileStore?.updateTiles({added:[],removed:this.tileStore.tiles.map((e=>e.id))}),this.tileStore?.destroy(),this.tileStore=new _t(e),this._pendingTileUpdates.length=0}for(await this._createProcessorAndController(t),await this.update({config:t}),this.controller.resume(),this.tileStore.clear(),this.tileStore.updateTiles(r),this._paused=!1;this._pendingTileUpdates.length;)this.tileStore.updateTiles(this._pendingTileUpdates.pop())}async updateTiles(e){this._paused?this._pendingTileUpdates.push(e):this.tileStore?.updateTiles(e)}async update({config:e}){const t=se.empty();return await Promise.all([this.processor.update(t,e),this.controller.update(t,e)]),t.toJSON()}async applyUpdate(e){return this.controller.applyUpdate(se.create(e))}async _createProcessorAndController(e){await Promise.all([this._handleControllerConfig(e),this._handleProcessorConfig(e)]),this.controller.processor=this.processor}async _handleControllerConfig(e){return this._createController(this.service,e)}async _handleProcessorConfig(e){return this._createProcessor(this.service,e)}async _createController(e,t){this.controller&&this.controller.destroy();const{tileStore:s,remoteClient:r}=this,i=new ot({service:e,tileStore:s,remoteClient:r});return this.controller=i,i}async _createProcessor(e,t){const r=t.schema.processors[0].type,i=(await function(e){return"heatmap"===e?s.e(9904).then(s.bind(s,49904)):Promise.all([s.e(5587),s.e(4982),s.e(9717)]).then(s.bind(s,19717))}(r)).default,{remoteClient:n,tileStore:o}=this,a=new i({service:e,config:t,tileStore:o,remoteClient:n});return this.processor&&this.processor.destroy(),this.processor=a,a}};(0,r._)([(0,a.Cb)()],yt.prototype,"controller",void 0),(0,r._)([(0,a.Cb)()],yt.prototype,"processor",void 0),(0,r._)([(0,a.Cb)()],yt.prototype,"updating",null),(0,r._)([(0,a.Cb)()],yt.prototype,"viewState",void 0),yt=(0,r._)([(0,h.j)("esri.views.2d.layers.features.Pipeline")],yt);const It=yt},59999:(e,t,s)=>{s.d(t,{J:()=>l});var r=s(80442),i=s(70586),n=s(22862),o=s(59266),a=s(92604);const h=s.e(4695).then(s.bind(s,14695));class l{constructor(e,t){this._canCacheExpressionValue=!1,this._sourceInfo=e,this._storage=t,this._bitsets={computed:t.getBitset(t.createBitset())}}get storage(){return this._storage}invalidate(){this._bitsets.computed.clear()}async updateSchema(e,t){const s=(0,n.Hg)(this._schema,t);if(this._schema=t,!t||(0,i.Wi)(s)||!(0,n.uD)(s,"attributes"))return;(0,r.Z)("esri-2d-update-debug")&&console.debug("Applying Update - Store:",s),this._bitsets.computed.clear(),e.targets[t.name]=!0;const o=t.attributes,a=[],h=[];for(const e in o){const t=o[e];switch(t.type){case"field":break;case"expression":a.push(this._createArcadeComputedField(t));break;case"label-expression":a.push(this._createLabelArcadeComputedField(t));break;case"statistic":h.push(t)}}this._computedFields=await Promise.all(a),this._canCacheExpressionValue=!this._computedFields.some((e=>"expression"===e.type&&(0,i.pC)(e.expression)&&e.expression.referencesScale())),this._statisticFields=h}setComputedAttributes(e,t,s,r){const i=this._bitsets.computed;if(!this._canCacheExpressionValue||!i.has(s)){i.set(s);for(const i of this._computedFields){const n=this._evaluateField(t,i,r);switch(i.resultType){case"numeric":e.setComputedNumericAtIndex(s,i.fieldIndex,n);break;case"string":e.setComputedStringAtIndex(s,i.fieldIndex,n)}}}}async _createArcadeComputedField(e){const t=this._sourceInfo.spatialReference,s=this._sourceInfo.fieldsIndex;return{...e,expression:await(0,o.Yi)(e.valueExpression,t,s)}}async _createLabelArcadeComputedField(e){const t=this._sourceInfo.spatialReference,s=this._sourceInfo.fieldsIndex,{createLabelFunction:r}=await h,i=await r(e.label,s,t);return{...e,builder:i}}_evaluateField(e,t,s){switch(t.type){case"label-expression":{const s=e.readArcadeFeature();return t.builder.evaluate(s)||""}case"expression":{const{expression:r}=t;return function(e,t,s){if((0,i.Wi)(e))return null;const r=t.readArcadeFeature();try{return e.evaluate({...s,$feature:r})}catch(e){return a.Z.getLogger("esri.views.2d.support.arcadeOnDemand").warn("Feature arcade evaluation failed:",e),null}}(r,e,{$view:{scale:s}})}}}}},87554:(e,t,s)=>{s.d(t,{s:()=>S}),s(66577);var r=s(48853),i=s(85839),n=s(80442),o=s(70586),a=s(29730),h=s(98732),l=s(5428),u=s(93054),c=s(33955);let d=0;const g=(0,n.Z)("featurelayer-simplify-thresholds")??[.5,.5,.5,.5],p=g[0],f=g[1],_=g[2],m=g[3],y=(0,n.Z)("featurelayer-simplify-payload-size-factors")??[1,2,4],I=y[0],b=y[1],v=y[2],x=(0,n.Z)("featurelayer-simplify-mobile-factor")??2,w=(0,n.Z)("esri-mobile");class S{constructor(e,t){this.type="FeatureSetReader",this.arcadeDeclaredClass="esri.arcade.Feature",this.seen=!1,this.instance=0,this._tx=0,this._ty=0,this._sx=1,this._sy=1,this._deleted=null,this._joined=[],this._objectIdToIndex=null,this._level=0,this._datetimeMetadata=null,this.contextTimeReference=null,this.instance=e,this._layerSchema=t}static createInstance(){return d++,d=d>65535?0:d,d}get isEmpty(){return(0,o.pC)(this._deleted)&&this._deleted.countSet()===this.getSize()}set level(e){this._level=e}getAreaSimplificationThreshold(e,t){let s=1;const r=w?x:1;t>4e6?s=v*r:t>1e6?s=b*r:t>5e5?s=I*r:t>1e5&&(s=r);let i=0;e>4e3?i=m*s:e>2e3?i=_*s:e>100?i=f:e>15&&(i=p);let n=8;return this._level<4?n=1:this._level<5?n=2:this._level<6&&(n=4),i*n}createQuantizedExtrudedQuad(e,t){return new l.Z([5],[e-1,t,1,-1,1,1,-1,1,-1,-1])}setArcadeSpatialReference(e){this._arcadeSpatialReference=e}attachStorage(e){this._storage=e}getQuantizationTransform(){throw new Error("Unable to find transform for featureSet")}getStorage(){return this._storage}getComputedNumeric(e){return this.getComputedNumericAtIndex(0)}setComputedNumeric(e,t){return this.setComputedNumericAtIndex(t,0)}getComputedString(e){return this.getComputedStringAtIndex(0)}setComputedString(e,t){return this.setComputedStringAtIndex(0,t)}getComputedNumericAtIndex(e){return this._storage.getComputedNumericAtIndex(this.getDisplayId(),e)}setComputedNumericAtIndex(e,t){this._storage.setComputedNumericAtIndex(this.getDisplayId(),e,t)}getComputedStringAtIndex(e){return this._storage.getComputedStringAtIndex(this.getDisplayId(),e)}setComputedStringAtIndex(e,t){return this._storage.setComputedStringAtIndex(this.getDisplayId(),e,t)}transform(e,t,s,r){const i=this.copy();return i._tx+=e,i._ty+=t,i._sx*=s,i._sy*=r,i}readAttribute(e,t=!1){const s=this._readAttribute(e,t);if(void 0!==s)return s;for(const s of this._joined){s.setIndex(this.getIndex());const r=s._readAttribute(e,t);if(void 0!==r)return r}}readAttributes(){const e=this._readAttributes();for(const t of this._joined){t.setIndex(this.getIndex());const s=t._readAttributes();for(const t of Object.keys(s))e[t]=s[t]}return e}joinAttributes(e){this._joined.push(e)}readArcadeFeature(){return this}geometry(){const e=this.readHydratedGeometry(),t=(0,h.di)(e,this.geometryType,this.hasZ,this.hasM),s=(0,c.im)(t);return s&&(s.spatialReference=this._arcadeSpatialReference),s}get dateTimeReferenceFieldIndex(){return this._datetimeMetadata||(this._datetimeMetadata=i.nu.create(this._layerSchema.fields,this._layerSchema)),this._datetimeMetadata}autocastArcadeDate(e,t){return t&&t instanceof Date?this.isUnknownDateTimeField(e)?r.iG.unknownDateJSToArcadeDate(t):r.iG.dateJSAndZoneToArcadeDate(t,this.contextTimeReference?.timeZone??"system"):t}isUnknownDateTimeField(e){return"unknown"===this.dateTimeReferenceFieldIndex?.fieldTimeZone(e)}fieldSourceTimeZone(e){return this.dateTimeReferenceFieldIndex?.fieldTimeZone(e)??""}get layerPreferredTimeZone(){return this.dateTimeReferenceFieldIndex?.layerPreferredTimeZone??""}field(e){if(this.hasField(e))return this.autocastArcadeDate(e,this.readAttribute(e,!0));for(const t of this._joined)if(t.setIndex(this.getIndex()),t.hasField(e)){const s=t._readAttribute(e,!0);return this.autocastArcadeDate(e,s)}throw new Error(`Field ${e} does not exist`)}setField(e,t){throw new Error("Unable to update feature attribute values, feature is readonly")}keys(){return this.getFieldNames()}castToText(e=!1){if(!e)return JSON.stringify(this.readLegacyFeature());const t=this.readLegacyFeature();if(!t)return JSON.stringify(null);const s={geometry:t.geometry,attributes:{...t.attributes?t.attributes:{}}};for(const e in s.attributes){const t=s.attributes[e];t instanceof Date&&(s.attributes[e]=t.getTime())}return JSON.stringify(s)}gdbVersion(){return null}fullSchema(){return this._layerSchema}castAsJson(e=null){return{attributes:this._readAttributes(),geometry:!0===e?.keepGeometryType?this.geometry():this.geometry()?.toJSON()??null}}castAsJsonAsync(e=null,t=null){return Promise.resolve(this.castAsJson(t))}removeIds(e){if((0,o.Wi)(this._objectIdToIndex)){const e=new Map,t=this.getCursor();for(;t.next();){const s=(0,o.s3)(t.getObjectId());e.set(s,t.getIndex())}this._objectIdToIndex=e}const t=this._objectIdToIndex;for(const s of e)t.has(s)&&this.removeAtIndex(t.get(s))}removeAtIndex(e){(0,o.Wi)(this._deleted)&&(this._deleted=u.p.create(this.getSize())),this._deleted.set(e)}readGeometryForDisplay(){return this.readUnquantizedGeometry(!0)}readLegacyGeometryForDisplay(){return this.readLegacyGeometry(!0)}*features(){const e=this.getCursor();for(;e.next();)yield e.readOptimizedFeature()}_getExists(){return(0,o.Wi)(this._deleted)||!this._deleted.has(this.getIndex())}_computeCentroid(){if("esriGeometryPolygon"!==this.geometryType)return null;const e=this.readUnquantizedGeometry();if(!e||e.hasIndeterminateRingOrder)return null;const t=(0,o.Pt)(this.getQuantizationTransform(),null);return(0,a.Y)(new l.Z,e,this.hasM,this.hasZ,t)}copyInto(e){e.seen=this.seen,e._storage=this._storage,e._arcadeSpatialReference=this._arcadeSpatialReference,e._joined=this._joined,e._tx=this._tx,e._ty=this._ty,e._sx=this._sx,e._sy=this._sy,e._deleted=this._deleted,e._objectIdToIndex=this._objectIdToIndex}}},61800:(e,t,s)=>{s.d(t,{t:()=>i});var r=s(87554);class i extends r.s{static from(e,t){return new i(e.copy(),t)}constructor(e,t){super(r.s.createInstance(),e.fullSchema()),this._currentIndex=-1,this._reader=e,this._indices=t}get hasNext(){return this._currentIndex+1<this._indices.length}getSize(){return this._indices.length}getCursor(){return this.copy()}copy(){const e=new i(this._reader.copy(),this._indices);return e._currentIndex=this._currentIndex,e}next(){for(;this._nextIndex()&&!this._reader._getExists(););return this._currentIndex<this._indices.length}_nextIndex(){return++this._currentIndex<this._indices.length&&(this._reader.setIndex(this._indices[this._currentIndex]),!0)}setArcadeSpatialReference(e){this._reader.setArcadeSpatialReference(e)}attachStorage(e){this._reader.attachStorage(e)}get geometryType(){return this._reader.geometryType}get hasFeatures(){return this._reader.hasFeatures}get exceededTransferLimit(){return this._reader.exceededTransferLimit}get hasZ(){return this._reader.hasZ}get hasM(){return this._reader.hasM}getStorage(){return this._reader.getStorage()}getComputedNumeric(e){return this._reader.getComputedNumericAtIndex(0)}setComputedNumeric(e,t){return this._reader.setComputedNumericAtIndex(t,0)}getComputedString(e){return this._reader.getComputedStringAtIndex(0)}setComputedString(e,t){return this._reader.setComputedStringAtIndex(0,t)}getComputedNumericAtIndex(e){return this._reader.getComputedNumericAtIndex(e)}setComputedNumericAtIndex(e,t){this._reader.setComputedNumericAtIndex(e,t)}getComputedStringAtIndex(e){return this._reader.getComputedStringAtIndex(e)}setComputedStringAtIndex(e,t){return this._reader.setComputedStringAtIndex(e,t)}transform(e,t,s,r){const i=this.copy();return i._reader=this._reader.transform(e,t,s,r),i}readAttribute(e,t=!1){return this._reader.readAttribute(e,t)}readAttributes(){return this._reader.readAttributes()}joinAttributes(e){return this._reader.joinAttributes(e)}readArcadeFeature(){return this._reader.readArcadeFeature()}geometry(){return this._reader.geometry()}field(e){return this.readAttribute(e,!0)}hasField(e){return this._reader.hasField(e)}setField(e,t){return this._reader.setField(e,t)}keys(){return this._reader.keys()}castToText(e=!1){return this._reader.castToText(e)}getQuantizationTransform(){return this._reader.getQuantizationTransform()}getFieldNames(){return this._reader.getFieldNames()}getAttributeHash(){return this._reader.getAttributeHash()}getObjectId(){return this._reader.getObjectId()}getDisplayId(){return this._reader.getDisplayId()}setDisplayId(e){return this._reader.setDisplayId(e)}getGroupId(){return this._reader.getGroupId()}setGroupId(e){return this._reader.setGroupId(e)}getXHydrated(){return this._reader.getXHydrated()}getYHydrated(){return this._reader.getYHydrated()}getX(){return this._reader.getX()}getY(){return this._reader.getY()}setIndex(e){return this._reader.setIndex(e)}getIndex(){return this._reader.getIndex()}readLegacyFeature(){return this._reader.readLegacyFeature()}readOptimizedFeature(){return this._reader.readOptimizedFeature()}readLegacyPointGeometry(){return this._reader.readLegacyPointGeometry()}readLegacyGeometry(){return this._reader.readLegacyGeometry()}readLegacyCentroid(){return this._reader.readLegacyCentroid()}readGeometryArea(){return this._reader.readGeometryArea()}readUnquantizedGeometry(){return this._reader.readUnquantizedGeometry()}readHydratedGeometry(){return this._reader.readHydratedGeometry()}readGeometry(){return this._reader.readGeometry()}readCentroid(){return this._reader.readCentroid()}_readAttribute(e,t){throw new Error("Error: Should not be called. Underlying _reader should be used instead")}_readAttributes(){throw new Error("Error: Should not be called. Underlying _reader should be used instead")}}},93054:(e,t,s)=>{s.d(t,{p:()=>r});class r{static fromBuffer(e,t){return new r(e,t)}static create(e,t=4294967295){const s=new Uint32Array(Math.ceil(e/32));return new r(s,t)}constructor(e,t){this._mask=0,this._buf=e,this._mask=t}_getIndex(e){return Math.floor(e/32)}has(e){const t=this._mask&e;return!!(this._buf[this._getIndex(t)]&1<<t%32)}hasRange(e,t){let s=e,r=t;for(;s%32&&s!==r;){if(this.has(s))return!0;s++}for(;r%32&&s!==r;){if(this.has(s))return!0;r--}if(s===r)return!1;for(let e=s/32;e!==r/32;e++)if(this._buf[e])return!0;return!1}set(e){const t=this._mask&e,s=this._getIndex(t),r=1<<t%32;this._buf[s]|=r}setRange(e,t){let s=e,r=t;for(;s%32&&s!==r;)this.set(s++);for(;r%32&&s!==r;)this.set(r--);if(s!==r)for(let e=s/32;e!==r/32;e++)this._buf[e]=4294967295}unset(e){const t=this._mask&e,s=this._getIndex(t),r=1<<t%32;this._buf[s]&=4294967295^r}resize(e){const t=this._buf,s=new Uint32Array(Math.ceil(e/32));s.set(t),this._buf=s}or(e){for(let t=0;t<this._buf.length;t++)this._buf[t]|=e._buf[t];return this}and(e){for(let t=0;t<this._buf.length;t++)this._buf[t]&=e._buf[t];return this}xor(e){for(let t=0;t<this._buf.length;t++)this._buf[t]^=e._buf[t];return this}ior(e){for(let t=0;t<this._buf.length;t++)this._buf[t]|=~e._buf[t];return this}iand(e){for(let t=0;t<this._buf.length;t++)this._buf[t]&=~e._buf[t];return this}ixor(e){for(let t=0;t<this._buf.length;t++)this._buf[t]^=~e._buf[t];return this}any(){for(let e=0;e<this._buf.length;e++)if(this._buf[e])return!0;return!1}copy(e){for(let t=0;t<this._buf.length;t++)this._buf[t]=e._buf[t];return this}clone(){return new r(this._buf.slice(),this._mask)}clear(){for(let e=0;e<this._buf.length;e++)this._buf[e]=0}forEachSet(e){for(let t=0;t<this._buf.length;t++){let s=this._buf[t],r=32*t;if(s)for(;s;)1&s&&e(r),s>>>=1,r++}}countSet(){let e=0;return this.forEachSet((t=>{e++})),e}}},67524:(e,t,s)=>{s.d(t,{Z:()=>i});var r=s(71143);class i{constructor(){this.spans=[]}acquire(e){this.lodInfo=e}release(){this.lodInfo=null,this.spans.length=0}forEach(e,t){const{spans:s,lodInfo:r}=this,{level:i}=r;if(0!==s.length)for(const{row:n,colFrom:o,colTo:a}of s)for(let s=o;s<=a;s++)e.call(t,i,n,r.normalizeCol(s),r.getWorldForColumn(s))}}i.pool=new r.Z(i)},83068:(e,t,s)=>{s.d(t,{Z:()=>f});var r=s(8744),i=s(70586),n=s(55415);function o(e,t){return[e,t]}function a(e,t,s){return e[0]=t,e[1]=s,e}const h=new n.Z("0/0/0/0");class l{static create(e,t,s=null){const n=(0,r.C5)(e.spatialReference),h=t.origin||o(e.origin.x,e.origin.y),u=o(e.size[0]*t.resolution,e.size[1]*t.resolution),c=o(-1/0,-1/0),d=o(1/0,1/0),g=o(1/0,1/0);(0,i.pC)(s)&&(a(c,Math.max(0,Math.floor((s.xmin-h[0])/u[0])),Math.max(0,Math.floor((h[1]-s.ymax)/u[1]))),a(d,Math.max(0,Math.floor((s.xmax-h[0])/u[0])),Math.max(0,Math.floor((h[1]-s.ymin)/u[1]))),a(g,d[0]-c[0]+1,d[1]-c[1]+1));const{cols:p,rows:f}=t;let _,m,y,I;return!s&&p&&f&&(a(c,p[0],f[0]),a(d,p[1],f[1]),a(g,p[1]-p[0]+1,f[1]-f[0]+1)),e.isWrappable?(_=o(Math.ceil(Math.round((n.valid[1]-n.valid[0])/t.resolution)/e.size[0]),g[1]),m=o(Math.floor((n.origin[0]-h[0])/u[0]),c[1]),y=o(_[0]+m[0]-1,d[1]),I=!0):(m=c,y=d,_=g,I=!1),new l(t.level,t.resolution,t.scale,h,c,d,g,u,m,y,_,I)}constructor(e,t,s,r,i,n,o,a,h,l,u,c){this.level=e,this.resolution=t,this.scale=s,this.origin=r,this.first=i,this.last=n,this.size=o,this.norm=a,this.worldStart=h,this.worldEnd=l,this.worldSize=u,this.wrap=c}normalizeCol(e){if(!this.wrap)return e;const t=this.worldSize[0];return e<0?t-1-Math.abs((e+1)%t):e%t}denormalizeCol(e,t){return this.wrap?this.worldSize[0]*t+e:e}getWorldForColumn(e){return this.wrap?Math.floor(e/this.worldSize[0]):0}getFirstColumnForWorld(e){return e*this.worldSize[0]+this.first[0]}getLastColumnForWorld(e){return e*this.worldSize[0]+this.first[0]+this.size[0]-1}getColumnForX(e){return(e-this.origin[0])/this.norm[0]}getXForColumn(e){return this.origin[0]+e*this.norm[0]}getRowForY(e){return(this.origin[1]-e)/this.norm[1]}getYForRow(e){return this.origin[1]-e*this.norm[1]}getTileBounds(e,t,s=!1){h.set(t);const r=s?h.col:this.denormalizeCol(h.col,h.world),i=h.row;return function(e,t,s,r,i){e[0]=t,e[1]=s,e[2]=r,e[3]=i}(e,this.getXForColumn(r),this.getYForRow(i+1),this.getXForColumn(r+1),this.getYForRow(i)),e}getTileCoords(e,t,s=!1){h.set(t);const r=s?h.col:this.denormalizeCol(h.col,h.world);return Array.isArray(e)?a(e,this.getXForColumn(r),this.getYForRow(h.row)):(e.x=this.getXForColumn(r),e.y=this.getYForRow(h.row)),e}}var u=s(67524);class c{constructor(e,t,s){this.row=e,this.colFrom=t,this.colTo=s}}const d=new n.Z("0/0/0/0");class g{static create(e,t){e[1]>t[1]&&([e,t]=[t,e]);const[s,r]=e,[i,n]=t,o=i-s,a=n-r,h=0!==a?o/a:0,l=(Math.ceil(r)-r)*h,u=(Math.floor(r)-r)*h;return new g(s,Math.floor(r),Math.ceil(n),h,o<0?l:u,o<0?u:l,o<0?i:s,o<0?s:i)}constructor(e,t,s,r,i,n,o,a){this.x=e,this.ymin=t,this.ymax=s,this.invM=r,this.leftAdjust=i,this.rightAdjust=n,this.leftBound=o,this.rightBound=a}incrRow(){this.x+=this.invM}getLeftCol(){return Math.max(this.x+this.leftAdjust,this.leftBound)}getRightCol(){return Math.min(this.x+this.rightAdjust,this.rightBound)}}const p=[[0,0],[0,0],[0,0],[0,0]];class f{constructor(e,t=null){this.tileInfo=e,this.fullExtent=t,this.scales=[],this._infoByScale={},this._infoByLevel={};const s=e.lods.slice();s.sort(((e,t)=>t.scale-e.scale));const r=this._lodInfos=s.map((s=>l.create(e,s,t)));s.forEach(((e,t)=>{this._infoByLevel[e.level]=r[t],this._infoByScale[e.scale]=r[t],this.scales[t]=e.scale}),this),this._wrap=e.isWrappable}get spatialReference(){return this.tileInfo.spatialReference}getLODInfoAt(e){return this._infoByLevel["number"==typeof e?e:e.level]}getTileBounds(e,t,s=!1){d.set(t);const r=this._infoByLevel[d.level];return r?r.getTileBounds(e,d,s):e}getTileCoords(e,t,s=!1){d.set(t);const r=this._infoByLevel[d.level];return r?r.getTileCoords(e,d,s):e}getTileCoverage(e,t=192,s="closest"){const r="closest"===s?this.getClosestInfoForScale(e.scale):this.getSmallestInfoForScale(e.scale),i=u.Z.pool.acquire(r),n=this._wrap;let o,a,h,l=1/0,d=-1/0;const f=i.spans;p[0][0]=p[0][1]=p[1][1]=p[3][0]=-t,p[1][0]=p[2][0]=e.size[0]+t,p[2][1]=p[3][1]=e.size[1]+t;for(const t of p)e.toMap(t,t),t[0]=r.getColumnForX(t[0]),t[1]=r.getRowForY(t[1]);const _=[];let m=3;for(let e=0;e<4;e++){if(p[e][1]===p[m][1]){m=e;continue}const t=g.create(p[e],p[m]);l=Math.min(t.ymin,l),d=Math.max(t.ymax,d),void 0===_[t.ymin]&&(_[t.ymin]=[]),_[t.ymin].push(t),m=e}if(null==l||null==d||d-l>100)return null;let y=[];for(o=l;o<d;){null!=_[o]&&(y=y.concat(_[o])),a=1/0,h=-1/0;for(let e=y.length-1;e>=0;e--){const t=y[e];a=Math.min(a,t.getLeftCol()),h=Math.max(h,t.getRightCol())}if(a=Math.floor(a),h=Math.floor(h),o>=r.first[1]&&o<=r.last[1])if(n)if(r.size[0]<r.worldSize[0]){const e=Math.floor(h/r.worldSize[0]);for(let t=Math.floor(a/r.worldSize[0]);t<=e;t++)f.push(new c(o,Math.max(r.getFirstColumnForWorld(t),a),Math.min(r.getLastColumnForWorld(t),h)))}else f.push(new c(o,a,h));else a>r.last[0]||h<r.first[0]||(a=Math.max(a,r.first[0]),h=Math.min(h,r.last[0]),f.push(new c(o,a,h)));o+=1;for(let e=y.length-1;e>=0;e--){const t=y[e];t.ymax>=o?t.incrRow():y.splice(e,1)}}return i}getTileParentId(e){d.set(e);const t=this._infoByLevel[d.level],s=this._lodInfos.indexOf(t)-1;return s<0?null:(this._getTileIdAtLOD(d,this._lodInfos[s],d),d.id)}getTileResolution(e){const t=this._infoByLevel["object"==typeof e?e.level:e];return t?t.resolution:-1}getTileScale(e){const t=this._infoByLevel[e.level];return t?t.scale:-1}intersects(e,t){d.set(t);const s=this._infoByLevel[d.level],r=e.lodInfo;if(r.resolution>s.resolution){this._getTileIdAtLOD(d,r,d);const t=r.denormalizeCol(d.col,d.world);for(const s of e.spans)if(s.row===d.row&&s.colFrom<=t&&s.colTo>=t)return!0}if(r.resolution<s.resolution){const[t,i,n,o]=e.spans.reduce(((e,t)=>(e[0]=Math.min(e[0],t.row),e[1]=Math.max(e[1],t.row),e[2]=Math.min(e[2],t.colFrom),e[3]=Math.max(e[3],t.colTo),e)),[1/0,-1/0,1/0,-1/0]),a=s.denormalizeCol(d.col,d.world),h=r.getColumnForX(s.getXForColumn(a)),l=r.getRowForY(s.getYForRow(d.row)),u=r.getColumnForX(s.getXForColumn(a+1))-1,c=r.getRowForY(s.getYForRow(d.row+1))-1;return!(h>o||u<n||l>i||c<t)}const i=r.denormalizeCol(d.col,d.world);return e.spans.some((e=>e.row===d.row&&e.colFrom<=i&&e.colTo>=i))}normalizeBounds(e,t,s){if(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],this._wrap){const t=(0,r.C5)(this.tileInfo.spatialReference),i=-s*(t.valid[1]-t.valid[0]);e[0]+=i,e[2]+=i}return e}getSmallestInfoForScale(e){const t=this.scales;if(this._infoByScale[e])return this._infoByScale[e];if(e>t[0])return this._infoByScale[t[0]];for(let s=1;s<t.length-1;s++)if(e>t[s]+1e-6)return this._infoByScale[t[s-1]];return this._infoByScale[t[t.length-1]]}getClosestInfoForScale(e){const t=this.scales;return this._infoByScale[e]||(e=t.reduce(((t,s)=>Math.abs(s-e)<Math.abs(t-e)?s:t),t[0])),this._infoByScale[e]}scaleToLevel(e){const t=this.scales;if(this._infoByScale[e])return this._infoByScale[e].level;for(let s=t.length-1;s>=0;s--)if(e<t[s])return s===t.length-1?this._infoByScale[t[t.length-1]].level:this._infoByScale[t[s]].level+(t[s]-e)/(t[s]-t[s+1]);return this._infoByScale[t[0]].level}scaleToZoom(e){return this.tileInfo.scaleToZoom(e)}_getTileIdAtLOD(e,t,s){const r=this._infoByLevel[s.level];return e.set(s),t.resolution<r.resolution?null:(t.resolution===r.resolution||(e.level=t.level,e.col=Math.floor(s.col*r.resolution/t.resolution+.01),e.row=Math.floor(s.row*r.resolution/t.resolution+.01)),e)}}},55415:(e,t,s)=>{s.d(t,{Z:()=>i});var r=s(71143);class i{static getId(e,t,s,r){return"object"==typeof e?`${e.level}/${e.row}/${e.col}/${e.world}`:`${e}/${t}/${s}/${r}`}constructor(e,t,s,r){this.set(e,t,s,r)}get key(){return this}get id(){return this.toString()}set id(e){this.set(e)}get hash(){const e=4095&this.row,t=4095&this.col,s=63&this.level;return(3&this.world)<<30|t<<22|e<<8|s}acquire(e,t,s,r){this.set(e,t,s,r)}contains(e){const t=e.level-this.level;return t>=0&&this.row===e.row>>t&&this.col===e.col>>t&&this.world===e.world}equals(e){return this.level===e.level&&this.row===e.row&&this.col===e.col&&this.world===e.world}clone(){return new i(this)}release(){this.level=0,this.row=0,this.col=0,this.world=0}set(e,t,s,r){if(null==e)this.level=0,this.row=0,this.col=0,this.world=0;else if("object"==typeof e)this.level=e.level||0,this.row=e.row||0,this.col=e.col||0,this.world=e.world||0;else if("string"==typeof e){const[t,s,r,i]=e.split("/");this.level=parseFloat(t),this.row=parseFloat(s),this.col=parseFloat(r),this.world=parseFloat(i)}else this.level=+e,this.row=+t,this.col=+s,this.world=+r||0;return this}toString(){return`${this.level}/${this.row}/${this.col}/${this.world}`}getParentKey(){return this.level<=0?null:new i(this.level-1,this.row>>1,this.col>>1,this.world)}getChildKeys(){const e=this.level+1,t=this.row<<1,s=this.col<<1,r=this.world;return[new i(e,t,s,r),new i(e,t,s+1,r),new i(e,t+1,s,r),new i(e,t+1,s+1,r)]}compareRowMajor(e){return this.row<e.row?-1:this.row>e.row?1:this.col<e.col?-1:this.col>e.col?1:0}}i.pool=new r.Z(i,null,null,25,50)},49733:(e,t,s)=>{s.d(t,{e:()=>h});var r=s(70586),i=s(95330),n=s(64830),o=s(1654);class a{constructor(e,t){this.item=e,this.controller=t,this.promise=null}}class h{constructor(e){this._deferreds=new Map,this._controllers=new Map,this._processingItems=new Map,this._isPaused=!1,this._schedule=null,this._task=null,this.concurrency=1,e.concurrency&&(this.concurrency=e.concurrency),this._queue=new n.Z(e.peeker),this.process=e.process;const t=e.scheduler;e.priority&&(0,r.pC)(t)&&(this._task=t.registerTask(e.priority,this))}destroy(){this.clear(),this._schedule&&(this._schedule.remove(),this._schedule=null),this._task&&(this._task.remove(),this._task=null)}get length(){return this._processingItems.size+this._queue.length}abort(e){const t=this._controllers.get(e);t&&t.abort()}clear(){this._queue.clear();const e=[];this._controllers.forEach((t=>e.push(t))),this._controllers.clear(),e.forEach((e=>e.abort())),this._processingItems.clear(),this._cancelNext()}forEach(e){this._deferreds.forEach(((t,s)=>e(s)))}get(e){const t=this._deferreds.get(e);return t?t.promise:void 0}isOngoing(e){return this._processingItems.has(e)}has(e){return this._deferreds.has(e)}pause(){this._isPaused||(this._isPaused=!0,this._cancelNext())}push(e,t){const s=this.get(e);if(s)return s;const n=new AbortController;let o=null;t&&(o=(0,i.fu)(t,(()=>n.abort())));const a=()=>{h.remove(),(0,r.pC)(o)&&o.remove(),this._deferreds.delete(e),this._controllers.delete(e),this._queue.remove(e),this._processingItems.delete(e),this._scheduleNext()},h=(0,i.$F)(n.signal,(()=>{const t=this._processingItems.get(e);t&&t.controller.abort(),a(),l.reject((0,i.zE)())})),l=(0,i.dD)();return this._deferreds.set(e,l),this._controllers.set(e,n),l.promise.then(a,a),this._queue.push(e),this._scheduleNext(),l.promise}last(){return this._queue.last()}peek(){return this._queue.peek()}popLast(){return this._queue.popLast()}reset(){const e=[];this._processingItems.forEach((t=>e.push(t))),this._processingItems.clear();for(const t of e)this._queue.push(t.item),t.controller.abort();this._scheduleNext()}resume(){this._isPaused&&(this._isPaused=!1,this._scheduleNext())}takeAll(){const e=[];for(;this._queue.length;)e.push(this._queue.pop());return this.clear(),e}get running(){return!this._isPaused&&this._queue.length>0&&this._processingItems.size<this.concurrency}runTask(e){for(;!e.done&&this._queue.length>0&&this._processingItems.size<this.concurrency;)this._process(this._queue.pop()),e.madeProgress()}_scheduleNext(){this._task||this._isPaused||this._schedule||(this._schedule=(0,o.Os)((()=>{this._schedule=null,this._next()})))}_next(){for(;this._queue.length>0&&this._processingItems.size<this.concurrency;)this._process(this._queue.pop())}_cancelNext(){this._schedule&&(this._schedule.remove(),this._schedule=null)}_processResult(e,t){this._canProcessFulfillment(e)&&(this._scheduleNext(),this._deferreds.get(e.item).resolve(t))}_processError(e,t){this._canProcessFulfillment(e)&&(this._scheduleNext(),this._deferreds.get(e.item).reject(t))}_canProcessFulfillment(e){return!!this._deferreds.get(e.item)&&this._processingItems.get(e.item)===e}_process(e){if((0,r.Wi)(e))return;let t;const s=new AbortController,n=new a(e,s);this._processingItems.set(e,n);try{t=this.process(e,s.signal)}catch(e){this._processError(n,e)}(0,i.y8)(t)?(n.promise=t,t.then((e=>this._processResult(n,e)),(e=>this._processError(n,e)))):this._processResult(n,t)}get test(){return{update:e=>this.runTask(e)}}}}}]);