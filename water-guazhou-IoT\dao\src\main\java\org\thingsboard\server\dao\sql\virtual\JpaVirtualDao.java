/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.virtual;

import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.virtual.Virtual;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.model.sql.VirtualEntity;
import org.thingsboard.server.dao.sql.JpaAbstractSearchTextDao;
import org.thingsboard.server.dao.util.SqlDao;
import org.thingsboard.server.dao.virtual.VirtualDao;

import java.util.List;

import static org.thingsboard.server.common.data.UUIDConverter.fromTimeUUID;

@Component
@SqlDao
public class JpaVirtualDao extends JpaAbstractSearchTextDao<VirtualEntity, Virtual> implements VirtualDao {


    @Autowired
    private VirtualRepository virtualRepository;

    @Override
    protected Class getEntityClass() {
        return VirtualEntity.class;
    }

    @Override
    protected CrudRepository getCrudRepository() {
        return virtualRepository;
    }


    @Override
    public ListenableFuture<List<Virtual>> findVirtualByTenant(TenantId tenantId) {
        return service.submit(() -> DaoUtil.convertDataList(virtualRepository.findByTenantId(fromTimeUUID(tenantId.getId()))));
    }

    @Override
    public ListenableFuture<Virtual> findByVirtualId(String virtualId) {
        return service.submit(() -> virtualRepository.findById(virtualId).toData());
    }

    @Override
    public ListenableFuture<List<Virtual>> findAll() {
        return service.submit(() -> find());
    }

    @Override
    public ListenableFuture<Virtual> virtualSave(TenantId tenantId,Virtual virtual) {
        return service.submit(() -> save(tenantId,virtual));
    }

    @Override
    public ListenableFuture<List<Virtual>> findBySerialNumber(String serialNumber) {
        return service.submit(() -> DaoUtil.convertDataList(virtualRepository.findBySerialNumber(serialNumber)));
    }

    @Override
    public ListenableFuture<List<Virtual>> findVirtualByTenantAndType(TenantId tenantId, String type) {
        return service.submit(() -> DaoUtil.convertDataList(virtualRepository.findByTenantIdAndType(fromTimeUUID(tenantId.getId()),type)));
    }

    @Override
    public List<Virtual> findVirtualByProjectId(String projectId) {
        return DaoUtil.convertDataList(virtualRepository.findVirtualByProjectId(projectId));
    }

}
