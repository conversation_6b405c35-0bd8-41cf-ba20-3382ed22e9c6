package org.thingsboard.server.dao.sql.smartService.wechat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.wechat.BusinessOutlet;
import org.thingsboard.server.dao.util.imodel.query.smartService.wechat.BusinessOutletPageRequest;

@Mapper
public interface BusinessOutletMapper extends BaseMapper<BusinessOutlet> {
    IPage<BusinessOutlet> findByPage(BusinessOutletPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(BusinessOutlet entity);

    boolean updateFully(BusinessOutlet entity);

}
