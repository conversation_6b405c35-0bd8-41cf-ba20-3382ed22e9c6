package org.thingsboard.server.dao.fault;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.fault.FaultPlanM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface FaultPlanMService {
    PageData getList(String planName, String teamName, String userName, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId);

    FaultPlanM getDetail(String mainId);

    FaultPlanM save(FaultPlanM maintainPlanM);

    IstarResponse delete(List<String> ids);

    void reviewer(FaultPlanM maintainPlanM);

    List<FaultPlanM> findAll();

    void execute(FaultPlanM maintainPlanM) throws ParseException;
}
