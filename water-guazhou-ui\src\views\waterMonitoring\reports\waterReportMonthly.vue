<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <SLCard
      class="card"
      title=" "
    >
      <template #title>
        <div class="card-title">
          <span class="title">{{ state.currentStation.label }}水质月报表</span>
          <span class="date">报表时间：{{ state.time }}</span>
        </div>
      </template>
      <div class="card-content">
        <div class="table-box__max">
          <FormTable :config="TableConfig_Max"></FormTable>
        </div>
        <div class="table-box__min">
          <FormTable :config="TableConfig_Min"></FormTable>
        </div>
        <div class="table-box__report">
          <FormTable :config="TableConfig_Report"></FormTable>
        </div>
      </div>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import moment from 'moment'
import { useBusinessStore } from '@/store'
import SLCard from '@/components/SLCard/index.vue'
import { initWaterDailyTableColumn } from '../data'
import FormTable from '@/components/Form/FormTable.vue'
import { getMaxAndMinReport, getWaterQualityReport, exportWaterQualityReport } from '@/api/waterQualityManage/waterQualityMonitoring'
import { GetStationList, GetStationAttrGroupNames } from '@/api/shuiwureports/zhandian'

const state = reactive<{
  stationList: any,
  groupTypeList: any,
  currentStation: any,
  time: any
}>({
  stationList: [],
  groupTypeList: [],
  currentStation: {},
  time: moment().format('YYYY-MM-DD')
})

const refSearch = ref<ICardSearchIns>()

const SearchConfig = reactive<ISearch>({
  defaultParams: {
    time: moment().format('YYYY-MM-DD')
  },
  filters: [
    {
      type: 'select',
      label: '监测站:',
      field: 'stationId',
      options: computed(() => state.stationList) as any,
      onChange: val => {
        getGroupType(val)
      }
    },
    {
      type: 'select',
      label: '监测点:',
      field: 'groupType',
      options: computed(() => state.groupTypeList) as any,
      onChange: () => {
        refreshData()
      }
    },
    {
      type: 'month',
      label: '月份',
      field: 'time',
      onChange: val => {
        state.time = val
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: 'iconfont icon-chaxun',
          click: () => refreshData()
        },
        { perm: true,
          iconifyIcon: 'ep:download',
          type: 'warning',
          text: '导出',
          click: () => handleExport() }
      ]
    }
  ]
})
const TableConfig_Max = reactive<ITable>({
  height: 'none',
  highlightCurrentRow: false,
  tableTitle: '最大值及发现时间',
  columns: initWaterDailyTableColumn('max'),
  dataList: [],
  pagination: {
    hide: true
  }
})

const TableConfig_Min = reactive<ITable>({
  tableTitle: '最小值及发现时间',
  dataList: [],
  height: 'none',
  columns: initWaterDailyTableColumn('min'),
  pagination: { hide: true }
})
const TableConfig_Report = reactive<ITable>({
  tableTitle: '水质月报表',
  height: 'none',
  dataList: [],
  columns: initWaterDailyTableColumn('report'),
  pagination: { hide: true }
})

const refreshData = async () => {
  const queryParams = refSearch.value?.queryParams as any
  const params = {
    stationType: '水质监测站',
    queryType: 'month',
    ...queryParams
  }
  const res = await getMaxAndMinReport(params)
  TableConfig_Max.dataList = convert(res.data.data.max, '最大值')
  TableConfig_Min.dataList = convert(res.data.data.min, '最小值')

  const data = await getWaterQualityReport(params)
  TableConfig_Report.columns = convertColum(data.data.data.tableInfo)
  TableConfig_Report.dataList = data.data.data.tableDataList
}
const handleExport = () => {
  const queryParams = refSearch.value?.queryParams as any
  const params = {
    stationType: '水质监测站',
    queryType: 'month',
    ...queryParams
  }
  exportWaterQualityReport(params).then(res => {
    const url = window.URL.createObjectURL(res.data)
    console.log(url)
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', `${state.currentStation.label}水质月报表.xlsx`)
    document.body.appendChild(link)
    link.click()
  })
}

function convert(params: any[], type: '最大值' | '最小值') {
  const data = { type }
  const time = { type: '发生时间' }
  params.forEach(item => {
    data[item.name] = item.value
    time[item.name] = item.time
  })
  return [data, time]
}

function convertColum(params: any[]) {
  return params.map(item => {
    item['label'] = item.columnName
    item['prop'] = item.columnValue
    return item
  }) as any
}

const getGroupType = async (stationId: string) => {
  const res = await GetStationAttrGroupNames({ stationId })
  state.groupTypeList = res?.data.map(d => {
    return {
      label: d,
      value: d
    }
  })
  state.currentStation = state.stationList.find(station => station.value === stationId)
  SearchConfig.defaultParams = {
    ...SearchConfig.defaultParams,
    stationId: state.currentStation.value,
    groupType: state.groupTypeList[0]?.value
  }
  refSearch.value?.resetForm()
  refreshData()
}

onMounted(async () => {
  const res = await GetStationList({
    page: 1,
    size: 999,
    type: '水质监测站',
    projectId: useBusinessStore().selectedProject?.value
  })
  const data = res.data.data
  state.stationList = data.map(d => {
    return {
      label: d.name,
      value: d.id
    }
  })
  state.currentStation = state.stationList[0]
  await getGroupType(state.stationList[0]?.value)
})
</script>
<style lang="scss" scoped>
.card {
  height: calc(100% - 100px);

  .card-title {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .date {
      font-size: 12px;
    }
  }
}

.card-content {
  height: 100%;
  overflow-y: auto;
  overflow-y: overlay;
}

.table-box__report {
  padding-bottom: 8px;
}

.table-box__min,
.table-box__max {
  margin-bottom: 20px;
}
</style>
