package org.thingsboard.server.dao.util.imodel.query.smartManagement.district;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.district.CircuitDistrictPoint;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class CircuitDistrictPointSaveRequest extends SaveRequest<CircuitDistrictPoint> {
    // 关键点名称
    @NotNullOrEmpty
    private String name;

    // 所属点阵Id
    @NotNullOrEmpty
    private String areaId;

    // 纬度
    @NotNullOrEmpty
    private String lat;

    // 经度
    @NotNullOrEmpty
    private String lon;

    // 备注
    private String remark;

    @Override
    protected boolean allowUpdate() {
        return false;
    }

    @Override
    protected CircuitDistrictPoint build() {
        CircuitDistrictPoint entity = new CircuitDistrictPoint();
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected CircuitDistrictPoint update(String id) {
        CircuitDistrictPoint entity = new CircuitDistrictPoint();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(CircuitDistrictPoint entity) {
        entity.setName(name);
        entity.setAreaId(areaId);
        entity.setLat(lat);
        entity.setLon(lon);
        entity.setRemark(remark);
    }
}