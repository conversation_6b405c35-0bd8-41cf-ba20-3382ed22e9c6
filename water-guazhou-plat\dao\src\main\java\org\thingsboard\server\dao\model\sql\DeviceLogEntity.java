package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2020/4/26 10:51
 */
@Data
@Entity
@Table(name = ModelConstants.DEVICE_LOG_TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class DeviceLogEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    /**
     * 用户名
     */
    @Column(name = ModelConstants.DEVICE_LOG_DEVICE_NAME)
    private String deviceName;

    /**
     * 手机号
     */
    @Column(name = ModelConstants.DEVICE_LOG_PROJECT_NAME)
    private String projectName;
    /**
     * 发送状态
     */
    @Column(name = ModelConstants.CUSTOMER_ROLE_STATUS)
    private String status;
    /**
     * 创建时间
     */
    @Column(name = ModelConstants.DATASOURCE_UPDATE_TIME)
    private long updateTime;
    /**
     * 备注
     */
    @Column(name = ModelConstants.DATASOURCE_FORMAT)
    private String format;

    /**
     * tenantId
     */
    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    /**
     * projectId
     */
    @Column(name = ModelConstants.PROJECT_RELATION_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.DEVICE_ID_PROPERTY)
    private String deviceId;

}
