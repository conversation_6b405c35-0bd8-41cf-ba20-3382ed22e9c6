const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/geometryEngineJSON-DX0oHAsv.js","static/js/geometryEngineBase-BhsKaODW.js","static/js/json-Wa8cmqdu.js"])))=>i.map(i=>d[i]);
import{T as g,a3 as U,R as q}from"./index-r0dFAfgr.js";import{h as A,E,X as G,p as J,k as b,q as _}from"./Point-WxyopZva.js";import{ap as z,aq as B,ar as k,I as v,J as Z,K as I,as as K,at as P,ao as S,au as y,av as j,aw as C,ax as T,ay as p,az as V}from"./MapView-DaoQedLH.js";import{f as F,g as M}from"./projectionSupport-BDUl30tr.js";const D=new A({esriSRUnit_Meter:"meters",esriSRUnit_Kilometer:"kilometers",esriSRUnit_Foot:"feet",esriSRUnit_StatuteMile:"miles",esriSRUnit_NauticalMile:"nautical-miles",esriSRUnit_USNauticalMile:"us-nautical-miles"}),x=Object.freeze({}),$=new S,L=new S,R=new S,l={esriGeometryPoint:y,esriGeometryPolyline:j,esriGeometryPolygon:C,esriGeometryMultipoint:T};function at(t,e,r,a=t.hasZ,n=t.hasM){if(g(e))return null;const o=t.hasZ&&a,s=t.hasM&&n;if(r){const i=p(R,e,t.hasZ,t.hasM,"esriGeometryPoint",r,a,n);return y(i,o,s)}return y(e,o,s)}function rt(t,e,r,a,n,o,s=e,i=r){var w,h,d;const c=e&&s,u=r&&i,f=q(a)?"coords"in a?a:a.geometry:null;if(g(f))return null;if(n){let m=V(L,f,e,r,t,n,s,i);return o&&(m=p(R,m,c,u,t,o)),((w=l[t])==null?void 0:w.call(l,m,c,u))??null}if(o){const m=p(R,f,e,r,t,o,s,i);return((h=l[t])==null?void 0:h.call(l,m,c,u))??null}return P($,f,e,r,s,i),((d=l[t])==null?void 0:d.call(l,$,c,u))??null}async function st(t,e,r){const{outFields:a,orderByFields:n,groupByFieldsForStatistics:o,outStatistics:s}=t;if(a)for(let i=0;i<a.length;i++)a[i]=a[i].trim();if(n)for(let i=0;i<n.length;i++)n[i]=n[i].trim();if(o)for(let i=0;i<o.length;i++)o[i]=o[i].trim();if(s)for(let i=0;i<s.length;i++)s[i].onStatisticField&&(s[i].onStatisticField=s[i].onStatisticField.trim());return t.geometry&&!t.outSR&&(t.outSR=t.geometry.spatialReference),X(t,e,r)}async function X(t,e,r){var o;if(!t)return null;let{where:a}=t;if(t.where=a=a&&a.trim(),(!a||/^1 *= *1$/.test(a)||e&&e===a)&&(t.where=null),!t.geometry)return t;let n=await Q(t);if(t.distance=0,t.units=null,t.spatialRel==="esriSpatialRelEnvelopeIntersects"){const{spatialReference:s}=t.geometry;n=z(n),n.spatialReference=s}if(n){await F(n.spatialReference,r),n=H(n,r);const s=(await B(k(n)))[0];if(g(s))throw x;const i="quantizationParameters"in t&&((o=t.quantizationParameters)==null?void 0:o.tolerance)||"maxAllowableOffset"in t&&t.maxAllowableOffset||0,c=i&&O(n,r)?{densificationStep:8*i}:void 0,u=s.toJSON(),f=await M(u,u.spatialReference,r,c);if(!f)throw x;f.spatialReference=r,t.geometry=f}return t}function O(t,e){if(!t)return!1;const r=t.spatialReference;return(v(t)||Z(t)||I(t))&&!E(r,e)&&!K(r,e)}function H(t,e){const r=t.spatialReference;return O(t,e)&&v(t)?{spatialReference:r,rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]}:t}async function Q(t){const{distance:e,units:r}=t,a=t.geometry;if(e==null||"vertexAttributes"in a)return a;const n=a.spatialReference,o=r?D.fromJSON(r):G(n),s=n&&(J(n)||b(n))?a:await F(n,_).then(()=>M(a,_));return(await W())(s.spatialReference,s,e,o)}async function W(){return(await U(async()=>{const{geodesicBuffer:t}=await import("./geometryEngineJSON-DX0oHAsv.js").then(e=>e.g);return{geodesicBuffer:t}},__vite__mapDeps([0,1,2]))).geodesicBuffer}function ot(t){return t&&N in t?JSON.parse(JSON.stringify(t,Y)):t}const N="_geVersion",Y=(t,e)=>t!==N?e:void 0;export{ot as E,x as F,X as J,at as b,rt as v,st as z};
