import{z as M,d as de,c as A,r as q,b as y,s as x,bE as Y,S as G,dc as ce,u as ye,D as me,Q as ue,g as ge,h as fe,F as D,G as he,q as I,i as z,p as be,_ as ve,aq as Te,al as we,b7 as $,bq as Se,dS as Le,C as ke}from"./index-r0dFAfgr.js";import{_ as Ce}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{g as _e,v as xe,b as Fe,n as He}from"./MapView-DaoQedLH.js";import{w as We}from"./Point-WxyopZva.js";import{p as E}from"./AnimatedLinesLayer-B2VbV4jv.js";import{h as qe,o as Re,s as w,m as K,n as Oe,g as Ae}from"./FeatureHelper-Da16o0mu.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as Q}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as j,i as De,R as F}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import Ie from"./RightDrawerMap-D5PhmGFO.js";import{S as ze,f as Z,h as Me}from"./config-DncLSA-r.js";import{f as ee}from"./DateFormatter-Bm9a68Ax.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./index-DeAQQ1ej.js";const te=S=>M({url:"/api/gis/label/save",method:"post",data:S}),Ne=S=>M({url:"/api/gis/label/list",method:"get",params:S}),Ue=S=>M({url:"/api/gis/label/remove",method:"delete",data:S}),Be={class:"table-box"},Ve=de({__name:"TagManage",setup(S){const H=A(),L=A(),f=A(),l=q({tabs:[],loading:!1,curType:"point"}),i={},ie=q({group:[{fieldset:{desc:"绘制范围"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",isTextBtn:!0,title:"点要素",iconifyIcon:"mdi:circle-slice-8",click:()=>R("point")},{perm:!0,text:"",type:"default",size:"large",title:"线要素",isTextBtn:!0,iconifyIcon:"mdi:chart-timeline-variant",click:()=>R("polyline")},{perm:!0,text:"",type:"default",size:"large",isTextBtn:!0,title:"面要素",iconifyIcon:"mdi:shape-polygon-plus",click:()=>R("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"清除要素",isTextBtn:!0,iconifyIcon:"ep:delete",click:()=>B()}]}]},{id:"symbol",fieldset:{desc:"渲染样式配置"},fields:[{handleHidden:(e,o,t)=>{t.hidden=l.curType!=="point"},type:"radio",field:"type",options:[{label:"符号",value:"simple"},{label:"图片",value:"picture"}]},{handleHidden:(e,o,t)=>{t.hidden=e.type!=="simple"||l.curType!=="point"},type:"select",field:"pointStyle",label:"样式",clearable:!1,options:ze},{handleHidden:(e,o,t)=>{t.hidden=e.type!=="simple"||l.curType!=="point"},type:"input-number",field:"size",label:"符号大小"},{handleHidden:(e,o,t)=>{t.hidden=e.type!=="picture"||l.curType!=="point"},type:"avatar",label:"图标",field:"img"},{handleHidden:(e,o,t)=>{t.hidden=e.type!=="picture"||l.curType!=="point"},type:"input-number",field:"width",label:"宽"},{handleHidden:(e,o,t)=>{t.hidden=e.type!=="picture"||l.curType!=="point"},type:"input-number",field:"height",label:"高"},{handleHidden:(e,o,t)=>{t.hidden=e.type!=="picture"||l.curType!=="point"},type:"input-number",field:"offsetX",label:"偏移-x"},{handleHidden:(e,o,t)=>{t.hidden=e.type!=="picture"||l.curType!=="point"},type:"input-number",field:"offsetY",label:"偏移-y"},{handleHidden:(e,o,t)=>{t.hidden=l.curType!=="polyline"},type:"select",field:"polylineStyle",label:"样式",clearable:!1,options:Z},{handleHidden:(e,o,t)=>{t.hidden=l.curType!=="polyline"},type:"input-number",field:"polylineWidth",label:"线宽"},{handleHidden:(e,o,t)=>{t.hidden=l.curType!=="polygon"},type:"select",field:"polygonFillStyle",label:"填充样式",clearable:!1,options:Me},{handleHidden:(e,o,t)=>{t.hidden=l.curType!=="polygon"},type:"select",field:"polygonLineStyle",label:"边界样式",clearable:!1,options:Z},{handleHidden:(e,o,t)=>{t.hidden=l.curType!=="polygon"},type:"input-number",field:"polygonLineWidth",label:"边界宽度"},{handleHidden:(e,o,t)=>{t.hidden=l.curType!=="polygon"},type:"color-picker",field:"polygonFillColor",label:"填充颜色"},{handleHidden:(e,o,t)=>{t.hidden=l.curType!=="polygon"},type:"color-picker",field:"polygonLineColor",label:"边界颜色"},{handleHidden:(e,o,t)=>{t.hidden=e.type!=="simple"||l.curType!=="point"},type:"color-picker",field:"pointColor",label:"颜色"},{handleHidden:(e,o,t)=>{t.hidden=e.type!=="simple"||l.curType!=="point"},type:"color-picker",field:"pointOutlineColor",label:"边线颜色"},{handleHidden:(e,o,t)=>{t.hidden=e.type!=="simple"||l.curType!=="point"},type:"input-number",field:"pointOutlineWidth",label:"边线宽"},{handleHidden:(e,o,t)=>{t.hidden=l.curType!=="polyline"},type:"color-picker",field:"polylineColor",label:"线条颜色"}]},{id:"params",fieldset:{desc:"文本属性"},fields:[{type:"input",field:"tag",label:"标签名称",rules:[{required:!0,message:"请输入标签名称"}]},{type:"textarea",field:"remark",label:"描述"},{type:"radio",field:"auth",label:"权限",options:[{label:"仅自己可见",value:"selfonly"},{label:"所有人可见",value:"all"}]},{type:"btn-group",btns:[{perm:!0,text:"添加",styles:{width:"50%"},click:()=>oe()},{perm:!0,text:"重置",type:"default",styles:{width:"50%"},click:()=>U()}]}]}],labelPosition:"top",gutter:12,defaultValue:{type:"simple",auth:"selfonly",size:12,width:100,height:100,offsetX:0,offsetY:0,pointStyle:"circle",pointOutlineWidth:1,pointOutlineColor:"rgba(0,255,255,1)",polylineStyle:"solid",polylineWidth:2,polygonLineStyle:"solid",polygonFillStyle:"solid",polygonLineWidth:2,pointColor:"rgba(0,255,255,1)",polylineColor:"rgba(0,255,255,1)",polygonFillColor:"rgba(0,255,255,0.2)",polygonLineColor:"rgba(0,255,255,1)"},submit:async e=>{var o;if(!i.graphics){y.warning("请先绘制标签图形");return}l.loading=!0;try{const t=i.graphics.geometry,r=e.geometryType==="point"&&e.type==="picture"?{img:e.img,imgWidth:e.width,imgHeight:e.height,imgOffsetX:e.offsetX,imgOffsetY:e.offsetY}:{},n=(t==null?void 0:t.type)==="point"?{...t.toJSON(),...r}:t==null?void 0:t.toJSON(),a=await te({name:e.tag,description:e.remark,available:e.auth==="selfonly"?"0":"1",geomtype:e.geometryType,geom:JSON.stringify(n),style:e.geometryType==="point"?e.pointStyle:e.geometryType==="polyline"?e.polylineStyle:e.geometryType==="polygon"?e.polygonFillStyle+","+e.polygonLineStyle:"",pointcolor:e.geometryType==="point"&&e.pointColor||"",pointsize:e.geometryType==="point"&&e.size||0,linecolor:e.geometryType==="point"?e.pointOutlineColor:e.geometryType==="polyline"?e.polylineColor:e.geometryType==="polygon"?e.polygonLineColor:"",linewidth:e.geometryType==="point"?e.pointOutlineWidth:e.geometryType==="polyline"?e.polylineWidth:e.geometryType==="polygon"?e.polygonLineWidth:"0",fillcolor:e.geometryType==="polygon"&&e.polygonFillColor||""});a.data.code===200?(y.success("添加成功"),h(),U()):y.error(a.data.message||"添加失败")}catch{y.error("添加失败")}l.loading=!1,(o=H.value)==null||o.toggleCustomDetail(!0)}}),R=e=>{var o,t,r,n,a,d,c,g,s,v,T;i.view&&(l.curType=e,L.value&&(L.value.dataForm.geometryType=e),j("crosshair"),(o=i.drawer)==null||o.destroy(),i.drawer=De(i.view),(t=i.drawAction)==null||t.destroy(),i.drawAction=(r=i.drawer)==null?void 0:r.create(e),(n=i.graphicsTempLayer)==null||n.removeAll(),(a=i.graphicsLayer)==null||a.removeAll(),(d=i.sketchUpdateHnalder)==null||d.remove(),(c=i.sketch)==null||c.destroy(),(g=i.temSketchUpdateHnalder)==null||g.remove(),(s=i.tempSketch)==null||s.destroy(),e!=="point"&&((v=i.drawAction)==null||v.on(["vertex-add","cursor-update"],N)),(T=i.drawAction)==null||T.on("draw-complete",async W=>{var k;N(W),i.tempSketch=new E({view:i.view,layer:i.graphicsTempLayer}),i.temSketchUpdateHnalder=(k=i.tempSketch)==null?void 0:k.on("update",C=>{C.state==="complete"&&(i.graphics=C.graphics[0])}),j("")}))},N=e=>{var n,a,d,c,g,s,v,T;const o=l.curType,t=((n=L.value)==null?void 0:n.dataForm)||{};if(!o)return;let r;switch(o){case"polyline":r=K(e.vertices,(a=i.view)==null?void 0:a.spatialReference,w(o,{style:t.polylineStyle,color:t.polylineColor,width:t.polylineWidth}));break;case"polygon":r=e.vertices.length<3?K(e.vertices,(d=i.view)==null?void 0:d.spatialReference,w("polyline",{color:t.polygonLineColor,style:t.polygonLineStyle,width:t.polygonLineWidth})):Oe(e.vertices,(c=i.view)==null?void 0:c.spatialReference,w(o,{style:t.polygonFillStyle,color:t.polygonFillColor,outlineColor:t.polygonLineColor,outlineWidth:t.polygonLineWidth,outlineStyle:t.polygonLineStyle}));break;case"point":r=t.type==="picture"?qe(e.vertices[0][0],e.vertices[0][1],{spatialReference:(g=i.view)==null?void 0:g.spatialReference,picUrl:t.img,picSize:[t.width||100,t.height||100],xOffset:t.offsetX,yOffset:t.offsetY}):Re(e.vertices,(s=i.view)==null?void 0:s.spatialReference,w("point",{size:t.size,style:t.pointStyle,color:t.pointColor,outlineColor:t.pointOutlineColor,outlineWidth:t.pointOutlineWidth}));break}(v=i.graphicsTempLayer)==null||v.removeAll(),r&&((T=i.graphicsTempLayer)==null||T.add(r)),i.graphics=r},oe=async()=>{var e;if(!i.graphics){y.warning("请先绘制图形");return}(e=L.value)==null||e.Submit()},U=()=>{var e;(e=L.value)==null||e.resetForm(),B()},B=()=>{var e,o;(e=i.graphicsTempLayer)==null||e.removeAll(),(o=i.graphics)==null||o.destroy(),i.graphics=void 0},le=q({group:[{fields:[{type:"radio-button",field:"createuser",options:[{label:"全部",value:"all"},{label:"我的",value:"selfonly"}],onChange:()=>h()},{type:"input",label:"名称",field:"name",labelWidth:"auto",onChange:()=>h()},{type:"daterange",field:"createtime",label:"创建时间",labelWidth:"auto",onChange:()=>h()},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:x(we),click:()=>h()},{perm:!0,text:"重置",type:"default",svgIcon:x($),click:()=>pe()},{perm:e=>e.createuser==="selfonly",text:"删除",type:"danger",disabled:()=>{var e;return!((e=u.selectList)!=null&&e.length)},svgIcon:x(Se),click:()=>ae()}]}]}],defaultValue:{createuser:"all"}}),u=q({handleSelectChange:e=>{u.selectList=e},rowKey:"id",selectable:()=>{var e;return((e=f.value)==null?void 0:e.dataForm.createuser)==="selfonly"},columns:[{width:60,label:"编号",prop:"id",align:"center"},{minWidth:120,label:"标签名称",prop:"name",formItemConfig:{type:"input",placeholder:" ",disabled:()=>{var e;return((e=f.value)==null?void 0:e.dataForm.createuser)!=="selfonly"}}},{width:120,label:"权限",align:"center",prop:"available",formItemConfig:{type:"switch",readonly:()=>{var e;return((e=f.value)==null?void 0:e.dataForm.createuser)!=="selfonly"},width:70,activeValue:"1",activeText:"所有人",inActiveValue:"0",inActiveText:"仅自己",inActiveColor:"#00ffff"}},{minWidth:120,label:"添加人",prop:"createuserName"},{minWidth:160,label:"创建时间",prop:"createtime",formatter(e,o){return ee(o,Y)}},{minWidth:160,label:"最后更新时间",prop:"updatetime",formatter(e,o){return ee(o,Y)}},{minWidth:120,label:"描述",prop:"description",formItemConfig:{type:"input",placeholder:" ",disabled:()=>{var e;return((e=f.value)==null?void 0:e.dataForm.createuser)!=="selfonly"}}}],dataList:[],pagination:{refreshData:({page:e,size:o})=>{u.pagination.page=e,u.pagination.limit=o,h()}},operations:[{perm:!0,text:"更新",disabled:()=>{var e;return((e=f.value)==null?void 0:e.dataForm.createuser)!=="selfonly"},svgIcon:x($),click:e=>re(e)},{perm:!0,text:"定位",svgIcon:x(Le),type:"success",click:e=>ne(e)}]}),re=e=>{const o={...e};G("确定提交？","提示信息").then(()=>{te(o).then(t=>{t.data.code===200?(y.success("更新成功"),h()):y.warning("更新失败")}).catch(()=>{y.warning("更新失败")})}).catch(()=>{})},ne=async e=>{var o,t,r,n,a,d,c,g,s,v,T,W,k,C,V,J,P;if(!e.geom){y.error("当前没有位置信息，无法定位");return}try{(o=H.value)==null||o.toggleCustomDetailMaxmin("normal"),(t=i.sketchUpdateHnalder)==null||t.remove(),(r=i.sketch)==null||r.destroy(),(n=i.temSketchUpdateHnalder)==null||n.remove(),(a=i.tempSketch)==null||a.destroy(),((d=f.value)==null?void 0:d.dataForm.createuser)==="selfonly"&&(i.sketch=new E({view:i.view,layer:i.graphicsLayer}),(c=i.sketch)==null||c.on("update",X=>{if(X.state==="complete"){const O=X.graphics[0],m=O==null?void 0:O.geometry,_=e.geom&&JSON.parse(e.geom)||{};(m==null?void 0:m.type)==="point"?(_.x=m.x,_.y=m.y):(m==null?void 0:m.type)==="polyline"?_.paths=m.paths:(m==null?void 0:m.type)==="polygon"&&(_.rings=m.rings),e.geom=JSON.stringify(_)}}));const p=JSON.parse(e.geom),b=new _e({attributes:{id:e.id}});(g=p.rings)!=null&&g.length?(b.geometry=new xe({rings:p.rings,spatialReference:p.spatialReference||((s=i.view)==null?void 0:s.spatialReference)}),b.symbol=w("polygon",{color:F(e.fillcolor,[0,255,255,.2]),outlineWidth:e.linewidth||1,outlineColor:F(e.linecolor,[0,255,255,1]),style:((v=e.style)==null?void 0:v.split(",")[0])||"solid",outlineStyle:((T=e.style)==null?void 0:T.split(",")[1])||"solid"})):(W=p.paths)!=null&&W.length?(b.geometry=new Fe({paths:p.paths,spatialReference:p.spatialReference||((k=i.view)==null?void 0:k.spatialReference)}),b.symbol=w("polyline",{color:F(e.linecolor,[0,255,255,1]),width:e.linewidth||1,style:e.style||"solid"})):p.x&&(b.geometry=new We({x:p.x,y:p.y,spatialReference:p.spatialReference||((C=i.view)==null?void 0:C.spatialReference)}),p.img!==void 0?b.symbol=new He({url:p.img,height:p.imgHeight||100,width:p.imgWidth||100,xoffset:p.imgOffsetX||0,yoffset:p.imgOffsetY||0}):b.symbol=w("point",{color:F(e.pointcolor,[0,255,255,1]),width:e.pointsize||12,outlineColor:F(e.linecolor,[0,255,255,1]),outlineWidth:e.linewidth||1,style:e.style||"circle"})),(V=i.graphicsLayer)==null||V.removeAll(),(J=i.graphicsTempLayer)==null||J.removeAll(),(P=i.graphicsLayer)==null||P.add(b),await ce(500),Ae(i.view,b,{avoidHighlight:!0})}catch{y.error("位置信息错误，无法定位")}},ae=()=>{G("确定删除？","提示信息").then(()=>{var o;const e=(o=u.selectList)==null?void 0:o.map(t=>t.id);Ue(e).then(t=>{t.data.code===200?(y.success("删除成功"),h()):y.error("删除失败")}).catch(()=>{y.error("删除失败")})}).catch(()=>{})},h=async()=>{var t,r,n,a,d,c;u.loading=!0;const e=ye(),o=((t=f.value)==null?void 0:t.dataForm)||{};try{const s=(c=(await Ne({page:u.pagination.page||1,size:u.pagination.limit||20,createuser:o.createuser==="selfonly"?((n=(r=e.user)==null?void 0:r.id)==null?void 0:n.id)&&me(e.user.id.id):void 0,name:o.name,beginTime:((a=o.createtime)==null?void 0:a.length)===2?o.createtime[0]:void 0,endTime:((d=o.createtime)==null?void 0:d.length)===2?o.createtime[1]:void 0})).data)==null?void 0:c.data;u.dataList=(s==null?void 0:s.data)||[],u.pagination.total=(s==null?void 0:s.total)||0}catch(g){y.error(g.message)}u.loading=!1},pe=()=>{var e;(e=f.value)==null||e.resetForm()},se=async e=>{var o;i.view=e,(o=H.value)==null||o.toggleCustomDetail(!0),i.graphicsTempLayer=Q(i.view,{id:"display-tags-temp",title:"标签临时绘制"}),i.graphicsLayer=Q(i.view,{id:"display-tags",title:"标签"}),h()};return ue(()=>{var e,o,t,r,n,a,d,c;(e=i.drawAction)==null||e.destroy(),(o=i.drawer)==null||o.destroy(),(t=i.sketch)==null||t.destroy(),(r=i.tempSketch)==null||r.destroy(),(n=i.graphicsLayer)==null||n.destroy(),(a=i.graphicsTempLayer)==null||a.destroy(),(d=i.temSketchUpdateHnalder)==null||d.remove(),(c=i.sketchUpdateHnalder)==null||c.remove()}),(e,o)=>{const t=ve,r=Ce,n=Te;return ge(),fe(Ie,{ref_key:"refMap",ref:H,title:"标签展示","detail-max-min":!0,onMapLoaded:se},{"detail-header":D(()=>o[0]||(o[0]=[he(" 标签列表 ")])),"detail-default":D(()=>[I(r,{ref_key:"refFormDetail",ref:f,config:z(le)},null,8,["config"]),be("div",Be,[I(n,{config:z(u)},null,8,["config"])])]),default:D(()=>[I(t,{ref_key:"refForm",ref:L,config:z(ie)},null,8,["config"])]),_:1},512)}}}),Ei=ke(Ve,[["__scopeId","data-v-fb166bfd"]]);export{Ei as default};
