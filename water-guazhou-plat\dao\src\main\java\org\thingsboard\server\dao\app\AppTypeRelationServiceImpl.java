package org.thingsboard.server.dao.app;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.dao.menu.MenuTenantService;
import org.thingsboard.server.dao.model.sql.AppType;
import org.thingsboard.server.dao.model.sql.AppTypeRelation;
import org.thingsboard.server.dao.sql.app.AppTypeRelationRepository;
import org.thingsboard.server.dao.tenant.TenantService;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class AppTypeRelationServiceImpl implements AppTypeRelationService {

    @Autowired
    private AppTypeRelationRepository appTypeRelationRepository;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private MenuTenantService menuTenantService;

    @Override
    public AppTypeRelation findById(String id) {
        return appTypeRelationRepository.findOne(id);
    }

    @Override
    public boolean assignMenuToAppType(String appTypeId, List<String> menuPoolIdList) {
        List<AppTypeRelation> appTypeRelations = new ArrayList<>();
        // 删除已分配的菜单
        appTypeRelationRepository.deleteByAppTypeId(appTypeId);

        // 更新菜单
        menuPoolIdList.forEach(menuPoolId -> {
            AppTypeRelation appTypeRelation = new AppTypeRelation();
            appTypeRelation.setAppTypeId(appTypeId);
            appTypeRelation.setMenuPoolId(menuPoolId);
            appTypeRelation.setCreateTime(System.currentTimeMillis());

            appTypeRelations.add(appTypeRelation);
        });
        appTypeRelationRepository.save(appTypeRelations);

        // 更新关联该应用的企业的菜单列表
        // TODO 会导致企业操作员菜单丢失，需要重新设置操作员角色的菜单
        List<Tenant> tenantList = tenantService.findByAppTypeId(appTypeId);
        for (Tenant tenant : tenantList) {
            List<AppTypeRelation> typeRelations = this.findByAppTypeId(appTypeId);
            List<MenuPoolId> menuPoolIds = typeRelations.stream()
                    .map(appTypeRelation -> new MenuPoolId(UUIDConverter.fromString(appTypeRelation.getMenuPoolId())))
                    .collect(Collectors.toList());
            try {
                menuTenantService.saveMenuTenant(menuPoolIds, tenant.getId());
            } catch (ThingsboardException e) {
                e.printStackTrace();
            }
        }


        return true;
    }

    @Override
    public List<AppTypeRelation> findByAppTypeId(String appTypeId) {
        return appTypeRelationRepository.findByAppTypeId(appTypeId);
    }

    /**
     * 查询指定应用分类拥有的子菜单ID列表
     *
     * @param appTypeId 应用分类ID
     * @return List
     */
    @Override
    public List<String> findMenuByAppTypeId(String appTypeId) {
        List<AppTypeRelation> menuByTypeIdAndMenuParentNotRoot = appTypeRelationRepository.findMenuByTypeIdAndMenuParentNotRoot(appTypeId);
        return menuByTypeIdAndMenuParentNotRoot.stream()
                .map(AppTypeRelation::getMenuPoolId)
                .collect(Collectors.toList());
    }
}
