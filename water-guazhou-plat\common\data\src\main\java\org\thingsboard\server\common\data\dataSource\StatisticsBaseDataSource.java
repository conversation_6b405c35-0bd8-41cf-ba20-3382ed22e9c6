package org.thingsboard.server.common.data.dataSource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/2/26 17:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsBaseDataSource extends BaseDataSource {

    private String formula;
    private String template;
    private String frequency;

    @Override
    public void setType(DataSourceType type) {
        super.setType(DataSourceType.STATISTICS_SOURCE);
    }


    public StatisticsBaseDataSource(BaseDataSource dataSource,String formula, String template, String frequency){
        this.setId(dataSource.getId());
        this.setType(DataSourceType.STATISTICS_SOURCE);
        this.setName(dataSource.getName());
        this.setEnable(dataSource.getEnable());
        this.setFormat(dataSource.getFormat());
        this.setUpdateTime(dataSource.getUpdateTime());
        this.setOrder(dataSource.getOrder());
        this.setFormula(formula);
        this.setTemplate(template);
        this.setFrequency(frequency);
    }
}
