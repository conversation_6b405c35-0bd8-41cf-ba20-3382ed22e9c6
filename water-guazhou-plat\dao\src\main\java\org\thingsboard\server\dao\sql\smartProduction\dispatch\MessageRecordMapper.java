package org.thingsboard.server.dao.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecord;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecordStatus;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageQueueMessageRecordSendRequestInfo;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageRecordSendRequest;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecordStatus.*;

@Mapper
public interface MessageRecordMapper extends BaseMapper<MessageRecord> {
    IPage<MessageRecord> findByPage(MessageRecordPageRequest request);

    boolean update(MessageRecord entity);

    int send(@Param("list") List<MessageRecordSendRequest.MessageRecordSendRequestInfo> list, @Param("autoDate") boolean autoDate, @Param("reliable") Boolean reliable);

    boolean markStatus(@Param("id") String id, @Param("status") String status);

    default List<MessageQueueMessageRecordSendRequestInfo> getReadyMessages() {
        return getReadyMessagesByArrangedStatus(PENDING, SENDING, SUCCESS);
    }

    List<MessageQueueMessageRecordSendRequestInfo> getReadyMessagesByArrangedStatus(@Param("pendingStatus") MessageRecordStatus pendingStatus,
                                                                                    @Param("sendingStatus") MessageRecordStatus sendingStatus,
                                                                                    @Param("successStatus") MessageRecordStatus successStatus);

    int markBatchStatus(@Param("list") List<String> list, @Param("status") String status);
}
