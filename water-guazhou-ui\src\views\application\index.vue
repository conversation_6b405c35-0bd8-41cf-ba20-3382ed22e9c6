<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      class="cardSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      class="card-table"
      :config="cardTableConfig"
    />
    <AddOrUpdateDialog
      v-if="addOrUpdateConfig.visible"
      ref="addOrUpdate"
      :config="addOrUpdateConfig"
      dialog-width="560px"
      @refresh-data="refreshData"
    />
    <MenuAssign
      v-if="menuConfig.visible"
      :config="menuConfig"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onBeforeMount, reactive } from 'vue'
import MenuAssign from './components/menuAssign.vue'
import { getAppList, deleteApp } from '@/api/application'
import useGlobal from '@/hooks/global/useGlobal'

const { $messageError, $messageSuccess, $confirm } = useGlobal()
const cardSearch = ref<any>(null)

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '搜索', field: 'keyWord', type: 'input' },
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', click: () => refreshData() },
        {
          perm: true,
          text: '添加',
          click: () => {
            addOrUpdateConfig.value.defaultValue = {}
            addOrUpdateConfig.value.visible = true
          }
        }
      ]
    }
  ],
  defaultParams: {}
})

const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'appName', label: '名称' },
    { prop: 'additionalInfo', label: '备注' }
  ],
  operations: [
    {
      text: '菜单赋予',
      isTextBtn: true,
      perm: true,
      icon: 'iconfont icon-guanli',
      click: row => {
        menuConfig.value.currentId = row.id
        menuConfig.value.visible = true
      }
    },
    {
      text: '编辑',
      isTextBtn: true,
      perm: true,
      icon: 'iconfont icon-baogao',
      click: row => {
        addOrUpdateConfig.value.defaultValue = row
        addOrUpdateConfig.value.visible = true
      }
    },
    {
      text: '删除',
      isTextBtn: true,
      perm: true,
      icon: 'iconfont icon-shanchu',
      click: row => handleDelete(row)
    }
  ],
  operationWidth: '260px',
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    layout: 'total, prev, pager, next, sizes, jumper',
    handleSize: val => {
      cardTableConfig.pagination.limit = val
      refreshData()
    },
    handlePage: val => {
      cardTableConfig.pagination.page = val
      refreshData()
    }
  }
})

const addOrUpdateConfig = ref<AOUConfig>({
  visible: false,
  title: '应用操作',
  close: () => (addOrUpdateConfig.value.visible = false),
  addUrl: 'api/app/type',
  editUrl: 'api/app/type/edit',
  defaultValue: {},
  externalParams: {},
  columns: [
    {
      type: 'input',
      label: '应用名称',
      key: 'appName',
      rules: [{ required: true, message: '请填写应用名称' }]
    },
    {
      type: 'textarea',
      label: '备注',
      key: 'additionalInfo',
      rows: 3
    }
  ]
})

const refreshData = async (isFirst?: boolean) => {
  cardTableConfig.loading = true
  const paramsObj: any = {
    page: cardTableConfig.pagination.page,
    size: cardTableConfig.pagination.limit
  }
  if (!isFirst && cardSearch.value) Object.assign(paramsObj, cardSearch.value.queryParams)
  try {
    const res = await getAppList()
    console.log(res, 'getAppList')
    cardTableConfig.loading = false
    if (res.status === 200) {
      cardTableConfig.dataList = res.data
      cardTableConfig.pagination.total = res.data.length
    } else {
      $messageError('获取失败')
    }
  } catch (error) {
    cardTableConfig.loading = false
  }
}

const handleDelete = (row?: any) => {
  $confirm('确定删除指定企业?', '删除提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteApp(row.id).then(() => {
      $messageSuccess('操作成功')
      refreshData()
    })
  })
}

const menuConfig = ref<DialogConfig>({
  currentId: '',
  visible: false,
  close: () => (menuConfig.value.visible = false)
})

onBeforeMount(() => {
  refreshData(true)
})
</script>
<style>
.tree-right-detail-box {
  height: 100%;
  padding: 15px;
}
</style>
