interface NavApp {
  projectType: string;
  appNames: string[];
}
interface Store_Business_State {
  /**
   * 查看的范围，在导航栏选择
   */
  navSelectedRange?: NormalOption;
  /**
   * 项目树选择后保存到这里，避免重复选择
   */
  selectedProject?: NormalOption;
  /** 项目和应用对照信息 */
  navAppsNames: NavApp[];
  /** 导航菜单数组 */
  curNavs: NavResult[];
  /** 后台获取到的导航栏 */
  navApps: NavResult[];
  /**
   * 格式化后区域划分
   *
   * 只有当开启项目类型过滤（在public/config(.dev）.js中设置的useprojectapp选项)才有值，否则值为[]
   */
  projectList: NormalOption[];
  /** 标记是否需要重新生成路由,设置为true后全在路由跳转前进行路由重新获取、过滤、左侧菜单重新生成，默认值为true，即项目初始化时会自动加载菜单，此项在business/usePortal设置为true时，默认效果与false相同 */
  shouldRefreshSideBar: boolean;
  shouldToDefaultFirstRout: boolean;
  /** 基于项目类型过滤应用 */
  useprojectapp: boolean;
  /** 开启着陆页  默认不开 */
  usePortal: boolean;
}

interface Store_Tags_state {
  /** 缓存的tags路由 */
  cachedRouters: any[];
  cachedRouterNames: string[];
  /** 显示tags标签，默认true */
  showTags: boolean;
  /**
   * 正在刷新标签指定的页面
   */
  reloading: boolean;
}
interface Store_App_State {
  /** 使用横向菜单 默认开启 */
  horizontalMenu: boolean;
  /** 导航栏样式 默认top，即下拉面板，left：左侧抽屉 */
  menuType: string;
  /** 菜单是否显示， 默认false */
  menuShow: boolean;
  /** 进阶菜单是否显示，默认false */
  subMenuShow: boolean;
  /** 进阶菜单高亮路由 */
  // subMenuHighlightPath: string
  /** 进阶菜单的父级路由 */
  subMenuParentRoute: any;
  /** 消息通知 */
  MessageNotification: boolean;
  /** 语音播报 */
  VoiceBroadcast: boolean;
  isDark: boolean;
  sidebar: any;
  appUrl: string;
  device: string;
  /** 图片/文件上传的接口ip地址 */
  actionUrl: string;
  scadaUrl: string;
  /**
   * @deprecated
   * 没有用了，主题请使用store.app.idDark
   */
  theme: string;
  sysSign?: any;
  userProject: any;
  /** 系统logo */
  logo: string;
  /** 当前应用的名称 */
  appname: string;
  /** 当前应用的id */
  appid: string;
  rtmQueryList?: any;
  htmQueryList?: any;
  /** 当前子应用是不是嵌套在iframe中的 */
  isFrameApp: boolean;
  /** 当前嵌套的子应用的url */
  frameAppUrl: string;
  /** 页面缓存最大数量 */
  keepAliveMax: number;
}
interface Store_Gis_State_UserInfo {
  departmentid: string;
  groupid: string;
  range: string;
  subsys: string;
  token: string;
  userid: string;
  username: string;
}
interface IGisLayerConfig {
  fields: {
    alias: string;
    group: string;
    index: number;
    name: string;
  };
  layerdbname: string;
  layername: string;
}
interface Store_Gis_State {
  // gisToken
  gToken?: string;
  gUserInfo?: Store_Gis_State_UserInfo;
  gLayerConfig?: IGisLayerConfig[];
  gLayerInfos?: ILayerInfo[];
  gLayerIds?: number[];
  gLayerOption_Line?: NormalOption[];
  gLayerOption_Point?: NormalOption[];
}
interface Store_Permission_State {
  routers: any[];
  addRouters: any[];
  btnPerms: any[];
  /** 使用按钮权限 */
  usebtnperms: boolean;
  tenantRouters: any[];
  untreatedTRouter: any;
  sysRouter: any[];
  bigScreenR: any[];
  sysBigScreenR: any[];
  defaultRoutePath: string;
}

interface IUserInfo {
  additionalInfo?: any;
  authority?: string;
  createdTime?: number;
  customerId?: {
    entityType?: string;
    id?: string;
  };
  email?: string;
  firstName?: string;
  id?: {
    entityType?: string;
    id?: string;
  };
  lastName?: string;
  loginName?: string;
  name?: string;
  phone?: string;
  projectIds?: string;
  serialNo?: string;
  status?: boolean;
  tenantId?: {
    entityType: string;
    id: string;
  };
  tenantList?: string;
}
interface Store_User_State {
  user?: IUserInfo;
  appname: string;
  status: any;
  token?: string;
  name: string;
  avatar: string;
  roles: string[];
  id: string;
  tenantId: string;
  sysTenantId: string;
  email: string;
  departmentId?: string;
  firstName: string;
  lastName: string;
  tenantList: any[];
  scadaToken?: string;
  playedAlarmList: any[];
  tenantInfo?: {
    id: {
      id: string;
      entityType: string;
    };
    additionalInfo?: {
      logoUrl?: string;
      firstScreen?: string;
    } & ISiteConfig;
    createdTime: number;
    country?: string;
    state?: string;
    city?: string;
    address?: string;
    address2?: string;
    zip?: string;
    phone?: string;
    email?: string;
    title?: string;
    region?: string;
    remark?: string;
    createTime?: string;
    managerUserId?: string;
    managerUserName?: string;
    appTypeId?: string;
    lgtd?: string;
    latd?: string;
    name?: string;
  };
}
interface RootStateType {
  business: Store_Business_State;
  app: Store_App_State;
  permission: Store_Permission_State;
  user: Store_User_State;
  gis: Store_Gis_State;
}
