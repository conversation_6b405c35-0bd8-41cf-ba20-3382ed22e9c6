import { getTenantUsersByPage } from '@/api/user'
import { formatTree } from '@/utils/GlobalHelper'

const useUser = () => {
  const getUserOptions = async (isAll?:boolean, params?:any) => {
    const res = await getTenantUsersByPage({
      page: 1,
      size: 999,
      ...(params || {})
    })
    const result = formatTree(res.data?.data || [], { id: 'id.id', value: 'id.id', label: 'firstName' })
    isAll && result.unshift({ label: '全部', value: '' })
    return result
  }
  return {
    getUserOptions
  }
}
export default useUser
