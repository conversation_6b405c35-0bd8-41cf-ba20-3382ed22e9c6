package org.thingsboard.server.dao.model.sql.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class DetailDeviceStorageInfo {
    // id
    private String id;

    // 设备编号
    private String serialId;

    // 设备台账
    private String deviceLabelCode;

    // 设备名称
    private String deviceName;

    // 设备单位
    private String deviceUnit;

    // 设备类型
    private String deviceType;

    // 设备类型
    private String deviceTypeId;

    // 设备型号
    private String deviceModel;

    // 设备图片
    private String deviceImages;

    // 设备文件
    private String deviceFiles;

    // 自定义设备属性
    private String autoField;

    // 供应商ID
    private String supplierId;

    // 供应商名称
    private String supplierName;

    // 采购金额
    private String price;

    // 入库日期
    private Date createTime;

    // 使用年限
    private Integer deviceUseYear;

    // 出库时间
    private Date outTime;

    // 报废时间
    private Date scrappedTime;

    // 报废时间
    private boolean scrapped;

    // 备注
    private String remark;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
