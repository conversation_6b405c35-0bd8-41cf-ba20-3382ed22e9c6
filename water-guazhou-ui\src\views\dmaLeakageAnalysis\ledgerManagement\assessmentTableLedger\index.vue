<template>
  <div class="assessment-table-ledger-container">
    <!-- 搜索与批量操作栏 -->
    <el-form :model="searchParams" :inline="true" class="filter-form">
      <el-form-item label="考核表名称">
        <el-input v-model="searchParams.name" placeholder="请输入考核表名称" clearable />
      </el-form-item>
      <el-form-item label="区域" style="min-width: 180px;">
        <el-select v-model="searchParams.region" placeholder="全部" clearable>
          <el-option label="全部" value="" />
          <el-option v-for="item in regionList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="所在分区" style="min-width: 180px;">
        <el-select v-model="searchParams.partition" placeholder="全部" clearable>
          <el-option label="全部" value="" />
          <el-option v-for="item in partitionList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="考核周期">
        <el-date-picker v-model="searchParams.period" type="month" placeholder="选择年月" clearable value-format="YYYY-MM" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="openAdd">新增</el-button>
        <el-button type="primary" @click="openImport">批量导入</el-button>
        <el-button type="warning" @click="handleExport">导出</el-button>
        <el-button type="danger" :disabled="!multipleSelection.length" @click="handleBatchDelete">批量删除</el-button>
        <el-button type="info" @click="downloadTemplate">下载模板</el-button>
      </el-form-item>
    </el-form>

    <!-- 考核表列表 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      :row-key="row => row.id"
      ref="tableRef"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="考核表名称" min-width="120" />
      <el-table-column prop="region" label="区域" min-width="100" />
      <el-table-column prop="partition" label="所在分区" min-width="100" />
      <el-table-column prop="period" label="考核周期" min-width="100" />
      <el-table-column prop="assessmentType" label="考核类型" min-width="100">
        <template #default="scope">
          {{ getAssessmentTypeName(scope.row.assessmentType) }}
        </template>
      </el-table-column>
      <el-table-column prop="assessmentLevel" label="考核等级" min-width="80" />
      <el-table-column prop="status" label="状态" min-width="80">
        <template #default="scope">
          {{ getStatusName(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column prop="creator" label="创建人" min-width="100" />
      <el-table-column prop="createTime" label="创建时间" min-width="140" />
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button type="primary" link @click="openEdit(scope.row)">编辑</el-button>
          <el-button type="info" link @click="openDetail(scope.row)">明细</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pagination.page"
      :page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 16px; text-align: right;"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 编辑/新增弹窗 -->
    <el-dialog v-model="editDialogVisible" :title="editForm.id ? '编辑考核表' : '新增考核表'" width="700px">
      <el-form :model="editForm" label-width="100px" :rules="rules" ref="editFormRef">
        <el-form-item label="考核表名称" prop="name">
          <el-input v-model="editForm.name" />
        </el-form-item>
        <el-form-item label="区域" prop="region">
          <el-select v-model="editForm.region" placeholder="请选择区域">
            <el-option v-for="item in regionList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="所在分区" prop="partition">
          <el-select v-model="editForm.partition" placeholder="请选择分区">
            <el-option v-for="item in partitionList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="考核周期" prop="period">
          <el-date-picker v-model="editForm.period" type="month" placeholder="选择年月" value-format="YYYY-MM" />
        </el-form-item>
        <el-form-item label="考核类型" prop="assessmentType">
          <el-select v-model="editForm.assessmentType" placeholder="请选择考核类型">
            <el-option v-for="(name, value) in assessmentTypeOptions" :key="value" :label="name" :value="value" />
          </el-select>
        </el-form-item>
        <el-form-item label="考核等级">
          <el-select v-model="editForm.assessmentLevel" placeholder="请选择考核等级">
            <el-option v-for="(name, value) in assessmentLevelOptions" :key="value" :label="name" :value="value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="editForm.status" placeholder="请选择状态">
            <el-option v-for="(name, value) in statusOptions" :key="value" :label="name" :value="value" />
          </el-select>
        </el-form-item>
        <el-form-item label="总分">
          <el-input-number v-model="editForm.totalScore" :min="0" :max="100" :precision="2" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editForm.remark" type="textarea" rows="2" />
        </el-form-item>
        <el-divider>考核指标明细</el-divider>
        <el-table :data="editForm.items" border size="small">
          <el-table-column prop="indicatorName" label="指标名称" min-width="120">
            <template #default="scope">
              <el-input v-model="scope.row.indicatorName" placeholder="如：漏损率" />
            </template>
          </el-table-column>
          <el-table-column prop="indicatorDesc" label="指标描述" min-width="120">
            <template #default="scope">
              <el-input v-model="scope.row.indicatorDesc" placeholder="请输入指标描述" />
            </template>
          </el-table-column>
          <el-table-column prop="indicatorType" label="指标类型" min-width="100">
            <template #default="scope">
              <el-select v-model="scope.row.indicatorType" placeholder="请选择">
                <el-option label="定量指标" value="1" />
                <el-option label="定性指标" value="2" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="indicatorUnit" label="单位" min-width="80">
            <template #default="scope">
              <el-input v-model="scope.row.indicatorUnit" placeholder="%、个、次" />
            </template>
          </el-table-column>
          <el-table-column prop="weight" label="权重" min-width="80">
            <template #default="scope">
              <el-input-number v-model="scope.row.weight" :min="0" :max="100" :precision="2" />
            </template>
          </el-table-column>
          <el-table-column prop="targetValue" label="目标值" min-width="80">
            <template #default="scope">
              <el-input-number v-model="scope.row.targetValue" :precision="2" />
            </template>
          </el-table-column>
          <el-table-column prop="actualValue" label="实际值" min-width="80">
            <template #default="scope">
              <el-input-number v-model="scope.row.actualValue" :precision="2" />
            </template>
          </el-table-column>
          <el-table-column prop="score" label="得分" min-width="80">
            <template #default="scope">
              <el-input-number v-model="scope.row.score" :min="0" :precision="2" />
            </template>
          </el-table-column>
          <el-table-column prop="sortNum" label="排序" min-width="60">
            <template #default="scope">
              <el-input-number v-model="scope.row.sortNum" :min="0" :precision="0" />
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="120">
            <template #default="scope">
              <el-input v-model="scope.row.remark" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="scope">
              <el-button type="danger" link @click="removeItem(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button type="primary" plain @click="addItem" style="margin-top:8px;">新增指标</el-button>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditSave">保存</el-button>
      </template>
    </el-dialog>

    <!-- 明细弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="考核表明细" width="700px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="考核表名称">{{ detailForm.name }}</el-descriptions-item>
        <el-descriptions-item label="区域">{{ detailForm.region }}</el-descriptions-item>
        <el-descriptions-item label="所在分区">{{ detailForm.partition }}</el-descriptions-item>
        <el-descriptions-item label="考核周期">{{ detailForm.period }}</el-descriptions-item>
        <el-descriptions-item label="考核类型">{{ getAssessmentTypeName(detailForm.assessmentType) }}</el-descriptions-item>
        <el-descriptions-item label="考核等级">{{ detailForm.assessmentLevel }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ getStatusName(detailForm.status) }}</el-descriptions-item>
        <el-descriptions-item label="总分">{{ detailForm.totalScore }}</el-descriptions-item>
        <el-descriptions-item label="审核人">{{ detailForm.reviewer }}</el-descriptions-item>
        <el-descriptions-item label="审核时间">{{ detailForm.reviewTime }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailForm.creator }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailForm.createTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
      <el-divider>考核指标明细</el-divider>
      <el-table :data="detailForm.items" border size="small">
        <el-table-column prop="indicatorName" label="指标名称" min-width="120" />
        <el-table-column prop="indicatorDesc" label="指标描述" min-width="120" />
        <el-table-column prop="indicatorType" label="指标类型" min-width="80">
          <template #default="scope">
            {{ scope.row.indicatorType === '1' ? '定量指标' : '定性指标' }}
          </template>
        </el-table-column>
        <el-table-column prop="indicatorUnit" label="单位" min-width="60" />
        <el-table-column prop="weight" label="权重" min-width="60" />
        <el-table-column prop="targetValue" label="目标值" min-width="80" />
        <el-table-column prop="actualValue" label="实际值" min-width="80" />
        <el-table-column prop="score" label="得分" min-width="60" />
        <el-table-column prop="scorer" label="评分人" min-width="80" />
        <el-table-column prop="scoreTime" label="评分时间" min-width="140" />
        <el-table-column prop="remark" label="备注" min-width="120" />
      </el-table>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 导入弹窗 -->
    <el-dialog v-model="importDialogVisible" title="批量导入" width="400px">
      <el-upload
        drag
        action="#"
        :auto-upload="false"
        :show-file-list="true"
        :on-change="handleImportChange"
        :http-request="handleImportUpload"
        :limit="1"
        :file-list="importFileList"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">仅支持Excel文件，可先下载模板填写</div>
        </template>
      </el-upload>
      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleImport" :loading="importLoading">导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAssessmentTables, getAssessmentTableById, saveAssessmentTable, deleteAssessmentTable, 
         batchDeleteAssessmentTables, importAssessmentTables, exportAssessmentTables, downloadAssessmentTemplate } from '@/api/ledgerManagement/assessmentTable'

// 区域和分区数据
const regionList = ['东区', '西区', '南区', '北区']
const partitionList = ['A分区', 'B分区', 'C分区', 'D分区']

// 考核类型选项
const assessmentTypeOptions = {
  '1': '水质考核',
  '2': '供水安全考核',
  '3': '管网漏损考核',
  '4': '能耗考核',
  '5': '设备运行考核',
  '6': '综合评估'
}

// 考核等级选项
const assessmentLevelOptions = {
  'A': '优秀',
  'B': '良好',
  'C': '合格',
  'D': '不合格'
}

// 状态选项
const statusOptions = {
  '0': '草稿',
  '1': '已提交',
  '2': '审核中',
  '3': '已通过',
  '4': '已驳回'
}

// 获取考核类型名称
const getAssessmentTypeName = (type) => {
  return assessmentTypeOptions[type] || type
}

// 获取状态名称
const getStatusName = (status) => {
  return statusOptions[status] || status
}

// 表单校验规则
const rules = {
  name: [{ required: true, message: '请输入考核表名称', trigger: 'blur' }],
  region: [{ required: true, message: '请选择区域', trigger: 'change' }],
  partition: [{ required: true, message: '请选择分区', trigger: 'change' }],
  period: [{ required: true, message: '请选择考核周期', trigger: 'change' }],
  assessmentType: [{ required: true, message: '请选择考核类型', trigger: 'change' }]
}

// 加载状态
const loading = ref(false)
const importLoading = ref(false)

// 搜索参数
const searchParams = ref({
  name: '',
  region: '',
  partition: '',
  period: ''
})

// 分页参数
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const tableData = ref([])
const multipleSelection = ref([])
const tableRef = ref()

// 编辑表单
const editDialogVisible = ref(false)
const editFormRef = ref()
const editForm = ref({
  id: '',
  name: '',
  region: '',
  partition: '',
  period: '',
  assessmentType: '',
  assessmentLevel: '',
  status: '',
  totalScore: 0,
  reviewer: '',
  reviewTime: null,
  remark: '',
  creator: '',
  createTime: null,
  items: []
})

// 详情表单
const detailDialogVisible = ref(false)
const detailForm = ref({
  id: '',
  name: '',
  region: '',
  partition: '',
  period: '',
  assessmentType: '',
  assessmentLevel: '',
  status: '',
  totalScore: 0,
  reviewer: '',
  reviewTime: null,
  creator: '',
  createTime: null,
  remark: '',
  items: []
})

// 导入相关
const importDialogVisible = ref(false)
const importFileList = ref([])

// 选择变更处理
function handleSelectionChange(val) {
  multipleSelection.value = val
}

// 分页处理
function handleSizeChange(size) {
  pagination.value.pageSize = size
  pagination.value.page = 1
  fetchTableData()
}

function handleCurrentChange(page) {
  pagination.value.page = page
  fetchTableData()
}

// 搜索处理
function handleSearch() {
  pagination.value.page = 1
  fetchTableData()
}

function resetSearch() {
  searchParams.value.name = ''
  searchParams.value.region = ''
  searchParams.value.partition = ''
  searchParams.value.period = ''
  pagination.value.page = 1
  fetchTableData()
}

// 获取表格数据
async function fetchTableData() {
  loading.value = true
  try {
    const { page, pageSize } = pagination.value
    const { name, region, partition, period } = searchParams.value
    
    const res = await getAssessmentTables({
      page,
      pageSize,
      name,
      region,
      partition,
      period
    })
    
    if (res && res.data) {
      tableData.value = res.data.data || []
      pagination.value.total = res.data.totalElements || 0
    }
  } catch (error) {
    ElMessage.error('获取考核表数据失败')
    console.error('获取考核表数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 编辑/新增
function openAdd() {
  editForm.value = { 
    id: '',
    name: '', 
    region: '', 
    partition: '', 
    period: '',
    assessmentType: '',
    assessmentLevel: '',
    status: '0', // 默认草稿状态
    totalScore: 0,
    reviewer: '',
    reviewTime: null,
    remark: '', 
    creator: '',
    createTime: null,
    items: []
  }
  editDialogVisible.value = true
}

async function openEdit(row) {
  try {
    const res = await getAssessmentTableById(row.id)
    if (res && res.data) {
      editForm.value = res.data
      if (!editForm.value.items) editForm.value.items = []
    }
    editDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取考核表详情失败')
    console.error('获取考核表详情失败:', error)
  }
}

function addItem() {
  if (!editForm.value.items) editForm.value.items = []
  editForm.value.items.push({ 
    id: null,
    assessmentTableId: editForm.value.id || null,
    indicatorName: '',
    indicatorDesc: '',
    indicatorType: '1',
    indicatorUnit: '',
    weight: 0,
    targetValue: 0,
    actualValue: 0,
    score: 0,
    scorer: '',
    scoreTime: null,
    remark: '',
    createTime: null,
    sortNum: editForm.value.items.length + 1
  })
}

function removeItem(idx) {
  editForm.value.items.splice(idx, 1)
}

async function handleEditSave() {
  if (!editFormRef.value) return
  
  await editFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning('请填写完整信息')
      return
    }
    
    try {
      await saveAssessmentTable(editForm.value)
      ElMessage.success(editForm.value.id ? '编辑成功' : '新增成功')
      editDialogVisible.value = false
      fetchTableData()
    } catch (error) {
      ElMessage.error(editForm.value.id ? '编辑失败' : '新增失败')
      console.error('保存考核表失败:', error)
    }
  })
}

// 明细弹窗
async function openDetail(row) {
  try {
    const res = await getAssessmentTableById(row.id)
    if (res && res.data) {
      detailForm.value = res.data
      if (!detailForm.value.items) detailForm.value.items = []
    }
    detailDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取考核表详情失败')
    console.error('获取考核表详情失败:', error)
  }
}

// 删除
async function handleDelete(row) {
  try {
    await ElMessageBox.confirm('确定要删除该考核表吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteAssessmentTable(row.id)
    ElMessage.success('删除成功')
    fetchTableData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除考核表失败:', error)
    }
  }
}

async function handleBatchDelete() {
  if (!multipleSelection.value.length) return
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const ids = multipleSelection.value.map(item => item.id)
    await batchDeleteAssessmentTables(ids)
    ElMessage.success('批量删除成功')
    fetchTableData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error('批量删除考核表失败:', error)
    }
  }
}

// 导入
function handleImportChange(file) {
  importFileList.value = [file]
}

function handleImportUpload({ file }) {
  // 这个函数不会被调用，因为我们设置了auto-upload为false
  // 我们会在handleImport中手动处理文件上传
  return Promise.resolve()
}

async function handleImport() {
  if (!importFileList.value.length) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  const file = importFileList.value[0].raw
  if (!file) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  // 检查文件类型
  const fileType = file.name.split('.').pop().toLowerCase()
  if (!['xlsx', 'xls'].includes(fileType)) {
    ElMessage.warning('只支持Excel文件格式')
    return
  }
  
  importLoading.value = true
  try {
    const formData = new FormData()
    formData.append('file', file)
    
    const res = await importAssessmentTables(formData)
    if (res && res.data) {
      ElMessage.success(`成功导入 ${res.data} 条记录`)
      importDialogVisible.value = false
      importFileList.value = []
      fetchTableData()
    }
  } catch (error) {
    ElMessage.error('导入失败')
    console.error('导入考核表失败:', error)
  } finally {
    importLoading.value = false
  }
}

function openImport() {
  importFileList.value = []
  importDialogVisible.value = true
}

// 导出
async function handleExport() {
  try {
    const { name, region, partition, period } = searchParams.value
    const res = await exportAssessmentTables({
      name,
      region,
      partition,
      period
    })
    
    // 处理blob响应，实现文件下载
    if (res && res.data) {
      const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `考核表导出_${new Date().getTime()}.xlsx`
      link.click()
      URL.revokeObjectURL(link.href)
      ElMessage.success('导出成功')
    }
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出考核表失败:', error)
  }
}

// 下载模板
async function downloadTemplate() {
  try {
    const res = await downloadAssessmentTemplate()
    
    // 处理blob响应，实现文件下载
    if (res && res.data) {
      const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = '考核表导入模板.xlsx'
      link.click()
      URL.revokeObjectURL(link.href)
      ElMessage.success('模板下载成功')
    }
  } catch (error) {
    ElMessage.error('模板下载失败')
    console.error('下载模板失败:', error)
  }
}

// 初始化
onMounted(() => {
  fetchTableData()
})
</script>

<style scoped>
.assessment-table-ledger-container {
  padding: 20px;
  background: #fff;
  border-radius: 6px;
}
.filter-form {
  margin-bottom: 16px;
}
</style> 