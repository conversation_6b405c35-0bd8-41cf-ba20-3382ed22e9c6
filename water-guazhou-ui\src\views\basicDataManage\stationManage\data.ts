import { getDeviceVarGroup } from '@/api/device';
import { formatTree } from '@/utils/GlobalHelper';
import { SLMessage } from '@/utils/Message';
import { removeSlash } from '@/utils/removeIdSlash';
// import { TabsPaneContext } from 'element-plus';

/** 当没有属性分组标签时显示的名称 */
export const AttrGroupName_none = '请添加分组';
export enum StationTypeEnums {
  SHUICHANGE = '水厂',
  CHELIUYAZHAN = '测流压站',
  BENGZHAN = '泵站',
  YALIJIANCEZHAN = '压力监测站',
  LIULIANGJIANCEZHAN = '流量监测站',
  SHUIZHIJIANCEZHAN = '水质监测站',
  SHUIYUANDI = '水源地',
  DAYONGHU = '大用户',
  WUSHUICHULICHANGE = '污水处理厂',
  RESHUIJIN = '热水井',
  DIBIAO = '地标',
  WENQIANJIUDIAN = '温泉酒店',
  LVGUAN = '旅馆',
  YIYUAN = '医院',
  PAICHUSUO = '派出所'
}
export const colors: Record<string, string> = {
  水厂: '#3c8da5',
  测流压站: '#9b4b62',
  泵站: '#8f5c3e',
  压力监测站: '#909c36',
  流量监测站: '#327c53',
  水质监测站: '#5f4894',
  水源地: '#43548f',
  大用户: '#489785',
  污水处理厂: '#9e561d',
  水池监测站: '#f03595',
  阀门: '#8f5c3e',
  热水井: '#f56c6c',
  地标: '#1ECE70',
  // 旅馆: '#CE1EBC',
  温泉酒店: '#CE1EBC',
  医院: '#FF0B0B',
  派出所: '#0B54FF',
  消防栓: '#F5A623'
};
export const initStationType = (type?: 'all', types?: string[]) => {
  const stationTypes: NormalOption[] = [];
  if (types?.length) {
    types.map((item) => {
      stationTypes.push({ label: item, value: item });
    });
  } else {
    for (const key in colors) {
      stationTypes.push({ label: key.toString(), value: key.toString() });
    }
  }
  if (type === 'all') {
    stationTypes.unshift({ label: '全部', value: '' });
  }
  return stationTypes;
};
export const initFormColumns = (
  options: IStationFormColumnOption
): IFormFieldGroup[] => {
  const typeTags = options.types.map((item) => ({
    value: item,
    label: item
  }));
  typeTags.length === 0 &&
    typeTags.push({ label: AttrGroupName_none, value: AttrGroupName_none });
  return [
    {
      fieldset: {
        type: 'underline',
        desc: '静态属性'
      },
      groupBtns: {
        styles: {
          paddingTop: '8px',
          textAlign: 'right'
        },
        btns: [
          {
            perm: true,
            text: '确定',
            click: options.station_submit
          }
        ]
      },
      fields: [
        {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          type: 'input',
          label: '名称：',
          field: 'name',
          rules: [{ required: true, message: '请输入名称' }]
        },
        {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          type: 'select',
          label: '类型：',
          field: 'type',
          options: initStationType(),
          rules: [{ required: true, message: '请输入类型' }]
        },
        {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          type: 'input',
          readonly: true,
          placeholder: ' ',
          label: '所属项目：',
          field: 'projectName'
        },
        {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          type: 'input',
          label: '地址：',
          field: 'address'
        },
        // {
        //   xs: 24,
        //   sm: 12,
        //   md: 8,
        //   lg: 8,
        //   xl: 8,
        //   type: 'input-number',
        //   label: 'X坐标：',
        //   field: 'x'
        // },
        // {
        //   xs: 24,
        //   sm: 12,
        //   md: 8,
        //   lg: 8,
        //   xl: 8,
        //   type: 'input',
        //   label: 'Y坐标：',
        //   field: 'y'
        // },
        { type: 'amap', field: 'location', resultType: 'str' },
        {
          xs: 24,
          sm: 8,
          md: 8,
          lg: 8,
          xl: 8,
          type: 'avatar',
          label: '图片：',
          field: 'imgs'
        },
        {
          xs: 24,
          sm: 16,
          md: 16,
          lg: 16,
          xl: 16,
          type: 'textarea',
          maxRow: 6,
          minRow: 6,
          label: '备注：',
          field: 'remark'
        },
        {
          type: 'tabs',
          label: '',
          field: 'stationAttrInfo_type',
          tabs: typeTags,
          closable: true,
          handleTabClick: (tab: any) => {
            if (tab.paneName === AttrGroupName_none) {
              options.group_add();
            }
          },
          handleTabRemove: options.group_remove,
          // selectNextAfterRemove: false,
          onChange: options.type_change,
          btns: [
            {
              type: 'primary',
              size: 'small',
              perm: true,
              text: '添加分组',
              click: options.group_add
            },
            {
              type: 'primary',
              size: 'small',
              perm: true,
              text: '添加变量',
              click: options.attrvar_add
            }
          ]
        },
        {
          type: 'table',
          label: '',
          field: 'attrList',
          config: options.TableConfig__AttrInfo,
          onChange: options.attrInfo_change
        },
        {
          type: 'tabs',
          label: '',
          field: 'type',
          tabs: [
            {
              value: 'scada',
              label: '组态配置'
            }
          ],
          btns: [
            {
              type: 'primary',
              size: 'small',
              text: '添加组态',
              perm: true,
              click: options.scada_add
            }
          ]
        },
        {
          type: 'table',
          label: '',
          field: 'scadaList',
          config: options.TableConfig__scada,
          onChange: options.scada_change
        }
      ]
    }
  ];
};
/**
 * 查询设备的参数列表
 * @param deviceId 不带-
 * @returns
 */
export const getAttrsOfDevice = async (deviceId: string) => {
  const optres = await getDeviceVarGroup(deviceId);
  const varsTree: NormalOption[] = formatTree(optres.data?.全部 || [], {
    label: 'value',
    value: 'label',
    id: 'label',
    children: 'children'
  });
  return varsTree;
};
export const getRowAttrs = async (item) => {
  if (!item?.deviceId) return item;
  const devid = removeSlash(item.deviceId);
  const varsTree = await getAttrsOfDevice(devid);
  if (item.range) {
    const minMax = item.range?.split(',');
    if (minMax?.length === 2) {
      item.min = minMax[0];
      item.max = minMax[1];
    }
  }

  item.attrAlias = item.attr;
  if (!item.attrAliasFormItemConfig) return item;
  item.attrAliasFormItemConfig.options = varsTree;
  return item;
};
export const initAttrInfoTableColumn = (
  devices?: NormalOption[]
): IFormTableColumn[] => {
  return [
    {
      minWidth: 60,
      label: '序号',
      prop: 'orderNum',
      align: 'center',
      width: 60
    },
    {
      minWidth: 180,
      label: '设备',
      prop: 'deviceId',
      formItemConfig: {
        type: 'select',
        label: '',
        field: '',
        options: devices || [],
        onChange: (val, row) => {
          if (!val) return;
          if (!row.attrAliasFormItemConfig) return;
          const devid = removeSlash(val);
          getAttrsOfDevice(devid).then((res) => {
            row.attrAliasFormItemConfig.options.length = 0;
            row.attrAliasFormItemConfig.options = res;
          });
        }
      }
    },
    {
      minWidth: 150,
      label: '变量名',
      prop: 'attrAlias'
    },
    { minWidth: 150, label: '字段名称', prop: 'attr' },
    {
      minWidth: 150,
      label: '别名',
      prop: 'name',
      formItemConfig: { type: 'input', label: '', field: '' }
    },
    {
      minWidth: 120,
      label: '最小值',
      prop: 'min',
      formItemConfig: {
        type: 'input-number',
        onChange: (val, row) => {
          if (row.min === undefined) row.min = '';
          if (row.max === undefined) row.max = '';
          if (row.min && row.max) {
            if (Number(row.min) > Number(row.max)) {
              SLMessage.warning('最小值不能大于最大值');
            }
          }
          row.range = row.min + ',' + row.max;
        }
      }
    },
    {
      minWidth: 120,
      label: '最大值',
      prop: 'max',
      formItemConfig: {
        type: 'input-number',
        onChange: (val, row) => {
          if (row.min === undefined) row.min = '';
          if (row.max === undefined) row.max = '';
          if (row.min && row.max) {
            if (Number(row.min) > Number(row.max)) {
              SLMessage.warning('最大值不能小于最小值');
            }
          }
          row.range = row.min + ',' + row.max;
        }
      }
    },
    {
      minWidth: 100,
      label: '单位',
      prop: 'unit',
      formItemConfig: {
        type: 'input'
      }
    }
  ];
};

export const initLineChartOption = (
  title: string,
  data?: any[],
  unit?: string
) => {
  const Data: number[] = [];
  const xData =
    data?.map((item) => {
      Data.push(item.value);
      return item.time;
    }) || [];
  const colors = ['#41fee7', '#40b4fb', '#3a89ac'];
  // const xData: string[] = []
  // const Data1: number[] = []
  // const Data2: number[] = []
  // Array.from({ length: 30 }).map((item, i) => {
  //   xData.push(moment().subtract(i, 'd').format('MM/DD'))
  //   Data1.push(basic + 0.05)
  //   Data2.push(Number((Math.random() * basic * 0.2 + basic).toFixed(3)))
  // })
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    // legend: {
    //   show: true,
    //   right: 'center',
    //   top: 10,
    //   textStyle: {
    //     color: '#aaa'
    //   }
    // },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData || [],
      axisLine: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#aaa'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: unit,
      nameLoacation: 'top',
      splitLine: {
        lineStyle: {
          color: '#aaa',
          opacity: 0.2
        }
      },
      splitArea: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#aaa'
        }
      }
      // boundaryGap: [0, '100%']
    },
    grid: {
      top: 40,
      left: 50,
      right: 20,
      bottom: 30
    },
    series: [
      // {
      //   name: '压力标线',
      //   type: 'line',
      //   symbol: 'none',
      //   sampling: 'lttb',
      //   itemStyle: {
      //     color: colors[0]
      //   },
      //   lineStyle: {
      //     type: 'dashed'
      //   },
      //   data: Data1 || []
      // },
      {
        name: title,
        type: 'line',
        smooth: true,
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: colors[1]
        },
        data: Data || []
      }
    ]
  };
  return option;
};
export const initDataSearchOption = (name?: string, data?: any[]) => {
  // const basic = 0.32
  const colors = ['#41fee7', '#40b4fb', '#3a89ac'];
  const xData: string[] = [];
  const Data2: number[] = [];
  data?.map((item) => {
    xData.push(item.time);
    Data2.push(item.value);
  });
  // Array.from({ length: 30 }).map((item, i) => {
  //   xData.push(moment().subtract(i, 'd').format('MM/DD'))
  //   // Data1.push(basic + 0.05)
  //   Data2.push(Number((Math.random() * basic * 0.2 + basic).toFixed(3)))
  // })
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      show: true,
      right: 'center',
      top: 10,
      textStyle: {
        color: '#aaa'
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData.reverse() || [],
      axisLine: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#aaa'
        }
      }
    },
    yAxis: {
      type: 'value',
      nameLoacation: 'top',
      splitLine: {
        lineStyle: {
          color: '#aaa',
          opacity: 0.2
        }
      },
      splitArea: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#aaa'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#aaa'
        }
      },
      boundaryGap: [0, '100%']
    },
    grid: {
      top: 60,
      left: 80,
      right: 30,
      bottom: 30
    },
    series: [
      // {
      //   name: '压力标线',
      //   type: 'line',
      //   symbol: 'none',
      //   sampling: 'lttb',
      //   itemStyle: {
      //     color: colors[0]
      //   },
      //   lineStyle: {
      //     type: 'dashed'
      //   },
      //   data: Data1 || []
      // },
      {
        name: name || '压力',
        type: 'line',
        smooth: true,
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: colors[1]
        },
        data: Data2 || []
      }
    ]
  };
  return option;
};
