<!--
  采集工程删除页面
 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <SLDrawer
      ref="refDrawer"
      :config="DrawerConfig"
    >
      <template #title>
        <span>{{ TableConfig.currentRow?.name }}</span> <span>{{ TableConfig.currentRow?.code }}</span>
      </template>
      <CollectDetail :row="TableConfig.currentRow"></CollectDetail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { SLConfirm, SLMessage } from '@/utils/Message'
import CollectDetail from './components/CollectDetail.vue'

const refSearch = ref<ICardSearchIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    { type: 'input', label: '工程名称', field: 'name' },
    { type: 'input', label: '工程编号', field: 'code' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '搜索', click: () => refreshData() },
        { perm: true, text: '重置', click: () => resetSearch() },
        { perm: true, text: '删除', type: 'danger', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})
const TableConfig = reactive<ITable>({
  columns: [
    { label: '工程编号', prop: 'code' },
    { label: '工程名称', prop: 'name' },
    { label: '竣工日期', prop: 'finishDate' }
  ],
  dataList: [],
  operations: [
    { perm: true, text: '详情', click: row => handleDetail(row) },
    { perm: true, text: '删除', type: 'danger', click: row => handleDelete(row) }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const resetSearch = () => {
  refSearch.value?.resetForm()
}
const handleDelete = async (row?: any) => {
  const ids = row ? [row.id] : TableConfig.selectList?.map(item => item.id) || []
  if (!ids.length) {
    SLMessage.warning('请选择要删除的数据')
    return
  }
  SLConfirm('确定删除？', '提示信息')
    .then(() => {
      try {
        // await DeleteProj(ids)
        SLMessage.success('删除成功')
        refreshData()
      } catch (error) {
        SLMessage.error('删除失败')
        console.log(error)
      }
    })
    .catch(() => {
      //
    })
}
const refreshData = () => {
  try {
    const params = {
      ...refSearch.value?.queryParams,
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit
    }
    console.log(params)
    // const res = await GetProjectList(params)
    // TableConfig.dataList = res.data.data || []
    // TableConfig.pagination.total = res.data.total || 0

    TableConfig.dataList = [
      { id: 'aaa', code: 'aaa', name: '工程1' },
      { id: 'bbb', code: 'bbb', name: '工程2' }
    ]
  } catch (error) {
    console.log(error)
  }
}

const refDrawer = ref<ISLDrawerIns>()

const DrawerConfig = reactive<IDrawerConfig>({
  title: '',
  group: [],
  appendToBody: false
})
const handleDetail = (row: any) => {
  TableConfig.currentRow = row
  refDrawer.value?.openDrawer()
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped></style>
