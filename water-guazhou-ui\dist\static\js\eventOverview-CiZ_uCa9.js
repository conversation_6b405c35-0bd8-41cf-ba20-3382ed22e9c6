import{m as t}from"./index-r0dFAfgr.js";const n=e=>t({url:"/api/eventOverview",method:"get",params:e}),o=e=>t({url:"/api/eventOverview",method:"post",data:e}),i=e=>t({url:`/api/eventOverview/${e.id}`,method:"post",data:e}),a=e=>t({url:"/api/eventOverview/batch",method:"delete",data:{ids:e}}),p=e=>t({url:`/api/eventOverview/${e}/complete`,method:"post"}),s=(e,v)=>t({url:`/api/eventOverview/${e}/approve`,method:"post",data:v}),u=(e,v)=>t({url:`/api/eventOverview/${e}/reject`,method:"post",data:v});export{s as A,a as B,o as C,n as G,u as R,i as U,p as a};
