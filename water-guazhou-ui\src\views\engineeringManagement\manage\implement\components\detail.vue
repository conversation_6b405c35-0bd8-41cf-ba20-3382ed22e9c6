<template>
  <div class="team_table">
    <FormTable :config="TableConfig"></FormTable>
    <DialogForm
      ref="refForm"
      class="dialogForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
    <DialogForm ref="refDeviceForm" :config="deviceConfig"></DialogForm>
    <DialogForm ref="refAddDeviceForm" :config="AdddeviceConfig"></DialogForm>
    <SLDrawer ref="refDetail" :config="detailConfig">
      <detail :config="data.selected" :show="10"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import {
  postConstructionApply,
  deleteConstructionApply,
  getConstructionContractType,
  postConstructionApplyComplete,
  getConstructionContractOptions
} from '@/api/engineeringManagement/manage';
import {
  postConstructionApplyDevice,
  getConstructionContractDevice,
  getConstructionApplyDevice
} from '@/api/engineeringManagement/device';
import { deleteDeviceItemExport } from '@/api/engineeringManagement/projectManagement';
import { formatDate } from '@/utils/DateFormatter';
import { StatusType } from '../../../data';
import { SLConfirm, SLMessage } from '@/utils/Message';
import detail from '../../../components/detail.vue';
import { traverse } from '@/utils/GlobalHelper';
import { ICONS } from '@/common/constans/common';

const refForm = ref<IDialogFormIns>();
const refDetail = ref<ISLDrawerIns>();
const refDeviceForm = ref<IDialogFormIns>();
const refAddDeviceForm = ref<IDialogFormIns>();

const props = defineProps<{
  config: {
    items: any;
  };
}>();

const emit = defineEmits(['extendedReturn']);

const TableConfig = reactive<ITable>({
  loading: false,
  indexVisible: true,
  dataList: computed(() => props.config.items) as any,
  columns: [
    // { prop: 'code', label: '实施编号' },
    { prop: 'contractName', label: '所属合同' },
    { prop: 'contractTypeName', label: '合同类别' },
    {
      prop: 'beginTime',
      label: '工期开始时间',
      formatter: (row) => formatDate(row.beginTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      prop: 'endTime',
      label: '工期结束时间',
      formatter: (row) => formatDate(row.endTime, 'YYYY-MM-DD HH:mm:ss')
    },
    { prop: 'creatorName', label: '创建人' },
    {
      prop: 'createTime',
      label: '创建时间',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      prop: 'status',
      label: '工作状态',
      tag: true,
      tagColor: (row): string =>
        StatusType.find((item) => item.value === row.status)?.color || '',
      formatter: (row) =>
        StatusType.find((item) => item.value === row.status)?.label
    }
  ],
  operationWidth: '320px',
  operations: [
    {
      text: '详情',
      perm: true,
      click: (row) => {
        data.selected = row;
        refDetail.value?.openDrawer();
      }
    },
    {
      disabled: (val) => val.status === 'COMPLETED',
      text: '完成',
      perm: true,
      click: (row) => {
        postConstructionApplyComplete(row.id).then((res) => {
          if (res.data.code === 200) {
            ElMessage.success('已完成');
          } else {
            ElMessage.warning('完成失败');
          }
          emit('extendedReturn', {});
        });
      }
    },
    {
      text: '查看设备',
      perm: true,
      click: (row) => {
        deviceConfig.defaultValue = {
          code: row.code,
          contractCode: row.contractCode
        };
        data.getProjectDeviceValue();
        refDeviceForm.value?.openDialog();
      }
    },
    {
      disabled: (val) => val.status === 'COMPLETED',
      text: '编辑',
      perm: true,
      click: (row) => clickEdit(row)
    },
    {
      disabled: (val) => val.status === 'COMPLETED',
      text: '删除',
      perm: true,
      click: (row) => handleDelete(row)
    },
    {
      disabled: (val) => val.status === 'COMPLETED',
      text: '添加设备',
      perm: true,
      click: (row) => {
        deviceConfig.defaultValue = {
          code: row.code,
          contractCode: row.contractCode
        };
        const table = AdddeviceConfig.group[0].fields.find(
          (item) => item.type === 'table'
        ) as IFormTable;
        table.config.selectList = [];
        AdddeviceConfig.defaultValue = { code: row.code };
        data.geDeviceListValue();
        refAddDeviceForm.value?.openDialog();
      }
    }
  ],
  pagination: {
    hide: true
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加实施明细',
  appendToBody: true,
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    let text = '新增';
    if (params.id) text = '修改';
    if (params.beginTime > params.endTime) {
      ElMessage.warning('时间范围异常');
      return;
    }
    addOrUpdateConfig.submitting = true;
    postConstructionApply(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
        } else {
          ElMessage.warning(text + '失败');
        }
        emit('extendedReturn', {});
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '实施编号',
          field: 'code',
          rules: [{ required: true, message: '请输入实施编号' }],
          disabled: true
        },
        {
          xs: 12,
          type: 'select',
          label: '所属合同编号',
          field: 'contractCode',
          options: computed(() => data.contractList) as any
        },
        {
          xs: 12,
          type: 'date',
          label: '工期开始时间',
          field: 'beginTime',
          rules: [{ required: true, message: '请输入工期开始时间' }],
          format: 'x'
        },
        {
          xs: 12,
          type: 'date',
          label: '工期结束时间',
          field: 'endTime',
          rules: [{ required: true, message: '请输入工期结束时间' }],
          format: 'x'
        },
        {
          xs: 12,
          type: 'input',
          label: '工程负责人',
          field: 'principal',
          rules: [{ required: true, message: '请输入工程负责人' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '联系电话',
          field: 'phone'
        },
        {
          xs: 12,
          type: 'input',
          label: '施工班组',
          field: 'constructClass'
        },
        {
          type: 'textarea',
          label: '详细说明',
          field: 'remark'
        }
      ]
    }
  ]
});

const deviceConfig = reactive<IDialogFormConfig>({
  title: '查看设备',
  labelWidth: '130px',
  appendToBody: true,
  dialogWidth: '1000px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'table',
          field: 'deviceTable',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceInformation) as any,
            columns: [
              { label: '设备名称', prop: 'deviceName' },
              { label: '设备编码', prop: 'serialId' },
              { label: '设备型号', prop: 'model' },
              { label: '所属大类', prop: 'deviceTopTypeName' },
              { label: '所属分类', prop: 'deviceType' },
              { label: '设备标识', prop: 'mark' },
              { label: '计量单位', prop: 'unit' },
              { label: '清单总量', prop: 'amount' }
            ],
            operations: [
              {
                perm: true,
                text: '删除',
                icon: ICONS.DELETE,
                click: (row) => {
                  SLConfirm('确定删除该设备', '删除提示').then(() => {
                    deleteDeviceItemExport(row.id)
                      .then(() => {
                        ElMessage.success('删除成功');
                        data.getProjectDeviceValue();
                      })
                      .catch((error) => {
                        ElMessage.warning(error);
                      });
                  });
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

const AdddeviceConfig = reactive<IDialogFormConfig>({
  title: '添加设备',
  labelWidth: '80px',
  appendToBody: true,
  dialogWidth: '1000px',
  defaultValue: {},
  submitting: false,
  submit: (params: any, status: boolean) => {
    if (status) {
      data.geDeviceListValue(params);
    } else {
      let status = false;
      const table = AdddeviceConfig.group[0].fields.find(
        (item) => item.type === 'table'
      ) as IFormTable;
      if (table.config.selectList?.length === 0) {
        ElMessage.warning('请勾选设备');
        return;
      }
      const val: any = table.config.selectList?.map((item) => {
        if (item.number === 0 || !item.number) {
          ElMessage.warning('数量最少为1台');
          status = true;
        }
        if (item.number > item.rest) {
          ElMessage.warning('申请数量超过剩余数量');
          status = true;
        }
        return {
          serialId: item.serialId,
          amount: item.number || 0
        };
      });
      if (status) {
        return;
      }
      AdddeviceConfig.submitting = true;
      postConstructionApplyDevice(params.code, val)
        .then((res) => {
          AdddeviceConfig.submitting = false;
          if (res.data.code === 200) {
            ElMessage.success('添加成功');
            refAddDeviceForm.value?.closeDialog();
          } else {
            ElMessage.warning('添加失败');
          }
        })
        .catch((error) => {
          AdddeviceConfig.submitting = false;
          ElMessage.warning(error);
        });
    }
  },
  group: [
    {
      fields: [
        {
          xs: 6,
          type: 'input',
          field: 'serialId',
          label: '设备编码'
        },
        {
          xs: 6,
          type: 'input',
          field: 'name',
          label: '设备名称'
        },
        {
          xs: 6,
          type: 'input',
          field: 'model',
          label: '设备型号'
        },
        {
          xs: 6,
          type: 'btn-group',
          btns: [
            {
              text: '查询',
              perm: true,
              click: () => {
                refAddDeviceForm.value?.Submit(true);
              }
            },
            {
              text: '重置',
              perm: true,
              type: 'default',
              click: () => {
                refAddDeviceForm.value?.resetForm();
                data.geDeviceListValue();
              }
            }
          ]
        },
        {
          type: 'table',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceInformation) as any,
            handleSelectChange: (val) => {
              const table = AdddeviceConfig.group[0].fields.find(
                (item) => item.type === 'table'
              ) as IFormTable;
              table.config.selectList = val;
            },
            selectList: [],
            columns: [
              { label: '设备名称', prop: 'deviceName' },
              { label: '设备编码', prop: 'serialId' },
              { label: '设备型号', prop: 'model' },
              { label: '所属大类', prop: 'deviceTopTypeName' },
              { label: '所属分类', prop: 'deviceType' },
              { label: '设备标识', prop: 'mark' },
              { label: '计量单位', prop: 'unit' },
              { label: '清单总量', prop: 'rest' },
              {
                label: '申请数量',
                prop: 'number',
                minWidth: '120px',
                formItemConfig: {
                  type: 'number',
                  field: 'number',
                  min: 0
                }
              }
            ],
            pagination: {
              page: 1,
              limit: 20,
              total: 0,
              refreshData: ({ page, size }) => {
                const table = AdddeviceConfig.group[0].fields.find(
                  (item) => item.type === 'table'
                ) as IFormTable;
                table.config.pagination.page = page;
                table.config.pagination.limit = size;
                data.geDeviceListValue();
              }
            }
          }
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  group: [],
  width: '80%',
  modalClass: 'lightColor',
  appendToBody: true,
  cancel: false
});

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑实施明细';
  addOrUpdateConfig.defaultValue = {
    ...(row || {}),
    time: [row.workTimeBegin, row.workTimeEnd]
  };
  data.getConstructionContract(row.contractCode);
  refForm.value?.openDialog();
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除？', '提示信息')
    .then(() => {
      deleteConstructionApply(row.id)
        .then((res) => {
          if (res.data.code === 200) {
            SLMessage.success(res.data.message);
          } else {
            SLMessage.error(res.data.message);
          }
          emit('extendedReturn', {});
        })
        .catch((error) => {
          ElMessage.warning(error);
        });
    })
    .catch(() => {
      //
    });
};

const data = reactive({
  // 合同类型
  ConstructionContractType: [],
  // 设备信息
  deviceInformation: [],
  // 合同列表
  contractList: [],
  selected: {},
  getOptions: () => {
    getConstructionContractType({ page: 1, size: -1 }).then((res) => {
      data.ConstructionContractType = traverse(
        res.data.data.data || [],
        'children'
      );
    });
  },
  geDeviceListValue: (val?: any) => {
    data.deviceInformation = [];
    const table = AdddeviceConfig.group[0].fields.find(
      (item) => item.type === 'table'
    ) as IFormTable;
    const params = {
      page: table.config.pagination.page || 1,
      size: table.config.pagination.limit || 20,
      ...val
    };
    getConstructionContractDevice(
      deviceConfig.defaultValue?.contractCode,
      params
    ).then((res) => {
      data.deviceInformation = res.data.data.data || [];
      table.config.pagination.total = res.data.data.total || 0;
    });
  },
  getProjectDeviceValue: () => {
    data.deviceInformation = [];
    const table = deviceConfig.group[0].fields.find(
      (item) => item.type === 'table'
    ) as IFormTable;
    const params = {
      page: table.config.pagination.page || 1,
      size: table.config.pagination.limit || 20
    };
    getConstructionApplyDevice(deviceConfig.defaultValue?.code, params).then(
      (res) => {
        data.deviceInformation = res.data.data.data || [];
        table.config.pagination.total = res.data.data.total || 0;
      }
    );
  },
  getConstructionContract: (val: string) => {
    getConstructionContractOptions(val).then((res) => {
      data.contractList = traverse(res.data.data.data || [], 'children', {
        label: 'name',
        value: 'code'
      });
    });
  }
});

onMounted(() => {
  data.getOptions();
  data.geDeviceListValue();
});
</script>

<style lang="scss" scoped>
.team_table {
  width: 100%;
  padding: 5px 15px;
}
</style>
