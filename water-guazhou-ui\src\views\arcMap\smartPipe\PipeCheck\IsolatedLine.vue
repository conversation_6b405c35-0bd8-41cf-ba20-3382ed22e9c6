<!-- 孤立组件检查 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'孤立组件检查'"
    @map-loaded="onMapLoaded"
    @detail-refreshed="state.loading = false"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point'
import {
  createPictureMarker,
  excuteIdentify,
  getGraphicLayer,
  getNeerestPoint,
  getSubLayerIds,
  initDrawer,
  initIdentifyParams,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { CheckIsolatedLine } from '@/api/mapservice/pipeCheck'
import { getStationImageUrl } from '@/utils/URLHelper'

const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerInfos: any[]
  layerIds: any[]
  curType: 'ellipse' | 'rectangle' | 'polygon' | ''
  flagOids: number[]
}>({
  tabs: [],
  curType: '',
  layerInfos: [],
  layerIds: [],
  loading: false,
  flagOids: []
})
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
  draw?: __esri.Draw
  drawAction?: __esri.DrawAction
  identifyResults: any[]
} = {
  identifyResults: []
}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '选择水源点'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '选择水源地',
              disabled: () => state.loading,
              iconifyIcon: 'gis:multipoint',
              click: () => initDraw()
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    },
    {
      id: 'layer',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: true,
          label: '选择图层',
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value',
          handleCheckChange: (data, isChecked) => {
            if (isChecked) {
              refForm.value && (refForm.value.dataForm.layerid = [data.value])
            }
          }
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: () => (state.loading ? '正在检查，过程稍长，请耐心等待！' : '检查'),
              styles: {
                width: '100%'
              },
              loading: () => state.loading,
              click: () => startQuery()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    length: 1
  }
})
const initDraw = () => {
  if (!staticState.view) return
  setMapCursor('crosshair')

  staticState.draw = initDrawer(staticState.view)
  staticState.drawAction = staticState.draw.create('point')

  staticState.drawAction?.on('draw-complete', async e => {
    doIdentify(e)
    initDraw()
  })
}
const doIdentify = async (e: any) => {
  if (!e.coordinates?.length) return
  if (!staticState.view) return
  const layerid = refForm.value?.dataForm.layerid || []
  if (!layerid.length === undefined) {
    SLMessage.warning('请选择一个图层')
    return
  }
  try {
    const mapPoint = new Point({
      x: e.coordinates[0],
      y: e.coordinates[1],
      spatialReference: staticState.view?.spatialReference
    })
    const queryParams = initIdentifyParams()
    queryParams.layerIds = layerid
    queryParams.geometry = mapPoint
    queryParams.mapExtent = staticState.view?.extent
    const res = await excuteIdentify(
      window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
      queryParams
    )
    const identifyResult = res.results && res.results[0]
    if (!identifyResult) {
      SLMessage.warning('没有查询到管线')
      return
    }
    const warterSourcePoint = getNeerestPoint(
      identifyResult?.feature?.geometry,
      mapPoint
    )
    if (!warterSourcePoint) return
    const feature = identifyResult.feature
    feature.symbol = setSymbol('polyline')
    staticState.graphicsLayer?.add(feature)
    state.flagOids.push(identifyResult?.feature.attributes.OBJECTID)
    const picture = createPictureMarker(
      warterSourcePoint.x,
      warterSourcePoint.y,
      {
        picUrl: getStationImageUrl('水源地.png'),
        picSize: [12, 15],
        yOffset: 7,
        spatialReference: staticState.view?.spatialReference
      }
    )
    staticState.graphicsLayer?.add(picture)
  } catch (error) {
    SLMessage.error('检查失败')
  }
}
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
  state.flagOids.length = 0
}
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
  const field = FormConfig.group.find(item => item.id === 'layer')
    ?.fields[0] as IFormTree
  // const points = state.layerInfos
  //   .filter(item => item.geometrytype === 'esriGeometryPoint')
  //   .map(item => {
  //     return {
  //       label: item.layername,
  //       value: item.layerid,
  //       data: item
  //     }
  //   })
  const lines = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPolyline')
    .map(item => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      }
    })
  field
    && (field.options = [
      // { label: '管点类', value: -1, children: points, disabled: true }
      { label: '管线类', value: -2, children: lines, disabled: true }
    ])
  if (lines.length === 1) {
    refForm.value
      && (refForm.value.dataForm.layerid = lines.map(item => item.value))
  }
}
const startQuery = async () => {
  staticState.drawAction?.destroy()
  setMapCursor('')
  SLMessage.info('正在检查，请稍候...')
  const layerid = refForm.value?.dataForm.layerid[0]
  if (layerid === undefined) {
    SLMessage.warning('请先选择一个图层')
    return
  }
  if (!state.flagOids.length) {
    SLMessage.warning('请先选择水源点')
    return
  }
  state.loading = true
  try {
    const layername = state.layerInfos.find(
      item => item.layerid === layerid
    )?.layername
    const res = await CheckIsolatedLine({
      flagOIDs: state.flagOids.join(','),
      layer: layername
    })
    if (!res.data.result?.length) {
      SLMessage.success('没有相关数据')
    }
    const tabs = [
      {
        label: layername,
        name: layername,
        data: res.data?.result || [],
        layerid
      }
    ]
    await refMap.value?.refreshDetail(tabs)
  } catch (error) {
    console.log(error)
    state.loading = false
    SLMessage.error('检查失败')
  }
}
const onMapLoaded = view => {
  staticState.view = view
  getLayerInfo()
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'isolated-line',
    title: '孤立组件'
  })
}
onBeforeUnmount(() => {
  staticState.graphicsLayer?.removeAll()
  staticState.drawAction?.destroy()
  staticState.draw?.destroy()
})
</script>
<style lang="scss" scoped></style>
