{"name": "thingsboard-js-executor", "private": true, "version": "2.3.0", "description": "ThingsBoard JavaScript Executor Microservice", "main": "server.js", "bin": "server.js", "scripts": {"build-proto": "pbjs -t static-module -w commonjs -o ./api/jsinvoke.proto.js ../../application/src/main/proto/jsinvoke.proto", "install": "npm run build-proto && pkg -t node8-linux-x64,node8-win-x64 --out-path ./target . && node install.js", "test": "echo \"Error: no test specified\" && exit 1", "start": "npm run build-proto && nodemon server.js", "start-prod": "npm run build-proto && NODE_ENV=production nodemon server.js"}, "dependencies": {"config": "^1.30.0", "js-yaml": "^3.12.0", "kafka-node": "^3.0.1", "long": "^4.0.0", "protobufjs": "^6.8.8", "uuid-parse": "^1.0.0", "winston": "^3.0.0", "winston-daily-rotate-file": "^3.2.1"}, "engine": "node >= 5.9.0", "nyc": {"exclude": ["test", "__tests__", "node_modules", "target"]}, "devDependencies": {"fs-extra": "^6.0.1", "nodemon": "^1.17.5", "pkg": "^4.3.3"}}