package org.thingsboard.server.dao.model.request;

import lombok.Data;

import java.util.List;

@Data
public class InfoEditRecordsRequest {

    private int page;

    private int size;

    private String orgId;

    private String meterBookIds;

    private List<String> orgIdList;

    private String custCode;  // 用户编号

    private String custName;  // 户名

    private String address;  // 用水地址

    private Long start; // 抄表序号开始

    private Long end; // 抄表序号结束

    private String type; // 维护类型

    private String tenantId;

}
