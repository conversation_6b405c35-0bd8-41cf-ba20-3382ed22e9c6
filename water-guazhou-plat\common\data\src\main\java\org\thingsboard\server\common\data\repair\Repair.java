/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.repair;

import org.thingsboard.server.common.data.BaseData;
import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.RepairId;
import org.thingsboard.server.common.data.id.TenantId;

public class Repair extends BaseData<RepairId> {

    private DeviceId deviceId;
    private Long repairStartTime;
    private Long repairEndTime;
    private String status;
    private String remark;
    private TenantId tenantId;

    public Repair() {
        super();
    }

    public Repair(RepairId id) {
        super(id);
    }

    public Repair(Repair repair) {
        super();
        this.deviceId = repair.getDeviceId();
        this.createdTime = repair.getCreatedTime();
        this.repairStartTime = repair.getRepairStartTime();
        this.repairEndTime = repair.getRepairEndTime();
        this.status = repair.getStatus();
        this.remark = repair.getRemark();
        this.tenantId = repair.getTenantId();
    }

    public DeviceId getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(DeviceId deviceId) {
        this.deviceId = deviceId;
    }

    public Long getRepairStartTime() {
        return repairStartTime;
    }

    public void setRepairStartTime(Long repairStartTime) {
        this.repairStartTime = repairStartTime;
    }

    public Long getRepairEndTime() {
        return repairEndTime;
    }

    public void setRepairEndTime(Long repairEndTime) {
        this.repairEndTime = repairEndTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setStatus(RepairType status) {
        this.status = status.getCode();
    }

    public TenantId getTenantId() {
        return tenantId;
    }

    public void setTenantId(TenantId tenantId) {
        this.tenantId = tenantId;
    }
}
