package org.thingsboard.server.dao.tenant;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.TenantLogin;
import org.thingsboard.server.dao.sql.tenant.TenantLoginRepository;

import java.security.MessageDigest;

@Slf4j
@Service
public class TenantLoginServiceImpl implements TenantLoginService {

    @Autowired
    private TenantLoginRepository tenantLoginRepository;

    @Override
    public TenantLogin login(String account, String password) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] messageDigest = md.digest(password.getBytes());
        StringBuilder passwordSb = new StringBuilder();
        for (byte b : messageDigest) {
            passwordSb.append(String.format("%02x", b));
        }
        // 查询账号密码
        TenantLogin tenantLogin = tenantLoginRepository.findByAccountAndPassword(account, passwordSb.toString().toUpperCase());
        if (tenantLogin == null) {
            throw new ThingsboardException("账号密码错误!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        return tenantLogin;
    }
}
