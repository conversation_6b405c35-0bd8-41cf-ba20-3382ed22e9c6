<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.tenant.TenantApplicationGuideMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.TenantApplicationGuideEntity">
        select a.*, b.name as applicationName
        from tenant_application_guide a
        left join tenant_application b on a.application_id = b.id
        where a.tenant_id = #{tenantId}
        order by a.create_time desc
    </select>

    <select id="getByApplicationId" resultType="org.thingsboard.server.dao.model.sql.TenantApplicationGuideEntity">
        select a.*, b.name as applicationName
        from tenant_application_guide a
        left join tenant_application b on a.application_id = b.id
        where a.application_id = #{applicationId}
    </select>

    <select id="getNotSetList" resultType="org.thingsboard.server.dao.model.sql.TenantApplicationEntity">
        select *
        from tenant_application
        where id not in (
            select application_id from tenant_application_guide where tenant_id = #{tenantId}
            )
        and tenant_id = #{tenantId}
        order by create_time desc
    </select>
</mapper>