import{s as i,e as r,J as G}from"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{W as t}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";const C=(e,s)=>i(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisBurstGPService,{pipefeatureclassname:e,pipeobjectid:s,bysource:!0,usertoken:t().gToken}),F=e=>i(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisShutValveAnalysGPService,{...e||{}}),N=e=>i(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisExtendShutValveAnalysGPService,{...e||{}}),O=(e,s,o)=>i(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisConnectGPService,{pipefeatureclassname:e,pipeobjectid:s,applystop:o,usertoken:t().gToken,versioned:!1}),_=(e,s)=>i(e,s),p=(e,s)=>{(!e||!s)&&console.log("未传递参数");const o=window.SITE_CONFIG.GIS_CONFIG.gisUtilitiesService+window.SITE_CONFIG.GIS_CONFIG.gisPrintingToolsGPService+"/Export%20Web%20Map%20Task",n=new r({view:e,template:s,outSpatialReference:e==null?void 0:e.spatialReference});return G(decodeURI(o),n)};export{N as a,O as b,_ as c,F as d,p as e,C as s};
