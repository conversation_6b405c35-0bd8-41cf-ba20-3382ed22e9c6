<!-- 统一工单-统计分析-人员上报分析 -->
<template>
  <div class="wrapper">
    <SLCard
      class="card"
      title=" "
      overlay
    >
      <template #title>
        <Search
          ref="refSearch"
          :config="SearchConfig"
        ></Search>
      </template>
      <div class="top">
        <div class="top-left">
          <FormTable :config="TableConfig"></FormTable>
        </div>
        <div class="top-right">
          <VChart
            ref="refChart1"
            :option="state.pieOption"
          ></VChart>
        </div>
      </div>
      <div class="bottom">
        <VChart
          ref="refChart"
          :option="state.barOption"
        ></VChart>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, shallowRef, onMounted, onBeforeUnmount } from 'vue'
import { Search as SearchIcon } from '@element-plus/icons-vue'
import { ISearchIns } from '@/components/type'
// import { getUserEventCount } from '../config'
import { BarOption, PieChartOption } from './echart'
import { IECharts } from '@/plugins/echart'
import { GetWorkOrderCountStatistic } from '@/api/workorder'

const refSearch = ref<ISearchIns>()
const refChart = ref<IECharts>()
const refChart1 = ref<IECharts>()
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      label: '时间范围',
      options: [
        { label: '日', value: 'date' },
        { label: '月', value: 'month' },
        { label: '年', value: 'year' }
      ],
      field: 'type',
      onChange: () => refreshData()
    },
    {
      clearable: false,
      handleHidden: (params, query, config) => {
        config.hidden = params.type !== 'date'
      },
      type: 'date',
      label: '',
      field: 'date'
    },
    {
      clearable: false,
      handleHidden: (params, query, config) => {
        config.hidden = params.type !== 'month'
      },
      type: 'month',
      label: '',
      field: 'month'
    },
    {
      clearable: false,
      handleHidden: (params, query, config) => {
        config.hidden = params.type !== 'year'
      },
      type: 'year',
      label: '',
      field: 'year'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        }
      ]
    }
  ],
  defaultParams: {
    type: 'date',
    month: moment().format('YYYY-MM'),
    year: moment().format('YYYY'),
    date: moment().format('YYYY-MM-DD')
  }
})
const TableConfig = reactive<ITable>({
  indexVisible: true,
  columns: [
    { label: '发起人员', prop: 'key' },
    { label: '发起事件数', prop: 'value' }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
const state = reactive<{
  pieOption: any
  barOption: any
}>({
  pieOption: null,
  barOption: null
})

const refreshData = async () => {
  const query = refSearch.value?.queryParams || {}

  const type = query?.type || 'date'
  let date: moment.Moment
  switch (type) {
    case 'month':
      date = moment(query[type], 'YYYY-MM')
      break
    case 'year':
      date = moment(query[type], 'YYYY')
      break
    default:
      date = moment(query[type], 'YYYY-MM-DD')
      break
  }
  const res = await GetWorkOrderCountStatistic({
    fromTime: date
      .startOf(type === 'year' ? 'y' : type === 'month' ? 'M' : 'D')
      .valueOf(),
    toTime: date
      .endOf(type === 'year' ? 'y' : type === 'month' ? 'M' : 'D')
      .valueOf(),
    statisticOrganizer: true
  })
  const data = res.data?.data?.organizers || {}
  state.barOption = BarOption(data.data || [])
  state.pieOption = PieChartOption(
    data.data || [],
    '人员上报分析'
  )
  TableConfig.dataList = data.data || []
  TableConfig.pagination.total = data.total || 0
}
const resizeChart = () => {
  refChart.value?.resize()
  refChart1.value?.resize()
}
onMounted(() => {
  refreshData()
  window.addEventListener('resize', resizeChart)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeChart)
})
</script>
<style lang="scss" scoped>
.card {
  height: 100%;
}

.top {
  display: flex;
  flex-direction: row;
  height: 400px;

  .top-left {
    width: 50%;
    height: 100%;
  }

  .top-right {
    width: 50%;
    height: 100%;
  }
}

.bottom {
  height: 500px;
}
</style>
