export const useScroll = () => {
  const scrollLeft = ref<number>(0)
  let scrollYTimer = 0
  const init = (el: HTMLElement) => {
    if (!el) return
    el.addEventListener('wheel', e => {
      e.preventDefault()
      el.scrollLeft -= e.deltaY
      scrollLeft.value = el.scrollLeft
    })
  }
  let isPaused = false
  /**
   * 开始纵向滚动
   * @param el 绑定的滚动窗口
   * @param options {deltaY: 每次滚动的距离 = 1px，deltaTime: 滚动时间间隔 = 66ms}
   */
  const startScrollY = (
    el?: HTMLElement,
    options?: { deltaTime?: number; deltaY?: number }
  ) => {
    const defaultOptions = {
      deltaTime: 66,
      deltaY: 1,
      ...(options || {})
    }
    if (!el) return
    isPaused = false
    scrollYTimer = setInterval(() => {
      if (!el) {
        stopScrollY()
        return
      }
      const clientHeight = el.clientHeight
      const scrollTop = el.scrollTop
      const scrollHeight = el.scrollHeight
      if (clientHeight + scrollTop >= scrollHeight) {
        stopScrollY()
        // 滚动到底部后间隔1秒再进行下一轮滚动
        setTimeout(() => {
          if (!isPaused) return
          el.scrollTo({ top: 0 })
          startScrollY(el, options)
        }, 300)
      } else if (!isPaused) {
        el.scrollTo({ top: scrollTop + defaultOptions.deltaY })
      }
    }, defaultOptions.deltaTime)
    el.onmouseover = () => stopScrollY()
    el.onmouseleave = () => startScrollY(el, options)
  }
  const stopScrollY = () => {
    isPaused = true
    clearInterval(scrollYTimer)
  }
  return {
    init,
    scrollLeft,
    startScrollY,
    stopScrollY
  }
}
