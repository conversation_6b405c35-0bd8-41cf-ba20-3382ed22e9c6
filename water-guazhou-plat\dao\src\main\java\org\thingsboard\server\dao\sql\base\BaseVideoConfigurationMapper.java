package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseVideoConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseVideoConfigurationPageRequest;

import java.util.List;

/**
 * 平台管理-视频管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Mapper
public interface BaseVideoConfigurationMapper {
    /**
     * 查询平台管理-视频管理
     *
     * @param id 平台管理-视频管理主键
     * @return 平台管理-视频管理
     */
    public BaseVideoConfiguration selectBaseVideoConfigurationById(String id);

    /**
     * 查询平台管理-视频管理列表
     *
     * @param baseVideoConfiguration 平台管理-视频管理
     * @return 平台管理-视频管理集合
     */
    public IPage<BaseVideoConfiguration> selectBaseVideoConfigurationList(BaseVideoConfigurationPageRequest baseVideoConfiguration);

    /**
     * 新增平台管理-视频管理
     *
     * @param baseVideoConfiguration 平台管理-视频管理
     * @return 结果
     */
    public int insertBaseVideoConfiguration(BaseVideoConfiguration baseVideoConfiguration);

    /**
     * 修改平台管理-视频管理
     *
     * @param baseVideoConfiguration 平台管理-视频管理
     * @return 结果
     */
    public int updateBaseVideoConfiguration(BaseVideoConfiguration baseVideoConfiguration);

    /**
     * 删除平台管理-视频管理
     *
     * @param id 平台管理-视频管理主键
     * @return 结果
     */
    public int deleteBaseVideoConfigurationById(String id);

    /**
     * 批量删除平台管理-视频管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseVideoConfigurationByIds(@Param("array") List<String> ids);
}
