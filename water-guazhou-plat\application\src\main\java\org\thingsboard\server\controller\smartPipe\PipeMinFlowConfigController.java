package org.thingsboard.server.controller.smartPipe;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeMinFlowConfig;
import org.thingsboard.server.dao.smartPipe.PipeMinFlowConfigService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 智慧管网-小流量指标设置
 */
@RestController
@RequestMapping("api/spp/minFlowConfig")
public class PipeMinFlowConfigController extends BaseController {

    @Autowired
    private PipeMinFlowConfigService partitionMountService;

    @GetMapping("listByPartitionId/{partitionId}")
    public IstarResponse getListByPartitionId(@PathVariable String partitionId) {
        return IstarResponse.ok(partitionMountService.getListByPartition(partitionId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody PipeMinFlowConfig pipeMinFlowConfig) throws ThingsboardException {
        if (StringUtils.isBlank(pipeMinFlowConfig.getPartitionId())) {
            return IstarResponse.error("分区id不能为null");
        }
        List<PipeMinFlowConfig> listByPartition = partitionMountService.getListByPartition(pipeMinFlowConfig.getPartitionId());
        if (listByPartition == null || listByPartition.size() == 0) {
            return IstarResponse.error("该分区不存在");
        }
        pipeMinFlowConfig.setId(listByPartition.get(0).getId());

        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        pipeMinFlowConfig.setTenantId(tenantId);
        pipeMinFlowConfig.setCreator(userId);
        return IstarResponse.ok(partitionMountService.save(pipeMinFlowConfig));
    }
}
