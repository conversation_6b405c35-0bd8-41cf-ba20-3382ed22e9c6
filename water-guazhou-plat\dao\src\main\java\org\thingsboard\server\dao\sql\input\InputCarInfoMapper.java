package org.thingsboard.server.dao.sql.input;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.InputCarInfoListRequest;
import org.thingsboard.server.dao.model.sql.input.InputCarInfo;

@Mapper
public interface InputCarInfoMapper extends BaseMapper<InputCarInfo> {

    IPage<InputCarInfo> findList(IPage<InputCarInfo> pageRequest, @Param("param") InputCarInfoListRequest request);

}
