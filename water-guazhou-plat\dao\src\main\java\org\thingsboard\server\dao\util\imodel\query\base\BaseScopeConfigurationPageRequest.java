package org.thingsboard.server.dao.util.imodel.query.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseScopeConfiguration;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 公共管理平台-范围设置对象 base_scope_configuration
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@ApiModel(value = "范围设置", description = "公共管理平台-范围设置实体类")
@Data
public class BaseScopeConfigurationPageRequest extends PageableQueryEntity<BaseScopeConfiguration> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 范围名称
     */
    @ApiModelProperty(value = "范围名称")
    private String name;

    /**
     * 范围描述
     */
    @ApiModelProperty(value = "范围描述")
    private String description;

    /**
     * 范围类型
     */
    @ApiModelProperty(value = "范围类型")
    private String regionType;

    /**
     * 范围值-最小横坐标
     */
    @ApiModelProperty(value = "范围值-最小横坐标")
    private String minX;

    /**
     * 范围值-最小纵坐标
     */
    @ApiModelProperty(value = "范围值-最小纵坐标")
    private String minY;

    /**
     * 范围值-最大横坐标
     */
    @ApiModelProperty(value = "范围值-最大横坐标")
    private String maxX;

    /**
     * 范围值-最大纵坐标
     */
    @ApiModelProperty(value = "范围值-最大纵坐标")
    private String maxY;
}
