<template :key="key">
  <el-descriptions
    :title="config.title || ''"
    :border="config.border || false"
    :column="config.column || 3"
    :direction="config.direction || 'horizontal'"
    :size="config.size || 'default'"
  >
    <template v-for="(item, key) in config.fields" :key="key">
      <el-descriptions-item
        :label="item.label"
        :span="item.span || 1"
        :width="config.width || item.width || 'auto'"
        :label-align="config.labelAlign || item.labelAlign || 'center'"
      >
        <div class="item">
          <!-- 图片处理 -->
          <el-image
            v-if="
              item.type === 'image' &&
              !Array.isArray(DataDeconstruction(item.field, state.dataForm)) &&
              state.dataForm[item.field || '']
            "
            style="width: 100px; height: 100px"
            :preview-src-list="[state.dataForm[item.field || '']]"
            :src="state.dataForm[item.field || '']"
          />
          <template
            v-else-if="
              item.type === 'image' &&
              Array.isArray(DataDeconstruction(item.field, state.dataForm))
            "
          >
            <template
              v-for="(key, index) in DataDeconstruction(
                item.field,
                state.dataForm
              )"
              :key="index"
            >
              <el-image
                style="width: 100px; height: 100px; margin-right: 10px"
                :preview-src-list="[key]"
                :src="key"
              />
            </template>
          </template>
          <!-- 文件 -->
          <a
            v-else-if="
              item.type === 'file' &&
              !Array.isArray(DataDeconstruction(item.field, state.dataForm)) &&
              state.dataForm[item.field || '']
            "
            @click="downloadFile(state.dataForm[item.field || ''])"
            >{{ state.dataForm[item.field || ''] }}</a
          >
          <template
            v-else-if="
              item.type === 'file' &&
              Array.isArray(DataDeconstruction(item.field, state.dataForm))
            "
          >
            <div class="flex"></div>
            <template
              v-for="(key, index) in DataDeconstruction(
                item.field,
                state.dataForm
              )"
              :key="index"
            >
              <a @click="downloadFile(key)">{{ key }}</a>
              <br />
            </template>
          </template>
          <!-- tag -->
          <el-tag
            v-else-if="item.type === 'text' && item.tag"
            :style="styles(item)"
          >
            {{
              (item.formatter &&
                item.formatter(state.dataForm[item.field || ''], item)) ||
              getJsonValue(state.dataForm, item.field)
            }}
          </el-tag>
          <!-- 音频 -->
          <audio
            v-else-if="
              item.type === 'audio' &&
              !Array.isArray(DataDeconstruction(item.field, state.dataForm)) &&
              state.dataForm[item.field || '']
            "
            controls
          >
            <source :src="state.dataForm[item.field || '']" type="audio/mpeg" />
            您的浏览器不支持 audio 元素.
          </audio>
          <template
            v-else-if="
              item.type === 'audio' &&
              Array.isArray(DataDeconstruction(item.field, state.dataForm))
            "
          >
            <div class="flex"></div>
            <template
              v-for="(key, index) in DataDeconstruction(
                item.field,
                state.dataForm
              )"
              :key="index"
            >
              <audio controls>
                <source :src="key" type="audio/mpeg" />
                您的浏览器不支持 audio 元素.
              </audio>
            </template>
          </template>
          <!-- 视频 -->
          <DPlayer
            v-else-if="
              item.type === 'video' &&
              !Array.isArray(DataDeconstruction(item.field, state.dataForm)) &&
              state.dataForm[item.field || '']
            "
            style="width: 300px; height: 300px"
            :video-info="{
              live: false,
              video: {
                url: state.dataForm[item.field || '']
              }
            }"
          >
          </DPlayer>
          <template
            v-else-if="
              item.type === 'video' &&
              Array.isArray(DataDeconstruction(item.field, state.dataForm))
            "
          >
            <template
              v-for="(key, index) in DataDeconstruction(
                item.field,
                state.dataForm
              )"
              :key="index"
            >
              <DPlayer
                style="width: 300px; margin-right: 10px; height: 300px"
                :video-info="{
                  live: false,
                  video: {
                    url: key
                  }
                }"
              >
              </DPlayer>
            </template>
          </template>
          <!-- 分割线 -->
          <hr v-else-if="item.type === 'divider'" />
          <!-- 地图 -->
          <template v-else-if="item.type === 'map'">
            {{ setposition(state.dataForm[item.field || ''], key) }}
            <SuperMapProvider
              style="width: 100%; height: 350px; min-width: 600px"
            >
              <SuperMapContainer
                ref="refContainer"
                :default-mouse-status="'crosshair'"
              >
                <SuperPointPicker
                  :ref="(el) => (refPointPicker[key] = el)"
                  :disabled="true"
                >
                </SuperPointPicker>
              </SuperMapContainer>
            </SuperMapProvider>
          </template>
          <!-- 富文本 -->
          <span v-else>{{
            (item.formatter &&
              item.formatter(state.dataForm[item.field || ''], item)) ||
            getJsonValue(state.dataForm, item.field)
          }}</span>
        </div>
      </el-descriptions-item>
    </template>
  </el-descriptions>
</template>

<script lang="ts" setup>
import DPlayer from '@/components/videoPlayer/DPlayer.vue';
import { hexToRgba } from '@/utils/GlobalHelper';
import dayjs from 'dayjs';

const props = defineProps<{
  config: IDescriptionsConfig;
}>();

const key = ref(dayjs());
const refPointPicker = ref<any[]>([]);

const state = reactive<{
  /**
   * 表单状态
   */
  dataForm: any;
  /**
   * 供title右侧表单用的查询状态
   */
  queryParams: any;
}>({
  queryParams: {
    ...(props.config.defaultQuerys || {})
  },
  dataForm: { ...(props.config.defaultValue || {}) }
});

function getJsonValue(obj, node) {
  if (!obj) {
    return '-';
  }
  if (!node) {
    return '-';
  }
  const nodes = node.split('.');
  if (nodes.length === 1) {
    return obj[node];
  }
  return getJsonValue(obj[nodes[0]], node.substr(nodes[0].length + 1));
}

const styles = (item) => {
  const tColor =
    typeof item.colorTag === 'function'
      ? item.colorTag(state.dataForm[item.field || ''], item) ||
        getJsonValue(state.dataForm, item.field)
      : item.color;
  return {
    '--el-tag-bg-color': hexToRgba(tColor, 0.2),
    '--el-tag-border-color': hexToRgba(tColor, 0.2),
    '--el-tag-hover-color': tColor,
    '--el-tag-text-color': tColor
  };
};

// 数据解构
const DataDeconstruction = (key, val) => {
  let value: any = val;
  const length = key.split(',');
  length.forEach((item) => {
    value = value[item] ? value[item] : [];
  });
  console.log('value', value);

  if (value && !Array.isArray(value) && value.indexOf(',http') !== -1) {
    value = value.split(',');
  }
  if (Array.isArray(value)) return value.filter((item) => item !== '');
  return value;
};

// a标签下载文件
const downloadFile = (url) => {
  const link = document.createElement('a');
  link.download = 'true';
  link.href = `${url}?response-content-type=application/octet-stream`;
  link.click();
  document.body.removeChild(link);
};

// 设置点位图标
const setposition = (val, key) => {
  // 设置点位图标
  setTimeout(() => {
    if (val) {
      refPointPicker?.value[key].setMarker(
        val.split(',')?.map((item) => parseFloat(item) || item)
      );
      refPointPicker.value[key].zoomTo();
    } else {
      refPointPicker.value[key]?.clear();
    }
  }, 0);
};

watch(
  () => props.config.defaultValue,
  () => {
    state.dataForm = { ...(props.config.defaultValue || {}) };
    key.value = dayjs();
  }
);
</script>

<style lang="scss" scoped>
.el-descriptions {
  margin-bottom: 20px;
}

.itemStyle {
  display: flex;
  align-items: center;
}

// :deep(.el-descriptions__cell){
//   display: flex;
//   align-items: center;
// }

.flex {
  display: flex;
  justify-content: start;
}

.item {
  display: inline-block;
}
</style>
