import{ab as m,j as l}from"./index-r0dFAfgr.js";const p=(t=[],o,u,r=2)=>{const a="总数",f=function(e){const n=/(?=(\B)(\d{3})+$)/g;return e.toString().replace(n,",")},c=t.reduce((e,n)=>e+(parseFloat(n.value)||0)*1,0),i=m(c);return{tooltip:{trigger:"item",formatter:e=>""+e.name+": "+Number(e.value).toFixed(r)+" "+o},legend:{type:"scroll",icon:"circle",orient:"vertical",right:100,top:"center",align:"left",itemGap:10,itemWidth:10,itemHeight:10,symbolKeepAspect:!0,textStyle:{color:"#fff",rich:{name:{align:"left",width:120,fontSize:12,color:l().isDark?"#fff":"#2A2A2A"},value:{align:"left",width:80,fontSize:12,color:"#00ff00"},downRate:{align:"left",fontSize:12,color:"#409EFF"}}},data:t.map(e=>e.name),formatter(e){if(t&&t.length){for(let n=0;n<t.length;n++)if(e===t[n].name)return"{name| "+(t[n].nameAlias||e)+"}{value| "+(t[n].valueAlias||t[n].value)+" "+o+"}{downRate| "+(t[n].scale||"")+"}"}}},title:[{text:"{name|"+a+("("+i.unit+o+")"||"("+i.unit+")")+`}
{val|`+f(i.value.toFixed(r))+"}",top:"center",left:"30%",textAlign:"center",textStyle:{rich:{name:{fontSize:10,fontWeight:"normal",padding:[8,0],align:"center",color:l().isDark?"#fff":"#2A2A2A"},val:{fontSize:16,fontWeight:"bold",color:l().isDark?"#fff":"#2A2A2A"}}}}],series:[{type:"pie",radius:["65%","80%"],center:["30%","50%"],data:t,hoverAnimation:!0,label:{show:!1,formatter:e=>"{icon|●}{name|"+e.name+"}{value|"+f(Number(e.value||"0").toFixed(r))+"}",padding:[0,-100,25,-100],rich:{icon:{fontSize:16},name:{fontSize:14,padding:[0,10,0,4]},value:{fontSize:18,fontWeight:"bold"}}}}]}};export{p as r};
