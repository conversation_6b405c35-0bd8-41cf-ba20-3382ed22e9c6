package org.thingsboard.server.dao.util.imodel.response.tree;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.util.imodel.response.Responsible;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;

import java.util.List;

public class PagedTreeResponse<T> implements Responsible {
    private final PageData<T> pageData;


    public PagedTreeResponse(IPage<T> pagedStore, List<T> treeEntityNodes) {
        pageData = new PageData<>(pagedStore.getTotal(), treeEntityNodes);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Object postProcess(ReturnHelper returnHelper, Object arg) {
        pageData.setData((List<T>) returnHelper.process(pageData.getData(), null));
        return pageData;
    }
}
