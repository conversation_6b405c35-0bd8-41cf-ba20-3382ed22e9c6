package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.ConstructionWorkflow;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProject;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.*;

import java.util.List;

public interface SoProjectService {
    /**
     * 分页条件查询项目
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoProject> findAllConditional(SoProjectPageRequest request);

    /**
     * 保存项目
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoProject save(SoProjectSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoProject entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 分页条件查询项目类型
     *
     * @param request 分页请求
     * @return 项目类型分页
     */
    IPage<SoGeneralType> getTypes(SoGeneralTypePageRequest request);

    /**
     * 保存项目类型
     *
     * @param request 明细
     * @return 保存好的数据
     */
    SoGeneralType saveType(SoGeneralTypeSaveRequest request);

    /**
     * 自动生成编码
     *
     * @param tenantId 客户id
     * @return 自动生成的编码
     */
    String generateCode(String tenantId);

    /**
     * 分页条件查询设备项
     *
     * @param request 分页请求
     * @return 设备项
     */
    IPage<SoDeviceItem> getDevices(SoDeviceItemPageRequest request);

    /**
     * 保存设备项
     *
     * @param request 明细
     * @return 保存好的设备项
     */
    List<SoDeviceItem> saveDevice(List<SoDeviceItemSaveRequest> request);

    /**
     * 编号是否已存在
     *
     * @param code     编号
     * @param tenantId 客户id
     * @param id       自身id（更新时不为null）
     * @return 是否已存在
     */
    boolean isCodeExists(String code, String tenantId, String id);

    /**
     * 项目完成信息
     *
     * @param projectCode 项目编码
     * @param tenantId    客户id
     * @return 完成信息
     */
    List<ConstructionWorkflow> completionInfo(String projectCode, String tenantId);

    /**
     * 判断项目是否可被删除
     * @param id 项目id
     * @return 是否可被删除
     */
    boolean canBeDelete(String id);

}
