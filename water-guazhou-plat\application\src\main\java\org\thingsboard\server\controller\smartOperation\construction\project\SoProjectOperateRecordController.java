package org.thingsboard.server.controller.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectOperateRecord;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectOperateRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectOperateRecordSaveRequest;
import org.thingsboard.server.dao.construction.project.SoProjectOperateRecordService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/projectOperateRecord")
public class SoProjectOperateRecordController extends BaseController {
    @Autowired
    private SoProjectOperateRecordService service;


    @GetMapping
    public IPage<SoProjectOperateRecord> findAllConditional(SoProjectOperateRecordPageRequest request) {
        return service.findAllConditional(request);
    }

    // @PostMapping
    public SoProjectOperateRecord save(@RequestBody SoProjectOperateRecordSaveRequest req) {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoProjectOperateRecordSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}