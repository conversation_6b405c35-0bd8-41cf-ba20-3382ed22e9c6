/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.util.EntityUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;
import org.thingsboard.server.common.data.utils.CharUtil;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 此为访问TSDB数据库获取和push数据的
 */
@Slf4j
public class RestUtil {

//    /**
//     * 获取数据
//     *
//     * @param restUrl
//     * @param body
//     * @return
//     * @throws ThingsboardException
//     */
//    public static List<ResponseTs> doResetPost(String restUrl, Object body) throws ThingsboardException {
//        try {
//            // 处理body适配中文
//            JSONObject jsonObject = JSON.parseObject(JacksonUtil.toString(body));
//            JSONArray array = jsonObject.getJSONArray("queries");
//            // 保存tags
//            Map<String, String> oldTags = new LinkedHashMap<>();
//            for (int i = 0; i < array.size(); i++) {
//                JSONObject tags = array.getJSONObject(i).getJSONObject("tags");
//                String prop = tags.getString("prop");
//                // 当前tag中是否包含中文
//                boolean chinese = CharUtil.isChinese(prop);
//                if (chinese) {
//                    try {
//                        String tagBase64 = new String(Base64.encodeBase64(prop.getBytes()))
//                                .replace("+", "")
//                                .replace("=", "")
//                                .replace("/", "");
//
//                        oldTags.put("prop" + i, tags.getString("prop"));
//                        tags.put("prop", tagBase64);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                        log.error("tag中文转换失败");
//                        tags.put("prop", prop);
//                    }
//                } else {
//                    oldTags.put("prop" + i, tags.getString("prop"));
//                }
//            }
//            StringEntity eStringEntity = null;
//            if (oldTags.containsKey("prop0")) {
//                eStringEntity = new StringEntity(JacksonUtil.toString(jsonObject), "utf-8");
//            } else {
//                eStringEntity = new StringEntity(JacksonUtil.toString(body), "utf-8");
//            }
//            CloseableHttpClient httpClient = HttpClients.createDefault();
//            HttpPost httpPost = new HttpPost(restUrl);
//            eStringEntity.setContentType("application/json");
//            httpPost.setEntity(eStringEntity);
//            HttpResponse response = null;
//            Optional<List<ResponseTs>> resultObj = null;
//            response = httpClient.execute(httpPost);
//            int statusCode = response.getStatusLine().getStatusCode();
//            //阿里云TSDB push数据成功之后返回状态码为200或204
//            if (statusCode == 200 || statusCode == 204) {
//                String result = EntityUtils.toString(response.getEntity(), "UTF-8");
//                ObjectMapper objectMapper = new ObjectMapper();
//                List<ResponseTs> responseTs = objectMapper.readValue(result, new TypeReference<List<ResponseTs>>() {
//                });
//                resultObj = Optional.ofNullable(responseTs);
//                //resultObj = Optional.ofNullable(Jackson.fromJsonString(result, ResponseTs.class));
//            } else {
//                String result = EntityUtils.toString(response.getEntity(), "UTF-8");
//                log.info("调用openTSDB失败，原因为" + result);
//                resultObj = Optional.ofNullable(null);
////                throw new ThingsboardException(DataConstants.RESPONSE_ERROR_GET_DATA_FALIED + result, ThingsboardErrorCode.GENERAL);
//            }
//            httpClient.close();
//            if (!resultObj.isPresent()) {
//                return null;
//            }
//            List<ResponseTs> responseTs = resultObj.get();
//            for (int i = 0; i < responseTs.size(); i++) {
//                String prop = responseTs.get(i).getTags().get("prop");
//                if (!oldTags.containsValue(prop) && Base64.isBase64(prop) && oldTags.containsKey("prop0")) {
//                    responseTs.get(i).getTags().put("prop", oldTags.get("prop" + i));
//                }
//            }
//            return responseTs;
//        } catch (Exception e) {
//            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_GET_DATA_FALIED, ThingsboardErrorCode.GENERAL);
//        }
//    }

//    //向TSDB push数据
//    public static boolean doResetPush(String restUrl, Object body) {
//        StringEntity eStringEntity = new StringEntity(JacksonUtil.toString(body), "utf-8");
//        CloseableHttpClient httpClient = HttpClients.createDefault();
//        HttpPost httpPost = new HttpPost(restUrl);
//        eStringEntity.setContentType("application/json");
//        httpPost.setEntity(eStringEntity);
//        HttpResponse response = null;
//        Optional<ResponseTs> resultObj = null;
//        try {
//            response = httpClient.execute(httpPost);
//            int statusCode = response.getStatusLine().getStatusCode();
//            //阿里云TSDB push数据成功之后返回状态码为200或204
//            if (statusCode == 200 || statusCode == 204) {
//                httpClient.close();
//                return true;
//            } else {
//                httpClient.close();
//                return false;
//            }
//        } catch (IOException e1) {
//            e1.printStackTrace();
//        }
//        return false;
//    }


    //GET 请求
    public static String GetMothod(String restUrl, Map<String, String> headers) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(restUrl);
        headers.forEach(httpGet::setHeader);
        HttpResponse response = null;
        String result = null;
        try {
            response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            //阿里云TSDB push数据成功之后返回状态码为200或204
            if (statusCode == 200 || statusCode == 201) {
                result = EntityUtils.toString(response.getEntity(), "UTF-8");
                httpClient.close();
            } else {
                httpClient.close();
            }
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        return result;
    }


    //GET 请求
    @SneakyThrows
    public static String GetMothod(String restUrl, Map<String, String> headers, Map<String, String> params)  {

        CloseableHttpClient httpClient;
        if (restUrl.startsWith("http")) {
            httpClient = HttpClients.createDefault();
        } else {
            httpClient = new SSLClient();
        }
        BasicHttpParams params1 = new BasicHttpParams();
        String paramsJson = null;
        for (Map.Entry<String, String> map : params.entrySet()) {
            if (paramsJson == null) {
                paramsJson = map.getKey() + "=" + map.getValue();
            } else {
                paramsJson = paramsJson + "&" + map.getKey() + "=" + map.getValue();
            }
            params1.setParameter(map.getKey(), map.getValue());
        }

        if (paramsJson != null) {
                restUrl = restUrl + "?" + paramsJson;
        }
        HttpGet httpGet = new HttpGet(restUrl.trim());
        if (headers != null && headers.size() > 0) {
            headers.forEach(httpGet::setHeader);
        }
        //httpGet.setParams(params1);
        HttpResponse response = null;
        String result = null;
        try {
            response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            //阿里云TSDB push数据成功之后返回状态码为200或204
            if (statusCode == 200 || statusCode == 201) {
                result = EntityUtils.toString(response.getEntity(), "UTF-8");
                result = JSONObject.parse(result).toString();
                httpClient.close();
            } else {
                httpClient.close();
            }
        } catch (IOException e1) {
            log.info("调用RESTAPI失败");
        }
        return result;
    }

    //delete 请求
    public static String DeleteMothod(String restUrl, Map<String, String> headers) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpDelete httpDelete = new HttpDelete(restUrl);
        headers.forEach(httpDelete::setHeader);
        HttpResponse response = null;
        String result = null;
        try {
            response = httpClient.execute(httpDelete);
            int statusCode = response.getStatusLine().getStatusCode();
            //阿里云TSDB push数据成功之后返回状态码为200或204
            if (statusCode == 200 || statusCode == 201) {
                result = EntityUtils.toString(response.getEntity(), "UTF-8");
                httpClient.close();
            } else {
                httpClient.close();
            }
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        return result;
    }

    //post 请求
    public static String PostMothod(String restUrl, Map<String, String> headers, String body) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(restUrl);
        //JSONObject jsonObject = JSONObject.parseObject(body);
        StringEntity stringEntity = new StringEntity(body, ContentType.APPLICATION_JSON);
        //stringEntity.setContentEncoding("UTF-8");
        stringEntity.setContentType("application/json");
        headers.forEach(httpPost::setHeader);
        httpPost.setEntity(stringEntity);
        HttpResponse response = null;
        String result = null;
        try {
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            //阿里云TSDB push数据成功之后返回状态码为200或204
            if (statusCode == 200 || statusCode == 201) {
                result = EntityUtils.toString(response.getEntity(), "UTF-8");
                httpClient.close();
            } else {
                httpClient.close();
            }
        } catch (IOException e1) {
            log.info("调用REST失败！错误："+ e1.getLocalizedMessage());
        }
        return result;
    }


}
