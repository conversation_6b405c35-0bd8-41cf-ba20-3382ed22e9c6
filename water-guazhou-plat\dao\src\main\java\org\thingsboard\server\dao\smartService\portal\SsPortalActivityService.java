package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalActivity;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActivityPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActivitySaveRequest;

public interface SsPortalActivityService {
    SsPortalActivity findById(String id);

    IPage<SsPortalActivity> findAllConditional(SsPortalActivityPageRequest request);

    SsPortalActivity save(SsPortalActivitySaveRequest entity);

    boolean update(SsPortalActivity entity);

    boolean delete(String id);

    /**
     * 切换活动的激活和非激活状态
     * @param req 请求信息
     * @return 是否设置成功
     */
    boolean active(SsPortalActiveRequest req);

}
