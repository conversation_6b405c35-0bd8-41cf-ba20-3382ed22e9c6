package org.thingsboard.server.dao.sql.dma;

import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.dma.DmaWarnCEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@SqlDao
public interface DmaWarnCRepository extends CrudRepository<DmaWarnCEntity, String> {

    List<DmaWarnCEntity> findAllByPidOrderByCreateTimeDesc(String pid);
}
