declare global {
  const AMap: any;

  type NormalOption = {
    id?: string;
    label: string;
    value: any;
    data?: any;
    disabled?: boolean | ((value?: any, row?: any, ...args: any[]) => boolean);
    path?: string[] | number[];
    parent?: string | number;
    children?: NormalOption[];
    icon?: string;
    svgIcon?: any;
    iconifyIcon?: string | ((row?: any, ...args: any[]) => string);
    iconStyle?: any;
    type?: string;
    isLeaf?: boolean;
    /** 单位 表单中值第一个参数为值，第二个参数为表单数据，第三个值为当前表单元素的配置 */
    unit?: string | ((val: any, row: any, config: any) => any);
    // 表单右侧
    append?: string;
  };
  type IQueryPagerParams = { page: number; size: number };
  interface NormalParams {
    [propName: string]: string | string[] | Date[];
  }

  interface NormalOperation {
    label?: string;
    text?: string;
    perm?: boolean | ((row?: any) => boolean);
    type?: string;
    labelHandle?: (row?: any) => string;
    handle?: (row?: any, ...args: any[]) => void;
    component?: any;
    disabled?: boolean;
    disableHandle?: (row?: any) => boolean;
    icon?: string;
    iconHandle?: (row?: any) => string;
    color?: string;
    colorHandle?: (row?: any) => string;
    key?: string;
    loading?: boolean;
    className?: string;
  }

  interface FormRule {
    required?: boolean;
    message?: string;
    validator?: () => void;
    trigger?: string;
  }

  interface FormRules {
    [propName: string]: FormRule[];
  }

  interface SelectOption {
    label: string;
    value: any;
  }

  interface SubParams {
    [propName: string]: boolean | string | string[] | Date[] | SubParams;
  }

  interface DialogConfig {
    visible: boolean;
    title?: string;
    close: () => void;
    currentId: any;
    dialogWidth?: string;
    readonly?: boolean;
    deviceNo?: string;
  }

  interface tree {
    id?: string;
    label?: string;
    type?: number;
    path?: string;
    nodeType?: string;
    children?: any;
  }
  type ISiteName = '' | undefined;
  interface ISITELAYOUT {
    /**
     * 显示告警铃铛
     */
    SHOWBELL?: boolean;
  }
  interface ISiteConfig {
    /**
     * app 下载路径
     */
    appDownloadUrl: string;
    /** 是否显示短信配置 */
    SHORTMESSAGE?: boolean;
    LAYOUT?: ISITELAYOUT;
    /**
     * 网站名称，可据此对页面进行调整
     */
    SITENAME: ISiteName;
    /** 系统版本 */
    version: string;
    /** 接口服务 */
    apiURL: string;
    /** bim 3D 接口路径 */
    bimUrl: string;
    /** 组态接口 */
    scadaURL: string;
    /** rtsp视频 */
    rtspUrl: string;
    /** 营收接口 */
    yinshouUrl: string;
    /** 帆软报表 */
    fineReportURL: string;
    /** radar */
    radarImgUrl: string;
    /** 视频接口 */
    videoURL: string;
    /** 图片上传路径 */
    imgUrl: string;
    /** 登录页标题 */
    TITLE: string;
    // 视频转码地址
    videoUploadUrl: string;
    /** 登录页副标题 */
    SUBTITLE: string;
    GIS_CONFIG: IGISCONFIG;
    /**
     * 一张图配置
     */
    ONE_MAP_CONFIG: IOneMapConfig;
    WEATHER_CONFIG: IWEATHER_CONFIG;
    OPERATION_OVERVIEW: IOPERATION_OVERVIEW;
    SMART_DECISION_CONFIG: ISMART_DECISION_CONFIG;
    SMART_SWAGE_CRAFT_CONFIG: ISMART_SWAGE_CRAFT_CONFIG;
    /**
     * 登录页配置
     */
    LOGIN_CONFIG: ILOGIN_CONFIG;
    /**
     * 污水厂配置
     */
    SEWAGE_CONFIG: ISEWAGE_CONFIG;
    HUADE?: {
      stations?: {
        dongshuichang;
        xishan: string;
        xingrong: string;
        xiangyang: string;
        xiaojiacun: string;
      };
      overview?: {
        style: {
          left: string;
          top: string;
        };
        text: string;
        type: string;
        id: string;
        property: string;
      }[];
    };
    JINZHOU?: {
      stations?: {
        FUXINJIAYUANXIAOQUBENGZHAN?: IJinZhouZhandianProperties;
        JUNQUJIAYAZHAN?: IJinZhouZhandianProperties;
        YIZHOUYIPIN?: IJinZhouZhandianProperties;
        JINSHUIBENGFANG?: IJinZhouZhandianProperties;
        LAOGONGDIANJU?: IJinZhouZhandianProperties;
        JINSHUYUAN?: IJinZhouZhandianProperties;
      };
    };
    WUDANG?: {
      stations?: {
        GAOXUEBENGFANG?: IJinZhouZhandianProperties;
        ALIBENGFANG?: IJinZhouZhandianProperties;
      };
    };
    SONGXIAN?: {
      stations?: {
        WUSHUISHUIZHI?: IJinZhouZhandianProperties;
        WUSHUILIULIANG?: IJinZhouZhandianProperties;
        WUSHUICHANG?: IJinZhouZhandianProperties;
      };
      CHILIGUIMO?: number; // 污水处理规模
    };
    SUPERMAP_CONFIG?: {
      iserverurl?: string;
    };
    FORM: {
      /**
       * 表单里面的地图的默认配置，优先级： props>表单地图默认配置>地图默认配置
       */
      GIS_CONFIG: IFormGisConfig;
    };
    //VR路径
    VR?: string;
  }
  type IFormGisConfig = {
    defaultCenter?: number[];
    zoom?: number;
    defaultBaseMap?:
      | 'vec_c'
      | 'vec_w'
      | 'img_c'
      | 'img_w'
      | 'ter_c'
      | 'ter_w'
      | 'ibo_c'
      | 'ibo_w'
      | 'arcgis_imagery';
    defaultFilter?: string;
    defaultFilterColor?: any;
    showPoi?: boolean;
    minZoom?: number;
    maxZoom?: number;
    disableMove?: boolean;
  };
  type IJinZhouZhandianProperties = {
    id?: string;
    id1?: string;
    deviceIds?: Record<string, any>;
  };
  type IGISCONFIG = {
    /** gis接口 */
    gisApi: string;
    /** gis apikey */
    gisApiKey: string;
    /**
     * gis的sdk服务器地址
     */
    gisSDK: string;
    /**
     * gis 地图打开时默认加载的地图
     * 可选值：
     * 'vec_c' 矢量底图
     * 'vec_w' 矢量底图
     * 'img_c' 影像底图
     * 'img_w' 影像底图
     * 'ter_c' 地形晕渲
     * 'ter_w' 地形晕渲
     * 'ibo_c' 全球境界
     * 'ibo_w' 全球境界
     * 'arcgis_imagery' ArcGIS World Imagery
     * 默认值：'vec_w',
     */
    gisDefaultBaseMap:
      | 'vec_c'
      | 'vec_w'
      | 'img_c'
      | 'img_w'
      | 'ter_c'
      | 'ter_w'
      | 'ibo_c'
      | 'ibo_w'
      | 'arcgis_imagery';
    /**
     * gis 地图默认加载的Poi服务
     * 可选值：
     * 'cva_c' 矢量注记
     * 'cva_w' 矢量注记
     * 'cia_c' 影像注记
     * 'cia_w' 影像注记
     * 'cta_c' 地形注记
     * 'cta_w' 地形注记
     * 'eva_c' 矢量英文注记
     * 'eva_w' 矢量英文注记
     * 'eia_c' 影像英文注记
     * 'eia_w' 影像英文注记
     * 默认值： cva_w
     */
    gisDefaultPoi:
      | 'cva_c'
      | 'cva_w'
      | 'cia_c'
      | 'cia_w'
      | 'cta_c'
      | 'cta_w'
      | 'eva_c'
      | 'eva_w'
      | 'eia_c'
      | 'eia_w'
      | '';
    /** 深色底图的滤镜颜色 默认值为：rgba(255, 255, 255, 0.0) */
    gisDefaultBaseMapFilterColor: string;
    // 地图默认中心
    gisDefaultCenter: number[];
    /**
     * 默认地图缩放级别1-18
     */
    gisDefaultZoom: number;
    gisMinZoom?: number;
    gisMaxZoom?: number;
    /**
     * 禁用地图移动
     */
    gisDisableMove?: boolean;
    /**
     * 是否显示区域边界
     */
    gisShowBoundary: boolean;
    /**
     * 显示的区域边界名称，比如北碚区就填北碚区
     */
    gisBoundaryName: string;
    /**
     * 显示区域边界的文件/THREEJS/geojson/xxx_boundary.geojson
     */
    gisBoundaryFileUrl: string;
    /** gis 天地图token */
    gisTdtToken: string;
    /** gis服务基础路径 */
    gisService: string;
    /**
     * 合并后的管线服务
     */
    gisDissolvedService: string;
    /** gis服务代理路径 */
    gisProxyService: string;
    /** gis图层信息 */
    gisPipeDataService: string;
    /** gis动态图层地信息，支持控制子图层显隐 */
    gisPipeDynamicService: string;
    /** gis爆管分析 */
    gisBurstGPService: string;
    /** gis连通性分析 */
    gisConnectGPService: string;
    /** gis路径分析 */
    gisPathAnalysGPService: string;
    /** gis关阀分析 */
    gisShutValveAnalysGPService: string;
    /** gis二次关阀分析 */
    gisExtendShutValveAnalysGPService: string;
    /** gis放大镜 */
    gisFangDaGPService: string;
    /** gis要素服务 */
    gisGeometryService: string;
    /** gis模板打印 */
    gisPrintTemplatePath: string;
    /** gis打印GP服务 */
    gisPrintGPService: string;
    /** gis打印工具GP服务 */
    gisPrintingToolsGPService: string;
    /** gis工具服务 */
    gisUtilitiesService: string;
    /** gis管网要素服务 */
    gisPipeFeatureServiceFeatureServer: string;
    /** gis管网要素服务（MapServer */
    gisPipeFeatureServiceMapServer: string;
    /** gis地图上是否显示报警信息 */
    gisShowAlarms?: boolean;
    /** 是否需要gis的保存方案功能 */
    gisSaveScheme?: boolean;
  };
  type ISMART_DECISION_CONFIG_Bar = {
    text: string;
    name: string;
    id: string;
    orgId: string;
    img: string;
  };
  type IOPERATION_OVERVIEW_Bar = ISMART_DECISION_CONFIG_Bar & {
    title?: string;
  };
  type ISMART_DECISION_CONFIG_D3MAPCONFIG = {
    /**
     * 地界数据
     */
    boundaryGeoJsonUrl: string;
    /**
     * 管线数据
     */
    linesGeoJsonUrl: string;
    /**
     * 监测点
     */
    pointsGeoJsonUrl: string;
    /**
     * 贴图路径
     */
    textureUrl: string;
    /**
     * 贴图缩放
     */
    textureRepeat: [number, number];
    /**
     * 贴图偏移
     */
    textureOffset: [number, number];
    /**
     * 摄像头位置
     */
    cameraPosition: [number, number, number];
    /**
     * 地图中心点
     */
    mapCenter: [number, number];
    /**
     * 地图突起高度
     */
    height: number;
  };
  /**
   * 运营总览大屏
   */
  type IOPERATION_OVERVIEW = {
    bars: IOPERATION_OVERVIEW_Bar[];
    mapConfig: IFormGisConfig;
  };
  /**
   * 智慧决策配置
   */
  interface ISMART_DECISION_CONFIG {
    title: string;
    bars: ISMART_DECISION_CONFIG_Bar[];
    /**
     * 三维地图静态资源配置
     */
    d3MapConfig: Partial<ISMART_DECISION_CONFIG_D3MAPCONFIG>;
  }
  interface ISMART_SWAGE_CRAFT_CONFIG {
    title: string;
  }
  interface IEsriConfig {
    modules: Record<string, any>;
  }
  interface IOneMapConfig {
    /**
     * 禁用鼠标悬浮弹窗
     */
    disableHoverPopup: boolean;
    /**
     * 隐藏标签下的文本
     */
    hideTextSymbol: boolean;
    /**
     * 是否把站点文本在旁边展示
     */
    textSymbolAside: boolean;
    /**
     * 隐藏的下拉菜单 'ssjk'|'sbzc'|'rycl'|'ywlc'
     */
    hiddenMenus: string[];
    /**
     * 隐藏的子菜单
     */
    hiddenSubMenus: string[];
  }
  interface IWEATHER_CONFIG {
    KEY: string;
  }
  interface ILOGIN_CONFIG {
    SHOWFOOTER: boolean;
    /**
     * 显示版权信息
     */
    SHOWCOPYRIGHT: boolean;
    COPYRIGHT: string;
    /**
     * 显示插件
     */
    SHOWPLUGINS: boolean;
    /**
     * 显示二维码
     */
    SHOWQRCODE: boolean;
    /**
     * app上传时的key
     */
    APPKEY: string;
    /**
     * 微信公众号链接
     */
    WXPUBLICACCOUNTURL: string;
  }
  /**
   * 污水厂配置
   */
  interface ISEWAGE_CONFIG {
    sewageName: string;
    content: string;
    /**
     * 大屏配置
     */
    largeScreen: {
      /**
       * 标题
       */
      title: string;
    };
  }
  interface Window {
    /** 网站配置,配置文件在Public/config.*.js */
    SITE_CONFIG: ISiteConfig;
    store: IPiniaStore;
    // 打印
    getLodop: any;
    /** ***************************cesium相关全局配置 */
    /**
     * 静态资源的基础路径
     */
    CESIUM_BASE_URL: string;
    getCLodop: any;
  }
  type IGisOpeateLogMessageProperties = {
    type: 'update' | 'add' | 'delete';
    /**
     * 图层
     */
    layer: string;
    message: string;
    // updates: Partial<{
    //   /**
    //    * 修改前的值
    //    * （对单一目标进行处理时用）
    //    */
    //   before: any
    //   /**
    //    * 修改后的值
    //    * （对单一目标进行处理时用）
    //    */
    //   after: any
    //   /**
    //    * 修改的所有要素的编号
    //    * （对多个目标进行处理时用）
    //    */
    //   ids: string[]

    //   /**
    //    * 修改的要素的编号
    //    * （对单一目标进行处理时用）
    //    */
    //   id: string
    //   /**
    //    * 修改的字段
    //    * （对多个目标进行处理时用）
    //    */
    //   field: string
    //   /**
    //    * 修改后的值
    //    * （对多个目标进行处理时用）
    //    */
    //   value: string
    // }>[]
  };

  interface DPlayerIns {
    /** 视频自动播放 */
    autoplay?: boolean;
    /** 主题色 */
    theme?: string;
    /** 视频循环播放 */
    loop?: boolean;
    /** 语言 */
    lang?: 'zh-cn' | 'en' | 'zh-tw';
    /** 开启截图，如果开启，视频和视频封面需要允许跨域 */
    screenshot?: boolean;
    /** 开启热键，支持快进、快退、音量控制、播放暂停 */
    hotkey?: boolean;
    /** 视频预加载 */
    preload?: 'none' | 'metadata' | 'auto';
    /** 在左上角展示一个 logo，你可以通过 CSS 调整它的大小和位置 */
    logo?: string;
    /** 默认音量 0-1 */
    volume?: number;
    /** 阻止多个播放器同时播放 */
    mutex?: boolean;
    /** 开启直播模式 */
    live?: boolean;
    /** 阻止点击播放器时候自动切换播放/暂停 */
    preventClickToggle?: boolean;
    /** 视频信息 */
    video: {
      /** 视频链接 */
      url: string;
      /** 视频封面 */
      pic?: string;
      /** 视频缩略图，可以使用 DPlayer-thumbnails (opens new window)生成 */
      thumbnails?: string;
      /** 视频类型 */
      type?:
        | 'auto'
        | 'hls'
        | 'flv'
        | 'dash'
        | 'webtorrent'
        | 'normal'
        | 'customHls'
        | 'customFlv';
    };
  }
}

export {};
