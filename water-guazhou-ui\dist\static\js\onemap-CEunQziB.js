import{z as e}from"./index-r0dFAfgr.js";const r=t=>e({url:"/istar/api/water/source/getWaterSupplyDetail",method:"get",params:t}),i=t=>e({url:"/istar/api/production/waterPlant/getWaterSupplyInfo",method:"get",params:t}),o=t=>e({url:"/istar/api/production/waterPlant/gis/getWaterSupplyDetail",method:"get",params:t}),s=t=>e({url:"/istar/api/production/waterPlant/gis/getWaterInfo",method:"get",params:t}),n=t=>e({url:"/istar/api/flowMonitoringStation/getList",method:"get",params:t}),u=t=>e({url:"/istar/api/flowMonitoringStation/gis/getDataDetail",method:"get",params:t}),l=t=>e({url:"/istar/api/pressureMonitoringStation/getList",method:"get",params:t}),g=t=>e({url:"/istar/api/valveMonitoringStation/getList",method:"get",params:t}),p=t=>e({url:"/istar/api/pressureMonitoringStation/gis/getDataDetail",method:"get",params:t}),S=t=>e({url:"/istar/api/valveMonitoringStation/gis/getDataDetail",method:"get",params:t}),c=t=>e({url:"/istar/api/waterQualityStation/getList",method:"get",params:t}),m=t=>e({url:"/istar/api/waterQualityStation/gis/getDataDetail",method:"get",params:t}),D=t=>e({url:"/istar/api/boosterPumpStation/getWaterSupplyDetail",method:"get",params:t}),d=t=>e({url:"/istar/api/boosterPumpStation/gis/getWaterInfo",method:"get",params:t}),h=t=>e({url:"/istar/api/bigUser/getList",method:"get",params:t}),G=t=>e({url:"/istar/api/bigUser/gis/getDataDetail",method:"get",params:t}),y=t=>e({url:"/api/sp/circuitTask/statusCount",method:"get",params:t});export{n as G,l as a,g as b,i as c,c as d,G as e,u as f,p as g,S as h,D as i,d as j,o as k,m as l,r as m,s as n,y as o,h as p};
