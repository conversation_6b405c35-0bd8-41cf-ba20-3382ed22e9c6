<!-- 设备出库 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <SLDrawer ref="refForm" :config="addOrUpdateConfig"></SLDrawer>
    <SLDrawer ref="refFormEquipment" :config="addEquipment"></SLDrawer>
    <!-- 详情 -->
    <SLDrawer ref="detailForm" :config="detailConfig"></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import {
  getDeviceStorageJournalEq,
  getDeviceStorageJournalSerch
} from '@/api/equipment_assets/ledgerManagement';
import useGlobal from '@/hooks/global/useGlobal';
import {
  getStoreOutRecordSerch,
  getStoreOutRecordDetailSerch,
  postCheckout,
  getstoreSerch
} from '@/api/equipment_assets/equipmentOutStock';
import { formatDate } from '@/utils/DateFormatter';
import { exportType, exportStatus } from '../../equipmentAssetsData';
import { uniqueFunc, deduplicationQuantity } from '@/utils/GlobalHelper';

const { $btnPerms } = useGlobal();

const refSearch = ref<ICardSearchIns>();

const refForm = ref<ISLDrawerIns>();

const refFormEquipment = ref<ISLDrawerIns>();

const detailForm = ref<ISLDrawerIns>();

const chosen = ref([]);

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '出库单编码', field: 'code', type: 'input', labelWidth: '90px' },
    { label: '出库单标题', field: 'title', type: 'input', labelWidth: '90px' },
    {
      label: '仓库名称',
      field: 'storehouseId',
      type: 'select',
      options: computed(() => data.storeList) as any
    },
    { label: '出库时间', field: 'outtime', type: 'daterange' },
    { label: '创建时间', field: 'createtime', type: 'daterange' },
    {
      type: 'select',
      label: '出库状态',
      field: 'isOut',
      options: exportStatus,
      onChange: () => refreshData()
    },
    {
      type: 'select',
      label: '出库类型',
      field: 'type',
      options: exportType,
      onChange: () => refreshData()
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '出库单标题', prop: 'title' },
    { label: '出库单编码', prop: 'code' },
    { label: '仓库名称', prop: 'storehouseName' },
    {
      label: '出库时间',
      prop: 'outTime',
      formatter: (row) => formatDate(row.outTime, 'YYYY-MM-DD') || ''
    },
    { label: '领用部门', prop: 'managerDepartmentName' },
    { label: '领用人', prop: 'receiveUserName' },
    { label: '经办人', prop: 'managerName' },
    { label: '创建人', prop: 'creatorName' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD')
    },
    {
      label: '出库类型',
      prop: 'type',
      formatter: (row) =>
        exportType.find((item: any) => item.value === row.type)?.label
    }
  ],
  operationWidth: '160px',
  operations: [
    {
      type: 'success',
      text: '出库',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.EXPORT,
      click: (row) => clickEdit(row)
    },
    {
      type: 'primary',
      color: '#4195f0',
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.DETAIL,
      click: (row) => openDetails(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '',
  labelWidth: '100px',
  width: '1200px',
  submit: (val: any) => {
    const params = {
      storeOutId: val.id,
      checkouts: data.outStock.map((item) => item.id)
    };
    postCheckout(params)
      .then(() => {
        refreshData();
        ElMessage.success('出库成功');
        refForm.value?.closeDrawer();
      })
      .catch((error) => {
        ElMessage.warning(error);
      });
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '出库单编码',
          field: 'code',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '出库单标题',
          field: 'title',
          disabled: true
        },
        {
          xl: 8,
          type: 'select',
          label: '仓库名称',
          field: 'storehouseName',
          readonly: true
        },
        {
          xl: 8,
          type: 'input',
          label: '出库时间',
          field: 'outTime',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '领用人',
          field: 'receiveUserName',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '领用部门',
          field: 'managerDepartmentName',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '经办人',
          field: 'managerName',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '创建人',
          field: 'creatorName',
          disabled: true
        },
        {
          xl: 8,
          type: 'date',
          label: '创建时间',
          field: 'createTime',
          readonly: true
        },
        {
          xl: 18,
          type: 'textarea',
          label: '备注',
          field: 'remark',
          disabled: true
        },
        {
          type: 'divider',
          text: '出库单设备'
        },
        {
          type: 'table',
          field: 'drive',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              {
                label: '设备编码',
                prop: 'serialId'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '规格/型号',
                prop: 'model'
              },
              {
                label: '所属大类',
                prop: 'topType'
              },
              {
                label: '所属类别',
                prop: 'type'
              },
              {
                label: '出库数量',
                prop: 'num'
              },
              {
                label: '计算单位',
                prop: 'unit'
              }
            ],
            operations: [
              {
                text: '出库',
                type: 'success',
                icon: ICONS.SEND,
                perm: $btnPerms('RoleManageDelete'),
                click: (row) => {
                  const val = deduplicationQuantity(data.outStock, 'serialId');
                  console.log(val.get(row.serialId));

                  if (val.get(row.serialId) >= row.num) {
                    ElMessage.warning('勾选的数量多于或等于规定出库的数量');
                    return;
                  }
                  chosen.value = [];
                  data.getoutDeviceValue({
                    serialId: row.serialId,
                    storeId: data.outboundOrder.storehouseId || '',
                    shelvesId: row.shelvesId || ''
                  });
                  addEquipment.defaultValue = {
                    serialId: row.serialId,
                    storeId: data.outboundOrder.storehouseId || '',
                    shelvesId: row.shelvesId || '',
                    num: row.num - (val.get(row.serialId) || 0)
                  };
                  refFormEquipment.value?.openDrawer();
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        },
        {
          type: 'divider',
          text: '出库设备列表'
        },
        {
          type: 'table',
          field: 'outstock',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.outStock) as any,
            columns: [
              {
                label: '标签编码',
                prop: 'deviceLabelCode'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '设备型号',
                prop: 'model'
              },
              {
                label: '所属大类',
                prop: 'topType'
              },
              {
                label: '所属类别',
                prop: 'type'
              },
              {
                label: '供应商',
                prop: 'supplierName'
              },
              {
                label: '过期时间',
                prop: 'key8'
              },
              {
                label: '仓库编号',
                prop: 'storehouseName'
              },
              {
                label: '仓库名称',
                prop: 'storehouseName'
              },
              {
                label: '货架名称',
                prop: 'shelvesName'
              }
              // {
              //   label: '状态',
              //   prop: 'isCheckout',
              //   formatter: row => (row.isCheckout ? '已出库' : '未出库')
              // }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

// 设备选择
const addEquipment = reactive<IDrawerConfig>({
  title: '设备选择',
  labelWidth: '130px',
  submit: (params: any, status?: boolean) => {
    if (chosen.value.length > params.num) {
      ElMessage.warning('勾选的数量多于规定出库的数量');
      return;
    }
    delete params.device;
    if (status) {
      data.getoutDeviceValue({ ...params });
    } else {
      data.outStock = [...data.outStock, ...chosen.value];
      data.outStock = uniqueFunc(data.outStock, 'id');
      refFormEquipment.value?.closeDrawer();
    }
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 12,
          type: 'input',
          label: '设备编码',
          field: 'serialId'
        },
        {
          type: 'table',
          field: 'device',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.outDeviceValue) as any,
            selectList: [],
            handleSelectChange: (val) => {
              chosen.value = val;
            },
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '查询',
                        perm: true,
                        click: () => {
                          refFormEquipment.value?.Submit(true);
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '标签编码',
                prop: 'deviceLabelCode'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '设备型号',
                prop: 'model'
              },
              {
                label: '所属大类',
                prop: 'topType'
              },
              {
                label: '所属类别',
                prop: 'type'
              },
              {
                label: '供应商',
                prop: 'supplierName'
              },
              {
                label: '仓库名称',
                prop: 'storehouseName'
              },
              {
                label: '仓库名称',
                prop: 'storehouseName'
              },
              {
                label: '货架名称',
                prop: 'shelvesName'
              },
              {
                label: '状态',
                prop: 'isCheckout',
                formatter: (row) => (row.isCheckout ? '已出库' : '未出库')
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  labelWidth: '100px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '出库单编码',
          field: 'code',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '出库单标题',
          field: 'title',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '仓库名称',
          field: 'storehouseName',
          disabled: true
        },
        {
          xl: 8,
          type: 'date',
          label: '出库时间',
          field: 'outTime',
          readonly: true
        },
        {
          xl: 8,
          type: 'input',
          label: '领用人',
          field: 'receiveUserName',
          disabled: true
        },
        {
          xl: 8,
          type: 'input',
          label: '领用部门',
          field: 'managerDepartmentName',
          disabled: true
        },
        {
          xl: 8,
          type: 'select',
          label: '经办人',
          field: 'managerName',
          readonly: true
        },
        {
          xl: 8,
          type: 'input',
          label: '创建人',
          field: 'creatorName',
          disabled: true
        },
        {
          xl: 8,
          type: 'date',
          label: '创建时间',
          field: 'createTime',
          readonly: true
        },
        {
          xl: 18,
          type: 'textarea',
          label: '备注',
          field: 'remark',
          disabled: true
        },
        {
          type: 'divider',
          text: '出库单设备'
        },
        {
          type: 'table',
          field: 'drive',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              {
                label: '设备编码',
                prop: 'serialId'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '规格/型号',
                prop: 'model'
              },
              {
                label: '所属大类',
                prop: 'topType'
              },
              {
                label: '所属类别',
                prop: 'type'
              },
              {
                label: '出库数量',
                prop: 'num'
              },
              {
                label: '单位',
                prop: 'unit'
              }
            ],
            pagination: {
              hide: true
            }
          }
        },
        {
          type: 'divider',
          text: '出库设备列表'
        },
        {
          type: 'table',
          field: 'outstock',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.outStock) as any,
            columns: [
              {
                label: '标签编码',
                prop: 'deviceLabelCode'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '设备型号',
                prop: 'model'
              },
              {
                label: '所属大类',
                prop: 'topType'
              },
              {
                label: '所属类别',
                prop: 'type'
              },
              {
                label: '供应商',
                prop: 'supplierName'
              },
              {
                label: '过期时间',
                prop: 'key8'
              },
              {
                label: '仓库编号',
                prop: 'storehouseName'
              },
              {
                label: '仓库名称',
                prop: 'storehouseName'
              },
              {
                label: '货架名称',
                prop: 'shelvesName'
              }
              // {
              //   label: '状态',
              //   prop: 'isCheckout',
              //   formatter: row => (row.isCheckout ? '已出库' : '未出库')
              // }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

const openDetails = (row: { [x: string]: any }) => {
  const value = { ...row };
  for (const i in value) {
    if (value[i] === undefined || value[i] === null) value[i] = ' ';
  }
  detailConfig.title = '设备出库详情';
  detailConfig.defaultValue = { ...(value || {}) };
  detailForm.value?.openDrawer();
  const params = { page: 1, size: 99999, mainId: value.id };
  getStoreOutRecordDetailSerch(params).then((res) => {
    data.selectList = res.data.data.data || [];
  });
  data.outStock = [];
  data.getOutStock({ storeOutId: value.id });
};

const clickEdit = (row: { [x: string]: any }) => {
  const val = { ...row };
  for (const i in val) {
    if (val[i] === undefined || val[i] === null) val[i] = ' ';
  }
  addOrUpdateConfig.title = '设备出库';
  addOrUpdateConfig.defaultValue = { ...val };
  refForm.value?.openDrawer();
  data.outboundOrder = val;
  const params = { page: 1, size: 99999, mainId: val.id };
  getStoreOutRecordDetailSerch(params).then((res) => {
    data.selectList = res.data.data.data || [];
  });
  data.outStock = [];
  data.getOutStock({ storeOutId: val.id });
};

const data = reactive({
  // 设备列表
  deviceValue: [] as any[],
  // 仓库
  storeList: [],
  // 选中的出库单
  outboundOrder: {} as any,
  // 选中的出库设备
  selectList: [] as any[],
  outDeviceValue: [] as any[],
  outStock: [] as any,
  getDevice: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      ...param
    };
    getStoreOutRecordDetailSerch(params).then((res) => {
      data.deviceValue = res.data.data.data || [];
    });
  },
  getoutDeviceValue: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      inStoreOnly: true,
      ...param
    };
    getDeviceStorageJournalEq(params).then((res) => {
      const value = res.data.data.data || [];
      let value2: any[] = [];
      value.forEach((element: any) => {
        value2 = [...value2, ...element.restDeviceInfos];
      });
      data.outDeviceValue = value2;
    });
  },
  getOutStock: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      scrapped: true,
      ...param
    };
    getDeviceStorageJournalSerch(params).then((res) => {
      data.outStock = res.data.data.data || [];
    });
  },
  // 获取仓库
  getstoreSerchValue: () => {
    const params = { page: 1, size: 99999 };
    getstoreSerch(params).then((res) => {
      const value = res.data.data.data || [];
      data.storeList = value.map((item) => {
        return { label: item.name, value: item.id };
      });
    });
  }
});

const refreshData = async () => {
  const params: {
    size: number | undefined;
    page: number | undefined;
    fromTime?: string;
    toTime?: string;
    outTimeFrom?: string;
    outTimeTo?: string;
    createtime?: string;
    outtime?: string;
  } = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  };
  if (params.createtime && params.createtime?.length > 1) {
    params.fromTime = (refSearch.value?.queryParams as any).createtime[0] || '';
    params.toTime = (refSearch.value?.queryParams as any).createtime[1] || '';
  }
  if (params.outtime && params.outtime?.length > 1) {
    params.outTimeFrom = (refSearch.value?.queryParams as any).outtime[0] || '';
    params.outTimeTo = (refSearch.value?.queryParams as any).outtime[1] || '';
  }
  delete params.createtime;
  delete params.outtime;
  getStoreOutRecordSerch(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  data.getDevice();
  data.getstoreSerchValue();
});
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
