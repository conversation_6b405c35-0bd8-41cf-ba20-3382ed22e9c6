<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.plan.CircuitTaskReportMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->report.id,
                           type,
                           task_code,
                           report.name,
                           device_type,
                           is_settle,
                           settle_time,
                           is_fallback,
                           fallback_time,
                           image,
                           video,
                           audio,
                           file,
                           (report.is_settle and (task.is_need_feedback = false or report.is_fallback)) is_complete,
                           report.tenant_id<!--@sql from sm_circuit_task_report report, sm_circuit_task task-->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitTaskReport">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="task_code" property="taskCode"/>
        <result column="name" property="name"/>
        <result column="device_type" property="deviceType"/>
        <result column="is_settle" property="isSettle"/>
        <result column="settle_time" property="settleTime"/>
        <result column="is_fallback" property="isFallback"/>
        <result column="fallback_time" property="fallbackTime"/>
        <result column="image" property="image"/>
        <result column="video" property="video"/>
        <result column="audio" property="audio"/>
        <result column="file" property="file"/>
        <result column="is_complete" property="isComplete"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_circuit_task_report report
                 left join sm_circuit_task task
                           on report.task_code = task.code and report.tenant_id = task.tenant_id
        <where>
            task_code = #{taskCode}
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
        </where>
    </select>

    <update id="update">
        update sm_circuit_task_report
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="taskCode != null">
                task_code = #{taskCode},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="deviceType != null">
                device_type = #{deviceType},
            </if>
            <if test="isSettle != null">
                is_settle = #{isSettle},
            </if>
            <if test="settleTime != null">
                settle_time = #{settleTime},
            </if>
            <if test="isFallback != null">
                is_fallback = #{isFallback},
            </if>
            <if test="fallbackTime != null">
                fallback_time = #{fallbackTime},
            </if>
            <if test="image != null">
                image = #{image},
            </if>
            <if test="video != null">
                video = #{video},
            </if>
            <if test="audio != null">
                audio = #{audio},
            </if>
            <if test="file != null">
                file = #{file},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO sm_circuit_task_report(id,
                                           type,
                                           task_code,
                                           name,
                                           device_type,
                                           is_settle,
                                           settle_time,
                                           is_fallback,
                                           fallback_time,
                                           image,
                                           video,
                                           audio,
                                           file,
                                           tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.type},
             #{element.taskCode},
             #{element.name},
             #{element.deviceType},
             #{element.isSettle},
             #{element.settleTime},
             #{element.isFallback},
             #{element.fallbackTime},
             #{element.image},
             #{element.video},
             #{element.audio},
             #{element.file},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="present">
        update sm_circuit_task_report
        set is_settle   = true,
            settle_time = now()
        where id = #{reportId}
    </update>

    <update id="fallback">
        update sm_circuit_task_report
        set is_fallback   = true,
            fallback_time = now(),
            image         = #{image},
            video         = #{video},
            audio         = #{audio}
        where id = #{reportId}
    </update>

    <select id="findByPoint" resultMap="BaseResultMap">
        select *
        from sm_circuit_task_report
        where task_code = #{taskCode}
          and device_type = #{pointId}
          and tenant_id = #{tenantId}
    </select>

    <select id="arrivalRate" resultType="java.lang.Double">
        SELECT
        CASE
        WHEN (SELECT COUNT(1) FROM sm_circuit_task_report) = 0 THEN '0.0000'
        ELSE ROUND(((SELECT COUNT(1)::float FROM sm_circuit_task_report WHERE is_settle = true) /
        (SELECT COUNT(1)::float FROM sm_circuit_task_report))::numeric, 4)::text
        END
    </select>

    <select id="feedbackRate" resultType="java.lang.Double">
        SELECT
          CASE
            WHEN (SELECT COUNT(1) FROM sm_circuit_task_report) = 0 THEN '0.0000'
            ELSE ROUND(((SELECT COUNT(1)::float FROM sm_circuit_task_report WHERE is_fallback = true) /
                  (SELECT COUNT(1)::float FROM sm_circuit_task_report))::numeric, 4)::text
          END
    </select>
</mapper>