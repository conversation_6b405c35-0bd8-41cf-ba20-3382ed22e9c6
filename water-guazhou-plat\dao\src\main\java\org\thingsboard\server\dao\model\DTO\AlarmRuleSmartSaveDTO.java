package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleSmart;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleUser;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 报警Version2 报警规则
 */
@Data
public class AlarmRuleSmartSaveDTO implements Serializable {

    private AlarmRuleSmart alarmRuleSmart;

    private List<AlarmRuleStationParamDTO> stationParamList = new ArrayList<>();

    private List<AlarmRuleUser> msgList = new ArrayList<>();

    private List<AlarmRuleUser> appList = new ArrayList<>();

}
