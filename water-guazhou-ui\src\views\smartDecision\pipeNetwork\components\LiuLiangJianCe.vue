<template>
  <div class="card">
    <div class="title card">
      <span>监测点名称</span>
      <span>正瞬值</span>
      <span>负瞬值</span>
      <span>正累计</span>
      <span>负累计</span>
      <span>读取时间</span>
    </div>

    <VueScroll :data="data" :class-option="optionHover" class="warp">
      <div>
        <li v-for="(item, i) in data" :key="i" class="title">
          <span>{{ item.name }}</span>
          <span>{{ item.value }}</span>
          <span>{{ item.date }}</span>
          <span>{{ item.name }}</span>
          <span>{{ item.value }}</span>
          <span>{{ item.date }}</span>
        </li>
      </div>
    </VueScroll>
  </div>
</template>

<script lang="ts" setup>
const data: any[] = [
  { name: 'sds', value: '3434', date: '2022-11-22' },
  { name: 'sds', value: '3434', date: '2022-11-22' },
  { name: 'sds', value: '3434', date: '2022-11-22' }
];

const optionHover = {
  step: 0.2
};
</script>

<style lang="scss" scoped>
.card {
  margin-top: 5px;
  overflow: hidden;
}

.title {
  display: flex;
  span {
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  span:nth-child(1) {
    width: 100px;
  }
  span:nth-child(2) {
    width: 70px;
  }
  span:nth-child(3) {
    width: 70px;
  }
  span:nth-child(4) {
    width: 70px;
  }
  span:nth-child(5) {
    width: 70px;
  }
  span:nth-child(6) {
    width: 95px;
    text-align: left;
  }
}
.warp {
  margin-top: 15px;
  height: 200px;
  overflow: hidden;
  li {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
  }
  li:nth-child(2n) {
    background-color: rgb(26, 69, 173);
  }
}
</style>
