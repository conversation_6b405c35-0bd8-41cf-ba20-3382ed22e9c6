import{d as se,c as o,r as M,a8 as q,s as le,o as ne,x as c,y as H,g as S,n as P,q as a,F as s,p as l,aB as oe,aJ as ie,h as re,G as U,bh as k,i as x,eG as ce,eH as de,eI as ue,cz as me,aK as pe,aL as fe,I as ve,c2 as _e,J as ge,K as ye,bz as he,cE as be,bU as we,bW as ke,b7 as xe,C as Te}from"./index-r0dFAfgr.js";import{I as Le}from"./common-CvK_P_ao.js";import{g as Ae,a as Ie,r as Ce,d as Re,p as De}from"./conservationWaterLevel-BIi1yWt3.js";import{g as Ee}from"./index-C7go6VEC.js";import{_ as Se}from"./CardSearch-CB_HNR-Q.js";import{_ as Be}from"./CardTable-rdWOL4_6.js";import Ve from"./AnalysisDetailDialog-DxhJ9ApY.js";import{f as Fe}from"./DateFormatter-Bm9a68Ax.js";import{d as We}from"./processNumber-Clv_jqeh.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                 */const Ye={class:"wrapper"},Ne={class:"stat-content"},Me={class:"stat-value"},qe={class:"stat-icon"},He={class:"stat-content"},Pe={class:"stat-value"},Ue={class:"stat-icon"},ze={class:"stat-content"},$e={class:"stat-value"},Ge={class:"stat-icon"},Je={class:"stat-content"},Ke={class:"stat-value"},Oe={class:"stat-icon"},Qe=se({__name:"analysis",setup(je){const T=e=>Fe(e,"YYYY-MM-DD HH:mm:ss"),L=(e,t=2)=>e==null||e===""?"--":We(e),B=o(!1),A=o(!1),I=o([]),V=o(0),F=o(0),C=o(!1),W=o({}),R=o([]),g=o({}),Y=o(),D=o(),v=M({stationId:"",timeRange:null}),i=o([]),z=o({labelWidth:"100px",filters:[{type:"select",label:"测点名称",field:"stationId",options:q(()=>[{label:"全部测点",value:""},...R.value.map(e=>({label:e.name,value:e.id}))])},{type:"select",label:"分析状态",field:"status",options:[{label:"分析中",value:1},{label:"已完成",value:2},{label:"分析失败",value:3}]},{type:"select",label:"风险等级",field:"riskLevel",options:[{label:"低风险",value:1},{label:"中风险",value:2},{label:"高风险",value:3}]},{type:"daterange",label:"分析时间",field:"createTime"}],operations:[{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",icon:Le.QUERY,click:()=>p()},{type:"default",perm:!0,text:"重置",svgIcon:le(xe),click:()=>Q()}]}]}),d=M({title:"分析结果列表",columns:[{label:"测点名称",prop:"stationName",minWidth:140},{label:"分析周期",prop:"period",minWidth:320,formatter:e=>`${T(e.startTime)} 至 ${T(e.endTime)}`},{label:"水位变化(m)",prop:"levelChange",minWidth:130,align:"center",formatter:e=>L(e.levelChange)},{label:"涵养潜力",prop:"conservationPotential",minWidth:130,align:"center",formatter:e=>`${L(e.conservationPotential)}%`},{label:"风险等级",prop:"riskLevel",minWidth:110,align:"center",formatter:e=>ee(e.riskLevel)},{label:"建议涵养量(m³)",prop:"suggestedConservationAmount",minWidth:150,align:"center",formatter:e=>L(e.suggestedConservationAmount)},{label:"分析状态",prop:"status",minWidth:110,align:"center",formatter:e=>te(e.status)},{label:"分析时间",prop:"createTime",minWidth:170,align:"center",formatter:e=>T(e.createTime)},{label:"分析人",prop:"creatorName",minWidth:100,align:"center"}],dataList:I,operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"详情",icon:"iconfont icon-chakan",click:e=>j(e)},{perm:!0,type:"warning",isTextBtn:!0,text:"重新分析",icon:"iconfont icon-shuaxin",click:e=>X(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",icon:"iconfont icon-shanchu",click:e=>Z(e)}],pagination:{total:V.value,page:1,limit:10,handlePage:e=>{d.pagination&&(d.pagination.page=e,p())},handleSize:e=>{d.pagination&&(d.pagination.limit=e,p())}}}),$=q(()=>i.value&&Array.isArray(i.value)&&i.value.length===2);ne(()=>{G(),p(),E()});const G=async()=>{try{const e=await Ee({type:"水厂"});e.status===200&&(R.value=e.data.data||[])}catch(e){console.error("加载测点列表失败:",e),c.error("加载测点列表失败")}},J=e=>{e&&Array.isArray(e)&&e.length===2?i.value=[e[0],e[1]]:i.value=[]},E=async()=>{try{const e=await Ae();if(e.data.code===200){const t=e.data.data.reduce((n,r)=>(r.riskLevel===1&&(n.low=r.count),r.riskLevel===2&&(n.medium=r.count),r.riskLevel===3&&(n.high=r.count),n),{});g.value=t}}catch(e){console.error("加载风险统计失败:",e),N()}},N=()=>{const e=I.value.reduce((t,n)=>(n.riskLevel===1&&(t.low=(t.low||0)+1),n.riskLevel===2&&(t.medium=(t.medium||0)+1),n.riskLevel===3&&(t.high=(t.high||0)+1),t),{low:0,medium:0,high:0});g.value=e},p=async()=>{var e,t,n,r,_;B.value=!0;try{const u=((e=D.value)==null?void 0:e.queryParams)||{},h={pageNum:((t=d.pagination)==null?void 0:t.page)||1,pageSize:((n=d.pagination)==null?void 0:n.limit)||10,stationId:u.stationId||"",status:u.status,riskLevel:u.riskLevel,startTime:(r=u.createTime)==null?void 0:r[0],endTime:(_=u.createTime)==null?void 0:_[1]},f=await Ie(h);f.data.code===200&&(I.value=f.data.data.list,V.value=f.data.data.total,F.value=f.data.data.total,d.pagination&&(d.pagination.total=f.data.data.total),N())}catch(u){console.error("获取分析结果失败:",u),c.error("获取数据失败")}finally{B.value=!1}},K=async()=>{if(!v.stationId){c.warning("请选择测点");return}if(!i.value||i.value.length!==2){c.warning("请选择完整的分析周期");return}try{A.value=!0;const[e,t]=i.value;(await De(v.stationId,e,t)).data.code===200&&(c.success("分析任务已启动，请稍后查看结果"),p(),E())}catch(e){console.error("启动分析失败:",e),c.error("启动分析失败")}finally{A.value=!1}},O=()=>{var e;(e=Y.value)==null||e.resetFields(),i.value=[]},Q=()=>{var e;(e=D.value)==null||e.resetForm(),p()},j=e=>{W.value=e,C.value=!0},X=async e=>{try{await H.confirm("确定要重新分析吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(await Ce(e.id)).data.code===200&&(c.success("重新分析任务已启动"),p(),E())}catch(t){t!=="cancel"&&(console.error("重新分析失败:",t),c.error("重新分析失败"))}},Z=async e=>{try{await H.confirm("确定要删除这条分析结果吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(await Re(e.id)).data.code===200&&(c.success("删除成功"),p())}catch(t){t!=="cancel"&&(console.error("删除失败:",t),c.error("删除失败"))}},ee=e=>{switch(e){case 1:return"低风险";case 2:return"中风险";case 3:return"高风险";default:return"未知"}},te=e=>{switch(e){case 1:return"分析中";case 2:return"已完成";case 3:return"分析失败";default:return"未知"}};return(e,t)=>{const n=pe,r=fe,_=ve,u=_e,h=ge,f=ye,y=he,b=be,w=we,ae=ke;return S(),P("div",Ye,[a(y,{class:"analysis-panel",shadow:"never"},{default:s(()=>[t[5]||(t[5]=l("div",{class:"panel-header"},[l("h3",null,"智能分析控制面板")],-1)),a(f,{model:v,ref_key:"analysisFormRef",ref:Y,inline:!0,"label-width":"100px"},{default:s(()=>[a(_,{label:"选择测点",prop:"stationId",required:""},{default:s(()=>[a(r,{modelValue:v.stationId,"onUpdate:modelValue":t[0]||(t[0]=m=>v.stationId=m),placeholder:"请选择测点",filterable:"",style:{width:"200px"}},{default:s(()=>[(S(!0),P(oe,null,ie(R.value,m=>(S(),re(n,{key:m.id,label:m.name,value:m.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"分析周期",prop:"timeRange",required:""},{default:s(()=>[a(u,{modelValue:i.value,"onUpdate:modelValue":t[1]||(t[1]=m=>i.value=m),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"x","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)],onChange:J,style:{width:"350px"}},null,8,["modelValue","default-time"])]),_:1}),a(_,null,{default:s(()=>[a(h,{type:"primary",icon:"DataAnalysis",onClick:K,loading:A.value,disabled:!v.stationId||!$.value},{default:s(()=>t[3]||(t[3]=[U(" 开始分析 ")])),_:1},8,["loading","disabled"]),a(h,{icon:"Refresh",onClick:O},{default:s(()=>t[4]||(t[4]=[U("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(ae,{gutter:20,style:{"margin-bottom":"20px"}},{default:s(()=>[a(w,{span:6},{default:s(()=>[a(y,{class:"stat-card",shadow:"never"},{default:s(()=>[l("div",Ne,[l("div",Me,k(F.value),1),t[6]||(t[6]=l("div",{class:"stat-label"},"总分析次数",-1))]),l("div",qe,[a(b,null,{default:s(()=>[a(x(ce))]),_:1})])]),_:1})]),_:1}),a(w,{span:6},{default:s(()=>[a(y,{class:"stat-card risk-low",shadow:"never"},{default:s(()=>[l("div",He,[l("div",Pe,k(g.value.low||0),1),t[7]||(t[7]=l("div",{class:"stat-label"},"低风险",-1))]),l("div",Ue,[a(b,null,{default:s(()=>[a(x(de))]),_:1})])]),_:1})]),_:1}),a(w,{span:6},{default:s(()=>[a(y,{class:"stat-card risk-medium",shadow:"never"},{default:s(()=>[l("div",ze,[l("div",$e,k(g.value.medium||0),1),t[8]||(t[8]=l("div",{class:"stat-label"},"中风险",-1))]),l("div",Ge,[a(b,null,{default:s(()=>[a(x(ue))]),_:1})])]),_:1})]),_:1}),a(w,{span:6},{default:s(()=>[a(y,{class:"stat-card risk-high",shadow:"never"},{default:s(()=>[l("div",Je,[l("div",Ke,k(g.value.high||0),1),t[9]||(t[9]=l("div",{class:"stat-label"},"高风险",-1))]),l("div",Oe,[a(b,null,{default:s(()=>[a(x(me))]),_:1})])]),_:1})]),_:1})]),_:1}),a(Se,{ref_key:"refSearch",ref:D,config:z.value},null,8,["config"]),a(Be,{config:d,class:"card-table"},null,8,["config"]),a(Ve,{visible:C.value,"onUpdate:visible":t[2]||(t[2]=m=>C.value=m),"analysis-data":W.value},null,8,["visible","analysis-data"])])}}}),dt=Te(Qe,[["__scopeId","data-v-3b89fe94"]]);export{dt as default};
