import { request } from '@/plugins/axios';

/**
 * 查询设备数量以及昨日、今日供水量统计
 * @param params
 * @returns
 */
export const GetviewCount = (params: {
  projectId: string;
  stationType?: string;
}) => {
  return request({
    url: '/istar/api/waterPlant/view/data/viewCount',
    method: 'get',
    params
  });
};
/**
 * 产销率
 * @param params
 */
export const nrwByName = (params: { name?: string }) => {
  return request({
    url: '/api/spp/dma/partition/nrwByName',
    method: 'get',
    params
  });
};

/**
 * 查询生产看板数量统计列表
 * @param params
 * @returns
 */
export const GetLookBoardCountStatistic = (params: {
  stationType: string;
  projectId: string;
}) => {
  return request({
    url: '/istar/api/waterPlant/view/data/produceDashboardCount',
    method: 'get',
    params
  });
};
