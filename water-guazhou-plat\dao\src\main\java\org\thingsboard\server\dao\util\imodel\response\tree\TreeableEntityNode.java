package org.thingsboard.server.dao.util.imodel.response.tree;

import org.thingsboard.server.dao.util.imodel.query.TreeQueryOptions;
import org.thingsboard.server.dao.util.imodel.response.Responsible;

import java.util.List;

public interface TreeableEntityNode extends Responsible {
    int layer();

    void layer(int layer);

    void children(List<TreeableEntityNode> nodes);

    String getId();

    /**
     * 拿取一次深度
     */
    int take();

    /**
     * 查询剩余可使用的深度
     */
    int rest();

    /**
     * 设置剩余可使用的深度
     */
    void rest(int rest);

    /**
     * 装载树配置
     *
     * @param options 配置
     */
    void options(TreeQueryOptions options);

    /**
     * 有多少个子对象
     *
     * @return 数量
     */
    int size();

}
