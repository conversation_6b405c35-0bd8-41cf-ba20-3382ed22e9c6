<!-- 养护方案 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig" />
    <CardTable :config="TableConfig" class="card-table" />
    <DialogForm ref="refForm" :config="FormConfig"></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Plus, Search } from '@element-plus/icons-vue';
import { ICONS } from '@/common/constans/common';
import { SLConfirm } from '@/utils/Message';
import useGlobal from '@/hooks/global/useGlobal';
import { formatDate } from '@/utils/DateFormatter';
import {
  getSchemeList,
  delScheme,
  saveScheme
} from '@/api/secondSupplyManage/stationCircuit';

const { $messageSuccess, $messageError, $messageWarning } = useGlobal();
const refForm = ref<IDialogFormIns>();
const refSearch = ref<ISearchIns>();
const SearchConfig = reactive<ISearch>({
  filters: [{ label: '方案名称', field: 'name', type: 'input' }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => refreshData()
        },
        {
          text: '新增',
          perm: true,
          type: 'success',
          svgIcon: shallowRef(Plus),
          click: () => {
            FormConfig.defaultValue = {};
            FormConfig.title = '新增';
            refForm.value?.openDialog();
          }
        }
      ]
    }
  ]
});

const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'name', label: '方案名称' },
    // { prop: 'content', label: '方案内容' },
    { prop: 'createUserName', label: '创建人' },
    {
      prop: 'createTime',
      label: '添加时间',
      formatter: (row: any, val: any) => formatDate(val, 'YYYY-MM-DD HH:mm:ss')
    }
  ],
  operations: [
    {
      text: '编辑',
      isTextBtn: true,
      perm: true,
      icon: 'iconfont icon-bianji',
      click: (row) => {
        FormConfig.title = '编辑';
        FormConfig.defaultValue = {
          ...row
        };
        refForm.value?.openDialog();
      }
    },
    {
      perm: true,
      text: '删除',
      isTextBtn: true,
      type: 'danger',
      icon: 'iconfont icon-shanchu',
      click: (row) => handleDelete(row)
    }
  ],
  operationWidth: '200px',
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.limit = size;
      TableConfig.pagination.page = page;
      refreshData();
    }
  }
});
const FormConfig = reactive<IDialogFormConfig>({
  title: '知识库',
  defaultValue: {},
  dialogWidth: 800,
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '方案名称',
          field: 'name',
          rules: [{ required: true, message: '请填写方案名称' }]
        },
        {
          type: 'wangeditor',
          label: '方案内容',
          field: 'content',
          rules: [{ required: true, message: '请填写方案内容' }]
        }
      ]
    }
  ],
  submit: (params: any) => {
    saveScheme(params)
      .then((res) => {
        if (res.data?.code === 200) {
          $messageSuccess('保存成功');
        } else {
          $messageError('保存失败');
        }
        refreshData();
        refForm.value?.closeDialog();
      })
      .catch((err) => {
        $messageError(err);
      });
  }
});

const refreshData = async () => {
  TableConfig.loading = true;
  const query = refSearch.value?.queryParams || {};
  const newParams: any = {
    ...query,
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1
  };
  getSchemeList(newParams)
    .then((res) => {
      TableConfig.dataList = res.data?.data?.data || [];
      TableConfig.pagination.total = res.data?.data?.total || 0;
      TableConfig.loading = false;
    })
    .catch((err) => {
      $messageError(err);
      TableConfig.loading = false;
    });
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除指定养护方案?', '删除提示').then(() => {
    delScheme([row.id])
      .then((res) => {
        if (res.data?.code === 200) {
          $messageSuccess('删除成功');
          refreshData();
        } else {
          $messageError('删除失败');
        }
      })
      .catch((err) => {
        $messageError(err);
      });
  });
};

onMounted(() => {
  refreshData();
});
</script>
<style lang="scss" scoped></style>
