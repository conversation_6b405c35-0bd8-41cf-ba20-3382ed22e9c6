package org.thingsboard.server.dao.model.sql.smartManagement;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class UserCoordinate {
    // id
    @TableId
    private String id;

    // 用户id
    @ParseUsername(withDepartment = true)
    private String userId;

    // 坐标
    private String coordinate;

    // 创建时间/上报时间
    private Date createTime;

    // 租户Id
    private String tenantId;

}
