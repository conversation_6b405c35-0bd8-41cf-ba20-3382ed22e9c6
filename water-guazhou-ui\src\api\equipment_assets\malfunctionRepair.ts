import { request } from '@/plugins/axios';

// 故障知识库
// 分页条件查询故障知识库信息
export const getFaultKnowledgeSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  name?: string;
  deviceTypeId?: string;
}) =>
  request({
    url: `/api/fault/type`,
    method: 'get',
    params
  });

// 添加/修改故障知识库
export const postFaultKnowledge = (params?: {
  id?: string;
  serialId: string;
  name: string;
  remark: string;
}) =>
  request({
    url: `/api/fault/type`,
    method: 'post',
    data: params
  });

// 删除故障知识库
export const deleteFaultKnowledge = (ids: string[]) =>
  request({
    url: `/api/fault/type`,
    method: 'delete',
    data: ids
  });

// 故障信息
// 分页条件查询故障知识库故障信息
export const getFaultKnowledgeDetailSerch = (
  mainId: string,
  params?: {
    name?: string;
    serialId?: string;
  }
) =>
  request({
    url: `/api/fault/info/${mainId}`,
    method: 'get',
    params
  });

// 添加/修改故障知识库故障信息
export const postFaultKnowledgeDetail = (params?: {
  id?: string;
  mainId: string;
  name: string;
  remark: string;
}) =>
  request({
    url: `/api/fault/info`,
    method: 'post',
    data: params
  });

// 删除故障知识库故障信息
export const deleteFaultKnowledgeDetail = (ids: string[]) =>
  request({
    url: `/api/fault/info`,
    method: 'delete',
    data: ids
  });

// 解决方案
// 分页条件查询故障知识库解决方案
export const getFaultKnowledgeWithSerch = (
  mainId: string,
  params?: {
    name?: string;
    serialId?: string;
  }
) =>
  request({
    url: `/api/fault/solution/${mainId}`,
    method: 'get',
    params
  });

// 添加/修改故障知识库解决方案
export const postFaultKnowledgeWith = (params?: {
  id?: string;
  mainId: string;
  name: string;
  remark: string;
}) =>
  request({
    url: `/api/fault/solution`,
    method: 'post',
    data: params
  });

// 删除故障知识库解决方案
export const deleteFaultKnowledgeWith = (ids: string[]) =>
  request({
    url: `/api/fault/solution`,
    method: 'delete',
    data: ids
  });

// 维修计划
// 分页条件查询维修计划信息
export const getMaintenancePlanSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  planName?: string;
  teamName?: string;
  userName?: string;
  startStartTime?: string;
  startEndTime?: string;
  endStartTime?: string;
  endEndTime?: string;
}) =>
  request({
    url: `/api/fault/plan/m`,
    method: 'get',
    params
  });

// 分页条件查询维修计划信息详情
export const getMaintenancePlanSerchDetail = (id: string) =>
  request({
    url: `/api/fault/plan/m/detail/${id}`,
    method: 'get'
  });

// 添加/修改维修计划
export const postMaintenancePlan = (params?: {
  id?: string;
  name: string;
  teamId: string;
  userId: string;
  startTime: string;
  executionDays: string;
  intervalDays: string;
  executionNum: string;
  reviewer: string;
  remark: string;
  maintainPlanCList: any[];
}) =>
  request({
    url: `/api/fault/plan/m`,
    method: 'post',
    data: params
  });

// 删除维修计划
export const deleteMaintenancePlan = (ids: string[]) =>
  request({
    url: `/api/fault/plan/m`,
    method: 'delete',
    data: ids
  });

// 审核维修计划
export const putMaintenancePlan = (params?: { id: string; status: string }) =>
  request({
    url: `/api/fault/plan/m/reviewer`,
    method: 'post',
    data: params
  });

// 故障上报
export const postFaultReport = (params?: {
  title: string;
  faultProject: string;
  faultRemark: string;
  faultAddress: string;
  faultReportCList: [{ mmDeviceId: string }];
  workOrder: {
    title: string;
    source: string;
    organizerId: string;
    level: string;
    type: string;
    address: string;
    remark: string;
    videoUrl: string;
    audioUrl: string;
    imgUrl: string;
    otherFileUrl: string;
    uploadUserId: string;
    uploadPhone: string;
    uploadNo: string;
    uploadAddress: string;
    isDirectDispatch: string;
    status: string;
    projectId: string;
    ccUserId: string;
    processLevel?: string;
    stepProcessUserId?: string;
  };
}) =>
  request({
    url: `/api/fault/report`,
    method: 'post',
    data: params
  });

// 工单设备列表
// 分页条件查询工单设备列表信息
export const getWorkOrderEquipmentList = (params?: { workOrderId: string }) =>
  request({
    url: `/api/fault/report/device`,
    method: 'get',
    params
  });
