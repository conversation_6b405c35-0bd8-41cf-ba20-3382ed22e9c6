/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.alarm;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.common.data.EntityType;
import org.thingsboard.server.dao.model.sql.AlarmEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/21/2017.
 */
@SqlDao
public interface AlarmRepository extends CrudRepository<AlarmEntity, String> {

    @Query("SELECT a FROM AlarmEntity a WHERE a.tenantId = :tenantId AND a.originatorId = :originatorId " +
            "AND a.originatorType = :entityType AND a.type = :alarmType ORDER BY a.type ASC, a.id DESC")
    List<AlarmEntity> findLatestByOriginatorAndType(@Param("tenantId") String tenantId,
                                                    @Param("originatorId") String originatorId,
                                                    @Param("entityType") EntityType entityType,
                                                    @Param("alarmType") String alarmType,
                                                    Pageable pageable);

    //@Query("SELECT a FROM AlarmEntity a WHERE a.alarmJsonId = :alarmJsonId  order by a.startTs ASC ")
    List<AlarmEntity> findByAlarmJsonId(@Param("alarm_Json_id") String alarmJsonId);

    @Query("SELECT a FROM AlarmEntity a WHERE a.tenantId = :tenantId AND a.startTs > :start_ts and a.startTs<:end_ts order by a.startTs DESC ")
    List<AlarmEntity> findByTenantIdAndStartTsBetween(@Param("tenantId") String tenantId,
                                                      @Param("start_ts") long start,
                                                      @Param("end_ts") long end);

    @Query("SELECT a FROM AlarmEntity a WHERE a.originatorId = :originator_id AND a.startTs > :start_ts and a.startTs<:end_ts order by a.startTs DESC ")
    List<AlarmEntity> findByOriginatorIdAndStartTsAndEndTs(@Param("originator_id") String originatorId,
                                                           @Param("start_ts") long start,
                                                           @Param("end_ts") long end);

    List<AlarmEntity> findByOriginatorIdAndType(@Param("originator_id") String originatorId,
                                                @Param("type") String alarmType);

    List<AlarmEntity> findByOriginatorIdAndLevel(@Param("originator_id") String originatorId,
                                                @Param("level") String level);

    void deleteAllByOriginatorId(@Param("originator_id") String originatorId);

    List<AlarmEntity> findByOriginatorIdAndAlarmJsonIdIsNotNullOrderByStartTs(@Param("originator_id") String deviceId);

    @Query("SELECT originatorId FROM AlarmEntity where tenantId = ?1 AND status <> 'CLEARED_ACK' AND status <> 'CLEAR_FORCED' GROUP BY originatorId, severity ")
    List<String> findAlarmDeviceIdList(String tenantId);
}
