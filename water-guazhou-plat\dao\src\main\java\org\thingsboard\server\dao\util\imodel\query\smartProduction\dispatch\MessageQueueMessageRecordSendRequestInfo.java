package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MessageQueueMessageRecordSendRequestInfo {
    // 记录id
    private String id;

    // 接收人电话
    private String receiveUserPhone;

    // 模板id
    private String templateId;

    // 短信模板编号
    private String code;

    // 短信签名
    private String signKey;

    // 变量
    private String variables;

    public MessageQueueMessageRecordSendRequestInfo() {
        
    }

    public MessageQueueMessageRecordSendRequestInfo(MessageRecordSendRequest.MessageRecordSendRequestInfo messageRecordSendRequestInfo) {
        this.id = messageRecordSendRequestInfo.getId();
        this.receiveUserPhone = messageRecordSendRequestInfo.getReceiveUserPhone();
        this.templateId = messageRecordSendRequestInfo.getTemplateId();
        this.code = messageRecordSendRequestInfo.getCode();
        this.signKey = messageRecordSendRequestInfo.getSignKey();
        this.variables = messageRecordSendRequestInfo.getVariables();
    }
}
