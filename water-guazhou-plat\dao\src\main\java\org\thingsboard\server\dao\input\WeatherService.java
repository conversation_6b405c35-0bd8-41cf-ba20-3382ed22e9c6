package org.thingsboard.server.dao.input;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.Weather;

import java.util.List;

public interface WeatherService {
    PageData<Weather> findList(int page, int size, String time, TenantId tenantId);

    void save(Weather weather);

    void remove(List<String> ids);
}
