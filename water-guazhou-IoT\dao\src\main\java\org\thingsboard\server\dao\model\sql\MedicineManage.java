package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_MEDICINE_MANAGE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class MedicineManage {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_MEDICINE_MANAGE_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.TB_MEDICINE_MANAGE_MEDICINE_TYPE)
    private String medicineType;

    @Column(name = ModelConstants.TB_MEDICINE_MANAGE_TIME)
    private Date time;

    @Column(name = ModelConstants.TB_MEDICINE_MANAGE_NUM)
    private BigDecimal num;

    @Column(name = ModelConstants.TB_MEDICINE_MANAGE_UNIT_PRICE)
    private BigDecimal unitPrice;

    @Column(name = ModelConstants.TB_MEDICINE_MANAGE_PRICE)
    private BigDecimal price;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
