package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MENU_BUTTON_ROLE)
@NoArgsConstructor
@AllArgsConstructor
public class MenuButtonRole {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.MENU_BUTTON_ROLE_MENU_BUTTON_ID)
    private String menuButtonId;

    @Column(name = ModelConstants.MENU_BUTTON_ROLE_ID)
    private String roleId;
}

