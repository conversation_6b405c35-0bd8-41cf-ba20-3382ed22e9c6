<template>
  <div class="chart-wrapper">
    <VChart :option="state.diameterOption"></VChart>
    <!-- <div class="left">
      <VChart :option="state.materialOption"></VChart>
    </div>

    <div class="right">
    </div> -->
  </div>
</template>
<script lang="ts" setup>
import { useBusinessStore } from '@/store'
import useAlarm from '@/views/reports/hooks/useAlarm'

const state = reactive<{
  materialOption: any
  diameterOption: any
}>({
  materialOption: null,
  diameterOption: null
})

const generateWorkorderTypeOption = (data: { value: number; name: string }[]) => {
  return {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 0,
      top: 'center',
      pageIconColor: '#fff',
      pageTextStyle: { color: '#fff' },
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 170,
            fontSize: 12
          },
          value: {
            align: 'right',
            width: 40,
            fontSize: 12,
            color: '#00ff00'
          }
        }
      },
      data: data.map(item => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            if (name === data[i].name) {
              return '{name| ' + name + '}' + '{value| ' + data[i].value + '}'
            }
          }
        }
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '60%'],
        center: ['20%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        labelLine: {
          show: false
        },
        data
      }
    ]
  }
}
const { getAlarmRank, AlarmRank } = useAlarm()
onMounted(() => {
  getAlarmRank({
    projectId: useBusinessStore().selectedProject?.value,
    start: moment().startOf('year').valueOf(),
    end: moment().valueOf()
  }).then(() => {
    state.diameterOption = generateWorkorderTypeOption(
      AlarmRank.value.map(item => {
        return {
          name: item.deviecName,
          value: item.alarm
        }
      })
    )
  })
})
</script>
<style lang="scss" scoped>
.chart-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  .left,
  .right {
    width: 50%;
  }
}
</style>
