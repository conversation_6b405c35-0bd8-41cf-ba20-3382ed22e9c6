import{n as P,g as b,m as O,y as S,l as f,S as W,a as C,v as z,w as $,b as k}from"./MapView-DaoQedLH.js";import{w as t}from"./Point-WxyopZva.js";import{nearestCoordinate as E,planarArea as L,planarLength as X,intersects as Y,equals as D}from"./geometryEngine-OGzB5MRq.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{l as F}from"./index-r0dFAfgr.js";const Z=(l,e,r)=>{const a=new t({x:l,y:e,spatialReference:r.spatialReference}),c=new P({url:r.picUrl,height:(r.picSize instanceof Array?r.picSize[1]:r.picSize)||15,width:(r.picSize instanceof Array?r.picSize[0]:r.picSize)||15,xoffset:r.xOffset||0,yoffset:r.yOffset||0});return new b({geometry:a,symbol:c,attributes:r.attributes})},_=(l,e,r)=>{const a=new t({x:l,y:e,spatialReference:r.spatialReference}),c=new O({text:r.text,backgroundColor:[0,0,0,.1],xoffset:r.xOffset||0,yoffset:r.yOffset||-15,color:"#fff",font:{size:12,weight:"bold",...r.font||{}}});return new b({geometry:a,symbol:c,attributes:r.attributes})},d=(l,e)=>{switch(l.toLowerCase()){case"polyline":return new C({color:(e==null?void 0:e.color)instanceof f?e.color:new f((e==null?void 0:e.color)||"#00ffff"),style:(e==null?void 0:e.style)||"solid",width:(e==null?void 0:e.width)||4});case"multilinestring":return new C({color:(e==null?void 0:e.color)instanceof f?e.color:new f((e==null?void 0:e.color)||"#00ffff"),style:(e==null?void 0:e.style)||"solid",width:(e==null?void 0:e.width)||4});case"polygon":return new W({color:(e==null?void 0:e.color)instanceof f?e.color:new f((e==null?void 0:e.color)||[0,0,0,.1]),outline:{style:(e==null?void 0:e.outlineStyle)||"solid",color:new f((e==null?void 0:e.outlineColor)||"#00ffff"),width:(e==null?void 0:e.outlineWidth)||2},style:(e==null?void 0:e.style)||"solid"});case"point":return new S({size:((e==null?void 0:e.size)instanceof Array?e.size[0]:e==null?void 0:e.size)||12,color:(e==null?void 0:e.color)instanceof f?e.color:new f((e==null?void 0:e.color)||"#00ffff"),style:(e==null?void 0:e.style)||"circle",outline:{color:new f((e==null?void 0:e.outlineColor)||"#00ffff"),width:(e==null?void 0:e.outlineWidth)||.2}});case"text":return new O({text:e==null?void 0:e.text,backgroundColor:(e==null?void 0:e.color)instanceof f?e.color:(e==null?void 0:e.color)||[0,0,0,.3],xoffset:(e==null?void 0:e.xOffset)||0,yoffset:(e==null?void 0:e.yOffset)===void 0?-15:e.yOffset,color:(e==null?void 0:e.textColor)||"#fff",font:{size:12,weight:"bold"}});case"picture":return new P({url:e==null?void 0:e.url,height:((e==null?void 0:e.size)instanceof Array?e.size[1]:e==null?void 0:e.size)||15,width:((e==null?void 0:e.size)instanceof Array?e.size[0]:e==null?void 0:e.size)||15,xoffset:(e==null?void 0:e.xOffset)||0,yoffset:(e==null?void 0:e.yOffset)||0});default:return new S({color:new f((e==null?void 0:e.color)||"#00ffff"),style:"diamond",outline:{color:new f((e==null?void 0:e.outlineColor)||"#00ffff"),width:(e==null?void 0:e.outlineWidth)||.2}})}},H=(l,e)=>(l==null?void 0:l.length)!==2?void 0:new z({rings:[[[l[0][0],l[0][1]],[l[1][0],l[0][1]],[l[1][0],l[1][1]],[l[0][0],l[1][1]]]],spatialReference:e}),n=l=>new b(l),j=l=>{const e=l.symbol,r=l.symbol.color;let a=0;const c=setInterval(()=>{l.symbol=d(l.geometry.type,{color:a%2===0?"#ff0000":r}),a++,a===6&&(clearInterval(c),l.symbol=e)},300)},s=async(l,e,r)=>{if(!(!e||!l)){try{const a=e==null?void 0:e.geometry.extent,c=l.extent?l.extent.width/l.extent.height:16/9;if(a){let u=(a==null?void 0:a.xmin)||0,x=(a==null?void 0:a.xmax)||0,m=(a==null?void 0:a.ymin)||0,w=(a==null?void 0:a.ymax)||0,h=x-u,y=w-m;h>y*c?y=h/c:h=y*c,u-=h*((r==null?void 0:r.ratio)||2),x+=h*((r==null?void 0:r.ratio)||2),m-=y*((r==null?void 0:r.ratio)||2),w+=y*((r==null?void 0:r.ratio)||2),await(l==null?void 0:l.goTo(new $({spatialReference:a.spatialReference,xmin:u,xmax:x,ymin:m,ymax:w}),{duration:500}))}else await(l==null?void 0:l.goTo({zoom:(r==null?void 0:r.zoom)||16,target:e==null?void 0:e.geometry}))}catch(a){console.log(a)}r!=null&&r.avoidHighlight||j(e)}},v=l=>{if(l){if(l.type==="point")return[l.x,l.y];if(l.extent){const e=[(l.extent.xmax+l.extent.xmin)/2,(l.extent.ymax+l.extent.ymin)/2];if(l.type==="polyline"){const r=new t({x:e[0],y:e[1],spatialReference:l.spatialReference}),a=R(l,r);return a?[a.x,a.y]:void 0}return e}}},i=l=>{if(l){if(l.type==="point")return l;if(l.extent){const e=[(l.extent.xmax+l.extent.xmin)/2,(l.extent.ymax+l.extent.ymin)/2];if(l.type==="polyline"){const r=new t({x:e[0],y:e[1],spatialReference:l.spatialReference});return R(l,r)}}}},R=(l,e)=>{if(!(!l||!e))return E(l,e).coordinate},p=(l,e)=>!l||!e?!1:Y(l,e),o=(l,e,r,a)=>l!=null&&l.length&&l.length<2?void 0:n({geometry:new k({paths:l,spatialReference:e}),symbol:r||d("polyline"),attributes:a}),ee=(l,e,r)=>{if(l!=null&&l.length)return n({geometry:new t({x:l[0][0],y:l[0][1],spatialReference:e}),symbol:r||d("point")})},J=(l,e,r,a)=>{if(!(!(l!=null&&l.length)||l.length<3))return n({geometry:new z({rings:l,spatialReference:e}),symbol:r||d("polygon"),attributes:a})},le=(l,e,r,a)=>{if((l==null?void 0:l.length)!==2)return;const c=[],u=Math.max(l[0][0],l[1][0]),x=Math.max(l[0][1],l[1][1]),m=Math.min(l[0][0],l[1][0]),w=Math.min(l[0][1],l[1][1]),h=u-m,y=x-w,G=u-h/2,q=x-y/2;for(let g=0;g<360;g++){const M=g*Math.PI/180,I=G-h/2*Math.cos(M),T=q+y/2*Math.sin(M);c.push([I,T])}return c.push(c[0]),J(c,e,r||d("polygon",{outlineWidth:1}),a)},re=(l,e,r,a)=>{const c=H(l,e);return c&&n({geometry:c,symbol:r||d("polygon",{outlineColor:"#ff0000",outlineWidth:1}),attributes:a})},ae=(l,e)=>{e=e||"body";const r=document.querySelector(e),a=F().valueOf().toFixed(0),c=document.createElement("textarea");return c.id=a,c.cols=27,c.rows=3,c.style.position="absolute",c.style.top=((l==null?void 0:l.y)||-1e3)+"px",c.style.left=((l==null?void 0:l.x)||-1e3)+"px",c.style.width="250px",c.style.height="60px",c.style.backgroundColor="transparent",c.style.borderWidth="3px",c.style.borderColor="red",c.style.borderStyle="solid",c.style.fontSize="18px",c.style.color="#fff",r==null||r.append(c),c==null||c.focus(),c},A=(l,e,r)=>{if(e)switch(l){case"point":return new t({x:e[0][0],y:e[0][1],spatialReference:r});case"polygon":return new z({rings:e,spatialReference:r});case"polyline":return new k({paths:e,spatialReference:r});default:return new t({x:e[0][0],y:e[0][1],spatialReference:r})}},ce=(l,e,r)=>{try{const a=A("polygon",l,r);return a&&L(a,e||"square-meters")||0}catch{return 0}},fe=(l,e,r)=>{const a=A("polyline",l,r);return a?X(a,e||"meters"):0},ue=l=>{if((l==null?void 0:l.length)!==3)return 0;const e=[];e.push(l[0][0]-l[1][0]),e.push(l[0][1]-l[1][1]);const r=[];r.push(l[2][0]-l[1][0]),r.push(l[2][1]-l[1][1]);let a;if(e[0]*r[1]===e[1]*r[0])a=180;else{const c=e[0]*r[0]+e[1]*r[1];a=Math.acos(c/Math.sqrt(e[0]**2+e[1]**2)/Math.sqrt(r[0]**2+r[1]**2))*180/Math.PI}return a},he=(l,e)=>{const r=e*Math.PI/180;return{x:l*Math.sin(r),y:l*Math.cos(r)}},ye=l=>{if(!l)return;const e=l.width,r=l.height;return Math.max(e,r)/1},te=(l,e)=>D(l,e);export{te as E,A as a,i as b,n as c,R as d,ce as e,H as f,s as g,Z as h,ye as i,ae as j,le as k,re as l,o as m,J as n,ee as o,v as p,fe as q,_ as r,d as s,he as t,ue as u,p as v};
