"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[639],{92908:(e,t,s)=>{s.d(t,{S:()=>n,X:()=>i});const i=1;function n(e,t){let s=0;for(const i of t){const t=i.attributes?.[e];"number"==typeof t&&isFinite(t)&&(s=Math.max(s,t))}return s}},30639:(e,t,s)=>{s.r(t),s.d(t,{default:()=>_});var i=s(20102),n=s(70586),r=s(33955),a=s(8744),o=s(98732),l=s(92908),u=s(57191),d=s(37427),p=s(50245),c=s(25278),y=s(23095),f=s(99514),h=s(86719),m=s(35671);const g=a.Zn,I={xmin:-180,ymin:-90,xmax:180,ymax:90,spatialReference:a.Zn},b={hasAttachments:!1,capabilities:"query, editing, create, delete, update",useStandardizedQueries:!0,supportsCoordinatesQuantization:!0,supportsReturningQueryGeometry:!0,advancedQueryCapabilities:{supportsQueryAttachments:!1,supportsStatistics:!0,supportsPercentileStatistics:!0,supportsReturningGeometryCentroid:!0,supportsQueryWithDistance:!0,supportsDistinct:!0,supportsReturningQueryExtent:!0,supportsReturningGeometryProperties:!1,supportsHavingClause:!0,supportsOrderBy:!0,supportsPagination:!0,supportsQueryWithResultType:!1,supportsSqlExpression:!0,supportsDisjointSpatialRel:!0}};function F(e){return(0,r.wp)(e)?null!=e.z:!!e.hasZ}function S(e){return(0,r.wp)(e)?null!=e.m:!!e.hasM}class _{constructor(){this._queryEngine=null,this._nextObjectId=null}destroy(){this._queryEngine&&this._queryEngine&&this._queryEngine.destroy(),this._queryEngine=this._fieldsIndex=this._createDefaultAttributes=null}async load(e){const t=[],{features:s}=e,n=this._inferLayerProperties(s,e.fields),r=e.fields||[],a=null!=e.hasM?e.hasM:!!n.hasM,o=null!=e.hasZ?e.hasZ:!!n.hasZ,y=!e.spatialReference&&!n.spatialReference,F=y?g:e.spatialReference||n.spatialReference,S=y?I:null,_=e.geometryType||n.geometryType,E=!_;let T=e.objectIdField||n.objectIdField,x=e.timeInfo;if(!E&&(y&&t.push({name:"feature-layer:spatial-reference-not-found",message:"Spatial reference not provided or found in features. Defaults to WGS84"}),!_))throw new i.Z("feature-layer:missing-property","geometryType not set and couldn't be inferred from the provided features");if(!T)throw new i.Z("feature-layer:missing-property","objectIdField not set and couldn't be found in the provided fields");if(n.objectIdField&&T!==n.objectIdField&&(t.push({name:"feature-layer:duplicated-oid-field",message:`Provided objectIdField "${T}" doesn't match the field name "${n.objectIdField}", found in the provided fields`}),T=n.objectIdField),T&&!n.objectIdField){let e=null;r.some((t=>t.name===T&&(e=t,!0)))?(e.type="esriFieldTypeOID",e.editable=!1,e.nullable=!1):r.unshift({alias:T,name:T,type:"esriFieldTypeOID",editable:!1,nullable:!1})}for(const e of r){if(null==e.name&&(e.name=e.alias),null==e.alias&&(e.alias=e.name),!e.name)throw new i.Z("feature-layer:invalid-field-name","field name is missing",{field:e});if(e.name===T&&(e.type="esriFieldTypeOID"),!h.v.jsonValues.includes(e.type))throw new i.Z("feature-layer:invalid-field-type",`invalid type for field "${e.name}"`,{field:e})}const R={};for(const e of r)if("esriFieldTypeOID"!==e.type&&"esriFieldTypeGlobalID"!==e.type){const t=(0,m.os)(e);void 0!==t&&(R[e.name]=t)}if(this._fieldsIndex=new f.Z(r),this._createDefaultAttributes=(0,c.Dm)(R,T),x){if(x.startTimeField){const e=this._fieldsIndex.get(x.startTimeField);e?(x.startTimeField=e.name,e.type="esriFieldTypeDate"):x.startTimeField=null}if(x.endTimeField){const e=this._fieldsIndex.get(x.endTimeField);e?(x.endTimeField=e.name,e.type="esriFieldTypeDate"):x.endTimeField=null}if(x.trackIdField){const e=this._fieldsIndex.get(x.trackIdField);e?x.trackIdField=e.name:(x.trackIdField=null,t.push({name:"feature-layer:invalid-timeInfo-trackIdField",message:"trackIdField is missing",details:{timeInfo:x}}))}x.startTimeField||x.endTimeField||(t.push({name:"feature-layer:invalid-timeInfo",message:"startTimeField and endTimeField are missing or invalid",details:{timeInfo:x}}),x=null)}const j={warnings:t,featureErrors:[],layerDefinition:{...b,drawingInfo:(0,c.bU)(_),templates:(0,c.Hq)(R),extent:S,geometryType:_,objectIdField:T,fields:r,hasZ:o,hasM:a,timeInfo:x},assignedObjectIds:{}};if(this._queryEngine=new p.q({fields:r,geometryType:_,hasM:a,hasZ:o,objectIdField:T,spatialReference:F,featureStore:new u.Z({geometryType:_,hasM:a,hasZ:o}),timeInfo:x,cacheSpatialQueries:!0}),!s||!s.length)return this._nextObjectId=l.X,j;const q=(0,l.S)(T,s);return this._nextObjectId=q+1,await(0,d._W)(s,F),this._loadInitialFeatures(j,s)}async applyEdits(e){const{spatialReference:t,geometryType:s}=this._queryEngine;return await Promise.all([(0,y.b)(t,s),(0,d._W)(e.adds,t),(0,d._W)(e.updates,t)]),this._applyEdits(e)}queryFeatures(e,t={}){return this._queryEngine.executeQuery(e,t.signal)}queryFeatureCount(e,t={}){return this._queryEngine.executeQueryForCount(e,t.signal)}queryObjectIds(e,t={}){return this._queryEngine.executeQueryForIds(e,t.signal)}queryExtent(e,t={}){return this._queryEngine.executeQueryForExtent(e,t.signal)}querySnapping(e,t={}){return this._queryEngine.executeQueryForSnapping(e,t.signal)}_inferLayerProperties(e,t){let s,i,a=null,o=null,l=null;for(const t of e){const e=t.geometry;if(!(0,n.Wi)(e)&&(a||(a=(0,r.Ji)(e)),o||(o=e.spatialReference),null==s&&(s=F(e)),null==i&&(i=S(e)),a&&o&&null!=s&&null!=i))break}if(t&&t.length){let e=null;t.some((t=>{const s="esriFieldTypeOID"===t.type,i=!t.type&&t.name&&"objectid"===t.name.toLowerCase();return e=t,s||i}))&&(l=e.name)}return{geometryType:a,spatialReference:o,objectIdField:l,hasM:i,hasZ:s}}async _loadInitialFeatures(e,t){const{geometryType:s,hasM:i,hasZ:a,objectIdField:l,spatialReference:u,featureStore:p}=this._queryEngine,c=[];for(const i of t){if(null!=i.uid&&(e.assignedObjectIds[i.uid]=-1),i.geometry&&s!==(0,r.Ji)(i.geometry)){e.featureErrors.push((0,y.av)("Incorrect geometry type."));continue}const t=this._createDefaultAttributes(),a=(0,y.O0)(this._fieldsIndex,t,i.attributes,!0,e.warnings);a?e.featureErrors.push(a):(this._assignObjectId(t,i.attributes,!0),i.attributes=t,null!=i.uid&&(e.assignedObjectIds[i.uid]=i.attributes[l]),(0,n.pC)(i.geometry)&&(i.geometry=(0,d.iV)(i.geometry,i.geometry.spatialReference,u)),c.push(i))}p.addMany((0,o.Yn)([],c,s,a,i,l));const{fullExtent:f,timeExtent:h}=await this._queryEngine.fetchRecomputedExtents();if(e.layerDefinition.extent=f,h){const{start:t,end:s}=h;e.layerDefinition.timeInfo.timeExtent=[t,s]}return e}async _applyEdits(e){const{adds:t,updates:s,deletes:i}=e,n={addResults:[],deleteResults:[],updateResults:[],uidToObjectId:{}};if(t&&t.length&&this._applyAddEdits(n,t),s&&s.length&&this._applyUpdateEdits(n,s),i&&i.length){for(const e of i)n.deleteResults.push((0,y.d1)(e));this._queryEngine.featureStore.removeManyById(i)}const{fullExtent:r,timeExtent:a}=await this._queryEngine.fetchRecomputedExtents();return{extent:r,timeExtent:a,featureEditResults:n}}_applyAddEdits(e,t){const{addResults:s}=e,{geometryType:i,hasM:a,hasZ:l,objectIdField:u,spatialReference:p,featureStore:c}=this._queryEngine,f=[];for(const a of t){if(a.geometry&&i!==(0,r.Ji)(a.geometry)){s.push((0,y.av)("Incorrect geometry type."));continue}const t=this._createDefaultAttributes(),o=(0,y.O0)(this._fieldsIndex,t,a.attributes);if(o)s.push(o);else{if(this._assignObjectId(t,a.attributes),a.attributes=t,null!=a.uid){const t=a.attributes[u];e.uidToObjectId[a.uid]=t}if((0,n.pC)(a.geometry)){const e=a.geometry.spatialReference??p;a.geometry=(0,d.iV)((0,y.og)(a.geometry,e),e,p)}f.push(a),s.push((0,y.d1)(a.attributes[u]))}}c.addMany((0,o.Yn)([],f,i,l,a,u))}_applyUpdateEdits({updateResults:e},t){const{geometryType:s,hasM:i,hasZ:a,objectIdField:l,spatialReference:u,featureStore:p}=this._queryEngine;for(const c of t){const{attributes:t,geometry:f}=c,h=t&&t[l];if(null==h){e.push((0,y.av)(`Identifier field ${l} missing`));continue}if(!p.has(h)){e.push((0,y.av)(`Feature with object id ${h} missing`));continue}const m=(0,o.EI)(p.getFeature(h),s,a,i);if((0,n.pC)(f)){if(s!==(0,r.Ji)(f)){e.push((0,y.av)("Incorrect geometry type."));continue}const t=f.spatialReference??u;m.geometry=(0,d.iV)((0,y.og)(f,t),t,u)}if(t){const s=(0,y.O0)(this._fieldsIndex,m.attributes,t);if(s){e.push(s);continue}}p.add((0,o.XA)(m,s,a,i,l)),e.push((0,y.d1)(h))}}_assignObjectId(e,t,s=!1){const i=this._queryEngine.objectIdField;s&&t&&isFinite(t[i])?e[i]=t[i]:e[i]=this._nextObjectId++}}},25278:(e,t,s)=>{s.d(t,{Dm:()=>d,Hq:()=>p,MS:()=>c,bU:()=>o});var i=s(80442),n=s(22974),r=s(61159),a=s(58333);function o(e){return{renderer:{type:"simple",symbol:"esriGeometryPoint"===e||"esriGeometryMultipoint"===e?a.I4:"esriGeometryPolyline"===e?a.ET:a.lF}}}const l=/^[_$a-zA-Z][_$a-zA-Z0-9]*$/;let u=1;function d(e,t){if((0,i.Z)("esri-csp-restrictions"))return()=>({[t]:null,...e});try{let s=`this.${t} = null;`;for(const t in e)s+=`this${l.test(t)?`.${t}`:`["${t}"]`} = ${JSON.stringify(e[t])};`;const i=new Function(`\n      return class AttributesClass$${u++} {\n        constructor() {\n          ${s};\n        }\n      }\n    `)();return()=>new i}catch(s){return()=>({[t]:null,...e})}}function p(e={}){return[{name:"New Feature",description:"",prototype:{attributes:(0,n.d9)(e)}}]}function c(e,t){return{analytics:{supportsCacheHint:!1},attachment:null,data:{isVersioned:!1,supportsAttachment:!1,supportsM:!1,supportsZ:e},metadata:{supportsAdvancedFieldProperties:!1},operations:{supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsAdd:t,supportsDelete:t,supportsEditing:t,supportsChangeTracking:!1,supportsQuery:!0,supportsQueryAnalytics:!1,supportsQueryAttachments:!1,supportsQueryTopFeatures:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsUpdate:t,supportsExceedsLimitStatistics:!0},query:r.g,queryRelated:{supportsCount:!0,supportsOrderBy:!0,supportsPagination:!0,supportsCacheHint:!1},queryTopFeatures:{supportsCacheHint:!1},editing:{supportsGeometryUpdate:t,supportsGlobalId:!1,supportsReturnServiceEditsInSourceSpatialReference:!1,supportsRollbackOnFailure:!1,supportsUpdateWithoutM:!1,supportsUploadWithItemId:!1,supportsDeleteByAnonymous:!1,supportsDeleteByOthers:!1,supportsUpdateByAnonymous:!1,supportsUpdateByOthers:!1}}}},58333:(e,t,s)=>{s.d(t,{ET:()=>r,I4:()=>n,eG:()=>l,lF:()=>a,lj:()=>d,qP:()=>o,wW:()=>u});const i=[252,146,31,255],n={type:"esriSMS",style:"esriSMSCircle",size:6,color:i,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},r={type:"esriSLS",style:"esriSLSSolid",width:.75,color:i},a={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},o={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},l={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},u={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},d={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);