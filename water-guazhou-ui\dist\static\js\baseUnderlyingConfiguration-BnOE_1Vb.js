import{_ as w}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as T}from"./CardSearch-CB_HNR-Q.js";import{z as d,C as k,c as h,r as p,b as i,S as P,o as v,g as I,n as L,q as g}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function U(l){return d({url:"/api/base/underlying/configuration/list",method:"get",params:l})}function V(l){return d({url:"/api/base/underlying/configuration/getDetail",method:"get",params:{id:l}})}function _(l){return d({url:"/api/base/underlying/configuration/add",method:"post",data:l})}function C(l){return d({url:"/api/base/underlying/configuration/edit",method:"post",data:l})}function A(l){return d({url:"/api/base/underlying/configuration/deleteIds",method:"delete",data:l})}const B={class:"wrapper"},E={__name:"baseUnderlyingConfiguration",setup(l){const f=h(),u=h(),x=p({labelWidth:"100px",filters:[{type:"input",label:"主机地址",field:"hostAddress",placeholder:"请输入主机地址",onChange:()=>o()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>o()},{perm:!0,type:"primary",text:"新增",click:()=>b()},{perm:!0,type:"danger",text:"批量删除",click:()=>y()}]}],defaultParams:{}}),s=p({columns:[{label:"主机地址",prop:"hostAddress"},{label:"端口号",prop:"hostPort"},{label:"数据库名称",prop:"name"},{label:"用户名",prop:"username"},{label:"url扩展参数",prop:"urlEx"},{label:"连接池配置",prop:"poolConfig"},{label:"安全配置",prop:"safeConfig"},{label:"超时与重试",prop:"connTimeout"},{label:"唯一站点id",prop:"siteId"},{label:"IP地址",prop:"ipAddress"},{label:"数据库版本",prop:"dbVersion"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>D(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>b(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>y(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{s.pagination.page=e,o()},handleSize:e=>{s.pagination.limit=e,o()}},handleSelectChange:e=>{s.selectList=e||[]}}),r=p({title:"新增基础配置",group:[{fields:[{type:"input",label:"主机地址",field:"hostAddress",rules:[{required:!0,message:"请输入主机地址"}]},{type:"input",label:"端口号",field:"hostPort",rules:[{required:!0,message:"请输入端口号"}]},{type:"input",label:"数据库名称",field:"name",rules:[{required:!0,message:"请输入数据库名称"}]},{type:"input",label:"用户名",field:"username",rules:[{required:!0,message:"请输入用户名"}]},{type:"input",label:"密码",field:"password",rules:[{required:!0,message:"请输入密码"}]},{type:"input",label:"url扩展参数",field:"urlEx",rules:[{required:!0,message:"请输入url扩展参数"}]},{type:"input",label:"连接池配置",field:"poolConfig",rules:[{required:!0,message:"请输入连接池配置"}]},{type:"input",label:"安全配置",field:"safeConfig",rules:[{required:!0,message:"请输入安全配置"}]},{type:"input",label:"超时与重试",field:"connTimeout",rules:[{required:!0,message:"请输入超时与重试"}]},{type:"input",label:"唯一站点id",field:"siteId",rules:[{required:!0,message:"请输入唯一站点id"}]},{type:"input",label:"IP地址",field:"ipAddress",rules:[{required:!0,message:"请输入IP地址"}]},{type:"input",label:"数据库版本",field:"dbVersion",rules:[{required:!0,message:"请输入数据库版本"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var t;try{e.id?(await C(e),i.success("修改成功")):(await _(e),i.success("新增成功")),(t=u.value)==null||t.closeDialog(),o()}catch{i.error("操作失败")}}}),m=()=>{r.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),r.showSubmit=!0,r.showCancel=!0,r.cancelText="取消",r.submitText="确定",r.submit=async e=>{var t;try{e.id?(await C(e),i.success("修改成功")):(await _(e),i.success("新增成功")),(t=u.value)==null||t.closeDialog(),o()}catch{i.error("操作失败")}}},D=async e=>{var t,a;try{const n=await V(e.id),c=((t=n.data)==null?void 0:t.data)||n;m(),r.title="基础配置详情",r.defaultValue={...c},r.group[0].fields.forEach(q=>{q.disabled=!0}),r.showSubmit=!1,r.cancelText="关闭",(a=u.value)==null||a.openDialog()}catch{i.error("获取详情失败")}},b=e=>{var t;m(),e?(r.title="编辑基础配置",r.defaultValue={...e}):(r.title="新增基础配置",r.defaultValue={}),(t=u.value)==null||t.openDialog()},y=async e=>{try{const t=e?[e.id]:s.selectList.map(a=>a.id);if(!t.length){i.warning("请选择要删除的数据");return}await P("确定要删除选中的数据吗？"),await A(t),i.success("删除成功"),o()}catch(t){t!=="cancel"&&i.error("删除失败")}},o=async()=>{var e,t;try{const a=await U({page:s.pagination.page,size:s.pagination.limit,...((e=f.value)==null?void 0:e.queryParams)||{}}),n=((t=a.data)==null?void 0:t.data)||a;s.dataList=n.records||n,s.pagination.total=n.total||n.length||0}catch{i.error("数据加载失败")}};return v(()=>{o()}),(e,t)=>{const a=T,n=S,c=w;return I(),L("div",B,[g(a,{ref_key:"refSearch",ref:f,config:x},null,8,["config"]),g(n,{class:"card-table",config:s},null,8,["config"]),g(c,{ref_key:"refDialogForm",ref:u,config:r},null,8,["config"])])}}},j=k(E,[["__scopeId","data-v-137346e5"]]);export{j as default};
