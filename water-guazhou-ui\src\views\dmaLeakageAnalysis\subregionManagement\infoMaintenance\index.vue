<!-- 分区信息维护 -->
<template>
  <DrawerBox
    ref="refDrawerBox"
    :right-drawer="true"
    :bottom-drawer="true"
    :bottom-drawer-bar-position="'left'"
    :bottom-drawer-title="TreeData.currentProject?.label || '请选择分区'"
  >
    <template #right>
      <SLTree :tree-data="TreeData"></SLTree>
    </template>
    <template #bottom>
      <PartitionDetail
        ref="refDetail"
        :tree-data="TreeData.data"
        :current-tree-node="TreeData.currentProject"
        @edit="editDraw"
        @draw="detailDraw"
        @success="detailRefreshed"
        @more="viewMoreDetail"
      ></PartitionDetail>
    </template>
    <ArcLayout ref="refArcLayout" @map-loaded="onMapLoaded"> </ArcLayout>
  </DrawerBox>
  <DialogForm
    ref="refDialogForm"
    :config="DialogFormConfig"
    @close="clearGraphicsLayer()"
  ></DialogForm>
  <DialogForm ref="refDialogConfig_HookUp" :config="DialogConfig_HookUp">
    <DeviceHookUp
      v-if="curHookUp === 'device'"
      :tree="TreeData.data"
      :current-tree-node="TreeData.currentProject"
    ></DeviceHookUp>
    <BigUserHookUp
      v-if="curHookUp === 'user'"
      :tree="TreeData.data"
      :current-tree-node="TreeData.currentProject"
    ></BigUserHookUp>
    <RevenueHookUp
      v-if="curHookUp === 'revenue'"
      :current-tree-node="TreeData.currentProject"
    ></RevenueHookUp>
    <JZRevenueHookUp
      v-if="curHookUp === 'jzrevenue'"
      :current-tree-node="TreeData.currentProject"
    ></JZRevenueHookUp>
  </DialogForm>
  <DialogForm
    ref="refDialogConfig_RevenueType"
    :config="DialogConfig_RevenueType"
  >
  </DialogForm>
  <DialogForm
    ref="refDialogConfig_ImportHookUpRevenue"
    :config="DialogConfig_ImportHookUpRevenue"
  >
    <div class="import-hookup-revenue">
      <div class="btns">
        <el-button type="success" @click="DownloadDMAUserTemplate">
          下载模板
        </el-button>
        <ImportButton
          :config="{
            perm: true,
            text: '追加挂接',
            click: handleImportHookUpRevenue
          }"
        >
          追加挂接
        </ImportButton>
      </div>
      <div class="text">
        <div class="curpartition">
          当前分区ID: {{ TreeData.currentProject?.value }}
        </div>
        <div class="warning">注： 选择导入文件时，请选择类型为xlsx</div>
      </div>
    </div>
  </DialogForm>

  <MoreDetail
    ref="refMoreDetail"
    :partition="TreeData.currentProject"
  ></MoreDetail>
</template>
<script lang="ts" setup>
import {
  CopyMeterOptions,
  DMAStatusOptions,
  DMATypeOptions,
  DownloadDMAUserTemplate,
  ImportDMAUsers,
  IsMachineMeterOptions,
  PostPartition
} from '@/api/mapservice/dma';
import { PipeStatistics } from '@/api/mapservice/pipe';
import DrawerBox from '@/components/DrawerBox/DrawerBox.vue';
import DialogForm from '@/components/Form/DialogForm.vue';
import { usePartition, useSketch } from '@/hooks/arcgis';
import { useGisStore } from '@/store';
import {
  EStatisticField,
  calcArea,
  getGraphicLayer,
  getPipeLineLayerOption,
  setSymbol
} from '@/utils/MapHelper';
import { SLConfirm, SLMessage } from '@/utils/Message';
import BigUserHookUp from './components/BigUserHookUp.vue';
import DeviceHookUp from './components/DeviceHookUp.vue';
import MoreDetail from './components/MoreDetail.vue';
import PartitionDetail from './components/PartitionDetail.vue';
import RevenueHookUp from './components/RevenueHookUp.vue';
import JZRevenueHookUp from './components/JZRevenueHookUp.vue';
// import { formatTree } from '@/utils/GlobalHelper'
// import {
//   calcArea,
//   createGraphic,
//   getGraphicsLayer,
//   getPipeLineLayerOption,
//   initSketch,
//   setGeometry,
//   setSymbol
// } from '@/hooks/arcgis/utils'
const refDetail = ref<InstanceType<typeof PartitionDetail>>();
const refDrawerBox = ref<InstanceType<typeof DrawerBox>>();

const refMoreDetail = ref<InstanceType<typeof MoreDetail>>();
// const refMap = ref<InstanceType<typeof Map>>()
const state = reactive<{
  drawFrom: 'add' | 'detail' | '';
}>({
  drawFrom: ''
});
const partition = usePartition();
const TreeData = reactive<SLTreeConfig>({
  nodeOperations: [
    {
      icon: 'ep:delete',
      color: '#ff0000',
      click: (data: any) => {
        partition.Delete([data.id]).then(() => {
          TreeData.data = partition.Tree.value;
          partition.refreshPartitions(staticState.view);
          TreeData.currentProject = undefined;
        });
      }
    }
  ],
  extraFilters: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '新建分区',
          type: 'success',
          click: () => addDivision()
        },
        {
          perm: true,
          text: '挂接',
          items: [
            {
              styles: { width: '120px' },
              perm: true,
              text: '设备挂接',
              click: () => hookUp('device')
            },
            {
              styles: { width: '120px' },
              perm: true,
              text: '大用户挂接',
              click: () => hookUp('user')
            },
            {
              hide: window.SITE_CONFIG.SITENAME === 'jingzhou',
              styles: { width: '120px' },
              perm: true,
              text: '营收挂接',
              click: () => {
                hookUp('revenue');
              }
            },
            {
              // 锦州专用
              hide: window.SITE_CONFIG.SITENAME !== 'jingzhou',
              styles: { width: '120px' },
              perm: true,
              text: '营收挂接',
              click: () => {
                hookUp('jzrevenue');
              }
            }
          ]
        },
        {
          perm: true,
          text: '更多',
          items: [
            {
              perm: true,
              text: '导出shapefile',
              click: () => exportShapefileVue()
            }
          ]
        }
      ]
    }
  ],
  data: [],
  isFilterTree: true,
  expandOnClickNode: false,
  title: '分区信息维护',
  treeNodeHandleClick: (params) => {
    TreeData.currentProject = params;
    const par = partition.List.value.find((item) => item.id === params.id);
    par.geom &&
      partition.extentToPartition(staticState.view, JSON.parse(par.geom));
    refDrawerBox.value?.toggleDrawer('btt', true);
  }
});

const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '新建分区',
  dialogWidth: '70%',
  labelWidth: 120,
  labelPosition: 'right',
  desTroyOnClose: false,
  defaultValue: {
    borderColor: 'rgba(245,108,108,1)',
    rangeColor: 'rgba(245,108,108,0.4)',
    type: '1',
    isMachineMeter: false,
    copyMeterType: '1',
    status: '2'
  },
  group: [
    {
      fieldset: {
        desc: '基本信息'
      },
      fields: [
        {
          lg: 8,
          md: 12,
          type: 'select-tree',
          label: '上级分区',
          checkStrictly: true,
          field: 'pid',
          options: computed(() => {
            return partition.Tree.value;
          }) as any
          // autoFillOptions: config => {
          //   config.options = partition.Tree.value
          // }
        },
        {
          md: 12,
          type: 'input',
          label: '分区名称',
          field: 'name',
          rules: [{ required: true, message: '请输入分区名称' }]
        },
        {
          lg: 8,
          md: 12,
          type: 'select',
          label: '分区类型',
          field: 'type',
          clearable: false,
          options: DMATypeOptions
        },
        {
          lg: 8,
          md: 12,
          type: 'input-number',
          label: '分区排序',
          field: 'orderNum',
          rules: [{ required: true, message: '请输入分区排序' }]
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = params.type === '1';
          },
          lg: 8,
          md: 12,
          type: 'select',
          label: '分区状态',
          field: 'status',
          clearable: false,
          options: DMAStatusOptions
        },
        {
          lg: 8,
          md: 12,
          type: 'select',
          label: '分区抄表类型',
          field: 'copyMeterType',
          clearable: false,
          options: CopyMeterOptions
        },
        {
          lg: 8,
          md: 12,
          type: 'select',
          label: '是否有机械表',
          field: 'isMachineMeter',
          clearable: false,
          options: IsMachineMeterOptions
        },
        {
          lg: 8,
          md: 12,
          type: 'select',
          label: '小区采集频率',
          field: 'collectRate',
          options: [
            { label: '5', value: 5 },
            { label: '15', value: 15 },
            { label: '30', value: 30 },
            { label: '60', value: 60 }
          ],
          rules: [{ required: true, message: '请选择采集频率' }]
        },

        {
          lg: 8,
          md: 12,
          type: 'input',
          label: '负责人',
          field: 'director'
        },
        // {
        //   lg: 8,
        //   md: 12,
        //   type: 'number',
        //   readonly: true,
        //   label: '大用户数',
        //   field: 'bigUserNum'
        // },
        // {
        //   lg: 8,
        //   md: 12,
        //   type: 'number',
        //   readonly: true,
        //   label: '营收户数',
        //   field: 'revenueUserNum'
        // },
        // {
        //   type: 'select',
        //   label: '分区状态',
        //   field: 'status',
        //   options: DMAStatusOptions
        // },
        {
          type: 'input',
          label: '分区范围',
          field: 'range'
        }
      ]
    },
    {
      fieldset: {
        desc: '分区绘制'
      },
      fields: [
        {
          lg: 8,
          md: 12,
          label: '分区绘制',
          type: 'btn-group',
          btns: [{ perm: true, text: '点击开始绘制', click: () => initDraw() }]
        },
        {
          lg: 8,
          md: 12,
          type: 'color-picker',
          label: '边框颜色',
          field: 'borderColor',
          input: true
        },
        {
          lg: 8,
          md: 12,
          type: 'color-picker',
          label: '区域颜色',
          field: 'rangeColor',
          input: true
        },
        {
          lg: 8,
          md: 12,
          type: 'input-number',
          label: '供水面积(k㎡)',
          field: 'supplyWaterArea',
          appendBtns: [{ perm: true, text: '计算', click: () => getArea() }]
        },
        {
          lg: 8,
          md: 12,
          type: 'input-number',
          label: '主管线长度(km)',
          field: 'mainLineLength',
          appendBtns: [{ perm: true, text: '计算', click: () => calcLength() }]
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        }
      ]
    }
  ],
  submit: (params) => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        try {
          DialogFormConfig.submitting = true;
          const submitParams = {
            ...params,
            geom: staticState.graphic?.geometry
              ? JSON.stringify((staticState.graphic.geometry as any).rings)
              : undefined
          };
          const res = await PostPartition(submitParams);
          if (res.data) {
            clearGraphicsLayer();
            SLMessage.success('操作成功');
            refreshTree();
            partition.refreshPartitions(staticState.view);
            refDialogForm.value?.closeDialog();
          }
        } catch (error) {
          console.log(error);
          SLMessage.error('操作失败');
        }
        DialogFormConfig.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});

const refDialogForm = ref<InstanceType<typeof DialogForm>>();
const exportShapefileVue = () => {
  SLMessage.info('功能开发中...');
};
const curHookUp = ref<'device' | 'user' | 'revenue' | '' | 'jzrevenue'>('');
const refDialogConfig_HookUp = ref<InstanceType<typeof DialogForm>>();
const DialogConfig_HookUp = reactive<IDialogFormConfig>({
  title: computed(() =>
    curHookUp.value === 'device'
      ? '设备挂接'
      : curHookUp.value === 'revenue'
        ? '营收手动挂接'
        : curHookUp.value === 'user'
          ? '大用户挂接'
          : ''
  ) as any,
  dialogWidth: '80%',
  group: [],
  cancel: false
});

const refDialogConfig_RevenueType = ref<InstanceType<typeof DialogForm>>();
const DialogConfig_RevenueType = reactive<IDialogFormConfig>({
  title: '营收挂接',
  dialogWidth: 450,
  group: [
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            { perm: true, text: '手动挂接', click: () => manualHookRevenue() },
            { perm: true, text: '批量导入', click: () => importHookUpRevenue() }
          ]
        }
      ]
    }
  ],
  cancel: false
});
const manualHookRevenue = () => {
  refDialogConfig_HookUp.value?.openDialog();
};
const importHookUpRevenue = () => {
  refDialogConfig_ImportHookUpRevenue.value?.openDialog();
};
const refDialogConfig_ImportHookUpRevenue =
  ref<InstanceType<typeof DialogForm>>();

const DialogConfig_ImportHookUpRevenue = reactive<IDialogFormConfig>({
  title: '批量导入',
  dialogWidth: 450,
  group: [],
  cancel: false
});

const handleImportHookUpRevenue = (formData: any) => {
  SLConfirm('将上传数据并进行解析，确定上传？', '提示信息').then(async () => {
    try {
      const res = await ImportDMAUsers(formData);
      if (res.data.code === 200) {
        SLMessage.success('导入成功');
      } else {
        SLMessage.error(res.data.message);
      }
    } catch (error) {
      SLMessage.error('上传失败');
    }
  });
};
const hookUp = (type: 'device' | 'user' | 'revenue' | '' | 'jzrevenue') => {
  if (type === '') return;
  if (!TreeData.currentProject) {
    SLMessage.warning('请先选择一个分区');
    return;
  }
  curHookUp.value = type;
  if (type === 'revenue') {
    refDialogConfig_RevenueType.value?.openDialog();
  } else if (type === 'jzrevenue') {
    refDialogConfig_HookUp.value?.openDialog();
  } else {
    refDialogConfig_HookUp.value?.openDialog();
  }
};
const addDivision = () => {
  state.drawFrom = 'add';
  DialogFormConfig.defaultValue = {
    ...(DialogFormConfig.defaultValue || {}),
    pid: TreeData.currentProject?.id
  };
  refDialogForm.value?.openDialog();
  refDialogForm.value?.resetForm();
};
const viewMoreDetail = () => {
  refMoreDetail.value?.openDialog();
};
const refreshTree = () => {
  partition.getTree().then(() => {
    TreeData.data = partition.Tree.value;
  });
};
const staticState: {
  graphic?: __esri.Graphic;
  graphicsLayer?: __esri.GraphicsLayer;
  sketch?: __esri.SketchViewModel;
  partitionLayer?: __esri.GraphicsLayer;
  curPartitionLayer?: __esri.GraphicsLayer;
  view?: __esri.MapView;
} = {};
const initDraw = () => {
  state.drawFrom = 'add';
  refDialogForm.value?.closeDialog();
  clearGraphicsLayer();
  if (staticState.sketch) {
    const formData = refDialogForm.value?.refForm?.dataForm;
    staticState.sketch.polygonSymbol = setSymbol('polygon', {
      color: formData.rangeColor,
      outlineColor: formData.borderColor
    }) as __esri.SimpleFillSymbol;
    staticState.sketch.create('polygon');
  }
};
const detailDraw = () => {
  state.drawFrom = 'detail';
  refDialogForm.value?.closeDialog();
  clearGraphicsLayer();
  if (staticState.sketch) {
    const formData = refDetail.value?.refForm?.dataForm || {};
    staticState.sketch.polygonSymbol = setSymbol('polygon', {
      color: formData.rangeColor,
      outlineColor: formData.borderColor
    }) as __esri.SimpleFillSymbol;
    staticState.sketch.create('polygon');
  }
};
const editDraw = (id: string) => {
  state.drawFrom = 'detail';
  refDialogForm.value?.closeDialog();
  clearGraphicsLayer();
  if (staticState.sketch) {
    const dataForm = refDetail.value?.refForm?.dataForm;
    staticState.sketch.polygonSymbol = setSymbol('polygon', {
      color: dataForm?.rangeColor,
      outlineColor: dataForm?.borderColor
    }) as __esri.SimpleFillSymbol;
    const graphic = partition.getPartitionGraphic(id);
    if (!graphic) {
      SLMessage.error('当前分区暂无区域信息');
      return;
    }
    staticState.graphic = graphic.clone();
    staticState.graphicsLayer?.add(staticState.graphic);

    staticState.sketch.create('polygon');
    staticState.sketch.update(staticState.graphic);
  }
};
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.graphic = undefined;
};
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    staticState.graphic = result.graphics[0];
    state.drawFrom === 'add' && refDialogForm.value?.openDialog();
    getArea();
    calcLength();
  }
};
const getArea = () => {
  if (!staticState.graphic) {
    SLMessage.warning('请先绘制图形');
    return;
  }
  const rings = (staticState.graphic.geometry as __esri.Polygon).rings;
  const res = calcArea(
    rings[0],
    'square-kilometers',
    staticState.view?.spatialReference
  ).toFixed(6);
  const dataForm = refDialogForm.value?.refForm?.dataForm;
  if (dataForm) {
    dataForm.supplyWaterArea = res;
  }
  const detail = refDetail.value?.refForm?.dataForm;
  if (detail) {
    detail.supplyWaterArea = res;
    detail.geom = staticState.graphic?.geometry
      ? JSON.stringify((staticState.graphic.geometry as any).rings)
      : undefined;
  }
};

const calcLength = async () => {
  if (!staticState.graphic) {
    SLMessage.warning('请先绘制图形');
    return;
  }
  const layerOptions = await getPipeLineLayerOption(staticState.view);
  const layerIds = layerOptions?.map((item) => item.id);
  try {
    const res = await PipeStatistics({
      usertoken: useGisStore().gToken,
      layerids: JSON.stringify(layerIds),
      group_fields: JSON.stringify(['DIAMETER']),
      statistic_field: EStatisticField.ShapeLen,
      statistic_type: '2',
      where: '1=1',
      geometry: staticState.graphic.geometry,
      f: 'pjson'
    });
    const dataForm = refDialogForm.value?.refForm?.dataForm;

    const detail = refDetail.value?.refForm?.dataForm;

    if (res.data.code === 10000) {
      const data = res.data?.result?.rows[0]?.rows || [];
      let length = 0;
      data.map((item) => {
        const value = Number(item[EStatisticField.ShapeLen]);
        if (!isNaN(value)) {
          length += value;
        }
      });
      const total = (length / 1000).toFixed(5);
      if (dataForm) {
        dataForm.mainLineLength = total;
      }
      if (detail) {
        detail.mainLineLength = total;
      }
    } else {
      dataForm && (dataForm.mainLineLength = 0);
      if (detail) {
        detail.mainLineLength = 0;
      }
    }
  } catch (error) {
    console.log(error);
    SLMessage.error('计算失败');
  }
};
const detailRefreshed = () => {
  clearGraphicsLayer();
  refreshTree();
  partition.refreshPartitions(staticState.view);
};
const { initSketch, destroySketch } = useSketch();

const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view;
  refreshTree();
  await partition.refreshPartitions(staticState.view);

  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'partition-new-layer',
    title: '绘制新分区'
  });

  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  });
  refDrawerBox.value?.toggleDrawer('rtl', true);
};
// onMounted(async () => {
//   staticState.view = await refMap.value?.init()
//   refreshTree()
//   await partition.refreshPartitions(staticState.view)

//   staticState.graphicsLayer = getGraphicLayer(staticState.view, {
//     id: 'partition-new-layer',
//     title: '绘制新分区'
//   })

//   staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
//     updateCallBack: resolveDrawEnd,
//     createCallBack: resolveDrawEnd
//   })
//   refDrawerBox.value?.toggleDrawer('rtl', true)
// })
onBeforeUnmount(() => {
  destroySketch();
});
</script>
<style lang="scss" scoped>
.import-hookup-revenue {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .btns {
    height: 100px;
    display: flex;
    flex-direction: row;
    place-items: center;
  }
  .text {
    line-height: 50px;
  }
}
</style>
