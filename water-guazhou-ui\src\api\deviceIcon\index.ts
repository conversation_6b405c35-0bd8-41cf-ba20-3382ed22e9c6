import request from '@/plugins/axios'

/**
 * 分页查询设备图标
 * @param params 查询参数
 * @returns 设备图标列表
 */
export function getDeviceIconList(params: any) {
  // 确保包含必要的分页参数
  const defaultParams = {
    page: 1,
    size: 10
  };

  return request({
    url: '/api/deviceIcon',
    method: 'get',
    params: { ...defaultParams, ...params }
  });
}

/**
 * 根据ID查询设备图标
 * @param id 设备图标ID
 * @returns 设备图标详情
 */
export function getDeviceIconById(id: string) {
  return request({
    url: `/api/deviceIcon/${id}`,
    method: 'get'
  });
}

/**
 * 根据设备类型查询设备图标
 * @param deviceType 设备类型
 * @returns 设备图标列表
 */
export function getDeviceIconByDeviceType(deviceType: string) {
  return request({
    url: `/api/deviceIcon/deviceType/${deviceType}`,
    method: 'get'
  });
}

/**
 * 根据设备类型和设备状态查询设备图标
 * @param deviceType 设备类型
 * @param deviceStatus 设备状态
 * @returns 设备图标
 */
export function getDeviceIconByDeviceTypeAndStatus(deviceType: string, deviceStatus: string) {
  return request({
    url: `/api/deviceIcon/deviceType/${deviceType}/status/${deviceStatus}`,
    method: 'get'
  });
}

/**
 * 保存设备图标
 * @param data 设备图标数据
 * @returns 操作结果
 */
export function saveDeviceIcon(data: any) {
  return request({
    url: '/api/deviceIcon',
    method: 'post',
    data
  });
}

/**
 * 批量保存设备图标
 * @param data 批量保存数据
 * @returns 操作结果
 */
export function batchSaveDeviceIcon(data: any) {
  return request({
    url: '/api/deviceIcon/batch',
    method: 'post',
    data
  });
}

/**
 * 删除设备图标
 * @param id 设备图标ID
 * @returns 操作结果
 */
export function deleteDeviceIcon(id: string) {
  return request({
    url: `/api/deviceIcon/${id}`,
    method: 'delete'
  });
}

/**
 * 根据设备类型删除设备图标
 * @param deviceType 设备类型
 * @returns 操作结果
 */
export function deleteDeviceIconByDeviceType(deviceType: string) {
  return request({
    url: `/api/deviceIcon/deviceType/${deviceType}`,
    method: 'delete'
  });
}
