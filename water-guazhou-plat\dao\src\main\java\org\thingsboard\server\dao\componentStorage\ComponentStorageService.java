package org.thingsboard.server.dao.componentStorage;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.ComponentStorageEntity;

import java.util.List;

public interface ComponentStorageService {
    ComponentStorageEntity get(String id);

    PageData<ComponentStorageEntity> findList(int page, int size, String code, String name, String type, TenantId tenantId);

    List<ComponentStorageEntity> all(TenantId tenantId);

    List<String> specificationList(TenantId tenantId);

    Object save(ComponentStorageEntity entity, User currentUser);

    void remove(List<String> ids);

    void save(ComponentStorageEntity entity);

    void save(List<ComponentStorageEntity> updateList);
}
