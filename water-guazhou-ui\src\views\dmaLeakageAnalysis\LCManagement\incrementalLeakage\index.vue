<!-- 增量漏失评估 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>

    <stationDetailMonitoring
      v-if="state.isDetail"
      :device="TableConfig.currentRow"
      @hiddenLoading="TreeData.loading = false"
      @back="state.isDetail = false"
    >
    </stationDetailMonitoring>
    <template v-else>
      <CardSearch
        ref="refSearch"
        :config="SearchConfig"
      ></CardSearch>
      <CardTable
        ref="refTable"
        class="card-table"
        :config="TableConfig"
      ></CardTable>
    </template>
    <NewOrder
      ref="refDialog"
      :default-values="{ partitionId: TableConfig.selectList?.[0]?.partitionId }"
    ></NewOrder>
  </TreeBox>
</template>
<script lang="ts" setup>
import { reactive } from 'vue'
import stationDetailMonitoring from '../stockLeakage/components/stationDetailMonitoring.vue'
import { ICardTableIns, ISearchIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { usePartition } from '@/hooks/arcgis'
import {
  EditDMAPartitionStatus,
  EDMAStatus,
  GetDMAProgressLossControlList
} from '@/api/mapservice/dma'
import NewOrder from '../components/NewOrder.vue'
import { formatterDate, formatterDateTime } from '@/utils/GlobalHelper'
import { formatDate } from '@/utils/DateFormatter'

const state = reactive<{
  isDetail: boolean
}>({
  isDetail: false
})
const refTable = ref<ICardTableIns>()
const refSearch = ref<ISearchIns>()
const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: true,
  title: '选择分区',
  expandOnClickNode: false,
  treeNodeHandleClick: async (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      TreeData.currentProject = data
      refreshData()
    }
  }
})
// 列表模式搜索配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    date: moment().format(formatterDate)
  },
  filters: [
    {
      type: 'date',
      label: '日期',
      field: 'date',
      clearable: false,
      format: formatterDate
    },
    {
      type: 'input',
      label: '分区名称',
      field: 'name'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '工单',
          type: 'success',
          iconifyIcon: 'ep:plus',
          click: () => {
            if (!TableConfig.selectList?.length) {
              SLMessage.warning('请选择一条数据')
            } else if (TableConfig.selectList.length > 1) {
              SLMessage.warning('只能选择一条数据')
            } else {
              refDialog.value?.openDialog()
            }
          }
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ]
})

// 列表
const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'partitionName', label: '分区名称', minWidth: 220 },
    { prop: 'statusName', label: '分区状态', minWidth: 120 },
    { prop: 'userNum', label: '用户数(户)', minWidth: 120 },
    { prop: 'inlet', label: '进水口(个)', minWidth: 120 },
    {
      prop: 'supplyTotal',
      label: '供水量',
      unit: '(m³)',
      minWidth: 120
    },
    {
      prop: 'minTime',
      label: '夜间最小流时间',
      minWidth: 160,
      sortable: true,
      formatter(row, value) {
        return formatDate(value, formatterDateTime)
      }
    },
    {
      prop: 'minFlow',
      label: '夜间最小流',
      unit: '(m³/h)',
      minWidth: 220,
      sortable: true
    },
    {
      prop: 'minValue',
      label: '夜间最小水量值',
      unit: '(m³)',
      minWidth: 210,
      sortable: true
    },
    {
      label: '基准值时间',
      prop: 'incrTime',
      minWidth: 160,
      formatter(row, value) {
        return formatDate(value, formatterDateTime)
      }
    },
    {
      label: '基准值(m³/h)',
      prop: 'incrBase',
      minWidth: 140
    },
    {
      label: '黄色预警值',
      prop: 'incrWarn',
      minWidth: 140
    },
    {
      label: '红色预警值',
      prop: 'incrError',
      minWidth: 140
    }
  ],
  operations: [
    {
      text: '切评估',
      isTextBtn: true,
      perm: true,
      iconifyIcon: 'ep:edit',
      click: row => shiftStatus(row)
    }
  ],
  operationWidth: '100px',
  handleRowDbClick(row) {
    TableConfig.currentRow = row
    state.isDetail = true
  },
  handleSelectChange(val) {
    TableConfig.selectList = val
  },
  singleSelect: true,
  select(row) {
    if (
      TableConfig.selectList?.length
      && TableConfig.selectList.findIndex(
        item => item.partitionId === row.partitionId
      ) !== -1
    ) {
      TableConfig.selectList = []
    } else {
      TableConfig.selectList = [row]
    }
  },
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const shiftStatus = (row: any) => {
  SLConfirm('是否将状态修改为评估中?', '提示信息')
    .then(async () => {
      try {
        const res = await EditDMAPartitionStatus({
          id: row.partitionId,
          status: EDMAStatus.PingGuZhong
        })
        if (res.data.code === 200) {
          SLMessage.success('操作成功')
          refreshData()
        } else {
          SLMessage.success('操作失败')
        }
      } catch (error) {
        SLMessage.error('操作失败')
      }
    })
    .catch(() => {
      //
    })
}
const refDialog = ref<InstanceType<typeof NewOrder>>()
// 数据获取
const refreshData = async () => {
  if (!TreeData.currentProject) return
  const query = refSearch.value?.queryParams || {}
  const res = await GetDMAProgressLossControlList({
    ...query,
    partitionId: TreeData.currentProject?.value
  })
  TableConfig.dataList = res.data?.data || []
  TableConfig.pagination.total = res.data?.total || 0
}

const partition = usePartition()
onMounted(async () => {
  const p1 = partition.getTree()
  const p2 = partition.getList()
  await Promise.all([p1, p2])

  TreeData.data = partition.Tree.value || []
  TreeData.currentProject = TreeData.data[0]
  refreshData()
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}
</style>
