/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import com.datastax.driver.core.utils.UUIDs;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.alarm.AlarmJsonId;
import org.thingsboard.server.common.data.alarm.AttrAlarmJson;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.BaseEntity;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static org.thingsboard.server.dao.model.ModelConstants.*;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ALARM_JSON)
public final class AlarmJsonEntity extends BaseSqlEntity<AttrAlarmJson> implements BaseEntity<AttrAlarmJson> {

    @Column(name = ALARM_TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ALARM_SEVERITY_PROPERTY)
    private String severity;

    @Type(type = "json")
    @Column(name = ModelConstants.ASSET_ADDITIONAL_INFO_PROPERTY)
    private JsonNode details;

    @Column(name = ALARM_PROPAGATE_PROPERTY)
    private Boolean propagate;

    @Column(name = ModelConstants.ALARM_ATTRIBUTE)
    private String attribute;

    @Column(name = ModelConstants.ALARM_PARAMS)
    private String params;
    //检测报警js
    @Column(name = ModelConstants.ALARM_SCRIPT)
    private String alarmScript;
    //检测恢复js
    @Column(name = ModelConstants.ALARM_RESTORE_SCRIPT)
    private String restoreScript;
    //报警类型
    @Column(name = ModelConstants.ALARM_TYPE)
    private String alarmType;

    @Column(name = ModelConstants.ALARM_ALARM_NAME)
    private String alarmName;

    @Column(name = ModelConstants.DEVICE_ID_PROPERTY)
    private String deviceId;

    @Column(name = ModelConstants.ALARM_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.ALARM_PERIOD)
    private String period;

    @Column(name = ModelConstants.ALARM_RESTORE_TYPE)
    private String restoreType;

    @Column(name = ALARM_CYCLE)
    private String cycle;

    @Column(name = ALARM_IS_CYCLE)
    private Boolean isCycle;

    @Column(name = ALARM_GROUP)
    private String group;


    public AlarmJsonEntity() {
        super();
    }

    public AlarmJsonEntity(AttrAlarmJson alarm) {
        if (alarm.getId() != null) {
            this.setId(alarm.getId().getId());
        }
        if (alarm.getTenantId() != null) {
            this.tenantId = UUIDConverter.fromTimeUUID(alarm.getTenantId().getId());
        }
        if (alarm.getDeviceId() != null) {
            this.deviceId = UUIDConverter.fromTimeUUID(alarm.getDeviceId().getId());
        }
        this.severity = alarm.getSeverity();
        this.propagate = alarm.isPropagate();
        this.details = alarm.getDetails();
        this.alarmName = alarm.getAlarmName();
        this.alarmScript = alarm.getAlarmScript();
        this.alarmType = alarm.getAlarmType();
        this.restoreScript = alarm.getRestoreScript();
        this.attribute = alarm.getAttribute();
        this.createTime = alarm.getCreateTime();
        this.params = alarm.getParams();
        this.period = alarm.getPeriod();
        this.restoreType = alarm.getRestoreType();
        this.cycle = alarm.getCycle();
        this.isCycle = alarm.getIsCycle();
        this.group = alarm.getGroupName();
    }

    @Override
    public AttrAlarmJson toData() {
        AttrAlarmJson alarm = new AttrAlarmJson(new AlarmJsonId(UUIDConverter.fromString(id)));
        alarm.setCreatedTime(UUIDs.unixTimestamp(UUIDConverter.fromString(id)));
        if (tenantId != null) {
            alarm.setTenantId(new TenantId(UUIDConverter.fromString(tenantId)));
        }
        if (deviceId != null) {
            alarm.setDeviceId(new DeviceId(UUIDConverter.fromString(deviceId)));
        }
        alarm.setAlarmName(alarmName);
        alarm.setAlarmScript(alarmScript);
        alarm.setRestoreScript(restoreScript);
        alarm.setParams(params);
        alarm.setAttribute(attribute);
        alarm.setSeverity(severity);
        alarm.setPropagate(propagate);
        alarm.setDetails(details);
        alarm.setPeriod(period);
        alarm.setAlarmType(alarmType);
        alarm.setRestoreType(restoreType);
        alarm.setCycle(cycle);
        alarm.setIsCycle(isCycle);
        alarm.setGroupName(group);
        return alarm;
    }

}