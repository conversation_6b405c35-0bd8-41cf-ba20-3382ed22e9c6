package org.thingsboard.server.dao.gis;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisExceptionUploadListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisExceptionUpload;
import org.thingsboard.server.dao.sql.gis.GisExceptionUploadMapper;
import org.thingsboard.server.dao.user.UserService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GisExceptionUploadServiceImpl implements GisExceptionUploadService {

    @Autowired
    private GisExceptionUploadMapper gisExceptionUploadMapper;

    @Autowired
    private UserService userService;


    @Override
    public PageData<GisExceptionUpload> findList(GisExceptionUploadListRequest request, TenantId tenantId) {
        Page<GisExceptionUpload> pageRequest = new Page<>(request.getPage(), request.getSize());
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        IPage<GisExceptionUpload> pageResult = gisExceptionUploadMapper.findList(pageRequest, request);

        // 查询用户列表
        List<User> userList = userService.findUserByTenant(new TenantId(UUIDConverter.fromString(request.getTenantId())));
        Map<String, User> userMap = userList.stream()
                .collect(Collectors.toMap(user -> UUIDConverter.fromTimeUUID(user.getUuidId()), user -> user));

        for (GisExceptionUpload record : pageResult.getRecords()) {
            // 上报人
            String uploadUser = record.getUploadUser();
            if (StringUtils.isNotBlank(uploadUser)) {
                User upload = userMap.get(uploadUser);
                if (upload != null) {
                    record.setUploadUserName(upload.getFirstName());
                }
            }

            // 审批人
            String approvalUser = record.getApprovalUser();
            if (StringUtils.isNotBlank(approvalUser)) {
                User approval = userMap.get(approvalUser);
                if (approval != null) {
                    record.setApprovalUserName(approval.getFirstName());
                }
            }
        }

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void save(GisExceptionUpload entity, User currentUser) {
        entity.setUploadUser(UUIDConverter.fromTimeUUID(currentUser.getUuidId()));
        entity.setUploadTime(new Date());
        entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));
        entity.setStatus("0");

        gisExceptionUploadMapper.insert(entity);
    }

    @Override
    public void approval(String id, String status, String remark, User currentUser) {
        GisExceptionUpload exceptionUpload = gisExceptionUploadMapper.selectById(id);
        if (exceptionUpload != null) {
            exceptionUpload.setApprovalUser(UUIDConverter.fromTimeUUID(currentUser.getUuidId()));
            exceptionUpload.setApprovalTime(new Date());
            exceptionUpload.setStatus(status);
            exceptionUpload.setRemark(remark);

            gisExceptionUploadMapper.updateById(exceptionUpload);
        }
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            gisExceptionUploadMapper.deleteById(id);
        }
    }
}
