package org.thingsboard.server.dao.model.sql.report;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 * 票据
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.REPORT_DATABASE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class ReportDatabase {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.REPORT_DATABASE_NAME)
    private String name;

    @TableField(ModelConstants.REPORT_DATABASE_TYPE)
    private String type;

    @TableField(ModelConstants.REPORT_DATABASE_HOST)
    private String host;

    @TableField(ModelConstants.REPORT_DATABASE_PORT)
    private Integer port;

    @TableField(ModelConstants.REPORT_DATABASE_SERVER)
    private String server;

    @TableField(ModelConstants.REPORT_DATABASE_USERNAME)
    private String username;

    @TableField(ModelConstants.REPORT_DATABASE_PASSWORD)
    private String password;

    @TableField(ModelConstants.REMARK)
    private String remark;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
