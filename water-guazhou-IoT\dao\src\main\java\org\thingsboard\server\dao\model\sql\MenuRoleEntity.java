/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.MenuCustomerId;
import org.thingsboard.server.common.data.id.RoleId;
import org.thingsboard.server.common.data.role.MenuRole;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.CUSTOMER_MENU_ROLE)
public class MenuRoleEntity extends BaseSqlEntity<MenuRole> {

    @Column(name = ModelConstants.CUSTOMER_MENU_ROLE_MENU_ID)
    private String menuId;

    @Column(name = ModelConstants.CUSTOMER_MENU_ROLE_ROLE_ID)
    private String roleId;

    public MenuRoleEntity() {
    }

    public MenuRoleEntity(MenuRole menuRole) {
        this.id = menuRole.getId();
        this.menuId = toString(menuRole.getMenuId().getId());
        this.roleId = toString(menuRole.getRoleId().getId());
    }

    @Override
    public MenuRole toData() {
        MenuRole menuRole = new MenuRole();
        menuRole.setId(id);
        menuRole.setMenuId(new MenuCustomerId(toUUID(menuId)));
        menuRole.setRoleId(new RoleId(toUUID(roleId)));

        return menuRole;
    }
}
