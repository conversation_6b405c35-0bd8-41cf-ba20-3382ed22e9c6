<template>
  <div class="table-wrapper">
    <div
      class="table-container"
      :class="config.pagination.hide ? 'table-full' : ''"
    >
      <el-table
        ref="refElTable"
        v-loading="config.loading"
        style="width: 100%"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        class="table-custom"
        :border="true"
        :stripe="config.stripe"
        :data="config.dataList"
        :height="config.height === 'none' ? undefined : config.height || '100%'"
        :max-height="
          config.maxHeight === 'none' ? undefined : config.maxHeight || '100%'
        "
        :header-row-class-name="'color-header'"
        :span-method="config.spanMethod"
        :show-summary="config.showSummary"
        :summary-method="getSummaries"
        :highlight-current-row="config.highlightCurrentRow !== false"
        @row-dblclick="config.handleRowDbClick"
        @row-click="config.handleRowClick"
        @selection-change="config.handleSelectChange"
      >
        <!-- checkbox 根据是否配置了 selectChange 选项来控制显隐 -->
        <el-table-column
          v-if="config.handleSelectChange"
          :align="'center'"
          type="selection"
          width="55px"
        ></el-table-column>
        <!-- 序号列 根据 indexVisible 判断是否有序号列 -->

        <el-table-column
          v-if="config.indexVisible"
          :label="
            typeof config.indexVisible === 'boolean'
              ? '序号'
              : config.indexVisible?.label || '序号'
          "
          type="index"
          :align="
            typeof config.indexVisible === 'boolean'
              ? 'center'
              : config.indexVisible.align || 'center'
          "
          :width="
            typeof config.indexVisible === 'boolean'
              ? '55px'
              : config.indexVisible.width || '55px'
          "
          :fixed="
            typeof config.indexVisible === 'boolean'
              ? 'left'
              : config.indexVisible.fixed || 'left'
          "
        ></el-table-column>
        <el-table-column
          v-if="config.sort"
          :label="'排行'"
          :width="'80px'"
          :prop="config.sort.prop"
          align="center"
        >
          <template #default="scope">
            <span
              v-if="scope.$index + 1 === 1"
              :class="{ 'sort-1': config.sort?.showBackground }"
            >{{ scope.$index + 1 }}</span>
            <span
              v-else-if="scope.$index + 1 === 2"
              :class="{ 'sort-2': config.sort?.showBackground }"
            >{{ scope.$index + 1 }}</span>
            <span
              v-else-if="scope.$index + 1 === 3"
              :class="{ 'sort-3': config.sort?.showBackground }"
            >{{ scope.$index + 1 }}</span>
            <span v-else>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <!-- 遍历列配置 -->
        <TableColumn
          v-for="item in config.columns"
          :key="item.prop"
          :config="item"
        ></TableColumn>

        <!-- 操作 -->
        <el-table-column
          v-if="config.operations && operationVisible"
          label="操作"
          :fixed="config.operationFixed || 'right'"
          :header-align="'left'"
          :align="'center'"
          :width="config.operationWidth || '120px'"
        >
          <template #default="scope">
            <div class="operation-btn-box">
              <SLButton
                v-for="(btn, i) in config.operations"
                :key="i"
                :size="config.operationSize || 'small'"
                :config="btn"
                :row="scope.row"
              ></SLButton>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <SLPagination
      v-if="!config.pagination?.hide"
      :config="config.pagination"
    ></SLPagination>
  </div>
</template>

<script lang="ts" setup>
import { BigNumber } from 'bignumber.js'
import { IElTable } from '@/common/types/element-plus'
import { ISLTableColumn, ISLTableConfig } from './type'
import { fileStrToArr } from '@/utils/GlobalHelper'
import { TrueExcel } from '@/utils/exportExcel'

const props = defineProps<{ config: ISLTableConfig }>()
const emit = defineEmits(['change'])

const refElTable = ref<IElTable>()
// 是否有有效的operation
const operationVisible = computed(() => props.config.operations?.some(item => item.perm))
const pic = (image?: any, preview?: boolean) => {
  if (!image) return preview ? [] : ''
  const images = image instanceof Array ? image : fileStrToArr(image)
  return preview ? images : images[0]
}
/**
 * 设置选中项
 */
const toggleRowSelection = () => {
  props.config.selectList?.forEach((item: any) => {
    refElTable.value?.toggleRowSelection(item, true)
  })
}

const getSummaries = (param: any) => {
  if (!props.config.showSummary) return
  const { data } = param
  if (props.config.summaryMethod) {
    return props.config.summaryMethod(props.config.columns, data)
  }
  const sums: string[] = []
  const dealColums = (columns: ISLTableColumn[], startIndex: number) => {
    let preColumnChildenLength = 0
    columns.map(column => {
      if (column.subColumns?.length) {
        dealColums(column.subColumns, startIndex + preColumnChildenLength)
        preColumnChildenLength += column.subColumns.length
      } else {
        const index = startIndex + preColumnChildenLength
        if (column.summary) {
          const values = data.map(
            item => column.prop && Number(item[column.prop])
          )
          if (!values.every(value => Number.isNaN(value))) {
            sums[index] = `${column.preUnit || ''}${values.reduce(
              (prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                  return Number(new BigNumber(prev).plus(curr))
                }
                return prev
              },
              0
            )} ${column.unit || ''}`
          } else {
            sums[index] = '-'
          }
        } else {
          sums[index] = '-'
        }
        preColumnChildenLength++
      }
    })
  }
  let startIndex = 0
  if (props.config.indexVisible) startIndex++
  if (props.config.sort) startIndex++
  if (props.config.handleSelectChange) startIndex++
  dealColums(props.config.columns, startIndex)
  sums[0] = props.config.summaryDesc || '合计'
  return sums
}
const exportTable = () => {
  const excel = new TrueExcel()
  excel.addElTable(refElTable.value)
  excel.export()
}
watch(
  () => props.config.selectList,
  () => toggleRowSelection()
)
watch(
  () => props.config.dataList,
  (newVal: any) => {
    if (!newVal) newVal = []
    emit('change', newVal)
    const currentId = props.config.currentRow && props.config.currentRow[0]?.id
    const dataObj = currentId && props.config.dataList.find(item => item.id === currentId)
    dataObj && refElTable.value?.setCurrentRow(dataObj)
  }
)
onMounted(() => {
  toggleRowSelection()
})
defineExpose({
  operationVisible,
  refElTable,
  pic,
  getSummaries,
  exportTable
})
</script>

<style lang="scss" scoped>
.table-wrapper {
  height: 100%;
  width: 100%;
}
.table-container {
  height: calc(100% - 40px) !important;
  // padding-bottom: 5px;
  width: 100%;
  &.table-full {
    height: 100% !important;
  }
  // border: none;
}
.operation-btn-box {
  width: 100%;
  display: flex;
  justify-content: space-around;
}
.sort-1,
.sort-2,
.sort-3 {
  display: block;
  border-radius: 12px;
}
.sort-1 {
  background: #ff5a5a;
}
.sort-2 {
  background: #ff955a;
}
.sort-3 {
  background: #fed75a;
}
</style>
