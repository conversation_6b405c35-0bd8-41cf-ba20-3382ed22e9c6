package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitHandoverRecord;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

@Getter
@Setter
public class CircuitHandoverRecordSaveRequest extends SaveRequest<CircuitHandoverRecord> {

    // 值班地点
    private String dutyLocation;

    // 运维情况
    private String omRemark;

    // 泵房状态。状态值：正常、异常、危险
    private String pumpRoomStatus;

    // 日常记录
    private String record;

    // 交接班时间
    private Date shiftTime;

    // 交班人
    private String handoverPerson;

    // 接班人
    private String takeoverPerson;

    // 交接班说明
    private String handoverRemark;

    @Override
    protected CircuitHandoverRecord build() {
        CircuitHandoverRecord entity = new CircuitHandoverRecord();
        entity.setRecordTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected CircuitHandoverRecord update(String id) {
        CircuitHandoverRecord entity = new CircuitHandoverRecord();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(CircuitHandoverRecord entity) {
        entity.setDutyLocation(dutyLocation);
        entity.setOmRemark(omRemark);
        entity.setPumpRoomStatus(pumpRoomStatus);
        entity.setRecord(record);
        entity.setShiftTime(shiftTime);
        entity.setHandoverPerson(handoverPerson);
        entity.setTakeoverPerson(takeoverPerson);
        entity.setHandoverRemark(handoverRemark);
    }
}