<!-- 报警中心 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable class="card-table" :config="TableConfig" />
    <DialogForm ref="refForm" :config="addOrUpdateConfig"> </DialogForm>
    <SLDrawer ref="refdetail" :config="detailConfig">
      <detail :config="config"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import {
  getAlarmCenter,
  postAlarmWorkOrder,
  postClearAlarm
} from '@/api/shuiwureports/zhandian';
import { getUserslistByAuth } from '@/api/user';
import { ICONS } from '@/common/constans/common';
import useStation from '@/hooks/station/useStation';
import { useUserStore } from '@/store';
import { formatDate } from '@/utils/DateFormatter';
import { SLConfirm } from '@/utils/Message';
import { removeSlash } from '@/utils/removeIdSlash';
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import {
  WorkOrderDealLevel,
  getEmergencyLevelOpetions,
  getFromOptions,
  getOrderTypeOptions
} from './components/config';
import detail from './components/detail.vue';
import { alarmType } from './data';

const { getStationTree, getStationTreeByDisabledType } = useStation();

const refForm = ref<IDialogFormIns>();

const alarmLevel = [
  { label: '提醒报警', value: '1' },
  { label: '重要报警', value: '2' },
  { label: '紧急报警', value: '3' }
];

const alarmStatus = [
  { label: '报警中', value: '1' },
  { label: '已恢复', value: '2' },
  { label: '已解除', value: '3' }
];

const processStatus = [
  { label: '未处理', value: '1' },
  { label: '处理中', value: '2' },
  { label: '已处理', value: '3' }
];

const refSearch = ref<ICardSearchIns>();
const refdetail = ref<ISLDrawerIns>();

// 明细弹框
const detailConfig = reactive<IDrawerConfig>({
  title: '报警详情',
  cancel: false,
  className: 'lightColor',
  group: []
});

const config = ref({});

const cardSearchConfig = ref<ISearch>({
  defaultParams: {
    processStatus: '1',
    alarmStatus: '1'
  },
  filters: [
    {
      label: '站点',
      field: 'stationId',
      type: 'select-tree',
      multiple: true,
      options: computed(() => data.stationTree) as any
    },
    {
      label: '处理状态',
      field: 'processStatus',
      type: 'select',
      options: processStatus
    },
    {
      label: '报警类型',
      field: 'alarmType',
      type: 'select',
      options: alarmType
    },
    {
      label: '报警等级',
      field: 'alarmLevel',
      type: 'select',
      options: alarmLevel
    },
    {
      label: '报警状态',
      field: 'alarmStatus',
      type: 'select',
      options: alarmStatus
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '解除告警',
          click: () => {
            if (!TableConfig.selectList?.length) {
              ElMessage.warning('请选择需要解除的数据');
              return;
            }
            ClearAlarm();
          }
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  handleSelectChange(val) {
    TableConfig.selectList = val;
  },
  selectList: [],
  columns: [
    { label: '站点名称', prop: 'stationName' },
    // { label: '站点ID', prop: 'stationId' },
    { label: '报警标题', prop: 'title' },
    {
      label: '首次报警时间',
      prop: 'time',
      formatter: (row) => formatDate(row.time, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      label: '报警结束时间',
      prop: 'endTime',
      formatter: (row) => formatDate(row.endTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      label: '报警等级',
      prop: 'alarmLevel',
      width: '110px',
      formatter: (row) =>
        alarmLevel.find((item) => item.value === row.alarmLevel)?.label
    },
    {
      label: '报警类型',
      prop: 'alarmType',
      width: '110px',
      formatter: (row) =>
        alarmType.find((item) => item.value === row.alarmType)?.label
    },
    { label: '报警描述', prop: 'alarmInfo' },
    { label: '处理建议', prop: 'processMethod' },
    {
      label: '报警状态',
      prop: 'alarmStatus',
      width: '110px',
      formatter: (row) =>
        alarmStatus.find((item) => item.value === row.alarmStatus)?.label
    },
    {
      label: '处理状态',
      prop: 'processStatus',
      width: '110px',
      formatter: (row) =>
        processStatus.find((item) => item.value === row.processStatus)?.label
    },
    // { label: '工单ID', prop: 'workOrderId' },
    { label: '操作人', prop: 'optionUserName', width: '100px' }
  ],
  operationWidth: '170px',
  operations: [
    {
      perm: true,
      text: '解除告警',
      click: (val) => {
        ClearAlarm(val);
      }
    },
    // {
    //   perm: true,
    //   text: '工单处理',
    //   click: (val) => {
    //     WorkOrderProcessing(val);
    //   }
    // },
    {
      perm: true,
      text: '详情',
      click: (val) => {
        config.value = val;
        refdetail.value?.openDrawer();
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  dialogWidth: '1000px',
  labelWidth: '100px',
  submitting: false,
  submit: (params: any) => {
    const id = params.id;
    delete params.id;
    params.ccUserId = params.ccUserId.join(',');
    postAlarmWorkOrder(id, params).then((res) => {
      if (res.data.code === 200) {
        ElMessage.success('创建完成');
        refreshData();
        refForm.value?.closeDialog();
      } else {
        ElMessage.warning('创建失败');
      }
    });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '标题',
          field: 'title',
          rules: [{ required: true, message: '请输入标题' }]
        },
        {
          xs: 6,
          type: 'select',
          label: '来源',
          field: 'source',
          options: getFromOptions(),
          rules: [{ required: true, message: '请选择来源' }]
        },
        {
          xs: 6,
          type: 'select',
          label: '紧急程度',
          field: 'level',
          options: getEmergencyLevelOpetions(),
          rules: [{ required: true, message: '请选择紧急程度' }]
        },
        // {
        //   xs: 6,
        //   type: 'select-tree',
        //   label: '工单类型',
        //   field: 'type',
        //   options: getOrderTypeOptions(),
        //   rules: [{ required: true, message: '请选择工单类型' }]
        // },
        {
          field: 'address',
          label: '地址',
          type: 'textarea',
          rules: [{ required: true, message: '请输入地址' }]
        },
        { field: 'remark', label: '描述', type: 'textarea' }
      ]
      // fieldset: {
      //   desc: '工单信息',
      //   type: 'underline'
      // }
    },
    {
      fieldset: {
        desc: '分派信息',
        type: 'underline'
      },
      fields: [
        {
          type: 'switch',
          label: '是否直接派发',
          field: 'isDirectDispatch'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = !params.isDirectDispatch;
          },
          label: '处理人',
          xs: 6,
          field: 'stepProcessUserId',
          type: 'select',
          options: []
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = !params.isDirectDispatch;
          },
          xs: 6,
          field: 'processLevel',
          label: '处理级别',
          type: 'select',
          options: WorkOrderDealLevel()
        }
      ]
    },
    {
      fields: [
        {
          field: 'ccUserId',
          label: '抄送人',
          type: 'select',
          multiple: true,
          options: []
        }
      ],
      fieldset: {
        desc: '抄送',
        type: 'underline'
      }
    },
    {
      fields: [
        {
          field: 'imgUrl',
          label: '现场图片',
          type: 'image',
          returnType: 'comma',
          limit: 2,
          multiple: true,
          accept: 'image/*'
        },
        {
          field: 'videoUrl',
          label: '现场视频',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          accept: 'video/*',
          tips: '只能上传视频文件,最多上传2个，大小不能超过100M'
        },
        {
          field: 'audioUrl',
          label: '现场录音',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          accept: 'audio/*',
          tips: '只能上传音频文件,最多上传2个，大小不能超过4M'
        },
        {
          field: 'otherFileUrl',
          label: '其它附件',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传文件,最多上传2个，大小不能超过4M'
        }
      ]
    }
  ]
});

const data = reactive({
  stationTree: [] as any[]
});

function WorkOrderProcessing(params?: any) {
  addOrUpdateConfig.defaultValue = {
    id: params.id,
    address: params.stationName,
    remark: params.title,
    coordinate: '30.516663,104.070048',
    organizerId: useUserStore().user?.id?.id
  };
  refForm.value?.openDialog();
}

function ClearAlarm(params?: any) {
  SLConfirm('确定解除选中的告警, 是否继续?', '解除提示').then(async () => {
    let ids: any = [];

    if (params) {
      ids = [params.id];
    } else {
      ids = TableConfig.selectList?.map((item) => {
        return item.id;
      });
    }
    postClearAlarm(ids).then((res) => {
      if (res.data.code === 200) {
        ElMessage.success('解除告警成功');
        TableConfig.selectList = [];
        refreshData();
      } else {
        ElMessage.warning('解除告警失败');
      }
    });
  });
}

function traverse(val: any) {
  const k = val.map((item) => {
    if (item.children && item.children.length !== 0) {
      traverse(item.children);
    } else {
      item.disabled = false;
    }
    return item;
  });
  return k;
}

const initUserOptions = async () => {
  const res = await getUserslistByAuth({
    authType: 'CUSTOMER_USER'
  });
  const userField = ['uploadUserId', 'stepProcessUserId', 'ccUserId'];
  addOrUpdateConfig.group.map((group) => {
    const filters = group.fields.filter(
      (item) => item.field && userField.indexOf(item.field) !== -1
    ) as IFormSelect[];
    filters.map((item) => {
      item.options = res.data.map((item) => {
        return {
          label: item.firstName,
          value: removeSlash(item.id.id)
        };
      });
    });
  });
};

const refreshData = async () => {
  TableConfig.dataList = [];
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  };
  params.stationId && (params.stationId = params.stationId.join(','));
  getAlarmCenter(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  initUserOptions();
});

onBeforeMount(async () => {
  const treeData = (await getStationTree()) as any[];
  await getStationTreeByDisabledType(
    treeData,
    ['Project', 'Station'],
    false,
    'Station'
  );
  data.stationTree = traverse(treeData || []);
});

onActivated(() => {
  refreshData();
});
</script>
