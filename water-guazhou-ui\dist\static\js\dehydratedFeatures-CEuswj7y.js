import{k as l,s as e,D as a,o as r}from"./MapView-DaoQedLH.js";import{R as o,T as h}from"./index-r0dFAfgr.js";import"./Point-WxyopZva.js";class m{constructor(s,n,i){this.uid=s,this.geometry=n,this.attributes=i,this.visible=!0,this.objectId=null,this.centroid=null}}function f(t){return o(t.geometry)}class y{constructor(){this.exceededTransferLimit=!1,this.features=[],this.fields=[],this.hasM=!1,this.hasZ=!1,this.geometryType=null,this.objectIdFieldName=null,this.globalIdFieldName=null,this.geometryProperties=null,this.geohashFieldName=null,this.spatialReference=null,this.transform=null}}function g(t,s,n,i){return{x:t,y:s,z:n,hasZ:n!=null,hasM:!1,spatialReference:i,type:"point"}}function x(t){if(h(t))return 0;switch(t.type){case"point":return 1;case"polyline":{let s=0;for(const n of t.paths)s+=n.length;return s}case"polygon":{let s=0;for(const n of t.rings)s+=n.length;return s}case"multipoint":return t.points.length;case"extent":return 2;case"mesh":{const s=t.vertexAttributes&&t.vertexAttributes.position;return s?s.length/3:0}default:return}}function b(t,s){switch(l(s),t.type==="mesh"&&(t=t.extent),t.type){case"point":s[0]=s[3]=t.x,s[1]=s[4]=t.y,t.hasZ&&(s[2]=s[5]=t.z);break;case"polyline":for(let n=0;n<t.paths.length;n++)e(s,t.paths[n],!!t.hasZ);break;case"polygon":for(let n=0;n<t.rings.length;n++)e(s,t.rings[n],!!t.hasZ);break;case"multipoint":e(s,t.points,!!t.hasZ);break;case"extent":s[0]=t.xmin,s[1]=t.ymin,s[3]=t.xmax,s[4]=t.ymax,t.zmin!=null&&(s[2]=t.zmin),t.zmax!=null&&(s[5]=t.zmax)}}function d(t,s){switch(a(s),t.type==="mesh"&&(t=t.extent),t.type){case"point":s[0]=s[2]=t.x,s[1]=s[3]=t.y;break;case"polyline":for(let n=0;n<t.paths.length;n++)r(s,t.paths[n]);break;case"polygon":for(let n=0;n<t.rings.length;n++)r(s,t.rings[n]);break;case"multipoint":r(s,t.points);break;case"extent":s[0]=t.xmin,s[1]=t.ymin,s[2]=t.xmax,s[3]=t.ymax}}function k(t,s){return t.objectId!=null?t.objectId:t.attributes&&s?t.attributes[s]:null}export{b as I,g as M,k as R,f as b,y as d,m as g,d as v,x as z};
