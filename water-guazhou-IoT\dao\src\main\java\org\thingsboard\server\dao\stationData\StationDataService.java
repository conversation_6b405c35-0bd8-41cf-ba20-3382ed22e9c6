package org.thingsboard.server.dao.stationData;

import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.VO.DynamicTableVO;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

public interface StationDataService {
    List<DeviceFullData> getStationDataDetail(String stationId, String type, boolean customName, TenantId tenantId);

    Object findStationDataList(String stationId, String queryType, Date startTime, Date endTime, TenantId tenantId);

    Object getStationDataDetailList(String stationType, String projectId, TenantId tenantId);

    Object getStationDataDetailListView(String stationType, boolean customName, String projectId, TenantId tenantId);

    /**
     * 查询指定数据属性三天内的数据曲线
     *
     * @param deviceId 设备ID
     * @param attr     属性
     * @param tenantId 租户ID
     * @return 曲线数据
     */
    Object getThreeDaysData(String deviceId, String attr, TenantId tenantId);

    /**
     * 按日查询站点数据并返回
     *
     * @param stationId   站点ID
     * @param start       查询的开始时间
     * @param end         查询的结束时间
     * @param filterStart 筛选开始时间
     * @param filterEnd   筛选结束时间
     * @param queryType   查询的时间间隔
     * @param group       要查询的动态属性分组
     * @param attributeId 要查询的具体属性
     * @param tenantId    租户ID
     * @return 数据
     */
    DynamicTableVO stationDayDataQuery(String stationId, Long start, Long end, Integer filterStart, Integer filterEnd, String queryType, String group, String attributeId, TenantId tenantId) throws ThingsboardException;

    /**
     * 按日查询站点数据并返回
     *
     * @param stationId 站点ID
     * @param start     查询的开始时间
     * @param end       查询的结束时间
     * @param queryType 查询的时间间隔
     * @param group     要查询的动态属性分组
     * @param attrs     要查询的具体属性列表
     * @param tenantId  租户ID
     * @return 数据
     */
    DynamicTableVO stationDataQuery(String stationId, Long start, Long end, String queryType, String group, List<String> attrs, TenantId tenantId) throws ThingsboardException;

    /**
     * 按日查询站点数据并按日分组返回
     *
     * @param stationId   站点ID
     * @param start       查询的开始时间
     * @param end         查询的结束时间
     * @param filterStart 筛选开始时间
     * @param filterEnd   筛选结束时间
     * @param queryType   查询的时间间隔
     * @param attributeId 要查询的具体属性
     * @param tenantId    租户ID
     * @return 数据
     */
    Object stationAttrDataQueryGroupByDay(String stationId, Long start, Long end, Integer filterStart, Integer filterEnd, String queryType, String attributeId, TenantId tenantId) throws ThingsboardException;

    /**
     * 数据对比
     * 查询指定时间范围内指定数据项（可为多个）的历史数据列表
     * 返回的数据列为：数据项。方便多个数据项同一时间做对比
     *
     * @param attributes  数据项列表
     * @param queryType   查询的数据结构
     * @param start       查询时间范围的开始时间
     * @param end         查询时间范围的结束时间
     * @param filterStart 筛选开始时间
     * @param filterEnd   筛选结束时间
     * @param tenantId    租户ID
     * @return 数据
     */
    DynamicTableVO getDataCompare(String attributes, String queryType, Long start, Long end, Integer filterStart, Integer filterEnd, TenantId tenantId) throws ThingsboardException;

    /**
     * 周期对比
     * 查询指定时间范围内指定数据项（单个）的历史数据列表
     * 返回的数据列为：时间。方便同个数据项同一时间多个时间段的对比
     *
     * @param attributeId 数据项
     * @param queryType   查询类型
     * @param start       查询时间范围的开始时间
     * @param end         查询时间范围的结束时间
     * @param tenantId    租户ID
     * @return 数据
     */
    DynamicTableVO getCycleCompare(String attributeId, String queryType, Long start, Long end, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点所有数据点的今日数据曲线
     *
     * @param stationId 站点ID
     * @param tenantId  租户ID
     * @return 数据
     */
    Object todayDataList(String stationId, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定的站点实时数据并按照变量分组返回数据
     *
     * @param stationId 站点ID
     * @param tenantId  租户ID
     * @return 数据
     */
    Object getStationDataDetailByGroup(String stationId, TenantId tenantId) throws ThingsboardException;

    Object getStationDataDetailGroupListView(String stationType, String projectId, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询站点指定分组的所有数据点数据列表
     *
     * @param stationId   站点ID
     * @param groupType   属性分组
     * @param stationType 站点类型
     * @param time        查询时间
     * @param queryType   报表类型
     * @param tenantId    租户ID
     * @return 数据
     */
    DynamicTableVO getStationDataReport(String stationId, String groupType, String stationType, String time, String queryType, TenantId tenantId) throws Exception;

    /**
     * 查询站点指定分组的所有数据点数据列表的最大最小值报表
     *
     * @param stationId   站点ID
     * @param groupType   属性分组
     * @param stationType 站点类型
     * @param time        查询时间
     * @param queryType   报表类型
     * @param tenantId    租户ID
     * @return 数据
     */
    Object getMaxAndMinReport(String stationId, String groupType, String stationType, String time, String queryType, TenantId tenantId) throws Exception;

    /**
     * 查询站点原始数据列表
     *
     * @param stationId 站点ID
     * @param group     属性恩组
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param tenantId  租户ID
     * @return 数据
     */
    DynamicTableVO getStationOriginal(String stationId, String group, Date beginTime, Date endTime, TenantId tenantId);

    /**
     * 查询站点数据列表
     *
     * @param stationIds 站点ID列表
     * @return 数据
     */
    Object getStationDataByList(String stationIds, TenantId tenantId);

    Object getStationDataDetail(String stationId, String type, String property, TenantId tenantId);
}
