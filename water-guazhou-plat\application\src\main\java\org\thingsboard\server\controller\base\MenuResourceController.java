package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.menu2.MenuResourceService;
import org.thingsboard.server.dao.model.sql.MenuResource;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("api/menuResource")
public class MenuResourceController extends BaseController {

    @Autowired
    private MenuResourceService menuResourceService;

    @GetMapping("list")
    public PageData<MenuResource> list(@RequestParam int page, @RequestParam int size,
                                       @RequestParam(required = false, defaultValue = "") String name,
                                       @RequestParam(required = false, defaultValue = "") String url) {
        return menuResourceService.findList(page, size, name, url);
    }

    @GetMapping("findAll")
    public List<MenuResource> findAll() {
        return menuResourceService.findAll();
    }

    @PostMapping
    public MenuResource save(@RequestBody MenuResource menuResource) {
        return menuResourceService.save(menuResource);
    }

    @DeleteMapping
    public List<String> remove(@RequestBody List<String> ids) {
        menuResourceService.remove(ids);
        return ids;
    }

    @GetMapping("menuPoolToMenuResource")
    public void menuPoolToMenuResource() {
        menuResourceService.menuPoolToMenuResource();
    }

}
