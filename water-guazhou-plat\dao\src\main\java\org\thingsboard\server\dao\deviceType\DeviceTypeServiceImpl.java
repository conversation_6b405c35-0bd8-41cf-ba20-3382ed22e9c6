package org.thingsboard.server.dao.deviceType;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceTypeConstants;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypeSaveRequest;

import java.util.List;

@Service
public class DeviceTypeServiceImpl implements DeviceTypeService {
    @Autowired
    private DeviceTypeMapper mapper;

    @Override
    public IPage<DeviceType> findAllConditional(DeviceTypePageRequest request) {
        return mapper.findAllConditional(request);
    }

    @Override
    public List<DeviceType> findAllStructure(String tenantId) {
        DeviceType fakeRoot = DeviceTypeConstants.TOP_ROOT;
        return QueryUtil.buildTreeWithFakeRoot(mapper.findRoots(tenantId), fakeRoot, mapper::findChildren);
    }

    @Override
    public DeviceType save(DeviceTypeSaveRequest deviceType) {
        DeviceType result = QueryUtil.saveOrUpdateOneByRequest(deviceType, mapper::insert, mapper::update);
        result.setLevel(Integer.toString(mapper.getDepth(result.getId())));
        return result;
    }

    @Override
    public boolean update(DeviceType deviceType) {

        return mapper.update(deviceType);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public Integer getDepth(String id) {
        if (id == null)
            return 0;

        return mapper.getDepth(id);
    }

    @Override
    public boolean canBeDelete(String id, String tenantId) {
        return mapper.canBeDelete(id, tenantId);
    }

    @Override
    public String getSerialId(String id) {
        return mapper.getSerialId(id);
    }

    @Override
    public boolean existsBySerialId(String serialId, String tenantId) {
        return mapper.existsBySerialId(serialId, tenantId);
    }

    @Override
    public String getParentId(String id) {
        return mapper.getParentId(id);
    }

    @Override
    public boolean isSerialIdExists(String serialId, String id, String tenantId) {
        return mapper.isSerialIdExists(serialId, id, tenantId);
    }

    @Override
    public int getDepthBySerialId(String serialId, String tenantId) {
        return mapper.getDepthBySerialId(serialId, tenantId);
    }
}
