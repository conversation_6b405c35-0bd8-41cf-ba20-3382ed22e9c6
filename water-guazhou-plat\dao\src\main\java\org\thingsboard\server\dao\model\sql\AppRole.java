package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.APP_ROLE_TABLE)
public class AppRole {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.APP_ROLE_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.APP_ROLE_NAME)
    private String name;

    @Column(name = ModelConstants.APP_ROLE_ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.APP_ROLE_TYPE_ID)
    private String appTypeId;

}
