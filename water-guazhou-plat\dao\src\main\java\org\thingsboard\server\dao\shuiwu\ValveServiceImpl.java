package org.thingsboard.server.dao.shuiwu;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.ValveEntity;
import org.thingsboard.server.dao.sql.shuiwu.ValveRepository;

import java.util.List;

@Slf4j
@Service
public class ValveServiceImpl implements ValveService {

    @Autowired
    private ValveRepository valveRepository;


    @Override
    public ValveEntity findById(String id) {
        return valveRepository.findOne(id);
    }

    @Override
    public PageData<ValveEntity> findList(int page, int size, String name, TenantId tenantId) {
        PageRequest pageRequest = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");

        Page<ValveEntity> pageResult = valveRepository.findList(name, UUIDConverter.fromTimeUUID(tenantId.getId()), pageRequest);

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public ValveEntity save(ValveEntity valveEntity) {
        return valveRepository.save(valveEntity);
    }

    @Override
    public void delete(List<String> ids) {
        for (String id : ids) {
            valveRepository.delete(id);
        }
    }
}
