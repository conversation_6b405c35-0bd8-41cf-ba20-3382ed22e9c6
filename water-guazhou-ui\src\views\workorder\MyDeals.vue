<!-- 统一工单-我的工单-由我处理 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable
      :key="key"
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    />
    <DialogForm ref="refForm_Trans" :config="FormConfig_Trans"></DialogForm>
    <DialogForm ref="refForm_Colab" :config="FormConfig_Colab"></DialogForm>
    <DialogForm ref="refForm_Arrive" :config="FormConfig_Arrive"></DialogForm>
    <DialogForm ref="refForm_Deal" :config="FormConfig_Deal"></DialogForm>
    <DialogForm ref="refForm_Comp" :config="FormConfig_Comp"></DialogForm>
    <DialogForm
      ref="refForm_ChargeBack"
      :config="FormConfig_ChargeBack"
    ></DialogForm>
    <DialogForm ref="refFormReview" :config="FormConfig_Review"></DialogForm>
    <SLDrawer ref="refdetail" :config="DrawerConfig">
      <detail :id="TableConfig.currentRow?.id"></detail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref, shallowRef } from 'vue';
import detail from './components/detail.vue';
import OrderStepTagsVue from './components/OrderStepTags.vue';
import {
  getEmergencyLevelOpetions,
  getFromOptions,
  getOrderTypeOptions
} from './config';
import {
  AddWorkOrderStage,
  ChargeBackWorkOrderApply,
  HandOverWorkOrderApply,
  ReceiveWorkOrder,
  ReviewWorkOrder,
  collaborativeApplication,
  GetWorkOrderPage,
  getWorkOrderEmergencyLevelList
} from '@/api/workorder';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { useUserStore } from '@/store';
import { formatterDate, traverse } from '@/utils/GlobalHelper';
import useUser from '@/hooks/user/useUser';
import { removeSlash } from '@/utils/removeIdSlash';
import { formatDate } from '@/utils/DateFormatter';

const { getUserOptions } = useUser();

const refForm_Trans = ref<IDialogFormIns>();
const refForm_Colab = ref<IDialogFormIns>();
const refForm_Arrive = ref<IDialogFormIns>();
const refFormReview = ref<IDialogFormIns>();
const refForm_Deal = ref<IDialogFormIns>();
const refForm_Comp = ref<IDialogFormIns>();
const refForm_ChargeBack = ref<IDialogFormIns>();
const refSearch = ref<ICardSearchIns>();
const refTable = ref<ICardTableIns>();

const state = reactive<{
  WorkOrderEmergencyLevelList: any[];
}>({
  WorkOrderEmergencyLevelList: []
});

function initOptions() {
  // 紧急程度
  getWorkOrderEmergencyLevelList('1').then((res) => {
    state.WorkOrderEmergencyLevelList = traverse(
      res.data.data || [],
      'children',
      { label: 'name', value: 'id' }
    );
  });
}

const key = ref('0');
// 明细弹框
const refdetail = ref<ISLDrawerIns>();
const DrawerConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  cancel: false,
  className: 'lightColor',
  group: []
});
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      field: 'stageBetween',
      label: '工单状态',
      labelWidth: 40,
      type: 'radio-button',
      options: [
        { label: '待接收', value: 'ASSIGN' },
        { label: '待完成', value: 'RESOLVING' },
        { label: '已完成', value: 'SUBMIT' }
      ],
      onChange: (val) => {
        const perms =
          val === 'ASSIGN'
            ? ['详情', '接收']
            : val === 'RESOLVING'
              ? ['详情', '到场', '处理', '完成', '退单']
              : ['详情', '复审'];
        const permWidth =
          val === 'ASSIGN' ? 100 : val === 'RESOLVING' ? 320 : 100;
        TableConfig.operationWidth = permWidth;
        TableConfig.operations?.map((item) => {
          const text: string = item.text as string;
          item.perm = perms.indexOf(text) !== -1;
        });
        refreshData();
      }
    },
    {
      field: 'title',
      label: '标题',
      type: 'input',
      onChange: () => refreshData()
    },
    { field: 'date', label: '发起时间', type: 'daterange' },
    {
      field: 'source',
      label: '来源',
      type: 'select',
      options: getFromOptions()
    },
    {
      field: 'level',
      label: '紧急程度',
      type: 'select',
      options: getEmergencyLevelOpetions()
    },
    {
      field: 'type',
      label: '类型',
      type: 'select-tree',
      options: getOrderTypeOptions()
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          iconifyIcon: 'ep:refresh',
          click: () => resetForm()
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          iconifyIcon: 'ep:download',
          click: () => exportTable()
        }
      ]
    }
  ],
  defaultParams: {
    stageBetween: 'ASSIGN',
    date: [
      moment().subtract(1, 'M').format(formatterDate),
      moment().format(formatterDate)
    ]
  },
  handleSearch: () => refreshData()
});
const TableConfig = reactive<ICardTable>({
  expandable: true,
  expandComponent: shallowRef(OrderStepTagsVue),
  columns: [
    { minWidth: 180, prop: 'serialNo', label: '工单编号' },
    { minWidth: 100, prop: 'title', label: '标题' },
    { minWidth: 120, prop: 'source', label: '来源' },
    { minWidth: 120, prop: 'type', label: '类型' },
    {
      minWidth: 120,
      prop: 'level',
      label: '紧急程度',
      tag: true,
      tagColor: (row): string =>
        state.WorkOrderEmergencyLevelList.find(
          (item) => item.value === row.level
        )?.color || '',
      formatter: (row) =>
        state.WorkOrderEmergencyLevelList.find(
          (item) => item.value === row.level
        )?.label
    },
    { minWidth: 100, prop: 'organizerName', label: '发起人' },
    {
      minWidth: 160,
      prop: 'createTime',
      label: '发起时间',
      formatter: (row) => formatDate(row.createTime)
    },

    { minWidth: 120, prop: 'processUserName', label: '处理人' },
    {
      minWidth: 120,
      prop: 'processLevelLabel',
      label: '处理级别'
    },
    {
      minWidth: 160,
      prop: 'estimatedFinishTime',
      label: '预计完成时间',
      formatter: (row) => formatDate(row.estimatedFinishTime)
    },
    {
      minWidth: 160,
      prop: 'completeTime',
      label: '完成时间',
      formatter: (row) => formatDate(row.completeTime)
    },
    {
      minWidth: 160,
      prop: 'updateTime',
      label: '最后更新时间',
      formatter: (row) => formatDate(row.updateTime)
    }
  ],
  defaultExpandAll: true,
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  },
  operationWidth: 100,
  operations: [
    {
      perm: true,
      text: '详情',
      isTextBtn: true,
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      text: '接收',
      isTextBtn: true,
      click: (row) => {
        TableConfig.currentRow = row;
        handleReceive();
      }
    },
    {
      perm: false,
      text: '转发',
      isTextBtn: true,
      click: (row) => {
        TableConfig.currentRow = row;
        // FormConfig_Trans.defaultValue = {
        //   processUserId: row.processUserId,
        //   level: row.level,
        //   estimatedFinishTime: row.estimatedFinishTime
        // }
        refForm_Trans.value?.openDialog();
      }
    },
    {
      perm: false,
      text: '协作',
      click: (row) => {
        TableConfig.currentRow = row;
        FormConfig_Colab.defaultValue = { type: row.source };
        refForm_Colab.value?.openDialog();
      }
    },
    {
      perm: false,
      text: '到场',
      click: (row) => {
        TableConfig.currentRow = row;
        refForm_Arrive.value?.openDialog();
      }
    },
    {
      perm: false,
      text: '处理',
      click: (row) => {
        TableConfig.currentRow = row;
        refForm_Deal.value?.openDialog();
      }
    },
    {
      perm: false,
      text: '完成',
      click: (row) => {
        TableConfig.currentRow = row;
        refForm_Comp.value?.openDialog();
      }
    },
    {
      hide: computed(() => window.SITE_CONFIG.SITENAME === 'xiaoqiu') as any,
      perm: false,
      text: '退单',
      click: (row) => {
        TableConfig.currentRow = row;
        refForm_ChargeBack.value?.openDialog();
      }
    },
    {
      perm: false,
      text: '复审',
      isTextBtn: true,
      disabled: (row: any): boolean => {
        return row.status !== 'SUBMIT' && row.status !== 'REVIEW';
      },
      click: (row) => {
        TableConfig.currentRow = row;
        refFormReview.value?.openDialog();
      }
    }
  ]
});
const handleReceive = () => {
  SLConfirm('确定接收？', '提示信息')
    .then(async () => {
      try {
        const res = await ReceiveWorkOrder(TableConfig.currentRow?.id);
        if (res.data.code === 200) {
          SLMessage.success('接收成功！');
          refreshData();
        } else {
          SLMessage.error(res.data?.err || '接收失败');
        }
      } catch (error) {
        SLMessage.error('系统错误');
      }
    })
    .catch(() => {
      //
    });
};

const FormConfig_Review = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  title: '复审',
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '审核人：',
          field: 'processUserId',
          options: [],
          rules: [{ required: true, message: '请选择审核人' }]
        },
        { type: 'textarea', label: '备注：', field: 'processRemark' }
      ]
    }
  ],
  defaultValue: {},
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        FormConfig_Review.submitting = true;
        try {
          const res = await ReviewWorkOrder(TableConfig.currentRow?.id, {
            ...params,
            processAdditionalInfo: JSON.stringify({}),
            stage: 'REVIEW'
          });
          if (res.data?.code === 200) {
            SLMessage.success('操作成功');
            refFormReview.value?.closeDialog();
            refreshData();
          } else {
            SLMessage.error(res.data?.err || '操作失败');
          }
        } catch (error) {
          SLMessage.error('系统错误');
        }
        FormConfig_Review.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const FormConfig_Trans = reactive<IDialogFormConfig>({
  title: '转发工单',
  labelPosition: 'top',
  dialogWidth: 500,
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '转发至：',
          field: 'expectUserId',
          options: [],
          rules: [{ required: true, message: '请选择接收人' }]
        },
        {
          type: 'select',
          label: '审核人：',
          field: 'nextProcessUserId',
          options: [],
          rules: [{ required: true, message: '请选择审核人' }]
        },
        {
          type: 'textarea',
          label: '备注：',
          field: 'processRemark',
          placeholder: ' '
        }
      ]
    }
  ],
  submit: (params) => {
    SLConfirm('确定转发？', '提示信息')
      .then(async () => {
        FormConfig_Trans.submitting = true;
        try {
          const toUser = (
            FormConfig_Trans.group[0].fields[0] as IFormSelect
          ).options?.find((item) => item.value === params.expectUserId)?.label;
          params = {
            ...params,
            expectUserId: removeSlash(params.expectUserId),
            nextProcessUserId: removeSlash(params.nextProcessUserId)
          };
          const res = await HandOverWorkOrderApply(TableConfig.currentRow?.id, {
            processAdditionalInfo: JSON.stringify({
              expectUserId: toUser
            }),
            ...params
          });
          if (res.data.code === 200) {
            SLMessage.success('转发成功！');
            refreshData();
            refForm_Trans.value?.closeDialog();
          } else {
            SLMessage.error(res.data?.err || '转发失败');
          }
          //   refreshData()
          // } else {
          //   SLMessage.error(res.data?.err || '接收失败')
          // }
        } catch (error) {
          SLMessage.error('系统错误');
        }
        FormConfig_Trans.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const FormConfig_Colab = reactive<IDialogFormConfig>({
  title: '申请协作',
  labelPosition: 'top',
  dialogWidth: 500,
  group: [
    {
      fields: [
        {
          type: 'textarea',
          label: '申请理由：',
          field: 'processRemark',
          placeholder: ' ',
          rules: [{ required: true, message: '请输入申请理由' }]
        },
        {
          type: 'select',
          label: '审核人：',
          field: 'nextProcessUserId',
          options: [],
          rules: [{ required: true, message: '请选择审核人' }]
        },
        {
          type: 'select-tree',
          label: '工单类型',
          field: 'type',
          options: getOrderTypeOptions(),
          rules: [{ required: true, message: '请选择工单类型' }]
        },
        {
          type: 'textarea',
          field: 'remark',
          label: '备注'
        },
        {
          type: 'textarea',
          field: 'collaborationRemark',
          label: '协作备注'
        }
      ]
    }
  ],
  submit: (params) => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        FormConfig_Trans.submitting = true;
        try {
          params = { ...params, isDirectDispatch: false };
          if (params.nextProcessUserId) {
            params.nextProcessUserId = removeSlash(params.nextProcessUserId);
          }
          collaborativeApplication(TableConfig.currentRow?.id, params).then(
            (res) => {
              if (res.data.code === 200) {
                SLMessage.success('协作提交成功！');
                refreshData();
                refForm_Trans.value?.closeDialog();
              } else {
                SLMessage.error(res.data?.err || '协作提交失败');
              }
            }
          );
        } catch (error) {
          SLMessage.error('系统错误');
        }
        FormConfig_Trans.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const FormConfig_Arrive = reactive<IDialogFormConfig>({
  title: '到场',
  dialogWidth: 500,
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          field: 'imgUrl',
          label: '现场图片：',
          type: 'image',
          returnType: 'comma',
          limit: 2,
          multiple: true
        },
        {
          field: 'videoUrl',
          label: '现场视频：',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传视频文件,最多上传2个，大小不能超过100M'
        },
        {
          field: 'audioUrl',
          label: '现场录音：',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传音频文件,最多上传2个，大小不能超过4M'
        },
        {
          field: 'otherFileUrl',
          label: '其它附件：',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传文件,最多上传2个，大小不能超过4M'
        },
        {
          field: 'processRemark',
          label: '备注：',
          type: 'textarea'
        }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        FormConfig_Trans.submitting = true;
        try {
          // const res = await ReceiveWorkOrder(TableConfig.currentRow?.id)
          // if (res.data.code === 200) {
          //   SLMessage.success('接收成功！')
          const res = await AddWorkOrderStage(TableConfig.currentRow?.id, {
            processRemark: '到场',
            processAdditionalInfo: JSON.stringify(params),
            stage: 'ARRIVING',
            nextProcessUserId: removeSlash(useUserStore().user?.id?.id || '')
          });
          if (res.data?.code === 200) {
            SLMessage.success('操作成功');
            refForm_Arrive.value?.closeDialog();
            refreshData();
          } else {
            SLMessage.error(res.data?.err || '操作失败');
          }
          // } else {
          //   SLMessage.error(res.data?.err || '接收失败')
          // }
        } catch (error) {
          SLMessage.error('系统错误');
        }
        FormConfig_Trans.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const FormConfig_Deal = reactive<IDialogFormConfig>({
  title: '处理',
  labelPosition: 'top',
  dialogWidth: 500,
  group: [
    {
      fields: [
        {
          field: 'imgUrl',
          label: '现场图片：',
          type: 'image',
          returnType: 'comma',
          limit: 2,
          multiple: true
        },
        {
          field: 'videoUrl',
          label: '现场视频：',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传视频文件,最多上传2个，大小不能超过100M'
        },
        {
          field: 'audioUrl',
          label: '现场录音：',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传音频文件,最多上传2个，大小不能超过4M'
        },
        {
          field: 'otherFileUrl',
          label: '其它附件：',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传文件,最多上传2个，大小不能超过4M'
        },
        {
          field: 'processRemark',
          label: '备注：',
          type: 'textarea'
        }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        FormConfig_Trans.submitting = true;
        try {
          // const res = await ReceiveWorkOrder(TableConfig.currentRow?.id)
          // if (res.data.code === 200) {
          //   SLMessage.success('接收成功！')
          const res = await AddWorkOrderStage(TableConfig.currentRow?.id, {
            processRemark: '处理',
            processAdditionalInfo: JSON.stringify(params),
            stage: 'PROCESSING',
            nextProcessUserId: removeSlash(useUserStore().user?.id?.id || '')
          });
          if (res.data?.code === 200) {
            SLMessage.success('操作成功');
            refForm_Deal.value?.closeDialog();
            refreshData();
          } else {
            SLMessage.error(res.data?.err || '操作失败');
          }
          // } else {
          //   SLMessage.error(res.data?.err || '接收失败')
          // }
        } catch (error) {
          SLMessage.error('系统错误');
        }
        FormConfig_Trans.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const FormConfig_Comp = reactive<IDialogFormConfig>({
  title: '完成',
  dialogWidth: 500,
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          field: 'imgUrl',
          label: '图片：',
          type: 'image',
          returnType: 'comma',
          limit: 2,
          multiple: true
        },
        {
          field: 'audioUrl',
          label: '录音：',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传音频文件,最多上传2个，大小不能超过4M'
        },
        {
          field: 'videoUrl',
          label: '视频：',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传视频文件,最多上传2个，大小不能超过100M'
        },
        {
          field: 'otherFileUrl',
          label: '附件：',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传文件,最多上传2个，大小不能超过4M'
        },
        {
          field: 'auditUserId',
          label: '审核人：',
          type: 'select',
          options: [],
          rules: [{ required: true, message: '请选择审核人' }]
        },
        {
          field: 'processRemark',
          label: '备注：',
          type: 'textarea'
        }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        FormConfig_Trans.submitting = true;
        try {
          // const res = await ReceiveWorkOrder(TableConfig.currentRow?.id)
          // if (res.data.code === 200) {
          //   SLMessage.success('接收成功！')
          const res = await AddWorkOrderStage(TableConfig.currentRow?.id, {
            processRemark: '完成',
            processAdditionalInfo: JSON.stringify(params),
            stage: 'SUBMIT',
            nextProcessUserId: removeSlash(params.auditUserId)
          });
          if (res.data?.code === 200) {
            SLMessage.success('操作成功');
            refForm_Comp.value?.closeDialog();
            refreshData();
          } else {
            SLMessage.error(res.data?.err || '操作失败');
          }
          // } else {
          //   SLMessage.error(res.data?.err || '接收失败')
          // }
        } catch (error) {
          SLMessage.error('系统错误');
        }
        FormConfig_Trans.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const FormConfig_ChargeBack = reactive<IDialogFormConfig>({
  title: '退单',
  labelPosition: 'top',
  dialogWidth: 500,
  group: [
    {
      fields: [
        {
          type: 'textarea',
          label: '退单原因：',
          field: 'processRemark',
          rules: [{ required: true, message: '请输入退单原因' }]
        },
        {
          type: 'select',
          label: '审核人：',
          field: 'nextProcessUserId',
          options: [],
          rules: [{ required: true, message: '请选择审核人' }]
        }
      ]
    }
  ],
  defaultValue: {
    processAdditionalInfo: JSON.stringify({})
  },
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        FormConfig_Trans.submitting = true;
        try {
          const res = await ChargeBackWorkOrderApply(
            TableConfig.currentRow?.id,
            {
              processRemark: '退单申请',
              processAdditionalInfo: JSON.stringify(params),
              nextProcessUserId: removeSlash(params.nextProcessUserId)
            }
          );
          if (res.data?.code === 200) {
            SLMessage.success('操作成功');
            refForm_ChargeBack.value?.closeDialog();
            refreshData();
          } else {
            SLMessage.error(res.data?.err || '操作失败');
          }
        } catch (error) {
          SLMessage.error('系统错误');
        }
        FormConfig_Trans.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const handleDetail = (row: any) => {
  TableConfig.currentRow = row;
  DrawerConfig.title = row.serialNo;
  refdetail.value?.openDrawer();
  // router.push({
  //   name: 'WorkOrderDetail',
  //   query: {
  //     id: row.id
  //   }
  // })
};
const resetForm = () => {
  refSearch.value?.resetForm();
  refreshData();
};
const exportTable = () => {
  refTable.value?.exportTable();
};
const initUserOption = async () => {
  const options = await getUserOptions(false, { authority: 'CUSTOMER_USER' });
  const field = FormConfig_Trans.group[0].fields[0] as IFormSelect;
  const transer = FormConfig_Trans.group[0].fields[1] as IFormSelect;
  const cooperation = FormConfig_Colab.group[0].fields[1] as IFormSelect;
  const compUser = FormConfig_Comp.group[0].fields.find(
    (item) => item.field === 'auditUserId'
  ) as IFormSelect;
  const backUser = FormConfig_ChargeBack.group[0].fields.find(
    (item) => item.field === 'nextProcessUserId'
  ) as IFormSelect;
  const reviewUser = FormConfig_Review.group[0].fields[0] as IFormSelect;
  reviewUser.options = options;
  field.options = options;
  transer.options = options;
  cooperation.options = options;
  compUser.options = options;
  backUser.options = options;
};
const refreshData = async () => {
  TableConfig.loading = true;
  try {
    const query = refSearch.value?.queryParams || {};
    const [fromTime, toTime] =
      query.date?.length === 2
        ? [
            moment(query.date[0], formatterDate).valueOf(),
            moment(query.date[1], formatterDate).endOf('D').valueOf()
          ]
        : [
            moment().subtract(1, 'M').startOf('D').valueOf(),
            moment().endOf('D').valueOf()
          ];
    const stageBetween =
      query.stageBetween === 'ASSIGN'
        ? ['ASSIGN']
        : query.stageBetween === 'RESOLVING'
          ? ['RESOLVING', 'REJECTED']
          : query.stageBetween === 'SUBMIT'
            ? ['SUBMIT', 'APPROVED']
            : ['ASSIGN'];
    const params: any = {
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit || 20,
      ...query,
      fromTime,
      toTime,
      stepProcessUserId: removeSlash(useUserStore().user?.id?.id || ''),
      stageBetween: stageBetween.join(',')
    };
    delete params.date;
    const res = await GetWorkOrderPage(params);
    const data = res.data?.data;
    key.value = (Math.random() * 1000).toFixed(0);
    TableConfig.dataList = data.data;
    TableConfig.pagination.total = data.total;
  } catch (error) {
    //
  }
  TableConfig.loading = false;
};
onMounted(() => {
  initOptions();
  refreshData();
  initUserOption();
});
</script>
<style lang="scss" scoped></style>
