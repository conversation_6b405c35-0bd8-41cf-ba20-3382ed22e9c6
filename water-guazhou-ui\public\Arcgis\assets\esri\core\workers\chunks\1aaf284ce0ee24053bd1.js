"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6237],{66237:(e,t,l)=>{l.r(t),l.d(t,{default:()=>v});var o=l(43697),r=(l(66577),l(5600)),n=(l(75215),l(67676),l(52011)),s=l(94139),i=l(16199),a=l(39450),p=l(11145),u=l(15235),c=l(82971),w=l(6570);let d=class extends i.default{constructor(...e){super(...e),this.portalItem=null,this.isReference=null,this.tileInfo=new p.Z({size:[256,256],dpi:96,format:"png8",compressionQuality:0,origin:new s.Z({x:-20037508.342787,y:20037508.342787,spatialReference:c.<PERSON>.WebMercator}),spatialReference:c.<PERSON>.WebMercator,lods:[new a.Z({level:0,scale:591657527.591555,resolution:156543.033928}),new a.Z({level:1,scale:295828763.795777,resolution:78271.5169639999}),new a.Z({level:2,scale:147914381.897889,resolution:39135.7584820001}),new a.Z({level:3,scale:73957190.948944,resolution:19567.8792409999}),new a.Z({level:4,scale:36978595.474472,resolution:9783.93962049996}),new a.Z({level:5,scale:18489297.737236,resolution:4891.96981024998}),new a.Z({level:6,scale:9244648.868618,resolution:2445.98490512499}),new a.Z({level:7,scale:4622324.434309,resolution:1222.99245256249}),new a.Z({level:8,scale:2311162.217155,resolution:611.49622628138}),new a.Z({level:9,scale:1155581.108577,resolution:305.748113140558}),new a.Z({level:10,scale:577790.554289,resolution:152.874056570411}),new a.Z({level:11,scale:288895.277144,resolution:76.4370282850732}),new a.Z({level:12,scale:144447.638572,resolution:38.2185141425366}),new a.Z({level:13,scale:72223.819286,resolution:19.1092570712683}),new a.Z({level:14,scale:36111.909643,resolution:9.55462853563415}),new a.Z({level:15,scale:18055.954822,resolution:4.77731426794937}),new a.Z({level:16,scale:9027.977411,resolution:2.38865713397468}),new a.Z({level:17,scale:4513.988705,resolution:1.19432856685505}),new a.Z({level:18,scale:2256.994353,resolution:.597164283559817}),new a.Z({level:19,scale:1128.497176,resolution:.298582141647617})]}),this.subDomains=["a","b","c"],this.fullExtent=new w.Z(-20037508.342787,-20037508.34278,20037508.34278,20037508.342787,c.Z.WebMercator),this.urlTemplate="https://{subDomain}.tile.openstreetmap.org/{level}/{col}/{row}.png",this.operationalLayerType="OpenStreetMap",this.type="open-street-map",this.copyright="Map data &copy; OpenStreetMap contributors, CC-BY-SA"}get refreshInterval(){return 0}};(0,o._)([(0,r.Cb)({type:u.default,json:{read:!1,write:!1,origins:{"web-document":{read:!1,write:!1}}}})],d.prototype,"portalItem",void 0),(0,o._)([(0,r.Cb)({type:Boolean,json:{read:!1,write:!1}})],d.prototype,"isReference",void 0),(0,o._)([(0,r.Cb)({type:Number,readOnly:!0,json:{read:!1,write:!1,origins:{"web-document":{read:!1,write:!1}}}})],d.prototype,"refreshInterval",null),(0,o._)([(0,r.Cb)({type:p.Z,json:{write:!1}})],d.prototype,"tileInfo",void 0),(0,o._)([(0,r.Cb)({type:["show","hide"]})],d.prototype,"listMode",void 0),(0,o._)([(0,r.Cb)({readOnly:!0,json:{read:!1,write:!1}})],d.prototype,"subDomains",void 0),(0,o._)([(0,r.Cb)({readOnly:!0,json:{read:!1,write:!1},nonNullable:!0})],d.prototype,"fullExtent",void 0),(0,o._)([(0,r.Cb)({readOnly:!0,json:{read:!1,write:!1}})],d.prototype,"urlTemplate",void 0),(0,o._)([(0,r.Cb)({type:["OpenStreetMap"]})],d.prototype,"operationalLayerType",void 0),(0,o._)([(0,r.Cb)({json:{read:!1}})],d.prototype,"type",void 0),(0,o._)([(0,r.Cb)({json:{read:!1,write:!1}})],d.prototype,"copyright",void 0),(0,o._)([(0,r.Cb)({json:{read:!1,write:!1}})],d.prototype,"wmtsInfo",void 0),d=(0,o._)([(0,n.j)("esri.layers.OpenStreetMapLayer")],d);const v=d}}]);