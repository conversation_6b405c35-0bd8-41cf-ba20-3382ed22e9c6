package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractContainer;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SoConstructionContractPageRequest extends AdvancedPageableQueryEntity<SoConstructionContractContainer, SoConstructionContractPageRequest> {
    // 所属工程编号
    private String constructionCode;

    // 所属工程名称
    private String constructionName;

    // 所属工程类别Id
    private String constructionTypeId;

    // 业主单位
    private String firstpartOrganization;

    // 业主单位
    private String secondpartOrganization;

    // 监理单位
    private String superVisitorOrganization;
    
}
