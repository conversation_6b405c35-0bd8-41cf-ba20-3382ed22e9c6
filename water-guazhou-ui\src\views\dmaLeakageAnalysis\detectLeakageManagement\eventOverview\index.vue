<template>
  <DrawerBox
    ref="refDrawerBox"
    :right-drawer="true"
    :bottom-drawer="true"
    :bottom-drawer-title="'事件列表'"
    bottom-drawer-bar-position="left"
  >
    <!-- 筛选表单 -->
    <template #header>
      <el-form :model="queryParams" :inline="true" class="filter-form">
        <el-form-item label="事件类型">
          <el-select v-model="queryParams.type" placeholder="全部" clearable>
            <el-option label="全部" value="" />
            <el-option label="漏点" value="leak" />
            <el-option label="维修" value="repair" />
          </el-select>
        </el-form-item>
        <el-form-item label="分区">
          <el-select v-model="queryParams.partition" placeholder="全部" clearable>
            <el-option label="全部" value="" />
            <el-option v-for="item in partitionList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="全部" clearable>
            <el-option label="全部" value="" />
            <el-option label="待处理" value="pending" />
            <el-option label="处理中" value="processing" />
            <el-option label="已完成" value="done" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="success" @click="openEventList">事件列表</el-button>
        </el-form-item>
      </el-form>
    </template>

    <!-- 右侧抽屉：统计分析和排行 -->
    <template #right>
      <div class="right-drawer-container">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">事件类型分布</div>
          </template>
          <div ref="pieChartContainer" class="chart-container"></div>
        </el-card>
        <el-card class="box-card">
          <template #header>
            <div class="card-header">事件趋势</div>
          </template>
          <div ref="barChartContainer" class="chart-container"></div>
        </el-card>
        <el-card class="box-card rank-card">
          <template #header>
            <div class="card-header">分区事件数量排行</div>
          </template>
          <el-table :data="rankData" style="width: 100%" height="220">
            <el-table-column type="index" label="排行" width="60" />
            <el-table-column prop="partition" label="分区" />
            <el-table-column prop="count" label="事件数" sortable />
          </el-table>
        </el-card>
      </div>
    </template>

    <!-- 下方抽屉：事件列表 -->
    <template #bottom>
      <el-table :data="filteredEvents" style="width: 100%" height="320">
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="partition" label="分区" />
        <el-table-column prop="status" label="状态" />
        <el-table-column prop="date" label="日期" />
        <el-table-column prop="desc" label="描述" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button link type="primary" @click="viewEvent(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>

    <!-- 主体地图 -->
    <ArcLayout ref="refArcLayout" @map-loaded="onMapLoaded"></ArcLayout>
  </DrawerBox>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import DrawerBox from '@/components/DrawerBox/DrawerBox.vue'
import ArcLayout from '@/components/arcMap/widgets/ArcLayout.vue'
import Graphic from '@arcgis/core/Graphic'
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol'
import Point from '@arcgis/core/geometry/Point'

const refDrawerBox = ref()
const refArcLayout = ref()
const pieChartContainer = ref()
const barChartContainer = ref()

// 假数据
const partitionList = ['A区', 'B区', 'C区', 'D区']
const allEvents = ref([
  { id: 1, type: 'leak', partition: 'A区', status: 'pending', date: '2023-10-01', desc: 'A区管道漏点', lng: 120.38, lat: 31.83 },
  { id: 2, type: 'repair', partition: 'A区', status: 'done', date: '2023-10-02', desc: 'A区阀门维修', lng: 120.39, lat: 31.84 },
  { id: 3, type: 'leak', partition: 'B区', status: 'processing', date: '2023-10-03', desc: 'B区漏点', lng: 120.35, lat: 31.80 },
  { id: 4, type: 'repair', partition: 'C区', status: 'done', date: '2023-10-04', desc: 'C区管道维修', lng: 120.42, lat: 31.81 },
  { id: 5, type: 'leak', partition: 'C区', status: 'pending', date: '2023-10-05', desc: 'C区漏点', lng: 120.39, lat: 31.79 },
  { id: 6, type: 'leak', partition: 'D区', status: 'done', date: '2023-10-06', desc: 'D区漏点', lng: 120.41, lat: 31.78 },
  { id: 7, type: 'repair', partition: 'B区', status: 'processing', date: '2023-10-07', desc: 'B区管道维修', lng: 120.36, lat: 31.82 },
])

const queryParams = ref({
  type: '',
  partition: '',
  status: '',
  dateRange: [] as any[],
})

const filteredEvents = computed(() => {
  return allEvents.value.filter(ev => {
    const typeMatch = !queryParams.value.type || ev.type === queryParams.value.type
    const partitionMatch = !queryParams.value.partition || ev.partition === queryParams.value.partition
    const statusMatch = !queryParams.value.status || ev.status === queryParams.value.status
    let dateMatch = true
    if (queryParams.value.dateRange?.length === 2) {
      const d = new Date(ev.date)
      const start = new Date(queryParams.value.dateRange[0])
      const end = new Date(queryParams.value.dateRange[1])
      dateMatch = d >= start && d <= end
    }
    return typeMatch && partitionMatch && statusMatch && dateMatch
  })
})

const rankData = computed(() => {
  const map = new Map<string, number>()
  filteredEvents.value.forEach(ev => {
    map.set(ev.partition, (map.get(ev.partition) || 0) + 1)
  })
  return partitionList.map(part => ({ partition: part, count: map.get(part) || 0 }))
    .sort((a, b) => b.count - a.count)
})

function handleQuery() {
  // 这里只需触发 filteredEvents 重新计算即可
}
function resetQuery() {
  queryParams.value.type = ''
  queryParams.value.partition = ''
  queryParams.value.status = ''
  queryParams.value.dateRange = []
}
function openEventList() {
  refDrawerBox.value?.toggleDrawer('btt', true)
}
function viewEvent(row: any) {
  ElMessage.info(`查看事件ID: ${row.id}，类型: ${row.type}`)
}

// 地图相关
const staticsState: { view?: __esri.MapView } = {}
const onMapLoaded = (view: __esri.MapView) => {
  staticsState.view = view
  addEventsToMap()
}
function addEventsToMap() {
  const view = staticsState.view
  if (!view) return
  view.graphics.removeAll()
  filteredEvents.value.forEach(ev => {
    const color = ev.type === 'leak' ? [226, 119, 40] : [40, 120, 226]
    const markerSymbol = new SimpleMarkerSymbol({
      color,
      outline: { color: [255, 255, 255], width: 2 },
    })
    const point = new Point({ longitude: ev.lng, latitude: ev.lat })
    const graphic = new Graphic({ geometry: point, symbol: markerSymbol, attributes: ev })
    view.graphics.add(graphic)
  })
}

// 图表
function initCharts() {
  if (pieChartContainer.value && barChartContainer.value) {
    // 事件类型分布
    const typeMap: Record<string, number> = { '漏点': 0, '维修': 0 }
    filteredEvents.value.forEach(ev => {
      if (ev.type === 'leak') typeMap['漏点']++
      if (ev.type === 'repair') typeMap['维修']++
    })
    const pieChart = echarts.init(pieChartContainer.value)
    pieChart.setOption({
      title: { text: '事件类型分布', x: 'center' },
      tooltip: { trigger: 'item' },
      legend: { orient: 'vertical', left: 'left', data: ['漏点', '维修'] },
      series: [{
        name: '类型', type: 'pie', radius: '60%',
        data: [ { value: typeMap['漏点'], name: '漏点' }, { value: typeMap['维修'], name: '维修' } ]
      }]
    })
    // 事件趋势
    const monthMap: Record<string, number> = {}
    filteredEvents.value.forEach(ev => {
      const m = ev.date.slice(0,7)
      monthMap[m] = (monthMap[m] || 0) + 1
    })
    const months = Object.keys(monthMap).sort()
    const barChart = echarts.init(barChartContainer.value)
    barChart.setOption({
      title: { text: '事件趋势', x: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: months },
      yAxis: { type: 'value' },
      series: [{ name: '事件数', data: months.map(m => monthMap[m]), type: 'bar' }]
    })
  }
}

onMounted(() => {
  refDrawerBox.value?.toggleDrawer('rtl', true)
  initCharts()
})

watch(filteredEvents, () => {
  addEventsToMap()
  initCharts()
})
</script>

<style scoped>
.filter-form {
  padding: 10px 20px 0 20px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 10px;
}
.right-drawer-container {
  padding: 10px;
  height: 100%;
  overflow-y: auto;
}
.box-card {
  margin-bottom: 15px;
}
.card-header {
  font-weight: bold;
}
.chart-container {
  width: 100%;
  height: 220px;
}
</style> 