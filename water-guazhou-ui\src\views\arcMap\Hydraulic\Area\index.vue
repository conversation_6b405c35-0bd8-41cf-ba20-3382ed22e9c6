<!-- 管网分析 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="管网分析"
    @map-loaded="onMaploaded"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>

    <FormTable
      class="table-box"
      :config="TableConfig"
    ></FormTable>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { getGraphicLayer } from '@/utils/MapHelper'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { useSketch } from '@/hooks/arcgis'

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()

const state = reactive<{
  tabs: any[]
  loading: boolean
  layerInfos: any[]
  layerIds: any[]
}>({
  tabs: [],
  layerInfos: [],
  layerIds: [],
  loading: false
})
const staticState: {
  view?: __esri.MapView
  graphics?: __esri.Graphic
  graphicsLayer?: __esri.GraphicsLayer
} = {}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})

const TableConfig = reactive<ITable>({
  columns: [
    { label: '分区', prop: 'name' },
    { label: '创建时间', prop: 'time' }
  ],
  dataList: [],
  operations: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const refreshData = () => {
  //
}
const initDraw = (type: any) => {
  if (!staticState.view) return
  staticState.graphicsLayer?.removeAll()
  staticState.graphics = undefined
  sketch.value?.create(type)
}

const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphics = undefined
}

const { initSketch, destroySketch, sketch } = useSketch()
const resolveDrawEnd = (res: ISketchHandlerParameter) => {
  if (res.state === 'complete') {
    staticState.graphics = res.graphics[0]
    console.log(JSON.stringify(staticState.graphics))
  }
}
const onMaploaded = async view => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-quick',
    title: '快速查询'
  })
  initSketch(staticState.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  })
}

onBeforeUnmount(() => {
  staticState.graphicsLayer && staticState.view?.map.remove(staticState.graphicsLayer)
  destroySketch()
})
</script>
<style lang="scss" scoped>
.right-title {
  width: 100%;

  display: flex;
  align-items: center;
  justify-content: space-between;
}
.table-box {
  height: calc(100% - 120px);
}
</style>
