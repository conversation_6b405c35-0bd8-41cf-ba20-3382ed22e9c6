/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
syntax = "proto3";
package cluster;

option java_package = "org.thingsboard.server.gen.cluster";
option java_outer_classname = "ClusterAPIProtos";

service ClusterRpcService {
  rpc handleMsgs(stream ClusterMessage) returns (stream ClusterMessage) {}
}

message ClusterMessage {
  MessageType messageType = 1;
  MessageMataInfo messageMetaInfo = 2;
  ServerAddress serverAddress = 3;
  bytes payload = 4;
}

message ServerAddress {
  string host = 1;
  int32 port = 2;
}

message MessageMataInfo {
  string payloadMetaInfo = 1;
  repeated string tags = 2;
}

enum MessageType {

  //Cluster control messages
  RPC_SESSION_CREATE_REQUEST_MSG = 0;
  TO_ALL_NODES_MSG = 1;
  RPC_SESSION_TELL_MSG = 2;
  RPC_BROADCAST_MSG = 3;
  CONNECT_RPC_MESSAGE =4;

  CLUSTER_ACTOR_MESSAGE = 5;
  // Messages related to TelemetrySubscriptionService
  CLUSTER_TELEMETRY_SUBSCRIPTION_CREATE_MESSAGE = 6;
  CLUSTER_TELEMETRY_SUBSCRIPTION_UPDATE_MESSAGE = 7;
  CLUSTER_TELEMETRY_SUBSCRIPTION_CLOSE_MESSAGE = 8;
  CLUSTER_TELEMETRY_SESSION_CLOSE_MESSAGE = 9;
  CLUSTER_TELEMETRY_ATTR_UPDATE_MESSAGE = 10;
  CLUSTER_TELEMETRY_TS_UPDATE_MESSAGE = 11;
  CLUSTER_RPC_FROM_DEVICE_RESPONSE_MESSAGE = 12;

  CLUSTER_DEVICE_STATE_SERVICE_MESSAGE = 13;
  CLUSTER_TRANSACTION_SERVICE_MESSAGE = 14;
}

// Messages related to CLUSTER_TELEMETRY_MESSAGE
message SubscriptionProto {
  string sessionId = 1;
  int32 subscriptionId = 2;
  string entityType = 3;
  string tenantId = 4;
  string entityId = 5;
  string type = 6;
  bool allKeys = 7;
  repeated SubscriptionKetStateProto keyStates = 8;
  string scope = 9;
}

message SubscriptionUpdateProto {
    string sessionId = 1;
    int32 subscriptionId = 2;
    int32 errorCode = 3;
    string errorMsg = 4;
    repeated SubscriptionUpdateValueListProto data = 5;
}

message AttributeUpdateProto {
    string entityType = 1;
    string entityId = 2;
    string scope = 3;
    repeated KeyValueProto data = 4;
}

message TimeseriesUpdateProto {
    string entityType = 1;
    string entityId = 2;
    repeated KeyValueProto data = 4;
}

message SessionCloseProto {
    string sessionId = 1;
}

message SubscriptionCloseProto {
    string sessionId = 1;
    int32 subscriptionId = 2;
}

message SubscriptionKetStateProto {
    string key = 1;
    int64 ts = 2;
}

message SubscriptionUpdateValueListProto {
    string key = 1;
    repeated int64 ts = 2;
    repeated string value = 3;
}

message KeyValueProto {
    string key = 1;
    int64 ts = 2;
    int32 valueType = 3;
    string strValue = 4;
    int64 longValue = 5;
    double doubleValue = 6;
    bool boolValue = 7;
}

message FromDeviceRPCResponseProto {
    int64 requestIdMSB = 1;
    int64 requestIdLSB = 2;
    string response = 3;
    int32 error = 4;
}

message DeviceStateServiceMsgProto {
    int64 tenantIdMSB = 1;
    int64 tenantIdLSB = 2;
    int64 deviceIdMSB = 3;
    int64 deviceIdLSB = 4;
    bool added = 5;
    bool updated = 6;
    bool deleted = 7;
}

message TransactionEndServiceMsgProto {
    string entityType = 1;
    int64 originatorIdMSB = 2;
    int64 originatorIdLSB = 3;
    int64 transactionIdMSB = 4;
    int64 transactionIdLSB = 5;
}
