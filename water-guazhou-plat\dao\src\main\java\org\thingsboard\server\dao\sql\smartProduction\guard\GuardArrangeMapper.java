package org.thingsboard.server.dao.sql.smartProduction.guard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrange;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangeQuickArrangeData;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangeQuickArrangeRequest;

import java.util.Date;
import java.util.List;

@Mapper
public interface GuardArrangeMapper extends BaseMapper<GuardArrange> {
    IPage<GuardArrange> findByPage(GuardArrangePageRequest request);

    boolean updateFully(GuardArrange entity);

    int quickArrange(@Param("arrangeDataList") List<GuardArrangeQuickArrangeData> arrangeDataList,
                     @Param("userId") String userId,
                     @Param("tenantId") String tenantId);

    boolean detectArrangeOverride(GuardArrangeQuickArrangeRequest req);

    List<String> removeOnQuickArrangeAtDate(List<Date> hitDate);

    GuardArrange getCurrentGuard(@Param("now") Long now, @Param("tenantId") String tenantId);
}
