package org.thingsboard.server.dao.settings;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.QueryTemplate;
import org.thingsboard.server.dao.sql.settings.QueryTemplateRepository;

import java.util.List;

@Slf4j
@Service
public class QueryTemplateServiceImpl implements QueryTemplateService {

    @Autowired
    private QueryTemplateRepository queryTemplateRepository;

    @Override
    public List<QueryTemplate> findList(TenantId tenantId) {
        return queryTemplateRepository.findByTenantIdOrderByType(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    @Transactional
    public void saveAll(List<QueryTemplate> entityList) {
        for (QueryTemplate queryTemplate : entityList) {
            // 先删除再保存
            queryTemplateRepository.deleteByTenantIdAndType(queryTemplate.getTenantId(), queryTemplate.getType());

            // 保存新规则
            queryTemplateRepository.save(queryTemplate);
        }
    }

    @Override
    public QueryTemplate findByType(String type, TenantId tenantId) {
        return queryTemplateRepository.findByTenantIdAndType(UUIDConverter.fromTimeUUID(tenantId.getId()), type);
    }
}
