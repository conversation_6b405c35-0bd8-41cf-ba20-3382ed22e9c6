package org.thingsboard.server.dao.sql.smartProduction.pumpHouse;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpHouseStorage;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpHouseStoragePageRequest;

import java.util.List;

@Mapper
public interface PumpHouseStorageMapper extends BaseMapper<PumpHouseStorage> {
    IPage<PumpHouseStorage> findByPage(PumpHouseStoragePageRequest request);

    boolean update(PumpHouseStorage entity);

    int saveAll(List<PumpHouseStorage> list);

    int updateAll(List<PumpHouseStorage> list);

    String getPumpRoomCode(String id);

    String getNameById(String id);

}
