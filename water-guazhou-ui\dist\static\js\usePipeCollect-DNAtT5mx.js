import{z as e,c as o}from"./index-r0dFAfgr.js";const p=t=>e({url:"/api/spp/collect",method:"post",data:t}),i=t=>e({url:"/api/spp/collect/list",method:"get",params:t}),u=t=>e({url:"/api/spp/collect",method:"delete",data:t}),n=t=>e({url:"/api/spp/collect/review",method:"post",data:t}),d=t=>e({url:"/api/spp/collect/assign",method:"post",data:t}),c=t=>e({url:"/api/spp/collect/data/list",method:"get",params:t}),P=t=>e({url:"/api/spp/collect/data",method:"delete",data:t}),m=t=>e({url:`/api/spp/collect/layerIdList?mainId=${t}`,method:"get"}),h=()=>{const t=o([]);return{list:t,getList:async l=>{try{const a=await c({mainId:l});t.value=a.data.data||[]}catch{t.value=[]}}}},C=(t="")=>{const s=o(t);return{setStatus:a=>{s.value=a},status:s}};export{n as A,u as D,i as G,p as P,d as a,P as b,h as c,m as d,C as u};
