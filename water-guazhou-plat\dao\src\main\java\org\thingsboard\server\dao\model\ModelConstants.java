/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model;

import com.datastax.driver.core.utils.UUIDs;
import org.apache.commons.lang3.ArrayUtils;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.Aggregation;

import java.util.UUID;

public class ModelConstants {

    private ModelConstants() {
    }

    public static final UUID NULL_UUID = UUIDs.startOf(0);
    public static final String NULL_UUID_STR = UUIDConverter.fromTimeUUID(NULL_UUID);
    public static final TenantId SYSTEM_TENANT = new TenantId(ModelConstants.NULL_UUID);
    public static final String GATEWAY = "gateWay";
    public static final String CONFIGURATION = "configuration";
    public static final String LAST_UPDATE_TIME = "lastUpdateTime";
    public static final String LAST_CONNECT_TIME = "lastConnectTime";
    public static final String ATTRIBUTE = "info";
    public static final String PROR = "prop";
    public static final String ENERGY_TYPE = "energyType";
    public static final String ENERGY_ATTRIBUTE = "attribute";

    public static final String DEVICE_ALARM = "deviceAlarm";
    public static final String TIME_SHARING = "timeSharing";
    public static final String TIME_SHARING_WEEK = "week";
    public static final String TIME_SHARING_TIME = "time";
    public static final String ENERGY_PRICE = "energyPrice";
    public static final String ALARM_TIME = "alarmTime";
    public static final String ALARM_TYPE_OFFLINE = "offline";
    public static final String ALARM_FORM_EMAIL = "alarmFromEmail";
    public static final String ALARM_FORM_SMS = "alarmFromSms";
    public static final String USER_PHONE = "phone";
    public static final String ALARM_RELEASED = "false";
    public static final Boolean ALARM_RELEASE_NOT = true;
    public static final String ALARM_TYPE_UPPER_LIMIT = "upperLimit";
    public static final String ALARM_TYPE_LOWER_LIMIT = "lowerLimit";
    public static final String ALARM_TYPE_EQUIVALENCE = "equivalence";
    public static final String ALARM_LEVEL_ONE = "一级";
    public static final String ALARM_LEVEL_TWO = "二级";
    public static final String ALARM_LEVEL_THREE = "三级";

    /**
     * common Field
     */
    public static final String NAME = "name";
    public static final String TYPE = "type";
    public static final String UPDATE_TIME = "update_time";
    public static final String CREATE_TIME = "create_time";
    public static final String CREATED_TIME = "created_time";
    public static final String START_TIME = "start_time";
    public static final String END_TIME = "end_time";
    public static final String ADDITIONAL_INFO = "additional_info";
    public static final String IS_DEL = "is_del";
    public static final String CREATOR = "creator";
    public static final String REMARK = "remark";
    public static final String EDIT_REMARK = "edit_remark";

    /**
     * table name
     */
    public static final String TABLE_LOGICAL_FLOW = "logical_flow";
    public static final String TABLE_LOGICAL_FLOW_NODE = "logical_flow_node";
    public static final String TABLE_LOGICAL_FLOW_HISTORY = "logical_flow_history";
    public static final String TABLE_LOGICAL_FLOW_NODE_HISTORY = "logical_flow_node_history";
    public static final String TABLE_LOGICAL_FLOW_ALARM_NODE_HISTORY = "logical_flow_alarm_node_history";

    /**
     * logical flow special field
     */
    public static final String LOGICAL_FLOW_TYPE = "type";
    public static final String LOGICAL_FLOW_REMARK = "remark";
    public static final String LOGICAL_FLOW_CREATER = "creater";
    public static final String LOGICAL_FLOW_PARENT_ID = "parent_id";
    public static final String LOGICAL_FLOW_STATUS = "status";
    public static final String LOGICAL_FLOW_STOP_FORCE = "stop_force";


    /**
     * logical flow node special field
     */
    public static final String LOGICAL_FLOW_NODE_PARENT_ID = "parent_id";
    public static final String LOGICAL_FLOW_NODE_TYPE = "type";
    public static final String LOGICAL_FLOW_NODE_SCRIPT = "script";
    public static final String LOGICAL_FLOW_NODE_PARAM = "param";
    public static final String LOGICAL_FLOW_NODE_ORDER = "order_num";
    public static final String LOGICAL_FLOW_ID = "logical_flow_id";
    public static final String LOGICAL_FLOW_NODE_REMARK = "remark";

    /**
     * logical flow history special field
     */
    public static final String LOGICAL_FLOW_NAME = "logical_flow_name";

    /**
     * logical flow alarm node history special field
     */
    public static final String LOGICAL_FLOW_ALARM_NODE_HISTORY_RECIPIENT = "recipient";
    public static final String LOGICAL_FLOW_ALARM_NODE_HISTORY_SEND_FLAG = "send_flag";

    /**
     * logical flow node history special field
     */
    public static final String HISTORY_ID = "history_id";
    public static final String LOGICAL_FLOW_NODE_ID = "logical_flow_node_id";
    public static final String LOGICAL_FLOW_NODE_NAME = "logical_flow_node_name";
    public static final String LOGICAL_FLOW_RUN_RESULT = "result";
    public static final String LOGICAL_FLOW_RUN_SCRIPT = "script";
    public static final String LOGICAL_FLOW_PARENT_NODE_ID = "parent_node_id";


    /**
     * energy
     */
    public static final String ENERGY = "energy";
    public static final String ENERGY_UNIT = "unit";
    public static final String ENERGY_TABLE_TYPE = "energy_type";
    public static final String ENERGY_NAME = "energy_name";
    public static final String ENERGY_ENABLE = "enable";

    /**
     * Generic constants.
     */
    public static final String ID_PROPERTY = "id";
    public static final String USER_ID_PROPERTY = "user_id";
    public static final String TENANT_ID_PROPERTY = "tenant_id";
    public static final String CREATOR_PROPERTY = "creator";
    public static final String CUSTOMER_ID_PROPERTY = "customer_id";
    public static final String GATEWAY_ID_PROPERTY = "gateway_id";
    public static final String DEVICE_ID_PROPERTY = "device_id";
    public static final String TITLE_PROPERTY = "title";
    public static final String ALIAS_PROPERTY = "alias";
    public static final String SEARCH_TEXT_PROPERTY = "search_text";
    public static final String ADDITIONAL_INFO_PROPERTY = "additional_info";
    public static final String ENTITY_TYPE_PROPERTY = "entity_type";
    public static final String ENTITY_SEQUENCE = "sequence";

    public static final String ENTITY_TYPE_COLUMN = ENTITY_TYPE_PROPERTY;
    public static final String ENTITY_ID_COLUMN = "entity_id";
    public static final String ATTRIBUTE_TYPE_COLUMN = "attribute_type";
    public static final String ATTRIBUTE_KEY_COLUMN = "attribute_key";
    public static final String LAST_UPDATE_TS_COLUMN = "last_update_ts";


    /**
     * Cassandra user constants.
     */
    public static final String USER_COLUMN_FAMILY_NAME = "user";
    public static final String USER_PG_HIBERNATE_COLUMN_FAMILY_NAME = "tb_user";
    public static final String USER_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String USER_CUSTOMER_ID_PROPERTY = CUSTOMER_ID_PROPERTY;
    public static final String USER_EMAIL_PROPERTY = "email";
    public static final String USER_AUTHORITY_PROPERTY = "authority";
    public static final String USER_FIRST_NAME_PROPERTY = "first_name";
    public static final String USER_LAST_NAME_PROPERTY = "last_name";
    public static final String USER_SERIAL_NO = "serial_no";
    public static final String USER_SERIAL_LOGIN_NAME = "login_name";
    public static final String USER_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;
    public static final String USER_DEPARTMENT_ID = "department_id";

    public static final String USER_BY_EMAIL_COLUMN_FAMILY_NAME = "user_by_email";
    public static final String USER_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "user_by_tenant_and_search_text";
    public static final String USER_BY_CUSTOMER_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "user_by_customer_and_search_text";

    /**
     * 用户行程
     */
    public static final String USER_SCHEDULE_TABLE = "tb_user_schedule";
    public static final String USER_SCHEDULE_TIME = "time";
    public static final String USER_SCHEDULE_USER_ID = "user_id";
    public static final String USER_SCHEDULE_TITLE = "title";
    public static final String USER_SCHEDULE_CONTENT = "content";
    public static final String USER_SCHEDULE_CREATE_TIME = "create_time";


    /**
     * Cassandra user_credentials constants.
     */
    public static final String USER_CREDENTIALS_COLUMN_FAMILY_NAME = "user_credentials";
    public static final String USER_CREDENTIALS_USER_ID_PROPERTY = USER_ID_PROPERTY;
    public static final String USER_CREDENTIALS_ENABLED_PROPERTY = "enabled";
    public static final String USER_CREDENTIALS_PASSWORD_PROPERTY = "password"; //NOSONAR, the constant used to identify password column name (not password value itself)
    public static final String USER_CREDENTIALS_ACTIVATE_TOKEN_PROPERTY = "activate_token";
    public static final String USER_CREDENTIALS_RESET_TOKEN_PROPERTY = "reset_token";

    public static final String USER_CREDENTIALS_BY_USER_COLUMN_FAMILY_NAME = "user_credentials_by_user";
    public static final String USER_CREDENTIALS_BY_ACTIVATE_TOKEN_COLUMN_FAMILY_NAME = "user_credentials_by_activate_token";
    public static final String USER_CREDENTIALS_BY_RESET_TOKEN_COLUMN_FAMILY_NAME = "user_credentials_by_reset_token";

    /**
     * Cassandra admin_settings constants.
     */
    public static final String ADMIN_SETTINGS_COLUMN_FAMILY_NAME = "admin_settings";
    public static final String ADMIN_SETTINGS_KEY_PROPERTY = "key";
    public static final String ADMIN_SETTINGS_JSON_VALUE_PROPERTY = "json_value";

    public static final String ADMIN_SETTINGS_BY_KEY_COLUMN_FAMILY_NAME = "admin_settings_by_key";

    /**
     * Cassandra contact constants.
     */
    public static final String COUNTRY_PROPERTY = "country";
    public static final String STATE_PROPERTY = "state";
    public static final String CITY_PROPERTY = "city";
    public static final String ADDRESS_PROPERTY = "address";
    public static final String ADDRESS2_PROPERTY = "address2";
    public static final String ZIP_PROPERTY = "zip";
    public static final String PHONE_PROPERTY = "phone";
    public static final String EMAIL_PROPERTY = "email";

    /**
     * Cassandra tenant constants.
     */
    public static final String TENANT_COLUMN_FAMILY_NAME = "tenant";
    public static final String TENANT_TITLE_PROPERTY = TITLE_PROPERTY;
    public static final String TENANT_REGION_PROPERTY = "region";
    public static final String TENANT_REGION_APP_TYPE_ID = "app_type_id";
    public static final String TENANT_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;
    public static final String TENANT_LATITUDE = "latd";
    public static final String TENANT_LONGITUDE = "lgtd";

    public static final String TENANT_BY_REGION_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "tenant_by_region_and_search_text";

    /**
     * Cassandra customer constants.
     */
    public static final String CUSTOMER_COLUMN_FAMILY_NAME = "customer";
    public static final String CUSTOMER_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String CUSTOMER_TITLE_PROPERTY = TITLE_PROPERTY;
    public static final String CUSTOMER_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;

    public static final String CUSTOMER_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "customer_by_tenant_and_search_text";
    public static final String CUSTOMER_BY_TENANT_AND_TITLE_VIEW_NAME = "customer_by_tenant_and_title";


    public static final String ENTITY_VIEW_TABLE_FAMILY_NAME = "entity_view";
    public static final String ENTITY_VIEW_ENTITY_ID_PROPERTY = ENTITY_ID_COLUMN;
    public static final String ENTITY_VIEW_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String ENTITY_VIEW_CUSTOMER_ID_PROPERTY = CUSTOMER_ID_PROPERTY;
    public static final String ENTITY_VIEW_NAME_PROPERTY = "name";
    public static final String ENTITY_VIEW_BY_TENANT_AND_CUSTOMER_CF = "entity_view_by_tenant_and_customer";
    public static final String ENTITY_VIEW_BY_TENANT_AND_CUSTOMER_AND_TYPE_CF = "entity_view_by_tenant_and_customer_and_type";
    public static final String ENTITY_VIEW_BY_TENANT_AND_ENTITY_ID_CF = "entity_view_by_tenant_and_entity_id";
    public static final String ENTITY_VIEW_KEYS_PROPERTY = "keys";
    public static final String ENTITY_VIEW_TYPE_PROPERTY = "type";
    public static final String ENTITY_VIEW_START_TS_PROPERTY = "start_ts";
    public static final String ENTITY_VIEW_END_TS_PROPERTY = "end_ts";
    public static final String ENTITY_VIEW_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;
    public static final String ENTITY_VIEW_BY_TENANT_AND_SEARCH_TEXT_CF = "entity_view_by_tenant_and_search_text";
    public static final String ENTITY_VIEW_BY_TENANT_BY_TYPE_AND_SEARCH_TEXT_CF = "entity_view_by_tenant_by_type_and_search_text";
    public static final String ENTITY_VIEW_BY_TENANT_AND_NAME = "entity_view_by_tenant_and_name";

    /**
     * Cassandra device constants.
     */
    public static final String DEVICE_COLUMN_FAMILY_NAME = "device";
    public static final String DEVICE_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String DEVICE_CUSTOMER_ID_PROPERTY = CUSTOMER_ID_PROPERTY;
    public static final String DEVICE_HARDWARE_ID="hardware_id";
    public static final String DEVICE_NAME_PROPERTY = "name";
    public static final String DEVICE_TYPE_PROPERTY = "type";
    public static final String DEVICE_IS_DELETE_PROPERTY = "is_delete";
    public static final String DEVICE_TEMPLATE_ID = "template_id";
    public static final String DEVICE_AEP_DEVICE_ID = "aep_device_id";
    public static final String FOREIGN_KEY = "foreign_key";
    public static final String TO_ORACLE = "to_oracle";
    public static final String GROUP_ID = "group_id";
    public static final String ORDER_NUM = "order_num";
    public static final String DEVICE_LOCALTION = "location";
    public static final String DEVICE_TYPE_NAME = "device_type_name";
    public static final String DEVICE_ORACLE_KEY = "oracle_key";
    public static final String DEVICE_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;

    public static final String DEVICE_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "device_by_tenant_and_search_text";
    public static final String DEVICE_BY_TENANT_BY_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "device_by_tenant_by_type_and_search_text";
    public static final String DEVICE_BY_CUSTOMER_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "device_by_customer_and_search_text";
    public static final String DEVICE_BY_CUSTOMER_BY_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "device_by_customer_by_type_and_search_text";
    public static final String DEVICE_BY_TENANT_AND_NAME_VIEW_NAME = "device_by_tenant_and_name";
    public static final String DEVICE_TYPES_BY_TENANT_VIEW_NAME = "device_types_by_tenant";

    /**
     * Cassandra audit log constants.
     */
    public static final String AUDIT_LOG_COLUMN_FAMILY_NAME = "audit_log";

    public static final String AUDIT_LOG_BY_ENTITY_ID_CF = "audit_log_by_entity_id";
    public static final String AUDIT_LOG_BY_CUSTOMER_ID_CF = "audit_log_by_customer_id";
    public static final String AUDIT_LOG_BY_USER_ID_CF = "audit_log_by_user_id";
    public static final String AUDIT_LOG_BY_TENANT_ID_CF = "audit_log_by_tenant_id";
    public static final String AUDIT_LOG_BY_TENANT_ID_PARTITIONS_CF = "audit_log_by_tenant_id_partitions";

    public static final String AUDIT_LOG_ID_PROPERTY = ID_PROPERTY;
    public static final String AUDIT_LOG_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String AUDIT_LOG_CUSTOMER_ID_PROPERTY = CUSTOMER_ID_PROPERTY;
    public static final String AUDIT_LOG_ENTITY_TYPE_PROPERTY = ENTITY_TYPE_PROPERTY;
    public static final String AUDIT_LOG_ENTITY_ID_PROPERTY = ENTITY_ID_COLUMN;
    public static final String AUDIT_LOG_ENTITY_NAME_PROPERTY = "entity_name";
    public static final String AUDIT_LOG_USER_ID_PROPERTY = USER_ID_PROPERTY;
    public static final String AUDIT_LOG_PARTITION_PROPERTY = "partition";
    public static final String AUDIT_LOG_USER_NAME_PROPERTY = "user_name";
    public static final String AUDIT_LOG_ACTION_TYPE_PROPERTY = "action_type";
    public static final String AUDIT_LOG_ACTION_DATA_PROPERTY = "action_data";
    public static final String AUDIT_LOG_ACTION_STATUS_PROPERTY = "action_status";
    public static final String AUDIT_LOG_ACTION_FAILURE_DETAILS_PROPERTY = "action_failure_details";

    /**
     * Cassandra asset constants.
     */
    public static final String ASSET_COLUMN_FAMILY_NAME = "asset";
    public static final String ASSET_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String ASSET_CUSTOMER_ID_PROPERTY = CUSTOMER_ID_PROPERTY;
    public static final String ASSET_NAME_PROPERTY = "name";
    public static final String ASSET_TYPE_PROPERTY = "type";
    public static final String ASSET_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;

    public static final String ASSET_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "asset_by_tenant_and_search_text";
    public static final String ASSET_BY_TENANT_BY_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "asset_by_tenant_by_type_and_search_text";
    public static final String ASSET_BY_CUSTOMER_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "asset_by_customer_and_search_text";
    public static final String ASSET_BY_CUSTOMER_BY_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "asset_by_customer_by_type_and_search_text";
    public static final String ASSET_BY_TENANT_AND_NAME_VIEW_NAME = "asset_by_tenant_and_name";
    public static final String ASSET_TYPES_BY_TENANT_VIEW_NAME = "asset_types_by_tenant";

    /**
     * Cassandra entity_subtype constants.
     */
    public static final String ENTITY_SUBTYPE_COLUMN_FAMILY_NAME = "entity_subtype";
    public static final String ENTITY_SUBTYPE_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String ENTITY_SUBTYPE_ENTITY_TYPE_PROPERTY = ENTITY_TYPE_PROPERTY;
    public static final String ENTITY_SUBTYPE_TYPE_PROPERTY = "type";

    /**
     * Cassandra alarm constants.
     */
    public static final String ALARM_COLUMN_FAMILY_NAME = "alarm";
    public static final String ALARM_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String ALARM_TYPE_PROPERTY = "type";
    public static final String ALARM_DETAILS_PROPERTY = "details";
    public static final String ALARM_ORIGINATOR_ID_PROPERTY = "originator_id";
    public static final String ALARM_ORIGINATOR_TYPE_PROPERTY = "originator_type";
    public static final String ALARM_SEVERITY_PROPERTY = "severity";
    public static final String ALARM_STATUS_PROPERTY = "status";
    public static final String ALARM_START_TS_PROPERTY = "start_ts";
    public static final String ALARM_END_TS_PROPERTY = "end_ts";
    public static final String ALARM_ACK_TS_PROPERTY = "ack_ts";
    public static final String ALARM_CLEAR_TS_PROPERTY = "clear_ts";
    public static final String ALARM_PROPAGATE_PROPERTY = "propagate";
    public static final String ALARM_VALUE = "value";
    public static final String ALARM_LEVEL = "level";
    public static final String ALARM_JSON = "alarm_json";
    public static final String ALARM_ATTRIBUTE = "attribute";
    public static final String ALARM_TYPE = "alarm_type";
    public static final String ALARM_PARAMS = "params";
    public static final String ALARM_SCRIPT = "alarm_script";
    public static final String ALARM_RESTORE_SCRIPT = "restore_script";
    public static final String ALARM_ALARM_NAME = "alarm_name";
    public static final String ALARM_CREATE_TIME = "create_time";
    public static final String ALARM_JSON_ID = "alarm_json_id";
    public static final String ALARM_JSON_NAME = "alarm_json_name";
    public static final String ALARM_PERIOD = "period";
    public static final String ALARM_RESTORE_TYPE = "restore_type";
    public static final String ALARM_ALARM_TYPE = "alarm_type";
    public static final String ALARM_CYCLE = "alarm_cycle";
    public static final String ALARM_IS_CYCLE = "alarm_is_cycle";
    public static final String ALARM_GROUP = "group_name";

    public static final String ALARM_BY_ID_VIEW_NAME = "alarm_by_id";


    public static final String DATA_MONITOR = "dataMonitor";


    /**
     * Cassandra entity relation constants.
     */
    public static final String RELATION_COLUMN_FAMILY_NAME = "relation";
    public static final String RELATION_FROM_ID_PROPERTY = "from_id";
    public static final String RELATION_FROM_TYPE_PROPERTY = "from_type";
    public static final String RELATION_TO_ID_PROPERTY = "to_id";
    public static final String RELATION_TO_TYPE_PROPERTY = "to_type";
    public static final String RELATION_TYPE_PROPERTY = "relation_type";
    public static final String RELATION_TYPE_GROUP_PROPERTY = "relation_type_group";

    public static final String RELATION_BY_TYPE_AND_CHILD_TYPE_VIEW_NAME = "relation_by_type_and_child_type";
    public static final String RELATION_REVERSE_VIEW_NAME = "reverse_relation";


    /**
     * Cassandra device_credentials constants.
     */
    public static final String DEVICE_CREDENTIALS_COLUMN_FAMILY_NAME = "device_credentials";
    public static final String DEVICE_CREDENTIALS_DEVICE_ID_PROPERTY = DEVICE_ID_PROPERTY;
    public static final String DEVICE_CREDENTIALS_CREDENTIALS_TYPE_PROPERTY = "credentials_type";
    public static final String DEVICE_CREDENTIALS_CREDENTIALS_ID_PROPERTY = "credentials_id";
    public static final String DEVICE_CREDENTIALS_CREDENTIALS_VALUE_PROPERTY = "credentials_value";

    public static final String DEVICE_CREDENTIALS_BY_DEVICE_COLUMN_FAMILY_NAME = "device_credentials_by_device";
    public static final String DEVICE_CREDENTIALS_BY_CREDENTIALS_ID_COLUMN_FAMILY_NAME = "device_credentials_by_credentials_id";

    /**
     * Cassandra widgets_bundle constants.
     */
    public static final String WIDGETS_BUNDLE_COLUMN_FAMILY_NAME = "widgets_bundle";
    public static final String WIDGETS_BUNDLE_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String WIDGETS_BUNDLE_ALIAS_PROPERTY = ALIAS_PROPERTY;
    public static final String WIDGETS_BUNDLE_TITLE_PROPERTY = TITLE_PROPERTY;
    public static final String WIDGETS_BUNDLE_IMAGE_PROPERTY = "image";

    public static final String WIDGETS_BUNDLE_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "widgets_bundle_by_tenant_and_search_text";
    public static final String WIDGETS_BUNDLE_BY_TENANT_AND_ALIAS_COLUMN_FAMILY_NAME = "widgets_bundle_by_tenant_and_alias";

    /**
     * Cassandra widget_type constants.
     */
    public static final String WIDGET_TYPE_COLUMN_FAMILY_NAME = "widget_type";
    public static final String WIDGET_TYPE_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String WIDGET_TYPE_BUNDLE_ALIAS_PROPERTY = "bundle_alias";
    public static final String WIDGET_TYPE_ALIAS_PROPERTY = ALIAS_PROPERTY;
    public static final String WIDGET_TYPE_NAME_PROPERTY = "name";
    public static final String WIDGET_TYPE_DESCRIPTOR_PROPERTY = "descriptor";

    public static final String WIDGET_TYPE_BY_TENANT_AND_ALIASES_COLUMN_FAMILY_NAME = "widget_type_by_tenant_and_aliases";

    /**
     * Cassandra dashboard constants.
     */
    public static final String DASHBOARD_COLUMN_FAMILY_NAME = "dashboard";
    public static final String DASHBOARD_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String DASHBOARD_TITLE_PROPERTY = TITLE_PROPERTY;
    public static final String DASHBOARD_CONFIGURATION_PROPERTY = "configuration";
    public static final String DASHBOARD_ASSIGNED_CUSTOMERS_PROPERTY = "assigned_customers";

    public static final String DASHBOARD_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "dashboard_by_tenant_and_search_text";

    /**
     * Cassandra plugin component metadata constants.
     */
    public static final String COMPONENT_DESCRIPTOR_COLUMN_FAMILY_NAME = "component_descriptor";
    public static final String COMPONENT_DESCRIPTOR_TYPE_PROPERTY = "type";
    public static final String COMPONENT_DESCRIPTOR_SCOPE_PROPERTY = "scope";
    public static final String COMPONENT_DESCRIPTOR_NAME_PROPERTY = "name";
    public static final String COMPONENT_DESCRIPTOR_CLASS_PROPERTY = "clazz";
    public static final String COMPONENT_DESCRIPTOR_CONFIGURATION_DESCRIPTOR_PROPERTY = "configuration_descriptor";
    public static final String COMPONENT_DESCRIPTOR_ACTIONS_PROPERTY = "actions";

    public static final String COMPONENT_DESCRIPTOR_BY_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "component_desc_by_type_search_text";
    public static final String COMPONENT_DESCRIPTOR_BY_SCOPE_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "component_desc_by_scope_type_search_text";
    public static final String COMPONENT_DESCRIPTOR_BY_ID = "component_desc_by_id";

    /**
     * Cassandra event constants.
     */
    public static final String EVENT_COLUMN_FAMILY_NAME = "event";
    public static final String EVENT_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String EVENT_TYPE_PROPERTY = "event_type";
    public static final String EVENT_UID_PROPERTY = "event_uid";
    public static final String EVENT_ENTITY_TYPE_PROPERTY = ENTITY_TYPE_PROPERTY;
    public static final String EVENT_ENTITY_ID_PROPERTY = "entity_id";
    public static final String EVENT_BODY_PROPERTY = "body";

    public static final String EVENT_BY_TYPE_AND_ID_VIEW_NAME = "event_by_type_and_id";
    public static final String EVENT_BY_ID_VIEW_NAME = "event_by_id";

    public static final String DEBUG_MODE = "debug_mode";

    /**
     * Cassandra rule chain constants.
     */
    public static final String RULE_CHAIN_COLUMN_FAMILY_NAME = "rule_chain";
    public static final String RULE_CHAIN_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String RULE_CHAIN_NAME_PROPERTY = "name";
    public static final String RULE_CHAIN_FIRST_RULE_NODE_ID_PROPERTY = "first_rule_node_id";
    public static final String RULE_CHAIN_ROOT_PROPERTY = "root";
    public static final String RULE_CHAIN_CONFIGURATION_PROPERTY = "configuration";

    public static final String RULE_CHAIN_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "rule_chain_by_tenant_and_search_text";

    /**
     * Cassandra rule node constants.
     */
    public static final String RULE_NODE_COLUMN_FAMILY_NAME = "rule_node";
    public static final String RULE_NODE_CHAIN_ID_PROPERTY = "rule_chain_id";
    public static final String RULE_NODE_TYPE_PROPERTY = "type";
    public static final String RULE_NODE_NAME_PROPERTY = "name";
    public static final String RULE_NODE_CONFIGURATION_PROPERTY = "configuration";

    /**
     * Cassandra attributes and timeseries constants.
     */
    public static final String ATTRIBUTES_KV_CF = "attributes_kv_cf";
    public static final String TS_KV_CF = "ts_kv_cf";
    public static final String TS_KV_PARTITIONS_CF = "ts_kv_partitions_cf";
    public static final String TS_KV_LATEST_CF = "ts_kv_latest_cf";

    public static final String PARTITION_COLUMN = "partition";
    public static final String KEY_COLUMN = "key";
    public static final String TS_COLUMN = "ts";
    public static final String VALUE_COLUMN = "value";

    /**
     * cassandra telemetryAttribute constants
     */
    public static final String TELEMETRY_ATTRIBUTE_COLIMN_FAMILY_NAME = "telemetry_attribute";
    public static final String TELEMETRY_ATTRIBUTE_DEVICE_ID = "device_id";
    public static final String TELEMETRY_ATTRIBUTE_ATTRIBUTE_TYPE = "attribute_type";
    public static final String TELEMETRY_ATTRIBUTE_AGREEMENT_NAME = "agreement_name";
    public static final String TELEMETRY_ATTRIBUTE_HARMONIC_NUMBER = "harmonic_number";
    public static final String TELEMETRY_ATTRIBUTE_SHOW_NAME = "show_name";
    public static final String TELEMETRY_ATTRIBUTE_DATA_TYPE = "data_type";
    public static final String TELEMETRY_ATTRIBUTE_DATA_OFFSET = "data_offset";
    public static final String TELEMETRY_ATTRIBUTE_COEFFICIENT = "coefficient";
    public static final String TELEMETRY_ATTRIBUTE_MIN_VALUE = "min_value";
    public static final String TELEMETRY_ATTRIBUTE_MAX_VALUE = "max_value";
    public static final String TELEMETRY_ATTRIBUTE_FORMULA = "formula";
    public static final String TELEMETRY_ATTRIBUTE_STATISTICS = "statistics";
    public static final String TELEMETRY_ATTRIBUTE_UNIT = "unit";
    public static final String TELEMETRY_ATTRIBUTE_UNIT_COEFFICIENT = "unit_coefficient";
    public static final String TELEMETRY_ATTRIBUTE_BYTE_ORDER = "byte_order";
    public static final String TELEMETRY_ATTRIBUTE_CONFIGURATION_PROPERTY = "configuration_property";

    /**
     * virtual
     */
    public static final String VIRTUAL_COLUMN_FAMILY_NAME = "virtual";
    public static final String VIRTUAL_TENANT_ID = "tenant_id";
    public static final String VIRTUAL_NAME = "name";
    public static final String VIRTUAL_GROUP = "virtual_group";
    public static final String VIRTUAL_TYPE = "type";
    public static final String VIRTUAL_UNIT = "unit";
    public static final String VIRTUAL_FORULA = "formula";
    public static final String VIRTUAL_SERIAL_NUMBER = "serial_number";


    public static final String CONSTANTS_ATTRIBUTE_NAME = "constants_attribute";

    /**
     * menu pool
     */
    public static final String MENU_POOL_NAME = "menu_pool";
    public static final String MENU_POOL_PARENT_ID = "parent_id";
    public static final String MENU_POOL_DEFAULT_NAME = "default_name";
    public static final String MENU_POOL_TYPE = "type";
    public static final String MENU_POOL_ORDER_NUM = "order_num";
    public static final String MENU_POOL_PARAMS = "params";
    public static final String MENU_POOL_STATUS = "status";
    public static final String MENU_POOL_FLAG_DELETE = "flag_delete";
    public static final String MENU_POOL_ADDITIONAL_INFO = "additional_info";


    public static final UUID MENU_POOL_ROOT = UUIDs.startOf(9527);
    public static final String MENU_POOL_ROOT_STR = "1b21dd218cd73808080808080808080";
    public static final Integer MENU_POOL_STATUS_DISPLAY = 0;
    public static final Integer MENU_POOL_STATUS_HIDE = 1;
    public static final Integer MENU_POOL_FLAG_DELETE_FALSE = 0;
    public static final Integer MENU_POOL_FLAG_DELETE_TRUE = 1;

    /**
     * menu button
     */
    public static final String MENU_BUTTON = "menu_button";
    public static final String MENU_BUTTON_ID = "id";
    public static final String MENU_BUTTON_MENU_ID = "menu_id";
    public static final String MENU_BUTTON_NAME = "name";
    public static final String MENU_BUTTON_PERMISSIONS = "permissions";
    public static final String MENU_BUTTON_ICON = "icon";
    public static final String MENU_BUTTON_ADDITIONAL_INFO = "additional_info";

    /**
     * menu button role
     */
    public static final String MENU_BUTTON_ROLE = "menu_button_role";
    public static final String MENU_BUTTON_ROLE_MENU_BUTTON_ID = "menu_button_id";
    public static final String MENU_BUTTON_ROLE_ID = "role_id";


    /**
     * menu tenant
     */
    public static final String MENU_TENANT_NAME = "menu_tenant";
    public static final String MENU_TENANT_TENANT_ID = "tenant_id";
    public static final String MENU_TENANT_MENU_POOL_ID = "menu_pool_id";
    public static final String MENU_TENANT_IS_EXTENSION_MENU = "is_extension_menu";


    /**
     * menu customer
     */
    public static final String MENU_CUSTOMER_NAME = "menu_customer";
    public static final String MENU_CUSTOMER_NAME_PROPERTY = "name";
    public static final String MENU_CUSTOMER_DEFAULT_NAME = "default_name";
    public static final String MENU_CUSTOMER_PARENT_ID = "parent_id";
    public static final String MENU_CUSTOMER_PERMS = "perms";
    public static final String MENU_CUSTOMER_ICON = "icon";
    public static final String MENU_CUSTOMER_ORDER_NUM = "order_num";
    public static final String MENU_CUSTOMER_URL = "url";
    public static final String MENU_CUSTOMER_STATUS = "status";
    public static final String MENU_CUSTOMER_FLAG_DELETE = "flag_delete";
    public static final String MENU_CUSTOMER_MENT_TENANT_ID = "menu_tenant_id";
    public static final String MENU_CUSTOMER_ADDITIONAL_INFO = "additional_info";
    public static final UUID MENU_CUSTOMER_ROOT = UUIDs.startOf(8888);
    public static final UUID MENU_CUSTOMER_UUID = UUIDs.startOf(6666);

    public static final Integer MENU_CUSTOMER_STATUS_DISPLAY = 0;
    public static final Integer MENU_CUSTOMER_STATUS_HIDE = 1;
    public static final Integer MENU_CUSTOMER_FLAG_DELETE_FALSE = 0;
    public static final Integer MENU_CUSTOMER_FLAG_DELETE_TRUE = 1;

    /**
     * role
     */
    public static final String CUSTOMER_ROLE_NAME = "customer_role";
    public static final String CUSTOMER_ROLE_NAME_PROPERTY = "name";
    public static final String CUSTOMER_ROLE_STATUS = "status";
    public static final String CUSTOMER_ROLE_OPEN_ID = "open_id";
    public static final String CUSTOMER_ROLE_FLAG_DELETE = "flag_delete";
    public static final String CUSTOMER_ROLE_ADDITIONAL_INFO = "additional_info";
    public static final String CUSTOMER_ROLE_TENANT_ID = "tenant_id";

    public static final Integer CUSTOMER_ROLE_STATUS_DISPLAY = 0;
    public static final Integer CUSTOMER_ROLE_STATUS_HIDE = 1;
    public static final Integer CUSTOMER_ROLE_FLAG_DELETE_FALSE = 0;
    public static final Integer CUSTOMER_ROLE_FLAG_DELETE_TRUE = 1;

    /**
     * menu role
     */
    public static final String CUSTOMER_MENU_ROLE = "customer_menu_role";
    public static final String CUSTOMER_MENU_ROLE_MENU_ID = "menu_id";
    public static final String CUSTOMER_MENU_ROLE_ROLE_ID = "role_id";

    /**
     * user role
     */
    public static final String CUSTOMER_USER_ROLE = "customer_user_role";
    public static final String CUSTOMER_USER_ROLE_MENU_ID = "user_id";
    public static final String CUSTOMER_USER_ROLE_ROLE_ID = "role_id";

    /**
     * repair
     */
    public static final String REPAIR_NAME = "repair";
    public static final String REPAIR_DEVICE_ID = "device_id";
    public static final String REPAIR_CREATE_TIME = "created_time";
    public static final String REPAIR_REPAIR_START_TIME = "repair_start_time";
    public static final String REPAIR_REPAIR_END_TIME = "repair_end_time";
    public static final String REPAIR_STATUS = "status";
    public static final String REPAIR_REMARK = "remark";
    public static final String REPAIR_TENANT_ID = "tenant_id";


    /**
     * maintain
     */

    public static final String MAINTAIN_NAME = "maintain";
    public static final String MAINTAIN_JSON_NAME = "name";
    public static final String MAINTAIN_JSON = "maintain_json";
    public static final String MAINTAIN_RECORD_NAME = "maintain_record";
    public static final String MAINTAIN_RECORD_START = "start_time";
    public static final String MAINTAIN_RECORD_END = "end_time";
    public static final String MAINTAIN_ID = "maintain_id";
    public static final String MAINTAIN_USER = "maintain_user";
    public static final String MAINTAIN_PARAMS = "params";
    public static final String MAINTAIN_TYPE = "type";
    public static final String MAINTAIN_PERIOD = "period";

    /**
     * lotCard
     */
    public static final String LOT_CARD_NAME = "lot_card";
    public static final String LOT_CARD_ID = "card_id";
    public static final String NUMBER = "number";
    public static final String OPERATOR = "operator";
    public static final String DATA_FLOW = "data_flow";


    /**
     * option log
     */
    public static final String OPTION_LOG_NAME = "option_log";
    public static final String OPTION_LOG_USER_ID = "user_id";
    public static final String OPTION_LOG_TENANT_ID = "tenant_id";
    public static final String OPTION_LOG_FIRST_NAME = "first_name";
    public static final String OPTION_LOG_AUTHORITY = "authority";
    public static final String OPTION_LOG_OPTIONS = "options";
    public static final String OPTION_LOG_CREATE_TIME = "create_time";
    public static final String OPTION_LOG_ADDITIONAL_INFO = "additional_info";
    public static final String OPTION_LOG_INFO = "info";
    public static final String OPTION_LOG_TYPE = "type";
    public static final String OPTION_LOG_STATUS = "status";
    public static final String OPTION_LOG_EXAMINE_ID = "examine_id";
    public static final String OPTION_LOG_EXAMINE_NAME = "examine_name";
    public static final String OPTION_LOG_EXAMINE_TENANT_ID = "examine_tenant_id";
    public static final String OPTION_LOG_EXAMINE_TIME = "examine_time";

    /**
     * project
     */
    public static final String PROJECT_TABLE = "project";
    public static final String PROJECT_PARENT_ID = "parent_id";
    public static final String PROJECT_CREATE_TIME = "create_time";
    public static final String PROJECT_TENANT_ID = "tenant_id";
    public static final String PROJECT_NAME = "name";
    public static final String PROJECT_ADDITIONAL_INFO = "additional_info";

    /**
     * project relation
     */
    public static final String PROJECT_RELATION_TABLE = "project_relation";
    public static final String PROJECT_RELATION_PROJECT_ID = "project_id";
    public static final String PROJECT_RELATION_ENTITY_ID = "entity_id";
    public static final String PROJECT_RELATION_ENTITY_TYPE = "entity_type";
    public static final String PROJECT_RELATION_CREATE_TIME = "create_time";

    /**
     * menu resource
     */
    public static final String TB_MENU_RESOURCE_TABLE = "tb_menu_resource";
    public static final String TB_MENU_RESOURCE_IMG = "img";

    /**
     * tenant menu
     */
    public static final String TB_TENANT_MENU_TABLE = "tb_tenant_menus";

    /**
     * tenant menu role
     */
    public static final String TB_TENANT_MENU_ROLE_TABLE = "tb_tenant_menus_role";
    public static final String TB_TENANT_MENU_ROLE_APPLICATION_ID = "tenant_application_id";
    public static final String TB_TENANT_MENU_ROLE_MENU_ID = "menu_id";
    public static final String TB_TENANT_MENU_ROLE_ROLE_ID = "role_id";

    /**
     * data backup
     */
    public static final String DATA_BACKUP_TABLE = "data_backup";
    public static final String DATA_BACKUP_TYPE = "type";
    public static final String DATA_BACKUP_CREATE_TIME = "create_time";
    public static final String DATA_BACKUP_END_TIME = "end_time";
    public static final String DATA_BACKUP_CREATER = "creater";
    public static final String DATA_BACKUP_CREATE_BY = "create_by";

    /**
     * 报警Version2  报警规则
     */
    public static final String V2_TB_ALARM_RULE_TABLE = "tb_alarm_rule";
    public static final String V2_TB_ALARM_RULE_STATION_ID = "station_id";
    public static final String V2_TB_ALARM_RULE_STATION_ATTR_ID = "station_attr_id";
    public static final String V2_TB_ALARM_RULE_RULE_TYPE = "rule_type";
    public static final String V2_TB_ALARM_RULE_RULE_PARAM = "rule_param";
    public static final String V2_TB_ALARM_RULE_STATUS = "status";
    public static final String V2_TB_ALARM_RULE_ALARM_LEVEL = "alarm_level";
    public static final String V2_TB_ALARM_RULE_ALARM_TYPE = "alarm_type";
    public static final String V2_TB_ALARM_RULE_PROCESS_METHOD = "process_method";
    public static final String V2_TB_ALARM_RULE_REMOTE_STATION_ATTR_ID = "remote_station_attr_id";
    public static final String V2_TB_ALARM_RULE_REMOTE_VIDEO_ID = "remote_video_id";
    public static final String V2_TB_ALARM_RULE_REMOTE_RE_ALARM_TYPE = "re_alarm_type";
    public static final String V2_TB_ALARM_RULE_REMOTE_RE_ALARM_VALUE = "re_alarm_value";
    public static final String V2_TB_ALARM_RULE_REMOTE_RE_ALARM_UNIT = "re_alarm_unit";
    public static final String V2_TB_ALARM_RULE_SEND_WAY = "send_way";

    /**
     * 报警Version2 报警中心
     */
    public static final String V2_TB_ALARM_CENTER_TABLE = "tb_alarm_center";
    public static final String V2_TB_ALARM_CENTER_STATION_ID = "station_id";
    public static final String V2_TB_ALARM_CENTER_TITLE = "title";
    public static final String V2_TB_ALARM_CENTER_TIME = "time";
    public static final String V2_TB_ALARM_CENTER_END_TIME = "end_time";
    public static final String V2_TB_ALARM_CENTER_ALARM_TYPE = "alarm_type";
    public static final String V2_TB_ALARM_CENTER_ALARM_LEVEL = "alarm_level";
    public static final String V2_TB_ALARM_CENTER_ALARM_INFO = "alarm_info";
    public static final String V2_TB_ALARM_CENTER_PROCESS_METHOD = "process_method";
    public static final String V2_TB_ALARM_CENTER_ALARM_STATUS = "alarm_status";
    public static final String V2_TB_ALARM_CENTER_PROCESS_STATUS = "process_status";
    public static final String V2_TB_ALARM_CENTER_WORK_ORDER_ID = "work_order_id";
    public static final String V2_TB_ALARM_CENTER_ALARM_RULE_ID = "alarm_rule_id";
    public static final String V2_TB_ALARM_CENTER_DEVICE_ID = "device_id";
    public static final String V2_TB_ALARM_CENTER_OPTION_USER = "option_user";



    /**
     * 检修标准库
     */
    public static final String REPAIR_STANDARD_TABLE = "tb_repair_standard";
    public static final String REPAIR_STANDARD_NAME = "name";
    public static final String REPAIR_STANDARD_DEVICE_TYPE = "device_type";
    public static final String REPAIR_STANDARD_REMARK = "remark";
    public static final String REPAIR_STANDARD_DETAIL = "detail";

    /**
     * 检修计划
     */
    public static final String REPAIR_PLAN_TABLE = "tb_repair_plan";
    public static final String REPAIR_PLAN_NAME = "name";
    public static final String REPAIR_PLAN_EXECUTE_TIME = "execute_time";
    public static final String REPAIR_PLAN_PERIOD_TIME = "period_time";
    public static final String REPAIR_PLAN_STATUS = "status";

    /**
     * 检修计划子表
     */
    public static final String REPAIR_PLAN_C_TABLE = "tb_repair_plan_c";
    public static final String REPAIR_PLAN_C_MAIN_ID = "main_id";
    public static final String REPAIR_PLAN_C_STANDARD_ID = "standard_id";
    public static final String REPAIR_PLAN_C_ORDER_NUMBER = "order_number";

    /**
     * 检修触发性计划子表
     */
    public static final String REPAIR_PLAN_TRIGGER_TABLE = "tb_repair_plan_trigger";
    public static final String REPAIR_JOB_TRIGGER_TABLE = "tb_repair_job_trigger";
    public static final String REPAIR_PLAN_TRIGGER_MAIN_ID = "main_id";
    public static final String REPAIR_PLAN_TRIGGER_PROJECT_ID = "project_id";
    public static final String REPAIR_PLAN_TRIGGER_DEVICE_ID = "device_id";
    public static final String REPAIR_PLAN_TRIGGER_DETAIL = "detail";
    public static final String REPAIR_PLAN_TRIGGER_STANDARD_ID = "standard_id";

    /**
     * 检修任务
     */
    public static final String REPAIR_JOB_TABLE = "tb_repair_job";
    public static final String REPAIR_JOB_NAME = "name";
    public static final String REPAIR_JOB_TYPE = "type";
    public static final String REPAIR_JOB_STATUS = "status";
    public static final String REPAIR_JOB_EXECUTE_TIME = "execute_time";
    public static final String REPAIR_JOB_COMPLETE_TIME = "complete_time";
    public static final String REPAIR_JOB_ACCEPTANCE_TIME = "acceptance_time";
    public static final String REPAIR_JOB_END_TIME = "end_time";

    /**
     * 检修任务子表
     */
    public static final String REPAIR_JOB_C_TABLE = "tb_repair_job_c";
    public static final String REPAIR_JOB_C_MAIN_ID = "main_id";
    public static final String REPAIR_JOB_C_PROJECT_ID = "project_id";
    public static final String REPAIR_JOB_C_DEVICE_ID = "device_id";
    public static final String REPAIR_JOB_C_START_TIME = "start_time";
    public static final String REPAIR_JOB_C_END_TIME = "end_time";
    public static final String REPAIR_JOB_C_STATUS = "status";
    public static final String REPAIR_JOB_C_STANDARD_NAME = "standard_name";
    public static final String REPAIR_JOB_C_STANDARD_DETAIL = "standard_detail";
    public static final String REPAIR_JOB_C_IMGS = "imgs";
    public static final String REPAIR_JOB_C_VOICE_FILE = "voice_file";
    public static final String REPAIR_JOB_C_REMARK = "remark";
    public static final String REPAIR_JOB_C_PROCESS_REMARK = "process_remark";
    public static final String REPAIR_JOB_C_ORDER_NUMBER = "order_number";


    /**
     * 保养标准库
     */
    public static final String MAINTENANCE_STANDARD_TABLE = "tb_maintenance_standard";
    public static final String MAINTENANCE_STANDARD_NAME = "name";
    public static final String MAINTENANCE_STANDARD_DEVICE_TYPE = "device_type";
    public static final String MAINTENANCE_STANDARD_REMARK = "remark";
    public static final String MAINTENANCE_STANDARD_DETAIL = "detail";

    /**
     * 保养计划
     */
    public static final String MAINTENANCE_PLAN_TABLE = "tb_maintenance_plan";
    public static final String MAINTENANCE_PLAN_NAME = "name";
    public static final String MAINTENANCE_PLAN_EXECUTE_TIME = "execute_time";
    public static final String MAINTENANCE_PLAN_PERIOD_TIME = "period_time";
    public static final String MAINTENANCE_PLAN_STATUS = "status";

    /**
     * 保养计划子表
     */
    public static final String MAINTENANCE_PLAN_C_TABLE = "tb_maintenance_plan_c";
    public static final String MAINTENANCE_PLAN_C_MAIN_ID = "main_id";
    public static final String MAINTENANCE_PLAN_C_STANDARD_ID = "standard_id";
    public static final String MAINTENANCE_PLAN_C_ORDER_NUMBER = "order_number";

    /**
     * 检修触发性计划子表
     */
    public static final String MAINTENANCE_PLAN_TRIGGER_TABLE = "tb_maintenance_plan_trigger";
    public static final String MAINTENANCE_JOB_TRIGGER_TABLE = "tb_maintenance_job_trigger";
    public static final String MAINTENANCE_PLAN_TRIGGER_MAIN_ID = "main_id";
    public static final String MAINTENANCE_PLAN_TRIGGER_PROJECT_ID = "project_id";
    public static final String MAINTENANCE_PLAN_TRIGGER_DEVICE_ID = "device_id";
    public static final String MAINTENANCE_PLAN_TRIGGER_DETAIL = "detail";
    public static final String MAINTENANCE_PLAN_TRIGGER_STANDARD_ID = "standard_id";

    /**
     * 检修任务
     */
    public static final String MAINTENANCE_JOB_TABLE = "tb_maintenance_job";
    public static final String MAINTENANCE_JOB_NAME = "name";
    public static final String MAINTENANCE_JOB_STATUS = "status";
    public static final String MAINTENANCE_JOB_EXECUTE_TIME = "execute_time";
    public static final String MAINTENANCE_JOB_COMPLETE_TIME = "complete_time";
    public static final String MAINTENANCE_JOB_ACCEPTANCE_TIME = "acceptance_time";
    public static final String MAINTENANCE_JOB_END_TIME = "end_time";

    /**
     * 检修任务子表
     */
    public static final String MAINTENANCE_JOB_C_TABLE = "tb_maintenance_job_c";
    public static final String MAINTENANCE_JOB_C_MAIN_ID = "main_id";
    public static final String MAINTENANCE_JOB_C_PROJECT_ID = "project_id";
    public static final String MAINTENANCE_JOB_C_DEVICE_ID = "device_id";
    public static final String MAINTENANCE_JOB_C_START_TIME = "start_time";
    public static final String MAINTENANCE_JOB_C_END_TIME = "end_time";
    public static final String MAINTENANCE_JOB_C_STATUS = "status";
    public static final String MAINTENANCE_JOB_C_STANDARD_NAME = "standard_name";
    public static final String MAINTENANCE_JOB_C_STANDARD_DETAIL = "standard_detail";
    public static final String MAINTENANCE_JOB_C_IMGS = "imgs";
    public static final String MAINTENANCE_JOB_C_VOICE_FILE = "voice_file";
    public static final String MAINTENANCE_JOB_C_REMARK = "remark";
    public static final String MAINTENANCE_JOB_C_PROCESS_REMARK = "process_remark";
    public static final String MAINTENANCE_JOB_C_ORDER_NUMBER = "order_number";

    /**
     * 备件库
     */
    public static final String COMPONENT_STORAGE_TABLE = "tb_component_storage";
    public static final String COMPONENT_STORAGE_CODE = "code";
    public static final String COMPONENT_STORAGE_TYPE = "type";
    public static final String COMPONENT_STORAGE_SPECIFICATION = "specification";
    public static final String COMPONENT_STORAGE_UNIT = "unit";
    public static final String COMPONENT_STORAGE_NUMBER = "number";
    public static final String COMPONENT_STORAGE_IS_DEL = "is_del";

    /**
     * 备件出入库
     */
    public static final String COMPONENT_STORAGE_OPTION_TABLE = "tb_component_option";
    public static final String COMPONENT_STORAGE_OPTION_TYPE = "type";
    public static final String COMPONENT_STORAGE_OPTION_CODE = "code";
    public static final String COMPONENT_STORAGE_OPTION_USERNAME = "username";
    public static final String COMPONENT_STORAGE_OPTION_TIME = "time";
    public static final String COMPONENT_STORAGE_OPTION_REMARK = "remark";

    /**
     * 备件出库明细
     */
    public static final String COMPONENT_STORAGE_OPTION_C_TABLE = "tb_component_option_c";
    public static final String COMPONENT_STORAGE_OPTION_C_MAIN_ID = "main_id";
    public static final String COMPONENT_STORAGE_OPTION_C_COMPONENT_ID = "component_id";
    public static final String COMPONENT_STORAGE_OPTION_C_ORDER_NUMBER = "order_number";
    public static final String COMPONENT_STORAGE_OPTION_C_NUMBER = "number";

    /**
     * 工单
     */
    public static final String WORK_ORDER_TABLE = "tb_work_order";
    public static final String WORK_ORDER_TYPE = "type";
    public static final String WORK_ORDER_CODE = "code";
    public static final String WORK_ORDER_NAME = "name";
    public static final String WORK_ORDER_QUESTION_REMARK = "question_remark";
    public static final String WORK_ORDER_QUESTION_FILE = "question_file";
    public static final String WORK_ORDER_STATUS = "status";
    public static final String WORK_ORDER_IS_OUTSIDER = "is_outsider";
    public static final String WORK_ORDER_EXECUTOR = "executor";
    public static final String WORK_ORDER_DEADLINE_TIME = "deadline_time";
    public static final String WORK_ORDER_REMARK = "remark";
    public static final String WORK_ORDER_IS_END = "is_end";
    public static final String WORK_ORDER_REAL_END_TIME = "real_end_time";
    public static final String WORK_ORDER_REAL_START_TIME = "real_start_time";
    public static final String WORK_ORDER_ACCEPTANCE_TIME = "acceptance_time";
    public static final String WORK_ORDER_ACCEPTANCE_REMARK = "acceptance_remark";
    public static final String WORK_ORDER_ACCEPTANCE_IMGS = "acceptance_imgs";
    public static final String WORK_ORDER_ACCEPTANCE_VOICE_FILE = "acceptance_voice_file";
    public static final String WORK_ORDER_PROCESS_IMGS = "process_imgs";
    public static final String WORK_ORDER_PROCESS_VOICE_FILE = "process_voice_file";
    public static final String WORK_ORDER_PRIORITY = "priority";
    public static final String WORK_ORDER_CONTENT_ID = "content_id";


    /**
     * 水务-巡检标准库
     */
    public static final String SHUIWU_CRITERION_TABLE = "z_shuiwu_criterion";
    public static final String SHUIWU_CRITERION_NAME = "name";
    public static final String SHUIWU_CRITERION_DEVICE_TYPE = "device_type";
    public static final String SHUIWU_CRITERION_REMARK = "remark";
    public static final String SHUIWU_CRITERION_DETAIL = "detail";
    public static final String SHUIWU_CRITERION_CREATOR = "creator";

    /**
     * 水务-巡检任务管理主表
     */
    public static final String SHUIWU_XJRWGL_M_TABLE = "z_shuiwu_xjrwgl_m";
    public static final String SHUIWU_XJRWGL_M_CONTENT = "content";
    public static final String SHUIWU_XJRWGL_M_STATUS = "status";
    public static final String SHUIWU_XJRWGL_M_EXECUTE_TIME = "execute_time";
    public static final String SHUIWU_XJRWGL_M_LIMIT_TIME = "limit_time";
    public static final String SHUIWU_XJRWGL_M_PERIOD_TIME = "period_time";
    public static final String SHUIWU_XJRWGL_M_USERS = "users";

    /**
     * 水务-巡检任务管理子表
     */
    public static final String SHUIWU_XJRWGL_C_TABLE = "z_shuiwu_xjrwgl_c";
    public static final String SHUIWU_XJRWGL_C_MAIN_ID = "main_id";
    public static final String SHUIWU_XJRWGL_C_PROJECT_ID = "project_id";
    public static final String SHUIWU_XJRWGL_C_DEVICE_ID = "device_id";
    public static final String SHUIWU_XJRWGL_C_CRITERION_ID = "criterion_id";
    public static final String SHUIWU_XJRWGL_C_ORDER_NUMBER = "order_num";

    /**
     * 水务-巡检任务管理主表
     */
    public static final String SHUIWU_XJRW_M_TABLE = "z_shuiwu_xjrw_m";
    public static final String SHUIWU_XJRW_M_CONTENT = "content";
    public static final String SHUIWU_XJRW_M_EXECUTE_TIME = "execute_time";
    public static final String SHUIWU_XJRW_M_LIMIT_TIME = "limit_time";
    public static final String SHUIWU_XJRW_M_PERIOD_TIME = "period_time";
    public static final String SHUIWU_XJRW_M_USERS = "users";
    public static final String SHUIWU_XJRW_M_STATUS = "status";

    /**
     * 水务-巡检任务子表
     */
    public static final String SHUIWU_XJRW_C_TABLE = "z_shuiwu_xjrw_c";
    public static final String SHUIWU_XJRW_C_MAIN_ID = "main_id";
    public static final String SHUIWU_XJRW_C_PROJECT_ID = "project_id";
    public static final String SHUIWU_XJRW_C_DEVICE_ID = "device_id";
    public static final String SHUIWU_XJRW_C_CRITERION_NAME = "criterion_name";
    public static final String SHUIWU_XJRW_C_CRITERION_DETAIL = "criterion_detail";
    public static final String SHUIWU_XJRW_C_STATUS = "status";
    public static final String SHUIWU_XJRW_C_RECORD_STATUS = "record_status";
    public static final String SHUIWU_XJRW_C_IMGS = "imgs";
    public static final String SHUIWU_XJRW_C_VOICE_FILE = "voice_file";
    public static final String SHUIWU_XJRW_C_OK = "ok";
    public static final String SHUIWU_XJRW_C_JOB_REMARK = "job_remark";

    /**
     * 水务-管道信息
     */
    public static final String SHUIWU_PIPE_TABLE = "z_shuiwu_pipe";
    public static final String SHUIWU_PIPE_NAME = "name";
    public static final String SHUIWU_PIPE_CODE = "code";
    public static final String SHUIWU_PIPE_TYPE = "type";
    public static final String SHUIWU_PIPE_START_POINT = "start_point_id";
    public static final String SHUIWU_PIPE_END_POINT = "end_point_id";
    public static final String SHUIWU_PIPE_MATERIAL = "material";
    public static final String SHUIWU_PIPE_CALIBER = "caliber";
    public static final String SHUIWU_PIPE_BUILD_TIME = "build_time";
    public static final String SHUIWU_PIPE_START_USE_TIME = "start_use_time";
    public static final String SHUIWU_PIPE_DISCARD_TIME = "discard_time";
    public static final String SHUIWU_PIPE_BURIED_METHOD = "buried_method";
    public static final String SHUIWU_PIPE_ADDRESS = "address";

    /**
     * 水务-管道节点
     */
    public static final String SHUIWU_PIPE_POINT_TABLE = "z_shuiwu_pipe_point";
    public static final String SHUIWU_PIPE_POINT_CODE = "code";
    public static final String SHUIWU_PIPE_POINT_NAME = "name";
    public static final String SHUIWU_PIPE_POINT_LOCATION = "location";
    public static final String SHUIWU_PIPE_POINT_POINT_ELEVATION = "point_elevation";
    public static final String SHUIWU_PIPE_POINT_POINT_DEPTH = "depth";

    /**
     * 水务-管道节点
     */
    public static final String SHUIWU_VALVE_TABLE = "z_shuiwu_valve";
    public static final String SHUIWU_VALVE_NAME = "name";
    public static final String SHUIWU_VALVE_CODE = "code";
    public static final String SHUIWU_VALVE_ADDRESS = "address";
    public static final String SHUIWU_VALVE_LOCATION = "location";

    /**
     * 水务-出口流量平均值
     */
    public static final String SHUIWU_LIULIANG_AVG_TABLE = "z_shuiwu_liuliang_avg";
    public static final String SHUIWU_LIULIANG_AVG_VALUE_1 = "value_1";
    public static final String SHUIWU_LIULIANG_AVG_VALUE_2 = "value_2";
    public static final String SHUIWU_LIULIANG_AVG_VALUE_3 = "value_3";
    public static final String SHUIWU_LIULIANG_AVG_CREATE_TIME = "create_time";


    /**
     * 关注工单
     */
    public static final String ORDER_FOLLOW_TABLE = "tb_order_follow";
    public static final String ORDER_FOLLOW_TIME = "time";
    public static final String ORDER_FOLLOW_USER_ID = "user_id";
    public static final String ORDER_FOLLOW_ORDER_ID = "order_id";

    /**
     * 任务-历史轨迹
     */
    public static final String TASK_TRACK_HISTORY_TABLE = "tb_task_track_history";
    public static final String TASK_TRACK_HISTORY_CONTENT_ID = "content_id";
    public static final String TASK_TRACK_HISTORY_LOCATION = "location";
    public static final String TASK_TRACK_HISTORY_TS = "ts";
    public static final String TASK_TRACK_HISTORY_USER_ID = "user_id";

    /**
     * 水表真实表号-IEMI关系
     */
    public static final String SHUIWU_WATER_METER_IEMI_RELATION = "shuiwu_water_meter_iemi_relation";

    /**
     * 营收数据录入
     */
    public static final String TB_OPERATING_INCOME_INPUT_TABLE = "tb_operating_income_input";
    public static final String TB_OPERATING_INCOME_INPUT_TS = "ts";
    public static final String TB_OPERATING_INCOME_INPUT_STATION_ID = "station_id";
    public static final String TB_OPERATING_INCOME_INPUT_WATER_SALES = "water_sales";
    public static final String TB_OPERATING_INCOME_INPUT_MONEY = "money";
    public static final String TB_OPERATING_INCOME_INPUT_UPDATE_USER = "update_user";
    public static final String TB_OPERATING_INCOME_INPUT_UPDATE_TIME = "update_time";



    /**
     * 设备台账 assets_account
     */
    public static final String ASSETS_ACCOUNT_TABLE = "shuiwu_assets_account";
    public static final String ASSETS_ACCOUNT_ID = "id";
    public static final String ASSETS_ACCOUNT_DEVICE_ID = "device_id";
    public static final String ASSETS_ACCOUNT_DEVICE_NO = "device_no";
    public static final String ASSETS_ACCOUNT_DEVICE_NAME = "device_name";
    public static final String ASSETS_ACCOUNT_UNIT = "unit";
    public static final String ASSETS_ACCOUNT_DEVICE_TYPE = "device_type";
    public static final String ASSETS_ACCOUNT_SPECIFICATION_MODEL = "specification_model";
    public static final String ASSETS_ACCOUNT_BRAND = "brand";
    public static final String ASSETS_ACCOUNT_SUPPLIER = "supplier";
    public static final String ASSETS_ACCOUNT_DEVICE_SOURCE = "device_source";
    public static final String ASSETS_ACCOUNT_PURCHASE_AMOUNT = "purchase_amount";
    public static final String ASSETS_ACCOUNT_PURCHASE_TIME = "purchase_time";
    public static final String ASSETS_ACCOUNT_WARRANTY_TIME = "warranty_time";
    public static final String ASSETS_ACCOUNT_ENABLE_TIME = "enable_time";
    public static final String ASSETS_ACCOUNT_EXPECT_SCRAP_TIME = "expect_scrap_time";
    public static final String ASSETS_ACCOUNT_DEVICE_STATUS = "device_status";
    public static final String ASSETS_ACCOUNT_DEVICE_GRADE = "device_grade";
    public static final String ASSETS_ACCOUNT_DIRECTOR = "director";
    public static final String ASSETS_ACCOUNT_PROJECT_ID = "project_id";
    public static final String ASSETS_ACCOUNT_DEVICE_POSITION = "device_position";
    public static final String ASSETS_ACCOUNT_LOCATION = "location";
    public static final String ASSETS_ACCOUNT_REMARK = "remark";
    public static final String ASSETS_ACCOUNT_DEPRECIATION_METHOD = "depreciation_method";
    public static final String ASSETS_ACCOUNT_USE_LIFE = "use_life";
    public static final String ASSETS_ACCOUNT_ORIGIN_VALUE = "origin_value";
    public static final String ASSETS_ACCOUNT_NET_RESIDUAL_RATE = "net_residual_rate";
    public static final String ASSETS_ACCOUNT_NET_VALUE = "net_value";
    public static final String ASSETS_ACCOUNT_DEPRECIATION_AMOUNT_MONTH = "depreciation_amount_month";
    public static final String ASSETS_ACCOUNT_TEXT1 = "text1";
    public static final String ASSETS_ACCOUNT_TEXT2 = "text2";
    public static final String ASSETS_ACCOUNT_TEXT3 = "text3";
    public static final String ASSETS_ACCOUNT_TEXT4 = "text4";
    public static final String ASSETS_ACCOUNT_TEXT5 = "text5";
    public static final String ASSETS_ACCOUNT_TEXT6 = "text6";
    public static final String ASSETS_ACCOUNT_TEXT7 = "text7";
    public static final String ASSETS_ACCOUNT_TEXT8 = "text8";
    public static final String ASSETS_ACCOUNT_NUMBER1 = "number1";
    public static final String ASSETS_ACCOUNT_NUMBER2 = "number2";
    public static final String ASSETS_ACCOUNT_DATE1 = "date1";
    public static final String ASSETS_ACCOUNT_DATE2 = "date2";
    public static final String ASSETS_ACCOUNT_IMGS = "imgs";
    public static final String ASSETS_ACCOUNT_SUB_DEVICE_IDS = "sub_device_ids";
    public static final String ASSETS_ACCOUNT_PARENT_DEVICE_IDS = "parent_device_ids";
    public static final String ASSETS_ACCOUNT_SPARE_PART_IDS = "spare_part_ids";
    public static final String ASSETS_ACCOUNT_CREATE_TIME = "create_time";
    public static final String ASSETS_ACCOUNT_UPDATE_TIME = "update_time";
    public static final String ASSETS_ACCOUNT_TENANT_ID = "tenant_id";
    public static final String ASSETS_ACCOUNT_FILE_IDS = "file_ids";

    /**
     * 设备台账文件 assets_file
     */
    public static final String ASSETS_FILE_TABLE = "shuiwu_assets_file";
    public static final String ASSETS_FILE_ID = "id";
    public static final String ASSETS_FILE_NAME = "name";
    public static final String ASSETS_FILE_CREATE_TIME = "create_time";
    public static final String ASSETS_FILE_PROJECT_ID = "project_id";
    public static final String ASSETS_FILE_TENANT_ID = "tenant_id";

    /**
     * 设备台账备件 assets_account_spare_park
     */
    public static final String ASSETS_ACCOUNT_COMPONENT_TABLE = "shuiwu_assets_account_component";
    public static final String ASSETS_ACCOUNT_COMPONENT_ID = "id";
    public static final String ASSETS_ACCOUNT_COMPONENT_PID = "pid";
    public static final String ASSETS_ACCOUNT_COMPONENT_COMPONENT_ID = "component_id";
    public static final String ASSETS_ACCOUNT_COMPONENT_NUM = "num";
    public static final String ASSETS_ACCOUNT_COMPONENT_CREATE_TIME = "create_time";
    public static final String ASSETS_ACCOUNT_COMPONENT_PROJECT_ID = "project_id";
    public static final String ASSETS_ACCOUNT_COMPONENT_TENANT_ID = "tenant_id";



    /**
     * 设备转移 assets_transfer
     */
    public static final String ASSETS_TRANSFER_TABLE = "shuiwu_assets_transfer";
    public static final String ASSETS_TRANSFER_ID = "id";
    public static final String ASSETS_TRANSFER_TRANSFER_NO = "transfer_no";
    public static final String ASSETS_TRANSFER_PROJECT_ID = "project_id";
    public static final String ASSETS_TRANSFER_POSITION = "position";
    public static final String ASSETS_TRANSFER_DIRECTOR = "director";
    public static final String ASSETS_TRANSFER_REMARK = "remark";
    public static final String ASSETS_TRANSFER_DEVICE_IDS = "device_ids";
    public static final String ASSETS_TRANSFER_APPLICANT_ID = "applicant_id";
    public static final String ASSETS_TRANSFER_REVIEWER_ID = "reviewer_id";
    public static final String ASSETS_TRANSFER_STATUS = "status";
    public static final String ASSETS_TRANSFER_CREATE_TIME = "create_time";
    public static final String ASSETS_TRANSFER_UPDATE_TIME = "update_time";
    public static final String ASSETS_TRANSFER_TENANT_ID = "tenant_id";



    /**
     * 设备处置 assets_handle
     */
    public static final String ASSETS_HANDLE_TABLE = "shuiwu_assets_handle";
    public static final String ASSETS_HANDLE_ID = "id";
    public static final String ASSETS_HANDLE_HANDLE_NO = "handle_no";
    public static final String ASSETS_HANDLE_HANDLE_TYPE = "handle_type";
    public static final String ASSETS_HANDLE_REMARK = "remark";
    public static final String ASSETS_HANDLE_DEVICE_IDS = "device_ids";
    public static final String ASSETS_HANDLE_APPLICANT_ID = "applicant_id";
    public static final String ASSETS_HANDLE_REVIEWER_ID = "reviewer_id";
    public static final String ASSETS_HANDLE_STATUS = "status";
    public static final String ASSETS_HANDLE_CREATE_TIME = "create_time";
    public static final String ASSETS_HANDLE_UPDATE_TIME = "update_time";
    public static final String ASSETS_HANDLE_TENANT_ID = "tenant_id";



    /**
     * 设备盘点 assets_inventory
     */
    public static final String ASSETS_INVENTORY_TABLE = "shuiwu_assets_inventory";
    public static final String ASSETS_INVENTORY_ID = "id";
    public static final String ASSETS_INVENTORY_INVENTORY_NO = "inventory_no";
    public static final String ASSETS_INVENTORY_INVENTORY_NAME = "inventory_name";
    public static final String ASSETS_INVENTORY_EXPECT_INVENTORY_TIME = "expect_inventory_time";
    public static final String ASSETS_INVENTORY_INVENTORY_PERSON_IDS = "inventory_person_ids";
    public static final String ASSETS_INVENTORY_REMARK = "remark";
    public static final String ASSETS_INVENTORY_CREATE_TIME = "create_time";
    public static final String ASSETS_INVENTORY_UPDATE_TIME = "update_time";
    public static final String ASSETS_INVENTORY_TENANT_ID = "tenant_id";



    /**
     * 设备台账文件 assets_inventory_c
     */
    public static final String ASSETS_INVENTORY_C_TABLE = "shuiwu_assets_inventory_c";
    public static final String ASSETS_INVENTORY_C_ID = "id";
    public static final String ASSETS_INVENTORY_C_PID = "pid";
    public static final String ASSETS_INVENTORY_C_DEVICE_ID = "device_id";
    public static final String ASSETS_INVENTORY_C_CHANGE = "change";
    public static final String ASSETS_INVENTORY_C_REMARK = "remark";
    public static final String ASSETS_INVENTORY_C_INVENTORY_TIME = "inventory_time";
    public static final String ASSETS_INVENTORY_C_INVENTORY_PERSON_ID = "inventory_person_id";
    public static final String ASSETS_INVENTORY_C_STATUS = "status";
    public static final String ASSETS_INVENTORY_C_CREATE_TIME = "create_time";
    public static final String ASSETS_INVENTORY_C_UPDATE_TIME = "update_time";
    public static final String ASSETS_INVENTORY_C_TENANT_ID = "tenant_id";


    /**
     * 设备折旧 shuiwu_assets_depreciation
     */
    public static final String SHUIWU_ASSETS_DEPRECIATION_TABLE = "shuiwu_assets_depreciation";
    public static final String SHUIWU_ASSETS_DEPRECIATION_ID = "id";
    public static final String SHUIWU_ASSETS_DEPRECIATION_PID = "pid";
    public static final String SHUIWU_ASSETS_DEPRECIATION_DEPRECIATION_MONTH = "depreciation_month";
    public static final String SHUIWU_ASSETS_DEPRECIATION_COUNT_MONTH = "count_month";
    public static final String SHUIWU_ASSETS_DEPRECIATION_DEPRECIATION_CURRENT = "depreciation_current";
    public static final String SHUIWU_ASSETS_DEPRECIATION_DEPRECIATION_TOTAL = "depreciation_total";
    public static final String SHUIWU_ASSETS_DEPRECIATION_CREATE_TIME = "create_time";
    public static final String SHUIWU_ASSETS_DEPRECIATION_PROJECT_ID = "project_id";
    public static final String SHUIWU_ASSETS_DEPRECIATION_TENANT_ID = "tenant_id";

    /**
     * 站点表
     */
    public static final String TB_STATION_TABLE = "tb_station";
    public static final String TB_STATION_NAME = "name";
    public static final String TB_STATION_TYPE = "type";
    public static final String TB_STATION_ADDRESS = "address";
    public static final String TB_STATION_LOCATION = "location";
    public static final String TB_STATION_ADDITIONAL_INFO = "additional_info";
    public static final String TB_STATION_REMARK = "remark";
    public static final String TB_STATION_IMGS = "imgs";
    public static final String TB_STATION_ORDER_NUM = "order_num";
    public static final String TB_STATION_SCADA_URL = "scadaUrl";

    /**
     * 站点设备数据点表
     */
    public static final String TB_STATION_ATTR_TABLE = "tb_station_attr";
    public static final String TB_STATION_ATTR_STATION_ID = "station_id";
    public static final String TB_STATION_ATTR_DEVICE_ID = "device_id";
    public static final String TB_STATION_ATTR_RANGE = "range";
    public static final String TB_STATION_ATTR_ATTR = "attr";
    public static final String TB_STATION_ATTR_TYPE = "type";
    public static final String TB_STATION_ATTR_ORDER_NUM = "order_num";
    public static final String TB_STATION_ATTR_UNIT = "unit";

    /**
     * 系统消息通知
     */
    public static final String TB_SYSTEM_NOTIFY = "tb_system_notify";
    public static final String TB_SYSTEM_NOTIFY_CODE = "code";
    public static final String TB_SYSTEM_NOTIFY_TYPE = "type";
    public static final String TB_SYSTEM_NOTIFY_TOPIC = "topic";
    public static final String TB_SYSTEM_NOTIFY_TOPIC_TYPE = "topic_type";
    public static final String TB_SYSTEM_NOTIFY_CONTENT = "content";
    public static final String TB_SYSTEM_NOTIFY_FROM = "from_user";
    public static final String TB_SYSTEM_NOTIFY_TO = "to_user";
    public static final String TB_SYSTEM_NOTIFY_TIME = "time";
    public static final String TB_SYSTEM_NOTIFY_STATUS = "status";

    /**
     * 水质报表填报
     */
    public static final String TB_WATER_REPORT_TABLE = "tb_water_report";
    public static final String TB_WATER_REPORT_TYPE = "type";
    public static final String TB_WATER_REPORT_STATION_ID = "station_id";
    public static final String TB_WATER_REPORT_COD = "cod";
    public static final String TB_WATER_REPORT_BOD = "bod";
    public static final String TB_WATER_REPORT_NH3 = "nh3";
    public static final String TB_WATER_REPORT_PH = "ph";
    public static final String TB_WATER_REPORT_TP = "tp";
    public static final String TB_WATER_REPORT_TN = "tn";
    public static final String TB_WATER_REPORT_TIME = "time";
    public static final String TB_WATER_REPORT_TENANT_ID = "tenant_id";
    public static final String TB_WATER_REPORT_TIME_TYPE = "time_type";


    /**
     * 加药管理
     */
    public static final String TB_MEDICINE_MANAGE_TABLE = "tb_medicine_manage";
    public static final String TB_MEDICINE_MANAGE_STATION_ID = "station_id";
    public static final String TB_MEDICINE_MANAGE_MEDICINE_TYPE = "medicine_type";
    public static final String TB_MEDICINE_MANAGE_TIME = "time";
    public static final String TB_MEDICINE_MANAGE_NUM = "num";
    public static final String TB_MEDICINE_MANAGE_UNIT_PRICE = "unit_price";
    public static final String TB_MEDICINE_MANAGE_PRICE = "price";

    /**
     * 数据填报-物料填报
     */
    public static final String TB_INPUT_WULIAO_TABLE = "tb_input_wuliao";
    public static final String TB_INPUT_WULIAO_TIME = "time";
    public static final String TB_INPUT_WULIAO_PROCESS_WATER = "process_water";
    public static final String TB_INPUT_WULIAO_XFLJ_NUM = "xflj_num";
    public static final String TB_INPUT_WULIAO_XFLJ_UNIT = "xflj_unit";
    public static final String TB_INPUT_WULIAO_XDJ_NUM = "xdj_num";
    public static final String TB_INPUT_WULIAO_XDJ_UNIT = "xdj_unit";
    public static final String TB_INPUT_WULIAO_TY_NUM = "ty_num";
    public static final String TB_INPUT_WULIAO_TY_UNIT = "ty_unit";
    public static final String TB_INPUT_WULIAO_CLYJ_NUM = "clyj_num";
    public static final String TB_INPUT_WULIAO_CLYJ_UNIT = "clyj_unit";
    public static final String TB_INPUT_WULIAO_WNTSJ_NUM = "wntsj_num";
    public static final String TB_INPUT_WULIAO_WNTSJ_UNIT = "wntsj_unit";
    public static final String TB_INPUT_WULIAO_ENERGY_NUM = "energy_num";
    public static final String TB_INPUT_WULIAO_ENERGY_UNIT = "energy_unit";

    /**
     * 数据填报-车辆信息填报
     */
    public static final String TB_INPUT_CAR_INFO_TABLE = "tb_input_car_info";
    public static final String TB_INPUT_CAR_INFO_CAR_NO = "car_no";
    public static final String TB_INPUT_CAR_INFO_PASSENGER = "passenger";
    public static final String TB_INPUT_CAR_INFO_EVENT = "event";
    public static final String TB_INPUT_CAR_INFO_IN_TIME = "in_time";
    public static final String TB_INPUT_CAR_INFO_OUT_TIME = "out_time";
    public static final String TB_INPUT_CAR_INFO_CREATE_USER = "create_user";

    /**
     * 数据填报-生产计划填报
     */
    public static final String TB_INPUT_PRODUCT_PLAN_TABLE = "tb_input_product_plan";
    public static final String TB_INPUT_PRODUCT_PLAN_TIME = "time";
    public static final String TB_INPUT_PRODUCT_PLAN_CAPACITY = "capacity";
    public static final String TB_INPUT_PRODUCT_PLAN_MONTH_PLAN = "month_plan";
    public static final String TB_INPUT_PRODUCT_PLAN_MONTH_NUM = "month_num";
    public static final String TB_INPUT_PRODUCT_PLAN_MAX_DAY = "max_day";
    public static final String TB_INPUT_PRODUCT_PLAN_AVG_DAY = "avg_day";
    public static final String TB_INPUT_PRODUCT_PLAN_WORK_DAYS = "work_days";
    public static final String TB_INPUT_PRODUCT_PLAN_LOAD_RATE = "load_rate";
    public static final String TB_INPUT_PRODUCT_PLAN_COMPLETE_RATE = "complete_rate";



    /**
     *  指标配置（即实时数据和历史数据的查询时间间隔配置）
     */
    public static final String TB_QUERY_TEMPLATE_TABLE = "tb_query_template";
    public static final String TB_QUERY_TEMPLATE_TYPE = "type";
    public static final String TB_QUERY_TEMPLATE_DATA = "data";

    /**
     * 展示配置
     */
    public static final String TB_SHOW_TEMPLATE_TABLE = "tb_show_template";
    public static final String TB_SHOW_TEMPLATE_TYPE = "type";
    public static final String TB_SHOW_TEMPLATE_NAME = "name";
    public static final String TB_SHOW_TEMPLATE_IMG = "img";

    /**
     * 指令执行日志
     */
    public static final String TB_COMMAND_LOG_TABLE_NAME = "tb_command_log";
    public static final String TB_COMMAND_LOG_OPTION_USER_ID = "option_user_id";
    public static final String TB_COMMAND_LOG_TIME = "time";
    public static final String TB_COMMAND_LOG_STATION_ID = "station_id";
    public static final String TB_COMMAND_LOG_DEVICE_ID = "device_id";
    public static final String TB_COMMAND_LOG_ATTR = "attr";
    public static final String TB_COMMAND_LOG_VALUE = "value";
    public static final String TB_COMMAND_LOG_RESULT = "result";
    public static final String TB_COMMAND_LOG_PARAM = "param";

    /**
     * 设备指令
     */
    public static final String DEVICE_COMMAND_TABLE_NAME = "tb_command_log";
    public static final String DEVICE_COMMAND_DEVICE_KEY = "device_key";
    public static final String DEVICE_COMMAND_COMMAND_TYPE = "command_type";
    public static final String DEVICE_COMMAND_COMMAND = "command";
    public static final String DEVICE_COMMAND_OPTION_TIME = "option_time";
    public static final String DEVICE_COMMAND_OPTION_USER = "option_user";
    public static final String DEVICE_COMMAND_OPTION_IP = "option_ip";
    public static final String DEVICE_COMMAND_OPTION_RESULT = "option_result";
    public static final String DEVICE_COMMAND_EXECUTE_TIME = "execute_time";
    public static final String DEVICE_COMMAND_EXECUTE_RESULT = "execute_result";
    public static final String DEVICE_COMMAND_EXECUTE_RESPONSE = "execute_response";
    public static final String DEVICE_COMMAND_AEP_COMMAND_ID = "aep_command_id";


    /**
     * GIS一张图-人员配置
     */
    public static final String TB_GIS_PEOPLE_SETTING_TABLE = "tb_gis_people_setting";
    public static final String TB_GIS_PEOPLE_SETTING_NAME = "name";
    public static final String TB_GIS_PEOPLE_SETTING_ROLES = "roles";

    /**
     * GIS管网附加属性
     */
    public static final String TB_GIS_PIPE_ADDITIONAL_INFO_TABLE = "tb_gis_pipe_additional_info";
    public static final String TB_GIS_PIPE_ADDITIONAL_INFO_OBJECTID = "objectid";
    public static final String TB_GIS_PIPE_ADDITIONAL_INFO_LAYERID = "layerid";
    public static final String TB_GIS_PIPE_ADDITIONAL_INFO_IMG = "img";
    public static final String TB_GIS_PIPE_ADDITIONAL_INFO_AUDIO = "audio";
    public static final String TB_GIS_PIPE_ADDITIONAL_INFO_VIDIO = "vidio";
    public static final String TB_GIS_PIPE_ADDITIONAL_INFO_FILES = "files";


    /**
     * 管网gis异常上报
     */
    public static final String TB_GIS_EXCEPTION_UPLOAD_TABLE = "tb_gis_exception_upload";
    public static final String TB_GIS_EXCEPTION_UPLOAD_LAYER = "layer";
    public static final String TB_GIS_EXCEPTION_UPLOAD_FID = "fid";
    public static final String TB_GIS_EXCEPTION_UPLOAD_DEVICE_TYPE = "device_type";
    public static final String TB_GIS_EXCEPTION_UPLOAD_UPLOAD_USER = "upload_user";
    public static final String TB_GIS_EXCEPTION_UPLOAD_UPLOAD_TIME = "upload_time";
    public static final String TB_GIS_EXCEPTION_UPLOAD_UPLOAD_CONTENT = "upload_content";
    public static final String TB_GIS_EXCEPTION_UPLOAD_STATUS = "status";
    public static final String TB_GIS_EXCEPTION_UPLOAD_REMARK = "remark";
    public static final String TB_GIS_EXCEPTION_UPLOAD_APPROVAL_USER = "approval_user";
    public static final String TB_GIS_EXCEPTION_UPLOAD_APPROVAL_TIME = "approval_time";

    /**
     * 管网gis标签
     */
    public static final String TB_GIS_LABEL_TABLE = "tb_gis_label";
    public static final String TB_GIS_LABEL_NAME = "name";
    public static final String TB_GIS_LABEL_CREATEUSER = "createuser";
    public static final String TB_GIS_LABEL_CREATETIME = "createtime";
    public static final String TB_GIS_LABEL_UPDATETIME = "updatetime";
    public static final String TB_GIS_LABEL_DESCRIPTION = "description";
    public static final String TB_GIS_LABEL_AVAILABLE = "available";
    public static final String TB_GIS_LABEL_GEOMTYPE = "geomtype";
    public static final String TB_GIS_LABEL_GEOM = "geom";
    public static final String TB_GIS_LABEL_STYLE = "style";
    public static final String TB_GIS_LABEL_POINTCOLOR = "pointcolor";
    public static final String TB_GIS_LABEL_POINTSIZE = "pointsize";
    public static final String TB_GIS_LABEL_LINECOLOR = "linecolor";
    public static final String TB_GIS_LABEL_LINEWIDTH = "linewidth";
    public static final String TB_GIS_LABEL_FILLCOLOR = "fillcolor";

    /**
     * 管网gis方案
     */
    public static final String TB_GIS_PLAN_TABLE = "tb_gis_plan";
    public static final String TB_GIS_PLAN_NAME = "name";
    public static final String TB_GIS_PLAN_TYPE = "type";
    public static final String TB_GIS_PLAN_USER_ID = "user_id";
    public static final String TB_GIS_PLAN_REMARK = "remark";
    public static final String TB_GIS_PLAN_DETAIL = "detail";


    /**
     * gis操作日志
     */
    public static final String GIS_OPTION_LOG_TABLE = "tb_gis_option_log";
    public static final String GIS_OPTION_LOG_TYPE = "type";
    public static final String GIS_OPTION_LOG_CONTENT = "content";
    public static final String GIS_OPTION_LOG_OPTION_USER = "option_user";
    public static final String GIS_OPTION_LOG_OPTION_USER_NAME = "option_user_name";
    public static final String GIS_OPTION_LOG_OPTION_TYPE = "option_type";
    public static final String GIS_OPTION_LOG_OPTION_NAME = "option_name";
    public static final String GIS_OPTION_LOG_OPTION_TIME = "option_time";

    /**
     * 水质化验-基础配置
     */
    public static final String ASSAY_BASE_SETTING_TABLE = "tb_assay_base_setting";
    public static final String ASSAY_BASE_SETTING_NAME = "name";
    public static final String ASSAY_BASE_SETTING_TYPE = "type";
    public static final String ASSAY_BASE_SETTING_ORDER_NUMBER = "order_number";
    public static final String ASSAY_BASE_SETTING_REMARK = "remark";
    public static final String ASSAY_BASE_SETTING_CREATE_USER = "create_user";

    /**
     * 水质化验-化验项内容
     */
    public static final String ASSAY_ITEM_TABLE = "tb_assay_item";
    public static final String ASSAY_ITEM_TITLE = "title";
    public static final String ASSAY_ITEM_SETTING_ID = "setting_id";
    public static final String ASSAY_ITEM_ORDER_NUMBER = "order_number";
    public static final String ASSAY_ITEM_SERIAL_CODE = "serial_code";
    public static final String ASSAY_ITEM_TARGET = "target";
    public static final String ASSAY_ITEM_UNIT = "unit";
    public static final String ASSAY_ITEM_REMARK = "remark";
    public static final String ASSAY_ITEM_CREATE_USER = "create_user";

    /**
     * 水质-化验报告-报告化验项配置
     */
    public static final String ASSAY_REPORT_TYPE_TABLE = "tb_assay_report_type";
    public static final String ASSAY_REPORT_TYPE_NAME = "name";
    public static final String ASSAY_REPORT_TYPE_FILE = "file";

    /**
     * 水质-化验报告-报告-化验内容
     */
    public static final String ASSAY_REPORT_ITEM_TABLE = "tb_assay_report_item";
    public static final String ASSAY_REPORT_ITEM_REPORT_ID = "report_id";
    public static final String ASSAY_REPORT_ITEM_ITEM_ID = "item_id";

    /**
     * 水质-化验报告-报告-采样地点
     */
    public static final String ASSAY_REPORT_ADDRESS_TABLE = "tb_assay_report_address";
    public static final String ASSAY_REPORT_ADDRESS_REPORT_ID = "report_id";
    public static final String ASSAY_REPORT_ADDRESS_ADDRESS_ID = "address_id";

    /**
     * 水质-化验报告-数据录入
     */
    public static final String ASSAY_REPORT_DATA_TABLE = "tb_assay_report_data";
    public static final String ASSAY_REPORT_DATA_TITLE = "title";
    public static final String ASSAY_REPORT_DATA_REPORT_TYPE = "report_type";
    public static final String ASSAY_REPORT_DATA_WATER_SAMPLE_TYPE = "water_sample_type";
    public static final String ASSAY_REPORT_DATA_WATER_SAMPLE_NAME = "water_sample_name";
    public static final String ASSAY_REPORT_DATA_WATER_SAMPLE_STATE = "water_sample_state";
    public static final String ASSAY_REPORT_DATA_COLLECT_TIME = "collect_time";
    public static final String ASSAY_REPORT_DATA_COLLECT_PERSON = "collect_person";
    public static final String ASSAY_REPORT_DATA_COLLECT_ENVIRONMENT = "collect_environment";
    public static final String ASSAY_REPORT_DATA_WEATHER = "weather";
    public static final String ASSAY_REPORT_DATA_TEMPERATURE = "temperature";
    public static final String ASSAY_REPORT_DATA_HUMIDITY = "humidity";
    public static final String ASSAY_REPORT_DATA_WATER_SAMPLE_CODE = "water_sample_code";
    public static final String ASSAY_REPORT_DATA_SEND_CHECK_TIME = "send_check_time";
    public static final String ASSAY_REPORT_DATA_CHECK_TIME_START = "check_time_start";
    public static final String ASSAY_REPORT_DATA_CHECK_TIME_END = "check_time_end";
    public static final String ASSAY_REPORT_DATA_REPORT_TIME = "report_time";
    public static final String ASSAY_REPORT_DATA_FILE = "file";
    public static final String ASSAY_REPORT_DATA_CODE = "code";
    public static final String ASSAY_REPORT_DATA_CHECK_BASIS = "check_basis";
    public static final String ASSAY_REPORT_DATA_IS_QUALIFIED = "is_qualified";
    public static final String ASSAY_REPORT_DATA_IS_COMPLETE = "is_complete";

    /**
     * 水质-化验报告-数据录入-化验指标
     */
    public static final String ASSAY_REPORT_DATA_ITEM_TABLE = "tb_assay_report_data_item";
    public static final String ASSAY_REPORT_DATA_ITEM_PID = "pid";
    public static final String ASSAY_REPORT_DATA_ITEM_TITLE = "title";
    public static final String ASSAY_REPORT_DATA_ITEM_TARGET = "target";
    public static final String ASSAY_REPORT_DATA_ITEM_UNIT = "unit";
    public static final String ASSAY_REPORT_DATA_ITEM_CHECK_PERSON = "check_person";

    /**
     * 水质-化验报告-数据录入-关联地址
     */
    public static final String ASSAY_REPORT_DATA_ADDRESS_TABLE = "tb_assay_report_data_address";
    public static final String ASSAY_REPORT_DATA_ADDRESS_PID = "pid";
    public static final String ASSAY_REPORT_DATA_ADDRESS_ADDRESS = "address";
    public static final String ASSAY_REPORT_DATA_ADDRESS_VALUE = "value";
    public static final String ASSAY_REPORT_DATA_ADDRESS_STATUS = "status";


    /**
     * 化验记录
     */
    public static final String TB_ASSAY_RECORD_TABLE = "tb_assay_record";
    public static final  String TB_ASSAY_RECORD_STATION_ID = "station_id";
    public static final  String TB_ASSAY_RECORD_DISSOLVED_OXYGEN = "dissolved_oxygen";
    public static final  String TB_ASSAY_RECORD_CONDUCTIVITY = "conductivity";
    public static final  String TB_ASSAY_RECORD_TEMPERATURE = "temperature";
    public static final  String TB_ASSAY_RECORD_CHLOROPHYL = "chlorophyl";
    public static final  String TB_ASSAY_RECORD_AMMONIA_NITROGEN = "ammonia_nitrogen";
    public static final  String TB_ASSAY_RECORD_NITRATE = "nitrate";
    public static final  String TB_ASSAY_RECORD_DISSOLVED_ORGANIC_CARBON = "dissolved_organic_carbon";
    public static final  String TB_ASSAY_RECORD_ORGANIC_CARBON = "organic_carbon";
    public static final  String TB_ASSAY_RECORD_TURBIDITY = "turbidity";
    public static final  String TB_ASSAY_RECORD_PH = "ph";
    public static final  String TB_ASSAY_RECORD_CHROMATICITY = "chromaticity";
    public static final  String TB_ASSAY_RECORD_UV254 = "uv254";
    public static final  String TB_ASSAY_RECORD_STATUS = "status";
    public static final  String TB_ASSAY_RECORD_STATION_NAME = "station_name";
    public static final  String TB_ASSAY_RECORD_FILE = "file";

    /**
     * 门禁管理
     */
    public static final String TB_ACCESS_CONTROL_TABLE = "tb_access_control";
    public static final String TB_ACCESS_CONTROL_NAME = "name";
    public static final String TB_ACCESS_CONTROL_DEVICE_ID = "device_id";
    public static final String TB_ACCESS_CONTROL_DOOR_NAME = "door_name";
    public static final String TB_ACCESS_CONTROL_INSTALL_ADDRESS = "install_address";
    public static final String TB_ACCESS_CONTROL_INSTALLER_NAME = "installer_name";
    public static final String TB_ACCESS_CONTROL_INSTALL_TIME = "install_time";
    public static final String TB_ACCESS_CONTROL_REMARK = "remark";
    public static final String TB_ACCESS_CONTROL_PROJECT_ID = "project_id";
    public static final String TB_ACCESS_CONTROL_TENANT_ID = "tenant_id";



    /**
     * 企业应用
     */
    public static final String TENANT_APPLICATION_TABLE = "tenant_application";
    public static final String TENANT_APPLICATION_NAME = "name";
    public static final String TENANT_APPLICATION_TYPE = "type";
    public static final String TENANT_APPLICATION_RESOURCE_TYPE = "resource_type";
    public static final String TENANT_APPLICATION_APPLICATION = "application_url";
    public static final String TENANT_APPLICATION_ORDER_NUM = "order_num";
    public static final String TENANT_APPLICATION_REMARK = "remark";
    public static final String TENANT_APPLICATION_IMG = "img";
    public static final String TENANT_APPLICATION_ICON = "icon";
    public static final String TENANT_APPLICATION_CREATE_TIME = "create_time";
    public static final String TENANT_APPLICATION_TENANT_ID = "tenant_id";

    /**
     * 企业应用-菜单
     */
    public static final String APPLICATION_MENU_RELATION_TABLE = "application_menu_relation";
    public static final String APPLICATION_MENU_RELATION_APPLICATION_ID = "tenant_application_id";
    public static final String APPLICATION_MENU_RELATION_MENU_ID = "menu_pool_id";

    /**
     * 企业应用-角色-菜单
     */
    public static final String CUSTOMER_APPLICATION_MENU_ROLE_TABLE = "customer_application_menu_role";
    public static final String CUSTOMER_APPLICATION_MENU_ROLE_MENU_ID = "menu_id";
    public static final String CUSTOMER_APPLICATION_MENU_ROLE_ROLE_ID = "role_id";
    public static final String CUSTOMER_APPLICATION_MENU_ROLE_APPLICATION_ID = "tenant_application_id";

    /**
     * 企业角色-应用
     */
    public static final String CUSTOMER_ROLE_APPLICATION_TABLE = "customer_role_application";
    public static final String CUSTOMER_ROLE_APPLICATION_ROLE_ID = "role_id";
    public static final String CUSTOMER_ROLE_APPLICATION_APPLICATION_ID = "tenant_application_id";


    /**
     * dataSource
     */
    public static final String DATASOURCE_TABLE = "data_source";
    public static final String DATASOURCE_NAME = "name";
    public static final String DATASOURCE_FORMAT = "format";
    public static final String DATASOURCE_ENABLE = "enable";
    public static final String DATASOURCE_ORDER = "number";
    public static final String DATASOURCE_UPDATE_TIME = "update_time";
    public static final String DATASOURCE_RELATION = "data_source_relation";
    public static final String DATASOURCE_STATISTICS_PREPARATION = "preparation";
    public static final String DATASOURCE_DATATYPE = "data_type";
    public static final String RESTAPI_TABLE = "rest_api";

    /* -- deviceDataSource -- */
    public static final String DATASOURCE_DEVICE_ID = "device_id";
    public static final String DATASOURCE_PROPERTY = "property";
    public static final String DATASOURCE_DEVICE_STATISTICS="statistics_type";

    /* -- APIDataSource -- */
    public static final String DATASOURCE_API_URL = "url";
    public static final String DATASOURCE_API_METHOD = "method";
    public static final String DATASOURCE_API_PARAMS = "params";
    public static final String DATASOURCE_API_HEADERS = "headers";
    public static final String DATASOURCE_PARSING_ATTRIBUTE = "parsing_attribute";
    public static final String DATASOURCE_PARSING_PATH = "parsing_path";

    /* -- statisticsDataSource -- */
    public static final String DATASOURCE_STATISTICS_FORMULA = "formula";
    public static final String DATASOURCE_STATISTICS_FREQUENCY = "frequency";
    public static final String DATASOURCE_STATISTICS_TEMPLATE = "template";

    /**
     * originData
     */
    public static final String ORIGIN_DATA_TABLE = "origin_data";
    public static final String DATASOURCE_ID = "data_source_id";
    public static final String DATASOURCE_VALUE = "value";


    public static final String EXTRA_USER_TABLE="extra_user";

    /**
     * 站点养护计划
     */
    public static final String TB_STATION_CIRCUIT_PLAN_TABLE = "tb_station_circuit_plan";
    public static final String TB_STATION_CIRCUIT_PLAN_NAME = "name";
    public static final String TB_STATION_CIRCUIT_PLAN_TYPE = "type";
    public static final String TB_STATION_CIRCUIT_PLAN_DAYS = "days";
    public static final String TB_STATION_CIRCUIT_PLAN_START_TIME = "start_time";
    public static final String TB_STATION_CIRCUIT_PLAN_END_TIME = "end_time";
    public static final String TB_STATION_CIRCUIT_PLAN_AUDIT_DEP = "audit_dep";
    public static final String TB_STATION_CIRCUIT_PLAN_AUDIT_USER_ID = "audit_user_id";
    public static final String TB_STATION_CIRCUIT_PLAN_OPTION_DEP = "option_dep";
    public static final String TB_STATION_CIRCUIT_PLAN_OPTION_USER_ID = "option_user_id";
    public static final String TB_STATION_CIRCUIT_PLAN_STATION_IDS = "station_ids";
    public static final String TB_STATION_CIRCUIT_PLAN_REMARK = "remark";
    public static final String TB_STATION_CIRCUIT_PLAN_STATION_TYPE = "station_type";
    public static final String TB_STATION_CIRCUIT_PLAN_SCHEME_ID = "scheme_id";

    /**
     * 站点养护任务
     */
    public static final String TB_STATION_CIRCUIT_TASK_TABLE = "tb_station_circuit_task";
    public static final String TB_STATION_CIRCUIT_TASK_CODE = "code";
    public static final String TB_STATION_CIRCUIT_TASK_NAME = "name";
    public static final String TB_STATION_CIRCUIT_TASK_TYPE = "type";
    public static final String TB_STATION_CIRCUIT_TASK_STATION_ID = "station_id";
    public static final String TB_STATION_CIRCUIT_TASK_OPTION_DEP = "option_dep";
    public static final String TB_STATION_CIRCUIT_TASK_OPTION_USER_ID = "option_user_id";
    public static final String TB_STATION_CIRCUIT_TASK_START_TIME = "start_time";
    public static final String TB_STATION_CIRCUIT_TASK_END_TIME = "end_time";
    public static final String TB_STATION_CIRCUIT_TASK_REAL_START_TIME = "real_start_time";
    public static final String TB_STATION_CIRCUIT_TASK_REAL_END_TIME = "real_end_time";
    public static final String TB_STATION_CIRCUIT_TASK_STATUS = "status";
    public static final String TB_STATION_CIRCUIT_TASK_AUDIT_DEP = "audit_dep";
    public static final String TB_STATION_CIRCUIT_TASK_AUDIT_USER_ID = "audit_user_id";
    public static final String TB_STATION_CIRCUIT_TASK_AUDIT_RESULT = "audit_result";
    public static final String TB_STATION_CIRCUIT_TASK_AUDIT_REMARK = "audit_remark";
    public static final String TB_STATION_CIRCUIT_TASK_AUDIT_TIME = "audit_time";

    /**
     * 站点养护方案
     */
    public static final String TB_STATION_CIRCUIT_SCHEME_TABLE = "tb_station_circuit_scheme";
    public static final String TB_STATION_CIRCUIT_SCHEME_NAME = "name";
    public static final String TB_STATION_CIRCUIT_SCHEME_CONTENT = "content";
    public static final String TB_STATION_CIRCUIT_SCHEME_CREATE_USER = "create_user";
    public static final String TB_STATION_CIRCUIT_SCHEME_STATION_TYPE = "station_type";


    /**
     * 水泵
     */
    public static final String WATER_PUMP_TABLE = "tb_water_pump";
    public static final String WATER_PUMP_NAME = "name";
    public static final String WATER_PUMP_LOCATION = "location";

    /**
     * 水泵关联设备
     */
    public static final String WATER_PUMP_RELATION_TABLE = "tb_water_pump_relation";
    public static final String WATER_PUMP_RELATION_WATER_PUMP_ID = "water_pump_id";
    public static final String WATER_PUMP_RELATION_DEVICE_ID = "device_id";
    public static final String WATER_PUMP_RELATION_ATTR = "attr";


    /**
     * 审计记录
     */
    public static final String AUDIT_RECORD_TABLE = "audit_record";
    public static final String AUDIT_RECORD_TIME = "time";
    public static final String AUDIT_RECORD_USERNAME = "username";
    public static final String AUDIT_RECORD_TARGET = "target";
    public static final String AUDIT_RECORD_COUNT = "count";
    public static final String AUDIT_RECORD_RESULT = "result";
    public static final String AUDIT_RECORD_EVENT_ANALYSIS = "event_analysis";
    public static final String AUDIT_RECORD_CREATED = "created";


    /**
     * 公告
     */
    public static final String BULLETIN_TABLE="bulletin";
    public static final String BULLETIN_TITLE="title";
    public static final String BULLETIN_BODY="body";


    /**
     * 验证
     */
    public static final String CAPTCHA_TABLE="captcha";
    public static final String CAPTCHA_CAPTCHA="captcha";
    public static final String CAPTCHA_INVALID="invalid";


    /**
     * 控制日志
     */
    public static final String OPERATE_LOG_TABLE="operate_log";
    public static final String OPERATE_LOG_RESULT_MSG="result_msg";

    /**
     * app version
     */
    public static final String APP_VERSION_TABLE = "app_version";
    public static final String APP_VERSION_CODE = "version_code";
    public static final String APP_VERSION_NAME = "version_name";
    public static final String APP_VERSION_URL = "url";
    public static final String APP_VERSION_REMARK = "remark";
    public static final String APP_VERSION_APP_KEY = "app_key";
    public static final String APP_VERSION_TENANT_KEY = "tenant_key";
    public static final String APP_VERSION_APP_NAME = "app_name";
    public static final String APP_VERSION_VERSION_CONTENT = "version_content";

    /**
     * app底部菜单
     */
    public static final String APP_BOTTOM_MENU_TABLE_NAME = "app_bottom_menu";
    public static final String APP_BOTTOM_MENU_NAME = "name";
    public static final String APP_BOTTOM_MENU_MENU_KEY = "menu_key";

    /**
     * app USER CID
     */
    public static final String APP_USER_CID_TABLE_NAME = "tb_app_user_cid";
    public static final String APP_USER_CID_USER_ID = "user_id";
    public static final String APP_USER_CID_CID = "cid";

    /**
     * 企业登录
     */
    public static final String TB_TENANT_LOGIN_TABLE_NAME = "tb_tenant_login";
    public static final String TB_TENANT_LOGIN_NAME = "name";
    public static final String TB_TENANT_LOGIN_ACCOUNT = "account";
    public static final String TB_TENANT_LOGIN_PASSWORD = "password";
    public static final String TB_TENANT_LOGIN_URL = "url";



    /**
     * Video
     */
    public static final String VIDEO_TABLE_NAME = "video";
    public static final String VIDEO_SERIAL_NUMBER = "serial_number";
    public static final String VIDEO_CHANNEL_ID = "channel_id";
    public static final String VIDEO_CAPTCHA = "captcha";
    public static final String URL = "url";
    public static final String VIDEO_APP_KEY = "app_key";
    public static final String VIDEO_APP_SECRET = "app_secret";
    public static final String VIDEO_TYPE = "video_type";
    public static final String VIDEO_LONGITUDE = "longitude";
    public static final String VIDEO_LATITUDE = "latitude";
    public static final String VIDEO_LOCATION = "location";


    /**
     * msg_log
     */
    public static final String MSG_LOG_TABLE_NAME = "msg_log";
    public static final String MSG_LOG_MSG_BODY = "body";
    public static final String MSG_LOG_LOG_TYPE = "log_type";

    /**
     * devcie_log
     */
    public static final String DEVICE_LOG_TABLE_NAME = "device_log";
    public static final String DEVICE_LOG_DEVICE_NAME = "device_name";
    public static final String DEVICE_LOG_PROJECT_NAME = "project_name";

    /**
     * 组态看板
     */
    public static final String DASH_CHART_TABLE_NAME = "dash_chart";
    public static final String DASH_CHART_PROTECT = "protect";
    public static final String DASH_CHART_PROTECT_PWD = "protect_pwd";
    public static final String DASH_CHART_DASHBOARD_ID = "dashboard_id";
    public static final String DASH_CHART_DASHBOARD_JSON_ID = "dashboard_json_id";
    public static final String DASH_CHART_DASH_JSON="dash_json";


    /**
     * app type
     */
    public static final String APP_TYPE_TABLE = "app_type";
    public static final String APP_TYPE_CREATE_TIME = "create_time";
    public static final String APP_TYPE_NAME = "app_name";
    public static final String APP_TYPE_ADDITIONAL_INFO = "additional_info";

    /**
     * app role
     */
    public static final String APP_ROLE_TABLE = "app_role";
    public static final String APP_ROLE_CREATE_TIME = "create_time";
    public static final String APP_ROLE_NAME = "name";
    public static final String APP_ROLE_ADDITIONAL_INFO = "additional_info";
    public static final String APP_ROLE_TYPE_ID = "app_type_id";

    /**
     * app type relation
     */
    public static final String APP_TYPE_RELATION_TABLE = "app_type_relation";
    public static final String APP_TYPE_RELATION_CREATE_TIME = "create_time";
    public static final String APP_TYPE_RELATION_MENU_POOL_ID = "menu_pool_id";
    public static final String APP_TYPE_RELATION_APP_TYPE_ID = "appTypeId";

    /**
     * device template
     */
    public static final String DEVICE_TEMPLATE_TABLE = "device_template";
    public static final String DEVICE_TEMPLATE_CREATE_TIME = "create_time";
    public static final String DEVICE_TEMPLATE_NAME = "name";
    public static final String DEVICE_TEMPLATE_REMARK = "remark";
    public static final String DEVICE_TEMPLATE_TEMPLATE = "template";
    public static final String DEVICE_TEMPLATE_ADDITIONAL_INFO = "additional_info";
    public static final String DEVICE_TEMPLATE_TENANT_ID = "tenant_id";
    public static final String DEVICE_TEMPLATE_TYPE = "type";
    public static final String DEVICE_TEMPLATE_PROTOCOL = "protocol";


    /**
     * device template
     */
    public static final String PROTOCOL_TABLE = "protocol_template";
    public static final String PROTOCOL_TEMPLATE_ID = "template_id";
    public static final String PROTOCOL_NUMBER = "number";
    public static final String PROTOCOL_PROPERTY_CATEGORY = "property_category";
    public static final String PROTOCOL_NAME = "name";
    public static final String PROTOCOL_STAT_TYPE = "stat_type";
    public static final String PROTOCOL_DATA_TYPE = "data_type";
    public static final String PROTOCOL_PROPERTY_TYPE = "property_type";
    public static final String PROTOCOL_UNIT = "unit";
    public static final String PROTOCOL_REGISTER_TYPE = "register_type";
    public static final String PROTOCOL_FUNCTION_CODE = "function_code";
    public static final String PROTOCOL_REGISTER_ADDRESS = "register_address";
    public static final String PROTOCOL_BYTE_COUNT = "byte_count";
    public static final String PROTOCOL_BIT_POSITION = "bit_position";
    public static final String PROTOCOL_REGISTER_SIGN_FLAG = "register_sign_flag";
    public static final String PROTOCOL_SAMPLE_DEVIATION = "sample_deviation";
    public static final String PROTOCOL_ORDER = "order";
    public static final String PROTOCOL_BYTE_ORDER = "byte_order";
    public static final String PROTOCOL_DATA_OFFSET = "data_offset";
    public static final String PROTOCOL_SAMPLING_MAX = "sampling_max";
    public static final String PROTOCOL_SAMPLING_MIN = "sampling_min";
    public static final String PROTOCOL_SAMPLE_COEF = "sample_coef";
    public static final String PROTOCOL_UNIT_COEF = "unit_coef";
    public static final String PROTOCOL_RANGE = "range";
    public static final String PROTOCOL_FORMULA_PROPERTY = "formula_property";


    /**
     * 组态assets
     * zutai_assets
     */
    public static final String ZUTAI_ASSETS_TABLE = "zutai_assets";
    public static final String ZUTAI_ASSETS_ID = "id";
    public static final String ZUTAI_ASSETS_URI = "uri";
    public static final String ZUTAI_ASSETS_SIZE = "size";
    public static final String ZUTAI_ASSETS_TYPE = "type";
    public static final String ZUTAI_ASSETS_CREATE_TIME = "create_time";
    public static final String ZUTAI_ASSETS_PROJECT_ID = "project_id";
    public static final String ZUTAI_ASSETS_TENANT_ID = "tenant_id";

    /**
     * 组态dashboard
     * zutai_组态dashboard
     */
    public static final String ZUTAI_DASHBOARD_TABLE = "zutai_dashboard";
    public static final String ZUTAI_DASHBOARD_ID = "id";
    public static final String ZUTAI_DASHBOARD_DATA = "data";
    public static final String ZUTAI_DASHBOARD_CREATE_TIME = "create_time";
    public static final String ZUTAI_DASHBOARD_PROJECT_ID = "project_id";
    public static final String ZUTAI_DASHBOARD_TENANT_ID = "tenant_id";

    /**
     * dma分区设置
     * dma_partition
     */
    public static final String DMA_PARTITION_TABLE = "dma_partition";
    public static final String DMA_PARTITION_ID = "id";
    public static final String DMA_PARTITION_PID = "pid";
    public static final String DMA_PARTITION_NAME = "name";
    public static final String DMA_PARTITION_CODE = "code";
    public static final String DMA_PARTITION_STATUS = "status";
    public static final String DMA_PARTITION_HOUSEHOLDS_NUMBER = "households_number";
    public static final String DMA_PARTITION_REAL_HOUSEHOLDS_NUMBER = "real_households_number";
    public static final String DMA_PARTITION_PIPE_LENGTH = "pipe_length";
    public static final String DMA_PARTITION_PIPE_DEPTH = "pipe_depth";
    public static final String DMA_PARTITION_PIPE_HOUSEHOLDS = "pipe_households";
    public static final String DMA_PARTITION_PIPE_BRANCH = "pipe_branch";
    public static final String DMA_PARTITION_DIFFERENCE_SALE_TARGET = "difference_sale_target";
    public static final String DMA_PARTITION_LEAKAGE_CONTROL_TARGET = "leakage_control_target";
    public static final String DMA_PARTITION_PARTITION_AREA = "partition_area";
    public static final String DMA_PARTITION_PARTITION_SCOPE = "partition_scope";
    public static final String DMA_PARTITION_ONLINE_DATE = "online_date";
    public static final String DMA_PARTITION_HOUSEHOLD_NIGHT_RATED_WATER = "household_night_rated_water";
    public static final String DMA_PARTITION_SUPPLY_MODE = "supply_mode";
    public static final String DMA_PARTITION_PIPE_MATERIAL = "pipe_material";
    public static final String DMA_PARTITION_PARTITION_TYPE = "partition_type";
    public static final String DMA_PARTITION_PIPE_CREATE_TIME = "pipe_create_time";
    public static final String DMA_PARTITION_CREATE_TIME = "create_time";
    public static final String DMA_PARTITION_UPDATE_TIME = "update_time";
    public static final String DMA_PARTITION_PROJECT_ID = "project_id";
    public static final String DMA_PARTITION_TENANT_ID = "tenant_id";


    /**
     * dma分区关联总表
     * dma_device_relation
     */
    public static final String DMA_DEVICE_RELATION_TABLE = "dma_device_relation";
    public static final String DMA_DEVICE_RELATION_ID = "id";
    public static final String DMA_DEVICE_RELATION_DEVICE_ID = "device_id";
    public static final String DMA_DEVICE_RELATION_TYPE = "type";
    public static final String DMA_DEVICE_RELATION_PARTITION_ID = "partition_id";
    public static final String DMA_DEVICE_RELATION_CREATE_TIME = "create_time";

    /**
     * dma夜间最小流量
     * dma_night_min_flow
     */
    public static final String DMA_NIGHT_MIN_FLOW_TABLE = "dma_night_min_flow";
    public static final String DMA_NIGHT_MIN_FLOW_ID = "id";
    public static final String DMA_NIGHT_MIN_FLOW_PARTITION_ID = "partition_id";
    public static final String DMA_NIGHT_MIN_FLOW_IN_FLOW = "in_flow";
    public static final String DMA_NIGHT_MIN_FLOW_OUT_FLOW = "out_flow";
    public static final String DMA_NIGHT_MIN_FLOW_BACKGROUND_WATER_LEAKAGE = "background_water_leakage";
    public static final String DMA_NIGHT_MIN_FLOW_NIGHT_MIN_FLOW = "night_min_flow";
    public static final String DMA_NIGHT_MIN_FLOW_NIGHT_LAWFUL_USE_TOTAL = "night_lawful_use_total";
    public static final String DMA_NIGHT_MIN_FLOW_BROKEN_PIPE_LEAKAGE_TOTAL = "broken_pipe_leakage_total";
    public static final String DMA_NIGHT_MIN_FLOW_CREATE_TIME = "create_time";

    /**
     * dma分析
     * dma_analysis
     */
    public static final String DMA_ANALYSIS_TABLE = "dma_analysis";
    public static final String DMA_ANALYSIS_ID = "id";
    public static final String DMA_ANALYSIS_PARTITION_ID = "partition_id";
    public static final String DMA_ANALYSIS_SUPPLY_TOTAL = "supply_total";
    public static final String DMA_ANALYSIS_REAL_TOTAL = "real_total";
    public static final String DMA_ANALYSIS_FREE_USE = "free_use";
    public static final String DMA_ANALYSIS_LOSS_TOTAL = "loss_total";
    public static final String DMA_ANALYSIS_CREATE_TIME = "create_time";

    /**
     * dma压力
     * dma_pressure
     */
    public static final String DMA_PRESSURE_TABLE = "dma_pressure";
    public static final String DMA_PRESSURE_ID = "id";
    public static final String DMA_PRESSURE_PARTITION_ID = "partition_id";
    public static final String DMA_PRESSURE_DEVICE_ID = "device_id";
    public static final String DMA_PRESSURE_PRESSURE = "pressure";
    public static final String DMA_PRESSURE_CREATE_TIME = "create_time";
    public static final String DMA_PRESSURE_TENANT_ID = "tenant_id";

    /**
     * dma预警设置
     * dma_warn_config
     */
    public static final String DMA_WARN_CONFIG_TABLE = "dma_warn_config";
    public static final String DMA_WARN_CONFIG_ID = "id";
    public static final String DMA_WARN_CONFIG_PARTITION_ID = "partition_id";
    public static final String DMA_WARN_CONFIG_START_TIME = "start_time";
    public static final String DMA_WARN_CONFIG_END_TIME = "end_time";
    public static final String DMA_WARN_CONFIG_INFO_VALUE = "info_value";
    public static final String DMA_WARN_CONFIG_WARN_VALUE = "warn_value";
    public static final String DMA_WARN_CONFIG_ERROR_VALUE = "error_value";
    public static final String DMA_WARN_CONFIG_PRE_DAY = "pre_day";
    public static final String DMA_WARN_CONFIG_MIN_FLOW = "min_flow";
    public static final String DMA_WARN_CONFIG_MAX_FLOW = "max_flow";
    public static final String DMA_WARN_CONFIG_WARN_MULTIPLE = "warn_multiple";
    public static final String DMA_WARN_CONFIG_ERROR_MULTIPLE = "error_multiple";
    public static final String DMA_WARN_CONFIG_TYPE = "type";

    /**
     * dma报警列表
     * dma_warn_list
     */
    public static final String DMA_WARN_LIST_TABLE = "dma_warn_list";
    public static final String DMA_WARN_LIST_ID = "id";
    public static final String DMA_WARN_LIST_PARTITION_ID = "partition_id";
    public static final String DMA_WARN_LIST_TYPE = "type";
    public static final String DMA_WARN_LIST_DETAIL = "detail";
    public static final String DMA_WARN_LIST_WARN_VALUE = "warn_value";
    public static final String DMA_WARN_LIST_THRESHOLD_VALUE = "threshold_value";
    public static final String DMA_WARN_LIST_STATUS = "status";
    public static final String DMA_WARN_LIST_OP_TIME = "op_time";
    public static final String DMA_WARN_LIST_OPERATOR = "operator";
    public static final String DMA_WARN_LIST_REMARK = "remark";
    public static final String DMA_WARN_LIST_CREATE_TIME = "create_time";

    /**
     * dma报警详情
     * dma_warn_c
     */
    public static final String DMA_WARN_C_TABLE = "dma_warn_c";
    public static final String DMA_WARN_C_ID = "id";
    public static final String DMA_WARN_C_PID = "pid";
    public static final String DMA_WARN_C_STATUS = "status";
    public static final String DMA_WARN_C_REMARK = "remark";
    public static final String DMA_WARN_C_CREATE_TIME = "create_time";

    /**
     * 锦州DMA小区
     * jinzhou_dma_book
     */
    public static final String JINZHOU_DMA_TABLE = "jinzhou_dma_book";
    public static final String JINZHOU_DMA_BOOK_ID = "book_id";
    public static final String JINZHOU_DMA_NAME = "name";
    public static final String JINZHOU_DMA_MAP_JSON = "map_json";
    /**
     * 锦州大屏配置
     * jinzhou_scada_config
     */
    public static final String JINZHOU_SCADA_CONFIG_TABLE = "jinzhou_scada_config";
    public static final String JINZHOU_SCADA_CONFIG_CODE = "code";
    public static final String JINZHOU_SCADA_CONFIG_JSON_DATA = "json_data";

    /**
     * 大屏配置
     * scada_config
     */
    public static final String SCADA_CONFIG_TABLE = "scada_config";
    public static final String SCADA_CONFIG_CODE = "code";
    public static final String SCADA_CONFIG_JSON_DATA = "json_data";
    public static final String SCADA_CONFIG_TYPE = "type";



    /**
     * 组态dashboard
     * zutai_组态dashboard_list
     */
    public static final String ZUTAI_DASHBOARD_LIST_TABLE = "zutai_dashboard_list";
    public static final String ZUTAI_DASHBOARD_LIST_ID = "id";
    public static final String ZUTAI_DASHBOARD_LIST_DASHBOARD_ID = "dashboard_id";
    public static final String ZUTAI_DASHBOARD_LIST_NAME = "name";
    public static final String ZUTAI_DASHBOARD_LIST_DETAIL = "detail";
    public static final String ZUTAI_DASHBOARD_LIST_PROTECT = "protect";
    public static final String ZUTAI_DASHBOARD_LIST_PROTECT_PWD = "protect_pwd";
    public static final String ZUTAI_DASHBOARD_LIST_CHECKED = "checked";
    public static final String ZUTAI_DASHBOARD_LIST_TYPE = "type";
    public static final String ZUTAI_DASHBOARD_LIST_CREATE_TIME = "create_time";
    public static final String ZUTAI_DASHBOARD_LIST_PROJECT_ID = "project_id";
    public static final String ZUTAI_DASHBOARD_LIST_TENANT_ID = "tenant_id";

    /**
     * Main names of cassandra key-value columns storage.
     */
    public static final String BOOLEAN_VALUE_COLUMN = "bool_v";
    public static final String STRING_VALUE_COLUMN = "str_v";
    public static final String LONG_VALUE_COLUMN = "long_v";
    public static final String DOUBLE_VALUE_COLUMN = "dbl_v";

    protected static final String[] NONE_AGGREGATION_COLUMNS = new String[]{LONG_VALUE_COLUMN, DOUBLE_VALUE_COLUMN, BOOLEAN_VALUE_COLUMN, STRING_VALUE_COLUMN, KEY_COLUMN, TS_COLUMN};

    protected static final String[] COUNT_AGGREGATION_COLUMNS = new String[]{count(LONG_VALUE_COLUMN), count(DOUBLE_VALUE_COLUMN), count(BOOLEAN_VALUE_COLUMN), count(STRING_VALUE_COLUMN)};

    protected static final String[] MIN_AGGREGATION_COLUMNS = ArrayUtils.addAll(COUNT_AGGREGATION_COLUMNS,
            new String[]{min(LONG_VALUE_COLUMN), min(DOUBLE_VALUE_COLUMN), min(BOOLEAN_VALUE_COLUMN), min(STRING_VALUE_COLUMN)});
    protected static final String[] MAX_AGGREGATION_COLUMNS = ArrayUtils.addAll(COUNT_AGGREGATION_COLUMNS,
            new String[]{max(LONG_VALUE_COLUMN), max(DOUBLE_VALUE_COLUMN), max(BOOLEAN_VALUE_COLUMN), max(STRING_VALUE_COLUMN)});
    protected static final String[] SUM_AGGREGATION_COLUMNS = ArrayUtils.addAll(COUNT_AGGREGATION_COLUMNS,
            new String[]{sum(LONG_VALUE_COLUMN), sum(DOUBLE_VALUE_COLUMN)});
    protected static final String[] AVG_AGGREGATION_COLUMNS = SUM_AGGREGATION_COLUMNS;

    public static String min(String s) {
        return "min(" + s + ")";
    }

    public static String max(String s) {
        return "max(" + s + ")";
    }

    public static String sum(String s) {
        return "sum(" + s + ")";
    }

    public static String count(String s) {
        return "count(" + s + ")";
    }

    public static String[] getFetchColumnNames(Aggregation aggregation) {
        switch (aggregation) {
            case NONE:
                return NONE_AGGREGATION_COLUMNS;
            case MIN:
                return MIN_AGGREGATION_COLUMNS;
            case MAX:
                return MAX_AGGREGATION_COLUMNS;
            case SUM:
                return SUM_AGGREGATION_COLUMNS;
            case COUNT:
                return COUNT_AGGREGATION_COLUMNS;
            case AVG:
                return AVG_AGGREGATION_COLUMNS;
            default:
                throw new RuntimeException("Aggregation type: " + aggregation + " is not supported!");
        }
    }

    /**
     * 营收通用字段
     */
    public static final String REVENUE_ORG_ID = "org_id";
    public static final String REVENUE_EXPANDING_CODE = "expanding_code";
    public static final String REVENUE_EXPANDING_TYPE= "expanding_type";
    public static final String REVENUE_EDIT_CODE = "edit_code";

    /**
     * 数据字典主表
     */
    public static final String SYS_CODE_TABLE = "tb_sys_code";
    public static final String SYS_CODE_CODE = "code";
    public static final String SYS_CODE_NAME = "name";
    public static final String SYS_CODE_ORDER_NUMBER = "order_number";

    /**
     * 数据字典子表
     */
    public static final String SYS_CODE_DETAIL_TABLE = "tb_sys_code_detail";
    public static final String SYS_CODE_DETAIL_CODE = "code";
    public static final String SYS_CODE_DETAIL_NAME = "name";
    public static final String SYS_CODE_DETAIL_KEY = "key";
    public static final String SYS_CODE_DETAIL_ORDER_NUMBER = "order_number";

    /**
     * 系统配置表
     */
    public static final String SYSTEM_SETTING_TABLE = "tb_system_setting";
    public static final String SYSTEM_SETTING_NAME = "name";
    public static final String SYSTEM_SETTING_REMARK = "remark";
    public static final String SYSTEM_SETTING_VALUE = "value";
    public static final String SYSTEM_SETTING_JSON = "json";
    public static final String SYSTEM_SETTING_CODE = "code";


    /**
     * 票据模板
     */
    public static final String BILL_TEMPLATE_TABLE = "tb_bill_template";
    public static final String BILL_TEMPLATE_NAME = "name";
    public static final String BILL_TEMPLATE_TITLE = "title";
    public static final String BILL_TEMPLATE_LENGTH = "length";
    public static final String BILL_TEMPLATE_WIDTH = "width";
    public static final String BILL_TEMPLATE_TOP_MARGIN = "top_margin";
    public static final String BILL_TEMPLATE_LEFT_MARGIN = "left_margin";
    public static final String BILL_TEMPLATE_CONTENT_LENGTH = "content_length";
    public static final String BILL_TEMPLATE_CONTENT_WIDTH = "content_width";
    public static final String BILL_TEMPLATE_STATUS = "status";
    public static final String BILL_TEMPLATE_TYPE = "type";
    public static final String BILL_TEMPLATE_CODE = "code";
    public static final String BILL_TEMPLATE_PARAM_JSON = "param_json";

    /**
     * 水表底数（账期结算后）
     */
    public static final String METER_BASE_NUMBER_TABLE = "tb_meter_base_number";
    public static final String METER_BASE_NUMBER_CUST_CODE = "cust_code";
    public static final String METER_BASE_NUMBER_METER_CODE = "meter_code";
    public static final String METER_BASE_NUMBER_SEND_TIME = "send_time";
    public static final String METER_BASE_NUMBER_READ_DATA = "read_data";
    public static final String METER_BASE_NUMBER_LAST_READ_DATA = "last_read_data";
    public static final String METER_BASE_NUMBER_APPEND_WATER = "append_water";
    public static final String METER_BASE_NUMBER_TOTAL_WATER = "total_water";
    public static final String METER_BASE_NUMBER_YM = "ym";
    public static final String METER_BASE_NUMBER_YM_TIME = "ym_time";
    public static final String METER_BASE_NUMBER_CREATE_TIME = "create_time";
    public static final String METER_BASE_NUMBER_METER_COPY_TIME = "meter_copy_time";



    /**
     * 应收水费账期
     */
    public static final String WATER_FEE_YM_TABLE = "tb_water_fee_ym";
    public static final String WATER_FEE_YM_TIME = "time";
    public static final String WATER_FEE_YM_START_TIME = "start_time";
    public static final String WATER_FEE_YM_END_TIME = "end_time";
    public static final String WATER_FEE_YM_STATUS = "status";
    public static final String WATER_FEE_YM_EXECUTOR = "executor";

    /**
     * 抄表册表字段
     */
    public static final String METER_BOOK_TABLE = "tb_meter_book";
    public static final String METER_BOOK_CODE = "code";
    public static final String METER_BOOK_NAME = "name";
    public static final String METER_BOOK_TYPE = "type";
    public static final String METER_BOOK_METER_COPY_EVENT = "meter_copy_event";
    public static final String METER_BOOK_CYCLE = "cycle";
    public static final String METER_BOOK_CYCLE_MONTH_NUMBER = "cycle_month_number";
    public static final String METER_BOOK_DAY_OF_MONTH = "day_of_month";
    public static final String METER_BOOK_METER_COPY_METHOD = "meter_copy_method";
    public static final String METER_BOOK_METER_LAST_MONTH = "last_month";
    public static final String METER_BOOK_METER_STATUS = "status";
    public static final String METER_BOOK_METER_COPY_USER = "meter_copy_user";
    public static final String METER_BOOK_PHONE_METER_COPY_USER = "phone_meter_copy_user";
    public static final String METER_BOOK_ACCOUNTANTS_USER = "accountants_user";

    /**
     * 抄表册流程步骤信息
     */
    public static final String METER_BOOK_PROCESS_TABLE = "tb_meter_book_process";
    public static final String METER_BOOK_PROCESS_ORDER_NUMBER = "order_number";
    public static final String METER_BOOK_PROCESS_NAME = "name";
    public static final String METER_BOOK_PROCESS_EXECUTIVE_DEPT = "executive_dept";
    public static final String METER_BOOK_PROCESS_EXECUTOR = "executor";

    /**
     * 抄表计划
     */
    public static final String METER_COPY_PLAN_TABLE = "tb_meter_copy_plan";
    public static final String METER_COPY_PLAN_ORG_ID = "org_id";
    public static final String METER_COPY_PLAN_BOOK_CODE = "book_code";
    public static final String METER_COPY_PLAN_CODE = "code";
    public static final String METER_COPY_PLAN_EVENT_TYPE = "event_type";
    public static final String METER_COPY_PLAN_YM = "ym";
    public static final String METER_COPY_PLAN_EXECUTOR = "executor";
    public static final String METER_COPY_PLAN_LAST_METER_COPY_DATE = "last_meter_copy_date";
    public static final String METER_COPY_PLAN_PLAN_DATE = "plan_date";
    public static final String METER_COPY_PLAN_PLAN_STATUS = "plan_status";
    public static final String METER_COPY_PLAN_STATUS = "status";
    public static final String METER_COPY_PLAN_CALCULATE_DATE = "calculate_date";
    public static final String METER_COPY_PLAN_SEND_DATE = "send_date";
    public static final String METER_COPY_PLAN_REMARK = "remark";

    /**
     * 远传表指令执行情况表
     */
    public static final String REMOTE_METER_COMMAND_TABLE = "code";
    public static final String REMOTE_METER_COMMAND_CODE = "code";
    public static final String REMOTE_METER_COMMAND_METER_CODE = "meter_code";
    public static final String REMOTE_METER_COMMAND_TYPE = "type";
    public static final String REMOTE_METER_COMMAND_SEND_TIME = "send_time";
    public static final String REMOTE_METER_COMMAND_RESULT = "result";
    public static final String REMOTE_METER_COMMAND_RESULT_MSG = "result_msg";
    public static final String REMOTE_METER_COMMAND_RESULT_TIME = "result_time";

    /**
     * 远传表系统-厂商配置
     */
    public static final String TB_REMOTE_BRAND_SETTING_TABLE = "tb_remote_brand_setting";
    public static final String TB_REMOTE_BRAND_SETTING_NAME = "name";
    public static final String TB_REMOTE_BRAND_SETTING_DAY_RANGE = "day_range";
    public static final String TB_REMOTE_BRAND_SETTING_FACTORY_TYPE = "factory_type";
    public static final String TB_REMOTE_BRAND_SETTING_MAX_RANGE = "max_range";
    public static final String TB_REMOTE_BRAND_SETTING_APP_ID = "app_id";
    public static final String TB_REMOTE_BRAND_SETTING_APP_SECRET = "app_secret";


    /**
     * 远传表系统-厂商API信息
     */
    public static final String TB_REMOTE_API_INFO_TABLE = "tb_remote_api_info";
    public static final String TB_REMOTE_API_INFO_MAIN_ID = "main_id";
    public static final String TB_REMOTE_API_INFO_NAME = "name";
    public static final String TB_REMOTE_API_INFO_URL = "url";
    public static final String TB_REMOTE_API_INFO_ENABLE = "enable";
    public static final String TB_REMOTE_API_INFO_TYPE = "type";



    /**
     * 抄核数据副本-用户信息表
     */
    public static final String COPY_DATA_CUST_INFO_TABLE = "copy_data_cust_info";
    public static final String COPY_DATA_CUST_INFO_ORG_ID = "org_id";
    public static final String COPY_DATA_CUST_INFO_METER_BOOK_ID = "meter_book_id";
    public static final String COPY_DATA_CUST_INFO_PLAN_CODE = "plan_code";
    public static final String COPY_DATA_CUST_INFO_CODE = "code";
    public static final String COPY_DATA_CUST_INFO_NAME = "name";
    public static final String COPY_DATA_CUST_INFO_ID_NUMBER = "id_number";
    public static final String COPY_DATA_CUST_INFO_PHONE = "phone";
    public static final String COPY_DATA_CUST_INFO_ADDRESS = "address";
    public static final String COPY_DATA_CUST_INFO_EMAIL = "email";
    public static final String COPY_DATA_CUST_INFO_POPULATION = "population";
    public static final String COPY_DATA_CUST_INFO_WATER_CATEGORY = "water_category";
    public static final String COPY_DATA_CUST_INFO_INDUSTRY_CATEGORY = "industry_category";
    public static final String COPY_DATA_CUST_INFO_USER_TYPE = "user_type";
    public static final String COPY_DATA_CUST_INFO_PAYMENT_METHOD = "payment_method";
    public static final String COPY_DATA_CUST_INFO_PAY_TYPE = "pay_type";
    public static final String COPY_DATA_CUST_INFO_SPECIAL_USER_TYPE = "special_user_type";
    public static final String COPY_DATA_CUST_INFO_BILL_TYPE = "bill_type";
    public static final String COPY_DATA_CUST_INFO_TRANSFER_USER_FLAG = "transfer_user_flag";
    public static final String COPY_DATA_CUST_INFO_TEMPORARY_WATER_FLAG = "temporary_water_flag";
    public static final String COPY_DATA_CUST_INFO_TEMPORARY_WATER_END_DATE = "temporary_water_end_date";
    public static final String COPY_DATA_CUST_INFO_LIQUIDATED_DAMAGES = "liquidated_damages";
    public static final String COPY_DATA_CUST_INFO_LOW_CONSUMPTION_WATER = "low_consumption_water";
    public static final String COPY_DATA_CUST_INFO_CONTRACT_NUMBER = "contract_number";
    public static final String COPY_DATA_CUST_INFO_REMARK = "remark";
    public static final String COPY_DATA_CUST_INFO_STATUS = "status";

    /**
     * 抄核数据副本-抄表册表
     */
    public static final String COPY_DATA_METER_BOOK_TABLE = "copy_data_meter_book";
    public static final String COPY_DATA_METER_BOOK_CODE = "code";
    public static final String COPY_DATA_METER_BOOK_NAME = "name";
    public static final String COPY_DATA_METER_BOOK_TYPE = "type";
    public static final String COPY_DATA_METER_BOOK_METER_COPY_EVENT = "meter_copy_event";
    public static final String COPY_DATA_METER_BOOK_CYCLE = "cycle";
    public static final String COPY_DATA_METER_BOOK_CYCLE_MONTH_NUMBER = "cycle_month_number";
    public static final String COPY_DATA_METER_BOOK_DAY_OF_MONTH = "day_of_month";
    public static final String COPY_DATA_METER_BOOK_METER_COPY_METHOD = "meter_copy_method";
    public static final String COPY_DATA_METER_BOOK_METER_LAST_MONTH = "last_month";
    public static final String COPY_DATA_METER_BOOK_METER_STATUS = "status";
    public static final String COPY_DATA_METER_BOOK_PLAN_CODE = "plan_code";

    /**
     * 抄核数据副本-计量点信息
     */
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_TABLE = "copy_data_cust_metering_point_info";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_CODE = "code";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_POINT_CODE = "point_code";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_NAME = "name";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_ADDRESS = "address";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_IS_METER = "is_meter";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_METER_COPY_ORDER_NUMBER = "meter_copy_order_number";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_CALCULATION_METHOD = "calculation_method";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_CALCULATION_VALUE = "calculation_value";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_CALCULATION_DEDUCTION_FLAG = "calculation_deduction_flag";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_EXECUTE_ORDER_NUMBER = "execute_order_number";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_LOST_TYPE = "lost_type";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_LOST_VALUE = "lost_value";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_INDUSTRY_CATEGORY = "industry_category";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_WATER_PRICE_TYPE = "water_price_type";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_PRICE_CODE = "price_code";
    public static final String COPY_DATA_CUST_METERING_POINT_INFO_IS_LADDER = "is_ladder";

    /**
     * 抄核数据副本-计量点关系表
     */
    public static final String COPY_DATA_CUST_METERING_POINT_RELATION_TABLE = "copy_data_cust_metering_point_relation";
    public static final String COPY_DATA_CUST_METERING_POINT_RELATION_TEMP_TABLE = "tb_cust_metering_point_relation_temp";
    public static final String COPY_DATA_CUST_METERING_POINT_RELATION_POINT_ID = "point_id";
    public static final String COPY_DATA_CUST_METERING_POINT_RELATION_RELATION_POINT_ID = "relation_point_id";
    public static final String COPY_DATA_CUST_METERING_POINT_RELATION_RELATION_TYPE = "relation_type";

    /**
     * 抄核数据副本-DMA抄表数据
     */
    public static final String PIPE_COPY_DATA_READ_METER_DATA_TABLE = "tb_pipe_copy_data_read_meter_data";
    public static final String PIPE_COPY_DATA_READ_METER_DATA_CORRECT_WATER = "correct_water";

    /**
     * 智慧管网-用水量修正记录
     */
    public static final String PIPE_COPY_DATA_CORRECT_RECORDS_TABLE = "tb_pipe_copy_data_correct_records";
    public static final String PIPE_COPY_DATA_CORRECT_RECORDS_COPY_ID = "copy_id";
    public static final String PIPE_COPY_DATA_CORRECT_RECORDS_CORRECT_WATER = "correct_water";
    public static final String PIPE_COPY_DATA_CORRECT_RECORDS_OLD_VALUE = "old_value";
    public static final String PIPE_COPY_DATA_CORRECT_RECORDS_NEW_VALUE = "new_value";

    /**
     * 抄核数据副本-抄表数据
     */
    public static final String COPY_DATA_READ_METER_DATA_TABLE = "copy_data_read_meter_data";
    public static final String COPY_DATA_PHONE_READ_METER_DATA_TABLE = "copy_data_read_meter_data";
    public static final String COPY_DATA_READ_METER_DATA_CODE = "code";
    public static final String COPY_DATA_READ_METER_DATA_YM = "ym";
    public static final String COPY_DATA_READ_METER_DATA_USER_CODE = "user_code";
    public static final String COPY_DATA_READ_METER_DATA_ORG_ID = "org_id";
    public static final String COPY_DATA_READ_METER_DATA_METER_ID = "meter_id";
    public static final String COPY_DATA_READ_METER_DATA_POINT_ID = "point_id";
    public static final String COPY_DATA_READ_METER_DATA_METER_COPY_NUMBER = "meter_copy_number";
    public static final String COPY_DATA_READ_METER_DATA_LAST_READ_NUM = "last_read_num";
    public static final String COPY_DATA_READ_METER_DATA_LAST_READ_WATER = "last_read_water";
    public static final String COPY_DATA_READ_METER_DATA_THIS_READ_NUM = "this_read_num";
    public static final String COPY_DATA_READ_METER_DATA_THIS_READ_WATER = "this_read_water";
    public static final String COPY_DATA_READ_METER_DATA_APPEND_WATER = "append_water";
    public static final String COPY_DATA_READ_METER_DATA_TOTAL_WATER = "total_water";
    public static final String COPY_DATA_READ_METER_DATA_ORIGIN_WATER = "origin_water";
    public static final String COPY_DATA_READ_METER_DATA_READ_STATUS = "read_status";
    public static final String COPY_DATA_READ_METER_DATA_EXCEPTION_TYPE = "exception_type";
    public static final String COPY_DATA_READ_METER_DATA_LAST_READ_DATE = "last_read_date";
    public static final String COPY_DATA_READ_METER_DATA_THIS_READ_DATE = "this_read_date";
    public static final String COPY_DATA_READ_METER_DATA_RECORD_TYPE = "record_type";
    public static final String COPY_DATA_READ_METER_DATA_METER_ADDRESS = "meter_address";
    public static final String COPY_DATA_READ_METER_DATA_REMARK = "remark";
    public static final String COPY_DATA_READ_METER_DATA_EXECUTE_USER = "execute_user";
    public static final String COPY_DATA_READ_METER_DATA_UPDATE_USER = "update_user";

    /**
     * 抄核数据副本-水费账单
     */
    public static final String COPY_DATA_WATER_BILL_TABLE = "copy_data_water_bill";
    public static final String COPY_DATA_WATER_BILL_ORG_ID = "org_id";
    public static final String COPY_DATA_WATER_BILL_PLAN_CODE = "plan_code";
    public static final String COPY_DATA_WATER_BILL_USER_CODE = "user_code";
    public static final String COPY_DATA_WATER_BILL_YM = "ym";
    public static final String COPY_DATA_WATER_BILL_METER_COPY_DATA_ID = "meter_copy_data_id";
    public static final String COPY_DATA_WATER_BILL_BASE_MONEY = "base_money";
    public static final String COPY_DATA_WATER_BILL_SIGN_PRICE_MONEY = "sign_price_money";
    public static final String COPY_DATA_WATER_BILL_ADDITIONAL_MONEY = "additional_money";
    public static final String COPY_DATA_WATER_BILL_SEWAGE_MONEY = "sewage_money";
    public static final String COPY_DATA_WATER_BILL_RESOURCE_TAX_MONEY = "resource_tax_money";
    public static final String COPY_DATA_WATER_BILL_PRESSURIZATION_MONEY = "pressurization_money";
    public static final String COPY_DATA_WATER_BILL_RECEIVABLE_MONEY = "receivable_money";
    public static final String COPY_DATA_WATER_BILL_TOTAL_WATER = "total_water";
    public static final String COPY_DATA_WATER_BILL_TOTAL_READ_WATER = "total_read_water";
    public static final String COPY_DATA_WATER_BILL_TOTAL_APPEND_WATER = "total_append_water";

    /**
     * 抄核数据副本-水费账单明细
     */
    public static final String COPY_DATA_WATER_BILL_DETAIL_TABLE = "copy_data_water_bill_detail";
    public static final String COPY_DATA_WATER_BILL_DETAIL_TYPE = "type";
    public static final String COPY_DATA_WATER_BILL_DETAIL_WATER_BILL_ID = "water_bill_id";
    public static final String COPY_DATA_WATER_BILL_DETAIL_PRICE_NAME = "price_name";
    public static final String COPY_DATA_WATER_BILL_DETAIL_SIGN_PRICE_NAME = "sign_price_name";
    public static final String COPY_DATA_WATER_BILL_DETAIL_UNIT_PRICE = "unit_price";
    public static final String COPY_DATA_WATER_BILL_DETAIL_LADDER = "ladder";
    public static final String COPY_DATA_WATER_BILL_DETAIL_WATER_NUM = "water_num";
    public static final String COPY_DATA_WATER_BILL_DETAIL_MONEY = "money";
    public static final String COPY_DATA_WATER_BILL_DETAIL_SIGN_PRICE_MONEY = "sign_price_money";
    public static final String COPY_DATA_WATER_BILL_DETAIL_PLAN_CODE = "plan_code";

    /**
     * 水费账单
     */
    public static final String TB_WATER_BILL_TABLE = "tb_water_bill";
    public static final String TB_WATER_BILL_ORG_ID = "org_id";
    public static final String TB_WATER_BILL_PLAN_CODE = "plan_code";
    public static final String TB_WATER_BILL_USER_CODE = "user_code";
    public static final String TB_WATER_BILL_YM = "ym";
    public static final String TB_WATER_BILL_YM_TIME = "ym_time";
    public static final String TB_WATER_BILL_SEND_TIME = "send_time";
    public static final String TB_WATER_BILL_METER_COPY_DATA_ID = "meter_copy_data_id";
    public static final String TB_WATER_BILL_BASE_MONEY = "base_money";
    public static final String TB_WATER_BILL_SIGN_PRICE_MONEY = "sign_price_money";
    public static final String TB_WATER_BILL_ADDITIONAL_MONEY = "additional_money";
    public static final String TB_WATER_BILL_SEWAGE_MONEY = "sewage_money";
    public static final String TB_WATER_BILL_RESOURCE_TAX_MONEY = "resource_tax_money";
    public static final String TB_WATER_BILL_PRESSURIZATION_MONEY = "pressurization_money";
    public static final String TB_WATER_BILL_RECEIVABLE_MONEY = "receivable_money";
    public static final String TB_WATER_BILL_TOTAL_WATER = "total_water";
    public static final String TB_WATER_BILL_TOTAL_READ_WATER = "total_read_water";
    public static final String TB_WATER_BILL_TOTAL_APPEND_WATER = "total_append_water";

    /**
     * 应收水费
     */
    public static final String RECEIVABLE_MONEY_TABLE = "tb_receivable_money";
    public static final String RECEIVABLE_MONEY_CODE = "code";
    public static final String RECEIVABLE_MONEY_PLAN_CODE = "plan_code";
    public static final String RECEIVABLE_MONEY_METER_COPY_ID = "meter_copy_id";
    public static final String RECEIVABLE_MONEY_ORG_ID = "org_id";
    public static final String RECEIVABLE_MONEY_USER_CODE = "user_code";
    public static final String RECEIVABLE_MONEY_YM = "ym";
    public static final String RECEIVABLE_MONEY_RECEIVABLE_TIME = "receivable_time";
    public static final String RECEIVABLE_MONEY_PAY_MODE = "pay_mode";
    public static final String RECEIVABLE_MONEY_TYPE = "type";
    public static final String RECEIVABLE_MONEY_TOTAL_WATER = "total_water";
    public static final String RECEIVABLE_MONEY_RECEIVABLE_MONEY = "receivable_money";
    public static final String RECEIVABLE_MONEY_ADVANCE_PAYMENT_MONEY = "advance_payment_money";
    public static final String RECEIVABLE_MONEY_RECEIVED_MONEY = "received_money";
    public static final String RECEIVABLE_MONEY_STATUS = "status";
    public static final String RECEIVABLE_MONEY_SETTLE_FLAG = "settle_flag";
    public static final String RECEIVABLE_MONEY_PENALTY_START_DATE = "penalty_start_date";
    public static final String RECEIVABLE_MONEY_RECEIVABLE_BASE = "receivable_base";
    public static final String RECEIVABLE_MONEY_RECEIVABLE_SIGN_PRICE = "receivable_sign_price";
    public static final String RECEIVABLE_MONEY_RECEIVED_BASE = "received_base";
    public static final String RECEIVABLE_MONEY_RECEIVED_SIGN_PRICE = "received_sign_price";
    public static final String RECEIVABLE_MONEY_RECEIVABLE_PENALTY = "receivable_penalty";
    public static final String RECEIVABLE_MONEY_RECEIVED_PENALTY = "received_penalty";
    public static final String RECEIVABLE_MONEY_RECEIVABLE_ADDITIONAL = "receivable_additional";
    public static final String RECEIVABLE_MONEY_RECEIVED_ADDITIONAL = "received_additional";
    public static final String RECEIVABLE_MONEY_SEND_DATE = "send_date";
    public static final String RECEIVABLE_MONEY_OWED_MONEY = "owed_money";
    public static final String RECEIVABLE_MONEY_IS_RESEND = "is_resend";
    public static final String RECEIVABLE_MONEY_TENANT_ID = "tenant_id";

    /**
     * 抄核数据副本-水费账单明细
     */
    public static final String TB_WATER_BILL_DETAIL_TABLE = "tb_water_bill_detail";
    public static final String TB_WATER_BILL_DETAIL_TYPE = "type";
    public static final String TB_WATER_BILL_DETAIL_WATER_BILL_ID = "water_bill_id";
    public static final String TB_WATER_BILL_DETAIL_PRICE_NAME = "price_name";
    public static final String TB_WATER_BILL_DETAIL_SIGN_PRICE_NAME = "sign_price_name";
    public static final String TB_WATER_BILL_DETAIL_UNIT_PRICE = "unit_price";
    public static final String TB_WATER_BILL_DETAIL_LADDER = "ladder";
    public static final String TB_WATER_BILL_DETAIL_WATER_NUM = "water_num";
    public static final String TB_WATER_BILL_DETAIL_MONEY = "money";
    public static final String TB_WATER_BILL_DETAIL_SIGN_PRICE_MONEY = "sign_price_money";
    public static final String TB_WATER_BILL_DETAIL_PLAN_CODE = "plan_code";
    public static final String TB_WATER_BILL_DETAIL_RECEIVABLE_ID = "receivable_id";


    /**
     * 实收水费
     */
    public static final String RECEIVED_MONEY_TABLE = "tb_received_money";
    public static final String RECEIVED_MONEY_RECEIVABLE_ID = "receivable_id";
    public static final String RECEIVED_MONEY_ORG_ID = "org_id";
    public static final String RECEIVED_MONEY_USER_CODE = "user_code";
    public static final String RECEIVED_MONEY_YM = "ym";
    public static final String RECEIVED_MONEY_RECEIVED_DATE = "received_date";
    public static final String RECEIVED_MONEY_THIS_RECEIVED_WATER = "this_received_water";
    public static final String RECEIVED_MONEY_THIS_RECEIVED_MONEY = "this_received_money";
    public static final String RECEIVED_MONEY_THIS_RECEIVED_BASE = "this_received_base";
    public static final String RECEIVED_MONEY_THIS_RECEIVED_SIGN_PRICE = "this_received_sign_price";
    public static final String RECEIVED_MONEY_THIS_RECEIVED_PENALTY = "this_received_penalty";
    public static final String RECEIVED_MONEY_THIS_RECEIVED_ADDITIONAL = "this_received_additional";
    public static final String RECEIVED_MONEY_OWED_MONEY = "owed_money";
    public static final String RECEIVED_MONEY_RECEIVABLE_YM = "receivable_ym";
    public static final String RECEIVED_MONEY_PAY_TYPE = "pay_type";
    public static final String RECEIVED_MONEY_LAST_MONEY = "last_money";
    public static final String RECEIVED_MONEY_THIS_MONEY = "this_money";
    public static final String RECEIVED_MONEY_PREPAY_IN = "prepay_in";
    public static final String RECEIVED_MONEY_PREPAY_OUT = "prepay_out";
    public static final String RECEIVED_MONEY_THIS_RECEIVED_WATER_MONEY = "this_received_water_money";
    public static final String RECEIVED_MONEY_TYPE = "type";
    public static final String RECEIVED_MONEY_OPTION_USER = "option_user";
    public static final String RECEIVED_MONEY_BILL_ID = "bill_id";



    /**
     * 抄表数据
     */
    public static final String READ_METER_DATA_TABLE = "tb_read_meter_data";
    public static final String READ_METER_DATA_CODE = "code";
    public static final String READ_METER_DATA_YM = "ym";
    public static final String READ_METER_DATA_YM_TIME = "ym_time";
    public static final String READ_METER_DATA_USER_CODE = "user_code";
    public static final String READ_METER_DATA_ORG_ID = "org_id";
    public static final String READ_METER_DATA_METER_ID = "meter_id";
    public static final String READ_METER_DATA_POINT_ID = "point_id";
    public static final String READ_METER_DATA_METER_COPY_NUMBER = "meter_copy_number";
    public static final String READ_METER_DATA_LAST_READ_NUM = "last_read_num";
    public static final String READ_METER_DATA_LAST_READ_WATER = "last_read_water";
    public static final String READ_METER_DATA_THIS_READ_NUM = "this_read_num";
    public static final String READ_METER_DATA_THIS_READ_WATER = "this_read_water";
    public static final String READ_METER_DATA_APPEND_WATER = "append_water";
    public static final String READ_METER_DATA_TOTAL_WATER = "total_water";
    public static final String READ_METER_DATA_READ_STATUS = "read_status";
    public static final String READ_METER_DATA_SEND_STATUS = "send_status";
    public static final String READ_METER_DATA_EXCEPTION_TYPE = "exception_type";
    public static final String READ_METER_DATA_LAST_READ_DATE = "last_read_date";
    public static final String READ_METER_DATA_THIS_READ_DATE = "this_read_date";
    public static final String READ_METER_DATA_DATA_TIME = "data_time";
    public static final String READ_METER_DATA_RECORD_TYPE = "record_type";
    public static final String READ_METER_DATA_TYPE = "type";
    public static final String READ_METER_DATA_IMGS = "imgs";
    public static final String READ_METER_DATA_METER_ADDRESS = "meter_address";
    public static final String READ_METER_DATA_METER_COPY_USER = "meter_copy_user";
    public static final String READ_METER_DATA_METER_REMARK = "remark";


    /**
     * 用户调整所在表册日志
     */
    public static final String USER_BOOK_CHANGE_LOG_TABLE = "tb_user_book_change_log";
    public static final String USER_BOOK_CHANGE_LOG_USER_CODE = "user_code";
    public static final String USER_BOOK_CHANGE_LOG_OLD_BOOK_CODE = "old_book_code";
    public static final String USER_BOOK_CHANGE_LOG_NEW_BOOK_CODE = "new_book_code";
    public static final String USER_BOOK_CHANGE_LOG_EXECUTE_TIME = "execute_time";
    public static final String USER_BOOK_CHANGE_LOG_EXECUTOR = "executor";

    /**
     * 代扣银行信息
     */
    public static final String CUST_BANK_INFO_TABLE = "tb_cust_bank_info";
    public static final String CUST_BANK_INFO_TEMP_TABLE = "tb_cust_bank_info_temp";
    public static final String CUST_BANK_INFO_CODE = "code";
    public static final String CUST_BANK_INFO_TYPE = "type";
    public static final String CUST_BANK_INFO_NAME = "name";
    public static final String CUST_BANK_INFO_NUMBER = "number";
    public static final String CUST_BANK_INFO_CONTRACT_NUMBER = "contract_number";

    /**
     * 用户联系人
     */
    public static final String CUST_CONTACTS_TABLE = "tb_cust_contacts";
    public static final String CUST_CONTACTS_TEMP_TABLE = "tb_cust_contacts_temp";
    public static final String CUST_CONTACTS_CODE = "code";
    public static final String CUST_CONTACTS_TYPE = "type";
    public static final String CUST_CONTACTS_NAME = "name";
    public static final String CUST_CONTACTS_PHONE = "phone";
    public static final String CUST_CONTACTS_EMAIL = "email";

    /**
     * 用户证件信息
     */
    public static final String CUST_CREDENTIALS_TABLE = "tb_cust_credentials";
    public static final String CUST_CREDENTIALS_TEMP_TABLE = "tb_cust_credentials_temp";
    public static final String CUST_CREDENTIALS_TEMP_ORIGIN_ID= "origin_id";
    public static final String CUST_CREDENTIALS_CODE = "code";
    public static final String CUST_CREDENTIALS_TYPE = "type";
    public static final String CUST_CREDENTIALS_NAME = "name";
    public static final String CUST_CREDENTIALS_NUMBER = "number";

    /**
     * 用户基本信息
     */
    public static final String CUST_INFO_TABLE = "tb_cust_info";
    public static final String CUST_INFO_TEMP_TABLE = "tb_cust_info_temp";
    public static final String CUST_INFO_ORG_ID = "org_id";
    public static final String CUST_INFO_METER_BOOK_ID = "meter_book_id";
    public static final String CUST_INFO_CODE = "code";
    public static final String CUST_INFO_NAME = "name";
    public static final String CUST_INFO_ID_NUMBER = "id_number";
    public static final String CUST_INFO_PHONE = "phone";
    public static final String CUST_INFO_ADDRESS = "address";
    public static final String CUST_INFO_EMAIL = "email";
    public static final String CUST_INFO_POPULATION = "population";
    public static final String CUST_INFO_WATER_CATEGORY = "water_category";
    public static final String CUST_INFO_INDUSTRY_CATEGORY = "industry_category";
    public static final String CUST_INFO_USER_TYPE = "user_type";
    public static final String CUST_INFO_PAYMENT_METHOD = "payment_method";
    public static final String CUST_INFO_PAY_TYPE = "pay_type";
    public static final String CUST_INFO_SPECIAL_USER_TYPE = "special_user_type";
    public static final String CUST_INFO_BILL_TYPE = "bill_type";
    public static final String CUST_INFO_TRANSFER_USER_FLAG = "transfer_user_flag";
    public static final String CUST_INFO_TEMPORARY_WATER_FLAG = "temporary_water_flag";
    public static final String CUST_INFO_TEMPORARY_WATER_END_DATE = "temporary_water_end_date";
    public static final String CUST_INFO_LIQUIDATED_DAMAGES = "liquidated_damages";
    public static final String CUST_INFO_LOW_CONSUMPTION_WATER = "low_consumption_water";
    public static final String CUST_INFO_CONTRACT_NUMBER = "contract_number";
    public static final String CUST_INFO_WATER_STOP_FLAG = "water_stop_flag";
    public static final String CUST_INFO_REMARK = "remark";
    public static final String CUST_INFO_STATUS = "status";

    /**
     * 用户余额表
     */
    public static final String BALANCE_TABLE = "tb_balance";
    public static final String BALANCE_CUST_CODE = "cust_code";
    public static final String BALANCE_BALANCE = "balance";
    public static final String BALANCE_FROZEN_BALANCE = "frozen_balance";

    /**
     * 微信绑定用户表
     */
    public static final String WECHAT_BIND_TABLE = "tb_wechat_bind";
    public static final String WECHAT_BIND_OPENID = "openid";
    public static final String WECHAT_BIND_CUST_CODE = "cust_code";

    /**
     * 违约金配置
     */
    public static final String LIQUIDATED_DAMAGES_TABLE = "tb_liquidated_damages";
    public static final String LIQUIDATED_DAMAGES_ORG_ID = "org_id";
    public static final String LIQUIDATED_DAMAGES_NAME = "name";
    public static final String LIQUIDATED_DAMAGES_TYPE = "type";
    public static final String LIQUIDATED_DAMAGES_VALUE = "value";
    public static final String LIQUIDATED_DAMAGES_MAX_TYPE = "max_type";
    public static final String LIQUIDATED_DAMAGES_MAX_VALUE = "max_value";
    public static final String LIQUIDATED_DAMAGES_CALCULATION_TYPE = "calculation_type";
    public static final String LIQUIDATED_DAMAGES_START_DAY = "start_day";
    public static final String LIQUIDATED_DAMAGES_RATE = "rate";
    public static final String LIQUIDATED_DAMAGES_BEGIN_FLAG = "begin_flag";
    public static final String LIQUIDATED_DAMAGES_STATUS = "status";

    /**
     * 违约金减免/暂缓
     */
    public static final String LIQUIDATED_DAMAGES_REDUCE_TABLE = "tb_liquidated_damages_reduce";
    public static final String LIQUIDATED_DAMAGES_REDUCE_ORG_ID = "org_id";
    public static final String LIQUIDATED_DAMAGES_REDUCE_CUST_CODE = "cust_code";
    public static final String LIQUIDATED_DAMAGES_REDUCE_TYPE = "type";
    public static final String LIQUIDATED_DAMAGES_REDUCE_REDUCE_MONEY = "reduce_money";
    public static final String LIQUIDATED_DAMAGES_REDUCE_OPTION_TIME = "option_time";
    public static final String LIQUIDATED_DAMAGES_REDUCE_OPTION_USER = "option_user";
    public static final String LIQUIDATED_DAMAGES_REDUCE_RESTART_TIME = "restart_time";
    public static final String LIQUIDATED_DAMAGES_REDUCE_REMARK = "remark";
    public static final String LIQUIDATED_DAMAGES_REDUCE_RECEIVABLE_ID = "receivable_id";



    /**
     * 开票信息
     */
    public static final String CUST_INVOICE_INFO_TABLE = "tb_cust_invoice_info";
    public static final String CUST_INVOICE_INFO_TEMP_TABLE = "tb_cust_invoice_info_temp";
    public static final String CUST_INVOICE_INFO_CODE = "code";
    public static final String CUST_INVOICE_INFO_NAME = "name";
    public static final String CUST_INVOICE_INFO_TAX_NUMBER = "tax_number";
    public static final String CUST_INVOICE_INFO_REGISTER_ADDRESS = "register_address";
    public static final String CUST_INVOICE_INFO_BANK_NAME = "bank_name";
    public static final String CUST_INVOICE_INFO_BANK_NUMBER = "bank_number";
    public static final String CUST_INVOICE_INFO_PHONE = "phone";

    /**
     * 水表附加价绑定
     */
    public static final String CUST_ADDITIONAL_PRICE_TABLE = "tb_cust_additional_price";
    public static final String CUST_ADDITIONAL_PRICE_TEMP_TABLE = "tb_cust_additional_price_temp";
    public static final String CUST_ADDITIONAL_PRICE_CODE = "code";
    public static final String CUST_ADDITIONAL_PRICE_ADDITIONAL_PRICE_CODE = "additional_price_code";
    public static final String CUST_ADDITIONAL_PRICE_FACTOR = "factor";
    public static final String CUST_ADDITIONAL_PRICE_PLAN_CODE = "plan_code";

    /**
     * 水表附加价绑定
     */
    public static final String COPY_DATA_CUST_ADDITIONAL_PRICE_TABLE = "copy_data_cust_additional_price";
    public static final String COPY_DATA_CUST_ADDITIONAL_PRICE_CODE = "code";
    public static final String COPY_DATA_CUST_ADDITIONAL_PRICE_ADDITIONAL_PRICE_CODE = "additional_price_code";
    public static final String COPY_DATA_CUST_ADDITIONAL_PRICE_FACTOR = "factor";

    /**
     * 计量点信息
     */
    public static final String CUST_METERING_POINT_INFO_TABLE = "tb_cust_metering_point_info";
    public static final String CUST_METERING_POINT_INFO_TEMP_TABLE = "tb_cust_metering_point_info_temp";
    public static final String CUST_METERING_POINT_INFO_CODE = "code";
    public static final String CUST_METERING_POINT_INFO_POINT_CODE = "point_code";
    public static final String CUST_METERING_POINT_INFO_NAME = "name";
    public static final String CUST_METERING_POINT_INFO_ADDRESS = "address";
    public static final String CUST_METERING_POINT_INFO_IS_METER = "is_meter";
    public static final String CUST_METERING_POINT_INFO_METER_COPY_ORDER_NUMBER = "meter_copy_order_number";
    public static final String CUST_METERING_POINT_INFO_CALCULATION_METHOD = "calculation_method";
    public static final String CUST_METERING_POINT_INFO_CALCULATION_VALUE = "calculation_value";
    public static final String CUST_METERING_POINT_INFO_CALCULATION_DEDUCTION_FLAG = "calculation_deduction_flag";
    public static final String CUST_METERING_POINT_INFO_EXECUTE_ORDER_NUMBER = "execute_order_number";
    public static final String CUST_METERING_POINT_INFO_LOST_TYPE = "lost_type";
    public static final String CUST_METERING_POINT_INFO_LOST_VALUE = "lost_value";
    public static final String CUST_METERING_POINT_INFO_INDUSTRY_CATEGORY = "industry_category";
    public static final String CUST_METERING_POINT_INFO_WATER_PRICE_TYPE = "water_price_type";
    public static final String CUST_METERING_POINT_INFO_PRICE_CODE = "price_code";
    public static final String CUST_METERING_POINT_INFO_IS_LADDER = "is_ladder";

    /**
     * 计量点关系表
     */
    public static final String CUST_METERING_POINT_RELATION_TABLE = "tb_cust_metering_point_relation";
    public static final String CUST_METERING_POINT_RELATION_TEMP_TABLE = "tb_cust_metering_point_relation_temp";
    public static final String CUST_METERING_POINT_RELATION_POINT_ID = "point_id";
    public static final String CUST_METERING_POINT_RELATION_RELATION_POINT_ID = "relation_point_id";
    public static final String CUST_METERING_POINT_RELATION_RELATION_TYPE = "relation_type";

    /**
     * 水表信息
     */
    public static final String WATER_METER_TABLE = "tb_water_meter";
    public static final String WATER_METER_TEMP_TABLE = "tb_water_meter_temp";
    public static final String WATER_METER_CODE = "code";
    public static final String WATER_METER_CUST_CODE = "cust_code";
    public static final String WATER_METER_OLD_METER_CODE = "old_meter_code";
    public static final String WATER_METER_METER_CODE = "meter_code";
    public static final String WATER_METER_TYPE = "type";
    public static final String WATER_METER_BRAND = "brand";
    public static final String WATER_METER_CALIBER = "caliber";
    public static final String WATER_METER_ADDRESS = "address";
    public static final String WATER_METER_BAR_CODE = "bar_code";
    public static final String WATER_METER_REMOTE_METER_ADDRESS = "remote_meter_address";
    public static final String WATER_METER_STEEL_SEAL_NUMBER = "steel_seal_number";
    public static final String WATER_METER_INSTALL_POSITION = "install_position";
    public static final String WATER_METER_METER_WELL_CODE = "meter_well_code";
    public static final String WATER_METER_ACCEPTANCE_LEAD_SEAL_NUMBER = "acceptance_lead_seal_number";
    public static final String WATER_METER_INSTALL_LEAD_SEAL_NUMBER = "install_lead_seal_number";
    public static final String WATER_METER_MEASURE_LEAD_SEAL_NUMBER = "measure_lead_seal_number";
    public static final String WATER_METER_REMARK = "remark";
    public static final String WATER_METER_LONGITUDE = "longitude";
    public static final String WATER_METER_LATITUDE = "latitude";
    public static final String WATER_METER_INSTALL_ENVIRONMENT = "install_environment";
    public static final String WATER_METER_READ_BIT = "read_bit";
    public static final String WATER_METER_CURRENT_READ = "current_read";
    public static final String WATER_METER_VALVE_STATE = "valve_state";
    public static final String WATER_METER_APPEND_WATER = "append_water";
    public static final String WATER_METER_REMOVE_METER_BOTTOM = "remove_meter_bottom";
    public static final String WATER_METER_CHANGE_TIME = "change_time";

    /**
     * 远传表-抄表数据库
     */
    public static final String REMOTE_METER_DATA_TABLE = "tb_remote_meter_data";
    public static final String REMOTE_METER_DATA_CUST_CODE = "cust_code";
    public static final String REMOTE_METER_DATA_METER_CODE = "meter_code";
    public static final String REMOTE_METER_DATA_REMOTE_METER_CODE = "remote_meter_code";
    public static final String REMOTE_METER_DATA_VALUE = "value";
    public static final String REMOTE_METER_DATA_VALVE_STATE = "valve_state";
    public static final String REMOTE_METER_DATA_UPLOAD_TIME = "upload_time";
    public static final String REMOTE_METER_DATA_DATA_TIME = "data_time";
    public static final String REMOTE_METER_DATA_TYPE = "type";

    /**
     * 远传表-区域管理
     */
    public static final String REMOTE_AREA_TABLE = "tb_remote_area";
    public static final String REMOTE_AREA_PID = "pid";
    public static final String REMOTE_AREA_TYPE = "type";
    public static final String REMOTE_AREA_NAME = "name";
    public static final String REMOTE_AREA_CODE = "code";
    public static final String REMOTE_AREA_ORDER_NUM = "order_num";

    /**
     * 远传表-抄表数据库
     */
    public static final String REMOTE_METER_INFO_TABLE = "tb_remote_meter_info";
    public static final String REMOTE_METER_INFO_AREA_ID = "area_id";
    public static final String REMOTE_METER_INFO_CUST_CODE = "cust_code";
    public static final String REMOTE_METER_INFO_TYPE = "type";
    public static final String REMOTE_METER_INFO_BASE_NUMBER = "base_number";
    public static final String REMOTE_METER_INFO_CHARGE_TYPE = "charge_type";
    public static final String REMOTE_METER_INFO_ADDRESS = "address";
    public static final String REMOTE_METER_INFO_IMEI = "imei";
    public static final String REMOTE_METER_INFO_OPERATOR = "operator";
    public static final String REMOTE_METER_INFO_CCID = "ccid";
    public static final String REMOTE_METER_INFO_IMSI = "imsi";
    public static final String REMOTE_METER_INFO_BRAND = "brand";
    public static final String REMOTE_METER_INFO_INSTALL_ADDRESS = "install_address";
    public static final String REMOTE_METER_INFO_INSTALL_TIME = "install_time";
    public static final String REMOTE_METER_INFO_VALVE_CLOSE_REMINDER_VALUE = "valve_close_reminder_value";
    public static final String REMOTE_METER_INFO_IS_OPEN = "is_open";
    public static final String REMOTE_METER_INFO_OPEN_TIME = "open_time";
    public static final String REMOTE_METER_INFO_VALVE_CLOSE_OWED_VALUE = "valve_close_owed_value";
    public static final String REMOTE_METER_INFO_ENABLE_CHARGE_OPEN_VALVE = "enable_charge_open_valve";
    public static final String REMOTE_METER_INFO_ENABLE_WARN_CLOSE_VALVE = "enable_warn_close_valve";
    public static final String REMOTE_METER_INFO_MULTIPLE = "multiple";
    public static final String REMOTE_METER_INFO_HAVE_VALVE = "have_valve";
    public static final String REMOTE_METER_INFO_PLASTIC_SEAL_NO = "plastic_seal_no";
    public static final String REMOTE_METER_INFO_WITHHOLD_ACCOUNT = "withhold_account";
    public static final String REMOTE_METER_INFO_MONTH_MIN_USE = "month_min_use";
    public static final String REMOTE_METER_INFO_CUST_TYPE = "cust_type";
    public static final String REMOTE_METER_INFO_CUST_IDENTIFY = "cust_identify";
    public static final String REMOTE_METER_INFO_FLOOR_AREA = "floor_area";
    public static final String REMOTE_METER_INFO_FIXED_LINE = "fixed_line";
    public static final String REMOTE_METER_INFO_HOUSE_DIRECTION = "house_direction";
    public static final String REMOTE_METER_INFO_FLOOR_NUM = "floor_num";
    public static final String REMOTE_METER_INFO_LONGITUDE = "longitude";
    public static final String REMOTE_METER_INFO_LATITUDE = "latitude";
    public static final String REMOTE_METER_INFO_FEE_MODEL = "fee_model";
    public static final String REMOTE_METER_INFO_PRICE_NAME = "price_name";
    public static final String REMOTE_METER_INFO_BALANCE_MSG1 = "balance_msg1";
    public static final String REMOTE_METER_INFO_BALANCE_MSG2 = "balance_msg2";
    public static final String REMOTE_METER_INFO_BALANCE_MSG3 = "balance_msg3";

    /**
     * 远传表-报警类型
     */
    public static final String REMOTE_METER_ALARM_TYPE_TABLE = "tb_remote_meter_alarm_type";
    public static final String REMOTE_METER_ALARM_TYPE_NAME = "name";
    public static final String REMOTE_METER_ALARM_TYPE_TYPE = "type";
    public static final String REMOTE_METER_ALARM_TYPE_JSON = "json";
    public static final String REMOTE_METER_ALARM_TYPE_ENABLE = "enable";

    /**
     * 远传表-异常报警
     */
    public static final String REMOTE_METER_ALARM_TABLE = "tb_remote_meter_alarm";
    public static final String REMOTE_METER_ALARM_METER_CODE = "meter_code";
    public static final String REMOTE_METER_ALARM_ALARM_TYPE = "alarm_type";
    public static final String REMOTE_METER_ALARM_FIRST_TIME = "first_time";
    public static final String REMOTE_METER_ALARM_LAST_TIME = "last_time";
    public static final String REMOTE_METER_ALARM_ALARM_CONTENT = "alarm_content";
    public static final String REMOTE_METER_ALARM_ALARM_COUNT = "alarm_count";
    public static final String REMOTE_METER_ALARM_PROCESS_USER = "process_user";
    public static final String REMOTE_METER_ALARM_PROCESS_USER_PHONE = "process_user_phone";
    public static final String REMOTE_METER_ALARM_PROCESS_RESULT = "process_result";
    public static final String REMOTE_METER_ALARM_PROCESS_TIME = "process_time";
    public static final String REMOTE_METER_ALARM_PROCESS_STATUS = "process_status";




    /**
     * 表井信息
     */
    public static final String METER_WELL_TABLE = "tb_meter_well";
    public static final String METER_WELL_CODE = "code";
    public static final String METER_WELL_NAME = "name";
    public static final String METER_WELL_VILLAGE = "village";
    public static final String METER_WELL_COORDINATE = "coordinate";

    /**
     * 特殊户类别
     */
    public static final String SPECIAL_USER_TYPE_TABLE = "tb_special_user_type";
    public static final String SPECIAL_USER_TYPE_ORG_ID = "org_id";
    public static final String SPECIAL_USER_TYPE_TYPE = "type";
    public static final String SPECIAL_USER_TYPE_NAME = "name";
    public static final String SPECIAL_USER_TYPE_ENABLE_TIME = "enable_time";
    public static final String SPECIAL_USER_TYPE_END_TIME = "end_time";
    public static final String SPECIAL_USER_TYPE_STATUS = "status";

    /**
     * 特殊户类别详情
     */
    public static final String SPECIAL_USER_TYPE_DETAIL_TABLE = "tb_special_user_type_detail";
    public static final String SPECIAL_USER_TYPE_DETAIL_TYPE_ID = "type_id";
    public static final String SPECIAL_USER_TYPE_DETAIL_CALCULATION_DIRECTION = "calculation_direction";
    public static final String SPECIAL_USER_TYPE_DETAIL_CALCULATION_UNIT = "calculation_unit";
    public static final String SPECIAL_USER_TYPE_DETAIL_PARAM_VALUE = "param_value";
    public static final String SPECIAL_USER_TYPE_DETAIL_PARAM_ORDER_NUMBER = "param_order_number";
    public static final String SPECIAL_USER_TYPE_DETAIL_CALCULATION_TYPE = "calculation_type";

    /**
     * 退补水费申请记录
     */
    public static final String RS_RECORD_TABLE = "tb_rs_record";
    public static final String RS_RECORD_CODE = "code";
    public static final String RS_RECORD_TYPE = "type";
    public static final String RS_RECORD_YM = "ym";
    public static final String RS_RECORD_EXECUTE_DATE = "execute_date";
    public static final String RS_RECORD_ORG_ID = "org_id";
    public static final String RS_RECORD_USER_CODE = "user_code";
    public static final String RS_RECORD_STATUS = "status";
    public static final String RS_RECORD_APPROVAL_STATUS = "approval_status";
    public static final String RS_RECORD_APPLICANT = "applicant";
    public static final String RS_RECORD_APPROVER = "approver";
    public static final String RS_RECORD_APPLICATION_TIME = "application_time";
    public static final String RS_RECORD_PROCESS_TIME = "process_time";
    public static final String RS_RECORD_REMARK = "remark";
    public static final String RS_RECORD_RECEIVABLE_ID = "receivable_id";

    /**
     * 退补水费-水费清单
     */
    public static final String RS_WATER_COST_TABLE = "tb_rs_water_cost";
    public static final String RS_WATER_COST_CODE = "code";
    public static final String RS_WATER_COST_WATER_NUMBER = "water_number";
    public static final String RS_WATER_COST_PRICE_CODE = "price_code";
    public static final String RS_WATER_COST_PRICE_DETAIL_ID = "price_detail_id";
    public static final String RS_WATER_COST_PRICE_NAME = "price_name";
    public static final String RS_WATER_COST_UNIT_PRICE = "unit_price";
    public static final String RS_WATER_COST_BASE_PRICE = "base_price";
    public static final String RS_WATER_COST_SIGN_PRICE = "sign_price";
    public static final String RS_WATER_COST_TOTAL_PRICE = "total_price";
    public static final String RS_WATER_COST_SIGN_PRICE_CODES = "sign_price_codes";

    /**
     * 退补水费-代征费清单
     */
    public static final String RS_SIGN_COST_TABLE = "tb_rs_sign_cost";
    public static final String RS_SIGN_COST_CODE = "code";
    public static final String RS_SIGN_COST_WATER_NUMBER = "water_number";
    public static final String RS_SIGN_COST_SIGN_PRICE_CODE = "sign_price_code";
    public static final String RS_SIGN_COST_SIGN_PRICE_NAME = "sign_price_name";
    public static final String RS_SIGN_COST_SIGN_PRICE = "sign_price";
    public static final String RS_SIGN_COST_TOTAL_PRICE = "total_price";

    /**
     * 退补水费-附加费清单
     */
    public static final String RS_ADDITIONAL_COST_TABLE = "tb_rs_additional_cost";
    public static final String RS_ADDITIONAL_COST_CODE = "code";
    public static final String RS_ADDITIONAL_COST_ADDITIONAL_PRICE_CODE = "additional_price_code";
    public static final String RS_ADDITIONAL_COST_ADDITIONAL_PRICE_NAME = "additional_price_name";
    public static final String RS_ADDITIONAL_COST_UNIT_PRICE = "unit_price";
    public static final String RS_ADDITIONAL_COST_TOTAL_PRICE = "total_price";
    public static final String RS_ADDITIONAL_COST_TYPE = "type";
    public static final String RS_ADDITIONAL_COST_NUMBER = "number";

    /**
     * 退补水费-调整表底清单
     */
    public static final String RS_METER_DATA_CHANGE_TABLE = "tb_rs_meter_data_change";
    public static final String RS_METER_DATA_CHANGE_CODE = "code";
    public static final String RS_METER_DATA_CHANGE_METER_CODE = "meter_code";
    public static final String RS_METER_DATA_CHANGE_VALUE = "value";
    public static final String RS_METER_DATA_CHANGE_OLD_VALUE = "old_value";




    /**
     * 工作任务主表
     */
    public static final String WORK_TASK_TABLE = "tb_work_task";
    public static final String WORK_TASK_CODE = "code";
    public static final String WORK_TASK_PROCESS_TYPE = "process_type";
    public static final String WORK_TASK_PROCESS_NAME = "process_name";
    public static final String WORK_TASK_ORG_ID = "org_id";
    public static final String WORK_TASK_NODE_NAME = "node_name";
    public static final String WORK_TASK_START_TIME = "start_time";
    public static final String WORK_TASK_END_TIME = "end_time";
    public static final String WORK_TASK_INSTANCE_CODE = "instance_code";
    public static final String WORK_TASK_INSTANCE_NAME = "instance_name";
    public static final String WORK_TASK_EXECUTOR = "executor";
    public static final String WORK_TASK_STATUS = "status";

    /**
     * 工作任务流转历史表
     */
    public static final String WORK_TASK_C_TABLE = "tb_work_task_c";
    public static final String WORK_TASK_C_CODE = "code";
    public static final String WORK_TASK_C_NODE_NAME = "node_name";
    public static final String WORK_TASK_C_START_TIME = "start_time";
    public static final String WORK_TASK_C_END_TIME = "end_time";
    public static final String WORK_TASK_C_EXECUTOR = "executor";
    public static final String WORK_TASK_C_ORDER_NUMBER = "order_number";
    public static final String WORK_TASK_C_STATUS = "status";
    public static final String WORK_TASK_C_REMARK = "remark";

    /**
     * 业扩报装数据表
     */
    public static final String EXPANDING_TABLE = "tb_expanding";
    public static final String EXPANDING_CODE = "code";
    public static final String EXPANDING_TYPE = "type";
    public static final String EXPANDING_START_TIME = "start_time";
    public static final String EXPANDING_END_TIME = "end_time";
    public static final String EXPANDING_CREATOR = "creator";
    public static final String EXPANDING_EXECUTOR = "executor";
    public static final String EXPANDING_REMARK = "remark";
    public static final String EXPANDING_STATUS = "status";


    /**
     * 业扩报装数据子表
     */
    public static final String EXPANDING_C_TABLE = "tb_expanding_c";
    public static final String EXPANDING_C_EXPANDING_CODE = "expanding_code";
    public static final String EXPANDING_C_CUST_CODE = "cust_code";
    public static final String EXPANDING_C_INSTANCE_CODE = "instance_code";

    /**
     *收费-票据
     */
    public static final String BILL_TABLE = "tb_bill";
    public static final String BILL_BILL_NO = "bill_no";
    public static final String BILL_STATUS = "status";
    public static final String BILL_KEEPER_USER = "keeper_user";
    public static final String BILL_IN_OPTION_ID = "in_option_id";
    public static final String BILL_OUT_OPTION_ID = "out_option_id";
    public static final String BILL_INVALID_OPTION_ID = "invalid_option_id";
    public static final String BILL_LOST_OPTION_ID = "lost_option_id";


    /**
     *收费-票据库
     */
    public static final String BILL_STORE_TABLE = "tb_bill_store";
    public static final String BILL_STORE_ORG_ID = "org_id";
    public static final String BILL_STORE_VERSION_ID = "version_id";
    public static final String BILL_STORE_START_NO = "start_no";
    public static final String BILL_STORE_END_NO = "end_no";
    public static final String BILL_STORE_TOTAL = "total";
    public static final String BILL_STORE_SURPLUS = "surplus";
    public static final String BILL_STORE_CURRENT_NO = "current_no";
    public static final String BILL_STORE_OPTION_TYPE = "option_type";

    /**
     *收费-票据版本
     */
    public static final String BILL_VERSION_TABLE = "tb_bill_version";
    public static final String BILL_VERSION_ORG_ID = "org_id";
    public static final String BILL_VERSION_CODE = "code";
    public static final String BILL_VERSION_TYPE = "type";

    /**
     *收费-票据-领取明细
     */
    public static final String BILL_RECEIVE_TABLE = "tb_bill_receive";
    public static final String BILL_RECEIVE_START_NO = "start_no";
    public static final String BILL_RECEIVE_END_NO = "end_no";
    public static final String BILL_RECEIVE_CURRENT_NO = "current_no";
    public static final String BILL_RECEIVE_SURPLUS = "surplus";
    public static final String BILL_RECEIV_RECEIVER = "receiver";
    public static final String BILL_RECEIVE_STORE_ID = "store_id";
    public static final String BILL_RECEIVE_TOTAL = "total";

    /**
     *收费-票据-领取明细
     */
    public static final String BILL_RECEIVE_DETAIL_TABLE = "tb_bill_receive_detail";
    public static final String BILL_RECEIVE_DETAIL_RECEIVE_ID = "receive_id";
    public static final String BILL_RECEIVE_DETAIL_USE_NO = "use_no";
    public static final String BILL_RECEIVE_DETAIL_NUM = "num";
    public static final String BILL_RECEIVE_DETAIL_TYPE = "type";

    /**
     *收费-大厅收费
     */
    public static final String PREPAY_TABLE = "tb_prepay";
    public static final String PREPAY_CUST_CODE = "cust_code";
    public static final String PREPAY_FEE_PAYMENT_METHOD = "fee_payment_method";
    public static final String PREPAY_BILL_TYPE = "bill_type";
    public static final String PREPAY_BILL_NO = "bill_no";
    public static final String PREPAY_PAYMENT = "payment";
    public static final String PREPAY_CHANGE = "change";
    public static final String PREPAY_OLD_BALANCE = "old_balance";
    public static final String PREPAY_IMPREST = "imprest";
    public static final String PREPAY_NEW_BALANCE = "new_balance";
    public static final String PREPAY_IS_ZERO_RECEIPT = "is_zero_receipt";
    public static final String PREPAY_IS_CHECK_BILL = "is_check_bill";
    public static final String PREPAY_TYPE = "type";
    public static final String PREPAY_CHARGE_YM = "charge_ym";
    public static final String PREPAY_PAY_TYPE = "pay_type";
    public static final String PREPAY_BILL_ID = "bill_id";
    public static final String PREPAY_ORDER_NO = "order_no";
    public static final String PREPAY_SEND_MONEY = "send_money";

    /**
     *收费-大厅收费
     */
    public static final String REFUND_TABLE = "tb_refund";
    public static final String REFUND_CUST_CODE = "cust_code";
    public static final String REFUND_AMOUNT = "amount";
    public static final String REFUND_FILE = "file";


    /**
     *收费-大厅收费
     */
    public static final String PREPAY_CORRECT_TABLE = "tb_prepay_correct";
    public static final String PREPAY_CORRECT_MAIN_ID = "main_id";

    /**
     *收费-大厅收费关联银行
     */
    public static final String PREPAY_BANK_INFO_TABLE = "tb_prepay_bank_info";
    public static final String PREPAY_BANK_INFO_MAIN_ID = "main_id";
    public static final String PREPAY_BANK_INFO_BANK_TYPE = "bank_type";
    public static final String PREPAY_BANK_INFO_NUMBER = "number";

    /**
     *档案管理-信息修改记录
     */
    public static final String INFO_EDIT_RECORDS_TABLE = "tb_info_edit_records";
    public static final String INFO_EDIT_RECORDS_CODE = "code";
    public static final String INFO_EDIT_RECORDS_CUST_CODE = "cust_code";
    public static final String INFO_EDIT_RECORDS_INSTANCE_CODE = "instance_code";
    public static final String INFO_EDIT_RECORDS_TYPE = "type";

    /**
     * 预存结转记录
     */
    public static final String CARRY_OVER_TABLE = "tb_carry_over";
    public static final String CARRY_OVER_RECEIVED_ID = "received_id";
    public static final String CARRY_OVER_STATUS = "status";

    /**
     * 分区挂接
     */
    public static final String PIPE_PARTITION_MOUNT_TABLE = "tb_pipe_partition_mount";
    public static final String PIPE_PARTITION_MOUNT_PARTITION_ID = "partition_id";
    public static final String PIPE_PARTITION_MOUNT_DEVICE_ID = "device_id";
    public static final String PIPE_PARTITION_MOUNT_DIRECTION = "direction";
    public static final String PIPE_PARTITION_MOUNT_TYPE= "type";
    public static final String PIPE_PARTITION_MOUNT_IS_ACCOUNT = "is_account";

    /**
     * 智慧管网-小流指标设置
     */
    public static final String PIPE_MIN_FLOW_CONFIG_TABLE = "tb_pipe_min_flow_config";
    public static final String PIPE_MIN_FLOW_CONFIG_PARTITION_ID = "partition_id";
    public static final String PIPE_MIN_FLOW_CONFIG_NIGHT_FLOW_MIN = "night_flow_min";
    public static final String PIPE_MIN_FLOW_CONFIG_NIGHT_FLOW_MAX = "night_flow_max";
    public static final String PIPE_MIN_FLOW_CONFIG_NIGHT_VALUE_MIN = "night_value_min";
    public static final String PIPE_MIN_FLOW_CONFIG_NIGHT_VALUE_MAX = "night_value_max";
    public static final String PIPE_MIN_FLOW_CONFIG_UNIT_PIPE_NIGHT_FLOW_MIN = "unit_pipe_night_flow_min";
    public static final String PIPE_MIN_FLOW_CONFIG_UNIT_PIPE_NIGHT_FLOW_MAX = "unit_pipe_night_flow_max";
    public static final String PIPE_MIN_FLOW_CONFIG_MNF_DIV_DAY_AVG_HOUR_FLOW_MIN = "mnf_div_day_avg_hour_flow_min";
    public static final String PIPE_MIN_FLOW_CONFIG_MNF_DIV_DAY_AVG_HOUR_FLOW_MAX = "mnf_div_day_avg_hour_flow_max";
    public static final String PIPE_MIN_FLOW_CONFIG_COLLECT_RATE = "collect_rate";
    public static final String PIPE_MIN_FLOW_CONFIG_STOCK_TYPE = "stock_type";
    public static final String PIPE_MIN_FLOW_CONFIG_INCR_TIME = "incr_time";
    public static final String PIPE_MIN_FLOW_CONFIG_INCR_BASE = "incr_base";
    public static final String PIPE_MIN_FLOW_CONFIG_INCR_WARN = "incr_warn";
    public static final String PIPE_MIN_FLOW_CONFIG_INCR_ERROR = "incr_error";
    public static final String PIPE_MIN_FLOW_CONFIG_INCR_TYPE = "incr_type";

    /**
     * 智慧管网-核算周期配置
     */
    public static final String PIPE_ACCOUNT_CYCLE_CONFIG_TABLE = "tb_pipe_account_cycle_config";
    public static final String PIPE_ACCOUNT_CYCLE_CONFIG_PARTITION_ID = "partition_id";
    public static final String PIPE_ACCOUNT_CYCLE_CONFIG_START_TIME = "start_time";
    public static final String PIPE_ACCOUNT_CYCLE_CONFIG_END_TIME = "end_time";
    public static final String PIPE_ACCOUNT_CYCLE_CONFIG_MONTH = "month";
    public static final String PIPE_ACCOUNT_CYCLE_CONFIG_IS_DEFAULT = "is_default";

    /**
     * 智慧管网-营收用户管理
     */
    public static final String PIPE_PARTITION_CUST_TABLE = "tb_pipe_partition_cust";
    public static final String PIPE_PARTITION_CUST_PARTITION_ID = "partition_id";
    public static final String PIPE_PARTITION_CUST_CUST_CODE = "cust_code";
    public static final String PIPE_PARTITION_CUST_BUSINESS_HALL = "business_hall";
    public static final String PIPE_PARTITION_CUST_COPY_METER_USER = "copy_meter_user";

    /**
     * 智慧管网-分区流量
     */
    public static final String PIPE_PARTITION_TOTAL_FLOW_TABLE = "tb_pipe_partition_total_flow";
    public static final String PIPE_PARTITION_TOTAL_FLOW_DEVICE_ID = "device_id";
    public static final String PIPE_PARTITION_TOTAL_FLOW_VALUE = "value";
    public static final String PIPE_PARTITION_TOTAL_ORIGIN_VALUE = "origin_water";
    public static final String PIPE_PARTITION_TOTAL_FLOW_COLLECT_TIME = "collect_time";
    public static final String PIPE_PARTITION_TOTAL_FLOW_TYPE = "type";
    public static final String PIPE_PARTITION_TOTAL_FLOW_CORRECT_WATER = "correct_water";

    /**
     * 智慧管网-水量填报
     */
    public static final String PIPE_USE_WATER_REPORT_TABLE = "tb_pipe_use_water_report";
    public static final String PIPE_USE_WATER_REPORT_PARTITION_ID = "partition_id";
    public static final String PIPE_USE_WATER_REPORT_UPLOAD_NAME = "upload_name";
    public static final String PIPE_USE_WATER_REPORT_YM = "ym";
    public static final String PIPE_USE_WATER_REPORT_OWN_SUPPLY_WATER = "own_supply_water";
    public static final String PIPE_USE_WATER_REPORT_BUY_SUPPLY_WATER = "buy_supply_water";
    public static final String PIPE_USE_WATER_REPORT_BATCH_SALE_WATER = "batch_sale_water";
    public static final String PIPE_USE_WATER_REPORT_SUPPLY_TOTAL_WATER = "supply_total_water";
    public static final String PIPE_USE_WATER_REPORT_FEE_METERING_USE_WATER = "fee_metering_use_water";
    public static final String PIPE_USE_WATER_REPORT_HUANWEI_USE_WATER = "huanwei_use_water";
    public static final String PIPE_USE_WATER_REPORT_LVHUA_USE_WATER = "lvhua_use_water";
    public static final String PIPE_USE_WATER_REPORT_PIPE_USE_WATER = "pipe_use_water";
    public static final String PIPE_USE_WATER_REPORT_SUNHUAI_USE_WATER = "sunhuai_use_water";
    public static final String PIPE_USE_WATER_REPORT_DINGLIANG_USE_WATER = "dingliang_use_water";
    public static final String PIPE_USE_WATER_REPORT_FEE_NO_METERING_USE_WATER = "fee_no_metering_use_water";
    public static final String PIPE_USE_WATER_REPORT_BANGONG_USE_WATER = "bangong_use_water";
    public static final String PIPE_USE_WATER_REPORT_XIAOFANG_USE_WATER = "xiaofang_use_water";
    public static final String PIPE_USE_WATER_REPORT_JIANMIAN_USE_WATER = "jianmian_use_water";
    public static final String PIPE_USE_WATER_REPORT_FREE_METERING_USE_WATER = "free_metering_use_water";
    public static final String PIPE_USE_WATER_REPORT_FREE_XIAOFANG_USE_WATER = "free_xiaofang_use_water";
    public static final String PIPE_USE_WATER_REPORT_FREE_WEIHU_USE_WATER = "free_weihu_use_water";
    public static final String PIPE_USE_WATER_REPORT_FREE_BANGONG_USE_WATER = "free_bangong_use_water";
    public static final String PIPE_USE_WATER_REPORT_FREE_CHONGXI_USE_WATER = "free_chongxi_use_water";
    public static final String PIPE_USE_WATER_REPORT_FREE_NO_METERING_USE_WATER = "free_no_metering_use_water";
    public static final String PIPE_USE_WATER_REPORT_USE_TOTAL_WATER = "use_total_water";
    public static final String PIPE_USE_WATER_REPORT_FRONT_POINT_LEAK_WATER = "front_point_leak_water";
    public static final String PIPE_USE_WATER_REPORT_FRONT_PIPE_LEAK_WATER = "front_pipe_leak_water";
    public static final String PIPE_USE_WATER_REPORT_FRONT_LEAK_TOTAL_WATER = "front_leak_total_water";
    public static final String PIPE_USE_WATER_REPORT_BACKEND_CHECKED_LEAK_WATER = "backend_checked_leak_water";
    public static final String PIPE_USE_WATER_REPORT_BACKEND_NO_CHECKED_WATER = "backend_no_checked_water";
    public static final String PIPE_USE_WATER_REPORT_BACKEND_LEAK_TOTAL_WATER = "backend_leak_total_water";
    public static final String PIPE_USE_WATER_REPORT_BACKGROUND_LEAK_WATER = "background_leak_water";
    public static final String PIPE_USE_WATER_REPORT_SHUIXIANG_LEAK_WATER = "shuixiang_leak_water";
    public static final String PIPE_USE_WATER_REPORT_LEAK_TOTAL_WATER = "leak_total_water";
    public static final String PIPE_USE_WATER_REPORT_CUST_MISTAKE_LOSS_WATER = "cust_mistake_loss_water";
    public static final String PIPE_USE_WATER_REPORT_NON_CUST_MISTAKE_LOSS_WATER = "non_cust_mistake_loss_water";
    public static final String PIPE_USE_WATER_REPORT_MISTAKE_LOSS_TOTAL_WATER = "mistake_loss_total_water";
    public static final String PIPE_USE_WATER_REPORT_TOUDAO_LOSS_WATER = "toudao_loss_water";
    public static final String PIPE_USE_WATER_REPORT_COPY_METER_LOSS_WATER = "copy_meter_loss_water";
    public static final String PIPE_USE_WATER_REPORT_OTHER_LOSS_WATER = "other_loss_water";
    public static final String PIPE_USE_WATER_REPORT_NO_REGISTER_LOSS_WATER = "no_register_loss_water";
    public static final String PIPE_USE_WATER_REPORT_PIPE_LOSS_WATER = "pipe_loss_water";
    public static final String PIPE_USE_WATER_REPORT_OTHER_LOSS_TOTAL_WATER = "other_loss_total_water";
    public static final String PIPE_USE_WATER_REPORT_LOSS_TOTAL_WATER = "loss_total_water";
    public static final String PIPE_USE_WATER_REPORT_DN75_PIPE_LENGTH = "dn75_pipe_length";
    public static final String PIPE_USE_WATER_REPORT_UNIT_SUPPLY_PIPE_LENGTH = "unit_supply_pipe_length";
    public static final String PIPE_USE_WATER_REPORT_YEAR_AVG_PRESSURE = "year_avg_pressure";
    public static final String PIPE_USE_WATER_REPORT_CUST_COPIED_WATER = "cust_copied_water";
    public static final String PIPE_USE_WATER_REPORT_MAX_FROZEN_SOIL_DEPTH = "max_frozen_soil_depth";

    /**
     * 智慧管网-分区漏点记录
     */
    public static final String PIPE_WATER_FACTORY_MONITOR_POINT_TABLE = "tb_pipe_water_factory_monitor_point";
    public static final String PIPE_WATER_FACTORY_MONITOR_POINT_FACTORY_NAME = "factory_name";
    public static final String PIPE_WATER_FACTORY_MONITOR_POINT_ORDER_NUM = "order_num";
    public static final String PIPE_WATER_FACTORY_MONITOR_POINT_DEVICE_ID = "device_id";
    public static final String PIPE_WATER_FACTORY_MONITOR_POINT_DIRECTION = "direction";

    /**
     * 智慧管网-水厂监测点配置
     */
    public static final String PIPE_PARTITION_LOSS_POINT_TABLE = "tb_pipe_partition_loss_point";
    public static final String PIPE_PARTITION_LOSS_POINT_PARTITION_ID = "partition_id";
    public static final String PIPE_PARTITION_LOSS_POINT_FIND_DATE = "find_date";
    public static final String PIPE_PARTITION_LOSS_POINT_NUM = "num";

    /**
     * 智慧管网-分区流量表信息
     */
    public static final String PIPE_PARTITION_FLOW_METER_TABLE = "tb_pipe_partition_flow_meter";
    public static final String PIPE_PARTITION_FLOW_METER_PARTITION_ID = "partition_id";
    public static final String PIPE_PARTITION_FLOW_METER_TYPE = "type";
    public static final String PIPE_PARTITION_FLOW_METER_CODE = "code";
    public static final String PIPE_PARTITION_FLOW_METER_POSITION = "position";
    public static final String PIPE_PARTITION_FLOW_METER_METER_TYPE = "meter_type";
    public static final String PIPE_PARTITION_FLOW_METER_BRAND = "brand";
    public static final String PIPE_PARTITION_FLOW_METER_CALIBER = "caliber";
    public static final String PIPE_PARTITION_FLOW_METER_PIPE = "pipe";
    public static final String PIPE_PARTITION_FLOW_METER_IS_REMOTE = "is_remote";
    public static final String PIPE_PARTITION_FLOW_METER_YEAR = "year";
    public static final String PIPE_PARTITION_FLOW_METER_REMARK = "remark";
    public static final String PIPE_PARTITION_FLOW_METER_IMG = "img";

    /**
     * 智慧管网-分区户表信息
     */
    public static final String PIPE_PARTITION_CUST_METER_TABLE = "tb_pipe_partition_cust_meter";
    public static final String PIPE_PARTITION_CUST_METER_PARTITION_ID = "partition_id";
    public static final String PIPE_PARTITION_CUST_METER_CALIBER = "caliber";
    public static final String PIPE_PARTITION_CUST_METER_NUM = "num";
    public static final String PIPE_PARTITION_CUST_METER_BRAND = "brand";
    public static final String PIPE_PARTITION_CUST_METER_TYPE = "type";
    public static final String PIPE_PARTITION_CUST_METER_IS_COLLECT_COPY = "is_collect_copy";
    public static final String PIPE_PARTITION_CUST_METER_REMARK = "remark";
    public static final String PIPE_PARTITION_CUST_METER_IMG = "img";

    /**
     *智慧管网-分区管网信息
     */
    public static final String PIPE_PARTITION_PIPE_TABLE = "tb_pipe_partition_pipe";
    public static final String PIPE_PARTITION_PIPE_PARTITION_ID = "partition_id";
    public static final String PIPE_PARTITION_PIPE_MAIN_PIPE = "main_pipe";
    public static final String PIPE_PARTITION_PIPE_PIPE = "pipe";
    public static final String PIPE_PARTITION_PIPE_LENGTH = "length";
    public static final String PIPE_PARTITION_PIPE_YEAR = "year";
    public static final String PIPE_PARTITION_PIPE_REMARK = "remark";
    public static final String PIPE_PARTITION_PIPE_FILE = "file";

    /**
     *智慧管网-分区二供泵房
     */
    public static final String PIPE_PARTITION_PUMP_HOUSE_TABLE = "tb_pipe_partition_pump_house";
    public static final String PIPE_PARTITION_PUMP_HOUSE_PARTITION_ID = "partition_id";
    public static final String PIPE_PARTITION_PUMP_HOUSE_TYPE = "type";
    public static final String PIPE_PARTITION_PUMP_HOUSE_POSITION = "position";
    public static final String PIPE_PARTITION_PUMP_HOUSE_MODE = "mode";
    public static final String PIPE_PARTITION_PUMP_HOUSE_BRAND = "brand";
    public static final String PIPE_PARTITION_PUMP_HOUSE_IN_WATER_METERING = "in_water_metering";
    public static final String PIPE_PARTITION_PUMP_HOUSE_OUT_WATER_METERING = "out_water_metering";
    public static final String PIPE_PARTITION_PUMP_HOUSE_CALIBER = "caliber";
    public static final String PIPE_PARTITION_PUMP_HOUSE_REMARK = "remark";
    public static final String PIPE_PARTITION_PUMP_HOUSE_IMG = "img";

    /**
     *智慧管网-分区阀门信息
     */
    public static final String PIPE_PARTITION_VALVE_TABLE = "tb_pipe_partition_valve";
    public static final String PIPE_PARTITION_VALVE_PARTITION_ID = "partition_id";
    public static final String PIPE_PARTITION_VALVE_TYPE = "type";
    public static final String PIPE_PARTITION_VALVE_CODE = "code";
    public static final String PIPE_PARTITION_VALVE_CALIBER = "caliber";
    public static final String PIPE_PARTITION_VALVE_VALVE_TYPE = "valve_type";
    public static final String PIPE_PARTITION_VALVE_ADDRESS = "address";
    public static final String PIPE_PARTITION_VALVE_RUN_STATUS = "run_status";
    public static final String PIPE_PARTITION_VALVE_REMARK = "remark";
    public static final String PIPE_PARTITION_VALVE_IMG = "img";

    /**
     *智慧管网-分区消火栓信息
     */
    public static final String PIPE_PARTITION_FIRE_HYDRANT_TABLE = "tb_pipe_partition_fire_hydrant";
    public static final String PIPE_PARTITION_FIRE_HYDRANT_PARTITION_ID = "partition_id";
    public static final String PIPE_PARTITION_FIRE_HYDRANT_CODE = "code";
    public static final String PIPE_PARTITION_FIRE_HYDRANT_ADDRESS = "address";
    public static final String PIPE_PARTITION_FIRE_HYDRANT_TYPE = "type";
    public static final String PIPE_PARTITION_FIRE_HYDRANT_RUN_STATUS = "run_status";
    public static final String PIPE_PARTITION_FIRE_HYDRANT_REMARK = "remark";
    public static final String PIPE_PARTITION_FIRE_HYDRANT_IMG = "img";

    /**
     *智慧管网-分区维护记录
     */
    public static final String PIPE_PARTITION_MAINTAIN_RECORDS_TABLE = "tb_pipe_partition_maintain_records";
    public static final String PIPE_PARTITION_MAINTAIN_RECORDS_PARTITION_ID = "partition_id";
    public static final String PIPE_PARTITION_MAINTAIN_RECORDS_MAINTAIN_DATE = "maintain_date";
    public static final String PIPE_PARTITION_MAINTAIN_RECORDS_REMARK = "remark";
    public static final String PIPE_PARTITION_MAINTAIN_RECORDS_IMG = "img";

    /**
     *智慧管网-漏损工单
     */
    public static final String PIPE_WORK_ORDER_TABLE = "tb_pipe_work_order";
    public static final String PIPE_WORK_ORDER_WORK_ORDER_ID = "work_order_id";
    public static final String PIPE_WORK_ORDER_PARTITION_ID = "partition_id";

    /**
     *报表平台-报表类型管理
     */
    public static final String REPORT_TYPE_TABLE = "tb_report_type";
    public static final String REPORT_TYPE_PID = "pid";
    public static final String REPORT_TYPE_NAME = "name";
    public static final String REPORT_TYPE_ORDER_NUM = "order_num";

    /**
     *报表平台-数据库管理
     */
    public static final String REPORT_DATABASE_TABLE = "tb_report_database";
    public static final String REPORT_DATABASE_NAME = "name";
    public static final String REPORT_DATABASE_TYPE = "type";
    public static final String REPORT_DATABASE_HOST = "host";
    public static final String REPORT_DATABASE_PORT = "port";
    public static final String REPORT_DATABASE_SERVER = "server";
    public static final String REPORT_DATABASE_USERNAME = "username";
    public static final String REPORT_DATABASE_PASSWORD = "password";

    /**
     *报表平台-查询组件管理
     */
    public static final String REPORT_COMPONENT_TABLE = "tb_report_component";
    public static final String REPORT_COMPONENT_CONTENT = "content";

    /**
     *报表平台-报表管理
     */
    public static final String REPORT_TABLE = "tb_report";
    public static final String REPORT_TYPE_ID = "type_id";
    public static final String REPORT_NAME = "name";
    public static final String REPORT_CONFIG = "config";
    public static final String REPORT_STATUS = "status";

    /**
     *报表平台-查询组件管理
     */
    public static final String REPORT_QUERY_TABLE = "tb_report_query";
    public static final String REPORT_QUERY_REPORT_ID = "report_id";
    public static final String REPORT_QUERY_PID = "pid";
    public static final String REPORT_QUERY_DATABASE_ID = "database_id";
    public static final String REPORT_QUERY_CONTENT = "content";

    /**
     *报表平台-报表表格配置
     */
    public static final String REPORT_TABLE_TABLE = "tb_report_table";
    public static final String REPORT_TABLE_PID = "pid";
    public static final String REPORT_TABLE_CONTENT = "content";


    /**
     *微信缴费历史状态
     */
    public static final String WECHAT_PAY_HIS_TABLE = "tb_wechat_pay_his";
    public static final String WECHAT_PAY_HIS_ORDER_NO = "order_no";
    public static final String WECHAT_PAY_HIS_CUST_CODE = "cust_code";
    public static final String WECHAT_PAY_HIS_MONEY = "money";
    public static final String WECHAT_PAY_HIS_STATUS = "status";


    /**
     *知识库文档
     */
    public static final String SERVICE_KNOWLEDGE_DOCUMENT_TABLE = "tb_service_knowledge_document";
    public static final String SERVICE_KNOWLEDGE_DOCUMENT_NAME = "name";
    public static final String SERVICE_KNOWLEDGE_DOCUMENT_TYPE_ID = "type_id";
    public static final String SERVICE_KNOWLEDGE_DOCUMENT_URL = "url";

}
