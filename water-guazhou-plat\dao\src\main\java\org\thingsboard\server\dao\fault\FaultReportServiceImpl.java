package org.thingsboard.server.dao.fault;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.model.sql.fault.FaultReport;
import org.thingsboard.server.dao.model.sql.fault.FaultReportC;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderDetail;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.fault.FaultReportCMapper;
import org.thingsboard.server.dao.sql.fault.FaultReportMapper;
import org.thingsboard.server.dao.sql.workOrder.NewlyWorkOrderMapper;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderDetailMapper;
import org.thingsboard.server.dao.util.RedisUtil;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderAssignRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class FaultReportServiceImpl implements FaultReportService {

    @Autowired
    private FaultReportMapper faultReportMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private WorkOrderDetailMapper stageMapper;

    @Autowired
    private NewlyWorkOrderMapper newlyWorkOrderMapper;

    @Autowired
    private FaultReportCMapper faultReportCMapper;


    @Override
    @Transactional
    public FaultReport save(FaultReport faultReport) {
        if (StringUtils.isBlank(faultReport.getId())) {
            // 生成工单
            WorkOrder workOrder = faultReport.getWorkOrder();
            workOrder.setCreateTime(new Date());
            workOrder.setStatus(WorkOrderStatus.PENDING);
            if (StringUtils.isBlank(workOrder.getType())) {
                workOrder.setType("维修");
            }
            faultReport.setFaultAddress(faultReport.getWorkOrder().getAddress());
            faultReport.getWorkOrder().setTitle(faultReport.getTitle());
            faultReport.getWorkOrder().setUploadUserId(faultReport.getCreator());
            faultReport.getWorkOrder().setStepProcessUserId(faultReport.getWorkOrder().getProcessUserId());
            workOrder.setOrganizerId(faultReport.getCreator());
            workOrder.setTenantId(faultReport.getTenantId());
            workOrder.setSerialNo(RedisUtil.nextId(DataConstants.REDIS_KEY.WORK_ORDER, ""));
            newlyWorkOrderMapper.insert(workOrder);
            WorkOrderAssignRequest workOrderAssignRequest = new WorkOrderAssignRequest();

            workOrderAssignRequest.setProcessLevel(workOrder.getProcessLevel());
            workOrderAssignRequest.setProcessLevel(workOrder.getProcessLevel());
            workOrderAssignRequest.setStepProcessUserId(workOrder.getStepProcessUserId());
            WorkOrderDetail detail = workOrderAssignRequest.process(workOrder);
            newlyWorkOrderMapper.updateById(workOrder);
            stageMapper.insert(detail);

            faultReport.setCreateTime(new Date());
            faultReport.setWorkOrderId(workOrder.getId());
            faultReportMapper.insert(faultReport);

        } else {
            faultReportMapper.updateById(faultReport);
        }

        Map deleteMap = new HashMap<>();
        deleteMap.put("main_id", faultReport.getId());
        faultReportCMapper.deleteByMap(deleteMap);

        if (faultReport.getFaultReportCList() != null) {
            for (FaultReportC faultReportC : faultReport.getFaultReportCList()) {
                faultReportC.setMainId(faultReport.getId());
                faultReportC.setTenantId(faultReport.getTenantId());
                faultReportC.setCreateTime(new Date());

                faultReportCMapper.insert(faultReportC);
            }
        }

        return faultReport;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        faultReportMapper.deleteBatchIds(ids);
        return IstarResponse.ok("删除成功");
    }

    @Override
    public List<FaultReportC> getDeviceList(String workOrderId) {
        // 故障上报
        FaultReport faultReport = faultReportMapper.getByWorkOrderId(workOrderId);
        if (faultReport == null) {
            return new ArrayList<>();
        }
        // 获取任务子表
        List<FaultReportC> faultReportCList = faultReportCMapper.getList(faultReport.getId());

        // 所属分类链表
        for (FaultReportC maintainTaskC : faultReportCList) {
            this.setType(maintainTaskC);
        }

        return faultReportCList;
    }

    /**
     * 故障工单开始时间
     * @param id
     */
    @Override
    public void isFault(String id) {
        try {
            FaultReport faultReport = faultReportMapper.getByWorkOrderId(id);
            if (faultReport != null) {
                faultReport.setStartTime(new Date());
                faultReportMapper.updateById(faultReport);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public FaultReport getByWorkOrderId(String id) {
        QueryWrapper<FaultReport> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("work_order_id", id);

        return faultReportMapper.selectOne(queryWrapper);
    }

    @Override
    public Map statistics(String deviceLabelCode) {
        Map result = new HashMap();
        // 故障次数
        int count = faultReportCMapper.countByLabelCode(deviceLabelCode);
        result.put("count", count);
        // 最近维修
        result.put("latestRepairTime", "-");
        FaultReportC faultReportC = faultReportCMapper.selectFirstByDeviceLabelCode(deviceLabelCode);
        if (faultReportC != null) {
            result.put("latestRepairTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(faultReportC.getCreateTime()));
        }

        // 故障等级占比
        List<Map> gradeCount = faultReportCMapper.getGradeCountByDeviceLabelCode(deviceLabelCode);
        result.put("gradeCount", gradeCount);

        // 今年维修情况
        Date nowYear = new Date();
        SimpleDateFormat yyyyFormat = new SimpleDateFormat("yyyy");
        try {
            nowYear = yyyyFormat.parse(yyyyFormat.format(new Date()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<Map> nowYearRepair = faultReportCMapper.getNowYearRepairByDeviceLabelCode(deviceLabelCode, nowYear);
        result.put("nowYearRepair", nowYearRepair);

        return result;
    }

    @Override
    public PageData<Map> getRepairList(String deviceLabelCode, int page, int size) {
        List<Map> repairList = faultReportCMapper.getRepairList(deviceLabelCode, page, size);
        int total = faultReportCMapper.getRepairListCount(deviceLabelCode);

        return new PageData<>(total, repairList);
    }

    // 设备类型树
    public void getAllDeviceTypeId(List<String> input, List<String> output) {
        QueryWrapper<DeviceType> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("parent_id", input);
        List<DeviceType> list = deviceTypeMapper.selectList(queryWrapper);
        if (list.size() > 0) {
            List<String> ids = list.stream().map(a -> a.getId()).collect(Collectors.toList());
            output.addAll(list.stream().map(a -> a.getSerialId()).collect(Collectors.toList()));
            this.getAllDeviceTypeId(ids, output);
        }
    }

    private void setType(FaultReportC faultReportC) {
        if (StringUtils.isBlank(faultReportC.getTypeId())) {
            faultReportC.setLinkedType("-");
            return;
        }
        String linkedType = "-";
        String topType = "-";
        DeviceType deviceType = null;
        String parentId = faultReportC.getTypeId();
        for (int i = 0; i < 5; i++) {
            deviceType = deviceTypeMapper.selectById(parentId);
            if (deviceType == null) {
                break;
            }
            linkedType = deviceType.getName() + ">" + linkedType;
            if (StringUtils.isBlank(deviceType.getParentId())) {
                topType = deviceType.getName();
                break;
            }
            parentId = deviceType.getParentId();
        }
        if (linkedType.length() >= 2) {
            linkedType = linkedType.substring(0, linkedType.length() - 2);
        }

        faultReportC.setLinkedType(linkedType);
        faultReportC.setTopType(topType);
        faultReportC.setType(linkedType);
        try {
            if (linkedType.contains(">")) {
                faultReportC.setType(linkedType.substring(linkedType.lastIndexOf(">") + 1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
