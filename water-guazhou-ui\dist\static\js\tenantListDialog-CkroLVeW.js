import{C as m,M as u,aM as p,D as b,aN as C,g as _,h,F as a,p as L,q as i,G as r,J as T,L as k}from"./index-r0dFAfgr.js";import{_ as v}from"./CardTable-rdWOL4_6.js";import"./index-C9hz-UZb.js";const{$message:n,$confirm:x}=u(),y={name:"CharacterDialog",props:["tableConfig"],data(){return{roleId:"",roleList:[],cardTableConfig:{loading:!1,dataList:[],selectList:[],handleSelectChange:e=>{this.cardTableConfig.selectList=e},columns:[{prop:"title",label:"项目"},{prop:"managerUsername",label:"当前项目拥有人"}]}}},computed:{visible(){return this.tableConfig.visible}},created(){this.getTenantList()},methods:{clickSave(){x("确定要赋予企业吗?","删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{(await p({toUserId:b(row.id.id),tenantIdList:this.cardTableConfig.selectList.map(t=>t.id)})).status===200&&(n.success("赋予成功"),this.tableConfig.close())}catch(e){n.error(e.data.message)}}).catch(()=>{})},async getTenantList(){try{const e=await C(this.tableConfig.userId);console.log(e),e.status===200&&(this.cardTableConfig.dataList=e.data.tenantList,this.initSelect(e.currentUserList))}catch(e){n.error(e.data.message),this.tableConfig.close()}},initSelect(e){this.cardTableConfig.selectList=this.cardTableConfig.dataList.filter((t,s)=>t.id===e[s])}}},w={class:"dialog-footer"};function B(e,t,s,S,c,o){const d=v,l=T,f=k;return _(),h(f,{modelValue:o.visible,"onUpdate:modelValue":t[0]||(t[0]=g=>o.visible=g),title:"企业赋予",width:"30%",class:"alarm-design","close-on-click-modal":!1},{footer:a(()=>[L("span",w,[i(l,{type:"primary",onClick:o.clickSave},{default:a(()=>t[1]||(t[1]=[r("项目赋予")])),_:1},8,["onClick"]),i(l,{onClick:s.tableConfig.close},{default:a(()=>t[2]||(t[2]=[r("取 消")])),_:1},8,["onClick"])])]),default:a(()=>[i(d,{config:c.cardTableConfig},null,8,["config"])]),_:1},8,["modelValue"])}const I=m(y,[["render",B]]);export{I as default};
