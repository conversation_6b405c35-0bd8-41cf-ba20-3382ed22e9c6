<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalVideoMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalVideo">
        select *
        from ss_portal_video a
        <where>
            a.tenant_id = #{param.tenantId}
            <if test="param.title != null">
                and a.title like '%'||#{param.title}||'%'
            </if>
            <if test="param.vformat != null">
                and a.vformat like '%'||#{param.vformat}||'%'
            </if>
            <if test="param.isIntroduce != null">
                and a.is_introduce like '%'||#{param.isIntroduce}||'%'
            </if>
            <if test="param.isPublicize != null">
                and a.is_publicize like '%'||#{param.isPublicize}||'%'
            </if>
        </where>
        order by a.order_num, a.create_time desc
    </select>
</mapper>