package org.thingsboard.server.dao.pumpHouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpManage;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpManagePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpManageSaveRequest;

import java.util.List;

public interface PumpManageService {
    /**
     * 分页条件查询泵房管理相关信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<PumpManage> findAllConditional(PumpManagePageRequest request);

    /**
     * 保存泵房管理相关信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    PumpManage save(PumpManageSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(PumpManage entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量保存
     *
     * @param entities 泵房管理信息列表
     * @return 保存好的数据
     */
    List<PumpManage> saveAll(List<PumpManageSaveRequest> entities);

}
