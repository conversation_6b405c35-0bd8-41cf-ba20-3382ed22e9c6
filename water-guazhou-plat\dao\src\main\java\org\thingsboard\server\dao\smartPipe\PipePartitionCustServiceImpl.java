package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.sql.revenue.CustInfo;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionCust;
import org.thingsboard.server.dao.revenue.CustInfoService;
import org.thingsboard.server.dao.sql.smartPipe.PipePartitionCustMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class PipePartitionCustServiceImpl implements PipePartitionCustService {

    @Autowired
    private PipePartitionCustMapper pipePartitionCustMapper;

    @Autowired
    private CustInfoService custInfoService;


    @Override
    public PageData<PipePartitionCust> getList(PartitionCustRequest partitionCustRequest) {
        Page<PipePartitionCust> custPage = new Page<>(partitionCustRequest.getPage(), partitionCustRequest.getSize());
        IPage<PipePartitionCust> pipePartitionCustIPage = pipePartitionCustMapper.getList(custPage, partitionCustRequest);
        return new PageData<>(pipePartitionCustIPage.getTotal(), pipePartitionCustIPage.getRecords());
    }

    @Override
    public PipePartitionCust save(PipePartitionCust pipePartitionCust) {
        if (StringUtils.isBlank(pipePartitionCust.getId())) {
            pipePartitionCust.setCreateTime(new Date());
            pipePartitionCustMapper.insert(pipePartitionCust);
        } else {
            pipePartitionCustMapper.updateById(pipePartitionCust);
        }
        return pipePartitionCust;
    }

    @Override
    @Transactional
    public IstarResponse importSave(MultipartFile file, String tenantId) {
        String fileName = file.getOriginalFilename();
        if ((!fileName.startsWith("分区用户挂接模板") && !fileName.startsWith("导入模板")) || !fileName.endsWith("xlsx")) {
            return IstarResponse.error("上传文件错误");
        }
        XSSFWorkbook workbook;
        try {
            workbook = new XSSFWorkbook(file.getInputStream());
        } catch (IOException e) {
            return IstarResponse.error("上传文件错误");
        }
        int i = 1;
        Row row;
        Sheet sheet = workbook.getSheetAt(0);
        PipePartitionCust pipePartitionCust;
        String errorMsg = "";
        List<PipePartitionCust> pipePartitionCustList;
        Map<String, Integer> indexMap = new LinkedHashMap<>();
        Map<String, PipePartitionCust> partitionCustMap = new HashMap<>();
        for (; i < sheet.getLastRowNum() + 1; i++) {
            row = sheet.getRow(i);
            for (int j = 0; j < 6; j++) {
                if (row.getCell(j) == null) {
                    row.createCell(j);
                }
                row.getCell(j).setCellType(CellType.STRING);
            }
            if (StringUtils.isBlank(row.getCell(0).getStringCellValue()) && StringUtils.isBlank(row.getCell(1).getStringCellValue())) {
                // 数据结束
                break;
            }
            try {
                indexMap.put(row.getCell(2).getStringCellValue(), i);
                pipePartitionCust = new PipePartitionCust();
                pipePartitionCust.setPartitionId(row.getCell(1).getStringCellValue());
                pipePartitionCust.setCustCode(row.getCell(2).getStringCellValue());
                pipePartitionCust.setCopyMeterUser(row.getCell(4).getStringCellValue());
                pipePartitionCust.setBusinessHall(row.getCell(5).getStringCellValue());
                pipePartitionCust.setTenantId(tenantId);
                pipePartitionCust.setCreateTime(new Date());
                partitionCustMap.put(pipePartitionCust.getCustCode(), pipePartitionCust);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        // 查找用户是否存在
        List<String> custCodeList = indexMap.keySet().stream().collect(Collectors.toList());
        if (custCodeList.size() > 0) {
            List<String> containCodeList = custInfoService.findByUserCodeList(custCodeList, new TenantId(UUIDConverter.fromString(tenantId))).stream().map(a -> a.getCode()).collect(Collectors.toList());

            for (String custCode : custCodeList) {
                if (!containCodeList.contains(custCode)) {
                    errorMsg = errorMsg + indexMap.get(custCode) + ",";
                    // 移除数据
                    partitionCustMap.remove(custCode);
                }
            }
            if (StringUtils.isNotBlank(errorMsg)) {
                errorMsg = "第" + errorMsg.substring(0, errorMsg.length() - 1) + "条用户不存在,第";
            }

            // 是否已添加
            QueryWrapper<PipePartitionCust> pipePartitionCustQueryWrapper = new QueryWrapper<>();
            pipePartitionCustQueryWrapper.in("cust_code", custCodeList).eq("tenant_id", tenantId);
            containCodeList = pipePartitionCustMapper.selectList(pipePartitionCustQueryWrapper).stream().map(a -> a.getCustCode()).collect(Collectors.toList());
            boolean hasAdd = false;
            for (String custCode : custCodeList) {
                if (containCodeList.contains(custCode)) {
                    hasAdd = true;
                    errorMsg = errorMsg +  indexMap.get(custCode) + ",";
                    // 移除数据
                    partitionCustMap.remove(custCode);
                }
            }
            if (hasAdd) {
                if (StringUtils.isNotBlank(errorMsg) && errorMsg.endsWith(",")) {
                    errorMsg = errorMsg.substring(0, errorMsg.length() - 1) + "条用户已添加";
                }
            }
            if (errorMsg.endsWith(",第")) {
                errorMsg = errorMsg.substring(0, errorMsg.length() - 2);
            }

        }
        pipePartitionCustList = partitionCustMap.values().stream().collect(Collectors.toList());
        if (pipePartitionCustList.size() > 0) {
            int maxNum = pipePartitionCustList.size() / 1000 + 1;
            for (int j = 0; j < maxNum; j++) {
                pipePartitionCustMapper.batchSave(pipePartitionCustList.stream().skip(j * 1000).limit(1000).collect(Collectors.toList()));
            }
        }

        return IstarResponse.ok("导入成功" + pipePartitionCustList.size() + "条,失败" + (sheet.getLastRowNum() - pipePartitionCustList.size()) + "条," + errorMsg);
    }

    @Override
    public String check(PipePartitionCust pipePartitionCust) {
        CustInfo custInfo = custInfoService.findOneByCodeAndTenantId(pipePartitionCust.getCustCode(), pipePartitionCust.getTenantId());
        if (custInfo == null) {
            return "该用户不存在";
        }
        QueryWrapper<PipePartitionCust> pipePartitionCustQueryWrapper = new QueryWrapper<>();
        List<PipePartitionCust> pipePartitionCustList = pipePartitionCustMapper.selectList(pipePartitionCustQueryWrapper);

        if (pipePartitionCustList.size() > 0 && pipePartitionCustList.get(0).getId().equals(pipePartitionCust.getId())) {
            return "该用户已被挂接";
        }
        return "";
    }

    @Override
    public String batchSave(List<PipePartitionCust> pipePartitionCustList, String tenantId) {
        // 检查是否重复添加
        if (pipePartitionCustList.size() == 0) {
            return "请选择用户";
        }
        List<String> idList = pipePartitionCustList.stream().map(a -> a.getId()).collect(Collectors.toList());
        QueryWrapper<PipePartitionCust> pipePartitionCustQueryWrapper = new QueryWrapper<>();
        pipePartitionCustQueryWrapper.in("id", idList);
        List<PipePartitionCust> pipePartitionCustList1 = pipePartitionCustMapper.selectList(pipePartitionCustQueryWrapper);
        List<String> bindIdList = pipePartitionCustList1.stream().filter(a -> StringUtils.isNotBlank(a.getPartitionId())).map(a -> a.getId()).collect(Collectors.toList());
        pipePartitionCustList = pipePartitionCustList.stream().filter(a -> !bindIdList.contains(a.getCustCode())).collect(Collectors.toList());
        for (PipePartitionCust pipePartitionCust : pipePartitionCustList) {
            pipePartitionCust.setCreateTime(new Date());
            pipePartitionCust.setTenantId(tenantId);
        }
        if (pipePartitionCustList.size() > 0) {
            pipePartitionCustMapper.batchUpdatePartitionId(pipePartitionCustList);
        }
        return "";
    }

    @Override
    public void batchRemove(List<String> idList) {
        if (idList.size() == 0) {
            return;
        }
        pipePartitionCustMapper.batchRemove(idList);
    }


    @Override
    @Transactional
    public void delete(List<String> idList) {
        pipePartitionCustMapper.deleteBatchIds(idList);
    }
}