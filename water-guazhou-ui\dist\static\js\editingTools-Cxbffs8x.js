import{e as h,y as _,a as Q,v as Fi,S as Zo,Q as It,ax as xe,O as me,w as ei,bu as qo,aY as Qo,bv as Er,M as oi,$ as hs,b as Jo,aJ as Ko,ai as Ks,az as en,W as el,aV as tl}from"./Point-WxyopZva.js";import{R as d,T as m,b2 as ds,b0 as Oe,a3 as Ni,cl as tn,eS as an,$ as se,aO as C,aW as k,a4 as Ar,a_ as mt,b1 as il,hJ as al,fw as sl}from"./index-r0dFAfgr.js";import{Z as nl,l as G,a as rl,k as qe,c as ke,f as ai,w as Cs,U as pa}from"./widget-BcWKanF2.js";import{l as J,bp as ol,jr as ll,dE as cl,dB as Rr,e0 as Ie,gV as ua,f_ as ca,gN as hl,fZ as yi,aA as Je,dD as tt,af as M,cP as dl,hr as _a,aR as B,aW as V,b4 as ut,ae as re,aT as Re,aS as ga,a_ as ge,aO as j,ey as Tr,ac as He,aQ as X,gl as Dr,ag as F,bm as Oi,aN as ma,aX as si,a9 as Fe,a0 as Ue,ji as pl,fY as Pr,kI as fa,d0 as Te,b1 as va,bb as ul,ev as Ei,kJ as ps,fT as us,kK as ya,eO as Ai,jk as Ls,ez as $r,eA as jt,hu as _l,hh as gl,bQ as Cr,aP as pe,k as Lr,a1 as ml,bd as fl,x as Gr,kL as vl,kM as yl,kN as _s,i9 as sn,kO as te,kP as gs,dA as ba,e7 as bl,bf as nn,bh as rn,kQ as on,b2 as Ir,as as Ml,kR as ms,kS as Sl,kT as x,kU as wl,ex as Ke,kV as xl,kW as Ol,kX as fs,kY as El,aY as Al,kZ as Rl,dC as Tl,fr as Vr,ab as Ma,k_ as Gs,a$ as zr,k$ as Sa,jg as Dl,l0 as Fr,l1 as Pl,f as Nr,bk as $l,aU as jr,b0 as Cl,c_ as Is,l2 as Ll,kh as ln,l3 as cn,b as Gl,hs as hn,l4 as Il,b3 as Ri,cO as Vl,g3 as dn,l5 as pn,ea as Ji,l6 as zl}from"./MapView-DaoQedLH.js";import{i as Hr,Z as un,E as _n,s as Fl,u as gn,f as K,d as Ur,g as Nl}from"./elevationInfoUtils-5B4aSzEU.js";import{d as jl,e as Hl,x as Ul,v as wa,c as pt,y as xa,j as Ti,m as ji,t as Va,f as kl,b as Bl,k as Wl,a as Di,n as Ja,D as Yl}from"./automaticLengthMeasurementUtils-DljoUgEz.js";import{af as kr,ag as Xl,ah as mn,ai as fn,aj as ee,ak as Ka,al as Zl,am as ql,a2 as Ql,an as Jl,ao as Kl,ap as vn,aq as ec,ar as tc,a7 as Vs,as as ic,at as ac,a4 as Ne,au as Si,av as sc,aw as li,ax as Jt,Q as za,g as xt,ay as nc,az as Br,aA as rc,aB as oc,i as Oa,V as Fa,aC as Ea,aD as yn,aE as lc,a6 as ct,aF as bn,S as Wr,aG as cc,aH as hc,aI as dc,a8 as Mn}from"./AnimatedLinesLayer-B2VbV4jv.js";import{r as Pi,t as Yr}from"./vec4f32-CjrfB-0a.js";import{a as Xr,y as Zr,b as qr,p as Qr,g as Jr}from"./Octree-s2cwrV3a.js";import{l as Pt,v as $i,h as Aa,m as pc,b as Ra,M as uc,B as _c}from"./lineSegment-DQ0q5UHF.js";import{R as gc,p as vs,d as Hi,h as es,c as w,e as ne,t as ys,i as mc,b as Sn,j as fc,o as wn}from"./sphere-NgXH-gLx.js";import{e as vc,C as Na,a as wi,E as xn,l as zs,x as Fs,z as it,h as ja,f as je,c as Ta,o as yc,$ as bc,d as Ha,b as ci,g as Mc,i as Sc,j as ze,r as bs,k as wc,m as On,p as xc,n as Oc,q as bi,s as Ec,t as ts,u as Ac,v as Rc,w as Ns,A as Tc,y as Da,O as Kr,B as Dc,D as Pc,F as Ms,G as eo}from"./NativeLine.glsl-4J4u-lA2.js";import{an as Ut,al as to,ao as $c,ap as Cc,b as he,$ as ht,j as Ss,aq as Lt,Q as Ui,f as En,a6 as ki,a7 as Bi,a5 as Wi,ar as Lc,r as N,t as Gc,as as Ic,W as St,a8 as js,at as Vc,ah as Ae,au as zc,av as Fc,a4 as R,am as io,ak as ws,T as Nc,h as U,g as ao,u as so,P as no,v as Hs,d as jc,X as ro,a as Hc,ac as oo,ad as Uc,af as An,aw as kc,ax as lo,ay as Bc,az as Wc,a3 as Rn,a9 as Yc,aa as Xc,ab as Zc,ai as qc,aj as Qc}from"./VertexColor.glsl-CUs3Tjt8.js";import{o as Jc}from"./glUtil-D4FNL8tc.js";import{T as co}from"./InterleavedLayout-EYSqXknm.js";import{n as Pa}from"./triangulationUtils-Da3LiW_b.js";import{O as A}from"./VertexAttribute-BAIQI41G.js";import{R as Qe,E as ho,F as Kc,I as ha}from"./enums-BDQrMlcz.js";import{o as T,W as Yi,e as Us,_ as Xi,n as eh,a as at,c as th,A as po,h as uo,b as _o,E as go,f as ih,l as ah,d as sh,S as nh,g as rh}from"./OrderIndependentTransparency-C5Ap76ew.js";import{E as oh}from"./FramebufferObject-8j9PRuxE.js";import{M as Ht}from"./dehydratedFeatures-CEuswj7y.js";import{e as fe,t as lh,o as ch}from"./mat4f64-BCm7QTSd.js";import{n as ie}from"./basicInterfaces-Dc_Mm1a-.js";import{i as hh}from"./BufferView-BcX1hwIm.js";import{s as dh}from"./Util-sSNWzwlq.js";import{H as ph,B as uh,M as Tn,A as _h,G as gh,S as Ki,Y as Dn}from"./editPlaneUtils-BTKj0FMd.js";import{e as mh}from"./mat3f64-BVJGbF0t.js";import{p as hi,_ as Ft,x as ni,Y as Zi,J as fh}from"./plane-BhzlJB-C.js";import{W as ea,Z as pi}from"./boundedPlane-DeyjpfhM.js";import{j as vh}from"./GraphicManipulator-HH4WUKex.js";import{r as Ci,p as Ua,a as ks,l as yh,c as bh,n as Mh}from"./TranslateTooltipInfos-D4yK3rJA.js";import{i as Sh}from"./automaticAreaMeasurementUtils-DfgMw58X.js";import{g as wh,l as xh}from"./axisAngleDegrees-CVgmQKGQ.js";import{g as ui}from"./persistable-CIG2ELSD.js";import{Q as Pn}from"./quat-CM9ioDFt.js";import"./triangle-lwOWqU0w.js";import"./Indices-iFKW8TWb.js";import"./spatialReferenceEllipsoidUtils-j_kxMN-4.js";import{i as Oh,p as Eh}from"./ExtentTooltipInfos-BUk_dezP.js";import"./vec3f32-nZdmKIgz.js";import"./pe-B8dP0-Ut.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./enums-B5k73o5q.js";import"./quatf64-QCogZAoR.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./Texture-BYqObwfn.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./NestedMap-DgiGbX8E.js";import"./floatRGBA-PQQNbO39.js";import"./requestImageUtils-DBUrisSF.js";import"./doublePrecisionUtils-B0owpBza.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./types-Cezv0Yl1.js";import"./deduplicate-Clsym5GM.js";import"./drapedUtils-DJwxIB1g.js";import"./multiOriginJSONSupportUtils-C0wm8_Yw.js";import"./resourceExtension-DfSw5lpL.js";let Ah=a=>({vnodeSelector:"",properties:void 0,children:void 0,text:a.toString(),domNode:null}),mo=(a,e,t)=>{for(let i=0,s=e.length;i<s;i++){let n=e[i];Array.isArray(n)?mo(a,n,t):n!=null&&n!==!1&&(typeof n=="string"&&(n=Ah(n)),t.push(n))}};function Rh(a,e,t){if(Array.isArray(e))t=e,e=void 0;else if(e&&(typeof e=="string"||e.hasOwnProperty("vnodeSelector"))||t&&(typeof t=="string"||t.hasOwnProperty("vnodeSelector")))throw new Error("h called with invalid arguments");let i,s;return t&&t.length===1&&typeof t[0]=="string"?i=t[0]:t&&(s=[],mo(a,t,s),s.length===0&&(s=void 0)),{vnodeSelector:a,properties:e,children:s,text:i===""?void 0:i,domNode:null}}const $n={bottom:"esri-text-overlay-item-anchor-bottom","bottom-right":"esri-text-overlay-item-anchor-bottom-right","bottom-left":"esri-text-overlay-item-anchor-bottom-left",top:"esri-text-overlay-item-anchor-top","top-right":"esri-text-overlay-item-anchor-top-right","top-left":"esri-text-overlay-item-anchor-top-left",center:"esri-text-overlay-item-anchor-center",right:"esri-text-overlay-item-anchor-right",left:"esri-text-overlay-item-anchor-left"};let ae=class extends Fi{get position(){return[this.x,this.y]}set position(e){this._set("x",e[0]),this._set("y",e[1])}get _textShadowColor(){return this.backgroundColor}get _textShadow(){const e=this._textShadowColor.toCss(!1);return`0 0 ${this._textShadowSize}px ${e}`}get _padding(){return .5*this.fontSize}get _borderRadius(){return this._padding}constructor(e){super(e),this.x=0,this.y=0,this.text="-",this.fontSize=14,this.anchor="center",this.visible=!0,this.backgroundColor=new J([0,0,0,.6]),this.textColor=new J([255,255,255]),this._textShadowSize=1}render(){return Rh("div",{classes:this._cssClasses(),styles:{left:Math.floor(this.x)+"px",top:Math.floor(this.y)+"px",visibility:this.visible?"visible":"hidden",fontSize:this.fontSize+"px",lineHeight:this.fontSize+"px",backgroundColor:this.backgroundColor.toCss(!0),color:this.textColor.toCss(!0),padding:this._padding+"px",borderRadius:this._borderRadius+"px",textShadow:this._textShadow}},[this.text])}renderCanvas(e){if(!this.visible)return;const t=e.font.replace(/^(.*?)px/,"");e.font=`${this.fontSize}px ${t}`;const i=this._padding,s=this._borderRadius,n=e.measureText(this.text).width,r=this.fontSize,o=Th[this.anchor];e.textAlign="center",e.textBaseline="middle";const l=n+2*i,c=r+2*i,p=this.x+o.x*l,u=this.y+o.y*c;this._roundedRect(e,p,u,l,c,s),e.fillStyle=this.backgroundColor.toCss(!0),e.fill();const g=this.x+(o.x+.5)*l,v=this.y+(o.y+.5)*c;this._renderTextShadow(e,this.text,g,v),e.fillStyle=this.textColor.toCss(!0),e.fillText(this.text,g,v)}_renderTextShadow(e,t,i,s){e.lineJoin="miter",e.fillStyle=`rgba(${this._textShadowColor.r}, ${this._textShadowColor.g}, ${this._textShadowColor.b}, ${1/xs.length})`;const n=this._textShadowSize;for(const[r,o]of xs)e.fillText(t,i+n*r,s+n*o)}_roundedRect(e,t,i,s,n,r){e.beginPath(),e.moveTo(t,i+r),e.arcTo(t,i,t+r,i,r),e.lineTo(t+s-r,i),e.arcTo(t+s,i,t+s,i+r,r),e.lineTo(t+s,i+n-r),e.arcTo(t+s,i+n,t+s-r,i+n,r),e.lineTo(t+r,i+n),e.arcTo(t,i+n,t,i+n-r,r),e.closePath()}_cssClasses(){const e={"esri-text-overlay-item":!0};for(const t in $n)e[$n[t]]=this.anchor===t;return e}};h([_()],ae.prototype,"x",void 0),h([_()],ae.prototype,"y",void 0),h([_()],ae.prototype,"position",null),h([_()],ae.prototype,"text",void 0),h([_()],ae.prototype,"fontSize",void 0),h([_()],ae.prototype,"anchor",void 0),h([_()],ae.prototype,"visible",void 0),h([_()],ae.prototype,"backgroundColor",void 0),h([_()],ae.prototype,"textColor",void 0),h([_()],ae.prototype,"_textShadowSize",void 0),h([_()],ae.prototype,"_textShadowColor",null),h([_()],ae.prototype,"_textShadow",null),h([_()],ae.prototype,"_padding",null),h([_()],ae.prototype,"_borderRadius",null),ae=h([Q("esri.views.overlay.TextOverlayItem")],ae);const Th={bottom:{x:-.5,y:-1,textAlign:"center",textBaseline:"bottom"},"bottom-left":{x:0,y:-1,textAlign:"left",textBaseline:"bottom"},"bottom-right":{x:-1,y:-1,textAlign:"right",textBaseline:"bottom"},center:{x:-.5,y:-.5,textAlign:"center",textBaseline:"middle"},left:{x:0,y:-.5,textAlign:"left",textBaseline:"middle"},right:{x:-1,y:-.5,textAlign:"right",textBaseline:"middle"},top:{x:-.5,y:0,textAlign:"center",textBaseline:"top"},"top-left":{x:0,y:0,textAlign:"left",textBaseline:"top"},"top-right":{x:-1,y:0,textAlign:"right",textBaseline:"top"}},xs=[];for(let e=0;e<360;e+=360/16)xs.push([Math.cos(Math.PI*e/180),Math.sin(Math.PI*e/180)]);const Dh=ae,Ph=3025,$h={default:15,far:25};let Xe=class extends Fi{constructor(e){super(e),this.context=null,this.stagedVertex=null,this.visible=!0,this.edgeDistance="default",this._messagesUnits=null,this._labelInfos=[],this._nextLabelIndex=0}initialize(){const e=ol(async i=>{const s=await nl("esri/core/t9n/Units");Zo(i),this._messagesUnits=s}),t=()=>ds(this.context,i=>i.editGeometryOperations);this.addHandles([G(()=>[d(this.context)&&this.getCameraOrExtent(this.context),this.visible,this._edgeDistancePixels,this.stagedVertex,this._messagesUnits],()=>this._update()),...["vertex-add","vertex-update","vertex-remove"].map(i=>rl(t,i,()=>this._update())),It(()=>e.abort())])}destroy(){for(this._nextLabelIndex=0;this._labelInfos.length;)this._destroyLabel(this._labelInfos.pop())}get updating(){return m(this._messagesUnits)}get test(){return{labelContents:this._labelInfos.slice(0,this._nextLabelIndex).map(e=>e.label.text)}}get _edgeDistancePixels(){return $h[this.edgeDistance]}_update(){this._nextLabelIndex=0;const e=this.context;if(m(e))return void this._destroyUnusedLabels();const{components:t,geometry:i,coordinateHelper:s}=e.editGeometryOperations.data;if(!i)return void this._destroyUnusedLabels();const n=t.length;for(let r=0;r<n;++r){const o=[];if(t[r].iterateVertices(v=>{o.push(s.toXYZ(v.pos))}),r===0&&d(this.stagedVertex)&&o.push(s.toXYZ(this.stagedVertex)),o.length<2)continue;const l=o[0],c=o[o.length-1];i.type==="polygon"&&o.length>2&&!ll(l,c)&&o.push(l);const p=n===1&&!cl(o,!1,!1);let u=Ch,g=Lh;this.toScreenPointArray(e,l,u);for(let v=1;v<o.length;++v){const b=o[v-1],f=o[v];this.toScreenPointArray(e,f,g),this._addLabel(e,b,u,f,g,p),[u,g]=[g,u]}}this._destroyUnusedLabels()}_addLabel(e,t,i,s,n,r){const{label:o}=this._getOrCreateLabel(e);if(!this.visible||Rr(i,n)<Ph)return void(o.visible=!1);const l=d(e.graphicState)?e.graphicState.isDraped?"on-the-ground":"absolute-height":Hr(e.editGeometryOperations.data.geometry,e.elevationInfo),{spatialReference:c}=e.editGeometryOperations.data,p=jl(t,s,c,l),u=this._messagesUnits,g=Hl(e.view);o.text=d(u)&&d(p)?Ul(u,p,g):"",o.visible=!0;const v=n[0]-i[0],b=n[1]-i[1];r?Ie(Pe,-b,v):Ie(Pe,b,-v),ua(Pe,Pe),ca(Pe,Pe,this._edgeDistancePixels),hl(_i,i,n,.5),yi(_i,_i,Pe),o.position=[_i[0],_i[1]],Math.abs(Pe[0])>Math.abs(Pe[1])?o.anchor=Pe[0]>0?"left":"right":o.anchor=-Pe[1]<0?"top":"bottom"}_getOrCreateLabel(e){var s;if(this._labelInfos.length>this._nextLabelIndex)return this._labelInfos[this._nextLabelIndex++];const t=new Dh({fontSize:10,anchor:"center"});(s=e.view.overlay)==null||s.items.add(t);const i={label:t};return this._labelInfos.push(i),this._nextLabelIndex=this._labelInfos.length,i}_destroyUnusedLabels(){for(;this._labelInfos.length>this._nextLabelIndex;)this._destroyLabel(this._labelInfos.pop())}_destroyLabel({label:e}){ds(this.context,t=>{var i;return(i=t.view.overlay)==null?void 0:i.items.remove(e)}),e.destroy()}};h([_()],Xe.prototype,"context",void 0),h([_()],Xe.prototype,"stagedVertex",void 0),h([_()],Xe.prototype,"visible",void 0),h([_()],Xe.prototype,"edgeDistance",void 0),h([_()],Xe.prototype,"updating",null),h([_()],Xe.prototype,"_messagesUnits",void 0),h([_()],Xe.prototype,"_edgeDistancePixels",null),Xe=h([Q("esri.views.interactive")],Xe);const Pe=Je(),_i=Je(),Ch=tt(),Lh=tt();let $a=class extends Xe{getCameraOrExtent({view:e}){return e.state.camera}toScreenPointArray({view:e,elevationInfo:t,editGeometryOperations:i},s,n=tt()){const{spatialReference:r}=i.data.coordinateHelper;return kr(s,r,t,e,Cn),e.state.camera.projectToScreen(Cn,n),n}};$a=h([Q("esri.views.3d.interactive.SegmentLabels3D")],$a);const Cn=M();let Gh=class{constructor(e){this._resourceFactory=e,this._resources=null,this._visible=!0,this._attached=!1,this._renderGroup=wi.Outline}destroy(){this._destroyResources()}get resources(){return d(this._resources)?this._resources.external:null}get visible(){return this._visible}set visible(e){e!==this._visible&&(this._visible=e,this._syncGeometriesToRenderer())}get attached(){return this._attached}set attached(e){e!==this._attached&&(this._attached=e,this._createOrDestroyResources())}get renderGroup(){return this._renderGroup}set renderGroup(e){var i;this._renderGroup=e;const t=(i=Oe(this._resources))==null?void 0:i.layerView;t&&(t.renderGroup=e)}recreate(){this.attached&&this._createResources()}recreateGeometry(){this._resourceFactory.recreateGeometry?m(this._resources)||(this._ensureRenderGeometriesRemoved(),this._resourceFactory.recreateGeometry(this._resources.external),this._syncGeometriesToRenderer()):this.recreate()}_createOrDestroyResources(){this._attached?m(this._resources)&&this._createResources():this._destroyResources()}_createResources(){var s;this._destroyResources();const e=this._resourceFactory.createResources(),t=new $t({view:this._resourceFactory.view,renderGroup:this._renderGroup}),i=(s=this._resourceFactory.view.basemapTerrain)==null?void 0:s.overlayManager;this._resources={layerView:new $t({view:this._resourceFactory.view,renderGroup:this._renderGroup}),external:e,geometriesAdded:!1},i&&(this._resources.drapeSourceRenderer=i.registerGeometryDrapeSource(t)),this._syncGeometriesToRenderer()}_destroyResources(){var t;if(m(this._resources))return;this._ensureRenderGeometriesRemoved();const e=(t=this._resourceFactory.view.basemapTerrain)==null?void 0:t.overlayManager;e&&e.unregisterDrapeSource(this._resources.layerView),this._resourceFactory.destroyResources(this._resources.external),this._resources=null}_syncGeometriesToRenderer(){this._visible?this._ensureRenderGeometriesAdded():this._ensureRenderGeometriesRemoved()}_ensureRenderGeometriesRemoved(){m(this._resources)||m(this._resources.drapeSourceRenderer)||this._resources.geometriesAdded&&(this._resources.drapeSourceRenderer.removeGeometries(this._resources.external.geometries,xn.UPDATE),this._resources.geometriesAdded=!1)}_ensureRenderGeometriesAdded(){m(this._resources)||m(this._resources.drapeSourceRenderer)||this._resources.geometriesAdded||(this._resources.drapeSourceRenderer.addGeometries(this._resources.external.geometries,xn.UPDATE),this._resources.geometriesAdded=!0)}},$t=class extends dl(Fi){constructor(e){super(e),this.drapeSourceType=vc.Features,this.updatePolicy=Na.SYNC,this.renderGroup=wi.Outline}};h([_({constructOnly:!0})],$t.prototype,"view",void 0),h([_({readOnly:!0})],$t.prototype,"drapeSourceType",void 0),h([_()],$t.prototype,"renderGroup",void 0),$t=h([Q("DrapedVisualElementLayerView")],$t);let Bs=class{constructor(e){this._attached=!1,this._resourcesCreated=!1,this._visible=!0,this.view=e,this._handle=G(()=>this.view.ready,t=>{this._resourcesCreated&&(t?this._createResources():this._destroyResources())})}applyProps(e){let t=!1;for(const i in e)i in this?i==="attached"?t=e[i]:this[i]=e[i]:console.error("Cannot set unknown property",i);this.attached=t}destroy(){this.attached=!1,this._handle.remove()}get attached(){return this._attached}set attached(e){e!==this._attached&&this.view._stage&&(this._attached=e,this._attached&&!this._resourcesCreated?this._createResources():!this._attached&&this._resourcesCreated&&this._destroyResources(),this.onAttachedChange(e))}onAttachedChange(e){}get visible(){return this._visible}set visible(e){e!==this._visible&&(this._visible=e,this.attached&&this.updateVisibility(e))}_createResources(){this.createResources(),this._resourcesCreated=!0,this.updateVisibility(this.visible)}_destroyResources(){this.destroyResources(),this._resourcesCreated=!1}},fo=class{constructor(e){this._resourceFactory=e,this._resources=null,this._visible=!0,this._attached=!1}destroy(){this._destroyResources()}get object(){return d(this._resources)?this._resources.object:null}get resources(){return d(this._resources)?this._resources.external:null}get visible(){return this._visible}set visible(e){e!==this._visible&&(this._visible=e,this._syncVisible())}get attached(){return this._attached}set attached(e){e!==this._attached&&(this._attached=e,this._createOrDestroyResources())}recreate(){this.attached&&this._createResources()}recreateGeometry(){if(!this._resourceFactory.recreateGeometry)return void this.recreate();const e=this._resourceFactory.view._stage;if(m(this._resources)||!e)return;const t=this._resources.object;this._resources.external.forEach(i=>{i.type!==Ut.Mesh&&i.type!==Ut.Line&&i.type!==Ut.Point||e.remove(i)}),t.removeAllGeometries(),this._resourceFactory.recreateGeometry(this._resources.external,t,this._resources.layer),this._resources.external.forEach(i=>{i.type!==Ut.Mesh&&i.type!==Ut.Line&&i.type!==Ut.Point||e.add(i)})}_createOrDestroyResources(){this._attached?this._resources||this._createResources():this._destroyResources()}_createResources(){this._destroyResources();const e=this._resourceFactory,t=e.view,i=t._stage;if(!i)return;const s=new zs({pickable:!1,updatePolicy:Na.SYNC});i.add(s);const n=new Fs({castShadow:!1}),r=e.createResources(n,s);r.forEach(c=>{i.add(c),c instanceof to&&i.loadImmediate(c)}),i.add(n),s.add(n);const o=e.cameraChanged,l=o?G(()=>t.state.camera,c=>o(c),qe):null;this._resources={layer:s,object:n,external:r,cameraHandle:l},this._syncVisible()}_destroyResources(){var t;if(m(this._resources))return;const e=this._resourceFactory.view._stage;e&&(e.remove(this._resources.object),e.remove(this._resources.layer),this._resources.external.forEach(i=>e.remove(i))),this._resources.object.dispose(),(t=this._resources.cameraHandle)==null||t.remove(),this._resourceFactory.destroyResources(this._resources.external),this._resources=null}_syncVisible(){m(this._resources)||(this._resources.object.visible=this._visible)}},ka=class extends Bs{constructor({view:e,isDraped:t}){super(e),this._isDraped=!1,this.object3dResources=new fo(this.createObject3DResourceFactory(e)),this.drapedResources=new Gh(this.createDrapedResourceFactory(e)),this.isDraped=t??!1}get isDraped(){return this._isDraped}set isDraped(e){e!==this._isDraped&&(this._isDraped=e,this.object3dResources.attached=this.attached&&!e,this.drapedResources.attached=this.attached&&e)}get renderGroup(){return this.drapedResources.renderGroup}set renderGroup(e){this.drapedResources.renderGroup=e}createResources(){this.object3dResources.attached=!this._isDraped,this.drapedResources.attached=this._isDraped}destroyResources(){this.object3dResources.attached=!1,this.drapedResources.attached=!1}recreate(){this.object3dResources.recreate(),this.drapedResources.recreate()}recreateGeometry(){this.object3dResources.recreateGeometry(),this.drapedResources.recreateGeometry()}destroy(){this.object3dResources.destroy(),this.drapedResources.destroy(),super.destroy()}updateVisibility(e){this.object3dResources.visible=e,this.drapedResources.visible=e}};function vo(a,e){a.extensions.add("GL_OES_standard_derivatives");const t=a.fragment;t.include($c),a.include(Cc),t.uniforms.add([new he("globalAlpha",i=>i.globalAlpha),new ht("glowColor",i=>i.glowColor),new he("glowWidth",(i,s)=>i.glowWidth*s.camera.pixelRatio),new he("glowFalloff",i=>i.glowFalloff),new ht("innerColor",i=>i.innerColor),new he("innerWidth",(i,s)=>i.innerWidth*s.camera.pixelRatio),new Ss("depthMap",(i,s)=>s.linearDepthTexture),new Lt("nearFar",(i,s)=>s.camera.nearFar),new Ss("frameColor",(i,s)=>s.mainColorTexture)]),t.code.add(T`vec4 blendPremultiplied(vec4 source, vec4 dest) {
float oneMinusSourceAlpha = 1.0 - source.a;
return vec4(
source.rgb + dest.rgb * oneMinusSourceAlpha,
source.a + dest.a * oneMinusSourceAlpha
);
}`),t.code.add(T`vec4 premultipliedColor(vec3 rgb, float alpha) {
return vec4(rgb * alpha, alpha);
}`),t.code.add(T`vec4 laserlineProfile(float dist) {
if (dist > glowWidth) {
return vec4(0.0);
}
float innerAlpha = (1.0 - smoothstep(0.0, innerWidth, dist));
float glowAlpha = pow(max(0.0, 1.0 - dist / glowWidth), glowFalloff);
return blendPremultiplied(
premultipliedColor(innerColor, innerAlpha),
premultipliedColor(glowColor, glowAlpha)
);
}`),t.code.add(T`bool laserlineReconstructFromDepth(out vec3 pos, out vec3 normal, out float depthDiscontinuityAlpha) {
float depth = linearDepthFromTexture(depthMap, uv, nearFar);
if (-depth == nearFar[0]) {
return false;
}
pos = reconstructPosition(gl_FragCoord.xy, depth);
normal = normalize(cross(dFdx(pos), dFdy(pos)));
float ddepth = fwidth(depth);
depthDiscontinuityAlpha = 1.0 - smoothstep(0.0, 0.01, -ddepth / depth);
return true;
}`),e.contrastControlEnabled?(t.uniforms.add(new he("globalAlphaContrastBoost",i=>d(i.globalAlphaContrastBoost)?i.globalAlphaContrastBoost:1)),t.code.add(T`float rgbToLuminance(vec3 color) {
return dot(vec3(0.2126, 0.7152, 0.0722), color);
}
vec4 laserlineOutput(vec4 color) {
float backgroundLuminance = rgbToLuminance(texture2D(frameColor, uv).rgb);
float alpha = clamp(globalAlpha * max(backgroundLuminance * globalAlphaContrastBoost, 1.0), 0.0, 1.0);
return color * alpha;
}`)):t.code.add(T`vec4 laserlineOutput(vec4 color) {
return color * globalAlpha;
}`)}function yo(a){const e=new Ui;e.include(vo,a);const{vertex:t,fragment:i}=e;return t.uniforms.add([new En("modelView",(s,n)=>_a(Vh,n.camera.viewMatrix,s.origin)),new En("proj",(s,n)=>n.camera.projectionMatrix),new he("glowWidth",(s,n)=>s.glowWidth*n.camera.pixelRatio),new Lt("pixelToNDC",(s,n)=>Ie(Ih,2/n.camera.fullViewport[2],2/n.camera.fullViewport[3]))]),e.attributes.add(A.START,"vec3"),e.attributes.add(A.END,"vec3"),e.attributes.add(A.UP,"vec3"),e.attributes.add(A.EXTRUDE,"vec2"),e.varyings.add("uv","vec2"),e.varyings.add("vViewStart","vec3"),e.varyings.add("vViewEnd","vec3"),e.varyings.add("vViewPlane","vec4"),t.code.add(T`void main() {
vec3 pos = mix(start, end, extrude.x);
vec4 viewPos = modelView * vec4(pos, 1);
vec4 projPos = proj * viewPos;
vec2 ndcPos = projPos.xy / projPos.w;
vec3 viewUp = (modelView * vec4(extrude.y * up, 0)).xyz;
vec4 projPosUp = proj * vec4(viewPos.xyz + viewUp, 1);
vec2 projExtrudeDir = normalize(projPosUp.xy / projPosUp.w - ndcPos);
vec2 lxy = abs(sign(projExtrudeDir) - ndcPos);
ndcPos += length(lxy) * projExtrudeDir;
vec3 worldPlaneNormal = normalize(cross(up, normalize(end - start)));
vec3 viewPlaneNormal = (modelView * vec4(worldPlaneNormal, 0)).xyz;
vViewStart = (modelView * vec4(start, 1)).xyz;
vViewEnd = (modelView * vec4(end, 1)).xyz;
vViewPlane = vec4(viewPlaneNormal, -dot(viewPlaneNormal, vViewStart));
float xPaddingPixels = sign(dot(viewPlaneNormal, viewPos.xyz)) * (extrude.x * 2.0 - 1.0) * glowWidth;
ndcPos.x += xPaddingPixels * pixelToNDC.x;
uv = ndcPos * 0.5 + 0.5;
gl_Position = vec4(ndcPos, 0, 1);
}`),i.uniforms.add(new he("perScreenPixelRatio",(s,n)=>n.camera.perScreenPixelRatio)),i.code.add(T`float planeDistancePixels(vec4 plane, vec3 pos, vec3 start, vec3 end) {
vec3 origin = mix(start, end, 0.5);
vec3 basis = end - origin;
vec3 posAtOrigin = pos - origin;
float x = dot(normalize(basis), posAtOrigin);
float y = dot(plane.xyz, posAtOrigin);
float dx = max(abs(x) - length(basis), 0.0);
float dy = y;
float dist = length(vec2(dx, dy));
float width = fwidth(y);
float maxPixelDistance = length(pos) * perScreenPixelRatio * 2.0;
float pixelDist = dist / min(width, maxPixelDistance);
return abs(pixelDist);
}
void main() {
vec3 pos;
vec3 normal;
float depthDiscontinuityAlpha;
if (!laserlineReconstructFromDepth(pos, normal, depthDiscontinuityAlpha)) {
discard;
}
float distance = planeDistancePixels(vViewPlane, pos, vViewStart, vViewEnd);
vec4 color = laserlineProfile(distance);
float alpha = 1.0 - smoothstep(0.995, 0.999, abs(dot(normal, vViewPlane.xyz)));
gl_FragColor = laserlineOutput(color * alpha * depthDiscontinuityAlpha);
}`),e}const Ih=Je(),Vh=fe(),zh=Object.freeze(Object.defineProperty({__proto__:null,build:yo},Symbol.toStringTag,{value:"Module"}));let bo=class Mo extends ki{initializeProgram(e){return new Bi(e.rctx,Mo.shader.get().build(this.configuration),So)}initializePipeline(){return Yi({blending:Us(Qe.ONE,Qe.ONE_MINUS_SRC_ALPHA),colorWrite:Xi})}};bo.shader=new Wi(zh,()=>Ni(()=>Promise.resolve().then(()=>gu),void 0));const So=new Map([[A.START,0],[A.END,1],[A.UP,2],[A.EXTRUDE,3]]);let Ln=class{constructor(e){this._renderCoordsHelper=e,this._buffers=null,this._origin=M(),this._dirty=!1,this._count=0,this._vao=null}set vertices(e){const t=Pa(3*e.length);let i=0;for(const s of e)t[i++]=s[0],t[i++]=s[1],t[i++]=s[2];this.buffers=[t]}set buffers(e){if(this._buffers=e,this._buffers.length>0){const t=this._buffers[0],i=3*Math.floor(t.length/3/2);B(this._origin,t[i+0],t[i+1],t[i+2])}else B(this._origin,0,0,0);this._dirty=!0}get origin(){return this._origin}draw(e){const t=this._ensureVAO(e);d(t)&&(e.bindVAO(t),e.drawArrays(ho.TRIANGLES,0,this._count))}dispose(){d(this._vao)&&this._vao.dispose()}_ensureVAO(e){return m(this._buffers)?null:(m(this._vao)&&(this._vao=this._createVAO(e,this._buffers)),this._ensureVertexData(this._vao,this._buffers),this._vao)}_createVAO(e,t){const i=this._createDataBuffer(t);return this._dirty=!1,new Lc(e,So,{data:Jc(In)},{data:oh.createVertex(e,Kc.STATIC_DRAW,i)})}_ensureVertexData(e,t){var s;if(!this._dirty)return;const i=this._createDataBuffer(t);(s=e.vertexBuffers.data)==null||s.setData(i),this._dirty=!1}_numberOfRenderVertices(e){return 3*(2*(e.length/3-1))}_createDataBuffer(e){const t=e.reduce((o,l)=>o+this._numberOfRenderVertices(l),0);this._count=t;const i=In.createBuffer(t),s=this._origin;let n=0,r=0;for(const o of e){for(let l=0;l<o.length;l+=3){const c=B(Gn,o[l+0],o[l+1],o[l+2]);l===0?r=this._renderCoordsHelper.getAltitude(c):this._renderCoordsHelper.setAltitude(c,r);const p=this._renderCoordsHelper.worldUpAtPosition(c,Fh),u=n+2*l,g=V(Gn,c,s);if(l<o.length-3){i.up.setVec(u,p),i.up.setVec(u+3,p),i.up.setVec(u+5,p);for(let v=0;v<6;v++)i.start.setVec(u+v,g);i.extrude.setValues(u+0,0,-1),i.extrude.setValues(u+1,1,-1),i.extrude.setValues(u+2,1,1),i.extrude.setValues(u+3,0,-1),i.extrude.setValues(u+4,1,1),i.extrude.setValues(u+5,0,1)}if(l>0){i.up.setVec(u-2,p),i.up.setVec(u-4,p),i.up.setVec(u-5,p);for(let v=-6;v<0;v++)i.end.setVec(u+v,g)}}n+=this._numberOfRenderVertices(o)}return i.buffer}};const Fh=M(),Gn=M(),In=co().vec3f(A.START).vec3f(A.END).vec3f(A.UP).vec2f(A.EXTRUDE);let Ws=class extends Gc{constructor(){super(...arguments),this.contrastControlEnabled=!1}};h([N()],Ws.prototype,"contrastControlEnabled",void 0);const Ys=ut(6);function wo(a){const e=new Ui;e.extensions.add("GL_OES_standard_derivatives"),e.include(Ic),e.include(vo,a);const t=e.fragment;if(a.lineVerticalPlaneEnabled||a.heightManifoldEnabled)if(t.uniforms.add(new he("maxPixelDistance",(i,s)=>a.heightManifoldEnabled?2*s.camera.computeScreenPixelSizeAt(i.heightManifoldTarget):2*s.camera.computeScreenPixelSizeAt(i.lineVerticalPlaneSegment.origin))),t.code.add(T`float planeDistancePixels(vec4 plane, vec3 pos) {
float dist = dot(plane.xyz, pos) + plane.w;
float width = fwidth(dist);
dist /= min(width, maxPixelDistance);
return abs(dist);
}`),a.spherical){const i=(n,r,o)=>ge(n,r.heightManifoldTarget,o.camera.viewMatrix),s=(n,r)=>ge(n,[0,0,0],r.camera.viewMatrix);t.uniforms.add([new St("heightManifoldOrigin",(n,r)=>(i(Ze,n,r),s(qt,r),V(qt,qt,Ze),re(Se,qt),Se[3]=Re(qt),Se)),new ht("globalOrigin",(n,r)=>s(Ze,r)),new he("cosSphericalAngleThreshold",(n,r)=>1-Math.max(2,ga(r.camera.eye,n.heightManifoldTarget)*r.camera.perRenderPixelRatio)/Re(n.heightManifoldTarget))]),t.code.add(T`float globeDistancePixels(float posInGlobalOriginLength) {
float dist = abs(posInGlobalOriginLength - heightManifoldOrigin.w);
float width = fwidth(dist);
dist /= min(width, maxPixelDistance);
return abs(dist);
}
float heightManifoldDistancePixels(vec4 heightPlane, vec3 pos) {
vec3 posInGlobalOriginNorm = normalize(globalOrigin - pos);
float cosAngle = dot(posInGlobalOriginNorm, heightManifoldOrigin.xyz);
vec3 posInGlobalOrigin = globalOrigin - pos;
float posInGlobalOriginLength = length(posInGlobalOrigin);
float sphericalDistance = globeDistancePixels(posInGlobalOriginLength);
float planarDistance = planeDistancePixels(heightPlane, pos);
return cosAngle < cosSphericalAngleThreshold ? sphericalDistance : planarDistance;
}`)}else t.code.add(T`float heightManifoldDistancePixels(vec4 heightPlane, vec3 pos) {
return planeDistancePixels(heightPlane, pos);
}`);if(a.pointDistanceEnabled&&(t.uniforms.add(new he("maxPixelDistance",(i,s)=>2*s.camera.computeScreenPixelSizeAt(i.pointDistanceTarget))),t.code.add(T`float sphereDistancePixels(vec4 sphere, vec3 pos) {
float dist = distance(sphere.xyz, pos) - sphere.w;
float width = fwidth(dist);
dist /= min(width, maxPixelDistance);
return abs(dist);
}`)),a.intersectsLineEnabled&&(t.uniforms.add(new he("perScreenPixelRatio",(i,s)=>s.camera.perScreenPixelRatio)),t.code.add(T`float lineDistancePixels(vec3 start, vec3 dir, float radius, vec3 pos) {
float dist = length(cross(dir, pos - start)) / (length(pos) * perScreenPixelRatio);
return abs(dist) - radius;
}`)),(a.lineVerticalPlaneEnabled||a.intersectsLineEnabled)&&t.code.add(T`bool pointIsWithinLine(vec3 pos, vec3 start, vec3 end) {
vec3 dir = end - start;
float t2 = dot(dir, pos - start);
float l2 = dot(dir, dir);
return t2 >= 0.0 && t2 <= l2;
}`),t.code.add(T`void main() {
vec3 pos;
vec3 normal;
float depthDiscontinuityAlpha;
if (!laserlineReconstructFromDepth(pos, normal, depthDiscontinuityAlpha)) {
discard;
}
vec4 color = vec4(0, 0, 0, 0);`),a.heightManifoldEnabled){t.uniforms.add([new Lt("angleCutoff",s=>ta(s)),new St("heightPlane",(s,n)=>xo(s.heightManifoldTarget,s.renderCoordsHelper.worldUpAtPosition(s.heightManifoldTarget,Ze),n.camera.viewMatrix))]);const i=a.spherical?T`normalize(globalOrigin - pos)`:T`heightPlane.xyz`;t.code.add(T`
    {
      float heightManifoldAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, abs(dot(normal, ${i})));
      vec4 heightManifoldColor = laserlineProfile(heightManifoldDistancePixels(heightPlane, pos));
      color = max(color, heightManifoldColor * heightManifoldAlpha);
    }
    `)}return a.pointDistanceEnabled&&(t.uniforms.add([new Lt("angleCutoff",i=>ta(i)),new St("pointDistanceSphere",(i,s)=>Nh(i,s))]),t.code.add(T`{
float pointDistanceSphereDistance = sphereDistancePixels(pointDistanceSphere, pos);
vec4 pointDistanceSphereColor = laserlineProfile(pointDistanceSphereDistance);
float pointDistanceSphereAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, abs(dot(normal, normalize(pos - pointDistanceSphere.xyz))));
color = max(color, pointDistanceSphereColor * pointDistanceSphereAlpha);
}`)),a.lineVerticalPlaneEnabled&&(t.uniforms.add([new Lt("angleCutoff",i=>ta(i)),new St("lineVerticalPlane",(i,s)=>jh(i,s)),new ht("lineVerticalStart",(i,s)=>Hh(i,s)),new ht("lineVerticalEnd",(i,s)=>Uh(i,s))]),t.code.add(T`{
if (pointIsWithinLine(pos, lineVerticalStart, lineVerticalEnd)) {
float lineVerticalDistance = planeDistancePixels(lineVerticalPlane, pos);
vec4 lineVerticalColor = laserlineProfile(lineVerticalDistance);
float lineVerticalAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, abs(dot(normal, lineVerticalPlane.xyz)));
color = max(color, lineVerticalColor * lineVerticalAlpha);
}
}`)),a.intersectsLineEnabled&&(t.uniforms.add([new Lt("angleCutoff",i=>ta(i)),new ht("intersectsLineStart",(i,s)=>ge(Ze,i.lineStartWorld,s.camera.viewMatrix)),new ht("intersectsLineEnd",(i,s)=>ge(Ze,i.lineEndWorld,s.camera.viewMatrix)),new ht("intersectsLineDirection",(i,s)=>(j(Se,i.intersectsLineSegment.vector),Se[3]=0,re(Ze,Tr(Se,Se,s.camera.viewMatrix)))),new he("intersectsLineRadius",i=>i.intersectsLineRadius)]),t.code.add(T`{
if (pointIsWithinLine(pos, intersectsLineStart, intersectsLineEnd)) {
float intersectsLineDistance = lineDistancePixels(intersectsLineStart, intersectsLineDirection, intersectsLineRadius, pos);
vec4 intersectsLineColor = laserlineProfile(intersectsLineDistance);
float intersectsLineAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, 1.0 - abs(dot(normal, intersectsLineDirection)));
color = max(color, intersectsLineColor * intersectsLineAlpha);
}
}`)),t.code.add(T`gl_FragColor = laserlineOutput(color * depthDiscontinuityAlpha);
}`),e}function ta(a){return Ie(kh,Math.cos(a.angleCutoff),Math.cos(Math.max(0,a.angleCutoff-ut(2))))}function Nh(a,e){return ge(is,a.pointDistanceOrigin,e.camera.viewMatrix),is[3]=ga(a.pointDistanceOrigin,a.pointDistanceTarget),is}function jh(a,e){const t=Pt(a.lineVerticalPlaneSegment,.5,Ze),i=a.renderCoordsHelper.worldUpAtPosition(t,Bh),s=re(qt,a.lineVerticalPlaneSegment.vector),n=He(Se,i,s);return re(n,n),xo(a.lineVerticalPlaneSegment.origin,n,e.camera.viewMatrix)}function Hh(a,e){const t=j(Ze,a.lineVerticalPlaneSegment.origin);return a.renderCoordsHelper.setAltitude(t,0),ge(t,t,e.camera.viewMatrix)}function Uh(a,e){const t=X(Ze,a.lineVerticalPlaneSegment.origin,a.lineVerticalPlaneSegment.vector);return a.renderCoordsHelper.setAltitude(t,0),ge(t,t,e.camera.viewMatrix)}function xo(a,e,t){return ge(Vn,a,t),j(Se,e),Se[3]=0,Tr(Se,Se,t),Ft(Vn,Se,Wh)}const kh=Je(),Ze=M(),Se=Dr(),Bh=M(),qt=M(),Vn=M(),Wh=hi(),is=gc(),Yh=Object.freeze(Object.defineProperty({__proto__:null,build:wo,defaultAngleCutoff:Ys},Symbol.toStringTag,{value:"Module"}));let Xh=class extends eh{constructor(){super(...arguments),this.innerColor=F(1,1,1),this.innerWidth=1,this.glowColor=F(1,.5,0),this.glowWidth=8,this.glowFalloff=8,this.globalAlpha=.75,this.globalAlphaContrastBoost=2,this.angleCutoff=ut(6),this.pointDistanceOrigin=M(),this.pointDistanceTarget=M(),this.lineVerticalPlaneSegment=$i(),this.intersectsLineSegment=$i(),this.intersectsLineRadius=3,this.heightManifoldTarget=M(),this.lineStartWorld=M(),this.lineEndWorld=M()}},Oo=class Eo extends ki{initializeProgram(e){return new Bi(e.rctx,Eo.shader.get().build(this.configuration),js)}initializePipeline(){return Yi({blending:Us(Qe.ONE,Qe.ONE_MINUS_SRC_ALPHA),colorWrite:Xi})}};Oo.shader=new Wi(Yh,()=>Ni(()=>Promise.resolve().then(()=>mu),void 0));let Qt=class extends Ws{constructor(){super(...arguments),this.heightManifoldEnabled=!1,this.pointDistanceEnabled=!1,this.lineVerticalPlaneEnabled=!1,this.intersectsLineEnabled=!1,this.spherical=!1}};h([N()],Qt.prototype,"heightManifoldEnabled",void 0),h([N()],Qt.prototype,"pointDistanceEnabled",void 0),h([N()],Qt.prototype,"lineVerticalPlaneEnabled",void 0),h([N()],Qt.prototype,"intersectsLineEnabled",void 0),h([N()],Qt.prototype,"spherical",void 0);let Zh=class{constructor(e,t={contrastControlEnabled:!1}){this._config=t,this._technique=null,this._heightManifoldEnabled=!1,this._pointDistanceEnabled=!1,this._lineVerticalPlaneEnabled=!1,this._intersectsLineEnabled=!1,this._intersectsLineInfinite=!1,this._viewingMode=Oi.Local,this._pathVerticalPlaneEnabled=!1,this._pathVerticalPlaneData=null,this._pathTechnique=null,this.canRender=!0,this._passParameters=Vc(e,new Xh)}get renderSlots(){return[this._config.contrastControlEnabled?Ae.LASERLINES_CONTRAST_CONTROL:Ae.LASERLINES]}get needsLinearDepth(){return!0}get heightManifoldEnabled(){return this._heightManifoldEnabled}set heightManifoldEnabled(e){this._heightManifoldEnabled!==e&&(this._heightManifoldEnabled=e,this._requestRender())}get heightManifoldTarget(){return this._passParameters.heightManifoldTarget}set heightManifoldTarget(e){j(this._passParameters.heightManifoldTarget,e),this._requestRender()}get pointDistanceEnabled(){return this._pointDistanceEnabled}set pointDistanceEnabled(e){e!==this._pointDistanceEnabled&&(this._pointDistanceEnabled=e,this._requestRender())}get pointDistanceTarget(){return this._passParameters.pointDistanceTarget}set pointDistanceTarget(e){j(this._passParameters.pointDistanceTarget,e),this._requestRender()}get pointDistanceOrigin(){return this._passParameters.pointDistanceOrigin}set pointDistanceOrigin(e){j(this._passParameters.pointDistanceOrigin,e),this._requestRender()}get lineVerticalPlaneEnabled(){return this._lineVerticalPlaneEnabled}set lineVerticalPlaneEnabled(e){e!==this._lineVerticalPlaneEnabled&&(this._lineVerticalPlaneEnabled=e,this._requestRender())}get lineVerticalPlaneSegment(){return this._passParameters.lineVerticalPlaneSegment}set lineVerticalPlaneSegment(e){Aa(e,this._passParameters.lineVerticalPlaneSegment),this._requestRender()}get intersectsLineEnabled(){return this._intersectsLineEnabled}set intersectsLineEnabled(e){e!==this._intersectsLineEnabled&&(this._intersectsLineEnabled=e,this._requestRender())}get intersectsLineSegment(){return this._passParameters.intersectsLineSegment}set intersectsLineSegment(e){Aa(e,this._passParameters.intersectsLineSegment),this._requestRender()}get intersectsLineRadius(){return this._passParameters.intersectsLineRadius}set intersectsLineRadius(e){e!==this._passParameters.intersectsLineRadius&&(this._passParameters.intersectsLineRadius=e,this._requestRender())}get intersectsLineInfinite(){return this._intersectsLineInfinite}set intersectsLineInfinite(e){e!==this._intersectsLineInfinite&&(this._intersectsLineInfinite=e,this._requestRender())}get viewingMode(){return this._viewingMode}set viewingMode(e){e!==this._viewingMode&&(this._viewingMode=e,this._requestRender())}get pathVerticalPlaneEnabled(){return this._pathVerticalPlaneEnabled}set pathVerticalPlaneEnabled(e){e!==this._pathVerticalPlaneEnabled&&(this._pathVerticalPlaneEnabled=e,d(this._pathVerticalPlaneData)&&this._requestRender())}set pathVerticalPlaneVertices(e){m(this._pathVerticalPlaneData)&&(this._pathVerticalPlaneData=new Ln(this._passParameters.renderCoordsHelper)),this._pathVerticalPlaneData.vertices=e,this.pathVerticalPlaneEnabled&&this._requestRender()}set pathVerticalPlaneBuffers(e){m(this._pathVerticalPlaneData)&&(this._pathVerticalPlaneData=new Ln(this._passParameters.renderCoordsHelper)),this._pathVerticalPlaneData.buffers=e,this.pathVerticalPlaneEnabled&&this._requestRender()}setParameters(e){zc(this._passParameters,e)&&this._requestRender()}initializeRenderContext(e){this._context=e;const t=e.renderContext.rctx;this._quadVAO=Fc(t),this._techniqueRepository=e.techniqueRepository,this._techniqueConfig=new Qt;const i=new Ws;i.contrastControlEnabled=this._config.contrastControlEnabled,this._pathTechnique=this._techniqueRepository.acquire(bo,i)}uninitializeRenderContext(){this._quadVAO=tn(this._quadVAO),this._technique=an(this._technique),this._pathVerticalPlaneData=tn(this._pathVerticalPlaneData),this._pathTechnique=an(this._pathTechnique)}prepareTechnique(){return this.heightManifoldEnabled||this.pointDistanceEnabled||this.lineVerticalPlaneSegment||this.intersectsLineEnabled?(this._techniqueConfig.heightManifoldEnabled=this.heightManifoldEnabled,this._techniqueConfig.lineVerticalPlaneEnabled=this.lineVerticalPlaneEnabled,this._techniqueConfig.pointDistanceEnabled=this.pointDistanceEnabled,this._techniqueConfig.intersectsLineEnabled=this.intersectsLineEnabled,this._techniqueConfig.contrastControlEnabled=this._config.contrastControlEnabled,this._techniqueConfig.spherical=this._viewingMode===Oi.Global,this._technique=this._techniqueRepository.releaseAndAcquire(Oo,this._techniqueConfig,this._technique),this._technique):this._pathTechnique}render(e,t){(this.heightManifoldEnabled||this.pointDistanceEnabled||this.lineVerticalPlaneSegment||this.intersectsLineEnabled)&&this._renderUnified(e,t),this.pathVerticalPlaneEnabled&&this._renderPath(e)}_renderUnified(e,t){const i=e.rctx;this._updatePassParameters(e),i.bindTechnique(t,this._passParameters,e.bindParameters),i.bindVAO(this._quadVAO),i.drawArrays(ho.TRIANGLE_STRIP,0,4)}_renderPath(e){if(m(this._pathVerticalPlaneData)||m(this._pathTechnique))return;const t=e.rctx,i=this._pathTechnique;t.bindTechnique(i,{...this._passParameters,origin:this._pathVerticalPlaneData.origin},e.bindParameters),this._pathVerticalPlaneData.draw(e.rctx)}_updatePassParameters(e){if(!this._intersectsLineEnabled)return;const t=e.bindParameters.camera;if(this._intersectsLineInfinite){if(Zr(vs(this._passParameters.intersectsLineSegment.origin,this._passParameters.intersectsLineSegment.vector),gi),gi.c0=-Number.MAX_VALUE,!qr(t.frustum,gi))return;Qr(gi,this._passParameters.lineStartWorld),Jr(gi,this._passParameters.lineEndWorld)}else j(this._passParameters.lineStartWorld,this._passParameters.intersectsLineSegment.origin),X(this._passParameters.lineEndWorld,this._passParameters.intersectsLineSegment.origin,this._passParameters.intersectsLineSegment.vector)}_requestRender(){this._context&&this._context.requestRender()}};const gi=Xr();let Li=class extends Bs{constructor(e){super(e.view),this._angleCutoff=Ys,this._style={},this._heightManifoldTarget=M(),this._heightManifoldEnabled=!1,this._intersectsLine=$i(),this._intersectsLineEnabled=!1,this._intersectsLineInfinite=!1,this._lineVerticalPlaneSegment=null,this._pathVerticalPlaneBuffers=null,this._pointDistanceLine=null,this.applyProps(e)}get testData(){return this._renderer}createResources(){this._ensureRenderer()}destroyResources(){this._disposeRenderer()}updateVisibility(){this._syncRenderer(),this._syncHeightManifold(),this._syncIntersectsLine(),this._syncPathVerticalPlane(),this._syncLineVerticalPlane(),this._syncPointDistance()}get angleCutoff(){return this._angleCutoff}set angleCutoff(e){this._angleCutoff!==e&&(this._angleCutoff=e,this._syncAngleCutoff())}get style(){return this._style}set style(e){this._style=e,this._syncStyle()}get heightManifoldTarget(){return this._heightManifoldEnabled?this._heightManifoldTarget:null}set heightManifoldTarget(e){d(e)?(j(this._heightManifoldTarget,e),this._heightManifoldEnabled=!0):this._heightManifoldEnabled=!1,this._syncRenderer(),this._syncHeightManifold()}set intersectsWorldUpAtLocation(e){if(m(e))return void(this.intersectsLine=null);const t=this.view.renderCoordsHelper.worldUpAtPosition(e,qh);this.intersectsLine=pc(e,t),this.intersectsLineInfinite=!0}get intersectsLine(){return this._intersectsLineEnabled?this._intersectsLine:null}set intersectsLine(e){d(e)?(Aa(e,this._intersectsLine),this._intersectsLineEnabled=!0):this._intersectsLineEnabled=!1,this._syncIntersectsLine(),this._syncRenderer()}get intersectsLineInfinite(){return this._intersectsLineInfinite}set intersectsLineInfinite(e){this._intersectsLineInfinite=e,this._syncIntersectsLineInfinite()}get lineVerticalPlaneSegment(){return this._lineVerticalPlaneSegment}set lineVerticalPlaneSegment(e){this._lineVerticalPlaneSegment=d(e)?Aa(e):null,this._syncLineVerticalPlane(),this._syncRenderer()}get pathVerticalPlane(){return this._pathVerticalPlaneBuffers}set pathVerticalPlane(e){this._pathVerticalPlaneBuffers=e,this._syncPathVerticalPlane(),this._syncLineVerticalPlane(),this._syncPointDistance(),this._syncRenderer()}get pointDistanceLine(){return this._pointDistanceLine}set pointDistanceLine(e){this._pointDistanceLine=d(e)?{origin:ma(e.origin),target:e.target?ma(e.target):null}:null,this._syncPointDistance(),this._syncRenderer()}_syncRenderer(){this.attached&&this.visible&&(this._intersectsLineEnabled||this._heightManifoldEnabled||d(this._pointDistanceLine)||d(this._pathVerticalPlaneBuffers))?this._ensureRenderer():this._disposeRenderer()}_ensureRenderer(){d(this._renderer)||(this._renderer=new Zh({renderCoordsHelper:this.view.renderCoordsHelper},{contrastControlEnabled:!0}),this._renderer.viewingMode=this.view.state.viewingMode,this._syncStyle(),this._syncHeightManifold(),this._syncIntersectsLine(),this._syncIntersectsLineInfinite(),this._syncPathVerticalPlane(),this._syncLineVerticalPlane(),this._syncPointDistance(),this._syncAngleCutoff(),this.view._stage&&this.view._stage.addRenderPlugin(this._renderer.renderSlots,this._renderer))}_syncStyle(){m(this._renderer)||(this._renderer.setParameters(this._style),this._style.intersectsLineRadius!=null&&(this._renderer.intersectsLineRadius=this._style.intersectsLineRadius))}_syncAngleCutoff(){m(this._renderer)||this._renderer.setParameters({angleCutoff:this._angleCutoff})}_syncHeightManifold(){m(this._renderer)||(this._renderer.heightManifoldEnabled=this._heightManifoldEnabled&&this.visible,this._heightManifoldEnabled&&(this._renderer.heightManifoldTarget=this._heightManifoldTarget))}_syncIntersectsLine(){m(this._renderer)||(this._renderer.intersectsLineEnabled=this._intersectsLineEnabled&&this.visible,this._intersectsLineEnabled&&(this._renderer.intersectsLineSegment=this._intersectsLine))}_syncIntersectsLineInfinite(){m(this._renderer)||(this._renderer.intersectsLineInfinite=this._intersectsLineInfinite)}_syncPathVerticalPlane(){m(this._renderer)||(this._renderer.pathVerticalPlaneEnabled=d(this._pathVerticalPlaneBuffers)&&this.visible,d(this._pathVerticalPlaneBuffers)&&(this._renderer.pathVerticalPlaneBuffers=this._pathVerticalPlaneBuffers))}_syncLineVerticalPlane(){m(this._renderer)||(this._renderer.lineVerticalPlaneEnabled=d(this._lineVerticalPlaneSegment)&&this.visible,d(this._lineVerticalPlaneSegment)&&(this._renderer.lineVerticalPlaneSegment=this._lineVerticalPlaneSegment))}_syncPointDistance(){if(m(this._renderer))return;const e=this._pointDistanceLine,t=d(e);this._renderer.pointDistanceEnabled=t&&e.target!=null&&this.visible,t&&(this._renderer.pointDistanceOrigin=e.origin,e.target!=null&&(this._renderer.pointDistanceTarget=e.target))}_disposeRenderer(){d(this._renderer)&&this.view._stage&&(this.view._stage.removeRenderPlugin(this._renderer),this._renderer=null)}};const qh=M();let Ba=class extends ka{constructor(e){super(e),this._ray=Hi(),this._isWorldDown=!1,this._start=M(),this._end=F(1,0,0),this._width=1,this._color=Pi(1,0,1,1),this._polygonOffset=!1,this._writeDepthEnabled=!0,this._innerWidth=0,this._innerColor=Pi(1,1,1,1),this._stipplePattern=null,this._stippleOffColor=null,this._stipplePreferContinuous=!0,this._falloff=0,this._extensionType=Me.LINE,this._laserlineStyle=null,this._laserlineEnabled=!1,this._renderOccluded=R.OccludeAndTransparent,this._fadedExtensions=Nn,this._laserline=new Li({view:this.view}),this.applyProps(e)}destroy(){this._laserline.destroy(),super.destroy()}createObject3DResourceFactory(e){return{view:e,createResources:t=>this._createObject3DResources(t),destroyResources:t=>this._destroyExternalResources(t),recreateGeometry:(t,i)=>this._recreateObject3DGeometry(t,i),cameraChanged:()=>this._updateGeometry()}}createDrapedResourceFactory(e){return{view:e,createResources:()=>this._createDrapedResources(),destroyResources:t=>this._destroyExternalResources(t),recreateGeometry:t=>this._recreateDrapedGeometry(t)}}updateVisibility(e){super.updateVisibility(e),this._laserline.visible=e}onAttachedChange(){this._laserline.attached=this._laserlineAttached}setStartEndFromWorldDownAtLocation(e){this._isWorldDown=!0,j(this._start,e),this.view.renderCoordsHelper.worldUpAtPosition(e,this._end),V(this._end,e,this._end),es(this._start,this._end,this._ray),this._updateGeometry()}get start(){return this._start}set start(e){this._isWorldDown=!1,si(this._start,e)||(j(this._start,e),es(this._start,this._end,this._ray),this._updateGeometry())}get end(){return this._end}set end(e){this._isWorldDown=!1,si(this._end,e)||(j(this._end,e),es(this._start,this._end,this._ray),this._updateGeometry())}get width(){return this._width}set width(e){e!==this._width&&(this._width=e,this._updateMaterial())}get color(){return this._color}set color(e){Fe(e,this._color)||(Ue(this._color,e),this._updateMaterial())}get polygonOffset(){return this._polygonOffset}set polygonOffset(e){e!==this._polygonOffset&&(this._polygonOffset=e,this._updateMaterial())}get writeDepthEnabled(){return this._writeDepthEnabled}set writeDepthEnabled(e){this._writeDepthEnabled!==e&&(this._writeDepthEnabled=e,this._updateMaterial())}get innerWidth(){return this._innerWidth}set innerWidth(e){e!==this._innerWidth&&(this._innerWidth=e,this._updateMaterial())}get innerColor(){return this._innerColor}set innerColor(e){Fe(e,this._innerColor)||(Ue(this._innerColor,e),this._updateMaterial())}get stipplePattern(){return this._stipplePattern}set stipplePattern(e){const t=d(e)!==d(this._stipplePattern);this._stipplePattern=e,t?this.recreate():this._updateMaterial()}get stippleOffColor(){return this._stippleOffColor}set stippleOffColor(e){(m(e)||m(this._stippleOffColor)||!Fe(e,this._stippleOffColor))&&(this._stippleOffColor=d(e)?Yr(e):null,this._updateMaterial())}get stipplePreferContinuous(){return this._stipplePreferContinuous}set stipplePreferContinuous(e){e!==this._stipplePreferContinuous&&(this._stipplePreferContinuous=e,this._updateMaterial())}get falloff(){return this._falloff}set falloff(e){e!==this._falloff&&(this._falloff=e,this._updateMaterial())}get extensionType(){return this._extensionType}set extensionType(e){e!==this._extensionType&&(this._extensionType=e,this.recreateGeometry())}get _laserlineAttached(){return this._laserlineEnabled&&d(this._laserlineStyle)&&this.attached&&!this.isDraped}get laserlineStyle(){return this._laserlineStyle}set laserlineStyle(e){this._laserlineStyle=e,this._laserline.attached=this._laserlineAttached,d(e)&&(this._laserline.style=e)}get laserlineEnabled(){return this._laserlineEnabled}set laserlineEnabled(e){this._laserlineEnabled!==e&&(this._laserlineEnabled=e,this._laserline.attached=this._laserlineAttached)}get renderOccluded(){return this._renderOccluded}set renderOccluded(e){e!==this._renderOccluded&&(this._renderOccluded=e,this._updateMaterial())}get _normalizedRenderOccluded(){return this.isDraped&&this._renderOccluded===R.OccludeAndTransparentStencil?R.OccludeAndTransparent:this._renderOccluded}get fadedExtensions(){return this._fadedExtensions}set fadedExtensions(e){this._fadedExtensions=se(e,Nn),this.recreateGeometry()}_updateMaterial(){var t,i;const{materialParameters:e}=this;(t=Oe(this.object3dResources.resources))==null||t.material.setParameters(e),(i=Oe(this.drapedResources.resources))==null||i.material.setParameters(e)}get materialParameters(){return{width:this._width,color:this._color,stippleOffColor:this._stippleOffColor,stipplePattern:this._stipplePattern,stipplePreferContinuous:this._stipplePreferContinuous,innerWidth:this._innerWidth,innerColor:this._innerColor,falloff:this._falloff,hasPolygonOffset:this._polygonOffset,renderOccluded:this._normalizedRenderOccluded,writeDepth:this._writeDepthEnabled}}_createObject3DResources(e){const t=new it(this.materialParameters),i=new Array;return this._createObject3DGeometry(t,e,i),{material:t,geometries:i,forEach:s=>{s(t),i.forEach(s)}}}_destroyExternalResources(e){e.geometries=[],e.material.dispose()}_recreateObject3DGeometry(e,t){e.geometries.length=0,this._createObject3DGeometry(e.material,t,e.geometries)}_createObject3DGeometry(e,t,i){const s=this._createGeometry(e);i.push(s),t.addGeometry(s),this._updateVerticesObject3D(t)}_createDrapedResources(){const e=new it(this.materialParameters);return{material:e,geometries:[this._createDrapedGeometry(e)]}}_recreateDrapedGeometry(e){e.geometries=[this._createDrapedGeometry(e.material)]}_createDrapedGeometry(e){const t=this._createGeometry(e);this._updateVerticesDraped(t);const i=new ja(t,{boundingInfo:t.boundingInfo});return i.computeBoundingSphere(i.transformation,i.boundingSphere),i}_createGeometry(e){const t=this.extensionType===Me.FADED,i=t?[M(),M(),M(),M()]:[M(),M()];return je(e,i,null,t?[1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,0]:null)}_updateGeometry(){if(this.isDraped)this.drapedResources.recreateGeometry();else{const e=Oe(this.object3dResources.object);e&&this._updateVerticesObject3D(e)}}_updateVerticesObject3D(e){const t=this._lineSegment;this._updateVertexAttributesObject3D(e,t),this._laserline.intersectsLine=t}_updateVerticesDraped(e){this._updateVertexAttributesDraped(e,this._lineSegment)}get _lineSegment(){return this._extensionType===Me.FADED?this._updateLineSegmentFinite(zn):this._updateLineSegmentInfinite(this._extensionType,zn)}_updateLineSegmentFinite(e){return Ra(this._start,this._end,e)}_updateLineSegmentInfinite(e,t){const i=this.view.state.camera;switch(Zr(this._ray,ft),e){case Me.LINE:ft.c0=-Number.MAX_VALUE;break;case Me.RAY:case Me.GROUND_RAY:{const r=this._ray.origin,o=se(this.view.elevationProvider.getElevation(r[0],r[1],r[2],this.view.renderCoordsHelper.spatialReference,"ground"),0),l=this.view.renderCoordsHelper.getAltitude(r);this._isWorldDown&&l<o&&pl(ft.ray.direction,ft.ray.direction),this._extensionType===Me.GROUND_RAY&&o!=null&&(ft.c1=Math.abs(l-o));break}}if(!qr(i.frustum,ft))return this._updateLineSegmentFinite(t);const s=Qr(ft,Et),n=Jr(ft,Qh);return Ra(s,n,t)}_updateVertexAttributesObject3D(e,t){var n;const i=(n=e.geometries[0].getMutableAttribute(A.POSITION))==null?void 0:n.data;if(!i)return;let s=0;for(const r of this._lineVertices(t))i[s++]=r[0],i[s++]=r[1],i[s++]=r[2];e.geometryVertexAttrsUpdated(e.geometries[0])}_updateVertexAttributesDraped(e,t){var n;const i=(n=e.getMutableAttribute(A.POSITION))==null?void 0:n.data;if(!i)return;let s=0;for(const r of this._lineVertices(t))i[s++]=r[0],i[s++]=r[1],i[s++]=Ta;e.invalidateBoundingInfo()}*_lineVertices(e){this.extensionType===Me.FADED?(yield Pt(e,-this.fadedExtensions.start,Et),yield Pt(e,0,Et),yield Pt(e,1,Et),yield Pt(e,1+this.fadedExtensions.end,Et)):(yield Pt(e,0,Et),yield Pt(e,1,Et))}};const ft=Xr(),Et=M(),Qh=M(),zn=$i();var Me;(function(a){a[a.LINE=0]="LINE",a[a.RAY=1]="RAY",a[a.GROUND_RAY=2]="GROUND_RAY",a[a.FADED=3]="FADED"})(Me||(Me={}));const Fn=1/3,Nn={start:Fn,end:Fn};let Jh=class extends ka{constructor(e){super(e),this._location=M(),this._direction=F(1,0,0),this._width=1,this._offset=1,this._length=18,this._color=Pi(1,0,1,1),this._renderOccluded=R.OccludeAndTransparent,this.applyProps(e)}createObject3DResourceFactory(e){return{view:e,createResources:t=>this._createObject3DResources(t),destroyResources:t=>this._destroyObject3DResources(t),recreateGeometry:(t,i)=>this._recreateObject3DGeometry(t,i),cameraChanged:()=>this._updateGeometry()}}createDrapedResourceFactory(e){return{view:e,createResources:()=>this._createDrapedResources(),destroyResources:t=>this._destroyDrapedResources(t),recreateGeometry:t=>this._recreateDrapedGeometry(t)}}get location(){return this._location}set location(e){si(this._location,e)||(j(this._location,e),this._updateGeometry())}get direction(){return this._direction}set direction(e){si(this._direction,e)||(j(this._direction,e),this._updateGeometry())}setDirectionFromPoints(e,t){re(this._direction,V(this._direction,t,e)),this._updateGeometry()}get width(){return this._width}set width(e){e!==this._width&&(this._width=e,this._updateMaterial())}get offset(){return this._offset}set offset(e){e!==this._offset&&(this._offset=e,this._updateGeometry())}get length(){return this._length}set length(e){e!==this._length&&(this._length=e,this._updateGeometry())}get color(){return this._color}set color(e){Fe(e,this._color)||(Ue(this._color,e),this._updateMaterial())}get renderOccluded(){return this._renderOccluded}set renderOccluded(e){e!==this._renderOccluded&&(this._renderOccluded=e,this._updateMaterial())}_createObject3DResources(e){const t=new it(this.materialParameters),i=new Array;return this._createObject3DGeometry(t,e,i),{material:t,geometries:i,forEach:s=>{s(t),i.forEach(s)}}}_destroyObject3DResources(e){e.geometries.length=0,e.material.dispose()}_recreateObject3DGeometry(e,t){e.geometries.length=0,this._createObject3DGeometry(e.material,t,e.geometries)}_createObject3DGeometry(e,t,i){const[s,n]=this._createGeometries(e);t.addGeometry(s),t.addGeometry(n),i.push(s),i.push(n),this._updateVerticesObject3D(t)}_createDrapedResources(){const e=new it(this.materialParameters),t=G(()=>this.view.state.contentPixelRatio,()=>{this.drapedResources.recreateGeometry()});return{material:e,geometries:this._createDrapedGeometry(e),pixelRatioHandle:t}}_destroyDrapedResources(e){e.pixelRatioHandle.remove(),e.geometries=[],e.material.dispose()}_recreateDrapedGeometry(e){e.geometries=this._createDrapedGeometry(e.material)}_createDrapedGeometry(e){const t=this._createGeometries(e);this._updateVerticesDraped(t);const i=t.map(s=>new ja(s,{boundingInfo:s.boundingInfo}));for(const s of i)s.computeBoundingSphere(s.transformation,s.boundingSphere);return i}_createGeometries(e){return[je(e,[M(),M()]),je(e,[M(),M()])]}_updateMaterial(){var t,i;const{materialParameters:e}=this;(t=Oe(this.object3dResources.resources))==null||t.material.setParameters(e),(i=Oe(this.drapedResources.resources))==null||i.material.setParameters(e)}get materialParameters(){return{width:this._width,color:this._color,renderOccluded:this._renderOccluded}}_updateGeometry(){if(this.isDraped)this.drapedResources.recreateGeometry();else{const e=Oe(this.object3dResources.object);e&&this._updateVerticesObject3D(e)}}_updateVerticesObject3D(e){const t=this.view.state.camera;t.projectToScreen(this.location,aa),X(Ye,this.location,this.direction),t.projectToScreen(Ye,kt),ua(kt,Pr(kt,kt,aa)),this._updateVertexAttributesObject3D(t,e,0,aa,kt,1),this._updateVertexAttributesObject3D(t,e,1,aa,kt,-1)}_updateVertexAttributesObject3D(e,t,i,s,n,r){var u;const o=t.geometries[i],l=(u=o.getMutableAttribute(A.POSITION))==null?void 0:u.data;if(!l)return;const{start:c,end:p}=this._computeStartEnd(n,s,r,this.offset,this.width,this.length);e.unprojectFromScreen(fa(c),Ye),l[0]=Ye[0],l[1]=Ye[1],l[2]=Ye[2],e.unprojectFromScreen(fa(p),Ye),l[3]=Ye[0],l[4]=Ye[1],l[5]=Ye[2],t.geometryVertexAttrsUpdated(o)}_updateVerticesDraped(e){const{view:{basemapTerrain:{overlayManager:t},state:{contentPixelRatio:i}}}=this,{location:s,width:n,length:r,offset:o}=this,l=Kh;l.spatialReference=C(t.renderer.spatialReference),l.x=s[0],l.y=s[1];const c=t.overlayPixelSizeInMapUnits(l)*i,p=n*c,u=r*c,g=o*c;this._updateVertexAttributesDraped(e[0],p,u,g,-1),this._updateVertexAttributesDraped(e[1],p,u,g,1)}_updateVertexAttributesDraped(e,t,i,s,n){var u;const r=(u=e.getMutableAttribute(A.POSITION))==null?void 0:u.data;if(!r)return;const{location:o,direction:l}=this,{start:c,end:p}=this._computeStartEnd(l,o,n,s,t,i);r[0]=c[0],r[1]=c[1],r[2]=Ta,r[3]=p[0],r[4]=p[1],r[5]=Ta,e.invalidateBoundingInfo()}_computeStartEnd(e,t,i,s,n,r){const o=ca(jn,Ie(jn,e[1]*i,e[0]*-i),s+n/2),l=yi(ia,yi(ia,yi(ia,t,ca(ia,e,r/2)),o),o);return{start:l,end:yi(Hn,l,ca(Hn,e,-r))}}};const Ye=M(),jn=Je(),ia=Je(),Hn=Je(),aa=tt(),kt=tt(),Kh=Ht(0,0,void 0,null);let Os=class{constructor(e){this.view=null,this._geometry=null,this._size=3,this._color=Te(1,0,1,1),this._pixelSnappingEnabled=!0,this._primitive="square",this._outlineSize=1,this._outlineColor=Te(1,1,1,1),this._elevationInfo=null,this._resources=new fo({view:e.view,createResources:i=>this._createResources(i),destroyResources:i=>this._destroyResources(i),recreateGeometry:(i,s)=>{i.geometry=this._recreateGeometry(s,i.material)}});let t=!0;for(const i in e)i in this?i==="attached"?t=e[i]??!1:this[i]=e[i]:console.error("Cannot set unknown property",i);this.attached=t}destroy(){this._resources.destroy()}get visible(){return this._resources.visible}set visible(e){this._resources.visible=e}get attached(){return this._resources.attached}set attached(e){this._resources.attached=e}get geometry(){return this._geometry}set geometry(e){this._geometry=e,this._resources.recreateGeometry()}get size(){return this._size}set size(e){if(e!==this._size){const t=this._preferredTextureSize;this._size=e,t<this._preferredTextureSize?d(this._resources)&&this._resources.recreate():this._updateSizeAttribute()}}get color(){return this._color}set color(e){Fe(e,this._color)||(Ue(this._color,e),this._updateMaterial())}get pixelSnappingEnabled(){return this._pixelSnappingEnabled}set pixelSnappingEnabled(e){this._pixelSnappingEnabled!==e&&(this._pixelSnappingEnabled=e,this._updateMaterial())}get primitive(){return this._primitive}set primitive(e){this._primitive!==e&&(this._primitive=e,d(this._resources)&&this._resources.recreate())}get outlineSize(){return this._outlineSize}set outlineSize(e){e!==this._outlineSize&&(this._outlineSize=e,this._updateMaterial())}get outlineColor(){return this._outlineColor}set outlineColor(e){Fe(e,this._outlineColor)||(Ue(this._outlineColor,e),this._updateMaterial())}get elevationInfo(){return this._elevationInfo}set elevationInfo(e){this._elevationInfo=e,this._resources&&this._resources.recreateGeometry()}_updateMaterial(){const e=this._resources.resources;m(e)||e.material.setParameters(this._materialParameters(e.texture.id))}_updateSizeAttribute(){const e=this._resources.resources,t=this._resources.object;if(m(e)||m(t))return;const i=e.geometry;if(m(i))return;const s=i.getMutableAttribute(A.SIZE).data,n=this._geometrySize;s[0]=n,s[1]=n,t.geometryVertexAttrsUpdated(t.geometries[0])}_materialParameters(e){return{color:this._color,textureIsSignedDistanceField:!0,distanceFieldBoundingBox:ed,occlusionTest:!1,outlineColor:this._outlineColor,outlineSize:this._outlineSize,textureId:e,polygonOffset:!1,shaderPolygonOffset:0,drawInSecondSlot:!0,depthEnabled:!1,pixelSnappingEnabled:this.pixelSnappingEnabled}}get _geometrySize(){return this._size/Mi}_recreateGeometry(e,t){const i=this._createRenderGeometry(t);return d(i)&&e.addGeometry(i),i}_createResources(e){const t=yc(this._primitive,this._preferredTextureSize),i=new bc(this._materialParameters(t.id)),s=this._recreateGeometry(e,i);return{material:i,texture:t,geometry:s,forEach(n){n(t),n(i),d(this.geometry)&&n(this.geometry)}}}_destroyResources(e){e.geometry=null,e.material.dispose(),e.texture.dispose()}get _preferredTextureSize(){return va(ul(2*this._geometrySize),16,128)}calculateMapBounds(e){if(m(this._resources.resources)||m(this._resources.resources.geometry))return!1;const t=this._resources.resources.geometry.vertexAttributes.get(A.POSITION);return Ei(t.data,this.view.renderCoordsHelper.spatialReference,Un,this.view.spatialReference),ps(e,Un),!0}_createRenderGeometry(e){const t=this.geometry;if(m(t))return null;const{renderCoordsHelper:i,elevationProvider:s}=this.view,n=Ha(t,s,ci.fromElevationInfo(this.elevationInfo),i),r=B(w.get(),t.x,t.y,n),o=w.get();Ei(r,t.spatialReference,o,i.spatialReference);const l=this._geometrySize;return Mc(e,null,o,null,[l,l],[0,0,0,1])}};const Mi=Sc,ed=[Mi/2,Mi/2,1-Mi/2,1-Mi/2],Un=M();let td=class extends ka{constructor(e){super(e),this._maxSize=0,this._position=M(),this._up=M(),this._right=M(),this._renderOccluded=R.OccludeAndTransparent,this._color=Te(1,0,0,1),this._outlineColor=Te(0,0,0,1),this._outlineSize=0,this._size=32,this._outlineRenderOccluded=R.Opaque,this.applyProps(e)}createObject3DResourceFactory(e){return{view:e,createResources:t=>this._createObject3DResources(t),destroyResources:t=>this._destroyObject3DResources(t),cameraChanged:()=>this._updateTransformObject3D()}}createDrapedResourceFactory(e){return{view:e,createResources:()=>this._createDrapedResources(),destroyResources:t=>this._destroyDrapedResources(t)}}get renderOccluded(){return this._renderOccluded}set renderOccluded(e){e!==this._renderOccluded&&(this._renderOccluded=e,this._updateQuadMaterial())}get color(){return this._color}set color(e){Ue(this._color,e),this._updateQuadMaterial()}get outlineColor(){return this._outlineColor}set outlineColor(e){Ue(this._outlineColor,e),this._updateOutlineMaterial()}get outlineSize(){return this._outlineSize}set outlineSize(e){const t=this._outlineSize===0!=(e===0);this._outlineSize=e,t?this.recreateGeometry():this._updateOutlineMaterial()}get size(){return this._size}set size(e){e!==this._size&&(this._size=e,this._updateTransform())}get outlineRenderOccluded(){return this._outlineRenderOccluded}set outlineRenderOccluded(e){this._outlineRenderOccluded=e,this._updateOutlineMaterial()}set geometry({previous:e,center:t,next:i}){this._maxSize=Math.min(ga(t,e),ga(t,i))/3,re(this._up,V(this._up,e,t)),re(this._right,V(this._right,i,t)),j(this._position,t),this.recreateGeometry()}_createObject3DResources(e){const t=new ze(this._quadMaterialParameters),i=this._outlineSize===0?void 0:new it(this._outlineMaterialParameters);return this._createObject3DGeometries(e,t,i),{quadMaterial:t,outlineMaterial:i,forEach:s=>{s(t),i&&s(i)}}}_destroyObject3DResources(e){var t;e.quadMaterial.dispose(),(t=e.outlineMaterial)==null||t.dispose()}_createObject3DGeometries(e,t,i){if(si(this._up,us)&&si(this._right,us))return;const s=this._createGeometries(t,i);for(const n of s)e.addGeometry(n);this._updateTransformObject3D(e)}_createDrapedResources(){const e=new ze(this._quadMaterialParameters),t=this._outlineSize===0?void 0:new it(this._outlineMaterialParameters),i=this._createGeometries(e,t).map(s=>new ja(s,{boundingInfo:s.boundingInfo}));return this._setTransformDraped(i),{quadMaterial:e,outlineMaterial:t,geometries:i,pixelRatioHandle:G(()=>this.view.state.contentPixelRatio,()=>{this.drapedResources.recreateGeometry()})}}_destroyDrapedResources(e){var t;e.pixelRatioHandle.remove(),e.geometries=[],(t=e.outlineMaterial)==null||t.dispose(),e.quadMaterial.dispose()}_createGeometries(e,t){const{up:i,right:s,corner:n}=this._getVertices(),r=this._quadGeometryData(i,s,n,e);return t?[r,je(t,[i,n,s])]:[r]}_getVertices(){let e=this._up,t=this._right;const i=X(w.get(),e,t);return this.isDraped&&(e=j(w.get(),e),t=j(w.get(),t),e[2]=0,t[2]=0,i[2]=0),{up:e,right:t,corner:i}}_updateTransform(){this.isDraped?this.drapedResources.recreateGeometry():this._updateTransformObject3D()}_updateTransformObject3D(e=Oe(this.object3dResources.object)){if(!e)return;const t=this.view.state.camera,i=this._size*t.computeScreenPixelSizeAt(this._position),s=Math.min(this._maxSize,i);ya(vt,this._position),Ai(vt,vt,[s,s,s]),e.transformation=vt}_setTransformDraped(e){if(e.length===0)return;const{view:{basemapTerrain:{overlayManager:t},state:{contentPixelRatio:i}}}=this,{_position:s,_size:n}=this,r=j(w.get(),s);r[2]=Ta;const o=id;o.spatialReference=C(t.renderer.spatialReference),o.x=r[0],o.y=r[1];const l=n*(t.overlayPixelSizeInMapUnits(o)*i),c=Math.min(this._maxSize,l);ya(vt,r),Ai(vt,vt,[c,c,1]);for(const p of e)p.updateTransformation(u=>{Ls(u,vt)})}_quadGeometryData(e,t,i,s){return new io(s,[[A.POSITION,new ws([0,0,0,...t,...e,...i],3,!0)]],[[A.POSITION,[0,1,2,1,2,3]]])}get _quadMaterialParameters(){return{color:this._color,transparent:!0,writeDepth:!1,polygonOffset:!0,renderOccluded:this._renderOccluded}}_updateQuadMaterial(){var e,t;(e=Oe(this.object3dResources.resources))==null||e.quadMaterial.setParameters(this._quadMaterialParameters),(t=Oe(this.drapedResources.resources))==null||t.quadMaterial.setParameters(this._quadMaterialParameters)}get _outlineMaterialParameters(){return{color:this._outlineColor,width:this._outlineSize,renderOccluded:this._outlineRenderOccluded}}_updateOutlineMaterial(){var e,t,i,s;(t=(e=Oe(this.object3dResources.resources))==null?void 0:e.outlineMaterial)==null||t.setParameters(this._outlineMaterialParameters),(s=(i=Oe(this.drapedResources.resources))==null?void 0:i.outlineMaterial)==null||s.setParameters(this._outlineMaterialParameters)}};const vt=fe(),id=Ht(0,0,void 0,null);let Gi=class extends Xl{sortUniqueHints(e){return e.sort((t,i)=>(i instanceof mn?i.length:0)-(t instanceof mn?t.length:0))}visualizeIntersectionPoint(e,t){const{spatialReference:i,view:s}=t;return xe(new Os({view:s,primitive:"circle",geometry:fn(e.intersectionPoint,i),elevationInfo:e.isDraped?un:_n,size:20,outlineSize:2,color:[0,0,0,0],outlineColor:J.toUnitRGBA(ee.orange),pixelSnappingEnabled:!1}))}visualizePoint(e,t){const{view:i,spatialReference:s}=t,n=this._alignPoint(e.point,e.domain,t);return xe(new Os({view:i,primitive:"circle",geometry:fn(n,s),elevationInfo:this._hintElevationInfo(e,t),size:20,outlineSize:2,color:[0,0,0,0],outlineColor:J.toUnitRGBA(ee.orange),pixelSnappingEnabled:!1}))}visualizeLine(e,t){const{view:i,spatialReference:s}=t,n=this._alignPoint(e.lineStart,e.domain,t),r=this._alignPoint(e.lineEnd,e.domain,t);return xe(this._createLineSegmentHint(e.type,n,r,s,this._hintElevationInfo(e,t),i,e.isDraped,e.fadeLeft,e.fadeRight))}visualizeParallelSign(e,t){const{view:i,spatialReference:s}=t,{isDraped:n}=e,r=this._hintElevationInfo(e,t),o=this._alignPoint(e.lineStart,e.domain,t),l=this._alignPoint(e.lineEnd,e.domain,t),c=At(o,s,r,i,n),p=At(l,s,r,i,n),u=$r(p,c,p,.5),g=new Jh({view:i,attached:!1,offset:ee.parallelLineHintOffset,length:ee.parallelLineHintLength,width:ee.parallelLineHintWidth,color:J.toUnitRGBA(ee.orange),location:u,renderOccluded:n?R.OccludeAndTransparent:R.Opaque,isDraped:n,renderGroup:wi.SnappingHint});return g.setDirectionFromPoints(c,u),g.attached=!0,xe(g)}visualizeRightAngleQuad(e,t){const{view:i,spatialReference:s}=t,n=this._hintElevationInfo(e,t),{isDraped:r}=e,o=this._alignPoint(e.previousVertex,e.domain,t),l=this._alignPoint(e.centerVertex,e.domain,t),c=this._alignPoint(e.nextVertex,e.domain,t),p=At(o,s,n,i,r),u=At(l,s,n,i,r),g=At(c,s,n,i,r);return xe(new td({view:i,attached:!0,color:r?J.toUnitRGBA(ee.orangeTransparent):J.toUnitRGBA(ee.orange),renderOccluded:r?R.OccludeAndTransparent:R.Transparent,outlineRenderOccluded:r?R.OccludeAndTransparent:R.Opaque,outlineColor:J.toUnitRGBA(ee.orange),outlineSize:ee.rightAngleHintOutlineSize,size:ee.rightAngleHintSize,isDraped:r,geometry:{previous:p,center:u,next:g},renderGroup:wi.SnappingHint}))}_createLineSegmentHint(e,t,i,s,n,r,o=!1,l=!0,c=!0){const p=At(t,s,n,r,o),u=At(i,s,n,r,o),g=new Ba({view:r,extensionType:Me.FADED,start:p,end:u,isDraped:o,color:J.toUnitRGBA(ee.orange),renderOccluded:o?R.OccludeAndTransparent:R.Opaque,renderGroup:wi.SnappingHint});switch(e){case Ka.TARGET:g.width=ee.lineHintWidthTarget,g.fadedExtensions={start:0,end:ee.lineHintFadedExtensions};break;case Ka.REFERENCE_EXTENSION:g.width=ee.lineHintWidthReference,g.fadedExtensions={start:0,end:0};break;case Ka.REFERENCE:g.width=ee.lineHintWidthReference,g.fadedExtensions={start:l?ee.lineHintFadedExtensions:0,end:c?ee.lineHintFadedExtensions:0}}return g.attached=!0,g}_alignPoint(e,t,i){const s=this._getSelfSnappingZ(t,i);return m(s)?e:Zl(e[0],e[1],s)}_hintElevationInfo(e,t){return d(this._getSelfSnappingZ(e.domain,t))?C(t.selfSnappingZ).elevationInfo:e.isDraped?un:_n}_getSelfSnappingZ(e,{selfSnappingZ:t}){return e===ql.SELF&&d(t)?t.value:null}};function At(a,e,t,i,s,n=M()){if(s){const r=i.basemapTerrain.overlayManager.renderer.spatialReference;Ei(a,e,n,r)}else kr(a,e,t,i,n);return n}const P={main:new J([255,127,0]),selected:new J([255,255,255]),staged:new J([12,207,255]),outline:new J([0,0,0,.5]),selectedOutline:new J([255,255,255])},ti=.3;function Ii(a,e){const t=a.clone();return t.a*=e,t}function ad(a,e){const t=a.clone(),i=_l(t);i.s*=e;const s=gl(i);return t.r=s.r,t.g=s.g,t.b=s.b,t}function De(a,e){if(e)for(const t in e)a[t]=e[t]}let sd=class{constructor(e){this.color=P.main,this.height=90,this.coneHeight=40,this.coneWidth=23,this.width=3,this.renderOccluded=R.Opaque,De(this,e)}},as=class{constructor(e){this.size=11,this.outlineSize=1,this.collisionPadding=9,this.color=P.main,this.outlineColor=P.outline,this.renderOccluded=R.Opaque,this.hoverOutlineColor=P.selectedOutline,De(this,e)}apply(e,t){const i=this[e];t.setParameters({color:wt(i),transparent:e!=="color"||i.a<1,renderOccluded:this.renderOccluded})}},nd=class{constructor(e){this.size=40,this.height=.2,this.offset=.25,this.collisionPadding=2,this.color=Ii(P.main,.5),this.hoverColor=P.main,this.renderOccluded=R.Transparent,this.minSquaredEdgeLength=900,De(this,e)}apply(e,t){const i=this[e];t.setParameters({color:wt(i),transparent:i.a<1,renderOccluded:this.renderOccluded})}},rd=class{constructor(e){this.vertex=new as({color:P.main,outlineColor:P.outline}),this.edge=new as({color:ad(Ii(P.main,2/3),.5),outlineColor:Ii(P.outline,.5),size:8,collisionPadding:8}),this.selected=new as({color:P.selected,outlineColor:P.outline}),this.edgeOffset=new nd,De(this,e)}},Es=class{constructor(e){this.color=P.selected,this.width=1.5,this.stipplePattern=bs(5),this.stippleOffColor=P.outline,this.falloff=0,this.innerWidth=1.5,this.innerColor=P.selected,this.renderOccluded=R.OccludeAndTransparent,De(this,e)}apply(e){e.color=wt(this.color),e.width=this.width,e.stipplePattern=this.stipplePattern,e.stippleOffColor=wt(this.stippleOffColor),e.falloff=this.falloff,e.innerWidth=this.innerWidth,e.innerColor=wt(this.innerColor),e.renderOccluded=this.renderOccluded}},od=class{constructor(e){this.color=P.selected,this.size=4,this.outlineSize=1,this.outlineColor=P.outline,this.shape="square",De(this,e)}apply(e){e.color=wt(this.color),e.size=this.size,e.outlineSize=this.outlineSize,e.outlineColor=wt(this.outlineColor),e.primitive=this.shape}},Ca=class{constructor(e){this.innerColor=P.selected,this.innerWidth=1,this.glowColor=P.main,this.glowWidth=8,this.glowFalloff=8,this.globalAlpha=ti,this.globalAlphaContrastBoost=1.5,this.radius=3,this.heightFillColor=P.main,De(this,e)}apply(e,t=1){const i={glowColor:J.toUnitRGB(this.glowColor),glowFalloff:this.glowFalloff,glowWidth:this.glowWidth,innerColor:J.toUnitRGB(this.innerColor),innerWidth:this.innerWidth,globalAlpha:this.globalAlpha*t,globalAlphaContrastBoost:this.globalAlphaContrastBoost,intersectsLineRadius:this.radius};"style"in e?e.style=i:e.laserlineStyle=i}},ld=class{constructor(e){this.outline=new Es({color:P.outline,renderOccluded:R.OccludeAndTransparentStencil,stippleOffColor:P.selected,stipplePattern:bs(5),width:1.5,innerWidth:0}),this.staged=new Es({color:P.selected,renderOccluded:R.OccludeAndTransparentStencil,innerColor:P.staged,stippleOffColor:P.outline,stipplePattern:bs(5),width:1.5}),this.shadowStyle=new Ca({globalAlpha:ti,glowColor:P.main,glowFalloff:8,glowWidth:8,innerColor:P.selected,innerWidth:1}),De(this,e)}},cd=class{constructor(e){this.outline=new od({color:P.selected,outlineColor:P.outline,outlineSize:1,shape:"circle",size:4}),this.shadowStyle=new Ca({globalAlpha:ti,glowColor:P.main,glowFalloff:1.5,glowWidth:6,innerColor:P.selected,innerWidth:1,radius:2}),De(this,e)}};class hd extends Es{constructor(e){super(),this.extensionType=Me.GROUND_RAY,De(this,e)}}let dd=class{constructor(e){this.lineGraphics=new ld,this.pointGraphics=new cd,this.heightPlane=new Ca({globalAlpha:ti,glowColor:P.main,glowFalloff:8,glowWidth:8,innerColor:P.selected,innerWidth:1}),this.heightBox=new Ca({globalAlpha:ti,glowColor:P.main,glowFalloff:8,glowWidth:8,innerColor:P.selected,innerWidth:0,heightFillColor:P.main}),this.zVerticalLine=new hd({color:Ii(P.main,5*ti/3),falloff:2,innerColor:Ii(P.selected,0),renderOccluded:R.OccludeAndTransparent,stipplePattern:null,width:5,extensionType:Me.GROUND_RAY}),this.laserlineAlphaMultiplier=1.5,this.heightPlaneAngleCutoff=20,De(this,e)}},pd=class{constructor(e){this.visualElements=new dd,this.reshapeManipulators=new rd,this.zManipulator=new sd,De(this,e)}colorToVec4(e){return wt(e)}};function wt(a){return jt(J.toUnitRGBA(a))}const y=new pd;let Nt=class extends ka{constructor(e){super(e),this._attachmentOrigin=Ht(0,0,0,null),this._attachmentOriginDirty=!0,this.events=new ke,this._geometry=null,this._width=1,this._color=Pi(1,0,1,1),this._innerWidth=0,this._innerColor=Pi(1,1,1,1),this._stipplePattern=null,this._stippleOffColor=null,this._falloff=0,this._elevationInfo=null,this._laserlineStyle=null,this._laserlineEnabled=!1,this._renderOccluded=R.OccludeAndTransparentStencil,this._attachmentOrigin.spatialReference=e.view.spatialReference,this._laserline=new Li({view:e.view}),this.applyProps(e),this.attached=e.attached??!0}destroy(){this._laserline.destroy(),super.destroy()}createObject3DResourceFactory(e){return{view:e,createResources:t=>this._createObject3DResources(t),destroyResources:t=>this._destroyExternalResources(t),recreateGeometry:(t,i)=>{t.geometries.length=0,this._recreateGeometry(i,t.material,t.geometries)}}}createDrapedResourceFactory(e){return{view:e,createResources:()=>this._createDrapedResources(),destroyResources:t=>this._destroyExternalResources(t),recreateGeometry:t=>{t.geometries=this._createRenderGeometriesDraped(t.material),this._attachmentOriginChanged()}}}get _laserlineAttached(){return this.attached&&this.visible&&d(this._laserlineStyle)&&!this.isDraped&&this.laserlineEnabled}onAttachedChange(e){this._laserline.attached=this._laserlineAttached,e&&this._attachmentOriginChanged()}get geometry(){return this._geometry}set geometry(e){this._geometry=e,this.recreateGeometry()}get width(){return this._width}set width(e){e!==this._width&&(this._width=e,this._updateMaterial())}get color(){return this._color}set color(e){Fe(e,this._color)||(Ue(this._color,e),this._updateMaterial())}get innerWidth(){return this._innerWidth}set innerWidth(e){e!==this._innerWidth&&(this._innerWidth=e,this._updateMaterial())}get innerColor(){return this._innerColor}set innerColor(e){Fe(e,this._innerColor)||(Ue(this._innerColor,e),this._updateMaterial())}get stipplePattern(){return this._stipplePattern}set stipplePattern(e){const t=d(e)!==d(this._stipplePattern);this._stipplePattern=e,t?this.recreate():this._updateMaterial()}get stippleOffColor(){return this._stippleOffColor}set stippleOffColor(e){e&&this._stippleOffColor&&Fe(e,this._stippleOffColor)||(this._stippleOffColor=e?Yr(e):null,this._updateMaterial())}get falloff(){return this._falloff}set falloff(e){e!==this._falloff&&(this._falloff=e,this._updateMaterial())}get elevationInfo(){return this._elevationInfo}set elevationInfo(e){this._elevationInfo=e,this.recreateGeometry()}get laserlineStyle(){return this._laserlineStyle}set laserlineStyle(e){this._laserlineStyle=e,this._laserline.attached=this._laserlineAttached,d(e)&&(this._laserline.style=e)}get laserlineEnabled(){return this._laserlineEnabled}set laserlineEnabled(e){this._laserlineEnabled!==e&&(this._laserlineEnabled=e,this._laserline.attached=this._laserlineAttached)}get renderOccluded(){return this._renderOccluded}set renderOccluded(e){e!==this._renderOccluded&&(this._renderOccluded=e,this._updateMaterial())}get attachmentOrigin(){var i;if(!this._attachmentOriginDirty)return this._attachmentOrigin;const e=(i=Oe(this.object3dResources.resources))==null?void 0:i.geometries;if(!e||e.length===0)return null;B(Bt,0,0,0);let t=0;for(const s of e)s.computeAttachmentOrigin(kn)&&(X(Bt,Bt,kn),t++);return t===0?null:(pe(Bt,Bt,1/t),this.view.renderCoordsHelper.fromRenderCoords(Bt,this._attachmentOrigin),this._attachmentOriginDirty=!1,this._attachmentOrigin)}_updateMaterial(){d(this.object3dResources.resources)&&this.object3dResources.resources.material.setParameters(this._materialParameters),d(this.drapedResources.resources)&&this.drapedResources.resources.material.setParameters(this._materialParameters)}get _isClosed(){return d(this.geometry)&&this.geometry.type==="polygon"}get _materialParameters(){return{width:this._width,color:this._color,stippleOffColor:this._stippleOffColor,stipplePattern:this._stipplePattern,stipplePreferContinuous:!1,isClosed:this._isClosed,falloff:this._falloff,innerColor:this._innerColor,innerWidth:this._innerWidth,join:"round",hasPolygonOffset:!0,renderOccluded:this._normalizedRenderOccluded}}get _normalizedRenderOccluded(){return this.isDraped&&this._renderOccluded===R.OccludeAndTransparentStencil?R.OccludeAndTransparent:this._renderOccluded}_recreateGeometry(e,t,i){this._createRenderGeometries(t,i);for(const s of i)e.addGeometry(s);this._attachmentOriginChanged()}_attachmentOriginChanged(){this._attachmentOriginDirty=!0,this.events.emit("attachment-origin-changed")}_destroyExternalResources(e){e.geometries=[],e.material.dispose()}_createObject3DResources(e){const t=new it(this._materialParameters),i=new Array;return this._recreateGeometry(e,t,i),{material:t,geometries:i,forEach:s=>{s(t),i.forEach(s)}}}_createDrapedResources(){const e=new it(this._materialParameters);return{material:e,geometries:this._createRenderGeometriesDraped(e)}}_createRenderGeometriesDraped(e){const t=this.geometry;if(m(t)||m(this.view.basemapTerrain.spatialReference))return[];const i=wc(t,this.view.basemapTerrain.spatialReference),s=[];for(const{position:n}of i.lines){const r={overlayInfo:{spatialReference:this.view.basemapTerrain.spatialReference,renderCoordsHelper:this.view.renderCoordsHelper},attributeData:{position:n},removeDuplicateStartEnd:this._isClosed},o=new ja(On(e,r)),l=Lr(ud);ps(l,n),ml(o.boundingSphere,.5*(l[0]+l[3]),.5*(l[1]+l[4]),0,.5*Math.sqrt((l[3]-l[0])*(l[3]-l[0])+(l[4]-l[1])*(l[4]-l[1]))),s.push(o)}return s}calculateMapBounds(e){if(m(this.object3dResources.resources))return!1;const t=this.view.renderCoordsHelper;for(const i of this.object3dResources.resources.geometries){const s=i.vertexAttributes.get(A.POSITION),n=Pa(s.data.length);fl(s.data,t.spatialReference,0,n,this.view.spatialReference,0,s.data.length/3),ps(e,n)}return!0}_createRenderGeometries(e,t){const i=this.geometry;if(m(i))return;const s=xc(i,this.view.elevationProvider,this.view.renderCoordsHelper,ci.fromElevationInfo(this.elevationInfo??new Gr({mode:Hr(i,null)}))),n=new Array;for(const{position:r,mapPositions:o}of s.lines){const l={mapPositions:o,attributeData:{position:r},removeDuplicateStartEnd:this._isClosed};t.push(On(e,l)),n.push(r)}this._laserline.pathVerticalPlane=n}};const ud=Cr(),kn=M(),Bt=M();let _d=class extends Bs{constructor(e){super(e.view),this._resources=null,this._transform=fe()}get object(){return d(this._resources)?this._resources.object:null}get transform(){return this._transform}set transform(e){Ls(this._transform,e),d(this._resources)&&(this._resources.object.transformation=this._transform)}recreate(){this.attached&&this.createResources()}recreateGeometry(){if(m(this._resources))return;const e=this._resources.object,t=this.view._stage;t.removeMany(e.geometries),e.removeAllGeometries(),this.createGeometries(e),e.visible=this.visible,t.addMany(e.geometries)}createResources(){this.destroyResources();const e=this.view._stage;if(!e)return;const t=new zs({pickable:!1,updatePolicy:Na.SYNC});e.add(t);const i=new Fs({castShadow:!1});i.transformation=this._transform,this.createExternalResources(),this.createGeometries(i),e.addMany(i.geometries),this.forEachExternalMaterial(s=>e.add(s)),e.add(i),t.add(i),i.visible=this.visible,this._resources={layer:t,object:i}}destroyResources(){const e=this.view._stage;!m(this._resources)&&e&&(e.remove(this._resources.object),e.remove(this._resources.layer),this.forEachExternalMaterial(t=>{e.remove(t),t.dispose()}),e.removeMany(this._resources.object.geometries),this._resources.object.dispose(),this.destroyExternalResources(),this._resources=null)}updateVisibility(e){m(this._resources)||(this._resources.object.visible=e)}};function gd(a,e,t,i,s){const n=Pa(3*a.length),r=Pa(n.length);a.forEach((c,p)=>{n[3*p+0]=c[0],n[3*p+1]=c[1],n[3*p+2]=c.length>2?c[2]:0});const o=Oc(n,e,0,r,0,n,0,n.length/3,t,i,s),l=o!=null;return{numVertices:a.length,position:n,mapPositions:r,projectionSuccess:l,sampledElevation:o}}function md(a,e){if(!e.screenSizeEnabled)return;const t=a.vertex;Nc(t,e),t.uniforms.add(new he("perScreenPixelRatio",(i,s)=>s.camera.perScreenPixelRatio)),t.uniforms.add(new he("screenSizeScale",i=>i.screenSizeScale)),t.code.add(T`float computeRenderPixelSizeAt( vec3 pWorld ){
vec3 viewForward = - vec3(view[0][2], view[1][2], view[2][2]);
float viewDirectionDistance = abs(dot(viewForward, pWorld - cameraPosition));
return viewDirectionDistance * perScreenPixelRatio;
}
vec3 screenSizeScaling(vec3 position, vec3 anchor){
return position * screenSizeScale * computeRenderPixelSizeAt(anchor) + anchor;
}`)}function Ao(a){const e=new Ui,t=a.hasMultipassTerrain&&(a.output===U.Color||a.output===U.Alpha);e.include(ao,a),e.include(md,a),e.include(so,a);const{vertex:i,fragment:s}=e;return s.include(no),Hs(i,a),s.uniforms.add(new St("uColor",n=>n.color)),e.attributes.add(A.POSITION,"vec3"),e.varyings.add("vWorldPosition","vec3"),t&&e.varyings.add("depth","float"),a.screenSizeEnabled&&e.attributes.add(A.OFFSET,"vec3"),a.shadingEnabled&&(jc(i),e.attributes.add(A.NORMAL,"vec3"),e.varyings.add("vViewNormal","vec3")),i.code.add(T`
    void main(void) {
      vWorldPosition = ${a.screenSizeEnabled?"screenSizeScaling(offset, position)":"position"};
  `),a.shadingEnabled&&i.code.add(T`vec3 worldNormal = normal;
vViewNormal = (viewNormal * vec4(worldNormal, 1)).xyz;`),i.code.add(T`
    ${t?"depth = (view * vec4(vWorldPosition, 1.0)).z;":""}
    gl_Position = transformPosition(proj, view, vWorldPosition);
  }
  `),t&&e.include(ro,a),s.code.add(T`
    void main() {
      discardBySlice(vWorldPosition);
      ${t?"terrainDepthTest(gl_FragCoord, depth);":""}
    `),a.shadingEnabled?(s.uniforms.add(new ht("shadingDirection",n=>n.shadingDirection)),s.uniforms.add(new St("shadedColor",n=>fd(n.shadingTint,n.color))),s.code.add(T`vec3 viewNormalNorm = normalize(vViewNormal);
float shadingFactor = 1.0 - clamp(-dot(viewNormalNorm, shadingDirection), 0.0, 1.0);
vec4 finalColor = mix(uColor, shadedColor, shadingFactor);`)):s.code.add(T`vec4 finalColor = uColor;`),s.code.add(T`
      ${a.output===U.ObjectAndLayerIdColor?T`finalColor.a = 1.0;`:""}
      if (finalColor.a < ${T.float(Hc)}) {
        discard;
      }
      ${a.output===U.Alpha?T`gl_FragColor = vec4(finalColor.a);`:""}

      ${a.output===U.Color?T`gl_FragColor = highlightSlice(finalColor, vWorldPosition); ${a.transparencyPassType===at.Color?"gl_FragColor = premultiplyAlpha(gl_FragColor);":""}`:""}
    }
    `),e}function fd(a,e){const t=1-a[3],i=a[3]+e[3]*t;return i===0?(Rt[3]=i,Rt):(Rt[0]=(a[0]*a[3]+e[0]*e[3]*t)/i,Rt[1]=(a[1]*a[3]+e[1]*e[3]*t)/i,Rt[2]=(a[2]*a[3]+e[2]*e[3]*t)/i,Rt[3]=e[3],Rt)}const Rt=Dr(),vd=Object.freeze(Object.defineProperty({__proto__:null,build:Ao},Symbol.toStringTag,{value:"Module"}));let Ro=class To extends ki{initializeProgram(e){return new Bi(e.rctx,To.shader.get().build(this.configuration),Do)}_setPipelineState(e){const t=this.configuration,i=e===at.NONE,s=e===at.FrontFace;return Yi({blending:t.output!==U.Color&&t.output!==U.Alpha||!t.transparent?null:i?th:po(e),culling:uo(t.cullFace),depthTest:{func:s?ha.LESS:t.shadingEnabled?ha.LEQUAL:ha.LESS},depthWrite:i?t.writeDepth?_o:null:go(e),colorWrite:Xi,polygonOffset:i||s?null:ih})}initializePipeline(){return this._setPipelineState(this.configuration.transparencyPassType)}};Ro.shader=new Wi(vd,()=>Ni(()=>Promise.resolve().then(()=>fu),void 0));let $e=class extends oo{constructor(){super(...arguments),this.output=U.Color,this.cullFace=ie.None,this.transparencyPassType=at.NONE,this.hasSlicePlane=!1,this.transparent=!1,this.writeDepth=!0,this.screenSizeEnabled=!0,this.shadingEnabled=!0,this.hasMultipassTerrain=!1,this.cullAboveGround=!1}};h([N({count:U.COUNT})],$e.prototype,"output",void 0),h([N({count:ie.COUNT})],$e.prototype,"cullFace",void 0),h([N({count:at.COUNT})],$e.prototype,"transparencyPassType",void 0),h([N()],$e.prototype,"hasSlicePlane",void 0),h([N()],$e.prototype,"transparent",void 0),h([N()],$e.prototype,"writeDepth",void 0),h([N()],$e.prototype,"screenSizeEnabled",void 0),h([N()],$e.prototype,"shadingEnabled",void 0),h([N()],$e.prototype,"hasMultipassTerrain",void 0),h([N()],$e.prototype,"cullAboveGround",void 0);const Do=new Map([[A.POSITION,0],[A.NORMAL,1],[A.OFFSET,2]]);let As=class extends Uc{constructor(e){super(e,new bd),this.supportsEdges=!0,this._configuration=new $e,this._vertexAttributeLocations=Do}getConfiguration(e,t){return this._configuration.output=e,this._configuration.cullFace=this.parameters.cullFace,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.transparent=this.parameters.transparent,this._configuration.writeDepth=this.parameters.writeDepth,this._configuration.screenSizeEnabled=this.parameters.screenSizeEnabled,this._configuration.shadingEnabled=this.parameters.shadingEnabled,this._configuration.transparencyPassType=t.transparencyPassType,this._configuration.hasMultipassTerrain=t.multipassTerrain.enabled,this._configuration.cullAboveGround=t.multipassTerrain.cullAboveGround,this._configuration}intersect(e,t,i,s,n,r){if(this.parameters.screenSizeEnabled){const o=e.vertexAttributes.get(A.OFFSET);An(e,i,s,n,{applyToVertex:(c,p,u,g)=>{const v=B(Bn,o.data[3*g+0],o.data[3*g+1],o.data[3*g+2]),b=B(Sd,c,p,u);return pe(v,v,this.parameters.screenSizeScale*i.camera.computeRenderPixelSizeAt(v)),X(b,b,v),[b[0],b[1],b[2]]},applyToAabb:c=>{const p=vl(c,Bn);return yl(c,this.parameters.screenSizeScale*i.camera.computeRenderPixelSizeAt(p))}},r)}else An(e,i,s,n,void 0,r)}requiresSlot(e,t){if(t===U.Highlight)return e===Ae.OPAQUE_MATERIAL;if(t===U.Color||t===U.Alpha||t===U.ObjectAndLayerIdColor){let i=Ae.OPAQUE_MATERIAL;return this.parameters.transparent&&(i=this.parameters.writeDepth?Ae.TRANSPARENT_MATERIAL:Ae.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL),e===i||e===Ae.DRAPED_MATERIAL}return!1}createGLMaterial(e){return new yd(e)}createBufferWriter(){return new Md(this.parameters.screenSizeEnabled)}},yd=class extends kc{beginSlot(e){return this.ensureTechnique(Ro,e)}},bd=class extends lo{constructor(){super(...arguments),this.color=Te(1,1,1,1),this.shadingTint=Te(0,0,0,.25),this.shadingDirection=re(M(),[.5,-.5,-.5]),this.screenSizeScale=14,this.transparent=!1,this.writeDepth=!0,this.hasSlicePlane=!1,this.cullFace=ie.None,this.screenSizeEnabled=!1,this.shadingEnabled=!0}};class Md{constructor(e){this.screenSizeEnabled=e;const t=co().vec3f(A.POSITION).vec3f(A.NORMAL);this.screenSizeEnabled&&t.vec3f(A.OFFSET),this.vertexBufferLayout=t}allocate(e){return this.vertexBufferLayout.createBuffer(e)}elementCount(e){return e.indices.get(A.POSITION).length}write(e,t,i,s,n){if(Bc(i,this.vertexBufferLayout,e,t,s,n),this.screenSizeEnabled){if(!i.vertexAttributes.has(A.OFFSET))throw new Error(`${A.OFFSET} vertex attribute required for screenSizeEnabled ShadedColorMaterial`);{const r=i.vertexAttributes.get(A.OFFSET),o=i.indices.get(A.OFFSET);dh(r.size===3);const l=s.getField(A.OFFSET,hh);if(!l)throw new Error("unable to acquire view for "+A.OFFSET);Wc(o,r.data,t,l,n)}}}}const Bn=M(),Sd=M();let Wn=class extends _d{constructor(e){super(e),this.view=null,this._renderOccluded=R.OccludeAndTransparent,this._vertices=null,this._spatialReference=null,this._color=y.colorToVec4(y.reshapeManipulators.vertex.color),this._size=y.reshapeManipulators.vertex.size,this._outlineColor=y.colorToVec4(y.reshapeManipulators.vertex.outlineColor),this._outlineSize=y.reshapeManipulators.vertex.outlineSize,this._elevationInfo=null,this.applyProps(e)}get renderOccluded(){return this._renderOccluded}set renderOccluded(e){e!==this._renderOccluded&&(this._renderOccluded=e,this._updateMaterial(),this._updateOutlineMaterial())}get vertices(){return this._vertices}set vertices(e){this._vertices=e,this.recreateGeometry()}get spatialReference(){return this._spatialReference}set spatialReference(e){this._spatialReference=e,this.recreateGeometry()}get color(){return this._color}set color(e){Fe(e,this._color)||(Ue(this._color,e),this._updateMaterial())}get size(){return this._size}set size(e){e!==this._size&&(this._size=e,this._updateMaterial())}get outlineColor(){return this._outlineColor}set outlineColor(e){Fe(e,this._outlineColor)||(Ue(this._outlineColor,e),this._updateOutlineMaterial())}get outlineSize(){return this._outlineSize}set outlineSize(e){e!==this._outlineSize&&(this._outlineSize=e,this._updateOutlineMaterial())}get elevationInfo(){return this._elevationInfo}set elevationInfo(e){this._elevationInfo=e,this.recreateGeometry()}get _vertexMaterialParameters(){return{color:this._color,transparent:this._color[3]<1,screenSizeScale:this.size,renderOccluded:this._renderOccluded}}get _vertexOutlineMaterialParameters(){return{color:this._outlineColor,transparent:this._outlineColor[3]<1,screenSizeScale:this.size+2*this.outlineSize,renderOccluded:this._renderOccluded}}_updateMaterial(){this.attached&&this._vertexMaterial.setParameters(this._vertexMaterialParameters)}_updateOutlineMaterial(){this.attached&&this._vertexOutlineMaterial.setParameters(this._vertexOutlineMaterialParameters)}_createRenderGeometries(){const e=this.vertices;if(m(e)||e.length===0)return[];const t=.5,i=.5,s=gd(e,this.spatialReference,this.view.elevationProvider,this.view.renderCoordsHelper,ci.fromElevationInfo(this.elevationInfo)),n=[],r=s.numVertices,o=s.position;for(let l=0;l<r;++l){const c=B(wd,o[3*l+0],o[3*l+1],o[3*l+2]),p=Yn(this._vertexMaterial,t,c),u=Yn(this._vertexOutlineMaterial,i,c);n.push({vertexGeometry:p,vertexOutlineGeometry:u})}return n}createGeometries(e){const t=this._createRenderGeometries();for(const{vertexGeometry:i,vertexOutlineGeometry:s}of t)e.addGeometry(i),e.addGeometry(s)}createExternalResources(){this._vertexMaterial=new As({...this._vertexMaterialParameters,writeDepth:!0,cullFace:ie.Back,screenSizeEnabled:!0}),this._vertexOutlineMaterial=new As({...this._vertexOutlineMaterialParameters,transparent:!0,writeDepth:!0,cullFace:ie.Front,screenSizeEnabled:!0,shadingEnabled:!1})}destroyExternalResources(){this._vertexMaterial=null,this._vertexOutlineMaterial=null}forEachExternalMaterial(e){e(this._vertexMaterial),e(this._vertexOutlineMaterial)}};const wd=M();function Yn(a,e,t){return bi(a,e,16,16,{offset:t})}let we=class extends ke.EventedAccessor{constructor(e){super(e),this.tracking=!1,this.displaying=!1,this.isDraped=!1}};h([_({constructOnly:!0})],we.prototype,"graphic",void 0),h([_()],we.prototype,"tracking",void 0),h([_()],we.prototype,"displaying",void 0),h([_()],we.prototype,"isDraped",void 0),we=h([Q("esri.views.3d.layers.graphics.GraphicState")],we);let Wt=class extends ph{constructor(e){super(e),this._activeVertexVisualElement=null,this._createGraphicState=null,this._outlineVisualElement=null,this._verticesVisualElement=null,this._verticalLineVisualElement=null,this.geometryType=null,this.type="draw-3d"}initialize(){const{mode:e,offset:t}=this.elevationInfo;this.internalGraphicsLayer.elevationInfo=new Gr({mode:e,offset:t})}normalizeCtorArgs(e){if(!e.elevationInfo){const t=e.hasZ??!0;return{...e,elevationInfo:Fl(t)}}return e}initializeGraphic(e){const t=this._createGraphicState=new we({graphic:e});return me([this.view.maskOccludee(e),this.view.trackGraphicState(t),G(()=>({element:this._outlineVisualElement,isDraped:t.isDraped}),({element:i,isDraped:s})=>{ds(i,n=>n.isDraped=s)},qe)])}makeDrawOperation(){const{geometryType:e}=this,t=e!=="circle"&&e!=="rectangle";return new Ql({view:this.view,manipulators:this.manipulators,geometryType:uh(e),drawingMode:this.mode,hasZ:this.hasZ,defaultZ:this.defaultZ,snapToSceneEnabled:this.snapToScene,drawSurface:new Jl(this.view,this.elevationInfo,[this.internalGraphicsLayer]),elevationDrawSurface:new Kl(this.elevationInfo,this.defaultZ,this.view,this.internalGraphicsLayer),hasM:!1,elevationInfo:this.elevationInfo,snappingManager:this.snappingManager,snappingVisualizer:new Gi,segmentLabels:t?new $a:null,labelOptions:this.labelOptions,tooltipOptions:this.tooltipOptions,isDraped:d(this._createGraphicState)?this._createGraphicState.isDraped:gn(this.hasZ,this.elevationInfo)==="on-the-ground"})}onActiveVertexChanged(e){const{view:t}=this;return d(this._activeVertexVisualElement)?(this._activeVertexVisualElement.vertices=[e],this._updateVerticalLineVisualElement(e),null):(this._activeVertexVisualElement=new Wn({view:t,spatialReference:t.spatialReference,vertices:[e],elevationInfo:this.internalGraphicsLayer.elevationInfo,renderOccluded:y.reshapeManipulators.vertex.renderOccluded,attached:!1}),this._activeVertexVisualElement.color=y.colorToVec4(y.reshapeManipulators.selected.color),this._activeVertexVisualElement.attached=!0,this._verticalLineVisualElement=new Ba({view:t,extensionType:y.visualElements.zVerticalLine.extensionType,innerWidth:1,attached:!1,writeDepthEnabled:!1,renderOccluded:R.OccludeAndTransparent}),y.visualElements.zVerticalLine.apply(this._verticalLineVisualElement),It(()=>{this._activeVertexVisualElement=k(this._activeVertexVisualElement),this._verticalLineVisualElement=k(this._verticalLineVisualElement)}))}_updateVerticalLineVisualElement(e){const t=this._verticalLineVisualElement;if(m(t))return;const{renderCoordsHelper:i,elevationProvider:s}=this.view;B(Yt,e[0],e[1],e[2]),Xn.setFromElevationInfo(this.elevationInfo),Yt[2]=Ha(Yt,s,Xn,i),i.toRenderCoords(Yt,this.view.spatialReference,Yt)?(t.setStartEndFromWorldDownAtLocation(Yt),t.attached=!0):t.attached=!1}onOutlineChanged(e){if(d(this._outlineVisualElement))return this._outlineVisualElement.geometry=e,null;const t=this.internalGraphicsLayer.elevationInfo;return this._outlineVisualElement=new Nt({view:this.view,geometry:e,elevationInfo:t,isDraped:d(this._createGraphicState)?this._createGraphicState.isDraped:gn(this.hasZ,t)==="on-the-ground",attached:!1}),y.visualElements.lineGraphics.outline.apply(this._outlineVisualElement),y.visualElements.lineGraphics.shadowStyle.apply(this._outlineVisualElement),this._outlineVisualElement.attached=!0,this._outlineVisualElement.laserlineEnabled=!0,It(()=>{this._outlineVisualElement=k(this._outlineVisualElement)})}onRegularVerticesChanged(e){return d(this._verticesVisualElement)?(this._verticesVisualElement.vertices=e,null):(this._verticesVisualElement=new Wn({view:this.view,spatialReference:this.view.spatialReference,vertices:e,elevationInfo:this.internalGraphicsLayer.elevationInfo,renderOccluded:y.reshapeManipulators.vertex.renderOccluded,attached:!1}),this._verticesVisualElement.attached=!0,It(()=>{this._verticesVisualElement=k(this._verticesVisualElement)}))}};h([_({constructOnly:!0})],Wt.prototype,"elevationInfo",void 0),h([_({constructOnly:!0})],Wt.prototype,"geometryType",void 0),h([_()],Wt.prototype,"type",void 0),h([_({constructOnly:!0})],Wt.prototype,"view",void 0),Wt=h([Q("esri.views.3d.interactive.editingTools.draw.DrawGraphicTool3D")],Wt);const Xn=new ci,Yt=M();function ii(a,e,t){return xd(a,a.screenToRender(e,_s(w.get())),t)}function xd(a,e,t){const i=_s(sn(w.get(),e));if(i[2]=0,!a.unprojectFromRenderScreen(i,t.origin))return null;const s=_s(sn(w.get(),e));s[2]=1;const n=a.unprojectFromRenderScreen(s,w.get());return m(n)?null:(V(t.direction,n,t.origin),t)}class ue{constructor(e){var i;this.metadata=void 0,this._camera=new Ec,this._elevation={offset:0,override:null},this.collisionType={type:"point"},this.collisionPriority=0,this._renderObjects=new Array,this.autoScaleRenderObjects=!0,this._available=!0,this._noDisplayCount=0,this._radius=10,this._worldSized=!1,this.focusMultiplier=2,this.touchMultiplier=2.5,this.worldOriented=!1,this._modelTransform=fe(),this._worldFrame=null,this._renderLocation=M(),this._renderLocationDirty=!0,this._location=new ei({x:0,y:0,z:0}),this._elevationAlignedLocation=new ei,this._elevationAlignedLocationDirty=!0,this.interactive=!0,this.selectable=!1,this.grabbable=!0,this.cursor=null,this.grabCursor=null,this._grabbing=!1,this.dragging=!1,this._hovering=!1,this._selected=!1,this._state=te.None,this._focused=!1,this.events=new ke.EventEmitter,this._screenLocation={screenPointArray:tt(),renderScreenPointArray:gs(),pixelSize:0},this._screenLocationDirty=!0,this._engineResourcesAddedToStage=!1,this._attached=!1,this._location.spatialReference=e.view.spatialReference;for(const s in e)this[s]=e[s];const t=(i=this.view.state)==null?void 0:i.camera;t&&this._camera.copyFrom(t)}destroy(){this._removeResourcesFromStage(),this._engineResources=null,this.view=null,this._camera=null}get _stage(){var e;return(e=this.view)==null?void 0:e._stage}get elevationInfo(){return this._elevationInfo}set elevationInfo(e){this._elevationInfo=e,this._elevationAlignedLocationDirty=!0,this._renderLocationDirty=!0,this._updateEngineObject()}get renderObjects(){return this._renderObjects}set renderObjects(e){this._removeResourcesFromStage(),this._engineResources=null,this._renderObjects=e.slice(),this._updateEngineObject()}set available(e){e!==this._available&&(this._available=e,this._updateEngineObject())}get available(){return this._available}disableDisplay(){return this._noDisplayCount++,this._noDisplayCount===1&&this._updateEngineObject(),{remove:qo(()=>{this._noDisplayCount--,this._noDisplayCount===0&&this._updateEngineObject()})}}set radius(e){e!==this._radius&&(this._radius=e,this._updateEngineObject())}get radius(){return this._radius}set worldSized(e){e!==this._worldSized&&(this._worldSized=e,this._updateEngineObject())}get worldSized(){return this._worldSized}get modelTransform(){return this._modelTransform}set modelTransform(e){Zn(e)&&(this._screenLocationDirty=!0),Ls(this._modelTransform,e),this._updateEngineObject()}get renderLocation(){return this._renderLocationDirty&&(this._renderLocationDirty=!1,this.view.renderCoordsHelper.toRenderCoords(this.elevationAlignedLocation,this._renderLocation),this.worldOriented?(this._worldFrame||(this._worldFrame=fe()),Od(this.view,this._renderLocation,this._worldFrame)):this._worldFrame&&(this._worldFrame=null)),this._renderLocation}set renderLocation(e){this.view.renderCoordsHelper.fromRenderCoords(e,this._location),this.elevationAlignedLocation=this._location}get location(){return this._location}set location(e){vn(e,this._location),this._notifyLocationChanged()}_notifyLocationChanged(){this._renderLocationDirty=!0,this._screenLocationDirty=!0,this._elevationAlignedLocationDirty=!0,this._updateEngineObject(),this.events.emit("location-update",{location:this._location})}get elevationAlignedLocation(){return this._elevationAlignedLocationDirty?(this._evaluateElevationAlignment(),this._updateElevationAlignedLocation(),this._elevationAlignedLocation):this._elevationAlignedLocation}set elevationAlignedLocation(e){vn(e,this._location),this._evaluateElevationAlignment(),this._location.z-=this._elevation.offset,this._updateElevationAlignedLocation(),this._updateEngineObject(),this.events.emit("location-update",{location:this._location})}_updateElevationAlignedLocation(){const e=d(this._elevation.override)?this._elevation.override:this.location.z||0;this._elevationAlignedLocation.x=this.location.x,this._elevationAlignedLocation.y=this.location.y,this._elevationAlignedLocation.z=e+this._elevation.offset,this._elevationAlignedLocation.spatialReference=ec(this.location.spatialReference),this._renderLocationDirty=!0,this._screenLocationDirty=!0,this._elevationAlignedLocationDirty=!1}grabbableForEvent(){return!0}get grabbing(){return this._grabbing}set grabbing(e){e!==this._grabbing&&(this._grabbing=e,this._setFocused(this._hovering||this._grabbing),this._updateEngineObject())}get hovering(){return this._hovering}set hovering(e){e!==this._hovering&&(this._hovering=e,this._setFocused(this._hovering||this._grabbing),this._updateEngineObject())}get selected(){return this._selected}set selected(e){e!==this._selected&&(this._selected=e,this._updateEngineObject(),this.events.emit("select-changed",{action:e?"select":"deselect"}))}get state(){return this._state}set state(e){e!==this._state&&(this._state=e,this._updateEngineObject())}updateStateEnabled(e,t){t?this.state|=e:this.state&=~e}_setFocused(e){e!==this._focused&&(this._focused=e,this.events.emit("focus-changed",{action:e===!0?"focus":"unfocus"}))}get focused(){return this._focused}get screenLocation(){return this._ensureScreenLocation(),this._screenLocation}_ensureScreenLocation(){if(!this._screenLocationDirty)return;this._screenLocation.pixelSize=this._camera.computeScreenPixelSizeAt(this.renderLocation),this._screenLocationDirty=!1;let e;if(Zn(this._modelTransform)){const t=this._calculateModelTransformOffset(Rd);e=X(t,t,this.renderLocation)}else e=this.renderLocation;this._camera.projectToRenderScreen(e,this._screenLocation.renderScreenPointArray),this._camera.renderToScreen(this._screenLocation.renderScreenPointArray,this._screenLocation.screenPointArray)}get applyObjectTransform(){return this._applyObjectTransform}set applyObjectTransform(e){this._applyObjectTransform=e,this._screenLocationDirty=!0,this._updateEngineObject()}get attached(){return this._attached}intersectionDistance(e,t){if(!this.available)return null;const i=ba(e,Ed),s=this._getCollisionRadius(t),n=-1*this.collisionPriority;switch(this.collisionType.type){case"point":if(Rr(this.screenLocation.screenPointArray,i)<s*s)return this.screenLocation.renderScreenPointArray[2]+n;break;case"line":{const r=this.collisionType.paths,o=this._getWorldToScreenObjectScale(),l=this._calculateObjectTransform(o,sa),c=s*this.screenLocation.pixelSize,p=ii(this._camera,i,ss);if(m(p))return null;for(const u of r){if(u.length===0)continue;const g=ge(xi,u[0],l);for(let v=1;v<u.length;v++){const b=ge(Jn,u[v],l),f=_c(Ra(g,b,qn),p);if(f!=null&&f<c*c){const S=X(w.get(),g,b);pe(S,S,.5);const O=on(w.get());return this._camera.projectToRenderScreen(S,O),O[2]+n}j(g,b)}}break}case"disc":{const r=this.collisionType.direction,o=this.collisionType.offset??us,l=this._getWorldToScreenObjectScale(),c=this._calculateObjectTransform(l,sa),p=s*this.screenLocation.pixelSize,u=ii(this._camera,i,ss);if(m(u))return null;const g=nn(Qn,c),v=rn(er,r,g),b=ge(tr,o,c);Ft(b,v,na);const f=Kn;if(ni(na,u,f)&&Ir(f,b)<p*p)return this.screenLocation.renderScreenPointArray[2]+n;break}case"ribbon":{const{paths:r,direction:o}=this.collisionType,l=this._getWorldToScreenObjectScale(),c=this._calculateObjectTransform(l,sa),p=s*this._camera.computeScreenPixelSizeAt(this.renderLocation),u=ii(this._camera,i,ss);if(m(u))return null;const g=nn(Qn,c),v=rn(er,o,g),b=this._calculateModelTransformPosition(tr);Ft(b,v,na);const f=Kn;if(!ni(na,u,f))break;for(const S of r){if(S.length===0)continue;const O=ge(xi,S[0],c);for(let z=1;z<S.length;z++){const W=ge(Jn,S[z],c),_e=uc(Ra(O,W,qn),f);if(_e!=null&&_e<p*p){const Y=X(w.get(),O,W);pe(Y,Y,.5);const Z=on(w.get());return this._camera.projectToRenderScreen(Y,Z),Z[2]+n}j(O,W)}}break}default:bl(this.collisionType)}return null}attach(e={manipulator3D:{}}){const t=this._stage;if(!t)return;const i=e.manipulator3D;if(m(i.engineLayerId)){const s=new zs({pickable:!1,updatePolicy:Na.SYNC});t.add(s),i.engineLayerId=s.id,this._engineLayer=s}else t!=null&&t.getObject&&(this._engineLayer=t.getObject(i.engineLayerId));i.engineLayerReferences=(i.engineLayerReferences||0)+1,this._materialIdReferences=i.materialIdReferences,m(this._materialIdReferences)&&(this._materialIdReferences=new Map,i.materialIdReferences=this._materialIdReferences),this._camera.copyFrom(this.view.state.camera),this._attached=!0,this._updateEngineObject(),Ml(this._location.spatialReference,this.view.spatialReference)||(this.location=new ei({x:0,y:0,z:0,spatialReference:this.view.spatialReference}))}detach(e={manipulator3D:{}}){const t=e.manipulator3D;t.engineLayerReferences--;const i=t.engineLayerReferences===0;i&&(t.engineLayerId=null),this._removeResourcesFromStage(i),this._engineResources=null,this._engineLayer=null,this._materialIdReferences=null,this._attached=!1}onViewChange(){this._camera.copyFrom(this.view.state.camera),this._screenLocationDirty=!0,this._updateEngineObject()}onElevationChange(e){ms(this.location,ir,e.spatialReference)&&Sl(e.extent,ir)&&this._notifyLocationChanged()}_evaluateElevationAlignment(){if(m(this.elevationInfo))return;let e=null,t=0;const i=se(this.elevationInfo.offset,0);switch(this.elevationInfo.mode){case"on-the-ground":e=se(ts(this.view.elevationProvider,this.location,"ground"),0);break;case"relative-to-ground":t=se(ts(this.view.elevationProvider,this.location,"ground"),0)+i;break;case"relative-to-scene":t=se(ts(this.view.elevationProvider,this.location,"scene"),0)+i;break;case"absolute-height":t=i}return t!==this._elevation.offset||e!==this._elevation.override?(this._elevation.offset=t,void(this._elevation.override=e)):void 0}_updateEngineObject(){if(!this._attached)return;if(!this.available)return void this._removeResourcesFromStage();const e=this._getWorldToScreenObjectScale(),t=sa;if(this.autoScaleRenderObjects===!0){const r=this._getFocusedSize(this._radius,this.focused)*e;this._calculateObjectTransform(r,t)}else this._calculateObjectTransform(e,t);const{objectsByState:i}=this._ensureEngineResources(),s=(this.focused?x.Focused:x.Unfocused)|(this.selected?x.Selected:x.Unselected),n=this._noDisplayCount>0;for(const{stateMask:r,objects:o}of i){if(n){for(const g of o)g.visible=!1;continue}const l=(r&x.All)!==x.None,c=(r&te.All)!==te.None,p=!l||(s&r)==(r&x.All),u=!c||(this.state&r)==(r&te.All);if(p&&u)for(const g of o)g.visible=!0,g.transformation=t;else for(const g of o)g.visible=!1}}_ensureEngineResources(){if(m(this._engineResources)){const e=C(this._engineLayer),t=[],i=new Set;this.renderObjects.forEach(({geometry:{material:r}})=>{i.has(r)||(t.push(r),i.add(r))});const s=new Map;this._renderObjects.forEach(r=>{const o=new Fs({castShadow:!1,geometries:[r.geometry]}),l=s.get(r.stateMask)||[];l.push(o),s.set(r.stateMask,l)});const n=[];s.forEach((r,o)=>n.push({stateMask:o,objects:r})),this._engineResources={objectsByState:n,layer:e,materials:t}}return this._addResourcesToStage(),this._engineResources}_addResourcesToStage(){const e=this._stage;if(this._engineResourcesAddedToStage||m(this._engineResources)||!e)return;const{objectsByState:t,layer:i,materials:s}=this._engineResources;s.forEach(n=>{const r=C(this._materialIdReferences),o=r.get(n.id)||0;o===0&&e.add(n),r.set(n.id,o+1)}),t.forEach(({objects:n})=>{i.addMany(n),e.addMany(n)}),this._engineResourcesAddedToStage=!0}_removeResourcesFromStage(e=!1){const t=this._stage;if(!this._engineResourcesAddedToStage||m(this._engineResources)||!t)return;const{objectsByState:i,layer:s,materials:n}=this._engineResources;i.forEach(({objects:r})=>{s.removeMany(r),t.removeMany(r)}),n.forEach(r=>{const o=C(this._materialIdReferences),l=o.get(r.id);l===1?(t.remove(r),o.delete(r.id)):o.set(r.id,l-1)}),e&&t.remove(s),this._engineResourcesAddedToStage=!1}_getCollisionRadius(e){return this._getFocusedSize(this.radius,!0)*(e==="touch"?this.touchMultiplier:1)}_getFocusedSize(e,t){return e*(t?this.focusMultiplier:1)}_getWorldToScreenObjectScale(){return this._worldSized?1:this.screenLocation.pixelSize}_calculateModelTransformPosition(e){const t=this._getWorldToScreenObjectScale(),i=this._calculateObjectTransform(t,Ad);return B(e,i[12],i[13],i[14])}_calculateModelTransformOffset(e){const t=this._calculateModelTransformPosition(e);return V(e,t,this.renderLocation)}_calculateObjectTransform(e,t){return wl(t,e,0,0,0,0,e,0,0,0,0,e,0,0,0,0,1),this._worldFrame&&Ke(t,t,this._worldFrame),Ke(t,t,this._modelTransform),t[12]+=this.renderLocation[0],t[13]+=this.renderLocation[1],t[14]+=this.renderLocation[2],t[15]=1,d(this._applyObjectTransform)&&this._applyObjectTransform(t),t}get test(){let e=!1;if(d(this._engineResources))for(const t in this._engineResources.objectsByState){const i=this._engineResources.objectsByState[t];for(const s of i.objects)if(s.visible){e=!0;break}if(e)break}return{areAnyResourcesVisible:e}}}function Zn(a){return a[12]!==0||a[13]!==0||a[14]!==0}function Od(a,e,t){switch(a.viewingMode){case"local":return fs(t),!0;case"global":{const i=Qo(a.renderCoordsHelper.spatialReference);return xl(e,0,xi,0,i.radius),Ol(ut(xi[0]),ut(xi[1]),t),!0}}}const Ed=tt(),qn=$i(),ss=Hi(),Qn=mh(),Ad=fe(),sa=fe(),na=hi(),xi=M(),Jn=M(),Kn=M(),er=M(),tr=M(),Rd=M(),ir=new ei({x:0,y:0,z:0,spatialReference:null});let E=class{constructor(e,t=x.None){this.geometry=e,this.stateMask=t}};function Po(a){const e=new Ui,{vertex:t,fragment:i}=e;return Hs(t,a),e.include(ao,a),e.attributes.add(A.POSITION,"vec3"),e.attributes.add(A.UV0,"vec2"),e.varyings.add("vpos","vec3"),a.hasMultipassTerrain&&e.varyings.add("depth","float"),t.uniforms.add(new Lt("textureCoordinateScaleFactor",s=>d(s.texture)&&d(s.texture.descriptor.textureCoordinateScaleFactor)?s.texture.descriptor.textureCoordinateScaleFactor:El)),t.code.add(T`
    void main(void) {
      vpos = position;
      ${a.hasMultipassTerrain?"depth = (view * vec4(vpos, 1.0)).z;":""}
      vTexCoord = uv0 * textureCoordinateScaleFactor;
      gl_Position = transformPosition(proj, view, vpos);
    }
  `),e.include(so,a),e.include(ro,a),i.uniforms.add([new Ss("tex",s=>s.texture),new he("opacity",s=>s.opacity)]),e.varyings.add("vTexCoord","vec2"),a.output===U.Alpha?i.code.add(T`
    void main() {
      discardBySlice(vpos);
      ${a.hasMultipassTerrain?"terrainDepthTest(gl_FragCoord, depth);":""}

      float alpha = texture2D(tex, vTexCoord).a * opacity;
      if (alpha  < ${T.float(Rn)}) {
        discard;
      }

      gl_FragColor = vec4(alpha);
    }
    `):(i.include(no),i.code.add(T`
    void main() {
      discardBySlice(vpos);
      ${a.hasMultipassTerrain?"terrainDepthTest(gl_FragCoord, depth);":""}
      gl_FragColor = texture2D(tex, vTexCoord) * opacity;

      if (gl_FragColor.a < ${T.float(Rn)}) {
        discard;
      }

      gl_FragColor = highlightSlice(gl_FragColor, vpos);
      ${a.transparencyPassType===at.Color?"gl_FragColor = premultiplyAlpha(gl_FragColor);":""}
    }
    `)),e}const Td=Object.freeze(Object.defineProperty({__proto__:null,build:Po},Symbol.toStringTag,{value:"Module"}));class Wa extends ki{initializeProgram(e){return new Bi(e.rctx,Wa.shader.get().build(this.configuration),js)}_setPipelineState(e,t){const i=this.configuration,s=e===at.NONE,n=e===at.FrontFace;return Yi({blending:i.output!==U.Color&&i.output!==U.Alpha||!i.transparent?null:s?Dd:po(e),culling:uo(i.cullFace),depthTest:{func:ah(e)},depthWrite:s?i.writeDepth?_o:null:go(e),colorWrite:Xi,stencilWrite:i.hasOccludees?Yc:null,stencilTest:i.hasOccludees?t?Xc:Zc:null,polygonOffset:s||n?null:sh(i.enableOffset)})}initializePipeline(){return this._occludeePipelineState=this._setPipelineState(this.configuration.transparencyPassType,!0),this._setPipelineState(this.configuration.transparencyPassType,!1)}getPipelineState(e,t){return t?this._occludeePipelineState:super.getPipelineState(e,t)}}Wa.shader=new Wi(Td,()=>Ni(()=>Promise.resolve().then(()=>vu),void 0));const Dd=Us(Qe.ONE,Qe.ONE_MINUS_SRC_ALPHA);let Ce=class extends oo{constructor(){super(...arguments),this.output=U.Color,this.cullFace=ie.None,this.hasSlicePlane=!1,this.transparent=!1,this.enableOffset=!0,this.writeDepth=!0,this.hasOccludees=!1,this.transparencyPassType=at.NONE,this.hasMultipassTerrain=!1,this.cullAboveGround=!1}};h([N({count:U.COUNT})],Ce.prototype,"output",void 0),h([N({count:ie.COUNT})],Ce.prototype,"cullFace",void 0),h([N()],Ce.prototype,"hasSlicePlane",void 0),h([N()],Ce.prototype,"transparent",void 0),h([N()],Ce.prototype,"enableOffset",void 0),h([N()],Ce.prototype,"writeDepth",void 0),h([N()],Ce.prototype,"hasOccludees",void 0),h([N({count:at.COUNT})],Ce.prototype,"transparencyPassType",void 0),h([N()],Ce.prototype,"hasMultipassTerrain",void 0),h([N()],Ce.prototype,"cullAboveGround",void 0);let Pd=class extends Ac{constructor(e){super(e,new Cd),this.supportsEdges=!0,this._configuration=new Ce}getConfiguration(e,t){return this._configuration.output=e,this._configuration.cullFace=this.parameters.cullFace,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.transparent=this.parameters.transparent,this._configuration.writeDepth=this.parameters.writeDepth,this._configuration.hasOccludees=this.parameters.hasOccludees,this._configuration.transparencyPassType=t.transparencyPassType,this._configuration.enableOffset=t.camera.relativeElevation<nh,this._configuration.hasMultipassTerrain=t.multipassTerrain.enabled,this._configuration.cullAboveGround=t.multipassTerrain.cullAboveGround,this._configuration}requiresSlot(e,t){return t===U.Color||t===U.Alpha||t===U.Highlight?e===Ae.DRAPED_MATERIAL?!0:t===U.Highlight?e===Ae.OPAQUE_MATERIAL:e===(this.parameters.transparent?this.parameters.writeDepth?Ae.TRANSPARENT_MATERIAL:Ae.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL:Ae.OPAQUE_MATERIAL):!1}createGLMaterial(e){return new $d(e)}createBufferWriter(){return new qc(Rc)}},$d=class extends Qc{constructor(e){super({...e,...e.material.parameters})}_updateParameters(e){return this.updateTexture(this._material.parameters.textureId),this._material.setParameters(this.textureBindParameters),this.ensureTechnique(Wa,e)}_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&(this._material.setParameters({hasOccludees:e.hasOccludees}),this._updateParameters(e))}beginSlot(e){return this._output!==U.Color&&this._output!==U.Alpha||this._updateOccludeeState(e),this._updateParameters(e)}},Cd=class extends lo{constructor(){super(...arguments),this.transparent=!1,this.writeDepth=!0,this.hasSlicePlane=!1,this.cullFace=ie.None,this.hasOccludees=!1,this.opacity=1,this.textureId=null,this.initTextureTransparent=!0}};function dt(a,e=R.OccludeAndTransparent,t=!0){const i=Te(a[0],a[1],a[2],a.length>3?a[3]:1),s=a[3]<1;return t?new As({color:i,transparent:s,writeDepth:!0,cullFace:ie.Back,renderOccluded:e}):new ze({color:i,transparent:s,writeDepth:!0,cullFace:ie.Back,renderOccluded:e})}function mi(a,e=R.OccludeAndTransparent){const t=Te(a[0],a[1],a[2],a.length===4?a[3]:1);return new ze({color:t,transparent:!0,writeDepth:!0,cullFace:ie.Front,renderOccluded:e})}const fi=Object.freeze({calloutLength:40,calloutWidth:1,discRadius:27,focusMultiplier:1.1,calloutColor:F(1,.5,0)});function Ld(a,e){const t=new ue({view:a,autoScaleRenderObjects:!1,collisionPriority:1,metadata:e.metadata});return Gd(t,e),t}function Gd(a,e){var b;const t=e.material??new Pd({transparent:!0,writeDepth:!1,textureId:(b=e.texture)==null?void 0:b.id,renderOccluded:R.Opaque}),i=e.focusMultiplier??fi.focusMultiplier,s=e.calloutLength??fi.calloutLength,n=fi.discRadius*(e.discScale??1),r=n*i,o=(f,S)=>{const O=[0,1,2,2,3,0];return new io(S,[[A.POSITION,new ws([s-f,-f,0,s+f,-f,0,s+f,f,0,s-f,f,0],3,!0)],[A.UV0,new ws([0,0,1,0,1,1,0,1],2,!0)]],[[A.POSITION,O],[A.UV0,O]])},l=fi.calloutColor,c=e.calloutWidth??fi.calloutWidth,p=new(c>1?it:Ns)({width:c,color:Te(l[0],l[1],l[2],e.calloutOpacity??1),renderOccluded:R.OccludeAndTransparent}),u=je(p,[[0,0,0],[s-n,0,0]]),g=je(p,[[0,0,0],[s-r,0,0]]),v=e.customStateMask??te.None;a.collisionType={type:"disc",direction:[0,0,1],offset:[s,0,0]},a.focusMultiplier=i,a.metadata=e.metadata,a.radius=n,a.renderObjects=[new E(o(n,t),x.Unfocused|v),new E(u,x.Unfocused|v),new E(o(r,t),x.Focused|v),new E(g,x.Focused|v)]}const Id=Object.freeze({autoScaleRenderObjects:!1,worldSized:!0});function $o(a,e,t,i){const s=V(w.get(),a,t),n=Co(s,He(w.get(),i,s),t,ne.get());Al(n,n);const r=ge(w.get(),e,n);return Math.atan2(r[1],r[0])}function Co(a,e,t,i){const s=re(w.get(),a),n=re(w.get(),e),r=He(w.get(),s,n);return i[0]=s[0],i[1]=s[1],i[2]=s[2],i[3]=0,i[4]=n[0],i[5]=n[1],i[6]=n[2],i[7]=0,i[8]=r[0],i[9]=r[1],i[10]=r[2],i[11]=0,i[12]=t[0],i[13]=t[1],i[14]=t[2],i[15]=1,i}function La(a,e){const t=a.getViewForGraphic(e);return d(t)&&"computeAttachmentOrigin"in t?t.computeAttachmentOrigin(e,a.spatialReference):null}function Lo(a,e,t){const i=La(a,t);d(i)?e.elevationAlignedLocation=i:Vd(e,t.geometry)}function Vd(a,e){if(m(e))return;const t=e.type==="mesh"?e.anchor:Tc(e);m(t)||(a.location=tc(t))}function Vt(a,e=K(a)){return e.mode!=="on-the-ground"&&!(m(a.geometry)||!a.geometry.hasZ)}var Ve;(function(a){a[a.NONE=0]="NONE",a[a.ANY=1]="ANY",a[a.Z=2]="Z",a[a.XY=4]="XY"})(Ve||(Ve={}));var I;(function(a){a[a.TRANSLATE_Z=0]="TRANSLATE_Z",a[a.TRANSLATE_XY=1]="TRANSLATE_XY",a[a.SCALE=2]="SCALE",a[a.ROTATE=3]="ROTATE",a[a.SCALE_ROTATE=4]="SCALE_ROTATE"})(I||(I={}));let Go=class{constructor(){this.grabbingState=Ve.NONE,this.zManipulator=null,this.firstSelected=null,this.numSelected=0,this.firstGrabbedXY=null}update(e){this.grabbingState=Ve.NONE,this.zManipulator=null,this.numSelected=0,this.firstSelected=null,this.firstGrabbedXY=null,e.forEachManipulator((t,i)=>{if(i===I.TRANSLATE_Z&&(this.zManipulator=t),t instanceof ue&&(t.selected&&(this.numSelected===0&&(this.firstSelected=t),this.numSelected++),m(this.firstGrabbedXY)&&t.grabbing&&i===I.TRANSLATE_XY&&(this.firstGrabbedXY=t)),t.grabbing)switch(this.grabbingState|=Ve.ANY,i){case I.TRANSLATE_Z:this.grabbingState|=Ve.Z;break;case I.TRANSLATE_XY:this.grabbingState|=Ve.XY}})}};function zd(a){const{view:e,graphic:t}=a,i=new we({graphic:t}),s=Fd(a,i),n=[s,Nd(a,s.visualElement,i),e.maskOccludee(t),e.trackGraphicState(i)];return{visualElement:s.visualElement,remove:()=>me(n).remove()}}function Fd(a,e){const{view:t,graphic:i}=a,s=new Nt({view:t,geometry:Rs(i)?i.geometry:null,elevationInfo:K(i),attached:!1});y.visualElements.lineGraphics.shadowStyle.apply(s);const n=()=>{s.attached=e.displaying};y.visualElements.lineGraphics.outline.apply(s);const r=[G(()=>e.displaying,n),G(()=>e.isDraped,o=>{s.isDraped=o}),e.on("changed",()=>s.geometry=Rs(i)?i.geometry:null),xe(s)];return n(),{visualElement:s,remove:()=>me(r).remove()}}function Nd(a,e,t){const{graphic:i,view:s}=a,n=[],r=K(i),o=r.mode==="on-the-ground"||!r.offset&&r.mode!=="absolute-height",l=new Go,c=new Ba({view:s,extensionType:y.visualElements.zVerticalLine.extensionType,innerWidth:1,attached:!1,writeDepthEnabled:!1,renderOccluded:R.OccludeAndTransparent});y.visualElements.pointGraphics.shadowStyle.apply(c);const p=ut(y.visualElements.heightPlaneAngleCutoff),u=new Li({view:s,attached:!1,angleCutoff:p});y.visualElements.heightPlane.apply(u);let g=1,v=1;const b=()=>{if(l.update(a),!t.displaying||o&&(t.isDraped||!Rs(i)||!i.geometry.hasZ))return e.laserlineEnabled=!1,c.attached=!1,void(u.attached=!1);e.laserlineEnabled=!0;const O=l.grabbingState&Ve.XY?y.visualElements.laserlineAlphaMultiplier:1;O!==g&&(g=O,y.visualElements.lineGraphics.shadowStyle.apply(e,O),y.visualElements.pointGraphics.shadowStyle.apply(c,O));const z=l.grabbingState&Ve.Z?y.visualElements.laserlineAlphaMultiplier:1;z!==v&&(v=z,y.visualElements.heightPlane.apply(u,z)),jd(c,l),Hd(a,e,u,l)};y.visualElements.zVerticalLine.apply(c),n.push(t.on("changed",b),G(()=>t.displaying,b),e.events.on("attachment-origin-changed",b),xe(c),xe(u));const f=[],S=()=>{me(f).remove(),f.length=0,a.forEachManipulator(O=>f.push(O.events.on("grab-changed",b))),a.forEachManipulator(O=>f.push(O.events.on("select-changed",b))),b()};return S(),n.push(a.onManipulatorsChanged(S),Er(()=>me(f))),me(n)}function jd(a,e){const t=e.numSelected===1?e.firstSelected:e.numSelected>1&&d(e.firstGrabbedXY)?e.firstGrabbedXY:null;d(t)?(a.setStartEndFromWorldDownAtLocation(t.renderLocation),a.attached=!0):a.attached=!1}function Hd(a,e,t,i){if(i.numSelected>0){B(Tt,0,0,0);let s=0;a.forEachManipulator((n,r)=>{r===I.TRANSLATE_XY&&n.selected&&n instanceof ue&&(X(Tt,Tt,n.renderLocation),s++)}),s>0?(t.heightManifoldTarget=pe(Tt,Tt,1/s),t.attached=!0):t.attached=!1}else{const s=e.attachmentOrigin;d(s)&&a.view.renderCoordsHelper.toRenderCoords(s,Tt)?(t.heightManifoldTarget=Tt,t.attached=!0):t.attached=!1}}function Rs(a){return d(a.geometry)&&(a.geometry.type==="polygon"||a.geometry.type==="polyline")}const Tt=M();function Ud(a){const{view:e,graphic:t}=a,i=new we({graphic:t}),s=[],n=Bd(a,i,s);return kd(a,i,s,n),s.push(e.trackGraphicState(i)),{visualElement:n,remove(){me(s).remove()}}}function kd(a,e,t,i){const{view:s,graphic:n}=a,r=new Ba({view:s,extensionType:y.visualElements.zVerticalLine.extensionType,innerWidth:1,attached:!1,writeDepthEnabled:!1,renderOccluded:R.OccludeAndTransparent});y.visualElements.zVerticalLine.apply(r);const o=new Li({view:s,intersectsLineInfinite:!0,attached:!1});y.visualElements.pointGraphics.shadowStyle.apply(o);const l=ut(y.visualElements.heightPlaneAngleCutoff),c=new Li({view:s,attached:!1,angleCutoff:l});y.visualElements.heightPlane.apply(c);const p=K(a.graphic),u=ci.fromElevationInfo(p),g=p.mode==="on-the-ground"||!p.offset&&p.mode!=="absolute-height",v=new Go;let b=1,f=1;const S=()=>{v.update(a);const O=Xs(n),z=g&&(e.isDraped||m(O)||!O.hasZ);let W=!0;if(!z&&d(O)){const $=Ha(O,s.elevationProvider,u,s.renderCoordsHelper);B(Ge,O.x,O.y,$),Ei(Ge,O.spatialReference,Ge,s.renderCoordsHelper.spatialReference),r.setStartEndFromWorldDownAtLocation(Ge),o.intersectsWorldUpAtLocation=Ge}else W=!1;const _e=v.grabbingState&Ve.Z?y.visualElements.laserlineAlphaMultiplier:1;_e!==b&&(b=_e,y.visualElements.heightPlane.apply(c,_e));const Y=Lr(Zd);!z&&e.displaying&&i.calculateMapBounds(Y)&&Ei(Rl(Y,Ge),s.spatialReference,Ge,s.renderCoordsHelper.spatialReference)?(c.heightManifoldTarget=Ge,c.attached=!0):c.attached=!1;const Z=v.grabbingState&Ve.XY?y.visualElements.laserlineAlphaMultiplier:1;Z!==f&&(f=Z,y.visualElements.pointGraphics.shadowStyle.apply(o,Z));const Be=W&&e.displaying&&!z;o.attached=Be,r.attached=Be};t.push(G(()=>[e.displaying,e.isDraped],S),e.on("changed",S)),a.forEachManipulator(O=>{t.push(O.events.on("grab-changed",S))}),t.push(xe(o)),t.push(xe(r)),t.push(xe(c)),S()}function Bd(a,e,t){const{view:i,graphic:s}=a,n=new Os({view:i,geometry:Xs(s),elevationInfo:K(s),attached:!1});return Wd(a,n,e,t),t.push(xe(n)),n}function Xs(a){const e=a.geometry;return m(e)?null:e.type==="point"?e:e.type==="mesh"?e.anchor.clone():null}function Wd(a,e,t,i){const s=()=>e.attached=t.displaying;Yd(a,e,t,i),y.visualElements.pointGraphics.outline.apply(e),i.push(G(()=>t.displaying,s,qe))}function Yd(a,e,t,i){const{view:s,graphic:n}=a;let r=null;const o=c=>{d(r)&&(r.remove(),r=null),t.isDraped&&d(c)&&(r=Xd(s,c,()=>{e.geometry=c}))},l=()=>{const c=Xs(n);o(c),e.geometry=c};i.push(t.on("changed",l),Er(()=>r)),l()}function Xd(a,e,t){const i=a.elevationProvider.spatialReference;Tl(e,Ge,i);const s=Ge[0],n=Ge[1];return a.elevationProvider.on("elevation-change",r=>{Vr(r.extent,s,n)&&t()})}const Ge=M(),Zd=Cr();function Ya(a){switch(C(a.graphic.geometry).type){case"point":case"mesh":return Ud(a);case"polygon":case"polyline":return zd(a);default:return null}}const qd=[1,.5,0],Io=128,et=70,Qd=80,Vo=.02,Jd=54,Kd=100,zo=Math.ceil(et/3*2),Ct=160,ns=.5,rs=24,Xt=9,ep=Ct+30,ar=Ct+53,tp=60,ip=23,ap=5*Math.PI/12,sp=1*Math.PI/3,sr=10,nr=.2,rr=30,or=53,lr=.2,cr=.3,np=200,rp=3,op=1e6;let qi=class{constructor(){this._available=!0}set location(e){this._forEachManipulator3D(t=>t.location=e)}set elevationAlignedLocation(e){this._forEachManipulator3D(t=>t.elevationAlignedLocation=e)}set elevationInfo(e){this._forEachManipulator3D(t=>t.elevationInfo=e)}get renderLocation(){let e;return this._forEachManipulator3D(t=>{e||(e=t.renderLocation)}),e}get available(){return this._available}set available(e){this._available=e,this._forEachManipulator3D(t=>t.available=e)}get hovering(){return this.someManipulator(e=>e.hovering)}get grabbing(){return this.someManipulator(e=>e.grabbing)}get dragging(){return this.someManipulator(e=>e.dragging)}hasManipulator(e){return this.someManipulator(t=>t===e)}someManipulator(e){let t=!1;return this.forEachManipulator(i=>{!t&&e(i)&&(t=!0)}),t}_forEachManipulator3D(e){this.forEachManipulator((t,i)=>{t instanceof ue&&e(t,i)})}};function Ga(a,e){return Fo(a,()=>e)}function lp(a){return Fo(a,e=>e.plane)}function Fo(a,e){const t=M(),i=M();let s=!1;return n=>{const r=e(n);if(n.action==="start"){const c=ba(n.screenStart,fa(ys.get())),p=ii(a.state.camera,c,dr);d(p)&&(s=ni(r,p,t))}if(!s)return null;const o=ba(n.screenEnd,fa(ys.get())),l=ii(a.state.camera,o,dr);return m(l)?null:ni(r,l,i)?{...n,renderStart:t,renderEnd:i,plane:r,ray:l}:null}}function cp(a,e,t=0,i=null,s=null){let n=null;return r=>{if(r.action==="start"&&(n=a.sceneIntersectionHelper.intersectElevationFromScreen(tt(r.screenStart.x,r.screenStart.y),e,t,s),d(n)&&d(i)&&!ms(n,n,i))||m(n))return null;const o=a.sceneIntersectionHelper.intersectElevationFromScreen(tt(r.screenEnd.x,r.screenEnd.y),e,t,s);return d(o)?d(i)&&!ms(o,o,i)?null:{...r,mapStart:n,mapEnd:o}:null}}function Xa(a,e,t,i=null,s=null){return cp(a,t,Ur(e,a,t),i,s)}function No(a,e,t,i=null,s=null){return Xa(a,t,K(e),i,s)}function hp(a,e,t,i){const s=e.toMap(a.screenStart,{include:[t]});return d(s)?No(e,t,s,i):null}function dp(a,e){const t=up,i=_p,s=hi();a.renderCoordsHelper.worldUpAtPosition(e,t);const n=He(s,t,V(i,e,a.state.camera.eye));return He(n,n,t),Ft(e,n,s)}function jo(a,e,t){let i=null;const s=new Vs;return s.next(Ga(a,dp(a,e))).next(pp(a,e)).next(Ho(a,t)).next(n=>{n.mapEnd.x=n.mapStart.x,n.mapEnd.y=n.mapStart.y,i=n}),n=>(i=null,s.execute(n),i)}function pp(a,e){const t=M(),i=Re(e);a.renderCoordsHelper.worldUpAtPosition(e,t);const s=M(),n=M(),r=o=>(V(o,o,e),mc(t,o,o),a.viewingMode==="global"&&Re(o)*Math.sign(Ma(t,o))<.001-i&&V(o,pe(o,t,.001),e),X(o,o,e),o);return o=>(o.renderStart=r(j(s,o.renderStart)),o.renderEnd=r(j(n,o.renderEnd)),o)}function Ho(a,e){const t=a.renderCoordsHelper;return i=>{const s=t.fromRenderCoords(i.renderStart,e),n=t.fromRenderCoords(i.renderEnd,e);return d(s)&&d(n)?{...i,mapStart:s,mapEnd:n}:null}}var hr;(function(a){a[a.GROUND=0]="GROUND",a[a.OTHER=1]="OTHER"})(hr||(hr={}));const up=M(),_p=M(),dr=Hi();function Za(a,e,t,i){const s=a.graphic,n=(r,o)=>e({action:r,graphic:s,dxScreen:o.screenDeltaX,dyScreen:o.screenDeltaY});return t((r,o,l)=>(o.next(c=>(c.action==="start"&&n("start",c),c)).next(ic(s,i)).next(c=>{switch(c.action){case"start":case"update":(c.translationX||c.translationY||c.translationZ)&&n("update",c);break;case"end":n("end",c)}return c}),{steps:o,cancel:l=l.next(ac(s)).next(c=>(n("end",{screenDeltaX:0,screenDeltaY:0}),c))}))}function Uo(a){if(m(a)||a.type!=="polyline"&&a.type!=="polygon")return 0;const e=(a.type==="polyline"?a.paths:a.rings)[0];if(!e||e.length<2)return 0;const t=e[0],i=e[1];return Math.atan2(i[1]-t[1],i[0]-t[0])}function Ia(a){if(m(a)||m(a.axis))return 1;const{mapStart:e,mapEnd:t,axis:i}=a,s=[t.x-e.x,t.y-e.y];return s[0]*i[0]+s[1]*i[1]>0?1:-1}let gp=class extends qi{constructor(e){super(),this._handles=new oi,this._arrowManipulatorInfos=new Array,this._opaqueMaterial=this._createMaterial(),this._transparentMaterial=this._createMaterial(.5),this._angle=0,this._scale=1,this._radius=et,this._updateAfterDrag=!1,this.events=new ke,this._tool=e.tool,this._view=e.view,e.radius!=null&&(this._radius=e.radius),this._createManipulators(),this.forEachManipulator(t=>this._tool.manipulators.add(t))}set orthogonalAvailable(e){this._arrowManipulatorInfos[1].manipulator.available=e,this._arrowManipulatorInfos[3].manipulator.available=e}destroy(){this._handles=k(this._handles),this.forEachManipulator(e=>{this._tool.manipulators.remove(e),e.destroy()}),this._tool=null,this._view=null,this._arrowManipulatorInfos.length=0}forEachManipulator(e){for(const{manipulator:t}of this._arrowManipulatorInfos)e(t,I.TRANSLATE_XY)}createGraphicDragPipeline(e,t,i){const s=t.graphic,n=K(s),r=C(s.geometry).spatialReference;return Za(t,i,o=>this.createDragPipeline((l,c,p,u,g)=>({steps:c,cancel:p}=e(l,c,p,u,g),o(l,c,p)),n,r,s),this._view.state.viewingMode)}createDragPipeline(e,t,i,s){return me(this._arrowManipulatorInfos.map(({manipulator:n},r)=>Ne(n,(o,l,c,p,u)=>{const g=l.next(v=>({...v,manipulatorType:I.TRANSLATE_XY})).next(Si(this._view,o.elevationAlignedLocation)).next(Xa(this._view,o.elevationAlignedLocation,t,i,s)).next(sc(o.location,this.angle+(r+1)*Math.PI*.5)).next(li());e(o,g,c,p,u)})))}get angle(){return this._angle}set angle(e){this._angle=e,this.dragging?this._updateAfterDrag=!0:this._updateManipulatorTransform()}get displayScale(){return this._scale}set displayScale(e){this._scale=e,this._updateManipulatorTransform()}get radius(){return this._radius}set radius(e){this._radius!==e&&(this._radius=e,this._updateManipulators())}_updateManipulators(){for(let e=0;e<this._arrowManipulatorInfos.length;e++)this._updateArrowManipulator(this._arrowManipulatorInfos[e],e);this._updateManipulatorTransform()}_updateArrowManipulator({manipulator:e,transform:t},i){const s=this._radius/et,n=Jd*s,r=n*Math.sqrt(3)/2,o=Da(this._opaqueMaterial,r,n/2,n/2,Vo);Kr(o,ya(ne.get(),B(w.get(),0,-r/3,0))),e.renderObjects=[new E(o,x.Focused),new E(o.instantiate({material:this._transparentMaterial}),x.Unfocused)],e.radius=r/3*2*1.2;const l=Gs(ne.get(),i*Math.PI/2),c=ya(ne.get(),B(w.get(),0,Kd*s,0));Ke(t,l,c)}_createManipulators(){for(let e=0;e<4;e++){const t=this._createArrowManipulator(e);this._arrowManipulatorInfos.push(t)}this._updateManipulatorTransform()}_updateManipulatorTransform(){const e=this.angle,t=zr(ne.get(),e,F(0,0,1));if(m(t))return;const i=Sa(ne.get(),B(w.get(),this.displayScale,this.displayScale,this.displayScale)),s=Ke(ne.get(),i,t);for(const n of this._arrowManipulatorInfos){const r=Ke(ne.get(),s,n.transform);n.manipulator.modelTransform=r}}_createArrowManipulator(e){const t=new ue({view:this._view,autoScaleRenderObjects:!1,worldOriented:!0,focusMultiplier:1,touchMultiplier:1,collisionType:{type:"disc",direction:F(0,0,1)}}),i={manipulator:t,transform:fe()};return this._updateArrowManipulator(i,e),this._handles.add(t.events.on("drag",s=>{this._updateAfterDrag&&s.action==="end"&&!this.dragging&&(this._updateManipulatorTransform(),this._updateAfterDrag=!1)})),i}_createMaterial(e=1){const t=J.toUnitRGBA(P.main);return t[3]*=e,new ze({color:t,transparent:e!==1,cullFace:ie.Back,renderOccluded:R.Transparent})}get test(){return{arrowManipulators:this._arrowManipulatorInfos.map(({manipulator:e})=>e)}}},mp=class{constructor(){this._view=null,this._elevationInfo=null,this._lastDragEvent=null,this._next=null,this._enabled=!1}get enabled(){return this._enabled}set enabled(e){if(this._enabled!==e&&d(this._lastDragEvent)&&d(this._next)){const t=this._lastDragEvent.mapEnd,i=this._snap(this._lastDragEvent.screenEnd);if(d(i)){const s={action:"update",mapStart:this._lastDragEvent.mapStart,mapEnd:e===!0?i:t,screenStart:this._lastDragEvent.screenEnd,screenEnd:this._lastDragEvent.screenEnd};this._next.execute(s)}}this._enabled=e}_snap(e){const t=d(this._view)?this._view.toMap(e,{exclude:[]}):null;return d(t)&&d(this._view)&&(t.z=Ur(t,this._view,this._elevationInfo)),t}createDragEventPipelineStep(e,t){this._view=e,this._elevationInfo=t,this._lastDragEvent=null;const i=new Vs;return this._next=i,[s=>{if(this._lastDragEvent=s.action!=="end"?{...s}:null,this._enabled){const n=this._snap(s.screenEnd);return d(n)?{action:s.action,mapStart:s.mapStart,mapEnd:n,screenStart:s.screenStart,screenEnd:s.screenEnd}:null}return{action:s.action,mapStart:s.mapStart,mapEnd:s.mapEnd,screenStart:s.screenStart,screenEnd:s.screenEnd}},i]}},fp=class extends qi{constructor(e){super(),this._snapToScene=new mp,this._discMaterial=this._createMaterial(),this._discMaterialTransparent=this._createMaterial(.5),this._scale=1,this._radius=et,this._view=e.view,this._tool=e.tool,e.snapToScene!=null&&(this.snapToScene=e.snapToScene),e.radius!=null&&(this._radius=e.radius),this._createManipulator(),this.forEachManipulator(t=>this._tool.manipulators.add(t))}destroy(){this.forEachManipulator(e=>{this._tool.manipulators.remove(e),e.destroy()}),this._tool=null,this._view=null,this._manipulator=null}forEachManipulator(e){e(this._manipulator,I.TRANSLATE_XY)}get displayScale(){return this._scale}set displayScale(e){this._scale=e,this._updateManipulatorTransform()}get snapToScene(){return this._snapToScene.enabled}set snapToScene(e){this._snapToScene.enabled=e}get radius(){return this._radius}set radius(e){e!==this._radius&&(this._radius=e,this._updateManipulator())}createGraphicDragPipeline(e,t,i){const s=t.graphic,n=K(s),r=C(s.geometry).spatialReference;return Za(t,i,o=>this.createDragPipeline((l,c,p,u,g)=>({steps:c,cancel:p}=e(l,c,p,u,g),o(l,c,p)),n,r,s),this._view.state.viewingMode)}createDragPipeline(e,t,i,s){const n=this._view;return Ne(this._manipulator,(r,o,l,c,p)=>{const u=o.next(Si(n,r.elevationAlignedLocation)).next(Xa(n,r.elevationAlignedLocation,t,i,s)).next(...this._snapToScene.createDragEventPipelineStep(n,t)).next(g=>({...g,manipulatorType:I.TRANSLATE_XY})).next(li());e(r,u,l,c,p)})}_updateManipulatorTransform(){const e=Sa(ne.get(),B(w.get(),this.displayScale,this.displayScale,this.displayScale));this._manipulator.modelTransform=e}_createManipulator(){const e=this._view;this._manipulator=new ue({view:e,worldSized:!1,autoScaleRenderObjects:!1,focusMultiplier:1,touchMultiplier:1,collisionType:{type:"disc",direction:F(0,0,1)},worldOriented:!0}),this._updateManipulator()}_updateManipulator(){const e=Dc(this._discMaterial,Vo,1,Io,F(0,0,1),F(0,0,0));e.transformation=Sa(fe(),F(this._radius,this._radius,this._radius)),this._manipulator.renderObjects=[new E(e,x.Focused),new E(e.instantiate({material:this._discMaterialTransparent}),x.Unfocused)],this._manipulator.radius=Qd*(this._radius/et)}_createMaterial(e=1){const t=J.toUnitRGBA(P.main);return t[3]*=e,new ze({color:t,transparent:e!==1,cullFace:ie.Back,renderOccluded:R.Transparent})}get test(){return{discManipulator:this._manipulator}}},vp=class extends qi{constructor(e){super(),this._radius=et,this.events=new ke,this._tool=e.tool,this._view=e.view,e.radius!=null&&(this._radius=e.radius),this._createManipulator(),this.forEachManipulator(t=>this._tool.manipulators.add(t))}destroy(){this.forEachManipulator(e=>{this._tool.manipulators.remove(e),e.destroy()})}forEachManipulator(e){e(this._manipulator,I.TRANSLATE_Z)}createGraphicDragPipeline(e,t,i){const s=C(t.graphic.geometry).spatialReference;return Za(t,i,n=>this.createDragPipeline((r,o,l,c,p)=>({steps:o,cancel:l}=e(r,o,l,c,p),n(r,o,l)),s),this._view.state.viewingMode)}createDragPipeline(e,t){const i=this._view;return Ne(this._manipulator,(s,n,r,o,l)=>{const c=n.next(p=>({...p,manipulatorType:I.TRANSLATE_Z})).next(jo(i,s.renderLocation,t)).next(li());e(s,c,r,o,l)})}get radius(){return this._radius}set radius(e){e!==this._radius&&(this._radius=e,this._updateManipulator())}_updateManipulator(){const e=this._radius/et,t=y.zManipulator.height*e,i=y.zManipulator.coneHeight*e,s=y.zManipulator.coneWidth*e,n=y.zManipulator.width*e,r=[F(0,0,0),F(0,0,t)],o=[F(0,0,0),F(0,0,t+i)],l=O=>{const z=fe();return _a(z,z,[0,0,t]),Fr(z,z,Math.PI/2),z},c=l(0),p=(O,z)=>{const W=Pl(y.zManipulator.color,z);return[W.r/255,W.g/255,W.b/255,y.zManipulator.color.a*O]},u=dt(p(1,.25),R.Occlude),g=dt(p(1,0),R.Occlude),v=dt(p(.7,0),y.zManipulator.renderOccluded),b=dt(p(.85,0),y.zManipulator.renderOccluded),f=Pc(u,r,n/2,16,!1),S=Ms(u,i,s/2,16,!1);S.transformation=c,this._manipulator.renderObjects=[new E(S,x.Unfocused),new E(f,x.Unfocused),new E(S.instantiate({material:g}),x.Focused),new E(f.instantiate({material:g}),x.Focused),new E(S.instantiate({material:v}),x.Unfocused),new E(f.instantiate({material:v}),x.Unfocused),new E(S.instantiate({material:b}),x.Focused),new E(f.instantiate({material:b}),x.Focused)],this._manipulator.radius=n/2+2,this._manipulator.collisionType={type:"line",paths:[o]}}_createManipulator(){const e=new ue({view:this._view,autoScaleRenderObjects:!1,worldSized:!1,selectable:!1,cursor:"ns-resize",elevationInfo:this.elevationInfo,worldOriented:!0,collisionPriority:1.6});e.applyObjectTransform=t=>{const i=this._view.state.camera,s=pr;this._view.renderCoordsHelper.toRenderCoords(this._manipulator.elevationAlignedLocation,s);const n=Dl(i.eye,s),r=i.computeRenderPixelSizeAtDist(n),o=V(Zt,s,i.eye);re(o,o);const l=yp;this._view.renderCoordsHelper.worldUpAtPosition(pr,l);const c=Math.abs(Ma(o,l)),p=He(Zt,o,l),u=He(Zt,p,l),g=va(c,.01,1),v=1-Math.sqrt(1-g*g)/g/i.fullWidth,b=this._radius/et,f=y.zManipulator.width*b;pe(u,re(u,u),(1/v-1)*n+r*f),t[12]-=Zt[0],t[13]-=Zt[1],t[14]-=Zt[2]},this._manipulator=e,this._updateManipulator()}get test(){return{manipulator:this._manipulator}}};const pr=M(),Zt=M(),yp=M();let zt=class extends qi{constructor(e){super(),this._handles=new oi,this._interactive=!0;const{tool:t,view:i,snapToScene:s,radius:n}=e;this._view=i,this.xyManipulation=new fp({tool:t,view:i,snapToScene:s,radius:n}),this.xyAxisManipulation=new gp({tool:t,view:i,radius:n}),this.zManipulation=new vp({tool:t,view:i,radius:n}),this.xyManipulation.available=e.xyAvailable,this.xyAxisManipulation.available=e.xyAxisAvailable,this.zManipulation.available=e.zAvailable,this._autoHideXYAxis(),this.forEachManipulator(r=>this._handles.add(r.events.on("grab-changed",()=>this._updateManipulatorInteractivity())))}destroy(){this._handles.destroy(),this.xyManipulation.destroy(),this.xyAxisManipulation.destroy(),this.zManipulation.destroy()}createGraphicDragPipeline(e,t,i){return me([this.xyManipulation.createGraphicDragPipeline((s,n,r,o,l)=>e(D.XY,s,n,r,o,l),t,i),this.xyAxisManipulation.createGraphicDragPipeline((s,n,r,o,l)=>e(D.XY_AXIS,s,n,r,o,l),t,i),this.zManipulation.createGraphicDragPipeline((s,n,r,o,l)=>e(D.Z,s,n,r,o,l),t,i)])}createDragPipeline(e,t,i,s){return me([this.xyManipulation.createDragPipeline((n,r,o,l,c)=>e(D.XY,n,r,o,l,c),t,i,s),this.xyAxisManipulation.createDragPipeline((n,r,o,l,c)=>e(D.XY_AXIS,n,r,o,l,c),t,i,s),this.zManipulation.createDragPipeline((n,r,o,l,c)=>e(D.Z,n,r,o,l,c),i)])}set snapToScene(e){this.xyManipulation.snapToScene=e}set angle(e){this.xyAxisManipulation.angle=e}set interactive(e){this._interactive!==e&&(this._interactive=e,this._updateManipulatorInteractivity())}set radius(e){this.xyAxisManipulation.radius=e,this.xyManipulation.radius=e,this.zManipulation.radius=e}set displayScale(e){this.xyManipulation.displayScale=e,this.xyAxisManipulation.displayScale=e}forEachManipulator(e){this.xyManipulation.forEachManipulator(t=>e(t,I.TRANSLATE_XY)),this.xyAxisManipulation.forEachManipulator(t=>e(t,I.TRANSLATE_XY)),this.zManipulation.forEachManipulator(t=>e(t,I.TRANSLATE_Z))}get _xyAxisVisible(){const e=this.xyManipulation.someManipulator(t=>t.focused)||this.xyAxisManipulation.someManipulator(t=>t.focused);return this._view.inputManager&&this._view.inputManager.latestPointerType==="touch"||e}_autoHideXYAxis(){const e=this.xyAxisManipulation,t=this.xyManipulation;if(Ar("esri-mobile"))return;const i=[];t.forEachManipulator(n=>i.push(n)),e.forEachManipulator(n=>i.push(n));const s=()=>{const n=[];this._xyAxisVisible||e.forEachManipulator(r=>n.push(r.disableDisplay())),this._handles.remove(ur),this._handles.add(n,ur)};for(const n of i)this._handles.add(n.events.on("focus-changed",s));this._view.inputManager&&this._handles.add(ai(()=>{var n;return(n=this._view.inputManager)==null?void 0:n.latestPointerType},s)),s()}_updateManipulatorInteractivity(){const e=this.grabbing;this.forEachManipulator(t=>{t.interactive=!e&&this._interactive||t.grabbing})}static radiusForSymbol(e){const t=d(e)&&e.type==="point-3d"&&e.symbolLayers;return t&&t.length>0&&t.some(i=>i.type==="icon")?zo:et}};const ur="disable-xy-axis-display";var D;(function(a){a[a.XY=0]="XY",a[a.XY_AXIS=1]="XY_AXIS",a[a.Z=2]="Z"})(D||(D={}));let Zs=class extends qi{constructor(e){super(),this._view=e.view,this._tool=e.tool,this._graphicState=e.graphicState,this._createManipulator(),this.forEachManipulator(t=>this._tool.manipulators.add(t))}destroy(){this.forEachManipulator(e=>{this._tool.manipulators.remove(e),e.destroy()}),this._tool=null,this._view=null,this._manipulator=null,this._graphicState=null}forEachManipulator(e){e(this._manipulator,I.TRANSLATE_XY)}createGraphicDragPipeline(e){return Za(this._graphicState,e,t=>this.createDragPipeline(t),this._view.state.viewingMode)}createDragPipeline(e){const t=this._view,i=this._graphicState.graphic,s=d(i.geometry)?i.geometry.spatialReference:null;return Ne(this._manipulator,(n,r,o,l,c)=>{const p=r.next(hp(c,t,i,s)).next(Jt()).next(li());e(n,p,o,l,c)})}_createManipulator(){const e=this._view,t=this._graphicState.graphic;this._manipulator=new vh({graphic:t,view:e,selectable:!0,cursor:"move"})}};class bp{constructor(e){this.allGraphics=e,this.type="graphic-move-start"}}class Mp{constructor(e,t,i){this.dx=e,this.dy=t,this.allGraphics=i,this.type="graphic-move"}}let _r=class{constructor(e){this.allGraphics=e,this.type="graphic-move-stop"}},st=class extends Nr(ke.EventedMixin(za)){constructor(e){super(e),this.graphics=new $l,this.enableZ=!0,this.tooltipOptions=new xt,this.type="move-3d",this._tooltip=null}initialize(){const{graphics:e,view:t}=this;this.addHandles([e.on("change",()=>this._refreshManipulators()),G(()=>this.tooltipOptions.enabled,i=>{this._tooltip=i?new ji({view:t}):k(this._tooltip)},Cs)]),this._refreshManipulators(),this.finishToolCreation()}destroy(){this._tooltip=k(this._tooltip),this._moveManipulation=k(this._moveManipulation),this.graphics.removeAll(),this._set("view",null)}get updating(){return this.updatingHandles.updating}reset(){}_refreshManipulators(){var t;this.handles.removeAll(),(t=this._moveManipulation)==null||t.destroy(),this.manipulators.removeAll();const e=this.graphics.toArray().filter(i=>nc(i)===Br.SUPPORTED).map(i=>new Sp(i));e.length&&(this._createManipulators(e),this._createVisualElements(e),this.handles.add(e.map(i=>this.view.trackGraphicState(i.state))),this._updateMoveManipulation(e))}_createManipulators(e){for(const t of e){const i=t.state;t.manipulationXY=new Zs({tool:this,view:this.view,graphicState:i}),t.manipulationXY.forEachManipulator(s=>this.handles.add([s.events.on("immediate-click",n=>{this.emit("immediate-click",{...n,graphic:i.graphic}),n.stopPropagation()}),s.events.on("grab-changed",({action:n})=>{const{tooltipOptions:r,_tooltip:o}=this;m(o)||(n==="start"?o.info=new Ci({tooltipOptions:r}):o.clear())})])),this.handles.add(t.manipulationXY.createDragPipeline((s,n,r,o)=>this._buildDragEventPipeline(e,D.XY,s,n,r,o)))}this._createMoveManipulation(e)}_createMoveManipulation(e){const t=new zt({tool:this,view:this.view,snapToScene:!1,xyAvailable:!0,xyAxisAvailable:!0,zAvailable:!0,radius:e.length===1?zt.radiusForSymbol(e[0].graphic.symbol):et});this._moveManipulation=t,t.elevationInfo={mode:"absolute-height",offset:0},t.forEachManipulator(r=>{this.handles.add(r.events.on("immediate-click",o=>{t.zManipulation.hasManipulator(r)||this.graphics.length!==1||this.emit("immediate-click",{...o,graphic:this.graphics.getItemAt(0)}),o.stopPropagation()}))});const i=r=>o=>{this.handles.add(o.events.on("focus-changed",({action:l})=>{const c=this._tooltip;m(c)||(l==="focus"?this._updateMoveTooltip(e,r):c.clear())}))};this._moveManipulation.xyManipulation.forEachManipulator(i(D.XY)),this._moveManipulation.xyAxisManipulation.forEachManipulator(i(D.XY_AXIS)),this._moveManipulation.zManipulation.forEachManipulator(i(D.Z));const s=()=>this._updateMoveManipulation(e);for(const r of e)this.handles.add([r.state.on("changed",s),G(()=>r.state.displaying,s)]);const n=e[e.length-1];this.handles.add(n.state.on("changed",()=>this._updateMoveManipulationAngle(n))),this.handles.add(t.createDragPipeline((r,o,l,c,p)=>this._buildDragEventPipeline(e,r,o,l,c,p),K(n.graphic),C(n.graphic.geometry).spatialReference,n.graphic)),this._updateMoveManipulationAngle(n)}_createVisualElements(e){for(const t of e){const i=t.graphic,s=Ya({view:this.view,graphic:i,forEachManipulator:n=>{var r;(r=t.manipulationXY)==null||r.forEachManipulator(n),this._moveManipulation.forEachManipulator(n)},onManipulatorsChanged:()=>It()});m(s)||(t.geometryRepresentation=s.visualElement,t.geometryRepresentation instanceof Nt&&this.handles.add([t.geometryRepresentation.events.on("attachment-origin-changed",()=>{t.state.isDraped||this._updateMoveManipulation(e)}),G(()=>t.state.isDraped,()=>this._updateMoveManipulation(e))]),this.handles.add(s))}}_updateMoveManipulationAngle(e){this._moveManipulation.angle=Uo(e.graphic.geometry)}_updateMoveManipulation(e){const t=Ht(0,0,0,this.view.spatialReference);let i=0,s=!1;const n=this._moveManipulation;for(const r of e){if(!r.state.displaying)continue;const o=r.state.graphic;this.enableZ&&Vt(o)&&(s=!0);const l=r.geometryRepresentation instanceof Nt&&!r.state.isDraped?r.geometryRepresentation.attachmentOrigin:La(this.view,o);if(d(l)){const{x:c,y:p,z:u}=l;t.x+=c,t.y+=p,u&&(t.z??(t.z=0),t.z+=u),i++}}i>0?(t.x/=i,t.y/=i,t.z??(t.z=0),t.z/=i,n.location=t,n.xyManipulation.available=!0,n.xyAxisManipulation.available=!0,n.zManipulation.available=s):n.available=!1}_buildDragEventPipeline(e,t,i,s,n,r){const o=[],l=[];let c=null,p=null;const u=()=>{for(const g of o)g.dragging=!1;o.length=0,l.length=0,c=null,p=null,this._moveManipulation.interactive=!0};if(e.length===1&&t===D.XY){const g=e[0].graphic;({steps:s,cancel:n}=this._buildSnappingPipelineSteps(g,K(g),s,n,r))}return n=n.next(g=>p==null?void 0:p(g)).next(()=>(this.emit("graphic-move-stop",new _r(l)),this.destroyed||u(),null)),{steps:s=s.next(g=>{var v,b;if(g.action==="start"){o.length=0,l.length=0;for(const f of e)f.dragging||!((v=f.manipulationXY)!=null&&v.hasManipulator(i))&&((b=f.manipulationXY)!=null&&b.grabbing)||(o.push(f),l.push(f.graphic),f.dragging=!0);if(l.length!==0&&(this._moveManipulation.interactive=!1,c=rc(l,this.view.state.viewingMode),p=oc(l),this.emit("graphic-move-start",new bp(l)),this.destroyed))return null}return l.length!==0?g:null}).next(g=>c==null?void 0:c(g)).next(g=>(this._updateMoveTooltip(e,t,g),g)).next(g=>{switch(g.action){case"start":case"update":if(g.translationX||g.translationY||g.translationZ){const v=this.view.toScreen(g.mapStart),b=this.view.toScreen(g.mapEnd),f=b.x-v.x,S=b.y-v.y;if(this.emit("graphic-move",new Mp(f,S,l)),this.destroyed)return null}break;case"end":if(this.emit("graphic-move-stop",new _r(l)),this.destroyed)return null;u()}return null}),cancel:n}}_updateMoveTooltip(e,t,i){const{tooltipOptions:s,_tooltip:n}=this;if(m(n))return;n.clear();const r=e.length===0?"absolute-height":e[0].state.isDraped?"on-the-ground":"absolute-height";switch(t){case D.XY:n.info=new Ci({tooltipOptions:s}),this._updateMoveTooltipDistance(n.info,i,(o,l)=>pt(o,l,r));break;case D.XY_AXIS:n.info=new ks({tooltipOptions:s}),this._updateMoveTooltipDistance(n.info,i,(o,l)=>{const c=pt(o,l,r);return xa(c,Ia(i))});break;case D.Z:n.info=new Ua({tooltipOptions:s}),this._updateMoveTooltipDistance(n.info,i,wa)}}_updateMoveTooltipDistance(e,t,i){if(d(t)&&t.action!=="end"){const{mapStart:s,mapEnd:n}=t,r=i(s,n);e.distance=d(r)?r:Ti}}_buildSnappingPipelineSteps(e,t,i,s,n){const r=e.geometry;if(m(r)||r.type!=="point"&&r.type!=="mesh")return{steps:i,cancel:s};const o=(r.type==="point"?r:r.anchor).clone(),l=new Oa({elevationInfo:t,pointer:n,editGeometryOperations:Fa.fromGeometry(o,this.view.state.viewingMode),visualizer:new Gi,excludeFeature:e}),c=this.snappingManager,{snappingStep:p,cancelSnapping:u}=Ea({snappingContext:l,snappingManager:c,updatingHandles:this.updatingHandles});return s=s.next(u),{steps:i=i.next(g=>(o.z=Nl(this.view,o,K(e),{mode:"absolute-height",offset:0}),{...g,snapOrigin:l.coordinateHelper.pointToVector(o)})).next(...p),cancel:s}}get test(){return{tooltip:this._tooltip}}};h([_({constructOnly:!0,nonNullable:!0})],st.prototype,"view",void 0),h([_()],st.prototype,"graphics",void 0),h([_({constructOnly:!0,nonNullable:!0})],st.prototype,"enableZ",void 0),h([_({constructOnly:!0,type:xt})],st.prototype,"tooltipOptions",void 0),h([_({constructOnly:!0})],st.prototype,"snappingManager",void 0),h([_()],st.prototype,"type",void 0),h([_()],st.prototype,"updating",null),st=h([Q("esri.views.3d.interactive.editingTools.graphicMove3D.GraphicMoveTool")],st);let Sp=class{constructor(e){this.geometryRepresentation=null,this.manipulationXY=null,this.dragging=!1,this.state=new we({graphic:e})}get graphic(){return this.state.graphic}};function gr(a,e,t){const i=t.mode==="on-the-ground"?yn.XY:yn.XYZ;return new lc(a,i,e,0)}function mr(a,e,t){const i=M();if(!a.renderCoordsHelper.toRenderCoords(e,i))return null;const s=fr(a,e,Zi(t.plane)),n=fr(a,e,t.edgeDirection);if(m(s)||m(n))return null;const r=He(M(),s,n);return Ft(i,r,hi())}function fr(a,e,t){const i=Ht(e.x+t[0],e.y+t[1],e.z+t[2],e.spatialReference),s=M(),n=M();return a.renderCoordsHelper.toRenderCoords(e,s)&&a.renderCoordsHelper.toRenderCoords(i,n)?jr(n,s,n):null}function wp(a,e,t){const i=Zi(a),s=jr(M(),e,t),n=He(M(),s,i),r=He(M(),s,n);return lh(s[0],s[1],s[2],0,n[0],n[1],n[2],0,r[0],r[1],r[2],0,0,0,0,1)}function xp(a,e,t){const i=t.projectToRenderScreen(a,gs()),s=t.projectToRenderScreen(e,gs());return d(i)&&d(s)?Cl(V(i,i,s)):0}let Mt=class extends Va{constructor(e){super(e),this.type="reshape-edge-offset",this.distance=Ti,this.area=null,this.totalLength=null}};h([_()],Mt.prototype,"type",void 0),h([_()],Mt.prototype,"distance",void 0),h([_()],Mt.prototype,"area",void 0),h([_()],Mt.prototype,"totalLength",void 0),Mt=h([Q("esri.views.interactive.tooltip.ReshapeEdgeOffsetTooltipInfo")],Mt);let q=class extends ke.EventedMixin(Is){constructor(a){super(a),this._vertexManipulatorMaterial=dt(y.colorToVec4(y.reshapeManipulators.vertex.color),y.reshapeManipulators.vertex.renderOccluded),this._vertexManipulatorOutlineMaterial=mi(y.colorToVec4(y.reshapeManipulators.vertex.outlineColor),y.reshapeManipulators.vertex.renderOccluded),this._vertexManipulatorHoverOutlineMaterial=mi(y.colorToVec4(y.reshapeManipulators.vertex.hoverOutlineColor),y.reshapeManipulators.vertex.renderOccluded),this._edgeManipulatorMaterial=dt(y.colorToVec4(y.reshapeManipulators.edge.color),y.reshapeManipulators.edge.renderOccluded),this._edgeManipulatorOutlineMaterial=mi(y.colorToVec4(y.reshapeManipulators.edge.outlineColor),y.reshapeManipulators.edge.renderOccluded),this._edgeOffsetManipulatorMaterial=dt(y.colorToVec4(y.reshapeManipulators.edgeOffset.color),y.reshapeManipulators.edgeOffset.renderOccluded,!1),this._edgeOffsetManipulatorHoverMaterial=dt(y.colorToVec4(y.reshapeManipulators.edgeOffset.hoverColor),y.reshapeManipulators.edgeOffset.renderOccluded,!1),this._selectedManipulatorMaterial=dt(y.colorToVec4(y.reshapeManipulators.selected.color),y.reshapeManipulators.selected.renderOccluded),this._selectedManipulatorOutlineMaterial=mi(y.colorToVec4(y.reshapeManipulators.selected.outlineColor),y.reshapeManipulators.selected.renderOccluded),this._selectedManipulatorHoverOutlineMaterial=mi(y.colorToVec4(y.reshapeManipulators.selected.hoverOutlineColor),y.reshapeManipulators.selected.renderOccluded),this._selectedIndex=0,this._manipulatorHandles=new oi,this._manipulatorInfos=[],this._numGrabbing=0,this._numDragging=0,this._reshapeEventState=H.NONE,this._recreatingManipulators=!1,this.outputGeometry=null,this._vertexLaserLineVisualElement=null}initialize(){const{graphic:a,view:e}=this,t=this._graphicState=new we({graphic:a});this._tooltip=new ji({view:e}),this.addHandles([G(()=>t.displaying,i=>{for(const s of this._manipulatorInfos)s.manipulator.available=i}),G(()=>({labels:this._segmentLabels,enabled:this._labelOptions.enabled,edgeOffsetEnabled:this.enableEdgeOffset}),({labels:i,enabled:s,edgeOffsetEnabled:n})=>{d(i)&&(i.visible=s,i.edgeDistance=n?"far":"default")},qe),ai(()=>!this._tooltipOptions.enabled,()=>this._tooltip.clear(),qe),this.view.trackGraphicState(t)])}destroy(){this._segmentLabels=k(this._segmentLabels),this._tooltip=k(this._tooltip),this._removeManipulators()}get inputGeometry(){return d(this._editGeometryOperations)?this._editGeometryOperations.data.geometry:null}set inputGeometry(a){this._recreateEditGeometryAndManipulators(a)}get updating(){return this.updatingHandles.updating}get manipulators(){return this.tool.manipulators}get view(){return this.tool.view}get graphic(){return this.tool.graphic}get enableZShape(){return this.tool.enableZShape}get enableZVertex(){return this.tool.enableZVertex}get enableMoveGraphic(){return this.tool.enableMoveGraphic}get enableMidpoints(){return this.tool.enableMidpoints}get enableEdgeOffset(){return this.tool.enableEdgeOffset}get _labelOptions(){return this.tool.labelOptions}get _tooltipOptions(){return this.tool.tooltipOptions}removeSelectedVertices(){const a=this._manipulatorInfos.filter(e=>e.manipulator.selected&&e.type==="vertex");this._removeVertices(a)}onManipulatorSelectionChanged(){this.emit("manipulators-changed")}_removeManipulators(){this._manipulatorHandles.removeAll(),this._moveManipulation=k(this._moveManipulation),this._graphicMoveManipulation=k(this._graphicMoveManipulation),this.manipulators.removeAll(),this._manipulatorInfos=[],this._numGrabbing=0,this._numDragging=0}_createManipulators(a){if(m(this._editGeometryOperations))return;const e=K(this.graphic);for(const t of this._editGeometryOperations.data.components){const i=a==null?void 0:a.byComponentIndex.get(t.index);for(const s of t.vertices){const n=i==null?void 0:i.has(s.index);this._createVertexOrEdgeManipulator(s,e,n)}for(const s of t.edges)this._createVertexOrEdgeManipulator(s,e)}this._createGraphicMoveManipulation(),this._createMoveManipulation(e),this._createVisualElements()}get canRedo(){return d(this._editGeometryOperations)&&this._editGeometryOperations.canRedo}get canUndo(){return d(this._editGeometryOperations)&&this._editGeometryOperations.canUndo}redo(){if(m(this._editGeometryOperations))return null;const a=this._editGeometryOperations.redo();return d(a)&&(this.outputGeometry=this._editGeometryOperations.data.geometry,this._recreateManipulators()),a}undo(){if(m(this._editGeometryOperations))return null;this.emit("undo");const a=this._editGeometryOperations.undo();return d(a)&&(this.outputGeometry=this._editGeometryOperations.data.geometry,this._recreateManipulators()),a}_recreateManipulators(){this._recreatingManipulators||(this._recreatingManipulators=!0,this._removeManipulators(),this._tooltip.clear(),this._createManipulators(),this._recreatingManipulators=!1)}_recreateEditGeometryAndManipulators(a){const e={byComponentIndex:new Map};if(d(a)&&d(this.inputGeometry)&&Ll(a,this.inputGeometry)){for(const t of this._manipulatorInfos)if(t.type==="vertex"&&t.manipulator.selected){const{index:i,component:{index:s}}=t.handle,{byComponentIndex:n}=e,r=n.get(s)||new Set;r.add(i),n.set(s,r)}}this._recreatingManipulators=!0,this._removeManipulators(),this._tooltip.clear(),this._editGeometryOperations=k(this._editGeometryOperations),this._segmentLabels=k(this._segmentLabels),m(a)||(this._editGeometryOperations=Fa.fromGeometry(a,this.view.state.viewingMode),this._createManipulators(e),this._segmentLabels=new $a({context:{view:this.view,editGeometryOperations:this._editGeometryOperations,elevationInfo:K(this.graphic),labelOptions:this._labelOptions,graphic:this.graphic,graphicState:this._graphicState},visible:this._labelOptions.enabled})),this._recreatingManipulators=!1}_perGraphicManipulatorDragAction(a,e){if(e.action==="end")return e;let t=0;const i=[],s=this._manipulatorInfos.some(r=>r.type==="vertex"&&r.manipulator.selected),n=a===Kt.SELECTED_OR_ALL&&s;for(const r of this._manipulatorInfos)r.type==="vertex"&&(r.manipulator.grabbing||n&&!r.manipulator.selected||i.push(r),t++);if(i.length===0)return e;if(this._moveVertices(i,e),i.length===t){if(this._updateEventState(H.MOVING),this.destroyed)return e;this.emit("move",{type:"move",dx:e.screenDeltaX,dy:e.screenDeltaY,mover:this.graphic})}else{if(this._updateEventState(H.RESHAPING),this.destroyed)return e;this.emit("reshape",{type:"reshape",mover:this.graphic})}return e}_isMultiVertexSelection(){return this._manipulatorInfos.reduce((a,e)=>e.type==="vertex"&&e.manipulator.selected?a+1:a,0)>1}_perVertexManipulatorDragAction(a){if(this._updateEventState(H.RESHAPING),this.destroyed)return;const{mapDeltaX:e,mapDeltaY:t,mapDeltaZ:i}=a;if(!e&&!t&&!i)return;const s=[];for(const n of this._manipulatorInfos)n.type==="vertex"&&(n.manipulator.selected&&!n.manipulator.grabbing||n===a.info)&&s.push(n);this._moveVertices(s,a,ct.ACCUMULATE_STEPS),this.emit("reshape",{type:"reshape",mover:this.graphic})}_updateEventState(a){if(a===this._reshapeEventState)return!1;switch(a){case H.NONE:if(this._numGrabbing!==0||this._numDragging!==0)return!1;switch(this._reshapeEventState){case H.MOVING:this.emit("move",{type:"move-stop",dx:0,dy:0,mover:this.graphic});break;case H.RESHAPING:this.emit("reshape",{type:"reshape-stop",mover:this.graphic})}break;case H.MOVING:switch(this._reshapeEventState){case H.NONE:this.emit("move",{type:"move-start",dx:0,dy:0,mover:this.graphic});break;case H.RESHAPING:this.emit("reshape",{type:"reshape-stop",mover:this.graphic}),this.destroyed||this.emit("move",{type:"move-start",dx:0,dy:0,mover:this.graphic})}break;case H.RESHAPING:switch(this._reshapeEventState){case H.NONE:this.emit("reshape",{type:"reshape-start",mover:this.graphic});break;case H.MOVING:this.emit("move",{type:"move-stop",dx:0,dy:0,mover:this.graphic}),this.destroyed||this.emit("reshape",{type:"reshape-start",mover:this.graphic})}}if(this.destroyed)return!1;const e=this._reshapeEventState!==a;return this._reshapeEventState=a,e}_createGraphicMoveManipulation(){const{tool:a,view:e}=this,t=this._graphicState;if(this._graphicMoveManipulation=new Zs({tool:a,view:e,graphicState:t}),this.enableMoveGraphic){let i=null;this._manipulatorHandles.add(this._graphicMoveManipulation.createDragPipeline((s,n,r)=>{n.next(o=>this._trackNumDragging(o)).next(o=>(o.action==="start"&&(i=C(this._editGeometryOperations).createUndoGroup()),o)).next(o=>this._perGraphicManipulatorDragAction(Kt.ALL,o)).next(o=>(this._updateTranslateGraphicTooltip(D.XY,o),o)).next(o=>{o.action==="end"&&(this._tooltip.clear(),i=mt(i))}),r.next(()=>this._onDragCancel(!0,()=>i=mt(i)))})),this._graphicMoveManipulation.forEachManipulator(s=>this._manipulatorHandles.add(this._watchAndUpdateGrabState(s,!1)))}else this._graphicMoveManipulation.forEachManipulator(i=>{i.grabbable=!1,i.cursor=null});this._graphicMoveManipulation.forEachManipulator(i=>this._manipulatorHandles.add(i.events.on("immediate-click",s=>{this._manipulatorInfos.some(n=>n.manipulator.selected)?this._clearSelection():this.emit("immediate-click",{...s,graphic:this.graphic}),s.stopPropagation()})))}_createMoveManipulation(a){const{graphic:e,handles:t,tool:i,view:s}=this,n=this._graphicState;this._moveManipulation=new zt({tool:i,view:s,xyAvailable:!0,xyAxisAvailable:!0,zAvailable:this.enableZShape&&Vt(e),snapToScene:!1,radius:zt.radiusForSymbol(e.symbol)}),this._moveManipulation.forEachManipulator(l=>t.add([l.events.on("immediate-click",c=>{this._moveManipulation.zManipulation.hasManipulator(l)||this._manipulatorInfos.some(p=>p.manipulator.selected)||this.emit("immediate-click",{...c,graphic:e}),c.stopPropagation()}),this._watchAndUpdateGrabState(l,!1)]));const r=l=>c=>{t.add(c.events.on("focus-changed",({action:p})=>{p==="focus"&&this._tooltipOptions.enabled?this._updateTranslateTooltip(l):this._tooltip.clear()}))};this._moveManipulation.xyManipulation.forEachManipulator(r(D.XY)),this._moveManipulation.xyAxisManipulation.forEachManipulator(r(D.XY_AXIS)),this._moveManipulation.zManipulation.forEachManipulator(r(D.Z)),this._moveManipulation.elevationInfo={mode:"absolute-height",offset:0};const o=C(e.geometry).spatialReference;t.add([this._moveManipulation.createDragPipeline((l,c,p,u,g)=>{const{snappingStep:v,cancelSnapping:b}=Ea({predicate:f=>!!f.info,snappingManager:i.snappingManager,snappingContext:new Oa({editGeometryOperations:C(this._editGeometryOperations),elevationInfo:a,pointer:g,excludeFeature:e,visualizer:new Gi}),updatingHandles:this.updatingHandles,useZ:!1});return u=u.next(f=>(this._onDragCancel(),f)).next(b),{steps:p=p.next(f=>this._trackNumDragging(f)).next(f=>{const S=this._manipulatorInfos.filter(O=>O.type==="vertex"&&O.manipulator.selected);return f.manipulatorType===I.TRANSLATE_XY&&S.length===1?{...f,info:S[0],snapOrigin:S[0].handle.pos}:f}).next(bn(this.view,a,e)).next(...v).next(Jt()).next(f=>this._perGraphicManipulatorDragAction(Kt.SELECTED_OR_ALL,f)).next(f=>(this._updateTranslateTooltip(l,f),f)),cancel:u}},a,o,e),G(()=>n.displaying,()=>this._updateMoveManipulationPosition(),qe),n.on("changed",()=>{this._recreatingManipulators||this._updateMoveManipulationPosition()}),G(()=>n.isDraped,l=>{this._updateMoveManipulationPosition();const c="align-move-manipulation";l?t.add(this.view.elevationProvider.on("elevation-change",()=>this._updateMoveManipulationPosition()),c):t.remove(c)},qe)])}_createVisualElements(){const{graphic:a,view:e}=this,t=Ya({view:e,graphic:a,forEachManipulator:i=>{if(!this.destroyed&&!this._recreatingManipulators){this._graphicMoveManipulation.forEachManipulator(i),this._moveManipulation.forEachManipulator(i);for(const s of this._manipulatorInfos)i(s.manipulator,I.TRANSLATE_XY)}},onManipulatorsChanged:i=>this.on("manipulators-changed",i)});d(t)&&(this._outlineVisualElement=t.visualElement instanceof Nt?t.visualElement:null),d(this._outlineVisualElement)&&this._manipulatorHandles.add(this._outlineVisualElement.events.on("attachment-origin-changed",()=>{this._graphicState.isDraped||this._updateMoveManipulationPosition()})),this._manipulatorHandles.add(t)}_createEdgeOffsetManipulator(a,e=K(this.graphic)){var v,b;const t=y.reshapeManipulators.edgeOffset,i=t.size/2,s=i+t.collisionPadding,n=i/s,r=n*Math.sqrt(3)/2;m(this._edgeOffsetManipulatorGeometryInside)&&(this._edgeOffsetManipulatorGeometryInside=Da(this._edgeOffsetManipulatorMaterial,r,n/2,n/2,t.height,t.offset)),m(this._edgeOffsetManipulatorGeometryOutside)&&(this._edgeOffsetManipulatorGeometryOutside=Da(this._edgeOffsetManipulatorMaterial,-r,n/2,n/2,t.height,-t.offset));const o=[new E(this._edgeOffsetManipulatorGeometryInside.instantiate(),x.Unfocused),new E(this._edgeOffsetManipulatorGeometryInside.instantiate({material:this._edgeOffsetManipulatorHoverMaterial}),x.Focused),new E(this._edgeOffsetManipulatorGeometryOutside.instantiate(),x.Unfocused),new E(this._edgeOffsetManipulatorGeometryOutside.instantiate({material:this._edgeOffsetManipulatorHoverMaterial}),x.Focused)],l=new ue({view:this.view,renderObjects:o,elevationInfo:e.mode!=="on-the-ground"||ln(this.graphic.symbol)?{mode:"absolute-height",offset:0}:e,worldOriented:!1,focusMultiplier:1,radius:s,available:!(!this.graphic.visible||!((v=this.graphic.layer)!=null&&v.visible)),collisionType:{type:"disc",direction:F(0,0,1)},collisionPriority:1,metadata:{deleting:!1}}),c=new ue({view:this.view,worldSized:!0,worldOriented:!1,available:!(!this.graphic.visible||!((b=this.graphic.layer)!=null&&b.visible)),collisionPriority:-10,cursor:this.enableMoveGraphic?"move":"default",metadata:{deleting:!1}}),p={manipulator:l,handle:a,locationUpdateHandle:null,type:"edge",selectedIndex:0,edgeManipulator:c,elevationInfo:e,visibilityHandle:null};this._autoHideEdgeOffsetManipulator(p,t.minSquaredEdgeLength),this._updateEdgeOffsetManipulator(p);const u=[];for(const f of[p.handle.leftVertex,p.handle.rightVertex]){const S=this._getManipulatorInfoFromHandle(f);d(S)&&u.push(S.manipulator.events.on("location-update",()=>this._updateEdgeOffsetManipulator(p)))}p.locationUpdateHandle=me(u),this._manipulatorHandles.add(p.locationUpdateHandle,l),this._manipulatorHandles.add([this._watchAndUpdateGrabState(l,!0),this._watchAndUpdateGrabState(c,!0)],l),this._manipulatorHandles.add(Ne(l,this._createEdgeOffsetPipeline(p,e)),l),this._manipulatorHandles.add(Ne(c,(f,S,O,z)=>{if(z==="touch")this._createEdgeOffsetPipeline(p,e)(f,S,O);else if(this.enableMoveGraphic){const W=this.graphic,_e=d(W.geometry)?W.geometry.spatialReference:null;S.next(Y=>this._trackNumDragging(Y)).next(Si(this.view,f.elevationAlignedLocation)).next(Xa(this.view,f.elevationAlignedLocation,e,_e,W)).next(li()).next(Jt()).next(Y=>this._perGraphicManipulatorDragAction(Kt.ALL,Y)).next(Y=>(this._updateTranslateGraphicTooltip(D.XY,Y),Y)).next(Y=>{Y.action==="end"&&this._tooltip.clear()}),O.next(()=>this._onDragCancel(!f.metadata.deleting))}}),l);const g=f=>{this._manipulatorInfos.some(S=>S.manipulator.selected)?this._clearSelection():this.emit("immediate-click",{...f,graphic:this.graphic}),f.stopPropagation()};return this._manipulatorHandles.add([l.events.on("immediate-click",g),c.events.on("immediate-click",g),l.events.on("focus-changed",({action:f})=>{const S=this._tooltipOptions;if(f==="focus"&&S.enabled){const O=this._tooltip.info=new Mt({tooltipOptions:S});this._updateTooltipAreaOrTotalLength(O)}else this._tooltip.clear()})],l),this._manipulatorInfos.push(p),this.manipulators.add(l),this.manipulators.add(c),this.emit("manipulators-changed"),p}_autoHideEdgeOffsetManipulator(a,e){const t=a.manipulator,i=a.edgeManipulator,s=()=>{a.visibilityHandle=mt(a.visibilityHandle);const n=this._getManipulatorInfoFromHandle(a.handle.leftVertex),r=this._getManipulatorInfoFromHandle(a.handle.rightVertex),o=d(n)&&d(r)&&xp(n.manipulator.renderLocation,r.manipulator.renderLocation,this.view.state.camera)<e;(!t.focused&&!i.focused||o)&&(t.grabbable=!o,i.grabbable=!o,a.visibilityHandle=me([t.disableDisplay(),{remove:()=>{t.grabbable=!0,i.grabbable=this.enableMoveGraphic}}]))};this._manipulatorHandles.add([t.events.on("focus-changed",s),i.events.on("focus-changed",s),{remove:()=>{mt(a.visibilityHandle),i.metadata.deleting=!0,this.manipulators.remove(i)}}],t),s()}_updateEdgeOffsetManipulator(a){this._updateManipulatorPosition(a);const{coordinateHelper:e}=C(this._editGeometryOperations).data,t=mr(this.view,a.manipulator.elevationAlignedLocation,gr(e,a.handle,C(a.manipulator.elevationInfo))),i=this._getManipulatorInfoFromHandle(a.handle.leftVertex),s=this._getManipulatorInfoFromHandle(a.handle.rightVertex);if(m(i)||m(s))return;const n=i.manipulator.renderLocation,r=s.manipulator.renderLocation,o=d(t)?wp(t,n,r):ch;a.manipulator.modelTransform=o,a.edgeManipulator.elevationAlignedLocation=a.manipulator.elevationAlignedLocation,a.edgeManipulator.modelTransform=o;const l=Re(V(ra,n,r))/2;a.edgeManipulator.collisionType={type:"line",paths:[[[-l,0,0],[l,0,0]]]}}_createEdgeOffsetPipeline(a,e){return(t,i,s)=>{this._clearSelection();const{step:n,cleanup:r}=this._initializeEdgeOffset(a,e);i.next(o=>this._trackNumDragging(o)).next(Si(this.view,t.elevationAlignedLocation)).next(n).next(lp(this.view)).next(Ho(this.view,C(this._editGeometryOperations).data.spatialReference)).next(Jt()).next(this._applyComputeEdgeOffsetDistanceStep()).next(this._applyEdgeOffsetStep(a)).next(this._showEdgeOffsetTooltip()).next(o=>{o.action==="end"&&r()}),s.next(()=>{t.metadata.deleting||(r(),this._onDragCancel())})}}_initializeEdgeOffset(a,e){const t=C(this._editGeometryOperations),i=gr(t.data.coordinateHelper,a.handle,e),s=t.createUndoGroup(),n=mr(this.view,a.manipulator.elevationAlignedLocation,i);if(i.requiresSplitEdgeLeft){const u=this._getManipulatorInfoFromHandle(a.handle.leftVertex.leftEdge);d(u)&&this._splitEdgeManipulator(u,1)}if(i.requiresSplitEdgeRight){const u=this._getManipulatorInfoFromHandle(a.handle.rightVertex.rightEdge);d(u)&&this._splitEdgeManipulator(u,0)}const r=()=>new Gl({paths:[[a.handle.leftVertex.pos,a.handle.rightVertex.pos]],spatialReference:t.data.spatialReference}),o=new Nt({view:this.view,isDraped:this._graphicState.isDraped,geometry:r(),elevationInfo:a.elevationInfo,width:y.visualElements.lineGraphics.outline.width,color:y.colorToVec4(P.main),attached:!0});let l;const c=()=>{this._cleanEdgeOffsetCollapsedEdges(a),l=mt(l)},p=this.on("undo",c);return l=me([xe(o),G(()=>this._graphicState.isDraped,u=>o.isDraped=u),this._graphicState.on("changed",()=>o.geometry=r()),s,p]),{step:u=>m(i)||m(n)?(c(),null):{...u,operation:i,plane:n},cleanup:c}}_applyEdgeOffsetStep(a){return e=>{if(this.destroyed||m(e.operation))return e;this._updateEventState(H.RESHAPING);const{mapDeltaX:t,mapDeltaY:i,mapDeltaZ:s}=e;return(t||i||s)&&(this._offsetEdge(a,e),this.emit("reshape",{type:"reshape",mover:this.graphic})),e}}_applyComputeEdgeOffsetDistanceStep(){return a=>{const{operation:e,mapEnd:t}=a;return m(e)||m(t)?a:(a.action==="start"&&e.selectArrowFromStartPoint(t),{...a,signedDistance:e.signedDistanceToPoint(t)})}}_showEdgeOffsetTooltip(){return a=>{const{mapEnd:e,signedDistance:t,operation:i}=a,s=this._tooltip,n=this._tooltipOptions;if(!n.enabled||m(t))return s.clear(),a;let r=s.info;(m(r)||r.type!=="reshape-edge-offset")&&(r=s.info=new Mt({tooltipOptions:n}));const{coordinateHelper:o}=C(this._editGeometryOperations).data;return r.distance=a.action==="end"?Ti:Op(this._graphicState.isDraped,t*i.selectedArrow,e,i.plane,o),this._updateTooltipAreaOrTotalLength(r),a}}_cleanEdgeOffsetCollapsedEdges(a){var o,l;const e=(o=a.handle.leftVertex.leftEdge)==null?void 0:o.leftVertex,t=a.handle.leftVertex,i=(l=a.handle.rightVertex.rightEdge)==null?void 0:l.rightVertex,s=a.handle.rightVertex,n=C(this._editGeometryOperations).data.coordinateHelper,r=[];if(e&&n.distance(e.pos,t.pos)<os){const c=this._getManipulatorInfoFromHandle(t);d(c)&&r.push(c)}if(n.distance(t.pos,s.pos)<os||i&&n.distance(i.pos,s.pos)<os){const c=this._getManipulatorInfoFromHandle(s);d(c)&&r.push(c)}r.length&&this._removeVertices(r)}_computeVertexManipulatorSizeAndOutline(a){const e=a.size/2,t=e+a.collisionPadding;return{size:e/t,outlineSize:(e+a.outlineSize)/t}}_createVertexOrEdgeManipulator(a,e=K(this.graphic),t=!1){var o;if(a.type==="edge"){if(this.enableEdgeOffset)return this._createEdgeOffsetManipulator(a,e);if(!this.enableMidpoints)return null}if(m(this._vertexManipulatorGeometry)||m(this._vertexManipulatorOutlineGeometry)){const{size:l,outlineSize:c}=this._computeVertexManipulatorSizeAndOutline(y.reshapeManipulators.vertex);this._vertexManipulatorGeometry=bi(this._vertexManipulatorMaterial,l,16,16),this._vertexManipulatorOutlineGeometry=bi(this._vertexManipulatorOutlineMaterial,c,16,16)}if(m(this._edgeManipulatorGeometry)||m(this._edgeManipulatorOutlineGeometry)){const{size:l,outlineSize:c}=this._computeVertexManipulatorSizeAndOutline(y.reshapeManipulators.edge);this._edgeManipulatorGeometry=bi(this._edgeManipulatorMaterial,l,16,16),this._edgeManipulatorOutlineGeometry=bi(this._edgeManipulatorOutlineMaterial,c,16,16)}const i=d(this.graphic.geometry)&&this.graphic.geometry.type==="point"?[]:[new E(this._vertexManipulatorGeometry.instantiate(),Le.Vertex|x.Unselected),new E(this._vertexManipulatorOutlineGeometry.instantiate(),Le.Vertex|x.Unfocused|x.Unselected),new E(this._vertexManipulatorOutlineGeometry.instantiate({material:this._vertexManipulatorHoverOutlineMaterial}),Le.Vertex|x.Focused|x.Unselected),new E(this._vertexManipulatorGeometry.instantiate({material:this._selectedManipulatorMaterial}),x.Selected),new E(this._vertexManipulatorOutlineGeometry.instantiate({material:this._selectedManipulatorOutlineMaterial}),x.Selected|x.Unfocused),new E(this._vertexManipulatorOutlineGeometry.instantiate({material:this._selectedManipulatorHoverOutlineMaterial}),x.Selected|x.Focused)];this.enableMidpoints&&i.push(new E(this._edgeManipulatorGeometry.instantiate({material:this._vertexManipulatorMaterial}),Le.Edge|x.Focused|x.Unselected),new E(this._edgeManipulatorOutlineGeometry.instantiate({material:this._vertexManipulatorHoverOutlineMaterial}),Le.Edge|x.Focused|x.Unselected),new E(this._edgeManipulatorGeometry.instantiate(),Le.Edge|x.Unfocused|x.Unselected),new E(this._edgeManipulatorOutlineGeometry.instantiate(),Le.Edge|x.Unfocused|x.Unselected));const s=new ue({view:this.view,renderObjects:i,elevationInfo:e,focusMultiplier:1,touchMultiplier:1,available:!(!this.graphic.visible||!((o=this.graphic.layer)!=null&&o.visible)),metadata:{deleting:!1}});s.selected=t,this._setTypeSpecificManipulatorSettings(s,a,e);const n=a.type==="edge"?{manipulator:s,handle:a,locationUpdateHandle:null,type:"edge",selectedIndex:0}:{manipulator:s,handle:a,type:"vertex",selectedIndex:0};if(this._manipulatorInfos.push(n),this.manipulators.add(s),this._updateManipulatorPosition(n),n.type==="edge"){const l=[];for(const c of[n.handle.leftVertex,n.handle.rightVertex]){const p=this._getManipulatorInfoFromHandle(c);d(p)&&l.push(p.manipulator.events.on("location-update",()=>this._updateManipulatorPosition(n)))}n.locationUpdateHandle=me(l),this._manipulatorHandles.add(n.locationUpdateHandle,s)}this._manipulatorHandles.add(this._watchAndUpdateGrabState(s,!0),s);const r=Ne(s,(l,c,p,u)=>{let g=null;const{snappingStep:v,cancelSnapping:b}=Ea({predicate:()=>!this._isMultiVertexSelection(),snappingManager:this.tool.snappingManager,snappingContext:new Oa({editGeometryOperations:C(this._editGeometryOperations),elevationInfo:e,pointer:u,excludeFeature:this.graphic,visualizer:new Gi}),updatingHandles:this.updatingHandles,useZ:!1});p=p.next(f=>(this._onDragCancel(!l.metadata.deleting,()=>g=mt(g)),f)).next(b),c.next(f=>this._trackNumDragging(f)).next(f=>{if(f.action==="start"&&(g=C(this._editGeometryOperations).createUndoGroup()),n.type==="edge"){const S=this._splitEdgeManipulator(n);return{...f,info:S,snapOrigin:S.handle.pos}}return{...f,info:n,snapOrigin:n.handle.pos}}).next(Si(this.view,l.elevationAlignedLocation)).next(No(this.view,this.graphic,l.elevationAlignedLocation,l.location.spatialReference,this.graphic)).next(bn(this.view,e,this.graphic)).next(...v).next(Jt()).next(f=>{this._perVertexManipulatorDragAction(f),f.action==="end"&&(g=mt(g)),this._updateTranslateVertexTooltip(l,D.XY,f)})});return this._manipulatorHandles.add([r,s.events.on("immediate-click",l=>this._manipulatorClickCallback(l,n)),s.events.on("select-changed",()=>{n.selectedIndex=++this._selectedIndex,this._updateMoveManipulationPosition()}),s.events.on("focus-changed",({action:l})=>{l==="focus"&&n.type!=="edge"?this._updateTranslateVertexTooltip(s,D.XY):this._tooltip.clear()})],s),this.emit("manipulators-changed"),n}_trackNumDragging(a){switch(a.action){case"start":this._numDragging++;break;case"end":this._numDragging--}return a}_onDragCancel(a=!0,e){switch(this._numDragging--,a&&(this.undo(),this.outputGeometry=d(this._editGeometryOperations)?this._editGeometryOperations.data.geometry:null),d(this.tool.snappingManager)&&this.tool.snappingManager.doneSnapping(),this._tooltip.clear(),this._reshapeEventState){case H.NONE:break;case H.MOVING:this.emit("move",{type:"move",dx:0,dy:0,mover:this.graphic});break;case H.RESHAPING:this.emit("reshape",{type:"reshape",mover:this.graphic})}e&&e(),this.destroyed||this._updateEventState(H.NONE)}_setTypeSpecificManipulatorSettings(a,e,t){switch(e.type){case"vertex":a.state=Le.Vertex,a.selectable=!0,a.cursor="move",a.collisionPriority=2,a.radius=y.reshapeManipulators.vertex.size/2+y.reshapeManipulators.vertex.collisionPadding,a.elevationInfo=t,a.interactive=d(this.graphic.geometry)&&this.graphic.geometry.type!=="point";break;case"edge":a.state=Le.Edge,a.selectable=!1,a.cursor="copy",a.collisionPriority=-1,a.radius=y.reshapeManipulators.edge.size/2+y.reshapeManipulators.edge.collisionPadding,a.elevationInfo=t.mode!=="on-the-ground"||ln(this.graphic.symbol)?{mode:"absolute-height",offset:0}:t}}_watchAndUpdateGrabState(a,e){return a.events.on("grab-changed",t=>this._onGrabStateChanged(a,e,t.action,t.pointerType))}_onGrabStateChanged(a,e,t,i="mouse"){if(!this._recreatingManipulators){if(t==="start")e&&this._updateSelection(a),this._numGrabbing++;else if(this._numGrabbing--,this._updateEventState(H.NONE),this.destroyed)return;this._moveManipulation.interactive=!this._numGrabbing,(i!=="touch"||this.enableEdgeOffset)&&(this._manipulatorInfos.forEach(s=>{s.manipulator.interactive=s.manipulator.grabbing||!this._numGrabbing&&d(this.graphic.geometry)&&this.graphic.geometry.type!=="point","edgeManipulator"in s&&(s.edgeManipulator.interactive=s.edgeManipulator.grabbing||!this._numGrabbing)}),this._graphicMoveManipulation.forEachManipulator(s=>{s.interactive=s.grabbing||!this._numGrabbing}))}}_clearSelection(){for(const a of this._manipulatorInfos)a.manipulator.grabbing||(a.manipulator.selected=!1)}_updateSelection(a){a.grabbing&&!a.selected&&a.selectable&&(this._clearSelection(),a.selected=!0,this.emit("manipulators-changed"))}_removeManipulator(a){m(a)||(a.manipulator.metadata.deleting=!0,this.manipulators.remove(a.manipulator),this._manipulatorHandles.remove(a.manipulator),il(this._manipulatorInfos,a),this.emit("manipulators-changed"))}_getManipulatorInfoFromHandle(a){if(a){for(const e of this._manipulatorInfos)if(a===e.handle)return e}return null}_updateManipulatorPosition(a){if(m(a))return;const e=C(this._editGeometryOperations);if(a.type==="vertex")a.manipulator.location=e.data.coordinateHelper.vectorToDehydratedPoint(a.handle.pos,vi),a.manipulator.grabbing&&d(this._vertexLaserLineVisualElement)&&(this._vertexLaserLineVisualElement.visualElement.intersectsWorldUpAtLocation=a.manipulator.renderLocation);else if(a.type==="edge"){const t=this._getManipulatorInfoFromHandle(a.handle.leftVertex),i=this._getManipulatorInfoFromHandle(a.handle.rightVertex);if(m(t)||m(i))return;const s=t.manipulator,n=i.manipulator;if(d(a.manipulator.elevationInfo)&&a.manipulator.elevationInfo.mode==="on-the-ground"){const r=s.location,o=n.location,l=.5,c=r.x+l*(o.x-r.x),p=r.y+l*(o.y-r.y),u=r.hasZ&&o.hasZ?0:void 0;a.manipulator.location=Ht(c,p,u,e.data.spatialReference)}else $r(ra,s.renderLocation,n.renderLocation,.5),a.manipulator.renderLocation=ra}}_splitEdgeManipulator(a,e=.5){const t=C(this._editGeometryOperations),i=C(t.splitEdge(a.handle,e).createdVertex);a.locationUpdateHandle=mt(a.locationUpdateHandle);const s=K(this.graphic);let n;this.enableEdgeOffset?(this._removeManipulator(a),n=this._createVertexOrEdgeManipulator(i)):(n=a,n.handle=i,n.type="vertex",this._setTypeSpecificManipulatorSettings(a.manipulator,a.handle,s)),i.leftEdge&&this._createVertexOrEdgeManipulator(i.leftEdge),i.rightEdge&&this._createVertexOrEdgeManipulator(i.rightEdge),this.outputGeometry=t.data.geometry,this._updateManipulatorPosition(n),this.enableEdgeOffset||this._updateTranslateVertexTooltip(n.manipulator,D.XY),this._updateSelection(a.manipulator);const r=this._updateEventState(H.RESHAPING),o=t.data.coordinateHelper.vectorToArray(n.handle.pos),l=t.data.components.indexOf(i.component);return this.emit("vertex-add",{type:"vertex-add",vertices:[{coordinates:o,componentIndex:l,vertexIndex:C(i.index)}],added:o}),r&&this._updateEventState(H.NONE),n}_updateMoveManipulationPosition(){const a=B(ra,0,0,0);let e=0,t=!1,i=null,s=null;for(const n of this._manipulatorInfos)n.type==="vertex"&&(n.manipulator.selected?(e++,X(a,a,n.manipulator.renderLocation),m(i)||n.selectedIndex>i.selectedIndex?(s=i,i=n):(m(s)||n.selectedIndex>s.selectedIndex)&&(s=n)):t=!0);if(e===0){const n=this._graphicState.displaying&&this.enableMoveGraphic;this._moveManipulation.xyManipulation.available=n,this._moveManipulation.xyAxisManipulation.available=n,this._moveManipulation.xyAxisManipulation.orthogonalAvailable=n,this._moveManipulation.zManipulation.available=n&&this.enableZShape&&Vt(this.graphic),this._moveManipulation.angle=Uo(C(this.graphic.geometry)),this._moveManipulation.radius=zt.radiusForSymbol(this.graphic.symbol)}else{const n=this._graphicState.displaying;this._moveManipulation.xyManipulation.available=n,this._moveManipulation.xyAxisManipulation.available=n,this._moveManipulation.zManipulation.available=n&&this.enableZVertex&&Vt(this.graphic),this._moveManipulation.xyAxisManipulation.orthogonalAvailable=n&&e!==1;let r=0;if(d(i)){const o=i.handle.pos,l=d(s)?s.handle.pos:i.handle.leftEdge&&i.handle.leftEdge.leftVertex?i.handle.leftEdge.leftVertex.pos:null,c=m(s)&&i.handle.rightEdge&&i.handle.rightEdge.rightVertex?i.handle.rightEdge.rightVertex.pos:null;l&&c?this._moveManipulation.xyAxisManipulation.available=!1:l?r=vr(l,o):c&&(r=vr(o,c))}this._moveManipulation.angle=r,this._moveManipulation.radius=zo}e!==0&&t?(pe(a,a,1/e),vi.spatialReference=C(this._editGeometryOperations).data.spatialReference,vi.hasZ=!0,this.view.renderCoordsHelper.fromRenderCoords(a,vi),this._moveManipulation.elevationAlignedLocation=vi):d(this._outlineVisualElement)&&!this._graphicState.isDraped&&d(this._outlineVisualElement.attachmentOrigin)?this._moveManipulation.elevationAlignedLocation=this._outlineVisualElement.attachmentOrigin:Lo(this.view,this._moveManipulation,this.graphic)}_removeVertices(a){var i;const e=new Array,t=C(this._editGeometryOperations);for(const s of a)if(s.type==="vertex"&&t.canRemoveVertex()){e.push(s.handle),this._removeManipulator(s),this._removeManipulator(this._getManipulatorInfoFromHandle(s.handle.leftEdge)),this._removeManipulator(this._getManipulatorInfoFromHandle(s.handle.rightEdge));const n=t.removeVertices([s.handle]),r=C((i=n.removedVertices)==null?void 0:i[0].createdEdge);r&&this._createVertexOrEdgeManipulator(r)}if(e.length>0){const s=e.map(r=>{const o=t.data.components.indexOf(r.component);return{coordinates:t.data.coordinateHelper.vectorToArray(r.pos),componentIndex:o,vertexIndex:C(r.index)}});this.outputGeometry=t.data.geometry;const n=this._updateEventState(H.RESHAPING);if(this.destroyed||(this.emit("vertex-remove",{type:"vertex-remove",removed:s.map(r=>r.coordinates),vertices:s}),this.destroyed)||n&&(this._updateEventState(H.NONE),this.destroyed))return;this._updateMoveManipulationPosition()}}_moveVertices(a,e,t=e.action==="start"?ct.NEW_STEP:ct.ACCUMULATE_STEPS){const i=C(this._editGeometryOperations);i.moveVertices(a.map(s=>s.handle),e.mapDeltaX,e.mapDeltaY,e.mapDeltaZ,t),this.outputGeometry=i.data.geometry;for(const s of a)this._updateManipulatorPosition(s)}_offsetEdge(a,e){if(m(e.operation)||m(e.signedDistance))return;const t=C(this._editGeometryOperations),i=e.operation.clone();i.distance=e.signedDistance,t.updateVertices([a.handle.leftVertex,a.handle.rightVertex],i),this.outputGeometry=t.data.geometry,this._updateManipulatorPosition(this._getManipulatorInfoFromHandle(a.handle.leftVertex)),this._updateManipulatorPosition(this._getManipulatorInfoFromHandle(a.handle.rightVertex))}_manipulatorClickCallback(a,e){a.shiftKey||this._clearSelection(),e.type==="vertex"&&(e.manipulator.selected=!e.manipulator.selected,a.button===cn.Right&&this._removeVertices([e])),e.type==="edge"&&a.button===cn.Left&&this._splitEdgeManipulator(e),a.stopPropagation()}_updateTranslateTooltip(a,e){const t=this._manipulatorInfos.filter(i=>i.type==="vertex"&&i.manipulator.selected);t.length===1?this._updateTranslateVertexTooltip(t[0].manipulator,a,e):this._updateTranslateGraphicTooltip(a,e)}_updateTranslateGraphicTooltip(a,e){const t=this._tooltipOptions;if(!t.enabled)return;const i=this._graphicState.isDraped?"on-the-ground":"absolute-height";switch(a){case D.XY:this._tooltip.info=new Ci({tooltipOptions:t}),this._updateTranslateTooltipDistance(this._tooltip.info,e,(s,n)=>pt(s,n,i));break;case D.XY_AXIS:this._tooltip.info=new ks({tooltipOptions:t}),this._updateTranslateTooltipDistance(this._tooltip.info,e,(s,n)=>{const r=pt(s,n,i);return xa(r,Ia(e))});break;case D.Z:this._tooltip.info=new Ua({tooltipOptions:t}),this._updateTranslateTooltipDistance(this._tooltip.info,e,wa)}}_updateTranslateVertexTooltip(a,e,t){const i=this._tooltipOptions;if(!i.enabled)return;let s;const n=this._graphicState.isDraped?"on-the-ground":"absolute-height";switch(e){case D.XY:s=new Mh({tooltipOptions:i}),this._updateTranslateTooltipDistance(s,t,(o,l)=>pt(o,l,n)),this._updateTooltipAreaOrTotalLength(s);break;case D.XY_AXIS:s=new bh({tooltipOptions:i}),this._updateTranslateTooltipDistance(s,t,(o,l)=>{const c=pt(o,l,n);return xa(c,Ia(t))}),this._updateTooltipAreaOrTotalLength(s);break;case D.Z:s=new yh({tooltipOptions:i}),this._updateTranslateTooltipDistance(s,t,wa)}const r=kl(a.elevationAlignedLocation);d(r)&&(s.elevation={mode:"absolute-height",...r}),this._tooltip.info=s}_updateTranslateTooltipDistance(a,e,t){if(d(e)&&e.action!=="end"){const{mapStart:i,mapEnd:s}=e,n=t(i,s);a.distance=d(n)?n:Ti}}_updateTooltipAreaOrTotalLength(a){const{geometry:e}=this.graphic;if(m(e))return;const t=this._graphicState.isDraped?"on-the-ground":"absolute-height";a.area=e.type==="polygon"?Sh(e,t):null,a.totalLength=e.type==="polyline"?Bl(e,t):null}get test(){return{segmentLabels:this._segmentLabels,tooltip:this._tooltip}}};function vr(a,e){return Math.atan2(e[1]-a[1],e[0]-a[0])+Math.PI/2}function Op(a,e,t,i,s){if(a){const n=s.toXYZ(s.pointToVector(t)),r=fh(i,n,w.get()),o=Wl(r,n,s.spatialReference);if(d(o))return Di(o.value*Math.sign(e),o.unit)}return Di(e*hs(t.spatialReference),"meters")}h([_()],q.prototype,"_editGeometryOperations",void 0),h([_()],q.prototype,"_segmentLabels",void 0),h([_({constructOnly:!0})],q.prototype,"tool",void 0),h([_()],q.prototype,"_tooltip",void 0),h([_()],q.prototype,"inputGeometry",null),h([_()],q.prototype,"outputGeometry",void 0),h([_({readOnly:!0})],q.prototype,"updating",null),h([_()],q.prototype,"manipulators",null),h([_()],q.prototype,"view",null),h([_()],q.prototype,"graphic",null),h([_()],q.prototype,"enableZShape",null),h([_()],q.prototype,"enableZVertex",null),h([_()],q.prototype,"enableMoveGraphic",null),h([_()],q.prototype,"enableMidpoints",null),h([_()],q.prototype,"enableEdgeOffset",null),h([_()],q.prototype,"_labelOptions",null),h([_()],q.prototype,"_tooltipOptions",null),q=h([Q("esri.views.3d.interactive.editingTools.reshapeGraphic.ReshapeOperation")],q);const vi=Ht(0,0,void 0,Jo.WGS84),ra=M(),os=1e-6;var Le,H,Kt;(function(a){a.Vertex=te.Custom1,a.Edge=te.Custom2})(Le||(Le={})),function(a){a[a.NONE=0]="NONE",a[a.MOVING=1]="MOVING",a[a.RESHAPING=2]="RESHAPING"}(H||(H={})),function(a){a[a.ALL=0]="ALL",a[a.SELECTED_OR_ALL=1]="SELECTED_OR_ALL"}(Kt||(Kt={}));let le=class extends ke.EventedMixin(za){constructor(a){super(a),this._handles=new oi,this._internalGeometryUpdate=!1,this.enableZShape=!0,this.enableZVertex=!0,this.enableMoveGraphic=!0,this.enableMidpoints=!0,this.enableEdgeOffset=!1,this.type="reshape-3d",this.labelOptions=new Wr,this.tooltipOptions=new xt,this.snappingManager=null,this.automaticManipulatorSelection=!1}initialize(){const a=this._reshapeOperation=new q({tool:this});this.addHandles([a.on("reshape",e=>{e.type==="reshape"&&this._onReshapeGeometryChanged(),this.emit("reshape",e)}),a.on("move",e=>{e.type==="move"&&this._onReshapeGeometryChanged(),this.emit("move",e)}),a.on("vertex-add",e=>{this._onReshapeGeometryChanged(),this.emit("vertex-add",e)}),a.on("vertex-remove",e=>{this._onReshapeGeometryChanged(),this.emit("vertex-remove",e)}),a.on("immediate-click",e=>this.emit("immediate-click",e)),this.view.on("pointer-down",["Shift"],e=>e.stopPropagation()),G(()=>this.graphic,()=>this._updateGraphic(),Cs)]),this.finishToolCreation()}destroy(){this._handles=k(this._handles),this._reshapeOperation=k(this._reshapeOperation)}get updating(){var a;return((a=this._reshapeOperation)==null?void 0:a.updating)??!1}_updateGeometry(){const a=cc(this.graphic);this._reshapeOperation.inputGeometry=d(a)?a.clone():null}_updateGraphic(){if(this._handles.remove("onGraphicGeometryChange"),this._updateGeometry(),hc(this.graphic)!==Br.SUPPORTED)return;const a=G(()=>{var e;return(e=this.graphic)==null?void 0:e.geometry},()=>{this._internalGeometryUpdate===!1&&this._updateGeometry()},pa);this._handles.add(a,"onGraphicGeometryChange")}onManipulatorSelectionChanged(){this._reshapeOperation&&this._reshapeOperation.onManipulatorSelectionChanged()}_updateGeometryInternally(a){this._internalGeometryUpdate=!0,this.graphic.geometry=a,this._internalGeometryUpdate=!1}_onReshapeGeometryChanged(){const{outputGeometry:a}=this._reshapeOperation;!m(this.graphic)&&a&&this._updateGeometryInternally(a.clone())}get canUndo(){return this._reshapeOperation.canUndo??!1}undo(){d(this.snappingManager)&&this.snappingManager.doneSnapping();const a=this._reshapeOperation.undo(),{outputGeometry:e}=this._reshapeOperation;a&&e&&this._updateGeometryInternally(e.clone())}get canRedo(){return this._reshapeOperation.canRedo??!1}redo(){d(this.snappingManager)&&this.snappingManager.doneSnapping();const a=this._reshapeOperation.redo(),{outputGeometry:e}=this._reshapeOperation;a&&e&&this._updateGeometryInternally(e.clone())}onInputEvent(a){a.type!=="key-down"||a.key!=="Delete"&&a.key!=="Backspace"||this._reshapeOperation.removeSelectedVertices()}reset(){}get test(){return{snappingManager:this.snappingManager,reshapeOperation:this._reshapeOperation}}};h([_()],le.prototype,"_reshapeOperation",void 0),h([_({constructOnly:!0,nonNullable:!0})],le.prototype,"view",void 0),h([_({constructOnly:!0})],le.prototype,"graphic",void 0),h([_({constructOnly:!0,nonNullable:!0})],le.prototype,"enableZShape",void 0),h([_({constructOnly:!0,nonNullable:!0})],le.prototype,"enableZVertex",void 0),h([_({constructOnly:!0,nonNullable:!0})],le.prototype,"enableMoveGraphic",void 0),h([_({constructOnly:!0,nonNullable:!0})],le.prototype,"enableMidpoints",void 0),h([_({constructOnly:!0,nonNullable:!0})],le.prototype,"enableEdgeOffset",void 0),h([_()],le.prototype,"type",void 0),h([_({constructOnly:!0,type:Wr})],le.prototype,"labelOptions",void 0),h([_({constructOnly:!0,type:xt})],le.prototype,"tooltipOptions",void 0),h([_({constructOnly:!0})],le.prototype,"snappingManager",void 0),h([_()],le.prototype,"updating",null),h([_()],le.prototype,"automaticManipulatorSelection",void 0),le=h([Q("esri.views.3d.interactive.editingTools.graphicReshape3D.GraphicReshapeTool")],le);let nt=class extends Va{constructor(e){super(e),this.type="transform-rotate",this.rotationType="geographic"}};h([_()],nt.prototype,"type",void 0),h([_()],nt.prototype,"rotation",void 0),h([_()],nt.prototype,"rotationPrecision",void 0),h([_()],nt.prototype,"orientation",void 0),h([_()],nt.prototype,"orientationPrecision",void 0),h([_()],nt.prototype,"rotationType",void 0),nt=h([Q("esri.views.interactive.tooltip.TransformRotateTooltipInfo")],nt);let bt=class extends Va{constructor(e){super(e),this.type="transform-scale",this.sizeUnit=null,this.sizePrecision=null}};h([_()],bt.prototype,"type",void 0),h([_()],bt.prototype,"scale",void 0),h([_()],bt.prototype,"size",void 0),h([_()],bt.prototype,"sizeUnit",void 0),h([_()],bt.prototype,"sizePrecision",void 0),bt=h([Q("esri.views.interactive.tooltip.TransformScaleTooltipInfo")],bt);let Ee=class extends Va{constructor(a){super(a),this.type="transform-absolute",this.orientationEnabled=!0,this.orientationPrecision=null,this.rotationType="geographic",this.sizeUnit=null,this.sizeEnabled=!0,this.sizePrecision=null}};h([_()],Ee.prototype,"type",void 0),h([_()],Ee.prototype,"orientation",void 0),h([_()],Ee.prototype,"orientationEnabled",void 0),h([_()],Ee.prototype,"orientationPrecision",void 0),h([_()],Ee.prototype,"rotationType",void 0),h([_()],Ee.prototype,"size",void 0),h([_()],Ee.prototype,"sizeUnit",void 0),h([_()],Ee.prototype,"sizeEnabled",void 0),h([_()],Ee.prototype,"sizePrecision",void 0),Ee=h([Q("esri.views.interactive.tooltip.TransformAbsoluteTooltipInfo")],Ee);var L,ri;(function(a){a.ScaleIn=te.Custom2,a.ScaleOut=te.Custom3,a.RotateLeft=te.Custom4,a.RotateRight=te.Custom5,a.Unlocked=te.Custom7,a.DelayedFocused=te.Custom8,a.TouchInput=te.Custom12})(L||(L={}));class Ep{get angle(){return this._adapter.angle}get scale(){return this._adapter.scale}set location(e){this._ringManipulator.location=e}set elevationAlignedLocation(e){this._ringManipulator.elevationAlignedLocation=e}get grabbing(){return this._ringManipulator.grabbing}set interactive(e){this._ringManipulator.interactive=e}constructor({adapter:e,tooltipOptions:t,mode:i,tool:s}){this._mode=null,this._handles=new oi,this._scaleRotateDragData=null,this._activeAnimation=null,this._ringIndicatorDelayMs=np,this.events=new ke,this.getFocused=()=>this._ringManipulator.focused,this.getScale=()=>d(this._scaleRotateDragData)&&this._scaleRotateDragData.mode==="scale"?this._adapter.scale:1,this.tool=s,this._mode=i,this._adapter=e,this._tooltipOptions=t,this._tooltip=new ji({view:s.view}),this._createManipulator(),this._updateDragState(),this._updateManipulatorTransform(),this._handles.add(ai(()=>!this._tooltipOptions.enabled,()=>this._tooltip.clear(),qe))}destroy(){d(this._activeAnimation)&&(this._activeAnimation.frameTask.remove(),this._activeAnimation=null),this._handles=k(this._handles),this.tool.manipulators.remove(this._ringManipulator),this._ringManipulator=null,this._tooltip=k(this._tooltip)}startAnimation(e){this.cancelActiveAnimation(),e.start();const t=Ko({update:({deltaTime:i})=>{e.update(i)&&this.cancelActiveAnimation()}});this._activeAnimation={...e,frameTask:t}}cancelActiveAnimation(){d(this._activeAnimation)&&(this._activeAnimation.frameTask.remove(),this._activeAnimation=k(this._activeAnimation))}forEachManipulator(e){e(this._ringManipulator,I.SCALE_ROTATE)}_createManipulator(){const e=this._createRingManipulator();this._ringManipulator=e,this.tool.manipulators.add(e);const t=this.tool.graphicState.graphic,i=Ne(e,(s,n,r)=>{this._scaleRotateDragData=null;const o=this._adapter.startInteraction(),l={mode:"none",origin:ma(s.renderLocation),initialAngle:this._adapter.angle,angle:0,angleDir:0,scaleDir:0};this._scaleRotateDragData=l,this._updateDragState();const c=w.get();this.tool.view.renderCoordsHelper.worldUpAtPosition(s.renderLocation,c),n.next(Ga(this.tool.view,Ft(s.renderLocation,c,hi()))).next(p=>{const u=Zi(p.plane),g=$o(p.renderStart,p.renderEnd,l.origin,u),v=Il.shortestSignedDiff(l.angle,g);l.angleDir=va(l.angleDir+v,-cr,cr),l.angle=g;const b=Ap(l,p),f=b-this._adapter.scale;if(l.scaleDir=va(l.scaleDir+f,-lr,lr),this._onScaleChanged(),l.mode==="none"){const S=this._mode||Rp(p,p.plane,l.origin,this.tool.view.state.camera);if(d(S)){switch(S){case"rotate":this.tool.emit("graphic-rotate-start",{graphic:t,angle:0}),this.tool.emit("record-undo",{record:this._adapter.createUndoRecord()});break;case"scale":this.tool.emit("graphic-scale-start",{graphic:t,xScale:1,yScale:1}),this.tool.emit("record-undo",{record:this._adapter.createUndoRecord()})}l.mode=S}}switch(l.mode){case"rotate":o.state.angle=l.initialAngle+g;break;case"scale":o.state.scale=b,this._onScaleChanged()}switch(this._updateDragState(),this._updateManipulatorTransform(),p.action){case"start":case"update":switch(l.mode){case"rotate":this.tool.emit("graphic-rotate",{graphic:t,angle:Ri(l.angle)});break;case"scale":this.tool.emit("graphic-scale",{graphic:t,xScale:b,yScale:b})}break;case"end":switch(l.mode){case"rotate":this.tool.emit("graphic-rotate-stop",{graphic:t,angle:Ri(l.angle)});break;case"scale":this.tool.emit("graphic-scale-stop",{graphic:t,xScale:b,yScale:b})}}return p.action==="end"&&(this.startAnimation(yr(this,()=>this._onScaleChanged())),this._scaleRotateDragData=null,this._updateDragState(),o.done()),p}).next(this._updateTooltipPipelineStep(l)),r.next(()=>{if(o.cancel(),d(this._scaleRotateDragData)){switch(this._scaleRotateDragData.mode){case"none":break;case"rotate":this.tool.emit("graphic-rotate-stop",{graphic:t,angle:0});break;case"scale":this.tool.emit("graphic-scale-stop",{graphic:t,xScale:1,yScale:1})}this.startAnimation(yr(this,()=>this._onScaleChanged())),this._scaleRotateDragData=null,this._updateDragState()}this._updateFocusTooltip()})});this._handles.add([i,e.events.on("focus-changed",s=>{s.action==="focus"?this.startAnimation(Tp(this,()=>this._updateDelayedFocusedState(),{delayMs:this._ringIndicatorDelayMs})):this._updateDelayedFocusedState()}),e.events.on("immediate-click",s=>{s.stopPropagation()}),G(()=>{var s;return(s=this.tool.graphicState)==null?void 0:s.displaying},s=>this._ringManipulator.available=s,qe)])}_updateTooltipPipelineStep(e){return t=>{const i=this._tooltipOptions;if(!i.enabled)return t;if(t.action==="end")return this._updateFocusTooltip(),t;const s=this._tooltip,n=this._tooltipOptions.visualVariables,r=d(n)?n.size:null,o=d(n)?n.rotation:null;switch(e.mode){case"scale":s.info=new bt({tooltipOptions:i,scale:{value:this._adapter.scale},size:Di(se(this._adapter.size,-1),"meters"),sizeUnit:d(r)?r.unit:null,sizePrecision:d(r)?la(r.valueType):null});break;case"rotate":{const l=d(o)?la(o.valueType):null,c=d(o)?o.rotationType:"geographic";s.info=new nt({tooltipOptions:i,rotation:Ja(se(-this._adapter.relativeAngle,0),"radians","geographic"),rotationPrecision:l,orientation:Ja(-this._adapter.angle,"radians","geographic"),orientationPrecision:l,rotationType:c})}}return t}}_updateFocusTooltip(){if(this._tooltipOptions.enabled)if(this.getFocused()){const e=this._tooltipOptions.visualVariables,t=d(e)?e.rotation:null,i=d(e)?e.size:null;this._tooltip.info=new Ee({tooltipOptions:this._tooltipOptions,orientation:Ja(-this._adapter.angle,"radians","geographic"),orientationEnabled:this._mode==null||this._mode==="rotate",orientationPrecision:d(t)?la(t.valueType):null,rotationType:d(t)?t.rotationType:"geographic",size:Di(se(this._adapter.size,-1),"meters"),sizeUnit:d(i)?i.unit:null,sizeEnabled:this._mode==null||this._mode==="scale",sizePrecision:d(i)?la(i.valueType):null})}else this._tooltip.clear()}_onScaleChanged(){this.events.emit("scale-changed"),this._updateManipulatorTransform()}_updateDelayedFocusedState(){this._ringManipulator.updateStateEnabled(L.DelayedFocused,this.getFocused()),this._updateFocusTooltip()}_updateDragState(){if(this._ringManipulator.updateStateEnabled(L.Unlocked,!(d(this._scaleRotateDragData)&&this._scaleRotateDragData.mode!=="none")),d(this._scaleRotateDragData))switch(this._scaleRotateDragData.mode){case"rotate":this._ringManipulator.updateStateEnabled(L.ScaleIn|L.ScaleOut,!1),this._ringManipulator.updateStateEnabled(L.RotateLeft,this._scaleRotateDragData.angleDir<0),this._ringManipulator.updateStateEnabled(L.RotateRight,this._scaleRotateDragData.angleDir>=0);break;case"scale":this._ringManipulator.updateStateEnabled(L.RotateLeft|L.RotateRight,!1),this._ringManipulator.updateStateEnabled(L.ScaleIn,this._scaleRotateDragData.scaleDir<0),this._ringManipulator.updateStateEnabled(L.ScaleOut,this._scaleRotateDragData.scaleDir>=0)}else this._ringManipulator.updateStateEnabled(L.ScaleIn|L.ScaleOut|L.RotateLeft|L.RotateRight,!1)}_updateManipulatorTransform(){const e=zr(ne.get(),this._adapter.angle,F(0,0,1));if(m(e))return;const t=this.getScale(),i=Sa(ne.get(),B(w.get(),t,t,t));this._ringManipulator.modelTransform=Ke(ne.get(),i,e)}_createRingManipulator(){const e=($,de,We)=>{const _t=[],gt=Math.ceil(Io*(de-$)/(2*Math.PI));for(let oe=0;oe<gt+1;oe++){const di=$+oe*(de-$)/gt;_t.push(F(We*Math.cos(di),We*Math.sin(di),0))}return _t},t=$=>e(0,2*Math.PI,$),i=$=>[[-$/2,0],[$/2,0],[$/2,ns/2],[-$/2,ns/2]],s=this._createMaterial(1),n=($,de,We=s)=>eo(We,i(de),$,[],[],!1),r=t(Ct),o=n(r,rs),l={left:new Array,right:new Array},c=[];for(let $=0;$<2;$++){const de=$*Math.PI-Math.PI/4,We=Math.PI/2-ap,_t=de+We,gt=de+Math.PI/2-We,oe=e(_t,gt,ep),di=n(oe,Xt);c.push(oe),c.push(e(_t,gt,ar-rs/2)),l.left.push(di),l.right.push(di.instantiate());for(let Qa=0;Qa<2;Qa++){const Qs=Qa===0,ve=fe();if(Qs){Ai(ve,ve,[1,-1,1]),hn(ve,ve,-_t,[0,0,1]);const Ot=Math.round(nr*(oe.length-1));ve[12]=oe[Ot][0],ve[13]=oe[Ot][1],ve[14]=oe[Ot][2]}else{hn(ve,ve,gt,[0,0,1]);const Ot=Math.round((1-nr)*(oe.length-1));ve[12]=oe[Ot][0],ve[13]=oe[Ot][1],ve[14]=oe[Ot][2]}const Js=Da(s,tp,0,ip,ns);Kr(Js,ve),(Qs?l.left:l.right).push(Js)}}const p=[];for(let $=0;$<2;$++){const de=$*Math.PI-Math.PI/4,We=Math.PI/2-sp,_t=de+We,gt=de+Math.PI/2-We,oe=e(_t,gt,ar);p.push(n(oe,Xt))}const u=this._createMaterial(.66),g=this._createMaterial(.5),v=this._createMaterial(.33),b=t(Ct+rr),f=t(Ct+or),S=n(b,Xt,u),O=n(f,Xt,v),z=t(Ct-rr),W=t(Ct-or),_e=n(z,Xt,u),Y=n(W,Xt,v);let Z=[new E(o,L.DelayedFocused),new E(o.instantiate({material:g}),x.None)];this._mode&&this._mode!=="scale"||(Z=Z.concat([...p.map($=>new E($,L.DelayedFocused|L.Unlocked)),new E(S,L.DelayedFocused|L.ScaleIn),new E(O,L.DelayedFocused|L.ScaleIn),new E(_e,L.DelayedFocused|L.ScaleOut),new E(Y,L.DelayedFocused|L.ScaleOut)])),this._mode&&this._mode!=="rotate"||(Z=Z.concat([...l.right.map($=>new E($.instantiate(),L.DelayedFocused|L.Unlocked)),...l.left.map($=>new E($,L.DelayedFocused|L.RotateLeft)),...l.right.map($=>new E($,L.DelayedFocused|L.RotateRight))]));const Be=[r,...c];return new ue({view:this.tool.view,renderObjects:Z,autoScaleRenderObjects:!1,worldOriented:!0,radius:rs,focusMultiplier:1,touchMultiplier:1.5,elevationInfo:K(this.tool.graphicState.graphic),collisionType:{type:"ribbon",paths:Be,direction:F(0,0,1)}})}_createMaterial(e){const t=jt([...qd,e]);return new ze({color:t,transparent:e!==1,cullFace:ie.Back,renderOccluded:R.Transparent})}get test(){return{ringManipulator:this._ringManipulator,setRingIndicatorDelayMs:e=>this._ringIndicatorDelayMs=e,tooltip:this._tooltip}}}function Ap(a,e){const t=V(w.get(),e.renderStart,a.origin),i=V(w.get(),e.renderEnd,a.origin),s=Re(t),n=Re(i);return s===0?0:n/s}function Rp(a,e,t,i){const{renderStart:s,renderEnd:n}=a,r=oa(s,i,w.get()),o=oa(n,i,w.get());if(Ir(r,o)<sr*sr)return null;const l=V(w.get(),s,t),c=He(w.get(),l,Zi(e)),p=s,u=X(w.get(),p,c),g=oa(t,i,w.get()),v=r,b=oa(u,i,w.get()),f=V(w.get(),b,v),S=V(w.get(),r,g),O=vs(v,f),z=vs(g,S);return Sn(O,o)<Sn(z,o)?"rotate":"scale"}function oa(a,e,t){return e.projectToScreen(a,ls),B(t,ls[0],ls[1],0)}function yr(a,e){let t=null,i=1;const s=()=>i;return{start:()=>{i=a.getScale(),t=a.getScale,a.getScale=s,e()},update:n=>(i+=((i+1)/2-i)*Math.min(n*rp,1),e(),Math.abs(i-1)<.01?ri.STOP:ri.CONTINUE),destroy:()=>{t&&(a.getScale=t),e()}}}function Tp(a,e,t){let i=0,s=null;const n=()=>!1;return{start:()=>{s=a.getFocused,a.getFocused=n,i=0,e()},update:r=>(i+=r,!(s!=null&&s())||i>=t.delayMs?ri.STOP:ri.CONTINUE),destroy:()=>{s&&(a.getFocused=s),e()}}}function la(a){switch(a){case"integer":case"long":return 0;default:return null}}(function(a){a[a.CONTINUE=0]="CONTINUE",a[a.STOP=1]="STOP"})(ri||(ri={}));const ls=tt();function ko(a){return d(a.geometry)&&a.geometry.type==="mesh"?Dp(a.geometry):Pp(a)}function Dp(a){return d(a.transform)?$p(a,a.transform):Cp(a)}function Pp(a){let e=a.geometry,t=al;return{undo(i){t=i.geometry,i.geometry=e},redo(i){e=i.geometry,i.geometry=t}}}function $p(a,e){let t=e.clone(),i=null;return{undo:s=>{i=d(a.transform)?a.transform.clone():null,a.transform=t,s.notifyGeometryChanged()},redo:s=>{t=d(a.transform)?a.transform.clone():null,a.transform=i,s.notifyGeometryChanged()}}}function Cp(a){let e,t=a.vertexAttributes.clonePositional();return{undo:i=>{e=a.vertexAttributes.clonePositional(),a.vertexAttributes=t,i.notifyGeometryChanged()},redo:i=>{t=a.vertexAttributes.clonePositional(),a.vertexAttributes=e,i.notifyGeometryChanged()}}}let rt=class extends Is{constructor(e){super(e),this._interactionState=null}initialize(){this.addHandles([ai(()=>d(this._interactionState)&&this._interactionState.angle!==this._interactionState.previousAngle?{interactionState:this._interactionState,angle:this._interactionState.state.angle}:null,({interactionState:e})=>{this._updateMeshRotation(e)},pa),ai(()=>d(this._interactionState)&&this._interactionState.scale!==this._interactionState.previousScale?{interactionState:this._interactionState,scale:this._interactionState.state.scale}:null,({interactionState:e})=>{this._updateMeshSize(e)},pa)])}get angle(){const e=this.geometry.transform;if(m(e))return 0;const t=wh(e.rotation)[2];return Math.abs(t)>.999999?ut(xh(e.rotation))*Math.sign(t):0}get scale(){return d(this._interactionState)?this._interactionState.scale:1}startInteraction(){const e=new ot({angle:this.angle});this._interactionState=e;const t=()=>{this._interactionState=null};return{state:e,done:t,cancel:()=>{e.cancel(),t()}}}createUndoRecord(){return ko(this.graphic)}_updateMeshRotation(e){const t=this.geometry.anchor,i=this.viewingMode===Oi.Global,{angle:s,previousAngle:n}=e;this.geometry.rotate(0,0,Ri(s-n),{origin:t,geographic:i}),e.previousAngle=s,d(this.geometry.transform)&&this.graphic.notifyMeshTransformChanged(),this.graphic.notifyGeometryChanged()}_updateMeshSize(e){const t=this.geometry.anchor,i=this.viewingMode===Oi.Global,{scale:s,previousScale:n}=e;this.geometry.scale(s/n,{origin:t,geographic:i}),e.previousScale=s,d(this.geometry.transform)&&this.graphic.notifyMeshTransformChanged(),this.graphic.notifyGeometryChanged()}};h([_({constructOnly:!0})],rt.prototype,"graphic",void 0),h([_({constructOnly:!0})],rt.prototype,"geometry",void 0),h([_({constructOnly:!0})],rt.prototype,"viewingMode",void 0),h([_()],rt.prototype,"angle",null),h([_()],rt.prototype,"scale",null),h([_()],rt.prototype,"_interactionState",void 0),rt=h([Q("esri.views.3d.interactive.editingTools.transformGraphic.ScaleRotateMeshAdapter")],rt);let ot=class extends Fi{get state(){const{angle:e,scale:t}=this;return{angle:e,scale:t}}constructor(e){super(e),this.angle=0,this.initialAngle=0,this.previousAngle=0,this.previousScale=1,this.scale=1,this.initialAngle=e.angle,this.previousAngle=e.angle}cancel(){this.angle=this.initialAngle,this.scale=1}};h([_()],ot.prototype,"angle",void 0),h([_()],ot.prototype,"initialAngle",void 0),h([_()],ot.prototype,"previousAngle",void 0),h([_()],ot.prototype,"previousScale",void 0),h([_()],ot.prototype,"scale",void 0),h([_()],ot.prototype,"state",null),ot=h([Q("InteractionState")],ot);let ce=class extends Is{constructor(e){super(e),this.sizeAxis=null,this._interactionState=null}initialize(){this.addHandles(ai(()=>d(this._interactionState)?this._interactionState.state:null,e=>{this._updateSymbol(e)},pa))}get angle(){return d(this._interactionState)?this._interactionState.angle:d(this._orientationReferenceSymbolLayer)?Lp(this._orientationReferenceSymbolLayer.heading??0):0}get scale(){return d(this._interactionState)?this._interactionState.scale:1}get relativeAngle(){return this.angle-this.initialAngle}get initialAngle(){return d(this._interactionState)?this._interactionState.initialAngle:0}get size(){const e=this._sizeReferenceSymbolLayer;if(m(e))return null;const t=this.findLayerView(),i=this._graphicSymbol;if(m(t)||m(i)||i.type!=="point-3d")return null;const s=t.getSymbolLayerSize(i,e);if("size"in s&&d(s.size))return s.size;const n=this.sizeAxis;return"width"in s&&d(s.width)&&(m(n)||n==="width"||n==="all"||n==="width-and-depth")?s.width:"depth"in s&&d(e.depth)&&(m(n)||n==="depth"||n==="all"||n==="width-and-depth")?s.depth:"height"in s&&d(e.height)&&(m(n)||n==="height"||n==="all")?s.height:null}get _sizeReferenceSymbolLayer(){const e=this._graphicSymbol;return m(e)||e.symbolLayers.length===0?null:e.symbolLayers.find(t=>t.type==="object")}get _orientationReferenceSymbolLayer(){const e=this._graphicSymbol;return m(e)||e.symbolLayers.length===0?null:e.symbolLayers.find(t=>t.type==="object"&&d(t.heading))}get _graphicSymbol(){return d(this.graphic)&&d(this.graphic.symbol)&&this.graphic.symbol.type==="point-3d"?this.graphic.symbol:null}set _graphicSymbol(e){this.graphic.symbol=e}startInteraction(){const e=this._graphicSymbol,t=this.findLayerView();if(d(this._interactionState)||m(e)||m(t))return Gp;const i=e.symbolLayers.map(l=>l.type==="object"?t.getSymbolLayerSize(e,l):null).toArray(),s=e.clone(),n=this.angle,r=new lt({originalSymbol:s,angle:n,initialSizes:i});this._interactionState=r;const o=()=>{this._interactionState=null};return{state:r,done:o,cancel:()=>{this._graphicSymbol=s,o()}}}createUndoRecord(){let e=this.graphic.symbol,t=null;return{undo:i=>{t=i.symbol,i.symbol=e},redo:i=>{e=i.symbol,i.symbol=t}}}_updateSymbol({scale:e,angle:t,originalSymbol:i,initialSizes:s}){const n=this._graphicSymbol;if(m(n)||n.type!=="point-3d")return;const r=n.clone(),o=-Ri(t-this.initialAngle);let l=!1;this._forEachObjectSymbolLayerPair(i,r,(c,p,u)=>{const g=se(c.heading,0)+o;p.heading!==g&&(p.heading=g,l=!0);const v=s[u];if(d(v)&&"width"in v){v.width=this.sizeFilter(v.width),v.height=this.sizeFilter(v.height),v.depth=this.sizeFilter(v.depth);const b=v.width*e;p.width!==b&&(p.width=b,l=!0);const f=v.depth*e;p.depth!==f&&(p.depth=f,l=!0);const S=v.height*e;p.height!==S&&(p.height=S,l=!0)}}),l&&(this._graphicSymbol=r)}_forEachObjectSymbolLayerPair(e,t,i){e.symbolLayers.forEach((s,n)=>{const r=t.symbolLayers.getItemAt(n);s.type==="object"&&r.type==="object"&&i(s,r,n)})}};function Lp(a){return-ut(a)}h([_()],ce.prototype,"angle",null),h([_()],ce.prototype,"scale",null),h([_()],ce.prototype,"relativeAngle",null),h([_()],ce.prototype,"initialAngle",null),h([_()],ce.prototype,"size",null),h([_()],ce.prototype,"sizeAxis",void 0),h([_({constructOnly:!0})],ce.prototype,"graphic",void 0),h([_()],ce.prototype,"_interactionState",void 0),h([_({constructOnly:!0})],ce.prototype,"findLayerView",void 0),h([_({constructOnly:!0})],ce.prototype,"sizeFilter",void 0),h([_()],ce.prototype,"_sizeReferenceSymbolLayer",null),h([_()],ce.prototype,"_orientationReferenceSymbolLayer",null),h([_()],ce.prototype,"_graphicSymbol",null),ce=h([Q("esri.views.3d.interactive.editingTools.transformGraphic.ScaleRotateObjectSymbol3DAdapter")],ce);const Gp={state:{angle:0,scale:0},done:()=>{},cancel:()=>{}};let lt=class extends Fi{get state(){const{originalSymbol:e,angle:t,initialAngle:i,scale:s,initialSizes:n}=this;return{originalSymbol:e,angle:t,initialAngle:i,scale:s,initialSizes:n}}constructor(e){super(e),this.angle=0,this.initialAngle=0,this.scale=1,this.initialAngle=e.angle}};h([_()],lt.prototype,"originalSymbol",void 0),h([_()],lt.prototype,"angle",void 0),h([_()],lt.prototype,"initialAngle",void 0),h([_()],lt.prototype,"initialSizes",void 0),h([_()],lt.prototype,"scale",void 0),h([_()],lt.prototype,"state",null),lt=h([Q("InteractionState")],lt);let ye=class extends Nr(ke.EventedMixin(za)){constructor(e){super(e),this.enableZ=!0,this.enableRotation=!0,this.enableScaling=!0,this.tooltipOptions=new xt,this.type="transform-3d",this._scaleRotate=null,this._tooltip=null}initialize(){const{graphic:e,view:t}=this;this.graphicState=new we({graphic:e}),this.addHandles(G(()=>this.tooltipOptions.enabled,r=>{this._tooltip=r?new ji({view:t}):k(this._tooltip)},Cs)),this._moveManipulation=new zt({tool:this,view:t,snapToScene:this.snapToScene,xyAvailable:!0,xyAxisAvailable:!0,zAvailable:this.enableZ&&Vt(e),radius:zt.radiusForSymbol(e.symbol)}),this._moveManipulation.forEachManipulator(r=>this.handles.add(r.events.on("immediate-click",o=>{this.emit("immediate-click",{...o,graphic:e}),o.stopPropagation()})));const i=r=>o=>{this.handles.add(o.events.on("focus-changed",({action:l})=>{const c=this._tooltip;m(c)||(l==="focus"?this._updateMoveTooltip(r):c.clear())}))};this._moveManipulation.xyManipulation.forEachManipulator(i(D.XY)),this._moveManipulation.xyAxisManipulation.forEachManipulator(i(D.XY_AXIS)),this._moveManipulation.zManipulation.forEachManipulator(i(D.Z));const s=K(e);this._moveManipulation.elevationInfo=s;const n=e.geometry;if(this._moveManipulation.createGraphicDragPipeline((r,o,l,c,p)=>{if(d(n)&&r===D.XY){const{snappingStep:u,cancelSnapping:g}=Ea({snappingContext:new Oa({elevationInfo:s,pointer:p,editGeometryOperations:Fa.fromGeometry(new ei({spatialReference:n.spatialReference}),t.state.viewingMode),visualizer:new Gi,excludeFeature:e}),snappingManager:this.snappingManager,updatingHandles:this.updatingHandles,useZ:!1});c=c.next(g),l=l.next(dc(this.view,s)).next(...u)}return{steps:l=l.next(u=>(this._updateMoveTooltip(r,u),u)),cancel:c}},this.graphicState,r=>{const{action:o,graphic:l,dxScreen:c,dyScreen:p}=r,u={graphic:l,dxScreen:c,dyScreen:p};switch(o){case"start":this.emit("graphic-translate-start",u),this.emit("record-undo",{record:this._createGeometryUndoRecord()});break;case"update":this.emit("graphic-translate",u);break;case"end":this.emit("graphic-translate-stop",u)}}),this._moveManipulation.angle=d(this._scaleRotate)?this._scaleRotate.angle:0,this._scaleRotateAdapter=this._createScaleRotateAdapter(),this.handles.add(G(()=>this._scaleRotateAdapter.angle,()=>this._updateMoveAngle())),this.enableScaling||this.enableRotation){const r=this.enableScaling&&this.enableRotation?null:this.enableScaling?"scale":"rotate";this._scaleRotate=new Ep({tool:this,mode:r,adapter:this._scaleRotateAdapter,tooltipOptions:this.tooltipOptions}),this.handles.add(this._scaleRotate.events.on("scale-changed",()=>this._onScaleChanged()))}this.handles.add([Ya({view:this.view,graphic:this.graphic,forEachManipulator:r=>this._forEachManipulator(r),onManipulatorsChanged:()=>It()}),this.graphicState.on("changed",()=>this._onGeometryChanged()),this._hideManipulatorsForGraphicState(),G(()=>t.scale,()=>this._updateMoveAngle())]),this.handles.add(this.view.trackGraphicState(this.graphicState)),this._onGeometryChanged(),this._updateMoveAngle(),this._forEachManipulator(r=>{r instanceof ue&&this.handles.add(r.events.on("grab-changed",()=>this._updateManipulatorsInteractive()))}),this.finishToolCreation()}destroy(){this._tooltip=k(this._tooltip),this._moveManipulation.destroy(),this._scaleRotate=k(this._scaleRotate),this._scaleRotateAdapter=k(this._scaleRotateAdapter),this._set("view",null),this._set("graphic",null)}_updateManipulatorsInteractive(){m(this._scaleRotate)||(this._scaleRotate.interactive=!this._moveManipulation.grabbing,this._moveManipulation.interactive=!this._scaleRotate.grabbing)}_createScaleRotateAdapter(){return d(this.graphic.geometry)&&this.graphic.geometry.type==="mesh"?new rt({graphic:this.graphic,geometry:this.graphic.geometry,viewingMode:this.view.state.viewingMode}):new ce({graphic:this.graphic,sizeFilter:e=>this._enforceNonZeroSize(e),findLayerView:()=>this.view.allLayerViews.find(e=>e.layer===this.graphic.layer),sizeAxis:d(this.tooltipOptions.visualVariables)&&d(this.tooltipOptions.visualVariables.size)?this.tooltipOptions.visualVariables.size.axis:null})}_forEachManipulator(e){this._moveManipulation.forEachManipulator(e),d(this._scaleRotate)&&this._scaleRotate.forEachManipulator(e)}_hideManipulatorsForGraphicState(){return G(()=>this.graphicState.displaying,e=>{this._forEachManipulator(t=>t.available=e),this._moveManipulation.zManipulation.available=e&&this.enableZ&&Vt(this.graphic)})}_createGeometryUndoRecord(){return ko(this.graphic)}set snapToScene(e){this._moveManipulation&&(this._moveManipulation.snapToScene=e),this._set("snapToScene",e)}get updating(){return this.updatingHandles.updating}set location(e){this._moveManipulation.location=e,d(this._scaleRotate)&&(this._scaleRotate.location=e)}set elevationAlignedLocation(e){this._moveManipulation.elevationAlignedLocation=e,d(this._scaleRotate)&&(this._scaleRotate.elevationAlignedLocation=e)}reset(){}onHide(){d(this._scaleRotate)&&this._scaleRotate.cancelActiveAnimation()}_onScaleChanged(){if(m(this._scaleRotate))return;const e=this._scaleRotate.getScale();this._moveManipulation.displayScale=e}_updateMoveAngle(){this.view.state.viewingMode===Oi.Local||this.view.scale<op?this._moveManipulation.angle=this._scaleRotateAdapter.angle:this._moveManipulation.angle=0}_onGeometryChanged(){Lo(this.view,this,this.graphic)}_enforceNonZeroSize(e){return e||this.view.state.camera.computeRenderPixelSizeAt(this._moveManipulation.renderLocation)}_updateMoveTooltip(e,t){const{tooltipOptions:i,_tooltip:s}=this;if(m(s))return;s.clear();const n=this.graphicState.isDraped?"on-the-ground":"absolute-height";switch(e){case D.XY:s.info=new Ci({tooltipOptions:i}),this._updateMoveTooltipDistance(s.info,t,(r,o)=>pt(r,o,n));break;case D.XY_AXIS:s.info=new ks({tooltipOptions:i}),this._updateMoveTooltipDistance(s.info,t,(r,o)=>{const l=pt(r,o,n);return xa(l,Ia(t))});break;case D.Z:s.info=new Ua({tooltipOptions:i}),this._updateMoveTooltipDistance(s.info,t,wa)}}_updateMoveTooltipDistance(e,t,i){if(d(t)&&t.action!=="end"){const{mapStart:s,mapEnd:n}=t,r=i(s,n);e.distance=d(r)?r:Ti}}get test(){return{discManipulator:this._moveManipulation.xyManipulation.test.discManipulator,zManipulator:this._moveManipulation.zManipulation.test.manipulator,ringManipulator:d(this._scaleRotate)?this._scaleRotate.test.ringManipulator:null,arrowManipulators:this._moveManipulation.xyAxisManipulation.test.arrowManipulators,setRingIndicatorDelayMs:e=>d(this._scaleRotate)?this._scaleRotate.test.setRingIndicatorDelayMs(e):null,scaleRotateAdapter:this._scaleRotateAdapter,scaleRotateTransform:this._scaleRotate,tooltip:this._tooltip}}};h([_({constructOnly:!0,nonNullable:!0})],ye.prototype,"view",void 0),h([_({constructOnly:!0,nonNullable:!0})],ye.prototype,"graphic",void 0),h([_({constructOnly:!0,nonNullable:!0})],ye.prototype,"enableZ",void 0),h([_()],ye.prototype,"enableRotation",void 0),h([_()],ye.prototype,"enableScaling",void 0),h([_({constructOnly:!0,type:xt})],ye.prototype,"tooltipOptions",void 0),h([_()],ye.prototype,"graphicState",void 0),h([_({value:!1})],ye.prototype,"snapToScene",null),h([_({constructOnly:!0})],ye.prototype,"snappingManager",void 0),h([_({readOnly:!0})],ye.prototype,"type",void 0),h([_({readOnly:!0})],ye.prototype,"updating",null),ye=h([Q("esri.views.3d.interactive.editingTools.graphicTransform3D.GraphicTransformTool")],ye);let yt=class extends Vl(el){constructor(a){super(a),this.type="plane",this.position=null,this.heading=0,this.tilt=0,this.width=10,this.height=10}equals(a){return this.heading===a.heading&&this.tilt===a.tilt&&sl(this.position,a.position)&&this.width===a.width&&this.height===a.height}};h([_({readOnly:!0,json:{read:!1,write:!0}})],yt.prototype,"type",void 0),h([_({type:ei}),ui()],yt.prototype,"position",void 0),h([_({type:Number,nonNullable:!0,range:{min:0,max:360}}),ui(),Ks(a=>dn.normalize(en(a),0,!0))],yt.prototype,"heading",void 0),h([_({type:Number,nonNullable:!0,range:{min:0,max:360}}),ui(),Ks(a=>dn.normalize(en(a),0,!0))],yt.prototype,"tilt",void 0),h([_({type:Number,nonNullable:!0}),ui()],yt.prototype,"width",void 0),h([_({type:Number,nonNullable:!0}),ui()],yt.prototype,"height",void 0),yt=h([Q("esri.analysis.SlicePlane")],yt);Ar("mac");const Ip=2,Vp=1.15,zp=1.15,Fp=.7,Qi=F(1,.5,0);jt([...Qi,Fp]);jt([...Qi,.5]);const Np=Te(1,1,1,1),jp=Te(1,.8,.6,1),Hp=Te(1,.93,.86,1),Up=jt([...Qi,1]),kp=jt([...Qi,1]),Bp=3,Bo=11,Ts=22.5,cs=40,br=48,Wp=2.25,Yp=jt([...Qi,1]),Xp=4,Mr=1,Zp=.3,qp=6,Qp=4;function Wo(a){const e=new Ui;e.extensions.add("GL_OES_standard_derivatives");const{vertex:t,fragment:i,attributes:s,varyings:n}=e;return Hs(t,a),s.add(A.POSITION,"vec3"),s.add(A.UV0,"vec2"),n.add("vUV","vec2"),t.code.add(T`void main(void) {
vUV = uv0;
gl_Position = proj * view * vec4(position, 1.0);
}`),i.uniforms.add([new St("backgroundColor",r=>r.backgroundColor),new St("gridColor",r=>r.gridColor),new he("gridWidth",r=>r.gridWidth)]),i.code.add(T`void main() {
const float LINE_WIDTH = 1.0;
vec2 uvScaled = vUV * gridWidth;
vec2 gridUV = (fract(uvScaled + 0.5) - 0.5) / (LINE_WIDTH * fwidth(uvScaled));
vec2 grid = (1.0 - step(0.5, gridUV)) * step(-0.5, gridUV);
grid.x *= step(0.5, uvScaled.x) * step(uvScaled.x, gridWidth - 0.5);
grid.y *= step(0.5, uvScaled.y) * step(uvScaled.y, gridWidth - 0.5);
float gridFade = max(grid.x, grid.y);
float gridAlpha = gridColor.a * gridFade;
gl_FragColor =
vec4(backgroundColor.rgb * backgroundColor.a, backgroundColor.a) * (1.0 - gridAlpha) +
vec4(gridColor.rgb, 1.0) * gridAlpha;
}`),e}const Jp=Object.freeze(Object.defineProperty({__proto__:null,build:Wo},Symbol.toStringTag,{value:"Module"}));class qs extends ki{initializeProgram(e){return new Bi(e.rctx,qs.shader.get().build(this.configuration),js)}initializePipeline(){return Yi({blending:rh(Qe.ONE,Qe.ONE,Qe.ONE_MINUS_SRC_ALPHA,Qe.ONE_MINUS_SRC_ALPHA),depthTest:{func:ha.LESS},colorWrite:Xi})}}qs.shader=new Wi(Jp,()=>Ni(()=>Promise.resolve().then(()=>yu),void 0));function Kp(a,e){return Co(a.basis1,a.basis2,a.origin,e)}function eu(a,e,t,i){const s=e.worldUpAtPosition(a.origin,w.get()),n=w.get();switch(t){case Vi.HEADING:j(n,s);break;case Vi.TILT:j(n,a.basis1)}return Ft(a.origin,n,i)}function tu(a,e,t,i){const s=Re(i.basis1),n=Re(i.basis2),r=Yo(i),o=Ds(i),l=B(w.get(),0,0,0);X(l,pe(w.get(),i.basis1,e.direction[0]),pe(w.get(),i.basis2,e.direction[1])),X(l,i.origin,l);let c=0,p=1;if(qa(e))e.direction[0]===1&&e.direction[1]===-1?c=$s:e.direction[0]===1&&e.direction[1]===1?c=Math.PI:e.direction[0]===-1&&e.direction[1]===1&&(c=3*Math.PI/2),p=o;else{const g=e.direction[0]!==0?1:2;c=g===1?$s:0,p=(g===1?n:s)-r}const u=Gs(ne.get(),c);Ai(u,u,B(w.get(),p,p,p)),Ke(u,t,u),u[12]=0,u[13]=0,u[14]=0,a.modelTransform=u,a.renderLocation=l}function iu(a,e,t,i){const s=i.worldUpAtPosition(t.origin,w.get()),n=au(t,Gt.POSITIVE_X),r=Gs(ne.get(),n.edge*Math.PI/2);Fr(r,r,-cu(t,s)),Ke(r,e,r),r[12]=0,r[13]=0,r[14]=0,a.modelTransform=r,a.renderLocation=n.position}var Gt;function au(a,e){switch(e){case Gt.POSITIVE_X:return{basis:a.basis1,direction:1,position:X(w.get(),a.origin,a.basis1),edge:e};case Gt.POSITIVE_Y:return{basis:a.basis2,direction:1,position:X(w.get(),a.origin,a.basis2),edge:e};case Gt.NEGATIVE_X:return{basis:a.basis1,direction:-1,position:V(w.get(),a.origin,a.basis1),edge:e};case Gt.NEGATIVE_Y:return{basis:a.basis2,direction:-1,position:V(w.get(),a.origin,a.basis2),edge:e}}}function Yo(a){const e=Re(a.basis1),t=Re(a.basis2);return Zp*Math.min(e,t)}function Ds(a){return Yo(a)}function qa(a){return a.direction[0]!==0&&a.direction[1]!==0}function su(a,e=zi.CENTER_ON_ARROW){const t=e===zi.CENTER_ON_CALLOUT?cs:0,i=[F(t,0,-br/2),F(t,0,br/2)],s=lu(i,!0),n=(u,g)=>ru(i,u,g),r=n(0,!1),o=n(Wp,!0),l=new Ns({color:Up,renderOccluded:R.OccludeAndTransparent}),c=je(l,[[t,0,0],[t-cs,0,0]]),p=je(l,[[t,0,0],[t-cs,0,0]]);return new ue({view:a,renderObjects:[...r.normal.map(u=>new E(u,x.Unfocused|Dt)),...o.normal.map(u=>new E(u,x.Unfocused|Dt)),new E(c,x.Unfocused|Dt|Ps),...r.focused.map(u=>new E(u,x.Focused|Dt)),...o.focused.map(u=>new E(u,x.Focused|Dt)),new E(p,x.Focused|Dt|Ps)],autoScaleRenderObjects:!1,collisionType:{type:"line",paths:[s]},collisionPriority:1,radius:Bo,state:Dt})}function nu(a,e){const t=qa(e),i=t?[F(1,0,0),F(0,0,0),F(0,1,0)]:[F(1,0,0),F(-1,0,0)],s=Yp,n=v=>new it({color:s,width:v,renderOccluded:R.OccludeAndTransparent}),r=()=>new Ns({color:s,renderOccluded:R.OccludeAndTransparent}),o=t?Xp:Mr,l=o*Ip,c=Mr,p=v=>v>1?n(v):r(),u=[new E(je(p(o),i),x.Unfocused|da),new E(je(p(l),i),x.Focused|da),new E(je(p(c),i),Xo)],g=new ue({view:a,renderObjects:u,collisionType:{type:"line",paths:[i]},radius:t?qp:Qp,...Id});return g.state=da,g}function ru(a,e,t){const i=new ze({color:Np,cullFace:ie.Back,renderOccluded:R.Opaque}),s=new ze({color:jp,cullFace:ie.Back,renderOccluded:R.Opaque}),n=new ze({color:Hp,cullFace:ie.Back,renderOccluded:R.Opaque}),r=new ze({color:kp,transparent:!0,writeDepth:!1,cullFace:ie.Front,renderOccluded:R.Transparent}),o=l=>{a=a.slice(0);const c=V(w.get(),a[0],a[1]);re(c,c);const p=V(w.get(),a[a.length-1],a[a.length-2]);if(re(p,p),e>0){const Be=pe(M(),p,-e);a[a.length-1]=X(Be,Be,a[a.length-1]);const $=pe(M(),c,-e);a[0]=X($,$,a[0])}const u=l?zp:1,g=Ts*u,v=Bo*u,b=fs(ne.get());if(e>0){const Be=g/4,$=B(w.get(),0,Be,0),de=1+e/Be;_a(b,b,$),Ai(b,b,B(w.get(),de,de,de)),_a(b,b,pe($,$,-1/de))}const f=fs(fe()),S=F(0,1,0),O=pn(fe(),Pn(wn.get(),S,p));O[12]=a[a.length-1][0],O[13]=a[a.length-1][1],O[14]=a[a.length-1][2],Ke(O,O,b);const z=ou(Bp*(l?Vp:1)+e,a,t?r:n);z.transformation=f;const W=[z],_e=Ms(t?r:i,g,v,24,!1,!1,!0);_e.transformation=O,W.push(_e);const Y=Ms(t?r:s,g,v,24,!1,!0,!1);Y.transformation=O,W.push(Y);const Z=pn(fe(),Pn(wn.get(),S,c));return Z[12]=a[0][0],Z[13]=a[0][1],Z[14]=a[0][2],Ke(Z,Z,b),W.push(_e.instantiate({transformation:Z})),W.push(Y.instantiate({transformation:Z})),W};return{normal:o(!1),focused:o(!0)}}function ou(a,e,t){const i=[];for(let n=0;n<12;n++){const r=n/12*2*Math.PI;i.push([Math.cos(r)*a,Math.sin(r)*a])}return eo(t,i,e,[],[],!1)}function lu(a,e){const t=V(M(),a[a.length-1],a[a.length-2]);if(re(t,t),pe(t,t,Ts),X(t,t,a[a.length-1]),e){const i=V(M(),a[0],a[1]);return re(i,i),pe(i,i,Ts),X(i,i,a[0]),[i,...a,t]}}function cu(a,e){return fc(e,a.basis2,a.basis1)+$s}(function(a){a[a.POSITIVE_X=0]="POSITIVE_X",a[a.POSITIVE_Y=1]="POSITIVE_Y",a[a.NEGATIVE_X=2]="NEGATIVE_X",a[a.NEGATIVE_Y=3]="NEGATIVE_Y"})(Gt||(Gt={}));const Dt=te.Custom1;var Vi,Sr;(function(a){a[a.HEADING=1]="HEADING",a[a.TILT=2]="TILT"})(Vi||(Vi={})),function(a){a[a.HORIZONTAL_OR_VERTICAL=0]="HORIZONTAL_OR_VERTICAL",a[a.HORIZONTAL=1]="HORIZONTAL",a[a.VERTICAL=2]="VERTICAL",a[a.TILTED=3]="TILTED"}(Sr||(Sr={}));const Ps=te.Custom2;Hi();const $s=Math.PI/2,da=te.Custom1,Xo=te.Custom2;var zi;(function(a){a[a.CENTER_ON_CALLOUT=0]="CENTER_ON_CALLOUT",a[a.CENTER_ON_ARROW=1]="CENTER_ON_ARROW"})(zi||(zi={}));const wr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAnFBMVEUAAAD/gAD/gAD/cAD/gAD/eAD/gAD/eQD/gAD/egD/gAD/ewD/gAD/fAD/gAD/fAD/gAD/fAD/fQD/fQD/fQD/fQD/fQD/fgD/jR7/mjn/mjf/p1H/plH/smf/sWb/vHr/u3n/xYz/xIv/zZz/zJv/zJr/1Kv/1Kr/06r/06n/27n/27f/4cX/4cT/59D/7dr/8uX/9u7/+/f////u2EN0AAAAM3RSTlMACBAQICAoKDAwODhAQEhIUFBYYGhweICIkJCXmJ+gp6ivsLe4uL+/wMDHx8/P19/n7/cWvjXwAAACeUlEQVR42tWX3XqiMBCGY2pbtbrUnzhhdak/lHWliJD7v7fdJ+KG5AMhh30P8zCTmS+TycDaeBoHi5Wgf4jVYvbKmRfPgSAHMX9mPRnM1tSIGHM/c2QddLp4c8wxCvYIvqROBPfbHlm/sRYC6smMNTKn3sxZAyvyYNW1v38MM/IkcPQnZHPMLtciz9P9hhqwzoLD+cnfpTIUaYinyZlBkE2YKZcMXCyN/YhsPkuFlMfWJLiwo89VMxfpJDForMCwuG+Zx7ttGO2S/w4LJ42ZURDty5M0a4dqsZAQAihQfXqWdlhnpcmdEPAI0tv2EbnsbsKmdgi6/1GN7T1XJLx5sF0P9SWABMC+co5JBE4Ge/1NTM3EGIJgjFONXCdAbeQYwhN7pRrRV20LJNIhWOczdu+xPFzIBiQ62iIsyIOTvlZUY+HXySLQaMUEeSC1CPYxENIlwk+q8e0clFAIfiKG+qpaIvod4wfU8sqvkDLda+xCCqgDaAk7uyeNqD+feFlfGCcg3Hzsk+xS7Nz1Aq4CcauhhMc0uxaqIgcFsF0J+1WQyoCN7Y9ezeCVH5LhSxmyRvsihKbK1m7LafpSpkpj6yJgtsiVBh6AX5UyCVmMbrNpcwj5/h6DPN79JjAiQAhXVeN6SZI0q5bQnn4wBiHEqpUybp1ZJzWxStVCHhKhAhVLp/Emh6trHpGLaB6yZHk7wu3Z+ChOxhwUNEmYYjpUvqJDksSHraQmJm2DdqQK6sGUObybYtpSN+8Phm3pN2xjDH33R6b0CKxAZNLvl8foD3BBnSw5e8RI+G2P8GD9wHw6YN3wkfA0R4Zz8CGCIfOCv8zM738walXuLw6nXBvPr8wvAAAAAElFTkSuQmCC",hu={mipmap:!0,preMultiplyAlpha:!0,width:64,height:64};function du(a){return a.fromData(wr,()=>new to(wr,hu))}class pu{constructor(){this._lastDragEvent=null,this._next=null,this._enabled=!1}get enabled(){return this._enabled}set enabled(e){if(this._enabled!==e&&d(this._lastDragEvent)&&d(this._next)){const t={...this._lastDragEvent,action:"update"};e&&this._adjustScaleFactors(t),this._next.execute(t)}this._enabled=e}createDragEventPipelineStep(){this._lastDragEvent=null;const e=new Vs;return this._next=e,[t=>(this._lastDragEvent=t.action!=="end"?{...t}:null,this._enabled&&this._adjustScaleFactors(t),t),e]}_adjustScaleFactors(e){const t=qa(e.handle)?Math.max(Math.abs(e.factor1),Math.abs(e.factor2)):e.handle.direction[0]===0?Math.abs(e.factor2):Math.abs(e.factor1);e.factor1=e.factor1<0?-t:t,e.factor2=e.factor2<0?-t:t}get test(){return{_adjustScaleFactors:e=>this._adjustScaleFactors(e)}}}let be=class extends ke.EventedMixin(za){constructor(a){super(a),this.enableZ=!0,this.enableRotation=!0,this.enableScaling=!0,this.tooltipOptions=new xt,this._preserveAspectRatio=new pu,this.grabbing=!1,this.inputState=null,this.type="transform-3d",this._handles=new oi,this._attachmentOrigin=null,this._outlineVisualElement=null,this._mapBounds=ea(),this._mapBoundsStart=ea(),this._zmax=0,this._sizeStart=null,this._displayBounds=ea(),this._displayBoundsStart=ea(),this._displayBoundsMarginStart=0,this._resizeHandles=[{direction:[1,0]},{direction:[1,1]},{direction:[0,1]},{direction:[-1,1]},{direction:[-1,0]},{direction:[-1,-1]},{direction:[0,-1]},{direction:[1,-1]}],this._moveXYTooltipInfo=null,this._moveZTooltipInfo=null,this._rotateTooltipInfo=null,this._scaleTooltipInfo=null,this._startAngle=0,this._endAngle=0,this._startScale=Je(),this._endScale=Je()}initialize(){const{view:a,graphic:e,manipulators:t,_handles:i}=this,s=this._graphicState=new we({graphic:e}),n=e.geometry;this._editGeometryOperations=Fa.fromGeometry(n,a.state.viewingMode),this._graphicMoveManipulation=new Zs({tool:this,view:a,graphicState:s}),this._moveZManipulator=su(a,zi.CENTER_ON_CALLOUT),this._moveZManipulator.state|=Ps,i.add([this._createMoveXYGraphicDragPipeline(),G(()=>this.enableZ,()=>this._updateManipulatorAvailability(this._moveZManipulator,I.TRANSLATE_Z)),this._createMoveZDragPipeline()]),t.add(this._moveZManipulator),this._resizeManipulators=this._resizeHandles.map(p=>{const u=nu(a,p);return i.add([G(()=>this.enableScaling,()=>this._updateManipulatorAvailability(u,I.SCALE)),u.events.on("grab-changed",g=>this._onResizeGrab(g)),this._createResizeDragPipeline(u,p)]),u}),t.addMany(this._resizeManipulators),this._rotateManipulatorTexture=du(a.toolViewManager.textures),this._rotateManipulator=Ld(a,{texture:this._rotateManipulatorTexture.texture}),i.add([G(()=>this.enableRotation,()=>this._updateManipulatorAvailability(this._rotateManipulator,I.ROTATE)),this._rotateManipulator.events.on("grab-changed",p=>{this._onRotateGrab(p)}),this._createRotateDragPipeline(this._rotateManipulator)]),t.add(this._rotateManipulator),this._calculateMapBounds(),this._updateDisplayBounds();const r=Ya({view:a,graphic:e,forEachManipulator:p=>this._forEachManipulator(p),onManipulatorsChanged:()=>It()});d(r)&&(this._outlineVisualElement=r.visualElement instanceof Nt?r.visualElement:null),d(this._outlineVisualElement)&&i.add(this._outlineVisualElement.events.on("attachment-origin-changed",()=>this._updateDisplayBounds())),i.add(r),i.add([s.on("changed",()=>this._onGeometryChanged()),G(()=>s.displaying,()=>this._updateAllManipulatorAvailability()),G(()=>s.isDraped,()=>this._graphicDrapedChanged(),qe),a.trackGraphicState(s)]);const o=a.pointsOfInterest;i.add(G(()=>o==null?void 0:o.centerOnSurfaceFrequent.location,()=>this._updateDisplayBounds()));const l=p=>{i.add(p.events.on("grab-changed",()=>{this.grabbing=p.grabbing,this._updateAllManipulatorAvailability()}))};this._forEachManipulator(l);const c=(p,u)=>{i.add(p.events.on("immediate-click",g=>{u===I.TRANSLATE_XY&&this.emit("immediate-click",{...g,graphic:e}),g.stopPropagation()}))};this._forEachManipulator(c),this._onGeometryChanged(),this._updateAllManipulatorAvailability(),this._initializeTooltip(),this.finishToolCreation()}destroy(){this._mapBounds=null,this._displayBounds=null,this._rotateManipulatorTexture.release(),this._handles.destroy(),this._graphicMoveManipulation.destroy(),this._editGeometryOperations.destroy(),this._tooltip.destroy(),this._set("view",null),this._set("graphic",null)}_initializeTooltip(){const{_handles:a,view:e}=this,t=this._tooltip=new ji({view:e}),i=()=>{t.info=this._getUpdatedTooltipInfo()};a.add([this.on("graphic-translate-start",i),this.on("graphic-translate",i),this.on("graphic-translate-stop",()=>{this._moveXYTooltipInfo=null,this._moveZTooltipInfo=null,this._tooltip.clear()}),this.on("graphic-rotate-start",s=>{this._startAngle=s.angle,i()}),this.on("graphic-rotate",s=>{this._endAngle=s.angle,i()}),this.on("graphic-rotate-stop",()=>{this._startAngle=0,this._endAngle=0,i()}),this.on("graphic-scale-start",s=>{Ie(this._startScale,s.xScale,s.yScale),Ie(this._endScale,s.xScale,s.yScale),i()}),this.on("graphic-scale",s=>{Ie(this._endScale,s.xScale,s.yScale),i()}),this.on("graphic-scale-stop",()=>{Ie(this._startScale,0,0),Ie(this._endScale,0,0),i()})]),this._forEachManipulator(s=>{a.add([s.events.on("focus-changed",i),s.events.on("grab-changed",i),s.events.on("drag",n=>{n.action==="cancel"?this._tooltip.clear():i()})])})}_getUpdatedTooltipInfo(){return this.tooltipOptions.enabled?this._graphicMoveManipulation.grabbing||this._graphicMoveManipulation.dragging?this._computeMoveXYTooltipInfo():this._moveZManipulator.focused?this._computeMoveZTooltipInfo():this._rotateManipulator.focused?this._computeRotateTooltipInfo():this._resizeManipulators.some(a=>a.focused)?this._computeScaleTooltipInfo():null:null}_computeMoveXYTooltipInfo(){return this._moveXYTooltipInfo=se(this._moveXYTooltipInfo,()=>new Ci({tooltipOptions:this.tooltipOptions}))}_computeMoveZTooltipInfo(){const a=this._moveZTooltipInfo=se(this._moveZTooltipInfo,()=>new Ua({tooltipOptions:this.tooltipOptions})),e=this._moveUnit;if(this._moveZManipulator.dragging){const t=this._mapBoundsStart.origin,i=this._mapBounds.origin,s=Yl(t,i,this.view.spatialReference);if(m(s))return null;a.distance=s}else a.distance=Di(0,e);return a}_computeRotateTooltipInfo(){const a=this._rotateTooltipInfo=se(this._rotateTooltipInfo,()=>new Oh({tooltipOptions:this.tooltipOptions}));return a.angle=this._startAngle-this._endAngle,a}_computeScaleTooltipInfo(){const a=this.graphic.geometry;if(m(a))return null;const e=this._scaleTooltipInfo=se(this._scaleTooltipInfo,()=>new Eh({tooltipOptions:this.tooltipOptions})),t=Tn(this._mapBounds,this._zmax,a.spatialReference,this._graphicState.isDraped);return m(t)?null:(e.xSize=t[0],e.ySize=t[1],d(this._sizeStart)&&this._resizeManipulators.some(i=>i.dragging)?(e.xScale=t[0].value/this._sizeStart[0].value,e.yScale=t[1].value/this._sizeStart[1].value):(e.xScale=1,e.yScale=1),e)}_graphicDrapedChanged(){this._handles.remove(xr),this._updateDisplayBounds(),this._graphicState.isDraped&&this._handles.add(this.view.elevationProvider.on("elevation-change",a=>{d(this._attachmentOrigin)&&Vr(a.extent,this._attachmentOrigin.x,this._attachmentOrigin.y)&&this._updateDisplayBounds()}),xr)}_updateAllManipulatorAvailability(){this._forEachManipulator((a,e)=>this._updateManipulatorAvailability(a,e))}_updateManipulatorAvailability(a,e){const t=this.grabbing&&!a.grabbing;if(a.interactive=!t,a instanceof ue){const i=this._graphicState.displaying,s=this.enableZ&&Vt(this.graphic);switch(e){case I.ROTATE:a.available=i&&this.enableRotation;break;case I.SCALE:a.available=i&&(this.enableScaling||this.enableRotation||s),a.interactive=!t&&this.enableScaling,a.state=this.enableScaling?da:Xo;break;case I.TRANSLATE_Z:a.available=i&&s;break;default:a.available=i}}}_forEachManipulator(a){this._graphicMoveManipulation.forEachManipulator(a),this._resizeManipulators.forEach(e=>a(e,I.SCALE)),a(this._rotateManipulator,I.ROTATE),a(this._moveZManipulator,I.TRANSLATE_Z)}get preserveAspectRatio(){return this._preserveAspectRatio.enabled}set preserveAspectRatio(a){this._preserveAspectRatio.enabled=a,this._set("preserveAspectRatio",a)}get _moveUnit(){return se(tl(this.view.spatialReference),"meters")}reset(){}_onGeometryChanged(){this._updateDisplayBounds()}_calculateMapBounds(){const a=this.graphic.geometry,e=this._editGeometryOperations.data,t=e.components[0].edges[0],i=Pr(ys.get(),t.leftVertex.pos,t.rightVertex.pos);ua(i,i);const s=se(La(this.view,this.graphic),()=>{var l;return(l=a.extent)==null?void 0:l.center});let n=s?_u*this.view.pixelSizeAt(s):0;const r=this.view.spatialReference,o=a.spatialReference;r.equals(o)||(n*=hs(r)/hs(o)),_h(i,e,n,this._mapBounds),this._updateZMax()}_updateZMax(){const a=this._editGeometryOperations.data;if(!a.geometry.hasZ)return void(this._zmax=0);const e=a.coordinateHelper;let t=Number.NEGATIVE_INFINITY;for(const i of a.components)for(const s of i.vertices){const n=e.getZ(s.pos)??0;t=Math.max(n,t)}this._zmax=t}_updateDisplayBounds(){const{geometry:a}=this.graphic;if(m(a))return;const{extent:e}=a;if(!e)return;const t=d(this._outlineVisualElement)&&!this._graphicState.isDraped&&d(this._outlineVisualElement.attachmentOrigin)?this._outlineVisualElement.attachmentOrigin:La(this.view,this.graphic);this._attachmentOrigin=se(t,e.center);const i=d(t)?t.z:Ha(this._mapBounds.origin,this.view.elevationProvider,ci.fromElevationInfo(K(this.graphic)),this.view.renderCoordsHelper),s=pi(this._mapBounds);s.origin[2]=i??0,gh(s,this.view.renderCoordsHelper,a.spatialReference,this._displayBoundsMargin,this._displayBounds),this._updateManipulators()}get _displayBoundsMargin(){var t;const a=this.view.pointsOfInterest,e=a?a.centerOnSurfaceFrequent.location:(t=this._editGeometryOperations.data.geometry.extent)==null?void 0:t.center;return e?uu*this.view.pixelSizeAt(e):0}_createMoveXYGraphicDragPipeline(){return this._graphicMoveManipulation.createDragPipeline((a,e,t)=>this._applyGraphicMoveSteps(e,t,D.XY))}_createMoveZDragPipeline(){const a=this.view,e=this._editGeometryOperations.data.spatialReference;return Ne(this._moveZManipulator,(t,i,s)=>{const n=ma(t.renderLocation),r=i.next(jo(a,n,e)).next(li());this._applyGraphicMoveSteps(r,s,D.Z)})}_applyGraphicMoveSteps(a,e,t){const i=a.next(s=>(s.action==="start"&&(this.inputState={type:"move"},this._updateOperationStartProperties(),this.emit("graphic-translate-start",{graphic:this.graphic,dxScreen:s.screenDeltaX,dyScreen:s.screenDeltaY})),s)).next(Jt()).next(this._moveDragUpdateGeometry()).next(s=>{const n={graphic:this.graphic,dxScreen:s.screenDeltaX,dyScreen:s.screenDeltaY};switch(s.action){case"start":case"update":(s.mapEnd.x-s.mapStart.x||s.mapEnd.y-s.mapStart.y||(s.mapEnd.z??0)-(s.mapStart.z??0))&&this.emit("graphic-translate",n);break;case"end":this.inputState=null,this.emit("graphic-translate-stop",n)}return s}).next(s=>this._updateMoveTooltip(s,t));return e.next(()=>{d(this.inputState)&&this.emit("graphic-translate-stop",{graphic:this.graphic,dxScreen:0,dyScreen:0}),this._cancel()}),i}_updateOperationStartProperties(){pi(this._displayBounds,this._displayBoundsStart),pi(this._mapBounds,this._mapBoundsStart),m(this.graphic.geometry)?this._sizeStart=null:this._sizeStart=Tn(this._mapBoundsStart,this._zmax,this.graphic.geometry.spatialReference,this._graphicState.isDraped)}_moveDragUpdateGeometry(){return a=>{if(m(this.inputState)||this.inputState.type!=="move")return a;const e=[];for(const s of this._editGeometryOperations.data.components)e.push(...s.vertices);const t=a.action==="start"?ct.NEW_STEP:ct.ACCUMULATE_STEPS,i=this._editGeometryOperations.moveVertices(e,a.mapDeltaX,a.mapDeltaY,a.mapDeltaZ,t);return Ki(i,this._mapBounds),this.graphic.geometry=this._editGeometryOperations.data.geometry,a}}_updateMoveTooltip(a,e){if(e===D.XY||e===D.XY_AXIS){const t=pt(a.mapStart,a.mapEnd,this._graphicState.isDraped?"on-the-ground":"absolute-height");d(t)&&d(this._moveXYTooltipInfo)&&(this._moveXYTooltipInfo.distance=t)}return a}_onResizeGrab({action:a,screenPoint:e}){if(a!=="start"||!e)return;const t=this._calculatePickRay(e);ni(this._displayBounds.plane,t,w.get())&&(this._updateOperationStartProperties(),this._displayBoundsMarginStart=this._displayBoundsMargin,this.inputState={type:"resize"})}_createResizeDragPipeline(a,e){return Ne(a,(t,i,s)=>{m(this.inputState)||(i.next(n=>(n.action==="start"&&this.emit("graphic-scale-start",{graphic:this.graphic,xScale:1,yScale:1}),n)).next(Ga(this.view,this._displayBoundsStart.plane)).next(n=>({...n,handle:e})).next(this._resizeDragRenderPlaneToFactors()).next(...this._preserveAspectRatio.createDragEventPipelineStep()).next(this._resizeDragUpdateGeometry()).next(n=>{const r={graphic:this.graphic,xScale:n.factor1,yScale:n.factor2};switch(n.action){case"start":case"update":this.emit("graphic-scale",r);break;case"end":this.inputState=null,this.emit("graphic-scale-stop",r)}return n}),s.next(()=>{d(this.inputState)&&this.emit("graphic-scale-stop",{graphic:this.graphic,xScale:1,yScale:1}),this._cancel()}))})}_resizeDragRenderPlaneToFactors(){return a=>{const e=this._displayBoundsStart,t=a.handle.direction,i=this._displayBoundsMargin,s=this._displayBoundsMarginStart,n=j(w.get(),e.origin);Ji(n,n,e.basis1,-t[0]),Ji(n,n,e.basis2,-t[1]);const r=V(w.get(),a.renderEnd,n),o=V(w.get(),a.renderStart,n),l=qa(a.handle),c=Ds(e),p=Ds(this._displayBounds)/c,u=(g,v)=>{if(g===0)return 1;let b=Re(v),f=.5*g*Ma(v,r)/b;const S=f<0?-1:1;l&&(f+=(b-.5*g*Ma(v,o)/b)*S*p);const O=b<1.5*s?1:Or;return b=Math.max(b-s,Or),S>0&&(f-=i),S*Math.max(S*(f/b),O)};return{...a,factor1:u(t[0],e.basis1),factor2:u(t[1],e.basis2)}}}_resizeDragUpdateGeometry(){return a=>{const e=j(M(),this._mapBoundsStart.origin);Ji(e,e,this._mapBoundsStart.basis1,-a.handle.direction[0]),Ji(e,e,this._mapBoundsStart.basis2,-a.handle.direction[1]);const t=Ie(Je(),this._mapBoundsStart.basis1[0],this._mapBoundsStart.basis1[1]);ua(t,t);const i=[];for(const r of this._editGeometryOperations.data.components)i.push(...r.vertices);const s=a.action==="start"?ct.NEW_STEP:ct.ACCUMULATE_STEPS,n=this._editGeometryOperations.scaleVertices(i,e,t,a.factor1,a.factor2,s,Mn.REPLACE);return pi(this._mapBoundsStart,this._mapBounds),Ki(n,this._mapBounds),this.graphic.geometry=this._editGeometryOperations.data.geometry,a}}_onRotateGrab({action:a,screenPoint:e}){if(a!=="start"||!e)return;const t=eu(this._displayBounds,this.view.renderCoordsHelper,Vi.HEADING,hi()),i=this._calculatePickRay(e);ni(t,i,w.get())&&(this._updateOperationStartProperties(),this.inputState={type:"rotate",rotatePlane:t})}_createRotateDragPipeline(a){return Ne(a,(e,t,i)=>{const s=this.inputState;m(s)||(t.next(n=>(n.action==="start"&&this.emit("graphic-rotate-start",{graphic:this.graphic,angle:0}),n)).next(Ga(this.view,s.rotatePlane)).next(this._rotateDragRenderPlaneToRotate(s)).next(this._rotateDragUpdateGeometry()).next(n=>{const r={graphic:this.graphic,angle:Ri(n.rotateAngle)};switch(n.action){case"start":case"update":this.emit("graphic-rotate",r);break;case"end":this.inputState=null,this.emit("graphic-rotate-stop",r)}return n}),i.next(()=>{d(this.inputState)&&this.emit("graphic-rotate-stop",{graphic:this.graphic,angle:0}),this._cancel()}))})}_rotateDragRenderPlaneToRotate(a){return e=>{const t=Zi(a.rotatePlane),i=$o(e.renderStart,e.renderEnd,this._displayBounds.origin,t);return{...e,rotateAxis:t,rotateAngle:i}}}_rotateDragUpdateGeometry(){return a=>{const e=j(M(),this._mapBoundsStart.origin),t=[];for(const n of this._editGeometryOperations.data.components)t.push(...n.vertices);const i=a.action==="start"?ct.NEW_STEP:ct.ACCUMULATE_STEPS,s=this._editGeometryOperations.rotateVertices(t,e,a.rotateAngle,i,Mn.REPLACE);return pi(this._mapBoundsStart,this._mapBounds),Ki(s,this._mapBounds),this.graphic.geometry=this._editGeometryOperations.data.geometry,a}}_calculatePickRay(a){const e=Hi(),t=ba(a);return ii(this.view.state.camera,t,e),re(e.direction,e.direction),e}_updateManipulators(){if(!this.visible)return;const a=Kp(this._displayBounds,ne.get());iu(this._rotateManipulator,a,this._displayBounds,this.view.renderCoordsHelper),this._updateZMoveHandle(this._moveZManipulator,a),this._resizeManipulators.forEach((e,t)=>{tu(e,this._resizeHandles[t],a,this._displayBounds)})}_updateZMoveHandle(a,e){const t=this._displayBounds,i={basis:t.basis1,direction:-1,position:V(w.get(),t.origin,t.basis1),edge:2},s=ne.get();zl(s,e,i.edge*Math.PI/2),s[12]=0,s[13]=0,s[14]=0,a.modelTransform=s,a.renderLocation=i.position}_cancel(){const a=this._editGeometryOperations.lastOperation;m(a)||(this._editGeometryOperations.undo(),this.graphic.geometry=this._editGeometryOperations.data.geometry,Dn(a,this._mapBounds),this._updateDisplayBounds(),this.inputState=null)}get canUndo(){return this._editGeometryOperations.canUndo}undo(){if(d(this.inputState))this.view.activeTool=null;else if(this.canUndo){const a=this._editGeometryOperations.undo();this.graphic.geometry=this._editGeometryOperations.data.geometry,Dn(C(a),this._mapBounds),this._updateDisplayBounds()}}get canRedo(){return this._editGeometryOperations.canRedo}redo(){if(this.canRedo){const a=this._editGeometryOperations.redo();this.graphic.geometry=this._editGeometryOperations.data.geometry,Ki(C(a),this._mapBounds),this._updateDisplayBounds()}}get test(){return{resizeManipulators:this._resizeManipulators,rotateManipulator:this._rotateManipulator,moveZManipulator:this._moveZManipulator,tooltip:this._tooltip}}};h([_({constructOnly:!0,nonNullable:!0})],be.prototype,"view",void 0),h([_({constructOnly:!0,nonNullable:!0})],be.prototype,"graphic",void 0),h([_({constructOnly:!0,nonNullable:!0})],be.prototype,"enableZ",void 0),h([_()],be.prototype,"enableRotation",void 0),h([_()],be.prototype,"enableScaling",void 0),h([_({constructOnly:!0,type:xt})],be.prototype,"tooltipOptions",void 0),h([_()],be.prototype,"preserveAspectRatio",null),h([_()],be.prototype,"grabbing",void 0),h([_()],be.prototype,"inputState",void 0),h([_({readOnly:!0})],be.prototype,"type",void 0),h([_()],be.prototype,"_moveUnit",null),be=h([Q("esri.views.3d.interactive.editingTools.graphicTransform3D.ExtentTransformTool")],be);const xr="draped-elevation-changes",uu=10,_u=80,Or=1e-6,gu=Object.freeze(Object.defineProperty({__proto__:null,build:yo},Symbol.toStringTag,{value:"Module"})),mu=Object.freeze(Object.defineProperty({__proto__:null,build:wo,defaultAngleCutoff:Ys},Symbol.toStringTag,{value:"Module"})),fu=Object.freeze(Object.defineProperty({__proto__:null,build:Ao},Symbol.toStringTag,{value:"Module"})),vu=Object.freeze(Object.defineProperty({__proto__:null,build:Po},Symbol.toStringTag,{value:"Module"})),yu=Object.freeze(Object.defineProperty({__proto__:null,build:Wo},Symbol.toStringTag,{value:"Module"}));export{Wt as DrawGraphicTool3D,be as ExtentTransformTool,st as GraphicMoveTool,le as GraphicReshapeTool,ye as GraphicTransformTool};
