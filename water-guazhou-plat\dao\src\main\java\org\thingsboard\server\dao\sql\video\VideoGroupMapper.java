package org.thingsboard.server.dao.sql.video;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.VideoGroupEntity;

import java.util.List;

@Mapper
public interface VideoGroupMapper extends BaseMapper<VideoGroupEntity> {

    @Select("select a.*, count(b.id) as videoNum " +
            "from video_group a " +
            "left join video b on a.id = b.group_id " +
            "where a.parent_id like '%'||#{projectId}||'%' " +
            "group by a.id, a.name, a.parent_id, a.create_time, a.tenant_id order by a.order_num, a.create_time desc " )
    List<VideoGroupEntity> getList(@Param("projectId") String projectId);
}
