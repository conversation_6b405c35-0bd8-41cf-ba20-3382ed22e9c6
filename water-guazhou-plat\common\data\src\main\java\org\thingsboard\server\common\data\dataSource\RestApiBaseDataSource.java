package org.thingsboard.server.common.data.dataSource;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2020/2/26 16:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RestApiBaseDataSource extends BaseDataSource {

    private String URL;
    private String method;
    private String params;
    private String parsingAttribute;
    private String parsingPath;


    @Override
    public void setType(DataSourceType type) {
        super.setType(DataSourceType.RESTAPI_SOURCE);
    }

    public RestApiBaseDataSource(BaseDataSource dataSource,String URL, String method, String params, String parsingAttribute, String parsingPath ){
        this.setId(dataSource.getId());
        this.setType(DataSourceType.RESTAPI_SOURCE);
        this.setName(dataSource.getName());
        this.setEnable(dataSource.getEnable());
        this.setFormat(dataSource.getFormat());
        this.setUpdateTime(dataSource.getUpdateTime());
        this.setOrder(dataSource.getOrder());
        this.setURL(URL);
        this.setMethod(method);
        this.setParams(params);
        this.setParsingAttribute(parsingAttribute);
        this.setParsingPath(parsingPath);
    }

}
