import{fN as y,fO as O,fP as k,fQ as E,fR as j}from"./index-r0dFAfgr.js";function x(r,e,a){var f=r.length;return a=a===void 0?f:a,!e&&a>=f?r:y(r,e,a)}var z="\\ud800-\\udfff",J="\\u0300-\\u036f",P="\\ufe20-\\ufe2f",T="\\u20d0-\\u20ff",V=J+P+T,F="\\ufe0e\\ufe0f",H="\\u200d",N=RegExp("["+H+z+V+F+"]");function d(r){return N.test(r)}function U(r){return r.split("")}var b="\\ud800-\\udfff",I="\\u0300-\\u036f",W="\\ufe20-\\ufe2f",Z="\\u20d0-\\u20ff",q=I+W+Z,w="\\ufe0e\\ufe0f",L="["+b+"]",u="["+q+"]",o="\\ud83c[\\udffb-\\udfff]",_="(?:"+u+"|"+o+")",l="[^"+b+"]",$="(?:\\ud83c[\\udde6-\\uddff]){2}",R="[\\ud800-\\udbff][\\udc00-\\udfff]",G="\\u200d",c=_+"?",v="["+w+"]?",Q="(?:"+G+"(?:"+[l,$,R].join("|")+")"+v+c+")*",X=v+c+Q,B="(?:"+[l+u+"?",u,$,R,L].join("|")+")",D=RegExp(o+"(?="+o+")|"+B+X,"g");function K(r){return r.match(D)||[]}function Y(r){return d(r)?K(r):U(r)}var h=9007199254740991,rr=Math.floor;function t(r,e){var a="";if(!r||e<1||e>h)return a;do e%2&&(a+=r),e=rr(e/2),e&&(r+=r);while(e);return a}var er=O("length"),m="\\ud800-\\udfff",ar="\\u0300-\\u036f",fr="\\ufe20-\\ufe2f",ur="\\u20d0-\\u20ff",or=ar+fr+ur,sr="\\ufe0e\\ufe0f",nr="["+m+"]",s="["+or+"]",n="\\ud83c[\\udffb-\\udfff]",dr="(?:"+s+"|"+n+")",g="[^"+m+"]",S="(?:\\ud83c[\\udde6-\\uddff]){2}",p="[\\ud800-\\udbff][\\udc00-\\udfff]",tr="\\u200d",C=dr+"?",M="["+sr+"]?",ir="(?:"+tr+"(?:"+[g,S,p].join("|")+")"+M+C+")*",br=M+C+ir,lr="(?:"+[g+s+"?",s,S,p,nr].join("|")+")",i=RegExp(n+"(?="+n+")|"+lr+br,"g");function $r(r){for(var e=i.lastIndex=0;i.test(r);)++e;return e}function A(r){return d(r)?$r(r):er(r)}var Rr=Math.ceil;function cr(r,e){e=e===void 0?" ":k(e);var a=e.length;if(a<2)return a?t(e,r):e;var f=t(e,Rr(r/A(e)));return d(e)?x(Y(f),0,r).join(""):f.slice(0,r)}function mr(r,e,a){r=E(r),e=j(e);var f=e?A(r):0;return e&&f<e?cr(e-f,a)+r:r}export{A as a,cr as b,x as c,t as d,d as h,mr as p,Y as s};
