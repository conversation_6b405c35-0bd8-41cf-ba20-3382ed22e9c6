package org.thingsboard.server.utils.imodel.validate;

import org.springframework.stereotype.Component;
import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.query.annotations.DefaultValue;
import org.thingsboard.server.dao.util.reflection.BeanWrapper;
import org.thingsboard.server.dao.util.reflection.FieldWrapper;
import org.thingsboard.server.utils.imodel.aop.ValidatePerformer;

@Component
public class DefaultValueValidator implements ValidatePerformer {
    @Override
    public boolean match(FieldWrapper field) {
        return field.isAnnotationPresent(DefaultValue.class);
    }

    @Override
    public String resolveString(BeanWrapper bean, FieldWrapper field, String value, boolean hasParent) {
        String defaultValue = field.getAnnotation(DefaultValue.class).value();
        return field.conditionalSetValue(defaultValue, !StringUtils.isNullOrBlank(value));
    }

    @Override
    public String resolveDestType(Bean<PERSON>rapper bean, FieldWrapper field, Class<?> type, Object value, boolean hasParent) {
        return DefaultValue.class.getName() + "不支持的类型，于字段" + field.getName();
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
