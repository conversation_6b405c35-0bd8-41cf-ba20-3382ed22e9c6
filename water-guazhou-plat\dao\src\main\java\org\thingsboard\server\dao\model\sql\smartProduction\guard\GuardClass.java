package org.thingsboard.server.dao.model.sql.smartProduction.guard;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("guard_class")
public class GuardClass {
    // 班次id
    private String id;

    // 班次名称
    private String name;

    // 班次序号
    private Integer serialNo;

    // 上班时间
    private String beginTime;

    // 结束时间
    private String endTime;

    // 地点id
    private String placeId;

    // 地点
    @TableField(exist = false)
    private String placeName;

    // 修改人id
    @ParseUsername
    private String updateUserId;

    // 修改时间
    private Date updateTime;

    // 创建人id
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户id
    private String tenantId;

}
