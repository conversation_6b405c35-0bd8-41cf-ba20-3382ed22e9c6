package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseIotConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseIotConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseIotConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-物联配置Controller
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Api(tags = "平台管理-物联配置")
@RestController
@RequestMapping("api/base/iot/configuration")
public class BaseIotConfigurationController extends BaseController {

    @Autowired
    private IBaseIotConfigurationService baseIotConfigurationService;

    /**
     * 查询平台管理-物联配置列表
     */
    @MonitorPerformance(description = "平台管理-查询物联配置列表")
    @ApiOperation(value = "查询物联配置列表")
    @GetMapping("/list")
    public IstarResponse list(BaseIotConfigurationPageRequest baseIotConfiguration) {
        return IstarResponse.ok(baseIotConfigurationService.selectBaseIotConfigurationList(baseIotConfiguration));
    }

    /**
     * 获取平台管理-物联配置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询物联配置详情")
    @ApiOperation(value = "查询物联配置详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseIotConfigurationService.selectBaseIotConfigurationById(id));
    }

    /**
     * 新增平台管理-物联配置
     */
    @MonitorPerformance(description = "平台管理-新增物联配置")
    @ApiOperation(value = "新增物联配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseIotConfiguration baseIotConfiguration) {
        return IstarResponse.ok(baseIotConfigurationService.insertBaseIotConfiguration(baseIotConfiguration));
    }

    /**
     * 修改平台管理-物联配置
     */
    @MonitorPerformance(description = "平台管理-修改物联配置")
    @ApiOperation(value = "修改物联配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseIotConfiguration baseIotConfiguration) {
        return IstarResponse.ok(baseIotConfigurationService.updateBaseIotConfiguration(baseIotConfiguration));
    }

    /**
     * 删除平台管理-物联配置
     */
    @MonitorPerformance(description = "平台管理-删除物联配置")
    @ApiOperation(value = "删除物联配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseIotConfigurationService.deleteBaseIotConfigurationByIds(ids));
    }
}
