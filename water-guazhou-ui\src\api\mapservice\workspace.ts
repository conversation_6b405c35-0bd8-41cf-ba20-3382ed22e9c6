import { request } from '@/plugins/axios/gis'

/**
* 添加工作空间
* @param params
* @returns
*/
export const AddBookMark = (params: {
  markname?:string
  remark?:string
  xmin:number
  ymin:number
  xmax:number
  ymax:number
}) => {
  return request({
    url: '/api/webapp/bookmarks/add',
    method: 'post',
    data: params
  })
}
/**
* 标签分页数据
* @param params
* @returns
*/
export const GetBookMarks = (params: {
  pagenumber:number
  pagesize:number
}) => {
  return request({
    url: '/api/webapp/bookmarks',
    method: 'post',
    data: params
  })
}
/**
* 删除标签
* @param ids
* @returns
*/
export const DeleteBookMarks = (id: string) => {
  return request({
    url: '/api/webapp/bookmarks/delete',
    method: 'post',
    data: { id }
  })
}
