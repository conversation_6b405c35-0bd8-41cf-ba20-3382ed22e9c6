/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.inputKv;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.InputKvEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 *
 */
@SqlDao
public interface InputKvRepository extends CrudRepository<InputKvEntity, String> {


    @Query("SELECT a FROM InputKvEntity a WHERE a.entityId = :entityId " + "order by a.ts desc ")
    List<InputKvEntity> findByEntityId(@Param("entityId") String entityId);

    @Query("SELECT a FROM InputKvEntity a WHERE a.entityId = :entityId AND a.ts >= :start and a.ts <=:endTime")
    List<InputKvEntity> getInputKvByEntityIdAndTime(@Param("entityId") String entityId, @Param("start") long start, @Param("endTime") long endTime);

    @Transactional
    @Modifying
    @Query("DELETE  FROM InputKvEntity a WHERE a.entityId= :entityId AND a.ts >= :start and a.ts <=:endTime")
    void deleteByTs(@Param("entityId") String entityId, @Param("start") long start, @Param("endTime") long end);

}
