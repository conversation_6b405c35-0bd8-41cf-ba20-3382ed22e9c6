package org.thingsboard.server.dao.sql.logicalFlow;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.LogicalFlowNode;

import java.util.List;

public interface LogicalFlowNodeRepository extends JpaRepository<LogicalFlowNode, String> {
    List<LogicalFlowNode> findByLogicalFlowIdAndParentId(String logicalFlowId, String logicalFlowNodeRootParentId);

    List<LogicalFlowNode> findByLogicalFlowId(String logicalFlowId);

    int deleteByLogicalFlowId(String id);

    List<LogicalFlowNode> findByType(String type);

    List<LogicalFlowNode> findByTypeAndLogicalFlowIdIn(String type, List<String> logicalFlowIds);
}
