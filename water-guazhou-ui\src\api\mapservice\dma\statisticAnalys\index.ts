import { request } from '@/plugins/axios';

export * from './plantSupply';
export * from './proSale';
export * from './inOutWater';
export * from './partitionSupply';

/**
 * 查询售水量报表
 * @param params
 * @returns
 */
export const GetDmaPartitionSaleReport = (
  params: IQueryPagerParams & {
    partitionId?: string;
    type?: string;
    year?: string;
    custCode?: string;
    custName?: string;
    meterBookId?: string;
    WaterCategory?: string;
  }
) => {
  return request({
    url: '/api/spp/statisticsReport/saleWater',
    method: 'get',
    params
  });
};

/**
 * 查询漏点台账统计
 * @param params
 * @returns
 */
export const GetDmaLossPointStatementReport = (params: {
  date: string;
  name: string;
}) => {
  return request({
    url: '/api/spp/statisticsReport/lossPoint',
    method: 'get',
    params
  });
};
/**
 * 查询漏失水量同比报表
 * @param params
 * @returns
 */
export const GetDmaLossWaterCompareToLastYear = (params: {
  date: string;
  name: string;
}) => {
  return request({
    url: '/api/spp/statisticsReport/lossWaterCompareToLastYear',
    method: 'get',
    params
  });
};
/**
 * 查询压力合格率报表
 * @param params
 * @returns
 */
export const GetDmaPressurePassRate = (params: {
  date: string;
  name: string;
}) => {
  return request({
    url: '/api/spp/statisticsReport/pressurePassRate',
    method: 'get',
    params
  });
};
/**
 * 查询大用户列表
 * @param params
 * @returns
 */
export const GetDmaBigUserReport = (params: {
  type?: string;
  date?: string;
  start?: string;
  end?: string;
  grade?: string;
}) => {
  return request({
    url: '/api/spp/statisticsReport/bigUser',
    method: 'get',
    params
  });
};
/**
 * 查询DMA进度统计报表
 * @param params
 * @returns
 */
export const GetDmaProgressOverview = (params: {
  status: string;
  partitionName: string;
}) => {
  return request({
    url: '/api/spp/statisticsReport/dmaOverview',
    method: 'get',
    params
  });
};
