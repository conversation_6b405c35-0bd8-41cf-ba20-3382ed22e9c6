import IdentifyParameters from '@arcgis/core/rest/support/IdentifyParameters'
import * as identify from '@arcgis/core/rest/identify'
import esriRequest from '@arcgis/core/request.js';
import * as webMercatorUtils from "@arcgis/core/geometry/support/webMercatorUtils.js";
import { letterSpacing } from 'html2canvas/dist/types/css/property-descriptors/letter-spacing';
/**
 * 点击查询的参数
 * @param options
 * @returns
 */
export const initIdentifyParams = (options?: {
  tolerance?: number
  layerIds?: number[]
  layerOption?: 'top' | 'visible' | 'all' | 'popup'
  width?: number
  height?: number
  returnGeometry?: boolean
  returnFieldName?: boolean
  geometry?: __esri.Geometry
  mapExtent?: __esri.Extent
}) => {
  return new IdentifyParameters({
    layerIds: [1, 2, 3, 4, 5],
    layerOption: 'all',
    tolerance: 1,
    returnGeometry: true,
    returnFieldName: false,
    ...(options || {})
  }) as __esri.IdentifyParameters
}
/**
 * 执行点击查询
 * @param url 查询服务路径
 * @param params 查询参数
 * @returns
 */
export const excuteIdentify = (url: string, params: __esri.IdentifyParameters) => {
  return identify.identify(url, params)
}

/**
 * 执行点击查询
 * @param url 查询服务路径
 * @param params 查询参数
 * @returns
 */
export const excuteIdentifyByGeoserver = async (view,wmsUrl,layers,screenPoint) => {
  // 构造 GetFeatureInfo 请求 URL
  const bbox = view.extent.toJSON(); // 当前地图范围
  const width = view.width; // 地图宽度
  const height = view.height; // 地图高度
  const x = Math.floor(screenPoint.x); // 点击位置的屏幕 X 坐标
  const y = Math.floor(screenPoint.y); // 点击位置的屏幕 Y 坐标
  const bbox4326 = {
    xmin: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmin, y: bbox.ymin }).x,
    ymin: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmin, y: bbox.ymin }).y,
    xmax: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmax, y: bbox.ymax }).x,
    ymax: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmax, y: bbox.ymax }).y
  };
  const getFeatureInfoUrl = `${wmsUrl}?` +
    `service=WMS&` +
    `version=1.1.1&` +
    `request=GetFeatureInfo&` +
    `layers=${layers}&` +
    `bbox=${bbox4326.xmin},${bbox4326.ymin},${bbox4326.xmax},${bbox4326.ymax}&` +
    `width=${width}&` +
    `height=${height}&` +
    `query_layers=${layers}&` +
    `info_format=application/json&` + // 返回格式（支持 text/xml 或 application/json）
    `x=${x}&` +
    `y=${y}&` +
    `feature_count=10&` + // 返回的特征数量
    `srs=EPSG:4326`; // 坐标系

  // 发送 GetFeatureInfo 请求
  let response = await esriRequest(getFeatureInfoUrl, {
    responseType: "json" // 返回格式为文本
  })
  .catch(function(error) {
    console.error("查询失败：", error);
  });
  return response;
}

/**
 * 执行点击查询
 * @param url 查询服务路径
 * @param params 查询参数
 * @returns
 */
export const excuteIdentifyAllLayersByGeoserver = async (view,wmsLayer,screenPoint) => {
  // 构造 GetFeatureInfo 请求 URL
  const wmsUrl = wmsLayer.url;
  const layers = wmsLayer.sublayers.map(layer => layer.name).join(",");
  const bbox = view.extent.toJSON(); // 当前地图范围
  const width = view.width; // 地图宽度
  const height = view.height; // 地图高度
  const x = Math.floor(screenPoint.x); // 点击位置的屏幕 X 坐标
  const y = Math.floor(screenPoint.y); // 点击位置的屏幕 Y 坐标
  const bbox4326 = {
    xmin: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmin, y: bbox.ymin }).x,
    ymin: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmin, y: bbox.ymin }).y,
    xmax: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmax, y: bbox.ymax }).x,
    ymax: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmax, y: bbox.ymax }).y
  };
  //http://localhost:5000/geoserver/pgsql/wms?service=WMS&version=1.1.1&request=GetFeatureInfo&layers=guandao&bbox=117.33123779296872%2C31.78619384765625%2C117.33261108398435%2C31.78756713867187&width=256&height=256&srs=EPSG%3A4326&query_layers=pgsql%3Aguandao&info_format=application%2Fjson&x=242&y=50
  //http://localhost:5000/geoserver/pgsql/wms?service=WMS&version=1.1.1&request=GetFeatureInfo&layers=guandao&bbox=117.33123779296872%2C31.78619384765625%2C117.33261108398435%2C31.78756713867187&width=478&height=1012&srs=EPSG%3A4326&query_layers=pgsql:guandao&info_format=application%2Fjson&x=300&y=510
  const getFeatureInfoUrl = `${wmsUrl}?` +
    `service=WMS&` +
    `version=1.1.1&` +
    `request=GetFeatureInfo&` +
    `layers=${layers}&` +
    `bbox=${bbox4326.xmin},${bbox4326.ymin},${bbox4326.xmax},${bbox4326.ymax}&` +
    `width=${width}&` +
    `height=${height}&` +
    `query_layers=${layers}&` +
    `info_format=application/json&` + // 返回格式（支持 text/xml 或 application/json）
    `x=${x}&` +
    `y=${y}&` +
    `srs=EPSG:4326`; // 坐标系

  // 发送 GetFeatureInfo 请求
  let response = await esriRequest(getFeatureInfoUrl, {
    responseType: "json" // 返回格式为文本
  })
  .catch(function(error) {
    console.error("查询失败：", error);
  });
  return response;
}
