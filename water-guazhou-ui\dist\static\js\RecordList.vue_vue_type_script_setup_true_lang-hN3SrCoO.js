import{_ as d}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./index-0NlGN6gS.js";import{s as g}from"./printUtils-C-AxhDcd.js";import{E as u,G as _}from"./useWaterCorrect-CpCzjcCp.js";import{d as y,c as b,r as l,o as h,g as x,h as R,i as w}from"./index-r0dFAfgr.js";const L=y({__name:"RecordList",setup(F,{expose:m}){const n=b(),e=l({dataList:[],columns:[{label:"分区名称",prop:"partitionName"},{label:"水表名称",prop:"partitionName"},{label:"日期",prop:"collectTime"},{label:"追加水量",prop:"correctWater"},{label:"修正日期",prop:"createTime"},{label:"操作人",prop:"updateUser"}],height:400,pagination:{refreshData:({page:r,size:o})=>{e.pagination.page=r,e.pagination.limit=o,a()}}}),f=l({title:"修正记录",labelWidth:70,group:[{fields:[{type:"input",label:"分区名称",field:"partitionName",inputStyle:{width:"200px"},extraFormItem:[{type:"btn-group",btns:[{perm:!0,text:"查询",styles:{marginLeft:"20px"},iconifyIcon:"ep:search",click:()=>a()},{perm:!0,text:"导出",type:"success",iconifyIcon:"ep:download",click:()=>a(!0)}]}]}]},{fields:[{type:"table",config:e}]}]}),a=async r=>{var o,i,s;try{const p={page:e.pagination.page||1,size:e.pagination.limit||20,partitionName:(i=(o=n.value)==null?void 0:o.refForm)==null?void 0:i.dataForm.partitionName};if(r){const c=await u(p);g(c.data,"供水量修正记录")}else{e.loading=!0;const t=(s=(await _(p)).data)==null?void 0:s.data;e.dataList=(t==null?void 0:t.data)||[],e.pagination.total=(t==null?void 0:t.total)||0}}catch{}e.loading=!1};return h(()=>{a()}),m({refRecord:n}),(r,o)=>{const i=d;return x(),R(i,{ref_key:"refRecord",ref:n,config:w(f)},null,8,["config"])}}});export{L as _};
