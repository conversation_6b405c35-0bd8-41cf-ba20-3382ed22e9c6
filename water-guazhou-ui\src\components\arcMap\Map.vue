<template>
  <div
    id="arcmap-wrapper"
    ref="refMapWrapper"
    class="map-wrapper"
    @click="(e) => emit('click', e)"
  >
    <div id="viewDiv" ref="refMap" class="viewDiv"></div>
    <div class="pop-container">
      <template v-for="pop in pops" :key="pop.id">
        <PopLayout
          v-show="pop.visible"
          :ref="'refPop' + pop.id"
          :title="pop.title"
          :show-more="pop.showMore"
          :show-back="pop.showBack"
          :status="pop.status"
          :background-color="pop.bgColor"
          :x="pop.x"
          :y="pop.y"
          :latitude="pop.latitude"
          :longitude="pop.longitude"
          :offsetx="pop.offsetX"
          :offsety="pop.offsetY"
          @more="$emit('pop-more', pop)"
          @back="$emit('pop-back', pop)"
          @toggle="(flag) => togglePop(pop, flag)"
          @refresh-position="setPopPosition(pop)"
        >
          <slot name="pop-default" :config="pop">
            <component
              :is="pop.customComponent"
              :visible="pop.visible"
              :config="pop.customConfig"
            ></component>
          </slot>
        </PopLayout>
      </template>
    </div>
    <PoiSearch id="tool-search-poi" @change="handlePoiChange"></PoiSearch>
    <div class="tools-temp-wrapper">
      <ArcWidgetButton
        id="tool-pipelength"
        ref="refArcWidgetButton-tool-pipelength"
        :icon="'mdi:ruler'"
        :title="'管线长度'"
        @click="
          (isCollapsed) =>
            handleToolClick('pipelength', '管线长度测量', isCollapsed)
        "
      ></ArcWidgetButton>
      <ArcWidgetButton
        id="tool-areameasure"
        ref="refArcWidgetButton-tool-areameasure"
        :icon="'gis:measure-area-alt'"
        :title="'面积测量'"
        @click="
          (isCollapsed) =>
            handleToolClick('areameasure', '面积测量', isCollapsed)
        "
      ></ArcWidgetButton>
      <div
        id="gis-overview"
        class="esri-widget esri-expand esri-component esri-widget--button custom-toolbar"
        @click="() => (state.showOverViewMap = !state.showOverViewMap)"
      >
        <el-icon :size="16" class="tool-icon">
          <Icon
            :icon="state.showOverViewMap ? 'ep:d-arrow-right' : 'mdi:earth'"
          ></Icon>
        </el-icon>
      </div>
    </div>
    <div
      v-show="state.showOverViewMap"
      id="overviewmap"
      class="overviewmap"
    ></div>
    <Panel
      v-if="state.mounted"
      ref="refToolPanel"
      :custom-class="'tool-panel'"
      :telport="'#arcmap-wrapper'"
      :title="state.toolPanelTitle"
      :destroy-by-close="true"
      :before-close="() => handleToolPanelClose()"
    >
      <PipeLength
        v-if="state.toolPanelOperate === 'pipelength'"
        :view="staticState.view"
      ></PipeLength>
      <AreaMeasure
        v-if="state.toolPanelOperate === 'areameasure'"
        :view="staticState.view"
      ></AreaMeasure>
    </Panel>
    <Panel
      v-if="state.mounted"
      ref="refPanel"
      :custom-class="panelCustomClass || 'gis-detail-panel'"
      :telport="'#arcmap-wrapper'"
      :draggable="false"
      :full-content="fullContent"
      :destroy-by-close="true"
      :after-open="handleDetailOpen"
      :show-close="detailClose ?? true"
      :max-min="detailMaxMin ?? true"
      :before-close="handleDetailClose"
    >
      <template #header>
        <slot name="detail-header"></slot>
      </template>
      <template #default>
        <slot name="detail-default"></slot>
      </template>
    </Panel>
    <slot name="map-bars"></slot>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import Point from '@arcgis/core/geometry/Point.js';
import Graphic from '@arcgis/core/Graphic.js';
import PoiSearch from './PoiSearchV2.vue';
import { useGisStore } from '@/store';
import {
  getPipeLayer,
  gotoAndHighLight,
  initMap,
  setSymbol
} from '@/utils/MapHelper';
import {
  useCoordinate,
  useLayerList,
  useScaleBar,
  useOverViewMap,
  useZoomBar,
  useHomeBar,
  usePrintBar,
  useWidgets,
  useLegend
} from '@/hooks/arcgis';
import { getMapLocationImageUrl } from '@/utils/URLHelper';

type IMapTools =
  | 'search'
  | 'layerlist'
  | 'coordinate'
  | 'pipe'
  | 'scale'
  | 'zoom'
  | 'pipelength'
  | 'print'
  | 'overviewmap'
  | 'home';
const refMap = ref<HTMLDivElement>();
const refToolPanel = ref<IPanelIns>();
const { proxy }: any = getCurrentInstance();
const refPanel = ref<IPanelIns>();
const refMapWrapper = ref<HTMLDivElement>();
const customTools = ['areameasure', 'pipelength'];
interface EmitMethods {
  (e: 'pipe-loaded'): any;
  (e: 'detail-closed'): any;
  (e: 'detail-opened'): any;
  (e: 'pop-more', attributes: any): any;
  (e: 'pop-back', attributes: any): any;
  (e: 'click', event: any): any;
  (e: 'pop-toggle', pop: IArcMarkerProps, flag?: boolean): any;
}
const emit = defineEmits<EmitMethods>();
const props = defineProps<{
  /** 针对详情的边距处理 */
  fullContent?: boolean;
  panelCustomClass?: string;
  tools?: IMapTools[];
  pops?: IArcPopConfig[];
  detailClose?: boolean;
  detailMaxMin?: boolean;
}>();
const state = reactive<{
  mounted: boolean;
  showOverViewMap: boolean;
  toolPanelTitle: string;
  toolPanelOperate: string;
}>({
  mounted: false,
  showOverViewMap: false,
  toolPanelTitle: '',
  toolPanelOperate: ''
});
const staticState: {
  view?: __esri.MapView;
  homeBar?: __esri.Home;
  legend?: __esri.Legend;
} = {};
// const refPipeLength = ref<InstanceType<typeof PipeLength>>()
const handleToolClick = async (
  path: string,
  title: string,
  isCollapsed: boolean,
  fromPanel?: boolean
) => {
  if (!isCollapsed) {
    state.toolPanelOperate = path;
    state.toolPanelTitle = title;
    refToolPanel.value?.Open();
    customTools.map((item) => {
      if (item !== state.toolPanelOperate) {
        proxy.$refs['refArcWidgetButton-tool-' + item]?.toggle(true);
      }
    });
  } else {
    !fromPanel && refToolPanel.value?.Close();
  }
};
const handleToolPanelClose = () => {
  customTools.map((item) => {
    proxy.$refs['refArcWidgetButton-tool-' + item]?.toggle(true);
  });
  // proxy.$refs['refArcWidgetButton-tool-' + state.toolPanelOperate]?.toggle(true)
};
const handleDetailOpen = () => {
  emit('detail-opened');
};
const handleDetailClose = () => {
  emit('detail-closed');
};
const handlePoiChange = (location: number[]) => {
  if (!staticState.view) return;
  if (location.length !== 2) return;
  const mark = new Graphic({
    geometry: new Point({
      longitude: location?.[0],
      latitude: location?.[1],
      spatialReference: staticState.view.spatialReference
    }),
    symbol: setSymbol('picture', {
      url: getMapLocationImageUrl(),
      yOffset: -8
    })
  });
  // const mark = createGraphic({
  //   geometry: createGeometry(
  //     'point',
  //     location,
  //     staticState.view.spatialReference
  //   ),
  //   symbol: setSymbol('picture', {
  //     url: getMapLocationImageUrl(),
  //     yOffset: -8
  //   })
  // })
  staticState.view?.graphics.removeAll();
  staticState.view?.graphics.add(mark);
  gotoAndHighLight(staticState.view, mark, {
    avoidHighlight: true,
    zoom: 16
  });
};
const toggleDetail = (open?: boolean) => {
  refPanel.value?.Toggle(open);
};
const isDetailOpened = () => {
  return refPanel.value?.visible;
};
const toggleDetailMaxmin = (type: 'max' | 'min' | 'normal') => {
  refPanel.value?.toggleMaxMin(type);
};
const setPopPosition = (popConfig: IArcPopConfig) => {
  const pop = proxy.$refs['refPop' + popConfig.id];
  if (!pop?.[0]) return;
  pop[0]?.setPosition(staticState.view);
};
const togglePop = (popConfig: IArcPopConfig, flag: boolean) => {
  closeAllPop();
  const id = popConfig.id;
  if (id === undefined) return;
  const pop = proxy.$refs['refPop' + id];
  if (!pop?.[0]) return;
  pop[0]?.toggle(flag);
  pop[0]?.setPosition(staticState.view);
};
const closeAllPop = () => {
  props.pops?.map((item) => {
    const id = item?.id;
    if (!id) return;
    const pop = proxy.$refs['refPop' + id];
    pop?.length && pop[0]?.toggle(false);
  });
};

const scale = useScaleBar();
const coordinate = useCoordinate();
const layerList = useLayerList();
const { addCustomWidget } = useWidgets();
const printBar = usePrintBar();
const home = useHomeBar();
const legend = useLegend();
// const compass = useCompass()
const overViewMap = useOverViewMap();
// const baseMapGallary = useBasemapGallary()
const zoom = useZoomBar();
const init = async (params?: {
  defaultCenter?: number[];
  zoom?: number;
  showPoi?: boolean;
  defaultBaseMap?:
    | 'vec_c'
    | 'vec_w'
    | 'img_c'
    | 'img_w'
    | 'ter_c'
    | 'ter_w'
    | 'ibo_c'
    | 'ibo_w';
  defaultFilter?: string;
  defaultFilterColor?: any;
}) => {
  await useGisStore().Auth();
  staticState.view = initMap({ el: refMap.value, ...params });
  await staticState.view.when();
  staticState.view.watch('extent', () => {
    props.pops?.map((item) => {
      const pop = proxy.$refs['refPop' + item.id];
      pop[0]?.setPosition(staticState.view);
    });
  });
  const tools = props.tools || [
    'search',
    'coordinate',
    'pipe',
    'scale',
    'zoom',
    'pipelength',
    'area',
    'print',
    'legend',
    'overviewmap',
    'home',
    'layerlist'
  ];
  tools.map((tool) => {
    if (!staticState.view) return;
    switch (tool) {
      case 'search':
        staticState.view.ui.add('tool-search-poi', 'top-right');
        break;
      case 'layerlist':
        layerList.init(staticState.view);
        break;
      case 'coordinate':
        coordinate.init(staticState.view);
        break;
      case 'pipe':
        getPipeLayer(staticState.view).then((layer) => {
          emit('pipe-loaded');
          // const extent = layer?.fullExtent
          // if (staticState.homeBar) {
          //   staticState.homeBar.goToOverride = (view: __esri.MapView | __esri.SceneView) => {
          //     view.goTo(extent)
          //   }
          // }
          if (staticState.legend) {
            staticState.legend.layerInfos = [{ title: layer.title, layer }];
          }
        });
        break;
      case 'scale':
        scale.init(staticState.view);
        break;
      case 'zoom':
        zoom.init(staticState.view, 'bottom-right');
        break;
      case 'pipelength':
        addCustomWidget(staticState.view, 'tool-pipelength', 'bottom-right');
        break;
      case 'area':
        addCustomWidget(staticState.view, 'tool-areameasure', 'bottom-right');
        break;
      case 'print':
        printBar.init(staticState.view, '', 'bottom-right');
        break;
      case 'overviewmap':
        overViewMap.init(staticState.view, 'overviewmap');
        addCustomWidget(staticState.view, 'gis-overview', 'bottom-right');
        break;
      case 'home':
        staticState.homeBar = home.init(staticState.view);
        break;
      case 'legend':
        staticState.legend = legend.init(staticState.view, 'botom-right', []);
        break;
      default:
        break;
    }
  });
  return staticState.view;
};
const destroy = () => {
  if (staticState.view?.destroy) return;
  staticState.homeBar?.destroy();
  staticState.view?.map.removeHandles();
  staticState.view?.removeHandles();
  staticState.view?.map.removeAll();
  staticState.view?.map.destroy();
  staticState.view?.destroy();
  staticState.view = undefined;
};
onMounted(() => {
  state.mounted = true;
});
onBeforeUnmount(() => {
  toggleDetail(false);
});
defineExpose({
  init,
  destroy,
  toggleDetail,
  toggleDetailMaxmin,
  isDetailOpened
});
</script>
<style lang="scss" scoped>
.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  :deep(.esri-ui-top-left) {
    flex-flow: row;
  }

  :deep(.esri-ui-bottom-right) {
    flex-flow: column;
  }
}
.viewDiv {
  width: 100%;
  height: 100%;
  background-color: var(--el-bg-color);
}
.tools-temp-wrapper {
  position: absolute;
}
.custom-toolbar {
  line-height: 32px;
  text-align: center;
  display: flex;

  .tool-icon {
    margin: auto;
  }
}

.overviewmap {
  width: 240px;
  height: 135px;
  position: absolute;
  right: 55px;
  bottom: 15px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
}
.pop-table-box {
  height: 200px;
}

:deep(.esri-ui-bottom-right) {
  &.esri-widget--button,
  .esri-widget--button {
    border-top: solid 1px rgba(173, 173, 173, 0.3);
  }
}
.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  :deep(.esri-ui-top-left) {
    flex-flow: row;
  }

  :deep(.esri-ui-bottom-right) {
    flex-flow: column;
  }
}
:deep(.esri-ui-bottom-right) {
  &.esri-widget--button,
  .esri-widget--button {
    border-top: solid 1px rgba(173, 173, 173, 0.3);
  }
}
</style>
<style lang="scss">
.tool-panel {
  width: 260px;
  min-height: 150px;
  position: absolute;
  top: 100px;
  right: 70px;
}

html {
  body {
    --esri-calcite-theme-name: 'light';
  }

  &.dark {
    body {
      --esri-calcite-theme-name: 'dark';
    }
  }
}
html.dark {
  .esri-input,
  .esri-widget--button,
  .esri-widget,
  .esri-select,
  .esri-menu,
  .esri-popup__pointer-direction,
  .esri-menu__list-item {
    background-color: #2e3449;
    &:hover {
      background-color: #2e3449;
    }
  }
  .esri-basemap-gallery__item:hover,
  .esri-basemap-gallery__item--selected,
  .esri-basemap-gallery__item.esri-basemap-gallery__item--selected:hover,
  .esri-basemap-gallery__item.esri-basemap-gallery__item--selected:focus {
    background-color: var(--el-bg-color);
  }
  .esri-widget--button {
    &.esri-compass,
    &.esri-expand,
    &.esri-home {
      background-color: #2e3449;
      &:hover {
        background-color: #2e3449;
      }
      &:focus-visible {
        outline: none;
      }
    }
  }
  .esri-legend,
  .esri-layer-list__item,
  .esri-layer-list__item-actions-menu-item,
  .esri-coordinate-conversion__select-row,
  .esri-coordinate-conversion__button {
    color: #adadad;
    background-color: var(--el-bg-color);
    &:hover {
      color: #fff;
      background-color: var(--el-bg-color);
    }
  }
  .esri-coordinate-conversion__row {
    .esri-select {
      background-color: var(--el-bg-color);
    }
  }
  .esri-coordinate-conversion__heading {
    background-color: var(--el-fill-color-lighter);
    .esri-coordinate-conversion__back-button {
      &,
      &:hover {
        background-color: transparent;
      }
    }
  }
  .esri-scale-bar {
    background-color: transparent;
  }
  .esri-coordinate-conversion__display {
    &,
    &:hover {
      background-color: transparent;
    }
  }
  .esri-widget--button {
    &,
    &:hover {
      background-color: transparent;
    }
  }
  // .esri-coordinate-conversion__tools{

  // }

  .esri-popup__header-container--button,
  .esri-popup__button,
  .esri-widget.esri-feature,
  .esri-widget.esri-search-result-renderer {
    &,
    &:hover {
      background-color: transparent;
    }
  }

  .esri-view .esri-view-surface--inset-outline:focus::after {
    outline: none;
  }
  .esri-print__layout-tab {
    &:hover,
    &:focus {
      background-color: var(--el-bg-color);
    }
  }
  .esri-print__layout-tab[aria-selected='true'] {
    background-color: var(--el-bg-color);
    border-bottom-color: var(--el-bg-color);
    &:hover {
      background-color: var(--el-bg-color);
    }
  }
  .esri-print__advanced-options-section {
    background-color: transparent;
  }
  .esri-coordinate-conversion--capture-mode {
    .esri-coordinate-conversion__mode-toggle {
      background-color: transparent;
      color: var(--el-text-color-regular);
    }
  }
}
html {
  .esri-widget {
    color: var(--el-text-color-regular);
    background-color: rgba(255, 255, 255, 0.9);
    &:hover {
      color: var(--el-text-color-regular);
    }
  }
  .esri-input,
  .esri-widget--button,
  .esri-select,
  .esri-menu,
  .esri-popup__pointer-direction,
  .esri-menu__list-item {
    color: var(--el-text-color-regular);
    background-color: rgba(255, 255, 255, 0.9);
    &:hover {
      color: var(--el-text-color-regular);
      background-color: rgba(255, 255, 255, 1);
    }
  }
  .esri-basemap-gallery__item:hover,
  .esri-basemap-gallery__item--selected,
  .esri-basemap-gallery__item.esri-basemap-gallery__item--selected:hover,
  .esri-basemap-gallery__item.esri-basemap-gallery__item--selected:focus {
    background-color: #fff;
    box-shadow: 0 0 1px #adadad;
    color: var(--el-text-color-regular);
    .esri-basemap-gallery__item-title {
      color: var(--el-text-color-regular);
    }
  }
  .esri-widget--button {
    &.esri-compass,
    &.esri-expand,
    &.esri-home {
      background-color: rgba(255, 255, 255, 0.9);
      &:hover {
        background-color: rgba(255, 255, 255, 1);
      }
      &:focus-visible {
        outline: none;
      }
    }
  }
  .esri-legend,
  .esri-layer-list__item,
  .esri-layer-list__item-actions-menu-item,
  .esri-coordinate-conversion__select-row,
  .esri-coordinate-conversion__button,
  .esri-layer-list__child-toggle,
  .esri-layer-list__item-toggle {
    color: var(--el-text-color-regular);
    background-color: var(--el-bg-color-page);
    &:hover {
      background-color: var(--el-bg-color-page);
    }
  }
  .esri-layer-list__item-toggle,
  .esri-layer-list__child-toggle {
    background-color: transparent;
    &:hover {
      background-color: transparent;
    }
  }
  .esri-coordinate-conversion__row {
    .esri-select {
      background-color: rgba(255, 255, 255, 1);
    }
  }
  .esri-coordinate-conversion__heading {
    background-color: rgba(255, 255, 255, 1);
    .esri-coordinate-conversion__back-button {
      &,
      &:hover {
        background-color: transparent;
      }
    }
  }
  .esri-coordinate-conversion--capture-mode {
    .esri-coordinate-conversion__mode-toggle {
      background-color: rgba(255, 255, 255, 1);
      color: var(--el-text-color-regular);
    }
  }
  .esri-coordinate-conversion__display {
    &,
    &:hover {
      background-color: transparent;
    }
  }
  .esri-widget--button {
    &,
    &:hover {
      background-color: transparent;
    }
  }
  // .esri-coordinate-conversion__tools{

  // }

  .esri-popup__header-container--button,
  .esri-popup__button,
  .esri-widget.esri-feature,
  .esri-widget.esri-search-result-renderer {
    &,
    &:hover {
      background-color: transparent;
    }
  }

  .esri-view .esri-view-surface--inset-outline:focus::after {
    outline: none;
  }
  .esri-print__layout-tab {
    color: var(--el-text-color-regular);
    &:hover,
    &:focus {
      color: var(--el-text-color-regular);
      background-color: rgba(255, 255, 255, 0.9);
    }
  }
  .esri-print__layout-tab[aria-selected='true'] {
    background-color: rgba(255, 255, 255, 0.9);
    border-bottom-color: rgba(255, 255, 255, 0.9);
    color: var(--el-text-color-regular);
    &:hover {
      color: var(--el-text-color-regular);
      background-color: rgba(255, 255, 255, 0.9);
    }
  }
  .esri-print__advanced-options-button-container,
  .esri-print__advanced-options-section,
  .esri-widget__heading,
  .esri-print__exported-file-link {
    color: var(--el-text-color-regular);
  }
  .esri-print__exported-file-link {
    &:hover {
      color: var(--el-text-color-regular);
    }
  }
  .esri-print__export-panel-container [class^='esri-icon-'] {
    margin: 0;
    margin-right: 4px;
  }
  .esri-print__scale-info-container {
    label {
      display: flex;
      align-items: center;
      input {
        margin-right: 4px;
      }
    }
  }
  .esri-print__advanced-options-section {
    background-color: transparent;
  }
  #tool-search-poi {
    border-radius: 4px;
  }

  .esri-ui-corner.esri-ui-bottom-right {
    .esri-component {
      &:first-child {
        border-radius: 12px 12px 0 0;
        & > .esri-widget--button {
          border-top: none;
        }
      }
      &:last-child {
        border-radius: 0 0 12px 12px;
      }
    }
  }
}
</style>
