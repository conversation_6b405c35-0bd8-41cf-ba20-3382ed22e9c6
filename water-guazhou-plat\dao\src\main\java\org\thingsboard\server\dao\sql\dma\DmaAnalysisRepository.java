package org.thingsboard.server.dao.sql.dma;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.dma.DmaAnalysisEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.Date;
import java.util.List;

@SqlDao
public interface DmaAnalysisRepository extends CrudRepository<DmaAnalysisEntity, String> {

    @Query(value = "select a from dma_analysis a, dma_partition b where a.partition_id = b.id and b.id = ?1 and a.create_time between ?2 and ?3 and b.tenant_id = ?4 order by a.create_time desc ", nativeQuery = true)
    List<DmaAnalysisEntity> findAll(String partitionId, Date startTime, Date endTime, String tenantId);

    List<DmaAnalysisEntity> findAllByPartitionIdInAndCreateTimeBetween(List<String> partitionIdList, Date start, Date end);

    List<DmaAnalysisEntity> findAllByPartitionIdAndCreateTimeBetween(String partitionId, Date start, Date end);
}
