package org.thingsboard.server.dao.smartService.seats;

import org.thingsboard.server.dao.model.sql.smartService.seats.SeatsExtension;

import java.util.List;

/**
 * 黑名单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface SeatsExtensionService {
    List getList(String keywords, String tenantId);

    SeatsExtension save(SeatsExtension seatsExtension);

    String delete(List<String> ids);

    boolean check(SeatsExtension seatsExtension);

    List getNotBindList(String tenantId);

    SeatsExtension getById(String extensionId);
}
