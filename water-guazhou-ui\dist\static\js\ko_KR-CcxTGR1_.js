import{o as s}from"./_commonjsHelpers-DCkdB7M8.js";import{r as m}from"./_commonjs-dynamic-modules-DfYEAvWy.js";function u(o,_){for(var r=0;r<_.length;r++){const e=_[r];if(typeof e!="string"&&!Array.isArray(e)){for(const t in e)if(t!=="default"&&!(t in o)){const i=Object.getOwnPropertyDescriptor(e,t);i&&Object.defineProperty(o,t,i.get?i:{enumerable:!0,get:()=>e[t]})}}}return Object.freeze(Object.defineProperty(o,Symbol.toStringTag,{value:"Module"}))}var d,n,a={},l={get exports(){return a},set exports(o){a=o}};d=l,(n=function(o,_){Object.defineProperty(_,"__esModule",{value:!0}),_.default={_decimalSeparator:".",_thousandSeparator:",",_percentPrefix:null,_percentSuffix:"%",_big_number_suffix_3:"k",_big_number_suffix_6:"M",_big_number_suffix_9:"G",_big_number_suffix_12:"T",_big_number_suffix_15:"P",_big_number_suffix_18:"E",_big_number_suffix_21:"Z",_big_number_suffix_24:"Y",_small_number_suffix_3:"m",_small_number_suffix_6:"μ",_small_number_suffix_9:"n",_small_number_suffix_12:"p",_small_number_suffix_15:"f",_small_number_suffix_18:"a",_small_number_suffix_21:"z",_small_number_suffix_24:"y",_byte_suffix_B:"B",_byte_suffix_KB:"KB",_byte_suffix_MB:"MB",_byte_suffix_GB:"GB",_byte_suffix_TB:"TB",_byte_suffix_PB:"PB",_date:"yyyy-MM-dd",_date_millisecond:"mm:ss SSS",_date_second:"HH:mm:ss",_date_minute:"HH:mm",_date_hour:"HH:mm",_date_day:"MMM dd",_date_week:"ww",_date_month:"MMM",_date_year:"yyyy",_duration_millisecond:"SSS",_duration_millisecond_second:"ss.SSS",_duration_millisecond_minute:"mm:ss SSS",_duration_millisecond_hour:"hh:mm:ss SSS",_duration_millisecond_day:"d'd' mm:ss SSS",_duration_millisecond_week:"d'd' mm:ss SSS",_duration_millisecond_month:"M'm' dd'd' mm:ss SSS",_duration_millisecond_year:"y'y' MM'm' dd'd' mm:ss SSS",_duration_second:"ss",_duration_second_minute:"mm:ss",_duration_second_hour:"hh:mm:ss",_duration_second_day:"d'd' hh:mm:ss",_duration_second_week:"d'd' hh:mm:ss",_duration_second_month:"M'm' dd'd' hh:mm:ss",_duration_second_year:"y'y' MM'm' dd'd' hh:mm:ss",_duration_minute:"mm",_duration_minute_hour:"hh:mm",_duration_minute_day:"d'd' hh:mm",_duration_minute_week:"d'd' hh:mm",_duration_minute_month:"M'm' dd'd' hh:mm",_duration_minute_year:"y'y' MM'm' dd'd' hh:mm",_duration_hour:"hh'h'",_duration_hour_day:"d'd' hh'h'",_duration_hour_week:"d'd' hh'h'",_duration_hour_month:"M'm' dd'd' hh'h'",_duration_hour_year:"y'y' MM'm' dd'd' hh'h'",_duration_day:"d'd'",_duration_day_week:"d'd'",_duration_day_month:"M'm' dd'd'",_duration_day_year:"y'y' MM'm' dd'd'",_duration_week:"w'w'",_duration_week_month:"w'w'",_duration_week_year:"w'w'",_duration_month:"M'm'",_duration_month_year:"y'y' MM'm'",_duration_year:"y'y'",_era_ad:"AD",_era_bc:"BC",A:"AM",P:"PM",AM:"AM",PM:"PM","A.M.":"오전","P.M.":"오후",January:"1월",February:"2월",March:"3월",April:"4월",May:"5월",June:"6월",July:"7월",August:"8월",September:"9월",October:"10월",November:"11월",December:"12월",Jan:"1월",Feb:"2월",Mar:"3월",Apr:"4월","May(short)":"5월",Jun:"6월",Jul:"7월",Aug:"8월",Sep:"9월",Oct:"10월",Nov:"11월",Dec:"12월",Sunday:"일요일",Monday:"월요일",Tuesday:"화요일",Wednesday:"수요일",Thursday:"목요일",Friday:"금요일",Saturday:"토요일",Sun:"일",Mon:"월",Tue:"화",Wed:"수",Thu:"목",Fri:"금",Sat:"토",_dateOrd:function(r){var e="일";if(r<11||r>13)switch(r%10){case 1:case 2:case 3:e="일"}return e},"Zoom Out":"축소",Play:"시작",Stop:"정지",Legend:"범례","Click, tap or press ENTER to toggle":"켜고 끄려면 클릭, 탭 혹은 엔터를 눌러주세요.",Loading:"불러오는 중",Home:"홈",Chart:"차트","Serial chart":"시리얼 차트","X/Y chart":"X/Y 차트","Pie chart":"파이 차트","Gauge chart":"게이지 차트","Radar chart":"레이더 차트","Sankey diagram":"생키 다이어그램","Flow diagram":"플로우 다이어그램","Chord diagram":"코드 다이어그램","TreeMap chart":"트리맵 차트","Force directed tree":"포스 디렉티드 트리","Sliced chart":"슬라이스 차트",Series:"시리즈","Candlestick Series":"캔들스틱 시리즈","OHLC Series":"OHLC 시리즈","Column Series":"컬럼 시리즈","Line Series":"라인 시리즈","Pie Slice Series":"파이 슬라이스 시리즈","Funnel Series":"퍼널 시리즈","Pyramid Series":"피라미드 시리즈","X/Y Series":"X/Y 시리즈",Map:"맵","Press ENTER to zoom in":"확대하려면 엔터를 누르세요.","Press ENTER to zoom out":"축소하려면 엔터를 누르세요.","Use arrow keys to zoom in and out":"확대 혹은 축소하려면 방향키를 이용하세요.","Use plus and minus keys on your keyboard to zoom in and out":"확대 혹은 축소하려면 키보드의 +/- 키를 이용하세요.",Export:"내보내기",Image:"이미지",Data:"데이터",Print:"인쇄","Click, tap or press ENTER to open":"열려면, 클릭, 탭 또는 엔터를 누르세요.","Click, tap or press ENTER to print.":"출력하려면, 클릭, 탭 또는 엔터를 누르세요.","Click, tap or press ENTER to export as %1.":"%1(으)로 내보내려면 클릭, 탭 또는 엔터를 누르세요.",'To save the image, right-click this link and choose "Save picture as..."':'이미지를 저장하려면, 이 링크를 마우스로 우클릭하여 "다른 이름으로 저장"을 선택하세요.','To save the image, right-click thumbnail on the left and choose "Save picture as..."':'이미지를 저장하려면, 좌측 썸네일을 마우스로 우클릭하여 "다른 이름으로 저장"을 선택하세요.',"(Press ESC to close this message)":"(이 메시지를 끄려면 ESC를 누르세요.)","Image Export Complete":"이미지 내보내기 완료","Export operation took longer than expected. Something might have gone wrong.":"내보내기가 지연되고 있습니다. 문제가 없는지 확인이 필요합니다.","Saved from":"다음으로부터 저장됨: ",PNG:"",JPG:"",GIF:"",SVG:"",PDF:"",JSON:"",CSV:"",XLSX:"","Use TAB to select grip buttons or left and right arrows to change selection":"선택 범위를 변경하려면 선택 버튼이나 좌우 화살표를 이용하세요.","Use left and right arrows to move selection":"선택 범위를 움직이려면 좌우 화살표를 이용하세요.","Use left and right arrows to move left selection":"왼쪽 선택 범위를 움직이려면 좌우 화살표를 이용하세요.","Use left and right arrows to move right selection":"오른쪽 선택 범위를 움직이려면 좌우 화살표를 이용하세요.","Use TAB select grip buttons or up and down arrows to change selection":"선택 범위를 변경하려면 선택 버튼이나 상하 화살표를 이용하세요.","Use up and down arrows to move selection":"선택 범위를 움직이려면 상하 화살표를 이용하세요.","Use up and down arrows to move lower selection":"하단 선택 범위를 움직이려면 상하 화살표를 이용하세요.","Use up and down arrows to move upper selection":"상단 선택 범위를 움직이려면 상하 화살표를 이용하세요.","From %1 to %2":"%1 부터 %2 까지","From %1":"%1 부터","To %1":"%1 까지","No parser available for file: %1":"파일 파싱 불가능: %1","Error parsing file: %1":"파일 파싱 오류: %1","Unable to load file: %1":"파일 로드 불가능: %1","Invalid date":"날짜 올바르지 않음"}}(m,a))!==void 0&&(d.exports=n);const f=u({__proto__:null,default:s(a)},[a]);export{f as k};
