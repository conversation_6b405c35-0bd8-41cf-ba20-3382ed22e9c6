package org.thingsboard.server.controller.device;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchase;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchasePageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchaseSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.purchase.DevicePurchaseService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/devicePurchase")
public class DevicePurchaseController extends BaseController {
    @Autowired
    private DevicePurchaseService devicePurchaseService;


    @GetMapping
    public IPage<DevicePurchase> findAllConditional(DevicePurchasePageRequest request) {
        return devicePurchaseService.findAllConditional(request);
    }

    @PostMapping
    public DevicePurchase save(@RequestBody DevicePurchaseSaveRequest req) throws ThingsboardException {
        return devicePurchaseService.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody DevicePurchaseSaveRequest req, @PathVariable String id) {
        return devicePurchaseService.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public IstarResponse delete(@PathVariable String id) {
        return IstarResponse.ok(devicePurchaseService.delete(id));
    }
}