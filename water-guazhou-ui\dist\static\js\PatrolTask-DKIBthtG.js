import{_ as Y}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{d as H,c as h,r as g,s as c,b as l,bB as I,S as Z,a1 as ee,dl as U,y as te,o as oe,Q as re,g as N,n as F,q as m,F as k,p as u,i as n,an as ae,bo as ie,bR as le,cy as se,aB as ne,aq as ce,cE as me,al as pe,b7 as W,da as S,bq as M,dp as de,db as ue,X as ge,C as fe}from"./index-r0dFAfgr.js";import{_ as be}from"./Search-NSrhrIa_.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as he}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import{D as ve,b as ye,P as _e,V,a as we}from"./plan-BLf3nu6_.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import Ie from"./RightDrawerMap-D5PhmGFO.js";import ke from"./PatrolDetail-3FfvS_c4.js";import{P as q}from"./config-C9CMv0E7.js";import{g as De}from"./config-DqqM5K5L.js";import{u as Re}from"./useDistrict-B4Fis32p.js";import{P as Te}from"./gisSetting-CQEP-Q3N.js";import{E as Pe,c as Ce,d as B}from"./config-DncLSA-r.js";import{w as xe}from"./useCTI-CrDoUkpT.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./Videor.vue_vue_type_script_setup_true_lang-EsHlP83o.js";import"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import"./circuitTaskFormRecord-CjbtiPXk.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";import"./area-Bpl-8n1R.js";import"./index-DeAQQ1ej.js";const Ee={class:"detail-page"},Le={key:0,class:"page-wrapper"},Ue={class:"table-box"},Ne={class:"page-wrapper"},Fe={class:"detail-header"},We={class:"detail-main overlay-y"},Se=H({__name:"PatrolTask",setup(Me){const D=h(),_=h(),w=h(),f=h(),v=h(),y={},p=g({tabs:[],loading:!1,layerIds:[],layerInfos:[],curPage:"table",orderTypes:[]}),j=g({scrollBarGradientColor:"#fafafa",filters:[{type:"radio-button",options:[{label:"全部",value:""},{label:"常规",value:"true"},{label:"临时",value:"false"}],field:"isNormalPlan"},{type:"radio-button",options:[{label:"全部",value:""},{label:"已接收",value:"true"},{label:"未接收",value:"false"}],field:"isReceived"},{type:"datetimerange",field:"fromTime",label:"开始时间"},{type:"user-select",labelWidth:60,field:"receiveUserId",label:"巡检员"},{type:"user-select",field:"creator",label:"创建人"},{type:"user-select",field:"collaborateUserId",label:"共同处理人"},{type:"input",label:"快速查找",field:"keyword"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:c(pe),click:()=>d()},{perm:!0,text:"重置",type:"default",svgIcon:c(W),click:()=>{var e;(e=w.value)==null||e.resetForm(),d()}},{perm:!0,text:"批量分派",svgIcon:c(S),disabled:()=>{var e;return!((e=a.selectList)!=null&&e.length)},click:()=>T(!0)},{perm:!0,text:"批量删除",type:"danger",svgIcon:c(M),disabled:()=>{var e;return!((e=a.selectList)!=null&&e.length)},click:()=>P()}]}],defaultParams:{}}),a=g({dataList:[],columns:[{minWidth:120,label:"是否常规计划",prop:"isNormalPlan",tag:!0,tagColor:e=>e.isNormalPlan?"#409eff":"#0cbb4a",formatter:e=>e.isNormalPlan?"常规":"临时"},{minWidth:120,label:"任务编号",prop:"code"},{minWidth:120,label:"任务名称",prop:"name"},{minWidth:120,label:"巡检周期",prop:"planCircleName"},{minWidth:160,label:"开始时间",prop:"beginTime"},{minWidth:160,label:"结束时间",prop:"endTime"},{minWidth:120,label:"巡检员",prop:"receiveUserName"},{minWidth:120,label:"创建人",prop:"creatorName"},{minWidth:160,label:"创建时间",prop:"createTime"},{minWidth:120,label:"到位状况",prop:"presentState"},{minWidth:120,label:"反馈状况",prop:"fallbackState"},{minWidth:100,align:"center",label:"任务状态",prop:"status",tag:!0,tagColor:e=>{var t;return(t=q[e.status])==null?void 0:t.color},formatter:e=>{var t;return((t=q[e.status])==null?void 0:t.text)||e.status}}],operationWidth:250,operations:[{perm:!0,text:"查看",svgIcon:c(de),type:"success",click:e=>{a.currentRow=e,z()}},{perm:e=>e.status==="PENDING"||e.status==="REJECTED",text:"分派",svgIcon:c(S),click:e=>{a.currentRow=e,T()}},{perm:e=>e.status==="VERIFY",text:"审核",svgIcon:c(W),type:"warning",click:e=>{a.currentRow=e,$()}},{perm:e=>e.status!=="PENDING",text:"详情",svgIcon:c(ue),type:"info",click:e=>{p.curPage="detail",a.currentRow=e,O()}},{perm:!0,text:"删除",svgIcon:c(M),type:"danger",click:e=>P(e)}],pagination:{refreshData:({page:e,size:t})=>{a.pagination.page=e||1,a.pagination.limit=t||20,d()}},handleSelectChange:e=>{a.selectList=e}}),R=g({dialogWidth:450,labelPosition:"right",title:"分派",group:[{fields:[{type:"user-select",field:"receiveUserId",label:"接收人员",departField:"receiveUserDepartmentId",rules:[{required:!0,message:"请选择接收人员"}]},{type:"user-select",multiple:!0,field:"collaborateUserId",label:"共同完成人"}]}],defaultValue:{collaborateUserId:[],receiveUserId:[]},submit:e=>{var o,r,i;const t=e.isMulti?((o=a.selectList)==null?void 0:o.map(s=>s.id))||[]:[a.currentRow.id];if(!t.length){l.warning("请选择要分派的任务");return}ve({taskIdList:t,collaborateUserId:(r=e.collaborateUserId)==null?void 0:r.join(","),receiveUserId:(i=e.receiveUserId)==null?void 0:i.join(",")}).then(s=>{var b;s.data.code===200?(l.success(s.data.message),d(),(b=v.value)==null||b.closeDialog()):l.error(s.data.message)}).catch(s=>{console.log(s),l.error("系统错误")})}}),T=async e=>{var t,o,r,i;R.defaultValue={isMulti:!!e,receiveUserDepartmentId:(t=a.currentRow)==null?void 0:t.receiveUserDepartmentId,receiveUserId:[(o=a.currentRow)==null?void 0:o.receiveUserId]},(r=v.value)==null||r.openDialog(),await I(),(i=v.value)==null||i.resetForm()},P=e=>{const t=(e?[e]:a.selectList)||[],o=t.map(r=>r.id);if(!(o!=null&&o.length)){l.warning("请选择要删除的数据");return}Z("确定删除？","提示信息").then(()=>{ye(o).then(r=>{r.data.code===200?(l.success(r.data.message),Te({optionName:Pe.XUNJIANRENWU,type:Ce.INSPECT,content:`${B.DELETE}巡检任务：${t.map(i=>i.name+(i.code?"【"+i.code+"】":"")).join("、")}`,optionType:B.DELETE}).catch(()=>{console.log("生成gis操作日志失败")}),d()):l.error(r.data.message)}).catch(r=>{console.log(r),l.error("系统错误")})}).catch(()=>{}),console.log(a.selectList)},G=()=>{p.curPage="table"},O=()=>{},A=g({dialogWidth:450,labelPosition:"right",title:"事件上报",group:[{fields:[{rules:[{required:!0,message:"请输入工单标题"}],type:"input",label:"工单标题",field:"title"},{rules:[{required:!0,message:"请选择紧急程度"}],type:"select",label:"紧急程度",field:"level",options:De()},{rules:[{required:!0,message:"请选择工单类型"}],type:"select-tree",label:"工单类型",field:"type",async autoFillOptions(e){var t;try{const o=await xe({isDel:0});e.options=ee(((t=o==null?void 0:o.data)==null?void 0:t.data)||[],{label:"name",value:"id",children:"children",id:"id"})}catch{}}},{type:"textarea",label:"描述",field:"remark"}]}],defaultValue:{},submit:e=>{var o;const t={...e,code:(o=a.currentRow)==null?void 0:o.code};_e(t).then(r=>{var i;r.data.code===200?(l.success(r.data.message),(i=D.value)==null||i.closeDialog()):l.error(r.data.message)}).catch(r=>{console.log(r),l.error("系统错误")})}}),J=g({dialogWidth:500,labelPosition:"top",title:"任务驳回",group:[{fields:[{type:"textarea",field:"remark",label:"驳回原因",placeholder:"请输入驳回原因，将通知给任务执行人",rows:4,rules:[{required:!0,message:"请输入驳回原因"},{min:5,message:"驳回原因不能少于5个字符"},{max:500,message:"驳回原因不能超过500个字符"}]}]}],defaultValue:{},submit:e=>{var o;if(!((o=a.currentRow)!=null&&o.id)){l.warning("未选择任务");return}const t=U.service({lock:!0,text:"提交中...",background:"rgba(0, 0, 0, 0.7)"});V({taskIdList:[a.currentRow.id],status:"REJECTED",rejectReason:e.remark}).then(r=>{var i;t.close(),r.data.code===200?(l.success("任务已驳回"),d(),(i=_.value)==null||i.closeDialog()):l.error(r.data.message||"驳回失败")}).catch(r=>{t.close(),console.log(r),l.error("系统错误")})}}),$=()=>{var e;if(!((e=a.currentRow)!=null&&e.id)){l.warning("未选择任务");return}te.confirm("请选择审核结果","审核",{confirmButtonText:"通过审核",cancelButtonText:"驳回",distinguishCancelAndClose:!0,closeOnClickModal:!1,type:"warning"}).then(()=>{const t=U.service({lock:!0,text:"提交中...",background:"rgba(0, 0, 0, 0.7)"});V({taskIdList:[a.currentRow.id],status:"RECEIVED"}).then(o=>{t.close(),o.data.code===200?(l.success("审核通过成功"),d()):l.error(o.data.message||"审核通过失败")}).catch(o=>{t.close(),console.log(o),l.error("系统错误")})}).catch(t=>{var o;t==="cancel"&&((o=_.value)==null||o.openDialog())})},C=Re("viewDiv"),z=async()=>{var e;a.currentRow&&((e=f.value)==null||e.toggleCustomDetailMaxmin("normal"),await I(),C.add(y.view,a.currentRow.districtAreaId,{goto:!0,ratio:1,showKeyPoint:!0}))},d=()=>{var t,o,r,i;const e=((t=w.value)==null?void 0:t.queryParams)||{};we({page:a.pagination.page||1,size:a.pagination.limit||20,...e,fromTime:e.fromTime&&e.fromTime[0],toTime:e.fromTime&&e.fromTime[1],receiveUserId:(o=e.receiveUserId)==null?void 0:o.join(","),collaborateUserId:(r=e.collaborateUserId)==null?void 0:r.join(","),creator:(i=e.creator)==null?void 0:i.join(",")}).then(s=>{var b,x,E,L;a.dataList=((x=(b=s.data)==null?void 0:b.data)==null?void 0:x.data)||[],a.pagination.total=((L=(E=s.data)==null?void 0:E.data)==null?void 0:L.total)||0})},X=async()=>{var t,o;p.layerIds=he(y.view);const e=await ge(p.layerIds);p.layerInfos=((o=(t=e.data)==null?void 0:t.result)==null?void 0:o.rows)||[]},K=()=>{var e;(e=f.value)==null||e.toggleCustomDetailMaxmin("normal")},Q=async e=>{var t,o;y.view=e,(t=f.value)==null||t.toggleCustomDetail(!0),await I(),(o=f.value)==null||o.toggleCustomDetailMaxmin("max"),await X()};return oe(()=>{d()}),re(()=>{C.destroy()}),(e,t)=>{const o=be,r=ce,i=me,s=Y;return N(),F(ne,null,[m(Ie,{ref_key:"refMap",ref:f,title:"巡检任务","detail-max-min":!0,"hide-right-drawer":!0,"hide-detail-close":!0,onMapLoaded:Q},{"detail-header":k(()=>t[0]||(t[0]=[u("span",null,"巡检任务",-1)])),"detail-default":k(()=>[u("div",Ee,[n(p).curPage==="table"?(N(),F("div",Le,[m(o,{ref_key:"refSearch",ref:w,config:n(j),style:{"margin-bottom":"8px"}},null,8,["config"]),u("div",Ue,[m(r,{config:n(a)},null,8,["config"])])])):ae("",!0),ie(u("div",Ne,[u("div",Fe,[m(i,{onClick:G},{default:k(()=>[m(n(se))]),_:1}),t[1]||(t[1]=u("div",{class:"detail-header-divider"},null,-1)),t[2]||(t[2]=u("span",null,"巡检任务详情",-1))]),u("div",We,[m(ke,{view:y.view,row:n(a).currentRow,"layer-info":n(p).layerInfos,onRowClick:K},null,8,["view","row","layer-info"])])],512),[[le,n(p).curPage==="detail"]])])]),_:1},512),m(s,{ref_key:"refDialogForm",ref:v,config:n(R)},null,8,["config"]),m(s,{ref_key:"refDialogFormReport",ref:D,config:n(A)},null,8,["config"]),m(s,{ref_key:"refDialogFormReject",ref:_,config:n(J)},null,8,["config"])],64)}}}),rr=fe(Se,[["__scopeId","data-v-f0ee5a7c"]]);export{rr as default};
