"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6695],{27535:(e,r,o)=>{var n;o.d(r,{Hy:()=>d,OF:()=>u,TD:()=>m,Tu:()=>f,VO:()=>p,aV:()=>c,kq:()=>l,rH:()=>n}),function(e){e.AsyncNotEnabled="AsyncNotEnabled",e.ModulesNotSupported="ModulesNotSupported",e.CircularModules="CircularModules",e.<PERSON>each="NeverReach",e.UnsupportedHashType="UnsupportedHashType",e.InvalidParameter="InvalidParameter",e.UnexpectedToken="UnexpectedToken",e.Unrecognised="Unrecognised",e.UnrecognisedType="UnrecognisedType",e.MaximumCallDepth="MaximumCallDepth",e.BooleanConditionRequired="BooleanConditionRequired",e.TypeNotAllowedInFeature="TypeNotAllowedInFeature",e.KeyMustBeString="KeyMustBeString",e.WrongNumberOfParameters="WrongNumberOfParameters",e.CallNonFunction="CallNonFunction",e.NoFunctionInTemplateLiteral="NoFunctionInTemplateLiteral",e.NoFunctionInDictionary="NoFunctionInDictionary",e.NoFunctionInArray="NoFunctionInArray",e.AssignModuleFunction="AssignModuleFunction",e.LogicExpressionOrAnd="LogicExpressionOrAnd",e.LogicalExpressionOnlyBoolean="LogicalExpressionOnlyBoolean",e.FuncionNotFound="FunctionNotFound",e.InvalidMemberAccessKey="InvalidMemberAccessKey",e.UnsupportedUnaryOperator="UnsupportUnaryOperator",e.InvalidIdentifier="InvalidIdentifier",e.MemberOfNull="MemberOfNull",e.UnsupportedOperator="UnsupportedOperator",e.Cancelled="Cancelled",e.ModuleAccessorMustBeString="ModuleAccessorMustBeString",e.ModuleExportNotFound="ModuleExportNotFound",e.Immutable="Immutable",e.OutOfBounds="OutOfBounds",e.IllegalResult="IllegalResult",e.FieldNotFound="FieldNotFound",e.PortalRequired="PortalRequired",e.LogicError="LogicError",e.ArrayAccessorMustBeNumber="ArrayAccessMustBeNumber",e.KeyAccessorMustBeString="KeyAccessorMustBeString",e.WrongSpatialReference="WrongSpatialReference"}(n||(n={}));const t={[n.TypeNotAllowedInFeature]:"Feature attributes only support dates, numbers, strings, guids.",[n.LogicError]:"Logic error - {reason}",[n.NeverReach]:"Encountered unreachable logic",[n.AsyncNotEnabled]:"Async Arcade must be enabled for this script",[n.ModuleAccessorMustBeString]:"Module accessor must be a string",[n.ModuleExportNotFound]:"Module has no export with provided identifier",[n.ModulesNotSupported]:"Current profile does not support modules",[n.ArrayAccessorMustBeNumber]:"Array accessor must be a number",[n.FuncionNotFound]:"Function not found",[n.FieldNotFound]:"Key not found - {key}",[n.CircularModules]:"Circular module dependencies are not allowed",[n.Cancelled]:"Execution cancelled",[n.UnsupportedHashType]:"Type not supported in hash function",[n.IllegalResult]:"Value is not a supported return type",[n.PortalRequired]:"Portal is required",[n.InvalidParameter]:"Invalid parameter",[n.WrongNumberOfParameters]:"Call with wrong number of parameters",[n.Unrecognised]:"Unrecognised code structure",[n.UnrecognisedType]:"Unrecognised type",[n.WrongSpatialReference]:"Cannot work with geometry in this spatial reference. It is different to the execution spatial reference",[n.BooleanConditionRequired]:"Conditions must use booleans",[n.NoFunctionInDictionary]:"Dictionaries cannot contain functions.",[n.NoFunctionInArray]:"Arrays cannot contain functions.",[n.NoFunctionInTemplateLiteral]:"Template Literals do not expect functions by value.",[n.KeyAccessorMustBeString]:"Accessor must be a string",[n.KeyMustBeString]:"Object keys must be a string",[n.Immutable]:"Object is immutable",[n.InvalidParameter]:"Invalid parameter",[n.UnexpectedToken]:"Unexpected token",[n.MemberOfNull]:"Cannot access property of null object",[n.MaximumCallDepth]:"Exceeded maximum function depth",[n.OutOfBounds]:"Out of bounds",[n.InvalidIdentifier]:"Identifier not recognised",[n.FuncionNotFound]:"Function not found",[n.CallNonFunction]:"Expression is not a function",[n.InvalidMemberAccessKey]:"Cannot access value using a key of this type",[n.AssignModuleFunction]:"Cannot assign function to module variable",[n.UnsupportedUnaryOperator]:"Unsupported unary operator",[n.UnsupportedOperator]:"Unsupported operator",[n.LogicalExpressionOnlyBoolean]:"Logical expressions must be boolean",[n.LogicExpressionOrAnd]:"Logical expression can only be combined with || or &&"};class a extends Error{constructor(...e){super(...e)}}class s extends a{constructor(e,r){super(i(r)+e.message,{cause:e}),this.loc=null,Error.captureStackTrace&&Error.captureStackTrace(this,s),r&&r.loc&&(this.loc=r.loc)}}class c extends Error{constructor(e,r,o,n){super("Execution error - "+i(o)+d(t[r],n)),this.loc=null,this.declaredRootClass="esri.arcade.arcadeexecutionerror",Error.captureStackTrace&&Error.captureStackTrace(this,c),o&&o.loc&&(this.loc=o.loc)}}function i(e){return e&&e.loc?`Line : ${e.loc.start?.line}, ${e.loc.start?.column}: `:""}class u extends Error{constructor(e,r,o,n){super("Compilation error - "+i(o)+d(t[r],n)),this.loc=null,this.declaredRootClass="esri.arcade.arcadecompilationerror",Error.captureStackTrace&&Error.captureStackTrace(this,u),o&&o.loc&&(this.loc=o.loc)}}class l extends Error{constructor(){super("Uncompilable code structures"),this.declaredRootClass="esri.arcade.arcadeuncompilableerror",Error.captureStackTrace&&Error.captureStackTrace(this,l)}}function d(e,r){try{if(!r)return e;for(const o in r){let n=r[o];n||(n=""),e=e.replace("{"+o+"}",r[o])}}catch(e){}return e}function p(e,r,o){return"esri.arcade.arcadeexecutionerror"===o.declaredRootClass||"esri.arcade.arcadecompilationerror"===o.declaredRootClass?null===o.loc&&r&&r.loc?new s(o,{cause:o}):o:("esri.arcade.featureset.support.featureseterror"===o.declaredRootClass||"esri.arcade.featureset.support.sqlerror"===o.declaredRootClass||o.declaredRootClass,r&&r.loc?new s(o,{cause:o}):o)}var m;!function(e){e.UnrecognisedUri="UnrecognisedUri",e.UnsupportedUriProtocol="UnsupportedUriProtocol"}(m||(m={}));const g={[m.UnrecognisedUri]:"Unrecognised uri - {uri}",[m.UnsupportedUriProtocol]:"Unrecognised uri protocol"};class f extends Error{constructor(e,r){super(d(g[e],r)),this.declaredRootClass="esri.arcade.arcademoduleerror",Error.captureStackTrace&&Error.captureStackTrace(this,f)}}},5732:(e,r,o)=>{o.d(r,{c:()=>n});var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}}}]);