import{d as R,c as d,am as T,o as x,g as b,h as D,F as i,q as u,ax as M,p as N,G as f,cd as S,ce as V,D as h,cf as E,b as J,bb as K,J as L,L as O}from"./index-r0dFAfgr.js";import{s as q}from"./index-CaaU9niG.js";const F={class:"dialog-footer"},$=R({__name:"btnpermissions",props:{roleId:{default:""},curRole:{default:{}}},emits:["handleclose"],setup(k,{expose:y}){const a=k,s=d(!1),v=d([]),m=d([]),p=async()=>{let e=(await S()).data.map(o=>(o.children=o.children.filter(l=>l.children&&l.children.length>0),o));e=e.filter(o=>o.children&&o.children.length>0),v.value=e},w=()=>s.value=!0,g=()=>s.value=!1,n=d(),C=async()=>{var e,o;const t=a.roleId?await V(h(a.roleId)):void 0;m.value=((e=t==null?void 0:t.data)==null?void 0:e.map(l=>l.id))||[],(o=n.value)==null||o.setCheckedKeys(m.value,!1)},B=async()=>{var r,c;const t=a!=null&&a.roleId?await E(h(a==null?void 0:a.roleId),(r=n.value)==null?void 0:r.getCheckedKeys(!0)):void 0,e={...JSON.parse(((c=a==null?void 0:a.curRole)==null?void 0:c.additionalInfo)||"{}")},o={...(a==null?void 0:a.curRole)||{},additionalInfo:JSON.stringify(e)},l=await q(o);(t==null?void 0:t.status)===200&&l.status===200&&(J.success("操作成功"),s.value=!1)},I=()=>{var t;(t=n.value)==null||t.setCheckedKeys([],!1)};T(()=>s.value,()=>{_()});const _=async()=>{await p(),await C()};return x(()=>{_()}),y({openDialog:w,closeDialog:g}),(t,e)=>{const o=K,l=L,r=O;return b(),D(r,{modelValue:s.value,"onUpdate:modelValue":e[0]||(e[0]=c=>s.value=c),title:"按钮权限赋予",width:"30%","close-on-click-modal":!1},{default:i(()=>[u(o,{ref_key:"refTree",ref:n,data:v.value,"show-checkbox":"","node-key":"id","default-expand-all":!0},null,8,["data"]),M(t.$slots,"footer",{},()=>[N("span",F,[u(l,{type:"primary",onClick:B},{default:i(()=>e[1]||(e[1]=[f("保存")])),_:1}),u(l,{onClick:I},{default:i(()=>e[2]||(e[2]=[f("清空")])),_:1}),u(l,{onClick:g},{default:i(()=>e[3]||(e[3]=[f("取 消")])),_:1})])])]),_:3},8,["modelValue"])}}});export{$ as _};
