import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'
import printJS from 'print-js'

export function toBlob(base64Data) {
  let byteString = base64Data
  if (base64Data.split(',')[0].indexOf('base64') >= 0) {
    byteString = atob(base64Data.split(',')[1]) // base64 解码
  } else {
    byteString = unescape(base64Data.split(',')[1])
  }
  // 获取文件类型
  const mimeString = base64Data.split('')[0].split(':')[1] // mime类型

  // ArrayBuffer 对象用来表示通用的、固定长度的原始二进制数据缓冲区
  // let arrayBuffer = new ArrayBuffer(byteString.length) // 创建缓冲数组
  // let uintArr = new Uint8Array(arrayBuffer) // 创建视图

  const uintArr = new Uint8Array(byteString.length) // 创建视图

  for (let i = 0; i < byteString.length; i += 1) {
    uintArr[i] = byteString.charCodeAt(i)
  }
  // 生成blob
  const blob = new Blob([uintArr], {
    type: mimeString
  })
  // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
  return blob
}

export function outPutPdf(idName, pdfName, isDownload = false, isPrint = false, callback) {
  const element = document.getElementById(idName) as any // 这个dom元素是要导出的pdf的div容器
  const w = element.offsetWidth // 获得该容器的宽
  const h = element.offsetHeight // 获得该容器的高
  const offsetTop = element.offsetTop // 获得该容器到文档顶部的距离
  const offsetLeft = element.offsetLeft // 获得该容器到文档最左的距离
  const canvas = document.createElement('canvas')
  let abs = 0
  const winI = document.body.clientWidth // 获得当前可视窗口的宽度（不包含滚动条）
  const winO = window.innerWidth // 获得当前窗口的宽度（包含滚动条）
  if (winO > winI) {
    abs = (winO - winI) / 2 // 获得滚动条宽度的一半
  }
  canvas.width = w * 2 // 将画布宽&&高放大两倍
  canvas.height = h * 2
  const context = canvas.getContext('2d') as any
  context.scale(2, 2)
  context.translate(-offsetLeft - abs, -offsetTop)
  // 这里默认横向没有滚动条的情况，因为offset.left()，有无滚动条的时候存在差值，因此translate的时候，要把这个差值去掉
  html2Canvas(element, {
    useCORS: true, // 允许加载跨域的图片
    allowTaint: true,
    scale: 2 // 提升画面质量，但是会增加文件大小
  }).then(cs => {
    const contentWidth = cs.width
    const contentHeight = cs.height
    // 一页pdf显示html页面生成的canvas高度
    const pageHeight = contentWidth / 592.28 * 841.89
    // 未生成pdf的html页面高度
    let leftHeight = contentHeight
    // 页面偏移
    let position = 0
    // a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
    const imgWidth = 595.28
    const imgHeight = 592.28 / contentWidth * contentHeight
    const pageDate = cs.toDataURL('image/jpeg', 1.0)

    const pdf = new JsPDF('p', 'pt', 'a4')
    // 有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面的高度（841.89）
    // 当内容未超过pdf一页显示的范围，无需分页
    if (leftHeight < pageHeight) {
      pdf.addImage(pageDate, 'JPEG', 0, position, imgWidth, imgHeight)
    } else { // 分页
      while (leftHeight > 0) {
        pdf.addImage(pageDate, 'JPEG', 0, position, imgWidth, imgHeight)
        leftHeight -= pageHeight
        position -= 841.89
        // 避免添加空白页
        if (leftHeight > 0) {
          pdf.addPage()
        }
      }
    }
    if (isDownload) {
      pdf.save(`${pdfName}.pdf`)
    }
    if (isPrint) {
      const link = window.URL.createObjectURL(toBlob(pdf.output('datauristring')))
      // const myWindow = window.open(link) as any
      // myWindow.print()
      printJS({ printable: link, type: 'pdf' })
    }
    callback && callback(pdf)
  })
}
