// 智慧生产=二供管理-泵房信息 /api
import request from '@/plugins/axios';

// 泵房台账列表
export function pumpHouseStorageList(params?: any) {
  return request({
    url: '/api/sp/pumpHouseStorage',
    method: 'get',
    params
  });
}

// 新增泵房台账
export function addPumpHouseStorage(params?: any) {
  return request({
    url: '/api/sp/pumpHouseStorage',
    method: 'post',
    data: params
  });
}

// 修改泵房台账
export function editPumpHouseStorage(params?: any) {
  return request({
    url: `/api/sp/pumpHouseStorage/${params.id}`,
    method: 'patch',
    data: params
  });
}

// 删除泵房台账
export function delPumpHouseStorage(id: string) {
  return request({
    url: `/api/sp/pumpHouseStorage/${id}`,
    method: 'delete'
  });
}

// 批量添加泵房台账
export function batchAddPumpHouseStorage(params?: any) {
  return request({
    url: `/api/sp/pumpHouseStorage/batch`,
    method: 'post',
    data: params
  });
}

// 泵房台账模板
export function pumpHouseStorageTemplate() {
  return request({
    url: '/api/sp/pumpHouseStorage/excel/template',
    responseType: 'blob',
    method: 'get'
  });
}
// 泵房台账导出
export function pumpHouseStorageExport(params?: any) {
  return request({
    url: '/api/sp/pumpHouseStorage/excel/export',
    method: 'get',
    responseType: 'blob',
    params
  });
}

//* ******************************//

// 泵机管理列表
export function pumpManageList(params?: any) {
  return request({
    url: '/api/sp/pumpManage',
    method: 'get',
    params
  });
}

// 新增泵机管理
export function addPumpManage(params?: any) {
  return request({
    url: '/api/sp/pumpManage',
    method: 'post',
    data: params
  });
}

// 修改泵机管理
export function editPumpManage(params?: any) {
  return request({
    url: `/api/sp/pumpManage/${params.id}`,
    method: 'patch',
    data: params
  });
}

// 删除泵泵机管理
export function delPumpManage(id: string) {
  return request({
    url: `/api/sp/pumpManage/${id}`,
    method: 'delete'
  });
}

// 批量添加泵泵机管理
export function batchAddPumpManage(params?: any) {
  return request({
    url: `/api/sp/pumpManage/batch`,
    method: 'post',
    data: params
  });
}

// 泵机管理模板
export function pumpManageTemplate() {
  return request({
    url: '/api/sp/pumpManage/excel/template',
    responseType: 'blob',
    method: 'get'
  });
}
// 泵机管理导出
export function pumpManageExport(params?: any) {
  return request({
    url: '/api/sp/pumpManage/excel/export',
    method: 'get',
    responseType: 'blob',
    params
  });
}

// 台账文件列表
export function fileRegistryList(params?: any) {
  return request({
    url: '/api/fileRegistry',
    method: 'get',
    params
  });
}

// 保存台账文件
export function saveRegistry(params?: any) {
  return request({
    url: '/api/fileRegistry',
    method: 'post',
    data: params
  });
}

// 删除台账文件
export function delRegistry(id: string) {
  return request({
    url: `/api/fileRegistry/${id}`,
    method: 'delete'
  });
}
