<!--流量日报表 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      ref="refTable"
      :config="TableConfig"
      class="card-table"
    ></CardTable>
  </TreeBox>
</template>
<script lang="ts" setup>
import { GetDmaPartitionDayliFlowReport } from '@/api/mapservice/dma'
import { ICardTableIns, ISearchIns } from '@/components/type'
import { usePartition } from '@/hooks/arcgis'
import { formatterDate } from '@/utils/GlobalHelper'

const refTable = ref<ICardTableIns>()
const refSearch = ref<ISearchIns>()

const TreeData = reactive<SLTreeConfig>({
  data: [],
  title: '选择分区',
  expandOnClickNode: false,
  showCheckbox: true,
  defaultExpandAll: true,
  checkedKeys: [],
  handleCheck: (
    ids: string[],
    data: {
      checkedKeys?: string[] | undefined
      checkedNodes?: Omit<NormalOption, 'children'>[] | undefined
    }
  ) => {
    TreeData.checkedKeys = data.checkedKeys || []
    TreeData.checkedNodes = data.checkedNodes || []
    // refreshData()
  }
  // treeNodeHandleClick: (data: NormalOption) => {
  //   if (TreeData.currentProject !== data) {
  //     TreeData.currentProject = data
  //     refreshData()
  //   }
  // }
})

// 列表模式搜索配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    date: moment().format(formatterDate)
  },
  filters: [
    {
      type: 'date',
      label: '查询日期',
      clearable: false,
      field: 'date'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ]
})
// 列表
const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  columns: [
    {
      prop: 'partitionType',
      label: '分区类别',
      minWidth: 100,
      fixed: 'left'
    },
    {
      prop: 'partitionName',
      label: '分区名称',
      minWidth: 160,
      fixed: 'left'
    },
    {
      prop: 'title',
      label: '日供水（总）量（立方米）',
      align: 'center',
      subColumns: [
        {
          prop: 'dayFlow',
          label: '本日流量',
          minWidth: 160
        },
        {
          prop: 'dayFlowCompareToLastDayRate',
          label: '环比昨日',
          unit: '(%)',
          minWidth: 160
        },
        {
          prop: 'dayChangeRate',
          label: '日增减率',
          unit: '(%)',
          minWidth: 160
        },
        {
          prop: 'dayFlowCompareToLastYearRate',
          label: '日流量同比去年',
          unit: '(%)',
          minWidth: 160
        },
        {
          prop: 'tenDaysFlow',
          label: '本旬累计',
          minWidth: 160
        },
        {
          prop: 'tenDaysFlowCompareToLastTenDaysRate',
          label: '环比上旬',
          unit: '(%)',
          minWidth: 160
        },
        {
          prop: 'tenDaysChangeRate',
          label: '旬增减率',
          unit: '(%)',
          minWidth: 160
        },
        {
          prop: 'tenDaysFlowCompareToLastYearRate',
          label: '旬流量同比去年',
          unit: '(%)',
          minWidth: 160
        },
        {
          prop: 'monthFlow',
          label: '本月累计',
          minWidth: 160
        },
        {
          prop: 'monthFlowCompareToLastMonthRate',
          label: '环比上月',
          unit: '(%)',
          minWidth: 160
        },
        {
          prop: 'monthChangeRate',
          label: '月增减率',
          unit: '(%)',
          minWidth: 160
        },
        {
          prop: 'monthFlowCompareToLastYearRate',
          label: '月流量同比去年',
          unit: '(%)',
          minWidth: 160
        },
        {
          prop: 'yearFlow',
          label: '本年累计',
          minWidth: 160
        },
        {
          prop: 'yearFlowCompareToLastYearRate',
          label: '同比去年',
          unit: '(%)',
          minWidth: 160
        },
        {
          prop: 'dayFlowDivideParentRate',
          label: '日流量占比上级流量',
          unit: '(%)',
          minWidth: 180
        },
        {
          prop: 'dayFlowDivideParentCompareToLastDayRate',
          label: '日流量占比环比昨日',
          unit: '(%)',
          minWidth: 180
        },
        {
          prop: 'dayFlowDivideParentCompareToLastYearRate',
          label: '日流量占比环比去年',
          unit: '(%)',
          minWidth: 180
        },
        {
          prop: 'tenDaysFlowDivideParentRate',
          label: '旬流量占比上级流量',
          unit: '(%)',
          minWidth: 180
        },
        {
          prop: 'tenDaysFlowDivideParentCompareToLastTenDaysRate',
          label: '旬流量占比环比上旬',
          unit: '(%)',
          minWidth: 180
        },
        {
          prop: 'tenDaysFlowDivideParentCompareToLastYearRate',
          label: '旬流量占比同比去年',
          unit: '(%)',
          minWidth: 180
        },
        {
          prop: 'monthFlowDivideParentRate',
          label: '月流量占比上级流量',
          unit: '(%)',
          minWidth: 180
        },
        {
          prop: 'monthFlowDivideParentCompareToLastMonthRate',
          label: '月流量占比环比上月',
          unit: '(%)',
          minWidth: 180
        },
        {
          prop: 'monthFlowDivideParentCompareToLastYearRate',
          label: '月流量占比同比去年',
          unit: '(%)',
          minWidth: 180
        },
        {
          prop: 'yearFlowDivideParentRate',
          label: '年累计占比上级流量',
          unit: '(%)',
          minWidth: 180
        },
        {
          prop: 'yearFlowDivideParentCompareToLastYearRate',
          label: '年累计占比同比去年',
          unit: '(%)',
          minWidth: 180
        }
      ]
    }
  ],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

// 数据获取
const refreshData = async () => {
  if (!TreeData.checkedNodes?.length) {
    // SLMessage.warning('请先选择分区')
    return
  }
  TableConfig.loading = true
  try {
    const res = await GetDmaPartitionDayliFlowReport({
      partitionIds: TreeData.checkedNodes?.map(item => item.value)?.join(','),
      day: refSearch.value?.queryParams.date
    })
    TableConfig.dataList = res.data.data || []
  } catch (error) {
    //
  }
  TableConfig.loading = false
}

const partition = usePartition()
onMounted(async () => {
  await partition.getTree()
  TreeData.data = partition.Tree.value
  refreshData()
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}
</style>
