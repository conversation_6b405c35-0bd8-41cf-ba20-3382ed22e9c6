import{d as H,a0 as P,M as W,c as I,r as d,s as A,D as p,l as K,S as D,b as C,o as Y,bo as q,i as r,g,h as v,F as b,q as u,an as T,bp as U,bq as $,b6 as J,br as z}from"./index-r0dFAfgr.js";import{_ as G}from"./TreeBox-DDD2iwoR.js";import{_ as Q}from"./CardTable-rdWOL4_6.js";import{_ as X}from"./CardSearch-CB_HNR-Q.js";import{_ as Z}from"./index-BJ-QPYom.js";import{h as ee,i as te,c as ae,a as ie}from"./index-Bj5d3Vsu.js";import{b as oe}from"./index-BggOjNGp.js";import ne from"./confirmDialog-CX21K6ND.js";import re from"./historyTable-BL4IYIFD.js";import se from"./infoTable-CW0u6ryD.js";import{f as L}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const Te=H({__name:"realTimeAlarm",setup(le){const h=P(),{$btnPerms:_}=W(),S=I(),R=I(),N=I(),k=[{label:"紧急",color:"#F56C6C"},{label:"重要",color:"#E6A23C"}],a=d({alarmState:"",deviceName:new Map,alarmInfo:new Map,listFilter:!1,filterItem:"",unconfirmed:0,unsolved:0,unconfirmedList:new Map,confirmInfo:{visible:!1,row:{},close:()=>{a.confirmInfo.visible=!1}},handleInfo:{visible:!1,row:{},close:()=>{a.handleInfo.visible=!1}},historyInfo:{visible:!1,project:{},row:{},close:()=>{a.historyInfo.visible=!1}},detailsInfo:{visible:!1,row:{},close:()=>{a.detailsInfo.visible=!1}},severityColor:{提示:"rgb(85,204,244)",次要:"rgb(255,216,0)",重要:"#f58717",紧急:"rgb(245,75,23)",严重:"#FF0000"},alarmTypes:{offline:"掉线报警",scope:"范围报警",change:"变动报警"}}),f=d({title:"区域划分",data:h.projectList,currentProject:h.selectedProject,isFilterTree:!0,treeNodeHandleClick:t=>{f.currentProject=t,h.SET_selectedProject(t),m()}}),x=d({filters:[{label:"搜索",field:"keyword",type:"input"},{field:"type",type:"select",label:"报警类型",options:[{value:"scope",label:"范围报警"},{value:"change",label:"变动报警"},{value:"offline",label:"掉线报警"}]},{type:"btn-group",btns:[{text:"查询",perm:!0,icon:"iconfont icon-chaxun",click:()=>m()},{text:"批量确认",svgIcon:A(U),perm:_("RealTimeAlarmMultiConfirm"),disabled:()=>{var t;return!((t=n.selectList)!=null&&t.length)},click:()=>O()},{text:"批量解除",type:"danger",svgIcon:A($),perm:_("RealTimeAlarmMultiRemove"),disabled:()=>{var t;return!((t=n.selectList)!=null&&t.length)},click:()=>w()}]}]}),n=d({loading:!1,dataList:[],selectList:[],handleSelectChange:t=>{n.selectList=t},columns:[{prop:"name",label:"报警名称",minWidth:160},{prop:"showDeviceN",label:"报警设备",minWidth:160},{prop:"showDeviceN",label:"报警类型",minWidth:120},{prop:"cycleName",label:"周期",minWidth:55},{prop:"alarmValue",label:"报警触发值",minWidth:120},{prop:"recoverSet",label:"恢复触发值",minWidth:120},{prop:"createdTime",label:"报警时间",minWidth:180,icon:"iconfont icon-shijian",formatter:t=>L(t.createdTime,"YYYY-MM-DD HH:mm"),iconStyle:{color:"#69e850"}},{prop:"severity",label:"报警级别",tag:!0,tagColor:t=>{var i;return((i=k.find(o=>o.label===t.severity))==null?void 0:i.color)||""},formatter:t=>{var i;return(i=k.find(o=>o.label===t.severity))==null?void 0:i.label}},{prop:"confirm",label:"报警状态",minWidth:240,cellStyle:t=>t.confirm!=="未恢复 | 未确认"?"#36a624":""}],operationFixed:"right",operations:[{text:"强制解除",isTextBtn:!0,perm:_("RealTimeAlarmRemove"),icon:"iconfont icon-xiangqing",click:t=>w(t)},{text:"历史",perm:!0,isTextBtn:!0,icon:"iconfont icon-xiangqing",click:t=>V(t)},{text:"详情",perm:!0,isTextBtn:!0,icon:"iconfont icon-xiangqing",click:t=>B(t)}],operationWidth:"260px",pagination:{refreshData:({page:t,size:i})=>{n.pagination.page=t,n.pagination.limit=i,m()}}}),M=d({title:"历史记录",group:[]}),F=d({title:"告警详情",group:[]}),m=async()=>{var t;if(!f.currentProject.disabled){const i=((t=S.value)==null?void 0:t.queryParams)||{};n.loading=!0,(await ee()).data.forEach(c=>a.alarmInfo.set(p(c.id.id),c.details));const s={start:K().subtract(1,"year").startOf("day").valueOf(),end:new Date().getTime(),page:n.pagination.page||1,size:n.pagination.limit||20,...i},e=await te(s,f.currentProject.id);n.loading=!1,j(e.data)}},j=t=>{const i=t.data,o=[];a.unconfirmed=0,a.unsolved=0;const s={day:"日",month:"月",year:"年"};for(const e of i){if(e.severityColor=a.severityColor[e.severity],(e.status==="CONFIRM_UNACK"||e.status==="ACTIVE_ACK")&&(e.confirm="未恢复 | 未确认",a.unconfirmed++,a.unsolved++,a.unconfirmedList.set(e.id.id,e.confirm)),e.status==="CONFIRM_ACK"&&(e.confirm="未恢复 | 已确认",a.unsolved++),e.status==="RESTORE_ACK"&&(e.confirm="已恢复 | 未确认",a.unconfirmed++,a.unconfirmedList.set(e.id.id,e.confirm)),e.alarmType=a.alarmTypes[e.type],e.details!==null){e.alarmRemarks=e.details.alarmRemarks;const l=a.alarmInfo.get(e.alarmJsonId);l?(e.alarmValue=l.attributeName+": "+l.alarmSetValue,e.recoverSet=l.recoverSetValue,e.alarmRemarks=l.alarmRemarks):(e.alarmValue="此条设置已删除",e.recoverSet="此条设置已删除",e.type==="offline"&&(e.alarmValue="-",e.recoverSet="-"))}if(e.alarmCycle?(e.cycleName=s[e.alarmCycle],e.recoverSet="-"):e.cycleName="",e.info=[],e.details){if(e.details.record)for(const l of e.details.record){const y={time:L(parseInt(l.ts),"YYYY-MM-DD HH:mm"),infoValue:l.info,status:l.status.toUpperCase()==="ALARM"?"触发报警":"恢复"};e.info.push(y)}e.activeRemarks=e.details.activeRemarks}const c=a.deviceName.get(e.originator.id)||"设备已删除";e.name=(e.alarmJsonName||"掉线 - ")+c,e.showDeviceN=c,a.listFilter?(a.filterItem==="unconfirmed"&&(e.status==="CONFIRM_UNACK"||e.status==="RESTORE_ACK")&&o.push(e),a.filterItem==="unsolved"&&(e.status==="CONFIRM_UNACK"||e.status==="CONFIRM_ACK")&&o.push(e)):o.push(e)}n.dataList=o,n.pagination.total=t.total},w=t=>{const i=t?"强制解除成功":"批量解除成功";D("确定解除告警, 是否继续?","解除提示").then(()=>{var s;let o=[];t?o=[p(t.id.id)]:o=((s=n.selectList)==null?void 0:s.map(e=>p(e.id.id)))||[],ae({alarmId:o}).then(()=>{C.success(i),m()})})},O=t=>{D("确定解除目标告警, 是否继续?","解除提示").then(()=>{var o;let i=[];{i=((o=n.selectList)==null?void 0:o.map(s=>p(s.id.id)))||[];for(const s of n.selectList||[])if(!a.unconfirmedList.get(s.id.id)){C.error("只能选择未确认数据，请重选");return}}ie({alarmId:i}).then(()=>{C.success("确认成功"),m()})})},V=t=>{var i;a.historyInfo.row=t,a.historyInfo.project=f.currentProject,a.historyInfo.visible=!0,(i=R.value)==null||i.openDrawer()},B=t=>{var i;a.detailsInfo.row=t,a.detailsInfo.visible=!0,(i=N.value)==null||i.openDrawer()},E=async()=>{const t=await oe("info");t.data&&t.data.length&&t.data.forEach(i=>a.deviceName.set(i.id.id,i.name))};return Y(()=>{E()}),(t,i)=>{const o=Z,s=X,e=Q,c=J,l=G,y=z;return q((g(),v(l,null,{tree:b(()=>[u(o,{"tree-data":r(f)},null,8,["tree-data"])]),default:b(()=>[u(s,{ref_key:"refSearch",ref:S,config:r(x)},null,8,["config"]),u(e,{config:r(n),class:"card-table"},null,8,["config"]),r(a).confirmInfo.visible?(g(),v(ne,{key:0,"dialog-info":r(a).confirmInfo,onRefresh:i[0]||(i[0]=ce=>m())},null,8,["dialog-info"])):T("",!0),u(c,{ref_key:"HistoryTableref",ref:R,config:r(M)},{default:b(()=>[r(a).historyInfo.visible?(g(),v(re,{key:0,"dialog-info":r(a).historyInfo,"device-name":r(a).deviceName},null,8,["dialog-info","device-name"])):T("",!0)]),_:1},8,["config"]),u(c,{ref_key:"InfoTableref",ref:N,config:r(F)},{default:b(()=>[r(a).detailsInfo.visible?(g(),v(se,{key:0,"dialog-info":r(a).detailsInfo,"device-name":r(a).deviceName},null,8,["dialog-info","device-name"])):T("",!0)]),_:1},8,["config"])]),_:1})),[[y,!!r(f).loading]])}}});export{Te as default};
