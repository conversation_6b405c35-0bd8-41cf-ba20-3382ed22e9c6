package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionArchive;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionArchivePageRequest;

@Mapper
public interface SoConstructionArchiveMapper extends BaseMapper<SoConstructionArchive> {
    IPage<SoConstructionArchive> findByPage(SoConstructionArchivePageRequest request);

    int save(SoConstructionArchive entity);

    @Override
    default int insert(SoConstructionArchive entity) {
        return save(entity);
    }

    boolean isComplete(@Param("code") String code, @Param("tenantId") String tenantId, @Param("processingStatus") SoGeneralTaskStatus processingStatus);

    boolean update(SoConstructionArchive entity);

    boolean updateFully(SoConstructionArchive entity);

    String getIdByConstructionCodeAndTenantId(@Param("constructionCode") String constructionCode, @Param("tenantId") String tenantId);


}
