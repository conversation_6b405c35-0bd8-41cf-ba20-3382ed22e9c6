/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.device;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import jdk.nashorn.internal.runtime.regexp.joni.encoding.ObjPtr;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.alarm.Alarm;
import org.thingsboard.server.common.data.alarm.AttrAlarmJson;
import org.thingsboard.server.common.data.constantsAttribute.DisplayObj;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.device.DeviceSearchQuery;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.*;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.common.data.relation.EntityRelation;
import org.thingsboard.server.common.data.relation.EntitySearchDirection;
import org.thingsboard.server.common.data.security.DeviceCredentials;
import org.thingsboard.server.common.data.security.DeviceCredentialsType;
import org.thingsboard.server.dao.alarm.AlarmJsonDao;
import org.thingsboard.server.dao.alarm.AlarmService;
import org.thingsboard.server.dao.attributes.AttributesDao;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.client.WaterPumpFeignClient;
import org.thingsboard.server.dao.client.WaterPumpRelationFeignClient;
import org.thingsboard.server.dao.customer.CustomerDao;
import org.thingsboard.server.dao.entity.AbstractEntityService;
import org.thingsboard.server.dao.entityview.EntityViewService;
import org.thingsboard.server.dao.exception.DataValidationException;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.model.sql.WaterPumpEntity;
import org.thingsboard.server.dao.model.sql.WaterPumpRelationEntity;
import org.thingsboard.server.dao.service.DataValidator;
import org.thingsboard.server.dao.service.PaginatedRemover;
import org.thingsboard.server.dao.tenant.TenantDao;
import org.thingsboard.server.dao.timeseries.TimeseriesService;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.CacheConstants.DEVICE_CACHE;
import static org.thingsboard.server.dao.DaoUtil.toUUIDs;
import static org.thingsboard.server.dao.model.ModelConstants.NULL_UUID;
import static org.thingsboard.server.dao.service.Validator.validateId;
import static org.thingsboard.server.dao.service.Validator.validateIds;
import static org.thingsboard.server.dao.service.Validator.validatePageLink;
import static org.thingsboard.server.dao.service.Validator.validateString;

@Service
@Slf4j
public class DeviceServiceImpl extends AbstractEntityService implements DeviceService {

    public static final String INCORRECT_TENANT_ID = "Incorrect tenantId ";
    public static final String INCORRECT_PAGE_LINK = "Incorrect page link ";
    public static final String INCORRECT_CUSTOMER_ID = "Incorrect customerId ";
    public static final String INCORRECT_DEVICE_ID = "Incorrect deviceId ";
    @Autowired
    private DeviceDao deviceDao;

    @Autowired
    private TenantDao tenantDao;

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private DeviceCredentialsService deviceCredentialsService;

    @Autowired
    private EntityViewService entityViewService;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private AttributesDao attributesDao;

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private AlarmJsonDao alarmJsonDao;

    @Autowired
    private TimeseriesService timeseriesService;

    @Autowired
    private WaterPumpFeignClient waterPumpFeignClient;

    @Autowired
    private WaterPumpRelationFeignClient waterPumpRelationFeignClient;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Value("${app.data-scala}")
    private Integer scala;

    @Override
    public Device findDeviceById(TenantId tenantId, DeviceId deviceId) {
        log.trace("Executing findDeviceById [{}]", deviceId);
        validateId(deviceId, INCORRECT_DEVICE_ID + deviceId);
        return deviceDao.findById(tenantId, deviceId.getId());
    }

    @Override
    //@Cacheable(cacheNames = DEVICE_CACHE, key = "{#deviceId}")
    public Device findDeviceById(DeviceId deviceId) {
        log.trace("Executing findDeviceById [{}]", deviceId);
        validateId(deviceId, INCORRECT_DEVICE_ID + deviceId);
        return deviceDao.findById(deviceId.getId());
    }

    @Override
    public ListenableFuture<Device> findDeviceByIdAsync(TenantId tenantId, DeviceId deviceId) {
        log.trace("Executing findDeviceById [{}]", deviceId);
        validateId(deviceId, INCORRECT_DEVICE_ID + deviceId);
        return deviceDao.findByIdAsync(tenantId, deviceId.getId());
    }

    @Override
    public ListenableFuture<Device> findDeviceByIdAsync(DeviceId deviceId) {
        return deviceDao.findDeviceByIdAsync(deviceId);
    }

    @Cacheable(cacheNames = DEVICE_CACHE, key = "{#tenantId, #name}")
    @Override
    public Device findDeviceByTenantIdAndName(TenantId tenantId, String name) {
        log.trace("Executing findDeviceByTenantIdAndName [{}][{}]", tenantId, name);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        Optional<Device> deviceOpt = deviceDao.findDeviceByTenantIdAndName(tenantId.getId(), name);
        return deviceOpt.orElse(null);
    }

    @Caching(evict = { @CacheEvict(cacheNames = DEVICE_CACHE, key = "{#device.tenantId, #device.name}")})
    @Override
    public Device saveDevice(Device device) {
        //device添加创建时间
        device.setCreatedTime(System.currentTimeMillis());
        log.trace("Executing saveDevice [{}]", device);
        deviceValidator.validate(device, Device::getTenantId);
        Device savedDevice;
        if (device.getId() == null) {
            savedDevice = deviceDao.save(device.getTenantId(), device);
            DeviceCredentials deviceCredentials = new DeviceCredentials();
            deviceCredentials.setDeviceId(new DeviceId(savedDevice.getUuidId()));
            deviceCredentials.setCredentialsType(DeviceCredentialsType.ACCESS_TOKEN);
            deviceCredentials.setCredentialsId(RandomStringUtils.randomAlphanumeric(20));
            deviceCredentialsService.createDeviceCredentials(device.getTenantId(), deviceCredentials);
            return savedDevice;
        } else {
            try {
                Device findDevice = deviceDao.findDeviceByIdAsync(device.getId()).get();
                return updateDevice(device, findDevice);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("查询到该设备的数据失败");
            }
        }
        return null;
    }

    private Device updateDevice(Device newDevice, Device oldDevice) {
        if (newDevice.getAdditionalInfo() != null) {
            oldDevice.setAdditionalInfo(newDevice.getAdditionalInfo());
        }
        if (newDevice.getType() != null) {
            oldDevice.setType(newDevice.getType());
        }
        if (newDevice.getIsDelete() != null) {
            oldDevice.setIsDelete(newDevice.getIsDelete());
        }
        if (newDevice.getTenantId() != null) {
            oldDevice.setTenantId(newDevice.getTenantId());
        }
        if (newDevice.getName() != null) {
            oldDevice.setName(newDevice.getName());
        }
        if (newDevice.getCustomerId() != null) {
            oldDevice.setCustomerId(newDevice.getCustomerId());
        }
        oldDevice.setGateWayId(newDevice.getGateWayId());
        deviceDao.save(oldDevice.getTenantId(), oldDevice);
        return oldDevice;
    }

    @Override
    public Device assignDeviceToCustomer(TenantId tenantId, DeviceId deviceId, CustomerId customerId) {
        Device device = findDeviceById(tenantId, deviceId);
        device.setCustomerId(customerId);
        return saveDevice(device);
    }

    @Override
    public Device unassignDeviceFromCustomer(TenantId tenantId, DeviceId deviceId) {
        Device device = findDeviceById(tenantId, deviceId);
        device.setCustomerId(null);
        return saveDevice(device);
    }

    @Override
    @Caching(evict = {@CacheEvict(cacheNames = DEVICE_CACHE, key = "{#device.tenantId, #device.name}"),
            @CacheEvict(cacheNames = DEVICE_CACHE, key = "{#deviceId}")})
    public void deleteDevice(TenantId tenantId, DeviceId deviceId) {
        log.trace("Executing deleteDevice [{}]", deviceId);
        Cache cache = cacheManager.getCache(DEVICE_CACHE);
        validateId(deviceId, INCORRECT_DEVICE_ID + deviceId);
        DeviceCredentials deviceCredentials = deviceCredentialsService.findDeviceCredentialsByDeviceId(tenantId, deviceId);
        if (deviceCredentials != null) {
            deviceCredentialsService.deleteDeviceCredentials(tenantId, deviceCredentials);
        }
        deleteEntityRelations(tenantId, deviceId);
        Device device = deviceDao.findById(deviceId.getId());
        List<Object> list = new ArrayList<>();
        list.add(device.getTenantId());
        list.add(device.getName());
        cache.evict(list);
        // 修改删除标记
        device.setIsDelete(DataConstants.IS_DELETE_YES);
        // 设置设备名字为ID
        device.setName(device.getId().getId().toString());
        saveDevice(device);
    }

    @Override
    public TextPageData<Device> findDevicesByTenantId(TenantId tenantId, TextPageLink pageLink) {
        log.trace("Executing findDevicesByTenantId, tenantId [{}], pageLink [{}]", tenantId, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validatePageLink(pageLink, INCORRECT_PAGE_LINK + pageLink);
        //List<Device> devices = deviceDao.findDevicesByTenantId(tenantId.getId(), pageLink);
        List<Device> devices = deviceDao.findAllByTenantId(tenantId);
        devices = devices.stream()
                .filter(d -> d.getIsDelete().equals(DataConstants.IS_DELETE_NO)
                        && !d.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME)
                        && !d.getType().equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS)
                        && !d.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_DTU))
                .collect(Collectors.toList());

        return new TextPageData<>(getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)), pageLink);
    }

    @Override
    public TextPageData<Device> findDevicesByTenantIdAndType(TenantId tenantId, String type, TextPageLink pageLink) {
        log.trace("Executing findDevicesByTenantIdAndType, tenantId [{}], type [{}], pageLink [{}]", tenantId, type, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateString(type, "Incorrect type " + type);
        validatePageLink(pageLink, INCORRECT_PAGE_LINK + pageLink);
        List<Device> devices = deviceDao.findDevicesByTenantIdAndType(tenantId.getId(), type, pageLink);
        devices = devices.stream()
                .filter(d -> d.getIsDelete().equals(DataConstants.IS_DELETE_NO))
                .collect(Collectors.toList());
        // 若不是查询网关设备，过滤掉网关设备再返回给前端
        if (!type.equalsIgnoreCase(DataConstants.GATEWAY_NAME)) {
            devices = devices.stream()
                    .filter(d -> !d.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME)
                            && !d.getType().equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS)
                            && !d.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_DTU))
                    .collect(Collectors.toList());
        }
        return new TextPageData<>(devices, pageLink);
    }

    @Override
    public ListenableFuture<List<Device>> findDevicesByTenantIdAndIdsAsync(TenantId tenantId, List<DeviceId> deviceIds) {
        log.trace("Executing findDevicesByTenantIdAndIdsAsync, tenantId [{}], deviceIds [{}]", tenantId, deviceIds);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateIds(deviceIds, "Incorrect deviceIds " + deviceIds);
        return deviceDao.findDevicesByTenantIdAndIdsAsync(tenantId.getId(), toUUIDs(deviceIds));
    }


    @Override
    public void deleteDevicesByTenantId(TenantId tenantId) {
        log.trace("Executing deleteDevicesByTenantId, tenantId [{}]", tenantId);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        tenantDevicesRemover.removeEntities(tenantId, tenantId);
    }

    @Override
    public TextPageData<Device> findDevicesByTenantIdAndCustomerId(TenantId tenantId, CustomerId customerId, TextPageLink pageLink) {
        log.trace("Executing findDevicesByTenantIdAndCustomerId, tenantId [{}], customerId [{}], pageLink [{}]", tenantId, customerId, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateId(customerId, INCORRECT_CUSTOMER_ID + customerId);
        validatePageLink(pageLink, INCORRECT_PAGE_LINK + pageLink);
        List<Device> devices = deviceDao.findDevicesByTenantIdAndCustomerId(tenantId.getId(), customerId.getId(), pageLink);
        return new TextPageData<>(devices, pageLink);
    }

    @Override
    public TextPageData<Device> findDevicesByTenantIdAndCustomerIdAndType(TenantId tenantId, CustomerId customerId, String type, TextPageLink pageLink) {
        log.trace("Executing findDevicesByTenantIdAndCustomerIdAndType, tenantId [{}], customerId [{}], type [{}], pageLink [{}]", tenantId, customerId, type, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateId(customerId, INCORRECT_CUSTOMER_ID + customerId);
        validateString(type, "Incorrect type " + type);
        validatePageLink(pageLink, INCORRECT_PAGE_LINK + pageLink);
        List<Device> devices = deviceDao.findDevicesByTenantIdAndCustomerIdAndType(tenantId.getId(), customerId.getId(), type, pageLink);
        return new TextPageData<>(devices, pageLink);
    }

    @Override
    public ListenableFuture<List<Device>> findDevicesByTenantIdCustomerIdAndIdsAsync(TenantId tenantId, CustomerId customerId, List<DeviceId> deviceIds) {
        log.trace("Executing findDevicesByTenantIdCustomerIdAndIdsAsync, tenantId [{}], customerId [{}], deviceIds [{}]", tenantId, customerId, deviceIds);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateId(customerId, INCORRECT_CUSTOMER_ID + customerId);
        validateIds(deviceIds, "Incorrect deviceIds " + deviceIds);
        return deviceDao.findDevicesByTenantIdCustomerIdAndIdsAsync(tenantId.getId(),
                customerId.getId(), toUUIDs(deviceIds));
    }

    @Override
    public void unassignCustomerDevices(TenantId tenantId, CustomerId customerId) {
        log.trace("Executing unassignCustomerDevices, tenantId [{}], customerId [{}]", tenantId, customerId);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateId(customerId, INCORRECT_CUSTOMER_ID + customerId);
        customerDeviceUnasigner.removeEntities(tenantId, customerId);
    }

    @Override
    public ListenableFuture<List<Device>> findDevicesByQuery(TenantId tenantId, DeviceSearchQuery query) {
        ListenableFuture<List<EntityRelation>> relations = relationService.findByQuery(tenantId, query.toEntitySearchQuery());
        ListenableFuture<List<Device>> devices = Futures.transformAsync(relations, r -> {
            EntitySearchDirection direction = query.toEntitySearchQuery().getParameters().getDirection();
            List<ListenableFuture<Device>> futures = new ArrayList<>();
            for (EntityRelation relation : r) {
                EntityId entityId = direction == EntitySearchDirection.FROM ? relation.getTo() : relation.getFrom();
                if (entityId.getEntityType() == EntityType.DEVICE) {
                    futures.add(findDeviceByIdAsync(tenantId, new DeviceId(entityId.getId())));
                }
            }
            return Futures.successfulAsList(futures);
        });

        devices = Futures.transform(devices, new Function<List<Device>, List<Device>>() {
            @Nullable
            @Override
            public List<Device> apply(@Nullable List<Device> deviceList) {
                return deviceList == null ? Collections.emptyList() : deviceList.stream().filter(device -> query.getDeviceTypes().contains(device.getType())).collect(Collectors.toList());
            }
        });

        return devices;
    }

    @Override
    public List<Device> findAllByTenantId(TenantId tenantId, String key) {
        List<Device> devices = deviceDao.findAllByTenantId(tenantId, key).stream()
                .filter(device -> !device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_PORT)
                        && !device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_DTU)
                        && !device.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME)
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }


    @Override
    public List<Device> findAllByTenantId(TenantId tenantId) {
        List<Device> devices = deviceDao.findAllByTenantId(tenantId).stream()
                .filter(device -> !device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_PORT)
                        && !device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_DTU)
                        && !device.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME)
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    /**
     * 根据tenantId获取gateWay列表，兼容之前默认网关类型为gateWay，新的网关类型为PORT-串口型网关，DTU-DTU型网关
     * todo 前端需要一次性获取最后一次上线时间？
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<Device> findGateWayByTenantId(TenantId tenantId) {
        List<Device> devices = deviceDao.findAllByTenantId(tenantId)
                .stream()
                .filter(device -> (device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_PORT)
                        || device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_DTU)
                        || device.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)
                        && device.getGateWayId() == null).collect(Collectors.toList());
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    /**
     * 获取gateWay下面挂载的设备
     *
     * @param gateWayId
     * @return
     */
    @Override
    public List<Device> findDeviceByGateWayId(DeviceId gateWayId) {
        List<Device> devices = deviceDao.findByGateway(gateWayId).stream().filter(device -> device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_CONNECT_TIME));
    }

    @Override
    public ListenableFuture<List<EntitySubtype>> findDeviceTypesByTenantId(TenantId tenantId) {
        log.trace("Executing findDeviceTypesByTenantId, tenantId [{}]", tenantId);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        ListenableFuture<List<EntitySubtype>> tenantDeviceTypes = deviceDao.findTenantDeviceTypesAsync(tenantId.getId());
        return Futures.transform(tenantDeviceTypes,
                deviceTypes -> {
                    deviceTypes.sort(Comparator.comparing(EntitySubtype::getType));
                    return deviceTypes;
                });
    }

    private DataValidator<Device> deviceValidator =
            new DataValidator<Device>() {

                @Override
                protected void validateCreate(TenantId tenantId, Device device) {
                    deviceDao.findDeviceByTenantIdAndName(device.getTenantId().getId(), device.getName()).ifPresent(
                            d -> {
                                throw new DataValidationException("Device with such name already exists!");
                            }
                    );
                }

                @Override
                protected void validateUpdate(TenantId tenantId, Device device) {
                    deviceDao.findDeviceByTenantIdAndName(device.getTenantId().getId(), device.getName()).ifPresent(
                            d -> {
                                if (!d.getUuidId().equals(device.getUuidId())) {
                                    throw new DataValidationException("Device with such name already exists!");
                                }
                            }
                    );
                }

                @Override
                protected void validateDataImpl(TenantId tenantId, Device device) {
                    if (StringUtils.isEmpty(device.getType())) {
                        throw new DataValidationException("Device type should be specified!");
                    }
                    if (StringUtils.isEmpty(device.getName())) {
                        throw new DataValidationException("Device name should be specified!");
                    }
                    if (device.getTenantId() == null) {
                        throw new DataValidationException("Device should be assigned to tenant!");
                    } else {
                        Tenant tenant = tenantDao.findById(device.getTenantId(), device.getTenantId().getId());
                        if (tenant == null) {
                            throw new DataValidationException("Device is referencing to non-existent tenant!");
                        }
                    }
                    if (device.getCustomerId() == null) {
                        device.setCustomerId(new CustomerId(NULL_UUID));
                    } else if (!device.getCustomerId().getId().equals(NULL_UUID)) {
                        Customer customer = customerDao.findById(device.getTenantId(), device.getCustomerId().getId());
                        if (customer == null) {
                            throw new DataValidationException("Can't assign device to non-existent customer!");
                        }
                        if (!customer.getTenantId().getId().equals(device.getTenantId().getId())) {
                            throw new DataValidationException("Can't assign device to customer from different tenant!");
                        }
                    }
                }
            };

    private PaginatedRemover<TenantId, Device> tenantDevicesRemover =
            new PaginatedRemover<TenantId, Device>() {

                @Override
                protected List<Device> findEntities(TenantId tenantId, TenantId id, TextPageLink pageLink) {
                    return deviceDao.findDevicesByTenantId(id.getId(), pageLink);
                }

                @Override
                protected void removeEntity(TenantId tenantId, Device entity) {
                    deleteDevice(tenantId, new DeviceId(entity.getUuidId()));
                }
            };

    private PaginatedRemover<CustomerId, Device> customerDeviceUnasigner = new PaginatedRemover<CustomerId, Device>() {

        @Override
        protected List<Device> findEntities(TenantId tenantId, CustomerId id, TextPageLink pageLink) {
            return deviceDao.findDevicesByTenantIdAndCustomerId(tenantId.getId(), id.getId(), pageLink);
        }

        @Override
        protected void removeEntity(TenantId tenantId, Device entity) {
            unassignDeviceFromCustomer(tenantId, new DeviceId(entity.getUuidId()));
        }
    };

    @Override
    public void updateDeviceOnline(DeviceId deviceId) {
        Device device = deviceDao.findById(deviceId.getId());
        if (device.getGateWayId() != null) {
            attributesDao.save(device.getGateWayId(), DataConstants.SERVER_SCOPE, new BaseAttributeKvEntry(new LongDataEntry(ModelConstants.LAST_UPDATE_TIME, System.currentTimeMillis()), System.currentTimeMillis()));
        }
        attributesDao.save(deviceId, DataConstants.SERVER_SCOPE, new BaseAttributeKvEntry(new LongDataEntry(ModelConstants.LAST_UPDATE_TIME, System.currentTimeMillis()), System.currentTimeMillis()));
    }

    @Override
    public Device findDeviceByAdditionalInfo(String gatewayId) {
        StringBuilder builder = new StringBuilder();
        builder.append("{").append("\"description\":\"").append(gatewayId).append("\"}");
        List<Device> deviceList = deviceDao.findDeviceByAdditionalInfo(builder.toString());

        if (deviceList.size() == 1) {
            return deviceList.get(0);
        }

        return null;
    }

    @Override
    public List<DeviceFullData> getDeviceFullData(TenantId tenantId, DeviceId deviceId) {
        return null;
    }


    /**
     * 获取设备的在线状态
     *
     * @return
     */
    public List<Device> getDeviceStatus(List<Device> devices, List<AttributeKeyKvEntry> attrs) {
        try {
            //获取网关的最后一次连接时间
            Map<String, Long> map = new HashMap<>();
            attrs.forEach(at -> {
                if (at.getValueAsString() != null)
                    map.put(at.getAttributeBaseKey().getEntityId(), Long.valueOf(at.getValueAsString()));
            });
            for (Device d : devices) {
                if (map.containsKey(UUIDConverter.fromTimeUUID(d.getId().getId()))) {
                    d.setStatus(System.currentTimeMillis() - map.get(UUIDConverter.fromTimeUUID(d.getId().getId())) > (1000 * 60 * 60 * 24) ? false : true);
                } else {
                    d.setStatus(false);
                }
            }
            return devices;
        } catch (Exception e) {
            e.printStackTrace();
            return devices;
        }
    }

    @Override
    public List<DeviceFullData> getDeviceFullData(TenantId tenantId, DeviceId deviceId, String group) {
        List<DeviceFullData> result = new ArrayList<>();
        try {
            AttributeKvEntry attributeKvEntry = attributesDao.find(deviceId, DataConstants.SHARED_SCOPE, DataConstants.ATTRIBUTE_PROP).get();
            if (attributeKvEntry!=null) {
                List<PropAttribute> props = new ObjectMapper().readValue(attributeKvEntry.getValueAsString(), new TypeReference<List<PropAttribute>>() {});
                if (!StringUtils.isEmpty(group)) {
                    props = props.stream().filter(prop -> group.equals(prop.getGroup())).collect(Collectors.toList());
                }

                //最后一次数据
                List<TsKvEntry> tsKvEntries = timeseriesService.findAllLatest(tenantId, deviceId).get();
                Map<String, TsKvEntry> tsKvEntryMap = new HashMap<>();
                tsKvEntries.forEach(tsKvEntry -> {
                    tsKvEntryMap.put(tsKvEntry.getKey(), tsKvEntry);
                });
                //设备的报警信息
                List<AttrAlarmJson> attrAlarmJsons = alarmJsonDao.findByDevice(deviceId).get();
                Map<String, AttrAlarmJson> attrAlarmJsonMap = new HashMap<>();
                attrAlarmJsons.forEach(attrAlarmJson -> {
                    attrAlarmJsonMap.put(attrAlarmJson.getAttribute(),attrAlarmJson);
                });
                Map<String, Alarm> alarmMap = alarmService.findLastAllAlarm(deviceId);
                props.forEach(propAttribute -> {
//                    if (propAttribute.getPropertyType().equals("1")) {
                        DeviceFullData deviceFullData = new DeviceFullData();
                        deviceFullData.setProperty(propAttribute.getPropertyCategory());
                        deviceFullData.setPropertyName(propAttribute.getName());
                        deviceFullData.setUnit(propAttribute.getUnit());
                        deviceFullData.setPropertyType(propAttribute.getPropertyType());
                        deviceFullData.setEntityType("String");
                        deviceFullData.setSerialNumber(propAttribute.getSerialNumber());
                        deviceFullData.setPointAddress(propAttribute.getPointAddress());
                        deviceFullData.setGroup(propAttribute.getGroup());
                        //获取最后一次数据
                        if (tsKvEntryMap.containsKey(propAttribute.getPropertyCategory())) {
                            String valueAsString = tsKvEntryMap.get(propAttribute.getPropertyCategory()).getValueAsString();
                            if (valueAsString != null && valueAsString.trim().length() > 0) {
                                deviceFullData.setValue(new BigDecimal(tsKvEntryMap.get(propAttribute.getPropertyCategory()).getValueAsString()).setScale(scala, BigDecimal.ROUND_HALF_EVEN).toPlainString());
                            }
                            deviceFullData.setCollectionTime(tsKvEntryMap.get(propAttribute.getPropertyCategory()).getTs());
                            if (System.currentTimeMillis() - tsKvEntryMap.get(propAttribute.getPropertyCategory()).getTs() > (1000 * 60 * 60 * 48)) {
                                deviceFullData.setStatus(false);
                            } else {
                                deviceFullData.setStatus(true);
                            }
                            // 数据展示处理
                            if (deviceFullData.getValue() != null) {
                                try {
                                    BigDecimal data = new BigDecimal(deviceFullData.getValue());
                                    List<DisplayObj> displayList = propAttribute.getDisplay();
                                    if (displayList != null) {
                                        for (DisplayObj display : displayList) {
                                            BigDecimal displayData = new BigDecimal(display.getValue()).setScale(scala, BigDecimal.ROUND_HALF_EVEN);
                                            if (displayData.compareTo(data) == 0) {
                                                deviceFullData.setValue(display.getLabel());
                                                break;
                                            }
                                        }
                                    }

                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }

                        }
                        //获取报警数据
                        if (attrAlarmJsonMap.containsKey(propAttribute.getPropertyCategory())) {
                            AttrAlarmJson attrAlarmJson = attrAlarmJsonMap.get(propAttribute.getPropertyCategory());
                            if (alarmMap.containsKey(UUIDConverter.fromTimeUUID(attrAlarmJson.getUuidId()))) {
                                Alarm alarm = alarmMap.get(UUIDConverter.fromTimeUUID(attrAlarmJson.getUuidId()));
                                deviceFullData.setAlarmStatus(alarm.getStatus().name());
                                deviceFullData.setAlarmValue(alarm.getValue());
                                deviceFullData.setAlarmTerm(attrAlarmJson.getDetails().get("alarmSetValue").asText());
                                deviceFullData.setAlarmTime(alarm.getStartTs());
                                deviceFullData.setAlarmLevel(alarm.getSeverity());
                            }
                            if ("scope".equals(attrAlarmJson.getAlarmType())) {
                                JSONObject params = JSON.parseObject(attrAlarmJson.getParams());
                                BigDecimal alarmMaxValue = params.getBigDecimal("alarmMaxValue");
                                BigDecimal alarmMinValue = params.getBigDecimal("alarmMinValue");
                                if (alarmMinValue != null) {
                                    deviceFullData.setMaxValue(alarmMaxValue.doubleValue());
                                }
                                if (alarmMinValue != null) {
                                    deviceFullData.setMinValue(alarmMinValue.doubleValue());
                                }
                            }
                        }
                        result.add(deviceFullData);
//                    }
                });
            }
        } catch(Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public List<Device> findAllDeviceByTenantId(TenantId tenantId) {
        return deviceDao.findAllByTenantId(tenantId).stream().filter(device -> device.getGateWayId() != null).collect(Collectors.toList());
    }


    public List<Device> getCloudDeviceList(TenantId tenantId) {
        List<Device> deviceList = this.findAllByTenantId(tenantId);

        // 筛选出从机
        deviceList = deviceList.stream().filter(device -> {
            if (device.getGateWayId() != null) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        return deviceList;
    }

    @Override
    public List<Device> findByForeignKeyIn(List<String> meterCodeList) {
        return deviceDao.findByForeignKeyIn(meterCodeList);
    }

    @Override
    public List<Device> findByDeviceTypeName(String deviceTypeName, TenantId tenantId) {
        return deviceDao.findByDeviceTypeName(deviceTypeName, tenantId);
    }

    @Override
    public List<Object> findWaterPumpMonitorData(String type, TenantId tenantId) throws ThingsboardException {
        // 查询水泵列表
        List<WaterPumpEntity> waterPumpList = waterPumpFeignClient.findAll();
        if (waterPumpList == null || waterPumpList.isEmpty()) {
            return new ArrayList<>();
        }

        // 查询每个水泵关联的设备数据
        List<WaterPumpRelationEntity> waterPumpRelationList = null;
        if (StringUtils.isEmpty(type)) {
            waterPumpRelationList = waterPumpRelationFeignClient.findAll();
        } else {
            waterPumpRelationList = waterPumpRelationFeignClient.findByType(type);
        }
        // 按水泵对数据进行分组
        Map<String, List<WaterPumpRelationEntity>> waterPumpRelationMap = new HashMap<>();
        Map<String, List<DeviceFullData>> deviceFullDataMap = new HashMap<>();
        if (waterPumpRelationList != null) {
            for (WaterPumpRelationEntity waterPumpRelationEntity : waterPumpRelationList) {
                List<WaterPumpRelationEntity> list = new ArrayList<>();
                if (waterPumpRelationMap.containsKey(waterPumpRelationEntity.getWaterPumpId())) {
                    list = waterPumpRelationMap.get(waterPumpRelationEntity.getWaterPumpId());
                }

                list.add(waterPumpRelationEntity);
                waterPumpRelationMap.put(waterPumpRelationEntity.getWaterPumpId(), list);
            }

            // 查询所涉及到的设备数据
            List<String> deviceIdList = waterPumpRelationList.stream().map(WaterPumpRelationEntity::getDeviceId).collect(Collectors.toList());
            for (String deviceId : deviceIdList) {
                List<DeviceFullData> deviceFullData = this.getDeviceFullData(tenantId, new DeviceId(UUIDConverter.fromString(deviceId)), null);
                deviceFullDataMap.put(deviceId, deviceFullData);
            }
        }

        // 按水泵关联关系拼装数据返回
        List<Object> result = new ArrayList<>();
        for (WaterPumpEntity waterPump : waterPumpList) {
            List<WaterPumpRelationEntity> waterPumpRelations = waterPumpRelationMap.get(waterPump.getId());
            JSONObject obj = new JSONObject();
            obj.put("id", waterPump.getId());
            obj.put("name", waterPump.getName());
            // 拼装数据
            if (waterPumpRelations != null) {
                for (WaterPumpRelationEntity waterPumpRelation : waterPumpRelations) {
                    String deviceId = waterPumpRelation.getDeviceId();
                    String attr = waterPumpRelation.getAttr();
                    List<DeviceFullData> deviceFullData = deviceFullDataMap.get(deviceId);
                    if (deviceFullData != null && deviceFullData.size() > 0) {
                        for (DeviceFullData deviceFullDatum : deviceFullData) {
                            if (attr.equals(deviceFullDatum.getProperty())) {
                                obj.put(deviceId + "." + deviceFullDatum.getProperty(), deviceFullDatum.getValue());
                            }
                        }
                    }
                }
            }
            result.add(obj);
        }

        return result;
    }

    @Override
    public List<JSONObject> findStationDataListByStationType(String stationType, TenantId tenantId) {
        /*
         * 查询指定类型站点的最新数据
         * 1. 查询指定类型的站点列表
         * 2. 查询每个站点的关联变量
         * 3. 查询站点涉及到的设备的数据
         * 4. 用查询到的数据构建站点的数据列表
         */

        // 查询指定类型的站点列表
        PageData<StationEntity> pageData = stationFeignClient.list(1, 999999, stationType, "");
        if (pageData.getData() == null || pageData.getData().isEmpty()) {
            return new ArrayList<>();
        }
        List<StationEntity> data = pageData.getData();
        List<String> stationIdList = data.stream().map(StationEntity::getId).collect(Collectors.toList());
        List<StationAttrEntity> attrList = stationFeignClient.getAttrList(stationIdList, "监测量");
        // 将获取到的站点变量按站点ID进行分组
        Map<String, List<StationAttrEntity>> stationAttrMap = new HashMap<>();
        // 将查询到的设备数据按设备以及变量名进行分组
        Map<String, Map<String, DeviceFullData>> deviceDataMap = new HashMap<>();
        if (attrList != null && attrList.size() > 0) {
            for (StationAttrEntity stationAttr : attrList) {
                String stationId = stationAttr.getStationId();
                List<StationAttrEntity> stationAttrList = new ArrayList<>();
                if (stationAttrMap.containsKey(stationId)) {
                    stationAttrList = stationAttrMap.get(stationId);
                }
                stationAttrList.add(stationAttr);

                stationAttrMap.put(stationId, stationAttrList);
            }
            // 查询涉及到的设备数据
            List<String> deviceIdList = attrList.stream().map(StationAttrEntity::getDeviceId).distinct().collect(Collectors.toList());
            // 对数据进行处理(按设备ID以及变量名分组, 方便后续查询)
            for (String deviceId : deviceIdList) {
                List<DeviceFullData> deviceFullData = this.getDeviceFullData(tenantId, new DeviceId(UUIDConverter.fromString(deviceId)), "");
                if (deviceFullData != null && deviceFullData.size() > 0) {
                    Map<String, DeviceFullData> dataMap = new HashMap<>();
                    for (DeviceFullData deviceFullDatum : deviceFullData) {
                        String property = deviceFullDatum.getProperty();
                        dataMap.put(property, deviceFullDatum);
                    }
                    deviceDataMap.put(deviceId, dataMap);
                }
            }
        }

        // 拼装数据
        List<JSONObject> resultList = new ArrayList<>();
        for (StationEntity station : data) {
            String id = station.getId();
            String name = station.getName();
            String type = station.getType();
            JSONObject obj = new JSONObject();
            obj.put("id", id);
            obj.put("name", name);
            obj.put("type", type);

            List<StationAttrEntity> stationAttrList = stationAttrMap.get(id);
            if (stationAttrList != null && stationAttrList.size() > 0) {
                for (StationAttrEntity stationAttr : stationAttrList) {
                    String deviceId = stationAttr.getDeviceId();
                    String attr = stationAttr.getAttr();

                    Map<String, DeviceFullData> fullDataMap = deviceDataMap.get(deviceId);
                    if (fullDataMap != null) {
                        DeviceFullData deviceFullData = fullDataMap.get(attr);
                        if (deviceFullData != null) {
                            String property = deviceFullData.getProperty();
                            obj.put(property, deviceFullData.getValue());
                            long collectionTime = deviceFullData.getCollectionTime();
                            obj.put("time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(collectionTime)));
                            if (!deviceFullData.isStatus()) {
                                obj.put(property + "_status", "offline");
                            } else {
                                obj.put(property + "_status", deviceFullData.getAlarmLevel());
                            }
                        }
                    }
                    obj.put(attr + "_deviceId", deviceId);
                }
            }

            resultList.add(obj);
        }

        return resultList;
    }
}
