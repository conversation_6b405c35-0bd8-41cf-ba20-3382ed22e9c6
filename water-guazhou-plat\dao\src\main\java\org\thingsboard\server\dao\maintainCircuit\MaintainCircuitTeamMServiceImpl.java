package org.thingsboard.server.dao.maintainCircuit;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.maintainCircuit.MaintainCircuitTeamC;
import org.thingsboard.server.dao.model.sql.maintainCircuit.MaintainCircuitTeamM;
import org.thingsboard.server.dao.sql.maintainCircuit.MaintainCircuitTeamCMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.MaintainCircuitTeamMMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class MaintainCircuitTeamMServiceImpl implements MaintainCircuitTeamMService {
    @Autowired
    private MaintainCircuitTeamMMapper maintainCircuitTeamMMapper;

    @Autowired
    private MaintainCircuitTeamCMapper maintainCircuitTeamCMapper;

    @Override
    public PageData getList(String name, String type, int page, int size, String tenantId) {
        List<MaintainCircuitTeamM> maintainCircuitTeamMList = maintainCircuitTeamMMapper.getList(name, type, page, size, tenantId);
        // 获取成员
        List<MaintainCircuitTeamC> maintainCircuitTeamCList;
        for (MaintainCircuitTeamM maintainCircuitTeamM : maintainCircuitTeamMList) {
            maintainCircuitTeamCList = maintainCircuitTeamCMapper.getListByMainId(maintainCircuitTeamM.getId());
            maintainCircuitTeamM.setMaintainCircuitTeamCList(maintainCircuitTeamCList);
        }
        int total = maintainCircuitTeamMMapper.getListCount(name, type, tenantId);

        return new PageData(total, maintainCircuitTeamMList);

    }

    @Override
    @Transactional
    public MaintainCircuitTeamM save(MaintainCircuitTeamM maintainCircuitTeamM) {

        if (StringUtils.isBlank(maintainCircuitTeamM.getId())) {
            maintainCircuitTeamM.setCreateTime(new Date());
            maintainCircuitTeamMMapper.insert(maintainCircuitTeamM);
        } else {
            maintainCircuitTeamMMapper.updateById(maintainCircuitTeamM);
        }


        Map deleteMap = new HashMap();
        deleteMap.put("main_id", maintainCircuitTeamM.getId());
        maintainCircuitTeamCMapper.deleteByMap(deleteMap);
        if (maintainCircuitTeamM.getMaintainCircuitTeamCList() != null) {
            for (MaintainCircuitTeamC maintainCircuitTeamC : maintainCircuitTeamM.getMaintainCircuitTeamCList()) {
                maintainCircuitTeamC.setMainId(maintainCircuitTeamM.getId());
                maintainCircuitTeamC.setTenantId(maintainCircuitTeamM.getTenantId());
                maintainCircuitTeamC.setCreateTime(new Date());

                maintainCircuitTeamCMapper.insert(maintainCircuitTeamC);
            }
        }

        return maintainCircuitTeamM;
    }

    @Override
    @Transactional
    public IstarResponse delete(List<String> ids) {
        maintainCircuitTeamMMapper.deleteBatchIds(ids);
        QueryWrapper<MaintainCircuitTeamC> wrapper = new QueryWrapper<>();
        wrapper.in("main_id", ids);
        maintainCircuitTeamCMapper.delete(wrapper);
        return IstarResponse.ok("删除成功");
    }
}
