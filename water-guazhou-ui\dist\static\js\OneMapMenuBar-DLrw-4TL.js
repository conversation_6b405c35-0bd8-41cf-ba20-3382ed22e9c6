import{d as b,r as y,a8 as g,am as x,o as A,g as o,n as c,i as s,q as h,F as _,aB as l,aJ as w,h as f,p as u,bh as M,an as i,cs as k,aw as B,cK as P,cL as S,cM as D,C as L}from"./index-r0dFAfgr.js";const T={class:"onemap-menu-bar"},V={key:0,class:"bar-item"},q={class:"dropdown-content"},z={class:"dropdown-title"},J={key:1,class:"divider"},K=["onClick"],j={class:"title"},H=b({__name:"OneMapMenuBar",props:{menus:{}},emits:["change","item-click"],setup(v,{emit:E}){const r=v,n=y({current:void 0,activeMenuItem:void 0,ONE_MAP_CONFIG:{...window.SITE_CONFIG.ONE_MAP_CONFIG,hiddenMenus:window.SITE_CONFIG.ONE_MAP_CONFIG.hiddenMenus||[],hiddenSubMenus:window.SITE_CONFIG.ONE_MAP_CONFIG.hiddenSubMenus||[]}}),O=g(()=>r.menus.filter(e=>n.ONE_MAP_CONFIG.hiddenMenus.indexOf(e.path)===-1).length),I=E,p=e=>{var d;if(!e)return;const a=r.menus.find(m=>m.path===e);n.current=a,n.activeMenuItem=(d=a==null?void 0:a.children)==null?void 0:d[0],I("change",a)},F=e=>{e&&(n.activeMenuItem=e,I("item-click",e))};return x(()=>r.menus,()=>{var e,a;n.current=(e=r.menus)==null?void 0:e[0],p((a=n.current)==null?void 0:a.path)}),A(()=>{var e,a;n.current=(e=r.menus)==null?void 0:e[0],p((a=n.current)==null?void 0:a.path)}),(e,a)=>{var C;const d=P,m=S,G=D;return o(),c("div",T,[s(O)>1?(o(),c("div",V,[h(G,{onCommand:p},{dropdown:_(()=>[h(m,null,{default:_(()=>[(o(!0),c(l,null,w(r.menus,t=>(o(),c(l,{key:t.path},[s(n).ONE_MAP_CONFIG.hiddenMenus.indexOf(t.path)===-1?(o(),f(d,{key:0,command:t.path},{default:_(()=>[u("span",null,M(t.meta.title),1)]),_:2},1032,["command"])):i("",!0)],64))),128))]),_:1})]),default:_(()=>{var t;return[u("div",q,[u("span",z,M(((t=s(n).current)==null?void 0:t.meta.title)||"暂无菜单"),1),h(s(k),{icon:"ep:caret-bottom"})])]}),_:1})])):i("",!0),s(O)>1?(o(),c("div",J)):i("",!0),(o(!0),c(l,null,w((C=s(n).current)==null?void 0:C.children,t=>{var N;return o(),c(l,{key:t.path},[s(n).ONE_MAP_CONFIG.hiddenSubMenus.indexOf(t.path)===-1?(o(),c("div",{key:0,class:B(["bar-item",[((N=s(n).activeMenuItem)==null?void 0:N.path)===t.path?"is-active":""]]),onClick:Q=>F(t)},[t.meta.icon?(o(),f(s(k),{key:0,icon:t.meta.icon},null,8,["icon"])):i("",!0),u("span",j,M(t.meta.title),1)],10,K)):i("",!0)],64)}),128))])}}}),U=L(H,[["__scopeId","data-v-80681f50"]]);export{U as default};
