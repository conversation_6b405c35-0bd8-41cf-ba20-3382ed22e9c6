<template>
  <div class="wrapper overlay-y">
    <SLCard
      v-if="!currentSYS && shortMessage"
      title="短信配置"
    >
      <ShortMessage></ShortMessage>
    </SLCard>
    <SLCard
      v-if="currentSYS"
      title="邮箱配置"
    >
      <EmailSetting></EmailSetting>
    </SLCard>
    <SLCard
      v-if="!currentSYS"
      title="企业配置"
    >
      <EnterpriseSetting></EnterpriseSetting>
    </SLCard>
    <SLCard
      v-if="showAppSetting"
      title="APP配置"
    >
      <AppSetting></AppSetting>
    </SLCard>
    <SLCard
      v-if="false"
      title="大屏配置"
    >
      <BigScreenSetting></BigScreenSetting>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store'
import ShortMessage from './components/ShortMessage.vue'
import EmailSetting from './components/EmailSetting.vue'
import EnterpriseSetting from './components/EnterpriseSetting.vue'
import BigScreenSetting from './components/BigScreenSetting.vue'
import AppSetting from './components/AppSetting.vue'

const currentSYS = ref<boolean>(true)
const showAppSetting = ref<boolean>(
  window.SITE_CONFIG.LOGIN_CONFIG.SHOWFOOTER && window.SITE_CONFIG.LOGIN_CONFIG.SHOWQRCODE
)
const shortMessage = ref<boolean>(window.SITE_CONFIG.SHORTMESSAGE ?? true)
onMounted(() => {
  currentSYS.value = useUserStore().roles[0] === 'SYS_ADMIN'
})
</script>
<style lang="scss" scoped>
.sl-card {
  margin-bottom: 20px;
}
</style>
