/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.constantsAttribute;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder(toBuilder = true)
public class GateWayDevice {
    private String unitId;
    private String deviceId;
    private String deviceName;
    private String attributesPollPeriod;
    private String timeseriesPollPeriod;
    private List<String> attributes;
    private List<Timeseries> timeseries;
    private Boolean singleRead = false;
    private Integer deviceReadRegisterNumberByOnce = 20;
    private Integer wireSystem;

    public GateWayDevice() {
    }

    public GateWayDevice(String unitId, String deviceId, String deviceName, String attributesPollPeriod, String timeseriesPollPeriod, List<String> attributes, List<Timeseries> timeseries, Boolean singleRead, Integer deviceReadRegisterNumberByOnce) {
        this.unitId = unitId;
        this.deviceId = deviceId;
        this.deviceName = deviceName;
        this.attributesPollPeriod = attributesPollPeriod;
        this.timeseriesPollPeriod = timeseriesPollPeriod;
        this.attributes = attributes;
        this.timeseries = timeseries;
        this.singleRead = singleRead;
        this.deviceReadRegisterNumberByOnce = deviceReadRegisterNumberByOnce;
    }

    public GateWayDevice(String unitId, String deviceId, String deviceName, String attributesPollPeriod,
                         String timeseriesPollPeriod, List<String> attributes, List<Timeseries> timeseries,
                         Boolean singleRead, Integer deviceReadRegisterNumberByOnce, Integer wireSystem) {
        this.unitId = unitId;
        this.deviceId = deviceId;
        this.deviceName = deviceName;
        this.attributesPollPeriod = attributesPollPeriod;
        this.timeseriesPollPeriod = timeseriesPollPeriod;
        this.attributes = attributes;
        this.timeseries = timeseries;
        this.singleRead = singleRead;
        this.deviceReadRegisterNumberByOnce = deviceReadRegisterNumberByOnce;
        this.wireSystem = wireSystem;
    }
}
