import{d as Y,c as b,a8 as $,g as _,h as j,F as r,p as l,q as s,G as w,i as J,eK as U,n as h,bh as y,an as F,aB as z,aJ as H,x as i,eJ as K,J as q,cE as G,bK as O,N as P,O as Q,L as X,C as Z}from"./index-r0dFAfgr.js";/* empty css                 */import{x as g}from"./xlsx-rVJkW9yq.js";import{c as ee}from"./conservationWaterLevel-BIi1yWt3.js";const te={class:"import-content"},ae={class:"template-section"},oe={class:"upload-section"},se={key:0,class:"preview-section"},le={class:"preview-header"},ne={class:"preview-count"},re={key:1,class:"error-section"},ie={class:"error-list"},de={key:0},ce={class:"dialog-footer"},pe=Y({__name:"ImportDialog",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","confirm"],setup(C,{emit:A}){const B=C,E=A,k=b(!1),I=b(),d=b([]),c=b([]),L=$({get:()=>B.visible,set:a=>E("update:visible",a)}),M=()=>{const a=[{测点ID:"station001","原水液位(米)":10.5,"地下水位(米)":8.2,"降雨量(毫米)":25.5,"蒸发量(毫米)":5.2,"地表径流量(立方米)":1e3,"地下水开采量(立方米)":500,"土壤含水率(%)":35.5,渗透系数:.0025,记录时间:"2024-01-01 10:00:00",备注:"示例数据"}],e=g.utils.json_to_sheet(a),o=g.utils.book_new();g.utils.book_append_sheet(o,e,"涵养水位数据"),g.writeFile(o,"涵养水位数据导入模板.xlsx")},N=a=>{const e=a.raw;if(!e)return;if(!(e.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||e.type==="application/vnd.ms-excel")){i.error("只能上传Excel文件");return}if(!(e.size/1024/1024<10)){i.error("文件大小不能超过10MB");return}V(e)},T=()=>{i.warning("只能上传一个文件")},V=a=>{const e=new FileReader;e.onload=o=>{var t;try{const u=new Uint8Array((t=o.target)==null?void 0:t.result),m=g.read(u,{type:"array"}),n=m.SheetNames[0],v=m.Sheets[n],f=g.utils.sheet_to_json(v);R(f)}catch(u){console.error("读取文件失败:",u),i.error("文件格式错误，请检查文件内容")}},e.readAsArrayBuffer(a)},R=a=>{const e=[],o=[];a.forEach((t,u)=>{var n,v,f;const m=u+2;try{const p={stationId:(n=t.测点ID)==null?void 0:n.toString().trim(),rawWaterLevel:parseFloat(t["原水液位(米)"]),groundwaterLevel:parseFloat(t["地下水位(米)"]),rainfallAmount:t["降雨量(毫米)"]?parseFloat(t["降雨量(毫米)"]):void 0,evaporationAmount:t["蒸发量(毫米)"]?parseFloat(t["蒸发量(毫米)"]):void 0,surfaceRunoff:t["地表径流量(立方米)"]?parseFloat(t["地表径流量(立方米)"]):void 0,extractionAmount:t["地下水开采量(立方米)"]?parseFloat(t["地下水开采量(立方米)"]):void 0,soilMoisture:t["土壤含水率(%)"]?parseFloat(t["土壤含水率(%)"]):void 0,permeabilityCoefficient:t.渗透系数?parseFloat(t.渗透系数):void 0,recordTime:(v=t.记录时间)==null?void 0:v.toString().trim(),remark:(f=t.备注)==null?void 0:f.toString().trim(),dataSource:1},x=S(p,m);x.length>0?o.push(...x):e.push(p)}catch{o.push({row:m,message:"数据格式错误"})}}),d.value=e,c.value=o,o.length>0?i.warning(`发现 ${o.length} 个数据错误，请修正后重新上传`):i.success(`成功解析 ${e.length} 条数据`)},S=(a,e)=>{const o=[];return a.stationId||o.push({row:e,message:"测点ID不能为空"}),(isNaN(a.rawWaterLevel)||a.rawWaterLevel<0)&&o.push({row:e,message:"原水液位必须是大于等于0的数字"}),(isNaN(a.groundwaterLevel)||a.groundwaterLevel<0)&&o.push({row:e,message:"地下水位必须是大于等于0的数字"}),a.recordTime?/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(a.recordTime)||o.push({row:e,message:"记录时间格式错误，应为：YYYY-MM-DD HH:mm:ss"}):o.push({row:e,message:"记录时间不能为空"}),o},W=async()=>{if(d.value.length===0){i.warning("没有可导入的数据");return}if(c.value.length>0){i.error("请先修正数据错误");return}try{k.value=!0,(await ee(d.value)).data.code===200&&(i.success(`成功导入 ${d.value.length} 条数据`),E("confirm"),D())}catch(a){console.error("导入失败:",a),i.error("导入失败")}finally{k.value=!1}},D=()=>{var a;d.value=[],c.value=[],(a=I.value)==null||a.clearFiles(),E("update:visible",!1)};return(a,e)=>{const o=K,t=q,u=G,m=O,n=P,v=Q,f=X;return _(),j(f,{modelValue:L.value,"onUpdate:modelValue":e[0]||(e[0]=p=>L.value=p),title:"批量导入涵养水位数据",width:"600px","close-on-click-modal":!1,onClose:D},{footer:r(()=>[l("div",ce,[s(t,{onClick:D},{default:r(()=>e[6]||(e[6]=[w("取消")])),_:1}),s(t,{type:"primary",onClick:W,loading:k.value,disabled:d.value.length===0||c.value.length>0},{default:r(()=>e[7]||(e[7]=[w(" 确认导入 ")])),_:1},8,["loading","disabled"])])]),default:r(()=>[l("div",te,[s(o,{title:"导入说明",type:"info",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}},{default:r(()=>e[1]||(e[1]=[l("div",null,[l("p",null,"1. 请下载模板文件，按照模板格式填写数据"),l("p",null,"2. 支持Excel文件格式(.xlsx, .xls)"),l("p",null,"3. 必填字段：测点ID、原水液位、地下水位、记录时间"),l("p",null,"4. 单次最多导入1000条数据")],-1)])),_:1}),l("div",ae,[s(t,{type:"primary",icon:"Download",onClick:M},{default:r(()=>e[2]||(e[2]=[w(" 下载导入模板 ")])),_:1})]),l("div",oe,[s(m,{ref_key:"uploadRef",ref:I,class:"upload-demo",drag:"","auto-upload":!1,limit:1,"on-change":N,"on-exceed":T,accept:".xlsx,.xls"},{tip:r(()=>e[3]||(e[3]=[l("div",{class:"el-upload__tip"}," 只能上传Excel文件，且不超过10MB ",-1)])),default:r(()=>[s(u,{class:"el-icon--upload"},{default:r(()=>[s(J(U))]),_:1}),e[4]||(e[4]=l("div",{class:"el-upload__text"},[w(" 将文件拖到此处，或"),l("em",null,"点击上传")],-1))]),_:1},512)]),d.value.length>0?(_(),h("div",se,[l("div",le,[e[5]||(e[5]=l("span",{class:"preview-title"},"数据预览（前10条）",-1)),l("span",ne,"共 "+y(d.value.length)+" 条数据",1)]),s(v,{data:d.value.slice(0,10),border:"",size:"small","max-height":"300"},{default:r(()=>[s(n,{prop:"stationId",label:"测点ID",width:"100"}),s(n,{prop:"rawWaterLevel",label:"原水液位(m)",width:"120"}),s(n,{prop:"groundwaterLevel",label:"地下水位(m)",width:"120"}),s(n,{prop:"rainfallAmount",label:"降雨量(mm)",width:"100"}),s(n,{prop:"evaporationAmount",label:"蒸发量(mm)",width:"100"}),s(n,{prop:"extractionAmount",label:"开采量(m³)",width:"120"}),s(n,{prop:"recordTime",label:"记录时间",width:"160"}),s(n,{prop:"remark",label:"备注","show-overflow-tooltip":""})]),_:1},8,["data"])])):F("",!0),c.value.length>0?(_(),h("div",re,[s(o,{title:"数据验证错误",type:"error",closable:!1,"show-icon":""},{default:r(()=>[l("div",ie,[(_(!0),h(z,null,H(c.value.slice(0,10),(p,x)=>(_(),h("div",{key:x}," 第"+y(p.row)+"行："+y(p.message),1))),128)),c.value.length>10?(_(),h("div",de," ...还有"+y(c.value.length-10)+"个错误 ",1)):F("",!0)])]),_:1})])):F("",!0)])]),_:1},8,["modelValue"])}}}),_e=Z(pe,[["__scopeId","data-v-8ead1bfe"]]);export{_e as default};
