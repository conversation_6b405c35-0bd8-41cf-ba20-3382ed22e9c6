/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.optionLog;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.optionLog.OptionLog;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.model.sql.base.OptionLogList;
import org.thingsboard.server.dao.sql.base.OptionLogMapper;
import org.thingsboard.server.dao.util.imodel.query.base.OptionLogListPageRequest;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@Transactional
public class OptionLogServiceImpl implements OptionLogService {

    @Autowired
    private OptionLogDao optionLogDao;

    @Autowired
    private OptionLogMapper optionLogMapper;

    @Override
    public OptionLog save(OptionLog optionLog) {
        log.trace("Executing save [{}]", optionLog);
        return optionLogDao.save(optionLog);
    }

    @Override
    public List<OptionLog> getLogList(TenantId tenantId, String keyword, String ip, String options, Long startTime, Long endTime, String authority) {
        log.trace("Executing getLogList tenantId [{}] options [{}]", tenantId, options);
        // 默认查询三天内的登录日志
        if (startTime == null && endTime == null) {
            startTime = System.currentTimeMillis() - 1000 * 60 * 24 * 7;
            endTime = System.currentTimeMillis();
        } else {
            if (startTime == null) {
                startTime = 0L;
            }
            if (endTime == null) {
                endTime = System.currentTimeMillis();
            }
        }

        if (StringUtils.isNotBlank(ip)) {
            ip = "%" + ip + "%";
        } else {
            ip = "%";
        }

        if (Authority.SYS_ADMIN.name().equals(authority) && UUIDConverter.fromTimeUUID(tenantId.getId()).equals(DataConstants.DEFAULT_CUSTOMER_USER_ID)) {
            String authorityParam = "SYS_ADMIN";
            return optionLogDao.findByAuthorityAndOptionsAndTenantIdAndIpAndFirstNameLike(authorityParam, options, tenantId, startTime, endTime, ip, keyword);
        } else {
            return optionLogDao.findByTenantIdAndOptionsAndIpAndFirstNameLike(tenantId, options, startTime, endTime, ip, keyword);
        }

    }

    @Override
    public Map<String, Object> getOptionLogList(TenantId tenantId, Long startTime, Long endTime, String authority, String ip, String keyword, PageRequest pageRequest) {
        // 默认查询1天内的登录日志
        if (startTime == null && endTime == null) {
            startTime = System.currentTimeMillis() - 1000 * 60 * 24 * 7;
            endTime = System.currentTimeMillis();
        } else {
            if (startTime == null) {
                startTime = 0L;
            }
            if (endTime == null) {
                endTime =System.currentTimeMillis();
            }
        }
        if (StringUtils.isNotBlank(ip)) {
            ip = "%" + ip + "%";
        } else {
            ip = "%";
        }

        if (StringUtils.isNotBlank(keyword)) {
            keyword = "%" + keyword + "%";
        } else {
            keyword = "%";
        }

        if (Authority.SYS_ADMIN.name().equals(authority) && UUIDConverter.fromTimeUUID(tenantId.getId()).equals(DataConstants.DEFAULT_CUSTOMER_USER_ID)) {
            String authorityParam = "SYS_ADMIN,TENANT_ADMIN,CUSTOMER_USER";
            return optionLogDao.findOptionLogByAuthorityAndTenantIdAndIpAndFirstNameLike(authorityParam, DataConstants.DEFAULT_CUSTOMER_USER_ID, startTime, endTime, ip, keyword, pageRequest);
        } else {
            String tenantString = UUIDConverter.fromTimeUUID(tenantId.getId());
            String authorityParam = "TENANT_ADMIN,CUSTOMER_USER";
            return optionLogDao.findOptionLogByAuthorityAndTenantIdAndIpAndFirstNameLike(authorityParam, tenantString, startTime, endTime, ip, keyword, pageRequest);
        }
    }

    @Override
    public Object getUserLoginInfo(UserId id) {
        // 查询用户登录日志
        PageRequest request = new PageRequest(0, 2);
        List<OptionLog> logs = optionLogDao.findByUserId(id, request);
        JSONObject result = new JSONObject();
        if (logs == null || logs.isEmpty()) {
            result.put("lastLoginDate", "-");
            result.put("lastLoginIp", "-");
        } else {
            OptionLog log;
            if (logs.size() == 1) {
                log = logs.get(0);
            } else {
                log = logs.get(1);
            }
            result.put("lastLoginDate", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(log.getCreateTime())));
            JsonNode additionalInfo = log.getAdditionalInfo();
            result.put("lastLoginIp", additionalInfo.get("ip").asText());
        }

        return result;
    }

    @Override
    public int examine(String id, String examineId, String examineName, String examineTenantId, Long examineTime) {
        return optionLogDao.examine(id, examineId, examineName, examineTenantId, examineTime);
    }

    @Override
    public PageData<OptionLog> getLogListByPage(TenantId tenantId, String keyword, String ip, String options, Long startTime, Long endTime, String authority, int page, int size) {
        log.trace("Executing getLogList tenantId [{}] options [{}]", tenantId, options);
        // 默认查询三天内的登录日志
        if (startTime == null && endTime == null) {
            startTime = 0L;
            endTime = System.currentTimeMillis();
        } else {
            if (startTime == null) {
                startTime = 0L;
            }
            if (endTime == null) {
                endTime = System.currentTimeMillis();
            }
        }

        if (StringUtils.isNotBlank(ip)) {
            ip = "%" + ip + "%";
        } else {
            ip = "%";
        }

        if (StringUtils.isNotBlank(keyword)) {
            keyword = "%" + keyword + "%";
        } else {
            keyword = "%";
        }
        
        if (Authority.SYS_ADMIN.name().equals(authority) && UUIDConverter.fromTimeUUID(tenantId.getId()).equals(DataConstants.DEFAULT_CUSTOMER_USER_ID)) {
            String authorityParam = "SYS_ADMIN";
            return optionLogDao.findByAuthorityAndOptionsAndTenantIdAndIpAndFirstNameLike(authorityParam, options, tenantId, startTime, endTime, ip, keyword, new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime"));
        } else {
            return optionLogDao.findByTenantIdAndOptionsAndIpAndFirstNameLike(tenantId, options, startTime, endTime, ip, keyword, new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime"));
        }
    }

    @Override
    public IPage<OptionLogList> selectLogList(OptionLogListPageRequest optionLogListPageRequest) {
        return optionLogMapper.selectLogList(optionLogListPageRequest);
    }

    @Override
    public IPage<OptionLogList> selectOperationLogList(OptionLogListPageRequest optionLogListPageRequest) {
        return optionLogMapper.selectOperationLogList(optionLogListPageRequest);
    }
}
