package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.request.UserScheduleListRequest;
import org.thingsboard.server.dao.model.sql.UserSchedule;
import org.thingsboard.server.dao.user.UserScheduleService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("api/userSchedule")
public class UserScheduleController extends BaseController {

    @Autowired
    private UserScheduleService userScheduleService;

    @GetMapping("list")
    public IstarResponse findList(UserScheduleListRequest request) throws ThingsboardException {
        return IstarResponse.ok(userScheduleService.findList(request, getCurrentUser()));
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody UserSchedule entity) throws ThingsboardException {
        entity.setUserId(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        entity.setCreateTime(new Date());
        userScheduleService.save(entity);
        return IstarResponse.ok();
    }

    @DeleteMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        userScheduleService.remove(ids);

        return IstarResponse.ok();
    }


}
