package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 报装流程类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_gan_repair_c")
@Data
public class GanRepairC {
    @TableId
    private String id;

    private String pid;

    private Integer stepNo;

    private String file;

    private String remark;

    private String status;

    private Date createTime;

    private String creator;

    private String processUser;

    @TableField(exist = false)
    private String creatorName;
}
