package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBidding;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingSaveRequest;

public interface SoBiddingService {
    /**
     * 分页条件查询项目招投标信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoBidding> findAllConditional(SoBiddingPageRequest request);

    /**
     * 保存项目招投标信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoBidding save(SoBiddingSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoBidding entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
