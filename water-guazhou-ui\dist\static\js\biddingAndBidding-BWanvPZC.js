import{d as E,M as I,c,a8 as u,s as j,r as g,x as b,a9 as C,o as v,g as w,n as L,q as r,i as p,F as M,b6 as Y,b7 as F}from"./index-r0dFAfgr.js";import{_ as N}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as O}from"./CardTable-rdWOL4_6.js";import{_ as R}from"./CardSearch-CB_HNR-Q.js";import{I as y}from"./common-CvK_P_ao.js";import{g as U}from"./manage-BReaEVJk.js";import{a as S,p as V,b as q,c as z,g as A}from"./projectManagement-CDcrrCQ1.js";import H from"./detail-DEo1RlcF.js";import{f as x}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const P={class:"wrapper"},je=E({__name:"biddingAndBidding",setup(W){const{$btnPerms:_}=I(),f=c(),l=c(),h=c(),B=c({filters:[{label:"项目编号",field:"code",type:"input"},{label:"项目名称",field:"name",type:"input"},{label:"项目类别",field:"typeId",type:"select",options:u(()=>i.projectType)},{label:"启动时间",field:"time",type:"daterange",format:"x"}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:y.DOWNLOAD,click:()=>{S().then(e=>{const t=window.URL.createObjectURL(e.data),a=document.createElement("a");a.style.display="none",a.href=t,a.setAttribute("download","招投标流程.xlsx"),document.body.appendChild(a),a.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:j(F),click:()=>{var e;(e=f.value)==null||e.resetForm(),s()}},{perm:!0,text:"查询",icon:y.QUERY,click:()=>s()}]}]}),o=g({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"项目编号",prop:"code"},{label:"项目名称",prop:"name"},{label:"项目类别",prop:"typeName"},{label:"项目概算(万元)",prop:"estimate"},{label:"启动时间",prop:"startTime",formatter:e=>x(e.startTime,"YYYY-MM-DD HH:mm:ss")},{label:"预计结束时间",prop:"expectEndTime",formatter:e=>x(e.expectEndTime,"YYYY-MM-DD HH:mm:ss")},{label:"项目负责人",prop:"principal"}],operationWidth:"200px",operations:[{isTextBtn:!1,type:"success",text:"更新招投标流程",perm:_("RoleManageEdit"),click:e=>T(e)},{isTextBtn:!1,text:"详情",perm:_("RoleManageEdit"),click:e=>{var t;i.selected=e,(t=h.value)==null||t.openDrawer()}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{o.pagination.page=e,o.pagination.limit=t,s()}}}),d=g({title:"更新招投标流程",labelWidth:"100px",dialogWidth:"1000px",submitting:!1,submit:e=>{d.submitting=!0,e.items.map(t=>{t.biddingId=e.biddingId}),V(e).then(t=>{var a;d.submitting=!1,t.data.code===200?(b.success("更新成功"),(a=l.value)==null||a.closeDialog()):b.warning("更新失败")}).catch(t=>{d.submitting=!1,b.warning(t)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"工程编号",field:"projectCode"},{xs:12,type:"input",label:"代理招标公司",field:"proxyBiddingCompany"},{type:"table",field:"items",label:"参与投标公司",config:{titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加",perm:!0,click:()=>{i.BiddingCompany.push({num:i.BiddingCompany.length,name:"",contactUser:"",phone:""})}}]}]}],indexVisible:!0,height:"200px",dataList:u(()=>i.BiddingCompany),columns:[{label:"公司名称",prop:"name",tableDataName:"items",formItemConfig:{type:"input",field:"name",rules:[{required:!0,message:"请输入公司名称"}]}},{label:"联系人",prop:"contactUser",tableDataName:"items",formItemConfig:{type:"input",field:"contactUser",rules:[{required:!0,message:"请输入联系人"}]}},{label:"联系电话",prop:"phone",tableDataName:"items",formItemConfig:{type:"input",field:"phone",rules:[{required:!0,message:"请输入联系电话"}]}}],operations:[{type:"danger",perm:!0,text:"删除",icon:y.DELETE,click:e=>{i.BiddingCompany=i.BiddingCompany.filter(t=>t.num!==e.num)}}],pagination:{hide:!0}}},{type:"select",label:"中标公司",field:"preferCompanyId",options:u(()=>C(i.BiddingCompany.filter(e=>e.id),"children",{label:"name",value:"id"}))},{type:"file",label:"附件",field:"attachments"}]}]}),D=g({title:"详情",group:[],width:"80%",modalClass:"lightColor",cancel:!1}),T=e=>{var t;i.BiddingCompany=[],i.getbiddingInformation(e.code,e),(t=l.value)==null||t.openDialog()},i=g({projectType:[],BiddingCompany:[],selected:{},getOptions:()=>{U({page:1,size:-1}).then(e=>{i.projectType=C(e.data.data.data||[])})},getbiddingInformation:(e,t)=>{q({page:1,size:-1,projectCode:e}).then(a=>{var n,m;l.value.refForm.dataForm={projectCode:t.code,biddingId:(n=a.data.data.data[0])==null?void 0:n.id,...a.data.data.data[0]||{}},i.getBiddingCompanyValue((m=a.data.data.data[0])==null?void 0:m.projectCode)})},getBiddingCompanyValue:e=>{z({page:1,size:-1,projectCode:e}).then(t=>{i.BiddingCompany=(t.data.data.data||[]).map((a,n)=>(a.num=n,a))})}}),s=async()=>{var t;const e={size:o.pagination.limit,page:o.pagination.page,...((t=f.value)==null?void 0:t.queryParams)||{}};e!=null&&e.time&&(e.startTimeFrom=e.time[0],e.startTimeTo=e.time[1],delete e.time),A(e).then(a=>{o.dataList=a.data.data.data||[],o.pagination.total=a.data.data.total||0})};return v(()=>{s(),i.getOptions()}),(e,t)=>{const a=R,n=O,m=N,k=Y;return w(),L("div",P,[r(a,{ref_key:"refSearch",ref:f,config:p(B)},null,8,["config"]),r(n,{config:p(o),class:"card-table"},null,8,["config"]),r(m,{ref_key:"refForm",ref:l,config:p(d)},null,8,["config"]),r(k,{ref_key:"refDetail",ref:h,config:p(D)},{default:M(()=>[r(H,{config:p(i).selected,show:2},null,8,["config"])]),_:1},8,["config"])])}}});export{je as default};
