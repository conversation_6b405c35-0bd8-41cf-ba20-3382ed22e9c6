const L=9999999e31,S=2e-7,g={u1:[0,1],u2:[0,3],u4:[0,15],u8:[0,255],s8:[-128,127],u16:[0,65535],s16:[-32768,32767],u32:[0,4294967295],s32:[-2147483648,2147483647],f32:[-34028234663852886e22,34028234663852886e22],f64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function k(t){return g[t]??[-34028234663852886e22,34028234663852886e22]}function x(t,f,c){if(t.depthCount&&t.depthCount>1)return;const{pixels:b,statistics:i,pixelType:r}=t,p=b[0].length,m=t.bandMasks??[],h=t.mask??new Uint8Array(p).fill(255),d=r==="f32"||r==="f64",N=k(r);let l=!1;for(let s=0;s<b.length;s++){const n=typeof f=="number"?f:f[s];if(n==null)continue;const A=(i==null?void 0:i[s].minValue)??N[0],E=(i==null?void 0:i[s].maxValue)??N[1];if(A>n+Number.EPSILON||E<n-Number.EPSILON)continue;const o=m[s]||new Uint8Array(p).fill(255),a=b[s],M=c==null?void 0:c.customFloatTolerance;if(d&&M!==0){let e=M;e||(e=Math.abs(n)>=L?S*Math.abs(n):r==="f32"?2**-23:Number.EPSILON);for(let u=0;u<a.length;u++)o[u]&&Math.abs(a[u]-n)<e&&(a[u]=0,o[u]=0,h[u]=0,l=!0)}else for(let e=0;e<a.length;e++)o[e]&&a[e]===n&&(a[e]=0,o[e]=0,h[e]=0,l=!0);m[s]=o}l&&(t.bandMasks=m,t.mask=h),l&&"updateStatistics"in t&&t.updateStatistics()}export{k as s,x as u};
