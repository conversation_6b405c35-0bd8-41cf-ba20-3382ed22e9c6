package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionPumpHouse;
import org.thingsboard.server.dao.sql.smartPipe.PartitionPumpHouseMapper;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PartitionPumpHouseServiceImpl implements PartitionPumpHouseService {

    @Autowired
    private PartitionPumpHouseMapper partitionPumpHouseMapper;

    @Override
    public PartitionPumpHouse save(PartitionPumpHouse partitionPumpHouse) {
        if (StringUtils.isBlank(partitionPumpHouse.getId())) {
            partitionPumpHouse.setCreateTime(new Date());
            partitionPumpHouseMapper.insert(partitionPumpHouse);
        } else {
            partitionPumpHouseMapper.updateById(partitionPumpHouse);
        }
        return partitionPumpHouse;
    }


    @Override
    public PageData<PartitionPumpHouse> getList(PartitionMountRequest request) {
        IPage<PartitionPumpHouse> page = new Page<>(request.getPage(), request.getSize());
        IPage<PartitionPumpHouse> result = partitionPumpHouseMapper.getList(page, request);
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        partitionPumpHouseMapper.deleteBatchIds(ids);
    }

}
