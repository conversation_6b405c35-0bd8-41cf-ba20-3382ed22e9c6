<template>
  <div
    v-loading="state.loading"
    class="jumping-modal"
  ></div>
</template>
<script lang="ts" setup>
import { getKey, scadaLogin } from '@/api/login'
import { useBusinessStore, useUserStore } from '@/store'
import AES from '@/utils/AES'

const router = useRouter()
const state = reactive<{
  loading: boolean
}>({
  loading: true
})
const goHome = async () => {
  state.loading = false
  const res = await getKey()
  const lparams = {
    username: AES.encrypt('<EMAIL>', res.data.key),
    password: AES.encrypt('Aa123456', res.data.key)
  }
  await useUserStore().Login(lparams)

  const roles = useUserStore().roles
  router
    .push({
      path: useBusinessStore().usePortal
        ? roles[0] === 'SYS_ADMIN'
          ? '/'
          : '/app'
        : '/'
    })
    .finally(() => {
      state.loading = false
    })
  // 拿组态登陆信息
  scadaLogin({
    strategy: 'local',
    username: lparams.username,
    password: lparams.password
  }).then(res => {
    useUserStore().ToggleScadaToken(res.data.accessToken)
  })
  localStorage.setItem(
    'ysinfo',
    JSON.stringify({
      u: lparams.username,
      p: lparams.password
    })
  )
  // AuthYinshou().then(res => {
  //   Cookies.remove('JSESSIONID')
  //   Cookies.set('JSESSIONID', {
  //     value: res.data.obj,
  //     path: '/water_project',
  //     httponly: true
  //   })
  // })
}
onMounted(() => {
  goHome()
})
</script>
<style lang="scss" scoped>
.jumping-modal {
  width: 100%;
  height: 100%;
}
</style>
