package org.thingsboard.server.dao.base;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BaseBuildWeb;
import org.thingsboard.server.dao.util.imodel.query.base.BaseBuildWebPageRequest;

/**
 * 平台管理-Web搭建Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IBaseBuildWebService 
{
    /**
     * 查询平台管理-Web搭建
     * 
     * @param id 平台管理-Web搭建主键
     * @return 平台管理-Web搭建
     */
    public BaseBuildWeb selectBaseBuildWebById(String id);

    /**
     * 查询平台管理-Web搭建列表
     * 
     * @param baseBuildWeb 平台管理-Web搭建
     * @return 平台管理-Web搭建集合
     */
    public IPage<BaseBuildWeb> selectBaseBuildWebList(BaseBuildWebPageRequest baseBuildWeb);

    /**
     * 新增平台管理-Web搭建
     * 
     * @param baseBuildWeb 平台管理-Web搭建
     * @return 结果
     */
    public int insertBaseBuildWeb(BaseBuildWeb baseBuildWeb);

    /**
     * 修改平台管理-Web搭建
     * 
     * @param baseBuildWeb 平台管理-Web搭建
     * @return 结果
     */
    public int updateBaseBuildWeb(BaseBuildWeb baseBuildWeb);

    /**
     * 批量删除平台管理-Web搭建
     * 
     * @param ids 需要删除的平台管理-Web搭建主键集合
     * @return 结果
     */
    public int deleteBaseBuildWebByIds(List<String> ids);

    /**
     * 删除平台管理-Web搭建信息
     * 
     * @param id 平台管理-Web搭建主键
     * @return 结果
     */
    public int deleteBaseBuildWebById(String id);
}
