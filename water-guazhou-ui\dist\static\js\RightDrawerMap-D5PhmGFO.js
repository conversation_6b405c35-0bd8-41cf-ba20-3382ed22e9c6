import{d as $e,j as Se,cN as _e,c as T,r as Q,am as Le,o as xe,bA as Pe,Q as Fe,g as y,h as v,F as C,ax as R,bo as X,n as M,p as K,q as P,aB as Z,aJ as ee,cn as te,bR as Ae,an as B,i as oe,aw as ze,bB as Ee,d1 as D,co as b,aq as Ie,_ as Ue,br as Ge,C as je}from"./index-r0dFAfgr.js";import{_ as Oe}from"./ArcView-DpMnCY82.js";import{_ as We}from"./Panel-DyoxrWMd.js";import{_ as Ve}from"./ArcStationWarning-BF9YrSzF.js";import{B as qe}from"./widget-BcWKanF2.js";import{y as F,n as G,ei as re,H as He,g as ie,m as Ne}from"./MapView-DaoQedLH.js";import{w as le}from"./Point-WxyopZva.js";import{D as Ye}from"./DrawerBox-CLde5xC8.js";import Je from"./PipeDetail-CTBPYFJW.js";import Qe from"./ArcBRTools-BO92yznB.js";import Xe from"./ListWindow-WS05QqV0.js";import Ke from"./PopLayout-BP55MvL7.js";import{P as Ze}from"./PoiSearchV2-D7yNeLuv.js";import{s as et,g as tt}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{a as ot}from"./URLHelper-B9aplt5w.js";import{u as rt}from"./useLayerList-DmEwJ-ws.js";import{u as it,a as lt}from"./useScaleBar-Beed-z91.js";import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./v4-SoommWqA.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./SideDrawer-CBntChyn.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";const st={class:"loading-wrapper"},at={class:"pop-container"},nt={key:0,class:"infowindow-container"},mt={key:1,class:"pop-table-box overlay-y"},ut="grayscale(0%) invert(100%) opacity(100%)",ct=$e({__name:"RightDrawerMap",props:{useArclayout:{type:Boolean},detailDragout:{type:Boolean},detailDragable:{type:Boolean},detailTitle:{},title:{},rightDrawerWidth:{},rightDrawerAbsolute:{type:Boolean},rightDrawerMinWidth:{},hideRightDrawer:{type:Boolean},hideDetailClose:{type:Boolean},detailExtra:{type:Boolean},windows:{},windowTableConfig:{},windowFormConfig:{},windowStyle:{},detailMaxMin:{type:Boolean},fullContent:{type:Boolean},panelCustomClass:{},detailUrl:{},hideSearch:{type:Boolean},hideLayerList:{type:Boolean},hideCoords:{type:Boolean},hidePipe:{type:Boolean},pops:{},enableCluster:{type:Boolean},clusterRadius:{},clusterMinSize:{},clusterMaxSize:{},clusterGraphics:{},beforeCollapse:{type:Function}},emits:["map-loaded","detail-refreshing","detail-refreshed","detail-closed","detail-opened","pop-more","pop-back","click","pop-toggle"],setup(se,{expose:ae,emit:ne}){const j=Se(),{proxy:$}=_e(),O=T(),A=Q({defaultBaseMap:"img_w",defaultFilter:""}),me=e=>{console.log(e),e.id==="dark_vec_w"?A.defaultFilter=ut:A.defaultFilter=""},S=T(),_=T(),ue=T(),z=T(),W=T(),ce=it(),de=lt(),pe=rt(),k=ne,c=se,w=Q({tabs:[],toolsMounted:!1,mounted:!1,isClusterEnabled:c.enableCluster||!0});let p=null;const i={featureLayers:[]},V=(e,t)=>k("pop-toggle",e,t),q=e=>{e.highLight=!0},fe=()=>{k("detail-opened")},ge=()=>{k("detail-closed")},ye=async()=>{var e;i.view&&((e=S.value)==null||e.extentTo(i.view))},he=async e=>{var t;w.tabs=e,await Ee(),(t=S.value)==null||t.openDialog()},we=()=>{var e;(e=S.value)==null||e.clearData()},H=e=>{var t;(t=_.value)==null||t.Toggle(e)},be=()=>{var e;return(e=_.value)==null?void 0:e.visible},ve=e=>{var t;(t=_.value)==null||t.toggleMaxMin(e)},Ce=e=>{var s,n,a;if(E(),(s=O.value)==null||s.togglePop(!1),e===void 0)return;const t=$.$refs["refPop"+e];t!=null&&t[0]&&((n=t[0])==null||n.toggle(!0),(a=t[0])==null||a.setPosition(i.view))},E=()=>{var e,t;(e=c.windows)==null||e.map(s=>{var l,d;const n=(l=s.attributes)==null?void 0:l.id;if(!n)return;const a=$.$refs["refPop"+n];a!=null&&a.length&&((d=a[0])==null||d.toggle(!1))}),(t=c.pops)==null||t.map(s=>{var l;const n=s==null?void 0:s.id;if(!n)return;const a=$.$refs["refPop"+n];a!=null&&a.length&&((l=a[0])==null||l.toggle(!1))})},ke=e=>{var t;return(t=z.value)==null?void 0:t.toggleDrawer("rtl",e===void 0?!0:e)},N=e=>{if(!e||!Array.isArray(e)||e.length===0)return console.log("📝 没有数据需要转换"),[];const t=[];return e.forEach((s,n)=>{var a,l,d,m,f,o,u,h,g;try{const r=D(s);if(!r||typeof r.longitude!="number"||typeof r.latitude!="number"){console.warn(`⚠️ 第${n}个数据项缺少有效的坐标:`,r);return}const Be=b(new le({longitude:r.longitude,latitude:r.latitude,spatialReference:((a=i.view)==null?void 0:a.spatialReference)||{wkid:4326}}));let L;r.symbolType==="marker"&&r.symbolConfig?L=b(new G({...r.symbolConfig,width:r.symbolConfig.width||25,height:r.symbolConfig.height||30})):r.symbolType==="text"&&r.symbolConfig?L=b(new Ne({...r.symbolConfig,text:r.symbolConfig.text||r.name,color:r.symbolConfig.color||"#000000"})):L=b(new F({style:"circle",color:"#3388ff",outline:{width:1,color:"white"},size:12}));const De=b(new ie({geometry:Be,symbol:L,attributes:{ObjectID:((l=r.attributes)==null?void 0:l.ObjectID)||n+1,name:((d=r.attributes)==null?void 0:d.name)||r.name||`点位${n+1}`,symbolType:r.symbolType,symbolUrl:r.symbolType==="marker"?(m=r.symbolConfig)==null?void 0:m.url:null,symbolWidth:r.symbolType==="marker"?((f=r.symbolConfig)==null?void 0:f.width)||25:null,symbolHeight:r.symbolType==="marker"?((o=r.symbolConfig)==null?void 0:o.height)||30:null,symbolYOffset:r.symbolType==="marker"?((u=r.symbolConfig)==null?void 0:u.yoffset)||0:null,textContent:r.symbolType==="text"?((h=r.symbolConfig)==null?void 0:h.text)||r.name:null,textColor:r.symbolType==="text"?((g=r.symbolConfig)==null?void 0:g.color)||"#000000":null,...Object.fromEntries(Object.entries(r.attributes||{}).filter(([dt,x])=>typeof x=="string"||typeof x=="number"||typeof x=="boolean"||x===null))}}));t.push(De)}catch(r){console.error(`❌ 转换第${n}个数据项时出错:`,r,s)}}),console.log(`📝 成功转换${t.length}个Graphic对象，原始数据${e.length}个`),t},I=()=>{var d;if(!i.view)return;const e=c.clusterGraphics?N(D(c.clusterGraphics)):[],t=b(new F({style:"circle",color:"#3388ff",outline:{width:1,color:"white"},size:12})),s=new Map;(D(c.clusterGraphics)||[]).forEach(m=>{var f;if(m.symbolType==="marker"&&((f=m.symbolConfig)!=null&&f.url)){const o=m.symbolConfig.url;s.has(o)||s.set(o,{value:o,symbol:b(new G({url:m.symbolConfig.url,width:m.symbolConfig.width||25,height:m.symbolConfig.height||30,yoffset:m.symbolConfig.yoffset||0})),label:`图标 ${o.split("/").pop()}`})}});const a=new re({field:"symbolUrl",defaultSymbol:t,uniqueValueInfos:Array.from(s.values())}),l=new He({title:"点聚合图层",objectIdField:"ObjectID",fields:[{name:"ObjectID",alias:"ObjectID",type:"oid"},{name:"name",alias:"名称",type:"string"},{name:"symbolType",alias:"符号类型",type:"string"},{name:"symbolUrl",alias:"符号URL",type:"string"},{name:"symbolWidth",alias:"符号宽度",type:"integer"},{name:"symbolHeight",alias:"符号高度",type:"integer"},{name:"symbolYOffset",alias:"符号Y偏移",type:"integer"},{name:"textContent",alias:"文本内容",type:"string"},{name:"textColor",alias:"文本颜色",type:"string"}],geometryType:"point",spatialReference:i.view.spatialReference,source:e,renderer:a,featureReduction:{type:"cluster",clusterRadius:`${c.clusterRadius||100}px`,clusterMinSize:`${c.clusterMinSize||24}px`,clusterMaxSize:`${c.clusterMaxSize||60}px`,symbol:b(new F({style:"circle",color:[51,136,255,.8],outline:{width:2,color:"white"},size:30})),labelingInfo:[{deconflictionStrategy:"none",labelExpressionInfo:{expression:"Text($feature.cluster_count, '#,###')"},symbol:{type:"text",color:"white",font:{weight:"bold",family:"Noto Sans",size:"12px"}},labelPlacement:"center-center"}],popupTemplate:{title:"聚合点",content:"该区域包含 {cluster_count} 个点位",fieldInfos:[{fieldName:"cluster_count",format:{places:0,digitSeparator:!0}}]}}});return l.when(()=>{var m;(m=i.view)==null||m.whenLayerView(l).then(f=>{console.log("聚合图层视图已创建，符号将由UniqueValueRenderer根据URL控制"),console.log("已创建符号映射:",Array.from(s.keys())),console.log("聚合配置:",{radius:c.clusterRadius||100,minSize:c.clusterMinSize||24,maxSize:c.clusterMaxSize||60,symbolType:"默认蓝色圆形符号用于聚合点"}),console.log("散开时将使用:",`${s.size}种不同图标`)})}),i.view.map.add(l),p=l,(d=i.featureLayers)==null||d.push(l),l},U=async e=>{var t,s,n,a;if(p)try{const l=e||D(c.clusterGraphics)||[];console.log("📝 准备转换简化数据为Graphic对象，数据长度:",l.length);const d=N(l);console.log("📝 转换后的Graphic对象:",d);const m=new Map;if(l.forEach(u=>{var h;if(u.symbolType==="marker"&&((h=u.symbolConfig)!=null&&h.url)){const g=u.symbolConfig.url;m.has(g)||m.set(g,{value:g,symbol:b(new G({url:u.symbolConfig.url,width:u.symbolConfig.width||25,height:u.symbolConfig.height||30,yoffset:u.symbolConfig.yoffset||0})),label:`图标 ${g.split("/").pop()}`})}}),m.size>0){const u=b(new F({style:"circle",color:"#3388ff",outline:{width:1,color:"white"},size:12})),h=new re({field:"symbolUrl",defaultSymbol:u,uniqueValueInfos:Array.from(m.values())});p.renderer=h,console.log("📝 已更新渲染器，符号数量:",m.size)}const f={deleteFeatures:[],addFeatures:d},o=await p.queryFeatures({where:"1=1",returnGeometry:!1,outFields:["*"]});if(o.features&&o.features.length>0&&(f.deleteFeatures=o.features,console.log("🗑️ 准备删除现有要素数量:",o.features.length)),f.deleteFeatures.length>0||f.addFeatures.length>0){const u=await p.applyEdits(f);console.log("✅ 聚合图层数据更新成功",{addResults:((t=u.addFeatureResults)==null?void 0:t.length)||0,deleteResults:((s=u.deleteFeatureResults)==null?void 0:s.length)||0});const h=((n=u.addFeatureResults)==null?void 0:n.filter(r=>!r.objectId))||[],g=((a=u.deleteFeatureResults)==null?void 0:a.filter(r=>!r.objectId))||[];(h.length>0||g.length>0)&&console.warn("⚠️ 部分编辑操作失败:",{addErrors:h.length,deleteErrors:g.length,result:u})}else console.log("📝 没有需要更新的数据")}catch(l){console.error("❌ 更新聚合数据时出错:",l),console.error("错误详情:",{message:(l==null?void 0:l.message)||"未知错误",stack:l==null?void 0:l.stack})}},Y=e=>{try{const t=e!==void 0?e:!w.isClusterEnabled;t&&!p&&I(),p&&(p.visible=t),w.isClusterEnabled=t}catch(t){console.error("切换聚合功能时出错:",t),w.isClusterEnabled=!1}},Re=async e=>{var t;if(p)try{const s=p;(t=i.view)==null||t.map.remove(s),I(),await U()}catch(s){console.error("更新聚合配置时出错:",s)}},J=()=>{if(!p)return console.log("❌ 聚合图层未创建"),!1;const e=p.featureReduction;return e?e.type!=="cluster"?(console.log("❌ 不是聚合类型的featureReduction"),!1):(console.log("✅ 聚合配置验证通过"),console.log("聚合类型:",e.type),console.log("聚合半径:",e.clusterRadius),console.log("聚合符号:",e.symbol?"已设置":"未设置"),console.log("散开时渲染器:",p.renderer?"UniqueValueRenderer":"无"),!0):(console.log("❌ 聚合配置未设置"),!1)};Le(()=>c.clusterGraphics,async e=>{if(console.log("=== 监听到简化数据变化 ==="),console.log("新数据长度:",e==null?void 0:e.length),console.log("enableCluster:",c.enableCluster),console.log("clusterLayerMap exists:",!!p),console.log("staticState.view exists:",!!i.view),e!=null&&e.length){if(console.log("📊 开始处理简化数据..."),c.enableCluster&&!p&&i.view&&(console.log("🚀 创建聚合图层..."),I(),w.isClusterEnabled=!0,setTimeout(()=>{J()},1e3)),p){console.log("📝 更新聚合数据...");const t=D(e);await U(t)}}else console.log("❌ 没有有效的数据，跳过处理")},{immediate:!0,deep:!0});const Te=e=>{var s,n;if(!i.view||e.length!==2)return;const t=new ie({geometry:new le({longitude:e==null?void 0:e[0],latitude:e==null?void 0:e[1],spatialReference:i.view.spatialReference}),symbol:et("picture",{url:ot(),yOffset:-8})});(s=i.view)==null||s.graphics.removeAll(),(n=i.view)==null||n.graphics.add(t),tt(i.view,t,{avoidHighlight:!0,zoom:16})},Me=e=>{e.when().then(()=>{var s,n;i.view=e,w.mounted=!0,c.enableCluster&&Y(!0);const t=(s=i.view)==null?void 0:s.watch("extent",()=>{var a;(a=c.pops)==null||a.map(l=>{var m;(m=$.$refs["refPop"+l.id][0])==null||m.setPosition(i.view)})});(n=i.watchers)==null||n.push(t),qe(()=>i.view).then(()=>{var a;i.view&&(i.view.ui.add("tool-search-poi","top-right"),!c.hideLayerList&&pe.init(i.view),!c.hideCoords&&de.init(i.view),ce.init(i.view),(a=W.value)==null||a.init({hidePipe:c.hidePipe}).then(l=>{w.toolsMounted=!0,i.toolWdigets=l}).finally(()=>{i.view&&k("map-loaded",i.view,i.toolWdigets)}))})})};return xe(async()=>{var e;(e=z.value)==null||e.toggleDrawer("rtl",!0)}),Pe(()=>{var e,t,s,n,a,l;(e=i.watchers)==null||e.map(d=>d.remove&&d.remove()),(s=(t=i.view)==null?void 0:t.map)==null||s.removeAll(),(a=(n=i.view)==null?void 0:n.map)==null||a.destroy(),(l=i.view)==null||l.destroy(),i.view=void 0}),Fe(()=>{H(!1)}),ae({staticState:i,refreshDetail:he,toggleCustomDetail:H,highlightPop:q,openPop:Ce,closeAllPop:E,toggleCustomDetailMaxmin:ve,isCustomOpened:be,toggleDrawer:ke,clearDetailData:we,toggleCluster:Y,updateClusterConfig:Re,updateClusterGraphics:U,verifyClusterConfig:J}),(e,t)=>{const s=Ve,n=Ie,a=Ue,l=We,d=Oe,m=Ge;return y(),v(Ye,{ref_key:"refDrawerBox",ref:z,"right-drawer":!e.hideRightDrawer,"right-drawer-title":e.title,"right-drawer-width":e.rightDrawerWidth,"right-drawer-absolute":e.rightDrawerAbsolute,"right-drawer-bar-position":"top",theme:oe(j).isDark?"darkblue":"light",class:ze(oe(j).isDark?"darkblue":""),"before-collapse":e.beforeCollapse,"right-drawer-min-width":e.rightDrawerMinWidth},{"right-title":C(()=>[R(e.$slots,"right-title",{},void 0,!0)]),right:C(()=>[R(e.$slots,"default",{},void 0,!0)]),default:C(()=>[X((y(),M("div",st,[K("div",{id:"map-outer-box",ref_key:"refMapWrapper",ref:ue,class:"map-wrapper",onClick:t[3]||(t[3]=f=>k("click",f))},[P(d,{"map-config":A,onLoaded:Me},{default:C(()=>{var f;return[P(s,{ref_key:"refAlarms",ref:O,onClick:E},null,512),K("div",at,[(y(!0),M(Z,null,ee(e.pops,o=>X((y(),v(Ke,{key:o.id,ref_for:!0,ref:"refPop"+o.id,title:o.title,"show-more":o.showMore,"show-back":o.showBack,status:o.status,"background-color":o.bgColor,x:o.x,y:o.y,latitude:o.latitude,longitude:o.longitude,offsetx:o.offsetX,offsety:o.offsetY,onMore:()=>k("pop-more",o),onBack:()=>k("pop-back",o),onToggled:u=>V(o,u)},{default:C(()=>[R(e.$slots,"pop-default",{config:o},()=>[(y(),v(te(o.customComponent),{visible:o.visible,config:o.customConfig},null,8,["visible","config"]))],!0)]),_:2},1032,["title","show-more","show-back","status","background-color","x","y","latitude","longitude","offsetx","offsety","onMore","onBack","onToggled"])),[[Ae,o.visible]])),128))]),(f=e.windows)!=null&&f.length?(y(),M("div",nt,[(y(!0),M(Z,null,ee(e.windows,(o,u)=>{var h;return y(),v(Xe,{key:u,ref_for:!0,ref:"refPop"+((h=o.attributes)==null?void 0:h.id),view:i.view,config:o,"disable-scroll":!0,onToggled:g=>V(o,g),onHighlight:q},{default:C(()=>{var g;return[(g=o.attributes)!=null&&g.customComponent?(y(),v(te(o.attributes.customComponent),{key:0,visible:o.visible,config:o.attributes.customConfig},null,8,["visible","config"])):e.windowTableConfig?(y(),M("div",mt,[P(n,{config:e.windowTableConfig},null,8,["config"])])):e.windowFormConfig?(y(),v(a,{key:2,config:e.windowFormConfig},null,8,["config"])):B("",!0)]}),_:2},1032,["view","config","onToggled"])}),128))])):B("",!0),P(Ze,{id:"tool-search-poi",onChange:Te}),w.mounted?(y(),v(Qe,{key:1,ref_key:"refArcBRTools",ref:W,view:i.view,"basemap-change":me},null,8,["view"])):B("",!0),w.mounted?(y(),v(Je,{key:2,ref_key:"refDetail",ref:S,tabs:w.tabs,view:i.view,telport:"#map-outer-box","detail-url":e.detailUrl,maxmin:e.detailMaxMin,onRefreshed:t[0]||(t[0]=o=>e.$emit("detail-refreshed")),onRefreshing:t[1]||(t[1]=o=>e.$emit("detail-refreshing")),onClose:t[2]||(t[2]=o=>e.$emit("detail-closed")),onRowdblclick:ye},null,8,["tabs","view","detail-url","maxmin"])):B("",!0),w.mounted?(y(),v(l,{key:3,ref_key:"refPanel",ref:_,"custom-class":e.panelCustomClass||"gis-detail-panel",telport:"#map-outer-box",draggable:e.detailDragable,dragout:e.detailDragout,"full-content":e.fullContent,"destroy-by-close":!0,"after-open":fe,extra:e.detailExtra,"show-close":!e.hideDetailClose,"max-min":e.detailMaxMin===void 0?!0:e.detailMaxMin,"before-close":ge,title:e.detailTitle},{extra:C(()=>[R(e.$slots,"detail-extra",{},void 0,!0)]),header:C(()=>[R(e.$slots,"detail-header",{},void 0,!0)]),default:C(()=>[R(e.$slots,"detail-default",{},void 0,!0)]),_:3},8,["custom-class","draggable","dragout","full-content","extra","show-close","max-min","title"])):B("",!0),R(e.$slots,"map-bars",{},void 0,!0)]}),_:3},8,["map-config"])],512)])),[[m,!w.mounted]])]),_:3},8,["right-drawer","right-drawer-title","right-drawer-width","right-drawer-absolute","theme","class","before-collapse","right-drawer-min-width"])}}}),gr=je(ct,[["__scopeId","data-v-c9405348"]]);export{gr as default};
