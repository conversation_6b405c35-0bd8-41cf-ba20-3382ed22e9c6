package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DEVICE_TEMPLATE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class DeviceTemplate {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.DEVICE_TEMPLATE_NAME)
    private String name;

    @Column(name = ModelConstants.DEVICE_TEMPLATE_REMARK)
    private String remark;

    @Column(name = ModelConstants.DEVICE_TEMPLATE_ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.DEVICE_TEMPLATE_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.DEVICE_TEMPLATE_TENANT_ID)
    private String tenantId;

    @Column(name = ModelConstants.DEVICE_TEMPLATE_TYPE)
    private String type;

    @Column(name = ModelConstants.DEVICE_TEMPLATE_PROTOCOL)
    private String protocol;

}
