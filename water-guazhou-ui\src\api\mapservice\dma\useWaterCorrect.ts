import { request } from '@/plugins/axios'

/**
 * 查询供水量修正列表
 * @param params
 * @returns
 */
export const GetDMASupplyTotalFlowList = (
  params: IQueryPagerParams & {
    /**
     * 类型 1时  2日 3月
     */
    type?: string
    start?: string
    end?: string
    meterName?: string
  }
) => {
  return request({
    url: '/api/spp/partitionTotalFlow/list',
    method: 'get',
    params
  })
}
/**
 * 导出供水量修正
 * @param params
 * @returns
 */
export const ExportDMASupplyTotalFlowList = (params: any) => {
  return request({
    url: '/api/spp/partitionTotalFlow/listExport',
    method: 'get',
    data: params,
    responseType: 'blob'
  })
}
/**
 * 查询供水量修正记录列表
 * @param params
 * @returns
 */
export const GetDMASupplyTotalFlowCorrectRecords = (
  params?: IQueryPagerParams & {
    partitionName?: string
  }
) => {
  return request({
    url: '/api/spp/partitionTotalFlow/correctRecords',
    method: 'get',
    params
  })
}
/**
 * 导出供水量修正记录
 * @param params
 * @returns
 */
export const ExportDMASupplyTotalFlowCorrectRecords = (params?: any) => {
  return request({
    url: `/api/spp/partitionTotalFlow/correctRecordsExport`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}
/**
 * 追加供水量
 * @param params
 * @returns
 */
export const CorrectDMASupplyTotalFlow = (params: { id: string; correctWater: number }) => {
  return request({
    url: '/api/spp/partitionTotalFlow/correct',
    method: 'post',
    data: params
  })
}
