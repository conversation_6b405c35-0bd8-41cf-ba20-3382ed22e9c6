import { request } from '@/plugins/axios'

/**
 * 查询DMA增量漏失评估列表
 * @param params
 * @returns
 */
export const GetDMAProgressLossControlList = (params: {
  date?: string
  partitionId?: string
  name?: string
}) => {
  return request({
    url: '/api/spp/dma/partitionEvaluate/normalList',
    method: 'get',
    params
  })
}
/**
 * 查询DMA分区流量分析 1-24小时流量分析
 * @param params
 * @returns
 */
export const GetDMALossControlFlowAnalys = (params: {
  date?: string
  partitionId: string
}) => {
  return request({
    url: '/api/spp/dma/partitionEvaluate/dayFlowAnalysis',
    method: 'get',
    params
  })
}
/**
 * 查询DMA存量漏失评估列表
 * @param params
 * @returns
 */
export const GetDMAStockLossControlList = (params: {
  date?: string
  partitionId?: string
  name?: string
}) => {
  return request({
    url: '/api/spp/dma/partitionEvaluate/notNormalList',
    method: 'get',
    params
  })
}
/**
 * 查询DMA漏控明细
 * @param params
 * @returns
 */
export const GetDMALossControlDetail = (params?: {
  date?: string
  partitionId?: string
  type?: string
  start?: string
  end?: string
}) => {
  return request({
    url: `/api/spp/dma/partitionEvaluate/normalListDetail`,
    method: 'get',
    params
  })
}
/**
 * 查询总分表差列表
 * @param params
 * @returns
 */
export const GetDMATotalDifferList = (params: {
  date?: string
  partitionId?: string
  type?: string
  start?: string
  end?: string
}) => {
  return request({
    url: '/api/spp/dma/partitionEvaluate/totalDifference',
    method: 'get',
    params
  })
}
/**
 * 导出
 * @param params
 */
export const ExportDMATotalDifferList = (params: {
  date?: string
  partitionId?: string
  type?: string
  start?: string
  end?: string
}) => {
  return request({
    url: '/api/spp/dma/partitionEvaluate/totalDifferenceExport',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
/**
 * 修改分区状态
 * @param params
 * @returns
 */
export const EditDMAPartitionStatus = (params: {
  id: string
  /**
   * 状态 1规划中 2建制中 3评估中 4检修漏 5营运中
   */
  status: string
}) => {
  return request({
    url: '/api/spp/dma/partition/changeStatus',
    method: 'post',
    data: params
  })
}
/**
 * 查询漏控工单列表
 * @param params
 * @returns
 */
export const GetLossWorkOrderList = (
  params: IQueryPagerParams & {
    type?: string
    year?: string
    month?: string
    start?: string
    end?: string
    organizerName?: string
    partitionName?: string
    processUserName?: string
    receiveDepartmentName?: string
  }
) => {
  return request({
    url: '/api/spp/dma/workOrder/list',
    method: 'get',
    params
  })
}
/**
 * 发起漏损工单
 *
 * @param params
 * @returns
 */
export const PostLossWorkOrder = (params: {
  isDirectDispatch: boolean
  organizerId: string
  coordinate: string
  address: string
  title: string
  source: string
  level: string
  type: string
  remark: string
  stepProcessUserId: string
  processLevel: string
  partitionId: string
}) => {
  return request({
    url: '/api/spp/dma/workOrder',
    method: 'post',
    data: params
  })
}
