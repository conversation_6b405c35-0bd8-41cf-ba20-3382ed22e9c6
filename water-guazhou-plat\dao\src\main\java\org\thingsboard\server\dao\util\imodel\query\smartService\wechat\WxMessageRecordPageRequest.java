package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecord;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class WxMessageRecordPageRequest extends AdvancedPageableQueryEntity<WxMessageRecord, WxMessageRecordPageRequest> {
    // 0发送中、1发送完成
    private String status;

    // 模板名称
    private String templateName;

    // 标题
    private String title;



}
