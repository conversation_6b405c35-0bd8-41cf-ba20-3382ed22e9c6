//* * 设计分类 */
export const DesignClassification = [
  { label: '变更设计', value: '变更设计' },
  { label: '主题设计', value: '主题设计' }
];

/** 状态 */
export const StatusType = [
  { label: '处理中', value: null, color: '#409EFF' },
  { label: '处理中', value: 'PROCESSING', color: '#409EFF' },
  { label: '已完成', value: 'COMPLETED', color: '#67C23A' }
];

/** 费用类型 */
export const TypesFee = [
  { label: '预付款', value: '预付款' },
  { label: '进度款', value: '进度款' },
  { label: '尾款', value: '尾款' },
  { label: '质保金', value: '质保金' },
  { label: '其它', value: '其它' }
];

/** 支付方式 */
export const PaymentMethod = [
  { label: '其它方式', value: '其它方式' },
  { label: '在线支付', value: '在线支付' },
  { label: '银行转账', value: '银行转账' },
  { label: '现金支付', value: '现金支付' }
];
