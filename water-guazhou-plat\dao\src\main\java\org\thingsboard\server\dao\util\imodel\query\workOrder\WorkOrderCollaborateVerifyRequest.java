package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.*;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class WorkOrderCollaborateVerifyRequest extends SaveRequest<WorkOrderDetail> {
    // 标题
    @NotNullOrEmpty(condition = "validateProcessLevel")
    private String title;

    // 紧急程度
    @NotNullOrEmpty(condition = "validateProcessLevel")
    private String level;

    // 地址
    @NotNullOrEmpty(condition = "validateProcessLevel")
    private String address;

    // 直接派发处理人
    @NotNullOrEmpty(condition = "validateProcessLevel")
    private String stepProcessUserId;

    // 描述/备注
    private String orderRemark;

    // 现场视频，多个用逗号分隔（最多2）
    private String videoUrl;

    // 现场音频，多个用逗号分隔（最多2）
    private String audioUrl;

    // 现场图片，多个用逗号分隔
    private String imgUrl;

    // 其他附件，多个用逗号分隔（最多2）
    private String otherFileUrl;

    // region TODO: [LFT] 被忽略的上报信息
    // // 上报人
    // private String uploadUserId;
    //
    // // 上报人电话
    // private String uploadPhone;
    //
    // // 上报人户号
    // private String uploadNo;
    //
    // // 上报人地址
    // private String uploadAddress;
    // endregion

    // 处理级别
//    private WorkOrderLevel processLevel;

    // 处理级别
    private Integer processLevel;

    // 处理级别
    private String processLevelLabel;

    // 项目ID
    private String projectId;

    // 抄送人，多个用逗号隔开
    private String ccUserId;

    // 是否通过审核（仅在审核中有效）
    private String approved;

    public boolean approved() {
        return Boolean.parseBoolean(approved);
    }

    public boolean validateProcessLevel() {
        return Boolean.parseBoolean(approved);
    }


    public WorkOrderDetail process(WorkOrder workOrder, WorkOrderCollaboration collaboration, Date now, String tenantId) {
        workOrder.setType(collaboration.getType());
        // 处理人 设为 【当前用户】
        workOrder.setProcessUserId(currentUserUUID());
        workOrder.setDirectDispatch(true);
        workOrder.setTitle(title);
        workOrder.setSource("工单协作");
        // 发起人 设为 【协作审核人】
        workOrder.setOrganizerId(collaboration.getUserId());
        workOrder.setLevel(level);
        workOrder.setAddress(address);
        workOrder.setRemark(orderRemark);
        workOrder.setVideoUrl(videoUrl);
        workOrder.setAudioUrl(audioUrl);
        workOrder.setImgUrl(imgUrl);
        workOrder.setOtherFileUrl(otherFileUrl);
        workOrder.setParentId(collaboration.getOrderId());
        // TODO: [LFT] 暂时不设定 【上报人】
        // workOrder.setUploadUserId();
        // workOrder.setUploadPhone();
        // workOrder.setUploadNo();
        // workOrder.setUploadAddress();
        workOrder.setProcessLevel(processLevel);
        workOrder.setProcessLevelLabel(processLevelLabel);
        workOrder.setEstimatedFinishTime(new Date(System.currentTimeMillis() + (processLevel * 60 * 1000)));
        workOrder.setStatus(WorkOrderStatus.RESOLVING);
        // 指定新订单当前处理人为 【直接派发处理人】
        workOrder.setStepProcessUserId(stepProcessUserId);
        workOrder.setCreateTime(now);
        workOrder.setUpdateTime(now);
        workOrder.setProjectId(projectId);
        workOrder.setTenantId(tenantId);
        workOrder.setCcUserId(ccUserId);

        WorkOrderDetail detail = new WorkOrderDetail();
        // 需要在外面设置主表工单ID
        // detail.setMainId(workOrder.getId());
        detail.setType(WorkOrderStatus.RESOLVING);
        // 处理人设为 【当前用户】
        detail.setProcessUserId(currentUserUUID());
        detail.setProcessTime(now);
        // 下一步处理人为 【直接派发处理人】
        detail.setNextProcessUserId(stepProcessUserId);

        return detail;
    }

    @Override
    public WorkOrderDetail build() {
        return null;
    }

    @Override
    public WorkOrderDetail update(String id) {
        return null;
    }
}
