package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.DmaCost;
import org.thingsboard.server.dao.sql.smartPipe.DmaCostMapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2024-06-18
 */
@Service
public class DmaCostServiceImpl implements DmaCostService {

    @Autowired
    private DmaCostMapper dmaCostMapper;

    @Autowired
    private PartitionService partitionService;

    @Override
    public DmaCost save(DmaCost dmaCost) {
        if (StringUtils.isBlank(dmaCost.getId())) {
            dmaCost.setCreateTime(new Date());
            dmaCostMapper.insert(dmaCost);
        } else {
            dmaCostMapper.updateById(dmaCost);
        }
        return dmaCost;
    }

    @Override
    public PageData<DmaCost> getList(Map<String, Object> params) {
        if (params.get("partitionId") != null) {
            List<String> partitionIdList = new ArrayList<>();
            partitionService.getAllChildId(params.get("partitionId").toString(), partitionIdList);
            params.put("partitionIdList", partitionIdList);
        }
        int page = 1;
        int size = 999;
        if (params.get("page") != null) {
            page = Integer.parseInt(params.get("page").toString());
        }
        if (params.get("size") != null) {
            size = Integer.parseInt(params.get("size").toString());
        }
        IPage<DmaCost> pages = new Page<>(page, size);
        pages = dmaCostMapper.getList(params, pages);

        return new PageData<>(pages.getTotal(), pages.getRecords());
    }

    @Override
    public void delete(List<String> idList) {
        dmaCostMapper.deleteBatchIds(idList);
    }
}
