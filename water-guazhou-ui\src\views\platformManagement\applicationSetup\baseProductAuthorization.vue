<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getBaseProductAuthorizationList, 
  addBaseProductAuthorization, 
  editBaseProductAuthorization, 
  deleteBaseProductAuthorization,
  getBaseProductAuthorizationDetail 
} from '@/api/platformManagement/baseProductAuthorization'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()
const refDialogForm = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '产品名称', 
      field: 'name', 
      placeholder: '请输入产品名称',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '产品名称', prop: 'name' },
    { label: '产品类型', prop: 'type' },
    { label: '产品描述', prop: 'description' },
    { 
      label: '是否开启', 
      prop: 'enabled',
      formatter: (row) => row.enabled === 'true' ? '是' : '否',
      component: 'switch',
      activeValue: 'true',
      inactiveValue: 'false',
      activeText: '是',
      inactiveText: '否'
    }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增产品授权',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '产品名称',
          field: 'name',
          rules: [{ required: true, message: '请输入产品名称' }]
        },
        {
          type: 'input',
          label: '产品类型',
          field: 'type',
          rules: [{ required: true, message: '请输入产品类型' }]
        },
        {
          type: 'textarea',
          label: '产品描述',
          field: 'description',
          placeholder: '请输入产品描述'
        },
        {
          type: 'textarea',
          label: '路由配置',
          field: 'routeConfig',
          placeholder: '请输入路由配置'
        },
        {
          type: 'textarea',
          label: '配置信息',
          field: 'config',
          placeholder: '请输入配置信息'
        },
        {
          type: 'switch',
          label: '是否开启',
          field: 'enabled',
          activeValue: 'true',
          inactiveValue: 'false',
          activeText: '是',
          inactiveText: '否',
          rules: [{ required: true, message: '请选择是否开启' }]
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      if (params.id) {
        await editBaseProductAuthorization(params)
        SLMessage.success('修改成功')
      } else {
        await addBaseProductAuthorization(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  // 重置所有表单字段为可编辑状态
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  
  // 恢复默认按钮配置
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'
  
  // 恢复提交函数
  DialogFormConfig.submit = async (params) => {
    try {
      if (params.id) {
        await editBaseProductAuthorization(params)
        SLMessage.success('修改成功')
      } else {
        await addBaseProductAuthorization(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
}

const refreshData = async () => {
  try {
    const res = await getBaseProductAuthorizationList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}


// 新增/编辑
const handleAdd = (row) => {
  resetDialogConfig()
  if (row) {
    DialogFormConfig.title = '编辑产品授权'
    DialogFormConfig.defaultValue = { ...row }
  } else {
    DialogFormConfig.title = '新增产品授权'
    DialogFormConfig.defaultValue = {}
  }
  refDialogForm.value?.openDialog()
}

const handleDetail = async (row) => {
  try {
    const res = await getBaseProductAuthorizationDetail(row.id)
    const detailData = res.data?.data || res
    
    resetDialogConfig()
    DialogFormConfig.title = '产品授权详情'
    DialogFormConfig.defaultValue = { ...detailData }
    DialogFormConfig.group[0].fields.forEach(field => {
      field.disabled = true
    })
    DialogFormConfig.showSubmit = false
    DialogFormConfig.cancelText = '关闭'
    refDialogForm.value?.openDialog()
  } catch (error) {
    SLMessage.error('获取详情失败')
  }
}

const handleDelete = (row) => {
  SLConfirm('确定删除？', '删除提示')
    .then(async () => {
      const ids = row ? [row.id] : TableConfig.selectList?.map(item => item.id) || []
      if (!ids.length) {
        SLMessage.warning('请选择要删除的数据')
        return
      }
      await deleteBaseProductAuthorization(ids)
      SLMessage.success('删除成功')
      refreshData()
    })
    .catch(() => {})
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  flex: 1;
  margin-top: 16px;
}
</style>