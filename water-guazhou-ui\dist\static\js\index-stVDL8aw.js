import{_}from"./CardTable-rdWOL4_6.js";import{_ as y}from"./CardSearch-CB_HNR-Q.js";import{d as g,b3 as h,b4 as v,c as m,r as i,D as k,u as C,b5 as D,am as S,o as x,f as F,n as P,q as f,i as d,g as R}from"./index-r0dFAfgr.js";import{f as Y}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const w={class:"wrapper"},E=g({__name:"index",setup(T){const p=h(),s=v(),r=m(),c=[{label:"未读",value:"0",color:"#409EFF"},{label:"已读",value:"1",color:"#67C23A"}],n=i({type:"0"}),u=i({defaultParams:{type:s.params.id||"0"},filters:[{type:"select",field:"type",label:"消息类型",options:[{label:"待办",value:"0"},{label:"报警",value:"1"},{label:"通知",value:"2"}],onChange:e=>{n.type=e}},{type:"date",label:"起始时间",field:"beginTime"},{type:"date",label:"结束时间",field:"endTime"},{type:"select",field:"status",label:"消息状态",options:[{label:"未读",value:"0"},{label:"已读",value:"1"}]},{type:"input",field:"fromName",label:"发送人名称"},{type:"input",field:"topic",label:"主题"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>l()},{perm:!0,text:"重置",iconifyIcon:"material-symbols:refresh",click:()=>{var e;(e=r.value)==null||e.resetForm(),l()}}]}]}),o=i({dataList:[],operationWidth:220,columns:[{label:"消息编号",prop:"code"},{label:"消息主题",prop:"topic"},{label:"消息内容",prop:"content"},{label:"消息发送人",prop:"fromUser"},{label:"消息发送时间",prop:"time",formatter:e=>Y(e.time,"YYYY-MM-DD HH:mm:ss")},{label:"消息状态",prop:"status",tag:!0,tagColor:e=>{var a;return((a=c.find(t=>t.value===e.status))==null?void 0:a.color)||""},formatter:e=>{var a;return(a=c.find(t=>t.value===e.status))==null?void 0:a.label}}],pagination:{refreshData:({page:e,size:a})=>{o.pagination.page=e,o.pagination.limit=a,l()}},operations:[{perm:!0,text:"详情",type:"success",click:e=>{switch(n.type){case"0":p.push({name:"defaultRoute_GDFP"});break;case"1":p.push({name:"defaultRoute_BJZX"});break;case"2":p.push({name:"defaultRoute_YWCL"});break}}}]});m([]);const l=()=>{var a;const e={size:o.pagination.limit||20,page:o.pagination.page||1,to:k(C().id),...((a=r.value)==null?void 0:a.queryParams)||{}};n.type=e.type,D(e).then(t=>{o.dataList=t.data.data.data||[],o.pagination.total=t.data.data.total||0})};return S(s,()=>{var e,a;u.defaultParams={...((e=r.value)==null?void 0:e.queryParams)||{},type:s.params.id||"0"},n.type=s.params.id||"0",(a=r.value)==null||a.resetForm(),l()}),x(()=>{l()}),F(()=>{l()}),(e,a)=>{const t=y,b=_;return R(),P("div",w,[f(t,{ref_key:"refSearch",ref:r,config:d(u)},null,8,["config"]),f(b,{class:"card-table",config:d(o)},null,8,["config"])])}}});export{E as default};
