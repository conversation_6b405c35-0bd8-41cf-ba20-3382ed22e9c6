<template>
  <DialogForm ref="refDialog" :config="DialogFormConfig"></DialogForm>
</template>
<script lang="ts" setup>
import { PostGisFileBatch } from '@/api/mapservice/engineeringDocuments';
import { useGisStore } from '@/store';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { EPipeFileStatus } from '../config';

const emit = defineEmits(['uploaded']);
const props = defineProps<{ 
  objectid?: number;      // 关联的设施ID
  layerid?: number;       // 图层ID
  layername?: string;     // 图层名称 - 新增
  geo?: any;              // 要素的矢量信息 - 新增
}>();
const refDialog = ref<IDialogFormIns>();
const state = reactive<{
  image: any[];
  video: any[];
  audio: any[];
  file: any[];
}>({
  image: [],
  video: [],
  audio: [],
  file: []
});

// 获取父组件的PipeMedias组件引用
const pipeMediasRef = inject('pipeMediasRef', null);

// 安全地调用PipeMedias组件的方法
const safeCallPipeMedias = (methodName: string, ...args: any[]) => {
  if (pipeMediasRef && pipeMediasRef.value && typeof pipeMediasRef.value[methodName] === 'function') {
    return pipeMediasRef.value[methodName](...args);
  }
  console.warn(`PipeMedias组件的${methodName}方法不可用`);
  return null;
};

const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '图档上传',
  dialogWidth: 500,
  group: [
    {
      fields: [
        {
          type: 'image',
          label: '图片',
          field: 'image',
          accept: '.png,.jpg',
          handleSuccess: (list: any[]) => handleFileUploaded('image', list)
        },
        {
          type: 'file',
          label: '录音',
          field: 'audio',
          accept:
            '.avi,.wav,.weba,.webm,.oga,.ogx,.opus,mpeg,.mp4,.mp3,.aac,.3gp,.3gp2',
          handleSuccess: (list: any[]) => handleFileUploaded('audio', list)
        },
        {
          type: 'file',
          label: '视频',
          field: 'video',
          accept: '.avi,.webm,.oga,.ogx,.opus,.mpeg,.mp4',
          handleSuccess: (list: any[]) => handleFileUploaded('video', list)
        },
        {
          type: 'file',
          label: '文档',
          field: 'file',
          handleSuccess: (list: any[]) => handleFileUploaded('file', list)
        },
        { type: 'textarea', label: '备注', field: 'remark' }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {},
  submit: (params) => {
    // 判断是否有图层名称
    if (!props.layername) {
      SLMessage.error('上传失败：缺少图层信息');
      return;
    }
    
    refDialog.value?.closeDialog();
    SLConfirm('确定上传?', '提示')
      .then(async () => {
        try {
          // 构建图层信息对象，此时已确保layername不为undefined
          const layerData = {
            layerid: props.layerid,
            layername: props.layername as string
          };
          
          const data = [
            ...filterFileListToUploadObj('image', layerData, params),
            ...filterFileListToUploadObj('video', layerData, params),
            ...filterFileListToUploadObj('audio', layerData, params),
            ...filterFileListToUploadObj('file', layerData, params)
          ];
          debugger
          const res = await PostGisFileBatch(data);
          if (res.data.code === 200) {
            SLMessage.success('上传成功');
            refDialog.value?.closeDialog();
            
            // 转换为PipeMedias组件需要的格式
            const mediaData = {
              image: data.filter(item => item.fileType === 'image').map(item => ({
                name: item.name,
                file: item.file
              })),
              video: data.filter(item => item.fileType === 'video').map(item => ({
                name: item.name,
                file: item.file
              })),
              audio: data.filter(item => item.fileType === 'audio').map(item => ({
                name: item.name,
                file: item.file
              })),
              file: data.filter(item => item.fileType === 'file').map(item => ({
                name: item.name,
                file: item.file
              }))
            };
            
            // 调用PipeMedias的addMediaFiles方法
            safeCallPipeMedias('addMediaFiles', mediaData);
            
            emit('uploaded');
          } else {
            SLMessage.error('上传失败');
            console.log(res.data.message);
          }
        } catch (error) {
          SLMessage.error('上传失败');
          console.log(error);
        }
      })
      .catch(() => {
        // 用户取消
      });
  }
});

const filterFileListToUploadObj = (
  type: string,
  layerData: { layername: string | undefined, layerid?: number | undefined },
  params: any
) => {
  // 确保layerData.layername有值
  const deviceType = layerData.layername || 'unknown';
  
  return (
    state[type]?.map((item) => {
      return {
        name: item.name,
        fileType: type,
        deviceType: deviceType,
        deviceCode: props.objectid,
        remark: params.remark,
        // 添加要素的矢量信息，如果props.geo存在则使用，否则为空字符串
        geo: JSON.stringify(props.geo),
        file: item.url,
        status: EPipeFileStatus.正常
      };
    }) || []
  );
};

const openDialog = () => {
  DialogFormConfig.title = `图档上传(${props.objectid || ''})`;
  state.image = [];
  state.video = [];
  state.audio = [];
  state.file = [];
  refDialog.value?.openDialog();
};

const handleFileUploaded = (type: string, list: any[]) => {
  console.log(type, list);
  
  // 为每个文件添加地理信息
  if (props.geo) {
    list = list.map(item => {
      return {
        ...item,
        geoInfo: props.geo
      };
    });
  }
  
  state[type] = list || [];
};

defineExpose({
  openDialog
});
</script>
<style lang="scss" scoped></style>
