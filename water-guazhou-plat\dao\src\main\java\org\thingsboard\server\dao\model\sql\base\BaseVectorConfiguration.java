package org.thingsboard.server.dao.model.sql.base;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公共管理平台-矢量数据配置对象 base_vector_configuration
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@ApiModel(value = "矢量数据配置", description = "公共管理平台-矢量数据配置实体类")
@Data
public class BaseVectorConfiguration {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 矢量数据名称
     */
    @ApiModelProperty(value = "矢量数据名称")
    private String name;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    private String type;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    private String source;

    /**
     * 刷新频率（秒）
     */
    @ApiModelProperty(value = "刷新频率（秒）")
    private String refreshInterval;

    /**
     * 3D模型url
     */
    @ApiModelProperty(value = "3D模型url")
    private String modelUrl;

    /**
     * 模型缩放比例
     */
    @ApiModelProperty(value = "模型缩放比例")
    private String scale;

    /**
     * 初始旋转角度
     */
    @ApiModelProperty(value = "初始旋转角度")
    private String rotation;

    /**
     * 细节层次逻辑
     */
    @ApiModelProperty(value = "细节层次逻辑")
    private String lodStrategy;
}
