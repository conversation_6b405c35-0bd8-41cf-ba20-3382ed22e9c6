<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.thingsboard.server.dao.sql.revenue.CopyDataReadMeterDataMapper">

    <select id="findAllByTime" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataReadMeterData">
        select id, cust_code as userCode, meter_code as meterId, send_time as dataTime, read_data as thisReadWater, last_read_data as lastReadWater,
               ym, tenant_id, append_water, total_water
               from revenue.tb_meter_base_number where create_time between #{start} and #{end}
    </select>

    <select id="sumByTime" resultType="java.math.BigDecimal">
        select ifnull(sum(total_water), 0)
        from tb_pipe_partition c
        left join tb_pipe_partition_cust b on c.id = b.partition_id
        left join revenue.tb_pipe_copy_data_read_meter_data a on b.cust_code = a.user_code
        where c.status = '5' and c.type = '2' and a.this_read_date &gt;= to_timestamp(#{start} / 1000) and a.this_read_date &lt; to_timestamp(#{end} / 1000)
          and a.tenant_id = #{tenantId}
    </select>

    <select id="sumByPartitionId" resultType="com.alibaba.fastjson.JSONObject">
        select parti.id as id, parti.name as name, ifnull(sum(total_water), 0) as total
        from tb_pipe_partition parti
        left join tb_pipe_partition_cust partiton_cust on parti.id = partiton_cust.partition_id
        left join revenue.tb_water_meter twm on partiton_cust.cust_code = twm.cust_code
        left join revenue.tb_pipe_copy_data_read_meter_data a on a.meter_id = twm.meter_code
        <where>
            parti.tenant_id = #{tenantId} and a.total_water is not null and a.total_water &gt; 0 and a.total_water &lt; 3000
            <if test="partitionIdList != null and partitionIdList.size() > 0">
                and parti.id in
                <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="start != null">
                and a.this_read_date &gt;= to_timestamp(#{start} / 1000) and a.this_read_date &lt; to_timestamp(#{end} / 1000)
            </if>
            <if test="partitionType != null and partitionType != ''">
                and parti.type = #{partitionType}
            </if>
        </where>
        group by parti.id, parti.name
    </select>

    <select id="getListByPartitionId" resultType="org.thingsboard.server.dao.model.sql.revenue.CopyDataReadMeterData">
        select *
        from tb_pipe_partition parti
        left join tb_pipe_partition_cust partiton_cust on parti.id = partiton_cust.partition_id
        left join revenue.tb_water_meter twm on partiton_cust.cust_code = twm.cust_code and partiton_cust.tenant_id = twm.tenant_id
        left join revenue.tb_pipe_copy_data_read_meter_data a on a.meter_id = twm.meter_code and a.tenant_id = partiton_cust.tenant_id
        <where>
            parti.tenant_id = #{tenantId}
            <if test="start != null">
                and a.this_read_date &gt;= to_timestamp(#{start} / 1000) and a.this_read_date &lt; to_timestamp(#{end} / 1000)
            </if>
            <if test="partitionIdList != null and partitionIdList.size() > 0">
                and parti.id in
                <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="partitionType != null and partitionType != ''">
                and parti.type = #{partitionType}
            </if>
        </where>
    </select>
    <select id="getListByCustCodeList" resultType="org.thingsboard.server.dao.model.DTO.ReadMeterDataDTO">
        select meter.cust_code as userCode, a.*
        from revenue.tb_pipe_copy_data_read_meter_data a
        left join revenue.tb_water_meter meter on a.meter_id = meter.meter_code
        <where>
            meter.tenant_id = #{tenantId}
            <if test="start != null">
                and a.this_read_date &gt;= to_timestamp(#{start} / 1000) and a.this_read_date &lt; to_timestamp(#{end} / 1000)
            </if>
        </where>
        and meter.cust_code in
        <foreach collection="custCodeList" open="(" item="item" close=")" separator=",">
            #{item}
        </foreach>
        order by a.data_time
    </select>

</mapper>