package org.thingsboard.server.dao.model.sql.workOrder;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.workOrder.NewlyWorkOrderMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class WorkOrderCollaboration {
    @TableId
    // id
    private String id;

    // 需要协作的工单ID
    @ParseViaMapper(NewlyWorkOrderMapper.class)
    private String orderId;

    // 协作工单发起类型。直接发起工单协作/申请协作
    private String type;

    // 申请原因
    private String remark;

    // 申请人ID 错！ 是审核人id
    @ParseUsername
    private String userId;

    // 申请时间
    private Date time;

    // 状态。待审核/通过/未通过
    private WorkOrderCollaborationStatus status;

    // 新协作工单元数据。JSON格式的需要生成的新的协作工单的数据。
    private String additionalInfo;

    // 租户ID
    private String tenantId;

    private transient String processRemark;

}