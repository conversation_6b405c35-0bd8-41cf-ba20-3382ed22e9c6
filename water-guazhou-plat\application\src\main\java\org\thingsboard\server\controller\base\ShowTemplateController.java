package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.ShowTemplate;
import org.thingsboard.server.dao.settings.ShowTemplateService;

import java.util.List;

@RestController
@RequestMapping("api/showTemplate")
public class ShowTemplateController extends BaseController {

    @Autowired
    private ShowTemplateService showTemplateService;

    @GetMapping
    public List<ShowTemplate> list(@RequestParam String type) throws ThingsboardException {
        TenantId tenantId = getTenantId();

        return showTemplateService.findList(tenantId, type);
    }

    @PostMapping
    public ShowTemplate saveOne(@RequestParam ShowTemplate showTemplate) throws ThingsboardException {
        showTemplate.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        showTemplateService.saveOne(showTemplate);
        return showTemplate;
    }

    @PostMapping("saveAll")
    public List<ShowTemplate> saveAll(@RequestBody List<ShowTemplate> entityList) throws ThingsboardException {
        TenantId tenantId = getTenantId();
        for (ShowTemplate showTemplate : entityList) {
            showTemplate.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
        }
        showTemplateService.saveAll(entityList);
        return entityList;
    }

}
