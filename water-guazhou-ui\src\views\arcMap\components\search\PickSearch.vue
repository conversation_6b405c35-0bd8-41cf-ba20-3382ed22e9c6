<!-- 点选查询 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="点选查询1"
    :windows="state.windows"
    @map-loaded="onMaploaded"
  >
    <template #right-title>
      <SchemeHeader
        :title="'点选查询'"
        @scheme-click="scheme.openManagerDialog"
      ></SchemeHeader>
    </template>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <FormTable :config="TableConfig"></FormTable>
    <el-button
      v-if="state.gisSaveScheme"
      style="width: 100%; margin-top: 20px"
      type="primary"
      @click="handleSaveScheme"
    >
      保存方案
    </el-button>
    <SchemeManage
      :ref="scheme.getSchemeManageRef"
      :type="scheme.schemeType.value"
      @row-click="handleUseScheme"
    ></SchemeManage>
    <SaveScheme
      :ref="scheme.getSaveSchemeRef"
      @submit="handleSchemeSubmit"
    ></SaveScheme>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Graphic from "@arcgis/core/Graphic.js";
import Point from "@arcgis/core/geometry/Point";
import Multipoint from "@arcgis/core/geometry/Multipoint";
import Polyline from "@arcgis/core/geometry/Polyline";
import Polygon from "@arcgis/core/geometry/Polygon";
import Extent from '@arcgis/core/geometry/Extent'
import {
  excuteIdentify,excuteIdentifyByGeoserver,
  setSymbol,
  getGraphicLayer,
  getSubLayerIds,
  initIdentifyParams,
  setMapCursor,
  gotoAndHighLight,
  getGeometryCenter
} from '@/utils/MapHelper'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../common/RightDrawerMap.vue'
import SchemeManage from './Scheme/SchemeManage.vue'
import SaveScheme from './Scheme/SaveScheme.vue'
import { useScheme } from '@/hooks/arcgis/useScheme'
import SchemeHeader from './Scheme/SchemeHeader.vue'

const scheme = useScheme('pick')
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()
let graphicPair = {};
const state = reactive<{
  layerInfos: any[]
  layerIds: any[]
  windows: IArcMarkerProps[]
  gisSaveScheme?: boolean
}>({
  layerInfos: [],
  layerIds: [],
  windows: [],
  gisSaveScheme: window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme
})
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
  mapClick?: any
  identifyResults: any[]
  mapPoint?: __esri.Point
  mapExtent?: __esri.Extent
} = {
  view: undefined,
  identifyResults: []
}
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: false,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
        }
      ]
    },
    {
      fieldset: {
        desc: '点击地图进行查询'
      },
      fields: []
    }
  ]
})
const TableConfig = reactive<ITable>({
  height: 'none',
  columns: [
    { label: '类型', prop: 'layerName' },
    { label: '编号', prop: GIS_SERVER_SWITCH ? 'OBJECTID' : '新编号' }
  ],
  pagination: {
    hide: true
  },
  handleRowClick: async row => {
    TableConfig.currentRow = row
    let result;
    if(GIS_SERVER_SWITCH){
      result = staticState.identifyResults.find(item => item.properties.OBJECTID === row.OBJECTID)
      await gotoAndHighLight(staticState.view, graphicPair[row.OBJECTID])
    }else{
      result = staticState.identifyResults.find(item => item.feature.attributes.OBJECTID === row.OBJECTID)
      await gotoAndHighLight(staticState.view, result?.feature)
    }
    // openPop(row.OBJECTID)
    refMap.value?.openPop(row.OBJECTID)
  },
  dataList: []
})
const getLayerInfo = () => {
  if(GIS_SERVER_SWITCH){
    const field = FormConfig.group[0].fields[0] as IFormTree
    const layerInfo = staticState.view?.layerViews.items[0].layer.sublayers;
    let layers = layerInfo.items.map(item => {
      return {
        label: item.name,
        value: item.name,
        // data: item
      }
    });
    field.options =  layers;//[{ label: '管线类', value: -2, children: layers }]
    refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
  }else{
    state.layerIds = getSubLayerIds(staticState.view)
    queryLayerClassName(state.layerIds).then(layerInfo => {
      state.layerInfos = layerInfo.data?.result?.rows || []
      const field = FormConfig.group[0].fields[0] as IFormTree
      const points = state.layerInfos
        .filter(item => item.geometrytype === 'esriGeometryPoint')
        .map(item => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          }
        });
      const lines = state.layerInfos
        .filter(item => item.geometrytype === 'esriGeometryPolyline')
        .map(item => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          }
        });
      field
        && (field.options = [
          { label: '管点类', value: -1, children: points },
          { label: '管线类', value: -2, children: lines }
        ])
      refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
    })
  }
}
const pickPipe = () => {
  if (!staticState.view) return
  setMapCursor('crosshair')
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-pick',
    title: '查询结果'
  })
  staticState.mapClick = staticState.view?.on('click', async e => {
    await doIdentify(e)
  })
}
const queryPick = async (e: any, extent?: __esri.Extent) => {
  const mapPoint = e.mapPoint;
  if (!staticState.view) return
  staticState.mapExtent = extent ?? staticState.view.extent
  staticState.mapPoint = mapPoint
  const layerIds = refForm.value?.dataForm.layerid || []
  if (!layerIds.length) {
    SLMessage.warning('请先选择要查询的图层')
    return
  }
  refMap.value?.closeAllPop()
  let res;
  if(GIS_SERVER_SWITCH){
    // const layers = FormConfig.group[0].fields[0].options;
    // const wmsLayers = layers.filter(item => layerIds.includes(item.value));
    res = await excuteIdentifyByGeoserver(staticState.view, '/geoserver/guazhou/wms',layerIds.join(','), e);
    if (!res) {
      SLMessage.warning('没有相关数据');
      return;
    }
    res = res.data;
    staticState.graphicsLayer?.removeAll()
    staticState.identifyResults = res.results || res.features || []
    staticState.identifyResults.length > 50 && (staticState.identifyResults.length = 50)
  
    const tableData: any[] = []
    const features: __esri.Graphic[] = []
    state.windows = []
    graphicPair = {};
    staticState.identifyResults.map(item => {
      const geometry = geoJsonToArcGIS(item.geometry);
      let graphic = new Graphic({
        geometry,
        symbol: setSymbol(geometry.type)
      });
      features.push(graphic)
      graphicPair[item.properties.OBJECTID] = graphic;
      // const center = getGeometryCenter(item.geometry) || []
      state.windows.push({
        visible: true,
        longitude: item.geometry.type === 'MultiLineString' ? item.geometry.coordinates[0][0][0] : item.geometry.coordinates[0],
        latitude: item.geometry.type === 'MultiLineString' ? item.geometry.coordinates[0][0][1] : item.geometry.coordinates[1],
        title: item.id,
        attributes: {
          row: item.properties,
          id: item.properties.OBJECTID
        }
      })
      tableData.push({
        layerName: item.id,
        OBJECTID: item.properties.OBJECTID,
        // feature:graphic
      })
    })
    staticState.graphicsLayer?.addMany(features)
    TableConfig.dataList = tableData
    TableConfig.currentRow = tableData[0]
  }else{
    const queryParams = initIdentifyParams({
      layerIds: refForm.value?.dataForm.layerid || [],
      geometry: mapPoint,
      mapExtent: extent || staticState.view.extent
    })
    res = await excuteIdentify(
      window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
      queryParams
    )
    staticState.graphicsLayer?.removeAll()
    staticState.identifyResults = res.results || res.features || []
    staticState.identifyResults.length > 50 && (staticState.identifyResults.length = 50)
  
    const tableData: any[] = []
    const features: __esri.Graphic[] = []
    state.windows = []
    staticState.identifyResults.map(item => {
      tableData.push({
        layerName: item.layerName,
        layerId: item.layerId,
        ...item.feature.attributes
      })
      item.feature.symbol = setSymbol(item.feature.geometry.type)
      features.push(item.geometry)
      const center = getGeometryCenter(item.feature.geometry) || []
      state.windows.push({
        visible: false,
        x: center[0],
        y: center[1],
        title: item.layerName + '(' + item.feature.attributes['新编号'] + ')',
        attributes: {
          row: item.feature.attributes,
          id: item.feature.attributes.OBJECTID
        }
      })
    })
    staticState.graphicsLayer?.addMany(features)
    TableConfig.dataList = tableData
    TableConfig.currentRow = tableData[0]
    if (!res.results?.length) {
      SLMessage.warning('没有查询到设备')
    }
  }
}
const gotoPop = async (id?: string) => {
  id = id ?? TableConfig.currentRow?.OBJECTID
  if(GIS_SERVER_SWITCH){
    TableConfig.currentRow = TableConfig.dataList.find(item => item.OBJECTID === id)
    await gotoAndHighLight(staticState.view, graphicPair[id])
  }else{
    // id = id ?? TableConfig.currentRow?.OBJECTID
    await gotoAndHighLight(staticState.view, staticState.identifyResults[0]?.feature)
    TableConfig.currentRow = TableConfig.dataList.find(item => item.OBJECTID === id)
  }
  refMap.value?.openPop(id)
}
const doIdentify = async (e: any) => {
  if (!staticState.view) return
  try {
    await queryPick(e)
    await gotoPop()
    staticState.view.refresh();
  } catch (error) {
    staticState.view?.graphics.removeAll()
  }
}
const handleUseScheme = async (row: any) => {
  const detail = scheme.parseScheme(row)
  if (refForm.value?.dataForm) {
    refForm.value.dataForm.layerid = detail.layerid || []
  }
  if (detail.mapPoint) {
    const extent = detail.mapExtent
      ? new Extent({
        ...detail.mapExtent
      })
      : undefined
    const mapPoint = new Point({
      ...detail.mapPoint
    })

    if (mapPoint) {
      await queryPick({mapPoint}, extent)
      await gotoPop(detail.OBJECTID)
    }
  }
}
const handleSaveScheme = () => {
  if (!staticState.mapPoint || !staticState.identifyResults.length) {
    SLMessage.warning('请先在地图上进行点选操作')
    return
  }
  scheme.openSaveDialog()
}
const handleSchemeSubmit = params => {
  scheme.submitScheme({
    ...params,
    type: scheme.schemeType.value,
    detail: JSON.stringify({
      layerid: refForm.value?.dataForm.layerid || [],
      mapPoint: staticState.mapPoint,
      OBJECTID: TableConfig.currentRow?.OBJECTID,
      mapExtent: staticState?.mapExtent
    })
  })
}
const geoJsonToArcGIS = (geoJson) => {
  switch (geoJson.type) {
    case 'Point':
      return new Point({
        x: geoJson.coordinates[0],
        y: geoJson.coordinates[1],
        spatialReference: { wkid: 4326 } // 你可以设置适当的 spatialReference
      });
    case 'MultiPoint':
      return new Multipoint({
        points: geoJson.coordinates,
        spatialReference: { wkid: 4326 }
      });
    case 'LineString':
      return new Polyline({
        paths: [geoJson.coordinates],
        spatialReference: { wkid: 4326 }
      });
    case 'MultiLineString':
      return new Polyline({
        paths: geoJson.coordinates,
        spatialReference: { wkid: 4326 }
      });
    case 'Polygon':
      return new Polygon({
        rings: geoJson.coordinates,
        spatialReference: { wkid: 4326 }
      });
    case 'MultiPolygon':
      return new Polygon({
        // 将 MultiPolygon 转换为单个 Polygon 对象的 rings 数组
        rings: geoJson.coordinates.reduce((rings, polygon) => rings.concat(polygon), []),
        spatialReference: { wkid: 4326 }
      });
    default:
      console.error('Unsupported GeoJSON type:', geoJson.type);
      return null;
  }
}
const onMaploaded = view => {
  staticState.view = view
  setTimeout(() => {
    getLayerInfo()
    pickPipe()
  },1000)
}
onBeforeUnmount(() => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphicsLayer?.destroy()
  staticState.mapClick?.remove()
})
</script>
<style lang="scss" scoped></style>
