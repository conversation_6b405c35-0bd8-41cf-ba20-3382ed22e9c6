import{d as k,c as g,r as s,b as c,S as x,o as L,g as v,n as B,q as a,i as e,F as _,p as b,bh as h,G as R,b6 as T}from"./index-r0dFAfgr.js";import{_ as N}from"./CardTable-rdWOL4_6.js";import{_ as V}from"./CardSearch-CB_HNR-Q.js";import{C as q}from"./CollectDetail-CZJXrerj.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./Point-WxyopZva.js";import"./MapView-DaoQedLH.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./detail-CU6-qhMl.js";/* empty css                         */import"./index-CpGhZCTT.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./usePipeCollect-DNAtT5mx.js";import"./pipe-nogVzCHG.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./LayerHelper-Cn-iiqxI.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const F={class:"wrapper"},ur=k({__name:"CollectDelete",setup(M){const n=g(),w=s({filters:[{type:"input",label:"工程名称",field:"name"},{type:"input",label:"工程编号",field:"code"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"搜索",click:()=>p()},{perm:!0,text:"重置",click:()=>y()},{perm:!0,text:"删除",type:"danger",click:()=>l()}]}],defaultParams:{}}),o=s({columns:[{label:"工程编号",prop:"code"},{label:"工程名称",prop:"name"},{label:"竣工日期",prop:"finishDate"}],dataList:[],operations:[{perm:!0,text:"详情",click:t=>C(t)},{perm:!0,text:"删除",type:"danger",click:t=>l(t)}],pagination:{refreshData:({page:t,size:r})=>{o.pagination.page=t||1,o.pagination.limit=r||20,p()}}}),y=()=>{var t;(t=n.value)==null||t.resetForm()},l=async t=>{var m;if(!(t?[t.id]:((m=o.selectList)==null?void 0:m.map(i=>i.id))||[]).length){c.warning("请选择要删除的数据");return}x("确定删除？","提示信息").then(()=>{try{c.success("删除成功"),p()}catch(i){c.error("删除失败"),console.log(i)}}).catch(()=>{})},p=()=>{var t;try{const r={...(t=n.value)==null?void 0:t.queryParams,page:o.pagination.page,size:o.pagination.limit};console.log(r),o.dataList=[{id:"aaa",code:"aaa",name:"工程1"},{id:"bbb",code:"bbb",name:"工程2"}]}catch(r){console.log(r)}},f=g(),D=s({title:"",group:[],appendToBody:!1}),C=t=>{var r;o.currentRow=t,(r=f.value)==null||r.openDrawer()};return L(()=>{p()}),(t,r)=>{const m=V,i=N,S=T;return v(),B("div",F,[a(m,{ref_key:"refSearch",ref:n,config:e(w)},null,8,["config"]),a(i,{config:e(o),class:"card-table"},null,8,["config"]),a(S,{ref_key:"refDrawer",ref:f,config:e(D)},{title:_(()=>{var u,d;return[b("span",null,h((u=e(o).currentRow)==null?void 0:u.name),1),r[0]||(r[0]=R()),b("span",null,h((d=e(o).currentRow)==null?void 0:d.code),1)]}),default:_(()=>[a(q,{row:e(o).currentRow},null,8,["row"])]),_:1},8,["config"])])}}});export{ur as default};
