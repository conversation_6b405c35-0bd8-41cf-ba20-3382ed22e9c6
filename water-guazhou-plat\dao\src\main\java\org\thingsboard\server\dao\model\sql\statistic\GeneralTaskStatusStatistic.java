package org.thingsboard.server.dao.model.sql.statistic;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GeneralTaskStatusStatistic {
    // 总数
    private Integer total;

    // 指定状态数量
    private Integer count;

    // 状态占比
    private String percent;

    public GeneralTaskStatusStatistic(Integer count, Integer total) {
        this.total = total;
        this.count = count;
        this.percent = total == 0 ? "0" : String.format("%.2f",(double) count / total);
    }
}
