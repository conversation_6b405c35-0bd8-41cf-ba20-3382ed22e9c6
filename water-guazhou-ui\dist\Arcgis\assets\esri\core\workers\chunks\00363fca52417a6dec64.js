"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6368],{57435:(e,t,r)=>{r.d(t,{Z:()=>p});var i=r(43697),s=r(46791),o=r(70586),n=(r(80442),r(20102),r(92604),r(26258),r(87538)),a=r(5600),l=(r(75215),r(67676),r(52011));let u=class extends s.Z{constructor(e){super(e),this.getCollections=null}initialize(){this.own((0,n.EH)((()=>this._refresh())))}destroy(){this.getCollections=null}_refresh(){const e=(0,o.pC)(this.getCollections)?this.getCollections():null;if((0,o.Wi)(e))return void this.removeAll();let t=0;for(const r of e)(0,o.pC)(r)&&(t=this._processCollection(t,r));this.splice(t,this.length)}_createNewInstance(e){return new s.Z(e)}_processCollection(e,t){if(!t)return e;const r=this.itemFilterFunction?this.itemFilterFunction:e=>!!e;for(const i of t)if(i){if(r(i)){const t=this.indexOf(i,e);t>=0?t!==e&&this.reorder(i,e):this.add(i,e),++e}if(this.getChildrenFunction){const t=this.getChildrenFunction(i);if(Array.isArray(t))for(const r of t)e=this._processCollection(e,r);else e=this._processCollection(e,t)}}return e}};(0,i._)([(0,a.Cb)()],u.prototype,"getCollections",void 0),(0,i._)([(0,a.Cb)()],u.prototype,"getChildrenFunction",void 0),(0,i._)([(0,a.Cb)()],u.prototype,"itemFilterFunction",void 0),u=(0,i._)([(0,l.j)("esri.core.CollectionFlattener")],u);const p=u},23670:(e,t,r)=>{r.d(t,{G:()=>s});var i=r(20102);let s=class{constructor(e,t,r=""){this.major=e,this.minor=t,this._context=r}lessThan(e,t){return this.major<e||e===this.major&&this.minor<t}since(e,t){return!this.lessThan(e,t)}validate(e){if(this.major!==e.major){const t=this._context&&this._context+":",r=this._context&&this._context+" ";throw new i.Z(t+"unsupported-version",`Required major ${r}version is '${this.major}', but got '\${version.major}.\${version.minor}'`,{version:e})}}clone(){return new s(this.major,this.minor,this._context)}static parse(e,t=""){const[r,o]=e.split("."),n=/^\s*\d+\s*$/;if(!r||!r.match||!r.match(n))throw new i.Z((t&&t+":")+"invalid-version","Expected major version to be a number, but got '${version}'",{version:e});if(!o||!o.match||!o.match(n))throw new i.Z((t&&t+":")+"invalid-version","Expected minor version to be a number, but got '${version}'",{version:e});const a=parseInt(r,10),l=parseInt(o,10);return new s(a,l,t)}}},68668:(e,t,r)=>{r.d(t,{G:()=>a,w:()=>l});var i=r(66643),s=r(46791),o=r(83379),n=r(70586);async function a(e,t){return await e.load(),l(e,t)}async function l(e,t){const r=[],a=(...e)=>{for(const t of e)(0,n.Wi)(t)||(Array.isArray(t)?a(...t):s.Z.isCollection(t)?t.forEach((e=>a(e))):o.Z.isLoadable(t)&&r.push(t))};t(a);let l=null;if(await(0,i.UI)(r,(async e=>{const t=await(0,i.q6)(function(e){return"loadAll"in e&&"function"==typeof e.loadAll}(e)?e.loadAll():e.load());!1!==t.ok||l||(l=t)})),l)throw l.error;return e}},42033:(e,t,r)=>{r.d(t,{E:()=>s,_:()=>o});var i=r(70586);async function s(e,t){const{WhereClause:i}=await r.e(1534).then(r.bind(r,41534));return i.create(e,t)}function o(e,t){return(0,i.pC)(e)?(0,i.pC)(t)?`(${e}) AND (${t})`:e:t}},74654:(e,t,r)=>{r.d(t,{Z:()=>A});var i=r(43697),s=r(15923),o=r(20102),n=r(80442),a=r(70586),l=r(95330),u=r(17452),p=r(5600),d=(r(75215),r(67676),r(52011)),c=r(10158),y=r(11282),h=r(41818),f=(r(66577),r(34599)),m=r(14165),b=r(6570),g=r(5396),S=r(4967),v=r(69285),w=r(98732);function _(e,t){return t}function C(e,t,r,i){switch(r){case 0:return E(e,t+i,0);case 1:return"lowerLeft"===e.originPosition?E(e,t+i,1):function({translate:e,scale:t},r,i){return e[i]-r*t[i]}(e,t+i,1)}}function I(e,t,r,i){return 2===r?E(e,t,2):C(e,t,r,i)}function x(e,t,r,i){return 2===r?E(e,t,3):C(e,t,r,i)}function F(e,t,r,i){return 3===r?E(e,t,3):I(e,t,r,i)}function E({translate:e,scale:t},r,i){return e[i]+r*t[i]}class T{constructor(e){this._options=e,this.geometryTypes=["esriGeometryPoint","esriGeometryMultipoint","esriGeometryPolyline","esriGeometryPolygon"],this._previousCoordinate=[0,0],this._transform=null,this._applyTransform=_,this._lengths=[],this._currentLengthIndex=0,this._toAddInCurrentPath=0,this._vertexDimension=0,this._coordinateBuffer=null,this._coordinateBufferPtr=0,this._attributesConstructor=class{}}createFeatureResult(){return{fields:[],features:[]}}finishFeatureResult(e){if(this._options.applyTransform&&(e.transform=null),this._attributesConstructor=class{},this._coordinateBuffer=null,this._lengths.length=0,!e.hasZ)return;const t=(0,v.k)(e.geometryType,this._options.sourceSpatialReference,e.spatialReference);if(!(0,a.Wi)(t))for(const r of e.features)t(r.geometry)}createSpatialReference(){return{}}addField(e,t){const r=e.fields;(0,a.O3)(r),r.push(t);const i=r.map((e=>e.name));this._attributesConstructor=function(){for(const e of i)this[e]=null}}addFeature(e,t){e.features.push(t)}prepareFeatures(e){switch(this._transform=e.transform,this._options.applyTransform&&e.transform&&(this._applyTransform=this._deriveApplyTransform(e)),this._vertexDimension=2,e.hasZ&&this._vertexDimension++,e.hasM&&this._vertexDimension++,e.geometryType){case"esriGeometryPoint":this.addCoordinate=(e,t,r)=>this.addCoordinatePoint(e,t,r),this.createGeometry=e=>this.createPointGeometry(e);break;case"esriGeometryPolygon":this.addCoordinate=(e,t,r)=>this._addCoordinatePolygon(e,t,r),this.createGeometry=e=>this._createPolygonGeometry(e);break;case"esriGeometryPolyline":this.addCoordinate=(e,t,r)=>this._addCoordinatePolyline(e,t,r),this.createGeometry=e=>this._createPolylineGeometry(e);break;case"esriGeometryMultipoint":this.addCoordinate=(e,t,r)=>this._addCoordinateMultipoint(e,t,r),this.createGeometry=e=>this._createMultipointGeometry(e)}}createFeature(){return this._lengths.length=0,this._currentLengthIndex=0,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0,this._coordinateBuffer=null,this._coordinateBufferPtr=0,{attributes:new this._attributesConstructor}}allocateCoordinates(){}addLength(e,t,r){0===this._lengths.length&&(this._toAddInCurrentPath=t),this._lengths.push(t)}addQueryGeometry(e,t){const{queryGeometry:r,queryGeometryType:i}=t,s=(0,w.$g)(r.clone(),r,!1,!1,this._transform),o=(0,w.di)(s,i,!1,!1);e.queryGeometryType=i,e.queryGeometry={...o}}createPointGeometry(e){const t={x:0,y:0,spatialReference:e.spatialReference};return e.hasZ&&(t.z=0),e.hasM&&(t.m=0),t}addCoordinatePoint(e,t,r){const i=(0,a.s3)(this._transform,"transform");switch(t=this._applyTransform(i,t,r,0),r){case 0:e.x=t;break;case 1:e.y=t;break;case 2:"z"in e?e.z=t:e.m=t;break;case 3:e.m=t}}_transformPathLikeValue(e,t){let r=0;t<=1&&(r=this._previousCoordinate[t],this._previousCoordinate[t]+=e);const i=(0,a.s3)(this._transform,"transform");return this._applyTransform(i,e,t,r)}_addCoordinatePolyline(e,t,r){this._dehydratedAddPointsCoordinate(e.paths,t,r)}_addCoordinatePolygon(e,t,r){this._dehydratedAddPointsCoordinate(e.rings,t,r)}_addCoordinateMultipoint(e,t,r){0===r&&e.points.push([]);const i=this._transformPathLikeValue(t,r);e.points[e.points.length-1].push(i)}_createPolygonGeometry(e){return{rings:[[]],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_createPolylineGeometry(e){return{paths:[[]],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_createMultipointGeometry(e){return{points:[],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_dehydratedAddPointsCoordinate(e,t,r){0===r&&0==this._toAddInCurrentPath--&&(e.push([]),this._toAddInCurrentPath=this._lengths[++this._currentLengthIndex]-1,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0);const i=this._transformPathLikeValue(t,r),s=e[e.length-1];0===r&&(this._coordinateBufferPtr=0,this._coordinateBuffer=new Array(this._vertexDimension),s.push(this._coordinateBuffer)),this._coordinateBuffer[this._coordinateBufferPtr++]=i}_deriveApplyTransform(e){const{hasZ:t,hasM:r}=e;return t&&r?F:t?I:r?x:C}}var O=r(74889);let D=class extends s.Z{constructor(e){super(e),this.dynamicDataSource=null,this.fieldsIndex=null,this.gdbVersion=null,this.infoFor3D=null,this.pbfSupported=!1,this.queryAttachmentsSupported=!1,this.sourceSpatialReference=null,this.url=null}get parsedUrl(){return(0,u.mN)(this.url)}async execute(e,t){const r=await this.executeJSON(e,t);return this.featureSetFromJSON(e,r,t)}async executeJSON(e,t){const r=this._normalizeQuery(e),i=null!=e.outStatistics?.[0],s=(0,n.Z)("featurelayer-pbf-statistics"),o=!i||s;let a;if(this.pbfSupported&&o)try{a=await async function(e,t,r){const i=(0,y.en)(e),s={...r},o=m.Z.from(t),n=!o.quantizationParameters,{data:a}=await(0,f.qp)(i,o,new T({sourceSpatialReference:o.sourceSpatialReference,applyTransform:n}),s);return a}(this.url,r,t)}catch(e){if("query:parsing-pbf"!==e.name)throw e;this.pbfSupported=!1}return this.pbfSupported&&o||(a=await(0,S.F)(this.url,r,t)),this._normalizeFields(a.fields),a}async featureSetFromJSON(e,t,i){if(!this._queryIs3DObjectFormat(e)||(0,a.Wi)(this.infoFor3D)||!t.assetMaps||!t.features||!t.features.length)return O.Z.fromJSON(t);const{meshFeatureSetFromJSON:s}=await(0,l.Hl)(Promise.all([r.e(6481),r.e(6459),r.e(1932)]).then(r.bind(r,21932)),i);return s(e,this.infoFor3D,t)}executeForCount(e,t){return(0,h.P)(this.url,this._normalizeQuery(e),t)}executeForExtent(e,t){return async function(e,t,r){const i=(0,y.en)(e);return(0,f.Vr)(i,m.Z.from(t),{...r}).then((e=>({count:e.data.count,extent:b.Z.fromJSON(e.data.extent)})))}(this.url,this._normalizeQuery(e),t)}executeForIds(e,t){return(0,g.G)(this.url,this._normalizeQuery(e),t)}async executeRelationshipQuery(e,t){const[{default:i},{executeRelationshipQuery:s}]=await(0,l.Hl)(Promise.all([r.e(5935).then(r.bind(r,75935)),r.e(1073).then(r.bind(r,81073))]),t);return e=i.from(e),(this.gdbVersion||this.dynamicDataSource)&&((e=e.clone()).gdbVersion=e.gdbVersion||this.gdbVersion,e.dynamicDataSource=e.dynamicDataSource||this.dynamicDataSource),s(this.url,e,t)}async executeRelationshipQueryForCount(e,t){const[{default:i},{executeRelationshipQueryForCount:s}]=await(0,l.Hl)(Promise.all([r.e(5935).then(r.bind(r,75935)),r.e(1073).then(r.bind(r,81073))]),t);return e=i.from(e),(this.gdbVersion||this.dynamicDataSource)&&((e=e.clone()).gdbVersion=e.gdbVersion||this.gdbVersion,e.dynamicDataSource=e.dynamicDataSource||this.dynamicDataSource),s(this.url,e,t)}async executeAttachmentQuery(e,t){const{executeAttachmentQuery:i,fetchAttachments:s,processAttachmentQueryResult:o}=await(0,l.Hl)(r.e(540).then(r.bind(r,30540)),t),n=(0,y.en)(this.url);return o(n,await(this.queryAttachmentsSupported?i(n,e,t):s(n,e,t)))}async executeTopFeaturesQuery(e,t){const{executeTopFeaturesQuery:i}=await(0,l.Hl)(r.e(1158).then(r.bind(r,71158)),t);return i(this.parsedUrl,e,this.sourceSpatialReference,t)}async executeForTopIds(e,t){const{executeForTopIds:i}=await(0,l.Hl)(r.e(3992).then(r.bind(r,33992)),t);return i(this.parsedUrl,e,t)}async executeForTopExtents(e,t){const{executeForTopExtents:i}=await(0,l.Hl)(r.e(1790).then(r.bind(r,21790)),t);return i(this.parsedUrl,e,t)}async executeForTopCount(e,t){const{executeForTopCount:i}=await(0,l.Hl)(r.e(4371).then(r.bind(r,14371)),t);return i(this.parsedUrl,e,t)}_normalizeQuery(e){let t=m.Z.from(e);if(t.sourceSpatialReference=t.sourceSpatialReference||this.sourceSpatialReference,(this.gdbVersion||this.dynamicDataSource)&&(t=t===e?t.clone():t,t.gdbVersion=e.gdbVersion||this.gdbVersion,t.dynamicDataSource=e.dynamicDataSource?c.n.from(e.dynamicDataSource):this.dynamicDataSource),(0,a.pC)(this.infoFor3D)&&this._queryIs3DObjectFormat(e)){t=t===e?t.clone():t,t.formatOf3DObjects=null;for(const e of this.infoFor3D.queryFormats){if("3D_glb"===e){t.formatOf3DObjects=e;break}"3D_gltf"!==e||t.formatOf3DObjects||(t.formatOf3DObjects=e)}if(!t.formatOf3DObjects)throw new o.Z("query:unsupported-3d-query-formats","Could not find any supported 3D object query format. Only supported formats are 3D_glb and 3D_gltf");if((0,a.Wi)(t.outFields)||!t.outFields.includes("*")){t=t===e?t.clone():t,(0,a.Wi)(t.outFields)&&(t.outFields=[]);const{originX:r,originY:i,originZ:s,translationX:o,translationY:n,translationZ:l,scaleX:u,scaleY:p,scaleZ:d,rotationX:c,rotationY:y,rotationZ:h,rotationDeg:f}=this.infoFor3D.transformFieldRoles;t.outFields.push(r,i,s,o,n,l,u,p,d,c,y,h,f)}}return t}_normalizeFields(e){if((0,a.pC)(this.fieldsIndex)&&(0,a.pC)(e))for(const t of e){const e=this.fieldsIndex.get(t.name);e&&Object.assign(t,e.toJSON())}}_queryIs3DObjectFormat(e){return(0,a.pC)(this.infoFor3D)&&!0===e.returnGeometry&&"xyFootprint"!==e.multipatchOption&&!e.outStatistics}};(0,i._)([(0,p.Cb)({type:c.n})],D.prototype,"dynamicDataSource",void 0),(0,i._)([(0,p.Cb)()],D.prototype,"fieldsIndex",void 0),(0,i._)([(0,p.Cb)()],D.prototype,"gdbVersion",void 0),(0,i._)([(0,p.Cb)()],D.prototype,"infoFor3D",void 0),(0,i._)([(0,p.Cb)({readOnly:!0})],D.prototype,"parsedUrl",null),(0,i._)([(0,p.Cb)()],D.prototype,"pbfSupported",void 0),(0,i._)([(0,p.Cb)()],D.prototype,"queryAttachmentsSupported",void 0),(0,i._)([(0,p.Cb)()],D.prototype,"sourceSpatialReference",void 0),(0,i._)([(0,p.Cb)({type:String})],D.prototype,"url",void 0),D=(0,i._)([(0,d.j)("esri.tasks.QueryTask")],D);const A=D},54295:(e,t,r)=>{r.d(t,{V:()=>n});var i=r(43697),s=r(5600),o=(r(75215),r(67676),r(52011));const n=e=>{let t=class extends e{get apiKey(){return this._isOverridden("apiKey")?this._get("apiKey"):"portalItem"in this?this.portalItem?.apiKey:null}set apiKey(e){null!=e?this._override("apiKey",e):(this._clearOverride("apiKey"),this.clear("apiKey","user"))}};return(0,i._)([(0,s.Cb)({type:String})],t.prototype,"apiKey",null),t=(0,i._)([(0,o.j)("esri.layers.mixins.APIKeyMixin")],t),t}},7944:(e,t,r)=>{r.d(t,{O:()=>h});var i=r(43697),s=r(3172),o=r(95330),n=r(17452),a=r(23670),l=r(5600),u=(r(75215),r(67676),r(71715)),p=r(52011),d=r(6570),c=r(82971),y=r(21506);const h=e=>{let t=class extends e{constructor(){super(...arguments),this.capabilities=void 0,this.copyright=null,this.fullExtent=null,this.legendEnabled=!0,this.spatialReference=null,this.version=void 0,this._allLayersAndTablesPromise=null,this._allLayersAndTablesMap=null}readCapabilities(e,t){const r=t.capabilities&&t.capabilities.split(",").map((e=>e.toLowerCase().trim()));if(!r)return{operations:{supportsExportMap:!1,supportsExportTiles:!1,supportsIdentify:!1,supportsQuery:!1,supportsTileMap:!1},exportMap:null,exportTiles:null};const i=this.type,s="tile"!==i&&!!t.supportsDynamicLayers,o=r.includes("query"),n=r.includes("map"),l=!!t.exportTilesAllowed,u=r.includes("tilemap"),p=r.includes("data"),d="tile"!==i&&(!t.tileInfo||s),c="tile"!==i&&(!t.tileInfo||s),y="tile"!==i,h=t.cimVersion&&a.G.parse(t.cimVersion),f=h?.since(1,4)??!1,m=h?.since(2,0)??!1;return{operations:{supportsExportMap:n,supportsExportTiles:l,supportsIdentify:o,supportsQuery:p,supportsTileMap:u},exportMap:n?{supportsArcadeExpressionForLabeling:f,supportsSublayersChanges:y,supportsDynamicLayers:s,supportsSublayerVisibility:d,supportsSublayerDefinitionExpression:c,supportsCIMSymbols:m}:null,exportTiles:l?{maxExportTilesCount:+t.maxExportTilesCount}:null}}readVersion(e,t){let r=t.currentVersion;return r||(r=t.hasOwnProperty("capabilities")||t.hasOwnProperty("tables")?10:t.hasOwnProperty("supportedImageFormatTypes")?9.31:9.3),r}async fetchSublayerInfo(e,t){try{return await this.fetchAllLayersAndTables(t),this._allLayersAndTablesMap?.get(e)}catch{return}}async fetchAllLayersAndTables(e){await this.load(e),this._allLayersAndTablesPromise||(this._allLayersAndTablesPromise=(0,s.default)((0,n.mN)(this.url).path+"/layers",{responseType:"json",query:{f:"json",...this.customParameters,token:this.apiKey}}).then((e=>{this._allLayersAndTablesMap=new Map;for(const t of e.data.layers)this._allLayersAndTablesMap.set(t.id,t);return{result:e.data}}),(e=>({error:e}))));const t=await this._allLayersAndTablesPromise;if((0,o.k_)(e),"result"in t)return t.result;throw t.error}};return(0,i._)([(0,l.Cb)({readOnly:!0})],t.prototype,"capabilities",void 0),(0,i._)([(0,u.r)("service","capabilities",["capabilities","exportTilesAllowed","maxExportTilesCount","supportsDynamicLayers","tileInfo"])],t.prototype,"readCapabilities",null),(0,i._)([(0,l.Cb)({json:{read:{source:"copyrightText"}}})],t.prototype,"copyright",void 0),(0,i._)([(0,l.Cb)({type:d.Z})],t.prototype,"fullExtent",void 0),(0,i._)([(0,l.Cb)(y.id)],t.prototype,"id",void 0),(0,i._)([(0,l.Cb)({type:Boolean,json:{origins:{service:{read:{enabled:!1}}},read:{source:"showLegend"},write:{target:"showLegend"}}})],t.prototype,"legendEnabled",void 0),(0,i._)([(0,l.Cb)(y.C_)],t.prototype,"popupEnabled",void 0),(0,i._)([(0,l.Cb)({type:c.Z})],t.prototype,"spatialReference",void 0),(0,i._)([(0,l.Cb)({readOnly:!0})],t.prototype,"version",void 0),(0,i._)([(0,u.r)("version",["currentVersion","capabilities","tables","supportedImageFormatTypes"])],t.prototype,"readVersion",null),t=(0,i._)([(0,p.j)("esri.layers.mixins.ArcGISMapService")],t),t}},10343:(e,t,r)=>{r.d(t,{x:()=>g});var i=r(43697),s=r(46791),o=r(57435),n=r(20102),a=r(92604),l=r(17445),u=r(5600),p=(r(75215),r(67676),r(1153)),d=r(52011),c=r(31263),y=r(49867),h=r(32073);const f=a.Z.getLogger("esri.layers.TileLayer"),m=s.Z.ofType(y.Z);function b(e,t){e&&e.forEach((e=>{t(e),e.sublayers&&e.sublayers.length&&b(e.sublayers,t)}))}const g=e=>{let t=class extends e{constructor(...e){super(...e),this.allSublayers=new o.Z({getCollections:()=>[this.sublayers],getChildrenFunction:e=>e.sublayers}),this.sublayersSourceJSON={[c.s3.SERVICE]:{},[c.s3.PORTAL_ITEM]:{},[c.s3.WEB_SCENE]:{},[c.s3.WEB_MAP]:{}},this.addHandles((0,l.YP)((()=>this.sublayers),((e,t)=>this._handleSublayersChange(e,t)),l.Z_))}readSublayers(e,t){if(!t||!e)return;const{sublayersSourceJSON:r}=this,i=(0,c.M9)(t.origin);if(i<c.s3.SERVICE)return;if(r[i]={context:t,visibleLayers:e.visibleLayers||r[i].visibleLayers,layers:e.layers||r[i].layers},i>c.s3.SERVICE)return;this._set("serviceSublayers",this.createSublayersForOrigin("service").sublayers);const{sublayers:s,origin:o}=this.createSublayersForOrigin("web-document"),n=(0,p.vw)(this);n.setDefaultOrigin(o),this._set("sublayers",new m(s)),n.setDefaultOrigin("user")}findSublayerById(e){return this.allSublayers.find((t=>t.id===e))}createServiceSublayers(){return this.createSublayersForOrigin("service").sublayers}createSublayersForOrigin(e){const t=(0,c.M9)("web-document"===e?"web-map":e);let r=c.s3.SERVICE,i=this.sublayersSourceJSON[c.s3.SERVICE].layers,s=this.sublayersSourceJSON[c.s3.SERVICE].context,o=null;const n=[c.s3.PORTAL_ITEM,c.s3.WEB_SCENE,c.s3.WEB_MAP].filter((e=>e<=t));for(const e of n){const t=this.sublayersSourceJSON[e];(0,h.ac)(t.layers)&&(r=e,i=t.layers,s=t.context,t.visibleLayers&&(o={visibleLayers:t.visibleLayers,context:t.context}))}const a=[c.s3.PORTAL_ITEM,c.s3.WEB_SCENE,c.s3.WEB_MAP].filter((e=>e>r&&e<=t));let l=null;for(const e of a){const{layers:t,visibleLayers:r,context:i}=this.sublayersSourceJSON[e];t&&(l={layers:t,context:i}),r&&(o={visibleLayers:r,context:i})}const u=function(e,t){const r=[],i={};return e?(e.forEach((e=>{const s=new y.Z;if(s.read(e,t),i[s.id]=s,null!=e.parentLayerId&&-1!==e.parentLayerId){const t=i[e.parentLayerId];t.sublayers||(t.sublayers=[]),t.sublayers.unshift(s)}else r.unshift(s)})),r):r}(i,s),p=new Map,d=new Set;if(l)for(const e of l.layers)p.set(e.id,e);if(o?.visibleLayers)for(const e of o.visibleLayers)d.add(e);return b(u,(e=>{l&&e.read(p.get(e.id),l.context),o&&e.read({defaultVisibility:d.has(e.id)},o.context)})),{origin:(0,c.x3)(r),sublayers:new m({items:u})}}read(e,t){super.read(e,t),this.readSublayers(e,t)}_handleSublayersChange(e,t){t&&(t.forEach((e=>{e.parent=null,e.layer=null})),this.handles.remove("sublayers-owner")),e&&(e.forEach((e=>{e.parent=this,e.layer=this})),this.handles.add([e.on("after-add",(({item:e})=>{e.parent=this,e.layer=this})),e.on("after-remove",(({item:e})=>{e.parent=null,e.layer=null}))],"sublayers-owner"),"tile"===this.type&&this.handles.add(e.on("before-changes",(e=>{f.error(new n.Z("tilelayer:sublayers-non-modifiable","ISublayer can't be added, moved, or removed from the layer's sublayers",{layer:this})),e.preventDefault()})),"sublayers-owner"))}};return(0,i._)([(0,u.Cb)({readOnly:!0})],t.prototype,"allSublayers",void 0),(0,i._)([(0,u.Cb)({readOnly:!0,type:s.Z.ofType(y.Z)})],t.prototype,"serviceSublayers",void 0),(0,i._)([(0,u.Cb)({value:null,type:m,json:{read:!1,write:{allowNull:!0,ignoreOrigin:!0}}})],t.prototype,"sublayers",void 0),(0,i._)([(0,u.Cb)({readOnly:!0})],t.prototype,"sublayersSourceJSON",void 0),t=(0,i._)([(0,d.j)("esri.layers.mixins.SublayersOwner")],t),t}},70082:(e,t,r)=>{r.d(t,{Z:()=>d});var i=r(43697),s=r(2368),o=r(35454),n=r(96674),a=r(5600),l=(r(75215),r(67676),r(52011));const u=new o.X({esriFeatureEditToolAutoCompletePolygon:"auto-complete-polygon",esriFeatureEditToolCircle:"circle",esriFeatureEditToolEllipse:"ellipse",esriFeatureEditToolFreehand:"freehand",esriFeatureEditToolLine:"line",esriFeatureEditToolNone:"none",esriFeatureEditToolPoint:"point",esriFeatureEditToolPolygon:"polygon",esriFeatureEditToolRectangle:"rectangle",esriFeatureEditToolArrow:"arrow",esriFeatureEditToolTriangle:"triangle",esriFeatureEditToolLeftArrow:"left-arrow",esriFeatureEditToolRightArrow:"right-arrow",esriFeatureEditToolUpArrow:"up-arrow",esriFeatureEditToolDownArrow:"down-arrow"});let p=class extends((0,s.J)(n.wq)){constructor(e){super(e),this.name=null,this.description=null,this.drawingTool=null,this.prototype=null,this.thumbnail=null}};(0,i._)([(0,a.Cb)({json:{write:!0}})],p.prototype,"name",void 0),(0,i._)([(0,a.Cb)({json:{write:!0}})],p.prototype,"description",void 0),(0,i._)([(0,a.Cb)({json:{read:u.read,write:u.write}})],p.prototype,"drawingTool",void 0),(0,i._)([(0,a.Cb)({json:{write:!0}})],p.prototype,"prototype",void 0),(0,i._)([(0,a.Cb)({json:{write:!0}})],p.prototype,"thumbnail",void 0),p=(0,i._)([(0,l.j)("esri.layers.support.FeatureTemplate")],p);const d=p},16451:(e,t,r)=>{r.d(t,{Z:()=>y});var i=r(43697),s=r(2368),o=r(96674),n=r(5600),a=(r(75215),r(67676),r(71715)),l=r(52011),u=r(30556),p=r(72729),d=r(70082);let c=class extends((0,s.J)(o.wq)){constructor(e){super(e),this.id=null,this.name=null,this.domains=null,this.templates=null}readDomains(e){const t={};for(const r of Object.keys(e))t[r]=(0,p.im)(e[r]);return t}writeDomains(e,t){const r={};for(const t of Object.keys(e))e[t]&&(r[t]=e[t]?.toJSON());t.domains=r}};(0,i._)([(0,n.Cb)({json:{write:!0}})],c.prototype,"id",void 0),(0,i._)([(0,n.Cb)({json:{write:!0}})],c.prototype,"name",void 0),(0,i._)([(0,n.Cb)({json:{write:!0}})],c.prototype,"domains",void 0),(0,i._)([(0,a.r)("domains")],c.prototype,"readDomains",null),(0,i._)([(0,u.c)("domains")],c.prototype,"writeDomains",null),(0,i._)([(0,n.Cb)({type:[d.Z],json:{write:!0}})],c.prototype,"templates",void 0),c=(0,i._)([(0,l.j)("esri.layers.support.FeatureType")],c);const y=c},56765:(e,t,r)=>{r.d(t,{Z:()=>p});var i,s=r(43697),o=r(46791),n=r(96674),a=r(5600),l=(r(75215),r(67676),r(52011));let u=i=class extends n.wq{constructor(e){super(e),this.floorField=null,this.viewAllMode=!1,this.viewAllLevelIds=new o.Z}clone(){return new i({floorField:this.floorField,viewAllMode:this.viewAllMode,viewAllLevelIds:this.viewAllLevelIds})}};(0,s._)([(0,a.Cb)({type:String,json:{write:!0}})],u.prototype,"floorField",void 0),(0,s._)([(0,a.Cb)({json:{read:!1,write:!1}})],u.prototype,"viewAllMode",void 0),(0,s._)([(0,a.Cb)({json:{read:!1,write:!1}})],u.prototype,"viewAllLevelIds",void 0),u=i=(0,s._)([(0,l.j)("esri.layers.support.LayerFloorInfo")],u);const p=u},49867:(e,t,r)=>{r.d(t,{Z:()=>X});var i,s=r(43697),o=(r(66577),r(51773)),n=(r(16050),r(12501),r(28756),r(92271),r(72529),r(5499),r(84382),r(81571),r(91423),r(32400)),a=r(3172),l=r(9790),u=r(46791),p=r(20102),d=r(3920),c=r(80442),y=r(10699),h=r(22974),f=r(83379),m=r(92604),b=r(70586),g=r(16453),S=r(42033),v=r(17452),w=r(5600),_=r(90578),C=r(71715),I=r(52011),x=r(30556),F=r(75215),E=r(31263),T=r(1153),O=r(74654),D=r(16451),A=r(1231),P=r(99514),L=r(54306),R=r(30707),j=r(56765),M=r(72064),Z=r(10158),V=r(42843),N=r(56545),q=r(14165),Q=r(32163),G=r(6570),k=r(86973);function z(e){return null!=e&&"esriSMS"===e.type}function U(e,t,r){const i=this.originIdOf(t)>=(0,E.M9)(r.origin);return{ignoreOrigin:!0,allowNull:i,enabled:!!r&&"map-image"===r.layer?.type&&(r.writeSublayerStructure||i)}}function B(e,t,r){return{enabled:!!r&&"tile"===r.layer?.type&&this._isOverridden(t)}}function J(e,t,r){return{ignoreOrigin:!0,enabled:r&&r.writeSublayerStructure||!1}}function W(e,t,r){return{ignoreOrigin:!0,enabled:!!r&&(r.writeSublayerStructure||this.originIdOf(t)>=(0,E.M9)(r.origin))}}let H=0;const $=new Set;$.add("layer"),$.add("parent"),$.add("loaded"),$.add("loadStatus"),$.add("loadError"),$.add("loadWarnings");let K=i=class extends((0,d.p)((0,g.R)((0,y.IG)(f.Z)))){constructor(e){super(e),this.capabilities=void 0,this.fields=null,this.fullExtent=null,this.geometryType=null,this.globalIdField=null,this.legendEnabled=!0,this.objectIdField=null,this.popupEnabled=!0,this.popupTemplate=null,this.sourceJSON=null,this.title=null,this.typeIdField=null,this.types=null,this._lastParsedUrl=null}async load(e){return this.addResolvingPromise((async()=>{const{layer:t,source:r,url:i}=this;if(!t&&!i)throw new p.Z("sublayer:missing-layer","Sublayer can't be loaded without being part of a layer",{sublayer:this});let s=null;if(!t||this.originIdOf("url")>E.s3.SERVICE||"data-layer"===r?.type)s=(await(0,a.default)(i,{responseType:"json",query:{f:"json"},...e})).data;else{let i=this.id;"map-layer"===r?.type&&(i=r.mapLayerId),s=await t.fetchSublayerInfo(i,e)}s&&(this.sourceJSON=s,this.read({layerDefinition:s},{origin:"service"}))})()),this}readCapabilities(e,t){t=t.layerDefinition||t;const{operations:{supportsQuery:r,supportsQueryAttachments:i},query:{supportsFormatPBF:s},data:{supportsAttachment:o}}=(0,M.h)(t,this.url);return{exportMap:{supportsModification:!!t.canModifyLayer},operations:{supportsQuery:r,supportsQueryAttachments:i},data:{supportsAttachment:o},query:{supportsFormatPBF:s}}}get defaultPopupTemplate(){return this.createPopupTemplate()}set definitionExpression(e){this._setAndNotifyLayer("definitionExpression",e)}get fieldsIndex(){return new P.Z(this.fields||[])}set floorInfo(e){this._setAndNotifyLayer("floorInfo",e)}readGlobalIdFieldFromService(e,t){if((t=t.layerDefinition||t).globalIdField)return t.globalIdField;if(t.fields)for(const e of t.fields)if("esriFieldTypeGlobalID"===e.type)return e.name}get id(){return this._get("id")??H++}set id(e){this._get("id")!==e&&(!1!==this.layer?.capabilities?.exportMap?.supportsDynamicLayers?this._set("id",e):this._logLockedError("id","capability not available 'layer.capabilities.exportMap.supportsDynamicLayers'"))}set labelingInfo(e){this._setAndNotifyLayer("labelingInfo",e)}writeLabelingInfo(e,t,r,i){e&&e.length&&(t.layerDefinition={drawingInfo:{labelingInfo:e.map((e=>e.write({},i)))}})}set labelsVisible(e){this._setAndNotifyLayer("labelsVisible",e)}set layer(e){this._set("layer",e),this.sublayers&&this.sublayers.forEach((t=>t.layer=e))}set listMode(e){this._set("listMode",e)}set minScale(e){this._setAndNotifyLayer("minScale",e)}readMinScale(e,t){return t.minScale||t.layerDefinition&&t.layerDefinition.minScale||0}set maxScale(e){this._setAndNotifyLayer("maxScale",e)}readMaxScale(e,t){return t.maxScale||t.layerDefinition&&t.layerDefinition.maxScale||0}get effectiveScaleRange(){const{minScale:e,maxScale:t}=this;return{minScale:e,maxScale:t}}readObjectIdFieldFromService(e,t){if((t=t.layerDefinition||t).objectIdField)return t.objectIdField;if(t.fields)for(const e of t.fields)if("esriFieldTypeOID"===e.type)return e.name}set opacity(e){this._setAndNotifyLayer("opacity",e)}readOpacity(e,t){const r=t.layerDefinition;return 1-.01*((null!=r?.transparency?r.transparency:r?.drawingInfo?.transparency)??0)}writeOpacity(e,t,r,i){t.layerDefinition={drawingInfo:{transparency:100-100*e}}}writeParent(e,t){this.parent&&this.parent!==this.layer?t.parentLayerId=(0,F.vU)(this.parent.id):t.parentLayerId=-1}get queryTask(){if(!this.layer)return null;const{spatialReference:e}=this.layer,t="gdbVersion"in this.layer?this.layer.gdbVersion:void 0,{capabilities:r,fieldsIndex:i}=this,s=(0,c.Z)("featurelayer-pbf")&&r?.query.supportsFormatPBF,o=r?.operations?.supportsQueryAttachments??!1;return new O.Z({url:this.url,pbfSupported:s,fieldsIndex:i,gdbVersion:t,sourceSpatialReference:e,queryAttachmentsSupported:o})}set renderer(e){if(e)for(const t of e.getSymbols())if((0,l.dU)(t)){m.Z.getLogger(this.declaredClass).warn("Sublayer renderer should use 2D symbols");break}this._setAndNotifyLayer("renderer",e)}get source(){return this._get("source")||new V.R({mapLayerId:this.id})}set source(e){this._setAndNotifyLayer("source",e)}set sublayers(e){this._handleSublayersChange(e,this._get("sublayers")),this._set("sublayers",e)}castSublayers(e){return(0,F.se)(u.Z.ofType(i),e)}writeSublayers(e,t,r){this.sublayers?.length&&(t[r]=this.sublayers.map((e=>e.id)).toArray().reverse())}readTypeIdField(e,t){let r=(t=t.layerDefinition||t).typeIdField;if(r&&t.fields){r=r.toLowerCase();const e=t.fields.find((e=>e.name.toLowerCase()===r));e&&(r=e.name)}return r}get url(){const e=this.layer?.parsedUrl??this._lastParsedUrl,t=this.source;if(!e)return null;if(this._lastParsedUrl=e,"map-layer"===t?.type)return`${e.path}/${t.mapLayerId}`;const r={layer:JSON.stringify({source:this.source})};return`${e.path}/dynamicLayer?${(0,v.B7)(r)}`}set url(e){this._overrideIfSome("url",e)}set visible(e){this._setAndNotifyLayer("visible",e)}writeVisible(e,t,r,i){t[r]=this.getAtOrigin("defaultVisibility","service")||e}clone(){const{store:e}=(0,T.vw)(this),t=new i;return(0,T.vw)(t).store=e.clone($),this.commitProperty("url"),t._lastParsedUrl=this._lastParsedUrl,t}createPopupTemplate(e){return(0,Q.eZ)(this,e)}createQuery(){return new q.Z({returnGeometry:!0,where:this.definitionExpression||"1=1"})}async createFeatureLayer(){if(this.hasOwnProperty("sublayers"))return null;const{layer:e}=this,t=e?.parsedUrl,i=new(0,(await Promise.all([r.e(5546),r.e(9942),r.e(9238)]).then(r.bind(r,19238))).default)({url:t?.path});return t&&this.source&&("map-layer"===this.source.type?i.layerId=this.source.mapLayerId:i.dynamicDataSource=this.source),null!=e?.refreshInterval&&(i.refreshInterval=e.refreshInterval),this.definitionExpression&&(i.definitionExpression=this.definitionExpression),this.floorInfo&&(i.floorInfo=(0,h.d9)(this.floorInfo)),this.originIdOf("labelingInfo")>E.s3.SERVICE&&(i.labelingInfo=(0,h.d9)(this.labelingInfo)),this.originIdOf("labelsVisible")>E.s3.DEFAULTS&&(i.labelsVisible=this.labelsVisible),this.originIdOf("legendEnabled")>E.s3.DEFAULTS&&(i.legendEnabled=this.legendEnabled),this.originIdOf("visible")>E.s3.DEFAULTS&&(i.visible=this.visible),this.originIdOf("minScale")>E.s3.DEFAULTS&&(i.minScale=this.minScale),this.originIdOf("maxScale")>E.s3.DEFAULTS&&(i.maxScale=this.maxScale),this.originIdOf("opacity")>E.s3.DEFAULTS&&(i.opacity=this.opacity),this.originIdOf("popupTemplate")>E.s3.DEFAULTS&&(i.popupTemplate=(0,h.d9)(this.popupTemplate)),this.originIdOf("renderer")>E.s3.SERVICE&&(i.renderer=(0,h.d9)(this.renderer)),"data-layer"===this.source?.type&&(i.dynamicDataSource=this.source.clone()),this.originIdOf("title")>E.s3.DEFAULTS&&(i.title=this.title),"map-image"===e?.type&&e.originIdOf("customParameters")>E.s3.DEFAULTS&&(i.customParameters=e.customParameters),"tile"===e?.type&&e.originIdOf("customParameters")>E.s3.DEFAULTS&&(i.customParameters=e.customParameters),i}getField(e){return this.fieldsIndex.get(e)}getFeatureType(e){const{typeIdField:t,types:r}=this;if(!t||!e)return null;const i=e.attributes?e.attributes[t]:void 0;if(null==i)return null;let s=null;return r?.some((e=>{const{id:t}=e;return null!=t&&(t.toString()===i.toString()&&(s=e),!!s)})),s}getFieldDomain(e,t){const r=t&&t.feature,i=this.getFeatureType(r);if(i){const t=i.domains&&i.domains[e];if(t&&"inherited"!==t.type)return t}return this._getLayerDomain(e)}async queryAttachments(e,t){await this.load(),e=N.Z.from(e);const r=this.capabilities;if(!r?.data?.supportsAttachment)throw new p.Z("queryAttachments:not-supported","this layer doesn't support attachments");const{attachmentTypes:i,objectIds:s,globalIds:o,num:n,size:a,start:l,where:u}=e;if(!r?.operations?.supportsQueryAttachments&&(i?.length>0||o?.length>0||a?.length>0||n||l||u))throw new p.Z("queryAttachments:option-not-supported","when 'capabilities.operations.supportsQueryAttachments' is false, only objectIds is supported",e);if(!(s?.length||o?.length||u))throw new p.Z("queryAttachments:invalid-query","'objectIds', 'globalIds', or 'where' are required to perform attachment query",e);return this.queryTask.executeAttachmentQuery(e,t)}async queryFeatures(e=this.createQuery(),t){if(await this.load(),!this.capabilities.operations.supportsQuery)throw new p.Z("queryFeatures:not-supported","this layer doesn't support queries.");if(!this.url)throw new p.Z("queryFeatures:not-supported","this layer has no url.");const r=await this.queryTask.execute(e,{...t,query:{...this.layer?.customParameters,token:this.layer?.apiKey}});if(r?.features)for(const e of r.features)e.sourceLayer=this;return r}toExportImageJSON(e){const t={id:this.id,source:this.source?.toJSON()||{mapLayerId:this.id,type:"mapLayer"}},r=(0,S._)(e,this.definitionExpression);(0,b.pC)(r)&&(t.definitionExpression=r);const i=["renderer","labelingInfo","opacity","labelsVisible"].reduce(((e,t)=>(e[t]=this.originIdOf(t),e)),{}),s=Object.keys(i).some((e=>i[e]>E.s3.SERVICE));if(s){const e=t.drawingInfo={};if(i.renderer>E.s3.SERVICE&&(e.renderer=this.renderer?this.renderer.toJSON():null),i.labelsVisible>E.s3.SERVICE&&(e.showLabels=this.labelsVisible),this.labelsVisible&&i.labelingInfo>E.s3.SERVICE){!this.loaded&&this.labelingInfo.some((e=>!e.labelPlacement))&&m.Z.getLogger(this.declaredClass).warnOnce(`A Sublayer (title: ${this.title}, id: ${this.id}) has an undefined 'labelPlacement' and so labels cannot be displayed. Either define a valid 'labelPlacement' or call Sublayer.load() to use a default value based on geometry type.`,{sublayer:this});let t=this.labelingInfo;(0,b.pC)(this.geometryType)&&(t=(0,R.a)(this.labelingInfo,k.M.toJSON(this.geometryType))),e.labelingInfo=t.filter((e=>e.labelPlacement)).map((e=>e.toJSON({origin:"service",layer:this.layer}))),e.showLabels=!0}i.opacity>E.s3.SERVICE&&(e.transparency=100-100*this.opacity),this._assignDefaultSymbolColors(e.renderer)}return t}_assignDefaultSymbolColors(e){this._forEachSimpleMarkerSymbols(e,(e=>{e.color||"esriSMSX"!==e.style&&"esriSMSCross"!==e.style||(e.outline&&e.outline.color?e.color=e.outline.color:e.color=[0,0,0,0])}))}_forEachSimpleMarkerSymbols(e,t){if(e){const r=("uniqueValueInfos"in e?e.uniqueValueInfos:"classBreakInfos"in e?e.classBreakInfos:null)??[];for(const e of r)z(e.symbol)&&t(e.symbol);"symbol"in e&&z(e.symbol)&&t(e.symbol),"defaultSymbol"in e&&z(e.defaultSymbol)&&t(e.defaultSymbol)}}_setAndNotifyLayer(e,t){const r=this.layer,i=this._get(e);let s,o;switch(e){case"definitionExpression":case"floorInfo":s="supportsSublayerDefinitionExpression";break;case"minScale":case"maxScale":case"visible":s="supportsSublayerVisibility";break;case"labelingInfo":case"labelsVisible":case"opacity":case"renderer":case"source":s="supportsDynamicLayers",o="supportsModification"}const n=(0,T.vw)(this).getDefaultOrigin();if("service"!==n){if(s&&!1===this.layer?.capabilities?.exportMap?.[s])return void this._logLockedError(e,`capability not available 'layer.capabilities.exportMap.${s}'`);if(o&&!1===this.capabilities?.exportMap[o])return void this._logLockedError(e,`capability not available 'capabilities.exportMap.${o}'`)}"source"!==e||"not-loaded"===this.loadStatus?(this._set(e,t),"service"!==n&&i!==t&&r&&r.emit&&r.emit("sublayer-update",{propertyName:e,target:this})):this._logLockedError(e,"'source' can't be changed after calling sublayer.load()")}_handleSublayersChange(e,t){t&&(t.forEach((e=>{e.parent=null,e.layer=null})),this.handles.removeAll()),e&&(e.forEach((e=>{e.parent=this,e.layer=this.layer})),this.handles.add([e.on("after-add",(({item:e})=>{e.parent=this,e.layer=this.layer})),e.on("after-remove",(({item:e})=>{e.parent=null,e.layer=null})),e.on("before-changes",(e=>{const t=this.layer?.capabilities?.exportMap?.supportsSublayersChanges;null==t||t||(m.Z.getLogger(this.declaredClass).error(new p.Z("sublayer:sublayers-non-modifiable","Sublayer can't be added, moved, or removed from the layer's sublayers",{sublayer:this,layer:this.layer})),e.preventDefault())}))]))}_logLockedError(e,t){const{layer:r,declaredClass:i}=this;m.Z.getLogger(i).error(new p.Z("sublayer:locked",`Property '${String(e)}' can't be changed on Sublayer from the layer '${r?.id}'`,{reason:t,sublayer:this,layer:r}))}_getLayerDomain(e){const t=this.fieldsIndex.get(e);return t?t.domain:null}};K.test={isMapImageLayerOverridePolicy:e=>e===J||e===U,isTileImageLayerOverridePolicy:e=>e===B},(0,s._)([(0,w.Cb)({readOnly:!0})],K.prototype,"capabilities",void 0),(0,s._)([(0,C.r)("service","capabilities",["layerDefinition.canModifyLayer","layerDefinition.capabilities"])],K.prototype,"readCapabilities",null),(0,s._)([(0,w.Cb)()],K.prototype,"defaultPopupTemplate",null),(0,s._)([(0,w.Cb)({type:String,value:null,json:{name:"layerDefinition.definitionExpression",write:{allowNull:!0,overridePolicy:U}}})],K.prototype,"definitionExpression",null),(0,s._)([(0,w.Cb)({type:[A.Z],json:{origins:{service:{read:{source:"layerDefinition.fields"}}}}})],K.prototype,"fields",void 0),(0,s._)([(0,w.Cb)({readOnly:!0})],K.prototype,"fieldsIndex",null),(0,s._)([(0,w.Cb)({type:j.Z,value:null,json:{name:"layerDefinition.floorInfo",read:{source:"layerDefinition.floorInfo"},write:{target:"layerDefinition.floorInfo",overridePolicy:U},origins:{"web-scene":{read:!1,write:!1}}}})],K.prototype,"floorInfo",null),(0,s._)([(0,w.Cb)({type:G.Z,json:{read:{source:"layerDefinition.extent"}}})],K.prototype,"fullExtent",void 0),(0,s._)([(0,w.Cb)({type:k.M.apiValues,json:{origins:{service:{name:"layerDefinition.geometryType",read:{reader:k.M.read}}}}})],K.prototype,"geometryType",void 0),(0,s._)([(0,w.Cb)({type:String})],K.prototype,"globalIdField",void 0),(0,s._)([(0,C.r)("service","globalIdField",["layerDefinition.globalIdField","layerDefinition.fields"])],K.prototype,"readGlobalIdFieldFromService",null),(0,s._)([(0,w.Cb)({type:F.z8,json:{write:{ignoreOrigin:!0}}})],K.prototype,"id",null),(0,s._)([(0,w.Cb)({value:null,type:[L.Z],json:{read:{source:"layerDefinition.drawingInfo.labelingInfo"},write:{target:"layerDefinition.drawingInfo.labelingInfo",overridePolicy:J}}})],K.prototype,"labelingInfo",null),(0,s._)([(0,x.c)("labelingInfo")],K.prototype,"writeLabelingInfo",null),(0,s._)([(0,w.Cb)({type:Boolean,value:!0,json:{read:{source:"layerDefinition.drawingInfo.showLabels"},write:{target:"layerDefinition.drawingInfo.showLabels",overridePolicy:J}}})],K.prototype,"labelsVisible",null),(0,s._)([(0,w.Cb)({value:null})],K.prototype,"layer",null),(0,s._)([(0,w.Cb)({type:Boolean,value:!0,json:{origins:{service:{read:{enabled:!1}}},read:{source:"showLegend"},write:{target:"showLegend",overridePolicy:W}}})],K.prototype,"legendEnabled",void 0),(0,s._)([(0,w.Cb)({type:["show","hide","hide-children"],value:"show",json:{read:!1,write:!1,origins:{"web-scene":{read:!0,write:!0}}}})],K.prototype,"listMode",null),(0,s._)([(0,w.Cb)({type:Number,value:0,json:{write:{overridePolicy:J}}})],K.prototype,"minScale",null),(0,s._)([(0,C.r)("minScale",["minScale","layerDefinition.minScale"])],K.prototype,"readMinScale",null),(0,s._)([(0,w.Cb)({type:Number,value:0,json:{write:{overridePolicy:J}}})],K.prototype,"maxScale",null),(0,s._)([(0,C.r)("maxScale",["maxScale","layerDefinition.maxScale"])],K.prototype,"readMaxScale",null),(0,s._)([(0,w.Cb)({readOnly:!0})],K.prototype,"effectiveScaleRange",null),(0,s._)([(0,w.Cb)({type:String})],K.prototype,"objectIdField",void 0),(0,s._)([(0,C.r)("service","objectIdField",["layerDefinition.objectIdField","layerDefinition.fields"])],K.prototype,"readObjectIdFieldFromService",null),(0,s._)([(0,w.Cb)({type:Number,value:1,json:{write:{target:"layerDefinition.drawingInfo.transparency",overridePolicy:J}}})],K.prototype,"opacity",null),(0,s._)([(0,C.r)("opacity",["layerDefinition.drawingInfo.transparency","layerDefinition.transparency"])],K.prototype,"readOpacity",null),(0,s._)([(0,x.c)("opacity")],K.prototype,"writeOpacity",null),(0,s._)([(0,w.Cb)({json:{type:F.z8,write:{target:"parentLayerId",writerEnsuresNonNull:!0,overridePolicy:J}}})],K.prototype,"parent",void 0),(0,s._)([(0,x.c)("parent")],K.prototype,"writeParent",null),(0,s._)([(0,w.Cb)({type:Boolean,value:!0,json:{read:{source:"disablePopup",reader:(e,t)=>!t.disablePopup},write:{target:"disablePopup",overridePolicy:W,writer(e,t,r){t[r]=!e}}}})],K.prototype,"popupEnabled",void 0),(0,s._)([(0,w.Cb)({type:o.Z,json:{read:{source:"popupInfo"},write:{target:"popupInfo",overridePolicy:W}}})],K.prototype,"popupTemplate",void 0),(0,s._)([(0,w.Cb)({readOnly:!0})],K.prototype,"queryTask",null),(0,s._)([(0,w.Cb)({types:n.A,value:null,json:{name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy:J},origins:{"web-scene":{types:n.o,name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy:J}}}}})],K.prototype,"renderer",null),(0,s._)([(0,w.Cb)({types:{key:"type",base:null,typeMap:{"data-layer":Z.n,"map-layer":V.R}},cast(e){if(e){if("mapLayerId"in e)return(0,F.TJ)(V.R,e);if("dataSource"in e)return(0,F.TJ)(Z.n,e)}return e},json:{name:"layerDefinition.source",write:{overridePolicy:J}}})],K.prototype,"source",null),(0,s._)([(0,w.Cb)()],K.prototype,"sourceJSON",void 0),(0,s._)([(0,w.Cb)({value:null,json:{type:[F.z8],write:{target:"subLayerIds",allowNull:!0,overridePolicy:J}}})],K.prototype,"sublayers",null),(0,s._)([(0,_.p)("sublayers")],K.prototype,"castSublayers",null),(0,s._)([(0,x.c)("sublayers")],K.prototype,"writeSublayers",null),(0,s._)([(0,w.Cb)({type:String,json:{name:"name",write:{overridePolicy:W}}})],K.prototype,"title",void 0),(0,s._)([(0,w.Cb)({type:String})],K.prototype,"typeIdField",void 0),(0,s._)([(0,C.r)("typeIdField",["layerDefinition.typeIdField"])],K.prototype,"readTypeIdField",null),(0,s._)([(0,w.Cb)({type:[D.Z],json:{origins:{service:{read:{source:"layerDefinition.types"}}}}})],K.prototype,"types",void 0),(0,s._)([(0,w.Cb)({type:String,json:{read:{source:"layerUrl"},write:{target:"layerUrl",overridePolicy:B}}})],K.prototype,"url",null),(0,s._)([(0,w.Cb)({type:Boolean,value:!0,json:{read:{source:"defaultVisibility"},write:{target:"defaultVisibility",overridePolicy:J}}})],K.prototype,"visible",null),(0,s._)([(0,x.c)("visible")],K.prototype,"writeVisible",null),K=i=(0,s._)([(0,I.j)("esri.layers.support.Sublayer")],K);const X=K},90082:(e,t,r)=>{r.d(t,{g:()=>s});var i=r(20102);async function s(e,t){try{return await createImageBitmap(e)}catch(e){throw new i.Z("request:server",`Unable to load ${t}`,{url:t,error:e})}}},72064:(e,t,r)=>{r.d(t,{h:()=>d});var i=r(80442),s=r(70586),o=r(66677);const n={name:"supportsName",size:"supportsSize",contentType:"supportsContentType",keywords:"supportsKeywords",exifInfo:"supportsExifInfo"};function a(e,t,r){return!!(e&&e.hasOwnProperty(t)?e[t]:r)}function l(e,t,r){return e&&e.hasOwnProperty(t)?e[t]:r}function u(e){const t=e?.supportedSpatialAggregationStatistics?.map((e=>e.toLowerCase()));return{envelope:!!t?.includes("envelopeaggregate"),centroid:!!t?.includes("centroidaggregate"),convexHull:!!t?.includes("convexhullaggregate")}}function p(e,t){const r=e?.supportedOperationsWithCacheHint?.map((e=>e.toLowerCase()));return!!r?.includes(t.toLowerCase())}function d(e,t){return{analytics:c(e),attachment:y(e),data:h(e),metadata:f(e),operations:m(e.capabilities,e,t),query:b(e,t),queryRelated:g(e),queryTopFeatures:S(e),editing:v(e)}}function c(e){return{supportsCacheHint:p(e.advancedQueryCapabilities,"queryAnalytics")}}function y(e){const t=e.attachmentProperties,r={supportsName:!1,supportsSize:!1,supportsContentType:!1,supportsKeywords:!1,supportsExifInfo:!1,supportsCacheHint:p(e.advancedQueryCapabilities,"queryAttachments"),supportsResize:a(e,"supportsAttachmentsResizing",!1)};return t&&Array.isArray(t)&&t.forEach((e=>{const t=n[e.name];t&&(r[t]=!!e.isEnabled)})),r}function h(e){return{isVersioned:a(e,"isDataVersioned",!1),supportsAttachment:a(e,"hasAttachments",!1),supportsM:a(e,"hasM",!1),supportsZ:a(e,"hasZ",!1)}}function f(e){return{supportsAdvancedFieldProperties:a(e,"supportsFieldDescriptionProperty",!1)}}function m(e,t,r){const i=e?e.toLowerCase().split(",").map((e=>e.trim())):[],n=r?(0,o.Qc)(r):null,l=i.includes((0,s.pC)(n)&&"MapServer"===n.serverType?"data":"query"),u=i.includes("editing")&&!t.datesInUnknownTimezone;let p=u&&i.includes("create"),d=u&&i.includes("delete"),c=u&&i.includes("update");const y=i.includes("changetracking"),h=t.advancedQueryCapabilities;return u&&!(p||d||c)&&(p=d=c=!0),{supportsCalculate:a(t,"supportsCalculate",!1),supportsTruncate:a(t,"supportsTruncate",!1),supportsValidateSql:a(t,"supportsValidateSql",!1),supportsAdd:p,supportsDelete:d,supportsEditing:u,supportsChangeTracking:y,supportsQuery:l,supportsQueryAnalytics:a(h,"supportsQueryAnalytic",!1),supportsQueryAttachments:a(h,"supportsQueryAttachments",!1),supportsQueryTopFeatures:a(h,"supportsTopFeaturesQuery",!1),supportsResizeAttachments:a(t,"supportsAttachmentsResizing",!1),supportsSync:i.includes("sync"),supportsUpdate:c,supportsExceedsLimitStatistics:a(t,"supportsExceedsLimitStatistics",!1)}}function b(e,t){const r=e.advancedQueryCapabilities,s=e.ownershipBasedAccessControlForFeatures,n=e.archivingInfo,d=e.currentVersion,c=t?.includes("MapServer"),y=!c||d>=(0,i.Z)("mapserver-pbf-version-support"),h=(0,o.M8)(t),f=new Set((e.supportedQueryFormats??"").split(",").map((e=>e.toLowerCase().trim())));return{supportsStatistics:a(r,"supportsStatistics",e.supportsStatistics),supportsPercentileStatistics:a(r,"supportsPercentileStatistics",!1),supportsSpatialAggregationStatistics:a(r,"supportsSpatialAggregationStatistics",!1),supportedSpatialAggregationStatistics:u(r),supportsCentroid:a(r,"supportsReturningGeometryCentroid",!1),supportsDistance:a(r,"supportsQueryWithDistance",!1),supportsDistinct:a(r,"supportsDistinct",e.supportsAdvancedQueries),supportsExtent:a(r,"supportsReturningQueryExtent",!1),supportsGeometryProperties:a(r,"supportsReturningGeometryProperties",!1),supportsHavingClause:a(r,"supportsHavingClause",!1),supportsOrderBy:a(r,"supportsOrderBy",e.supportsAdvancedQueries),supportsPagination:a(r,"supportsPagination",!1),supportsQuantization:a(e,"supportsCoordinatesQuantization",!1),supportsQuantizationEditMode:a(e,"supportsQuantizationEditMode",!1),supportsQueryGeometry:a(e,"supportsReturningQueryGeometry",!1),supportsResultType:a(r,"supportsQueryWithResultType",!1),supportsMaxRecordCountFactor:a(r,"supportsMaxRecordCountFactor",!1),supportsSqlExpression:a(r,"supportsSqlExpression",!1),supportsStandardizedQueriesOnly:a(e,"useStandardizedQueries",!1),supportsTopFeaturesQuery:a(r,"supportsTopFeaturesQuery",!1),supportsQueryByOthers:a(s,"allowOthersToQuery",!0),supportsHistoricMoment:a(n,"supportsQueryWithHistoricMoment",!1),supportsFormatPBF:y&&f.has("pbf"),supportsDisjointSpatialRelationship:a(r,"supportsDisjointSpatialRel",!1),supportsCacheHint:a(r,"supportsQueryWithCacheHint",!1)||p(r,"query"),supportsDefaultSpatialReference:a(r,"supportsDefaultSR",!1),supportsCompactGeometry:h,supportsFullTextSearch:a(r,"supportsFullTextSearch",!1),maxRecordCountFactor:l(e,"maxRecordCountFactor",void 0),maxRecordCount:l(e,"maxRecordCount",void 0),standardMaxRecordCount:l(e,"standardMaxRecordCount",void 0),tileMaxRecordCount:l(e,"tileMaxRecordCount",void 0)}}function g(e){const t=e.advancedQueryCapabilities,r=a(t,"supportsAdvancedQueryRelated",!1);return{supportsPagination:a(t,"supportsQueryRelatedPagination",!1),supportsCount:r,supportsOrderBy:r,supportsCacheHint:p(t,"queryRelated")}}function S(e){return{supportsCacheHint:p(e.advancedQueryCapabilities,"queryTopFilter")}}function v(e){const t=e.ownershipBasedAccessControlForFeatures;return{supportsGeometryUpdate:a(e,"allowGeometryUpdates",!0),supportsGlobalId:a(e,"supportsApplyEditsWithGlobalIds",!1),supportsReturnServiceEditsInSourceSpatialReference:a(e,"supportsReturnServiceEditsInSourceSR",!1),supportsRollbackOnFailure:a(e,"supportsRollbackOnFailureParameter",!1),supportsUpdateWithoutM:a(e,"allowUpdateWithoutMValues",!1),supportsUploadWithItemId:a(e,"supportsAttachmentsByUploadId",!1),supportsDeleteByAnonymous:a(t,"allowAnonymousToDelete",!0),supportsDeleteByOthers:a(t,"allowOthersToDelete",!0),supportsUpdateByAnonymous:a(t,"allowAnonymousToUpdate",!0),supportsUpdateByOthers:a(t,"allowOthersToUpdate",!0)}}},32073:(e,t,r)=>{r.d(t,{FN:()=>n,QV:()=>o,ac:()=>l});var i=r(70586),s=r(31263);function o(e,t,r){const i=t.flatten((({sublayers:e})=>e)).length;return i!==e.length||!!e.some((e=>e.originIdOf("minScale")>r||e.originIdOf("maxScale")>r||e.originIdOf("renderer")>r||e.originIdOf("labelingInfo")>r||e.originIdOf("opacity")>r||e.originIdOf("labelsVisible")>r||e.originIdOf("source")>r))||!a(e,t)}function n(e,t,r){return!!e.some((e=>{const t=e.source;return!(!t||"map-layer"===t.type&&t.mapLayerId===e.id&&((0,i.Wi)(t.gdbVersion)||t.gdbVersion===r))||e.originIdOf("renderer")>s.s3.SERVICE||e.originIdOf("labelingInfo")>s.s3.SERVICE||e.originIdOf("opacity")>s.s3.SERVICE||e.originIdOf("labelsVisible")>s.s3.SERVICE}))||!a(e,t)}function a(e,t){if(!e||!e.length||(0,i.Wi)(t))return!0;const r=t.slice().reverse().flatten((({sublayers:e})=>e&&e.toArray().reverse())).map((e=>e.id)).toArray();if(e.length>r.length)return!1;let s=0;const o=r.length;for(const{id:t}of e){for(;s<o&&r[s]!==t;)s++;if(s>=o)return!1}return!0}function l(e){return!!e&&e.some((e=>null!=e.minScale||e.layerDefinition&&null!=e.layerDefinition.minScale))}},51706:(e,t,r)=>{var i,s;function o(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function n(e){return null!=e&&!isNaN(e)&&isFinite(e)}function a(e){return e.valueExpression?i.Expression:e.field&&"string"==typeof e.field?i.Field:i.Unknown}function l(e,t){const r=t||a(e),o=e.valueUnit||"unknown";return r===i.Unknown?s.Constant:e.stops?s.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?s.ClampedLinear:"unknown"===o?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?s.Proportional:s.Additive:s.Identity:s.RealWorldSize}r.d(t,{PS:()=>a,QW:()=>l,RY:()=>i,hL:()=>s,iY:()=>o,qh:()=>n}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(i||(i={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(s||(s={}))},41818:(e,t,r)=>{r.d(t,{P:()=>n});var i=r(11282),s=r(34599),o=r(14165);async function n(e,t,r){const n=(0,i.en)(e);return(0,s.hH)(n,o.Z.from(t),{...r}).then((e=>e.data.count))}},5396:(e,t,r)=>{r.d(t,{G:()=>n});var i=r(11282),s=r(34599),o=r(14165);async function n(e,t,r){const n=(0,i.en)(e);return(0,s.Ev)(n,o.Z.from(t),{...r}).then((e=>e.data.objectIds))}},4967:(e,t,r)=>{r.d(t,{F:()=>l,e:()=>a});var i=r(11282),s=r(34599),o=r(74889),n=r(14165);async function a(e,t,r){const i=await l(e,t,r);return o.Z.fromJSON(i)}async function l(e,t,r){const o=(0,i.en)(e),a={...r},l=n.Z.from(t),{data:u}=await(0,s.JT)(o,l,l.sourceSpatialReference,a);return u}},28694:(e,t,r)=>{r.d(t,{p:()=>o});var i=r(70586),s=r(69285);function o(e,t,r){if(!r||!r.features||!r.hasZ)return;const o=(0,s.k)(r.geometryType,t,e.outSpatialReference);if(!(0,i.Wi)(o))for(const e of r.features)o(e.geometry)}},56545:(e,t,r)=>{r.d(t,{Z:()=>c});var i,s=r(43697),o=r(96674),n=r(22974),a=r(5600),l=r(75215),u=r(52011),p=r(30556);let d=i=class extends o.wq{constructor(e){super(e),this.attachmentTypes=null,this.attachmentsWhere=null,this.cacheHint=void 0,this.keywords=null,this.globalIds=null,this.name=null,this.num=null,this.objectIds=null,this.returnMetadata=!1,this.size=null,this.start=null,this.where=null}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10}clone(){return new i((0,n.d9)({attachmentTypes:this.attachmentTypes,attachmentsWhere:this.attachmentsWhere,cacheHint:this.cacheHint,keywords:this.keywords,where:this.where,globalIds:this.globalIds,name:this.name,num:this.num,objectIds:this.objectIds,returnMetadata:this.returnMetadata,size:this.size,start:this.start}))}};(0,s._)([(0,a.Cb)({type:[String],json:{write:!0}})],d.prototype,"attachmentTypes",void 0),(0,s._)([(0,a.Cb)({type:String,json:{read:{source:"attachmentsDefinitionExpression"},write:{target:"attachmentsDefinitionExpression"}}})],d.prototype,"attachmentsWhere",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],d.prototype,"cacheHint",void 0),(0,s._)([(0,a.Cb)({type:[String],json:{write:!0}})],d.prototype,"keywords",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"globalIds",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],d.prototype,"name",void 0),(0,s._)([(0,a.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],d.prototype,"num",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"objectIds",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{default:!1,write:!0}})],d.prototype,"returnMetadata",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"size",void 0),(0,s._)([(0,a.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],d.prototype,"start",void 0),(0,s._)([(0,p.c)("start"),(0,p.c)("num")],d.prototype,"writeStart",null),(0,s._)([(0,a.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],d.prototype,"where",void 0),d=i=(0,s._)([(0,u.j)("esri.rest.support.AttachmentQuery")],d),d.from=(0,l.se)(d);const c=d},74889:(e,t,r)=>{r.d(t,{Z:()=>v});var i,s=r(43697),o=r(66577),n=r(38171),a=r(35454),l=r(96674),u=r(22974),p=r(70586),d=r(5600),c=(r(75215),r(71715)),y=r(52011),h=r(30556),f=r(82971),m=r(33955),b=r(1231);const g=new a.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let S=i=class extends l.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const r=f.Z.fromJSON(t.spatialReference),i=[];for(let t=0;t<e.length;t++){const s=e[t],o=n.Z.fromJSON(s),a=s.geometry&&s.geometry.spatialReference;(0,p.pC)(o.geometry)&&!a&&(o.geometry.spatialReference=r);const l=s.aggregateGeometries,u=o.aggregateGeometries;if(l&&(0,p.pC)(u))for(const e in u){const t=u[e],i=l[e]?.spatialReference;(0,p.pC)(t)&&!i&&(t.spatialReference=r)}i.push(o)}return i}writeGeometryType(e,t,r,i){if(e)return void g.write(e,t,r,i);const{features:s}=this;if(s)for(const e of s)if(e&&(0,p.pC)(e.geometry))return void g.write(e.geometry.type,t,r,i)}readQueryGeometry(e,t){if(!e)return null;const r=!!e.spatialReference,i=(0,m.im)(e);return i&&!r&&t.spatialReference&&(i.spatialReference=f.Z.fromJSON(t.spatialReference)),i}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:r}=this;if(r)for(const e of r)if(e&&(0,p.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new i(this.cloneProperties())}cloneProperties(){return(0,u.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let r=0;r<t.features.length;r++){const i=t.features[r];if(i.geometry){const t=e&&e[r];i.geometry=t&&t.toJSON()||i.geometry}}return t}quantize(e){const{scale:[t,r],translate:[i,s]}=e,o=this.features,n=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-i)/t)),(e=>Math.round((s-e)/r)));for(let e=0,t=o.length;e<t;e++)n?.((0,p.Wg)(o[e].geometry))||(o.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:r}=this;if(!r)return this;const{translate:[i,s],scale:[o,n]}=r,a=this._getHydrationFunction(e,(e=>e*o+i),(e=>s-e*n));for(const{geometry:e}of t)(0,p.pC)(e)&&a&&a(e);return this.transform=null,this}_quantizePoints(e,t,r){let i,s;const o=[];for(let n=0,a=e.length;n<a;n++){const a=e[n];if(n>0){const e=t(a[0]),n=r(a[1]);e===i&&n===s||(o.push([e-i,n-s]),i=e,s=n)}else i=t(a[0]),s=r(a[1]),o.push([i,s])}return o.length>0?o:null}_getQuantizationFunction(e,t,r){return"point"===e?e=>(e.x=t(e.x),e.y=r(e.y),e):"polyline"===e||"polygon"===e?e=>{const i=(0,m.oU)(e)?e.rings:e.paths,s=[];for(let e=0,o=i.length;e<o;e++){const o=i[e],n=this._quantizePoints(o,t,r);n&&s.push(n)}return s.length>0?((0,m.oU)(e)?e.rings=s:e.paths=s,e):null}:"multipoint"===e?e=>{const i=this._quantizePoints(e.points,t,r);return i&&i.length>0?(e.points=i,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,r){return"point"===e?e=>{e.x=t(e.x),e.y=r(e.y)}:"polyline"===e||"polygon"===e?e=>{const i=(0,m.oU)(e)?e.rings:e.paths;let s,o;for(let e=0,n=i.length;e<n;e++){const n=i[e];for(let e=0,i=n.length;e<i;e++){const i=n[e];e>0?(s+=i[0],o+=i[1]):(s=i[0],o=i[1]),i[0]=t(s),i[1]=r(o)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=r(e.ymin),e.xmax=t(e.xmax),e.ymax=r(e.ymax)}:"multipoint"===e?e=>{const i=e.points;let s,o;for(let e=0,n=i.length;e<n;e++){const n=i[e];e>0?(s+=n[0],o+=n[1]):(s=n[0],o=n[1]),n[0]=t(s),n[1]=r(o)}}:null}};(0,s._)([(0,d.Cb)({type:String,json:{write:!0}})],S.prototype,"displayFieldName",void 0),(0,s._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],S.prototype,"exceededTransferLimit",void 0),(0,s._)([(0,d.Cb)({type:[n.Z],json:{write:!0}})],S.prototype,"features",void 0),(0,s._)([(0,c.r)("features")],S.prototype,"readFeatures",null),(0,s._)([(0,d.Cb)({type:[b.Z],json:{write:!0}})],S.prototype,"fields",void 0),(0,s._)([(0,d.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:g.read}}})],S.prototype,"geometryType",void 0),(0,s._)([(0,h.c)("geometryType")],S.prototype,"writeGeometryType",null),(0,s._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],S.prototype,"hasM",void 0),(0,s._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],S.prototype,"hasZ",void 0),(0,s._)([(0,d.Cb)({types:o.qM,json:{write:!0}})],S.prototype,"queryGeometry",void 0),(0,s._)([(0,c.r)("queryGeometry")],S.prototype,"readQueryGeometry",null),(0,s._)([(0,d.Cb)({type:f.Z,json:{write:!0}})],S.prototype,"spatialReference",void 0),(0,s._)([(0,h.c)("spatialReference")],S.prototype,"writeSpatialReference",null),(0,s._)([(0,d.Cb)({json:{write:!0}})],S.prototype,"transform",void 0),S=i=(0,s._)([(0,y.j)("esri.rest.support.FeatureSet")],S),S.prototype.toJSON.isDefaultToJSON=!0;const v=S},58333:(e,t,r)=>{r.d(t,{ET:()=>o,I4:()=>s,eG:()=>l,lF:()=>n,lj:()=>p,qP:()=>a,wW:()=>u});const i=[252,146,31,255],s={type:"esriSMS",style:"esriSMSCircle",size:6,color:i,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},o={type:"esriSLS",style:"esriSLSSolid",width:.75,color:i},n={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},a={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},l={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},u={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},p={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);