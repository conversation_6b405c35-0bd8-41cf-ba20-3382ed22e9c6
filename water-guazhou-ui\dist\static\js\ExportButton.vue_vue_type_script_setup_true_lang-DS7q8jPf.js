import{d as a,r,aG as s,g as p,h as i,i as l,aH as d}from"./index-r0dFAfgr.js";const m=a({__name:"ExportButton",setup(u){const o=r({perm:!0,type:"primary",isTextBtn:!1,iconifyIcon:"ep:download",text:"导出",click:()=>c()}),c=()=>{s().then(t=>{const e=window.URL.createObjectURL(t.data),n=document.createElement("a");n.style.display="none",n.href=e,n.setAttribute("download","菜单信息.json"),document.body.appendChild(n),n.click()})};return(t,e)=>{const n=d;return p(),i(n,{config:l(o)},null,8,["config"])}}});export{m as _};
