package org.thingsboard.server.dao.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.DataConstants;

import java.sql.*;

/**
 * 数据库工具
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-06-19
 */
@Component
@Slf4j
public class DatabaseUtil {

    /**
     *
     * @param host 主机
     * @param port 端口
     * @param server 数据库
     * @param user 用户名
     * @param password 密码
     * @param databaseType 数据库类型
     * @return
     */
    public Connection getConnection(String host, Integer port, String server, String user, String password, DataConstants.REPORT_DATABASE_TYPE databaseType) {
        Connection conn = null;

        try {
            String driver = "";
            String url = "";
            switch (databaseType) {
                case ORACLE:
                    driver = "oracle.jdbc.driver.OracleDriver";
                    url = "jdbc:oracle:thin:@" + host + ":" + port + ":" + server;
                    break;
                case POSTGRESQL:
                    driver = "org.postgresql.Driver";
                    url = "jdbc:postgresql://" + host + ":" + port + "/" + server;
                    break;
                case MYSQL:
                    driver = "com.mysql.jdbc.Driver";
                    url = "jdbc:mysql://" + host + ":" + port + "/" + server;
                    break;
                case SQLSERVER:
                    driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
                    url = "jdbc:sqlserver://" + host + ":" + port + ";databaseName=" + server;
                    break;

            }

            Class.forName(driver);//加载数据驱动
            conn = DriverManager.getConnection(url, user, password);// 连接数据库

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            System.out.println("加载数据库驱动失败");
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("连接数据库失败");
        }
        return conn;
    }

    public void close(Connection conn, PreparedStatement ps, ResultSet rs) {
        try {
            if (rs != null) {
                rs.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        try {
            if (ps != null) {
                ps.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        try {
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

    }
}
