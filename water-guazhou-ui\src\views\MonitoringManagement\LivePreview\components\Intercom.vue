<!-- 对讲 -->
<template>
  <template v-for="(item, index) in btns" :key="index">
    <div v-if="item.type === 'interval'" class="interval"></div>
    <el-tooltip
      v-else
      class="box-item"
      effect="dark"
      :content="item.title"
      placement="top"
    >
      <el-button
        text
        :disabled="item.disabled"
        @click="item.onclick && item.onclick(item)"
      >
        <Icon
          :icon="item.icon"
          :color="item.color"
          style="font-size: 20px"
        ></Icon>
      </el-button>
    </el-tooltip>
  </template>

  <el-slider
    :disabled="!volumeDisabled"
    style="width: 100px; margin-left: 15px"
    v-model="volume"
    :step="1"
    @change="putVolume"
  />
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue';

const emit = defineEmits(['talkStart', 'talkStop', 'putVolume']);

// 对讲状态
const status = ref(false);
const disabled = ref(true);
const index = ref(0);
const volume = ref(100);
const volumeDisabled = ref(false);

const btns = ref<
  {
    title?: string;
    onclick?: Function;
    icon: string;
    type?: string;
    disabled?: boolean;
    color?: string;
  }[]
>([
  {
    title: computed(() => (status.value ? '结束对讲' : '点击对讲')) as any,
    onclick: () => {
      if (status.value) {
        emit('talkStop', index.value);
      } else {
        emit('talkStart', index.value);
      }
      status.value = !status.value;
    },
    icon: 'ant-design:sound-filled',
    disabled: computed(() => disabled.value) as any,
    color: computed(() => {
      if (disabled.value) return '#eeeeee';
      return status.value ? '#4caf50' : '#409eff';
    }) as any
  },
  {
    type: 'interval',
    icon: ''
  }
]);

const refresh = (val, i) => {
  if (!val.talkurl) {
    disabled.value = true;
  } else {
    disabled.value = false;
  }
  status.value = false;
  index.value = i;
};

const setVolume = (status, value) => {
  volumeDisabled.value = status;
  volume.value = value;
};

const putVolume = (val) => {
  emit('putVolume', val);
};

defineExpose({
  refresh,
  setVolume
});
</script>

<style lang="scss" scoped>
.btns {
  display: flex;
  align-items: center;
}

.el-button {
  margin: 0px;
  height: 100%;
}

.interval {
  width: 1px;
  height: 80%;
  background-color: rgb(173, 173, 173);
}

.Intercom {
  width: 100px;
}
</style>
