import{_ as ge}from"./Search-NSrhrIa_.js";import{bF as v,d as ye,M as he,c as k,r as f,j as be,s as u,x as F,S as V,bC as _e,o as ke,g as j,n as G,q as r,i as p,F as m,p as B,aj as z,G as S,ca as Q,aB as ve,aJ as De,h as xe,u as Fe,J as we,bK as Te,I as Re,bU as Ie,aK as Le,aL as Ne,bW as Ue,K as Ce,aq as qe,al as Me,b7 as Z,ak as Se,bM as J,bq as ee,C as We}from"./index-r0dFAfgr.js";import{_ as Ye}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as Ee}from"./CardTable-rdWOL4_6.js";import{_ as Oe}from"./CardSearch-CB_HNR-Q.js";import{X as te}from"./xlsx-rVJkW9yq.js";import{d as Pe,g as Ae,h as Ve,s as je}from"./pumpRoomInfo-DV75B9MO.js";import{c as Be,p as ze,f as Je,g as $e,h as Xe,i as He,j as Ke}from"./index-CjYkj_FN.js";import"./index-C9hz-UZb.js";const Ge=[{label:"泵房名称",prop:"pumpRoomId",minWidth:120},{label:"设备编码",prop:"code",minWidth:120},{label:"设备名称",prop:"name",minWidth:120},{label:"设备简称",prop:"nickname",minWidth:120},{label:"泵个数",prop:"pumpNum",minWidth:120},{label:"厂家名称",prop:"companyName",minWidth:120},{label:"设备型号",prop:"model",minWidth:120},{label:"安装人",prop:"installUserName",minWidth:120},{label:"安装日期",prop:"installDate",minWidth:120,formatter:(y,D)=>v(D).format("YYYY-MM-DD")},{label:"录入日期",prop:"createTime",minWidth:120,formatter:(y,D)=>v(D).format("YYYY-MM-DD")},{minWidth:120,label:"性能参数",prop:"performanceParameters"},{label:"备注",prop:"remark"}],Qe=[{label:"设备名称",field:"name",type:"input",placeholder:"请输入设备名称"},{label:"设备简称",field:"nickname",type:"input",placeholder:"请输入设备简称"},{label:"泵个数",field:"pumpNum",type:"input-number",placeholder:"请输入泵个数"},{label:"厂家名称",field:"companyName",type:"input",placeholder:"请输入厂家名称"},{label:"设备型号",field:"model",type:"input",placeholder:"请输入设备型号"},{label:"安装人",field:"installUserName",type:"input",placeholder:"请输入安装人"},{label:"安装日期",field:"installDateFrom",type:"daterange"},{label:"录入日期",field:"fromTime",type:"daterange",format:"YYYY-MM-DD"}],W=[{label:"泵房名称",field:"pumpRoomId",prop:"pumpRoomId",type:"input",rules:[{required:!0,message:"请输入泵房名称"}],placeholder:"请输入泵房名称"},{label:"设备编码",field:"code",prop:"code",type:"input",rules:[{required:!0,message:"请填写设备编码"}],placeholder:"请填写设备编码"},{label:"设备名称",field:"name",prop:"name",type:"input",rules:[{required:!0,message:"请填写设备名称"}],placeholder:"请填写设备名称"},{label:"设备简称",field:"nickname",prop:"nickname",type:"input",rules:[{required:!0,message:"请填写设备简称"}],placeholder:"请填写设备简称"},{label:"泵个数",field:"pumpNum",prop:"pumpNum",type:"input-number",rules:[{required:!0,message:"请填写泵个数"}],placeholder:"请填写泵个数"},{label:"厂家名称",field:"companyName",prop:"companyName",type:"input",rules:[{required:!0,message:"请填写厂家名称"}],placeholder:"请填写厂家名称"},{label:"设备型号",field:"model",prop:"model",type:"input",rules:[{required:!0,message:"请填写设备型号"}],placeholder:"请填写设备型号"},{label:"安装人",field:"installUserName",prop:"installUserName",type:"input",rules:[{required:!0,message:"请填写安装人"}],placeholder:"请填写安装人"},{label:"安装日期",field:"installDate",prop:"installDate",type:"date",rules:[{required:!0,message:"请选择安装日期"}]},{label:"性能参数 ",field:"performanceParameters",prop:"performanceParameters",type:"textarea",rules:[{required:!0,message:"请填写性能参数"}],placeholder:"请填写性能参数"},{label:"备注",field:"remark",prop:"remark",type:"textarea",placeholder:"请填写备注"}],ae={设备编码:"code",设备名称:"name",设备简称:"nickname",泵个数:"pumpNum",厂家名称:"companyName",设备型号:"model",安装人:"installUserName",安装日期:"installDate",性能参数:"performanceParameters",备注:"remark"},Ze=y=>new Promise(D=>{const R=new FileReader;R.onload=N=>{const b=new Uint8Array(N.target.result),U=te.read(b,{type:"array"}),I=U.Sheets[U.SheetNames[0]],w=te.utils.sheet_to_json(I);D(w)},R.readAsArrayBuffer(y.raw)}),et=()=>{let y=W;return y=y.concat(W),y.push({type:"textarea",label:"备注",field:"remark",prop:"remark",placeholder:"请输入备注"}),y},tt={class:"wrapper"},at={class:"buttons"},ot={class:"btns"},lt=ye({__name:"index",setup(y){const{$messageSuccess:D,$messageError:R}=he(),N=k(),b=k(),U=k(),I=k(),w=k(),$=k(),Y=k(),E=k(),o=f({pumpRooms:[],fileType:"",pumpRoomId:"",fileActionUrl:be().actionUrl+"/file/api/upload/file",dataList:[],rowId:""}),C=f({pumpRoomId:""}),oe=f({pumpRoomId:[{required:!0,message:"请选择泵房",trigger:"blur"}]}),le=f({filters:[{type:"input",label:"泵房编码",field:"pumpRoomCode",placeholder:"请输入泵房编码"},{type:"input",label:"泵房名称",field:"pumpRoomName",placeholder:"请输入泵房名称"},{type:"input",label:"设备编码",field:"code",placeholder:"请输入设备编码"},...Qe],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:u(Me),click:()=>x()},{perm:!0,text:"重置",type:"default",svgIcon:u(Z),click:()=>{var t;(t=w.value)==null||t.resetForm()}},{perm:!0,text:"添加",type:"success",svgIcon:u(Se),click:()=>{T.title="新增",X()}},{perm:!0,text:"导入",type:"warning",svgIcon:u(Q),click:()=>{var t;P(),(t=I.value)==null||t.openDialog()}},{perm:!0,text:"导出",type:"primary",svgIcon:u(z),click:()=>se()}]}]}),re=f({title:"导入",dialogWidth:1200,group:[],cancel:!0,btns:[{perm:!0,text:"确定导入",click:async()=>{var t;await((t=N.value)==null?void 0:t.validate(async e=>{e&&(o.dataList.length>0?(console.log(o.dataList),o.dataList=o.dataList.map(a=>a={...a,pumpRoomId:C.pumpRoomId}),Be(o.dataList).then(a=>{var n,l;((n=a.data)==null?void 0:n.code)===200?(P(),(l=I.value)==null||l.closeDialog(),F.success("提交成功"),x()):F.error("提交失败")}).catch(a=>{console.log(a),F.error("提交失败")})):F.warning("请导入正确的xlsx文件！"))}))}}]}),ne=t=>{o.dataList=[],Ze(t).then(e=>{e&&e.forEach(l=>{const d={};for(const i in l){if(typeof ae[i]>"u"){F.info("设备编码/设备名称/设备简称/泵个数/厂家名称/设备型号/安装人/安装日期/性能参数/备注; 且每行均有对应数据!");return}d[ae[i]]=l[i]}o.dataList.push(d)});const a=o.dataList.map(l=>JSON.stringify(l)),n=[...new Set(a)];O.dataList=n.map(l=>JSON.parse(l))})},se=async()=>{var g;const t=((g=w.value)==null?void 0:g.queryParams)||{},[e,a]=t.installDateFrom,[n,l]=t.fromTime,d={...t,page:_.pagination.page||1,size:-1,installDateFrom:e?v(e).startOf("day").valueOf():null,installDateTo:a?v(a).startOf("day").valueOf():null,fromTime:n,toTime:l},i=await ze(d),c=window.URL.createObjectURL(i.data);console.log(c);const s=document.createElement("a");s.style.display="none",s.href=c,s.setAttribute("download","机房信息列表.xlsx"),document.body.appendChild(s),s.click()},ie=async()=>{const t=await Ke(),e=window.URL.createObjectURL(t.data);console.log(e);const a=document.createElement("a");a.style.display="none",a.href=e,a.setAttribute("download","机房信息模板.xlsx"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(a.href)},O=f({indexVisible:!0,columns:W.slice(1),dataList:[],pagination:{hide:!0}}),pe=async(t,e)=>{const a={fileName:e.name,fileAddress:e.response,label:o.fileType,host:o.rowId};je(a).then(()=>{L(o.fileType,o.rowId)})},de=f({defaultParams:{time:[]},filters:[{type:"input",label:"文件名",field:"fileName",placeholder:"请输入文件名"},{type:"daterange",label:"上传日期",field:"fromTime",format:"YYYY-MM-DD"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>L(o.fileType,o.rowId)},{perm:!0,text:"重置",type:"default",svgIcon:u(Z),click:()=>{var t;(t=Y.value)==null||t.resetForm()}}]}]}),_=f({indexVisible:!0,columns:Ge.concat([{minWidth:120,label:"施工图纸",prop:"constructionFile",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",type:"default",svgIcon:u(J),click:t=>{var e;console.log(t.id),o.fileType="constructionFile",(e=E.value)==null||e.openDialog(),L(o.fileType,t.id)}}]}},{minWidth:120,label:"其他文件",prop:"otherFile",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",type:"default",svgIcon:u(J),click:t=>{var e;o.fileType="otherFile",(e=E.value)==null||e.openDialog(),L(o.fileType,t.id)}}]}}]),operations:[{perm:!0,text:"修改",svgIcon:u(J),click:t=>{T.title="修改",X(t)}},{perm:!0,text:"删除",type:"danger",svgIcon:u(ee),click:t=>ce(t)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{_.pagination.page=t,_.pagination.limit=e,x()}}}),me=f({dialogWidth:1050,title:"上传文件",labelWidth:120,group:[{fields:et()}]}),T=f({dialogWidth:600,title:"新增",labelWidth:120,group:[{fields:W}],submit:t=>{V("确定提交？","提示信息").then(()=>{T.title==="新增"?Je(t).then(()=>{var e,a,n;(a=(e=b.value)==null?void 0:e.refForm)==null||a.resetForm(),(n=b.value)==null||n.closeDialog(),F.success("提交成功"),x()}):$e(t).then(()=>{var e,a,n;(a=(e=b.value)==null?void 0:e.refForm)==null||a.resetForm(),(n=b.value)==null||n.closeDialog(),F.success("提交成功"),x()})}).catch(()=>{})}}),X=t=>{var e;T.defaultValue=t?{...t,installDate:v(t.installDate).format()}:{},(e=b.value)==null||e.openDialog()},ce=t=>{V("确定删除?","提示信息").then(()=>{Xe(t.id).then(()=>{x()})}).catch(()=>{})},H=f({indexVisible:!0,columns:[{label:"文件",prop:"fileName",minWidth:120},{label:"上传时间",prop:"uploadTime",minWidth:120,formatter:(t,e)=>e?v(e).format("YYYY-MM-DD HH:mm:ss"):""}],dataList:[],operations:[{perm:!0,text:"下载",type:"primary",svgIcon:u(z),click:t=>{_e(t.fileAddress,t.fileName)}},{perm:!0,text:"删除",type:"danger",svgIcon:u(ee),click:t=>{V("确定删除该附件, 是否继续?","删除提示").then(()=>{Pe(t.id).then(e=>{var a;((a=e.data)==null?void 0:a.code)===200?D("删除成功"):R("删除失败"),L(o.fileType,o.rowId)}).catch(e=>{R(e)})})}}],pagination:{hide:!0}}),x=async()=>{var c,s,g;const t=((c=w.value)==null?void 0:c.queryParams)||{},[e,a]=t.installDateFrom,[n,l]=t.fromTime,d={...t,page:_.pagination.page||1,size:_.pagination.limit||20,installDateFrom:e?v(e).startOf("day").valueOf():null,installDateTo:a?v(a).startOf("day").valueOf():null,fromTime:n,toTime:l},i=await He(d);console.log(i.data.data.total),_.pagination.total=(s=i.data)==null?void 0:s.data.total,_.dataList=(g=i.data)==null?void 0:g.data.data},L=async(t,e)=>{var c,s,g,q,M;const a=((c=Y.value)==null?void 0:c.queryParams)||{fromTime:[]},[n,l]=a.fromTime||[],d={...a,host:e,label:t,fromTime:n,toTime:l,page:1,size:99999};o.rowId=e;const i=await Ae(d);console.log((g=(s=i.data)==null?void 0:s.data)==null?void 0:g.data),H.dataList=(M=(q=i.data)==null?void 0:q.data)==null?void 0:M.data},P=()=>{o.dataList=[],O.dataList=[]};return ke(async()=>{var l,d,i,c;const t=await Ve({size:999,page:1}),e=(d=(l=t==null?void 0:t.data)==null?void 0:l.data)==null?void 0:d.data;o.pumpRooms=e;const a=e.map(s=>({label:s.name,value:s.id})),n=(c=(i=T.group[0])==null?void 0:i.fields)==null?void 0:c.find(s=>s.field==="pumpRoomId");n.options=a,x()}),(t,e)=>{const a=Oe,n=Ee,l=Ye,d=we,i=Te,c=Re,s=Ie,g=Le,q=Ne,M=Ue,ue=Ce,K=qe,fe=ge;return j(),G("div",tt,[r(a,{ref_key:"refSearch",ref:w,config:p(le)},null,8,["config"]),r(n,{ref_key:"refTable",ref:U,config:p(_),class:"card-table"},null,8,["config"]),r(l,{ref_key:"refForm",ref:b,config:p(T)},null,8,["config"]),r(l,{ref_key:"refUploadDialog",ref:I,config:p(re)},{default:m(()=>[r(ue,{ref_key:"ruleFormRef",ref:N,rules:p(oe),model:p(C)},{default:m(()=>[r(M,null,{default:m(()=>[r(s,{span:12},{default:m(()=>[r(c,null,{default:m(()=>[B("div",at,[r(d,{type:"primary",icon:p(z),plain:!0,onClick:ie},{default:m(()=>e[2]||(e[2]=[S(" 下载模板 ")])),_:1},8,["icon"]),r(i,{ref:"upload",action:"action",accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","show-file-list":!0,"auto-upload":!1,"on-remove":P,limit:1,"on-change":ne},{tip:m(()=>e[4]||(e[4]=[B("div",{class:"el-upload__tip"}," 只能导入xlsx文件, 请确保导入的文件单元格格式为文本! ",-1)])),default:m(()=>[r(d,{type:"primary",icon:p(Q)},{default:m(()=>e[3]||(e[3]=[S(" 添加文件 ")])),_:1},8,["icon"])]),_:1},512)])]),_:1})]),_:1}),r(s,{span:12},{default:m(()=>[r(c,{label:"泵房",required:"",prop:"pumpRoomId"},{default:m(()=>[r(q,{modelValue:p(C).pumpRoomId,"onUpdate:modelValue":e[0]||(e[0]=h=>p(C).pumpRoomId=h),placeholder:"请选择泵房"},{default:m(()=>[(j(!0),G(ve,null,De(p(o).pumpRooms,(h,A)=>(j(),xe(g,{key:A,label:h.name,value:h.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["rules","model"]),r(K,{config:p(O)},null,8,["config"])]),_:1},8,["config"]),r(l,{ref_key:"refFileUpload",ref:E,config:p(me)},{default:m(()=>[r(fe,{ref_key:"uploadSearch",ref:Y,config:p(de)},null,8,["config"]),B("div",ot,[r(i,{ref_key:"uploadRef",ref:$,action:p(o).fileActionUrl,"show-file-list":!0,accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","auto-upload":!1,multiple:"",headers:{"X-Authorization":"Bearer "+p(Fe)().token},"on-success":(h,A)=>pe(h,A),class:"upload-demo"},{trigger:m(()=>[r(d,{type:"primary"},{default:m(()=>e[5]||(e[5]=[S(" 读取文件 ")])),_:1})]),_:1},8,["action","headers","on-success"]),r(d,{type:"success",onClick:e[1]||(e[1]=()=>{var h;return(h=p($))==null?void 0:h.submit()})},{default:m(()=>e[6]||(e[6]=[S(" 上传 ")])),_:1})]),r(K,{config:p(H),class:"form-table"},null,8,["config"])]),_:1},8,["config"])])}}}),ft=We(lt,[["__scopeId","data-v-553c597d"]]);export{ft as default};
