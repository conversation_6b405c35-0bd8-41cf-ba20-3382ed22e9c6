import{_ as G}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{d as B,c as v,r as _,s as g,b as c,S as V,l as x,bE as C,bB as E,o as j,g as L,n as M,q as d,F as T,p as m,bo as O,bR as $,i as n,cy as A,an as X,aB as z,aq as J,cE as H,al as K,b7 as Q,bq as N,da as Y,db as Z,X as tt,C as et}from"./index-r0dFAfgr.js";import{_ as at}from"./Search-NSrhrIa_.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as it}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import{D as ot,a as rt,G as st}from"./maintenance-zUn_QdHH.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import mt from"./RightDrawerMap-D5PhmGFO.js";import nt from"./MaintainDetail-C9q9ex1w.js";import{P}from"./config-C9CMv0E7.js";import{P as pt}from"./gisSetting-CQEP-Q3N.js";import{E as lt,c as ct,d as S}from"./config-DncLSA-r.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./Videor.vue_vue_type_script_setup_true_lang-EsHlP83o.js";import"./DetailTable-Dc-xAY7v.js";import"./index-DeAQQ1ej.js";const dt={class:"detail-page"},ut={class:"page-wrapper"},gt={class:"table-box"},ft={key:0,class:"page-wrapper"},bt={class:"detail-header"},_t={class:"detail-main overlay-y"},ht=B({__name:"TaskManage",setup(yt){const h=v(),f=v(),b=v(),y={},p=_({tabs:[],loading:!1,layerIds:[],layerInfos:[],curPage:"table"}),U=_({scrollBarGradientColor:"#fafafa",operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:g(K),click:()=>u()},{perm:!0,text:"重置",type:"default",svgIcon:g(Q),click:()=>{var t;(t=h.value)==null||t.resetForm(),u()}},{perm:!0,text:"批量删除",type:"danger",disabled:()=>{var t;return!((t=o.selectList)!=null&&t.length)},svgIcon:g(N),click:()=>D()}]}],filters:[{type:"radio-button",label:"派发状态",field:"isAssigned",options:[{label:"全部",value:""},{label:"已分派",value:"true"},{label:"未分派",value:"false"}]},{type:"radio-button",label:"完成状态",field:"isComplete",options:[{label:"全部",value:""},{label:"已完成",value:"true"},{label:"未完成",value:"false"}]},{type:"datetimerange",label:"创建时间",field:"fromTime"},{type:"user-select",label:"养护人员",field:"maintainUser"}],defaultParams:{dispatchStatus:"",compStatus:""}}),o=_({dataList:[],indexVisible:!0,columns:[{minWidth:120,label:"任务名称",prop:"name"},{minWidth:120,label:"养护设备类型",prop:"deviceName"},{minWidth:120,label:"开始时间",prop:"beginTime"},{minWidth:120,label:"截止时间",prop:"endTime"},{minWidth:120,label:"创建人",prop:"creatorName"},{minWidth:120,label:"创建时间",prop:"createTime"},{minWidth:120,label:"养护人员",prop:"maintainUserName"},{minWidth:100,align:"center",label:"任务状态",prop:"status",tag:!0,tagColor:t=>{var e;return(e=P[t.status])==null?void 0:e.color},formatter:t=>{var e;return((e=P[t.status])==null?void 0:e.text)||t.status}}],handleSelectChange:t=>{o.selectList=t},operationWidth:220,operations:[{perm:!0,text:"分派",svgIcon:g(Y),disabled:t=>t.status!=="PENDING",click:t=>W(t)},{perm:!0,text:"详情",type:"info",svgIcon:g(Z),click:t=>{o.currentRow=t,p.curPage="detail"}},{perm:!0,text:"删除",type:"danger",svgIcon:g(N),click:t=>D(t)}],pagination:{refreshData:({page:t,size:e})=>{o.pagination.page=t||1,o.pagination.limit=e||20,u()}}}),F=()=>{p.curPage="table"},D=t=>{const e=(t?[t]:o.selectList)||[],a=e.map(i=>i.id);if(!a.length){c.warning("请先选择要删除的数据");return}V("确定删除？","提示信息").then(()=>{ot(a).then(i=>{i.data.code===200?(c.success(i.data.message),pt({optionName:lt.XUNJIANRENWU,type:ct.INSPECT,content:`${S.DELETE}养护任务：${e.map(r=>r.name+(r.code?"【"+r.code+"】":"")).join("、")}`,optionType:S.DELETE}).catch(()=>{console.log("生成gis操作日志失败")}),u()):c.error(i.data.message)}).catch(i=>{console.log(i),c.error("删除失败")})}).catch(()=>{})},k=_({title:"任务派发",dialogWidth:500,labelPosition:"right",group:[{fields:[{type:"input",readonly:!0,label:"任务名称",placeholder:" ",field:"name"},{type:"datetimerange",label:"起止时间",field:"beginTime",rules:[{required:!0,message:"请选择起止时间"}]},{type:"user-select",label:"养护人员",field:"maintainUser",departField:"maintainUserDepartmentId",rules:[{required:!0,message:"请选择养护人员"}]},{type:"textarea",label:"备注",field:"remark"}]}],submit:t=>{var e,a,i;rt(o.currentRow.id,{...t,maintainUser:(e=t.maintainUser)==null?void 0:e.join(","),beginTime:(a=t.beginTime)==null?void 0:a[0],endTime:(i=t.beginTime)==null?void 0:i[1]}).then(r=>{var l;r.data.code===200?(c.success(r.data.message),u(),(l=f.value)==null||l.closeDialog()):c.error(r.data.message)}).catch(r=>{console.log(r),c.error("系统错误")})}}),W=async t=>{var e,a;o.currentRow=t,k.defaultValue={name:t.name,beginTime:[x().startOf("D").format(C),x().endOf("D").format(C)],maintainUserDepartmentId:t.maintainUserDepartmentId,maintainUser:t.maintainUser},(e=f.value)==null||e.openDialog(),await E(),(a=f.value)==null||a.resetForm()},u=()=>{var e,a,i,r,l;const t=((e=h.value)==null?void 0:e.queryParams)||{};st({page:o.pagination.page||1,size:o.pagination.limit||20,...t,fromTime:(a=t.fromTime)==null?void 0:a[0],toTime:(i=t.fromTime)==null?void 0:i[1],creator:(r=t.creator)==null?void 0:r.join(","),maintainUser:(l=t.maintainUser)==null?void 0:l.join(",")}).then(w=>{var I;const s=(I=w.data)==null?void 0:I.data;o.dataList=(s==null?void 0:s.data)||[],o.pagination.total=(s==null?void 0:s.total)||0})},R=async()=>{var e,a;p.layerIds=it(y.view);const t=await tt(p.layerIds);p.layerInfos=((a=(e=t.data)==null?void 0:e.result)==null?void 0:a.rows)||[]},q=async t=>{var e,a;y.view=t,(e=b.value)==null||e.toggleCustomDetail(!0),await E(),(a=b.value)==null||a.toggleCustomDetailMaxmin("max"),await R()};return j(()=>{u()}),(t,e)=>{const a=at,i=J,r=H,l=G;return L(),M(z,null,[d(mt,{ref_key:"refMap",ref:b,title:"任务管理","hide-right-drawer":!0,"detail-max-min":!0,"hide-detail-close":!0,onMapLoaded:q},{"detail-header":T(()=>e[1]||(e[1]=[m("span",null,"任务管理",-1)])),"detail-default":T(()=>[m("div",dt,[O(m("div",ut,[d(a,{ref_key:"refSearch",ref:h,config:n(U),style:{"margin-bottom":"8px"}},null,8,["config"]),m("div",gt,[d(i,{config:n(o)},null,8,["config"])])],512),[[$,n(p).curPage==="table"]]),n(p).curPage==="detail"?(L(),M("div",ft,[m("div",bt,[d(r,{onClick:F},{default:T(()=>[d(n(A))]),_:1}),e[2]||(e[2]=m("div",{class:"detail-header-divider"},null,-1)),e[3]||(e[3]=m("span",null,"养护任务详情",-1))]),m("div",_t,[d(nt,{view:y.view,row:n(o).currentRow,onRowClick:e[0]||(e[0]=w=>{var s;return(s=n(b))==null?void 0:s.toggleCustomDetailMaxmin("normal")})},null,8,["view","row"])])])):X("",!0)])]),_:1},512),d(l,{ref_key:"refDialogForm",ref:f,config:n(k)},null,8,["config"])],64)}}}),Na=et(ht,[["__scopeId","data-v-eb0fba3e"]]);export{Na as default};
