package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTask;
import org.thingsboard.server.dao.util.imodel.query.GeneralTaskPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class SMCircuitTaskPageRequest extends GeneralTaskPageableQueryEntity<SMCircuitTask, SMCircuitTaskPageRequest> {

    // 是否为常规计划
    private Boolean isNormalPlan;

    // 是否被接收
    private Boolean isReceived;

    // 是否已完成
    private Boolean isComplete;

    // 开始时间
    private Date beginTimeFrom;

    // 开始时间
    private Date beginTimeTo;

    // 接收人员(巡检员)Id
    private String receiveUserId;

    // 创建人Id
    private String creator;

    // 共同完成人Id
    private String collaborateUserId;

    // 任务编号/任务名称/备注
    private String keyword;


}
