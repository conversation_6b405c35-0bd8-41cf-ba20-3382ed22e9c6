package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.plan.PlanTask;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskPageRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskStartRequest;

public interface PlanTaskService {
    /**
     * 分页条件查询盘点任务
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<PlanTask> findAllConditional(PlanTaskPageRequest request);

    /**
     * 保存盘点任务
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    PlanTask save(PlanTaskSaveRequest entity);

    /**
     * 保存原生盘点任务
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    PlanTask save(PlanTask entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(PlanTask entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 完成盘点
     *
     * @param req 明细
     * @return 是否成功
     */
    boolean complete(PlanTaskCompleteRequest req);

    /**
     * 重置盘点状态
     *
     * @param id 唯一标识
     * @return 是否成功
     */
    boolean reset(String id);

    /**
     * 开始盘点
     *
     * @param req 明细
     * @return 是否成功
     */
    boolean start(PlanTaskStartRequest req);

    /**
     * 判断盘点任务的执行人是否为指定用户
     *
     * @param id          唯一标识
     * @param currentUser 指定用户
     * @return 执行人是否为指定用户
     */
    boolean canOperate(String id, String currentUser);

}
