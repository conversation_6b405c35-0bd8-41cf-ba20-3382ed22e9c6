<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.plan.PlanMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           execution_user_id,
                           start_time,
                           end_time,
                           execution_days,
                           interval_days,
                           execution_num,
                           execution_type,
                           storehouse_id,
                           (select name from store where id = storehouse_id) storehouse_name,
                           remark,
                           creator,
                           create_time,
                           tenant_id<!--@sql from plan -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.plan.Plan">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="execution_user_id" property="executionUserId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="execution_days" property="executionDays"/>
        <result column="interval_days" property="intervalDays"/>
        <result column="execution_num" property="executionNum"/>
        <result column="execution_type" property="executionType"/>
        <result column="storehouse_id" property="storehouseId"/>
        <result column="storehouse_name" property="storehouseName"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from plan
        <where>
            <if test="keyWord != null and keyWord != ''">
                and "name" like '%' || #{keyWord} || '%'
            </if>
            <if test="name != null and name != ''">
                and "name" like '%' || #{name} || '%'
            </if>
            <if test="executionUserId != null and executionUserId != ''">
                and execution_user_id = #{executionUserId}
            </if>
            <if test="executionDepartmentId != null and executionDepartmentId != ''">
                and is_user_at_department(plan.execution_user_id, #{executionDepartmentId})
            </if>
            <if test="storehouseId != null and storehouseId != ''">
                and storehouse_id = #{storehouseId}
            </if>
            <if test="startTimeFrom != null">
                and start_time >= #{startTimeFrom}
            </if>
            <if test="startTimeTo != null">
                and start_time &lt;= #{startTimeTo}
            </if>
            <if test="endTimeFrom != null">
                and end_time >= #{endTimeFrom}
            </if>
            <if test="endTimeTo != null">
                and end_time &lt;= #{endTimeTo}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update plan
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="executionUserId != null">
                execution_user_id = #{executionUserId},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="executionDays != null">
                execution_days = #{executionDays},
            </if>
            <if test="intervalDays != null">
                interval_days = #{intervalDays},
            </if>
            <if test="executionNum != null">
                execution_num = #{executionNum},
            </if>
            <if test="executionType != null">
                execution_type = #{executionType},
            </if>
            <if test="storehouseId != null">
                storehouse_id = #{storehouseId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>