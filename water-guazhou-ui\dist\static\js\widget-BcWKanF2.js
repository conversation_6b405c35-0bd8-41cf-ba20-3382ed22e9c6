import{e as v,O as rr,a as ce,v as nr,aG as wn,aA as Xe,an as Ye,j as Sn,aO as En,aP as sr,ar as ir,am as An,ak as Tn,Q as nt,i as st,ag as _n,s as oe,aH as bt,M as kn,x as Ln,aQ as xn,aR as Rn,ab as wt,y as w,ai as Cn,u as Nn}from"./Point-WxyopZva.js";import{aT as or,a_ as it,fB as Pn,R as On,aW as St,a5 as In}from"./index-r0dFAfgr.js";import{U as Dn,a as ar,F as Fn}from"./pe-B8dP0-Ut.js";let Ee=class lr{constructor(){this._emitter=new lr.EventEmitter(this)}emit(t,r){return this._emitter.emit(t,r)}on(t,r){return this._emitter.on(t,r)}once(t,r){return this._emitter.once(t,r)}hasEventListener(t){return this._emitter.hasEventListener(t)}};(function(e){class t{constructor(s=null){this._target=s,this._listenersMap=null}clear(){this._listenersMap&&this._listenersMap.clear(),this._listenersMap=null}emit(s,i){const o=this._listenersMap&&this._listenersMap.get(s);if(!o)return!1;const l=this._target||this;return[...o].forEach(a=>{a.call(l,i)}),o.length>0}on(s,i){if(Array.isArray(s)){const l=s.map(a=>this.on(a,i));return rr(l)}if(s.includes(","))throw new TypeError("Evented.on() with a comma delimited string of event types is not supported");this._listenersMap||(this._listenersMap=new Map);const o=this._listenersMap.get(s)||[];return o.push(i),this._listenersMap.set(s,o),{remove:()=>{const l=this._listenersMap&&this._listenersMap.get(s)||[],a=l.indexOf(i);a>=0&&l.splice(a,1)}}}once(s,i){const o=this.on(s,l=>{o.remove(),i.call(null,l)});return o}hasEventListener(s){const i=this._listenersMap&&this._listenersMap.get(s);return i!=null&&i.length>0}}e.EventEmitter=t,e.EventedMixin=n=>{let s=class extends n{constructor(){super(...arguments),this._emitter=new t}destroy(){this._emitter.clear()}emit(i,o){return this._emitter.emit(i,o)}on(i,o){return this._emitter.on(i,o)}once(i,o){return this._emitter.once(i,o)}hasEventListener(i){return this._emitter.hasEventListener(i)}};return s=v([ce("esri.core.Evented")],s),s};let r=class extends nr{constructor(){super(...arguments),this._emitter=new Ee.EventEmitter(this)}destroy(){this._emitter.clear()}emit(n,s){return this._emitter.emit(n,s)}on(n,s){return this._emitter.on(n,s)}once(n,s){return this._emitter.once(n,s)}hasEventListener(n){return this._emitter.hasEventListener(n)}};r=v([ce("esri.core.Evented")],r),e.EventedAccessor=r})(Ee||(Ee={}));const Mn=Ee;let ae;var Vt,er;const Un=((Vt=globalThis.esriConfig)==null?void 0:Vt.locale)??((er=globalThis.dojoConfig)==null?void 0:er.locale);function cr(){var e;return Un??((e=globalThis.navigator)==null?void 0:e.language)??"en"}function J(){return ae===void 0&&(ae=cr()),ae}function ia(e=J()){var t;return(t=/^([a-zA-Z]{2,3})(?:[_\-]\w+)*$/.exec(e))==null?void 0:t[1].toLowerCase()}const le=[];function Bn(e){return le.push(e),{remove(){le.splice(le.indexOf(e),1)}}}const Qe=[];function ot(e){return Qe.push(e),{remove(){le.splice(Qe.indexOf(e),1)}}}function jn(){const e=cr();ae!==e&&(ae=e,[...Qe].forEach(t=>{t.call(null,e)}),[...le].forEach(t=>{t.call(null,e)}))}var tr;(tr=globalThis.addEventListener)==null||tr.call(globalThis,"languagechange",jn);const R={year:"numeric",month:"numeric",day:"numeric"},ee={year:"numeric",month:"long",day:"numeric"},te={year:"numeric",month:"short",day:"numeric"},re={year:"numeric",month:"long",weekday:"long",day:"numeric"},E={hour:"numeric",minute:"numeric"},T={...E,second:"numeric"},at={"short-date":R,"short-date-short-time":{...R,...E},"short-date-short-time-24":{...R,...E,hour12:!1},"short-date-long-time":{...R,...T},"short-date-long-time-24":{...R,...T,hour12:!1},"short-date-le":R,"short-date-le-short-time":{...R,...E},"short-date-le-short-time-24":{...R,...E,hour12:!1},"short-date-le-long-time":{...R,...T},"short-date-le-long-time-24":{...R,...T,hour12:!1},"long-month-day-year":ee,"long-month-day-year-short-time":{...ee,...E},"long-month-day-year-short-time-24":{...ee,...E,hour12:!1},"long-month-day-year-long-time":{...ee,...T},"long-month-day-year-long-time-24":{...ee,...T,hour12:!1},"day-short-month-year":te,"day-short-month-year-short-time":{...te,...E},"day-short-month-year-short-time-24":{...te,...E,hour12:!1},"day-short-month-year-long-time":{...te,...T},"day-short-month-year-long-time-24":{...te,...T,hour12:!1},"long-date":re,"long-date-short-time":{...re,...E},"long-date-short-time-24":{...re,...E,hour12:!1},"long-date-long-time":{...re,...T},"long-date-long-time-24":{...re,...T,hour12:!1},"long-month-year":{month:"long",year:"numeric"},"short-month-year":{month:"short",year:"numeric"},year:{year:"numeric"},"short-time":E,"long-time":T},ue=wn()({shortDate:"short-date",shortDateShortTime:"short-date-short-time",shortDateShortTime24:"short-date-short-time-24",shortDateLongTime:"short-date-long-time",shortDateLongTime24:"short-date-long-time-24",shortDateLE:"short-date-le",shortDateLEShortTime:"short-date-le-short-time",shortDateLEShortTime24:"short-date-le-short-time-24",shortDateLELongTime:"short-date-le-long-time",shortDateLELongTime24:"short-date-le-long-time-24",longMonthDayYear:"long-month-day-year",longMonthDayYearShortTime:"long-month-day-year-short-time",longMonthDayYearShortTime24:"long-month-day-year-short-time-24",longMonthDayYearLongTime:"long-month-day-year-long-time",longMonthDayYearLongTime24:"long-month-day-year-long-time-24",dayShortMonthYear:"day-short-month-year",dayShortMonthYearShortTime:"day-short-month-year-short-time",dayShortMonthYearShortTime24:"day-short-month-year-short-time-24",dayShortMonthYearLongTime:"day-short-month-year-long-time",dayShortMonthYearLongTime24:"day-short-month-year-long-time-24",longDate:"long-date",longDateShortTime:"long-date-short-time",longDateShortTime24:"long-date-short-time-24",longDateLongTime:"long-date-long-time",longDateLongTime24:"long-date-long-time-24",longMonthYear:"long-month-year",shortMonthYear:"short-month-year",year:"year"});ue.apiValues;ue.toJSON.bind(ue);ue.fromJSON.bind(ue);const Hn={ar:"ar-u-nu-latn-ca-gregory"};let Je=new WeakMap,ur=at["short-date-short-time"];function zn(e){const t=e||ur;let r=Je.get(t);if(!r){const n=J(),s=Hn[J()]||n;r=new Intl.DateTimeFormat(s,t),Je.set(t,r)}return r}function oa(e){return e?at[e]:null}function _e(e,t){return zn(t).format(e)}ot(()=>{Je=new WeakMap,ur=at["short-date-short-time"]});const Wn={ar:"ar-u-nu-latn"};let Ae=new WeakMap,fr={};function qn(e){const t=e||fr;if(!Ae.has(t)){const r=J(),n=Wn[J()]||r;Ae.set(t,new Intl.NumberFormat(n,e))}return or(Ae.get(t))}function aa(e={}){const t={};return e.digitSeparator!=null&&(t.useGrouping=e.digitSeparator),e.places!=null&&(t.minimumFractionDigits=t.maximumFractionDigits=e.places),t}function ke(e,t){return e===-0&&(e=0),qn(t).format(e)}ot(()=>{Ae=new WeakMap,fr={}});var P;(function(e){e[e.PENDING=0]="PENDING",e[e.RESOLVED=1]="RESOLVED",e[e.REJECTED=2]="REJECTED"})(P||(P={}));let Gn=class{constructor(t){this.instance=t,this._resolver=Xe(),this._status=P.PENDING,this._resolvingPromises=[],this._resolver.promise.then(()=>{this._status=P.RESOLVED,this._cleanUp()},()=>{this._status=P.REJECTED,this._cleanUp()})}addResolvingPromise(t){this._resolvingPromises.push(t),this._tryResolve()}isResolved(){return this._status===P.RESOLVED}isRejected(){return this._status===P.REJECTED}isFulfilled(){return this._status!==P.PENDING}abort(){this._resolver.reject(Ye())}when(t,r){return this._resolver.promise.then(t,r)}_cleanUp(){this._allPromise=this._resolvingPromises=this._allPromise=null}_tryResolve(){if(this.isFulfilled())return;const t=Xe(),r=[...this._resolvingPromises,or(t.promise)],n=this._allPromise=Promise.all(r);n.then(()=>{this.isFulfilled()||this._allPromise!==n||this._resolver.resolve(this.instance)},s=>{this.isFulfilled()||this._allPromise!==n||Sn(s)||this._resolver.reject(s)}),t.resolve()}};const dr=e=>{let t=class extends e{constructor(...r){super(...r),this._promiseProps=new Gn(this),this.addResolvingPromise(Promise.resolve())}isResolved(){return this._promiseProps.isResolved()}isRejected(){return this._promiseProps.isRejected()}isFulfilled(){return this._promiseProps.isFulfilled()}when(r,n){return new Promise((s,i)=>{this._promiseProps.when(s,i)}).then(r,n)}catch(r){return this.when(null,r)}addResolvingPromise(r){r&&!this._promiseProps.isFulfilled()&&this._promiseProps.addResolvingPromise("_promiseProps"in r?r.when():r)}};return t=v([ce("esri.core.Promise")],t),t};let Et=class extends dr(nr){};Et=v([ce("esri.core.Promise")],Et);function Le(e,t,r={}){return lt(e,t,r,hr)}function Xn(e,t,r={}){return lt(e,t,r,pr)}function lt(e,t,r={},n){let s=null;const i=r.once?(o,l)=>{n(o)&&(it(s),t(o,l))}:(o,l)=>{n(o)&&t(o,l)};if(s=En(e,i,r.sync,r.equals),r.initial){const o=e();i(o,o)}return s}function ua(e,t,r,n={}){let s=null,i=null,o=null;function l(){var f;s&&i&&(i.remove(),(f=n.onListenerRemove)==null||f.call(n,s),s=null,i=null)}function a(f){n.once&&n.once&&it(o),r(f)}const c=Le(e,(f,u)=>{var d;l(),sr(f)&&(s=f,i=ir(f,t,a),(d=n.onListenerAdd)==null||d.call(n,f))},{sync:n.sync,initial:!0});return o=nt(()=>{c.remove(),l()}),o}function fa(e,t){return Yn(e,pr,t)}function Yn(e,t,r){if(An(r))return Promise.reject(Ye());const n=e();if(t!=null&&t(n))return Promise.resolve(n);let s=null;function i(){s=it(s)}return new Promise((o,l)=>{s=rr([Tn(r,()=>{i(),l(Ye())}),lt(e,a=>{i(),o(a)},{sync:!1,once:!0},t??hr)])})}function hr(e){return!0}function pr(e){return!!e}function da(e,t,r={}){let n=!1;const s=Le(e,(i,o)=>{n||t(i,o)},r);return{remove(){s.remove()},pause(){n=!0},resume(){n=!1}}}const ha={sync:!0},Qn={initial:!0},pa={sync:!0,initial:!0},gr=st.getLogger("esri.intl.substitute");function ga(e,t,r={}){const{format:n={}}=r;return _n(e,s=>Jn(s,t,n))}function Jn(e,t,r){let n,s;const i=e.indexOf(":");if(i===-1?n=e.trim():(n=e.slice(0,i).trim(),s=e.slice(i+1).trim()),!n)return"";const o=Pn(n,t);if(o==null)return"";const l=(s?r==null?void 0:r[s]:null)??(r==null?void 0:r[n]);return l?Zn(o,l):s?Kn(o,s):ct(o)}function Zn(e,t){switch(t.type){case"date":return _e(e,t.intlOptions);case"number":return ke(e,t.intlOptions);default:return gr.warn("missing format descriptor for key {key}"),ct(e)}}function Kn(e,t){switch(t.toLowerCase()){case"dateformat":return _e(e);case"numberformat":return ke(e);default:return gr.warn(`inline format is unsupported since 4.12: ${t}`),/^(dateformat|datestring)/i.test(t)?_e(e):/^numberformat/i.test(t)?ke(e):ct(e)}}function ct(e){switch(typeof e){case"string":return e;case"number":return ke(e);case"boolean":return""+e;default:return e instanceof Date?_e(e):""}}const At=/^([a-z]{2})(?:[-_]([A-Za-z]{2}))?$/,Vn={ar:!0,bg:!0,bs:!0,ca:!0,cs:!0,da:!0,de:!0,el:!0,en:!0,es:!0,et:!0,fi:!0,fr:!0,he:!0,hr:!0,hu:!0,id:!0,it:!0,ja:!0,ko:!0,lt:!0,lv:!0,nb:!0,nl:!0,pl:!0,"pt-BR":!0,"pt-PT":!0,ro:!0,ru:!0,sk:!0,sl:!0,sr:!0,sv:!0,th:!0,tr:!0,uk:!0,vi:!0,"zh-CN":!0,"zh-HK":!0,"zh-TW":!0};function Tt(e){return Vn[e]??!1}const ie=[],Y=new Map;function _t(e){for(const t of Y.keys())mr(e.pattern,t)&&Y.delete(t)}function es(e){return ie.includes(e)||(_t(e),ie.unshift(e)),{remove(){const t=ie.indexOf(e);t>-1&&(ie.splice(t,1),_t(e))}}}async function ts(e){const t=J();Y.has(e)||Y.set(e,ns(e,t));const r=Y.get(e);return r&&await ss.add(r),r}function rs(e){if(!At.test(e))return null;const t=At.exec(e);if(t===null)return null;const[,r,n]=t,s=r+(n?"-"+n.toUpperCase():"");return Tt(s)?s:Tt(r)?r:null}async function ns(e,t){const r=[];for(const n of ie)if(mr(n.pattern,e))try{return await n.fetchMessageBundle(e,t)}catch(s){r.push(s)}throw r.length?new oe("intl:message-bundle-error",`Errors occurred while loading "${e}"`,{errors:r}):new oe("intl:no-message-bundle-loader",`No loader found for message bundle "${e}"`)}function mr(e,t){return typeof e=="string"?t.startsWith(e):e.test(t)}ot(()=>{Y.clear()});const ss=new class{constructor(){this._numLoading=0,this._dfd=null}async waitForAll(){this._dfd&&await this._dfd.promise}add(e){return this._increase(),e.then(()=>this._decrease(),()=>this._decrease()),this.waitForAll()}_increase(){this._numLoading++,this._dfd||(this._dfd=Xe())}_decrease(){this._numLoading=Math.max(this._numLoading-1,0),this._dfd&&this._numLoading===0&&(this._dfd.resolve(),this._dfd=null)}};async function is(e,t,r,n){const s=t.exec(r);if(!s)throw new oe("esri-intl:invalid-bundle",`Bundle id "${r}" is not compatible with the pattern "${t}"`);const i=s[1]?`${s[1]}/`:"",o=s[2],l=rs(n),a=`${i}${o}.json`,c=l?`${i}${o}_${l}.json`:a;let f;try{f=await kt(e(c))}catch(u){if(c===a)throw new oe("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:u});try{f=await kt(e(a))}catch(d){throw new oe("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:d})}}return f}async function kt(e){if(On(Lt.fetchBundleAsset))return Lt.fetchBundleAsset(e);const t=await Dn(e,{responseType:"text"});return JSON.parse(t.data)}let os=class{constructor({base:t="",pattern:r,location:n=new URL(window.location.href)}){let s;s=typeof n=="string"?i=>new URL(i,new URL(n,window.location.href)).href:n instanceof URL?i=>new URL(i,n).href:n,this.pattern=typeof r=="string"?new RegExp(`^${r}`):r,this.getAssetUrl=s,t=t?t.endsWith("/")?t:t+"/":"",this.matcher=new RegExp(`^${t}(?:(.*)/)?(.*)$`)}fetchMessageBundle(t,r){return is(this.getAssetUrl,this.matcher,t,r)}};function as(e){return new os(e)}const Lt={};es(as({pattern:"esri/",location:ar}));function ls(e){return typeof e=="string"?document.getElementById(e):e??null}function $a(e){for(;e.hasChildNodes();)e.removeChild(e.firstChild)}function va(e,t){const r=t.parentNode;r&&r.insertBefore(e,t)}function ya(e,t){for(;;){const r=e.firstChild;if(!r)break;t.appendChild(r)}}function ba(e){e.parentNode&&e.parentNode.removeChild(e)}const cs="randomUUID"in crypto;function us(){if(cs)return crypto.randomUUID();const e=crypto.getRandomValues(new Uint16Array(8));e[3]=4095&e[3]|16384,e[4]=16383&e[4]|32768;const t=r=>e[r].toString(16).padStart(4,"0");return t(0)+t(1)+"-"+t(2)+"-"+t(3)+"-"+t(4)+"-"+t(5)+t(6)+t(7)}/*!
 * @esri/arcgis-html-sanitizer - v3.0.1 - Tue Nov 15 2022 09:46:54 GMT-0800 (Pacific Standard Time)
 * Copyright (c) 2022 - Environmental Systems Research Institute, Inc.
 * Apache-2.0
 * 
 * js-xss
 * Copyright (c) 2012-2018 Zongmin Lei(雷宗民) <<EMAIL>>
 * http://ucdok.com
 * MIT License, see https://github.com/leizongmin/js-xss/blob/master/LICENSE for details
 */var fs=function(e){if(typeof e!="object"||e===null||Object.prototype.toString.call(e)!=="[object Object]")return!1;var t=Object.getPrototypeOf(e);if(t===null)return!0;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t},F={exports:{}},m={},fe={exports:{}},j={};function $r(){var e={};return e["align-content"]=!1,e["align-items"]=!1,e["align-self"]=!1,e["alignment-adjust"]=!1,e["alignment-baseline"]=!1,e.all=!1,e["anchor-point"]=!1,e.animation=!1,e["animation-delay"]=!1,e["animation-direction"]=!1,e["animation-duration"]=!1,e["animation-fill-mode"]=!1,e["animation-iteration-count"]=!1,e["animation-name"]=!1,e["animation-play-state"]=!1,e["animation-timing-function"]=!1,e.azimuth=!1,e["backface-visibility"]=!1,e.background=!0,e["background-attachment"]=!0,e["background-clip"]=!0,e["background-color"]=!0,e["background-image"]=!0,e["background-origin"]=!0,e["background-position"]=!0,e["background-repeat"]=!0,e["background-size"]=!0,e["baseline-shift"]=!1,e.binding=!1,e.bleed=!1,e["bookmark-label"]=!1,e["bookmark-level"]=!1,e["bookmark-state"]=!1,e.border=!0,e["border-bottom"]=!0,e["border-bottom-color"]=!0,e["border-bottom-left-radius"]=!0,e["border-bottom-right-radius"]=!0,e["border-bottom-style"]=!0,e["border-bottom-width"]=!0,e["border-collapse"]=!0,e["border-color"]=!0,e["border-image"]=!0,e["border-image-outset"]=!0,e["border-image-repeat"]=!0,e["border-image-slice"]=!0,e["border-image-source"]=!0,e["border-image-width"]=!0,e["border-left"]=!0,e["border-left-color"]=!0,e["border-left-style"]=!0,e["border-left-width"]=!0,e["border-radius"]=!0,e["border-right"]=!0,e["border-right-color"]=!0,e["border-right-style"]=!0,e["border-right-width"]=!0,e["border-spacing"]=!0,e["border-style"]=!0,e["border-top"]=!0,e["border-top-color"]=!0,e["border-top-left-radius"]=!0,e["border-top-right-radius"]=!0,e["border-top-style"]=!0,e["border-top-width"]=!0,e["border-width"]=!0,e.bottom=!1,e["box-decoration-break"]=!0,e["box-shadow"]=!0,e["box-sizing"]=!0,e["box-snap"]=!0,e["box-suppress"]=!0,e["break-after"]=!0,e["break-before"]=!0,e["break-inside"]=!0,e["caption-side"]=!1,e.chains=!1,e.clear=!0,e.clip=!1,e["clip-path"]=!1,e["clip-rule"]=!1,e.color=!0,e["color-interpolation-filters"]=!0,e["column-count"]=!1,e["column-fill"]=!1,e["column-gap"]=!1,e["column-rule"]=!1,e["column-rule-color"]=!1,e["column-rule-style"]=!1,e["column-rule-width"]=!1,e["column-span"]=!1,e["column-width"]=!1,e.columns=!1,e.contain=!1,e.content=!1,e["counter-increment"]=!1,e["counter-reset"]=!1,e["counter-set"]=!1,e.crop=!1,e.cue=!1,e["cue-after"]=!1,e["cue-before"]=!1,e.cursor=!1,e.direction=!1,e.display=!0,e["display-inside"]=!0,e["display-list"]=!0,e["display-outside"]=!0,e["dominant-baseline"]=!1,e.elevation=!1,e["empty-cells"]=!1,e.filter=!1,e.flex=!1,e["flex-basis"]=!1,e["flex-direction"]=!1,e["flex-flow"]=!1,e["flex-grow"]=!1,e["flex-shrink"]=!1,e["flex-wrap"]=!1,e.float=!1,e["float-offset"]=!1,e["flood-color"]=!1,e["flood-opacity"]=!1,e["flow-from"]=!1,e["flow-into"]=!1,e.font=!0,e["font-family"]=!0,e["font-feature-settings"]=!0,e["font-kerning"]=!0,e["font-language-override"]=!0,e["font-size"]=!0,e["font-size-adjust"]=!0,e["font-stretch"]=!0,e["font-style"]=!0,e["font-synthesis"]=!0,e["font-variant"]=!0,e["font-variant-alternates"]=!0,e["font-variant-caps"]=!0,e["font-variant-east-asian"]=!0,e["font-variant-ligatures"]=!0,e["font-variant-numeric"]=!0,e["font-variant-position"]=!0,e["font-weight"]=!0,e.grid=!1,e["grid-area"]=!1,e["grid-auto-columns"]=!1,e["grid-auto-flow"]=!1,e["grid-auto-rows"]=!1,e["grid-column"]=!1,e["grid-column-end"]=!1,e["grid-column-start"]=!1,e["grid-row"]=!1,e["grid-row-end"]=!1,e["grid-row-start"]=!1,e["grid-template"]=!1,e["grid-template-areas"]=!1,e["grid-template-columns"]=!1,e["grid-template-rows"]=!1,e["hanging-punctuation"]=!1,e.height=!0,e.hyphens=!1,e.icon=!1,e["image-orientation"]=!1,e["image-resolution"]=!1,e["ime-mode"]=!1,e["initial-letters"]=!1,e["inline-box-align"]=!1,e["justify-content"]=!1,e["justify-items"]=!1,e["justify-self"]=!1,e.left=!1,e["letter-spacing"]=!0,e["lighting-color"]=!0,e["line-box-contain"]=!1,e["line-break"]=!1,e["line-grid"]=!1,e["line-height"]=!1,e["line-snap"]=!1,e["line-stacking"]=!1,e["line-stacking-ruby"]=!1,e["line-stacking-shift"]=!1,e["line-stacking-strategy"]=!1,e["list-style"]=!0,e["list-style-image"]=!0,e["list-style-position"]=!0,e["list-style-type"]=!0,e.margin=!0,e["margin-bottom"]=!0,e["margin-left"]=!0,e["margin-right"]=!0,e["margin-top"]=!0,e["marker-offset"]=!1,e["marker-side"]=!1,e.marks=!1,e.mask=!1,e["mask-box"]=!1,e["mask-box-outset"]=!1,e["mask-box-repeat"]=!1,e["mask-box-slice"]=!1,e["mask-box-source"]=!1,e["mask-box-width"]=!1,e["mask-clip"]=!1,e["mask-image"]=!1,e["mask-origin"]=!1,e["mask-position"]=!1,e["mask-repeat"]=!1,e["mask-size"]=!1,e["mask-source-type"]=!1,e["mask-type"]=!1,e["max-height"]=!0,e["max-lines"]=!1,e["max-width"]=!0,e["min-height"]=!0,e["min-width"]=!0,e["move-to"]=!1,e["nav-down"]=!1,e["nav-index"]=!1,e["nav-left"]=!1,e["nav-right"]=!1,e["nav-up"]=!1,e["object-fit"]=!1,e["object-position"]=!1,e.opacity=!1,e.order=!1,e.orphans=!1,e.outline=!1,e["outline-color"]=!1,e["outline-offset"]=!1,e["outline-style"]=!1,e["outline-width"]=!1,e.overflow=!1,e["overflow-wrap"]=!1,e["overflow-x"]=!1,e["overflow-y"]=!1,e.padding=!0,e["padding-bottom"]=!0,e["padding-left"]=!0,e["padding-right"]=!0,e["padding-top"]=!0,e.page=!1,e["page-break-after"]=!1,e["page-break-before"]=!1,e["page-break-inside"]=!1,e["page-policy"]=!1,e.pause=!1,e["pause-after"]=!1,e["pause-before"]=!1,e.perspective=!1,e["perspective-origin"]=!1,e.pitch=!1,e["pitch-range"]=!1,e["play-during"]=!1,e.position=!1,e["presentation-level"]=!1,e.quotes=!1,e["region-fragment"]=!1,e.resize=!1,e.rest=!1,e["rest-after"]=!1,e["rest-before"]=!1,e.richness=!1,e.right=!1,e.rotation=!1,e["rotation-point"]=!1,e["ruby-align"]=!1,e["ruby-merge"]=!1,e["ruby-position"]=!1,e["shape-image-threshold"]=!1,e["shape-outside"]=!1,e["shape-margin"]=!1,e.size=!1,e.speak=!1,e["speak-as"]=!1,e["speak-header"]=!1,e["speak-numeral"]=!1,e["speak-punctuation"]=!1,e["speech-rate"]=!1,e.stress=!1,e["string-set"]=!1,e["tab-size"]=!1,e["table-layout"]=!1,e["text-align"]=!0,e["text-align-last"]=!0,e["text-combine-upright"]=!0,e["text-decoration"]=!0,e["text-decoration-color"]=!0,e["text-decoration-line"]=!0,e["text-decoration-skip"]=!0,e["text-decoration-style"]=!0,e["text-emphasis"]=!0,e["text-emphasis-color"]=!0,e["text-emphasis-position"]=!0,e["text-emphasis-style"]=!0,e["text-height"]=!0,e["text-indent"]=!0,e["text-justify"]=!0,e["text-orientation"]=!0,e["text-overflow"]=!0,e["text-shadow"]=!0,e["text-space-collapse"]=!0,e["text-transform"]=!0,e["text-underline-position"]=!0,e["text-wrap"]=!0,e.top=!1,e.transform=!1,e["transform-origin"]=!1,e["transform-style"]=!1,e.transition=!1,e["transition-delay"]=!1,e["transition-duration"]=!1,e["transition-property"]=!1,e["transition-timing-function"]=!1,e["unicode-bidi"]=!1,e["vertical-align"]=!1,e.visibility=!1,e["voice-balance"]=!1,e["voice-duration"]=!1,e["voice-family"]=!1,e["voice-pitch"]=!1,e["voice-range"]=!1,e["voice-rate"]=!1,e["voice-stress"]=!1,e["voice-volume"]=!1,e.volume=!1,e["white-space"]=!1,e.widows=!1,e.width=!0,e["will-change"]=!1,e["word-break"]=!0,e["word-spacing"]=!0,e["word-wrap"]=!0,e["wrap-flow"]=!1,e["wrap-through"]=!1,e["writing-mode"]=!1,e["z-index"]=!1,e}function ds(e,t,r){}function hs(e,t,r){}var ps=/javascript\s*\:/img;function gs(e,t){return ps.test(t)?"":t}j.whiteList=$r();j.getDefaultWhiteList=$r;j.onAttr=ds;j.onIgnoreAttr=hs;j.safeAttrValue=gs;var ms={indexOf:function(e,t){var r,n;if(Array.prototype.indexOf)return e.indexOf(t);for(r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1},forEach:function(e,t,r){var n,s;if(Array.prototype.forEach)return e.forEach(t,r);for(n=0,s=e.length;n<s;n++)t.call(r,e[n],n,e)},trim:function(e){return String.prototype.trim?e.trim():e.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(e){return String.prototype.trimRight?e.trimRight():e.replace(/(\s*$)/g,"")}},ne=ms;function $s(e,t){e=ne.trimRight(e),e[e.length-1]!==";"&&(e+=";");var r=e.length,n=!1,s=0,i=0,o="";function l(){if(!n){var f=ne.trim(e.slice(s,i)),u=f.indexOf(":");if(u!==-1){var d=ne.trim(f.slice(0,u)),h=ne.trim(f.slice(u+1));if(d){var p=t(s,o.length,d,h,f);p&&(o+=p+"; ")}}}s=i+1}for(;i<r;i++){var a=e[i];if(a==="/"&&e[i+1]==="*"){var c=e.indexOf("*/",i+2);if(c===-1)break;i=c+1,s=i+1,n=!1}else a==="("?n=!0:a===")"?n=!1:a===";"?n||l():a===`
`&&l()}return ne.trim(o)}var vs=$s,ye=j,ys=vs;function xt(e){return e==null}function bs(e){var t={};for(var r in e)t[r]=e[r];return t}function vr(e){e=bs(e||{}),e.whiteList=e.whiteList||ye.whiteList,e.onAttr=e.onAttr||ye.onAttr,e.onIgnoreAttr=e.onIgnoreAttr||ye.onIgnoreAttr,e.safeAttrValue=e.safeAttrValue||ye.safeAttrValue,this.options=e}vr.prototype.process=function(e){if(e=e||"",e=e.toString(),!e)return"";var t=this,r=t.options,n=r.whiteList,s=r.onAttr,i=r.onIgnoreAttr,o=r.safeAttrValue,l=ys(e,function(a,c,f,u,d){var h=n[f],p=!1;if(h===!0?p=h:typeof h=="function"?p=h(u):h instanceof RegExp&&(p=h.test(u)),p!==!0&&(p=!1),u=o(f,u),!!u){var g={position:c,sourcePosition:a,source:d,isWhite:p};if(p){var $=s(f,u,g);return xt($)?f+":"+u:$}else{var $=i(f,u,g);if(!xt($))return $}}});return l};var ws=vr;(function(e,t){var r=j,n=ws;function s(o,l){var a=new n(l);return a.process(o)}t=e.exports=s,t.FilterCSS=n;for(var i in r)t[i]=r[i]})(fe,fe.exports);var ut={indexOf:function(e,t){var r,n;if(Array.prototype.indexOf)return e.indexOf(t);for(r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1},forEach:function(e,t,r){var n,s;if(Array.prototype.forEach)return e.forEach(t,r);for(n=0,s=e.length;n<s;n++)t.call(r,e[n],n,e)},trim:function(e){return String.prototype.trim?e.trim():e.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(e){var t=/\s|\n|\t/,r=t.exec(e);return r?r.index:-1}},Ss=fe.exports.FilterCSS,Es=fe.exports.getDefaultWhiteList,xe=ut;function yr(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height"],ins:["datetime"],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var br=new Ss;function As(e,t,r){}function Ts(e,t,r){}function _s(e,t,r){}function ks(e,t,r){}function wr(e){return e.replace(xs,"&lt;").replace(Rs,"&gt;")}function Ls(e,t,r,n){if(r=kr(r),t==="href"||t==="src"){if(r=xe.trim(r),r==="#")return"#";if(!(r.substr(0,7)==="http://"||r.substr(0,8)==="https://"||r.substr(0,7)==="mailto:"||r.substr(0,4)==="tel:"||r.substr(0,11)==="data:image/"||r.substr(0,6)==="ftp://"||r.substr(0,2)==="./"||r.substr(0,3)==="../"||r[0]==="#"||r[0]==="/"))return""}else if(t==="background"){if(be.lastIndex=0,be.test(r))return""}else if(t==="style"){if(Rt.lastIndex=0,Rt.test(r)||(Ct.lastIndex=0,Ct.test(r)&&(be.lastIndex=0,be.test(r))))return"";n!==!1&&(n=n||br,r=n.process(r))}return r=Lr(r),r}var xs=/</g,Rs=/>/g,Cs=/"/g,Ns=/&quot;/g,Ps=/&#([a-zA-Z0-9]*);?/gim,Os=/&colon;?/gim,Is=/&newline;?/gim,be=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,Rt=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,Ct=/u\s*r\s*l\s*\(.*/gi;function Sr(e){return e.replace(Cs,"&quot;")}function Er(e){return e.replace(Ns,'"')}function Ar(e){return e.replace(Ps,function(r,n){return n[0]==="x"||n[0]==="X"?String.fromCharCode(parseInt(n.substr(1),16)):String.fromCharCode(parseInt(n,10))})}function Tr(e){return e.replace(Os,":").replace(Is," ")}function _r(e){for(var t="",r=0,n=e.length;r<n;r++)t+=e.charCodeAt(r)<32?" ":e.charAt(r);return xe.trim(t)}function kr(e){return e=Er(e),e=Ar(e),e=Tr(e),e=_r(e),e}function Lr(e){return e=Sr(e),e=wr(e),e}function Ds(){return""}function Fs(e,t){typeof t!="function"&&(t=function(){});var r=!Array.isArray(e);function n(o){return r?!0:xe.indexOf(e,o)!==-1}var s=[],i=!1;return{onIgnoreTag:function(o,l,a){if(n(o))if(a.isClosing){var c="[/removed]",f=a.position+c.length;return s.push([i!==!1?i:a.position,f]),i=!1,c}else return i||(i=a.position),"[removed]";else return t(o,l,a)},remove:function(o){var l="",a=0;return xe.forEach(s,function(c){l+=o.slice(a,c[0]),a=c[1]}),l+=o.slice(a),l}}}function Ms(e){for(var t="",r=0;r<e.length;){var n=e.indexOf("<!--",r);if(n===-1){t+=e.slice(r);break}t+=e.slice(r,n);var s=e.indexOf("-->",n);if(s===-1)break;r=s+3}return t}function Us(e){var t=e.split("");return t=t.filter(function(r){var n=r.charCodeAt(0);return n===127?!1:n<=31?n===10||n===13:!0}),t.join("")}m.whiteList=yr();m.getDefaultWhiteList=yr;m.onTag=As;m.onIgnoreTag=Ts;m.onTagAttr=_s;m.onIgnoreTagAttr=ks;m.safeAttrValue=Ls;m.escapeHtml=wr;m.escapeQuote=Sr;m.unescapeQuote=Er;m.escapeHtmlEntities=Ar;m.escapeDangerHtml5Entities=Tr;m.clearNonPrintableCharacter=_r;m.friendlyAttrValue=kr;m.escapeAttrValue=Lr;m.onIgnoreTagStripAll=Ds;m.StripTagBody=Fs;m.stripCommentTag=Ms;m.stripBlankChar=Us;m.cssFilter=br;m.getDefaultCSSWhiteList=Es;var Ue={},O=ut;function Bs(e){var t=O.spaceIndex(e),r;return t===-1?r=e.slice(1,-1):r=e.slice(1,t+1),r=O.trim(r).toLowerCase(),r.slice(0,1)==="/"&&(r=r.slice(1)),r.slice(-1)==="/"&&(r=r.slice(0,-1)),r}function js(e){return e.slice(0,2)==="</"}function Hs(e,t,r){var n="",s=0,i=!1,o=!1,l=0,a=e.length,c="",f="";e:for(l=0;l<a;l++){var u=e.charAt(l);if(i===!1){if(u==="<"){i=l;continue}}else if(o===!1){if(u==="<"){n+=r(e.slice(s,l)),i=l,s=l;continue}if(u===">"){n+=r(e.slice(s,i)),f=e.slice(i,l+1),c=Bs(f),n+=t(i,n.length,c,f,js(f)),s=l+1,i=!1;continue}if(u==='"'||u==="'")for(var d=1,h=e.charAt(l-d);h.trim()===""||h==="=";){if(h==="="){o=u;continue e}h=e.charAt(l-++d)}}else if(u===o){o=!1;continue}}return s<e.length&&(n+=r(e.substr(s))),n}var zs=/[^a-zA-Z0-9\\_:.-]/gim;function Ws(e,t){var r=0,n=0,s=[],i=!1,o=e.length;function l(d,h){if(d=O.trim(d),d=d.replace(zs,"").toLowerCase(),!(d.length<1)){var p=t(d,h||"");p&&s.push(p)}}for(var a=0;a<o;a++){var c=e.charAt(a),f,u;if(i===!1&&c==="="){i=e.slice(r,a),r=a+1,n=e.charAt(r)==='"'||e.charAt(r)==="'"?r:Gs(e,a+1);continue}if(i!==!1&&a===n){if(u=e.indexOf(c,a+1),u===-1)break;f=O.trim(e.slice(n+1,u)),l(i,f),i=!1,a=u,r=a+1;continue}if(/\s|\n|\t/.test(c))if(e=e.replace(/\s|\n|\t/g," "),i===!1)if(u=qs(e,a),u===-1){f=O.trim(e.slice(r,a)),l(f),i=!1,r=a+1;continue}else{a=u-1;continue}else if(u=Xs(e,a-1),u===-1){f=O.trim(e.slice(r,a)),f=Nt(f),l(i,f),i=!1,r=a+1;continue}else continue}return r<e.length&&(i===!1?l(e.slice(r)):l(i,Nt(O.trim(e.slice(r))))),O.trim(s.join(" "))}function qs(e,t){for(;t<e.length;t++){var r=e[t];if(r!==" ")return r==="="?t:-1}}function Gs(e,t){for(;t<e.length;t++){var r=e[t];if(r!==" ")return r==="'"||r==='"'?t:-1}}function Xs(e,t){for(;t>0;t--){var r=e[t];if(r!==" ")return r==="="?t:-1}}function Ys(e){return e[0]==='"'&&e[e.length-1]==='"'||e[0]==="'"&&e[e.length-1]==="'"}function Nt(e){return Ys(e)?e.substr(1,e.length-2):e}Ue.parseTag=Hs;Ue.parseAttr=Ws;var Qs=fe.exports.FilterCSS,_=m,xr=Ue,Js=xr.parseTag,Zs=xr.parseAttr,Te=ut;function we(e){return e==null}function Ks(e){var t=Te.spaceIndex(e);if(t===-1)return{html:"",closing:e[e.length-2]==="/"};e=Te.trim(e.slice(t+1,-1));var r=e[e.length-1]==="/";return r&&(e=Te.trim(e.slice(0,-1))),{html:e,closing:r}}function Vs(e){var t={};for(var r in e)t[r]=e[r];return t}function ei(e){var t={};for(var r in e)Array.isArray(e[r])?t[r.toLowerCase()]=e[r].map(function(n){return n.toLowerCase()}):t[r.toLowerCase()]=e[r];return t}function Rr(e){e=Vs(e||{}),e.stripIgnoreTag&&(e.onIgnoreTag&&console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time'),e.onIgnoreTag=_.onIgnoreTagStripAll),e.whiteList||e.allowList?e.whiteList=ei(e.whiteList||e.allowList):e.whiteList=_.whiteList,e.onTag=e.onTag||_.onTag,e.onTagAttr=e.onTagAttr||_.onTagAttr,e.onIgnoreTag=e.onIgnoreTag||_.onIgnoreTag,e.onIgnoreTagAttr=e.onIgnoreTagAttr||_.onIgnoreTagAttr,e.safeAttrValue=e.safeAttrValue||_.safeAttrValue,e.escapeHtml=e.escapeHtml||_.escapeHtml,this.options=e,e.css===!1?this.cssFilter=!1:(e.css=e.css||{},this.cssFilter=new Qs(e.css))}Rr.prototype.process=function(e){if(e=e||"",e=e.toString(),!e)return"";var t=this,r=t.options,n=r.whiteList,s=r.onTag,i=r.onIgnoreTag,o=r.onTagAttr,l=r.onIgnoreTagAttr,a=r.safeAttrValue,c=r.escapeHtml,f=t.cssFilter;r.stripBlankChar&&(e=_.stripBlankChar(e)),r.allowCommentTag||(e=_.stripCommentTag(e));var u=!1;r.stripIgnoreTagBody&&(u=_.StripTagBody(r.stripIgnoreTagBody,i),i=u.onIgnoreTag);var d=Js(e,function(h,p,g,$,$e){var K={sourcePosition:h,position:p,isClosing:$e,isWhite:Object.prototype.hasOwnProperty.call(n,g)},H=s(g,$,K);if(!we(H))return H;if(K.isWhite){if(K.isClosing)return"</"+g+">";var ze=Ks($),vt=n[g],L=Zs(ze.html,function(A,x){var V=Te.indexOf(vt,A)!==-1,N=o(g,A,x,V);return we(N)?V?(x=a(g,A,x,f),x?A+'="'+x+'"':A):(N=l(g,A,x,V),we(N)?void 0:N):N});return $="<"+g,L&&($+=" "+L),ze.closing&&($+=" /"),$+=">",$}else return H=i(g,$,K),we(H)?c($):H},c);return u&&(d=u.remove(d)),d};var ti=Rr;(function(e,t){var r=m,n=Ue,s=ti;function i(l,a){var c=new s(a);return c.process(l)}t=e.exports=i,t.filterXSS=i,t.FilterXSS=s,function(){for(var l in r)t[l]=r[l];for(var a in n)t[a]=n[a]}();function o(){return typeof self<"u"&&typeof DedicatedWorkerGlobalScope<"u"&&self instanceof DedicatedWorkerGlobalScope}o()&&(self.filterXSS=e.exports)})(F,F.exports);var ri=function(){function e(t,r){var n=this;this.arcgisWhiteList={a:["href","style","target"],abbr:["title"],audio:["autoplay","controls","loop","muted","preload"],b:[],br:[],dd:["style"],div:["align","style"],dl:["style"],dt:["style"],em:[],figcaption:["style"],figure:["style"],font:["color","face","size","style"],h1:["style"],h2:["style"],h3:["style"],h4:["style"],h5:["style"],h6:["style"],hr:[],i:[],img:["alt","border","height","src","style","width"],li:[],ol:[],p:["style"],source:["media","src","type"],span:["style"],strong:[],sub:["style"],sup:["style"],table:["border","cellpadding","cellspacing","height","style","width"],tbody:[],tr:["align","height","style","valign"],td:["align","colspan","height","nowrap","rowspan","style","valign","width"],th:["align","colspan","height","nowrap","rowspan","style","valign","width"],u:[],ul:[],video:["autoplay","controls","height","loop","muted","poster","preload","width"]},this.allowedProtocols=["http","https","mailto","iform","tel","flow","lfmobile","arcgis-navigator","arcgis-appstudio-player","arcgis-survey123","arcgis-collector","arcgis-workforce","arcgis-explorer","arcgis-trek2there","arcgis-quickcapture","mspbi","comgooglemaps","pdfefile","pdfehttp","pdfehttps","boxapp","boxemm","awb","awbs","gropen","radarscope"],this.arcgisFilterOptions={allowCommentTag:!0,safeAttrValue:function(i,o,l,a){return i==="a"&&o==="href"||(i==="img"||i==="source")&&o==="src"?n.sanitizeUrl(l):F.exports.safeAttrValue(i,o,l,a)}},this._entityMap={"&":"&#x38;","<":"&#x3C;",">":"&#x3E;",'"':"&#x22;","'":"&#x27;","/":"&#x2F;"};var s;t&&!r?s=t:t&&r?(s=Object.create(this.arcgisFilterOptions),Object.keys(t).forEach(function(i){i==="whiteList"?s.whiteList=n._extendObjectOfArrays([n.arcgisWhiteList,t.whiteList||{}]):s[i]=t[i]})):(s=Object.create(this.arcgisFilterOptions),s.whiteList=this.arcgisWhiteList),this.xssFilterOptions=s,this._xssFilter=new F.exports.FilterXSS(s)}return e.prototype.sanitize=function(t,r){switch(r===void 0&&(r={}),typeof t){case"number":return isNaN(t)||!isFinite(t)?null:t;case"boolean":return t;case"string":return this._xssFilter.process(t);case"object":return this._iterateOverObject(t,r);default:return r.allowUndefined&&typeof t>"u"?void 0:null}},e.prototype.sanitizeUrl=function(t,r){var n=(r??{}).isProtocolRequired,s=n===void 0?!0:n,i=this._trim(t.substring(0,t.indexOf(":"))),o=t==="/",l=/^#/.test(t),a=i&&this.allowedProtocols.indexOf(i.toLowerCase())>-1;return o||l||a?F.exports.escapeAttrValue(t):!i&&!s?F.exports.escapeAttrValue("https://".concat(t)):""},e.prototype.sanitizeHTMLAttribute=function(t,r,n,s){return typeof this.xssFilterOptions.safeAttrValue=="function"?this.xssFilterOptions.safeAttrValue(t,r,n,s):F.exports.safeAttrValue(t,r,n,s)},e.prototype.validate=function(t,r){r===void 0&&(r={});var n=this.sanitize(t,r);return{isValid:t===n,sanitized:n}},e.prototype.encodeHTML=function(t){var r=this;return String(t).replace(/[&<>"'\/]/g,function(n){return r._entityMap[n]})},e.prototype.encodeAttrValue=function(t){var r=/^[a-zA-Z0-9]$/;return String(t).replace(/[\x00-\xFF]/g,function(n,s){return r.test(n)?n:"&#x".concat(Number(t.charCodeAt(s)).toString(16),";")})},e.prototype._extendObjectOfArrays=function(t){var r={};return t.forEach(function(n){Object.keys(n).forEach(function(s){Array.isArray(n[s])&&Array.isArray(r[s])?r[s]=r[s].concat(n[s]):r[s]=n[s]})}),r},e.prototype._iterateOverObject=function(t,r){var n=this;r===void 0&&(r={});try{var s=!1,i=void 0;if(Array.isArray(t))i=t.reduce(function(l,a){var c=n.validate(a,r);return c.isValid?l.concat([a]):(s=!0,l.concat([c.sanitized]))},[]);else if(fs(t)){var o=Object.keys(t);i=o.reduce(function(l,a){var c=t[a],f=n.validate(c,r);return f.isValid?l[a]=c:(s=!0,l[a]=f.sanitized),l},{})}else return r.allowUndefined&&typeof t>"u"?void 0:null;return s?i:t}catch{return null}},e.prototype._trim=function(t){return String.prototype.trim?t.trim():t.replace(/(^\s*)|(\s*$)/g,"")},e}();const Be=new Map;function Cr(){Be.clear()}function ni(e){return Be.get(e)}function si(e,t){Be.set(e,t)}function We(e){Be.delete(e)}var M,Z,ii=function(e){if("WebkitTransition"in e.style)M="webkitTransitionEnd",Z="webkitAnimationEnd";else{if(!("transition"in e.style))throw new Error("Your browser is not supported!");M="transitionend",Z="animationend"}},Nr=function(e){M||ii(e)},oi=function(e,t){return t===void 0&&(t=e+"-active"),function(r){Nr(r);var n=!1,s=function(i){n||(n=!0,r.removeEventListener(M,s),r.removeEventListener(Z,s),r.classList.remove(e),r.classList.remove(t))};r.classList.add(e),r.addEventListener(M,s),r.addEventListener(Z,s),requestAnimationFrame(function(){r.classList.add(t)})}},ai=function(e,t){return t===void 0&&(t=e+"-active"),function(r,n){Nr(r);var s=!1,i=function(o){s||(s=!0,r.removeEventListener(M,i),r.removeEventListener(Z,i),n())};r.classList.add(e),r.addEventListener(M,i),r.addEventListener(Z,i),requestAnimationFrame(function(){r.classList.add(t)})}};const li=st.getLogger("esri.widgets.support.widgetUtils");function ci(e){const t=bt.acquire();for(let n=0;n<arguments.length;n++){const s=arguments[n],i=typeof s;if(i==="string")t.push(s);else if(Array.isArray(s))t.push.apply(t,s);else if(i==="object")for(const o in s)s[o]&&t.push(o)}const r=t.join(" ");return bt.release(t),r}(()=>{const e=new Map,t=new ResizeObserver(r=>{var n;Cr();for(const s of r)(n=e.get(s.target))==null||n(s)});return(r,n,s)=>(e.has(r)&&li.error("Already observing element",r),e.set(r,n),t.observe(r,s),nt(()=>{t.unobserve(r),e.delete(r)}))})();function ui(e){const t=e==null?void 0:e.closest("[dir]");return t!==null&&t instanceof HTMLElement&&t.dir==="rtl"||document.dir==="rtl"}function wa(e){return ui(e)?"rtl":"ltr"}function Sa(e){const t="data-node-ref";this[e.getAttribute(t)]=null}function Ea(e){const t="data-node-ref";this[e.getAttribute(t)]=e}function Aa(e,t){return(e==="enter"?oi:ai)(t)}const fi=["dd","dl","dt","h1","h2","h3","h4","h5","h6","sub","sup","animate","animatetransform","circle","clippath","defs","ellipse","g","image","line","lineargradient","marker","mask","path","pattern","polygon","polyline","radialgradient","rect","stop","svg","switch","symbol","text","textpath","tspan","use"],di=fi.reduce((e,t)=>(e[t]=[],e),{}),hi=["align","alink","alt","bgcolor","border","cellpadding","cellspacing","class","color","cols","colspan","coords","d","dir","face","height","hspace","ismap","lang","marginheight","marginwidth","multiple","nohref","noresize","noshade","nowrap","ref","rel","rev","rows","rowspan","scrolling","shape","span","summary","tabindex","title","usemap","valign","value","vlink","vspace","width"],Pr=new ri({whiteList:di,onTagAttr:(e,t,r)=>{const n=`${t}="${r}"`;if(hi.includes(t))return n},stripIgnoreTag:!0,stripIgnoreTagBody:["script","style"]},!0);function Ta(e,t){const r=e.getBoundingClientRect(),n=t.getBoundingClientRect(),s=r.top+r.height,i=n.top+n.height,o=r.top,l=n.top;(s>i||o<l)&&e.scrollIntoView({block:"end"})}function _a(e){return e==="Enter"||e===" "}const Or="http://www.w3.org/",je=`${Or}2000/svg`,Ir=`${Or}1999/xlink`;let Pt=[],ft=(e,t)=>{let r={};return Object.keys(e).forEach(n=>{r[n]=e[n]}),t&&Object.keys(t).forEach(n=>{r[n]=t[n]}),r},dt=(e,t)=>e.vnodeSelector===t.vnodeSelector&&(e.properties&&t.properties?e.properties.key===t.properties.key&&e.properties.bind===t.properties.bind:!e.properties&&!t.properties),Dr=e=>{if(typeof e!="string")throw new Error("Style values must be strings")},pi=(e,t,r)=>{if(t.vnodeSelector!==""){for(let n=r;n<e.length;n++)if(dt(e[n],t))return n}return-1},qe=(e,t,r,n)=>{let s=e[t];if(s.vnodeSelector==="")return;let i=s.properties;if(!(i&&(i.key===void 0?i.bind:i.key))){for(let o=0;o<e.length;o++)if(o!==t){let l=e[o];if(dt(l,s))throw new Error(`${r.vnodeSelector} had a ${s.vnodeSelector} child ${n==="added"?n:"removed"}, but there is now more than one. You must add unique key properties to make them distinguishable.`)}}},gi=e=>{if(e.properties){let t=e.properties.enterAnimation;t&&t(e.domNode,e.properties)}},Ze=[],Ke=!1,Fr=e=>{(e.children||[]).forEach(Fr),e.properties&&e.properties.afterRemoved&&e.properties.afterRemoved.apply(e.properties.bind||e.properties,[e.domNode])},Ot=()=>{Ke=!1,Ze.forEach(Fr),Ze.length=0},It=e=>{Ze.push(e),Ke||(Ke=!0,typeof window<"u"&&"requestIdleCallback"in window?window.requestIdleCallback(Ot,{timeout:16}):setTimeout(Ot,16))},Dt=e=>{let t=e.domNode;if(e.properties){let r=e.properties.exitAnimation;if(r)return t.style.pointerEvents="none",void r(t,()=>{t.parentNode&&(t.parentNode.removeChild(t),It(e))},e.properties)}t.parentNode&&(t.parentNode.removeChild(t),It(e))},mi=(e,t,r)=>{if(!t)return;let n=r.eventHandlerInterceptor,s=Object.keys(t),i=s.length;for(let o=0;o<i;o++){let l=s[o],a=t[l];if(l==="className")throw new Error('Property "className" is not supported, use "class".');if(l==="class")Ve(e,a,!0);else if(l==="classes"){let c=Object.keys(a),f=c.length;for(let u=0;u<f;u++){let d=c[u];a[d]&&e.classList.add(d)}}else if(l==="styles"){let c=Object.keys(a),f=c.length;for(let u=0;u<f;u++){let d=c[u],h=a[d];h&&(Dr(h),r.styleApplyer(e,d,h))}}else if(l!=="key"&&a!=null){let c=typeof a;c==="function"?(l.lastIndexOf("on",0)===0&&(n&&(a=n(l,a,e,t)),l==="oninput"&&function(){let f=a;a=function(u){f.apply(this,[u]),u.target["oninput-value"]=u.target.value}}()),e[l]=a):r.namespace===je?l==="href"?e.setAttributeNS(Ir,l,a):e.setAttribute(l,a):c==="string"&&l!=="value"?l==="innerHTML"?e[l]=Pr.sanitize(a):Mr(e)&&l in e?e[l]=a:e.setAttribute(l,a):e[l]=a}}};function Mr(e){if(!(e instanceof Element&&e.tagName.includes("-")))return!1;const t=window.customElements.get(e.tagName.toLowerCase());return!!t&&e instanceof t}let Re,$i=(e,t,r)=>{if(t)for(let n of t)X(n,e,void 0,r)},Ur=(e,t,r)=>{$i(e,t.children,r),t.text&&(e.textContent=t.text),mi(e,t.properties,r),t.properties&&t.properties.afterCreate&&t.properties.afterCreate.apply(t.properties.bind||t.properties,[e,r,t.vnodeSelector,t.properties,t.children])},X=(e,t,r,n)=>{let s,i=0,o=e.vnodeSelector,l=t.ownerDocument;if(o==="")s=e.domNode=l.createTextNode(e.text),r!==void 0?t.insertBefore(s,r):t.appendChild(s);else{for(let a=0;a<=o.length;++a){let c=o.charAt(a);if(a===o.length||c==="."||c==="#"){let f=o.charAt(i-1),u=o.slice(i,a);f==="."?s.classList.add(u):f==="#"?s.id=u:(u==="svg"&&(n=ft(n,{namespace:je})),n.namespace!==void 0?s=e.domNode=l.createElementNS(n.namespace,u):(s=e.domNode=e.domNode||l.createElement(u),u==="input"&&e.properties&&e.properties.type!==void 0&&s.setAttribute("type",e.properties.type)),r!==void 0?t.insertBefore(s,r):s.parentNode!==t&&t.appendChild(s)),i=a+1}}Ur(s,e,n)}},Ve=(e,t,r)=>{t&&t.split(" ").forEach(n=>{n&&e.classList.toggle(n,r)})},vi=(e,t,r,n)=>{if(!r)return;let s=!1,i=Object.keys(r),o=i.length;for(let l=0;l<o;l++){let a=i[l],c=r[a],f=t[a];if(a==="class")f!==c&&(Ve(e,f,!1),Ve(e,c,!0));else if(a==="classes"){let u=e.classList,d=Object.keys(c),h=d.length;for(let p=0;p<h;p++){let g=d[p],$=!!c[g];$!==!!f[g]&&(s=!0,$?u.add(g):u.remove(g))}}else if(a==="styles"){let u=Object.keys(c),d=u.length;for(let h=0;h<d;h++){let p=u[h],g=c[p];g!==f[p]&&(s=!0,g?(Dr(g),n.styleApplyer(e,p,g)):n.styleApplyer(e,p,""))}}else if(c||typeof f!="string"||(c=""),a==="value"){let u=e[a];u!==c&&(e["oninput-value"]?u===e["oninput-value"]:c!==f)&&(e[a]=c,e["oninput-value"]=void 0),c!==f&&(s=!0)}else if(c!==f){let u=typeof c;u==="function"&&n.eventHandlerInterceptor||(n.namespace===je?a==="href"?e.setAttributeNS(Ir,a,c):e.setAttribute(a,c):u==="string"?a==="innerHTML"?e[a]=Pr.sanitize(c):a==="role"&&c===""?e.removeAttribute(a):Mr(e)&&a in e?e[a]=c:e.setAttribute(a,c):e[a]!==c&&(e[a]=c),s=!0)}}return s},yi=(e,t,r,n,s)=>{if(r===n)return!1;n=n||Pt;let i,o=(r=r||Pt).length,l=n.length,a=0,c=0,f=!1;for(;c<l;){let u=a<o?r[a]:void 0,d=n[c];if(u!==void 0&&dt(u,d))f=Re(u,d,s)||f,a++;else{let h=pi(r,d,a+1);if(h>=0){for(i=a;i<h;i++)Dt(r[i]),qe(r,i,e,"removed");f=Re(r[h],d,s)||f,a=h+1}else X(d,t,a<o?r[a].domNode:void 0,s),gi(d),qe(n,c,e,"added")}c++}if(o>a)for(i=a;i<o;i++)Dt(r[i]),qe(r,i,e,"removed");return f};Re=(e,t,r)=>{let n=e.domNode,s=!1;if(e===t)return!1;let i=!1;if(t.vnodeSelector===""){if(t.text!==e.text){let o=n.ownerDocument.createTextNode(t.text);return n.parentNode.replaceChild(o,n),t.domNode=o,s=!0,s}t.domNode=n}else t.vnodeSelector.lastIndexOf("svg",0)===0&&(r=ft(r,{namespace:je})),e.text!==t.text&&(i=!0,t.text===void 0?n.removeChild(n.firstChild):n.textContent=t.text),t.domNode=n,i=yi(t,n,e.children,t.children,r)||i,i=vi(n,e.properties,t.properties,r)||i,t.properties&&t.properties.afterUpdate&&t.properties.afterUpdate.apply(t.properties.bind||t.properties,[n,r,t.vnodeSelector,t.properties,t.children]);return i&&t.properties&&t.properties.updateAnimation&&t.properties.updateAnimation(n,t.properties,e.properties),s};let se=(e,t)=>({getLastRender:()=>e,update:r=>{if(e.vnodeSelector!==r.vnodeSelector)throw new Error("The selector for the root VNode may not be changed. (consider using dom.merge and add one extra level to the virtual DOM)");let n=e;e=r,Re(n,r,t)},domNode:e.domNode});const bi={namespace:void 0,performanceLogger:()=>{},eventHandlerInterceptor:void 0,styleApplyer:(e,t,r)=>{t.charAt(0)==="-"?e.style.setProperty(t,r):e.style[t]=r}};let z=e=>ft(bi,e),I={create:(e,t)=>(t=z(t),X(e,document.createElement("div"),void 0,t),se(e,t)),append:(e,t,r)=>(r=z(r),X(t,e,void 0,r),se(t,r)),insertBefore:(e,t,r)=>(r=z(r),X(t,e.parentNode,e,r),se(t,r)),merge:(e,t,r)=>(r=z(r),t.domNode=e,Ur(e,t,r),se(t,r)),replace:(e,t,r)=>(r=z(r),X(t,e.parentNode,e,r),e.parentNode.removeChild(e),se(t,r))},Br,wi=(e,t)=>{let r=[];for(;e&&e!==t;)r.push(e),e=e.parentNode;return r};Br=Array.prototype.find?(e,t)=>e.find(t):(e,t)=>e.filter(t)[0];let Si=(e,t)=>{let r=e;return t.forEach(n=>{r=r&&r.children?Br(r.children,s=>s.domNode===n):void 0}),r},Ei=(e,t,r)=>{let n=function(s){r("domEvent",s);let i=t(),o=wi(s.currentTarget,i.domNode);o.reverse();let l,a=Si(i.getLastRender(),o);return e.scheduleRender(),a&&(l=a.properties[`on${s.type}`].apply(a.properties.bind||this,arguments)),r("domEventProcessed",s),l};return(s,i,o,l)=>n},ka=e=>{let t,r,n=z(e),s=n.performanceLogger,i=!0,o=!1,l=[],a=[],c=(u,d,h)=>{let p,g=()=>p;n.eventHandlerInterceptor=Ei(t,g,s),p=u(d,h(),n),l.push(p),a.push(h)},f=()=>{if(r=void 0,i){i=!1,s("renderStart",void 0);for(let u=0;u<l.length;u++){let d=a[u]();s("rendered",void 0),l[u].update(d),s("patched",void 0)}s("renderDone",void 0),i=!0}};return t={renderNow:f,scheduleRender:()=>{r||o||(r=requestAnimationFrame(f))},stop:()=>{r&&(cancelAnimationFrame(r),r=void 0),o=!0},resume:()=>{o=!1,i=!0,t.scheduleRender()},append:(u,d)=>{c(I.append,u,d)},insertBefore:(u,d)=>{c(I.insertBefore,u,d)},merge:(u,d)=>{c(I.merge,u,d)},replace:(u,d)=>{c(I.replace,u,d)},detach:u=>{for(let d=0;d<a.length;d++)if(a[d]===u)return a.splice(d,1),l.splice(d,1)[0];throw new Error("renderFunction was not found")}},t};const Ai={handleInterceptedEvent:(e,t,r,n)=>(e.scheduleRender(),t.properties[`on${n.type}`].apply(t.properties.bind||r,[n]))},Ti={namespace:void 0,performanceLogger:()=>{},eventHandlerInterceptor:void 0,styleApplyer:(e,t,r)=>{e.style[t]=r}},_i=e=>({...Ti,...e}),ki=(e,t)=>{const r=[];for(;e&&e!==t;)r.push(e),e=e.parentNode;return r},Li=(e,t)=>e.find(t),Ft=(e,t,r=!1)=>{let n=e;return t.forEach((s,i)=>{const o=n!=null&&n.children?Li(n.children,l=>l.domNode===s):void 0;r&&!o&&i!==t.length-1||(n=o)}),n},xi=e=>{let t;const r={...Ai,...e},n=_i(r),s=n.performanceLogger;let i,o=!0,l=!1;const a=[],c=[],f=(d,h,p)=>{var $e;let g;n.eventHandlerInterceptor=(K,H,ze,vt)=>function(L){let A;s("domEvent",L);const x=ki(L.currentTarget,g.domNode),V=x.some(D=>{var ve;return customElements.get((ve=D==null?void 0:D.tagName)==null?void 0:ve.toLowerCase())});if(L.eventPhase===Event.CAPTURING_PHASE||!V)x.reverse(),A=Ft(g.getLastRender(),x);else{const D=L.composedPath(),ve=D.slice(D.indexOf(L.currentTarget),D.indexOf(g.domNode)).filter(yt=>yt.getRootNode()===yt.ownerDocument).reverse();A=Ft(g.getLastRender(),ve,!0)}let N;return A&&(N=r.handleInterceptedEvent(t,A,this,L)),s("domEventProcessed",L),N},($e=r.postProcessProjectionOptions)==null||$e.call(r,n);const $=p();g=d(h,$,n),a.push(g),c.push(p),r.afterFirstVNodeRendered&&r.afterFirstVNodeRendered(g,$)};let u=()=>{if(i=void 0,o){o=!1,s("renderStart",void 0);for(let d=0;d<a.length;d++){const h=c[d]();s("rendered",void 0),a[d].update(h),s("patched",void 0)}s("renderDone",void 0),o=!0}};return r.modifyDoRenderImplementation&&(u=r.modifyDoRenderImplementation(u,a,c)),t={renderNow:u,scheduleRender:()=>{i||l||(i=requestAnimationFrame(u))},stop:()=>{i&&(cancelAnimationFrame(i),i=void 0),l=!0},resume:()=>{l=!1,o=!0,t.scheduleRender()},append:(d,h)=>{f(I.append,d,h)},insertBefore:(d,h)=>{f(I.insertBefore,d,h)},merge:(d,h)=>{f(I.merge,d,h)},replace:(d,h)=>{f(I.replace,d,h)},detach:d=>{for(let h=0;h<c.length;h++)if(c[h]===d)return c.splice(h,1),a.splice(h,1)[0];throw new Error("renderFunction was not found")}},t},W={allRenderFn:!1,cmpDidLoad:!0,cmpDidUnload:!1,cmpDidUpdate:!0,cmpDidRender:!0,cmpWillLoad:!0,cmpWillUpdate:!0,cmpWillRender:!0,connectedCallback:!0,disconnectedCallback:!0,element:!0,event:!0,hasRenderFn:!0,lifecycle:!0,hostListener:!0,hostListenerTargetWindow:!0,hostListenerTargetDocument:!0,hostListenerTargetBody:!0,hostListenerTargetParent:!1,hostListenerTarget:!0,member:!0,method:!0,mode:!0,observeAttribute:!0,prop:!0,propMutable:!0,reflect:!0,scoped:!0,shadowDom:!0,slot:!0,cssAnnotations:!0,state:!0,style:!0,svg:!0,updatable:!0,vdomAttribute:!0,vdomXlink:!0,vdomClass:!0,vdomFunctional:!0,vdomKey:!0,vdomListener:!0,vdomRef:!0,vdomPropOrAttr:!0,vdomRender:!0,vdomStyle:!0,vdomText:!0,watchCallback:!0,taskQueue:!0,hotModuleReplacement:!1,isDebug:!1,isDev:!1,isTesting:!1,hydrateServerSide:!1,hydrateClientSide:!1,lifecycleDOMEvents:!1,lazyLoad:!1,profile:!1,slotRelocation:!0,appendChildSlotFix:!1,cloneNodeFix:!1,hydratedAttribute:!1,hydratedClass:!0,safari10:!1,scriptDataOpts:!1,scopedSlotTextContentFix:!1,shadowDomShim:!1,slotChildNodesFix:!1,invisiblePrehydration:!0,propBoolean:!0,propNumber:!0,propString:!0,cssVarShim:!1,constructableCSS:!0,cmpShouldUpdate:!0,devTools:!1,dynamicImportShim:!1,shadowDelegatesFocus:!0,initializeNextTick:!1,asyncLoading:!1,asyncQueue:!1,transformTagName:!1,attachStyles:!0};let q,jr,He,Hr=!1,Ce=!1,ht=!1,S=!1,Mt=null,et=!1;const La=e=>{const t=new URL(e,b.$resourcesUrl$);return t.origin!==me.location.origin?t.href:t.pathname},Ri=e=>b.$resourcesUrl$=e,U=(e,t="")=>()=>{},Ut="http://www.w3.org/1999/xlink",Bt={},Ci="http://www.w3.org/2000/svg",Ni="http://www.w3.org/1999/xhtml",Pi=e=>e!=null,pt=e=>(e=typeof e,e==="object"||e==="function"),zr=(e,t,...r)=>{let n=null,s=null,i=null,o=!1,l=!1;const a=[],c=u=>{for(let d=0;d<u.length;d++)n=u[d],Array.isArray(n)?c(n):n!=null&&typeof n!="boolean"&&((o=typeof e!="function"&&!pt(n))&&(n=String(n)),o&&l?a[a.length-1].$text$+=n:a.push(o?Ne(null,n):n),l=o)};if(c(r),t){t.key&&(s=t.key),t.name&&(i=t.name);{const u=t.className||t.class;u&&(t.class=typeof u!="object"?u:Object.keys(u).filter(d=>u[d]).join(" "))}}if(typeof e=="function")return e(t===null?{}:t,a,Di);const f=Ne(e,null);return f.$attrs$=t,a.length>0&&(f.$children$=a),f.$key$=s,f.$name$=i,f},Ne=(e,t)=>{const r={$flags$:0,$tag$:e,$text$:t,$elm$:null,$children$:null};return r.$attrs$=null,r.$key$=null,r.$name$=null,r},Oi={},Ii=e=>e&&e.$tag$===Oi,Di={forEach:(e,t)=>e.map(jt).forEach(t),map:(e,t)=>e.map(jt).map(t).map(Fi)},jt=e=>({vattrs:e.$attrs$,vchildren:e.$children$,vkey:e.$key$,vname:e.$name$,vtag:e.$tag$,vtext:e.$text$}),Fi=e=>{if(typeof e.vtag=="function"){const r=Object.assign({},e.vattrs);return e.vkey&&(r.key=e.vkey),e.vname&&(r.name=e.vname),zr(e.vtag,r,...e.vchildren||[])}const t=Ne(e.vtag,e.vtext);return t.$attrs$=e.vattrs,t.$children$=e.vchildren,t.$key$=e.vkey,t.$name$=e.vname,t},Mi=e=>fo.map(t=>t(e)).find(t=>!!t),Ui=(e,t)=>e!=null&&!pt(e)?t&4?e==="false"?!1:e===""||!!e:t&2?parseFloat(e):t&1?String(e):e:e,Bi=e=>e,xa=(e,t,r)=>{const n=Bi(e);return{emit:s=>ji(n,t,{bubbles:!!(r&4),composed:!!(r&2),cancelable:!!(r&1),detail:s})}},ji=(e,t,r)=>{const n=b.ce(t,r);return e.dispatchEvent(n),n},Ht=new WeakMap,Hi=(e,t,r)=>{let n=Oe.get(e);go&&r?(n=n||new CSSStyleSheet,typeof n=="string"?n=t:n.replaceSync(t)):n=t,Oe.set(e,n)},zi=(e,t,r,n)=>{let s=Wr(t,r);const i=Oe.get(s);if(e=e.nodeType===11?e:C,i)if(typeof i=="string"){e=e.head||e;let o=Ht.get(e),l;o||Ht.set(e,o=new Set),o.has(s)||(l=C.createElement("style"),l.innerHTML=i,e.insertBefore(l,e.querySelector("link")),o&&o.add(s))}else e.adoptedStyleSheets.includes(i)||(e.adoptedStyleSheets=[...e.adoptedStyleSheets,i]);return s},Wi=e=>{const t=e.$cmpMeta$,r=e.$hostElement$,n=t.$flags$,s=U("attachStyles",t.$tagName$),i=zi(r.shadowRoot?r.shadowRoot:r.getRootNode(),t,e.$modeName$);n&10&&(r["s-sc"]=i,r.classList.add(i+"-h"),n&2&&r.classList.add(i+"-s")),s()},Wr=(e,t)=>"sc-"+(t&&e.$flags$&32?e.$tagName$+"-"+t:e.$tagName$),zt=(e,t,r,n,s,i)=>{if(r!==n){let o=Xt(e,t),l=t.toLowerCase();if(t==="class"){const a=e.classList,c=Wt(r),f=Wt(n);a.remove(...c.filter(u=>u&&!f.includes(u))),a.add(...f.filter(u=>u&&!c.includes(u)))}else if(t==="style"){for(const a in r)(!n||n[a]==null)&&(a.includes("-")?e.style.removeProperty(a):e.style[a]="");for(const a in n)(!r||n[a]!==r[a])&&(a.includes("-")?e.style.setProperty(a,n[a]):e.style[a]=n[a])}else if(t!=="key")if(t==="ref")n&&n(e);else if(!e.__lookupSetter__(t)&&t[0]==="o"&&t[1]==="n")t[2]==="-"?t=t.slice(3):Xt(me,l)?t=l.slice(2):t=l[2]+t.slice(3),r&&b.rel(e,t,r,!1),n&&b.ael(e,t,n,!1);else{const a=pt(n);if((o||a&&n!==null)&&!s)try{if(e.tagName.includes("-"))e[t]=n;else{const f=n??"";t==="list"?o=!1:(r==null||e[t]!=f)&&(e[t]=f)}}catch{}let c=!1;l!==(l=l.replace(/^xlink\:?/,""))&&(t=l,c=!0),n==null||n===!1?(n!==!1||e.getAttribute(t)==="")&&(c?e.removeAttributeNS(Ut,t):e.removeAttribute(t)):(!o||i&4||s)&&!a&&(n=n===!0?"":n,c?e.setAttributeNS(Ut,t,n):e.setAttribute(t,n))}}},qi=/\s/,Wt=e=>e?e.split(qi):[],qr=(e,t,r,n)=>{const s=t.$elm$.nodeType===11&&t.$elm$.host?t.$elm$.host:t.$elm$,i=e&&e.$attrs$||Bt,o=t.$attrs$||Bt;for(n in i)n in o||zt(s,n,i[n],void 0,r,t.$flags$);for(n in o)zt(s,n,i[n],o[n],r,t.$flags$)},Pe=(e,t,r,n)=>{const s=t.$children$[r];let i=0,o,l,a;if(Hr||(ht=!0,s.$tag$==="slot"&&(q&&n.classList.add(q+"-s"),s.$flags$|=s.$children$?2:1)),s.$text$!==null)o=s.$elm$=C.createTextNode(s.$text$);else if(s.$flags$&1)o=s.$elm$=C.createTextNode("");else{if(S||(S=s.$tag$==="svg"),o=s.$elm$=C.createElementNS(S?Ci:Ni,s.$flags$&2?"slot-fb":s.$tag$),S&&s.$tag$==="foreignObject"&&(S=!1),qr(null,s,S),Pi(q)&&o["s-si"]!==q&&o.classList.add(o["s-si"]=q),s.$children$)for(i=0;i<s.$children$.length;++i)l=Pe(e,s,i,o),l&&o.appendChild(l);s.$tag$==="svg"?S=!1:o.tagName==="foreignObject"&&(S=!0)}return o["s-hn"]=He,s.$flags$&3&&(o["s-sr"]=!0,o["s-cr"]=jr,o["s-sn"]=s.$name$||"",a=e&&e.$children$&&e.$children$[r],a&&a.$tag$===s.$tag$&&e.$elm$&&de(e.$elm$,!1)),o},de=(e,t)=>{b.$flags$|=1;const r=e.childNodes;for(let n=r.length-1;n>=0;n--){const s=r[n];s["s-hn"]!==He&&s["s-ol"]&&(Yr(s).insertBefore(s,gt(s)),s["s-ol"].remove(),s["s-ol"]=void 0,ht=!0),t&&de(s,t)}b.$flags$&=-2},Gr=(e,t,r,n,s,i)=>{let o=e["s-cr"]&&e["s-cr"].parentNode||e,l;for(o.shadowRoot&&o.tagName===He&&(o=o.shadowRoot);s<=i;++s)n[s]&&(l=Pe(null,r,s,e),l&&(n[s].$elm$=l,o.insertBefore(l,gt(t))))},Xr=(e,t,r,n,s)=>{for(;t<=r;++t)(n=e[t])&&(s=n.$elm$,Zr(n),Ce=!0,s["s-ol"]?s["s-ol"].remove():de(s,!0),s.remove())},Gi=(e,t,r,n)=>{let s=0,i=0,o=0,l=0,a=t.length-1,c=t[0],f=t[a],u=n.length-1,d=n[0],h=n[u],p,g;for(;s<=a&&i<=u;)if(c==null)c=t[++s];else if(f==null)f=t[--a];else if(d==null)d=n[++i];else if(h==null)h=n[--u];else if(Se(c,d))G(c,d),c=t[++s],d=n[++i];else if(Se(f,h))G(f,h),f=t[--a],h=n[--u];else if(Se(c,h))(c.$tag$==="slot"||h.$tag$==="slot")&&de(c.$elm$.parentNode,!1),G(c,h),e.insertBefore(c.$elm$,f.$elm$.nextSibling),c=t[++s],h=n[--u];else if(Se(f,d))(c.$tag$==="slot"||h.$tag$==="slot")&&de(f.$elm$.parentNode,!1),G(f,d),e.insertBefore(f.$elm$,c.$elm$),f=t[--a],d=n[++i];else{for(o=-1,l=s;l<=a;++l)if(t[l]&&t[l].$key$!==null&&t[l].$key$===d.$key$){o=l;break}o>=0?(g=t[o],g.$tag$!==d.$tag$?p=Pe(t&&t[i],r,o,e):(G(g,d),t[o]=void 0,p=g.$elm$),d=n[++i]):(p=Pe(t&&t[i],r,i,e),d=n[++i]),p&&Yr(c.$elm$).insertBefore(p,gt(c.$elm$))}s>a?Gr(e,n[u+1]==null?null:n[u+1].$elm$,r,n,i,u):i>u&&Xr(t,s,a)},Se=(e,t)=>e.$tag$===t.$tag$?e.$tag$==="slot"?e.$name$===t.$name$:e.$key$===t.$key$:!1,gt=e=>e&&e["s-ol"]||e,Yr=e=>(e["s-ol"]?e["s-ol"]:e).parentNode,G=(e,t)=>{const r=t.$elm$=e.$elm$,n=e.$children$,s=t.$children$,i=t.$tag$,o=t.$text$;let l;o===null?(S=i==="svg"?!0:i==="foreignObject"?!1:S,i==="slot"||qr(e,t,S),n!==null&&s!==null?Gi(r,n,t,s):s!==null?(e.$text$!==null&&(r.textContent=""),Gr(r,null,t,s,0,s.length-1)):n!==null&&Xr(n,0,n.length-1),S&&i==="svg"&&(S=!1)):(l=r["s-cr"])?l.parentNode.textContent=o:e.$text$!==o&&(r.data=o)},Qr=e=>{const t=e.childNodes;let r,n,s,i,o,l;for(n=0,s=t.length;n<s;n++)if(r=t[n],r.nodeType===1){if(r["s-sr"]){for(o=r["s-sn"],r.hidden=!1,i=0;i<s;i++)if(l=t[i].nodeType,t[i]["s-hn"]!==r["s-hn"]||o!==""){if(l===1&&o===t[i].getAttribute("slot")){r.hidden=!0;break}}else if(l===1||l===3&&t[i].textContent.trim()!==""){r.hidden=!0;break}}Qr(r)}},k=[],Jr=e=>{let t,r,n,s,i,o,l=0;const a=e.childNodes,c=a.length;for(;l<c;l++){if(t=a[l],t["s-sr"]&&(r=t["s-cr"])&&r.parentNode)for(n=r.parentNode.childNodes,s=t["s-sn"],o=n.length-1;o>=0;o--)r=n[o],!r["s-cn"]&&!r["s-nr"]&&r["s-hn"]!==t["s-hn"]&&(qt(r,s)?(i=k.find(f=>f.$nodeToRelocate$===r),Ce=!0,r["s-sn"]=r["s-sn"]||s,i?i.$slotRefNode$=t:k.push({$slotRefNode$:t,$nodeToRelocate$:r}),r["s-sr"]&&k.map(f=>{qt(f.$nodeToRelocate$,r["s-sn"])&&(i=k.find(u=>u.$nodeToRelocate$===r),i&&!f.$slotRefNode$&&(f.$slotRefNode$=i.$slotRefNode$))})):k.some(f=>f.$nodeToRelocate$===r)||k.push({$nodeToRelocate$:r}));t.nodeType===1&&Jr(t)}},qt=(e,t)=>e.nodeType===1?e.getAttribute("slot")===null&&t===""||e.getAttribute("slot")===t:e["s-sn"]===t?!0:t==="",Zr=e=>{e.$attrs$&&e.$attrs$.ref&&e.$attrs$.ref(null),e.$children$&&e.$children$.map(Zr)},Xi=(e,t)=>{const r=e.$hostElement$,n=e.$cmpMeta$,s=e.$vnode$||Ne(null,null),i=Ii(t)?t:zr(null,null,t);He=r.tagName,n.$attrsToReflect$&&(i.$attrs$=i.$attrs$||{},n.$attrsToReflect$.map(([o,l])=>i.$attrs$[l]=r[o])),i.$tag$=null,i.$flags$|=4,e.$vnode$=i,i.$elm$=s.$elm$=r.shadowRoot||r,q=r["s-sc"],jr=r["s-cr"],Hr=(n.$flags$&1)!==0,Ce=!1,G(s,i);{if(b.$flags$|=1,ht){Jr(i.$elm$);let o,l,a,c,f,u,d=0;for(;d<k.length;d++)o=k[d],l=o.$nodeToRelocate$,l["s-ol"]||(a=C.createTextNode(""),a["s-nr"]=l,l.parentNode.insertBefore(l["s-ol"]=a,l));for(d=0;d<k.length;d++)if(o=k[d],l=o.$nodeToRelocate$,o.$slotRefNode$){for(c=o.$slotRefNode$.parentNode,f=o.$slotRefNode$.nextSibling,a=l["s-ol"];a=a.previousSibling;)if(u=a["s-nr"],u&&u["s-sn"]===l["s-sn"]&&c===u.parentNode&&(u=u.nextSibling,!u||!u["s-nr"])){f=u;break}(!f&&c!==l.parentNode||l.nextSibling!==f)&&l!==f&&(!l["s-hn"]&&l["s-ol"]&&(l["s-hn"]=l["s-ol"].parentNode.nodeName),c.insertBefore(l,f))}else l.nodeType===1&&(l.hidden=!0)}Ce&&Qr(i.$elm$),b.$flags$&=-2,k.length=0}},Yi=(e,t)=>{},mt=(e,t)=>(e.$flags$|=16,Yi(e,e.$ancestorComponent$),vo(()=>Qi(e,t))),Qi=(e,t)=>{const r=e.$hostElement$,n=U("scheduleUpdate",e.$cmpMeta$.$tagName$),s=r;let i;return t?i=Q(s,"componentWillLoad"):i=Q(s,"componentWillUpdate"),i=Gt(i,()=>Q(s,"componentWillRender")),n(),Gt(i,()=>Ji(e,s,t))},Ji=async(e,t,r)=>{const n=e.$hostElement$,s=U("update",e.$cmpMeta$.$tagName$);n["s-rc"],r&&Wi(e);const i=U("render",e.$cmpMeta$.$tagName$);Zi(e,t,n),i(),s(),Ki(e)},Zi=(e,t,r)=>{try{Mt=t,t=t.render&&t.render(),e.$flags$&=-17,e.$flags$|=2,(W.hasRenderFn||W.reflect)&&(W.vdomRender||W.reflect)&&(W.hydrateServerSide||Xi(e,t))}catch(l){ge(l,e.$hostElement$)}return Mt=null,null},Ki=e=>{const t=e.$cmpMeta$.$tagName$,r=e.$hostElement$,n=U("postUpdate",t),s=r;e.$ancestorComponent$,Q(s,"componentDidRender"),e.$flags$&64?(Q(s,"componentDidUpdate"),n()):(e.$flags$|=64,Q(s,"componentDidLoad"),n())},Ra=e=>{{const t=pe(e),r=t.$hostElement$.isConnected;return r&&(t.$flags$&18)===2&&mt(t,!1),r}},Q=(e,t,r)=>{if(e&&e[t])try{return e[t](r)}catch(n){ge(n)}},Gt=(e,t)=>e&&e.then?e.then(t):t(),Vi=(e,t)=>pe(e).$instanceValues$.get(t),eo=(e,t,r,n)=>{const s=pe(e),i=e,o=s.$instanceValues$.get(t),l=s.$flags$,a=i;r=Ui(r,n.$members$[t][0]);const c=Number.isNaN(o)&&Number.isNaN(r);if(r!==o&&!c){s.$instanceValues$.set(t,r);{if(n.$watchers$&&l&128){const u=n.$watchers$[t];u&&u.map(d=>{try{a[d](r,o,t)}catch(h){ge(h,i)}})}if((l&18)===2){if(a.componentShouldUpdate&&a.componentShouldUpdate(r,o,t)===!1)return;mt(s,!1)}}}},to=(e,t,r)=>{if(t.$members$){e.watchers&&(t.$watchers$=e.watchers);const n=Object.entries(t.$members$),s=e.prototype;n.map(([i,[o]])=>{(o&31||o&32)&&Object.defineProperty(s,i,{get(){return Vi(this,i)},set(l){eo(this,i,l,t)},configurable:!0,enumerable:!0})});{const i=new Map;s.attributeChangedCallback=function(o,l,a){b.jmp(()=>{const c=i.get(o);if(this.hasOwnProperty(c))a=this[c],delete this[c];else if(s.hasOwnProperty(c)&&typeof this[c]=="number"&&this[c]==a)return;this[c]=a===null&&typeof this[c]=="boolean"?!1:a})},e.observedAttributes=n.filter(([o,l])=>l[0]&15).map(([o,l])=>{const a=l[1]||o;return i.set(a,o),l[0]&512&&t.$attrsToReflect$.push([o,a]),a})}}return e},ro=async(e,t,r,n,s)=>{if(!(t.$flags$&32)&&(s=e.constructor,t.$flags$|=32,customElements.whenDefined(r.$tagName$).then(()=>t.$flags$|=128),s.style)){let o=s.style;typeof o!="string"&&(o=o[t.$modeName$=Mi(e)]);const l=Wr(r,t.$modeName$);if(!Oe.has(l)){const a=U("registerStyles",r.$tagName$);Hi(l,o,!!(r.$flags$&1)),a()}}t.$ancestorComponent$,mt(t,!0)},no=e=>{},so=e=>{if(!(b.$flags$&1)){const t=pe(e),r=t.$cmpMeta$,n=U("connectedCallback",r.$tagName$);t.$flags$&1?(Kr(e,t,r.$listeners$),no(t.$lazyInstance$)):(t.$flags$|=1,r.$flags$&12&&io(e),r.$members$&&Object.entries(r.$members$).map(([s,[i]])=>{if(i&31&&e.hasOwnProperty(s)){const o=e[s];delete e[s],e[s]=o}}),ro(e,t,r)),n()}},io=e=>{const t=e["s-cr"]=C.createComment("");t["s-cn"]=!0,e.insertBefore(t,e.firstChild)},oo=e=>{if(!(b.$flags$&1)){const t=pe(e);t.$rmListeners$&&(t.$rmListeners$.map(r=>r()),t.$rmListeners$=void 0)}},Ca=(e,t)=>{const r={$flags$:t[0],$tagName$:t[1]};r.$members$=t[2],r.$listeners$=t[3],r.$watchers$=e.$watchers$,r.$attrsToReflect$=[];const n=e.prototype.connectedCallback,s=e.prototype.disconnectedCallback;return Object.assign(e.prototype,{__registerHost(){uo(this,r)},connectedCallback(){so(this),n&&n.call(this)},disconnectedCallback(){oo(this),s&&s.call(this)},__attachShadow(){this.attachShadow({mode:"open",delegatesFocus:!!(r.$flags$&16)})}}),e.is=r.$tagName$,to(e,r)},Na=(e,t)=>t,Kr=(e,t,r,n)=>{r&&r.map(([s,i,o])=>{const l=lo(e,s),a=ao(t,o),c=co(s);b.ael(l,i,a,c),(t.$rmListeners$=t.$rmListeners$||[]).push(()=>b.rel(l,i,a,c))})},ao=(e,t)=>r=>{try{W.lazyLoad||e.$hostElement$[t](r)}catch(n){ge(n)}},lo=(e,t)=>t&4?C:t&8?me:t&16?C.body:e,co=e=>ho?{passive:(e&1)!==0,capture:(e&2)!==0}:(e&2)!==0,Vr=new WeakMap,pe=e=>Vr.get(e),uo=(e,t)=>{const r={$flags$:0,$hostElement$:e,$cmpMeta$:t,$instanceValues$:new Map};return Kr(e,r,t.$listeners$),Vr.set(e,r)},Xt=(e,t)=>t in e,ge=(e,t)=>(0,console.error)(e,t),Oe=new Map,fo=[],me=typeof window<"u"?window:{},C=me.document||{head:{}},Pa=me.HTMLElement||class{},b={$flags$:0,$resourcesUrl$:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,r,n)=>e.addEventListener(t,r,n),rel:(e,t,r,n)=>e.removeEventListener(t,r,n),ce:(e,t)=>new CustomEvent(e,t)},ho=(()=>{let e=!1;try{C.addEventListener("e",null,Object.defineProperty({},"passive",{get(){e=!0}}))}catch{}return e})(),po=e=>Promise.resolve(e),go=(()=>{try{return new CSSStyleSheet,typeof new CSSStyleSheet().replaceSync=="function"}catch{}return!1})(),Yt=[],en=[],mo=(e,t)=>r=>{e.push(r),et||(et=!0,t&&b.$flags$&4?$o(tt):b.raf(tt))},Qt=e=>{for(let t=0;t<e.length;t++)try{e[t](performance.now())}catch(r){ge(r)}e.length=0},tt=()=>{Qt(Yt),Qt(en),(et=Yt.length>0)&&b.raf(tt)},$o=e=>po().then(e),vo=mo(en,!0);/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const tn="calcite-mode-auto",rn="calcite-mode-dark",yo="calcite-mode-light",Oa={autoMode:tn,darkMode:rn,lightMode:yo,rtl:"calcite--rtl"};/*!
* tabbable 6.0.1
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var nn=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"],Ie=nn.join(","),sn=typeof Element>"u",B=sn?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,De=!sn&&Element.prototype.getRootNode?function(e){return e.getRootNode()}:function(e){return e.ownerDocument},on=function(t,r,n){var s=Array.prototype.slice.apply(t.querySelectorAll(Ie));return r&&B.call(t,Ie)&&s.unshift(t),s=s.filter(n),s},an=function e(t,r,n){for(var s=[],i=Array.from(t);i.length;){var o=i.shift();if(o.tagName==="SLOT"){var l=o.assignedElements(),a=l.length?l:o.children,c=e(a,!0,n);n.flatten?s.push.apply(s,c):s.push({scopeParent:o,candidates:c})}else{var f=B.call(o,Ie);f&&n.filter(o)&&(r||!t.includes(o))&&s.push(o);var u=o.shadowRoot||typeof n.getShadowRoot=="function"&&n.getShadowRoot(o),d=!n.shadowRootFilter||n.shadowRootFilter(o);if(u&&d){var h=e(u===!0?o.children:u.children,!0,n);n.flatten?s.push.apply(s,h):s.push({scopeParent:o,candidates:h})}else i.unshift.apply(i,o.children)}}return s},ln=function(t,r){return t.tabIndex<0&&(r||/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||t.isContentEditable)&&isNaN(parseInt(t.getAttribute("tabindex"),10))?0:t.tabIndex},bo=function(t,r){return t.tabIndex===r.tabIndex?t.documentOrder-r.documentOrder:t.tabIndex-r.tabIndex},cn=function(t){return t.tagName==="INPUT"},wo=function(t){return cn(t)&&t.type==="hidden"},So=function(t){var r=t.tagName==="DETAILS"&&Array.prototype.slice.apply(t.children).some(function(n){return n.tagName==="SUMMARY"});return r},Eo=function(t,r){for(var n=0;n<t.length;n++)if(t[n].checked&&t[n].form===r)return t[n]},Ao=function(t){if(!t.name)return!0;var r=t.form||De(t),n=function(l){return r.querySelectorAll('input[type="radio"][name="'+l+'"]')},s;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")s=n(window.CSS.escape(t.name));else try{s=n(t.name)}catch(o){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",o.message),!1}var i=Eo(s,t.form);return!i||i===t},To=function(t){return cn(t)&&t.type==="radio"},_o=function(t){return To(t)&&!Ao(t)},ko=function(t){for(var r,n=De(t).host,s=!!((r=n)!==null&&r!==void 0&&r.ownerDocument.contains(n)||t.ownerDocument.contains(t));!s&&n;){var i;n=De(n).host,s=!!((i=n)!==null&&i!==void 0&&i.ownerDocument.contains(n))}return s},Jt=function(t){var r=t.getBoundingClientRect(),n=r.width,s=r.height;return n===0&&s===0},Lo=function(t,r){var n=r.displayCheck,s=r.getShadowRoot;if(getComputedStyle(t).visibility==="hidden")return!0;var i=B.call(t,"details>summary:first-of-type"),o=i?t.parentElement:t;if(B.call(o,"details:not([open]) *"))return!0;if(!n||n==="full"||n==="legacy-full"){if(typeof s=="function"){for(var l=t;t;){var a=t.parentElement,c=De(t);if(a&&!a.shadowRoot&&s(a)===!0)return Jt(t);t.assignedSlot?t=t.assignedSlot:!a&&c!==t.ownerDocument?t=c.host:t=a}t=l}if(ko(t))return!t.getClientRects().length;if(n!=="legacy-full")return!0}else if(n==="non-zero-area")return Jt(t);return!1},xo=function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var r=t.parentElement;r;){if(r.tagName==="FIELDSET"&&r.disabled){for(var n=0;n<r.children.length;n++){var s=r.children.item(n);if(s.tagName==="LEGEND")return B.call(r,"fieldset[disabled] *")?!0:!s.contains(t)}return!0}r=r.parentElement}return!1},Fe=function(t,r){return!(r.disabled||wo(r)||Lo(r,t)||So(r)||xo(r))},rt=function(t,r){return!(_o(r)||ln(r)<0||!Fe(t,r))},Ro=function(t){var r=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(r)||r>=0)},Co=function e(t){var r=[],n=[];return t.forEach(function(s,i){var o=!!s.scopeParent,l=o?s.scopeParent:s,a=ln(l,o),c=o?e(s.candidates):l;a===0?o?r.push.apply(r,c):r.push(l):n.push({documentOrder:i,tabIndex:a,item:s,isScope:o,content:c})}),n.sort(bo).reduce(function(s,i){return i.isScope?s.push.apply(s,i.content):s.push(i.content),s},[]).concat(r)},No=function(t,r){r=r||{};var n;return r.getShadowRoot?n=an([t],r.includeContainer,{filter:rt.bind(null,r),flatten:!1,getShadowRoot:r.getShadowRoot,shadowRootFilter:Ro}):n=on(t,r.includeContainer,rt.bind(null,r)),Co(n)},Ia=function(t,r){r=r||{};var n;return r.getShadowRoot?n=an([t],r.includeContainer,{filter:Fe.bind(null,r),flatten:!0,getShadowRoot:r.getShadowRoot}):n=on(t,r.includeContainer,Fe.bind(null,r)),n},Da=function(t,r){if(r=r||{},!t)throw new Error("No node provided");return B.call(t,Ie)===!1?!1:rt(r,t)},Po=nn.concat("iframe").join(","),Fa=function(t,r){if(r=r||{},!t)throw new Error("No node provided");return B.call(t,Po)===!1?!1:Fe(r,t)};const Oo={getShadowRoot:!0};function Ma(e){const t="dir",r=`[${t}]`,n=Io(e,r);return n?n.getAttribute(t):"ltr"}function Ua(e,t,r){const n=`[${t}]`,s=e.closest(n);return s?s.getAttribute(t):r}function un(e){return e.getRootNode()}function fn(e){return e.host||null}function Ba(e,{selector:t,id:r}){function n(s){if(!s)return null;s.assignedSlot&&(s=s.assignedSlot);const i=un(s),o=r?"getElementById"in i?i.getElementById(r):null:t?i.querySelector(t):null,l=fn(i);return o||(l?n(l):null)}return n(e)}function Io(e,t){function r(n){return n?n.closest(t)||r(fn(un(n))):null}return r(e)}function Do(e,t){return dn(e,t)}function dn(e,t){if(!e)return;const r=t(e);if(r!==void 0)return r;const{parentNode:n}=e;return dn(n instanceof ShadowRoot?n.host:n,t)}function ja(e,t){return!!Do(t,r=>r===e?!0:void 0)}function Fo(e){return typeof(e==null?void 0:e.setFocus)=="function"}async function Ha(e){if(e)return Fo(e)?e.setFocus():e.focus()}function za(e){(No(e,Oo)[0]||e).focus()}const he=":not([slot])";function Wa(e,t,r){t&&!Array.isArray(t)&&typeof t!="string"&&(r=t,t=null);const n=t?Array.isArray(t)?t.map(s=>`[slot="${s}"]`).join(","):`[slot="${t}"]`:he;return r!=null&&r.all?Mo(e,n,r):Uo(e,n,r)}function hn(e,t){return e?Array.from(e.children||[]).filter(r=>r==null?void 0:r.matches(t)):[]}function Mo(e,t,r){let n=t===he?hn(e,he):Array.from(e.querySelectorAll(t));n=r&&r.direct===!1?n:n.filter(i=>i.parentElement===e),n=r!=null&&r.matches?n.filter(i=>i==null?void 0:i.matches(r.matches)):n;const s=r==null?void 0:r.selector;return s?n.map(i=>Array.from(i.querySelectorAll(s))).reduce((i,o)=>[...i,...o],[]).filter(i=>!!i):n}function Uo(e,t,r){let n=t===he?hn(e,he)[0]||null:e.querySelector(t);n=r&&r.direct===!1||(n==null?void 0:n.parentElement)===e?n:null,n=r!=null&&r.matches?n!=null&&n.matches(r.matches)?n:null:n;const s=r==null?void 0:r.selector;return s?n==null?void 0:n.querySelector(s):n}function qa(e,t,r){if(typeof t=="string"&&t!=="")return t;if(t==="")return e[r]}function Ga(e){return(!!e).toString()}function Xa(e){return!!Bo(e).length}function Bo(e){return e.target.assignedElements({flatten:!0})}function Ya(e){return!!(e.isPrimary&&e.button===0)}/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */function Zt(){const{classList:e}=document.body,t=window.matchMedia("(prefers-color-scheme: dark)").matches,r=()=>e.contains(rn)||e.contains(tn)&&t?"dark":"light",n=o=>document.body.dispatchEvent(new CustomEvent("calciteModeChange",{bubbles:!0,detail:{mode:o}})),s=o=>{i!==o&&n(o),i=o};let i=r();n(i),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",o=>s(o.matches?"dark":"light")),new MutationObserver(()=>s(r())).observe(document.body,{attributes:!0,attributeFilter:["class"]})}function jo(){typeof window<"u"&&typeof location<"u"&&typeof document<"u"&&window.location===location&&window.document===document&&(document.readyState==="interactive"?Zt():document.addEventListener("DOMContentLoaded",()=>Zt(),{once:!0}))}const Ho=jo;Ho();let pn;function zo(){Ri(Fn(ar(pn)))}pn="components/assets";const gn=Symbol("widget"),Wo=Symbol("widget-test-data"),qo=[],Go={},Me=new WeakMap;function mn(e,t){let r=t.children;if(r&&r.length)for(let s=0;s<r.length;++s)r[s]=mn(e,r[s]);else r=qo;const n=t.vnodeSelector;if($t(n)){const s=t.properties||Go,i=s.key||n;return{vnodeSelector:"div",properties:{key:i,afterCreate:Xo,afterUpdate:Yo,afterRemoved:$n,parentWidget:e,widgetConstructor:n,widgetProperties:{...s,key:i,children:r}},children:void 0,text:void 0,domNode:null}}return t}function Xo(e,t,r,{parentWidget:n,widgetConstructor:s,widgetProperties:i}){var l;const o=new s(i);o.container=e,Me.set(e,o),(l=o.afterCreate)==null||l.call(o,o,e),n._internalHandles.add(nt(()=>$n(e)))}function Yo(e,t,r,{widgetProperties:n}){var i;const s=Me.get(e);s&&(s.set(n),(i=s.afterUpdate)==null||i.call(s,s,e))}function $n(e){var r;const t=Me.get(e);t&&((r=t.afterRemoved)==null||r.call(t,t,e),t.destroy(),Me.delete(e))}function $t(e){return typeof e=="function"&&e[gn]}const Kt=new Set;function Qo(e){Kt.add(e),e.finally(()=>Kt.delete(e))}var vn;const Jo="esri.widgets.Widget";let Zo=0;const Ko={widgetIcon:"esri-icon-checkbox-unchecked"};function yn(e,t){for(const r in t)e[r]!=null&&(typeof e[r]=="object"&&typeof t[r]=="object"?yn(e[r],t==null?void 0:t[r]):e[r]=t[r]);return e}const Vo=xi({postProcessProjectionOptions(e){const t=e.eventHandlerInterceptor,r=/capture$/i;e.eventHandlerInterceptor=(n,s,i,o)=>{const l=t==null?void 0:t(n,s,i,o),a=r.test(n);if(!((n=n.replace(r,"")).toLowerCase()in i)||a){const c=n[2].toLowerCase()+n.slice(3),f=h=>l==null?void 0:l.call(i,h);i.addEventListener(c,f,a);const u=()=>i.removeEventListener(c,f,a),d=o.afterRemoved;o.afterRemoved=h=>{d==null||d(h),u()}}return l}},handleInterceptedEvent(e,t,r,n){const{eventPhase:s,type:i}=n,o=s===Event.CAPTURING_PHASE;let l=`on${i}${o?"capture":""}`;const a=t.properties;(a&&l in a||(l=`on${i[0].toUpperCase()}${i.slice(1)}${o?"Capture":""}`,a&&l in a))&&(Cr(),e.scheduleRender(),a[l].call(a.bind||r,n))}});let Ge=!1,y=class extends dr(Mn.EventedAccessor){constructor(e,t){super(e,t),this._attached=!1,this._internalHandles=new kn,this._projector=Vo,this._readyForTrueRender=!1,this.iconClass=Ko.widgetIcon,this.key=this,this._loadLocale=Ln(async()=>{if(this._messageBundleProps&&this._messageBundleProps.length){const i=await Nn(this._messageBundleProps.map(async({bundlePath:o,propertyName:l})=>{let a=await ts(o);this.uiStrings&&Object.keys(this.uiStrings)&&(a=yn(In(a),this.uiStrings)),this[l]=a}));for(const o of i)o.error&&st.getLogger(this.declaredClass).error("widget-intl:locale-error",this.declaredClass,o.error)}await this.loadLocale()}),zo();const r="esri-widget-uid-"+us(),n=this.render.bind(this);this._trackingTarget=new xn(()=>this.scheduleRender());const s=()=>{var f;if(!this._readyForTrueRender||this.destroyed)return null;if(!this.visible)return{vnodeSelector:"div",properties:{key:r,class:"",styles:{display:"none"}},domNode:null,children:void 0,text:void 0};const i=n();let{properties:o}=i;o||(i.properties=o={});let{key:l,styles:a}=o;l||(o.key=r),a||(o.styles=a={}),a.display||(a.display="");let c=0;return(f=i.children)==null||f.forEach(u=>{if($t(u.vnodeSelector))return;let{properties:d}=u;d||(u.properties=d={}),d.key||(d.key=`${this.id}--${c++}`)}),mn(this,i)};this.render=()=>{if(Ge)return s();let i=ni(this)??null;if(i)return i;this._trackingTarget.clear(),Ge=!0;try{i=Rn(this._trackingTarget,s)}catch(o){throw console.error(o),o}finally{Ge=!1}return i&&si(this,i),i},this.addResolvingPromise(this._resourcesFetch=this.beforeFirstRender().then(()=>{this._readyForTrueRender=!0,this._postInitialize()})),Qo(this._resourcesFetch)}normalizeCtorArgs(e,t){const r={...e};return t&&(r.container=t),r}postInitialize(){}beforeFirstRender(){return Promise.all([this.loadDependencies(),this._loadLocale()]).then(()=>{}).catch(wt)}async loadDependencies(){}async loadLocale(){}destroy(){this.destroyed||(St(this._trackingTarget),St(this.viewModel),this._detach(this.container),this._set("container",null),this._internalHandles.destroy(),this._emitter.clear(),this.render=()=>null,this._projector=null,We(this))}set container(e){this._get("container")||this._set("container",e)}castContainer(e){return ls(e)}get domNode(){return this.container}set domNode(e){this.container=e}get id(){return this._get("id")||this.get("container.id")||Date.now().toString(16)+"-widget-"+Zo++}set id(e){e&&this._set("id",e)}get label(){return this.declaredClass.split(".").pop()}set label(e){this._overrideIfSome("label",e)}get renderable(){return this._resourcesFetch}get visible(){return this._get("visible")}set visible(e){this._set("visible",e)}get[(vn=gn,Wo)](){return{projector:this._projector}}render(){throw new Error("not implemented")}scheduleRender(){this.destroyed||(We(this),this._projector.scheduleRender())}classes(...e){return ci.apply(this,e)}renderNow(){We(this),this._projector.renderNow()}_postInitialize(){var t;if(this.destroyed)return;this.scheduleRender(),(t=this._delegatedEventNames)!=null&&t.length&&this._internalHandles.add(Le(()=>this.viewModel,(r,n)=>{n&&this._internalHandles.remove("delegated-events"),r&&sr(r)&&this._internalHandles.add(this._delegatedEventNames.map(s=>ir(r,s,i=>{this.emit(s,i)})),"delegated-events")},Qn)),this.postInitialize();const e=async()=>{await this._loadLocale().catch(wt),this.scheduleRender()};this._internalHandles.add([Bn(e),Le(()=>this.uiStrings,e),Xn(()=>this.container,r=>{this.destroyed||this._attach(r)},{initial:!0,once:!0})])}_attach(e){e&&(this._projector.merge(e,this.render),this._attached=!0)}_detach(e){var t;this._attached&&(this._projector.detach(this.render),this._attached=!1),(t=e==null?void 0:e.parentNode)==null||t.removeChild(e)}};y[vn]=!0,v([w()],y.prototype,"_readyForTrueRender",void 0),v([w({value:null})],y.prototype,"container",null),v([Cn("container")],y.prototype,"castContainer",null),v([w()],y.prototype,"iconClass",void 0),v([w()],y.prototype,"id",null),v([w()],y.prototype,"label",null),v([w()],y.prototype,"renderable",null),v([w()],y.prototype,"uiStrings",void 0),v([w()],y.prototype,"viewModel",void 0),v([w({value:!0})],y.prototype,"visible",null),v([w()],y.prototype,"key",void 0),v([w()],y.prototype,"children",void 0),v([w()],y.prototype,"afterCreate",void 0),v([w()],y.prototype,"afterUpdate",void 0),v([w()],y.prototype,"afterRemoved",void 0),y=v([ce(Jo)],y);const Qa=y;function Ja(e){return(t,r)=>{t.hasOwnProperty("_messageBundleProps")||(t._messageBundleProps=t._messageBundleProps?t._messageBundleProps.slice():[]),t._messageBundleProps.push({bundlePath:e,propertyName:r})}}var ea=function(e){return{vnodeSelector:"",properties:void 0,children:void 0,text:e.toString(),domNode:null}},bn=function(e,t){for(var r=0,n=e.length;r<n;r++){var s=e[r];Array.isArray(s)?bn(s,t):s!=null&&s!==!1&&(s.hasOwnProperty("vnodeSelector")||(s=ea(s)),t.push(s))}},ta=function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];if(r.length===1&&typeof r[0]=="string")return{vnodeSelector:e,properties:t||void 0,children:void 0,text:r[0],domNode:null};var s=[];return bn(r,s),{vnodeSelector:e,properties:t||void 0,children:s,text:void 0,domNode:null}};function Za(e,t,...r){return typeof e!="function"||$t(e)?ta(e,t,...r):e(t,...r)}function Ka(e){return e&&typeof e.render=="function"}function Va(e){return e&&typeof e.postMixInProperties=="function"&&typeof e.buildRendering=="function"&&typeof e.postCreate=="function"&&typeof e.startup=="function"}export{Ta as $,qa as A,fa as B,Oa as C,Oo as D,No as E,Na as F,Ia as G,Pa as H,Da as I,Fa as J,dr as K,_e as L,Ja as M,Za as N,ga as O,da as P,Ea as Q,Ka as R,oa as S,ci as T,ha as U,ui as V,Qa as W,Va as X,Bn as Y,ts as Z,Et as _,ua as a,_a as a0,ia as a1,ba as a2,wa as a3,ls as a4,Pr as a5,ka as a6,aa as a7,Sa as a8,Aa as a9,ya as aa,$a as ab,va as ac,J as b,Mn as c,Ra as d,Oi as e,Xn as f,Ma as g,zr as h,La as i,Io as j,Qn as k,Le as l,xa as m,us as n,Ua as o,Ca as p,Ba as q,Wa as r,Xa as s,Ga as t,za as u,Ya as v,pa as w,ke as x,ja as y,Ha as z};
