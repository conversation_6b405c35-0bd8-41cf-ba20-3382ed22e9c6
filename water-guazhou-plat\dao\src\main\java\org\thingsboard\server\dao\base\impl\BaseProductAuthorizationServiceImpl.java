package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseProductAuthorizationService;
import org.thingsboard.server.dao.model.sql.base.BaseProductAuthorization;
import org.thingsboard.server.dao.sql.base.BaseProductAuthorizationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseMessageConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.query.base.BaseProductAuthorizationPageRequest;

/**
 * 平台管理-产品授权Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class BaseProductAuthorizationServiceImpl implements IBaseProductAuthorizationService {

    @Autowired
    private BaseProductAuthorizationMapper baseProductAuthorizationMapper;

    /**
     * 查询平台管理-产品授权
     *
     * @param id 平台管理-产品授权主键
     * @return 平台管理-产品授权
     */
    @Override
    public BaseProductAuthorization selectBaseProductAuthorizationById(String id) {
        return baseProductAuthorizationMapper.selectBaseProductAuthorizationById(id);
    }

    /**
     * 查询平台管理-产品授权列表
     *
     * @param baseProductAuthorization 平台管理-产品授权
     * @return 平台管理-产品授权
     */
    @Override
    public IPage<BaseProductAuthorization> selectBaseProductAuthorizationList(BaseProductAuthorizationPageRequest baseProductAuthorization) {
        return baseProductAuthorizationMapper.selectBaseProductAuthorizationList(baseProductAuthorization);
    }

    /**
     * 新增平台管理-产品授权
     *
     * @param baseProductAuthorization 平台管理-产品授权
     * @return 结果
     */
    @Override
    public int insertBaseProductAuthorization(BaseProductAuthorization baseProductAuthorization) {
        baseProductAuthorization.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseProductAuthorizationMapper.insertBaseProductAuthorization(baseProductAuthorization);
    }

    /**
     * 修改平台管理-产品授权
     *
     * @param baseProductAuthorization 平台管理-产品授权
     * @return 结果
     */
    @Override
    public int updateBaseProductAuthorization(BaseProductAuthorization baseProductAuthorization) {
        return baseProductAuthorizationMapper.updateBaseProductAuthorization(baseProductAuthorization);
    }

    /**
     * 批量删除平台管理-产品授权
     *
     * @param ids 需要删除的平台管理-产品授权主键
     * @return 结果
     */
    @Override
    public int deleteBaseProductAuthorizationByIds(List<String> ids) {
        return baseProductAuthorizationMapper.deleteBaseProductAuthorizationByIds(ids);
    }

    /**
     * 删除平台管理-产品授权信息
     *
     * @param id 平台管理-产品授权主键
     * @return 结果
     */
    @Override
    public int deleteBaseProductAuthorizationById(String id) {
        return baseProductAuthorizationMapper.deleteBaseProductAuthorizationById(id);
    }
}
