@font-face {
  font-family: "CalciteWebCoreIcons";
  src: url("fonts/CalciteWebCoreIcons.ttf?qt9ftt") format("truetype"),
    url("fonts/CalciteWebCoreIcons.woff?qt9ftt") format("woff"),
    url("fonts/CalciteWebCoreIcons.svg?qt9ftt#CalciteWebCoreIcons")
      format("svg");
  font-weight: normal;
  font-style: normal;
}

[class^="esri-icon-"],
[class*=" esri-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "CalciteWebCoreIcons" !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.esri-icon-close:before {
  content: "\e600";
  color: inherit;
}
.esri-icon-drag-horizontal:before {
  content: "\e601";
  color: inherit;
}
.esri-icon-drag-vertical:before {
  content: "\e602";
  color: inherit;
}
.esri-icon-handle-horizontal:before {
  content: "\e603";
  color: inherit;
}
.esri-icon-handle-vertical:before {
  content: "\e604";
  color: inherit;
}
.esri-icon-check-mark:before {
  content: "\e605";
  color: inherit;
}
.esri-icon-left-triangle-arrow:before {
  content: "\e606";
  color: inherit;
}
.esri-icon-right-triangle-arrow:before {
  content: "\e607";
  color: inherit;
}
.esri-icon-down-arrow:before {
  content: "\e608";
  color: inherit;
}
.esri-icon-up-arrow:before {
  content: "\e609";
  color: inherit;
}
.esri-icon-overview-arrow-bottom-left:before {
  content: "\e60a";
  color: inherit;
}
.esri-icon-overview-arrow-bottom-right:before {
  content: "\e60b";
  color: inherit;
}
.esri-icon-overview-arrow-top-left:before {
  content: "\e60c";
  color: inherit;
}
.esri-icon-overview-arrow-top-right:before {
  content: "\e60d";
  color: inherit;
}
.esri-icon-maximize:before {
  content: "\e60e";
  color: inherit;
}
.esri-icon-minimize:before {
  content: "\e60f";
  color: inherit;
}
.esri-icon-checkbox-unchecked:before {
  content: "\e610";
  color: inherit;
}
.esri-icon-checkbox-checked:before {
  content: "\e611";
  color: inherit;
}
.esri-icon-radio-unchecked:before {
  content: "\e612";
  color: inherit;
}
.esri-icon-radio-checked:before {
  content: "\e613";
  color: inherit;
}
.esri-icon-up-arrow-circled:before {
  content: "\e614";
  color: inherit;
}
.esri-icon-down-arrow-circled:before {
  content: "\e615";
  color: inherit;
}
.esri-icon-left-arrow-circled:before {
  content: "\e616";
  color: inherit;
}
.esri-icon-right-arrow-circled:before {
  content: "\e617";
  color: inherit;
}
.esri-icon-zoom-out-fixed:before {
  content: "\e618";
  color: inherit;
}
.esri-icon-zoom-in-fixed:before {
  content: "\e619";
  color: inherit;
}
.esri-icon-refresh:before {
  content: "\e61a";
  color: inherit;
}
.esri-icon-edit:before {
  content: "\e61b";
  color: inherit;
}
.esri-icon-authorize:before {
  content: "\e61c";
  color: inherit;
}
.esri-icon-map-pin:before {
  content: "\e61d";
  color: inherit;
}
.esri-icon-blank-map-pin:before {
  content: "\e61e";
  color: inherit;
}
.esri-icon-table:before {
  content: "\e61f";
  color: inherit;
}
.esri-icon-plus:before {
  content: "\e620";
  color: inherit;
}
.esri-icon-minus:before {
  content: "\e621";
  color: inherit;
}
.esri-icon-beginning:before {
  content: "\e622";
  color: inherit;
}
.esri-icon-reverse:before {
  content: "\e623";
  color: inherit;
}
.esri-icon-pause:before {
  content: "\e624";
  color: inherit;
}
.esri-icon-play:before {
  content: "\e625";
  color: inherit;
}
.esri-icon-forward:before {
  content: "\e626";
  color: inherit;
}
.esri-icon-end:before {
  content: "\e627";
  color: inherit;
}
.esri-icon-erase:before {
  content: "\e628";
  color: inherit;
}
.esri-icon-up-down-arrows:before {
  content: "\e629";
  color: inherit;
}
.esri-icon-left:before {
  content: "\e62a";
  color: inherit;
}
.esri-icon-right:before {
  content: "\e62b";
  color: inherit;
}
.esri-icon-announcement:before {
  content: "\e62c";
  color: inherit;
}
.esri-icon-notice-round:before {
  content: "\e62d";
  color: inherit;
}
.esri-icon-notice-triangle:before {
  content: "\e62e";
  color: inherit;
}
.esri-icon-home:before {
  content: "\e62f";
  color: inherit;
}
.esri-icon-locate:before {
  content: "\e630";
  color: inherit;
}
.esri-icon-expand:before {
  content: "\e631";
  color: inherit;
}
.esri-icon-collapse:before {
  content: "\e632";
  color: inherit;
}
.esri-icon-layer-list:before {
  content: "\e633";
  color: inherit;
}
.esri-icon-basemap:before {
  content: "\e634";
  color: inherit;
}
.esri-icon-globe:before {
  content: "\e635";
  color: inherit;
}
.esri-icon-applications:before {
  content: "\e636";
  color: inherit;
}
.esri-icon-arrow-up-circled:before {
  content: "\e637";
  color: inherit;
}
.esri-icon-arrow-down-circled:before {
  content: "\e638";
  color: inherit;
}
.esri-icon-arrow-left-circled:before {
  content: "\e639";
  color: inherit;
}
.esri-icon-arrow-right-circled:before {
  content: "\e63a";
  color: inherit;
}
.esri-icon-minus-circled:before {
  content: "\e63b";
  color: inherit;
}
.esri-icon-plus-circled:before {
  content: "\e63c";
  color: inherit;
}
.esri-icon-add-attachment:before {
  content: "\e63d";
  color: inherit;
}
.esri-icon-attachment:before {
  content: "\e63e";
  color: inherit;
}
.esri-icon-calendar:before {
  content: "\e63f";
  color: inherit;
}
.esri-icon-close-circled:before {
  content: "\e640";
  color: inherit;
}
.esri-icon-browser:before {
  content: "\e641";
  color: inherit;
}
.esri-icon-collection:before {
  content: "\e642";
  color: inherit;
}
.esri-icon-comment:before {
  content: "\e643";
  color: inherit;
}
.esri-icon-configure-popup:before {
  content: "\e644";
  color: inherit;
}
.esri-icon-contact:before {
  content: "\e645";
  color: inherit;
}
.esri-icon-dashboard:before {
  content: "\e646";
  color: inherit;
}
.esri-icon-deny:before {
  content: "\e647";
  color: inherit;
}
.esri-icon-description:before {
  content: "\e648";
  color: inherit;
}
.esri-icon-directions:before {
  content: "\e649";
  color: inherit;
}
.esri-icon-directions2:before {
  content: "\e64a";
  color: inherit;
}
.esri-icon-documentation:before {
  content: "\e64b";
  color: inherit;
}
.esri-icon-duplicate:before {
  content: "\e64c";
  color: inherit;
}
.esri-icon-review:before {
  content: "\e64d";
  color: inherit;
}
.esri-icon-environment-settings:before {
  content: "\e64e";
  color: inherit;
}
.esri-icon-error:before {
  content: "\e64f";
  color: inherit;
}
.esri-icon-error2:before {
  content: "\e650";
  color: inherit;
}
.esri-icon-experimental:before {
  content: "\e651";
  color: inherit;
}
.esri-icon-feature-layer:before {
  content: "\e652";
  color: inherit;
}
.esri-icon-filter:before {
  content: "\e653";
  color: inherit;
}
.esri-icon-grant:before {
  content: "\e654";
  color: inherit;
}
.esri-icon-group:before {
  content: "\e655";
  color: inherit;
}
.esri-icon-key:before {
  content: "\e656";
  color: inherit;
}
.esri-icon-labels:before {
  content: "\e657";
  color: inherit;
}
.esri-icon-tag:before {
  content: "\e658";
  color: inherit;
}
.esri-icon-layers:before {
  content: "\e659";
  color: inherit;
}
.esri-icon-left-arrow:before {
  content: "\e65a";
  color: inherit;
}
.esri-icon-right-arrow:before {
  content: "\e65b";
  color: inherit;
}
.esri-icon-link-external:before {
  content: "\e65c";
  color: inherit;
}
.esri-icon-link:before {
  content: "\e65d";
  color: inherit;
}
.esri-icon-loading-indicator:before {
  content: "\e65e";
  color: inherit;
}
.esri-icon-maps:before {
  content: "\e65f";
  color: inherit;
}
.esri-icon-marketplace:before {
  content: "\e660";
  color: inherit;
}
.esri-icon-media:before {
  content: "\e661";
  color: inherit;
}
.esri-icon-media2:before {
  content: "\e662";
  color: inherit;
}
.esri-icon-menu:before {
  content: "\e663";
  color: inherit;
}
.esri-icon-mobile:before {
  content: "\e664";
  color: inherit;
}
.esri-icon-phone:before {
  content: "\e665";
  color: inherit;
}
.esri-icon-navigation:before {
  content: "\e666";
  color: inherit;
}
.esri-icon-pan:before {
  content: "\e667";
  color: inherit;
}
.esri-icon-printer:before {
  content: "\e668";
  color: inherit;
}
.esri-icon-pie-chart:before {
  content: "\e669";
  color: inherit;
}
.esri-icon-chart:before {
  content: "\e66a";
  color: inherit;
}
.esri-icon-line-chart:before {
  content: "\e66b";
  color: inherit;
}
.esri-icon-question:before {
  content: "\e66c";
  color: inherit;
}
.esri-icon-resend-invitation:before {
  content: "\e66d";
  color: inherit;
}
.esri-icon-rotate:before {
  content: "\e66e";
  color: inherit;
}
.esri-icon-save:before {
  content: "\e66f";
  color: inherit;
}
.esri-icon-settings:before {
  content: "\e670";
  color: inherit;
}
.esri-icon-settings2:before {
  content: "\e671";
  color: inherit;
}
.esri-icon-share:before {
  content: "\e672";
  color: inherit;
}
.esri-icon-sign-out:before {
  content: "\e673";
  color: inherit;
}
.esri-icon-support:before {
  content: "\e674";
  color: inherit;
}
.esri-icon-user:before {
  content: "\e675";
  color: inherit;
}
.esri-icon-time-clock:before {
  content: "\e676";
  color: inherit;
}
.esri-icon-trash:before {
  content: "\e677";
  color: inherit;
}
.esri-icon-upload:before {
  content: "\e678";
  color: inherit;
}
.esri-icon-download:before {
  content: "\e679";
  color: inherit;
}
.esri-icon-zoom-in-magnifying-glass:before {
  content: "\e67a";
  color: #6e6e6e;
}
.esri-icon-search:before {
  content: "\e67b";
  color: inherit;
}
.esri-icon-zoom-out-magnifying-glass:before {
  content: "\e67c";
  color: #6e6e6e;
}
.esri-icon-locked:before {
  content: "\e67d";
  color: inherit;
}
.esri-icon-unlocked:before {
  content: "\e67e";
  color: inherit;
}
.esri-icon-favorites:before {
  content: "\e67f";
  color: inherit;
}
.esri-icon-compass:before {
  content: "\e680";
  color: inherit;
}
.esri-icon-down:before {
  content: "\e681";
  color: inherit;
}
.esri-icon-up:before {
  content: "\e682";
  color: inherit;
}
.esri-icon-chat:before {
  content: "\e683";
  color: inherit;
}
.esri-icon-dock-bottom:before {
  content: "\e684";
  color: inherit;
}
.esri-icon-dock-left:before {
  content: "\e685";
  color: inherit;
}
.esri-icon-dock-right:before {
  content: "\e686";
  color: inherit;
}
.esri-icon-organization:before {
  content: "\e687";
  color: inherit;
}
.esri-icon-north-navigation:before {
  content: "\e688";
  color: inherit;
}
.esri-icon-locate-circled:before {
  content: "\e689";
  color: inherit;
}
.esri-icon-dial:before {
  content: "\e68a";
  color: inherit;
}
.esri-icon-polygon:before {
  content: "\e68b";
  color: inherit;
}
.esri-icon-polyline:before {
  content: "\e68c";
  color: inherit;
}
.esri-icon-visible:before {
  content: "\e68d";
  color: inherit;
}
.esri-icon-non-visible:before {
  content: "\e68e";
  color: inherit;
}
.esri-icon-link-vertical:before {
  content: "\e68f";
  color: inherit;
}
.esri-icon-unlocked-link-vertical:before {
  content: "\e690";
  color: inherit;
}
.esri-icon-link-horizontal:before {
  content: "\e691";
  color: inherit;
}
.esri-icon-unlocked-link-horizontal:before {
  content: "\e692";
  color: inherit;
}
.esri-icon-swap:before {
  content: "\e693";
  color: inherit;
}
.esri-icon-cta-link-external:before {
  content: "\e694";
  color: inherit;
}
.esri-icon-reply:before {
  content: "\e695";
  color: inherit;
}
.esri-icon-public:before {
  content: "\e696";
  color: inherit;
}
.esri-icon-share2:before {
  content: "\e697";
  color: inherit;
}
.esri-icon-launch-link-external:before {
  content: "\e698";
  color: inherit;
}
.esri-icon-rotate-back:before {
  content: "\e699";
  color: inherit;
}
.esri-icon-pan2:before {
  content: "\e69a";
  color: inherit;
}
.esri-icon-tracking:before {
  content: "\e69b";
  color: inherit;
}
.esri-icon-expand2:before {
  content: "\e69c";
  color: inherit;
}
.esri-icon-arrow-down:before {
  content: "\e69d";
  color: inherit;
}
.esri-icon-arrow-up:before {
  content: "\e69e";
  color: inherit;
}
.esri-icon-hollow-eye:before {
  content: "\e69f";
  color: inherit;
}
.esri-icon-play-circled:before {
  content: "\e6a0";
  color: inherit;
}
.esri-icon-volume-off:before {
  content: "\e6a1";
  color: inherit;
}
.esri-icon-volume-on:before {
  content: "\e6a2";
  color: inherit;
}
.esri-icon-bookmark:before {
  content: "\e900";
  color: inherit;
}
.esri-icon-lightbulb:before {
  content: "\e901";
  color: inherit;
}
.esri-icon-sketch-rectangle:before {
  content: "\e902";
  color: inherit;
}
.esri-icon-north-navigation-filled:before {
  content: "\e903";
  color: inherit;
}
.esri-icon-default-action:before {
  content: "\e904";
  color: inherit;
}
.esri-icon-undo:before {
  content: "\e905";
  color: inherit;
}
.esri-icon-redo:before {
  content: "\e906";
  color: inherit;
}
.esri-icon-cursor:before {
  content: "\e907";
  color: inherit;
}
.esri-icon-cursor-filled:before {
  content: "\e908";
  color: inherit;
}
.esri-icon-measure:before {
  content: "\e90a";
  color: inherit;
}
.esri-icon-measure-line:before {
  content: "\e909";
  color: inherit;
}
.esri-icon-measure-area:before {
  content: "\e90b";
  color: inherit;
}
.esri-icon-legend:before {
  content: "\e90c";
  color: inherit;
}
.esri-icon-sliders:before {
  content: "\e90d";
  color: inherit;
}
.esri-icon-sliders-horizontal:before {
  content: "\e90e";
  color: inherit;
}
.esri-icon-cursor-marquee:before {
  content: "\e90f";
  color: inherit;
}
.esri-icon-lasso:before {
  content: "\e910";
  color: inherit;
}
.esri-icon-elevation-profile:before {
  content: "\e911";
  color: inherit;
}
.esri-icon-slice:before {
  content: "\e913";
  color: inherit;
}
.esri-icon-line-of-sight:before {
  content: "\e912";
  color: inherit;
}
.esri-icon-zoom-to-object:before {
  content: "\e914";
  color: inherit;
}
.esri-icon-urban-model:before {
  content: "\e915";
  color: inherit;
}
.esri-icon-measure-building-height-shadow:before {
  content: "\e916";
  color: inherit;
}
.esri-icon-partly-cloudy:before {
  content: "\e917";
  color: inherit;
}
