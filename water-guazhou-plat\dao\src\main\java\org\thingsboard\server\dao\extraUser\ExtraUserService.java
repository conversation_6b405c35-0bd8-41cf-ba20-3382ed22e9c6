package org.thingsboard.server.dao.extraUser;

import org.thingsboard.server.dao.model.sql.ExtraUser;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/3 14:01
 */
public interface ExtraUserService {


    ExtraUser save(ExtraUser extraUser);

    List<ExtraUser> findByTenant(String tenantId);

    boolean delete(String id);

    boolean deleteAll(List<String> ids);

    int checkName(String name,String tenantId);

}
