package org.thingsboard.server.dimain.smartproduct.totalreport.primeCost;

import lombok.Getter;
import lombok.Setter;
import org.joda.time.format.DateTimeFormat;

import java.util.Date;

@Getter
@Setter
public class PrimeCostStatisticRequest {
    // 开始时间
    private String fromTime;

    // 结束时间
    private String toTime;

    public Date getFromTime() {
        return DateTimeFormat.forPattern("yyyy-MM-dd").withOffsetParsed().parseDateTime(fromTime)
                .millisOfDay().withMinimumValue()
                .toDate();
    }

    public Date getToTime() {
        return DateTimeFormat.forPattern("yyyy-MM-dd").withOffsetParsed().parseDateTime(toTime)
                .millisOfDay().withMaximumValue()
                .toDate();
    }

}
