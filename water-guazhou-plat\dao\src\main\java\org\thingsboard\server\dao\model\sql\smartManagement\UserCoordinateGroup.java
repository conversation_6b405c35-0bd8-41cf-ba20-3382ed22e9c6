package org.thingsboard.server.dao.model.sql.smartManagement;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.List;

@Getter
@Setter
@ResponseEntity
public class UserCoordinateGroup {

    // 用户id
    @ParseUsername(withDepartment = true)
    private String userId;

    private List<UserCoordinate> coordinates;
}
