import{e as o,y as l,a as b,v as q,Z as Q,i as Z,Q as W,aA as R,M as X,S as y,b as P}from"./Point-WxyopZva.js";import{U as k,c as Y,l as H,B as K}from"./widget-BcWKanF2.js";import{T as g,R as h,fp as ee,aO as te}from"./index-r0dFAfgr.js";import{A as J,bp as w,g9 as D,d$ as A,ga as x,c_ as ie,aE as se,fo as re,db as T,w as ne,fp as ae,gb as oe,gc as le,g1 as ue,gd as he,cY as ce,et as de,c4 as pe,da as fe,z as S,ge as _e,gf as N,gg as L,B as ge,gh as $,x as ye}from"./MapView-DaoQedLH.js";import{g as me}from"./FeatureStore-BG3NYFyq.js";import{e as Ee}from"./QueryEngine-qET-Q1Qx.js";import{r as U,a as M,n as z}from"./symbologySnappingCandidates-CZjQb_7m.js";import{o as Fe}from"./BoundsStore-wYOD4ytd.js";import"./pe-B8dP0-Ut.js";import"./optimizedFeatureQueryEngineAdapter-VytK6WwF.js";import"./centroid-UTistape.js";import"./utils-dKbgHYZY.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./timeSupport-vHbsRqQz.js";import"./PooledRBush-CoOUdN-a.js";let v=class extends q{constructor(){super(...arguments),this.updating=!1,this._pending=[]}push(t,e){this._pending.push({promise:t,callback:e}),this._pending.length===1&&this._process()}_process(){if(!this._pending.length)return void(this.updating=!1);this.updating=!0;const t=this._pending[0];t.promise.then(e=>t.callback(e)).catch(()=>{}).then(()=>{this._pending.shift(),this._process()})}};o([l()],v.prototype,"updating",void 0),v=o([b("esri.core.AsyncSequence")],v);class Ce{constructor(e,i){this.data=e,this.resolution=i,this.state={type:a.CREATED},this.alive=!0}process(e){switch(this.state.type){case a.CREATED:return this.state=this._gotoFetchCount(this.state,e),this.state.task.promise.then(e.resume,e.resume);case a.FETCH_COUNT:break;case a.FETCHED_COUNT:return this.state=this._gotoFetchFeatures(this.state,e),this.state.task.promise.then(e.resume,e.resume);case a.FETCH_FEATURES:break;case a.FETCHED_FEATURES:this.state=this._goToDone(this.state,e);case a.DONE:}return null}get debugInfo(){return{data:this.data,featureCount:this._featureCount,state:this._stateToString}}get _featureCount(){switch(this.state.type){case a.CREATED:case a.FETCH_COUNT:return 0;case a.FETCHED_COUNT:return this.state.featureCount;case a.FETCH_FEATURES:return this.state.previous.featureCount;case a.FETCHED_FEATURES:return this.state.features.length;case a.DONE:return this.state.previous.features.length}}get _stateToString(){switch(this.state.type){case a.CREATED:return"created";case a.FETCH_COUNT:return"fetch-count";case a.FETCHED_COUNT:return"fetched-count";case a.FETCH_FEATURES:return"fetch-features";case a.FETCHED_FEATURES:return"fetched-features";case a.DONE:return"done"}}_gotoFetchCount(e,i){return{type:a.FETCH_COUNT,previous:e,task:w(async s=>{const r=await D(i.fetchCount(this,s));this.state.type===a.FETCH_COUNT&&(this.state=this._gotoFetchedCount(this.state,r.ok?r.value:1/0))})}}_gotoFetchedCount(e,i){return{type:a.FETCHED_COUNT,featureCount:i,previous:e}}_gotoFetchFeatures(e,i){return{type:a.FETCH_FEATURES,previous:e,task:w(async s=>{const r=await D(i.fetchFeatures(this,e.featureCount,s));this.state.type===a.FETCH_FEATURES&&(this.state=this._gotoFetchedFeatures(this.state,r.ok?r.value:[]))})}}_gotoFetchedFeatures(e,i){return{type:a.FETCHED_FEATURES,previous:e,features:i}}_goToDone(e,i){return i.finish(this,e.features),{type:a.DONE,previous:e}}reset(){const e=this.state;switch(this.state={type:a.CREATED},e.type){case a.CREATED:case a.FETCHED_COUNT:case a.FETCHED_FEATURES:case a.DONE:break;case a.FETCH_COUNT:case a.FETCH_FEATURES:e.task.abort()}}intersects(e){return!(!g(e)&&this.data.extent)||(A(e,j),x(this.data.extent,j))}}var a;(function(t){t[t.CREATED=0]="CREATED",t[t.FETCH_COUNT=1]="FETCH_COUNT",t[t.FETCHED_COUNT=2]="FETCHED_COUNT",t[t.FETCH_FEATURES=3]="FETCH_FEATURES",t[t.FETCHED_FEATURES=4]="FETCHED_FEATURES",t[t.DONE=5]="DONE"})(a||(a={}));const j=J();let c=class extends ie{get _minimumVerticesPerFeature(){var e;switch((e=this.store)==null?void 0:e.featureStore.geometryType){case"esriGeometryPoint":case"esriGeometryMultipoint":return 1;case"esriGeometryPolygon":return 4;case"esriGeometryPolyline":return 2}}set filter(e){const i=this._get("filter"),s=this._filterProperties(e);JSON.stringify(i)!==JSON.stringify(s)&&this._set("filter",s)}set customParameters(e){const i=this._get("customParameters");JSON.stringify(i)!==JSON.stringify(e)&&this._set("customParameters",e)}get _configuration(){return{filter:this.filter,customParameters:this.customParameters,tileInfo:this.tileInfo,tileSize:this.tileSize}}set tileInfo(e){const i=this._get("tileInfo");i!==e&&(h(e)&&h(i)&&JSON.stringify(e)===JSON.stringify(i)||(this._set("tileInfo",e),this.store.tileInfo=e))}set tileSize(e){this._get("tileSize")!==e&&this._set("tileSize",e)}get updating(){return this.updatingExcludingEdits||this._pendingEdits.updating}get updatingExcludingEdits(){return this.updatingHandles.updating}get hasZ(){return this.store.featureStore.hasZ}constructor(e){super(e),this.tilesOfInterest=[],this.availability=0,this._pendingTiles=new Map,this._pendingEdits=new v,this._pendingEditsAbortController=new AbortController}initialize(){this._initializeFetchExtent(),this.updatingHandles.add(()=>this._configuration,()=>this.refresh()),this.updatingHandles.add(()=>this.tilesOfInterest,(e,i)=>{ee(e,i,({id:s},{id:r})=>s===r)||this._process()},k)}destroy(){this._pendingTiles.forEach(e=>this._deletePendingTile(e)),this._pendingTiles.clear(),this.store.destroy(),this.tilesOfInterest.length=0,this._pendingEditsAbortController.abort(),this._pendingEditsAbortController=null}refresh(){this.store.refresh(),this._pendingTiles.forEach(e=>this._deletePendingTile(e)),this._process()}applyEdits(e){this._pendingEdits.push(e,async i=>{if(i.addedFeatures.length===0&&i.updatedFeatures.length===0&&i.deletedFeatures.length===0)return;for(const[,r]of this._pendingTiles)r.reset();const s={...i,deletedFeatures:i.deletedFeatures.map(({objectId:r,globalId:n})=>r&&r!==-1?r:this._lookupObjectIdByGlobalId(n))};await this.updatingHandles.addPromise(this.store.processEdits(s,(r,n)=>this._queryFeaturesById(r,n),this._pendingEditsAbortController.signal)),this._processPendingTiles()})}_initializeFetchExtent(){if(!this.capabilities.query.supportsExtent||!se(this.url))return;const e=w(async i=>{var s;try{const r=await re(this.url,new T({where:"1=1",outSpatialReference:this.spatialReference,cacheHint:!!this.capabilities.query.supportsCacheHint||void 0}),{query:this._configuration.customParameters,signal:i});this.store.extent=ne.fromJSON((s=r.data)==null?void 0:s.extent)}catch(r){Q(r),Z.getLogger(this.declaredClass).warn("Failed to fetch data extent",r)}});this.updatingHandles.addPromise(e.promise.then(()=>this._process())),this.handles.add(W(()=>e.abort()))}get debugInfo(){return{numberOfFeatures:this.store.featureStore.numFeatures,tilesOfInterest:this.tilesOfInterest,pendingTiles:Array.from(this._pendingTiles.values()).map(e=>e.debugInfo),storedTiles:this.store.debugInfo}}_process(){this._markTilesNotAlive(),this._createPendingTiles(),this._deletePendingTiles(),this._processPendingTiles()}_markTilesNotAlive(){for(const[,e]of this._pendingTiles)e.alive=!1}_createPendingTiles(){const e=this._collectMissingTilesInfo();if(this._setAvailability(g(e)?1:e.coveredArea/e.fullArea),!g(e))for(const{data:i,resolution:s}of e.missingTiles){const r=this._pendingTiles.get(i.id);r?(r.resolution=s,r.alive=!0):this._createPendingTile(i,s)}}_collectMissingTilesInfo(){let e=null;for(let i=this.tilesOfInterest.length-1;i>=0;i--){const s=this.tilesOfInterest[i],r=this.store.process(s,(n,u)=>this._verifyTileComplexity(n,u));g(e)?e=r:e.prepend(r)}return e}_deletePendingTiles(){for(const[,e]of this._pendingTiles)e.alive||this._deletePendingTile(e)}_processPendingTiles(){const e={fetchCount:(i,s)=>this._fetchCount(i,s),fetchFeatures:(i,s,r)=>this._fetchFeatures(i,s,r),finish:(i,s)=>this._finishPendingTile(i,s),resume:()=>this._processPendingTiles()};if(this._ensureFetchAllCounts(e))for(const[,i]of this._pendingTiles)this._verifyTileComplexity(this.store.getFeatureCount(i.data),i.resolution)&&this.updatingHandles.addPromise(i.process(e))}_verifyTileComplexity(e,i){return this._verifyVertexComplexity(e)&&this._verifyFeatureDensity(e,i)}_verifyVertexComplexity(e){return e*this._minimumVerticesPerFeature<ve}_verifyFeatureDensity(e,i){if(g(this.tileInfo))return!1;const s=this.tileSize*i;return e*(Se/(s*s))<be}_ensureFetchAllCounts(e){let i=!0;for(const[,s]of this._pendingTiles)s.state.type<a.FETCHED_COUNT&&this.updatingHandles.addPromise(s.process(e)),s.state.type<=a.FETCH_COUNT&&(i=!1);return i}_finishPendingTile(e,i){this.store.add(e.data,i),this._deletePendingTile(e),this._updateAvailability()}_updateAvailability(){const e=this._collectMissingTilesInfo();this._setAvailability(g(e)?1:e.coveredArea/e.fullArea)}_setAvailability(e){this._set("availability",e)}_createPendingTile(e,i){const s=new Ce(e,i);return this._pendingTiles.set(e.id,s),s}_deletePendingTile(e){e.reset(),this._pendingTiles.delete(e.data.id)}async _fetchCount(e,i){return this.store.fetchCount(e.data,this.url,this._createCountQuery(e),{query:this.customParameters,timeout:I,signal:i})}async _fetchFeatures(e,i,s){let r=0;const n=[];let u=0,f=i;for(;;){const d=this._createFeaturesQuery(e),p=this._setPagingParameters(d,r,f),{features:_,exceededTransferLimit:V}=await this._queryFeatures(d,s);p&&(r+=te(d.num)),u+=_.length;for(const G of _)n.push(G);if(f=i-u,!p||!V||f<=0)return n}}_filterProperties(e){return g(e)?{where:"1=1",gdbVersion:void 0,timeExtent:void 0}:{where:e.where||"1=1",timeExtent:e.timeExtent,gdbVersion:e.gdbVersion}}_lookupObjectIdByGlobalId(e){const i=this.globalIdField,s=this.objectIdField;if(g(i))throw new Error("Expected globalIdField to be defined");let r=null;if(this.store.featureStore.forEach(n=>{e===n.attributes[i]&&(r=n.objectId??n.attributes[s])}),g(r))throw new Error(`Expected to find a feature with globalId ${e}`);return r}_queryFeaturesById(e,i){const s=this._createFeaturesQuery();return s.objectIds=e,this._queryFeatures(s,i)}_queryFeatures(e,i){return this.capabilities.query.supportsFormatPBF?this._queryFeaturesPBF(e,i):this._queryFeaturesJSON(e,i)}async _queryFeaturesPBF(e,i){const{sourceSpatialReference:s}=this,{data:r}=await ae(this.url,e,new oe({sourceSpatialReference:s}),{query:this._configuration.customParameters,timeout:I,signal:i});return le(r)}async _queryFeaturesJSON(e,i){const{sourceSpatialReference:s}=this,{data:r}=await ue(this.url,e,s,{query:this._configuration.customParameters,timeout:I,signal:i});return he(r,this.objectIdField)}_createCountQuery(e){const i=this._createBaseQuery(e);return this.capabilities.query.supportsCacheHint&&(i.cacheHint=!0),i}_createFeaturesQuery(e=null){const i=this._createBaseQuery(e);return i.outFields=this.globalIdField?[this.globalIdField,this.objectIdField]:[this.objectIdField],i.returnGeometry=!0,h(e)&&(this.capabilities.query.supportsResultType?i.resultType="tile":this.capabilities.query.supportsCacheHint&&(i.cacheHint=!0)),i}_createBaseQuery(e){const i=new T({returnZ:this.hasZ,returnM:!1,geometry:h(this.tileInfo)&&h(e)?ce(e.data.extent,this.tileInfo.spatialReference):void 0}),s=this._configuration.filter;return h(s)&&(i.where=s.where,i.gdbVersion=s.gdbVersion,i.timeExtent=s.timeExtent),i.outSpatialReference=this.spatialReference,i}_setPagingParameters(e,i,s){if(!this.capabilities.query.supportsPagination)return!1;const{supportsMaxRecordCountFactor:r,supportsCacheHint:n,tileMaxRecordCount:u,maxRecordCount:f,supportsResultType:d}=this.capabilities.query,p=r?T.MAX_MAX_RECORD_COUNT_FACTOR:1,_=p*((d||n)&&u?u:f||Te);return e.start=i,r?(e.maxRecordCountFactor=Math.min(p,Math.ceil(s/_)),e.num=Math.min(s,e.maxRecordCountFactor*_)):e.num=Math.min(s,_),!0}};o([l({constructOnly:!0})],c.prototype,"url",void 0),o([l({constructOnly:!0})],c.prototype,"objectIdField",void 0),o([l({constructOnly:!0})],c.prototype,"globalIdField",void 0),o([l({constructOnly:!0})],c.prototype,"capabilities",void 0),o([l({constructOnly:!0})],c.prototype,"sourceSpatialReference",void 0),o([l({constructOnly:!0})],c.prototype,"spatialReference",void 0),o([l({constructOnly:!0})],c.prototype,"store",void 0),o([l({readOnly:!0})],c.prototype,"_minimumVerticesPerFeature",null),o([l()],c.prototype,"filter",null),o([l()],c.prototype,"customParameters",null),o([l({readOnly:!0})],c.prototype,"_configuration",null),o([l()],c.prototype,"tileInfo",null),o([l()],c.prototype,"tileSize",null),o([l()],c.prototype,"tilesOfInterest",void 0),o([l({readOnly:!0})],c.prototype,"updating",null),o([l({readOnly:!0})],c.prototype,"updatingExcludingEdits",null),o([l({readOnly:!0})],c.prototype,"availability",void 0),o([l()],c.prototype,"hasZ",null),c=o([b("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTiledFetcher")],c);const Te=2e3,I=6e5,ve=1e6,Se=25,be=1;class Ie{constructor(){this._store=new Map,this._byteSize=0}set(e,i){this.delete(e),this._store.set(e,i),this._byteSize+=i.byteSize}delete(e){const i=this._store.get(e);return!!this._store.delete(e)&&(i!=null&&(this._byteSize-=i.byteSize),!0)}get(e){return this._used(e),this._store.get(e)}has(e){return this._used(e),this._store.has(e)}clear(){this._store.clear()}applyByteSizeLimit(e,i){for(const[s,r]of this._store){if(this._byteSize<=e)break;this.delete(s),i(r)}}values(){return this._store.values()}[Symbol.iterator](){return this._store[Symbol.iterator]()}_used(e){const i=this._store.get(e);i&&(this._store.delete(e),this._store.set(e,i))}}let F=class extends q{constructor(t){super(t),this.tileInfo=null,this.extent=null,this.maximumByteSize=10*de.MEGABYTES,this._tileBounds=new Fe,this._tiles=new Ie,this._refCounts=new Map,this._tileFeatureCounts=new Map,this._tmpBoundingRect=J()}add(t,e){const i=[];for(const s of e)this._referenceFeature(s.objectId)===E.ADDED&&i.push(s);this._addTileStorage(t,new Set(e.map(s=>s.objectId)),we(e)),this.featureStore.addMany(i),this._tiles.applyByteSizeLimit(this.maximumByteSize,s=>this._removeTileStorage(s))}destroy(){this.clear(),this._tileFeatureCounts.clear()}clear(){this.featureStore.clear(),this._tileBounds.clear(),this._tiles.clear(),this._refCounts.clear()}refresh(){this.clear(),this._tileFeatureCounts.clear()}processEdits(t,e,i){return this._processEditsDelete(t.deletedFeatures.concat(t.updatedFeatures)),this._processEditsRefetch(t.addedFeatures.concat(t.updatedFeatures),e,i)}_addTileStorage(t,e,i){const s=t.id;this._tiles.set(s,new xe(t,e,i)),this._tileBounds.set(s,t.extent),this._tileFeatureCounts.set(s,e.size)}_remove({id:t}){const e=this._tiles.get(t);e&&this._removeTileStorage(e)}_removeTileStorage(t){const e=[];for(const s of t.objectIds)this._unreferenceFeature(s)===E.REMOVED&&e.push(s);this.featureStore.removeManyById(e);const i=t.data.id;this._tiles.delete(i),this._tileBounds.delete(i)}_processEditsDelete(t){this.featureStore.removeManyById(t);for(const[,e]of this._tiles){for(const i of t)e.objectIds.delete(i);this._tileFeatureCounts.set(e.data.id,e.objectIds.size)}for(const e of t)this._refCounts.delete(e)}async _processEditsRefetch(t,e,i){const s=(await e(t,i)).features,{hasZ:r,hasM:n}=this.featureStore;for(const u of s){const f=pe(this._tmpBoundingRect,u.geometry,r,n);g(f)||this._tileBounds.forEachInBounds(f,d=>{const p=this._tiles.get(d);this.featureStore.add(u);const _=u.objectId;p.objectIds.has(_)||(p.objectIds.add(_),this._referenceFeature(_),this._tileFeatureCounts.set(p.data.id,p.objectIds.size))})}}process(t,e=()=>!0){if(g(this.tileInfo)||!t.extent||h(this.extent)&&!x(A(this.extent,this._tmpBoundingRect),t.extent))return new O(t);if(this._tiles.has(t.id))return new O(t);const i=this._createTileTree(t,this.tileInfo);return this._simplify(i,e,null,0,1),this._collectMissingTiles(t,i,this.tileInfo)}get debugInfo(){return Array.from(this._tiles.values()).map(({data:t})=>({data:t,featureCount:this._tileFeatureCounts.get(t.id)||0}))}getFeatureCount(t){return this._tileFeatureCounts.get(t.id)??0}async fetchCount(t,e,i,s){const r=this._tileFeatureCounts.get(t.id);if(r!=null)return r;const n=await fe(e,i,s);return this._tileFeatureCounts.set(t.id,n.data.count),n.data.count}_createTileTree(t,e){const i=new B(t.level,t.row,t.col);return e.updateTileInfo(i,S.ExtrapolateOptions.POWER_OF_TWO),this._tileBounds.forEachInBounds(t.extent,s=>{var n;const r=(n=this._tiles.get(s))==null?void 0:n.data;r&&this._tilesAreRelated(t,r)&&this._populateChildren(i,r,e,this._tileFeatureCounts.get(r.id)||0)}),i}_tilesAreRelated(t,e){if(!t||!e)return!1;if(t.level===e.level)return t.row===e.row&&t.col===e.col;const i=t.level<e.level,s=i?t:e,r=i?e:t,n=1<<r.level-s.level;return Math.floor(r.row/n)===s.row&&Math.floor(r.col/n)===s.col}_populateChildren(t,e,i,s){const r=e.level-t.level-1;if(r<0)return void(t.isLeaf=!0);const n=e.row>>r,u=e.col>>r,f=t.row<<1,d=u-(t.col<<1)+(n-f<<1),p=t.children[d];if(h(p))this._populateChildren(p,e,i,s);else{const _=new B(t.level+1,n,u);i.updateTileInfo(_,S.ExtrapolateOptions.POWER_OF_TWO),t.children[d]=_,this._populateChildren(_,e,i,s)}}_simplify(t,e,i,s,r){const n=r*r;if(t.isLeaf)return e(this.getFeatureCount(t),r)?0:(this._remove(t),h(i)&&(i.children[s]=null),n);const u=r/2,f=u*u;let d=0;for(let p=0;p<t.children.length;p++){const _=t.children[p];d+=h(_)?this._simplify(_,e,t,p,u):f}return d===0?this._mergeChildren(t):1-d/n<Pe&&(this._purge(t),h(i)&&(i.children[s]=null),d=n),d}_mergeChildren(t){const e=new Set;let i=0;this._forEachLeaf(t,s=>{const r=this._tiles.get(s.id);if(r){i+=r.byteSize;for(const n of r.objectIds)e.has(n)||(e.add(n),this._referenceFeature(n));this._remove(s)}}),this._addTileStorage(t,e,i),t.isLeaf=!0,t.children[0]=t.children[1]=t.children[2]=t.children[3]=null,this._tileFeatureCounts.set(t.id,e.size)}_forEachLeaf(t,e){for(const i of t.children)g(i)||(i.isLeaf?e(i):this._forEachLeaf(i,e))}_purge(t){if(!g(t))if(t.isLeaf)this._remove(t);else for(let e=0;e<t.children.length;e++){const i=t.children[e];this._purge(i),t.children[e]=null}}_collectMissingTiles(t,e,i){const s=new Re(i,t,this.extent);return this._collectMissingTilesRecurse(e,s,1),s.info}_collectMissingTilesRecurse(t,e,i){if(t.isLeaf)return;if(!t.hasChildren)return void e.addMissing(t.level,t.row,t.col,i);const s=i/2;for(let r=0;r<t.children.length;r++){const n=t.children[r];g(n)?e.addMissing(t.level+1,(t.row<<1)+((2&r)>>1),(t.col<<1)+(1&r),s):this._collectMissingTilesRecurse(n,e,s)}}_referenceFeature(t){const e=(this._refCounts.get(t)||0)+1;return this._refCounts.set(t,e),e===1?E.ADDED:E.UNCHANGED}_unreferenceFeature(t){const e=(this._refCounts.get(t)||0)-1;return e===0?(this._refCounts.delete(t),E.REMOVED):(e>0&&this._refCounts.set(t,e),E.UNCHANGED)}get test(){return{tiles:Array.from(this._tiles.values()).map(t=>`${t.data.id}:[${Array.from(t.objectIds)}]`),featureReferences:Array.from(this._refCounts.keys()).map(t=>`${t}:${this._refCounts.get(t)}`)}}};function we(t){return t.reduce((e,i)=>e+Oe(i),0)}function Oe(t){return 32+Ae(t.geometry)+_e(t.attributes)}function Ae(t){if(g(t))return 0;const e=N(t.lengths,4);return 32+N(t.coords,8)+e}o([l({constructOnly:!0})],F.prototype,"featureStore",void 0),o([l()],F.prototype,"tileInfo",void 0),o([l()],F.prototype,"extent",void 0),o([l()],F.prototype,"maximumByteSize",void 0),F=o([b("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTileStore")],F);class xe{constructor(e,i,s){this.data=e,this.objectIds=i,this.byteSize=s}}class B{constructor(e,i,s){this.level=e,this.row=i,this.col=s,this.isLeaf=!1,this.extent=null,this.children=[null,null,null,null]}get hasChildren(){return!this.isLeaf&&(h(this.children[0])||h(this.children[1])||h(this.children[2])||h(this.children[3]))}}class O{constructor(e,i=[]){this.missingTiles=i,this.fullArea=0,this.coveredArea=0,this.fullArea=L(e.extent),this.coveredArea=this.fullArea}prepend(e){this.missingTiles=e.missingTiles.concat(this.missingTiles),this.coveredArea+=e.coveredArea,this.fullArea+=e.fullArea}}let Re=class{constructor(e,i,s){this._tileInfo=e,this._extent=null,this.info=new O(i),h(s)&&(this._extent=A(s))}addMissing(e,i,s,r){const n=new ge(null,e,i,s);this._tileInfo.updateTileInfo(n,S.ExtrapolateOptions.POWER_OF_TWO),g(n.extent)||h(this._extent)&&!x(this._extent,n.extent)||(this.info.missingTiles.push({data:n,resolution:r}),this.info.coveredArea-=L(n.extent))}};const Pe=.18751;var E;(function(t){t[t.ADDED=0]="ADDED",t[t.REMOVED=1]="REMOVED",t[t.UNCHANGED=2]="UNCHANGED"})(E||(E={}));let C=class extends Y.EventedAccessor{constructor(){super(...arguments),this._isInitializing=!0,this.remoteClient=null,this._whenSetup=R(),this._elevationAligner=U(),this._elevationFilter=M(),this._symbologyCandidatesFetcher=z(),this._handles=new X,this._updatingHandles=new $,this._editsUpdatingHandles=new $,this._pendingApplyEdits=new Map,this._alignPointsInFeatures=async(t,e)=>{const i={points:t},s=await this.remoteClient.invoke("alignElevation",i,{signal:e});return y(e),s},this._getSymbologyCandidates=async(t,e)=>{const i={candidates:t,spatialReference:this._spatialReference.toJSON()},s=await this.remoteClient.invoke("getSymbologyCandidates",i,{signal:e});return y(e),s}}get updating(){return this.updatingExcludingEdits||this._editsUpdatingHandles.updating||this._featureFetcher.updating}get updatingExcludingEdits(){return this._featureFetcher.updatingExcludingEdits||this._isInitializing||this._updatingHandles.updating}destroy(){var t,e,i,s;(t=this._featureFetcher)==null||t.destroy(),(e=this._queryEngine)==null||e.destroy(),(i=this._featureStore)==null||i.clear(),(s=this._handles)==null||s.destroy()}async setup(t){if(this.destroyed)return{result:{}};const{geometryType:e,objectIdField:i,timeInfo:s,fields:r}=t.serviceInfo,{hasZ:n}=t,u=P.fromJSON(t.spatialReference);this._spatialReference=u,this._featureStore=new me({...t.serviceInfo,hasZ:n,hasM:!1}),this._queryEngine=new Ee({spatialReference:t.spatialReference,featureStore:this._featureStore,geometryType:e,fields:r,hasZ:n,hasM:!1,objectIdField:i,timeInfo:s}),this._featureFetcher=new c({store:new F({featureStore:this._featureStore}),url:t.serviceInfo.url,objectIdField:t.serviceInfo.objectIdField,globalIdField:t.serviceInfo.globalIdField,capabilities:t.serviceInfo.capabilities,spatialReference:u,sourceSpatialReference:P.fromJSON(t.serviceInfo.spatialReference)});const f=t.configuration.viewType==="3d";return this._elevationAligner=U(f,{elevationInfo:h(t.elevationInfo)?ye.fromJSON(t.elevationInfo):null,alignPointsInFeatures:this._alignPointsInFeatures,spatialReference:u}),this._elevationFilter=M(f),this._handles.add([H(()=>this._featureFetcher.availability,d=>this.emit("notify-availability",{availability:d}),k),H(()=>this.updating,()=>this._notifyUpdating())]),this._whenSetup.resolve(),this._isInitializing=!1,this.configure(t.configuration)}async configure(t){return await this._updatingHandles.addPromise(this._whenSetup.promise),this._updateFeatureFetcherConfiguration(t),{result:{}}}async fetchCandidates(t,e){await this._whenSetup.promise,y(e);const i=He(t),s=h(e)?e.signal:null,r=await this._queryEngine.executeQueryForSnapping(i,s);y(s);const n=await this._elevationAligner.alignCandidates(r.candidates,s);y(s);const u=await this._symbologyCandidatesFetcher.fetch(n,s);y(s);const f=u.length===0?n:n.concat(u);return{result:{candidates:this._elevationFilter.filter(i,f)}}}async updateTiles(t,e){return await this._updatingHandles.addPromise(this._whenSetup.promise),y(e),this._featureFetcher.tileSize=t.tileSize,this._featureFetcher.tilesOfInterest=t.tiles,this._featureFetcher.tileInfo=h(t.tileInfo)?S.fromJSON(t.tileInfo):null,m}async refresh(t,e){return await this._updatingHandles.addPromise(this._whenSetup.promise),y(e),this._featureFetcher.refresh(),m}async whenNotUpdating(t,e){return await this._updatingHandles.addPromise(this._whenSetup.promise),y(e),await K(()=>!this.updatingExcludingEdits,e),y(e),m}async getDebugInfo(t,e){return y(e),{result:this._featureFetcher.debugInfo}}async beginApplyEdits(t,e){this._updatingHandles.addPromise(this._whenSetup.promise),y(e);const i=R();return this._pendingApplyEdits.set(t.id,i),this._featureFetcher.applyEdits(i.promise),this._editsUpdatingHandles.addPromise(i.promise),m}async endApplyEdits(t,e){const i=this._pendingApplyEdits.get(t.id);return i&&i.resolve(t.edits),y(e),m}async notifyElevationSourceChange(t,e){return this._elevationAligner.notifyElevationSourceChange(),m}async notifySymbologyChange(t,e){return this._symbologyCandidatesFetcher.notifySymbologyChange(),m}async setSymbologySnappingSupported(t){return this._symbologyCandidatesFetcher=z(t,this._getSymbologyCandidates),m}_updateFeatureFetcherConfiguration(t){this._featureFetcher.filter=h(t.filter)?T.fromJSON(t.filter):null,this._featureFetcher.customParameters=t.customParameters}_notifyUpdating(){this.emit("notify-updating",{updating:this.updating})}};o([l({readOnly:!0})],C.prototype,"updating",null),o([l({readOnly:!0})],C.prototype,"updatingExcludingEdits",null),o([l()],C.prototype,"_isInitializing",void 0),C=o([b("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceSnappingSourceWorker")],C);const nt=C;function He(t){return{point:t.point,mode:t.mode,distance:t.distance,types:t.types,query:h(t.filter)?t.filter:{where:"1=1"}}}const m={result:{}};export{nt as default};
