import{_ as ne}from"./Panel-DyoxrWMd.js";import{d as le,c as k,r as S,b,X as me,W as pe,Q as ue,g as ce,h as de,F as H,q as V,i as D,p as ye,_ as ge,aq as fe,C as be}from"./index-r0dFAfgr.js";import{w as he}from"./MapView-DaoQedLH.js";import{s as G,d as ve,h as we,g as xe}from"./FeatureHelper-Da16o0mu.js";import{s as Se,a as Le}from"./GPHelper-fLrvVD-A.js";import{i as Oe,e as Ie}from"./IdentifyHelper-RJWmLn49.js";import{g as M,a as j,e as $}from"./LayerHelper-Cn-iiqxI.js";import{a as T,i as R}from"./QueryHelper-ILO3qZqg.js";import{s as N}from"./ToolHelper-BiiInOzB.js";import{GetFieldConfig as _e}from"./fieldconfig-Bk3o1wi7.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import"./Point-WxyopZva.js";/* empty css                                                                      */import{b as Ce}from"./URLHelper-B9aplt5w.js";import"./index-0NlGN6gS.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import{g as ke}from"./gisUser-Ba96nctf.js";import De from"./RightDrawerMap-D5PhmGFO.js";import"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./scaleUtils-DgkF6NQH.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./project-DUuzYgGl.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./imageBitmapUtils-Db1drMDc.js";import"./ExportImageParameters-BiedgHNY.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Ae={class:"table-box"},Fe=le({__name:"BurstAnalys copy",setup(Pe){const X=k(),E=k(),B=k(),a=S({tabs:[],detailUrl:"",curOperate:"",mustshutOids:[],extentMustShutOids:[]}),e={identifyResult:void 0,markLayer:void 0,mapClick:void 0,queryParams:Oe(),jobid:"",valveFlag:!1,deviceids:[],devicelayers:["阀门","计量装置"],mustShutVolveFeatures:[]},_=S({columns:[{label:"管线类型",prop:"layerName"},{label:"管线编号",prop:"value"}],dataList:[],pagination:{hide:!0}}),C=S({data:[],columns:[]}),L=S({handleRowDbClick:t=>{var i;const r=e.mustShutVolveFeatures.find(s=>s.attributes.SID===t.SID);r&&((i=e.view)==null||i.goTo(r))},dataList:[],columns:[{label:"编号",prop:"SID"},{label:"阀门级别",prop:"VALVECLASS"}],pagination:{hide:!0}}),O=S({handleSelectChange:t=>{var s,o;if(!e.view)return;O.selectList=t||[];const r=t.map(n=>n.SID),i=e.mustShutVolveFeatures.filter(n=>r.indexOf(n.attributes.SID)!==-1).map(n=>(n.symbol=G("point",{color:[0,255,255],outlineColor:[255,0,255],outlineWidth:2}),n));e.extentMustShutValveLayer=M(e.view,{id:"extentMustShutValveLayer",title:"二次关阀"}),(s=e.extentMustShutValveLayer)==null||s.removeAll(),(o=e.extentMustShutValveLayer)==null||o.addMany(i)},dataList:[],columns:[{label:"编号",prop:"SID"},{label:"阀门级别",prop:"VALVECLASS"}],pagination:{hide:!0}}),A=S({dataList:[],columns:[],pagination:{hide:!0,refreshData:({page:t,size:r})=>{A.pagination.page=t,A.pagination.limit=r,z()}}}),F=S({columns:[{prop:"yhbh",label:"用户编号"},{prop:"yhxm",label:"用户姓名"},{prop:"yhdz",label:"用户地址"},{prop:"lxdh",label:"联系电话"},{prop:"dxdh",label:"短信电话"},{prop:"ysxz",label:"用水性质"},{prop:"vnum",label:"阀门编号"},{prop:"sbbh",label:"水表编号"}],dataList:[],pagination:{hide:!0}}),K=S({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"选取管线"},fields:[{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},type:"warning",text:()=>a.curOperate==="picking"?"正在选取管线":"点击选取管线",loading:()=>a.curOperate==="picking",disabled:()=>["picking","analysing","extendAnalysing"].indexOf(a.curOperate)!==-1,click:()=>Y()}]},{type:"table",style:{height:"80px"},config:_}]},{fieldset:{desc:"执行分析"},fields:[{type:"btn-group",itemContainerStyle:{marginBottom:0},btns:[{perm:!0,styles:{width:"100%"},loading:()=>a.curOperate==="analysing",text:()=>a.curOperate==="analysing"?"正在分析":"开始分析",disabled:()=>["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(a.curOperate)!==-1||!_.dataList.length,click:()=>ee()}]}]},{fieldset:{desc:"分析结果"},fields:[{label:"影响范围概览",type:"checkbox",field:"showInMap",options:[{label:"地图显示",value:"show"}],onChange:t=>{e.resultLayer&&(e.resultLayer.visible=!!t.length)}},{type:"attr-table",style:{minHeight:"50px"},config:C},{type:"table",label:"必关阀",style:{height:"250px"},config:L},{type:"btn-group",itemContainerStyle:{marginBottom:"5px",marginTop:"15px"},btns:[{perm:!0,styles:{width:"100%"},disabled:()=>{var t;return["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(a.curOperate)!==-1||!((t=C.columns)!=null&&t.length)},loading:()=>a.curOperate==="detailing",text:()=>a.curOperate==="detailing"?"正在查询...":"查看详细结果",click:()=>z()}]},{type:"btn-group",itemContainerStyle:{marginBottom:"0"},btns:[{perm:!0,styles:{width:"100%"},text:()=>a.curOperate==="userDetailing"?"正在查询":"查看受影响用户",loading:()=>a.curOperate==="userDetailing",disabled:()=>["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(a.curOperate)!==-1,click:()=>re()}]}]},{fieldset:{desc:"必关阀拓展分析"},fields:[{type:"table",style:{height:"250px"},label:"选择必关阀",config:O},{type:"btn-group",itemContainerStyle:{marginBottom:"5px",marginTop:"15px"},btns:[{perm:!0,styles:{width:"100%"},loading:()=>a.curOperate==="extendAnalysing",text:()=>a.curOperate==="extendAnalysing"?"正在分析":"二次关阀分析",disabled:()=>{var t;return["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(a.curOperate)!==-1||!((t=O.selectList)!=null&&t.length)},click:()=>te()}]},{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},type:"danger",disabled:()=>["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(a.curOperate)!==-1,text:"清除所有",click:()=>ie()}]}]}],defaultValue:{showInMap:["show"]}}),Y=()=>{e.view&&(N("crosshair"),a.curOperate="picking",e.markLayer=M(e.view,{id:"burst-analys",title:"爆管标注"}),e.mapClick=e.view.on("click",async t=>{var r;(r=e.markLayer)==null||r.removeAll(),await Z(t)}))},Z=async t=>{var r,i,s,o,n,m,d,p,u,c;if(e.view){try{e.queryParams.layerIds=j(e.view,!0),e.queryParams.geometry=t.mapPoint,e.queryParams.mapExtent=e.view.extent;const h=await Ie(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,e.queryParams);(r=e.view)==null||r.graphics.removeAll();const l=(i=h.results)==null?void 0:i.filter(f=>{var w,x;return((x=(w=f.feature)==null?void 0:w.geometry)==null?void 0:x.type)==="polyline"});if(!(l!=null&&l.length)){b.warning("没有查询到管线"),a.curOperate="";return}e.identifyResult=l[0],e.burstPoint=l&&ve((o=(s=l[0])==null?void 0:s.feature)==null?void 0:o.geometry,t.mapPoint);const y=e.burstPoint&&we(e.burstPoint.x,e.burstPoint.y,{picUrl:Ce("poi_burst.png"),spatialReference:(n=e.view)==null?void 0:n.spatialReference,yOffset:8}),v=l[0].feature;v&&(v.symbol=G(v.geometry.type)),l.length&&((m=e.markLayer)==null||m.add(v)),(d=e.markLayer)==null||d.add(y);const g=l.map(f=>{var w,x;return{layerName:f.layerName,layerId:f.layerId,value:(w=f.feature.attributes)==null?void 0:w.OBJECTID,attributes:(x=f.feature)==null?void 0:x.attributes}})||[];_.dataList=g.length&&[g[0]]||[],xe(e.view,v),N(""),(p=e.mapClick)!=null&&p.remove&&((u=e.mapClick)==null||u.remove())}catch{(c=e.view)==null||c.graphics.removeAll(),a.curOperate=""}a.curOperate="picked"}},ee=async()=>{var t,r,i,s,o,n,m,d;if(e.view){a.curOperate="analysing";try{e.resultLayer&&((t=e.view)==null||t.map.remove(e.resultLayer)),e.extendResultLayer&&((r=e.view)==null||r.map.remove(e.extendResultLayer));const u=(s=(i=(await me(_.dataList[0].layerId)).data)==null?void 0:i.result)==null?void 0:s.rows,c=(u==null?void 0:u.length)&&u[0].layerdbname,h=(o=e.identifyResult)==null?void 0:o.feature.attributes.OBJECTID,l=await Se(c,h);if(await l.waitForJobCompletion(),l.jobStatus==="job-succeeded"){e.jobid=l.jobId,e.resultLayer=await l.fetchResultMapImageLayer(l.jobId),a.detailUrl=e.resultLayer.url,e.resultLayer.title="爆管分析结果";const y=$(e.view);(n=e.view)==null||n.map.add(e.resultLayer,y);const g=(await l.fetchResultData("summary")).value;(g==null?void 0:g.code)!==1e4?b.error(g.error):(e.resultSummary=((m=g==null?void 0:g.result)==null?void 0:m.summary)||[],J(),a.tabs=e.resultSummary.layersummary.map(f=>({label:f.layername,name:f.layername,data:[]})),a.mustshutOids.length=0,await P(a.tabs,0),await q(),e.mustShutValveLayer=M(e.view,{id:"mustShutValveLayer",title:"必关阀"}),(d=e.mustShutValveLayer)==null||d.addMany(e.mustShutVolveFeatures))}else l.jobStatus==="job-cancelled"?b.info("已取消分析"):l.jobStatus==="job-cancelling"?b.info("任务正在取消"):l.jobStatus==="job-failed"&&b.info("分析失败，请联系管理员")}catch{b.info("分析失败，请联系管理员"),a.curOperate="picked"}a.curOperate="analysed"}},P=async(t,r)=>{if(r<t.length){const i=t[r];i.data=await U(i.name),r<t.length-1&&await P(t,++r)}},U=async t=>{var r,i,s,o,n,m,d;try{const p=(s=(i=(r=e.resultSummary)==null?void 0:r.layersummary)==null?void 0:i.find(y=>y.layername===t))==null?void 0:s.geometrytype,u=p==="esriGeometryPolyline"?1:p==="esriGeometryPoint"?0:-1,c=await T(((a.curOperate==="extendAnalysing"?(o=e.extendResultLayer)==null?void 0:o.url:(n=e.resultLayer)==null?void 0:n.url)||"")+"/"+u,R({where:"layername='"+t+"'",outFields:p==="esriGeometryPoint"?["sourceoid","mustshut"]:["sourceoid"],returnGeometry:!1})),h=(m=c.features)==null?void 0:m.filter(y=>y.attributes.mustshut===1).map(y=>y.attributes.sourceoid);a.curOperate==="extendAnalysing"?a.extentMustShutOids=a.extentMustShutOids.concat(...h):a.mustshutOids=a.mustshutOids.concat(...h);let l=(d=c.features)==null?void 0:d.map(y=>y.attributes.sourceoid);return l===null&&(l=await U(t)),l}catch{return[]}},q=async()=>{var t,r,i,s,o,n;if(e.view){a.curOperate==="extendAnalysing"&&((t=e.extentMustShutValveLayer)==null||t.removeAll()),a.curOperate==="analysing"&&((r=e.mustShutValveLayer)==null||r.removeAll());try{L.loading=!0,O.loading=!0;const m=[];if(a.mustshutOids.length){const d=j(e.view,void 0,void 0,"阀门"),p=await _e("阀门"),u=await T(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+d[0],R({objectIds:a.mustshutOids,outFields:((o=(s=(i=p.data)==null?void 0:i.result)==null?void 0:s.rows)==null?void 0:o.filter(c=>c.visible).map(c=>c.name))||[],returnGeometry:!0}));(n=u.features)==null||n.map(c=>{m.push({...c.attributes||{},layerId:d[0]}),c.symbol=G("point",{color:[0,255,255],outlineColor:[255,0,255],outlineWidth:2})}),e.mustShutVolveFeatures=u.features}else e.mustShutVolveFeatures=[];L.dataList=m,O.dataList=m,L.selectList=m}catch(m){b.error("必关阀查询失败"),console.dir(m)}L.loading=!1,O.loading=!1}},J=()=>{var d,p,u,c,h,l,y,v,g,f,w;const t=e.resultSummary;if((d=t==null?void 0:t.layersummary)!=null&&d.length){const x={},W=[];t.layersummary.forEach(I=>{x[I.layerdbname]=I.geometrytype==="esriGeometryPoint"?I.count+"个":I.length+"米",W.push([{label:I.layername,prop:I.layerdbname}])}),C.data=x,C.columns=W}let r=t.xmin||((u=(p=e.view)==null?void 0:p.extent)==null?void 0:u.xmin),i=t.xmax||((h=(c=e.view)==null?void 0:c.extent)==null?void 0:h.xmax),s=t.ymin||((y=(l=e.view)==null?void 0:l.extent)==null?void 0:y.ymin),o=t.ymax||((g=(v=e.view)==null?void 0:v.extent)==null?void 0:g.ymax);const n=i-r,m=o-s;r-=n/2,i+=n/2,s-=m/2,o+=m/2,(w=e.view)==null||w.goTo(new he({xmin:r,ymin:s,xmax:i,ymax:o,spatialReference:(f=e.view)==null?void 0:f.spatialReference}))},z=async()=>{var t;a.curOperate="detailing",(t=B.value)==null||t.refreshDetail(a.tabs)},te=async()=>{var t,r;a.curOperate="extendAnalysing";try{e.resultLayer&&((t=e.view)==null||t.map.remove(e.resultLayer));const i=((r=L.selectList)==null?void 0:r.map(s=>s.OBJECTID))||[];await ae("Valve",i),a.curOperate="extendAnalysed"}catch{a.curOperate="analysed"}},ae=async(t,r)=>{var s,o;const i=await Le({bysource:!0,usertoken:pe().gToken,valves:t+":"+r.join(",")});if(await i.waitForJobCompletion(),i.jobStatus==="job-succeeded"){e.jobid=i.jobId;const n=await i.fetchResultMapImageLayer(i.jobId);e.extendResultLayer=n,e.extendResultLayer.title="二次关阀分析结果";const m=$(e.view);(s=e.view)==null||s.map.add(e.extendResultLayer,m);const p=(await i.fetchResultData("summary")).value;(p==null?void 0:p.code)!==1e4?b.error(p.error):(e.resultSummary=(o=p==null?void 0:p.result)==null?void 0:o.summary,J()),a.tabs=e.resultSummary.layersummary.map(u=>({label:u.layername,name:u.layername,data:[]})),a.extentMustShutOids.length=0,await P(a.tabs,0),await q()}else i.jobStatus==="job-cancelled"?b.info("已取消分析"):i.jobStatus==="job-cancelling"?b.info("任务正在取消"):i.jobStatus==="job-failed"&&(b.info("分析失败，请联系管理员"),a.curOperate="analysed")},re=async()=>{var t;a.curOperate="userDetailing";try{e.deviceids=[],await Q(0);const r=await ke(e.deviceids);F.dataList=r.data.Data.rows,k}catch{b.error("获取用户信息失败"),a.curOperate="analysed";return}a.curOperate="viewingUserDetail",(t=E.value)==null||t.Open()},Q=async t=>{var i;const r=e.devicelayers[t];if(!(!e.view||!r))try{const s=await j(e.view,void 0,void 0,r),o=(i=a.tabs.find(m=>m.name===r))==null?void 0:i.data;(await T(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+s,R({outFields:["SID"],objectIds:o,returnGeometry:!1}))).features.map(m=>{e.deviceids.push(m.attributes.SID)}),t<e.devicelayers.length-1&&await Q(++t)}catch(s){console.dir(s)}},ie=()=>{se(),A.dataList=[],C.data=[],O.dataList=[],L.dataList=[],_.dataList=[],F.dataList=[]},se=()=>{var t,r,i,s,o,n;N(""),e.resultLayer&&((t=e.view)==null||t.map.remove(e.resultLayer)),e.extendResultLayer&&((r=e.view)==null||r.map.remove(e.extendResultLayer)),e.markLayer&&((i=e.view)==null||i.map.remove(e.markLayer)),e.mustShutValveLayer&&((s=e.view)==null||s.map.remove(e.mustShutValveLayer)),e.extentMustShutValveLayer&&((o=e.view)==null||o.map.remove(e.extentMustShutValveLayer)),(n=e.mapClick)!=null&&n.remove&&e.mapClick.remove()},oe=t=>{e.view=t};return ue(()=>{var t,r;(t=e.markLayer)==null||t.removeAll(),(r=e.mapClick)==null||r.remove()}),(t,r)=>{const i=ge,s=fe,o=ne;return ce(),de(De,{ref_key:"refMap",ref:B,title:"爆管分析","full-content":!0,onMapLoaded:oe,onDetailRefreshed:r[0]||(r[0]=n=>D(a).curOperate="viewingDetail")},{default:H(()=>[V(i,{ref_key:"refForm",ref:X,config:D(K)},null,8,["config"]),V(o,{ref_key:"refPanel_User",ref:E,"custom-class":"gis-detail-panel",draggable:!1,"max-min":!0,title:"受影响用户详情","before-close":()=>D(a).curOperate="analysed"},{default:H(()=>[ye("div",Ae,[V(s,{config:D(F)},null,8,["config"])])]),_:1},8,["before-close"])]),_:1},512)}}}),Ea=be(Fe,[["__scopeId","data-v-577db340"]]);export{Ea as default};
