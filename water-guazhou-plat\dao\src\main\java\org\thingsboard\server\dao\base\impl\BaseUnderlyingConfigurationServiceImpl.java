package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseUnderlyingConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseUnderlyingConfiguration;
import org.thingsboard.server.dao.sql.base.BaseUnderlyingConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseUnderlyingConfigurationPageRequest;

/**
 * 平台管理-基础配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class BaseUnderlyingConfigurationServiceImpl implements IBaseUnderlyingConfigurationService {

    @Autowired
    private BaseUnderlyingConfigurationMapper baseUnderlyingConfigurationMapper;

    /**
     * 查询平台管理-基础配置
     *
     * @param id 平台管理-基础配置主键
     * @return 平台管理-基础配置
     */
    @Override
    public BaseUnderlyingConfiguration selectBaseUnderlyingConfigurationById(String id) {
        return baseUnderlyingConfigurationMapper.selectBaseUnderlyingConfigurationById(id);
    }

    /**
     * 查询平台管理-基础配置列表
     *
     * @param baseUnderlyingConfiguration 平台管理-基础配置
     * @return 平台管理-基础配置
     */
    @Override
    public IPage<BaseUnderlyingConfiguration> selectBaseUnderlyingConfigurationList(BaseUnderlyingConfigurationPageRequest baseUnderlyingConfiguration) {
        return baseUnderlyingConfigurationMapper.selectBaseUnderlyingConfigurationList(baseUnderlyingConfiguration);
    }

    /**
     * 新增平台管理-基础配置
     *
     * @param baseUnderlyingConfiguration 平台管理-基础配置
     * @return 结果
     */
    @Override
    public int insertBaseUnderlyingConfiguration(BaseUnderlyingConfiguration baseUnderlyingConfiguration) {
        baseUnderlyingConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseUnderlyingConfigurationMapper.insertBaseUnderlyingConfiguration(baseUnderlyingConfiguration);
    }

    /**
     * 修改平台管理-基础配置
     *
     * @param baseUnderlyingConfiguration 平台管理-基础配置
     * @return 结果
     */
    @Override
    public int updateBaseUnderlyingConfiguration(BaseUnderlyingConfiguration baseUnderlyingConfiguration) {
        return baseUnderlyingConfigurationMapper.updateBaseUnderlyingConfiguration(baseUnderlyingConfiguration);
    }

    /**
     * 批量删除平台管理-基础配置
     *
     * @param ids 需要删除的平台管理-基础配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseUnderlyingConfigurationByIds(List<String> ids) {
        return baseUnderlyingConfigurationMapper.deleteBaseUnderlyingConfigurationByIds(ids);
    }

    /**
     * 删除平台管理-基础配置信息
     *
     * @param id 平台管理-基础配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseUnderlyingConfigurationById(String id) {
        return baseUnderlyingConfigurationMapper.deleteBaseUnderlyingConfigurationById(id);
    }
}
