package org.thingsboard.server.dao.menu;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.model.sql.MenuButtonEntity;

import java.util.List;

public interface MenuButtonService {
    List<MenuButtonEntity> findList(String menuId);

    MenuButtonEntity save(MenuButtonEntity buttonEntity);

    void deleteBatch(List<String> ids);

    void setMenuButtonList(String roleId, List<String> menuButtonIdList);

    List<MenuButtonEntity> getCurrentUserMenuButtonList(UserId id, Authority authority);

    Object tree(User currentUser);

    List<MenuButtonEntity> getRoleButtonList(String roleId);
}
