package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeWaterFactoryMonitorPoint;

import java.util.List;

/**
 * 水厂监测点
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-19
 */
@Mapper
public interface PipeWaterFactoryMonitorPointMapper extends BaseMapper<PipeWaterFactoryMonitorPoint> {

    @Select("select a.*, b.name as deviceName from tb_pipe_water_factory_monitor_point a left join device b on a.device_id = b.id " +
            "where a.factory_name like '%' || #{name} || '%' and a.direction like '%' || #{direction} || '%' and a.tenant_id = #{tenantId} " +
            "order by a.order_num, a.create_time desc")
    List<PipeWaterFactoryMonitorPoint> getList(@Param("name") String name, @Param("direction") String direction, @Param("tenantId") String tenantId);
}
