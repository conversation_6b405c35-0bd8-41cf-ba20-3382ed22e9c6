<!-- 简易树 -->
<template>
  <div>
    <el-input
      v-if="props.config.search"
      v-model="state.search"
      style="width: 100%"
      placeholder="请输入"
      class="input_search"
      :size="(config.search as any)?.size ?? 'small'"
    >
      <template #append>
        <el-button
          :icon="Search"
          :size="(config.search as any)?.size ?? 'small'"
          @click="handSearch"
        />
      </template>
    </el-input>
    <el-tree
      ref="treeRef"
      style="width: 100%; height: calc(100% - 50px); overflow-y: auto"
      :data="state.data"
      node-key="id"
      default-expand-all
      :expand-on-click-node="false"
      :props="state.defaultProps"
      :highlight-current="config.highlight ?? true"
      :filter-node-method="filterNode"
      :lazy="config.lazy ?? false"
      :load="config.load as any"
      @node-click="handClick"
    >
      <template #default="{ node, data }">
        <div :class="{ 'custom-tree-node': true }">
          <div class="label">
            <template v-if="node.data.icon ?? config.icon ?? ''">
              <Icon
                :icon="node.data.icon ?? config.icon ?? ''"
                style="font-size: 20px"
              ></Icon>
            </template>
            <span> {{ node.label }}</span>
          </div>
          <div v-if="props.config.btns" class="btns">
            <template v-for="(item, index) in props.config.btns" :key="index">
              <el-button
                :type="item.type as any"
                link
                :size="item.size ?? 'small'"
                @click.stop="item.click && item.click(data)"
              >
                {{ item.text }}
              </el-button>
            </template>
          </div>
        </div>
      </template>
    </el-tree>
  </div>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue';
import { Icon } from '@iconify/vue';
import { ElTree } from 'element-plus';

interface Tree {
  [key: string]: any;
}

const treeRef = ref<InstanceType<typeof ElTree>>();

const state = reactive<{
  search: string;
  data: any[];
  defaultProps: any;
  currentProject: any | any[];
}>({
  search: '',
  data: [],
  defaultProps: {
    children: 'children',
    label: 'name',
    disabled: 'disabled'
  },
  currentProject: ''
});

const props = defineProps<{
  config: simpleTree;
}>();

const handClick = (params: any, node?: any, e?: any) => {
  if (props.config.click) {
    props.config.click(params, node, e);
  }
};

const handSearch = () => {
  treeRef.value!.filter(state.search);
};

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data[state.defaultProps.label].includes(value);
};

watch(
  () => props.config.data,
  () => {
    state.data = props.config.data;
  }
);

watch(
  () => props.config.currentProject,
  () => {
    state.currentProject = props.config.currentProject.id;
    treeRef.value?.setCurrentKey(props.config.currentProject.id);
  }
);

onMounted(() => {
  state.data = props.config.data;

  if (props.config.currentProject) {
    setTimeout(() => {
      state.currentProject = props.config.currentProject.id;
      treeRef.value?.setCurrentKey(props.config.currentProject.id);
    }, 0);
  }
});
</script>

<style lang="scss" scoped>
.custom-tree-node {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .label {
    display: flex;
    align-items: center;
  }

  .btns {
    display: flex;
    align-items: center;
  }
}

.input_search {
  margin-bottom: 10px;
}
</style>
