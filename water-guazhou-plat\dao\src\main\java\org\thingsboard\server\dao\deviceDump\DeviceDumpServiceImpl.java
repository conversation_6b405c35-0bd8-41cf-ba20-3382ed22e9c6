package org.thingsboard.server.dao.deviceDump;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.deviceDump.DeviceDump;
import org.thingsboard.server.dao.sql.deviceDump.DeviceDumpMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.deviceDump.DeviceDumpPageRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceDump.DeviceDumpSaveRequest;

@Service
public class DeviceDumpServiceImpl implements DeviceDumpService {
    @Autowired
    private DeviceDumpMapper mapper;

    @Autowired
    private DeviceDumpDetailService service;


    @Override
    public IPage<DeviceDump> findAllConditional(DeviceDumpPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public DeviceDump save(DeviceDumpSaveRequest entity) {
        DeviceDump result = QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
        service.saveAll(entity.getItems(result.getId()));
        // service.removeAll(entity.getRemove());
        return result;
    }

    @Override
    public boolean update(DeviceDump entity) {
        return mapper.update(entity);
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        service.removeAll(mapper.getChildrenId(id));
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean dump(String id) {
        return mapper.dump(id) > 0;
    }

    @Override
    public boolean isUserHandleUser(String id, String userId) {
        return mapper.isUserHandleUser(id, userId);
    }

}
