import Draw from '@arcgis/core/views/draw/Draw'
import SketchViewModel from '@arcgis/core/widgets/Sketch/SketchViewModel'
import proj4 from 'proj4'
import Graphic from '@arcgis/core/Graphic'
import Polyline from '@arcgis/core/geometry/Polyline'
import SpatialReference from '@arcgis/core/geometry/SpatialReference'
import Point from '@arcgis/core/geometry/Point'
import { setSymbol } from './FeatureHelper'

/**
 * 初始化绘画笔
 * @param view
 * @returns
 */
export const initDrawer = (view: __esri.MapView) => {
  return new Draw({ view }) as __esri.Draw
}

/**
 * 初始化画笔
 * @param view
 * @param graphicsLayer
 * @param options
 * @returns
 */
export const initSketch = (
  view?: __esri.MapView,
  graphicsLayer?: __esri.GraphicsLayer,
  callBack?: (graphic: __esri.Graphic) => any
) => {
  const sketch = new SketchViewModel({
    view,
    layer: graphicsLayer,
    polygonSymbol: setSymbol('polygon') as any,
    polylineSymbol: setSymbol('polyline') as any,
    pointSymbol: setSymbol('point') as any
  })
  const createHandler = sketch?.on('create', result => {
    if (result.state === 'complete') {
      callBack && callBack(result.graphic)
    }
  })
  const updateHandler = sketch?.on('update', result => {
    if (result.state === 'complete') {
      callBack && callBack(result.graphics[0])
    }
  })
  return {
    sketch,
    createHandler,
    updateHandler
  }
}
/**
 *  设置地图dom的cursor
 * @param type
 */
export const setMapCursor = (type: string, elId?: string) => {
  const mapDiv = document.getElementById(elId || 'viewDiv')
  mapDiv && (mapDiv.style.cursor = type)
}

/**
 * 把rbga字符串转换成数组
 * @param rgba 要转换的值
 * @param defaultVals 默认值
 * @returns
 */
export const RgbaToArr = (rgba?: string, defaultVals?: number[]): number[] | undefined => {
  if (!rgba) return defaultVals
  const val: any = rgba?.match(/(\d(\.\d+)?)+/g)
  return val || defaultVals
}

export const calcStringWidth = (str?: string) => {
  let strlen = 0
  if (!str?.length) return 0
  for (let i = 0; i < str.length; i++) {
    // 如果是汉字，则字符串长度加2
    if (str.charCodeAt(i) > 255) strlen += 2
    else strlen++
  }

  return strlen
}
/**
 * 将3857的xy坐标转换成4548坐标系
 * @param xy 3857的xy坐标： [13114097.279, 3583831.260]
 * @returns 4548坐标： [577086, 3388051]
 */
export const to4548 = (xy: number[]) => {
  try {
    proj4.defs('EPSG:4548', '+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs')
    const coords = proj4(new proj4.Proj('EPSG:3857'), new proj4.Proj('EPSG:4548'), xy)
    return coords
  } catch (error) {
    console.log(error)
    return undefined
  }
}
export const from4548 = (xy: number[]) => {
  try {
    proj4.defs('EPSG:4548', '+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs')
    const coords = proj4(new proj4.Proj('EPSG:4548'), new proj4.Proj('EPSG:3857'), xy)
    return coords
  } catch (error) {
    console.log(error)
    return undefined
  }
}
export const generate4548Graphic = (graphic: __esri.Graphic) => {
  if (graphic.geometry.spatialReference.wkid === 4548) {
    return graphic
  }
  if (graphic?.geometry?.type === 'polyline') {
    const geometry = graphic.geometry as __esri.Polyline
    const submitCoords = geometry.paths[0].map(to4548)
    return new Graphic({
      geometry: new Polyline({
        paths: [submitCoords],
        spatialReference: new SpatialReference({ wkid: 4548 })
      }),
      attributes: graphic.attributes,
      symbol: graphic.symbol
    })
  }
  if (graphic?.geometry?.type === 'point') {
    const geometry = graphic.geometry as __esri.Point
    const submitCoords = to4548([geometry.x, geometry.y])
    return new Graphic({
      geometry: new Point({
        x: submitCoords[0],
        y: submitCoords[1],
        spatialReference: new SpatialReference({ wkid: 4548 })
      }),
      attributes: {
        ...(graphic.attributes || {}),
        X: submitCoords[0],
        Y: submitCoords[1]
      },
      symbol: graphic.symbol
    })
  }
  return graphic
}
export const from4548Graphic = (graphic: __esri.Graphic) => {
  if (graphic.geometry.spatialReference.wkid === 4548) {
    if (graphic?.geometry?.type === 'polyline') {
      const geometry = graphic.geometry as __esri.Polyline
      const submitCoords = geometry.paths[0].map(from4548)
      return new Graphic({
        geometry: new Polyline({
          paths: [submitCoords],
          spatialReference: new SpatialReference({ wkid: 3857 })
        }),
        attributes: graphic.attributes,
        symbol: graphic.symbol
      })
    }
    if (graphic?.geometry?.type === 'point') {
      const geometry = graphic.geometry as __esri.Point
      const submitCoords = from4548([geometry.x, geometry.y])
      return new Graphic({
        geometry: new Point({
          x: submitCoords[0],
          y: submitCoords[1],
          spatialReference: new SpatialReference({ wkid: 3857 })
        }),
        attributes: {
          ...(graphic.attributes || {}),
          X: submitCoords[0],
          Y: submitCoords[1]
        },
        symbol: graphic.symbol
      })
    }
  }

  return graphic
}
