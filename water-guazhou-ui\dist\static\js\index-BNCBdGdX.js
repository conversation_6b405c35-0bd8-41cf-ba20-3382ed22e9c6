import{_ as Z}from"./TreeBox-DDD2iwoR.js";import{_ as ee}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as te}from"./CardTable-rdWOL4_6.js";import{_ as ae}from"./CardSearch-CB_HNR-Q.js";import{_ as re}from"./index-BJ-QPYom.js";import{b as se,a as ie,i as oe,d as ne}from"./index-CaaU9niG.js";import{d as le,M as ce,c as N,r as m,u as _,s as de,a0 as j,ar as ue,b as i,a8 as P,a9 as pe,S as h,a as O,x as z,e1 as fe,D as b,bT as ge,eA as me,ck as he,eB as ye,eC as _e,eD as be,eE as Se,eF as Te,o as xe,g as Ue,h as Ie,F as B,q as S,C as Ne}from"./index-r0dFAfgr.js";import{I as De}from"./ImportButton-BXPDxx92.js";import{a as Ee,b as Pe}from"./formValidate-U0WTqY4Y.js";import{E as Me,d as ve}from"./index-Bo22WWST.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                                                                     */const ke=le({__name:"index",setup(Ae){const{$btnPerms:p}=ce(),M=N(),g=N(),D=N(),U=N(),E=m({visible:!1,userId:"",close:()=>{E.visible=!1}}),v=m({defaultParams:{status:"1"},labelWidth:40,filters:[{label:"搜索",field:"name",type:"input"},{label:"状态",field:"status",type:"select",options:[{label:"启用",value:"1"},{label:"停用",value:"0"}]},{label:"角色",field:"roleId",type:"select",options:[]}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{c.selectList=[],u()}},{text:"添加用户",disabled:()=>_().roles[0]==="SYS_ADMIN",click:()=>w(),perm:p("UserManageAdd")||_().roles[0]==="SYS_ADMIN"},{text:"权限赋予",click:()=>A(),disabled:()=>{var e;return!((e=c.selectList)!=null&&e.length)},perm:p("UserManageAuth")},{text:"批量删除",type:"danger",disabled:()=>{var e;return!((e=c.selectList)!=null&&e.length)},click:()=>k(),perm:p("UserManageMultiDelete")},{text:"下载模板",perm:p("UserManageDownloadTemp"),click:()=>X()},{text:"导出",type:"warning",perm:p("UserManageExport"),click:()=>Q()},{text:"导入",perm:p("UserManageImport"),click:(e,t,a)=>G(e,t,a),component:de(De)}]}]}),y=m({title:"组织架构",data:[],currentProject:{},isFilterTree:!0,treeNodeHandleClick:e=>{y.currentProject=e,j().SET_selectedProject(e),u()}}),T=m({organizeData:[],getorganize:()=>{ue(2).then(t=>{var r;const a=((r=t.data)==null?void 0:r.data)||[];T.organizeData=a||[],y.data=a||[],y.currentProject=a[0],u()})},isorganize:e=>{if(e===void 0)return!1;const t=K(T.organizeData,e);return t.layer&&t.layer!==2?(i.warning("请选择部门"),!1):t.data&&t.data.layer&&t.data.layer!==2?(i.warning("请选择部门"),!1):!0}}),c=m({indexVisible:!0,handleSelectChange:e=>{c.selectList=e||[]},columns:[{prop:"firstName",label:"姓名"},{prop:"departmentName",label:"部门"},{prop:"roleName",label:"角色"},{prop:"phone",label:"手机号"}],dataList:[],pagination:{refreshData:({page:e,size:t})=>{c.pagination.page=e,c.pagination.limit=t,u()}},operations:[{text:"企业赋予",isTextBtn:!0,icon:"iconfont icon-guanli",disabled:e=>H(e),perm:!1,click:e=>$(e)},{text:"角色赋予",isTextBtn:!0,perm:p("UserManageAuthRole"),icon:"iconfont icon-jiaose",click:e=>q(e)},{text:"权限范围",isTextBtn:!0,perm:p("UserManagePermArea"),icon:"iconfont icon-quanxian",click:e=>A(e)},{text:e=>e.status?"停用":"启用",isTextBtn:!0,icon:e=>e.status?"iconfont icon-qitingcaozuo-tingzhi":"iconfont icon-jiechuhezuoguanxi",perm:p("UserManageStop"),click:e=>{if(e.status){W(e);return}Y(e)}},{text:"重置密码",isTextBtn:!0,icon:"iconfont icon-jiechuhezuoguanxi",perm:p("UserManageRestPass"),click:e=>V(e)},{text:"编辑",isTextBtn:!0,icon:"iconfont icon-bianji",perm:p("UserManageEdit"),click:e=>w(e)},{text:"删除",isTextBtn:!0,type:"danger",perm:p("UserManageDelete"),icon:"iconfont icon-shanchu1",click:e=>k(e)}],operationWidth:"540px"}),f=m({dialogWidth:500,labelWidth:120,title:"添加用户",group:[{fields:[{type:"input",label:"姓名",field:"firstName",rules:[{required:!0,message:"请输入用户姓名"}]},{type:"password",label:"账户密码",field:"password",hidden:P(()=>f.title==="编辑用户"),rules:[{required:!0,message:"请输入密码"}]},{type:"password",label:"确认密码",field:"password1",hidden:P(()=>f.title==="编辑用户"),rules:[{required:!0,message:"请确认密码"}]},{type:"input",label:"电话号码",field:"phone",rules:[{required:!0,validator:Ee,trigger:"blur"}]},{type:"input",label:"邮箱",field:"email",rules:[{required:!0,validator:Pe,trigger:"blur"}]},{type:"select-tree",label:"所属部门",field:"departmentId",defaultExpandAll:!0,checkStrictly:!0,options:P(()=>pe(T.organizeData)),onChange:e=>{var t,a,r;return T.isorganize(e)||(t=g.value)!=null&&t.refForm&&((r=(a=g.value)==null?void 0:a.refForm)!=null&&r.dataForm)&&(g.value.refForm.dataForm.departmentId=""),e},rules:[{required:!0,message:"请选择所属部门"}]},{hidden:_().roles[0]!=="SYS_ADMIN",type:"select",label:"角色配置",field:"authority",onChange:e=>{var r,n,s;const t=e==="TENANT_SUPPORT"?{jszc:!1}:{sctg:!1},a=((n=(r=g.value)==null?void 0:r.refForm)==null?void 0:n.dataForm)||{};delete a.jszc,delete a.sctg,f.defaultValue={...a,...t},(s=g.value)==null||s.resetForm()},options:[{label:"技术支持",value:"TENANT_SUPPORT"},{label:"市场推广",value:"TENANT_PROMOTE"}],rules:[{required:!0,message:"请选择用户角色配置"}]},{hidden:!0,handleHidden:(e,t,a)=>{a.hidden=e.authority!=="TENANT_SUPPORT"||_().roles[0]!=="SYS_ADMIN"},type:"radio",label:"固件升级权限",field:"jszc",options:[{label:"开启",value:!0},{label:"关闭",value:!1}],rules:[{required:!0,message:"请选择"}]},{hidden:!0,handleHidden:(e,t,a)=>{a.hidden=e.authority!=="TENANT_PROMOTE"||_().roles[0]!=="SYS_ADMIN"},type:"radio",label:"是否代理商",field:"sctg",options:[{label:"是",value:!0},{label:"否",value:!1}],rules:[{required:!0,message:"请选择"}]}]}],submit:e=>{if(f.title==="编辑用户"){h("确定提交？","提示信息").then(async()=>{var t;f.submitting=!0;try{const a={...e,customerId:{entityType:"CUSTOMER",id:"b49ef6e0-8100-11e8-9cbe-77a34f885729"},authority:"CUSTOMER_USER"};delete a.password,delete a.password1,await O(a),i.success("操作成功"),(t=g.value)==null||t.closeDialog(),u()}catch(a){i.error(a)}f.submitting=!1}).catch(()=>{});return}if(e.password!==e.password1){z.warning("密码不一致，请确认密码");return}h("确定提交？","提示信息").then(async()=>{var t;f.submitting=!0;try{const a={...e,customerId:{entityType:"CUSTOMER",id:"b49ef6e0-8100-11e8-9cbe-77a34f885729"},authority:"CUSTOMER_USER"};await O(a),i.success("操作成功"),(t=g.value)==null||t.closeDialog(),u()}catch(a){i.error(a)}f.submitting=!1}).catch(()=>{})}}),x=m({dialogWidth:500,labelWidth:60,title:"角色赋予",group:[{fields:[{type:"select-tree",label:"角色",placeholder:"请选择角色",field:"roleId",multiple:!0,options:[]}]}],submit:e=>{h("确定提交？","提示信息").then(async()=>{var t;x.submitting=!0;try{const a=e.roleId.map(r=>fe(r));e.roleId=a.join(","),await se(e),i.success("提交成功"),u(),(t=D.value)==null||t.closeDialog()}catch{i.error("操作失败")}x.submitting=!1}).catch(()=>{})}}),q=async e=>{var a;const t=await ie(e.id.id);x.defaultValue={userId:e.id.id,roleId:t.data&&b(t.data)},(a=D.value)==null||a.openDialog()},I=m({dialogWidth:500,labelWidth:60,title:"权限范围",group:[{fields:[{type:"select-tree",label:"项目",field:"projectIds",showCheckbox:!0,options:j().projectList,checkStrictly:!0,multiple:!0}]}],submit:e=>{h("确定提交？","提示信息").then(async()=>{var t;I.submitting=!0;try{await oe(e),i.success("提交成功"),u(),(t=U.value)==null||t.closeDialog()}catch{i.error("操作失败")}I.submitting=!1}).catch(()=>{})}}),u=async()=>{var n,s,l,d,R,C,F;const e=((n=M.value)==null?void 0:n.queryParams)||{},t={page:c.pagination.page||1,size:c.pagination.limit||20,authority:"CUSTOMER_USER",pid:((s=y.currentProject)==null?void 0:s.id)||"",...e||{}},a=await ge(t),r=(R=(d=(l=a.data)==null?void 0:l.data)==null?void 0:d.data)==null?void 0:R.map(o=>(o.additionalInfo&&typeof o.additionalInfo=="string"&&(o.additionalInfo=JSON.parse(o.additionalInfo)),o.authority==="TENANT_ADMIN"?o.authority="企业管理人员":o.authority==="TENANT_SYS"?o.authority="企业配置人员":o.authority==="CUSTOMER_USER"?o.authority="企业用户":o.authority==="SYS_ADMIN"?o.authority="超级管理员":o.authority==="TENANT_SUPPORT"?o.authority="技术支持":o.authority==="TENANT_PROMOTE"&&(o.authority="市场推广"),o));c.dataList=[...r],c.pagination.total=((F=(C=a.data)==null?void 0:C.data)==null?void 0:F.total)||0},L=async()=>{var r,n,s;const e=await ne({size:9999999,page:1}),t=(r=v.filters)==null?void 0:r.find(l=>l.field==="roleId");t&&(t.options=(s=(n=e.data)==null?void 0:n.data)==null?void 0:s.map(l=>({label:l.name,value:b(l.id.id)})));const a=x.group[0].fields[0];a&&(a.options=t.options)},k=e=>{h("确定删除?","删除提示").then(async()=>{var a;let t=[];if(e?t=[e.id.id]:t=((a=c.selectList)==null?void 0:a.map(r=>r.id.id))||[],!t.length){i.warning("请选择要删除的用户");return}try{await me(t),i.success("删除成功"),u()}catch{i.error("删除失败")}})},A=async e=>{var r,n,s,l;const t=e&&[e.id.id]||((r=c.selectList)==null?void 0:r.map(d=>d.id.id));if(!t.length){i.warning("请先选择用户");return}const a=await he("USER",t[0]);console.log(a.data),I.defaultValue={userIds:t,projectIds:((n=a.data)==null?void 0:n.map(d=>d.id))||[]},(s=U.value)==null||s.resetForm(),(l=U.value)==null||l.openDialog()},w=e=>{var t,a;if(f.title=e?"编辑用户":"添加用户",!e&&y.currentProject.layer!==2){z.warning("请选中部门");return}f.defaultValue={...e||{}},(t=g.value)==null||t.resetForm(),(a=g.value)==null||a.openDialog()},W=e=>{h("确定停用该用户?","提示信息").then(async()=>{try{const t=b(e.id.id);await ye(t),i.success("已停用该用户"),u()}catch{i.warning("停用失败")}}).catch(()=>{})},Y=e=>{h("确定启用?","提示信息").then(async()=>{try{const t=b(e.id.id);await _e(t),i.success("已启用用户，当前用户可以正常登录"),u()}catch{i.warning("启用失败")}}).catch(()=>{})},V=async e=>{h("确定重置密码吗?","删除提示").then(async()=>{try{(await be(b(e.id.id))).status===200&&i.success("重置成功")}catch(t){i.error(t.data.message||"重置失败")}})},H=e=>e.authority!=="市场推广",J=()=>{if(_().roles[0]==="SYS_ADMIN"){const t=["企业赋予","重置密码","启用","停用","编辑","删除"];for(const a of c.operations||[])a.perm=t.some(r=>r===a.text)}},$=e=>{E.userId=e.id.id,E.visible=!0},G=async(e,t,a)=>{e.append("roleId",b(t)),e.append("projectIdsString",a);const r=await Se(e);!~~r.data.code&&~~r.status===200&&(i.success("导入成功"),u())},Q=async()=>{const e=await Te();Me(e.data,"用户列表.xlsx")};function K(e,t){e=JSON.parse(JSON.stringify(e));let a=!1,r=null;function n(s,l){for(let d=0;d<s.length;d++)if(s[d].children&&s[d].children.length>0&&n(s[d].children,l),l===s[d].id||a){a||(r=s[d]),a=!0;break}}return n(e,t),r}const X=()=>ve("http://124.112.49.66:9950/user_template.xlsx","设备模板.xlsx");return xe(()=>{J(),u(),L(),T.getorganize()}),(e,t)=>{const a=re,r=ae,n=te,s=ee,l=Z;return Ue(),Ie(l,null,{tree:B(()=>[S(a,{"tree-data":y},null,8,["tree-data"])]),default:B(()=>[S(r,{ref_key:"refSearch",ref:M,config:v},null,8,["config"]),S(n,{class:"card-table",config:c},null,8,["config"]),S(s,{ref_key:"refForm_Profile",ref:U,config:I},null,8,["config"]),S(s,{ref_key:"refForm_Aou",ref:g,config:f},null,8,["config"]),S(s,{ref_key:"refForm_Character",ref:D,config:x},null,8,["config"])]),_:1})}}}),He=Ne(ke,[["__scopeId","data-v-2c273738"]]);export{He as default};
