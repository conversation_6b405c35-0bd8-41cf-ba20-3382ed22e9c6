import{d as V,a0 as B,M as F,c as M,r as g,S as q,D as E,b as j,o as R,ay as k,bo as z,i as l,g as h,h as y,F as D,q as S,an as w,br as O,C as $}from"./index-r0dFAfgr.js";import{_ as G}from"./TreeBox-DDD2iwoR.js";import{_ as H}from"./CardTable-rdWOL4_6.js";import{_ as J}from"./CardSearch-CB_HNR-Q.js";import{_ as K}from"./index-BJ-QPYom.js";import{j as Q,k as U,s as X}from"./index-Bj5d3Vsu.js";import{a as Y}from"./index-BggOjNGp.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const Z=V({__name:"AlarmPlan",setup(I){const b=B(),{$btnPerms:_}=F(),C=M(),r=g({devices:[],ents:new Map,severityColor:{提示:"rgb(85,204,244)",次要:"rgb(255,216,0)",重要:"#f58717",紧急:"rgb(245,75,23)",严重:"#FF0000"}}),s=g({title:"区域划分",data:b.projectList,loading:!1,isFilterTree:!0,currentProject:b.selectedProject,treeNodeHandleClick:t=>{s.currentProject=t,b.SET_selectedProject(t),m()}}),x=g({defaultParams:{keyword:""},filters:[{type:"btn-group",btns:[{perm:!0,type:"primary",icon:"iconfont icon-chaxun",text:"查询",click:()=>m()},{perm:_("AlarmSettingAdd"),type:"primary",icon:"iconfont icon-jia",text:"添加告警",click:()=>N()}]}]}),o=g({loading:!1,dataList:[],columns:[{minWidth:200,prop:"name",label:"告警名称"},{minWidth:120,prop:"dName",label:"告警设备",width:180},{minWidth:120,prop:"attributeName",label:"监测数据",width:150},{minWidth:120,prop:"alarmTypeName",label:"告警类型"},{minWidth:120,prop:"cycleName",label:"周期",width:120},{minWidth:120,prop:"alarmValue",label:"告警触发值",width:120},{minWidth:120,prop:"recoverSet",label:"恢复类型",width:120},{minWidth:120,prop:"recoverValue",label:"恢复触发值",width:120},{minWidth:120,prop:"severity",label:"告警级别",cellStyle:t=>({color:t.severityColor})},{minWidth:120,prop:"period",label:"有效时间段",icon:"el-icon-time",iconStyle:{color:"#69e850"},width:200},{minWidth:120,prop:"alarmRemarks",label:"告警描述"}],operations:[{text:"关联联系人",isTextBtn:!0,perm:_("AlarmSettingConnectPerson"),icon:"iconfont icon-xiangqing",click:t=>T(t)},{text:"编辑",isTextBtn:!0,perm:_("AlarmSettingEdit"),icon:"iconfont icon-bianji",click:t=>W(t)},{text:"删除",isTextBtn:!0,type:"danger",perm:_("AlarmSettingDelete"),icon:"iconfont icon-shanchu",click:t=>A(t)}],operationWidth:"280px",pagination:{refreshData:({page:t,size:n})=>{o.pagination.limit=n,o.pagination.page=t,m()}}}),f=g({visible:!1,tableData:[],close:()=>f.visible=!1}),i=g({visible:!1,temp:{},project:{},deviceList:[],close:()=>i.visible=!1}),T=t=>{f.visible=!0,f.tableData=t},m=async t=>{var n,u;o.loading=!0;try{const d={keyword:"水质",page:o.pagination.page||1,size:o.pagination.limit||20};t||Object.assign(d,((n=C.value)==null?void 0:n.queryParams)||{});const p=await Q((u=s.currentProject)==null?void 0:u.value,d),e=await P(p.data);console.log(e,"listlistlistlist"),o.dataList=e,o.pagination.total=p.data.total}catch{}o.loading=!1},P=async t=>{var p;r.devices=[];let n=t.data;o.pagination.total=(p=t.data)==null?void 0:p.length,(r.devices.length===0||r.ents.size===0)&&(await Y(s.currentProject.id)).data.forEach(a=>{const c={label:a.name,value:a.id.id};r.ents.set(a.id.id,a.name),r.devices.push(c)});const u={day:"日",month:"月",year:"年"};n=n.map(e=>{const a={};e.alarmValue=e.details.alarmSetValue,e.recoverValue=e.details.recoverSetValue,e.alarmTypeName=e.details.setAlarmType,e.recoverSet=e.details.rType,e.alarmRemarks=e.details.alarmRemarks,e.attributeName=e.details.attributeName,e.severityColor=r.severityColor[e.severity];for(const c in e)a[c]=e[c];return a.isCycle=e.isCycle||!1,e.cycle?(a.cycleName=u[e.cycle],a.recoverSet="-",a.recoverValue="-"):(a.cycleName="",a.cycle=null),a.dName=r.ents.get(e.deviceId.id),a.name=e.name,a});const d=function(e,a){const c=e.createdTime,v=a.createdTime;return c<v?1:c>v?-1:0};return n=n.sort(d),n},A=async t=>{q("确定删除该告警, 是否继续?","删除提示").then(async()=>{(await U(E(t.id.id))).status===200&&(j.success("删除成功"),m())})},N=()=>{i.project=s.currentProject,i.temp={},i.deviceList=r.devices,i.visible=!0},W=t=>{i.project=s.currentProject,i.temp=t,i.deviceList=r.devices,i.visible=!0},L=t=>{t.projectId=s.currentProject.id,X(t).then(()=>{j.success("保存成功"),o.pagination.page=1,m()})};return R(()=>{m()}),(t,n)=>{const u=K,d=J,p=H,e=k("SettingsDialog"),a=k("ConnectDialog"),c=G,v=O;return z((h(),y(c,null,{tree:D(()=>[S(u,{"tree-data":l(s)},null,8,["tree-data"])]),default:D(()=>[S(d,{ref_key:"refSearch",ref:C,config:l(x)},null,8,["config"]),S(p,{config:l(o),class:"card-table"},null,8,["config"]),l(i).visible?(h(),y(e,{key:0,"dialog-config":l(i),onSave:L},null,8,["dialog-config"])):w("",!0),l(f).visible?(h(),y(a,{key:1,"connect-dialog":l(f)},null,8,["connect-dialog"])):w("",!0)]),_:1})),[[v,!!l(s).loading]])}}}),le=$(Z,[["__scopeId","data-v-c6cd1ccc"]]);export{le as default};
