<template>
  <div class="item flex">
    <img
      src="../../imgs/zhibiao.png"
      alt=""
    />
    <div class="text flex fl_column ds_between">
      <span>{{ props.config.label }}</span>
      <span>{{ props.config.value }}</span>
      <span>同比
        <span
          v-if="props.config.status"
          class="red"
        >
          <el-icon color="red"><CaretTop /></el-icon>
          {{ props.config.status }}
        </span>
        <span
          v-else
          class="blue"
        >
          <el-icon color="blue"><CaretTop /></el-icon>
          {{ props.config.status }}
        </span>
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { CaretTop } from '@element-plus/icons-vue'

const props = defineProps<{ config: any }>()
</script>

<style lang="scss" scoped>
.item {
  width: 240px;
  height: 100px;
  img {
    width: 100px;
    height: 100px;
    margin-right: 20px;
  }
}
.text {
  font-size: 16px;
  :nth-child(2) {
    font-size: 20px;
    color: aqua;
  }
  :nth-child(3) {
    font-size: 14px;
    .i {
      width: 20px;
      height: 20px;
    }
  }
}
.flex {
  display: flex;
}

.ds_between {
  justify-content: space-between;
}
.fl_column {
  flex-direction: column;
}

.red {
  color: red;
}
.blue {
  color: blue;
}
</style>
